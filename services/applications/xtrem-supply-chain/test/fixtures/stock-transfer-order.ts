import type { Context, Dict, NodeCreateData } from '@sage/xtrem-core';
import { date } from '@sage/xtrem-core';
import type * as xtremDistribution from '@sage/xtrem-distribution';
import * as xtremSupplyChain from '../../lib';

export async function readTestStockTransferOrder(context: Context, stockTransferOrderNumber: string) {
    const stockTransferOrder = await context.read(
        xtremSupplyChain.nodes.StockTransferOrder,
        {
            number: stockTransferOrderNumber,
        },
        { forUpdate: true },
    );
    return stockTransferOrder;
}

export function createStockTransferOrder(
    context: Context,
    testKey: Dict<any>,
    statusDict = {
        status: 'draft' as xtremSupplyChain.enums.StockTransferOrderStatus,
        shippingStatus: 'notShipped' as xtremDistribution.enums.ShippingStatus,
    },
    lineInclusion = {
        skipLines: false as boolean,
        multipleLines: false as boolean,
    },
    item = '#STOFIFO',
    sites: {
        site: string | null;
        receivingSite: string | null;
    } = {
        site: '#CAS01',
        receivingSite: '#CAS02',
    },
    dataDict: {
        date?: date | undefined;
        expectedDeliveryDate?: date | undefined;
        shippingDate?: date | undefined;
        doNotShipBeforeDate?: date | null;
        doNotShipAfterDate?: date | null;
        stockSite?: string;
    } = {
        date: date.parse('2020-11-18', context.currentLocale as any) || null,
        expectedDeliveryDate: date.parse('2020-11-18', context.currentLocale as any),
        shippingDate: date.parse('2020-11-18', context.currentLocale as any),
        doNotShipBeforeDate: date.parse('2020-11-12', context.currentLocale as any),
        doNotShipAfterDate: date.parse('2020-11-18', context.currentLocale as any),
        stockSite: sites.site ?? '#CAS01',
    },
): Promise<xtremSupplyChain.nodes.StockTransferOrder> {
    let lines;

    if (!lineInclusion.skipLines && !lineInclusion.multipleLines) {
        lines = [
            {
                status: statusDict.status,
                shippingStatus: statusDict.shippingStatus,
                item,
                stockSite: dataDict.stockSite ?? '#CAS01',
                quantity: 1,
                expectedDeliveryDate: dataDict.expectedDeliveryDate,
                shippingDate: dataDict.shippingDate,
                doNotShipBeforeDate: dataDict.doNotShipBeforeDate,
                doNotShipAfterDate: dataDict.doNotShipAfterDate,
            },
        ];
    }

    const linePayLoad = {
        status: statusDict.status,
        shippingStatus: statusDict.shippingStatus,
        item,
        stockSite: dataDict.stockSite ?? '#CAS01',
        quantity: 1,
        expectedDeliveryDate: dataDict.expectedDeliveryDate,
        shippingDate: dataDict.shippingDate,
        doNotShipBeforeDate: dataDict.doNotShipBeforeDate,
        doNotShipAfterDate: dataDict.doNotShipAfterDate,
    };

    // create transfer order with multiple lines based on parameter
    if (!lineInclusion.skipLines) {
        lines = lineInclusion.multipleLines ? [linePayLoad, { ...linePayLoad }] : [linePayLoad];
    }

    const baseData: NodeCreateData<xtremSupplyChain.nodes.StockTransferOrder> = {
        site: sites.site,
        receivingSite: sites.receivingSite,
        date: dataDict.date,
        expectedDeliveryDate: dataDict.expectedDeliveryDate,
        shippingDate: dataDict.shippingDate,
        requestedDeliveryDate: date.parse('2020-11-20', context.currentLocale as any),
        doNotShipBeforeDate: date.parse('2020-11-12', context.currentLocale as any),
        doNotShipAfterDate: date.parse('2020-11-18', context.currentLocale as any),
        ...testKey,
        lines,
    };
    return context.create(xtremSupplyChain.nodes.StockTransferOrder, baseData);
}
