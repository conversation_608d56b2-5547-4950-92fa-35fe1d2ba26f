import type { Context, NodeCreateData } from '@sage/xtrem-core';
import { date } from '@sage/xtrem-core';
import * as xtremSupplyChain from '../../lib';

export function readTestStockTransferReceipt(context: Context, stockTransferReceiptNumber: string) {
    return context.read(
        xtremSupplyChain.nodes.StockTransferReceipt,
        {
            number: stockTransferReceiptNumber,
        },
        { forUpdate: true },
    );
}

export function createStockTransferReceipt(
    context: Context,
    docNumber: 'TR260001',
    status: 'readyToProcess',
    skipLines = false,
    item = '#STOFIFO',
    sites: {
        site: string | null;
        supplier: string | null;
        shippingSite: string | null;
    } = {
        site: '#CAS02',
        supplier: '#CAS01',
        shippingSite: '#CAS01',
    },
    dataDict: {
        date?: date | null;
        stockSite?: string;
    } = {
        date: date.parse('2020-11-18', context.currentLocale as any) || null,
        stockSite: sites.site ?? '#CAS02',
    },
): Promise<xtremSupplyChain.nodes.StockTransferReceipt> {
    let lines;

    if (skipLines === false) {
        lines = [
            {
                status,
                item,
                stockSite: dataDict.stockSite ?? '#CAS01',
                quantity: 1,
            },
        ];
    }

    const baseData: NodeCreateData<xtremSupplyChain.nodes.StockTransferReceipt> = {
        site: sites.site,
        supplier: sites.supplier,
        shippingSite: sites.shippingSite,
        date: dataDict.date ?? undefined,
        number: docNumber,
        lines,
    };

    return context.create(xtremSupplyChain.nodes.StockTransferReceipt, baseData);
}
