import { Test } from '@sage/xtrem-core';
import { assert } from 'chai';
import * as xtremSupply<PERSON>hain from '../../../lib';
import * as fixtures from '../../fixtures';

describe('StockTransferOrderLib', () => {
    it('StockTransferOrderLine control STO for STS creation fails - status cannot be draft or closed', () =>
        Test.withContext(async context => {
            const stockTransferOrder = await fixtures.createStockTransferOrder(
                context,
                { number: 'test1' },
                { status: 'draft', shippingStatus: 'notShipped' },
                { skipLines: false, multipleLines: false },
                '#STOFIFO',
            );
            await stockTransferOrder.$.save();

            await assert.isRejected(
                xtremSupplyChain.functions.controlStockTransferOrderForStockTransferShipmentCreation(
                    context,
                    await stockTransferOrder.lines.toArray(),
                ),
                'The status for the selected lines cannot be Draft.',
            );
        }));

    it('StockTransferOrderLine control STO for STS creation fails - already shipped', () =>
        Test.withContext(async context => {
            const stockTransferOrder = await fixtures.createStockTransferOrder(
                context,
                { number: 'test1' },
                { status: 'draft', shippingStatus: 'notShipped' },
                { skipLines: false, multipleLines: false },
                '#STOFIFO',
            );
            await stockTransferOrder.$.save();

            await stockTransferOrder.lines.forEach(async (line: xtremSupplyChain.nodes.StockTransferOrderLine) => {
                await line.$.set({ status: 'inProgress', shippingStatus: 'shipped' });
            });
            await stockTransferOrder.$.set({ status: 'inProgress' });

            await stockTransferOrder.$.trySave();

            assert.deepEqual(stockTransferOrder.$.context.diagnoses, []);

            await assert.isRejected(
                xtremSupplyChain.functions.controlStockTransferOrderForStockTransferShipmentCreation(
                    context,
                    await stockTransferOrder.lines.toArray(),
                ),
                'The stock transfer order line is already shipped.',
            );
        }));

    it('StockTransferOrderLine control STO for STS creation fails - allocation in progress', () =>
        Test.withContext(async context => {
            const stockTransferOrder = await fixtures.createStockTransferOrder(
                context,
                { number: 'test1' },
                { status: 'draft', shippingStatus: 'notShipped' },
                { skipLines: false, multipleLines: false },
                '#STOFIFO',
            );
            await stockTransferOrder.$.save();

            await stockTransferOrder.lines.forEach(async (line: xtremSupplyChain.nodes.StockTransferOrderLine) => {
                await line.$.set({
                    status: 'inProgress',
                    shippingStatus: 'notShipped',
                    allocationRequestStatus: 'inProgress',
                });
            });
            await stockTransferOrder.$.set({ status: 'inProgress' });

            await stockTransferOrder.$.trySave();

            assert.deepEqual(stockTransferOrder.$.context.diagnoses, []);

            await assert.isRejected(
                xtremSupplyChain.functions.controlStockTransferOrderForStockTransferShipmentCreation(
                    context,
                    await stockTransferOrder.lines.toArray(),
                ),
                'You can only ship the stock transfer order line after the allocation request is complete.',
            );
        }));

    it('StockTransferOrderLine control STO for STS creation fails - ship after allocation is completed', () =>
        Test.withContext(async context => {
            const stockTransferOrder = await fixtures.createStockTransferOrder(
                context,
                { number: 'test1' },
                { status: 'draft', shippingStatus: 'notShipped' },
                { skipLines: false, multipleLines: false },
                '#STOFIFO',
            );
            await stockTransferOrder.$.save();

            await stockTransferOrder.lines.forEach(async (line: xtremSupplyChain.nodes.StockTransferOrderLine) => {
                await line.$.set({ status: 'inProgress', allocationRequestStatus: 'inProgress' });
            });
            await stockTransferOrder.$.set({ status: 'inProgress' });

            await stockTransferOrder.$.trySave();

            assert.deepEqual(stockTransferOrder.$.context.diagnoses, []);

            await assert.isRejected(
                xtremSupplyChain.functions.controlStockTransferOrderForStockTransferShipmentCreation(
                    context,
                    await stockTransferOrder.lines.toArray(),
                    { isForFinanceCheck: false },
                ),
                'You can only ship the stock transfer order line after the allocation request is complete.',
            );
        }));

    it('StockTransferOrderLine confirm status change fails - is not draft', () =>
        Test.withContext(async context => {
            const stockTransferOrder = await fixtures.createStockTransferOrder(
                context,
                { number: 'test1' },
                { status: 'inProgress', shippingStatus: 'notShipped' },
                { skipLines: false, multipleLines: false },
                '#STOFIFO',
            );

            await stockTransferOrder.lines.forEach(async (line: xtremSupplyChain.nodes.StockTransferOrderLine) => {
                await line.$.set({ status: 'pending' });
            });
            await stockTransferOrder.$.set({ status: 'pending' });
            await stockTransferOrder.$.save();

            const confirmStatus = await xtremSupplyChain.functions.confirm(stockTransferOrder);

            assert.equal(confirmStatus, 'isNotDraft');
        }));

    it('StockTransferOrderLine confirm status change - is confirmed', () =>
        Test.withContext(async context => {
            const stockTransferOrder = await fixtures.createStockTransferOrder(
                context,
                { number: 'test1' },
                { status: 'draft', shippingStatus: 'notShipped' },
                { skipLines: false, multipleLines: false },
                '#STOFIFO',
            );
            await stockTransferOrder.$.save();

            const confirmStatus = await xtremSupplyChain.functions.confirm(stockTransferOrder);

            assert.equal(confirmStatus, 'isConfirmed');
        }));

    it('StockTransferOrderLine is not allocable', () =>
        Test.withContext(async context => {
            const stockTransferOrder = await fixtures.createStockTransferOrder(
                context,
                { number: 'test1' },
                { status: 'draft', shippingStatus: 'notShipped' },
                { skipLines: false, multipleLines: false },
                '#STOFIFO',
            );
            await stockTransferOrder.$.save();

            const isLineAllocable = await xtremSupplyChain.functions.isStockTransferOrderLineAllocable(
                await stockTransferOrder.lines.elementAt(0),
            );

            assert.equal(isLineAllocable, false);
        }));

    it('StockTransferOrderLine is allocable', () =>
        Test.withContext(async context => {
            const stockTransferOrder = await fixtures.createStockTransferOrder(
                context,
                { number: 'test1' },
                { status: 'inProgress', shippingStatus: 'notShipped' },
                { skipLines: false, multipleLines: false },
                '#STOFIFO',
            );

            await stockTransferOrder.lines.forEach(async (line: xtremSupplyChain.nodes.StockTransferOrderLine) => {
                await line.$.set({ status: 'pending' });
            });
            await stockTransferOrder.$.set({ status: 'pending' });
            await stockTransferOrder.$.save();

            const isLineAllocable = await xtremSupplyChain.functions.isStockTransferOrderLineAllocable(
                await stockTransferOrder.lines.elementAt(0),
            );

            assert.equal(isLineAllocable, true);
        }));
});
