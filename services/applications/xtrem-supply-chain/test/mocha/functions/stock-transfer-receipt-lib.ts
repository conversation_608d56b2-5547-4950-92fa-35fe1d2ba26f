import { Test } from '@sage/xtrem-core';
import { assert } from 'chai';
import * as xtremSupply<PERSON>hain from '../../../lib';
import * as fixtures from '../../fixtures';

describe('StockTransferReceiptLib', () => {
    it('StockTransferReceiptLine updateStockTransferShipmentLineAfterReceiptDeleted', () =>
        Test.withContext(async context => {
            const stockTransferOrderNumber = 'TOTEST01';
            const stockTransferOrder = await fixtures.readTestStockTransferOrder(context, stockTransferOrderNumber);

            await stockTransferOrder.lines.forEach(async (line: xtremSupplyChain.nodes.StockTransferOrderLine) => {
                await line.$.set({ status: 'pending' });
            });
            await stockTransferOrder.$.set({ status: 'pending' });
            await stockTransferOrder.$.save();

            const stockTransferShipment = await xtremSupplyChain.nodes.StockTransferOrder.createStockTransferShipment(
                context,
                stockTransferOrder,
            );

            await context.flushDeferredActions();

            const stockTransferReceipt = await xtremSupplyChain.nodes.StockTransferShipment.createStockTransferReceipt(
                context,
                stockTransferShipment,
            );

            await context.flushDeferredActions();

            assert.deepEqual(stockTransferReceipt.$.context.diagnoses, []);

            await assert.isFulfilled(
                xtremSupplyChain.functions.updateStockTransferShipmentLineAfterReceiptDeleted(
                    await stockTransferReceipt.lines.elementAt(0),
                ),
                undefined,
            );
        }));

    it('StockTransferReceiptLine updateStockTransferShipmentLineAfterReceiptPosted', () =>
        Test.withContext(async context => {
            const stockTransferOrderNumber = 'TOTEST01';
            const stockTransferOrder = await fixtures.readTestStockTransferOrder(context, stockTransferOrderNumber);

            await stockTransferOrder.lines.forEach(async (line: xtremSupplyChain.nodes.StockTransferOrderLine) => {
                await line.$.set({ status: 'pending' });
            });
            await stockTransferOrder.$.set({ status: 'pending' });
            await stockTransferOrder.$.save();

            const stockTransferShipment = await xtremSupplyChain.nodes.StockTransferOrder.createStockTransferShipment(
                context,
                stockTransferOrder,
            );

            await context.flushDeferredActions();

            const stockTransferReceipt = await xtremSupplyChain.nodes.StockTransferShipment.createStockTransferReceipt(
                context,
                stockTransferShipment,
            );

            await context.flushDeferredActions();

            assert.deepEqual(stockTransferReceipt.$.context.diagnoses, []);

            await assert.isFulfilled(
                xtremSupplyChain.functions.updateStockTransferShipmentLineAfterReceiptPosted(
                    await stockTransferReceipt.lines.elementAt(0),
                ),
                undefined,
            );
        }));
});
