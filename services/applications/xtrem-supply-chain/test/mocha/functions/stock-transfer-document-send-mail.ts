import { Test } from '@sage/xtrem-core';
import * as xtremSystem from '@sage/xtrem-system';
import { assert, expect } from 'chai';
import * as xtremSupply<PERSON>hain from '../../../index';
import * as fixtures from '../../fixtures';

describe('Stock transfer document send mail', () => {
    it('Stock transfer order with defaultApprover on site send mail', () =>
        Test.withContext(async context => {
            const site: xtremSystem.nodes.Site = await context.read(
                xtremSystem.nodes.Site,
                { id: 'CAS01' },
                { forUpdate: true },
            );
            const user: xtremSystem.nodes.User = await context.read(xtremSystem.nodes.User, {
                _id: '#<EMAIL>',
            });
            await site.$.set({ stockTransferOrderDefaultApprover: user });
            await site.$.save();

            const stockTransferOrder = await fixtures.createStockTransferOrder(
                context,
                { number: 'test1' },
                { status: 'draft', shippingStatus: 'notShipped' },
                { skipLines: false, multipleLines: false },
                '#STOFIFO',
            );

            const { mailerUser, ...mail } = await stockTransferOrder.beforeSendApprovalRequestMail(context, user);

            assert.strictEqual(await mailerUser.email, '<EMAIL>');
            assert.strictEqual(mail.subject, '[Stock transfer order test1] approval request');
        }));

    it('Stock transfer order with defaultApprover on site', () =>
        Test.withContext(async context => {
            const site: xtremSystem.nodes.Site = await context.read(
                xtremSystem.nodes.Site,
                { id: 'CAS01' },
                { forUpdate: true },
            );
            const user: xtremSystem.nodes.User = await context.read(xtremSystem.nodes.User, {
                _id: '#<EMAIL>',
            });
            await site.$.set({ stockTransferOrderDefaultApprover: user });
            await site.$.save();

            const stockTransferOrderNumber = 'TOTEST01';
            const stockTransferOrder = await fixtures.readTestStockTransferOrder(context, stockTransferOrderNumber);

            const result = await xtremSupplyChain.functions.getApprovalUser(context, stockTransferOrder);

            assert.strictEqual(await result.email, await user.email);
        }));

    it('Stock transfer order with substituteApprover on site', () =>
        Test.withContext(async context => {
            const site: xtremSystem.nodes.Site = await context.read(
                xtremSystem.nodes.Site,
                { id: 'CAS01' },
                { forUpdate: true },
            );
            const user: xtremSystem.nodes.User = await context.read(xtremSystem.nodes.User, {
                _id: '#<EMAIL>',
            });
            await site.$.set({ stockTransferOrderSubstituteApprover: user });
            await site.$.save();

            const stockTransferOrderNumber = 'TOTEST01';
            const stockTransferOrder = await fixtures.readTestStockTransferOrder(context, stockTransferOrderNumber);

            const result = await xtremSupplyChain.functions.getApprovalUser(context, stockTransferOrder);

            assert.strictEqual(await result.email, await user.email);
        }));

    it('Stock transfer order with no approver on site', () =>
        Test.withContext(async context => {
            const stockTransferOrderNumber = 'TOTEST01';
            const stockTransferOrder = await fixtures.readTestStockTransferOrder(context, stockTransferOrderNumber);

            await expect(xtremSupplyChain.functions.getApprovalUser(context, stockTransferOrder)).to.be.rejectedWith(
                'There is no default or substitute approver for the current document.',
            );
        }));

    it('Stock transfer order submission for approval action allowed', () =>
        Test.withContext(async context => {
            const stockTransferOrderNumber = 'TOTEST01';
            const stockTransferOrder = await fixtures.readTestStockTransferOrder(context, stockTransferOrderNumber);

            assert.strictEqual(await stockTransferOrder.approvalStatus, 'draft');

            expect(
                await xtremSupplyChain.functions.controlBeforeSendMailApproval(context, stockTransferOrder),
            ).to.equal(undefined);
        }));

    it('Stock transfer order submission for approval action not allowed', () =>
        Test.withContext(async context => {
            const stockTransferOrderNumber = 'TOTEST04';
            const stockTransferOrder = await fixtures.readTestStockTransferOrder(context, stockTransferOrderNumber);

            await expect(
                xtremSupplyChain.functions.controlBeforeSendMailApproval(context, stockTransferOrder),
            ).to.be.rejectedWith(
                'Submission for approval action not allowed. The stock transfer order is not in draft status.',
            );
        }));

    it('Stock transfer order get transfer order data for approval', () =>
        Test.withContext(async context => {
            const site: xtremSystem.nodes.Site = await context.read(
                xtremSystem.nodes.Site,
                { id: 'CAS01' },
                { forUpdate: true },
            );
            const user: xtremSystem.nodes.User = await context.read(xtremSystem.nodes.User, {
                _id: '#<EMAIL>',
            });
            await site.$.set({ stockTransferOrderSubstituteApprover: user });
            await site.$.save();

            const stockTransferOrderNumber = 'TOTEST01';
            const stockTransferOrder = await fixtures.readTestStockTransferOrder(context, stockTransferOrderNumber);

            const result = await xtremSupplyChain.functions.getTransferOrderDataForApproval(
                context,
                stockTransferOrder,
                user,
            );

            assert.strictEqual(
                result.urlApprovalDocument,
                `http://localhost:8240/@sage/xtrem-supply-chain/StockTransferOrder/${Buffer.from(JSON.stringify({ _id: stockTransferOrder._id })).toString('base64')}`,
            );
            assert.strictEqual(
                result.urlApprove,
                `http://localhost:8240/@sage/xtrem-supply-chain/StockTransferOrder/${Buffer.from(JSON.stringify({ _id: String(stockTransferOrder._id), action: 'approved' })).toString('base64')}`,
            );
            assert.strictEqual(
                result.urlReject,
                `http://localhost:8240/@sage/xtrem-supply-chain/StockTransferOrder/${Buffer.from(JSON.stringify({ _id: String(stockTransferOrder._id), action: 'rejected' })).toString('base64')}`,
            );
        }));
});
