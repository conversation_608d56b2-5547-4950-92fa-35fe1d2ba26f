import { Test } from '@sage/xtrem-core';
import { assert } from 'chai';
import * as xtremSupply<PERSON>hain from '../../../lib';
import * as fixtures from '../../fixtures';

describe('StockTransferShipmentLib', () => {
    it('StockTransferShipmentLine updateStockTransferOrderLineAfterShipmentDeleted', () =>
        Test.withContext(async context => {
            const stockTransferOrderNumber = 'TOTEST01';
            const stockTransferOrder = await fixtures.readTestStockTransferOrder(context, stockTransferOrderNumber);

            await stockTransferOrder.lines.forEach(async (line: xtremSupplyChain.nodes.StockTransferOrderLine) => {
                await line.$.set({ status: 'pending' });
            });
            await stockTransferOrder.$.set({ status: 'pending' });
            await stockTransferOrder.$.save();

            const stockTransferShipment = await xtremSupplyChain.nodes.StockTransferOrder.createStockTransferShipment(
                context,
                stockTransferOrder,
            );

            await context.flushDeferredActions();

            await assert.isFulfilled(
                xtremSupplyChain.functions.updateStockTransferOrderLineAfterShipmentDeleted(
                    await stockTransferShipment.lines.elementAt(0),
                ),
                undefined,
            );
        }));

    it('StockTransferShipmentLine updateStockTransferOrderLineAfterReceiptCreated', () =>
        Test.withContext(async context => {
            const stockTransferOrderNumber = 'TOTEST01';
            const stockTransferOrder = await fixtures.readTestStockTransferOrder(context, stockTransferOrderNumber);

            await stockTransferOrder.lines.forEach(async (line: xtremSupplyChain.nodes.StockTransferOrderLine) => {
                await line.$.set({ status: 'pending' });
            });
            await stockTransferOrder.$.set({ status: 'pending' });
            await stockTransferOrder.$.save();

            const stockTransferShipment = await xtremSupplyChain.nodes.StockTransferOrder.createStockTransferShipment(
                context,
                stockTransferOrder,
            );

            await context.flushDeferredActions();

            await assert.isFulfilled(
                xtremSupplyChain.functions.updateStockTransferOrderLineAfterReceiptCreated(
                    await stockTransferShipment.lines.elementAt(0),
                ),
                undefined,
            );
        }));
});
