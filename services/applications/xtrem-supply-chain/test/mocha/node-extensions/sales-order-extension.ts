/**
Since xtrem-supply-chain as  acess to xtrem-purchase and xtrem-sales, we can test the order to order assignment here
*/
import { Test } from '@sage/xtrem-core';
import * as xtremManufacturing from '@sage/xtrem-manufacturing';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremSales from '@sage/xtrem-sales';
import * as xtremTechnicalData from '@sage/xtrem-technical-data';
import { assert } from 'chai';
import * as xtremSupply<PERSON>hain from '../../../index';

describe('SalesOrder node', () => {
    it('Close sales order fail when trying to close a order with links on sales order lines - isSafeToRetry = false', () =>
        Test.withContext(
            async context => {
                await context.serviceOptionManager.activateServiceOptions(context, [
                    xtremMasterData.serviceOptions.orderToOrderOption,
                ]);
                let salesOrder = await context.read(xtremSales.nodes.SalesOrder, { number: 'order-to-order-test' });

                await xtremSupplyChain.nodeExtensions.SalesOrderExtension.createBackToBackOrderFromSalesOrder(
                    context,
                    salesOrder,
                );

                await Test.rollbackCache(context);

                salesOrder = await context.read(
                    xtremSales.nodes.SalesOrder,
                    { number: 'order-to-order-test' },
                    { forUpdate: true },
                );
                await assert.isRejected(
                    xtremSales.nodes.SalesOrder.closeSalesOrder(context, salesOrder, false, true),
                    'Remove the supply order links before closing the sales order.',
                );
            },
            { testActiveServiceOptions: [xtremMasterData.serviceOptions.orderToOrderOption] },
        ));

    it('Close sales order fail when trying to close a order with links on sales order lines - isSafeToRetry = true', () =>
        Test.withContext(
            async context => {
                await context.serviceOptionManager.activateServiceOptions(context, [
                    xtremMasterData.serviceOptions.orderToOrderOption,
                ]);
                let salesOrder = await context.read(xtremSales.nodes.SalesOrder, { number: 'order-to-order-test' });

                await xtremSupplyChain.nodeExtensions.SalesOrderExtension.createBackToBackOrderFromSalesOrder(
                    context,
                    salesOrder,
                );

                await Test.rollbackCache(context);
                salesOrder = await context.read(
                    xtremSales.nodes.SalesOrder,
                    { number: 'order-to-order-test' },
                    { forUpdate: true },
                );

                // After trying to close with isSafeToRetry = true, the order shouldn't be closed
                salesOrder = await xtremSales.nodes.SalesOrder.closeSalesOrder(context, salesOrder, true, true);
                assert.equal(await salesOrder.status, 'pending');
            },
            { testActiveServiceOptions: [xtremMasterData.serviceOptions.orderToOrderOption] },
        ));

    it('Create shipment from sales order fail when trying to ship a order with links on sales order lines and no quantity allocated - isSafeToRetry = true', () =>
        Test.withContext(
            async context => {
                await context.serviceOptionManager.activateServiceOptions(context, [
                    xtremMasterData.serviceOptions.orderToOrderOption,
                ]);
                let salesOrder = await context.read(
                    xtremSales.nodes.SalesOrder,
                    { number: 'order-to-order-test' },
                    { forUpdate: true },
                );
                assert.equal(await salesOrder.status, 'pending');
                assert.equal(await salesOrder.shippingStatus, 'notShipped');

                await xtremSupplyChain.nodeExtensions.SalesOrderExtension.createBackToBackOrderFromSalesOrder(
                    context,
                    salesOrder,
                );

                await Test.rollbackCache(context);
                salesOrder = await context.read(
                    xtremSales.nodes.SalesOrder,
                    { number: 'order-to-order-test' },
                    { forUpdate: true },
                );

                await assert.isRejected(
                    xtremSales.nodes.SalesOrder.createShipmentsFromOrder(context, salesOrder, false, true),
                    'The supply order quantities and sales order quantities need to be the same. You cannot ship the sales order.',
                );
            },
            { testActiveServiceOptions: [xtremMasterData.serviceOptions.orderToOrderOption] },
        ));

    it('Create shipment from sales order fail when trying to ship a order with links on sales order lines and no quantity allocated - isSafeToRetry = true', () =>
        Test.withContext(
            async context => {
                await context.serviceOptionManager.activateServiceOptions(context, [
                    xtremMasterData.serviceOptions.orderToOrderOption,
                ]);
                let salesOrder = await context.read(
                    xtremSales.nodes.SalesOrder,
                    { number: 'order-to-order-test' },
                    { forUpdate: true },
                );
                assert.equal(await salesOrder.status, 'pending');
                assert.equal(await salesOrder.shippingStatus, 'notShipped');

                await xtremSupplyChain.nodeExtensions.SalesOrderExtension.createBackToBackOrderFromSalesOrder(
                    context,
                    salesOrder,
                );

                await Test.rollbackCache(context);
                salesOrder = await context.read(
                    xtremSales.nodes.SalesOrder,
                    { number: 'order-to-order-test' },
                    { forUpdate: true },
                );

                // After trying to close with isSafeToRetry = true, the order shouldn't be closed
                await assert.isRejected(
                    xtremSales.nodes.SalesOrder.createShipmentsFromOrder(context, salesOrder, true, true),
                    'The supply order quantities and sales order quantities need to be the same. You cannot ship the sales order.',
                );

                salesOrder = await context.read(xtremSales.nodes.SalesOrder, { number: 'order-to-order-test' });
                assert.equal(await salesOrder.status, 'pending');
                assert.equal(await salesOrder.shippingStatus, 'notShipped');
            },
            { testActiveServiceOptions: [xtremMasterData.serviceOptions.orderToOrderOption] },
        ));
});
describe('SalesOrder - back to back order', () => {
    it('Create back to back orders from sales order', () =>
        Test.withContext(
            async context => {
                await context.serviceOptionManager.activateServiceOptions(context, [
                    xtremMasterData.serviceOptions.orderToOrderOption,
                ]);

                let salesOrder = await context.read(
                    xtremSales.nodes.SalesOrder,
                    { number: 'order-to-order-test-2' },
                    { forUpdate: true },
                );
                assert.equal(await salesOrder.status, 'quote');

                await xtremSales.nodes.SalesOrder.confirmSalesOrder(context, salesOrder);

                await Test.rollbackCache(context);
                salesOrder = await context.read(xtremSales.nodes.SalesOrder, { number: 'order-to-order-test-2' });
                assert.equal(await salesOrder.status, 'pending');

                const backToBackOrders =
                    await xtremSupplyChain.nodeExtensions.SalesOrderExtension.createBackToBackOrderFromSalesOrder(
                        context,
                        salesOrder,
                    );

                assert.equal(backToBackOrders.purchaseOrders.length, 2);
                assert.equal(backToBackOrders.workOrders.length, 1);

                await Test.rollbackCache(context);
                salesOrder = await context.read(xtremSales.nodes.SalesOrder, { number: 'order-to-order-test-2' });
                let salesOrderLine1 = await salesOrder.lines.at(0);
                let salesOrderLine2 = await salesOrder.lines.at(1);
                let salesOrderLine3 = await salesOrder.lines.at(2);

                assert.equal(await salesOrderLine1?.uAssignmentOrder, 'WO250003');
                assert.equal(await salesOrderLine2?.uAssignmentOrder, 'PO250009');
                assert.equal(await salesOrderLine3?.uAssignmentOrder, 'PO250010');

                // Try to create again - should not create any purchase/work order and return the same values
                const backToBackOrders2 =
                    await xtremSupplyChain.nodeExtensions.SalesOrderExtension.createBackToBackOrderFromSalesOrder(
                        context,
                        salesOrder,
                    );
                assert.equal(backToBackOrders2.purchaseOrders.length, 2);
                assert.equal(backToBackOrders2.workOrders.length, 1);

                await Test.rollbackCache(context);
                salesOrder = await context.read(xtremSales.nodes.SalesOrder, { number: 'order-to-order-test-2' });
                salesOrderLine1 = await salesOrder.lines.at(0);
                salesOrderLine2 = await salesOrder.lines.at(1);
                salesOrderLine3 = await salesOrder.lines.at(2);

                assert.equal(await salesOrderLine1?.uAssignmentOrder, 'WO250003');
                assert.equal(await salesOrderLine2?.uAssignmentOrder, 'PO250009');
                assert.equal(await salesOrderLine3?.uAssignmentOrder, 'PO250010');
            },
            { testActiveServiceOptions: [xtremMasterData.serviceOptions.orderToOrderOption] },
        ));

    it('Create purchase order from sales order - Sales order line status should not be quote or closed.', () =>
        Test.withContext(
            async context => {
                await context.serviceOptionManager.activateServiceOptions(context, [
                    xtremMasterData.serviceOptions.orderToOrderOption,
                ]);

                const salesOrder = await context.read(xtremSales.nodes.SalesOrder, { number: 'order-to-order-test-2' });
                assert.equal(await salesOrder.status, 'quote');

                await assert.isRejected(
                    xtremSupplyChain.nodeExtensions.SalesOrderExtension.createBackToBackOrderFromSalesOrder(
                        context,
                        salesOrder,
                    ),
                    'Sales order line status should not be quote or closed.',
                );

                assert.equal(await salesOrder.status, 'quote');
            },
            { testActiveServiceOptions: [xtremMasterData.serviceOptions.orderToOrderOption] },
        ));
    it('Create purchase order from sales order - The Bill-to customer is on hold. The shipment cannot be posted.', () =>
        Test.withContext(
            async context => {
                await context.serviceOptionManager.activateServiceOptions(context, [
                    xtremMasterData.serviceOptions.orderToOrderOption,
                ]);
                const billToCustomer = await context.read(
                    xtremMasterData.nodes.Customer,
                    {
                        businessEntity: '#US017',
                    },
                    { forUpdate: true },
                );

                await billToCustomer.$.set({ isOnHold: true });
                await billToCustomer.$.save();

                const salesOrder = await context.read(xtremSales.nodes.SalesOrder, { number: 'order-to-order-test' });

                await assert.isRejected(
                    xtremSupplyChain.nodeExtensions.SalesOrderExtension.createBackToBackOrderFromSalesOrder(
                        context,
                        salesOrder,
                    ),
                    'The Bill-to customer is on hold. The shipment cannot be posted.',
                );
            },
            { testActiveServiceOptions: [xtremMasterData.serviceOptions.orderToOrderOption] },
        ));
    it('Create purchase order from sales order - Default supplier not found for item Stock Item 01 and site Siège social S01  PARIS.', () =>
        Test.withContext(
            async context => {
                await context.serviceOptionManager.activateServiceOptions(context, [
                    xtremMasterData.serviceOptions.orderToOrderOption,
                ]);

                // Delete all the existing suppliers and item suppliers to not get a default supplier
                await context.bulkDeleteSql(xtremMasterData.nodes.ItemSiteSupplier, { where: { _id: { _ne: '' } } });
                await context.bulkDeleteSql(xtremMasterData.nodes.ItemSupplier, { where: { _id: { _ne: '' } } });

                const salesOrder = await context.read(xtremSales.nodes.SalesOrder, { number: 'order-to-order-test' });

                await assert.isRejected(
                    xtremSupplyChain.nodeExtensions.SalesOrderExtension.createBackToBackOrderFromSalesOrder(
                        context,
                        salesOrder,
                    ),
                    'Default supplier not found for item Stock Item 01 and site Siège social S01  PARIS.',
                );
            },
            { testActiveServiceOptions: [xtremMasterData.serviceOptions.orderToOrderOption] },
        ));
    it('Create back to back orders from sales order - No bill of material available to use found.', () =>
        Test.withContext(
            async context => {
                await context.serviceOptionManager.activateServiceOptions(context, [
                    xtremMasterData.serviceOptions.orderToOrderOption,
                ]);

                let salesOrder = await context.read(
                    xtremSales.nodes.SalesOrder,
                    { number: 'order-to-order-test-2' },
                    { forUpdate: true },
                );
                assert.equal(await salesOrder.status, 'quote');

                await xtremSales.nodes.SalesOrder.confirmSalesOrder(context, salesOrder);

                await Test.rollbackCache(context);
                salesOrder = await context.read(xtremSales.nodes.SalesOrder, { number: 'order-to-order-test-2' });
                assert.equal(await salesOrder.status, 'pending');

                const billOfMaterial = await context.read(
                    xtremTechnicalData.nodes.BillOfMaterial,
                    {
                        item: await (await salesOrder.lines.at(0))!.item,
                        site: await salesOrder.stockSite,
                    },
                    { forUpdate: true },
                );

                await billOfMaterial.$.delete();

                await assert.isRejected(
                    xtremSupplyChain.nodeExtensions.SalesOrderExtension.createBackToBackOrderFromSalesOrder(
                        context,
                        salesOrder,
                    ),
                    'No bill of material available to use found.',
                );
            },
            { testActiveServiceOptions: [xtremMasterData.serviceOptions.orderToOrderOption] },
        ));

    it('Create back to back orders from sales order - Bill of material without routing', () =>
        Test.withContext(
            async context => {
                await context.serviceOptionManager.activateServiceOptions(context, [
                    xtremMasterData.serviceOptions.orderToOrderOption,
                ]);

                let salesOrder = await context.read(
                    xtremSales.nodes.SalesOrder,
                    { number: 'order-to-order-test-2' },
                    { forUpdate: true },
                );
                assert.equal(await salesOrder.status, 'quote');

                await xtremSales.nodes.SalesOrder.confirmSalesOrder(context, salesOrder);

                await Test.rollbackCache(context);
                salesOrder = await context.read(xtremSales.nodes.SalesOrder, { number: 'order-to-order-test-2' });
                assert.equal(await salesOrder.status, 'pending');

                await context.bulkUpdate(xtremManufacturing.nodes.WorkOrderCategory, {
                    set: { isDefault: false },
                });

                const result =
                    await xtremSupplyChain.nodeExtensions.SalesOrderExtension.createBackToBackOrderFromSalesOrder(
                        context,
                        salesOrder,
                    );
                assert.equal(await result.workOrders.at(0)?.number, 'WO250003');

                const workOrder = await context.read(xtremManufacturing.nodes.WorkOrder, { number: 'WO250003' });
                assert.equal(await (await workOrder.category).id, 'Assembly');
            },
            { testActiveServiceOptions: [xtremMasterData.serviceOptions.orderToOrderOption] },
        ));

    it('Create back to back orders from sales order', () =>
        Test.withContext(
            async context => {
                await context.serviceOptionManager.activateServiceOptions(context, [
                    xtremMasterData.serviceOptions.orderToOrderOption,
                ]);

                const salesOrder = await context.read(xtremSales.nodes.SalesOrder, { number: 'order-to-order-test-3' });
                await context.bulkUpdate(xtremManufacturing.nodes.WorkOrderCategory, {
                    set: { isDefault: false },
                });

                const result =
                    await xtremSupplyChain.nodeExtensions.SalesOrderExtension.createBackToBackOrderFromSalesOrder(
                        context,
                        salesOrder,
                    );
                assert.equal(await result.workOrders.at(0)?.number, 'WO250003');

                const workOrder = await context.read(xtremManufacturing.nodes.WorkOrder, { number: 'WO250003' });
                assert.equal(await (await workOrder.category).id, 'Normal');
            },
            { testActiveServiceOptions: [xtremMasterData.serviceOptions.orderToOrderOption] },
        ));
});
