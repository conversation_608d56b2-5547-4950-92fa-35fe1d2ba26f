import { Test } from '@sage/xtrem-core';
import * as xtremFinanceData from '@sage/xtrem-finance-data';
import * as chai from 'chai';
import { assert } from 'chai';
import * as chaiAsPromised from 'chai-as-promised';
import { it } from 'mocha';
import * as sinon from 'sinon';
import * as xtremSupplyChain from '../../../lib';

import * as fixtures from '../../fixtures';

chai.use(chaiAsPromised);

describe('StockTransferReceipt', () => {
    it('Create StockTransferReceipt creation fails 01 - The stock transfer receipt must contain at least one line', () =>
        Test.withContext(async context => {
            const stockTransferReceipt = await fixtures.createStockTransferReceipt(
                context,
                'TR260001',
                'readyToProcess',
                true,
                '#STOFIFO',
            );

            await assert.isRejected(stockTransferReceipt.$.save());
            assert.deepEqual(stockTransferReceipt.$.context.diagnoses, [
                {
                    message: 'The document needs at least one line.',
                    path: [],
                    severity: 3,
                },
            ]);
        }));

    it('Update stock transfer receipt change site', () =>
        Test.withContext(async context => {
            const stockTransferReceiptNumber = 'TRTEST01';
            const stockTransferReceipt = await fixtures.readTestStockTransferReceipt(
                context,
                stockTransferReceiptNumber,
            );

            await assert.isRejected(
                stockTransferReceipt.$.set({ site: '#CAS01' }),
                Error,
                'StockTransferReceipt.site: cannot set value on frozen property',
            );
        }));

    it('Update stock transfer receipt change stock site', () =>
        Test.withContext(async context => {
            const stockTransferReceiptNumber = 'TRTEST01';
            const stockTransferReceipt = await fixtures.readTestStockTransferReceipt(
                context,
                stockTransferReceiptNumber,
            );

            await assert.isRejected(
                stockTransferReceipt.$.set({ stockSite: '#CAS01' }),
                Error,
                'StockTransferReceipt.stockSite: cannot set value on frozen property',
            );
        }));

    it('Update stock transfer receipt change supplier', () =>
        Test.withContext(async context => {
            const stockTransferReceiptNumber = 'TRTEST01';
            const stockTransferReceipt = await fixtures.readTestStockTransferReceipt(
                context,
                stockTransferReceiptNumber,
            );

            await assert.isRejected(
                stockTransferReceipt.$.set({ supplier: '#CAS02' }),
                Error,
                'StockTransferReceipt.supplier: cannot set value on frozen property',
            );
        }));
});

describe('Resend notification for finance', () => {
    it('Stock Transfer Receipt - Successfully resend notification for finance', () =>
        Test.withContext(
            async context => {
                const documentNumber = 'TR250001';

                const stockTransferReceipt: xtremSupplyChain.nodes.StockTransferReceipt = await context.read(
                    xtremSupplyChain.nodes.StockTransferReceipt,
                    {
                        number: documentNumber,
                    },
                );

                const filter = { documentNumber, documentType: 'stockTransferReceipt' };
                let amountType = await (
                    await (
                        await context
                            .query(xtremFinanceData.nodes.AccountingStaging, {
                                filter,
                            })
                            .at(0)
                    )?.amounts.at(0)
                )?.amountType;

                let financeTransactionRecord = await context
                    .query(xtremFinanceData.nodes.FinanceTransaction, { filter })
                    .at(0);
                let financeTransactionStatus = await financeTransactionRecord?.status;
                let financeTransactionMessage = await financeTransactionRecord?.message;

                assert.equal(amountType, 'inTransitAmount');
                assert.equal(financeTransactionStatus, 'notRecorded');
                assert.notEqual(financeTransactionMessage, '');

                const notifySpy = sinon.spy(context, 'notify');
                await xtremSupplyChain.nodes.StockTransferReceipt.resendNotificationForFinance(
                    context,
                    stockTransferReceipt,
                );

                assert.equal(notifySpy.getCalls().length, 0);

                amountType = await (
                    await (
                        await context
                            .query(xtremFinanceData.nodes.AccountingStaging, {
                                filter,
                            })
                            .at(0)
                    )?.amounts.at(0)
                )?.amountType;

                financeTransactionRecord = await context
                    .query(xtremFinanceData.nodes.FinanceTransaction, { filter })
                    .at(0);
                financeTransactionStatus = await financeTransactionRecord?.status;
                financeTransactionMessage = await financeTransactionRecord?.message;

                assert.equal(amountType, 'inTransitAmount');
                assert.equal(financeTransactionStatus, 'pending');
                assert.equal(financeTransactionMessage, '');
            },
            { today: '2025-02-18' },
        ));
});
