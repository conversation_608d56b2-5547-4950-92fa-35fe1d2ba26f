import { date, Test, ValidationSeverity } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremSystem from '@sage/xtrem-system';
import { assert } from 'chai';
import * as xtremSupp<PERSON><PERSON>hain from '../../../lib';
import * as fixtures from '../../fixtures';

describe('StockTransferOrder', () => {
    it('Create StockTransferOrder creation fails 01 - The stock transfer order must contain at least one line', () =>
        Test.withContext(async context => {
            const stockTransferOrder = await fixtures.createStockTransferOrder(
                context,
                { number: 'test1' },
                { status: 'draft', shippingStatus: 'notShipped' },
                { skipLines: true, multipleLines: false },
                '#STOFIFO',
            );

            await assert.isRejected(stockTransferOrder.$.save());
            assert.deepEqual(stockTransferOrder.$.context.diagnoses, [
                {
                    message: 'The document needs at least one line.',
                    path: [],
                    severity: 3,
                },
            ]);
        }));

    it('Create StockTransferOrder creation fails 02 - supplier not active', () =>
        Test.withContext(async context => {
            const supplier = await context.read(xtremMasterData.nodes.Supplier, { _id: '#CAS01' }, { forUpdate: true });
            await supplier.$.set({ isActive: false });
            await supplier.$.save();

            const stockTransferOrder = await fixtures.createStockTransferOrder(
                context,
                { number: 'test1' },
                { status: 'draft', shippingStatus: 'notShipped' },
                { skipLines: false, multipleLines: false },
                '#STOFIFO',
            );
            await assert.isRejected(stockTransferOrder.$.save());
            assert.deepEqual(stockTransferOrder.$.context.diagnoses, [
                {
                    message: 'The record is not valid. You need to select a different record.',
                    path: ['site'],
                    severity: ValidationSeverity.error,
                },
                {
                    message: 'The record cannot be referenced because it is inactive.',
                    path: ['supplier'],
                    severity: ValidationSeverity.error,
                },
                {
                    message: 'Supplier CAS01 is not active',
                    path: ['supplier'],
                    severity: ValidationSeverity.error,
                },
            ]);
        }));

    it('Create StockTransferOrder creation fails 04 - financial site needs to be the same for site and receiving site', () =>
        Test.withContext(async context => {
            const stockTransferOrder = await fixtures.createStockTransferOrder(
                context,
                { number: 'test1' },
                { status: 'draft', shippingStatus: 'notShipped' },
                { skipLines: false, multipleLines: false },
                '#STOFIFO',
                { site: '#US016', receivingSite: '#CAS02' },
            );

            await assert.isRejected(stockTransferOrder.$.save());
            assert.deepEqual(stockTransferOrder.$.context.diagnoses, [
                {
                    message:
                        'The financial site for the receiving site needs to be the same as the financial site for the shipping site.',
                    path: [],
                    severity: 3,
                },
                {
                    message: 'The record is not valid. You need to select a different record.',
                    path: ['receivingSite'],
                    severity: 3,
                },
                {
                    message: 'The record is not valid. You need to select a different record.',
                    path: ['lines', '-1000000002', 'item'],
                    severity: 3,
                },
            ]);
        }));

    it('Create StockTransferOrder creation fails 05 - wrong orderDate', () =>
        Test.withContext(async context => {
            const stockTransferOrder = await fixtures.createStockTransferOrder(
                context,
                { number: 'test1' },
                { status: 'draft', shippingStatus: 'notShipped' },
                { skipLines: false, multipleLines: false },
                '#STOFIFO',
                { site: '#CAS01', receivingSite: '#CAS02' },
                {
                    date: date.parse('2030-01-01', context.currentLocale as any),
                    shippingDate: date.parse('2020-11-18', context.currentLocale as any),
                },
            );

            await assert.isRejected(stockTransferOrder.$.save());
            assert.deepEqual(stockTransferOrder.$.context.diagnoses, [
                {
                    severity: 3,
                    path: ['lines', '-1000000002', 'shippingDate'],
                    message: 'The shipping date needs to be after the order date.',
                },
            ]);
        }));

    it('Create StockTransferOrder creation fails 06 - wrong orderDate', () =>
        Test.withContext(async context => {
            const stockTransferOrder = await fixtures.createStockTransferOrder(
                context,
                { number: 'test1' },
                { status: 'draft', shippingStatus: 'notShipped' },
                { skipLines: false, multipleLines: false },
                '#STOFIFO',
                { site: '#CAS01', receivingSite: '#CAS02' },
                {
                    date: date.parse('2020-11-16', context.currentLocale as any),
                    shippingDate: date.parse('2020-11-18', context.currentLocale as any),
                    doNotShipBeforeDate: date.parse('2020-11-20', context.currentLocale as any),
                    doNotShipAfterDate: date.parse('2020-11-20', context.currentLocale as any),
                },
            );

            await assert.isRejected(stockTransferOrder.$.save());
            assert.deepEqual(stockTransferOrder.$.context.diagnoses, [
                {
                    severity: 3,
                    path: ['lines', '-1000000002', 'shippingDate'],
                    message: 'The shipping date needs to be after the Do not ship before date.',
                },
            ]);
        }));

    it('Create StockTransferOrder creation fails 07 - wrong orderDate', () =>
        Test.withContext(async context => {
            const stockTransferOrder = await fixtures.createStockTransferOrder(
                context,
                { number: 'test1' },
                { status: 'draft', shippingStatus: 'notShipped' },
                { skipLines: false, multipleLines: false },
                '#STOFIFO',
                { site: '#CAS01', receivingSite: '#CAS02' },
                {
                    date: date.parse('2020-11-16', context.currentLocale as any),
                    shippingDate: date.parse('2020-11-18', context.currentLocale as any),
                    doNotShipBeforeDate: date.parse('2020-11-16', context.currentLocale as any),
                    doNotShipAfterDate: date.parse('2020-11-16', context.currentLocale as any),
                },
            );

            await assert.isRejected(stockTransferOrder.$.save());
            assert.deepEqual(stockTransferOrder.$.context.diagnoses, [
                {
                    severity: 3,
                    path: ['lines', '-1000000002', 'shippingDate'],
                    message: 'The shipping date needs to be before the Do not ship after date.',
                },
            ]);
        }));

    it('Create StockTransferOrder creation success 02', () =>
        Test.withContext(async context => {
            const stockTransferOrder = await fixtures.createStockTransferOrder(
                context,
                { number: 'test1' },
                { status: 'draft', shippingStatus: 'notShipped' },
                { skipLines: false, multipleLines: false },
                '#STOFIFO',
            );
            await stockTransferOrder.$.save();
            assert.deepEqual(stockTransferOrder.$.context.diagnoses, []);
            assert.equal(await stockTransferOrder.workDays, 62);
            assert.equal(String(await stockTransferOrder.shippingDate), '2020-11-18');
        }));

    it('Update stock transfer order change stock site', () =>
        Test.withContext(async context => {
            const stockTransferOrderNumber = 'TOTEST01';
            const stockTransferOrder = await fixtures.readTestStockTransferOrder(context, stockTransferOrderNumber);
            await stockTransferOrder.$.set({ stockSite: '#CAS02' });
            await assert.isRejected(stockTransferOrder.$.save());
            assert.deepEqual(stockTransferOrder.$.context.diagnoses, [
                {
                    severity: 3,
                    path: [],
                    message: 'The stock site of the stock transfer order must not be changed.',
                },
            ]);
        }));

    it('Create stock transfer shipment', () =>
        Test.withContext(
            async context => {
                const stockTransferOrderNumber = 'TOTEST01';
                const stockTransferOrder = await fixtures.readTestStockTransferOrder(context, stockTransferOrderNumber);

                await stockTransferOrder.lines.forEach(async (line: xtremSupplyChain.nodes.StockTransferOrderLine) => {
                    await line.$.set({ status: 'pending' });
                });
                await stockTransferOrder.$.set({ status: 'pending' });
                await stockTransferOrder.$.save();

                const lines = await stockTransferOrder.lines.toArray();

                // Line 0
                assert.equal(await lines[0].status, 'pending');

                const stockTransferShipment =
                    await xtremSupplyChain.nodes.StockTransferOrder.createStockTransferShipment(
                        context,
                        stockTransferOrder,
                    );
                assert.deepEqual(stockTransferOrder.$.context.diagnoses, []);

                await context.flushDeferredActions();
                assert.equal(await stockTransferShipment.number, 'TS240002');
            },
            { today: '2024-11-11' },
        ));

    it('Create StockTransferOrder creation - default dimensions and attributes', () =>
        Test.withContext(async context => {
            // Project => ShippingSite
            // Task => ShippingSite
            // Department => ReceivingSite => dimensionType01
            // Channel => ShippingSite => dimensionType02
            // Dimension Type 1 => Item => dimensionType03
            // Dimension Type 2 => Supplier => dimensionType04
            // Dimension Type 3 => Customer => dimensionType05

            const shippingSite = await context.read(xtremSystem.nodes.Site, { id: 'CAS01' }, { forUpdate: true });
            const receivingSite = await context.read(xtremSystem.nodes.Site, { id: 'CAS02' }, { forUpdate: true });
            const customer = await context.read(xtremMasterData.nodes.Customer, { _id: 'CAS02' }, { forUpdate: true });
            const supplier = await context.read(xtremMasterData.nodes.Supplier, { _id: 'CAS01' }, { forUpdate: true });
            const item = await context.read(xtremMasterData.nodes.Item, { _id: 'STOFIFO' }, { forUpdate: true });

            await shippingSite.$.set({
                storedAttributes: { project: 'AttPROJ', employee: '', task: 'Task1' },
                storedDimensions: { dimensionType02: 'CHANNELVALUE1' },
            });
            await shippingSite.$.save();

            await receivingSite.$.set({
                storedAttributes: { project: 'AttPROJ', employee: '', task: 'Task1' },
                storedDimensions: { dimensionType01: '300' },
            });
            await receivingSite.$.save();

            await customer.$.set({
                storedDimensions: { dimensionType05: 'RETAIL' },
            });
            await customer.$.save();

            await supplier.$.set({
                storedDimensions: { dimensionType04: 'DIMTYPE2VALUE2' },
            });
            await supplier.$.save();

            await item.$.set({
                storedDimensions: { dimensionType03: 'MANUFACTURING' },
            });
            await item.$.save();

            const stockTransferOrder = await fixtures.createStockTransferOrder(
                context,
                { number: 'test2' },
                { status: 'draft', shippingStatus: 'notShipped' },
                { skipLines: false, multipleLines: false },
                '#STOFIFO',
            );

            await stockTransferOrder.$.save();
            assert.deepEqual(
                JSON.stringify(await (await stockTransferOrder.lines.elementAt(0)).storedAttributes),
                JSON.stringify({
                    project: 'AttPROJ',
                    task: 'Task1',
                }),
            );
            assert.deepEqual(
                JSON.stringify(await (await stockTransferOrder.lines.elementAt(0)).storedDimensions),
                JSON.stringify({
                    dimensionType01: '300',
                    dimensionType02: 'CHANNELVALUE1',
                    dimensionType03: 'MANUFACTURING',
                    dimensionType04: 'DIMTYPE2VALUE2',
                    dimensionType05: 'RETAIL',
                }),
            );
            assert.deepEqual(
                JSON.stringify(await (await stockTransferOrder.lines.elementAt(0)).computedAttributes),
                JSON.stringify({
                    financialSite: 'CAS01',
                    stockSite: 'CAS01',
                    item: 'STOFIFO',
                    customer: 'CAS02',
                }),
            );
        }));

    it('Stock transfer order - openLine - lines already opened', () =>
        Test.withContext(async context => {
            const stockTransferOrderNumber = 'TOTEST01';
            const stockTransferOrder = await fixtures.readTestStockTransferOrder(context, stockTransferOrderNumber);

            await stockTransferOrder.lines.forEach(async (line: xtremSupplyChain.nodes.StockTransferOrderLine) => {
                await line.$.set({ status: 'pending' });
            });
            await stockTransferOrder.$.set({ status: 'pending' });
            await stockTransferOrder.$.save();

            const status = await xtremSupplyChain.nodes.StockTransferOrder.openLine(
                context,
                await stockTransferOrder.lines.elementAt(0),
            );

            assert.equal(status, 'isAlreadyOpen');
        }));

    it('Stock transfer order - openLine - line not shipped', () =>
        Test.withContext(async context => {
            const stockTransferOrderNumber = 'TOTEST01';
            const stockTransferOrder = await fixtures.readTestStockTransferOrder(context, stockTransferOrderNumber);

            await stockTransferOrder.lines.forEach(async (line: xtremSupplyChain.nodes.StockTransferOrderLine) => {
                await line.$.set({ status: 'closed' });
            });
            await stockTransferOrder.$.set({ status: 'closed' });
            await stockTransferOrder.$.save();

            const status = await xtremSupplyChain.nodes.StockTransferOrder.openLine(
                context,
                await stockTransferOrder.lines.elementAt(0),
            );

            assert.equal(status, 'isNowOpen');
        }));

    it('Stock transfer order - closeLine - line is already closed', () =>
        Test.withContext(async context => {
            const stockTransferOrderNumber = 'TOTEST01';
            const stockTransferOrder = await fixtures.readTestStockTransferOrder(context, stockTransferOrderNumber);

            await stockTransferOrder.lines.forEach(async (line: xtremSupplyChain.nodes.StockTransferOrderLine) => {
                await line.$.set({ status: 'closed' });
            });
            await stockTransferOrder.$.set({ status: 'closed' });
            await stockTransferOrder.$.save();

            const status = await xtremSupplyChain.nodes.StockTransferOrder.closeLine(
                context,
                await stockTransferOrder.lines.elementAt(0),
            );

            assert.equal(status, 'isAlreadyClosed');
        }));

    it('Stock transfer order - closeLine - line is now closed', () =>
        Test.withContext(async context => {
            const stockTransferOrderNumber = 'TOTEST01';
            const stockTransferOrder = await fixtures.readTestStockTransferOrder(context, stockTransferOrderNumber);

            await stockTransferOrder.lines.forEach(async (line: xtremSupplyChain.nodes.StockTransferOrderLine) => {
                await line.$.set({ status: 'pending' });
            });
            await stockTransferOrder.$.set({ status: 'pending' });
            await stockTransferOrder.$.save();

            const status = await xtremSupplyChain.nodes.StockTransferOrder.closeLine(
                context,
                await stockTransferOrder.lines.elementAt(0),
            );

            assert.equal(status, 'isNowClosed');
        }));

    it('Stock transfer order - open - order is already open', () =>
        Test.withContext(async context => {
            const stockTransferOrderNumber = 'TOTEST01';
            const stockTransferOrder = await fixtures.readTestStockTransferOrder(context, stockTransferOrderNumber);

            await stockTransferOrder.lines.forEach(async (line: xtremSupplyChain.nodes.StockTransferOrderLine) => {
                await line.$.set({ status: 'pending' });
            });
            await stockTransferOrder.$.set({ status: 'pending' });
            await stockTransferOrder.$.save();

            const status = await xtremSupplyChain.nodes.StockTransferOrder.open(context, stockTransferOrder);

            assert.equal(status, 'isAlreadyOpen');
        }));

    it('Stock transfer order - open - order is now open', () =>
        Test.withContext(async context => {
            const stockTransferOrderNumber = 'TOTEST01';
            const stockTransferOrder = await fixtures.readTestStockTransferOrder(context, stockTransferOrderNumber);

            await stockTransferOrder.lines.forEach(async (line: xtremSupplyChain.nodes.StockTransferOrderLine) => {
                await line.$.set({ status: 'closed' });
            });
            await stockTransferOrder.$.set({ status: 'closed' });
            await stockTransferOrder.$.save();

            const status = await xtremSupplyChain.nodes.StockTransferOrder.open(context, stockTransferOrder);

            assert.equal(status, 'isNowOpen');
        }));

    it('Stock transfer order - open 02 - order is already open', () =>
        Test.withContext(async context => {
            const stockTransferOrderNumber = 'TOTEST01';
            const stockTransferOrder = await fixtures.readTestStockTransferOrder(context, stockTransferOrderNumber);

            await stockTransferOrder.lines.forEach(async (line: xtremSupplyChain.nodes.StockTransferOrderLine) => {
                await line.$.set({ status: 'closed' });
            });
            await stockTransferOrder.$.set({ status: 'closed', approvalStatus: 'approved' });
            await stockTransferOrder.$.save();

            const status = await xtremSupplyChain.nodes.StockTransferOrder.open(context, stockTransferOrder);

            assert.equal(status, 'isNowOpen');
        }));

    it('Stock transfer order - open 03 - order is already open', () =>
        Test.withContext(async context => {
            const stockTransferOrderNumber = 'TOTEST01';
            const stockTransferOrder = await fixtures.readTestStockTransferOrder(context, stockTransferOrderNumber);

            await stockTransferOrder.lines.forEach(async (line: xtremSupplyChain.nodes.StockTransferOrderLine) => {
                await line.$.set({ status: 'closed' });
            });
            await stockTransferOrder.$.set({ status: 'closed', approvalStatus: 'changeRequested' });
            await stockTransferOrder.$.save();

            const status = await xtremSupplyChain.nodes.StockTransferOrder.open(context, stockTransferOrder);

            assert.equal(status, 'isNowOpen');
        }));

    it('Stock transfer order - close - order is already open', () =>
        Test.withContext(async context => {
            const stockTransferOrderNumber = 'TOTEST01';
            const stockTransferOrder = await fixtures.readTestStockTransferOrder(context, stockTransferOrderNumber);

            await stockTransferOrder.lines.forEach(async (line: xtremSupplyChain.nodes.StockTransferOrderLine) => {
                await line.$.set({ status: 'closed' });
            });
            await stockTransferOrder.$.set({ status: 'closed' });
            await stockTransferOrder.$.save();

            const status = await xtremSupplyChain.nodes.StockTransferOrder.close(context, stockTransferOrder);

            assert.equal(status, 'isAlreadyClosed');
        }));

    it('Stock transfer order - close - order is now closed', () =>
        Test.withContext(async context => {
            const stockTransferOrderNumber = 'TOTEST01';
            const stockTransferOrder = await fixtures.readTestStockTransferOrder(context, stockTransferOrderNumber);

            await stockTransferOrder.lines.forEach(async (line: xtremSupplyChain.nodes.StockTransferOrderLine) => {
                await line.$.set({ status: 'pending' });
            });
            await stockTransferOrder.$.set({ status: 'pending' });
            await stockTransferOrder.$.save();

            const status = await xtremSupplyChain.nodes.StockTransferOrder.close(context, stockTransferOrder);

            assert.equal(status, 'isNowClosed');
        }));

    it('Stock transfer order - confirm', () =>
        Test.withContext(async context => {
            const stockTransferOrderNumber = 'TOTEST01';
            const stockTransferOrder = await fixtures.readTestStockTransferOrder(context, stockTransferOrderNumber);

            await stockTransferOrder.lines.forEach(async (line: xtremSupplyChain.nodes.StockTransferOrderLine) => {
                await line.$.set({ status: 'pending' });
            });
            await stockTransferOrder.$.set({ status: 'pending' });
            await stockTransferOrder.$.save();

            await assert.isFulfilled(
                xtremSupplyChain.nodes.StockTransferOrder.confirm(context, stockTransferOrder),
                'isNotDraft',
            );
        }));

    it('Stock transfer order - confirm order with a single line status closed', () =>
        Test.withContext(async context => {
            const stockTransferOrderNumber = 'TOTEST01';
            const stockTransferOrder = await fixtures.readTestStockTransferOrder(context, stockTransferOrderNumber);

            await stockTransferOrder.lines.forEach(async (line: xtremSupplyChain.nodes.StockTransferOrderLine) => {
                await line.$.set({ status: 'closed' });
            });
            await stockTransferOrder.$.save();

            await assert.isFulfilled(
                xtremSupplyChain.nodes.StockTransferOrder.confirm(context, stockTransferOrder),
                'Test',
            );
        }));

    it('StockTransferOrder multiple line confirm with closed line', () =>
        Test.withContext(async context => {
            const stockTransferOrder = await fixtures.createStockTransferOrder(
                context,
                { number: 'test1' },
                { status: 'draft', shippingStatus: 'notShipped' },
                { skipLines: false, multipleLines: true },
                '#STOFIFO',
            );

            // confirm lines
            await stockTransferOrder.$.save();

            await (await stockTransferOrder.lines.elementAt(0)).$.set({ status: 'closed' });
            await stockTransferOrder.$.save();
            assert.deepEqual(stockTransferOrder.$.context.diagnoses, []);

            await (await stockTransferOrder.lines.elementAt(1)).$.set({ status: 'pending' });
            await stockTransferOrder.$.save();
            assert.deepEqual(stockTransferOrder.$.context.diagnoses, []);

            const stockTransferShipment = await xtremSupplyChain.nodes.StockTransferOrder.createStockTransferShipment(
                context,
                stockTransferOrder,
            );
            assert.deepEqual(stockTransferShipment.$.context.diagnoses, []);
            assert.deepEqual(await stockTransferShipment.lines.length, 1);
        }));

    it('Stock transfer order - approve', () =>
        Test.withContext(async context => {
            const stockTransferOrderNumber = 'TOTEST01';
            const stockTransferOrder = await fixtures.readTestStockTransferOrder(context, stockTransferOrderNumber);

            await stockTransferOrder.lines.forEach(async (line: xtremSupplyChain.nodes.StockTransferOrderLine) => {
                await line.$.set({ status: 'pending' });
            });
            await stockTransferOrder.$.set({ status: 'pending', approvalStatus: 'pendingApproval' });
            await stockTransferOrder.$.save();

            await assert.isFulfilled(
                xtremSupplyChain.nodes.StockTransferOrder.approve(context, stockTransferOrder),
                'The stock transfer order is approved.',
            );
        }));

    it('Stock transfer order - approve is rejected', () =>
        Test.withContext(async context => {
            const stockTransferOrderNumber = 'TOTEST01';
            const stockTransferOrder = await fixtures.readTestStockTransferOrder(context, stockTransferOrderNumber);

            await stockTransferOrder.lines.forEach(async (line: xtremSupplyChain.nodes.StockTransferOrderLine) => {
                await line.$.set({ status: 'pending' });
            });
            await stockTransferOrder.$.set({ status: 'pending' });
            await stockTransferOrder.$.save();

            await assert.isRejected(
                xtremSupplyChain.nodes.StockTransferOrder.approve(context, stockTransferOrder),
                'You can only approve a stock transfer order if the approval status is Pending approval.',
            );
        }));

    it('Stock transfer order - reject is fulfilled', () =>
        Test.withContext(async context => {
            const stockTransferOrderNumber = 'TOTEST01';
            const stockTransferOrder = await fixtures.readTestStockTransferOrder(context, stockTransferOrderNumber);

            await stockTransferOrder.lines.forEach(async (line: xtremSupplyChain.nodes.StockTransferOrderLine) => {
                await line.$.set({ status: 'pending' });
            });
            await stockTransferOrder.$.set({ status: 'pending', approvalStatus: 'pendingApproval' });
            await stockTransferOrder.$.save();

            await assert.isFulfilled(
                xtremSupplyChain.nodes.StockTransferOrder.reject(context, stockTransferOrder),
                'The stock transfer order is rejected.',
            );
        }));

    it('Stock transfer order - reject is rejected', () =>
        Test.withContext(async context => {
            const stockTransferOrderNumber = 'TOTEST01';
            const stockTransferOrder = await fixtures.readTestStockTransferOrder(context, stockTransferOrderNumber);

            await stockTransferOrder.lines.forEach(async (line: xtremSupplyChain.nodes.StockTransferOrderLine) => {
                await line.$.set({ status: 'pending' });
            });
            await stockTransferOrder.$.set({ status: 'pending' });
            await stockTransferOrder.$.save();

            await assert.isRejected(
                xtremSupplyChain.nodes.StockTransferOrder.reject(context, stockTransferOrder),
                'You can only reject a stock transfer order if the approval status is Pending approval.',
            );
        }));
});
