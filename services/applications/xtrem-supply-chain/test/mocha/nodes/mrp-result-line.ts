import { BusinessRuleError, Test } from '@sage/xtrem-core';
import * as xtremMrpData from '@sage/xtrem-mrp-data';
import { expect } from 'chai';
import * as xtremSupplyChain from '../../../lib';

describe('MrpResultLine node-extension', () => {
    it('should block its deletion if the associated supply-planning record is being ordered', () =>
        Test.withContext(
            async context => {
                const firstSupplyPlanningRecord = await context
                    .query(xtremSupplyChain.nodes.SupplyPlanning, {
                        first: 1,
                        forUpdate: true,
                    })
                    .elementAt(0);

                await firstSupplyPlanningRecord.$.set({
                    status: 'createOrder',
                });
                await firstSupplyPlanningRecord.$.save();

                const mrpCalculation = await context.read(
                    xtremMrpData.nodes.MrpCalculation,
                    { _id: (await (await firstSupplyPlanningRecord.mrpResultLine).mrpCalculation)._id },
                    { forUpdate: true },
                );

                await expect(mrpCalculation.$.delete())
                    .to.eventually.be.rejectedWith(
                        'You cannot delete this result set, purchase orders are being created.',
                    )
                    .and.be.an.instanceOf(BusinessRuleError);
            },
            { today: '2024-02-29' },
        ));
});
