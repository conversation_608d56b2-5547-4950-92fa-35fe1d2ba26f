import { BatchContext } from '@sage/xtrem-communication';
import type { integer } from '@sage/xtrem-core';
import { DataInputError, Logger, Test } from '@sage/xtrem-core';
import { DateValue } from '@sage/xtrem-date-time';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremMrpData from '@sage/xtrem-mrp-data';
import * as xtremSystem from '@sage/xtrem-system';
import { assert, expect } from 'chai';
import * as Sinon from 'sinon';
import * as xtremSupplyChain from '../../../lib';

const logger = Logger.getLogger(__filename, 'supply-planning');

describe('supplyPlanning node', () => {
    describe('supplyPlanning records', () => {
        it('Creates Supply planning on MRP calculation done', () =>
            Test.withContext(async context => {
                const calculationId = '1';
                await assert.isFulfilled(
                    xtremSupplyChain.nodes.SupplyPlanning.onMrpCalculationDone(context, {
                        calculationId,
                        items: [
                            {
                                item: '5467',
                                site: 'US001',
                            },
                            {
                                item: '8553',
                                site: 'US001',
                            },
                            {
                                item: '7625',
                                site: 'US001',
                            },
                        ],
                    }),
                );

                const supplPlanningLines = context.query(xtremSupplyChain.nodes.SupplyPlanning, {
                    filter: { mrpResultLine: { mrpCalculation: calculationId } },
                });

                logger.verbose(
                    () => `onMRPCalculationRequest - supply planning records \n ${supplPlanningLines.length}`,
                );

                const expectations = [
                    {
                        type: null,
                        item: '#5467',
                        site: '#US001',
                        status: 'pending',
                        stockQuantity: 300,
                        purchaseQuantity: 300,
                        suggestionSource: 'MRP',
                    },
                    {
                        type: null,
                        item: '#8553',
                        site: '#US001',
                        status: 'pending',
                        stockQuantity: 120,
                        purchaseQuantity: 120,
                        suggestionSource: 'MRP',
                    },
                    {
                        type: null,
                        item: '#7625',
                        site: '#US001',
                        status: 'pending',
                        stockQuantity: 320,
                        purchaseQuantity: 320,
                        suggestionSource: 'MRP',
                    },
                ];

                await supplPlanningLines.forEach(async (supplyPlaningRecord, index) => {
                    const expected = expectations[index];

                    assert.equal(await supplyPlaningRecord.type, expected.type);
                    assert.equal(
                        (await supplyPlaningRecord.itemSite)._id,
                        (
                            await context.read(xtremMasterData.nodes.ItemSite, {
                                item: expected.item,
                                site: expected.site,
                            })
                        )._id,
                    );
                    assert.equal(await supplyPlaningRecord.status, expected.status);
                    assert.equal(await supplyPlaningRecord.stockQuantity, expected.stockQuantity);
                    assert.equal(await supplyPlaningRecord.purchaseQuantity, expected.purchaseQuantity);
                    assert.equal(await supplyPlaningRecord.suggestionSource, expected.suggestionSource);
                });
            }));

        it('should delete planning records that are ordered from suggestion', () =>
            Test.withContext(async context => {
                const firstSupplyPlanningRecord = await context
                    .query(xtremSupplyChain.nodes.SupplyPlanning, {
                        first: 1,
                    })
                    .elementAt(0);

                const mrpResultLine = await firstSupplyPlanningRecord.mrpResultLine;

                const mrpCalculation = await context.read(
                    xtremMrpData.nodes.MrpCalculation,
                    { _id: (await mrpResultLine.mrpCalculation)._id },
                    { forUpdate: true },
                );

                await xtremMrpData.nodes.MrpCalculation.updateResultLineSuggestionStatus(
                    context,
                    mrpCalculation,
                    mrpResultLine._id,
                    'ordered',
                );

                await expect(
                    context.read(xtremSupplyChain.nodes.SupplyPlanning, {
                        _id: firstSupplyPlanningRecord._id,
                    }),
                )
                    .to.eventually.be.rejectedWith(
                        `SupplyPlanning: record not found: {"_id":${firstSupplyPlanningRecord._id}}`,
                    )
                    .and.be.an.instanceOf(DataInputError);
            }));

        it('should delete planning records if their linked suggestion is deleted', () =>
            Test.withContext(async context => {
                const firstSupplyPlanningRecord = await context
                    .query(xtremSupplyChain.nodes.SupplyPlanning, {
                        first: 1,
                    })
                    .elementAt(0);

                const mrpCalculation = await context.read(
                    xtremMrpData.nodes.MrpCalculation,
                    { _id: (await (await firstSupplyPlanningRecord.mrpResultLine).mrpCalculation)._id },
                    { forUpdate: true },
                );
                await mrpCalculation.$.delete();

                await expect(
                    context.read(xtremSupplyChain.nodes.SupplyPlanning, {
                        _id: firstSupplyPlanningRecord._id,
                    }),
                )
                    .to.eventually.be.rejectedWith(
                        `SupplyPlanning: record not found: {"_id":${firstSupplyPlanningRecord._id}}`,
                    )
                    .and.be.an.instanceOf(DataInputError);
            }));

        it('should delete planning records and set suggestion status to ordered when PO created from the supply planning', () =>
            Test.withContext(
                async context => {
                    const supplyPlanningToBeOrdered: integer[] = [];
                    const mrpResultLineToBeUpdated: integer[] = [];

                    await xtremSupplyChain.functions.bulkCreatePurchaseOrderHandler(
                        context,
                        await context
                            .query(xtremSupplyChain.nodes.SupplyPlanning, {
                                first: 2,
                                filter: {
                                    type: 'purchased',
                                },
                                forUpdate: true,
                            })
                            .map(async supplyPlanning => {
                                supplyPlanningToBeOrdered.push(supplyPlanning._id);
                                mrpResultLineToBeUpdated.push((await supplyPlanning.mrpResultLine)._id);

                                await supplyPlanning.$.set({
                                    supplier: '#LECLERC',
                                });
                                await supplyPlanning.$.save();

                                return supplyPlanning.$.payload({ withIds: true });
                            })
                            .toArray(),
                    );

                    const mrpResultLinesUpdated = context.query(xtremMrpData.nodes.MrpResultLine, {
                        filter: { _id: { _in: mrpResultLineToBeUpdated } },
                    });

                    await expect(
                        context.read(xtremSupplyChain.nodes.SupplyPlanning, {
                            _id: supplyPlanningToBeOrdered.at(0),
                        }),
                    )
                        .to.eventually.be.rejectedWith(
                            `SupplyPlanning: record not found: {"_id":${supplyPlanningToBeOrdered.at(0)}}`,
                        )
                        .and.be.an.instanceOf(DataInputError);
                    assert.equal(await (await mrpResultLinesUpdated.elementAt(0)).suggestionStatus, 'ordered');

                    await expect(
                        context.read(xtremSupplyChain.nodes.SupplyPlanning, {
                            _id: supplyPlanningToBeOrdered.at(1),
                        }),
                    )
                        .to.eventually.be.rejectedWith(
                            `SupplyPlanning: record not found: {"_id":${supplyPlanningToBeOrdered.at(1)}}`,
                        )
                        .and.be.an.instanceOf(DataInputError);
                    assert.equal(await (await mrpResultLinesUpdated.elementAt(1)).suggestionStatus, 'ordered');
                },
                { today: '2024-02-29' },
            ));

        it('should delete planning records and set suggestion status to ordered when WO created from the supply planning', () =>
            Test.withContext(
                async context => {
                    const supplyPlanningToBeOrdered: integer[] = [];
                    const mrpResultLineToBeUpdated: integer[] = [];

                    await xtremSupplyChain.functions.bulkCreateWorkOrderHandler(
                        context,
                        await context
                            .query(xtremSupplyChain.nodes.SupplyPlanning, {
                                first: 2,
                                filter: {
                                    type: 'manufactured',
                                },
                                forUpdate: true,
                            })
                            .map(async supplyPlanning => {
                                supplyPlanningToBeOrdered.push(supplyPlanning._id);
                                mrpResultLineToBeUpdated.push((await supplyPlanning.mrpResultLine)._id);

                                return supplyPlanning.$.payload({ withIds: true });
                            })
                            .toArray(),
                    );

                    const mrpResultLinesUpdated = context.query(xtremMrpData.nodes.MrpResultLine, {
                        filter: { _id: { _in: mrpResultLineToBeUpdated } },
                    });

                    await expect(
                        context.read(xtremSupplyChain.nodes.SupplyPlanning, {
                            _id: supplyPlanningToBeOrdered.at(0),
                        }),
                    )
                        .to.eventually.be.rejectedWith(
                            `SupplyPlanning: record not found: {"_id":${supplyPlanningToBeOrdered.at(0)}}`,
                        )
                        .and.be.an.instanceOf(DataInputError);
                    assert.equal(await (await mrpResultLinesUpdated.elementAt(0)).suggestionStatus, 'ordered');

                    await expect(
                        context.read(xtremSupplyChain.nodes.SupplyPlanning, {
                            _id: supplyPlanningToBeOrdered.at(1),
                        }),
                    )
                        .to.eventually.be.rejectedWith(
                            `SupplyPlanning: record not found: {"_id":${supplyPlanningToBeOrdered.at(1)}}`,
                        )
                        .and.be.an.instanceOf(DataInputError);
                    assert.equal(await (await mrpResultLinesUpdated.elementAt(1)).suggestionStatus, 'ordered');
                },
                { today: '2024-02-29' },
            ));

        it('should use the company purchase site if only one exists and current site is not a purchase site', () =>
            Test.withContext(async context => {
                const site = await context.read(xtremSystem.nodes.Site, { id: 'US001' }, { forUpdate: true });
                await site.$.set({ isPurchase: false });
                await site.$.save();
                const calculationId = '3';
                await assert.isFulfilled(
                    xtremSupplyChain.nodes.SupplyPlanning.onMrpCalculationDone(context, {
                        calculationId,
                        items: [
                            {
                                item: 'Chair',
                                site: 'US001',
                            },
                        ],
                    }),
                );

                const supplPlanningLines = await context
                    .query(xtremSupplyChain.nodes.SupplyPlanning, {
                        filter: { mrpResultLine: calculationId },
                    })
                    .toArray();
                const expected = {
                    item: '#7625',
                    site: '#US001',
                    purchaseSite: 'US002',
                    suggestionSource: 'MRP',
                };
                assert.equal(supplPlanningLines.length, 1);
                assert.equal(await (await supplPlanningLines[0].purchaseSite)?.id, expected.purchaseSite);
                assert.equal(
                    await (
                        await supplPlanningLines[0].itemSite
                    ).id,
                    await (
                        await context.read(xtremMasterData.nodes.ItemSite, {
                            item: expected.item,
                            site: expected.site,
                        })
                    ).id,
                );
                assert.equal(await supplPlanningLines[0].suggestionSource, expected.suggestionSource);
            }));

        it('should set purchaseSite as undefined when company has multiple purchase sites and current site is not a purchase site', () =>
            Test.withContext(async context => {
                const newSite = await context.create(xtremSystem.nodes.Site, {
                    id: 'SPECIAL',
                    isFinance: true,
                    businessEntity: 13,
                    financialSite: null,
                    legalCompany: 'US001',
                    isPurchase: true,
                });
                await newSite.$.save();
                const site = await context.read(xtremSystem.nodes.Site, { id: 'US001' }, { forUpdate: true });
                await site.$.set({ isPurchase: false });
                await site.$.save();
                const calculationId = '3';
                await assert.isFulfilled(
                    xtremSupplyChain.nodes.SupplyPlanning.onMrpCalculationDone(context, {
                        calculationId,
                        items: [
                            {
                                item: 'Chair',
                                site: 'US001',
                            },
                        ],
                    }),
                );

                const supplPlanningLines = await context
                    .query(xtremSupplyChain.nodes.SupplyPlanning, {
                        filter: { mrpResultLine: calculationId },
                    })
                    .toArray();
                const expected = {
                    item: '#7625',
                    site: '#US001',
                    purchaseSite: undefined,
                    suggestionSource: 'MRP',
                };
                assert.equal(supplPlanningLines.length, 1);
                assert.equal(await (await supplPlanningLines[0].purchaseSite)?.id, expected.purchaseSite);
                assert.equal(
                    await (
                        await supplPlanningLines[0].itemSite
                    ).id,
                    await (
                        await context.read(xtremMasterData.nodes.ItemSite, {
                            item: expected.item,
                            site: expected.site,
                        })
                    ).id,
                );
                assert.equal(await supplPlanningLines[0].suggestionSource, expected.suggestionSource);
            }));
    });

    describe('Create purchase order validation messages', () => {
        it('Purchase site is required', () =>
            Test.withContext(
                async context => {
                    const logMessageMock = xtremSystem.TestHelpers.Sinon.registerMocks([
                        {
                            type: 'stub',
                            isMethod: true,
                            class: BatchContext,
                            name: 'logMessage',
                        },
                    ])[0].mock as Sinon.SinonStub;

                    await xtremMasterData.functions.testLib.simulateBatchContext(context);

                    const SupplPlanningPurchaseSite = await context.create(xtremSupplyChain.nodes.SupplyPlanning, {
                        mrpResultLine: 14,
                        type: 'purchased',
                        purchaseSite: null,
                        itemSite: '#Chair|US001',
                        supplier: '#LECLERC',
                        stockQuantity: 100,
                        purchaseQuantity: 50,
                        startDate: DateValue.make(2022, 5, 4),
                        purchaseUnit: '#EACH',
                        stockUnit: '#EACH',
                        requirementDate: DateValue.make(2022, 5, 15),
                        grossPrice: 2000,
                        status: 'pending',
                        suggestionSource: 'MRP',
                        suggestionDate: DateValue.make(2022, 4, 15),
                        suggestionUser: '#<EMAIL>',
                    });

                    await xtremSupplyChain.functions.bulkCreatePurchaseOrderHandler(context, [
                        await SupplPlanningPurchaseSite.$.payload({ withIds: true }),
                    ]);

                    Sinon.assert.calledOnceWithExactly(logMessageMock, 'error', 'Purchase site is required.');
                },
                { today: '2022-05-15' },
            ));

        it('Supplier is required', () =>
            Test.withContext(
                async context => {
                    const logMessageMock = xtremSystem.TestHelpers.Sinon.registerMocks([
                        {
                            type: 'stub',
                            isMethod: true,
                            class: BatchContext,
                            name: 'logMessage',
                        },
                    ])[0].mock as Sinon.SinonStub;

                    await xtremMasterData.functions.testLib.simulateBatchContext(context);

                    const newSupplPlanning = await context.create(xtremSupplyChain.nodes.SupplyPlanning, {
                        mrpResultLine: 14,
                        type: 'purchased',
                        purchaseSite: '#US001',
                        itemSite: '#Chair|US001',
                        supplier: null,
                        stockQuantity: 100,
                        purchaseQuantity: 50,
                        startDate: DateValue.make(2022, 5, 4),
                        purchaseUnit: '#EACH',
                        stockUnit: '#EACH',
                        requirementDate: DateValue.make(2022, 5, 15),
                        grossPrice: 2000,
                        status: 'pending',
                        suggestionSource: 'MRP',
                        suggestionDate: DateValue.make(2022, 4, 15),
                        suggestionUser: '#<EMAIL>',
                    });

                    await xtremSupplyChain.functions.bulkCreatePurchaseOrderHandler(context, [
                        await newSupplPlanning.$.payload({ withIds: true }),
                    ]);

                    Sinon.assert.calledOnceWithExactly(logMessageMock, 'error', 'Supplier is required.');
                },
                { today: '2022-05-15' },
            ));

        it('Purchase quantity is required', () =>
            Test.withContext(
                async context => {
                    const logMessageMock = xtremSystem.TestHelpers.Sinon.registerMocks([
                        {
                            type: 'stub',
                            isMethod: true,
                            class: BatchContext,
                            name: 'logMessage',
                        },
                    ])[0].mock as Sinon.SinonStub;

                    await xtremMasterData.functions.testLib.simulateBatchContext(context);

                    const SupplPlanningPurchaseQuantity = await context.create(xtremSupplyChain.nodes.SupplyPlanning, {
                        mrpResultLine: 14,
                        type: 'purchased',
                        purchaseSite: '#US001',
                        itemSite: '#Chair|US001',
                        supplier: '#LECLERC',
                        stockQuantity: 100,
                        purchaseQuantity: null,
                        startDate: DateValue.make(2022, 5, 4),
                        purchaseUnit: '#EACH',
                        stockUnit: '#EACH',
                        requirementDate: DateValue.make(2022, 5, 15),
                        grossPrice: 2000,
                        status: 'pending',
                        suggestionSource: 'MRP',
                        suggestionDate: DateValue.make(2022, 4, 15),
                        suggestionUser: '#<EMAIL>',
                    });

                    await xtremSupplyChain.functions.bulkCreatePurchaseOrderHandler(context, [
                        await SupplPlanningPurchaseQuantity.$.payload({ withIds: true }),
                    ]);

                    Sinon.assert.calledOnceWithExactly(logMessageMock, 'error', 'Purchase quantity is required.');
                },
                { today: '2022-05-15' },
            ));

        it('Purchase unit is required', () =>
            Test.withContext(
                async context => {
                    const logMessageMock = xtremSystem.TestHelpers.Sinon.registerMocks([
                        {
                            type: 'stub',
                            isMethod: true,
                            class: BatchContext,
                            name: 'logMessage',
                        },
                    ])[0].mock as Sinon.SinonStub;

                    await xtremMasterData.functions.testLib.simulateBatchContext(context);

                    const SupplPlanningPurchaseUnit = await context.create(xtremSupplyChain.nodes.SupplyPlanning, {
                        mrpResultLine: 14,
                        type: 'purchased',
                        purchaseSite: '#US001',
                        itemSite: '#Chair|US001',
                        supplier: '#LECLERC',
                        stockQuantity: 100,
                        purchaseQuantity: 50,
                        startDate: DateValue.make(2022, 5, 4),
                        purchaseUnit: null,
                        stockUnit: '#EACH',
                        requirementDate: DateValue.make(2022, 5, 15),
                        grossPrice: 2000,
                        status: 'pending',
                        suggestionSource: 'MRP',
                        suggestionDate: DateValue.make(2022, 4, 15),
                        suggestionUser: '#<EMAIL>',
                    });

                    await xtremSupplyChain.functions.bulkCreatePurchaseOrderHandler(context, [
                        await SupplPlanningPurchaseUnit.$.payload({ withIds: true }),
                    ]);

                    Sinon.assert.calledOnceWithExactly(logMessageMock, 'error', 'Purchase unit is required.');
                },
                { today: '2022-05-15' },
            ));

        it('The purchase order is already created', () =>
            Test.withContext(
                async context => {
                    const logMessageMock = xtremSystem.TestHelpers.Sinon.registerMocks([
                        {
                            type: 'stub',
                            isMethod: true,
                            class: BatchContext,
                            name: 'logMessage',
                        },
                    ])[0].mock as Sinon.SinonStub;

                    await xtremMasterData.functions.testLib.simulateBatchContext(context);

                    const SupplPlanningStatus = await context.create(xtremSupplyChain.nodes.SupplyPlanning, {
                        mrpResultLine: 14,
                        type: 'purchased',
                        purchaseSite: '#US001',
                        itemSite: '#Chair|US001',
                        supplier: '#LECLERC',
                        stockQuantity: 100,
                        purchaseQuantity: 50,
                        startDate: DateValue.make(2022, 5, 4),
                        purchaseUnit: '#EACH',
                        stockUnit: '#EACH',
                        requirementDate: DateValue.make(2022, 5, 15),
                        grossPrice: 2000,
                        status: 'createOrder',
                        suggestionSource: 'MRP',
                        suggestionDate: DateValue.make(2022, 4, 15),
                        suggestionUser: '#<EMAIL>',
                    });

                    await xtremSupplyChain.functions.bulkCreatePurchaseOrderHandler(context, [
                        await SupplPlanningStatus.$.payload({ withIds: true }),
                    ]);

                    Sinon.assert.calledOnceWithExactly(
                        logMessageMock,
                        'error',
                        'The purchase order is already created.',
                    );
                },
                { today: '2022-05-15' },
            ));

        it('The start date cannot be set after today', () =>
            Test.withContext(
                async context => {
                    const logMessageMock = xtremSystem.TestHelpers.Sinon.registerMocks([
                        {
                            type: 'stub',
                            isMethod: true,
                            class: BatchContext,
                            name: 'logMessage',
                        },
                    ])[0].mock as Sinon.SinonStub;

                    await xtremMasterData.functions.testLib.simulateBatchContext(context);

                    const SupplPlanningStatus = await context.create(xtremSupplyChain.nodes.SupplyPlanning, {
                        mrpResultLine: 14,
                        type: 'purchased',
                        purchaseSite: '#US001',
                        itemSite: '#Chair|US001',
                        supplier: '#LECLERC',
                        stockQuantity: 100,
                        purchaseQuantity: 50,
                        startDate: DateValue.make(2022, 5, 4),
                        purchaseUnit: '#EACH',
                        stockUnit: '#EACH',
                        requirementDate: DateValue.make(2022, 5, 15),
                        grossPrice: 2000,
                        status: 'pending',
                        suggestionSource: 'MRP',
                        suggestionDate: DateValue.make(2022, 4, 15),
                        suggestionUser: '#<EMAIL>',
                    });

                    await xtremSupplyChain.functions.bulkCreatePurchaseOrderHandler(context, [
                        await SupplPlanningStatus.$.payload({ withIds: true }),
                    ]);

                    Sinon.assert.calledOnceWithExactly(
                        logMessageMock,
                        'error',
                        'The start date needs to be on or before the current date.',
                    );
                },
                { today: '2022-05-01' },
            ));

        it('The requirement date cannot be set before today', () =>
            Test.withContext(
                async context => {
                    const logMessageMock = xtremSystem.TestHelpers.Sinon.registerMocks([
                        {
                            type: 'stub',
                            isMethod: true,
                            class: BatchContext,
                            name: 'logMessage',
                        },
                    ])[0].mock as Sinon.SinonStub;

                    await xtremMasterData.functions.testLib.simulateBatchContext(context);

                    const SupplPlanningStatus = await context.create(xtremSupplyChain.nodes.SupplyPlanning, {
                        mrpResultLine: 14,
                        type: 'purchased',
                        purchaseSite: '#US001',
                        itemSite: '#Chair|US001',
                        supplier: '#LECLERC',
                        stockQuantity: 100,
                        purchaseQuantity: 50,
                        startDate: DateValue.make(2022, 5, 4),
                        purchaseUnit: '#EACH',
                        stockUnit: '#EACH',
                        requirementDate: DateValue.make(2022, 5, 15),
                        grossPrice: 2000,
                        status: 'pending',
                        suggestionSource: 'MRP',
                        suggestionDate: DateValue.make(2022, 4, 15),
                        suggestionUser: '#<EMAIL>',
                    });

                    await xtremSupplyChain.functions.bulkCreatePurchaseOrderHandler(context, [
                        await SupplPlanningStatus.$.payload({ withIds: true }),
                    ]);

                    Sinon.assert.calledOnceWithExactly(
                        logMessageMock,
                        'error',
                        'The requirement date needs to be on or after the current date.',
                    );
                },
                { today: '2023-05-15' },
            ));
    });

    describe('Create work order validation messages', () => {
        it('The start date cannot be set before today', () =>
            Test.withContext(
                async context => {
                    const logMessageMock = xtremSystem.TestHelpers.Sinon.registerMocks([
                        {
                            type: 'stub',
                            isMethod: true,
                            class: BatchContext,
                            name: 'logMessage',
                        },
                    ])[0].mock as Sinon.SinonStub;

                    await xtremMasterData.functions.testLib.simulateBatchContext(context);

                    const SupplPlanningWorkOrderCategory = await context.create(xtremSupplyChain.nodes.SupplyPlanning, {
                        mrpResultLine: 11,
                        workOrderCategory: '#Normal',
                        type: 'manufactured',
                        itemSite: '#Chair|US001',
                        stockQuantity: 100,
                        startDate: DateValue.make(2024, 4, 1),
                        stockUnit: '#EACH',
                        requirementDate: DateValue.make(2024, 5, 15),
                        status: 'pending',
                        suggestionSource: 'MRP',
                        suggestionDate: DateValue.make(2024, 4, 5),
                        suggestionUser: '#<EMAIL>',
                    });

                    await xtremSupplyChain.functions.bulkCreateWorkOrderHandler(context, [
                        await SupplPlanningWorkOrderCategory.$.payload({ withIds: true }),
                    ]);

                    Sinon.assert.calledOnceWithExactly(
                        logMessageMock,
                        'error',
                        'The start date needs to be on or after the current date.',
                    );
                },
                { today: '2024-04-05' },
            ));

        it('Work order category is required', () =>
            Test.withContext(
                async context => {
                    const logMessageMock = xtremSystem.TestHelpers.Sinon.registerMocks([
                        {
                            type: 'stub',
                            isMethod: true,
                            class: BatchContext,
                            name: 'logMessage',
                        },
                    ])[0].mock as Sinon.SinonStub;

                    await xtremMasterData.functions.testLib.simulateBatchContext(context);

                    const SupplPlanningWorkOrderCategory = await context.create(xtremSupplyChain.nodes.SupplyPlanning, {
                        mrpResultLine: 11,
                        type: 'manufactured',
                        itemSite: '#Chair|US001',
                        stockQuantity: 100,
                        startDate: DateValue.make(2024, 4, 5),
                        stockUnit: '#EACH',
                        requirementDate: DateValue.make(2024, 5, 15),
                        status: 'pending',
                        suggestionSource: 'MRP',
                        suggestionDate: DateValue.make(2024, 4, 5),
                        suggestionUser: '#<EMAIL>',
                        workOrderCategory: null,
                    });

                    await xtremSupplyChain.functions.bulkCreateWorkOrderHandler(context, [
                        await SupplPlanningWorkOrderCategory.$.payload({ withIds: true }),
                    ]);

                    Sinon.assert.calledOnceWithExactly(logMessageMock, 'error', 'You need to add a category.');
                },
                { today: '2024-04-05' },
            ));

        it('Work order type is required', () =>
            Test.withContext(
                async context => {
                    const logMessageMock = xtremSystem.TestHelpers.Sinon.registerMocks([
                        {
                            type: 'stub',
                            isMethod: true,
                            class: BatchContext,
                            name: 'logMessage',
                        },
                    ])[0].mock as Sinon.SinonStub;

                    await xtremMasterData.functions.testLib.simulateBatchContext(context);

                    const SupplPlanningWorkOrderCategory = await context.create(xtremSupplyChain.nodes.SupplyPlanning, {
                        mrpResultLine: 11,
                        type: 'manufactured',
                        workOrderCategory: '#Normal',
                        workOrderType: null,
                        itemSite: '#Chair|US001',
                        stockQuantity: 100,
                        startDate: DateValue.make(2024, 4, 5),
                        stockUnit: '#EACH',
                        requirementDate: DateValue.make(2024, 5, 15),
                        status: 'pending',
                        suggestionSource: 'MRP',
                        suggestionDate: DateValue.make(2024, 4, 5),
                        suggestionUser: '#<EMAIL>',
                    });

                    await xtremSupplyChain.functions.bulkCreateWorkOrderHandler(context, [
                        await SupplPlanningWorkOrderCategory.$.payload({ withIds: true }),
                    ]);

                    Sinon.assert.calledOnceWithExactly(logMessageMock, 'error', 'You need to add a type.');
                },
                { today: '2024-04-05' },
            ));

        it('Stock quantity is required', () =>
            Test.withContext(
                async context => {
                    const logMessageMock = xtremSystem.TestHelpers.Sinon.registerMocks([
                        {
                            type: 'stub',
                            isMethod: true,
                            class: BatchContext,
                            name: 'logMessage',
                        },
                    ])[0].mock as Sinon.SinonStub;

                    await xtremMasterData.functions.testLib.simulateBatchContext(context);

                    const SupplPlanningWorkOrderCategory = await context.create(xtremSupplyChain.nodes.SupplyPlanning, {
                        mrpResultLine: 11,
                        type: 'manufactured',
                        workOrderCategory: '#Normal',
                        itemSite: '#Chair|US001',
                        stockQuantity: 0,
                        startDate: DateValue.make(2024, 4, 5),
                        stockUnit: '#EACH',
                        requirementDate: DateValue.make(2024, 5, 15),
                        status: 'pending',
                        suggestionSource: 'MRP',
                        suggestionDate: DateValue.make(2024, 4, 5),
                        suggestionUser: '#<EMAIL>',
                    });

                    await xtremSupplyChain.functions.bulkCreateWorkOrderHandler(context, [
                        await SupplPlanningWorkOrderCategory.$.payload({ withIds: true }),
                    ]);

                    Sinon.assert.calledOnceWithExactly(logMessageMock, 'error', 'You need to add a quantity.');
                },
                { today: '2024-04-05' },
            ));

        it('This number is already used in an existing work order', () =>
            Test.withContext(
                async context => {
                    const logMessageMock = xtremSystem.TestHelpers.Sinon.registerMocks([
                        {
                            type: 'stub',
                            isMethod: true,
                            class: BatchContext,
                            name: 'logMessage',
                        },
                    ])[0].mock as Sinon.SinonStub;

                    await xtremMasterData.functions.testLib.simulateBatchContext(context);

                    const SupplPlanningWorkOrderCategory = await context.create(xtremSupplyChain.nodes.SupplyPlanning, {
                        mrpResultLine: 11,
                        type: 'manufactured',
                        workOrderCategory: '#Normal',
                        workOrderNumber: 'WO240001',
                        itemSite: '#CCZ_APPLE_PIE|DEP1-S01',
                        stockQuantity: 100,
                        startDate: DateValue.make(2024, 4, 5),
                        stockUnit: '#EACH',
                        requirementDate: DateValue.make(2024, 5, 15),
                        status: 'pending',
                        suggestionSource: 'MRP',
                        suggestionDate: DateValue.make(2024, 4, 5),
                        suggestionUser: '#<EMAIL>',
                    });

                    await xtremSupplyChain.functions.bulkCreateWorkOrderHandler(context, [
                        await SupplPlanningWorkOrderCategory.$.payload({ withIds: true }),
                    ]);

                    Sinon.assert.calledOnceWithExactly(
                        logMessageMock,
                        'error',
                        'This work order number has been used. Enter a different number.',
                    );
                },
                { today: '2024-04-05' },
            ));

        it('There is no valid routing or bill of material for this work order category, item and site', () =>
            Test.withContext(
                async context => {
                    const logMessageMock = xtremSystem.TestHelpers.Sinon.registerMocks([
                        {
                            type: 'stub',
                            isMethod: true,
                            class: BatchContext,
                            name: 'logMessage',
                        },
                    ])[0].mock as Sinon.SinonStub;

                    await xtremMasterData.functions.testLib.simulateBatchContext(context);

                    const SupplPlanningWorkOrderCategory = await context.create(xtremSupplyChain.nodes.SupplyPlanning, {
                        mrpResultLine: 11,
                        type: 'manufactured',
                        workOrderCategory: '#Normal',
                        itemSite: '#STKCOUNT3|US001',
                        stockQuantity: 100,
                        startDate: DateValue.make(2024, 4, 5),
                        stockUnit: '#EACH',
                        requirementDate: DateValue.make(2024, 5, 15),
                        status: 'pending',
                        suggestionSource: 'MRP',
                        suggestionDate: DateValue.make(2024, 4, 5),
                        suggestionUser: '#<EMAIL>',
                    });

                    await xtremSupplyChain.functions.bulkCreateWorkOrderHandler(context, [
                        await SupplPlanningWorkOrderCategory.$.payload({ withIds: true }),
                    ]);

                    Sinon.assert.calledOnceWithExactly(
                        logMessageMock,
                        'error',
                        'You need to select a different category. There is no routing or bill of material for this item-site.',
                    );
                },
                { today: '2024-04-05' },
            ));

        it('The work order is already created', () =>
            Test.withContext(
                async context => {
                    const logMessageMock = xtremSystem.TestHelpers.Sinon.registerMocks([
                        {
                            type: 'stub',
                            isMethod: true,
                            class: BatchContext,
                            name: 'logMessage',
                        },
                    ])[0].mock as Sinon.SinonStub;

                    await xtremMasterData.functions.testLib.simulateBatchContext(context);

                    const SupplPlanningWorkOrderCategory = await context.create(xtremSupplyChain.nodes.SupplyPlanning, {
                        mrpResultLine: 11,
                        type: 'manufactured',
                        workOrderCategory: '#Normal',
                        itemSite: '#CCZ_APPLE_PIE|DEP1-S01',
                        stockQuantity: 100,
                        startDate: DateValue.make(2024, 4, 5),
                        stockUnit: '#EACH',
                        requirementDate: DateValue.make(2024, 5, 15),
                        status: 'createOrder',
                        suggestionSource: 'MRP',
                        suggestionDate: DateValue.make(2024, 4, 5),
                        suggestionUser: '#<EMAIL>',
                    });

                    await xtremSupplyChain.functions.bulkCreateWorkOrderHandler(context, [
                        await SupplPlanningWorkOrderCategory.$.payload({ withIds: true }),
                    ]);

                    Sinon.assert.calledOnceWithExactly(logMessageMock, 'error', 'The work order is already created.');
                },
                { today: '2024-04-05' },
            ));
    });

    afterEach(() => {
        xtremSystem.TestHelpers.Sinon.removeMocks();
    });
});
