import { asyncArray, date, Test, ValidationSeverity } from '@sage/xtrem-core';
import * as xtremFinanceData from '@sage/xtrem-finance-data';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremStockData from '@sage/xtrem-stock-data';
import { assert } from 'chai';
import * as sinon from 'sinon';
import * as xtremSupplyChain from '../../../lib';
import { StockTransferShipment } from '../../../lib/nodes';
import * as fixtures from '../../fixtures';

describe('StockTransferShipment', () => {
    it('Create StockTransferShipment creation fails 01 - The stock transfer shipment must contain at least one line', () =>
        Test.withContext(async context => {
            const stockTransferShipment = await fixtures.createStockTransferShipment(context, true);

            await assert.isRejected(stockTransferShipment.$.save());
            assert.deepEqual(stockTransferShipment.$.context.diagnoses, [
                {
                    message: 'The document needs at least one line.',
                    path: [],
                    severity: 3,
                },
            ]);
        }));

    it('Create StockTransferShipment creation fails 02 - supplier not active', () =>
        Test.withContext(async context => {
            const supplier = await context.read(xtremMasterData.nodes.Supplier, { _id: '#CAS01' }, { forUpdate: true });
            await supplier.$.set({ isActive: false });
            await supplier.$.save();

            const stockTransferShipment = await fixtures.createStockTransferShipment(context, false);
            await assert.isRejected(stockTransferShipment.$.save());
            assert.deepEqual(stockTransferShipment.$.context.diagnoses, [
                {
                    message: 'The record is not valid. You need to select a different record.',
                    path: ['site'],
                    severity: ValidationSeverity.error,
                },
                {
                    message: 'The record cannot be referenced because it is inactive.',
                    path: ['supplier'],
                    severity: ValidationSeverity.error,
                },
                {
                    message: 'Supplier CAS01 is not active',
                    path: ['supplier'],
                    severity: ValidationSeverity.error,
                },
            ]);
        }));

    it('Create StockTransferShipment creation fails 03 - financial site needs to be the same for site and receiving site', () =>
        Test.withContext(async context => {
            const stockTransferShipment = await fixtures.createStockTransferShipment(context, false, {
                site: '#US016',
                receivingSite: '#CAS02',
            });

            await assert.isRejected(stockTransferShipment.$.save());
            assert.deepEqual(stockTransferShipment.$.context.diagnoses, [
                {
                    severity: ValidationSeverity.error,
                    path: [],
                    message:
                        'The financial site for the receiving site needs to be the same as the financial site for the shipping site.',
                },
                {
                    message: 'The record is not valid. You need to select a different record.',
                    path: ['lines', '-1000000002', 'site'],
                    severity: ValidationSeverity.error,
                },
                {
                    message: 'The record is not valid. You need to select a different record.',
                    path: ['lines', '-1000000002', 'item'],
                    severity: ValidationSeverity.error,
                },
                {
                    message: 'The record is not valid. You need to select a different record.',
                    path: ['lines', '-1000000002', 'stockSite'],
                    severity: ValidationSeverity.error,
                },
            ]);
        }));

    it('Create StockTransferShipment creation fails 04 - line site needs to be the same as line transfer site', () =>
        Test.withContext(async context => {
            const stockTransferShipment = await fixtures.createStockTransferShipment(
                context,
                false,
                { site: '#CAS01', receivingSite: '#CAS02' },
                date.parse('2024-07-08', context.currentLocale as any),
                date.parse('2026-03-03', context.currentLocale as any),
                {
                    businessRelation: '#CAS02',
                },
                [
                    {
                        item: '#STOFIFO',
                        site: '#US016',
                        stockSite: '#CAS01',
                        quantity: 1,
                    },
                ],
            );

            await assert.isRejected(stockTransferShipment.$.save());
            assert.deepEqual(stockTransferShipment.$.context.diagnoses, [
                {
                    message: 'The line site needs to be the same as the transfer site.',
                    path: ['lines', '-1000000002'],
                    severity: ValidationSeverity.error,
                },
                {
                    message: 'The record is not valid. You need to select a different record.',
                    path: ['lines', '-1000000002', 'site'],
                    severity: ValidationSeverity.error,
                },
            ]);
        }));

    it('Create StockTransferShipment creation fails 05 - shipment is ready to or has already been shipped', () =>
        Test.withContext(async context => {
            const stockTransferShipment = await fixtures.createStockTransferShipment(
                context,
                false,
                { site: '#CAS01', receivingSite: '#CAS02' },
                date.parse('2024-07-08', context.currentLocale as any),
                date.parse('2026-03-03', context.currentLocale as any),
                {
                    status: 'readyToShip',
                    businessRelation: '#CAS02',
                },
                [
                    {
                        item: '#STOFIFO',
                        stockSite: '#CAS01',
                        quantity: 1,
                        status: 'readyToShip',
                    },
                ],
            );

            await assert.isRejected(stockTransferShipment.$.save());

            assert.deepEqual(stockTransferShipment.$.context.diagnoses, [
                {
                    message: 'The line status needs to be Ready to process.',
                    path: ['lines', '-1000000002'],
                    severity: ValidationSeverity.error,
                },
                {
                    message:
                        'The stock transfer shipment is ready to ship or has already been shipped. You cannot add a new line.',
                    path: ['lines', '-1000000002'],
                    severity: ValidationSeverity.error,
                },
            ]);
        }));

    it('Create StockTransferShipment creation success 02', () =>
        Test.withContext(async context => {
            const stockTransferShipment = await fixtures.createStockTransferShipment(
                context,
                false,
                { site: '#CAS01', receivingSite: '#CAS02' },
                date.parse('2024-07-08', context.currentLocale as any),
                date.parse('2026-03-03', context.currentLocale as any),

                {
                    businessRelation: '#CAS02',
                },
            );

            await stockTransferShipment.$.save();

            assert.deepEqual(stockTransferShipment.$.context.diagnoses, []);
            assert.deepEqual(
                JSON.stringify(await (await stockTransferShipment.lines.elementAt(0)).computedAttributes),
                JSON.stringify({
                    financialSite: 'CAS01',
                    stockSite: 'CAS01',
                    item: 'STOFIFO',
                    customer: 'CAS02',
                    supplier: 'CAS01',
                }),
            );
        }));

    it('Create stock transfer receipt', () =>
        Test.withContext(
            async context => {
                const stockTransferOrderNumber = 'TOTEST01';
                const stockTransferOrder = await fixtures.readTestStockTransferOrder(context, stockTransferOrderNumber);

                await stockTransferOrder.lines.forEach(async (line: xtremSupplyChain.nodes.StockTransferOrderLine) => {
                    await line.$.set({ status: 'pending' });
                });
                await stockTransferOrder.$.set({ status: 'pending' });
                await stockTransferOrder.$.save();

                const lines = await stockTransferOrder.lines.toArray();

                // Line 0
                assert.equal(await lines[0].status, 'pending');

                const stockTransferShipment =
                    await xtremSupplyChain.nodes.StockTransferOrder.createStockTransferShipment(
                        context,
                        stockTransferOrder,
                    );
                assert.deepEqual(stockTransferOrder.$.context.diagnoses, []);

                await context.flushDeferredActions();
                assert.equal(await stockTransferShipment.number, 'TS240002');

                const stockTransferReceipt =
                    await xtremSupplyChain.nodes.StockTransferShipment.createStockTransferReceipt(
                        context,
                        stockTransferShipment,
                    );

                await context.flushDeferredActions();
                assert.equal(await stockTransferReceipt.number, 'TR240002');
            },
            { today: '2024-12-15' },
        ));

    it('Stock transfer receipt - confirm', () =>
        Test.withContext(async context => {
            const stockTransferOrderNumber = 'TOTEST01';
            const stockTransferOrder = await fixtures.readTestStockTransferOrder(context, stockTransferOrderNumber);

            await stockTransferOrder.lines.forEach(async (line: xtremSupplyChain.nodes.StockTransferOrderLine) => {
                await line.$.set({ status: 'pending' });
            });
            await stockTransferOrder.$.set({ status: 'pending' });
            await stockTransferOrder.$.save();

            const stockTransferShipment = await xtremSupplyChain.nodes.StockTransferOrder.createStockTransferShipment(
                context,
                stockTransferOrder,
            );

            await context.flushDeferredActions();

            await assert.isRejected(
                xtremSupplyChain.nodes.StockTransferShipment.confirm(context, await stockTransferShipment.number),
                'You need to allocate stock in full to all shipment lines before you can confirm the shipment.',
            );
        }));

    it('Stock transfer receipt - revoke', () =>
        Test.withContext(async context => {
            const stockTransferOrderNumber = 'TOTEST01';
            const stockTransferOrder = await fixtures.readTestStockTransferOrder(context, stockTransferOrderNumber);

            await stockTransferOrder.lines.forEach(async (line: xtremSupplyChain.nodes.StockTransferOrderLine) => {
                await line.$.set({ status: 'pending' });
            });
            await stockTransferOrder.$.set({ status: 'pending' });
            await stockTransferOrder.$.save();

            const stockTransferShipment = await xtremSupplyChain.nodes.StockTransferOrder.createStockTransferShipment(
                context,
                stockTransferOrder,
            );

            await context.flushDeferredActions();

            await assert.isRejected(
                xtremSupplyChain.nodes.StockTransferShipment.revoke(context, await stockTransferShipment.number),
                "You can only revert a shipment if the status is 'Ready to ship'",
            );
        }));

    it('Stock transfer receipt - repost', () =>
        Test.withContext(async context => {
            const stockTransferOrderNumber = 'TOTEST01';
            const stockTransferOrder = await fixtures.readTestStockTransferOrder(context, stockTransferOrderNumber);

            await stockTransferOrder.lines.forEach(async (line: xtremSupplyChain.nodes.StockTransferOrderLine) => {
                await line.$.set({ status: 'pending' });
            });
            await stockTransferOrder.$.set({ status: 'pending' });
            await stockTransferOrder.$.save();

            const stockTransferShipment = await xtremSupplyChain.nodes.StockTransferOrder.createStockTransferShipment(
                context,
                stockTransferOrder,
            );

            await context.flushDeferredActions();

            const documentLines = stockTransferShipment.lines
                .filter(line => Number(line._id) > 0)
                .map(line => ({
                    baseDocumentLineSysId: line._id,
                    storedAttributes: line.storedAttributes,
                    storedDimensions: line.storedDimensions,
                }));

            await assert.isRejected(
                xtremSupplyChain.nodes.StockTransferShipment.repost(
                    context,
                    stockTransferShipment,
                    documentLines as any,
                ),
                'You can only repost a stock transfer shipment if the status is Failed or Not recorded.',
            );
        }));

    it('Stock transfer receipt - onceStockCompleted', () =>
        Test.withContext(async context => {
            const stockTransferOrderNumber = 'TOTEST01';
            const stockTransferOrder = await fixtures.readTestStockTransferOrder(context, stockTransferOrderNumber);

            await stockTransferOrder.lines.forEach(async (line: xtremSupplyChain.nodes.StockTransferOrderLine) => {
                await line.$.set({ status: 'pending' });
            });
            await stockTransferOrder.$.set({ status: 'pending' });
            await stockTransferOrder.$.save();

            const stockTransferShipment = await xtremSupplyChain.nodes.StockTransferOrder.createStockTransferShipment(
                context,
                stockTransferOrder,
            );

            await context.flushDeferredActions();

            await assert.isFulfilled(
                xtremSupplyChain.nodes.StockTransferShipment.onceStockCompleted(context, stockTransferShipment),
            );
        }));

    it('Stock transfer shipment - onStockReply', () =>
        Test.withContext(
            async context => {
                const stockTransferShipmentId = (
                    await context.select(StockTransferShipment, { _id: true }, { filter: { number: 'TSTEST04' } })
                )[0]._id;

                await xtremStockData.functions.testLib.simulateStockTransaction(
                    context,
                    stockTransferShipmentId,
                    StockTransferShipment,
                    'transfer',
                    StockTransferShipment.onStockReply,
                    { stockUpdateResultStatus: 'decreased' },
                );

                const stockTransferShipment = await context.read(StockTransferShipment, {
                    number: 'TSTEST04',
                });
                const stockTransferShipmentLines = await stockTransferShipment.lines.toArray();
                assert.equal(stockTransferShipmentLines.length, 2);
                assert.deepEqual(await stockTransferShipmentLines[0].orderCost, 5.0);
                assert.deepEqual(await stockTransferShipmentLines[1].orderCost, 7.0);
                const inTransitValues = [
                    { quantityInStockUnit: -3.0, unitCost: 5.0, stockValue: 15.0 },
                    { quantityInStockUnit: -4.0, unitCost: 7.0, stockValue: 28.0 },
                ];

                await asyncArray(stockTransferShipmentLines).forEach(async (stockTransferShipmentLine, index) => {
                    const stockTransferLinks =
                        await stockTransferShipmentLine.linkToStockTransferReceiptLines.toArray();
                    assert.equal(stockTransferLinks.length, 1);
                    const receiptLine = await stockTransferLinks[0].from;

                    const stockTransferShipmentLineInTransit = await stockTransferShipmentLine.inTransit;
                    assert.deepEqual(
                        await stockTransferShipmentLineInTransit?.quantityInStockUnit,
                        inTransitValues[index].quantityInStockUnit,
                    );
                    assert.deepEqual(
                        await stockTransferShipmentLineInTransit?.unitCost,
                        inTransitValues[index].unitCost,
                    );
                    assert.deepEqual(
                        await stockTransferShipmentLineInTransit?.stockValue,
                        inTransitValues[index].stockValue,
                    );
                    assert.deepEqual((await receiptLine.item)._id, (await stockTransferShipmentLine.item)._id);
                    assert.deepEqual(
                        await receiptLine.quantityInStockUnit,
                        await stockTransferShipmentLine.quantityInStockUnit,
                    );
                });
            },
            { user: { email: '<EMAIL>' } },
        ));
});

describe('Resend notification for finance', () => {
    it('Stock Transfer Shipment - Successfully resend notification for finance', () =>
        Test.withContext(
            async context => {
                const documentNumber = 'TS250001';

                const stockTransferShipment: xtremSupplyChain.nodes.StockTransferShipment = await context.read(
                    xtremSupplyChain.nodes.StockTransferShipment,
                    {
                        number: documentNumber,
                    },
                );

                const filter = { documentNumber, documentType: 'stockTransferShipment' };
                let amountType = await (
                    await (
                        await context
                            .query(xtremFinanceData.nodes.AccountingStaging, {
                                filter,
                            })
                            .at(0)
                    )?.amounts.at(0)
                )?.amountType;

                let financeTransactionRecord = await context
                    .query(xtremFinanceData.nodes.FinanceTransaction, { filter })
                    .at(0);
                let financeTransactionStatus = await financeTransactionRecord?.status;
                let financeTransactionMessage = await financeTransactionRecord?.message;

                assert.equal(amountType, 'inTransitAmount');
                assert.equal(financeTransactionStatus, 'notRecorded');
                assert.notEqual(financeTransactionMessage, '');

                const notifySpy = sinon.spy(context, 'notify');
                await xtremSupplyChain.nodes.StockTransferShipment.resendNotificationForFinance(
                    context,
                    stockTransferShipment,
                );

                assert.equal(notifySpy.getCalls().length, 0);

                amountType = await (
                    await (
                        await context
                            .query(xtremFinanceData.nodes.AccountingStaging, {
                                filter,
                            })
                            .at(0)
                    )?.amounts.at(0)
                )?.amountType;

                financeTransactionRecord = await context
                    .query(xtremFinanceData.nodes.FinanceTransaction, { filter })
                    .at(0);
                financeTransactionStatus = await financeTransactionRecord?.status;
                financeTransactionMessage = await financeTransactionRecord?.message;

                assert.equal(amountType, 'inTransitAmount');
                assert.equal(financeTransactionStatus, 'pending');
                assert.equal(financeTransactionMessage, '');
            },
            { today: '2025-02-18' },
        ));

    it('Stock change detail - intersite stock received detail record not removed when stock record deleted', () =>
        Test.withContext(async context => {
            await context.delete(xtremStockData.nodes.Stock, { _id: 124 });
            const stockChangeDetail = await context.read(xtremStockData.nodes.StockChangeDetail, {
                _id: 350401,
            });
            assert.equal(JSON.stringify((await stockChangeDetail.intersiteReceivedStockRecord)?._id), '128');
        }));

    describe('Pre/Post printing mutations', () => {
        it('stockTransferPackingSlip - beforePrintStockTransferPackingSlip - checks the status of the shipment before printing the packing slip', () =>
            Test.withContext(async context => {
                const stockTransferShipment = await context.read(xtremSupplyChain.nodes.StockTransferShipment, {
                    _id: '#TSTEST01',
                });
                await assert.isRejected(
                    xtremSupplyChain.nodes.StockTransferShipment.beforePrintStockTransferPackingSlip(
                        context,
                        stockTransferShipment,
                    ),
                    'To print the packing slip, the stock transfer shipment cannot be Ready to process: TSTEST01.',
                );
            }));
        it('stockTransferPackingSlip - afterPrintStockTransferPackingSlip - sets the isPrinted flag to true', () =>
            Test.withContext(async context => {
                let stockTransferShipment = await context.read(
                    xtremSupplyChain.nodes.StockTransferShipment,
                    { _id: '#TSTEST03' },
                    { forUpdate: true },
                );
                assert.isFalse(await stockTransferShipment.isPrinted);

                const resultAfterPrint =
                    await xtremSupplyChain.nodes.StockTransferShipment.afterPrintStockTransferPackingSlip(
                        context,
                        stockTransferShipment,
                    );
                assert.isTrue(resultAfterPrint);

                stockTransferShipment = await context.read(xtremSupplyChain.nodes.StockTransferShipment, {
                    _id: '#TSTEST03',
                });
                assert.isTrue(await stockTransferShipment.isPrinted);
            }));
        it('stockTransferShipmentPickList - beforePrintStockTransferShipmentPickList - checks the status of the shipment before printing the picking list', () =>
            Test.withContext(async context => {
                const salesShipment = await context.read(xtremSupplyChain.nodes.StockTransferShipment, {
                    _id: '#TS240001',
                });
                await assert.isRejected(
                    xtremSupplyChain.nodes.StockTransferShipment.beforePrintStockTransferShipmentPickList(
                        context,
                        salesShipment,
                    ),
                    'To print the pick list, the stock transfer shipment needs to be Ready to process: TS240001.',
                );
            }));
        it('stockTransferShipmentPickList - printBulkStockTransferShipmentPickList', () =>
            Test.withReadonlyContext(async context => {
                const stockTransferShipment = await context.read(xtremSupplyChain.nodes.StockTransferShipment, {
                    _id: '#TSTEST02',
                });

                const result =
                    await xtremSupplyChain.nodes.StockTransferShipment.printBulkStockTransferShipmentPickList(
                        context,
                        stockTransferShipment,
                    );

                assert.equal(result.status, 'verified');
                assert.isNotEmpty(result.key);
                assert.isNotEmpty(result.downloadUrl);
            }));
        it('stockTransferShipmentPackingSlip - printBulkStockTransferShipmentPackingSlip', () =>
            Test.withReadonlyContext(
                async context => {
                    const stockTransferShipment = await context.read(xtremSupplyChain.nodes.StockTransferShipment, {
                        _id: '#TSTEST04',
                    });

                    const result =
                        await xtremSupplyChain.nodes.StockTransferShipment.printBulkStockTransferShipmentPackingSlip(
                            context,
                            stockTransferShipment,
                        );

                    assert.equal(result.status, 'verified');
                    assert.isNotEmpty(result.key);
                    assert.isNotEmpty(result.downloadUrl);
                },
                { locale: 'en-GB' },
            ));
    });
});
