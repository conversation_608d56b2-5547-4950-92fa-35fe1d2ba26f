{"@sage/xtrem-supply-chain": [{"topic": "MRPCalculationRequestListener/mrp/calculation-done/broadcast", "queue": "purchasing", "sourceFileName": "supply-planning.ts"}, {"topic": "StockTransferInTransitInquiry/asyncExport/start", "queue": "import-export", "sourceFileName": "stock-transfer-in-transit-inquiry.ts"}, {"topic": "StockTransferOrder/allocation/reply", "queue": "purchasing", "sourceFileName": "stock-transfer-order.ts"}, {"topic": "StockTransferOrder/asyncExport/start", "queue": "import-export", "sourceFileName": "stock-transfer-order.ts"}, {"topic": "StockTransferOrderLine/asyncExport/start", "queue": "import-export", "sourceFileName": "stock-transfer-order-line.ts"}, {"topic": "StockTransferReceipt/accountingInterface", "queue": "purchasing", "sourceFileName": "stock-transfer-receipt.ts"}, {"topic": "StockTransferReceipt/asyncExport/start", "queue": "import-export", "sourceFileName": "stock-transfer-receipt.ts"}, {"topic": "StockTransferReceipt/resendNotificationForFinance", "queue": "purchasing", "sourceFileName": "stock-transfer-receipt.ts"}, {"topic": "StockTransferReceipt/stock/transfer/reply", "queue": "purchasing", "sourceFileName": "stock-transfer-receipt.ts"}, {"topic": "StockTransferReceiptLine/asyncExport/start", "queue": "import-export", "sourceFileName": "stock-transfer-receipt-line.ts"}, {"topic": "StockTransferReceiptLineToStockTransferShipmentLine/asyncExport/start", "queue": "import-export", "sourceFileName": "stock-transfer-receipt-line-to-stock-transfer-shipment-line.ts"}, {"topic": "StockTransferShipment/accountingInterface", "queue": "purchasing", "sourceFileName": "stock-transfer-shipment.ts"}, {"topic": "StockTransferShipment/asyncExport/start", "queue": "import-export", "sourceFileName": "stock-transfer-shipment.ts"}, {"topic": "StockTransferShipment/printBulkStockTransferShipmentPackingSlip/start", "queue": "reporting", "sourceFileName": "stock-transfer-shipment.ts"}, {"topic": "StockTransferShipment/printBulkStockTransferShipmentPickList/start", "queue": "reporting", "sourceFileName": "stock-transfer-shipment.ts"}, {"topic": "StockTransferShipment/resendNotificationForFinance", "queue": "purchasing", "sourceFileName": "stock-transfer-shipment.ts"}, {"topic": "StockTransferShipment/stock/transfer/reply", "queue": "purchasing", "sourceFileName": "stock-transfer-shipment.ts"}, {"topic": "StockTransferShipmentLine/asyncExport/start", "queue": "import-export", "sourceFileName": "stock-transfer-shipment-line.ts"}, {"topic": "StockTransferShipmentLineInTransit/asyncExport/start", "queue": "import-export", "sourceFileName": "stock-transfer-shipment-line-in-transit.ts"}, {"topic": "StockTransferShipmentLineToStockTransferOrderLine/asyncExport/start", "queue": "import-export", "sourceFileName": "stock-transfer-shipment-line-to-stock-transfer-order-line.ts"}, {"topic": "SupplyPlanning/asyncExport/start", "queue": "import-export", "sourceFileName": "supply-planning.ts"}, {"topic": "SupplyPlanning/createOrder/start", "queue": "purchasing", "sourceFileName": "supply-planning.ts"}, {"topic": "SupplyPlanning/createWorkOrder/start", "queue": "purchasing", "sourceFileName": "supply-planning.ts"}, {"topic": "WorkInProgressStockTransferOrderLine/asyncExport/start", "queue": "import-export", "sourceFileName": "work-in-progress-stock-transfer-order-line.ts"}, {"topic": "WorkInProgressStockTransferReceiptLine/asyncExport/start", "queue": "import-export", "sourceFileName": "work-in-progress-stock-transfer-receipt-line.ts"}]}