declare module '@sage/xtrem-supply-chain-api-partial' {
    import type { Package as SageXtremAuditing$Package } from '@sage/xtrem-auditing-api';
    import type {
        GroupRoleSite,
        Package as SageXtremAuthorization$Package,
        SiteGroup,
    } from '@sage/xtrem-authorization-api';
    import type { Package as SageXtremCommunication$Package } from '@sage/xtrem-communication-api';
    import type { Package as SageXtremCustomization$Package } from '@sage/xtrem-customization-api';
    import type { Package as SageXtremDashboard$Package } from '@sage/xtrem-dashboard-api';
    import type {
        DocumentLineDiscountCharge,
        DocumentLineDiscountChargeBinding,
        DocumentLineDiscountChargeInput,
        Package as SageXtremDistribution$Package,
    } from '@sage/xtrem-distribution-api';
    import type {
        AnalyticalData,
        FinanceTransaction,
        Package as SageXtremFinanceData$Package,
        PaymentTracking,
        PaymentTrackingBinding,
        PaymentTrackingInput,
        PostingClass,
    } from '@sage/xtrem-finance-data-api';
    import type { Package as SageXtremImportExport$Package } from '@sage/xtrem-import-export-api';
    import type {
        LandedCostAllocation,
        LandedCostDocumentLine,
        LandedCostDocumentLineBinding,
        LandedCostDocumentLineInput,
        LandedCostLine,
        LandedCostLineBinding,
        LandedCostLineInput,
        Package as SageXtremLandedCost$Package,
    } from '@sage/xtrem-landed-cost-api';
    import type { Package as SageXtremMailer$Package } from '@sage/xtrem-mailer-api';
    import type {
        Package as SageXtremManufacturing$Package,
        WorkOrder,
        WorkOrderCategory,
    } from '@sage/xtrem-manufacturing-api';
    import type {
        Address,
        BaseDocumentLine,
        BusinessEntity,
        BusinessEntityAddress,
        BusinessEntityInput,
        Contact,
        Currency,
        Customer,
        DeliveryMode,
        Incoterm,
        Item,
        ItemCategory,
        ItemCustomer,
        ItemSite,
        ItemSupplier,
        Location,
        Package as SageXtremMasterData$Package,
        PaymentTerm,
        Supplier,
        UnitOfMeasure,
    } from '@sage/xtrem-master-data-api';
    import type { MetaNodeFactory, Package as SageXtremMetadata$Package } from '@sage/xtrem-metadata-api';
    import type { MrpCalculation, MrpResultLine, Package as SageXtremMrpData$Package } from '@sage/xtrem-mrp-data-api';
    import type {
        Package as SageXtremPurchasing$Package,
        PurchaseOrder,
        PurchaseRequisitionLine,
    } from '@sage/xtrem-purchasing-api';
    import type { Package as SageXtremReporting$Package } from '@sage/xtrem-reporting-api';
    import type { Package as SageXtremRouting$Package } from '@sage/xtrem-routing-api';
    import type {
        Package as SageXtremSales$Package,
        ProformaInvoice,
        SalesOrderLine,
        SalesOrderLineBinding,
        SalesOrderLineInput,
        SalesOrderTax,
        SalesOrderTaxBinding,
        SalesOrderTaxInput,
    } from '@sage/xtrem-sales-api';
    import type { Package as SageXtremScheduler$Package } from '@sage/xtrem-scheduler-api';
    import type {
        Package as SageXtremStockData$Package,
        StockAllocation,
        StockChangeDetail,
        StockChangeDetailBinding,
        StockChangeDetailInput,
        StockJournal,
        StockStatus,
        StockTransaction,
        StockTransactionBinding,
        StockTransactionInput,
    } from '@sage/xtrem-stock-data-api';
    import type { Country, Package as SageXtremStructure$Package } from '@sage/xtrem-structure-api';
    import type { Package as SageXtremSynchronization$Package } from '@sage/xtrem-synchronization-api';
    import type {
        Company,
        Package as SageXtremSystem$Package,
        Site,
        SiteInput,
        User,
        UserInput,
    } from '@sage/xtrem-system-api';
    import type {
        DocumentLineTax,
        DocumentLineTaxBinding,
        DocumentLineTaxInput,
        DocumentTax,
        DocumentTaxBinding,
        DocumentTaxInput,
        Package as SageXtremTax$Package,
    } from '@sage/xtrem-tax-api';
    import type { Package as SageXtremTechnicalData$Package } from '@sage/xtrem-technical-data-api';
    import type {
        AttachmentAssociation,
        AttachmentAssociationInput,
        Package as SageXtremUpload$Package,
    } from '@sage/xtrem-upload-api';
    import type { Package as SageXtremWorkflow$Package } from '@sage/xtrem-workflow-api';
    import type {
        AggregateQueryOperation,
        AggregateReadOperation,
        AsyncOperation,
        ClientCollection,
        ClientNode,
        ClientNodeInput,
        CreateOperation,
        DeleteOperation,
        DuplicateOperation,
        GetDefaultsOperation,
        GetDuplicateOperation,
        Operation as Node$Operation,
        QueryOperation,
        ReadOperation,
        TextStream,
        UpdateByIdOperation,
        UpdateOperation,
        VitalClientNode,
        VitalClientNodeInput,
        decimal,
        integer,
    } from '@sage/xtrem-client';
    export interface StockTransferOrderCreateShipmentMethodReturn$Enum {
        parametersAreIncorrect: 1;
        isNotShipped: 2;
        isPartiallyShipped: 3;
        isShipped: 4;
    }
    export type StockTransferOrderCreateShipmentMethodReturn = keyof StockTransferOrderCreateShipmentMethodReturn$Enum;
    export interface StockTransferOrderDisplayStatus$Enum {
        draft: 1;
        pendingApproval: 2;
        approved: 3;
        rejected: 4;
        changeRequested: 5;
        confirmed: 6;
        partiallyShipped: 9;
        shipped: 10;
        partiallyReceived: 17;
        received: 11;
        closed: 14;
        error: 16;
    }
    export type StockTransferOrderDisplayStatus = keyof StockTransferOrderDisplayStatus$Enum;
    export interface StockTransferOrderDocumentType$Enum {
        interSite: 0;
        company: 1;
        services: 2;
    }
    export type StockTransferOrderDocumentType = keyof StockTransferOrderDocumentType$Enum;
    export interface StockTransferOrderFlowType$Enum {
        oneStep: 0;
        twoSteps: 1;
    }
    export type StockTransferOrderFlowType = keyof StockTransferOrderFlowType$Enum;
    export interface StockTransferOrderStatus$Enum {
        draft: 1;
        pending: 2;
        inProgress: 3;
        closed: 4;
    }
    export type StockTransferOrderStatus = keyof StockTransferOrderStatus$Enum;
    export interface StockTransferReceiptDisplayStatus$Enum {
        readyToProcess: 7;
        received: 11;
        postingInProgress: 15;
        error: 16;
    }
    export type StockTransferReceiptDisplayStatus = keyof StockTransferReceiptDisplayStatus$Enum;
    export interface StockTransferReceiptStatus$Enum {
        readyToProcess: 26;
        received: 19;
    }
    export type StockTransferReceiptStatus = keyof StockTransferReceiptStatus$Enum;
    export interface StockTransferShipmentDisplayStatus$Enum {
        readyToProcess: 7;
        readyToShip: 8;
        shipped: 10;
        postingInProgress: 15;
        error: 16;
        received: 11;
    }
    export type StockTransferShipmentDisplayStatus = keyof StockTransferShipmentDisplayStatus$Enum;
    export interface StockTransferShipmentStatus$Enum {
        readyToProcess: 26;
        readyToShip: 27;
        shipped: 28;
        received: 19;
    }
    export type StockTransferShipmentStatus = keyof StockTransferShipmentStatus$Enum;
    export interface SupplyPlanningSource$Enum {
        MRP: 1;
    }
    export type SupplyPlanningSource = keyof SupplyPlanningSource$Enum;
    export interface SupplyPlanningStatus$Enum {
        pending: 1;
        createOrder: 2;
    }
    export type SupplyPlanningStatus = keyof SupplyPlanningStatus$Enum;
    export interface SupplyPlanningType$Enum {
        purchased: 1;
        manufactured: 2;
    }
    export type SupplyPlanningType = keyof SupplyPlanningType$Enum;
    export interface StockTransferShipmentLineInTransit extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        line: StockTransferShipmentLine;
        unitCost: string;
        currency: Currency;
        costType: CostValuationMethod;
        stockValue: string;
        quantityInStockUnit: string;
        commodityCode: string;
        startDate: string;
        endDate: string;
    }
    export interface StockTransferShipmentLineInTransitInput extends VitalClientNodeInput {
        unitCost?: decimal | string;
        currency?: integer | string;
        costType?: CostValuationMethod;
        stockValue?: decimal | string;
        quantityInStockUnit?: decimal | string;
        commodityCode?: string;
        startDate?: string;
        endDate?: string;
    }
    export interface StockTransferShipmentLineInTransitBinding extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        line: StockTransferShipmentLine;
        unitCost: string;
        currency: Currency;
        costType: CostValuationMethod;
        stockValue: string;
        quantityInStockUnit: string;
        commodityCode: string;
        startDate: string;
        endDate: string;
    }
    export interface StockTransferShipmentLineInTransit$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface StockTransferShipmentLineInTransit$Lookups {
        currency: QueryOperation<Currency>;
    }
    export interface StockTransferShipmentLineInTransit$Operations {
        query: QueryOperation<StockTransferShipmentLineInTransit>;
        read: ReadOperation<StockTransferShipmentLineInTransit>;
        aggregate: {
            read: AggregateReadOperation<StockTransferShipmentLineInTransit>;
            query: AggregateQueryOperation<StockTransferShipmentLineInTransit>;
        };
        create: CreateOperation<StockTransferShipmentLineInTransitInput, StockTransferShipmentLineInTransit>;
        getDuplicate: GetDuplicateOperation<StockTransferShipmentLineInTransit>;
        duplicate: DuplicateOperation<
            string,
            StockTransferShipmentLineInTransitInput,
            StockTransferShipmentLineInTransit
        >;
        update: UpdateOperation<StockTransferShipmentLineInTransitInput, StockTransferShipmentLineInTransit>;
        updateById: UpdateByIdOperation<StockTransferShipmentLineInTransitInput, StockTransferShipmentLineInTransit>;
        delete: DeleteOperation<{}>;
        deleteById: DeleteOperation<string>;
        asyncOperations: StockTransferShipmentLineInTransit$AsyncOperations;
        lookups(
            dataOrId: string | { data: StockTransferShipmentLineInTransitInput },
        ): StockTransferShipmentLineInTransit$Lookups;
        getDefaults: GetDefaultsOperation<StockTransferShipmentLineInTransit>;
    }
    export interface SupplyPlanning extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        purchaseRequisitionLine: PurchaseRequisitionLine;
        mrpResultLine: MrpResultLine;
        type: SupplyPlanningType;
        purchaseSite: Site;
        itemSite: ItemSite;
        supplier: Supplier;
        itemSupplier: ItemSupplier;
        purchaseUnit: UnitOfMeasure;
        purchaseQuantity: string;
        stockUnit: UnitOfMeasure;
        stockQuantity: string;
        startDate: string;
        requirementDate: string;
        grossPrice: string;
        status: SupplyPlanningStatus;
        suggestionSource: SupplyPlanningSource;
        suggestionDate: string;
        suggestionUser: User;
        workOrderCategory: WorkOrderCategory;
        workOrderType: WorkOrderType;
        workOrderNumber: string;
        workOrderName: string;
    }
    export interface SupplyPlanningInput extends ClientNodeInput {
        purchaseRequisitionLine?: integer | string;
        mrpResultLine?: integer | string;
        type?: SupplyPlanningType;
        purchaseSite?: integer | string;
        itemSite?: integer | string;
        supplier?: integer | string;
        itemSupplier?: integer | string;
        purchaseUnit?: integer | string;
        purchaseQuantity?: decimal | string;
        stockQuantity?: decimal | string;
        startDate?: string;
        requirementDate?: string;
        grossPrice?: decimal | string;
        status?: SupplyPlanningStatus;
        suggestionSource?: SupplyPlanningSource;
        suggestionDate?: string;
        suggestionUser?: integer | string;
        workOrderCategory?: integer | string;
        workOrderType?: WorkOrderType;
        workOrderNumber?: string;
        workOrderName?: string;
    }
    export interface SupplyPlanningBinding extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        purchaseRequisitionLine: PurchaseRequisitionLine;
        mrpResultLine: MrpResultLine;
        type: SupplyPlanningType;
        purchaseSite: Site;
        itemSite: ItemSite;
        supplier: Supplier;
        itemSupplier: ItemSupplier;
        purchaseUnit: UnitOfMeasure;
        purchaseQuantity: string;
        stockUnit: UnitOfMeasure;
        stockQuantity: string;
        startDate: string;
        requirementDate: string;
        grossPrice: string;
        status: SupplyPlanningStatus;
        suggestionSource: SupplyPlanningSource;
        suggestionDate: string;
        suggestionUser: User;
        workOrderCategory: WorkOrderCategory;
        workOrderType: WorkOrderType;
        workOrderNumber: string;
        workOrderName: string;
    }
    export interface SupplyPlanning$Queries {
        checkSupplyPlanningPurchaseOrderCreated: Node$Operation<{}, boolean>;
    }
    export interface SupplyPlanning$AsyncOperations {
        createOrder: AsyncOperation<
            {
                filter?: string;
            },
            boolean
        >;
        createWorkOrder: AsyncOperation<
            {
                filter?: string;
            },
            boolean
        >;
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface SupplyPlanning$Lookups {
        purchaseRequisitionLine: QueryOperation<PurchaseRequisitionLine>;
        mrpResultLine: QueryOperation<MrpResultLine>;
        purchaseSite: QueryOperation<Site>;
        itemSite: QueryOperation<ItemSite>;
        supplier: QueryOperation<Supplier>;
        itemSupplier: QueryOperation<ItemSupplier>;
        purchaseUnit: QueryOperation<UnitOfMeasure>;
        stockUnit: QueryOperation<UnitOfMeasure>;
        suggestionUser: QueryOperation<User>;
        workOrderCategory: QueryOperation<WorkOrderCategory>;
    }
    export interface SupplyPlanning$Operations {
        query: QueryOperation<SupplyPlanning>;
        read: ReadOperation<SupplyPlanning>;
        aggregate: {
            read: AggregateReadOperation<SupplyPlanning>;
            query: AggregateQueryOperation<SupplyPlanning>;
        };
        queries: SupplyPlanning$Queries;
        update: UpdateOperation<SupplyPlanningInput, SupplyPlanning>;
        updateById: UpdateByIdOperation<SupplyPlanningInput, SupplyPlanning>;
        asyncOperations: SupplyPlanning$AsyncOperations;
        lookups(dataOrId: string | { data: SupplyPlanningInput }): SupplyPlanning$Lookups;
        getDefaults: GetDefaultsOperation<SupplyPlanning>;
    }
    export interface StockTransferInTransitInquiry extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        user: User;
        company: Company;
        site: Site;
        itemCategory: ItemCategory;
        commodityCode: string;
        fromItem: Item;
        toItem: Item;
        date: string;
        lines: ClientCollection<StockTransferShipmentLine>;
        postingClass: PostingClass;
    }
    export interface StockTransferInTransitInquiryInput extends ClientNodeInput {
        user?: integer | string;
        company?: integer | string;
        site?: integer | string;
        itemCategory?: integer | string;
        commodityCode?: string;
        fromItem?: integer | string;
        toItem?: integer | string;
        date?: string;
        postingClass?: integer | string;
    }
    export interface StockTransferInTransitInquiryBinding extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        user: User;
        company: Company;
        site: Site;
        itemCategory: ItemCategory;
        commodityCode: string;
        fromItem: Item;
        toItem: Item;
        date: string;
        lines: ClientCollection<StockTransferShipmentLine>;
        postingClass: PostingClass;
    }
    export interface StockTransferInTransitInquiry$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface StockTransferInTransitInquiry$Lookups {
        user: QueryOperation<User>;
        company: QueryOperation<Company>;
        site: QueryOperation<Site>;
        itemCategory: QueryOperation<ItemCategory>;
        fromItem: QueryOperation<Item>;
        toItem: QueryOperation<Item>;
    }
    export interface StockTransferInTransitInquiry$Operations {
        query: QueryOperation<StockTransferInTransitInquiry>;
        read: ReadOperation<StockTransferInTransitInquiry>;
        aggregate: {
            read: AggregateReadOperation<StockTransferInTransitInquiry>;
            query: AggregateQueryOperation<StockTransferInTransitInquiry>;
        };
        create: CreateOperation<StockTransferInTransitInquiryInput, StockTransferInTransitInquiry>;
        getDuplicate: GetDuplicateOperation<StockTransferInTransitInquiry>;
        duplicate: DuplicateOperation<string, StockTransferInTransitInquiryInput, StockTransferInTransitInquiry>;
        update: UpdateOperation<StockTransferInTransitInquiryInput, StockTransferInTransitInquiry>;
        updateById: UpdateByIdOperation<StockTransferInTransitInquiryInput, StockTransferInTransitInquiry>;
        delete: DeleteOperation<{}>;
        deleteById: DeleteOperation<string>;
        asyncOperations: StockTransferInTransitInquiry$AsyncOperations;
        lookups(dataOrId: string | { data: StockTransferInTransitInquiryInput }): StockTransferInTransitInquiry$Lookups;
        getDefaults: GetDefaultsOperation<StockTransferInTransitInquiry>;
    }
    export interface StockTransferOrder extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        number: string;
        status: BaseStatus;
        approvalStatus: ApprovalStatus;
        date: string;
        canPrint: boolean;
        isPrinted: boolean;
        isSent: boolean;
        site: Site;
        stockSite: Site;
        companyCurrency: Currency;
        financialSite: Site;
        text: TextStream;
        internalNote: TextStream;
        externalNote: TextStream;
        isExternalNote: boolean;
        isTransferHeaderNote: boolean;
        isTransferLineNote: boolean;
        siteAddress: Address;
        businessEntityAddress: BusinessEntityAddress;
        invoiceStatus: InvoiceStatus;
        fxRateDate: string;
        taxCalculationStatus: TaxCalculationStatus;
        taxEngine: TaxEngine;
        paymentTracking: PaymentTracking;
        netBalance: string;
        customer: Customer;
        customerNumber: string;
        doNotShipBeforeDate: string;
        doNotShipAfterDate: string;
        requestedDeliveryDate: string;
        shippingStatus: ShippingStatus;
        receivingStatus: ReceivingStatus;
        supplier: Supplier;
        receivingSite: Site;
        stockTransferOrderDocumentType: StockTransferOrderDocumentType;
        stockTransferOrderFlowType: StockTransferOrderFlowType;
        requester: User;
        isCloseHidden: boolean;
        financeIntegrationStatus: FinanceIntegrationStatus;
        _attachments: ClientCollection<AttachmentAssociation>;
        displayStatus: BaseDisplayStatus;
        businessRelation: Customer;
        paymentTerm: PaymentTerm;
        shipToCustomer: Customer;
        shipToCustomerAddress: BusinessEntityAddress;
        shipToAddress: Address;
        shipToContact: Contact;
        deliveryMode: DeliveryMode;
        deliveryLeadTime: integer;
        workDays: integer;
        shipToLinkedAddress: BusinessEntityAddress;
        currency: Currency;
        transactionCurrency: Currency;
        companyFxRate: string;
        companyFxRateDivisor: string;
        rateDescription: string;
        totalAmountExcludingTax: string;
        totalAmountExcludingTaxInCompanyCurrency: string;
        taxes: ClientCollection<DocumentTax>;
        totalTaxAmount: string;
        totalTaxableAmount: string;
        totalExemptAmount: string;
        totalTaxAmountAdjusted: string;
        incoterm: Incoterm;
        shippingDate: string;
        expectedDeliveryDate: string;
        lines: ClientCollection<StockTransferOrderLine>;
        totalAmountIncludingTax: string;
        allocationStatus: StockAllocationStatus;
        allocationRequestStatus: AllocationRequestStatus;
        jsonAggregateLandedCostTypes: string;
    }
    export interface StockTransferOrderInput extends ClientNodeInput {
        number?: string;
        status?: BaseStatus;
        approvalStatus?: ApprovalStatus;
        date?: string;
        isPrinted?: boolean | string;
        isSent?: boolean | string;
        site?: integer | string;
        stockSite?: integer | string;
        financialSite?: integer | string;
        text?: TextStream;
        internalNote?: TextStream;
        externalNote?: TextStream;
        isExternalNote?: boolean | string;
        isOverwriteNote?: boolean | string;
        isTransferHeaderNote?: boolean | string;
        isTransferLineNote?: boolean | string;
        siteAddress?: integer | string;
        businessEntityAddress?: integer | string;
        invoiceStatus?: InvoiceStatus;
        fxRateDate?: string;
        taxCalculationStatus?: TaxCalculationStatus;
        paymentTracking?: PaymentTrackingInput;
        customer?: integer | string;
        customerNumber?: string;
        doNotShipBeforeDate?: string;
        doNotShipAfterDate?: string;
        requestedDeliveryDate?: string;
        shippingStatus?: ShippingStatus;
        receivingStatus?: ReceivingStatus;
        supplier?: integer | string;
        receivingSite?: integer | string;
        stockTransferOrderDocumentType?: StockTransferOrderDocumentType;
        stockTransferOrderFlowType?: StockTransferOrderFlowType;
        requester?: integer | string;
        _attachments?: Partial<AttachmentAssociationInput>[];
        displayStatus?: BaseDisplayStatus;
        businessRelation?: integer | string;
        paymentTerm?: integer | string;
        shipToCustomer?: integer | string;
        shipToCustomerAddress?: integer | string;
        shipToAddress?: integer | string;
        shipToContact?: integer | string;
        deliveryMode?: integer | string;
        deliveryLeadTime?: integer | string;
        shipToLinkedAddress?: integer | string;
        currency?: integer | string;
        companyFxRateDivisor?: decimal | string;
        totalAmountExcludingTax?: decimal | string;
        totalAmountExcludingTaxInCompanyCurrency?: decimal | string;
        taxes?: Partial<DocumentTaxInput>[];
        totalTaxAmount?: decimal | string;
        totalTaxableAmount?: decimal | string;
        totalExemptAmount?: decimal | string;
        totalTaxAmountAdjusted?: decimal | string;
        incoterm?: integer | string;
        shippingDate?: string;
        expectedDeliveryDate?: string;
        lines?: Partial<StockTransferOrderLineInput>[];
    }
    export interface StockTransferOrderBinding extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        number: string;
        status: BaseStatus;
        approvalStatus: ApprovalStatus;
        date: string;
        canPrint: boolean;
        isPrinted: boolean;
        isSent: boolean;
        site: Site;
        stockSite: Site;
        companyCurrency: Currency;
        financialSite: Site;
        text: TextStream;
        internalNote: TextStream;
        externalNote: TextStream;
        isExternalNote: boolean;
        isOverwriteNote: boolean;
        isTransferHeaderNote: boolean;
        isTransferLineNote: boolean;
        siteAddress: Address;
        businessEntityAddress: BusinessEntityAddress;
        invoiceStatus: InvoiceStatus;
        fxRateDate: string;
        taxCalculationStatus: TaxCalculationStatus;
        taxEngine: TaxEngine;
        paymentTracking: PaymentTrackingBinding;
        netBalance: string;
        customer: Customer;
        customerNumber: string;
        doNotShipBeforeDate: string;
        doNotShipAfterDate: string;
        requestedDeliveryDate: string;
        shippingStatus: ShippingStatus;
        receivingStatus: ReceivingStatus;
        supplier: Supplier;
        receivingSite: Site;
        stockTransferOrderDocumentType: StockTransferOrderDocumentType;
        stockTransferOrderFlowType: StockTransferOrderFlowType;
        requester: User;
        isCloseHidden: boolean;
        financeIntegrationStatus: FinanceIntegrationStatus;
        _attachments: ClientCollection<AttachmentAssociation>;
        displayStatus: BaseDisplayStatus;
        businessRelation: Customer;
        paymentTerm: PaymentTerm;
        shipToCustomer: Customer;
        shipToCustomerAddress: BusinessEntityAddress;
        shipToAddress: Address;
        shipToContact: Contact;
        deliveryMode: DeliveryMode;
        deliveryLeadTime: integer;
        workDays: integer;
        shipToLinkedAddress: BusinessEntityAddress;
        currency: Currency;
        transactionCurrency: Currency;
        companyFxRate: string;
        companyFxRateDivisor: string;
        rateDescription: string;
        totalAmountExcludingTax: string;
        totalAmountExcludingTaxInCompanyCurrency: string;
        taxes: ClientCollection<DocumentTaxBinding>;
        totalTaxAmount: string;
        totalTaxableAmount: string;
        totalExemptAmount: string;
        totalTaxAmountAdjusted: string;
        incoterm: Incoterm;
        shippingDate: string;
        expectedDeliveryDate: string;
        lines: ClientCollection<StockTransferOrderLineBinding>;
        totalAmountIncludingTax: string;
        allocationStatus: StockAllocationStatus;
        allocationRequestStatus: AllocationRequestStatus;
        jsonAggregateLandedCostTypes: any;
    }
    export interface StockTransferOrder$Queries {
        getFilteredUsers: Node$Operation<
            {
                criteria: {
                    userType: string;
                    isApiUser?: boolean | string;
                    isActive?: boolean | string;
                    isOperatorUser?: boolean | string;
                };
            },
            {
                _id: string;
                email: string;
                firstName: string;
                lastName: string;
            }[]
        >;
    }
    export interface StockTransferOrder$Mutations {
        closeLine: Node$Operation<
            {
                stockTransferOrderLine: string;
            },
            DocumentCloseStatusMethodReturn
        >;
        openLine: Node$Operation<
            {
                stockTransferOrderLine: string;
            },
            DocumentOpenStatusMethodReturn
        >;
        close: Node$Operation<
            {
                stockTransferOrder: string;
            },
            DocumentCloseStatusMethodReturn
        >;
        open: Node$Operation<
            {
                stockTransferOrder: string;
            },
            DocumentOpenStatusMethodReturn
        >;
        confirm: Node$Operation<
            {
                stockTransferOrder: string;
            },
            DocumentConfirmStatusMethodReturn
        >;
        subWorkDays: Node$Operation<
            {
                requestedDeliveryDate: string;
                orderDate: string;
                doNotShipBeforeDate?: string | null;
                doNotShipAfterDate?: string | null;
                deliveryLeadTime: integer | string;
                workDaysMask: integer | string;
            },
            string
        >;
        addWorkDays: Node$Operation<
            {
                shippingDate: string;
                deliveryLeadTime: integer | string;
                workDays: integer | string;
            },
            string
        >;
        setIsPrintedTrue: Node$Operation<
            {
                order: string;
                isPrinted?: boolean | string;
            },
            boolean
        >;
        requestAutoAllocation: Node$Operation<
            {
                data?: {
                    document?: integer | string;
                    requestType?: string;
                };
            },
            string
        >;
        financeIntegrationCheck: Node$Operation<
            {
                stockTransferOrder: string;
            },
            string[]
        >;
        approve: Node$Operation<
            {
                stockTransferOrder: string;
            },
            string
        >;
        reject: Node$Operation<
            {
                stockTransferOrder: string;
            },
            string
        >;
        createStockTransferShipment: Node$Operation<
            {
                stockTransferOrder: string;
            },
            StockTransferShipment
        >;
        sendApprovalRequestMail: Node$Operation<
            {
                document?: string;
                user?: UserInput | null;
            },
            boolean
        >;
    }
    export interface StockTransferOrder$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
        bulkResync: AsyncOperation<
            {
                filter?: string;
            },
            boolean
        >;
    }
    export interface StockTransferOrder$Lookups {
        site: QueryOperation<Site>;
        stockSite: QueryOperation<Site>;
        companyCurrency: QueryOperation<Currency>;
        financialSite: QueryOperation<Site>;
        siteAddress: QueryOperation<Address>;
        businessEntityAddress: QueryOperation<BusinessEntityAddress>;
        customer: QueryOperation<Customer>;
        supplier: QueryOperation<Supplier>;
        receivingSite: QueryOperation<Site>;
        requester: QueryOperation<User>;
        businessRelation: QueryOperation<BaseBusinessRelation>;
        paymentTerm: QueryOperation<PaymentTerm>;
        shipToCustomer: QueryOperation<Customer>;
        shipToCustomerAddress: QueryOperation<BusinessEntityAddress>;
        shipToAddress: QueryOperation<Address>;
        shipToContact: QueryOperation<Contact>;
        deliveryMode: QueryOperation<DeliveryMode>;
        shipToLinkedAddress: QueryOperation<BusinessEntityAddress>;
        currency: QueryOperation<Currency>;
        transactionCurrency: QueryOperation<Currency>;
        incoterm: QueryOperation<Incoterm>;
    }
    export interface StockTransferOrder$Operations {
        query: QueryOperation<StockTransferOrder>;
        read: ReadOperation<StockTransferOrder>;
        aggregate: {
            read: AggregateReadOperation<StockTransferOrder>;
            query: AggregateQueryOperation<StockTransferOrder>;
        };
        queries: StockTransferOrder$Queries;
        create: CreateOperation<StockTransferOrderInput, StockTransferOrder>;
        getDuplicate: GetDuplicateOperation<StockTransferOrder>;
        duplicate: DuplicateOperation<string, StockTransferOrderInput, StockTransferOrder>;
        update: UpdateOperation<StockTransferOrderInput, StockTransferOrder>;
        updateById: UpdateByIdOperation<StockTransferOrderInput, StockTransferOrder>;
        delete: DeleteOperation<{}>;
        deleteById: DeleteOperation<string>;
        mutations: StockTransferOrder$Mutations;
        asyncOperations: StockTransferOrder$AsyncOperations;
        lookups(dataOrId: string | { data: StockTransferOrderInput }): StockTransferOrder$Lookups;
        getDefaults: GetDefaultsOperation<StockTransferOrder>;
    }
    export interface StockTransferOrderLine extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        documentNumber: string;
        documentId: integer;
        document: StockTransferOrder;
        origin: BaseOrigin;
        status: BaseStatus;
        site: Site;
        siteLinkedAddress: BusinessEntityAddress;
        stockSite: Site;
        stockSiteLinkedAddress: BusinessEntityAddress;
        internalNote: TextStream;
        externalNote: TextStream;
        isExternalNote: boolean;
        currency: Currency;
        grossPrice: string;
        discountCharges: ClientCollection<DocumentLineDiscountCharge>;
        priceOrigin: BasePriceOrigin;
        netPrice: string;
        taxableAmount: string;
        taxAmount: string;
        exemptAmount: string;
        taxDate: string;
        canHaveLandedCost: boolean;
        canHaveLandedCostLine: boolean;
        landedCostLines: ClientCollection<LandedCostLine>;
        taxes: ClientCollection<DocumentLineTax>;
        taxCalculationStatus: TaxCalculationStatus;
        taxAmountAdjusted: string;
        stockSiteAddress: Address;
        shipToCustomerAddress: BusinessEntityAddress;
        shipToAddress: Address;
        shipToContact: Contact;
        deliveryLeadTime: integer;
        doNotShipBeforeDate: string;
        doNotShipAfterDate: string;
        allocationRequestStatus: AllocationRequestStatus;
        deliveryMode: DeliveryMode;
        stockAllocations: ClientCollection<StockAllocation>;
        shippingStatus: ShippingStatus;
        requestedDeliveryDate: string;
        quantityAllocatedInStockUnit: string;
        remainingQuantityToShipInStockUnit: string;
        receivingStatus: ReceivingStatus;
        receivingSite: Site;
        quantityToShipInProgressInStockUnit: string;
        shippedQuantityInStockUnit: string;
        totalQuantityInStockUnitInAllLinkedShipments: string;
        workInProgress: WorkInProgressStockTransferOrderLine;
        actualLandedCostInCompanyCurrency: string;
        stockTransferReceiptLine: StockTransferReceiptLine;
        dimensionDefinitionLevel: DimensionDefinitionLevel;
        computedAttributes: string;
        item: Item;
        itemDescription: string;
        itemSite: ItemSite;
        stockUnit: UnitOfMeasure;
        unit: UnitOfMeasure;
        quantity: string;
        signedQuantity: string;
        amountExcludingTax: string;
        text: TextStream;
        signedAmountExcludingTax: string;
        amountExcludingTaxInCompanyCurrency: string;
        signedAmountExcludingTaxInCompanyCurrency: string;
        landedCost: LandedCostDocumentLine;
        amountIncludingTax: string;
        amountIncludingTaxInCompanyCurrency: string;
        itemCustomer: ItemCustomer;
        stockOnHand: string;
        shippingDate: string;
        availableQuantityInStockUnit: string;
        remainingQuantityToAllocateInStockUnit: string;
        storedDimensions: string;
        storedAttributes: string;
        analyticalData: AnalyticalData;
        quantityInStockUnit: string;
        unitToStockUnitConversionFactor: string;
        expectedDeliveryDate: string;
        stockCostAmountInCompanyCurrency: string;
        stockCostAmount: string;
        stockCostUnit: string;
        allocationStatus: StockAllocationStatus;
        stockShortageInStockUnit: string;
        stockShortageStatus: boolean;
    }
    export interface StockTransferOrderLineInput extends VitalClientNodeInput {
        origin?: BaseOrigin;
        status?: BaseStatus;
        site?: integer | string;
        siteLinkedAddress?: integer | string;
        stockSite?: integer | string;
        stockSiteLinkedAddress?: integer | string;
        internalNote?: TextStream;
        externalNote?: TextStream;
        isExternalNote?: boolean | string;
        grossPrice?: decimal | string;
        discountCharges?: Partial<DocumentLineDiscountChargeInput>[];
        priceOrigin?: BasePriceOrigin;
        netPrice?: decimal | string;
        taxableAmount?: decimal | string;
        taxAmount?: decimal | string;
        exemptAmount?: decimal | string;
        taxDate?: string;
        landedCostLines?: Partial<LandedCostLineInput>[];
        taxes?: Partial<DocumentLineTaxInput>[];
        taxAmountAdjusted?: decimal | string;
        stockSiteAddress?: integer | string;
        shipToCustomerAddress?: integer | string;
        shipToAddress?: integer | string;
        shipToContact?: integer | string;
        deliveryLeadTime?: integer | string;
        doNotShipBeforeDate?: string;
        doNotShipAfterDate?: string;
        allocationRequestStatus?: AllocationRequestStatus;
        deliveryMode?: integer | string;
        shippingStatus?: ShippingStatus;
        requestedDeliveryDate?: string;
        receivingStatus?: ReceivingStatus;
        receivingSite?: integer | string;
        workInProgress?: WorkInProgressStockTransferOrderLineInput;
        item?: integer | string;
        itemDescription?: string;
        stockUnit?: integer | string;
        unit?: integer | string;
        quantity?: decimal | string;
        amountExcludingTax?: decimal | string;
        text?: TextStream;
        amountExcludingTaxInCompanyCurrency?: decimal | string;
        landedCost?: LandedCostDocumentLineInput;
        amountIncludingTax?: decimal | string;
        amountIncludingTaxInCompanyCurrency?: decimal | string;
        shippingDate?: string;
        storedDimensions?: string;
        storedAttributes?: string;
        analyticalData?: integer | string;
        quantityInStockUnit?: decimal | string;
        unitToStockUnitConversionFactor?: decimal | string;
        expectedDeliveryDate?: string;
    }
    export interface StockTransferOrderLineBinding extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        documentNumber: string;
        documentId: integer;
        document: StockTransferOrder;
        origin: BaseOrigin;
        status: BaseStatus;
        site: Site;
        siteLinkedAddress: BusinessEntityAddress;
        stockSite: Site;
        stockSiteLinkedAddress: BusinessEntityAddress;
        internalNote: TextStream;
        externalNote: TextStream;
        isExternalNote: boolean;
        currency: Currency;
        grossPrice: string;
        discountCharges: ClientCollection<DocumentLineDiscountChargeBinding>;
        priceOrigin: BasePriceOrigin;
        netPrice: string;
        taxableAmount: string;
        taxAmount: string;
        exemptAmount: string;
        taxDate: string;
        canHaveLandedCost: boolean;
        canHaveLandedCostLine: boolean;
        landedCostLines: ClientCollection<LandedCostLineBinding>;
        taxes: ClientCollection<DocumentLineTaxBinding>;
        taxCalculationStatus: TaxCalculationStatus;
        taxAmountAdjusted: string;
        stockSiteAddress: Address;
        shipToCustomerAddress: BusinessEntityAddress;
        shipToAddress: Address;
        shipToContact: Contact;
        deliveryLeadTime: integer;
        doNotShipBeforeDate: string;
        doNotShipAfterDate: string;
        allocationRequestStatus: AllocationRequestStatus;
        deliveryMode: DeliveryMode;
        stockAllocations: ClientCollection<StockAllocation>;
        shippingStatus: ShippingStatus;
        requestedDeliveryDate: string;
        quantityAllocatedInStockUnit: string;
        remainingQuantityToShipInStockUnit: string;
        receivingStatus: ReceivingStatus;
        receivingSite: Site;
        quantityToShipInProgressInStockUnit: string;
        shippedQuantityInStockUnit: string;
        totalQuantityInStockUnitInAllLinkedShipments: string;
        workInProgress: WorkInProgressStockTransferOrderLineBinding;
        actualLandedCostInCompanyCurrency: string;
        stockTransferReceiptLine: StockTransferReceiptLine;
        dimensionDefinitionLevel: DimensionDefinitionLevel;
        computedAttributes: any;
        item: Item;
        itemDescription: string;
        itemSite: ItemSite;
        stockUnit: UnitOfMeasure;
        unit: UnitOfMeasure;
        quantity: string;
        signedQuantity: string;
        amountExcludingTax: string;
        text: TextStream;
        signedAmountExcludingTax: string;
        amountExcludingTaxInCompanyCurrency: string;
        signedAmountExcludingTaxInCompanyCurrency: string;
        landedCost: LandedCostDocumentLineBinding;
        amountIncludingTax: string;
        amountIncludingTaxInCompanyCurrency: string;
        itemCustomer: ItemCustomer;
        stockOnHand: string;
        shippingDate: string;
        availableQuantityInStockUnit: string;
        remainingQuantityToAllocateInStockUnit: string;
        storedDimensions: any;
        storedAttributes: any;
        analyticalData: AnalyticalData;
        quantityInStockUnit: string;
        unitToStockUnitConversionFactor: string;
        expectedDeliveryDate: string;
        stockCostAmountInCompanyCurrency: string;
        stockCostAmount: string;
        stockCostUnit: string;
        allocationStatus: StockAllocationStatus;
        stockShortageInStockUnit: string;
        stockShortageStatus: boolean;
    }
    export interface StockTransferOrderLine$Mutations {
        setDimension: Node$Operation<
            {
                baseDocumentItemLine: string;
                storedDimensions?: string;
                storedAttributes?: string;
            },
            boolean
        >;
    }
    export interface StockTransferOrderLine$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface StockTransferOrderLine$Lookups {
        site: QueryOperation<Site>;
        siteLinkedAddress: QueryOperation<BusinessEntityAddress>;
        stockSite: QueryOperation<Site>;
        stockSiteLinkedAddress: QueryOperation<BusinessEntityAddress>;
        currency: QueryOperation<Currency>;
        stockSiteAddress: QueryOperation<Address>;
        shipToCustomerAddress: QueryOperation<BusinessEntityAddress>;
        shipToAddress: QueryOperation<Address>;
        shipToContact: QueryOperation<Contact>;
        deliveryMode: QueryOperation<DeliveryMode>;
        receivingSite: QueryOperation<Site>;
        stockTransferReceiptLine: QueryOperation<StockTransferReceiptLine>;
        item: QueryOperation<Item>;
        itemSite: QueryOperation<ItemSite>;
        stockUnit: QueryOperation<UnitOfMeasure>;
        unit: QueryOperation<UnitOfMeasure>;
        itemCustomer: QueryOperation<ItemCustomer>;
    }
    export interface StockTransferOrderLine$Operations {
        query: QueryOperation<StockTransferOrderLine>;
        read: ReadOperation<StockTransferOrderLine>;
        aggregate: {
            read: AggregateReadOperation<StockTransferOrderLine>;
            query: AggregateQueryOperation<StockTransferOrderLine>;
        };
        mutations: StockTransferOrderLine$Mutations;
        asyncOperations: StockTransferOrderLine$AsyncOperations;
        lookups(dataOrId: string | { data: StockTransferOrderLineInput }): StockTransferOrderLine$Lookups;
        getDefaults: GetDefaultsOperation<StockTransferOrderLine>;
    }
    export interface StockTransferReceipt extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        number: string;
        status: BaseStatus;
        approvalStatus: ApprovalStatus;
        date: string;
        canPrint: boolean;
        isPrinted: boolean;
        isSent: boolean;
        site: Site;
        stockSite: Site;
        companyCurrency: Currency;
        financialSite: Site;
        text: TextStream;
        externalNote: TextStream;
        isExternalNote: boolean;
        isTransferHeaderNote: boolean;
        isTransferLineNote: boolean;
        siteAddress: Address;
        businessEntityAddress: BusinessEntityAddress;
        invoiceStatus: InvoiceStatus;
        fxRateDate: string;
        taxCalculationStatus: TaxCalculationStatus;
        taxEngine: TaxEngine;
        paymentTracking: PaymentTracking;
        netBalance: string;
        supplier: Supplier;
        shippingSite: Site;
        postingDetails: ClientCollection<FinanceTransaction>;
        financeIntegrationStatus: FinanceIntegrationStatus;
        _attachments: ClientCollection<AttachmentAssociation>;
        businessRelation: Supplier;
        paymentTerm: PaymentTerm;
        billBySupplier: Supplier;
        supplierAddress: Address;
        currency: Currency;
        transactionCurrency: Currency;
        internalNote: TextStream;
        companyFxRate: string;
        companyFxRateDivisor: string;
        rateDescription: string;
        totalAmountExcludingTax: string;
        totalAmountExcludingTaxInCompanyCurrency: string;
        taxes: ClientCollection<DocumentTax>;
        totalTaxAmount: string;
        totalTaxableAmount: string;
        totalExemptAmount: string;
        totalTaxAmountAdjusted: string;
        lines: ClientCollection<StockTransferReceiptLine>;
        totalAmountIncludingTax: string;
        stockTransactionStatus: StockDocumentTransactionStatus;
        jsonAggregateLandedCostTypes: string;
        displayStatus: BaseDisplayStatus;
    }
    export interface StockTransferReceiptInput extends ClientNodeInput {
        number?: string;
        status?: BaseStatus;
        approvalStatus?: ApprovalStatus;
        date?: string;
        isPrinted?: boolean | string;
        isSent?: boolean | string;
        site?: integer | string;
        stockSite?: integer | string;
        financialSite?: integer | string;
        text?: TextStream;
        externalNote?: TextStream;
        isExternalNote?: boolean | string;
        isOverwriteNote?: boolean | string;
        isTransferHeaderNote?: boolean | string;
        isTransferLineNote?: boolean | string;
        siteAddress?: integer | string;
        businessEntityAddress?: integer | string;
        invoiceStatus?: InvoiceStatus;
        fxRateDate?: string;
        taxCalculationStatus?: TaxCalculationStatus;
        paymentTracking?: PaymentTrackingInput;
        supplier?: integer | string;
        shippingSite?: integer | string;
        _attachments?: Partial<AttachmentAssociationInput>[];
        businessRelation?: integer | string;
        paymentTerm?: integer | string;
        billBySupplier?: integer | string;
        supplierAddress?: integer | string;
        currency?: integer | string;
        internalNote?: TextStream;
        companyFxRateDivisor?: decimal | string;
        totalAmountExcludingTax?: decimal | string;
        totalAmountExcludingTaxInCompanyCurrency?: decimal | string;
        taxes?: Partial<DocumentTaxInput>[];
        totalTaxAmount?: decimal | string;
        totalTaxableAmount?: decimal | string;
        totalExemptAmount?: decimal | string;
        totalTaxAmountAdjusted?: decimal | string;
        lines?: Partial<StockTransferReceiptLineInput>[];
        displayStatus?: BaseDisplayStatus;
    }
    export interface StockTransferReceiptBinding extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        number: string;
        status: BaseStatus;
        approvalStatus: ApprovalStatus;
        date: string;
        canPrint: boolean;
        isPrinted: boolean;
        isSent: boolean;
        site: Site;
        stockSite: Site;
        companyCurrency: Currency;
        financialSite: Site;
        text: TextStream;
        externalNote: TextStream;
        isExternalNote: boolean;
        isOverwriteNote: boolean;
        isTransferHeaderNote: boolean;
        isTransferLineNote: boolean;
        siteAddress: Address;
        businessEntityAddress: BusinessEntityAddress;
        invoiceStatus: InvoiceStatus;
        fxRateDate: string;
        taxCalculationStatus: TaxCalculationStatus;
        taxEngine: TaxEngine;
        paymentTracking: PaymentTrackingBinding;
        netBalance: string;
        supplier: Supplier;
        shippingSite: Site;
        postingDetails: ClientCollection<FinanceTransaction>;
        financeIntegrationStatus: FinanceIntegrationStatus;
        _attachments: ClientCollection<AttachmentAssociation>;
        businessRelation: Supplier;
        paymentTerm: PaymentTerm;
        billBySupplier: Supplier;
        supplierAddress: Address;
        currency: Currency;
        transactionCurrency: Currency;
        internalNote: TextStream;
        companyFxRate: string;
        companyFxRateDivisor: string;
        rateDescription: string;
        totalAmountExcludingTax: string;
        totalAmountExcludingTaxInCompanyCurrency: string;
        taxes: ClientCollection<DocumentTaxBinding>;
        totalTaxAmount: string;
        totalTaxableAmount: string;
        totalExemptAmount: string;
        totalTaxAmountAdjusted: string;
        lines: ClientCollection<StockTransferReceiptLineBinding>;
        totalAmountIncludingTax: string;
        stockTransactionStatus: StockDocumentTransactionStatus;
        jsonAggregateLandedCostTypes: any;
        displayStatus: BaseDisplayStatus;
    }
    export interface StockTransferReceipt$Mutations {
        repost: Node$Operation<
            {
                stockTransferReceipt: string;
                documentLines?: {
                    baseDocumentLineSysId?: integer | string;
                    storedAttributes?: string;
                    storedDimensions?: string;
                }[];
            },
            {
                wasSuccessful: boolean;
                message: string;
            }
        >;
        resendNotificationForFinance: Node$Operation<
            {
                stockTransferReceipt: string;
            },
            boolean
        >;
        post: Node$Operation<
            {
                receipt: string;
            },
            boolean
        >;
        sendApprovalRequestMail: Node$Operation<
            {
                document?: string;
                user?: UserInput | null;
            },
            boolean
        >;
    }
    export interface StockTransferReceipt$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
        bulkResync: AsyncOperation<
            {
                filter?: string;
            },
            boolean
        >;
    }
    export interface StockTransferReceipt$Lookups {
        site: QueryOperation<Site>;
        stockSite: QueryOperation<Site>;
        companyCurrency: QueryOperation<Currency>;
        financialSite: QueryOperation<Site>;
        siteAddress: QueryOperation<Address>;
        businessEntityAddress: QueryOperation<BusinessEntityAddress>;
        supplier: QueryOperation<Supplier>;
        shippingSite: QueryOperation<Site>;
        businessRelation: QueryOperation<BaseBusinessRelation>;
        paymentTerm: QueryOperation<PaymentTerm>;
        billBySupplier: QueryOperation<Supplier>;
        supplierAddress: QueryOperation<Address>;
        currency: QueryOperation<Currency>;
        transactionCurrency: QueryOperation<Currency>;
    }
    export interface StockTransferReceipt$Operations {
        query: QueryOperation<StockTransferReceipt>;
        read: ReadOperation<StockTransferReceipt>;
        aggregate: {
            read: AggregateReadOperation<StockTransferReceipt>;
            query: AggregateQueryOperation<StockTransferReceipt>;
        };
        create: CreateOperation<StockTransferReceiptInput, StockTransferReceipt>;
        getDuplicate: GetDuplicateOperation<StockTransferReceipt>;
        duplicate: DuplicateOperation<string, StockTransferReceiptInput, StockTransferReceipt>;
        update: UpdateOperation<StockTransferReceiptInput, StockTransferReceipt>;
        updateById: UpdateByIdOperation<StockTransferReceiptInput, StockTransferReceipt>;
        delete: DeleteOperation<{}>;
        deleteById: DeleteOperation<string>;
        mutations: StockTransferReceipt$Mutations;
        asyncOperations: StockTransferReceipt$AsyncOperations;
        lookups(dataOrId: string | { data: StockTransferReceiptInput }): StockTransferReceipt$Lookups;
        getDefaults: GetDefaultsOperation<StockTransferReceipt>;
    }
    export interface StockTransferReceiptLine extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        documentNumber: string;
        documentId: integer;
        document: StockTransferReceipt;
        origin: BaseOrigin;
        status: BaseStatus;
        site: Site;
        siteLinkedAddress: BusinessEntityAddress;
        item: Item;
        itemDescription: string;
        stockSite: Site;
        stockSiteLinkedAddress: BusinessEntityAddress;
        itemSite: ItemSite;
        stockUnit: UnitOfMeasure;
        unit: UnitOfMeasure;
        quantity: string;
        internalNote: TextStream;
        externalNote: TextStream;
        isExternalNote: boolean;
        currency: Currency;
        grossPrice: string;
        discountCharges: ClientCollection<DocumentLineDiscountCharge>;
        priceOrigin: BasePriceOrigin;
        netPrice: string;
        signedQuantity: string;
        amountExcludingTax: string;
        taxableAmount: string;
        taxAmount: string;
        exemptAmount: string;
        taxDate: string;
        text: TextStream;
        signedAmountExcludingTax: string;
        amountExcludingTaxInCompanyCurrency: string;
        signedAmountExcludingTaxInCompanyCurrency: string;
        canHaveLandedCost: boolean;
        landedCost: LandedCostDocumentLine;
        canHaveLandedCostLine: boolean;
        landedCostLines: ClientCollection<LandedCostLine>;
        taxes: ClientCollection<DocumentLineTax>;
        taxCalculationStatus: TaxCalculationStatus;
        taxAmountAdjusted: string;
        itemSupplier: ItemSupplier;
        stockTransactionStatus: StockDocumentTransactionStatus;
        supplier: Supplier;
        purchaseUnit: UnitOfMeasure;
        companyCurrency: Currency;
        company: Company;
        amountForLandedCostAllocation: string;
        stockTransactions: ClientCollection<StockTransaction>;
        stockDetails: ClientCollection<StockChangeDetail>;
        stockMovements: ClientCollection<StockJournal>;
        linkToStockTransferShipmentLines: ClientCollection<StockTransferReceiptLineToStockTransferShipmentLine>;
        shipmentLine: StockTransferShipmentLine;
        workInProgress: WorkInProgressStockTransferReceiptLine;
        actualLandedCostInCompanyCurrency: string;
        dimensionDefinitionLevel: DimensionDefinitionLevel;
        storedDimensions: string;
        storedAttributes: string;
        computedAttributes: string;
        analyticalData: AnalyticalData;
        quantityInStockUnit: string;
        unitToStockUnitConversionFactor: string;
        amountIncludingTax: string;
        amountIncludingTaxInCompanyCurrency: string;
        stockCostAmountInCompanyCurrency: string;
        stockCostAmount: string;
        stockCostUnit: string;
        jsonStockDetails: string;
        stockDetailStatus: StockDetailStatus;
    }
    export interface StockTransferReceiptLineInput extends VitalClientNodeInput {
        origin?: BaseOrigin;
        status?: BaseStatus;
        site?: integer | string;
        siteLinkedAddress?: integer | string;
        item?: integer | string;
        itemDescription?: string;
        stockSite?: integer | string;
        stockSiteLinkedAddress?: integer | string;
        stockUnit?: integer | string;
        unit?: integer | string;
        quantity?: decimal | string;
        internalNote?: TextStream;
        externalNote?: TextStream;
        isExternalNote?: boolean | string;
        grossPrice?: decimal | string;
        discountCharges?: Partial<DocumentLineDiscountChargeInput>[];
        priceOrigin?: BasePriceOrigin;
        netPrice?: decimal | string;
        amountExcludingTax?: decimal | string;
        taxableAmount?: decimal | string;
        taxAmount?: decimal | string;
        exemptAmount?: decimal | string;
        taxDate?: string;
        text?: TextStream;
        amountExcludingTaxInCompanyCurrency?: decimal | string;
        landedCost?: LandedCostDocumentLineInput;
        landedCostLines?: Partial<LandedCostLineInput>[];
        taxes?: Partial<DocumentLineTaxInput>[];
        taxAmountAdjusted?: decimal | string;
        stockTransactionStatus?: StockDocumentTransactionStatus;
        purchaseUnit?: integer | string;
        stockTransactions?: Partial<StockTransactionInput>[];
        stockDetails?: Partial<StockChangeDetailInput>[];
        linkToStockTransferShipmentLines?: Partial<StockTransferReceiptLineToStockTransferShipmentLineInput>[];
        workInProgress?: WorkInProgressStockTransferReceiptLineInput;
        storedDimensions?: string;
        storedAttributes?: string;
        analyticalData?: integer | string;
        quantityInStockUnit?: decimal | string;
        unitToStockUnitConversionFactor?: decimal | string;
        amountIncludingTax?: decimal | string;
        amountIncludingTaxInCompanyCurrency?: decimal | string;
        jsonStockDetails?: string;
    }
    export interface StockTransferReceiptLineBinding extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        documentNumber: string;
        documentId: integer;
        document: StockTransferReceipt;
        origin: BaseOrigin;
        status: BaseStatus;
        site: Site;
        siteLinkedAddress: BusinessEntityAddress;
        item: Item;
        itemDescription: string;
        stockSite: Site;
        stockSiteLinkedAddress: BusinessEntityAddress;
        itemSite: ItemSite;
        stockUnit: UnitOfMeasure;
        unit: UnitOfMeasure;
        quantity: string;
        internalNote: TextStream;
        externalNote: TextStream;
        isExternalNote: boolean;
        currency: Currency;
        grossPrice: string;
        discountCharges: ClientCollection<DocumentLineDiscountChargeBinding>;
        priceOrigin: BasePriceOrigin;
        netPrice: string;
        signedQuantity: string;
        amountExcludingTax: string;
        taxableAmount: string;
        taxAmount: string;
        exemptAmount: string;
        taxDate: string;
        text: TextStream;
        signedAmountExcludingTax: string;
        amountExcludingTaxInCompanyCurrency: string;
        signedAmountExcludingTaxInCompanyCurrency: string;
        canHaveLandedCost: boolean;
        landedCost: LandedCostDocumentLineBinding;
        canHaveLandedCostLine: boolean;
        landedCostLines: ClientCollection<LandedCostLineBinding>;
        taxes: ClientCollection<DocumentLineTaxBinding>;
        taxCalculationStatus: TaxCalculationStatus;
        taxAmountAdjusted: string;
        itemSupplier: ItemSupplier;
        stockTransactionStatus: StockDocumentTransactionStatus;
        supplier: Supplier;
        purchaseUnit: UnitOfMeasure;
        companyCurrency: Currency;
        company: Company;
        amountForLandedCostAllocation: string;
        stockTransactions: ClientCollection<StockTransactionBinding>;
        stockDetails: ClientCollection<StockChangeDetailBinding>;
        stockMovements: ClientCollection<StockJournal>;
        linkToStockTransferShipmentLines: ClientCollection<StockTransferReceiptLineToStockTransferShipmentLineBinding>;
        shipmentLine: StockTransferShipmentLine;
        workInProgress: WorkInProgressStockTransferReceiptLineBinding;
        actualLandedCostInCompanyCurrency: string;
        dimensionDefinitionLevel: DimensionDefinitionLevel;
        storedDimensions: any;
        storedAttributes: any;
        computedAttributes: any;
        analyticalData: AnalyticalData;
        quantityInStockUnit: string;
        unitToStockUnitConversionFactor: string;
        amountIncludingTax: string;
        amountIncludingTaxInCompanyCurrency: string;
        stockCostAmountInCompanyCurrency: string;
        stockCostAmount: string;
        stockCostUnit: string;
        jsonStockDetails: any;
        stockDetailStatus: StockDetailStatus;
    }
    export interface StockTransferReceiptLine$Mutations {
        setDimension: Node$Operation<
            {
                baseDocumentItemLine: string;
                storedDimensions?: string;
                storedAttributes?: string;
            },
            boolean
        >;
    }
    export interface StockTransferReceiptLine$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface StockTransferReceiptLine$Lookups {
        site: QueryOperation<Site>;
        siteLinkedAddress: QueryOperation<BusinessEntityAddress>;
        item: QueryOperation<Item>;
        stockSite: QueryOperation<Site>;
        stockSiteLinkedAddress: QueryOperation<BusinessEntityAddress>;
        itemSite: QueryOperation<ItemSite>;
        stockUnit: QueryOperation<UnitOfMeasure>;
        unit: QueryOperation<UnitOfMeasure>;
        currency: QueryOperation<Currency>;
        itemSupplier: QueryOperation<ItemSupplier>;
        supplier: QueryOperation<Supplier>;
        purchaseUnit: QueryOperation<UnitOfMeasure>;
        companyCurrency: QueryOperation<Currency>;
        company: QueryOperation<Company>;
        shipmentLine: QueryOperation<StockTransferShipmentLine>;
    }
    export interface StockTransferReceiptLine$Operations {
        query: QueryOperation<StockTransferReceiptLine>;
        read: ReadOperation<StockTransferReceiptLine>;
        aggregate: {
            read: AggregateReadOperation<StockTransferReceiptLine>;
            query: AggregateQueryOperation<StockTransferReceiptLine>;
        };
        mutations: StockTransferReceiptLine$Mutations;
        asyncOperations: StockTransferReceiptLine$AsyncOperations;
        lookups(dataOrId: string | { data: StockTransferReceiptLineInput }): StockTransferReceiptLine$Lookups;
        getDefaults: GetDefaultsOperation<StockTransferReceiptLine>;
    }
    export interface StockTransferReceiptLineToStockTransferShipmentLine extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        from: StockTransferReceiptLine;
        to: StockTransferShipmentLine;
    }
    export interface StockTransferReceiptLineToStockTransferShipmentLineInput extends VitalClientNodeInput {
        to?: integer | string;
    }
    export interface StockTransferReceiptLineToStockTransferShipmentLineBinding extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        from: StockTransferReceiptLine;
        to: StockTransferShipmentLine;
    }
    export interface StockTransferReceiptLineToStockTransferShipmentLine$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface StockTransferReceiptLineToStockTransferShipmentLine$Lookups {
        to: QueryOperation<BaseDocumentItemLine>;
    }
    export interface StockTransferReceiptLineToStockTransferShipmentLine$Operations {
        query: QueryOperation<StockTransferReceiptLineToStockTransferShipmentLine>;
        read: ReadOperation<StockTransferReceiptLineToStockTransferShipmentLine>;
        aggregate: {
            read: AggregateReadOperation<StockTransferReceiptLineToStockTransferShipmentLine>;
            query: AggregateQueryOperation<StockTransferReceiptLineToStockTransferShipmentLine>;
        };
        asyncOperations: StockTransferReceiptLineToStockTransferShipmentLine$AsyncOperations;
        lookups(
            dataOrId: string | { data: StockTransferReceiptLineToStockTransferShipmentLineInput },
        ): StockTransferReceiptLineToStockTransferShipmentLine$Lookups;
        getDefaults: GetDefaultsOperation<StockTransferReceiptLineToStockTransferShipmentLine>;
    }
    export interface StockTransferShipment extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        number: string;
        status: BaseStatus;
        approvalStatus: ApprovalStatus;
        date: string;
        canPrint: boolean;
        isPrinted: boolean;
        isSent: boolean;
        site: Site;
        stockSite: Site;
        companyCurrency: Currency;
        financialSite: Site;
        text: TextStream;
        internalNote: TextStream;
        externalNote: TextStream;
        isExternalNote: boolean;
        isTransferHeaderNote: boolean;
        isTransferLineNote: boolean;
        siteAddress: Address;
        businessEntityAddress: BusinessEntityAddress;
        invoiceStatus: InvoiceStatus;
        fxRateDate: string;
        taxCalculationStatus: TaxCalculationStatus;
        taxEngine: TaxEngine;
        paymentTracking: PaymentTracking;
        netBalance: string;
        customer: Customer;
        customerNumber: string;
        trackingNumber: string;
        receivingStatus: ReceivingStatus;
        supplier: Supplier;
        receivingSite: Site;
        postingDetails: ClientCollection<FinanceTransaction>;
        financeIntegrationStatus: FinanceIntegrationStatus;
        _attachments: ClientCollection<AttachmentAssociation>;
        businessRelation: Customer;
        paymentTerm: PaymentTerm;
        shipToCustomer: Customer;
        shipToCustomerAddress: BusinessEntityAddress;
        shipToAddress: Address;
        shipToContact: Contact;
        deliveryMode: DeliveryMode;
        deliveryLeadTime: integer;
        workDays: integer;
        deliveryDate: string;
        currency: Currency;
        transactionCurrency: Currency;
        companyFxRate: string;
        companyFxRateDivisor: string;
        rateDescription: string;
        totalAmountExcludingTax: string;
        totalAmountExcludingTaxInCompanyCurrency: string;
        taxes: ClientCollection<DocumentTax>;
        totalTaxAmount: string;
        totalTaxableAmount: string;
        totalExemptAmount: string;
        totalTaxAmountAdjusted: string;
        incoterm: Incoterm;
        lines: ClientCollection<StockTransferShipmentLine>;
        totalAmountIncludingTax: string;
        allocationStatus: StockAllocationStatus;
        stockTransactionStatus: StockDocumentTransactionStatus;
        displayStatus: BaseDisplayStatus;
    }
    export interface StockTransferShipmentInput extends ClientNodeInput {
        number?: string;
        status?: BaseStatus;
        approvalStatus?: ApprovalStatus;
        date?: string;
        isPrinted?: boolean | string;
        isSent?: boolean | string;
        site?: integer | string;
        stockSite?: integer | string;
        financialSite?: integer | string;
        text?: TextStream;
        internalNote?: TextStream;
        externalNote?: TextStream;
        isExternalNote?: boolean | string;
        isOverwriteNote?: boolean | string;
        isTransferHeaderNote?: boolean | string;
        isTransferLineNote?: boolean | string;
        siteAddress?: integer | string;
        businessEntityAddress?: integer | string;
        invoiceStatus?: InvoiceStatus;
        fxRateDate?: string;
        taxCalculationStatus?: TaxCalculationStatus;
        paymentTracking?: PaymentTrackingInput;
        customer?: integer | string;
        customerNumber?: string;
        trackingNumber?: string;
        receivingStatus?: ReceivingStatus;
        supplier?: integer | string;
        receivingSite?: integer | string;
        _attachments?: Partial<AttachmentAssociationInput>[];
        businessRelation?: integer | string;
        paymentTerm?: integer | string;
        shipToCustomer?: integer | string;
        shipToCustomerAddress?: integer | string;
        shipToAddress?: integer | string;
        shipToContact?: integer | string;
        deliveryMode?: integer | string;
        deliveryLeadTime?: integer | string;
        deliveryDate?: string;
        currency?: integer | string;
        companyFxRateDivisor?: decimal | string;
        totalAmountExcludingTax?: decimal | string;
        totalAmountExcludingTaxInCompanyCurrency?: decimal | string;
        taxes?: Partial<DocumentTaxInput>[];
        totalTaxAmount?: decimal | string;
        totalTaxableAmount?: decimal | string;
        totalExemptAmount?: decimal | string;
        totalTaxAmountAdjusted?: decimal | string;
        incoterm?: integer | string;
        lines?: Partial<StockTransferShipmentLineInput>[];
        displayStatus?: BaseDisplayStatus;
    }
    export interface StockTransferShipmentBinding extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        number: string;
        status: BaseStatus;
        approvalStatus: ApprovalStatus;
        date: string;
        canPrint: boolean;
        isPrinted: boolean;
        isSent: boolean;
        site: Site;
        stockSite: Site;
        companyCurrency: Currency;
        financialSite: Site;
        text: TextStream;
        internalNote: TextStream;
        externalNote: TextStream;
        isExternalNote: boolean;
        isOverwriteNote: boolean;
        isTransferHeaderNote: boolean;
        isTransferLineNote: boolean;
        siteAddress: Address;
        businessEntityAddress: BusinessEntityAddress;
        invoiceStatus: InvoiceStatus;
        fxRateDate: string;
        taxCalculationStatus: TaxCalculationStatus;
        taxEngine: TaxEngine;
        paymentTracking: PaymentTrackingBinding;
        netBalance: string;
        customer: Customer;
        customerNumber: string;
        trackingNumber: string;
        receivingStatus: ReceivingStatus;
        supplier: Supplier;
        receivingSite: Site;
        postingDetails: ClientCollection<FinanceTransaction>;
        financeIntegrationStatus: FinanceIntegrationStatus;
        _attachments: ClientCollection<AttachmentAssociation>;
        businessRelation: Customer;
        paymentTerm: PaymentTerm;
        shipToCustomer: Customer;
        shipToCustomerAddress: BusinessEntityAddress;
        shipToAddress: Address;
        shipToContact: Contact;
        deliveryMode: DeliveryMode;
        deliveryLeadTime: integer;
        workDays: integer;
        deliveryDate: string;
        currency: Currency;
        transactionCurrency: Currency;
        companyFxRate: string;
        companyFxRateDivisor: string;
        rateDescription: string;
        totalAmountExcludingTax: string;
        totalAmountExcludingTaxInCompanyCurrency: string;
        taxes: ClientCollection<DocumentTaxBinding>;
        totalTaxAmount: string;
        totalTaxableAmount: string;
        totalExemptAmount: string;
        totalTaxAmountAdjusted: string;
        incoterm: Incoterm;
        lines: ClientCollection<StockTransferShipmentLineBinding>;
        totalAmountIncludingTax: string;
        allocationStatus: StockAllocationStatus;
        stockTransactionStatus: StockDocumentTransactionStatus;
        displayStatus: BaseDisplayStatus;
    }
    export interface StockTransferShipment$Mutations {
        resendNotificationForFinance: Node$Operation<
            {
                stockTransferShipment: string;
            },
            boolean
        >;
        confirm: Node$Operation<
            {
                stockTransferShipmentNumber?: string;
            },
            string
        >;
        revoke: Node$Operation<
            {
                stockTransferShipmentNumber?: string;
            },
            string
        >;
        createReceiptFromShipment: Node$Operation<
            {
                stockTransferShipment: string;
            },
            StockTransferReceipt
        >;
        postToStock: Node$Operation<
            {
                documentIds?: (integer | string)[];
            },
            string
        >;
        repost: Node$Operation<
            {
                stockTransferShipment: string;
                documentLines?: {
                    baseDocumentLineSysId?: integer | string;
                    storedAttributes?: string;
                    storedDimensions?: string;
                }[];
            },
            {
                wasSuccessful: boolean;
                message: string;
            }
        >;
        beforePrintStockTransferShipmentPickList: Node$Operation<
            {
                stockTransferShipment: string;
            },
            boolean
        >;
        beforePrintStockTransferPackingSlip: Node$Operation<
            {
                stockTransferShipment: string;
            },
            boolean
        >;
        afterPrintStockTransferPackingSlip: Node$Operation<
            {
                stockTransferShipment: string;
            },
            boolean
        >;
        sendApprovalRequestMail: Node$Operation<
            {
                document?: string;
                user?: UserInput | null;
            },
            boolean
        >;
    }
    export interface StockTransferShipment$AsyncOperations {
        printBulkStockTransferShipmentPackingSlip: AsyncOperation<
            {
                filter?: string;
            },
            boolean
        >;
        printBulkStockTransferShipmentPickList: AsyncOperation<
            {
                filter?: string;
            },
            boolean
        >;
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
        bulkResync: AsyncOperation<
            {
                filter?: string;
            },
            boolean
        >;
    }
    export interface StockTransferShipment$Lookups {
        site: QueryOperation<Site>;
        stockSite: QueryOperation<Site>;
        companyCurrency: QueryOperation<Currency>;
        financialSite: QueryOperation<Site>;
        siteAddress: QueryOperation<Address>;
        businessEntityAddress: QueryOperation<BusinessEntityAddress>;
        customer: QueryOperation<Customer>;
        supplier: QueryOperation<Supplier>;
        receivingSite: QueryOperation<Site>;
        businessRelation: QueryOperation<BaseBusinessRelation>;
        paymentTerm: QueryOperation<PaymentTerm>;
        shipToCustomer: QueryOperation<Customer>;
        shipToCustomerAddress: QueryOperation<BusinessEntityAddress>;
        shipToAddress: QueryOperation<Address>;
        shipToContact: QueryOperation<Contact>;
        deliveryMode: QueryOperation<DeliveryMode>;
        currency: QueryOperation<Currency>;
        transactionCurrency: QueryOperation<Currency>;
        incoterm: QueryOperation<Incoterm>;
    }
    export interface StockTransferShipment$Operations {
        query: QueryOperation<StockTransferShipment>;
        read: ReadOperation<StockTransferShipment>;
        aggregate: {
            read: AggregateReadOperation<StockTransferShipment>;
            query: AggregateQueryOperation<StockTransferShipment>;
        };
        create: CreateOperation<StockTransferShipmentInput, StockTransferShipment>;
        getDuplicate: GetDuplicateOperation<StockTransferShipment>;
        duplicate: DuplicateOperation<string, StockTransferShipmentInput, StockTransferShipment>;
        update: UpdateOperation<StockTransferShipmentInput, StockTransferShipment>;
        updateById: UpdateByIdOperation<StockTransferShipmentInput, StockTransferShipment>;
        delete: DeleteOperation<{}>;
        deleteById: DeleteOperation<string>;
        mutations: StockTransferShipment$Mutations;
        asyncOperations: StockTransferShipment$AsyncOperations;
        lookups(dataOrId: string | { data: StockTransferShipmentInput }): StockTransferShipment$Lookups;
        getDefaults: GetDefaultsOperation<StockTransferShipment>;
    }
    export interface StockTransferShipmentLine extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        documentNumber: string;
        documentId: integer;
        document: StockTransferShipment;
        origin: BaseOrigin;
        status: BaseStatus;
        site: Site;
        siteLinkedAddress: BusinessEntityAddress;
        item: Item;
        itemDescription: string;
        stockSite: Site;
        stockSiteLinkedAddress: BusinessEntityAddress;
        itemSite: ItemSite;
        stockUnit: UnitOfMeasure;
        unit: UnitOfMeasure;
        quantity: string;
        internalNote: TextStream;
        externalNote: TextStream;
        isExternalNote: boolean;
        currency: Currency;
        grossPrice: string;
        discountCharges: ClientCollection<DocumentLineDiscountCharge>;
        priceOrigin: BasePriceOrigin;
        netPrice: string;
        signedQuantity: string;
        amountExcludingTax: string;
        taxableAmount: string;
        taxAmount: string;
        exemptAmount: string;
        taxDate: string;
        text: TextStream;
        signedAmountExcludingTax: string;
        amountExcludingTaxInCompanyCurrency: string;
        signedAmountExcludingTaxInCompanyCurrency: string;
        canHaveLandedCost: boolean;
        landedCost: LandedCostDocumentLine;
        canHaveLandedCostLine: boolean;
        landedCostLines: ClientCollection<LandedCostLine>;
        taxes: ClientCollection<DocumentLineTax>;
        taxCalculationStatus: TaxCalculationStatus;
        taxAmountAdjusted: string;
        itemCustomer: ItemCustomer;
        stockAllocations: ClientCollection<StockAllocation>;
        stockDetails: ClientCollection<StockChangeDetail>;
        stockTransactionStatus: StockDocumentTransactionStatus;
        quantityAllocatedInStockUnit: string;
        stockMovements: ClientCollection<StockJournal>;
        stockTransactions: ClientCollection<StockTransaction>;
        receivingStatus: ReceivingStatus;
        linkToStockTransferOrderLines: ClientCollection<StockTransferShipmentLineToStockTransferOrderLine>;
        linkToStockTransferReceiptLines: ClientCollection<StockTransferReceiptLineToStockTransferShipmentLine>;
        inTransit: StockTransferShipmentLineInTransit;
        dimensionDefinitionLevel: DimensionDefinitionLevel;
        storedDimensions: string;
        storedAttributes: string;
        computedAttributes: string;
        analyticalData: AnalyticalData;
        quantityInStockUnit: string;
        unitToStockUnitConversionFactor: string;
        amountIncludingTax: string;
        amountIncludingTaxInCompanyCurrency: string;
        stockCostAmountInCompanyCurrency: string;
        stockCostAmount: string;
        orderCost: string;
        stockCostUnit: string;
        remainingQuantityInStockUnit: string;
        remainingQuantityToAllocateInStockUnit: string;
        allocationStatus: StockAllocationStatus;
    }
    export interface StockTransferShipmentLineInput extends VitalClientNodeInput {
        origin?: BaseOrigin;
        status?: BaseStatus;
        site?: integer | string;
        siteLinkedAddress?: integer | string;
        item?: integer | string;
        itemDescription?: string;
        stockSite?: integer | string;
        stockSiteLinkedAddress?: integer | string;
        stockUnit?: integer | string;
        unit?: integer | string;
        quantity?: decimal | string;
        internalNote?: TextStream;
        externalNote?: TextStream;
        isExternalNote?: boolean | string;
        grossPrice?: decimal | string;
        discountCharges?: Partial<DocumentLineDiscountChargeInput>[];
        priceOrigin?: BasePriceOrigin;
        netPrice?: decimal | string;
        amountExcludingTax?: decimal | string;
        taxableAmount?: decimal | string;
        taxAmount?: decimal | string;
        exemptAmount?: decimal | string;
        taxDate?: string;
        text?: TextStream;
        amountExcludingTaxInCompanyCurrency?: decimal | string;
        landedCost?: LandedCostDocumentLineInput;
        landedCostLines?: Partial<LandedCostLineInput>[];
        taxes?: Partial<DocumentLineTaxInput>[];
        taxAmountAdjusted?: decimal | string;
        stockDetails?: Partial<StockChangeDetailInput>[];
        stockTransactionStatus?: StockDocumentTransactionStatus;
        stockTransactions?: Partial<StockTransactionInput>[];
        receivingStatus?: ReceivingStatus;
        linkToStockTransferOrderLines?: Partial<StockTransferShipmentLineToStockTransferOrderLineInput>[];
        linkToStockTransferReceiptLines?: Partial<StockTransferReceiptLineToStockTransferShipmentLineInput>[];
        inTransit?: StockTransferShipmentLineInTransitInput;
        storedDimensions?: string;
        storedAttributes?: string;
        analyticalData?: integer | string;
        quantityInStockUnit?: decimal | string;
        unitToStockUnitConversionFactor?: decimal | string;
        amountIncludingTax?: decimal | string;
        amountIncludingTaxInCompanyCurrency?: decimal | string;
        orderCost?: decimal | string;
    }
    export interface StockTransferShipmentLineBinding extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        documentNumber: string;
        documentId: integer;
        document: StockTransferShipment;
        origin: BaseOrigin;
        status: BaseStatus;
        site: Site;
        siteLinkedAddress: BusinessEntityAddress;
        item: Item;
        itemDescription: string;
        stockSite: Site;
        stockSiteLinkedAddress: BusinessEntityAddress;
        itemSite: ItemSite;
        stockUnit: UnitOfMeasure;
        unit: UnitOfMeasure;
        quantity: string;
        internalNote: TextStream;
        externalNote: TextStream;
        isExternalNote: boolean;
        currency: Currency;
        grossPrice: string;
        discountCharges: ClientCollection<DocumentLineDiscountChargeBinding>;
        priceOrigin: BasePriceOrigin;
        netPrice: string;
        signedQuantity: string;
        amountExcludingTax: string;
        taxableAmount: string;
        taxAmount: string;
        exemptAmount: string;
        taxDate: string;
        text: TextStream;
        signedAmountExcludingTax: string;
        amountExcludingTaxInCompanyCurrency: string;
        signedAmountExcludingTaxInCompanyCurrency: string;
        canHaveLandedCost: boolean;
        landedCost: LandedCostDocumentLineBinding;
        canHaveLandedCostLine: boolean;
        landedCostLines: ClientCollection<LandedCostLineBinding>;
        taxes: ClientCollection<DocumentLineTaxBinding>;
        taxCalculationStatus: TaxCalculationStatus;
        taxAmountAdjusted: string;
        itemCustomer: ItemCustomer;
        stockAllocations: ClientCollection<StockAllocation>;
        stockDetails: ClientCollection<StockChangeDetailBinding>;
        stockTransactionStatus: StockDocumentTransactionStatus;
        quantityAllocatedInStockUnit: string;
        stockMovements: ClientCollection<StockJournal>;
        stockTransactions: ClientCollection<StockTransactionBinding>;
        receivingStatus: ReceivingStatus;
        linkToStockTransferOrderLines: ClientCollection<StockTransferShipmentLineToStockTransferOrderLineBinding>;
        linkToStockTransferReceiptLines: ClientCollection<StockTransferReceiptLineToStockTransferShipmentLine>;
        inTransit: StockTransferShipmentLineInTransitBinding;
        dimensionDefinitionLevel: DimensionDefinitionLevel;
        storedDimensions: any;
        storedAttributes: any;
        computedAttributes: any;
        analyticalData: AnalyticalData;
        quantityInStockUnit: string;
        unitToStockUnitConversionFactor: string;
        amountIncludingTax: string;
        amountIncludingTaxInCompanyCurrency: string;
        stockCostAmountInCompanyCurrency: string;
        stockCostAmount: string;
        orderCost: string;
        stockCostUnit: string;
        remainingQuantityInStockUnit: string;
        remainingQuantityToAllocateInStockUnit: string;
        allocationStatus: StockAllocationStatus;
    }
    export interface StockTransferShipmentLine$Mutations {
        setDimension: Node$Operation<
            {
                baseDocumentItemLine: string;
                storedDimensions?: string;
                storedAttributes?: string;
            },
            boolean
        >;
    }
    export interface StockTransferShipmentLine$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface StockTransferShipmentLine$Lookups {
        site: QueryOperation<Site>;
        siteLinkedAddress: QueryOperation<BusinessEntityAddress>;
        item: QueryOperation<Item>;
        stockSite: QueryOperation<Site>;
        stockSiteLinkedAddress: QueryOperation<BusinessEntityAddress>;
        itemSite: QueryOperation<ItemSite>;
        stockUnit: QueryOperation<UnitOfMeasure>;
        unit: QueryOperation<UnitOfMeasure>;
        currency: QueryOperation<Currency>;
        itemCustomer: QueryOperation<ItemCustomer>;
    }
    export interface StockTransferShipmentLine$Operations {
        query: QueryOperation<StockTransferShipmentLine>;
        read: ReadOperation<StockTransferShipmentLine>;
        aggregate: {
            read: AggregateReadOperation<StockTransferShipmentLine>;
            query: AggregateQueryOperation<StockTransferShipmentLine>;
        };
        mutations: StockTransferShipmentLine$Mutations;
        asyncOperations: StockTransferShipmentLine$AsyncOperations;
        lookups(dataOrId: string | { data: StockTransferShipmentLineInput }): StockTransferShipmentLine$Lookups;
        getDefaults: GetDefaultsOperation<StockTransferShipmentLine>;
    }
    export interface StockTransferShipmentLineToStockTransferOrderLine extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        from: StockTransferShipmentLine;
        to: StockTransferOrderLine;
    }
    export interface StockTransferShipmentLineToStockTransferOrderLineInput extends VitalClientNodeInput {
        to?: integer | string;
    }
    export interface StockTransferShipmentLineToStockTransferOrderLineBinding extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        from: StockTransferShipmentLine;
        to: StockTransferOrderLine;
    }
    export interface StockTransferShipmentLineToStockTransferOrderLine$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface StockTransferShipmentLineToStockTransferOrderLine$Lookups {
        to: QueryOperation<BaseDocumentItemLine>;
    }
    export interface StockTransferShipmentLineToStockTransferOrderLine$Operations {
        query: QueryOperation<StockTransferShipmentLineToStockTransferOrderLine>;
        read: ReadOperation<StockTransferShipmentLineToStockTransferOrderLine>;
        aggregate: {
            read: AggregateReadOperation<StockTransferShipmentLineToStockTransferOrderLine>;
            query: AggregateQueryOperation<StockTransferShipmentLineToStockTransferOrderLine>;
        };
        asyncOperations: StockTransferShipmentLineToStockTransferOrderLine$AsyncOperations;
        lookups(
            dataOrId: string | { data: StockTransferShipmentLineToStockTransferOrderLineInput },
        ): StockTransferShipmentLineToStockTransferOrderLine$Lookups;
        getDefaults: GetDefaultsOperation<StockTransferShipmentLineToStockTransferOrderLine>;
    }
    export interface WorkInProgressStockTransferOrderLine extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        documentType: WorkInProgressDocumentType;
        stockTransferOrderLine: StockTransferOrderLine;
        item: Item;
        site: Site;
        status: OrderType;
        startDate: string;
        endDate: string;
        expectedQuantity: string;
        actualQuantity: string;
        outstandingQuantity: string;
    }
    export interface WorkInProgressStockTransferOrderLineInput extends VitalClientNodeInput {
        documentType?: WorkInProgressDocumentType;
        item?: integer | string;
        site?: integer | string;
        status?: OrderType;
        startDate?: string;
        endDate?: string;
        expectedQuantity?: decimal | string;
        actualQuantity?: decimal | string;
        outstandingQuantity?: decimal | string;
    }
    export interface WorkInProgressStockTransferOrderLineBinding extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        documentType: WorkInProgressDocumentType;
        documentId: integer;
        originDocumentType: WorkInProgressDocumentType;
        originDocumentLine: integer;
        stockTransferOrderLine: StockTransferOrderLine;
        item: Item;
        site: Site;
        status: OrderType;
        startDate: string;
        endDate: string;
        expectedQuantity: string;
        actualQuantity: string;
        outstandingQuantity: string;
        remainingQuantityToAllocate: string;
        documentNumber: string;
        documentLine: integer;
    }
    export interface WorkInProgressStockTransferOrderLine$Queries {
        getWorkInProgressQuantityPerItemSite: Node$Operation<
            {
                currentItem: integer | string;
                currentSite: integer | string;
                currentDate?: string;
                currentStatus?: OrderType;
                originDocumentType?: WorkInProgressDocumentType;
            },
            {
                supply: string;
                demand: string;
            }
        >;
    }
    export interface WorkInProgressStockTransferOrderLine$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface WorkInProgressStockTransferOrderLine$Lookups {
        item: QueryOperation<Item>;
        site: QueryOperation<Site>;
    }
    export interface WorkInProgressStockTransferOrderLine$Operations {
        query: QueryOperation<WorkInProgressStockTransferOrderLine>;
        read: ReadOperation<WorkInProgressStockTransferOrderLine>;
        aggregate: {
            read: AggregateReadOperation<WorkInProgressStockTransferOrderLine>;
            query: AggregateQueryOperation<WorkInProgressStockTransferOrderLine>;
        };
        queries: WorkInProgressStockTransferOrderLine$Queries;
        asyncOperations: WorkInProgressStockTransferOrderLine$AsyncOperations;
        lookups(
            dataOrId: string | { data: WorkInProgressStockTransferOrderLineInput },
        ): WorkInProgressStockTransferOrderLine$Lookups;
        getDefaults: GetDefaultsOperation<WorkInProgressStockTransferOrderLine>;
    }
    export interface WorkInProgressStockTransferReceiptLine extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        actualQuantity: string;
        documentType: WorkInProgressDocumentType;
        stockTransferReceiptLine: StockTransferReceiptLine;
        item: Item;
        site: Site;
        status: OrderType;
        startDate: string;
        endDate: string;
        expectedQuantity: string;
        outstandingQuantity: string;
    }
    export interface WorkInProgressStockTransferReceiptLineInput extends VitalClientNodeInput {
        actualQuantity?: decimal | string;
        remainingQuantityToAllocate?: decimal | string;
        documentType?: WorkInProgressDocumentType;
        item?: integer | string;
        site?: integer | string;
        status?: OrderType;
        startDate?: string;
        endDate?: string;
        expectedQuantity?: decimal | string;
        outstandingQuantity?: decimal | string;
    }
    export interface WorkInProgressStockTransferReceiptLineBinding extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        actualQuantity: string;
        remainingQuantityToAllocate: string;
        documentType: WorkInProgressDocumentType;
        documentId: integer;
        originDocumentType: WorkInProgressDocumentType;
        originDocumentLine: integer;
        stockTransferReceiptLine: StockTransferReceiptLine;
        item: Item;
        site: Site;
        status: OrderType;
        startDate: string;
        endDate: string;
        expectedQuantity: string;
        outstandingQuantity: string;
        documentNumber: string;
        documentLine: integer;
    }
    export interface WorkInProgressStockTransferReceiptLine$Queries {
        getWorkInProgressQuantityPerItemSite: Node$Operation<
            {
                currentItem: integer | string;
                currentSite: integer | string;
                currentDate?: string;
                currentStatus?: OrderType;
                originDocumentType?: WorkInProgressDocumentType;
            },
            {
                supply: string;
                demand: string;
            }
        >;
    }
    export interface WorkInProgressStockTransferReceiptLine$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface WorkInProgressStockTransferReceiptLine$Lookups {
        item: QueryOperation<Item>;
        site: QueryOperation<Site>;
    }
    export interface WorkInProgressStockTransferReceiptLine$Operations {
        query: QueryOperation<WorkInProgressStockTransferReceiptLine>;
        read: ReadOperation<WorkInProgressStockTransferReceiptLine>;
        aggregate: {
            read: AggregateReadOperation<WorkInProgressStockTransferReceiptLine>;
            query: AggregateQueryOperation<WorkInProgressStockTransferReceiptLine>;
        };
        queries: WorkInProgressStockTransferReceiptLine$Queries;
        asyncOperations: WorkInProgressStockTransferReceiptLine$AsyncOperations;
        lookups(
            dataOrId: string | { data: WorkInProgressStockTransferReceiptLineInput },
        ): WorkInProgressStockTransferReceiptLine$Lookups;
        getDefaults: GetDefaultsOperation<WorkInProgressStockTransferReceiptLine>;
    }
    export interface LandedCostAllocationExtension {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        line: LandedCostDocumentLine;
        allocatedDocumentLine: BaseDocumentLine;
        allocatedDocumentType: string;
        landedCostItem: Item;
        costAmount: string;
        costAmountInCompanyCurrency: string;
        item: Item;
        documentLineAmountInCompanyCurrency: string;
        documentLineQuantityInStockUnit: string;
        documentLineWeight: string;
        documentLineVolume: string;
        sourceAllocationLine: LandedCostAllocation;
        creditedCostAmount: string;
        creditedCostAmountInCompanyCurrency: string;
    }
    export interface LandedCostAllocationInputExtension {
        allocatedDocumentLine?: integer | string;
        costAmount?: decimal | string;
        costAmountInCompanyCurrency?: decimal | string;
        sourceAllocationLine?: integer | string;
        creditedCostAmount?: decimal | string;
        creditedCostAmountInCompanyCurrency?: decimal | string;
    }
    export interface LandedCostAllocationBindingExtension {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        line: LandedCostDocumentLine;
        allocatedDocumentLine: BaseDocumentLine;
        allocatedDocumentType: string;
        landedCostItem: Item;
        costAmount: string;
        costAmountInCompanyCurrency: string;
        item: Item;
        documentLineAmountInCompanyCurrency: string;
        documentLineQuantityInStockUnit: string;
        documentLineWeight: string;
        documentLineVolume: string;
        sourceAllocationLine: LandedCostAllocation;
        creditedCostAmount: string;
        creditedCostAmountInCompanyCurrency: string;
    }
    export interface MrpResultLineExtension {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        mrpCalculation: MrpCalculation;
        company: string;
        site: string;
        item: string;
        startDate: string;
        endDate: string;
        preferredProcess: string;
        suggestionStatus: SuggestionStatus;
        quantity: string;
        referenceItem: Item;
        referenceSite: Site;
        referenceItemSite: ItemSite;
        stockUnit: UnitOfMeasure;
    }
    export interface MrpResultLineInputExtension {
        company?: string;
        site?: string;
        item?: string;
        startDate?: string;
        endDate?: string;
        preferredProcess?: string;
        suggestionStatus?: SuggestionStatus;
        quantity?: decimal | string;
    }
    export interface MrpResultLineBindingExtension {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        mrpCalculation: MrpCalculation;
        company: any;
        site: any;
        item: any;
        startDate: string;
        endDate: string;
        preferredProcess: string;
        suggestionStatus: SuggestionStatus;
        quantity: string;
        referenceItem: Item;
        referenceSite: Site;
        referenceItemSite: ItemSite;
        stockUnit: UnitOfMeasure;
    }
    export interface SalesOrderExtension {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        number: string;
        status: BaseStatus;
        displayStatus: BaseDisplayStatus;
        approvalStatus: ApprovalStatus;
        date: string;
        canPrint: boolean;
        isPrinted: boolean;
        isSent: boolean;
        site: Site;
        companyCurrency: Currency;
        financialSite: Site;
        text: TextStream;
        externalNote: TextStream;
        isExternalNote: boolean;
        isTransferHeaderNote: boolean;
        isTransferLineNote: boolean;
        siteAddress: Address;
        businessEntityAddress: BusinessEntityAddress;
        customerNumber: string;
        isQuote: boolean;
        invoiceStatus: SalesDocumentInvoiceStatus;
        orderDate: string;
        taxEngine: TaxEngine;
        soldToCustomer: Customer;
        soldToLinkedAddress: BusinessEntityAddress;
        soldToAddress: Address;
        soldToContact: Contact;
        fxRateDate: string;
        requestedDeliveryDate: string;
        doNotShipBeforeDate: string;
        doNotShipAfterDate: string;
        shippingStatus: SalesDocumentShippingStatus;
        salesSite: Site;
        shipToCustomer: Customer;
        shipToCustomerAddress: BusinessEntityAddress;
        shipToAddress: Address;
        shipToContact: Contact;
        billToCustomer: Customer;
        billToLinkedAddress: BusinessEntityAddress;
        billToAddress: Address;
        billToContact: Contact;
        incoterm: Incoterm;
        deliveryMode: DeliveryMode;
        deliveryLeadTime: integer;
        isOrderAssignmentLinked: boolean;
        taxCalculationStatus: TaxCalculationStatus;
        recordUrl: string;
        proformaInvoices: ClientCollection<ProformaInvoice>;
        financeIntegrationStatus: FinanceIntegrationStatus;
        _attachments: ClientCollection<AttachmentAssociation>;
        stockSite: Site;
        currency: Currency;
        transactionCurrency: Currency;
        internalNote: TextStream;
        companyFxRate: string;
        companyFxRateDivisor: string;
        paymentTerm: PaymentTerm;
        isOnHold: boolean;
        workDays: integer;
        totalTaxAmount: string;
        totalTaxableAmount: string;
        totalExemptAmount: string;
        totalTaxAmountAdjusted: string;
        taxes: ClientCollection<SalesOrderTax>;
        lines: ClientCollection<SalesOrderLine>;
        rateDescription: string;
        shippingDate: string;
        expectedDeliveryDate: string;
        totalAmountExcludingTax: string;
        remainingTotalAmountToShipExcludingTax: string;
        remainingTotalAmountExcludingTaxInCompanyCurrency: string;
        totalAmountIncludingTax: string;
        totalAmountIncludingTaxInCompanyCurrency: string;
        totalAmountExcludingTaxInCompanyCurrency: string;
        allocationStatus: StockAllocationStatus;
        allocationRequestStatus: AllocationRequestStatus;
        totalGrossProfit: string;
        isCloseHidden: boolean;
    }
    export interface SalesOrderInputExtension {
        number?: string;
        status?: BaseStatus;
        displayStatus?: BaseDisplayStatus;
        approvalStatus?: ApprovalStatus;
        date?: string;
        isPrinted?: boolean | string;
        isSent?: boolean | string;
        site?: integer | string;
        financialSite?: integer | string;
        text?: TextStream;
        externalNote?: TextStream;
        isExternalNote?: boolean | string;
        isOverwriteNote?: boolean | string;
        isTransferHeaderNote?: boolean | string;
        isTransferLineNote?: boolean | string;
        siteAddress?: integer | string;
        businessEntityAddress?: integer | string;
        customerNumber?: string;
        isQuote?: boolean | string;
        invoiceStatus?: SalesDocumentInvoiceStatus;
        soldToCustomer?: integer | string;
        soldToLinkedAddress?: integer | string;
        soldToAddress?: integer | string;
        soldToContact?: integer | string;
        fxRateDate?: string;
        requestedDeliveryDate?: string;
        doNotShipBeforeDate?: string;
        doNotShipAfterDate?: string;
        shippingStatus?: SalesDocumentShippingStatus;
        shipToCustomer?: integer | string;
        shipToCustomerAddress?: integer | string;
        shipToAddress?: integer | string;
        shipToContact?: integer | string;
        billToCustomer?: integer | string;
        billToLinkedAddress?: integer | string;
        billToAddress?: integer | string;
        billToContact?: integer | string;
        incoterm?: integer | string;
        deliveryMode?: integer | string;
        deliveryLeadTime?: integer | string;
        taxCalculationStatus?: TaxCalculationStatus;
        _attachments?: Partial<AttachmentAssociationInput>[];
        stockSite?: integer | string;
        currency?: integer | string;
        internalNote?: TextStream;
        companyFxRate?: decimal | string;
        companyFxRateDivisor?: decimal | string;
        paymentTerm?: integer | string;
        totalTaxAmount?: decimal | string;
        totalTaxableAmount?: decimal | string;
        totalExemptAmount?: decimal | string;
        totalTaxAmountAdjusted?: decimal | string;
        taxes?: Partial<SalesOrderTaxInput>[];
        lines?: Partial<SalesOrderLineInput>[];
        shippingDate?: string;
        expectedDeliveryDate?: string;
    }
    export interface SalesOrderBindingExtension {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        number: string;
        status: BaseStatus;
        displayStatus: BaseDisplayStatus;
        approvalStatus: ApprovalStatus;
        date: string;
        canPrint: boolean;
        isPrinted: boolean;
        isSent: boolean;
        site: Site;
        companyCurrency: Currency;
        financialSite: Site;
        text: TextStream;
        externalNote: TextStream;
        isExternalNote: boolean;
        isOverwriteNote: boolean;
        isTransferHeaderNote: boolean;
        isTransferLineNote: boolean;
        siteAddress: Address;
        businessEntityAddress: BusinessEntityAddress;
        customerNumber: string;
        isQuote: boolean;
        invoiceStatus: SalesDocumentInvoiceStatus;
        orderDate: string;
        taxEngine: TaxEngine;
        soldToCustomer: Customer;
        soldToLinkedAddress: BusinessEntityAddress;
        soldToAddress: Address;
        soldToContact: Contact;
        fxRateDate: string;
        requestedDeliveryDate: string;
        doNotShipBeforeDate: string;
        doNotShipAfterDate: string;
        shippingStatus: SalesDocumentShippingStatus;
        salesSite: Site;
        shipToCustomer: Customer;
        shipToCustomerAddress: BusinessEntityAddress;
        shipToAddress: Address;
        shipToContact: Contact;
        billToCustomer: Customer;
        billToLinkedAddress: BusinessEntityAddress;
        billToAddress: Address;
        billToContact: Contact;
        incoterm: Incoterm;
        deliveryMode: DeliveryMode;
        deliveryLeadTime: integer;
        isOrderAssignmentLinked: boolean;
        taxCalculationStatus: TaxCalculationStatus;
        recordUrl: string;
        proformaInvoices: ClientCollection<ProformaInvoice>;
        financeIntegrationStatus: FinanceIntegrationStatus;
        _attachments: ClientCollection<AttachmentAssociation>;
        stockSite: Site;
        currency: Currency;
        transactionCurrency: Currency;
        internalNote: TextStream;
        companyFxRate: string;
        companyFxRateDivisor: string;
        paymentTerm: PaymentTerm;
        isOnHold: boolean;
        workDays: integer;
        totalTaxAmount: string;
        totalTaxableAmount: string;
        totalExemptAmount: string;
        totalTaxAmountAdjusted: string;
        taxes: ClientCollection<SalesOrderTaxBinding>;
        lines: ClientCollection<SalesOrderLineBinding>;
        rateDescription: string;
        shippingDate: string;
        expectedDeliveryDate: string;
        totalAmountExcludingTax: string;
        remainingTotalAmountToShipExcludingTax: string;
        remainingTotalAmountExcludingTaxInCompanyCurrency: string;
        totalAmountIncludingTax: string;
        totalAmountIncludingTaxInCompanyCurrency: string;
        totalAmountExcludingTaxInCompanyCurrency: string;
        allocationStatus: StockAllocationStatus;
        allocationRequestStatus: AllocationRequestStatus;
        totalGrossProfit: string;
        isCloseHidden: boolean;
    }
    export interface SalesOrderExtension$Mutations {
        sendApprovalRequestMail: Node$Operation<
            {
                document?: string;
                user?: UserInput | null;
            },
            boolean
        >;
        createBackToBackOrderFromSalesOrder: Node$Operation<
            {
                salesOrder: string;
            },
            {
                purchaseOrders: PurchaseOrder[];
                workOrders: WorkOrder[];
            }
        >;
        createBackToBackPurchaseOrder: Node$Operation<
            {
                data?: {
                    orderDate?: string;
                    expectedReceiptDate?: string;
                    quantity?: decimal | string;
                    grossPrice?: decimal | string;
                    unit?: integer | string;
                    site?: integer | string;
                    stockSite?: integer | string;
                    item?: integer | string;
                    supplier?: integer | string;
                    priceOrigin?: PriceOrigin;
                    storedDimensions?: string;
                    storedAttributes?: string;
                    currency?: integer | string;
                    remainingQuantityToShipInStockUnit?: decimal | string;
                    salesOrderLineSysId?: integer | string;
                    salesOrderLineWorkInProgress?: (integer | string) | null;
                };
            },
            PurchaseOrder | null
        >;
        createBackToBackWorkOrder: Node$Operation<
            {
                data: {
                    site: integer | string;
                    item: integer | string;
                    releasedQuantity: decimal | string;
                    name: string;
                    type: WorkOrderType;
                    workOrderCategory: integer | string;
                    startDate?: string;
                    billOfMaterial?: integer | string;
                    routing?: integer | string;
                    storedDimensions?: string | null;
                    storedAttributes?: string | null;
                    remainingQuantityToShipInStockUnit?: decimal | string;
                    salesOrderLineSysId?: integer | string;
                    salesOrderLineWorkInProgress?: (integer | string) | null;
                    workOrderNumber?: string;
                };
            },
            WorkOrder
        >;
    }
    export interface SalesOrderExtension$AsyncOperations {
        bulkResync: AsyncOperation<
            {
                filter?: string;
            },
            boolean
        >;
    }
    export interface SalesOrderExtension$Operations {
        mutations: SalesOrderExtension$Mutations;
        asyncOperations: SalesOrderExtension$AsyncOperations;
        getDefaults: GetDefaultsOperation<SalesOrder>;
    }
    export interface SiteExtension {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        id: string;
        name: string;
        description: string;
        isActive: boolean;
        legalCompany: Company;
        linkedSites: ClientCollection<Site>;
        siteGroups: ClientCollection<SiteGroup>;
        groupRoleSites: ClientCollection<GroupRoleSite>;
        hierarchyChartContent: TextStream;
        businessEntity: BusinessEntity;
        financialCurrency: Currency;
        isFinance: boolean;
        isPurchase: boolean;
        isInventory: boolean;
        isSales: boolean;
        isManufacturing: boolean;
        isProjectManagement: boolean;
        stockSite: Site;
        itemSites: ClientCollection<ItemSite>;
        primaryAddress: BusinessEntityAddress;
        financialSite: Site;
        isLocationManaged: boolean;
        defaultLocation: Location;
        sequenceNumberId: string;
        isSequenceNumberIdUsed: boolean;
        timeZone: string;
        country: Country;
        taxIdNumber: string;
        siret: string;
        currency: Currency;
        defaultStockStatus: StockStatus;
        storedDimensions: string;
        storedAttributes: string;
        isPurchaseRequisitionApprovalManaged: boolean;
        purchaseRequisitionDefaultApprover: User;
        purchaseRequisitionSubstituteApprover: User;
        isPurchaseOrderApprovalManaged: boolean;
        isPurchaseReturnApprovalManaged: boolean;
        purchaseOrderDefaultApprover: User;
        purchaseOrderSubstituteApprover: User;
        isSalesReturnRequestApprovalManaged: boolean;
        salesReturnRequestDefaultApprover: User;
        salesReturnRequestSubstituteApprover: User;
        isStockTransferOrderApprovalManaged: boolean;
        stockTransferOrderDefaultApprover: User;
        stockTransferOrderSubstituteApprover: User;
        _syncTick: string;
        analyticalData: AnalyticalData;
    }
    export interface SiteInputExtension {
        id?: string;
        name?: string;
        description?: string;
        isActive?: boolean | string;
        legalCompany?: integer | string;
        businessEntity?: BusinessEntityInput;
        isFinance?: boolean | string;
        isPurchase?: boolean | string;
        isInventory?: boolean | string;
        isSales?: boolean | string;
        isManufacturing?: boolean | string;
        isProjectManagement?: boolean | string;
        primaryAddress?: integer | string;
        financialSite?: integer | string;
        isLocationManaged?: boolean | string;
        defaultLocation?: integer | string;
        sequenceNumberId?: string;
        timeZone?: string;
        defaultStockStatus?: integer | string;
        storedDimensions?: string;
        storedAttributes?: string;
        isPurchaseRequisitionApprovalManaged?: boolean | string;
        purchaseRequisitionDefaultApprover?: integer | string;
        purchaseRequisitionSubstituteApprover?: integer | string;
        isPurchaseOrderApprovalManaged?: boolean | string;
        isPurchaseReturnApprovalManaged?: boolean | string;
        purchaseOrderDefaultApprover?: integer | string;
        purchaseOrderSubstituteApprover?: integer | string;
        isSalesReturnRequestApprovalManaged?: boolean | string;
        salesReturnRequestDefaultApprover?: integer | string;
        salesReturnRequestSubstituteApprover?: integer | string;
        isStockTransferOrderApprovalManaged?: boolean | string;
        stockTransferOrderDefaultApprover?: integer | string;
        stockTransferOrderSubstituteApprover?: integer | string;
        _syncTick?: decimal | string;
        analyticalData?: integer | string;
    }
    export interface SiteBindingExtension {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        id: string;
        name: string;
        description: string;
        isActive: boolean;
        legalCompany: Company;
        linkedSites: ClientCollection<Site>;
        siteGroups: ClientCollection<SiteGroup>;
        groupRoleSites: ClientCollection<GroupRoleSite>;
        hierarchyChartContent: TextStream;
        businessEntity: BusinessEntity;
        financialCurrency: Currency;
        isFinance: boolean;
        isPurchase: boolean;
        isInventory: boolean;
        isSales: boolean;
        isManufacturing: boolean;
        isProjectManagement: boolean;
        stockSite: Site;
        itemSites: ClientCollection<ItemSite>;
        primaryAddress: BusinessEntityAddress;
        financialSite: Site;
        isLocationManaged: boolean;
        defaultLocation: Location;
        sequenceNumberId: string;
        isSequenceNumberIdUsed: boolean;
        timeZone: string;
        country: Country;
        taxIdNumber: string;
        siret: string;
        currency: Currency;
        defaultStockStatus: StockStatus;
        storedDimensions: any;
        storedAttributes: any;
        isPurchaseRequisitionApprovalManaged: boolean;
        purchaseRequisitionDefaultApprover: User;
        purchaseRequisitionSubstituteApprover: User;
        isPurchaseOrderApprovalManaged: boolean;
        isPurchaseReturnApprovalManaged: boolean;
        purchaseOrderDefaultApprover: User;
        purchaseOrderSubstituteApprover: User;
        isSalesReturnRequestApprovalManaged: boolean;
        salesReturnRequestDefaultApprover: User;
        salesReturnRequestSubstituteApprover: User;
        isStockTransferOrderApprovalManaged: boolean;
        stockTransferOrderDefaultApprover: User;
        stockTransferOrderSubstituteApprover: User;
        _syncTick: string;
        analyticalData: AnalyticalData;
    }
    export interface SiteExtension$Lookups {
        stockTransferOrderDefaultApprover: QueryOperation<User>;
        stockTransferOrderSubstituteApprover: QueryOperation<User>;
    }
    export interface SiteExtension$Operations {
        lookups(dataOrId: string | { data: SiteInput }): SiteExtension$Lookups;
    }
    export interface Package {
        '@sage/xtrem-supply-chain/StockTransferShipmentLineInTransit': StockTransferShipmentLineInTransit$Operations;
        '@sage/xtrem-supply-chain/SupplyPlanning': SupplyPlanning$Operations;
        '@sage/xtrem-supply-chain/StockTransferInTransitInquiry': StockTransferInTransitInquiry$Operations;
        '@sage/xtrem-supply-chain/StockTransferOrder': StockTransferOrder$Operations;
        '@sage/xtrem-supply-chain/StockTransferOrderLine': StockTransferOrderLine$Operations;
        '@sage/xtrem-supply-chain/StockTransferReceipt': StockTransferReceipt$Operations;
        '@sage/xtrem-supply-chain/StockTransferReceiptLine': StockTransferReceiptLine$Operations;
        '@sage/xtrem-supply-chain/StockTransferReceiptLineToStockTransferShipmentLine': StockTransferReceiptLineToStockTransferShipmentLine$Operations;
        '@sage/xtrem-supply-chain/StockTransferShipment': StockTransferShipment$Operations;
        '@sage/xtrem-supply-chain/StockTransferShipmentLine': StockTransferShipmentLine$Operations;
        '@sage/xtrem-supply-chain/StockTransferShipmentLineToStockTransferOrderLine': StockTransferShipmentLineToStockTransferOrderLine$Operations;
        '@sage/xtrem-supply-chain/WorkInProgressStockTransferOrderLine': WorkInProgressStockTransferOrderLine$Operations;
        '@sage/xtrem-supply-chain/WorkInProgressStockTransferReceiptLine': WorkInProgressStockTransferReceiptLine$Operations;
    }
    export interface GraphApi
        extends Package,
            SageXtremAuditing$Package,
            SageXtremAuthorization$Package,
            SageXtremCommunication$Package,
            SageXtremCustomization$Package,
            SageXtremDashboard$Package,
            SageXtremDistribution$Package,
            SageXtremFinanceData$Package,
            SageXtremImportExport$Package,
            SageXtremLandedCost$Package,
            SageXtremMailer$Package,
            SageXtremManufacturing$Package,
            SageXtremMasterData$Package,
            SageXtremMetadata$Package,
            SageXtremMrpData$Package,
            SageXtremPurchasing$Package,
            SageXtremReporting$Package,
            SageXtremRouting$Package,
            SageXtremSales$Package,
            SageXtremScheduler$Package,
            SageXtremStockData$Package,
            SageXtremStructure$Package,
            SageXtremSynchronization$Package,
            SageXtremSystem$Package,
            SageXtremTax$Package,
            SageXtremTechnicalData$Package,
            SageXtremUpload$Package,
            SageXtremWorkflow$Package {}
}
declare module '@sage/xtrem-supply-chain-api' {
    export type * from '@sage/xtrem-supply-chain-api-partial';
}
declare module '@sage/xtrem-auditing-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-supply-chain-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-authorization-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-supply-chain-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-communication-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-supply-chain-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-customization-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-supply-chain-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-dashboard-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-supply-chain-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-distribution-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-supply-chain-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-finance-data-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-supply-chain-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-import-export-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-supply-chain-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-landed-cost-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-supply-chain-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-mailer-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-supply-chain-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-manufacturing-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-supply-chain-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-master-data-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-supply-chain-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-metadata-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-supply-chain-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-mrp-data-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-supply-chain-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-purchasing-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-supply-chain-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-reporting-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-supply-chain-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-routing-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-supply-chain-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-sales-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-supply-chain-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-scheduler-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-supply-chain-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-stock-data-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-supply-chain-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-structure-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-supply-chain-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-synchronization-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-supply-chain-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-system-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-supply-chain-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-tax-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-supply-chain-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-technical-data-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-supply-chain-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-upload-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-supply-chain-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-workflow-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-supply-chain-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-landed-cost-api-partial' {
    import type {
        LandedCostAllocationBindingExtension,
        LandedCostAllocationExtension,
        LandedCostAllocationInputExtension,
    } from '@sage/xtrem-supply-chain-api';
    export interface LandedCostAllocation extends LandedCostAllocationExtension {}
    export interface LandedCostAllocationBinding extends LandedCostAllocationBindingExtension {}
    export interface LandedCostAllocationInput extends LandedCostAllocationInputExtension {}
}
declare module '@sage/xtrem-mrp-data-api-partial' {
    import type {
        MrpResultLineBindingExtension,
        MrpResultLineExtension,
        MrpResultLineInputExtension,
    } from '@sage/xtrem-supply-chain-api';
    export interface MrpResultLine extends MrpResultLineExtension {}
    export interface MrpResultLineBinding extends MrpResultLineBindingExtension {}
    export interface MrpResultLineInput extends MrpResultLineInputExtension {}
}
declare module '@sage/xtrem-sales-api-partial' {
    import type {
        SalesOrderBindingExtension,
        SalesOrderExtension,
        SalesOrderExtension$AsyncOperations,
        SalesOrderExtension$Mutations,
        SalesOrderExtension$Operations,
        SalesOrderInputExtension,
    } from '@sage/xtrem-supply-chain-api';
    export interface SalesOrder extends SalesOrderExtension {}
    export interface SalesOrderBinding extends SalesOrderBindingExtension {}
    export interface SalesOrderInput extends SalesOrderInputExtension {}
    export interface SalesOrder$Mutations extends SalesOrderExtension$Mutations {}
    export interface SalesOrder$AsyncOperations extends SalesOrderExtension$AsyncOperations {}
    export interface SalesOrder$Operations extends SalesOrderExtension$Operations {}
}
declare module '@sage/xtrem-system-api-partial' {
    import type {
        SiteBindingExtension,
        SiteExtension,
        SiteExtension$Lookups,
        SiteExtension$Operations,
        SiteInputExtension,
    } from '@sage/xtrem-supply-chain-api';
    export interface Site extends SiteExtension {}
    export interface SiteBinding extends SiteBindingExtension {}
    export interface SiteInput extends SiteInputExtension {}
    export interface Site$Lookups extends SiteExtension$Lookups {}
    export interface Site$Operations extends SiteExtension$Operations {}
}
