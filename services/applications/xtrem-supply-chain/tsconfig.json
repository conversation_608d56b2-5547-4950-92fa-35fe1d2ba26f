{"extends": "../../tsconfig-base.json", "compilerOptions": {"outDir": "build", "rootDir": ".", "baseUrl": ".", "target": "es2022", "module": "commonjs", "moduleResolution": "node"}, "include": ["index.ts", "application.ts", "lib/**/*.ts", "test/**/*.ts", "test/**/*.json", "api/api.d.ts"], "exclude": [".eslintrc*.cjs", "lib/pages/**/*", "lib/widgets/**/*", "lib/page-extensions/**/*", "lib/page-fragments/**/*", "lib/stickers/**/*", "lib/i18n/**/*", "**/*.feature", "**/*.png", "lib/client-functions/**/*"], "references": [{"path": "../../../platform/shared/xtrem-async-helper"}, {"path": "../../../platform/system/xtrem-authorization"}, {"path": "../../../platform/front-end/xtrem-client"}, {"path": "../../../platform/system/xtrem-communication"}, {"path": "../../../platform/back-end/xtrem-core"}, {"path": "../../../platform/shared/xtrem-date-time"}, {"path": "../../../platform/shared/xtrem-decimal"}, {"path": "../xtrem-distribution"}, {"path": "../../shared/xtrem-finance-data"}, {"path": "../../shared/xtrem-landed-cost"}, {"path": "../../../platform/system/xtrem-mailer"}, {"path": "../xtrem-manufacturing"}, {"path": "../../shared/xtrem-master-data"}, {"path": "../../shared/xtrem-mrp-data"}, {"path": "../xtrem-purchasing"}, {"path": "../../../platform/system/xtrem-reporting"}, {"path": "../xtrem-sales"}, {"path": "../../../platform/system/xtrem-scheduler"}, {"path": "../../../platform/shared/xtrem-shared"}, {"path": "../../shared/xtrem-stock-data"}, {"path": "../../shared/xtrem-structure"}, {"path": "../../../platform/system/xtrem-system"}, {"path": "../../shared/xtrem-technical-data"}, {"path": "../../../platform/front-end/xtrem-ui"}, {"path": "../../../platform/system/xtrem-upload"}, {"path": "../../../platform/back-end/eslint-plugin-xtrem"}, {"path": "../../../platform/cli/xtrem-cli"}, {"path": "../../../platform/back-end/xtrem-config"}, {"path": "../xtrem-distribution/api"}, {"path": "../../shared/xtrem-finance-data/api"}, {"path": "../../../platform/system/xtrem-mailer/api"}, {"path": "../xtrem-manufacturing/api"}, {"path": "../../shared/xtrem-master-data/api"}, {"path": "../../shared/xtrem-mrp-data/api"}, {"path": "../xtrem-purchasing/api"}, {"path": "../../../platform/system/xtrem-reporting/api"}, {"path": "../../../platform/system/xtrem-routing"}, {"path": "../xtrem-sales/api"}, {"path": "../../shared/xtrem-stock-data/api"}, {"path": "../../shared/xtrem-structure/api"}, {"path": "api"}, {"path": "../../../platform/system/xtrem-system/api"}]}