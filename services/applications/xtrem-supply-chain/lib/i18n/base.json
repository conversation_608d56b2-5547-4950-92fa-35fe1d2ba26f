{"@sage/xtrem-supply-chain/activity__stock_in_transit_inquiry__name": "Stock in transit inquiry", "@sage/xtrem-supply-chain/activity__stock_transfer_order__name": "Stock transfer order", "@sage/xtrem-supply-chain/activity__stock_transfer_receipt__name": "Stock transfer receipt", "@sage/xtrem-supply-chain/activity__stock_transfer_shipment__name": "Stock transfer shipment", "@sage/xtrem-supply-chain/activity__supply_planning__name": "Supply planning", "@sage/xtrem-supply-chain/cant_approve_stock_transfer_order_when_not_pending_approval_or_closed": "You can only approve a stock transfer order if the approval status is {{pendingApproval}}.", "@sage/xtrem-supply-chain/cant_reject_stock_transfer_order_when_not_pending_approval_or_closed": "You can only reject a stock transfer order if the approval status is {{pendingApproval}}.", "@sage/xtrem-supply-chain/data_types__stock_transfer_order_create_shipment_method_return_enum__name": "Stock transfer order create shipment method return enum", "@sage/xtrem-supply-chain/data_types__stock_transfer_order_display_status_enum__name": "Stock transfer order display status enum", "@sage/xtrem-supply-chain/data_types__stock_transfer_order_document_type_enum__name": "Stock transfer order document type enum", "@sage/xtrem-supply-chain/data_types__stock_transfer_order_flow_type_enum__name": "Stock transfer order flow type enum", "@sage/xtrem-supply-chain/data_types__stock_transfer_order_status_enum__name": "Stock transfer order status enum", "@sage/xtrem-supply-chain/data_types__stock_transfer_receipt__name": "Stock transfer receipt", "@sage/xtrem-supply-chain/data_types__stock_transfer_receipt_display_status_enum__name": "Stock transfer receipt display status enum", "@sage/xtrem-supply-chain/data_types__stock_transfer_receipt_status_enum__name": "Stock transfer receipt status enum", "@sage/xtrem-supply-chain/data_types__stock_transfer_shipment_display_status_enum__name": "Stock transfer shipment display status enum", "@sage/xtrem-supply-chain/data_types__stock_transfer_shipment_status_enum__name": "Stock transfer shipment status enum", "@sage/xtrem-supply-chain/data_types__supply_planning_source_enum__name": "Supply planning source enum", "@sage/xtrem-supply-chain/data_types__supply_planning_status_enum__name": "Supply planning status enum", "@sage/xtrem-supply-chain/data_types__supply_planning_type_enum__name": "Supply planning type enum", "@sage/xtrem-supply-chain/edit-create-line": "Add new line", "@sage/xtrem-supply-chain/enums__stock_transfer_order_create_shipment_method_return__isNotShipped": "Is not shipped", "@sage/xtrem-supply-chain/enums__stock_transfer_order_create_shipment_method_return__isPartiallyShipped": "Is partially shipped", "@sage/xtrem-supply-chain/enums__stock_transfer_order_create_shipment_method_return__isShipped": "Is shipped", "@sage/xtrem-supply-chain/enums__stock_transfer_order_create_shipment_method_return__parametersAreIncorrect": "Parameters are incorrect", "@sage/xtrem-supply-chain/enums__stock_transfer_order_display_status__approved": "Approved", "@sage/xtrem-supply-chain/enums__stock_transfer_order_display_status__changeRequested": "Change requested", "@sage/xtrem-supply-chain/enums__stock_transfer_order_display_status__closed": "Closed", "@sage/xtrem-supply-chain/enums__stock_transfer_order_display_status__confirmed": "Confirmed", "@sage/xtrem-supply-chain/enums__stock_transfer_order_display_status__draft": "Draft", "@sage/xtrem-supply-chain/enums__stock_transfer_order_display_status__error": "Error", "@sage/xtrem-supply-chain/enums__stock_transfer_order_display_status__partiallyReceived": "Partially received", "@sage/xtrem-supply-chain/enums__stock_transfer_order_display_status__partiallyShipped": "Partially shipped", "@sage/xtrem-supply-chain/enums__stock_transfer_order_display_status__pendingApproval": "Pending approval", "@sage/xtrem-supply-chain/enums__stock_transfer_order_display_status__received": "Received", "@sage/xtrem-supply-chain/enums__stock_transfer_order_display_status__rejected": "Rejected", "@sage/xtrem-supply-chain/enums__stock_transfer_order_display_status__shipped": "Shipped", "@sage/xtrem-supply-chain/enums__stock_transfer_order_document_type__company": "Company", "@sage/xtrem-supply-chain/enums__stock_transfer_order_document_type__interSite": "Inter site", "@sage/xtrem-supply-chain/enums__stock_transfer_order_document_type__services": "Services", "@sage/xtrem-supply-chain/enums__stock_transfer_order_flow_type__oneStep": "One step", "@sage/xtrem-supply-chain/enums__stock_transfer_order_flow_type__twoSteps": "Two steps", "@sage/xtrem-supply-chain/enums__stock_transfer_order_status__closed": "Closed", "@sage/xtrem-supply-chain/enums__stock_transfer_order_status__draft": "Draft", "@sage/xtrem-supply-chain/enums__stock_transfer_order_status__inProgress": "In progress", "@sage/xtrem-supply-chain/enums__stock_transfer_order_status__pending": "Pending", "@sage/xtrem-supply-chain/enums__stock_transfer_receipt_display_status__error": "Error", "@sage/xtrem-supply-chain/enums__stock_transfer_receipt_display_status__postingInProgress": "Posting in progress", "@sage/xtrem-supply-chain/enums__stock_transfer_receipt_display_status__readyToProcess": "Ready to process", "@sage/xtrem-supply-chain/enums__stock_transfer_receipt_display_status__received": "Received", "@sage/xtrem-supply-chain/enums__stock_transfer_receipt_status__readyToProcess": "Ready to process", "@sage/xtrem-supply-chain/enums__stock_transfer_receipt_status__received": "Received", "@sage/xtrem-supply-chain/enums__stock_transfer_shipment_display_status__error": "Error", "@sage/xtrem-supply-chain/enums__stock_transfer_shipment_display_status__postingInProgress": "Posting in progress", "@sage/xtrem-supply-chain/enums__stock_transfer_shipment_display_status__readyToProcess": "Ready to process", "@sage/xtrem-supply-chain/enums__stock_transfer_shipment_display_status__readyToShip": "Ready to ship", "@sage/xtrem-supply-chain/enums__stock_transfer_shipment_display_status__received": "Received", "@sage/xtrem-supply-chain/enums__stock_transfer_shipment_display_status__shipped": "Shipped", "@sage/xtrem-supply-chain/enums__stock_transfer_shipment_status__readyToProcess": "Ready to process", "@sage/xtrem-supply-chain/enums__stock_transfer_shipment_status__readyToShip": "Ready to ship", "@sage/xtrem-supply-chain/enums__stock_transfer_shipment_status__received": "Received", "@sage/xtrem-supply-chain/enums__stock_transfer_shipment_status__shipped": "Shipped", "@sage/xtrem-supply-chain/enums__supply_planning_source__MRP": "Mrp", "@sage/xtrem-supply-chain/enums__supply_planning_status__createOrder": "Create order", "@sage/xtrem-supply-chain/enums__supply_planning_status__pending": "Pending", "@sage/xtrem-supply-chain/enums__supply_planning_type__manufactured": "Manufactured", "@sage/xtrem-supply-chain/enums__supply_planning_type__purchased": "Purchased", "@sage/xtrem-supply-chain/functions__send__email__no_default_or_substitute_approver": "There is no default or substitute approver for the current document.", "@sage/xtrem-supply-chain/functions__send__email__not_in_submission_for_approval_not_allowed": "Submission for approval action not allowed. The stock transfer order is not in draft status.", "@sage/xtrem-supply-chain/functions__stock_transfer_order__approval_email_subject": "[Stock transfer order {{stockTransferOrderNumber}}] approval request", "@sage/xtrem-supply-chain/mailer_no_mailer_redirect_url_provided": "Mailer's redirect URL is missing in configuration file.", "@sage/xtrem-supply-chain/menu_item__stock-transfer": "Stock transfer", "@sage/xtrem-supply-chain/node__stock_transfer_receipt__resend_notification_for_finance": "Resending finance notification for stock transfer receipt: {{stockTransferReceiptNumber}}.", "@sage/xtrem-supply-chain/node__stock_transfer_shipment__resend_notification_for_finance": "Resending finance notification for stock transfer shipment: {{stockTransferShipmentNumber}}.", "@sage/xtrem-supply-chain/node-extensions__sales_order_extension__mutation__createBackToBackOrderFromSalesOrder": "Create back to back order from sales order", "@sage/xtrem-supply-chain/node-extensions__sales_order_extension__mutation__createBackToBackOrderFromSalesOrder__failed": "Create back to back order from sales order failed.", "@sage/xtrem-supply-chain/node-extensions__sales_order_extension__mutation__createBackToBackOrderFromSalesOrder__parameter__salesOrder": "Sales order", "@sage/xtrem-supply-chain/node-extensions__sales_order_extension__mutation__createBackToBackPurchaseOrder": "Create back to back purchase order", "@sage/xtrem-supply-chain/node-extensions__sales_order_extension__mutation__createBackToBackPurchaseOrder__failed": "Create back to back purchase order failed.", "@sage/xtrem-supply-chain/node-extensions__sales_order_extension__mutation__createBackToBackPurchaseOrder__parameter__data": "Data", "@sage/xtrem-supply-chain/node-extensions__sales_order_extension__mutation__createBackToBackWorkOrder": "Create back to back work order", "@sage/xtrem-supply-chain/node-extensions__sales_order_extension__mutation__createBackToBackWorkOrder__failed": "Create back to back work order failed.", "@sage/xtrem-supply-chain/node-extensions__sales_order_extension__mutation__createBackToBackWorkOrder__parameter__data": "Data", "@sage/xtrem-supply-chain/node-extensions__site_extension__property__isStockTransferOrderApprovalManaged": "Is stock transfer order approval managed", "@sage/xtrem-supply-chain/node-extensions__site_extension__property__stockTransferOrderDefaultApprover": "Stock transfer order default approver", "@sage/xtrem-supply-chain/node-extensions__site_extension__property__stockTransferOrderSubstituteApprover": "Stock transfer order substitute approver", "@sage/xtrem-supply-chain/nodes__landed_cost_allocation__financial_site": "The stock transfer receipt does not share the same financial site", "@sage/xtrem-supply-chain/nodes__landed_cost_allocation__status_not_received": "The status of stock transfer receipt lines allocated to the landed cost need to be Received.", "@sage/xtrem-supply-chain/nodes__landed_cost_allocation__stock_transaction_status_not_completed": "The stock status of all stock transfer receipt lines allocated to the landed cost need to be Completed.", "@sage/xtrem-supply-chain/nodes__mrp_result_line__cannot_delete_when_purchase_order_is_being_created__error": "You cannot delete this result set, purchase orders are being created.", "@sage/xtrem-supply-chain/nodes__site_extension__check_pending_stock_transfer_orders": "Pending stock transfer orders need to be handled before disabling the approval process", "@sage/xtrem-supply-chain/nodes__stock_transfer_in_transit_inquiry__asyncMutation__asyncExport": "Export", "@sage/xtrem-supply-chain/nodes__stock_transfer_in_transit_inquiry__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-supply-chain/nodes__stock_transfer_in_transit_inquiry__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-supply-chain/nodes__stock_transfer_in_transit_inquiry__node_name": "Stock transfer in transit inquiry", "@sage/xtrem-supply-chain/nodes__stock_transfer_in_transit_inquiry__property__lines": "Lines", "@sage/xtrem-supply-chain/nodes__stock_transfer_order__approved": "The stock transfer order is approved.", "@sage/xtrem-supply-chain/nodes__stock_transfer_order__asyncMutation__asyncExport": "Export", "@sage/xtrem-supply-chain/nodes__stock_transfer_order__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-supply-chain/nodes__stock_transfer_order__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-supply-chain/nodes__stock_transfer_order__cannot_approve_stock_transfer_order_mandatory_dimensions": " {{errorMessage}}", "@sage/xtrem-supply-chain/nodes__stock_transfer_order__cannot_close_allocated_request_in_progress": "You can only close the stock transfer order after the allocation request for the lines is complete.", "@sage/xtrem-supply-chain/nodes__stock_transfer_order__cannot_close_line___line_is_shipped": "You cannot close the stock transfer order line. It is already shipped.", "@sage/xtrem-supply-chain/nodes__stock_transfer_order__cannot_close_line_allocated_request_in_progress": "You cannot close the stock transfer order line. An allocation request is in progress.", "@sage/xtrem-supply-chain/nodes__stock_transfer_order__cannot_close_line_quantity_allocated": "Remove the stock allocation before closing the line.", "@sage/xtrem-supply-chain/nodes__stock_transfer_order__cannot_close_order_quantity_allocated": "Remove the stock allocation before closing the order.", "@sage/xtrem-supply-chain/nodes__stock_transfer_order__cannot_confirm_stock_transfer_order_mandatory_dimensions": " {{errorMessage}}", "@sage/xtrem-supply-chain/nodes__stock_transfer_order__header_receiving_site_not_updatable": "You cannot change the receiving site for the stock transfer order.", "@sage/xtrem-supply-chain/nodes__stock_transfer_order__header_stock_site_not_updatable": "The stock site of the stock transfer order must not be changed.", "@sage/xtrem-supply-chain/nodes__stock_transfer_order__improper_shipping_date": "The shipping date needs to be later than the do not ship before date and needs to be earlier than the do not ship after date.", "@sage/xtrem-supply-chain/nodes__stock_transfer_order__invalid_selected_lines__allocation_request_status": "You can only ship the stock transfer order line after the allocation request is complete.", "@sage/xtrem-supply-chain/nodes__stock_transfer_order__invalid_selected_lines__shipping_status": "The stock transfer order line is already shipped.", "@sage/xtrem-supply-chain/nodes__stock_transfer_order__invalid_selected_lines_status": "The status for the selected lines cannot be Draft.", "@sage/xtrem-supply-chain/nodes__stock_transfer_order__line_closed_quote_exception": "The line status cannot be \"Draft\" or \"Closed\".", "@sage/xtrem-supply-chain/nodes__stock_transfer_order__mutation__addWorkDays": "Add work days", "@sage/xtrem-supply-chain/nodes__stock_transfer_order__mutation__addWorkDays__failed": "Add work days failed.", "@sage/xtrem-supply-chain/nodes__stock_transfer_order__mutation__addWorkDays__parameter__deliveryLeadTime": "Delivery lead time", "@sage/xtrem-supply-chain/nodes__stock_transfer_order__mutation__addWorkDays__parameter__shippingDate": "Shipping date", "@sage/xtrem-supply-chain/nodes__stock_transfer_order__mutation__addWorkDays__parameter__workDays": "Work days", "@sage/xtrem-supply-chain/nodes__stock_transfer_order__mutation__approve": "Approve", "@sage/xtrem-supply-chain/nodes__stock_transfer_order__mutation__approve__failed": "Approve failed.", "@sage/xtrem-supply-chain/nodes__stock_transfer_order__mutation__approve__parameter__stockTransferOrder": "Stock transfer order", "@sage/xtrem-supply-chain/nodes__stock_transfer_order__mutation__close": "Close", "@sage/xtrem-supply-chain/nodes__stock_transfer_order__mutation__close__failed": "Close failed.", "@sage/xtrem-supply-chain/nodes__stock_transfer_order__mutation__close__parameter__stockTransferOrder": "Stock transfer order", "@sage/xtrem-supply-chain/nodes__stock_transfer_order__mutation__closeLine": "Close line", "@sage/xtrem-supply-chain/nodes__stock_transfer_order__mutation__closeLine__failed": "Close line failed.", "@sage/xtrem-supply-chain/nodes__stock_transfer_order__mutation__closeLine__parameter__stockTransferOrderLine": "Stock transfer order line", "@sage/xtrem-supply-chain/nodes__stock_transfer_order__mutation__confirm": "Confirm", "@sage/xtrem-supply-chain/nodes__stock_transfer_order__mutation__confirm__failed": "Confirm failed.", "@sage/xtrem-supply-chain/nodes__stock_transfer_order__mutation__confirm__parameter__stockTransferOrder": "Stock transfer order", "@sage/xtrem-supply-chain/nodes__stock_transfer_order__mutation__createStockTransferShipment": "Create stock transfer shipment", "@sage/xtrem-supply-chain/nodes__stock_transfer_order__mutation__createStockTransferShipment__failed": "Create stock transfer shipment failed.", "@sage/xtrem-supply-chain/nodes__stock_transfer_order__mutation__createStockTransferShipment__parameter__stockTransferOrder": "Stock transfer order", "@sage/xtrem-supply-chain/nodes__stock_transfer_order__mutation__financeIntegrationCheck": "Finance integration check", "@sage/xtrem-supply-chain/nodes__stock_transfer_order__mutation__financeIntegrationCheck__failed": "Finance integration check failed.", "@sage/xtrem-supply-chain/nodes__stock_transfer_order__mutation__financeIntegrationCheck__parameter__stockTransferOrder": "Stock transfer order", "@sage/xtrem-supply-chain/nodes__stock_transfer_order__mutation__open": "Open", "@sage/xtrem-supply-chain/nodes__stock_transfer_order__mutation__open__failed": "Open failed.", "@sage/xtrem-supply-chain/nodes__stock_transfer_order__mutation__open__parameter__stockTransferOrder": "Stock transfer order", "@sage/xtrem-supply-chain/nodes__stock_transfer_order__mutation__openLine": "Open line", "@sage/xtrem-supply-chain/nodes__stock_transfer_order__mutation__openLine__failed": "Open line failed.", "@sage/xtrem-supply-chain/nodes__stock_transfer_order__mutation__openLine__parameter__stockTransferOrderLine": "Stock transfer order line", "@sage/xtrem-supply-chain/nodes__stock_transfer_order__mutation__reject": "Reject", "@sage/xtrem-supply-chain/nodes__stock_transfer_order__mutation__reject__failed": "Reject failed.", "@sage/xtrem-supply-chain/nodes__stock_transfer_order__mutation__reject__parameter__stockTransferOrder": "Stock transfer order", "@sage/xtrem-supply-chain/nodes__stock_transfer_order__mutation__requestAutoAllocation": "Request auto allocation", "@sage/xtrem-supply-chain/nodes__stock_transfer_order__mutation__requestAutoAllocation__failed": "Request auto allocation failed.", "@sage/xtrem-supply-chain/nodes__stock_transfer_order__mutation__requestAutoAllocation__parameter__data": "Data", "@sage/xtrem-supply-chain/nodes__stock_transfer_order__mutation__setIsPrintedTrue": "Set is printed true", "@sage/xtrem-supply-chain/nodes__stock_transfer_order__mutation__setIsPrintedTrue__failed": "Set is printed true failed.", "@sage/xtrem-supply-chain/nodes__stock_transfer_order__mutation__setIsPrintedTrue__parameter__isPrinted": "Is printed", "@sage/xtrem-supply-chain/nodes__stock_transfer_order__mutation__setIsPrintedTrue__parameter__order": "Order", "@sage/xtrem-supply-chain/nodes__stock_transfer_order__mutation__subWorkDays": "Sub work days", "@sage/xtrem-supply-chain/nodes__stock_transfer_order__mutation__subWorkDays__failed": "Sub work days failed.", "@sage/xtrem-supply-chain/nodes__stock_transfer_order__mutation__subWorkDays__parameter__deliveryLeadTime": "Delivery lead time", "@sage/xtrem-supply-chain/nodes__stock_transfer_order__mutation__subWorkDays__parameter__doNotShipAfterDate": "Do not ship after date", "@sage/xtrem-supply-chain/nodes__stock_transfer_order__mutation__subWorkDays__parameter__doNotShipBeforeDate": "Do not ship before date", "@sage/xtrem-supply-chain/nodes__stock_transfer_order__mutation__subWorkDays__parameter__orderDate": "Order date", "@sage/xtrem-supply-chain/nodes__stock_transfer_order__mutation__subWorkDays__parameter__requestedDeliveryDate": "Requested delivery date", "@sage/xtrem-supply-chain/nodes__stock_transfer_order__mutation__subWorkDays__parameter__workDaysMask": "Work days mask", "@sage/xtrem-supply-chain/nodes__stock_transfer_order__node_name": "Stock transfer order", "@sage/xtrem-supply-chain/nodes__stock_transfer_order__property__allocationRequestStatus": "Allocation request status", "@sage/xtrem-supply-chain/nodes__stock_transfer_order__property__allocationStatus": "Allocation status", "@sage/xtrem-supply-chain/nodes__stock_transfer_order__property__businessRelation": "Business relation", "@sage/xtrem-supply-chain/nodes__stock_transfer_order__property__currency": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-supply-chain/nodes__stock_transfer_order__property__displayStatus": "Display status", "@sage/xtrem-supply-chain/nodes__stock_transfer_order__property__financialSite": "Financial site", "@sage/xtrem-supply-chain/nodes__stock_transfer_order__property__fxRateDate": "Fx rate date", "@sage/xtrem-supply-chain/nodes__stock_transfer_order__property__incoterm": "Incoterm", "@sage/xtrem-supply-chain/nodes__stock_transfer_order__property__isCloseHidden": "Is close hidden", "@sage/xtrem-supply-chain/nodes__stock_transfer_order__property__jsonAggregateLandedCostTypes": "Json aggregate landed cost types", "@sage/xtrem-supply-chain/nodes__stock_transfer_order__property__lines": "Lines", "@sage/xtrem-supply-chain/nodes__stock_transfer_order__property__page": "Page", "@sage/xtrem-supply-chain/nodes__stock_transfer_order__property__receivingSite": "Receiving site", "@sage/xtrem-supply-chain/nodes__stock_transfer_order__property__receivingStatus": "Receiving status", "@sage/xtrem-supply-chain/nodes__stock_transfer_order__property__requester": "Requester", "@sage/xtrem-supply-chain/nodes__stock_transfer_order__property__shippingDate": "Shipping date", "@sage/xtrem-supply-chain/nodes__stock_transfer_order__property__shipToCustomerAddress": "Ship to customer address", "@sage/xtrem-supply-chain/nodes__stock_transfer_order__property__shipToLinkedAddress": "Ship to linked address", "@sage/xtrem-supply-chain/nodes__stock_transfer_order__property__site": "Site", "@sage/xtrem-supply-chain/nodes__stock_transfer_order__property__status": "Status", "@sage/xtrem-supply-chain/nodes__stock_transfer_order__property__stockTransferOrderDocumentType": "Stock transfer order document type", "@sage/xtrem-supply-chain/nodes__stock_transfer_order__property__stockTransferOrderFlowType": "Stock transfer order flow type", "@sage/xtrem-supply-chain/nodes__stock_transfer_order__property__supplier": "Supplier", "@sage/xtrem-supply-chain/nodes__stock_transfer_order__query__getFilteredUsers": "Get filtered users", "@sage/xtrem-supply-chain/nodes__stock_transfer_order__query__getFilteredUsers__failed": "Get filtered users failed.", "@sage/xtrem-supply-chain/nodes__stock_transfer_order__query__getFilteredUsers__parameter__criteria": "Criteria", "@sage/xtrem-supply-chain/nodes__stock_transfer_order__rejected": "The stock transfer order has been rejected.", "@sage/xtrem-supply-chain/nodes__stock_transfer_order_line__asyncMutation__asyncExport": "Export", "@sage/xtrem-supply-chain/nodes__stock_transfer_order_line__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-supply-chain/nodes__stock_transfer_order_line__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-supply-chain/nodes__stock_transfer_order_line__improper_status_during_creation": "The status for the stock transfer order line needs to be \"Draft\" or \"Pending\".", "@sage/xtrem-supply-chain/nodes__stock_transfer_order_line__mandatory_dimension_not_found": "You need to select the company dimension: {{dimension}}.", "@sage/xtrem-supply-chain/nodes__stock_transfer_order_line__node_name": "Stock transfer order line", "@sage/xtrem-supply-chain/nodes__stock_transfer_order_line__property__actualLandedCostInCompanyCurrency": "Actual landed cost in company currency", "@sage/xtrem-supply-chain/nodes__stock_transfer_order_line__property__computedAttributes": "Computed attributes", "@sage/xtrem-supply-chain/nodes__stock_transfer_order_line__property__dimensionDefinitionLevel": "Dimension definition level", "@sage/xtrem-supply-chain/nodes__stock_transfer_order_line__property__document": "Document", "@sage/xtrem-supply-chain/nodes__stock_transfer_order_line__property__expectedDeliveryDate": "Expected delivery date", "@sage/xtrem-supply-chain/nodes__stock_transfer_order_line__property__item": "<PERSON><PERSON>", "@sage/xtrem-supply-chain/nodes__stock_transfer_order_line__property__linkToStockTransferShipmentLines": "Link to stock transfer shipment lines", "@sage/xtrem-supply-chain/nodes__stock_transfer_order_line__property__quantity": "Quantity", "@sage/xtrem-supply-chain/nodes__stock_transfer_order_line__property__quantityInStockUnit": "Quantity in stock unit", "@sage/xtrem-supply-chain/nodes__stock_transfer_order_line__property__quantityToShipInProgressInStockUnit": "Quantity to ship in progress in stock unit", "@sage/xtrem-supply-chain/nodes__stock_transfer_order_line__property__receivingSite": "Receiving site", "@sage/xtrem-supply-chain/nodes__stock_transfer_order_line__property__receivingStatus": "Receiving status", "@sage/xtrem-supply-chain/nodes__stock_transfer_order_line__property__remainingQuantityToShipInStockUnit": "Remaining quantity to ship in stock unit", "@sage/xtrem-supply-chain/nodes__stock_transfer_order_line__property__shippedQuantityInStockUnit": "Shipped quantity in stock unit", "@sage/xtrem-supply-chain/nodes__stock_transfer_order_line__property__shippingDate": "Shipping date", "@sage/xtrem-supply-chain/nodes__stock_transfer_order_line__property__status": "Status", "@sage/xtrem-supply-chain/nodes__stock_transfer_order_line__property__stockCostAmountInCompanyCurrency": "Stock cost amount in company currency", "@sage/xtrem-supply-chain/nodes__stock_transfer_order_line__property__stockTransferReceiptLine": "Stock transfer receipt line", "@sage/xtrem-supply-chain/nodes__stock_transfer_order_line__property__storedAttributes": "Stored attributes", "@sage/xtrem-supply-chain/nodes__stock_transfer_order_line__property__storedDimensions": "Stored dimensions", "@sage/xtrem-supply-chain/nodes__stock_transfer_order_line__property__totalQuantityInStockUnitInAllLinkedShipments": "Total quantity in stock unit in all linked shipments", "@sage/xtrem-supply-chain/nodes__stock_transfer_order_line__property__unit": "Unit", "@sage/xtrem-supply-chain/nodes__stock_transfer_order_line__property__unitToStockUnitConversionFactor": "Unit to stock unit conversion factor", "@sage/xtrem-supply-chain/nodes__stock_transfer_order_line__property__workInProgress": "Work in progress", "@sage/xtrem-supply-chain/nodes__stock_transfer_order_line__stock_transfer_order_lines_control_shippingdate_with_doNotShipAfterDate": "The shipping date needs to be before the Do not ship after date.", "@sage/xtrem-supply-chain/nodes__stock_transfer_order_line__stock_transfer_order_lines_control_shippingdate_with_doNotShipBeforeDate": "The shipping date needs to be after the Do not ship before date.", "@sage/xtrem-supply-chain/nodes__stock_transfer_order_line__stock_transfer_order_lines_control_shippingdate_with_orderdate": "The shipping date needs to be after the order date.", "@sage/xtrem-supply-chain/nodes__stock_transfer_order_line__too_much_quantity_allocated,": "The allocated quantity on the stock transfer order line cannot be larger than the remaining quantity to ship.", "@sage/xtrem-supply-chain/nodes__stock_transfer_receipt__asyncMutation__asyncExport": "Export", "@sage/xtrem-supply-chain/nodes__stock_transfer_receipt__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-supply-chain/nodes__stock_transfer_receipt__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-supply-chain/nodes__stock_transfer_receipt__cant_repost_stock_transfer_receipt_when_status_is_not_failed": "You can only repost a stock transfer receipt if the status is Failed or Not recorded.", "@sage/xtrem-supply-chain/nodes__stock_transfer_receipt__deletion_not_allowed": "You cannot delete this stock transfer receipt.", "@sage/xtrem-supply-chain/nodes__stock_transfer_receipt__document_was_posted": "The stock transfer receipt was posted.", "@sage/xtrem-supply-chain/nodes__stock_transfer_receipt__mutation__repost": "Repost", "@sage/xtrem-supply-chain/nodes__stock_transfer_receipt__mutation__repost__failed": "Repost failed.", "@sage/xtrem-supply-chain/nodes__stock_transfer_receipt__mutation__repost__parameter__documentLines": "Document lines", "@sage/xtrem-supply-chain/nodes__stock_transfer_receipt__mutation__repost__parameter__stockTransferReceipt": "Stock transfer receipt", "@sage/xtrem-supply-chain/nodes__stock_transfer_receipt__mutation__resendNotificationForFinance": "Resend notification for finance", "@sage/xtrem-supply-chain/nodes__stock_transfer_receipt__mutation__resendNotificationForFinance__failed": "Resend notification for finance failed.", "@sage/xtrem-supply-chain/nodes__stock_transfer_receipt__mutation__resendNotificationForFinance__parameter__stockTransferReceipt": "Stock transfer receipt", "@sage/xtrem-supply-chain/nodes__stock_transfer_receipt__node_name": "Stock transfer receipt", "@sage/xtrem-supply-chain/nodes__stock_transfer_receipt__property__displayStatus": "Display status", "@sage/xtrem-supply-chain/nodes__stock_transfer_receipt__property__documentDate": "Document date", "@sage/xtrem-supply-chain/nodes__stock_transfer_receipt__property__jsonAggregateLandedCostTypes": "Json aggregate landed cost types", "@sage/xtrem-supply-chain/nodes__stock_transfer_receipt__property__lines": "Lines", "@sage/xtrem-supply-chain/nodes__stock_transfer_receipt__property__postingDetails": "Posting details", "@sage/xtrem-supply-chain/nodes__stock_transfer_receipt__property__shippingSite": "Shipping site", "@sage/xtrem-supply-chain/nodes__stock_transfer_receipt__property__site": "Site", "@sage/xtrem-supply-chain/nodes__stock_transfer_receipt__property__status": "Status", "@sage/xtrem-supply-chain/nodes__stock_transfer_receipt__property__stockSite": "Stock site", "@sage/xtrem-supply-chain/nodes__stock_transfer_receipt__property__supplier": "Supplier", "@sage/xtrem-supply-chain/nodes__stock_transfer_receipt_line__asyncMutation__asyncExport": "Export", "@sage/xtrem-supply-chain/nodes__stock_transfer_receipt_line__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-supply-chain/nodes__stock_transfer_receipt_line__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-supply-chain/nodes__stock_transfer_receipt_line__node_name": "Stock transfer receipt line", "@sage/xtrem-supply-chain/nodes__stock_transfer_receipt_line__property__actualLandedCostInCompanyCurrency": "Actual landed cost in company currency", "@sage/xtrem-supply-chain/nodes__stock_transfer_receipt_line__property__canHaveLandedCostLine": "Can have landed cost line", "@sage/xtrem-supply-chain/nodes__stock_transfer_receipt_line__property__computedAttributes": "Computed attributes", "@sage/xtrem-supply-chain/nodes__stock_transfer_receipt_line__property__document": "Document", "@sage/xtrem-supply-chain/nodes__stock_transfer_receipt_line__property__jsonStockDetails": "Json stock details", "@sage/xtrem-supply-chain/nodes__stock_transfer_receipt_line__property__linkToStockTransferShipmentLines": "Link to stock transfer shipment lines", "@sage/xtrem-supply-chain/nodes__stock_transfer_receipt_line__property__quantityInStockUnit": "Quantity in stock unit", "@sage/xtrem-supply-chain/nodes__stock_transfer_receipt_line__property__shipmentLine": "Shipment line", "@sage/xtrem-supply-chain/nodes__stock_transfer_receipt_line__property__status": "Status", "@sage/xtrem-supply-chain/nodes__stock_transfer_receipt_line__property__stockDetails": "Stock details", "@sage/xtrem-supply-chain/nodes__stock_transfer_receipt_line__property__stockDetailStatus": "Stock detail status", "@sage/xtrem-supply-chain/nodes__stock_transfer_receipt_line__property__stockMovements": "Stock movements", "@sage/xtrem-supply-chain/nodes__stock_transfer_receipt_line__property__stockTransactions": "Stock transactions", "@sage/xtrem-supply-chain/nodes__stock_transfer_receipt_line__property__unit": "Unit", "@sage/xtrem-supply-chain/nodes__stock_transfer_receipt_line__property__unitToStockUnitConversionFactor": "Unit to stock unit conversion factor", "@sage/xtrem-supply-chain/nodes__stock_transfer_receipt_line__property__workInProgress": "Work in progress", "@sage/xtrem-supply-chain/nodes__stock_transfer_receipt_line_to_stock_transfer_shipment_line__asyncMutation__asyncExport": "Export", "@sage/xtrem-supply-chain/nodes__stock_transfer_receipt_line_to_stock_transfer_shipment_line__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-supply-chain/nodes__stock_transfer_receipt_line_to_stock_transfer_shipment_line__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-supply-chain/nodes__stock_transfer_receipt_line_to_stock_transfer_shipment_line__node_name": "Stock transfer receipt line to stock transfer shipment line", "@sage/xtrem-supply-chain/nodes__stock_transfer_receipt_line_to_stock_transfer_shipment_line__property__from": "From", "@sage/xtrem-supply-chain/nodes__stock_transfer_receipt_line_to_stock_transfer_shipment_line__property__to": "To", "@sage/xtrem-supply-chain/nodes__stock_transfer_shipment__asyncMutation__asyncExport": "Export", "@sage/xtrem-supply-chain/nodes__stock_transfer_shipment__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-supply-chain/nodes__stock_transfer_shipment__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-supply-chain/nodes__stock_transfer_shipment__bulk_print_pick_list_report_name": "Stock transfer shipment pick list", "@sage/xtrem-supply-chain/nodes__stock_transfer_shipment__bulk_print_report_name": "Stock transfer shipment packing slip", "@sage/xtrem-supply-chain/nodes__stock_transfer_shipment__bulkMutation__printBulkStockTransferShipmentPackingSlip": "Print bulk stock transfer shipment packing slip", "@sage/xtrem-supply-chain/nodes__stock_transfer_shipment__bulkMutation__printBulkStockTransferShipmentPackingSlip__failed": "Print bulk stock transfer shipment packing slip failed.", "@sage/xtrem-supply-chain/nodes__stock_transfer_shipment__bulkMutation__printBulkStockTransferShipmentPickList": "Print bulk stock transfer shipment pick list", "@sage/xtrem-supply-chain/nodes__stock_transfer_shipment__bulkMutation__printBulkStockTransferShipmentPickList__failed": "Print bulk stock transfer shipment pick list failed.", "@sage/xtrem-supply-chain/nodes__stock_transfer_shipment__cant_print_packing_slip_when_status_is_ready_to_process": "To print the packing slip, the stock transfer shipment cannot be Ready to process: {{number}}.", "@sage/xtrem-supply-chain/nodes__stock_transfer_shipment__cant_print_pick_list_when_status_is_not_ready_to_process_bulk": "To print the pick list, the stock transfer shipment needs to be Ready to process: {{number}}.", "@sage/xtrem-supply-chain/nodes__stock_transfer_shipment__confirm_ready_to_process": "You can only confirm a shipment if the status is 'Ready to process'.", "@sage/xtrem-supply-chain/nodes__stock_transfer_shipment__confirm_unallocated lines": "You need to allocate stock in full to all shipment lines before you can confirm the shipment.", "@sage/xtrem-supply-chain/nodes__stock_transfer_shipment__financial_site_not_matching": "The financial site for the receiving site needs to be the same as the financial site for the shipping site.", "@sage/xtrem-supply-chain/nodes__stock_transfer_shipment__mutation__afterPrintStockTransferPackingSlip": "After print stock transfer packing slip", "@sage/xtrem-supply-chain/nodes__stock_transfer_shipment__mutation__afterPrintStockTransferPackingSlip__failed": "After print stock transfer packing slip failed.", "@sage/xtrem-supply-chain/nodes__stock_transfer_shipment__mutation__afterPrintStockTransferPackingSlip__parameter__stockTransferShipment": "Stock transfer shipment", "@sage/xtrem-supply-chain/nodes__stock_transfer_shipment__mutation__beforePrintStockTransferPackingSlip": "Before print stock transfer packing slip", "@sage/xtrem-supply-chain/nodes__stock_transfer_shipment__mutation__beforePrintStockTransferPackingSlip__failed": "Before print stock transfer packing slip failed.", "@sage/xtrem-supply-chain/nodes__stock_transfer_shipment__mutation__beforePrintStockTransferPackingSlip__parameter__stockTransferShipment": "Stock transfer shipment", "@sage/xtrem-supply-chain/nodes__stock_transfer_shipment__mutation__beforePrintStockTransferShipmentPickList": "Before print stock transfer shipment pick list", "@sage/xtrem-supply-chain/nodes__stock_transfer_shipment__mutation__beforePrintStockTransferShipmentPickList__failed": "Before print stock transfer shipment pick list failed.", "@sage/xtrem-supply-chain/nodes__stock_transfer_shipment__mutation__beforePrintStockTransferShipmentPickList__parameter__stockTransferShipment": "Stock transfer shipment", "@sage/xtrem-supply-chain/nodes__stock_transfer_shipment__mutation__confirm": "Confirm", "@sage/xtrem-supply-chain/nodes__stock_transfer_shipment__mutation__confirm__failed": "Confirm failed.", "@sage/xtrem-supply-chain/nodes__stock_transfer_shipment__mutation__confirm__parameter__stockTransferShipmentNumber": "Stock transfer shipment number", "@sage/xtrem-supply-chain/nodes__stock_transfer_shipment__mutation__createReceiptFromShipment": "Create receipt from shipment", "@sage/xtrem-supply-chain/nodes__stock_transfer_shipment__mutation__createReceiptFromShipment__failed": "Create receipt from shipment failed.", "@sage/xtrem-supply-chain/nodes__stock_transfer_shipment__mutation__createReceiptFromShipment__parameter__stockTransferShipment": "Stock transfer shipment", "@sage/xtrem-supply-chain/nodes__stock_transfer_shipment__mutation__postToStock": "Post to stock", "@sage/xtrem-supply-chain/nodes__stock_transfer_shipment__mutation__postToStock__failed": "Post to stock failed.", "@sage/xtrem-supply-chain/nodes__stock_transfer_shipment__mutation__postToStock__parameter__documentIds": "Document ids", "@sage/xtrem-supply-chain/nodes__stock_transfer_shipment__mutation__repost": "Repost", "@sage/xtrem-supply-chain/nodes__stock_transfer_shipment__mutation__repost__failed": "Repost failed.", "@sage/xtrem-supply-chain/nodes__stock_transfer_shipment__mutation__repost__parameter__documentLines": "Document lines", "@sage/xtrem-supply-chain/nodes__stock_transfer_shipment__mutation__repost__parameter__stockTransferShipment": "Stock transfer shipment", "@sage/xtrem-supply-chain/nodes__stock_transfer_shipment__mutation__resendNotificationForFinance": "Resend notification for finance", "@sage/xtrem-supply-chain/nodes__stock_transfer_shipment__mutation__resendNotificationForFinance__failed": "Resend notification for finance failed.", "@sage/xtrem-supply-chain/nodes__stock_transfer_shipment__mutation__resendNotificationForFinance__parameter__stockTransferShipment": "Stock transfer shipment", "@sage/xtrem-supply-chain/nodes__stock_transfer_shipment__mutation__revoke": "Revoke", "@sage/xtrem-supply-chain/nodes__stock_transfer_shipment__mutation__revoke__failed": "Revoke failed.", "@sage/xtrem-supply-chain/nodes__stock_transfer_shipment__mutation__revoke__parameter__stockTransferShipmentNumber": "Stock transfer shipment number", "@sage/xtrem-supply-chain/nodes__stock_transfer_shipment__node_name": "Stock transfer shipment", "@sage/xtrem-supply-chain/nodes__stock_transfer_shipment__property__businessRelation": "Business relation", "@sage/xtrem-supply-chain/nodes__stock_transfer_shipment__property__displayStatus": "Display status", "@sage/xtrem-supply-chain/nodes__stock_transfer_shipment__property__documentDate": "Document date", "@sage/xtrem-supply-chain/nodes__stock_transfer_shipment__property__financialSite": "Financial site", "@sage/xtrem-supply-chain/nodes__stock_transfer_shipment__property__isPrinted": "Is printed", "@sage/xtrem-supply-chain/nodes__stock_transfer_shipment__property__lines": "Lines", "@sage/xtrem-supply-chain/nodes__stock_transfer_shipment__property__postingDetails": "Posting details", "@sage/xtrem-supply-chain/nodes__stock_transfer_shipment__property__receivingSite": "Receiving site", "@sage/xtrem-supply-chain/nodes__stock_transfer_shipment__property__receivingStatus": "Receiving status", "@sage/xtrem-supply-chain/nodes__stock_transfer_shipment__property__site": "Site", "@sage/xtrem-supply-chain/nodes__stock_transfer_shipment__property__status": "Status", "@sage/xtrem-supply-chain/nodes__stock_transfer_shipment__property__supplier": "Supplier", "@sage/xtrem-supply-chain/nodes__stock_transfer_shipment__revoked_ready_to_ship": "You can only revert a shipment if the status is 'Ready to ship'.", "@sage/xtrem-supply-chain/nodes__stock_transfer_shipment__revoked_success": "The shipment status was reverted. Its status is 'Ready to process'.", "@sage/xtrem-supply-chain/nodes__stock_transfer_shipment__same_sites": "The shipping site needs to be different from the receiving site.", "@sage/xtrem-supply-chain/nodes__stock_transfer_shipment__stock_transfer_receipt__notification__title": "Stock transfer receipt", "@sage/xtrem-supply-chain/nodes__stock_transfer_shipment__stock_transfer_receipt_created_successfully": "Stock transfer receipt created successfully.", "@sage/xtrem-supply-chain/nodes__stock_transfer_shipment_line__asyncMutation__asyncExport": "Export", "@sage/xtrem-supply-chain/nodes__stock_transfer_shipment_line__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-supply-chain/nodes__stock_transfer_shipment_line__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-supply-chain/nodes__stock_transfer_shipment_line__creation_forbidden_improper_status": "The stock transfer shipment is ready to ship or has already been shipped. You cannot add a new line.", "@sage/xtrem-supply-chain/nodes__stock_transfer_shipment_line__deletion_is_not_allowed_line_is_ready_to_ship_or_has_already_been_shipped": "The line is ready to ship or has already been shipped. You cannot delete the line.", "@sage/xtrem-supply-chain/nodes__stock_transfer_shipment_line__line_status_must_be_ready_to_process": "The line status needs to be Ready to process.", "@sage/xtrem-supply-chain/nodes__stock_transfer_shipment_line__node_name": "Stock transfer shipment line", "@sage/xtrem-supply-chain/nodes__stock_transfer_shipment_line__property__computedAttributes": "Computed attributes", "@sage/xtrem-supply-chain/nodes__stock_transfer_shipment_line__property__document": "Document", "@sage/xtrem-supply-chain/nodes__stock_transfer_shipment_line__property__inTransit": "In transit", "@sage/xtrem-supply-chain/nodes__stock_transfer_shipment_line__property__item": "<PERSON><PERSON>", "@sage/xtrem-supply-chain/nodes__stock_transfer_shipment_line__property__linkToStockTransferOrderLines": "Link to stock transfer order lines", "@sage/xtrem-supply-chain/nodes__stock_transfer_shipment_line__property__linkToStockTransferReceiptLines": "Link to stock transfer receipt lines", "@sage/xtrem-supply-chain/nodes__stock_transfer_shipment_line__property__quantityInStockUnit": "Quantity in stock unit", "@sage/xtrem-supply-chain/nodes__stock_transfer_shipment_line__property__receivingStatus": "Receiving status", "@sage/xtrem-supply-chain/nodes__stock_transfer_shipment_line__property__site": "Site", "@sage/xtrem-supply-chain/nodes__stock_transfer_shipment_line__property__status": "Status", "@sage/xtrem-supply-chain/nodes__stock_transfer_shipment_line__property__stockDetails": "Stock details", "@sage/xtrem-supply-chain/nodes__stock_transfer_shipment_line__property__stockSite": "Stock site", "@sage/xtrem-supply-chain/nodes__stock_transfer_shipment_line__property__unit": "Unit", "@sage/xtrem-supply-chain/nodes__stock_transfer_shipment_line__property__unitToStockUnitConversionFactor": "Unit to stock unit conversion factor", "@sage/xtrem-supply-chain/nodes__stock_transfer_shipment_line__site_must_have_the_same_value": "The line site needs to be the same as the transfer site.", "@sage/xtrem-supply-chain/nodes__stock_transfer_shipment_line_in_transit__asyncMutation__asyncExport": "Export", "@sage/xtrem-supply-chain/nodes__stock_transfer_shipment_line_in_transit__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-supply-chain/nodes__stock_transfer_shipment_line_in_transit__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-supply-chain/nodes__stock_transfer_shipment_line_in_transit__node_name": "Stock transfer shipment line in transit", "@sage/xtrem-supply-chain/nodes__stock_transfer_shipment_line_in_transit__property__commodityCode": "Commodity code", "@sage/xtrem-supply-chain/nodes__stock_transfer_shipment_line_in_transit__property__costType": "Cost type", "@sage/xtrem-supply-chain/nodes__stock_transfer_shipment_line_in_transit__property__currency": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-supply-chain/nodes__stock_transfer_shipment_line_in_transit__property__endDate": "End date", "@sage/xtrem-supply-chain/nodes__stock_transfer_shipment_line_in_transit__property__line": "Line", "@sage/xtrem-supply-chain/nodes__stock_transfer_shipment_line_in_transit__property__quantityInStockUnit": "Quantity in stock unit", "@sage/xtrem-supply-chain/nodes__stock_transfer_shipment_line_in_transit__property__startDate": "Start date", "@sage/xtrem-supply-chain/nodes__stock_transfer_shipment_line_in_transit__property__stockValue": "Stock value", "@sage/xtrem-supply-chain/nodes__stock_transfer_shipment_line_in_transit__property__unitCost": "Unit cost", "@sage/xtrem-supply-chain/nodes__stock_transfer_shipment_line_to_stock_transfer_order_line__asyncMutation__asyncExport": "Export", "@sage/xtrem-supply-chain/nodes__stock_transfer_shipment_line_to_stock_transfer_order_line__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-supply-chain/nodes__stock_transfer_shipment_line_to_stock_transfer_order_line__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-supply-chain/nodes__stock_transfer_shipment_line_to_stock_transfer_order_line__node_name": "Stock transfer shipment line to stock transfer order line", "@sage/xtrem-supply-chain/nodes__stock_transfer_shipment_line_to_stock_transfer_order_line__property__from": "From", "@sage/xtrem-supply-chain/nodes__stock_transfer_shipment_line_to_stock_transfer_order_line__property__to": "To", "@sage/xtrem-supply-chain/nodes__stock-transfer-shipment__cant_repost_stock_transfer_shipment_when_status_is_not_failed": "You can only repost a stock transfer shipment if the status is Failed or Not recorded.", "@sage/xtrem-supply-chain/nodes__stock-transfer-shipment__document_was_posted": "The stock transfer shipment was posted.", "@sage/xtrem-supply-chain/nodes__supply_planning__already_used_work_order_number": "This work order number has been used. Enter a different number.", "@sage/xtrem-supply-chain/nodes__supply_planning__asyncMutation__asyncExport": "Export", "@sage/xtrem-supply-chain/nodes__supply_planning__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-supply-chain/nodes__supply_planning__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-supply-chain/nodes__supply_planning__bulkMutation__createOrder": "Create order", "@sage/xtrem-supply-chain/nodes__supply_planning__bulkMutation__createOrder__failed": "Create order failed.", "@sage/xtrem-supply-chain/nodes__supply_planning__bulkMutation__createWorkOrder": "Create work order", "@sage/xtrem-supply-chain/nodes__supply_planning__bulkMutation__createWorkOrder__failed": "Create work order failed.", "@sage/xtrem-supply-chain/nodes__supply_planning__error_notification_title": "Purchase order planning error", "@sage/xtrem-supply-chain/nodes__supply_planning__expected_receipt_date": "Requirement date is required.", "@sage/xtrem-supply-chain/nodes__supply_planning__missing_work_order_category": "You need to add a category.", "@sage/xtrem-supply-chain/nodes__supply_planning__missing_work_order_type": "You need to add a type.", "@sage/xtrem-supply-chain/nodes__supply_planning__node_name": "Supply planning", "@sage/xtrem-supply-chain/nodes__supply_planning__order_date": "Start date is required.", "@sage/xtrem-supply-chain/nodes__supply_planning__property__canBypassDateControl": "Can bypass date control", "@sage/xtrem-supply-chain/nodes__supply_planning__property__grossPrice": "Gross price", "@sage/xtrem-supply-chain/nodes__supply_planning__property__itemSite": "Item site", "@sage/xtrem-supply-chain/nodes__supply_planning__property__itemSupplier": "Item supplier", "@sage/xtrem-supply-chain/nodes__supply_planning__property__mrpResultLine": "Mrp result line", "@sage/xtrem-supply-chain/nodes__supply_planning__property__purchaseQuantity": "Purchase quantity", "@sage/xtrem-supply-chain/nodes__supply_planning__property__purchaseRequisitionLine": "Purchase requisition line", "@sage/xtrem-supply-chain/nodes__supply_planning__property__purchaseSite": "Purchase site", "@sage/xtrem-supply-chain/nodes__supply_planning__property__purchaseUnit": "Purchase unit", "@sage/xtrem-supply-chain/nodes__supply_planning__property__requirementDate": "Requirement date", "@sage/xtrem-supply-chain/nodes__supply_planning__property__startDate": "Start date", "@sage/xtrem-supply-chain/nodes__supply_planning__property__status": "Status", "@sage/xtrem-supply-chain/nodes__supply_planning__property__stockQuantity": "Stock quantity", "@sage/xtrem-supply-chain/nodes__supply_planning__property__stockUnit": "Stock unit", "@sage/xtrem-supply-chain/nodes__supply_planning__property__suggestionDate": "Suggestion date", "@sage/xtrem-supply-chain/nodes__supply_planning__property__suggestionSource": "Suggestion source", "@sage/xtrem-supply-chain/nodes__supply_planning__property__suggestionUser": "Suggestion user", "@sage/xtrem-supply-chain/nodes__supply_planning__property__supplier": "Supplier", "@sage/xtrem-supply-chain/nodes__supply_planning__property__type": "Type", "@sage/xtrem-supply-chain/nodes__supply_planning__property__workOrderCategory": "Work order category", "@sage/xtrem-supply-chain/nodes__supply_planning__property__workOrderName": "Work order name", "@sage/xtrem-supply-chain/nodes__supply_planning__property__workOrderNumber": "Work order number", "@sage/xtrem-supply-chain/nodes__supply_planning__property__workOrderType": "Work order type", "@sage/xtrem-supply-chain/nodes__supply_planning__purchase_order_creation_error": "Failed to create purchase order: {{errors}}", "@sage/xtrem-supply-chain/nodes__supply_planning__purchase_order_creation_succeeded": "Purchase order {{number}} created.", "@sage/xtrem-supply-chain/nodes__supply_planning__purchase_order_error_notification_create": "Purchase order planning error", "@sage/xtrem-supply-chain/nodes__supply_planning__purchase_quantity": "Purchase quantity is required.", "@sage/xtrem-supply-chain/nodes__supply_planning__purchase_quantity_zero_or_less": "The purchase quantity cannot be less than or equal to 0.", "@sage/xtrem-supply-chain/nodes__supply_planning__purchase_site": "Purchase site is required.", "@sage/xtrem-supply-chain/nodes__supply_planning__purchase_unit": "Purchase unit is required.", "@sage/xtrem-supply-chain/nodes__supply_planning__query__checkSupplyPlanningPurchaseOrderCreated": "Check supply planning purchase order created", "@sage/xtrem-supply-chain/nodes__supply_planning__query__checkSupplyPlanningPurchaseOrderCreated__failed": "Check supply planning purchase order created failed.", "@sage/xtrem-supply-chain/nodes__supply_planning__status": "The purchase order is already created.", "@sage/xtrem-supply-chain/nodes__supply_planning__stock_quantity": "You need to add a quantity.", "@sage/xtrem-supply-chain/nodes__supply_planning__success_notification_description": "Purchase orders created", "@sage/xtrem-supply-chain/nodes__supply_planning__success_notification_title": "Purchase order planning complete", "@sage/xtrem-supply-chain/nodes__supply_planning__supplier": "Supplier is required.", "@sage/xtrem-supply-chain/nodes__supply_planning__view_link": "View", "@sage/xtrem-supply-chain/nodes__supply_planning__WO_success_notification_description": "Work orders created", "@sage/xtrem-supply-chain/nodes__supply_planning__WO_success_notification_title": "Work order planning complete", "@sage/xtrem-supply-chain/nodes__supply_planning__work_order_creation_error": "Failed to create work order: {{errors}}", "@sage/xtrem-supply-chain/nodes__supply_planning__work_order_creation_succeeded": "Work order {{number}} created.", "@sage/xtrem-supply-chain/nodes__supply_planning__work_order_error_notification_create": "Work order planning error", "@sage/xtrem-supply-chain/nodes__supply_planning__work_order_error_notification_title": "Work order planning error", "@sage/xtrem-supply-chain/nodes__supply_planning__work_order_status": "The work order is already created.", "@sage/xtrem-supply-chain/nodes__supply_planning__wrong_purchase_order_requirement_date": "The requirement date needs to be on or after the current date.", "@sage/xtrem-supply-chain/nodes__supply_planning__wrong_purchase_order_start_date": "The start date needs to be on or before the current date.", "@sage/xtrem-supply-chain/nodes__supply_planning__wrong_work_order_category": "You need to select a different category. There is no routing or bill of material for this item-site.", "@sage/xtrem-supply-chain/nodes__supply_planning__wrong_work_order_start_date": "The start date needs to be on or after the current date.", "@sage/xtrem-supply-chain/nodes__supply-chain_confirm_success": "The stock transfer shipment is confirmed and set to 'Ready to ship'.", "@sage/xtrem-supply-chain/nodes__work_in_progress_stock_transfer_order_line__asyncMutation__asyncExport": "Export", "@sage/xtrem-supply-chain/nodes__work_in_progress_stock_transfer_order_line__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-supply-chain/nodes__work_in_progress_stock_transfer_order_line__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-supply-chain/nodes__work_in_progress_stock_transfer_order_line__node_name": "Work in progress stock transfer order line", "@sage/xtrem-supply-chain/nodes__work_in_progress_stock_transfer_order_line__property__actualQuantity": "Actual quantity", "@sage/xtrem-supply-chain/nodes__work_in_progress_stock_transfer_order_line__property__documentId": "Document id", "@sage/xtrem-supply-chain/nodes__work_in_progress_stock_transfer_order_line__property__documentLine": "Document line", "@sage/xtrem-supply-chain/nodes__work_in_progress_stock_transfer_order_line__property__documentNumber": "Document number", "@sage/xtrem-supply-chain/nodes__work_in_progress_stock_transfer_order_line__property__documentType": "Document type", "@sage/xtrem-supply-chain/nodes__work_in_progress_stock_transfer_order_line__property__endDate": "End date", "@sage/xtrem-supply-chain/nodes__work_in_progress_stock_transfer_order_line__property__expectedQuantity": "Expected quantity", "@sage/xtrem-supply-chain/nodes__work_in_progress_stock_transfer_order_line__property__item": "<PERSON><PERSON>", "@sage/xtrem-supply-chain/nodes__work_in_progress_stock_transfer_order_line__property__originDocumentType": "Origin document type", "@sage/xtrem-supply-chain/nodes__work_in_progress_stock_transfer_order_line__property__outstandingQuantity": "Outstanding quantity", "@sage/xtrem-supply-chain/nodes__work_in_progress_stock_transfer_order_line__property__remainingQuantityToAllocate": "Remaining quantity to allocate", "@sage/xtrem-supply-chain/nodes__work_in_progress_stock_transfer_order_line__property__site": "Site", "@sage/xtrem-supply-chain/nodes__work_in_progress_stock_transfer_order_line__property__startDate": "Start date", "@sage/xtrem-supply-chain/nodes__work_in_progress_stock_transfer_order_line__property__status": "Status", "@sage/xtrem-supply-chain/nodes__work_in_progress_stock_transfer_order_line__property__stockTransferOrderLine": "Stock transfer order line", "@sage/xtrem-supply-chain/nodes__work_in_progress_stock_transfer_receipt_line__asyncMutation__asyncExport": "Export", "@sage/xtrem-supply-chain/nodes__work_in_progress_stock_transfer_receipt_line__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-supply-chain/nodes__work_in_progress_stock_transfer_receipt_line__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-supply-chain/nodes__work_in_progress_stock_transfer_receipt_line__node_name": "Work in progress stock transfer receipt line", "@sage/xtrem-supply-chain/nodes__work_in_progress_stock_transfer_receipt_line__property__actualQuantity": "Actual quantity", "@sage/xtrem-supply-chain/nodes__work_in_progress_stock_transfer_receipt_line__property__documentId": "Document id", "@sage/xtrem-supply-chain/nodes__work_in_progress_stock_transfer_receipt_line__property__documentLine": "Document line", "@sage/xtrem-supply-chain/nodes__work_in_progress_stock_transfer_receipt_line__property__documentNumber": "Document number", "@sage/xtrem-supply-chain/nodes__work_in_progress_stock_transfer_receipt_line__property__documentType": "Document type", "@sage/xtrem-supply-chain/nodes__work_in_progress_stock_transfer_receipt_line__property__endDate": "End date", "@sage/xtrem-supply-chain/nodes__work_in_progress_stock_transfer_receipt_line__property__expectedQuantity": "Expected quantity", "@sage/xtrem-supply-chain/nodes__work_in_progress_stock_transfer_receipt_line__property__item": "<PERSON><PERSON>", "@sage/xtrem-supply-chain/nodes__work_in_progress_stock_transfer_receipt_line__property__originDocumentType": "Origin document type", "@sage/xtrem-supply-chain/nodes__work_in_progress_stock_transfer_receipt_line__property__outstandingQuantity": "Outstanding quantity", "@sage/xtrem-supply-chain/nodes__work_in_progress_stock_transfer_receipt_line__property__site": "Site", "@sage/xtrem-supply-chain/nodes__work_in_progress_stock_transfer_receipt_line__property__startDate": "Start date", "@sage/xtrem-supply-chain/nodes__work_in_progress_stock_transfer_receipt_line__property__status": "Status", "@sage/xtrem-supply-chain/nodes__work_in_progress_stock_transfer_receipt_line__property__stockTransferReceiptLine": "Stock transfer receipt line", "@sage/xtrem-supply-chain/nodes_extension__sales_order__default_supplier_not_found": "Default supplier not found for item {{item}} and site {{site}}.", "@sage/xtrem-supply-chain/nodes_extension__sales_order_line__cant_post_shipment_when_bill_to_customer_is_on_hold": "The Bill-to customer is on hold. The shipment cannot be posted.", "@sage/xtrem-supply-chain/nodes_extension__sales_order_line__status_is_not_ok": "Sales order line status should not be quote or closed.", "@sage/xtrem-supply-chain/nodes_extensions__sales_order_extension__bill_of_material_not_found": "No bill of material available to use found.", "@sage/xtrem-supply-chain/package__name": "Sage xtrem supply chain", "@sage/xtrem-supply-chain/page__stock_transfer_order__auto_allocation_cannot_decrease_quantity": "You can only reduce the quantity of the line after the allocation request is complete.", "@sage/xtrem-supply-chain/page-extensions__landed_cost_allocation_panel_extension__lines____columns__title__stockTransactionStatus": "Stock status", "@sage/xtrem-supply-chain/page-extensions__landed_cost_allocation_panel_extension__selectFromStockTransferReceipt____title": "Add lines from stock transfer receipts", "@sage/xtrem-supply-chain/page-extensions__landed_cost_document_line_pick_list_extension__documents____levels__columns__title__displayStatus": "Status", "@sage/xtrem-supply-chain/page-extensions__landed_cost_document_line_pick_list_extension__documents____levels__columns__title__supplier__name": "Supplier", "@sage/xtrem-supply-chain/page-extensions__landed_cost_document_line_pick_list_extension__stockTransferOrder____columns__title__displayStatus": "Status", "@sage/xtrem-supply-chain/page-extensions__landed_cost_document_line_pick_list_extension__stockTransferOrder____columns__title__number": "Number", "@sage/xtrem-supply-chain/page-extensions__landed_cost_document_line_pick_list_extension__stockTransferOrder____columns__title__receivingSite": "Receiving site", "@sage/xtrem-supply-chain/page-extensions__landed_cost_document_line_pick_list_extension__stockTransferOrder____columns__title__site": "Shipping site", "@sage/xtrem-supply-chain/page-extensions__landed_cost_document_line_pick_list_extension__stockTransferOrder____lookupDialogTitle": "Select stock transfer order", "@sage/xtrem-supply-chain/page-extensions__landed_cost_document_line_pick_list_extension__stockTransferOrder____title": "Stock transfer order", "@sage/xtrem-supply-chain/page-extensions__landed_cost_document_line_pick_list_extension__stockTransferReceipt____columns__title__displayStatus": "Status", "@sage/xtrem-supply-chain/page-extensions__landed_cost_document_line_pick_list_extension__stockTransferReceipt____columns__title__number": "Number", "@sage/xtrem-supply-chain/page-extensions__landed_cost_document_line_pick_list_extension__stockTransferReceipt____columns__title__shippingSite": "Shipping site", "@sage/xtrem-supply-chain/page-extensions__landed_cost_document_line_pick_list_extension__stockTransferReceipt____columns__title__site": "Receiving site", "@sage/xtrem-supply-chain/page-extensions__landed_cost_document_line_pick_list_extension__stockTransferReceipt____lookupDialogTitle": "Select stock transfer receipt", "@sage/xtrem-supply-chain/page-extensions__landed_cost_document_line_pick_list_extension__stockTransferReceipt____title": "Stock transfer receipt", "@sage/xtrem-supply-chain/page-extensions__site_extension__isStockTransferOrderApprovalManaged____title": "Stock transfer", "@sage/xtrem-supply-chain/page-extensions__site_extension__stockDefaultApproverBlock____title": "Stock approval", "@sage/xtrem-supply-chain/page-extensions__site_extension__stockTransferOrderDefaultApprover____columns__title__email": "Email", "@sage/xtrem-supply-chain/page-extensions__site_extension__stockTransferOrderDefaultApprover____columns__title__firstName": "First name", "@sage/xtrem-supply-chain/page-extensions__site_extension__stockTransferOrderDefaultApprover____columns__title__lastName": "Last name", "@sage/xtrem-supply-chain/page-extensions__site_extension__stockTransferOrderDefaultApprover____lookupDialogTitle": "Select approver", "@sage/xtrem-supply-chain/page-extensions__site_extension__stockTransferOrderDefaultApprover____title": "Approver", "@sage/xtrem-supply-chain/page-extensions__site_extension__stockTransferOrderSubstituteApprover____columns__title__email": "Email", "@sage/xtrem-supply-chain/page-extensions__site_extension__stockTransferOrderSubstituteApprover____columns__title__firstName": "First name", "@sage/xtrem-supply-chain/page-extensions__site_extension__stockTransferOrderSubstituteApprover____columns__title__lastName": "Last name", "@sage/xtrem-supply-chain/page-extensions__site_extension__stockTransferOrderSubstituteApprover____lookupDialogTitle": "Select substitute approver", "@sage/xtrem-supply-chain/page-extensions__site_extension__stockTransferOrderSubstituteApprover____title": "Substitute approver", "@sage/xtrem-supply-chain/pages__document_line_landed_cost_pick_list___no_results": "No results found.", "@sage/xtrem-supply-chain/pages__document_line_landed_cost_pick_list__search": "Search", "@sage/xtrem-supply-chain/pages__item_site_table_panel____title": "Add lines from item site", "@sage/xtrem-supply-chain/pages__item_site_table_panel__cancel____title": "Cancel", "@sage/xtrem-supply-chain/pages__item_site_table_panel__confirm____title": "Add", "@sage/xtrem-supply-chain/pages__item_site_table_panel__isAutoAllocationRequested____title": "Auto allocation", "@sage/xtrem-supply-chain/pages__item_site_table_panel__itemSiteLines____columns__columns__item__name__title": "Category", "@sage/xtrem-supply-chain/pages__item_site_table_panel__itemSiteLines____columns__title__availableQuantityInStockUnit": "Stock available", "@sage/xtrem-supply-chain/pages__item_site_table_panel__itemSiteLines____columns__title__isAvailableQuantityInStockUnitSelected": "Select all", "@sage/xtrem-supply-chain/pages__item_site_table_panel__itemSiteLines____columns__title__item__id": "Item ID", "@sage/xtrem-supply-chain/pages__item_site_table_panel__itemSiteLines____columns__title__item__image": "Image", "@sage/xtrem-supply-chain/pages__item_site_table_panel__itemSiteLines____columns__title__item__name": "Item name", "@sage/xtrem-supply-chain/pages__item_site_table_panel__itemSiteLines____columns__title__quantity": "Quantity to ship", "@sage/xtrem-supply-chain/pages__item_site_table_panel__itemSiteLines____mobileCard__image__title": "Image", "@sage/xtrem-supply-chain/pages__item_site_table_panel__itemSiteLines____mobileCard__line2Right__title": "Quantity to ship", "@sage/xtrem-supply-chain/pages__item_site_table_panel__itemSiteLines____mobileCard__title__title": "Site", "@sage/xtrem-supply-chain/pages__item_site_table_panel__itemSiteLines____mobileCard__titleRight__title": "<PERSON><PERSON>", "@sage/xtrem-supply-chain/pages__item_site_table_panel__itemSiteLines____title": "Item sites", "@sage/xtrem-supply-chain/pages__item_site_table_panel__quantity_in_stock_unit_error_negative": "You need to enter a quantity equal to or higher than zero.", "@sage/xtrem-supply-chain/pages__item_site_table_panel__quantity_in_stock_unit_error_not_available": "You need to enter a quantity equal to or less than the quantity available in stock.", "@sage/xtrem-supply-chain/pages__purchase_order_planning____navigationPanel__bulkActions__title": "Create order", "@sage/xtrem-supply-chain/pages__purchase_order_planning____navigationPanel__listItem__companyID__title": "Company ID", "@sage/xtrem-supply-chain/pages__purchase_order_planning____navigationPanel__listItem__grossPrice__title": "Gross price", "@sage/xtrem-supply-chain/pages__purchase_order_planning____navigationPanel__listItem__id__title": "Supplier ID", "@sage/xtrem-supply-chain/pages__purchase_order_planning____navigationPanel__listItem__itemDescription__title": "Item description", "@sage/xtrem-supply-chain/pages__purchase_order_planning____navigationPanel__listItem__itemID__title": "Item ID", "@sage/xtrem-supply-chain/pages__purchase_order_planning____navigationPanel__listItem__line2__title": "<PERSON><PERSON>", "@sage/xtrem-supply-chain/pages__purchase_order_planning____navigationPanel__listItem__line3__title": "Supplier", "@sage/xtrem-supply-chain/pages__purchase_order_planning____navigationPanel__listItem__preferredProcess__title": "Preferred process", "@sage/xtrem-supply-chain/pages__purchase_order_planning____navigationPanel__listItem__purchaseQuantity__title": "Purchase quantity", "@sage/xtrem-supply-chain/pages__purchase_order_planning____navigationPanel__listItem__requirementDate__title": "Expected receipt date", "@sage/xtrem-supply-chain/pages__purchase_order_planning____navigationPanel__listItem__siteID__title": "Site ID", "@sage/xtrem-supply-chain/pages__purchase_order_planning____navigationPanel__listItem__startDate__title": "Order date", "@sage/xtrem-supply-chain/pages__purchase_order_planning____navigationPanel__listItem__suggestionDate__title": "Suggestion date", "@sage/xtrem-supply-chain/pages__purchase_order_planning____navigationPanel__listItem__suggestionSource__title": "Suggestion source", "@sage/xtrem-supply-chain/pages__purchase_order_planning____navigationPanel__listItem__title__title": "Company name", "@sage/xtrem-supply-chain/pages__purchase_order_planning____navigationPanel__listItem__titleRight__title": "Site name", "@sage/xtrem-supply-chain/pages__purchase_order_planning____navigationPanel__optionsMenu__title": "All", "@sage/xtrem-supply-chain/pages__purchase_order_planning____title": "Purchase order planning", "@sage/xtrem-supply-chain/pages__purchase_order_planning__company____columns__title__id": "ID ", "@sage/xtrem-supply-chain/pages__purchase_order_planning__company____title": "Company", "@sage/xtrem-supply-chain/pages__purchase_order_planning__grossPrice____title": "Gross price", "@sage/xtrem-supply-chain/pages__purchase_order_planning__item____title": "Item ID", "@sage/xtrem-supply-chain/pages__purchase_order_planning__mainSection____title": "General", "@sage/xtrem-supply-chain/pages__purchase_order_planning__no_conversion_factor_defined": "You need to add a unit conversion from {{fromUnit}} to {{toUnit}}.", "@sage/xtrem-supply-chain/pages__purchase_order_planning__preferredProcess____title": "Preferred process", "@sage/xtrem-supply-chain/pages__purchase_order_planning__purchase_quantity_zero_or_less": "The purchase quantity cannot be less than or equal to 0.", "@sage/xtrem-supply-chain/pages__purchase_order_planning__purchaseQuantity____title": "Purchase quantity", "@sage/xtrem-supply-chain/pages__purchase_order_planning__purchaseSite____columns__title__id": "ID ", "@sage/xtrem-supply-chain/pages__purchase_order_planning__purchaseSite____title": "Purchasing site", "@sage/xtrem-supply-chain/pages__purchase_order_planning__purchaseUnit____columns__title__name": "Name", "@sage/xtrem-supply-chain/pages__purchase_order_planning__purchaseUnit____columns__title__symbol": "Symbol", "@sage/xtrem-supply-chain/pages__purchase_order_planning__purchaseUnit____lookupDialogTitle": "Select unit", "@sage/xtrem-supply-chain/pages__purchase_order_planning__requirementDate____title": "Expected receipt date", "@sage/xtrem-supply-chain/pages__purchase_order_planning__save____title": "Save", "@sage/xtrem-supply-chain/pages__purchase_order_planning__startDate____title": "Order date", "@sage/xtrem-supply-chain/pages__purchase_order_planning__status____title": "Create order status", "@sage/xtrem-supply-chain/pages__purchase_order_planning__stockQuantity____title": "Stock quantity", "@sage/xtrem-supply-chain/pages__purchase_order_planning__stockSite____title": "Stock site", "@sage/xtrem-supply-chain/pages__purchase_order_planning__stockUnit____columns__title__name": "Name", "@sage/xtrem-supply-chain/pages__purchase_order_planning__stockUnit____columns__title__symbol": "Symbol", "@sage/xtrem-supply-chain/pages__purchase_order_planning__suggestionDate____title": "Suggestion date", "@sage/xtrem-supply-chain/pages__purchase_order_planning__suggestionSource____title": "Suggestion source", "@sage/xtrem-supply-chain/pages__purchase_order_planning__suggestionUser____title": "Calculation user", "@sage/xtrem-supply-chain/pages__purchase_order_planning__supplier____columns__title__businessEntity__id": "ID", "@sage/xtrem-supply-chain/pages__purchase_order_planning__supplier____columns__title__businessEntity__name": "Name", "@sage/xtrem-supply-chain/pages__purchase_order_planning__supplier____columns__title__businessEntity__taxIdNumber": "Tax ID", "@sage/xtrem-supply-chain/pages__purchase_order_planning__supplier____lookupDialogTitle": "Select supplier", "@sage/xtrem-supply-chain/pages__sales_draft__section_title_send_stock_transfer_order_dialog_": "Send draft stock transfer order", "@sage/xtrem-supply-chain/pages__site_extension__control_stock_transfer_order_approval": "You need to approve or reject pending documents before you deactivate the approval process.", "@sage/xtrem-supply-chain/pages__stock__transfer_receipt__stock_details_control_message": "Stock details cannot be added to the receipt, the Receipt date is in the future.", "@sage/xtrem-supply-chain/pages__stock__transfer_receipt__stock_details_control_title": "Stock details", "@sage/xtrem-supply-chain/pages__stock_transfer_in_transit_inquiry____title": "Stock in transit inquiry", "@sage/xtrem-supply-chain/pages__stock_transfer_in_transit_inquiry__commodityCode____title": "Commodity code", "@sage/xtrem-supply-chain/pages__stock_transfer_in_transit_inquiry__company____title": "Company", "@sage/xtrem-supply-chain/pages__stock_transfer_in_transit_inquiry__criteriaBlock____title": "Criteria", "@sage/xtrem-supply-chain/pages__stock_transfer_in_transit_inquiry__date____title": "Date", "@sage/xtrem-supply-chain/pages__stock_transfer_in_transit_inquiry__fromItem____title": "From item", "@sage/xtrem-supply-chain/pages__stock_transfer_in_transit_inquiry__inTransitStockValue____title": "In transit stock value", "@sage/xtrem-supply-chain/pages__stock_transfer_in_transit_inquiry__itemCategory____title": "Item category", "@sage/xtrem-supply-chain/pages__stock_transfer_in_transit_inquiry__lines____columns__title__document__number": "Shipment number", "@sage/xtrem-supply-chain/pages__stock_transfer_in_transit_inquiry__lines____columns__title__document__receivingSite": "Receiving site", "@sage/xtrem-supply-chain/pages__stock_transfer_in_transit_inquiry__lines____columns__title__document__site": "Shipping site", "@sage/xtrem-supply-chain/pages__stock_transfer_in_transit_inquiry__lines____columns__title__document__site__legalCompany": "Company", "@sage/xtrem-supply-chain/pages__stock_transfer_in_transit_inquiry__lines____columns__title__document__site__legalCompany__currency__name": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-supply-chain/pages__stock_transfer_in_transit_inquiry__lines____columns__title__inTransit__commodityCode": "Commodity code", "@sage/xtrem-supply-chain/pages__stock_transfer_in_transit_inquiry__lines____columns__title__inTransit__costType": "Cost type", "@sage/xtrem-supply-chain/pages__stock_transfer_in_transit_inquiry__lines____columns__title__inTransit__stockValue": "Stock value in transit", "@sage/xtrem-supply-chain/pages__stock_transfer_in_transit_inquiry__lines____columns__title__inTransit__unitCost": "Unit cost", "@sage/xtrem-supply-chain/pages__stock_transfer_in_transit_inquiry__lines____columns__title__item__category__name": "Item category", "@sage/xtrem-supply-chain/pages__stock_transfer_in_transit_inquiry__lines____columns__title__item__id": "Item ID", "@sage/xtrem-supply-chain/pages__stock_transfer_in_transit_inquiry__lines____columns__title__item__name": "<PERSON><PERSON>", "@sage/xtrem-supply-chain/pages__stock_transfer_in_transit_inquiry__lines____columns__title__item__postingClass__name": "Posting class", "@sage/xtrem-supply-chain/pages__stock_transfer_in_transit_inquiry__lines____columns__title__itemDescription": "Item description", "@sage/xtrem-supply-chain/pages__stock_transfer_in_transit_inquiry__lines____columns__title__quantityInStockUnit": "Quantity in stock unit", "@sage/xtrem-supply-chain/pages__stock_transfer_in_transit_inquiry__lines____columns__title__stockUnit__name": "Stock unit", "@sage/xtrem-supply-chain/pages__stock_transfer_in_transit_inquiry__lines____title": "Results", "@sage/xtrem-supply-chain/pages__stock_transfer_in_transit_inquiry__mainSection____title": "General", "@sage/xtrem-supply-chain/pages__stock_transfer_in_transit_inquiry__postingClass____title": "Posting class", "@sage/xtrem-supply-chain/pages__stock_transfer_in_transit_inquiry__resultsSection____title": "Results", "@sage/xtrem-supply-chain/pages__stock_transfer_in_transit_inquiry__runStockTransferInTransitInquiry____title": "Run", "@sage/xtrem-supply-chain/pages__stock_transfer_in_transit_inquiry__site____title": "Shipping site", "@sage/xtrem-supply-chain/pages__stock_transfer_in_transit_inquiry__toItem____title": "To item", "@sage/xtrem-supply-chain/pages__stock_transfer_in_transit_inquiry__user____title": "User", "@sage/xtrem-supply-chain/pages__stock_transfer_order____navigationPanel__dropdownActions__title": "Confirm", "@sage/xtrem-supply-chain/pages__stock_transfer_order____navigationPanel__dropdownActions__title__10": "Set dimensions", "@sage/xtrem-supply-chain/pages__stock_transfer_order____navigationPanel__dropdownActions__title__11": "Delete", "@sage/xtrem-supply-chain/pages__stock_transfer_order____navigationPanel__dropdownActions__title__2": "Approve", "@sage/xtrem-supply-chain/pages__stock_transfer_order____navigationPanel__dropdownActions__title__3": "Reject", "@sage/xtrem-supply-chain/pages__stock_transfer_order____navigationPanel__dropdownActions__title__4": "Submit for approval", "@sage/xtrem-supply-chain/pages__stock_transfer_order____navigationPanel__dropdownActions__title__5": "Create shipment", "@sage/xtrem-supply-chain/pages__stock_transfer_order____navigationPanel__dropdownActions__title__6": "Close order", "@sage/xtrem-supply-chain/pages__stock_transfer_order____navigationPanel__dropdownActions__title__7": "Reopen order", "@sage/xtrem-supply-chain/pages__stock_transfer_order____navigationPanel__dropdownActions__title__8": "Allocate stock", "@sage/xtrem-supply-chain/pages__stock_transfer_order____navigationPanel__dropdownActions__title__9": "Deallocate stock", "@sage/xtrem-supply-chain/pages__stock_transfer_order____navigationPanel__listItem__deliveryLeadTime__postfix": "days", "@sage/xtrem-supply-chain/pages__stock_transfer_order____navigationPanel__listItem__deliveryLeadTime__title": "Delivery lead time", "@sage/xtrem-supply-chain/pages__stock_transfer_order____navigationPanel__listItem__deliveryMode__title": "Delivery mode", "@sage/xtrem-supply-chain/pages__stock_transfer_order____navigationPanel__listItem__doNotShipAfterDate__title": "Do-not-ship-after date", "@sage/xtrem-supply-chain/pages__stock_transfer_order____navigationPanel__listItem__doNotShipBeforeDate__title": "Do-not-ship-before date", "@sage/xtrem-supply-chain/pages__stock_transfer_order____navigationPanel__listItem__expectedDeliveryDate__title": "Delivery date", "@sage/xtrem-supply-chain/pages__stock_transfer_order____navigationPanel__listItem__incoterm__title": "Incoterms® rule", "@sage/xtrem-supply-chain/pages__stock_transfer_order____navigationPanel__listItem__isPrinted__title": "Printed", "@sage/xtrem-supply-chain/pages__stock_transfer_order____navigationPanel__listItem__isSent__title": "<PERSON><PERSON>", "@sage/xtrem-supply-chain/pages__stock_transfer_order____navigationPanel__listItem__line2__title": "Receiving site", "@sage/xtrem-supply-chain/pages__stock_transfer_order____navigationPanel__listItem__line2Right__title": "Order date", "@sage/xtrem-supply-chain/pages__stock_transfer_order____navigationPanel__listItem__requestedDeliveryDate__title": "Requested delivery date", "@sage/xtrem-supply-chain/pages__stock_transfer_order____navigationPanel__listItem__shippingDate__title": "Shipping date", "@sage/xtrem-supply-chain/pages__stock_transfer_order____navigationPanel__listItem__shipToCustomer__title": "Ship-to customer", "@sage/xtrem-supply-chain/pages__stock_transfer_order____navigationPanel__listItem__site__title": "Shipping site", "@sage/xtrem-supply-chain/pages__stock_transfer_order____navigationPanel__listItem__stockSite__title": "Stock site", "@sage/xtrem-supply-chain/pages__stock_transfer_order____navigationPanel__listItem__title__title": "Number", "@sage/xtrem-supply-chain/pages__stock_transfer_order____navigationPanel__listItem__titleRight__title": "Status", "@sage/xtrem-supply-chain/pages__stock_transfer_order____navigationPanel__listItem__transactionCurrency__columns__title__decimalDigits": "Decimal points", "@sage/xtrem-supply-chain/pages__stock_transfer_order____navigationPanel__listItem__transactionCurrency__columns__title__name": "Name", "@sage/xtrem-supply-chain/pages__stock_transfer_order____navigationPanel__listItem__transactionCurrency__columns__title__rounding": "Rounding", "@sage/xtrem-supply-chain/pages__stock_transfer_order____navigationPanel__listItem__transactionCurrency__columns__title__symbol": "Symbol", "@sage/xtrem-supply-chain/pages__stock_transfer_order____navigationPanel__listItem__transactionCurrency__title": "Transaction currency", "@sage/xtrem-supply-chain/pages__stock_transfer_order____navigationPanel__optionsMenu__title": "All open statuses", "@sage/xtrem-supply-chain/pages__stock_transfer_order____navigationPanel__optionsMenu__title__10": "Confirmed", "@sage/xtrem-supply-chain/pages__stock_transfer_order____navigationPanel__optionsMenu__title__2": "All statuses", "@sage/xtrem-supply-chain/pages__stock_transfer_order____navigationPanel__optionsMenu__title__3": "Draft", "@sage/xtrem-supply-chain/pages__stock_transfer_order____navigationPanel__optionsMenu__title__4": "Pending approval", "@sage/xtrem-supply-chain/pages__stock_transfer_order____navigationPanel__optionsMenu__title__5": "Approved", "@sage/xtrem-supply-chain/pages__stock_transfer_order____navigationPanel__optionsMenu__title__6": "Closed", "@sage/xtrem-supply-chain/pages__stock_transfer_order____navigationPanel__optionsMenu__title__7": "Rejected", "@sage/xtrem-supply-chain/pages__stock_transfer_order____navigationPanel__optionsMenu__title__8": "Shipped", "@sage/xtrem-supply-chain/pages__stock_transfer_order____navigationPanel__optionsMenu__title__9": "Received", "@sage/xtrem-supply-chain/pages__stock_transfer_order____objectTypePlural": "Stock transfer orders", "@sage/xtrem-supply-chain/pages__stock_transfer_order____objectTypeSingular": "Stock transfer order", "@sage/xtrem-supply-chain/pages__stock_transfer_order____title": "Stock transfer order", "@sage/xtrem-supply-chain/pages__stock_transfer_order___apply_dimensions_success": "Dimensions applied.", "@sage/xtrem-supply-chain/pages__stock_transfer_order___id____title": "ID", "@sage/xtrem-supply-chain/pages__stock_transfer_order___no_parameters": "No action set.", "@sage/xtrem-supply-chain/pages__stock_transfer_order__allocationRequestStatus____title": "Allocation request status", "@sage/xtrem-supply-chain/pages__stock_transfer_order__allocationStatus____title": "Stock allocation status", "@sage/xtrem-supply-chain/pages__stock_transfer_order__approvalStatus____title": "Approval status", "@sage/xtrem-supply-chain/pages__stock_transfer_order__approve____title": "Approve", "@sage/xtrem-supply-chain/pages__stock_transfer_order__cannot_delete_line_allocation_request_in_progress": "Automatic allocation needs to finish before you can delete the line.", "@sage/xtrem-supply-chain/pages__stock_transfer_order__cascade_ship_to_address_update_dialog_content": "You are about to update the delivery information.", "@sage/xtrem-supply-chain/pages__stock_transfer_order__cascade_ship_to_address_update_dialog_title": "Confirm update", "@sage/xtrem-supply-chain/pages__stock_transfer_order__close____title": "Close order", "@sage/xtrem-supply-chain/pages__stock_transfer_order__companyFxRate____title": "Exchange rate", "@sage/xtrem-supply-chain/pages__stock_transfer_order__companyFxRateDivisor____title": "Exchange rate divisor", "@sage/xtrem-supply-chain/pages__stock_transfer_order__confirm____title": "Confirm", "@sage/xtrem-supply-chain/pages__stock_transfer_order__contacts____columns__title__email": "Email", "@sage/xtrem-supply-chain/pages__stock_transfer_order__contacts____columns__title__firstName": "First name", "@sage/xtrem-supply-chain/pages__stock_transfer_order__contacts____columns__title__lastName": "Last name", "@sage/xtrem-supply-chain/pages__stock_transfer_order__contacts____columns__title__title": "Title", "@sage/xtrem-supply-chain/pages__stock_transfer_order__contacts____title": "Contacts", "@sage/xtrem-supply-chain/pages__stock_transfer_order__contactSelectionBlock____title": "Contact selection", "@sage/xtrem-supply-chain/pages__stock_transfer_order__currency____title": "Transaction currency", "@sage/xtrem-supply-chain/pages__stock_transfer_order__date____title": "Order date", "@sage/xtrem-supply-chain/pages__stock_transfer_order__defaultDimension____title": "Set dimensions", "@sage/xtrem-supply-chain/pages__stock_transfer_order__deleteStockTransferOrder____title": "Delete", "@sage/xtrem-supply-chain/pages__stock_transfer_order__deliveryLeadTime____postfix": "day(s)", "@sage/xtrem-supply-chain/pages__stock_transfer_order__deliveryLeadTime____title": "Delivery lead time", "@sage/xtrem-supply-chain/pages__stock_transfer_order__deliveryMode____lookupDialogTitle": "Select delivery mode", "@sage/xtrem-supply-chain/pages__stock_transfer_order__deliveryMode____title": "Delivery mode", "@sage/xtrem-supply-chain/pages__stock_transfer_order__displayStatus____title": "Display status", "@sage/xtrem-supply-chain/pages__stock_transfer_order__doNotShipAfterDate____title": "Do-not-ship-after date", "@sage/xtrem-supply-chain/pages__stock_transfer_order__doNotShipBeforeDate____title": "Do-not-ship-before date", "@sage/xtrem-supply-chain/pages__stock_transfer_order__emailAddress____helperText": "An email will be sent to this address.", "@sage/xtrem-supply-chain/pages__stock_transfer_order__emailAddress____title": "Email", "@sage/xtrem-supply-chain/pages__stock_transfer_order__emailFirstName____title": "First name", "@sage/xtrem-supply-chain/pages__stock_transfer_order__emailLastName____title": "Last name", "@sage/xtrem-supply-chain/pages__stock_transfer_order__emailTitle____title": "Title", "@sage/xtrem-supply-chain/pages__stock_transfer_order__expected_delivery_cannot_be_a_not_working_day": "The expected delivery date must be on a working day.", "@sage/xtrem-supply-chain/pages__stock_transfer_order__expectedDeliveryDate____title": "Expected delivery date", "@sage/xtrem-supply-chain/pages__stock_transfer_order__expectedDeliveryDateHeader____title": "Expected delivery date", "@sage/xtrem-supply-chain/pages__stock_transfer_order__externalNote____helperText": "Notes display on customer documents.", "@sage/xtrem-supply-chain/pages__stock_transfer_order__externalNote____title": "Customer notes", "@sage/xtrem-supply-chain/pages__stock_transfer_order__externalNoteLine____helperText": "Notes display on customer documents.", "@sage/xtrem-supply-chain/pages__stock_transfer_order__externalNoteLine____title": "Customer line notes", "@sage/xtrem-supply-chain/pages__stock_transfer_order__financialSite____title": "Financial site", "@sage/xtrem-supply-chain/pages__stock_transfer_order__fxRateDate____title": "Exchange rate date", "@sage/xtrem-supply-chain/pages__stock_transfer_order__headerSection____title": "Header section", "@sage/xtrem-supply-chain/pages__stock_transfer_order__incoterm____lookupDialogTitle": "Select Incoterms® rule", "@sage/xtrem-supply-chain/pages__stock_transfer_order__incoterm____title": "Incoterms® rule", "@sage/xtrem-supply-chain/pages__stock_transfer_order__informationBlock____title": "Information", "@sage/xtrem-supply-chain/pages__stock_transfer_order__informationSection____title": "Information", "@sage/xtrem-supply-chain/pages__stock_transfer_order__internalNote____helperText": "Notes display on internal documents.", "@sage/xtrem-supply-chain/pages__stock_transfer_order__internalNote____title": "Internal notes", "@sage/xtrem-supply-chain/pages__stock_transfer_order__internalNoteLine____helperText": "Notes display on internal documents.", "@sage/xtrem-supply-chain/pages__stock_transfer_order__internalNoteLine____title": "Internal line notes", "@sage/xtrem-supply-chain/pages__stock_transfer_order__invalid-email": "Invalid email address: {{value}}", "@sage/xtrem-supply-chain/pages__stock_transfer_order__isExternalNote____title": "Add notes to customer document", "@sage/xtrem-supply-chain/pages__stock_transfer_order__isExternalNoteLine____title": "Add notes to customer document", "@sage/xtrem-supply-chain/pages__stock_transfer_order__isSent____title": "<PERSON><PERSON>", "@sage/xtrem-supply-chain/pages__stock_transfer_order__isTransferHeaderNote____title": "Repeat the document notes on new documents.", "@sage/xtrem-supply-chain/pages__stock_transfer_order__isTransferLineNote____title": "Repeat all the line notes on new documents.", "@sage/xtrem-supply-chain/pages__stock_transfer_order__itemSection____title": "Lines", "@sage/xtrem-supply-chain/pages__stock_transfer_order__landedCosts____columns__title__actualCostAmountInCompanyCurrency": "Actual cost amount in company currency", "@sage/xtrem-supply-chain/pages__stock_transfer_order__landedCosts____columns__title__landedCostType": "Type", "@sage/xtrem-supply-chain/pages__stock_transfer_order__landedCosts____title": "Summary by landed cost type", "@sage/xtrem-supply-chain/pages__stock_transfer_order__landedCostsSection____title": "Landed costs", "@sage/xtrem-supply-chain/pages__stock_transfer_order__landedCostsSectionBlock____title": "Total in company currency", "@sage/xtrem-supply-chain/pages__stock_transfer_order__line_delete_action_dialog_content": "You are about to delete this stock transfer order line. This action cannot be undone.", "@sage/xtrem-supply-chain/pages__stock_transfer_order__line_delete_action_dialog_title": "Confirm delete", "@sage/xtrem-supply-chain/pages__stock_transfer_order__lines____columns__columns__deliveryMode__name__title": "Name", "@sage/xtrem-supply-chain/pages__stock_transfer_order__lines____columns__columns__item__name__title": "Category", "@sage/xtrem-supply-chain/pages__stock_transfer_order__lines____columns__columns__receivingSite__name__columns__title__id": "ID", "@sage/xtrem-supply-chain/pages__stock_transfer_order__lines____columns__columns__receivingSite__name__columns__title__name": "Name", "@sage/xtrem-supply-chain/pages__stock_transfer_order__lines____columns__columns__receivingSite__name__title": "ID", "@sage/xtrem-supply-chain/pages__stock_transfer_order__lines____columns__columns__receivingSite__name__title__2": "Name", "@sage/xtrem-supply-chain/pages__stock_transfer_order__lines____columns__columns__receivingSite__name__title__3": "Company", "@sage/xtrem-supply-chain/pages__stock_transfer_order__lines____columns__columns__shipToCustomerAddress__name__columns__columns__mode__name__title": "Name", "@sage/xtrem-supply-chain/pages__stock_transfer_order__lines____columns__columns__shipToCustomerAddress__name__columns__columns__shipmentSite__columns__title__id": "ID", "@sage/xtrem-supply-chain/pages__stock_transfer_order__lines____columns__columns__shipToCustomerAddress__name__columns__columns__shipmentSite__columns__title__name": "Name", "@sage/xtrem-supply-chain/pages__stock_transfer_order__lines____columns__columns__shipToCustomerAddress__name__columns__columns__shipmentSite__title": "Name", "@sage/xtrem-supply-chain/pages__stock_transfer_order__lines____columns__columns__shipToCustomerAddress__name__columns__columns__shipmentSite__title__2": "ID", "@sage/xtrem-supply-chain/pages__stock_transfer_order__lines____columns__columns__shipToCustomerAddress__name__columns__columns__shipmentSite__title__3": "Company", "@sage/xtrem-supply-chain/pages__stock_transfer_order__lines____columns__columns__shipToCustomerAddress__name__columns__postfix__leadTime": "day(s)", "@sage/xtrem-supply-chain/pages__stock_transfer_order__lines____columns__columns__shipToCustomerAddress__name__columns__title__id": "ISO 3166-1 alpha-2 code", "@sage/xtrem-supply-chain/pages__stock_transfer_order__lines____columns__columns__shipToCustomerAddress__name__columns__title__incoterm": "Incoterms® rule", "@sage/xtrem-supply-chain/pages__stock_transfer_order__lines____columns__columns__shipToCustomerAddress__name__columns__title__leadTime": "Delivery lead time", "@sage/xtrem-supply-chain/pages__stock_transfer_order__lines____columns__columns__shipToCustomerAddress__name__columns__title__mode__name": "Delivery mode", "@sage/xtrem-supply-chain/pages__stock_transfer_order__lines____columns__columns__shipToCustomerAddress__name__columns__title__name": "Name", "@sage/xtrem-supply-chain/pages__stock_transfer_order__lines____columns__columns__shipToCustomerAddress__name__columns__title__shipmentSite": "Stock site", "@sage/xtrem-supply-chain/pages__stock_transfer_order__lines____columns__columns__shipToCustomerAddress__name__title": "Primary ship-to address", "@sage/xtrem-supply-chain/pages__stock_transfer_order__lines____columns__columns__shipToCustomerAddress__name__title__10": "Delivery detail", "@sage/xtrem-supply-chain/pages__stock_transfer_order__lines____columns__columns__shipToCustomerAddress__name__title__2": "Active", "@sage/xtrem-supply-chain/pages__stock_transfer_order__lines____columns__columns__shipToCustomerAddress__name__title__3": "Name", "@sage/xtrem-supply-chain/pages__stock_transfer_order__lines____columns__columns__shipToCustomerAddress__name__title__4": "Address line 1", "@sage/xtrem-supply-chain/pages__stock_transfer_order__lines____columns__columns__shipToCustomerAddress__name__title__5": "Address line 2", "@sage/xtrem-supply-chain/pages__stock_transfer_order__lines____columns__columns__shipToCustomerAddress__name__title__6": "City", "@sage/xtrem-supply-chain/pages__stock_transfer_order__lines____columns__columns__shipToCustomerAddress__name__title__7": "Region", "@sage/xtrem-supply-chain/pages__stock_transfer_order__lines____columns__columns__shipToCustomerAddress__name__title__8": "ZIP code", "@sage/xtrem-supply-chain/pages__stock_transfer_order__lines____columns__columns__shipToCustomerAddress__name__title__9": "Phone number", "@sage/xtrem-supply-chain/pages__stock_transfer_order__lines____columns__columns__stockSite__name__columns__title__id": "ID", "@sage/xtrem-supply-chain/pages__stock_transfer_order__lines____columns__columns__stockSite__name__columns__title__name": "Name", "@sage/xtrem-supply-chain/pages__stock_transfer_order__lines____columns__columns__stockSite__name__title": "ID", "@sage/xtrem-supply-chain/pages__stock_transfer_order__lines____columns__columns__stockSite__name__title__2": "Name", "@sage/xtrem-supply-chain/pages__stock_transfer_order__lines____columns__columns__stockSite__name__title__3": "Company", "@sage/xtrem-supply-chain/pages__stock_transfer_order__lines____columns__columns__unit__name__title": "Name", "@sage/xtrem-supply-chain/pages__stock_transfer_order__lines____columns__columns__unit__name__title__2": "Symbol", "@sage/xtrem-supply-chain/pages__stock_transfer_order__lines____columns__lookupDialogTitle__item__name": "Select item", "@sage/xtrem-supply-chain/pages__stock_transfer_order__lines____columns__lookupDialogTitle__receivingSite__name": "Select receiving site", "@sage/xtrem-supply-chain/pages__stock_transfer_order__lines____columns__lookupDialogTitle__stockSite__name": "Select stock site", "@sage/xtrem-supply-chain/pages__stock_transfer_order__lines____columns__lookupDialogTitle__unit__name": "Select unit", "@sage/xtrem-supply-chain/pages__stock_transfer_order__lines____columns__postfix__deliveryLeadTime": "day(s)", "@sage/xtrem-supply-chain/pages__stock_transfer_order__lines____columns__title__actualLandedCostInCompanyCurrency": "Actual landed cost in company currency", "@sage/xtrem-supply-chain/pages__stock_transfer_order__lines____columns__title__allocationRequestStatus": "Allocation request status", "@sage/xtrem-supply-chain/pages__stock_transfer_order__lines____columns__title__allocationStatus": "Allocation status", "@sage/xtrem-supply-chain/pages__stock_transfer_order__lines____columns__title__availableQuantityInStockUnit": "Stock available", "@sage/xtrem-supply-chain/pages__stock_transfer_order__lines____columns__title__deliveryLeadTime": "Delivery lead time", "@sage/xtrem-supply-chain/pages__stock_transfer_order__lines____columns__title__deliveryMode__name": "Delivery mode", "@sage/xtrem-supply-chain/pages__stock_transfer_order__lines____columns__title__doNotShipAfterDate": "Do-not-ship-after date", "@sage/xtrem-supply-chain/pages__stock_transfer_order__lines____columns__title__doNotShipBeforeDate": "Do-not-ship-before date", "@sage/xtrem-supply-chain/pages__stock_transfer_order__lines____columns__title__expectedDeliveryDate": "Expected delivery date", "@sage/xtrem-supply-chain/pages__stock_transfer_order__lines____columns__title__isExternalNote": "Add notes to customer document", "@sage/xtrem-supply-chain/pages__stock_transfer_order__lines____columns__title__item__id": "Item ID", "@sage/xtrem-supply-chain/pages__stock_transfer_order__lines____columns__title__item__image": "Image", "@sage/xtrem-supply-chain/pages__stock_transfer_order__lines____columns__title__item__name": "<PERSON><PERSON>", "@sage/xtrem-supply-chain/pages__stock_transfer_order__lines____columns__title__itemDescription": "Item description", "@sage/xtrem-supply-chain/pages__stock_transfer_order__lines____columns__title__quantity": "Quantity in stock unit", "@sage/xtrem-supply-chain/pages__stock_transfer_order__lines____columns__title__quantityAllocatedInStockUnit": "Allocated quantity", "@sage/xtrem-supply-chain/pages__stock_transfer_order__lines____columns__title__quantityInStockUnit": "Quantity in stock unit", "@sage/xtrem-supply-chain/pages__stock_transfer_order__lines____columns__title__quantityToShipInProgressInStockUnit": "Quantity in progress", "@sage/xtrem-supply-chain/pages__stock_transfer_order__lines____columns__title__receivingSite__name": "Receiving site", "@sage/xtrem-supply-chain/pages__stock_transfer_order__lines____columns__title__receivingStatus": "Receiving status", "@sage/xtrem-supply-chain/pages__stock_transfer_order__lines____columns__title__remainingQuantityToAllocateInStockUnit": "Remaining quantity", "@sage/xtrem-supply-chain/pages__stock_transfer_order__lines____columns__title__remainingQuantityToShipInStockUnit": "Quantity to allocate", "@sage/xtrem-supply-chain/pages__stock_transfer_order__lines____columns__title__requestedDeliveryDate": "Requested delivery date", "@sage/xtrem-supply-chain/pages__stock_transfer_order__lines____columns__title__shippedQuantityInStockUnit": "Shipped quantity", "@sage/xtrem-supply-chain/pages__stock_transfer_order__lines____columns__title__shippingDate": "Shipping date", "@sage/xtrem-supply-chain/pages__stock_transfer_order__lines____columns__title__shippingStatus": "Shipping status", "@sage/xtrem-supply-chain/pages__stock_transfer_order__lines____columns__title__shipToCustomerAddress__name": "Ship-to address", "@sage/xtrem-supply-chain/pages__stock_transfer_order__lines____columns__title__status": "Status", "@sage/xtrem-supply-chain/pages__stock_transfer_order__lines____columns__title__stockCostAmount": "Stock cost amount", "@sage/xtrem-supply-chain/pages__stock_transfer_order__lines____columns__title__stockCostUnit": "Stock cost unit", "@sage/xtrem-supply-chain/pages__stock_transfer_order__lines____columns__title__stockOnHand": "Stock on hand", "@sage/xtrem-supply-chain/pages__stock_transfer_order__lines____columns__title__stockShortageInStockUnit": "Stock shortage", "@sage/xtrem-supply-chain/pages__stock_transfer_order__lines____columns__title__stockShortageStatus": "Stock shortage status", "@sage/xtrem-supply-chain/pages__stock_transfer_order__lines____columns__title__stockSite__name": "Stock site", "@sage/xtrem-supply-chain/pages__stock_transfer_order__lines____columns__title__stockUnit__name": "Stock unit", "@sage/xtrem-supply-chain/pages__stock_transfer_order__lines____columns__title__unit__name": "Stock unit", "@sage/xtrem-supply-chain/pages__stock_transfer_order__lines____columns__title__unitToStockUnitConversionFactor": "Stock unit conversion factor", "@sage/xtrem-supply-chain/pages__stock_transfer_order__lines____dropdownActions__title": "Open", "@sage/xtrem-supply-chain/pages__stock_transfer_order__lines____dropdownActions__title__2": "Close", "@sage/xtrem-supply-chain/pages__stock_transfer_order__lines____dropdownActions__title__3": "Allocate stock", "@sage/xtrem-supply-chain/pages__stock_transfer_order__lines____dropdownActions__title__4": "Projected stock", "@sage/xtrem-supply-chain/pages__stock_transfer_order__lines____dropdownActions__title__5": "Manage allocations", "@sage/xtrem-supply-chain/pages__stock_transfer_order__lines____dropdownActions__title__6": "Dimensions", "@sage/xtrem-supply-chain/pages__stock_transfer_order__lines____dropdownActions__title__7": "Delete", "@sage/xtrem-supply-chain/pages__stock_transfer_order__lines____dropdownActions__title__8": "Landed costs", "@sage/xtrem-supply-chain/pages__stock_transfer_order__lines____inlineActions__title": "Open line panel", "@sage/xtrem-supply-chain/pages__stock_transfer_order__lines____mobileCard__line2__title": "Description", "@sage/xtrem-supply-chain/pages__stock_transfer_order__lines____mobileCard__title__title": "Product", "@sage/xtrem-supply-chain/pages__stock_transfer_order__lines____mobileCard__titleRight__title": "Status", "@sage/xtrem-supply-chain/pages__stock_transfer_order__lines____optionsMenu__title": "All statuses", "@sage/xtrem-supply-chain/pages__stock_transfer_order__lines____optionsMenu__title__2": "All open statuses", "@sage/xtrem-supply-chain/pages__stock_transfer_order__lines____optionsMenu__title__3": "Allocation required", "@sage/xtrem-supply-chain/pages__stock_transfer_order__lines____sidebar__headerDropdownActions__title": "Open", "@sage/xtrem-supply-chain/pages__stock_transfer_order__lines____sidebar__headerDropdownActions__title__2": "Close", "@sage/xtrem-supply-chain/pages__stock_transfer_order__lines____sidebar__headerDropdownActions__title__3": "Projected stock", "@sage/xtrem-supply-chain/pages__stock_transfer_order__lines____sidebar__headerDropdownActions__title__4": "Manage allocations", "@sage/xtrem-supply-chain/pages__stock_transfer_order__lines____sidebar__headerDropdownActions__title__5": "Dimensions", "@sage/xtrem-supply-chain/pages__stock_transfer_order__lines____sidebar__headerDropdownActions__title__6": "Delete", "@sage/xtrem-supply-chain/pages__stock_transfer_order__lines____sidebar__headerDropdownActions__title__7": "Landed costs", "@sage/xtrem-supply-chain/pages__stock_transfer_order__lines____title": "Lines", "@sage/xtrem-supply-chain/pages__stock_transfer_order__linkToStockTransferShipmentLine____columns__title___id": "Shipment number", "@sage/xtrem-supply-chain/pages__stock_transfer_order__linkToStockTransferShipmentLine____columns__title__quantityInStockUnit": "Quantity in stock unit", "@sage/xtrem-supply-chain/pages__stock_transfer_order__linkToStockTransferShipmentLine____columns__title__status": "Shipment status", "@sage/xtrem-supply-chain/pages__stock_transfer_order__linkToStockTransferShipmentLine____title": "Shipment lines", "@sage/xtrem-supply-chain/pages__stock_transfer_order__noteBlock____title": "Notes", "@sage/xtrem-supply-chain/pages__stock_transfer_order__notesSection____title": "Notes", "@sage/xtrem-supply-chain/pages__stock_transfer_order__number____title": "Number", "@sage/xtrem-supply-chain/pages__stock_transfer_order__open____title": "Reopen order", "@sage/xtrem-supply-chain/pages__stock_transfer_order__order_date__cannot__be__future": "The order date cannot be later than today.", "@sage/xtrem-supply-chain/pages__stock_transfer_order__rateDescription____title": "Exchange rate", "@sage/xtrem-supply-chain/pages__stock_transfer_order__receivingSite____columns__columns__legalCompany__name__title": "Name", "@sage/xtrem-supply-chain/pages__stock_transfer_order__receivingSite____columns__columns__legalCompany__name__title__2": "ID", "@sage/xtrem-supply-chain/pages__stock_transfer_order__receivingSite____columns__title__id": "ID", "@sage/xtrem-supply-chain/pages__stock_transfer_order__receivingSite____columns__title__legalCompany__name": "Company", "@sage/xtrem-supply-chain/pages__stock_transfer_order__receivingSite____columns__title__name": "Name", "@sage/xtrem-supply-chain/pages__stock_transfer_order__receivingSite____lookupDialogTitle": "Select receiving site", "@sage/xtrem-supply-chain/pages__stock_transfer_order__receivingSite____title": "Receiving site", "@sage/xtrem-supply-chain/pages__stock_transfer_order__receivingStatus____title": "Receiving status", "@sage/xtrem-supply-chain/pages__stock_transfer_order__recompute_shipping_and_delivery_dates_dialog_content": "You are about to update the shipping date and the delivery date.", "@sage/xtrem-supply-chain/pages__stock_transfer_order__recompute_shipping_and_delivery_dates_dialog_title": "Confirm update", "@sage/xtrem-supply-chain/pages__stock_transfer_order__reject____title": "Reject", "@sage/xtrem-supply-chain/pages__stock_transfer_order__requestAllocation____title": "Allocate stock", "@sage/xtrem-supply-chain/pages__stock_transfer_order__requestApproval____title": "Submit for approval", "@sage/xtrem-supply-chain/pages__stock_transfer_order__requestDeallocation____title": "Deallocate stock", "@sage/xtrem-supply-chain/pages__stock_transfer_order__requested_delivery_cannot_be_a_not_working_day": "The requested delivery date must be on a working day.", "@sage/xtrem-supply-chain/pages__stock_transfer_order__requestedDeliveryDate____title": "Requested delivery date", "@sage/xtrem-supply-chain/pages__stock_transfer_order__save____title": "Save", "@sage/xtrem-supply-chain/pages__stock_transfer_order__save_warnings": "Warnings while saving:", "@sage/xtrem-supply-chain/pages__stock_transfer_order__select_sold_to_contact_button_text": "Select sold-to customer contact", "@sage/xtrem-supply-chain/pages__stock_transfer_order__selectedContact____columns__title__firstName": "First name", "@sage/xtrem-supply-chain/pages__stock_transfer_order__selectedContact____columns__title__lastName": "Last name", "@sage/xtrem-supply-chain/pages__stock_transfer_order__selectedContact____columns__title__title": "Title", "@sage/xtrem-supply-chain/pages__stock_transfer_order__selectedContact____lookupDialogTitle": "Select selected contact", "@sage/xtrem-supply-chain/pages__stock_transfer_order__selectedContact____title": "Selected contact", "@sage/xtrem-supply-chain/pages__stock_transfer_order__selectFromItemSites____title": "Add lines from shipping site", "@sage/xtrem-supply-chain/pages__stock_transfer_order__selectSoldToContact____title": "Select sold-to customer contact", "@sage/xtrem-supply-chain/pages__stock_transfer_order__sendEmail____title": "Send", "@sage/xtrem-supply-chain/pages__stock_transfer_order__sendEmailBlock____title": "To", "@sage/xtrem-supply-chain/pages__stock_transfer_order__sendEmailSection____title": "Send email", "@sage/xtrem-supply-chain/pages__stock_transfer_order__ship____title": "Create shipment", "@sage/xtrem-supply-chain/pages__stock_transfer_order__shipping_date_cannot_be_a_earlier_ship_before": "The shipping date ({{value}}) needs to be later than the 'Do-not-ship-before' date ({{doNotShipBeforeDate}}).", "@sage/xtrem-supply-chain/pages__stock_transfer_order__shipping_date_cannot_be_a_later_ship_after": "The shipping date ({{value}}) needs to be earlier than the 'Do-not-ship-after' date ({{doNotShipAfterDate}}).", "@sage/xtrem-supply-chain/pages__stock_transfer_order__shipping_date_cannot_be_a_less_order_date": "The shipping date ({{value}}) needs to be the same or later than the order date ({{date}}).", "@sage/xtrem-supply-chain/pages__stock_transfer_order__shippingDate____title": "Shipping date", "@sage/xtrem-supply-chain/pages__stock_transfer_order__shippingSection____title": "Shipping", "@sage/xtrem-supply-chain/pages__stock_transfer_order__shippingSectionShippingBlock____title": "Shipping", "@sage/xtrem-supply-chain/pages__stock_transfer_order__shippingStatus____title": "Shipping status", "@sage/xtrem-supply-chain/pages__stock_transfer_order__shipToAddress____columns__columns__country__title": "Name", "@sage/xtrem-supply-chain/pages__stock_transfer_order__shipToAddress____columns__columns__country__title__2": "ISO 3166-1 alpha-2 code", "@sage/xtrem-supply-chain/pages__stock_transfer_order__shipToAddress____columns__title__addressLine1": "Address line 1", "@sage/xtrem-supply-chain/pages__stock_transfer_order__shipToAddress____columns__title__addressLine2": "Address line 2", "@sage/xtrem-supply-chain/pages__stock_transfer_order__shipToAddress____columns__title__city": "City", "@sage/xtrem-supply-chain/pages__stock_transfer_order__shipToAddress____columns__title__concatenatedAddress": "Bill-to address", "@sage/xtrem-supply-chain/pages__stock_transfer_order__shipToAddress____columns__title__locationPhoneNumber": "Phone number", "@sage/xtrem-supply-chain/pages__stock_transfer_order__shipToAddress____columns__title__name": "Name", "@sage/xtrem-supply-chain/pages__stock_transfer_order__shipToAddress____dropdownActions__title": "Replace", "@sage/xtrem-supply-chain/pages__stock_transfer_order__shipToAddress____dropdownActions__title__2": "Edit", "@sage/xtrem-supply-chain/pages__stock_transfer_order__shipToAddress____dropdownActions__title__3": "Read-only", "@sage/xtrem-supply-chain/pages__stock_transfer_order__shipToAddress____title": "Ship-to address", "@sage/xtrem-supply-chain/pages__stock_transfer_order__shipToAddressLineDetail____columns__columns__country__title": "Name", "@sage/xtrem-supply-chain/pages__stock_transfer_order__shipToAddressLineDetail____columns__columns__country__title__2": "ISO 3166-1 alpha-2 code", "@sage/xtrem-supply-chain/pages__stock_transfer_order__shipToAddressLineDetail____columns__title__addressLine1": "Address line 1", "@sage/xtrem-supply-chain/pages__stock_transfer_order__shipToAddressLineDetail____columns__title__addressLine2": "Address line 2", "@sage/xtrem-supply-chain/pages__stock_transfer_order__shipToAddressLineDetail____columns__title__city": "City", "@sage/xtrem-supply-chain/pages__stock_transfer_order__shipToAddressLineDetail____columns__title__concatenatedAddress": "Bill-to address", "@sage/xtrem-supply-chain/pages__stock_transfer_order__shipToAddressLineDetail____columns__title__locationPhoneNumber": "Phone number", "@sage/xtrem-supply-chain/pages__stock_transfer_order__shipToAddressLineDetail____columns__title__name": "Name", "@sage/xtrem-supply-chain/pages__stock_transfer_order__shipToAddressLineDetail____title": "Ship-to address", "@sage/xtrem-supply-chain/pages__stock_transfer_order__shipToContact____columns__title__firstName": "First name", "@sage/xtrem-supply-chain/pages__stock_transfer_order__shipToContact____columns__title__lastName": "Last name", "@sage/xtrem-supply-chain/pages__stock_transfer_order__shipToContact____columns__title__title": "Title", "@sage/xtrem-supply-chain/pages__stock_transfer_order__shipToContact____lookupDialogTitle": "Select sold-to customer address contact detail", "@sage/xtrem-supply-chain/pages__stock_transfer_order__shipToContact____title": "Sold-to customer address contact detail", "@sage/xtrem-supply-chain/pages__stock_transfer_order__shipToCustomer____title": "Ship-to customer", "@sage/xtrem-supply-chain/pages__stock_transfer_order__shipToCustomerAddress____columns__columns__country__title": "Name", "@sage/xtrem-supply-chain/pages__stock_transfer_order__shipToCustomerAddress____columns__columns__country__title__2": "ISO 3166-1 alpha-2 code", "@sage/xtrem-supply-chain/pages__stock_transfer_order__shipToCustomerAddress____columns__columns__deliveryDetail___id__columns__columns__legalCompany__name__title": "Name", "@sage/xtrem-supply-chain/pages__stock_transfer_order__shipToCustomerAddress____columns__columns__deliveryDetail___id__columns__columns__legalCompany__name__title__2": "ID", "@sage/xtrem-supply-chain/pages__stock_transfer_order__shipToCustomerAddress____columns__columns__deliveryDetail___id__columns__title__id": "ID", "@sage/xtrem-supply-chain/pages__stock_transfer_order__shipToCustomerAddress____columns__columns__deliveryDetail___id__columns__title__id__2": "ID", "@sage/xtrem-supply-chain/pages__stock_transfer_order__shipToCustomerAddress____columns__columns__deliveryDetail___id__columns__title__legalCompany__name": "Company", "@sage/xtrem-supply-chain/pages__stock_transfer_order__shipToCustomerAddress____columns__columns__deliveryDetail___id__columns__title__name": "Name", "@sage/xtrem-supply-chain/pages__stock_transfer_order__shipToCustomerAddress____columns__columns__deliveryDetail___id__columns__title__name__2": "Name", "@sage/xtrem-supply-chain/pages__stock_transfer_order__shipToCustomerAddress____columns__columns__deliveryDetail___id__columns__title__name__3": "Name", "@sage/xtrem-supply-chain/pages__stock_transfer_order__shipToCustomerAddress____columns__columns__deliveryDetail___id__postfix": "day(s)", "@sage/xtrem-supply-chain/pages__stock_transfer_order__shipToCustomerAddress____columns__columns__deliveryDetail___id__title": "Delivery lead time", "@sage/xtrem-supply-chain/pages__stock_transfer_order__shipToCustomerAddress____columns__columns__deliveryDetail___id__title__2": "Incoterms® rule", "@sage/xtrem-supply-chain/pages__stock_transfer_order__shipToCustomerAddress____columns__columns__deliveryDetail___id__title__3": "Delivery mode", "@sage/xtrem-supply-chain/pages__stock_transfer_order__shipToCustomerAddress____columns__columns__deliveryDetail___id__title__4": "Stock site", "@sage/xtrem-supply-chain/pages__stock_transfer_order__shipToCustomerAddress____columns__title__addressLine1": "Address line 1", "@sage/xtrem-supply-chain/pages__stock_transfer_order__shipToCustomerAddress____columns__title__addressLine2": "Address line 2", "@sage/xtrem-supply-chain/pages__stock_transfer_order__shipToCustomerAddress____columns__title__city": "City", "@sage/xtrem-supply-chain/pages__stock_transfer_order__shipToCustomerAddress____columns__title__deliveryDetail___id": "Ship-to address", "@sage/xtrem-supply-chain/pages__stock_transfer_order__shipToCustomerAddress____columns__title__deliveryDetail__isPrimary": "Primary address", "@sage/xtrem-supply-chain/pages__stock_transfer_order__shipToCustomerAddress____columns__title__isActive": "Active", "@sage/xtrem-supply-chain/pages__stock_transfer_order__shipToCustomerAddress____columns__title__locationPhoneNumber": "Phone number", "@sage/xtrem-supply-chain/pages__stock_transfer_order__shipToCustomerAddress____columns__title__name": "Name", "@sage/xtrem-supply-chain/pages__stock_transfer_order__shipToCustomerAddress____columns__title__postcode": "ZIP code", "@sage/xtrem-supply-chain/pages__stock_transfer_order__shipToCustomerAddress____columns__title__region": "Region", "@sage/xtrem-supply-chain/pages__stock_transfer_order__shipToCustomerAddress____lookupDialogTitle": "Select ship-to address", "@sage/xtrem-supply-chain/pages__stock_transfer_order__shipToCustomerAddress____title": "Ship-to address", "@sage/xtrem-supply-chain/pages__stock_transfer_order__shipToLinkedAddress____columns__columns__country__title": "Name", "@sage/xtrem-supply-chain/pages__stock_transfer_order__shipToLinkedAddress____columns__columns__country__title__2": "ISO 3166-1 alpha-2 code", "@sage/xtrem-supply-chain/pages__stock_transfer_order__shipToLinkedAddress____columns__title__addressLine1": "Address line 1", "@sage/xtrem-supply-chain/pages__stock_transfer_order__shipToLinkedAddress____columns__title__addressLine2": "Address line 2", "@sage/xtrem-supply-chain/pages__stock_transfer_order__shipToLinkedAddress____columns__title__city": "City", "@sage/xtrem-supply-chain/pages__stock_transfer_order__shipToLinkedAddress____columns__title__isActive": "Active", "@sage/xtrem-supply-chain/pages__stock_transfer_order__shipToLinkedAddress____columns__title__locationPhoneNumber": "Phone number", "@sage/xtrem-supply-chain/pages__stock_transfer_order__shipToLinkedAddress____columns__title__name": "Name", "@sage/xtrem-supply-chain/pages__stock_transfer_order__shipToLinkedAddress____columns__title__postcode": "ZIP code", "@sage/xtrem-supply-chain/pages__stock_transfer_order__shipToLinkedAddress____columns__title__region": "Region", "@sage/xtrem-supply-chain/pages__stock_transfer_order__shipToLinkedAddress____lookupDialogTitle": "Select ship-to address", "@sage/xtrem-supply-chain/pages__stock_transfer_order__shipToLinkedAddress____title": "Ship-to address", "@sage/xtrem-supply-chain/pages__stock_transfer_order__site____columns__columns__legalCompany__name__title": "Name", "@sage/xtrem-supply-chain/pages__stock_transfer_order__site____columns__columns__legalCompany__name__title__2": "ID", "@sage/xtrem-supply-chain/pages__stock_transfer_order__site____columns__title__id": "ID", "@sage/xtrem-supply-chain/pages__stock_transfer_order__site____columns__title__legalCompany__name": "Company", "@sage/xtrem-supply-chain/pages__stock_transfer_order__site____columns__title__name": "Name", "@sage/xtrem-supply-chain/pages__stock_transfer_order__site____lookupDialogTitle": "Select shipping site", "@sage/xtrem-supply-chain/pages__stock_transfer_order__site____title": "Shipping site", "@sage/xtrem-supply-chain/pages__stock_transfer_order__status____title": "Status", "@sage/xtrem-supply-chain/pages__stock_transfer_order__stockSite____columns__columns__legalCompany__name__title": "Name", "@sage/xtrem-supply-chain/pages__stock_transfer_order__stockSite____columns__columns__legalCompany__name__title__2": "ID", "@sage/xtrem-supply-chain/pages__stock_transfer_order__stockSite____columns__title__businessEntity__id": "ID", "@sage/xtrem-supply-chain/pages__stock_transfer_order__stockSite____columns__title__businessEntity__name": "Name", "@sage/xtrem-supply-chain/pages__stock_transfer_order__stockSite____columns__title__legalCompany__name": "Company", "@sage/xtrem-supply-chain/pages__stock_transfer_order__stockSite____lookupDialogTitle": "Select stock site", "@sage/xtrem-supply-chain/pages__stock_transfer_order__stockSite____title": "Stock site", "@sage/xtrem-supply-chain/pages__stock_transfer_order__stockTransferOrderLineCount____title": "Number of items", "@sage/xtrem-supply-chain/pages__stock_transfer_order__stockTransferOrderLineShipToAddressDetailPanel____columns__columns__country__title": "Name", "@sage/xtrem-supply-chain/pages__stock_transfer_order__stockTransferOrderLineShipToAddressDetailPanel____columns__columns__country__title__2": "ISO 3166-1 alpha-2 code", "@sage/xtrem-supply-chain/pages__stock_transfer_order__stockTransferOrderLineShipToAddressDetailPanel____columns__title__addressLine1": "Address line 1", "@sage/xtrem-supply-chain/pages__stock_transfer_order__stockTransferOrderLineShipToAddressDetailPanel____columns__title__addressLine2": "Address line 2", "@sage/xtrem-supply-chain/pages__stock_transfer_order__stockTransferOrderLineShipToAddressDetailPanel____columns__title__city": "City", "@sage/xtrem-supply-chain/pages__stock_transfer_order__stockTransferOrderLineShipToAddressDetailPanel____columns__title__concatenatedAddress": "Bill-to address", "@sage/xtrem-supply-chain/pages__stock_transfer_order__stockTransferOrderLineShipToAddressDetailPanel____columns__title__locationPhoneNumber": "Phone number", "@sage/xtrem-supply-chain/pages__stock_transfer_order__stockTransferOrderLineShipToAddressDetailPanel____columns__title__name": "Name", "@sage/xtrem-supply-chain/pages__stock_transfer_order__stockTransferOrderLineShipToAddressDetailPanel____title": "Ship-to address", "@sage/xtrem-supply-chain/pages__stock_transfer_order__supplier____title": "Supplier", "@sage/xtrem-supply-chain/pages__stock_transfer_order__totalActualLandedCostsInCompanyCurrency____title": "Actual landed costs", "@sage/xtrem-supply-chain/pages__stock_transfer_order__workDays____title": "Work days", "@sage/xtrem-supply-chain/pages__stock_transfer_order_line_panel__display_shortage_status_false": "Stock available", "@sage/xtrem-supply-chain/pages__stock_transfer_order_line_panel__display_shortage_status_true": "Stock shortage", "@sage/xtrem-supply-chain/pages__stock_transfer_order_line_panel__quantity_in_stock_unit_error_negative": "You need to enter a quantity higher than 0.", "@sage/xtrem-supply-chain/pages__stock_transfer_order_line_panel__quantity_in_stock_unit_error_not_available": "You need to enter a quantity equal to or less than the quantity available in stock.", "@sage/xtrem-supply-chain/pages__stock_transfer_order_section_title__send_stock_transfer_order_dialog": "Send stock transfer order", "@sage/xtrem-supply-chain/pages__stock_transfer_receipt____navigationPanel__dropdownActions__title": "Post stock", "@sage/xtrem-supply-chain/pages__stock_transfer_receipt____navigationPanel__listItem__line2__title": "Receiving site", "@sage/xtrem-supply-chain/pages__stock_transfer_receipt____navigationPanel__listItem__line2Right__title": "Receipt date", "@sage/xtrem-supply-chain/pages__stock_transfer_receipt____navigationPanel__listItem__stockSite__title": "Stock site", "@sage/xtrem-supply-chain/pages__stock_transfer_receipt____navigationPanel__listItem__supplier__title": "Supplier", "@sage/xtrem-supply-chain/pages__stock_transfer_receipt____navigationPanel__listItem__title__title": "Number", "@sage/xtrem-supply-chain/pages__stock_transfer_receipt____navigationPanel__listItem__titleRight__title": "Status", "@sage/xtrem-supply-chain/pages__stock_transfer_receipt____navigationPanel__optionsMenu__title": "All open statuses", "@sage/xtrem-supply-chain/pages__stock_transfer_receipt____navigationPanel__optionsMenu__title__2": "All statuses", "@sage/xtrem-supply-chain/pages__stock_transfer_receipt____navigationPanel__optionsMenu__title__3": "Ready to process", "@sage/xtrem-supply-chain/pages__stock_transfer_receipt____navigationPanel__optionsMenu__title__4": "Received", "@sage/xtrem-supply-chain/pages__stock_transfer_receipt____navigationPanel__optionsMenu__title__5": "Posting in progress", "@sage/xtrem-supply-chain/pages__stock_transfer_receipt____navigationPanel__optionsMenu__title__6": "Error", "@sage/xtrem-supply-chain/pages__stock_transfer_receipt____objectTypePlural": "Stock transfer receipts", "@sage/xtrem-supply-chain/pages__stock_transfer_receipt____objectTypeSingular": "Stock transfer receipt", "@sage/xtrem-supply-chain/pages__stock_transfer_receipt____title": "Stock transfer receipt", "@sage/xtrem-supply-chain/pages__stock_transfer_receipt__businessEntityAddress____columns__columns__country__title": "ISO 3166-1 alpha-2 code", "@sage/xtrem-supply-chain/pages__stock_transfer_receipt__businessEntityAddress____columns__title__addressLine1": "Address line 1", "@sage/xtrem-supply-chain/pages__stock_transfer_receipt__businessEntityAddress____columns__title__addressLine2": "Address line 2", "@sage/xtrem-supply-chain/pages__stock_transfer_receipt__businessEntityAddress____columns__title__city": "City", "@sage/xtrem-supply-chain/pages__stock_transfer_receipt__businessEntityAddress____columns__title__deliveryDetail__isPrimary": "Primary address", "@sage/xtrem-supply-chain/pages__stock_transfer_receipt__businessEntityAddress____columns__title__isActive": "Active", "@sage/xtrem-supply-chain/pages__stock_transfer_receipt__businessEntityAddress____columns__title__locationPhoneNumber": "Phone number", "@sage/xtrem-supply-chain/pages__stock_transfer_receipt__businessEntityAddress____columns__title__postcode": "ZIP code", "@sage/xtrem-supply-chain/pages__stock_transfer_receipt__businessEntityAddress____columns__title__region": "Region", "@sage/xtrem-supply-chain/pages__stock_transfer_receipt__businessEntityAddress____lookupDialogTitle": "Stock site address", "@sage/xtrem-supply-chain/pages__stock_transfer_receipt__businessEntityAddress____title": "Stock site address", "@sage/xtrem-supply-chain/pages__stock_transfer_receipt__check_date_before_posting_dialog_content": "The receipt date is different to today's date. Do you want to continue?", "@sage/xtrem-supply-chain/pages__stock_transfer_receipt__check_date_before_posting_dialog_title": "Confirm receipt date before posting", "@sage/xtrem-supply-chain/pages__stock_transfer_receipt__currency____title": "Transaction currency", "@sage/xtrem-supply-chain/pages__stock_transfer_receipt__date____title": "Receipt date", "@sage/xtrem-supply-chain/pages__stock_transfer_receipt__defaultDimension____title": "Set dimensions", "@sage/xtrem-supply-chain/pages__stock_transfer_receipt__displayStatus____title": "Display status", "@sage/xtrem-supply-chain/pages__stock_transfer_receipt__externalNote____helperText": "Notes display on customer documents.", "@sage/xtrem-supply-chain/pages__stock_transfer_receipt__externalNote____title": "Customer notes", "@sage/xtrem-supply-chain/pages__stock_transfer_receipt__externalNoteLine____helperText": "Notes display on supplier documents.", "@sage/xtrem-supply-chain/pages__stock_transfer_receipt__externalNoteLine____title": "Supplier line notes", "@sage/xtrem-supply-chain/pages__stock_transfer_receipt__goToSysNotificationPage____title": "Retry", "@sage/xtrem-supply-chain/pages__stock_transfer_receipt__headerSection____title": "Header section", "@sage/xtrem-supply-chain/pages__stock_transfer_receipt__informationBlock____title": "Information", "@sage/xtrem-supply-chain/pages__stock_transfer_receipt__informationSection____title": "Information", "@sage/xtrem-supply-chain/pages__stock_transfer_receipt__internalNote____helperText": "Notes display on internal documents.", "@sage/xtrem-supply-chain/pages__stock_transfer_receipt__internalNote____title": "Internal notes", "@sage/xtrem-supply-chain/pages__stock_transfer_receipt__internalNoteLine____helperText": "Notes display on internal documents.", "@sage/xtrem-supply-chain/pages__stock_transfer_receipt__internalNoteLine____title": "Internal line notes", "@sage/xtrem-supply-chain/pages__stock_transfer_receipt__isExternalNote____title": "Add notes to supplier document", "@sage/xtrem-supply-chain/pages__stock_transfer_receipt__isExternalNoteLine____title": "Add notes to supplier document", "@sage/xtrem-supply-chain/pages__stock_transfer_receipt__isTransferHeaderNote____title": "Repeat the document notes on new documents.", "@sage/xtrem-supply-chain/pages__stock_transfer_receipt__isTransferLineNote____title": "Repeat all the line notes on new documents.", "@sage/xtrem-supply-chain/pages__stock_transfer_receipt__itemsSection____title": "Lines", "@sage/xtrem-supply-chain/pages__stock_transfer_receipt__landedCosts____columns__title__actualCostAmountInCompanyCurrency": "Actual cost amount in company currency", "@sage/xtrem-supply-chain/pages__stock_transfer_receipt__landedCosts____columns__title__landedCostType": "Type", "@sage/xtrem-supply-chain/pages__stock_transfer_receipt__landedCosts____title": "Summary by landed cost type", "@sage/xtrem-supply-chain/pages__stock_transfer_receipt__landedCostsSection____title": "Landed costs", "@sage/xtrem-supply-chain/pages__stock_transfer_receipt__landedCostsSectionBlock____title": "Total in company currency", "@sage/xtrem-supply-chain/pages__stock_transfer_receipt__lines____columns__columns__item__name__title": "Category", "@sage/xtrem-supply-chain/pages__stock_transfer_receipt__lines____columns__columns__item__name__title__2": "Expiration managed", "@sage/xtrem-supply-chain/pages__stock_transfer_receipt__lines____columns__title__actualLandedCostInCompanyCurrency": "Actual landed cost in company currency", "@sage/xtrem-supply-chain/pages__stock_transfer_receipt__lines____columns__title__isExternalNote": "Add notes to supplier document", "@sage/xtrem-supply-chain/pages__stock_transfer_receipt__lines____columns__title__item__id": "Item ID", "@sage/xtrem-supply-chain/pages__stock_transfer_receipt__lines____columns__title__item__image": "Image", "@sage/xtrem-supply-chain/pages__stock_transfer_receipt__lines____columns__title__item__name": "<PERSON><PERSON>", "@sage/xtrem-supply-chain/pages__stock_transfer_receipt__lines____columns__title__itemDescription": "Item description", "@sage/xtrem-supply-chain/pages__stock_transfer_receipt__lines____columns__title__quantityInStockUnit": "Quantity in stock unit", "@sage/xtrem-supply-chain/pages__stock_transfer_receipt__lines____columns__title__status": "Status", "@sage/xtrem-supply-chain/pages__stock_transfer_receipt__lines____columns__title__stockCostAmount": "Stock cost amount", "@sage/xtrem-supply-chain/pages__stock_transfer_receipt__lines____columns__title__stockCostUnit": "Stock cost unit", "@sage/xtrem-supply-chain/pages__stock_transfer_receipt__lines____columns__title__stockDetailStatus": "Stock details", "@sage/xtrem-supply-chain/pages__stock_transfer_receipt__lines____columns__title__stockTransactionStatus": "Stock status", "@sage/xtrem-supply-chain/pages__stock_transfer_receipt__lines____columns__title__stockUnit__name": "Stock unit", "@sage/xtrem-supply-chain/pages__stock_transfer_receipt__lines____dropdownActions__title": "Stock details", "@sage/xtrem-supply-chain/pages__stock_transfer_receipt__lines____dropdownActions__title__2": "Dimensions", "@sage/xtrem-supply-chain/pages__stock_transfer_receipt__lines____dropdownActions__title__3": "Landed costs", "@sage/xtrem-supply-chain/pages__stock_transfer_receipt__lines____inlineActions__title": "Open line panel", "@sage/xtrem-supply-chain/pages__stock_transfer_receipt__lines____mobileCard__line2__title": "Description", "@sage/xtrem-supply-chain/pages__stock_transfer_receipt__lines____mobileCard__title__title": "Product", "@sage/xtrem-supply-chain/pages__stock_transfer_receipt__lines____mobileCard__titleRight__title": "Status", "@sage/xtrem-supply-chain/pages__stock_transfer_receipt__lines____optionsMenu__title": "All statuses", "@sage/xtrem-supply-chain/pages__stock_transfer_receipt__lines____optionsMenu__title__2": "Stock details required", "@sage/xtrem-supply-chain/pages__stock_transfer_receipt__lines____sidebar__headerDropdownActions__title": "Stock details", "@sage/xtrem-supply-chain/pages__stock_transfer_receipt__lines____sidebar__headerDropdownActions__title__2": "Dimensions", "@sage/xtrem-supply-chain/pages__stock_transfer_receipt__lines____sidebar__headerDropdownActions__title__3": "Landed costs", "@sage/xtrem-supply-chain/pages__stock_transfer_receipt__lines____title": "Lines", "@sage/xtrem-supply-chain/pages__stock_transfer_receipt__linkToStockTransferShipmentLine____columns__title___id": "Shipment number", "@sage/xtrem-supply-chain/pages__stock_transfer_receipt__linkToStockTransferShipmentLine____columns__title__quantityInStockUnit": "Quantity in stock unit", "@sage/xtrem-supply-chain/pages__stock_transfer_receipt__linkToStockTransferShipmentLine____columns__title__status": "Shipment status", "@sage/xtrem-supply-chain/pages__stock_transfer_receipt__linkToStockTransferShipmentLine____title": "Shipment lines", "@sage/xtrem-supply-chain/pages__stock_transfer_receipt__noteBlock____title": "Notes", "@sage/xtrem-supply-chain/pages__stock_transfer_receipt__notesSection____title": "Notes", "@sage/xtrem-supply-chain/pages__stock_transfer_receipt__number____title": "Number", "@sage/xtrem-supply-chain/pages__stock_transfer_receipt__post____title": "Post stock", "@sage/xtrem-supply-chain/pages__stock_transfer_receipt__post_action_dialog_content": "You are about to change the status of this stock transfer receipt to 'Received'.", "@sage/xtrem-supply-chain/pages__stock_transfer_receipt__post_action_dialog_title": "Confirm posting", "@sage/xtrem-supply-chain/pages__stock_transfer_receipt__posted": "The stock transfer receipt was posted.", "@sage/xtrem-supply-chain/pages__stock_transfer_receipt__postingDetails____columns__title__financeIntegrationAppRecordId": "Accounting integration reference", "@sage/xtrem-supply-chain/pages__stock_transfer_receipt__postingDetails____columns__title__postingStatus": "Status", "@sage/xtrem-supply-chain/pages__stock_transfer_receipt__postingDetails____columns__title__targetDocumentNumber": "Document number", "@sage/xtrem-supply-chain/pages__stock_transfer_receipt__postingDetails____columns__title__targetDocumentType": "Document type", "@sage/xtrem-supply-chain/pages__stock_transfer_receipt__postingDetails____title": "Results", "@sage/xtrem-supply-chain/pages__stock_transfer_receipt__postingMessageBlock____title": "Error details", "@sage/xtrem-supply-chain/pages__stock_transfer_receipt__postingSection____title": "Posting", "@sage/xtrem-supply-chain/pages__stock_transfer_receipt__receivingBlock____title": "Receiving", "@sage/xtrem-supply-chain/pages__stock_transfer_receipt__receivingSection____title": "Receiving", "@sage/xtrem-supply-chain/pages__stock_transfer_receipt__repost____title": "Repost", "@sage/xtrem-supply-chain/pages__stock_transfer_receipt__repost_errors": "Errors while reposting:", "@sage/xtrem-supply-chain/pages__stock_transfer_receipt__save____title": "Save", "@sage/xtrem-supply-chain/pages__stock_transfer_receipt__shippingSite____title": "Shipping site", "@sage/xtrem-supply-chain/pages__stock_transfer_receipt__site____columns__title__legalCompany__name": "Company", "@sage/xtrem-supply-chain/pages__stock_transfer_receipt__site____title": "Receiving site", "@sage/xtrem-supply-chain/pages__stock_transfer_receipt__siteAddress____columns__columns__country__title": "ISO 3166-1 alpha-2 code", "@sage/xtrem-supply-chain/pages__stock_transfer_receipt__siteAddress____columns__title__addressLine1": "Address line 1", "@sage/xtrem-supply-chain/pages__stock_transfer_receipt__siteAddress____columns__title__addressLine2": "Address line 2", "@sage/xtrem-supply-chain/pages__stock_transfer_receipt__siteAddress____columns__title__city": "City", "@sage/xtrem-supply-chain/pages__stock_transfer_receipt__siteAddress____columns__title__concatenatedAddress": "Ship-to address", "@sage/xtrem-supply-chain/pages__stock_transfer_receipt__siteAddress____columns__title__locationPhoneNumber": "Phone number", "@sage/xtrem-supply-chain/pages__stock_transfer_receipt__siteAddress____columns__title__name": "Name", "@sage/xtrem-supply-chain/pages__stock_transfer_receipt__siteAddress____dropdownActions__title": "Replace", "@sage/xtrem-supply-chain/pages__stock_transfer_receipt__siteAddress____dropdownActions__title__2": "Edit", "@sage/xtrem-supply-chain/pages__stock_transfer_receipt__siteAddress____dropdownActions__title__3": "Read-only", "@sage/xtrem-supply-chain/pages__stock_transfer_receipt__siteAddress____title": "Receiving site address", "@sage/xtrem-supply-chain/pages__stock_transfer_receipt__status____title": "Status", "@sage/xtrem-supply-chain/pages__stock_transfer_receipt__stockSite____title": "Stock site", "@sage/xtrem-supply-chain/pages__stock_transfer_receipt__stockTransactionStatus____title": "Stock status", "@sage/xtrem-supply-chain/pages__stock_transfer_receipt__stockTransferReceiptLineCount____title": "Number of items", "@sage/xtrem-supply-chain/pages__stock_transfer_receipt__supplier____title": "Supplier", "@sage/xtrem-supply-chain/pages__stock_transfer_receipt__totalActualLandedCostsInCompanyCurrency____title": "Actual landed costs", "@sage/xtrem-supply-chain/pages__stock_transfer_receipt_post__stock_details_required": "You need to enter stock details for all lines before you can post.", "@sage/xtrem-supply-chain/pages__stock_transfer_shipment____navigationPanel__bulkActions__title": "Print packing slip", "@sage/xtrem-supply-chain/pages__stock_transfer_shipment____navigationPanel__bulkActions__title__2": "Print pick list", "@sage/xtrem-supply-chain/pages__stock_transfer_shipment____navigationPanel__dropdownActions__title": "Confirm", "@sage/xtrem-supply-chain/pages__stock_transfer_shipment____navigationPanel__dropdownActions__title__2": "Post stock", "@sage/xtrem-supply-chain/pages__stock_transfer_shipment____navigationPanel__dropdownActions__title__3": "<PERSON><PERSON>", "@sage/xtrem-supply-chain/pages__stock_transfer_shipment____navigationPanel__dropdownActions__title__4": "Print", "@sage/xtrem-supply-chain/pages__stock_transfer_shipment____navigationPanel__dropdownActions__title__5": "Delete", "@sage/xtrem-supply-chain/pages__stock_transfer_shipment____navigationPanel__listItem__deliveryDate__title": "Delivery date", "@sage/xtrem-supply-chain/pages__stock_transfer_shipment____navigationPanel__listItem__deliveryLeadTime__postfix": "days", "@sage/xtrem-supply-chain/pages__stock_transfer_shipment____navigationPanel__listItem__deliveryLeadTime__title": "Delivery lead time", "@sage/xtrem-supply-chain/pages__stock_transfer_shipment____navigationPanel__listItem__deliveryMode__title": "Delivery mode", "@sage/xtrem-supply-chain/pages__stock_transfer_shipment____navigationPanel__listItem__isPrinted__title": "Printed", "@sage/xtrem-supply-chain/pages__stock_transfer_shipment____navigationPanel__listItem__line2__title": "Receiving site", "@sage/xtrem-supply-chain/pages__stock_transfer_shipment____navigationPanel__listItem__line2Right__title": "Shipping date", "@sage/xtrem-supply-chain/pages__stock_transfer_shipment____navigationPanel__listItem__shipToCustomer__title": "Ship-to customer", "@sage/xtrem-supply-chain/pages__stock_transfer_shipment____navigationPanel__listItem__site__title": "Shipping site", "@sage/xtrem-supply-chain/pages__stock_transfer_shipment____navigationPanel__listItem__stockSite__title": "Stock site", "@sage/xtrem-supply-chain/pages__stock_transfer_shipment____navigationPanel__listItem__title__title": "Number", "@sage/xtrem-supply-chain/pages__stock_transfer_shipment____navigationPanel__listItem__titleRight__title": "Status", "@sage/xtrem-supply-chain/pages__stock_transfer_shipment____navigationPanel__optionsMenu__title": "All open statuses", "@sage/xtrem-supply-chain/pages__stock_transfer_shipment____navigationPanel__optionsMenu__title__2": "All statuses", "@sage/xtrem-supply-chain/pages__stock_transfer_shipment____navigationPanel__optionsMenu__title__3": "Ready to process", "@sage/xtrem-supply-chain/pages__stock_transfer_shipment____navigationPanel__optionsMenu__title__4": "Ready to ship", "@sage/xtrem-supply-chain/pages__stock_transfer_shipment____navigationPanel__optionsMenu__title__5": "Shipped", "@sage/xtrem-supply-chain/pages__stock_transfer_shipment____navigationPanel__optionsMenu__title__6": "Received", "@sage/xtrem-supply-chain/pages__stock_transfer_shipment____navigationPanel__optionsMenu__title__7": "Posting in progress", "@sage/xtrem-supply-chain/pages__stock_transfer_shipment____navigationPanel__optionsMenu__title__8": "Error", "@sage/xtrem-supply-chain/pages__stock_transfer_shipment____objectTypePlural": "Stock transfer shipments", "@sage/xtrem-supply-chain/pages__stock_transfer_shipment____objectTypeSingular": "Stock transfer shipment", "@sage/xtrem-supply-chain/pages__stock_transfer_shipment____title": "Stock transfer shipment", "@sage/xtrem-supply-chain/pages__stock_transfer_shipment__cascade_ship_to_address_update_dialog_content": "You are about to update delivery information.", "@sage/xtrem-supply-chain/pages__stock_transfer_shipment__cascade_ship_to_address_update_dialog_title": "Confirm update", "@sage/xtrem-supply-chain/pages__stock_transfer_shipment__confirm____title": "Confirm", "@sage/xtrem-supply-chain/pages__stock_transfer_shipment__date____title": "Shipping date", "@sage/xtrem-supply-chain/pages__stock_transfer_shipment__defaultDimension____title": "Set dimensions", "@sage/xtrem-supply-chain/pages__stock_transfer_shipment__deliveryDate____title": "Delivery date", "@sage/xtrem-supply-chain/pages__stock_transfer_shipment__deliveryDateHeader____title": "Estimated delivery date", "@sage/xtrem-supply-chain/pages__stock_transfer_shipment__deliveryLeadTime____postfix": "day(s)", "@sage/xtrem-supply-chain/pages__stock_transfer_shipment__deliveryLeadTime____title": "Delivery lead time", "@sage/xtrem-supply-chain/pages__stock_transfer_shipment__deliveryMode____lookupDialogTitle": "Select delivery mode", "@sage/xtrem-supply-chain/pages__stock_transfer_shipment__deliveryMode____title": "Delivery mode", "@sage/xtrem-supply-chain/pages__stock_transfer_shipment__displayStatus____title": "Display status", "@sage/xtrem-supply-chain/pages__stock_transfer_shipment__externalNote____helperText": "Notes display on customer documents.", "@sage/xtrem-supply-chain/pages__stock_transfer_shipment__externalNote____title": "Customer notes", "@sage/xtrem-supply-chain/pages__stock_transfer_shipment__externalNoteLine____helperText": "Notes display on customer documents.", "@sage/xtrem-supply-chain/pages__stock_transfer_shipment__externalNoteLine____title": "Customer line notes", "@sage/xtrem-supply-chain/pages__stock_transfer_shipment__goToSysNotificationPage____title": "Retry", "@sage/xtrem-supply-chain/pages__stock_transfer_shipment__headerSection____title": "Header section", "@sage/xtrem-supply-chain/pages__stock_transfer_shipment__incoterm____lookupDialogTitle": "Select incoterms rule", "@sage/xtrem-supply-chain/pages__stock_transfer_shipment__incoterm____title": "Incoterms® rule", "@sage/xtrem-supply-chain/pages__stock_transfer_shipment__informationBlock____title": "Information", "@sage/xtrem-supply-chain/pages__stock_transfer_shipment__informationSection____title": "Information", "@sage/xtrem-supply-chain/pages__stock_transfer_shipment__internalNote____helperText": "Notes display on internal documents.", "@sage/xtrem-supply-chain/pages__stock_transfer_shipment__internalNote____title": "Internal notes", "@sage/xtrem-supply-chain/pages__stock_transfer_shipment__internalNoteLine____helperText": "Notes display on internal documents.", "@sage/xtrem-supply-chain/pages__stock_transfer_shipment__internalNoteLine____title": "Internal line notes", "@sage/xtrem-supply-chain/pages__stock_transfer_shipment__isExternalNote____title": "Add notes to customer document", "@sage/xtrem-supply-chain/pages__stock_transfer_shipment__isExternalNoteLine____title": "Add notes to customer document", "@sage/xtrem-supply-chain/pages__stock_transfer_shipment__isPrinted____title": "Printed", "@sage/xtrem-supply-chain/pages__stock_transfer_shipment__isTransferHeaderNote____title": "Repeat the document notes on new documents.", "@sage/xtrem-supply-chain/pages__stock_transfer_shipment__isTransferLineNote____title": "Repeat all the line notes on new documents.", "@sage/xtrem-supply-chain/pages__stock_transfer_shipment__itemsSection____title": "Lines", "@sage/xtrem-supply-chain/pages__stock_transfer_shipment__lines____columns__columns__item__name__title": "Category", "@sage/xtrem-supply-chain/pages__stock_transfer_shipment__lines____columns__columns__item__name__title__2": "Expiration managed", "@sage/xtrem-supply-chain/pages__stock_transfer_shipment__lines____columns__title__allocationStatus": "Allocation status", "@sage/xtrem-supply-chain/pages__stock_transfer_shipment__lines____columns__title__isExternalNote": "Add notes to customer document", "@sage/xtrem-supply-chain/pages__stock_transfer_shipment__lines____columns__title__item__id": "Item ID", "@sage/xtrem-supply-chain/pages__stock_transfer_shipment__lines____columns__title__item__image": "Image", "@sage/xtrem-supply-chain/pages__stock_transfer_shipment__lines____columns__title__item__name": "<PERSON><PERSON>", "@sage/xtrem-supply-chain/pages__stock_transfer_shipment__lines____columns__title__itemDescription": "Item description", "@sage/xtrem-supply-chain/pages__stock_transfer_shipment__lines____columns__title__quantityAllocatedInStockUnit": "Allocated quantity", "@sage/xtrem-supply-chain/pages__stock_transfer_shipment__lines____columns__title__quantityInStockUnit": "Quantity in stock unit", "@sage/xtrem-supply-chain/pages__stock_transfer_shipment__lines____columns__title__receivingStatus": "Receiving status", "@sage/xtrem-supply-chain/pages__stock_transfer_shipment__lines____columns__title__remainingQuantityInStockUnit": "Remaining quantity", "@sage/xtrem-supply-chain/pages__stock_transfer_shipment__lines____columns__title__remainingQuantityToAllocateInStockUnit": "Remaining quantity", "@sage/xtrem-supply-chain/pages__stock_transfer_shipment__lines____columns__title__status": "Status", "@sage/xtrem-supply-chain/pages__stock_transfer_shipment__lines____columns__title__stockTransactionStatus": "Stock status", "@sage/xtrem-supply-chain/pages__stock_transfer_shipment__lines____columns__title__stockUnit__name": "Stock unit", "@sage/xtrem-supply-chain/pages__stock_transfer_shipment__lines____dropdownActions__title": "Allocate stock", "@sage/xtrem-supply-chain/pages__stock_transfer_shipment__lines____dropdownActions__title__2": "Issued stock", "@sage/xtrem-supply-chain/pages__stock_transfer_shipment__lines____dropdownActions__title__3": "Manage allocations", "@sage/xtrem-supply-chain/pages__stock_transfer_shipment__lines____dropdownActions__title__4": "Dimensions", "@sage/xtrem-supply-chain/pages__stock_transfer_shipment__lines____inlineActions__title": "Open line panel", "@sage/xtrem-supply-chain/pages__stock_transfer_shipment__lines____mobileCard__line2__title": "Description", "@sage/xtrem-supply-chain/pages__stock_transfer_shipment__lines____mobileCard__title__title": "Product", "@sage/xtrem-supply-chain/pages__stock_transfer_shipment__lines____mobileCard__titleRight__title": "Status", "@sage/xtrem-supply-chain/pages__stock_transfer_shipment__lines____optionsMenu__title": "All statuses", "@sage/xtrem-supply-chain/pages__stock_transfer_shipment__lines____optionsMenu__title__2": "Allocation required", "@sage/xtrem-supply-chain/pages__stock_transfer_shipment__lines____sidebar__headerDropdownActions__title": "Issued stock", "@sage/xtrem-supply-chain/pages__stock_transfer_shipment__lines____sidebar__headerDropdownActions__title__2": "Manage allocations", "@sage/xtrem-supply-chain/pages__stock_transfer_shipment__lines____sidebar__headerDropdownActions__title__3": "Dimensions", "@sage/xtrem-supply-chain/pages__stock_transfer_shipment__lines____sidebar__headerDropdownActions__title__4": "Delete", "@sage/xtrem-supply-chain/pages__stock_transfer_shipment__lines____title": "Lines", "@sage/xtrem-supply-chain/pages__stock_transfer_shipment__linkToStockTransferOrderLine____columns__title___id": "Order number", "@sage/xtrem-supply-chain/pages__stock_transfer_shipment__linkToStockTransferOrderLine____columns__title__quantityInStockUnit": "Quantity in stock unit", "@sage/xtrem-supply-chain/pages__stock_transfer_shipment__linkToStockTransferOrderLine____columns__title__status": "Order status", "@sage/xtrem-supply-chain/pages__stock_transfer_shipment__linkToStockTransferOrderLine____title": "Order lines", "@sage/xtrem-supply-chain/pages__stock_transfer_shipment__linkToStockTransferReceiptLine____columns__title___id": "Receipt number", "@sage/xtrem-supply-chain/pages__stock_transfer_shipment__linkToStockTransferReceiptLine____columns__title__quantityInStockUnit": "Quantity in stock unit", "@sage/xtrem-supply-chain/pages__stock_transfer_shipment__linkToStockTransferReceiptLine____columns__title__status": "Receipt status", "@sage/xtrem-supply-chain/pages__stock_transfer_shipment__linkToStockTransferReceiptLine____title": "Receipt lines", "@sage/xtrem-supply-chain/pages__stock_transfer_shipment__noteBlock____title": "Notes", "@sage/xtrem-supply-chain/pages__stock_transfer_shipment__notesSection____title": "Notes", "@sage/xtrem-supply-chain/pages__stock_transfer_shipment__number____title": "Number", "@sage/xtrem-supply-chain/pages__stock_transfer_shipment__post____title": "Post stock", "@sage/xtrem-supply-chain/pages__stock_transfer_shipment__post_action_dialog_content": "You are about to change the status of this stock transfer shipment to 'Shipped'.", "@sage/xtrem-supply-chain/pages__stock_transfer_shipment__post_action_dialog_title": "Confirm posting", "@sage/xtrem-supply-chain/pages__stock_transfer_shipment__posted": "The stock transfer shipment was posted.", "@sage/xtrem-supply-chain/pages__stock_transfer_shipment__postingDetails____columns__title__financeIntegrationAppRecordId": "Accounting integration reference", "@sage/xtrem-supply-chain/pages__stock_transfer_shipment__postingDetails____columns__title__postingStatus": "Status", "@sage/xtrem-supply-chain/pages__stock_transfer_shipment__postingDetails____columns__title__targetDocumentNumber": "Document number", "@sage/xtrem-supply-chain/pages__stock_transfer_shipment__postingDetails____columns__title__targetDocumentType": "Document type", "@sage/xtrem-supply-chain/pages__stock_transfer_shipment__postingDetails____title": "Results", "@sage/xtrem-supply-chain/pages__stock_transfer_shipment__postingMessageBlock____title": "Error details", "@sage/xtrem-supply-chain/pages__stock_transfer_shipment__postingSection____title": "Posting", "@sage/xtrem-supply-chain/pages__stock_transfer_shipment__print____title": "Print", "@sage/xtrem-supply-chain/pages__stock_transfer_shipment__receivingSite____title": "Receiving site", "@sage/xtrem-supply-chain/pages__stock_transfer_shipment__repost____title": "Repost", "@sage/xtrem-supply-chain/pages__stock_transfer_shipment__repost_errors": "Errors while reposting:", "@sage/xtrem-supply-chain/pages__stock_transfer_shipment__revert____title": "<PERSON><PERSON>", "@sage/xtrem-supply-chain/pages__stock_transfer_shipment__save____title": "Save", "@sage/xtrem-supply-chain/pages__stock_transfer_shipment__shippingBlock____title": "Shipping", "@sage/xtrem-supply-chain/pages__stock_transfer_shipment__shippingSection____title": "Shipping", "@sage/xtrem-supply-chain/pages__stock_transfer_shipment__shipToAddress____columns__columns__country__title": "ISO 3166-1 alpha-2 code", "@sage/xtrem-supply-chain/pages__stock_transfer_shipment__shipToAddress____columns__title__addressLine1": "Address line 1", "@sage/xtrem-supply-chain/pages__stock_transfer_shipment__shipToAddress____columns__title__addressLine2": "Address line 2", "@sage/xtrem-supply-chain/pages__stock_transfer_shipment__shipToAddress____columns__title__city": "City", "@sage/xtrem-supply-chain/pages__stock_transfer_shipment__shipToAddress____columns__title__concatenatedAddress": "Ship-to address", "@sage/xtrem-supply-chain/pages__stock_transfer_shipment__shipToAddress____columns__title__locationPhoneNumber": "Phone number", "@sage/xtrem-supply-chain/pages__stock_transfer_shipment__shipToAddress____columns__title__name": "Name", "@sage/xtrem-supply-chain/pages__stock_transfer_shipment__shipToAddress____dropdownActions__title": "Replace", "@sage/xtrem-supply-chain/pages__stock_transfer_shipment__shipToAddress____dropdownActions__title__2": "Edit", "@sage/xtrem-supply-chain/pages__stock_transfer_shipment__shipToAddress____dropdownActions__title__3": "Read-only", "@sage/xtrem-supply-chain/pages__stock_transfer_shipment__shipToAddress____title": "Ship-to address", "@sage/xtrem-supply-chain/pages__stock_transfer_shipment__shipToCustomer____title": "Ship-to customer", "@sage/xtrem-supply-chain/pages__stock_transfer_shipment__shipToCustomerAddress____columns__columns__country__title": "ISO 3166-1 alpha-2 code", "@sage/xtrem-supply-chain/pages__stock_transfer_shipment__shipToCustomerAddress____columns__columns__deliveryDetail___id__columns__title__legalCompany__name": "Company", "@sage/xtrem-supply-chain/pages__stock_transfer_shipment__shipToCustomerAddress____columns__columns__deliveryDetail___id__postfix": "day(s)", "@sage/xtrem-supply-chain/pages__stock_transfer_shipment__shipToCustomerAddress____columns__columns__deliveryDetail___id__title": "Incoterms® rule", "@sage/xtrem-supply-chain/pages__stock_transfer_shipment__shipToCustomerAddress____columns__columns__deliveryDetail___id__title__2": "Delivery mode", "@sage/xtrem-supply-chain/pages__stock_transfer_shipment__shipToCustomerAddress____columns__columns__deliveryDetail___id__title__3": "Delivery lead time", "@sage/xtrem-supply-chain/pages__stock_transfer_shipment__shipToCustomerAddress____columns__columns__deliveryDetail___id__title__4": "Stock site", "@sage/xtrem-supply-chain/pages__stock_transfer_shipment__shipToCustomerAddress____columns__title__addressLine1": "Address line 1", "@sage/xtrem-supply-chain/pages__stock_transfer_shipment__shipToCustomerAddress____columns__title__addressLine2": "Address line 2", "@sage/xtrem-supply-chain/pages__stock_transfer_shipment__shipToCustomerAddress____columns__title__city": "City", "@sage/xtrem-supply-chain/pages__stock_transfer_shipment__shipToCustomerAddress____columns__title__deliveryDetail___id": "Ship-to address", "@sage/xtrem-supply-chain/pages__stock_transfer_shipment__shipToCustomerAddress____columns__title__deliveryDetail__isPrimary": "Primary ship-to address", "@sage/xtrem-supply-chain/pages__stock_transfer_shipment__shipToCustomerAddress____columns__title__isActive": "Active", "@sage/xtrem-supply-chain/pages__stock_transfer_shipment__shipToCustomerAddress____columns__title__locationPhoneNumber": "Phone number", "@sage/xtrem-supply-chain/pages__stock_transfer_shipment__shipToCustomerAddress____columns__title__postcode": "ZIP code", "@sage/xtrem-supply-chain/pages__stock_transfer_shipment__shipToCustomerAddress____columns__title__region": "Region", "@sage/xtrem-supply-chain/pages__stock_transfer_shipment__shipToCustomerAddress____lookupDialogTitle": "Select ship-to address", "@sage/xtrem-supply-chain/pages__stock_transfer_shipment__shipToCustomerAddress____title": "Ship-to address", "@sage/xtrem-supply-chain/pages__stock_transfer_shipment__site____columns__title__legalCompany__name": "Company", "@sage/xtrem-supply-chain/pages__stock_transfer_shipment__site____title": "Shipping site", "@sage/xtrem-supply-chain/pages__stock_transfer_shipment__status____title": "Status", "@sage/xtrem-supply-chain/pages__stock_transfer_shipment__stockSite____title": "Stock site", "@sage/xtrem-supply-chain/pages__stock_transfer_shipment__stockTransactionStatus____title": "Stock status", "@sage/xtrem-supply-chain/pages__stock_transfer_shipment__stockTransferShipmentLineCount____title": "Number of items", "@sage/xtrem-supply-chain/pages__stock_transfer_shipment__trackingNumber____title": "Tracking number", "@sage/xtrem-supply-chain/pages__stock_transfer_shipment__workDays____title": "Work days", "@sage/xtrem-supply-chain/pages__stock_transfer_shipment_shipment__printed": "The stock transfer shipment was printed.", "@sage/xtrem-supply-chain/pages__transfer_order__create_shipment_dialog_content": "You are about to create a shipment from this transfer order.", "@sage/xtrem-supply-chain/pages__transfer_order__create_shipment_dialog_title": "Confirm shipment creation", "@sage/xtrem-supply-chain/pages__transfer_order__printed": "The stock transfer order was printed", "@sage/xtrem-supply-chain/pages__transfer_order__printed_status_not_updated": "Unable to update stock transfer order {{number}} to printed status.", "@sage/xtrem-supply-chain/pages__transfer-order__receiving-site-not-a-customer": "The receiving site is not a customer.", "@sage/xtrem-supply-chain/pages__transfer-order__shipping-site-not-a-supplier": "The shipping site is not a supplier.", "@sage/xtrem-supply-chain/pages__work_order_planning____navigationPanel__bulkActions__title": "Create order", "@sage/xtrem-supply-chain/pages__work_order_planning____navigationPanel__listItem__companyName__title": "Company name", "@sage/xtrem-supply-chain/pages__work_order_planning____navigationPanel__listItem__itemDescription__title": "Item description", "@sage/xtrem-supply-chain/pages__work_order_planning____navigationPanel__listItem__itemName__title": "<PERSON><PERSON>", "@sage/xtrem-supply-chain/pages__work_order_planning____navigationPanel__listItem__line2__title": "Item ID", "@sage/xtrem-supply-chain/pages__work_order_planning____navigationPanel__listItem__preferredProcess__title": "Preferred process", "@sage/xtrem-supply-chain/pages__work_order_planning____navigationPanel__listItem__requirementDate__title": "End date", "@sage/xtrem-supply-chain/pages__work_order_planning____navigationPanel__listItem__siteName__title": "Site name", "@sage/xtrem-supply-chain/pages__work_order_planning____navigationPanel__listItem__startDate__title": "Start date", "@sage/xtrem-supply-chain/pages__work_order_planning____navigationPanel__listItem__stockQuantity__title": "Stock quantity", "@sage/xtrem-supply-chain/pages__work_order_planning____navigationPanel__listItem__suggestionDate__title": "Suggestion date", "@sage/xtrem-supply-chain/pages__work_order_planning____navigationPanel__listItem__suggestionSource__title": "Suggestion source", "@sage/xtrem-supply-chain/pages__work_order_planning____navigationPanel__listItem__title__title": "Company", "@sage/xtrem-supply-chain/pages__work_order_planning____navigationPanel__listItem__titleRight__title": "Site", "@sage/xtrem-supply-chain/pages__work_order_planning____navigationPanel__listItem__workOrderCategory__title": "Category", "@sage/xtrem-supply-chain/pages__work_order_planning____navigationPanel__listItem__workOrderType__title": "Type", "@sage/xtrem-supply-chain/pages__work_order_planning____navigationPanel__optionsMenu__title": "All", "@sage/xtrem-supply-chain/pages__work_order_planning____title": "Work order planning", "@sage/xtrem-supply-chain/pages__work_order_planning__company____columns__title__id": "ID ", "@sage/xtrem-supply-chain/pages__work_order_planning__company____title": "Company", "@sage/xtrem-supply-chain/pages__work_order_planning__item____title": "<PERSON><PERSON>", "@sage/xtrem-supply-chain/pages__work_order_planning__mainSection____title": "General", "@sage/xtrem-supply-chain/pages__work_order_planning__preferredProcess____title": "Preferred process", "@sage/xtrem-supply-chain/pages__work_order_planning__requirementDate____title": "End date", "@sage/xtrem-supply-chain/pages__work_order_planning__save____title": "Save", "@sage/xtrem-supply-chain/pages__work_order_planning__startDate____title": "Start date", "@sage/xtrem-supply-chain/pages__work_order_planning__status____title": "Create order status", "@sage/xtrem-supply-chain/pages__work_order_planning__stock_quantity_zero_or_less": "The stock quantity cannot be less than or equal to 0.", "@sage/xtrem-supply-chain/pages__work_order_planning__stockQuantity____title": "Stock quantity", "@sage/xtrem-supply-chain/pages__work_order_planning__stockSite____title": "Stock site", "@sage/xtrem-supply-chain/pages__work_order_planning__stockUnit____columns__title__name": "Name", "@sage/xtrem-supply-chain/pages__work_order_planning__stockUnit____columns__title__symbol": "Symbol", "@sage/xtrem-supply-chain/pages__work_order_planning__suggestionDate____title": "Suggestion date", "@sage/xtrem-supply-chain/pages__work_order_planning__suggestionSource____title": "Suggestion source", "@sage/xtrem-supply-chain/pages__work_order_planning__suggestionUser____title": "Suggestion user", "@sage/xtrem-supply-chain/pages__work_order_planning__workOrderCategory____columns__title__billOfMaterial": "Bill of material", "@sage/xtrem-supply-chain/pages__work_order_planning__workOrderCategory____columns__title__id": "ID", "@sage/xtrem-supply-chain/pages__work_order_planning__workOrderCategory____columns__title__name": "Name", "@sage/xtrem-supply-chain/pages__work_order_planning__workOrderCategory____columns__title__routing": "Routing", "@sage/xtrem-supply-chain/pages__work_order_planning__workOrderCategory____lookupDialogTitle": "Select category", "@sage/xtrem-supply-chain/pages__work_order_planning__workOrderCategory____title": "Category", "@sage/xtrem-supply-chain/pages__work_order_planning__workOrderName____title": "Name", "@sage/xtrem-supply-chain/pages__work_order_planning__workOrderNumber____title": "Work order number", "@sage/xtrem-supply-chain/pages__work_order_planning__workOrderType____title": "Type", "@sage/xtrem-supply-chain/pages_functions__stock_transfer_order_create_shipment_action_is_shipped": "Transfer shipment created ({{shipmentNumberCreated}})", "@sage/xtrem-supply-chain/pages_functions__stock_transfer_order_create_shipment_action_not_allowed_exception": "Could not create shipment.", "@sage/xtrem-supply-chain/pages_sidebar_block_title_allocation": "Allocation", "@sage/xtrem-supply-chain/pages_sidebar_block_title_delivery": "Delivery", "@sage/xtrem-supply-chain/pages_sidebar_block_title_receipt": "Receipt", "@sage/xtrem-supply-chain/pages_sidebar_block_title_stock": "Stock", "@sage/xtrem-supply-chain/pages_sidebar_tab_title_address": "Address", "@sage/xtrem-supply-chain/pages_sidebar_tab_title_delivery": "Delivery", "@sage/xtrem-supply-chain/pages_sidebar_tab_title_information": "Information", "@sage/xtrem-supply-chain/pages_sidebar_tab_title_line_notes": "Line notes", "@sage/xtrem-supply-chain/pages_sidebar_tab_title_origin": "Origin", "@sage/xtrem-supply-chain/pages_sidebar_tab_title_progress": "Progress", "@sage/xtrem-supply-chain/pages_sidebar_tab_title_stock": "Stock", "@sage/xtrem-supply-chain/pages-confirm-confirm": "Confirm", "@sage/xtrem-supply-chain/pages-confirm-continue": "Continue", "@sage/xtrem-supply-chain/pages-confirm-create": "Create", "@sage/xtrem-supply-chain/pages-confirm-update": "Update", "@sage/xtrem-supply-chain/permission__approve__name": "Approve", "@sage/xtrem-supply-chain/permission__confirm__name": "Confirm", "@sage/xtrem-supply-chain/permission__manage__name": "Manage", "@sage/xtrem-supply-chain/permission__post__name": "Post", "@sage/xtrem-supply-chain/permission__print__name": "Print", "@sage/xtrem-supply-chain/permission__read__name": "Read", "@sage/xtrem-supply-chain/stock_transfer_order_line__quantity_bellow_already_shipped_quantity": "The stock transfer order line quantity cannot be lower than the quantity already shipped."}