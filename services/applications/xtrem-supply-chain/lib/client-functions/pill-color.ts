import type { PreferredProcess } from '@sage/xtrem-master-data-api';
import type { SupplyPlanningSource, SupplyPlanningStatus } from '@sage/xtrem-supply-chain-api';
import type { ColoredElement } from '@sage/xtrem-system-api';
import { colorfulPillPattern } from '@sage/xtrem-system/build/lib/client-functions/color-pattern';

export function setEnumPreferredProcessColor(
    preferredProcess: PreferredProcess,
    coloredElement: ColoredElement,
): string {
    switch (preferredProcess) {
        case 'production':
            return colorfulPillPattern.outlinedInformation[coloredElement];
        case 'purchasing':
            return colorfulPillPattern.outlinedNeutral[coloredElement];
        default:
            return colorfulPillPattern.default[coloredElement];
    }
}

export function setEnumSupplyPlanningSourceColor(
    preferredProcess: SupplyPlanningSource,
    coloredElement: ColoredElement,
): string {
    // TODO: remove cast when more than one enum value exist
    switch (preferredProcess as SupplyPlanningSource | string) {
        case 'MRP':
            return colorfulPillPattern.outlinedPositive[coloredElement];
        case '':
        default:
            return colorfulPillPattern.default[coloredElement];
    }
}

export function setEnumSupplyPlanningStatusColor(
    preferredProcess: SupplyPlanningStatus,
    coloredElement: ColoredElement,
): string {
    switch (preferredProcess) {
        case 'createOrder':
            return colorfulPillPattern.filledInformation[coloredElement];
        case 'pending':
            return colorfulPillPattern.filledNeutral[coloredElement];
        default:
            return colorfulPillPattern.default[coloredElement];
    }
}
