import type { ExtractEdgesPartial } from '@sage/xtrem-client';
import * as attributesAndDimensions from '@sage/xtrem-finance-data/build/lib/client-functions/attributes-and-dimensions';
import * as dimensionPanelHelpers from '@sage/xtrem-finance-data/build/lib/client-functions/dimension-panel-helpers';
import type * as dimensionPanelInterfaces from '@sage/xtrem-finance-data/build/lib/client-functions/interfaces/index';
import type { Customer } from '@sage/xtrem-master-data-api';
import type { GraphApi, Site } from '@sage/xtrem-system-api';
import type * as ui from '@sage/xtrem-ui';

// TODO: this function needs to be generic and moved to a more appropriate place
/**
 * Get a string with all attributes defaulted from item and default dimensions attributes to show on 'Set dimensions'
 * dialog called from main list
 * @param page
 * @param site
 * @param customer
 * @returns Promise<{ defaultedFromItem: string; defaultDimensionsAttributes: dimensionPanelInterfaces.DefaultDimensions }>
 */
export async function getValuesForSetDimensionsFromMainList(options: {
    page: ui.Page<GraphApi>;
    site: ExtractEdgesPartial<Site> | null;
    customer: ExtractEdgesPartial<Customer> | null;
}): Promise<{ defaultedFromItem: string; defaultDimensionsAttributes: dimensionPanelInterfaces.DefaultDimensions }> {
    const defaultedFromItem = await attributesAndDimensions.getAttributesAndDimensionsFromItem({
        page: options.page,
        dimensionDefinitionLevel: 'salesDirect',
        companyId: Number(options.site?.legalCompany?._id),
    });
    const defaultDimensionsAttributes = await attributesAndDimensions.initDefaultDimensions({
        page: options.page,
        dimensionDefinitionLevel: 'salesDirect',
        site: options.site || null,
        customer: options.customer,
    });

    return { defaultedFromItem, defaultDimensionsAttributes };
}

// TODO: this function needs to be generic and moved to a more appropriate place - why is this function repeated
/**
 * Get dimensions and attributes to set them in sales lines from main grid
 * @param line
 * @param stockTransferPage //stockTransferOrderPage, stockTransferShipmentPage, stockTransferReceiptPage
 */
export async function getDimensionsForStockTransferLines(options: {
    line: ui.PartialNodeWithId<Node>;
    stockTransferPage: ui.Page<GraphApi>;
    defaultDimensionsAttributes: dimensionPanelInterfaces.DefaultDimensions;
    defaultedFromItem?: string;
}) {
    const stockTransferBase = dimensionPanelHelpers.fromPartialToBaseDocumentLineWithAnalytical(options.line);
    const rowWithDimensions = dimensionPanelHelpers.editDisplayDimensions(
        options.stockTransferPage,
        {
            documentLine: stockTransferBase,
        },
        {
            editable: true,
            calledFromMainGrid: true,
        },
        options.defaultDimensionsAttributes,
        options.defaultedFromItem,
    );
    if ((await rowWithDimensions).storedAttributes !== '' || (await rowWithDimensions).storedDimensions !== '') {
        return {
            resultDimensionsToSet: (await rowWithDimensions).storedDimensions,
            resultAttributesToSet: (await rowWithDimensions).storedAttributes,
            resultDataDetermined: true,
        };
    }
    return {
        resultDimensionsToSet: '',
        resultAttributesToSet: '',
        resultDataDetermined: false,
    };
}
