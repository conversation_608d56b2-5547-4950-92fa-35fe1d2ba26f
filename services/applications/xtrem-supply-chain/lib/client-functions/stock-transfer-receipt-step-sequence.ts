import * as stepSequenceDocument from '@sage/xtrem-distribution/build/lib/client-functions/step-sequence';
import type { Dict } from '@sage/xtrem-shared';
import type * as ui from '@sage/xtrem-ui';
import type { StockTransferReceiptStepSequenceStatus } from './interfaces/stock-transfer-receipt';

export function getStepSequence() {
    return [stepSequenceDocument.create, stepSequenceDocument.post];
}

function _setStepSequenceStatusObject(
    stepSequenceValues: StockTransferReceiptStepSequenceStatus,
): Dict<ui.StepSequenceStatus> {
    return {
        [stepSequenceDocument.create]: stepSequenceValues.create,
        [stepSequenceDocument.post]: stepSequenceValues.post,
    };
}

export function getDisplayStatusStepSequence(
    status: string,
    stockTransactionStatus: string,
): Dict<ui.StepSequenceStatus> {
    if (['inProgress', 'error'].includes(stockTransactionStatus || '')) {
        return _setStepSequenceStatusObject({
            create: 'complete',
            post: 'current',
        });
    }

    if (stockTransactionStatus === 'completed') {
        return _setStepSequenceStatusObject({
            create: 'complete',
            post: 'complete',
        });
    }

    if (status === 'readyToProcess') {
        return _setStepSequenceStatusObject({
            create: 'complete',
            post: 'incomplete',
        });
    }

    return _setStepSequenceStatusObject({
        create: 'current',
        post: 'incomplete',
    });
}
