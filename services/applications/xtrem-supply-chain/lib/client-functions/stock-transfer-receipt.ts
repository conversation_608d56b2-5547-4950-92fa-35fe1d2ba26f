import { DateValue } from '@sage/xtrem-date-time';
import * as dimensionPanelHelpers from '@sage/xtrem-finance-data/build/lib/client-functions/dimension-panel-helpers';
import type * as LandedCostInterfaces from '@sage/xtrem-landed-cost/build/lib/client-functions/interfaces';
import * as ui from '@sage/xtrem-ui';
import type { StockTransferReceipt as StockTransferReceiptPage } from '../pages/stock-transfer-receipt';

function mapLandedCostSummary(aggregateLandedCosts: LandedCostInterfaces.LandedCostTypeSummaryLine[]): {
    tableLandedCostsValues: LandedCostInterfaces.LandedCostTypeSummaryLineBinding[];
    total: number;
} {
    let total = 0;
    const tableLandedCostsValues = aggregateLandedCosts.map((line, index) => {
        total += Number(line.actualCostAmountInCompanyCurrency);
        return {
            _id: String(index + 1),
            landedCostType: line.landedCostType,
            actualCostAmountInCompanyCurrency: Number(line.actualCostAmountInCompanyCurrency),
            actualAllocatedCostAmountInCompanyCurrency: Number(line.actualAllocatedCostAmountInCompanyCurrency),
        } as LandedCostInterfaces.LandedCostTypeSummaryLineBinding;
    });
    return { tableLandedCostsValues, total };
}

export function disableSomeHeaderPropertiesIfLines(pageInstance: StockTransferReceiptPage) {
    const isDisabled = pageInstance.lines.value.length > 0;
    pageInstance.site.isReadOnly = isDisabled;
}
function hasStockDetailEntered(pageInstance: StockTransferReceiptPage) {
    return pageInstance.lines.value.some(line => line.stockDetailStatus === 'entered');
}

export function hasDateInTheFuture(date: string | null): boolean {
    if (!date) {
        return false;
    }
    return date > DateValue.today().toString();
}

export function setDateToReadOnly(pageInstance: StockTransferReceiptPage, date: string | null) {
    return hasStockDetailEntered(pageInstance) && hasDateInTheFuture(date);
}

export function initPage(pageInstance: StockTransferReceiptPage) {
    pageInstance.landedCostsSection.isHidden = true;
    if (pageInstance.$.recordId) {
        pageInstance.date.isDisabled = setDateToReadOnly(pageInstance, pageInstance.date.value);
        const isHeaderDisabled = pageInstance.status.value === 'received';
        pageInstance.date.isDisabled = isHeaderDisabled;
        pageInstance.businessEntityAddress.isDisabled = isHeaderDisabled;
        pageInstance.siteAddress.isDisabled = isHeaderDisabled;
        if (
            pageInstance.$.isServiceOptionEnabled('landedCostOption') &&
            pageInstance.$.isServiceOptionEnabled('landedCostStockTransferOption') &&
            pageInstance.jsonAggregateLandedCostTypes.value
        ) {
            pageInstance.landedCostsSection.isHidden = pageInstance.status.value === 'draft';
            const landedCostSummary = mapLandedCostSummary(
                JSON.parse(
                    pageInstance.jsonAggregateLandedCostTypes.value,
                ) as LandedCostInterfaces.LandedCostTypeSummaryLine[],
            );
            pageInstance.landedCosts.value = landedCostSummary.tableLandedCostsValues;
            pageInstance.totalActualLandedCostsInCompanyCurrency.value = landedCostSummary.total;
        }
    } else {
        pageInstance.date.value = new Date(Date.now()).toISOString().substring(0, 10);
        disableSomeHeaderPropertiesIfLines(pageInstance);
    }

    if (!pageInstance.status.value) {
        pageInstance.status.value = 'readyToProcess';
    }

    pageInstance._defaultDimensionsAttributes = dimensionPanelHelpers.initDefaultDimensions();
}

export async function fillStockTransferShipmentLines(pageInstance: StockTransferReceiptPage, rowId: string) {
    const oldIsDirty = pageInstance.$.isDirty;
    pageInstance.linkToStockTransferShipmentLine.value.forEach(podLine => {
        pageInstance.linkToStockTransferShipmentLine.removeRecord(podLine._id);
    });
    const lines = await pageInstance.$.graph
        .node('@sage/xtrem-supply-chain/StockTransferReceiptLineToStockTransferShipmentLine')
        .query(
            ui.queryUtils.edgesSelector(
                {
                    to: {
                        _id: true,
                        item: {
                            _id: true,
                            name: true,
                        },
                        status: true,
                        quantity: true,
                        unit: {
                            _id: true,
                            name: true,
                            decimalDigits: true,
                            id: true,
                            symbol: true,
                        },
                        quantityInStockUnit: true,
                        stockUnit: {
                            _id: true,
                            name: true,
                            decimalDigits: true,
                            id: true,
                            symbol: true,
                        },
                        document: {
                            _id: true,
                            number: true,
                        },
                    },
                },
                {
                    filter: { from: { _id: rowId } },
                },
            ),
        )
        .execute();
    pageInstance.linkToStockTransferShipmentLine.value = lines.edges.map(e => e.node.to);
    pageInstance.linkToStockTransferShipmentLine.isHidden =
        pageInstance.linkToStockTransferShipmentLine.value.length === 0;
    if (!oldIsDirty && pageInstance.$.isDirty) {
        pageInstance.$.setPageClean();
    }
}

export async function isStockDetailEntryEnabled(pageInstance: StockTransferReceiptPage) {
    if (hasDateInTheFuture(pageInstance.date.value)) {
        await pageInstance.$.dialog.message(
            'error',
            ui.localize(
                '@sage/xtrem-supply-chain/pages__stock__transfer_receipt__stock_details_control_title',
                'Stock details',
            ),
            ui.localize(
                '@sage/xtrem-supply-chain/pages__stock__transfer_receipt__stock_details_control_message',
                'Stock details cannot be added to the receipt, the Receipt date is in the future.',
            ),
        );
        return false;
    }
    return true;
}
