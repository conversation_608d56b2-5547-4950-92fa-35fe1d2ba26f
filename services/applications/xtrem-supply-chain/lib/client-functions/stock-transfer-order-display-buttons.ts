import type { StockTransferOrderLineBinding } from '@sage/xtrem-supply-chain-api';
import type * as isDisabledButtonsCommon from '@sage/xtrem-system/build/lib/client-functions/interfaces/is-disabled-buttons';
import type * as isHiddenButtonsCommon from '@sage/xtrem-system/build/lib/client-functions/interfaces/is-hidden-buttons';
import type * as isDisabledButtons from './interfaces/stock-transfer-order-is-disabled-buttons';
import type * as isHiddenButtons from './interfaces/stock-transfer-order-is-hidden-buttons';

export function isDisabledButtonSaveOrCancelAction(
    data: isDisabledButtonsCommon.SaveOrCancelParameters<isDisabledButtons.SaveOrCancelParameters>,
) {
    if (data.recordId) {
        if (
            data.parameters.status === 'draft' ||
            data.parameters.status === 'pending' ||
            data.parameters.status === 'inProgress'
        ) {
            return !data.isDirty;
        }
        return true;
    }
    return !data.isDirty;
}

export function isDisabledButtonDeleteAction(
    data: isDisabledButtonsCommon.DeleteParameters<isDisabledButtons.DeleteParameters>,
) {
    if (
        data.recordId &&
        (data.parameters.status === 'draft' ||
            data.parameters.status === 'pending' ||
            (data.parameters.status === 'closed' && data.parameters.shippingStatus === 'notShipped'))
    ) {
        return false;
    }
    return true;
}

export function isHiddenButtonDeleteAction(
    data: isHiddenButtonsCommon.DeleteParameters<isHiddenButtons.DeleteParameters>,
) {
    if (data.recordId && (data.parameters.status === 'draft' || data.parameters.status === 'pendingApproval')) {
        return false;
    }
    return true;
}

export function isHiddenButtonConfirmAction(
    data: isHiddenButtonsCommon.BusinessButtonParameters<isHiddenButtons.ConfirmParameters>,
) {
    if (
        data.recordId &&
        data.parameters.status !== 'closed' &&
        !data.parameters.isApprovalManaged &&
        !['pendingApproval', 'approved', 'rejected', 'changeRequested', 'confirmed'].includes(
            data.parameters.approvalStatus || '',
        )
    ) {
        return data.isDirty ?? false;
    }
    return true;
}

export function isHiddenButtonShipAction(
    data: isHiddenButtonsCommon.BusinessButtonParameters<isHiddenButtons.ShipParameters>,
) {
    if (
        data.recordId &&
        data.parameters.status !== 'draft' &&
        data.parameters.status !== 'closed' &&
        data.parameters.status !== 'inProgress' &&
        data.parameters.allocationRequestStatus !== 'inProgress'
    ) {
        return data.isDirty ?? false;
    }
    return true;
}

export function isHiddenButtonOpenAction(
    data: isHiddenButtonsCommon.BusinessButtonParameters<isHiddenButtons.OpenParameters>,
) {
    if (
        data.recordId &&
        data.parameters.status === 'closed' &&
        data.parameters.shippingStatus === 'notShipped' &&
        data.parameters.approvalStatus !== 'rejected'
    ) {
        return data.isDirty ?? false;
    }
    return true;
}

export function isHiddenButtonDefaultDimensionAction(
    data: isHiddenButtonsCommon.HeaderDropDownActionButtonParameters<isHiddenButtons.DefaultDimensionParameters>,
) {
    if (
        data.recordId &&
        data.parameters &&
        data.parameters.status &&
        (['inProgress', 'error', 'closed'].includes(data.parameters.status) ||
            (data.parameters.shippingStatus && data.parameters.shippingStatus !== 'notShipped'))
    ) {
        return true;
    }
    return false;
}

export function isDisabledButtonDefaultDimensionAction(
    data: isDisabledButtonsCommon.HeaderDropDownActionButtonParameters<isDisabledButtons.DefaultDimensionParameters>,
) {
    if (
        data.parameters &&
        data.parameters.site &&
        data.parameters.shipToCustomer &&
        data.parameters.orderDate &&
        data.parameters.currency
    ) {
        return false;
    }
    return true;
}

export function isHiddenButtonRequestAllocationAction(
    data: isHiddenButtonsCommon.HeaderDropDownActionButtonParameters<isHiddenButtons.RequestAllocationParameters>,
) {
    return (
        !data.parameters ||
        !data.recordId ||
        data.parameters.allocationRequestStatus === 'inProgress' ||
        !['pending', 'inProgress'].includes(data.parameters.status || '') ||
        !['notAllocated', 'partiallyAllocated'].includes(data.parameters.allocationStatus || '')
    );
}

export function isHiddenButtonRequestDeallocationAction(
    data: isHiddenButtonsCommon.HeaderDropDownActionButtonParameters<isHiddenButtons.RequestAllocationParameters>,
) {
    return (
        !data.parameters ||
        !data.recordId ||
        data.parameters.allocationRequestStatus === 'inProgress' ||
        !['pending', 'inProgress'].includes(data.parameters.status || '') ||
        !['allocated', 'partiallyAllocated'].includes(data.parameters.allocationStatus || '')
    );
}

export function isHiddenButtonApproveOrRejectAction(
    data: isHiddenButtonsCommon.BusinessButtonParameters<isHiddenButtons.ApproveOrRejectParameters>,
) {
    if (data.recordId && data.parameters.status !== 'closed' && data.parameters.approvalStatus === 'pendingApproval') {
        return data.isDirty ?? false;
    }
    return true;
}

export function isHiddenButtonRequestApprovalAction(
    data: isHiddenButtonsCommon.BusinessButtonParameters<isHiddenButtons.RequestApprovalParameters>,
) {
    if (
        data.recordId &&
        data.parameters.status !== 'closed' &&
        data.parameters.isApprovalManaged &&
        !['pendingApproval', 'approved', 'rejected', 'confirmed'].includes(data.parameters.approvalStatus || '')
    ) {
        return data.isDirty ?? false;
    }
    return true;
}

function commonIsHiddenProperties(
    data:
        | isHiddenButtonsCommon.HeaderDropDownActionButtonParameters<isHiddenButtons.SelectFromItemSitesParameters>
        | isDisabledButtonsCommon.PhantomRowButtonParameters<isDisabledButtons.LinePhantomRowParameters>,
) {
    return (
        data.parameters &&
        data.parameters.status &&
        data.parameters.status === 'draft' &&
        data.parameters.site &&
        data.parameters.shipToCustomer &&
        data.parameters.receivingSite &&
        data.parameters.orderDate &&
        data.parameters.currency
    );
}

export function isDisabledLinePhantomRow(
    data: isDisabledButtonsCommon.PhantomRowButtonParameters<isDisabledButtons.LinePhantomRowParameters>,
) {
    if (commonIsHiddenProperties(data)) {
        return false;
    }
    return true;
}

export function isHiddenButtonSelectFromItemSitesAction(
    data: isHiddenButtonsCommon.HeaderDropDownActionButtonParameters<isHiddenButtons.SelectFromItemSitesParameters>,
) {
    return isDisabledLinePhantomRow(data);
}

export function isHiddenSidebarStockSection(recordValue?: StockTransferOrderLineBinding | undefined): boolean {
    return !['inProgress', 'pending', 'draft'].includes(recordValue?.status ?? '') || !recordValue?.item.isStockManaged;
}

export function isHiddenSidebarAllocationBlock(recordValue?: StockTransferOrderLineBinding | undefined): boolean {
    return !recordValue || ['closed', 'draft'].includes(recordValue.status) || !recordValue.item.isStockManaged;
}
