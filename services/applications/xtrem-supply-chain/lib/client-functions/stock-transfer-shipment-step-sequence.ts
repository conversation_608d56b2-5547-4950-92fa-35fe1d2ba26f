import * as stepSequenceDocument from '@sage/xtrem-distribution/build/lib/client-functions/step-sequence';
import type { Dict } from '@sage/xtrem-shared';
import type * as ui from '@sage/xtrem-ui';
import type { StockTransferShipmentStepSequenceStatus } from './interfaces/stock-transfer-shipment';

export function getStepSequence() {
    return [stepSequenceDocument.create, stepSequenceDocument.confirm, stepSequenceDocument.post];
}

function _setStepSequenceStatusObject(
    stepSequenceValues: StockTransferShipmentStepSequenceStatus,
): Dict<ui.StepSequenceStatus> {
    return {
        [stepSequenceDocument.create]: stepSequenceValues.create,
        [stepSequenceDocument.confirm]: stepSequenceValues.confirm,
        [stepSequenceDocument.post]: stepSequenceValues.post,
    };
}

export function getDisplayStatusStepSequence(
    status: string,
    stockTransactionStatus: string,
): Dict<ui.StepSequenceStatus> {
    if (['inProgress', 'error'].includes(stockTransactionStatus || '')) {
        return _setStepSequenceStatusObject({
            create: 'complete',
            confirm: 'complete',
            post: 'current',
        });
    }

    if (stockTransactionStatus === 'completed') {
        return _setStepSequenceStatusObject({
            create: 'complete',
            confirm: 'complete',
            post: 'complete',
        });
    }

    if (status === 'readyToProcess') {
        return _setStepSequenceStatusObject({
            create: 'complete',
            confirm: 'current',
            post: 'incomplete',
        });
    }

    if (status === 'readyToShip') {
        return _setStepSequenceStatusObject({
            create: 'complete',
            confirm: 'complete',
            post: 'current',
        });
    }

    if (status === 'shipped') {
        return _setStepSequenceStatusObject({
            create: 'complete',
            confirm: 'complete',
            post: 'complete',
        });
    }

    return _setStepSequenceStatusObject({
        create: 'current',
        confirm: 'incomplete',
        post: 'incomplete',
    });
}
