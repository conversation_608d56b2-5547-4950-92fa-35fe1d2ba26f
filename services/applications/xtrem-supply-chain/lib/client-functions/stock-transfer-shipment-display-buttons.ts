import type * as isDisabledButtonsCommon from '@sage/xtrem-system/build/lib/client-functions/interfaces/is-disabled-buttons';
import type * as isHiddenButtonsCommon from '@sage/xtrem-system/build/lib/client-functions/interfaces/is-hidden-buttons';
import type * as isDisabledButtons from './interfaces/stock-transfer-shipment-is-disabled-buttons';
import type * as isHiddenButtons from './interfaces/stock-transfer-shipment-is-hidden-buttons';

export function isDisabledButtonSaveOrCancelAction(
    data: isDisabledButtonsCommon.SaveOrCancelParameters<isDisabledButtons.SaveOrCancelParameters>,
) {
    if (data.recordId) {
        if (
            data.parameters.status === 'readyToProcess' ||
            data.parameters.status === 'readyToShip' ||
            data.parameters.status === 'shipped'
        ) {
            return !data.isDirty;
        }
        return true;
    }
    return !data.isDirty;
}

export function isHiddenButtonDeleteAction(
    data: isHiddenButtonsCommon.DeleteParameters<isHiddenButtons.DeleteParameters>,
) {
    if (data.recordId && data.parameters.status === 'readyToProcess' && data.parameters.displayStatus !== 'error') {
        return false;
    }
    return true;
}

export function isHiddenButtonConfirmAction(
    data: isHiddenButtonsCommon.BusinessButtonParameters<isHiddenButtons.ConfirmParameters>,
) {
    if (data.recordId && data.parameters.status === 'readyToProcess') {
        return data.isDirty;
    }
    return true;
}

export function isHiddenButtonPostOrRevertAction(
    data: isHiddenButtonsCommon.BusinessButtonParameters<isHiddenButtons.PostOrRevertParameters>,
) {
    if (
        data.recordId &&
        data.parameters.status === 'readyToShip' &&
        data.parameters.stockTransactionStatus !== 'inProgress'
    ) {
        return data.isDirty;
    }
    return true;
}

export function isHiddenButtonRepostAction(
    data: isDisabledButtonsCommon.BusinessButtonParameters<isHiddenButtons.RepostParameters>,
) {
    if (data.recordId && data.parameters.fromNotificationHistory) {
        return !data.isDirty;
    }
    return true;
}

export function isHiddenButtonDefaultDimensionAction(
    data: isHiddenButtonsCommon.HeaderDropDownActionButtonParameters<isHiddenButtons.DefaultDimensionParameters>,
) {
    if (
        data.recordId &&
        data.parameters &&
        (data.parameters.status === 'shipped' || data.parameters.displayStatus === 'postingInProgress')
    ) {
        return true;
    }
    return false;
}

export function isHiddenButtonPrintAction(
    data: isHiddenButtonsCommon.HeaderQuickActionButtonParameters<isHiddenButtons.PrintParameters>,
) {
    return !data.recordId || data.parameters.status === 'closed';
}

export function isDisabledButtonPrintAction(
    data: isDisabledButtonsCommon.HeaderQuickActionButtonParameters<isDisabledButtons.PrintParameters>,
) {
    return !data.recordId || data.isDirty || (data.parameters && data.parameters.status === 'closed');
}

export function isDisabledButtonDefaultDimensionAction(
    data: isDisabledButtonsCommon.HeaderDropDownActionButtonParameters<isDisabledButtons.DefaultDimensionParameters>,
) {
    if (
        data.parameters &&
        data.parameters.stockSite &&
        data.parameters.shipToCustomer &&
        data.parameters.shippingDate
    ) {
        return false;
    }
    return true;
}
