import * as commonDistribution from '@sage/xtrem-distribution/build/lib/client-functions/common';
import * as dimensionPanelHelpers from '@sage/xtrem-finance-data/build/lib/client-functions/dimension-panel-helpers';
import { setReferenceIfSingleValue } from '@sage/xtrem-master-data/build/lib/client-functions/get-default';
import * as ui from '@sage/xtrem-ui';
import type { StockTransferShipment as StockTransferShipmentPage } from '../pages/stock-transfer-shipment';

export function disableSomeHeaderPropertiesIfLines(pageInstance: StockTransferShipmentPage) {
    const isDisabled = pageInstance.lines.value.length > 0;
    pageInstance.site.isReadOnly = isDisabled;
}

export async function initPage(pageInstance: StockTransferShipmentPage) {
    if (pageInstance.$.recordId) {
        const isHeaderDisabled = pageInstance.status.value === 'shipped';
        pageInstance.date.isDisabled = isHeaderDisabled;
        pageInstance.shipToCustomerAddress.isDisabled = isHeaderDisabled;
        pageInstance.shipToAddress.isDisabled = isHeaderDisabled;
        pageInstance.incoterm.isDisabled = isHeaderDisabled;
        pageInstance.deliveryMode.isDisabled = isHeaderDisabled;
        pageInstance.deliveryLeadTime.isDisabled = isHeaderDisabled;
        pageInstance.deliveryDate.isDisabled = isHeaderDisabled;
    } else {
        await setReferenceIfSingleValue([pageInstance.stockSite]);
        pageInstance.date.value = new Date(Date.now()).toISOString().substring(0, 10);
        disableSomeHeaderPropertiesIfLines(pageInstance);
    }

    if (!pageInstance.status.value) {
        pageInstance.status.value = 'readyToProcess';
    }

    pageInstance._defaultDimensionsAttributes = dimensionPanelHelpers.initDefaultDimensions();
}

async function fetchDefaultsFromShipToAddress(pageInstance: StockTransferShipmentPage) {
    await pageInstance.$.fetchDefaults(['shipToAddress', 'incoterm', 'deliveryMode', 'deliveryLeadTime', 'workDays']);
    if (pageInstance.shipToCustomerAddress.value?.deliveryDetail?.shipmentSite && !pageInstance.stockSite.value) {
        await pageInstance.$.fetchDefaults(['stockSite']);
    }
}

export async function updateDeliveryDate(pageInstance: StockTransferShipmentPage) {
    pageInstance.deliveryDate.value = await pageInstance.$.graph
        .node('@sage/xtrem-sales/SalesShipment')
        .mutations.addWorkDays(true, {
            shippingDate: pageInstance.date.value ?? '',
            deliveryLeadTime: pageInstance.deliveryLeadTime.value ?? '',
            workDays: pageInstance.workDays.value ?? '',
        })
        .execute();
}

export async function cascadeAndFetchDefaultsFromShipToAddress(pageInstance: StockTransferShipmentPage) {
    if (pageInstance.incoterm.value || pageInstance.deliveryMode.value) {
        if (
            await commonDistribution.confirmDialogWithAcceptButtonText(
                pageInstance,
                ui.localize(
                    '@sage/xtrem-supply-chain/pages__stock_transfer_shipment__cascade_ship_to_address_update_dialog_title',
                    'Confirm update',
                ),
                ui.localize(
                    '@sage/xtrem-supply-chain/pages__stock_transfer_shipment__cascade_ship_to_address_update_dialog_content',
                    'You are about to update delivery information.',
                ),
                ui.localize('@sage/xtrem-supply-chain/pages-confirm-update', 'Update'),
            )
        ) {
            await fetchDefaultsFromShipToAddress(pageInstance);
            await updateDeliveryDate(pageInstance);
        } else {
            await pageInstance.$.fetchDefaults(['shipToAddress']);
        }
    } else {
        await fetchDefaultsFromShipToAddress(pageInstance);
        await updateDeliveryDate(pageInstance);
    }
}

export async function fillStockTransferOrderLines(pageInstance: StockTransferShipmentPage, rowId: string) {
    const oldIsDirty = pageInstance.$.isDirty;
    pageInstance.linkToStockTransferOrderLine.value.forEach(podLine => {
        pageInstance.linkToStockTransferOrderLine.removeRecord(podLine._id);
    });
    const lines = await pageInstance.$.graph
        .node('@sage/xtrem-supply-chain/StockTransferShipmentLineToStockTransferOrderLine')
        .query(
            ui.queryUtils.edgesSelector(
                {
                    to: {
                        _id: true,
                        item: {
                            _id: true,
                            name: true,
                        },
                        status: true,
                        quantity: true,
                        unit: {
                            _id: true,
                            name: true,
                            decimalDigits: true,
                            id: true,
                            symbol: true,
                        },
                        quantityInStockUnit: true,
                        stockUnit: {
                            _id: true,
                            name: true,
                            decimalDigits: true,
                            id: true,
                            symbol: true,
                        },
                        document: {
                            _id: true,
                            number: true,
                        },
                    },
                },
                {
                    filter: { from: { _id: rowId } },
                },
            ),
        )
        .execute();
    pageInstance.linkToStockTransferOrderLine.value = lines.edges.map(e => e.node.to);
    pageInstance.linkToStockTransferOrderLine.isHidden = pageInstance.linkToStockTransferOrderLine.value.length === 0;
    if (!oldIsDirty && pageInstance.$.isDirty) {
        pageInstance.$.setPageClean();
    }
}

export async function fillStockTransferReceiptLines(pageInstance: StockTransferShipmentPage, rowId: string) {
    const oldIsDirty = pageInstance.$.isDirty;
    pageInstance.linkToStockTransferReceiptLine.value.forEach(podLine => {
        pageInstance.linkToStockTransferReceiptLine.removeRecord(podLine._id);
    });
    const lines = await pageInstance.$.graph
        .node('@sage/xtrem-supply-chain/StockTransferReceiptLineToStockTransferShipmentLine')
        .query(
            ui.queryUtils.edgesSelector(
                {
                    from: {
                        _id: true,
                        item: {
                            _id: true,
                            name: true,
                        },
                        status: true,
                        quantity: true,
                        unit: {
                            _id: true,
                            name: true,
                            decimalDigits: true,
                            id: true,
                            symbol: true,
                        },
                        quantityInStockUnit: true,
                        stockUnit: {
                            _id: true,
                            name: true,
                            decimalDigits: true,
                            id: true,
                            symbol: true,
                        },
                        document: {
                            _id: true,
                            number: true,
                        },
                    },
                },
                {
                    filter: { to: { _id: rowId } },
                },
            ),
        )
        .execute();
    pageInstance.linkToStockTransferReceiptLine.value = lines.edges.map(e => e.node.from);
    pageInstance.linkToStockTransferReceiptLine.isHidden =
        pageInstance.linkToStockTransferReceiptLine.value.length === 0;
    if (!oldIsDirty && pageInstance.$.isDirty) {
        pageInstance.$.setPageClean();
    }
}
