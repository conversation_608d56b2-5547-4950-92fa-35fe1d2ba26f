import type * as isDisabledButtonsCommon from '@sage/xtrem-system/build/lib/client-functions/interfaces/is-disabled-buttons';
import type * as isHiddenButtonsCommon from '@sage/xtrem-system/build/lib/client-functions/interfaces/is-hidden-buttons';
import type * as isDisabledButtons from './interfaces/stock-transfer-receipt-is-disabled-buttons';
import type * as isHiddenButtons from './interfaces/stock-transfer-receipt-is-hidden-buttons';

export function isDisabledButtonSaveOrCancelAction(
    data: isDisabledButtonsCommon.SaveOrCancelParameters<isDisabledButtons.SaveOrCancelParameters>,
) {
    if (data.recordId) {
        if (data.parameters.status === 'readyToProcess' || data.parameters.status === 'received') {
            return !data.isDirty;
        }
        return true;
    }
    return !data.isDirty;
}

export function isHiddenButtonPostOrRevertAction(
    data: isHiddenButtonsCommon.BusinessButtonParameters<isHiddenButtons.PostOrRevertParameters>,
) {
    if (
        data.recordId &&
        data.parameters.status === 'readyToProcess' &&
        data.parameters.stockTransactionStatus !== 'inProgress'
    ) {
        return data.isDirty;
    }
    return true;
}

export function isHiddenButtonRepostAction(
    data: isDisabledButtonsCommon.BusinessButtonParameters<isHiddenButtons.RepostParameters>,
) {
    if (data.recordId && data.parameters.fromNotificationHistory) {
        return !data.isDirty;
    }
    return true;
}

export function isHiddenButtonDefaultDimensionAction(
    data: isHiddenButtonsCommon.HeaderDropDownActionButtonParameters<isHiddenButtons.DefaultDimensionParameters>,
) {
    if (
        data.recordId &&
        data.parameters &&
        (data.parameters.status === 'received' || data.parameters.displayStatus === 'postingInProgress')
    ) {
        return true;
    }
    return false;
}

export function isDisabledButtonDefaultDimensionAction(
    data: isDisabledButtonsCommon.HeaderDropDownActionButtonParameters<isDisabledButtons.DefaultDimensionParameters>,
) {
    if (data.parameters && data.parameters.stockSite) {
        return false;
    }
    return true;
}
