import type {
    DocumentLineNestedGrid,
    DocumentNestedGrid,
    Unit,
} from '@sage/xtrem-landed-cost/build/lib/client-functions/interfaces';

export interface StockTransferReceiptLineNestedGrid extends DocumentLineNestedGrid {
    quantity: string;
    unit: Unit;
    receivedQuantity?: string;
    stockTransactionStatus: string | null;
}

export interface StockTransferReceiptNestedGrid extends DocumentNestedGrid {
    displayStatus: string | null;
    supplierDocumentNumber: string;
    supplier: {
        businessEntity: {
            name: string;
        };
    };
    lines: DocumentLineNestedGrid;
}
