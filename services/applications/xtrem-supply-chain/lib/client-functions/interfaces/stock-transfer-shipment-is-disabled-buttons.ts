import type { ExtractEdgesPartial } from '@sage/xtrem-client';
import type { Customer } from '@sage/xtrem-master-data-api';
import type { Site } from '@sage/xtrem-system-api';

export interface SaveOrCancelParameters {
    status?: string | null;
}

export interface PrintParameters {
    status?: string | null;
}

export interface DefaultDimensionParameters {
    stockSite?: ExtractEdgesPartial<Site> | null;
    shipToCustomer?: ExtractEdgesPartial<Customer> | null;
    shippingDate?: string | null;
}
