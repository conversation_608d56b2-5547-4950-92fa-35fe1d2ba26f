import type { ExtractEdgesPartial } from '@sage/xtrem-client';
import type { Currency, Customer } from '@sage/xtrem-master-data-api';
import type { Site } from '@sage/xtrem-system-api';

export interface SaveOrCancelParameters {
    status?: string | null;
}

export interface DeleteParameters {
    status?: string | null;
    shippingStatus?: string | null;
}

export interface DefaultDimensionParameters {
    site?: ExtractEdgesPartial<Site> | null;
    shipToCustomer?: ExtractEdgesPartial<Customer> | null;
    orderDate?: string | null;
    currency?: ExtractEdgesPartial<Currency> | null;
}

export interface LinePhantomRowParameters {
    status?: string | null;
    site?: ExtractEdgesPartial<Site> | null;
    receivingSite?: ExtractEdgesPartial<Site> | null;
    shipToCustomer?: ExtractEdgesPartial<Customer> | null;
    orderDate?: string | null;
    currency?: ExtractEdgesPartial<Currency> | null;
    approvalStatus?: string | null;
}
