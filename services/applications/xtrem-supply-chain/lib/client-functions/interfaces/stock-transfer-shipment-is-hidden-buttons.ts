export interface DeleteParameters {
    status?: string | null;
    displayStatus: string | null;
}

export interface ConfirmParameters {
    status?: string | null;
}

export interface PostOrRevertParameters {
    status?: string | null;
    stockTransactionStatus?: string | null;
}

export interface RepostParameters {
    fromNotificationHistory?: boolean;
}

export interface PrintParameters {
    status?: string | null;
}

export interface DefaultDimensionParameters {
    status?: string | null;
    displayStatus?: string | null;
}
