import type { StockTransferShipment as StockTransferShipmentPage } from '../../pages/stock-transfer-shipment';

export interface PostActionParameters {
    recordId: string;
}

export interface StockTransferShipmentParameter {
    stockTransferShipmentPage: StockTransferShipmentPage;
    recordId: string;
}

export interface PrintShipmentParameters {
    status: string;
    recordId: string;
    recordNumber: string;
}
