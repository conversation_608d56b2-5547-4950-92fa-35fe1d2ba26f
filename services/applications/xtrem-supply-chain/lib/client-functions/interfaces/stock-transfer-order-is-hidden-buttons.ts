import type { ExtractEdgesPartial } from '@sage/xtrem-client';
import type { Currency, Customer } from '@sage/xtrem-master-data-api';
import type { Site } from '@sage/xtrem-system-api';

export interface DeleteParameters {
    status?: string | null;
    approvalStatus?: string | null;
    isApprovalManaged?: boolean | null;
    displayStatus?: string | null;
}

export interface ConfirmParameters {
    status?: string | null;
    approvalStatus?: string | null;
    isApprovalManaged?: boolean | null;
}

export interface ShipParameters {
    status?: string | null;
    allocationRequestStatus?: string | null;
}
export interface OpenParameters {
    status?: string | null;
    shippingStatus?: string | null;
    approvalStatus?: string | null;
}

export interface CloseParameters {
    status?: string | null;
    allocationRequestStatus?: string | null;
}

export interface ApproveOrRejectParameters {
    status?: string | null;
    approvalStatus?: string | null;
}
export interface RequestAllocationParameters {
    status?: string | null;
    approvalStatus?: string | null;
    isApprovalManaged?: boolean | null;
    allocationRequestStatus?: string | null;
    allocationStatus?: string | null;
}

export interface RequestApprovalParameters {
    status?: string | null;
    approvalStatus?: string | null;
    isApprovalManaged?: boolean | null;
}

export interface DefaultDimensionParameters {
    status?: string | null;
    shippingStatus?: string | null;
}

export interface SelectFromItemSitesParameters {
    status?: string | null;
    site?: ExtractEdgesPartial<Site> | null;
    receivingSite?: ExtractEdgesPartial<Site> | null;
    shipToCustomer?: ExtractEdgesPartial<Customer> | null;
    orderDate?: string | null;
    currency?: ExtractEdgesPartial<Currency> | null;
    approvalStatus?: string | null;
}
