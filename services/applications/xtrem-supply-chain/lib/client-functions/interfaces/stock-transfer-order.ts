import type * as ui from '@sage/xtrem-ui';

export interface StockTransferOrderStepSequenceStatus {
    create: ui.StepSequenceStatus;
    approve: ui.StepSequenceStatus;
    confirm: ui.StepSequenceStatus;
    ship: ui.StepSequenceStatus;
    receive: ui.StepSequenceStatus;
}

export interface ItemData {
    item: {
        minimumSalesQuantity?: number;
        maximumSalesQuantity?: number;
        conversionFactor?: number;
    };
    itemCustomer: {
        minimumSalesQuantity?: number;
        maximumSalesQuantity?: number;
        conversionFactor?: number;
    };
}
