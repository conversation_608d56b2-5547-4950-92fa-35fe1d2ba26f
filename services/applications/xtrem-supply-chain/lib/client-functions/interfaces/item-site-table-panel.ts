import type { ExtractEdgesPartial } from '@sage/xtrem-client';
import type { ItemSite } from '@sage/xtrem-master-data-api';

export type ItemSiteLines = ItemSite & {
    availableQuantityInStockUnit: number;
    quantity: number;
    isAvailableQuantityInStockUnitSelected: boolean;
};

export interface ItemSiteLinesReturned {
    lines: ExtractEdgesPartial<ItemSiteLines & { itemId: string }>[];
    isAutoAllocationRequested: boolean | null;
}
