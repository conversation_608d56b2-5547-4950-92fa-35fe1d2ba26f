import { setDisplayOfCommonPageActions } from '@sage/xtrem-system/build/lib/client-functions/applicative-crud-actions';
import type { StockTransferOrder as StockTransferOrderPage } from '../pages/stock-transfer-order';
import * as displayButtons from './stock-transfer-order-display-buttons';

export function manageDisplayButtonConfirmAction(pageInstance: StockTransferOrderPage, isDirty: boolean) {
    pageInstance.confirm.isHidden = displayButtons.isHiddenButtonConfirmAction({
        parameters: {
            status: pageInstance.status.value,
            approvalStatus: pageInstance.approvalStatus.value,
            isApprovalManaged: pageInstance.site.value?.isStockTransferOrderApprovalManaged,
        },
        recordId: pageInstance.$.recordId,
        isDirty,
    });
}

export function manageDisplayButtonRejectAction(pageInstance: StockTransferOrderPage, isDirty: boolean) {
    pageInstance.reject.isHidden = displayButtons.isHiddenButtonApproveOrRejectAction({
        parameters: { status: pageInstance.status.value, approvalStatus: pageInstance.approvalStatus.value },
        recordId: pageInstance.$.recordId,
        isDirty,
    });
}

export function manageDisplayButtonRequestApprovalAction(pageInstance: StockTransferOrderPage, isDirty: boolean) {
    pageInstance.requestApproval.isHidden = displayButtons.isHiddenButtonRequestApprovalAction({
        parameters: {
            status: pageInstance.status.value,
            approvalStatus: pageInstance.approvalStatus.value,
            isApprovalManaged: pageInstance.site.value?.isStockTransferOrderApprovalManaged,
        },
        recordId: pageInstance.$.recordId,
        isDirty,
    });
}

export function manageDisplayButtonApproveAction(pageInstance: StockTransferOrderPage, isDirty: boolean) {
    pageInstance.approve.isHidden = displayButtons.isHiddenButtonApproveOrRejectAction({
        parameters: { status: pageInstance.status.value, approvalStatus: pageInstance.approvalStatus.value },
        recordId: pageInstance.$.recordId,
        isDirty,
    });
}

export function manageDisplayButtonShipAction(pageInstance: StockTransferOrderPage, isDirty: boolean) {
    pageInstance.ship.isHidden = displayButtons.isHiddenButtonShipAction({
        parameters: {
            status: pageInstance.status.value,
            allocationRequestStatus: pageInstance.allocationRequestStatus.value,
        },
        recordId: pageInstance.$.recordId,
        isDirty,
    });
}

export function manageDisplayButtonCloseAction(pageInstance: StockTransferOrderPage, isDirty: boolean) {
    if (isDirty || !pageInstance.$.recordId) {
        pageInstance.close.isHidden = true;
    } else {
        pageInstance.close.isHidden =
            pageInstance.isCloseHidden.value === true ? pageInstance.isCloseHidden.value : false;
    }
}

export function manageDisplayButtonOpenAction(pageInstance: StockTransferOrderPage, isDirty: boolean) {
    pageInstance.open.isHidden = displayButtons.isHiddenButtonOpenAction({
        parameters: { status: pageInstance.status.value, shippingStatus: pageInstance.shippingStatus.value },
        recordId: pageInstance.$.recordId,
        isDirty,
    });
}

export function manageDisplayLinePhantomRow(pageInstance: StockTransferOrderPage) {
    pageInstance.lines.isPhantomRowDisabled = displayButtons.isDisabledLinePhantomRow({
        parameters: {
            status: pageInstance.status.value,
            site: pageInstance.site.value,
            receivingSite: pageInstance.receivingSite.value,
            shipToCustomer: pageInstance.shipToCustomer.value,
            orderDate: pageInstance.date.value,
            currency: pageInstance.currency.value,
            approvalStatus: pageInstance.approvalStatus.value,
        },
    });
}

export function manageDisplayButtonSelectFromItemSitesAction(pageInstance: StockTransferOrderPage) {
    pageInstance.selectFromItemSites.isHidden = true;
    // TODO: enable once validated with Ux
    // pageInstance.selectFromItemSites.isHidden = displayButtons.isHiddenButtonSelectFromItemSitesAction({
    //     parameters: {
    //         status: pageInstance.status.value,
    //         site: pageInstance.site.value,
    //         receivingSite: pageInstance.receivingSite.value,
    //         shipToCustomer: pageInstance.shipToCustomer.value,
    //         orderDate: pageInstance.date.value,
    //         currency: pageInstance.currency.value,
    //         approvalStatus: pageInstance.approvalStatus.value,
    //     },
    // });
}

export function manageDisplayButtonDefaultDimensionAction(pageInstance: StockTransferOrderPage) {
    pageInstance.defaultDimension.isHidden = displayButtons.isHiddenButtonDefaultDimensionAction({
        parameters: { status: pageInstance.status.value, shippingStatus: pageInstance.shippingStatus.value },
        recordId: pageInstance.$.recordId,
    });
    pageInstance.defaultDimension.isDisabled = displayButtons.isDisabledButtonDefaultDimensionAction({
        parameters: {
            site: pageInstance.site.value,
            shipToCustomer: pageInstance.shipToCustomer.value,
            orderDate: pageInstance.date.value,
            currency: pageInstance.currency.value,
        },
    });
}

export function manageDisplayButtonRequestAllocationAction(pageInstance: StockTransferOrderPage) {
    pageInstance.requestAllocation.isHidden = displayButtons.isHiddenButtonRequestAllocationAction({
        parameters: {
            allocationRequestStatus: pageInstance.allocationRequestStatus.value,
            allocationStatus: pageInstance.allocationStatus.value,
            status: pageInstance.status.value,
        },
        recordId: pageInstance.$.recordId,
    });
}

export function manageDisplayButtonRequestDeallocationAction(pageInstance: StockTransferOrderPage) {
    pageInstance.requestDeallocation.isHidden = displayButtons.isHiddenButtonRequestDeallocationAction({
        parameters: {
            allocationRequestStatus: pageInstance.allocationRequestStatus.value,
            allocationStatus: pageInstance.allocationStatus.value,
            status: pageInstance.status.value,
        },
        recordId: pageInstance.$.recordId,
    });
}

export function manageDisplayButtonAllOtherActions(pageInstance: StockTransferOrderPage, isDirty = false) {
    // footer business actions
    manageDisplayButtonConfirmAction(pageInstance, isDirty);
    manageDisplayButtonApproveAction(pageInstance, isDirty);
    manageDisplayButtonRejectAction(pageInstance, isDirty);
    manageDisplayButtonRequestApprovalAction(pageInstance, isDirty);

    manageDisplayButtonOpenAction(pageInstance, isDirty);
    manageDisplayButtonCloseAction(pageInstance, isDirty);
    manageDisplayButtonShipAction(pageInstance, isDirty);
    // other header actions
    manageDisplayButtonRequestAllocationAction(pageInstance);
    manageDisplayButtonRequestDeallocationAction(pageInstance);
    manageDisplayButtonDefaultDimensionAction(pageInstance);

    manageDisplayButtonSelectFromItemSitesAction(pageInstance);
    // not for now
    // pageInstance.manageDisplayButtonSendMailAction(pageInstance: StockTransferOrder,isDirty);
    manageDisplayLinePhantomRow(pageInstance);
}

function manageDisplayButtonCRUDActions(pageInstance: StockTransferOrderPage, isDirty = false) {
    pageInstance.save.isDisabled = displayButtons.isDisabledButtonSaveOrCancelAction({
        parameters: { status: pageInstance.status.value },
        recordId: pageInstance.$.recordId,
        isDirty,
    });
    pageInstance.$standardCancelAction.isDisabled = displayButtons.isDisabledButtonSaveOrCancelAction({
        parameters: { status: pageInstance.status.value },
        recordId: pageInstance.$.recordId,
        isDirty,
    });
    pageInstance.deleteStockTransferOrder.isDisabled = displayButtons.isDisabledButtonDeleteAction({
        parameters: { status: pageInstance.status.value },
        recordId: pageInstance.$.recordId,
    });
    pageInstance.deleteStockTransferOrder.isHidden = displayButtons.isHiddenButtonDeleteAction({
        parameters: {
            status: pageInstance.status.value,
            displayStatus: pageInstance.displayStatus.value,
        },
        recordId: pageInstance.$.recordId,
    });
}

function _manageDisplayAdditionalPageActions(pageInstance: StockTransferOrderPage, isDirty = false) {
    manageDisplayButtonCRUDActions(pageInstance, isDirty);
    manageDisplayButtonAllOtherActions(pageInstance, isDirty);
}

export function _manageDisplayApplicativePageActions(pageInstance: StockTransferOrderPage, isDirty = false) {
    setDisplayOfCommonPageActions({
        page: pageInstance,
        isDirty,
        save: pageInstance.save,
        cancel: pageInstance.$standardCancelAction,
        // not for now
        // duplicate: pageInstance.$standardDuplicateAction,
        remove: pageInstance.deleteStockTransferOrder,
        businessActions: [
            pageInstance.$standardOpenCustomizationPageWizardAction,
            pageInstance.confirm,
            pageInstance.ship,
            pageInstance.close,
            pageInstance.open,
        ],
    });
    _manageDisplayAdditionalPageActions(pageInstance, isDirty);
}
