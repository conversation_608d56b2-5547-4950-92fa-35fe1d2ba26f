import type * as isDisabledButtonsCommon from '@sage/xtrem-system/build/lib/client-functions/interfaces/is-disabled-buttons';
import type * as isHiddenButtonsCommon from '@sage/xtrem-system/build/lib/client-functions/interfaces/is-hidden-buttons';
import type * as isDisabledButtons from './interfaces/is-disabled-buttons-landed-cost-allocation-panel';
import type * as isHiddenButtons from './interfaces/is-hidden-buttons-landed-cost-allocation-panel';

export function isHiddenButtonSelectFromReceiptAction(
    data: isHiddenButtonsCommon.HeaderDropDownActionButtonParameters<isHiddenButtons.SelectFromReceiptParameters>,
) {
    return !data.parameters?.isEditable;
}

export function isDisabledButtonSelectFromReceiptAction(
    data: isDisabledButtonsCommon.HeaderDropDownActionButtonParameters<isDisabledButtons.SelectFromReceiptParameters>,
) {
    return isHiddenButtonSelectFromReceiptAction(data);
}

export function isHiddenButtonSelectFromOrderAction(
    data: isHiddenButtonsCommon.HeaderDropDownActionButtonParameters<isHiddenButtons.SelectFromOrderParameters>,
) {
    return !data.parameters?.isEditable;
}

export function isDisabledButtonSelectFromOrderAction(
    data: isDisabledButtonsCommon.HeaderDropDownActionButtonParameters<isDisabledButtons.SelectFromOrderParameters>,
) {
    return isHiddenButtonSelectFromReceiptAction(data);
}
