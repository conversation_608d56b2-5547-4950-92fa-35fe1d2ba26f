import type { ExtensionMembers } from '@sage/xtrem-core';
import type { Site } from '@sage/xtrem-master-data/build/lib/pages/site';
import * as ui from '@sage/xtrem-ui';
import type { SiteExtension } from '../page-extensions/site-extension';
import { stockTransferOrdersPendingApproval } from './stock-transfer-order';

export async function onChangeIsStockTransferOrderApprovalManaged(
    sitePage: ExtensionMembers<Site & SiteExtension>,
    wasDirtyBeforeChanging: boolean,
) {
    if (sitePage.isStockTransferOrderApprovalManaged.value) {
        sitePage.stockTransferOrderDefaultApprover.isReadOnly = false;
        sitePage.stockTransferOrderSubstituteApprover.isReadOnly = false;
        return;
    }
    const _anyStockTransferOrderPendingApproval =
        sitePage.$.recordId && (await stockTransferOrdersPendingApproval(sitePage.$.graph, sitePage.$.recordId));

    if (_anyStockTransferOrderPendingApproval) {
        sitePage.$.showToast(
            ui.localize(
                '@sage/xtrem-supply-chain/pages__site_extension__control_stock_transfer_order_approval',
                'You need to approve or reject pending documents before you deactivate the approval process.',
            ),
            { type: 'warning' },
        );
        sitePage.isStockTransferOrderApprovalManaged.value = true;
        if (!wasDirtyBeforeChanging) {
            sitePage.$.setPageClean();
        }
        return;
    }

    sitePage.stockTransferOrderDefaultApprover.isReadOnly = true;
    sitePage.stockTransferOrderSubstituteApprover.isReadOnly = true;
}
