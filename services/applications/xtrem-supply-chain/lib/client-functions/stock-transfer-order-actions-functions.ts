import * as authorizationFilters from '@sage/xtrem-authorization/build/lib/client-functions/filters';
import { extractEdges, withoutEdges } from '@sage/xtrem-client';
import { confirmDialogWithAcceptButtonText } from '@sage/xtrem-distribution/build/lib/client-functions/common';
import type {
    ApproveRejectMutationParameters,
    CloseLineMutationParameters,
    CloseMutationParameters,
    ConfirmOrderMutationParameters,
    OpenLineMutationParameters,
    OpenMutationParameters,
    RequestAllocationMutationParameters,
} from '@sage/xtrem-distribution/build/lib/client-functions/interfaces/document-actions-functions';
import type { BaseStatus } from '@sage/xtrem-master-data-api';
import * as MasterDataUtils from '@sage/xtrem-master-data/build/lib/client-functions/utils';
import * as StockDetailHelper from '@sage/xtrem-stock-data/build/lib/client-functions/stock-details-helper';
import type { GraphApi, StockTransferOrderLineBinding } from '@sage/xtrem-supply-chain-api';
import * as ui from '@sage/xtrem-ui';
import type { StockTransferOrder as StockTransferOrderPage } from '../pages/stock-transfer-order';
import { getDimensionsForStockTransferLines, getValuesForSetDimensionsFromMainList } from './finance-integration';
import type {
    CreateStockTransferShipmentActionParameters,
    CreateStockTransferShipmentMutationParameters,
    FilteredUsers,
    GetStockTransferOrderLinesParameter,
    PrintStockTransferOrderParameters,
    SetDimensionActionParameter,
    SiteApprovalData,
    SiteApprover,
    UpdateStockTransferOrderPrintStatusParameters,
} from './interfaces/stock-transfer-order-actions-functions';
import * as displayButtonsManagement from './stock-transfer-order-display-buttons-management';

export async function updateStockTransferOrderPrintStatusAction(
    parameters: UpdateStockTransferOrderPrintStatusParameters,
) {
    parameters.stockTransferOrderPage.$.loader.isHidden = false;
    const isSetIsPrintedTrue = await parameters.stockTransferOrderPage.$.graph
        .node('@sage/xtrem-supply-chain/StockTransferOrder')
        .mutations.setIsPrintedTrue(true, { order: parameters._id, isPrinted: parameters.isPrinted })
        .execute();

    if (parameters.isPrinted && isSetIsPrintedTrue) {
        const displaySuccess = ui.localize(
            '@sage/xtrem-supply-chain/pages__transfer_order__printed',
            'The stock transfer order was printed',
        );

        parameters.stockTransferOrderPage.$.showToast(displaySuccess, { type: 'success' });
    }

    if (!isSetIsPrintedTrue) {
        throw new Error(
            ui.localize(
                '@sage/xtrem-supply-chain/pages__transfer_order__printed_status_not_updated',
                'Unable to update stock transfer order {{number}} to printed status.',
                { number: parameters.number },
            ),
        );
    }
    parameters.stockTransferOrderPage.$.loader.isHidden = true;
}

export async function printStockTransferOrderAction(parameters: PrintStockTransferOrderParameters) {
    if (parameters.number) {
        const reportName = 'stockTransferOrder';

        await parameters.stockTransferOrderPage.$.dialog.page(
            '@sage/xtrem-reporting/PrintDocument',
            {
                reportName,
                order: parameters._id,
            },
            {
                size: 'extra-large',
                resolveOnCancel: true,
            },
        );
    }

    if (parameters._id) {
        await updateStockTransferOrderPrintStatusAction({
            stockTransferOrderPage: parameters.stockTransferOrderPage,
            _id: parameters._id,
            status: parameters.status,
            isPrinted: true,
            number: parameters.number,
        });
    }
    if (parameters.isCalledFromRecordPage) {
        await parameters.stockTransferOrderPage.$.refreshNavigationPanel();
        await parameters.stockTransferOrderPage.$.router.refresh();
    }
    parameters.stockTransferOrderPage.$.loader.isHidden = true;
}

export function loadApprovers(page: ui.Page<GraphApi>): Promise<FilteredUsers[]> {
    return page.$.graph
        .node('@sage/xtrem-supply-chain/StockTransferOrder')
        .queries.getFilteredUsers(
            {
                _id: true,
                email: true,
                firstName: true,
                lastName: true,
            },
            {
                criteria: authorizationFilters.user.activeApplicationUsers,
            },
        )
        .execute();
}

// TODO: these functions below should be more generic and placed in the distribution package
function runCreateStockTransferShipmentMutation(parameters: CreateStockTransferShipmentMutationParameters) {
    if (parameters._id !== '') {
        return parameters.stockTransferOrderPage.$.graph
            .node('@sage/xtrem-supply-chain/StockTransferOrder')
            .mutations.createStockTransferShipment(
                { _id: true, number: true },
                {
                    stockTransferOrder: parameters._id,
                },
            )
            .execute();
    }
    return null;
}

export async function createStockTransferShipmentAction(parameters: CreateStockTransferShipmentActionParameters) {
    if (
        await confirmDialogWithAcceptButtonText(
            parameters.stockTransferOrderPage,
            ui.localize(
                '@sage/xtrem-supply-chain/pages__transfer_order__create_shipment_dialog_title',
                'Confirm shipment creation',
            ),
            ui.localize(
                '@sage/xtrem-supply-chain/pages__transfer_order__create_shipment_dialog_content',
                'You are about to create a shipment from this transfer order.',
            ),
            ui.localize('@sage/xtrem-supply-chain/pages-confirm-create', 'Create'),
        )
    ) {
        const shipmentNumberCreated = await runCreateStockTransferShipmentMutation({
            stockTransferOrderPage: parameters.stockTransferOrderPage,
            _id: parameters._id,
        });

        if (shipmentNumberCreated) {
            parameters.stockTransferOrderPage.$.showToast(
                ui.localize(
                    '@sage/xtrem-supply-chain/pages_functions__stock_transfer_order_create_shipment_action_is_shipped',
                    'Transfer shipment created ({{shipmentNumberCreated}})',
                    { shipmentNumberCreated: shipmentNumberCreated.number.toString() },
                ),
                { type: 'success' },
            );
        } else {
            parameters.stockTransferOrderPage.$.showToast(
                ui.localize(
                    '@sage/xtrem-supply-chain/pages_functions__stock_transfer_order_create_shipment_action_not_allowed_exception',
                    'Could not create shipment.',
                ),
                { type: 'error' },
            );
        }
        if (parameters.isCalledFromRecordPage && shipmentNumberCreated) {
            parameters.stockTransferOrderPage.$.setPageClean();
            parameters.stockTransferOrderPage.$.loader.isHidden = true;
            parameters.stockTransferOrderPage.$.router.goTo(`@sage/xtrem-supply-chain/StockTransferShipment`, {
                _id: shipmentNumberCreated._id,
            });
        } else {
            await parameters.stockTransferOrderPage.$.refreshNavigationPanel();
            await parameters.stockTransferOrderPage.$.router.refresh();
        }
    }
}

// //////////////////////

export function runRequestAllocationMutation(parameters: RequestAllocationMutationParameters) {
    return () =>
        parameters.pageInstance.$.graph
            .node('@sage/xtrem-supply-chain/StockTransferOrder')
            .mutations.requestAutoAllocation(true, {
                data: {
                    document: parameters._id,
                    requestType: parameters.allocationType,
                },
            })
            .execute();
}

export function runConfirmOrderMutation(parameters: ConfirmOrderMutationParameters) {
    return () =>
        parameters.pageInstance.$.graph
            .node('@sage/xtrem-supply-chain/StockTransferOrder')
            .mutations.confirm(true, { stockTransferOrder: parameters.recordId })
            .execute();
}

export function runRejectMutation(parameters: ApproveRejectMutationParameters) {
    return () =>
        parameters.pageInstance.$.graph
            .node('@sage/xtrem-supply-chain/StockTransferOrder')
            .mutations.reject(true, { stockTransferOrder: parameters.recordId })
            .execute();
}

export function runApproveMutation(parameters: ApproveRejectMutationParameters) {
    return () =>
        parameters.pageInstance.$.graph
            .node('@sage/xtrem-supply-chain/StockTransferOrder')
            .mutations.approve(true, { stockTransferOrder: parameters.recordId })
            .execute();
}

export function runCloseMutation(parameters: CloseMutationParameters) {
    return () =>
        parameters.pageInstance.$.graph
            .node('@sage/xtrem-supply-chain/StockTransferOrder')
            .mutations.close(true, { stockTransferOrder: parameters.recordId })
            .execute();
}

export function runOpenMutation(parameters: OpenMutationParameters) {
    return () =>
        parameters.pageInstance.$.graph
            .node('@sage/xtrem-supply-chain/StockTransferOrder')
            .mutations.open(true, { stockTransferOrder: parameters.recordId })
            .execute();
}

export function runCloseLineMutation(parameters: CloseLineMutationParameters) {
    return () =>
        parameters.pageInstance.$.graph
            .node('@sage/xtrem-supply-chain/StockTransferOrder')
            .mutations.closeLine(true, {
                stockTransferOrderLine: `_id:${parameters.lineId}`,
            })
            .execute();
}

export function runOpenLineMutation(parameters: OpenLineMutationParameters) {
    return () =>
        parameters.pageInstance.$.graph
            .node('@sage/xtrem-supply-chain/StockTransferOrder')
            .mutations.openLine(true, {
                stockTransferOrderLine: `_id:${parameters.lineId}`,
            })
            .execute();
}

export async function addFromStock(pageInstance: StockTransferOrderPage) {
    const changedLine = await pageInstance.$.dialog.page(
        '@sage/xtrem-supply-chain/ItemSiteTablePanel',
        {
            site: JSON.stringify(pageInstance.site),
            stockTransferOrderLines: JSON.stringify(pageInstance.lines),
        },
        {
            size: 'large',
        },
    );
    if (
        changedLine &&
        changedLine.lines.length &&
        pageInstance.shipToAddress.value &&
        pageInstance.deliveryMode.value &&
        pageInstance.requestedDeliveryDate.value &&
        pageInstance.deliveryLeadTime.value &&
        pageInstance.doNotShipBeforeDate.value &&
        pageInstance.doNotShipAfterDate.value &&
        pageInstance.shippingDate.value &&
        pageInstance.expectedDeliveryDate.value
    ) {
        changedLine.lines.forEach((line: any) => {
            pageInstance.lines.addOrUpdateRecordValue({
                item: line.item,
                itemDescription: line.item.description,
                quantity: line.quantity,
                unit: line.stockUnit,
                status: pageInstance.status.value as BaseStatus,
                shipToAddress: pageInstance.shipToAddress.value ?? undefined,
                deliveryMode: pageInstance.deliveryMode.value ?? undefined,
                requestedDeliveryDate: pageInstance.requestedDeliveryDate.value ?? undefined,
                deliveryLeadTime: pageInstance.deliveryLeadTime.value ?? undefined,
                doNotShipBeforeDate: pageInstance.doNotShipBeforeDate.value ?? undefined,
                doNotShipAfterDate: pageInstance.doNotShipAfterDate.value ?? undefined,
                shippingDate: pageInstance.shippingDate.value ?? undefined,
                expectedDeliveryDate: pageInstance.expectedDeliveryDate.value ?? undefined,
            });
        });
        return { isLineAdded: true, changedLine };
    }
    return { isLineAdded: false, changedLine };
}

async function companyAttributeDimensionControl(pageInstance: StockTransferOrderPage) {
    if (pageInstance._id.value) {
        const financeIntegrationCheckResult = (await pageInstance.$.graph
            .node('@sage/xtrem-supply-chain/StockTransferOrder')
            .mutations.financeIntegrationCheck(true, { stockTransferOrder: pageInstance._id.value })
            .execute()) as string[];

        if (financeIntegrationCheckResult.length > 0) {
            pageInstance.$.showToast(
                `**${ui.localize(
                    '@sage/xtrem-supply-chain/pages__stock_transfer_order__save_warnings',
                    'Warnings while saving:',
                )}**\n\n${financeIntegrationCheckResult.map(result => result).join('\n\n')}`,
                { type: 'warning', timeout: 20000 },
            );
        }
    }
}

export async function saveAction(pageInstance: StockTransferOrderPage) {
    await pageInstance.$standardSaveAction.execute(true);
    await companyAttributeDimensionControl(pageInstance);
}

export async function allocateStockDialog(
    pageInstance: StockTransferOrderPage,
    rowId: string,
    rowItem: ui.PartialCollectionValue<StockTransferOrderLineBinding>,
): Promise<void> {
    if (pageInstance.shippingDate.value && pageInstance.stockSite.value?._id) {
        const line = MasterDataUtils.removeExtractEdgesPartial(rowItem);
        const allocationChanged = await MasterDataUtils.confirmDialogToBoolean(
            StockDetailHelper.editStockDetails(pageInstance, line, {
                movementType: 'allocation',
                data: {
                    isEditable: ['pending', 'inProgress'].includes(line.status),
                    needFullAllocation: false,
                    cannotOverAllocate: true,
                    documentLineHasAlreadySomeAllocations: Number(line.quantityAllocatedInStockUnit) > 0,
                    effectiveDate: pageInstance.shippingDate.value,
                    documentLineSortValue: line._sortValue,
                    documentLine: rowId,
                    item: line.item?._id,
                    stockSite: pageInstance.stockSite.value?._id,
                    quantity: +line.remainingQuantityToShipInStockUnit,
                    unit: line.item.stockUnit?._id,
                    searchCriteria: {
                        activeQuantityInStockUnit: +line.remainingQuantityToShipInStockUnit,
                        item: line.item?._id,
                        site: pageInstance.stockSite.value?._id,
                        stockUnit: line.item.stockUnit?._id,
                        statusList: [{ statusType: 'accepted' }],
                    },
                },
            }),
        );
        if (allocationChanged) {
            await pageInstance.lines.refresh();
            await pageInstance.allocationStatus.refresh();
            displayButtonsManagement.manageDisplayButtonAllOtherActions(pageInstance, false);
        }
    }
}

export async function transferAllocationDialog(
    pageInstance: StockTransferOrderPage,
    rowId: string,
    rowItem: ui.PartialCollectionValue<StockTransferOrderLineBinding>,
): Promise<void> {
    if (pageInstance.number.value) {
        await MasterDataUtils.catchPanelCrossQuitButtonAsNoop(
            pageInstance.$.dialog.page(
                '@sage/xtrem-stock-data/TransferAllocationsDialog',
                {
                    item: JSON.stringify(rowItem.item),
                    site: JSON.stringify(pageInstance.stockSite.value),
                    shippingDate: JSON.stringify(pageInstance.shippingDate.value),
                    lineId: rowId,
                    requiredQuantity: rowItem.remainingQuantityToShipInStockUnit || 0,
                    allocatedQuantity: rowItem.quantityAllocatedInStockUnit || 0,
                    documentTypes: JSON.stringify(['StockTransferOrderLine', 'StockTransferShipmentLine']),
                    documentNumber: pageInstance.number.value,
                },
                {
                    rightAligned: false,
                    size: 'extra-large',
                },
            ),
        );
        await pageInstance.lines.refreshRecord(rowId);
        await pageInstance.allocationStatus.refresh();
    }
}

export async function projectedStockDialog(
    pageInstance: StockTransferOrderPage,
    rowItem: ui.PartialCollectionValue<StockTransferOrderLineBinding>,
) {
    await MasterDataUtils.catchPanelCrossQuitButtonAsNoop(
        pageInstance.$.dialog.page(
            '@sage/xtrem-stock-data/ProjectedStockDialog',
            {
                item: JSON.stringify(rowItem.item),
                site: JSON.stringify(pageInstance.stockSite.value),
            },
            {
                rightAligned: false,
                size: 'extra-large',
            },
        ),
    );
}

export async function getSiteApprovers(page: StockTransferOrderPage, siteId: string): Promise<SiteApprover | null> {
    const siteApprovers: SiteApprover[] = withoutEdges(
        await page.$.graph
            .node('@sage/xtrem-system/Site')
            .query(
                ui.queryUtils.edgesSelector(
                    {
                        stockTransferOrderDefaultApprover: {
                            _id: true,
                            email: true,
                            firstName: true,
                            lastName: true,
                        },
                        stockTransferOrderSubstituteApprover: {
                            _id: true,
                            email: true,
                            firstName: true,
                            lastName: true,
                        },
                    },
                    {
                        filter: {
                            _id: siteId,
                        },
                    },
                ),
            )
            .execute(),
    );

    if (siteApprovers.length > 0) {
        return siteApprovers[0];
    }

    return null;
}

export async function submitForApproval(page: StockTransferOrderPage, siteApproverData: SiteApprovalData) {
    const usersList = await loadApprovers(page);

    const result = (await page.$.dialog.page(
        '@sage/xtrem-master-data/RequestApprovalDialog',
        {
            _id: siteApproverData.recordId ?? '',
            users: JSON.stringify(usersList),
            defaultApprover: JSON.stringify(siteApproverData.defaultApprover),
            substituteApprover: JSON.stringify(siteApproverData.substituteApprover),
            nodeName: JSON.stringify('@sage/xtrem-supply-chain/StockTransferOrder'),
        },
        { resolveOnCancel: true },
    )) as { isSent: boolean };

    if (result.isSent) {
        await page.$.refreshNavigationPanel();
        await page.$.router.refresh();
    }
}

async function getStockTransferOrderLinesForDimensions(parameters: GetStockTransferOrderLinesParameter) {
    return extractEdges(
        await parameters.stockTransferOrderPage.$.graph
            .node('@sage/xtrem-supply-chain/StockTransferOrderLine')
            .query(
                ui.queryUtils.edgesSelector(
                    {
                        _id: true,
                        storedDimensions: true,
                        storedAttributes: true,
                        document: {
                            number: true,
                        },
                    },

                    {
                        filter: {
                            document: {
                                number: parameters.recordNumber,
                            },
                        },
                    },
                ),
            )
            .execute(),
    );
}

export async function setDimensions(parameters: SetDimensionActionParameter) {
    let dataDetermined = false;
    let dimensionsToSet = '';
    let attributesToSet = '';
    const linesToSetDimensions = await getStockTransferOrderLinesForDimensions({
        stockTransferOrderPage: parameters.stockTransferOrderPage,
        recordNumber: parameters.recordNumber,
    });

    // eslint-disable-next-line no-restricted-syntax
    for (const lineToSetDimensions of linesToSetDimensions) {
        if (!dataDetermined) {
            const { defaultedFromItem, defaultDimensionsAttributes } = await getValuesForSetDimensionsFromMainList({
                page: parameters.stockTransferOrderPage,
                site: parameters.site || null,
                customer: parameters.customer || null,
            });
            const { resultDimensionsToSet, resultAttributesToSet, resultDataDetermined } =
                await getDimensionsForStockTransferLines({
                    line: lineToSetDimensions,
                    stockTransferPage: parameters.stockTransferOrderPage,
                    defaultDimensionsAttributes,
                    defaultedFromItem,
                });
            if (!resultDataDetermined) {
                break;
            }
            dimensionsToSet = resultDimensionsToSet;
            attributesToSet = resultAttributesToSet;
            dataDetermined = resultDataDetermined;
        }
        if (dataDetermined) {
            await parameters.stockTransferOrderPage.$.graph
                .node('@sage/xtrem-supply-chain/StockTransferOrderLine')
                .mutations.setDimension(true, {
                    baseDocumentItemLine: lineToSetDimensions._id,
                    storedDimensions: dimensionsToSet,
                    storedAttributes: attributesToSet,
                })
                .execute();
        }
    }
    parameters.stockTransferOrderPage.$.showToast(
        ui.localize(
            '@sage/xtrem-supply-chain/pages__stock_transfer_order___apply_dimensions_success',
            'Dimensions applied.',
        ),
        { type: 'success' },
    );
}
