import * as commonDistribution from '@sage/xtrem-distribution/build/lib/client-functions/common';
import { confirmDialogWithAcceptButtonText } from '@sage/xtrem-distribution/build/lib/client-functions/common';
import type {
    ConfirmShipmentMutationParameters,
    RevertShipmentMutationParameters,
} from '@sage/xtrem-distribution/build/lib/client-functions/interfaces/document-actions-functions';
import * as MasterDataUtils from '@sage/xtrem-master-data/build/lib/client-functions/utils';
import * as StockDetailHelper from '@sage/xtrem-stock-data/build/lib/client-functions/stock-details-helper';
import * as StockDocumentHelper from '@sage/xtrem-stock-data/build/lib/client-functions/stock-document-helper';
import type { StockTransferShipmentLine, StockTransferShipmentLineBinding } from '@sage/xtrem-supply-chain-api';
import * as ui from '@sage/xtrem-ui';
import type { StockTransferShipment as StockTransferShipmentPage } from '../pages/stock-transfer-shipment';
import type {
    PostActionParameters,
    PrintShipmentParameters,
    StockTransferShipmentParameter,
} from './interfaces/stock-transfer-shipment-actions-functions';
import * as displayButtonsManagement from './stock-transfer-shipment-display-buttons-management';

function _areAllLinesAllocated(pageInstance: StockTransferShipmentPage): boolean {
    return pageInstance.lines.value
        .filter(line => line.stockTransactionStatus !== 'completed' && line.allocationStatus !== 'notManaged')
        .every(line => Number(line.quantityInStockUnit) - Number(line.quantityAllocatedInStockUnit) === 0);
}

export function areAllLinesAllocated(pageInstance: StockTransferShipmentPage) {
    return () => _areAllLinesAllocated(pageInstance);
}

export function runConfirmShipmentMutation(parameters: ConfirmShipmentMutationParameters) {
    return () =>
        parameters.pageInstance.$.graph
            .node('@sage/xtrem-supply-chain/StockTransferShipment')
            .mutations.confirm(true, { stockTransferShipmentNumber: parameters.number })
            .execute();
}

export function runRevertMutation(parameters: RevertShipmentMutationParameters) {
    return () =>
        parameters.pageInstance.$.graph
            .node('@sage/xtrem-supply-chain/StockTransferShipment')
            .mutations.revoke(true, { stockTransferShipmentNumber: parameters.number })
            .execute();
}

function lineNotesChanged(pageInstance: StockTransferShipmentPage) {
    return pageInstance.lines.value.some(
        line => line.internalNote?.value || line.isExternalNote || line.externalNote?.value,
    );
}

function headerNotesChanged(pageInstance: StockTransferShipmentPage) {
    return (
        pageInstance.internalNote.value !== '' ||
        pageInstance.isExternalNote.value ||
        pageInstance.externalNote.value !== ''
    );
}

export async function saveAction(pageInstance: StockTransferShipmentPage) {
    if (!pageInstance.$.recordId) {
        if (headerNotesChanged(pageInstance) || lineNotesChanged(pageInstance)) {
            pageInstance.isOverwriteNote.value = await commonDistribution.notesOverwriteWarning(pageInstance);
        } else {
            pageInstance.isOverwriteNote.value = false;
        }
    }
    await pageInstance.$standardSaveAction.execute(true);
}

export async function transferAllocationDialog(
    pageInstance: StockTransferShipmentPage,
    rowId: string,
    rowItem: ui.PartialCollectionValue<StockTransferShipmentLineBinding>,
): Promise<void> {
    if (pageInstance.number.value) {
        await MasterDataUtils.catchPanelCrossQuitButtonAsNoop(
            pageInstance.$.dialog.page(
                '@sage/xtrem-stock-data/TransferAllocationsDialog',
                {
                    item: JSON.stringify(rowItem.item),
                    site: JSON.stringify(pageInstance.stockSite.value),
                    shippingDate: JSON.stringify(pageInstance.date.value),
                    lineId: rowId,
                    requiredQuantity: rowItem.quantityInStockUnit ?? 0,
                    allocatedQuantity: rowItem.quantityAllocatedInStockUnit || 0,
                    documentTypes: JSON.stringify(['StockTransferOrderLine', 'StockTransferShipmentLine']),
                    documentNumber: pageInstance.number.value ?? 0,
                },
                {
                    rightAligned: false,
                    size: 'extra-large',
                    resolveOnCancel: false,
                },
            ),
        );
        await pageInstance.lines.refreshRecord(rowId);
        displayButtonsManagement.manageDisplayButtonAllOtherActions(pageInstance);
    }
}

export async function allocateStockDialog(
    pageInstance: StockTransferShipmentPage,
    rowId: string,
    rowItem: ui.PartialCollectionValue<StockTransferShipmentLineBinding>,
): Promise<void> {
    const line = MasterDataUtils.removeExtractEdgesPartial(rowItem);
    const allocationChanged = await MasterDataUtils.confirmDialogToBoolean(
        StockDetailHelper.editStockDetails(pageInstance, line, {
            movementType: 'allocation',
            data: {
                isEditable: ['readyToProcess', 'readyToShip'].includes(line.status),
                needFullAllocation: line.status === 'readyToShip',
                cannotOverAllocate: true,
                // TODO: once we put in place lineToLine
                // orderDocumentLine: stockTransferOrderLine?._id,
                orderDocumentLine: undefined,
                documentLineHasAlreadySomeAllocations: Number(line.quantityAllocatedInStockUnit) > 0,
                effectiveDate: pageInstance.date.value ?? '',
                documentLineSortValue: line._sortValue,
                documentLine: rowId,
                item: line.item?._id,
                stockSite: pageInstance.stockSite.value?._id,
                quantity: +line.quantityInStockUnit,
                unit: line.item.stockUnit?._id,
                searchCriteria: {
                    activeQuantityInStockUnit: +line.quantityInStockUnit,
                    item: line.item?._id,
                    site: pageInstance.stockSite.value?._id ?? '',
                    stockUnit: line.item.stockUnit?._id,
                    statusList: [{ statusType: 'accepted' }],
                },
            },
        }),
    );
    if (allocationChanged) {
        await pageInstance.lines.refresh();
    }
}

export async function issueStockDialog(
    pageInstance: StockTransferShipmentPage,
    rowId: string,
    rowItem: ui.PartialCollectionValue<StockTransferShipmentLineBinding>,
): Promise<void> {
    const line = MasterDataUtils.removeExtractEdgesPartial(rowItem);
    await MasterDataUtils.catchPanelCrossQuitButtonAsNoop(
        StockDetailHelper.editStockDetails(pageInstance, line, {
            movementType: 'issue',
            data: {
                documentLineSortValue: line._sortValue,
                documentLine: rowId,
                item: line.item?._id,
                stockSite: pageInstance.stockSite.value?._id,
                quantity: +line.quantityInStockUnit,
                number: pageInstance.number.value ?? '',
                unit: line.item.stockUnit?._id,
                stockDetailType: '@sage/xtrem-stock-data/StockChangeDetail',
            },
        }) as Promise<StockTransferShipmentLine>,
    );
}

async function printPackingSlip(pageInstance: StockTransferShipmentPage, parameters: PrintShipmentParameters) {
    await pageInstance.$.dialog
        .page(
            '@sage/xtrem-reporting/PrintDocument',
            { reportName: 'stockTransferPackingSlip', stockTransferShipment: parameters.recordId },
            { size: 'extra-large', resolveOnCancel: true },
        )
        .then(async () => {
            pageInstance.$.loader.isHidden = false;
            pageInstance.$.showToast(
                ui.localize(
                    '@sage/xtrem-supply-chain/pages__stock_transfer_shipment_shipment__printed',
                    'The stock transfer shipment was printed.',
                ),
                { type: 'success' },
            );
            await pageInstance.$.router.refresh();
            await pageInstance.$.refreshNavigationPanel();
            pageInstance.$.loader.isHidden = true;
        });
}

export async function printShipmentAction(
    pageInstance: StockTransferShipmentPage,
    parameters: PrintShipmentParameters,
) {
    if (parameters.status === 'readyToProcess' && parameters.recordId) {
        await pageInstance.$.dialog.page(
            '@sage/xtrem-reporting/PrintDocument',
            { reportName: 'stockTransferShipmentPickList', stockTransferShipment: parameters.recordId },
            { size: 'extra-large' },
        );
    } else {
        await printPackingSlip(pageInstance, parameters);
    }
}

async function checkForUpdate(parameters: StockTransferShipmentParameter) {
    // TODO: This is a hack. It can be removed when XT-23948 is ready.
    let refreshCounter = 0;
    const checkForStatusChanged = async () => {
        await parameters.stockTransferShipmentPage.status.refresh();
        const status = parameters.stockTransferShipmentPage.status.value;
        if (status === 'shipped') {
            await parameters.stockTransferShipmentPage.$.router.refresh(true);
            await parameters.stockTransferShipmentPage.$.refreshNavigationPanel();
            parameters.stockTransferShipmentPage.$.loader.isHidden = true;
            return;
        }
        refreshCounter += 1;
        if (refreshCounter < 10) {
            await new Promise(resolve => {
                setTimeout(() => resolve(checkForStatusChanged()), 1000);
            });
        } else {
            parameters.stockTransferShipmentPage.$.loader.isHidden = true;
        }
    };
    await checkForStatusChanged();
}

export async function postAction(
    stockTransferShipmentPage: StockTransferShipmentPage,
    parameters: PostActionParameters,
) {
    if (
        await confirmDialogWithAcceptButtonText(
            stockTransferShipmentPage,
            ui.localize(
                '@sage/xtrem-supply-chain/pages__stock_transfer_shipment__post_action_dialog_title',
                'Confirm posting',
            ),
            ui.localize(
                '@sage/xtrem-supply-chain/pages__stock_transfer_shipment__post_action_dialog_content',
                "You are about to change the status of this stock transfer shipment to 'Shipped'.",
            ),
            ui.localize('@sage/xtrem-supply-chain/pages-confirm-confirm', 'Confirm'),
        )
    ) {
        // Disable post button so the user cannot post twice
        stockTransferShipmentPage.post.isDisabled = true;
        stockTransferShipmentPage.revert.isDisabled = true;
        stockTransferShipmentPage.revert.isHidden = true;
        stockTransferShipmentPage.$.loader.isHidden = false;

        const postResult = await stockTransferShipmentPage.$.graph
            .node('@sage/xtrem-supply-chain/StockTransferShipment')
            .mutations.postToStock(true, { documentIds: [parameters.recordId] })
            .execute();

        StockDocumentHelper.catchPostingError(postResult);

        stockTransferShipmentPage.$.showToast(
            ui.localize(
                '@sage/xtrem-supply-chain/pages__stock_transfer_shipment__posted',
                'The stock transfer shipment was posted.',
            ),
            { type: 'success' },
        );
        await checkForUpdate({ stockTransferShipmentPage, recordId: parameters.recordId });
        stockTransferShipmentPage.$.loader.isHidden = true;
    }
}
