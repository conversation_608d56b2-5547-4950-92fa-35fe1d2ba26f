import { setDisplayOfCommonPageActions } from '@sage/xtrem-system/build/lib/client-functions/applicative-crud-actions';
import type { StockTransferReceipt as StockTransferReceiptPage } from '../pages/stock-transfer-receipt';
import * as displayButtons from './stock-transfer-receipt-display-buttons';

export function manageDisplayButtonPostAction(pageInstance: StockTransferReceiptPage, isDirty: boolean) {
    pageInstance.post.isHidden = displayButtons.isHiddenButtonPostOrRevertAction({
        parameters: {
            status: pageInstance.status.value,
            stockTransactionStatus: pageInstance.stockTransactionStatus.value,
        },
        recordId: pageInstance.$.recordId,
        isDirty,
    });
}

export function manageDisplayButtonRepostAction(pageInstance: StockTransferReceiptPage, isDirty: boolean) {
    pageInstance.repost.isHidden = displayButtons.isHiddenButtonRepostAction({
        parameters: { fromNotificationHistory: pageInstance.fromNotificationHistory },
        recordId: pageInstance.$.recordId,
        isDirty,
    });
}

export function manageDisplayButtonDefaultDimensionAction(pageInstance: StockTransferReceiptPage) {
    pageInstance.defaultDimension.isHidden = displayButtons.isHiddenButtonDefaultDimensionAction({
        parameters: { status: pageInstance.status.value, displayStatus: pageInstance.displayStatus.value },
        recordId: pageInstance.$.recordId,
    });

    pageInstance.defaultDimension.isDisabled = displayButtons.isDisabledButtonDefaultDimensionAction({
        parameters: {
            stockSite: pageInstance.stockSite.value,
        },
    });
}

export function manageDisplayButtonAllOtherActions(pageInstance: StockTransferReceiptPage, isDirty = false) {
    // footer business actions
    manageDisplayButtonPostAction(pageInstance, isDirty);
    manageDisplayButtonRepostAction(pageInstance, isDirty);
    // // other header actions
    manageDisplayButtonDefaultDimensionAction(pageInstance);
}

function manageDisplayButtonCRUDActions(pageInstance: StockTransferReceiptPage, isDirty = false) {
    pageInstance.save.isDisabled = displayButtons.isDisabledButtonSaveOrCancelAction({
        parameters: { status: pageInstance.status.value },
        recordId: pageInstance.$.recordId,
        isDirty,
    });
    pageInstance.$standardCancelAction.isDisabled = displayButtons.isDisabledButtonSaveOrCancelAction({
        parameters: { status: pageInstance.status.value },
        recordId: pageInstance.$.recordId,
        isDirty,
    });
}

function _manageDisplayAdditionalPageActions(pageInstance: StockTransferReceiptPage, isDirty = false) {
    manageDisplayButtonCRUDActions(pageInstance, isDirty);
    manageDisplayButtonAllOtherActions(pageInstance, isDirty);
}

export function _manageDisplayApplicativePageActions(pageInstance: StockTransferReceiptPage, isDirty = false) {
    setDisplayOfCommonPageActions({
        page: pageInstance,
        isDirty,
        save: pageInstance.save,
        cancel: pageInstance.$standardCancelAction,
        remove: [],
        businessActions: [
            pageInstance.$standardOpenCustomizationPageWizardAction,
            pageInstance.post,
            pageInstance.repost,
        ],
    });
    _manageDisplayAdditionalPageActions(pageInstance, isDirty);
}
