import { extractEdges, withoutEdges, type ExtractEdgesPartial } from '@sage/xtrem-client';
import { DateValue } from '@sage/xtrem-date-time';
import * as commonDistribution from '@sage/xtrem-distribution/build/lib/client-functions/common';
import { initDefaultDimensions } from '@sage/xtrem-finance-data/build/lib/client-functions/attributes-and-dimensions';
import type * as LandedCostInterfaces from '@sage/xtrem-landed-cost/build/lib/client-functions/interfaces';
import type { BaseStatus, Currency, Customer } from '@sage/xtrem-master-data-api';
import { queryItemSite } from '@sage/xtrem-master-data/build/lib/client-functions/common';
import { getAddressDetail } from '@sage/xtrem-master-data/build/lib/shared-functions/address-functions';
import { convertAmount } from '@sage/xtrem-master-data/build/lib/shared-functions/common';
import type { GraphApi, StockTransferOrderLineBinding } from '@sage/xtrem-supply-chain-api';
import type { Site } from '@sage/xtrem-system-api';
import * as ui from '@sage/xtrem-ui';
import type { StockTransferOrder as StockTransferOrderPage } from '../pages/stock-transfer-order';

function mapLandedCostSummary(aggregateLandedCosts: LandedCostInterfaces.LandedCostTypeSummaryLine[]): {
    tableLandedCostsValues: LandedCostInterfaces.LandedCostTypeSummaryLineBinding[];
    total: number;
} {
    let total = 0;
    const tableLandedCostsValues = aggregateLandedCosts.map((line, index) => {
        total += Number(line.actualCostAmountInCompanyCurrency);
        return {
            _id: String(index + 1),
            landedCostType: line.landedCostType,
            actualCostAmountInCompanyCurrency: Number(line.actualCostAmountInCompanyCurrency),
            actualAllocatedCostAmountInCompanyCurrency: Number(line.actualAllocatedCostAmountInCompanyCurrency),
        } as LandedCostInterfaces.LandedCostTypeSummaryLineBinding;
    });
    return { tableLandedCostsValues, total };
}

export async function stockTransferOrderApproving(pageInstance: StockTransferOrderPage) {
    pageInstance.$.loader.isHidden = false;

    const result = await pageInstance.$.graph
        .node('@sage/xtrem-supply-chain/StockTransferOrder')
        .mutations.approve(true, { stockTransferOrder: pageInstance.$.recordId ?? '' })
        .execute();

    pageInstance.$.showToast(result, { type: 'success' });
    pageInstance.$.router.goTo(`@sage/xtrem-supply-chain/StockTransferOrder`, { _id: pageInstance.$.recordId ?? '' });
    pageInstance.$.loader.isHidden = true;
}

export async function stockTransferOrderRejecting(pageInstance: StockTransferOrderPage) {
    pageInstance.$.loader.isHidden = false;

    const result = await pageInstance.$.graph
        .node('@sage/xtrem-supply-chain/StockTransferOrder')
        .mutations.reject(true, { stockTransferOrder: pageInstance.$.recordId ?? '' })
        .execute();

    pageInstance.$.showToast(result, { type: 'success' });
    pageInstance.$.router.goTo(`@sage/xtrem-supply-chain/StockTransferOrder`, { _id: pageInstance.$.recordId ?? '' });
    pageInstance.$.loader.isHidden = true;
}

export function isStockTransferOrderLinePropertyDisabled(orderStatus: BaseStatus, lineStatus: BaseStatus) {
    const isDisabled = orderStatus === 'closed';

    switch (lineStatus) {
        case 'pending':
        case 'inProgress':
        case 'closed':
            return true;
        default:
            return isDisabled;
    }
}

export function isExchangeRateHidden(
    currency: ExtractEdgesPartial<Currency> | null,
    site: ExtractEdgesPartial<Site> | null,
    customer: ExtractEdgesPartial<Customer> | null,
) {
    if (site?.legalCompany?.currency?.id && currency?.id && customer) {
        return site.legalCompany.currency.id === currency.id;
    }
    return true;
}

/**
 * Checks if a site has stock transfer order pending approval.
 * @param _id : the id of the site.
 * @param graph
 */
export async function stockTransferOrdersPendingApproval(
    graph: ui.GraphQLApi<GraphApi>,
    sysIdSite: string,
): Promise<boolean> {
    const stockTransferOrdersPendingApprovalQuery = extractEdges(
        await graph
            .node('@sage/xtrem-supply-chain/StockTransferOrder')
            .aggregate.query(
                ui.queryUtils.edgesSelector(
                    { group: { _id: { _by: 'value' } } },
                    { filter: { site: { _eq: sysIdSite }, approvalStatus: { _eq: 'pendingApproval' } }, first: 1 },
                ),
            )
            .execute(),
    );

    return !!stockTransferOrdersPendingApprovalQuery.length;
}

export function disableSomeHeaderPropertiesIfLines(pageInstance: StockTransferOrderPage) {
    const isDisabled = pageInstance.lines.value.length > 0;
    pageInstance.site.isReadOnly = isDisabled;
    pageInstance.receivingSite.isReadOnly = isDisabled;
}

export async function initPage(pageInstance: StockTransferOrderPage) {
    pageInstance.landedCostsSection.isHidden = true;
    if (pageInstance.$.recordId) {
        if (pageInstance.status.value === 'closed' || pageInstance.status.value === 'inProgress') {
            pageInstance.shippingDate.isDisabled = true;
            pageInstance.requestedDeliveryDate.isDisabled = true;
            pageInstance.expectedDeliveryDate.isDisabled = true;
            pageInstance.doNotShipBeforeDate.isDisabled = true;
            pageInstance.doNotShipAfterDate.isDisabled = true;
            pageInstance.date.isDisabled = true;
            pageInstance.shipToCustomerAddress.isDisabled = true;
            pageInstance.incoterm.isDisabled = true;
            pageInstance.deliveryMode.isDisabled = true;
            pageInstance.deliveryLeadTime.isDisabled = true;
            pageInstance.shipToAddress.isDisabled = true;
        } else {
            pageInstance.shippingDate.isDisabled = false;
            pageInstance.requestedDeliveryDate.isDisabled = false;
            pageInstance.expectedDeliveryDate.isDisabled = false;
            pageInstance.doNotShipBeforeDate.isDisabled = false;
            pageInstance.doNotShipAfterDate.isDisabled = false;
            pageInstance.date.isDisabled = pageInstance.shippingStatus.value !== 'notShipped';
            pageInstance.shipToCustomerAddress.isDisabled = false;
            pageInstance.incoterm.isDisabled = false;
            pageInstance.deliveryMode.isDisabled = false;
            pageInstance.deliveryLeadTime.isDisabled = false;
            pageInstance.shipToAddress.isDisabled = false;
            disableSomeHeaderPropertiesIfLines(pageInstance);
        }
        if (
            pageInstance.$.isServiceOptionEnabled('landedCostOption') &&
            pageInstance.$.isServiceOptionEnabled('landedCostStockTransferOption') &&
            pageInstance.jsonAggregateLandedCostTypes.value
        ) {
            pageInstance.landedCostsSection.isHidden = pageInstance.status.value === 'draft';
            const landedCostSummary = mapLandedCostSummary(
                JSON.parse(
                    pageInstance.jsonAggregateLandedCostTypes.value,
                ) as LandedCostInterfaces.LandedCostTypeSummaryLine[],
            );
            pageInstance.landedCosts.value = landedCostSummary.tableLandedCostsValues;
            pageInstance.totalActualLandedCostsInCompanyCurrency.value = landedCostSummary.total;
        }
        pageInstance.initLinesLength = Number(pageInstance.lines.value.length);
    } else {
        pageInstance.date.value = DateValue.today().toString();
        pageInstance.status.value = 'draft';
        pageInstance.shippingStatus.value = 'notShipped';
    }

    if (pageInstance.stockSite.value && pageInstance.shipToCustomer.value) {
        pageInstance._defaultDimensionsAttributes = await initDefaultDimensions({
            page: pageInstance,
            dimensionDefinitionLevel: 'intersiteTransferOrder',
            site: pageInstance.stockSite.value,
            customer: pageInstance.shipToCustomer.value,
            supplier: pageInstance.supplier.value,
            receivingSite: pageInstance.receivingSite.value,
        });
    }

    pageInstance.confirmDialogResponseOnShipToAddressUpdate = false;

    pageInstance.sendEmailSection.title =
        pageInstance.status.value === 'draft'
            ? ui.localize(
                  '@sage/xtrem-supply-chain/pages__sales_draft__section_title_send_stock_transfer_order_dialog_',
                  'Send draft stock transfer order',
              )
            : ui.localize(
                  '@sage/xtrem-supply-chain/pages__stock_transfer_order_section_title__send_stock_transfer_order_dialog',
                  'Send stock transfer order',
              );

    if (pageInstance.$.queryParameters.action) {
        switch (pageInstance.$.queryParameters.action) {
            case 'approved':
                await stockTransferOrderApproving(pageInstance);
                break;
            case 'rejected':
                await stockTransferOrderRejecting(pageInstance);
                break;
            default:
                throw new Error(
                    ui.localize(
                        '@sage/xtrem-supply-chain/pages__stock_transfer_order___no_parameters',
                        'No action set.',
                    ),
                );
        }
    }
}

export async function updateExpectedDeliveryDate(pageInstance: StockTransferOrderPage) {
    pageInstance.expectedDeliveryDate.value = await pageInstance.$.graph
        .node('@sage/xtrem-supply-chain/StockTransferOrder')
        .mutations.addWorkDays(true, {
            shippingDate: pageInstance.shippingDate.value?.toString() ?? '',
            deliveryLeadTime: pageInstance.deliveryLeadTime.value?.toString() ?? '',
            workDays: pageInstance.workDays.value ?? '',
        })
        .execute();
}

export async function updateShippingDate(pageInstance: StockTransferOrderPage) {
    if (pageInstance.requestedDeliveryDate.value && pageInstance.date.value) {
        pageInstance.shippingDate.value = await pageInstance.$.graph
            .node('@sage/xtrem-supply-chain/StockTransferOrder')
            .mutations.subWorkDays(true, {
                requestedDeliveryDate: pageInstance.requestedDeliveryDate.value,
                orderDate: pageInstance.date.value,
                doNotShipBeforeDate: pageInstance.doNotShipBeforeDate.value ?? null,
                doNotShipAfterDate: pageInstance.doNotShipAfterDate.value ?? null,
                deliveryLeadTime: pageInstance.deliveryLeadTime.value ?? '',
                workDaysMask: pageInstance.workDays.value ?? '',
            })
            .execute();
        await updateExpectedDeliveryDate(pageInstance);
    }
}

export async function updateShippingDateLine(
    pageInstance: StockTransferOrderPage,
    rowItem: ui.PartialCollectionValue<StockTransferOrderLineBinding>,
) {
    if (
        rowItem.requestedDeliveryDate &&
        pageInstance.date.value &&
        rowItem.deliveryLeadTime !== undefined &&
        pageInstance.workDays.value
    ) {
        rowItem.shippingDate = await pageInstance.$.graph
            .node('@sage/xtrem-supply-chain/StockTransferOrder')
            .mutations.subWorkDays(true, {
                requestedDeliveryDate: rowItem.requestedDeliveryDate,
                orderDate: pageInstance.date.value,
                doNotShipBeforeDate: rowItem.doNotShipBeforeDate,
                doNotShipAfterDate: rowItem.doNotShipAfterDate,
                deliveryLeadTime: rowItem.deliveryLeadTime,
                workDaysMask: pageInstance.workDays.value,
            })
            .execute();
        rowItem.expectedDeliveryDate = await pageInstance.$.graph
            .node('@sage/xtrem-supply-chain/StockTransferOrder')
            .mutations.addWorkDays(true, {
                shippingDate: rowItem.shippingDate,
                deliveryLeadTime: rowItem.deliveryLeadTime,
                workDays: pageInstance.workDays.value,
            })
            .execute();
    }
    pageInstance.lines.addOrUpdateRecordValue(rowItem);
}

export async function recomputeShippingDateAndDeliveryDate(pageInstance: StockTransferOrderPage) {
    if (
        pageInstance.shippingDate.value &&
        pageInstance.workDays.value &&
        pageInstance.requestedDeliveryDate.value &&
        !commonDistribution.isNotWorkDay(
            +pageInstance.workDays.value,
            DateValue.parse(pageInstance.requestedDeliveryDate.value),
        )
    ) {
        if (
            await commonDistribution.confirmDialogWithAcceptButtonText(
                pageInstance,
                ui.localize(
                    '@sage/xtrem-supply-chain/pages__stock_transfer_order__recompute_shipping_and_delivery_dates_dialog_title',
                    'Confirm update',
                ),
                ui.localize(
                    '@sage/xtrem-supply-chain/pages__stock_transfer_order__recompute_shipping_and_delivery_dates_dialog_content',
                    'You are about to update the shipping date and the delivery date.',
                ),
                ui.localize('@sage/xtrem-supply-chain/pages-confirm-update', 'Update'),
            )
        ) {
            await updateShippingDate(pageInstance);
        }
    } else {
        await updateShippingDate(pageInstance);
    }
}

export async function recomputeShippingDateAndDeliveryDateLine(
    pageInstance: StockTransferOrderPage,
    rowData: ui.PartialCollectionValue<StockTransferOrderLineBinding>,
) {
    if (
        rowData &&
        rowData.shippingDate &&
        pageInstance.workDays.value &&
        rowData.requestedDeliveryDate &&
        !commonDistribution.isNotWorkDay(+pageInstance.workDays.value, DateValue.parse(rowData.requestedDeliveryDate))
    ) {
        if (
            await commonDistribution.confirmDialogWithAcceptButtonText(
                pageInstance,
                ui.localize(
                    '@sage/xtrem-supply-chain/pages__stock_transfer_order__recompute_shipping_and_delivery_dates_dialog_title',
                    'Confirm update',
                ),
                ui.localize(
                    '@sage/xtrem-supply-chain/pages__stock_transfer_order__recompute_shipping_and_delivery_dates_dialog_content',
                    'You are about to update the shipping date and the delivery date.',
                ),
                ui.localize('@sage/xtrem-supply-chain/pages-confirm-update', 'Update'),
            )
        ) {
            await updateShippingDateLine(pageInstance, rowData);
        }
    } else {
        await updateShippingDateLine(pageInstance, rowData);
    }
}

export async function fillStockTransferShipmentLines(pageInstance: StockTransferOrderPage, rowId: string) {
    const oldIsDirty = pageInstance.$.isDirty;
    pageInstance.linkToStockTransferShipmentLine.value.forEach(podLine => {
        pageInstance.linkToStockTransferShipmentLine.removeRecord(podLine._id);
    });
    const lines = await pageInstance.$.graph
        .node('@sage/xtrem-supply-chain/StockTransferShipmentLineToStockTransferOrderLine')
        .query(
            ui.queryUtils.edgesSelector(
                {
                    from: {
                        _id: true,
                        item: {
                            _id: true,
                            name: true,
                        },
                        status: true,
                        quantity: true,
                        unit: {
                            _id: true,
                            name: true,
                            decimalDigits: true,
                            id: true,
                            symbol: true,
                        },
                        quantityInStockUnit: true,
                        stockUnit: {
                            _id: true,
                            name: true,
                            decimalDigits: true,
                            id: true,
                            symbol: true,
                        },
                        document: {
                            _id: true,
                            number: true,
                        },
                    },
                },
                {
                    filter: { to: { _id: rowId } },
                },
            ),
        )
        .execute();
    pageInstance.linkToStockTransferShipmentLine.value = lines.edges.map(e => e.node.from);
    pageInstance.linkToStockTransferShipmentLine.isHidden =
        pageInstance.linkToStockTransferShipmentLine.value.length === 0;
    if (!oldIsDirty && pageInstance.$.isDirty) {
        pageInstance.$.setPageClean();
    }
}

export async function getReceivingSites(pageInstance: StockTransferOrderPage) {
    const financialSite = pageInstance.financialSite.value;
    const shippingSite = pageInstance.site.value;

    if (!(shippingSite?._id && financialSite?._id && financialSite?.legalCompany?._id)) {
        return [];
    }

    return withoutEdges(
        await pageInstance.$.graph
            .node('@sage/xtrem-system/Site')
            .query(
                ui.queryUtils.edgesSelector(
                    {
                        _id: true,
                        isFinance: true,
                        financialSite: { _id: true },
                    },
                    {
                        filter: {
                            isInventory: true,
                            legalCompany: { _id: { _eq: financialSite.legalCompany._id } },
                            businessEntity: {
                                isCustomer: true,
                                customer: { isActive: true },
                            },
                        },
                    },
                ),
            )
            .execute(),
    )
        .filter(site => {
            const financialSiteId = site.isFinance ? site._id : (site.financialSite._id ?? site._id);
            return site._id !== shippingSite._id && financialSiteId === financialSite._id;
        })
        .map(site => site._id);
}

export async function getItemSiteAvailableStockQuantity(
    pageInstance: StockTransferOrderPage,
    rowData: ui.PartialCollectionValue<StockTransferOrderLineBinding>,
) {
    const { item, stockSite } = rowData;
    if (item?._id && stockSite?._id) {
        const [{ acceptedStockQuantity, allocatedQuantity, inStockQuantity }] = await queryItemSite(
            pageInstance,
            item,
            stockSite,
        );

        if (acceptedStockQuantity && allocatedQuantity && inStockQuantity) {
            const availableQuantityInStockUnit = Number(acceptedStockQuantity) - Number(allocatedQuantity);

            Object.assign(rowData, {
                availableQuantityInStockUnit: String(availableQuantityInStockUnit),
                stockShortageInStockUnit: String(
                    Math.max(Number(rowData.quantity) - (availableQuantityInStockUnit + Number(allocatedQuantity)), 0),
                ),
                stockShortageStatus: Number(rowData.stockShortageInStockUnit) > 0,
                stockOnHand: String(inStockQuantity),
            });
        }
    } else {
        Object.assign(rowData, {
            availableQuantityInStockUnit: '0',
            stockShortageInStockUnit: '0',
            stockShortageStatus: false,
            quantityToShipInProgressInStockUnit: '0',
            shippedQuantityInStockUnit: '0',
            remainingQuantityToShipInStockUnit: '0',
            stockOnHand: '0',
        });
    }
}

export async function updateCosts(
    pageInstance: StockTransferOrderPage,
    rowData: ExtractEdgesPartial<StockTransferOrderLineBinding>,
) {
    const siteCurrency = pageInstance.site.value?.legalCompany?.currency;
    const newCostInCompanyCurrency = await commonDistribution.calculateStockCost(
        pageInstance,
        rowData.item?._id ?? '',
        rowData.stockSite?._id ?? '',
        Number(rowData.quantityInStockUnit),
    );
    const newStockCostAmount = convertAmount(
        newCostInCompanyCurrency,
        pageInstance.companyFxRateDivisor?.value ?? 0,
        pageInstance.companyFxRate?.value ?? 0,
        siteCurrency?.decimalDigits ?? 2,
        pageInstance.currency?.value?.decimalDigits ?? 2,
    );

    rowData.stockCostAmount = newStockCostAmount.toString();
    rowData.stockCostUnit = (newStockCostAmount / Number(rowData.quantityInStockUnit)).toString();
    pageInstance.lines.addOrUpdateRecordValue(rowData);
}

export async function fetchDefaultsFromShipToAddress(pageInstance: StockTransferOrderPage) {
    if (!pageInstance.shipToCustomerAddress.value?.deliveryDetail?.shipmentSite && pageInstance.stockSite.value) {
        await pageInstance.$.fetchDefaults([
            'shipToAddress',
            'shipToContact',
            'incoterm',
            'deliveryMode',
            'deliveryLeadTime',
            'workDays',
        ]);
    } else {
        await pageInstance.$.fetchDefaults([
            'shipToAddress',
            'shipToContact',
            'incoterm',
            'deliveryMode',
            'deliveryLeadTime',
            'workDays',
            'stockSite',
        ]);
    }
}

export async function cascadeAndFetchDefaultsFromShipToAddress(pageInstance: StockTransferOrderPage) {
    if (
        pageInstance.incoterm.value ||
        pageInstance.deliveryMode.value ||
        pageInstance.deliveryLeadTime.value ||
        pageInstance.shippingDate.value ||
        pageInstance.expectedDeliveryDate.value
    ) {
        if (
            await commonDistribution.confirmDialogWithAcceptButtonText(
                pageInstance,
                ui.localize(
                    '@sage/xtrem-supply-chain/pages__stock_transfer_order__cascade_ship_to_address_update_dialog_title',
                    'Confirm update',
                ),
                ui.localize(
                    '@sage/xtrem-supply-chain/pages__stock_transfer_order__cascade_ship_to_address_update_dialog_content',
                    'You are about to update the delivery information.',
                ),
                ui.localize('@sage/xtrem-supply-chain/pages-confirm-update', 'Update'),
            )
        ) {
            await fetchDefaultsFromShipToAddress(pageInstance);
            await updateShippingDate(pageInstance);
        } else {
            await pageInstance.$.fetchDefaults(['shipToAddress']);
        }
    } else {
        await fetchDefaultsFromShipToAddress(pageInstance);
        await updateShippingDate(pageInstance);
    }
}

export async function cascadeAndFetchDefaultsFromShipToAddressLine(
    pageInstance: StockTransferOrderPage,
    rowItem: ui.PartialCollectionValue<StockTransferOrderLineBinding>,
) {
    if (
        pageInstance.deliveryMode.value ||
        pageInstance.deliveryLeadTime.value ||
        pageInstance.shippingDate.value ||
        pageInstance.expectedDeliveryDate.value
    ) {
        if (
            await commonDistribution.confirmDialogWithAcceptButtonText(
                pageInstance,
                ui.localize(
                    '@sage/xtrem-supply-chain/pages__stock_transfer_order__cascade_ship_to_address_update_dialog_title',
                    'Confirm update',
                ),
                ui.localize(
                    '@sage/xtrem-supply-chain/pages__stock_transfer_order__cascade_ship_to_address_update_dialog_content',
                    'You are about to update the delivery information.',
                ),
                ui.localize('@sage/xtrem-supply-chain/pages-confirm-update', 'Update'),
            )
        ) {
            pageInstance.shipToAddressLineDetail.value = rowItem.shipToCustomerAddress
                ? getAddressDetail(rowItem.shipToCustomerAddress)
                : undefined;
            rowItem.deliveryMode = rowItem.shipToCustomerAddress?.deliveryDetail?.mode;
            rowItem.deliveryLeadTime = rowItem.shipToCustomerAddress?.deliveryDetail?.leadTime
                ? rowItem.shipToCustomerAddress?.deliveryDetail?.leadTime
                : 0;
            await updateShippingDateLine(pageInstance, rowItem);
        } else {
            pageInstance.shipToAddressLineDetail.value = rowItem.shipToCustomerAddress
                ? getAddressDetail(rowItem.shipToCustomerAddress)
                : undefined;
        }
    } else {
        pageInstance.shipToAddressLineDetail.value = rowItem.shipToCustomerAddress
            ? getAddressDetail(rowItem.shipToCustomerAddress)
            : undefined;
        rowItem.deliveryMode = rowItem.shipToCustomerAddress?.deliveryDetail?.mode;
        rowItem.deliveryLeadTime = rowItem.shipToCustomerAddress?.deliveryDetail?.leadTime
            ? rowItem.shipToCustomerAddress?.deliveryDetail?.leadTime
            : 0;
        await updateShippingDateLine(pageInstance, rowItem);
        pageInstance.lines.addOrUpdateRecordValue(rowItem);
    }
}
