import { DateValue } from '@sage/xtrem-date-time';
import * as commonDistribution from '@sage/xtrem-distribution/build/lib/client-functions/common';
import { confirmDialogWithAcceptButtonText } from '@sage/xtrem-distribution/build/lib/client-functions/common';
import * as MasterDataUtils from '@sage/xtrem-master-data/build/lib/client-functions/utils';
import * as StockDetailHelper from '@sage/xtrem-stock-data/build/lib/client-functions/stock-details-helper';
import type { StockTransferReceiptLine, StockTransferReceiptLineBinding } from '@sage/xtrem-supply-chain-api';
import * as ui from '@sage/xtrem-ui';
import type { StockTransferReceipt as StockTransferReceiptPage } from '../pages/stock-transfer-receipt';
import type {
    PostActionParameters,
    StockTransferReceiptParameter,
} from './interfaces/stock-transfer-receipt-actions-function';

function lineNotesChanged(pageInstance: StockTransferReceiptPage) {
    return pageInstance.lines.value.some(
        line => line.internalNote?.value || line.isExternalNote || line.externalNote?.value,
    );
}

function headerNotesChanged(pageInstance: StockTransferReceiptPage) {
    return (
        pageInstance.internalNote.value !== '' ||
        pageInstance.isExternalNote.value ||
        pageInstance.externalNote.value !== ''
    );
}

/**
 * Stock detail management.
 * Calls the StockReceiptDetailsPanel and sets the line stockDetails to the return of the StockReceiptDetailsPanel
 */
export async function editStockDetailsDialog(
    pageInstance: StockTransferReceiptPage,
    rowItem: ui.PartialCollectionValue<StockTransferReceiptLineBinding>,
) {
    const line = MasterDataUtils.removeExtractEdgesPartial(rowItem);
    const isEditable = ['draft', 'error'].includes(line.stockTransactionStatus);

    await MasterDataUtils.applyPanelToLineIfChanged(
        pageInstance.lines,
        StockDetailHelper.editStockDetails(pageInstance, line, {
            movementType: 'receipt',
            data: {
                isEditable,
                effectiveDate: pageInstance.date.value ?? '',
                documentLineSortValue: line._sortValue,
                documentLine: line._id,
                item: line.item?._id,
                stockSite: pageInstance.site.value?._id,
                quantity: +line.quantityInStockUnit,
                unit: line.item.stockUnit?._id,
                stockDetailStatus: line.stockDetailStatus,
                jsonStockDetails: line.jsonStockDetails,
                trackCheckStock: 'track',
                stockTransactionStatus: line.stockTransactionStatus,
                cannotLoadExistingDetails: line.stockDetailStatus !== 'entered',
                originBaseDocumentLineSysId: Number(pageInstance.linkToStockTransferShipmentLine.value[0]?._id),
                baseDocumentLineSysId: Number(line._id),
                isTransferDocument: true,
                existingLot: undefined,
                lotCreateData: undefined,
                stockDetailType: '@sage/xtrem-stock-data/StockChangeDetail',
                fieldCustomizations: {
                    addStockDetail: {
                        isDisabled: false,
                    },
                    date: {
                        isHidden: true,
                    },
                    enterSerialNumbers: {
                        isDisabled: false,
                    },
                    quantityInStockUnit: {
                        isDisabled: true,
                    },
                    lotNumber: {
                        isDisabled: true,
                    },
                    expirationDate: {
                        isDisabled: true,
                    },
                    sublot: {
                        isDisabled: true,
                    },
                    supplierLot: {
                        isDisabled: true,
                    },
                    delete: {
                        isDisabled: true,
                    },
                    location: {
                        isDisabled: false,
                    },
                    status: {
                        isDisabled: false,
                    },
                },
            },
        }) as Promise<StockTransferReceiptLine>,
    );
}

export async function saveAction(pageInstance: StockTransferReceiptPage) {
    if (!pageInstance.$.recordId) {
        if (headerNotesChanged(pageInstance) || lineNotesChanged(pageInstance)) {
            pageInstance.isOverwriteNote.value = await commonDistribution.notesOverwriteWarning(pageInstance);
        } else {
            pageInstance.isOverwriteNote.value = false;
        }
    }
    await pageInstance.$standardSaveAction.execute(true);
}

function checkDateBeforePosting(pageInstance: StockTransferReceiptPage, parameters: PostActionParameters) {
    if (parameters.date === DateValue.today().toString()) return true;
    return confirmDialogWithAcceptButtonText(
        pageInstance,
        ui.localize(
            '@sage/xtrem-supply-chain/pages__stock_transfer_receipt__check_date_before_posting_dialog_title',
            'Confirm receipt date before posting',
        ),
        ui.localize(
            '@sage/xtrem-supply-chain/pages__stock_transfer_receipt__check_date_before_posting_dialog_content',
            "The receipt date is different to today's date. Do you want to continue?",
        ),
        ui.localize('@sage/xtrem-supply-chain/pages-confirm-confirm', 'Confirm'),
    );
}

async function checkForUpdate(parameters: StockTransferReceiptParameter) {
    let refreshCounter = 0;
    const checkForStatusChanged = async () => {
        await parameters.stockTransferReceiptPage.status.refresh();
        const status = parameters.stockTransferReceiptPage.status.value;
        if (status === 'received') {
            await parameters.stockTransferReceiptPage.$.router.refresh(true);
            await parameters.stockTransferReceiptPage.$.refreshNavigationPanel();
            parameters.stockTransferReceiptPage.$.loader.isHidden = true;
            return;
        }
        refreshCounter += 1;
        if (refreshCounter < 10) {
            await new Promise(resolve => {
                setTimeout(() => resolve(checkForStatusChanged()), 1000);
            });
        } else {
            parameters.stockTransferReceiptPage.$.loader.isHidden = true;
        }
    };
    await checkForStatusChanged();
}

export async function postAction(stockTransferReceiptPage: StockTransferReceiptPage, parameters: PostActionParameters) {
    if (
        (await checkDateBeforePosting(stockTransferReceiptPage, parameters)) &&
        (await confirmDialogWithAcceptButtonText(
            stockTransferReceiptPage,
            ui.localize(
                '@sage/xtrem-supply-chain/pages__stock_transfer_receipt__post_action_dialog_title',
                'Confirm posting',
            ),
            ui.localize(
                '@sage/xtrem-supply-chain/pages__stock_transfer_receipt__post_action_dialog_content',
                "You are about to change the status of this stock transfer receipt to 'Received'.",
            ),
            ui.localize('@sage/xtrem-supply-chain/pages-confirm-confirm', 'Confirm'),
        ))
    ) {
        // Disable post button so the user cannot post twice
        stockTransferReceiptPage.post.isDisabled = true;
        stockTransferReceiptPage.$.loader.isHidden = false;

        await stockTransferReceiptPage.$.graph
            .node('@sage/xtrem-supply-chain/StockTransferReceipt')
            .mutations.post(true, { receipt: parameters.recordId ?? '' })
            .execute();

        stockTransferReceiptPage.$.showToast(
            ui.localize(
                '@sage/xtrem-supply-chain/pages__stock_transfer_receipt__posted',
                'The stock transfer receipt was posted.',
            ),
            { type: 'success' },
        );
        await checkForUpdate({ stockTransferReceiptPage, recordId: parameters.recordId });
        stockTransferReceiptPage.$.loader.isHidden = true;
    }
}
