import * as stepSequenceDocument from '@sage/xtrem-distribution/build/lib/client-functions/step-sequence';
import type { Dict } from '@sage/xtrem-shared';
import type * as ui from '@sage/xtrem-ui';
import type { StockTransferOrderStepSequenceStatus } from './interfaces/stock-transfer-order';

export function getStepSequence(approvalStatus: string | null, isStockTransferOrderApprovalManaged = false) {
    return [
        stepSequenceDocument.create,
        (isStockTransferOrderApprovalManaged ||
            (approvalStatus &&
                ['pendingApproval', 'approved', 'rejected', 'changeRequested'].includes(approvalStatus))) &&
        approvalStatus !== 'confirmed'
            ? stepSequenceDocument.approve
            : stepSequenceDocument.confirm,
        stepSequenceDocument.ship,
        stepSequenceDocument.receive,
    ];
}

function _setStepSequenceStatusObject(
    stepSequenceValues: StockTransferOrderStepSequenceStatus,
): Dict<ui.StepSequenceStatus> {
    return {
        [stepSequenceDocument.create]: stepSequenceValues.create,
        [stepSequenceDocument.confirm]: stepSequenceValues.confirm,
        [stepSequenceDocument.approve]: stepSequenceValues.approve,
        [stepSequenceDocument.ship]: stepSequenceValues.ship,
        [stepSequenceDocument.receive]: stepSequenceValues.receive,
    };
}

export function getDisplayStatusStepSequence(
    id: string | undefined,
    status: string,
    approvalStatus: string,
    shippingStatus: string,
    receivingStatus: string,
): Dict<ui.StepSequenceStatus> {
    if (approvalStatus && ['approved', 'rejected', 'confirmed'].includes(approvalStatus)) {
        if (shippingStatus === 'notShipped' && receivingStatus === 'notReceived') {
            return _setStepSequenceStatusObject({
                create: 'complete',
                confirm: 'complete',
                approve: 'complete',
                ship: 'current',
                receive: 'incomplete',
            });
        }
        if (shippingStatus !== 'notShipped' && receivingStatus === 'notReceived') {
            return _setStepSequenceStatusObject({
                create: 'complete',
                confirm: 'complete',
                approve: 'complete',
                ship: 'complete',
                receive: 'current',
            });
        }
        if (receivingStatus !== 'notReceived') {
            return _setStepSequenceStatusObject({
                create: 'complete',
                confirm: 'complete',
                approve: 'complete',
                ship: 'complete',
                receive: 'complete',
            });
        }
    }

    if (status === 'draft' && !id) {
        return _setStepSequenceStatusObject({
            create: 'current',
            confirm: 'incomplete',
            approve: 'incomplete',
            ship: 'incomplete',
            receive: 'incomplete',
        });
    }

    if (status === 'draft' && id) {
        return _setStepSequenceStatusObject({
            create: 'complete',
            confirm: 'current',
            approve: 'current',
            ship: 'incomplete',
            receive: 'incomplete',
        });
    }

    if (status === 'inProgress') {
        return _setStepSequenceStatusObject({
            create: 'complete',
            confirm: 'incomplete',
            approve: 'incomplete',
            ship: 'current',
            receive: 'incomplete',
        });
    }

    if (status === 'pending') {
        return _setStepSequenceStatusObject({
            create: 'complete',
            confirm: 'complete',
            approve: 'complete',
            ship: 'incomplete',
            receive: 'incomplete',
        });
    }

    if (approvalStatus && ['approved', 'confirmed'].includes(approvalStatus)) {
        if (status === 'closed') {
            return _setStepSequenceStatusObject({
                create: 'complete',
                confirm: 'complete',
                approve: 'complete',
                ship: 'incomplete',
                receive: 'incomplete',
            });
        }
    }
    if (status === 'closed') {
        return _setStepSequenceStatusObject({
            create: 'complete',
            confirm: 'incomplete',
            approve: 'incomplete',
            ship: 'incomplete',
            receive: 'incomplete',
        });
    }

    return _setStepSequenceStatusObject({
        create: 'current',
        confirm: 'incomplete',
        approve: 'incomplete',
        ship: 'incomplete',
        receive: 'incomplete',
    });
}
