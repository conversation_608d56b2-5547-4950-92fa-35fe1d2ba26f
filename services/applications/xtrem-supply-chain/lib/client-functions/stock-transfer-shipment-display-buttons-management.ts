import { setDisplayOfCommonPageActions } from '@sage/xtrem-system/build/lib/client-functions/applicative-crud-actions';
import type { StockTransferShipment as StockTransferShipmentPage } from '../pages/stock-transfer-shipment';
import * as displayButtons from './stock-transfer-shipment-display-buttons';

export function manageDisplayButtonConfirmAction(pageInstance: StockTransferShipmentPage, isDirty: boolean) {
    pageInstance.confirm.isHidden = displayButtons.isHiddenButtonConfirmAction({
        parameters: { status: pageInstance.status.value },
        recordId: pageInstance.$.recordId,
        isDirty,
    });
}

export function manageDisplayRevertAction(pageInstance: StockTransferShipmentPage, isDirty: boolean) {
    pageInstance.revert.isHidden = displayButtons.isHiddenButtonPostOrRevertAction({
        parameters: {
            status: pageInstance.status.value,
            stockTransactionStatus: pageInstance.stockTransactionStatus.value,
        },
        recordId: pageInstance.$.recordId,
        isDirty,
    });
}

export function manageDisplayButtonPostAction(pageInstance: StockTransferShipmentPage, isDirty: boolean) {
    pageInstance.post.isHidden = displayButtons.isHiddenButtonPostOrRevertAction({
        parameters: {
            status: pageInstance.status.value,
            stockTransactionStatus: pageInstance.stockTransactionStatus.value,
        },
        recordId: pageInstance.$.recordId,
        isDirty,
    });
}

export function manageDisplayButtonRepostAction(pageInstance: StockTransferShipmentPage, isDirty: boolean) {
    pageInstance.repost.isHidden = displayButtons.isHiddenButtonRepostAction({
        parameters: { fromNotificationHistory: pageInstance.fromNotificationHistory },
        recordId: pageInstance.$.recordId,
        isDirty,
    });
}

function manageDisplayButtonPrintAction(pageInstance: StockTransferShipmentPage, isDirty: boolean) {
    pageInstance.print.isHidden = displayButtons.isHiddenButtonPrintAction({
        parameters: {
            status: pageInstance.status.value,
        },
        recordId: pageInstance.$.recordId,
    });

    pageInstance.print.isDisabled = displayButtons.isDisabledButtonPrintAction({
        parameters: {
            status: pageInstance.status.value,
        },
        recordId: pageInstance.$.recordId,
        isDirty,
    });
}

export function manageDisplayButtonDefaultDimensionAction(pageInstance: StockTransferShipmentPage) {
    pageInstance.defaultDimension.isHidden = displayButtons.isHiddenButtonDefaultDimensionAction({
        parameters: { status: pageInstance.status.value, displayStatus: pageInstance.displayStatus.value },
        recordId: pageInstance.$.recordId,
    });

    pageInstance.defaultDimension.isDisabled = displayButtons.isDisabledButtonDefaultDimensionAction({
        parameters: {
            stockSite: pageInstance.stockSite.value,
            shipToCustomer: pageInstance.shipToCustomer.value,
            shippingDate: pageInstance.date.value,
        },
    });
}

export function manageDisplayButtonAllOtherActions(pageInstance: StockTransferShipmentPage, isDirty = false) {
    // footer business actions
    manageDisplayButtonConfirmAction(pageInstance, isDirty);
    manageDisplayRevertAction(pageInstance, isDirty);
    manageDisplayButtonPostAction(pageInstance, isDirty);
    manageDisplayButtonRepostAction(pageInstance, isDirty);
    // // other header actions
    manageDisplayButtonPrintAction(pageInstance, isDirty);
    manageDisplayButtonDefaultDimensionAction(pageInstance);
}

function manageDisplayButtonCRUDActions(pageInstance: StockTransferShipmentPage, isDirty = false) {
    pageInstance.save.isDisabled = displayButtons.isDisabledButtonSaveOrCancelAction({
        parameters: { status: pageInstance.status.value },
        recordId: pageInstance.$.recordId,
        isDirty,
    });
    pageInstance.$standardCancelAction.isDisabled = displayButtons.isDisabledButtonSaveOrCancelAction({
        parameters: { status: pageInstance.status.value },
        recordId: pageInstance.$.recordId,
        isDirty,
    });
    pageInstance.$standardDeleteAction.isHidden = displayButtons.isHiddenButtonDeleteAction({
        parameters: { status: pageInstance.status.value, displayStatus: pageInstance.displayStatus.value },
        recordId: pageInstance.$.recordId,
    });
}

function _manageDisplayAdditionalPageActions(pageInstance: StockTransferShipmentPage, isDirty = false) {
    manageDisplayButtonCRUDActions(pageInstance, isDirty);
    manageDisplayButtonAllOtherActions(pageInstance, isDirty);
}

export function _manageDisplayApplicativePageActions(pageInstance: StockTransferShipmentPage, isDirty = false) {
    setDisplayOfCommonPageActions({
        page: pageInstance,
        isDirty,
        save: pageInstance.save,
        cancel: pageInstance.$standardCancelAction,
        remove: pageInstance.$standardDeleteAction,
        businessActions: [
            pageInstance.$standardOpenCustomizationPageWizardAction,
            pageInstance.confirm,
            pageInstance.post,
            pageInstance.repost,
            pageInstance.revert,
        ],
    });
    _manageDisplayAdditionalPageActions(pageInstance, isDirty);
}
