import { withoutEdges } from '@sage/xtrem-client';
import type { GraphApi } from '@sage/xtrem-supply-chain-api';
import * as ui from '@sage/xtrem-ui';

export async function getStockTransferReceiptsFromStockTransferOrder(
    pageInstance: ui.Page<GraphApi>,
    stockTransferOrderId: string,
) {
    return withoutEdges(
        await pageInstance.$.graph
            .node('@sage/xtrem-supply-chain/StockTransferReceipt')
            .query(
                ui.queryUtils.edgesSelector(
                    {
                        _id: true,
                    },
                    {
                        filter: {
                            lines: {
                                _atLeast: 1,
                                linkToStockTransferShipmentLines: {
                                    _atLeast: 1,
                                    to: {
                                        linkToStockTransferOrderLines: {
                                            _atLeast: 1,
                                            to: {
                                                document: {
                                                    _id: { _eq: stockTransferOrderId },
                                                },
                                            },
                                        },
                                    },
                                },
                            },
                        },
                    },
                ),
            )
            .execute(),
    )
        .map(receipt => receipt._id)
        .reduce((acc: string[], receipt) => {
            if (!acc.includes(receipt)) acc.push(receipt);
            return acc;
        }, []);
}
