import type { ExtensionMembers, ValidationContext } from '@sage/xtrem-core';
import * as xtremFinanceData from '@sage/xtrem-finance-data';
import type * as xtremLandedCost from '@sage/xtrem-landed-cost';
import type * as xtremPurchasing from '@sage/xtrem-purchasing';
import type * as xtremSupply<PERSON>hain from '../..';

export async function checkStockTransactionStatus(
    cx: ValidationContext,
    landedCostAllocation: ExtensionMembers<
        xtremSupplyChain.nodeExtensions.LandedCostAllocationExtension & xtremLandedCost.nodes.LandedCostAllocation
    > &
        xtremSupplyChain.nodeExtensions.LandedCostAllocationExtension,
) {
    if (
        (await landedCostAllocation.allocatedDocumentType) === 'stockTransferReceipt' &&
        (await ((await landedCostAllocation.allocatedDocumentLine) as xtremSupplyChain.nodes.StockTransferReceiptLine)
            .stockTransactionStatus) !== 'completed'
    ) {
        cx.error.addLocalized(
            '@sage/xtrem-supply-chain/nodes__landed_cost_allocation__stock_transaction_status_not_completed',
            'The stock status of all stock transfer receipt lines allocated to the landed cost need to be Completed.',
        );
    }
}

export async function checkStatus(
    cx: ValidationContext,
    landedCostAllocation: ExtensionMembers<
        xtremSupplyChain.nodeExtensions.LandedCostAllocationExtension & xtremLandedCost.nodes.LandedCostAllocation
    > &
        xtremSupplyChain.nodeExtensions.LandedCostAllocationExtension,
) {
    if (
        (await landedCostAllocation.allocatedDocumentType) === 'stockTransferReceipt' &&
        (await ((await landedCostAllocation.allocatedDocumentLine) as xtremSupplyChain.nodes.StockTransferReceiptLine)
            .status) !== 'received'
    ) {
        cx.error.addLocalized(
            '@sage/xtrem-supply-chain/nodes__landed_cost_allocation__status_not_received',
            'The status of stock transfer receipt lines allocated to the landed cost need to be Received.',
        );
    }
}

export async function checkSite(
    cx: ValidationContext,
    landedCostAllocation: ExtensionMembers<
        xtremSupplyChain.nodeExtensions.LandedCostAllocationExtension & xtremLandedCost.nodes.LandedCostAllocation
    > &
        xtremSupplyChain.nodeExtensions.LandedCostAllocationExtension,
) {
    if ((await landedCostAllocation.allocatedDocumentType) === 'stockTransferReceipt') {
        const purchaseInvoiceFinancialSite = await (
            await ((await (await landedCostAllocation.line).documentLine) as xtremPurchasing.nodes.PurchaseInvoiceLine)
                .document
        ).site;

        const stockTransferReceiptLineFinancialSite = await xtremFinanceData.functions.getFinancialSite(
            await (
                (await landedCostAllocation.allocatedDocumentLine) as xtremSupplyChain.nodes.StockTransferReceiptLine
            ).site,
        );

        if (purchaseInvoiceFinancialSite !== stockTransferReceiptLineFinancialSite) {
            cx.error.addLocalized(
                '@sage/xtrem-supply-chain/nodes__landed_cost_allocation__financial_site',
                'The stock transfer receipt does not share the same financial site',
            );
        }
    }
}
