import type { ValidationContext, date } from '@sage/xtrem-core';
import { NodeStatus } from '@sage/xtrem-core';
import type * as xtremSupply<PERSON>hain from '../..';

export async function controlShippingDate(
    cx: ValidationContext,
    stockTransferOrderLineInstance: xtremSupplyChain.nodes.StockTransferOrderLine,
    val: date,
) {
    const document = await stockTransferOrderLineInstance.document;
    if ((await document.date) && val < (await document.date)) {
        cx.error.addLocalized(
            '@sage/xtrem-supply-chain/nodes__stock_transfer_order_line__stock_transfer_order_lines_control_shippingdate_with_orderdate',
            'The shipping date needs to be after the order date.',
        );
    }
    const doNotShipBeforeDate = await stockTransferOrderLineInstance.doNotShipBeforeDate;
    if (doNotShipBeforeDate && val < doNotShipBeforeDate) {
        cx.error.addLocalized(
            '@sage/xtrem-supply-chain/nodes__stock_transfer_order_line__stock_transfer_order_lines_control_shippingdate_with_doNotShipBeforeDate',
            'The shipping date needs to be after the Do not ship before date.',
        );
    }
    const doNotShipAfterDate = await stockTransferOrderLineInstance.doNotShipAfterDate;
    if (doNotShipAfterDate && val > doNotShipAfterDate) {
        cx.error.addLocalized(
            '@sage/xtrem-supply-chain/nodes__stock_transfer_order_line__stock_transfer_order_lines_control_shippingdate_with_doNotShipAfterDate',
            'The shipping date needs to be before the Do not ship after date.',
        );
    }
}

export async function quantityAllocatedInStockUnitIsGreaterThanRemainingQuantityToShipInStockUnit(
    cx: ValidationContext,
    stockTransferOrderLineInstance: xtremSupplyChain.nodes.StockTransferOrderLine,
) {
    if (
        stockTransferOrderLineInstance.$.status === NodeStatus.modified &&
        (await stockTransferOrderLineInstance.remainingQuantityToShipInStockUnit) >= 0
    ) {
        await cx.error
            .withMessage(
                '@sage/xtrem-supply-chain/nodes__stock_transfer_order_line__too_much_quantity_allocated,',
                'The allocated quantity on the stock transfer order line cannot be larger than the remaining quantity to ship.',
            )
            .if(await stockTransferOrderLineInstance.quantityAllocatedInStockUnit)
            .is.greater.than(await stockTransferOrderLineInstance.remainingQuantityToShipInStockUnit);
    }
}

export async function lineStatusInDraftOrPending(
    cx: ValidationContext,
    stockTransferOrderLineInstance: xtremSupplyChain.nodes.StockTransferOrderLine,
) {
    if (stockTransferOrderLineInstance.$.status === NodeStatus.added) {
        await cx.error
            .withMessage(
                '@sage/xtrem-supply-chain/nodes__stock_transfer_order_line__improper_status_during_creation',
                'The status for the stock transfer order line needs to be "Draft" or "Pending".',
            )
            .if(await stockTransferOrderLineInstance.status)
            .is.not.in(['draft', 'pending']);
    }
}
