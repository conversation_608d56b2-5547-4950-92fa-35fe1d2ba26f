import type { ValidationContext } from '@sage/xtrem-core';
import type * as xtremSupply<PERSON>hain from '../..';

export async function checkLineStatus(cx: ValidationContext, line: xtremSupplyChain.nodes.StockTransferShipmentLine) {
    await cx.error
        .withMessage(
            '@sage/xtrem-supply-chain/nodes__stock_transfer_shipment_line__line_status_must_be_ready_to_process',
            'The line status needs to be Ready to process.',
        )
        .if(await line.status)
        .is.not.equal.to('readyToProcess');
}

export async function checkIfSameSite(cx: ValidationContext, line: xtremSupplyChain.nodes.StockTransferShipmentLine) {
    if ((await (await line.document).site) !== (await line.site)) {
        cx.error.addLocalized(
            '@sage/xtrem-supply-chain/nodes__stock_transfer_shipment_line__site_must_have_the_same_value',
            'The line site needs to be the same as the transfer site.',
        );
    }
}

export async function checkDeleteLine(cx: ValidationContext, line: xtremSupplyChain.nodes.StockTransferShipmentLine) {
    if ((await line.status) !== 'readyToProcess' || (await (await line.document).status) !== 'readyToProcess') {
        cx.error.addLocalized(
            '@sage/xtrem-supply-chain/nodes__stock_transfer_shipment_line__deletion_is_not_allowed_line_is_ready_to_ship_or_has_already_been_shipped',
            'The line is ready to ship or has already been shipped. You cannot delete the line.',
        );
    }
}

export async function checkCreateLine(cx: ValidationContext, line: xtremSupplyChain.nodes.StockTransferShipmentLine) {
    if ((await (await line.document).status) !== 'readyToProcess') {
        cx.error.addLocalized(
            '@sage/xtrem-supply-chain/nodes__stock_transfer_shipment_line__creation_forbidden_improper_status',
            'The stock transfer shipment is ready to ship or has already been shipped. You cannot add a new line.',
        );
    }
}
