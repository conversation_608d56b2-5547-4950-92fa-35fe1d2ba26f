import type { ValidationContext } from '@sage/xtrem-core';
import * as xtremFinanceData from '@sage/xtrem-finance-data';
import type { StockTransfer } from './interfaces/stock-transfer';

export async function shippingSiteIsNotEqualToReceivingSite(cx: ValidationContext, stockTransfer: StockTransfer) {
    await cx.error
        .withMessage(
            '@sage/xtrem-supply-chain/nodes__stock_transfer_shipment__same_sites',
            'The shipping site needs to be different from the receiving site.',
        )
        .if((await stockTransfer.site)._id)
        .is.equal.to((await stockTransfer.receivingSite)._id);
}

export async function financialSiteIsEqualForShippingSiteAndReceivingSite(
    cx: ValidationContext,
    stockTransfer: StockTransfer,
) {
    const shippingSiteFinancialSite = await stockTransfer.financialSite;
    const receivingSiteFinancialSite = await xtremFinanceData.functions.getFinancialSite(
        await stockTransfer.receivingSite,
    );
    await cx.error
        .withMessage(
            '@sage/xtrem-supply-chain/nodes__stock_transfer_shipment__financial_site_not_matching',
            'The financial site for the receiving site needs to be the same as the financial site for the shipping site.',
        )
        .if(receivingSiteFinancialSite)
        .is.not.equal.to(shippingSiteFinancialSite);
}
