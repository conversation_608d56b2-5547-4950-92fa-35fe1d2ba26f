import type { ValidationContext } from '@sage/xtrem-core';
import type * as xtremSupply<PERSON>hain from '../..';

export async function cannotUpdateStockTransferOrderReceivingSite(
    cx: ValidationContext,
    stockTransferOrder: xtremSupplyChain.nodes.StockTransferOrder,
) {
    await cx.error
        .withMessage(
            '@sage/xtrem-supply-chain/nodes__stock_transfer_order__header_receiving_site_not_updatable',
            'You cannot change the receiving site for the stock transfer order.',
        )
        .if(await (await stockTransferOrder.$.old).receivingSite)
        .is.not.equal.to(await stockTransferOrder.receivingSite);
}

export async function cannotUpdateStockTransferOrderStockSite(
    cx: ValidationContext,
    stockTransferOrder: xtremSupplyChain.nodes.StockTransferOrder,
) {
    await cx.error
        .withMessage(
            '@sage/xtrem-supply-chain/nodes__stock_transfer_order__header_stock_site_not_updatable',
            'The stock site of the stock transfer order must not be changed.',
        )
        .if(await (await stockTransferOrder.$.old).stockSite)
        .is.not.equal.to(await stockTransferOrder.stockSite);
}
