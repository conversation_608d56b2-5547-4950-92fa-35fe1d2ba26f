import { NodeExtension, NodeStatus, decorators } from '@sage/xtrem-core';
import * as xtremLandedCost from '@sage/xtrem-landed-cost';
import * as xtremSupplyChain from '../index';

@decorators.nodeExtension<LandedCostAllocationExtension>({
    extends: () => xtremLandedCost.nodes.LandedCostAllocation,

    async controlEnd(cx) {
        if (this.$.status === NodeStatus.added) {
            await xtremSupplyChain.events.control.landedCostAllocationExtension.checkStockTransactionStatus(cx, this);
            await xtremSupplyChain.events.control.landedCostAllocationExtension.checkStatus(cx, this);
            await xtremSupplyChain.events.control.landedCostAllocationExtension.checkSite(cx, this);
        }
    },
})
export class LandedCostAllocationExtension extends NodeExtension<xtremLandedCost.nodes.LandedCostAllocation> {}

declare module '@sage/xtrem-landed-cost/lib/nodes/landed-cost-allocation' {
    interface LandedCostAllocation extends LandedCostAllocationExtension {}
}
