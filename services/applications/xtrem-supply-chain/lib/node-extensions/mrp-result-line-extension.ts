import { BusinessRuleError, Logger, <PERSON>de<PERSON>x<PERSON>ion, NodeStatus, decorators } from '@sage/xtrem-core';
import * as xtremMrpData from '@sage/xtrem-mrp-data';
import * as xtremSupplyChain from '..';

const logger = Logger.getLogger(__filename, 'xtrem-supply-chain');

@decorators.nodeExtension<MrpResultLineExtension>({
    extends: () => xtremMrpData.nodes.MrpResultLine,
    async deleteBegin() {
        // check if the associated supply-planning is being ordered and prevent the deletion if this is the case
        const supplyPlanningRecord = await this.getAssociatedSupplyPlaning();

        if (supplyPlanningRecord) {
            if ((await supplyPlanningRecord.status) === 'createOrder') {
                throw new BusinessRuleError(
                    this.$.context.localize(
                        '@sage/xtrem-supply-chain/nodes__mrp_result_line__cannot_delete_when_purchase_order_is_being_created__error',
                        'You cannot delete this result set, purchase orders are being created.',
                    ),
                );
            }
            await supplyPlanningRecord.$.delete();
        }
    },
    async saveBegin() {
        // delete the associated supply planning record if the suggestionStatus is set to ordered
        if (this.$.status === NodeStatus.modified) {
            if (
                (await (await this.$.old).suggestionStatus) !== 'ordered' &&
                (await this.suggestionStatus) === 'ordered'
            ) {
                const supplyPlanningRecord = await this.getAssociatedSupplyPlaning();

                if (supplyPlanningRecord) {
                    logger.verbose(
                        () =>
                            `deleting SupplyPlanning(_id:${supplyPlanningRecord._id}) after MRPResultLine(_id:${this._id}) suggestion status changed to 'ordered'`,
                    );
                    await supplyPlanningRecord.$.delete();
                }
            }
        }
    },
})
export class MrpResultLineExtension extends NodeExtension<xtremMrpData.nodes.MrpResultLine> {
    getAssociatedSupplyPlaning() {
        return this.$.context
            .query(xtremSupplyChain.nodes.SupplyPlanning, {
                filter: { mrpResultLine: this._id },
                forUpdate: true,
            })
            .at(0);
    }
}

declare module '@sage/xtrem-mrp-data/lib/nodes/mrp-result-line' {
    interface MrpResultLine extends MrpResultLineExtension {}
}
