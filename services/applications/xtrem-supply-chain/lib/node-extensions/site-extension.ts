import * as xtremAuthorization from '@sage/xtrem-authorization';
import type { Reference } from '@sage/xtrem-core';
import { NodeExtension, decorators } from '@sage/xtrem-core';
import * as xtremSystem from '@sage/xtrem-system';
import { StockTransferOrder } from '../nodes';

@decorators.nodeExtension<SiteExtension>({
    extends: () => xtremSystem.nodes.Site,
})
export class SiteExtension extends NodeExtension<xtremSystem.nodes.Site> {
    @decorators.booleanProperty<SiteExtension, 'isStockTransferOrderApprovalManaged'>({
        defaultValue: true,
        isPublished: true,
        isStored: true,
        lookupAccess: true,
        async control(cx, val) {
            // If approval managed, no need to check for stock transfer orders pending approval.
            if (val) {
                return;
            }

            const pendingApprovalCount = await this.$.context.queryCount(StockTransferOrder, {
                filter: { site: this._id, approvalStatus: 'pendingApproval' },
            });
            if (pendingApprovalCount) {
                cx.error.addLocalized(
                    '@sage/xtrem-supply-chain/nodes__site_extension__check_pending_stock_transfer_orders',
                    'Pending stock transfer orders need to be handled before disabling the approval process',
                );
            }
        },
    })
    readonly isStockTransferOrderApprovalManaged: Promise<boolean>;

    @decorators.referenceProperty<SiteExtension, 'stockTransferOrderDefaultApprover'>({
        node: () => xtremSystem.nodes.User,
        isNullable: true,
        isPublished: true,
        isStored: true,
        lookupAccess: true,
        filters: {
            lookup: xtremAuthorization.filters.user.interactiveUsers,
        },
    })
    readonly stockTransferOrderDefaultApprover: Reference<xtremSystem.nodes.User | null>;

    @decorators.referenceProperty<SiteExtension, 'stockTransferOrderSubstituteApprover'>({
        node: () => xtremSystem.nodes.User,
        isNullable: true,
        isPublished: true,
        isStored: true,
        lookupAccess: true,
        filters: {
            lookup: xtremAuthorization.filters.user.interactiveUsers,
        },
    })
    readonly stockTransferOrderSubstituteApprover: Reference<xtremSystem.nodes.User | null>;
}
declare module '@sage/xtrem-system/lib/nodes/site' {
    export interface Site extends SiteExtension {}
}
