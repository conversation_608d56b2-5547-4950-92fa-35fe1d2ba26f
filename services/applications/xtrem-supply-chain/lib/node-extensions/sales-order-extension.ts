import type { Context } from '@sage/xtrem-core';
import { date, decorators, SubNodeExtension1 } from '@sage/xtrem-core';
import * as xtremFinanceData from '@sage/xtrem-finance-data';
import * as xtremManufacturing from '@sage/xtrem-manufacturing';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremPurchasing from '@sage/xtrem-purchasing';
import * as xtremSales from '@sage/xtrem-sales';
import * as xtremSystem from '@sage/xtrem-system';
import {
    controlOnHoldCustomer,
    controlSalesOrderStatus,
    createPurchaseOrderAssignment,
    createWorkOrderAssignment,
    getGetDefaultSupplier,
    getGrossPrice,
    getPurchaseQuantity,
    getWorkOrderCategory,
    getWorkOrderStartDate,
} from '../functions/sales-order-extension-lib';
import * as xtremSupplyChain from '../index';
import type { BackToBackPurchaseOrderCreationParameters, BackToBackWorkOrderCreationParameters } from '../interfaces';

@decorators.subNodeExtension1<SalesOrderExtension>({
    extends: () => xtremSales.nodes.SalesOrder,
})
export class SalesOrderExtension extends SubNodeExtension1<xtremSales.nodes.SalesOrder> {
    static async controlBackToBackOrder(salesOrder: xtremSales.nodes.SalesOrder): Promise<void> {
        await controlSalesOrderStatus(salesOrder);
        await controlOnHoldCustomer(salesOrder);
    }

    static async createWorkOrder(
        orderLine: xtremSales.nodes.SalesOrderLine,
    ): Promise<xtremManufacturing.nodes.WorkOrder> {
        const { context } = orderLine.$;
        const site = await orderLine.stockSite;
        const item = await orderLine.item;
        const { attributes, dimensions } =
            await xtremFinanceData.nodes.DimensionDefinitionLevelAndDefault.getDefaultAttributesAndDimensionsOrderToOrder(
                context,
                {
                    dimensionDefinitionLevel: 'manufacturingOrderToOrder',
                    companyId: (await site.legalCompany)._id,
                    siteId: site._id,
                    itemId: item._id,
                    storedAttributes: JSON.stringify(orderLine.storedAttributes),
                    storedDimensions: JSON.stringify(orderLine.storedDimensions ?? {}),
                    onlyFromItem: false,
                },
            );
        const workOrderCategory = await getWorkOrderCategory(context, item, site);
        return xtremSupplyChain.nodeExtensions.SalesOrderExtension.createBackToBackWorkOrder(context, {
            startDate: getWorkOrderStartDate(
                (await (await orderLine.itemSite)?.prodLeadTime) ?? 0,
                await orderLine.shippingDate,
            ),
            releasedQuantity: await orderLine.remainingQuantityToShipInStockUnit,
            site,
            item,
            type: 'planned',
            billOfMaterial: (await workOrderCategory.billOfMaterial) ? item : undefined,
            routing: (await workOrderCategory.routing) ? item : undefined,
            storedAttributes: JSON.parse(attributes),
            storedDimensions: JSON.parse(dimensions),
            workOrderCategory,
            name: await item.name,
            remainingQuantityToShipInStockUnit: await orderLine.remainingQuantityToShipInStockUnit,
            salesOrderLineSysId: orderLine._id,
            salesOrderLineWorkInProgress: await orderLine.workInProgress,
        });
    }

    static async createPurchaseOrder(
        orderLine: xtremSales.nodes.SalesOrderLine,
    ): Promise<xtremPurchasing.nodes.PurchaseOrder> {
        const { context } = orderLine.$;
        const item = await orderLine.item;
        const site = await orderLine.site;
        const defaultSupplier = await getGetDefaultSupplier(orderLine);
        const currency = (await (await defaultSupplier.businessEntity).currency) ?? (await defaultSupplier.currency);
        const orderDate = date.today();

        const itemSupplier = await context.tryRead(xtremMasterData.nodes.ItemSupplier, {
            supplier: defaultSupplier,
            item,
        });
        const unit = itemSupplier
            ? await itemSupplier.purchaseUnitOfMeasure
            : ((await (await orderLine.item).purchaseUnit) ?? (await orderLine.unit));

        const { attributes, dimensions } =
            await xtremFinanceData.nodes.DimensionDefinitionLevelAndDefault.getDefaultAttributesAndDimensionsOrderToOrder(
                context,
                {
                    dimensionDefinitionLevel: 'purchasingOrderToOrder',
                    companyId: (await site.legalCompany)._id,
                    siteId: site._id,
                    itemId: item._id,
                    supplierId: defaultSupplier._id,
                    storedAttributes: JSON.stringify(orderLine.storedAttributes),
                    storedDimensions: JSON.stringify(orderLine.storedDimensions ?? {}),
                    onlyFromItem: false,
                },
            );
        const quantity = await getPurchaseQuantity(
            context,
            { item, quantity: await orderLine.remainingQuantityToShipInStockUnit, supplier: defaultSupplier, unit },
            itemSupplier,
        );

        return xtremSupplyChain.nodeExtensions.SalesOrderExtension.createBackToBackPurchaseOrder(context, {
            currency,
            expectedReceiptDate: await orderLine.shippingDate,
            grossPrice: await getGrossPrice(context, {
                item,
                site,
                supplier: defaultSupplier,
                currency,
                quantity,
                unit,
                date: orderDate,
                convertUnit: false,
            }),
            item,
            orderDate,
            priceOrigin: 'manual',
            quantity,
            remainingQuantityToShipInStockUnit: await orderLine.remainingQuantityToShipInStockUnit,
            salesOrderLineSysId: orderLine._id,
            salesOrderLineWorkInProgress: await orderLine.workInProgress,
            site,
            stockSite: await orderLine.stockSite,
            storedAttributes: JSON.parse(attributes),
            storedDimensions: JSON.parse(dimensions),
            supplier: defaultSupplier,
            unit,
        });
    }

    static createOrGetWorkOrdersFromSalesOrder(
        salesOrder: xtremSales.nodes.SalesOrder,
    ): Promise<xtremManufacturing.nodes.WorkOrder[]> {
        return salesOrder.lines
            .filter(async line => {
                return (
                    (await line.remainingQuantityToShipInStockUnit) > 0 &&
                    (await xtremSales.functions.checkPreferredProcess(line, 'production')) &&
                    (await line.stockSite).isManufacturing
                );
            })
            .map(async salesOrderLine => {
                const workOrderLine = await salesOrderLine.uWorkOrderLine;
                return workOrderLine
                    ? (
                          await salesOrder.$.context.read(xtremManufacturing.nodes.WorkOrderComponent, {
                              _id: workOrderLine._id,
                          })
                      ).document
                    : this.createWorkOrder(salesOrderLine);
            })
            .toArray();
    }

    static createOrGetPurchaseOrdersFromSalesOrder(
        salesOrder: xtremSales.nodes.SalesOrder,
    ): Promise<xtremPurchasing.nodes.PurchaseOrder[]> {
        return salesOrder.lines
            .filter(async line => {
                return (
                    (await line.remainingQuantityToShipInStockUnit) > 0 &&
                    (await xtremSales.functions.checkPreferredProcess(line, 'purchasing')) &&
                    (await line.item).isBought
                );
            })
            .map(async salesOrderLine => {
                const purchaseOrderLine = await salesOrderLine.uPurchaseOrderLine;
                return purchaseOrderLine
                    ? (
                          await salesOrder.$.context.read(xtremPurchasing.nodes.PurchaseOrderLine, {
                              _id: purchaseOrderLine._id,
                          })
                      ).document
                    : this.createPurchaseOrder(salesOrderLine);
            })
            .toArray();
    }

    // To be executed from workflow
    @decorators.mutation<typeof SalesOrderExtension, 'createBackToBackOrderFromSalesOrder'>({
        isPublished: true,
        parameters: [
            {
                name: 'salesOrder',
                type: 'reference',
                isMandatory: true,
                node: () => xtremSales.nodes.SalesOrder,
            },
        ],
        return: {
            type: 'object',
            properties: {
                purchaseOrders: {
                    type: 'array',
                    item: {
                        type: 'reference',
                        node: () => xtremPurchasing.nodes.PurchaseOrder,
                    },
                },
                workOrders: {
                    type: 'array',
                    item: {
                        type: 'reference',
                        node: () => xtremManufacturing.nodes.WorkOrder,
                    },
                },
            },
        },
        serviceOptions: () => [xtremMasterData.serviceOptions.orderToOrderOption],
    })
    static async createBackToBackOrderFromSalesOrder(
        context: Context,
        salesOrder: xtremSales.nodes.SalesOrder,
    ): Promise<{
        purchaseOrders: xtremPurchasing.nodes.PurchaseOrder[];
        workOrders: xtremManufacturing.nodes.WorkOrder[];
    }> {
        // Initial control
        await this.controlBackToBackOrder(salesOrder);

        return {
            purchaseOrders: await this.createOrGetPurchaseOrdersFromSalesOrder(salesOrder),
            workOrders: await this.createOrGetWorkOrdersFromSalesOrder(salesOrder),
        };
    }

    // To be executed from the UI where the user have the chance to introduce the values on a side panel
    @decorators.mutation<typeof SalesOrderExtension, 'createBackToBackPurchaseOrder'>({
        isPublished: true,
        serviceOptions: () => [xtremMasterData.serviceOptions.orderToOrderOption],
        parameters: [
            {
                name: 'data',
                type: 'object',
                properties: {
                    orderDate: { type: 'date' },
                    expectedReceiptDate: { type: 'date' },
                    quantity: { type: 'decimal' },
                    grossPrice: { type: 'decimal' },
                    unit: { type: 'reference', node: () => xtremMasterData.nodes.UnitOfMeasure },
                    site: { type: 'reference', node: () => xtremSystem.nodes.Site },
                    stockSite: { type: 'reference', node: () => xtremSystem.nodes.Site },
                    item: { type: 'reference', node: () => xtremMasterData.nodes.Item },
                    supplier: { type: 'reference', node: () => xtremMasterData.nodes.Supplier },
                    priceOrigin: { type: 'enum', dataType: () => xtremPurchasing.enums.priceOriginDataType },
                    storedDimensions: { type: 'string' },
                    storedAttributes: { type: 'string' },
                    currency: { type: 'reference', node: () => xtremMasterData.nodes.Currency },
                    remainingQuantityToShipInStockUnit: { type: 'decimal' },
                    salesOrderLineSysId: { type: 'integer' },
                    salesOrderLineWorkInProgress: {
                        type: 'reference',
                        node: () => xtremSales.nodes.WorkInProgressSalesOrderLine,
                        isNullable: true,
                    },
                },
            },
        ],
        return: { type: 'reference', node: () => xtremPurchasing.nodes.PurchaseOrder, isNullable: true },
    })
    static async createBackToBackPurchaseOrder(
        context: Context,
        data: BackToBackPurchaseOrderCreationParameters,
    ): Promise<xtremPurchasing.nodes.PurchaseOrder> {
        const { orderDate, expectedReceiptDate } = data;

        const purchaseOrder = await xtremPurchasing.nodes.PurchaseOrder.createPurchaseOrderReplenishment(context, {
            ...data,
            expectedReceiptDate: expectedReceiptDate.compare(orderDate) < 0 ? orderDate : expectedReceiptDate,
        });
        await createPurchaseOrderAssignment(context, data, purchaseOrder);

        return purchaseOrder;
    }

    // To be executed from the UI where the user have the chance to introduce the values on a side panel
    @decorators.mutation<typeof SalesOrderExtension, 'createBackToBackWorkOrder'>({
        isPublished: true,
        serviceOptions: () => [xtremMasterData.serviceOptions.orderToOrderOption],
        parameters: [
            {
                name: 'data',
                type: 'object',
                properties: {
                    site: { type: 'reference', node: () => xtremSystem.nodes.Site, isMandatory: true },
                    item: { type: 'reference', node: () => xtremMasterData.nodes.Item, isMandatory: true },
                    releasedQuantity: { type: 'decimal', isMandatory: true },
                    name: { type: 'string', isMandatory: true },
                    type: {
                        type: 'enum',
                        dataType: () => xtremManufacturing.enums.workOrderTypeDataType,
                        isMandatory: true,
                    },
                    workOrderCategory: {
                        type: 'reference',
                        isMandatory: true,
                        node: () => xtremManufacturing.nodes.WorkOrderCategory,
                    },
                    startDate: { type: 'date' },
                    billOfMaterial: { type: 'reference', node: () => xtremMasterData.nodes.Item },
                    routing: { type: 'reference', node: () => xtremMasterData.nodes.Item },
                    storedDimensions: { isNullable: true, type: 'string' },
                    storedAttributes: { isNullable: true, type: 'string' },
                    remainingQuantityToShipInStockUnit: { type: 'decimal' },
                    salesOrderLineSysId: { type: 'integer' },
                    salesOrderLineWorkInProgress: {
                        type: 'reference',
                        node: () => xtremSales.nodes.WorkInProgressSalesOrderLine,
                        isNullable: true,
                    },
                    workOrderNumber: { type: 'string' },
                },
                isMandatory: true,
            },
        ],
        return: { type: 'reference', node: () => xtremManufacturing.nodes.WorkOrder },
    })
    static async createBackToBackWorkOrder(
        context: Context,
        data: BackToBackWorkOrderCreationParameters,
    ): Promise<xtremManufacturing.nodes.WorkOrder> {
        const workOrder = await xtremManufacturing.nodes.WorkOrder.createWorkOrder(context, {
            ...data,
            siteId: await data.site.id,
            releasedItem: await data.item.id,
            workOrderNumber: data.workOrderNumber ?? '',
            bom: await data.billOfMaterial?.id,
            route: await data.routing?.id,
        });
        await createWorkOrderAssignment(context, data, workOrder);
        return workOrder;
    }
}

declare module '@sage/xtrem-sales/lib/nodes/sales-order' {
    interface SalesOrder extends SalesOrderExtension {}
}
