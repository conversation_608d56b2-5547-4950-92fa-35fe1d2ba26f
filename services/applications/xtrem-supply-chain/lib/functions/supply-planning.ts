import type { Context, NodeCreateData, NodePayloadData } from '@sage/xtrem-core';
import { asyncArray } from '@sage/xtrem-core';
import { date } from '@sage/xtrem-date-time';
import * as xtremManufacturing from '@sage/xtrem-manufacturing';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremMrpData from '@sage/xtrem-mrp-data';
import * as xtremPurchasing from '@sage/xtrem-purchasing';
import * as xtremSystem from '@sage/xtrem-system';
import * as xtremTechnicalData from '@sage/xtrem-technical-data';
import * as _ from 'lodash';
import * as xtremSupplyPlanning from '..';

export async function validateCreatePurchaseOrder(
    context: Context,
    validationData: {
        supplyPlanning: NodePayloadData<xtremSupplyPlanning.nodes.SupplyPlanning>;
    },
): Promise<string> {
    const messages: string[] = [];
    const { supplyPlanning } = validationData;
    const today = date.today();

    if (supplyPlanning?.startDate && supplyPlanning.startDate > today) {
        messages.push(
            context.localize(
                '@sage/xtrem-supply-chain/nodes__supply_planning__wrong_purchase_order_start_date',
                'The start date needs to be on or before the current date.',
            ),
        );
    }
    if (supplyPlanning?.requirementDate && supplyPlanning.requirementDate < today) {
        messages.push(
            context.localize(
                '@sage/xtrem-supply-chain/nodes__supply_planning__wrong_purchase_order_requirement_date',
                'The requirement date needs to be on or after the current date.',
            ),
        );
    }
    if (!supplyPlanning.purchaseSite) {
        messages.push(
            context.localize(
                '@sage/xtrem-supply-chain/nodes__supply_planning__purchase_site',
                'Purchase site is required.',
            ),
        );
    }
    if (!supplyPlanning.supplier) {
        messages.push(
            context.localize('@sage/xtrem-supply-chain/nodes__supply_planning__supplier', 'Supplier is required.'),
        );
    }
    if (!supplyPlanning?.purchaseQuantity || supplyPlanning.purchaseQuantity <= 0) {
        messages.push(
            context.localize(
                '@sage/xtrem-supply-chain/nodes__supply_planning__purchase_quantity',
                'Purchase quantity is required.',
            ),
        );
    }
    if (!supplyPlanning.purchaseUnit) {
        messages.push(
            context.localize(
                '@sage/xtrem-supply-chain/nodes__supply_planning__purchase_unit',
                'Purchase unit is required.',
            ),
        );
    }
    if (!supplyPlanning.startDate) {
        messages.push(
            context.localize('@sage/xtrem-supply-chain/nodes__supply_planning__order_date', 'Start date is required.'),
        );
    }
    if (!supplyPlanning.requirementDate) {
        messages.push(
            context.localize(
                '@sage/xtrem-supply-chain/nodes__supply_planning__expected_receipt_date',
                'Requirement date is required.',
            ),
        );
    }
    if (supplyPlanning.status !== 'pending') {
        messages.push(
            context.localize(
                '@sage/xtrem-supply-chain/nodes__supply_planning__status',
                'The purchase order is already created.',
            ),
        );
    }

    if (messages.length) {
        const message = messages.join('\n');

        await asyncArray(messages).forEach(async errorMessage => {
            await context.batch.logMessage('error', errorMessage);
        });

        await context.notifyUser({
            title: context.localize(
                '@sage/xtrem-supply-chain/nodes__supply_planning__error_notification_title',
                'Purchase order planning error',
            ),
            description: message,
            icon: 'cross',
            level: 'error',
            shouldDisplayToast: true,
            actions: [],
        });

        return message;
    }

    return '';
}

export async function getPurchaseOrderLinesFromGrouping(
    context: Context,
    supplyPlanningsPerGroup: NodePayloadData<xtremSupplyPlanning.nodes.SupplyPlanning>[],
): Promise<NodeCreateData<xtremPurchasing.nodes.PurchaseOrderLine>[]> {
    const groupedSupplyPlanningLines: { [group: string]: NodePayloadData<xtremSupplyPlanning.nodes.SupplyPlanning>[] } =
        _.groupBy(
            supplyPlanningsPerGroup,
            line => `"${line.itemSite?._id}+${line.purchaseUnit?._id}+${line.requirementDate}+${line.grossPrice}"`,
        );

    const lines: NodeCreateData<xtremPurchasing.nodes.PurchaseOrderLine>[] = [];

    await asyncArray(Object.keys(groupedSupplyPlanningLines)).forEach(async groupSupplyPlanningLineKey => {
        const supplyPlanningLinePerGroup: NodePayloadData<xtremSupplyPlanning.nodes.SupplyPlanning>[] =
            groupedSupplyPlanningLines[groupSupplyPlanningLineKey];

        const supplyPlanningHeader = supplyPlanningLinePerGroup[0];

        const quantity = await asyncArray(supplyPlanningLinePerGroup).sum(
            supplyPlanning => supplyPlanning.purchaseQuantity || 0,
        );

        const itemSite = await context.read(xtremMasterData.nodes.ItemSite, {
            _id: supplyPlanningHeader.itemSite?._id,
        });

        lines.push({
            item: (await itemSite.item) ?? null,
            stockSite: (await itemSite.site) ?? null,
            quantity,
            purchaseUnit: supplyPlanningHeader.purchaseUnit,
            grossPrice: supplyPlanningHeader.grossPrice ?? 0,
            isPurchaseOrderSuggestion: true,
            expectedReceiptDate: supplyPlanningHeader.requirementDate,
        });
    });
    return lines;
}

async function groupedSupplyPlanningsStatusUpdate(
    context: Context,
    supplyPlannings: NodePayloadData<xtremSupplyPlanning.nodes.SupplyPlanning>[],
) {
    await context.bulkUpdate(xtremSupplyPlanning.nodes.SupplyPlanning, {
        set: { status: 'createOrder' },
        where: { _id: { _in: supplyPlannings.map(sp => sp._id ?? 0) } },
    });
}

async function notifyEndProcess(context: Context, createdOrders: xtremSupplyPlanning.interfaces.CreatedOrders) {
    const idsToFilter = await asyncArray(createdOrders.orders)
        .map(async order => {
            await context.batch.logMessage(
                'result',
                createdOrders.type === 'purchased'
                    ? context.localize(
                          '@sage/xtrem-supply-chain/nodes__supply_planning__purchase_order_creation_succeeded',
                          'Purchase order {{number}} created.',
                          { number: order.number },
                      )
                    : context.localize(
                          '@sage/xtrem-supply-chain/nodes__supply_planning__work_order_creation_succeeded',
                          'Work order {{number}} created.',
                          { number: order.number },
                      ),
            );
            return order._id.toString();
        })
        .toArray();

    const filter = { _id: { _in: idsToFilter } };

    await context.notifyUser({
        title:
            createdOrders.type === 'purchased'
                ? context.localize(
                      '@sage/xtrem-supply-chain/nodes__supply_planning__success_notification_title',
                      'Purchase order planning complete',
                  )
                : context.localize(
                      '@sage/xtrem-supply-chain/nodes__supply_planning__WO_success_notification_title',
                      'Work order planning complete',
                  ),
        description:
            createdOrders.type === 'purchased'
                ? context.localize(
                      '@sage/xtrem-supply-chain/nodes__supply_planning__success_notification_description',
                      'Purchase orders created',
                  )
                : context.localize(
                      '@sage/xtrem-supply-chain/nodes__supply_planning__WO_success_notification_description',
                      'Work orders created',
                  ),
        icon: 'tick',
        level: 'success',
        shouldDisplayToast: true,
        actions: [
            {
                link:
                    createdOrders.type === 'purchased'
                        ? xtremSystem.functions.linkToFilteredMainList<xtremPurchasing.nodes.PurchaseOrder>(
                              '@sage/xtrem-purchasing/PurchaseOrder',
                              filter,
                          )
                        : xtremSystem.functions.linkToFilteredMainList<xtremManufacturing.nodes.WorkOrder>(
                              '@sage/xtrem-manufacturing/WorkOrder',
                              filter,
                          ),
                title: context.localize('@sage/xtrem-supply-chain/nodes__supply_planning__view_link', 'View'),
                icon: 'link',
                style: 'tertiary',
            },
        ],
    });
}

async function groupedSupplyPlanningsMrpCalculationUpdate(
    context: Context,
    supplyPlannings: NodePayloadData<xtremSupplyPlanning.nodes.SupplyPlanning>[],
) {
    await asyncArray(supplyPlannings).forEach(async supplyPlanning => {
        const mrpResultLine = await context.read(xtremMrpData.nodes.MrpResultLine, {
            _id: supplyPlanning.mrpResultLine?._id ?? 0,
        });
        const mrpCalculation = await context.read(
            xtremMrpData.nodes.MrpCalculation,
            { _id: (await mrpResultLine.mrpCalculation)._id },
            { forUpdate: true },
        );
        await xtremMrpData.nodes.MrpCalculation.updateResultLineSuggestionStatus(
            context,
            mrpCalculation,
            mrpResultLine._id,
            'ordered',
        );
    });
}

export async function createPurchaseOrder(
    context: Context,
    groupedSupplyPlannings: { [_id: string]: NodePayloadData<xtremSupplyPlanning.nodes.SupplyPlanning>[] },
) {
    const createdOrders: xtremSupplyPlanning.interfaces.CreatedOrder[] = [];

    // doing this in 2 writable contexts in order to have the document numbers in the end, since they are deferred
    // first context deals with all database updates
    await context.runInWritableContext(async writableContext => {
        await asyncArray(Object.values(groupedSupplyPlannings)).forEach(async supplyPlanningsPerGroup => {
            const supplyPlanningHeader = supplyPlanningsPerGroup[0];

            const messages = await xtremSupplyPlanning.functions.validateCreatePurchaseOrder(writableContext, {
                supplyPlanning: supplyPlanningHeader,
            });

            if (!messages.length) {
                const newPurchaseOrder = await writableContext.create(xtremPurchasing.nodes.PurchaseOrder, {
                    site: supplyPlanningHeader.purchaseSite,
                    businessRelation: supplyPlanningHeader.supplier,
                    orderDate: supplyPlanningHeader.startDate,
                    status: 'draft',
                    isPurchaseOrderSuggestion: true,
                    lines: await getPurchaseOrderLinesFromGrouping(writableContext, supplyPlanningsPerGroup),
                });

                if (await newPurchaseOrder.$.trySave()) {
                    // We can't use the PO number here because the property is deferred
                    // The flushDeferredActions should not be used to prevent deadlocks between users
                    // So for now, we use the _id as a number...
                    createdOrders.push({ _id: newPurchaseOrder._id, number: newPurchaseOrder._id.toString() });
                    await groupedSupplyPlanningsStatusUpdate(writableContext, supplyPlanningsPerGroup);
                    await groupedSupplyPlanningsMrpCalculationUpdate(writableContext, supplyPlanningsPerGroup);
                } else {
                    const errors = newPurchaseOrder.$.context.diagnoses.map(diagnosis => diagnosis.message).join(' ');
                    await writableContext.batch.logMessage(
                        'error',
                        newPurchaseOrder.$.context.localize(
                            '@sage/xtrem-supply-chain/nodes__supply_planning__purchase_order_creation_error',
                            'Failed to create purchase order: {{errors}}',
                            { errors },
                        ),
                    );

                    await writableContext.notifyUser({
                        title: writableContext.localize(
                            '@sage/xtrem-supply-chain/nodes__supply_planning__purchase_order_error_notification_create',
                            'Purchase order planning error',
                        ),
                        description: JSON.stringify(errors),
                        icon: 'cross',
                        level: 'error',
                        shouldDisplayToast: true,
                        actions: [],
                    });
                }
            }
        });
    });

    // now, if there are created orders, we will read them in order to be able to log the messages with the correct PO number
    if (createdOrders.length) {
        await context.runInWritableContext(async writableContext => {
            await asyncArray(createdOrders).forEach(async order => {
                const purchaseOrder = await writableContext.read(xtremPurchasing.nodes.PurchaseOrder, {
                    _id: order._id,
                });
                // this check is for the unit test cases, where the value is always deferred
                if (!purchaseOrder.$.isValueDeferred('number')) {
                    order.number = await purchaseOrder.number;
                }
            });
            await notifyEndProcess(writableContext, { type: 'purchased', orders: createdOrders });
        });
    }
}

export async function bulkCreatePurchaseOrderHandler(
    context: Context,
    supplyPlannings: NodePayloadData<xtremSupplyPlanning.nodes.SupplyPlanning>[],
) {
    const groupedSupplyPlannings: { [group: string]: NodePayloadData<xtremSupplyPlanning.nodes.SupplyPlanning>[] } =
        _.groupBy(supplyPlannings, line => `"${line.purchaseSite?._id}+${line.supplier?._id}+${line.startDate}"`);

    await createPurchaseOrder(context, groupedSupplyPlannings);
}

export async function validateCreateWorkOrder(
    context: Context,
    validationData: {
        supplyPlanning: NodePayloadData<xtremSupplyPlanning.nodes.SupplyPlanning>;
    },
): Promise<string> {
    const messages: string[] = [];
    const { supplyPlanning } = validationData;
    const itemSite = await context.read(xtremMasterData.nodes.ItemSite, { _id: supplyPlanning.itemSite?._id });
    const siteId = (await itemSite.site)._id;
    const itemId = (await itemSite.item)._id;

    if (supplyPlanning?.startDate && supplyPlanning.startDate < date.today()) {
        messages.push(
            context.localize(
                '@sage/xtrem-supply-chain/nodes__supply_planning__wrong_work_order_start_date',
                'The start date needs to be on or after the current date.',
            ),
        );
    }
    if (!supplyPlanning?.workOrderCategory) {
        messages.push(
            context.localize(
                '@sage/xtrem-supply-chain/nodes__supply_planning__missing_work_order_category',
                'You need to add a category.',
            ),
        );
    }
    if (!supplyPlanning?.workOrderType) {
        messages.push(
            context.localize(
                '@sage/xtrem-supply-chain/nodes__supply_planning__missing_work_order_type',
                'You need to add a type.',
            ),
        );
    }
    if (!supplyPlanning?.stockQuantity || supplyPlanning.stockQuantity <= 0) {
        messages.push(
            context.localize(
                '@sage/xtrem-supply-chain/nodes__supply_planning__stock_quantity',
                'You need to add a quantity.',
            ),
        );
    }
    if (supplyPlanning.workOrderNumber) {
        const existingWorkOrder = await context.tryRead(xtremManufacturing.nodes.WorkOrder, {
            site: siteId,
            number: supplyPlanning.workOrderNumber,
        });
        if (existingWorkOrder) {
            messages.push(
                context.localize(
                    '@sage/xtrem-supply-chain/nodes__supply_planning__already_used_work_order_number',
                    'This work order number has been used. Enter a different number.',
                ),
            );
        }
    }
    if (supplyPlanning.workOrderCategory) {
        const workOrderCategory = await context.read(xtremManufacturing.nodes.WorkOrderCategory, {
            _id: supplyPlanning.workOrderCategory._id,
        });
        let existingRouting = false;
        let existingBillOfMaterial = false;
        if (await workOrderCategory.routing) {
            existingRouting =
                (await (
                    await context.tryRead(xtremTechnicalData.nodes.Routing, {
                        item: itemId,
                        site: siteId,
                    })
                )?.status) === 'availableToUse';
        }
        if (!existingRouting && (await workOrderCategory.billOfMaterial)) {
            existingBillOfMaterial =
                (await (
                    await context.tryRead(xtremTechnicalData.nodes.BillOfMaterial, {
                        item: itemId,
                        site: siteId,
                    })
                )?.status) === 'availableToUse';
        }
        if (!existingRouting && !existingBillOfMaterial) {
            messages.push(
                context.localize(
                    '@sage/xtrem-supply-chain/nodes__supply_planning__wrong_work_order_category',
                    'You need to select a different category. There is no routing or bill of material for this item-site.',
                ),
            );
        }
    }
    if (supplyPlanning.status !== 'pending') {
        messages.push(
            context.localize(
                '@sage/xtrem-supply-chain/nodes__supply_planning__work_order_status',
                'The work order is already created.',
            ),
        );
    }

    if (messages.length) {
        const message = messages.join('\n');

        await asyncArray(messages).forEach(async errorMessage => {
            await context.batch.logMessage('error', errorMessage);
        });

        await context.notifyUser({
            title: context.localize(
                '@sage/xtrem-supply-chain/nodes__supply_planning__work_order_error_notification_title',
                'Work order planning error',
            ),
            description: message,
            icon: 'cross',
            level: 'error',
            shouldDisplayToast: true,
            actions: [],
        });

        return message;
    }

    return '';
}

export async function getSupplyPlanningToCreate(
    supplyPlanningsPerGroup: NodePayloadData<xtremSupplyPlanning.nodes.SupplyPlanning>[],
): Promise<NodePayloadData<xtremSupplyPlanning.nodes.SupplyPlanning>> {
    return {
        ...supplyPlanningsPerGroup[0],
        stockQuantity: await asyncArray(supplyPlanningsPerGroup).sum(
            supplyPlanning => supplyPlanning.stockQuantity || 0,
        ),
    };
}

async function callCreateWorkOrderMutation(
    context: Context,
    supplyPlanningHeader: NodePayloadData<xtremSupplyPlanning.nodes.SupplyPlanning>,
) {
    const itemSite = await context.read(xtremMasterData.nodes.ItemSite, {
        _id: supplyPlanningHeader.itemSite?._id,
    });
    const workOrderCategory = await context.read(xtremManufacturing.nodes.WorkOrderCategory, {
        _id: supplyPlanningHeader.workOrderCategory?._id,
    });

    return xtremManufacturing.nodes.WorkOrder.createWorkOrder(context, {
        siteId: (await (await itemSite?.site)?.id) ?? '',
        releasedItem: (await (await itemSite?.item)?.id) ?? '',
        releasedQuantity: supplyPlanningHeader.stockQuantity ?? 0,
        name: supplyPlanningHeader.workOrderName ?? (await (await itemSite?.item)?.name) ?? '',
        type: supplyPlanningHeader.workOrderType ?? 'planned',
        workOrderCategory,
        startDate: supplyPlanningHeader.startDate,
        workOrderNumber: supplyPlanningHeader.workOrderNumber ?? '',
        bom: (await workOrderCategory.billOfMaterial) ? await (await itemSite?.item)?.id : '',
        route: (await workOrderCategory.routing) ? await (await itemSite?.item)?.id : '',
    });
}

export async function createWorkOrder(
    context: Context,
    groupedSupplyPlannings: { [_id: string]: NodePayloadData<xtremSupplyPlanning.nodes.SupplyPlanning>[] },
) {
    const createdOrders: xtremSupplyPlanning.interfaces.CreatedOrder[] = [];

    await asyncArray(Object.values(groupedSupplyPlannings)).forEach(async supplyPlanningsPerGroup => {
        const supplyPlanningToCreate =
            await xtremSupplyPlanning.functions.getSupplyPlanningToCreate(supplyPlanningsPerGroup);

        const messages = await xtremSupplyPlanning.functions.validateCreateWorkOrder(context, {
            supplyPlanning: supplyPlanningToCreate,
        });

        if (!messages.length) {
            const newWorkOrder = await callCreateWorkOrderMutation(context, supplyPlanningToCreate);
            if (newWorkOrder) {
                createdOrders.push({ _id: newWorkOrder._id, number: await newWorkOrder.number });

                await groupedSupplyPlanningsStatusUpdate(context, supplyPlanningsPerGroup);
                await groupedSupplyPlanningsMrpCalculationUpdate(context, supplyPlanningsPerGroup);
            } else {
                const errors = context.diagnoses.map(diagnosis => diagnosis.message).join(' ');
                await context.batch.logMessage(
                    'error',
                    context.localize(
                        '@sage/xtrem-supply-chain/nodes__supply_planning__work_order_creation_error',
                        'Failed to create work order: {{errors}}',
                        { errors },
                    ),
                );
                await context.notifyUser({
                    title: context.localize(
                        '@sage/xtrem-supply-chain/nodes__supply_planning__work_order_error_notification_create',
                        'Work order planning error',
                    ),
                    description: JSON.stringify(errors),
                    icon: 'cross',
                    level: 'error',
                    shouldDisplayToast: true,
                    actions: [],
                });
            }
        }
    });

    if (createdOrders.length) {
        await notifyEndProcess(context, { type: 'manufactured', orders: createdOrders });
    }
}

export async function bulkCreateWorkOrderHandler(
    context: Context,
    supplyPlannings: NodePayloadData<xtremSupplyPlanning.nodes.SupplyPlanning>[],
) {
    const groupedSupplyPlannings: { [group: string]: NodePayloadData<xtremSupplyPlanning.nodes.SupplyPlanning>[] } =
        _.groupBy(
            supplyPlannings,
            line =>
                `"${line.itemSite?._id}+${line.startDate}+${line.workOrderCategory?._id}+${line.workOrderType}+${line.workOrderName}+${line.workOrderNumber}"`,
        );

    await createWorkOrder(context, groupedSupplyPlannings);
}
