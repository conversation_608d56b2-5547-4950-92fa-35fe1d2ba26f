import type { Context } from '@sage/xtrem-core';
import { BusinessRuleError, date } from '@sage/xtrem-core';
import * as xtremManufacturing from '@sage/xtrem-manufacturing';
import * as xtremMasterData from '@sage/xtrem-master-data';
import type * as xtremPurchasing from '@sage/xtrem-purchasing';
import * as xtremSales from '@sage/xtrem-sales';
import * as xtremStockData from '@sage/xtrem-stock-data';
import type * as xtremSystem from '@sage/xtrem-system';
import * as xtremTechnicalData from '@sage/xtrem-technical-data';
import type {
    AssignmentCreationParameters,
    BackToBackPurchaseOrderCreationParameters,
    BackToBackWorkOrderCreationParameters,
} from '../interfaces';

export async function controlSalesOrderStatus(order: xtremSales.nodes.SalesOrder) {
    if (['quote', 'closed'].includes(await order.status)) {
        throw new BusinessRuleError(
            order.$.context.localize(
                '@sage/xtrem-supply-chain/nodes_extension__sales_order_line__status_is_not_ok',
                'Sales order line status should not be quote or closed.',
            ),
        );
    }
    return true;
}

export async function controlOnHoldCustomer(order: xtremSales.nodes.SalesOrder) {
    if (
        xtremSales.functions.isBlockedOnHoldCustomer(
            await order.isOnHold,
            await (
                await (
                    await order.site
                ).legalCompany
            ).customerOnHoldCheck,
        )
    ) {
        throw new BusinessRuleError(
            order.$.context.localize(
                '@sage/xtrem-supply-chain/nodes_extension__sales_order_line__cant_post_shipment_when_bill_to_customer_is_on_hold',
                'The Bill-to customer is on hold. The shipment cannot be posted.',
            ),
        );
    }
}

export async function getGetDefaultSupplier(
    orderLine: xtremSales.nodes.SalesOrderLine,
): Promise<xtremMasterData.nodes.Supplier> {
    const { context } = orderLine.$;
    const item = await orderLine.item;
    const site = await orderLine.site;
    const supplier = await xtremMasterData.nodes.Supplier.getDefaultSupplier(context, item, site);
    if (!supplier) {
        throw new BusinessRuleError(
            context.localize(
                '@sage/xtrem-supply-chain/nodes_extension__sales_order__default_supplier_not_found',
                'Default supplier not found for item {{item}} and site {{site}}.',
                { item: await item.name, site: await site.name },
            ),
        );
    }
    return supplier;
}

export async function getGrossPrice(
    context: Context,
    priceParameters: xtremMasterData.interfaces.PurchasePriceParameters,
): Promise<number> {
    const price = await xtremMasterData.nodes.ItemSupplierPrice.getPurchasePrice(context, priceParameters);
    return price < 0 ? 0 : price; // Ensure price is not negative
}

async function createOrderAssignment(
    context: Context,
    data: AssignmentCreationParameters,
): Promise<xtremStockData.nodes.OrderAssignment> {
    const orderAssignment = await context.create(xtremStockData.nodes.OrderAssignment, data);
    await orderAssignment.$.save();
    return orderAssignment;
}

export async function createPurchaseOrderAssignment(
    context: Context,
    data: BackToBackPurchaseOrderCreationParameters,
    purchaseOrder: xtremPurchasing.nodes.PurchaseOrder,
): Promise<xtremStockData.nodes.OrderAssignment> {
    const purchaseOrderLine = await purchaseOrder.lines.at(0);
    const { remainingQuantityToShipInStockUnit } = data;

    const supplyOrderQuantityInStockUnit = (await purchaseOrderLine?.quantityInStockUnit) ?? 0;

    return createOrderAssignment(context, {
        ...data,
        quantityInStockUnit:
            supplyOrderQuantityInStockUnit >= remainingQuantityToShipInStockUnit
                ? remainingQuantityToShipInStockUnit
                : supplyOrderQuantityInStockUnit,
        demandType: 'salesOrderLine',
        demandDocumentLine: data.salesOrderLineSysId,
        demandWorkInProgress: data.salesOrderLineWorkInProgress,
        supplyType: 'purchaseOrderLine',
        supplyDocumentLine: purchaseOrderLine ? purchaseOrderLine?._id : 0,
        supplyWorkInProgress: purchaseOrderLine ? await purchaseOrderLine?.workInProgress : null,
    });
}

export async function createWorkOrderAssignment(
    context: Context,
    data: BackToBackWorkOrderCreationParameters,
    workOrder: xtremManufacturing.nodes.WorkOrder,
): Promise<xtremStockData.nodes.OrderAssignment> {
    const productionItems = await workOrder.productionItems.at(0);
    const { remainingQuantityToShipInStockUnit } = data;

    const supplyOrderQuantityInStockUnit = (await productionItems?.releasedQuantity) ?? 0;

    return createOrderAssignment(context, {
        ...data,
        quantityInStockUnit:
            supplyOrderQuantityInStockUnit >= remainingQuantityToShipInStockUnit
                ? remainingQuantityToShipInStockUnit
                : supplyOrderQuantityInStockUnit,
        demandType: 'salesOrderLine',
        demandDocumentLine: data.salesOrderLineSysId,
        demandWorkInProgress: data.salesOrderLineWorkInProgress,
        supplyType: 'workOrder',
        supplyDocumentLine: productionItems ? productionItems._id : 0,
        supplyWorkInProgress: productionItems ? await productionItems.workInProgress : null,
        stockSite: data.site,
    });
}
async function getWorkOrderCategoryWithoutRouting(
    context: Context,
): Promise<xtremManufacturing.nodes.WorkOrderCategory> {
    return (
        (await context
            .query(xtremManufacturing.nodes.WorkOrderCategory, {
                filter: { billOfMaterial: true, routing: false, isDefault: true },
            })
            .at(0)) ?? context.read(xtremManufacturing.nodes.WorkOrderCategory, { id: 'Assembly' })
    );
}

async function getWorkOrderCategoryWithRouting(context: Context): Promise<xtremManufacturing.nodes.WorkOrderCategory> {
    return (
        (await context
            .query(xtremManufacturing.nodes.WorkOrderCategory, {
                filter: { billOfMaterial: true, routing: true, isDefault: true },
            })
            .at(0)) ?? context.read(xtremManufacturing.nodes.WorkOrderCategory, { id: 'Normal' })
    );
}

async function readBillOfMaterial(
    context: Context,
    item: xtremMasterData.nodes.Item,
    site: xtremSystem.nodes.Site,
): Promise<xtremTechnicalData.nodes.BillOfMaterial | null> {
    const billOfMaterial = await context.tryRead(xtremTechnicalData.nodes.BillOfMaterial, { item, site });
    if (!billOfMaterial) {
        throw new BusinessRuleError(
            context.localize(
                '@sage/xtrem-supply-chain/nodes_extensions__sales_order_extension__bill_of_material_not_found',
                'No bill of material available to use found.',
            ),
        );
    }
    return billOfMaterial;
}

export async function getWorkOrderCategory(
    context: Context,
    item: xtremMasterData.nodes.Item,
    site: xtremSystem.nodes.Site,
): Promise<xtremManufacturing.nodes.WorkOrderCategory> {
    if (await (await readBillOfMaterial(context, item, site))?.routingCode) {
        return getWorkOrderCategoryWithRouting(context);
    }
    return getWorkOrderCategoryWithoutRouting(context);
}

export function getWorkOrderStartDate(prodLeadTime: number, shippingDate: date): date {
    const startDate = shippingDate.addDays(-prodLeadTime);
    return startDate.daysDiff(date.today()) < 0 ? date.today() : startDate;
}

export async function getPurchaseQuantity(
    context: Context,
    orderLine: {
        item: xtremMasterData.nodes.Item;
        quantity: number;
        supplier: xtremMasterData.nodes.Supplier;
        unit: xtremMasterData.nodes.UnitOfMeasure;
    },
    itemSupplier: xtremMasterData.nodes.ItemSupplier | null,
): Promise<number> {
    const purchaseQuantity = await xtremMasterData.nodes.UnitOfMeasure.convertFromTo(
        context,
        await orderLine.item.stockUnit,
        orderLine.unit,
        orderLine.quantity,
        orderLine.item,
        orderLine.supplier,
        undefined,
        'purchase',
        true,
    );
    const minimumPurchaseQuantity = itemSupplier ? await itemSupplier.minimumPurchaseQuantity : 0;
    return purchaseQuantity < minimumPurchaseQuantity ? minimumPurchaseQuantity : purchaseQuantity;
}
