import type { ValidationContext } from '@sage/xtrem-core';

import * as xtremFinanceData from '@sage/xtrem-finance-data';
import type * as xtremSupply<PERSON>hain from '..';

export async function mandatoryDimensionAndAttributeControl(
    cx: ValidationContext,
    nodeInstance: xtremSupplyChain.nodes.StockTransferOrderLine,
) {
    // Mandatory company attributes and dimensions control - to be replaced with financeIntegrationCheck when integration is implemented
    const companyId = (await (await (await nodeInstance.document)?.stockSite)?.legalCompany)?._id;
    const mandatoryAttributes = nodeInstance.$.context.query(xtremFinanceData.nodes.CompanyAttributeType, {
        filter: { company: companyId, isRequired: true },
    });
    const mandatoryDimensions = nodeInstance.$.context.query(xtremFinanceData.nodes.CompanyDimensionType, {
        filter: { company: companyId, isRequired: true },
    });

    const undefinedAttributes = await xtremFinanceData.functions.mandatoryAttributeControl(
        (await nodeInstance.storedAttributes) ?? { employee: '', project: '', task: '' },
        await mandatoryAttributes.toArray(),
    );
    const undefinedDimensions = await xtremFinanceData.functions.mandatoryDimensionControl(
        (await nodeInstance.storedDimensions) ?? '{}',
        await mandatoryDimensions.toArray(),
    );
    const undefinedAttributeDimension = [...undefinedAttributes, ...undefinedDimensions];

    if (undefinedAttributeDimension.length > 0) {
        const dimensions = undefinedAttributeDimension.map(String).join(', ');
        cx.warn.addLocalized(
            '@sage/xtrem-supply-chain/nodes__stock_transfer_order_line__mandatory_dimension_not_found',
            'You need to select the company dimension: {{dimension}}.',
            {
                dimension: dimensions.toLowerCase(),
            },
        );
    }
}
