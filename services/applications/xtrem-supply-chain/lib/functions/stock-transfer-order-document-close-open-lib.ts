import type * as xtremDistribution from '@sage/xtrem-distribution';
import type * as xtremSupply<PERSON>hain from '..';

type OpenOrClose = 'open' | 'close';
type LineStatusReturnType<T> = T extends 'open'
    ? xtremDistribution.enums.DocumentOpenStatusMethodReturn
    : xtremDistribution.enums.DocumentCloseStatusMethodReturn;

async function setLineStatus<T extends OpenOrClose>(
    type: T,
    stockTransferOrder: xtremSupplyChain.nodes.StockTransferOrder,
    stockTransferOrderLine: xtremSupplyChain.nodes.StockTransferOrderLine,
): Promise<LineStatusReturnType<T>> {
    if (type === 'open') {
        const status = await stockTransferOrder.status;
        const shippingStatus = await stockTransferOrder.shippingStatus;
        const approvalStatus = await stockTransferOrder.approvalStatus;
        if (['draft', 'closed'].includes(status) && shippingStatus === 'notShipped') {
            if (['confirmed', 'approved'].includes(approvalStatus)) {
                await stockTransferOrder.$.set({
                    lines: [
                        {
                            _action: 'update',
                            _sortValue: await stockTransferOrderLine._sortValue,
                            _id: stockTransferOrderLine._id,
                            status: 'pending',
                        },
                    ],
                });
            } else {
                await stockTransferOrder.$.set({
                    lines: [
                        {
                            _action: 'update',
                            _sortValue: await stockTransferOrderLine._sortValue,
                            _id: stockTransferOrderLine._id,
                            status: 'draft',
                        },
                    ],
                });
            }
            return 'isNowOpen' as LineStatusReturnType<T>;
        }
        if (['pending', 'closed'].includes(status) && shippingStatus === 'notShipped') {
            await stockTransferOrder.$.set({
                lines: [
                    {
                        _action: 'update',
                        _sortValue: await stockTransferOrderLine._sortValue,
                        _id: stockTransferOrderLine._id,
                        status: 'pending',
                    },
                ],
            });
            return 'isNowOpen' as LineStatusReturnType<T>;
        }
        return 'isShipped' as LineStatusReturnType<T>;
    }

    await stockTransferOrder.$.set({
        lines: [
            {
                _action: 'update',
                _sortValue: await stockTransferOrderLine._sortValue,
                _id: stockTransferOrderLine._id,
                status: 'closed',
            },
        ],
    });
    return 'isNowClosed' as LineStatusReturnType<T>;
}

export async function closeLine(
    stockTransferOrder: xtremSupplyChain.nodes.StockTransferOrder,
    stockTransferOrderLine: xtremSupplyChain.nodes.StockTransferOrderLine,
): Promise<xtremDistribution.enums.DocumentCloseStatusMethodReturn> {
    if ((await stockTransferOrderLine.status) === 'closed') {
        return 'isAlreadyClosed';
    }

    const lineStatus = await setLineStatus('close', stockTransferOrder, stockTransferOrderLine);
    await stockTransferOrder.$.save();
    return lineStatus;
}

export async function openLine(
    stockTransferOrder: xtremSupplyChain.nodes.StockTransferOrder,
    stockTransferOrderLine: xtremSupplyChain.nodes.StockTransferOrderLine,
): Promise<xtremDistribution.enums.DocumentOpenStatusMethodReturn> {
    if ((await stockTransferOrderLine.status) !== 'closed') {
        return 'isAlreadyOpen';
    }

    const lineStatus = await setLineStatus('open', stockTransferOrder, stockTransferOrderLine);
    await stockTransferOrder.$.save();
    return lineStatus;
}

export async function closeStockTransferOrder(
    stockTransferOrder: xtremSupplyChain.nodes.StockTransferOrder,
): Promise<xtremDistribution.enums.DocumentCloseStatusMethodReturn> {
    if ((await stockTransferOrder.status) === 'closed') {
        return 'isAlreadyClosed';
    }

    const lineStatuses = await stockTransferOrder.lines
        .map(stockTransferOrderLine => setLineStatus('close', stockTransferOrder, stockTransferOrderLine))
        .toArray();

    await stockTransferOrder.$.save();
    if (lineStatuses.length === 0) {
        return 'isAlreadyClosed';
    }
    return 'isNowClosed';
}

export async function openStockTransferOrder(
    stockTransferOrder: xtremSupplyChain.nodes.StockTransferOrder,
): Promise<xtremDistribution.enums.DocumentOpenStatusMethodReturn> {
    if ((await stockTransferOrder.status) !== 'closed') {
        return 'isAlreadyOpen';
    }

    const lineStatuses = await stockTransferOrder.lines
        .map(stockTransferOrderLine => setLineStatus('open', stockTransferOrder, stockTransferOrderLine))
        .toArray();

    await stockTransferOrder.$.save();
    if (lineStatuses.some(status => status === 'isShipped')) {
        return 'isShipped';
    }
    if (lineStatuses.length === 0) {
        return 'isAlreadyOpen';
    }
    return 'isNowOpen';
}
