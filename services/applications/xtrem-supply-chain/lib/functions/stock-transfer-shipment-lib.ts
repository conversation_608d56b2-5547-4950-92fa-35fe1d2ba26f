import type { Collection, Context, UpdateAction } from '@sage/xtrem-core';
import { NodeStatus } from '@sage/xtrem-core';
import type * as xtremDistribution from '@sage/xtrem-distribution';
import * as xtremStockData from '@sage/xtrem-stock-data';
import * as xtremSupply<PERSON>hain from '..';
import type { OrderToUpdate } from '../interfaces';

export async function updateStockTransferShipmentLinesAfterShipmentStockCompleted(
    context: Context,
    stockTransferShipment: xtremSupplyChain.nodes.StockTransferShipment,
): Promise<void> {
    await stockTransferShipment.$.set({
        lines: await stockTransferShipment.lines
            .map(async line => {
                const { orderAmount, quantityInStockUnit } =
                    await xtremStockData.functions.stockValuationLib.getDocumentLineStockValue(context, line._id);
                const item = await line.item;
                const itemSite = (await line.itemSite) ?? undefined;
                const unitCost = await xtremStockData.functions.stockValuationLib.getStockCost(context, {
                    costType: 'value',
                    documentLineId: line._id,
                    movementType: 'transfer',
                    itemSite,
                });
                return {
                    _action: 'update' as UpdateAction,
                    _id: line._id,
                    orderCost: quantityInStockUnit !== 0 ? orderAmount / quantityInStockUnit : 0,
                    inTransit: {
                        unitCost,
                        costType: await itemSite?.valuationMethod,
                        currency: await stockTransferShipment.currency,
                        stockValue: Math.abs(quantityInStockUnit * (await line.getValuedCost())),
                        quantityInStockUnit,
                        commodityCode: await item.commodityCode,
                        startDate: await stockTransferShipment.documentDate,
                        stockTransferShipmentLine: line,
                    },
                };
            })
            .toArray(),
    });
}

async function calculateReceivingStatus(
    stockTransferShippingLines: Collection<xtremSupplyChain.nodes.StockTransferShipmentLine>,
): Promise<xtremDistribution.enums.ReceivingStatus> {
    if ((await stockTransferShippingLines.length) <= 0) {
        return 'notReceived';
    }
    if (await stockTransferShippingLines.every(async line => (await line.receivingStatus) === 'received')) {
        return 'received';
    }
    if (
        (await stockTransferShippingLines.filter(async line =>
            ['received', 'partiallyReceived'].includes(await line.receivingStatus),
        ).length) > 0
    ) {
        return 'partiallyReceived';
    }
    return 'notReceived';
}

async function calculateStatus(
    stockTransferShipmentLines: Collection<xtremSupplyChain.nodes.StockTransferShipmentLine>,
): Promise<xtremSupplyChain.enums.StockTransferShipmentStatus> {
    if ((await stockTransferShipmentLines.length) <= 0) {
        return 'readyToProcess';
    }
    if (await stockTransferShipmentLines.every(async line => (await line.status) === 'shipped')) {
        return 'shipped';
    }
    if (await stockTransferShipmentLines.every(async line => (await line.status) === 'readyToShip')) {
        return 'readyToShip';
    }
    if (
        (await stockTransferShipmentLines.filter(async line => ['readyToShip', 'shipped'].includes(await line.status))
            .length) > 0
    ) {
        return 'readyToShip';
    }
    return 'readyToProcess';
}

export function calculateShipmentDisplayStatus(
    status: xtremSupplyChain.enums.StockTransferShipmentStatus,
    receivingStatus: xtremDistribution.enums.ReceivingStatus,
    stockTransactionStatus: xtremStockData.enums.StockDocumentTransactionStatus,
): xtremSupplyChain.enums.StockTransferShipmentDisplayStatus {
    if (stockTransactionStatus === 'error') {
        return 'error';
    }
    if (stockTransactionStatus === 'inProgress') {
        return 'postingInProgress';
    }
    if (stockTransactionStatus === 'completed') {
        return 'shipped';
    }
    if (receivingStatus === 'received') {
        return 'received';
    }
    if (status === 'readyToShip') {
        return 'readyToShip';
    }
    if (status === 'shipped') {
        return 'shipped';
    }
    if (status === 'received') {
        return 'received';
    }
    return 'readyToProcess';
}

export async function calculateShipmentStatuses(
    stockTransferShipmentLines: Collection<xtremSupplyChain.nodes.StockTransferShipmentLine>,
    stockTransactionStatus: xtremStockData.enums.StockDocumentTransactionStatus,
) {
    const receivingStatus = await calculateReceivingStatus(stockTransferShipmentLines);
    const status = await calculateStatus(stockTransferShipmentLines);
    const displayStatus = calculateShipmentDisplayStatus(status, receivingStatus, stockTransactionStatus);
    return {
        receivingStatus,
        status,
        displayStatus,
    };
}

async function getStockTransferOrder(
    context: Context,
    link: xtremSupplyChain.nodes.StockTransferShipmentLineToStockTransferOrderLine,
): Promise<OrderToUpdate> {
    const stockTransferOrderId = (await (await link.to).document)._id;
    const stockTransferOrderLineId = (await link.to)._id;
    const stockTransferOrder = await context.read(
        xtremSupplyChain.nodes.StockTransferOrder,
        {
            _id: stockTransferOrderId,
        },
        { forUpdate: true },
    );
    return { stockTransferOrder, stockTransferOrderLineId };
}

export async function updateStockTransferOrderLineAfterShipmentCreated(
    stockTransferShipmentLine: xtremSupplyChain.nodes.StockTransferShipmentLine,
) {
    await stockTransferShipmentLine.linkToStockTransferOrderLines.forEach(async link => {
        const order: OrderToUpdate = await getStockTransferOrder(stockTransferShipmentLine.$.context, link);
        await order.stockTransferOrder.$.set({
            lines: [
                {
                    _action: 'update' as UpdateAction,
                    _id: order.stockTransferOrderLineId,
                    status: 'inProgress' as xtremSupplyChain.enums.StockTransferOrderStatus,
                },
            ],
        });
        await order.stockTransferOrder.$.save();
    });
}

export async function updateStockTransferOrderLineAfterShipmentDeleted(
    stockTransferShipmentLine: xtremSupplyChain.nodes.StockTransferShipmentLine,
) {
    await stockTransferShipmentLine.linkToStockTransferOrderLines.forEach(async link => {
        const order: OrderToUpdate = await getStockTransferOrder(stockTransferShipmentLine.$.context, link);
        await order.stockTransferOrder.$.set({
            lines: [
                {
                    _action: 'update' as UpdateAction,
                    _id: order.stockTransferOrderLineId,
                    status: 'pending' as xtremSupplyChain.enums.StockTransferOrderStatus,
                },
            ],
        });
        await order.stockTransferOrder.$.save();
    });
}

export async function updateStockTransferOrderLineAfterReceiptCreated(
    stockTransferShipmentLine: xtremSupplyChain.nodes.StockTransferShipmentLine,
) {
    await stockTransferShipmentLine.linkToStockTransferOrderLines.forEach(async link => {
        const order: OrderToUpdate = await getStockTransferOrder(stockTransferShipmentLine.$.context, link);
        await order.stockTransferOrder.$.set({
            lines: [
                {
                    _action: 'update' as UpdateAction,
                    _id: order.stockTransferOrderLineId,
                    shippingStatus: 'shipped' as xtremDistribution.enums.ShippingStatus,
                },
            ],
        });
        await order.stockTransferOrder.$.save();
    });
}

export async function updateStockTransferOrderLineAfterReceiptDeleted(
    stockTransferShipmentLine: xtremSupplyChain.nodes.StockTransferShipmentLine,
) {
    await stockTransferShipmentLine.linkToStockTransferOrderLines.forEach(async link => {
        const order: OrderToUpdate = await getStockTransferOrder(stockTransferShipmentLine.$.context, link);
        await order.stockTransferOrder.$.set({
            lines: [
                {
                    _action: 'update' as UpdateAction,
                    _id: order.stockTransferOrderLineId,
                    shippingStatus: 'notShipped' as xtremDistribution.enums.ShippingStatus,
                },
            ],
        });
        await order.stockTransferOrder.$.save();
    });
}

export async function updateStockTransferOrderLineAfterReceiptPosted(
    stockTransferShipmentLine: xtremSupplyChain.nodes.StockTransferShipmentLine,
) {
    await stockTransferShipmentLine.linkToStockTransferOrderLines.forEach(async link => {
        const order: OrderToUpdate = await getStockTransferOrder(stockTransferShipmentLine.$.context, link);
        await order.stockTransferOrder.$.set({
            lines: [
                {
                    _action: 'update' as UpdateAction,
                    _id: order.stockTransferOrderLineId,
                    status: 'closed' as xtremSupplyChain.enums.StockTransferOrderStatus,
                    receivingStatus: 'received' as xtremDistribution.enums.ReceivingStatus,
                },
            ],
        });
        await order.stockTransferOrder.$.save();
    });
}

export async function transferOrderLineAllocationsAfterReceiptCreatedOrOrderUpdated(
    stockTransferShipmentLine: xtremSupplyChain.nodes.StockTransferShipmentLine,
) {
    const quantityInStockUnit = await stockTransferShipmentLine.quantityInStockUnit;
    const oldQuantityInStockUnit =
        stockTransferShipmentLine.$.status === NodeStatus.modified
            ? await (
                  await stockTransferShipmentLine.$.old
              ).quantityInStockUnit
            : 0;
    if (
        stockTransferShipmentLine.$.status === NodeStatus.added ||
        (stockTransferShipmentLine.$.status === NodeStatus.modified && quantityInStockUnit - oldQuantityInStockUnit > 0)
    ) {
        await stockTransferShipmentLine.linkToStockTransferOrderLines.forEach(async line => {
            const requiredQuantity =
                stockTransferShipmentLine.$.status === NodeStatus.added
                    ? quantityInStockUnit
                    : quantityInStockUnit - oldQuantityInStockUnit;

            const allocationPropositions = await xtremStockData.nodes.Stock.proposeAllocationsToTransfer(
                stockTransferShipmentLine.$.context,
                {
                    orderDocumentLine: await stockTransferShipmentLine.$.context.read(
                        xtremSupplyChain.nodes.StockTransferOrderLine,
                        { _id: (await line.to)._id },
                        { forUpdate: true },
                    ),
                    requiredQuantity,
                    hasAllAllocationsReturned: false,
                },
            );
            if (allocationPropositions.length > 0) {
                await xtremStockData.nodes.Stock.updateAllocations(stockTransferShipmentLine.$.context, {
                    documentLine: line.from,
                    allocationUpdates: allocationPropositions.map(proposition => {
                        return {
                            action: 'transfer',
                            quantityToTransfer: proposition.quantityToTransfer,
                            allocationRecord:
                                proposition.allocationRecord as unknown as Promise<xtremStockData.nodes.StockAllocation>,
                            serialNumbers: proposition.serialNumbers,
                        };
                    }),
                });
            }
        });
    }
}
