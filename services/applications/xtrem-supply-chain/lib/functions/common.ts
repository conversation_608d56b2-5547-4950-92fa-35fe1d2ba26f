import type { OperationGrant } from '@sage/xtrem-core';
import * as xtremFinanceData from '@sage/xtrem-finance-data';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremStructure from '@sage/xtrem-structure';
import * as xtremSystem from '@sage/xtrem-system';
import * as xtremUpload from '@sage/xtrem-upload';

export const commonStockTransferActivities: OperationGrant[] = [
    { operations: ['lookup'], on: [() => xtremSystem.nodes.Company] },
    { operations: ['lookup'], on: [() => xtremSystem.nodes.Site] },
    { operations: ['lookup'], on: [() => xtremMasterData.nodes.BusinessEntity] },
    { operations: ['lookup'], on: [() => xtremMasterData.nodes.Supplier] },
    { operations: ['lookup'], on: [() => xtremStructure.nodes.Legislation] },
    { operations: ['lookup'], on: [() => xtremStructure.nodes.Country] },
    { operations: ['lookup'], on: [() => xtremMasterData.nodes.Currency] },
    { operations: ['lookup', 'getPurchaseUnit', 'convertFromTo'], on: [() => xtremMasterData.nodes.UnitOfMeasure] },
    { operations: ['lookup'], on: [() => xtremMasterData.nodes.Item] },
    { operations: ['lookup'], on: [() => xtremMasterData.nodes.Customer] },
    { operations: ['lookup'], on: [() => xtremMasterData.nodes.Incoterm] },
    { operations: ['lookup'], on: [() => xtremMasterData.nodes.DeliveryMode] },
    { operations: ['lookup'], on: [() => xtremMasterData.nodes.PaymentTerm] },
    { operations: ['lookup'], on: [() => xtremMasterData.nodes.ItemCustomer] },
    { operations: ['lookup'], on: [() => xtremMasterData.nodes.ItemSite] },
    { operations: ['lookup'], on: [() => xtremMasterData.nodes.Location] },
    { operations: ['lookup'], on: [() => xtremMasterData.nodes.LocationType] },
    { operations: ['lookup'], on: [() => xtremMasterData.nodes.LocationZone] },
    { operations: ['lookup', 'getSalesPrice'], on: [() => xtremMasterData.nodes.ItemCustomerPrice] },
    { operations: ['lookup'], on: [() => xtremUpload.nodes.AttachmentAssociation] },
    ...xtremFinanceData.functions.dimensionsAndAttributesOperations,
];
