import type { Collection, Context } from '@sage/xtrem-core';
import { BusinessRuleError, asyncArray } from '@sage/xtrem-core';
import type * as xtremDistribution from '@sage/xtrem-distribution';
import * as xtremLandedCost from '@sage/xtrem-landed-cost';
import type * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremSupplyChain from '..';

export async function controlStockTransferOrderForStockTransferShipmentCreation(
    context: Context,
    stockTransferOrderLines: xtremSupplyChain.nodes.StockTransferOrderLine[],
    options?: xtremSupplyChain.interfaces.CreateShipmentOptions,
) {
    await asyncArray(stockTransferOrderLines)
        .filter(async line => (await line.status) !== 'closed')
        .forEach(async line => {
            const isStatusOk = (await line.status) !== 'draft' || options?.isForFinanceCheck;
            if (!isStatusOk) {
                throw new BusinessRuleError(
                    context.localize(
                        '@sage/xtrem-supply-chain/nodes__stock_transfer_order__invalid_selected_lines_status',
                        'The status for the selected lines cannot be Draft.',
                    ),
                );
            }

            if ((await line.shippingStatus) === 'shipped') {
                throw new BusinessRuleError(
                    context.localize(
                        '@sage/xtrem-supply-chain/nodes__stock_transfer_order__invalid_selected_lines__shipping_status',
                        'The stock transfer order line is already shipped.',
                    ),
                );
            }

            if ((await line.allocationRequestStatus) === 'inProgress' && !options?.isForFinanceCheck) {
                throw new BusinessRuleError(
                    context.localize(
                        '@sage/xtrem-supply-chain/nodes__stock_transfer_order__invalid_selected_lines__allocation_request_status',
                        'You can only ship the stock transfer order line after the allocation request is complete.',
                    ),
                );
            }

            const shippingDate = await line.shippingDate;
            const doNotShipBeforeDate = await line.doNotShipBeforeDate;
            const doNotShipAfterDate = await line.doNotShipAfterDate;
            if (!shippingDate || !doNotShipBeforeDate || !doNotShipAfterDate) {
                return;
            }
            if (shippingDate < doNotShipBeforeDate || shippingDate > doNotShipAfterDate) {
                throw new BusinessRuleError(
                    context.localize(
                        '@sage/xtrem-supply-chain/nodes__stock_transfer_order__improper_shipping_date',
                        'The shipping date needs to be later than the do not ship before date and needs to be earlier than the do not ship after date.',
                    ),
                );
            }

            const documentLineStatus = await line.status;
            // Line status closed or quote
            if (documentLineStatus === 'closed' || (documentLineStatus === 'draft' && !options?.isForFinanceCheck)) {
                throw new BusinessRuleError(
                    context.localize(
                        '@sage/xtrem-supply-chain/nodes__stock_transfer_order__line_closed_quote_exception',
                        'The line status cannot be "Draft" or "Closed".',
                    ),
                );
            }
        });
}

/**
 * Function that changes the stock transfer order status from draft to pending
 * @param stockTransferOrder the stock transfer order
 * @returns enum StockTransferOrderConfirmMethodReturn
 */
export async function confirm(
    stockTransferOrder: xtremSupplyChain.nodes.StockTransferOrder,
): Promise<xtremDistribution.enums.DocumentConfirmStatusMethodReturn> {
    // Stock transfer order can be inProgress status but check that lines is still in draft status for processing
    const canConfirm = await stockTransferOrder.lines.some(async line => (await line.status) === 'draft');
    if (!canConfirm) {
        return 'isNotDraft';
    }

    await stockTransferOrder.lines
        .filter(async line => (await line.status) === 'draft')
        .forEach(line => line.$.set({ status: 'pending' }));
    await stockTransferOrder.$.set({ approvalStatus: 'confirmed' });
    await stockTransferOrder.$.save();
    return 'isConfirmed';
}

/**
 * Indicates if the stock transfer order line can be automatically allocated
 * @param line stock transfer order line to check
 * @returns true if this SO line should be considered in the automatic allocation
 */
export async function isStockTransferOrderLineAllocable(
    line: xtremSupplyChain.nodes.StockTransferOrderLine,
): Promise<boolean> {
    return ['inProgress', 'pending'].includes(await line.status) && (await line.item).isStockManaged;
}

/**
 * Indicates if the stock transfer order line should be taken into account for computing the header allocation status
 * @param line stock transfer order line to check
 * @returns true if this SO line should be considered in the allocation status determination
 */
export async function isStockTransferOrderLineConsideredForAllocationStatus(
    line: xtremSupplyChain.nodes.StockTransferOrderLine,
): Promise<boolean> {
    return (await line.status) !== 'closed';
}

async function calculateReceivingStatus(
    stockTransferOrderLines: Collection<xtremSupplyChain.nodes.StockTransferOrderLine>,
): Promise<xtremDistribution.enums.ReceivingStatus> {
    if ((await stockTransferOrderLines.length) <= 0) {
        return 'notReceived';
    }
    if (
        await stockTransferOrderLines.every(
            async line => (await line.status) === 'closed' || (await line.status) === 'inProgress',
        )
    ) {
        if (await stockTransferOrderLines.find(async line => (await line.receivingStatus) === 'received')) {
            return 'received';
        }
        if (await stockTransferOrderLines.find(async line => (await line.receivingStatus) === 'partiallyReceived')) {
            return 'partiallyReceived';
        }
    }
    return 'notReceived';
}

async function calculateShippingStatus(
    stockTransferOrderLines: Collection<xtremSupplyChain.nodes.StockTransferOrderLine>,
): Promise<xtremDistribution.enums.ShippingStatus> {
    if ((await stockTransferOrderLines.length) <= 0) {
        return 'notShipped';
    }
    if (
        await stockTransferOrderLines.every(
            async line => (await line.status) === 'closed' || (await line.status) === 'inProgress',
        )
    ) {
        if (await stockTransferOrderLines.find(async line => (await line.shippingStatus) === 'shipped')) {
            return 'shipped';
        }
        if (await stockTransferOrderLines.find(async line => (await line.shippingStatus) === 'partiallyShipped')) {
            return 'partiallyShipped';
        }
    }
    return 'notShipped';
}

/**
 * Calculates the display status of the order based on the other document status properties
 * @param status
 * @param shippingStatus
 * @param receivingStatus
 * @param taxCalculationStatus
 * @returns display status enum
 */
export function calculateOrderDisplayStatus(
    status: xtremSupplyChain.enums.StockTransferOrderStatus,
    shippingStatus: xtremDistribution.enums.ShippingStatus,
    receivingStatus: xtremDistribution.enums.ReceivingStatus,
    approvalStatus: xtremMasterData.enums.ApprovalStatus,
): xtremSupplyChain.enums.StockTransferOrderDisplayStatus {
    if (approvalStatus === 'rejected') {
        return 'rejected';
    }
    if (receivingStatus === 'received') {
        return 'received';
    }
    if (receivingStatus === 'partiallyReceived') {
        return 'partiallyReceived';
    }
    if (shippingStatus === 'shipped') {
        return 'shipped';
    }
    if (status === 'closed') {
        return 'closed';
    }
    if (shippingStatus === 'partiallyShipped') {
        return 'partiallyShipped';
    }
    if (['pendingApproval', 'approved', 'confirmed'].includes(approvalStatus)) {
        return approvalStatus as xtremSupplyChain.enums.StockTransferOrderDisplayStatus;
    }
    if (status === 'pending') {
        return 'confirmed';
    }
    return 'draft';
}

async function calculateStatus(
    stockTransferOrderLines: Collection<xtremSupplyChain.nodes.StockTransferOrderLine>,
): Promise<xtremSupplyChain.enums.StockTransferOrderStatus> {
    if ((await stockTransferOrderLines.length) <= 0) {
        return 'draft';
    }
    if (await stockTransferOrderLines.every(async line => (await line.status) === 'closed')) {
        return 'closed';
    }
    if (await stockTransferOrderLines.find(async line => (await line.status) === 'pending')) {
        return 'pending';
    }
    if (await stockTransferOrderLines.find(async line => (await line.status) === 'inProgress')) {
        return 'inProgress';
    }
    return 'draft';
}

export async function calculateOrderStatuses(
    stockTransferOrderLines: Collection<xtremSupplyChain.nodes.StockTransferOrderLine>,
    approvalStatus: xtremMasterData.enums.ApprovalStatus,
) {
    const status = await calculateStatus(stockTransferOrderLines);
    const shippingStatus = await calculateShippingStatus(stockTransferOrderLines);
    const receivingStatus = await calculateReceivingStatus(stockTransferOrderLines);
    const displayStatus = calculateOrderDisplayStatus(status, shippingStatus, receivingStatus, approvalStatus);
    return {
        receivingStatus,
        shippingStatus,
        status,
        displayStatus,
    };
}

export async function getStockTransferReceiptLineFromStockTransferOrderLine(
    context: Context,
    stockTransferOrderLine: xtremSupplyChain.nodes.StockTransferOrderLine,
): Promise<xtremSupplyChain.nodes.StockTransferReceiptLine | null> {
    const link1 = await stockTransferOrderLine.linkToStockTransferShipmentLines.at(0);
    if (link1) {
        const stockTransferShipmentLineId = (await link1.from)._id;
        const stockTransferShipmentLine = await context.read(xtremSupplyChain.nodes.StockTransferShipmentLine, {
            _id: stockTransferShipmentLineId,
        });
        const link2 = await stockTransferShipmentLine.linkToStockTransferReceiptLines.at(0);
        if (link2) {
            const stockTransferReceiptLineId = (await link2.from)._id;
            return context.read(xtremSupplyChain.nodes.StockTransferReceiptLine, {
                _id: stockTransferReceiptLineId,
            });
        }
    }
    return null;
}

export function getStockTransferReceiptLineActualLandedCostInCompanyCurrency(
    stockTransferReceiptLine: xtremSupplyChain.nodes.StockTransferReceiptLine | null,
) {
    return stockTransferReceiptLine?.actualLandedCostInCompanyCurrency || 0;
}

async function getStockTransferReceiptLinesFromStockTransferOrder(
    context: Context,
    stockTransferOrder: xtremSupplyChain.nodes.StockTransferOrder,
): Promise<Collection<xtremSupplyChain.nodes.StockTransferReceiptLine> | null> {
    const stockTransferOrderLine = await stockTransferOrder.lines.at(0);
    if (stockTransferOrderLine) {
        const link1 = await stockTransferOrderLine.linkToStockTransferShipmentLines.at(0);
        if (link1) {
            const stockTransferShipmentLineId = (await link1.from)._id;
            const stockTransferShipmentLine = await context.read(xtremSupplyChain.nodes.StockTransferShipmentLine, {
                _id: stockTransferShipmentLineId,
            });
            const link2 = await stockTransferShipmentLine.linkToStockTransferReceiptLines.at(0);
            if (link2) {
                const stockTransferReceiptId = (await (await link2.from).document)._id;
                const stockTransferReceipt = context.read(xtremSupplyChain.nodes.StockTransferReceipt, {
                    _id: stockTransferReceiptId,
                });
                return (await stockTransferReceipt).lines;
            }
        }
    }
    return null;
}

export async function getStockTransferOrderLandedCostsPerType(
    context: Context,
    stockTransferOrder: xtremSupplyChain.nodes.StockTransferOrder,
) {
    const stockTransferReceiptLines = await getStockTransferReceiptLinesFromStockTransferOrder(
        context,
        stockTransferOrder,
    );
    return stockTransferReceiptLines
        ? xtremLandedCost.functions.landedCostLineLib.getLandedCostsPerType(stockTransferReceiptLines)
        : [];
}
