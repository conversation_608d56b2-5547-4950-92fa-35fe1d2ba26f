import type { Context } from '@sage/xtrem-core';
import { BusinessRuleError } from '@sage/xtrem-core';
import type * as xtremMailer from '@sage/xtrem-mailer';
import * as xtremMasterData from '@sage/xtrem-master-data';
import type * as xtremSystem from '@sage/xtrem-system';
import type * as xtremSupplyChain from '..';

export async function controlBeforeSendMailApproval(
    context: Context,
    transferOrder: xtremSupplyChain.nodes.StockTransferOrder,
) {
    if (
        !['draft', 'changeRequested'].includes(await transferOrder.approvalStatus) &&
        (await transferOrder.status) !== 'closed'
    ) {
        throw new BusinessRuleError(
            context.localize(
                '@sage/xtrem-supply-chain/functions__send__email__not_in_submission_for_approval_not_allowed',
                'Submission for approval action not allowed. The stock transfer order is not in draft status.',
            ),
        );
    }
}

export async function getApprovalUser(
    context: Context,
    transferOrder: xtremSupplyChain.nodes.StockTransferOrder,
): Promise<xtremSystem.nodes.User> {
    const site = await transferOrder.site;

    const defaultApprover = await site.stockTransferOrderDefaultApprover;
    const substituteApprover = await site.stockTransferOrderSubstituteApprover;
    if (defaultApprover) {
        return defaultApprover;
    }
    if (substituteApprover) {
        return substituteApprover;
    }
    throw new BusinessRuleError(
        context.localize(
            '@sage/xtrem-supply-chain/functions__send__email__no_default_or_substitute_approver',
            'There is no default or substitute approver for the current document.',
        ),
    );
}

export function getMailerConfig(context: Context): xtremMailer.interfaces.MailerConfig {
    const config = context.configuration.getPackageConfig<xtremMailer.interfaces.MailerConfig>('@sage/xtrem-mailer');
    if (!config || !config.redirectUrl) {
        throw new BusinessRuleError(
            context.localize(
                '@sage/xtrem-supply-chain/mailer_no_mailer_redirect_url_provided',
                "Mailer's redirect URL is missing in configuration file.",
            ),
        );
    }

    return config;
}

export async function getTransferOrderDataForApproval(
    context: Context,
    transferOrder: xtremSupplyChain.nodes.StockTransferOrder,
    user: xtremSystem.nodes.User,
) {
    const config = getMailerConfig(context);

    const documentNumber = transferOrder._id;
    const redirectUrl = `${config.redirectUrl}/${await transferOrder.page}`;
    const parametersApprovalDocument = Buffer.from(JSON.stringify({ _id: documentNumber })).toString('base64');
    const urlApprovalDocument = `${redirectUrl}/${parametersApprovalDocument}`;

    const { urlReject, urlApprove } = xtremMasterData.functions.generateUrl(redirectUrl, documentNumber);

    return {
        number: await transferOrder.number,
        requester: `${await user.lastName} ${await user.firstName}`,
        site: await (await transferOrder.site).name,
        returnRequestDate: (await transferOrder.date).toString(),
        urlApprovalDocument,
        urlReject,
        urlApprove,
    };
}
