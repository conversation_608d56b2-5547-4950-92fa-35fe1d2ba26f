import type { Collection, Context, UpdateAction } from '@sage/xtrem-core';
import { NodeStatus } from '@sage/xtrem-core';
import type * as xtremDistribution from '@sage/xtrem-distribution';
import * as xtremStockData from '@sage/xtrem-stock-data';
import * as xtremSupplyChain from '..';
import type { ShipmentToUpdate } from '../interfaces';

export async function updateStockTransferShipmentLineInTransitAfterReceiptStockCompleted(
    context: Context,
    stockTransferReceipt: xtremSupplyChain.nodes.StockTransferReceipt,
): Promise<void> {
    const shipmentDocumentNumber = await (
        await (
            await (await stockTransferReceipt.lines.elementAt(0)).linkToStockTransferShipmentLines.elementAt(0)
        ).to
    ).documentNumber;

    await context.bulkUpdate(xtremSupplyChain.nodes.StockTransferShipmentLineInTransit, {
        set: {
            endDate: await stockTransferReceipt.documentDate,
        },
        where: {
            line: { documentNumber: shipmentDocumentNumber },
        },
    });
}

async function calculateStatus(
    stockTransferReceiptLines: Collection<xtremSupplyChain.nodes.StockTransferReceiptLine>,
    stockTransactionStatus: xtremStockData.enums.StockDocumentTransactionStatus,
): Promise<xtremSupplyChain.enums.StockTransferReceiptStatus> {
    if ((await stockTransferReceiptLines.length) <= 0) {
        return 'readyToProcess';
    }
    if (stockTransactionStatus === 'completed') {
        return 'received';
    }
    return 'readyToProcess';
}

export function calculateReceiptDisplayStatus(
    status: xtremSupplyChain.enums.StockTransferReceiptStatus,
    stockTransactionStatus: xtremStockData.enums.StockDocumentTransactionStatus,
): xtremSupplyChain.enums.StockTransferReceiptDisplayStatus {
    if (stockTransactionStatus === 'error') {
        return 'error';
    }
    if (stockTransactionStatus === 'inProgress') {
        return 'postingInProgress';
    }
    if (stockTransactionStatus === 'completed') {
        return 'received';
    }
    return 'readyToProcess';
}

export async function calculateReceiptStatuses(
    stockTransferReceiptLines: Collection<xtremSupplyChain.nodes.StockTransferReceiptLine>,
    stockTransactionStatus: xtremStockData.enums.StockDocumentTransactionStatus,
) {
    const status = await calculateStatus(stockTransferReceiptLines, stockTransactionStatus);
    const displayStatus = calculateReceiptDisplayStatus(status, stockTransactionStatus);
    return {
        status,
        displayStatus,
    };
}

export async function getStockTransferShipment(
    context: Context,
    link: xtremSupplyChain.nodes.StockTransferReceiptLineToStockTransferShipmentLine,
): Promise<ShipmentToUpdate> {
    const stockTransferShipmentId = (await (await link.to).document)._id;
    const stockTransferShipmentLineId = (await link.to)._id;
    const stockTransferShipment = await context.read(
        xtremSupplyChain.nodes.StockTransferShipment,
        {
            _id: stockTransferShipmentId,
        },
        { forUpdate: true },
    );
    return { stockTransferShipment, stockTransferShipmentLineId };
}

export async function updateStockTransferShipmentLineAfterReceiptCreated(
    stockTransferReceiptLine: xtremSupplyChain.nodes.StockTransferReceiptLine,
) {
    await stockTransferReceiptLine.linkToStockTransferShipmentLines.forEach(async link => {
        const shipment: ShipmentToUpdate = await getStockTransferShipment(stockTransferReceiptLine.$.context, link);
        await shipment.stockTransferShipment.$.set({
            lines: [
                {
                    _action: 'update' as UpdateAction,
                    _id: shipment.stockTransferShipmentLineId,
                    status: 'shipped' as xtremSupplyChain.enums.StockTransferShipmentStatus,
                },
            ],
        });
        await shipment.stockTransferShipment.$.save();
    });
}

export async function updateStockTransferShipmentLineAfterReceiptDeleted(
    stockTransferReceiptLine: xtremSupplyChain.nodes.StockTransferReceiptLine,
) {
    await stockTransferReceiptLine.linkToStockTransferShipmentLines.forEach(async link => {
        const shipment: ShipmentToUpdate = await getStockTransferShipment(stockTransferReceiptLine.$.context, link);
        await shipment.stockTransferShipment.$.set({
            lines: [
                {
                    _action: 'update' as UpdateAction,
                    _id: shipment.stockTransferShipmentLineId,
                    status: 'readyToShip' as xtremSupplyChain.enums.StockTransferShipmentStatus,
                },
            ],
        });
        await shipment.stockTransferShipment.$.save();
    });
}

export async function updateStockTransferShipmentLineAfterReceiptPosted(
    stockTransferReceiptLine: xtremSupplyChain.nodes.StockTransferReceiptLine,
) {
    await stockTransferReceiptLine.linkToStockTransferShipmentLines.forEach(async link => {
        const shipment: ShipmentToUpdate = await getStockTransferShipment(stockTransferReceiptLine.$.context, link);
        await shipment.stockTransferShipment.$.set({
            lines: [
                {
                    _action: 'update' as UpdateAction,
                    _id: shipment.stockTransferShipmentLineId,
                    receivingStatus: 'received' as xtremDistribution.enums.ReceivingStatus,
                },
            ],
        });
        await shipment.stockTransferShipment.$.save();
    });
}

export async function transferShipmentLineAllocationsAfterReceiptCreatedOrOrderUpdated(
    stockTransferReceiptLine: xtremSupplyChain.nodes.StockTransferReceiptLine,
) {
    const quantityInStockUnit = await stockTransferReceiptLine.quantityInStockUnit;
    const oldQuantityInStockUnit =
        stockTransferReceiptLine.$.status === NodeStatus.modified
            ? await (
                  await stockTransferReceiptLine.$.old
              ).quantityInStockUnit
            : 0;
    if (
        stockTransferReceiptLine.$.status === NodeStatus.added ||
        (stockTransferReceiptLine.$.status === NodeStatus.modified && quantityInStockUnit - oldQuantityInStockUnit > 0)
    ) {
        await stockTransferReceiptLine.linkToStockTransferShipmentLines.forEach(async line => {
            const requiredQuantity =
                stockTransferReceiptLine.$.status === NodeStatus.added
                    ? quantityInStockUnit
                    : quantityInStockUnit - oldQuantityInStockUnit;

            const allocationPropositions = await xtremStockData.nodes.Stock.proposeAllocationsToTransfer(
                stockTransferReceiptLine.$.context,
                {
                    orderDocumentLine: await stockTransferReceiptLine.$.context.read(
                        xtremSupplyChain.nodes.StockTransferShipmentLine,
                        { _id: (await line.to)._id },
                        { forUpdate: true },
                    ),
                    requiredQuantity,
                    hasAllAllocationsReturned: false,
                },
            );
            if (allocationPropositions.length > 0) {
                await xtremStockData.nodes.Stock.updateAllocations(stockTransferReceiptLine.$.context, {
                    documentLine: line.to,
                    allocationUpdates: allocationPropositions.map(proposition => {
                        return {
                            action: 'transfer',
                            quantityToTransfer: proposition.quantityToTransfer,
                            allocationRecord:
                                proposition.allocationRecord as unknown as Promise<xtremStockData.nodes.StockAllocation>,
                            serialNumbers: proposition.serialNumbers,
                        };
                    }),
                });
            }
        });
    }
}
