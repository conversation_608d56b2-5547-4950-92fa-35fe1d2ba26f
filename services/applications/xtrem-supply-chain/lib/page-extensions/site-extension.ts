import type { Site } from '@sage/xtrem-master-data/build/lib/pages/site';
import type { GraphApi, Site as SiteNode, User } from '@sage/xtrem-system-api';
import * as ui from '@sage/xtrem-ui';
import { onChangeIsStockTransferOrderApprovalManaged } from '../client-functions/business-entity-site';

@ui.decorators.pageExtension<SiteExtension, SiteNode>({
    extends: '@sage/xtrem-master-data/Site',
})
export class SiteExtension extends ui.PageExtension<Site, GraphApi> {
    wasDirtyBeforeChanging: boolean;

    @ui.decorators.block<SiteExtension>({
        parent() {
            return this.managementSection;
        },
        title: 'Stock approval',
    })
    stockDefaultApproverBlock: ui.containers.Block;

    @ui.decorators.switchField<SiteExtension>({
        parent() {
            return this.stockDefaultApproverBlock;
        },
        title: 'Stock transfer',
        onClick() {
            this.wasDirtyBeforeChanging = this.$.isDirty;
        },
        async onChange() {
            await onChangeIsStockTransferOrderApprovalManaged(this, this.wasDirtyBeforeChanging);
        },
    })
    isStockTransferOrderApprovalManaged: ui.fields.Switch;

    @ui.decorators.referenceField<SiteExtension, User>({
        parent() {
            return this.stockDefaultApproverBlock;
        },
        title: 'Approver',
        node: '@sage/xtrem-system/User',
        tunnelPage: '@sage/xtrem-authorization/User',
        valueField: 'displayName',
        lookupDialogTitle: 'Select approver',
        minLookupCharacters: 2,
        width: 'large',
        columns: [
            ui.nestedFields.text({ title: 'Last name', bind: 'lastName' }),
            ui.nestedFields.text({ title: 'First name', bind: 'firstName' }),
            ui.nestedFields.text({ title: 'Email', bind: 'email' }),
        ],
        isReadOnly() {
            return !this.isStockTransferOrderApprovalManaged.value;
        },
    })
    stockTransferOrderDefaultApprover: ui.fields.Reference<User>;

    @ui.decorators.referenceField<SiteExtension, User>({
        parent() {
            return this.stockDefaultApproverBlock;
        },
        title: 'Substitute approver',
        node: '@sage/xtrem-system/User',
        tunnelPage: '@sage/xtrem-authorization/User',
        valueField: 'displayName',
        lookupDialogTitle: 'Select substitute approver',
        minLookupCharacters: 2,
        width: 'large',
        columns: [
            ui.nestedFields.text({ title: 'Last name', bind: 'lastName' }),
            ui.nestedFields.text({ title: 'First name', bind: 'firstName' }),
            ui.nestedFields.text({ title: 'Email', bind: 'email' }),
        ],
        isReadOnly() {
            return !this.isStockTransferOrderApprovalManaged.value;
        },
    })
    stockTransferOrderSubstituteApprover: ui.fields.Reference<User>;
}

declare module '@sage/xtrem-master-data/build/lib/pages/site' {
    export interface Site extends SiteExtension {}
}
