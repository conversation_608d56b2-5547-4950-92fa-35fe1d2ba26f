import { asyncArray } from '@sage/xtrem-async-helper';
import type * as LandedCostInterfaces from '@sage/xtrem-landed-cost/build/lib/client-functions/interfaces';
import {
    getQueryParameters,
    openLandedCostDocumentLinePickList,
} from '@sage/xtrem-landed-cost/build/lib/client-functions/landed-cost-allocation-helpers';
import type { LandedCostAllocationPanel as LandedCostAllocationPanelPage } from '@sage/xtrem-landed-cost/build/lib/pages/landed-cost-allocation-panel';
import type { StockDocumentTransactionStatus } from '@sage/xtrem-stock-data-api';
import * as PillColorStock from '@sage/xtrem-stock-data/build/lib/client-functions/pill-color';
import type { GraphApi } from '@sage/xtrem-supply-chain-api';
import * as ui from '@sage/xtrem-ui';
import * as displayButtons from '../client-functions/display-buttons-stock-transfer-landed-cost-allocation-panel';

@ui.decorators.pageExtension<LandedCostAllocationPanelExtension>({
    extends: '@sage/xtrem-landed-cost/LandedCostAllocationPanel',
    async onLoad() {
        if (this.$.isServiceOptionEnabled('landedCostStockTransferOption')) {
            this.initStockTransferExtension();
            this.manageDisplayButtonSelectFromReceiptAction();
            await this.setStockTransferStockTransactionStatus();
            this.headerBusinessActions.push(this.selectFromStockTransferReceipt);
        }
    },
})
export class LandedCostAllocationPanelExtension extends ui.PageExtension<LandedCostAllocationPanelPage, GraphApi> {
    panelPurchaseExtensionParameters: LandedCostInterfaces.LandedCostAllocationParameters;

    @ui.decorators.pageAction<LandedCostAllocationPanelExtension>({
        icon: 'search',
        title: 'Add lines from stock transfer receipts',
        async onClick() {
            await openLandedCostDocumentLinePickList(
                this,
                this.panelPurchaseExtensionParameters,
                'stockTransferReceipt',
            );
            await this.setStockTransferStockTransactionStatus();
        },
    })
    selectFromStockTransferReceipt: ui.PageAction;

    @ui.decorators.tableFieldOverride<LandedCostAllocationPanelExtension>({
        // TODO: XT-62301
        // replace the headerBusinessActions below with addItemActions
        // when we will be able to have both drop down action button + phantom row hidden
        // addItemActions() {
        //     return [this.selectFromStockTransferReceipt];
        // },
        columns: [
            ui.nestedFieldExtensions.label({
                title: 'Stock status',
                bind: 'stockTransactionStatus',
                insertAfter: 'distributionValue',
                style: (_id, rowData) =>
                    PillColorStock.getLabelColorByStatus(
                        'StockDocumentTransactionStatus',
                        rowData?.stockTransactionStatus,
                    ),
            }),
        ],
    })
    lines: ui.fields.Table<
        LandedCostInterfaces.LandedCostAllocationPageBinding & {
            stockTransactionStatus?: StockDocumentTransactionStatus;
        }
    >;

    private initStockTransferExtension() {
        const queryParams = getQueryParameters(this.$.queryParameters.args);
        if (!queryParams) {
            throw new Error('Parameters are missing.');
        }
        this.panelPurchaseExtensionParameters = queryParams;
    }

    private manageDisplayButtonSelectFromReceiptAction() {
        this.selectFromStockTransferReceipt.isHidden = displayButtons.isHiddenButtonSelectFromReceiptAction({
            parameters: { isEditable: this.panelPurchaseExtensionParameters.isEditable },
        });
        this.selectFromStockTransferReceipt.isDisabled = displayButtons.isDisabledButtonSelectFromReceiptAction({
            parameters: { isEditable: this.panelPurchaseExtensionParameters.isEditable },
        });
    }

    async setStockTransferStockTransactionStatus() {
        await asyncArray(this.lines.value)
            .filter(line => line.allocatedDocumentType === 'stockTransferReceipt' && !line.stockTransactionStatus)
            .forEach(async line => {
                const receiptLineId = line.allocatedDocumentLine?._id;
                if (receiptLineId) {
                    const receiptLine = await this.$.graph
                        .node('@sage/xtrem-supply-chain/StockTransferReceiptLine')
                        .read({ stockTransactionStatus: true }, `${receiptLineId}`)
                        .execute();
                    if (receiptLine.stockTransactionStatus) {
                        line.stockTransactionStatus = receiptLine.stockTransactionStatus;
                        this.lines.addOrUpdateRecordValue(line);
                    }
                }
            });
    }
}

declare module '@sage/xtrem-landed-cost/build/lib/pages/landed-cost-allocation-panel' {
    interface LandedCostAllocationPanel extends ui.PageExtension<LandedCostAllocationPanelPage, GraphApi> {}
}
