import type { Filter, Selector } from '@sage/xtrem-client';
import { extractEdges, querySelector } from '@sage/xtrem-client';
import type * as LandedCostInterfaces from '@sage/xtrem-landed-cost/build/lib/client-functions/interfaces';
import { clearNestedGrid } from '@sage/xtrem-landed-cost/build/lib/client-functions/landed-cost-allocation-helpers';
import type { LandedCostDocumentLinePickList as LandedCostDocumentLinePickListPage } from '@sage/xtrem-landed-cost/build/lib/pages/landed-cost-document-line-pick-list';
import * as PillColorCommon from '@sage/xtrem-master-data/build/lib/client-functions/pill-color';
import type {
    GraphApi,
    StockTransferOrder,
    StockTransferOrderLine,
    StockTransferReceipt,
    StockTransferReceiptLine,
} from '@sage/xtrem-supply-chain-api';
import type { Site } from '@sage/xtrem-system-api';
import * as ui from '@sage/xtrem-ui';
import type { ExtensionMembers } from '@sage/xtrem-ui/build/lib/service/page-extension';
import type * as StockTransferClientInterfaces from '../client-functions/interfaces/landed-cost';
import * as pageFunctions from '../client-functions/landed-cost';

@ui.decorators.pageExtension<LandedCostDocumentLinePickListExtension>({
    extends: '@sage/xtrem-landed-cost/LandedCostDocumentLinePickList',
    onLoad() {
        this.initSupplyChainExtension();
    },
})
export class LandedCostDocumentLinePickListExtension extends ui.PageExtension<
    LandedCostDocumentLinePickListPage,
    GraphApi
> {
    isStockTransferSelectionActive: boolean;

    stockTransferReceiptFilter: Filter<StockTransferReceipt>;

    stockTransferOrderFilter: Filter<StockTransferOrder>;

    @ui.decorators.referenceField<LandedCostDocumentLinePickListExtension, StockTransferReceipt>({
        parent() {
            return this.lineBlock;
        },
        title: 'Stock transfer receipt',
        lookupDialogTitle: 'Select stock transfer receipt',
        node: '@sage/xtrem-supply-chain/StockTransferReceipt',
        valueField: 'number',
        minLookupCharacters: 3,
        width: 'small',
        insertBefore() {
            return this.item ?? null;
        },
        isHidden() {
            return !this.isSelectingStockTransferReceipt();
        },
        onChange() {
            this.stockTransferOrder.value = null;
            clearNestedGrid(this);
        },
        filter() {
            return this.stockTransferReceiptFilter;
        },
        columns: [
            ui.nestedFields.text({ title: 'Number', bind: 'number' }),
            ui.nestedFields.reference<LandedCostDocumentLinePickListExtension, StockTransferReceipt, Site>({
                title: 'Shipping site',
                bind: 'shippingSite',
                tunnelPage: '@sage/xtrem-master-data/Site',
            }),
            ui.nestedFields.label({
                bind: 'displayStatus',
                title: 'Status',
                optionType: '@sage/xtrem-supply-chain/StockTransferReceiptDisplayStatus',
                backgroundColor: value => PillColorCommon.getDisplayStatusPillFeatures(value).backgroundColor,
                borderColor: value => PillColorCommon.getDisplayStatusPillFeatures(value).borderColor,
                color: value => PillColorCommon.getDisplayStatusPillFeatures(value).textColor,
            }),
            ui.nestedFields.reference<LandedCostDocumentLinePickListExtension, StockTransferReceipt, Site>({
                title: 'Receiving site',
                bind: 'site',
                tunnelPage: '@sage/xtrem-master-data/Site',
            }),
        ],
    })
    stockTransferReceipt: ui.fields.Reference<StockTransferReceipt>;

    @ui.decorators.referenceField<LandedCostDocumentLinePickListExtension, StockTransferOrder>({
        parent() {
            return this.lineBlock;
        },
        title: 'Stock transfer order',
        lookupDialogTitle: 'Select stock transfer order',
        node: '@sage/xtrem-supply-chain/StockTransferOrder',
        valueField: 'number',
        minLookupCharacters: 3,
        width: 'small',
        insertBefore() {
            return this.item ?? null;
        },
        isHidden() {
            return !this.isSelectingStockTransferReceipt();
        },
        onChange() {
            this.stockTransferReceipt.value = null;
            clearNestedGrid(this);
        },
        filter() {
            return this.stockTransferOrderFilter;
        },
        columns: [
            ui.nestedFields.text({ title: 'Number', bind: 'number' }),
            ui.nestedFields.reference<LandedCostDocumentLinePickListExtension, StockTransferOrder, Site>({
                title: 'Shipping site',
                bind: 'site',
                tunnelPage: '@sage/xtrem-master-data/Site',
            }),
            ui.nestedFields.label({
                bind: 'displayStatus',
                title: 'Status',
                optionType: '@sage/xtrem-supply-chain/StockTransferOrderDisplayStatus',
                backgroundColor: value => PillColorCommon.getDisplayStatusPillFeatures(value).backgroundColor,
                borderColor: value => PillColorCommon.getDisplayStatusPillFeatures(value).borderColor,
                color: value => PillColorCommon.getDisplayStatusPillFeatures(value).textColor,
            }),
            ui.nestedFields.reference<LandedCostDocumentLinePickListExtension, StockTransferOrder, Site>({
                title: 'Receiving site',
                bind: 'receivingSite',
                tunnelPage: '@sage/xtrem-master-data/Site',
            }),
        ],
    })
    stockTransferOrder: ui.fields.Reference<StockTransferOrder>;

    @ui.decorators.buttonField<LandedCostDocumentLinePickListExtension>({
        parent() {
            return this.lineBlock;
        },
        map() {
            return ui.localize('@sage/xtrem-supply-chain/pages__document_line_landed_cost_pick_list__search', 'Search');
        },
        isHidden() {
            return !this.isSelectingStockTransferReceipt();
        },
        async onClick() {
            this.$.loader.isHidden = false;
            clearNestedGrid(this);
            await this.searchStockTransferDocument();
            this.$.loader.isHidden = true;
        },
    })
    searchStockTransferButton: ui.fields.Button;

    @ui.decorators.nestedGridFieldOverride<
        LandedCostDocumentLinePickListExtension,
        [
            StockTransferClientInterfaces.StockTransferReceiptNestedGrid,
            StockTransferClientInterfaces.StockTransferReceiptLineNestedGrid,
        ]
    >({
        levels: [
            {
                columns: [
                    ui.nestedFieldExtensions.reference<
                        LandedCostDocumentLinePickListExtension,
                        StockTransferClientInterfaces.StockTransferReceiptNestedGrid,
                        StockTransferReceipt['supplier']
                    >({
                        title: 'Supplier',
                        bind: 'supplier',
                        node: '@sage/xtrem-master-data/Supplier',
                        valueField: 'name',
                        tunnelPage: '@sage/xtrem-master-data/Supplier',
                        insertAfter: 'number',
                        isHidden() {
                            return !this.isStockTransferSelectionActive;
                        },
                    }),
                    ui.nestedFieldExtensions.label({
                        title: 'Status',
                        bind: 'displayStatus',
                        insertAfter: 'number',
                        optionType: '@sage/xtrem-supply-chain/StockTransferReceiptDisplayStatus',
                        isHidden() {
                            return !this.isSelectingStockTransferReceipt();
                        },
                        style: (_id, rowData) => PillColorCommon.getDisplayStatusPillFeatures(rowData?.displayStatus),
                    }),
                ],
            },
            {},
        ],
    })
    documents: ui.fields.NestedGrid<
        [LandedCostInterfaces.DocumentNestedGrid, LandedCostInterfaces.DocumentLineNestedGrid],
        LandedCostDocumentLinePickListPage
    >;

    isSelectingStockTransferReceipt() {
        return this.getThis().documentType.value === 'stockTransferReceipt';
    }

    initSupplyChainExtension() {
        this.isStockTransferSelectionActive = this.isSelectingStockTransferReceipt();

        const commonFilter = {
            site: { legalCompany: { _id: { _eq: this.getThis().companyId.value } } },
            financialSite: { _id: { _eq: this.getThis().siteId.value } },
            lines: { _atLeast: 1 },
        };

        this.stockTransferReceiptFilter = {
            ...commonFilter,
            ...(this.isSelectingStockTransferReceipt() && {
                status: 'received',
            }),
        };

        this.stockTransferOrderFilter = {
            ...commonFilter,
            ...(this.isSelectingStockTransferReceipt() && {
                receivingStatus: 'received',
            }),
        };
    }

    async searchStockTransferDocument() {
        const filteredStockTransferDocuments = await this.getStockTransferDocuments();

        this.documents.value = filteredStockTransferDocuments.filter(
            receipt => receipt.lines?.length > 0,
        ) as unknown as StockTransferClientInterfaces.StockTransferReceiptNestedGrid[];

        if (!filteredStockTransferDocuments.length) {
            this.$.showToast(
                ui.localize(
                    '@sage/xtrem-supply-chain/pages__document_line_landed_cost_pick_list___no_results',
                    'No results found.',
                ),
                { type: 'warning' },
            );
        }
    }

    async getStockTransferDocuments() {
        const selector = this.getStockTransferSelector();
        const headerFilter = await this.getStockTransferHeaderFilter();

        let nodeName: any = '@sage/xtrem-supply-chain/StockTransferOrder';
        if (this.isSelectingStockTransferReceipt()) {
            nodeName = '@sage/xtrem-supply-chain/StockTransferReceipt';
        }

        return extractEdges<Partial<StockTransferReceipt | StockTransferOrder>>(
            await this.$.graph
                .node(nodeName)
                .query(
                    ui.queryUtils.edgesSelector<StockTransferReceipt | StockTransferOrder>(selector, {
                        first: 1000,
                        filter: headerFilter,
                        orderBy: { number: +1 },
                    }),
                )
                .execute(),
        );
    }

    // eslint-disable-next-line class-methods-use-this
    getStockTransferCommonHeaderFilter(): Filter<StockTransferOrder | StockTransferReceipt> {
        return {};
    }

    async getStockTransferHeaderFilter(): Promise<Filter<StockTransferReceipt>> {
        if (!this.isSelectingStockTransferReceipt()) {
            return {};
        }

        return {
            ...(this.getStockTransferCommonHeaderFilter() as Filter<StockTransferReceipt>),
            ...this.stockTransferReceiptFilter,
            ...(this.stockTransferReceipt.value ? { _id: this.stockTransferReceipt.value._id } : {}),
            ...(this.stockTransferOrder.value?._id
                ? {
                      _id: {
                          _in: await pageFunctions.getStockTransferReceiptsFromStockTransferOrder(
                              this,
                              this.stockTransferOrder.value._id,
                          ),
                      },
                  }
                : {}),
        };
    }

    getStockTransferCommonLineFilter(): Filter<StockTransferOrderLine | StockTransferReceiptLine> {
        return {
            _id: { _nin: JSON.parse(this.getThis().alreadyPickedLineIds.value ?? '[]') },
            item: { isStockManaged: true },
            ...(this.getThis().item?.value ? { item: { _id: this.getThis().item?.value?._id ?? '' } } : {}),
        };
    }

    getOrderLineFilter(): Filter<StockTransferOrderLine> {
        return {
            ...(this.getStockTransferCommonLineFilter() as Filter<StockTransferOrderLine>),
        };
    }

    getReceiptLineFilter(): Filter<StockTransferReceiptLine> {
        if (!this.isSelectingStockTransferReceipt()) {
            return {};
        }

        return {
            ...(this.getStockTransferCommonLineFilter() as Filter<StockTransferReceiptLine>),
        };
    }

    getStockTransferSelector(): Selector<StockTransferOrder | StockTransferReceipt> {
        const lineFilter = this.isSelectingStockTransferReceipt()
            ? this.getReceiptLineFilter()
            : this.getOrderLineFilter();

        return {
            _id: true,
            number: true,
            ...(this.isSelectingStockTransferReceipt() ? { displayStatus: true, stockTransactionStatus: true } : {}),
            supplier: { name: true },
            lines: querySelector(
                {
                    _id: true,
                    _sortValue: true,
                    item: {
                        id: true,
                        name: true,
                        weight: true,
                        volume: true,
                        stockUnit: { _id: true, id: true, symbol: true, decimalDigits: true },
                        weightUnit: { _id: true },
                        volumeUnit: { _id: true },
                    },
                    quantity: true,
                    ...(this.isSelectingStockTransferReceipt() ? { stockTransactionStatus: true } : {}),
                    quantityInStockUnit: true,
                    unit: { id: true, symbol: true, decimalDigits: true },
                    document: { _id: true, number: true },
                },
                { first: 1000, filter: lineFilter },
            ),
        };
    }

    getThis() {
        return this as unknown as ExtensionMembers<
            LandedCostDocumentLinePickListExtension & LandedCostDocumentLinePickListPage
        >;
    }
}

declare module '@sage/xtrem-landed-cost/build/lib/pages/landed-cost-document-line-pick-list' {
    interface LandedCostDocumentLinePickList extends LandedCostDocumentLinePickListExtension {}
}
