import type { WorkOrderCategory } from '@sage/xtrem-manufacturing-api';
import * as manufacturingPillColor from '@sage/xtrem-manufacturing/build/lib/client-functions/pill-color';
import type { Item, ItemSite, PreferredProcess, UnitOfMeasure } from '@sage/xtrem-master-data-api';
import * as masterDataUtils from '@sage/xtrem-master-data/build/lib/client-functions/utils';
import { manufacturing } from '@sage/xtrem-master-data/build/lib/menu-items/manufacturing';
import type {
    GraphApi,
    SupplyPlanning,
    SupplyPlanningSource,
    SupplyPlanningStatus,
} from '@sage/xtrem-supply-chain-api';
import type { Company, Site, User } from '@sage/xtrem-system-api';
import { setDisplayOfCommonPageActions } from '@sage/xtrem-system/build/lib/client-functions/applicative-crud-actions';
import * as ui from '@sage/xtrem-ui';
import * as pillColorDocument from '../client-functions/pill-color';

@ui.decorators.page<WorkOrderPlanning, SupplyPlanning>({
    title: 'Work order planning',
    mode: 'default',
    node: '@sage/xtrem-supply-chain/SupplyPlanning',

    menuItem: manufacturing,
    priority: 500,

    navigationPanel: {
        bulkActions: [
            {
                mutation: 'createWorkOrder',
                title: 'Create order',
                icon: 'none',
                buttonType: 'primary',
                isDestructive: false,
            },
        ],
        optionsMenu: [
            {
                title: 'All',
                graphQLFilter: { type: { _in: ['manufactured', null] } },
            },
        ],
        listItem: {
            title: ui.nestedFields.text<WorkOrderPlanning, SupplyPlanning>({
                bind: { itemSite: { site: { legalCompany: { id: true } } } },
                title: 'Company',
            }),
            companyName: ui.nestedFields.reference<WorkOrderPlanning, SupplyPlanning, Company>({
                bind: { itemSite: { site: { legalCompany: true } } },
                node: '@sage/xtrem-system/Company',
                valueField: 'name',
                tunnelPage: undefined,
                title: 'Company name',
            }),
            titleRight: ui.nestedFields.text<WorkOrderPlanning, SupplyPlanning>({
                bind: { itemSite: { site: { id: true } } },
                title: 'Site',
            }),
            siteName: ui.nestedFields.reference<WorkOrderPlanning, SupplyPlanning, Site>({
                bind: { itemSite: { site: true } },
                node: '@sage/xtrem-system/Site',
                tunnelPage: undefined,
                valueField: 'name',
                title: 'Site name',
            }),
            itemName: ui.nestedFields.reference<WorkOrderPlanning, SupplyPlanning, Item>({
                bind: { itemSite: { item: true } },
                node: '@sage/xtrem-master-data/Item',
                valueField: 'name',
                tunnelPage: undefined,
                title: 'Item',
            }),
            line2: ui.nestedFields.text<WorkOrderPlanning, SupplyPlanning>({
                bind: { itemSite: { item: { id: true } } },
                title: 'Item ID',
            }),
            itemDescription: ui.nestedFields.text<WorkOrderPlanning, SupplyPlanning>({
                bind: { itemSite: { item: { description: true } } },
                title: 'Item description',
                isHiddenOnMainField: true,
            }),
            stockUnitDecimalDigits: ui.nestedFields.technical<WorkOrderPlanning, SupplyPlanning, UnitOfMeasure>({
                bind: { stockUnit: { decimalDigits: true } },
            }),
            stockUnitSymbol: ui.nestedFields.technical<WorkOrderPlanning, SupplyPlanning, UnitOfMeasure>({
                bind: { stockUnit: { symbol: true } },
            }),
            stockQuantity: ui.nestedFields.numeric<WorkOrderPlanning, SupplyPlanning>({
                bind: 'stockQuantity',
                title: 'Stock quantity',
                scale(_recordID, recordData) {
                    return masterDataUtils.getScaleValue(2, recordData?.stockUnit?.decimalDigits);
                },
                postfix(_recordID, recordData) {
                    return recordData?.stockUnit?.symbol || '';
                },
            }),
            startDate: ui.nestedFields.date<WorkOrderPlanning, SupplyPlanning>({
                bind: 'startDate',
                title: 'Start date',
            }),
            requirementDate: ui.nestedFields.date<WorkOrderPlanning, SupplyPlanning>({
                bind: 'requirementDate',
                title: 'End date',
            }),
            preferredProcess: ui.nestedFields.label<WorkOrderPlanning, SupplyPlanning>({
                bind: { itemSite: { preferredProcess: true } },
                optionType: '@sage/xtrem-supply-chain/PreferredProcess',
                title: 'Preferred process',
                backgroundColor: value => pillColorDocument.setEnumPreferredProcessColor(value, 'backgroundColor'),
                borderColor: value => pillColorDocument.setEnumPreferredProcessColor(value, 'borderColor'),
                color: value => pillColorDocument.setEnumPreferredProcessColor(value, 'textColor'),
            }),
            workOrderCategory: ui.nestedFields.reference<WorkOrderPlanning, SupplyPlanning, WorkOrderCategory>({
                bind: 'workOrderCategory',
                node: '@sage/xtrem-manufacturing/WorkOrderCategory',
                valueField: 'name',
                tunnelPage: undefined,
                title: 'Category',
            }),
            workOrderType: ui.nestedFields.label<WorkOrderPlanning, SupplyPlanning>({
                bind: 'workOrderType',
                optionType: '@sage/xtrem-manufacturing/WorkOrderType',
                title: 'Type',
                style: (_id, rowData) =>
                    manufacturingPillColor.getLabelColorByStatus('WorkOrderType', rowData.workOrderType),
            }),
            suggestionSource: ui.nestedFields.label<WorkOrderPlanning, SupplyPlanning>({
                bind: 'suggestionSource',
                optionType: '@sage/xtrem-supply-chain/SupplyPlanningSource',
                title: 'Suggestion source',
                backgroundColor: value => pillColorDocument.setEnumSupplyPlanningSourceColor(value, 'backgroundColor'),
                borderColor: value => pillColorDocument.setEnumSupplyPlanningSourceColor(value, 'borderColor'),
                color: value => pillColorDocument.setEnumSupplyPlanningSourceColor(value, 'textColor'),
            }),
            suggestionDate: ui.nestedFields.date<WorkOrderPlanning, SupplyPlanning>({
                bind: 'suggestionDate',
                title: 'Suggestion date',
            }),
        },
    },

    businessActions() {
        return [this.$standardCancelAction, this.save];
    },

    onDirtyStateUpdated(isDirty: boolean) {
        setDisplayOfCommonPageActions({
            page: this,
            isDirty,
            cancel: this.$standardCancelAction,
            save: this.save,
        });
    },
    onLoad() {
        setDisplayOfCommonPageActions({
            page: this,
            isDirty: false,
            cancel: this.$standardCancelAction,
            save: this.save,
        });
    },
})
export class WorkOrderPlanning extends ui.Page<GraphApi, SupplyPlanning> {
    @ui.decorators.section<WorkOrderPlanning>({
        isTitleHidden: true,
        title: 'General',
    })
    mainSection: ui.containers.Section;

    @ui.decorators.block<WorkOrderPlanning>({
        parent() {
            return this.mainSection;
        },
    })
    mainBlock: ui.containers.Block;

    @ui.decorators.labelField<WorkOrderPlanning>({
        bind: { itemSite: { preferredProcess: true } },
        optionType: '@sage/xtrem-master-data/PreferredProcess',
        title: 'Preferred process',
        parent() {
            return this.mainBlock;
        },
        backgroundColor(value: PreferredProcess) {
            return pillColorDocument.setEnumPreferredProcessColor(value, 'backgroundColor');
        },
        borderColor(value: PreferredProcess) {
            return pillColorDocument.setEnumPreferredProcessColor(value, 'borderColor');
        },
        color(value: PreferredProcess) {
            return pillColorDocument.setEnumPreferredProcessColor(value, 'textColor');
        },
    })
    preferredProcess: ui.fields.Label;

    @ui.decorators.referenceField<WorkOrderPlanning, Company>({
        bind: { itemSite: { site: { legalCompany: true } } },
        node: '@sage/xtrem-system/Company',
        valueField: 'name',
        helperTextField: 'id',
        tunnelPage: '@sage/xtrem-master-data/Company',
        isReadOnly: true,
        title: 'Company',
        columns: [
            ui.nestedFields.text({ bind: 'name' }),
            ui.nestedFields.text({ bind: 'id', title: 'ID ' }),
            ui.nestedFields.technical({ bind: 'priceScale' }),
        ],
        parent() {
            return this.mainBlock;
        },
    })
    company: ui.fields.Reference<Company>;

    @ui.decorators.referenceField<WorkOrderPlanning, Site>({
        bind: { itemSite: { site: true } },
        isReadOnly: true,
        title: 'Stock site',
        parent() {
            return this.mainBlock;
        },
    })
    stockSite: ui.fields.Reference<Site>;

    @ui.decorators.referenceField<WorkOrderPlanning, Item>({
        bind: { itemSite: { item: true } },
        node: '@sage/xtrem-master-data/Item',
        valueField: 'name',
        helperTextField: 'id',
        tunnelPage: '@sage/xtrem-master-data/Item',
        isReadOnly: true,
        title: 'Item',
        parent() {
            return this.mainBlock;
        },
    })
    item: ui.fields.Reference<Item>;

    // TODO: to be removed as soon as this is available XT-65704
    @ui.decorators.referenceField<WorkOrderPlanning, ItemSite>({
        bind: 'itemSite',
        node: '@sage/xtrem-master-data/ItemSite',
        valueField: 'id',
    })
    itemSite: ui.fields.Reference<ItemSite>;

    @ui.decorators.numericField<WorkOrderPlanning>({
        title: 'Stock quantity',
        parent() {
            return this.mainBlock;
        },
        scale() {
            return masterDataUtils.getScaleValue(2, this.stockUnit.value?.decimalDigits);
        },
        postfix() {
            return this.stockUnit.value?.symbol || '';
        },
        validation(val?: number) {
            if (val !== undefined && val <= 0) {
                return ui.localize(
                    '@sage/xtrem-supply-chain/pages__work_order_planning__stock_quantity_zero_or_less',
                    'The stock quantity cannot be less than or equal to 0.',
                );
            }
            return undefined;
        },
    })
    stockQuantity: ui.fields.Numeric;

    @ui.decorators.referenceField<WorkOrderPlanning, UnitOfMeasure>({
        isReadOnly: true,
        columns: [
            ui.nestedFields.text({ title: 'Name', bind: 'name', canFilter: false }),
            ui.nestedFields.text({ bind: 'id', isHidden: true }),
            ui.nestedFields.text({ bind: 'symbol', title: 'Symbol' }),
            ui.nestedFields.technical({ bind: 'decimalDigits' }),
        ],
        parent() {
            return this.mainBlock;
        },
    })
    stockUnit: ui.fields.Reference<UnitOfMeasure>;

    @ui.decorators.dateField<WorkOrderPlanning>({
        title: 'Start date',
        isMandatory: true,
        minDate: new Date(),
        parent() {
            return this.mainBlock;
        },
    })
    startDate: ui.fields.Date;

    @ui.decorators.dateField<WorkOrderPlanning>({
        title: 'End date',
        isMandatory: true,
        isReadOnly: true,
        parent() {
            return this.mainBlock;
        },
    })
    requirementDate: ui.fields.Date;

    @ui.decorators.referenceField<WorkOrderPlanning, WorkOrderCategory>({
        title: 'Category',
        node: '@sage/xtrem-manufacturing/WorkOrderCategory',
        lookupDialogTitle: 'Select category',
        valueField: 'name',
        helperTextField: 'id',
        isMandatory: true,
        parent() {
            return this.mainBlock;
        },
        columns: [
            ui.nestedFields.text({ title: 'Name', bind: 'name' }),
            ui.nestedFields.text({ title: 'ID', bind: 'id' }),
            ui.nestedFields.checkbox({ title: 'Routing', bind: 'routing' }),
            ui.nestedFields.checkbox({ title: 'Bill of material', bind: 'billOfMaterial' }),
        ],
    })
    workOrderCategory: ui.fields.Reference<WorkOrderCategory>;

    @ui.decorators.dropdownListField<WorkOrderPlanning>({
        title: 'Type',
        optionType: '@sage/xtrem-manufacturing/WorkOrderType',
        isMandatory: true,
        parent() {
            return this.mainBlock;
        },
    })
    workOrderType: ui.fields.DropdownList;

    @ui.decorators.textField<WorkOrderPlanning>({
        title: 'Name',
        parent() {
            return this.mainBlock;
        },
    })
    workOrderName: ui.fields.Text;

    @ui.decorators.textField<WorkOrderPlanning>({
        title: 'Work order number',
        parent() {
            return this.mainBlock;
        },
    })
    workOrderNumber: ui.fields.Text;

    @ui.decorators.labelField<WorkOrderPlanning>({
        title: 'Create order status',
        optionType: '@sage/xtrem-supply-planning/SupplyPlanningStatus',
        parent() {
            return this.mainBlock;
        },
        backgroundColor(value: SupplyPlanningStatus) {
            return pillColorDocument.setEnumSupplyPlanningStatusColor(value, 'backgroundColor');
        },
        borderColor(value: SupplyPlanningStatus) {
            return pillColorDocument.setEnumSupplyPlanningStatusColor(value, 'borderColor');
        },
        color(value: SupplyPlanningStatus) {
            return pillColorDocument.setEnumSupplyPlanningStatusColor(value, 'textColor');
        },
    })
    status: ui.fields.Label;

    @ui.decorators.labelField<WorkOrderPlanning>({
        title: 'Suggestion source',
        optionType: '@sage/xtrem-supply-planning/SupplyPlanningSource',
        parent() {
            return this.mainBlock;
        },
        backgroundColor(value: SupplyPlanningSource) {
            return pillColorDocument.setEnumSupplyPlanningSourceColor(value, 'backgroundColor');
        },
        borderColor(value: SupplyPlanningSource) {
            return pillColorDocument.setEnumSupplyPlanningSourceColor(value, 'borderColor');
        },
        color(value: SupplyPlanningSource) {
            return pillColorDocument.setEnumSupplyPlanningSourceColor(value, 'textColor');
        },
    })
    suggestionSource: ui.fields.Label;

    @ui.decorators.dateField<WorkOrderPlanning>({
        isReadOnly: true,
        title: 'Suggestion date',
        parent() {
            return this.mainBlock;
        },
    })
    suggestionDate: ui.fields.Date;

    @ui.decorators.referenceField<WorkOrderPlanning, User>({
        node: '@sage/xtrem-system/User',
        valueField: 'displayName',
        isReadOnly: true,
        title: 'Suggestion user',
        parent() {
            return this.mainBlock;
        },
    })
    suggestionUser: ui.fields.Reference<User>;

    @ui.decorators.pageAction<WorkOrderPlanning>({
        title: 'Save',
        buttonType: 'primary',
        access: { bind: '$update' },
        async onClick() {
            await this.$standardSaveAction.execute();
        },
    })
    save: ui.PageAction;
}
