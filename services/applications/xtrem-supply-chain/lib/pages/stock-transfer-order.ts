import type { ExtractEdgesPartial, Logical } from '@sage/xtrem-client';
import { DateValue } from '@sage/xtrem-date-time';
import type { ReceivingStatus, ShippingStatus } from '@sage/xtrem-distribution-api';
import * as commonDistribution from '@sage/xtrem-distribution/build/lib/client-functions/common';
import * as actionsFunctionsDistribution from '@sage/xtrem-distribution/build/lib/client-functions/document-actions-functions';
import * as pillColorDocument from '@sage/xtrem-distribution/build/lib/client-functions/pill-color';
import * as attributesAndDimensions from '@sage/xtrem-finance-data/build/lib/client-functions/attributes-and-dimensions';
import { initDefaultDimensions } from '@sage/xtrem-finance-data/build/lib/client-functions/attributes-and-dimensions';
import * as dimensionPanelHelpers from '@sage/xtrem-finance-data/build/lib/client-functions/dimension-panel-helpers';
import type * as financeInterfaces from '@sage/xtrem-finance-data/build/lib/client-functions/interfaces/dimension';
import type * as LandedCostInterfaces from '@sage/xtrem-landed-cost/build/lib/client-functions/interfaces';
import type {
    Address,
    BaseStatus,
    BusinessEntity,
    BusinessEntityAddress,
    BusinessEntityContact,
    Contact,
    Currency,
    Customer,
    DeliveryDetail,
    DeliveryMode,
    Incoterm,
    Item,
    Supplier,
    UnitOfMeasure,
} from '@sage/xtrem-master-data-api';
import {
    getCountryPostCodeTitle,
    getCountryRegionTitle,
} from '@sage/xtrem-master-data/build/lib/client-functions/country-fields-title';
import { validCapabilities } from '@sage/xtrem-master-data/build/lib/client-functions/fields-properties';
import { checkStockSite } from '@sage/xtrem-master-data/build/lib/client-functions/page-functions';
import * as pillColorCommon from '@sage/xtrem-master-data/build/lib/client-functions/pill-color';
import * as MasterDataUtils from '@sage/xtrem-master-data/build/lib/client-functions/utils';
import { getConcatenatedAddress } from '@sage/xtrem-master-data/build/lib/shared-functions/address-functions';
import { scaleOfCurrent } from '@sage/xtrem-master-data/build/lib/shared-functions/common';
import type { AllocationRequestStatus, StockAllocationStatus } from '@sage/xtrem-stock-data-api';
import * as PillColorStock from '@sage/xtrem-stock-data/build/lib/client-functions/pill-color';
import type { Country, Legislation } from '@sage/xtrem-structure-api';
import type {
    GraphApi,
    StockTransferOrderLine,
    StockTransferOrderLineBinding,
    StockTransferOrder as StockTransferOrderNode,
    StockTransferReceiptLine,
    StockTransferShipment,
    StockTransferShipmentLine,
} from '@sage/xtrem-supply-chain-api';
import type { Company, Site, User } from '@sage/xtrem-system-api';
import {
    setOrderOfPageBusinessActions,
    setOrderOfPageHeaderDropDownActions,
    setOrderOfPageHeaderQuickActions,
    setOrderOfPageTableHeaderBusinessActions,
} from '@sage/xtrem-system/build/lib/client-functions/applicative-crud-actions';
import * as MainListActions from '@sage/xtrem-system/build/lib/client-functions/main-list-actions';
import { validEmail } from '@sage/xtrem-system/build/lib/shared-functions/email-validation';
import * as ui from '@sage/xtrem-ui';
import * as pageFunctions from '../client-functions/stock-transfer-order';
import * as actionsFunctions from '../client-functions/stock-transfer-order-actions-functions';
import { getSiteApprovers } from '../client-functions/stock-transfer-order-actions-functions';
import * as displayButtons from '../client-functions/stock-transfer-order-display-buttons';
import * as displayButtonsManagement from '../client-functions/stock-transfer-order-display-buttons-management';
import * as stepSequence from '../client-functions/stock-transfer-order-step-sequence';
import { stockTransferMenu } from '../menu-items/stock-transfer';

@ui.decorators.page<StockTransferOrder, StockTransferOrderNode>({
    title: 'Stock transfer order',
    objectTypeSingular: 'Stock transfer order',
    objectTypePlural: 'Stock transfer orders',
    idField() {
        return this.number;
    },
    menuItem: stockTransferMenu,
    module: 'supply-chain',
    node: '@sage/xtrem-supply-chain/StockTransferOrder',
    hasAttachmentsSection: true,
    mode: 'tabs',
    priority: 100,
    headerLabel() {
        return this.displayStatus;
    },
    createAction() {
        return this.$standardNewAction;
    },
    businessActions() {
        return setOrderOfPageBusinessActions({
            save: this.save,
            cancel: this.$standardCancelAction,
            businessActions: [
                this.requestApproval,
                this.approve,
                this.reject,
                this.confirm,
                this.ship,
                this.close,
                this.open,
            ],
        });
    },
    headerQuickActions() {
        return setOrderOfPageHeaderQuickActions({
            duplicate: [],
            quickActions: [],
        });
    },
    headerDropDownActions() {
        return setOrderOfPageHeaderDropDownActions({
            remove: [this.deleteStockTransferOrder],
            dropDownBusinessActions: [
                this.$standardOpenRecordHistoryAction,
                this.$standardOpenCustomizationPageWizardAction,
                ui.menuSeparator(),
                this.defaultDimension,
                ui.menuSeparator(),
                // this.requestDeallocation, // isHidden until XT-82150
                // this.requestAllocation, // isHidden until XT-82150
                this.sendEmail,
            ],
        });
    },
    headerSection() {
        return this.headerSection;
    },
    onDirtyStateUpdated(isDirty: boolean) {
        if (this.status.value && this.approvalStatus.value && this.shippingStatus.value && this.receivingStatus.value) {
            this.orderStepSequence.statuses = stepSequence.getDisplayStatusStepSequence(
                this.$.recordId,
                this.status.value,
                this.approvalStatus.value,
                this.shippingStatus.value,
                this.receivingStatus.value,
            );
        }
        displayButtonsManagement._manageDisplayApplicativePageActions(this, isDirty);
    },
    async onLoad() {
        this.receivingSites = [];
        await pageFunctions.initPage(this);
        this._defaultDimensionsAttributes = await initDefaultDimensions({
            page: this,
            dimensionDefinitionLevel: 'intersiteTransferOrder',
            site: this.stockSite.value,
            customer: this.shipToCustomer.value,
            supplier: this.supplier.value,
            receivingSite: this.receivingSite.value,
        });
        if (this.status.value && this.approvalStatus.value && this.shippingStatus.value && this.receivingStatus.value) {
            this.orderStepSequence.statuses = stepSequence.getDisplayStatusStepSequence(
                this.$.recordId,
                this.status.value,
                this.approvalStatus.value,
                this.shippingStatus.value,
                this.receivingStatus.value,
            );
        }
        displayButtonsManagement._manageDisplayApplicativePageActions(this, false);
        this.$.setPageClean();
    },
    onError(error: string | (Error & { errors: Array<any> })) {
        return MasterDataUtils.formatError(this, error);
    },
    navigationPanel: {
        orderBy: { date: -1, number: -1 },
        listItem: {
            title: ui.nestedFields.link({
                bind: 'number',
                title: 'Number',
                isMandatory: true,
                page: '@sage/xtrem-supply-chain/StockTransferOrder',
                queryParameters(_value, rowData) {
                    return { _id: rowData?._id || '' };
                },
            }),
            line2: ui.nestedFields.reference<StockTransferOrder, StockTransferOrderNode>({
                bind: 'receivingSite',
                node: '@sage/xtrem-system/Site',
                tunnelPage: '@sage/xtrem-master-data/Site',
                valueField: 'name',
                title: 'Receiving site',
            }),
            line2Right: ui.nestedFields.date({ bind: 'date', title: 'Order date', isMandatory: true }),
            site: ui.nestedFields.reference<StockTransferOrder, StockTransferOrderNode>({
                bind: 'site',
                node: '@sage/xtrem-system/Site',
                tunnelPage: '@sage/xtrem-master-data/Site',
                valueField: 'name',
                title: 'Shipping site',
                columns: [ui.nestedFields.technical({ bind: 'isStockTransferOrderApprovalManaged' })],
            }),
            expectedDeliveryDate: ui.nestedFields.date({ title: 'Delivery date', bind: 'expectedDeliveryDate' }),
            titleRight: ui.nestedFields.label({
                bind: 'displayStatus',
                title: 'Status',
                optionType: '@sage/xtrem-supply-chain/StockTransferOrderDisplayStatus',
                isMandatory: true,
                style: (_id, rowData) => pillColorCommon.getDisplayStatusPillFeatures(rowData?.displayStatus),
            }),
            shipToCustomer: ui.nestedFields.reference<StockTransferOrder, StockTransferOrderNode>({
                bind: 'shipToCustomer',
                node: '@sage/xtrem-master-data/Customer',
                tunnelPage: '@sage/xtrem-master-data/Customer',
                valueField: { businessEntity: { name: true } },
                title: 'Ship-to customer',
                isHiddenOnMainField: true,
                groupAggregationMethod: 'distinctCount',
                isHidden: true,
            }),
            stockSite: ui.nestedFields.reference<StockTransferOrder, StockTransferOrderNode>({
                bind: 'stockSite',
                node: '@sage/xtrem-system/Site',
                tunnelPage: '@sage/xtrem-master-data/Site',
                valueField: 'name',
                title: 'Stock site',
                isHiddenOnMainField: true,
                groupAggregationMethod: 'distinctCount',
            }),
            deliveryMode: ui.nestedFields.reference<StockTransferOrder, StockTransferOrderNode>({
                bind: 'deliveryMode',
                node: '@sage/xtrem-master-data/DeliveryMode',
                tunnelPage: '@sage/xtrem-master-data/DeliveryMode',
                valueField: 'name',
                title: 'Delivery mode',
            }),
            requestedDeliveryDate: ui.nestedFields.date({
                title: 'Requested delivery date',
                bind: 'requestedDeliveryDate',
                isHiddenOnMainField: true,
            }),
            deliveryLeadTime: ui.nestedFields.numeric({
                bind: 'deliveryLeadTime',
                title: 'Delivery lead time',
                postfix: 'days',
            }),
            shippingDate: ui.nestedFields.date({
                title: 'Shipping date',
                bind: 'shippingDate',
                isHiddenOnMainField: true,
            }),
            doNotShipBeforeDate: ui.nestedFields.date({
                title: 'Do-not-ship-before date',
                bind: 'doNotShipBeforeDate',
                isHiddenOnMainField: true,
                isHidden: true,
            }),
            doNotShipAfterDate: ui.nestedFields.date({
                title: 'Do-not-ship-after date',
                bind: 'doNotShipAfterDate',
                isHiddenOnMainField: true,
                isHidden: true,
            }),
            transactionCurrency: ui.nestedFields.reference<StockTransferOrder, StockTransferOrderNode>({
                bind: 'transactionCurrency',
                node: '@sage/xtrem-master-data/Currency',
                valueField: 'name',
                title: 'Transaction currency',
                isHidden: true,
                columns: [
                    ui.nestedFields.text({ title: 'Name', bind: 'name' }),
                    ui.nestedFields.technical({ bind: 'id' }),
                    ui.nestedFields.numeric({ title: 'Decimal points', bind: 'decimalDigits', canFilter: false }),
                    ui.nestedFields.numeric({ title: 'Rounding', bind: 'rounding', canFilter: false }),
                    ui.nestedFields.text({ title: 'Symbol', bind: 'symbol', canFilter: false }),
                    ui.nestedFields.technical({ bind: 'isActive' }),
                ],
            }),
            incoterm: ui.nestedFields.reference<StockTransferOrder, StockTransferOrderNode>({
                bind: 'incoterm',
                title: 'Incoterms® rule',
                isHidden: true,
                isHiddenOnMainField: true,
            }),
            isPrinted: ui.nestedFields.icon({
                bind: 'isPrinted',
                title: 'Printed',
                isHidden: true, // not for now
                map: (_value, rowData) => (rowData.isPrinted ? 'tick' : 'none'),
            }),
            isSent: ui.nestedFields.icon({
                bind: 'isSent',
                title: 'Sent',
                map: (_value, rowData) => (rowData.isSent ? 'tick' : 'none'),
                isHidden: true, // not for now
            }),
            shippingStatus: ui.nestedFields.technical({ bind: 'shippingStatus' }),
            receivingStatus: ui.nestedFields.technical({ bind: 'receivingStatus' }),
            status: ui.nestedFields.technical<StockTransferOrder, StockTransferOrderNode>({ bind: 'status' }),
            approvalStatus: ui.nestedFields.technical({ bind: 'approvalStatus' }),
            isCloseHidden: ui.nestedFields.technical({ bind: 'isCloseHidden' }),
            allocationRequestStatus: ui.nestedFields.technical({ bind: 'allocationRequestStatus' }),
            allocationStatus: ui.nestedFields.technical({ bind: 'allocationStatus' }),
        },
        optionsMenu: [
            {
                title: 'All open statuses',
                graphQLFilter: {
                    displayStatus: {
                        _nin: ['closed', 'rejected', 'received', 'shipped', 'partiallyShipped', 'partiallyReceived'],
                    },
                },
            },
            { title: 'All statuses', graphQLFilter: {} },
            { title: 'Draft', graphQLFilter: { displayStatus: { _eq: 'draft' } } },
            { title: 'Pending approval', graphQLFilter: { displayStatus: { _eq: 'pendingApproval' } } },
            { title: 'Approved', graphQLFilter: { displayStatus: { _eq: 'approved' } } },
            { title: 'Closed', graphQLFilter: { displayStatus: { _eq: 'closed' } } },
            { title: 'Rejected', graphQLFilter: { displayStatus: { _eq: 'rejected' } } },
            { title: 'Shipped', graphQLFilter: { displayStatus: { _in: ['shipped', 'partiallyShipped'] } } },
            { title: 'Received', graphQLFilter: { displayStatus: { _in: ['received', 'partiallyReceived'] } } },
            { title: 'Confirmed', graphQLFilter: { displayStatus: { _eq: 'confirmed' } } },
        ],
        // not for now
        // inlineActions: [
        //     {
        //         title: 'Duplicate',
        //         icon: 'duplicate',
        //         onClick(rowId: string) {
        //             this.$standardDuplicateAction.execute(false, rowId);
        //         },
        //     },
        // ],
        dropdownActions: [
            {
                title: 'Confirm',
                icon: 'none',
                refreshesMainList: 'list',
                async onClick(recordId: string, rowItem: ui.PartialNodeWithId<StockTransferOrderNode>) {
                    if (rowItem.number && rowItem.status && recordId) {
                        await actionsFunctionsDistribution.confirmOrderAction({
                            isCalledFromRecordPage: false,
                            pageInstance: this,
                            number: rowItem.number,
                            mutation: actionsFunctions.runConfirmOrderMutation({
                                pageInstance: this,
                                recordId,
                            }),
                        });
                    }
                },
                isHidden(recordId: string, rowItem: ui.PartialNodeWithId<StockTransferOrderNode>) {
                    return displayButtons.isHiddenButtonConfirmAction({
                        parameters: {
                            status: rowItem.status,
                            approvalStatus: rowItem.approvalStatus,
                            isApprovalManaged: rowItem.site?.isStockTransferOrderApprovalManaged,
                        },
                        recordId,
                        isDirty: false,
                    });
                },
            },
            {
                title: 'Approve',
                icon: 'none',
                refreshesMainList: 'list',
                async onClick(recordId: string, rowItem: ui.PartialNodeWithId<StockTransferOrderNode>) {
                    if (rowItem.number && recordId) {
                        await actionsFunctionsDistribution.approveAction({
                            pageInstance: this,
                            recordId,
                            mutation: actionsFunctions.runApproveMutation({
                                pageInstance: this,
                                recordId,
                            }),
                        });
                    }
                },
                isHidden(recordId: string, rowItem: ui.PartialNodeWithId<StockTransferOrderNode>) {
                    return displayButtons.isHiddenButtonApproveOrRejectAction({
                        parameters: {
                            status: rowItem.status,
                            approvalStatus: rowItem.approvalStatus,
                        },
                        recordId,
                        isDirty: false,
                    });
                },
            },
            {
                title: 'Reject',
                icon: 'none',
                refreshesMainList: 'list',
                async onClick(recordId: string, rowItem: ui.PartialNodeWithId<StockTransferOrderNode>) {
                    if (rowItem.number && recordId) {
                        await actionsFunctionsDistribution.rejectAction({
                            pageInstance: this,
                            recordId,
                            mutation: actionsFunctions.runRejectMutation({
                                pageInstance: this,
                                recordId,
                            }),
                        });
                    }
                },
                isHidden(recordId: string, rowItem: ui.PartialNodeWithId<StockTransferOrderNode>) {
                    return displayButtons.isHiddenButtonApproveOrRejectAction({
                        parameters: {
                            status: rowItem.status,
                            approvalStatus: rowItem.approvalStatus,
                        },
                        recordId,
                        isDirty: false,
                    });
                },
            },
            {
                title: 'Submit for approval',
                icon: 'none',
                refreshesMainList: 'list',
                async onClick(recordId: string, rowItem: ui.PartialNodeWithId<StockTransferOrderNode>) {
                    if (recordId && rowItem.site?._id) {
                        this._id.value = recordId;
                        const siteApprovers = await getSiteApprovers(this, rowItem.site?._id);
                        const siteApproverData = {
                            recordId,
                            defaultApprover: siteApprovers?.stockTransferOrderDefaultApprover ?? null,
                            substituteApprover: siteApprovers?.stockTransferOrderSubstituteApprover ?? null,
                        };
                        await actionsFunctions.submitForApproval(this, siteApproverData);
                    }
                },
                isHidden(recordId, rowItem: ui.PartialNodeWithId<StockTransferOrderNode>) {
                    return displayButtons.isHiddenButtonRequestApprovalAction({
                        parameters: {
                            status: rowItem.status,
                            approvalStatus: rowItem.approvalStatus,
                            isApprovalManaged: rowItem.site?.isStockTransferOrderApprovalManaged,
                        },
                        recordId,
                        isDirty: false,
                    });
                },
            },
            {
                icon: 'none',
                title: 'Create shipment',
                refreshesMainList: 'list',
                async onClick(recordId) {
                    await actionsFunctions.createStockTransferShipmentAction({
                        isCalledFromRecordPage: false,
                        stockTransferOrderPage: this,
                        _id: recordId,
                    });
                },
                isHidden(recordId: string, rowItem: ui.PartialNodeWithId<StockTransferOrderNode>) {
                    return displayButtons.isHiddenButtonShipAction({
                        parameters: {
                            status: rowItem.status,
                            allocationRequestStatus: rowItem.allocationRequestStatus,
                        },
                        recordId,
                        isDirty: false,
                    });
                },
            },
            {
                icon: 'none',
                title: 'Close order',
                refreshesMainList: 'list',
                async onClick(recordId, rowItem: ui.PartialNodeWithId<StockTransferOrderNode>) {
                    if (rowItem.number) {
                        await actionsFunctionsDistribution.closeAction({
                            pageInstance: this,
                            recordNumber: rowItem.number,
                            isCalledFromRecordPage: false,
                            mutation: actionsFunctions.runCloseMutation({
                                pageInstance: this,
                                recordId,
                            }),
                        });
                    }
                },
                isHidden(_recordId: string, rowItem: ui.PartialNodeWithId<StockTransferOrderNode>) {
                    return rowItem.isCloseHidden ?? false;
                },
            },
            {
                icon: 'none',
                title: 'Reopen order',
                refreshesMainList: 'list',
                async onClick(recordId: string, rowItem: ui.PartialNodeWithId<StockTransferOrderNode>) {
                    if (rowItem.number) {
                        await actionsFunctionsDistribution.openAction({
                            pageInstance: this,
                            recordNumber: rowItem.number,
                            isCalledFromRecordPage: false,
                            mutation: actionsFunctions.runOpenMutation({
                                pageInstance: this,
                                recordId,
                            }),
                        });
                    }
                },
                isHidden(recordId: string, rowItem: ui.PartialNodeWithId<StockTransferOrderNode>) {
                    return displayButtons.isHiddenButtonOpenAction({
                        parameters: {
                            status: rowItem.status,
                            shippingStatus: rowItem.shippingStatus,
                            approvalStatus: rowItem.approvalStatus,
                        },
                        recordId,
                        isDirty: false,
                    });
                },
            },
            ui.menuSeparator(),
            {
                icon: 'three_boxes',
                title: 'Allocate stock',
                async onClick(rowId) {
                    await actionsFunctionsDistribution.requestAllocationAction({
                        pageInstance: this,
                        allocationType: 'allocation',
                        mutation: actionsFunctions.runRequestAllocationMutation({
                            pageInstance: this,
                            _id: rowId,
                            allocationType: 'allocation',
                        }),
                    });
                },
                isHidden() {
                    return true;
                },
                // isHidden until XT-82150
                // isHidden(recordId, rowItem: ui.PartialNodeWithId<StockTransferOrderNode>) {
                //     return displayButtons.isHiddenButtonRequestAllocationAction({
                //         parameters: {
                //             allocationRequestStatus: rowItem.allocationRequestStatus,
                //             allocationStatus: rowItem.allocationStatus,
                //             status: rowItem.status,
                //         },
                //         recordId,
                //     });
                // },
            },
            {
                icon: 'three_boxes',
                title: 'Deallocate stock',
                async onClick(rowId) {
                    await actionsFunctionsDistribution.requestAllocationAction({
                        pageInstance: this,
                        allocationType: 'deallocation',
                        mutation: actionsFunctions.runRequestAllocationMutation({
                            pageInstance: this,
                            _id: rowId,
                            allocationType: 'deallocation',
                        }),
                    });
                },
                isHidden() {
                    return true;
                },
                // isHidden until XT-82150
                // isHidden(recordId, rowItem: ui.PartialNodeWithId<StockTransferOrderNode>) {
                //     return displayButtons.isHiddenButtonRequestDeallocationAction({
                //         parameters: {
                //             allocationRequestStatus: rowItem.allocationRequestStatus,
                //             allocationStatus: rowItem.allocationStatus,
                //             status: rowItem.status,
                //         },
                //         recordId,
                //     });
                // },
            },
            ui.menuSeparator(),
            {
                icon: 'none',
                title: 'Set dimensions',
                async onClick(_rowId, rowItem: ui.PartialNodeWithId<StockTransferOrderNode>) {
                    if (rowItem.status !== 'closed' && rowItem.number && rowItem.status) {
                        await actionsFunctions.setDimensions({
                            stockTransferOrderPage: this,
                            recordNumber: rowItem.number,
                            status: rowItem.status,
                        });
                    }
                },
                isHidden(recordId, rowItem: ui.PartialNodeWithId<StockTransferOrderNode>) {
                    return displayButtons.isHiddenButtonDefaultDimensionAction({
                        parameters: { status: rowItem.status, shippingStatus: rowItem.shippingStatus },
                        recordId,
                    });
                },
            },
            ui.menuSeparator(),
            {
                title: 'Delete',
                icon: 'bin',
                refreshesMainList: 'list',
                isDestructive: true,
                onError(error: string | (Error & { errors: Array<any> })) {
                    this.$.loader.isHidden = true;
                    return MasterDataUtils.formatError(this, error);
                },
                isHidden(recordId, rowItem: ui.PartialNodeWithId<StockTransferOrderNode>) {
                    return displayButtons.isHiddenButtonDeleteAction({
                        parameters: {
                            status: rowItem.status,
                            displayStatus: rowItem.displayStatus,
                        },
                        recordId,
                    });
                },
                async onClick(recordId: string, rowItem: ui.PartialNodeWithId<StockTransferOrderNode>) {
                    this._id.value = recordId;

                    await MainListActions.deleteRecord<GraphApi>(this, {
                        _id: rowItem._id,
                        nodeName: '@sage/xtrem-supply-chain/StockTransferOrder',
                    });
                },
            },
        ],
    },
})
// implements financeInterfaces.PageWithDefaultDimensions
export class StockTransferOrder extends ui.Page<GraphApi, StockTransferOrderNode> {
    _defaultDimensionsAttributes: financeInterfaces.DefaultDimensions;

    protected _shipToAddressDetailsCounter = -4;

    confirmDialogResponseOnShipToAddressUpdate: boolean;

    initLinesLength: number;

    currentSelectedLineId: string;

    allocationFields: ui.SidebarFieldDefinition<StockTransferOrderLineBinding>[];

    receivingSites: string[];

    @ui.decorators.pageAction<StockTransferOrder>({
        icon: 'bin',
        title: 'Delete',
        isHidden: true,
        isDestructive: true,
        onError() {
            this.$.loader.isHidden = true;
        },
        async onClick() {
            await this.$standardDeleteAction.execute(true);
        },
    })
    deleteStockTransferOrder: ui.PageAction;

    @ui.decorators.pageAction<StockTransferOrder>({
        title: 'Confirm',
        isHidden: true,
        async onClick() {
            if (this.number.value && this.$.recordId) {
                await actionsFunctionsDistribution.confirmOrderAction({
                    isCalledFromRecordPage: true,
                    pageInstance: this,
                    number: this.number.value,
                    mutation: actionsFunctions.runConfirmOrderMutation({
                        pageInstance: this,
                        recordId: this.$.recordId,
                    }),
                });
            }
        },
    })
    confirm: ui.PageAction;

    @ui.decorators.pageAction<StockTransferOrder>({
        title: 'Close order',
        isHidden: true,
        async onClick() {
            if (this.number.value && this.$.recordId) {
                await actionsFunctionsDistribution.closeAction({
                    pageInstance: this,
                    recordNumber: this.number.value,
                    isCalledFromRecordPage: true,
                    mutation: actionsFunctions.runCloseMutation({
                        pageInstance: this,
                        recordId: this.$.recordId,
                    }),
                });
            }
        },
    })
    close: ui.PageAction;

    @ui.decorators.pageAction<StockTransferOrder>({
        title: 'Reopen order',
        isHidden: true,
        async onClick() {
            if (this.number.value && this.$.recordId) {
                await actionsFunctionsDistribution.openAction({
                    pageInstance: this,
                    recordNumber: this.number.value,
                    isCalledFromRecordPage: true,
                    mutation: actionsFunctions.runOpenMutation({
                        pageInstance: this,
                        recordId: this.$.recordId,
                    }),
                });
            }
        },
    })
    open: ui.PageAction;

    @ui.decorators.pageAction<StockTransferOrder>({
        title: 'Save',
        buttonType: 'primary',
        async onClick() {
            await actionsFunctions.saveAction(this);
        },
    })
    save: ui.PageAction;

    @ui.decorators.pageAction<StockTransferOrder>({
        title: 'Allocate stock',
        icon: 'three_boxes',
        onError(error: string | (Error & { errors: Array<any> })) {
            this.$.loader.isHidden = true;
            return MasterDataUtils.formatError(this, error);
        },
        // isHidden until XT-82150
        isHidden() {
            return true;
        },
        async onClick() {
            if (this._id.value) {
                await actionsFunctionsDistribution.requestAllocationAction({
                    pageInstance: this,
                    allocationType: 'allocation',
                    mutation: actionsFunctions.runRequestAllocationMutation({
                        pageInstance: this,
                        _id: this._id.value,
                        allocationType: 'allocation',
                    }),
                });
            }
        },
    })
    requestAllocation: ui.PageAction;

    @ui.decorators.pageAction<StockTransferOrder>({
        title: 'Deallocate stock',
        icon: 'three_boxes',
        onError(error: string | (Error & { errors: Array<any> })) {
            this.$.loader.isHidden = true;
            return MasterDataUtils.formatError(this, error);
        },
        // isHidden until XT-82150
        isHidden() {
            return true;
        },
        async onClick() {
            if (this._id.value) {
                await actionsFunctionsDistribution.requestAllocationAction({
                    pageInstance: this,
                    allocationType: 'deallocation',
                    mutation: actionsFunctions.runRequestAllocationMutation({
                        pageInstance: this,
                        _id: this._id.value,
                        allocationType: 'deallocation',
                    }),
                });
            }
        },
    })
    requestDeallocation: ui.PageAction;

    @ui.decorators.pageAction<StockTransferOrder>({
        icon: 'none',
        title: 'Set dimensions',
        async onClick() {
            const defaultedFromItem = await attributesAndDimensions.getAttributesAndDimensionsFromItem({
                page: this,
                dimensionDefinitionLevel: 'intersiteTransferOrder',
                companyId: Number(this.stockSite?.value?.legalCompany?._id),
            });
            this._defaultDimensionsAttributes = await dimensionPanelHelpers.editAndApplyDefaultDimensionsIfNeeded(
                this,
                this.lines,
                this._defaultDimensionsAttributes,
                () => true,
                defaultedFromItem,
            );
        },
    })
    defaultDimension: ui.PageAction;

    @ui.decorators.pageAction<StockTransferOrder>({
        title: 'Approve',
        isHidden: true,
        async onClick() {
            if (this._id.value) {
                await actionsFunctionsDistribution.approveAction({
                    pageInstance: this,
                    recordId: this._id.value,
                    mutation: actionsFunctions.runApproveMutation({
                        pageInstance: this,
                        recordId: this._id.value,
                    }),
                });
            }
            displayButtonsManagement.manageDisplayButtonApproveAction(this, false);
            displayButtonsManagement.manageDisplayButtonRejectAction(this, false);
            this.$.setPageClean();
            await this.$.refreshNavigationPanel();
            await this.$.router.refresh();
        },
    })
    approve: ui.PageAction;

    @ui.decorators.pageAction<StockTransferOrder>({
        title: 'Reject',
        isHidden: true,
        async onClick() {
            if (this._id.value) {
                await actionsFunctionsDistribution.rejectAction({
                    pageInstance: this,
                    recordId: this._id.value,
                    mutation: actionsFunctions.runRejectMutation({
                        pageInstance: this,
                        recordId: this._id.value,
                    }),
                });
            }
            displayButtonsManagement.manageDisplayButtonApproveAction(this, false);
            displayButtonsManagement.manageDisplayButtonRejectAction(this, false);
            this.$.setPageClean();
            await this.$.refreshNavigationPanel();
            await this.$.router.refresh();
        },
    })
    reject: ui.PageAction;

    @ui.decorators.pageAction<StockTransferOrder>({
        isTransient: true,
        isHidden: true,
        title: 'Submit for approval',
        async onClick() {
            if (this.site.value?._id && this.$.recordId) {
                const siteApproverData = {
                    recordId: this.$.recordId,
                    defaultApprover: this.site.value?.stockTransferOrderDefaultApprover ?? null,
                    substituteApprover: this.site.value?.stockTransferOrderSubstituteApprover ?? null,
                };
                await actionsFunctions.submitForApproval(this, siteApproverData);
            }
        },
    })
    requestApproval: ui.PageAction;

    @ui.decorators.pageAction<StockTransferOrder>({
        title: 'Create shipment',
        isHidden: true,
        async onClick() {
            if (this.$.recordId) {
                await actionsFunctions.createStockTransferShipmentAction({
                    isCalledFromRecordPage: true,
                    stockTransferOrderPage: this,
                    _id: this.$.recordId,
                });
            }
        },
    })
    ship: ui.PageAction;

    // not for now
    @ui.decorators.pageAction<StockTransferOrder>({
        isTransient: true,
        isHidden: true,
        title: 'Send',
        icon: 'email',
        async onClick() {
            this.sendEmailSection.isHidden = false;

            await commonDistribution.loadContacts(
                this,
                this.shipToLinkedAddress?.value?._id || '',
                this.shipToContact.value,
            );

            await this.$.dialog.custom('info', this.sendEmailSection, {
                cancelButton: { isHidden: true },
                acceptButton: { isHidden: true },
                resolveOnCancel: true,
            });
            this.sendEmailSection.isHidden = true;
            this.$.setPageClean();
        },
    })
    sendEmail: ui.PageAction;

    // Send stock transfer order by email start here
    @ui.decorators.section<StockTransferOrder>({
        isHidden: true,
        title: 'Send email',
    })
    sendEmailSection: ui.containers.Section;

    @ui.decorators.block<StockTransferOrder>({
        parent() {
            return this.sendEmailSection;
        },
        title: 'To',
    })
    sendEmailBlock: ui.containers.Block;

    @ui.decorators.referenceField<StockTransferOrder, Contact>({
        parent() {
            return this.contactSelectionBlock;
        },
        isHidden: true,
        node: '@sage/xtrem-master-data/Contact',
        valueField: 'email',
        columns: [
            ui.nestedFields.text({ title: 'Last name', bind: 'lastName' }),
            ui.nestedFields.text({ title: 'First name', bind: 'firstName' }),
            ui.nestedFields.text({ title: 'Title', bind: 'title' }),
            ui.nestedFields.technical({ bind: 'email' }),
            ui.nestedFields.technical({ bind: 'preferredName' }),
            ui.nestedFields.technical({ bind: 'role' }),
            ui.nestedFields.technical({ bind: 'position' }),
            ui.nestedFields.technical({ bind: 'locationPhoneNumber' }),
        ],
        title: 'Sold-to customer address contact detail',
        isTitleHidden: true,
        lookupDialogTitle: 'Select sold-to customer address contact detail',
    })
    shipToContact: ui.fields.Reference<Contact>;

    @ui.decorators.dropdownListField<StockTransferOrder>({
        parent() {
            return this.sendEmailBlock;
        },
        title: 'Title',
        optionType: '@sage/xtrem-master-data/Title',
        isTransient: true,
        isFullWidth: true,
    })
    emailTitle: ui.fields.DropdownList;

    @ui.decorators.textField<StockTransferOrder>({
        parent() {
            return this.sendEmailBlock;
        },
        title: 'First name',
        maxLength: 30,
        isTransient: true,
        isFullWidth: true,
    })
    emailFirstName: ui.fields.Text;

    @ui.decorators.textField<StockTransferOrder>({
        parent() {
            return this.sendEmailBlock;
        },
        title: 'Last name',
        maxLength: 30,
        isMandatory: true,
        isTransient: true,
        isFullWidth: true,
    })
    emailLastName: ui.fields.Text;

    @ui.decorators.textField<StockTransferOrder>({
        parent() {
            return this.sendEmailBlock;
        },
        title: 'Email',
        helperText: 'An email will be sent to this address.',
        isTransient: true,
        isFullWidth: true,
        isMandatory: true,
        validation(val) {
            if (val.length > 0 && !validEmail(val)) {
                return ui.localize(
                    '@sage/xtrem-supply-chain/pages__stock_transfer_order__invalid-email',
                    'Invalid email address: {{value}}',
                    {
                        value: val,
                    },
                );
            }
            return undefined;
        },
    })
    emailAddress: ui.fields.Text;

    @ui.decorators.buttonField<StockTransferOrder>({
        isTransient: true,
        width: 'small',
        parent() {
            return this.sendEmailBlock;
        },
        map() {
            return ui.localize(
                '@sage/xtrem-supply-chain/pages__stock_transfer_order__select_sold_to_contact_button_text',
                'Select sold-to customer contact',
            );
        },
        onError() {
            // Intentionally left empty
        },
        async onClick() {
            this.contactSelectionSection.isHidden = false;
            await this.$.dialog
                .custom('info', this.contactSelectionSection, { resolveOnCancel: true })
                .then(() => {
                    if (this.selectedContact.value) {
                        commonDistribution.assignEmailContactFrom(this, this.selectedContact.value);
                    }
                })
                .finally(() => {
                    this.selectedContact.value = null;
                    this.contactSelectionSection.isHidden = false;
                });
        },
        title: 'Select sold-to customer contact',
        isTitleHidden: true,
    })
    selectSoldToContact: ui.fields.Button;

    @ui.decorators.section<StockTransferOrder>({
        isHidden: true,
        isTitleHidden: true,
    })
    contactSelectionSection: ui.containers.Section;

    @ui.decorators.block<StockTransferOrder>({
        parent() {
            return this.contactSelectionSection;
        },
        title: 'Contact selection',
        isTitleHidden: true,
    })
    contactSelectionBlock: ui.containers.Block;

    @ui.decorators.tableField<StockTransferOrder, BusinessEntityContact>({
        parent() {
            return this.contactSelectionSection;
        },
        node: '@sage/xtrem-master-data/BusinessEntityContact',
        canSelect: false,
        title: 'Contacts',
        isTitleHidden: true,
        isTransient: true,
        pageSize: 10,
        orderBy: { lastName: +1, firstName: +1 },
        columns: [
            ui.nestedFields.dropdownList({
                title: 'Title',
                bind: 'title',
                optionType: '@sage/xtrem-master-data/enums/title',
            }),
            ui.nestedFields.text({ title: 'First name', bind: 'firstName' }),
            ui.nestedFields.text({ title: 'Last name', bind: 'lastName' }),
            ui.nestedFields.text({ title: 'Email', bind: 'email' }),
            ui.nestedFields.technical({ bind: 'preferredName' }),
            ui.nestedFields.technical({ bind: 'role' }),
            ui.nestedFields.technical({ bind: 'position' }),
            ui.nestedFields.technical({ bind: 'locationPhoneNumber' }),
        ],
        onRowClick(_id, rowData) {
            // todo : create enhancement request =>  this.selectedContact.value = rowData must work
            this.selectedContact.value = {
                _id: rowData._id,
                lastName: rowData.lastName,
                firstName: rowData.firstName,
                title: rowData.title,
                email: rowData.email,
            };
        },
    })
    contacts: ui.fields.Table<BusinessEntityContact>;

    @ui.decorators.referenceField<StockTransferOrder, BusinessEntityContact>({
        parent() {
            return this.contactSelectionBlock;
        },
        isReadOnly: true,
        node: '@sage/xtrem-master-data/BusinessEntityContact',
        valueField: 'email',
        isTransient: true,
        isFullWidth: true,
        columns: [
            ui.nestedFields.text({ title: 'Last name', bind: 'lastName' }),
            ui.nestedFields.text({ title: 'First name', bind: 'firstName' }),
            ui.nestedFields.text({ title: 'Title', bind: 'title' }),
            ui.nestedFields.technical({ bind: 'email' }),
        ],
        title: 'Selected contact',
        isTitleHidden: true,
        lookupDialogTitle: 'Select selected contact',
    })
    selectedContact: ui.fields.Reference<BusinessEntityContact>;

    // Send stock transfer order by email end here

    @ui.decorators.textField<StockTransferOrder>({
        isHidden: true,
        title: 'ID',
        isTitleHidden: true,
    })
    _id: ui.fields.Text;

    @ui.decorators.switchField<StockTransferOrder>({
        isHidden: true,
        bind: 'isCloseHidden',
    })
    isCloseHidden: ui.fields.Switch;

    @ui.decorators.section<StockTransferOrder>({ title: 'Lines' })
    itemSection: ui.containers.Section;

    @ui.decorators.stepSequenceField<StockTransferOrder>({
        parent() {
            return this.headerBlock;
        },
        options() {
            return stepSequence.getStepSequence(
                this.approvalStatus.value,
                this.site.value?.isStockTransferOrderApprovalManaged,
            );
        },
        width: 'small',
        isTransient: true,
        isFullWidth: true,
    })
    orderStepSequence: ui.fields.StepSequence;

    @ui.decorators.referenceField<StockTransferOrder, Site>({
        parent() {
            return this.headerBlock;
        },
        title: 'Shipping site',
        lookupDialogTitle: 'Select shipping site',
        minLookupCharacters: 0,
        isAutoSelectEnabled: true,
        fetchesDefaults: true,
        isMandatory: true,
        isReadOnly() {
            return !!this.$.recordId || this.lines.value.length > 0;
        },
        columns: [
            ui.nestedFields.text({ bind: 'name', title: 'Name' }),
            ui.nestedFields.text({ bind: 'id', title: 'ID' }),
            ui.nestedFields.technical({ bind: 'isStockTransferOrderApprovalManaged' }),
            ui.nestedFields.technical<StockTransferOrder, Site, BusinessEntity>({
                bind: 'businessEntity',
                nestedFields: [
                    ui.nestedFields.technical<StockTransferOrder, BusinessEntity, Supplier>({
                        node: '@sage/xtrem-master-data/Supplier',
                        bind: 'supplier',
                        nestedFields: [
                            ui.nestedFields.technical({ bind: 'id' }),
                            ui.nestedFields.technical({ bind: 'name' }),
                        ],
                    }),
                    ui.nestedFields.technical<StockTransferOrder, BusinessEntity, Currency>({
                        node: '@sage/xtrem-master-data/Currency',
                        bind: 'currency',
                        nestedFields: [
                            ui.nestedFields.technical({ bind: 'name' }),
                            ui.nestedFields.technical({ bind: 'id' }),
                            ui.nestedFields.technical({ bind: 'symbol' }),
                            ui.nestedFields.technical({ bind: 'decimalDigits' }),
                        ],
                    }),
                ],
            }),
            ui.nestedFields.reference<StockTransferOrder, Site, Company>({
                node: '@sage/xtrem-system/Company',
                tunnelPage: '@sage/xtrem-master-data/Company',
                title: 'Company',
                bind: 'legalCompany',
                valueField: 'name',
                helperTextField: 'id',
                columns: [
                    ui.nestedFields.text({ bind: 'name', title: 'Name' }),
                    ui.nestedFields.text({ bind: 'id', title: 'ID' }),
                    ui.nestedFields.technical<StockTransferOrder, Company, Legislation>({
                        node: '@sage/xtrem-structure/Legislation',
                        bind: 'legislation',
                        nestedFields: [
                            ui.nestedFields.technical({ bind: 'name' }),
                            ui.nestedFields.technical({ bind: 'id' }),
                        ],
                    }),
                    ui.nestedFields.technical<StockTransferOrder, Company, Currency>({
                        node: '@sage/xtrem-master-data/Currency',
                        bind: 'currency',
                        nestedFields: [
                            ui.nestedFields.technical({ bind: 'name' }),
                            ui.nestedFields.technical({ bind: 'id' }),
                            ui.nestedFields.technical({ bind: 'symbol' }),
                            ui.nestedFields.technical({ bind: 'decimalDigits' }),
                        ],
                    }),
                    ui.nestedFields.technical({ bind: 'taxEngine' }),
                    ui.nestedFields.technical({ bind: 'customerOnHoldCheck' }),
                ],
            }),
            ui.nestedFields.technical<StockTransferOrder, Site, User>({
                bind: 'stockTransferOrderDefaultApprover',
                node: '@sage/xtrem-system/User',
                nestedFields: [
                    ui.nestedFields.technical({ bind: 'email' }),
                    ui.nestedFields.technical({ bind: 'firstName' }),
                    ui.nestedFields.technical({ bind: 'lastName' }),
                ],
            }),
            ui.nestedFields.technical<StockTransferOrder, Site, User>({
                bind: 'stockTransferOrderSubstituteApprover',
                node: '@sage/xtrem-system/User',
                nestedFields: [
                    ui.nestedFields.technical({ bind: 'lastName' }),
                    ui.nestedFields.technical({ bind: 'firstName' }),
                    ui.nestedFields.technical({ bind: 'email' }),
                ],
            }),
            ui.nestedFields.technical({ bind: 'description' }),
            ui.nestedFields.technical({ bind: 'isInventory' }),
            ui.nestedFields.technical({ bind: 'isSales' }),
            ui.nestedFields.technical({ bind: 'isPurchase' }),
        ],
        filter() {
            return this.receivingSite.value
                ? ({
                      isInventory: true,
                      legalCompany: this.receivingSite.value.legalCompany,
                      _id: { _ne: this.receivingSite.value._id },
                  } as Logical<Site>)
                : {
                      isInventory: true,
                  };
        },
        async onChange() {
            if (this.site.value === null) {
                this.receivingSites = [];
            }

            displayButtonsManagement.manageDisplayButtonSelectFromItemSitesAction(this);
            if (this.site.value && !this.site.value?.businessEntity?.supplier) {
                throw new Error(
                    ui.localize(
                        '@sage/xtrem-supply-chain/pages__transfer-order__shipping-site-not-a-supplier',
                        'The shipping site is not a supplier.',
                    ),
                );
            }
            displayButtonsManagement.manageDisplayLinePhantomRow(this);
            displayButtonsManagement.manageDisplayButtonDefaultDimensionAction(this);
            this.rateDescription.isHidden = pageFunctions.isExchangeRateHidden(
                this.currency.value,
                this.site.value,
                this.shipToCustomer.value,
            );
            this._defaultDimensionsAttributes = await initDefaultDimensions({
                page: this,
                dimensionDefinitionLevel: 'intersiteTransferOrder',
                site: this.stockSite.value,
                supplier: this.supplier.value,
                customer: this.shipToCustomer.value,
                receivingSite: this.receivingSite.value,
                shippingSite: this.site.value,
            });
            this.receivingSites = await pageFunctions.getReceivingSites(this);
        },
    })
    site: ui.fields.Reference<Site>;

    @ui.decorators.referenceField<StockTransferOrder, Supplier>({
        parent() {
            return this.informationBlock;
        },
        title: 'Supplier',
        isMandatory: true,
        fetchesDefaults: true,
        isReadOnly: true,
        isHidden: true,
    })
    supplier: ui.fields.Reference<Supplier>;

    @ui.decorators.referenceField<StockTransferOrder, Site>({
        parent() {
            return this.informationBlock;
        },
        title: 'Financial site',
        fetchesDefaults: true,
        isMandatory: true,
        isReadOnly: true,
        columns: [
            ui.nestedFields.technical({ bind: 'name' }),
            ui.nestedFields.technical({ bind: 'id' }),
            ui.nestedFields.technical<StockTransferOrder, Site, Company>({
                bind: 'legalCompany',
                node: '@sage/xtrem-system/Company',
                nestedFields: [ui.nestedFields.technical({ bind: '_id' })],
            }),
        ],
    })
    financialSite: ui.fields.Reference<Site>;

    @ui.decorators.referenceField<StockTransferOrder, Site>({
        parent() {
            return this.headerBlock;
        },
        title: 'Receiving site',
        lookupDialogTitle: 'Select receiving site',
        minLookupCharacters: 0,
        isAutoSelectEnabled: true,
        fetchesDefaults: true,
        isMandatory: true,
        isReadOnly() {
            return !!this.$.recordId || this.lines.value.length > 0;
        },
        columns: [
            ui.nestedFields.text({ bind: 'name', title: 'Name' }),
            ui.nestedFields.text({ bind: 'id', title: 'ID' }),
            ui.nestedFields.technical<StockTransferOrder, Site, BusinessEntity>({
                bind: 'businessEntity',
                nestedFields: [
                    ui.nestedFields.technical<StockTransferOrder, BusinessEntity, Currency>({
                        node: '@sage/xtrem-master-data/Currency',
                        bind: 'currency',
                        nestedFields: [
                            ui.nestedFields.technical({ bind: 'name' }),
                            ui.nestedFields.technical({ bind: 'id' }),
                            ui.nestedFields.technical({ bind: 'symbol' }),
                            ui.nestedFields.technical({ bind: 'decimalDigits' }),
                        ],
                    }),
                    ui.nestedFields.technical<StockTransferOrder, BusinessEntity, Customer>({
                        node: '@sage/xtrem-master-data/Customer',
                        bind: 'customer',
                        nestedFields: [
                            ui.nestedFields.technical({ bind: 'name' }),
                            ui.nestedFields.technical({ bind: 'id' }),
                        ],
                    }),
                ],
            }),
            ui.nestedFields.reference<StockTransferOrder, Site, Company>({
                node: '@sage/xtrem-system/Company',
                tunnelPage: '@sage/xtrem-master-data/Company',
                title: 'Company',
                bind: 'legalCompany',
                valueField: 'name',
                helperTextField: 'id',
                columns: [
                    ui.nestedFields.text({ bind: 'name', title: 'Name' }),
                    ui.nestedFields.text({ bind: 'id', title: 'ID' }),
                    ui.nestedFields.technical<StockTransferOrder, Company, Legislation>({
                        node: '@sage/xtrem-structure/Legislation',
                        bind: 'legislation',
                        nestedFields: [
                            ui.nestedFields.technical({ bind: 'name' }),
                            ui.nestedFields.technical({ bind: 'id' }),
                        ],
                    }),
                    ui.nestedFields.technical<StockTransferOrder, Company, Currency>({
                        node: '@sage/xtrem-master-data/Currency',
                        bind: 'currency',
                        nestedFields: [
                            ui.nestedFields.technical({ bind: 'name' }),
                            ui.nestedFields.technical({ bind: 'id' }),
                            ui.nestedFields.technical({ bind: 'symbol' }),
                            ui.nestedFields.technical({ bind: 'decimalDigits' }),
                        ],
                    }),
                    ui.nestedFields.technical({ bind: 'taxEngine' }),
                    ui.nestedFields.technical({ bind: 'customerOnHoldCheck' }),
                ],
            }),
            ui.nestedFields.technical({ bind: 'description' }),
            ui.nestedFields.technical({ bind: 'isInventory' }),
            ui.nestedFields.technical({ bind: 'isSales' }),
            ui.nestedFields.technical({ bind: 'isPurchase' }),
        ],
        filter() {
            return { _id: { _in: this.receivingSites } };
        },
        async onChange() {
            displayButtonsManagement.manageDisplayButtonSelectFromItemSitesAction(this);
            if (this.receivingSite.value && !this.receivingSite.value?.businessEntity?.customer) {
                throw new Error(
                    ui.localize(
                        '@sage/xtrem-supply-chain/pages__transfer-order__receiving-site-not-a-customer',
                        'The receiving site is not a customer.',
                    ),
                );
            }

            displayButtonsManagement.manageDisplayLinePhantomRow(this);
            displayButtonsManagement.manageDisplayButtonDefaultDimensionAction(this);
            if (this.receivingSite.value && this.stockSite.value) {
                this._defaultDimensionsAttributes = await initDefaultDimensions({
                    page: this,
                    dimensionDefinitionLevel: 'intersiteTransferOrder', //
                    site: this.stockSite.value,
                    supplier: this.supplier.value,
                    customer: this.shipToCustomer.value,
                    receivingSite: this.receivingSite.value,
                });
            }
        },
    })
    receivingSite: ui.fields.Reference<Site>;

    @ui.decorators.referenceField<StockTransferOrder, Customer>({
        parent() {
            return this.shippingSectionShippingBlock;
        },
        title: 'Ship-to customer',
        isMandatory: true,
        isReadOnly: true,
        isHidden: true,
        async onChange() {
            if (this.site.value && this.shipToCustomer.value) {
                this._defaultDimensionsAttributes = await initDefaultDimensions({
                    page: this,
                    dimensionDefinitionLevel: 'intersiteTransferOrder',
                    site: this.stockSite.value,
                    supplier: this.supplier.value,
                    customer: this.shipToCustomer.value,
                    receivingSite: this.receivingSite.value,
                });
            }
        },
    })
    shipToCustomer: ui.fields.Reference<Customer>;

    @ui.decorators.textField<StockTransferOrder>({
        parent() {
            return this.headerBlock;
        },
        title: 'Number',
        isReadOnly() {
            return !!this.$.recordId;
        },
    })
    number: ui.fields.Text;

    @ui.decorators.dateField<StockTransferOrder>({
        parent() {
            return this.headerBlock;
        },
        title: 'Order date',
        isMandatory: true,
        fetchesDefaults: true,
        onError() {
            this.$.loader.isHidden = true;
        },
        onChange() {
            if (this.date.value) {
                if (Date.parse(this.date.value) > Date.now()) {
                    throw new Error(
                        ui.localize(
                            '@sage/xtrem-supply-chain/pages__stock_transfer_order__order_date__cannot__be__future',
                            'The order date cannot be later than today.',
                        ),
                    );
                }
            }
            displayButtonsManagement.manageDisplayLinePhantomRow(this);
            displayButtonsManagement.manageDisplayButtonDefaultDimensionAction(this);
        },
    })
    date: ui.fields.Date;

    @ui.decorators.dateField<StockTransferOrder>({
        parent() {
            return this.informationBlock;
        },
        title: 'Exchange rate date',
        isMandatory: true,
        isReadOnly: true,
        fetchesDefaults: true,
        isHidden: true,
    })
    fxRateDate: ui.fields.Date;

    @ui.decorators.numericField<StockTransferOrder>({
        parent() {
            return this.informationBlock;
        },
        title: 'Exchange rate',
        isReadOnly: true,
        scale() {
            return this.companyFxRate.value ? Math.max(scaleOfCurrent(this.companyFxRate.value), 2) : 2;
        },
        isHidden: true,
    })
    companyFxRate: ui.fields.Numeric;

    @ui.decorators.numericField<StockTransferOrder>({
        parent() {
            return this.informationBlock;
        },
        title: 'Exchange rate divisor',
        isReadOnly: true,
        scale() {
            return this.companyFxRateDivisor.value ? Math.max(scaleOfCurrent(this.companyFxRateDivisor.value), 2) : 2;
        },
        isHidden: true,
    })
    companyFxRateDivisor: ui.fields.Numeric;

    @ui.decorators.labelField<StockTransferOrder>({
        title: 'Display status',
        optionType: '@sage/xtrem-supply-chain/StockTransferOrderDisplayStatus',
        style() {
            return pillColorCommon.getDisplayStatusPillFeatures(this.displayStatus.value);
        },
    })
    displayStatus: ui.fields.Label;

    @ui.decorators.labelField<StockTransferOrder>({
        parent() {
            return this.headerBlock;
        },
        title: 'Approval status',
        isHidden: true,
        optionType: '@sage/xtrem-master-data/ApprovalStatus',
    })
    approvalStatus: ui.fields.Label;

    @ui.decorators.labelField<StockTransferOrder>({
        title: 'Status',
        bind: 'status',
        optionType: '@sage/xtrem-master-data/BaseStatus',
        isHidden: true,
    })
    status: ui.fields.Label;

    @ui.decorators.tableField<StockTransferOrder, StockTransferOrderLineBinding>({
        bind: 'lines',
        title: 'Lines',
        isTitleHidden: true,
        canSelect: false,
        pageSize: 10,
        canAddNewLine: true,
        hasLineNumbers: true,
        node: '@sage/xtrem-supply-chain/StockTransferOrderLine',
        orderBy: { _sortValue: +1 },
        parent() {
            return this.itemSection;
        },
        columns: [
            /** Hack for richText not supported to  nestedFields : XT-38752 */
            ui.nestedFields.technical({ bind: { externalNote: { value: true } } }),
            ui.nestedFields.technical({ bind: { internalNote: { value: true } } }),
            ui.nestedFields.switch({
                bind: 'isExternalNote',
                title: 'Add notes to customer document',
                isHidden: true, // not for now
            }),
            ui.nestedFields.label({
                title: 'Status',
                bind: 'status',
                optionType: '@sage/xtrem-master-data/BaseStatus',
                style: (_id, rowData) => pillColorDocument.getLabelColorByStatus('LineStatus', rowData?.status),
            }),
            ui.nestedFields.technical({
                node: '@sage/xtrem-master-data/ItemSite',
                bind: 'itemSite',
                nestedFields: [
                    ui.nestedFields.technical({ bind: 'prodLeadTime' }),
                    ui.nestedFields.technical({ bind: 'isOrderToOrder' }),
                    ui.nestedFields.technical({ bind: 'preferredProcess' }),
                    ui.nestedFields.technical({ bind: 'inStockQuantity' }),
                ],
            }),
            ui.nestedFields.reference<StockTransferOrder, StockTransferOrderLineBinding, Item>({
                title: 'Item',
                bind: 'item',
                node: '@sage/xtrem-master-data/Item',
                tunnelPage: '@sage/xtrem-master-data/Item',
                valueField: 'name',
                lookupDialogTitle: 'Select item',
                helperTextField: 'id',
                shouldSuggestionsIncludeColumns: true,
                isAutoSelectEnabled: true,
                minLookupCharacters: 3,
                isMandatory: true,
                columns: [
                    ui.nestedFields.text({ bind: 'name' }),
                    ui.nestedFields.text({ bind: 'id' }),
                    ui.nestedFields.text({ bind: 'description' }),
                    ui.nestedFields.text({ title: 'Category', bind: { category: { name: true } } }),
                    ui.nestedFields.image({ bind: 'image', isHidden: true }),
                    ui.nestedFields.technical({ bind: 'type' }),
                    ui.nestedFields.technical({ bind: 'isStockManaged' }),
                    ui.nestedFields.technical({ bind: 'isManufactured' }),
                    ui.nestedFields.technical({ bind: 'isBought' }),
                    ui.nestedFields.technical<StockTransferOrder, Item, UnitOfMeasure>({
                        bind: 'stockUnit',
                        node: '@sage/xtrem-master-data/UnitOfMeasure',
                        nestedFields: [
                            ui.nestedFields.technical({ bind: 'name' }),
                            ui.nestedFields.technical({ bind: 'id' }),
                            ui.nestedFields.technical({ bind: 'symbol' }),
                            ui.nestedFields.technical({ bind: 'decimalDigits' }),
                        ],
                    }),
                ],
                async onChange(_rowId, rowData: ui.PartialCollectionValue<StockTransferOrderLineBinding>) {
                    if (rowData?.item) {
                        rowData.itemDescription = rowData.item.description
                            ? rowData.item.description
                            : rowData.item.name;
                        rowData.stockUnit = rowData.item.stockUnit;
                        rowData.unit = rowData.item.stockUnit;
                        if (rowData.quantity && rowData.unitToStockUnitConversionFactor) {
                            rowData.quantityInStockUnit = String(
                                +rowData.quantity * +rowData.unitToStockUnitConversionFactor,
                            );
                        }
                        await pageFunctions.getItemSiteAvailableStockQuantity(this, rowData);
                        if (rowData.stockSite) {
                            rowData.stockSite = await checkStockSite(this, rowData.item, rowData.stockSite);
                        }
                        await pageFunctions.updateCosts(this, rowData);
                        if (
                            this.stockSite.value &&
                            this.receivingSite.value &&
                            this.supplier.value &&
                            this.shipToCustomer.value &&
                            rowData.item
                        ) {
                            const { storedAttributes, storedDimensions } =
                                await attributesAndDimensions.defaultAttributesAndDimensionsWithItem({
                                    page: this,
                                    _defaultDimensionsAttributes: this._defaultDimensionsAttributes,
                                    dimensionDefinitionLevel: 'intersiteTransferOrder',
                                    site: this.stockSite.value,
                                    supplier: this.supplier.value,
                                    customer: this.shipToCustomer.value,
                                    item: rowData.item,
                                    receivingSite: this.receivingSite.value,
                                });
                            rowData.storedAttributes = storedAttributes;
                            rowData.storedDimensions = storedDimensions;
                        }
                    }
                    this.lines.addOrUpdateRecordValue(rowData);
                },
                isDisabled(_rowId, rowValue) {
                    if (rowValue && Number(rowValue._id) < 0) {
                        return false;
                    }
                    if (rowValue && rowValue._id < 0 && rowValue.status !== 'closed') {
                        return false;
                    }
                    return true;
                },
            }),
            ui.nestedFields.text({
                title: 'Item ID',
                bind: { item: { id: true } },
                isReadOnly: true,
            }),
            ui.nestedFields.text({
                title: 'Item description',
                bind: 'itemDescription',
                width: 'large',
                isHiddenOnMainField: true,
                isDisabled(_value, rowData: ui.PartialCollectionValue<StockTransferOrderLineBinding>) {
                    if (this.status.value && rowData.status) {
                        return pageFunctions.isStockTransferOrderLinePropertyDisabled(
                            this.status.value as BaseStatus,
                            rowData.status as BaseStatus,
                        );
                    }
                    return false;
                },
            }),
            ui.nestedFields.image({
                title: 'Image',
                isTitleHidden: true,
                bind: { item: { image: true } },
                width: 'small',
                height: '40px',
                isReadOnly: true,
                isExcludedFromMainField: true,
                isHiddenOnMainField: true,
            }),
            ui.nestedFields.numeric({
                title: 'Quantity in stock unit',
                bind: 'quantity',
                isMandatory: true,
                scale: (_rowId, rowData) => MasterDataUtils.getScaleValue(2, rowData?.unit?.decimalDigits),
                postfix: (_rowId, rowData) => rowData?.unit?.symbol || '',
                isDisabled(_value, rowData: ui.PartialCollectionValue<StockTransferOrderLineBinding>) {
                    if (this.status.value && rowData.status) {
                        return pageFunctions.isStockTransferOrderLinePropertyDisabled(
                            this.status.value as BaseStatus,
                            rowData.status as BaseStatus,
                        );
                    }
                    return false;
                },
                validation(val: number, rowData: StockTransferOrderLineBinding) {
                    if (val <= 0) {
                        return ui.localize(
                            '@sage/xtrem-supply-chain/pages__stock_transfer_order_line_panel__quantity_in_stock_unit_error_negative',
                            'You need to enter a quantity higher than 0.',
                        );
                    }
                    if (val > +rowData.availableQuantityInStockUnit) {
                        return ui.localize(
                            '@sage/xtrem-supply-chain/pages__stock_transfer_order_line_panel__quantity_in_stock_unit_error_not_available',
                            'You need to enter a quantity equal to or less than the quantity available in stock.',
                        );
                    }
                    if (rowData.status === 'inProgress') {
                        this.$.loader.isHidden = false;
                        if (Number(rowData.totalQuantityInStockUnitInAllLinkedShipments) > Number(val)) {
                            return ui.localize(
                                '@sage/xtrem-supply-chain/stock_transfer_order_line__quantity_bellow_already_shipped_quantity',
                                'The stock transfer order line quantity cannot be lower than the quantity already shipped.',
                            );
                        }
                    }
                    // Automatic allocation in progress : we can't decrease the quantity
                    if (
                        val <
                            Number(rowData.quantityAllocatedInStockUnit || 0) +
                                Number(rowData.remainingQuantityToAllocateInStockUnit || 0) &&
                        rowData.allocationRequestStatus === 'inProgress'
                    ) {
                        return ui.localize(
                            '@sage/xtrem-supply-chain/page__stock_transfer_order__auto_allocation_cannot_decrease_quantity',
                            'You can only reduce the quantity of the line after the allocation request is complete.',
                        );
                    }

                    return undefined;
                },
                async onChange(_rowId, rowData: ui.PartialCollectionValue<StockTransferOrderLineBinding>) {
                    rowData.quantityInStockUnit = rowData.quantity;
                    await pageFunctions.getItemSiteAvailableStockQuantity(this, rowData);
                    await pageFunctions.updateCosts(this, rowData);
                    this.lines.addOrUpdateRecordValue(rowData);
                },
            }),
            ui.nestedFields.reference<StockTransferOrder, StockTransferOrderLineBinding, UnitOfMeasure>({
                title: 'Stock unit',
                bind: 'unit',
                node: '@sage/xtrem-master-data/UnitOfMeasure',
                tunnelPage: '@sage/xtrem-master-data/UnitOfMeasure',
                valueField: 'name',
                lookupDialogTitle: 'Select unit',
                helperTextField: 'symbol',
                minLookupCharacters: 1,
                isAutoSelectEnabled: true,
                isMandatory: true,
                isReadOnly: true,
                columns: [
                    ui.nestedFields.text({ bind: 'name', title: 'Name' }),
                    ui.nestedFields.technical({ bind: 'id' }),
                    ui.nestedFields.text({ bind: 'symbol', title: 'Symbol' }),
                    ui.nestedFields.technical({ bind: 'decimalDigits' }),
                ],
                filter(rowData) {
                    const filterArray: Array<string> = [];
                    if (rowData.stockUnit) {
                        filterArray.push(rowData.stockUnit._id);
                        if (rowData.item && rowData.item.stockUnit._id !== rowData.stockUnit._id) {
                            filterArray.push(rowData.item.stockUnit._id);
                        }
                    }
                    return {
                        _id: { _in: filterArray } as any,
                    };
                },
            }),

            ui.nestedFields.numeric({
                title: 'Stock on hand',
                bind: 'stockOnHand',
                isReadOnly: true,
                isHiddenOnMainField: true,
                unit: (_id, rowData) => rowData?.stockUnit,
                unitMode: 'unitOfMeasure',
                isHidden: (_value, rowData) =>
                    (rowData?.item?.type === 'service' || rowData?.item?.type === 'good') &&
                    !rowData?.item?.isStockManaged,
            }),
            ui.nestedFields.numeric({
                title: 'Stock available',
                bind: 'availableQuantityInStockUnit',
                isReadOnly: true,
                isHiddenOnMainField: true,
                unit: (_id, rowData) => rowData?.stockUnit,
                unitMode: 'unitOfMeasure',
                isHidden: (_value, rowData) =>
                    (rowData?.item?.type === 'service' || rowData?.item?.type === 'good') &&
                    !rowData?.item?.isStockManaged,
            }),
            ui.nestedFields.numeric({
                title: 'Stock shortage',
                bind: 'stockShortageInStockUnit',
                isReadOnly: true,
                isExcludedFromMainField: true,
                unit: (_id, rowData) => rowData?.stockUnit,
                unitMode: 'unitOfMeasure',
                isHidden: (_value, rowData) =>
                    (rowData?.item?.type === 'service' || rowData?.item?.type === 'good') &&
                    !rowData?.item?.isStockManaged,
            }),
            ui.nestedFields.reference<StockTransferOrder, StockTransferOrderLineBinding, UnitOfMeasure>({
                title: 'Stock unit',
                bind: 'stockUnit',
                node: '@sage/xtrem-master-data/UnitOfMeasure',
                tunnelPage: '@sage/xtrem-master-data/UnitOfMeasure',
                valueField: 'name',
                helperTextField: 'symbol',
                isMandatory: true,
                isReadOnly: true,
                isHidden: true,
                columns: [
                    ui.nestedFields.technical({ bind: 'name' }),
                    ui.nestedFields.technical({ bind: 'id' }),
                    ui.nestedFields.technical({ bind: 'symbol' }),
                    ui.nestedFields.technical({ bind: 'decimalDigits' }),
                ],
            }),
            ui.nestedFields.numeric<StockTransferOrder, StockTransferOrderLine>({
                title: 'Stock unit conversion factor',
                bind: 'unitToStockUnitConversionFactor',
                isReadOnly: true,
                scale: 10,
                isHidden: true,
            }),
            ui.nestedFields.numeric({
                title: 'Quantity in stock unit',
                bind: 'quantityInStockUnit',
                isMandatory: true,
                isExcludedFromMainField: true,
                scale: (_rowId, rowData) => MasterDataUtils.getScaleValue(2, rowData?.stockUnit?.decimalDigits),
                postfix: (_rowId, rowData) => rowData?.stockUnit?.symbol || '',
                isHidden: true,
            }),
            ui.nestedFields.numeric({
                isReadOnly: true,
                title: 'Stock cost unit',
                bind: 'stockCostUnit',
                unit() {
                    return this.currency.value;
                },
            }),
            ui.nestedFields.numeric({
                isReadOnly: true,
                title: 'Stock cost amount',
                bind: 'stockCostAmount',
                unit() {
                    return this.currency.value;
                },
            }),

            ui.nestedFields.numeric({
                title: 'Quantity in progress',
                bind: 'quantityToShipInProgressInStockUnit',
                isReadOnly: true,
                isExcludedFromMainField: true,
                unit: (_id, rowData) => rowData?.stockUnit,
                unitMode: 'unitOfMeasure',
            }),
            ui.nestedFields.numeric({
                title: 'Shipped quantity',
                bind: 'shippedQuantityInStockUnit',
                isReadOnly: true,
                isExcludedFromMainField: true,
                unit: (_id, rowData) => rowData?.stockUnit,
                unitMode: 'unitOfMeasure',
            }),
            ui.nestedFields.numeric({
                title: 'Quantity to allocate',
                bind: 'remainingQuantityToShipInStockUnit',
                isReadOnly: true,
                isExcludedFromMainField: true,
                unit: (_id, rowData) => rowData?.stockUnit,
                unitMode: 'unitOfMeasure',
            }),
            ui.nestedFields.reference<StockTransferOrder, StockTransferOrderLineBinding, Site>({
                title: 'Stock site',
                bind: 'stockSite',
                node: '@sage/xtrem-system/Site',
                valueField: 'name',
                lookupDialogTitle: 'Select stock site',
                helperTextField: 'id',
                minLookupCharacters: 0,
                isAutoSelectEnabled: true,
                isMandatory: true,
                isHidden: true,
                columns: [
                    ui.nestedFields.text({ bind: 'id', title: 'ID' }),
                    ui.nestedFields.text({ bind: 'name', title: 'Name' }),
                    ui.nestedFields.reference<StockTransferOrder, Site, Company>({
                        node: '@sage/xtrem-system/Company',
                        tunnelPage: '@sage/xtrem-master-data/Company',
                        title: 'Company',
                        bind: 'legalCompany',
                        valueField: 'name',
                        helperTextField: 'id',
                        columns: [
                            ui.nestedFields.text({ bind: 'name', title: 'Name' }),
                            ui.nestedFields.text({ bind: 'id', title: 'ID' }),
                        ],
                    }),
                    ui.nestedFields.technical({ bind: 'description' }),
                    ui.nestedFields.technical({ bind: 'isManufacturing' }),
                ],
            }),
            ui.nestedFields.reference<StockTransferOrder, StockTransferOrderLineBinding, Site>({
                title: 'Receiving site',
                bind: 'receivingSite',
                node: '@sage/xtrem-system/Site',
                valueField: 'name',
                lookupDialogTitle: 'Select receiving site',
                helperTextField: 'id',
                minLookupCharacters: 0,
                isAutoSelectEnabled: true,
                isMandatory: true,
                isHidden: true,
                columns: [
                    ui.nestedFields.text({ bind: 'id', title: 'ID' }),
                    ui.nestedFields.text({ bind: 'name', title: 'Name' }),
                    ui.nestedFields.reference<StockTransferOrder, Site, Company>({
                        node: '@sage/xtrem-system/Company',
                        tunnelPage: '@sage/xtrem-master-data/Company',
                        title: 'Company',
                        bind: 'legalCompany',
                        valueField: 'name',
                        helperTextField: 'id',
                        columns: [
                            ui.nestedFields.text({ bind: 'name', title: 'Name' }),
                            ui.nestedFields.text({ bind: 'id', title: 'ID' }),
                        ],
                    }),
                    ui.nestedFields.technical({ bind: 'description' }),
                    ui.nestedFields.technical({ bind: 'isManufacturing' }),
                ],
            }),
            ui.nestedFields.date({
                title: 'Shipping date',
                bind: 'shippingDate',
                isMandatory: true,
                isReadOnly: true,
                validation(val, rowValue) {
                    // TODO: we will control later the working days of the shipping
                    // site when the information will be available on the site
                    // if (commonDistribution.isNotWorkDay(+this.workDays.value, DateValue.parse(val))) {
                    //    return ui.localize(
                    //        '@sage/xtrem-supply-chain/pages__stock_transfer_order_line_panel__shipping_date_cannot_be_a_not_working_day',
                    //        'The shipping date must fall on a working day.',
                    //    );
                    // }
                    if (this.date.value && val < this.date.value) {
                        return ui.localize(
                            '@sage/xtrem-supply-chain/pages__stock_transfer_order__shipping_date_cannot_be_a_less_order_date',
                            'The shipping date ({{value}}) needs to be the same or later than the order date ({{date}}).',
                            {
                                value: val,
                                date: this.date.value,
                            },
                        );
                    }
                    if (rowValue.doNotShipBeforeDate && val < rowValue.doNotShipBeforeDate) {
                        return ui.localize(
                            '@sage/xtrem-supply-chain/pages__stock_transfer_order__shipping_date_cannot_be_a_earlier_ship_before',
                            "The shipping date ({{value}}) needs to be later than the 'Do-not-ship-before' date ({{doNotShipBeforeDate}}).",
                            {
                                value: val,
                                doNotShipBeforeDate: rowValue.doNotShipBeforeDate,
                            },
                        );
                    }

                    if (rowValue.doNotShipAfterDate && val > rowValue.doNotShipAfterDate) {
                        return ui.localize(
                            '@sage/xtrem-supply-chain/pages__stock_transfer_order__shipping_date_cannot_be_a_later_ship_after',
                            "The shipping date ({{value}}) needs to be earlier than the 'Do-not-ship-after' date ({{doNotShipAfterDate}}).",
                            {
                                value: val,
                                doNotShipAfterDate: rowValue.doNotShipAfterDate,
                            },
                        );
                    }
                    return undefined;
                },

                async onChange(_rowId, rowData) {
                    await pageFunctions.updateExpectedDeliveryDate(this);
                    this.lines.addOrUpdateRecordValue(rowData);
                },
            }),
            ui.nestedFields.date({
                title: 'Do-not-ship-before date',
                bind: 'doNotShipBeforeDate',
                isHiddenOnMainField: true,
                isReadOnly: true,
                isHidden: true,
            }),
            ui.nestedFields.date({
                title: 'Do-not-ship-after date',
                bind: 'doNotShipAfterDate',
                isHiddenOnMainField: true,
                isReadOnly: true,
                isHidden: true,
            }),
            ui.nestedFields.date({
                title: 'Requested delivery date',
                bind: 'requestedDeliveryDate',
                isMandatory: true,
                validation(val) {
                    if (
                        this.workDays.value &&
                        commonDistribution.isNotWorkDay(+this.workDays.value, DateValue.parse(val))
                    ) {
                        return ui.localize(
                            '@sage/xtrem-supply-chain/pages__stock_transfer_order__requested_delivery_cannot_be_a_not_working_day',
                            'The requested delivery date must be on a working day.',
                        );
                    }
                    return undefined;
                },
                async onChange(_rowId, rowData) {
                    await pageFunctions.recomputeShippingDateAndDeliveryDateLine(this, rowData);
                    this.lines.addOrUpdateRecordValue(rowData);
                },
                isReadOnly: true,
            }),
            ui.nestedFields.numeric({
                title: 'Delivery lead time',
                bind: 'deliveryLeadTime',
                isMandatory: true,
                postfix: 'day(s)',
                async onChange(_rowId, rowData) {
                    await pageFunctions.recomputeShippingDateAndDeliveryDateLine(this, rowData);
                    this.lines.addOrUpdateRecordValue(rowData);
                },
                isReadOnly: true,
            }),
            ui.nestedFields.date({
                title: 'Expected delivery date',
                bind: 'expectedDeliveryDate',
                isMandatory: true,
                validation(val) {
                    if (
                        this.workDays.value &&
                        commonDistribution.isNotWorkDay(+this.workDays.value, DateValue.parse(val))
                    ) {
                        return ui.localize(
                            '@sage/xtrem-supply-chain/pages__stock_transfer_order__expected_delivery_cannot_be_a_not_working_day',
                            'The expected delivery date must be on a working day.',
                        );
                    }
                    return undefined;
                },
                isReadOnly: true,
            }),
            ui.nestedFields.reference<StockTransferOrder, StockTransferOrderLineBinding, DeliveryMode>({
                title: 'Delivery mode',
                bind: 'deliveryMode',
                node: '@sage/xtrem-master-data/DeliveryMode',
                tunnelPage: '@sage/xtrem-master-data/DeliveryMode',
                valueField: 'name',
                isMandatory: true,
                columns: [
                    ui.nestedFields.text({ bind: 'name', title: 'Name' }),
                    ui.nestedFields.technical({ bind: 'description' }),
                ],
                isReadOnly: true,
            }),
            ui.nestedFields.label({
                title: 'Allocation status',
                bind: 'allocationStatus',
                optionType: '@sage/xtrem-stock-data/StockAllocationStatus',
                isHidden() {
                    return this.status.value ? ['closed', 'draft'].includes(this.status.value) : false;
                },
                style: (_id, rowData) =>
                    PillColorStock.getLabelColorByStatus('StockAllocationStatus', rowData?.allocationStatus),
            }),
            ui.nestedFields.label({
                title: 'Allocation request status',
                bind: 'allocationRequestStatus',
                optionType: '@sage/xtrem-stock-data/AllocationRequestStatus',
                isHidden() {
                    return ['noRequest', 'completed'].includes(this.allocationRequestStatus.value || '');
                },
                style: (_id, rowData) =>
                    PillColorStock.getLabelColorByStatus('AllocationRequestStatus', rowData?.allocationRequestStatus),
            }),
            ui.nestedFields.label<StockTransferOrder, StockTransferOrderLine>({
                title: 'Stock shortage status',
                bind: 'stockShortageStatus',
                isHiddenOnMainField: true,
                backgroundColor: value =>
                    pillColorDocument.setBooleanStatusColors('stockShortageStatus', value, 'backgroundColor'),
                borderColor: value =>
                    pillColorDocument.setBooleanStatusColors('stockShortageStatus', value, 'borderColor'),
                color: value => pillColorDocument.setBooleanStatusColors('stockShortageStatus', value, 'textColor'),
                map: value =>
                    value === true
                        ? ui.localize(
                              '@sage/xtrem-supply-chain/pages__stock_transfer_order_line_panel__display_shortage_status_true',
                              'Stock shortage',
                          )
                        : ui.localize(
                              '@sage/xtrem-supply-chain/pages__stock_transfer_order_line_panel__display_shortage_status_false',
                              'Stock available',
                          ),
                isHidden: (_value, rowData) =>
                    (rowData?.item?.type === 'service' || rowData?.item?.type === 'good') &&
                    !rowData?.item?.isStockManaged,
            }),
            ui.nestedFields.label({
                title: 'Shipping status',
                bind: 'shippingStatus',
                isHiddenOnMainField: true,
                optionType: '@sage/xtrem-distribution/ShippingStatus',
                style: (_id, rowData) =>
                    pillColorDocument.getLabelColorByStatus('ShippingStatus', rowData?.shippingStatus),
            }),
            ui.nestedFields.label({
                title: 'Receiving status',
                bind: 'receivingStatus',
                isHiddenOnMainField: true,
                optionType: '@sage/xtrem-distribution/ReceivingStatus',
                style: (_id, rowData) =>
                    pillColorDocument.getLabelColorByStatus('ReceivingStatus', rowData?.receivingStatus),
            }),
            ui.nestedFields.numeric({
                title: 'Allocated quantity',
                bind: 'quantityAllocatedInStockUnit',
                isReadOnly: true,
                isExcludedFromMainField: true,
                unit: (_id, rowData) => rowData?.stockUnit,
                unitMode: 'unitOfMeasure',
            }),
            ui.nestedFields.numeric({
                title: 'Remaining quantity',
                bind: 'remainingQuantityToAllocateInStockUnit',
                isReadOnly: true,
                isExcludedFromMainField: true,
                unit: (_id, rowData) => rowData?.stockUnit,
                unitMode: 'unitOfMeasure',
            }),
            ui.nestedFields.technical({
                bind: 'totalQuantityInStockUnitInAllLinkedShipments',
            }),
            ui.nestedFields.reference<StockTransferOrder, StockTransferOrderLineBinding, BusinessEntityAddress>({
                bind: 'shipToCustomerAddress',
                title: 'Ship-to address',
                node: '@sage/xtrem-master-data/BusinessEntityAddress',
                valueField: { name: true },
                columns: [
                    ui.nestedFields.checkbox({
                        bind: { deliveryDetail: { isPrimary: true } },
                        title: 'Primary ship-to address',
                    }),
                    ui.nestedFields.checkbox({ bind: 'isActive', title: 'Active' }),
                    ui.nestedFields.text({ bind: 'name', title: 'Name' }),
                    ui.nestedFields.text({ bind: 'addressLine1', title: 'Address line 1' }),
                    ui.nestedFields.text({ bind: 'addressLine2', title: 'Address line 2' }),
                    ui.nestedFields.text({ bind: 'city', title: 'City' }),
                    ui.nestedFields.text({ bind: 'region', title: 'Region' }),
                    ui.nestedFields.text({ bind: 'postcode', title: 'ZIP code' }),
                    ui.nestedFields.reference<StockTransferOrder, BusinessEntityAddress, Country>({
                        bind: 'country',
                        columns: [
                            ui.nestedFields.text({ bind: 'name', title: 'Name' }),
                            ui.nestedFields.text({ bind: 'id', title: 'ISO 3166-1 alpha-2 code' }),
                            ui.nestedFields.technical({ bind: 'regionLabel' }),
                            ui.nestedFields.technical({ bind: 'zipLabel' }),
                        ],
                    }),
                    ui.nestedFields.text({ bind: 'locationPhoneNumber', title: 'Phone number' }),
                    ui.nestedFields.technical<StockTransferOrder, BusinessEntityAddress, BusinessEntity>({
                        bind: 'businessEntity',
                        node: '@sage/xtrem-master-data/BusinessEntity',
                        nestedFields: [ui.nestedFields.text({ bind: 'name' }), ui.nestedFields.text({ bind: 'id' })],
                    }),
                    ui.nestedFields.reference<StockTransferOrder, BusinessEntityAddress, DeliveryDetail>({
                        bind: 'deliveryDetail',
                        title: 'Delivery detail',
                        node: '@sage/xtrem-master-data/DeliveryDetail',
                        valueField: '_id',
                        columns: [
                            ui.nestedFields.reference<StockTransferOrder, DeliveryDetail, Incoterm>({
                                bind: 'incoterm',
                                title: 'Incoterms® rule',
                            }),
                            ui.nestedFields.reference<StockTransferOrder, DeliveryDetail, DeliveryMode>({
                                bind: 'mode',
                                title: 'Delivery mode',
                                node: '@sage/xtrem-master-data/DeliveryMode',
                                tunnelPage: '@sage/xtrem-master-data/DeliveryMode',
                                valueField: 'name',
                                columns: [
                                    ui.nestedFields.text({ bind: 'name', title: 'Name' }),
                                    ui.nestedFields.technical({ bind: 'description' }),
                                ],
                            }),
                            ui.nestedFields.numeric({
                                bind: 'leadTime',
                                title: 'Delivery lead time',
                                postfix: 'day(s)',
                            }),
                            ui.nestedFields.reference<StockTransferOrder, DeliveryDetail, Site>({
                                bind: 'shipmentSite',
                                title: 'Stock site',
                                node: '@sage/xtrem-system/Site',
                                tunnelPage: '@sage/xtrem-master-data/Site',
                                columns: [
                                    ui.nestedFields.text({ bind: 'name', title: 'Name' }),
                                    ui.nestedFields.text({ bind: 'id', title: 'ID' }),
                                    ui.nestedFields.reference<StockTransferOrder, Site, Company>({
                                        bind: 'legalCompany',
                                        title: 'Company',
                                        node: '@sage/xtrem-system/Company',
                                        tunnelPage: '@sage/xtrem-master-data/Company',
                                        valueField: 'name',
                                        helperTextField: 'id',
                                        columns: [
                                            ui.nestedFields.text({ bind: 'name', title: 'Name' }),
                                            ui.nestedFields.text({ bind: 'id', title: 'ID' }),
                                        ],
                                    }),
                                ],
                            }),
                        ],
                    }),
                ],
                filter() {
                    return this.shipToCustomer.value?.businessEntity?._id
                        ? { businessEntity: { _id: this.shipToCustomer.value?.businessEntity?._id } }
                        : {};
                },
                async onChange(_rowId, rowData: ui.PartialCollectionValue<StockTransferOrderLineBinding>) {
                    if (rowData?.shipToCustomerAddress) {
                        rowData.shipToAddress = rowData.shipToCustomerAddress;
                        await pageFunctions.cascadeAndFetchDefaultsFromShipToAddressLine(this, rowData);
                        this.lines.addOrUpdateRecordValue(rowData);
                    }
                },
                isReadOnly: true,
            }),
            ui.nestedFields.numeric({
                title: 'Actual landed cost in company currency',
                bind: 'actualLandedCostInCompanyCurrency',
                width: 'small',
                isReadOnly: true,
                isHiddenOnMainField: true,
                isHidden() {
                    return (
                        this.status.value === 'draft' ||
                        !this.$.isServiceOptionEnabled('landedCostOption') ||
                        !this.$.isServiceOptionEnabled('landedCostStockTransferOption')
                    );
                },
                unit() {
                    return this.currency.value;
                },
            }),
            ui.nestedFields.technical({ bind: 'computedAttributes' }),
            ui.nestedFields.technical({ bind: 'storedAttributes' }),
            ui.nestedFields.technical({ bind: 'storedDimensions' }),
            ui.nestedFields.technical<StockTransferOrder, StockTransferOrderLine, StockTransferReceiptLine>({
                bind: 'stockTransferReceiptLine',
                node: '@sage/xtrem-supply-chain/StockTransferReceiptLine',
                nestedFields: [ui.nestedFields.technical({ bind: '_id' })],
            }),
            ui.nestedFields.technical<StockTransferOrder, StockTransferOrderLine, Address>({
                bind: 'shipToAddress',
                node: '@sage/xtrem-master-data/Address',
                nestedFields: [
                    ui.nestedFields.technical({ bind: 'name' }),
                    ui.nestedFields.technical({ bind: 'locationPhoneNumber' }),
                    ui.nestedFields.technical({ bind: 'addressLine1' }),
                    ui.nestedFields.technical({ bind: 'addressLine2' }),
                    ui.nestedFields.technical({ bind: 'city' }),
                    ui.nestedFields.technical({ bind: 'region' }),
                    ui.nestedFields.technical({ bind: 'postcode' }),
                    ui.nestedFields.technical({
                        bind: 'country',
                        node: '@sage/xtrem-structure/Country',
                        nestedFields: [
                            ui.nestedFields.technical({ bind: 'name' }),
                            ui.nestedFields.technical({ bind: 'id' }),
                            ui.nestedFields.technical({ bind: 'regionLabel' }),
                            ui.nestedFields.technical({ bind: 'zipLabel' }),
                        ],
                    }),
                    ui.nestedFields.technical({ bind: 'concatenatedAddress' }),
                ],
            }),
        ],
        onRowAdded() {
            pageFunctions.disableSomeHeaderPropertiesIfLines(this);
        },
        optionsMenu: [
            { title: 'All statuses', graphQLFilter: {} },
            { title: 'All open statuses', graphQLFilter: { status: { _ne: 'closed' } } },
            {
                title: 'Allocation required',
                graphQLFilter: { allocationStatus: { _nin: ['notManaged', 'allocated'] } },
            },
        ],
        optionsMenuType: 'dropdown',
        inlineActions: [
            {
                icon: 'box_arrow_left',
                title: 'Open line panel',
                onClick(rowId: string) {
                    this.lines.openSidebar(rowId);
                },
            },
        ],
        dropdownActions: [
            {
                icon: 'unlocked',
                title: 'Open',
                isDisabled(_rowId, rowItem) {
                    if (rowItem.status === 'closed') {
                        return (
                            this.shippingStatus.value !== 'notShipped' ||
                            this.status.value === 'inProgress' ||
                            this.$.isDirty
                        );
                    }
                    return false;
                },
                isHidden(_rowId, rowItem) {
                    return rowItem.status !== 'closed';
                },
                async onClick(rowId) {
                    await actionsFunctionsDistribution.openLineAction({
                        pageInstance: this,
                        lineId: rowId,
                        mutation: actionsFunctions.runOpenLineMutation({
                            pageInstance: this,
                            lineId: rowId,
                        }),
                    });
                },
            },
            {
                icon: 'locked',
                title: 'Close',
                isDisabled(_rowId, rowItem) {
                    return (
                        (this.$.isDirty ||
                            (rowItem.item?.isStockManaged && rowItem.allocationStatus !== 'notAllocated')) ??
                        false
                    );
                },
                isHidden(_rowId, rowItem) {
                    return (
                        rowItem.status === 'closed' ||
                        rowItem.shippingStatus === 'shipped' ||
                        rowItem.allocationRequestStatus === 'inProgress'
                    );
                },
                async onClick(rowId) {
                    await actionsFunctionsDistribution.closeLineAction({
                        pageInstance: this,
                        lineId: rowId,
                        mutation: actionsFunctions.runCloseLineMutation({
                            pageInstance: this,
                            lineId: rowId,
                        }),
                    });
                },
            },
            ui.menuSeparator(),
            {
                icon: 'three_boxes',
                title: 'Allocate stock',
                async onClick(rowId: string, rowItem: ui.PartialCollectionValue<StockTransferOrderLineBinding>) {
                    await actionsFunctions.allocateStockDialog(this, rowId, rowItem);
                },
                isDisabled(_rowId: string, rowItem: ui.PartialCollectionValue<StockTransferOrderLineBinding>) {
                    return (
                        this.$.isDirty ||
                        rowItem.status !== 'pending' ||
                        rowItem.allocationRequestStatus === 'inProgress'
                    );
                },
                isHidden(_rowId: string, rowItem: ui.PartialCollectionValue<StockTransferOrderLineBinding>) {
                    return (
                        rowItem.status !== 'pending' ||
                        !rowItem.item?.isStockManaged ||
                        rowItem.allocationRequestStatus === 'inProgress'
                    );
                },
            },
            {
                icon: 'three_boxes',
                title: 'Projected stock',
                async onClick(_rowId: string, rowItem: ui.PartialCollectionValue<StockTransferOrderLineBinding>) {
                    await actionsFunctions.projectedStockDialog(this, rowItem);
                },
                isHidden(_rowId: string, rowItem: ui.PartialCollectionValue<StockTransferOrderLineBinding>) {
                    const isStockManaged = rowItem?.item?.isStockManaged;
                    return !isStockManaged;
                },
            },
            {
                icon: 'three_boxes',
                title: 'Manage allocations',
                async onClick(rowId: string, rowItem: ui.PartialCollectionValue<StockTransferOrderLineBinding>) {
                    await actionsFunctions.transferAllocationDialog(this, rowId, rowItem);
                    displayButtonsManagement.manageDisplayButtonAllOtherActions(this, false);
                },
                isHidden() {
                    return true;
                },
                // isHidden until XT-82150
                // isHidden(_rowId: string, rowItem: ui.PartialCollectionValue<StockTransferOrderLineBinding>) {
                //     const isStockManaged = rowItem?.item?.isStockManaged;
                //     return !isStockManaged || this.status.value === 'closed';
                // },
                isDisabled(_rowId: string, rowItem: ui.PartialCollectionValue<StockTransferOrderLineBinding>) {
                    return (
                        rowItem.allocationStatus === 'allocated' ||
                        this.status.value === 'draft' ||
                        rowItem.shippingStatus.value !== 'notShipped'
                    );
                },
            },
            ui.menuSeparator(),
            {
                icon: 'none',
                title: 'Dimensions',
                async onClick(_rowId, rowItem: ui.PartialCollectionValue<StockTransferOrderLineBinding>) {
                    const rowData = dimensionPanelHelpers.fromPartialToBaseDocumentLineWithAnalytical(rowItem);
                    await MasterDataUtils.applyPanelToLineIfChanged(
                        this.lines,
                        dimensionPanelHelpers.editDisplayDimensions(
                            this,
                            {
                                documentLine: rowData,
                            },
                            {
                                editable: rowItem.status !== 'closed',
                            },
                        ),
                    );
                },
            },
            ui.menuSeparator(),
            {
                icon: 'bin',
                title: 'Delete',
                isDestructive: true,
                isDisabled(_rowId, rowItem) {
                    if (
                        rowItem.status === 'inProgress' ||
                        this.status.value === 'closed' ||
                        (rowItem.status === 'closed' && (rowItem.shippingStatus || 'notShipped') !== 'notShipped') ||
                        (this.$.recordId &&
                            rowItem.item?.isStockManaged &&
                            (rowItem.allocationStatus || 'notAllocated') !== 'notAllocated')
                    ) {
                        return true;
                    }
                    return false;
                },
                isHidden(_rowId, rowItem) {
                    if (rowItem.status === 'closed' && ['shipped', 'notShipped'].includes(rowItem.shippingStatus)) {
                        return true;
                    }
                    return false;
                },
                async onClick(rowId, rowItem) {
                    if (rowItem.allocationRequestStatus === 'inProgress') {
                        throw new Error(
                            ui.localize(
                                '@sage/xtrem-supply-chain/pages__stock_transfer_order__cannot_delete_line_allocation_request_in_progress',
                                'Automatic allocation needs to finish before you can delete the line.',
                            ),
                        );
                    }
                    if (
                        await commonDistribution.confirmDialogWithAcceptButtonText(
                            this,
                            ui.localize(
                                '@sage/xtrem-supply-chain/pages__stock_transfer_order__line_delete_action_dialog_title',
                                'Confirm delete',
                            ),
                            ui.localize(
                                '@sage/xtrem-supply-chain/pages__stock_transfer_order__line_delete_action_dialog_content',
                                'You are about to delete this stock transfer order line. This action cannot be undone.',
                            ),
                            ui.localize('@sage/xtrem-supply-chain/pages-confirm-continue', 'Continue'),
                        )
                    ) {
                        this.lines.removeRecord(rowId);
                        displayButtonsManagement.manageDisplayButtonSelectFromItemSitesAction(this);
                        pageFunctions.disableSomeHeaderPropertiesIfLines(this);
                    }
                },
            },
            {
                icon: 'none',
                title: 'Landed costs',
                isHidden(_rowId, rowItem: ui.PartialCollectionValue<StockTransferOrderLine>) {
                    return (
                        !rowItem.actualLandedCostInCompanyCurrency ||
                        this.status.value === 'draft' ||
                        !this.$.isServiceOptionEnabled('landedCostOption') ||
                        !this.$.isServiceOptionEnabled('landedCostStockTransferOption')
                    );
                },
                async onClick(_rowId, recordValue: ui.PartialCollectionValue<StockTransferOrderLine>) {
                    await this.displayLandedCosts(recordValue.stockTransferReceiptLine);
                },
            },
        ],

        headerBusinessActions() {
            return setOrderOfPageTableHeaderBusinessActions({
                actions: [this.selectFromItemSites],
            });
        },

        mobileCard: {
            image: ui.nestedFields.image({ bind: { item: { image: true } } }),
            title: ui.nestedFields.text({ bind: { item: { name: true } }, title: 'Product', isTitleHidden: true }),
            titleRight: ui.nestedFields.label({
                bind: 'status',
                title: 'Status',
                isTitleHidden: true,
                optionType: '@sage/xtrem-master-data/BaseStatus',
                style: (_id, rowData) => pillColorDocument.getLabelColorByStatus('BaseStatus', rowData?.status),
            }),
            line2: ui.nestedFields.text({ bind: 'itemDescription', title: 'Description', canFilter: false }),
        },
        sidebar: {
            title(_id, recordValue) {
                if (!recordValue || +recordValue._id < 0) {
                    return ui.localize('@sage/xtrem-supply-chain/edit-create-line', 'Add new line');
                }
                return `${recordValue.item.name} - ${recordValue.item.id}`;
            },
            headerDropdownActions: [
                {
                    icon: 'unlocked',
                    title: 'Open',
                    isDisabled(_rowId, rowItem) {
                        if (rowItem.status === 'closed') {
                            return (
                                this.shippingStatus.value !== 'notShipped' ||
                                this.status.value === 'inProgress' ||
                                this.$.isDirty
                            );
                        }
                        return false;
                    },
                    isHidden(_rowId, rowItem) {
                        return rowItem.status !== 'closed';
                    },
                    async onClick(rowId) {
                        await actionsFunctionsDistribution.openLineAction({
                            pageInstance: this,
                            lineId: rowId,
                            mutation: actionsFunctions.runOpenLineMutation({
                                pageInstance: this,
                                lineId: rowId,
                            }),
                        });
                    },
                },
                {
                    icon: 'locked',
                    title: 'Close',
                    isDisabled(_rowId, rowItem) {
                        return (
                            (this.$.isDirty ||
                                (rowItem.item?.isStockManaged && rowItem.allocationStatus !== 'notAllocated')) ??
                            false
                        );
                    },
                    isHidden(_rowId, rowItem) {
                        return (
                            rowItem.status === 'closed' ||
                            rowItem.shippingStatus === 'shipped' ||
                            rowItem.allocationRequestStatus === 'inProgress'
                        );
                    },
                    async onClick(rowId) {
                        await actionsFunctionsDistribution.closeLineAction({
                            pageInstance: this,
                            lineId: rowId,
                            mutation: actionsFunctions.runCloseLineMutation({
                                pageInstance: this,
                                lineId: rowId,
                            }),
                        });
                    },
                },
                ui.menuSeparator(),
                {
                    icon: 'three_boxes',
                    title: 'Projected stock',
                    async onClick(_rowId: string, rowItem: ui.PartialCollectionValue<StockTransferOrderLineBinding>) {
                        await actionsFunctions.projectedStockDialog(this, rowItem);
                    },
                    isHidden(_rowId: string, rowItem: ui.PartialCollectionValue<StockTransferOrderLineBinding>) {
                        const isStockManaged = rowItem?.item?.isStockManaged;
                        return !isStockManaged;
                    },
                },
                {
                    icon: 'three_boxes',
                    title: 'Manage allocations',
                    async onClick(rowId: string, rowItem: ui.PartialCollectionValue<StockTransferOrderLineBinding>) {
                        await actionsFunctions.transferAllocationDialog(this, rowId, rowItem);
                        displayButtonsManagement.manageDisplayButtonAllOtherActions(this, false);
                    },
                    isHidden() {
                        return true;
                    },
                    // isHidden until XT-82150
                    // isHidden(_rowId: string, rowItem: ui.PartialCollectionValue<StockTransferOrderLineBinding>) {
                    //     const isStockManaged = rowItem?.item?.isStockManaged;
                    //     return !isStockManaged || this.status.value === 'closed';
                    // },
                    isDisabled(_rowId: string, rowItem: ui.PartialCollectionValue<StockTransferOrderLineBinding>) {
                        return (
                            rowItem.allocationStatus === 'allocated' ||
                            this.status.value === 'draft' ||
                            rowItem.shippingStatus.value !== 'notShipped'
                        );
                    },
                },
                ui.menuSeparator(),
                {
                    icon: 'none',
                    title: 'Dimensions',
                    async onClick(_rowId, rowItem: ui.PartialCollectionValue<StockTransferOrderLineBinding>) {
                        const rowData = dimensionPanelHelpers.fromPartialToBaseDocumentLineWithAnalytical(rowItem);
                        await MasterDataUtils.applyPanelToLineIfChanged(
                            this.lines,
                            dimensionPanelHelpers.editDisplayDimensions(
                                this,
                                {
                                    documentLine: rowData,
                                },
                                {
                                    editable: rowItem.status !== 'closed',
                                },
                            ),
                        );
                    },
                },
                ui.menuSeparator(),
                {
                    icon: 'bin',
                    title: 'Delete',
                    isDestructive: true,
                    isDisabled(_rowId, rowItem) {
                        if (
                            rowItem.status === 'inProgress' ||
                            this.status.value === 'closed' ||
                            (rowItem.status === 'closed' &&
                                (rowItem.shippingStatus || 'notShipped') !== 'notShipped') ||
                            (this.$.recordId &&
                                rowItem.item?.isStockManaged &&
                                (rowItem.allocationStatus || 'notAllocated') !== 'notAllocated')
                        ) {
                            return true;
                        }
                        return false;
                    },
                    isHidden(_rowId, rowItem) {
                        if (rowItem.status === 'closed' && ['shipped', 'notShipped'].includes(rowItem.shippingStatus)) {
                            return true;
                        }
                        return false;
                    },
                    onClick(id) {
                        this.lines.removeRecord(id);
                    },
                },
                {
                    icon: 'none',
                    title: 'Landed costs',
                    isHidden(_rowId, recordValue: ui.PartialCollectionValue<StockTransferOrderLine>) {
                        return (
                            !recordValue.actualLandedCostInCompanyCurrency ||
                            !this.$.isServiceOptionEnabled('landedCostOption') ||
                            !this.$.isServiceOptionEnabled('landedCostStockTransferOption')
                        );
                    },
                    async onClick(_rowId, recordValue: ui.PartialCollectionValue<StockTransferOrderLine>) {
                        await this.displayLandedCosts(recordValue.stockTransferReceiptLine);
                    },
                },
            ],
            headerQuickActions: [],
            async onRecordOpened(_id, recordValue?: StockTransferOrderLineBinding) {
                if (recordValue) {
                    this.currentSelectedLineId = recordValue._id;
                    this.internalNoteLine.value = recordValue.internalNote?.value;
                    this.externalNoteLine.value = recordValue.externalNote?.value;
                    this.isExternalNoteLine.value = recordValue.isExternalNote;
                    this.externalNoteLine.isDisabled = !recordValue?.isExternalNote || recordValue.status === 'closed';
                    this.internalNoteLine.isDisabled = recordValue.status === 'closed';
                    this.isExternalNoteLine.isDisabled = recordValue.status === 'closed';
                    if (recordValue.shipToAddress) {
                        this.shipToAddressLineDetail.value = recordValue.shipToAddress;
                        this.shipToAddressLineDetail.value.concatenatedAddress = getConcatenatedAddress(
                            recordValue.shipToAddress,
                        );
                    } else {
                        this.shipToAddressLineDetail.value = this.shipToAddress.value;
                        if (this.shipToAddressLineDetail.value && this.shipToAddress.value) {
                            this.shipToAddressLineDetail.value._id = _id;
                            this.shipToAddressLineDetail.value.concatenatedAddress = getConcatenatedAddress(
                                this.shipToAddress.value,
                            );
                        }
                    }

                    if (+recordValue._id > 0) {
                        await pageFunctions.fillStockTransferShipmentLines(this, recordValue._id);
                    } else {
                        recordValue.stockSite = this.stockSite.value as unknown as Site;
                        recordValue.receivingSite = this.receivingSite.value as unknown as Site;
                        recordValue.shippingStatus = 'notShipped';
                        recordValue.requestedDeliveryDate = this.requestedDeliveryDate.value ?? '';
                        recordValue.expectedDeliveryDate = this.expectedDeliveryDate.value ?? '';
                        recordValue.doNotShipBeforeDate = this.doNotShipBeforeDate.value ?? '';
                        recordValue.doNotShipAfterDate = this.doNotShipAfterDate.value ?? '';
                        recordValue.deliveryLeadTime = this.deliveryLeadTime.value ?? 0;
                        recordValue.deliveryMode = this.deliveryMode.value as unknown as DeliveryMode;
                        recordValue.shippingDate = this.shippingDate.value ?? '';

                        if (
                            this._defaultDimensionsAttributes.dimensions !== '{}' ||
                            this._defaultDimensionsAttributes.attributes !== '{}'
                        ) {
                            const line = dimensionPanelHelpers.applyDefaultDimensionsOnNewLineIfNeeded(
                                {} as ui.PartialNodeWithId<StockTransferOrderLine>,
                                this._defaultDimensionsAttributes,
                            );
                            recordValue.storedAttributes = line.storedAttributes;
                            recordValue.storedDimensions = line.storedDimensions;
                        }

                        this.lines.addOrUpdateRecordValue(
                            recordValue as unknown as ExtractEdgesPartial<StockTransferOrderLineBinding>,
                        );
                    }
                    this.allocationFields = ['allocationStatus'];
                    if (!['noRequest', 'completed'].includes(recordValue.allocationRequestStatus || '')) {
                        this.allocationFields.push('allocationRequestStatus');
                    }
                }
            },
            async onRecordConfirmed(_id, recordValue?: StockTransferOrderLineBinding) {
                if (recordValue) {
                    recordValue.internalNote.value = this.internalNoteLine.value ? this.internalNoteLine.value : '';
                    recordValue.externalNote.value = this.externalNoteLine.value ? this.externalNoteLine.value : '';
                    recordValue.isExternalNote = this.isExternalNoteLine.value || false;
                    this.shipToAddressLineDetail.isReadOnly = true;
                    if (this.shipToAddressLineDetail.value) {
                        this.shipToAddressLineDetail.value.concatenatedAddress = getConcatenatedAddress(
                            this.shipToAddressLineDetail.value,
                        );
                    }

                    recordValue.shipToAddress = this.shipToAddressLineDetail.value as unknown as Address;

                    this.lines.addOrUpdateRecordValue(
                        recordValue as unknown as ExtractEdgesPartial<StockTransferOrderLineBinding>,
                    );

                    await pageFunctions.updateCosts(
                        this,
                        recordValue as unknown as ExtractEdgesPartial<StockTransferOrderLineBinding>,
                    );
                }
            },

            layout() {
                return {
                    information: {
                        title: ui.localize(
                            '@sage/xtrem-supply-chain/pages_sidebar_tab_title_information',
                            'Information',
                        ),
                        blocks: {
                            mainBlock: {
                                fields: ['item', 'itemDescription'],
                            },
                            stock: {
                                title: ui.localize('@sage/xtrem-supply-chain/pages_sidebar_block_title_stock', 'Stock'),
                                fields: ['quantity', 'unit', 'quantityInStockUnit', 'stockUnit'],
                            },
                        },
                    },
                    stock: {
                        title: ui.localize('@sage/xtrem-supply-chain/pages_sidebar_tab_title_stock', 'Stock'),
                        blocks: {
                            stockStatus: {
                                isHidden(_rowId, recordValue) {
                                    return (
                                        +(recordValue?._id ?? 0) <= 0 ||
                                        +(recordValue?.stockShortageInStockUnit ?? 0) === 0 ||
                                        displayButtons.isHiddenSidebarStockSection(recordValue)
                                    );
                                },
                                fields: ['stockShortageStatus'],
                            },
                            stockQuantity: {
                                isHidden(_rowId, recordValue) {
                                    return displayButtons.isHiddenSidebarAllocationBlock(recordValue);
                                },
                                fields: ['stockOnHand', 'availableQuantityInStockUnit', 'stockShortageInStockUnit'],
                            },
                            allocationStatus: {
                                isHidden(_rowId, recordValue) {
                                    return displayButtons.isHiddenSidebarAllocationBlock(recordValue);
                                },
                                // allocationFields is built in the event 'onRecordOpened'
                                fields: this.allocationFields,
                            },
                            allocationQuantity: {
                                isHidden(_rowId, recordValue) {
                                    return displayButtons.isHiddenSidebarAllocationBlock(recordValue);
                                },
                                fields: [
                                    'remainingQuantityToShipInStockUnit',
                                    'quantityAllocatedInStockUnit',
                                    'remainingQuantityToAllocateInStockUnit',
                                ],
                            },
                            stockCost: {
                                fields: ['stockCostUnit', 'stockCostAmount', 'actualLandedCostInCompanyCurrency'],
                            },
                        },
                    },
                    address: {
                        title: ui.localize('@sage/xtrem-supply-chain/pages_sidebar_tab_title_address', 'Address'),
                        blocks: {
                            mainBlock: {
                                fields: ['shipToCustomerAddress', this.shipToAddressLineDetail],
                            },
                        },
                    },
                    delivery: {
                        title: ui.localize('@sage/xtrem-supply-chain/pages_sidebar_tab_title_delivery', 'Delivery'),
                        blocks: {
                            mainBlock: {
                                fields: ['shippingStatus'],
                            },
                            mainBlock2: {
                                fields: ['shippingDate'],
                            },
                            mainBlock3: {
                                fields: ['doNotShipBeforeDate', 'doNotShipAfterDate'],
                            },
                            mainBlock4: {
                                title: ui.localize(
                                    '@sage/xtrem-supply-chain/pages_sidebar_block_title_delivery',
                                    'Delivery',
                                ),
                                fields: [
                                    'requestedDeliveryDate',
                                    'deliveryLeadTime',
                                    'expectedDeliveryDate',
                                    'deliveryMode',
                                ],
                            },
                        },
                    },
                    progress: {
                        title: ui.localize('@sage/xtrem-supply-chain/pages_sidebar_tab_title_progress', 'Progress'),
                        blocks: {
                            shipping: {
                                fields: [
                                    'shippingStatus',
                                    'receivingStatus',
                                    'quantityInStockUnit',
                                    this.linkToStockTransferShipmentLine,
                                ],
                            },
                        },
                    },
                    notes: {
                        title: ui.localize('@sage/xtrem-supply-chain/pages_sidebar_tab_title_line_notes', 'Line notes'),
                        blocks: {
                            notesBlock: {
                                fields: [this.internalNoteLine, this.isExternalNoteLine, this.externalNoteLine],
                            },
                        },
                    },
                };
            },
        },
    })
    lines: ui.fields.Table<StockTransferOrderLineBinding>;

    @ui.decorators.section<StockTransferOrder>({
        title: 'Information',
    })
    informationSection: ui.containers.Section;

    @ui.decorators.block<StockTransferOrder>({
        parent() {
            return this.informationSection;
        },
        width: 'large',
        title: 'Information',
        isTitleHidden: true,
    })
    informationBlock: ui.containers.Block;

    @ui.decorators.section<StockTransferOrder>({
        title: 'Header section',
        isTitleHidden: true,
    })
    headerSection: ui.containers.Section;

    @ui.decorators.block<StockTransferOrder>({
        parent() {
            return this.headerSection;
        },
        width: 'large',
    })
    headerBlock: ui.containers.Block;

    @ui.decorators.tile<StockTransferOrder>({
        parent() {
            return this.headerSection;
        },
    })
    tileContainer: ui.containers.Tile;

    @ui.decorators.countField<StockTransferOrder>({
        parent() {
            return this.tileContainer;
        },
        bind: 'lines',
        title: 'Number of items',
        size: 'medium',
    })
    stockTransferOrderLineCount: ui.fields.Count;

    @ui.decorators.dateField<StockTransferOrder>({
        parent() {
            return this.tileContainer;
        },
        title: 'Expected delivery date',
        bind: 'expectedDeliveryDate',
        width: 'medium',
    })
    expectedDeliveryDateHeader: ui.fields.Date;

    @ui.decorators.checkboxField<StockTransferOrder>({
        parent() {
            return this.informationBlock;
        },
        title: 'Sent',
        isDisabled: true,
        isHidden: true, // not for now
    })
    isSent: ui.fields.Checkbox;

    @ui.decorators.labelField<StockTransferOrder>({
        parent() {
            return this.informationBlock;
        },
        bind: 'allocationStatus',
        title: 'Stock allocation status',
        width: 'large',
        optionType: '@sage/xtrem-stock-data/StockAllocationStatus',
        isHidden() {
            return this.status.value ? ['draft', 'closed'].includes(this.status.value) : false;
        },
        style() {
            return PillColorStock.getLabelColorByStatus('StockAllocationStatus', this.allocationStatus.value);
        },
    })
    allocationStatus: ui.fields.Label<StockAllocationStatus>;

    @ui.decorators.labelField<StockTransferOrder>({
        parent() {
            return this.informationBlock;
        },
        title: 'Allocation request status',
        optionType: '@sage/xtrem-stock-data/AllocationRequestStatus',
        isHidden() {
            return ['noRequest', 'completed'].includes(this.allocationRequestStatus.value || '');
        },
        style() {
            return PillColorStock.getLabelColorByStatus('AllocationRequestStatus', this.allocationRequestStatus.value);
        },
    })
    allocationRequestStatus: ui.fields.Label<AllocationRequestStatus>;

    @ui.decorators.referenceField<StockTransferOrder, Currency>({
        parent() {
            return this.informationBlock;
        },
        title: 'Transaction currency',
        valueField: 'name',
        helperTextField: 'id',
        isMandatory: true,
        isReadOnly: true,
        isHidden: true,
    })
    currency: ui.fields.Reference<Currency>;

    @ui.decorators.textField<StockTransferOrder>({
        parent() {
            return this.informationBlock;
        },
        title: 'Exchange rate',
        isReadOnly: true,
        isHidden: true,
    })
    rateDescription: ui.fields.Text;

    @ui.decorators.referenceField<StockTransferOrder, BusinessEntityAddress>({
        parent() {
            return this.informationBlock;
        },
        node: '@sage/xtrem-master-data/BusinessEntityAddress',
        title: 'Ship-to address',
        lookupDialogTitle: 'Select ship-to address',
        valueField: 'name',
        isHidden: true,
        columns: [
            ui.nestedFields.checkbox({ bind: 'isActive', title: 'Active' }),
            ui.nestedFields.text({ bind: 'name', title: 'Name' }),
            ui.nestedFields.text({ bind: 'addressLine1', title: 'Address line 1' }),
            ui.nestedFields.text({ bind: 'addressLine2', title: 'Address line 2' }),
            ui.nestedFields.text({ bind: 'city', title: 'City' }),
            ui.nestedFields.text({ bind: 'region', title: 'Region' }),
            ui.nestedFields.text({ bind: 'postcode', title: 'ZIP code' }),
            ui.nestedFields.reference<StockTransferOrder, BusinessEntityAddress, Country>({
                bind: 'country',

                columns: [
                    ui.nestedFields.text({ bind: 'name', title: 'Name' }),
                    ui.nestedFields.text({ bind: 'id', title: 'ISO 3166-1 alpha-2 code' }),
                    ui.nestedFields.technical({ bind: 'regionLabel' }),
                    ui.nestedFields.technical({ bind: 'zipLabel' }),
                ],
            }),
            ui.nestedFields.text({ bind: 'locationPhoneNumber', title: 'Phone number' }),
            ui.nestedFields.technical<StockTransferOrder, BusinessEntityAddress, BusinessEntity>({
                bind: 'businessEntity',
                node: '@sage/xtrem-master-data/BusinessEntity',
                nestedFields: [ui.nestedFields.technical({ bind: 'name' }), ui.nestedFields.technical({ bind: 'id' })],
            }),
        ],
        filter() {
            return this.shipToCustomer.value?.businessEntity?.id
                ? { businessEntity: { id: this.shipToCustomer.value.businessEntity.id } }
                : {};
        },
        async onChange() {
            await pageFunctions.fetchDefaultsFromShipToAddress(this);
        },
    })
    shipToLinkedAddress: ui.fields.Reference<BusinessEntityAddress>;

    @ui.decorators.section<StockTransferOrder>({
        title: 'Shipping',
    })
    shippingSection: ui.containers.Section;

    @ui.decorators.block<StockTransferOrder>({
        parent() {
            return this.shippingSection;
        },
        width: 'large',
        title: 'Shipping',
        isTitleHidden: true,
    })
    shippingSectionShippingBlock: ui.containers.Block;

    @ui.decorators.vitalPodField<StockTransferOrder, Address>({
        parent() {
            return this.shippingSection;
        },
        node: '@sage/xtrem-master-data/Address',
        title: 'Ship-to address',
        width: 'small',
        onAddButtonClick() {
            if (this.shipToLinkedAddress.value) {
                const { ...values } = { ...this.shipToLinkedAddress.value };
                delete values.businessEntity;
                return { ...values, _id: null };
            }
            return {};
        },
        dropdownActions: [
            {
                icon: 'lookup',
                title: 'Replace',
                onClick() {
                    this.shipToLinkedAddress.openDialog();
                },
                isDisabled() {
                    return this.status.value === 'closed';
                },
            },
            {
                icon: 'edit',
                title: 'Edit',
                onClick() {
                    this.shipToAddress.isReadOnly = false;
                },
                isDisabled() {
                    return this.status.value === 'closed' || !this.shipToAddress.isReadOnly;
                },
            },
            {
                icon: 'tick',
                title: 'Read-only',
                onClick() {
                    this.shipToAddress.isReadOnly = true;
                    if (this.shipToAddress.value) {
                        this.shipToAddress.value.concatenatedAddress = getConcatenatedAddress(this.shipToAddress.value);
                    }
                },
                isDisabled() {
                    return this.status.value === 'closed' || this.shipToAddress.isReadOnly;
                },
            },
        ],
        isReadOnly: true,
        columns: [
            ui.nestedFields.text({
                bind: 'name',
                title: 'Name',
                isHidden() {
                    return this.shipToAddress.isReadOnly === true;
                },
            }),
            ui.nestedFields.textArea({
                title: 'Bill-to address',
                isTitleHidden: true,
                bind: 'concatenatedAddress',
                width: 'large',
                isHidden() {
                    return this.shipToAddress.isReadOnly === false;
                },
            }),
            ui.nestedFields.text({ bind: 'locationPhoneNumber', title: 'Phone number' }),
            ui.nestedFields.text({
                bind: 'addressLine1',
                title: 'Address line 1',
                width: 'large',
                isHidden() {
                    return this.shipToAddress.isReadOnly === true;
                },
            }),
            ui.nestedFields.text({
                bind: 'addressLine2',
                title: 'Address line 2',
                width: 'large',
                isHidden() {
                    return this.shipToAddress.isReadOnly === true;
                },
            }),
            ui.nestedFields.text({
                bind: 'city',
                title: 'City',
                isHidden() {
                    return this.shipToAddress.isReadOnly === true;
                },
            }),
            ui.nestedFields.text({
                bind: 'region',
                title() {
                    return getCountryRegionTitle(this.shipToAddress.value);
                },
                isHidden() {
                    return this.shipToAddress.isReadOnly === true;
                },
            }),
            ui.nestedFields.text({
                bind: 'postcode',
                title() {
                    return getCountryPostCodeTitle(this.shipToAddress.value);
                },
                isHidden() {
                    return this.shipToAddress.isReadOnly === true;
                },
            }),
            ui.nestedFields.reference({
                bind: 'country',

                columns: [
                    ui.nestedFields.text({ bind: 'name', title: 'Name' }),
                    ui.nestedFields.text({ bind: 'id', title: 'ISO 3166-1 alpha-2 code' }),
                    ui.nestedFields.technical({ bind: 'regionLabel' }),
                    ui.nestedFields.technical({ bind: 'zipLabel' }),
                ],
                isHidden() {
                    return this.shipToAddress.isReadOnly === true;
                },
            }),
        ],
    })
    shipToAddress: ui.fields.VitalPod;

    @ui.decorators.referenceField<StockTransferOrder, BusinessEntityAddress>({
        parent() {
            return this.shippingSectionShippingBlock;
        },
        node: '@sage/xtrem-master-data/BusinessEntityAddress',
        title: 'Ship-to address',
        lookupDialogTitle: 'Select ship-to address',
        valueField: 'name',
        isHidden: true,
        columns: [
            ui.nestedFields.checkbox({
                bind: { deliveryDetail: { isPrimary: true } },
                title: 'Primary address',
            }),
            ui.nestedFields.checkbox({ bind: 'isActive', title: 'Active' }),
            ui.nestedFields.text({ bind: 'name', title: 'Name' }),
            ui.nestedFields.text({ bind: 'addressLine1', title: 'Address line 1' }),
            ui.nestedFields.text({ bind: 'addressLine2', title: 'Address line 2' }),
            ui.nestedFields.text({ bind: 'city', title: 'City' }),
            ui.nestedFields.text({ bind: 'region', title: 'Region' }),
            ui.nestedFields.text({ bind: 'postcode', title: 'ZIP code' }),
            ui.nestedFields.text({ bind: 'locationPhoneNumber', title: 'Phone number' }),
            ui.nestedFields.reference<StockTransferOrder, BusinessEntityAddress, Country>({
                bind: 'country',

                columns: [
                    ui.nestedFields.text({ bind: 'name', title: 'Name' }),
                    ui.nestedFields.text({ bind: 'id', title: 'ISO 3166-1 alpha-2 code' }),
                    ui.nestedFields.technical({ bind: 'regionLabel' }),
                    ui.nestedFields.technical({ bind: 'zipLabel' }),
                ],
            }),
            ui.nestedFields.technical<StockTransferOrder, BusinessEntityAddress, BusinessEntity>({
                bind: 'businessEntity',
                node: '@sage/xtrem-master-data/BusinessEntity',
                nestedFields: [ui.nestedFields.technical({ bind: 'name' }), ui.nestedFields.technical({ bind: 'id' })],
            }),
            ui.nestedFields.reference<StockTransferOrder, BusinessEntityAddress, DeliveryDetail>({
                bind: 'deliveryDetail',
                title: 'Ship-to address',
                node: '@sage/xtrem-master-data/DeliveryDetail',
                valueField: '_id',
                columns: [
                    ui.nestedFields.numeric({ bind: 'leadTime', title: 'Delivery lead time', postfix: 'day(s)' }),
                    ui.nestedFields.reference<StockTransferOrder, DeliveryDetail, Incoterm>({
                        bind: 'incoterm',
                        title: 'Incoterms® rule',
                        node: '@sage/xtrem-master-data/Incoterm',
                        valueField: 'name',
                        helperTextField: 'id',
                        columns: [
                            ui.nestedFields.text({ bind: 'name', title: 'Name' }),
                            ui.nestedFields.text({ bind: 'id', title: 'ID' }),
                        ],
                    }),
                    ui.nestedFields.reference<StockTransferOrder, DeliveryDetail, DeliveryMode>({
                        bind: 'mode',
                        title: 'Delivery mode',
                        node: '@sage/xtrem-master-data/DeliveryMode',
                        valueField: 'name',
                        columns: [ui.nestedFields.text({ bind: 'name', title: 'Name' })],
                    }),
                    ui.nestedFields.reference<StockTransferOrder, DeliveryDetail, Site>({
                        bind: 'shipmentSite',
                        title: 'Stock site',
                        node: '@sage/xtrem-system/Site',
                        valueField: 'name',
                        helperTextField: 'id',
                        columns: [
                            ui.nestedFields.text({ bind: 'name', title: 'Name' }),
                            ui.nestedFields.text({ bind: 'id', title: 'ID' }),
                            ui.nestedFields.reference<StockTransferOrder, Site, Company>({
                                bind: 'legalCompany',
                                title: 'Company',
                                node: '@sage/xtrem-system/Company',
                                tunnelPage: '@sage/xtrem-master-data/Company',
                                valueField: 'name',
                                helperTextField: 'id',
                                columns: [
                                    ui.nestedFields.text({ bind: 'name', title: 'Name' }),
                                    ui.nestedFields.text({ bind: 'id', title: 'ID' }),
                                ],
                            }),
                        ],
                    }),
                ],
            }),
        ],
        filter() {
            // TODO be able to do { _not: { _eq: null } } & then delete the any
            // https://jira.sage.com/browse/XT-67917
            return this.shipToCustomer.value?.businessEntity?._id
                ? {
                      businessEntity: { _id: { _eq: this.shipToCustomer.value.businessEntity._id } },
                      deliveryDetail: { _not: { _eq: null } } as any,
                  }
                : {};
        },
        async onChange() {
            await pageFunctions.cascadeAndFetchDefaultsFromShipToAddress(this);
            displayButtonsManagement.manageDisplayLinePhantomRow(this);
        },
    })
    shipToCustomerAddress: ui.fields.Reference<BusinessEntityAddress>;

    @ui.decorators.referenceField<StockTransferOrder, Site>({
        parent() {
            return this.shippingSectionShippingBlock;
        },
        title: 'Stock site',
        lookupDialogTitle: 'Select stock site',
        minLookupCharacters: 0,
        isAutoSelectEnabled: true,
        fetchesDefaults: true,
        isMandatory: true,
        isHidden: true,
        columns: [
            ui.nestedFields.text({ bind: { businessEntity: { name: true } }, title: 'Name' }),
            ui.nestedFields.text({ bind: { businessEntity: { id: true } }, title: 'ID' }),
            ui.nestedFields.reference<StockTransferOrder, Site, Company>({
                node: '@sage/xtrem-system/Company',
                tunnelPage: '@sage/xtrem-master-data/Company',
                title: 'Company',
                bind: 'legalCompany',
                valueField: 'name',
                helperTextField: 'id',
                columns: [
                    ui.nestedFields.text({ bind: 'name', title: 'Name' }),
                    ui.nestedFields.text({ bind: 'id', title: 'ID' }),
                ],
            }),
            ui.nestedFields.technical({ bind: 'id' }),
            ui.nestedFields.technical({ bind: 'name' }),
            ui.nestedFields.technical({ bind: 'description' }),
            ui.nestedFields.technical({ bind: 'isInventory' }),
            ui.nestedFields.technical<StockTransferOrder, Site, BusinessEntityAddress>({
                bind: 'primaryAddress',
                node: '@sage/xtrem-master-data/BusinessEntityAddress',
                nestedFields: [
                    ui.nestedFields.technical<StockTransferOrder, BusinessEntityAddress, Country>({
                        bind: 'country',
                        node: '@sage/xtrem-structure/Country',
                        nestedFields: [
                            ui.nestedFields.technical({ bind: 'name' }),
                            ui.nestedFields.technical({ bind: 'id' }),
                        ],
                    }),
                ],
            }),
        ],
        filter() {
            return {
                isInventory: true,
            };
        },
        async onChange() {
            displayButtonsManagement.manageDisplayLinePhantomRow(this);
            if (this.stockSite.value && this.receivingSite.value) {
                this._defaultDimensionsAttributes = await initDefaultDimensions({
                    page: this,
                    dimensionDefinitionLevel: 'intersiteTransferOrder',
                    site: this.stockSite.value,
                    customer: this.shipToCustomer.value,
                    supplier: this.supplier.value,
                    receivingSite: this.receivingSite.value,
                });
            }
        },
    })
    stockSite: ui.fields.Reference<Site>;

    @ui.decorators.referenceField<StockTransferOrder, Incoterm>({
        parent() {
            return this.shippingSectionShippingBlock;
        },
        title: 'Incoterms® rule',
        lookupDialogTitle: 'Select Incoterms® rule',
        minLookupCharacters: 0,
        isAutoSelectEnabled: true,
        isHidden: true,
    })
    incoterm: ui.fields.Reference<Incoterm>;

    @ui.decorators.referenceField<StockTransferOrder, DeliveryMode>({
        parent() {
            return this.shippingSectionShippingBlock;
        },
        title: 'Delivery mode',
        lookupDialogTitle: 'Select delivery mode',
        isAutoSelectEnabled: true,
        isMandatory: true,
    })
    deliveryMode: ui.fields.Reference<DeliveryMode>;

    @ui.decorators.dateField<StockTransferOrder>({
        parent() {
            return this.shippingSectionShippingBlock;
        },
        title: 'Requested delivery date',
        isMandatory: true,
        validation(val) {
            if (this.workDays.value && commonDistribution.isNotWorkDay(+this.workDays.value, DateValue.parse(val))) {
                return ui.localize(
                    '@sage/xtrem-supply-chain/pages__stock_transfer_order__requested_delivery_cannot_be_a_not_working_day',
                    'The requested delivery date must be on a working day.',
                );
            }
            return undefined;
        },
        async onChange() {
            await pageFunctions.recomputeShippingDateAndDeliveryDate(this);
        },
    })
    requestedDeliveryDate: ui.fields.Date;

    @ui.decorators.numericField<StockTransferOrder>({
        parent() {
            return this.shippingSectionShippingBlock;
        },
        title: 'Delivery lead time',
        isMandatory: true,
        async onChange() {
            await pageFunctions.recomputeShippingDateAndDeliveryDate(this);
        },
        postfix: 'day(s)',
    })
    deliveryLeadTime: ui.fields.Numeric;

    @ui.decorators.numericField<StockTransferOrder>({
        parent() {
            return this.shippingSectionShippingBlock;
        },
        title: 'Work days',
        isReadOnly: true,
        isHidden: true,
    })
    workDays: ui.fields.Numeric;

    @ui.decorators.dateField<StockTransferOrder>({
        parent() {
            return this.shippingSectionShippingBlock;
        },
        title: 'Shipping date',
        isMandatory: true,
        validation(val) {
            // TODO: we will control later the working days of the shipping
            // site when the information will be available on the site
            // if (commonDistribution.isNotWorkDay(+this.workDays.value, new Date(val))) {
            //    return ui.localize(
            //        '@sage/xtrem-supply-chain/pages__stock_transfer_order__shipping_date_cannot_be_a_not_working_day',
            //        'The shipping date must fall on a working day.',
            //     );
            // }
            if (this.date.value && val < this.date.value) {
                return ui.localize(
                    '@sage/xtrem-supply-chain/pages__stock_transfer_order__shipping_date_cannot_be_a_less_order_date',
                    'The shipping date ({{value}}) needs to be the same or later than the order date ({{date}}).',
                    {
                        value: val,
                        date: this.date.value,
                    },
                );
            }
            if (this.doNotShipBeforeDate.value && val < this.doNotShipBeforeDate.value) {
                return ui.localize(
                    '@sage/xtrem-supply-chain/pages__stock_transfer_order__shipping_date_cannot_be_a_earlier_ship_before',
                    "The shipping date ({{value}}) needs to be later than the 'Do-not-ship-before' date ({{doNotShipBeforeDate}}).",
                    {
                        value: val,
                        doNotShipBeforeDate: this.doNotShipBeforeDate.value,
                    },
                );
            }

            if (this.doNotShipAfterDate.value && val > this.doNotShipAfterDate.value) {
                return ui.localize(
                    '@sage/xtrem-supply-chain/pages__stock_transfer_order__shipping_date_cannot_be_a_later_ship_after',
                    "The shipping date ({{value}}) needs to be earlier than the 'Do-not-ship-after' date ({{doNotShipAfterDate}}).",
                    {
                        value: val,
                        doNotShipAfterDate: this.doNotShipAfterDate.value,
                    },
                );
            }
            return undefined;
        },
        async onChange() {
            await pageFunctions.updateExpectedDeliveryDate(this);
        },
    })
    shippingDate: ui.fields.Date;

    @ui.decorators.dateField<StockTransferOrder>({
        parent() {
            return this.shippingSectionShippingBlock;
        },
        title: 'Expected delivery date',
        isMandatory: true,
        validation(val) {
            if (this.workDays.value && commonDistribution.isNotWorkDay(+this.workDays.value, DateValue.parse(val))) {
                return ui.localize(
                    '@sage/xtrem-supply-chain/pages__stock_transfer_order__expected_delivery_cannot_be_a_not_working_day',
                    'The expected delivery date must be on a working day.',
                );
            }
            return undefined;
        },
    })
    expectedDeliveryDate: ui.fields.Date;

    @ui.decorators.separatorField<StockTransferOrder>({
        parent() {
            return this.shippingSectionShippingBlock;
        },
        isFullWidth: true,
        isHidden: true,
    })
    expectedDeliveryDateSeparator: ui.fields.Separator;

    @ui.decorators.dateField<StockTransferOrder>({
        parent() {
            return this.shippingSectionShippingBlock;
        },
        title: 'Do-not-ship-before date',
        isHidden: true,
    })
    doNotShipBeforeDate: ui.fields.Date;

    @ui.decorators.dateField<StockTransferOrder>({
        parent() {
            return this.shippingSectionShippingBlock;
        },
        title: 'Do-not-ship-after date',
        isHidden: true,
    })
    doNotShipAfterDate: ui.fields.Date;

    @ui.decorators.separatorField<StockTransferOrder>({
        parent() {
            return this.shippingSectionShippingBlock;
        },
        isFullWidth: true,
        isInvisible: true,
    })
    shippingStatusSeparator: ui.fields.Separator;

    @ui.decorators.labelField<StockTransferOrder>({
        parent() {
            return this.shippingSectionShippingBlock;
        },
        title: 'Shipping status',
        isHidden: true,
        optionType: '@sage/xtrem-distribution/ShippingStatus',
    })
    shippingStatus: ui.fields.Label<ShippingStatus>;

    @ui.decorators.labelField<StockTransferOrder>({
        parent() {
            return this.shippingSectionShippingBlock;
        },
        title: 'Receiving status',
        bind: 'receivingStatus',
        isHidden: true,
        optionType: '@sage/xtrem-distribution/ReceivingStatus',
    })
    receivingStatus: ui.fields.Label<ReceivingStatus>;

    @ui.decorators.section<StockTransferOrder>({ title: 'Landed costs', isTitleHidden: true })
    landedCostsSection: ui.containers.Section;

    @ui.decorators.block<StockTransferOrder>({
        parent() {
            return this.landedCostsSection;
        },
        title: 'Total in company currency',
        width: 'large',
    })
    landedCostsSectionBlock: ui.containers.Block;

    @ui.decorators.numericField<StockTransferOrder>({
        title: 'Actual landed costs',
        isTransient: true,
        parent() {
            return this.landedCostsSectionBlock;
        },
        unit() {
            return this.currency.value;
        },
        isReadOnly: true,
    })
    totalActualLandedCostsInCompanyCurrency: ui.fields.Numeric;

    @ui.decorators.textField<StockTransferOrder>({ isHidden: true, bind: 'jsonAggregateLandedCostTypes' })
    jsonAggregateLandedCostTypes: ui.fields.Text;

    @ui.decorators.tableField<StockTransferOrder, LandedCostInterfaces.LandedCostTypeSummaryLineBinding>({
        parent() {
            return this.landedCostsSection;
        },
        canSelect: false,
        title: 'Summary by landed cost type',
        isFullWidth: true,
        isTransient: true,
        isReadOnly: true,
        pageSize: 10,
        orderBy: { landedCostType: +1 },
        columns: [
            ui.nestedFields.dropdownList({
                title: 'Type',
                bind: 'landedCostType',
                optionType: '@sage/xtrem-landed-cost/LandedCostType',
            }),
            ui.nestedFields.numeric({
                title: 'Actual cost amount in company currency',
                bind: 'actualCostAmountInCompanyCurrency',
                unit() {
                    return this.currency.value;
                },
            }),
        ],
    })
    landedCosts: ui.fields.Table<LandedCostInterfaces.LandedCostTypeSummaryLineBinding>;

    @ui.decorators.richTextField<StockTransferOrder>({
        isFullWidth: true,
        title: 'Internal line notes',
        capabilities: validCapabilities,
        isTransient: true,
        helperText: 'Notes display on internal documents.',
        isDisabled() {
            return this.status.value === 'posted' || this.status.value === 'closed';
        },
    })
    internalNoteLine: ui.fields.RichText;

    @ui.decorators.switchField<StockTransferOrder>({
        title: 'Add notes to customer document',
        isHidden: true, // not for now
        isFullWidth: true,
        isTransient: true,
        onChange() {
            this.externalNoteLine.isDisabled = !(this.isExternalNoteLine.value || false);
            this.externalNoteLine.value = '';
        },
        isDisabled() {
            return this.status.value === 'posted' || this.status.value === 'closed';
        },
    })
    isExternalNoteLine: ui.fields.Switch;

    @ui.decorators.richTextField<StockTransferOrder>({
        isFullWidth: true,
        title: 'Customer line notes',
        isHidden: true, // not for now
        capabilities: validCapabilities,
        isTransient: true,
        helperText: 'Notes display on customer documents.',
        isDisabled() {
            return !this.isExternalNoteLine.value || this.status.value === 'posted' || this.status.value === 'closed';
        },
    })
    externalNoteLine: ui.fields.RichText;

    @ui.decorators.vitalPodField<StockTransferOrder, Address>({
        node: '@sage/xtrem-master-data/Address',
        title: 'Ship-to address',
        width: 'small',
        isTransient: true,
        onAddButtonClick() {
            return {};
        },
        dropdownActions: [],
        isReadOnly: true,
        columns: [
            ui.nestedFields.text({
                bind: 'name',
                title: 'Name',
                isHidden() {
                    return this.stockTransferOrderLineShipToAddressDetailPanel.isReadOnly === true;
                },
            }),
            ui.nestedFields.textArea({
                title: 'Bill-to address',
                isTitleHidden: true,
                bind: 'concatenatedAddress',
                width: 'large',
                isHidden() {
                    return this.stockTransferOrderLineShipToAddressDetailPanel.isReadOnly === false;
                },
            }),
            ui.nestedFields.text({ bind: 'locationPhoneNumber', title: 'Phone number' }),
            ui.nestedFields.text({
                bind: 'addressLine1',
                title: 'Address line 1',
                width: 'large',
                isHidden() {
                    return this.stockTransferOrderLineShipToAddressDetailPanel.isReadOnly === true;
                },
            }),
            ui.nestedFields.text({
                bind: 'addressLine2',
                title: 'Address line 2',
                width: 'large',
                isHidden() {
                    return this.stockTransferOrderLineShipToAddressDetailPanel.isReadOnly === true;
                },
            }),
            ui.nestedFields.text({
                bind: 'city',
                title: 'City',
                isHidden() {
                    return this.stockTransferOrderLineShipToAddressDetailPanel.isReadOnly === true;
                },
            }),
            ui.nestedFields.text({
                bind: 'region',
                title() {
                    return getCountryRegionTitle(this.shipToCustomerAddress.value);
                },
                isHidden() {
                    return this.stockTransferOrderLineShipToAddressDetailPanel.isReadOnly === true;
                },
            }),
            ui.nestedFields.text({
                bind: 'postcode',
                title() {
                    return getCountryPostCodeTitle(this.shipToCustomerAddress.value);
                },
                isHidden() {
                    return this.stockTransferOrderLineShipToAddressDetailPanel.isReadOnly === true;
                },
            }),
            ui.nestedFields.reference({
                bind: 'country',

                columns: [
                    ui.nestedFields.text({ bind: 'name', title: 'Name' }),
                    ui.nestedFields.text({ bind: 'id', title: 'ISO 3166-1 alpha-2 code' }),
                    ui.nestedFields.technical({ bind: 'regionLabel' }),
                    ui.nestedFields.technical({ bind: 'zipLabel' }),
                ],
                isHidden() {
                    return this.stockTransferOrderLineShipToAddressDetailPanel.isReadOnly === true;
                },
            }),
        ],
    })
    stockTransferOrderLineShipToAddressDetailPanel: ui.fields.VitalPod<Address>;

    @ui.decorators.tableField<StockTransferOrder, StockTransferShipmentLine>({
        title: 'Shipment lines',
        isTitleHidden: true,
        isTransient: true,
        canSelect: false,
        node: '@sage/xtrem-supply-chain/StockTransferShipmentLine',
        orderBy: { _sortValue: +1 },
        columns: [
            ui.nestedFields.link({
                title: 'Shipment number',
                bind: '_id', // Dummy link to a string; value is set in the map, as we cannot use valueField like syntax in bind of link field type
                width: 'large',
                isTransient: true,
                map(_fieldValue, rowData) {
                    return `${rowData.document.number}`;
                },
                page: '@sage/xtrem-supply-chain/StockTransferShipment',
                queryParameters(_value, rowData) {
                    return {
                        _id: rowData?.document._id ?? '',
                    };
                },
            }),
            ui.nestedFields.technical<StockTransferOrder, StockTransferShipmentLine, StockTransferShipment>({
                bind: 'document',
                node: '@sage/xtrem-supply-chain/StockTransferShipment',
                nestedFields: [ui.nestedFields.technical({ bind: 'number' })],
            }),
            ui.nestedFields.reference<StockTransferOrder, StockTransferShipmentLine, Item>({
                bind: 'item',
                node: '@sage/xtrem-master-data/Item',
                valueField: 'name',
                helperTextField: 'id',
                isHidden: true,
                columns: [
                    ui.nestedFields.text({ bind: 'name' }),
                    ui.nestedFields.text({ bind: 'id' }),
                    ui.nestedFields.text({ bind: 'description' }),
                ],
            }),
            ui.nestedFields.label({
                title: 'Shipment status',
                bind: 'status',
                isTitleHidden: true,
                optionType: '@sage/xtrem-master-data/BaseStatus',
                style: (_id, rowData) => pillColorDocument.getLabelColorByStatus('BaseStatus', rowData?.status),
            }),
            ui.nestedFields.reference<StockTransferOrder, StockTransferShipmentLine, UnitOfMeasure>({
                bind: 'stockUnit',
                node: '@sage/xtrem-master-data/UnitOfMeasure',
                valueField: 'name',
                helperTextField: 'symbol',
                isHidden: true,
                columns: [
                    ui.nestedFields.text({ bind: 'name' }),
                    ui.nestedFields.technical({ bind: 'id' }),
                    ui.nestedFields.text({ bind: 'symbol' }),
                    ui.nestedFields.technical({ bind: 'decimalDigits' }),
                ],
            }),
            ui.nestedFields.numeric({
                title: 'Quantity in stock unit',
                bind: 'quantityInStockUnit',
                isReadOnly: true,
                width: 'large',
                unit: (_id, rowData) => rowData?.stockUnit,
                unitMode: 'unitOfMeasure',
            }),
        ],
    })
    linkToStockTransferShipmentLine: ui.fields.Table<StockTransferShipmentLine>;

    @ui.decorators.section<StockTransferOrder>({ title: 'Notes', isTitleHidden: true })
    notesSection: ui.containers.Section;

    @ui.decorators.block<StockTransferOrder>({
        title: 'Notes',
        isTitleHidden: true,
        parent() {
            return this.notesSection;
        },
    })
    noteBlock: ui.containers.Block;

    @ui.decorators.richTextField<StockTransferOrder>({
        parent() {
            return this.noteBlock;
        },
        isFullWidth: true,
        title: 'Internal notes',
        capabilities: validCapabilities,
        helperText: 'Notes display on internal documents.',
        isDisabled() {
            return this.status.value === 'closed';
        },
    })
    internalNote: ui.fields.RichText;

    @ui.decorators.switchField<StockTransferOrder>({
        parent() {
            return this.noteBlock;
        },
        title: 'Add notes to customer document',
        isHidden: true, // not for now
        onChange() {
            this.externalNote.isDisabled = !this.isExternalNote.value;
            this.externalNote.value = '';
        },
        isDisabled() {
            return this.status.value === 'closed';
        },
        width: 'large',
    })
    isExternalNote: ui.fields.Switch;

    @ui.decorators.richTextField<StockTransferOrder>({
        parent() {
            return this.noteBlock;
        },
        isFullWidth: true,
        title: 'Customer notes',
        isHidden: true, // not for now
        capabilities: validCapabilities,
        helperText: 'Notes display on customer documents.',
        isDisabled() {
            return !this.isExternalNote.value || this.status.value === 'closed';
        },
    })
    externalNote: ui.fields.RichText;

    @ui.decorators.switchField<StockTransferOrder>({
        parent() {
            return this.noteBlock;
        },
        title: 'Repeat the document notes on new documents.',
        isHidden: true, // not for now
        isDisabled() {
            return this.status.value === 'closed';
        },
        width: 'large',
    })
    isTransferHeaderNote: ui.fields.Switch;

    @ui.decorators.switchField<StockTransferOrder>({
        parent() {
            return this.noteBlock;
        },
        title: 'Repeat all the line notes on new documents.',
        isHidden: true, // not for now
        isDisabled() {
            return this.status.value === 'closed';
        },
        width: 'large',
    })
    isTransferLineNote: ui.fields.Switch;

    @ui.decorators.vitalPodField<StockTransferOrder, Address>({
        node: '@sage/xtrem-master-data/Address',
        title: 'Ship-to address',
        isFullWidth: true,
        isTransient: true,
        onAddButtonClick() {
            return {};
        },
        // // not for now
        // dropdownActions: [
        // {
        //     icon: 'lookup',
        //     title: 'Replace',
        //     onClick() {
        //         // rowData.shipToCustomerAddress.openDialog(); // intentionally left commented not for now
        //     },
        //     // isDisabled() {
        //     //     return this.status.value === 'closed';
        //     // },
        //     isDisabled() {
        //         return true;
        //     },
        // },
        // {
        //     icon: 'edit',
        //     title: 'Edit',
        //     onClick() {
        //         this.shipToAddressLineDetail.isReadOnly = false;
        //     },
        // },
        // {
        //     icon: 'tick',
        //     title: 'Read-only',
        //     onClick() {
        //         this.shipToAddressLineDetail.isReadOnly = true;
        //         this.shipToAddressLineDetail.value.concatenatedAddress = getConcatenatedAddress(
        //             this.shipToAddressLineDetail.value,
        //         );
        //     },
        // },
        // ],
        isReadOnly: true,
        columns: [
            ui.nestedFields.text({
                bind: 'name',
                title: 'Name',
                isHidden() {
                    return this.shipToAddressLineDetail.isReadOnly === true;
                },
            }),
            ui.nestedFields.textArea({
                title: 'Bill-to address',
                isTitleHidden: true,
                bind: 'concatenatedAddress',
                width: 'large',
                isHidden() {
                    return this.shipToAddressLineDetail.isReadOnly === false;
                },
            }),
            ui.nestedFields.text({ bind: 'locationPhoneNumber', title: 'Phone number' }),
            ui.nestedFields.text({
                bind: 'addressLine1',
                title: 'Address line 1',
                width: 'large',
                isHidden() {
                    return this.shipToAddressLineDetail.isReadOnly === true;
                },
            }),
            ui.nestedFields.text({
                bind: 'addressLine2',
                title: 'Address line 2',
                width: 'large',
                isHidden() {
                    return this.shipToAddressLineDetail.isReadOnly === true;
                },
            }),
            ui.nestedFields.text({
                bind: 'city',
                title: 'City',
                isHidden() {
                    return this.shipToAddressLineDetail.isReadOnly === true;
                },
            }),
            ui.nestedFields.text({
                bind: 'region',
                title() {
                    return getCountryRegionTitle(this.shipToAddressLineDetail.value);
                },
                isHidden() {
                    return this.shipToAddressLineDetail.isReadOnly === true;
                },
            }),
            ui.nestedFields.text({
                bind: 'postcode',
                title() {
                    return getCountryPostCodeTitle(this.shipToAddressLineDetail.value);
                },
                isHidden() {
                    return this.shipToAddressLineDetail.isReadOnly === true;
                },
            }),
            ui.nestedFields.reference({
                bind: 'country',

                columns: [
                    ui.nestedFields.text({ bind: 'name', title: 'Name' }),
                    ui.nestedFields.text({ bind: 'id', title: 'ISO 3166-1 alpha-2 code' }),
                    ui.nestedFields.dropdownList({
                        bind: 'regionLabel',
                        isHidden: true,
                    }),
                    ui.nestedFields.dropdownList({
                        bind: 'zipLabel',
                        isHidden: true,
                    }),
                ],
                isHidden() {
                    return this.shipToAddressLineDetail.isReadOnly === true;
                },
            }),
        ],
    })
    shipToAddressLineDetail: ui.fields.VitalPod;

    @ui.decorators.pageAction<StockTransferOrder>({
        icon: 'search',
        title: 'Add lines from shipping site',
        isHidden: true,
        async onClick() {
            if (this.site.value) {
                const addedFromStock = await actionsFunctions.addFromStock(this);
                if (addedFromStock.isLineAdded) {
                    await actionsFunctions.saveAction(this);
                    if (addedFromStock.changedLine.isAutoAllocationRequested && this._id.value) {
                        await actionsFunctionsDistribution.requestAllocationAction({
                            pageInstance: this,
                            allocationType: 'allocation',
                            mutation: actionsFunctions.runRequestAllocationMutation({
                                pageInstance: this,
                                _id: this._id.value,
                                allocationType: 'allocation',
                            }),
                        });
                    }
                }
                if (this.lines.value.length > 0) {
                    this.save.isDisabled = false;
                }
                displayButtonsManagement.manageDisplayButtonSelectFromItemSitesAction(this);
            }
        },
    })
    selectFromItemSites: ui.PageAction;

    async displayLandedCosts(stockTransferReceiptLine: ExtractEdgesPartial<StockTransferReceiptLine> | undefined) {
        await this.$.dialog.page(
            '@sage/xtrem-landed-cost/LandedCostSummary',
            {
                args: JSON.stringify({
                    documentLineId: stockTransferReceiptLine?._id,
                    companyCurrency: this.currency.value?._id ?? '',
                    hideAllocatedAmountColumn: true,
                }),
            },
            { size: 'extra-large' },
        );
    }
}
