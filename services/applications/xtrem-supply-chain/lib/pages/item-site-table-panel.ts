import type { Dict, ExtractEdgesPartial } from '@sage/xtrem-client';
import { extractEdges } from '@sage/xtrem-client';
import type { Item, ItemSite, UnitOfMeasure } from '@sage/xtrem-master-data-api';
import type { GraphApi, StockTransferOrderLineBinding } from '@sage/xtrem-supply-chain-api';
import type { Site } from '@sage/xtrem-system-api';
import * as ui from '@sage/xtrem-ui';
import type { ItemSiteLines, ItemSiteLinesReturned } from '../client-functions/interfaces/item-site-table-panel';

@ui.decorators.page<ItemSiteTablePanel, ItemSite>({
    title: 'Add lines from item site',
    mode: 'default',
    node: '@sage/xtrem-master-data/ItemSite',
    isTransient: true,
    // access: { node: '@sage/xtrem-sales/SalesShipment', bind: '$create' },
    businessActions() {
        return [this.cancel, this.confirm];
    },
    async onLoad() {
        await this.loadItemSites(
            JSON.parse(String(this.$.queryParameters.site)),
            JSON.parse(String(this.$.queryParameters.stockTransferOrderLines)),
        );
        this.$.setPageClean();
    },
})
export class ItemSiteTablePanel extends ui.Page<GraphApi> {
    @ui.decorators.pageAction<ItemSiteTablePanel>({
        title: 'Add',
        async onClick() {
            await this.confirmation();
        },
        isDisabled() {
            return this.itemSiteLines.selectedRecords.length === 0;
        },
    })
    confirm: ui.PageAction;

    @ui.decorators.pageAction<ItemSiteTablePanel>({
        title: 'Cancel',
        buttonType: 'tertiary',
        onClick() {
            this.$.finish();
        },
    })
    cancel: ui.PageAction;

    @ui.decorators.section<ItemSiteTablePanel>({ isHidden: true })
    headerSection: ui.containers.Section;

    @ui.decorators.block<ItemSiteTablePanel>({
        parent() {
            return this.headerSection;
        },
    })
    headerBlock: ui.containers.Block;

    @ui.decorators.checkboxField<ItemSiteTablePanel>({
        parent() {
            return this.headerBlock;
        },
        title: 'Auto allocation',
    })
    isAutoAllocationRequested: ui.fields.Checkbox;

    @ui.decorators.section<ItemSiteTablePanel>({})
    lineSection: ui.containers.Section;

    @ui.decorators.block<ItemSiteTablePanel>({
        parent() {
            return this.lineSection;
        },
    })
    lineBlock: ui.containers.Block;

    @ui.decorators.separatorField<ItemSiteTablePanel>({
        parent() {
            return this.lineBlock;
        },
        isFullWidth: true,
        isHidden: true,
    })
    lineStatusSeparator: ui.fields.Separator;

    @ui.decorators.tableField<ItemSiteTablePanel, ItemSiteLines>({
        parent() {
            return this.lineSection;
        },
        title: 'Item sites',
        isTitleHidden: true,
        canUserHideColumns: false,
        isChangeIndicatorDisabled: true,
        orderBy: { _sortValue: +1 },
        cardView: false,
        mobileCard: {
            title: ui.nestedFields.reference<ItemSiteTablePanel, ItemSiteLines, Site>({
                bind: 'site',
                title: 'Site',
                valueField: 'name',
                node: '@sage/xtrem-system/Site',
            }),
            titleRight: ui.nestedFields.reference<ItemSiteTablePanel, ItemSiteLines, Item>({
                bind: 'item',
                title: 'Item',
                valueField: 'id',
                node: '@sage/xtrem-master-data/Item',
                tunnelPage: '@sage/xtrem-master-data/Item',
                isHiddenOnMainField: true,
            }),
            // line2: ui.nestedFields.date({ title: 'Shipping date', bind: 'shippingDate' }),
            line2Right: ui.nestedFields.numeric({
                bind: 'quantity',
                title: 'Quantity to ship',
                postfix: (_rowId, rowData) => rowData?.stockUnit?.symbol,
                scale: (_rowId, rowData) => rowData?.stockUnit?.decimalDigits || 0,
            }),
            image: ui.nestedFields.image({ bind: { item: { image: true } }, title: 'Image' }),
        },
        columns: [
            ui.nestedFields.technical({ bind: '_sortValue' }),
            ui.nestedFields.image({ bind: { item: { image: true } }, title: 'Image', size: 'small' }),
            ui.nestedFields.reference<ItemSiteTablePanel, ItemSiteLines, Item>({
                bind: 'item',
                title: 'Item name',
                valueField: 'name',
                helperTextField: 'id',
                node: '@sage/xtrem-master-data/Item',
                tunnelPage: '@sage/xtrem-master-data/Item',
                isReadOnly: true,
                columns: [
                    ui.nestedFields.text({ bind: 'name' }),
                    ui.nestedFields.text({ bind: 'id' }),
                    ui.nestedFields.text({ bind: 'description' }),
                    ui.nestedFields.text({ title: 'Category', bind: { category: { name: true } } }),
                    ui.nestedFields.technical({ bind: 'type' }),
                    ui.nestedFields.technical({ bind: 'image' }),
                ],
            }),
            ui.nestedFields.text<ItemSiteTablePanel, ItemSiteLines>({
                bind: { item: { id: true } },
                title: 'Item ID',
                isReadOnly: true,
                isHiddenOnMainField: true,
            }),

            ui.nestedFields.numeric({
                bind: 'availableQuantityInStockUnit',
                title: 'Stock available',
                isReadOnly: true,
                postfix: (_rowId, rowData) => rowData?.stockUnit?.symbol || '',
                scale: (_rowId, rowData) => rowData?.stockUnit?.decimalDigits || 0,
            }),
            ui.nestedFields.checkbox({
                bind: 'isAvailableQuantityInStockUnitSelected',
                title: 'Select all',
                onChange(_value, rowData) {
                    if (rowData.isAvailableQuantityInStockUnitSelected) {
                        this.itemSiteLines.selectRecord(rowData._id);
                        rowData.quantity = rowData.availableQuantityInStockUnit;
                        this.itemSiteLines.addOrUpdateRecordValue(rowData);
                    }
                    if (!rowData.isAvailableQuantityInStockUnitSelected) {
                        this.itemSiteLines.unselectRecord(rowData._id);
                        rowData.quantity = 0;
                        this.itemSiteLines.addOrUpdateRecordValue(rowData);
                    }
                },
            }),
            ui.nestedFields.numeric({
                bind: 'quantity',
                title: 'Quantity to ship',
                postfix: (_rowId, rowData) => rowData?.stockUnit?.symbol || '',
                scale: (_rowId, rowData) => rowData?.stockUnit?.decimalDigits || 0,
                validation(value, rowData) {
                    if (+value < 0) {
                        return ui.localize(
                            '@sage/xtrem-supply-chain/pages__item_site_table_panel__quantity_in_stock_unit_error_negative',
                            'You need to enter a quantity equal to or higher than zero.',
                        );
                    }
                    if (+value > rowData.availableQuantityInStockUnit) {
                        return ui.localize(
                            '@sage/xtrem-supply-chain/pages__item_site_table_panel__quantity_in_stock_unit_error_not_available',
                            'You need to enter a quantity equal to or less than the quantity available in stock.',
                        );
                    }
                    return undefined;
                },
                onChange(_value, rowData) {
                    if (rowData.quantity === 0) {
                        this.itemSiteLines.unselectRecord(rowData._id);
                        rowData.isAvailableQuantityInStockUnitSelected = false;
                        this.itemSiteLines.addOrUpdateRecordValue(
                            rowData as unknown as ExtractEdgesPartial<ItemSiteLines>,
                        );
                    }
                    if (rowData.quantity !== 0) {
                        this.itemSiteLines.selectRecord(rowData._id);
                        if (rowData.quantity !== rowData.availableQuantityInStockUnit) {
                            rowData.isAvailableQuantityInStockUnitSelected = false;
                        } else {
                            rowData.isAvailableQuantityInStockUnitSelected = true;
                        }
                        this.itemSiteLines.addOrUpdateRecordValue(
                            rowData as unknown as ExtractEdgesPartial<ItemSiteLines>,
                        );
                    }
                },
            }),
            ui.nestedFields.technical<ItemSiteTablePanel, ItemSiteLines, UnitOfMeasure>({
                node: '@sage/xtrem-master-data/UnitOfMeasure',
                bind: 'stockUnit',
                nestedFields: [
                    ui.nestedFields.technical({ bind: 'name' }),
                    ui.nestedFields.technical({ bind: 'id' }),
                    ui.nestedFields.technical({ bind: 'symbol' }),
                    ui.nestedFields.technical({ bind: 'description' }),
                    ui.nestedFields.technical({ bind: 'decimalDigits' }),
                ],
            }),
        ],

        onRowSelected(_rowId: string, rowItem: ItemSiteLines) {
            rowItem.quantity = rowItem.availableQuantityInStockUnit;
            rowItem.isAvailableQuantityInStockUnitSelected = true;
            this.itemSiteLines.addOrUpdateRecordValue(rowItem as unknown as ExtractEdgesPartial<ItemSiteLines>);
        },

        onRowUnselected(_recordId: string, rowItem: ItemSiteLines) {
            rowItem.quantity = 0;
            rowItem.isAvailableQuantityInStockUnitSelected = false;
            this.itemSiteLines.addOrUpdateRecordValue(rowItem as unknown as ExtractEdgesPartial<ItemSiteLines>);
        },
    })
    itemSiteLines: ui.fields.Table<ItemSiteLines>;

    // /**
    //  * This asynchronous function loads a list of item sites filtered by criteria
    //  * and calculate the remaining quantity to ship for each of them
    //  * @param site is a shipping site:
    //  * @param stockTransferOrderLines:
    //  */
    private async loadItemSites(
        site: ExtractEdgesPartial<Site>,
        stockTransferOrderLines: ui.PartialNodeWithId<StockTransferOrderLineBinding>[],
    ) {
        const result = extractEdges(
            await this.$.graph
                .node('@sage/xtrem-master-data/ItemSite')
                .query(
                    ui.queryUtils.edgesSelector(
                        {
                            _id: true,
                            _sortValue: true,
                            item: {
                                _id: true,
                                name: true,
                                description: true,
                                id: true,
                                stockUnit: { name: true, id: true, _id: true, symbol: true },
                                isStockManaged: true,
                                image: { value: true },
                            },
                            site: { _id: true },
                            allocatedQuantity: true,
                            acceptedStockQuantity: true,
                            stockUnit: { name: true, id: true, _id: true, symbol: true },
                        },
                        {
                            filter: { site: { _id: site._id } },
                            first: 200,
                            orderBy: { item: { name: 1 }, _sortValue: 1 },
                        },
                    ),
                )
                .execute(),
        ).map((line: Dict<any>) => {
            const availableQuantityInStockUnit = line.acceptedStockQuantity - line.allocatedQuantity;
            return { ...line, availableQuantityInStockUnit };
        });

        this.itemSiteLines.value.forEach(line => this.itemSiteLines.removeRecord(line._id));

        result.forEach(resultLine => {
            if (resultLine.availableQuantityInStockUnit > 0) {
                this.itemSiteLines.addRecord(resultLine);
            }
        });

        if (stockTransferOrderLines) {
            this.addOrRemoveRowsFilteredOnAlreadySelectedItemSitesToShip(stockTransferOrderLines);
        }
    }

    private addOrRemoveRowsFilteredOnAlreadySelectedItemSitesToShip(
        stockTransferOrderLines: ui.PartialNodeWithId<StockTransferOrderLineBinding>[],
    ) {
        const groupedItemSiteLines = this.itemSiteLines.value.map(
            (itemSiteLine: ui.PartialCollectionValue<ItemSiteLines>) => ({
                item: itemSiteLine.item,
                quantity: stockTransferOrderLines
                    .filter(stockTransferOrderLine => stockTransferOrderLine.item?.id === itemSiteLine.item?.id)
                    .reduce(
                        (qty: number, stockTransferOrderLine) => qty + Number(stockTransferOrderLine.quantity ?? 0),
                        0,
                    ),
            }),
        );

        this.itemSiteLines.value.forEach((itemSiteLine: ui.PartialCollectionValue<ItemSiteLines>, index: number) => {
            if (itemSiteLine && itemSiteLine.availableQuantityInStockUnit) {
                const alreadyOrderedQuantity =
                    (groupedItemSiteLines
                        ? groupedItemSiteLines.find(
                              groupedItemSiteLine => groupedItemSiteLine.item?.id === itemSiteLine.item?.id,
                          )?.quantity
                        : 0) ?? 0;

                const remainingQuantity = itemSiteLine.availableQuantityInStockUnit - alreadyOrderedQuantity;
                if (remainingQuantity > 0) {
                    this.itemSiteLines.addOrUpdateRecordValue({
                        ...itemSiteLine,
                        availableQuantityInStockUnit: remainingQuantity,
                        _sortValue: (index + 1) * 10,
                    });
                }
            }
        });
    }

    private async confirmation() {
        const validation = await this.$.page.validate();
        if (validation.length === 0) {
            const orderLines = this.itemSiteLines.selectedRecords.map(rowId => {
                const line = this.itemSiteLines.getRecordValue(rowId);
                return {
                    ...line,
                    itemId: line?.item?.id,
                };
            });

            const itemSiteSelectionReturned: ItemSiteLinesReturned = {
                lines: orderLines,
                isAutoAllocationRequested: this.isAutoAllocationRequested.value,
            };

            this.$.finish(itemSiteSelectionReturned);
        } else {
            this.$.showToast(validation.join('\n'), { type: 'error' });
        }
    }
}
