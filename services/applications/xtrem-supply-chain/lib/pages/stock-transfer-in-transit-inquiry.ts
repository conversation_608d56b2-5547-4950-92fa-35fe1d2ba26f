import { withoutEdges } from '@sage/xtrem-client';
import { DateValue } from '@sage/xtrem-date-time';
import type { PostingClass } from '@sage/xtrem-finance-data-api';
import type { Currency, Item, ItemCategory, UnitOfMeasure } from '@sage/xtrem-master-data-api';
import * as MasterDataUtils from '@sage/xtrem-master-data/build/lib/client-functions/utils';
import type {
    GraphApi,
    StockTransferInTransitInquiry as StockTransferInTransitInquiryNode,
    StockTransferShipment,
    StockTransferShipmentLine,
} from '@sage/xtrem-supply-chain-api';
import type { Company, Site, User } from '@sage/xtrem-system-api';
import * as ui from '@sage/xtrem-ui';
import { stockTransferMenu } from '../menu-items/stock-transfer';

@ui.decorators.page<StockTransferInTransitInquiry, StockTransferInTransitInquiryNode>({
    menuItem: stockTransferMenu,
    priority: 400,
    title: 'Stock in transit inquiry',
    module: 'supply-chain',
    mode: 'default',
    skipDirtyCheck: true,
    node: '@sage/xtrem-supply-chain/StockTransferInTransitInquiry',

    createAction() {
        return this.$standardNewAction;
    },
    businessActions() {
        return [this.runStockTransferInTransitInquiry];
    },
    headerDropDownActions() {
        return [this.$standardDeleteAction];
    },
    async defaultEntry() {
        // As there is only one record per user try to load this record.
        //
        // NOTE (for local Cucumber script test on local development instances): Please make sure that the user
        // e-mail address in the top field is set to the e-mail address of the user configured in xtrem-config.yml!
        const inputSet = withoutEdges(
            await this.$.graph
                .node('@sage/xtrem-supply-chain/StockTransferInTransitInquiry')
                .query(
                    ui.queryUtils.edgesSelector(
                        { _id: true },
                        { filter: { user: { email: this.$.username } }, first: 1 },
                    ),
                )
                .execute(),
        );

        // If there is such record open it. Otherwise open work with an empty page in creation mode.
        return inputSet.at(0)?._id ?? '$new';
    },

    async onLoad() {
        if (!this.$.recordId) {
            // In case we have no record for this user pre-set the calculation user field.
            await this.$.fetchDefaults(['user']);
            if (this.lines.value.length === 0) {
                this.inTransitStockValue.value = 0;
            }
        }
    },
    navigationPanel: undefined,
    headerSection() {
        return this.mainSection;
    },
})
export class StockTransferInTransitInquiry extends ui.Page<GraphApi, StockTransferInTransitInquiryNode> {
    @ui.decorators.pageAction<StockTransferInTransitInquiry>({
        title: 'Run',
        async onClick() {
            this.$.loader.isHidden = false;
            if (await this.saveInquiry()) {
                await this.$.router.refresh();
            }
            this.$.loader.isHidden = true;
        },
        buttonType: 'primary',
    })
    runStockTransferInTransitInquiry: ui.PageAction;

    async saveInquiry() {
        const validation = await this.$.page.validate();
        if (validation.length !== 0) {
            this.$.showToast(validation.join('\n'), { type: 'error' });
            return false;
        }
        await this.$standardSaveAction.execute(true);
        // To be sure that the user is refreshed before we continue
        return true;
    }

    @ui.decorators.section<StockTransferInTransitInquiry>({ isOpen: true, isTitleHidden: true, title: 'General' })
    mainSection: ui.containers.Section;

    @ui.decorators.block<StockTransferInTransitInquiry>({
        parent() {
            return this.mainSection;
        },
        width: 'extra-large',
        title: 'Criteria',
    })
    criteriaBlock: ui.containers.Block;

    @ui.decorators.referenceField<StockTransferInTransitInquiry, User>({
        parent() {
            return this.criteriaBlock;
        },
        title: 'User',
        isReadOnly: true,
        fetchesDefaults: true,
    })
    user: ui.fields.Reference<User>;

    @ui.decorators.referenceField<StockTransferInTransitInquiry, Company>({
        parent() {
            return this.criteriaBlock;
        },
        title: 'Company',
        minLookupCharacters: 0,
        filter: { isActive: { _eq: true } },
        isMandatory: true,
        columns: [
            ui.nestedFields.text({ bind: 'name' }),
            ui.nestedFields.text({ bind: 'id' }),
            ui.nestedFields.text({ bind: 'description' }),
            ui.nestedFields.technical<StockTransferInTransitInquiry, Company, Currency>({
                bind: 'currency',
                node: '@sage/xtrem-master-data/Currency',
                nestedFields: [
                    ui.nestedFields.technical({ bind: 'id' }),
                    ui.nestedFields.technical({ bind: 'name' }),
                    ui.nestedFields.technical({ bind: 'decimalDigits' }),
                    ui.nestedFields.technical({ bind: 'symbol' }),
                ],
            }),
        ],
        onChange() {
            this.site.value = null;
        },
    })
    company: ui.fields.Reference<Company>;

    @ui.decorators.referenceField<StockTransferInTransitInquiry, Site>({
        node: '@sage/xtrem-system/Site',
        title: 'Shipping site',
        minLookupCharacters: 0,
        parent() {
            return this.criteriaBlock;
        },
        columns: [
            ui.nestedFields.text({ bind: 'name' }),
            ui.nestedFields.text({ bind: 'id' }),
            ui.nestedFields.text({ bind: 'description' }),
            ui.nestedFields.technical<StockTransferInTransitInquiry, Site, Company>({
                bind: 'legalCompany',
                node: '@sage/xtrem-system/Company',
                nestedFields: [
                    ui.nestedFields.technical({ bind: 'name' }),
                    ui.nestedFields.technical({ bind: 'id' }),
                    ui.nestedFields.technical({ bind: 'description' }),
                    ui.nestedFields.technical<StockTransferInTransitInquiry, Company, Currency>({
                        node: '@sage/xtrem-master-data/Currency',
                        bind: 'currency',
                        nestedFields: [
                            ui.nestedFields.technical({ bind: 'symbol' }),
                            ui.nestedFields.technical({ bind: 'decimalDigits' }),
                        ],
                    }),
                ],
            }),
        ],
        filter() {
            return {
                isActive: { _eq: true },
                isInventory: { _eq: true },
                ...(this.company.value ? { legalCompany: { id: { _eq: this.company.value?.id ?? '' } } } : {}),
            };
        },
        onChange() {
            if (this.site.value) {
                this.company.value = this.site.value.legalCompany ?? null;
            }
        },
    })
    site: ui.fields.Reference<Site>;

    @ui.decorators.referenceField<StockTransferInTransitInquiry, Item>({
        title: 'From item',
        minLookupCharacters: 0,
        parent() {
            return this.criteriaBlock;
        },
    })
    fromItem: ui.fields.Reference<Item>;

    @ui.decorators.referenceField<StockTransferInTransitInquiry, Item>({
        title: 'To item',
        minLookupCharacters: 0,
        parent() {
            return this.criteriaBlock;
        },
    })
    toItem: ui.fields.Reference<Item>;

    @ui.decorators.referenceField<StockTransferInTransitInquiry, ItemCategory>({
        title: 'Item category',
        minLookupCharacters: 1,
        parent() {
            return this.criteriaBlock;
        },
    })
    itemCategory: ui.fields.Reference<ItemCategory>;

    @ui.decorators.textField<StockTransferInTransitInquiry>({
        title: 'Commodity code',
        parent() {
            return this.criteriaBlock;
        },
    })
    commodityCode: ui.fields.Text;

    @ui.decorators.referenceField<StockTransferInTransitInquiry, PostingClass>({
        title: 'Posting class',
        minLookupCharacters: 1,
        parent() {
            return this.criteriaBlock;
        },
    })
    postingClass: ui.fields.Reference<PostingClass>;

    @ui.decorators.dateField<StockTransferInTransitInquiry>({
        parent() {
            return this.criteriaBlock;
        },
        title: 'Date',
        isMandatory: true,
        maxDate() {
            return DateValue.today().toString();
        },
    })
    date: ui.fields.Date;

    @ui.decorators.tile<StockTransferInTransitInquiry>({
        parent() {
            return this.mainSection;
        },
    })
    tileContainer: ui.containers.Tile;

    @ui.decorators.aggregateField<StockTransferInTransitInquiry>({
        parent() {
            return this.tileContainer;
        },
        aggregateOn: { inTransit: { stockValue: true } },
        aggregationMethod: 'sum',
        title: 'In transit stock value',
        bind: 'lines',
        unit() {
            return this.company.value?.currency;
        },
        width: 'medium',
    })
    inTransitStockValue: ui.fields.Aggregate;

    @ui.decorators.section<StockTransferInTransitInquiry>({ title: 'Results' })
    resultsSection: ui.containers.Section;

    @ui.decorators.tableField<StockTransferInTransitInquiry, StockTransferShipmentLine>({
        node: '@sage/xtrem-supply-chain/StockTransferShipmentLine',
        canResizeColumns: true,
        canSelect: false,

        isHelperTextHidden: true,
        canExport: true,
        title: 'Results',
        isTitleHidden: true,
        isReadOnly: true,
        parent() {
            return this.resultsSection;
        },
        columns: [
            ui.nestedFields.technical<StockTransferInTransitInquiry, StockTransferShipmentLine, StockTransferShipment>({
                bind: { document: { _id: true } },
            }),
            ui.nestedFields.link({
                title: 'Shipment number',
                bind: { document: { number: true } },
                width: 'large',
                onClick(_id, rowData) {
                    return this.$.dialog.page(
                        '@sage/xtrem-supply-chain/StockTransferShipment',
                        { _id: rowData?.document?._id ?? '' },
                        {
                            fullScreen: true,
                            resolveOnCancel: true,
                        },
                    );
                },
            }),
            ui.nestedFields.reference<StockTransferInTransitInquiry, StockTransferShipmentLine, Item>({
                bind: 'item',
                title: 'Item',
                node: '@sage/xtrem-master-data/Item',
                tunnelPage: '@sage/xtrem-master-data/Item',
                valueField: 'name',
            }),
            ui.nestedFields.text({
                bind: { item: { id: true } },
                title: 'Item ID',
            }),
            ui.nestedFields.text({
                bind: 'itemDescription',
                title: 'Item description',
                isHiddenOnMainField: true,
            }),
            ui.nestedFields.reference<StockTransferInTransitInquiry, StockTransferShipmentLine, Site>({
                bind: { document: { site: true } },
                title: 'Shipping site',
                node: '@sage/xtrem-system/Site',
                tunnelPage: '@sage/xtrem-master-data/Site',
            }),
            ui.nestedFields.reference<StockTransferInTransitInquiry, StockTransferShipmentLine, Site>({
                bind: { document: { receivingSite: true } },
                title: 'Receiving site',
                node: '@sage/xtrem-system/Site',
                tunnelPage: '@sage/xtrem-master-data/Site',
            }),
            ui.nestedFields.reference<StockTransferInTransitInquiry, StockTransferShipmentLine, Company>({
                bind: { document: { site: { legalCompany: true } } },
                title: 'Company',
                node: '@sage/xtrem-system/Company',
                tunnelPage: '@sage/xtrem-master-data/Company',
                columns: [
                    ui.nestedFields.text({ bind: 'name' }),
                    ui.nestedFields.text({ bind: 'id' }),
                    ui.nestedFields.technical<StockTransferInTransitInquiry, Company, Currency>({
                        bind: 'currency',
                        node: '@sage/xtrem-master-data/Currency',
                        nestedFields: [
                            ui.nestedFields.technical({ bind: 'symbol' }),
                            ui.nestedFields.technical({ bind: 'decimalDigits' }),
                        ],
                    }),
                ],
            }),
            ui.nestedFields.numeric({
                title: 'Quantity in stock unit',
                bind: 'quantityInStockUnit',
                unit: (_id, rowData) => rowData?.stockUnit,
                unitMode: 'unitOfMeasure',
            }),
            ui.nestedFields.reference<StockTransferInTransitInquiry, StockTransferShipmentLine, UnitOfMeasure>({
                title: 'Stock unit',
                bind: 'stockUnit',
                node: '@sage/xtrem-master-data/UnitOfMeasure',
                tunnelPage: '@sage/xtrem-master-data/UnitOfMeasure',
                valueField: 'name',
                helperTextField: 'symbol',
                isHiddenOnMainField: true,
                columns: [
                    ui.nestedFields.technical({ bind: 'symbol' }),
                    ui.nestedFields.technical({ bind: 'decimalDigits' }),
                ],
            }),
            ui.nestedFields.numeric({
                title: 'Unit cost',
                bind: { inTransit: { unitCost: true } },
                scale(_val, rowData) {
                    return MasterDataUtils.getScaleValue(
                        2,
                        rowData?.document?.site?.legalCompany?.currency.decimalDigits,
                    );
                },
                prefix(_val, rowData) {
                    return rowData?.document?.site?.legalCompany.currency.symbol || '';
                },
            }),
            ui.nestedFields.reference({
                title: 'Currency',
                bind: { document: { site: { legalCompany: { currency: true } } } },
                node: '@sage/xtrem-master-data/Currency',
                valueField: 'name',
                isHiddenOnMainField: true,
            }),
            ui.nestedFields.numeric({
                bind: { inTransit: { stockValue: true } },
                title: 'Stock value in transit',
                scale(_val, rowData) {
                    return MasterDataUtils.getScaleValue(
                        2,
                        rowData?.document?.site?.legalCompany?.currency?.decimalDigits,
                    );
                },
                prefix(_val, rowData) {
                    return rowData?.document?.site?.legalCompany?.currency?.symbol || '';
                },
            }),
            ui.nestedFields.dropdownList({
                bind: { inTransit: { costType: true } },
                title: 'Cost type',
                optionType: '@sage/xtrem-master-data/costValuationMethod',
            }),
            ui.nestedFields.reference<StockTransferInTransitInquiry, StockTransferShipmentLine, ItemCategory>({
                bind: { item: { category: true } },
                title: 'Item category',
                node: '@sage/xtrem-master-data/ItemCategory',
                valueField: 'name',
            }),
            ui.nestedFields.reference<StockTransferInTransitInquiry, StockTransferShipmentLine, PostingClass>({
                bind: { item: { postingClass: true } },
                title: 'Posting class',
                node: '@sage/xtrem-finance-data/PostingClass',
                valueField: 'name',
            }),
            ui.nestedFields.text({
                bind: { inTransit: { commodityCode: true } },
                title: 'Commodity code',
                isHiddenOnMainField: true,
            }),
        ],
    })
    lines: ui.fields.Table<StockTransferShipmentLine>;
}
