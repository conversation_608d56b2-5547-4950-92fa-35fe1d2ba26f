import type {
    BusinessEntity,
    Currency,
    Item,
    ItemSite,
    ItemSupplier,
    PreferredProcess,
    Supplier,
    UnitOfMeasure,
} from '@sage/xtrem-master-data-api';
import * as masterDataCommon from '@sage/xtrem-master-data/build/lib/client-functions/common';
import * as masterDataUtils from '@sage/xtrem-master-data/build/lib/client-functions/utils';
import { purchasing } from '@sage/xtrem-master-data/build/lib/menu-items/purchasing';
import * as purchasingCommon from '@sage/xtrem-purchasing/build/lib/client-functions/common';
import type { Country } from '@sage/xtrem-structure-api';
import type {
    GraphApi,
    SupplyPlanning,
    SupplyPlanningSource,
    SupplyPlanningStatus,
} from '@sage/xtrem-supply-chain-api';
import type { Company, Site, User } from '@sage/xtrem-system-api';
import { setDisplayOfCommonPageActions } from '@sage/xtrem-system/build/lib/client-functions/applicative-crud-actions';
import * as ui from '@sage/xtrem-ui';
import * as pillColorDocument from '../client-functions/pill-color';

@ui.decorators.page<PurchaseOrderPlanning, SupplyPlanning>({
    title: 'Purchase order planning',
    mode: 'default',
    node: '@sage/xtrem-supply-chain/SupplyPlanning',

    menuItem: purchasing,
    priority: 900,

    navigationPanel: {
        bulkActions: [
            {
                mutation: 'createOrder',
                title: 'Create order',
                icon: 'none',
                buttonType: 'primary',
                isDestructive: false,
            },
        ],
        optionsMenu: [
            {
                title: 'All',
                graphQLFilter: { type: { _in: ['purchased', null] } },
            },
        ],
        orderBy: { startDate: -1, requirementDate: 1 },
        listItem: {
            title: ui.nestedFields.reference<PurchaseOrderPlanning, SupplyPlanning, Company>({
                bind: { itemSite: { site: { legalCompany: true } } },
                node: '@sage/xtrem-system/Company',
                valueField: 'name',
                tunnelPage: undefined,
                title: 'Company name',
            }),
            companyID: ui.nestedFields.text<PurchaseOrderPlanning, SupplyPlanning>({
                bind: { itemSite: { site: { legalCompany: { id: true } } } },
                title: 'Company ID',
            }),
            titleRight: ui.nestedFields.reference<PurchaseOrderPlanning, SupplyPlanning, Site>({
                bind: 'purchaseSite',
                node: '@sage/xtrem-system/Site',
                tunnelPage: undefined,
                valueField: 'name',
                title: 'Site name',
            }),
            siteID: ui.nestedFields.text<PurchaseOrderPlanning, SupplyPlanning>({
                bind: { purchaseSite: { id: true } },
                title: 'Site ID',
            }),
            line2: ui.nestedFields.reference<PurchaseOrderPlanning, SupplyPlanning, Item>({
                bind: { itemSite: { item: true } },
                node: '@sage/xtrem-master-data/Item',
                valueField: 'name',
                tunnelPage: undefined,
                title: 'Item',
            }),
            itemID: ui.nestedFields.text<PurchaseOrderPlanning, SupplyPlanning>({
                bind: { itemSite: { item: { id: true } } },
                title: 'Item ID',
            }),
            itemDescription: ui.nestedFields.text<PurchaseOrderPlanning, SupplyPlanning>({
                bind: { itemSite: { item: { description: true } } },
                title: 'Item description',
                isHiddenOnMainField: true,
            }),

            line3: ui.nestedFields.reference<PurchaseOrderPlanning, SupplyPlanning, Supplier>({
                bind: 'supplier',
                node: '@sage/xtrem-master-data/Supplier',
                valueField: { businessEntity: { name: true } },
                tunnelPage: undefined,
                title: 'Supplier',
            }),
            id: ui.nestedFields.text({
                bind: { supplier: { id: true } },
                title: 'Supplier ID',
                isHiddenOnMainField: true,
            }),
            purchaseUnitDecimalDigits: ui.nestedFields.technical<PurchaseOrderPlanning, SupplyPlanning, UnitOfMeasure>({
                bind: { purchaseUnit: { decimalDigits: true } },
            }),
            purchaseUnitSymbol: ui.nestedFields.technical<PurchaseOrderPlanning, SupplyPlanning, UnitOfMeasure>({
                bind: { purchaseUnit: { symbol: true } },
            }),
            purchaseQuantity: ui.nestedFields.numeric<PurchaseOrderPlanning, SupplyPlanning>({
                bind: 'purchaseQuantity',
                title: 'Purchase quantity',
                scale(_recordID, recordData) {
                    return masterDataUtils.getScaleValue(2, recordData?.purchaseUnit?.decimalDigits);
                },
                postfix(_recordID, recordData) {
                    return recordData?.purchaseUnit?.symbol || '';
                },
            }),
            startDate: ui.nestedFields.date<PurchaseOrderPlanning, SupplyPlanning>({
                bind: 'startDate',
                title: 'Order date',
            }),
            requirementDate: ui.nestedFields.date<PurchaseOrderPlanning, SupplyPlanning>({
                bind: 'requirementDate',
                title: 'Expected receipt date',
            }),
            companyPriceScale: ui.nestedFields.technical<PurchaseOrderPlanning, SupplyPlanning>({
                bind: { itemSite: { site: { legalCompany: { priceScale: true } } } },
            }),
            companyCurrencySymbol: ui.nestedFields.technical<PurchaseOrderPlanning, SupplyPlanning>({
                bind: { itemSite: { site: { legalCompany: { currency: { symbol: true } } } } },
            }),
            grossPrice: ui.nestedFields.numeric<PurchaseOrderPlanning, SupplyPlanning>({
                bind: 'grossPrice',
                title: 'Gross price',
                scale(_recordID, recordData) {
                    return masterDataCommon.getCompanyPriceScale(recordData?.itemSite?.site?.legalCompany);
                },
                prefix(_recordID, recordData) {
                    return recordData?.itemSite?.site?.legalCompany?.currency?.symbol || '';
                },
            }),
            preferredProcess: ui.nestedFields.label<PurchaseOrderPlanning, SupplyPlanning>({
                bind: { itemSite: { preferredProcess: true } },
                optionType: '@sage/xtrem-supply-chain/PreferredProcess',
                title: 'Preferred process',
                backgroundColor(value: PreferredProcess) {
                    return pillColorDocument.setEnumPreferredProcessColor(value, 'backgroundColor');
                },
                borderColor(value: PreferredProcess) {
                    return pillColorDocument.setEnumPreferredProcessColor(value, 'borderColor');
                },
                color(value: PreferredProcess) {
                    return pillColorDocument.setEnumPreferredProcessColor(value, 'textColor');
                },
            }),
            suggestionSource: ui.nestedFields.label<PurchaseOrderPlanning, SupplyPlanning>({
                bind: 'suggestionSource',
                optionType: '@sage/xtrem-supply-chain/SupplyPlanningSource',
                title: 'Suggestion source',
                backgroundColor(value: SupplyPlanningSource) {
                    return pillColorDocument.setEnumSupplyPlanningSourceColor(value, 'backgroundColor');
                },
                borderColor(value: SupplyPlanningSource) {
                    return pillColorDocument.setEnumSupplyPlanningSourceColor(value, 'borderColor');
                },
                color(value: SupplyPlanningSource) {
                    return pillColorDocument.setEnumSupplyPlanningSourceColor(value, 'textColor');
                },
            }),
            suggestionDate: ui.nestedFields.date<PurchaseOrderPlanning, SupplyPlanning>({
                bind: 'suggestionDate',
                title: 'Suggestion date',
            }),
        },
    },

    businessActions() {
        return [this.$standardCancelAction, this.save];
    },

    onDirtyStateUpdated(isDirty: boolean) {
        setDisplayOfCommonPageActions({
            page: this,
            isDirty,
            cancel: this.$standardCancelAction,
            save: this.save,
        });
    },
    onLoad() {
        setDisplayOfCommonPageActions({
            page: this,
            isDirty: false,
            cancel: this.$standardCancelAction,
            save: this.save,
        });
    },
})
export class PurchaseOrderPlanning extends ui.Page<GraphApi, SupplyPlanning> {
    @ui.decorators.section<PurchaseOrderPlanning>({
        isTitleHidden: true,
        title: 'General',
    })
    mainSection: ui.containers.Section;

    @ui.decorators.block<PurchaseOrderPlanning>({
        parent() {
            return this.mainSection;
        },
    })
    mainBlock: ui.containers.Block;

    @ui.decorators.labelField<PurchaseOrderPlanning>({
        bind: { itemSite: { preferredProcess: true } },
        optionType: '@sage/xtrem-master-data/PreferredProcess',
        title: 'Preferred process',
        parent() {
            return this.mainBlock;
        },
        backgroundColor(value: PreferredProcess) {
            return pillColorDocument.setEnumPreferredProcessColor(value, 'backgroundColor');
        },
        borderColor(value: PreferredProcess) {
            return pillColorDocument.setEnumPreferredProcessColor(value, 'borderColor');
        },
        color(value: PreferredProcess) {
            return pillColorDocument.setEnumPreferredProcessColor(value, 'textColor');
        },
    })
    preferredProcess: ui.fields.Label;

    @ui.decorators.referenceField<PurchaseOrderPlanning, Company>({
        bind: { itemSite: { site: { legalCompany: true } } },
        node: '@sage/xtrem-system/Company',
        valueField: 'name',
        helperTextField: 'id',
        tunnelPage: '@sage/xtrem-master-data/Company',
        isReadOnly: true,
        title: 'Company',
        columns: [
            ui.nestedFields.text({ bind: 'name' }),
            ui.nestedFields.technical<PurchaseOrderPlanning, Company, Currency>({
                bind: 'currency',
                node: '@sage/xtrem-master-data/Currency',
                nestedFields: [
                    ui.nestedFields.technical({ bind: 'name' }),
                    ui.nestedFields.technical({ bind: 'id' }),
                    ui.nestedFields.technical({ bind: 'symbol' }),
                ],
            }),
            ui.nestedFields.text({ bind: 'id', title: 'ID ' }),
            ui.nestedFields.technical({ bind: 'priceScale' }),
        ],
        parent() {
            return this.mainBlock;
        },
    })
    company: ui.fields.Reference<Company>;

    @ui.decorators.referenceField<PurchaseOrderPlanning, Site>({
        bind: { itemSite: { site: true } },
        isReadOnly: true,
        title: 'Stock site',
        parent() {
            return this.mainBlock;
        },
    })
    stockSite: ui.fields.Reference<Site>;

    @ui.decorators.referenceField<PurchaseOrderPlanning, Site>({
        node: '@sage/xtrem-system/Site',
        title: 'Purchasing site',
        isMandatory: true,
        columns: [
            ui.nestedFields.text({ bind: 'name' }),
            ui.nestedFields.technical<PurchaseOrderPlanning, Site, Company>({
                bind: 'legalCompany',
                node: '@sage/xtrem-system/Company',
                nestedFields: [
                    ui.nestedFields.technical({ bind: 'name' }),
                    ui.nestedFields.technical<PurchaseOrderPlanning, Company, Currency>({
                        bind: 'currency',
                        node: '@sage/xtrem-master-data/Currency',
                        nestedFields: [
                            ui.nestedFields.technical({ bind: 'name' }),
                            ui.nestedFields.technical({ bind: 'id' }),
                            ui.nestedFields.technical({ bind: 'symbol' }),
                            ui.nestedFields.technical({ bind: 'decimalDigits' }),
                        ],
                    }),
                ],
            }),

            ui.nestedFields.text({ bind: 'id', title: 'ID ' }),
        ],
        parent() {
            return this.mainBlock;
        },
        filter() {
            return { legalCompany: { _id: this.company.value?._id }, isPurchase: true };
        },
        async onChange() {
            await purchasingCommon.setPurchaseGrossPriceAndPriceOrigin(this, {
                grossPriceField: this.grossPrice,

                supplier: this.supplier.value,
                purchasingSite: this.purchaseSite.value,
                item: this.item.value,
                unit: this.purchaseUnit.value,
                purchaseQuantity: this.purchaseQuantity.value,
                orderDate: this.startDate.value,
            });
        },
    })
    purchaseSite: ui.fields.Reference<Site>;

    @ui.decorators.referenceField<PurchaseOrderPlanning, Item>({
        bind: { itemSite: { item: true } },
        node: '@sage/xtrem-master-data/Item',
        valueField: 'name',
        helperTextField: 'id',
        tunnelPage: '@sage/xtrem-master-data/Item',
        isReadOnly: true,
        title: 'Item ID',
        parent() {
            return this.mainBlock;
        },
    })
    item: ui.fields.Reference<Item>;

    // TODO: to be removed as soon as this is available XT-65704
    @ui.decorators.referenceField<PurchaseOrderPlanning, ItemSite>({
        bind: 'itemSite',
        node: '@sage/xtrem-master-data/ItemSite',
        valueField: 'id',
    })
    itemSite: ui.fields.Reference<ItemSite>;

    @ui.decorators.referenceField<PurchaseOrderPlanning, Supplier>({
        lookupDialogTitle: 'Select supplier',
        isMandatory: true,
        isAutoSelectEnabled: true,
        shouldSuggestionsIncludeColumns: true,
        columns: [
            ui.nestedFields.text({ bind: { businessEntity: { name: true } }, title: 'Name' }),
            ui.nestedFields.text({ bind: { businessEntity: { id: true } }, title: 'ID' }),
            ui.nestedFields.text({ bind: { businessEntity: { taxIdNumber: true } }, title: 'Tax ID' }),
            ui.nestedFields.technical({ bind: { internalNote: { value: true } } }),
            ui.nestedFields.reference<PurchaseOrderPlanning, Supplier, Country>({
                bind: { businessEntity: { country: true } },
            }),
            ui.nestedFields.technical<PurchaseOrderPlanning, Supplier, BusinessEntity>({
                bind: 'businessEntity',
                node: '@sage/xtrem-master-data/BusinessEntity',
                nestedFields: [ui.nestedFields.technical({ bind: 'id' })],
            }),
            ui.nestedFields.technical<PurchaseOrderPlanning, Supplier>({
                bind: 'minimumOrderAmount',
            }),
        ],
        parent() {
            return this.mainBlock;
        },
        async onChange() {
            await purchasingCommon.setPurchaseGrossPriceAndPriceOrigin(this, {
                grossPriceField: this.grossPrice,

                supplier: this.supplier.value,
                purchasingSite: this.purchaseSite.value,
                item: this.item.value,
                unit: this.purchaseUnit.value,
                purchaseQuantity: this.purchaseQuantity.value,
                orderDate: this.startDate.value,
            });
        },
    })
    supplier: ui.fields.Reference<Supplier>;

    @ui.decorators.referenceField<PurchaseOrderPlanning, ItemSupplier>({
        bind: 'itemSupplier',
        node: '@sage/xtrem-master-data/ItemSupplier',
        valueField: 'id',
        columns: [
            ui.nestedFields.technical<PurchaseOrderPlanning, ItemSupplier, UnitOfMeasure>({
                bind: 'purchaseUnitOfMeasure',
                node: '@sage/xtrem-master-data/UnitOfMeasure',
                nestedFields: [ui.nestedFields.technical<PurchaseOrderPlanning, UnitOfMeasure>({ bind: '_id' })],
            }),
        ],
    })
    itemSupplier: ui.fields.Reference<ItemSupplier>;

    @ui.decorators.numericField<PurchaseOrderPlanning>({
        isReadOnly: true,
        title: 'Stock quantity',
        parent() {
            return this.mainBlock;
        },
        scale() {
            return masterDataUtils.getScaleValue(2, this.stockUnit.value?.decimalDigits);
        },
        postfix() {
            return this.stockUnit.value?.symbol || '';
        },
    })
    stockQuantity: ui.fields.Numeric;

    @ui.decorators.referenceField<PurchaseOrderPlanning, UnitOfMeasure>({
        isReadOnly: true,
        columns: [
            ui.nestedFields.text({ title: 'Name', bind: 'name', canFilter: false }),
            ui.nestedFields.text({ bind: 'id', isHidden: true }),
            ui.nestedFields.text({ bind: 'symbol', title: 'Symbol' }),
            ui.nestedFields.technical({ bind: 'decimalDigits' }),
            ui.nestedFields.technical({ bind: 'description' }),
        ],
        parent() {
            return this.mainBlock;
        },
        filter() {
            return { isActive: true };
        },
    })
    stockUnit: ui.fields.Reference<UnitOfMeasure>;

    @ui.decorators.numericField<PurchaseOrderPlanning>({
        title: 'Purchase quantity',
        isMandatory: true,
        min: 0,
        parent() {
            return this.mainBlock;
        },
        scale() {
            return masterDataUtils.getScaleValue(2, this.purchaseUnit.value?.decimalDigits);
        },
        postfix() {
            return this.purchaseUnit.value?.symbol || '';
        },
        validation(val?: number) {
            if (val !== undefined && val <= 0) {
                return ui.localize(
                    '@sage/xtrem-supply-chain/pages__purchase_order_planning__purchase_quantity_zero_or_less',
                    'The purchase quantity cannot be less than or equal to 0.',
                );
            }
            return undefined;
        },
        async onChange() {
            purchasingCommon.checkPurchaseOrderLineMinimumQuantity(this, {
                supplierMinimumQuantity: Number(this.supplier.value?.minimumOrderAmount),
                supplierPurchaseUnit: this.itemSupplier.value?.purchaseUnitOfMeasure,
                unit: this.purchaseUnit.value,
                purchaseQuantity: this.purchaseQuantity.value,
            });
            await purchasingCommon.setStockUnitQuantity(this, {
                stockQuantityField: this.stockQuantity,
                stockUnit: this.stockUnit.value,
                unit: this.purchaseUnit.value,
                purchaseQuantity: this.purchaseQuantity.value,

                item: this.item.value,
                supplier: this.supplier.value,
            });
            await purchasingCommon.setPurchaseUnitQuantity(this, {
                purchaseQuantityField: this.purchaseQuantity,
                stockUnit: this.stockUnit.value,
                unit: this.purchaseUnit.value,
                stockQuantity: this.stockQuantity.value,

                item: this.item.value,
                supplier: this.supplier.value,

                supplierMinimumQuantity: Number(this.supplier.value?.minimumOrderAmount),
                supplierPurchaseUnit: this.itemSupplier.value?.purchaseUnitOfMeasure,
            });
            await purchasingCommon.setPurchaseGrossPriceAndPriceOrigin(this, {
                grossPriceField: this.grossPrice,

                supplier: this.supplier.value,
                purchasingSite: this.purchaseSite.value,
                item: this.item.value,
                unit: this.purchaseUnit.value,
                purchaseQuantity: this.purchaseQuantity.value,
                orderDate: this.startDate.value,
            });
        },
    })
    purchaseQuantity: ui.fields.Numeric;

    @ui.decorators.referenceField<PurchaseOrderPlanning, UnitOfMeasure>({
        lookupDialogTitle: 'Select unit',
        isMandatory: true,
        columns: [
            ui.nestedFields.text({ title: 'Name', bind: 'name', canFilter: false }),
            ui.nestedFields.text({ bind: 'id', isHidden: true }),
            ui.nestedFields.text({ bind: 'symbol', title: 'Symbol' }),
            ui.nestedFields.technical({ bind: 'decimalDigits' }),
            ui.nestedFields.technical({ bind: 'description' }),
        ],
        parent() {
            return this.mainBlock;
        },
        filter() {
            return { isActive: true };
        },
        async validation(val?: UnitOfMeasure) {
            if (val) {
                if (
                    (
                        await masterDataCommon.convertFromTo(
                            this.$.graph,
                            this.stockUnit.value?._id || '',
                            val._id,
                            this.stockQuantity.value || 0,
                            this.item?.value?._id || undefined,
                            this.supplier?.value?._id || undefined,
                            '',
                            'purchase',
                            false,
                        )
                    ).conversionFactor === 0
                ) {
                    return ui.localize(
                        '@sage/xtrem-supply-chain/pages__purchase_order_planning__no_conversion_factor_defined',
                        'You need to add a unit conversion from {{fromUnit}} to {{toUnit}}.',
                        {
                            fromUnit: this.stockUnit.value?.description,
                            toUnit: val.description,
                        },
                    );
                }
            }
            return undefined;
        },
        async onChange() {
            await purchasingCommon.setPurchaseUnitQuantity(this, {
                purchaseQuantityField: this.purchaseQuantity,
                stockUnit: this.stockUnit.value,
                unit: this.purchaseUnit.value,
                stockQuantity: this.stockQuantity.value,
                item: this.item.value,
                supplier: this.supplier.value,
                supplierMinimumQuantity: Number(this.supplier.value?.minimumOrderAmount),
                supplierPurchaseUnit: this.itemSupplier.value?.purchaseUnitOfMeasure,
            });
            await purchasingCommon.setPurchaseGrossPriceAndPriceOrigin(this, {
                grossPriceField: this.grossPrice,
                supplier: this.supplier.value,
                purchasingSite: this.purchaseSite.value,
                item: this.item.value,
                unit: this.purchaseUnit.value,
                purchaseQuantity: this.purchaseQuantity.value,
                orderDate: this.startDate.value,
            });
        },
    })
    purchaseUnit: ui.fields.Reference<UnitOfMeasure>;

    @ui.decorators.dateField<PurchaseOrderPlanning>({
        title: 'Order date',
        isMandatory: true,
        maxDate: new Date(),
        parent() {
            return this.mainBlock;
        },
        async onChange() {
            await purchasingCommon.setPurchaseGrossPriceAndPriceOrigin(this, {
                grossPriceField: this.grossPrice,

                supplier: this.supplier.value,
                purchasingSite: this.purchaseSite.value,
                item: this.item.value,
                unit: this.purchaseUnit.value,
                purchaseQuantity: this.purchaseQuantity.value,
                orderDate: this.startDate.value,
            });
        },
    })
    startDate: ui.fields.Date;

    @ui.decorators.dateField<PurchaseOrderPlanning>({
        title: 'Expected receipt date',
        isMandatory: true,
        minDate: new Date(),
        parent() {
            return this.mainBlock;
        },
    })
    requirementDate: ui.fields.Date;

    @ui.decorators.numericField<PurchaseOrderPlanning>({
        title: 'Gross price',
        min: 0,
        parent() {
            return this.mainBlock;
        },
        scale() {
            return masterDataCommon.getCompanyPriceScale(this.company.value || undefined);
        },
        prefix() {
            return this.company.value?.currency?.symbol || '';
        },
    })
    grossPrice: ui.fields.Numeric;

    @ui.decorators.labelField<PurchaseOrderPlanning>({
        title: 'Create order status',
        optionType: '@sage/xtrem-supply-planning/SupplyPlanningStatus',
        parent() {
            return this.mainBlock;
        },
        backgroundColor(value: SupplyPlanningStatus) {
            return pillColorDocument.setEnumSupplyPlanningStatusColor(value, 'backgroundColor');
        },
        borderColor(value: SupplyPlanningStatus) {
            return pillColorDocument.setEnumSupplyPlanningStatusColor(value, 'borderColor');
        },
        color(value: SupplyPlanningStatus) {
            return pillColorDocument.setEnumSupplyPlanningStatusColor(value, 'textColor');
        },
    })
    status: ui.fields.Label;

    @ui.decorators.labelField<PurchaseOrderPlanning>({
        title: 'Suggestion source',
        optionType: '@sage/xtrem-supply-planning/SupplyPlanningSource',
        parent() {
            return this.mainBlock;
        },
        backgroundColor(value: SupplyPlanningSource) {
            return pillColorDocument.setEnumSupplyPlanningSourceColor(value, 'backgroundColor');
        },
        borderColor(value: SupplyPlanningSource) {
            return pillColorDocument.setEnumSupplyPlanningSourceColor(value, 'borderColor');
        },
        color(value: SupplyPlanningSource) {
            return pillColorDocument.setEnumSupplyPlanningSourceColor(value, 'textColor');
        },
    })
    suggestionSource: ui.fields.Label;

    @ui.decorators.dateField<PurchaseOrderPlanning>({
        isReadOnly: true,
        title: 'Suggestion date',
        parent() {
            return this.mainBlock;
        },
    })
    suggestionDate: ui.fields.Date;

    @ui.decorators.referenceField<PurchaseOrderPlanning, User>({
        node: '@sage/xtrem-system/User',
        valueField: 'displayName',
        isReadOnly: true,
        title: 'Calculation user',
        parent() {
            return this.mainBlock;
        },
    })
    suggestionUser: ui.fields.Reference<User>;

    @ui.decorators.pageAction<PurchaseOrderPlanning>({
        title: 'Save',
        buttonType: 'primary',
        access: { bind: '$update' },
        async onClick() {
            await this.$standardSaveAction.execute();
        },
    })
    save: ui.PageAction;
}
