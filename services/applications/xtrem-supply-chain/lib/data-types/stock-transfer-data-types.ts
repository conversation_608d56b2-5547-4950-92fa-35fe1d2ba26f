import { ReferenceDataType } from '@sage/xtrem-core';
import * as xtremSupply<PERSON>hain from '../index';

export const stockTransferReceipt = new ReferenceDataType<xtremSupplyChain.nodes.StockTransferReceipt>({
    reference: () => xtremSupplyChain.nodes.StockTransferReceipt,
    lookup: {
        valuePath: 'number',
        helperTextPath: 'number',
        columnPaths: ['number', 'displayStatus', 'date'],
    },
});
