import { Activity } from '@sage/xtrem-core';
import * as xtremSupply<PERSON>hain from '..';

export const supplyPlanning = new Activity({
    description: 'Supply planning',
    node: () => xtremSupplyChain.nodes.SupplyPlanning,
    __filename,
    permissions: ['read', 'manage'],
    operationGrants: {
        manage: [
            {
                operations: ['read', 'update', 'createOrder', 'createWorkOrder'],
                on: [() => xtremSupplyChain.nodes.SupplyPlanning],
            },
        ],
    },
});
