import * as xtremCommunication from '@sage/xtrem-communication';
import { Activity } from '@sage/xtrem-core';
import * as xtremFinanceData from '@sage/xtrem-finance-data';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremReporting from '@sage/xtrem-reporting';
import * as xtremStockData from '@sage/xtrem-stock-data';
import * as xtremSupplyChain from '..';
import { commonStockTransferActivities } from '../functions/common';

export const stockTransferShipment = new Activity({
    description: 'Stock transfer shipment',
    node: () => xtremSupplyChain.nodes.StockTransferShipment,
    __filename,
    permissions: ['read', 'manage', 'post', 'confirm', 'print'],
    operationGrants: {
        read: [
            {
                operations: ['lookup'],
                on: [() => xtremSupplyChain.nodes.StockTransferOrder],
            },
            {
                operations: ['lookup'],
                on: [() => xtremSupplyChain.nodes.StockTransferShipmentLineToStockTransferOrderLine],
            },
            {
                operations: ['lookup'],
                on: [() => xtremSupplyChain.nodes.StockTransferReceiptLineToStockTransferShipmentLine],
            },
            {
                operations: ['proposeAllocationsToTransfer'],
                on: [() => xtremStockData.nodes.Stock],
            },
            {
                operations: ['lookup'],
                on: [() => xtremStockData.nodes.AllocationResult],
            },
            {
                operations: ['searchStock', 'lookup'],
                on: [() => xtremStockData.nodes.Stock],
            },
            {
                operations: ['haveStockDetail'],
                on: [() => xtremStockData.nodes.StockMovementDetail],
            },
            {
                operations: ['lookup'],
                on: [() => xtremMasterData.nodes.BaseLineToLine],
            },
            {
                operations: ['lookup'],
                on: [() => xtremSupplyChain.nodes.StockTransferOrder],
            },
            {
                operations: ['lookup'],
                on: [() => xtremSupplyChain.nodes.StockTransferReceipt],
            },
            {
                operations: ['lookup'],
                on: [() => xtremSupplyChain.nodes.StockTransferReceiptLine],
            },
            {
                operations: ['getValuationCost'],
                on: [() => xtremMasterData.nodes.ItemSite],
            },
            {
                operations: ['getAttributesAndDimensionsFromItem', 'getDefaultAttributesAndDimensions'],
                on: [() => xtremFinanceData.nodes.DimensionDefinitionLevelAndDefault],
            },
            {
                operations: ['financeIntegrationCheck'],
                on: [() => xtremSupplyChain.nodes.StockTransferOrder],
            },
            ...commonStockTransferActivities,
            ...xtremStockData.functions.allocationLib.allocationLookUp,
        ],
        manage: [
            {
                operations: ['read', 'create', 'update', 'delete', 'revoke', 'lookup'],
                on: [() => xtremSupplyChain.nodes.StockTransferShipment],
            },
            {
                operations: ['setDimension'],
                on: [() => xtremSupplyChain.nodes.StockTransferShipmentLine],
            },
            ...commonStockTransferActivities,
            ...xtremStockData.functions.allocationLib.allocationOperation,
            {
                operations: ['lookup'],
                on: [() => xtremSupplyChain.nodes.StockTransferShipmentLineToStockTransferOrderLine],
            },
            {
                operations: ['lookup'],
                on: [() => xtremSupplyChain.nodes.StockTransferReceiptLineToStockTransferShipmentLine],
            },
            {
                operations: ['createStockTransferShipment'],
                on: [() => xtremSupplyChain.nodes.StockTransferOrder],
            },
        ],
        post: [
            {
                operations: ['read', 'postToStock', 'repost', 'resendNotificationForFinance'],
                on: [() => xtremSupplyChain.nodes.StockTransferShipment],
            },
            ...commonStockTransferActivities,
            ...xtremStockData.functions.allocationLib.allocationLookUp,
            {
                operations: ['read'],
                on: [() => xtremCommunication.nodes.SysNotificationHistory],
            },
            {
                operations: ['lookup'],
                on: [() => xtremSupplyChain.nodes.StockTransferOrder],
            },
            {
                operations: ['lookup'],
                on: [() => xtremSupplyChain.nodes.StockTransferShipmentLineToStockTransferOrderLine],
            },
            {
                operations: ['lookup'],
                on: [() => xtremSupplyChain.nodes.StockTransferReceiptLineToStockTransferShipmentLine],
            },
        ],
        confirm: [
            { operations: ['read', 'confirm'], on: [() => xtremSupplyChain.nodes.StockTransferShipment] },
            ...commonStockTransferActivities,
            ...xtremStockData.functions.allocationLib.allocationLookUp,
            {
                operations: ['lookup'],
                on: [() => xtremSupplyChain.nodes.StockTransferShipment],
            },
            {
                operations: ['lookup'],
                on: [() => xtremSupplyChain.nodes.StockTransferShipmentLineToStockTransferOrderLine],
            },
            {
                operations: ['lookup'],
                on: [() => xtremSupplyChain.nodes.StockTransferReceiptLineToStockTransferShipmentLine],
            },
        ],
        print: [
            {
                operations: [
                    'read',
                    'afterPrintStockTransferPackingSlip',
                    'printBulkStockTransferShipmentPackingSlip',
                    'printBulkStockTransferShipmentPickList',
                    'beforePrintStockTransferShipmentPickList',
                    'beforePrintStockTransferPackingSlip',
                ],
                on: [() => xtremSupplyChain.nodes.StockTransferShipment],
            },
            {
                operations: ['generateReportPdf', 'generateReportZip', 'generateReports'],
                on: [() => xtremReporting.nodes.Report],
            },
            ...commonStockTransferActivities,
            ...xtremStockData.functions.allocationLib.allocationLookUp,
            {
                operations: ['lookup'],
                on: [() => xtremSupplyChain.nodes.StockTransferOrder],
            },
            {
                operations: ['lookup'],
                on: [() => xtremSupplyChain.nodes.StockTransferShipmentLineToStockTransferOrderLine],
            },
            {
                operations: ['lookup'],
                on: [() => xtremSupplyChain.nodes.StockTransferReceiptLineToStockTransferShipmentLine],
            },
        ],
    },
});
