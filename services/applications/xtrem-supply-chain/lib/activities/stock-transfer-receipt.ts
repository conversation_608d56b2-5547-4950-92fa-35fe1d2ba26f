import * as xtremCommunication from '@sage/xtrem-communication';
import type { OperationGrant } from '@sage/xtrem-core';
import { Activity } from '@sage/xtrem-core';
import * as xtremStockData from '@sage/xtrem-stock-data';
import * as xtremSupply<PERSON>hain from '..';
import { commonStockTransferActivities } from '../functions/common';
import { StockTransferReceipt } from '../nodes/stock-transfer-receipt';

const { stockReceiptOperations } = xtremStockData.functions.stockDetailLib;
const commonStockTransferReceiptOperations: OperationGrant[] = [
    ...commonStockTransferActivities,
    ...stockReceiptOperations,
];

export const stockTransferReceipt = new Activity({
    description: 'Stock transfer receipt',
    node: () => StockTransferReceipt,
    __filename,
    permissions: ['read', 'manage', 'post'],
    operationGrants: {
        read: [
            {
                operations: ['lookup'],
                on: [() => xtremSupplyChain.nodes.StockTransferShipment],
            },
            {
                operations: ['lookup'],
                on: [() => xtremSupplyChain.nodes.StockTransferReceiptLineToStockTransferShipmentLine],
            },
            ...commonStockTransferReceiptOperations,
        ],
        manage: [
            {
                operations: ['read', 'create', 'update', 'delete'],
                on: [() => StockTransferReceipt],
            },
            // {
            //     operations: ['setDimension'],
            //     on: [() => xtremSupplyChain.nodes.StockTransferReceipt],
            // },
            {
                operations: ['lookup'],
                on: [() => xtremSupplyChain.nodes.StockTransferShipment],
            },
            {
                operations: ['lookup'],
                on: [() => xtremSupplyChain.nodes.StockTransferReceiptLineToStockTransferShipmentLine],
            },
            ...commonStockTransferReceiptOperations,
        ],
        post: [
            {
                operations: ['read', 'repost', 'post', 'resendNotificationForFinance'],
                on: [() => StockTransferReceipt],
            },
            {
                operations: ['read'],
                on: [() => xtremCommunication.nodes.SysNotificationHistory],
            },
            {
                operations: ['lookup'],
                on: [() => xtremSupplyChain.nodes.StockTransferOrder],
            },
            {
                operations: ['lookup'],
                on: [() => xtremSupplyChain.nodes.StockTransferShipment],
            },
            {
                operations: ['lookup'],
                on: [() => xtremSupplyChain.nodes.StockTransferReceiptLineToStockTransferShipmentLine],
            },
            ...commonStockTransferReceiptOperations,
        ],
    },
});
