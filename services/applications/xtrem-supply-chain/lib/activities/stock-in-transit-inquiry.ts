import { Activity } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremSupply<PERSON>hain from '..';
import { commonStockTransferActivities } from '../functions/common';

export const stockInTransitInquiry = new Activity({
    description: 'Stock in-transit inquiry',
    node: () => xtremSupplyChain.nodes.StockTransferInTransitInquiry,
    __filename,
    permissions: ['manage'],
    operationGrants: {
        manage: [
            {
                operations: ['read', 'create', 'update', 'delete', 'lookup'],
                on: [() => xtremSupplyChain.nodes.StockTransferInTransitInquiry],
            },
            ...commonStockTransferActivities,
            {
                operations: ['lookup'],
                on: [() => xtremSupplyChain.nodes.StockTransferShipmentLineInTransit],
            },
            {
                operations: ['lookup'],
                on: [() => xtremSupplyChain.nodes.StockTransferShipment],
            },
            {
                operations: ['lookup'],
                on: [() => xtremSupplyChain.nodes.StockTransferShipmentLine],
            },
            {
                operations: ['getValuationCost'],
                on: [() => xtremMasterData.nodes.ItemSite],
            },
        ],
    },
});
