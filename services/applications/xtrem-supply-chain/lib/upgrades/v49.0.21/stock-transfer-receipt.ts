import { CustomSqlAction } from '@sage/xtrem-system';

export const stockTransferReceiptShippingSite = new CustomSqlAction({
    description: 'Add properties to the stock transfer receipt - shipping site',
    fixes: {
        notNullableColumns: [{ table: 'stock_transfer_receipt', column: 'shipping_site' }],
    },
    body: async helper => {
        await helper.executeSql(`DO $$
        DECLARE
            sRecord RECORD;
        BEGIN
            FOR sRecord IN (
                SELECT
                    receipt._tenant_id,
                    receipt._id as stockTransferReceiptId,
                    site._id as siteId
                FROM
                    ${helper.schemaName}.base_document as receipt

                INNER JOIN
                    ${helper.schemaName}.base_distribution_document AS bdd ON bdd._id = receipt._id AND bdd._tenant_id = receipt._tenant_id
                INNER JOIN
                    ${helper.schemaName}.base_business_relation AS bbr ON bbr._id = bdd.business_relation AND bbr._tenant_id = receipt._tenant_id
                INNER JOIN
                    ${helper.schemaName}.site AS site ON site._id = (
                        SELECT _id
                        FROM ${helper.schemaName}.site
                        WHERE business_entity = bbr.business_entity AND is_inventory = TRUE AND _tenant_id = receipt._tenant_id
                        LIMIT 1
                    )
                WHERE
                    receipt._constructor = 'StockTransferReceipt'
                )
                LOOP
                    UPDATE
                        ${helper.schemaName}.stock_transfer_receipt
                    SET
                        shipping_site = sRecord.siteId
                    WHERE
                        _id = sRecord.stockTransferReceiptId AND _tenant_id = sRecord._tenant_id;
                END LOOP;
        END $$;`);
    },
});
