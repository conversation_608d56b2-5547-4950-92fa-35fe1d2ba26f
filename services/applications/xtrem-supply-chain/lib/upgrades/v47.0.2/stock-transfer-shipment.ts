import { CustomSqlAction } from '@sage/xtrem-system';

export const stockTransferShipmentReceivingSite = new CustomSqlAction({
    description: 'Add properties to the stock transfer shipment - receiving site',
    fixes: {
        notNullableColumns: [{ table: 'stock_transfer_shipment', column: 'receiving_site' }],
    },
    body: async helper => {
        await helper.executeSql(`DO $$
        BEGIN
            WITH CTE AS (
                SELECT DISTINCT ON (a._tenant_id) a._id, a._tenant_id
                FROM ${helper.schemaName}.site a
                WHERE a.is_inventory = True AND a.is_active = true
                LIMIT 1
            )
            UPDATE ${helper.schemaName}.stock_transfer_shipment a
            SET receiving_site = cte._id
            FROM CTE cte
            WHERE a._tenant_id = cte._tenant_id;
        END $$;`);
    },
});

export const stockTransferShipmentSupplier = new CustomSqlAction({
    description: 'Add properties to the stock transfer shipment - supplier',
    fixes: {
        notNullableColumns: [{ table: 'stock_transfer_shipment', column: 'supplier' }],
    },
    body: async helper => {
        await helper.executeSql(`DO $$
            BEGIN
                WITH CTE AS (
                    SELECT DISTINCT ON (a._tenant_id) a._id, a._tenant_id
                    FROM ${helper.schemaName}.supplier a
                    INNER JOIN ${helper.schemaName}.base_business_relation b
                    ON a._id = b._id AND a._tenant_id = b._tenant_id
                    WHERE b.is_active = True
                    LIMIT 1
                )
                UPDATE ${helper.schemaName}.stock_transfer_shipment a
                SET supplier = cte._id
                FROM CTE cte
                WHERE a._tenant_id = cte._tenant_id;
            END $$;`);
    },
});
