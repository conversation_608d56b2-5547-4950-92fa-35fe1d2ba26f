import type { date, decimal, integer, Reference } from '@sage/xtrem-core';
import { decorators, useDefaultValue } from '@sage/xtrem-core';
import { Decimal } from '@sage/xtrem-decimal';
import * as xtremMasterData from '@sage/xtrem-master-data';
import type * as xtremSystem from '@sage/xtrem-system';
import * as xtremSupply<PERSON>hain from '..';

@decorators.subNode<WorkInProgressStockTransferOrderLine>({
    isPublished: true,
    extends: () => xtremMasterData.nodes.WorkInProgress,
    canRead: true,
    canSearch: true,
    isVitalReferenceChild: true,
})
export class WorkInProgressStockTransferOrderLine extends xtremMasterData.nodes.WorkInProgress {
    @decorators.referenceProperty<WorkInProgressStockTransferOrderLine, 'stockTransferOrderLine'>({
        isPublished: true,
        isStored: true,
        isVitalParent: true,
        node: () => xtremSupplyChain.nodes.StockTransferOrderLine,
    })
    readonly stockTransferOrderLine: Reference<xtremSupplyChain.nodes.StockTransferOrderLine>;

    @decorators.referencePropertyOverride<WorkInProgressStockTransferOrderLine, 'item'>({
        dependsOn: [{ stockTransferOrderLine: ['item'] }],
        async defaultValue() {
            return (await this.stockTransferOrderLine).item;
        },
        updatedValue: useDefaultValue,
    })
    override readonly item: Reference<xtremMasterData.nodes.Item | null>;

    @decorators.referencePropertyOverride<WorkInProgressStockTransferOrderLine, 'site'>({
        dependsOn: [{ stockTransferOrderLine: ['stockSite'] }],
        async defaultValue() {
            return (await this.stockTransferOrderLine).stockSite;
        },
        updatedValue: useDefaultValue,
    })
    override readonly site: Reference<xtremSystem.nodes.Site | null>;

    private async _setStatus(): Promise<xtremMasterData.enums.OrderType> {
        switch (await (await this.stockTransferOrderLine).status) {
            case 'draft':
                return 'suggested';
            case 'pending':
                return 'planned';
            case 'inProgress':
                return 'firm';
            case 'closed':
                return 'closed';
            default:
                return 'closed';
        }
    }

    @decorators.enumPropertyOverride<WorkInProgressStockTransferOrderLine, 'status'>({
        dependsOn: [{ stockTransferOrderLine: ['status'] }],
        defaultValue() {
            return this._setStatus();
        },
        updatedValue: useDefaultValue,
    })
    override readonly status: Promise<xtremMasterData.enums.OrderType | null>;

    @decorators.datePropertyOverride<WorkInProgressStockTransferOrderLine, 'startDate'>({
        dependsOn: [{ stockTransferOrderLine: [{ document: ['date'] }] }],
        async defaultValue() {
            return (await (await this.stockTransferOrderLine).document).date;
        },
        updatedValue: useDefaultValue,
    })
    override readonly startDate: Promise<date | null>;

    @decorators.datePropertyOverride<WorkInProgressStockTransferOrderLine, 'endDate'>({
        dependsOn: [{ stockTransferOrderLine: ['shippingDate'] }],
        async defaultValue() {
            return (await this.stockTransferOrderLine).shippingDate;
        },
        updatedValue: useDefaultValue,
    })
    override readonly endDate: Promise<date | null>;

    /* quantities are expressed in item.stockUnit */
    @decorators.decimalPropertyOverride<WorkInProgressStockTransferOrderLine, 'expectedQuantity'>({
        dependsOn: [{ stockTransferOrderLine: ['quantityInStockUnit'] }],
        defaultValue() {
            return this.determineExpectedQuantity();
        },
        updatedValue: useDefaultValue,
    })
    override readonly expectedQuantity: Promise<decimal>;

    @decorators.decimalPropertyOverride<WorkInProgressStockTransferOrderLine, 'actualQuantity'>({
        dependsOn: [
            {
                stockTransferOrderLine: ['shippedQuantityInStockUnit', { stockUnit: ['decimalDigits'] }],
            },
        ],
        defaultValue() {
            return this.determineActualQuantity();
        },
        updatedValue: useDefaultValue,
    })
    override readonly actualQuantity: Promise<decimal>;

    @decorators.decimalPropertyOverride<WorkInProgressStockTransferOrderLine, 'outstandingQuantity'>({
        dependsOn: ['expectedQuantity', 'actualQuantity'],
        async defaultValue() {
            return Math.max((await this.expectedQuantity) - (await this.actualQuantity), 0);
        },
        updatedValue: useDefaultValue,
    })
    override readonly outstandingQuantity: Promise<decimal>;

    @decorators.decimalPropertyOverride<WorkInProgressStockTransferOrderLine, 'remainingQuantityToAllocate'>({
        dependsOn: [{ stockTransferOrderLine: ['remainingQuantityToAllocateInStockUnit'] }],
        async getValue() {
            return (await this.stockTransferOrderLine).remainingQuantityToAllocateInStockUnit;
        },
    })
    override readonly remainingQuantityToAllocate: Promise<decimal>;

    @decorators.enumPropertyOverride<WorkInProgressStockTransferOrderLine, 'documentType'>({
        defaultValue() {
            return 'stockTransferOrder';
        },
    })
    override readonly documentType: Promise<xtremMasterData.enums.WorkInProgressDocumentType | null>;

    @decorators.stringPropertyOverride<WorkInProgressStockTransferOrderLine, 'documentNumber'>({
        dependsOn: [{ stockTransferOrderLine: [{ document: ['number'] }] }],
        async getValue() {
            return (await (await this.stockTransferOrderLine).document).number;
        },
    })
    override readonly documentNumber: Promise<string>;

    @decorators.integerPropertyOverride<WorkInProgressStockTransferOrderLine, 'documentLine'>({
        dependsOn: [{ stockTransferOrderLine: ['_sortValue'] }],
        async getValue() {
            return (await this.stockTransferOrderLine)._sortValue;
        },
    })
    override readonly documentLine: Promise<integer>;

    @decorators.integerPropertyOverride<WorkInProgressStockTransferOrderLine, 'documentId'>({
        async getValue() {
            return (await (await this.stockTransferOrderLine).document)._id;
        },
    })
    override readonly documentId: Promise<integer>;

    @decorators.enumPropertyOverride<WorkInProgressStockTransferOrderLine, 'originDocumentType'>({
        getValue() {
            return null;
        },
    })
    override readonly originDocumentType: Promise<xtremMasterData.enums.WorkInProgressDocumentType | null>;

    static async shippedQuantityInStockUnitComputed(
        stockTransferOrderLine: xtremSupplyChain.nodes.StockTransferOrderLine,
    ): Promise<number> {
        return +new Decimal(await stockTransferOrderLine.shippedQuantityInStockUnit).toDecimalPlaces(
            (await (await stockTransferOrderLine.stockUnit)?.decimalDigits) || 0,
        );
    }

    override async determineExpectedQuantity(): Promise<decimal> {
        return (await this.stockTransferOrderLine).quantityInStockUnit;
    }

    override async determineActualQuantity(): Promise<decimal> {
        return WorkInProgressStockTransferOrderLine.shippedQuantityInStockUnitComputed(
            await this.stockTransferOrderLine,
        );
    }
}
