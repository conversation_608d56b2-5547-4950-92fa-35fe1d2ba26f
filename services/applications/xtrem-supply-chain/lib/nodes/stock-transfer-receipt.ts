import type { NotificationEnvelope } from '@sage/xtrem-communication';
import type { Collection, Context, Reference } from '@sage/xtrem-core';
import { BusinessRuleError, date, decorators, integer, Logger, NodeStatus } from '@sage/xtrem-core';
import * as xtremDistribution from '@sage/xtrem-distribution';
import * as xtremFinanceData from '@sage/xtrem-finance-data';
import * as xtremLandedCost from '@sage/xtrem-landed-cost';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremStockData from '@sage/xtrem-stock-data';
import * as xtremSystem from '@sage/xtrem-system';
import * as xtremSupplyChain from '..';

const logger = Logger.getLogger(__filename, 'stock-transfer-receipt');

@decorators.subNode<StockTransferReceipt>({
    extends: () => xtremDistribution.nodes.BaseInboundReceiptDocument,
    canCreate: true,
    canRead: true,
    canUpdate: true,
    canDelete: false,
    canDuplicate: true,
    isPublished: true,
    isClearedByReset: true,
    isCustomizable: true,
    hasAttachments: true,
    async controlBegin(cx) {
        await xtremMasterData.events.control.baseDocumentControls.atLeastOneLine(cx, this);
    },
    async saveBegin() {
        const statuses = await xtremSupplyChain.functions.calculateReceiptStatuses(
            this.lines,
            await this.stockTransactionStatus,
        );
        if (statuses?.status && statuses?.displayStatus) {
            await this.$.set({
                status: statuses.status,
                displayStatus: statuses.displayStatus,
            });
        }

        if (this.$.status === NodeStatus.modified) {
            if ((await (await this.$.old).status) === 'readyToProcess' && (await this.status) === 'received') {
                await this.lines.forEach(async line => {
                    await line.$.set({ status: 'received' });
                });
            }
        }

        await xtremMasterData.functions.controlDocumentNumber(this);
    },
    controlDelete(cx) {
        cx.error.addLocalized(
            '@sage/xtrem-supply-chain/nodes__stock_transfer_receipt__deletion_not_allowed',
            'You cannot delete this stock transfer receipt.',
        );
    },
})
@xtremStockData.functions.staticImplements<xtremStockData.interfaces.StaticDocumentHeaderWithStockPosting>()
export class StockTransferReceipt
    extends xtremDistribution.nodes.BaseInboundReceiptDocument
    implements
        xtremLandedCost.interfaces
            .LandedCostAllocatedDocumentHeader<xtremLandedCost.enums.LandedCostDocumentTypeEnum.transaction>
{
    /** Needed for the accounting interface */
    @decorators.datePropertyOverride<StockTransferReceipt, 'documentDate'>({
        getValue() {
            return this.date;
        },
    })
    override readonly documentDate: Promise<date>;

    @decorators.enumPropertyOverride<StockTransferReceipt, 'status'>({
        defaultValue: 'readyToProcess',
    })
    override readonly status: Promise<xtremSupplyChain.enums.StockTransferReceiptStatus>;

    @decorators.enumPropertyOverride<StockTransferReceipt, 'displayStatus'>({
        dependsOn: ['status', 'stockTransactionStatus'],
        defaultValue: 'readyToProcess',
        async updatedValue() {
            return xtremSupplyChain.functions.calculateReceiptDisplayStatus(
                await this.status,
                await this.stockTransactionStatus,
            );
        },
    })
    override readonly displayStatus: Promise<xtremSupplyChain.enums.StockTransferReceiptDisplayStatus>;

    @decorators.collectionPropertyOverride<StockTransferReceipt, 'lines'>({
        node: () => xtremSupplyChain.nodes.StockTransferReceiptLine,
    })
    override readonly lines: Collection<xtremSupplyChain.nodes.StockTransferReceiptLine>;

    @decorators.referencePropertyOverride<StockTransferReceipt, 'site'>({
        isFrozen: true,
        filters: {
            control: {
                isInventory: true,
                businessEntity: { isCustomer: true, customer: { isActive: true } },
            },
        },
    })
    override readonly site: Reference<xtremSystem.nodes.Site>;

    @decorators.referencePropertyOverride<StockTransferReceipt, 'stockSite'>({
        isFrozen: true,
    })
    override readonly stockSite: Reference<xtremSystem.nodes.Site>;

    @decorators.referencePropertyOverride<StockTransferReceipt, 'supplier'>({
        isFrozen: true,
    })
    override readonly supplier: Reference<xtremMasterData.nodes.Supplier>;

    @decorators.referenceProperty<StockTransferReceipt, 'shippingSite'>({
        isStored: true,
        isPublished: true,
        isRequired: true,
        lookupAccess: true,
        isFrozen: true,
        node: () => xtremSystem.nodes.Site,
        dataType: () => xtremMasterData.dataTypes.masterDataSite,
        filters: {
            control: {
                isInventory: true,
                businessEntity: { isSupplier: true, supplier: { isActive: true } },
            },
        },
    })
    readonly shippingSite: Reference<xtremSystem.nodes.Site>;

    @decorators.collectionProperty<StockTransferReceipt, 'postingDetails'>({
        isPublished: true,
        join: {
            documentSysId() {
                return this._id;
            },
            documentType() {
                return 'stockTransferReceipt';
            },
        },
        node: () => xtremFinanceData.nodes.FinanceTransaction,
        lookupAccess: true,
    })
    readonly postingDetails: Collection<xtremFinanceData.nodes.FinanceTransaction>;

    // The usage of a computeValue instead of a query allows to use directly "this.lines"
    // instead of having to read the specific document first => This property can be copy/paste as is.
    @decorators.jsonProperty<StockTransferReceipt, 'jsonAggregateLandedCostTypes'>({
        isPublished: true,
        dependsOn: [{ lines: ['landedCostLines'] }],
        serviceOptions: () => [xtremMasterData.serviceOptions.landedCostOption],
        computeValue() {
            return xtremLandedCost.functions.landedCostLineLib.getLandedCostsPerType(this.lines);
        },
        lookupAccess: true,
    })
    readonly jsonAggregateLandedCostTypes: Promise<xtremLandedCost.interfaces.JsonAggregateLandedCostTypes>;

    override async executePost(context: Context): Promise<boolean> {
        const stockMovementRequestResult = JSON.parse(
            await StockTransferReceipt.postToStock(context, [this._id]),
        ) as xtremStockData.interfaces.PostToStockReturnJsonType<xtremStockData.enums.StockMovementTypeEnum.receipt>;

        // even if all lines where skipped (non-stock item) the result here will be requested so we can make the diff with errors
        return stockMovementRequestResult.result === 'requested';
    }

    static postToStock(context: Context, documentIds: integer[]): Promise<string> {
        return context.runInWritableContext(childContext => {
            return xtremStockData.functions.notificationLib.stockTransferRequestNotification(childContext, {
                documentClass: StockTransferReceipt,
                documentIds,
                stockUpdateParameters: {
                    intersiteTransferData: {
                        type: 'internalReceipt',
                    },
                },
            });
        });
    }

    @decorators.notificationListener<typeof StockTransferReceipt>({
        startsReadOnly: true,
        topic: 'StockTransferReceipt/stock/transfer/reply',
        onError(
            context: Context,
            envelope: NotificationEnvelope<
                xtremStockData.interfaces.StockReplyPayload<xtremStockData.enums.StockMovementTypeEnum.transfer>
            >,
            error: Error,
        ) {
            return xtremStockData.functions.notificationLib.errorHandler(
                context,
                envelope,
                error,
                StockTransferReceipt,
            );
        },
    })
    static async onStockReply(
        readOnlyContext: Context,
        payload: xtremStockData.interfaces.StockReplyPayload<xtremStockData.enums.StockMovementTypeEnum.transfer>,
    ): Promise<void> {
        const shipmentUpdateResult = await readOnlyContext.runInWritableContext(
            async writableContext =>
                (
                    await xtremStockData.functions.notificationLib.reactToStockMovementReply(
                        writableContext,
                        payload,
                        StockTransferReceipt,
                    )
                ).transfer,
        );

        if (!shipmentUpdateResult) return;

        if (!shipmentUpdateResult.transactionHadAnError) {
            await shipmentUpdateResult.documents.forEach(async document => {
                await readOnlyContext.runInWritableContext(async writableContext => {
                    const stockTransferReceipt = await writableContext.read(
                        StockTransferReceipt,
                        { _id: document.id },
                        { forUpdate: true },
                    );

                    if (document.isStockDocumentCompleted) {
                        await StockTransferReceipt.onceStockCompleted(writableContext, stockTransferReceipt);
                    }
                });
            });
        }
        //
    }

    async createAccountingNotification(): Promise<void> {
        // Send notification in order to create staging table entries for the accounting engine
        if (await (await (await this.site).legalCompany).doStockPosting) {
            const sourceDocumentNumber = await (
                await (
                    await (
                        await (
                            await (await this.lines.elementAt(0)).linkToStockTransferShipmentLines.elementAt(0)
                        ).to
                    ).linkToStockTransferOrderLines.elementAt(0)
                ).to
            ).documentNumber;

            await xtremFinanceData.functions.stockTransferReceiptNotification(
                this.$.context,
                this as xtremFinanceData.interfaces.FinanceOriginBaseDocument,
                (await this.lines.toArray()) as xtremFinanceData.interfaces.FinanceOriginDocumentLineWithStock[],
                sourceDocumentNumber,
            );
        }
    }

    @decorators.notificationListener<typeof StockTransferReceipt>({
        topic: 'StockTransferReceipt/accountingInterface',
    })
    static async onFinanceIntegrationReply(
        context: Context,
        payload: xtremFinanceData.interfaces.FinanceTransactionData,
    ): Promise<void> {
        await xtremFinanceData.functions.reactToFinanceIntegrationReply(context, payload);
    }

    static async onceStockCompleted(context: Context, stockTransferReceipt: StockTransferReceipt): Promise<void> {
        await xtremSupplyChain.functions.updateStockTransferShipmentLineInTransitAfterReceiptStockCompleted(
            context,
            stockTransferReceipt,
        );

        await stockTransferReceipt.createAccountingNotification();
    }

    // Repost creates an update notification to the acc engine ir order to update attributes and dimenions.
    // In fact, the existing finance document headers are preserved while the lines are all rebuilt (old ones deleted and new ones calculated)
    @decorators.mutation<typeof StockTransferReceipt, 'repost'>({
        isPublished: true,
        parameters: [
            {
                name: 'stockTransferReceipt',
                type: 'reference',
                isMandatory: true,
                isWritable: true,
                node: () => StockTransferReceipt,
            },
            {
                name: 'documentLines',
                type: 'array',
                item: {
                    type: 'object',
                    properties: {
                        baseDocumentLineSysId: 'integer',
                        storedAttributes: 'json',
                        storedDimensions: 'json',
                    },
                },
            },
        ],
        return: {
            type: 'object',
            properties: {
                wasSuccessful: 'boolean',
                message: 'string',
            },
        },
    })
    static async repost(
        context: Context,
        stockTransferReceipt: StockTransferReceipt,
        documentLines: xtremFinanceData.interfaces.FinanceIntegrationDocumentLineUpdate[],
    ): Promise<xtremFinanceData.interfaces.MutationResult> {
        if (!xtremFinanceData.functions.canRepost(await stockTransferReceipt.financeIntegrationStatus)) {
            throw new BusinessRuleError(
                context.localize(
                    '@sage/xtrem-supply-chain/nodes__stock_transfer_receipt__cant_repost_stock_transfer_receipt_when_status_is_not_failed',
                    'You can only repost a stock transfer receipt if the status is Failed or Not recorded.',
                ),
            );
        }

        const updateLines = documentLines.map(documentLine => {
            return {
                _id: documentLine.baseDocumentLineSysId,
                storedAttributes: documentLine.storedAttributes,
                storedDimensions: documentLine.storedDimensions,
            };
        });
        await stockTransferReceipt.$.set({
            forceUpdateForFinance: true,
            lines: updateLines,
        });
        await stockTransferReceipt.$.save();

        // send notification in order to update a staging table entry for the accounting engine
        await xtremFinanceData.functions.financeDocumentUpdateNotification(context, {
            document: stockTransferReceipt,
            lines: await stockTransferReceipt.lines.toArray(),
            documentType: 'stockTransferReceipt',
            replyTopic: 'StockTransferReceiptUpdate/accountingInterface',
            doNotPostOnUpdate: false,
        });
        return {
            wasSuccessful: true,
            message: context.localize(
                '@sage/xtrem-supply-chain/nodes__stock_transfer_receipt__document_was_posted',
                'The stock transfer receipt was posted.',
            ),
        };
    }

    @decorators.mutation<typeof StockTransferReceipt, 'resendNotificationForFinance'>({
        isPublished: true,
        parameters: [
            { name: 'stockTransferReceipt', type: 'reference', isMandatory: true, node: () => StockTransferReceipt },
        ],
        return: { type: 'boolean' },
    })
    static async resendNotificationForFinance(
        context: Context,
        stockTransferReceipt: StockTransferReceipt,
    ): Promise<boolean> {
        logger.info(
            context.localize(
                '@sage/xtrem-supply-chain/node__stock_transfer_receipt__resend_notification_for_finance',
                'Resending finance notification for stock transfer receipt: {{stockTransferReceiptNumber}}.',
                { stockTransferReceiptNumber: await stockTransferReceipt.number },
            ),
        );

        if (
            await xtremFinanceData.functions.AccountingEngineCommon.validateResendNotificationForFinance(context, {
                documentNumber: await stockTransferReceipt.number,
                documentType: 'stockTransferReceipt',
            })
        ) {
            await stockTransferReceipt.createAccountingNotification();
        }

        return true;
    }

    @decorators.notificationListener<typeof StockTransferReceipt>({
        topic: 'StockTransferReceipt/resendNotificationForFinance',
    })
    static async onResendNotificationForFinance(context: Context, document: { number: number }) {
        const stockTransferReceipt = await context.read(StockTransferReceipt, { number: document.number });

        await StockTransferReceipt.resendNotificationForFinance(context, stockTransferReceipt);
    }

    override get landedCostAssignableLines() {
        return this.lines;
    }
}
