import type { Collection, Reference, decimal } from '@sage/xtrem-core';
import { NodeStatus, date, decorators, useDefaultValue } from '@sage/xtrem-core';
import * as xtremDistribution from '@sage/xtrem-distribution';
import * as xtremFinanceData from '@sage/xtrem-finance-data';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremStockData from '@sage/xtrem-stock-data';
import * as xtremSystem from '@sage/xtrem-system';
import * as xtremSupplyChain from '..';

@decorators.subNode<StockTransferOrderLine>({
    extends: () => xtremDistribution.nodes.BaseOutboundOrderDocumentLine,
    isPublished: true,
    isVitalCollectionChild: true,
    canSearch: true,
    canRead: true,
    async createEnd() {
        await this.$.set({ workInProgress: {} });
    },
    async saveBegin() {
        if (
            this.$.status === NodeStatus.unchanged &&
            (await (await this.document).status) === 'pending' &&
            (await this.status) === 'draft'
        ) {
            await this.$.set({ status: 'pending' });
        }

        // Reset the allocationRequestStatus if line is updated
        if (
            this.$.status === NodeStatus.modified &&
            ['completed', 'error'].includes(await (await this.$.old).allocationRequestStatus)
        ) {
            await this.$.set({ allocationRequestStatus: 'noRequest' });
        }

        if (this.quantity === this.totalQuantityInStockUnitInAllLinkedShipments) {
            await this.$.set({ status: 'closed' });
        }
    },
    async controlBegin(cx) {
        await xtremSupplyChain.events.control.stockTransferOrderLine.quantityAllocatedInStockUnitIsGreaterThanRemainingQuantityToShipInStockUnit(
            cx,
            this,
        );
        await xtremSupplyChain.events.control.stockTransferOrderLine.lineStatusInDraftOrPending(cx, this);
        if (this.$.status === NodeStatus.added) {
            await xtremDistribution.events.DistributionDocumentLine.checkIfSameStatus(cx, this);
            await xtremDistribution.events.DistributionDocumentLine.checkIfSameStockSite(cx, this);
        }

        // Mandatory company attributes and dimensions control - to be replaced with financeIntegrationCheck when integration is implemented
        await xtremSupplyChain.functions.mandatoryDimensionAndAttributeControl(cx, this);
    },
    async deleteBegin() {
        await this.checkAndDeleteAssignment();
    },
})
export class StockTransferOrderLine
    extends xtremDistribution.nodes.BaseOutboundOrderDocumentLine
    implements xtremStockData.interfaces.DocumentLineWithStockAllocation
{
    @decorators.referencePropertyOverride<StockTransferOrderLine, 'document'>({
        node: () => xtremSupplyChain.nodes.StockTransferOrder,
    })
    override readonly document: Reference<xtremSupplyChain.nodes.StockTransferOrder>;

    @decorators.enumPropertyOverride<StockTransferOrderLine, 'status'>({
        defaultValue: 'draft',
    })
    override readonly status: Promise<xtremSupplyChain.enums.StockTransferOrderStatus>;

    @decorators.enumProperty<StockTransferOrderLine, 'receivingStatus'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremDistribution.enums.receivingStatusDataType,
        defaultValue: 'notReceived',
        lookupAccess: true,
    })
    readonly receivingStatus: Promise<xtremDistribution.enums.ReceivingStatus>;

    @decorators.referenceProperty<StockTransferOrderLine, 'receivingSite'>({
        isStored: true,
        isNullable: true,
        isPublished: true,
        lookupAccess: true,
        dependsOn: [{ document: ['receivingSite'] }],
        node: () => xtremSystem.nodes.Site,
        dataType: () => xtremSystem.dataTypes.site,
        async defaultValue() {
            return (await this.document)?.receivingSite;
        },
    })
    readonly receivingSite: Reference<xtremSystem.nodes.Site | null>;

    @decorators.referencePropertyOverride<StockTransferOrderLine, 'item'>({
        node: () => xtremMasterData.nodes.Item,
        dependsOn: ['stockSite', 'receivingSite'],
        filters: {
            control: {
                isStockManaged: true,
                type: 'good',
                _and: [
                    {
                        async itemSites() {
                            const shippingSite = await this.stockSite;
                            return {
                                _atLeast: 1,
                                site: shippingSite?._id,
                            };
                        },
                    },
                    {
                        async itemSites() {
                            const receivingSite = await this.receivingSite;
                            return {
                                _atLeast: 1,
                                site: receivingSite?._id,
                            };
                        },
                    },
                ],
            },
        },
    })
    override readonly item: Reference<xtremMasterData.nodes.Item>;

    @decorators.datePropertyOverride<StockTransferOrderLine, 'shippingDate'>({
        dependsOn: [
            { document: ['date'] },
            'requestedDeliveryDate',
            'doNotShipBeforeDate',
            'doNotShipAfterDate',
            'deliveryLeadTime',
        ],
        async defaultValue() {
            const document = await this.document;
            return xtremDistribution.functions.subWorkDays(this.$.context, {
                requestedDeliveryDate: await this.requestedDeliveryDate,
                orderDate: await document?.date,
                doNotShipBeforeDate: (await this.doNotShipBeforeDate) ?? null,
                doNotShipAfterDate: (await this.doNotShipAfterDate) ?? null,
                deliveryLeadTime: await this.deliveryLeadTime,
                workDaysMask: await document?.workDays,
            });
        },
        updatedValue: useDefaultValue,
        async control(cx, val) {
            await xtremSupplyChain.events.control.stockTransferOrderLine.controlShippingDate(cx, this, val);
        },
    })
    override readonly shippingDate: Promise<date>;

    @decorators.datePropertyOverride<StockTransferOrderLine, 'expectedDeliveryDate'>({
        dependsOn: ['shippingDate', 'deliveryLeadTime', { document: ['workDays'] }],
        async defaultValue() {
            return xtremDistribution.functions.addWorkDays(
                await this.shippingDate,
                await this.deliveryLeadTime,
                await (
                    await this.document
                )?.workDays,
            );
        },
    })
    override readonly expectedDeliveryDate: Promise<date>;

    @decorators.collectionProperty<StockTransferOrderLine, 'linkToStockTransferShipmentLines'>({
        node: () => xtremSupplyChain.nodes.StockTransferShipmentLineToStockTransferOrderLine,
        reverseReference: 'to',
        isAssociation: true,
    })
    readonly linkToStockTransferShipmentLines: Collection<xtremSupplyChain.nodes.StockTransferShipmentLineToStockTransferOrderLine>;

    @decorators.decimalProperty<StockTransferOrderLine, 'quantityToShipInProgressInStockUnit'>({
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.quantityInStockUnit,
        getValue() {
            return (
                this.linkToStockTransferShipmentLines
                    .where(async linkToStockTransferShipmentLine =>
                        ['readyToProcess', 'readyToShip'].includes(
                            await (
                                await (
                                    await linkToStockTransferShipmentLine.from
                                ).document
                            ).status,
                        ),
                    )
                    .sum(
                        async linkToStockTransferShipmentLine => (await linkToStockTransferShipmentLine.from).quantity,
                    ) ?? 0
            );
        },
    })
    readonly quantityToShipInProgressInStockUnit: Promise<decimal>;

    @decorators.decimalProperty<StockTransferOrderLine, 'shippedQuantityInStockUnit'>({
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.quantityInStockUnit,
        getValue() {
            return (
                this.linkToStockTransferShipmentLines
                    .where(async linkToStockTransferShipmentLine =>
                        ['shipped'].includes(
                            await (
                                await (
                                    await linkToStockTransferShipmentLine.from
                                ).document
                            ).status,
                        ),
                    )
                    .sum(
                        async linkToStockTransferShipmentLine => (await linkToStockTransferShipmentLine.from).quantity,
                    ) ?? 0
            );
        },
    })
    readonly shippedQuantityInStockUnit: Promise<decimal>;

    @decorators.decimalProperty<StockTransferOrderLine, 'totalQuantityInStockUnitInAllLinkedShipments'>({
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.quantityInStockUnit,
        getValue() {
            return (
                this.linkToStockTransferShipmentLines.sum(
                    async linkToStockTransferShipmentLine => (await linkToStockTransferShipmentLine.from).quantity,
                ) ?? 0
            );
        },
    })
    readonly totalQuantityInStockUnitInAllLinkedShipments: Promise<decimal>;

    @decorators.decimalPropertyOverride<StockTransferOrderLine, 'remainingQuantityToShipInStockUnit'>({
        async getValue() {
            return (
                (await this.quantityInStockUnit) -
                (await this.shippedQuantityInStockUnit) -
                (await this.quantityToShipInProgressInStockUnit)
            );
        },
    })
    override readonly remainingQuantityToShipInStockUnit: Promise<decimal>;

    @decorators.referencePropertyOverride<StockTransferOrderLine, 'unit'>({
        dependsOn: ['stockUnit'],
        defaultValue() {
            return this.stockUnit;
        },
    })
    override readonly unit: Reference<xtremMasterData.nodes.UnitOfMeasure>;

    @decorators.decimalPropertyOverride<StockTransferOrderLine, 'unitToStockUnitConversionFactor'>({
        defaultValue: 1,
        // isFrozen: true,
        // isFrozen can't be redefined by an extension
        async control(cx, val) {
            await cx.error.if(val).is.not.equal.to(1);
        },
    })
    override readonly unitToStockUnitConversionFactor: Promise<decimal>;

    @decorators.decimalPropertyOverride<StockTransferOrderLine, 'quantityInStockUnit'>({
        dependsOn: ['quantity'],
        defaultValue() {
            return this.quantity;
        },
        updatedValue: useDefaultValue,
    })
    override readonly quantityInStockUnit: Promise<decimal>;

    @decorators.decimalPropertyOverride<StockTransferOrderLine, 'quantity'>({
        async control(cx, quantity) {
            if ((await this.quantity) === quantity) {
                return;
            }
            await cx.error
                .withMessage(
                    '@sage/xtrem-supply-chain/stock_transfer_order_line__quantity_bellow_already_shipped_quantity',
                    'The stock transfer order line quantity cannot be lower than the quantity already shipped.',
                )
                .if(await this.quantity)
                .is.less.than(await this.totalQuantityInStockUnitInAllLinkedShipments);
        },
    })
    override readonly quantity: Promise<decimal>;

    @decorators.decimalPropertyOverride<StockTransferOrderLine, 'stockCostAmountInCompanyCurrency'>({
        dependsOn: ['quantityInStockUnit', 'item', { document: ['stockSite'] }],
        defaultValue() {
            return this.getNewStockCostAmount();
        },
        // updatedValue: useDefaultValue,
    })
    override readonly stockCostAmountInCompanyCurrency: Promise<decimal>;

    @decorators.referenceProperty<StockTransferOrderLine, 'workInProgress'>({
        isPublished: true,
        isVital: true,
        isNullable: true,
        reverseReference: 'stockTransferOrderLine',
        duplicatedValue: null,
        node: () => xtremSupplyChain.nodes.WorkInProgressStockTransferOrderLine,
    })
    readonly workInProgress: Reference<xtremSupplyChain.nodes.WorkInProgressStockTransferOrderLine | null>;

    @decorators.enumPropertyOverride<StockTransferOrderLine, 'dimensionDefinitionLevel'>({
        getValue() {
            return 'intersiteTransferOrder';
        },
    })
    override readonly dimensionDefinitionLevel: Promise<xtremFinanceData.enums.DimensionDefinitionLevel>;

    @decorators.jsonPropertyOverride<StockTransferOrderLine, 'storedDimensions'>({
        dependsOn: ['item', 'site', 'document', 'dimensionDefinitionLevel'],
        async defaultValue() {
            return xtremFinanceData.functions.getDefaultDimensions(
                this.$.context,
                await this.getDefaultDimensionParameter(),
            );
        },
    })
    override readonly storedDimensions: Promise<object | null>;

    @decorators.jsonPropertyOverride<StockTransferOrderLine, 'storedAttributes'>({
        dependsOn: ['item', 'site', 'document', 'dimensionDefinitionLevel'],
        async defaultValue() {
            return xtremFinanceData.functions.getDefaultAttributes(
                this.$.context,
                await this.getDefaultDimensionParameter(),
            );
        },
    })
    override readonly storedAttributes: Promise<xtremMasterData.interfaces.StoredAttributes | null>;

    @decorators.jsonPropertyOverride<StockTransferOrderLine, 'computedAttributes'>({
        async computeValue() {
            return xtremFinanceData.functions.computeAttributes(this.$.context, {
                salesSite: await this.site,
                secondSite: await this.site,
                item: await this.item,
                billToCustomer: await (await this.document).businessRelation,
            });
        },
    })
    override readonly computedAttributes: Promise<object>;

    @decorators.decimalProperty<StockTransferOrderLine, 'actualLandedCostInCompanyCurrency'>({
        isPublished: true,
        dependsOn: ['landedCostLines'],
        lookupAccess: true,
        serviceOptions: () => [xtremMasterData.serviceOptions.landedCostOption],
        dataType: () => xtremMasterData.dataTypes.amountInCompanyCurrency,
        async computeValue() {
            return xtremSupplyChain.functions.getStockTransferReceiptLineActualLandedCostInCompanyCurrency(
                await this.stockTransferReceiptLine,
            );
        },
    })
    actualLandedCostInCompanyCurrency: Promise<decimal>;

    @decorators.referenceProperty<StockTransferOrderLine, 'stockTransferReceiptLine'>({
        isPublished: true,
        isNullable: true,
        duplicatedValue: null,
        node: () => xtremSupplyChain.nodes.StockTransferReceiptLine,
        computeValue() {
            return xtremSupplyChain.functions.getStockTransferReceiptLineFromStockTransferOrderLine(
                this.$.context,
                this,
            );
        },
    })
    readonly stockTransferReceiptLine: Reference<xtremSupplyChain.nodes.StockTransferReceiptLine | null>;

    async getDefaultDimensionParameter(): Promise<xtremFinanceData.functions.DefaultOptions> {
        return {
            dimensionDefinitionLevel: await this.dimensionDefinitionLevel,
            onlyFromItem: false,
            companyId: (await (await this.site).legalCompany)._id,
            site: await this.site,
            customer: await (await this.document).businessRelation,
            supplier: await (await this.document).supplier,
            item: await this.item,
            shippingSite: await (await this.document).site,
            receivingSite: await (await this.document).receivingSite,
        };
    }

    async getNewStockCostAmount() {
        return (
            await xtremStockData.nodeExtensions.ItemSiteExtension.getValuationCost(
                this.$.context,
                await this.item,
                (await (await this.document)?.stockSite) ?? (await (await this.document).site),
                { quantity: await this.quantityInStockUnit, dateOfValuation: date.today(), valuationType: 'issue' },
            )
        ).amount;
    }

    async checkAndDeleteAssignment(): Promise<void> {
        if (await this.$.context.isServiceOptionEnabled(xtremMasterData.serviceOptions.orderToOrderOption)) {
            const workOrderAssignment = await xtremStockData.nodes.OrderAssignment.getDemandAssignment(
                this.$.context,
                'salesOrderLine',
                this,
                'workOrder',
            );
            if (workOrderAssignment && workOrderAssignment._id) {
                await this.$.context.delete(xtremStockData.nodes.OrderAssignment, { _id: workOrderAssignment._id });
            }

            const purchaseOrderAssignment = await xtremStockData.nodes.OrderAssignment.getDemandAssignment(
                this.$.context,
                'salesOrderLine',
                this,
                'purchaseOrderLine',
            );
            if (purchaseOrderAssignment && purchaseOrderAssignment._id) {
                await this.$.context.delete(xtremStockData.nodes.OrderAssignment, { _id: purchaseOrderAssignment._id });
            }
        }
    }
}
