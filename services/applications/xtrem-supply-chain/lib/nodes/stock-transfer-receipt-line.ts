import type { Collection, JsonType, NodeCreateData, Reference, decimal } from '@sage/xtrem-core';
import { NodeStatus, asyncArray, decorators, useDefaultValue } from '@sage/xtrem-core';
import * as xtremDistribution from '@sage/xtrem-distribution';
import * as xtremFinanceData from '@sage/xtrem-finance-data';
import * as xtremLandedCost from '@sage/xtrem-landed-cost';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremStockData from '@sage/xtrem-stock-data';
import * as xtremSupplyChain from '..';
import {
    transferShipmentLineAllocationsAfterReceiptCreatedOrOrderUpdated,
    updateStockTransferShipmentLineAfterReceiptCreated,
    updateStockTransferShipmentLineAfterReceiptDeleted,
    updateStockTransferShipmentLineAfterReceiptPosted,
} from '../functions';

@decorators.subNode<StockTransferReceiptLine>({
    isPublished: true,
    extends: () => xtremDistribution.nodes.BaseInboundReceiptDocumentLine,
    async controlBegin(cx) {
        if (this.$.status === NodeStatus.added) {
            await xtremDistribution.events.DistributionDocumentLine.checkIfSameStatus(cx, this);
            await xtremDistribution.events.DistributionDocumentLine.checkIfSameStockSite(cx, this);
        }
    },
    async createEnd() {
        await this.$.set({ workInProgress: {} });
        await updateStockTransferShipmentLineAfterReceiptCreated(this);
    },
    async deleteBegin() {
        await updateStockTransferShipmentLineAfterReceiptDeleted(this);
    },
    async saveBegin() {
        if (this.$.status === NodeStatus.modified) {
            const oldNode = await this.$.old;
            if ((await oldNode.status) === 'readyToProcess' && (await this.status) === 'received') {
                await this.$.set({
                    workInProgress: { actualQuantity: await this.quantityInStockUnit, outstandingQuantity: 0 },
                });
            }
        }
    },
    async saveEnd() {
        if (this.$.status === NodeStatus.modified) {
            const oldNode = await this.$.old;
            if ((await oldNode.status) === 'readyToProcess' && (await this.status) === 'received') {
                await updateStockTransferShipmentLineAfterReceiptPosted(this);
            }
        }
        await transferShipmentLineAllocationsAfterReceiptCreatedOrOrderUpdated(this);
    },
})
@xtremStockData.functions.staticImplements<xtremStockData.interfaces.StaticDocumentLineWithStockPosting>()
// xtremFinanceData.interfaces.FinanceOriginDocumentLine,
export class StockTransferReceiptLine
    extends xtremDistribution.nodes.BaseInboundReceiptDocumentLine
    implements
        xtremLandedCost.interfaces
            .LandedCostAllocatedDocumentLine<xtremLandedCost.enums.LandedCostDocumentTypeEnum.transaction>
{
    async getOrderCost(): Promise<decimal> {
        return (await (await this.shipmentLine)?.orderCost) ?? 0;
    }

    getValuedCost(): Promise<decimal> {
        return xtremStockData.functions.stockDocumentLib.defaultDocumentLineCost(this.$.context, this, {
            valuationType: 'receipt',
        });
    }

    // eslint-disable-next-line class-methods-use-this
    getValuationParameters(): xtremStockData.interfaces.ValuationParameter {
        return { valuationType: 'receipt' };
    }

    @decorators.referencePropertyOverride<StockTransferReceiptLine, 'document'>({
        node: () => xtremSupplyChain.nodes.StockTransferReceipt,
    })
    override readonly document: Reference<xtremSupplyChain.nodes.StockTransferReceipt>;

    @decorators.enumPropertyOverride<StockTransferReceiptLine, 'status'>({
        defaultValue: () => 'readyToProcess',
    })
    override readonly status: Promise<xtremSupplyChain.enums.StockTransferReceiptStatus>;

    @decorators.collectionProperty<StockTransferReceiptLine, 'stockDetails'>({
        isPublished: true,
        isVital: true,
        reverseReference: 'documentLine',
        node: () => xtremStockData.nodes.StockChangeDetail,
    })
    readonly stockDetails: Collection<xtremStockData.nodes.StockChangeDetail>;

    @decorators.jsonProperty<StockTransferReceiptLine, 'jsonStockDetails'>({
        isPublished: true,
        dependsOn: ['stockDetails', 'amountExcludingTaxInCompanyCurrency', 'shipmentLine'],
        async setValue(details) {
            const shipmentLineStockDetails = (await (await this.shipmentLine)?.stockDetails?.toArray()) ?? [];
            if (details && details.length > 0 && details.length === shipmentLineStockDetails.length) {
                await this.$.set({
                    stockDetails: xtremStockData.functions.stockDetailLib.parseDetails(
                        await asyncArray(details)
                            .map(async (detail, index) => {
                                delete detail._id;
                                delete detail.jsonStockDetailLot;
                                return {
                                    ...detail,
                                    intersiteReceivedStockRecord: null,
                                    isIntersite: true,
                                    stockRecord: shipmentLineStockDetails[index].intersiteReceivedStockRecord,
                                    intersiteShipmentDetail: shipmentLineStockDetails[index],
                                    orderCost: await shipmentLineStockDetails[index].orderCost,
                                };
                            })
                            .toArray(),
                    ) as NodeCreateData<xtremStockData.nodes.StockChangeDetail>[],
                });
            }
        },
        async computeValue() {
            if ((await this.stockDetails.length) > 0) {
                return this.stockDetails
                    .map(async detail => {
                        const detailsWithId = await xtremStockData.functions.stockDetailLib.filterStockDetailProperties(
                            detail,
                            xtremStockData.nodes.StockChangeDetail,
                            {
                                onlyIds: true,
                            },
                        );
                        return {
                            ...detailsWithId,
                            location: detailsWithId.location?._id,
                            status: detailsWithId.status?._id,
                            ...(detailsWithId.stockDetailLot
                                ? {
                                      jsonStockDetailLot: JSON.stringify(detailsWithId.stockDetailLot),
                                  }
                                : {}),
                        };
                    })
                    .toArray();
            }

            const shipmentLineStockDetails: Collection<xtremStockData.nodes.StockChangeDetail> | undefined = (
                await this.shipmentLine
            )?.stockDetails;

            if (shipmentLineStockDetails && (await shipmentLineStockDetails.length) > 0) {
                return shipmentLineStockDetails
                    .map(async shipmentDetail => {
                        const site = await this.site;
                        const detailsWithId = await xtremStockData.functions.stockDetailLib.filterStockDetailProperties(
                            shipmentDetail,
                            xtremStockData.nodes.StockChangeDetail,
                            {
                                onlyIds: true,
                            },
                        );

                        return {
                            documentLine: { _id: this._id },
                            stockRecord: { _id: (await shipmentDetail.intersiteReceivedStockRecord)?._id },
                            stockSite: { _id: site?._id },
                            item: detailsWithId.item?._id,
                            status: detailsWithId.status?._id,
                            quantityInStockUnit: detailsWithId.quantityInStockUnit,
                            stockUnit: detailsWithId.stockUnit?._id,
                            owner: await site?.id,
                            // At this stage location is virtual if Site isLocationManaged. Should be set to null.
                            ...(detailsWithId.location ? { location: null } : {}),
                            ...(detailsWithId.stockDetailLot
                                ? {
                                      jsonStockDetailLot: JSON.stringify(detailsWithId.stockDetailLot),
                                  }
                                : {}),
                            ...(detailsWithId.stockDetailLot
                                ? {
                                      stockDetailLot: {
                                          lot: detailsWithId.stockDetailLot?.lot,
                                      },
                                  }
                                : {}),
                            ...((detailsWithId.stockDetailSerialNumbers?.length || 0) > 0
                                ? {
                                      stockDetailSerialNumbers: detailsWithId.stockDetailSerialNumbers?.map(
                                          serialNumber => ({
                                              serialNumber: serialNumber.serialNumber,
                                          }),
                                      ),
                                  }
                                : {}),
                        } as NodeCreateData<xtremStockData.nodes.StockChangeDetail>;
                    })
                    .toArray();
            }
            // forcing typing to accept an empty object
            return {} as any;
        },
    })
    readonly jsonStockDetails: Promise<Array<JsonType<NodeCreateData<xtremStockData.nodes.StockChangeDetail>>>>;

    @decorators.collectionProperty<StockTransferReceiptLine, 'stockMovements'>({
        isPublished: true,
        reverseReference: 'documentLine',
        node: () => xtremStockData.nodes.StockJournal,
    })
    readonly stockMovements: Collection<xtremStockData.nodes.StockJournal>;

    @decorators.collectionPropertyOverride<StockTransferReceiptLine, 'stockTransactions'>({
        // isPublished: true,
        // reverseReference: 'documentLine',
        // isVital: true,
        node: () => xtremStockData.nodes.StockTransaction,
    })
    override readonly stockTransactions: Collection<xtremStockData.nodes.StockTransaction>;

    @decorators.referencePropertyOverride<StockTransferReceiptLine, 'unit'>({
        dependsOn: ['stockUnit'],
        defaultValue() {
            return this.stockUnit;
        },
        updatedValue: useDefaultValue,
    })
    override readonly unit: Reference<xtremMasterData.nodes.UnitOfMeasure>;

    @decorators.decimalPropertyOverride<StockTransferReceiptLine, 'unitToStockUnitConversionFactor'>({
        async control(cx, val) {
            await cx.error.if(val).is.negative();
            await cx.error.if(val).is.zero();
        },
        defaultValue: 1,
        updatedValue: useDefaultValue,
    })
    override readonly unitToStockUnitConversionFactor: Promise<decimal>;

    @decorators.decimalPropertyOverride<StockTransferReceiptLine, 'quantityInStockUnit'>({
        dependsOn: ['quantity'],
        async control(cx, val) {
            await cx.error.if(val).is.negative();
        },
        defaultValue() {
            return this.quantity;
        },
        updatedValue: useDefaultValue,
    })
    override readonly quantityInStockUnit: Promise<decimal>;

    @decorators.collectionProperty<StockTransferReceiptLine, 'linkToStockTransferShipmentLines'>({
        isPublished: true,
        isVital: true,
        forceFullSave: true,
        dependsOn: ['quantity'],
        reverseReference: 'from',
        node: () => xtremSupplyChain.nodes.StockTransferReceiptLineToStockTransferShipmentLine,
    })
    readonly linkToStockTransferShipmentLines: Collection<xtremSupplyChain.nodes.StockTransferReceiptLineToStockTransferShipmentLine>;

    @decorators.jsonPropertyOverride<StockTransferReceiptLine, 'computedAttributes'>({
        dependsOn: ['item', 'site', 'document'],
        async computeValue() {
            const shipmentLine = await this.shipmentLine;
            const customer = shipmentLine ? (await shipmentLine.document)?.businessRelation : undefined;

            return xtremFinanceData.functions.computeAttributes(this.$.context, {
                salesSite: await this.site,
                secondSite: await this.site,
                item: await this.item,
                billToCustomer: await customer,
                supplier: await (await this.document).supplier,
            });
        },
    })
    override readonly computedAttributes: Promise<object>;

    @decorators.referenceProperty<StockTransferReceiptLine, 'shipmentLine'>({
        isPublished: true,
        isNullable: true,
        dependsOn: ['linkToStockTransferShipmentLines'],
        node: () => xtremSupplyChain.nodes.StockTransferShipmentLine,
        async getValue() {
            return (await this.linkToStockTransferShipmentLines.takeOne(link => link != null))?.to ?? null;
        },
    })
    readonly shipmentLine: Reference<xtremSupplyChain.nodes.StockTransferShipmentLine | null>;

    @decorators.referenceProperty<StockTransferReceiptLine, 'workInProgress'>({
        isPublished: true,
        isVital: true,
        isNullable: true,
        reverseReference: 'stockTransferReceiptLine',
        duplicatedValue: null,
        node: () => xtremSupplyChain.nodes.WorkInProgressStockTransferReceiptLine,
    })
    readonly workInProgress: Reference<xtremSupplyChain.nodes.WorkInProgressStockTransferReceiptLine | null>;

    @decorators.enumProperty<StockTransferReceiptLine, 'stockDetailStatus'>({
        isPublished: true,
        isStored: false, // not stored in the database for V48 TODO: store in DB for V49
        dataType: () => xtremStockData.enums.stockDetailStatusDataType,
        dependsOn: ['item', 'stockDetails', 'quantityInStockUnit'],
        async computeValue() {
            return xtremStockData.functions.stockDetailLib.getStockDetailStatus({
                line: this,
                quantityExpected: await this.quantityInStockUnit,
                checkSerialNumbers: false,
            });
        },
        updatedValue: useDefaultValue,
    })
    readonly stockDetailStatus: Promise<xtremStockData.enums.StockDetailStatus>;

    @decorators.booleanPropertyOverride<StockTransferReceiptLine, 'canHaveLandedCostLine'>({
        getValue: () => true,
    })
    override readonly canHaveLandedCostLine: Promise<boolean>;

    // @decorators.decimalPropertyOverride<StockTransferReceiptLine, 'amountForLandedCostAllocation'>({
    //     // isPublished: true,
    //     serviceOptions: () => [xtremMasterData.serviceOptions.landedCostOption],
    //     dataType: () => xtremMasterData.dataTypes.amountInCompanyCurrency,
    //     getValue() {
    //         return 0;
    //     },
    // })
    // override readonly amountForLandedCostAllocation: Promise<decimal>;

    // @decorators.decimalProperty<StockTransferReceiptLine, 'quantityInStockUnitForLandedCostAllocation'>({
    //     dependsOn: ['quantityInStockUnit'],
    //     serviceOptions: () => [xtremMasterData.serviceOptions.landedCostOption],
    //     dataType: () => xtremMasterData.dataTypes.quantityInStockUnit,
    //     getValue() {
    //         return this.quantityInStockUnit;
    //     },
    // })
    // readonly quantityInStockUnitForLandedCostAllocation: Promise<decimal>;

    @decorators.decimalProperty<StockTransferReceiptLine, 'actualLandedCostInCompanyCurrency'>({
        isPublished: true,
        dependsOn: ['landedCostLines'],
        lookupAccess: true,
        serviceOptions: () => [xtremMasterData.serviceOptions.landedCostOption],
        dataType: () => xtremMasterData.dataTypes.amountInCompanyCurrency,
        getValue() {
            return xtremLandedCost.nodes.LandedCostLine.getTotalActualCostAmountInCompanyCurrency(this);
        },
    })
    actualLandedCostInCompanyCurrency: Promise<decimal>;

    // @decorators.referenceProperty<StockTransferReceiptLine, 'companyCurrency'>({
    //     isPublished: true,
    //     node: () => xtremMasterData.nodes.Currency,
    //     async getValue() {
    //         return (await this.document).companyCurrency;
    //     },
    // })
    // readonly companyCurrency: Reference<xtremMasterData.nodes.Currency>;

    // eslint-disable-next-line class-methods-use-this, require-await
    override async getOrderDocumentLine(): Promise<undefined> {
        return undefined;
    }
}
