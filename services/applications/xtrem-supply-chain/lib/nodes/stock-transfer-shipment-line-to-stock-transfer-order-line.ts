import type { Reference } from '@sage/xtrem-core';
import { decorators } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremSupplyChain from '../../index';

@decorators.subNode<StockTransferShipmentLineToStockTransferOrderLine>({
    extends: () => xtremMasterData.nodes.BaseLineToLine,
    isPublished: true,
    canRead: true,
    canSearch: true,
})
export class StockTransferShipmentLineToStockTransferOrderLine extends xtremMasterData.nodes.BaseLineToLine {
    @decorators.referencePropertyOverride<StockTransferShipmentLineToStockTransferOrderLine, 'to'>({
        node: () => xtremSupplyChain.nodes.StockTransferOrderLine,
    })
    override readonly to: Reference<xtremSupplyChain.nodes.StockTransferOrderLine>;

    @decorators.referencePropertyOverride<StockTransferShipmentLineToStockTransferOrderLine, 'from'>({
        node: () => xtremSupplyChain.nodes.StockTransferShipmentLine,
    })
    override readonly from: Reference<xtremSupplyChain.nodes.StockTransferShipmentLine>;
}
