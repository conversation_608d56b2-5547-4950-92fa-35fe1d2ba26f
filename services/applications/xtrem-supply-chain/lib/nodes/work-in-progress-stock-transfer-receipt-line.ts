import type { date, decimal, integer, Reference } from '@sage/xtrem-core';
import { decorators, useDefaultValue } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import type * as xtremSystem from '@sage/xtrem-system';
import * as xtremSupplyChain from '..';

@decorators.subNode<WorkInProgressStockTransferReceiptLine>({
    isPublished: true,
    extends: () => xtremMasterData.nodes.WorkInProgress,
    canRead: true,
    canSearch: true,
    isVitalReferenceChild: true,
})
export class WorkInProgressStockTransferReceiptLine extends xtremMasterData.nodes.WorkInProgress {
    @decorators.referenceProperty<WorkInProgressStockTransferReceiptLine, 'stockTransferReceiptLine'>({
        isPublished: true,
        isStored: true,
        isVitalParent: true,
        node: () => xtremSupplyChain.nodes.StockTransferReceiptLine,
    })
    readonly stockTransferReceiptLine: Reference<xtremSupplyChain.nodes.StockTransferReceiptLine>;

    @decorators.referencePropertyOverride<WorkInProgressStockTransferReceiptLine, 'item'>({
        dependsOn: [{ stockTransferReceiptLine: ['item'] }],
        async defaultValue() {
            return (await this.stockTransferReceiptLine).item;
        },
        updatedValue: useDefaultValue,
    })
    override readonly item: Reference<xtremMasterData.nodes.Item | null>;

    @decorators.referencePropertyOverride<WorkInProgressStockTransferReceiptLine, 'site'>({
        dependsOn: [{ stockTransferReceiptLine: ['stockSite'] }],
        async defaultValue() {
            return (await this.stockTransferReceiptLine).stockSite;
        },
        updatedValue: useDefaultValue,
    })
    override readonly site: Reference<xtremSystem.nodes.Site | null>;

    private async _setStatus(): Promise<xtremMasterData.enums.OrderType> {
        switch (await (await this.stockTransferReceiptLine).status) {
            case 'readyToProcess': {
                return 'firm';
            }
            case 'received': {
                return 'closed';
            }
            default:
                return 'closed';
        }
    }

    @decorators.enumPropertyOverride<WorkInProgressStockTransferReceiptLine, 'status'>({
        dependsOn: [{ stockTransferReceiptLine: ['status'] }],
        defaultValue() {
            return this._setStatus();
        },
        updatedValue: useDefaultValue,
    })
    override readonly status: Promise<xtremMasterData.enums.OrderType | null>;

    @decorators.datePropertyOverride<WorkInProgressStockTransferReceiptLine, 'startDate'>({
        dependsOn: [{ stockTransferReceiptLine: [{ document: ['date'] }] }],
        async defaultValue() {
            return (await (await this.stockTransferReceiptLine).document).date;
        },
        updatedValue: useDefaultValue,
    })
    override readonly startDate: Promise<date | null>;

    @decorators.datePropertyOverride<WorkInProgressStockTransferReceiptLine, 'endDate'>({
        dependsOn: [{ stockTransferReceiptLine: [{ document: ['date'] }] }],
        async defaultValue() {
            return (await (await this.stockTransferReceiptLine).document).date;
        },
        updatedValue: useDefaultValue,
    })
    override readonly endDate: Promise<date | null>;

    @decorators.decimalPropertyOverride<WorkInProgressStockTransferReceiptLine, 'expectedQuantity'>({
        dependsOn: [{ stockTransferReceiptLine: ['quantityInStockUnit'] }],
        async defaultValue() {
            return (await this.stockTransferReceiptLine).quantityInStockUnit;
        },
        updatedValue: useDefaultValue,
    })
    override readonly expectedQuantity: Promise<decimal>;

    @decorators.decimalPropertyOverride<WorkInProgressStockTransferReceiptLine, 'actualQuantity'>({
        defaultValue: 0,
    })
    override readonly actualQuantity: Promise<decimal>;

    @decorators.decimalPropertyOverride<WorkInProgressStockTransferReceiptLine, 'outstandingQuantity'>({
        dependsOn: ['expectedQuantity', 'actualQuantity'],
        defaultValue() {
            return this.expectedQuantity;
        },
        async updatedValue() {
            return Math.max((await this.expectedQuantity) - (await this.actualQuantity), 0);
        },
    })
    override readonly outstandingQuantity: Promise<decimal>;

    @decorators.enumPropertyOverride<WorkInProgressStockTransferReceiptLine, 'documentType'>({
        defaultValue() {
            return 'stockTransferReceipt';
        },
    })
    override readonly documentType: Promise<xtremMasterData.enums.WorkInProgressDocumentType | null>;

    @decorators.stringPropertyOverride<WorkInProgressStockTransferReceiptLine, 'documentNumber'>({
        dependsOn: [{ stockTransferReceiptLine: [{ document: ['number'] }] }],
        async getValue() {
            return (await (await this.stockTransferReceiptLine).document).number;
        },
    })
    override readonly documentNumber: Promise<string>;

    @decorators.integerPropertyOverride<WorkInProgressStockTransferReceiptLine, 'documentLine'>({
        dependsOn: [{ stockTransferReceiptLine: ['_sortValue'] }],
        async getValue() {
            return (await this.stockTransferReceiptLine)._sortValue;
        },
    })
    override readonly documentLine: Promise<integer>;

    @decorators.integerPropertyOverride<WorkInProgressStockTransferReceiptLine, 'documentId'>({
        async getValue() {
            return (await (await this.stockTransferReceiptLine).document)._id;
        },
    })
    override readonly documentId: Promise<integer>;

    @decorators.enumPropertyOverride<WorkInProgressStockTransferReceiptLine, 'originDocumentType'>({
        getValue() {
            return null;
        },
    })
    override readonly originDocumentType: Promise<xtremMasterData.enums.WorkInProgressDocumentType | null>;
}
