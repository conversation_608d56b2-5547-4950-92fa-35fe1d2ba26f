import type { Collection, NodeQueryFilter } from '@sage/xtrem-core';
import { decorators } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremSupplyChain from '..';

@decorators.subNode<StockTransferInTransitInquiry>({
    isPublished: true,
    canCreate: true,
    canRead: true,
    canUpdate: true,
    canDelete: false,
    extends: () => xtremMasterData.nodes.BaseDocumentLineInquiry,
})
export class StockTransferInTransitInquiry extends xtremMasterData.nodes.BaseDocumentLineInquiry {
    @decorators.collectionPropertyOverride<StockTransferInTransitInquiry, 'lines'>({
        node: () => xtremSupplyChain.nodes.StockTransferShipmentLine,
    })
    override readonly lines: Collection<xtremSupplyChain.nodes.StockTransferShipmentLine>;

    override async addFilter(): Promise<Partial<NodeQueryFilter<xtremSupplyChain.nodes.StockTransferShipmentLine>>> {
        const date = await this.date;
        return Promise.resolve({
            inTransit: {
                _ne: null,
                startDate: { _lte: date },
                _or: [{ endDate: { _gt: date } }, { endDate: null }],
            },
        });
    }
}
