import type { Context, Reference, decimal } from '@sage/xtrem-core';
import { Logger, Node, SystemError, date, decorators, useDefaultValue } from '@sage/xtrem-core';
import * as xtremManufacturing from '@sage/xtrem-manufacturing';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremMrpData from '@sage/xtrem-mrp-data';
import * as xtremPurchasing from '@sage/xtrem-purchasing';
import * as xtremSystem from '@sage/xtrem-system';
import * as xtremSupplyChain from '..';

const logger = Logger.getLogger(__filename, 'xtrem-supply-planning');

@decorators.node<SupplyPlanning>({
    isClearedByReset: true,
    isPublished: true,
    canRead: true,
    canUpdate: true,
    storage: 'sql',
    indexes: [
        {
            orderBy: {
                mrpResultLine: +1,
            },
            isUnique: true,
        },
    ],
})
export class SupplyPlanning extends Node {
    @decorators.referenceProperty<SupplyPlanning, 'purchaseRequisitionLine'>({
        isNullable: true,
        isPublished: true,
        isStored: true,
        node: () => xtremPurchasing.nodes.PurchaseRequisitionLine,
    })
    readonly purchaseRequisitionLine: Reference<xtremPurchasing.nodes.PurchaseRequisitionLine | null>;

    @decorators.referenceProperty<SupplyPlanning, 'mrpResultLine'>({
        isPublished: true,
        isStored: true,
        node: () => xtremMrpData.nodes.MrpResultLine,
    })
    readonly mrpResultLine: Reference<xtremMrpData.nodes.MrpResultLine>;

    @decorators.enumProperty<SupplyPlanning, 'type'>({
        isNullable: true,
        isPublished: true,
        isStored: true,
        lookupAccess: true,
        dataType: () => xtremSupplyChain.enums.supplyPlanningTypeDataType,
    })
    readonly type: Promise<xtremSupplyChain.enums.SupplyPlanningType | null>;

    @decorators.referenceProperty<SupplyPlanning, 'purchaseSite'>({
        isNullable: true,
        isPublished: true,
        isStored: true,
        node: () => xtremSystem.nodes.Site,
        provides: ['site'],
    })
    readonly purchaseSite: Reference<xtremSystem.nodes.Site | null>;

    @decorators.referenceProperty<SupplyPlanning, 'itemSite'>({
        isPublished: true,
        isStored: true,
        node: () => xtremMasterData.nodes.ItemSite,
    })
    readonly itemSite: Reference<xtremMasterData.nodes.ItemSite>;

    @decorators.referenceProperty<SupplyPlanning, 'supplier'>({
        isNullable: true,
        isPublished: true,
        isStored: true,
        dependsOn: ['itemSite', 'purchaseSite'],
        node: () => xtremMasterData.nodes.Supplier,
        async defaultValue() {
            const site = await this.purchaseSite;

            return site
                ? xtremMasterData.nodes.Supplier.getDefaultSupplier(
                      this.$.context,
                      await (
                          await this.itemSite
                      ).item,
                      site,
                  )
                : null;
        },
        updatedValue: useDefaultValue,
    })
    readonly supplier: Reference<xtremMasterData.nodes.Supplier | null>;

    @decorators.referenceProperty<SupplyPlanning, 'itemSupplier'>({
        isNullable: true,
        isPublished: true,
        isStored: true,
        dependsOn: ['itemSite', 'supplier'],
        node: () => xtremMasterData.nodes.ItemSupplier,
        async defaultValue() {
            const supplier = await this.supplier;
            return supplier
                ? this.$.context.tryRead(xtremMasterData.nodes.ItemSupplier, {
                      item: await (await this.itemSite).item,
                      supplier,
                  })
                : null;
        },
        updatedValue: useDefaultValue,
    })
    readonly itemSupplier: Reference<xtremMasterData.nodes.ItemSupplier | null>;

    @decorators.referenceProperty<SupplyPlanning, 'purchaseUnit'>({
        isNullable: true,
        isPublished: true,
        isStored: true,
        dependsOn: ['itemSupplier', 'itemSite'],
        node: () => xtremMasterData.nodes.UnitOfMeasure,
        async defaultValue() {
            return (await this.itemSupplier)?.purchaseUnitOfMeasure ?? (await (await this.itemSite).item).purchaseUnit;
        },
        updatedValue: useDefaultValue,
    })
    readonly purchaseUnit: Reference<xtremMasterData.nodes.UnitOfMeasure | null>;

    @decorators.decimalProperty<SupplyPlanning, 'purchaseQuantity'>({
        isNullable: true,
        isPublished: true,
        isStored: true,
        dependsOn: ['supplier', 'purchaseUnit', 'itemSite'],
        dataType: () => xtremMasterData.dataTypes.stockQuantity,
        control(cx, val) {
            if (val !== null && val <= 0) {
                cx.error.addLocalized(
                    '@sage/xtrem-supply-chain/nodes__supply_planning__purchase_quantity_zero_or_less',
                    'The purchase quantity cannot be less than or equal to 0.',
                );
            }
        },
        async updatedValue() {
            const supplier = await this.supplier;
            const oldSupplier = await (await this.$.old).supplier;
            const currentQuantity = await this.purchaseQuantity;

            if (supplier && supplier._id !== oldSupplier?._id) {
                const purchaseUnit = await this.purchaseUnit;
                const oldPurchaseUnit = await (await this.$.old).purchaseUnit;
                if (currentQuantity && purchaseUnit && oldPurchaseUnit && purchaseUnit._id !== oldPurchaseUnit._id) {
                    return xtremMasterData.functions.convertFromTo(
                        oldPurchaseUnit,
                        purchaseUnit,
                        currentQuantity,
                        false,
                        undefined,
                        'purchase',
                        await (
                            await this.itemSite
                        ).item,
                        supplier,
                    );
                }
            }

            return currentQuantity;
        },
    })
    readonly purchaseQuantity: Promise<decimal | null>;

    @decorators.referenceProperty<SupplyPlanning, 'stockUnit'>({
        isPublished: true,
        dependsOn: ['itemSite'],
        node: () => xtremMasterData.nodes.UnitOfMeasure,
        async getValue() {
            return (await (await this.itemSite).item).stockUnit;
        },
    })
    readonly stockUnit: Reference<xtremMasterData.nodes.UnitOfMeasure>;

    @decorators.decimalProperty<SupplyPlanning, 'stockQuantity'>({
        isPublished: true,
        isStored: true,
        dependsOn: ['purchaseUnit', 'purchaseQuantity', 'stockUnit', 'type', 'itemSite', 'supplier'],
        dataType: () => xtremMasterData.dataTypes.stockQuantity,
        async defaultValue() {
            const qty = await this.purchaseQuantity;
            const supplier = (await this.supplier) ?? undefined;
            if (qty !== null) {
                return xtremMasterData.functions.convertFromTo(
                    await this.purchaseUnit,
                    await this.stockUnit,
                    qty,
                    false,
                    undefined,
                    supplier ? 'purchase' : 'other',
                    await (
                        await this.itemSite
                    ).item,
                    supplier,
                );
            }
            return 0;
        },
        updatedValue: useDefaultValue,
    })
    readonly stockQuantity: Promise<decimal>;

    @decorators.booleanProperty<SupplyPlanning, 'canBypassDateControl'>({})
    readonly canBypassDateControl: Promise<boolean>;

    @decorators.dateProperty<SupplyPlanning, 'startDate'>({
        isPublished: true,
        isStored: true,
        dependsOn: ['type', 'canBypassDateControl'],
        async control(cx, val) {
            if (val && !(await this.canBypassDateControl)) {
                if ((await this.type) === 'purchased') {
                    await cx.error.if(val).is.after(date.today());
                } else if ((await this.type) === 'manufactured') {
                    await cx.error.if(val).is.before(date.today());
                }
            }
        },
    })
    readonly startDate: Promise<date>;

    @decorators.dateProperty<SupplyPlanning, 'requirementDate'>({
        isPublished: true,
        isStored: true,
        dependsOn: ['type'],
        async control(cx, val) {
            if (val && !(await this.canBypassDateControl) && (await this.type) === 'purchased') {
                await cx.error.if(val).is.before(date.today());
            }
        },
    })
    readonly requirementDate: Promise<date>;

    @decorators.decimalProperty<SupplyPlanning, 'grossPrice'>({
        isPublished: true,
        isStored: true,
        dependsOn: ['purchaseSite', 'supplier', 'purchaseQuantity', 'purchaseUnit', 'startDate', 'itemSite'],
        dataType: () => xtremMasterData.dataTypes.price,
        async defaultValue() {
            const currency = await (await (await this.purchaseSite)?.legalCompany)?.currency;
            const item = await (await this.itemSite).item;
            const quantity = await this.purchaseQuantity;
            const unit = await this.purchaseUnit;

            if (currency && item && quantity !== null && unit) {
                const itemSupplierPrice = await xtremMasterData.nodes.ItemSupplierPrice.getPurchasePrice(
                    this.$.context,
                    {
                        site: await this.purchaseSite,
                        supplier: await this.supplier,
                        currency,
                        item,
                        quantity,
                        unit,
                        date: await this.startDate,
                        convertUnit: false,
                    },
                );

                return itemSupplierPrice > 0 ? itemSupplierPrice : 0;
            }

            return 0;
        },
        updatedValue: useDefaultValue,
        async control(cx, val) {
            if (val) {
                await cx.error.if(val).is.negative();
            }
        },
    })
    readonly grossPrice: Promise<decimal>;

    @decorators.enumProperty<SupplyPlanning, 'status'>({
        isPublished: true,
        isStored: true,
        lookupAccess: true,
        dataType: () => xtremSupplyChain.enums.supplyPlanningStatusDataType,
    })
    readonly status: Promise<xtremSupplyChain.enums.SupplyPlanningStatus>;

    @decorators.enumProperty<SupplyPlanning, 'suggestionSource'>({
        isPublished: true,
        isStored: true,
        lookupAccess: true,
        dataType: () => xtremSupplyChain.enums.supplyPlanningSourceDataType,
    })
    readonly suggestionSource: Promise<xtremSupplyChain.enums.SupplyPlanningSource>;

    @decorators.dateProperty<SupplyPlanning, 'suggestionDate'>({
        isPublished: true,
        isStored: true,
    })
    readonly suggestionDate: Promise<date>;

    @decorators.referenceProperty<SupplyPlanning, 'suggestionUser'>({
        isFrozen: true,
        isPublished: true,
        isStored: true,
        node: () => xtremSystem.nodes.User,
    })
    readonly suggestionUser: Reference<xtremSystem.nodes.User>;

    @decorators.referenceProperty<SupplyPlanning, 'workOrderCategory'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
        node: () => xtremManufacturing.nodes.WorkOrderCategory,
        dependsOn: ['type'],
        async defaultValue() {
            return (await this.type) !== 'purchased'
                ? xtremManufacturing.functions.getDefaultCategory(this.$.context)
                : null;
        },
    })
    readonly workOrderCategory: Reference<xtremManufacturing.nodes.WorkOrderCategory | null>;

    @decorators.enumProperty<SupplyPlanning, 'workOrderType'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
        lookupAccess: true,
        dataType: () => xtremManufacturing.enums.workOrderTypeDataType,
        dependsOn: ['type'],
        async defaultValue() {
            return (await this.type) !== 'purchased' ? 'planned' : null;
        },
    })
    readonly workOrderType: Promise<xtremManufacturing.enums.WorkOrderType | null>;

    @decorators.stringProperty<SupplyPlanning, 'workOrderNumber'>({
        isPublished: true,
        isStored: true,
        dataType: () => xtremMasterData.dataTypes.documentNumber,
    })
    readonly workOrderNumber: Promise<string>;

    @decorators.stringProperty<SupplyPlanning, 'workOrderName'>({
        isPublished: true,
        isStored: true,
        dataType: () => xtremSystem.dataTypes.name,
        dependsOn: ['itemSite', 'type'],
        async defaultValue() {
            return (await this.type) !== 'purchased' ? (await (await (await this.itemSite).item).name) || '' : '';
        },
    })
    readonly workOrderName: Promise<string>;

    @decorators.query<typeof SupplyPlanning, 'checkSupplyPlanningPurchaseOrderCreated'>({
        isPublished: true,
        return: 'boolean',
        parameters: [],
    })
    static async checkSupplyPlanningPurchaseOrderCreated(context: Context): Promise<boolean> {
        return !!(
            await context.select(
                xtremSupplyChain.nodes.SupplyPlanning,
                { _id: true },
                {
                    filter: {
                        status: 'createOrder',
                    },
                },
            )
        ).length;
    }

    @decorators.notificationListener<typeof SupplyPlanning>({
        topic: 'MRPCalculationRequestListener/mrp/calculation-done/broadcast',
    })
    static async onMrpCalculationDone(
        context: Context,
        payload: xtremMrpData.sharedFunctions.interfaces.MrpCalculationDoneBroadcastPayload,
    ) {
        logger.verbose(() => `onMRPWorkReply - ${JSON.stringify(payload)}`);

        const inputWorkLine = context.query(xtremMrpData.nodes.MrpWorkLine, {
            filter: { mrpCalculation: payload.calculationId },
        });

        if ((await inputWorkLine.length) > 0) {
            await context.bulkDeleteSql(xtremSupplyChain.nodes.SupplyPlanning, {
                where: {
                    itemSite: {
                        _in: await inputWorkLine
                            .map(async mrpWorkLine => {
                                return (
                                    await context.read(xtremMasterData.nodes.ItemSite, {
                                        item: await context.read(xtremMasterData.nodes.Item, {
                                            id: (await mrpWorkLine.item).id,
                                        }),
                                        site: await context.read(xtremSystem.nodes.Site, {
                                            id: (await mrpWorkLine.site).id,
                                        }),
                                    })
                                )._id;
                            })
                            .toArray(),
                    },
                },
            });
        }

        logger.verbose(() => `supply planning - suggestions start`);

        const inputResultLine = context.query(xtremMrpData.nodes.MrpResultLine, {
            filter: { mrpCalculation: payload.calculationId },
        });

        const mrpCalculation = await context.read(xtremMrpData.nodes.MrpCalculation, { _id: payload.calculationId });
        let supplyPlanningType: xtremSupplyChain.enums.SupplyPlanningType | null;

        await inputResultLine.forEach(async itemSiteBucket => {
            const itemSite = await itemSiteBucket.referenceItemSite;
            const item = await itemSite?.item;

            if (!itemSite || !item) {
                throw new SystemError(
                    `Bad reference to Item or ItemSite from MrpResultLine(_id:${itemSiteBucket._id})`,
                );
            }

            const site = await context.read(xtremSystem.nodes.Site, {
                id: (await itemSiteBucket.site).id,
            });

            const itemIsManufactured = await item?.isManufactured;
            const itemIsBought = await item?.isBought;

            if (itemIsManufactured && itemIsBought) {
                supplyPlanningType = null;
            } else if (itemIsBought) {
                supplyPlanningType = 'purchased';
            } else if (itemIsManufactured) {
                supplyPlanningType = 'manufactured';
            }

            const defaultSupplier = await xtremMasterData.nodes.Supplier.getDefaultSupplier(
                context,
                await context.read(xtremMasterData.nodes.Item, {
                    id: (await itemSiteBucket.item).id,
                }),
                site,
            );
            let itemSupplier;
            let price: number | undefined = 0;
            let purchaseSite;

            if (defaultSupplier) {
                itemSupplier = await context.read(xtremMasterData.nodes.ItemSupplier, {
                    item,
                    supplier: defaultSupplier,
                });
                const compatiblePrices = await context
                    .query(xtremMasterData.nodes.ItemSupplierPrice, {
                        filter: {
                            item,
                            supplier: defaultSupplier,
                        },
                    })
                    .toArray();
                price = await compatiblePrices.at(0)?.price;
            }

            if (await site.isPurchase) {
                purchaseSite = site;
            } else {
                const purchaseSites = await (await site.legalCompany).sites
                    .filter(companySite => companySite.isPurchase)
                    .toArray();
                if (purchaseSites.length === 1) {
                    [purchaseSite] = purchaseSites;
                }
            }

            const newSupplyPlanning = await context.create(xtremSupplyChain.nodes.SupplyPlanning, {
                mrpResultLine: itemSiteBucket._id,
                type: supplyPlanningType,
                purchaseSite,
                itemSite,
                supplier: defaultSupplier,
                stockQuantity: await itemSiteBucket.quantity,
                purchaseQuantity: await itemSiteBucket.quantity,
                startDate: await itemSiteBucket.startDate,
                purchaseUnit:
                    (await itemSupplier?.purchaseUnitOfMeasure) ?? (await (await itemSite?.item)?.purchaseUnit),
                stockUnit: await (await itemSite?.item)?.stockUnit,
                requirementDate: await itemSiteBucket.endDate,
                grossPrice: price,
                status: 'pending',
                suggestionSource: 'MRP',
                suggestionDate: (await mrpCalculation.calculationDate).date,
                suggestionUser: await mrpCalculation.user,
                canBypassDateControl: true,
            });
            await newSupplyPlanning.$.save();
        });
    }

    @decorators.bulkMutation<typeof SupplyPlanning, 'createOrder'>({
        isPublished: true,
        startsReadOnly: true,
        async onComplete(context, supplyPlanningArray) {
            await xtremSupplyChain.functions.bulkCreatePurchaseOrderHandler(context, supplyPlanningArray);
        },
    })
    static createOrder(_context: Context, supplyPlanning: SupplyPlanning) {
        return supplyPlanning.$.payload({ withIds: true });
    }

    @decorators.bulkMutation<typeof SupplyPlanning, 'createWorkOrder'>({
        isPublished: true,
        async onComplete(context, supplyPlanningArray) {
            await xtremSupplyChain.functions.bulkCreateWorkOrderHandler(context, supplyPlanningArray);
        },
    })
    static createWorkOrder(_context: Context, supplyPlanning: SupplyPlanning) {
        return supplyPlanning.$.payload({ withIds: true });
    }
}
