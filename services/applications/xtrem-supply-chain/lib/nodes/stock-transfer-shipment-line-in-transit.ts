import type { date, decimal, Reference } from '@sage/xtrem-core';
import { decorators, Node } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremSystem from '@sage/xtrem-system';
import * as xtremSupply<PERSON>hain from '..';

@decorators.node<StockTransferShipmentLineInTransit>({
    storage: 'sql',
    isPublished: true,
    canCreate: true,
    canRead: true,
    canUpdate: true,
    canDelete: true,
    canDuplicate: true,
    isCustomizable: true,
    isVitalReferenceChild: true,
})
export class StockTransferShipmentLineInTransit extends Node {
    @decorators.referenceProperty<StockTransferShipmentLineInTransit, 'line'>({
        isStored: true,
        isPublished: true,
        isRequired: true,
        lookupAccess: true,
        node: () => xtremSupplyChain.nodes.StockTransferShipmentLine,
        isVitalParent: true,
    })
    readonly line: Reference<xtremSupplyChain.nodes.StockTransferShipmentLine>;

    @decorators.decimalProperty<StockTransferShipmentLineInTransit, 'unitCost'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        dataType: () => xtremMasterData.dataTypes.costDataType,
    })
    readonly unitCost: Promise<decimal>;

    @decorators.referenceProperty<StockTransferShipmentLineInTransit, 'currency'>({
        isStored: true,
        isPublished: true,
        node: () => xtremMasterData.nodes.Currency,
        lookupAccess: true,
    })
    readonly currency: Reference<xtremMasterData.nodes.Currency>;

    @decorators.enumProperty<StockTransferShipmentLineInTransit, 'costType'>({
        isStored: true,
        isPublished: true,
        isRequired: true,
        dataType: () => xtremMasterData.enums.costValuationMethodDataType,
        lookupAccess: true,
    })
    readonly costType: Promise<xtremMasterData.enums.CostValuationMethod>;

    @decorators.decimalProperty<StockTransferShipmentLineInTransit, 'stockValue'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.stockVariationValue,
        lookupAccess: true,
    })
    readonly stockValue: Promise<decimal>;

    @decorators.decimalProperty<StockTransferShipmentLineInTransit, 'quantityInStockUnit'>({
        isStored: true,
        isPublished: true,
        isNotZero: true,
        dataType: () => xtremMasterData.dataTypes.quantityInUnit,
        lookupAccess: true,
    })
    readonly quantityInStockUnit: Promise<decimal>;

    @decorators.stringProperty<StockTransferShipmentLineInTransit, 'commodityCode'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremSystem.dataTypes.code,
        lookupAccess: true,
    })
    readonly commodityCode: Promise<string>;

    /**
     * Stock transfer shipment posting date
     */
    @decorators.dateProperty<StockTransferShipmentLineInTransit, 'startDate'>({
        isStored: true,
        isPublished: true,
        defaultValue: () => null,
        isNullable: true,
        lookupAccess: true,
    })
    readonly startDate: Promise<date | null>;

    /**
     * Stock transfer receipt posting date
     */
    @decorators.dateProperty<StockTransferShipmentLineInTransit, 'endDate'>({
        isStored: true,
        isPublished: true,
        defaultValue: () => null,
        isNullable: true,
        lookupAccess: true,
    })
    readonly endDate: Promise<date | null>;
}
