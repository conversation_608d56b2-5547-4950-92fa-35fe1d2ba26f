import type { Collection, Reference, decimal } from '@sage/xtrem-core';
import { NodeStatus, decorators, useDefaultValue } from '@sage/xtrem-core';
import * as xtremDistribution from '@sage/xtrem-distribution';
import * as xtremFinanceData from '@sage/xtrem-finance-data';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremStockData from '@sage/xtrem-stock-data';
import * as xtremSystem from '@sage/xtrem-system';
import * as xtremSupplyChain from '..';
import {
    transferOrderLineAllocationsAfterReceiptCreatedOrOrderUpdated,
    updateStockTransferOrderLineAfterReceiptCreated,
    updateStockTransferOrderLineAfterReceiptDeleted,
    updateStockTransferOrderLineAfterReceiptPosted,
    updateStockTransferOrderLineAfterShipmentCreated,
    updateStockTransferOrderLineAfterShipmentDeleted,
} from '../functions';

@decorators.subNode<StockTransferShipmentLine>({
    extends: () => xtremDistribution.nodes.BaseOutboundShipmentDocumentLine,
    isPublished: true,
    async controlBegin(cx) {
        if (this.$.status === NodeStatus.added) {
            await xtremSupplyChain.events.control.stockTransferShipmentLine.checkLineStatus(cx, this);
            await xtremDistribution.events.DistributionDocumentLine.checkIfSameStatus(cx, this);
            await xtremSupplyChain.events.control.stockTransferShipmentLine.checkIfSameSite(cx, this);
            await xtremDistribution.events.DistributionDocumentLine.checkIfSameStockSite(cx, this);
            await xtremSupplyChain.events.control.stockTransferShipmentLine.checkCreateLine(cx, this);
        }
    },
    async controlDelete(cx) {
        await xtremSupplyChain.events.control.stockTransferShipmentLine.checkDeleteLine(cx, this);
    },
    async saveEnd() {
        if (this.$.status === NodeStatus.modified) {
            const oldNode = await this.$.old;
            if ((await oldNode.status) === 'readyToShip' && (await this.status) === 'shipped') {
                await updateStockTransferOrderLineAfterReceiptCreated(this);
            }
            if ((await oldNode.status) === 'shipped' && (await this.status) === 'readyToShip') {
                await updateStockTransferOrderLineAfterReceiptDeleted(this);
            }
            if ((await oldNode.receivingStatus) === 'notReceived' && (await this.receivingStatus) === 'received') {
                await updateStockTransferOrderLineAfterReceiptPosted(this);
            }
        }
        await transferOrderLineAllocationsAfterReceiptCreatedOrOrderUpdated(this);
    },
    async createEnd() {
        await updateStockTransferOrderLineAfterShipmentCreated(this);
    },
    async deleteBegin() {
        await updateStockTransferOrderLineAfterShipmentDeleted(this);
    },
})
@xtremStockData.functions.staticImplements<xtremStockData.interfaces.StaticDocumentLineWithStockPosting>()
export class StockTransferShipmentLine extends xtremDistribution.nodes.BaseOutboundShipmentDocumentLine {
    getOrderCost(): Promise<decimal> {
        return xtremStockData.functions.stockDocumentLib.defaultDocumentLineCost(this.$.context, this, {
            valuationType: 'issue',
        });
    }

    getValuedCost(): Promise<decimal> {
        return this.getOrderCost();
    }

    // eslint-disable-next-line class-methods-use-this
    getValuationParameters(): xtremStockData.interfaces.ValuationParameter {
        return { valuationType: 'issue' };
    }

    @decorators.referencePropertyOverride<StockTransferShipmentLine, 'document'>({
        node: () => xtremSupplyChain.nodes.StockTransferShipment,
    })
    override readonly document: Reference<xtremSupplyChain.nodes.StockTransferShipment>;

    @decorators.enumPropertyOverride<StockTransferShipmentLine, 'status'>({
        defaultValue: () => 'readyToProcess',
    })
    override readonly status: Promise<xtremSupplyChain.enums.StockTransferShipmentStatus>;

    @decorators.referencePropertyOverride<StockTransferShipmentLine, 'site'>({
        dependsOn: [{ document: ['site'] }],
        node: () => xtremSystem.nodes.Site,
        filters: {
            control: {
                isInventory: true,
                async itemSites() {
                    return (await this.item) ? { _atLeast: 1, item: await this.item } : { _atLeast: 1 };
                },
            },
        },
        updatedValue: useDefaultValue,
    })
    override readonly site: Reference<xtremSystem.nodes.Site>;

    @decorators.referencePropertyOverride<StockTransferShipmentLine, 'stockSite'>({
        dependsOn: ['item', { document: ['site', 'stockSite'] }],
        filters: {
            control: {
                isInventory: true,
                async legalCompany(): Promise<number> {
                    return (await (await (await this.document)?.site)?.legalCompany)?._id;
                },
                async itemSites() {
                    return (await this.item) ? { _atLeast: 1, item: await this.item } : { _atLeast: 1 };
                },
            },
        },
    })
    override readonly stockSite: Reference<xtremSystem.nodes.Site>;

    @decorators.referencePropertyOverride<StockTransferShipmentLine, 'item'>({
        node: () => xtremMasterData.nodes.Item,
        filters: {
            control: {
                isStockManaged: true,
                type: 'good',
                async itemSites() {
                    return { _atLeast: 1, site: await this.stockSite };
                },
            },
        },
    })
    override readonly item: Reference<xtremMasterData.nodes.Item>;

    @decorators.referencePropertyOverride<StockTransferShipmentLine, 'unit'>({
        dependsOn: ['stockUnit'],
        defaultValue() {
            return this.stockUnit;
        },
        updatedValue: useDefaultValue,
    })
    override readonly unit: Reference<xtremMasterData.nodes.UnitOfMeasure>;

    @decorators.decimalPropertyOverride<StockTransferShipmentLine, 'unitToStockUnitConversionFactor'>({
        async control(cx, val) {
            await cx.error.if(val).is.negative();
            await cx.error.if(val).is.zero();
        },
        defaultValue: 1,
        updatedValue: useDefaultValue,
    })
    override readonly unitToStockUnitConversionFactor: Promise<decimal>;

    @decorators.decimalPropertyOverride<StockTransferShipmentLine, 'quantityInStockUnit'>({
        dependsOn: ['quantity'],
        async control(cx, val) {
            await cx.error.if(val).is.negative();
        },
        defaultValue() {
            return this.quantity;
        },
        updatedValue: useDefaultValue,
    })
    override readonly quantityInStockUnit: Promise<decimal>;

    @decorators.collectionPropertyOverride<StockTransferShipmentLine, 'stockDetails'>({
        node: () => xtremStockData.nodes.StockChangeDetail,
    })
    override readonly stockDetails: Collection<xtremStockData.nodes.StockChangeDetail>;

    @decorators.enumProperty<StockTransferShipmentLine, 'receivingStatus'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremDistribution.enums.receivingStatusDataType,
        defaultValue: 'notReceived',
        lookupAccess: true,
    })
    readonly receivingStatus: Promise<xtremDistribution.enums.ReceivingStatus>;

    @decorators.collectionProperty<StockTransferShipmentLine, 'linkToStockTransferOrderLines'>({
        isPublished: true,
        isVital: true,
        forceFullSave: true,
        reverseReference: 'from',
        dependsOn: ['quantity'],
        node: () => xtremSupplyChain.nodes.StockTransferShipmentLineToStockTransferOrderLine,
    })
    readonly linkToStockTransferOrderLines: Collection<xtremSupplyChain.nodes.StockTransferShipmentLineToStockTransferOrderLine>;

    @decorators.collectionProperty<StockTransferShipmentLine, 'linkToStockTransferReceiptLines'>({
        isPublished: true,
        node: () => xtremSupplyChain.nodes.StockTransferReceiptLineToStockTransferShipmentLine,
        reverseReference: 'to',
        isAssociation: true,
    })
    readonly linkToStockTransferReceiptLines: Collection<xtremSupplyChain.nodes.StockTransferReceiptLineToStockTransferShipmentLine>;

    @decorators.jsonPropertyOverride<StockTransferShipmentLine, 'computedAttributes'>({
        dependsOn: ['item', 'site', 'document'],
        async computeValue() {
            return xtremFinanceData.functions.computeAttributes(this.$.context, {
                salesSite: await this.site,
                secondSite: await this.site,
                item: await this.item,
                billToCustomer: await (await this.document).businessRelation,
                supplier: await (await this.document).supplier,
            });
        },
    })
    override readonly computedAttributes: Promise<object>;

    @decorators.referenceProperty<StockTransferShipmentLine, 'inTransit'>({
        node: () => xtremSupplyChain.nodes.StockTransferShipmentLineInTransit,
        isVital: true,
        reverseReference: 'line',
        isPublished: true,
        isNullable: true,
    })
    readonly inTransit: Reference<xtremSupplyChain.nodes.StockTransferShipmentLineInTransit | null>;
}
