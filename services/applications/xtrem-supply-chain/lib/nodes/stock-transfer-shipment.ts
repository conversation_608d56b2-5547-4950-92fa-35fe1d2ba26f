import type { NotificationEnvelope } from '@sage/xtrem-communication';
import type { Collection, Context, NodeCreateData, Reference, UpdateAction, date, integer } from '@sage/xtrem-core';
import { BusinessRuleError, Logger, NodeStatus, decorators, useDefaultValue } from '@sage/xtrem-core';
import * as xtremDistribution from '@sage/xtrem-distribution';
import * as xtremFinanceData from '@sage/xtrem-finance-data';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremReporting from '@sage/xtrem-reporting';
import * as xtremStockData from '@sage/xtrem-stock-data';
import * as xtremSystem from '@sage/xtrem-system';
import * as xtremSupplyChain from '..';

const logger = Logger.getLogger(__filename, 'stock-transfer-shipment');

@decorators.subNode<StockTransferShipment>({
    extends: () => xtremDistribution.nodes.BaseOutboundShipmentDocument,
    isPublished: true,
    canCreate: true,
    canRead: true,
    canUpdate: true,
    canDelete: true,
    isCustomizable: true,
    hasAttachments: true,
    async controlBegin(cx) {
        await xtremSupplyChain.events.control.stockTransferLib.shippingSiteIsNotEqualToReceivingSite(cx, this);
        await xtremSupplyChain.events.control.stockTransferLib.financialSiteIsEqualForShippingSiteAndReceivingSite(
            cx,
            this,
        );
        await xtremMasterData.events.control.baseDocumentControls.atLeastOneLine(cx, this);
    },
    async saveBegin() {
        const statuses = await xtremSupplyChain.functions.calculateShipmentStatuses(
            this.lines,
            await this.stockTransactionStatus,
        );
        if (statuses?.receivingStatus && statuses?.status && statuses?.displayStatus) {
            await this.$.set({
                receivingStatus: statuses.receivingStatus,
                status: statuses.status,
                displayStatus: statuses.displayStatus,
            });
        }

        await xtremMasterData.functions.controlDocumentNumber(this);
        if (['pending', 'inProgress'].includes(await this.status)) {
            if (this.$.status === NodeStatus.added) {
                await this.$.context.flushDeferredActions();
            }
        }
    },
})
@xtremStockData.functions.staticImplements<xtremStockData.interfaces.StaticDocumentHeaderWithStockPosting>()
export class StockTransferShipment extends xtremDistribution.nodes.BaseOutboundShipmentDocument {
    /** Needed for the accounting interface */
    @decorators.datePropertyOverride<StockTransferShipment, 'documentDate'>({
        getValue() {
            return this.date;
        },
    })
    override readonly documentDate: Promise<date>;

    @decorators.booleanPropertyOverride<StockTransferShipment, 'isPrinted'>({
        async control(cx, val) {
            if ((await this.status) === 'readyToProcess' && val === true) {
                cx.error.addLocalized(
                    '@sage/xtrem-supply-chain/nodes__stock_transfer_shipment__cant_print_packing_slip_when_status_is_ready_to_process',
                    'To print the packing slip, the stock transfer shipment cannot be Ready to process: {{number}}.',
                    {
                        number: await this.number,
                    },
                );
            }
        },
    })
    override readonly isPrinted: Promise<boolean>;

    @decorators.referencePropertyOverride<StockTransferShipment, 'site'>({
        node: () => xtremSystem.nodes.Site,
        filters: {
            control: {
                isInventory: true,
                businessEntity: { isSupplier: true, supplier: { isActive: true } },
            },
        },
    })
    override readonly site: Reference<xtremSystem.nodes.Site>;

    @decorators.referencePropertyOverride<StockTransferShipment, 'businessRelation'>({
        dependsOn: ['receivingSite'],
        async defaultValue() {
            return (await (await this.receivingSite)?.businessEntity)?.customer;
        },
    })
    override readonly businessRelation: Reference<xtremMasterData.nodes.Customer>;

    @decorators.enumPropertyOverride<StockTransferShipment, 'status'>({
        defaultValue: 'readyToProcess',
    })
    override readonly status: Promise<xtremSupplyChain.enums.StockTransferShipmentStatus>;

    @decorators.enumPropertyOverride<StockTransferShipment, 'displayStatus'>({
        dependsOn: ['status', 'receivingStatus', 'stockTransactionStatus'],
        defaultValue: 'readyToProcess',
        async updatedValue() {
            return xtremSupplyChain.functions.calculateShipmentDisplayStatus(
                await this.status,
                await this.receivingStatus,
                await this.stockTransactionStatus,
            );
        },
    })
    override readonly displayStatus: Promise<xtremSupplyChain.enums.StockTransferShipmentDisplayStatus>;

    @decorators.enumProperty<StockTransferShipment, 'receivingStatus'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremDistribution.enums.receivingStatusDataType,
        defaultValue: 'notReceived',
        lookupAccess: true,
    })
    readonly receivingStatus: Promise<xtremDistribution.enums.ReceivingStatus>;

    @decorators.referenceProperty<StockTransferShipment, 'supplier'>({
        isStored: true,
        isPublished: true,
        isRequired: true,
        node: () => xtremMasterData.nodes.Supplier,
        dataType: () => xtremMasterData.dataTypes.supplier,
        dependsOn: ['site'],
        lookupAccess: true,
        filters: {
            control: {
                async businessEntity() {
                    return (await (await this.site).businessEntity)._id;
                },
            },
        },
        async control(cx, val) {
            await xtremSystem.events.control.isActive(
                this.$.status,
                cx,
                this.$.status === NodeStatus.modified ? (await this.$.old)._id : null,
                val,
            );
        },
        async defaultValue() {
            if (await this.site) {
                return (await (await this.site).businessEntity).supplier;
            }
            return null;
        },
        updatedValue: useDefaultValue,
    })
    readonly supplier: Reference<xtremMasterData.nodes.Supplier>;

    @decorators.referenceProperty<StockTransferShipment, 'receivingSite'>({
        isStored: true,
        isPublished: true,
        isRequired: true,
        lookupAccess: true,
        isFrozen: true,
        node: () => xtremSystem.nodes.Site,
        dataType: () => xtremMasterData.dataTypes.masterDataSite,
        filters: {
            control: {
                isInventory: true,
                businessEntity: { isCustomer: true, customer: { isActive: true } },
            },
        },
    })
    readonly receivingSite: Reference<xtremSystem.nodes.Site>;

    @decorators.referencePropertyOverride<StockTransferShipment, 'financialSite'>({
        node: () => xtremSystem.nodes.Site,
        async control(cx, val) {
            await xtremSystem.events.control.isActive(
                this.$.status,
                cx,
                this.$.status === NodeStatus.modified ? (await this.$.old)._id : null,
                val,
            );
        },
        updatedValue: useDefaultValue,
    })
    override readonly financialSite: Reference<xtremSystem.nodes.Site>;

    @decorators.collectionPropertyOverride<StockTransferShipment, 'lines'>({
        node: () => xtremSupplyChain.nodes.StockTransferShipmentLine,
    })
    override readonly lines: Collection<xtremSupplyChain.nodes.StockTransferShipmentLine>;

    @decorators.collectionProperty<StockTransferShipment, 'postingDetails'>({
        lookupAccess: true,
        isPublished: true,
        node: () => xtremFinanceData.nodes.FinanceTransaction,
        join: {
            documentSysId() {
                return this._id;
            },
            documentType() {
                return 'stockTransferShipment';
            },
        },
    })
    readonly postingDetails: Collection<xtremFinanceData.nodes.FinanceTransaction>;

    async createAccountingNotification(): Promise<void> {
        // Send notification in order to create staging table entries for the accounting engine
        if (await (await (await this.site).legalCompany).doStockPosting) {
            const sourceDocumentNumber = await (
                await (
                    await (await this.lines.elementAt(0)).linkToStockTransferOrderLines.elementAt(0)
                ).to
            ).documentNumber;

            // Send notification in order to create staging table entries for the accounting engine
            await xtremFinanceData.functions.stockTransferShipmentNotification(
                this.$.context,
                this as xtremFinanceData.interfaces.FinanceOriginBaseDocument,
                (await this.lines.toArray()) as xtremFinanceData.interfaces.FinanceOriginDocumentLineWithStock[],
                sourceDocumentNumber,
            );
        }
    }

    @decorators.mutation<typeof StockTransferShipment, 'resendNotificationForFinance'>({
        isPublished: true,
        parameters: [
            { name: 'stockTransferShipment', type: 'reference', isMandatory: true, node: () => StockTransferShipment },
        ],
        return: { type: 'boolean' },
    })
    static async resendNotificationForFinance(
        context: Context,
        stockTransferShipment: StockTransferShipment,
    ): Promise<boolean> {
        logger.info(
            context.localize(
                '@sage/xtrem-supply-chain/node__stock_transfer_shipment__resend_notification_for_finance',
                'Resending finance notification for stock transfer shipment: {{stockTransferShipmentNumber}}.',
                { stockTransferShipmentNumber: await stockTransferShipment.number },
            ),
        );

        if (
            await xtremFinanceData.functions.AccountingEngineCommon.validateResendNotificationForFinance(context, {
                documentNumber: await stockTransferShipment.number,
                documentType: 'stockTransferShipment',
            })
        ) {
            await stockTransferShipment.createAccountingNotification();
        }

        return true;
    }

    @decorators.mutation<typeof StockTransferShipment, 'confirm'>({
        isPublished: true,
        parameters: [
            {
                name: 'stockTransferShipmentNumber',
                type: 'string',
            },
        ],
        return: 'string',
    })
    static async confirm(context: Context, stockTransferShipmentNumber: string): Promise<string> {
        const stockTransferShipment = await context.read(
            xtremSupplyChain.nodes.StockTransferShipment,
            {
                number: stockTransferShipmentNumber,
            },
            { forUpdate: true },
        );
        if ((await stockTransferShipment.status) !== 'readyToProcess') {
            throw new BusinessRuleError(
                context.localize(
                    '@sage/xtrem-supply-chain/nodes__stock_transfer_shipment__confirm_ready_to_process',
                    "You can only confirm a shipment if the status is 'Ready to process'.",
                ),
            );
        }
        await stockTransferShipment.$.set({ status: 'readyToShip' });
        await stockTransferShipment.lines.forEach(async (line: xtremSupplyChain.nodes.StockTransferShipmentLine) => {
            await line.$.set({ status: 'readyToShip' });
            if (
                (await line.quantityInStockUnit) !== (await line.quantityAllocatedInStockUnit) &&
                (await line.allocationStatus) !== 'notManaged'
            ) {
                throw new BusinessRuleError(
                    context.localize(
                        '@sage/xtrem-supply-chain/nodes__stock_transfer_shipment__confirm_unallocated lines',
                        'You need to allocate stock in full to all shipment lines before you can confirm the shipment.',
                    ),
                );
            }
        });
        await stockTransferShipment.$.save();
        return context.localize(
            '@sage/xtrem-supply-chain/nodes__supply-chain_confirm_success',
            "The stock transfer shipment is confirmed and set to 'Ready to ship'.",
        );
    }

    @decorators.mutation<typeof StockTransferShipment, 'revoke'>({
        isPublished: true,
        parameters: [
            {
                name: 'stockTransferShipmentNumber',
                type: 'string',
            },
        ],
        return: 'string',
    })
    static async revoke(context: Context, stockTransferShipmentNumber: string): Promise<string> {
        const stockTransferShipment = await context.read(
            xtremSupplyChain.nodes.StockTransferShipment,
            {
                number: stockTransferShipmentNumber,
            },
            { forUpdate: true },
        );

        if ((await stockTransferShipment.status) !== 'readyToShip') {
            throw new BusinessRuleError(
                context.localize(
                    '@sage/xtrem-supply-chain/nodes__stock_transfer_shipment__revoked_ready_to_ship',
                    "You can only revert a shipment if the status is 'Ready to ship'.",
                ),
            );
        }

        await stockTransferShipment.$.set({ status: 'readyToProcess' });
        await stockTransferShipment.lines.forEach(line => line.$.set({ status: 'readyToProcess' }));
        await stockTransferShipment.$.save();

        return context.localize(
            '@sage/xtrem-supply-chain/nodes__stock_transfer_shipment__revoked_success',
            "The shipment status was reverted. Its status is 'Ready to process'.",
        );
    }

    @decorators.mutation<typeof StockTransferShipment, 'createReceiptFromShipment'>({
        isPublished: true,
        parameters: [
            {
                name: 'stockTransferShipment',
                type: 'reference',
                node: () => xtremSupplyChain.nodes.StockTransferShipment,
                isMandatory: true,
            },
        ],
        return: {
            type: 'reference',
            node: () => xtremSupplyChain.nodes.StockTransferReceipt,
        },
    })
    static createReceiptFromShipment(
        context: Context,
        stockTransferShipment: xtremSupplyChain.nodes.StockTransferShipment,
    ): Promise<xtremSupplyChain.nodes.StockTransferReceipt> {
        return StockTransferShipment.createStockTransferReceipt(context, stockTransferShipment);
    }

    static async createStockTransferReceipt(
        context: Context,
        stockTransferShipment: StockTransferShipment,
    ): Promise<xtremSupplyChain.nodes.StockTransferReceipt> {
        let receiptHeader: NodeCreateData<xtremSupplyChain.nodes.StockTransferReceipt> = {};
        const receiptLines: NodeCreateData<xtremSupplyChain.nodes.StockTransferReceiptLine>[] =
            await stockTransferShipment.lines
                .map(async stockTransferShipmentLine => {
                    const site = (await stockTransferShipment.receivingSite)?._id;
                    const currency = (await stockTransferShipment.currency)?._id;
                    const item = (await stockTransferShipmentLine.item)?._id;
                    const unit = (await stockTransferShipmentLine.unit)?._id;
                    const stockUnit = (await stockTransferShipmentLine.stockUnit)?._id;
                    const quantity = await stockTransferShipmentLine.quantity;
                    const quantityInStockUnit = await stockTransferShipmentLine.quantityInStockUnit;
                    const owner = (await stockTransferShipment.receivingSite)?.id;

                    const stockRules = await xtremDistribution.functions.getDefaultStockRules(context, item, site);
                    const inboundDefaultLocation = stockRules?.inboundDefaultLocation;
                    const inboundDefaultQualityValue = stockRules?.inboundDefaultQualityValue;

                    let stockRecord: xtremStockData.nodes.Stock | null;
                    if (inboundDefaultLocation && inboundDefaultQualityValue && item !== undefined) {
                        const stockSearchData: xtremStockData.interfaces.StockSearchData = {
                            item,
                            location: inboundDefaultLocation ?? null,
                            site,
                            status: inboundDefaultQualityValue,
                            stockUnit,
                            owner: await owner,
                            lot: null,
                        };

                        if (stockSearchData) {
                            stockRecord = await xtremStockData.functions.stockLib.getStockRecord(
                                context,
                                stockSearchData,
                            );
                        }
                    }

                    const serialNumberArray: xtremStockData.nodes.SerialNumber[] = [];
                    // collect all serial numbers assigned to any stock detail over all lines
                    await stockTransferShipmentLine.stockDetails.forEach(async stockDetail => {
                        await stockDetail.stockDetailSerialNumbers.forEach(async detailSerialNumber => {
                            const serialNumId = await detailSerialNumber.serialNumber;
                            const serial = await context.read(xtremStockData.nodes.SerialNumber, {
                                _id: serialNumId?._id ?? '',
                            });
                            serialNumberArray.push(serial);
                        });
                    });

                    const stockDetailSerialNumbers = await Promise.all(
                        serialNumberArray.map(async serial => {
                            return {
                                action: 'create' as UpdateAction,
                                newSerialNumberId: await serial.id,
                                isUsable: true,
                                isInStock: false,
                                stockRecord: stockRecord?._id,
                            };
                        }),
                    );

                    if (Object.keys(receiptHeader).length === 0) {
                        receiptHeader = {
                            site,
                            stockSite: site,
                            shippingSite: (await stockTransferShipment.site)?._id,
                            currency,
                            supplier: (await stockTransferShipment.supplier)?._id,
                            date: await stockTransferShipment.deliveryDate,
                        };
                    }
                    return {
                        item,
                        itemDescription: await stockTransferShipmentLine.itemDescription,
                        quantity,
                        unit,
                        unitToStockUnitConversionFactor:
                            await stockTransferShipmentLine.unitToStockUnitConversionFactor,
                        stockUnit,
                        storedAttributes: await stockTransferShipmentLine.storedAttributes,
                        storedDimensions: await stockTransferShipmentLine.storedDimensions,
                        internalNote: await stockTransferShipmentLine.internalNote,
                        ...(inboundDefaultQualityValue &&
                            inboundDefaultLocation && {
                                jsonStockDetails: [
                                    {
                                        action: 'create' as UpdateAction,
                                        site,
                                        item,
                                        stockUnit,
                                        status: inboundDefaultQualityValue,
                                        location: inboundDefaultLocation,
                                        quantityInStockUnit,
                                        movementType: 'receipt' as const,
                                        stockDetailSerialNumbers: [...stockDetailSerialNumbers],
                                    },
                                ],
                            }),

                        stockDetailSerialNumberStatus: 'assigned',
                        linkToStockTransferShipmentLines: [
                            {
                                _action: 'create' as UpdateAction,
                                to: stockTransferShipmentLine._id,
                                quantity,
                                quantityInStockUnit,
                                amount: 0,
                                unit,
                                stockUnit,
                                currency,
                            },
                        ],
                    };
                })
                .toArray();
        const receiptPayload = {
            ...receiptHeader,
            lines: receiptLines,
        };

        const stockTransferReceipt = await context.create(xtremSupplyChain.nodes.StockTransferReceipt, receiptPayload);
        await stockTransferReceipt.$.save();
        return stockTransferReceipt;
    }

    @decorators.mutation<typeof StockTransferShipment, 'postToStock'>({
        isPublished: true,
        parameters: [
            {
                name: 'documentIds',
                type: 'array',
                item: {
                    type: 'integer',
                },
            },
        ],
        return: { type: 'string' },
    })
    static postToStock(context: Context, documentIds: integer[]): Promise<string> {
        return xtremStockData.functions.notificationLib.stockTransferRequestNotification(context, {
            documentClass: StockTransferShipment,
            documentIds,
            stockUpdateParameters: {
                intersiteTransferData: {
                    type: 'internalShipment',
                },
                stockDetailData: { isDocumentWithoutStockDetails: true },
                allocationData: { isUsingAllocations: true },
            },
        });
    }

    @decorators.notificationListener<typeof StockTransferShipment>({
        startsReadOnly: true,
        topic: 'StockTransferShipment/stock/transfer/reply',
        onError(
            context: Context,
            envelope: NotificationEnvelope<
                xtremStockData.interfaces.StockReplyPayload<xtremStockData.enums.StockMovementTypeEnum.transfer>
            >,
            error: Error,
        ) {
            return xtremStockData.functions.notificationLib.errorHandler(
                context,
                envelope,
                error,
                StockTransferShipment,
            );
        },
    })
    static async onStockReply(
        readOnlyContext: Context,
        payload: xtremStockData.interfaces.StockReplyPayload<xtremStockData.enums.StockMovementTypeEnum.transfer>,
    ): Promise<void> {
        const shipmentUpdateResult = await readOnlyContext.runInWritableContext(
            async writableContext =>
                (
                    await xtremStockData.functions.notificationLib.reactToStockMovementReply(
                        writableContext,
                        payload,
                        StockTransferShipment,
                    )
                ).transfer,
        );

        if (!shipmentUpdateResult) return;

        if (!shipmentUpdateResult.transactionHadAnError) {
            await shipmentUpdateResult.documents.forEach(async document => {
                await readOnlyContext.runInWritableContext(async writableContext => {
                    const stockTransferShipment = await writableContext.read(
                        StockTransferShipment,
                        { _id: document.id },
                        { forUpdate: true },
                    );

                    if (document.isStockDocumentCompleted) {
                        await StockTransferShipment.onceStockCompleted(writableContext, stockTransferShipment);
                    }
                });
            });
        }
    }

    @decorators.notificationListener<typeof StockTransferShipment>({
        topic: 'StockTransferShipment/accountingInterface',
    })
    static async onFinanceIntegrationReply(
        context: Context,
        payload: xtremFinanceData.interfaces.FinanceTransactionData,
    ): Promise<void> {
        await xtremFinanceData.functions.reactToFinanceIntegrationReply(context, payload);
    }

    static async onceStockCompleted(
        writableContext: Context,
        stockTransferShipment: StockTransferShipment,
    ): Promise<void> {
        await xtremSupplyChain.functions.updateStockTransferShipmentLinesAfterShipmentStockCompleted(
            writableContext,
            stockTransferShipment,
        );

        await stockTransferShipment.createAccountingNotification();

        // create stock transfer receipt
        const stockTransferReceipt = await StockTransferShipment.createStockTransferReceipt(
            writableContext,
            stockTransferShipment,
        );

        const urlId = btoa(JSON.stringify({ _id: stockTransferReceipt._id }));

        await writableContext.notifyUser({
            title: writableContext.localize(
                '@sage/xtrem-supply-chain/nodes__stock_transfer_shipment__stock_transfer_receipt__notification__title',
                'Stock transfer receipt',
            ),
            icon: 'add',
            description: writableContext.localize(
                '@sage/xtrem-supply-chain/nodes__stock_transfer_shipment__stock_transfer_receipt_created_successfully',
                'Stock transfer receipt created successfully.',
            ),
            level: 'success',
            shouldDisplayToast: true,
            actions: [
                {
                    link: `@sage/xtrem-supply-chain/StockTransferReceipt/${urlId}`,
                    title: 'Stock transfer receipt',
                    icon: 'link',
                    style: 'tertiary',
                },
            ],
        });
    }

    // Repost creates an update notification to the acc engine ir order to update attributes and dimenions.
    // In fact, the existing finance document headers are preserved while the lines are all rebuilt (old ones deleted and new ones calculated)
    @decorators.mutation<typeof StockTransferShipment, 'repost'>({
        isPublished: true,
        parameters: [
            {
                name: 'stockTransferShipment',
                type: 'reference',
                isMandatory: true,
                isWritable: true,
                node: () => StockTransferShipment,
            },
            {
                name: 'documentLines',
                type: 'array',
                item: {
                    type: 'object',
                    properties: {
                        baseDocumentLineSysId: 'integer',
                        storedAttributes: 'json',
                        storedDimensions: 'json',
                    },
                },
            },
        ],
        return: {
            type: 'object',
            properties: {
                wasSuccessful: 'boolean',
                message: 'string',
            },
        },
    })
    static async repost(
        context: Context,
        stockTransferShipment: StockTransferShipment,
        documentLines: xtremFinanceData.interfaces.FinanceIntegrationDocumentLineUpdate[],
    ): Promise<xtremFinanceData.interfaces.MutationResult> {
        if (!xtremFinanceData.functions.canRepost(await stockTransferShipment.financeIntegrationStatus)) {
            throw new BusinessRuleError(
                context.localize(
                    '@sage/xtrem-supply-chain/nodes__stock-transfer-shipment__cant_repost_stock_transfer_shipment_when_status_is_not_failed',
                    'You can only repost a stock transfer shipment if the status is Failed or Not recorded.',
                ),
            );
        }

        const updateLines = documentLines.map(documentLine => {
            return {
                _id: documentLine.baseDocumentLineSysId,
                storedAttributes: documentLine.storedAttributes,
                storedDimensions: documentLine.storedDimensions,
            };
        });
        await stockTransferShipment.$.set({
            forceUpdateForFinance: true,
            lines: updateLines,
        });
        await stockTransferShipment.$.save();

        // send notification in order to update a staging table entry for the accounting engine
        await xtremFinanceData.functions.financeDocumentUpdateNotification(context, {
            document: stockTransferShipment,
            lines: await stockTransferShipment.lines.toArray(),
            documentType: 'stockTransferShipment',
            replyTopic: 'StockTransferShipmentUpdate/accountingInterface',
            doNotPostOnUpdate: false,
        });
        return {
            wasSuccessful: true,
            message: context.localize(
                '@sage/xtrem-supply-chain/nodes__stock-transfer-shipment__document_was_posted',
                'The stock transfer shipment was posted.',
            ),
        };
    }

    @decorators.bulkMutation<typeof StockTransferShipment, 'printBulkStockTransferShipmentPackingSlip'>({
        isPublished: true,
        startsReadOnly: true,
        queue: 'reporting',
        async onComplete(context, reports) {
            const reportName = context.localize(
                '@sage/xtrem-supply-chain/nodes__stock_transfer_shipment__bulk_print_report_name',
                'Stock transfer shipment packing slip',
            );
            await xtremMasterData.functions.bulkPrintOnCompleteHandler(context, {
                documentType: 'stockTransferPackingSlip',
                documents: reports,
                reportName,
            });
        },
    })
    static printBulkStockTransferShipmentPackingSlip(context: Context, document: StockTransferShipment) {
        return xtremReporting.nodes.Report.generateReportPdf(context, 'stockTransferPackingSlip', '', {
            variables: JSON.stringify({
                stockTransferShipment: document._id,
            }),
            isBulk: true,
        });
    }

    @decorators.bulkMutation<typeof StockTransferShipment, 'printBulkStockTransferShipmentPickList'>({
        isPublished: true,
        startsReadOnly: true,
        queue: 'reporting',
        async onComplete(context, reports) {
            const reportName = context.localize(
                '@sage/xtrem-supply-chain/nodes__stock_transfer_shipment__bulk_print_pick_list_report_name',
                'Stock transfer shipment pick list',
            );
            await xtremMasterData.functions.bulkPrintOnCompleteHandler(context, {
                documentType: 'stockTransferShipmentPickList',
                documents: reports,
                reportName,
            });
        },
    })
    static printBulkStockTransferShipmentPickList(context: Context, document: StockTransferShipment) {
        return xtremReporting.nodes.Report.generateReportPdf(context, 'stockTransferShipmentPickList', '', {
            variables: JSON.stringify({ stockTransferShipment: document._id }),
        });
    }

    @decorators.notificationListener<typeof StockTransferShipment>({
        topic: 'StockTransferShipment/resendNotificationForFinance',
    })
    static async onResendNotificationForFinance(context: Context, document: { number: number }) {
        const stockTransferShipment = await context.read(StockTransferShipment, { number: document.number });

        await StockTransferShipment.resendNotificationForFinance(context, stockTransferShipment);
    }

    /**
     * Checks if the stockTransferShipmentPickList can be printed
     * @returns true if it can be printed
     * @throws {BusinessRuleError} if cannot be printed
     */
    @decorators.mutation<typeof StockTransferShipment, 'beforePrintStockTransferShipmentPickList'>({
        isPublished: true,
        startsReadOnly: true,
        parameters: [
            { name: 'stockTransferShipment', type: 'reference', isMandatory: true, node: () => StockTransferShipment },
        ],
        return: { type: 'boolean' },
    })
    static async beforePrintStockTransferShipmentPickList(
        context: Context,
        stockTransferShipment: StockTransferShipment,
    ): Promise<boolean> {
        if ((await stockTransferShipment.status) !== 'readyToProcess') {
            throw new BusinessRuleError(
                context.localize(
                    '@sage/xtrem-supply-chain/nodes__stock_transfer_shipment__cant_print_pick_list_when_status_is_not_ready_to_process_bulk',
                    'To print the pick list, the stock transfer shipment needs to be Ready to process: {{number}}.',
                    { number: await stockTransferShipment.number },
                ),
            );
        }
        return true;
    }

    /**
     * Checks if the stockTransferPackingSlip can be printed
     * @returns true if it can be printed
     * @throws {BusinessRuleError} if cannot be printed
     */
    @decorators.mutation<typeof StockTransferShipment, 'beforePrintStockTransferPackingSlip'>({
        isPublished: true,
        startsReadOnly: true,
        parameters: [
            {
                name: 'stockTransferShipment',
                type: 'reference',
                isMandatory: true,
                node: () => StockTransferShipment,
            },
        ],
        return: { type: 'boolean' },
    })
    static async beforePrintStockTransferPackingSlip(
        context: Context,
        stockTransferShipment: StockTransferShipment,
    ): Promise<boolean> {
        if ((await stockTransferShipment.status) === 'readyToProcess') {
            throw new BusinessRuleError(
                context.localize(
                    '@sage/xtrem-supply-chain/nodes__stock_transfer_shipment__cant_print_packing_slip_when_status_is_ready_to_process',
                    'To print the packing slip, the stock transfer shipment cannot be Ready to process: {{number}}.',
                    {
                        number: await stockTransferShipment.number,
                    },
                ),
            );
        }
        return true;
    }

    /**
     * Toggle the isPrinted property to true
     * @param context
     * @param stockTransferShipment
     * @returns boolean
     */
    @decorators.mutation<typeof StockTransferShipment, 'afterPrintStockTransferPackingSlip'>({
        isPublished: true,
        startsReadOnly: true,
        parameters: [
            {
                name: 'stockTransferShipment',
                type: 'reference',
                isMandatory: true,
                node: () => StockTransferShipment,
            },
        ],
        return: { type: 'boolean' },
    })
    static async afterPrintStockTransferPackingSlip(
        readonlyContext: Context,
        stockTransferShipment: StockTransferShipment,
    ): Promise<boolean> {
        if (!(await stockTransferShipment.isPrinted)) {
            await readonlyContext.runInWritableContext(async writableContext => {
                const writableStockTransferShipment = await writableContext.read(
                    StockTransferShipment,
                    { _id: stockTransferShipment._id },
                    { forUpdate: true },
                );
                writableStockTransferShipment.canUpdateIsPrinted = true;
                await writableStockTransferShipment.$.set({ isPrinted: true });
                await writableStockTransferShipment.$.save();
            });
        }
        return true;
    }
}
