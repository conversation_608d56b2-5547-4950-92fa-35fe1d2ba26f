import type { Reference } from '@sage/xtrem-core';
import { decorators } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremSupplyChain from '../../index';

@decorators.subNode<StockTransferReceiptLineToStockTransferShipmentLine>({
    extends: () => xtremMasterData.nodes.BaseLineToLine,
    isPublished: true,
    canRead: true,
    canSearch: true,
})
export class StockTransferReceiptLineToStockTransferShipmentLine extends xtremMasterData.nodes.BaseLineToLine {
    @decorators.referencePropertyOverride<StockTransferReceiptLineToStockTransferShipmentLine, 'to'>({
        node: () => xtremSupplyChain.nodes.StockTransferShipmentLine,
    })
    override readonly to: Reference<xtremSupplyChain.nodes.StockTransferShipmentLine>;

    @decorators.referencePropertyOverride<StockTransferReceiptLineToStockTransferShipmentLine, 'from'>({
        node: () => xtremSupplyChain.nodes.StockTransferReceiptLine,
    })
    override readonly from: Reference<xtremSupplyChain.nodes.StockTransferReceiptLine>;
}
