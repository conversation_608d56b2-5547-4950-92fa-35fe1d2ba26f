import { EnumDataType } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';

export enum StockTransferOrderDisplayStatusEnum {
    draft = xtremMasterData.enums.BaseDisplayStatusEnum.draft,
    pendingApproval = xtremMasterData.enums.BaseDisplayStatusEnum.pendingApproval,
    approved = xtremMasterData.enums.BaseDisplayStatusEnum.approved,
    rejected = xtremMasterData.enums.BaseDisplayStatusEnum.rejected,
    changeRequested = xtremMasterData.enums.BaseDisplayStatusEnum.changeRequested,
    confirmed = xtremMasterData.enums.BaseDisplayStatusEnum.confirmed,
    partiallyShipped = xtremMasterData.enums.BaseDisplayStatusEnum.partiallyShipped,
    shipped = xtremMasterData.enums.BaseDisplayStatusEnum.shipped,
    partiallyReceived = xtremMasterData.enums.BaseDisplayStatusEnum.partiallyReceived,
    received = xtremMasterData.enums.BaseDisplayStatusEnum.received,
    closed = xtremMasterData.enums.BaseDisplayStatusEnum.closed,
    error = xtremMasterData.enums.BaseDisplayStatusEnum.error,
}

export type StockTransferOrderDisplayStatus = keyof typeof StockTransferOrderDisplayStatusEnum;

export const stockTransferOrderDisplayStatusDataType = new EnumDataType<StockTransferOrderDisplayStatus>({
    enum: StockTransferOrderDisplayStatusEnum,
    filename: __filename,
});
