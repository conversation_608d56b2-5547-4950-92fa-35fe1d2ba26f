import { EnumDataType } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';

export enum StockTransferShipmentDisplayStatusEnum {
    readyToProcess = xtremMasterData.enums.BaseDisplayStatusEnum.readyToProcess,
    readyToShip = xtremMasterData.enums.BaseDisplayStatusEnum.readyToShip,
    shipped = xtremMasterData.enums.BaseDisplayStatusEnum.shipped,
    postingInProgress = xtremMasterData.enums.BaseDisplayStatusEnum.postingInProgress,
    error = xtremMasterData.enums.BaseDisplayStatusEnum.error,
    received = xtremMasterData.enums.BaseDisplayStatusEnum.received,
}

export type StockTransferShipmentDisplayStatus = keyof typeof StockTransferShipmentDisplayStatusEnum;

export const stockTransferShipmentDisplayStatusDataType = new EnumDataType<StockTransferShipmentDisplayStatus>({
    enum: StockTransferShipmentDisplayStatusEnum,
    filename: __filename,
});
