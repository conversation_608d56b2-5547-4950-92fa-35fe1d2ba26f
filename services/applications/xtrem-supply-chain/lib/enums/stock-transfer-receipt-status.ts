import { EnumDataType } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';

export enum StockTransferReceiptStatusEnum {
    readyToProcess = xtremMasterData.enums.BaseStatusEnum.readyToProcess,
    received = xtremMasterData.enums.BaseStatusEnum.received,
}

export type StockTransferReceiptStatus = keyof typeof StockTransferReceiptStatusEnum;

export const stockTransferReceiptStatusDataType = new EnumDataType<StockTransferReceiptStatus>({
    enum: StockTransferReceiptStatusEnum,
    filename: __filename,
});
