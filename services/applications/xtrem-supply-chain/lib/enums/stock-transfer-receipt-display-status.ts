import { EnumDataType } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';

export enum StockTransferReceiptDisplayStatusEnum {
    readyToProcess = xtremMasterData.enums.BaseDisplayStatusEnum.readyToProcess,
    received = xtremMasterData.enums.BaseDisplayStatusEnum.received,
    postingInProgress = xtremMasterData.enums.BaseDisplayStatusEnum.postingInProgress,
    error = xtremMasterData.enums.BaseDisplayStatusEnum.error,
}

export type StockTransferReceiptDisplayStatus = keyof typeof StockTransferReceiptDisplayStatusEnum;

export const StockTransferReceiptDisplayStatusDataType = new EnumDataType<StockTransferReceiptDisplayStatus>({
    enum: StockTransferReceiptDisplayStatusEnum,
    filename: __filename,
});
