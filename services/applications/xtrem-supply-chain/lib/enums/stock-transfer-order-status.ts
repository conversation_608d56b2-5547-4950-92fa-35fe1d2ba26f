import { EnumDataType } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';

export enum StockTransferOrderStatusEnum {
    draft = xtremMasterData.enums.BaseStatusEnum.draft,
    pending = xtremMasterData.enums.BaseStatusEnum.pending,
    inProgress = xtremMasterData.enums.BaseStatusEnum.inProgress,
    closed = xtremMasterData.enums.BaseStatusEnum.closed,
}

export type StockTransferOrderStatus = keyof typeof StockTransferOrderStatusEnum;

export const stockTransferOrderStatusDataType = new EnumDataType<StockTransferOrderStatus>({
    enum: StockTransferOrderStatusEnum,
    filename: __filename,
});
