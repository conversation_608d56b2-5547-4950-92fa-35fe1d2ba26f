import { EnumDataType } from '@sage/xtrem-core';

export enum StockTransferOrderCreateShipmentMethodReturnEnum {
    parametersAreIncorrect = 1,
    isNotShipped = 2,
    isPartiallyShipped = 3,
    isShipped = 4,
}

export type StockTransferOrderCreateShipmentMethodReturn =
    keyof typeof StockTransferOrderCreateShipmentMethodReturnEnum;

export const stockTransferOrderCreateShipmentMethodReturnDataType =
    new EnumDataType<StockTransferOrderCreateShipmentMethodReturn>({
        enum: StockTransferOrderCreateShipmentMethodReturnEnum,
        filename: __filename,
    });
