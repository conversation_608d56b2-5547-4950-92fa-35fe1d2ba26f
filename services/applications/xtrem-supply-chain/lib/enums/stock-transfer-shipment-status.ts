import { EnumDataType } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';

export enum StockTransferShipmentStatusEnum {
    readyToProcess = xtremMasterData.enums.BaseStatusEnum.readyToProcess,
    readyToShip = xtremMasterData.enums.BaseStatusEnum.readyToShip,
    shipped = xtremMasterData.enums.BaseStatusEnum.shipped,
    received = xtremMasterData.enums.BaseStatusEnum.received,
}

export type StockTransferShipmentStatus = keyof typeof StockTransferShipmentStatusEnum;

export const stockTransferShipmentStatusDataType = new EnumDataType<StockTransferShipmentStatus>({
    enum: StockTransferShipmentStatusEnum,
    filename: __filename,
});
