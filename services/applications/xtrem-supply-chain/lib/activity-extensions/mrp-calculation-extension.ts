import { ActivityExtension } from '@sage/xtrem-core';
import * as xtremMrpData from '@sage/xtrem-mrp-data';
import * as xtremSupplyChain from '..';

export const mrpCalculationExtension = new ActivityExtension({
    extends: xtremMrpData.activities.mrpCalculation,
    __filename,
    permissions: [],
    operationGrants: {
        manage: [
            {
                operations: ['checkSupplyPlanningPurchaseOrderCreated'],
                on: [() => xtremSupplyChain.nodes.SupplyPlanning],
            },
        ],
    },
});
