import type { date } from '@sage/xtrem-core';
import type * as xtremManufacturing from '@sage/xtrem-manufacturing';
import type * as xtremMasterData from '@sage/xtrem-master-data';
import type { StoredAttributes } from '@sage/xtrem-master-data/lib/interfaces/dimensions';
import type * as xtremPurchasing from '@sage/xtrem-purchasing';
import type * as xtremSales from '@sage/xtrem-sales';
import type * as xtremStockData from '@sage/xtrem-stock-data';
import type * as xtremSystem from '@sage/xtrem-system';
import type * as xtremSupplyChain from '../../index';

export interface CreatedOrder {
    _id: number;
    number: string;
}

export interface CreatedOrders {
    type: xtremSupplyChain.enums.SupplyPlanningType;
    orders: CreatedOrder[];
}

export interface BackToBackPurchaseOrderCreationParameters {
    orderDate: date;
    expectedReceiptDate: date;
    quantity: number;
    grossPrice: number;
    unit: xtremMasterData.nodes.UnitOfMeasure;
    site: xtremSystem.nodes.Site;
    stockSite: xtremSystem.nodes.Site;
    item: xtremMasterData.nodes.Item;
    supplier: xtremMasterData.nodes.Supplier;
    priceOrigin: xtremPurchasing.enums.PriceOrigin;
    storedDimensions: object;
    storedAttributes: StoredAttributes;
    currency: xtremMasterData.nodes.Currency;
    remainingQuantityToShipInStockUnit: number;
    salesOrderLineSysId: number;
    salesOrderLineWorkInProgress: xtremSales.nodes.WorkInProgressSalesOrderLine | null;
}

export interface BackToBackWorkOrderCreationParameters {
    site: xtremSystem.nodes.Site;
    item: xtremMasterData.nodes.Item;
    releasedQuantity: number;
    name: string;
    type: xtremManufacturing.enums.WorkOrderType;
    workOrderCategory: xtremManufacturing.nodes.WorkOrderCategory;
    startDate?: date;
    billOfMaterial?: xtremMasterData.nodes.Item;
    routing?: xtremMasterData.nodes.Item;
    storedDimensions?: object;
    storedAttributes?: xtremMasterData.interfaces.StoredAttributes;
    remainingQuantityToShipInStockUnit: number;
    salesOrderLineSysId: number;
    salesOrderLineWorkInProgress: xtremSales.nodes.WorkInProgressSalesOrderLine | null;
    workOrderNumber?: '';
}

export interface AssignmentCreationParameters {
    stockSite: xtremSystem.nodes.Site;
    item: xtremMasterData.nodes.Item;
    quantityInStockUnit: number;
    demandType: xtremStockData.enums.OrderToOrderDemandType;
    demandDocumentLine: number;
    demandWorkInProgress: xtremSales.nodes.WorkInProgressSalesOrderLine | null;
    supplyType: xtremStockData.enums.OrderToOrderSupplyType;
    supplyDocumentLine: number;
    supplyWorkInProgress:
        | xtremPurchasing.nodes.WorkInProgressPurchaseOrderLine
        | xtremManufacturing.nodes.WorkInProgressWorkOrderReleasedItem
        | null;
}
