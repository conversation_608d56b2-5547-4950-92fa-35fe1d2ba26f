{"extends": "../../tsconfig-base.json", "compilerOptions": {"outDir": "build", "rootDir": ".", "baseUrl": ".", "types": ["@types/mocha", "@types/chai-as-promised"], "composite": true}, "include": ["index.ts", "application.ts", "lib/**/*", "test/**/*.ts", "test/**/*.json", "api/api.d.ts", "upgrades/**/*"], "exclude": ["lib/pages/**/*", "lib/widgets/**/*", "lib/page-extensions/**/*", "lib/page-fragments/**/*", "lib/stickers/**/*", "lib/i18n/**/*", "**/*.feature", "**/*.png", "lib/client-functions/**/*", "upgrade-disabled"], "references": [{"path": "../../../platform/shared/xtrem-async-helper"}, {"path": "../../../platform/system/xtrem-authorization"}, {"path": "../../../platform/front-end/xtrem-client"}, {"path": "../../../platform/system/xtrem-communication"}, {"path": "../../../platform/back-end/xtrem-core"}, {"path": "../../../platform/system/xtrem-dashboard"}, {"path": "../../../platform/shared/xtrem-date-time"}, {"path": "../../../platform/shared/xtrem-decimal"}, {"path": "../xtrem-distribution"}, {"path": "../../shared/xtrem-finance-data"}, {"path": "../../shared/xtrem-landed-cost"}, {"path": "../../../platform/system/xtrem-mailer"}, {"path": "../../shared/xtrem-master-data"}, {"path": "../../../platform/system/xtrem-reporting"}, {"path": "../../../platform/system/xtrem-scheduler"}, {"path": "../../../platform/back-end/xtrem-service"}, {"path": "../../../platform/shared/xtrem-shared"}, {"path": "../../shared/xtrem-stock-data"}, {"path": "../../shared/xtrem-structure"}, {"path": "../../../platform/system/xtrem-system"}, {"path": "../../shared/xtrem-tax"}, {"path": "../../../platform/front-end/xtrem-ui"}, {"path": "../../../platform/system/xtrem-upload"}, {"path": "../../../platform/system/xtrem-workflow"}, {"path": "../../../platform/back-end/eslint-plugin-xtrem"}, {"path": "../../../platform/cli/xtrem-cli"}, {"path": "../xtrem-distribution/api"}, {"path": "../../shared/xtrem-finance-data/api"}, {"path": "../../../platform/system/xtrem-mailer/api"}, {"path": "../../shared/xtrem-master-data/api"}, {"path": "../../../platform/system/xtrem-reporting/api"}, {"path": "../../../platform/system/xtrem-routing"}, {"path": "api"}, {"path": "../../../platform/system/xtrem-scheduler/api"}, {"path": "../../shared/xtrem-stock-data/api"}, {"path": "../../shared/xtrem-structure/api"}, {"path": "../../../platform/system/xtrem-system/api"}, {"path": "../../shared/xtrem-tax/api"}]}