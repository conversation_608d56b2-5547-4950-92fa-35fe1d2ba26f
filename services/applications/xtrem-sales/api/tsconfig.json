{"extends": "../../../tsconfig-base.json", "compilerOptions": {"outDir": "build", "rootDir": ".", "baseUrl": ".", "composite": true}, "include": ["api.d.ts"], "references": [{"path": "../../../../platform/system/xtrem-authorization/api"}, {"path": "../../../../platform/front-end/xtrem-client"}, {"path": "../../../../platform/system/xtrem-communication/api"}, {"path": "../../../../platform/system/xtrem-dashboard/api"}, {"path": "../../xtrem-distribution/api"}, {"path": "../../../shared/xtrem-finance-data/api"}, {"path": "../../../shared/xtrem-landed-cost/api"}, {"path": "../../../../platform/system/xtrem-mailer/api"}, {"path": "../../../shared/xtrem-master-data/api"}, {"path": "../../../../platform/system/xtrem-reporting/api"}, {"path": "../../../../platform/system/xtrem-scheduler/api"}, {"path": "../../../shared/xtrem-stock-data/api"}, {"path": "../../../shared/xtrem-structure/api"}, {"path": "../../../../platform/system/xtrem-system/api"}, {"path": "../../../shared/xtrem-tax/api"}]}