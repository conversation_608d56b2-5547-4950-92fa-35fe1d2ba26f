declare module '@sage/xtrem-sales-api-partial' {
    import type { Package as SageXtremAuditing$Package } from '@sage/xtrem-auditing-api';
    import type {
        GroupRoleSite,
        Package as SageXtremAuthorization$Package,
        SiteGroup,
    } from '@sage/xtrem-authorization-api';
    import type { Package as SageXtremCommunication$Package } from '@sage/xtrem-communication-api';
    import type { Package as SageXtremCustomization$Package } from '@sage/xtrem-customization-api';
    import type { Package as SageXtremDashboard$Package } from '@sage/xtrem-dashboard-api';
    import type { Package as SageXtremDistribution$Package } from '@sage/xtrem-distribution-api';
    import type {
        Account,
        AnalyticalData,
        BaseOpenItem,
        CloseReason,
        FinanceTransaction,
        Package as SageXtremFinanceData$Package,
        PaymentDocumentLine,
    } from '@sage/xtrem-finance-data-api';
    import type { Package as SageXtremImportExport$Package } from '@sage/xtrem-import-export-api';
    import type { Package as SageXtremLandedCost$Package } from '@sage/xtrem-landed-cost-api';
    import type { Package as SageXtremMailer$Package } from '@sage/xtrem-mailer-api';
    import type {
        Address,
        BaseBusinessRelation,
        BaseDocumentLine,
        BusinessEntity,
        BusinessEntityAddress,
        BusinessEntityInput,
        Contact,
        CostCategory,
        Currency,
        Customer,
        CustomerPriceReason,
        DeliveryMode,
        Incoterm,
        IndirectCostSection,
        Item,
        ItemSite,
        ItemSiteCost,
        ItemSiteSupplier,
        Location,
        Package as SageXtremMasterData$Package,
        PaymentTerm,
        Supplier,
        UnitOfMeasure,
    } from '@sage/xtrem-master-data-api';
    import type { MetaNodeFactory, Package as SageXtremMetadata$Package } from '@sage/xtrem-metadata-api';
    import type { Package as SageXtremReporting$Package } from '@sage/xtrem-reporting-api';
    import type { Package as SageXtremRouting$Package } from '@sage/xtrem-routing-api';
    import type { Package as SageXtremScheduler$Package } from '@sage/xtrem-scheduler-api';
    import type {
        FifoValuationTier,
        Lot,
        OrderAssignment,
        Package as SageXtremStockData$Package,
        Stock,
        StockAllocation,
        StockIssueDetail,
        StockIssueDetailBinding,
        StockIssueDetailInput,
        StockJournal,
        StockReceiptDetail,
        StockReceiptDetailBinding,
        StockReceiptDetailInput,
        StockStatus,
        StockTransaction,
        StockTransactionBinding,
        StockTransactionInput,
    } from '@sage/xtrem-stock-data-api';
    import type { Country, Package as SageXtremStructure$Package } from '@sage/xtrem-structure-api';
    import type { Package as SageXtremSynchronization$Package } from '@sage/xtrem-synchronization-api';
    import type {
        Company,
        Package as SageXtremSystem$Package,
        Site,
        SiteInput,
        SysVendor,
        User,
        UserInput,
    } from '@sage/xtrem-system-api';
    import type { ItemTaxGroup, Package as SageXtremTax$Package, Tax, TaxCategory, TaxZone } from '@sage/xtrem-tax-api';
    import type {
        AttachmentAssociation,
        AttachmentAssociationInput,
        Package as SageXtremUpload$Package,
        UploadedFile,
    } from '@sage/xtrem-upload-api';
    import type { Package as SageXtremWorkflow$Package } from '@sage/xtrem-workflow-api';
    import type {
        AggregateQueryOperation,
        AggregateReadOperation,
        AsyncOperation,
        ClientCollection,
        ClientNode,
        ClientNodeInput,
        CreateOperation,
        DeleteOperation,
        DuplicateOperation,
        GetDefaultsOperation,
        GetDuplicateOperation,
        Operation as Node$Operation,
        QueryOperation,
        ReadOperation,
        TextStream,
        UpdateByIdOperation,
        UpdateOperation,
        VitalClientNode,
        VitalClientNodeInput,
        decimal,
        integer,
    } from '@sage/xtrem-client';
    export interface SalesCreditMemoDisplayStatus$Enum {
        draft: 0;
        postingInProgress: 1;
        error: 2;
        posted: 3;
        taxCalculationFailed: 4;
        partiallyPaid: 5;
        paid: 6;
    }
    export type SalesCreditMemoDisplayStatus = keyof SalesCreditMemoDisplayStatus$Enum;
    export interface SalesCreditMemoStatus$Enum {
        draft: 1;
        posted: 2;
        inProgress: 3;
        error: 4;
    }
    export type SalesCreditMemoStatus = keyof SalesCreditMemoStatus$Enum;
    export interface SalesDocumentCreditStatus$Enum {
        notCredited: 1;
        partiallyCredited: 2;
        credited: 3;
    }
    export type SalesDocumentCreditStatus = keyof SalesDocumentCreditStatus$Enum;
    export interface SalesDocumentInvoiceStatus$Enum {
        notInvoiced: 1;
        partiallyInvoiced: 2;
        invoiced: 3;
    }
    export type SalesDocumentInvoiceStatus = keyof SalesDocumentInvoiceStatus$Enum;
    export interface SalesDocumentReceiptStatus$Enum {
        notReturned: 1;
        partiallyReturned: 2;
        returned: 3;
    }
    export type SalesDocumentReceiptStatus = keyof SalesDocumentReceiptStatus$Enum;
    export interface SalesDocumentReturnRequestReceiptStatus$Enum {
        notReceived: 1;
        partiallyReceived: 2;
        received: 3;
    }
    export type SalesDocumentReturnRequestReceiptStatus = keyof SalesDocumentReturnRequestReceiptStatus$Enum;
    export interface SalesDocumentReturnStatus$Enum {
        noReturnRequested: 1;
        returnPartiallyRequested: 2;
        returnRequested: 3;
    }
    export type SalesDocumentReturnStatus = keyof SalesDocumentReturnStatus$Enum;
    export interface SalesDocumentShippingStatus$Enum {
        notShipped: 1;
        partiallyShipped: 2;
        shipped: 3;
    }
    export type SalesDocumentShippingStatus = keyof SalesDocumentShippingStatus$Enum;
    export interface SalesDocumentType$Enum {
        salesOrder: 1;
        salesShipment: 2;
        salesInvoice: 3;
        salesReturnRequest: 4;
        salesCreditMemo: 5;
    }
    export type SalesDocumentType = keyof SalesDocumentType$Enum;
    export interface SalesInvoiceDisplayStatus$Enum {
        draft: 0;
        postingInProgress: 1;
        posted: 2;
        partiallyCredited: 3;
        credited: 4;
        taxCalculationFailed: 5;
        error: 6;
        partiallyPaid: 7;
        paid: 8;
    }
    export type SalesInvoiceDisplayStatus = keyof SalesInvoiceDisplayStatus$Enum;
    export interface SalesInvoiceStatus$Enum {
        draft: 1;
        posted: 2;
        inProgress: 3;
        error: 4;
    }
    export type SalesInvoiceStatus = keyof SalesInvoiceStatus$Enum;
    export interface SalesOrderConfirmMethodReturn$Enum {
        parametersAreIncorrect: 1;
        salesOrderStatusIsNotDraft: 2;
        salesOrderIsConfirmed: 3;
    }
    export type SalesOrderConfirmMethodReturn = keyof SalesOrderConfirmMethodReturn$Enum;
    export interface SalesOrderCreateSalesShipmentMethodReturn$Enum {
        parametersAreIncorrect: 1;
        salesOrderIsNotShipped: 2;
        salesOrderIsPartiallyShipped: 3;
        salesOrderIsShipped: 4;
    }
    export type SalesOrderCreateSalesShipmentMethodReturn = keyof SalesOrderCreateSalesShipmentMethodReturn$Enum;
    export interface SalesOrderDisplayStatus$Enum {
        quote: 0;
        partiallyShipped: 1;
        confirmed: 2;
        shipped: 3;
        closed: 4;
        taxCalculationFailed: 5;
    }
    export type SalesOrderDisplayStatus = keyof SalesOrderDisplayStatus$Enum;
    export interface SalesOrderLineCloseStatusMethodReturn$Enum {
        parametersAreIncorrect: 1;
        salesOrderLineIsAlreadyClosed: 2;
        salesOrderLineIsNowClosed: 3;
        salesOrderHeaderIsNowClosed: 4;
    }
    export type SalesOrderLineCloseStatusMethodReturn = keyof SalesOrderLineCloseStatusMethodReturn$Enum;
    export interface SalesOrderLineOpenStatusMethodReturn$Enum {
        parametersAreIncorrect: 1;
        salesOrderLineIsAlreadyOpen: 2;
        salesOrderLineIsShipped: 3;
        salesOrderLineIsNowOpen: 4;
    }
    export type SalesOrderLineOpenStatusMethodReturn = keyof SalesOrderLineOpenStatusMethodReturn$Enum;
    export interface SalesOrderOpenStatusMethodReturn$Enum {
        parametersAreIncorrect: 1;
        salesOrderIsShipped: 2;
        salesOrderIsAlreadyOpen: 3;
        salesOrderIsNowOpen: 4;
    }
    export type SalesOrderOpenStatusMethodReturn = keyof SalesOrderOpenStatusMethodReturn$Enum;
    export interface SalesOrderStatus$Enum {
        quote: 1;
        pending: 2;
        inProgress: 3;
        closed: 4;
    }
    export type SalesOrderStatus = keyof SalesOrderStatus$Enum;
    export interface SalesOriginDocumentType$Enum {
        direct: 1;
        shipment: 2;
        order: 3;
        invoice: 4;
        return: 5;
    }
    export type SalesOriginDocumentType = keyof SalesOriginDocumentType$Enum;
    export interface SalesPriceOrigin$Enum {
        manual: 1;
        priceList: 2;
        basePrice: 3;
    }
    export type SalesPriceOrigin = keyof SalesPriceOrigin$Enum;
    export interface SalesReturnReceiptDisplayStatus$Enum {
        draft: 1;
        postingInProgress: 2;
        error: 3;
        closed: 4;
    }
    export type SalesReturnReceiptDisplayStatus = keyof SalesReturnReceiptDisplayStatus$Enum;
    export interface SalesReturnReceiptStatus$Enum {
        draft: 1;
        inProgress: 2;
        closed: 3;
    }
    export type SalesReturnReceiptStatus = keyof SalesReturnReceiptStatus$Enum;
    export interface SalesReturnRequestApprovalStatus$Enum {
        draft: 1;
        pendingApproval: 2;
        approved: 3;
        confirmed: 4;
        rejected: 5;
        changeRequested: 6;
    }
    export type SalesReturnRequestApprovalStatus = keyof SalesReturnRequestApprovalStatus$Enum;
    export interface SalesReturnRequestCloseStatusMethodReturn$Enum {
        parametersAreIncorrect: 1;
        salesReturnRequestIsAlreadyClosed: 2;
        salesReturnRequestIsNowClosed: 3;
        linkedSalesReturnReceiptIsNotClosed: 4;
    }
    export type SalesReturnRequestCloseStatusMethodReturn = keyof SalesReturnRequestCloseStatusMethodReturn$Enum;
    export interface SalesReturnRequestDisplayStatus$Enum {
        draft: 1;
        pendingApproval: 2;
        approved: 3;
        confirmed: 4;
        rejected: 5;
        partiallyReceived: 6;
        received: 7;
        closed: 8;
    }
    export type SalesReturnRequestDisplayStatus = keyof SalesReturnRequestDisplayStatus$Enum;
    export interface SalesReturnRequestLineCloseStatusMethodReturn$Enum {
        parametersAreIncorrect: 1;
        salesReturnRequestLineIsAlreadyClosed: 2;
        salesReturnRequestLineIsNowClosed: 3;
        salesReturnRequestHeaderIsNowClosed: 4;
        linkedSalesReturnReceiptIsNotClosed: 5;
    }
    export type SalesReturnRequestLineCloseStatusMethodReturn =
        keyof SalesReturnRequestLineCloseStatusMethodReturn$Enum;
    export interface SalesReturnRequestLineOpenStatusMethodReturn$Enum {
        parametersAreIncorrect: 1;
        salesReturnRequestLineIsAlreadyOpen: 2;
        salesReturnRequestLineIsReceived: 3;
        salesReturnRequestLineIsNowOpen: 4;
    }
    export type SalesReturnRequestLineOpenStatusMethodReturn = keyof SalesReturnRequestLineOpenStatusMethodReturn$Enum;
    export interface SalesReturnRequestOpenStatusMethodReturn$Enum {
        parametersAreIncorrect: 1;
        salesReturnRequestIsReceived: 2;
        salesReturnRequestIsAlreadyOpen: 3;
        salesReturnRequestIsNowOpen: 4;
    }
    export type SalesReturnRequestOpenStatusMethodReturn = keyof SalesReturnRequestOpenStatusMethodReturn$Enum;
    export interface SalesReturnRequestReturnType$Enum {
        receiptAndCreditMemo: 1;
        creditMemo: 2;
        receiptAndNoCreditMemo: 3;
    }
    export type SalesReturnRequestReturnType = keyof SalesReturnRequestReturnType$Enum;
    export interface SalesReturnRequestStatus$Enum {
        draft: 1;
        pending: 2;
        inProgress: 3;
        confirmed: 4;
        closed: 5;
    }
    export type SalesReturnRequestStatus = keyof SalesReturnRequestStatus$Enum;
    export interface SalesShipmentDisplayStatus$Enum {
        readyToProcess: 1;
        readyToShip: 2;
        shipped: 3;
        partiallyInvoiced: 4;
        invoiced: 5;
        postingInProgress: 6;
        error: 7;
    }
    export type SalesShipmentDisplayStatus = keyof SalesShipmentDisplayStatus$Enum;
    export interface SalesShipmentStatus$Enum {
        readyToProcess: 1;
        readyToShip: 2;
        shipped: 3;
    }
    export type SalesShipmentStatus = keyof SalesShipmentStatus$Enum;
    export interface UnbilledAccountReceivableStatus$Enum {
        inProgress: 0;
        completed: 1;
        draft: 2;
        error: 3;
    }
    export type UnbilledAccountReceivableStatus = keyof UnbilledAccountReceivableStatus$Enum;
    export interface BaseLineToSalesDocumentLine extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        _constructor: string;
        quantityInStockUnit: string;
        quantity: string;
        quantityInSalesUnit: string;
        amount: string;
    }
    export interface BaseLineToSalesDocumentLineInput extends ClientNodeInput {
        _constructor?: string;
        quantityInStockUnit?: decimal | string;
        quantity?: decimal | string;
        amount?: decimal | string;
    }
    export interface BaseLineToSalesDocumentLineBinding extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        _constructor: string;
        quantityInStockUnit: string;
        quantity: string;
        quantityInSalesUnit: string;
        amount: string;
    }
    export interface BaseLineToSalesDocumentLine$Operations {}
    export interface ProformaInvoice extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        salesOrder: SalesOrder;
        version: integer;
        expirationDate: string;
        issueDate: string;
        createdBy: string;
        isActive: boolean;
        isSent: boolean;
        isExpired: boolean;
        customerComment: TextStream;
        uploadedFile: UploadedFile;
        isLinkExpired: boolean;
    }
    export interface ProformaInvoiceInput extends ClientNodeInput {
        salesOrder?: integer | string;
        version?: integer | string;
        expirationDate?: string;
        isActive?: boolean | string;
        isSent?: boolean | string;
        customerComment?: TextStream;
        uploadedFile?: integer | string;
    }
    export interface ProformaInvoiceBinding extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        salesOrder: SalesOrder;
        version: integer;
        expirationDate: string;
        issueDate: string;
        createdBy: string;
        isActive: boolean;
        isSent: boolean;
        isExpired: boolean;
        customerComment: TextStream;
        uploadedFile: UploadedFile;
        isLinkExpired: boolean;
    }
    export interface ProformaInvoice$Mutations {
        beforePrintProformaInvoice: Node$Operation<
            {
                salesOrder: string;
            },
            boolean
        >;
        afterPrintProformaInvoice: Node$Operation<
            {
                salesOrder: string;
                uploadedFile: string;
                error?: {
                    message: string;
                } | null;
            },
            boolean
        >;
    }
    export interface ProformaInvoice$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface ProformaInvoice$Lookups {
        salesOrder: QueryOperation<SalesOrder>;
        uploadedFile: QueryOperation<UploadedFile>;
    }
    export interface ProformaInvoice$Operations {
        query: QueryOperation<ProformaInvoice>;
        read: ReadOperation<ProformaInvoice>;
        aggregate: {
            read: AggregateReadOperation<ProformaInvoice>;
            query: AggregateQueryOperation<ProformaInvoice>;
        };
        create: CreateOperation<ProformaInvoiceInput, ProformaInvoice>;
        getDuplicate: GetDuplicateOperation<ProformaInvoice>;
        update: UpdateOperation<ProformaInvoiceInput, ProformaInvoice>;
        updateById: UpdateByIdOperation<ProformaInvoiceInput, ProformaInvoice>;
        mutations: ProformaInvoice$Mutations;
        asyncOperations: ProformaInvoice$AsyncOperations;
        lookups(dataOrId: string | { data: ProformaInvoiceInput }): ProformaInvoice$Lookups;
        getDefaults: GetDefaultsOperation<ProformaInvoice>;
    }
    export interface SalesCreditMemoReason extends ClientNode {
        _factory: MetaNodeFactory;
        _vendor: SysVendor;
        _updateUser: User;
        _createUser: User;
        isActive: boolean;
        id: string;
        name: string;
        description: string;
    }
    export interface SalesCreditMemoReasonInput extends ClientNodeInput {
        _vendor?: integer | string;
        isActive?: boolean | string;
        id?: string;
        name?: string;
        description?: string;
    }
    export interface SalesCreditMemoReasonBinding extends ClientNode {
        _factory: MetaNodeFactory;
        _vendor: SysVendor;
        _updateUser: User;
        _createUser: User;
        isActive: boolean;
        id: string;
        name: string;
        description: string;
    }
    export interface SalesCreditMemoReason$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface SalesCreditMemoReason$Lookups {
        _vendor: QueryOperation<SysVendor>;
    }
    export interface SalesCreditMemoReason$Operations {
        query: QueryOperation<SalesCreditMemoReason>;
        read: ReadOperation<SalesCreditMemoReason>;
        aggregate: {
            read: AggregateReadOperation<SalesCreditMemoReason>;
            query: AggregateQueryOperation<SalesCreditMemoReason>;
        };
        create: CreateOperation<SalesCreditMemoReasonInput, SalesCreditMemoReason>;
        getDuplicate: GetDuplicateOperation<SalesCreditMemoReason>;
        duplicate: DuplicateOperation<string, SalesCreditMemoReasonInput, SalesCreditMemoReason>;
        update: UpdateOperation<SalesCreditMemoReasonInput, SalesCreditMemoReason>;
        updateById: UpdateByIdOperation<SalesCreditMemoReasonInput, SalesCreditMemoReason>;
        delete: DeleteOperation<{}>;
        deleteById: DeleteOperation<string>;
        asyncOperations: SalesCreditMemoReason$AsyncOperations;
        lookups(dataOrId: string | { data: SalesCreditMemoReasonInput }): SalesCreditMemoReason$Lookups;
        getDefaults: GetDefaultsOperation<SalesCreditMemoReason>;
    }
    export interface SalesReturnRequestReason extends ClientNode {
        _factory: MetaNodeFactory;
        _vendor: SysVendor;
        _updateUser: User;
        _createUser: User;
        isActive: boolean;
        id: string;
        name: string;
        description: string;
    }
    export interface SalesReturnRequestReasonInput extends ClientNodeInput {
        _vendor?: integer | string;
        isActive?: boolean | string;
        id?: string;
        name?: string;
        description?: string;
    }
    export interface SalesReturnRequestReasonBinding extends ClientNode {
        _factory: MetaNodeFactory;
        _vendor: SysVendor;
        _updateUser: User;
        _createUser: User;
        isActive: boolean;
        id: string;
        name: string;
        description: string;
    }
    export interface SalesReturnRequestReason$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface SalesReturnRequestReason$Lookups {
        _vendor: QueryOperation<SysVendor>;
    }
    export interface SalesReturnRequestReason$Operations {
        query: QueryOperation<SalesReturnRequestReason>;
        read: ReadOperation<SalesReturnRequestReason>;
        aggregate: {
            read: AggregateReadOperation<SalesReturnRequestReason>;
            query: AggregateQueryOperation<SalesReturnRequestReason>;
        };
        create: CreateOperation<SalesReturnRequestReasonInput, SalesReturnRequestReason>;
        getDuplicate: GetDuplicateOperation<SalesReturnRequestReason>;
        duplicate: DuplicateOperation<string, SalesReturnRequestReasonInput, SalesReturnRequestReason>;
        update: UpdateOperation<SalesReturnRequestReasonInput, SalesReturnRequestReason>;
        updateById: UpdateByIdOperation<SalesReturnRequestReasonInput, SalesReturnRequestReason>;
        delete: DeleteOperation<{}>;
        deleteById: DeleteOperation<string>;
        asyncOperations: SalesReturnRequestReason$AsyncOperations;
        lookups(dataOrId: string | { data: SalesReturnRequestReasonInput }): SalesReturnRequestReason$Lookups;
        getDefaults: GetDefaultsOperation<SalesReturnRequestReason>;
    }
    export interface UnbilledAccountReceivableInputSet extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        user: User;
        company: Company;
        sites: Site[];
        fromCustomer: Customer;
        toCustomer: Customer;
        asOfDate: string;
        status: UnbilledAccountReceivableStatus;
        executionDate: string;
        lines: ClientCollection<UnbilledAccountReceivableResultLine>;
    }
    export interface UnbilledAccountReceivableInputSetInput extends ClientNodeInput {
        user?: integer | string;
        company?: integer | string;
        sites?: (integer | string)[];
        fromCustomer?: integer | string;
        toCustomer?: integer | string;
        asOfDate?: string;
        status?: UnbilledAccountReceivableStatus;
        executionDate?: string;
        lines?: Partial<UnbilledAccountReceivableResultLineInput>[];
    }
    export interface UnbilledAccountReceivableInputSetBinding extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        user: User;
        company: Company;
        sites: Site[];
        fromCustomer: Customer;
        toCustomer: Customer;
        asOfDate: string;
        status: UnbilledAccountReceivableStatus;
        executionDate: string;
        lines: ClientCollection<UnbilledAccountReceivableResultLineBinding>;
    }
    export interface UnbilledAccountReceivableInputSet$AsyncOperations {
        unbilledAccountReceivableInquiry: AsyncOperation<
            {
                userId?: string;
            },
            boolean
        >;
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface UnbilledAccountReceivableInputSet$Lookups {
        user: QueryOperation<User>;
        company: QueryOperation<Company>;
        fromCustomer: QueryOperation<Customer>;
        toCustomer: QueryOperation<Customer>;
    }
    export interface UnbilledAccountReceivableInputSet$Operations {
        query: QueryOperation<UnbilledAccountReceivableInputSet>;
        read: ReadOperation<UnbilledAccountReceivableInputSet>;
        aggregate: {
            read: AggregateReadOperation<UnbilledAccountReceivableInputSet>;
            query: AggregateQueryOperation<UnbilledAccountReceivableInputSet>;
        };
        create: CreateOperation<UnbilledAccountReceivableInputSetInput, UnbilledAccountReceivableInputSet>;
        getDuplicate: GetDuplicateOperation<UnbilledAccountReceivableInputSet>;
        update: UpdateOperation<UnbilledAccountReceivableInputSetInput, UnbilledAccountReceivableInputSet>;
        updateById: UpdateByIdOperation<UnbilledAccountReceivableInputSetInput, UnbilledAccountReceivableInputSet>;
        delete: DeleteOperation<{}>;
        deleteById: DeleteOperation<string>;
        asyncOperations: UnbilledAccountReceivableInputSet$AsyncOperations;
        lookups(
            dataOrId: string | { data: UnbilledAccountReceivableInputSetInput },
        ): UnbilledAccountReceivableInputSet$Lookups;
        getDefaults: GetDefaultsOperation<UnbilledAccountReceivableInputSet>;
    }
    export interface UnbilledAccountReceivableResultLine extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        inputSet: UnbilledAccountReceivableInputSet;
        customer: Customer;
        currency: Currency;
        financialSite: Site;
        salesUnit: UnitOfMeasure;
        netPrice: string;
        item: Item;
        site: Site;
        quantity: string;
        account: Account;
        accountItem: Account;
        invoicedQuantity: string;
        creditedQuantity: string;
        returnedQuantity: string;
        invoiceIssuableQuantity: string;
        invoiceIssuableAmount: string;
        company: Company;
        shipmentNumber: string;
        shipmentInternalId: SalesShipment;
        documentDate: string;
        shipToCustomer: Customer;
        invoiceIssuableAmountInCompanyCurrency: string;
        invoiceIssuableAmountInCompanyCurrencyAtAsOfDate: string;
        companyCurrency: Currency;
    }
    export interface UnbilledAccountReceivableResultLineInput extends VitalClientNodeInput {
        customer?: integer | string;
        currency?: integer | string;
        financialSite?: integer | string;
        salesUnit?: integer | string;
        netPrice?: decimal | string;
        item?: integer | string;
        site?: integer | string;
        quantity?: decimal | string;
        account?: integer | string;
        accountItem?: integer | string;
        invoicedQuantity?: decimal | string;
        creditedQuantity?: decimal | string;
        returnedQuantity?: decimal | string;
        invoiceIssuableQuantity?: decimal | string;
        invoiceIssuableAmount?: decimal | string;
        shipmentNumber?: string;
        shipmentInternalId?: integer | string;
        documentDate?: string;
        shipToCustomer?: integer | string;
        invoiceIssuableAmountInCompanyCurrency?: decimal | string;
        invoiceIssuableAmountInCompanyCurrencyAtAsOfDate?: decimal | string;
        companyCurrency?: integer | string;
    }
    export interface UnbilledAccountReceivableResultLineBinding extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        inputSet: UnbilledAccountReceivableInputSet;
        customer: Customer;
        currency: Currency;
        financialSite: Site;
        salesUnit: UnitOfMeasure;
        netPrice: string;
        item: Item;
        site: Site;
        quantity: string;
        account: Account;
        accountItem: Account;
        invoicedQuantity: string;
        creditedQuantity: string;
        returnedQuantity: string;
        invoiceIssuableQuantity: string;
        invoiceIssuableAmount: string;
        company: Company;
        shipmentNumber: string;
        shipmentInternalId: SalesShipment;
        documentDate: string;
        shipToCustomer: Customer;
        invoiceIssuableAmountInCompanyCurrency: string;
        invoiceIssuableAmountInCompanyCurrencyAtAsOfDate: string;
        companyCurrency: Currency;
    }
    export interface UnbilledAccountReceivableResultLine$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface UnbilledAccountReceivableResultLine$Lookups {
        customer: QueryOperation<Customer>;
        currency: QueryOperation<Currency>;
        financialSite: QueryOperation<Site>;
        salesUnit: QueryOperation<UnitOfMeasure>;
        item: QueryOperation<Item>;
        site: QueryOperation<Site>;
        account: QueryOperation<Account>;
        accountItem: QueryOperation<Account>;
        company: QueryOperation<Company>;
        shipmentInternalId: QueryOperation<SalesShipment>;
        shipToCustomer: QueryOperation<Customer>;
        companyCurrency: QueryOperation<Currency>;
    }
    export interface UnbilledAccountReceivableResultLine$Operations {
        query: QueryOperation<UnbilledAccountReceivableResultLine>;
        read: ReadOperation<UnbilledAccountReceivableResultLine>;
        aggregate: {
            read: AggregateReadOperation<UnbilledAccountReceivableResultLine>;
            query: AggregateQueryOperation<UnbilledAccountReceivableResultLine>;
        };
        asyncOperations: UnbilledAccountReceivableResultLine$AsyncOperations;
        lookups(
            dataOrId: string | { data: UnbilledAccountReceivableResultLineInput },
        ): UnbilledAccountReceivableResultLine$Lookups;
        getDefaults: GetDefaultsOperation<UnbilledAccountReceivableResultLine>;
    }
    export interface SalesInvoiceLineToSalesCreditMemoLine extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        quantityInStockUnit: string;
        quantity: string;
        quantityInSalesUnit: string;
        amount: string;
        document: SalesCreditMemoLine;
        linkedDocument: SalesInvoiceLine;
    }
    export interface SalesInvoiceLineToSalesCreditMemoLineInput extends VitalClientNodeInput {
        quantityInStockUnit?: decimal | string;
        quantity?: decimal | string;
        amount?: decimal | string;
        linkedDocument?: integer | string;
    }
    export interface SalesInvoiceLineToSalesCreditMemoLineBinding extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        quantityInStockUnit: string;
        quantity: string;
        quantityInSalesUnit: string;
        amount: string;
        document: SalesCreditMemoLine;
        linkedDocument: SalesInvoiceLine;
    }
    export interface SalesInvoiceLineToSalesCreditMemoLine$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface SalesInvoiceLineToSalesCreditMemoLine$Lookups {
        linkedDocument: QueryOperation<SalesInvoiceLine>;
    }
    export interface SalesInvoiceLineToSalesCreditMemoLine$Operations {
        query: QueryOperation<SalesInvoiceLineToSalesCreditMemoLine>;
        read: ReadOperation<SalesInvoiceLineToSalesCreditMemoLine>;
        aggregate: {
            read: AggregateReadOperation<SalesInvoiceLineToSalesCreditMemoLine>;
            query: AggregateQueryOperation<SalesInvoiceLineToSalesCreditMemoLine>;
        };
        asyncOperations: SalesInvoiceLineToSalesCreditMemoLine$AsyncOperations;
        lookups(
            dataOrId: string | { data: SalesInvoiceLineToSalesCreditMemoLineInput },
        ): SalesInvoiceLineToSalesCreditMemoLine$Lookups;
        getDefaults: GetDefaultsOperation<SalesInvoiceLineToSalesCreditMemoLine>;
    }
    export interface SalesOrderLineToSalesInvoiceLine extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        quantityInStockUnit: string;
        quantity: string;
        quantityInSalesUnit: string;
        amount: string;
        document: SalesInvoiceLine;
        linkedDocument: SalesOrderLine;
    }
    export interface SalesOrderLineToSalesInvoiceLineInput extends VitalClientNodeInput {
        quantityInStockUnit?: decimal | string;
        quantity?: decimal | string;
        amount?: decimal | string;
        linkedDocument?: integer | string;
    }
    export interface SalesOrderLineToSalesInvoiceLineBinding extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        quantityInStockUnit: string;
        quantity: string;
        quantityInSalesUnit: string;
        amount: string;
        document: SalesInvoiceLine;
        linkedDocument: SalesOrderLine;
    }
    export interface SalesOrderLineToSalesInvoiceLine$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface SalesOrderLineToSalesInvoiceLine$Lookups {
        linkedDocument: QueryOperation<SalesOrderLine>;
    }
    export interface SalesOrderLineToSalesInvoiceLine$Operations {
        query: QueryOperation<SalesOrderLineToSalesInvoiceLine>;
        read: ReadOperation<SalesOrderLineToSalesInvoiceLine>;
        aggregate: {
            read: AggregateReadOperation<SalesOrderLineToSalesInvoiceLine>;
            query: AggregateQueryOperation<SalesOrderLineToSalesInvoiceLine>;
        };
        asyncOperations: SalesOrderLineToSalesInvoiceLine$AsyncOperations;
        lookups(
            dataOrId: string | { data: SalesOrderLineToSalesInvoiceLineInput },
        ): SalesOrderLineToSalesInvoiceLine$Lookups;
        getDefaults: GetDefaultsOperation<SalesOrderLineToSalesInvoiceLine>;
    }
    export interface SalesOrderLineToSalesShipmentLine extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        quantityInStockUnit: string;
        quantity: string;
        quantityInSalesUnit: string;
        amount: string;
        document: SalesShipmentLine;
        linkedDocument: SalesOrderLine;
    }
    export interface SalesOrderLineToSalesShipmentLineInput extends VitalClientNodeInput {
        quantityInStockUnit?: decimal | string;
        quantity?: decimal | string;
        amount?: decimal | string;
        linkedDocument?: integer | string;
    }
    export interface SalesOrderLineToSalesShipmentLineBinding extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        quantityInStockUnit: string;
        quantity: string;
        quantityInSalesUnit: string;
        amount: string;
        document: SalesShipmentLine;
        linkedDocument: SalesOrderLine;
    }
    export interface SalesOrderLineToSalesShipmentLine$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface SalesOrderLineToSalesShipmentLine$Lookups {
        linkedDocument: QueryOperation<SalesOrderLine>;
    }
    export interface SalesOrderLineToSalesShipmentLine$Operations {
        query: QueryOperation<SalesOrderLineToSalesShipmentLine>;
        read: ReadOperation<SalesOrderLineToSalesShipmentLine>;
        aggregate: {
            read: AggregateReadOperation<SalesOrderLineToSalesShipmentLine>;
            query: AggregateQueryOperation<SalesOrderLineToSalesShipmentLine>;
        };
        asyncOperations: SalesOrderLineToSalesShipmentLine$AsyncOperations;
        lookups(
            dataOrId: string | { data: SalesOrderLineToSalesShipmentLineInput },
        ): SalesOrderLineToSalesShipmentLine$Lookups;
        getDefaults: GetDefaultsOperation<SalesOrderLineToSalesShipmentLine>;
    }
    export interface SalesReturnRequestLineSalesCreditMemoLine extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        quantityInStockUnit: string;
        quantity: string;
        quantityInSalesUnit: string;
        amount: string;
        document: SalesCreditMemoLine;
        linkedDocument: SalesReturnRequestLine;
    }
    export interface SalesReturnRequestLineSalesCreditMemoLineInput extends VitalClientNodeInput {
        quantityInStockUnit?: decimal | string;
        quantity?: decimal | string;
        amount?: decimal | string;
        linkedDocument?: integer | string;
    }
    export interface SalesReturnRequestLineSalesCreditMemoLineBinding extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        quantityInStockUnit: string;
        quantity: string;
        quantityInSalesUnit: string;
        amount: string;
        document: SalesCreditMemoLine;
        linkedDocument: SalesReturnRequestLine;
    }
    export interface SalesReturnRequestLineSalesCreditMemoLine$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface SalesReturnRequestLineSalesCreditMemoLine$Lookups {
        linkedDocument: QueryOperation<SalesReturnRequestLine>;
    }
    export interface SalesReturnRequestLineSalesCreditMemoLine$Operations {
        query: QueryOperation<SalesReturnRequestLineSalesCreditMemoLine>;
        read: ReadOperation<SalesReturnRequestLineSalesCreditMemoLine>;
        aggregate: {
            read: AggregateReadOperation<SalesReturnRequestLineSalesCreditMemoLine>;
            query: AggregateQueryOperation<SalesReturnRequestLineSalesCreditMemoLine>;
        };
        asyncOperations: SalesReturnRequestLineSalesCreditMemoLine$AsyncOperations;
        lookups(
            dataOrId: string | { data: SalesReturnRequestLineSalesCreditMemoLineInput },
        ): SalesReturnRequestLineSalesCreditMemoLine$Lookups;
        getDefaults: GetDefaultsOperation<SalesReturnRequestLineSalesCreditMemoLine>;
    }
    export interface SalesReturnRequestLineToSalesReturnReceiptLine extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        quantityInStockUnit: string;
        quantity: string;
        quantityInSalesUnit: string;
        amount: string;
        document: SalesReturnReceiptLine;
        linkedDocument: SalesReturnRequestLine;
    }
    export interface SalesReturnRequestLineToSalesReturnReceiptLineInput extends VitalClientNodeInput {
        quantityInStockUnit?: decimal | string;
        quantity?: decimal | string;
        amount?: decimal | string;
        linkedDocument?: integer | string;
    }
    export interface SalesReturnRequestLineToSalesReturnReceiptLineBinding extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        quantityInStockUnit: string;
        quantity: string;
        quantityInSalesUnit: string;
        amount: string;
        document: SalesReturnReceiptLine;
        linkedDocument: SalesReturnRequestLine;
    }
    export interface SalesReturnRequestLineToSalesReturnReceiptLine$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface SalesReturnRequestLineToSalesReturnReceiptLine$Lookups {
        linkedDocument: QueryOperation<SalesReturnRequestLine>;
    }
    export interface SalesReturnRequestLineToSalesReturnReceiptLine$Operations {
        query: QueryOperation<SalesReturnRequestLineToSalesReturnReceiptLine>;
        read: ReadOperation<SalesReturnRequestLineToSalesReturnReceiptLine>;
        aggregate: {
            read: AggregateReadOperation<SalesReturnRequestLineToSalesReturnReceiptLine>;
            query: AggregateQueryOperation<SalesReturnRequestLineToSalesReturnReceiptLine>;
        };
        asyncOperations: SalesReturnRequestLineToSalesReturnReceiptLine$AsyncOperations;
        lookups(
            dataOrId: string | { data: SalesReturnRequestLineToSalesReturnReceiptLineInput },
        ): SalesReturnRequestLineToSalesReturnReceiptLine$Lookups;
        getDefaults: GetDefaultsOperation<SalesReturnRequestLineToSalesReturnReceiptLine>;
    }
    export interface SalesShipmentLineToSalesInvoiceLine extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        quantityInStockUnit: string;
        quantity: string;
        quantityInSalesUnit: string;
        amount: string;
        document: SalesInvoiceLine;
        linkedDocument: SalesShipmentLine;
    }
    export interface SalesShipmentLineToSalesInvoiceLineInput extends VitalClientNodeInput {
        quantityInStockUnit?: decimal | string;
        quantity?: decimal | string;
        amount?: decimal | string;
        linkedDocument?: integer | string;
    }
    export interface SalesShipmentLineToSalesInvoiceLineBinding extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        quantityInStockUnit: string;
        quantity: string;
        quantityInSalesUnit: string;
        amount: string;
        document: SalesInvoiceLine;
        linkedDocument: SalesShipmentLine;
    }
    export interface SalesShipmentLineToSalesInvoiceLine$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface SalesShipmentLineToSalesInvoiceLine$Lookups {
        linkedDocument: QueryOperation<SalesShipmentLine>;
    }
    export interface SalesShipmentLineToSalesInvoiceLine$Operations {
        query: QueryOperation<SalesShipmentLineToSalesInvoiceLine>;
        read: ReadOperation<SalesShipmentLineToSalesInvoiceLine>;
        aggregate: {
            read: AggregateReadOperation<SalesShipmentLineToSalesInvoiceLine>;
            query: AggregateQueryOperation<SalesShipmentLineToSalesInvoiceLine>;
        };
        asyncOperations: SalesShipmentLineToSalesInvoiceLine$AsyncOperations;
        lookups(
            dataOrId: string | { data: SalesShipmentLineToSalesInvoiceLineInput },
        ): SalesShipmentLineToSalesInvoiceLine$Lookups;
        getDefaults: GetDefaultsOperation<SalesShipmentLineToSalesInvoiceLine>;
    }
    export interface SalesShipmentLineToSalesReturnReceiptLine extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        quantityInStockUnit: string;
        quantity: string;
        quantityInSalesUnit: string;
        amount: string;
        document: SalesReturnReceiptLine;
        linkedDocument: SalesShipmentLine;
    }
    export interface SalesShipmentLineToSalesReturnReceiptLineInput extends VitalClientNodeInput {
        quantityInStockUnit?: decimal | string;
        quantity?: decimal | string;
        amount?: decimal | string;
        linkedDocument?: integer | string;
    }
    export interface SalesShipmentLineToSalesReturnReceiptLineBinding extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        quantityInStockUnit: string;
        quantity: string;
        quantityInSalesUnit: string;
        amount: string;
        document: SalesReturnReceiptLine;
        linkedDocument: SalesShipmentLine;
    }
    export interface SalesShipmentLineToSalesReturnReceiptLine$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface SalesShipmentLineToSalesReturnReceiptLine$Lookups {
        linkedDocument: QueryOperation<SalesShipmentLine>;
    }
    export interface SalesShipmentLineToSalesReturnReceiptLine$Operations {
        query: QueryOperation<SalesShipmentLineToSalesReturnReceiptLine>;
        read: ReadOperation<SalesShipmentLineToSalesReturnReceiptLine>;
        aggregate: {
            read: AggregateReadOperation<SalesShipmentLineToSalesReturnReceiptLine>;
            query: AggregateQueryOperation<SalesShipmentLineToSalesReturnReceiptLine>;
        };
        asyncOperations: SalesShipmentLineToSalesReturnReceiptLine$AsyncOperations;
        lookups(
            dataOrId: string | { data: SalesShipmentLineToSalesReturnReceiptLineInput },
        ): SalesShipmentLineToSalesReturnReceiptLine$Lookups;
        getDefaults: GetDefaultsOperation<SalesShipmentLineToSalesReturnReceiptLine>;
    }
    export interface SalesShipmentLineToSalesReturnRequestLine extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        quantityInStockUnit: string;
        quantity: string;
        quantityInSalesUnit: string;
        amount: string;
        document: SalesReturnRequestLine;
        linkedDocument: SalesShipmentLine;
    }
    export interface SalesShipmentLineToSalesReturnRequestLineInput extends VitalClientNodeInput {
        quantityInStockUnit?: decimal | string;
        quantity?: decimal | string;
        amount?: decimal | string;
        linkedDocument?: integer | string;
    }
    export interface SalesShipmentLineToSalesReturnRequestLineBinding extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        quantityInStockUnit: string;
        quantity: string;
        quantityInSalesUnit: string;
        amount: string;
        document: SalesReturnRequestLine;
        linkedDocument: SalesShipmentLine;
    }
    export interface SalesShipmentLineToSalesReturnRequestLine$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface SalesShipmentLineToSalesReturnRequestLine$Lookups {
        linkedDocument: QueryOperation<SalesShipmentLine>;
    }
    export interface SalesShipmentLineToSalesReturnRequestLine$Operations {
        query: QueryOperation<SalesShipmentLineToSalesReturnRequestLine>;
        read: ReadOperation<SalesShipmentLineToSalesReturnRequestLine>;
        aggregate: {
            read: AggregateReadOperation<SalesShipmentLineToSalesReturnRequestLine>;
            query: AggregateQueryOperation<SalesShipmentLineToSalesReturnRequestLine>;
        };
        asyncOperations: SalesShipmentLineToSalesReturnRequestLine$AsyncOperations;
        lookups(
            dataOrId: string | { data: SalesShipmentLineToSalesReturnRequestLineInput },
        ): SalesShipmentLineToSalesReturnRequestLine$Lookups;
        getDefaults: GetDefaultsOperation<SalesShipmentLineToSalesReturnRequestLine>;
    }
    export interface SalesCreditMemo extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        number: string;
        status: BaseStatus;
        approvalStatus: ApprovalStatus;
        date: string;
        canPrint: boolean;
        isPrinted: boolean;
        isSent: boolean;
        site: Site;
        stockSite: Site;
        companyCurrency: Currency;
        financialSite: Site;
        text: TextStream;
        internalNote: TextStream;
        externalNote: TextStream;
        isExternalNote: boolean;
        isTransferHeaderNote: boolean;
        isTransferLineNote: boolean;
        siteAddress: Address;
        businessEntityAddress: BusinessEntityAddress;
        paymentStatus: OpenItemStatus;
        taxEngine: TaxEngine;
        salesSite: Site;
        salesSiteName: string;
        salesSiteTaxIdNumber: string;
        salesSiteLinkedAddress: BusinessEntityAddress;
        salesSiteAddress: Address;
        salesSiteContact: Contact;
        reason: SalesCreditMemoReason;
        billToCustomer: Customer;
        billToCustomerName: string;
        billToLinkedAddress: BusinessEntityAddress;
        billToAddress: Address;
        billToContact: Contact;
        billToCustomerTaxIdNumber: string;
        paymentTerm: PaymentTerm;
        dueDate: string;
        incoterm: Incoterm;
        taxCalculationStatus: TaxCalculationStatus;
        penaltyPaymentType: DiscountOrPenaltyType;
        discountPaymentType: DiscountOrPenaltyType;
        fxRateDate: string;
        financeIntegrationApp: FinanceIntegrationApp;
        creationNumber: string;
        arOpenItems: ClientCollection<BaseOpenItem>;
        openItemSysId: integer;
        receipts: ClientCollection<PaymentDocumentLine>;
        discountPaymentBeforeDate: string;
        financeTransactions: ClientCollection<FinanceTransaction>;
        postingDetails: ClientCollection<FinanceTransaction>;
        isOpenItemPageOptionActive: boolean;
        financeIntegrationStatus: FinanceIntegrationStatus;
        _attachments: ClientCollection<AttachmentAssociation>;
        displayStatus: BaseDisplayStatus;
        currency: Currency;
        transactionCurrency: Currency;
        totalTaxAmount: string;
        totalTaxAmountAdjusted: string;
        totalTaxableAmount: string;
        totalExemptAmount: string;
        discountPaymentAmount: string;
        penaltyPaymentAmount: string;
        taxes: ClientCollection<SalesCreditMemoTax>;
        companyFxRate: string;
        companyFxRateDivisor: string;
        totalAmountPaid: string;
        forcedAmountPaid: string;
        transactionAmountPaid: string;
        companyAmountPaid: string;
        financialSiteAmountPaid: string;
        netBalance: string;
        lines: ClientCollection<SalesCreditMemoLine>;
        totalAmountExcludingTax: string;
        totalAmountIncludingTax: string;
        totalAmountIncludingTaxInCompanyCurrency: string;
        rateDescription: string;
        totalAmountExcludingTaxInCompanyCurrency: string;
        totalGrossProfit: string;
    }
    export interface SalesCreditMemoInput extends ClientNodeInput {
        number?: string;
        status?: BaseStatus;
        approvalStatus?: ApprovalStatus;
        date?: string;
        isPrinted?: boolean | string;
        isSent?: boolean | string;
        site?: integer | string;
        stockSite?: integer | string;
        financialSite?: integer | string;
        text?: TextStream;
        internalNote?: TextStream;
        externalNote?: TextStream;
        isExternalNote?: boolean | string;
        isOverwriteNote?: boolean | string;
        isTransferHeaderNote?: boolean | string;
        isTransferLineNote?: boolean | string;
        siteAddress?: integer | string;
        businessEntityAddress?: integer | string;
        paymentStatus?: OpenItemStatus;
        salesSiteName?: string;
        salesSiteTaxIdNumber?: string;
        salesSiteLinkedAddress?: integer | string;
        salesSiteAddress?: integer | string;
        salesSiteContact?: integer | string;
        reason?: integer | string;
        billToCustomer?: integer | string;
        billToCustomerName?: string;
        billToLinkedAddress?: integer | string;
        billToAddress?: integer | string;
        billToContact?: integer | string;
        billToCustomerTaxIdNumber?: string;
        paymentTerm?: integer | string;
        dueDate?: string;
        incoterm?: integer | string;
        taxCalculationStatus?: TaxCalculationStatus;
        penaltyPaymentType?: DiscountOrPenaltyType;
        discountPaymentType?: DiscountOrPenaltyType;
        fxRateDate?: string;
        creationNumber?: string;
        discountPaymentBeforeDate?: string;
        _attachments?: Partial<AttachmentAssociationInput>[];
        displayStatus?: BaseDisplayStatus;
        currency?: integer | string;
        totalTaxAmount?: decimal | string;
        totalTaxAmountAdjusted?: decimal | string;
        totalTaxableAmount?: decimal | string;
        totalExemptAmount?: decimal | string;
        discountPaymentAmount?: decimal | string;
        penaltyPaymentAmount?: decimal | string;
        taxes?: Partial<SalesCreditMemoTaxInput>[];
        companyFxRate?: decimal | string;
        companyFxRateDivisor?: decimal | string;
        transactionAmountPaid?: decimal | string;
        companyAmountPaid?: decimal | string;
        financialSiteAmountPaid?: decimal | string;
        lines?: Partial<SalesCreditMemoLineInput>[];
    }
    export interface SalesCreditMemoBinding extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        number: string;
        status: BaseStatus;
        approvalStatus: ApprovalStatus;
        date: string;
        canPrint: boolean;
        isPrinted: boolean;
        isSent: boolean;
        site: Site;
        stockSite: Site;
        companyCurrency: Currency;
        financialSite: Site;
        text: TextStream;
        internalNote: TextStream;
        externalNote: TextStream;
        isExternalNote: boolean;
        isOverwriteNote: boolean;
        isTransferHeaderNote: boolean;
        isTransferLineNote: boolean;
        siteAddress: Address;
        businessEntityAddress: BusinessEntityAddress;
        paymentStatus: OpenItemStatus;
        taxEngine: TaxEngine;
        salesSite: Site;
        salesSiteName: string;
        salesSiteTaxIdNumber: string;
        salesSiteLinkedAddress: BusinessEntityAddress;
        salesSiteAddress: Address;
        salesSiteContact: Contact;
        reason: SalesCreditMemoReason;
        billToCustomer: Customer;
        billToCustomerName: string;
        billToLinkedAddress: BusinessEntityAddress;
        billToAddress: Address;
        billToContact: Contact;
        billToCustomerTaxIdNumber: string;
        paymentTerm: PaymentTerm;
        dueDate: string;
        incoterm: Incoterm;
        taxCalculationStatus: TaxCalculationStatus;
        penaltyPaymentType: DiscountOrPenaltyType;
        discountPaymentType: DiscountOrPenaltyType;
        fxRateDate: string;
        financeIntegrationApp: FinanceIntegrationApp;
        creationNumber: string;
        arOpenItems: ClientCollection<BaseOpenItem>;
        openItemSysId: integer;
        receipts: ClientCollection<PaymentDocumentLine>;
        discountPaymentBeforeDate: string;
        financeTransactions: ClientCollection<FinanceTransaction>;
        postingDetails: ClientCollection<FinanceTransaction>;
        isOpenItemPageOptionActive: boolean;
        financeIntegrationStatus: FinanceIntegrationStatus;
        _attachments: ClientCollection<AttachmentAssociation>;
        displayStatus: BaseDisplayStatus;
        currency: Currency;
        transactionCurrency: Currency;
        totalTaxAmount: string;
        totalTaxAmountAdjusted: string;
        totalTaxableAmount: string;
        totalExemptAmount: string;
        discountPaymentAmount: string;
        penaltyPaymentAmount: string;
        taxes: ClientCollection<SalesCreditMemoTaxBinding>;
        companyFxRate: string;
        companyFxRateDivisor: string;
        totalAmountPaid: string;
        forcedAmountPaid: string;
        transactionAmountPaid: string;
        companyAmountPaid: string;
        financialSiteAmountPaid: string;
        netBalance: string;
        lines: ClientCollection<SalesCreditMemoLineBinding>;
        totalAmountExcludingTax: string;
        totalAmountIncludingTax: string;
        totalAmountIncludingTaxInCompanyCurrency: string;
        rateDescription: string;
        totalAmountExcludingTaxInCompanyCurrency: string;
        totalGrossProfit: string;
    }
    export interface SalesCreditMemo$Mutations {
        post: Node$Operation<
            {
                creditMemo: string;
            },
            {
                wasSuccessful: boolean;
                message: string;
            }
        >;
        repost: Node$Operation<
            {
                salesCreditMemo: string;
                documentData: {
                    header?: {
                        paymentTerm?: integer | string;
                    };
                    lines?: {
                        baseDocumentLineSysId?: integer | string;
                        storedAttributes?: string;
                        storedDimensions?: string;
                        uiTaxes?: string;
                    }[];
                };
            },
            {
                wasSuccessful: boolean;
                message: string;
            }
        >;
        financeIntegrationCheck: Node$Operation<
            {
                creditMemo: string;
            },
            {
                wasSuccessful: boolean;
                message: string;
            }
        >;
        setIsPrintedTrue: Node$Operation<
            {
                creditMemo: string;
            },
            boolean
        >;
        printSalesCreditMemoAndEmail: Node$Operation<
            {
                creditMemo: string;
                contactTitle: string;
                contactLastName: string;
                contactFirstName: string;
                contactEmail: string;
            },
            boolean
        >;
        beforePrintSalesCreditMemo: Node$Operation<
            {
                creditMemo: string;
            },
            boolean
        >;
        afterPrintSalesCreditMemo: Node$Operation<
            {
                creditMemo: string;
            },
            boolean
        >;
        resendNotificationForFinance: Node$Operation<
            {
                salesCreditMemo: string;
            },
            boolean
        >;
        beforePrint: Node$Operation<
            {
                creditMemo: string;
            },
            boolean
        >;
        enforceStatusPosted: Node$Operation<
            {
                creditMemo: string;
            },
            boolean
        >;
        sendApprovalRequestMail: Node$Operation<
            {
                document?: string;
                user?: UserInput | null;
            },
            boolean
        >;
    }
    export interface SalesCreditMemo$AsyncOperations {
        printBulkSalesCreditMemo: AsyncOperation<
            {
                filter?: string;
            },
            boolean
        >;
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
        bulkResync: AsyncOperation<
            {
                filter?: string;
            },
            boolean
        >;
    }
    export interface SalesCreditMemo$Lookups {
        site: QueryOperation<Site>;
        stockSite: QueryOperation<Site>;
        companyCurrency: QueryOperation<Currency>;
        financialSite: QueryOperation<Site>;
        siteAddress: QueryOperation<Address>;
        businessEntityAddress: QueryOperation<BusinessEntityAddress>;
        salesSite: QueryOperation<Site>;
        salesSiteLinkedAddress: QueryOperation<BusinessEntityAddress>;
        salesSiteAddress: QueryOperation<Address>;
        salesSiteContact: QueryOperation<Contact>;
        reason: QueryOperation<SalesCreditMemoReason>;
        billToCustomer: QueryOperation<Customer>;
        billToLinkedAddress: QueryOperation<BusinessEntityAddress>;
        billToAddress: QueryOperation<Address>;
        billToContact: QueryOperation<Contact>;
        paymentTerm: QueryOperation<PaymentTerm>;
        incoterm: QueryOperation<Incoterm>;
        currency: QueryOperation<Currency>;
        transactionCurrency: QueryOperation<Currency>;
    }
    export interface SalesCreditMemo$Operations {
        query: QueryOperation<SalesCreditMemo>;
        read: ReadOperation<SalesCreditMemo>;
        aggregate: {
            read: AggregateReadOperation<SalesCreditMemo>;
            query: AggregateQueryOperation<SalesCreditMemo>;
        };
        create: CreateOperation<SalesCreditMemoInput, SalesCreditMemo>;
        getDuplicate: GetDuplicateOperation<SalesCreditMemo>;
        duplicate: DuplicateOperation<string, SalesCreditMemoInput, SalesCreditMemo>;
        update: UpdateOperation<SalesCreditMemoInput, SalesCreditMemo>;
        updateById: UpdateByIdOperation<SalesCreditMemoInput, SalesCreditMemo>;
        delete: DeleteOperation<{}>;
        deleteById: DeleteOperation<string>;
        mutations: SalesCreditMemo$Mutations;
        asyncOperations: SalesCreditMemo$AsyncOperations;
        lookups(dataOrId: string | { data: SalesCreditMemoInput }): SalesCreditMemo$Lookups;
        getDefaults: GetDefaultsOperation<SalesCreditMemo>;
    }
    export interface SalesCreditMemoLine extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        documentNumber: string;
        documentId: integer;
        document: SalesCreditMemo;
        origin: BaseOrigin;
        status: BaseStatus;
        site: Site;
        siteLinkedAddress: BusinessEntityAddress;
        item: Item;
        itemDescription: string;
        stockSite: Site;
        stockSiteLinkedAddress: BusinessEntityAddress;
        itemSite: ItemSite;
        stockUnit: UnitOfMeasure;
        unit: UnitOfMeasure;
        quantity: string;
        internalNote: TextStream;
        externalNote: TextStream;
        isExternalNote: boolean;
        originDocumentType: SalesOriginDocumentType;
        salesSite: Site;
        quantityInSalesUnit: string;
        salesUnit: UnitOfMeasure;
        salesUnitToStockUnitConversionFactor: string;
        grossPrice: string;
        grossPriceDeterminated: string;
        discountCharges: ClientCollection<SalesCreditMemoLineDiscountCharge>;
        discount: string;
        charge: string;
        netPrice: string;
        taxDate: string;
        netPriceExcludingTax: string;
        amountExcludingTax: string;
        amountExcludingTaxInCompanyCurrency: string;
        toInvoiceLines: ClientCollection<SalesInvoiceLineToSalesCreditMemoLine>;
        toReturnRequestLines: ClientCollection<SalesReturnRequestLineSalesCreditMemoLine>;
        sourceDocumentNumber: string;
        sourceDocumentSysId: integer;
        sourceDocumentType: SourceDocumentType;
        consumptionLinkedAddress: BusinessEntityAddress;
        consumptionAddress: Address;
        providerSite: Site;
        providerSiteLinkedAddress: BusinessEntityAddress;
        providerSiteAddress: Address;
        priceOrigin: SalesPriceOrigin;
        priceReason: CustomerPriceReason;
        priceOriginDeterminated: SalesPriceOrigin;
        priceReasonDeterminated: CustomerPriceReason;
        isPriceDeterminated: boolean;
        taxes: ClientCollection<SalesCreditMemoLineTax>;
        uiTaxes: string;
        taxCalculationStatus: TaxCalculationStatus;
        dimensionDefinitionLevel: DimensionDefinitionLevel;
        storedDimensions: string;
        storedAttributes: string;
        computedAttributes: string;
        analyticalData: AnalyticalData;
        unitToStockUnitConversionFactor: string;
        discountDeterminated: string;
        chargeDeterminated: string;
        taxableAmount: string;
        taxAmount: string;
        exemptAmount: string;
        netPriceIncludingTax: string;
        taxAmountAdjusted: string;
        quantityInStockUnit: string;
        amountIncludingTax: string;
        amountIncludingTaxInCompanyCurrency: string;
        stockCostAmountInCompanyCurrency: string;
        grossProfitAmountInCompanyCurrency: string;
        stockCostAmount: string;
        grossProfitAmount: string;
        stockCostUnit: string;
        grossProfit: string;
    }
    export interface SalesCreditMemoLineInput extends VitalClientNodeInput {
        origin?: BaseOrigin;
        status?: BaseStatus;
        site?: integer | string;
        siteLinkedAddress?: integer | string;
        item?: integer | string;
        itemDescription?: string;
        stockSite?: integer | string;
        stockSiteLinkedAddress?: integer | string;
        stockUnit?: integer | string;
        unit?: integer | string;
        quantity?: decimal | string;
        internalNote?: TextStream;
        externalNote?: TextStream;
        isExternalNote?: boolean | string;
        grossPrice?: decimal | string;
        grossPriceDeterminated?: decimal | string;
        discountCharges?: Partial<SalesCreditMemoLineDiscountChargeInput>[];
        discount?: decimal | string;
        charge?: decimal | string;
        netPrice?: decimal | string;
        netPriceExcludingTax?: decimal | string;
        amountExcludingTaxInCompanyCurrency?: decimal | string;
        toInvoiceLines?: Partial<SalesInvoiceLineToSalesCreditMemoLineInput>[];
        toReturnRequestLines?: Partial<SalesReturnRequestLineSalesCreditMemoLineInput>[];
        consumptionLinkedAddress?: integer | string;
        consumptionAddress?: integer | string;
        providerSite?: integer | string;
        providerSiteLinkedAddress?: integer | string;
        providerSiteAddress?: integer | string;
        priceOrigin?: SalesPriceOrigin;
        priceReason?: integer | string;
        priceOriginDeterminated?: SalesPriceOrigin;
        priceReasonDeterminated?: integer | string;
        isPriceDeterminated?: boolean | string;
        taxes?: Partial<SalesCreditMemoLineTaxInput>[];
        uiTaxes?: string;
        storedDimensions?: string;
        storedAttributes?: string;
        analyticalData?: integer | string;
        unitToStockUnitConversionFactor?: decimal | string;
        discountDeterminated?: decimal | string;
        chargeDeterminated?: decimal | string;
        taxableAmount?: decimal | string;
        taxAmount?: decimal | string;
        exemptAmount?: decimal | string;
        netPriceIncludingTax?: decimal | string;
        taxAmountAdjusted?: decimal | string;
        quantityInStockUnit?: decimal | string;
        amountIncludingTax?: decimal | string;
        amountIncludingTaxInCompanyCurrency?: decimal | string;
    }
    export interface SalesCreditMemoLineBinding extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        documentNumber: string;
        documentId: integer;
        document: SalesCreditMemo;
        origin: BaseOrigin;
        status: BaseStatus;
        site: Site;
        siteLinkedAddress: BusinessEntityAddress;
        item: Item;
        itemDescription: string;
        stockSite: Site;
        stockSiteLinkedAddress: BusinessEntityAddress;
        itemSite: ItemSite;
        stockUnit: UnitOfMeasure;
        unit: UnitOfMeasure;
        quantity: string;
        internalNote: TextStream;
        externalNote: TextStream;
        isExternalNote: boolean;
        originDocumentType: SalesOriginDocumentType;
        salesSite: Site;
        quantityInSalesUnit: string;
        salesUnit: UnitOfMeasure;
        salesUnitToStockUnitConversionFactor: string;
        grossPrice: string;
        grossPriceDeterminated: string;
        discountCharges: ClientCollection<SalesCreditMemoLineDiscountChargeBinding>;
        discount: string;
        charge: string;
        netPrice: string;
        taxDate: string;
        netPriceExcludingTax: string;
        amountExcludingTax: string;
        amountExcludingTaxInCompanyCurrency: string;
        toInvoiceLines: ClientCollection<SalesInvoiceLineToSalesCreditMemoLineBinding>;
        toReturnRequestLines: ClientCollection<SalesReturnRequestLineSalesCreditMemoLineBinding>;
        sourceDocumentNumber: string;
        sourceDocumentSysId: integer;
        sourceDocumentType: SourceDocumentType;
        consumptionLinkedAddress: BusinessEntityAddress;
        consumptionAddress: Address;
        providerSite: Site;
        providerSiteLinkedAddress: BusinessEntityAddress;
        providerSiteAddress: Address;
        priceOrigin: SalesPriceOrigin;
        priceReason: CustomerPriceReason;
        priceOriginDeterminated: SalesPriceOrigin;
        priceReasonDeterminated: CustomerPriceReason;
        isPriceDeterminated: boolean;
        taxes: ClientCollection<SalesCreditMemoLineTaxBinding>;
        uiTaxes: any;
        taxCalculationStatus: TaxCalculationStatus;
        dimensionDefinitionLevel: DimensionDefinitionLevel;
        storedDimensions: any;
        storedAttributes: any;
        computedAttributes: any;
        analyticalData: AnalyticalData;
        unitToStockUnitConversionFactor: string;
        discountDeterminated: string;
        chargeDeterminated: string;
        taxableAmount: string;
        taxAmount: string;
        exemptAmount: string;
        netPriceIncludingTax: string;
        taxAmountAdjusted: string;
        quantityInStockUnit: string;
        amountIncludingTax: string;
        amountIncludingTaxInCompanyCurrency: string;
        stockCostAmountInCompanyCurrency: string;
        grossProfitAmountInCompanyCurrency: string;
        stockCostAmount: string;
        grossProfitAmount: string;
        stockCostUnit: string;
        grossProfit: string;
    }
    export interface SalesCreditMemoLine$Mutations {
        getTaxDetermination: Node$Operation<
            {
                creditMemoLine: string;
            },
            {
                taxCategory: TaxCategory;
                tax: Tax;
                isReverseCharge: boolean;
                rate: string;
                deductibleRate: string;
            }[]
        >;
        calculateLineTaxes: Node$Operation<
            {
                data?: {
                    site: integer | string;
                    businessPartner: integer | string;
                    addresses?: {
                        consumerCountry?: integer | string;
                        consumerPostcode?: string;
                        shipToCustomerAddress?: integer | string;
                    };
                    item: integer | string;
                    currency: integer | string;
                    amountExcludingTax: decimal | string;
                    quantity: decimal | string;
                    taxes?: {
                        taxReference?: integer | string;
                        taxCategoryReference?: integer | string;
                        isTaxMandatory: boolean | string;
                        isSubjectToGlTaxExcludedAmount: boolean | string;
                        _sortValue: integer | string;
                    }[];
                    taxDate?: string;
                };
            },
            {
                taxes: {
                    taxCategoryReference: TaxCategory;
                    taxCategory: string;
                    taxRate: string;
                    taxAmount: string;
                    taxReference: Tax;
                    tax: string;
                    deductibleTaxAmount: string;
                    deductibleTaxRate: string;
                    exemptAmount: string;
                    isReverseCharge: boolean;
                    nonTaxableAmount: string;
                    taxAmountAdjusted: string;
                    taxableAmount: string;
                    currency: Currency;
                    isTaxMandatory: boolean;
                    isSubjectToGlTaxExcludedAmount: boolean;
                    _sortValue: integer;
                }[];
                taxAmount: string;
                taxAmountAdjusted: string;
                amountIncludingTax: string;
            }
        >;
        setDimension: Node$Operation<
            {
                baseDocumentItemLine: string;
                storedDimensions?: string;
                storedAttributes?: string;
            },
            boolean
        >;
    }
    export interface SalesCreditMemoLine$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface SalesCreditMemoLine$Lookups {
        site: QueryOperation<Site>;
        siteLinkedAddress: QueryOperation<BusinessEntityAddress>;
        item: QueryOperation<Item>;
        stockSite: QueryOperation<Site>;
        stockSiteLinkedAddress: QueryOperation<BusinessEntityAddress>;
        itemSite: QueryOperation<ItemSite>;
        stockUnit: QueryOperation<UnitOfMeasure>;
        unit: QueryOperation<UnitOfMeasure>;
        salesSite: QueryOperation<Site>;
        salesUnit: QueryOperation<UnitOfMeasure>;
        consumptionLinkedAddress: QueryOperation<BusinessEntityAddress>;
        consumptionAddress: QueryOperation<Address>;
        providerSite: QueryOperation<Site>;
        providerSiteLinkedAddress: QueryOperation<BusinessEntityAddress>;
        providerSiteAddress: QueryOperation<Address>;
        priceReason: QueryOperation<CustomerPriceReason>;
        priceReasonDeterminated: QueryOperation<CustomerPriceReason>;
    }
    export interface SalesCreditMemoLine$Operations {
        query: QueryOperation<SalesCreditMemoLine>;
        read: ReadOperation<SalesCreditMemoLine>;
        aggregate: {
            read: AggregateReadOperation<SalesCreditMemoLine>;
            query: AggregateQueryOperation<SalesCreditMemoLine>;
        };
        mutations: SalesCreditMemoLine$Mutations;
        asyncOperations: SalesCreditMemoLine$AsyncOperations;
        lookups(dataOrId: string | { data: SalesCreditMemoLineInput }): SalesCreditMemoLine$Lookups;
        getDefaults: GetDefaultsOperation<SalesCreditMemoLine>;
    }
    export interface SalesOrderLineDiscountCharge extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        sign: DiscountChargeSign;
        valueType: DiscountChargeValueType;
        calculationBasis: DiscountChargeCalculationBasis;
        calculationRule: DiscountChargeCalculationRule;
        basisDeterminated: string;
        value: string;
        valueDeterminated: string;
        document: SalesOrderLine;
        basis: string;
        amount: string;
    }
    export interface SalesOrderLineDiscountChargeInput extends VitalClientNodeInput {
        sign?: DiscountChargeSign;
        valueType?: DiscountChargeValueType;
        calculationBasis?: DiscountChargeCalculationBasis;
        calculationRule?: DiscountChargeCalculationRule;
        basisDeterminated?: decimal | string;
        value?: decimal | string;
        valueDeterminated?: decimal | string;
        basis?: decimal | string;
        amount?: decimal | string;
    }
    export interface SalesOrderLineDiscountChargeBinding extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        sign: DiscountChargeSign;
        valueType: DiscountChargeValueType;
        calculationBasis: DiscountChargeCalculationBasis;
        calculationRule: DiscountChargeCalculationRule;
        basisDeterminated: string;
        value: string;
        valueDeterminated: string;
        document: SalesOrderLine;
        basis: string;
        amount: string;
    }
    export interface SalesOrderLineDiscountCharge$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface SalesOrderLineDiscountCharge$Operations {
        query: QueryOperation<SalesOrderLineDiscountCharge>;
        read: ReadOperation<SalesOrderLineDiscountCharge>;
        aggregate: {
            read: AggregateReadOperation<SalesOrderLineDiscountCharge>;
            query: AggregateQueryOperation<SalesOrderLineDiscountCharge>;
        };
        asyncOperations: SalesOrderLineDiscountCharge$AsyncOperations;
        getDefaults: GetDefaultsOperation<SalesOrderLineDiscountCharge>;
    }
    export interface SalesShipmentLineDiscountCharge extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        sign: DiscountChargeSign;
        valueType: DiscountChargeValueType;
        calculationBasis: DiscountChargeCalculationBasis;
        calculationRule: DiscountChargeCalculationRule;
        basisDeterminated: string;
        value: string;
        valueDeterminated: string;
        document: SalesShipmentLine;
        basis: string;
        amount: string;
    }
    export interface SalesShipmentLineDiscountChargeInput extends VitalClientNodeInput {
        sign?: DiscountChargeSign;
        valueType?: DiscountChargeValueType;
        calculationBasis?: DiscountChargeCalculationBasis;
        calculationRule?: DiscountChargeCalculationRule;
        basisDeterminated?: decimal | string;
        value?: decimal | string;
        valueDeterminated?: decimal | string;
        basis?: decimal | string;
        amount?: decimal | string;
    }
    export interface SalesShipmentLineDiscountChargeBinding extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        sign: DiscountChargeSign;
        valueType: DiscountChargeValueType;
        calculationBasis: DiscountChargeCalculationBasis;
        calculationRule: DiscountChargeCalculationRule;
        basisDeterminated: string;
        value: string;
        valueDeterminated: string;
        document: SalesShipmentLine;
        basis: string;
        amount: string;
    }
    export interface SalesShipmentLineDiscountCharge$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface SalesShipmentLineDiscountCharge$Operations {
        query: QueryOperation<SalesShipmentLineDiscountCharge>;
        read: ReadOperation<SalesShipmentLineDiscountCharge>;
        aggregate: {
            read: AggregateReadOperation<SalesShipmentLineDiscountCharge>;
            query: AggregateQueryOperation<SalesShipmentLineDiscountCharge>;
        };
        asyncOperations: SalesShipmentLineDiscountCharge$AsyncOperations;
        getDefaults: GetDefaultsOperation<SalesShipmentLineDiscountCharge>;
    }
    export interface SalesInvoiceLineDiscountCharge extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        sign: DiscountChargeSign;
        valueType: DiscountChargeValueType;
        calculationBasis: DiscountChargeCalculationBasis;
        calculationRule: DiscountChargeCalculationRule;
        basisDeterminated: string;
        value: string;
        valueDeterminated: string;
        document: SalesInvoiceLine;
        basis: string;
        amount: string;
    }
    export interface SalesInvoiceLineDiscountChargeInput extends VitalClientNodeInput {
        sign?: DiscountChargeSign;
        valueType?: DiscountChargeValueType;
        calculationBasis?: DiscountChargeCalculationBasis;
        calculationRule?: DiscountChargeCalculationRule;
        basisDeterminated?: decimal | string;
        value?: decimal | string;
        valueDeterminated?: decimal | string;
        basis?: decimal | string;
        amount?: decimal | string;
    }
    export interface SalesInvoiceLineDiscountChargeBinding extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        sign: DiscountChargeSign;
        valueType: DiscountChargeValueType;
        calculationBasis: DiscountChargeCalculationBasis;
        calculationRule: DiscountChargeCalculationRule;
        basisDeterminated: string;
        value: string;
        valueDeterminated: string;
        document: SalesInvoiceLine;
        basis: string;
        amount: string;
    }
    export interface SalesInvoiceLineDiscountCharge$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface SalesInvoiceLineDiscountCharge$Operations {
        query: QueryOperation<SalesInvoiceLineDiscountCharge>;
        read: ReadOperation<SalesInvoiceLineDiscountCharge>;
        aggregate: {
            read: AggregateReadOperation<SalesInvoiceLineDiscountCharge>;
            query: AggregateQueryOperation<SalesInvoiceLineDiscountCharge>;
        };
        asyncOperations: SalesInvoiceLineDiscountCharge$AsyncOperations;
        getDefaults: GetDefaultsOperation<SalesInvoiceLineDiscountCharge>;
    }
    export interface SalesCreditMemoLineDiscountCharge extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        sign: DiscountChargeSign;
        valueType: DiscountChargeValueType;
        calculationBasis: DiscountChargeCalculationBasis;
        calculationRule: DiscountChargeCalculationRule;
        basisDeterminated: string;
        value: string;
        valueDeterminated: string;
        document: SalesCreditMemoLine;
        basis: string;
        amount: string;
    }
    export interface SalesCreditMemoLineDiscountChargeInput extends VitalClientNodeInput {
        sign?: DiscountChargeSign;
        valueType?: DiscountChargeValueType;
        calculationBasis?: DiscountChargeCalculationBasis;
        calculationRule?: DiscountChargeCalculationRule;
        basisDeterminated?: decimal | string;
        value?: decimal | string;
        valueDeterminated?: decimal | string;
        basis?: decimal | string;
        amount?: decimal | string;
    }
    export interface SalesCreditMemoLineDiscountChargeBinding extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        sign: DiscountChargeSign;
        valueType: DiscountChargeValueType;
        calculationBasis: DiscountChargeCalculationBasis;
        calculationRule: DiscountChargeCalculationRule;
        basisDeterminated: string;
        value: string;
        valueDeterminated: string;
        document: SalesCreditMemoLine;
        basis: string;
        amount: string;
    }
    export interface SalesCreditMemoLineDiscountCharge$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface SalesCreditMemoLineDiscountCharge$Operations {
        query: QueryOperation<SalesCreditMemoLineDiscountCharge>;
        read: ReadOperation<SalesCreditMemoLineDiscountCharge>;
        aggregate: {
            read: AggregateReadOperation<SalesCreditMemoLineDiscountCharge>;
            query: AggregateQueryOperation<SalesCreditMemoLineDiscountCharge>;
        };
        asyncOperations: SalesCreditMemoLineDiscountCharge$AsyncOperations;
        getDefaults: GetDefaultsOperation<SalesCreditMemoLineDiscountCharge>;
    }
    export interface SalesInvoice extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        number: string;
        status: BaseStatus;
        approvalStatus: ApprovalStatus;
        date: string;
        canPrint: boolean;
        isPrinted: boolean;
        isSent: boolean;
        site: Site;
        stockSite: Site;
        companyCurrency: Currency;
        financialSite: Site;
        text: TextStream;
        internalNote: TextStream;
        externalNote: TextStream;
        isExternalNote: boolean;
        isTransferHeaderNote: boolean;
        isTransferLineNote: boolean;
        siteAddress: Address;
        businessEntityAddress: BusinessEntityAddress;
        paymentStatus: OpenItemStatus;
        invoiceDate: string;
        enablePrintButton: boolean;
        taxEngine: TaxEngine;
        salesSite: Site;
        salesSiteName: string;
        salesSiteTaxIdNumber: string;
        salesSiteLinkedAddress: BusinessEntityAddress;
        salesSiteAddress: Address;
        salesSiteContact: Contact;
        billToCustomer: Customer;
        billToCustomerName: string;
        billToCustomerTaxIdNumber: string;
        billToLinkedAddress: BusinessEntityAddress;
        billToAddress: Address;
        billToContact: Contact;
        paymentTerm: PaymentTerm;
        incoterm: Incoterm;
        taxCalculationStatus: TaxCalculationStatus;
        dueDate: string;
        penaltyPaymentType: DiscountOrPenaltyType;
        discountPaymentType: DiscountOrPenaltyType;
        creditStatus: SalesDocumentCreditStatus;
        rateDescription: string;
        fxRateDate: string;
        financeIntegrationApp: FinanceIntegrationApp;
        creationNumber: string;
        arOpenItems: ClientCollection<BaseOpenItem>;
        openItemSysId: integer;
        receipts: ClientCollection<PaymentDocumentLine>;
        discountPaymentBeforeDate: string;
        financeTransactions: ClientCollection<FinanceTransaction>;
        isOpenItemPageOptionActive: boolean;
        postingDetails: ClientCollection<FinanceTransaction>;
        financeIntegrationStatus: FinanceIntegrationStatus;
        _attachments: ClientCollection<AttachmentAssociation>;
        currency: Currency;
        transactionCurrency: Currency;
        totalTaxAmount: string;
        totalTaxableAmount: string;
        totalExemptAmount: string;
        totalTaxAmountAdjusted: string;
        discountPaymentAmount: string;
        penaltyPaymentAmount: string;
        taxes: ClientCollection<SalesInvoiceTax>;
        companyFxRate: string;
        companyFxRateDivisor: string;
        totalAmountPaid: string;
        forcedAmountPaid: string;
        transactionAmountPaid: string;
        companyAmountPaid: string;
        financialSiteAmountPaid: string;
        netBalance: string;
        lines: ClientCollection<SalesInvoiceLine>;
        totalAmountExcludingTax: string;
        totalAmountIncludingTax: string;
        totalAmountIncludingTaxInCompanyCurrency: string;
        totalAmountExcludingTaxInCompanyCurrency: string;
        totalGrossProfit: string;
        displayStatus: BaseDisplayStatus;
    }
    export interface SalesInvoiceInput extends ClientNodeInput {
        number?: string;
        status?: BaseStatus;
        approvalStatus?: ApprovalStatus;
        date?: string;
        isPrinted?: boolean | string;
        isSent?: boolean | string;
        site?: integer | string;
        stockSite?: integer | string;
        financialSite?: integer | string;
        text?: TextStream;
        internalNote?: TextStream;
        externalNote?: TextStream;
        isExternalNote?: boolean | string;
        isOverwriteNote?: boolean | string;
        isTransferHeaderNote?: boolean | string;
        isTransferLineNote?: boolean | string;
        siteAddress?: integer | string;
        businessEntityAddress?: integer | string;
        paymentStatus?: OpenItemStatus;
        salesSiteName?: string;
        salesSiteTaxIdNumber?: string;
        salesSiteLinkedAddress?: integer | string;
        salesSiteAddress?: integer | string;
        salesSiteContact?: integer | string;
        billToCustomer?: integer | string;
        billToCustomerName?: string;
        billToCustomerTaxIdNumber?: string;
        billToLinkedAddress?: integer | string;
        billToAddress?: integer | string;
        billToContact?: integer | string;
        paymentTerm?: integer | string;
        incoterm?: integer | string;
        taxCalculationStatus?: TaxCalculationStatus;
        dueDate?: string;
        penaltyPaymentType?: DiscountOrPenaltyType;
        discountPaymentType?: DiscountOrPenaltyType;
        creditStatus?: SalesDocumentCreditStatus;
        fxRateDate?: string;
        creationNumber?: string;
        discountPaymentBeforeDate?: string;
        _attachments?: Partial<AttachmentAssociationInput>[];
        currency?: integer | string;
        totalTaxAmount?: decimal | string;
        totalTaxableAmount?: decimal | string;
        totalExemptAmount?: decimal | string;
        totalTaxAmountAdjusted?: decimal | string;
        discountPaymentAmount?: decimal | string;
        penaltyPaymentAmount?: decimal | string;
        taxes?: Partial<SalesInvoiceTaxInput>[];
        companyFxRate?: decimal | string;
        companyFxRateDivisor?: decimal | string;
        transactionAmountPaid?: decimal | string;
        companyAmountPaid?: decimal | string;
        financialSiteAmountPaid?: decimal | string;
        lines?: Partial<SalesInvoiceLineInput>[];
        displayStatus?: BaseDisplayStatus;
    }
    export interface SalesInvoiceBinding extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        number: string;
        status: BaseStatus;
        approvalStatus: ApprovalStatus;
        date: string;
        canPrint: boolean;
        isPrinted: boolean;
        isSent: boolean;
        site: Site;
        stockSite: Site;
        companyCurrency: Currency;
        financialSite: Site;
        text: TextStream;
        internalNote: TextStream;
        externalNote: TextStream;
        isExternalNote: boolean;
        isOverwriteNote: boolean;
        isTransferHeaderNote: boolean;
        isTransferLineNote: boolean;
        siteAddress: Address;
        businessEntityAddress: BusinessEntityAddress;
        paymentStatus: OpenItemStatus;
        invoiceDate: string;
        enablePrintButton: boolean;
        taxEngine: TaxEngine;
        salesSite: Site;
        salesSiteName: string;
        salesSiteTaxIdNumber: string;
        salesSiteLinkedAddress: BusinessEntityAddress;
        salesSiteAddress: Address;
        salesSiteContact: Contact;
        billToCustomer: Customer;
        billToCustomerName: string;
        billToCustomerTaxIdNumber: string;
        billToLinkedAddress: BusinessEntityAddress;
        billToAddress: Address;
        billToContact: Contact;
        paymentTerm: PaymentTerm;
        incoterm: Incoterm;
        taxCalculationStatus: TaxCalculationStatus;
        dueDate: string;
        penaltyPaymentType: DiscountOrPenaltyType;
        discountPaymentType: DiscountOrPenaltyType;
        creditStatus: SalesDocumentCreditStatus;
        rateDescription: string;
        fxRateDate: string;
        financeIntegrationApp: FinanceIntegrationApp;
        creationNumber: string;
        arOpenItems: ClientCollection<BaseOpenItem>;
        openItemSysId: integer;
        receipts: ClientCollection<PaymentDocumentLine>;
        discountPaymentBeforeDate: string;
        financeTransactions: ClientCollection<FinanceTransaction>;
        isOpenItemPageOptionActive: boolean;
        postingDetails: ClientCollection<FinanceTransaction>;
        financeIntegrationStatus: FinanceIntegrationStatus;
        _attachments: ClientCollection<AttachmentAssociation>;
        currency: Currency;
        transactionCurrency: Currency;
        totalTaxAmount: string;
        totalTaxableAmount: string;
        totalExemptAmount: string;
        totalTaxAmountAdjusted: string;
        discountPaymentAmount: string;
        penaltyPaymentAmount: string;
        taxes: ClientCollection<SalesInvoiceTaxBinding>;
        companyFxRate: string;
        companyFxRateDivisor: string;
        totalAmountPaid: string;
        forcedAmountPaid: string;
        transactionAmountPaid: string;
        companyAmountPaid: string;
        financialSiteAmountPaid: string;
        netBalance: string;
        lines: ClientCollection<SalesInvoiceLineBinding>;
        totalAmountExcludingTax: string;
        totalAmountIncludingTax: string;
        totalAmountIncludingTaxInCompanyCurrency: string;
        totalAmountExcludingTaxInCompanyCurrency: string;
        totalGrossProfit: string;
        displayStatus: BaseDisplayStatus;
    }
    export interface SalesInvoice$Queries {
        getDealSizeTrend: Node$Operation<
            {
                currency?: string;
            },
            string
        >;
        getYtdSales: Node$Operation<
            {
                currency?: string;
            },
            string
        >;
        getMtdSales: Node$Operation<
            {
                currency?: string;
            },
            {
                currentMonth: string;
                previousMonth: string;
            }
        >;
        getCustomerYtdSales: Node$Operation<
            {
                customer: string;
            },
            {
                ytdTotal: string;
                currencySymbol: string;
                decimalDigits: integer;
            }
        >;
        getCustomerYtdGross: Node$Operation<
            {
                customer: string;
            },
            {
                ytdTotal: string;
                currencySymbol: string;
                decimalDigits: integer;
            }
        >;
    }
    export interface SalesInvoice$Mutations {
        createSalesCreditMemosFromInvoices: Node$Operation<
            {
                salesDocuments?: string[];
                creditMemoDate?: string;
            },
            {
                status: string;
                numberOfCreditMemos: integer;
                documentsCreated: SalesCreditMemo[];
                lineErrors: {
                    lineNumber: integer;
                    linePosition: integer;
                    message: string;
                }[];
            }
        >;
        createSalesCreditMemosFromInvoiceLines: Node$Operation<
            {
                salesDocumentLines?: {
                    salesDocumentLine?: integer | string;
                    quantityToProcess?: decimal | string;
                }[];
                creditMemoDate?: string;
            },
            {
                status: string;
                numberOfCreditMemos: integer;
                documentsCreated: string[];
                lineErrors: {
                    lineNumber: integer;
                    linePosition: integer;
                    message: string;
                }[];
            }
        >;
        post: Node$Operation<
            {
                invoice: string;
                isSafeToRetry?: boolean | string;
            },
            SalesInvoice
        >;
        repost: Node$Operation<
            {
                salesInvoice: string;
                documentData: {
                    header?: {
                        paymentTerm?: integer | string;
                    };
                    lines?: {
                        baseDocumentLineSysId?: integer | string;
                        storedAttributes?: string;
                        storedDimensions?: string;
                        uiTaxes?: string;
                    }[];
                };
            },
            {
                wasSuccessful: boolean;
                message: string;
            }
        >;
        financeIntegrationCheck: Node$Operation<
            {
                invoice: string;
            },
            {
                wasSuccessful: boolean;
                message: string;
            }
        >;
        printSalesInvoiceAndEmail: Node$Operation<
            {
                invoice: string;
                contactTitle: string;
                contactLastName: string;
                contactFirstName: string;
                contactEmail: string;
            },
            boolean
        >;
        massPriceDeterminationCalculation: Node$Operation<
            {
                salesDocument: string;
            },
            boolean
        >;
        beforePrint: Node$Operation<
            {
                invoice: string;
            },
            boolean
        >;
        afterPrint: Node$Operation<
            {
                invoice: string;
            },
            boolean
        >;
        resendNotificationForFinance: Node$Operation<
            {
                salesInvoice: string;
            },
            boolean
        >;
        enforceStatusPosted: Node$Operation<
            {
                invoice: string;
            },
            boolean
        >;
        sendApprovalRequestMail: Node$Operation<
            {
                document?: string;
                user?: UserInput | null;
            },
            boolean
        >;
    }
    export interface SalesInvoice$AsyncOperations {
        postInvoice: AsyncOperation<
            {
                invoice: string;
                isSafeToRetry?: boolean | string;
            },
            SalesInvoice
        >;
        printBulkSalesInvoice: AsyncOperation<
            {
                filter?: string;
            },
            boolean
        >;
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
        bulkResync: AsyncOperation<
            {
                filter?: string;
            },
            boolean
        >;
    }
    export interface SalesInvoice$Lookups {
        site: QueryOperation<Site>;
        stockSite: QueryOperation<Site>;
        companyCurrency: QueryOperation<Currency>;
        financialSite: QueryOperation<Site>;
        siteAddress: QueryOperation<Address>;
        businessEntityAddress: QueryOperation<BusinessEntityAddress>;
        salesSite: QueryOperation<Site>;
        salesSiteLinkedAddress: QueryOperation<BusinessEntityAddress>;
        salesSiteAddress: QueryOperation<Address>;
        salesSiteContact: QueryOperation<Contact>;
        billToCustomer: QueryOperation<Customer>;
        billToLinkedAddress: QueryOperation<BusinessEntityAddress>;
        billToAddress: QueryOperation<Address>;
        billToContact: QueryOperation<Contact>;
        paymentTerm: QueryOperation<PaymentTerm>;
        incoterm: QueryOperation<Incoterm>;
        currency: QueryOperation<Currency>;
        transactionCurrency: QueryOperation<Currency>;
    }
    export interface SalesInvoice$Operations {
        query: QueryOperation<SalesInvoice>;
        read: ReadOperation<SalesInvoice>;
        aggregate: {
            read: AggregateReadOperation<SalesInvoice>;
            query: AggregateQueryOperation<SalesInvoice>;
        };
        queries: SalesInvoice$Queries;
        create: CreateOperation<SalesInvoiceInput, SalesInvoice>;
        getDuplicate: GetDuplicateOperation<SalesInvoice>;
        duplicate: DuplicateOperation<string, SalesInvoiceInput, SalesInvoice>;
        update: UpdateOperation<SalesInvoiceInput, SalesInvoice>;
        updateById: UpdateByIdOperation<SalesInvoiceInput, SalesInvoice>;
        delete: DeleteOperation<{}>;
        deleteById: DeleteOperation<string>;
        mutations: SalesInvoice$Mutations;
        asyncOperations: SalesInvoice$AsyncOperations;
        lookups(dataOrId: string | { data: SalesInvoiceInput }): SalesInvoice$Lookups;
        getDefaults: GetDefaultsOperation<SalesInvoice>;
    }
    export interface SalesInvoiceLine extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        documentNumber: string;
        documentId: integer;
        document: SalesInvoice;
        origin: BaseOrigin;
        status: BaseStatus;
        site: Site;
        siteLinkedAddress: BusinessEntityAddress;
        item: Item;
        itemDescription: string;
        stockSite: Site;
        stockSiteLinkedAddress: BusinessEntityAddress;
        itemSite: ItemSite;
        stockUnit: UnitOfMeasure;
        unit: UnitOfMeasure;
        quantity: string;
        internalNote: TextStream;
        externalNote: TextStream;
        isExternalNote: boolean;
        originDocumentType: SalesOriginDocumentType;
        salesSite: Site;
        quantityInSalesUnit: string;
        salesUnit: UnitOfMeasure;
        salesUnitToStockUnitConversionFactor: string;
        discountCharges: ClientCollection<SalesInvoiceLineDiscountCharge>;
        grossPrice: string;
        discountDeterminated: string;
        chargeDeterminated: string;
        grossPriceDeterminated: string;
        netPrice: string;
        taxDate: string;
        netPriceExcludingTax: string;
        amountExcludingTax: string;
        amountExcludingTaxInCompanyCurrency: string;
        consumptionLinkedAddress: BusinessEntityAddress;
        consumptionAddress: Address;
        providerSite: Site;
        providerSiteLinkedAddress: BusinessEntityAddress;
        providerSiteAddress: Address;
        salesOrderLines: ClientCollection<SalesOrderLineToSalesInvoiceLine>;
        salesShipmentLines: ClientCollection<SalesShipmentLineToSalesInvoiceLine>;
        sourceDocumentNumber: string;
        sourceDocumentType: SourceDocumentType;
        sourceDocumentSysId: integer;
        creditStatus: SalesDocumentCreditStatus;
        quantityCreditedInProgressInSalesUnit: string;
        quantityCreditedPostedInSalesUnit: string;
        priceOrigin: SalesPriceOrigin;
        priceReason: CustomerPriceReason;
        priceOriginDeterminated: SalesPriceOrigin;
        priceReasonDeterminated: CustomerPriceReason;
        isPriceDeterminated: boolean;
        taxes: ClientCollection<SalesInvoiceLineTax>;
        uiTaxes: string;
        taxCalculationStatus: TaxCalculationStatus;
        customerNumber: string;
        dimensionDefinitionLevel: DimensionDefinitionLevel;
        storedDimensions: string;
        storedAttributes: string;
        computedAttributes: string;
        analyticalData: AnalyticalData;
        unitToStockUnitConversionFactor: string;
        discount: string;
        charge: string;
        taxableAmount: string;
        taxAmount: string;
        exemptAmount: string;
        netPriceIncludingTax: string;
        taxAmountAdjusted: string;
        quantityInStockUnit: string;
        amountIncludingTax: string;
        amountIncludingTaxInCompanyCurrency: string;
        stockCostAmountInCompanyCurrency: string;
        grossProfitAmountInCompanyCurrency: string;
        stockCostAmount: string;
        grossProfitAmount: string;
        stockCostUnit: string;
        grossProfit: string;
    }
    export interface SalesInvoiceLineInput extends VitalClientNodeInput {
        origin?: BaseOrigin;
        status?: BaseStatus;
        site?: integer | string;
        siteLinkedAddress?: integer | string;
        item?: integer | string;
        itemDescription?: string;
        stockSite?: integer | string;
        stockSiteLinkedAddress?: integer | string;
        stockUnit?: integer | string;
        unit?: integer | string;
        quantity?: decimal | string;
        internalNote?: TextStream;
        externalNote?: TextStream;
        isExternalNote?: boolean | string;
        discountCharges?: Partial<SalesInvoiceLineDiscountChargeInput>[];
        grossPrice?: decimal | string;
        discountDeterminated?: decimal | string;
        chargeDeterminated?: decimal | string;
        grossPriceDeterminated?: decimal | string;
        netPrice?: decimal | string;
        netPriceExcludingTax?: decimal | string;
        amountExcludingTaxInCompanyCurrency?: decimal | string;
        consumptionLinkedAddress?: integer | string;
        consumptionAddress?: integer | string;
        providerSite?: integer | string;
        providerSiteLinkedAddress?: integer | string;
        providerSiteAddress?: integer | string;
        salesOrderLines?: Partial<SalesOrderLineToSalesInvoiceLineInput>[];
        salesShipmentLines?: Partial<SalesShipmentLineToSalesInvoiceLineInput>[];
        creditStatus?: SalesDocumentCreditStatus;
        priceOrigin?: SalesPriceOrigin;
        priceReason?: integer | string;
        priceOriginDeterminated?: SalesPriceOrigin;
        priceReasonDeterminated?: integer | string;
        isPriceDeterminated?: boolean | string;
        taxes?: Partial<SalesInvoiceLineTaxInput>[];
        uiTaxes?: string;
        customerNumber?: string;
        storedDimensions?: string;
        storedAttributes?: string;
        analyticalData?: integer | string;
        unitToStockUnitConversionFactor?: decimal | string;
        discount?: decimal | string;
        charge?: decimal | string;
        taxableAmount?: decimal | string;
        taxAmount?: decimal | string;
        exemptAmount?: decimal | string;
        netPriceIncludingTax?: decimal | string;
        taxAmountAdjusted?: decimal | string;
        quantityInStockUnit?: decimal | string;
        amountIncludingTax?: decimal | string;
        amountIncludingTaxInCompanyCurrency?: decimal | string;
    }
    export interface SalesInvoiceLineBinding extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        documentNumber: string;
        documentId: integer;
        document: SalesInvoice;
        origin: BaseOrigin;
        status: BaseStatus;
        site: Site;
        siteLinkedAddress: BusinessEntityAddress;
        item: Item;
        itemDescription: string;
        stockSite: Site;
        stockSiteLinkedAddress: BusinessEntityAddress;
        itemSite: ItemSite;
        stockUnit: UnitOfMeasure;
        unit: UnitOfMeasure;
        quantity: string;
        internalNote: TextStream;
        externalNote: TextStream;
        isExternalNote: boolean;
        originDocumentType: SalesOriginDocumentType;
        salesSite: Site;
        quantityInSalesUnit: string;
        salesUnit: UnitOfMeasure;
        salesUnitToStockUnitConversionFactor: string;
        discountCharges: ClientCollection<SalesInvoiceLineDiscountChargeBinding>;
        grossPrice: string;
        discountDeterminated: string;
        chargeDeterminated: string;
        grossPriceDeterminated: string;
        netPrice: string;
        taxDate: string;
        netPriceExcludingTax: string;
        amountExcludingTax: string;
        amountExcludingTaxInCompanyCurrency: string;
        consumptionLinkedAddress: BusinessEntityAddress;
        consumptionAddress: Address;
        providerSite: Site;
        providerSiteLinkedAddress: BusinessEntityAddress;
        providerSiteAddress: Address;
        salesOrderLines: ClientCollection<SalesOrderLineToSalesInvoiceLineBinding>;
        salesShipmentLines: ClientCollection<SalesShipmentLineToSalesInvoiceLineBinding>;
        sourceDocumentNumber: string;
        sourceDocumentType: SourceDocumentType;
        sourceDocumentSysId: integer;
        creditStatus: SalesDocumentCreditStatus;
        quantityCreditedInProgressInSalesUnit: string;
        quantityCreditedPostedInSalesUnit: string;
        priceOrigin: SalesPriceOrigin;
        priceReason: CustomerPriceReason;
        priceOriginDeterminated: SalesPriceOrigin;
        priceReasonDeterminated: CustomerPriceReason;
        isPriceDeterminated: boolean;
        taxes: ClientCollection<SalesInvoiceLineTaxBinding>;
        uiTaxes: any;
        taxCalculationStatus: TaxCalculationStatus;
        customerNumber: string;
        dimensionDefinitionLevel: DimensionDefinitionLevel;
        storedDimensions: any;
        storedAttributes: any;
        computedAttributes: any;
        analyticalData: AnalyticalData;
        unitToStockUnitConversionFactor: string;
        discount: string;
        charge: string;
        taxableAmount: string;
        taxAmount: string;
        exemptAmount: string;
        netPriceIncludingTax: string;
        taxAmountAdjusted: string;
        quantityInStockUnit: string;
        amountIncludingTax: string;
        amountIncludingTaxInCompanyCurrency: string;
        stockCostAmountInCompanyCurrency: string;
        grossProfitAmountInCompanyCurrency: string;
        stockCostAmount: string;
        grossProfitAmount: string;
        stockCostUnit: string;
        grossProfit: string;
    }
    export interface SalesInvoiceLine$Mutations {
        calculateLineTaxes: Node$Operation<
            {
                data?: {
                    site: integer | string;
                    businessPartner: integer | string;
                    addresses?: {
                        consumerCountry?: integer | string;
                        consumerPostcode?: string;
                        shipToCustomerAddress?: integer | string;
                    };
                    item: integer | string;
                    currency: integer | string;
                    amountExcludingTax: decimal | string;
                    quantity: decimal | string;
                    taxes?: {
                        taxReference?: integer | string;
                        taxCategoryReference?: integer | string;
                        isTaxMandatory: boolean | string;
                        isSubjectToGlTaxExcludedAmount: boolean | string;
                        _sortValue: integer | string;
                    }[];
                    taxDate?: string;
                };
            },
            {
                taxes: {
                    taxCategoryReference: TaxCategory;
                    taxCategory: string;
                    taxRate: string;
                    taxAmount: string;
                    taxReference: Tax;
                    tax: string;
                    deductibleTaxAmount: string;
                    deductibleTaxRate: string;
                    exemptAmount: string;
                    isReverseCharge: boolean;
                    nonTaxableAmount: string;
                    taxAmountAdjusted: string;
                    taxableAmount: string;
                    currency: Currency;
                    isTaxMandatory: boolean;
                    isSubjectToGlTaxExcludedAmount: boolean;
                    _sortValue: integer;
                }[];
                taxAmount: string;
                taxAmountAdjusted: string;
                amountIncludingTax: string;
            }
        >;
        setDimension: Node$Operation<
            {
                baseDocumentItemLine: string;
                storedDimensions?: string;
                storedAttributes?: string;
            },
            boolean
        >;
    }
    export interface SalesInvoiceLine$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface SalesInvoiceLine$Lookups {
        site: QueryOperation<Site>;
        siteLinkedAddress: QueryOperation<BusinessEntityAddress>;
        item: QueryOperation<Item>;
        stockSite: QueryOperation<Site>;
        stockSiteLinkedAddress: QueryOperation<BusinessEntityAddress>;
        itemSite: QueryOperation<ItemSite>;
        stockUnit: QueryOperation<UnitOfMeasure>;
        unit: QueryOperation<UnitOfMeasure>;
        salesSite: QueryOperation<Site>;
        salesUnit: QueryOperation<UnitOfMeasure>;
        consumptionLinkedAddress: QueryOperation<BusinessEntityAddress>;
        consumptionAddress: QueryOperation<Address>;
        providerSite: QueryOperation<Site>;
        providerSiteLinkedAddress: QueryOperation<BusinessEntityAddress>;
        providerSiteAddress: QueryOperation<Address>;
        priceReason: QueryOperation<CustomerPriceReason>;
        priceReasonDeterminated: QueryOperation<CustomerPriceReason>;
    }
    export interface SalesInvoiceLine$Operations {
        query: QueryOperation<SalesInvoiceLine>;
        read: ReadOperation<SalesInvoiceLine>;
        aggregate: {
            read: AggregateReadOperation<SalesInvoiceLine>;
            query: AggregateQueryOperation<SalesInvoiceLine>;
        };
        mutations: SalesInvoiceLine$Mutations;
        asyncOperations: SalesInvoiceLine$AsyncOperations;
        lookups(dataOrId: string | { data: SalesInvoiceLineInput }): SalesInvoiceLine$Lookups;
        getDefaults: GetDefaultsOperation<SalesInvoiceLine>;
    }
    export interface SalesOrder extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        number: string;
        status: BaseStatus;
        displayStatus: BaseDisplayStatus;
        approvalStatus: ApprovalStatus;
        date: string;
        canPrint: boolean;
        isPrinted: boolean;
        isSent: boolean;
        site: Site;
        companyCurrency: Currency;
        financialSite: Site;
        text: TextStream;
        externalNote: TextStream;
        isExternalNote: boolean;
        isTransferHeaderNote: boolean;
        isTransferLineNote: boolean;
        siteAddress: Address;
        businessEntityAddress: BusinessEntityAddress;
        customerNumber: string;
        isQuote: boolean;
        invoiceStatus: SalesDocumentInvoiceStatus;
        orderDate: string;
        taxEngine: TaxEngine;
        soldToCustomer: Customer;
        soldToLinkedAddress: BusinessEntityAddress;
        soldToAddress: Address;
        soldToContact: Contact;
        fxRateDate: string;
        requestedDeliveryDate: string;
        doNotShipBeforeDate: string;
        doNotShipAfterDate: string;
        shippingStatus: SalesDocumentShippingStatus;
        salesSite: Site;
        shipToCustomer: Customer;
        shipToCustomerAddress: BusinessEntityAddress;
        shipToAddress: Address;
        shipToContact: Contact;
        billToCustomer: Customer;
        billToLinkedAddress: BusinessEntityAddress;
        billToAddress: Address;
        billToContact: Contact;
        incoterm: Incoterm;
        deliveryMode: DeliveryMode;
        deliveryLeadTime: integer;
        isOrderAssignmentLinked: boolean;
        taxCalculationStatus: TaxCalculationStatus;
        recordUrl: string;
        proformaInvoices: ClientCollection<ProformaInvoice>;
        financeIntegrationStatus: FinanceIntegrationStatus;
        _attachments: ClientCollection<AttachmentAssociation>;
        stockSite: Site;
        currency: Currency;
        transactionCurrency: Currency;
        internalNote: TextStream;
        companyFxRate: string;
        companyFxRateDivisor: string;
        paymentTerm: PaymentTerm;
        isOnHold: boolean;
        workDays: integer;
        totalTaxAmount: string;
        totalTaxableAmount: string;
        totalExemptAmount: string;
        totalTaxAmountAdjusted: string;
        taxes: ClientCollection<SalesOrderTax>;
        lines: ClientCollection<SalesOrderLine>;
        rateDescription: string;
        shippingDate: string;
        expectedDeliveryDate: string;
        totalAmountExcludingTax: string;
        remainingTotalAmountToShipExcludingTax: string;
        remainingTotalAmountExcludingTaxInCompanyCurrency: string;
        totalAmountIncludingTax: string;
        totalAmountIncludingTaxInCompanyCurrency: string;
        totalAmountExcludingTaxInCompanyCurrency: string;
        allocationStatus: StockAllocationStatus;
        allocationRequestStatus: AllocationRequestStatus;
        totalGrossProfit: string;
        isCloseHidden: boolean;
    }
    export interface SalesOrderInput extends ClientNodeInput {
        number?: string;
        status?: BaseStatus;
        displayStatus?: BaseDisplayStatus;
        approvalStatus?: ApprovalStatus;
        date?: string;
        isPrinted?: boolean | string;
        isSent?: boolean | string;
        site?: integer | string;
        financialSite?: integer | string;
        text?: TextStream;
        externalNote?: TextStream;
        isExternalNote?: boolean | string;
        isOverwriteNote?: boolean | string;
        isTransferHeaderNote?: boolean | string;
        isTransferLineNote?: boolean | string;
        siteAddress?: integer | string;
        businessEntityAddress?: integer | string;
        customerNumber?: string;
        isQuote?: boolean | string;
        invoiceStatus?: SalesDocumentInvoiceStatus;
        soldToCustomer?: integer | string;
        soldToLinkedAddress?: integer | string;
        soldToAddress?: integer | string;
        soldToContact?: integer | string;
        fxRateDate?: string;
        requestedDeliveryDate?: string;
        doNotShipBeforeDate?: string;
        doNotShipAfterDate?: string;
        shippingStatus?: SalesDocumentShippingStatus;
        shipToCustomer?: integer | string;
        shipToCustomerAddress?: integer | string;
        shipToAddress?: integer | string;
        shipToContact?: integer | string;
        billToCustomer?: integer | string;
        billToLinkedAddress?: integer | string;
        billToAddress?: integer | string;
        billToContact?: integer | string;
        incoterm?: integer | string;
        deliveryMode?: integer | string;
        deliveryLeadTime?: integer | string;
        taxCalculationStatus?: TaxCalculationStatus;
        _attachments?: Partial<AttachmentAssociationInput>[];
        stockSite?: integer | string;
        currency?: integer | string;
        internalNote?: TextStream;
        companyFxRate?: decimal | string;
        companyFxRateDivisor?: decimal | string;
        paymentTerm?: integer | string;
        totalTaxAmount?: decimal | string;
        totalTaxableAmount?: decimal | string;
        totalExemptAmount?: decimal | string;
        totalTaxAmountAdjusted?: decimal | string;
        taxes?: Partial<SalesOrderTaxInput>[];
        lines?: Partial<SalesOrderLineInput>[];
        shippingDate?: string;
        expectedDeliveryDate?: string;
    }
    export interface SalesOrderBinding extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        number: string;
        status: BaseStatus;
        displayStatus: BaseDisplayStatus;
        approvalStatus: ApprovalStatus;
        date: string;
        canPrint: boolean;
        isPrinted: boolean;
        isSent: boolean;
        site: Site;
        companyCurrency: Currency;
        financialSite: Site;
        text: TextStream;
        externalNote: TextStream;
        isExternalNote: boolean;
        isOverwriteNote: boolean;
        isTransferHeaderNote: boolean;
        isTransferLineNote: boolean;
        siteAddress: Address;
        businessEntityAddress: BusinessEntityAddress;
        customerNumber: string;
        isQuote: boolean;
        invoiceStatus: SalesDocumentInvoiceStatus;
        orderDate: string;
        taxEngine: TaxEngine;
        soldToCustomer: Customer;
        soldToLinkedAddress: BusinessEntityAddress;
        soldToAddress: Address;
        soldToContact: Contact;
        fxRateDate: string;
        requestedDeliveryDate: string;
        doNotShipBeforeDate: string;
        doNotShipAfterDate: string;
        shippingStatus: SalesDocumentShippingStatus;
        salesSite: Site;
        shipToCustomer: Customer;
        shipToCustomerAddress: BusinessEntityAddress;
        shipToAddress: Address;
        shipToContact: Contact;
        billToCustomer: Customer;
        billToLinkedAddress: BusinessEntityAddress;
        billToAddress: Address;
        billToContact: Contact;
        incoterm: Incoterm;
        deliveryMode: DeliveryMode;
        deliveryLeadTime: integer;
        isOrderAssignmentLinked: boolean;
        taxCalculationStatus: TaxCalculationStatus;
        recordUrl: string;
        proformaInvoices: ClientCollection<ProformaInvoice>;
        financeIntegrationStatus: FinanceIntegrationStatus;
        _attachments: ClientCollection<AttachmentAssociation>;
        stockSite: Site;
        currency: Currency;
        transactionCurrency: Currency;
        internalNote: TextStream;
        companyFxRate: string;
        companyFxRateDivisor: string;
        paymentTerm: PaymentTerm;
        isOnHold: boolean;
        workDays: integer;
        totalTaxAmount: string;
        totalTaxableAmount: string;
        totalExemptAmount: string;
        totalTaxAmountAdjusted: string;
        taxes: ClientCollection<SalesOrderTaxBinding>;
        lines: ClientCollection<SalesOrderLineBinding>;
        rateDescription: string;
        shippingDate: string;
        expectedDeliveryDate: string;
        totalAmountExcludingTax: string;
        remainingTotalAmountToShipExcludingTax: string;
        remainingTotalAmountExcludingTaxInCompanyCurrency: string;
        totalAmountIncludingTax: string;
        totalAmountIncludingTaxInCompanyCurrency: string;
        totalAmountExcludingTaxInCompanyCurrency: string;
        allocationStatus: StockAllocationStatus;
        allocationRequestStatus: AllocationRequestStatus;
        totalGrossProfit: string;
        isCloseHidden: boolean;
    }
    export interface SalesOrder$Queries {
        orderAssignmentQuantitySum: Node$Operation<
            {
                salesOrder: string;
            },
            {
                sumActualQuantity: string;
                sumQuantityInStockUnit: string;
                filteredAssignedOrders: {
                    line: SalesOrderLine;
                    orderAssignments: OrderAssignment[] | null;
                }[];
            }
        >;
        getLastOrderDate: Node$Operation<
            {
                customer: string;
            },
            string
        >;
        getOrderBookAmount: Node$Operation<
            {
                customer: string;
            },
            {
                orderBookTotal: string;
                currencySymbol: string;
                decimalDigits: integer;
            }
        >;
    }
    export interface SalesOrder$Mutations {
        createSalesShipmentsFromOrderLines: Node$Operation<
            {
                salesDocumentLines?: {
                    salesDocumentLine?: integer | string;
                    quantityToProcess?: decimal | string;
                }[];
                processOptions?: {
                    isForFinanceCheck?: boolean | string;
                    processAllShippableLines?: boolean | string;
                };
            },
            {
                status: string;
                numberOfShipments: integer;
                documentsCreated: SalesShipment[];
                lineErrors: {
                    lineNumber: integer;
                    linePosition: integer;
                    message: string;
                }[];
            }
        >;
        createSalesShipmentsFromOrders: Node$Operation<
            {
                salesDocuments?: string[];
                processOptions?: {
                    isForFinanceCheck?: boolean | string;
                    processAllShippableLines?: boolean | string;
                };
            },
            {
                status: string;
                numberOfShipments: integer;
                documentsCreated: SalesShipment[];
                lineErrors: {
                    lineNumber: integer;
                    linePosition: integer;
                    message: string;
                }[];
            }
        >;
        createShipmentsFromOrder: Node$Operation<
            {
                salesOrder: string;
                isSafeToRetry?: boolean | string;
                controlOrderLinks?: boolean | string;
            },
            SalesShipment[]
        >;
        setSalesOrderLineCloseStatus: Node$Operation<
            {
                salesOrderLine: string;
            },
            SalesOrderLineCloseStatusMethodReturn
        >;
        setSalesOrderLineOpenStatus: Node$Operation<
            {
                salesOrderLine: string;
                newQuantityInSalesUnit?: decimal | string;
            },
            SalesOrderLineOpenStatusMethodReturn
        >;
        closeSalesOrder: Node$Operation<
            {
                salesOrder: string;
                isSafeToRetry?: boolean | string;
                controlOrderLinks?: boolean | string;
            },
            SalesOrder
        >;
        setSalesOrderOpenStatus: Node$Operation<
            {
                salesOrderNumber?: string;
            },
            SalesOrderOpenStatusMethodReturn
        >;
        confirmSalesOrder: Node$Operation<
            {
                salesOrder: string;
                isSafeToRetry?: boolean | string;
            },
            SalesOrder
        >;
        subWorkDays: Node$Operation<
            {
                requestedDeliveryDate: string;
                orderDate: string;
                doNotShipBeforeDate?: string;
                doNotShipAfterDate?: string;
                deliveryLeadTime: integer | string;
                workDaysMask: integer | string;
            },
            string
        >;
        addWorkDays: Node$Operation<
            {
                shippingDate: string;
                deliveryLeadTime: integer | string;
                workDays: integer | string;
            },
            string
        >;
        massPriceDeterminationCalculation: Node$Operation<
            {
                salesDocument: string;
            },
            boolean
        >;
        setIsPrintedTrue: Node$Operation<
            {
                order: string;
                isPrinted?: boolean | string;
            },
            boolean
        >;
        printSalesOrderAndEmail: Node$Operation<
            {
                salesOrder: string;
                contactTitle: string;
                contactLastName: string;
                contactFirstName: string;
                contactEmail: string;
            },
            boolean
        >;
        massCreateSalesShipments: Node$Operation<
            {
                stockSite: string;
                fromSoldToCustomer?: string;
                toSoldToCustomer?: string;
                fromOrder?: string;
                toOrder?: string;
                shippingUntilDate?: string | null;
                incoterm?: string | null;
                deliveryMode?: string | null;
            },
            {
                numberOfShipments: integer;
            }
        >;
        beforePrintSalesOrder: Node$Operation<
            {
                order: string;
            },
            boolean
        >;
        afterPrintSalesOrder: Node$Operation<
            {
                order: string;
            },
            boolean
        >;
        requestAutoAllocation: Node$Operation<
            {
                salesOrder: string;
                requestType: AllocationRequestType;
                isSafeToRetry?: boolean | string;
            },
            string
        >;
        financeIntegrationCheck: Node$Operation<
            {
                salesOrder: string;
            },
            {
                wasSuccessful: boolean;
                message: string;
            }
        >;
        sendApprovalRequestMail: Node$Operation<
            {
                document?: string;
                user?: UserInput | null;
            },
            boolean
        >;
    }
    export interface SalesOrder$AsyncOperations {
        createTestSalesOrders: AsyncOperation<
            {
                orderCreation?: {
                    customerId: string;
                    item: integer | string;
                    itemQuantity: integer | string;
                    orderQuantity: integer | string;
                    numberOfLinesPerOrder: integer | string;
                    orderNumberRoot?: string;
                    fixedNumberOfLines?: boolean | string;
                    allocateStock?: boolean | string;
                    randomDeliveryDate?: boolean | string;
                };
            },
            string
        >;
        printBulkSalesOrders: AsyncOperation<
            {
                filter?: string;
            },
            boolean
        >;
        autoAllocate: AsyncOperation<
            {
                salesOrder: string;
                requestType?: string;
                isSafeToRetry?: boolean | string;
            },
            SalesOrder
        >;
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
        bulkResync: AsyncOperation<
            {
                filter?: string;
            },
            boolean
        >;
    }
    export interface SalesOrder$Lookups {
        site: QueryOperation<Site>;
        companyCurrency: QueryOperation<Currency>;
        financialSite: QueryOperation<Site>;
        siteAddress: QueryOperation<Address>;
        businessEntityAddress: QueryOperation<BusinessEntityAddress>;
        soldToCustomer: QueryOperation<Customer>;
        soldToLinkedAddress: QueryOperation<BusinessEntityAddress>;
        soldToAddress: QueryOperation<Address>;
        soldToContact: QueryOperation<Contact>;
        salesSite: QueryOperation<Site>;
        shipToCustomer: QueryOperation<Customer>;
        shipToCustomerAddress: QueryOperation<BusinessEntityAddress>;
        shipToAddress: QueryOperation<Address>;
        shipToContact: QueryOperation<Contact>;
        billToCustomer: QueryOperation<Customer>;
        billToLinkedAddress: QueryOperation<BusinessEntityAddress>;
        billToAddress: QueryOperation<Address>;
        billToContact: QueryOperation<Contact>;
        incoterm: QueryOperation<Incoterm>;
        deliveryMode: QueryOperation<DeliveryMode>;
        stockSite: QueryOperation<Site>;
        currency: QueryOperation<Currency>;
        transactionCurrency: QueryOperation<Currency>;
        paymentTerm: QueryOperation<PaymentTerm>;
    }
    export interface SalesOrder$Operations {
        query: QueryOperation<SalesOrder>;
        read: ReadOperation<SalesOrder>;
        aggregate: {
            read: AggregateReadOperation<SalesOrder>;
            query: AggregateQueryOperation<SalesOrder>;
        };
        queries: SalesOrder$Queries;
        create: CreateOperation<SalesOrderInput, SalesOrder>;
        getDuplicate: GetDuplicateOperation<SalesOrder>;
        duplicate: DuplicateOperation<string, SalesOrderInput, SalesOrder>;
        update: UpdateOperation<SalesOrderInput, SalesOrder>;
        updateById: UpdateByIdOperation<SalesOrderInput, SalesOrder>;
        delete: DeleteOperation<{}>;
        deleteById: DeleteOperation<string>;
        mutations: SalesOrder$Mutations;
        asyncOperations: SalesOrder$AsyncOperations;
        lookups(dataOrId: string | { data: SalesOrderInput }): SalesOrder$Lookups;
        getDefaults: GetDefaultsOperation<SalesOrder>;
    }
    export interface SalesOrderLine extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        documentNumber: string;
        documentId: integer;
        document: SalesOrder;
        origin: BaseOrigin;
        status: BaseStatus;
        site: Site;
        siteLinkedAddress: BusinessEntityAddress;
        item: Item;
        itemDescription: string;
        itemSite: ItemSite;
        stockUnit: UnitOfMeasure;
        unit: UnitOfMeasure;
        quantity: string;
        internalNote: TextStream;
        externalNote: TextStream;
        isExternalNote: boolean;
        originDocumentType: SalesOriginDocumentType;
        invoiceStatus: SalesDocumentInvoiceStatus;
        shippingStatus: SalesDocumentShippingStatus;
        salesSite: Site;
        shipToCustomerAddress: BusinessEntityAddress;
        shipToAddress: Address;
        shipToContact: Contact;
        quantityInSalesUnit: string;
        salesUnit: UnitOfMeasure;
        salesUnitToStockUnitConversionFactor: string;
        deliveryMode: DeliveryMode;
        requestedDeliveryDate: string;
        deliveryLeadTime: integer;
        doNotShipBeforeDate: string;
        doNotShipAfterDate: string;
        shippingDate: string;
        expectedDeliveryDate: string;
        taxZone: TaxZone;
        discountCharges: ClientCollection<SalesOrderLineDiscountCharge>;
        grossPrice: string;
        discountDeterminated: string;
        chargeDeterminated: string;
        grossPriceDeterminated: string;
        taxableAmount: string;
        taxAmount: string;
        exemptAmount: string;
        taxDate: string;
        availableQuantityInStockUnit: string;
        stockOnHand: string;
        salesShipmentLines: ClientCollection<SalesOrderLineToSalesShipmentLine>;
        quantityToShipInProgressInSalesUnit: string;
        shippedQuantityInSalesUnit: string;
        remainingQuantityToShipInSalesUnit: string;
        quantityToInvoiceInProgressInSalesUnit: string;
        invoicedQuantityInSalesUnit: string;
        remainingQuantityToInvoiceInSalesUnit: string;
        priceOrigin: SalesPriceOrigin;
        priceReason: CustomerPriceReason;
        priceOriginDeterminated: SalesPriceOrigin;
        priceReasonDeterminated: CustomerPriceReason;
        isPriceDeterminated: boolean;
        taxes: ClientCollection<SalesOrderLineTax>;
        uiTaxes: string;
        taxCalculationStatus: TaxCalculationStatus;
        workInProgress: WorkInProgressSalesOrderLine;
        stockAllocations: ClientCollection<StockAllocation>;
        allocationRequestStatus: AllocationRequestStatus;
        stockDetails: ClientCollection<StockIssueDetail>;
        assignments: ClientCollection<OrderAssignment>;
        uWorkOrderLine: BaseDocumentLine;
        uPurchaseOrderLine: BaseDocumentLine;
        assignedQuantity: string;
        uAssignmentOrder: string;
        dimensionDefinitionLevel: DimensionDefinitionLevel;
        storedDimensions: string;
        storedAttributes: string;
        computedAttributes: string;
        analyticalData: AnalyticalData;
        unitToStockUnitConversionFactor: string;
        discount: string;
        charge: string;
        netPrice: string;
        netPriceExcludingTax: string;
        netPriceIncludingTax: string;
        taxAmountAdjusted: string;
        amountExcludingTax: string;
        amountExcludingTaxInCompanyCurrency: string;
        remainingAmountToShipExcludingTax: string;
        remainingAmountToShipExcludingTaxInCompanyCurrency: string;
        availableQuantityInSalesUnit: string;
        suppliedQuantity: string;
        quantityAllocated: string;
        remainingQuantityToShipInStockUnit: string;
        remainingQuantityToAllocate: string;
        quantityInStockUnit: string;
        amountIncludingTax: string;
        amountIncludingTaxInCompanyCurrency: string;
        stockShortageInStockUnit: string;
        stockShortageInSalesUnit: string;
        stockShortageStatus: boolean;
        allocationStatus: StockAllocationStatus;
        stockSite: Site;
        stockSiteLinkedAddress: BusinessEntityAddress;
        stockSiteAddress: Address;
        stockCostAmountInCompanyCurrency: string;
        grossProfitAmountInCompanyCurrency: string;
        stockCostAmount: string;
        grossProfitAmount: string;
        stockCostUnit: string;
        grossProfit: string;
    }
    export interface SalesOrderLineInput extends VitalClientNodeInput {
        origin?: BaseOrigin;
        status?: BaseStatus;
        site?: integer | string;
        siteLinkedAddress?: integer | string;
        item?: integer | string;
        itemDescription?: string;
        stockUnit?: integer | string;
        unit?: integer | string;
        quantity?: decimal | string;
        internalNote?: TextStream;
        externalNote?: TextStream;
        isExternalNote?: boolean | string;
        invoiceStatus?: SalesDocumentInvoiceStatus;
        shippingStatus?: SalesDocumentShippingStatus;
        shipToCustomerAddress?: integer | string;
        shipToAddress?: integer | string;
        shipToContact?: integer | string;
        deliveryMode?: integer | string;
        requestedDeliveryDate?: string;
        deliveryLeadTime?: integer | string;
        doNotShipBeforeDate?: string;
        doNotShipAfterDate?: string;
        shippingDate?: string;
        expectedDeliveryDate?: string;
        discountCharges?: Partial<SalesOrderLineDiscountChargeInput>[];
        grossPrice?: decimal | string;
        discountDeterminated?: decimal | string;
        chargeDeterminated?: decimal | string;
        grossPriceDeterminated?: decimal | string;
        taxableAmount?: decimal | string;
        taxAmount?: decimal | string;
        exemptAmount?: decimal | string;
        priceOrigin?: SalesPriceOrigin;
        priceReason?: integer | string;
        priceOriginDeterminated?: SalesPriceOrigin;
        priceReasonDeterminated?: integer | string;
        isPriceDeterminated?: boolean | string;
        taxes?: Partial<SalesOrderLineTaxInput>[];
        uiTaxes?: string;
        workInProgress?: WorkInProgressSalesOrderLineInput;
        allocationRequestStatus?: AllocationRequestStatus;
        stockDetails?: Partial<StockIssueDetailInput>[];
        storedDimensions?: string;
        storedAttributes?: string;
        analyticalData?: integer | string;
        unitToStockUnitConversionFactor?: decimal | string;
        discount?: decimal | string;
        charge?: decimal | string;
        netPrice?: decimal | string;
        netPriceExcludingTax?: decimal | string;
        netPriceIncludingTax?: decimal | string;
        taxAmountAdjusted?: decimal | string;
        amountExcludingTaxInCompanyCurrency?: decimal | string;
        quantityInStockUnit?: decimal | string;
        amountIncludingTax?: decimal | string;
        amountIncludingTaxInCompanyCurrency?: decimal | string;
        stockSite?: integer | string;
        stockSiteLinkedAddress?: integer | string;
        stockSiteAddress?: integer | string;
    }
    export interface SalesOrderLineBinding extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        documentNumber: string;
        documentId: integer;
        document: SalesOrder;
        origin: BaseOrigin;
        status: BaseStatus;
        site: Site;
        siteLinkedAddress: BusinessEntityAddress;
        item: Item;
        itemDescription: string;
        itemSite: ItemSite;
        stockUnit: UnitOfMeasure;
        unit: UnitOfMeasure;
        quantity: string;
        internalNote: TextStream;
        externalNote: TextStream;
        isExternalNote: boolean;
        originDocumentType: SalesOriginDocumentType;
        invoiceStatus: SalesDocumentInvoiceStatus;
        shippingStatus: SalesDocumentShippingStatus;
        salesSite: Site;
        shipToCustomerAddress: BusinessEntityAddress;
        shipToAddress: Address;
        shipToContact: Contact;
        quantityInSalesUnit: string;
        salesUnit: UnitOfMeasure;
        salesUnitToStockUnitConversionFactor: string;
        deliveryMode: DeliveryMode;
        requestedDeliveryDate: string;
        deliveryLeadTime: integer;
        doNotShipBeforeDate: string;
        doNotShipAfterDate: string;
        shippingDate: string;
        expectedDeliveryDate: string;
        taxZone: TaxZone;
        discountCharges: ClientCollection<SalesOrderLineDiscountChargeBinding>;
        grossPrice: string;
        discountDeterminated: string;
        chargeDeterminated: string;
        grossPriceDeterminated: string;
        taxableAmount: string;
        taxAmount: string;
        exemptAmount: string;
        taxDate: string;
        availableQuantityInStockUnit: string;
        stockOnHand: string;
        salesShipmentLines: ClientCollection<SalesOrderLineToSalesShipmentLine>;
        quantityToShipInProgressInSalesUnit: string;
        shippedQuantityInSalesUnit: string;
        remainingQuantityToShipInSalesUnit: string;
        quantityToInvoiceInProgressInSalesUnit: string;
        invoicedQuantityInSalesUnit: string;
        remainingQuantityToInvoiceInSalesUnit: string;
        priceOrigin: SalesPriceOrigin;
        priceReason: CustomerPriceReason;
        priceOriginDeterminated: SalesPriceOrigin;
        priceReasonDeterminated: CustomerPriceReason;
        isPriceDeterminated: boolean;
        taxes: ClientCollection<SalesOrderLineTaxBinding>;
        uiTaxes: any;
        taxCalculationStatus: TaxCalculationStatus;
        workInProgress: WorkInProgressSalesOrderLineBinding;
        stockAllocations: ClientCollection<StockAllocation>;
        allocationRequestStatus: AllocationRequestStatus;
        stockDetails: ClientCollection<StockIssueDetailBinding>;
        assignments: ClientCollection<OrderAssignment>;
        uWorkOrderLine: BaseDocumentLine;
        uPurchaseOrderLine: BaseDocumentLine;
        assignedQuantity: string;
        uAssignmentOrder: string;
        dimensionDefinitionLevel: DimensionDefinitionLevel;
        storedDimensions: any;
        storedAttributes: any;
        computedAttributes: any;
        analyticalData: AnalyticalData;
        unitToStockUnitConversionFactor: string;
        discount: string;
        charge: string;
        netPrice: string;
        netPriceExcludingTax: string;
        netPriceIncludingTax: string;
        taxAmountAdjusted: string;
        amountExcludingTax: string;
        amountExcludingTaxInCompanyCurrency: string;
        remainingAmountToShipExcludingTax: string;
        remainingAmountToShipExcludingTaxInCompanyCurrency: string;
        availableQuantityInSalesUnit: string;
        suppliedQuantity: string;
        quantityAllocated: string;
        remainingQuantityToShipInStockUnit: string;
        remainingQuantityToAllocate: string;
        quantityInStockUnit: string;
        amountIncludingTax: string;
        amountIncludingTaxInCompanyCurrency: string;
        stockShortageInStockUnit: string;
        stockShortageInSalesUnit: string;
        stockShortageStatus: boolean;
        allocationStatus: StockAllocationStatus;
        stockSite: Site;
        stockSiteLinkedAddress: BusinessEntityAddress;
        stockSiteAddress: Address;
        stockCostAmountInCompanyCurrency: string;
        grossProfitAmountInCompanyCurrency: string;
        stockCostAmount: string;
        grossProfitAmount: string;
        stockCostUnit: string;
        grossProfit: string;
    }
    export interface SalesOrderLine$Queries {
        assignedOrderAssignment: Node$Operation<
            {
                salesOrderLine: string;
                supplyType?: OrderToOrderDemandType;
                preferredProcess?: PreferredProcess;
            },
            {
                line: SalesOrderLine;
                orderAssignments: OrderAssignment[] | null;
            }
        >;
    }
    export interface SalesOrderLine$Mutations {
        calculateLineTaxes: Node$Operation<
            {
                data?: {
                    site: integer | string;
                    businessPartner: integer | string;
                    addresses?: {
                        consumerCountry?: integer | string;
                        consumerPostcode?: string;
                        shipToCustomerAddress?: integer | string;
                    };
                    item: integer | string;
                    currency: integer | string;
                    amountExcludingTax: decimal | string;
                    quantity: decimal | string;
                    taxes?: {
                        taxReference?: integer | string;
                        taxCategoryReference?: integer | string;
                        isTaxMandatory: boolean | string;
                        isSubjectToGlTaxExcludedAmount: boolean | string;
                        _sortValue: integer | string;
                    }[];
                    taxDate?: string;
                };
            },
            {
                taxes: {
                    taxCategoryReference: TaxCategory;
                    taxCategory: string;
                    taxRate: string;
                    taxAmount: string;
                    taxReference: Tax;
                    tax: string;
                    deductibleTaxAmount: string;
                    deductibleTaxRate: string;
                    exemptAmount: string;
                    isReverseCharge: boolean;
                    nonTaxableAmount: string;
                    taxAmountAdjusted: string;
                    taxableAmount: string;
                    currency: Currency;
                    isTaxMandatory: boolean;
                    isSubjectToGlTaxExcludedAmount: boolean;
                    _sortValue: integer;
                }[];
                taxAmount: string;
                taxAmountAdjusted: string;
                amountIncludingTax: string;
            }
        >;
        setDimension: Node$Operation<
            {
                baseDocumentItemLine: string;
                storedDimensions?: string;
                storedAttributes?: string;
            },
            boolean
        >;
    }
    export interface SalesOrderLine$AsyncOperations {
        massAutoAllocation: AsyncOperation<
            {
                filter?: string;
                data?: {
                    requestType: AllocationRequestType;
                    requestDescription?: string;
                    userEntries?: {
                        lineId: integer | string;
                        quantityToProcess: decimal | string;
                    }[];
                };
                stockAllocationParameters?: {
                    cannotOverAllocate?: boolean | string;
                    shouldControlAllocationInProgress?: boolean | string;
                };
            },
            boolean
        >;
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface SalesOrderLine$Lookups {
        site: QueryOperation<Site>;
        siteLinkedAddress: QueryOperation<BusinessEntityAddress>;
        item: QueryOperation<Item>;
        itemSite: QueryOperation<ItemSite>;
        stockUnit: QueryOperation<UnitOfMeasure>;
        unit: QueryOperation<UnitOfMeasure>;
        salesSite: QueryOperation<Site>;
        shipToCustomerAddress: QueryOperation<BusinessEntityAddress>;
        shipToAddress: QueryOperation<Address>;
        shipToContact: QueryOperation<Contact>;
        salesUnit: QueryOperation<UnitOfMeasure>;
        deliveryMode: QueryOperation<DeliveryMode>;
        taxZone: QueryOperation<TaxZone>;
        priceReason: QueryOperation<CustomerPriceReason>;
        priceReasonDeterminated: QueryOperation<CustomerPriceReason>;
        uWorkOrderLine: QueryOperation<BaseDocumentLine>;
        uPurchaseOrderLine: QueryOperation<BaseDocumentLine>;
        stockSite: QueryOperation<Site>;
        stockSiteLinkedAddress: QueryOperation<BusinessEntityAddress>;
        stockSiteAddress: QueryOperation<Address>;
    }
    export interface SalesOrderLine$Operations {
        query: QueryOperation<SalesOrderLine>;
        read: ReadOperation<SalesOrderLine>;
        aggregate: {
            read: AggregateReadOperation<SalesOrderLine>;
            query: AggregateQueryOperation<SalesOrderLine>;
        };
        queries: SalesOrderLine$Queries;
        mutations: SalesOrderLine$Mutations;
        asyncOperations: SalesOrderLine$AsyncOperations;
        lookups(dataOrId: string | { data: SalesOrderLineInput }): SalesOrderLine$Lookups;
        getDefaults: GetDefaultsOperation<SalesOrderLine>;
    }
    export interface SalesReturnReceipt extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        number: string;
        approvalStatus: ApprovalStatus;
        date: string;
        canPrint: boolean;
        isPrinted: boolean;
        isSent: boolean;
        site: Site;
        stockSite: Site;
        currency: Currency;
        companyCurrency: Currency;
        financialSite: Site;
        text: TextStream;
        internalNote: TextStream;
        externalNote: TextStream;
        isExternalNote: boolean;
        isTransferHeaderNote: boolean;
        isTransferLineNote: boolean;
        siteAddress: Address;
        businessEntityAddress: BusinessEntityAddress;
        returnDate: string;
        effectiveDate: string;
        shipToCustomer: Customer;
        shipToCustomerAddress: BusinessEntityAddress;
        shipToAddress: Address;
        shipToContact: Contact;
        postingDetails: ClientCollection<FinanceTransaction>;
        financeIntegrationStatus: FinanceIntegrationStatus;
        _attachments: ClientCollection<AttachmentAssociation>;
        transactionCurrency: Currency;
        lines: ClientCollection<SalesReturnReceiptLine>;
        stockTransactionStatus: StockDocumentTransactionStatus;
        stockDetailStatus: StockDetailStatus;
        status: BaseStatus;
        displayStatus: BaseDisplayStatus;
    }
    export interface SalesReturnReceiptInput extends ClientNodeInput {
        number?: string;
        approvalStatus?: ApprovalStatus;
        date?: string;
        isPrinted?: boolean | string;
        isSent?: boolean | string;
        site?: integer | string;
        stockSite?: integer | string;
        currency?: integer | string;
        financialSite?: integer | string;
        text?: TextStream;
        internalNote?: TextStream;
        externalNote?: TextStream;
        isExternalNote?: boolean | string;
        isOverwriteNote?: boolean | string;
        isTransferHeaderNote?: boolean | string;
        isTransferLineNote?: boolean | string;
        siteAddress?: integer | string;
        businessEntityAddress?: integer | string;
        shipToCustomer?: integer | string;
        shipToCustomerAddress?: integer | string;
        shipToAddress?: integer | string;
        shipToContact?: integer | string;
        _attachments?: Partial<AttachmentAssociationInput>[];
        lines?: Partial<SalesReturnReceiptLineInput>[];
        status?: BaseStatus;
        displayStatus?: BaseDisplayStatus;
    }
    export interface SalesReturnReceiptBinding extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        number: string;
        approvalStatus: ApprovalStatus;
        date: string;
        canPrint: boolean;
        isPrinted: boolean;
        isSent: boolean;
        site: Site;
        stockSite: Site;
        currency: Currency;
        companyCurrency: Currency;
        financialSite: Site;
        text: TextStream;
        internalNote: TextStream;
        externalNote: TextStream;
        isExternalNote: boolean;
        isOverwriteNote: boolean;
        isTransferHeaderNote: boolean;
        isTransferLineNote: boolean;
        siteAddress: Address;
        businessEntityAddress: BusinessEntityAddress;
        returnDate: string;
        effectiveDate: string;
        shipToCustomer: Customer;
        shipToCustomerAddress: BusinessEntityAddress;
        shipToAddress: Address;
        shipToContact: Contact;
        postingDetails: ClientCollection<FinanceTransaction>;
        financeIntegrationStatus: FinanceIntegrationStatus;
        _attachments: ClientCollection<AttachmentAssociation>;
        transactionCurrency: Currency;
        lines: ClientCollection<SalesReturnReceiptLineBinding>;
        stockTransactionStatus: StockDocumentTransactionStatus;
        stockDetailStatus: StockDetailStatus;
        status: BaseStatus;
        displayStatus: BaseDisplayStatus;
    }
    export interface SalesReturnReceipt$Queries {
        validateQuantityInSalesUnit: Node$Operation<
            {
                newQuantityInSalesUnit?: decimal | string;
                salesReturnReceiptLine?: string;
                sumOfStockDetailQuantity?: decimal | string;
                salesReturnRequestLine?: string;
            },
            {
                hasErrors: boolean;
                errorMessage: string;
            }
        >;
    }
    export interface SalesReturnReceipt$Mutations {
        postToStock: Node$Operation<
            {
                documentIds?: (integer | string)[];
            },
            string
        >;
        repost: Node$Operation<
            {
                salesReturnReceipt: string;
                documentLines?: {
                    baseDocumentLineSysId?: integer | string;
                    storedAttributes?: string;
                    storedDimensions?: string;
                }[];
            },
            {
                wasSuccessful: boolean;
                message: string;
            }
        >;
        resendNotificationForFinance: Node$Operation<
            {
                salesReturnReceipt: string;
            },
            boolean
        >;
        sendApprovalRequestMail: Node$Operation<
            {
                document?: string;
                user?: UserInput | null;
            },
            boolean
        >;
    }
    export interface SalesReturnReceipt$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
        bulkResync: AsyncOperation<
            {
                filter?: string;
            },
            boolean
        >;
    }
    export interface SalesReturnReceipt$Lookups {
        site: QueryOperation<Site>;
        stockSite: QueryOperation<Site>;
        currency: QueryOperation<Currency>;
        companyCurrency: QueryOperation<Currency>;
        financialSite: QueryOperation<Site>;
        siteAddress: QueryOperation<Address>;
        businessEntityAddress: QueryOperation<BusinessEntityAddress>;
        shipToCustomer: QueryOperation<Customer>;
        shipToCustomerAddress: QueryOperation<BusinessEntityAddress>;
        shipToAddress: QueryOperation<Address>;
        shipToContact: QueryOperation<Contact>;
        transactionCurrency: QueryOperation<Currency>;
    }
    export interface SalesReturnReceipt$Operations {
        query: QueryOperation<SalesReturnReceipt>;
        read: ReadOperation<SalesReturnReceipt>;
        aggregate: {
            read: AggregateReadOperation<SalesReturnReceipt>;
            query: AggregateQueryOperation<SalesReturnReceipt>;
        };
        queries: SalesReturnReceipt$Queries;
        create: CreateOperation<SalesReturnReceiptInput, SalesReturnReceipt>;
        getDuplicate: GetDuplicateOperation<SalesReturnReceipt>;
        duplicate: DuplicateOperation<string, SalesReturnReceiptInput, SalesReturnReceipt>;
        update: UpdateOperation<SalesReturnReceiptInput, SalesReturnReceipt>;
        updateById: UpdateByIdOperation<SalesReturnReceiptInput, SalesReturnReceipt>;
        delete: DeleteOperation<{}>;
        deleteById: DeleteOperation<string>;
        mutations: SalesReturnReceipt$Mutations;
        asyncOperations: SalesReturnReceipt$AsyncOperations;
        lookups(dataOrId: string | { data: SalesReturnReceiptInput }): SalesReturnReceipt$Lookups;
        getDefaults: GetDefaultsOperation<SalesReturnReceipt>;
    }
    export interface SalesReturnReceiptLine extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        documentNumber: string;
        documentId: integer;
        document: SalesReturnReceipt;
        status: BaseStatus;
        site: Site;
        siteLinkedAddress: BusinessEntityAddress;
        item: Item;
        itemDescription: string;
        stockSite: Site;
        stockSiteLinkedAddress: BusinessEntityAddress;
        itemSite: ItemSite;
        stockUnit: UnitOfMeasure;
        unit: UnitOfMeasure;
        quantity: string;
        internalNote: TextStream;
        externalNote: TextStream;
        isExternalNote: boolean;
        quantityInSalesUnit: string;
        salesUnit: UnitOfMeasure;
        salesUnitToStockUnitConversionFactor: string;
        originDocumentType: SalesOriginDocumentType;
        orderCost: string;
        valuedCost: string;
        toReturnRequestLines: ClientCollection<SalesReturnRequestLineToSalesReturnReceiptLine>;
        salesShipmentLines: ClientCollection<SalesShipmentLineToSalesReturnReceiptLine>;
        stockDetails: ClientCollection<StockReceiptDetail>;
        jsonStockDetails: string;
        stockMovements: ClientCollection<StockJournal>;
        stockTransactions: ClientCollection<StockTransaction>;
        stockTransactionStatus: StockDocumentTransactionStatus;
        dimensionDefinitionLevel: DimensionDefinitionLevel;
        storedDimensions: string;
        storedAttributes: string;
        computedAttributes: string;
        analyticalData: AnalyticalData;
        origin: BaseOrigin;
        unitToStockUnitConversionFactor: string;
        isReceiptExpected: boolean;
        location: Location;
        stockStatus: StockStatus;
        lot: Lot;
        sourceDocumentType: SourceDocumentType;
        quantityInStockUnit: string;
        stockDetailStatus: StockDetailStatus;
    }
    export interface SalesReturnReceiptLineInput extends VitalClientNodeInput {
        status?: BaseStatus;
        site?: integer | string;
        siteLinkedAddress?: integer | string;
        item?: integer | string;
        itemDescription?: string;
        stockSite?: integer | string;
        stockSiteLinkedAddress?: integer | string;
        stockUnit?: integer | string;
        unit?: integer | string;
        quantity?: decimal | string;
        internalNote?: TextStream;
        externalNote?: TextStream;
        isExternalNote?: boolean | string;
        orderCost?: decimal | string;
        valuedCost?: decimal | string;
        toReturnRequestLines?: Partial<SalesReturnRequestLineToSalesReturnReceiptLineInput>[];
        salesShipmentLines?: Partial<SalesShipmentLineToSalesReturnReceiptLineInput>[];
        stockDetails?: Partial<StockReceiptDetailInput>[];
        jsonStockDetails?: string;
        stockTransactions?: Partial<StockTransactionInput>[];
        stockTransactionStatus?: StockDocumentTransactionStatus;
        storedDimensions?: string;
        storedAttributes?: string;
        analyticalData?: integer | string;
        origin?: BaseOrigin;
        unitToStockUnitConversionFactor?: decimal | string;
        quantityInStockUnit?: decimal | string;
        stockDetailStatus?: StockDetailStatus;
    }
    export interface SalesReturnReceiptLineBinding extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        documentNumber: string;
        documentId: integer;
        document: SalesReturnReceipt;
        status: BaseStatus;
        site: Site;
        siteLinkedAddress: BusinessEntityAddress;
        item: Item;
        itemDescription: string;
        stockSite: Site;
        stockSiteLinkedAddress: BusinessEntityAddress;
        itemSite: ItemSite;
        stockUnit: UnitOfMeasure;
        unit: UnitOfMeasure;
        quantity: string;
        internalNote: TextStream;
        externalNote: TextStream;
        isExternalNote: boolean;
        quantityInSalesUnit: string;
        salesUnit: UnitOfMeasure;
        salesUnitToStockUnitConversionFactor: string;
        originDocumentType: SalesOriginDocumentType;
        orderCost: string;
        valuedCost: string;
        toReturnRequestLines: ClientCollection<SalesReturnRequestLineToSalesReturnReceiptLineBinding>;
        salesShipmentLines: ClientCollection<SalesShipmentLineToSalesReturnReceiptLineBinding>;
        stockDetails: ClientCollection<StockReceiptDetailBinding>;
        jsonStockDetails: any;
        stockMovements: ClientCollection<StockJournal>;
        stockTransactions: ClientCollection<StockTransactionBinding>;
        stockTransactionStatus: StockDocumentTransactionStatus;
        dimensionDefinitionLevel: DimensionDefinitionLevel;
        storedDimensions: any;
        storedAttributes: any;
        computedAttributes: any;
        analyticalData: AnalyticalData;
        origin: BaseOrigin;
        unitToStockUnitConversionFactor: string;
        isReceiptExpected: boolean;
        location: Location;
        stockStatus: StockStatus;
        lot: Lot;
        sourceDocumentType: SourceDocumentType;
        quantityInStockUnit: string;
        stockDetailStatus: StockDetailStatus;
    }
    export interface SalesReturnReceiptLine$Mutations {
        setDimension: Node$Operation<
            {
                baseDocumentItemLine: string;
                storedDimensions?: string;
                storedAttributes?: string;
            },
            boolean
        >;
    }
    export interface SalesReturnReceiptLine$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface SalesReturnReceiptLine$Lookups {
        site: QueryOperation<Site>;
        siteLinkedAddress: QueryOperation<BusinessEntityAddress>;
        item: QueryOperation<Item>;
        stockSite: QueryOperation<Site>;
        stockSiteLinkedAddress: QueryOperation<BusinessEntityAddress>;
        itemSite: QueryOperation<ItemSite>;
        stockUnit: QueryOperation<UnitOfMeasure>;
        unit: QueryOperation<UnitOfMeasure>;
        salesUnit: QueryOperation<UnitOfMeasure>;
        location: QueryOperation<Location>;
        stockStatus: QueryOperation<StockStatus>;
        lot: QueryOperation<Lot>;
    }
    export interface SalesReturnReceiptLine$Operations {
        query: QueryOperation<SalesReturnReceiptLine>;
        read: ReadOperation<SalesReturnReceiptLine>;
        aggregate: {
            read: AggregateReadOperation<SalesReturnReceiptLine>;
            query: AggregateQueryOperation<SalesReturnReceiptLine>;
        };
        mutations: SalesReturnReceiptLine$Mutations;
        asyncOperations: SalesReturnReceiptLine$AsyncOperations;
        lookups(dataOrId: string | { data: SalesReturnReceiptLineInput }): SalesReturnReceiptLine$Lookups;
        getDefaults: GetDefaultsOperation<SalesReturnReceiptLine>;
    }
    export interface SalesReturnRequest extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        number: string;
        status: BaseStatus;
        approvalStatus: ApprovalStatus;
        date: string;
        canPrint: boolean;
        isPrinted: boolean;
        isSent: boolean;
        site: Site;
        currency: Currency;
        transactionCurrency: Currency;
        companyCurrency: Currency;
        financialSite: Site;
        text: TextStream;
        internalNote: TextStream;
        externalNote: TextStream;
        isExternalNote: boolean;
        isTransferHeaderNote: boolean;
        isTransferLineNote: boolean;
        siteAddress: Address;
        businessEntityAddress: BusinessEntityAddress;
        receiptStatus: SalesDocumentReturnRequestReceiptStatus;
        creditStatus: SalesDocumentCreditStatus;
        salesSite: Site;
        requester: User;
        returnRequestDate: string;
        soldToCustomer: Customer;
        soldToLinkedAddress: BusinessEntityAddress;
        soldToAddress: Address;
        soldToContact: Contact;
        shipToCustomer: Customer;
        shipToCustomerAddress: BusinessEntityAddress;
        shipToAddress: Address;
        shipToContact: Contact;
        billToCustomer: Customer;
        billToLinkedAddress: BusinessEntityAddress;
        billToAddress: Address;
        billToContact: Contact;
        deliveryMode: DeliveryMode;
        incoterm: Incoterm;
        returnType: SalesReturnRequestReturnType;
        isCreditingAllowed: boolean;
        financeIntegrationStatus: FinanceIntegrationStatus;
        _attachments: ClientCollection<AttachmentAssociation>;
        displayStatus: BaseDisplayStatus;
        stockSite: Site;
        lines: ClientCollection<SalesReturnRequestLine>;
    }
    export interface SalesReturnRequestInput extends ClientNodeInput {
        number?: string;
        status?: BaseStatus;
        approvalStatus?: ApprovalStatus;
        date?: string;
        isPrinted?: boolean | string;
        isSent?: boolean | string;
        site?: integer | string;
        currency?: integer | string;
        financialSite?: integer | string;
        text?: TextStream;
        internalNote?: TextStream;
        externalNote?: TextStream;
        isExternalNote?: boolean | string;
        isOverwriteNote?: boolean | string;
        isTransferHeaderNote?: boolean | string;
        isTransferLineNote?: boolean | string;
        siteAddress?: integer | string;
        businessEntityAddress?: integer | string;
        receiptStatus?: SalesDocumentReturnRequestReceiptStatus;
        creditStatus?: SalesDocumentCreditStatus;
        requester?: integer | string;
        soldToCustomer?: integer | string;
        soldToLinkedAddress?: integer | string;
        soldToAddress?: integer | string;
        soldToContact?: integer | string;
        shipToCustomer?: integer | string;
        shipToCustomerAddress?: integer | string;
        shipToAddress?: integer | string;
        shipToContact?: integer | string;
        billToCustomer?: integer | string;
        billToLinkedAddress?: integer | string;
        billToAddress?: integer | string;
        billToContact?: integer | string;
        deliveryMode?: integer | string;
        incoterm?: integer | string;
        returnType?: SalesReturnRequestReturnType;
        isCreditingAllowed?: boolean | string;
        _attachments?: Partial<AttachmentAssociationInput>[];
        displayStatus?: BaseDisplayStatus;
        stockSite?: integer | string;
        lines?: Partial<SalesReturnRequestLineInput>[];
    }
    export interface SalesReturnRequestBinding extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        number: string;
        status: BaseStatus;
        approvalStatus: ApprovalStatus;
        date: string;
        canPrint: boolean;
        isPrinted: boolean;
        isSent: boolean;
        site: Site;
        currency: Currency;
        transactionCurrency: Currency;
        companyCurrency: Currency;
        financialSite: Site;
        text: TextStream;
        internalNote: TextStream;
        externalNote: TextStream;
        isExternalNote: boolean;
        isOverwriteNote: boolean;
        isTransferHeaderNote: boolean;
        isTransferLineNote: boolean;
        siteAddress: Address;
        businessEntityAddress: BusinessEntityAddress;
        receiptStatus: SalesDocumentReturnRequestReceiptStatus;
        creditStatus: SalesDocumentCreditStatus;
        salesSite: Site;
        requester: User;
        returnRequestDate: string;
        soldToCustomer: Customer;
        soldToLinkedAddress: BusinessEntityAddress;
        soldToAddress: Address;
        soldToContact: Contact;
        shipToCustomer: Customer;
        shipToCustomerAddress: BusinessEntityAddress;
        shipToAddress: Address;
        shipToContact: Contact;
        billToCustomer: Customer;
        billToLinkedAddress: BusinessEntityAddress;
        billToAddress: Address;
        billToContact: Contact;
        deliveryMode: DeliveryMode;
        incoterm: Incoterm;
        returnType: SalesReturnRequestReturnType;
        isCreditingAllowed: boolean;
        financeIntegrationStatus: FinanceIntegrationStatus;
        _attachments: ClientCollection<AttachmentAssociation>;
        displayStatus: BaseDisplayStatus;
        stockSite: Site;
        lines: ClientCollection<SalesReturnRequestLineBinding>;
    }
    export interface SalesReturnRequest$Queries {
        getFilteredUsers: Node$Operation<
            {
                criteria: {
                    userType: string;
                    isApiUser?: boolean | string;
                    isActive?: boolean | string;
                    isOperatorUser?: boolean | string;
                };
            },
            {
                _id: string;
                email: string;
                firstName: string;
                lastName: string;
            }[]
        >;
    }
    export interface SalesReturnRequest$Mutations {
        setSalesReturnRequestCloseStatus: Node$Operation<
            {
                salesReturnRequestNumber?: string;
            },
            SalesReturnRequestCloseStatusMethodReturn
        >;
        setSalesReturnRequestOpenStatus: Node$Operation<
            {
                salesReturnRequestNumber?: string;
            },
            SalesReturnRequestOpenStatusMethodReturn
        >;
        setSalesReturnRequestLineCloseStatus: Node$Operation<
            {
                salesReturnRequestLine: string;
            },
            SalesReturnRequestLineCloseStatusMethodReturn
        >;
        setSalesReturnRequestLineOpenStatus: Node$Operation<
            {
                salesReturnRequestLine: string;
                newQuantityInSalesUnit?: decimal | string;
            },
            SalesReturnRequestLineOpenStatusMethodReturn
        >;
        validateQuantityInSalesUnit: Node$Operation<
            {
                salesReturnRequestLine?: string;
                newQuantityInSalesUnit?: decimal | string;
            },
            string
        >;
        approve: Node$Operation<
            {
                returnRequest: string;
            },
            string
        >;
        reject: Node$Operation<
            {
                returnRequest: string;
            },
            string
        >;
        createSalesCreditMemosFromReturnRequests: Node$Operation<
            {
                salesDocuments?: string[];
                creditMemoDate?: string;
            },
            {
                status: string;
                numberOfCreditMemos: integer;
                documentsCreated: SalesCreditMemo[];
                lineErrors: {
                    lineNumber: integer;
                    linePosition: integer;
                    message: string;
                }[];
            }
        >;
        createSalesCreditMemosFromReturnRequestLines: Node$Operation<
            {
                salesDocumentLines?: {
                    salesDocumentLine?: integer | string;
                    quantityToProcess?: decimal | string;
                }[];
                creditMemoDate?: string;
            },
            {
                status: string;
                numberOfCreditMemos: integer;
                documentsCreated: string[];
                lineErrors: {
                    lineNumber: integer;
                    linePosition: integer;
                    message: string;
                }[];
            }
        >;
        confirm: Node$Operation<
            {
                salesReturnRequestNumber: string;
            },
            boolean
        >;
        financeIntegrationCheck: Node$Operation<
            {
                salesReturnRequest: string;
            },
            {
                wasSuccessful: boolean;
                message: string;
            }
        >;
        sendApprovalRequestMail: Node$Operation<
            {
                document?: string;
                user?: UserInput | null;
            },
            boolean
        >;
    }
    export interface SalesReturnRequest$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
        bulkResync: AsyncOperation<
            {
                filter?: string;
            },
            boolean
        >;
    }
    export interface SalesReturnRequest$Lookups {
        site: QueryOperation<Site>;
        currency: QueryOperation<Currency>;
        transactionCurrency: QueryOperation<Currency>;
        companyCurrency: QueryOperation<Currency>;
        financialSite: QueryOperation<Site>;
        siteAddress: QueryOperation<Address>;
        businessEntityAddress: QueryOperation<BusinessEntityAddress>;
        salesSite: QueryOperation<Site>;
        requester: QueryOperation<User>;
        soldToCustomer: QueryOperation<Customer>;
        soldToLinkedAddress: QueryOperation<BusinessEntityAddress>;
        soldToAddress: QueryOperation<Address>;
        soldToContact: QueryOperation<Contact>;
        shipToCustomer: QueryOperation<Customer>;
        shipToCustomerAddress: QueryOperation<BusinessEntityAddress>;
        shipToAddress: QueryOperation<Address>;
        shipToContact: QueryOperation<Contact>;
        billToCustomer: QueryOperation<Customer>;
        billToLinkedAddress: QueryOperation<BusinessEntityAddress>;
        billToAddress: QueryOperation<Address>;
        billToContact: QueryOperation<Contact>;
        deliveryMode: QueryOperation<DeliveryMode>;
        incoterm: QueryOperation<Incoterm>;
        stockSite: QueryOperation<Site>;
    }
    export interface SalesReturnRequest$Operations {
        query: QueryOperation<SalesReturnRequest>;
        read: ReadOperation<SalesReturnRequest>;
        aggregate: {
            read: AggregateReadOperation<SalesReturnRequest>;
            query: AggregateQueryOperation<SalesReturnRequest>;
        };
        queries: SalesReturnRequest$Queries;
        create: CreateOperation<SalesReturnRequestInput, SalesReturnRequest>;
        getDuplicate: GetDuplicateOperation<SalesReturnRequest>;
        duplicate: DuplicateOperation<string, SalesReturnRequestInput, SalesReturnRequest>;
        update: UpdateOperation<SalesReturnRequestInput, SalesReturnRequest>;
        updateById: UpdateByIdOperation<SalesReturnRequestInput, SalesReturnRequest>;
        delete: DeleteOperation<{}>;
        deleteById: DeleteOperation<string>;
        mutations: SalesReturnRequest$Mutations;
        asyncOperations: SalesReturnRequest$AsyncOperations;
        lookups(dataOrId: string | { data: SalesReturnRequestInput }): SalesReturnRequest$Lookups;
        getDefaults: GetDefaultsOperation<SalesReturnRequest>;
    }
    export interface SalesReturnRequestLine extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        documentNumber: string;
        documentId: integer;
        document: SalesReturnRequest;
        origin: BaseOrigin;
        status: BaseStatus;
        site: Site;
        siteLinkedAddress: BusinessEntityAddress;
        item: Item;
        itemDescription: string;
        stockSite: Site;
        stockSiteLinkedAddress: BusinessEntityAddress;
        itemSite: ItemSite;
        stockUnit: UnitOfMeasure;
        unit: UnitOfMeasure;
        quantity: string;
        internalNote: TextStream;
        externalNote: TextStream;
        isExternalNote: boolean;
        originDocumentType: SalesOriginDocumentType;
        receiptStatus: SalesDocumentReturnRequestReceiptStatus;
        creditStatus: SalesDocumentCreditStatus;
        isCreditMemoExpected: boolean;
        isReceiptExpected: boolean;
        quantityInSalesUnit: string;
        salesUnit: UnitOfMeasure;
        salesUnitToStockUnitConversionFactor: string;
        originShippingSite: Site;
        reason: SalesReturnRequestReason;
        text: TextStream;
        salesShipmentLines: ClientCollection<SalesShipmentLineToSalesReturnRequestLine>;
        salesReturnReceiptLines: ClientCollection<SalesReturnRequestLineToSalesReturnReceiptLine>;
        quantityReceiptInSalesUnit: string;
        quantityToReceiveInProgressInSalesUnit: string;
        quantityCreditedInProgressInSalesUnit: string;
        quantityCreditedPostedInSalesUnit: string;
        isCreditingAllowed: boolean;
        dimensionDefinitionLevel: DimensionDefinitionLevel;
        storedDimensions: string;
        storedAttributes: string;
        computedAttributes: string;
        analyticalData: AnalyticalData;
        unitToStockUnitConversionFactor: string;
        uQuantityReceiptAndToReceiveInSalesUnit: string;
        quantityInStockUnit: string;
    }
    export interface SalesReturnRequestLineInput extends VitalClientNodeInput {
        origin?: BaseOrigin;
        status?: BaseStatus;
        site?: integer | string;
        siteLinkedAddress?: integer | string;
        item?: integer | string;
        itemDescription?: string;
        stockSite?: integer | string;
        stockSiteLinkedAddress?: integer | string;
        stockUnit?: integer | string;
        unit?: integer | string;
        quantity?: decimal | string;
        internalNote?: TextStream;
        externalNote?: TextStream;
        isExternalNote?: boolean | string;
        receiptStatus?: SalesDocumentReturnRequestReceiptStatus;
        creditStatus?: SalesDocumentCreditStatus;
        isCreditMemoExpected?: boolean | string;
        isReceiptExpected?: boolean | string;
        originShippingSite?: integer | string;
        reason?: integer | string;
        text?: TextStream;
        salesShipmentLines?: Partial<SalesShipmentLineToSalesReturnRequestLineInput>[];
        isCreditingAllowed?: boolean | string;
        storedDimensions?: string;
        storedAttributes?: string;
        analyticalData?: integer | string;
        unitToStockUnitConversionFactor?: decimal | string;
        quantityInStockUnit?: decimal | string;
    }
    export interface SalesReturnRequestLineBinding extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        documentNumber: string;
        documentId: integer;
        document: SalesReturnRequest;
        origin: BaseOrigin;
        status: BaseStatus;
        site: Site;
        siteLinkedAddress: BusinessEntityAddress;
        item: Item;
        itemDescription: string;
        stockSite: Site;
        stockSiteLinkedAddress: BusinessEntityAddress;
        itemSite: ItemSite;
        stockUnit: UnitOfMeasure;
        unit: UnitOfMeasure;
        quantity: string;
        internalNote: TextStream;
        externalNote: TextStream;
        isExternalNote: boolean;
        originDocumentType: SalesOriginDocumentType;
        receiptStatus: SalesDocumentReturnRequestReceiptStatus;
        creditStatus: SalesDocumentCreditStatus;
        isCreditMemoExpected: boolean;
        isReceiptExpected: boolean;
        quantityInSalesUnit: string;
        salesUnit: UnitOfMeasure;
        salesUnitToStockUnitConversionFactor: string;
        originShippingSite: Site;
        reason: SalesReturnRequestReason;
        text: TextStream;
        salesShipmentLines: ClientCollection<SalesShipmentLineToSalesReturnRequestLineBinding>;
        salesReturnReceiptLines: ClientCollection<SalesReturnRequestLineToSalesReturnReceiptLine>;
        quantityReceiptInSalesUnit: string;
        quantityToReceiveInProgressInSalesUnit: string;
        quantityCreditedInProgressInSalesUnit: string;
        quantityCreditedPostedInSalesUnit: string;
        isCreditingAllowed: boolean;
        dimensionDefinitionLevel: DimensionDefinitionLevel;
        storedDimensions: any;
        storedAttributes: any;
        computedAttributes: any;
        analyticalData: AnalyticalData;
        unitToStockUnitConversionFactor: string;
        uQuantityReceiptAndToReceiveInSalesUnit: string;
        quantityInStockUnit: string;
    }
    export interface SalesReturnRequestLine$Mutations {
        setDimension: Node$Operation<
            {
                baseDocumentItemLine: string;
                storedDimensions?: string;
                storedAttributes?: string;
            },
            boolean
        >;
    }
    export interface SalesReturnRequestLine$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface SalesReturnRequestLine$Lookups {
        site: QueryOperation<Site>;
        siteLinkedAddress: QueryOperation<BusinessEntityAddress>;
        item: QueryOperation<Item>;
        stockSite: QueryOperation<Site>;
        stockSiteLinkedAddress: QueryOperation<BusinessEntityAddress>;
        itemSite: QueryOperation<ItemSite>;
        stockUnit: QueryOperation<UnitOfMeasure>;
        unit: QueryOperation<UnitOfMeasure>;
        salesUnit: QueryOperation<UnitOfMeasure>;
        originShippingSite: QueryOperation<Site>;
        reason: QueryOperation<SalesReturnRequestReason>;
    }
    export interface SalesReturnRequestLine$Operations {
        query: QueryOperation<SalesReturnRequestLine>;
        read: ReadOperation<SalesReturnRequestLine>;
        aggregate: {
            read: AggregateReadOperation<SalesReturnRequestLine>;
            query: AggregateQueryOperation<SalesReturnRequestLine>;
        };
        mutations: SalesReturnRequestLine$Mutations;
        asyncOperations: SalesReturnRequestLine$AsyncOperations;
        lookups(dataOrId: string | { data: SalesReturnRequestLineInput }): SalesReturnRequestLine$Lookups;
        getDefaults: GetDefaultsOperation<SalesReturnRequestLine>;
    }
    export interface SalesShipment extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        number: string;
        status: BaseStatus;
        approvalStatus: ApprovalStatus;
        canPrint: boolean;
        isPrinted: boolean;
        isSent: boolean;
        site: Site;
        companyCurrency: Currency;
        financialSite: Site;
        text: TextStream;
        internalNote: TextStream;
        externalNote: TextStream;
        isExternalNote: boolean;
        isTransferHeaderNote: boolean;
        isTransferLineNote: boolean;
        siteAddress: Address;
        businessEntityAddress: BusinessEntityAddress;
        reference: string;
        invoiceStatus: SalesDocumentInvoiceStatus;
        returnRequestStatus: SalesDocumentReturnStatus;
        returnReceiptStatus: SalesDocumentReceiptStatus;
        salesSite: Site;
        recordUrl: string;
        effectiveDate: string;
        shipToCustomer: Customer;
        shipToCustomerAddress: BusinessEntityAddress;
        shipToAddress: Address;
        shipToContact: Contact;
        billToCustomer: Customer;
        billToLinkedAddress: BusinessEntityAddress;
        billToAddress: Address;
        billToContact: Contact;
        trackingNumber: string;
        incoterm: Incoterm;
        deliveryMode: DeliveryMode;
        deliveryLeadTime: integer;
        workDays: integer;
        paymentTerm: PaymentTerm;
        isOnHold: boolean;
        postingDetails: ClientCollection<FinanceTransaction>;
        financeIntegrationStatus: FinanceIntegrationStatus;
        _attachments: ClientCollection<AttachmentAssociation>;
        date: string;
        stockSite: Site;
        currency: Currency;
        transactionCurrency: Currency;
        shippingDate: string;
        deliveryDate: string;
        fxRateDate: string;
        companyFxRate: string;
        companyFxRateDivisor: string;
        lines: ClientCollection<SalesShipmentLine>;
        stockTransactionStatus: StockDocumentTransactionStatus;
        rateDescription: string;
        allocationStatus: StockAllocationStatus;
        totalAmountExcludingTax: string;
        totalAmountExcludingTaxInCompanyCurrency: string;
        totalGrossProfit: string;
        displayStatus: BaseDisplayStatus;
    }
    export interface SalesShipmentInput extends ClientNodeInput {
        number?: string;
        status?: BaseStatus;
        approvalStatus?: ApprovalStatus;
        isPrinted?: boolean | string;
        isSent?: boolean | string;
        site?: integer | string;
        financialSite?: integer | string;
        text?: TextStream;
        internalNote?: TextStream;
        externalNote?: TextStream;
        isExternalNote?: boolean | string;
        isOverwriteNote?: boolean | string;
        isTransferHeaderNote?: boolean | string;
        isTransferLineNote?: boolean | string;
        siteAddress?: integer | string;
        businessEntityAddress?: integer | string;
        reference?: string;
        invoiceStatus?: SalesDocumentInvoiceStatus;
        returnRequestStatus?: SalesDocumentReturnStatus;
        returnReceiptStatus?: SalesDocumentReceiptStatus;
        shipToCustomer?: integer | string;
        shipToCustomerAddress?: integer | string;
        shipToAddress?: integer | string;
        shipToContact?: integer | string;
        billToCustomer?: integer | string;
        billToLinkedAddress?: integer | string;
        billToAddress?: integer | string;
        billToContact?: integer | string;
        trackingNumber?: string;
        incoterm?: integer | string;
        deliveryMode?: integer | string;
        deliveryLeadTime?: integer | string;
        paymentTerm?: integer | string;
        _attachments?: Partial<AttachmentAssociationInput>[];
        date?: string;
        stockSite?: integer | string;
        currency?: integer | string;
        deliveryDate?: string;
        fxRateDate?: string;
        companyFxRate?: decimal | string;
        companyFxRateDivisor?: decimal | string;
        lines?: Partial<SalesShipmentLineInput>[];
        displayStatus?: BaseDisplayStatus;
    }
    export interface SalesShipmentBinding extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        number: string;
        status: BaseStatus;
        approvalStatus: ApprovalStatus;
        canPrint: boolean;
        isPrinted: boolean;
        isSent: boolean;
        site: Site;
        companyCurrency: Currency;
        financialSite: Site;
        text: TextStream;
        internalNote: TextStream;
        externalNote: TextStream;
        isExternalNote: boolean;
        isOverwriteNote: boolean;
        isTransferHeaderNote: boolean;
        isTransferLineNote: boolean;
        siteAddress: Address;
        businessEntityAddress: BusinessEntityAddress;
        reference: string;
        invoiceStatus: SalesDocumentInvoiceStatus;
        returnRequestStatus: SalesDocumentReturnStatus;
        returnReceiptStatus: SalesDocumentReceiptStatus;
        salesSite: Site;
        recordUrl: string;
        effectiveDate: string;
        shipToCustomer: Customer;
        shipToCustomerAddress: BusinessEntityAddress;
        shipToAddress: Address;
        shipToContact: Contact;
        billToCustomer: Customer;
        billToLinkedAddress: BusinessEntityAddress;
        billToAddress: Address;
        billToContact: Contact;
        trackingNumber: string;
        incoterm: Incoterm;
        deliveryMode: DeliveryMode;
        deliveryLeadTime: integer;
        workDays: integer;
        paymentTerm: PaymentTerm;
        isOnHold: boolean;
        postingDetails: ClientCollection<FinanceTransaction>;
        financeIntegrationStatus: FinanceIntegrationStatus;
        _attachments: ClientCollection<AttachmentAssociation>;
        date: string;
        stockSite: Site;
        currency: Currency;
        transactionCurrency: Currency;
        shippingDate: string;
        deliveryDate: string;
        fxRateDate: string;
        companyFxRate: string;
        companyFxRateDivisor: string;
        lines: ClientCollection<SalesShipmentLineBinding>;
        stockTransactionStatus: StockDocumentTransactionStatus;
        rateDescription: string;
        allocationStatus: StockAllocationStatus;
        totalAmountExcludingTax: string;
        totalAmountExcludingTaxInCompanyCurrency: string;
        totalGrossProfit: string;
        displayStatus: BaseDisplayStatus;
    }
    export interface SalesShipment$Mutations {
        confirm: Node$Operation<
            {
                salesShipment: string;
                isSafeToRetry?: boolean | string;
            },
            SalesShipment
        >;
        revoke: Node$Operation<
            {
                _id?: string;
            },
            string
        >;
        addWorkDays: Node$Operation<
            {
                shippingDate: string;
                deliveryLeadTime: integer | string;
                workDays: integer | string;
            },
            string
        >;
        createSalesInvoicesFromShipment: Node$Operation<
            {
                salesShipment: string;
                isSafeToRetry?: boolean | string;
                invoiceDate?: string;
            },
            SalesInvoice[]
        >;
        createSalesInvoicesFromShipmentLines: Node$Operation<
            {
                salesDocumentLines?: {
                    salesDocumentLine?: integer | string;
                    quantityToProcess?: decimal | string;
                }[];
                invoiceDate?: string;
            },
            {
                status: string;
                numberOfInvoices: integer;
                documentsCreated: SalesInvoice[];
                lineErrors: {
                    lineNumber: integer;
                    linePosition: integer;
                    message: string;
                }[];
                errorMessage: {
                    loggerMessage: string;
                    message: string;
                }[];
                financeMessages: string;
            }
        >;
        massCreateSalesInvoices: Node$Operation<
            {
                site: string;
                fromBillToCustomer?: string;
                toBillToCustomer?: string;
                fromShipment?: string;
                toShipment?: string;
                shipmentUntilDate?: string | null;
                invoiceDate?: string;
            },
            {
                numberOfInvoices: integer;
                financeMessages: string;
                errorMessage: {
                    loggerMessage: string;
                    message: string;
                }[];
            }
        >;
        createSalesReturnRequestFromShipmentLines: Node$Operation<
            {
                salesDocumentLines?: {
                    salesDocumentLine?: integer | string;
                    quantityToProcess?: decimal | string;
                }[];
            },
            {
                status: string;
                numberOfReturnRequests: integer;
                documentsCreated: SalesReturnRequest[];
                lineErrors: {
                    lineNumber: integer;
                    linePosition: integer;
                    message: string;
                }[];
            }
        >;
        createSalesReturnRequestFromShipments: Node$Operation<
            {
                salesDocuments?: string[];
            },
            {
                status: string;
                numberOfReturnRequests: integer;
                documentsCreated: SalesReturnRequest[];
                lineErrors: {
                    lineNumber: integer;
                    linePosition: integer;
                    message: string;
                }[];
            }
        >;
        postToStock: Node$Operation<
            {
                salesShipment: string;
                isSafeToRetry?: boolean | string;
            },
            string
        >;
        repost: Node$Operation<
            {
                salesShipment: string;
                documentLines?: {
                    baseDocumentLineSysId?: integer | string;
                    storedAttributes?: string;
                    storedDimensions?: string;
                }[];
            },
            {
                wasSuccessful: boolean;
                message: string;
            }
        >;
        resynchronizeStatus: Node$Operation<
            {
                salesShipment: string;
            },
            boolean
        >;
        resendNotificationForFinance: Node$Operation<
            {
                salesShipment: string;
            },
            boolean
        >;
        beforePrintPackingSlip: Node$Operation<
            {
                shipment: string;
            },
            boolean
        >;
        afterPrintPackingSlip: Node$Operation<
            {
                shipment: string;
            },
            boolean
        >;
        beforePrintSalesShipmentPickList: Node$Operation<
            {
                shipment: string;
            },
            boolean
        >;
        sendApprovalRequestMail: Node$Operation<
            {
                document?: string;
                user?: UserInput | null;
            },
            boolean
        >;
    }
    export interface SalesShipment$AsyncOperations {
        post: AsyncOperation<
            {
                salesShipment: string;
                isSafeToRetry?: boolean | string;
            },
            SalesShipment
        >;
        printBulkPackingSlip: AsyncOperation<
            {
                filter?: string;
            },
            boolean
        >;
        printBulkPickList: AsyncOperation<
            {
                filter?: string;
            },
            boolean
        >;
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
        bulkResync: AsyncOperation<
            {
                filter?: string;
            },
            boolean
        >;
    }
    export interface SalesShipment$Lookups {
        site: QueryOperation<Site>;
        companyCurrency: QueryOperation<Currency>;
        financialSite: QueryOperation<Site>;
        siteAddress: QueryOperation<Address>;
        businessEntityAddress: QueryOperation<BusinessEntityAddress>;
        salesSite: QueryOperation<Site>;
        shipToCustomer: QueryOperation<Customer>;
        shipToCustomerAddress: QueryOperation<BusinessEntityAddress>;
        shipToAddress: QueryOperation<Address>;
        shipToContact: QueryOperation<Contact>;
        billToCustomer: QueryOperation<Customer>;
        billToLinkedAddress: QueryOperation<BusinessEntityAddress>;
        billToAddress: QueryOperation<Address>;
        billToContact: QueryOperation<Contact>;
        incoterm: QueryOperation<Incoterm>;
        deliveryMode: QueryOperation<DeliveryMode>;
        paymentTerm: QueryOperation<PaymentTerm>;
        stockSite: QueryOperation<Site>;
        currency: QueryOperation<Currency>;
        transactionCurrency: QueryOperation<Currency>;
    }
    export interface SalesShipment$Operations {
        query: QueryOperation<SalesShipment>;
        read: ReadOperation<SalesShipment>;
        aggregate: {
            read: AggregateReadOperation<SalesShipment>;
            query: AggregateQueryOperation<SalesShipment>;
        };
        create: CreateOperation<SalesShipmentInput, SalesShipment>;
        getDuplicate: GetDuplicateOperation<SalesShipment>;
        duplicate: DuplicateOperation<string, SalesShipmentInput, SalesShipment>;
        update: UpdateOperation<SalesShipmentInput, SalesShipment>;
        updateById: UpdateByIdOperation<SalesShipmentInput, SalesShipment>;
        delete: DeleteOperation<{}>;
        deleteById: DeleteOperation<string>;
        mutations: SalesShipment$Mutations;
        asyncOperations: SalesShipment$AsyncOperations;
        lookups(dataOrId: string | { data: SalesShipmentInput }): SalesShipment$Lookups;
        getDefaults: GetDefaultsOperation<SalesShipment>;
    }
    export interface SalesShipmentLine extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        documentNumber: string;
        documentId: integer;
        document: SalesShipment;
        origin: BaseOrigin;
        status: BaseStatus;
        site: Site;
        siteLinkedAddress: BusinessEntityAddress;
        item: Item;
        itemDescription: string;
        stockSite: Site;
        stockSiteLinkedAddress: BusinessEntityAddress;
        itemSite: ItemSite;
        stockUnit: UnitOfMeasure;
        unit: UnitOfMeasure;
        quantity: string;
        internalNote: TextStream;
        externalNote: TextStream;
        isExternalNote: boolean;
        originDocumentType: SalesOriginDocumentType;
        invoiceStatus: SalesDocumentInvoiceStatus;
        returnRequestStatus: SalesDocumentReturnStatus;
        returnReceiptStatus: SalesDocumentReceiptStatus;
        stockTransactionStatus: StockDocumentTransactionStatus;
        quantityInSalesUnit: string;
        salesUnit: UnitOfMeasure;
        salesUnitToStockUnitConversionFactor: string;
        salesOrderLines: ClientCollection<SalesOrderLineToSalesShipmentLine>;
        grossPrice: string;
        quantityInvoicedInProgressInSalesUnit: string;
        quantityInvoicedPostedInSalesUnit: string;
        quantityRequestedInSalesUnit: string;
        quantityReceiptInSalesUnit: string;
        stockAllocations: ClientCollection<StockAllocation>;
        priceOrigin: SalesPriceOrigin;
        priceReason: CustomerPriceReason;
        priceOriginDeterminated: SalesPriceOrigin;
        priceReasonDeterminated: CustomerPriceReason;
        isPriceDeterminated: boolean;
        grossPriceDeterminated: string;
        stockDetails: ClientCollection<StockIssueDetail>;
        stockMovements: ClientCollection<StockJournal>;
        stockTransactions: ClientCollection<StockTransaction>;
        customerNumber: string;
        dimensionDefinitionLevel: DimensionDefinitionLevel;
        storedDimensions: string;
        storedAttributes: string;
        computedAttributes: string;
        analyticalData: AnalyticalData;
        unitToStockUnitConversionFactor: string;
        quantityAllocated: string;
        discountCharges: ClientCollection<SalesShipmentLineDiscountCharge>;
        discount: string;
        charge: string;
        netPrice: string;
        amountExcludingTax: string;
        amountExcludingTaxInCompanyCurrency: string;
        quantityInStockUnit: string;
        remainingQuantity: string;
        remainingQuantityToAllocate: string;
        stockCostAmountInCompanyCurrency: string;
        grossProfitAmountInCompanyCurrency: string;
        stockCostAmount: string;
        grossProfitAmount: string;
        stockCostUnit: string;
        grossProfit: string;
        allocationStatus: StockAllocationStatus;
    }
    export interface SalesShipmentLineInput extends VitalClientNodeInput {
        origin?: BaseOrigin;
        status?: BaseStatus;
        site?: integer | string;
        siteLinkedAddress?: integer | string;
        item?: integer | string;
        itemDescription?: string;
        stockSite?: integer | string;
        stockSiteLinkedAddress?: integer | string;
        stockUnit?: integer | string;
        unit?: integer | string;
        quantity?: decimal | string;
        internalNote?: TextStream;
        externalNote?: TextStream;
        isExternalNote?: boolean | string;
        invoiceStatus?: SalesDocumentInvoiceStatus;
        returnRequestStatus?: SalesDocumentReturnStatus;
        returnReceiptStatus?: SalesDocumentReceiptStatus;
        stockTransactionStatus?: StockDocumentTransactionStatus;
        salesOrderLines?: Partial<SalesOrderLineToSalesShipmentLineInput>[];
        grossPrice?: decimal | string;
        priceOrigin?: SalesPriceOrigin;
        priceReason?: integer | string;
        priceOriginDeterminated?: SalesPriceOrigin;
        priceReasonDeterminated?: integer | string;
        isPriceDeterminated?: boolean | string;
        grossPriceDeterminated?: decimal | string;
        stockDetails?: Partial<StockIssueDetailInput>[];
        stockTransactions?: Partial<StockTransactionInput>[];
        customerNumber?: string;
        storedDimensions?: string;
        storedAttributes?: string;
        analyticalData?: integer | string;
        unitToStockUnitConversionFactor?: decimal | string;
        discountCharges?: Partial<SalesShipmentLineDiscountChargeInput>[];
        discount?: decimal | string;
        charge?: decimal | string;
        netPrice?: decimal | string;
        amountExcludingTaxInCompanyCurrency?: decimal | string;
        quantityInStockUnit?: decimal | string;
    }
    export interface SalesShipmentLineBinding extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        documentNumber: string;
        documentId: integer;
        document: SalesShipment;
        origin: BaseOrigin;
        status: BaseStatus;
        site: Site;
        siteLinkedAddress: BusinessEntityAddress;
        item: Item;
        itemDescription: string;
        stockSite: Site;
        stockSiteLinkedAddress: BusinessEntityAddress;
        itemSite: ItemSite;
        stockUnit: UnitOfMeasure;
        unit: UnitOfMeasure;
        quantity: string;
        internalNote: TextStream;
        externalNote: TextStream;
        isExternalNote: boolean;
        originDocumentType: SalesOriginDocumentType;
        invoiceStatus: SalesDocumentInvoiceStatus;
        returnRequestStatus: SalesDocumentReturnStatus;
        returnReceiptStatus: SalesDocumentReceiptStatus;
        stockTransactionStatus: StockDocumentTransactionStatus;
        quantityInSalesUnit: string;
        salesUnit: UnitOfMeasure;
        salesUnitToStockUnitConversionFactor: string;
        salesOrderLines: ClientCollection<SalesOrderLineToSalesShipmentLineBinding>;
        grossPrice: string;
        quantityInvoicedInProgressInSalesUnit: string;
        quantityInvoicedPostedInSalesUnit: string;
        quantityRequestedInSalesUnit: string;
        quantityReceiptInSalesUnit: string;
        stockAllocations: ClientCollection<StockAllocation>;
        priceOrigin: SalesPriceOrigin;
        priceReason: CustomerPriceReason;
        priceOriginDeterminated: SalesPriceOrigin;
        priceReasonDeterminated: CustomerPriceReason;
        isPriceDeterminated: boolean;
        grossPriceDeterminated: string;
        stockDetails: ClientCollection<StockIssueDetailBinding>;
        stockMovements: ClientCollection<StockJournal>;
        stockTransactions: ClientCollection<StockTransactionBinding>;
        customerNumber: string;
        dimensionDefinitionLevel: DimensionDefinitionLevel;
        storedDimensions: any;
        storedAttributes: any;
        computedAttributes: any;
        analyticalData: AnalyticalData;
        unitToStockUnitConversionFactor: string;
        quantityAllocated: string;
        discountCharges: ClientCollection<SalesShipmentLineDiscountChargeBinding>;
        discount: string;
        charge: string;
        netPrice: string;
        amountExcludingTax: string;
        amountExcludingTaxInCompanyCurrency: string;
        quantityInStockUnit: string;
        remainingQuantity: string;
        remainingQuantityToAllocate: string;
        stockCostAmountInCompanyCurrency: string;
        grossProfitAmountInCompanyCurrency: string;
        stockCostAmount: string;
        grossProfitAmount: string;
        stockCostUnit: string;
        grossProfit: string;
        allocationStatus: StockAllocationStatus;
    }
    export interface SalesShipmentLine$Mutations {
        setDimension: Node$Operation<
            {
                baseDocumentItemLine: string;
                storedDimensions?: string;
                storedAttributes?: string;
            },
            boolean
        >;
    }
    export interface SalesShipmentLine$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface SalesShipmentLine$Lookups {
        site: QueryOperation<Site>;
        siteLinkedAddress: QueryOperation<BusinessEntityAddress>;
        item: QueryOperation<Item>;
        stockSite: QueryOperation<Site>;
        stockSiteLinkedAddress: QueryOperation<BusinessEntityAddress>;
        itemSite: QueryOperation<ItemSite>;
        stockUnit: QueryOperation<UnitOfMeasure>;
        unit: QueryOperation<UnitOfMeasure>;
        salesUnit: QueryOperation<UnitOfMeasure>;
        priceReason: QueryOperation<CustomerPriceReason>;
        priceReasonDeterminated: QueryOperation<CustomerPriceReason>;
    }
    export interface SalesShipmentLine$Operations {
        query: QueryOperation<SalesShipmentLine>;
        read: ReadOperation<SalesShipmentLine>;
        aggregate: {
            read: AggregateReadOperation<SalesShipmentLine>;
            query: AggregateQueryOperation<SalesShipmentLine>;
        };
        mutations: SalesShipmentLine$Mutations;
        asyncOperations: SalesShipmentLine$AsyncOperations;
        lookups(dataOrId: string | { data: SalesShipmentLineInput }): SalesShipmentLine$Lookups;
        getDefaults: GetDefaultsOperation<SalesShipmentLine>;
    }
    export interface SalesCreditMemoLineTax extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        taxRate: string;
        taxCategoryReference: TaxCategory;
        taxReference: Tax;
        deductibleTaxRate: string;
        isReverseCharge: boolean;
        isTaxMandatory: boolean;
        isSubjectToGlTaxExcludedAmount: boolean;
        jurisdictionName: string;
        document: SalesCreditMemoLine;
        taxCategory: string;
        tax: string;
        currency: Currency;
        deductibleTaxAmount: string;
        taxAmountAdjusted: string;
        nonTaxableAmount: string;
        taxAmount: string;
        exemptAmount: string;
        taxableAmount: string;
    }
    export interface SalesCreditMemoLineTaxInput extends VitalClientNodeInput {
        taxRate?: decimal | string;
        taxCategoryReference?: integer | string;
        taxReference?: integer | string;
        deductibleTaxRate?: decimal | string;
        isReverseCharge?: boolean | string;
        isTaxMandatory?: boolean | string;
        isSubjectToGlTaxExcludedAmount?: boolean | string;
        jurisdictionName?: string;
        taxCategory?: string;
        tax?: string;
        deductibleTaxAmount?: decimal | string;
        taxAmountAdjusted?: decimal | string;
        nonTaxableAmount?: decimal | string;
        taxAmount?: decimal | string;
        exemptAmount?: decimal | string;
        taxableAmount?: decimal | string;
    }
    export interface SalesCreditMemoLineTaxBinding extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        taxRate: string;
        taxCategoryReference: TaxCategory;
        taxReference: Tax;
        deductibleTaxRate: string;
        isReverseCharge: boolean;
        isTaxMandatory: boolean;
        isSubjectToGlTaxExcludedAmount: boolean;
        jurisdictionName: string;
        document: SalesCreditMemoLine;
        taxCategory: string;
        tax: string;
        currency: Currency;
        deductibleTaxAmount: string;
        taxAmountAdjusted: string;
        nonTaxableAmount: string;
        taxAmount: string;
        exemptAmount: string;
        taxableAmount: string;
    }
    export interface SalesCreditMemoLineTax$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface SalesCreditMemoLineTax$Lookups {
        taxCategoryReference: QueryOperation<TaxCategory>;
        taxReference: QueryOperation<Tax>;
        currency: QueryOperation<Currency>;
    }
    export interface SalesCreditMemoLineTax$Operations {
        query: QueryOperation<SalesCreditMemoLineTax>;
        read: ReadOperation<SalesCreditMemoLineTax>;
        aggregate: {
            read: AggregateReadOperation<SalesCreditMemoLineTax>;
            query: AggregateQueryOperation<SalesCreditMemoLineTax>;
        };
        asyncOperations: SalesCreditMemoLineTax$AsyncOperations;
        lookups(dataOrId: string | { data: SalesCreditMemoLineTaxInput }): SalesCreditMemoLineTax$Lookups;
        getDefaults: GetDefaultsOperation<SalesCreditMemoLineTax>;
    }
    export interface SalesCreditMemoTax extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        taxRate: string;
        taxCategoryReference: TaxCategory;
        taxReference: Tax;
        deductibleTaxRate: string;
        isReverseCharge: boolean;
        isTaxMandatory: boolean;
        isSubjectToGlTaxExcludedAmount: boolean;
        jurisdictionName: string;
        document: SalesCreditMemo;
        taxCategory: string;
        tax: string;
        currency: Currency;
        deductibleTaxAmount: string;
        taxAmountAdjusted: string;
        nonTaxableAmount: string;
        taxAmount: string;
        exemptAmount: string;
        taxableAmount: string;
    }
    export interface SalesCreditMemoTaxInput extends VitalClientNodeInput {
        taxRate?: decimal | string;
        taxCategoryReference?: integer | string;
        taxReference?: integer | string;
        deductibleTaxRate?: decimal | string;
        isReverseCharge?: boolean | string;
        isTaxMandatory?: boolean | string;
        isSubjectToGlTaxExcludedAmount?: boolean | string;
        jurisdictionName?: string;
        taxCategory?: string;
        tax?: string;
        deductibleTaxAmount?: decimal | string;
        taxAmountAdjusted?: decimal | string;
        nonTaxableAmount?: decimal | string;
        taxAmount?: decimal | string;
        exemptAmount?: decimal | string;
        taxableAmount?: decimal | string;
    }
    export interface SalesCreditMemoTaxBinding extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        taxRate: string;
        taxCategoryReference: TaxCategory;
        taxReference: Tax;
        deductibleTaxRate: string;
        isReverseCharge: boolean;
        isTaxMandatory: boolean;
        isSubjectToGlTaxExcludedAmount: boolean;
        jurisdictionName: string;
        document: SalesCreditMemo;
        taxCategory: string;
        tax: string;
        currency: Currency;
        deductibleTaxAmount: string;
        taxAmountAdjusted: string;
        nonTaxableAmount: string;
        taxAmount: string;
        exemptAmount: string;
        taxableAmount: string;
    }
    export interface SalesCreditMemoTax$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface SalesCreditMemoTax$Lookups {
        taxCategoryReference: QueryOperation<TaxCategory>;
        taxReference: QueryOperation<Tax>;
        currency: QueryOperation<Currency>;
    }
    export interface SalesCreditMemoTax$Operations {
        query: QueryOperation<SalesCreditMemoTax>;
        read: ReadOperation<SalesCreditMemoTax>;
        aggregate: {
            read: AggregateReadOperation<SalesCreditMemoTax>;
            query: AggregateQueryOperation<SalesCreditMemoTax>;
        };
        asyncOperations: SalesCreditMemoTax$AsyncOperations;
        lookups(dataOrId: string | { data: SalesCreditMemoTaxInput }): SalesCreditMemoTax$Lookups;
        getDefaults: GetDefaultsOperation<SalesCreditMemoTax>;
    }
    export interface SalesInvoiceLineTax extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        taxRate: string;
        taxCategoryReference: TaxCategory;
        taxReference: Tax;
        deductibleTaxRate: string;
        isReverseCharge: boolean;
        isTaxMandatory: boolean;
        isSubjectToGlTaxExcludedAmount: boolean;
        jurisdictionName: string;
        document: SalesInvoiceLine;
        taxCategory: string;
        tax: string;
        currency: Currency;
        deductibleTaxAmount: string;
        taxAmountAdjusted: string;
        nonTaxableAmount: string;
        taxAmount: string;
        exemptAmount: string;
        taxableAmount: string;
    }
    export interface SalesInvoiceLineTaxInput extends VitalClientNodeInput {
        taxRate?: decimal | string;
        taxCategoryReference?: integer | string;
        taxReference?: integer | string;
        deductibleTaxRate?: decimal | string;
        isReverseCharge?: boolean | string;
        isTaxMandatory?: boolean | string;
        isSubjectToGlTaxExcludedAmount?: boolean | string;
        jurisdictionName?: string;
        taxCategory?: string;
        tax?: string;
        deductibleTaxAmount?: decimal | string;
        taxAmountAdjusted?: decimal | string;
        nonTaxableAmount?: decimal | string;
        taxAmount?: decimal | string;
        exemptAmount?: decimal | string;
        taxableAmount?: decimal | string;
    }
    export interface SalesInvoiceLineTaxBinding extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        taxRate: string;
        taxCategoryReference: TaxCategory;
        taxReference: Tax;
        deductibleTaxRate: string;
        isReverseCharge: boolean;
        isTaxMandatory: boolean;
        isSubjectToGlTaxExcludedAmount: boolean;
        jurisdictionName: string;
        document: SalesInvoiceLine;
        taxCategory: string;
        tax: string;
        currency: Currency;
        deductibleTaxAmount: string;
        taxAmountAdjusted: string;
        nonTaxableAmount: string;
        taxAmount: string;
        exemptAmount: string;
        taxableAmount: string;
    }
    export interface SalesInvoiceLineTax$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface SalesInvoiceLineTax$Lookups {
        taxCategoryReference: QueryOperation<TaxCategory>;
        taxReference: QueryOperation<Tax>;
        currency: QueryOperation<Currency>;
    }
    export interface SalesInvoiceLineTax$Operations {
        query: QueryOperation<SalesInvoiceLineTax>;
        read: ReadOperation<SalesInvoiceLineTax>;
        aggregate: {
            read: AggregateReadOperation<SalesInvoiceLineTax>;
            query: AggregateQueryOperation<SalesInvoiceLineTax>;
        };
        asyncOperations: SalesInvoiceLineTax$AsyncOperations;
        lookups(dataOrId: string | { data: SalesInvoiceLineTaxInput }): SalesInvoiceLineTax$Lookups;
        getDefaults: GetDefaultsOperation<SalesInvoiceLineTax>;
    }
    export interface SalesInvoiceTax extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        taxRate: string;
        taxCategoryReference: TaxCategory;
        taxReference: Tax;
        deductibleTaxRate: string;
        isReverseCharge: boolean;
        isTaxMandatory: boolean;
        isSubjectToGlTaxExcludedAmount: boolean;
        jurisdictionName: string;
        document: SalesInvoice;
        taxCategory: string;
        tax: string;
        currency: Currency;
        deductibleTaxAmount: string;
        taxAmountAdjusted: string;
        nonTaxableAmount: string;
        taxAmount: string;
        exemptAmount: string;
        taxableAmount: string;
    }
    export interface SalesInvoiceTaxInput extends VitalClientNodeInput {
        taxRate?: decimal | string;
        taxCategoryReference?: integer | string;
        taxReference?: integer | string;
        deductibleTaxRate?: decimal | string;
        isReverseCharge?: boolean | string;
        isTaxMandatory?: boolean | string;
        isSubjectToGlTaxExcludedAmount?: boolean | string;
        jurisdictionName?: string;
        taxCategory?: string;
        tax?: string;
        deductibleTaxAmount?: decimal | string;
        taxAmountAdjusted?: decimal | string;
        nonTaxableAmount?: decimal | string;
        taxAmount?: decimal | string;
        exemptAmount?: decimal | string;
        taxableAmount?: decimal | string;
    }
    export interface SalesInvoiceTaxBinding extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        taxRate: string;
        taxCategoryReference: TaxCategory;
        taxReference: Tax;
        deductibleTaxRate: string;
        isReverseCharge: boolean;
        isTaxMandatory: boolean;
        isSubjectToGlTaxExcludedAmount: boolean;
        jurisdictionName: string;
        document: SalesInvoice;
        taxCategory: string;
        tax: string;
        currency: Currency;
        deductibleTaxAmount: string;
        taxAmountAdjusted: string;
        nonTaxableAmount: string;
        taxAmount: string;
        exemptAmount: string;
        taxableAmount: string;
    }
    export interface SalesInvoiceTax$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface SalesInvoiceTax$Lookups {
        taxCategoryReference: QueryOperation<TaxCategory>;
        taxReference: QueryOperation<Tax>;
        currency: QueryOperation<Currency>;
    }
    export interface SalesInvoiceTax$Operations {
        query: QueryOperation<SalesInvoiceTax>;
        read: ReadOperation<SalesInvoiceTax>;
        aggregate: {
            read: AggregateReadOperation<SalesInvoiceTax>;
            query: AggregateQueryOperation<SalesInvoiceTax>;
        };
        asyncOperations: SalesInvoiceTax$AsyncOperations;
        lookups(dataOrId: string | { data: SalesInvoiceTaxInput }): SalesInvoiceTax$Lookups;
        getDefaults: GetDefaultsOperation<SalesInvoiceTax>;
    }
    export interface SalesOrderLineTax extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        taxRate: string;
        taxCategoryReference: TaxCategory;
        taxReference: Tax;
        deductibleTaxRate: string;
        isReverseCharge: boolean;
        isTaxMandatory: boolean;
        isSubjectToGlTaxExcludedAmount: boolean;
        jurisdictionName: string;
        document: SalesOrderLine;
        taxCategory: string;
        tax: string;
        currency: Currency;
        deductibleTaxAmount: string;
        taxAmountAdjusted: string;
        nonTaxableAmount: string;
        taxAmount: string;
        exemptAmount: string;
        taxableAmount: string;
    }
    export interface SalesOrderLineTaxInput extends VitalClientNodeInput {
        taxRate?: decimal | string;
        taxCategoryReference?: integer | string;
        taxReference?: integer | string;
        deductibleTaxRate?: decimal | string;
        isReverseCharge?: boolean | string;
        isTaxMandatory?: boolean | string;
        isSubjectToGlTaxExcludedAmount?: boolean | string;
        jurisdictionName?: string;
        taxCategory?: string;
        tax?: string;
        deductibleTaxAmount?: decimal | string;
        taxAmountAdjusted?: decimal | string;
        nonTaxableAmount?: decimal | string;
        taxAmount?: decimal | string;
        exemptAmount?: decimal | string;
        taxableAmount?: decimal | string;
    }
    export interface SalesOrderLineTaxBinding extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        taxRate: string;
        taxCategoryReference: TaxCategory;
        taxReference: Tax;
        deductibleTaxRate: string;
        isReverseCharge: boolean;
        isTaxMandatory: boolean;
        isSubjectToGlTaxExcludedAmount: boolean;
        jurisdictionName: string;
        document: SalesOrderLine;
        taxCategory: string;
        tax: string;
        currency: Currency;
        deductibleTaxAmount: string;
        taxAmountAdjusted: string;
        nonTaxableAmount: string;
        taxAmount: string;
        exemptAmount: string;
        taxableAmount: string;
    }
    export interface SalesOrderLineTax$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface SalesOrderLineTax$Lookups {
        taxCategoryReference: QueryOperation<TaxCategory>;
        taxReference: QueryOperation<Tax>;
        currency: QueryOperation<Currency>;
    }
    export interface SalesOrderLineTax$Operations {
        query: QueryOperation<SalesOrderLineTax>;
        read: ReadOperation<SalesOrderLineTax>;
        aggregate: {
            read: AggregateReadOperation<SalesOrderLineTax>;
            query: AggregateQueryOperation<SalesOrderLineTax>;
        };
        asyncOperations: SalesOrderLineTax$AsyncOperations;
        lookups(dataOrId: string | { data: SalesOrderLineTaxInput }): SalesOrderLineTax$Lookups;
        getDefaults: GetDefaultsOperation<SalesOrderLineTax>;
    }
    export interface SalesOrderTax extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        taxRate: string;
        taxCategoryReference: TaxCategory;
        taxReference: Tax;
        deductibleTaxRate: string;
        isReverseCharge: boolean;
        isTaxMandatory: boolean;
        isSubjectToGlTaxExcludedAmount: boolean;
        jurisdictionName: string;
        document: SalesOrder;
        taxCategory: string;
        tax: string;
        currency: Currency;
        deductibleTaxAmount: string;
        taxAmountAdjusted: string;
        nonTaxableAmount: string;
        taxAmount: string;
        exemptAmount: string;
        taxableAmount: string;
    }
    export interface SalesOrderTaxInput extends VitalClientNodeInput {
        taxRate?: decimal | string;
        taxCategoryReference?: integer | string;
        taxReference?: integer | string;
        deductibleTaxRate?: decimal | string;
        isReverseCharge?: boolean | string;
        isTaxMandatory?: boolean | string;
        isSubjectToGlTaxExcludedAmount?: boolean | string;
        jurisdictionName?: string;
        taxCategory?: string;
        tax?: string;
        deductibleTaxAmount?: decimal | string;
        taxAmountAdjusted?: decimal | string;
        nonTaxableAmount?: decimal | string;
        taxAmount?: decimal | string;
        exemptAmount?: decimal | string;
        taxableAmount?: decimal | string;
    }
    export interface SalesOrderTaxBinding extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        taxRate: string;
        taxCategoryReference: TaxCategory;
        taxReference: Tax;
        deductibleTaxRate: string;
        isReverseCharge: boolean;
        isTaxMandatory: boolean;
        isSubjectToGlTaxExcludedAmount: boolean;
        jurisdictionName: string;
        document: SalesOrder;
        taxCategory: string;
        tax: string;
        currency: Currency;
        deductibleTaxAmount: string;
        taxAmountAdjusted: string;
        nonTaxableAmount: string;
        taxAmount: string;
        exemptAmount: string;
        taxableAmount: string;
    }
    export interface SalesOrderTax$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface SalesOrderTax$Lookups {
        taxCategoryReference: QueryOperation<TaxCategory>;
        taxReference: QueryOperation<Tax>;
        currency: QueryOperation<Currency>;
    }
    export interface SalesOrderTax$Operations {
        query: QueryOperation<SalesOrderTax>;
        read: ReadOperation<SalesOrderTax>;
        aggregate: {
            read: AggregateReadOperation<SalesOrderTax>;
            query: AggregateQueryOperation<SalesOrderTax>;
        };
        asyncOperations: SalesOrderTax$AsyncOperations;
        lookups(dataOrId: string | { data: SalesOrderTaxInput }): SalesOrderTax$Lookups;
        getDefaults: GetDefaultsOperation<SalesOrderTax>;
    }
    export interface WorkInProgressSalesOrderLine extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        documentType: WorkInProgressDocumentType;
        salesOrderLine: SalesOrderLine;
        item: Item;
        site: Site;
        status: OrderType;
        startDate: string;
        endDate: string;
        expectedQuantity: string;
        actualQuantity: string;
        outstandingQuantity: string;
    }
    export interface WorkInProgressSalesOrderLineInput extends VitalClientNodeInput {
        documentType?: WorkInProgressDocumentType;
        item?: integer | string;
        site?: integer | string;
        status?: OrderType;
        startDate?: string;
        endDate?: string;
        expectedQuantity?: decimal | string;
        actualQuantity?: decimal | string;
        outstandingQuantity?: decimal | string;
    }
    export interface WorkInProgressSalesOrderLineBinding extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        documentType: WorkInProgressDocumentType;
        documentId: integer;
        originDocumentType: WorkInProgressDocumentType;
        originDocumentLine: integer;
        salesOrderLine: SalesOrderLine;
        item: Item;
        site: Site;
        status: OrderType;
        startDate: string;
        endDate: string;
        expectedQuantity: string;
        actualQuantity: string;
        outstandingQuantity: string;
        remainingQuantityToAllocate: string;
        documentNumber: string;
        documentLine: integer;
    }
    export interface WorkInProgressSalesOrderLine$Queries {
        getWorkInProgressQuantityPerItemSite: Node$Operation<
            {
                currentItem: integer | string;
                currentSite: integer | string;
                currentDate?: string;
                currentStatus?: OrderType;
                originDocumentType?: WorkInProgressDocumentType;
            },
            {
                supply: string;
                demand: string;
            }
        >;
    }
    export interface WorkInProgressSalesOrderLine$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface WorkInProgressSalesOrderLine$Lookups {
        item: QueryOperation<Item>;
        site: QueryOperation<Site>;
    }
    export interface WorkInProgressSalesOrderLine$Operations {
        query: QueryOperation<WorkInProgressSalesOrderLine>;
        read: ReadOperation<WorkInProgressSalesOrderLine>;
        aggregate: {
            read: AggregateReadOperation<WorkInProgressSalesOrderLine>;
            query: AggregateQueryOperation<WorkInProgressSalesOrderLine>;
        };
        queries: WorkInProgressSalesOrderLine$Queries;
        asyncOperations: WorkInProgressSalesOrderLine$AsyncOperations;
        lookups(dataOrId: string | { data: WorkInProgressSalesOrderLineInput }): WorkInProgressSalesOrderLine$Lookups;
        getDefaults: GetDefaultsOperation<WorkInProgressSalesOrderLine>;
    }
    export interface BaseOpenItemExtension {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        _constructor: string;
        dueDate: string;
        businessEntity: BusinessEntity;
        businessRelation: BaseBusinessRelation;
        businessEntityPayment: BusinessEntity;
        type: BusinessEntityType;
        currency: Currency;
        transactionAmountDue: string;
        transactionAmountDueSigned: string;
        transactionAmountPaid: string;
        transactionAmountPaidSigned: string;
        remainingTransactionAmountSigned: string;
        companyAmountDue: string;
        companyAmountDueSigned: string;
        companyAmountPaid: string;
        companyAmountPaidSigned: string;
        remainingCompanyAmountSigned: string;
        financialSiteAmountDue: string;
        financialSiteAmountPaid: string;
        status: OpenItemStatus;
        documentType: FinanceDocumentType;
        documentNumber: string;
        documentSysId: integer;
        discountFrom: DueDateType;
        discountDate: integer;
        discountType: PaymentTermDiscountOrPenaltyType;
        discountAmount: string;
        discountPaymentBeforeDate: string;
        displayDiscountPaymentDate: string;
        penaltyPaymentType: DiscountOrPenaltyType;
        penaltyAmount: string;
        closeReason: CloseReason;
        closeText: string;
        forcedAmountPaid: string;
        forcedAmountPaidSigned: string;
    }
    export interface BaseOpenItemInputExtension {
        _constructor?: string;
        dueDate?: string;
        businessEntity?: integer | string;
        businessEntityPayment?: integer | string;
        type?: BusinessEntityType;
        currency?: integer | string;
        transactionAmountDue?: decimal | string;
        transactionAmountPaid?: decimal | string;
        companyAmountDue?: decimal | string;
        companyAmountPaid?: decimal | string;
        financialSiteAmountDue?: decimal | string;
        financialSiteAmountPaid?: decimal | string;
        documentType?: FinanceDocumentType;
        documentNumber?: string;
        documentSysId?: integer | string;
        discountFrom?: DueDateType;
        discountDate?: integer | string;
        discountType?: PaymentTermDiscountOrPenaltyType;
        discountAmount?: decimal | string;
        discountPaymentBeforeDate?: string;
        penaltyPaymentType?: DiscountOrPenaltyType;
        penaltyAmount?: decimal | string;
        closeReason?: integer | string;
        closeText?: string;
        forcedAmountPaid?: decimal | string;
    }
    export interface BaseOpenItemBindingExtension {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        _constructor: string;
        dueDate: string;
        businessEntity: BusinessEntity;
        businessRelation: BaseBusinessRelation;
        businessEntityPayment: BusinessEntity;
        type: BusinessEntityType;
        currency: Currency;
        transactionAmountDue: string;
        transactionAmountDueSigned: string;
        transactionAmountPaid: string;
        transactionAmountPaidSigned: string;
        remainingTransactionAmountSigned: string;
        companyAmountDue: string;
        companyAmountDueSigned: string;
        companyAmountPaid: string;
        companyAmountPaidSigned: string;
        remainingCompanyAmountSigned: string;
        financialSiteAmountDue: string;
        financialSiteAmountPaid: string;
        status: OpenItemStatus;
        documentType: FinanceDocumentType;
        documentNumber: string;
        documentSysId: integer;
        discountFrom: DueDateType;
        discountDate: integer;
        discountType: PaymentTermDiscountOrPenaltyType;
        discountAmount: string;
        discountPaymentBeforeDate: string;
        displayDiscountPaymentDate: string;
        penaltyPaymentType: DiscountOrPenaltyType;
        penaltyAmount: string;
        closeReason: CloseReason;
        closeText: string;
        forcedAmountPaid: string;
        forcedAmountPaidSigned: string;
    }
    export interface ItemSiteCostExtension {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        itemSite: ItemSite;
        costCategory: CostCategory;
        fromDate: string;
        toDate: string;
        version: integer;
        stockUnit: UnitOfMeasure;
        forQuantity: string;
        isCalculated: boolean;
        materialCost: string;
        machineCost: string;
        laborCost: string;
        toolCost: string;
        indirectCost: string;
        totalCost: string;
        unitCost: string;
        areStockTransactionsAvailable: boolean;
    }
    export interface ItemSiteCostInputExtension {
        itemSite?: integer | string;
        costCategory?: integer | string;
        fromDate?: string;
        toDate?: string;
        version?: integer | string;
        forQuantity?: decimal | string;
        isCalculated?: boolean | string;
        materialCost?: decimal | string;
        machineCost?: decimal | string;
        laborCost?: decimal | string;
        toolCost?: decimal | string;
        indirectCost?: decimal | string;
    }
    export interface ItemSiteCostBindingExtension {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        itemSite: ItemSite;
        costCategory: CostCategory;
        fromDate: string;
        toDate: string;
        version: integer;
        stockUnit: UnitOfMeasure;
        forQuantity: string;
        isCalculated: boolean;
        materialCost: string;
        machineCost: string;
        laborCost: string;
        toolCost: string;
        indirectCost: string;
        totalCost: string;
        unitCost: string;
        areStockTransactionsAvailable: boolean;
    }
    export interface ItemSiteExtension {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        item: Item;
        site: Site;
        id: string;
        prodLeadTime: integer;
        stockUnit: UnitOfMeasure;
        safetyStock: string;
        batchQuantity: string;
        itemSiteCost: ClientCollection<ItemSiteCost>;
        stdCostValue: string;
        purchaseLeadTime: integer;
        isOrderToOrder: boolean;
        expectedQuantity: string;
        requiredQuantity: string;
        replenishmentMethod: ReplenishmentMethod;
        reorderPoint: string;
        preferredProcess: PreferredProcess;
        suppliers: ClientCollection<ItemSiteSupplier>;
        defaultSupplier: Supplier;
        indirectCostSection: IndirectCostSection;
        valuationMethod: CostValuationMethod;
        costs: ClientCollection<ItemSiteCost>;
        outboundDefaultLocation: Location;
        inboundDefaultLocation: Location;
        completedProductDefaultLocation: Location;
        stockRecords: ClientCollection<Stock>;
        inStockQuantity: string;
        allocatedQuantity: string;
        acceptedStockQuantity: string;
        rejectedStockQuantity: string;
        onQualityControlStockQuantity: string;
        inTransitStockQuantity: string;
        fifoCosts: ClientCollection<FifoValuationTier>;
        fifoCostValue: string;
        fifoCostSum: string;
        currentCost: string;
        stockValueAtStandardCost: string;
        stockValue: string;
        countingInProgress: boolean;
        areStockTransactionsAvailable: boolean;
        allocations: ClientCollection<StockAllocation>;
        allocationRecords: ClientCollection<StockAllocation>;
        inboundDefaultQualityValue: StockStatus;
        completedProductDefaultQualityValue: StockStatus;
        isSpecificItemTaxGroup: boolean;
        itemTaxGroup: ItemTaxGroup;
        economicOrderQuantity: string;
        stockValuationAtAverageCost: string;
        averageCostValue: string;
    }
    export interface ItemSiteInputExtension {
        site?: integer | string;
        prodLeadTime?: integer | string;
        safetyStock?: decimal | string;
        batchQuantity?: decimal | string;
        purchaseLeadTime?: integer | string;
        isOrderToOrder?: boolean | string;
        replenishmentMethod?: ReplenishmentMethod;
        reorderPoint?: decimal | string;
        preferredProcess?: PreferredProcess;
        indirectCostSection?: integer | string;
        valuationMethod?: CostValuationMethod;
        outboundDefaultLocation?: integer | string;
        inboundDefaultLocation?: integer | string;
        completedProductDefaultLocation?: integer | string;
        inboundDefaultQualityValue?: integer | string;
        completedProductDefaultQualityValue?: integer | string;
        isSpecificItemTaxGroup?: boolean | string;
        itemTaxGroup?: integer | string;
        economicOrderQuantity?: decimal | string;
        stockValuationAtAverageCost?: decimal | string;
    }
    export interface ItemSiteBindingExtension {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        item: Item;
        site: Site;
        id: string;
        prodLeadTime: integer;
        stockUnit: UnitOfMeasure;
        safetyStock: string;
        batchQuantity: string;
        itemSiteCost: ClientCollection<ItemSiteCost>;
        stdCostValue: string;
        purchaseLeadTime: integer;
        isOrderToOrder: boolean;
        expectedQuantity: string;
        requiredQuantity: string;
        replenishmentMethod: ReplenishmentMethod;
        reorderPoint: string;
        preferredProcess: PreferredProcess;
        suppliers: ClientCollection<ItemSiteSupplier>;
        defaultSupplier: Supplier;
        indirectCostSection: IndirectCostSection;
        valuationMethod: CostValuationMethod;
        costs: ClientCollection<ItemSiteCost>;
        outboundDefaultLocation: Location;
        inboundDefaultLocation: Location;
        completedProductDefaultLocation: Location;
        stockRecords: ClientCollection<Stock>;
        inStockQuantity: string;
        allocatedQuantity: string;
        acceptedStockQuantity: string;
        rejectedStockQuantity: string;
        onQualityControlStockQuantity: string;
        inTransitStockQuantity: string;
        fifoCosts: ClientCollection<FifoValuationTier>;
        fifoCostValue: string;
        fifoCostSum: string;
        currentCost: string;
        stockValueAtStandardCost: string;
        stockValue: string;
        countingInProgress: boolean;
        areStockTransactionsAvailable: boolean;
        allocations: ClientCollection<StockAllocation>;
        allocationRecords: ClientCollection<StockAllocation>;
        inboundDefaultQualityValue: StockStatus;
        completedProductDefaultQualityValue: StockStatus;
        isSpecificItemTaxGroup: boolean;
        itemTaxGroup: ItemTaxGroup;
        economicOrderQuantity: string;
        stockValuationAtAverageCost: string;
        averageCostValue: string;
    }
    export interface SiteExtension {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        id: string;
        name: string;
        description: string;
        isActive: boolean;
        legalCompany: Company;
        linkedSites: ClientCollection<Site>;
        siteGroups: ClientCollection<SiteGroup>;
        groupRoleSites: ClientCollection<GroupRoleSite>;
        hierarchyChartContent: TextStream;
        businessEntity: BusinessEntity;
        financialCurrency: Currency;
        isFinance: boolean;
        isPurchase: boolean;
        isInventory: boolean;
        isSales: boolean;
        isManufacturing: boolean;
        isProjectManagement: boolean;
        stockSite: Site;
        itemSites: ClientCollection<ItemSite>;
        primaryAddress: BusinessEntityAddress;
        financialSite: Site;
        isLocationManaged: boolean;
        defaultLocation: Location;
        sequenceNumberId: string;
        isSequenceNumberIdUsed: boolean;
        timeZone: string;
        country: Country;
        taxIdNumber: string;
        siret: string;
        currency: Currency;
        defaultStockStatus: StockStatus;
        storedDimensions: string;
        storedAttributes: string;
        isSalesReturnRequestApprovalManaged: boolean;
        salesReturnRequestDefaultApprover: User;
        salesReturnRequestSubstituteApprover: User;
        _syncTick: string;
        analyticalData: AnalyticalData;
    }
    export interface SiteInputExtension {
        id?: string;
        name?: string;
        description?: string;
        isActive?: boolean | string;
        legalCompany?: integer | string;
        businessEntity?: BusinessEntityInput;
        isFinance?: boolean | string;
        isPurchase?: boolean | string;
        isInventory?: boolean | string;
        isSales?: boolean | string;
        isManufacturing?: boolean | string;
        isProjectManagement?: boolean | string;
        primaryAddress?: integer | string;
        financialSite?: integer | string;
        isLocationManaged?: boolean | string;
        defaultLocation?: integer | string;
        sequenceNumberId?: string;
        timeZone?: string;
        defaultStockStatus?: integer | string;
        storedDimensions?: string;
        storedAttributes?: string;
        isSalesReturnRequestApprovalManaged?: boolean | string;
        salesReturnRequestDefaultApprover?: integer | string;
        salesReturnRequestSubstituteApprover?: integer | string;
        _syncTick?: decimal | string;
        analyticalData?: integer | string;
    }
    export interface SiteBindingExtension {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        id: string;
        name: string;
        description: string;
        isActive: boolean;
        legalCompany: Company;
        linkedSites: ClientCollection<Site>;
        siteGroups: ClientCollection<SiteGroup>;
        groupRoleSites: ClientCollection<GroupRoleSite>;
        hierarchyChartContent: TextStream;
        businessEntity: BusinessEntity;
        financialCurrency: Currency;
        isFinance: boolean;
        isPurchase: boolean;
        isInventory: boolean;
        isSales: boolean;
        isManufacturing: boolean;
        isProjectManagement: boolean;
        stockSite: Site;
        itemSites: ClientCollection<ItemSite>;
        primaryAddress: BusinessEntityAddress;
        financialSite: Site;
        isLocationManaged: boolean;
        defaultLocation: Location;
        sequenceNumberId: string;
        isSequenceNumberIdUsed: boolean;
        timeZone: string;
        country: Country;
        taxIdNumber: string;
        siret: string;
        currency: Currency;
        defaultStockStatus: StockStatus;
        storedDimensions: any;
        storedAttributes: any;
        isSalesReturnRequestApprovalManaged: boolean;
        salesReturnRequestDefaultApprover: User;
        salesReturnRequestSubstituteApprover: User;
        _syncTick: string;
        analyticalData: AnalyticalData;
    }
    export interface SiteExtension$Lookups {
        salesReturnRequestDefaultApprover: QueryOperation<User>;
        salesReturnRequestSubstituteApprover: QueryOperation<User>;
    }
    export interface SiteExtension$Operations {
        lookups(dataOrId: string | { data: SiteInput }): SiteExtension$Lookups;
    }
    export interface Package {
        '@sage/xtrem-sales/BaseLineToSalesDocumentLine': BaseLineToSalesDocumentLine$Operations;
        '@sage/xtrem-sales/ProformaInvoice': ProformaInvoice$Operations;
        '@sage/xtrem-sales/SalesCreditMemoReason': SalesCreditMemoReason$Operations;
        '@sage/xtrem-sales/SalesReturnRequestReason': SalesReturnRequestReason$Operations;
        '@sage/xtrem-sales/UnbilledAccountReceivableInputSet': UnbilledAccountReceivableInputSet$Operations;
        '@sage/xtrem-sales/UnbilledAccountReceivableResultLine': UnbilledAccountReceivableResultLine$Operations;
        '@sage/xtrem-sales/SalesInvoiceLineToSalesCreditMemoLine': SalesInvoiceLineToSalesCreditMemoLine$Operations;
        '@sage/xtrem-sales/SalesOrderLineToSalesInvoiceLine': SalesOrderLineToSalesInvoiceLine$Operations;
        '@sage/xtrem-sales/SalesOrderLineToSalesShipmentLine': SalesOrderLineToSalesShipmentLine$Operations;
        '@sage/xtrem-sales/SalesReturnRequestLineSalesCreditMemoLine': SalesReturnRequestLineSalesCreditMemoLine$Operations;
        '@sage/xtrem-sales/SalesReturnRequestLineToSalesReturnReceiptLine': SalesReturnRequestLineToSalesReturnReceiptLine$Operations;
        '@sage/xtrem-sales/SalesShipmentLineToSalesInvoiceLine': SalesShipmentLineToSalesInvoiceLine$Operations;
        '@sage/xtrem-sales/SalesShipmentLineToSalesReturnReceiptLine': SalesShipmentLineToSalesReturnReceiptLine$Operations;
        '@sage/xtrem-sales/SalesShipmentLineToSalesReturnRequestLine': SalesShipmentLineToSalesReturnRequestLine$Operations;
        '@sage/xtrem-sales/SalesCreditMemo': SalesCreditMemo$Operations;
        '@sage/xtrem-sales/SalesCreditMemoLine': SalesCreditMemoLine$Operations;
        '@sage/xtrem-sales/SalesOrderLineDiscountCharge': SalesOrderLineDiscountCharge$Operations;
        '@sage/xtrem-sales/SalesShipmentLineDiscountCharge': SalesShipmentLineDiscountCharge$Operations;
        '@sage/xtrem-sales/SalesInvoiceLineDiscountCharge': SalesInvoiceLineDiscountCharge$Operations;
        '@sage/xtrem-sales/SalesCreditMemoLineDiscountCharge': SalesCreditMemoLineDiscountCharge$Operations;
        '@sage/xtrem-sales/SalesInvoice': SalesInvoice$Operations;
        '@sage/xtrem-sales/SalesInvoiceLine': SalesInvoiceLine$Operations;
        '@sage/xtrem-sales/SalesOrder': SalesOrder$Operations;
        '@sage/xtrem-sales/SalesOrderLine': SalesOrderLine$Operations;
        '@sage/xtrem-sales/SalesReturnReceipt': SalesReturnReceipt$Operations;
        '@sage/xtrem-sales/SalesReturnReceiptLine': SalesReturnReceiptLine$Operations;
        '@sage/xtrem-sales/SalesReturnRequest': SalesReturnRequest$Operations;
        '@sage/xtrem-sales/SalesReturnRequestLine': SalesReturnRequestLine$Operations;
        '@sage/xtrem-sales/SalesShipment': SalesShipment$Operations;
        '@sage/xtrem-sales/SalesShipmentLine': SalesShipmentLine$Operations;
        '@sage/xtrem-sales/SalesCreditMemoLineTax': SalesCreditMemoLineTax$Operations;
        '@sage/xtrem-sales/SalesCreditMemoTax': SalesCreditMemoTax$Operations;
        '@sage/xtrem-sales/SalesInvoiceLineTax': SalesInvoiceLineTax$Operations;
        '@sage/xtrem-sales/SalesInvoiceTax': SalesInvoiceTax$Operations;
        '@sage/xtrem-sales/SalesOrderLineTax': SalesOrderLineTax$Operations;
        '@sage/xtrem-sales/SalesOrderTax': SalesOrderTax$Operations;
        '@sage/xtrem-sales/WorkInProgressSalesOrderLine': WorkInProgressSalesOrderLine$Operations;
    }
    export interface GraphApi
        extends Package,
            SageXtremAuditing$Package,
            SageXtremAuthorization$Package,
            SageXtremCommunication$Package,
            SageXtremCustomization$Package,
            SageXtremDashboard$Package,
            SageXtremDistribution$Package,
            SageXtremFinanceData$Package,
            SageXtremImportExport$Package,
            SageXtremLandedCost$Package,
            SageXtremMailer$Package,
            SageXtremMasterData$Package,
            SageXtremMetadata$Package,
            SageXtremReporting$Package,
            SageXtremRouting$Package,
            SageXtremScheduler$Package,
            SageXtremStockData$Package,
            SageXtremStructure$Package,
            SageXtremSynchronization$Package,
            SageXtremSystem$Package,
            SageXtremTax$Package,
            SageXtremUpload$Package,
            SageXtremWorkflow$Package {}
}
declare module '@sage/xtrem-sales-api' {
    export type * from '@sage/xtrem-sales-api-partial';
}
declare module '@sage/xtrem-auditing-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-sales-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-authorization-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-sales-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-communication-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-sales-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-customization-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-sales-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-dashboard-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-sales-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-distribution-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-sales-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-finance-data-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-sales-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-import-export-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-sales-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-landed-cost-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-sales-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-mailer-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-sales-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-master-data-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-sales-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-metadata-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-sales-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-reporting-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-sales-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-routing-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-sales-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-scheduler-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-sales-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-stock-data-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-sales-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-structure-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-sales-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-synchronization-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-sales-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-system-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-sales-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-tax-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-sales-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-upload-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-sales-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-workflow-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-sales-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-finance-data-api-partial' {
    import type {
        BaseOpenItemBindingExtension,
        BaseOpenItemExtension,
        BaseOpenItemInputExtension,
    } from '@sage/xtrem-sales-api';
    export interface BaseOpenItem extends BaseOpenItemExtension {}
    export interface BaseOpenItemBinding extends BaseOpenItemBindingExtension {}
    export interface BaseOpenItemInput extends BaseOpenItemInputExtension {}
}
declare module '@sage/xtrem-master-data-api-partial' {
    import type {
        ItemSiteBindingExtension,
        ItemSiteCostBindingExtension,
        ItemSiteCostExtension,
        ItemSiteCostInputExtension,
        ItemSiteExtension,
        ItemSiteInputExtension,
    } from '@sage/xtrem-sales-api';
    export interface ItemSiteCost extends ItemSiteCostExtension {}
    export interface ItemSiteCostBinding extends ItemSiteCostBindingExtension {}
    export interface ItemSiteCostInput extends ItemSiteCostInputExtension {}
    export interface ItemSite extends ItemSiteExtension {}
    export interface ItemSiteBinding extends ItemSiteBindingExtension {}
    export interface ItemSiteInput extends ItemSiteInputExtension {}
}
declare module '@sage/xtrem-system-api-partial' {
    import type {
        SiteBindingExtension,
        SiteExtension,
        SiteExtension$Lookups,
        SiteExtension$Operations,
        SiteInputExtension,
    } from '@sage/xtrem-sales-api';
    export interface Site extends SiteExtension {}
    export interface SiteBinding extends SiteBindingExtension {}
    export interface SiteInput extends SiteInputExtension {}
    export interface Site$Lookups extends SiteExtension$Lookups {}
    export interface Site$Operations extends SiteExtension$Operations {}
}
