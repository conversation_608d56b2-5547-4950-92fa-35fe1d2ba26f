mutation salesReturnRequest($storedAttributes: Json, $storedDimensions: Json) {
  xtremSales {
    salesReturnRequest {
      create(
        data: {
          site: "#US001"
          stockSite: "#US001"
          soldToCustomer: "#US019"
          shipToCustomer: "#US019"
          incoterm: "#EXW"
          lines: [
            {
              item: "#Chair"
              quantity: 10
              reason: "#TEST_EXCESS_QUANTITY_SHIPPED"
              storedAttributes: $storedAttributes
              storedDimensions: $storedDimensions
            }
          ]
        }
      ) {
        lines {
          query {
            edges {
              node {
                computedAttributes
                storedAttributes
                storedDimensions
              }
            }
          }
        }
      }
    }
  }
}
