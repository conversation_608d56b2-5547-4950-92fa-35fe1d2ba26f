mutation {
    xtremSales {
        salesShipment {
            update(
                data: { _id: "#SSH1", lines: [{ _action: "update", _sortValue: 130000, quantityInStockUnit: 18 }] }
            ) {
                totalAmountExcludingTax
                lines {
                    query {
                        edges {
                            node {
                                quantity
                                grossPrice
                                netPrice
                                amountExcludingTax
                            }
                        }
                    }
                }
            }
        }
    }
}
