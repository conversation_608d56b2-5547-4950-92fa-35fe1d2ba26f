{"data": {"xtremSales": {"salesShipment": {"update": {"totalAmountExcludingTax": "1530", "lines": {"query": {"edges": [{"node": {"quantity": "15", "grossPrice": "30", "netPrice": "6", "amountExcludingTax": "90", "discountCharges": {"query": {"edges": [{"node": {"sign": "increase", "valueType": "percentage", "basis": "30", "value": "20", "amount": "6"}}, {"node": {"sign": "decrease", "valueType": "amount", "basis": "30", "value": "30", "amount": "30"}}]}}}}, {"node": {"quantity": "15", "grossPrice": "80", "netPrice": "96", "amountExcludingTax": "1440", "discountCharges": {"query": {"edges": [{"node": {"sign": "increase", "valueType": "amount", "basis": "80", "value": "20", "amount": "20"}}, {"node": {"sign": "decrease", "valueType": "percentage", "basis": "80", "value": "5", "amount": "4"}}]}}}}]}}}}}}}