mutation {
    xtremSales {
        salesShipment {
            update(
                data: {
                    _id: "#SSH1"
                    lines: [
                        {
                            _action: "update"
                            _sortValue: 130000
                            grossPrice: 30
                            quantity: 15
                            discountCharges: [
                                { _action: "update", _id: "501", valueType: "percentage", value: 20 }
                                { _action: "update", _id: "502", valueType: "amount", value: 30 }
                            ]
                        }
                        {
                            _action: "update"
                            _sortValue: 130500
                            grossPrice: 80
                            quantity: 15
                            discountCharges: [
                                { _action: "update", _id: "511", valueType: "amount", value: 20 }
                                { _action: "update", _id: "512", valueType: "percentage", value: 5 }
                            ]
                        }
                    ]
                }
            ) {
                totalAmountExcludingTax
                lines {
                    query {
                        edges {
                            node {
                                quantity
                                grossPrice
                                netPrice
                                amountExcludingTax
                                discountCharges {
                                    query {
                                        edges {
                                            node {
                                                sign
                                                valueType
                                                basis
                                                value
                                                amount
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}
