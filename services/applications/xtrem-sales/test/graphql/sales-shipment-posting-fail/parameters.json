{"Post sales shipment fail when line is not allocated": {"executionMode": "normal", "input": {"documentID": "8"}, "output": {"errors": [{"extensions": {"code": "operation-error", "diagnoses": [{"message": "All the lines must be allocated to ship this document.", "path": [], "severity": 4}]}, "message": "Post to stock failed.", "locations": [{"line": 4, "column": 7}], "path": ["xtremSales", "salesShipment", "postToStock"]}], "data": {"xtremSales": {"salesShipment": {"postToStock": null}}}}}, "Post sales shipment fail when document is not ready to ship": {"executionMode": "normal", "input": {"documentID": "11"}, "output": {"errors": [{"extensions": {"code": "operation-error", "diagnoses": [{"message": "The status is not Ready to ship, the shipment cannot be posted.", "path": [], "severity": 4}]}, "message": "Post to stock failed.", "locations": [{"line": 4, "column": 7}], "path": ["xtremSales", "salesShipment", "postToStock"]}], "data": {"xtremSales": {"salesShipment": {"postToStock": null}}}}}, "Post sales shipment fail when bill to customer is on Hold": {"executionMode": "normal", "input": {"documentID": "8"}, "output": {"errors": [{"extensions": {"code": "operation-error", "diagnoses": [{"message": "The shipping date is in the future, the shipment cannot be posted.", "path": [], "severity": 4}]}, "message": "Post to stock failed.", "locations": [{"line": 4, "column": 7}], "path": ["xtremSales", "salesShipment", "postToStock"]}], "data": {"xtremSales": {"salesShipment": {"postToStock": null}}}}, "envConfigs": {"now": "2019-10-21T00:00:00Z"}}}