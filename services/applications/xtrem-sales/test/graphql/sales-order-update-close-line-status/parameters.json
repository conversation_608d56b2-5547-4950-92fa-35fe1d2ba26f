{"Update status to closed on a sales order line using mutation": {"input": {"properties": {"salesOrderLine": "_id:609"}}, "output": {"data": {"xtremSales": {"salesOrder": {"setSalesOrderLineCloseStatus": "salesOrderLineIsNowClosed"}}}}}, "Update status to closed on a sales order line already closed using mutation": {"input": {"properties": {"salesOrderLine": "_id:611"}}, "output": {"data": {"xtremSales": {"salesOrder": {"setSalesOrderLineCloseStatus": "salesOrderLineIsAlreadyClosed"}}}}}, "Update status to closed on a sales order line and update header status to closed using mutation": {"input": {"properties": {"salesOrderLine": "_id:612"}}, "output": {"data": {"xtremSales": {"salesOrder": {"setSalesOrderLineCloseStatus": "salesOrderLineIsNowClosed"}}}}}, "Update status to closed on a sales order line fail - line is allocated": {"executionMode": "normal", "input": {"properties": {"salesOrderLine": "_id:1200"}}, "output": {"data": {"xtremSales": {"salesOrder": {"setSalesOrderLineCloseStatus": null}}}, "errors": [{"extensions": {"code": "operation-error", "diagnoses": [{"message": "Remove the stock allocation before closing the line.", "path": [], "severity": 4}]}, "locations": [{"line": 4, "column": 13}], "message": "Set sales order line close status failed.", "path": ["xtremSales", "salesOrder", "setSalesOrderLineCloseStatus"]}]}}, "Update status to closed on a sales order line fail - an allocation request is in progress": {"executionMode": "normal", "input": {"properties": {"salesOrderLine": "_id:1215"}}, "output": {"data": {"xtremSales": {"salesOrder": {"setSalesOrderLineCloseStatus": null}}}, "errors": [{"extensions": {"code": "operation-error", "diagnoses": [{"message": "You cannot close the sales order line. An allocation request is in progress.", "path": [], "severity": 4}]}, "locations": [{"line": 4, "column": 13}], "message": "Set sales order line close status failed.", "path": ["xtremSales", "salesOrder", "setSalesOrderLineCloseStatus"]}]}}}