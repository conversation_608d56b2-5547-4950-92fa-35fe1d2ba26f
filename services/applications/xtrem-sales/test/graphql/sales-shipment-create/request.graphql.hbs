mutation {
    xtremSales {
        salesShipment {
            create(data: {{inputParameters}}) {
                    number
                    billToCustomer {
                        businessEntity { name }
                    }
                    billToLinkedAddress {
                        address { name }
                    }
                    currency {
                        id
                    }
                    companyCurrency {
                    	id
                	}
                    transactionCurrency {
                        id
                    }
                    companyFxRate
                    rateDescription
                    totalAmountExcludingTaxInCompanyCurrency
                    lines {
                        query {
                            edges {
                                node {
                                    stockUnit {
                                        id
                                    }
                                    unit{
                                        id
                                    }
                                    status
                                    itemDescription
                                    quantityInStockUnit
                                    amountExcludingTaxInCompanyCurrency
                                    discountCharges {
                                        query {
                                            edges {
                                                node {
                                                    sign
                                                    valueType
                                                    calculationBasis
                                                    calculationRule
                                                    basis
                                                    value
                                                    amount
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }

                    }
            }
        }
    }
}
