mutation {
    xtremSales {
        salesReturnReceipt {
            create(
                data: {
                    site: "#US001"
                    shipToCustomer: "#US019"
                    shipToAddress: {
                        name: "New name1"
                        addressLine1: "New addressLine1"
                        addressLine2: "New addressLine2"
                        city: "New city"
                        region: "HDF"
                        postcode: "12345"
                        locationPhoneNumber: "1234567890"
                        country: "#US"
                    }
                    shipToContact: {
                        title: "mr"
                        firstName: "New firstName1"
                        lastName: "New lastName1"
                        preferredName: "New preferredName1"
                        role: "mainContact"
                        position: "CEO"
                        locationPhoneNumber: "+27821234567"
                        email: "<EMAIL>"
                    }
                    lines: [
                        {
                            item: "#17891"
                            quantity: 600
                            stockDetails: [
                                {
                                    site: "#US001"
                                    item: "#17891"
                                    location: "#LOC3|US001|Loading dock"
                                    lotCreateData: "{\"id\": \"17\",\"item\": {\"_id\":\"17\"}}"
                                    status: "#A"
                                    stockUnit: "#LITER"
                                    quantityInStockUnit: 600
                                }
                            ]
                        }
                    ]
                }
            ) {
                site {
                    name
                }
                shipToCustomer {
                    businessEntity {
                        name
                    }
                }
                shipToCustomerAddress {
                    name
                }
                shipToAddress {
                    name
                    addressLine1
                    addressLine2
                    city
                    region
                    postcode
                    country {
                        id
                    }
                    locationPhoneNumber
                }
                shipToContact {
                    title
                    firstName
                    lastName
                    preferredName
                    role
                    position
                    locationPhoneNumber
                    email
                }
                lines {
                    query {
                        edges {
                            node {
                                origin
                                item {
                                    id
                                    name
                                }
                                itemDescription
                                quantity
                                quantityInStockUnit
                                unit {
                                    id
                                    name
                                }
                                stockUnit {
                                    id
                                    name
                                }
                                valuedCost
                                orderCost
                            }
                        }
                    }
                }
            }
        }
    }
}
