mutation salesShipment($storedAttributes: Json, $storedDimensions: Json) {
  xtremSales {
    salesShipment {
      create(
        data: {
          site: "#US001"
          shipToCustomer: "#US020"
          billToLinkedAddress: "#US019|1500"
          lines: [
            {
              item: "#Chemical D"
              quantity: 10
              unitToStockUnitConversionFactor: 1.1
              storedAttributes: $storedAttributes
              storedDimensions: $storedDimensions
              salesOrderLines: [{ linkedDocument: "#SO6|60600", quantity: 11 }]
            }
          ]
        }
      ) {
        number
      }
    }
  }
}
