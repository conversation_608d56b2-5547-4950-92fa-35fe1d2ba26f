{
    xtremSales {
        salesOrder {
            read(_id: {{inputParameters}}) {
                expectedDeliveryDate
                paymentTerm {
                    isActive
                    name
                    businessEntityType
                    description
                }
                doNotShipAfterDate
                doNotShipBeforeDate
                shippingDate
                requestedDeliveryDate
                currency {
                    name
                }
                soldToCustomer {
                    businessEntity {
                        name
                    }
                }
                soldToLinkedAddress {
                    name
                }
                soldToAddress {
                    name
                    concatenatedAddress
                    concatenatedAddressWithoutName
                }
                billToCustomer {
                    businessEntity {
                        name
                    }
                }
                billToLinkedAddress {
                    name
                }
                billToAddress {
                    name
                }
                shipToCustomer {
                    businessEntity {
                        name
                    }
                }
                shipToCustomerAddress {
                    businessEntity {
                        name
                    }
                    name
                }
                shipToAddress {
                    name
                }
                date
                status
                isOnHold
                customerNumber
                shippingStatus
                site {
                    name
                }
                stockSite {
                    name
                }

                incoterm {
                    name
                }
                deliveryMode {
                    name
                }
                deliveryLeadTime
                lines {
                    query {
                        edges {
                            node {
                                _id
                                status
                                priceOrigin
                                priceReason {
                                    isActive
                                    name
                                    description
                                    priority
                                }
                                shippingStatus
                                item {
                                    name
                                }
                                itemDescription
                                quantity
                                unit {
                                    name
                                }
                                stockUnit {
                                    name
                                }
                                availableQuantityInStockUnit
                                availableQuantityInSalesUnit
                                stockShortageInStockUnit
                                stockShortageInSalesUnit
                                stockShortageStatus
                                quantityToShipInProgressInSalesUnit
                                shippedQuantityInSalesUnit
                                taxableAmount
                                taxAmount
                                exemptAmount
                                taxDate
                                netPriceIncludingTax
                                amountIncludingTax
                                allocationStatus
                                quantityAllocated
                                remainingQuantityToShipInStockUnit
                                remainingQuantityToAllocate
                                stockAllocations {
                                    query {
                                        edges {
                                            node {
                                                _sourceId
                                                _id
                                                quantityInStockUnit
                                                stockRecord {
                                                    _id
                                                }
                                                serialNumbers {
                                                    query {
                                                        edges {
                                                            node {
                                                                _id
                                                            }
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
                totalAmountExcludingTax
                totalAmountIncludingTax
                totalTaxAmount
                totalTaxableAmount
                totalExemptAmount
                taxCalculationStatus
            }
        }
    }
}
