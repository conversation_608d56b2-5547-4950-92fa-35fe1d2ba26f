{"Sales invoice update": {"input": {"properties": {"_id": "#SI1", "incoterm": "#EXW", "paymentTerm": "#TEST_NET_15_CUSTOMER", "lines": [{"_id": "#SI1|10", "_action": "update", "quantity": 20, "netPriceExcludingTax": 2.45, "netPriceIncludingTax": 3.57, "grossPrice": 2.45}]}}, "output": {"data": {"xtremSales": {"salesInvoice": {"update": {"totalAmountExcludingTax": "49", "totalAmountIncludingTax": "49", "isPrinted": true, "incoterm": {"id": "EXW"}, "paymentTerm": {"name": "Net 15"}}}}}}}}