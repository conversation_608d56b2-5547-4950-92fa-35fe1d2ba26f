mutation {
        xtremSales {
            salesReturnRequest {
                update(data: {{inputParameters}}) {
                number
                incoterm {
                    id
                }
                deliveryMode {
                    name
                }
                returnType
                lines {
                    query {
                        edges {
                            node {
                                status
                                isReceiptExpected
                                isCreditMemoExpected
                            }
                        }
                    }
                }
            }
        }
    }
}
