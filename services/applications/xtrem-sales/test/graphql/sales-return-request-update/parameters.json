{"Sales return request update - 1": {"input": {"properties": {"_id": "#SRR04", "incoterm": "#CPT", "deliveryMode": "#AIR", "returnType": "receiptAndNoCreditMemo", "status": "draft", "lines": [{"_sortValue": "10", "_action": "update", "quantity": 11}]}}, "output": {"xtremSales": {"salesReturnRequest": {"update": {"number": "SRR04", "incoterm": {"id": "CPT"}, "deliveryMode": {"name": "Air"}, "returnType": "receiptAndNoCreditMemo", "lines": {"query": {"edges": [{"node": {"status": "inProgress", "isCreditMemoExpected": false, "isReceiptExpected": true}}]}}}}}}}, "Sales return request update - 2": {"input": {"properties": {"_id": "#SRR03", "incoterm": "#CPT", "deliveryMode": "#AIR", "status": "draft", "returnType": "receiptAndNoCreditMemo"}}, "output": {"xtremSales": {"salesReturnRequest": {"update": {"number": "SRR03", "incoterm": {"id": "CPT"}, "deliveryMode": {"name": "Air"}, "returnType": "receiptAndNoCreditMemo", "lines": {"query": {"edges": [{"node": {"status": "inProgress", "isReceiptExpected": true, "isCreditMemoExpected": false}}]}}}}}}}, "Sales return request update - 3": {"input": {"properties": {"_id": "#SRR02", "incoterm": "#CPT", "deliveryMode": "#AIR", "status": "draft", "returnType": "creditMemo"}}, "output": {"xtremSales": {"salesReturnRequest": {"update": {"number": "SRR02", "incoterm": {"id": "CPT"}, "deliveryMode": {"name": "Air"}, "returnType": "creditMemo", "lines": {"query": {"edges": [{"node": {"status": "draft", "isReceiptExpected": false, "isCreditMemoExpected": true}}]}}}}}}}, "Sales return request update - 4": {"input": {"properties": {"_id": "#SRR04", "incoterm": "#CPT", "deliveryMode": "#AIR", "returnType": "creditMemo", "status": "draft"}}, "output": {"xtremSales": {"salesReturnRequest": {"update": {"number": "SRR04", "incoterm": {"id": "CPT"}, "deliveryMode": {"name": "Air"}, "returnType": "creditMemo", "lines": {"query": {"edges": [{"node": {"isCreditMemoExpected": true, "isReceiptExpected": false, "status": "inProgress"}}]}}}}}}}}