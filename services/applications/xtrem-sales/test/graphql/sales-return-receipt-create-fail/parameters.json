{"Create a Sales return receipt without receipt line": {"input": {"properties": {"site": "#US001", "shipToCustomer": "#US019"}}, "output": {"errors": [{"extensions": {"code": "operation-error", "diagnoses": [{"message": "The document needs at least one line.", "path": [], "severity": 3}]}, "locations": [{"column": 13, "line": 4}], "message": "The record was not created.", "path": ["xtremSales", "salesReturnReceipt", "create"]}]}}, "Create a Sales return receipt with a return date in the future": {"input": {"properties": {"site": "#US001", "shipToCustomer": "#US019", "date": "2021-12-31", "lines": [{"item": "#LOT_TEST1", "quantity": 600}]}}, "output": {"errors": [{"extensions": {"code": "operation-error", "diagnoses": [{"message": "The return date cannot be later than today.", "path": ["date"], "severity": 3}]}, "locations": [{"column": 13, "line": 4}], "message": "The record was not created.", "path": ["xtremSales", "salesReturnReceipt", "create"]}]}, "envConfigs": {"today": "2021-10-06"}}}