mutation {
    xtremSales {
        salesReturnRequest {
            create(data: {{inputParameters}}) {
                status
                approvalStatus
                receiptStatus
                creditStatus
                returnType
                isPrinted
                isSent
                site {
                    name
                }
                stockSite {
                    name
                }
                requester{
                    firstName,
                    lastName
                }
                soldToCustomer { businessEntity {
                    name
                }}
                soldToLinkedAddress {
                    name
                }
                shipToCustomer { businessEntity {
                    name
                }}
                shipToCustomerAddress{
                        name
                }
                billToLinkedAddress {
                    name
                }
                billToCustomer { businessEntity {
                    name
                }}
                deliveryMode {
                    name
                }
                incoterm {
                    name
                }
                text{
                    value
                }
                lines {
                    query {
                        edges {
                            node {
                                origin
                                status
                                receiptStatus
                                creditStatus
                                isCreditMemoExpected
                                isReceiptExpected
                                originShippingSite {
                                    name
                                }
                                item {
                                id
                                    name
                                }
                                itemDescription
                                quantity
                                quantityInStockUnit
                                unit {
                                    id
                                    name
                                }
                                stockUnit {
                                    name
                                }
                                reason {
                                    name
                                }
                                text{
                                    value
                                }
                                storedDimensions
                                storedAttributes
                                computedAttributes
                            }
                        }
                    }
                }
            }
        }
    }
}
