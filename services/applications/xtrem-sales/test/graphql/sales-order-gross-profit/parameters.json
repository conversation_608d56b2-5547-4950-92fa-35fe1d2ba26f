{"Check gross profit on sales order": {"variables": {"filter": "{_id: '#SO32'}"}, "output": {"data": {"xtremSales": {"salesOrder": {"query": {"edges": [{"node": {"totalGrossProfit": "56.33", "lines": {"query": {"edges": [{"node": {"_id": "1208", "stockCostAmountInCompanyCurrency": "1968", "grossProfitAmountInCompanyCurrency": "3.94", "stockCostAmount": "1964.07", "grossProfitAmount": "3.93", "stockCostUnit": "122.754375", "grossProfit": "0.245625"}}, {"node": {"_id": "1209", "stockCostAmountInCompanyCurrency": "1200", "grossProfitAmountInCompanyCurrency": "52.5", "stockCostAmount": "1197.6", "grossProfitAmount": "52.4", "stockCostUnit": "119.76", "grossProfit": "5.24"}}]}}}}]}}}}}, "envConfigs": {"today": "2023-05-31"}, "executionMode": "normal"}}