{
  xtremSales {
    salesInvoice {
      read(_id: "{{_id}}") {
        number
        status
        date
        isPrinted
        site {
          businessEntity {
            taxIdNumber
          }
        }
        salesSiteName
        salesSiteTaxIdNumber
        salesSiteLinkedAddress {
          name
        }
        salesSiteAddress {
          name
        }
        salesSiteContact {
          firstName
        }
        billToCustomer {
          businessEntity {
            name
          }
        }
        billToCustomerName
        billToCustomerTaxIdNumber
        billToLinkedAddress {
          addressLine2
        }
        billToAddress {
          region
        }
        billToContact {
          preferredName
        }
        currency {
          symbol
        }
        paymentTerm {
          description
        }
        lines {
          query {
            edges {
              node {
                origin
                sourceDocumentNumber
                amountExcludingTax
                item {
                  id
                }
                quantityCreditedInProgressInSalesUnit
                quantityCreditedPostedInSalesUnit
                taxableAmount
                taxAmount
                exemptAmount
                taxDate
                netPriceIncludingTax
                amountIncludingTax
              }
            }
          }
        }
        totalAmountExcludingTax
        totalAmountIncludingTax
        totalTaxAmount
        totalTaxableAmount
        totalExemptAmount
        taxCalculationStatus
      }
    }
  }
}
