{
    xtremSales {
        salesReturnRequestReason {
            query(
                filter: "{_id:{ _in:['#TEST_EXCESS_QUANTITY_ORDERED','#TEST_EXCESS_QUANTITY_SHIPPED','#TEST_DEFECTIVE_GOODS']}}"
            ) {
                edges {
                    node {
                        isActive
                        name
                        description
                    }
                }
            }
        }
    }
}
