mutation salesInvoice($storedAttributes: Json, $storedDimensions: Json) {
  xtremSales {
    salesInvoice {
      create(
        data: {
          site: "#US001"
          billToCustomer: "#US019"
          dueDate: "2021-05-20"
          lines: [
            {
              item: "#SalesItem81"
              quantity: 600
              storedAttributes: $storedAttributes
              storedDimensions: $storedDimensions
            }
          ]
        }
      ) {
        number
        lines {
          query {
            edges {
              node {
                computedAttributes
                storedAttributes
                storedDimensions
              }
            }
          }
        }
      }
    }
  }
}
