{"Fails to update on ship to customer header updates": {"executionMode": "normal", "input": {"properties": {" _id": "#SSH6", "shipToCustomer": "#US019", "shipToCustomerAddress": "#US019|1500"}}, "output": {"errors": [{"extensions": {"code": "operation-error", "diagnoses": [{"message": "SalesShipment.shipToCustomer: cannot set value on frozen property", "path": ["shipToCustomer"], "severity": 4}]}, "message": "The record was not updated.", "locations": [{"line": 4, "column": 13}], "path": ["xtremSales", "salesShipment", "update"]}]}}}