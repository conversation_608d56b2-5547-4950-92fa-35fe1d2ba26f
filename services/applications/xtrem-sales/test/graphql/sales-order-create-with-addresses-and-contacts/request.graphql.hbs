mutation {
    xtremSales {
        salesOrder {
            create( data:  {{inputParameters}}  ) {
                incoterm {
                    id
                }
                deliveryLeadTime
                currency {
                    id
                }
                doNotShipAfterDate
                doNotShipBeforeDate
                requestedDeliveryDate
 				paymentTerm {
                    name
                }
                soldToCustomer { businessEntity {
                    name
                }}
                soldToLinkedAddress {
                    name
                }
                soldToAddress  {
                  name
                }
            	soldToContact  {
                  firstName
                }
                billToCustomer { businessEntity {
                    name
                }}
                billToLinkedAddress {
                    name
                }
                billToAddress  {
                  name
                }
                billToContact  {
                  firstName
                }
                shipToCustomer { businessEntity {
                    name
                }}
                shipToCustomerAddress {
                    businessEntity {
                        name
                    }
                    name
                }
                shipToAddress  {
                  name
                }
                shipToContact  {
                  firstName
                }
                lines {
                    query {
                        edges {
                            node {
                                status
                                shippingStatus
                                shipToCustomerAddress{
                                  name
                                }
                                shipToAddress{
                                    name
                                }
                                shipToContact{
                                    firstName
                                }
                                item {
                                    name
                                    image {
                                        value
                                    }
                                }
                                itemDescription
                                quantity
                                unit {
                                    name
                                }
                                stockUnit {
                                    name
                                }
                            }
                        }
                    }
                }
            }
        }
        }
}
