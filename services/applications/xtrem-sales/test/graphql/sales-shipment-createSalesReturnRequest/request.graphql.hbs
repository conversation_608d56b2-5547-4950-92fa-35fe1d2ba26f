mutation {
    xtremSales {
         salesShipment{
            createSalesReturnRequestFromShipmentLines(
                {{#each arrayProperties}}
                    {{@key}} :
                    [
                        {{#each this}}
                        {
                            {{#each this}}
                            {{#unless this.[0]}}
                                {{@key}} : {{this}}
                            {{else}}
                                {{@key}} : "{{this}}"
                            {{/unless}}
                            {{/each}}
                        }
                        {{/each}}
                    ]
                {{/each}})
            {
                status
                numberOfReturnRequests
                lineErrors {
                    lineNumber
                    linePosition
                    message
                }
            }         
        }
    }
}
