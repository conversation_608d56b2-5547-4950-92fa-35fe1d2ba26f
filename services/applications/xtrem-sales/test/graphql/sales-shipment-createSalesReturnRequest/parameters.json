{"Create sales return request using mutation": {"input": {"arrayProperties": {"salesDocumentLines": [{"salesDocumentLine": "#SSH4|130300", "quantityToProcess": 10}]}}, "output": {"createSalesReturnRequestFromShipmentLines": {"status": "salesShipmentReturnIsRequested", "numberOfReturnRequests": 1, "lineErrors": []}}, "envConfigs": {"today": "2021-07-23"}}, "Create sales return request using mutation empty array": {"input": {"arrayProperties": {"salesDocumentLines": []}}, "output": {"createSalesReturnRequestFromShipmentLines": {"status": "parametersAreIncorrect", "numberOfReturnRequests": 0, "lineErrors": []}}, "envConfigs": {"today": "2021-07-23"}}, "Create sales return request using mutation without quantityForRequesting": {"input": {"arrayProperties": {"salesDocumentLines": [{"salesDocumentLine": "#SSH4|130300"}]}}, "output": {"createSalesReturnRequestFromShipmentLines": {"status": "salesShipmentReturnIsRequested", "numberOfReturnRequests": 1, "lineErrors": []}}, "envConfigs": {"today": "2021-07-23"}}}