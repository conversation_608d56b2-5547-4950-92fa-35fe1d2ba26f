{"Update internalNote on a closed sales return receipt": {"input": {"properties": {"_id": "#SRREC05", "internalNote": {"value": "test"}}}, "output": {"errors": [{"message": "The record was not updated.", "locations": [{"line": 4, "column": 13}], "path": ["xtremSales", "salesReturnReceipt", "update"], "extensions": {"code": "operation-error", "diagnoses": [{"severity": 4, "path": ["internalNote"], "message": "SalesReturnReceipt.internalNote: cannot set value on frozen property"}]}}]}}, "Update internalNote on lines a closed sales return receipt": {"input": {"properties": {"_id": "#SRREC14", "lines": [{"_sortValue": 10, "_action": "update", "internalNote": {"value": "test"}}]}}, "output": {"errors": [{"message": "The record was not updated.", "locations": [{"line": 4, "column": 13}], "path": ["xtremSales", "salesReturnReceipt", "update"], "extensions": {"code": "operation-error", "diagnoses": [{"severity": 4, "path": ["lines", "", "internalNote"], "message": "SalesReturnReceiptLine.internalNote: cannot set value on frozen property"}]}}]}}}