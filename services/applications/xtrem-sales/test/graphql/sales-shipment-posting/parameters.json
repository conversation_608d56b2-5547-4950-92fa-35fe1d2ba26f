{"Post sales shipment": {"comments": ["not anymore possible to unit test this because the notification is now needed to trigger stock movement processing...", "we would need the possibility to send notification in test mode to perform this test"], "executionMode": "skip", "input": {"shipmentId": "#SSHUS0022021030001"}, "output": {"data": {"xtremSales": {"salesShipment": {"post": true}}}}}, "Post sales shipment with multiple lines": {"comments": ["not anymore possible to unit test this because the notification is now needed to trigger stock movement processing...", "we would need the possibility to send notification in test mode to perform this test"], "executionMode": "skip", "input": {"shipmentId": "#SSHUS0022021030003"}, "output": {"data": {"xtremSales": {"salesShipment": {"post": true}}}}}}