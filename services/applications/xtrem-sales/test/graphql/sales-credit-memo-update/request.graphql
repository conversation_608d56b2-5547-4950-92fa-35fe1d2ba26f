mutation {
    xtremSales {
        salesCreditMemo {
            update(
                data: {
                    _id: "#SCE1210001"
                    site: "#US002"
                    billToCustomer: "#US019"
                    incoterm: "#EXW"
                    paymentTerm: "#TEST_NET_15_CUSTOMER"
                    lines: [
                        {
                            _id: "1602"
                            _action: "update"
                            item: "#Consulting01"
                            stockUnit: "#EACH"
                            unit: "#EACH"
                            netPriceExcludingTax: 2.45
                            netPriceIncludingTax: 2.45
                            grossPrice: 2.45
                            quantity: 20
                        }
                    ]
                }
            ) {
                salesSiteName
                salesSiteTaxIdNumber
                billToCustomerName
                billToCustomerTaxIdNumber
                totalAmountExcludingTax
                totalAmountIncludingTax
                isPrinted
                incoterm {
                    id
                }
                paymentTerm {
                    name
                }
                lines {
                    query {
                        edges {
                            node {
                                itemDescription
                                unitToStockUnitConversionFactor
                                grossPrice
                                netPrice
                                amountExcludingTax
                                amountIncludingTax
                            }
                        }
                    }
                }
            }
        }
    }
}
