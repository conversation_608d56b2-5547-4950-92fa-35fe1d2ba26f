{
  xtremSales {
    salesReturnRequest {
      read(_id: "{{_id}}") {
        status
        approvalStatus
        receiptStatus
        creditStatus
        returnType
        isPrinted
        isSent
        site {
          name
        }
        stockSite {
          name
        }
        requester {
          firstName
          lastName
        }
        soldToCustomer {
          businessEntity {
            name
          }
        }
        soldToLinkedAddress {
          name
        }
        soldToAddress {
          name
          addressLine1
          addressLine2
          city
          region
          postcode
          country {
            name
          }
          locationPhoneNumber
        }
        soldToContact {
          title
          firstName
          lastName
          preferredName
          role
          position
          locationPhoneNumber
          email
        }
        shipToCustomer {
          businessEntity {
            name
          }
        }
        shipToCustomerAddress {
          address {
            name
          }
        }
        shipToAddress {
          name
          addressLine1
          addressLine2
          city
          region
          postcode
          country {
            name
          }
          locationPhoneNumber
        }
        shipToContact {
          title
          firstName
          lastName
          preferredName
          role
          position
          locationPhoneNumber
          email
        }
        billToLinkedAddress {
          name
        }
        billToCustomer {
          businessEntity {
            name
          }
        }

        billToAddress {
          name
          addressLine1
          addressLine2
          city
          region
          postcode
          country {
            name
          }
          locationPhoneNumber
        }
        billToContact {
          title
          firstName
          lastName
          preferredName
          role
          position
          locationPhoneNumber
          email
        }
        deliveryMode {
          name
        }
        incoterm {
          name
        }
        text {
          value
        }
        lines {
          query {
            edges {
              node {
                origin
                status
                receiptStatus
                creditStatus
                isCreditMemoExpected
                isReceiptExpected
                originShippingSite {
                  name
                }
                item {
                  id
                  name
                }
                itemDescription
                quantity
                quantityInStockUnit
                uQuantityReceiptAndToReceiveInSalesUnit
                quantityReceiptInSalesUnit
                quantityToReceiveInProgressInSalesUnit
                quantityCreditedInProgressInSalesUnit
                quantityCreditedPostedInSalesUnit
                unit {
                  name
                }
                stockUnit {
                  name
                }
                unitToStockUnitConversionFactor
                text {
                  value
                }
                storedDimensions
                storedAttributes
                computedAttributes
                salesShipmentLines {
                  query {
                    edges {
                      node {
                        quantityInStockUnit
                        quantity

                        document {
                          document {
                            number
                          }
                          quantity
                          quantityInStockUnit
                          unitToStockUnitConversionFactor
                          unit {
                            name
                          }
                          stockUnit {
                            name
                          }
                        }
                      }
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
  }
}
