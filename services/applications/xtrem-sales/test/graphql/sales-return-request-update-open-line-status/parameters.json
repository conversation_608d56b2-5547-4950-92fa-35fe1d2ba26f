{"Update status to open on a sales return request line return request status is closed using mutation": {"input": {"properties": {"salesReturnRequestLine": "#SRR10|20"}}, "output": {"setSalesReturnRequestLineOpenStatus": "salesReturnRequestLineIsNowOpen"}}, "Update status to open on a sales return request line when return request status is draft using mutation": {"input": {"properties": {"salesReturnRequestLine": "#SRR01|20"}}, "output": {"setSalesReturnRequestLineOpenStatus": "salesReturnRequestLineIsNowOpen"}}, "Update status to open on a sales return request line already open using mutation": {"input": {"properties": {"salesReturnRequestLine": "#SRR09|20"}}, "output": {"setSalesReturnRequestLineOpenStatus": "salesReturnRequestLineIsAlreadyOpen"}}, "Update status to open on a sales return request line with new quantity": {"input": {"properties": {"newQuantityInSalesUnit": 100, "salesReturnRequestLine": "#SRR11|10"}}, "output": {"setSalesReturnRequestLineOpenStatus": "salesReturnRequestLineIsNowOpen"}}}