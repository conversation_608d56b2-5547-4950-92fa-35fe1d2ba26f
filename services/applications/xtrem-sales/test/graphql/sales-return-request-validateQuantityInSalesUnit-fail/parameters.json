{"validateQuantityInSalesUnit fail - quantity lower than received quantity": {"input": {"properties": {"salesReturnRequestLine": "1728", "newQuantityInSalesUnit": "29"}}, "output": {"errors": [{"message": "Validate quantity in sales unit failed.", "locations": [{"line": 4, "column": 13}], "path": ["xtremSales", "salesReturnRequest", "validateQuantityInSalesUnit"], "extensions": {"code": "operation-error", "diagnoses": [{"severity": 4, "message": "The sales return line quantity cannot be lower than the already received quantity.", "path": []}]}}]}}, "validateQuantityInSalesUnit fail - quantity lower than credited quantity": {"input": {"properties": {"salesReturnRequestLine": "1731", "newQuantityInSalesUnit": "8"}}, "output": {"errors": [{"message": "Validate quantity in sales unit failed.", "locations": [{"line": 4, "column": 13}], "path": ["xtremSales", "salesReturnRequest", "validateQuantityInSalesUnit"], "extensions": {"code": "operation-error", "diagnoses": [{"severity": 4, "message": "The sales return line quantity cannot be lower than the already credited quantity.", "path": []}]}}]}}}