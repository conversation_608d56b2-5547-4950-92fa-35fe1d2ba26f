{"Create sales credit memo": {"input": {"properties": {"site": "#US002", "incoterm": "#EXW", "dueDate": "2021-07-28", "billToCustomer": "#US019", "taxes": [{"taxCategory": "test", "tax": "test", "nonTaxableAmount": 20, "taxRate": 22.2, "taxAmount": 37, "exemptAmount": 28, "taxableAmount": 37}], "lines": [{"item": "#SalesItem81", "quantity": 20, "unit": "#MI<PERSON><PERSON><PERSON>ER", "discountCharges": [{"sign": "increase", "valueType": "percentage", "calculationBasis": "grossPrice", "basis": 2, "value": 20}], "grossPrice": 2, "netPriceExcludingTax": 2.4, "netPriceIncludingTax": 2.4, "taxes": [{"taxCategory": "test", "tax": "test", "nonTaxableAmount": 20, "taxRate": 22.2, "taxAmount": 37, "exemptAmount": 28, "taxableAmount": 37}]}, {"item": "#SalesItem81", "unit": "#MI<PERSON><PERSON><PERSON>ER", "quantity": 20, "discount": 20, "grossPrice": 2, "discountCharges": [{"sign": "decrease", "valueType": "percentage", "calculationBasis": "grossPrice", "basis": 2, "value": 20}], "providerSiteAddress": {"name": "test"}, "consumptionAddress": {"name": "test"}, "taxes": [{"taxCategory": "test", "tax": "test", "nonTaxableAmount": 20, "taxRate": 22.2, "taxAmount": 37, "exemptAmount": 28, "taxableAmount": 37}]}]}}, "envConfigs": {"today": "2021-06-28"}, "output": {"create": {"number": "SCE2210001", "dueDate": "2021-07-28", "status": "draft", "date": "2021-06-28", "isPrinted": false, "site": {"businessEntity": {"taxIdNumber": "2020-1234-016"}}, "salesSiteName": "<PERSON>em<PERSON>", "salesSiteTaxIdNumber": "2020-1234-016", "salesSiteLinkedAddress": {"name": "US002"}, "salesSiteAddress": {"name": "US002"}, "salesSiteContact": null, "billToCustomer": {"businessEntity": {"name": "Dépot de TOULOUSE - Sud Ouest"}}, "billToCustomerName": "Dépot de TOULOUSE - Sud Ouest", "billToCustomerTaxIdNumber": "***********", "billToLinkedAddress": {"addressLine2": ""}, "billToAddress": {"region": "AZ"}, "billToContact": {"preferredName": "<PERSON>"}, "currency": {"symbol": "$"}, "paymentTerm": {"description": ""}, "taxes": {"query": {"edges": [{"node": {"taxCategory": "test", "tax": "test", "nonTaxableAmount": "20", "taxRate": "22.2", "taxAmount": "37", "exemptAmount": "28", "taxableAmount": "37"}}]}}, "lines": {"query": {"edges": [{"node": {"itemDescription": "Sales Item 81", "unitToStockUnitConversionFactor": "0.001", "origin": "direct", "taxableAmount": "37", "taxAmount": "37", "exemptAmount": "28", "taxDate": "2021-06-28", "netPriceIncludingTax": "4.25", "amountIncludingTax": "48", "amountExcludingTaxInCompanyCurrency": "48", "amountIncludingTaxInCompanyCurrency": "48", "taxes": {"query": {"edges": [{"node": {"taxCategory": "test", "tax": "test", "nonTaxableAmount": "20", "taxRate": "22.2", "taxAmount": "37", "exemptAmount": "28", "taxableAmount": "37"}}]}}, "discountCharges": {"query": {"edges": [{"node": {"sign": "increase", "valueType": "percentage", "calculationBasis": "grossPrice", "calculationRule": "byUnit", "basis": "2", "value": "20", "amount": "0.4"}}]}}}}, {"node": {"itemDescription": "Sales Item 81", "unitToStockUnitConversionFactor": "0.001", "origin": "direct", "taxableAmount": "37", "taxAmount": "37", "exemptAmount": "28", "taxDate": "2021-06-28", "netPriceIncludingTax": "3.45", "amountIncludingTax": "32", "amountExcludingTaxInCompanyCurrency": "32", "amountIncludingTaxInCompanyCurrency": "32", "taxes": {"query": {"edges": [{"node": {"taxCategory": "test", "tax": "test", "nonTaxableAmount": "20", "taxRate": "22.2", "taxAmount": "37", "exemptAmount": "28", "taxableAmount": "37"}}]}}, "discountCharges": {"query": {"edges": [{"node": {"sign": "decrease", "valueType": "percentage", "calculationBasis": "grossPrice", "calculationRule": "byUnit", "basis": "2", "value": "20", "amount": "0.4"}}]}}}}]}}, "totalAmountExcludingTax": "80", "totalAmountIncludingTax": "80", "totalTaxAmount": "0", "totalTaxableAmount": "0", "totalExemptAmount": "0", "taxCalculationStatus": "notDone", "creationNumber": "", "companyCurrency": {"id": "USD"}, "companyFxRate": "1", "rateDescription": "1 USD = 1 USD", "totalAmountIncludingTaxInCompanyCurrency": "80", "totalAmountExcludingTaxInCompanyCurrency": "80"}}}, "Create sales credit memo null addresses": {"input": {"properties": {"number": "SCM1234567", "site": "#US002", "incoterm": "#EXW", "billToCustomer": "#US019", "lines": [{"item": "#SalesItem81", "quantity": 20, "charge": 20, "grossPrice": 2, "discountCharges": [{"sign": "increase", "valueType": "percentage", "calculationBasis": "grossPrice", "basis": 2, "value": 20}]}, {"item": "#SalesItem81", "quantity": 20, "grossPrice": 2, "discountCharges": [{"sign": "increase", "valueType": "percentage", "calculationBasis": "grossPrice", "basis": 2, "value": 20}], "consumptionLinkedAddress": "#500|800", "providerSiteLinkedAddress": "#500|900"}], "billToContact": {"title": "mr", "firstName": "test", "lastName": "test"}, "billToAddress": {"name": "test"}, "salesSiteAddress": {"name": "test"}, "salesSiteContact": {"title": "mr", "firstName": "test", "lastName": "test"}}}, "envConfigs": {"today": "2021-06-28"}, "output": {"create": {"number": "SCM1234567", "dueDate": "2021-06-28", "status": "draft", "date": "2021-06-28", "isPrinted": false, "site": {"businessEntity": {"taxIdNumber": "2020-1234-016"}}, "salesSiteName": "<PERSON>em<PERSON>", "salesSiteTaxIdNumber": "2020-1234-016", "salesSiteLinkedAddress": {"name": "US002"}, "salesSiteAddress": {"name": "test"}, "salesSiteContact": {"firstName": "test"}, "billToCustomer": {"businessEntity": {"name": "Dépot de TOULOUSE - Sud Ouest"}}, "billToCustomerName": "Dépot de TOULOUSE - Sud Ouest", "billToCustomerTaxIdNumber": "***********", "billToLinkedAddress": {"addressLine2": ""}, "billToAddress": {"region": ""}, "billToContact": {"preferredName": ""}, "currency": {"symbol": "$"}, "paymentTerm": {"description": ""}, "taxes": {"query": {"edges": []}}, "lines": {"query": {"edges": [{"node": {"itemDescription": "Sales Item 81", "unitToStockUnitConversionFactor": "0.001", "origin": "direct", "taxableAmount": "0", "taxAmount": "0", "exemptAmount": "0", "taxDate": "2021-06-28", "netPriceIncludingTax": "2.4", "amountIncludingTax": "48", "amountExcludingTaxInCompanyCurrency": "48", "amountIncludingTaxInCompanyCurrency": "48", "taxes": {"query": {"edges": []}}, "discountCharges": {"query": {"edges": [{"node": {"sign": "increase", "valueType": "percentage", "calculationBasis": "grossPrice", "calculationRule": "byUnit", "basis": "2", "value": "20", "amount": "0.4"}}]}}}}, {"node": {"itemDescription": "Sales Item 81", "unitToStockUnitConversionFactor": "0.001", "origin": "direct", "taxableAmount": "0", "taxAmount": "0", "exemptAmount": "0", "taxDate": "2021-06-28", "netPriceIncludingTax": "2.4", "amountIncludingTax": "48", "amountExcludingTaxInCompanyCurrency": "48", "amountIncludingTaxInCompanyCurrency": "48", "taxes": {"query": {"edges": []}}, "discountCharges": {"query": {"edges": [{"node": {"sign": "increase", "valueType": "percentage", "calculationBasis": "grossPrice", "calculationRule": "byUnit", "basis": "2", "value": "20", "amount": "0.4"}}]}}}}]}}, "totalAmountExcludingTax": "96", "totalAmountIncludingTax": "96", "totalTaxAmount": "0", "totalTaxableAmount": "0", "totalExemptAmount": "0", "taxCalculationStatus": "notDone", "creationNumber": "", "companyCurrency": {"id": "USD"}, "companyFxRate": "1", "rateDescription": "1 USD = 1 USD", "totalAmountIncludingTaxInCompanyCurrency": "96", "totalAmountExcludingTaxInCompanyCurrency": "96"}}}, "Create sales credit memo with discount and charge generated": {"input": {"properties": {"site": "#US001", "incoterm": "#EXW", "billToCustomer": "#US019", "currency": "#ZAR", "lines": [{"item": "#SalesItem81", "quantity": 20}, {"item": "#SalesItem81", "quantity": 40}]}}, "envConfigs": {"today": "2021-06-28"}, "output": {"create": {"number": "SCE1210008", "dueDate": "2021-06-28", "status": "draft", "date": "2021-06-28", "isPrinted": false, "site": {"businessEntity": {"taxIdNumber": "***********"}}, "salesSiteName": "Chem. Atlanta", "salesSiteTaxIdNumber": "***********", "salesSiteLinkedAddress": {"name": "US001"}, "salesSiteAddress": {"name": "US001"}, "salesSiteContact": null, "billToCustomer": {"businessEntity": {"name": "Dépot de TOULOUSE - Sud Ouest"}}, "billToCustomerName": "Dépot de TOULOUSE - Sud Ouest", "billToCustomerTaxIdNumber": "***********", "billToLinkedAddress": {"addressLine2": ""}, "billToAddress": {"region": "AZ"}, "billToContact": {"preferredName": "<PERSON>"}, "currency": {"symbol": "R"}, "paymentTerm": {"description": ""}, "taxes": {"query": {"edges": []}}, "lines": {"query": {"edges": [{"node": {"itemDescription": "Sales Item 81", "unitToStockUnitConversionFactor": "0.001", "origin": "direct", "taxableAmount": "0", "taxAmount": "0", "exemptAmount": "0", "taxDate": "2021-06-28", "netPriceIncludingTax": "0", "amountIncludingTax": "0", "amountExcludingTaxInCompanyCurrency": "0", "amountIncludingTaxInCompanyCurrency": "0", "taxes": {"query": {"edges": []}}, "discountCharges": {"query": {"edges": [{"node": {"sign": "increase", "valueType": "percentage", "calculationBasis": "grossPrice", "calculationRule": "byUnit", "basis": "0", "value": "0", "amount": "0"}}, {"node": {"sign": "decrease", "valueType": "percentage", "calculationBasis": "grossPrice", "calculationRule": "byUnit", "basis": "0", "value": "0", "amount": "0"}}]}}}}, {"node": {"itemDescription": "Sales Item 81", "unitToStockUnitConversionFactor": "0.001", "origin": "direct", "taxableAmount": "0", "taxAmount": "0", "exemptAmount": "0", "taxDate": "2021-06-28", "netPriceIncludingTax": "0", "amountIncludingTax": "0", "amountExcludingTaxInCompanyCurrency": "0", "amountIncludingTaxInCompanyCurrency": "0", "taxes": {"query": {"edges": []}}, "discountCharges": {"query": {"edges": [{"node": {"sign": "increase", "valueType": "percentage", "calculationBasis": "grossPrice", "calculationRule": "byUnit", "basis": "0", "value": "0", "amount": "0"}}, {"node": {"sign": "decrease", "valueType": "percentage", "calculationBasis": "grossPrice", "calculationRule": "byUnit", "basis": "0", "value": "0", "amount": "0"}}]}}}}]}}, "totalAmountExcludingTax": "0", "totalAmountIncludingTax": "0", "totalTaxAmount": "0", "totalTaxableAmount": "0", "totalExemptAmount": "0", "taxCalculationStatus": "notDone", "creationNumber": "", "companyCurrency": {"id": "USD"}, "companyFxRate": "15.87", "rateDescription": "1 ZAR = 15.87 USD", "totalAmountIncludingTaxInCompanyCurrency": "0", "totalAmountExcludingTaxInCompanyCurrency": "0"}}}, "Create sales credit memo for FR legislation": {"input": {"properties": {"number": "FR1234567", "site": "#ETS1-S01", "incoterm": "#EXW", "dueDate": "2021-07-28", "billToCustomer": "#US019", "taxes": [{"taxCategory": "test", "tax": "test", "nonTaxableAmount": 20, "taxRate": 22.2, "taxAmount": 16.42, "taxAmountAdjusted": 16.42, "exemptAmount": 28, "taxableAmount": 74}], "lines": [{"item": "#SalesItem81", "quantity": 20, "unit": "#MI<PERSON><PERSON><PERSON>ER", "grossPrice": 2, "discountCharges": [{"sign": "increase", "valueType": "percentage", "calculationBasis": "grossPrice", "basis": 2, "value": 20}], "netPriceExcludingTax": 2.4, "netPriceIncludingTax": 2.4, "taxes": [{"taxCategory": "test", "tax": "test", "nonTaxableAmount": 20, "taxRate": 22.2, "taxAmount": 8.21, "taxAmountAdjusted": 8.21, "exemptAmount": 28, "taxableAmount": 37}]}, {"item": "#SalesItem81", "unit": "#MI<PERSON><PERSON><PERSON>ER", "quantity": 20, "discount": 20, "grossPrice": 2, "discountCharges": [{"sign": "decrease", "valueType": "percentage", "calculationBasis": "grossPrice", "basis": 2, "value": 20}], "providerSiteAddress": {"name": "test"}, "consumptionAddress": {"name": "test"}, "taxes": [{"taxCategory": "test", "tax": "test", "nonTaxableAmount": 20, "taxRate": 22.2, "taxAmount": 8.21, "taxAmountAdjusted": 8.21, "exemptAmount": 28, "taxableAmount": 37}]}]}}, "envConfigs": {"today": "2021-12-21"}, "output": {"create": {"number": "FR1234567", "dueDate": "2021-07-28", "status": "draft", "date": "2021-12-21", "isPrinted": false, "site": {"businessEntity": {"taxIdNumber": "FR123456789"}}, "salesSiteName": "Siège social S01  PARIS", "salesSiteTaxIdNumber": "FR123456789", "salesSiteLinkedAddress": {"name": "Siege Social S1 Paris"}, "salesSiteAddress": {"name": "Siege Social S1 Paris"}, "salesSiteContact": null, "billToCustomer": {"businessEntity": {"name": "Dépot de TOULOUSE - Sud Ouest"}}, "billToCustomerName": "Dépot de TOULOUSE - Sud Ouest", "billToCustomerTaxIdNumber": "***********", "billToLinkedAddress": {"addressLine2": ""}, "billToAddress": {"region": "AZ"}, "billToContact": {"preferredName": "<PERSON>"}, "currency": {"symbol": "$"}, "paymentTerm": {"description": ""}, "taxes": {"query": {"edges": [{"node": {"taxCategory": "test", "tax": "test", "nonTaxableAmount": "0", "taxRate": "22.2", "taxAmount": "16.42", "exemptAmount": "0", "taxableAmount": "74"}}]}}, "lines": {"query": {"edges": [{"node": {"itemDescription": "Sales Item 81", "unitToStockUnitConversionFactor": "0.001", "origin": "direct", "taxableAmount": "17", "taxAmount": "8.21", "exemptAmount": "28", "taxDate": "2021-12-21", "netPriceIncludingTax": "2.81", "amountIncludingTax": "56.21", "amountExcludingTaxInCompanyCurrency": "47.9", "amountIncludingTaxInCompanyCurrency": "56.1", "taxes": {"query": {"edges": [{"node": {"taxCategory": "test", "tax": "test", "nonTaxableAmount": "20", "taxRate": "22.2", "taxAmount": "8.21", "exemptAmount": "28", "taxableAmount": "37"}}]}}, "discountCharges": {"query": {"edges": [{"node": {"sign": "increase", "valueType": "percentage", "calculationBasis": "grossPrice", "calculationRule": "byUnit", "basis": "2", "value": "20", "amount": "0.4"}}]}}}}, {"node": {"itemDescription": "Sales Item 81", "unitToStockUnitConversionFactor": "0.001", "origin": "direct", "taxableAmount": "17", "taxAmount": "8.21", "exemptAmount": "28", "taxDate": "2021-12-21", "netPriceIncludingTax": "2.01", "amountIncludingTax": "40.21", "amountExcludingTaxInCompanyCurrency": "31.94", "amountIncludingTaxInCompanyCurrency": "40.13", "taxes": {"query": {"edges": [{"node": {"taxCategory": "test", "tax": "test", "nonTaxableAmount": "20", "taxRate": "22.2", "taxAmount": "8.21", "exemptAmount": "28", "taxableAmount": "37"}}]}}, "discountCharges": {"query": {"edges": [{"node": {"sign": "decrease", "valueType": "percentage", "calculationBasis": "grossPrice", "calculationRule": "byUnit", "basis": "2", "value": "20", "amount": "0.4"}}]}}}}]}}, "totalAmountExcludingTax": "80", "totalAmountIncludingTax": "96.42", "totalTaxAmount": "16.42", "totalTaxableAmount": "34", "totalExemptAmount": "0", "taxCalculationStatus": "done", "creationNumber": "FR1234567", "companyCurrency": {"id": "EUR"}, "companyFxRate": "1", "rateDescription": "1 USD = 0.********* EUR", "totalAmountIncludingTaxInCompanyCurrency": "96.23", "totalAmountExcludingTaxInCompanyCurrency": "79.84"}}}, "Create sales credit memo for FR legislation with credit memo number": {"input": {"properties": {"site": "#ETS1-S01", "incoterm": "#EXW", "dueDate": "2021-07-28", "billToCustomer": "#US019", "taxes": [{"taxCategory": "test", "tax": "test", "nonTaxableAmount": 20, "taxRate": 22.2, "taxAmount": 16.42, "taxAmountAdjusted": 16.42, "exemptAmount": 28, "taxableAmount": 37}], "lines": [{"item": "#SalesItem81", "quantity": 20, "unit": "#MI<PERSON><PERSON><PERSON>ER", "discountCharges": [{"sign": "increase", "valueType": "percentage", "calculationBasis": "grossPrice", "basis": 2, "value": 20}], "grossPrice": 2, "netPriceExcludingTax": 2.4, "netPriceIncludingTax": 2.4, "taxes": [{"taxCategory": "test", "tax": "test", "nonTaxableAmount": 20, "taxRate": 22.2, "taxAmount": 8.21, "taxAmountAdjusted": 8.21, "exemptAmount": 28, "taxableAmount": 37}]}, {"item": "#SalesItem81", "unit": "#MI<PERSON><PERSON><PERSON>ER", "quantity": 20, "discount": 20, "grossPrice": 2, "discountCharges": [{"sign": "decrease", "valueType": "percentage", "calculationBasis": "grossPrice", "basis": 2, "value": 20}], "providerSiteAddress": {"name": "test"}, "consumptionAddress": {"name": "test"}, "taxes": [{"taxCategory": "test", "tax": "test", "nonTaxableAmount": 20, "taxRate": 22.2, "taxAmount": 8.21, "taxAmountAdjusted": 8.21, "exemptAmount": 28, "taxableAmount": 37}]}]}}, "envConfigs": {"today": "2021-12-21"}, "output": {"create": {"number": "SCEE210001", "dueDate": "2021-07-28", "status": "draft", "date": "2021-12-21", "isPrinted": false, "site": {"businessEntity": {"taxIdNumber": "FR123456789"}}, "salesSiteName": "Siège social S01  PARIS", "salesSiteTaxIdNumber": "FR123456789", "salesSiteLinkedAddress": {"name": "Siege Social S1 Paris"}, "salesSiteAddress": {"name": "Siege Social S1 Paris"}, "salesSiteContact": null, "billToCustomer": {"businessEntity": {"name": "Dépot de TOULOUSE - Sud Ouest"}}, "billToCustomerName": "Dépot de TOULOUSE - Sud Ouest", "billToCustomerTaxIdNumber": "***********", "billToLinkedAddress": {"addressLine2": ""}, "billToAddress": {"region": "AZ"}, "billToContact": {"preferredName": "<PERSON>"}, "currency": {"symbol": "$"}, "paymentTerm": {"description": ""}, "taxes": {"query": {"edges": [{"node": {"taxCategory": "test", "tax": "test", "nonTaxableAmount": "0", "taxRate": "22.2", "taxAmount": "16.42", "exemptAmount": "0", "taxableAmount": "74"}}]}}, "lines": {"query": {"edges": [{"node": {"itemDescription": "Sales Item 81", "unitToStockUnitConversionFactor": "0.001", "origin": "direct", "taxableAmount": "17", "taxAmount": "8.21", "exemptAmount": "28", "taxDate": "2021-12-21", "netPriceIncludingTax": "2.81", "amountIncludingTax": "56.21", "amountExcludingTaxInCompanyCurrency": "47.9", "amountIncludingTaxInCompanyCurrency": "56.1", "taxes": {"query": {"edges": [{"node": {"taxCategory": "test", "tax": "test", "nonTaxableAmount": "20", "taxRate": "22.2", "taxAmount": "8.21", "exemptAmount": "28", "taxableAmount": "37"}}]}}, "discountCharges": {"query": {"edges": [{"node": {"sign": "increase", "valueType": "percentage", "calculationBasis": "grossPrice", "calculationRule": "byUnit", "basis": "2", "value": "20", "amount": "0.4"}}]}}}}, {"node": {"itemDescription": "Sales Item 81", "unitToStockUnitConversionFactor": "0.001", "origin": "direct", "taxableAmount": "17", "taxAmount": "8.21", "exemptAmount": "28", "taxDate": "2021-12-21", "netPriceIncludingTax": "2.01", "amountIncludingTax": "40.21", "amountExcludingTaxInCompanyCurrency": "31.94", "amountIncludingTaxInCompanyCurrency": "40.13", "taxes": {"query": {"edges": [{"node": {"taxCategory": "test", "tax": "test", "nonTaxableAmount": "20", "taxRate": "22.2", "taxAmount": "8.21", "exemptAmount": "28", "taxableAmount": "37"}}]}}, "discountCharges": {"query": {"edges": [{"node": {"sign": "decrease", "valueType": "percentage", "calculationBasis": "grossPrice", "calculationRule": "byUnit", "basis": "2", "value": "20", "amount": "0.4"}}]}}}}]}}, "totalAmountExcludingTax": "80", "totalAmountIncludingTax": "96.42", "totalTaxAmount": "16.42", "totalTaxableAmount": "34", "totalExemptAmount": "0", "taxCalculationStatus": "done", "creationNumber": "SCEE210001", "companyCurrency": {"id": "EUR"}, "companyFxRate": "1", "rateDescription": "1 USD = 0.********* EUR", "totalAmountIncludingTaxInCompanyCurrency": "96.23", "totalAmountExcludingTaxInCompanyCurrency": "79.84"}}}, "Create sales credit memo with company currency conversion": {"input": {"properties": {"site": "#US002", "incoterm": "#EXW", "dueDate": "2021-07-28", "billToCustomer": "#US019", "currency": "#EUR", "taxes": [{"taxCategory": "test", "tax": "test", "nonTaxableAmount": 20, "taxRate": 22.2, "taxAmount": 37, "exemptAmount": 28, "taxableAmount": 37}], "lines": [{"item": "#SalesItem81", "quantity": 20, "unit": "#MI<PERSON><PERSON><PERSON>ER", "discountCharges": [{"sign": "increase", "valueType": "percentage", "calculationBasis": "grossPrice", "basis": 2, "value": 20}], "grossPrice": 2, "netPriceExcludingTax": 2.4, "netPriceIncludingTax": 2.4, "taxes": [{"taxCategory": "test", "tax": "test", "nonTaxableAmount": 20, "taxRate": 22.2, "taxAmount": 37, "exemptAmount": 28, "taxableAmount": 37}]}, {"item": "#SalesItem81", "unit": "#MI<PERSON><PERSON><PERSON>ER", "quantity": 20, "discount": 20, "grossPrice": 2, "discountCharges": [{"sign": "decrease", "valueType": "percentage", "calculationBasis": "grossPrice", "basis": 2, "value": 20}], "providerSiteAddress": {"name": "test"}, "consumptionAddress": {"name": "test"}, "taxes": [{"taxCategory": "test", "tax": "test", "nonTaxableAmount": 20, "taxRate": 22.2, "taxAmount": 37, "exemptAmount": 28, "taxableAmount": 37}]}]}}, "envConfigs": {"today": "2022-04-13"}, "output": {"create": {"number": "SCE2220001", "dueDate": "2021-07-28", "status": "draft", "date": "2022-04-13", "isPrinted": false, "site": {"businessEntity": {"taxIdNumber": "2020-1234-016"}}, "salesSiteName": "<PERSON>em<PERSON>", "salesSiteTaxIdNumber": "2020-1234-016", "salesSiteLinkedAddress": {"name": "US002"}, "salesSiteAddress": {"name": "US002"}, "salesSiteContact": null, "billToCustomer": {"businessEntity": {"name": "Dépot de TOULOUSE - Sud Ouest"}}, "billToCustomerName": "Dépot de TOULOUSE - Sud Ouest", "billToCustomerTaxIdNumber": "***********", "billToLinkedAddress": {"addressLine2": ""}, "billToAddress": {"region": "AZ"}, "billToContact": {"preferredName": "<PERSON>"}, "currency": {"symbol": "€"}, "paymentTerm": {"description": ""}, "taxes": {"query": {"edges": [{"node": {"taxCategory": "test", "tax": "test", "nonTaxableAmount": "20", "taxRate": "22.2", "taxAmount": "37", "exemptAmount": "28", "taxableAmount": "37"}}]}}, "lines": {"query": {"edges": [{"node": {"itemDescription": "Sales Item 81", "unitToStockUnitConversionFactor": "0.001", "origin": "direct", "taxableAmount": "37", "taxAmount": "37", "exemptAmount": "28", "taxDate": "2022-04-13", "netPriceIncludingTax": "4.25", "amountIncludingTax": "48", "amountExcludingTaxInCompanyCurrency": "48.1", "amountIncludingTaxInCompanyCurrency": "48.1", "taxes": {"query": {"edges": [{"node": {"taxCategory": "test", "tax": "test", "nonTaxableAmount": "20", "taxRate": "22.2", "taxAmount": "37", "exemptAmount": "28", "taxableAmount": "37"}}]}}, "discountCharges": {"query": {"edges": [{"node": {"sign": "increase", "valueType": "percentage", "calculationBasis": "grossPrice", "calculationRule": "byUnit", "basis": "2", "value": "20", "amount": "0.4"}}]}}}}, {"node": {"itemDescription": "Sales Item 81", "unitToStockUnitConversionFactor": "0.001", "origin": "direct", "taxableAmount": "37", "taxAmount": "37", "exemptAmount": "28", "taxDate": "2022-04-13", "netPriceIncludingTax": "3.45", "amountIncludingTax": "32", "amountExcludingTaxInCompanyCurrency": "32.06", "amountIncludingTaxInCompanyCurrency": "32.06", "taxes": {"query": {"edges": [{"node": {"taxCategory": "test", "tax": "test", "nonTaxableAmount": "20", "taxRate": "22.2", "taxAmount": "37", "exemptAmount": "28", "taxableAmount": "37"}}]}}, "discountCharges": {"query": {"edges": [{"node": {"sign": "decrease", "valueType": "percentage", "calculationBasis": "grossPrice", "calculationRule": "byUnit", "basis": "2", "value": "20", "amount": "0.4"}}]}}}}]}}, "totalAmountExcludingTax": "80", "totalAmountIncludingTax": "80", "totalTaxAmount": "0", "totalTaxableAmount": "0", "totalExemptAmount": "0", "taxCalculationStatus": "notDone", "creationNumber": "", "companyCurrency": {"id": "USD"}, "companyFxRate": "1.002", "rateDescription": "1 EUR = 1.002 USD", "totalAmountIncludingTaxInCompanyCurrency": "80.16", "totalAmountExcludingTaxInCompanyCurrency": "80.16"}}}}