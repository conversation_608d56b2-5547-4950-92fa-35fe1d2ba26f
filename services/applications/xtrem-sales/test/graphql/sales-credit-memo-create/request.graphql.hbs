mutation {
    xtremSales {
        salesCreditMemo {
            create(data: {{inputParameters}} ) {
                number
                dueDate
                status
                date
                isPrinted
                site { businessEntity {
                    taxIdNumber
                }}
                salesSiteName
                salesSiteTaxIdNumber
                salesSiteLinkedAddress {
                    name
                }
                salesSiteAddress {
                    name
                }
                salesSiteContact {
                    firstName
                }
                billToCustomer { businessEntity {
                    name
                }}
                billToCustomerName
                billToCustomerTaxIdNumber
                billToLinkedAddress {
                    addressLine2
                }
                billToAddress {
                    region
                }
                billToContact {
                    preferredName
                }
                currency {
                    symbol
                }
                paymentTerm {
                    description
                }
                taxes {
                    query {
                        edges {
                            node {
                                taxCategory
                                tax
                                nonTaxableAmount
                                taxRate
                                taxAmount
                                exemptAmount
                                taxableAmount
                            }
                        }
                    }
                }
                lines {
                    query {
                        edges {
                            node {
                                itemDescription
                                unitToStockUnitConversionFactor
                                origin
                                taxableAmount
                                taxAmount
                                exemptAmount
                                taxDate
                                netPriceIncludingTax
                                amountIncludingTax
                                amountExcludingTaxInCompanyCurrency
                                amountIncludingTaxInCompanyCurrency
                                taxes {
                                    query {
                                        edges {
                                            node {
                                                taxCategory
                                                tax
                                                nonTaxableAmount
                                                taxRate
                                                taxAmount
                                                exemptAmount
                                                taxableAmount
                                            }
                                        }
                                    }
                                }
                                discountCharges {
                                    query {
                                        edges {
                                            node {
                                                sign
                                                valueType
                                                calculationBasis
                                                calculationRule
                                                basis
                                                value
                                                amount
                                            }
                                        }
                                    }
                                }
                           }
                        }
                    }
                }
                totalAmountExcludingTax
                totalAmountIncludingTax
                totalTaxAmount
                totalTaxableAmount
                totalExemptAmount
                taxCalculationStatus
                creationNumber
                companyCurrency {
                    id
                }
                companyFxRate
                rateDescription
                totalAmountIncludingTaxInCompanyCurrency
                totalAmountExcludingTaxInCompanyCurrency
            }
        }
    }
}
