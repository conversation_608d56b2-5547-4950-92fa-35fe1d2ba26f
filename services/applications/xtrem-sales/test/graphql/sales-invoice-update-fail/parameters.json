{"Update sales invoice fail posted status": {"input": {"properties": {"_id": "#SI2", "lines": [{"_sortValue": "10", "_action": "update", "quantity": 20, "netPriceExcludingTax": 2.45, "netPriceIncludingTax": 3.57, "netPrice": 2.45}]}}, "envConfigs": {"today": "2021-04-28"}, "output": {"errors": [{"extensions": {"code": "operation-error", "diagnoses": [{"message": "The sales invoice is posted. You cannot update it.", "path": [], "severity": 3}]}, "locations": [{"column": 17, "line": 4}], "message": "The record was not updated.", "path": ["xtremSales", "salesInvoice", "update"]}]}}, "Update sales invoice fail update to posted status not allowed": {"input": {"properties": {"_id": "#SI1", "status": "posted", "lines": [{"_sortValue": "10", "_action": "update", "quantity": 20, "netPriceExcludingTax": 2.45, "netPriceIncludingTax": 3.57, "netPrice": 2.45}]}}, "envConfigs": {"today": "2021-04-22"}, "output": {"errors": [{"extensions": {"code": "operation-error", "diagnoses": [{"message": "You cannot update the document status to Posted.", "path": [], "severity": 3}]}, "locations": [{"column": 17, "line": 4}], "message": "The record was not updated.", "path": ["xtremSales", "salesInvoice", "update"]}]}}, "Update sales invoice fail update sales site not allowed": {"input": {"properties": {"_id": "#SI1", "site": "#US002", "lines": [{"_sortValue": "10", "_action": "update", "quantity": 20, "netPriceExcludingTax": 2.45, "netPriceIncludingTax": 3.57, "netPrice": 2.45}]}}, "envConfigs": {"today": "2021-05-06"}, "output": {"errors": [{"extensions": {"code": "operation-error", "diagnoses": [{"message": "SalesInvoice.site: cannot set value on frozen property", "path": ["site"], "severity": 4}]}, "locations": [{"column": 17, "line": 4}], "message": "The record was not updated.", "path": ["xtremSales", "salesInvoice", "update"]}]}}, "Update sales invoice fail update bill-to customer not allowed": {"input": {"properties": {"_id": "#SI1", "billToCustomer": "#US020", "lines": [{"_sortValue": "10", "_action": "update", "quantity": 20, "netPriceExcludingTax": 2.45, "netPriceIncludingTax": 3.57, "netPrice": 2.45}]}}, "envConfigs": {"today": "2021-05-06"}, "output": {"errors": [{"extensions": {"code": "operation-error", "diagnoses": [{"message": "SalesInvoice.billToCustomer: cannot set value on frozen property", "path": ["billToCustomer"], "severity": 4}]}, "locations": [{"column": 17, "line": 4}], "message": "The record was not updated.", "path": ["xtremSales", "salesInvoice", "update"]}]}}, "Update sales invoice fail update currency not allowed": {"input": {"properties": {"_id": "#SI1", "currency": "#EUR", "lines": [{"_sortValue": "10", "_action": "update", "quantity": 20, "netPriceExcludingTax": 2.45, "netPriceIncludingTax": 3.57, "netPrice": 2.45}]}}, "envConfigs": {"today": "2021-05-06"}, "output": {"errors": [{"extensions": {"code": "operation-error", "diagnoses": [{"message": "SalesInvoice.currency: cannot set value on frozen property", "path": ["currency"], "severity": 4}]}, "locations": [{"column": 17, "line": 4}], "message": "The record was not updated.", "path": ["xtremSales", "salesInvoice", "update"]}]}}, "Update sales invoice fail update quantity not allowed": {"input": {"properties": {"_id": "#SI3", "lines": [{"_sortValue": "10", "_action": "update", "quantity": 99}]}}, "envConfigs": {"today": "2021-05-10"}, "output": {"errors": [{"extensions": {"code": "operation-error", "diagnoses": [{"message": "Quantity update is not allowed. The sales invoice line is linked to a sales shipment line.", "path": ["lines", "1502"], "severity": 3}]}, "locations": [{"column": 17, "line": 4}], "message": "The record was not updated.", "path": ["xtremSales", "salesInvoice", "update"]}]}}, "create sales invoice line fail posted status on the header": {"input": {"properties": {"_id": "#SI2", "lines": [{"_action": "create", "quantity": 20, "netPriceExcludingTax": 2.45, "netPriceIncludingTax": 3.57, "netPrice": 2.45, "item": "#17891", "stockSiteLinkedAddress": "#US001|7100"}]}}, "envConfigs": {"today": "2021-05-10"}, "output": {"errors": [{"extensions": {"code": "operation-error", "diagnoses": [{"message": "The sales invoice is posted. You cannot update it.", "path": [], "severity": 3}, {"message": "Incorrect sales invoice status. The sales invoice must have the 'Draft' status to create the line.", "path": ["lines", "-1000000001"], "severity": 3}, {"message": "Incorrect item. The stock managed items are not allowed.", "path": ["lines", "-1000000001"], "severity": 3}, {"message": "The record is not valid. You need to select a different record.", "path": ["lines", "-1000000001", "item"], "severity": 3}]}, "locations": [{"column": 17, "line": 4}], "message": "The record was not updated.", "path": ["xtremSales", "salesInvoice", "update"]}]}}, "Delete sales invoice line fail posted status on the header": {"input": {"properties": {"_id": "#SI2", "lines": [{"_sortValue": "10", "_action": "delete"}]}}, "envConfigs": {"today": "2021-05-11"}, "output": {"errors": [{"extensions": {"code": "operation-error", "diagnoses": [{"message": "Line deletion is not allowed. The sales invoice is posted.", "path": ["lines", "1501"], "severity": 3}]}, "locations": [{"column": 17, "line": 4}], "message": "The record was not updated.", "path": ["xtremSales", "salesInvoice", "update"]}]}}, "Delete sales invoice line fail line is linked to a sales shipment line.": {"input": {"properties": {"_id": "#SI3", "lines": [{"_sortValue": "10", "_action": "delete"}]}}, "envConfigs": {"today": "2021-05-11"}, "output": {"errors": [{"extensions": {"code": "operation-error", "diagnoses": [{"message": "Line deletion is not allowed. The sales invoice line is linked to a sales shipment line.", "path": ["lines", "1502"], "severity": 3}]}, "locations": [{"column": 17, "line": 4}], "message": "The record was not updated.", "path": ["xtremSales", "salesInvoice", "update"]}]}}, "Update sales invoice fail update item not allowed": {"input": {"properties": {"_id": "#SI7", "lines": [{"_sortValue": "10", "_action": "update", "quantity": 20, "netPriceExcludingTax": 2.45, "netPriceIncludingTax": 3.57, "grossPrice": 2.45, "item": "#SalesItem81", "unit": "#MI<PERSON><PERSON><PERSON>ER", "stockUnit": "#LITER"}]}}, "envConfigs": {"today": "2021-12-20"}, "output": {"errors": [{"extensions": {"code": "operation-error", "diagnoses": [{"message": "The item cannot be updated after the sales invoice has been created.", "path": ["lines", "1507"], "severity": 3}, {"message": "The sales unit cannot be updated after the sales invoice has been created.", "path": ["lines", "1507"], "severity": 3}]}, "locations": [{"column": 17, "line": 4}], "message": "The record was not updated.", "path": ["xtremSales", "salesInvoice", "update"]}]}}, "Update sales invoice fail update sales unit not allowed": {"input": {"properties": {"_id": "#SI1", "lines": [{"_sortValue": "10", "_action": "update", "quantity": 20, "netPriceExcludingTax": 2.45, "netPriceIncludingTax": 3.57, "grossPrice": 2.45, "unit": "#GRAM"}]}}, "envConfigs": {"today": "2021-05-12"}, "output": {"errors": [{"extensions": {"code": "operation-error", "diagnoses": [{"message": "The sales unit cannot be updated after the sales invoice has been created.", "path": ["lines", "1500"], "severity": 3}]}, "locations": [{"column": 17, "line": 4}], "message": "The record was not updated.", "path": ["xtremSales", "salesInvoice", "update"]}]}}, "Update sales invoice fail update stock unit must match the item stock unit": {"input": {"properties": {"_id": "#SI1", "lines": [{"_sortValue": "10", "_action": "update", "quantity": 20, "netPriceExcludingTax": 2.45, "netPriceIncludingTax": 3.57, "grossPrice": 2.45, "stockUnit": "#METER"}]}}, "envConfigs": {"today": "2021-05-12"}, "output": {"errors": [{"extensions": {"code": "operation-error", "diagnoses": [{"message": "value must be equal to 'GRAM'", "path": ["lines", "1500", "stockUnit"], "severity": 3}]}, "locations": [{"column": 17, "line": 4}], "message": "The record was not updated.", "path": ["xtremSales", "salesInvoice", "update"]}]}}, "Update sales invoice fail not right period": {"input": {"properties": {"_id": "#SI1", "date": "2020-03-08"}}, "envConfigs": {"today": "2021-05-12"}, "output": {"errors": [{"extensions": {"code": "operation-error", "diagnoses": [{"message": "The new invoice date must be in the same period.", "path": [], "severity": 4}]}, "locations": [{"column": 17, "line": 4}], "message": "The record was not updated.", "path": ["xtremSales", "salesInvoice", "update"]}]}}, "Update sales invoice fail Wrong quantity in stock unit": {"input": {"properties": {"_id": "#SI1", "lines": [{"_sortValue": "10", "_action": "update", "quantityInStockUnit": 20}]}}, "envConfigs": {"today": "2021-06-21"}, "output": {"errors": [{"extensions": {"code": "operation-error", "diagnoses": [{"message": "value must be equal to 283.5", "path": ["lines", "1500", "quantityInStockUnit"], "severity": 3}]}, "locations": [{"column": 17, "line": 4}], "message": "The record was not updated.", "path": ["xtremSales", "salesInvoice", "update"]}]}}, "Update internalNote on a posted sales invoice header - fail ": {"input": {"properties": {"_id": "#SI2", "internalNote": {"value": "test"}}}, "envConfigs": {"today": "2021-04-28"}, "output": {"errors": [{"message": "The record was not updated.", "locations": [{"line": 4, "column": 17}], "path": ["xtremSales", "salesInvoice", "update"], "extensions": {"code": "operation-error", "diagnoses": [{"severity": 4, "path": ["internalNote"], "message": "SalesInvoice.internalNote: cannot set value on frozen property"}]}}]}}, "Update externalNote on a posted sales invoice header - fail ": {"input": {"properties": {"_id": "#SI2", "externalNote": {"value": "test"}}}, "envConfigs": {"today": "2021-04-28"}, "output": {"errors": [{"message": "The record was not updated.", "locations": [{"line": 4, "column": 17}], "path": ["xtremSales", "salesInvoice", "update"], "extensions": {"code": "operation-error", "diagnoses": [{"severity": 4, "path": ["externalNote"], "message": "SalesInvoice.externalNote: cannot set value on frozen property"}]}}]}}, "Update isExternalNote on a posted sales invoice header - fail ": {"input": {"properties": {"_id": "#SI2", "isExternalNote": true}}, "envConfigs": {"today": "2021-04-28"}, "output": {"errors": [{"message": "The record was not updated.", "locations": [{"line": 4, "column": 17}], "path": ["xtremSales", "salesInvoice", "update"], "extensions": {"code": "operation-error", "diagnoses": [{"severity": 4, "path": ["isExternalNote"], "message": "SalesInvoice.isExternalNote: cannot set value on frozen property"}]}}]}}, "Update internalNote on a posted sales invoice lines - fail ": {"input": {"properties": {"_id": "#SI2", "lines": [{"_sortValue": "10", "_action": "update", "internalNote": {"value": "test"}}]}}, "envConfigs": {"today": "2021-04-28"}, "output": {"errors": [{"message": "The record was not updated.", "locations": [{"line": 4, "column": 17}], "path": ["xtremSales", "salesInvoice", "update"], "extensions": {"code": "operation-error", "diagnoses": [{"severity": 4, "path": ["lines", "", "internalNote"], "message": "SalesInvoiceLine.internalNote: cannot set value on frozen property"}]}}]}}, "Update isExternalNote on a posted sales invoice lines - fail ": {"input": {"properties": {"_id": "#SI2", "lines": [{"_sortValue": "10", "_action": "update", "isExternalNote": true}]}}, "envConfigs": {"today": "2021-04-28"}, "output": {"errors": [{"message": "The record was not updated.", "locations": [{"line": 4, "column": 17}], "path": ["xtremSales", "salesInvoice", "update"], "extensions": {"code": "operation-error", "diagnoses": [{"severity": 4, "path": ["lines", "", "isExternalNote"], "message": "SalesInvoiceLine.isExternalNote: cannot set value on frozen property"}]}}]}}, "Update externalNote on a posted sales invoice lines - fail ": {"input": {"properties": {"_id": "#SI2", "lines": [{"_sortValue": "10", "_action": "update", "externalNote": {"value": "test"}}]}}, "envConfigs": {"today": "2021-04-28"}, "output": {"errors": [{"message": "The record was not updated.", "locations": [{"line": 4, "column": 17}], "path": ["xtremSales", "salesInvoice", "update"], "extensions": {"code": "operation-error", "diagnoses": [{"severity": 4, "path": ["lines", "", "externalNote"], "message": "SalesInvoiceLine.externalNote: cannot set value on frozen property"}]}}]}}}