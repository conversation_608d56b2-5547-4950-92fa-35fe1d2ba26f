{"Create sales order - minimal payload": {"executionMode": "normal", "input": {"properties": {"soldToCustomer": "#US019", "site": "#DEP1-S01", "requestedDeliveryDate": "2024-01-04", "lines": [{"item": "#CCZ_APPLE_PIE", "quantity": 10}]}}, "output": {"create": {"expectedDeliveryDate": "2026-03-10", "doNotShipAfterDate": null, "doNotShipBeforeDate": null, "shippingDate": "2026-03-03", "requestedDeliveryDate": "2024-01-04", "currency": {"name": "US Dollar"}, "billToLinkedAddress": {"name": "Cust address"}, "billToCustomer": {"businessEntity": {"name": "Dépot de TOULOUSE - Sud Ouest"}}, "shipToCustomerAddress": {"businessEntity": {"name": "Dépot de TOULOUSE - Sud Ouest"}, "name": "Cust address"}, "soldToCustomer": {"businessEntity": {"name": "Dépot de TOULOUSE - Sud Ouest"}}, "date": "2026-03-03", "status": "quote", "isOnHold": false, "customerNumber": "", "shippingStatus": "notShipped", "site": {"name": "Entrepot de  Saint  Denis"}, "stockSite": {"name": "Entrepot de  Saint  Denis"}, "soldToLinkedAddress": {"name": "Cust address"}, "shipToCustomer": {"businessEntity": {"name": "Dépot de TOULOUSE - Sud Ouest"}}, "incoterm": {"name": "Ex Work"}, "deliveryMode": {"name": "Rail"}, "deliveryLeadTime": 7, "paymentTerm": {"isActive": true, "name": "Payment due upon receipt", "businessEntityType": "all", "description": ""}, "internalNote": {"value": "internal note on document"}, "externalNote": {"value": ""}, "lines": {"query": {"edges": [{"node": {"status": "quote", "priceOrigin": "basePrice", "shippingStatus": "notShipped", "item": {"name": "CCZ Apple pie"}, "itemDescription": "Apple pie test MRP", "quantity": "10", "unit": {"name": "Each"}, "stockUnit": {"name": "Each"}, "taxableAmount": "300", "taxAmount": "0", "exemptAmount": "0", "taxDate": "2026-03-03", "netPriceIncludingTax": "15", "amountIncludingTax": "150", "taxes": {"query": {"edges": [{"node": {"taxCategory": "Value Added Tax", "tax": "Tax collected on debits, exempt rate", "nonTaxableAmount": "0", "taxRate": "0", "taxAmount": "0", "exemptAmount": "0", "taxableAmount": "150"}}, {"node": {"taxCategory": "TEST", "tax": "", "nonTaxableAmount": "0", "taxRate": "0", "taxAmount": "0", "exemptAmount": "0", "taxableAmount": "150"}}]}}, "discountCharges": {"query": {"edges": [{"node": {"sign": "increase", "valueType": "percentage", "calculationBasis": "grossPrice", "calculationRule": "byUnit", "basis": "15", "value": "0", "amount": "0", "_constructor": "SalesOrderLineDiscountCharge"}}, {"node": {"sign": "decrease", "valueType": "percentage", "calculationBasis": "grossPrice", "calculationRule": "byUnit", "basis": "15", "value": "0", "amount": "0", "_constructor": "SalesOrderLineDiscountCharge"}}]}}, "discount": "0", "charge": "0", "amountExcludingTaxInCompanyCurrency": "149.7", "remainingAmountToShipExcludingTax": "150", "remainingAmountToShipExcludingTaxInCompanyCurrency": "150", "workInProgress": {"documentType": "salesOrder", "status": "suggested", "startDate": "2026-03-03", "endDate": "2026-03-03", "expectedQuantity": "10", "actualQuantity": "0", "outstandingQuantity": "10"}, "internalNote": {"value": ""}, "externalNote": {"value": ""}}}]}}, "totalAmountExcludingTax": "150", "totalAmountIncludingTax": "150", "totalTaxAmount": "0", "totalTaxableAmount": "300", "totalExemptAmount": "0", "taxCalculationStatus": "failed", "companyCurrency": {"id": "EUR"}, "transactionCurrency": {"id": "USD"}, "companyFxRate": "1", "rateDescription": "1 USD = 0.********* EUR", "totalAmountIncludingTaxInCompanyCurrency": "149.7", "totalAmountExcludingTaxInCompanyCurrency": "149.7"}}, "envConfigs": {"today": "2024-27-03"}}, "Create sales order with standard discount": {"input": {"properties": {"shippingStatus": "notShipped", "site": "#US001", "shipToCustomer": "#US019", "soldToCustomer": "#US019", "incoterm": "#EXW", "shipToCustomerAddress": "#US019|1500", "billToLinkedAddress": "#US019|1500", "deliveryLeadTime": 1, "currency": "#ZAR", "doNotShipAfterDate": "2020-11-30", "doNotShipBeforeDate": "2020-11-28", "requestedDeliveryDate": "2020-11-28", "date": "2020-11-26", "paymentTerm": "#DUE_UPON_RECEIPT_ALL", "internalNote": {"value": "Internal note"}, "externalNote": {"value": "External note"}, "isExternalNote": true, "lines": [{"shipToCustomerAddress": "#US019|1500", "item": "#17891", "unit": "#LITER", "quantity": 600, "grossPrice": 2, "discountCharges": [{"sign": "increase", "valueType": "percentage", "calculationBasis": "grossPrice", "basis": 2, "value": 3}], "discount": 3, "internalNote": {"value": "Internal line note"}, "externalNote": {"value": "External line note"}, "isExternalNote": true}]}}, "output": {"create": {"expectedDeliveryDate": "2020-12-01", "doNotShipAfterDate": "2020-11-30", "doNotShipBeforeDate": "2020-11-28", "shippingDate": "2020-11-30", "requestedDeliveryDate": "2020-11-28", "currency": {"name": "South African Rand"}, "billToLinkedAddress": {"name": "Cust address"}, "billToCustomer": {"businessEntity": {"name": "Dépot de TOULOUSE - Sud Ouest"}}, "shipToCustomerAddress": {"businessEntity": {"name": "Dépot de TOULOUSE - Sud Ouest"}, "name": "Cust address"}, "soldToCustomer": {"businessEntity": {"name": "Dépot de TOULOUSE - Sud Ouest"}}, "date": "2020-11-26", "status": "quote", "isOnHold": false, "customerNumber": "", "shippingStatus": "notShipped", "site": {"name": "Chem. Atlanta"}, "stockSite": {"name": "Chem. Atlanta"}, "soldToLinkedAddress": {"name": "Cust address"}, "shipToCustomer": {"businessEntity": {"name": "Dépot de TOULOUSE - Sud Ouest"}}, "incoterm": {"name": "Ex Work"}, "deliveryMode": {"name": "Rail"}, "deliveryLeadTime": 1, "paymentTerm": {"isActive": true, "name": "Payment due upon receipt", "businessEntityType": "all", "description": ""}, "internalNote": {"value": "Internal note"}, "externalNote": {"value": "External note"}, "lines": {"query": {"edges": [{"node": {"status": "quote", "priceOrigin": "manual", "shippingStatus": "notShipped", "item": {"name": "Hydro-alcoholic 2 gel for hand antisepsis"}, "itemDescription": "Hydro-alcoholic 2 gel for hand antisepsis", "quantity": "600", "unit": {"name": "Liter"}, "stockUnit": {"name": "Liter"}, "taxableAmount": "0", "taxAmount": "0", "exemptAmount": "0", "taxDate": "2020-11-26", "netPriceIncludingTax": "2", "amountIncludingTax": "1200", "remainingAmountToShipExcludingTax": "1200", "remainingAmountToShipExcludingTaxInCompanyCurrency": "19044", "taxes": {"query": {"edges": []}}, "discountCharges": {"query": {"edges": [{"node": {"sign": "decrease", "valueType": "percentage", "calculationBasis": "grossPrice", "calculationRule": "byUnit", "basis": "2", "value": "3", "amount": "0.06", "_constructor": "SalesOrderLineDiscountCharge"}}, {"node": {"sign": "increase", "valueType": "percentage", "calculationBasis": "grossPrice", "calculationRule": "byUnit", "basis": "2", "value": "3", "amount": "0.06", "_constructor": "SalesOrderLineDiscountCharge"}}]}}, "discount": "3", "charge": "3", "amountExcludingTaxInCompanyCurrency": "19044", "workInProgress": {"documentType": "salesOrder", "status": "suggested", "startDate": "2020-11-26", "endDate": "2020-11-27", "expectedQuantity": "0.6", "actualQuantity": "0", "outstandingQuantity": "0.6"}, "internalNote": {"value": "Internal line note"}, "externalNote": {"value": "External line note"}}}]}}, "totalAmountExcludingTax": "1200", "totalAmountIncludingTax": "1200", "totalTaxAmount": "0", "totalTaxableAmount": "0", "totalExemptAmount": "0", "taxCalculationStatus": "notDone", "companyCurrency": {"id": "USD"}, "transactionCurrency": {"id": "ZAR"}, "companyFxRate": "15.87", "rateDescription": "1 ZAR = 15.87 USD", "totalAmountIncludingTaxInCompanyCurrency": "19044", "totalAmountExcludingTaxInCompanyCurrency": "19044"}}}, "Create sales order with standard discount and tax": {"input": {"properties": {"shippingStatus": "notShipped", "site": "#US001", "shipToCustomer": "#US019", "soldToCustomer": "#US019", "incoterm": "#EXW", "shipToCustomerAddress": "#US019|1500", "billToLinkedAddress": "#US019|1500", "deliveryLeadTime": 1, "currency": "#ZAR", "doNotShipAfterDate": "2020-11-30", "doNotShipBeforeDate": "2020-11-28", "requestedDeliveryDate": "2020-11-28", "date": "2020-11-26", "paymentTerm": "#DUE_UPON_RECEIPT_ALL", "taxes": [{"taxCategory": "test", "tax": "test", "nonTaxableAmount": 20, "taxRate": 22.2, "taxAmount": 37, "exemptAmount": 28, "taxableAmount": 37}], "lines": [{"item": "#SalesItem81", "quantity": 20, "unit": "#MI<PERSON><PERSON><PERSON>ER", "grossPrice": 2, "discountCharges": [{"sign": "increase", "valueType": "percentage", "calculationBasis": "grossPrice", "basis": 2, "value": 20}], "discount": 3, "netPriceExcludingTax": 2.4, "netPriceIncludingTax": 2.4, "taxes": [{"taxCategory": "test", "tax": "test", "nonTaxableAmount": 20, "taxRate": 22.2, "taxAmount": 37, "exemptAmount": 28, "taxableAmount": 37}]}, {"item": "#SalesItem81", "unit": "#MI<PERSON><PERSON><PERSON>ER", "quantity": 20, "discount": 20, "grossPrice": 2, "discountCharges": [{"sign": "decrease", "valueType": "percentage", "calculationBasis": "grossPrice", "basis": 2, "value": 20}], "stockSiteAddress": {"name": "test"}, "shipToAddress": {"name": "test"}, "taxes": [{"taxCategory": "test", "tax": "test", "nonTaxableAmount": 20, "taxRate": 22.2, "taxAmount": 37, "exemptAmount": 28, "taxableAmount": 37}]}]}}, "output": {"create": {"expectedDeliveryDate": "2020-12-01", "doNotShipAfterDate": "2020-11-30", "doNotShipBeforeDate": "2020-11-28", "shippingDate": "2020-11-30", "requestedDeliveryDate": "2020-11-28", "currency": {"name": "South African Rand"}, "billToLinkedAddress": {"name": "Cust address"}, "billToCustomer": {"businessEntity": {"name": "Dépot de TOULOUSE - Sud Ouest"}}, "shipToCustomerAddress": {"businessEntity": {"name": "Dépot de TOULOUSE - Sud Ouest"}, "name": "Cust address"}, "soldToCustomer": {"businessEntity": {"name": "Dépot de TOULOUSE - Sud Ouest"}}, "date": "2020-11-26", "status": "quote", "isOnHold": false, "customerNumber": "", "shippingStatus": "notShipped", "site": {"name": "Chem. Atlanta"}, "stockSite": {"name": "Chem. Atlanta"}, "soldToLinkedAddress": {"name": "Cust address"}, "shipToCustomer": {"businessEntity": {"name": "Dépot de TOULOUSE - Sud Ouest"}}, "incoterm": {"name": "Ex Work"}, "deliveryMode": {"name": "Rail"}, "deliveryLeadTime": 1, "paymentTerm": {"isActive": true, "name": "Payment due upon receipt", "businessEntityType": "all", "description": ""}, "externalNote": {"value": ""}, "internalNote": {"value": "internal note on document"}, "lines": {"query": {"edges": [{"node": {"status": "quote", "priceOrigin": "manual", "shippingStatus": "notShipped", "item": {"name": "Sales Item 81"}, "itemDescription": "Sales Item 81", "quantity": "20", "unit": {"name": "Milliliter"}, "stockUnit": {"name": "Liter"}, "taxableAmount": "0", "taxAmount": "0", "exemptAmount": "0", "taxDate": "2020-11-26", "netPriceIncludingTax": "2.4", "amountIncludingTax": "48", "taxes": {"query": {"edges": [{"node": {"taxCategory": "test", "tax": "test", "nonTaxableAmount": "20", "taxRate": "22.2", "taxAmount": "37", "exemptAmount": "28", "taxableAmount": "37"}}]}}, "discountCharges": {"query": {"edges": [{"node": {"sign": "decrease", "valueType": "percentage", "calculationBasis": "grossPrice", "calculationRule": "byUnit", "basis": "2", "value": "3", "amount": "0.06", "_constructor": "SalesOrderLineDiscountCharge"}}, {"node": {"sign": "increase", "valueType": "percentage", "calculationBasis": "grossPrice", "calculationRule": "byUnit", "basis": "2", "value": "20", "amount": "0.4", "_constructor": "SalesOrderLineDiscountCharge"}}]}}, "discount": "3", "charge": "20", "amountExcludingTaxInCompanyCurrency": "761.76", "remainingAmountToShipExcludingTax": "46.8", "remainingAmountToShipExcludingTaxInCompanyCurrency": "742.716", "workInProgress": {"documentType": "salesOrder", "status": "suggested", "startDate": "2020-11-26", "endDate": "2020-11-27", "expectedQuantity": "0.02", "actualQuantity": "0", "outstandingQuantity": "0.02"}, "externalNote": {"value": ""}, "internalNote": {"value": ""}}}, {"node": {"status": "quote", "priceOrigin": "manual", "shippingStatus": "notShipped", "item": {"name": "Sales Item 81"}, "itemDescription": "Sales Item 81", "quantity": "20", "unit": {"name": "Milliliter"}, "stockUnit": {"name": "Liter"}, "taxableAmount": "0", "taxAmount": "0", "exemptAmount": "0", "taxDate": "2020-11-26", "netPriceIncludingTax": "1.6", "amountIncludingTax": "32", "taxes": {"query": {"edges": [{"node": {"taxCategory": "test", "tax": "test", "nonTaxableAmount": "20", "taxRate": "22.2", "taxAmount": "37", "exemptAmount": "28", "taxableAmount": "37"}}]}}, "discountCharges": {"query": {"edges": [{"node": {"sign": "decrease", "valueType": "percentage", "calculationBasis": "grossPrice", "calculationRule": "byUnit", "basis": "2", "value": "20", "amount": "0.4", "_constructor": "SalesOrderLineDiscountCharge"}}]}}, "discount": "20", "charge": "0", "amountExcludingTaxInCompanyCurrency": "507.84", "remainingAmountToShipExcludingTax": "32", "remainingAmountToShipExcludingTaxInCompanyCurrency": "507.84", "workInProgress": {"documentType": "salesOrder", "status": "suggested", "startDate": "2020-11-26", "endDate": "2020-11-27", "expectedQuantity": "0.02", "actualQuantity": "0", "outstandingQuantity": "0.02"}, "externalNote": {"value": ""}, "internalNote": {"value": ""}}}]}}, "totalAmountExcludingTax": "80", "totalAmountIncludingTax": "80", "totalTaxAmount": "0", "totalTaxableAmount": "0", "totalExemptAmount": "0", "taxCalculationStatus": "notDone", "companyCurrency": {"id": "USD"}, "transactionCurrency": {"id": "ZAR"}, "companyFxRate": "15.87", "rateDescription": "1 ZAR = 15.87 USD", "totalAmountIncludingTaxInCompanyCurrency": "1269.6", "totalAmountExcludingTaxInCompanyCurrency": "1269.6"}}}}