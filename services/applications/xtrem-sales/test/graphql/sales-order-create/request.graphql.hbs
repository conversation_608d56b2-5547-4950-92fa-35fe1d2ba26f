mutation {
    xtremSales {
        salesOrder {
            create(data: {{inputParameters}} ) {
                expectedDeliveryDate
                doNotShipAfterDate
                doNotShipBeforeDate
                shippingDate
                requestedDeliveryDate
                currency {
                    name
                }
                billToLinkedAddress {
                    name
                }
                billToCustomer { businessEntity {
                    name
                }}
                shipToCustomerAddress {
                    businessEntity{
                        name
                    }
                    name
                }
                soldToCustomer { businessEntity {
                    name
                }}
                date
                status
                isOnHold
                customerNumber
                shippingStatus
                site {
                    name
                }
                stockSite {
                    name
                }
                soldToLinkedAddress {
                    name
                }
                shipToCustomer { businessEntity {
                    name
                }}
                billToCustomer { businessEntity {
                    name
                }}
                incoterm {
                    name
                }
                deliveryMode {
                    name
                }
                deliveryLeadTime
                paymentTerm {
                    isActive
                    name
                    businessEntityType
                    description
                }
                internalNote {
                    value
                }
                externalNote {
                    value
                }
                lines {
                    query {
                        edges {
                            node {
                                status
                                priceOrigin
                                shippingStatus
                                item {
                                    name
                                }
                                itemDescription
                                quantity
                                unit {
                                    name
                                }
                                stockUnit {
                                    name
                                }
                                taxableAmount
                                taxAmount
                                exemptAmount
                                taxDate
                                netPriceIncludingTax
                                amountIncludingTax
                                taxes {
                                    query {
                                        edges {
                                            node {
                                                taxCategory
                                                tax
                                                nonTaxableAmount
                                                taxRate
                                                taxAmount
                                                exemptAmount
                                                taxableAmount
                                            }
                                        }
                                    }
                                }
                                discountCharges {
                                    query {
                                        edges {
                                            node {
                                                sign
                                                valueType
                                                calculationBasis
                                                calculationRule
                                                basis
                                                value
                                                amount
                                                _constructor
                                            }
                                        }
                                    }
                                }
                                discount
                                charge
                                amountExcludingTaxInCompanyCurrency
                                remainingAmountToShipExcludingTax
                                remainingAmountToShipExcludingTaxInCompanyCurrency
                                workInProgress {
                                    documentType
                                    status
                                    startDate
                                    endDate
                                    expectedQuantity
                                    actualQuantity
                                    outstandingQuantity
                                }
                                internalNote {
                                    value
                                }
                                externalNote {
                                    value
                                }

                            }
                        }
                    }
                }
                totalAmountExcludingTax
                totalAmountIncludingTax
                totalTaxAmount
                totalTaxableAmount
                totalExemptAmount
                taxCalculationStatus
                companyCurrency {
                    id
                }
                transactionCurrency {
                    id
                }
                companyFxRate
                rateDescription
                totalAmountIncludingTaxInCompanyCurrency
                totalAmountExcludingTaxInCompanyCurrency
            }
        }
    }
}
