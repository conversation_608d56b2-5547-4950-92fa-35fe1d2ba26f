{"expirationDate isFrozen() when isActive is false": {"input": {"properties": {"_id": "2", "expirationDate": "2021-04-28"}}, "envConfigs": {"today": "2021-04-24"}, "output": {"errors": [{"message": "The record was not updated.", "locations": [{"line": 4, "column": 13}], "path": ["xtremSales", "proformaInvoice", "update"], "extensions": {"code": "operation-error", "diagnoses": [{"severity": 4, "path": ["expirationDate"], "message": "ProformaInvoice.expirationDate: cannot set value on frozen property"}]}}]}}, "customerComment isFrozen() when isActive is false": {"input": {"properties": {"_id": "2", "customerComment": {"value": "Customer comment changed"}}}, "envConfigs": {"today": "2021-04-24"}, "output": {"errors": [{"message": "The record was not updated.", "locations": [{"line": 4, "column": 13}], "path": ["xtremSales", "proformaInvoice", "update"], "extensions": {"code": "operation-error", "diagnoses": [{"severity": 4, "path": ["customerComment"], "message": "ProformaInvoice.customerComment: cannot set value on frozen property"}]}}]}}, "isSent isFrozen() when isActive is false": {"input": {"properties": {"_id": "2", "isSent": true}}, "envConfigs": {"today": "2021-04-24"}, "output": {"errors": [{"message": "The record was not updated.", "locations": [{"line": 4, "column": 13}], "path": ["xtremSales", "proformaInvoice", "update"], "extensions": {"code": "operation-error", "diagnoses": [{"severity": 4, "path": ["isSent"], "message": "ProformaInvoice.isSent: cannot set value on frozen property"}]}}]}}, "Only the last version can be active": {"input": {"properties": {"_id": "2", "isActive": true}}, "envConfigs": {"today": "2021-04-23"}, "output": {"errors": [{"message": "The record was not updated.", "locations": [{"line": 4, "column": 13}], "path": ["xtremSales", "proformaInvoice", "update"], "extensions": {"code": "operation-error", "diagnoses": [{"severity": 3, "path": ["isActive"], "message": "Only the last version can be active."}]}}]}}, "expirationDate cannot be earlier than today": {"input": {"properties": {"_id": "1", "expirationDate": "2021-04-23"}}, "envConfigs": {"today": "2021-04-24"}, "output": {"errors": [{"message": "The record was not updated.", "locations": [{"line": 4, "column": 13}], "path": ["xtremSales", "proformaInvoice", "update"], "extensions": {"code": "operation-error", "diagnoses": [{"severity": 3, "path": ["expirationDate"], "message": "The expiration date needs to be on or after the current date."}]}}]}}}