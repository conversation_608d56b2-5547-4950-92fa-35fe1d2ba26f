{"Create sales credit memo fail empty line": {"input": {"properties": {"site": "#US001", "incoterm": "#EXW", "billToCustomer": "#US019"}}, "envConfigs": {"today": "2021-06-28"}, "output": {"errors": [{"extensions": {"code": "operation-error", "diagnoses": [{"message": "The document needs at least one line.", "path": [], "severity": 3}]}, "locations": [{"column": 17, "line": 4}], "message": "The record was not created.", "path": ["xtremSales", "salesCreditMemo", "create"]}]}}, "Create sales credit memo fail wrong sales unit": {"input": {"properties": {"site": "#US001", "incoterm": "#EXW", "billToCustomer": "#US019", "lines": [{"item": "#SalesItem81", "unit": "#METER", "quantity": 20, "discountCharges": [{"sign": "increase", "valueType": "percentage", "calculationBasis": "grossPrice", "basis": 2, "value": 20}], "netPriceExcludingTax": 2.4}]}}, "envConfigs": {"today": "2021-06-28"}, "output": {"errors": [{"extensions": {"code": "operation-error", "diagnoses": [{"message": "value must be part of set [<PERSON><PERSON><PERSON><PERSON><PERSON>,LITER]", "path": ["lines", "-1000000002", "unit"], "severity": 3}]}, "locations": [{"column": 17, "line": 4}], "message": "The record was not created.", "path": ["xtremSales", "salesCreditMemo", "create"]}]}}, "Create sales credit memo fail wrong stock unit": {"input": {"properties": {"site": "#US001", "incoterm": "#EXW", "billToCustomer": "#US019", "lines": [{"item": "#SalesItem81", "stockUnit": "#METER", "quantity": 20, "discountCharges": [{"sign": "increase", "valueType": "percentage", "calculationBasis": "grossPrice", "basis": 2, "value": 20}], "netPriceExcludingTax": 2.4}]}}, "envConfigs": {"today": "2021-06-28"}, "output": {"errors": [{"extensions": {"code": "operation-error", "diagnoses": [{"message": "value must be equal to 'LITER'", "path": ["lines", "-1000000002", "stockUnit"], "severity": 3}]}, "locations": [{"column": 17, "line": 4}], "message": "The record was not created.", "path": ["xtremSales", "salesCreditMemo", "create"]}]}}, "Create sales credit memo fail wrong status": {"input": {"properties": {"site": "#US001", "incoterm": "#EXW", "billToCustomer": "#US019", "status": "posted", "lines": [{"item": "#SalesItem81", "quantity": 20, "netPriceExcludingTax": 2.4}]}}, "envConfigs": {"today": "2021-07-05"}, "output": {"errors": [{"extensions": {"code": "operation-error", "diagnoses": [{"message": "You can only add a line if the status of the sales credit memo is set to Draft.", "path": ["lines", "-1000000002"], "severity": 3}]}, "locations": [{"column": 17, "line": 4}], "message": "The record was not created.", "path": ["xtremSales", "salesCreditMemo", "create"]}]}}, "Create sales credit memo fail wrong date in future": {"input": {"properties": {"site": "#US001", "incoterm": "#EXW", "billToCustomer": "#US019", "date": "2021-10-19", "lines": [{"item": "#SalesItem81", "quantity": 20, "netPriceExcludingTax": 2.4}]}}, "envConfigs": {"today": "2021-10-18"}, "output": {"errors": [{"extensions": {"code": "operation-error", "diagnoses": [{"message": "The credit memo date cannot be later than today.", "path": [], "severity": 4}]}, "locations": [{"column": 17, "line": 4}], "message": "The record was not created.", "path": ["xtremSales", "salesCreditMemo", "create"]}]}}, "Create sales credit memo fail wrong date earlier than last credit memo": {"input": {"properties": {"site": "#US001", "incoterm": "#EXW", "billToCustomer": "#US019", "date": "2021-04-01", "lines": [{"item": "#SalesItem81", "quantity": 20, "netPriceExcludingTax": 2.4}]}}, "envConfigs": {"today": "2021-10-18"}, "output": {"errors": [{"extensions": {"code": "operation-error", "diagnoses": [{"message": "The document date 2021-04-01 is earlier than the previous document date 2021-04-28.", "path": [], "severity": 3}]}, "locations": [{"column": 17, "line": 4}], "message": "The record was not created.", "path": ["xtremSales", "salesCreditMemo", "create"]}]}}}