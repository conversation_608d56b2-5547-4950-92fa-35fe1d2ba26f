mutation {
    xtremSales {
        salesOrder {
            createSalesShipmentsFromOrderLines(
                {{#each properties}}
                {{@key}} : 
                    {{#each this}} 
                        { 
                            {{@key}}: {{this}} 
                        } 
                    {{/each}}
                {{/each}},
                {{#each arrayProperties}}
                    {{@key}} :
                    [
                        {{#each this}}
                        {
                            {{#each this}}
                            {{#unless this.[0]}}
                                {{@key}} : {{this}}
                            {{else}}
                                {{@key}} : "{{this}}"
                            {{/unless}}
                            {{/each}}
                        }
                        {{/each}}
                    ]
                {{/each}})
            {
                status
                numberOfShipments
                lineErrors {
                    lineNumber
                    linePosition
                    message
                }
            }         
        }
    }
}
