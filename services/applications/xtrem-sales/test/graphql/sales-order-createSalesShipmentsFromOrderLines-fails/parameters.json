{"Create sales shipment using mutation - billToCustomer is on hold": {"input": {"properties": {"processOptions": {"processAllShippableLines": true}}, "arrayProperties": {"salesDocumentLines": [{"salesDocumentLine": "_id:631", "quantityToProcess": 30}, {"salesDocumentLine": "_id:632", "quantityToProcess": 30}]}}, "output": {"createSalesShipmentsFromOrderLines": {"status": "salesOrderIsNotShipped", "numberOfShipments": 0, "lineErrors": [{"lineNumber": 631, "linePosition": 1, "message": "The Bill-to customer is on hold on line 1."}, {"lineNumber": 632, "linePosition": 2, "message": "The Bill-to customer is on hold on line 2."}]}}, "envConfigs": {"today": "2020-11-27"}}, "Create sales shipment using mutation empty array with processAllShippableLines processOption": {"input": {"properties": {"processOptions": {"processAllShippableLines": true}}, "arrayProperties": {"salesDocumentLines": []}}, "output": {"createSalesShipmentsFromOrderLines": {"status": "parametersAreIncorrect", "numberOfShipments": 0, "lineErrors": []}}, "envConfigs": {"today": "2020-11-27"}}, "Create sales shipment from order lines - already shipped": {"input": {"properties": {"processOptions": {"processAllShippableLines": true}}, "arrayProperties": {"salesDocumentLines": [{"salesDocumentLine": "_id:609"}]}}, "output": {"createSalesShipmentsFromOrderLines": {"status": "salesOrderIsNotShipped", "numberOfShipments": 0, "lineErrors": [{"lineNumber": 609, "linePosition": 1, "message": "The sales order line shipped already."}]}}, "envConfigs": {"today": "2020-11-27"}}, "Create sales shipment from order lines - allocation request in progress": {"input": {"properties": {"processOptions": {"processAllShippableLines": true}}, "arrayProperties": {"salesDocumentLines": [{"salesDocumentLine": "_id:1215"}]}}, "output": {"createSalesShipmentsFromOrderLines": {"status": "salesOrderIsNotShipped", "numberOfShipments": 0, "lineErrors": [{"lineNumber": 1215, "linePosition": 1, "message": "You can only ship the sales order line after the allocation request is complete."}]}}, "envConfigs": {"today": "2020-11-27"}}}