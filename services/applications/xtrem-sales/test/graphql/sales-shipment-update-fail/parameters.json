{"Change a the status of a closed sales shipment": {"executionMode": "normal", "input": {"properties": {"_id": "#SSH4", "status": "readyToProcess"}}, "output": {"errors": [{"extensions": {"code": "operation-error", "diagnoses": [{"message": "The sales shipment is closed. You cannot update it.", "path": [], "severity": 3}]}, "locations": [{"column": 17, "line": 4}], "message": "The record was not updated.", "path": ["xtremSales", "salesShipment", "update"]}]}}, "Update internalNote on a posted sales shipment header - fail ": {"input": {"properties": {"_id": "#SSH4", "internalNote": {"value": "test"}}}, "output": {"errors": [{"message": "The record was not updated.", "locations": [{"line": 4, "column": 17}], "path": ["xtremSales", "salesShipment", "update"], "extensions": {"code": "operation-error", "diagnoses": [{"severity": 4, "path": ["internalNote"], "message": "SalesShipment.internalNote: cannot set value on frozen property"}]}}]}}, "Update externalNote on a posted sales shipment header - fail ": {"input": {"properties": {"_id": "#SSH4", "externalNote": {"value": "test"}}}, "output": {"errors": [{"message": "The record was not updated.", "locations": [{"line": 4, "column": 17}], "path": ["xtremSales", "salesShipment", "update"], "extensions": {"code": "operation-error", "diagnoses": [{"severity": 4, "path": ["externalNote"], "message": "SalesShipment.externalNote: cannot set value on frozen property"}]}}]}}, "Update isExternalNote on a posted sales shipment header - fail ": {"input": {"properties": {"_id": "#SSH4", "isExternalNote": true}}, "output": {"errors": [{"message": "The record was not updated.", "locations": [{"line": 4, "column": 17}], "path": ["xtremSales", "salesShipment", "update"], "extensions": {"code": "operation-error", "diagnoses": [{"severity": 4, "path": ["isExternalNote"], "message": "SalesShipment.isExternalNote: cannot set value on frozen property"}]}}]}}, "Update internalNote on a posted sales shipment lines - fail ": {"input": {"properties": {"_id": "#SSH4", "lines": [{"_sortValue": 130300, "_action": "update", "internalNote": {"value": "test"}}]}}, "output": {"errors": [{"message": "The record was not updated.", "locations": [{"line": 4, "column": 17}], "path": ["xtremSales", "salesShipment", "update"], "extensions": {"code": "operation-error", "diagnoses": [{"severity": 4, "path": ["lines", "", "internalNote"], "message": "SalesShipmentLine.internalNote: cannot set value on frozen property"}]}}]}}, "Update isExternalNote on a posted sales shipment lines - fail ": {"input": {"properties": {"_id": "#SSH4", "lines": [{"_sortValue": 130300, "_action": "update", "isExternalNote": true}]}}, "output": {"errors": [{"message": "The record was not updated.", "locations": [{"line": 4, "column": 17}], "path": ["xtremSales", "salesShipment", "update"], "extensions": {"code": "operation-error", "diagnoses": [{"severity": 4, "path": ["lines", "", "isExternalNote"], "message": "SalesShipmentLine.isExternalNote: cannot set value on frozen property"}]}}]}}, "Update externalNote on a posted sales shipment lines - fail ": {"input": {"properties": {"_id": "#SSH4", "lines": [{"_sortValue": 130300, "_action": "update", "externalNote": {"value": "test"}}]}}, "output": {"errors": [{"message": "The record was not updated.", "locations": [{"line": 4, "column": 17}], "path": ["xtremSales", "salesShipment", "update"], "extensions": {"code": "operation-error", "diagnoses": [{"severity": 4, "path": ["lines", "", "externalNote"], "message": "SalesShipmentLine.externalNote: cannot set value on frozen property"}]}}]}}}