mutation {
    xtremSales {
        salesInvoice {
            create(data: {{inputParameters}}) {
                number
                dueDate
                status
                date
                isPrinted
                site { businessEntity {
                    taxIdNumber
                }}
                salesSiteName
                salesSiteTaxIdNumber
                salesSiteLinkedAddress {
                    name
                }
                salesSiteAddress {
                    name
                }
                salesSiteContact {
                    firstName
                }
                billToCustomer { businessEntity {
                    name
                }}
                billToCustomerName
                billToCustomerTaxIdNumber
                billToLinkedAddress {
                    addressLine2
                }
                billToAddress {
                    region
                }
                billToContact {
                    preferredName
                }
                currency {
                    symbol
                }
                paymentTerm {
                    description
                }
                financeIntegrationStatus
                taxes {
                    query {
                        edges {
                            node {
                                taxCategory
                                tax
                                nonTaxableAmount
                                taxRate
                                taxAmount
                                exemptAmount
                                taxableAmount
                                currency { id }
                            }
                        }
                    }
                }
                lines {
                    query {
                        edges {
                            node {
                                origin
                                amountExcludingTax
                                amountIncludingTax
                                amountExcludingTaxInCompanyCurrency
                                amountIncludingTaxInCompanyCurrency
                                discountCharges {
                                    query {
                                        edges {
                                            node {
                                                sign
                                                valueType
                                                calculationBasis
                                                calculationRule
                                                basis
                                                value
                                                amount
                                            }
                                        }
                                    }
                                }
                                 taxes {
                                    query {
                                        edges {
                                            node {
                                                taxCategory
                                                tax
                                                nonTaxableAmount
                                                taxRate
                                                taxAmount
                                                exemptAmount
                                                taxableAmount
                                            }
                                        }
                                    }
                                }
                           }
                        }
                    }
                }
                totalAmountExcludingTax
                totalAmountIncludingTax
                creationNumber
                companyCurrency {
                    id
                }
                companyFxRate
                rateDescription
                totalAmountIncludingTaxInCompanyCurrency
                totalAmountExcludingTaxInCompanyCurrency
            }
        }
    }

}
