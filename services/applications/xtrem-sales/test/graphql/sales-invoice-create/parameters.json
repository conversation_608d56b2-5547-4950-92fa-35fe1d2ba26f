{"Create sales invoice": {"input": {"properties": {"site": "#US001", "incoterm": "#EXW", "dueDate": "2021-04-24", "billToCustomer": "#US019", "currency": "#ZAR", "taxes": [{"taxCategory": "test", "tax": "test", "nonTaxableAmount": 20, "taxRate": 22.2, "taxAmount": 37, "exemptAmount": 28, "taxableAmount": 37}], "lines": [{"item": "#SalesItem81", "quantity": 20, "unit": "#MI<PERSON><PERSON><PERSON>ER", "grossPrice": 2, "discountCharges": [{"sign": "increase", "valueType": "percentage", "calculationBasis": "grossPrice", "basis": 2, "value": 20}], "netPriceExcludingTax": 2.4, "netPriceIncludingTax": 2.4, "taxes": [{"taxCategory": "test", "tax": "test", "nonTaxableAmount": 20, "taxRate": 22.2, "taxAmount": 37, "exemptAmount": 28, "taxableAmount": 37}]}, {"item": "#SalesItem81", "unit": "#MI<PERSON><PERSON><PERSON>ER", "quantity": 20, "discount": 20, "grossPrice": 2, "discountCharges": [{"sign": "decrease", "valueType": "percentage", "calculationBasis": "grossPrice", "basis": 2, "value": 20}], "providerSiteAddress": {"name": "test"}, "consumptionAddress": {"name": "test"}, "taxes": [{"taxCategory": "test", "tax": "test", "nonTaxableAmount": 20, "taxRate": 22.2, "taxAmount": 37, "exemptAmount": 28}]}]}}, "envConfigs": {"today": "2021-04-22"}, "output": {"create": {"number": "SITE1210001", "dueDate": "2021-04-24", "status": "draft", "date": "2021-04-22", "isPrinted": false, "site": {"businessEntity": {"taxIdNumber": "***********"}}, "salesSiteName": "Chem. Atlanta", "salesSiteTaxIdNumber": "***********", "salesSiteLinkedAddress": {"name": "US001"}, "salesSiteAddress": {"name": "US001"}, "salesSiteContact": null, "billToCustomer": {"businessEntity": {"name": "Dépot de TOULOUSE - Sud Ouest"}}, "billToCustomerName": "Dépot de TOULOUSE - Sud Ouest", "billToCustomerTaxIdNumber": "***********", "billToLinkedAddress": {"addressLine2": ""}, "billToAddress": {"region": "AZ"}, "billToContact": {"preferredName": "<PERSON>"}, "currency": {"symbol": "R"}, "paymentTerm": {"description": ""}, "financeIntegrationStatus": "toBeRecorded", "taxes": {"query": {"edges": [{"node": {"taxCategory": "test", "tax": "test", "nonTaxableAmount": "20", "taxRate": "22.2", "taxAmount": "37", "exemptAmount": "28", "taxableAmount": "37", "currency": {"id": "ZAR"}}}]}}, "lines": {"query": {"edges": [{"node": {"origin": "direct", "amountExcludingTax": "48", "amountIncludingTax": "48", "amountExcludingTaxInCompanyCurrency": "761.76", "amountIncludingTaxInCompanyCurrency": "761.76", "discountCharges": {"query": {"edges": [{"node": {"sign": "increase", "valueType": "percentage", "calculationBasis": "grossPrice", "calculationRule": "byUnit", "basis": "2", "value": "20", "amount": "0.4"}}]}}, "taxes": {"query": {"edges": [{"node": {"taxCategory": "test", "tax": "test", "nonTaxableAmount": "20", "taxRate": "22.2", "taxAmount": "37", "exemptAmount": "28", "taxableAmount": "37"}}]}}}}, {"node": {"origin": "direct", "amountExcludingTax": "32", "amountIncludingTax": "32", "amountExcludingTaxInCompanyCurrency": "507.84", "amountIncludingTaxInCompanyCurrency": "507.84", "discountCharges": {"query": {"edges": [{"node": {"sign": "decrease", "valueType": "percentage", "calculationBasis": "grossPrice", "calculationRule": "byUnit", "basis": "2", "value": "20", "amount": "0.4"}}]}}, "taxes": {"query": {"edges": [{"node": {"taxCategory": "test", "tax": "test", "nonTaxableAmount": "20", "taxRate": "22.2", "taxAmount": "37", "exemptAmount": "28", "taxableAmount": "0"}}]}}}}]}}, "totalAmountExcludingTax": "80", "totalAmountIncludingTax": "80", "creationNumber": "", "companyCurrency": {"id": "USD"}, "companyFxRate": "15.87", "rateDescription": "1 ZAR = 15.87 USD", "totalAmountIncludingTaxInCompanyCurrency": "1269.6", "totalAmountExcludingTaxInCompanyCurrency": "1269.6"}}}, "Create sales invoice null addresses": {"input": {"properties": {"number": "SINV4567", "site": "#US001", "incoterm": "#EXW", "billToCustomer": "#US019", "lines": [{"item": "#SalesItem81", "quantity": 20, "charge": 20, "grossPrice": 2, "discountCharges": [{"sign": "increase", "valueType": "percentage", "calculationBasis": "grossPrice", "basis": 2, "value": 20}]}, {"item": "#SalesItem81", "quantity": 20, "grossPrice": 2, "discountCharges": [{"sign": "increase", "valueType": "percentage", "calculationBasis": "grossPrice", "basis": 2, "value": 20}], "consumptionLinkedAddress": "#500|800", "providerSiteLinkedAddress": "#500|900"}], "billToContact": {"title": "mr", "firstName": "test", "lastName": "test"}, "billToAddress": {"name": "test"}, "salesSiteAddress": {"name": "test"}, "salesSiteContact": {"title": "mr", "firstName": "test", "lastName": "test"}}}, "envConfigs": {"today": "2021-04-22"}, "output": {"create": {"number": "SINV4567", "dueDate": "2021-04-22", "status": "draft", "date": "2021-04-22", "isPrinted": false, "site": {"businessEntity": {"taxIdNumber": "***********"}}, "salesSiteName": "Chem. Atlanta", "salesSiteTaxIdNumber": "***********", "salesSiteLinkedAddress": {"name": "US001"}, "salesSiteAddress": {"name": "test"}, "salesSiteContact": {"firstName": "test"}, "billToCustomer": {"businessEntity": {"name": "Dépot de TOULOUSE - Sud Ouest"}}, "billToCustomerName": "Dépot de TOULOUSE - Sud Ouest", "billToCustomerTaxIdNumber": "***********", "billToLinkedAddress": {"addressLine2": ""}, "billToAddress": {"region": ""}, "billToContact": {"preferredName": ""}, "currency": {"symbol": "$"}, "paymentTerm": {"description": ""}, "financeIntegrationStatus": "toBeRecorded", "taxes": {"query": {"edges": []}}, "lines": {"query": {"edges": [{"node": {"origin": "direct", "amountExcludingTax": "48", "amountIncludingTax": "48", "amountExcludingTaxInCompanyCurrency": "48", "amountIncludingTaxInCompanyCurrency": "48", "discountCharges": {"query": {"edges": [{"node": {"sign": "increase", "valueType": "percentage", "calculationBasis": "grossPrice", "calculationRule": "byUnit", "basis": "2", "value": "20", "amount": "0.4"}}]}}, "taxes": {"query": {"edges": []}}}}, {"node": {"origin": "direct", "amountExcludingTax": "48", "amountIncludingTax": "48", "amountExcludingTaxInCompanyCurrency": "48", "amountIncludingTaxInCompanyCurrency": "48", "discountCharges": {"query": {"edges": [{"node": {"sign": "increase", "valueType": "percentage", "calculationBasis": "grossPrice", "calculationRule": "byUnit", "basis": "2", "value": "20", "amount": "0.4"}}]}}, "taxes": {"query": {"edges": []}}}}]}}, "totalAmountExcludingTax": "96", "totalAmountIncludingTax": "96", "creationNumber": "", "companyCurrency": {"id": "USD"}, "companyFxRate": "1", "rateDescription": "1 USD = 1 USD", "totalAmountIncludingTaxInCompanyCurrency": "96", "totalAmountExcludingTaxInCompanyCurrency": "96"}}}, "Create sales invoice with discount and charge generated": {"input": {"properties": {"site": "#US001", "incoterm": "#EXW", "billToCustomer": "#US019", "lines": [{"item": "#SalesItem81", "quantity": 20}, {"item": "#SalesItem81", "quantity": 40}]}}, "envConfigs": {"today": "2021-05-07"}, "output": {"create": {"number": "SITE1210001", "dueDate": "2021-05-07", "status": "draft", "date": "2021-05-07", "isPrinted": false, "site": {"businessEntity": {"taxIdNumber": "***********"}}, "salesSiteName": "Chem. Atlanta", "salesSiteTaxIdNumber": "***********", "salesSiteLinkedAddress": {"name": "US001"}, "salesSiteAddress": {"name": "US001"}, "salesSiteContact": null, "billToCustomer": {"businessEntity": {"name": "Dépot de TOULOUSE - Sud Ouest"}}, "billToCustomerName": "Dépot de TOULOUSE - Sud Ouest", "billToCustomerTaxIdNumber": "***********", "billToLinkedAddress": {"addressLine2": ""}, "billToAddress": {"region": "AZ"}, "billToContact": {"preferredName": "<PERSON>"}, "currency": {"symbol": "$"}, "paymentTerm": {"description": ""}, "financeIntegrationStatus": "toBeRecorded", "taxes": {"query": {"edges": []}}, "lines": {"query": {"edges": [{"node": {"origin": "direct", "amountExcludingTax": "0", "amountIncludingTax": "0", "amountExcludingTaxInCompanyCurrency": "0", "amountIncludingTaxInCompanyCurrency": "0", "discountCharges": {"query": {"edges": [{"node": {"sign": "increase", "valueType": "percentage", "calculationBasis": "grossPrice", "calculationRule": "byUnit", "basis": "0", "value": "0", "amount": "0"}}, {"node": {"sign": "decrease", "valueType": "percentage", "calculationBasis": "grossPrice", "calculationRule": "byUnit", "basis": "0", "value": "0", "amount": "0"}}]}}, "taxes": {"query": {"edges": []}}}}, {"node": {"origin": "direct", "amountExcludingTax": "0", "amountIncludingTax": "0", "amountExcludingTaxInCompanyCurrency": "0", "amountIncludingTaxInCompanyCurrency": "0", "discountCharges": {"query": {"edges": [{"node": {"sign": "increase", "valueType": "percentage", "calculationBasis": "grossPrice", "calculationRule": "byUnit", "basis": "0", "value": "0", "amount": "0"}}, {"node": {"sign": "decrease", "valueType": "percentage", "calculationBasis": "grossPrice", "calculationRule": "byUnit", "basis": "0", "value": "0", "amount": "0"}}]}}, "taxes": {"query": {"edges": []}}}}]}}, "totalAmountExcludingTax": "0", "totalAmountIncludingTax": "0", "creationNumber": "", "companyCurrency": {"id": "USD"}, "companyFxRate": "1", "rateDescription": "1 USD = 1 USD", "totalAmountIncludingTaxInCompanyCurrency": "0", "totalAmountExcludingTaxInCompanyCurrency": "0"}}}, "Create sales invoice linked to a sales shipment": {"input": {"properties": {"site": "#US001", "incoterm": "#EXW", "billToCustomer": "#US020", "lines": [{"item": "#Chemical D", "origin": "shipment", "quantity": 10, "salesShipmentLines": [{"linkedDocument": 1304, "quantityInStockUnit": 10, "quantity": 10, "amount": 316}]}]}}, "envConfigs": {"today": "2021-05-07"}, "output": {"create": {"number": "SITE1210001", "dueDate": "2021-06-21", "status": "draft", "date": "2021-05-07", "isPrinted": false, "site": {"businessEntity": {"taxIdNumber": "***********"}}, "salesSiteName": "Chem. Atlanta", "salesSiteTaxIdNumber": "***********", "salesSiteLinkedAddress": {"name": "US001"}, "salesSiteAddress": {"name": "US001"}, "salesSiteContact": null, "billToCustomer": {"businessEntity": {"name": "Siège social S02 PARIS"}}, "billToCustomerName": "Siège social S02 PARIS", "billToCustomerTaxIdNumber": "*********", "billToLinkedAddress": {"addressLine2": ""}, "billToAddress": {"region": "Some region"}, "billToContact": null, "currency": {"symbol": "£"}, "paymentTerm": {"description": ""}, "financeIntegrationStatus": "toBeRecorded", "taxes": {"query": {"edges": []}}, "lines": {"query": {"edges": [{"node": {"origin": "shipment", "amountExcludingTax": "0", "amountIncludingTax": "0", "amountExcludingTaxInCompanyCurrency": "0", "amountIncludingTaxInCompanyCurrency": "0", "discountCharges": {"query": {"edges": [{"node": {"sign": "increase", "valueType": "percentage", "calculationBasis": "grossPrice", "calculationRule": "byUnit", "basis": "0", "value": "0", "amount": "0"}}, {"node": {"sign": "decrease", "valueType": "percentage", "calculationBasis": "grossPrice", "calculationRule": "byUnit", "basis": "0", "value": "0", "amount": "0"}}]}}, "taxes": {"query": {"edges": []}}}}]}}, "totalAmountExcludingTax": "0", "totalAmountIncludingTax": "0", "creationNumber": "", "companyCurrency": {"id": "USD"}, "companyFxRate": "1.33", "rateDescription": "1 GBP = 1.33 USD", "totalAmountIncludingTaxInCompanyCurrency": "0", "totalAmountExcludingTaxInCompanyCurrency": "0"}}}, "Create sales invoice for FR legislation": {"input": {"properties": {"site": "#ETS1-S01", "incoterm": "#EXW", "dueDate": "2021-04-24", "billToCustomer": "#US019", "currency": "#EUR", "taxes": [{"taxCategory": "test", "tax": "test", "nonTaxableAmount": 20, "taxRate": 22.2, "taxAmount": 16.42, "taxAmountAdjusted": 16.42, "exemptAmount": 28, "taxableAmount": 37}], "lines": [{"item": "#SalesItem81", "quantity": 20, "unit": "#MI<PERSON><PERSON><PERSON>ER", "grossPrice": 2, "discountCharges": [{"sign": "increase", "valueType": "percentage", "calculationBasis": "grossPrice", "basis": 2, "value": 20}], "netPriceExcludingTax": 2.4, "netPriceIncludingTax": 2.4, "taxes": [{"taxCategory": "test", "tax": "test", "nonTaxableAmount": 20, "taxRate": 22.2, "taxAmount": 8.21, "taxAmountAdjusted": 8.21, "exemptAmount": 28, "taxableAmount": 37}]}, {"item": "#SalesItem81", "unit": "#MI<PERSON><PERSON><PERSON>ER", "quantity": 20, "discount": 20, "grossPrice": 2, "discountCharges": [{"sign": "decrease", "valueType": "percentage", "calculationBasis": "grossPrice", "basis": 2, "value": 20}], "providerSiteAddress": {"name": "test"}, "consumptionAddress": {"name": "test"}, "taxes": [{"taxCategory": "test", "tax": "test", "nonTaxableAmount": 20, "taxRate": 22.2, "taxAmount": 8.21, "taxAmountAdjusted": 8.21, "exemptAmount": 28, "taxableAmount": 37}]}]}}, "envConfigs": {"today": "2021-12-21"}, "output": {"create": {"number": "SIEE210001", "dueDate": "2021-04-24", "status": "draft", "date": "2021-12-21", "isPrinted": false, "site": {"businessEntity": {"taxIdNumber": "FR123456789"}}, "salesSiteName": "Siège social S01  PARIS", "salesSiteTaxIdNumber": "FR123456789", "salesSiteLinkedAddress": {"name": "Siege Social S1 Paris"}, "salesSiteAddress": {"name": "Siege Social S1 Paris"}, "salesSiteContact": null, "billToCustomer": {"businessEntity": {"name": "Dépot de TOULOUSE - Sud Ouest"}}, "billToCustomerName": "Dépot de TOULOUSE - Sud Ouest", "billToCustomerTaxIdNumber": "***********", "billToLinkedAddress": {"addressLine2": ""}, "billToAddress": {"region": "AZ"}, "billToContact": {"preferredName": "<PERSON>"}, "currency": {"symbol": "€"}, "paymentTerm": {"description": ""}, "financeIntegrationStatus": "toBeRecorded", "taxes": {"query": {"edges": [{"node": {"taxCategory": "test", "tax": "test", "nonTaxableAmount": "0", "taxRate": "22.2", "taxAmount": "16.42", "exemptAmount": "0", "taxableAmount": "74", "currency": {"id": "EUR"}}}]}}, "lines": {"query": {"edges": [{"node": {"origin": "direct", "amountExcludingTax": "48", "amountIncludingTax": "56.21", "amountExcludingTaxInCompanyCurrency": "48", "amountIncludingTaxInCompanyCurrency": "56.21", "discountCharges": {"query": {"edges": [{"node": {"sign": "increase", "valueType": "percentage", "calculationBasis": "grossPrice", "calculationRule": "byUnit", "basis": "2", "value": "20", "amount": "0.4"}}]}}, "taxes": {"query": {"edges": [{"node": {"taxCategory": "test", "tax": "test", "nonTaxableAmount": "20", "taxRate": "22.2", "taxAmount": "8.21", "exemptAmount": "28", "taxableAmount": "37"}}]}}}}, {"node": {"origin": "direct", "amountExcludingTax": "32", "amountIncludingTax": "40.21", "amountExcludingTaxInCompanyCurrency": "32", "amountIncludingTaxInCompanyCurrency": "40.21", "discountCharges": {"query": {"edges": [{"node": {"sign": "decrease", "valueType": "percentage", "calculationBasis": "grossPrice", "calculationRule": "byUnit", "basis": "2", "value": "20", "amount": "0.4"}}]}}, "taxes": {"query": {"edges": [{"node": {"taxCategory": "test", "tax": "test", "nonTaxableAmount": "20", "taxRate": "22.2", "taxAmount": "8.21", "exemptAmount": "28", "taxableAmount": "37"}}]}}}}]}}, "totalAmountExcludingTax": "80", "totalAmountIncludingTax": "96.42", "creationNumber": "SIEE210001", "companyCurrency": {"id": "EUR"}, "companyFxRate": "1", "rateDescription": "1 EUR = 1 EUR", "totalAmountIncludingTaxInCompanyCurrency": "96.42", "totalAmountExcludingTaxInCompanyCurrency": "80"}}}, "Create sales invoice for FR legislation with number": {"input": {"properties": {"number": "FR1234567890", "site": "#ETS1-S01", "incoterm": "#EXW", "dueDate": "2021-04-24", "billToCustomer": "#US019", "currency": "#EUR", "taxes": [{"taxCategory": "test", "tax": "test", "nonTaxableAmount": 20, "taxRate": 22.2, "taxAmount": 16.42, "exemptAmount": 28, "taxableAmount": 37, "taxAmountAdjusted": 16.42}], "lines": [{"item": "#SalesItem81", "quantity": 20, "grossPrice": 2, "unit": "#MI<PERSON><PERSON><PERSON>ER", "discountCharges": [{"sign": "increase", "valueType": "percentage", "calculationBasis": "grossPrice", "basis": 2, "value": 20}], "netPriceExcludingTax": 2.4, "netPriceIncludingTax": 2.4, "taxes": [{"taxCategory": "test", "tax": "test", "nonTaxableAmount": 20, "taxRate": 22.2, "taxAmount": 8.21, "exemptAmount": 28, "taxableAmount": 37, "taxAmountAdjusted": 8.21}]}, {"item": "#SalesItem81", "unit": "#MI<PERSON><PERSON><PERSON>ER", "quantity": 20, "discount": 20, "grossPrice": 2, "discountCharges": [{"sign": "decrease", "valueType": "percentage", "calculationBasis": "grossPrice", "basis": 2, "value": 20}], "providerSiteAddress": {"name": "test"}, "consumptionAddress": {"name": "test"}, "taxes": [{"taxCategory": "test", "tax": "test", "nonTaxableAmount": 20, "taxRate": 22.2, "taxAmount": 8.21, "exemptAmount": 28, "taxableAmount": 37, "taxAmountAdjusted": 8.21}]}]}}, "envConfigs": {"today": "2021-12-21"}, "output": {"create": {"number": "FR1234567890", "dueDate": "2021-04-24", "status": "draft", "date": "2021-12-21", "isPrinted": false, "site": {"businessEntity": {"taxIdNumber": "FR123456789"}}, "salesSiteName": "Siège social S01  PARIS", "salesSiteTaxIdNumber": "FR123456789", "salesSiteLinkedAddress": {"name": "Siege Social S1 Paris"}, "salesSiteAddress": {"name": "Siege Social S1 Paris"}, "salesSiteContact": null, "billToCustomer": {"businessEntity": {"name": "Dépot de TOULOUSE - Sud Ouest"}}, "billToCustomerName": "Dépot de TOULOUSE - Sud Ouest", "billToCustomerTaxIdNumber": "***********", "billToLinkedAddress": {"addressLine2": ""}, "billToAddress": {"region": "AZ"}, "billToContact": {"preferredName": "<PERSON>"}, "currency": {"symbol": "€"}, "paymentTerm": {"description": ""}, "financeIntegrationStatus": "toBeRecorded", "taxes": {"query": {"edges": [{"node": {"taxCategory": "test", "tax": "test", "nonTaxableAmount": "0", "taxRate": "22.2", "taxAmount": "16.42", "exemptAmount": "0", "taxableAmount": "74", "currency": {"id": "EUR"}}}]}}, "lines": {"query": {"edges": [{"node": {"origin": "direct", "amountExcludingTax": "48", "amountIncludingTax": "56.21", "amountExcludingTaxInCompanyCurrency": "48", "amountIncludingTaxInCompanyCurrency": "56.21", "discountCharges": {"query": {"edges": [{"node": {"sign": "increase", "valueType": "percentage", "calculationBasis": "grossPrice", "calculationRule": "byUnit", "basis": "2", "value": "20", "amount": "0.4"}}]}}, "taxes": {"query": {"edges": [{"node": {"taxCategory": "test", "tax": "test", "nonTaxableAmount": "20", "taxRate": "22.2", "taxAmount": "8.21", "exemptAmount": "28", "taxableAmount": "37"}}]}}}}, {"node": {"origin": "direct", "amountExcludingTax": "32", "amountIncludingTax": "40.21", "amountExcludingTaxInCompanyCurrency": "32", "amountIncludingTaxInCompanyCurrency": "40.21", "discountCharges": {"query": {"edges": [{"node": {"sign": "decrease", "valueType": "percentage", "calculationBasis": "grossPrice", "calculationRule": "byUnit", "basis": "2", "value": "20", "amount": "0.4"}}]}}, "taxes": {"query": {"edges": [{"node": {"taxCategory": "test", "tax": "test", "nonTaxableAmount": "20", "taxRate": "22.2", "taxAmount": "8.21", "exemptAmount": "28", "taxableAmount": "37"}}]}}}}]}}, "totalAmountExcludingTax": "80", "totalAmountIncludingTax": "96.42", "creationNumber": "FR1234567890", "companyCurrency": {"id": "EUR"}, "companyFxRate": "1", "rateDescription": "1 EUR = 1 EUR", "totalAmountIncludingTaxInCompanyCurrency": "96.42", "totalAmountExcludingTaxInCompanyCurrency": "80"}}}, "Create sales invoice tax determination chosen by user": {"input": {"properties": {"site": "#ETS1-S01", "incoterm": "#EXW", "dueDate": "2021-04-24", "billToCustomer": "#US019", "currency": "#EUR", "lines": [{"item": "#SalesItem81", "quantity": 20, "unit": "#MI<PERSON><PERSON><PERSON>ER", "grossPrice": 2, "discountCharges": [{"sign": "increase", "valueType": "percentage", "calculationBasis": "grossPrice", "basis": 2, "value": 20}], "netPriceExcludingTax": 2.4, "netPriceIncludingTax": 2.4, "taxes": [{"taxReference": "#FR_TVA_REDUCED_COLLECTED_ON_PAYMENT", "taxRate": 0}]}, {"item": "#SalesItem81", "unit": "#MI<PERSON><PERSON><PERSON>ER", "quantity": 20, "discount": 20, "grossPrice": 2, "discountCharges": [{"sign": "decrease", "valueType": "percentage", "calculationBasis": "grossPrice", "basis": 2, "value": 20}], "providerSiteAddress": {"name": "test"}, "consumptionAddress": {"name": "test"}, "taxes": [{"taxReference": "#FR_TVA_REDUCED_COLLECTED_ON_PAYMENT", "taxRate": 0}]}]}}, "envConfigs": {"today": "2021-12-21"}, "output": {"create": {"number": "SIEE210001", "dueDate": "2021-04-24", "status": "draft", "date": "2021-12-21", "isPrinted": false, "site": {"businessEntity": {"taxIdNumber": "FR123456789"}}, "salesSiteName": "Siège social S01  PARIS", "salesSiteTaxIdNumber": "FR123456789", "salesSiteLinkedAddress": {"name": "Siege Social S1 Paris"}, "salesSiteAddress": {"name": "Siege Social S1 Paris"}, "salesSiteContact": null, "billToCustomer": {"businessEntity": {"name": "Dépot de TOULOUSE - Sud Ouest"}}, "billToCustomerName": "Dépot de TOULOUSE - Sud Ouest", "billToCustomerTaxIdNumber": "***********", "billToLinkedAddress": {"addressLine2": ""}, "billToAddress": {"region": "AZ"}, "billToContact": {"preferredName": "<PERSON>"}, "currency": {"symbol": "€"}, "paymentTerm": {"description": ""}, "financeIntegrationStatus": "toBeRecorded", "taxes": {"query": {"edges": [{"node": {"taxCategory": "Value Added Tax", "tax": "Tax collected on payments, reduced rate", "nonTaxableAmount": "0", "taxRate": "5.5", "taxAmount": "4.4", "exemptAmount": "0", "taxableAmount": "80", "currency": {"id": "EUR"}}}]}}, "lines": {"query": {"edges": [{"node": {"origin": "direct", "amountExcludingTax": "48", "amountIncludingTax": "50.64", "amountExcludingTaxInCompanyCurrency": "48", "amountIncludingTaxInCompanyCurrency": "50.64", "discountCharges": {"query": {"edges": [{"node": {"sign": "increase", "valueType": "percentage", "calculationBasis": "grossPrice", "calculationRule": "byUnit", "basis": "2", "value": "20", "amount": "0.4"}}]}}, "taxes": {"query": {"edges": [{"node": {"tax": "Tax collected on payments, reduced rate", "taxAmount": "2.64", "taxCategory": "Value Added Tax", "taxRate": "5.5", "nonTaxableAmount": "0", "exemptAmount": "0", "taxableAmount": "48"}}, {"node": {"exemptAmount": "0", "nonTaxableAmount": "0", "tax": "", "taxAmount": "0", "taxCategory": "TEST", "taxRate": "0", "taxableAmount": "48"}}]}}}}, {"node": {"origin": "direct", "amountExcludingTax": "32", "amountIncludingTax": "33.76", "amountExcludingTaxInCompanyCurrency": "32", "amountIncludingTaxInCompanyCurrency": "33.76", "discountCharges": {"query": {"edges": [{"node": {"sign": "decrease", "valueType": "percentage", "calculationBasis": "grossPrice", "calculationRule": "byUnit", "basis": "2", "value": "20", "amount": "0.4"}}]}}, "taxes": {"query": {"edges": [{"node": {"tax": "Tax collected on payments, reduced rate", "taxAmount": "1.76", "taxCategory": "Value Added Tax", "taxRate": "5.5", "nonTaxableAmount": "0", "exemptAmount": "0", "taxableAmount": "32"}}, {"node": {"exemptAmount": "0", "nonTaxableAmount": "0", "tax": "", "taxAmount": "0", "taxCategory": "TEST", "taxRate": "0", "taxableAmount": "32"}}]}}}}]}}, "totalAmountExcludingTax": "80", "totalAmountIncludingTax": "84.4", "creationNumber": "SIEE210001", "companyCurrency": {"id": "EUR"}, "companyFxRate": "1", "rateDescription": "1 EUR = 1 EUR", "totalAmountIncludingTaxInCompanyCurrency": "84.4", "totalAmountExcludingTaxInCompanyCurrency": "80"}}}, "Create sales invoice tax determination chosen by user with currency different than the company currency": {"input": {"properties": {"site": "#ETS1-S01", "incoterm": "#EXW", "dueDate": "2021-04-24", "billToCustomer": "#US019", "currency": "#USD", "lines": [{"item": "#SalesItem81", "quantity": 20, "unit": "#MI<PERSON><PERSON><PERSON>ER", "grossPrice": 2, "discountCharges": [{"sign": "increase", "valueType": "percentage", "calculationBasis": "grossPrice", "basis": 2, "value": 20}], "netPriceExcludingTax": 2.4, "netPriceIncludingTax": 2.4, "taxes": [{"taxReference": "#FR_TVA_REDUCED_COLLECTED_ON_PAYMENT", "taxRate": 0}]}, {"item": "#SalesItem81", "unit": "#MI<PERSON><PERSON><PERSON>ER", "quantity": 20, "discount": 20, "grossPrice": 2, "discountCharges": [{"sign": "decrease", "valueType": "percentage", "calculationBasis": "grossPrice", "basis": 2, "value": 20}], "providerSiteAddress": {"name": "test"}, "consumptionAddress": {"name": "test"}, "taxes": [{"taxReference": "#FR_TVA_REDUCED_COLLECTED_ON_PAYMENT", "taxRate": 0}]}]}}, "envConfigs": {"today": "2022-04-13"}, "output": {"create": {"number": "SIEE220001", "dueDate": "2021-04-24", "status": "draft", "date": "2022-04-13", "isPrinted": false, "site": {"businessEntity": {"taxIdNumber": "FR123456789"}}, "salesSiteName": "Siège social S01  PARIS", "salesSiteTaxIdNumber": "FR123456789", "salesSiteLinkedAddress": {"name": "Siege Social S1 Paris"}, "salesSiteAddress": {"name": "Siege Social S1 Paris"}, "salesSiteContact": null, "billToCustomer": {"businessEntity": {"name": "Dépot de TOULOUSE - Sud Ouest"}}, "billToCustomerName": "Dépot de TOULOUSE - Sud Ouest", "billToCustomerTaxIdNumber": "***********", "billToLinkedAddress": {"addressLine2": ""}, "billToAddress": {"region": "AZ"}, "billToContact": {"preferredName": "<PERSON>"}, "currency": {"symbol": "$"}, "paymentTerm": {"description": ""}, "financeIntegrationStatus": "toBeRecorded", "taxes": {"query": {"edges": [{"node": {"taxCategory": "Value Added Tax", "tax": "Tax collected on payments, reduced rate", "nonTaxableAmount": "0", "exemptAmount": "0", "taxRate": "5.5", "taxAmount": "4.4", "taxableAmount": "80", "currency": {"id": "USD"}}}]}}, "lines": {"query": {"edges": [{"node": {"origin": "direct", "amountExcludingTax": "48", "amountIncludingTax": "50.64", "amountExcludingTaxInCompanyCurrency": "47.9", "amountIncludingTaxInCompanyCurrency": "50.54", "discountCharges": {"query": {"edges": [{"node": {"sign": "increase", "valueType": "percentage", "calculationBasis": "grossPrice", "calculationRule": "byUnit", "basis": "2", "value": "20", "amount": "0.4"}}]}}, "taxes": {"query": {"edges": [{"node": {"tax": "Tax collected on payments, reduced rate", "taxAmount": "2.64", "taxCategory": "Value Added Tax", "taxRate": "5.5", "nonTaxableAmount": "0", "exemptAmount": "0", "taxableAmount": "48"}}, {"node": {"exemptAmount": "0", "nonTaxableAmount": "0", "tax": "", "taxAmount": "0", "taxCategory": "TEST", "taxRate": "0", "taxableAmount": "48"}}]}}}}, {"node": {"origin": "direct", "amountExcludingTax": "32", "amountIncludingTax": "33.76", "amountExcludingTaxInCompanyCurrency": "31.94", "amountIncludingTaxInCompanyCurrency": "33.69", "discountCharges": {"query": {"edges": [{"node": {"sign": "decrease", "valueType": "percentage", "calculationBasis": "grossPrice", "calculationRule": "byUnit", "basis": "2", "value": "20", "amount": "0.4"}}]}}, "taxes": {"query": {"edges": [{"node": {"tax": "Tax collected on payments, reduced rate", "taxAmount": "1.76", "taxCategory": "Value Added Tax", "taxRate": "5.5", "nonTaxableAmount": "0", "exemptAmount": "0", "taxableAmount": "32"}}, {"node": {"exemptAmount": "0", "nonTaxableAmount": "0", "tax": "", "taxAmount": "0", "taxCategory": "TEST", "taxRate": "0", "taxableAmount": "32"}}]}}}}]}}, "totalAmountExcludingTax": "80", "totalAmountIncludingTax": "84.4", "creationNumber": "SIEE220001", "companyCurrency": {"id": "EUR"}, "companyFxRate": "1", "rateDescription": "1 USD = 0.********* EUR", "totalAmountIncludingTaxInCompanyCurrency": "84.23", "totalAmountExcludingTaxInCompanyCurrency": "79.84"}}}}