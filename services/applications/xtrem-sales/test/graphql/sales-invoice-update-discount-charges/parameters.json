{"Sales invoice update discount charges": {"input": {"properties": {"_id": "#SI1", "lines": [{"_action": "update", "_sortValue": "10", "quantity": 20, "grossPrice": 30, "discountCharges": [{"_action": "update", "_sortValue": "60100", "valueType": "percentage", "value": 20}, {"_action": "update", "_sortValue": "60200", "valueType": "amount", "value": 30}]}]}}, "output": {"data": {"xtremSales": {"salesInvoice": {"update": {"totalAmountExcludingTax": "120", "lines": {"query": {"edges": [{"node": {"quantity": "20", "grossPrice": "30", "netPrice": "6", "amountExcludingTax": "120", "discountCharges": {"query": {"edges": [{"node": {"sign": "increase", "value": "20", "basis": "30", "amount": "6", "calculationRule": "byUnit", "valueType": "percentage"}}, {"node": {"sign": "decrease", "value": "30", "basis": "30", "amount": "30", "calculationRule": "byUnit", "valueType": "amount"}}]}}}}]}}}}}}}}}