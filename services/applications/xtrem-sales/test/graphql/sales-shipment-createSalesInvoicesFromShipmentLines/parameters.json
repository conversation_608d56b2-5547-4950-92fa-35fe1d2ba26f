{"Create sales shipment using mutation": {"input": {"arrayProperties": {"salesDocumentLines": [{"salesDocumentLine": "#SSH1|130000", "quantityToProcess": 10}, {"salesDocumentLine": "#SSH3|130200", "quantityToProcess": 10}]}}, "output": {"createSalesInvoicesFromShipmentLines": {"status": "salesShipmentIsInvoiced", "numberOfInvoices": 2, "lineErrors": []}}, "envConfigs": {"today": "2020-11-27"}}, "Create sales shipment using mutation empty array": {"input": {"arrayProperties": {"salesDocumentLines": []}}, "output": {"createSalesInvoicesFromShipmentLines": {"status": "parametersAreIncorrect", "numberOfInvoices": 0, "lineErrors": []}}, "envConfigs": {"today": "2020-11-27"}}}