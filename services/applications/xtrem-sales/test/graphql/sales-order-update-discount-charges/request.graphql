mutation {
    xtremSales {
        salesOrder {
            update(
                data: {
                    _id: "#SO5"
                    lines: [
                        {
                            _action: "update"
                            _sortValue: "60400"
                            grossPrice: 30
                            discountCharges: [
                                { _action: "update", _id: "7", valueType: "percentage", value: 20 }
                                { _action: "update", _id: "8", valueType: "amount", value: 30 }
                            ]
                        }
                        {
                            _action: "update"
                            _sortValue: "60500"
                            grossPrice: 80
                            discountCharges: [
                                {
                                    _action: "update"
                                    _id: "9"
                                    value: 20
                                    calculationRule: "byLine"
                                    valueType: "amount"
                                }
                                {
                                    _action: "update"
                                    _id: "10"
                                    value: 5
                                    calculationRule: "byLine"
                                    valueType: "amount"
                                }
                            ]
                        }
                    ]
                }
            ) {
                totalAmountExcludingTax
                lines {
                    query {
                        edges {
                            node {
                                quantity
                                grossPrice
                                netPrice
                                amountExcludingTax
                                discountCharges {
                                    query {
                                        edges {
                                            node {
                                                sign
                                                value
                                                basis
                                                value
                                                amount
                                                calculationRule
                                                valueType
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}
