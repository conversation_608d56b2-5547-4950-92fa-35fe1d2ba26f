{"data": {"xtremSales": {"salesOrder": {"update": {"totalAmountExcludingTax": "2595", "lines": {"query": {"edges": [{"node": {"quantity": "30", "grossPrice": "30", "netPrice": "6", "amountExcludingTax": "180", "discountCharges": {"query": {"edges": [{"node": {"sign": "increase", "value": "20", "basis": "30", "amount": "6", "calculationRule": "byUnit", "valueType": "percentage"}}, {"node": {"sign": "decrease", "value": "30", "basis": "30", "amount": "30", "calculationRule": "byUnit", "valueType": "amount"}}]}}}}, {"node": {"quantity": "30", "grossPrice": "80", "netPrice": "80.5", "amountExcludingTax": "2415", "discountCharges": {"query": {"edges": [{"node": {"sign": "increase", "value": "20", "basis": "80", "amount": "20", "calculationRule": "byLine", "valueType": "amount"}}, {"node": {"sign": "decrease", "value": "5", "basis": "80", "amount": "5", "calculationRule": "byLine", "valueType": "amount"}}]}}}}]}}}}}}}