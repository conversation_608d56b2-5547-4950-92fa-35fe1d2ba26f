{"Propose allocation to transfer": {"executionMode": "normal", "envConfigs": {"testActiveServiceOptions": ["serialNumberOption"]}, "input": {"properties": {"orderDocumentLine": "1203", "requiredQuantity": "4", "hasAllAllocationsReturned": false}}, "output": [{"quantityToTransfer": "4", "quantity": "5", "stockRecord": {"quantityInStockUnit": "1000", "owner": "US001", "totalAllocated": "5", "activeQuantityInStockUnit": "1000"}, "stockUnit": {"id": "EACH"}, "orderDocumentLine": {"documentNumber": "SO220003", "documentId": 97}, "serialNumbers": [{"id": "SALES-ALLOC-01"}, {"id": "SALES-ALLOC-02"}, {"id": "SALES-ALLOC-03"}, {"id": "SALES-ALLOC-05"}]}]}}