{"Create sales return receipt - attributes project": {"executionMode": "normal", "variables": {"storedAttributes": "{\"project\":\"AttPROJ\"}"}, "output": {"create": {"lines": {"query": {"edges": [{"node": {"computedAttributes": "{\"financialSite\":\"US001\",\"stockSite\":\"US001\",\"item\":\"LOT_TEST1\",\"businessSite\":\"US001\",\"customer\":\"US019\"}", "storedAttributes": "{\"project\":\"AttPROJ\"}", "storedDimensions": null}}]}}}}, "envConfigs": {"today": "2021-06-28"}}, "Create sales return receipt - attributes and dimensions": {"variables": {"storedAttributes": "{\"project\":\"AttPROJ\"}", "storedDimensions": "{\"dimensionType03\":\"DIMTYPE1VALUE1\",\"dimensionType04\":\"DIMTYPE2VALUE2\"}"}, "output": {"create": {"lines": {"query": {"edges": [{"node": {"computedAttributes": "{\"financialSite\":\"US001\",\"stockSite\":\"US001\",\"item\":\"LOT_TEST1\",\"businessSite\":\"US001\",\"customer\":\"US019\"}", "storedAttributes": "{\"project\":\"AttPROJ\"}", "storedDimensions": "{\"dimensionType03\":\"DIMTYPE1VALUE1\",\"dimensionType04\":\"DIMTYPE2VALUE2\"}"}}]}}}}, "envConfigs": {"today": "2021-06-28"}}}