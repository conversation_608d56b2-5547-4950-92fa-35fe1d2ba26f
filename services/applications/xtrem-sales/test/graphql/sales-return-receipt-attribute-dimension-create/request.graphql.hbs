mutation salesReturnReceipt($storedAttributes: Json, $storedDimensions: Json) {
  xtremSales {
    salesReturnReceipt {
      create(
        data: {
          site: "#US001"
          shipToCustomer: "#US019"
          lines: [
            {
              item: "#LOT_TEST1"
              quantity: 600
              storedAttributes: $storedAttributes
              storedDimensions: $storedDimensions
              stockDetails: [
                {
                  site: "#US001"
                  item: "#Chair"
                  location: 3
                  status: "#A"
                  stockUnit: "#BOX"
                  quantityInStockUnit: 600
                }
              ]
            }
          ]
        }
      ) {
        lines {
          query {
            edges {
              node {
                computedAttributes
                storedAttributes
                storedDimensions
              }
            }
          }
        }
      }
    }
  }
}
