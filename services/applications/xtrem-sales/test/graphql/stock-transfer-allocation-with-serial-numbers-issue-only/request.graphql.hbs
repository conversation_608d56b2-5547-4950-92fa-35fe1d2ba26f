mutation {
    xtremStockData {
         stock {
          updateAllocations(allocationData: {{inputParameters}}) {
            allocationRecord {
              quantityInStockUnit
              serialNumbers{
                query{
                  edges{
                    node{
                      id
                    }
                  }
                }
              }
            }
            oldAllocationRecord {
              _id
              quantityInStockUnit
            }
            resultAction
            stockRecord {
              _id
              quantityInStockUnit
              owner
              totalAllocated
              activeQuantityInStockUnit
            }
          }
        }
    }
}
