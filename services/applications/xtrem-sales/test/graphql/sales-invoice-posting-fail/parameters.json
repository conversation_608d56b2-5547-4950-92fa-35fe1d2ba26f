{"Post sales invoice fail when status is not pending": {"input": {"invoice": "#SI2", "isSafeToRetry": false}, "output": {"errors": [{"extensions": {"code": "operation-error", "diagnoses": [{"message": "The status is not Draft, the invoice cannot be posted.", "path": [], "severity": 4}]}, "message": "Post failed.", "locations": [{"line": 4, "column": 7}], "path": ["xtremSales", "salesInvoice", "post"]}], "data": {"xtremSales": {"salesInvoice": {"post": null}}}}}, "Post sales invoice fail when taxCalculationStatus is not done": {"input": {"invoice": "#SI1", "isSafeToRetry": false}, "output": {"errors": [{"extensions": {"code": "operation-error", "diagnoses": [{"message": "The tax calculation is not Done, the invoice cannot be posted.", "path": [], "severity": 4}]}, "message": "Post failed.", "locations": [{"line": 4, "column": 7}], "path": ["xtremSales", "salesInvoice", "post"]}], "data": {"xtremSales": {"salesInvoice": {"post": null}}}}}, "Post sales invoice fail for FR": {"input": {"invoice": "#SI9", "isSafeToRetry": false}, "envConfigs": {"today": "2022-01-10"}, "output": {"errors": [{"extensions": {"code": "operation-error", "diagnoses": [{"message": "The document date cannot be later than today.", "path": [], "severity": 4}]}, "message": "Post failed.", "locations": [{"line": 4, "column": 7}], "path": ["xtremSales", "salesInvoice", "post"]}], "data": {"xtremSales": {"salesInvoice": {"post": null}}}}}, "Post sales invoice fail when status is not pending - isSafeToRetry = true": {"input": {"invoice": "#SI2", "isSafeToRetry": true}, "output": {"data": {"xtremSales": {"salesInvoice": {"post": {"status": "posted"}}}}}}}