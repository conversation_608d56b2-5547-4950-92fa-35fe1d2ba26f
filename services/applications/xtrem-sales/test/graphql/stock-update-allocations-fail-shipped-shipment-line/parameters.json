{"Update allocations fail - already shipped shipment line": {"executionMode": "normal", "input": {"properties": {"documentLine": "1316", "allocationUpdates": [{"action": "transfer", "quantityToTransfer": "3", "allocationRecord": "120401"}]}}, "output": {"errors": [{"extensions": {"code": "operation-error", "diagnoses": [{"message": "Wrong data received for update-allocations: \n\t[allocationUpdate(0)] - The transfer-allocation cannot be performed on a shipped shipment line.\n\tPlease check your payload.", "path": [], "severity": 4}]}, "locations": [{"column": 11, "line": 4}], "message": "Update allocations failed.", "path": ["xtremStockData", "stock", "updateAllocations"]}]}}}