mutation {
    xtremSales {
        salesCreditMemo {
            update(
                data: {
                    _id: "#SCE1210005"
                    lines: [
                        {
                            _action: "update"
                            _id: "1600"
                            grossPrice: 30
                            discountCharges: [
                                { _action: "update", _id: "1300", valueType: "percentage", value: 20 }
                                { _action: "update", _id: "1301", valueType: "amount", value: 30 }
                            ]
                        }
                    ]
                }
            ) {
                totalAmountExcludingTax
                lines {
                    query {
                        edges {
                            node {
                                quantity
                                grossPrice
                                netPrice
                                amountExcludingTax
                                discountCharges {
                                    query {
                                        edges {
                                            node {
                                                sign
                                                value
                                                basis
                                                value
                                                amount
                                                calculationRule
                                                valueType
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}
