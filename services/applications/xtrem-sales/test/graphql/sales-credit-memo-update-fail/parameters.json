{"Update sales credit memo fail posted status": {"input": {"properties": {"_id": "#SCE1210006", "lines": [{"_id": "1601", "_action": "update", "grossPrice": 316}]}}, "envConfigs": {"today": "2021-07-05"}, "output": {"errors": [{"extensions": {"code": "operation-error", "diagnoses": [{"message": "The sales credit memo is posted. You cannot update it.", "path": ["lines", "1601"], "severity": 3}]}, "locations": [{"column": 17, "line": 4}], "message": "The record was not updated.", "path": ["xtremSales", "salesCreditMemo", "update"]}]}}, "Update sales credit memo fail update to posted status not allowed": {"input": {"properties": {"_id": "#SCE1210005", "status": "posted", "lines": [{"_id": "1600", "_action": "update", "quantityInStockUnit": 14}]}}, "envConfigs": {"today": "2021-07-05"}, "output": {"errors": [{"extensions": {"code": "operation-error", "diagnoses": [{"message": "You cannot update the document status to Posted.", "path": [], "severity": 3}]}, "locations": [{"column": 17, "line": 4}], "message": "The record was not updated.", "path": ["xtremSales", "salesCreditMemo", "update"]}]}}, "Update sales credit memo fail update stock unit must match the item stock unit": {"input": {"properties": {"_id": "#SCE1210005", "lines": [{"_id": "1600", "_action": "update", "quantity": 20, "netPriceExcludingTax": 2.45, "netPriceIncludingTax": 3.57, "grossPrice": 2.45, "stockUnit": "#METER"}]}}, "envConfigs": {"today": "2021-06-28"}, "output": {"errors": [{"extensions": {"code": "operation-error", "diagnoses": [{"message": "value must be equal to 'GRAM'", "path": ["lines", "1600", "stockUnit"], "severity": 3}]}, "locations": [{"column": 17, "line": 4}], "message": "The record was not updated.", "path": ["xtremSales", "salesCreditMemo", "update"]}]}}, "Update sales credit memo fail Wrong quantity in stock unit": {"input": {"properties": {"_id": "#SCE1210005", "lines": [{"_id": "1600", "_action": "update", "quantityInStockUnit": 20}]}}, "envConfigs": {"today": "2021-06-28"}, "output": {"errors": [{"extensions": {"code": "operation-error", "diagnoses": [{"message": "value must be equal to 14", "path": ["lines", "1600", "quantityInStockUnit"], "severity": 3}]}, "locations": [{"column": 17, "line": 4}], "message": "The record was not updated.", "path": ["xtremSales", "salesCreditMemo", "update"]}]}}, "Update sales credit memo fail Wrong date in the future": {"input": {"properties": {"_id": "#SCE1210005", "date": "2021-04-26"}}, "envConfigs": {"today": "2021-04-24"}, "output": {"errors": [{"extensions": {"code": "operation-error", "diagnoses": [{"message": "The credit memo date cannot be later than today.", "path": [], "severity": 4}]}, "locations": [{"column": 17, "line": 4}], "message": "The record was not updated.", "path": ["xtremSales", "salesCreditMemo", "update"]}]}}, "Update sales credit memo fail Wrong date not in same period": {"input": {"properties": {"_id": "#SCE1210001", "date": "2020-03-05"}}, "envConfigs": {"today": "2021-04-24"}, "output": {"errors": [{"extensions": {"code": "operation-error", "diagnoses": [{"message": "The new credit memo date must be in the same period.", "path": [], "severity": 4}]}, "locations": [{"column": 17, "line": 4}], "message": "The record was not updated.", "path": ["xtremSales", "salesCreditMemo", "update"]}]}}, "Update sales credit memo fail Wrong date earlier than previous credit memo": {"input": {"properties": {"_id": "#SCE1210005", "date": "2021-04-05"}}, "envConfigs": {"today": "2021-04-24"}, "output": {"errors": [{"extensions": {"code": "operation-error"}, "locations": [{"column": 17, "line": 4}], "message": "The record was not updated.", "path": ["xtremSales", "salesCreditMemo", "update"]}]}, "comments": "This test was created for a monthly sequenceNumberType we are now yearly", "executionMode": "skip"}, "Update sales credit memo fail Wrong date later than next credit memo": {"input": {"properties": {"_id": "#SCE1210005", "date": "2021-04-29"}}, "envConfigs": {"today": "2021-04-29"}, "output": {"errors": [{"extensions": {"code": "operation-error"}, "locations": [{"column": 17, "line": 4}], "message": "The record was not updated.", "path": ["xtremSales", "salesCreditMemo", "update"]}]}, "comments": "This test was created for a monthly sequenceNumberType we are now yearly", "executionMode": "skip"}, "Update internalNote on a posted sales invoice header - fail ": {"input": {"properties": {"_id": "#SCE1210006", "internalNote": {"value": "test"}}}, "envConfigs": {"today": "2021-04-28"}, "output": {"errors": [{"message": "The record was not updated.", "locations": [{"line": 4, "column": 17}], "path": ["xtremSales", "salesCreditMemo", "update"], "extensions": {"code": "operation-error", "diagnoses": [{"severity": 4, "path": ["internalNote"], "message": "SalesCreditMemo.internalNote: cannot set value on frozen property"}]}}]}}, "Update externalNote on a posted sales invoice header - fail ": {"input": {"properties": {"_id": "#SCE1210006", "externalNote": {"value": "test"}}}, "envConfigs": {"today": "2021-04-28"}, "output": {"errors": [{"message": "The record was not updated.", "locations": [{"line": 4, "column": 17}], "path": ["xtremSales", "salesCreditMemo", "update"], "extensions": {"code": "operation-error", "diagnoses": [{"severity": 4, "path": ["externalNote"], "message": "SalesCreditMemo.externalNote: cannot set value on frozen property"}]}}]}}, "Update isExternalNote on a posted sales invoice header - fail ": {"input": {"properties": {"_id": "#SCE1210006", "isExternalNote": true}}, "envConfigs": {"today": "2021-04-28"}, "output": {"errors": [{"message": "The record was not updated.", "locations": [{"line": 4, "column": 17}], "path": ["xtremSales", "salesCreditMemo", "update"], "extensions": {"code": "operation-error", "diagnoses": [{"severity": 4, "path": ["isExternalNote"], "message": "SalesCreditMemo.isExternalNote: cannot set value on frozen property"}]}}]}}, "Update internalNote on a posted sales invoice lines - fail ": {"input": {"properties": {"_id": "#SCE1210006", "lines": [{"_id": "1601", "_action": "update", "internalNote": {"value": "test"}}]}}, "envConfigs": {"today": "2021-04-28"}, "output": {"errors": [{"message": "The record was not updated.", "locations": [{"line": 4, "column": 17}], "path": ["xtremSales", "salesCreditMemo", "update"], "extensions": {"code": "operation-error", "diagnoses": [{"severity": 4, "path": ["lines", "1601", "internalNote"], "message": "SalesCreditMemoLine.internalNote: cannot set value on frozen property"}]}}]}}, "Update isExternalNote on a posted sales invoice lines - fail ": {"input": {"properties": {"_id": "#SCE1210006", "lines": [{"_id": "1601", "_action": "update", "isExternalNote": true}]}}, "envConfigs": {"today": "2021-04-28"}, "output": {"errors": [{"message": "The record was not updated.", "locations": [{"line": 4, "column": 17}], "path": ["xtremSales", "salesCreditMemo", "update"], "extensions": {"code": "operation-error", "diagnoses": [{"severity": 4, "path": ["lines", "1601", "isExternalNote"], "message": "SalesCreditMemoLine.isExternalNote: cannot set value on frozen property"}]}}]}}, "Update externalNote on a posted sales invoice lines - fail ": {"input": {"properties": {"_id": "#SCE1210006", "lines": [{"_id": "1601", "_action": "update", "externalNote": {"value": "test"}}]}}, "envConfigs": {"today": "2021-04-28"}, "output": {"errors": [{"message": "The record was not updated.", "locations": [{"line": 4, "column": 17}], "path": ["xtremSales", "salesCreditMemo", "update"], "extensions": {"code": "operation-error", "diagnoses": [{"severity": 4, "path": ["lines", "1601", "externalNote"], "message": "SalesCreditMemoLine.externalNote: cannot set value on frozen property"}]}}]}}}