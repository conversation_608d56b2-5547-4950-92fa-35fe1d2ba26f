mutation {
    xtremSales {
        salesReturnReceipt {
            create(
                data: {
                    site: "#US001"
                    shipToCustomer: "#US019"
                    lines: [
                        {
                            item: "#Chair"
                            quantity: 600
                            stockDetails: [
                                {
                                    site: "#US001"
                                    item: "#Chair"
                                    location: 3
                                    status: "#A"
                                    stockUnit: "#EACH"
                                    quantityInStockUnit: 600
                                }
                            ]
                        }
                    ]
                }
            ) {
                stockSite {
                    name
                }
                shipToCustomer {
                    businessEntity {
                        name
                    }
                }
                shipToCustomerAddress {
                    name
                }
                lines {
                    query {
                        edges {
                            node {
                                origin
                                item {
                                    id
                                    name
                                }
                                itemDescription
                                quantity
                                quantityInStockUnit
                                unit {
                                    name
                                }
                                stockUnit {
                                    name
                                }
                                valuedCost
                                orderCost
                            }
                        }
                    }
                }
            }
        }
    }
}
