{"Create sales invoice fail empty line": {"input": {"properties": {"site": "#US001", "incoterm": "#EXW", "billToCustomer": "#US019"}}, "envConfigs": {"today": "2021-04-22"}, "output": {"errors": [{"extensions": {"code": "operation-error", "diagnoses": [{"message": "The sales invoice must contain at least one line.", "path": [], "severity": 3}]}, "locations": [{"column": 17, "line": 4}], "message": "The record was not created.", "path": ["xtremSales", "salesInvoice", "create"]}]}}, "Create sales invoice error in sales order line to sales invoice line table": {"input": {"properties": {"site": "#US001", "incoterm": "#EXW", "billToCustomer": "#US019", "lines": [{"item": "#SalesItem81", "quantity": 20, "discountCharges": [{"sign": "increase", "valueType": "percentage", "calculationBasis": "grossPrice", "basis": 2, "value": 20}], "netPriceExcludingTax": 2.4, "salesOrderLines": [{"linkedDocument": 602, "quantityInStockUnit": -2, "quantity": -1, "amount": -2}]}]}}, "envConfigs": {"today": "2021-04-22"}, "output": {"errors": [{"extensions": {"code": "operation-error", "diagnoses": [{"message": "value must not be negative", "path": ["lines", "-**********", "salesOrderLines", "-1000000004", "amount"], "severity": 3}]}, "locations": [{"column": 17, "line": 4}], "message": "The record was not created.", "path": ["xtremSales", "salesInvoice", "create"]}]}}, "Create sales invoice fail wrong item": {"input": {"properties": {"site": "#US001", "incoterm": "#EXW", "billToCustomer": "#US019", "lines": [{"item": "#17891", "quantity": 20, "discountCharges": [{"sign": "increase", "valueType": "percentage", "calculationBasis": "grossPrice", "basis": 2, "value": 20}], "netPriceExcludingTax": 2.4, "salesOrderLines": [{"linkedDocument": 601, "quantityInStockUnit": -2, "quantity": -1, "amount": -2}]}]}}, "envConfigs": {"today": "2021-05-07"}, "output": {"errors": [{"extensions": {"code": "operation-error", "diagnoses": [{"message": "Incorrect item. The stock managed items are not allowed.", "path": ["lines", "-**********"], "severity": 3}, {"message": "The record is not valid. You need to select a different record.", "path": ["lines", "-**********", "item"], "severity": 3}, {"message": "value must not be negative", "path": ["lines", "-**********", "salesOrderLines", "-1000000004", "amount"], "severity": 3}]}, "locations": [{"column": 17, "line": 4}], "message": "The record was not created.", "path": ["xtremSales", "salesInvoice", "create"]}]}}, "Create sales invoice fail wrong invoice date": {"input": {"properties": {"site": "#US001", "incoterm": "#EXW", "billToCustomer": "#US019", "date": "2021-06-18", "lines": [{"item": "#SalesItem81", "quantity": 20, "discountCharges": [{"sign": "increase", "valueType": "percentage", "calculationBasis": "grossPrice", "basis": 2, "value": 20}], "netPriceExcludingTax": 2.4}]}}, "envConfigs": {"today": "2021-06-08"}, "output": {"errors": [{"extensions": {"code": "operation-error", "diagnoses": [{"message": "The invoice date cannot be later than today.", "path": [], "severity": 4}]}, "locations": [{"column": 17, "line": 4}], "message": "The record was not created.", "path": ["xtremSales", "salesInvoice", "create"]}]}}, "Create sales invoice fail wrong sales unit": {"input": {"properties": {"site": "#US001", "incoterm": "#EXW", "billToCustomer": "#US019", "lines": [{"item": "#SalesItem81", "unit": "#METER", "quantity": 20, "discountCharges": [{"sign": "increase", "valueType": "percentage", "calculationBasis": "grossPrice", "basis": 2, "value": 20}], "netPriceExcludingTax": 2.4}]}}, "envConfigs": {"today": "2021-06-08"}, "output": {"errors": [{"extensions": {"code": "operation-error", "diagnoses": [{"message": "value must be part of set [<PERSON><PERSON><PERSON><PERSON><PERSON>,LITER]", "path": ["lines", "-**********", "unit"], "severity": 3}]}, "locations": [{"column": 17, "line": 4}], "message": "The record was not created.", "path": ["xtremSales", "salesInvoice", "create"]}]}}, "Create sales invoice fail wrong stock unit": {"input": {"properties": {"site": "#US001", "incoterm": "#EXW", "billToCustomer": "#US019", "lines": [{"item": "#SalesItem81", "stockUnit": "#METER", "quantity": 20, "discountCharges": [{"sign": "increase", "valueType": "percentage", "calculationBasis": "grossPrice", "basis": 2, "value": 20}], "netPriceExcludingTax": 2.4}]}}, "envConfigs": {"today": "2021-06-08"}, "output": {"errors": [{"extensions": {"code": "operation-error", "diagnoses": [{"message": "value must be equal to 'LITER'", "path": ["lines", "-**********", "stockUnit"], "severity": 3}]}, "locations": [{"column": 17, "line": 4}], "message": "The record was not created.", "path": ["xtremSales", "salesInvoice", "create"]}]}}, "Create sales invoice fail wrong tax amounts": {"input": {"properties": {"taxes": [{"taxCategory": "test", "tax": "test", "nonTaxableAmount": -20, "taxRate": -22.2, "taxAmount": -37, "exemptAmount": -28, "taxableAmount": -37}], "site": "#US001", "incoterm": "#EXW", "billToCustomer": "#US019", "lines": [{"item": "#SalesItem81", "quantity": 20}, {"item": "#SalesItem81", "quantity": 40}]}}, "envConfigs": {"today": "2021-06-08"}, "output": {"errors": [{"extensions": {"code": "operation-error", "diagnoses": [{"message": "Percentage value (-22.2) must be between 0 and 100.", "path": ["taxes", "-**********", "taxRate"], "severity": 3}, {"message": "value must not be negative", "path": ["taxes", "-**********", "nonTaxableAmount"], "severity": 3}, {"message": "value must not be negative", "path": ["taxes", "-**********", "taxAmount"], "severity": 3}, {"message": "value must not be negative", "path": ["taxes", "-**********", "exemptAmount"], "severity": 3}, {"message": "value must not be negative", "path": ["taxes", "-**********", "taxableAmount"], "severity": 3}]}, "locations": [{"column": 17, "line": 4}], "message": "The record was not created.", "path": ["xtremSales", "salesInvoice", "create"]}]}}, "Create sales invoice fail with no company currency conversion": {"input": {"properties": {"site": "#ETS1-S01", "incoterm": "#EXW", "dueDate": "2021-04-24", "billToCustomer": "#US019", "currency": "#PLN", "lines": [{"item": "#SalesItem81", "quantity": 20, "unit": "#MI<PERSON><PERSON><PERSON>ER", "grossPrice": 2, "discountCharges": [{"sign": "increase", "valueType": "percentage", "calculationBasis": "grossPrice", "basis": 2, "value": 20}], "netPriceExcludingTax": 2.4, "netPriceIncludingTax": 2.4, "taxes": [{"taxReference": "#FR001", "taxRate": 0}]}, {"item": "#SalesItem81", "unit": "#MI<PERSON><PERSON><PERSON>ER", "quantity": 20, "discount": 20, "grossPrice": 2, "discountCharges": [{"sign": "decrease", "valueType": "percentage", "calculationBasis": "grossPrice", "basis": 2, "value": 20}], "providerSiteAddress": {"name": "test"}, "consumptionAddress": {"name": "test"}, "taxes": [{"taxReference": "#FR001", "taxRate": 0}]}]}}, "output": {"errors": [{"message": "The record was not created.", "locations": [{"line": 4, "column": 17}], "path": ["xtremSales", "salesInvoice", "create"], "extensions": {"code": "operation-error", "diagnoses": [{"severity": 3, "path": ["companyFxRate"], "message": "No exchange rate found."}]}}], "data": {"xtremSales": {"salesInvoice": {"create": null}}}}}, "Create sales invoice with data from stock transfer order": {"executionMode": "normal", "input": {"properties": {"number": "test", "site": "#CAS01", "billToCustomer": "#CAS02", "lines": [{"item": "#SalesItem81", "quantity": "10"}]}}, "output": {"errors": [{"message": "The record was not created.", "locations": [{"line": 4, "column": 17}], "path": ["xtremSales", "salesInvoice", "create"], "extensions": {"code": "operation-error", "diagnoses": [{"severity": 3, "path": ["billToCustomer"], "message": "This value is linked to a stock transfer document and cannot be used for this document. You need to select a different value."}, {"severity": 3, "path": ["lines", "-**********", "item"], "message": "The record is not valid. You need to select a different record."}, {"severity": 3, "path": ["lines", "-**********", "providerSite"], "message": "The record is not valid. You need to select a different record."}]}}], "data": {"xtremSales": {"salesInvoice": {"create": null}}}}}}