{
    xtremSales {
        salesReturnReceipt {
            read(_id: "#SRREC01") {
                number
                stockSite {
                    name
                }
                shipToCustomer {
                    businessEntity {
                        name
                    }
                }
                shipToCustomerAddress {
                    name
                }
                shipToAddress {
                    name
                    addressLine1
                    addressLine2
                    city
                    region
                    postcode
                    country {
                        name
                    }
                    locationPhoneNumber
                }
                shipToContact {
                    title
                    firstName
                    lastName
                    preferredName
                    role
                    position
                    locationPhoneNumber
                    email
                }
                lines {
                    query {
                        edges {
                            node {
                                isReceiptExpected
                                stockDetailStatus
                                origin
                                item {
                                    id
                                    name
                                }
                                itemDescription
                                quantity
                                quantityInStockUnit
                                unit {
                                    name
                                }
                                stockUnit {
                                    name
                                }
                                unitToStockUnitConversionFactor
                                location {
                                    id
                                }
                                lot {
                                    id
                                }
                                stockStatus {
                                    id
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}
