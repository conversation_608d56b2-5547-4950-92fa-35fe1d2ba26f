{"Create a SalesShipment without shipment line": {"input": {"properties": {"number": "test", "stockSite": "#US001", "site": "#US001", "shipToCustomer": "#US019", "shipToCustomerAddress": "#US019|1500", "billToLinkedAddress": "#US019|1500", "date": "2020-11-30", "paymentTerm": "#DUE_UPON_RECEIPT_ALL"}}, "output": {"errors": [{"extensions": {"code": "operation-error", "diagnoses": [{"message": "The document needs at least one line.", "path": [], "severity": 3}]}, "locations": [{"column": 13, "line": 4}], "message": "The record was not created.", "path": ["xtremSales", "salesShipment", "create"]}]}}, "Create a SalesShipment without stock site": {"input": {"properties": {"number": "test", "site": "#US012", "shipToCustomer": "#US019", "shipToCustomerAddress": "US019|1500", "billToLinkedAddress": "US019|1500", "date": "2020-11-30", "paymentTerm": "#DUE_UPON_RECEIPT_ALL", "lines": [{"item": "#Mu<PERSON>li", "quantity": "11", "unitToStockUnitConversionFactor": "1.1", "salesOrderLines": [{"linkedDocument": "#SO6|60600", "quantity": "11"}]}]}}, "output": {"errors": [{"message": "The record was not created.", "locations": [{"line": 4, "column": 13}], "path": ["xtremSales", "salesShipment", "create"], "extensions": {"code": "operation-error", "diagnoses": [{"severity": 3, "path": [], "message": "The stock site of the sales shipment is required."}, {"severity": 3, "path": ["lines", "-1000000002", "item"], "message": "The record is not valid. You need to select a different record."}, {"severity": 4, "path": ["lines", "-1000000002", "stockSiteLinkedAddress"], "message": "SalesShipmentLine.stockSiteLinkedAddress: property is required"}]}}]}}, "Create a SalesShipment with wrong sales unit": {"input": {"properties": {"number": "test", "stockSite": "#US001", "site": "#US001", "shipToCustomer": "#US019", "shipToCustomerAddress": "#US019|1500", "billToLinkedAddress": "#US019|1500", "date": "2020-11-30", "paymentTerm": "#DUE_UPON_RECEIPT_ALL", "lines": [{"item": "#Mu<PERSON>li", "unit": "#METER", "quantity": "11", "unitToStockUnitConversionFactor": "1.1", "salesOrderLines": [{"linkedDocument": "#SO6|60600", "quantity": "11"}]}]}}, "output": {"errors": [{"extensions": {"code": "operation-error", "diagnoses": [{"message": "value must be part of set [OUNCE,GRAM]", "path": ["lines", "-1000000002", "unit"], "severity": 3}]}, "locations": [{"column": 13, "line": 4}], "message": "The record was not created.", "path": ["xtremSales", "salesShipment", "create"]}]}}, "Create a SalesShipment with wrong stock unit": {"input": {"properties": {"number": "test", "stockSite": "#US001", "site": "#US001", "shipToCustomer": "#US019", "shipToCustomerAddress": "#US019|1500", "billToLinkedAddress": "#US019|1500", "date": "2020-11-30", "paymentTerm": "#DUE_UPON_RECEIPT_ALL", "lines": [{"item": "#Mu<PERSON>li", "stockUnit": "#METER", "quantity": "11", "unitToStockUnitConversionFactor": "1.1", "salesOrderLines": [{"linkedDocument": "#SO6|60600", "quantity": "11"}]}]}}, "output": {"errors": [{"extensions": {"code": "operation-error", "diagnoses": [{"message": "value must be equal to 'GRAM'", "path": ["lines", "-1000000002", "stockUnit"], "severity": 3}]}, "locations": [{"column": 13, "line": 4}], "message": "The record was not created.", "path": ["xtremSales", "salesShipment", "create"]}]}}, "Create a SalesShipment wrong unit conversion factor": {"input": {"properties": {"number": "test", "stockSite": "#US001", "site": "#US001", "shipToCustomer": "#US019", "shipToCustomerAddress": "#US019|1500", "billToLinkedAddress": "#US019|1500", "date": "2020-11-30", "paymentTerm": "#DUE_UPON_RECEIPT_ALL", "lines": [{"item": "#Mu<PERSON>li", "quantity": "11", "unitToStockUnitConversionFactor": "0", "salesOrderLines": [{"linkedDocument": "#SO6|60600", "quantity": "11"}]}]}}, "output": {"errors": [{"extensions": {"code": "operation-error", "diagnoses": [{"message": "property cannot be equal to 0", "path": ["lines", "-1000000002", "salesOrderLines", "-1000000003", "quantityInStockUnit"], "severity": 3}, {"message": "property cannot be equal to 0", "path": ["lines", "-1000000002", "unitToStockUnitConversionFactor"], "severity": 3}, {"message": "property cannot be equal to 0", "path": ["lines", "-1000000002", "quantityInStockUnit"], "severity": 3}]}, "locations": [{"column": 13, "line": 4}], "message": "The record was not created.", "path": ["xtremSales", "salesShipment", "create"]}]}}, "Create a SalesShipment with quantity set to 0": {"input": {"properties": {"number": "test", "stockSite": "#US001", "site": "#US001", "shipToCustomer": "#US019", "shipToCustomerAddress": "#US019|1500", "billToLinkedAddress": "#US019|1500", "date": "2020-11-30", "paymentTerm": "#DUE_UPON_RECEIPT_ALL", "lines": [{"item": "#Mu<PERSON>li", "quantity": "0", "unitToStockUnitConversionFactor": "1", "salesOrderLines": [{"linkedDocument": "#SO6|60600", "quantity": "0"}]}]}}, "output": {"errors": [{"extensions": {"code": "operation-error", "diagnoses": [{"message": "property cannot be equal to 0", "path": ["lines", "-1000000002", "quantity"], "severity": 3}, {"message": "property cannot be equal to 0", "path": ["lines", "-1000000002", "salesOrderLines", "-1000000003", "quantityInStockUnit"], "severity": 3}, {"message": "property cannot be equal to 0", "path": ["lines", "-1000000002", "salesOrderLines", "-1000000003", "quantity"], "severity": 3}, {"message": "property cannot be equal to 0", "path": ["lines", "-1000000002", "quantityInStockUnit"], "severity": 3}]}, "locations": [{"column": 13, "line": 4}], "message": "The record was not created.", "path": ["xtremSales", "salesShipment", "create"]}]}}, "Create a SalesShipment with data from stock transfer order": {"input": {"properties": {"number": "test", "site": "#CAS01", "shipToCustomer": "#CAS02", "lines": [{"item": "#Milk", "quantity": "10"}]}}, "output": {"errors": [{"message": "The record was not created.", "locations": [{"line": 4, "column": 13}], "path": ["xtremSales", "salesShipment", "create"], "extensions": {"code": "operation-error", "diagnoses": [{"severity": 3, "path": ["shipToCustomer"], "message": "This value is linked to a stock transfer document and cannot be used for this document. You need to select a different value."}]}}]}}}