{"Create sales return request": {"input": {"properties": {"site": "#US001", "stockSite": "#US001", "billToCustomer": "#US019", "soldToCustomer": "#US019", "shipToCustomer": "#US019", "soldToAddress": {"name": "New name1", "addressLine1": "New addressLine1", "addressLine2": "New addressLine2", "city": "New city", "region": "HDF", "postcode": "12345", "locationPhoneNumber": "1234567890", "country": "#US"}, "soldToContact": {"title": "mr", "firstName": "New firstName1", "lastName": "New lastName1", "preferredName": "New preferredName1", "role": "mainContact", "position": "CEO", "locationPhoneNumber": "+***********", "email": "<EMAIL>"}, "billToAddress": {"name": "New name1", "addressLine1": "New addressLine1", "addressLine2": "New addressLine2", "city": "New city", "region": "HDF", "postcode": "12345", "locationPhoneNumber": "1234567890", "country": "#US"}, "billToContact": {"title": "mr", "firstName": "New firstName1", "lastName": "New lastName1", "preferredName": "New preferredName1", "role": "mainContact", "position": "CEO", "locationPhoneNumber": "+***********", "email": "<EMAIL>"}, "shipToAddress": {"name": "New name1", "addressLine1": "New addressLine1", "addressLine2": "New addressLine2", "city": "New city", "region": "HDF", "postcode": "12345", "locationPhoneNumber": "1234567890", "country": "#US"}, "shipToContact": {"title": "mr", "firstName": "New firstName1", "lastName": "New lastName1", "preferredName": "New preferredName1", "role": "mainContact", "position": "CEO", "locationPhoneNumber": "+***********", "email": "<EMAIL>"}, "incoterm": "#EXW", "lines": [{"item": "#Chair", "quantity": 10, "reason": "#TEST_EXCESS_QUANTITY_SHIPPED"}]}}, "output": {"create": {"status": "draft", "approvalStatus": "draft", "receiptStatus": "notReceived", "creditStatus": "notCredited", "returnType": "receiptAndCreditMemo", "isPrinted": false, "isSent": false, "site": {"name": "Chem. Atlanta"}, "stockSite": {"name": "Chem. Atlanta"}, "requester": {"firstName": "Unit", "lastName": "Test"}, "soldToCustomer": {"businessEntity": {"name": "Dépot de TOULOUSE - Sud Ouest"}}, "soldToLinkedAddress": {"name": "Cust address"}, "shipToCustomer": {"businessEntity": {"name": "Dépot de TOULOUSE - Sud Ouest"}}, "shipToCustomerAddress": {"name": "Cust address"}, "billToLinkedAddress": {"name": "Cust address"}, "billToCustomer": {"businessEntity": {"name": "Dépot de TOULOUSE - Sud Ouest"}}, "deliveryMode": {"name": "Rail"}, "incoterm": {"name": "Ex Work"}, "text": {"value": ""}, "soldToAddress": {"name": "New name1", "addressLine1": "New addressLine1", "addressLine2": "New addressLine2", "city": "New city", "region": "HDF", "postcode": "12345", "country": {"id": "US"}, "locationPhoneNumber": "1234567890"}, "soldToContact": {"title": "mr", "firstName": "New firstName1", "lastName": "New lastName1", "preferredName": "New preferredName1", "role": "mainContact", "position": "CEO", "locationPhoneNumber": "+***********", "email": "<EMAIL>"}, "billToAddress": {"name": "New name1", "addressLine1": "New addressLine1", "addressLine2": "New addressLine2", "city": "New city", "region": "HDF", "postcode": "12345", "country": {"id": "US"}, "locationPhoneNumber": "1234567890"}, "billToContact": {"title": "mr", "firstName": "New firstName1", "lastName": "New lastName1", "preferredName": "New preferredName1", "role": "mainContact", "position": "CEO", "locationPhoneNumber": "+***********", "email": "<EMAIL>"}, "shipToAddress": {"name": "New name1", "addressLine1": "New addressLine1", "addressLine2": "New addressLine2", "city": "New city", "region": "HDF", "postcode": "12345", "country": {"id": "US"}, "locationPhoneNumber": "1234567890"}, "shipToContact": {"title": "mr", "firstName": "New firstName1", "lastName": "New lastName1", "preferredName": "New preferredName1", "role": "mainContact", "position": "CEO", "locationPhoneNumber": "+***********", "email": "<EMAIL>"}, "lines": {"query": {"edges": [{"node": {"origin": "direct", "status": "draft", "receiptStatus": "notReceived", "creditStatus": "notCredited", "isCreditMemoExpected": true, "isReceiptExpected": true, "originShippingSite": {"name": "Chem. Atlanta"}, "item": {"id": "Chair", "name": "Chair"}, "itemDescription": "Chair", "quantity": "10", "quantityInStockUnit": "120", "unit": {"name": "Box"}, "stockUnit": {"name": "Each"}, "reason": {"name": "Excess quantity shipped"}, "text": {"value": ""}, "storedDimensions": null, "storedAttributes": null, "computedAttributes": "{\"financialSite\":\"US001\",\"stockSite\":\"US001\",\"item\":\"Chair\",\"businessSite\":\"US001\",\"customer\":\"US019\"}"}}]}}}}}}