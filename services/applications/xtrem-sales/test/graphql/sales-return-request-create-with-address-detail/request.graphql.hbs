mutation {
    xtremSales {
        salesReturnRequest {
            create(data: {{inputParameters}}) {

                status
                approvalStatus
                receiptStatus
                creditStatus
                returnType
                isPrinted
                isSent
                site {
                    name
                }
                stockSite {
                    name
                }
                requester{
                    firstName,
                    lastName
                }
                soldToCustomer { businessEntity {
                    name
                }}
                soldToLinkedAddress {
                    name
                }
                shipToCustomer { businessEntity {
                    name
                }}
                shipToCustomerAddress{
                    name
                }
                billToLinkedAddress {
                    name
                }
                billToCustomer {  businessEntity {
                    name
                }}
                deliveryMode {
                    name
                }
                incoterm {
                    name
                }
                text{
                    value
                }
            soldToAddress{
              name
              addressLine1
              addressLine2
              city
              region
              postcode
              country{
                id
              }
              locationPhoneNumber
            }
            soldToContact{
              title
              firstName
              lastName
              preferredName
              role
              position
              locationPhoneNumber
              email
            }
            billToAddress{
              name
              addressLine1
              addressLine2
              city
              region
              postcode
              country{
                id
              }
              locationPhoneNumber
            }
            billToContact{
              title
              firstName
              lastName
              preferredName
              role
              position
              locationPhoneNumber
              email
            }
            shipToAddress{
              name
              addressLine1
              addressLine2
              city
              region
              postcode
              country{
                id
              }
              locationPhoneNumber
            }
            shipToContact{
              title
              firstName
              lastName
              preferredName
              role
              position
              locationPhoneNumber
              email
            }
                lines {
                    query {
                        edges {
                            node {
                                origin
                                status
                                receiptStatus
                                creditStatus
                                isCreditMemoExpected
                                isReceiptExpected
                                originShippingSite {
                                    name
                                }
                                item {
                                    id
                                    name
                                }
                                itemDescription
                                quantity
                                quantityInStockUnit
                                unit {
                                    name
                                }
                                stockUnit {
                                    name
                                }
                                reason {
                                    name
                                }
                                text{
                                    value
                                }
                                storedDimensions
                                storedAttributes
                                computedAttributes
                            }
                        }
                    }
                }
            }
        }
    }
}
