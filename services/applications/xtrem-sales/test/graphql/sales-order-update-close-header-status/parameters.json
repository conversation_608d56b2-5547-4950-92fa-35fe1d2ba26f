{"Update status to closed on a sales order using mutation": {"input": {"properties": {"salesOrder": "#SO11"}}, "output": {"data": {"xtremSales": {"salesOrder": {"closeSalesOrder": {"status": "closed"}}}}}}, "Update status to closed on a sales order with item stock managed and status notManaged using mutation": {"input": {"properties": {"salesOrder": "#SO19"}}, "output": {"data": {"xtremSales": {"salesOrder": {"closeSalesOrder": {"status": "closed"}}}}}}, "Update status to closed on a sales order already closed using mutation": {"input": {"properties": {"salesOrder": "#SO4"}}, "output": {"errors": [{"message": "Close sales order failed.", "locations": [{"line": 4, "column": 13}], "path": ["xtremSales", "salesOrder", "closeSalesOrder"], "extensions": {"code": "operation-error", "diagnoses": [{"severity": 4, "message": "The sales order is closed. You cannot close the line.", "path": []}]}}], "data": {"xtremSales": {"salesOrder": {"closeSalesOrder": null}}}}}, "Update status to closed on a sales order fail - some lines are allocated": {"executionMode": "normal", "input": {"properties": {"salesOrder": "#SO220001"}}, "output": {"errors": [{"message": "Close sales order failed.", "locations": [{"line": 4, "column": 13}], "path": ["xtremSales", "salesOrder", "closeSalesOrder"], "extensions": {"code": "operation-error", "diagnoses": [{"severity": 4, "message": "Remove the stock allocation before closing the order.", "path": []}]}}], "data": {"xtremSales": {"salesOrder": {"closeSalesOrder": null}}}}}, "Update status to closed on a sales order fail - some lines are in allocation request in progress": {"executionMode": "normal", "input": {"properties": {"salesOrder": "#SO35"}}, "output": {"errors": [{"message": "Close sales order failed.", "locations": [{"line": 4, "column": 13}], "path": ["xtremSales", "salesOrder", "closeSalesOrder"], "extensions": {"code": "operation-error", "diagnoses": [{"severity": 4, "message": "You can only close the sales order after the allocation request is complete for the lines.", "path": []}]}}], "data": {"xtremSales": {"salesOrder": {"closeSalesOrder": null}}}}}, "Update status to closed on a sales order fail - some lines are in allocation request in progress, isSafeToRetry = true": {"executionMode": "normal", "input": {"properties": {"salesOrder": "#SO35", "isSafeToRetry": true}}, "output": {"data": {"xtremSales": {"salesOrder": {"closeSalesOrder": {"status": "pending"}}}}}}, "Update status to closed on a sales order already closed using mutation, isSafeToRetry = true": {"input": {"properties": {"salesOrder": "#SO4", "isSafeToRetry": true}}, "output": {"data": {"xtremSales": {"salesOrder": {"closeSalesOrder": {"status": "closed"}}}}}}, "Update status to closed on a sales order fail - some lines are allocated, isSafeToRetry = true": {"executionMode": "normal", "input": {"properties": {"salesOrder": "#SO220001", "isSafeToRetry": true}}, "output": {"data": {"xtremSales": {"salesOrder": {"closeSalesOrder": {"status": "pending"}}}}}}}