mutation {
    xtremSales {
        salesInvoice {
            create(data: {{inputParameters}}) {
                currency {
                    name
                }
                billToCustomer {
                    businessEntity {
                        name
                    }
                }
                status
                site {
                    name
                }
                lines {
                    query {
                        edges {
                            node {
                                item {
                                    name
                                }
                                itemDescription
                                quantity
                                unit {
                                    name
                                }
                                amountExcludingTaxInCompanyCurrency
                                amountIncludingTaxInCompanyCurrency
                              	storedDimensions
                              	storedAttributes
                            }
                        }
                    }
                }
                totalAmountExcludingTax
                totalAmountIncludingTax
                companyCurrency {
                    id
                }
            }
        }
    }
}
