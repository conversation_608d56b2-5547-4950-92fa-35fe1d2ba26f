{"Update sales order fail - record not found": {"input": {"properties": {"_id": "9999", "status": "pending"}}, "envConfigs": {"today": "2022-09-16"}, "output": {"errors": [{"extensions": {"code": "operation-error", "diagnoses": [{"message": "SalesOrder: record not found: {\"_id\":9999}", "path": [], "severity": 4}]}, "locations": [{"column": 17, "line": 4}], "message": "The record was not updated.", "path": ["xtremSales", "salesOrder", "update"]}]}}, "Update sales order fail - quantity cannot be lower than the quantity already shipped": {"input": {"properties": {"_id": "#SO5", "lines": [{"_sortValue": "60500", "_action": "update", "quantity": 12}]}}, "envConfigs": {"today": "2022-09-16"}, "output": {"errors": [{"extensions": {"code": "operation-error", "diagnoses": [{"message": "The sales order line quantity cannot be lower than the quantity already shipped.", "path": [], "severity": 4}]}, "locations": [{"column": 17, "line": 4}], "message": "The record was not updated.", "path": ["xtremSales", "salesOrder", "update"]}]}}, "Update sales order fail - Deletion is not allowed. The sales order line is shipped or partially shipped.": {"input": {"properties": {"_id": "#SO12", "lines": [{"_sortValue": "61700", "_action": "delete"}]}}, "envConfigs": {"today": "2022-09-16"}, "output": {"errors": [{"extensions": {"code": "operation-error", "diagnoses": [{"message": "Deletion is not allowed. The sales order line is shipped or partially shipped.", "path": ["lines", "617"], "severity": 3}]}, "locations": [{"column": 17, "line": 4}], "message": "The record was not updated.", "path": ["xtremSales", "salesOrder", "update"]}]}}, "Update sales order fail - Deletion is not allowed. remove the allocation first": {"executionMode": "normal", "input": {"properties": {"_id": "#SO220001", "lines": [{"_sortValue": "10", "_action": "delete"}]}}, "envConfigs": {"today": "2022-09-21"}, "output": {"errors": [{"extensions": {"code": "operation-error", "diagnoses": [{"message": "Remove the stock allocation before deleting the line.", "path": ["lines", "1200"], "severity": 3}]}, "locations": [{"column": 17, "line": 4}], "message": "The record was not updated.", "path": ["xtremSales", "salesOrder", "update"]}]}}, "Update sales order fail - Deletion forbidden - Allocation request in progress": {"executionMode": "normal", "input": {"properties": {"_id": "#SO35", "lines": [{"_sortValue": "10", "_action": "delete"}]}}, "envConfigs": {"today": "2022-09-21"}, "output": {"errors": [{"extensions": {"code": "operation-error", "diagnoses": [{"message": "Automatic allocation needs to finish before you can delete the line.", "path": ["lines", "1215"], "severity": 3}]}, "locations": [{"column": 17, "line": 4}], "message": "The record was not updated.", "path": ["xtremSales", "salesOrder", "update"]}]}}, "Update sales order fail - Update stock site when line allocated": {"executionMode": "normal", "input": {"properties": {"_id": "#SO220001", "lines": [{"_sortValue": "10", "stockSite": "4", "_action": "update"}]}}, "envConfigs": {"today": "2022-09-21"}, "output": {"errors": [{"extensions": {"code": "operation-error", "diagnoses": [{"message": "SalesOrderLine.stockSite: cannot set value on frozen property", "path": ["lines", "", "stockSite"], "severity": 4}]}, "locations": [{"column": 17, "line": 4}], "message": "The record was not updated.", "path": ["xtremSales", "salesOrder", "update"]}]}}}