{"Revoke sales shipment": {"input": {"properties": {"_id": "#SSH3"}}, "output": {"data": {"xtremSales": {"salesShipment": {"revoke": "The sales shipment status has been reverted and set to 'Ready to process'."}}}}, "envConfigs": {"today": "2020-11-27"}}, "Revoke sales shipment not exists": {"input": {"properties": {"_id": "#UNKNOWN"}}, "output": {"data": {"xtremSales": {"salesShipment": {"revoke": ""}}}}, "envConfigs": {"today": "2020-11-27"}}, "Revoke sales shipment wrong status error": {"input": {"properties": {"_id": "#SSH1"}}, "output": {"data": {"xtremSales": {"salesShipment": {"revoke": null}}}, "errors": [{"extensions": {"code": "operation-error", "diagnoses": [{"message": "A shipment can only be reverted if the status is 'Ready to ship'.", "path": [], "severity": 4}]}, "locations": [{"column": 13, "line": 4}], "message": "Revoke failed.", "path": ["xtremSales", "salesShipment", "revoke"]}]}, "envConfigs": {"today": "2020-11-27"}}}