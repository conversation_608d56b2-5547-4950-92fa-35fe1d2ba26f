{"Update status to closed on a sales return request line using mutation": {"input": {"properties": {"salesReturnRequestLine": "#SRR08|10"}}, "output": {"setSalesReturnRequestLineCloseStatus": "salesReturnRequestLineIsNowClosed"}}, "Update status to closed on a sales return request line already closed using mutation": {"input": {"properties": {"salesReturnRequestLine": "#SRR12|10"}}, "output": {"setSalesReturnRequestLineCloseStatus": "salesReturnRequestLineIsAlreadyClosed"}}, "Update status to closed on a sales return request line and update header status to closed using mutation": {"input": {"properties": {"salesReturnRequestLine": "#SRR12|20"}}, "output": {"setSalesReturnRequestLineCloseStatus": "salesReturnRequestLineIsNowClosed"}}}