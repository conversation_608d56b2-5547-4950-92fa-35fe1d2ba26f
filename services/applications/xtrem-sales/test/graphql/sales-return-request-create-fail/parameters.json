{"Create a Sales return request with site and stockSite on different legalCompanies fails on line": {"input": {"properties": {"site": "#US001", "stockSite": "#US003", "soldToCustomer": "#US019", "shipToCustomer": "#US019", "incoterm": "#EXW", "lines": [{"item": "#Chair", "quantity": 10, "reason": "#TEST_EXCESS_QUANTITY_SHIPPED"}]}}, "output": {"errors": [{"message": "The record was not created.", "locations": [{"line": 4, "column": 13}], "path": ["xtremSales", "salesReturnRequest", "create"], "extensions": {"code": "operation-error", "diagnoses": [{"message": "Incorrect stock site on the sales return request. The sales site and the stock site must have the same company.", "path": [], "severity": 3}, {"message": "The site needs to belong to the same legal company.", "path": ["stockSite"], "severity": 3}, {"severity": 3, "path": ["lines", "-1000000002"], "message": "Incorrect origin shipping site on the sales return request line. The origin shipping site on the sales return request line and the the header sales site must have the same company."}, {"message": "The record is not valid. You need to select a different record.", "path": ["lines", "-1000000002", "item"], "severity": 3}, {"message": "The record is not valid. You need to select a different record.", "path": ["lines", "-1000000002", "stockSite"], "severity": 3}]}}]}}, "Create a Sales return request with site and stockSite on different legalCompanies fails on header": {"input": {"properties": {"site": "#US001", "stockSite": "#US003", "soldToCustomer": "#US019", "shipToCustomer": "#US019", "incoterm": "#EXW"}}, "output": {"errors": [{"message": "The record was not created.", "locations": [{"line": 4, "column": 13}], "path": ["xtremSales", "salesReturnRequest", "create"], "extensions": {"code": "operation-error", "diagnoses": [{"message": "Incorrect stock site on the sales return request. The sales site and the stock site must have the same company.", "path": [], "severity": 3}, {"message": "The document needs at least one line.", "path": [], "severity": 3}, {"message": "The site needs to belong to the same legal company.", "path": ["stockSite"], "severity": 3}]}}]}}, "Create a Sales return request with a bad line status fails": {"input": {"properties": {"site": "#US001", "stockSite": "#US003", "soldToCustomer": "#US019", "shipToCustomer": "#US019", "incoterm": "#EXW", "lines": [{"item": "#Chair", "quantity": 10, "status": "inProgress", "reason": "#TEST_EXCESS_QUANTITY_SHIPPED"}]}}, "output": {"errors": [{"message": "The record was not created.", "locations": [{"line": 4, "column": 13}], "path": ["xtremSales", "salesReturnRequest", "create"], "extensions": {"code": "operation-error", "diagnoses": [{"message": "Incorrect stock site on the sales return request. The sales site and the stock site must have the same company.", "path": [], "severity": 3}, {"message": "The site needs to belong to the same legal company.", "path": ["stockSite"], "severity": 3}, {"severity": 3, "path": ["lines", "-1000000002"], "message": "Incorrect sales return request line status. The sales return request line must have the 'Draft' or 'Pending' status during creation."}, {"message": "The sales return request lines status must have the same status than the header.", "path": ["lines", "-1000000002"], "severity": 3}, {"message": "Incorrect origin shipping site on the sales return request line. The origin shipping site on the sales return request line and the the header sales site must have the same company.", "path": ["lines", "-1000000002"], "severity": 3}, {"message": "The record is not valid. You need to select a different record.", "path": ["lines", "-1000000002", "item"], "severity": 3}, {"message": "The record is not valid. You need to select a different record.", "path": ["lines", "-1000000002", "stockSite"], "severity": 3}]}}]}}, "Create a Sales return request without request line": {"input": {"properties": {"site": "#US001", "stockSite": "#US001", "soldToCustomer": "#US019", "shipToCustomer": "#US019"}}, "output": {"errors": [{"extensions": {"code": "operation-error", "diagnoses": [{"message": "The document needs at least one line.", "path": [], "severity": 3}]}, "locations": [{"column": 13, "line": 4}], "message": "The record was not created.", "path": ["xtremSales", "salesReturnRequest", "create"]}]}}, "Create a Sales return request with request date in the future": {"input": {"properties": {"date": "2021-12-31", "site": "#US001", "stockSite": "#US001", "soldToCustomer": "#US019", "shipToCustomer": "#US019", "lines": [{"item": "#Chair", "quantity": 10, "reason": "#TEST_EXCESS_QUANTITY_SHIPPED"}]}}, "output": {"errors": [{"extensions": {"code": "operation-error", "diagnoses": [{"message": "The return request date cannot be later than today.", "path": ["date"], "severity": 3}]}, "locations": [{"column": 13, "line": 4}], "message": "The record was not created.", "path": ["xtremSales", "salesReturnRequest", "create"]}]}, "envConfigs": {"today": "2021-10-06"}}}