mutation {
    xtremSales {
        salesOrder {
            create(data: {{inputParameters}}) {
                expectedDeliveryDate
                doNotShipAfterDate
                doNotShipBeforeDate
                shippingDate
                requestedDeliveryDate
                currency {
                    name
                }
                billToCustomer {
                    businessEntity {
                        name
                    }
                }
                date
                status
                site {
                    name
                }
                shipToCustomer {
                    businessEntity {
                        name
                    }
                }
                lines {
                    query {
                        edges {
                            node {
                                status
                                priceOrigin
                                shippingStatus
                                item {
                                    name
                                }
                                itemDescription
                                quantity
                                unit {
                                    name
                                }
                                stockUnit {
                                    name
                                }
                                amountExcludingTaxInCompanyCurrency
                                amountIncludingTaxInCompanyCurrency
                              	storedDimensions
                              	storedAttributes
                            }
                        }
                    }
                }
                totalAmountExcludingTax
                totalAmountIncludingTax
                totalTaxAmount
                totalTaxableAmount
                totalExemptAmount
                taxCalculationStatus
                companyCurrency {
                    id
                }
                transactionCurrency {
                    id
                }
                companyFxRate
                rateDescription
                totalAmountIncludingTaxInCompanyCurrency
                totalAmountExcludingTaxInCompanyCurrency
            }
        }
    }
}
