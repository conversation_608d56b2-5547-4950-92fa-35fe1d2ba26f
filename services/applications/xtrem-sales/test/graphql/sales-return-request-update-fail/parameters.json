{"Update sales return request fail when soldToCustomer is changed": {"input": {"properties": {"_id": "#SRR01", "soldToCustomer": "#US020"}}, "output": {"errors": [{"message": "The record was not updated.", "locations": [{"line": 4, "column": 17}], "path": ["xtremSales", "salesReturnRequest", "update"], "extensions": {"code": "operation-error", "diagnoses": [{"message": "The sold-to customer of the sales return request must not be changed.", "path": [], "severity": 3}]}}]}}, "Update sales return request fail when site is changed": {"input": {"properties": {"_id": "#SRR01", "site": "#US002"}}, "output": {"errors": [{"message": "The record was not updated.", "locations": [{"line": 4, "column": 17}], "path": ["xtremSales", "salesReturnRequest", "update"], "extensions": {"code": "operation-error", "diagnoses": [{"message": "The sales site of the sales return request must not be changed.", "path": [], "severity": 3}]}}]}}, "Update sales return request fail when return request date isin future": {"input": {"properties": {"_id": "#SRR01", "date": "2021-12-31"}}, "output": {"errors": [{"extensions": {"code": "operation-error", "diagnoses": [{"message": "The return request date cannot be later than today.", "path": ["date"], "severity": 3}]}, "locations": [{"line": 4, "column": 17}], "message": "The record was not updated.", "path": ["xtremSales", "salesReturnRequest", "update"]}]}, "envConfigs": {"today": "2021-10-06"}}, "Update a closed sales return request header internal note ": {"input": {"properties": {"_id": "#SRR07", "internalNote": {"value": "test"}}}, "output": {"errors": [{"message": "The record was not updated.", "locations": [{"line": 4, "column": 17}], "path": ["xtremSales", "salesReturnRequest", "update"], "extensions": {"code": "operation-error", "diagnoses": [{"severity": 4, "path": ["internalNote"], "message": "SalesReturnRequest.internalNote: cannot set value on frozen property"}]}}]}}, "Update a closed sales return request line internal note ": {"input": {"properties": {"_id": "#SRR07", "lines": [{"_id": "#SRR07|10", "_action": "update", "internalNote": {"value": "test"}}]}}, "output": {"errors": [{"message": "The record was not updated.", "locations": [{"line": 4, "column": 17}], "path": ["xtremSales", "salesReturnRequest", "update"], "extensions": {"code": "operation-error", "diagnoses": [{"severity": 4, "path": ["lines", "1722", "internalNote"], "message": "SalesReturnRequestLine.internalNote: cannot set value on frozen property"}]}}]}}, "Update sales return request fail Return type cannot be updated": {"input": {"properties": {"_id": "#SRR01", "status": "pending", "returnType": "creditMemo"}}, "output": {"errors": [{"message": "The record was not updated.", "locations": [{"line": 4, "column": 17}], "path": ["xtremSales", "salesReturnRequest", "update"], "extensions": {"code": "operation-error", "diagnoses": [{"message": "You cannot update the return type. You can only update it if the sales return request status is 'Draft' and if it has not been approved.", "path": [], "severity": 3}, {"message": "This document is at Pending status. You cannot set the sales return request line to draft.", "path": ["lines", "1700"], "severity": 3}, {"message": "Update not allowed. The sales return request line is closed.", "path": ["lines", "1717"], "severity": 3}]}}]}}}