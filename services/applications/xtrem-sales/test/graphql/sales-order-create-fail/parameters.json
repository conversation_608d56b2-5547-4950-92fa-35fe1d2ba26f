{"Create sales order - customer is US019 shipAddress is on US020 ": {"executionMode": "normal", "input": {"properties": {"soldToCustomer": "#US019", "site": "#DEP1-S01", "shipToCustomerAddress": "#US020|1600", "requestedDeliveryDate": "2020-11-28", "lines": [{"item": "#CCZ_APPLE_PIE", "quantity": 10, "shipToCustomerAddress": "#US019|1500"}]}}, "output": {"errors": [{"extensions": {"code": "operation-error", "diagnoses": [{"message": "The record is not valid. You need to select a different record.", "severity": 3, "path": ["shipToCustomerAddress"]}]}, "locations": [{"column": 13, "line": 4}], "message": "The record was not created.", "path": ["xtremSales", "salesOrder", "create"]}]}}, "Create sales order - customer is US019 shipAddress on the line is on US020 ": {"executionMode": "normal", "input": {"properties": {"soldToCustomer": "#US019", "site": "#DEP1-S01", "shipToCustomerAddress": "#US019|1500", "requestedDeliveryDate": "2020-11-28", "lines": [{"item": "#CCZ_APPLE_PIE", "quantity": 10, "shipToCustomerAddress": "#US020|1600"}]}}, "output": {"errors": [{"extensions": {"code": "operation-error", "diagnoses": [{"message": "The record is not valid. You need to select a different record.", "severity": 3, "path": ["lines", "-1000000002", "shipToCustomerAddress"]}]}, "locations": [{"column": 13, "line": 4}], "message": "The record was not created.", "path": ["xtremSales", "salesOrder", "create"]}]}}, "Create a SalesOrder without item": {"input": {"properties": {"shippingStatus": "notShipped", "site": "#US001", "stockSite": "#US001", "billToCustomer": "#US019", "shipToCustomerAddress": "#US019|1500", "billToLinkedAddress": "500|800", "requestedDeliveryDate": "2020-11-28", "doNotShipBeforeDate": "2020-11-12", "doNotShipAfterDate": "2020-11-27", "soldToCustomer": "#US019", "lines": [{"shipToCustomerAddress": "#US019|1500", "site": "#US001", "unit": "#EACH", "quantity": 600, "unitToStockUnitConversionFactor": 1}]}}, "output": {"errors": [{"extensions": {"code": "operation-error", "diagnoses": [{"message": "SalesOrderLine.item: property is required", "path": ["lines", "", "itemDescription"], "severity": 4}]}, "locations": [{"column": 13, "line": 4}], "message": "The record was not created.", "path": ["xtremSales", "salesOrder", "create"]}]}, "envConfigs": {"today": "2020-11-26"}}, "Create a SalesOrder with wrong discount": {"input": {"properties": {"shippingStatus": "notShipped", "site": "#US001", "billToCustomer": "#US019", "shipToCustomerAddress": "#US019|1500", "billToLinkedAddress": "#US019|1500", "requestedDeliveryDate": "2020-11-28", "doNotShipBeforeDate": "2020-11-12", "doNotShipAfterDate": "2020-11-27", "soldToCustomer": "#US019", "lines": [{"shipToCustomerAddress": "#US019|1500", "item": "#17891", "site": "#US001", "unit": "#MI<PERSON><PERSON><PERSON>ER", "quantity": 600, "unitToStockUnitConversionFactor": 1, "discountCharges": [{"sign": "increase", "valueType": "percentage", "calculationBasis": "grossPrice", "calculationRule": "byLine", "basis": 2, "value": 3, "amount": 2}]}]}}, "output": {"errors": [{"extensions": {"code": "operation-error", "diagnoses": [{"message": "Incorrect calculation rule: For the 'Percentage' type, use the 'By unit' calculation rule.", "path": ["lines", "-1000000002", "discountCharges", "-1000000003", "calculationRule"], "severity": 3}]}, "locations": [{"column": 13, "line": 4}], "message": "The record was not created.", "path": ["xtremSales", "salesOrder", "create"]}]}, "envConfigs": {"today": "2020-11-26"}}, "Create a SalesOrder legal company not the same": {"input": {"properties": {"shippingStatus": "notShipped", "site": "#US001", "stockSite": "#US004", "billToCustomer": "#US019", "shipToCustomerAddress": "#US019|1500", "billToLinkedAddress": "500|800", "requestedDeliveryDate": "2020-11-28", "doNotShipBeforeDate": "2020-11-12", "doNotShipAfterDate": "2020-11-27", "soldToCustomer": "#US019", "lines": [{"item": "#17891", "shipToCustomerAddress": "#US019|1500", "site": "#US001", "stockSite": "#US001", "unit": "#EACH", "quantity": 600, "unitToStockUnitConversionFactor": 1}]}}, "output": {"errors": [{"extensions": {"code": "operation-error", "diagnoses": [{"message": "The site needs to belong to the same legal company.", "path": ["stockSite"], "severity": 3}, {"message": "Incorrect stock site on the sales order. The sales site and the stock site must have the same company.", "path": ["stockSite"], "severity": 3}, {"message": "value must be part of set [<PERSON><PERSON><PERSON><PERSON><PERSON>,LITER]", "path": ["lines", "-1000000002", "unit"], "severity": 3}]}, "locations": [{"column": 13, "line": 4}], "message": "The record was not created.", "path": ["xtremSales", "salesOrder", "create"]}]}, "envConfigs": {"today": "2020-11-26"}, "executionMode": "normal"}, "Create a SalesOrder with wrong sales unit": {"input": {"properties": {"shippingStatus": "notShipped", "site": "#US001", "billToCustomer": "#US019", "shipToCustomerAddress": "#US019|1500", "billToLinkedAddress": "#US019|1500", "requestedDeliveryDate": "2020-11-28", "doNotShipBeforeDate": "2020-11-12", "doNotShipAfterDate": "2020-11-27", "soldToCustomer": "#US019", "lines": [{"shipToCustomerAddress": "#US019|1500", "item": "#17891", "site": "#US001", "unit": "#METER", "quantity": 600, "unitToStockUnitConversionFactor": 1}]}}, "output": {"errors": [{"extensions": {"code": "operation-error", "diagnoses": [{"message": "value must be part of set [<PERSON><PERSON><PERSON><PERSON><PERSON>,LITER]", "path": ["lines", "-1000000002", "unit"], "severity": 3}]}, "locations": [{"column": 13, "line": 4}], "message": "The record was not created.", "path": ["xtremSales", "salesOrder", "create"]}]}, "envConfigs": {"today": "2020-11-26"}}, "Create a SalesOrder with wrong stock unit": {"input": {"properties": {"shippingStatus": "notShipped", "site": "#US001", "billToCustomer": "#US019", "shipToCustomerAddress": "#US019|1500", "billToLinkedAddress": "#US019|1500", "requestedDeliveryDate": "2020-11-28", "doNotShipBeforeDate": "2020-11-12", "doNotShipAfterDate": "2020-11-27", "soldToCustomer": "#US019", "lines": [{"shipToCustomerAddress": "#US019|1500", "item": "#17891", "site": "#US001", "stockUnit": "#METER", "quantity": 600, "unitToStockUnitConversionFactor": 1}]}}, "output": {"errors": [{"extensions": {"code": "operation-error", "diagnoses": [{"message": "value must be equal to 'LITER'", "path": ["lines", "-1000000002", "stockUnit"], "severity": 3}]}, "locations": [{"column": 13, "line": 4}], "message": "The record was not created.", "path": ["xtremSales", "salesOrder", "create"]}]}, "envConfigs": {"today": "2020-11-26"}}, "Create a SalesOrder with order date in future": {"input": {"properties": {"shippingStatus": "notShipped", "site": "#US001", "stockSite": "#US001", "billToCustomer": "#US019", "shipToCustomerAddress": "#US020|1600", "billToLinkedAddress": "500|800", "date": "2020-12-01", "requestedDeliveryDate": "2020-11-28", "doNotShipBeforeDate": "2020-11-12", "doNotShipAfterDate": "2020-11-27", "soldToCustomer": "#US020", "lines": [{"shipToCustomerAddress": "#US020|1600", "item": "#17891", "site": "#US001", "quantity": 600, "unitToStockUnitConversionFactor": 1}]}}, "output": {"errors": [{"extensions": {"code": "operation-error", "diagnoses": [{"message": "The order date cannot be later than today.", "path": ["date"], "severity": 3}]}, "locations": [{"column": 13, "line": 4}], "message": "The record was not created.", "path": ["xtremSales", "salesOrder", "create"]}]}, "envConfigs": {"today": "2020-11-26"}}, "Create a SalesOrder with no currency conversion": {"input": {"properties": {"shippingStatus": "notShipped", "site": "#US001", "shipToCustomer": "#US019", "soldToCustomer": "#US019", "incoterm": "#EXW", "shipToCustomerAddress": "#US019|1500", "billToLinkedAddress": "#US019|1500", "deliveryLeadTime": 1, "currency": "#PLN", "doNotShipAfterDate": "2020-11-30", "doNotShipBeforeDate": "2020-11-28", "requestedDeliveryDate": "2020-11-28", "date": "2020-11-26", "paymentTerm": "#DUE_UPON_RECEIPT_ALL", "lines": [{"shipToCustomerAddress": "#US019|1500", "item": "#17891", "unit": "#LITER", "quantity": 600, "grossPrice": 2, "discountCharges": [{"sign": "increase", "valueType": "percentage", "calculationBasis": "grossPrice", "basis": 2, "value": 3}], "discount": 3}]}}, "output": {"errors": [{"message": "The record was not created.", "locations": [{"line": 4, "column": 13}], "path": ["xtremSales", "salesOrder", "create"], "extensions": {"code": "operation-error", "diagnoses": [{"severity": 3, "path": ["companyFxRate"], "message": "No exchange rate found."}]}}]}}, "Create sales order - externalNote provided without isExternalNote": {"input": {"properties": {"shippingStatus": "notShipped", "site": "#US001", "shipToCustomer": "#US019", "soldToCustomer": "#US019", "incoterm": "#EXW", "shipToCustomerAddress": "#US019|1500", "billToLinkedAddress": "#US019|1500", "deliveryLeadTime": 1, "doNotShipAfterDate": "2020-11-30", "doNotShipBeforeDate": "2020-11-28", "requestedDeliveryDate": "2020-11-28", "date": "2020-11-26", "paymentTerm": "#DUE_UPON_RECEIPT_ALL", "externalNote": {"value": " must throw "}, "lines": [{"shipToCustomerAddress": "#US019|1500", "item": "#17891", "unit": "#LITER", "quantity": 600, "grossPrice": 2, "discountCharges": [{"sign": "increase", "valueType": "percentage", "calculationBasis": "grossPrice", "basis": 2, "value": 3}], "discount": 3, "externalNote": {"value": " must throw "}}]}}, "output": {"errors": [{"message": "The record was not created.", "locations": [{"line": 4, "column": 13}], "path": ["xtremSales", "salesOrder", "create"], "extensions": {"code": "operation-error", "diagnoses": [{"severity": 3, "path": ["externalNote"], "message": "The external note must be empty if the 'isExternalNote' property is false."}, {"severity": 3, "path": ["lines", "-1000000002", "externalNote"], "message": "The external note must be empty if the 'isExternalNote' property is false."}]}}]}}}