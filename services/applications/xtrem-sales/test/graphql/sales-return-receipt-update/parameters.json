{"Update sales return receipt update quantity in sales unit OK": {"input": {"properties": {"_id": "#SRREC08", "lines": [{"_sortValue": 10, "_action": "update", "orderCost": "15", "stockDetailStatus": "required"}]}}, "output": {"xtremSales": {"salesReturnReceipt": {"update": {"number": "SRREC08", "lines": {"query": {"edges": [{"node": {"stockDetailStatus": "notRequired", "orderCost": "15", "itemDescription": "SalesItem81"}}]}}}}}}}, "Update sales return receipt update item description OK": {"input": {"properties": {"_id": "#SRREC08", "lines": [{"_sortValue": 10, "_action": "update", "itemDescription": "SalesItem81 (updated description)", "stockDetailStatus": "entered"}]}}, "output": {"xtremSales": {"salesReturnReceipt": {"update": {"number": "SRREC08", "lines": {"query": {"edges": [{"node": {"stockDetailStatus": "notRequired", "orderCost": "0", "itemDescription": "SalesItem81 (updated description)"}}]}}}}}}}}