mutation {
    xtremSales {
        salesReturnReceipt {
            update(data: {{inputParameters}}) {
                number
                lines {
                    query {
                        edges {
                            node {
                                stockDetailStatus
                                orderCost
                                itemDescription
                            }
                        }
                    }
                }
            }
        }
    }
}
