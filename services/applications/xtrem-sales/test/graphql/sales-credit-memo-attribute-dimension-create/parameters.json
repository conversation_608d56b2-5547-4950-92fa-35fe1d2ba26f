{"Create sales credit memo - attributes project": {"variables": {"storedAttributes": "{\"project\":\"AttPROJ\"}"}, "output": {"create": {"number": "SCE1210008", "lines": {"query": {"edges": [{"node": {"computedAttributes": "{\"financialSite\":\"US001\",\"stockSite\":\"US001\",\"item\":\"SalesItem81\",\"businessSite\":\"US001\",\"customer\":\"US019\"}", "storedAttributes": "{\"project\":\"AttPROJ\"}", "storedDimensions": null}}]}}}}, "envConfigs": {"today": "2021-06-28"}}, "Create sales credit memo - attributes and dimensions": {"variables": {"storedAttributes": "{\"project\":\"AttPROJ\"}", "storedDimensions": "{\"dimensionType03\":\"DIMTYPE1VALUE1\",\"dimensionType04\":\"DIMTYPE2VALUE2\"}"}, "output": {"create": {"number": "SCE1210008", "lines": {"query": {"edges": [{"node": {"computedAttributes": "{\"financialSite\":\"US001\",\"stockSite\":\"US001\",\"item\":\"SalesItem81\",\"businessSite\":\"US001\",\"customer\":\"US019\"}", "storedAttributes": "{\"project\":\"AttPROJ\"}", "storedDimensions": "{\"dimensionType03\":\"DIMTYPE1VALUE1\",\"dimensionType04\":\"DIMTYPE2VALUE2\"}"}}]}}}}, "envConfigs": {"today": "2021-06-28"}}}