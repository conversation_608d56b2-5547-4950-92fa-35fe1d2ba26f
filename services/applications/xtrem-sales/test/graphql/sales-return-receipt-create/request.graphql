mutation {
    xtremSales {
        salesReturnReceipt {
            create(data: { site: "#US001", shipToCustomer: "#US019", lines: [{ item: "#LOT_TEST1", quantity: 600 }] }) {
                stockSite {
                    name
                }
                shipToCustomer {
                    businessEntity {
                        name
                    }
                }

                shipToCustomerAddress {
                    name
                }
                lines {
                    query {
                        edges {
                            node {
                                origin
                                item {
                                    id
                                    name
                                }
                                itemDescription
                                quantity
                                quantityInStockUnit
                                unit {
                                    name
                                }
                                stockUnit {
                                    name
                                }
                                valuedCost
                                orderCost
                                location {
                                    _id
                                }
                                stockStatus {
                                    _id
                                }
                                lot {
                                    _id
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}
