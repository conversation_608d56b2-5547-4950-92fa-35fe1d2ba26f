mutation {
    xtremSales {
        salesReturnReceipt {
            create(
                data: {
                    lines: [
                        {
                            item: "#Chemical D"
                            itemDescription: "Chemical contains nop"
                            origin: "return"
                            quantity: 14
                            toReturnRequestLines: [
                                { linkedDocument: "#SRR17|10", quantity: 14, quantityInStockUnit: 14 }
                            ]
                            unit: "#OUNCE"
                            unitToStockUnitConversionFactor: 1
                            stockDetails: [
                                {
                                    site: "#US001"
                                    item: "#Chemical D"
                                    location: 1
                                    existingLot: 30
                                    status: "#A"
                                    stockUnit: "#GRAM"
                                    quantityInStockUnit: 14
                                }
                            ]
                            stockUnit: "#GRAM"
                            storedAttributes: null
                            storedDimensions: null
                        }
                        {
                            item: "#Chemical D"
                            itemDescription: "Chemical contains nop"
                            origin: "return"
                            quantity: 14
                            toReturnRequestLines: [
                                { linkedDocument: "#SRR17|20", quantity: 14, quantityInStockUnit: 14 }
                            ]
                            unit: "#OUNCE"
                            unitToStockUnitConversionFactor: 1
                            stockDetails: [
                                {
                                    site: "#US001"
                                    item: "#Chemical D"
                                    location: 1
                                    existingLot: 30
                                    status: "#A"
                                    stockUnit: "#GRAM"
                                    quantityInStockUnit: 14
                                }
                            ]
                            stockUnit: "#GRAM"
                            storedAttributes: null
                            storedDimensions: null
                        }
                    ]
                    shipToAddress: {
                        country: "#US"
                        name: "Cust address"
                        locationPhoneNumber: "+15550123456"
                        addressLine1: "1st Avenue"
                        addressLine2: null
                        city: "One"
                        region: "Some region"
                        postcode: "0001"
                    }
                    shipToCustomerAddress: "#US019|1500"
                    shipToCustomer: "#US019"
                    site: "#US001"
                    number: null
                    date: "2021-09-21"
                }
            ) {
                site {
                    name
                }
                shipToCustomer {
                    businessEntity {
                        name
                    }
                }
                shipToCustomerAddress {
                    name
                }
                lines {
                    query {
                        edges {
                            node {
                                origin
                                stockDetailStatus
                                isReceiptExpected
                                item {
                                    id
                                    name
                                }
                                itemDescription
                                quantity
                                quantityInStockUnit
                                unit {
                                    name
                                }
                                stockUnit {
                                    name
                                }
                                valuedCost
                                orderCost
                                location {
                                    _id
                                }
                                stockStatus {
                                    id
                                }
                                lot {
                                    _id
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}
