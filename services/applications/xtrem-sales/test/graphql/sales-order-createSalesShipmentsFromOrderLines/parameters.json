{"Create sales shipment using mutation": {"input": {"arrayProperties": {"salesDocumentLines": [{"salesDocumentLine": "#SO5|60400", "quantityToProcess": 12}, {"salesDocumentLine": "#SO5|60500", "quantityToProcess": 10}, {"salesDocumentLine": "#SO18|62300", "quantityToProcess": 10}]}}, "output": {"createSalesShipmentsFromOrderLines": {"status": "salesOrderIsShipped", "numberOfShipments": 1, "lineErrors": []}}, "envConfigs": {"today": "2021-01-01"}}, "Create sales shipment using mutation empty array": {"input": {"arrayProperties": {"salesDocumentLines": []}}, "output": {"createSalesShipmentsFromOrderLines": {"status": "parametersAreIncorrect", "numberOfShipments": 0, "lineErrors": []}}, "envConfigs": {"today": "2020-11-27"}}, "Create sales shipment using mutation with processAllShippableLines processOption": {"input": {"properties": {"processOptions": {"processAllShippableLines": true}}, "arrayProperties": {"salesDocumentLines": [{"salesDocumentLine": "#SO5|60400", "quantityToProcess": 12}, {"salesDocumentLine": "#SO5|60500", "quantityToProcess": 10}]}}, "output": {"createSalesShipmentsFromOrderLines": {"status": "salesOrderIsShipped", "numberOfShipments": 1, "lineErrors": []}}, "envConfigs": {"today": "2020-11-27"}}}