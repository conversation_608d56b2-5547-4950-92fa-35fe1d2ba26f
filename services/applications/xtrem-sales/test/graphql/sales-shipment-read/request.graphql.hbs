{
  xtremSales {
    salesShipment {
      read(_id: "{{_id}}") {
        _id
        number
        effectiveDate
        reference
        status
        displayStatus
        isPrinted
        shippingDate
        trackingNumber
        deliveryLeadTime
        deliveryDate
        deliveryMode {
          name
        }
        paymentTerm {
          isActive
          name
          businessEntityType
          description
        }
        shipToCustomerAddress {
          address {
            name
          }
        }
        billToLinkedAddress {
          name
        }
        shipToCustomer {
          businessEntity {
            name
          }
        }
        billToCustomer {
          businessEntity {
            name
          }
        }
        currency {
          name
          symbol
        }
        billToAddress {
          name
          addressLine1
          addressLine2
          city
          region
          postcode
          locationPhoneNumber
        }
        shipToAddress {
          name
          addressLine1
          addressLine2
          city
          region
          postcode
          locationPhoneNumber
        }
        billToContact {
          title
          firstName
          lastName
          preferredName
          role
          position
          locationPhoneNumber
          email
        }
        shipToContact {
          title
          firstName
          lastName
          preferredName
          role
          position
          locationPhoneNumber
          email
        }
        lines {
          query {
            edges {
              node {
                status
                itemDescription
                quantityAllocated
                unitToStockUnitConversionFactor
                quantity
                quantityInStockUnit
                item {
                  id
                }
                remainingQuantity
                allocationStatus
                quantityInvoicedInProgressInSalesUnit
                quantityInvoicedPostedInSalesUnit
                quantityRequestedInSalesUnit
                quantityReceiptInSalesUnit
                unit {
                  name
                }
                stockUnit {
                  name
                }
                salesOrderLines {
                  query {
                    edges {
                      node {
                        _sortValue
                      }
                    }
                  }
                }
                stockAllocations {
                  query {
                    edges {
                      node {
                        _sourceId
                        _id
                        quantityInStockUnit
                        stockRecord {
                          _id
                        }
                        serialNumbers {
                          query {
                            edges {
                              node {
                                _id
                              }
                            }
                          }
                        }
                      }
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
  }
}
