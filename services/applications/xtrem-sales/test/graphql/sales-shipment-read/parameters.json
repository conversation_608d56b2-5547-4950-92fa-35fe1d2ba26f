{"Sales Shipment read - SSH4 ": {"executionMode": "normal", "input": {"_id": "#SSH4"}, "output": {"_id": "4", "number": "SSH4", "effectiveDate": "2020-11-20", "reference": "", "status": "shipped", "displayStatus": "shipped", "isPrinted": true, "shippingDate": "2020-11-20", "trackingNumber": "", "deliveryLeadTime": 0, "deliveryDate": "2021-03-01", "deliveryMode": {"name": "Sea"}, "paymentTerm": {"isActive": true, "name": "Net 45", "businessEntityType": "all", "description": ""}, "shipToCustomerAddress": {"address": {"name": "Cust address"}}, "billToLinkedAddress": {"name": "Cust address"}, "shipToCustomer": {"businessEntity": {"name": "Siège social S02 PARIS"}}, "billToCustomer": {"businessEntity": {"name": "Siège social S02 PARIS"}}, "currency": {"name": "South African Rand", "symbol": "R"}, "billToAddress": {"name": "Second Customer add", "addressLine1": "Add line 1", "addressLine2": "", "city": "", "region": "HDF", "postcode": "59650", "locationPhoneNumber": "0695125874"}, "shipToAddress": {"name": "Second Customer add", "addressLine1": "Add line 1", "addressLine2": "", "city": "", "region": "HDF", "postcode": "59650", "locationPhoneNumber": "0695125874"}, "billToContact": {"title": "ms", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON>", "preferredName": "<PERSON>", "role": "mainContact", "position": "CEO", "locationPhoneNumber": "+***********", "email": "<EMAIL>"}, "shipToContact": {"title": "ms", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON>", "preferredName": "<PERSON>", "role": "mainContact", "position": "CEO", "locationPhoneNumber": "+***********", "email": "<EMAIL>"}, "lines": {"query": {"edges": [{"node": {"status": "shipped", "itemDescription": "Chemical contains nop", "quantityAllocated": "0", "unitToStockUnitConversionFactor": "28.3495", "quantity": "10", "quantityInStockUnit": "283.5", "item": {"id": "Chemical D"}, "remainingQuantity": "283.5", "allocationStatus": "notAllocated", "quantityInvoicedInProgressInSalesUnit": "0", "quantityInvoicedPostedInSalesUnit": "0", "quantityRequestedInSalesUnit": "0", "quantityReceiptInSalesUnit": "0", "unit": {"name": "<PERSON><PERSON><PERSON>"}, "stockUnit": {"name": "Gram"}, "salesOrderLines": {"query": {"edges": [{"node": {"_sortValue": 10}}]}}, "stockAllocations": {"query": {"edges": []}}}}]}}}}, "Sales Shipment read - SSH5 ": {"executionMode": "normal", "input": {"_id": "#SSH5"}, "output": {"_id": "5", "number": "SSH5", "effectiveDate": "2020-11-20", "reference": "", "status": "readyToProcess", "displayStatus": "partiallyInvoiced", "isPrinted": false, "shippingDate": "2020-11-20", "trackingNumber": "", "deliveryLeadTime": 0, "deliveryDate": "2021-03-01", "deliveryMode": {"name": "Sea"}, "paymentTerm": {"isActive": true, "name": "Net 45", "businessEntityType": "all", "description": ""}, "shipToCustomerAddress": {"address": {"name": "Cust address"}}, "billToLinkedAddress": {"name": "Cust address"}, "shipToCustomer": {"businessEntity": {"name": "Siège social S02 PARIS"}}, "billToCustomer": {"businessEntity": {"name": "Siège social S02 PARIS"}}, "currency": {"name": "South African Rand", "symbol": "R"}, "billToAddress": {"name": "Second Customer add", "addressLine1": "Add line 1", "addressLine2": "", "city": "", "region": "HDF", "postcode": "59650", "locationPhoneNumber": "0695125874"}, "shipToAddress": {"name": "Second Customer add", "addressLine1": "Add line 1", "addressLine2": "", "city": "", "region": "HDF", "postcode": "59650", "locationPhoneNumber": "0695125874"}, "billToContact": {"title": "ms", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON>", "preferredName": "<PERSON>", "role": "mainContact", "position": "CEO", "locationPhoneNumber": "+***********", "email": "<EMAIL>"}, "shipToContact": {"title": "ms", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON>", "preferredName": "<PERSON>", "role": "mainContact", "position": "CEO", "locationPhoneNumber": "+***********", "email": "<EMAIL>"}, "lines": {"query": {"edges": [{"node": {"status": "readyToProcess", "itemDescription": "Chemical contains nop", "quantityAllocated": "0", "unitToStockUnitConversionFactor": "28.3495", "quantity": "30", "quantityInStockUnit": "850.49", "item": {"id": "Chemical D"}, "remainingQuantity": "850.49", "allocationStatus": "notAllocated", "quantityInvoicedInProgressInSalesUnit": "10", "quantityInvoicedPostedInSalesUnit": "0", "quantityReceiptInSalesUnit": "0", "quantityRequestedInSalesUnit": "0", "unit": {"name": "<PERSON><PERSON><PERSON>"}, "stockUnit": {"name": "Gram"}, "salesOrderLines": {"query": {"edges": [{"node": {"_sortValue": 10}}]}}, "stockAllocations": {"query": {"edges": []}}}}]}}}}, "Sales Shipment read - SSH2 ": {"executionMode": "normal", "input": {"_id": "#SSH2"}, "output": {"_id": "2", "number": "SSH2", "effectiveDate": "2020-11-20", "reference": "", "status": "readyToProcess", "displayStatus": "readyToProcess", "isPrinted": false, "shippingDate": "2020-11-20", "trackingNumber": "", "deliveryLeadTime": 0, "deliveryDate": "2021-03-01", "deliveryMode": {"name": "Sea"}, "paymentTerm": {"isActive": true, "name": "Net 45", "businessEntityType": "all", "description": ""}, "shipToCustomerAddress": {"address": {"name": "Cust address"}}, "billToLinkedAddress": {"name": "Cust address"}, "shipToCustomer": {"businessEntity": {"name": "Siège social S02 PARIS"}}, "billToCustomer": {"businessEntity": {"name": "Siège social S02 PARIS"}}, "currency": {"name": "South African Rand", "symbol": "R"}, "billToAddress": {"name": "Second Customer add", "addressLine1": "Add line 1", "addressLine2": "", "city": "", "region": "HDF", "postcode": "59650", "locationPhoneNumber": "0695125874"}, "shipToAddress": {"name": "Second Customer add", "addressLine1": "Add line 1", "addressLine2": "", "city": "", "region": "HDF", "postcode": "59650", "locationPhoneNumber": "0695125874"}, "billToContact": {"title": "ms", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON>", "preferredName": "<PERSON>", "role": "mainContact", "position": "CEO", "locationPhoneNumber": "+***********", "email": "<EMAIL>"}, "shipToContact": {"title": "ms", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON>", "preferredName": "<PERSON>", "role": "mainContact", "position": "CEO", "locationPhoneNumber": "+***********", "email": "<EMAIL>"}, "lines": {"query": {"edges": [{"node": {"status": "readyToProcess", "itemDescription": "Chemical contains nop", "quantityAllocated": "0", "unitToStockUnitConversionFactor": "28.3495", "quantity": "10", "quantityInStockUnit": "283.5", "item": {"id": "Chemical D"}, "remainingQuantity": "283.5", "allocationStatus": "notAllocated", "quantityInvoicedInProgressInSalesUnit": "0", "quantityInvoicedPostedInSalesUnit": "0", "quantityReceiptInSalesUnit": "0", "quantityRequestedInSalesUnit": "0", "unit": {"name": "<PERSON><PERSON><PERSON>"}, "stockUnit": {"name": "Gram"}, "salesOrderLines": {"query": {"edges": [{"node": {"_sortValue": 10}}]}}, "stockAllocations": {"query": {"edges": []}}}}, {"node": {"status": "readyToProcess", "itemDescription": "Chemical contains nop", "quantityAllocated": "0", "unitToStockUnitConversionFactor": "28.3495", "quantity": "10", "quantityInStockUnit": "283.5", "item": {"id": "Chemical D"}, "remainingQuantity": "283.5", "allocationStatus": "notAllocated", "quantityInvoicedInProgressInSalesUnit": "0", "quantityInvoicedPostedInSalesUnit": "10", "quantityReceiptInSalesUnit": "0", "quantityRequestedInSalesUnit": "0", "unit": {"name": "<PERSON><PERSON><PERSON>"}, "stockUnit": {"name": "Gram"}, "salesOrderLines": {"query": {"edges": [{"node": {"_sortValue": 10}}]}}, "stockAllocations": {"query": {"edges": []}}}}, {"node": {"status": "readyToProcess", "itemDescription": "Chemical contains nop", "quantityAllocated": "0", "unitToStockUnitConversionFactor": "28.3495", "quantity": "30", "quantityInStockUnit": "850.49", "item": {"id": "Chemical D"}, "remainingQuantity": "850.49", "allocationStatus": "notAllocated", "quantityInvoicedInProgressInSalesUnit": "0", "quantityInvoicedPostedInSalesUnit": "0", "quantityReceiptInSalesUnit": "0", "quantityRequestedInSalesUnit": "0", "unit": {"name": "<PERSON><PERSON><PERSON>"}, "stockUnit": {"name": "Gram"}, "salesOrderLines": {"query": {"edges": [{"node": {"_sortValue": 10}}]}}, "stockAllocations": {"query": {"edges": []}}}}]}}}}}