mutation salesOrder($storedAttributes: <PERSON><PERSON>, $storedDimensions: Json) {
  xtremSales {
    salesOrder {
      create(
        data: {
          shippingStatus: "notShipped"
          site: "#US001"
          shipToCustomer: "#US019"
          soldToCustomer: "#US019"
          shipToCustomerAddress: "#US019|1500"
          billToLinkedAddress: "#US019|1500"
          deliveryLeadTime: 1
          currency: "#ZAR"
          requestedDeliveryDate: "2021-05-03"
          date: "2021-04-22"
          paymentTerm: "#DUE_UPON_RECEIPT_ALL"
          lines: [
            {
              shipToCustomerAddress: "#US019|1500"
              item: "#17891"
              unit: "#LITER"
              quantity: 600
              storedAttributes: $storedAttributes
              storedDimensions: $storedDimensions
            }
          ]
        }
      ) {
        number
        lines {
          query {
            edges {
              node {
                computedAttributes
                storedAttributes
                storedDimensions
              }
            }
          }
        }
      }
    }
  }
}
