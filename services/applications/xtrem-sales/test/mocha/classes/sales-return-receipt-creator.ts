import { Test } from '@sage/xtrem-core';
import { assert } from 'chai';
import * as xtremSales from '../../../index';

describe('SalesReturnReceiptCreator', () => {
    before(() => {});
    it('create sales return receipt from sales return request', () =>
        Test.withContext(
            async context => {
                const salesShipment = await context.read(
                    xtremSales.nodes.SalesShipment,
                    { _id: '#SSH4' },
                    { forUpdate: true },
                );

                const line1 = await salesShipment.lines.elementAt(0);
                await line1.$.set({ status: 'shipped' });
                await salesShipment.$.save();

                const salesReturnRequestsCreated = await (
                    await new xtremSales.classes.SalesReturnRequestCreator(context).prepareNodeCreateData([
                        { salesDocumentLine: line1, quantityToProcess: 10 },
                    ])
                ).createSalesOutputDocuments();

                const salesInvoicesCreated = await (
                    await new xtremSales.classes.SalesInvoicesCreator(context).prepareNodeCreateData([
                        { salesDocumentLine: line1, quantityToProcess: 10 },
                    ])
                ).createSalesOutputDocuments();

                assert.isArray(salesReturnRequestsCreated.documentsCreated);
                assert.isArray(salesInvoicesCreated.documentsCreated);

                if (!salesReturnRequestsCreated.documentsCreated) {
                    assert.fail('Failed to create sales return requests from the provided sales documents.');
                }
                if (!salesInvoicesCreated.documentsCreated) {
                    assert.fail('Failed to create sales invoices from the provided sales documents.');
                }
                const salesReturnRequest = await context.read(
                    xtremSales.nodes.SalesReturnRequest,
                    { _id: salesReturnRequestsCreated.documentsCreated[0]._id },
                    { forUpdate: true },
                );
                await salesReturnRequest.$.set({
                    approvalStatus: 'approved',
                    lines: [
                        {
                            _sortValue: await (await salesReturnRequest.lines.elementAt(0))._sortValue,
                            isReceiptExpected: true,
                            _action: 'update',
                        },
                    ],
                });
                await salesReturnRequest.$.save();

                const salesInvoice = await context.read(
                    xtremSales.nodes.SalesInvoice,
                    { _id: salesInvoicesCreated.documentsCreated[0]._id },
                    { forUpdate: true },
                );

                await salesInvoice.$.set({ taxCalculationStatus: 'done' });
                await salesInvoice.$.save();
                await xtremSales.nodes.SalesInvoice.post(context, salesInvoice);

                assert.equal(await salesReturnRequest.creditStatus, 'notCredited');
                assert.equal(await salesInvoice.creditStatus, 'notCredited');

                // enforce the 'posted' status of the invoice (after post it is 'inProgress'). Normally the 'posted'
                // status is set asynchronously on the reply of the finance integration
                await salesInvoice.$.set({ status: 'posted', forceUpdateForFinance: true });
                await salesInvoice.$.save();

                const salesReturnReceiptsCreated = await (
                    await new xtremSales.classes.SalesReturnReceiptCreator(context).prepareNodeCreateData([
                        {
                            salesDocumentLine: await salesReturnRequest.lines.elementAt(0),
                            quantityToProcess: 10,
                        },
                    ])
                ).createSalesOutputDocuments();

                assert.isNotNull(salesReturnReceiptsCreated.documentsCreated);
                assert.isNotEmpty(salesReturnReceiptsCreated.documentsCreated);

                if (salesReturnReceiptsCreated.documentsCreated) {
                    const salesReturnReceipt = await context.read(xtremSales.nodes.SalesReturnReceipt, {
                        _id: salesReturnReceiptsCreated.documentsCreated[0]._id,
                    });
                    const salesReturnReceiptLine = await context.read(xtremSales.nodes.SalesReturnReceiptLine, {
                        _id: (await salesReturnReceipt.lines.elementAt(0))._id,
                    });

                    assert.equal(await salesReturnReceipt.status, 'draft');
                    assert.equal(await salesReturnReceiptLine.itemDescription, 'Chemical contains nop');
                }
            },
            { user: { email: '<EMAIL>' } },
        ));
});
