import { date, Test } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremStockData from '@sage/xtrem-stock-data';
import * as xtremSystem from '@sage/xtrem-system';
import { assert } from 'chai';
import * as xtremSales from '../../../index';

describe('SalesCreditMemosCreator', () => {
    before(() => {});
    it('create sales credit memo from sales return request', () =>
        Test.withContext(
            async context => {
                const salesShipment = await context.read(
                    xtremSales.nodes.SalesShipment,
                    { _id: '#SSH4' },
                    { forUpdate: true },
                );

                const line1 = await salesShipment.lines.elementAt(0);

                await line1.$.set({ status: 'shipped' });

                await salesShipment.$.save();

                const salesReturnRequestsCreated = await (
                    await new xtremSales.classes.SalesReturnRequestCreator(context).prepareNodeCreateData([
                        { salesDocumentLine: line1, quantityToProcess: 10 },
                    ])
                ).createSalesOutputDocuments();

                const salesInvoicesCreated = await (
                    await new xtremSales.classes.SalesInvoicesCreator(context).prepareNodeCreateData([
                        { salesDocumentLine: line1, quantityToProcess: 10 },
                    ])
                ).createSalesOutputDocuments();

                assert.isArray(salesReturnRequestsCreated.documentsCreated);
                assert.isArray(salesInvoicesCreated.documentsCreated);

                if (!salesReturnRequestsCreated.documentsCreated) {
                    assert.fail('Failed to create sales return requests from the provided sales documents.');
                }
                if (!salesInvoicesCreated.documentsCreated) {
                    assert.fail('Failed to create sales invoices from the provided sales documents.');
                }
                let salesReturnRequest = await context.read(
                    xtremSales.nodes.SalesReturnRequest,
                    { _id: salesReturnRequestsCreated.documentsCreated[0]._id },
                    { forUpdate: true },
                );

                await salesReturnRequest.$.set({
                    approvalStatus: 'approved',
                    lines: [
                        {
                            _id: (await salesReturnRequest.lines.elementAt(0))._id,
                            isReceiptExpected: false,
                            _action: 'update',
                        },
                    ],
                });

                await salesReturnRequest.$.save();

                let salesInvoice: xtremSales.nodes.SalesInvoice = await context.read(
                    xtremSales.nodes.SalesInvoice,
                    { _id: salesInvoicesCreated.documentsCreated[0]._id },
                    { forUpdate: true },
                );

                await salesInvoice.$.set({
                    taxCalculationStatus: 'done',
                });

                await salesInvoice.$.save();

                await xtremSales.nodes.SalesInvoice.post(context, salesInvoice);

                assert.equal(await salesReturnRequest.creditStatus, 'notCredited');
                assert.equal(await salesInvoice.creditStatus, 'notCredited');

                // enforce the 'posted' status of the invoice (after post it is 'inProgress'). Normally the 'posted'
                // status is set asynchronously on the reply of the finance integration
                salesInvoice = await context.read(
                    xtremSales.nodes.SalesInvoice,
                    { _id: salesInvoice._id },
                    { forUpdate: true },
                );
                await salesInvoice.$.set({ status: 'posted' });
                await salesInvoice.$.set({ forceUpdateForFinance: true });
                await salesInvoice.$.save();

                await xtremSales.nodes.SalesReturnRequest.createSalesCreditMemosFromReturnRequestLines(context, [
                    { salesDocumentLine: await salesReturnRequest.lines.elementAt(0) },
                ]);

                salesReturnRequest = await context.read(xtremSales.nodes.SalesReturnRequest, {
                    _id: salesReturnRequest._id,
                });

                salesInvoice = await context.read(xtremSales.nodes.SalesInvoice, { _id: salesInvoice._id });

                assert.equal(await salesReturnRequest.creditStatus, 'credited');
                assert.equal(await salesInvoice.creditStatus, 'credited');

                await assert.isRejected(
                    xtremSales.nodes.SalesReturnRequest.createSalesCreditMemosFromReturnRequestLines(context, [
                        {
                            salesDocumentLine: await salesReturnRequest.lines.elementAt(0),
                            quantityToProcess: 10,
                        },
                    ]),
                    'A return request line is already credited.',
                );
            },
            { user: { email: '<EMAIL>' } },
        ));

    it('create sales credit memo return request errors', () =>
        Test.withContext(
            async context => {
                const salesShipment = await context.read(
                    xtremSales.nodes.SalesShipment,
                    { _id: '#SSH4' },
                    { forUpdate: true },
                );

                const line1 = await salesShipment.lines.elementAt(0);

                const salesReturnRequestsCreated = await (
                    await new xtremSales.classes.SalesReturnRequestCreator(context).prepareNodeCreateData([
                        { salesDocumentLine: line1, quantityToProcess: 10 },
                    ])
                ).createSalesOutputDocuments();

                assert.isArray(salesReturnRequestsCreated.documentsCreated);

                if (!salesReturnRequestsCreated.documentsCreated) {
                    assert.fail('Failed to create sales return requests from the provided sales documents.');
                }
                let salesReturnRequest = await context.read(
                    xtremSales.nodes.SalesReturnRequest,
                    { _id: salesReturnRequestsCreated.documentsCreated[0]._id },
                    { forUpdate: true },
                );

                await assert.isRejected(
                    xtremSales.nodes.SalesReturnRequest.createSalesCreditMemosFromReturnRequestLines(context, [
                        {
                            salesDocumentLine: await salesReturnRequest.lines.elementAt(0),
                            quantityToProcess: 10,
                        },
                    ]),
                    'The related sales shipment line is not invoiced.',
                );

                await (
                    await new xtremSales.classes.SalesInvoicesCreator(context).prepareNodeCreateData([
                        { salesDocumentLine: line1, quantityToProcess: 10 },
                    ])
                ).createSalesOutputDocuments();

                const salesReturnRequestId = salesReturnRequest._id;
                // rollbackCache function will flush deferred actions and saves
                await Test.rollbackCache(context);

                salesReturnRequest = await context.read(xtremSales.nodes.SalesReturnRequest, {
                    _id: salesReturnRequestId,
                });

                await assert.isRejected(
                    xtremSales.nodes.SalesReturnRequest.createSalesCreditMemosFromReturnRequestLines(context, [
                        {
                            salesDocumentLine: await salesReturnRequest.lines.elementAt(0),
                            quantityToProcess: 10,
                        },
                    ]),
                    'A return request is not approved.',
                );

                // Disable approval management (enable confirmation workflow).
                const site = await context.read(xtremSystem.nodes.Site, { id: 'US001' }, { forUpdate: true });
                await site.$.set({ isSalesReturnRequestApprovalManaged: false });

                await assert.isRejected(
                    xtremSales.nodes.SalesReturnRequest.createSalesCreditMemosFromReturnRequestLines(context, [
                        {
                            salesDocumentLine: await salesReturnRequest.lines.elementAt(0),
                            quantityToProcess: 10,
                        },
                    ]),
                    'A return request is not confirmed.',
                );

                // Turn approval management back on (disable confirmation workflow).
                await site.$.set({ isSalesReturnRequestApprovalManaged: true });

                salesReturnRequest = await context.read(
                    xtremSales.nodes.SalesReturnRequest,
                    { _id: salesReturnRequest._id },
                    { forUpdate: true },
                );
                await salesReturnRequest.$.set({
                    approvalStatus: 'approved',
                    lines: [
                        {
                            _id: (await salesReturnRequest.lines.elementAt(0))._id,
                            isCreditMemoExpected: false,
                            _action: 'update',
                        },
                    ],
                });

                await salesReturnRequest.$.save();

                await assert.isRejected(
                    xtremSales.nodes.SalesReturnRequest.createSalesCreditMemosFromReturnRequestLines(context, [
                        {
                            salesDocumentLine: await salesReturnRequest.lines.elementAt(0),
                            quantityToProcess: 10,
                        },
                    ]),
                    'A sales credit memo is not expected for this return request line.',
                );

                await salesReturnRequest.$.set({
                    approvalStatus: 'approved',
                    lines: [
                        {
                            _id: (await salesReturnRequest.lines.elementAt(0))._id,
                            isCreditMemoExpected: true,
                            _action: 'update',
                        },
                    ],
                });

                await salesReturnRequest.$.save();

                await assert.isRejected(
                    xtremSales.nodes.SalesReturnRequest.createSalesCreditMemosFromReturnRequestLines(context, [
                        {
                            salesDocumentLine: await salesReturnRequest.lines.elementAt(0),
                            quantityToProcess: 10,
                        },
                    ]),
                    'A sales return receipt is expected but not yet received.',
                );

                await salesReturnRequest.$.set({
                    approvalStatus: 'approved',
                    lines: [
                        {
                            _id: (await salesReturnRequest.lines.elementAt(0))._id,
                            isReceiptExpected: false,
                            _action: 'update',
                        },
                    ],
                });

                await salesReturnRequest.$.save();
            },
            { user: { email: '<EMAIL>' } },
        ));

    it('create sales credit memo from sales return request errors 2', () =>
        Test.withContext(
            async context => {
                const salesShipment = await context.read(
                    xtremSales.nodes.SalesShipment,
                    { _id: '#SSH4' },
                    { forUpdate: true },
                );

                const line1 = await salesShipment.lines.elementAt(0);

                await line1.$.set({ status: 'shipped' });

                await line1.$.set({ quantity: 30 });

                await salesShipment.$.save();

                const salesReturnRequestsCreated = await (
                    await new xtremSales.classes.SalesReturnRequestCreator(context).prepareNodeCreateData([
                        { salesDocumentLine: line1 },
                    ])
                ).createSalesOutputDocuments();

                const salesInvoicesCreated = await (
                    await new xtremSales.classes.SalesInvoicesCreator(context).prepareNodeCreateData([
                        { salesDocumentLine: line1 },
                    ])
                ).createSalesOutputDocuments();

                assert.isArray(salesReturnRequestsCreated.documentsCreated);
                assert.isArray(salesInvoicesCreated.documentsCreated);

                if (!salesReturnRequestsCreated.documentsCreated) {
                    assert.fail('Failed to create sales return requests from the provided sales documents.');
                }
                if (!salesInvoicesCreated.documentsCreated) {
                    assert.fail('Failed to create sales invoices from the provided sales documents.');
                }
                let salesReturnRequest = await context.read(
                    xtremSales.nodes.SalesReturnRequest,
                    { _id: salesReturnRequestsCreated.documentsCreated[0]._id },
                    { forUpdate: true },
                );
                await salesReturnRequest.$.set({ approvalStatus: 'approved' });

                await salesReturnRequest.$.save();

                let salesInvoice = await context.read(
                    xtremSales.nodes.SalesInvoice,
                    { _id: salesInvoicesCreated.documentsCreated[0]._id },
                    { forUpdate: true },
                );

                await salesInvoice.$.set({ taxCalculationStatus: 'done' });

                await salesInvoice.$.save();

                await xtremSales.nodes.SalesInvoice.post(context, salesInvoice);

                const site = await context.read(xtremSystem.nodes.Site, { id: 'US001' });
                const item = await context.read(xtremMasterData.nodes.Item, { id: 'LOT_TEST1' }, { forUpdate: true });
                await item.$.set({ isSold: true });
                await item.$.save();

                const receipt = await context.create(xtremSales.nodes.SalesReturnReceipt, {
                    number: '',
                    effectiveDate: date.today(),
                    site: site._id,
                    shipToCustomer: '#US019',
                    lines: [
                        {
                            item,
                            quantityInStockUnit: 20,
                            quantity: 20,
                            toReturnRequestLines: [
                                {
                                    linkedDocument: await salesReturnRequest.lines.at(0),
                                    quantity: 20,
                                    amount: 2,
                                    quantityInStockUnit: 20,
                                },
                            ],
                            stockDetails: [
                                {
                                    site,
                                    item,
                                    location: await context.read(xtremMasterData.nodes.Location, { id: 'LOC3' }),
                                    lotCreateData: { expirationDate: date.today(), item },
                                    status: await context.read(xtremStockData.nodes.StockStatus, { id: 'A' }),
                                    quantityInStockUnit: 20,
                                },
                            ],
                        },
                    ],
                });

                await receipt.$.save();

                salesReturnRequest = await context.read(xtremSales.nodes.SalesReturnRequest, {
                    _id: salesReturnRequest._id,
                });

                assert.equal(await salesReturnRequest.isCreditingAllowed, false);

                await xtremSales.nodes.SalesReturnReceipt.postToStock(context, [receipt._id]);
                await xtremStockData.functions.testLib.simulateStockTransaction(
                    context,
                    receipt._id,
                    xtremSales.nodes.SalesReturnReceipt,
                    'receipt',
                    xtremSales.nodes.SalesReturnReceipt.onStockReply,
                );

                salesReturnRequest = await context.read(xtremSales.nodes.SalesReturnRequest, {
                    _id: salesReturnRequest._id,
                });

                assert.equal(await salesReturnRequest.receiptStatus, 'partiallyReceived');
                assert.equal(await salesReturnRequest.creditStatus, 'notCredited');
                assert.equal(await salesReturnRequest.isCreditingAllowed, true);
                assert.equal(await (await salesReturnRequest.lines.elementAt(0)).isCreditingAllowed, true);
                assert.equal(await salesInvoice.creditStatus, 'notCredited');

                // Ensure same isCreditingAllowed values when approvalStatus is 'confirmed'.

                await salesReturnRequest.$.set({ approvalStatus: 'confirmed' });
                await salesReturnRequest.$.save();

                await xtremStockData.functions.testLib.simulateStockTransaction(
                    context,
                    receipt._id,
                    xtremSales.nodes.SalesReturnReceipt,
                    'receipt',
                    xtremSales.nodes.SalesReturnReceipt.onStockReply,
                );

                assert.equal(await salesReturnRequest.isCreditingAllowed, true);
                assert.equal(await (await salesReturnRequest.lines.elementAt(0)).isCreditingAllowed, true);

                // Set approvalStatus back to 'approved'.
                await salesReturnRequest.$.set({ approvalStatus: 'approved' });
                await salesReturnRequest.$.save();

                // enforce the 'posted' status of the invoice (after post it is 'inProgress'). Normally the 'posted'
                // status is set asynchronously on the reply of the finance integration
                await salesInvoice.$.set({ status: 'posted' });
                await salesInvoice.$.set({ forceUpdateForFinance: true });
                await salesInvoice.$.save();

                const salesCreditMemosCreated =
                    await xtremSales.nodes.SalesReturnRequest.createSalesCreditMemosFromReturnRequestLines(context, [
                        { salesDocumentLine: await salesReturnRequest.lines.elementAt(0) },
                    ]);

                assert.isArray(salesCreditMemosCreated.documentsCreated);

                salesReturnRequest = await context.read(xtremSales.nodes.SalesReturnRequest, {
                    _id: salesReturnRequest._id,
                });

                salesInvoice = await context.read(xtremSales.nodes.SalesInvoice, { _id: salesInvoice._id });

                assert.equal(await salesReturnRequest.creditStatus, 'partiallyCredited');
                assert.equal(await salesInvoice.creditStatus, 'partiallyCredited');
                assert.equal(await salesReturnRequest.isCreditingAllowed, false);
                assert.equal(await (await salesReturnRequest.lines.elementAt(0)).isCreditingAllowed, false);

                if (!salesCreditMemosCreated.documentsCreated) {
                    assert.fail('Failed to create sales credit memos from the sales return request lines.');
                }
                const salesCreditMemo = await context.read(
                    xtremSales.nodes.SalesCreditMemo,
                    { _id: salesCreditMemosCreated.documentsCreated[0]._id },
                    { forUpdate: true },
                );

                await salesCreditMemo.$.set({ taxCalculationStatus: 'done' });
                await salesCreditMemo.$.save();

                await xtremSales.nodes.SalesCreditMemo.post(context, salesCreditMemo);

                assert.equal(await salesReturnRequest.creditStatus, 'partiallyCredited');
                assert.equal(await salesInvoice.creditStatus, 'partiallyCredited');
                assert.equal(await salesReturnRequest.isCreditingAllowed, false);
                assert.equal(await (await salesReturnRequest.lines.elementAt(0)).isCreditingAllowed, false);
                assert.equal(
                    await (
                        await salesReturnRequest.lines.elementAt(0)
                    ).quantityCreditedInProgressInSalesUnit,
                    0,
                );

                await assert.isRejected(
                    xtremSales.nodes.SalesReturnRequest.createSalesCreditMemosFromReturnRequestLines(context, [
                        { salesDocumentLine: await salesReturnRequest.lines.elementAt(0) },
                    ]),
                    'No quantity in sales unit left.',
                );

                const receipt2 = await context.create(xtremSales.nodes.SalesReturnReceipt, {
                    number: '',
                    effectiveDate: date.today(),
                    site: site._id,
                    shipToCustomer: '#US019',
                    lines: [
                        {
                            item,
                            quantityInStockUnit: 10,
                            quantity: 10,
                            toReturnRequestLines: [
                                {
                                    linkedDocument: await salesReturnRequest.lines.at(0),
                                    quantity: 10,
                                    amount: 2,
                                    quantityInStockUnit: 10,
                                },
                            ],
                            stockDetails: [
                                {
                                    site,
                                    item,
                                    location: await context.read(xtremMasterData.nodes.Location, { id: 'LOC3' }),
                                    lotCreateData: { expirationDate: date.today(), item },
                                    status: await context.read(xtremStockData.nodes.StockStatus, { id: 'A' }),
                                    quantityInStockUnit: 10,
                                },
                            ],
                        },
                    ],
                });

                await receipt2.$.save();

                assert.equal(await salesReturnRequest.creditStatus, 'partiallyCredited');
                assert.equal(await salesReturnRequest.isCreditingAllowed, false);
                assert.equal(await (await salesReturnRequest.lines.elementAt(0)).isCreditingAllowed, false);

                await xtremSales.nodes.SalesReturnRequest.createSalesCreditMemosFromReturnRequestLines(context, [
                    { salesDocumentLine: await salesReturnRequest.lines.elementAt(0) },
                ]);

                salesReturnRequest = await context.read(xtremSales.nodes.SalesReturnRequest, {
                    _id: salesReturnRequest._id,
                });

                salesInvoice = await context.read(xtremSales.nodes.SalesInvoice, { _id: salesInvoice._id });

                assert.equal(await salesReturnRequest.creditStatus, 'credited');
                assert.equal(await salesInvoice.creditStatus, 'credited');
            },
            { user: { email: '<EMAIL>' } },
        ));

    it('create sales credit memo from sales return request 2', () =>
        Test.withContext(
            async context => {
                let salesShipment = await context.read(
                    xtremSales.nodes.SalesShipment,
                    { _id: '#SSH4' },
                    { forUpdate: true },
                );

                const line1 = await salesShipment.lines.elementAt(0);

                await line1.$.set({ status: 'shipped', quantity: 30 });

                await salesShipment.$.save();

                const salesReturnRequestsCreated = await (
                    await new xtremSales.classes.SalesReturnRequestCreator(context).prepareNodeCreateData([
                        { salesDocumentLine: line1 },
                    ])
                ).createSalesOutputDocuments();

                const salesInvoicesCreated = await (
                    await new xtremSales.classes.SalesInvoicesCreator(context).prepareNodeCreateData([
                        { salesDocumentLine: line1 },
                    ])
                ).createSalesOutputDocuments();

                assert.isArray(salesReturnRequestsCreated.documentsCreated);
                assert.isArray(salesInvoicesCreated.documentsCreated);

                if (!salesReturnRequestsCreated.documentsCreated) {
                    assert.fail('Failed to create sales return requests from the provided sales documents.');
                }
                if (!salesInvoicesCreated.documentsCreated) {
                    assert.fail('Failed to create sales invoices from the provided sales documents.');
                }
                let salesReturnRequest = await context.read(
                    xtremSales.nodes.SalesReturnRequest,
                    { _id: salesReturnRequestsCreated.documentsCreated[0]._id },
                    { forUpdate: true },
                );
                await salesReturnRequest.$.set({ returnType: 'creditMemo' });
                await salesReturnRequest.$.save();

                await salesReturnRequest.$.set({ approvalStatus: 'approved' });
                await salesReturnRequest.$.save({ flushDeferredActions: true });

                salesReturnRequest = await context.read(xtremSales.nodes.SalesReturnRequest, {
                    _id: salesReturnRequest._id,
                });

                assert.equal(await (await salesReturnRequest.lines.elementAt(0)).isCreditMemoExpected, true);
                assert.equal(await (await salesReturnRequest.lines.elementAt(0)).isReceiptExpected, false);
                assert.equal(await (await salesReturnRequest.lines.elementAt(0)).quantityReceiptInSalesUnit, 0);
                assert.equal(
                    await (
                        await salesReturnRequest.lines.elementAt(0)
                    ).quantityToReceiveInProgressInSalesUnit,
                    0,
                );

                const salesReturnRequestId = salesReturnRequest._id;
                const salesInvoiceId = salesInvoicesCreated.documentsCreated[0]._id;
                // rollbackCache function will flush deferred actions and saves
                await Test.rollbackCache(context);

                salesReturnRequest = await context.read(xtremSales.nodes.SalesReturnRequest, {
                    _id: salesReturnRequestId,
                });

                await xtremSales.functions.setSalesDocumentCloseStatus(
                    context,
                    await salesReturnRequest.number,
                    xtremSales.nodes.SalesReturnRequest,
                    xtremSales.enums.salesReturnRequestCloseStatusMethodReturnDataType,
                );
                salesReturnRequest = await context.read(xtremSales.nodes.SalesReturnRequest, {
                    _id: salesReturnRequest._id,
                });

                assert.equal(await (await salesReturnRequest.lines.elementAt(0)).status, 'closed');
                assert.equal(await salesReturnRequest.status, 'closed');

                salesShipment = await context.read(xtremSales.nodes.SalesShipment, { _id: salesShipment._id });

                assert.equal(await salesShipment.status, 'shipped');
                assert.equal(await salesShipment.returnRequestStatus, 'returnRequested');

                const salesInvoice = await context.read(
                    xtremSales.nodes.SalesInvoice,
                    { _id: salesInvoiceId },
                    { forUpdate: true },
                );
                await salesInvoice.$.set({ taxCalculationStatus: 'done' });
                await salesInvoice.$.save();

                await xtremSales.nodes.SalesInvoice.post(context, salesInvoice);

                // enforce the 'posted' status of the invoice (after post it is 'inProgress'). Normally the 'posted'
                // status is set asynchronously on the reply of the finance integration
                await salesInvoice.$.set({ status: 'posted' });
                await salesInvoice.$.set({ forceUpdateForFinance: true });
                await salesInvoice.$.save();

                await xtremSales.nodes.SalesReturnRequest.createSalesCreditMemosFromReturnRequestLines(context, [
                    { salesDocumentLine: await salesReturnRequest.lines.elementAt(0) },
                ]);

                salesShipment = await context.read(xtremSales.nodes.SalesShipment, { _id: salesShipment._id });

                assert.equal(await salesShipment.returnRequestStatus, 'returnRequested');
            },
            { user: { email: '<EMAIL>' } },
        ));

    it('should not be possible to create credit memo if sales return receipt not posted', () =>
        Test.withContext(
            async context => {
                const salesShipment = await context.read(
                    xtremSales.nodes.SalesShipment,
                    { _id: '#SSH4' },
                    { forUpdate: true },
                );

                const line1 = await salesShipment.lines.elementAt(0);

                await line1.$.set({ status: 'shipped' });

                await line1.$.set({ quantity: 30 });

                await salesShipment.$.save();

                const salesReturnRequestsCreated = await (
                    await new xtremSales.classes.SalesReturnRequestCreator(context).prepareNodeCreateData([
                        { salesDocumentLine: line1 },
                    ])
                ).createSalesOutputDocuments();

                const salesInvoicesCreated = await (
                    await new xtremSales.classes.SalesInvoicesCreator(context).prepareNodeCreateData([
                        { salesDocumentLine: line1 },
                    ])
                ).createSalesOutputDocuments();

                let salesReturnRequest = await context.read(
                    xtremSales.nodes.SalesReturnRequest,
                    { _id: salesReturnRequestsCreated.documentsCreated[0]._id },
                    { forUpdate: true },
                );
                await salesReturnRequest.$.set({ returnType: 'creditMemo' });
                await salesReturnRequest.$.save();
                await salesReturnRequest.$.set({ approvalStatus: 'approved' });
                await salesReturnRequest.$.save();

                const salesInvoice = await context.read(
                    xtremSales.nodes.SalesInvoice,
                    { _id: salesInvoicesCreated.documentsCreated[0]._id },
                    { forUpdate: true },
                );

                await salesInvoice.$.set({ taxCalculationStatus: 'done' });

                await salesInvoice.$.save();

                await xtremSales.nodes.SalesInvoice.post(context, salesInvoice);

                const site = await context.read(xtremSystem.nodes.Site, { id: 'US001' });
                const item = await context.read(xtremMasterData.nodes.Item, { id: 'LOT_TEST1' }, { forUpdate: true });
                await item.$.set({ isSold: true });
                await item.$.save();

                const receipt = await context.create(xtremSales.nodes.SalesReturnReceipt, {
                    number: '',
                    effectiveDate: date.today(),
                    site: site._id,
                    shipToCustomer: '#US019',
                    lines: [
                        {
                            item,
                            quantityInStockUnit: 20,
                            quantity: 20,
                            toReturnRequestLines: [
                                {
                                    linkedDocument: await salesReturnRequest.lines.at(0),
                                    quantity: 20,
                                    amount: 2,
                                    quantityInStockUnit: 20,
                                },
                            ],
                            stockDetails: [
                                {
                                    site,
                                    item,
                                    location: await context.read(xtremMasterData.nodes.Location, { id: 'LOC3' }),
                                    lotCreateData: { expirationDate: date.today(), item },
                                    status: await context.read(xtremStockData.nodes.StockStatus, { id: 'A' }),
                                    quantityInStockUnit: 20,
                                },
                            ],
                        },
                    ],
                });

                await receipt.$.save();

                salesReturnRequest = await context.read(xtremSales.nodes.SalesReturnRequest, {
                    _id: salesReturnRequest._id,
                });

                assert.equal(await salesReturnRequest.isCreditingAllowed, false);

                await xtremSales.nodes.SalesReturnReceipt.postToStock(context, [receipt._id]);
                await xtremStockData.functions.testLib.simulateStockTransaction(
                    context,
                    receipt._id,
                    xtremSales.nodes.SalesReturnReceipt,
                    'receipt',
                    xtremSales.nodes.SalesReturnReceipt.onStockReply,
                );

                salesReturnRequest = await context.read(xtremSales.nodes.SalesReturnRequest, {
                    _id: salesReturnRequest._id,
                });

                assert.equal(await salesReturnRequest.isCreditingAllowed, true);
            },
            { user: { email: '<EMAIL>' } },
        ));
});
