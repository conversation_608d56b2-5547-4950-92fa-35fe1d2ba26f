import { Test } from '@sage/xtrem-core';
import { assert } from 'chai';
import * as xtremSales from '../../../index';

describe('SalesInvoicesCreator', () => {
    before(() => {});
    it('create SalesInvoices with attributes and dimensions from shipment line', () =>
        Test.withContext(async context => {
            const salesShipmentLine: xtremSales.nodes.SalesShipmentLine = await context.read(
                xtremSales.nodes.SalesShipmentLine,
                { _id: '#SSHUS0012021050001|131400' },
                { forUpdate: true },
            );

            // quantityToProcess changed from 10 to 5 to prevent having the following error:
            // The sales document line quantity cannot be larger than the remaining quantity in the related document
            const SalesInvoicesCreatorClass = await new xtremSales.classes.SalesInvoicesCreator(
                context,
            ).prepareNodeCreateData([{ salesDocumentLine: salesShipmentLine, quantityToProcess: 5 }]);

            const salesInvoicesCreated = await SalesInvoicesCreatorClass.createSalesOutputDocuments();

            assert.isArray(salesInvoicesCreated.documentsCreated);
            assert.equal(salesInvoicesCreated.status, 'salesShipmentIsInvoiced');

            if (!salesInvoicesCreated.documentsCreated) {
                assert.fail('Failed to create sales invoices from the provided sales documents.');
            }
            const salesInvoice = await context.read(
                xtremSales.nodes.SalesInvoice,
                { _id: salesInvoicesCreated.documentsCreated[0]._id },
                { forUpdate: true },
            );
            assert.equal(
                JSON.stringify(await (await salesInvoice.lines.elementAt(0)).storedAttributes),
                '{"project":"AttPROJ"}',
            );
            assert.equal(
                JSON.stringify(await (await salesInvoice.lines.elementAt(0)).storedDimensions),
                '{"dimensionType04":"DIMTYPE2VALUE2"}',
            );
        }));

    it('Sales shipment to sales invoice note propagation - Header and Lines', () =>
        Test.withContext(async context => {
            const salesShipmentLine = await context.read(xtremSales.nodes.SalesShipmentLine, { _id: '#SSH18|10' });
            const salesShipment = await salesShipmentLine.document;

            const salesInvoicesCreated = await (
                await new xtremSales.classes.SalesInvoicesCreator(context).prepareNodeCreateData([
                    { salesDocumentLine: salesShipmentLine, quantityToProcess: 10 },
                ])
            ).createSalesOutputDocuments();

            assert.isArray(salesInvoicesCreated.documentsCreated);
            assert.equal(salesInvoicesCreated.status, 'salesShipmentIsInvoiced');
            assert.isNotNull(salesInvoicesCreated.documentsCreated);
            assert.isNotEmpty(salesInvoicesCreated.documentsCreated);

            if (!salesInvoicesCreated.documentsCreated) {
                assert.fail('Failed to create sales invoices from the provided sales documents.');
            }
            assert.isNotEmpty(salesInvoicesCreated.documentsCreated[0]);

            const salesInvoice = await context.read(xtremSales.nodes.SalesInvoice, {
                _id: salesInvoicesCreated.documentsCreated[0]._id,
            });
            const invoiceInternalNote = (await salesInvoice.internalNote).toString();
            const invoiceExternalNote = (await salesInvoice.externalNote).toString();
            const invoiceIsExternalNote = await salesInvoice.isExternalNote;
            const invoiceIsTransferHeaderNote = await salesInvoice.isTransferHeaderNote;
            const invoiceIsTransferLineNote = await salesInvoice.isTransferLineNote;

            assert.isNotEmpty(invoiceInternalNote);
            assert.isNotEmpty(invoiceExternalNote);
            assert.isTrue(invoiceIsExternalNote);
            assert.isTrue(invoiceIsTransferHeaderNote);
            assert.isTrue(invoiceIsTransferLineNote);

            // Notes on invoice must be equal on shipment
            assert.equal(invoiceInternalNote, (await salesShipment.internalNote).toString());
            assert.equal(invoiceExternalNote, (await salesShipment.externalNote).toString());
            assert.equal(invoiceIsExternalNote, await salesShipment.isExternalNote);
            // Setup on invoice must be equal on shipment
            assert.equal(invoiceIsTransferHeaderNote, await salesShipment.isTransferHeaderNote);
            assert.equal(invoiceIsTransferLineNote, await salesShipment.isTransferLineNote);
        }));

    it('Sales shipment to sales invoice note propagation - Header and Lines set to no', () =>
        Test.withContext(async context => {
            const salesShipmentLine = await context.read(xtremSales.nodes.SalesShipmentLine, { _id: '#SSH19|10' });
            const salesShipment = await salesShipmentLine.document;
            const salesInvoicesCreated = await (
                await new xtremSales.classes.SalesInvoicesCreator(context).prepareNodeCreateData([
                    { salesDocumentLine: salesShipmentLine, quantityToProcess: 10 },
                ])
            ).createSalesOutputDocuments();

            assert.isArray(salesInvoicesCreated.documentsCreated);
            assert.equal(salesInvoicesCreated.status, 'salesShipmentIsInvoiced');
            assert.isNotNull(salesInvoicesCreated.documentsCreated);
            assert.isNotEmpty(salesInvoicesCreated.documentsCreated);

            if (!salesInvoicesCreated.documentsCreated) {
                assert.fail('Failed to create sales invoices from the provided sales documents.');
            }
            assert.isNotEmpty(salesInvoicesCreated.documentsCreated[0]);

            const salesInvoice = await context.read(xtremSales.nodes.SalesInvoice, {
                _id: salesInvoicesCreated.documentsCreated[0]._id,
            });
            const invoiceInternalNote = (await salesInvoice.internalNote).toString();
            const invoiceExternalNote = (await salesInvoice.externalNote).toString();
            const invoiceIsExternalNote = await salesInvoice.isExternalNote;
            const invoiceIsTransferHeaderNote = await salesInvoice.isTransferHeaderNote;
            const invoiceIsTransferLineNote = await salesInvoice.isTransferLineNote;

            assert.isEmpty(invoiceInternalNote);
            assert.isEmpty(invoiceExternalNote);
            assert.isFalse(invoiceIsExternalNote);
            assert.isFalse(invoiceIsTransferHeaderNote);
            assert.isFalse(invoiceIsTransferLineNote);

            // Notes on invoice must not be equal on shipment
            assert.notEqual(invoiceInternalNote, (await salesShipment.internalNote).toString());
            assert.notEqual(invoiceExternalNote, (await salesShipment.externalNote).toString());
            assert.notEqual(invoiceIsExternalNote, await salesShipment.isExternalNote);
            // Setup on invoice must be equal on shipment
            assert.equal(invoiceIsTransferHeaderNote, await salesShipment.isTransferHeaderNote);
            assert.equal(invoiceIsTransferLineNote, await salesShipment.isTransferLineNote);
        }));
});
