import type { Context, NodeCreateData } from '@sage/xtrem-core';
import { date, Test } from '@sage/xtrem-core';
import * as xtremSystem from '@sage/xtrem-system';
import { assert } from 'chai';
import * as xtremSales from '../../../index';

describe('SalesTaxCalculator', () => {
    before(() => {});

    function createSalesInvoice(context: Context, invoiceDate: date) {
        const baseData: NodeCreateData<xtremSales.nodes.SalesInvoice> = {
            billToContact: {
                title: 'ms',
                firstName: 'Jane',
                lastName: 'Doe',
                email: '<EMAIL>',
            },
            lines: [
                {
                    _action: 'create',
                    charge: 0,
                    consumptionLinkedAddress: '#US019|1500',
                    consumptionAddress: {
                        country: '#US',
                        name: 'Cust address',
                        locationPhoneNumber: '+15550123456',
                        addressLine1: '1st Avenue',
                        addressLine2: undefined,
                        city: 'One',
                        region: 'AZ',
                        postcode: '85034',
                    },
                    discount: 0,
                    grossPrice: 12,
                    item: '#SalesItem81',
                    itemDescription: 'Sales Item 81',
                    amountIncludingTax: 132,
                    netPrice: 12,
                    origin: 'direct',
                    priceOrigin: 'manual',
                    priceReason: null,
                    providerSite: '#US001',
                    quantity: 11,
                    quantityInStockUnit: 11,
                    site: '#US001',
                    unit: '#MILLILITER',
                    unitToStockUnitConversionFactor: 1,
                    stockUnit: '#LITER',
                    taxAmount: 0,
                },
            ],
            billToAddress: {
                country: '#US',
                name: 'Cust address',
                locationPhoneNumber: '+15550123456',
                addressLine1: '1st Avenue',
                addressLine2: undefined,
                city: 'One',
                region: 'AZ',
                postcode: '85034',
            },
            billToLinkedAddress: '#US019|1500',
            currency: '#USD',
            incoterm: null,
            paymentTerm: '#DUE_UPON_RECEIPT_ALL',
            billToCustomer: '#US019',
            site: '#US001',
            number: undefined,
            date: invoiceDate,
            dueDate: invoiceDate,
            creationNumber: undefined,
            isPrinted: false,
            isSent: false,
            status: 'draft',
            creditStatus: 'notCredited',
            taxCalculationStatus: 'notDone',
            totalAmountExcludingTax: 132,
            totalTaxAmount: 0,
        };
        return context.create(xtremSales.nodes.SalesInvoice, baseData);
    }

    it('XT-20293 Tax determination/calculation not triggered when updating sales invoice address line', () =>
        Test.withContext(
            async context => {
                const company = await context.tryRead(
                    xtremSystem.nodes.Company,
                    { _id: '#US001' },
                    { forUpdate: true },
                );

                await company!.$.set({ taxEngine: 'genericTaxCalculation' });

                await company!.$.save();

                let salesInvoice: xtremSales.nodes.SalesInvoice = await createSalesInvoice(
                    context,
                    date.make(2022, 2, 1),
                );
                await salesInvoice.$.save();

                assert.equal(String(await (await salesInvoice.taxes.at(0))?.taxAmount), '2.77');
                assert.equal(
                    String(await (await (await salesInvoice.taxes.at(0))?.taxReference)?.name),
                    'State reduced rate',
                );

                salesInvoice = (await context.tryRead(
                    xtremSales.nodes.SalesInvoice,
                    { _id: salesInvoice._id },
                    { forUpdate: true },
                ))!;

                const lineTaxId = (await (await salesInvoice.lines.at(0))?.taxes.at(0))!._id;
                await (
                    await salesInvoice.lines.at(0)
                )?.$.set({
                    grossPrice: 22,
                    taxes: [{ _action: 'update', _id: lineTaxId, taxReference: '#US005' }],
                });

                await salesInvoice.$.save();

                assert.equal(String(await (await salesInvoice.taxes.at(0))?.taxAmount), '7.5');
                assert.equal(
                    String(await (await (await salesInvoice.taxes.at(0))?.taxReference)?.name),
                    'Special reduced rate',
                );

                await (
                    await salesInvoice.lines.at(0)
                )?.$.set({
                    grossPrice: 52,
                });

                await salesInvoice.$.save();

                assert.equal(String(await (await salesInvoice.taxes.at(0))?.taxAmount), '17.73');
                assert.equal(
                    String(await (await (await salesInvoice.taxes.at(0))?.taxReference)?.name),
                    'Special reduced rate',
                );

                await (
                    await salesInvoice.lines.at(0)
                )?.$.set({
                    consumptionAddress: {
                        country: '#FR',
                    },
                });

                await salesInvoice.$.save();

                salesInvoice = (await context.tryRead(
                    xtremSales.nodes.SalesInvoice,
                    { _id: salesInvoice._id },
                    { forUpdate: true },
                ))!;

                assert.equal(String(await (await salesInvoice.taxes.at(0))?.taxAmount), '12.01');
                assert.equal(
                    String(await (await (await salesInvoice.taxes.at(0))?.taxReference)?.name),
                    'State reduced rate',
                );

                await (
                    await salesInvoice.lines.at(0)
                )?.$.set({
                    consumptionAddress: {
                        country: '#ZA',
                    },
                });

                await salesInvoice.$.save();

                salesInvoice = (await context.tryRead(
                    xtremSales.nodes.SalesInvoice,
                    { _id: salesInvoice._id },
                    { forUpdate: true },
                ))!;

                assert.equal(await (await salesInvoice.taxes.at(0))?.taxAmount, undefined);
                assert.equal(await (await salesInvoice.taxes.at(0))?.taxReference, undefined);
                assert.equal(await (await salesInvoice.taxes.at(0))?.taxCategory, undefined);
            },
            { today: '2022-02-01' },
        ));
});
