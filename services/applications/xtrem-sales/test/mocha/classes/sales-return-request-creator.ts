import { Test } from '@sage/xtrem-core';
import { assert } from 'chai';
import * as xtremSales from '../../../index';

describe('SalesReturnRequestCreator', () => {
    it('create Sales Return 1', () =>
        Test.withContext(
            async context => {
                const salesDocumentLine: xtremSales.nodes.SalesShipmentLine = await context.read(
                    xtremSales.nodes.SalesShipmentLine,
                    { _id: '#SSHUS0012021050001|131400' },
                    { forUpdate: true },
                );
                assert.equal(await salesDocumentLine._sortValue, 131400);
                assert.equal(await (await salesDocumentLine.document).number, 'SSHUS0012021050001');

                const { documentsCreated: salesReturnRequest, status: salesReturnRequestStatus } = await (
                    await new xtremSales.classes.SalesReturnRequestCreator(context).prepareNodeCreateData([
                        { salesDocumentLine, quantityToProcess: 10 },
                    ])
                ).createSalesOutputDocuments();

                assert.isArray(salesReturnRequest);
                assert.isNotEmpty(salesReturnRequest);

                assert.equal(salesReturnRequestStatus, 'salesShipmentReturnIsRequested');

                if (!salesReturnRequest?.[0]) {
                    assert.fail('The sales return request is empty.');
                }
                assert.equal(await salesReturnRequest?.[0]?.number, 'ST210001');

                assert.equal(
                    JSON.stringify(await (await salesReturnRequest?.[0]?.lines?.elementAt(0))?.storedAttributes),
                    '{"project":"AttPROJ"}',
                );
                assert.equal(
                    JSON.stringify(
                        (await (await salesReturnRequest?.[0]?.lines?.elementAt(0))?.storedDimensions) || {},
                    ),
                    '{"dimensionType04":"DIMTYPE2VALUE2"}',
                );
            },
            { user: { email: '<EMAIL>' }, today: '2021-05-01' },
        ));

    it('create Sales Return 2', () =>
        Test.withContext(
            async context => {
                let salesShipment: xtremSales.nodes.SalesShipment = await context.read(
                    xtremSales.nodes.SalesShipment,
                    { number: 'SSHUS0022021030003' },
                    { forUpdate: true },
                );

                const line1 = await salesShipment.lines.elementAt(0);
                const line2 = await salesShipment.lines.elementAt(1);

                await line1.$.set({ status: 'shipped' });
                await line2.$.set({ status: 'shipped' });

                await salesShipment.$.save();

                const { documentsCreated } = await (
                    await new xtremSales.classes.SalesReturnRequestCreator(context).prepareNodeCreateData([
                        { salesDocumentLine: line1, quantityToProcess: 20 },
                        { salesDocumentLine: line2, quantityToProcess: 20 },
                    ])
                ).createSalesOutputDocuments();

                assert.equal(documentsCreated?.length, 1);

                salesShipment = await context.read(xtremSales.nodes.SalesShipment, { number: 'SSHUS0022021030003' });

                assert.equal(await salesShipment.returnRequestStatus, 'returnRequested');

                const salesReturnRequest = await context.read(
                    xtremSales.nodes.SalesReturnRequest,
                    { _id: documentsCreated?.[0]._id },
                    { forUpdate: true },
                );
                await salesReturnRequest.$.set({
                    lines: [
                        {
                            _id: (await salesReturnRequest.lines.elementAt(0))._id,
                            _action: 'delete',
                        },
                    ],
                });

                await salesReturnRequest.$.save();

                salesShipment = await context.read(xtremSales.nodes.SalesShipment, { number: 'SSHUS0022021030003' });
                assert.equal(await salesShipment.returnRequestStatus, 'returnPartiallyRequested');
            },
            { user: { email: '<EMAIL>' } },
        ));

    it('create Sales Return 1 failed', () =>
        Test.withContext(
            async context => {
                const salesShipmentLine: xtremSales.nodes.SalesShipmentLine = await context.read(
                    xtremSales.nodes.SalesShipmentLine,
                    { _id: '#SSHUS0012021050001|131400' },
                    { forUpdate: true },
                );
                await salesShipmentLine.$.set({ returnRequestStatus: 'returnRequested' });
                await salesShipmentLine.$.save();

                await assert.isRejected(
                    new xtremSales.classes.SalesReturnRequestCreator(context).prepareNodeCreateData([
                        { salesDocumentLine: salesShipmentLine, quantityToProcess: 10 },
                    ]),
                    'A shipment line is already returned.',
                );
            },
            { user: { email: '<EMAIL>' } },
        ));

    it('create Sales Return 2 failed', () =>
        Test.withContext(
            async context => {
                const salesShipmentLine: xtremSales.nodes.SalesShipmentLine = await context.read(
                    xtremSales.nodes.SalesShipmentLine,
                    { _id: '#SSHUS0012021050001|131400' },
                    { forUpdate: true },
                );
                await salesShipmentLine.$.set({ status: 'readyToShip' });
                await salesShipmentLine.$.save();
                await assert.isRejected(
                    new xtremSales.classes.SalesReturnRequestCreator(context).prepareNodeCreateData([
                        { salesDocumentLine: salesShipmentLine, quantityToProcess: 10 },
                    ]),
                    'A shipment line is not posted.',
                );
            },
            { user: { email: '<EMAIL>' } },
        ));

    it('create Sales Return 3 failed', () =>
        Test.withContext(
            async context => {
                let salesShipmentLine: xtremSales.nodes.SalesShipmentLine = await context.read(
                    xtremSales.nodes.SalesShipmentLine,
                    { _id: '#SSHUS0022021030003|131300' },
                    { forUpdate: true },
                );

                await salesShipmentLine.$.set({ status: 'shipped' });
                await salesShipmentLine.$.save();

                const { documentsCreated } = await (
                    await new xtremSales.classes.SalesReturnRequestCreator(context).prepareNodeCreateData([
                        { salesDocumentLine: salesShipmentLine, quantityToProcess: 20 },
                    ])
                ).createSalesOutputDocuments();

                assert.isArray(documentsCreated);
                assert.isNotEmpty(documentsCreated);

                const salesReturnRequest = await context.read(
                    xtremSales.nodes.SalesReturnRequest,
                    { _id: documentsCreated?.[0]?._id },
                    { forUpdate: true },
                );
                await salesReturnRequest.$.set({
                    lines: [
                        {
                            _id: (await salesReturnRequest.lines.elementAt(0))._id,
                            status: 'inProgress',
                            _action: 'update',
                        },
                    ],
                });

                await salesReturnRequest.$.save();

                salesShipmentLine = await context.read(
                    xtremSales.nodes.SalesShipmentLine,
                    { _id: 'SSHUS0022021030003|131300' },
                    { forUpdate: true },
                );

                await assert.isRejected(
                    new xtremSales.classes.SalesReturnRequestCreator(context).prepareNodeCreateData([
                        { salesDocumentLine: salesShipmentLine, quantityToProcess: 10 },
                    ]),
                    'A shipment line is already returned.',
                );
            },
            { user: { email: '<EMAIL>' } },
        ));

    it('create Sales Return 4 failed', () =>
        Test.withContext(
            async context => {
                const salesShipment = await context.read(
                    xtremSales.nodes.SalesShipment,
                    { number: 'SSHUS0022021030003' },
                    { forUpdate: true },
                );

                const line1 = await salesShipment.lines.elementAt(0);
                const line2 = await salesShipment.lines.elementAt(1);

                await line1.$.set({ status: 'shipped' });
                await line2.$.set({ status: 'shipped' });

                await salesShipment.$.save();
                const prepared = await new xtremSales.classes.SalesReturnRequestCreator(context).prepareNodeCreateData([
                    { salesDocumentLine: line1, quantityToProcess: 10 },
                    { salesDocumentLine: line2, quantityToProcess: 21 },
                ]);
                await assert.isRejected(
                    prepared.createSalesOutputDocuments(),
                    'The sales document line quantity cannot be larger than the remaining quantity in the related document.',
                );
            },
            { user: { email: '<EMAIL>' } },
        ));

    it('Sales shipment to sales return request note propagation - Header and Lines', () =>
        Test.withContext(async context => {
            let salesShipment: xtremSales.nodes.SalesShipment = await context.read(
                xtremSales.nodes.SalesShipment,
                { number: 'SSHUS0022021030003' },
                { forUpdate: true },
            );

            await salesShipment.$.set({ isTransferHeaderNote: true });
            await salesShipment.$.set({ isTransferLineNote: true });

            const line1 = await salesShipment.lines.elementAt(0);

            await line1.$.set({ status: 'shipped' });

            await salesShipment.$.save();

            const { documentsCreated: salesReturnCreated } = await (
                await new xtremSales.classes.SalesReturnRequestCreator(context).prepareNodeCreateData([
                    { salesDocumentLine: line1, quantityToProcess: 20 },
                ])
            ).createSalesOutputDocuments();

            salesShipment = await context.read(xtremSales.nodes.SalesShipment, { number: 'SSHUS0022021030003' });

            assert.equal(await salesShipment.returnRequestStatus, 'returnPartiallyRequested');

            if (salesReturnCreated) {
                const salesReturnRequest: xtremSales.nodes.SalesReturnRequest = await context.read(
                    xtremSales.nodes.SalesReturnRequest,
                    { _id: salesReturnCreated?.[0]._id },
                );

                const salesReturnRequestInternalNote = (await salesReturnRequest.internalNote).toString();
                const salesReturnRequestIsTransferHeaderNote = await salesReturnRequest.isTransferHeaderNote;
                const salesReturnRequestIsTransferLineNote = await salesReturnRequest.isTransferLineNote;

                assert.isNotEmpty(salesReturnRequestInternalNote);
                assert.isTrue(salesReturnRequestIsTransferHeaderNote);
                assert.isTrue(salesReturnRequestIsTransferLineNote);

                // Notes on shipment must be equal on return request
                assert.equal(salesReturnRequestInternalNote, (await salesShipment.internalNote).toString());

                // Setup on shipment must be equal on return request
                assert.equal(salesReturnRequestIsTransferHeaderNote, await salesShipment.isTransferHeaderNote);
                assert.equal(salesReturnRequestIsTransferLineNote, await salesShipment.isTransferLineNote);

                // Lines
                const salesReturnRequestLine = await context
                    .query(xtremSales.nodes.SalesReturnRequestLine, {
                        filter: { document: { _id: salesReturnRequest._id } },
                        last: 1,
                    })
                    .elementAt(0);

                const lineInternalNote = (await salesReturnRequestLine.internalNote).toString();
                assert.isNotEmpty(lineInternalNote);

                // Notes on sales return request must be equal on shipment
                assert.equal(lineInternalNote, (await line1.internalNote).toString());
            }
        }));

    it('Sales shipment to sales return request note propagation - Header only', () =>
        Test.withContext(async context => {
            let salesShipment: xtremSales.nodes.SalesShipment = await context.read(
                xtremSales.nodes.SalesShipment,
                { number: 'SSHUS0022021030003' },
                { forUpdate: true },
            );

            await salesShipment.$.set({ isTransferHeaderNote: true });

            const line1 = await salesShipment.lines.elementAt(0);

            await line1.$.set({ status: 'shipped' });

            await salesShipment.$.save();

            const salesReturnCreated = await (
                await new xtremSales.classes.SalesReturnRequestCreator(context).prepareNodeCreateData([
                    { salesDocumentLine: line1, quantityToProcess: 20 },
                ])
            ).createSalesOutputDocuments();

            salesShipment = await context.read(xtremSales.nodes.SalesShipment, { number: 'SSHUS0022021030003' });

            assert.equal(await salesShipment.returnRequestStatus, 'returnPartiallyRequested');

            if (salesReturnCreated.documentsCreated) {
                const salesReturnRequest: xtremSales.nodes.SalesReturnRequest = await context.read(
                    xtremSales.nodes.SalesReturnRequest,
                    { _id: salesReturnCreated.documentsCreated[0]._id },
                );

                const salesReturnRequestInternalNote = (await salesReturnRequest.internalNote).toString();
                const salesReturnRequestIsTransferHeaderNote = await salesReturnRequest.isTransferHeaderNote;
                const salesReturnRequestIsTransferLineNote = await salesReturnRequest.isTransferLineNote;

                assert.isNotEmpty(salesReturnRequestInternalNote);
                assert.isTrue(salesReturnRequestIsTransferHeaderNote);
                assert.isFalse(salesReturnRequestIsTransferLineNote);

                // Notes on shipment must be equal on return request
                assert.equal(salesReturnRequestInternalNote, (await salesShipment.internalNote).toString());

                // Setup on shipment must be equal on return request
                assert.equal(salesReturnRequestIsTransferHeaderNote, await salesShipment.isTransferHeaderNote);
                assert.equal(salesReturnRequestIsTransferLineNote, await salesShipment.isTransferLineNote);

                // Lines
                const salesReturnRequestLine = await context
                    .query(xtremSales.nodes.SalesReturnRequestLine, {
                        filter: { document: { _id: salesReturnRequest._id } },
                        last: 1,
                    })
                    .elementAt(0);

                const lineInternalNote = (await salesReturnRequestLine.internalNote).toString();
                assert.isEmpty(lineInternalNote);
            }
        }));

    it('Sales shipment to sales return request note propagation - lines only', () =>
        Test.withContext(async context => {
            let salesShipment: xtremSales.nodes.SalesShipment = await context.read(
                xtremSales.nodes.SalesShipment,
                { number: 'SSHUS0022021030003' },
                { forUpdate: true },
            );

            await salesShipment.$.set({ isTransferLineNote: true });

            const line1 = await salesShipment.lines.elementAt(0);

            await line1.$.set({ status: 'shipped' });

            await salesShipment.$.save();

            const { documentsCreated: salesReturnCreated } = await (
                await new xtremSales.classes.SalesReturnRequestCreator(context).prepareNodeCreateData([
                    { salesDocumentLine: line1, quantityToProcess: 20 },
                ])
            ).createSalesOutputDocuments();

            salesShipment = await context.read(xtremSales.nodes.SalesShipment, { number: 'SSHUS0022021030003' });

            assert.equal(await salesShipment.returnRequestStatus, 'returnPartiallyRequested');

            if (salesReturnCreated) {
                const salesReturnRequest: xtremSales.nodes.SalesReturnRequest = await context.read(
                    xtremSales.nodes.SalesReturnRequest,
                    { _id: salesReturnCreated[0]._id },
                );

                const salesReturnRequestInternalNote = (await salesReturnRequest.internalNote).toString();
                const salesReturnRequestIsTransferHeaderNote = await salesReturnRequest.isTransferHeaderNote;
                const salesReturnRequestIsTransferLineNote = await salesReturnRequest.isTransferLineNote;

                assert.isEmpty(salesReturnRequestInternalNote);
                assert.isFalse(salesReturnRequestIsTransferHeaderNote);
                assert.isTrue(salesReturnRequestIsTransferLineNote);

                // Setup on shipment must be equal on return request
                assert.equal(salesReturnRequestIsTransferHeaderNote, await salesShipment.isTransferHeaderNote);
                assert.equal(salesReturnRequestIsTransferLineNote, await salesShipment.isTransferLineNote);

                // Lines
                const salesReturnRequestLine = await context
                    .query(xtremSales.nodes.SalesReturnRequestLine, {
                        filter: { document: { _id: salesReturnRequest._id } },
                        last: 1,
                    })
                    .elementAt(0);

                const lineInternalNote = (await salesReturnRequestLine.internalNote).toString();
                assert.isNotEmpty(lineInternalNote);

                // Notes on sales return request must be equal on shipment
                assert.equal(lineInternalNote, (await line1.internalNote).toString());
            }
        }));
});
