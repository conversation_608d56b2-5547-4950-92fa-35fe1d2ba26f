import { Test } from '@sage/xtrem-core';
import { assert } from 'chai';
import * as xtremSales from '../../../index';

describe('SalesShipmentsCreator', () => {
    before(() => {});

    it('Null dates class initialization 1', () =>
        Test.withContext(async context => {
            // @ts-ignore
            const salesOrderLine: xtremSales.nodes.SalesOrderLine = await context.tryRead(
                xtremSales.nodes.SalesOrderLine,
                { _id: '#SO5|60500' },
                { forUpdate: true },
            );
            await salesOrderLine.$.set({ doNotShipAfterDate: null });
            await salesOrderLine.$.set({ doNotShipBeforeDate: null });
            const salesShipmentsCreatorClass = await new xtremSales.classes.SalesShipmentsCreator(
                context,
            ).prepareNodeCreateData([{ salesDocumentLine: salesOrderLine, quantityToProcess: 2 }]);
            assert.exists(salesShipmentsCreatorClass);
        }));
    it('Null dates class initialization 2', () =>
        Test.withContext(async context => {
            // @ts-ignore
            const salesOrderLine: xtremSales.nodes.SalesOrderLine = await context.tryRead(
                xtremSales.nodes.SalesOrderLine,
                { _id: '#SO5|60500' },
                { forUpdate: true },
            );
            await salesOrderLine.$.set({ doNotShipAfterDate: null });
            await salesOrderLine.$.set({ doNotShipBeforeDate: null });
            const salesShipmentsCreatorClass = await new xtremSales.classes.SalesShipmentsCreator(
                context,
            ).prepareNodeCreateData([{ salesDocumentLine: salesOrderLine, quantityToProcess: 2 }], {
                processAllShippableLines: true,
            });
            assert.exists(salesShipmentsCreatorClass);
        }));
    it('getCreateSalesShipmentsStatus status salesOrderIsShipped', () =>
        Test.withContext(async context => {
            // @ts-ignore
            const salesOrderLine: xtremSales.nodes.SalesOrderLine = await context.tryRead(
                xtremSales.nodes.SalesOrderLine,
                { _id: '#SO5|60500' },
                { forUpdate: true },
            );
            const salesShipmentsCreatorClass = await new xtremSales.classes.SalesShipmentsCreator(
                context,
            ).prepareNodeCreateData([{ salesDocumentLine: salesOrderLine, quantityToProcess: 2 }], {
                processAllShippableLines: true,
            });
            assert.equal(salesShipmentsCreatorClass.getCreateSalesOutputDocumentsStatus(), 'salesOrderIsShipped');
        }));

    it('getCreateSalesShipmentsStatus status salesOrderIsPartiallyShipped', () =>
        Test.withContext(async context => {
            // @ts-ignore
            const salesOrderLine: xtremSales.nodes.SalesOrderLine = await context.tryRead(
                xtremSales.nodes.SalesOrderLine,
                { _id: '#SO5|60500' },
                { forUpdate: true },
            );
            const salesShipmentsCreatorClass = await new xtremSales.classes.SalesShipmentsCreator(
                context,
            ).prepareNodeCreateData([{ salesDocumentLine: salesOrderLine, quantityToProcess: 2 }], {
                processAllShippableLines: true,
            });
            salesShipmentsCreatorClass.lineErrors[0] = { message: 'error', lineNumber: 1, linePosition: 1 };
            assert.equal(
                salesShipmentsCreatorClass.getCreateSalesOutputDocumentsStatus(),
                'salesOrderIsPartiallyShipped',
            );
        }));

    it('getCreateSalesShipmentsStatus status salesOrderIsNotShipped', () =>
        Test.withContext(async context => {
            // @ts-ignore
            const salesOrderLine: xtremSales.nodes.SalesOrderLine = await context.tryRead(
                xtremSales.nodes.SalesOrderLine,
                { _id: '#SO5|60500' },
                { forUpdate: true },
            );
            const salesShipmentsCreatorClass = await new xtremSales.classes.SalesShipmentsCreator(
                context,
            ).prepareNodeCreateData([{ salesDocumentLine: salesOrderLine, quantityToProcess: 2 }], {
                processAllShippableLines: true,
            });
            salesShipmentsCreatorClass.lineErrors[0] = { message: 'error', lineNumber: 1, linePosition: 1 };
            salesShipmentsCreatorClass.salesOutputDocuments = {};
            assert.equal(salesShipmentsCreatorClass.getCreateSalesOutputDocumentsStatus(), 'salesOrderIsNotShipped');
        }));

    it('Sales order to sales shipment note propagation - Header and Lines', () =>
        Test.withContext(async context => {
            const salesOrder: xtremSales.nodes.SalesOrder = await context.read(
                xtremSales.nodes.SalesOrder,
                { _id: '#SO36' },
                { forUpdate: true },
            );
            await salesOrder.$.set({ isTransferHeaderNote: true, isTransferLineNote: true });
            await salesOrder.$.save();

            const salesDocumentLine = await salesOrder.lines.takeOne(async line => (await line._sortValue) === 10);

            if (!salesDocumentLine) {
                assert.fail('Sales order line not found');
            }

            const salesShipmentsCreatorClass = await new xtremSales.classes.SalesShipmentsCreator(
                context,
            ).prepareNodeCreateData([{ salesDocumentLine, quantityToProcess: 12 }]);
            assert.exists(salesShipmentsCreatorClass);

            const salesShipmentCreated = await salesShipmentsCreatorClass.createSalesOutputDocuments();
            assert.equal(salesShipmentCreated.status, 'salesOrderIsShipped');
            assert.isNotNull(salesShipmentCreated.documentsCreated);
            assert.isNotEmpty(salesShipmentCreated.documentsCreated);

            if (salesShipmentCreated.documentsCreated) {
                assert.isNotEmpty(salesShipmentCreated.documentsCreated[0]);

                const salesShipment: xtremSales.nodes.SalesShipment = await context.read(
                    xtremSales.nodes.SalesShipment,
                    { _id: salesShipmentCreated.documentsCreated[0]._id },
                    { forUpdate: true },
                );
                const shipmentInternalNote = (await salesShipment.internalNote).toString();
                const shipmentExternalNote = (await salesShipment.externalNote).toString();
                const shipmentIsExternalNote = await salesShipment.isExternalNote;
                const shipmentisTransferHeaderNote = await salesShipment.isTransferHeaderNote;
                const shipmentisTransferLineNote = await salesShipment.isTransferLineNote;

                assert.isNotEmpty(shipmentInternalNote);
                assert.isNotEmpty(shipmentExternalNote);
                assert.isTrue(shipmentIsExternalNote);
                assert.isTrue(shipmentisTransferHeaderNote);
                assert.isTrue(shipmentisTransferLineNote);

                // Notes on shipment must be equal on order
                assert.equal(shipmentInternalNote, (await salesOrder.internalNote).toString());
                assert.equal(shipmentExternalNote, (await salesOrder.externalNote).toString());
                assert.equal(shipmentIsExternalNote, await salesOrder.isExternalNote);
                // Setup on shipment must be equal on order
                assert.equal(shipmentisTransferHeaderNote, await salesOrder.isTransferHeaderNote);
                assert.equal(shipmentisTransferLineNote, await salesOrder.isTransferLineNote);
            }
        }));

    it('Sales order to sales shipment note propagation - Header only', () =>
        Test.withContext(async context => {
            const salesOrder: xtremSales.nodes.SalesOrder = await context.read(
                xtremSales.nodes.SalesOrder,
                { _id: '#SO36' },
                { forUpdate: true },
            );
            await salesOrder.$.set({ isTransferHeaderNote: true });
            await salesOrder.$.save();

            const salesOrderLine: xtremSales.nodes.SalesOrderLine = await context.read(
                xtremSales.nodes.SalesOrderLine,
                { _id: '#SO36|10' },
            );

            const salesShipmentsCreatorClass = await new xtremSales.classes.SalesShipmentsCreator(
                context,
            ).prepareNodeCreateData([{ salesDocumentLine: salesOrderLine, quantityToProcess: 12 }]);
            assert.exists(salesShipmentsCreatorClass);

            const salesShipmentCreated = await salesShipmentsCreatorClass.createSalesOutputDocuments();
            assert.equal(salesShipmentCreated.status, 'salesOrderIsShipped');
            assert.isNotNull(salesShipmentCreated.documentsCreated);
            assert.isNotEmpty(salesShipmentCreated.documentsCreated);

            if (salesShipmentCreated.documentsCreated) {
                const salesShipment: xtremSales.nodes.SalesShipment = await context.read(
                    xtremSales.nodes.SalesShipment,
                    { _id: salesShipmentCreated.documentsCreated[0]._id },
                );
                const shipmentInternalNote = (await salesShipment.internalNote).toString();
                const shipmentExternalNote = (await salesShipment.externalNote).toString();
                const shipmentIsExternalNote = await salesShipment.isExternalNote;
                const shipmentisTransferHeaderNote = await salesShipment.isTransferHeaderNote;
                const shipmentisTransferLineNote = await salesShipment.isTransferLineNote;

                assert.isNotEmpty(shipmentInternalNote);
                assert.isNotEmpty(shipmentExternalNote);
                assert.isTrue(shipmentIsExternalNote);
                assert.isTrue(shipmentisTransferHeaderNote);
                assert.isFalse(shipmentisTransferLineNote);

                // Header
                // Notes on shipment must be equal on order
                assert.equal(shipmentInternalNote, (await salesOrder.internalNote).toString());
                assert.equal(shipmentExternalNote, (await salesOrder.externalNote).toString());
                assert.equal(shipmentIsExternalNote, await salesOrder.isExternalNote);
                // Setup on shipment must be equal on order
                assert.equal(shipmentisTransferHeaderNote, await salesOrder.isTransferHeaderNote);
                assert.equal(shipmentisTransferLineNote, await salesOrder.isTransferLineNote);

                // Lines
                const salesShipmentLine = (
                    await context
                        .query(xtremSales.nodes.SalesShipmentLine, {
                            filter: {
                                document: { _id: salesShipment._id },
                            },
                            last: 1,
                        })
                        .toArray()
                )[0];

                const lineInternalNote = (await salesShipmentLine.internalNote).toString();
                const lineExternalNote = (await salesShipmentLine.externalNote).toString();
                const lineIsExternalNote = await salesShipmentLine.isExternalNote;

                assert.isEmpty(lineInternalNote);
                assert.isEmpty(lineExternalNote);
                assert.isFalse(lineIsExternalNote);

                // Notes on shipment must be equal on order
                assert.notEqual(lineInternalNote, (await salesOrderLine.internalNote).toString());
                assert.notEqual(lineExternalNote, (await salesOrderLine.externalNote).toString());
                assert.notEqual(lineIsExternalNote, await salesOrderLine.isExternalNote);
            }
        }));

    it('Sales order to sales shipment note propagation - Lines only', () =>
        Test.withContext(async context => {
            const salesOrder: xtremSales.nodes.SalesOrder = await context.read(
                xtremSales.nodes.SalesOrder,
                { _id: '#SO36' },
                { forUpdate: true },
            );
            await salesOrder.$.set({ isTransferLineNote: true });
            await salesOrder.$.save();

            const salesOrderLine: xtremSales.nodes.SalesOrderLine = await context.read(
                xtremSales.nodes.SalesOrderLine,
                { _id: '#SO36|10' },
            );

            const salesShipmentsCreatorClass = await new xtremSales.classes.SalesShipmentsCreator(
                context,
            ).prepareNodeCreateData([{ salesDocumentLine: salesOrderLine, quantityToProcess: 12 }]);
            assert.exists(salesShipmentsCreatorClass);

            const salesShipmentCreated = await salesShipmentsCreatorClass.createSalesOutputDocuments();
            assert.equal(salesShipmentCreated.status, 'salesOrderIsShipped');
            assert.isNotNull(salesShipmentCreated.documentsCreated);
            assert.isNotEmpty(salesShipmentCreated.documentsCreated);

            if (salesShipmentCreated.documentsCreated) {
                const salesShipment: xtremSales.nodes.SalesShipment = await context.read(
                    xtremSales.nodes.SalesShipment,
                    { _id: salesShipmentCreated.documentsCreated[0]._id },
                );
                const shipmentInternalNote = (await salesShipment.internalNote).toString();
                const shipmentExternalNote = (await salesShipment.externalNote).toString();
                const shipmentIsExternalNote = await salesShipment.isExternalNote;
                const shipmentisTransferHeaderNote = await salesShipment.isTransferHeaderNote;
                const shipmentisTransferLineNote = await salesShipment.isTransferLineNote;

                assert.isEmpty(shipmentInternalNote);
                assert.isEmpty(shipmentExternalNote);
                assert.isFalse(shipmentIsExternalNote);
                assert.isFalse(shipmentisTransferHeaderNote);
                assert.isTrue(shipmentisTransferLineNote);

                // Header
                // Notes on shipment must not be equal on order
                assert.notEqual(shipmentInternalNote, (await salesOrder.internalNote).toString());
                assert.notEqual(shipmentExternalNote, (await salesOrder.externalNote).toString());
                assert.notEqual(shipmentIsExternalNote, await salesOrder.isExternalNote);
                // Setup on shipment must be equal on order
                assert.equal(shipmentisTransferHeaderNote, await salesOrder.isTransferHeaderNote);
                assert.equal(shipmentisTransferLineNote, await salesOrder.isTransferLineNote);

                // Lines
                const salesShipmentLine = (
                    await context
                        .query(xtremSales.nodes.SalesShipmentLine, {
                            filter: {
                                document: { _id: salesShipment._id },
                            },
                            last: 1,
                        })
                        .toArray()
                )[0];

                const lineInternalNote = (await salesShipmentLine.internalNote).toString();
                const lineExternalNote = (await salesShipmentLine.externalNote).toString();
                const lineIsExternalNote = await salesShipmentLine.isExternalNote;

                assert.isNotEmpty(lineInternalNote);
                assert.isNotEmpty(lineExternalNote);
                assert.isTrue(lineIsExternalNote);

                // Notes on shipment must be equal on order
                assert.equal(lineInternalNote, (await salesOrderLine.internalNote).toString());
                assert.equal(lineExternalNote, (await salesOrderLine.externalNote).toString());
                assert.equal(lineIsExternalNote, await salesOrderLine.isExternalNote);
            }
        }));

    it('Sales order to sales shipment note propagation - No note propagation', () =>
        Test.withContext(async context => {
            const salesOrder: xtremSales.nodes.SalesOrder = await context.read(xtremSales.nodes.SalesOrder, {
                _id: '#SO36',
            });

            const salesOrderLine: xtremSales.nodes.SalesOrderLine = await context.read(
                xtremSales.nodes.SalesOrderLine,
                { _id: '#SO36|10' },
            );

            const salesShipmentsCreatorClass = await new xtremSales.classes.SalesShipmentsCreator(
                context,
            ).prepareNodeCreateData([{ salesDocumentLine: salesOrderLine, quantityToProcess: 12 }]);
            assert.exists(salesShipmentsCreatorClass);

            const salesShipmentCreated = await salesShipmentsCreatorClass.createSalesOutputDocuments();
            assert.equal(salesShipmentCreated.status, 'salesOrderIsShipped');
            assert.isNotNull(salesShipmentCreated.documentsCreated);
            assert.isNotEmpty(salesShipmentCreated.documentsCreated);

            if (salesShipmentCreated.documentsCreated) {
                const salesShipment: xtremSales.nodes.SalesShipment = await context.read(
                    xtremSales.nodes.SalesShipment,
                    { _id: salesShipmentCreated.documentsCreated[0]._id },
                );
                const shipmentInternalNote = (await salesShipment.internalNote).toString();
                const shipmentExternalNote = (await salesShipment.externalNote).toString();
                const shipmentIsExternalNote = await salesShipment.isExternalNote;
                const shipmentisTransferHeaderNote = await salesShipment.isTransferHeaderNote;
                const shipmentisTransferLineNote = await salesShipment.isTransferLineNote;

                assert.isEmpty(shipmentInternalNote);
                assert.isEmpty(shipmentExternalNote);
                assert.isFalse(shipmentIsExternalNote);
                assert.isFalse(shipmentisTransferHeaderNote);
                assert.isFalse(shipmentisTransferLineNote);

                // Header
                // Notes on shipment must not be equal on order
                assert.notEqual(shipmentInternalNote, (await salesOrder.internalNote).toString());
                assert.notEqual(shipmentExternalNote, (await salesOrder.externalNote).toString());
                assert.notEqual(shipmentIsExternalNote, await salesOrder.isExternalNote);

                // Setup on shipment must be equal on order
                assert.equal(shipmentisTransferHeaderNote, await salesOrder.isTransferHeaderNote);
                assert.equal(shipmentisTransferLineNote, await salesOrder.isTransferLineNote);

                // Lines
                const salesShipmentLine = (
                    await context
                        .query(xtremSales.nodes.SalesShipmentLine, {
                            filter: {
                                document: { _id: salesShipment._id },
                            },
                            last: 1,
                        })
                        .toArray()
                )[0];

                const lineInternalNote = (await salesShipmentLine.internalNote).toString();
                const lineExternalNote = (await salesShipmentLine.externalNote).toString();
                const lineIsExternalNote = await salesShipmentLine.isExternalNote;

                assert.isEmpty(lineInternalNote);
                assert.isEmpty(lineExternalNote);
                assert.isFalse(lineIsExternalNote);

                // Notes on shipment must not be equal on order
                assert.notEqual(lineInternalNote, (await salesOrderLine.internalNote).toString());
                assert.notEqual(lineExternalNote, (await salesOrderLine.externalNote).toString());
                assert.notEqual(lineIsExternalNote, await salesOrderLine.isExternalNote);
            }
        }));
});
