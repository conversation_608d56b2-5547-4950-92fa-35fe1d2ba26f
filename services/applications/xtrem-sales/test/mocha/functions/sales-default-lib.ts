// tslint:disable:no-duplicate-string
import { Test } from '@sage/xtrem-core';
import * as xtremSystem from '@sage/xtrem-system';
import { assert } from 'chai';
import * as xtremSales from '../../../index';

describe('SalesDefaultLib', () => {
    before(() => {});
    it('getStockSiteDefaultValue isInventory true', () =>
        Test.withContext(async context => {
            const resultSite = await context.read(xtremSystem.nodes.Site, { id: 'US011' });
            const salesOrder = await context.read(xtremSales.nodes.SalesOrder, { _id: '#SO1' }, { forUpdate: true });
            await salesOrder.$.set({ site: resultSite });
            const result = await xtremSales.functions.getStockSiteDefaultValue(salesOrder);

            assert.equal(result, resultSite);
        }));
});
