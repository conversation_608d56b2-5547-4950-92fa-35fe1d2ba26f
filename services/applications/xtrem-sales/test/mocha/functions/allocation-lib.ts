// tslint:disable:no-duplicate-string
import { Test } from '@sage/xtrem-core';
import { assert } from 'chai';
import * as xtremSales from '../../../index';

describe('AllocationLib', () => {
    before(() => {});
    it('salesConvertFilterToMassProcessCriteria return the expected object - All criteria given', () =>
        Test.withContext(() => {
            const filterInput = {
                item: {
                    name: {
                        _gte: 'A bottle of milk',
                        _lte: 'Triisopropanolamine',
                    },
                },
                document: {
                    stockSite: {
                        id: 'US001',
                    },
                    incoterm: {
                        id: 'DAP',
                    },
                    deliveryMode: {
                        id: 'ROAD',
                    },
                    shippingDate: {
                        _lte: '2023-10-25',
                    },
                    number: {
                        _gte: 'SO1',
                        _lte: 'SO5',
                    },
                    soldToCustomer: {
                        businessEntity: {
                            name: {
                                _gte: 'Siège social S01 PARIS',
                                _lte: 'Universal Alarms',
                            },
                        },
                    },
                },
            };

            const criteriaOutput = {
                stockSite: 'US001',
                fromOrderNumber: 'SO1',
                toOrderNumber: 'SO5',
                fromSoldToCustomer: 'Siège social S01 PARIS',
                toSoldToCustomer: 'Universal Alarms',
                incoterm: 'DAP',
                deliveryMode: 'ROAD',
                maximumShippingDate: '2023-10-25',
            };

            const result = xtremSales.functions.allocationLib.salesConvertFilterToMassProcessCriteria(filterInput);

            assert.deepEqual(result, criteriaOutput);
        }));

    it('salesConvertFilterToMassProcessCriteria return the expected object - Only site criteria given', () =>
        Test.withContext(() => {
            const filterInput = {
                document: {
                    stockSite: {
                        id: 'US001',
                    },
                },
            };

            const criteriaOutput = {
                stockSite: 'US001',
            };

            const result = xtremSales.functions.allocationLib.salesConvertFilterToMassProcessCriteria(filterInput);

            assert.deepEqual(result, criteriaOutput);
        }));

    it('salesConvertFilterToMassProcessCriteria return the expected object - Mixed criteria given', () =>
        Test.withContext(() => {
            const filterInput = {
                item: {
                    name: {
                        _gte: 'A bottle of milk',
                        _lte: 'Triisopropanolamine',
                    },
                },
                document: {
                    stockSite: {
                        id: 'US001',
                    },
                    shippingDate: {
                        _lte: '2023-10-25',
                    },
                    number: {
                        _gte: 'SO1',
                        _lte: 'SO5',
                    },
                },
            };

            const criteriaOutput = {
                stockSite: 'US001',
                fromOrderNumber: 'SO1',
                toOrderNumber: 'SO5',
                maximumShippingDate: '2023-10-25',
            };

            const result = xtremSales.functions.allocationLib.salesConvertFilterToMassProcessCriteria(filterInput);

            assert.deepEqual(result, criteriaOutput);
        }));
});
