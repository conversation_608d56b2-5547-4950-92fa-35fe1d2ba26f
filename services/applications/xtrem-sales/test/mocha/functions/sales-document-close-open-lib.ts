// tslint:disable:no-duplicate-string
import { date, Test } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremStockData from '@sage/xtrem-stock-data';
import * as xtremSystem from '@sage/xtrem-system';
import { assert } from 'chai';
import * as xtremSales from '../../../index';

describe('SalesOrderLib', () => {
    before(() => {});
    it('SalesOrderLineOpenStatus sales order already opened', () =>
        Test.withContext(async context => {
            const salesOrderLine = await context.read(
                xtremSales.nodes.SalesOrderLine,
                { _id: '#SO1|60100' },
                { forUpdate: true },
            );
            const result = await xtremSales.functions.setSalesDocumentLineOpenStatus<xtremSales.nodes.SalesOrderLine>(
                context,
                salesOrderLine,
                xtremSales.nodes.SalesOrder,
                xtremSales.enums.salesOrderLineOpenStatusMethodReturnDataType,
                xtremSales.nodes.SalesOrderLineToSalesShipmentLine,
            );

            assert.equal(result, 'salesOrderLineIsAlreadyOpen');
        }));
    it('SalesOrderLineOpenStatus sales order line is now open', () =>
        Test.withContext(async context => {
            const salesOrderLine = await context.read(
                xtremSales.nodes.SalesOrderLine,
                { _id: '#SO15|61900' },
                { forUpdate: true },
            );
            const result = await xtremSales.functions.setSalesDocumentLineOpenStatus<xtremSales.nodes.SalesOrderLine>(
                context,
                salesOrderLine,
                xtremSales.nodes.SalesOrder,
                xtremSales.enums.salesOrderLineOpenStatusMethodReturnDataType,
                xtremSales.nodes.SalesOrderLineToSalesShipmentLine,
                2,
            );
            assert.equal(result, 'salesOrderLineIsNowOpen');
        }));

    it('SalesOrderLineOpenStatus sales order line in closed status', () =>
        Test.withContext(async context => {
            const salesOrder = await context.read(xtremSales.nodes.SalesOrder, { _id: '#SO15' }, { forUpdate: true });
            await salesOrder.$.set({ status: 'closed' });
            const result = await xtremSales.functions.setSalesDocumentLineOpenStatus<xtremSales.nodes.SalesOrderLine>(
                context,
                await salesOrder.lines.elementAt(0),
                xtremSales.nodes.SalesOrder,
                xtremSales.enums.salesOrderLineOpenStatusMethodReturnDataType,
                xtremSales.nodes.SalesOrderLineToSalesShipmentLine,
                2,
            );
            assert.equal(result, 'salesOrderLineIsNowOpen');
        }));

    it('SalesOrderLineOpenStatus sales order in inProgress', () =>
        Test.withContext(async context => {
            const salesOrderLine = await context.read(
                xtremSales.nodes.SalesOrderLine,
                { _id: '#SO19|62400' },
                { forUpdate: true },
            );
            const result = await xtremSales.functions.setSalesDocumentLineOpenStatus<xtremSales.nodes.SalesOrderLine>(
                context,
                salesOrderLine,
                xtremSales.nodes.SalesOrder,
                xtremSales.enums.salesOrderLineOpenStatusMethodReturnDataType,
                xtremSales.nodes.SalesOrderLineToSalesShipmentLine,
                2,
            );
            assert.equal(result, 'salesOrderLineIsNowOpen');
        }));

    it('SalesOrderLineOpenStatus sales order in inProgress 2', () =>
        Test.withContext(async context => {
            const salesOrderLine = await context.read(
                xtremSales.nodes.SalesOrderLine,
                { _id: '#SO19|62500' },
                { forUpdate: true },
            );
            const result = await xtremSales.functions.setSalesDocumentLineOpenStatus<xtremSales.nodes.SalesOrderLine>(
                context,
                salesOrderLine,
                xtremSales.nodes.SalesOrder,
                xtremSales.enums.salesOrderLineOpenStatusMethodReturnDataType,
                xtremSales.nodes.SalesOrderLineToSalesShipmentLine,
                2,
            );
            assert.equal(result, 'salesOrderLineIsNowOpen');
        }));

    it.skip('SalesOrderLineCloseStatus sales order line already closed', () =>
        Test.withContext(async context => {
            const salesOrderLine = await context.read(
                xtremSales.nodes.SalesOrderLine,
                { _id: '#SO4|60300' },
                { forUpdate: true },
            );
            const result = (await xtremSales.functions.setSalesDocumentLineCloseStatus<xtremSales.nodes.SalesOrderLine>(
                context,
                salesOrderLine,
                xtremSales.nodes.SalesOrder,
                xtremSales.enums.salesOrderLineCloseStatusMethodReturnDataType,
            )) as xtremSales.enums.SalesOrderLineCloseStatusMethodReturn;
            assert.equal(result, 'salesOrderLineIsAlreadyClosed');
        }));

    it('SalesOrderOpenStatus sales order line status already closed', () =>
        Test.withContext(async context => {
            const result = await xtremSales.functions.setSalesDocumentOpenStatus(
                context,
                'SO13',
                xtremSales.nodes.SalesOrder,
                xtremSales.enums.salesOrderOpenStatusMethodReturnDataType,
                xtremSales.nodes.SalesOrderLineToSalesShipmentLine,
            );
            assert.equal(result, 'salesOrderIsNowOpen');
        }));

    it('SalesOrderOpenStatus sales order is shipped', () =>
        Test.withContext(async context => {
            const result = await xtremSales.functions.setSalesDocumentOpenStatus(
                context,
                'SO16',
                xtremSales.nodes.SalesOrder,
                xtremSales.enums.salesOrderOpenStatusMethodReturnDataType,
                xtremSales.nodes.SalesOrderLineToSalesShipmentLine,
            );
            assert.equal(result, 'salesOrderIsShipped');
        }));

    it('SalesOrderOpenStatus sales order is now open', () =>
        Test.withContext(async context => {
            const result = await xtremSales.functions.setSalesDocumentOpenStatus(
                context,
                'SO13',
                xtremSales.nodes.SalesOrder,
                xtremSales.enums.salesOrderOpenStatusMethodReturnDataType,
                xtremSales.nodes.SalesOrderLineToSalesShipmentLine,
            );
            assert.equal(result, 'salesOrderIsNowOpen');
        }));

    it('getItemSiteAvailableStockQuantity', () =>
        Test.withContext(async context => {
            const item = await context.read(xtremMasterData.nodes.Item, { _id: '#Chemical C' });
            const site = await context.read(xtremSystem.nodes.Site, { id: 'US001' });
            const result = await xtremSales.functions.getItemSiteAvailableStockQuantity(context, item, site);
            assert.equal(result, 100000);
        }));

    it('getItemSiteAvailableStockQuantity empty item _id', () =>
        Test.withContext(async context => {
            const site = await context.read(xtremSystem.nodes.Site, { id: 'US001' });
            const result = await xtremSales.functions.getItemSiteAvailableStockQuantity(context, {} as any, site);
            assert.equal(result, 0);
        }));

    it('getItemSiteAvailableStockQuantity itemSite not exist', () =>
        Test.withContext(async context => {
            const item = await context.read(xtremMasterData.nodes.Item, { _id: '#Chemical B' });
            const site = await context.read(xtremSystem.nodes.Site, { id: 'US004' });
            const result = await xtremSales.functions.getItemSiteAvailableStockQuantity(context, item, site);
            assert.equal(result, 0);
        }));
});

describe('SalesReturnRequestLib', () => {
    before(() => {});
    it('SalesReturnRequestLineOpenStatus sales return request line is now open', () =>
        Test.withContext(async context => {
            const salesReturnRequestLine = await context.read(
                xtremSales.nodes.SalesReturnRequestLine,
                { _id: '#SRR10|10' },
                { forUpdate: true },
            );
            const result =
                await xtremSales.functions.setSalesDocumentLineOpenStatus<xtremSales.nodes.SalesReturnRequestLine>(
                    context,
                    salesReturnRequestLine,
                    xtremSales.nodes.SalesReturnRequest,
                    xtremSales.enums.salesReturnRequestLineOpenStatusMethodReturnDataType,
                    xtremSales.nodes.SalesReturnRequestLineToSalesReturnReceiptLine,
                );
            assert.equal(result, 'salesReturnRequestLineIsNowOpen');
        }));

    // UPDATED in XT-10866
    // skip this test for now but will be fixed on PR2
    it('XT-10871 Unable to close a partially received return request', () =>
        Test.withContext(
            async context => {
                let salesShipment: xtremSales.nodes.SalesShipment = await context.read(
                    xtremSales.nodes.SalesShipment,
                    { _id: '6' },
                    { forUpdate: true },
                );

                const line1 = await salesShipment.lines.elementAt(0);
                const line2 = await salesShipment.lines.elementAt(1);

                await line1.$.set({ status: 'shipped' });
                await line2.$.set({ status: 'shipped' });

                await salesShipment.$.save();

                const { documentsCreated } = await (
                    await new xtremSales.classes.SalesReturnRequestCreator(context).prepareNodeCreateData([
                        { salesDocumentLine: line1, quantityToProcess: 20 },
                        { salesDocumentLine: line2, quantityToProcess: 30 },
                    ])
                ).createSalesOutputDocuments();

                let salesReturnRequest = await context.read(
                    xtremSales.nodes.SalesReturnRequest,
                    { _id: documentsCreated?.[0]?._id },
                    { forUpdate: true },
                );
                await salesReturnRequest.$.set({
                    approvalStatus: 'pendingApproval',
                });

                await salesReturnRequest.$.save();

                await xtremSales.nodes.SalesReturnRequest.approve(context, salesReturnRequest);

                const salesReturnRequestId = salesReturnRequest._id;
                const salesShipmentId = salesShipment._id;

                // rollbackCache function will flush deferred actions and saves
                await Test.rollbackCache(context);

                salesShipment = await context.read(xtremSales.nodes.SalesShipment, { _id: salesShipmentId });
                salesReturnRequest = await context.read(xtremSales.nodes.SalesReturnRequest, {
                    _id: salesReturnRequestId,
                });

                let site = await context.read(xtremSystem.nodes.Site, { id: 'US001' });
                let item = await context.read(
                    xtremMasterData.nodes.Item,
                    { id: 'LOT_TEST1' },
                    {
                        forUpdate: true,
                    },
                );
                await item.$.set({ isSold: true });
                await item.$.save();

                let receipt = await context.create(xtremSales.nodes.SalesReturnReceipt, {
                    number: '',
                    effectiveDate: date.today(),
                    site: site._id,
                    shipToCustomer: '#US019',
                    lines: [
                        {
                            item,
                            quantityInStockUnit: 30,
                            quantity: 30,
                            toReturnRequestLines: [
                                {
                                    linkedDocument: await salesReturnRequest.lines.at(1),
                                    quantity: 30,
                                    amount: 2,
                                    quantityInStockUnit: 30,
                                },
                            ],
                            stockDetails: [
                                {
                                    site,
                                    item,
                                    location: await context.read(xtremMasterData.nodes.Location, { id: 'LOC3' }),
                                    lotCreateData: { expirationDate: date.today(), item },
                                    status: await context.read(xtremStockData.nodes.StockStatus, { id: 'A' }),
                                    quantityInStockUnit: 30,
                                },
                            ],
                        },
                    ],
                });

                await receipt.$.save();

                const receiptId = receipt._id;
                const siteId = await site.id;
                const itemId = item._id;
                await Test.rollbackCache(context);

                receipt = await context.read(xtremSales.nodes.SalesReturnReceipt, { _id: receiptId });
                salesReturnRequest = await context.read(xtremSales.nodes.SalesReturnRequest, {
                    _id: salesReturnRequestId,
                });

                await xtremSales.nodes.SalesReturnReceipt.postToStock(context, [receipt._id]);
                await xtremStockData.functions.testLib.simulateStockTransaction(
                    context,
                    receipt._id,
                    xtremSales.nodes.SalesReturnReceipt,
                    'receipt',
                    xtremSales.nodes.SalesReturnReceipt.onStockReply,
                );

                const receiptSalesReturnRequests = receipt.__salesReturnRequests;
                const receiptSalesShipments = receipt.__salesShipments;
                await Test.rollbackCache(context);

                receipt = await context.read(xtremSales.nodes.SalesReturnReceipt, { _id: receiptId });
                receipt.__salesReturnRequests = receiptSalesReturnRequests;
                receipt.__salesShipments = receiptSalesShipments;

                salesReturnRequest = await context.read(xtremSales.nodes.SalesReturnRequest, {
                    _id: salesReturnRequestId,
                });

                const result =
                    (await xtremSales.functions.setSalesDocumentLineCloseStatus<xtremSales.nodes.SalesReturnRequestLine>(
                        context,
                        await salesReturnRequest.lines.elementAt(0),
                        xtremSales.nodes.SalesReturnRequest,
                        xtremSales.enums.salesReturnRequestLineCloseStatusMethodReturnDataType,
                    )) as xtremSales.enums.SalesReturnRequestLineCloseStatusMethodReturn;

                assert.equal(result, 'salesReturnRequestLineIsNowClosed');

                await Test.rollbackCache(context);

                site = await context.read(xtremSystem.nodes.Site, { id: siteId });
                item = await context.read(xtremMasterData.nodes.Item, { _id: itemId });

                salesReturnRequest = await context.read(xtremSales.nodes.SalesReturnRequest, {
                    _id: salesReturnRequestId,
                });
                assert.equal(await (await salesReturnRequest.lines.elementAt(0)).status, 'closed');
                assert.equal(await (await salesReturnRequest.lines.elementAt(1)).status, 'closed');
                assert.equal(await salesReturnRequest.status, 'closed');

                const result2 = (await xtremSales.functions.setSalesDocumentOpenStatus(
                    context,
                    await salesReturnRequest.number,
                    xtremSales.nodes.SalesReturnRequest,
                    xtremSales.enums.salesReturnRequestOpenStatusMethodReturnDataType,
                    xtremSales.nodes.SalesReturnRequestLineToSalesReturnReceiptLine,
                )) as xtremSales.enums.SalesReturnRequestOpenStatusMethodReturn;
                assert.equal(result2, 'salesReturnRequestIsNowOpen');

                salesReturnRequest = await context.read(xtremSales.nodes.SalesReturnRequest, {
                    _id: salesReturnRequest._id,
                });
                assert.equal(await salesReturnRequest.status, 'inProgress');
                assert.equal(await (await salesReturnRequest.lines.elementAt(0)).status, 'pending');
                assert.equal(await (await salesReturnRequest.lines.elementAt(1)).status, 'closed');

                await xtremSales.functions.setSalesDocumentCloseStatus(
                    context,
                    await salesReturnRequest.number,
                    xtremSales.nodes.SalesReturnRequest,
                    xtremSales.enums.salesReturnRequestCloseStatusMethodReturnDataType,
                );

                salesReturnRequest = await context.read(xtremSales.nodes.SalesReturnRequest, {
                    _id: salesReturnRequest._id,
                });
                assert.equal(await salesReturnRequest.status, 'closed');
                assert.equal(await (await salesReturnRequest.lines.elementAt(0)).status, 'closed');
                assert.equal(await (await salesReturnRequest.lines.elementAt(1)).status, 'closed');

                await xtremSales.functions.setSalesDocumentOpenStatus(
                    context,
                    await salesReturnRequest.number,
                    xtremSales.nodes.SalesReturnRequest,
                    xtremSales.enums.salesReturnRequestOpenStatusMethodReturnDataType,
                    xtremSales.nodes.SalesReturnRequestLineToSalesReturnReceiptLine,
                );

                const receipt2 = await context.create(xtremSales.nodes.SalesReturnReceipt, {
                    number: '',
                    effectiveDate: date.today(),
                    site: site._id,
                    shipToCustomer: '#US019',
                    lines: [
                        {
                            item,
                            quantityInStockUnit: 15,
                            quantity: 15,
                            toReturnRequestLines: [
                                {
                                    linkedDocument: await salesReturnRequest.lines.at(0),
                                    quantity: 15,
                                    amount: 2,
                                    quantityInStockUnit: 15,
                                },
                            ],
                            stockDetails: [
                                {
                                    site,
                                    item,
                                    location: await context.read(xtremMasterData.nodes.Location, { id: 'LOC3' }),
                                    lotCreateData: { expirationDate: date.today(), item },
                                    status: await context.read(xtremStockData.nodes.StockStatus, { id: 'A' }),
                                    quantityInStockUnit: 15,
                                },
                            ],
                        },
                    ],
                });

                await receipt2.$.save();

                salesReturnRequest = await context.read(xtremSales.nodes.SalesReturnRequest, {
                    _id: salesReturnRequest._id,
                });
                assert.equal(await salesReturnRequest.status, 'inProgress');
                assert.equal(await (await salesReturnRequest.lines.elementAt(0)).status, 'inProgress');
                assert.equal(await (await salesReturnRequest.lines.elementAt(1)).status, 'closed');
            },
            { user: { email: '<EMAIL>' } },
        ));
});
