import { Test } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremSystem from '@sage/xtrem-system';
import { assert } from 'chai';
import * as xtremSales from '../../../index';

describe('Sales common functions - doSalesDocumentsExist', () => {
    it('Check for sales orders and invoices', () =>
        Test.withContext(async context => {
            // Item-site with sales orders
            let item = await context.read(xtremMasterData.nodes.Item, { _id: '#Chemical D' });
            let site = await context.read(xtremSystem.nodes.Site, { id: 'US001' });
            let result = await xtremSales.functions.doSalesDocumentsExist(context, item, site);
            assert.deepEqual(result, true);

            // Item-site with sales invoices without orders
            item = await context.read(xtremMasterData.nodes.Item, { _id: '#SalesItem81' });
            result = await xtremSales.functions.doSalesDocumentsExist(context, item, site);
            assert.deepEqual(result, true);

            // Item-site with neither sales order nor invoice
            site = await context.read(xtremSystem.nodes.Site, { id: 'DEP1-S01' });
            result = await xtremSales.functions.doSalesDocumentsExist(context, item, site);
            assert.deepEqual(result, false);
        }));
});
