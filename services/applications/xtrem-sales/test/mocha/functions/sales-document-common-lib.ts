import type { Context } from '@sage/xtrem-core';
import { Test } from '@sage/xtrem-core';
import * as xtremFinanceData from '@sage/xtrem-finance-data';
import { assert } from 'chai';
import * as xtremSales from '../../../lib';

describe('Sales document common Lib', () => {
    async function assertErrorPrinting(
        context: Context,
        args: { salesDocument: xtremSales.nodes.SalesInvoice; thrownMessage: string },
    ) {
        await assert.isRejected(
            xtremSales.functions.checkIfCanPrintSalesInvoice(context, args.salesDocument, true),
            args.thrownMessage,
        );
    }

    async function assertPrinting(
        context: Context,
        args: {
            salesDocument: xtremSales.nodes.SalesInvoice;
            canPrint: boolean;
            enablePrintButton: boolean;
            thrownMessage?: string;
        },
    ) {
        const { canPrint, enablePrintButton } = await xtremSales.functions.checkIfCanPrintSalesInvoice(
            context,
            args.salesDocument,
            false,
        );
        assert.deepEqual(canPrint, args.canPrint);
        assert.deepEqual(enablePrintButton, args.enablePrintButton);
    }

    it('checkIfCanPrintSalesInvoice', () =>
        Test.withContext(async context => {
            const invoice = await context
                .query(xtremSales.nodes.SalesInvoice, { filter: { number: 'SI11' }, forUpdate: true })
                .elementAt(0);

            // Check existing data before
            assert.deepEqual(await invoice.status, 'posted');
            assert.deepEqual(await invoice.taxCalculationStatus, 'done');
            assert.deepEqual(await invoice.isPrinted, false);
            assert.deepEqual(await invoice.financeTransactions.length, 1);
            assert.deepEqual(await (await invoice.financeTransactions.elementAt(0)).status, 'pending');

            await assertPrinting(context, { salesDocument: invoice, canPrint: true, enablePrintButton: true });

            await invoice.$.set({ status: 'draft' });
            await assertPrinting(context, { salesDocument: invoice, canPrint: true, enablePrintButton: true });

            await invoice.$.set({ status: 'error' });
            await assertPrinting(context, { salesDocument: invoice, canPrint: false, enablePrintButton: true });
            await assertErrorPrinting(context, {
                salesDocument: invoice,
                thrownMessage: 'You need to resolve errors to print the document: SI11.',
            });

            await invoice.$.set({ status: 'draft', taxCalculationStatus: 'failed' });
            await assertPrinting(context, { salesDocument: invoice, canPrint: false, enablePrintButton: true });
            await assertErrorPrinting(context, {
                salesDocument: invoice,
                thrownMessage: 'You need to resolve tax calculation issues to print the document: SI11.',
            });

            await invoice.$.set({ status: 'inProgress' }); // status=inProgress + taxCalculationStatus=failed is not a normal business case
            await assertPrinting(context, { salesDocument: invoice, canPrint: false, enablePrintButton: false });
            await assertErrorPrinting(context, {
                salesDocument: invoice,
                thrownMessage:
                    'Cannot print the document. Document details: {"number":"SI11","status":"inProgress","taxCalculationStatus":"failed","displayStatus":"taxCalculationFailed"}',
            });

            await invoice.$.set({ status: 'inProgress', taxCalculationStatus: 'done' });
            await assertPrinting(context, { salesDocument: invoice, canPrint: false, enablePrintButton: false });
            await assertErrorPrinting(context, {
                salesDocument: invoice,
                thrownMessage: 'You cannot print the document. It is set to In progress: SI11.',
            });

            const financeTransaction = await context.read(
                xtremFinanceData.nodes.FinanceTransaction,
                {
                    _id: (await invoice.financeTransactions.elementAt(0))._id,
                },
                { forUpdate: true },
            );
            await financeTransaction.$.set({ status: 'posted' });
            await financeTransaction.$.save();
            await assertPrinting(context, { salesDocument: invoice, canPrint: true, enablePrintButton: true });
        }));
});
