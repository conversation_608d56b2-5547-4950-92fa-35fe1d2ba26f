import type { Context, NodeCreateData } from '@sage/xtrem-core';
import { date, Test } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremSystem from '@sage/xtrem-system';
import { assert } from 'chai';
import * as xtremSales from '../../../index';

describe('SalesPriceLib', () => {
    before(() => {});

    function createSalesInvoice(context: Context, invoiceDate: date) {
        const baseData: NodeCreateData<xtremSales.nodes.SalesInvoice> = {
            billToContact: {
                title: 'ms',
                firstName: 'Jane',
                lastName: 'Doe',
                email: '<EMAIL>',
            },
            lines: [
                {
                    _action: 'create',
                    consumptionLinkedAddress: '#US019|1500',
                    consumptionAddress: {
                        country: '#US',
                        name: 'Cust address',
                        locationPhoneNumber: '+15550123456',
                        addressLine1: '1st Avenue',
                        addressLine2: undefined,
                        city: 'One',
                        region: 'AZ',
                        postcode: '85034',
                    },
                    item: '#Consulting01',
                    itemDescription: 'Consulting service',
                    origin: 'direct',
                    providerSite: '#US001',
                    quantity: 11,
                    quantityInStockUnit: 11,
                    site: '#US001',
                    unit: '#LITER',
                    unitToStockUnitConversionFactor: 1,
                    stockUnit: '#LITER',
                },
            ],
            billToAddress: {
                country: '#US',
                name: 'Cust address',
                locationPhoneNumber: '+15550123456',
                addressLine1: '1st Avenue',
                addressLine2: undefined,
                city: 'One',
                region: 'AZ',
                postcode: '85034',
            },
            billToLinkedAddress: '#US019|1500',
            currency: '#USD',
            incoterm: null,
            paymentTerm: '#DUE_UPON_RECEIPT_ALL',
            billToCustomer: '#US019',
            site: '#US001',
            number: undefined,
            date: invoiceDate,
            dueDate: invoiceDate,
            creationNumber: undefined,
            isPrinted: false,
            isSent: false,
            status: 'draft',
            creditStatus: 'notCredited',
        };
        return context.create(xtremSales.nodes.SalesInvoice, baseData);
    }

    it(// TODO: can't execute this as there is not taxDetermination rule with the new US tax solution for the moment
    'Sales invoice creation automatic sales price calculation', () =>
        Test.withContext(
            async context => {
                const company = await context.tryRead(xtremSystem.nodes.Company, { _id: '1' }, { forUpdate: true });

                await company!.$.set({ taxEngine: 'genericTaxCalculation' });

                await company!.$.save();

                const item = await context.tryRead(
                    xtremMasterData.nodes.Item,
                    { _id: '#Consulting01' },
                    { forUpdate: true },
                );

                await item!.$.set({
                    category: '#CHEMICAL',
                    currency: '#USD',
                    salesUnit: '#LITER',
                    stockUnit: '#LITER',
                    maximumSalesQuantity: 14000,
                });

                await item!.$.save();

                const salesInvoice: xtremSales.nodes.SalesInvoice = await createSalesInvoice(
                    context,
                    date.make(2022, 2, 1),
                );
                await salesInvoice.$.save();

                assert.equal(String(await (await salesInvoice.lines.at(0))?.grossPrice), '100');
                assert.equal(String(await (await salesInvoice.taxes.at(0))?.taxAmount), '23.1');
                assert.equal(
                    String(await (await (await salesInvoice.taxes.at(0))?.taxReference)?.name),
                    'State reduced rate',
                );

                assert.equal(String(await (await salesInvoice.lines.at(0))?.priceReason), 'null');
                assert.equal(String(await (await salesInvoice.lines.at(0))?.priceOrigin), 'basePrice');
            },
            { today: '2022-02-01' },
        ));
    it('Sales invoice creation automatic sales price calculation 2', () =>
        Test.withContext(
            async context => {
                const company = await context.tryRead(
                    xtremSystem.nodes.Company,
                    { _id: '#US001' },
                    { forUpdate: true },
                );

                await company!.$.set({ taxEngine: 'genericTaxCalculation' });

                await company!.$.save();

                const item = await context.tryRead(
                    xtremMasterData.nodes.Item,
                    { _id: '#Consulting01' },
                    { forUpdate: true },
                );

                await item!.$.set({
                    category: '#CHEMICAL',
                    currency: '#USD',
                    salesUnit: '#LITER',
                    stockUnit: '#LITER',
                    maximumSalesQuantity: 14000,
                });

                await item!.$.save();

                const itemCustomerPrice = await context.create(xtremMasterData.nodes.ItemCustomerPrice, {
                    priceReason: '#CUSTOMER_PRICING',
                    unit: '#LITER',
                    price: 33.33,
                    discount: 20,
                    item: '#Consulting01',
                    currency: '#USD',
                });

                await itemCustomerPrice.$.save();

                const salesInvoice: xtremSales.nodes.SalesInvoice = await createSalesInvoice(
                    context,
                    date.make(2022, 2, 1),
                );
                await salesInvoice.$.save();

                assert.equal(String(await (await salesInvoice.lines.at(0))?.grossPrice), '33.33');
                assert.equal(String(await (await salesInvoice.lines.at(0))?.netPrice), '26.66');
                assert.equal(String(await (await salesInvoice.taxes.at(0))?.taxAmount), '7.7');
                assert.equal(
                    String(await (await (await salesInvoice.taxes.at(0))?.taxReference)?.name),
                    'State reduced rate',
                );

                assert.equal(
                    String(await (await (await salesInvoice.lines.at(0))?.priceReason)?.description),
                    'Customer pricing',
                );
                assert.equal(String(await (await salesInvoice.lines.at(0))?.priceOrigin), 'priceList');
            },
            { today: '2022-02-01' },
        ));
});
