import { Test } from '@sage/xtrem-core';
import type * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremSystem from '@sage/xtrem-system';
import { assert } from 'chai';
import * as xtremSales from '../../../index';
import { createReturnRequest } from '../../fixtures/lib/functions/sales-return-request';

describe('Sales-return-request-approval', () => {
    it('Sales return request approval with user given as a parameter', () =>
        Test.withContext(async context => {
            const myReturnRequest = await createReturnRequest(context);

            const user: xtremSystem.nodes.User = await context.read(xtremSystem.nodes.User, {
                _id: '#<EMAIL>',
            });
            const defaultApprover: xtremSystem.nodes.User = await context.read(xtremSystem.nodes.User, {
                _id: '#<EMAIL>',
            });
            const substituteApproverApprover: xtremSystem.nodes.User = await context.read(xtremSystem.nodes.User, {
                _id: '#<EMAIL>',
            });

            // set default and substitute approver on site
            const site: xtremSystem.nodes.Site = await context.read(
                xtremSystem.nodes.Site,
                { id: 'US001' },
                { forUpdate: true },
            );
            await site.$.set({
                salesReturnRequestDefaultApprover: defaultApprover,
                salesReturnRequestSubstituteApprover: substituteApproverApprover,
            });
            await site.$.save();

            const result = await myReturnRequest.beforeSendApprovalRequestMail(
                context,
                user, // user given as a parameter
            );
            const expectedResult: xtremMasterData.interfaces.ApprovalRequestMail = {
                subject: '[Sales return request ST200001] approval request',
                template: 'sales_return_request_approval_mail',
                data: {
                    date: '2020-08-10',
                    number: 'ST200001',
                    requester: 'Test Unit',
                    site: 'Chem. Atlanta',
                    urlApprovalDocument: '@sage/xtrem-sales/SalesReturnRequest/eyJfaWQiOiIxNjMifQ==',
                    urlApprove: '@sage/xtrem-sales/SalesReturnRequest/eyJfaWQiOiIxNjMiLCJhY3Rpb24iOiJhcHByb3ZlZCJ9',
                    urlReject: '@sage/xtrem-sales/SalesReturnRequest/eyJfaWQiOiIxNjMiLCJhY3Rpb24iOiJyZWplY3RlZCJ9',
                },
                mailerUser: user,
            };
            assert.deepEqual(result, expectedResult);
        }));

    it('Sales return request approval with defaultApprover on site', () =>
        Test.withContext(async context => {
            const myReturnRequest = await createReturnRequest(context);

            const defaultApprover: xtremSystem.nodes.User = await context.read(xtremSystem.nodes.User, {
                _id: '#<EMAIL>',
            });
            const substituteApproverApprover: xtremSystem.nodes.User = await context.read(xtremSystem.nodes.User, {
                _id: '#<EMAIL>',
            });

            // set default and substitute approver on site
            const site: xtremSystem.nodes.Site = await context.read(
                xtremSystem.nodes.Site,
                { id: 'US001' },
                { forUpdate: true },
            );
            await site.$.set({
                salesReturnRequestDefaultApprover: defaultApprover,
                salesReturnRequestSubstituteApprover: substituteApproverApprover,
            });
            await site.$.save();

            const result = await myReturnRequest.beforeSendApprovalRequestMail(
                context,
                {} as xtremSystem.nodes.User, // empty object to trigger the default mechanism
            );

            const expectedResult: xtremMasterData.interfaces.ApprovalRequestMail = {
                subject: '[Sales return request ST200001] approval request',
                template: 'sales_return_request_approval_mail',
                data: {
                    date: '2020-08-10',
                    number: 'ST200001',
                    requester: 'Test Unit',
                    site: 'Chem. Atlanta',
                    urlApprovalDocument: '@sage/xtrem-sales/SalesReturnRequest/eyJfaWQiOiIxNjMifQ==',
                    urlApprove: '@sage/xtrem-sales/SalesReturnRequest/eyJfaWQiOiIxNjMiLCJhY3Rpb24iOiJhcHByb3ZlZCJ9',
                    urlReject: '@sage/xtrem-sales/SalesReturnRequest/eyJfaWQiOiIxNjMiLCJhY3Rpb24iOiJyZWplY3RlZCJ9',
                },
                mailerUser: defaultApprover,
            };

            assert.deepEqual(result, expectedResult);
        }));

    it('Sales return request approval with substituteApprover on site', () =>
        Test.withContext(async context => {
            const myReturnRequest = await createReturnRequest(context);

            const substituteApproverApprover: xtremSystem.nodes.User = await context.read(xtremSystem.nodes.User, {
                _id: '#<EMAIL>',
            });

            // set only substitute approver on site
            const site: xtremSystem.nodes.Site = await context.read(
                xtremSystem.nodes.Site,
                { id: 'US001' },
                { forUpdate: true },
            );
            await site.$.set({
                salesReturnRequestSubstituteApprover: substituteApproverApprover,
            });
            await site.$.save();

            const result = await myReturnRequest.beforeSendApprovalRequestMail(
                context,
                {} as xtremSystem.nodes.User, // empty object to trigger the default mechanism
            );

            const expectedResult: xtremMasterData.interfaces.ApprovalRequestMail = {
                subject: '[Sales return request ST200001] approval request',
                template: 'sales_return_request_approval_mail',
                data: {
                    date: '2020-08-10',
                    number: 'ST200001',
                    requester: 'Test Unit',
                    site: 'Chem. Atlanta',
                    urlApprovalDocument: '@sage/xtrem-sales/SalesReturnRequest/eyJfaWQiOiIxNjMifQ==',
                    urlApprove: '@sage/xtrem-sales/SalesReturnRequest/eyJfaWQiOiIxNjMiLCJhY3Rpb24iOiJhcHByb3ZlZCJ9',
                    urlReject: '@sage/xtrem-sales/SalesReturnRequest/eyJfaWQiOiIxNjMiLCJhY3Rpb24iOiJyZWplY3RlZCJ9',
                },
                mailerUser: substituteApproverApprover,
            };

            assert.deepEqual(result, expectedResult);
        }));

    it('Submit approval fails 1 - sales return request wrong status', () =>
        Test.withContext(async context => {
            const myReturnRequest: xtremSales.nodes.SalesReturnRequest = await context.read(
                xtremSales.nodes.SalesReturnRequest,
                { number: 'SRR05' },
            );
            const user: xtremSystem.nodes.User = await context.read(xtremSystem.nodes.User, {
                _id: '#<EMAIL>',
            });

            await assert.isRejected(
                xtremSales.nodes.SalesReturnRequest.sendApprovalRequestMail(context, myReturnRequest, user),
                'Submission for approval action not allowed. The sales return request is not in draft status.',
            );
        }));

    it('Submit approval fails 2 - no default or substitute approver set', () =>
        Test.withContext(async context => {
            const myReturnRequest: xtremSales.nodes.SalesReturnRequest = await context.read(
                xtremSales.nodes.SalesReturnRequest,
                { number: 'SRR02' },
            );

            await assert.isRejected(
                xtremSales.nodes.SalesReturnRequest.sendApprovalRequestMail(
                    context,
                    myReturnRequest,
                    {} as xtremSystem.nodes.User, // empty object to trigger the default mechanism
                ),
                'There is no default or substitute approver for the current document.',
            );
        }));
});
