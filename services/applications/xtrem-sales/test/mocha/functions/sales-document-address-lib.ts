import { Test } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import { assert } from 'chai';
import * as xtremSales from '../../../lib';

describe('SalesDocumentAddressLib', () => {
    describe('getPrimaryAndActiveOfAnyCollection', () => {
        it('Should find primary item', () =>
            Test.withContext(async context => {
                const address = await context.read(xtremMasterData.nodes.BusinessEntityAddress, { _id: '#500|800' });
                const result = await xtremSales.functions.getPrimaryAndActiveOfAnyCollection(address.contacts);
                assert.equal(result, await address.contacts.at(0));
            }));
        it('Should not find primary item and take first entry', () =>
            Test.withContext(async context => {
                const contact = await context.read(
                    xtremMasterData.nodes.BusinessEntityContact,
                    { _id: '#500|100' },
                    { forUpdate: true },
                );
                await contact.$.set({ isPrimary: false });
                await contact.$.save();

                const address = await context.read(xtremMasterData.nodes.BusinessEntityAddress, { _id: '#500|800' });

                const result = await xtremSales.functions.getPrimaryAndActiveOfAnyCollection(address.contacts);
                assert.equal(result, await address.contacts.at(0));
            }));
        it('Should return null', () =>
            Test.withContext(async context => {
                const customer = await context.read(xtremMasterData.nodes.BusinessEntityAddress, {
                    _id: '#US020|1600',
                });
                const result = await xtremSales.functions.getPrimaryAndActiveOfAnyCollection(customer.contacts);
                assert.equal(result, null);
            }));
    });
});
