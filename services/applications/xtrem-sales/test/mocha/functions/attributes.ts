// tslint:disable:no-duplicate-string
import { Test } from '@sage/xtrem-core';
import * as xtremFinanceData from '@sage/xtrem-finance-data';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremSystem from '@sage/xtrem-system';
import { assert } from 'chai';
import * as xtremSales from '../../../index';

describe('computeAttributes', () => {
    it('checkAttributeTypeActive return undefined', () =>
        Test.withContext(async context => {
            const site = await context.read(xtremSystem.nodes.Site, { id: 'US001' });
            const secondSite = await context.read(xtremSystem.nodes.Site, { id: 'US001' });
            const item = await context.read(xtremMasterData.nodes.Item, { _id: '#Chemical A' });
            const billToCustomer = await context.read(xtremMasterData.nodes.Customer, { _id: '#US019' });

            await context.delete(xtremFinanceData.nodes.AttributeType, { id: 'businessSite' });
            await context.delete(xtremFinanceData.nodes.AccountAttributeType, { _id: '#1|TEST_US_DEFAULT|100' });
            await context.delete(xtremFinanceData.nodes.AccountAttributeType, { _id: '#2|TEST_US_DEFAULT|100' });

            const computedAttributes = await xtremSales.functions.computeAttributes(
                context,
                site,
                secondSite,
                item,
                billToCustomer,
            );
            assert.notEqual(computedAttributes, {});
        }));
    it('site isFinance false', () =>
        Test.withContext(async context => {
            const site = await context.read(xtremSystem.nodes.Site, { id: 'US004' }, { forUpdate: true });
            await site.$.set({ isFinance: false, financialSite: { id: 'US003' } });
            await site.$.save();

            const secondSite = await context.read(xtremSystem.nodes.Site, { id: 'US001' });
            const item = await context.read(xtremMasterData.nodes.Item, { _id: '#Chemical A' });
            const billToCustomer = await context.read(xtremMasterData.nodes.Customer, { _id: '#US019' });

            const computedAttributes = await xtremSales.functions.computeAttributes(
                context,
                site,
                secondSite,
                item,
                billToCustomer,
            );
            assert.notEqual(computedAttributes, {});
        }));
});
