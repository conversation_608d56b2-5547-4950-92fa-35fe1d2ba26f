import { Test } from '@sage/xtrem-core';
import * as xtremFinanceData from '@sage/xtrem-finance-data';
import * as xtremSales from '../../../index';

// Unit test cannot be done until we reimplement open item and make it a one node only (no abstract)
describe('BaseOpenItem node', () => {
    it.skip('Create base open item and check that it gets the property values from sales invoice', () =>
        Test.withContext(async context => {
            const salesInvoice = await context.read(xtremSales.nodes.SalesInvoice, { number: 'SI6' });

            const businessEntityPayment = await (await salesInvoice.billToCustomer).businessEntity;

            const newBaseOpenItem = await context.create(xtremFinanceData.nodes.BaseOpenItem, {
                dueDate: await salesInvoice.dueDate,
                businessEntity: businessEntityPayment,
                businessEntityPayment,
                type: 'customer',
                currency: await salesInvoice.currency,
                transactionAmountDue: await salesInvoice.totalAmountIncludingTax,
                transactionAmountPaid: 0,
                companyAmountDue: await salesInvoice.totalAmountIncludingTax,
                companyAmountPaid: 0,
                financialSiteAmountDue: await salesInvoice.totalAmountIncludingTax,
                financialSiteAmountPaid: 0,
                status: 'notPaid',
                documentSysId: salesInvoice._id,
                documentNumber: await salesInvoice.number,
                documentType: 'salesInvoice',
                // add the new discount properties
            });
            await newBaseOpenItem.$.save();
        }));
});
