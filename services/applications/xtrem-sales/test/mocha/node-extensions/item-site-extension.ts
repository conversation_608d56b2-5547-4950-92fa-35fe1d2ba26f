import type { Context, NodeCreateData } from '@sage/xtrem-core';
import { Test, date } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import { assert } from 'chai';
import * as xtremSales from '../../../index';

describe('Item site deletion', () => {
    before(() => {});

    function createSalesOrder(context: Context, item: string): Promise<xtremSales.nodes.SalesOrder> {
        const billToCustomer = '#US023';
        const site = '#US001';
        const orderDate = date.parse('2020-11-18', context.currentLocale as any);
        const doNotShipBeforeDate = date.parse('2020-11-12', context.currentLocale as any);
        const requestedDeliveryDate = date.parse('2020-11-20', context.currentLocale as any);

        const createData: NodeCreateData<xtremSales.nodes.SalesOrder> = {
            site,
            stockSite: site,
            billToCustomer,
            date: orderDate,
            expectedDeliveryDate: orderDate,
            shippingDate: orderDate,
            requestedDeliveryDate,
            doNotShipBeforeDate,
            doNotShipAfterDate: orderDate,
            soldToCustomer: billToCustomer,
            lines: [
                {
                    status: 'quote',
                    shippingStatus: 'notShipped',
                    item,
                    site,
                    stockSite: site,
                    quantity: 45,
                    grossPrice: 20.289,
                    expectedDeliveryDate: orderDate,
                    shippingDate: orderDate,
                    doNotShipBeforeDate,
                    doNotShipAfterDate: orderDate,
                },
            ],
        };
        return context.create(xtremSales.nodes.SalesOrder, createData);
    }

    it('ItemSite deletion with sales documents - should fail.', () =>
        Test.withContext(
            async context => {
                const itemId = '#5467';
                const item = await context.read(xtremMasterData.nodes.Item, { _id: itemId }, { forUpdate: true });
                await item.$.set({ isStockManaged: false });

                const salesOrder = await createSalesOrder(context, itemId);
                await salesOrder.$.save();

                const itemSite = await context.read(
                    xtremMasterData.nodes.ItemSite,
                    { _id: '#5467|US001' },
                    { forUpdate: true },
                );

                await assert.isRejected(itemSite.$.delete());
                assert.deepEqual(itemSite.$.context.diagnoses, [
                    {
                        severity: 3,
                        path: [],
                        message: 'A work in progress record with this item site exists. You cannot delete it.',
                    },
                    {
                        severity: 3,
                        path: [],
                        message: 'Delete not allowed. Sales documents exist for this item-site.',
                    },
                ]);
            },
            { today: '2020-11-18' },
        ));

    it('ItemSite deletion without sales documents - should succeed.', () =>
        Test.withContext(async context => {
            const item = await context.read(xtremMasterData.nodes.Item, { _id: '#5467' }, { forUpdate: true });
            await item.$.set({ isStockManaged: false });

            const itemSite = await context.read(
                xtremMasterData.nodes.ItemSite,
                { _id: '#CARROT|US001' },
                { forUpdate: true },
            );
            await itemSite.$.delete();
        }));
});
