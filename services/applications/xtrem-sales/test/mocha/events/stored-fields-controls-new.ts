// tslint:disable:no-duplicate-string
import { Test } from '@sage/xtrem-core';
import * as xtremFinanceData from '@sage/xtrem-finance-data';
import { assert } from 'chai';
import * as xtremSales from '../../../index';

describe.skip('stored-fields-controls: storedAttributes', () => {
    it('"Project" attribute type is not active. Update the project type', () =>
        Test.withCommittedContext(async context => {
            const employeeType = await context.read(
                xtremFinanceData.nodes.AttributeType,
                { id: 'employee' },
                { forUpdate: true },
            );
            console.log('Employee 0 isActive:', await employeeType.isActive);
            await employeeType.$.set({ isActive: true });
            await employeeType.$.save();

            const taskType = await context.read(
                xtremFinanceData.nodes.AttributeType,
                { id: 'task' },
                { forUpdate: true },
            );
            console.log('Task 0 isActive:', await taskType.isActive);
            await taskType.$.set({ isActive: false });
            await taskType.$.save();

            const projectType = await context.read(
                xtremFinanceData.nodes.AttributeType,
                { id: 'project' },
                { forUpdate: true },
            );
            console.log('Project 0 isActive:', await projectType.isActive);
            await projectType.$.set({ isActive: false });
            await projectType.$.save();
            const nodeCategory = context.getNodeCacheCategory(xtremFinanceData.nodes.AttributeType);
            console.log('NodeCategory:', nodeCategory);
            await context.invalidateCachedCategory(nodeCategory);

            console.log('Employee isActive:', await employeeType.isActive);
            console.log('Task isActive:', await taskType.isActive);
            console.log('Project isActive:', await projectType.isActive);
        }));
    it('"Project" attribute type is not active. Test the inactive project type', () =>
        Test.withContext(async context => {
            const nodeCategory = context.getNodeCacheCategory(xtremFinanceData.nodes.AttributeType);
            console.log('NodeCategory:', nodeCategory);
            await context.invalidateCachedCategory(nodeCategory);

            const projectType2 = await context.read(xtremFinanceData.nodes.AttributeType, { id: 'project' });
            const taskType2 = await context.read(xtremFinanceData.nodes.AttributeType, { id: 'task' });
            const employeeType2 = await context.read(xtremFinanceData.nodes.AttributeType, { id: 'employee' });
            console.log('Employee 2 isActive:', await employeeType2.isActive);
            console.log('Task 2 isActive:', await taskType2.isActive);
            console.log('Project 2 isActive:', await projectType2.isActive);

            const salesInvoiceLine = await context.read(
                xtremSales.nodes.SalesInvoiceLine,
                { _id: '#SI1|10' },
                { forUpdate: true },
            );
            await salesInvoiceLine.$.set({
                storedAttributes: { project: 'AttPROJ', task: '', employee: 'AttEMPL2' },
            });
            await assert.isRejected(salesInvoiceLine.$.save(), 'The record was not updated.');
        }));
    it('"Project" attribute type is not active. Revert the active flags', () =>
        Test.withCommittedContext(async context => {
            // Reset the data to how it was originally
            const employeeType = await context.read(
                xtremFinanceData.nodes.AttributeType,
                { id: 'employee' },
                { forUpdate: true },
            );
            await employeeType.$.set({ isActive: false });
            await employeeType.$.save();

            const projectType = await context.read(
                xtremFinanceData.nodes.AttributeType,
                { id: 'project' },
                { forUpdate: true },
            );
            await projectType.$.set({ isActive: true });
            await projectType.$.save();

            const taskType = await context.read(
                xtremFinanceData.nodes.AttributeType,
                { id: 'task' },
                { forUpdate: true },
            );
            await taskType.$.set({ isActive: true });
            await taskType.$.save();
            const nodeCategory = context.getNodeCacheCategory(xtremFinanceData.nodes.AttributeType);
            await context.invalidateCachedCategory(nodeCategory);
        }));
});
