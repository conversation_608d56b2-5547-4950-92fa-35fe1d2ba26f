// tslint:disable:no-duplicate-string
import { Test } from '@sage/xtrem-core';
import * as xtremFinanceData from '@sage/xtrem-finance-data';
import { assert } from 'chai';
import * as xtremSales from '../../../index';

describe.skip('storedAttributes', () => {
    it('"Project" attribute type is not active.', () =>
        Test.withContext(async context => {
            const employeeType = await context.read(
                xtremFinanceData.nodes.AttributeType,
                { id: 'employee' },
                { forUpdate: true },
            );
            await employeeType.$.set({ isActive: true });
            await employeeType.$.save();

            const taskType = await context.read(
                xtremFinanceData.nodes.AttributeType,
                { id: 'task' },
                { forUpdate: true },
            );
            await taskType.$.set({ isActive: false });
            await taskType.$.save();

            const projectType = await context.read(
                xtremFinanceData.nodes.AttributeType,
                { id: 'project' },
                { forUpdate: true },
            );
            await projectType.$.set({ isActive: false });
            await projectType.$.save();

            const nodeCategory = context.getNodeCacheCategory(xtremFinanceData.nodes.AttributeType);
            console.log('NodeCategory:', nodeCategory);
            await context.invalidateCachedCategory(nodeCategory);
            const salesInvoiceLine = await context.read(
                xtremSales.nodes.SalesInvoiceLine,
                { _id: '#SI1|10' },
                { forUpdate: true },
            );
            await salesInvoiceLine.$.set({
                storedAttributes: { project: 'AttPROJ', task: '', employee: 'AttEMPL2' },
            });
            await assert.isRejected(salesInvoiceLine.$.save());
        }));
});
