import type { NodeQueryFilter } from '@sage/xtrem-core';
import { Test, date } from '@sage/xtrem-core';
import { assert } from 'chai';
import * as xtremSales from '../../../index';

// TODO : Find the right parameters for the test
// TODO : change the type off the parameters !
describe('Filtering Shared function ', () => {
    it('Query test orderLineShippingFilterCriteria', () =>
        Test.withContext(
            async context => {
                const filter: NodeQueryFilter<xtremSales.nodes.SalesOrderLine> =
                    xtremSales.sharedFunctions.orderLineShippingFilterCriteria(
                        '#US002', // Stock Site
                        String(date.today()), // shippingDate
                        '2022-07-08', // shippingUntilDate
                        'Second Customer', // customerNameGte
                        'Universal Alarms', // customerNameLte
                        '#FCA', // incoterm
                        '#AIR', // deliveryMode
                        '1', // From Order
                        '9999', // To Order
                    );

                assert.deepEqual(
                    filter,
                    {
                        stockSite: { _id: { _eq: '#US002' } },
                        status: { _in: ['pending', 'inProgress'] },
                        shippingStatus: { _in: ['notShipped', 'partiallyShipped'] },
                        document: {
                            incoterm: '#FCA',
                            number: { _gte: '1', _lte: '9999' },
                            soldToCustomer: {
                                businessEntity: { name: { _gte: 'Second Customer', _lte: 'Universal Alarms' } },
                                isOnHold: false,
                            },
                            taxCalculationStatus: { _nin: ['failed'] },
                        },
                        deliveryMode: '#AIR',
                        doNotShipBeforeDate: { _or: [{ _lte: '2022-07-08' }, null] },
                        doNotShipAfterDate: { _or: [{ _gte: '2022-07-08' }, null] },
                        shippingDate: { _lte: '2022-07-08' },
                    },
                    JSON.stringify(filter, null, 4),
                );
                const salesShipments = context.query(xtremSales.nodes.SalesOrderLine, { filter });
                assert.equal(await salesShipments.length, 0);
            },
            { today: '2022-07-08' },
        ));
    it('Query test -  orderShippingFilterCriteria ', () =>
        Test.withContext(
            async context => {
                const filter: NodeQueryFilter<xtremSales.nodes.SalesOrder> =
                    xtremSales.sharedFunctions.orderShippingFilterCriteria(
                        '#US002', // Stock Site
                        String(date.today()), // shippingUntilDate
                        'Second Customer', // customerNameGte
                        'Universal Alarms', // customerNameLte
                        '#FCA', // incoterm
                        '#AIR', // deliveryMode
                    );

                assert.deepEqual(
                    filter,
                    {
                        stockSite: { _id: { _eq: '#US002' } },
                        status: { _in: ['pending', 'inProgress'] },
                        shippingStatus: { _in: ['notShipped', 'partiallyShipped'] },
                        soldToCustomer: {
                            businessEntity: { name: { _gte: 'Second Customer', _lte: 'Universal Alarms' } },
                            isOnHold: false,
                        },
                        incoterm: { _id: '#FCA' },
                        deliveryMode: { _id: '#AIR' },
                        shippingDate: { _lte: '2022-07-08' },
                    },
                    JSON.stringify(filter, null, 4),
                );
                const salesShipments = context.query(xtremSales.nodes.SalesOrder, { filter });
                assert.equal(await salesShipments.length, 0);
            },
            { today: '2022-07-08' },
        ));
});
