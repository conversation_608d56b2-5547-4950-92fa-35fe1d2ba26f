import type { NodeQueryFilter } from '@sage/xtrem-core';
import { Test } from '@sage/xtrem-core';
import { assert } from 'chai';
import * as xtremSales from '../../../index';

describe('shipmentInvoicingFilterCriteria Shared function ', () => {
    describe('shipmentInvoicingFilterCriteria', () => {
        it('skipNumberFiltering true', () => {
            const filter: NodeQueryFilter<xtremSales.nodes.SalesShipment> =
                xtremSales.sharedFunctions.shipmentInvoicingFilterCriteria('1', null, '1', '1');
            assert.deepEqual(
                filter,
                {
                    site: { _id: { _eq: '1' } },
                    status: 'shipped',
                    invoiceStatus: { _not: { _eq: 'invoiced' } },
                    billToCustomer: { businessEntity: { name: { _gte: '1', _lte: '1' } } },
                },
                JSON.stringify(filter, null, 4),
            );
        });

        it('skipNumberFiltering false', () => {
            const filter: NodeQueryFilter<xtremSales.nodes.SalesShipment> =
                xtremSales.sharedFunctions.shipmentInvoicingFilterCriteria('1', null, '1', '2', '3', '4', false);
            assert.deepEqual(
                filter,
                {
                    number: { _gte: '3', _lte: '4' },
                    site: { _id: { _eq: '1' } },
                    status: 'shipped',
                    invoiceStatus: { _not: { _eq: 'invoiced' } },
                    billToCustomer: { businessEntity: { name: { _gte: '1', _lte: '2' } } },
                },
                JSON.stringify(filter, null, 4),
            );
        });

        it('Query test', () =>
            Test.withContext(async context => {
                const filter: NodeQueryFilter<xtremSales.nodes.SalesShipment> =
                    xtremSales.sharedFunctions.shipmentInvoicingFilterCriteria(
                        '#US001',
                        '2021-06-11',
                        'Second Customer',
                        'Universal Alarms',
                        '4',
                        '10',
                        false,
                    );
                assert.deepEqual(
                    filter,
                    {
                        number: { _gte: '4', _lte: '10' },
                        site: { _id: { _eq: '#US001' } },
                        shippingDate: { _lte: '2021-06-11' },
                        status: 'shipped',
                        invoiceStatus: { _not: { _eq: 'invoiced' } },
                        billToCustomer: {
                            businessEntity: { name: { _gte: 'Second Customer', _lte: 'Universal Alarms' } },
                        },
                    },
                    JSON.stringify(filter, null, 4),
                );
                const salesShipments = context.query(xtremSales.nodes.SalesShipment, { filter });
                // This was two on the graphql unit test, but after my refactoring i found 0 because
                // the invoice Status of salesShipment are invoiced
                assert.equal(await salesShipments.length, 0);
            }));
    });
});
