// tslint:disable:no-duplicate-string
import type { Context, Dict, NodeCreateData } from '@sage/xtrem-core';
import { Test, ValidationSeverity, date } from '@sage/xtrem-core';
import { DateValue } from '@sage/xtrem-date-time';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremReporting from '@sage/xtrem-reporting';
import * as xtremStockData from '@sage/xtrem-stock-data';
import * as xtremStructure from '@sage/xtrem-structure';
import * as xtremSystem from '@sage/xtrem-system';
import * as xtremTax from '@sage/xtrem-tax';
import { assert } from 'chai';
import * as sinon from 'sinon';
import * as xtremSales from '../../../index';
import { calculateSalesOrderStatuses } from '../../../lib/functions/sales-order-lib';
import { readSalesOrder, salesOrderPrintValidation } from '../../fixtures/lib/functions/sales-order';

const { withSequenceNumberContext } = xtremMasterData.functions.testLib;

function compareData(
    actual: xtremStockData.interfaces.ProjectedStockRecord[] | null,
    expected: xtremStockData.interfaces.ProjectedStockRecord[] | null,
): boolean {
    if (actual && expected) {
        assert.equal(actual.length, expected.length);

        for (let i: number = 0; i < actual.length; i += 1) {
            assert.equal(actual[i].day.toString(), expected[i].day.toString());
            assert.equal(actual[i].demand, expected[i].demand);
            assert.equal(actual[i].supply, expected[i].supply);
            assert.equal(actual[i].stockLevel, expected[i].stockLevel);
        }
        return true;
    }
    return actual === expected;
}

describe('SalesOrderNode', () => {
    before(() => {});

    function createSalesNode(
        context: Context,
        testKey: Dict<any>,
        statusDict = { status: 'draft', shippingStatus: 'notShipped' },
        skipLines = false,
        item = '#3426',
        billToCustomer: string | null = '#US023',
        site: string | null = '#US001',
        stockSite: string | null = '#US001',
        dataDict: {
            date: date;
            expectedDeliveryDate: date | null;
            shippingDate: date | null;
            stockSite?: string;
        } = {
            date: date.parse('2020-11-18', context.currentLocale as any),
            expectedDeliveryDate: date.parse('2020-11-18', context.currentLocale as any),
            shippingDate: date.parse('2020-11-18', context.currentLocale as any),
            stockSite: stockSite || '#US001',
        },
        lineSalesSite = site || '#US001',
        discount: number | null = 0,
    ): Promise<xtremSales.nodes.SalesOrder> {
        let lines;
        if (skipLines === false) {
            lines = [
                {
                    status: statusDict.status,
                    shippingStatus: statusDict.shippingStatus,
                    item,
                    site: lineSalesSite,
                    stockSite: dataDict.stockSite || '#US001',
                    quantity: 45,
                    date: dataDict.date,
                    grossPrice: 20.289,
                    discount,
                    charge: discount,
                    expectedDeliveryDate: dataDict.expectedDeliveryDate,
                    shippingDate: dataDict.shippingDate,
                    doNotShipBeforeDate: date.parse('2020-11-12', context.currentLocale as any),
                    doNotShipAfterDate: date.parse('2020-11-18', context.currentLocale as any),
                },
            ];
        }

        const baseData: Dict<any> = {
            site,
            stockSite,
            billToCustomer,
            date: dataDict.date,
            expectedDeliveryDate: dataDict.expectedDeliveryDate,
            shippingDate: dataDict.shippingDate,
            requestedDeliveryDate: date.parse('2020-11-20', context.currentLocale as any),
            doNotShipBeforeDate: date.parse('2020-11-12', context.currentLocale as any),
            doNotShipAfterDate: date.parse('2020-11-18', context.currentLocale as any),
            soldToCustomer: billToCustomer || '#US023',
            billToLinkedAddress: '#US019|1500',
            ...testKey,
            lines,
        };
        return context.create(xtremSales.nodes.SalesOrder, baseData);
    }

    it('Create sales order node 1', () =>
        Test.withContext(async context => {
            const salesOrder = await createSalesNode(
                context,
                { number: 'test2' },
                { status: 'quote', shippingStatus: 'notShipped' },
                false,
                '#3542',
            );
            await salesOrder.$.save();
            assert.deepEqual(salesOrder.$.context.diagnoses, []);
            assert.equal(await salesOrder.workDays, 65); // work only in weekends (1000001)
            assert.equal(String(await salesOrder.shippingDate), '2020-11-18');
        }));

    it('Create sales order node 2', () =>
        Test.withContext(async context => {
            const salesOrder = await createSalesNode(
                context,
                { number: 'test3' },
                { status: 'quote', shippingStatus: 'notShipped' },
                false,
                '#5467',
            );
            await salesOrder.$.save();
            assert.deepEqual(salesOrder.$.context.diagnoses, []);
        }));

    it('Create sales order node 3', () => () =>
        Test.withContext(async context => {
            const salesOrder = await createSalesNode(
                context,
                { number: 'test4' },
                { status: 'quote', shippingStatus: 'notShipped' },
                false,
                '#17892',
            );
            await salesOrder.$.save();
            assert.deepEqual(salesOrder.$.context.diagnoses, []);
        }));

    it('Create sales order node 4', () =>
        Test.withContext(async context => {
            const salesOrder = await createSalesNode(
                context,
                { number: 'test5' },
                { status: 'quote', shippingStatus: 'notShipped' },
                false,
                '#3542',
                '#CUST07',
                '#US001',
                '#US001',
                {
                    date: date.parse('2020-11-17', context.currentLocale as any),
                    expectedDeliveryDate: null,
                    shippingDate: null,
                },
            );
            await salesOrder.$.save();
            assert.deepEqual(salesOrder.$.context.diagnoses, []);
            assert.equal(await salesOrder.workDays, 62);
            assert.equal(String(await salesOrder.shippingDate), '2020-11-17');
        }));

    it('Create sales order node 4', () =>
        Test.withContext(async context => {
            const salesOrder = await createSalesNode(
                context,
                { number: 'test5' },
                { status: 'quote', shippingStatus: 'notShipped' },
                false,
                '#3542',
                '#US024',
                '#US001',
                '#US001',
                {
                    date: date.parse('2020-11-17', context.currentLocale as any),
                    expectedDeliveryDate: date.parse('2020-11-20', context.currentLocale as any),
                    shippingDate: date.parse('2020-11-17', context.currentLocale as any),
                },
                '#US001',
                3.778,
            );
            await salesOrder.$.save();

            assert.deepEqual(salesOrder.$.context.diagnoses, []);
            assert.equal(await salesOrder.workDays, 0);
            assert.equal(String(await salesOrder.shippingDate), '2020-11-17');
            assert.equal(
                String(await (await (await salesOrder.lines.elementAt(0)).discountCharges.elementAt(0)).amount),
                '0.767',
            );
        }));

    it('Create sales order node 5', () =>
        Test.withContext(async context => {
            const salesOrder = await createSalesNode(
                context,
                { number: 'test5' },
                { status: 'quote', shippingStatus: 'notShipped' },
                false,
                '#3542',
                null,
            );
            await salesOrder.$.save();

            assert.deepEqual(salesOrder.$.context.diagnoses, []);
            assert.equal(await (await salesOrder.paymentTerm).name, 'Net 45');
        }));

    it('Sales order print and send email - success', () =>
        Test.withContext(async context => {
            const salesOrder = await context.read(xtremSales.nodes.SalesOrder, { _id: '#SO6' }, { forUpdate: true });

            const sandbox = sinon.createSandbox();
            sandbox.stub(xtremSales.nodes.SalesOrder, 'printSalesOrderAndEmail').returns(Promise.resolve(true));

            const result = await xtremSales.nodes.SalesOrder.printSalesOrderAndEmail(
                context,
                salesOrder,
                'mr',
                'John',
                'Doe',
                '<EMAIL>',
            );
            assert.equal(result, true);

            sandbox.restore();
        }));

    it('Sales order print and send email fail - invalid email', () =>
        Test.withContext(async context => {
            const salesOrder = await context.read(xtremSales.nodes.SalesOrder, { _id: '#SO1' }, { forUpdate: true });

            await assert.isRejected(
                xtremSales.nodes.SalesOrder.printSalesOrderAndEmail(context, salesOrder, 'mr', 'John', 'Doe', ''),
                'The sales order cannot be sent. The email is not valid.',
            );
        }));

    it('Sales order print and send email fail - wrong status', () =>
        Test.withContext(async context => {
            const salesOrder = await context.read(xtremSales.nodes.SalesOrder, { _id: '#SO16' }, { forUpdate: true });
            await assert.isRejected(
                xtremSales.nodes.SalesOrder.printSalesOrderAndEmail(
                    context,
                    salesOrder,
                    'mr',
                    'John',
                    'Doe',
                    '<EMAIL>',
                ),
                'You can only send a "Quote" or a "Pending" sales order.',
            );
        }));

    it('Sales order print and send email fail - wrong tax status', () =>
        Test.withContext(async context => {
            const salesOrder = await context.read(xtremSales.nodes.SalesOrder, { _id: '#SO29' }, { forUpdate: true });
            await assert.isRejected(
                xtremSales.nodes.SalesOrder.printSalesOrderAndEmail(
                    context,
                    salesOrder,
                    'mr',
                    'John',
                    'Doe',
                    '<EMAIL>',
                ),
                'The tax calculation needs to be done before you can send the sales order.',
            );
        }));

    it('Sales order print and send email fail - no report', () =>
        Test.withContext(async context => {
            const salesOrder = await context.read(xtremSales.nodes.SalesOrder, { _id: '#SO6' }, { forUpdate: true });

            const sandbox = sinon.createSandbox();
            sandbox.stub(xtremReporting.nodes.Report, 'generateReports').returns(Promise.resolve([]));

            await assert.isRejected(
                xtremSales.nodes.SalesOrder.printSalesOrderAndEmail(
                    context,
                    salesOrder,
                    'mr',
                    'John',
                    'Doe',
                    '<EMAIL>',
                ),
                'The sales order cannot be sent. No report has been created.',
            );

            sandbox.restore();
        }));

    it('Create sales order node fail', () =>
        Test.withContext(async context => {
            const salesOrder = await createSalesNode(
                context,
                { status: 'closed' },
                { status: 'closed', shippingStatus: 'notShipped' },
            );
            await assert.isRejected(salesOrder.$.save());
            assert.deepEqual(salesOrder.$.context.diagnoses, [
                {
                    message: 'The status for the sales order line needs to be "Quote" or "Pending".',
                    path: ['lines', '-1000000002'],
                    severity: ValidationSeverity.error,
                },
            ]);
        }));

    it('Create sales order node fail 2', () =>
        Test.withContext(async context => {
            const salesOrder = await createSalesNode(
                context,
                { status: 'quote' },
                { status: 'quote', shippingStatus: 'notShipped' },
                true,
            );
            await assert.isRejected(salesOrder.$.save());
            assert.deepEqual(salesOrder.$.context.diagnoses, [
                {
                    message: 'The sales order must contain at least one line.',
                    path: [],
                    severity: 3,
                },
            ]);
        }));

    it('Create sales order node fail 3', () =>
        Test.withContext(async context => {
            const salesOrder = await createSalesNode(
                context,
                { status: 'quote' },
                { status: 'pending', shippingStatus: 'notShipped' },
            );

            await assert.isRejected(salesOrder.$.save());
            assert.deepEqual(salesOrder.$.context.diagnoses, [
                {
                    message: 'The sales order lines status must have the same status than the header.',
                    path: ['lines', '-1000000002'],
                    severity: ValidationSeverity.error,
                },
            ]);
        }));

    it('Create sales order node fail 4', () =>
        Test.withContext(async context => {
            const salesOrder = await createSalesNode(
                context,
                { status: 'quote' },
                { status: 'quote', shippingStatus: 'partiallyShipped' },
            );
            await assert.isRejected(salesOrder.$.save());
            assert.deepEqual(salesOrder.$.context.diagnoses, [
                {
                    message:
                        'The shipping status of the sales order lines must be the same as the shipping status displayed in the header.',
                    path: ['lines', '-1000000002'],
                    severity: ValidationSeverity.error,
                },
            ]);
        }));

    it('Create sales order node fail 5', () =>
        Test.withContext(async context => {
            const salesOrder = await createSalesNode(
                context,
                { shippingStatus: 'shipped' },
                { status: 'quote', shippingStatus: 'shipped' },
            );

            await assert.isRejected(salesOrder.$.save());
            assert.deepEqual(salesOrder.$.context.diagnoses, [
                {
                    message:
                        "Incorrect sales order shipping status. The sales order must have the 'Not shipped' shipping status to be created.",
                    path: [],
                    severity: ValidationSeverity.error,
                },
            ]);
        }));

    it('Create sales order node fail 6', () =>
        Test.withContext(async context => {
            const salesOrder = await createSalesNode(
                context,
                { number: 'test2' },
                { status: 'quote', shippingStatus: 'notShipped' },
                false,
                '#3542',
                '#US024',
                '#US001',
                '#US001',
                {
                    date: date.parse('2020-11-17', context.currentLocale as any),
                    expectedDeliveryDate: date.parse('2020-11-20', context.currentLocale as any),
                    shippingDate: date.parse('2020-11-16', context.currentLocale as any),
                },
            );
            await assert.isRejected(salesOrder.$.save());
            assert.deepEqual(salesOrder.$.context.diagnoses, [
                {
                    message: 'The shipping date needs to be after the order date.',
                    path: ['lines', '-1000000002', 'shippingDate'],
                    severity: ValidationSeverity.error,
                },
            ]);
        }));

    it('Create sales order node fail 7', () =>
        Test.withContext(async context => {
            const salesOrder = await createSalesNode(
                context,
                { number: 'test2' },
                { status: 'quote', shippingStatus: 'notShipped' },
                false,
                '#3542',
                '#US024',
                '#US001',
                '#US001',
                {
                    date: date.parse('2020-11-11', context.currentLocale as any),
                    expectedDeliveryDate: date.parse('2020-11-20', context.currentLocale as any),
                    shippingDate: date.parse('2020-11-11', context.currentLocale as any),
                },
            );
            await assert.isRejected(salesOrder.$.save());
            assert.deepEqual(salesOrder.$.context.diagnoses, [
                {
                    message: 'The shipping date needs to be after the Do not ship before date.',
                    path: ['lines', '-1000000002', 'shippingDate'],
                    severity: ValidationSeverity.error,
                },
            ]);
        }));

    it('Create sales order node fail 8', () =>
        Test.withContext(async context => {
            const salesOrder = await createSalesNode(
                context,
                { number: 'test2' },
                { status: 'quote', shippingStatus: 'notShipped' },
                false,
                '#3542',
                '#US024',
                '#US001',
                '#US001',
                {
                    date: date.parse('2020-11-20', context.currentLocale as any),
                    expectedDeliveryDate: date.parse('2020-11-20', context.currentLocale as any),
                    shippingDate: date.parse('2020-12-11', context.currentLocale as any),
                },
            );
            await assert.isRejected(salesOrder.$.save());
            assert.deepEqual(salesOrder.$.context.diagnoses, [
                {
                    message: 'The shipping date needs to be before the Do not ship after date.',
                    path: ['lines', '-1000000002', 'shippingDate'],
                    severity: ValidationSeverity.error,
                },
            ]);
        }));

    it('Create sales order node fail 9', () =>
        Test.withContext(async context => {
            const salesOrder = await createSalesNode(
                context,
                { number: 'test5' },
                { status: 'quote', shippingStatus: 'notShipped' },
                false,
                '#3542',
                '#US024',
                '#US001',
                '#US001',
                {
                    date: date.parse('2020-11-17', context.currentLocale as any),
                    expectedDeliveryDate: date.parse('2020-11-20', context.currentLocale as any),
                    shippingDate: date.parse('2020-11-17', context.currentLocale as any),
                },
                '#US002',
            );
            await assert.isRejected(salesOrder.$.save());
            assert.deepEqual(salesOrder.$.context.diagnoses, [
                {
                    message: 'The site on the sales order lines must be the same as in the header.',
                    path: ['lines', '-1000000002'],
                    severity: ValidationSeverity.error,
                },
            ]);
        }));

    it('Create sales order node fail 10', () =>
        Test.withContext(async context => {
            const salesOrder = await createSalesNode(
                context,
                { number: 'test5' },
                { status: 'quote', shippingStatus: 'notShipped' },
                false,
                '#3542',
                '#US024',
                '#US001',
                '#US001',
                {
                    date: date.parse('2020-11-17', context.currentLocale as any),
                    expectedDeliveryDate: null,
                    shippingDate: date.parse('2020-11-18', context.currentLocale as any),
                    stockSite: '#US004',
                },
            );

            await assert.isRejected(salesOrder.$.save());
            assert.deepEqual(salesOrder.$.context.diagnoses, [
                {
                    message:
                        'Incorrect stock site on the sales order line. The sales site and the stock site on the sales order line must have the same company.',
                    path: ['lines', '-1000000002'],
                    severity: ValidationSeverity.error,
                },
                {
                    message: 'The record is not valid. You need to select a different record.',
                    path: ['lines', '-1000000002', 'stockSite'],
                    severity: ValidationSeverity.error,
                },
            ]);
        }));

    it('Updating sales order node fail', () =>
        Test.withContext(async context => {
            const salesOrder = await context.read(xtremSales.nodes.SalesOrder, { _id: '#SO4' }, { forUpdate: true });
            await salesOrder.$.set({ customerNumber: 'abcd' });
            await assert.isRejected(salesOrder.$.save());
        }));

    it('Updating sales order line node status by quantity', () =>
        Test.withContext(async context => {
            const salesOrderLine = await context.read(
                xtremSales.nodes.SalesOrderLine,
                { _id: '#SO5|60500' },
                { forUpdate: true },
            );
            await salesOrderLine.$.set({ quantity: 20 });
            await salesOrderLine.$.save();
            assert.equal(await salesOrderLine.status, 'closed');
        }));

    it('Update sales order node ', () =>
        Test.withContext(async context => {
            const salesOrder = await context.read(xtremSales.nodes.SalesOrder, { _id: '#SO2' }, { forUpdate: true });
            assert.equal(await (await salesOrder.site).id, 'US003');
            assert.equal(await (await (await salesOrder.site).legalCompany).id, 'US002');

            await salesOrder.$.set({
                customerNumber: 'test',
                lines: [
                    {
                        _action: 'update',
                        _sortValue: 60200,
                        grossPrice: 22.913,
                        discount: 5,
                        charge: 7.921,
                    },
                ],
            });
            assert.equal(await (await (await (await salesOrder.lines.elementAt(0)).site).legalCompany).id, 'US002');
            await salesOrder.$.trySave();
            assert.equal(await salesOrder.lines.length, 1);

            assert.deepEqual(salesOrder.$.context.diagnoses, []);
            assert.equal(await salesOrder.customerNumber, 'test');
            assert.equal(
                String(
                    await (await (
                        await salesOrder.lines.elementAt(0)
                    ).discountCharges.find(async discount => (await discount.sign) === 'decrease'))!.amount,
                ),
                '1.146',
            );
            assert.equal(
                String(
                    await (await (
                        await salesOrder.lines.elementAt(0)
                    ).discountCharges.find(async discount => (await discount.sign) === 'increase'))!.amount,
                ),
                '1.815',
            );
        }));

    it('Check tracking quantities', () =>
        Test.withContext(async context => {
            const salesOrderLine = await context.read(
                xtremSales.nodes.SalesOrderLine,
                { _id: 'SO19|62600' },
                { forUpdate: true },
            );
            assert.equal(await salesOrderLine.quantityToShipInProgressInSalesUnit, 30);
            assert.equal(await salesOrderLine.shippedQuantityInSalesUnit, 0);
        }));

    it('Get projected stock - Create  Sales Order node', () =>
        Test.withContext(
            async context => {
                const site = await context.read(xtremSystem.nodes.Site, { id: 'US001' });
                const itemSales = await context.read(xtremMasterData.nodes.Item, { _id: '#Chemical D' });

                let result = await xtremStockData.nodeExtensions.ItemSiteExtension.getProjectedStock(
                    context,
                    itemSales,
                    site,
                    5,
                );
                assert.isNotNull(result);
                compareData(result, [
                    {
                        day: date.today(),
                        demand: 0,
                        supply: 0,
                        stockLevel: 9989,
                        reorderLevel: 10,
                        safetyLevel: 3,
                    },
                    {
                        day: date.today().addDays(1),
                        demand: 0,
                        supply: 0,
                        stockLevel: 9989,
                        reorderLevel: 10,
                        safetyLevel: 3,
                    },
                    {
                        day: date.today().addDays(2),
                        demand: 0,
                        supply: 0,
                        stockLevel: 9989,
                        reorderLevel: 10,
                        safetyLevel: 3,
                    },
                    {
                        day: date.today().addDays(3),
                        demand: 0,
                        supply: 0,
                        stockLevel: 9989,
                        reorderLevel: 10,
                        safetyLevel: 3,
                    },
                    {
                        day: date.today().addDays(4),
                        demand: 0,
                        supply: 0,
                        stockLevel: 9989,
                        reorderLevel: 10,
                        safetyLevel: 3,
                    },
                ]);
                const salesOrder = await context.create(xtremSales.nodes.SalesOrder, {
                    site: '#US001',
                    stockSite: '#US001',
                    billToCustomer: '#US023',
                    date: date.today(),
                    expectedDeliveryDate: date.today().addDays(3),
                    shippingDate: date.today().addDays(1),
                    requestedDeliveryDate: date.today().addDays(3),
                    doNotShipBeforeDate: date.today(),
                    doNotShipAfterDate: date.today().addDays(3),
                    soldToCustomer: '#US023',
                    billToLinkedAddress: '#US019|1500',
                    lines: [
                        {
                            item: '#Chemical D',
                            quantityInStockUnit: 10,
                            quantity: 10,
                            unit: { id: 'GRAM' },
                            grossPrice: 30,
                            shippingDate: date.today().addDays(1),
                        },
                    ],
                });
                await salesOrder.$.save();
                assert.deepEqual(salesOrder.$.context.diagnoses, []);

                result = await xtremStockData.nodeExtensions.ItemSiteExtension.getProjectedStock(
                    context,
                    itemSales,
                    site,
                    5,
                );
                assert.isNotNull(result);
                compareData(result, [
                    {
                        day: date.today(),
                        demand: 0,
                        supply: 0,
                        stockLevel: 9989,
                        reorderLevel: 10,
                        safetyLevel: 3,
                    },
                    {
                        day: date.today().addDays(1),
                        demand: 10,
                        supply: 0,
                        stockLevel: 9979,
                        reorderLevel: 10,
                        safetyLevel: 3,
                    },
                    {
                        day: date.today().addDays(2),
                        demand: 0,
                        supply: 0,
                        stockLevel: 9979,
                        reorderLevel: 10,
                        safetyLevel: 3,
                    },
                    {
                        day: date.today().addDays(3),
                        demand: 0,
                        supply: 0,
                        stockLevel: 9979,
                        reorderLevel: 10,
                        safetyLevel: 3,
                    },
                    {
                        day: date.today().addDays(4),
                        demand: 0,
                        supply: 0,
                        stockLevel: 9979,
                        reorderLevel: 10,
                        safetyLevel: 3,
                    },
                ]);
            },
            {
                today: '2020-06-10',
            },
        ));

    it('Get projected stock with past transaction - Create  Sales Order node', () =>
        Test.withContext(
            async context => {
                const site = await context.read(xtremSystem.nodes.Site, { id: 'US001' });
                const itemSales = await context.read(xtremMasterData.nodes.Item, { _id: '#MRP_SIMPLE_ITEM' });

                let result = await xtremStockData.nodeExtensions.ItemSiteExtension.getProjectedStock(
                    context,
                    itemSales,
                    site,
                    5,
                );
                assert.isNotNull(result);
                compareData(result, [
                    {
                        day: date.today(),
                        demand: 100,
                        supply: 0,
                        stockLevel: 9900,
                        reorderLevel: 0,
                        safetyLevel: 180,
                    },
                    {
                        day: date.today().addDays(1),
                        demand: 0,
                        supply: 0,
                        stockLevel: 9900,
                        reorderLevel: 0,
                        safetyLevel: 180,
                    },
                    {
                        day: date.today().addDays(2),
                        demand: 0,
                        supply: 0,
                        stockLevel: 9900,
                        reorderLevel: 0,
                        safetyLevel: 180,
                    },
                    {
                        day: date.today().addDays(3),
                        demand: 0,
                        supply: 0,
                        stockLevel: 9900,
                        reorderLevel: 0,
                        safetyLevel: 180,
                    },
                    {
                        day: date.today().addDays(4),
                        demand: 0,
                        supply: 0,
                        stockLevel: 9900,
                        reorderLevel: 0,
                        safetyLevel: 180,
                    },
                ]);
                const salesOrder = await context.create(xtremSales.nodes.SalesOrder, {
                    site: '#US001',
                    stockSite: '#US001',
                    billToCustomer: '#US023',
                    date: date.today(),
                    expectedDeliveryDate: date.today().addDays(3),
                    shippingDate: date.today().addDays(1),
                    requestedDeliveryDate: date.today().addDays(3),
                    doNotShipBeforeDate: date.today(),
                    doNotShipAfterDate: date.today().addDays(3),
                    soldToCustomer: '#US023',
                    billToLinkedAddress: '#US019|1500',
                    lines: [
                        {
                            item: '#MRP_SIMPLE_ITEM',
                            quantityInStockUnit: 1000,
                            quantity: 1000,
                            unit: { id: 'EACH' },
                            grossPrice: 30,
                            shippingDate: date.today().addDays(1),
                        },

                        {
                            item: '#MRP_SIMPLE_ITEM',
                            quantityInStockUnit: 1000,
                            quantity: 1000,
                            unit: { id: 'EACH' },
                            grossPrice: 30,
                            shippingDate: date.today(),
                        },
                    ],
                });
                await salesOrder.$.save();
                assert.deepEqual(salesOrder.$.context.diagnoses, []);

                result = await xtremStockData.nodeExtensions.ItemSiteExtension.getProjectedStock(
                    context,
                    itemSales,
                    site,
                    5,
                );
                assert.isNotNull(result);
                compareData(result, [
                    {
                        day: date.today(),
                        demand: 1100,
                        supply: 0,
                        stockLevel: 8900,
                        reorderLevel: 0,
                        safetyLevel: 180,
                    },
                    {
                        day: date.today().addDays(1),
                        demand: 1000,
                        supply: 0,
                        stockLevel: 7900,
                        reorderLevel: 0,
                        safetyLevel: 180,
                    },
                    {
                        day: date.today().addDays(2),
                        demand: 0,
                        supply: 0,
                        stockLevel: 7900,
                        reorderLevel: 0,
                        safetyLevel: 180,
                    },
                    {
                        day: date.today().addDays(3),
                        demand: 0,
                        supply: 0,
                        stockLevel: 7900,
                        reorderLevel: 0,
                        safetyLevel: 180,
                    },
                    {
                        day: date.today().addDays(4),
                        demand: 0,
                        supply: 0,
                        stockLevel: 7900,
                        reorderLevel: 0,
                        safetyLevel: 180,
                    },
                ]);
            },
            {
                today: '2022-10-10',
            },
        ));
    it('Update of ItemSite.requiredQuantity - Create Sales Order node', () =>
        Test.withContext(
            async context => {
                const site = await context.read(xtremSystem.nodes.Site, { id: 'US001' });
                const itemSales = await context.read(xtremMasterData.nodes.Item, { _id: '#Chemical D' });
                const orderDate = date.today();

                const itemSite = await context.read(xtremMasterData.nodes.ItemSite, {
                    item: itemSales._id,
                    site: site._id,
                });
                const wipDemandQty = await itemSite.requiredQuantity;

                const salesOrder = await context.create(xtremSales.nodes.SalesOrder, {
                    site: '#US001',
                    stockSite: '#US001',
                    billToCustomer: '#US023',
                    date: orderDate,
                    expectedDeliveryDate: date.today().addDays(3),
                    shippingDate: date.today().addDays(1),
                    requestedDeliveryDate: date.today().addDays(3),
                    doNotShipBeforeDate: date.today(),
                    doNotShipAfterDate: date.today().addDays(3),
                    soldToCustomer: '#US023',
                    billToLinkedAddress: '#US019|1500',
                    lines: [
                        {
                            item: '#Chemical D',
                            quantityInStockUnit: 10,
                            quantity: 10,
                            unit: { id: 'GRAM' },
                            grossPrice: 30,
                            shippingDate: date.today().addDays(1),
                        },
                    ],
                });
                await salesOrder.$.save();
                assert.deepEqual(salesOrder.$.context.diagnoses, []);

                assert.deepEqual(Number(await itemSite.requiredQuantity), wipDemandQty + 10);
            },
            {
                today: '2020-06-10',
            },
        ));

    it('Inactive item - Create Sales Order node', () =>
        Test.withContext(
            async context => {
                const orderDate = date.today();

                const salesOrder = await context.create(xtremSales.nodes.SalesOrder, {
                    site: '#US001',
                    stockSite: '#US001',
                    billToCustomer: '#US023',
                    date: orderDate,
                    expectedDeliveryDate: date.today().addDays(3),
                    shippingDate: date.today().addDays(1),
                    requestedDeliveryDate: date.today().addDays(3),
                    doNotShipBeforeDate: date.today(),
                    doNotShipAfterDate: date.today().addDays(3),
                    soldToCustomer: '#US023',
                    billToLinkedAddress: '#US019|1500',
                    lines: [
                        {
                            item: '#Chemical B',
                            quantityInStockUnit: 10,
                            quantity: 10,
                            unit: { id: 'GRAM' },
                            grossPrice: 30,
                            shippingDate: date.today().addDays(1),
                        },
                    ],
                });
                await assert.isRejected(salesOrder.$.save());
                assert.deepEqual(salesOrder.$.context.diagnoses, [
                    {
                        severity: 3,
                        path: ['lines', '-1000000002', 'item'],
                        message: 'You need to remove the inactive items before you change the document status.',
                    },
                ]);
            },
            {
                today: '2020-06-10',
            },
        ));

    it('Verify chronological control between two sales orders on creation', () =>
        withSequenceNumberContext('SalesOrder', { isChronological: true }, async context => {
            const salesOrder = await createSalesNode(
                context,
                { number: '' },
                { status: 'quote', shippingStatus: 'notShipped' },
                false,
                '#3542',
            );
            await salesOrder.$.save({ flushDeferredActions: true });

            const salesOrderError = await createSalesNode(
                context,
                { date: '2020-11-17' },
                { status: 'quote', shippingStatus: 'notShipped' },
                false,
                '#3542',
            );

            await assert.isRejected(
                salesOrderError.$.save({ flushDeferredActions: true }),
                'The record was not created.',
            );
            assert.deepEqual(salesOrderError.$.context.diagnoses, [
                {
                    message: 'The document date 2020-11-17 is earlier than the previous document date 2020-11-18.',
                    path: [],
                    severity: 3,
                },
            ]);
        }));

    it('Verify chronological control between two sales orders on change', () =>
        withSequenceNumberContext('SalesOrder', { isChronological: true }, async context => {
            const salesOrder = await createSalesNode(
                context,
                { number: '' },
                { status: 'quote', shippingStatus: 'notShipped' },
                false,
                '#3542',
            );
            await salesOrder.$.save({ flushDeferredActions: true });
            assert.equal((await salesOrder.date).toString(), DateValue.parse('2020-11-18').toString());
            const salesOrderError = await createSalesNode(
                context,
                { number: '' },
                { status: 'quote', shippingStatus: 'notShipped' },
                false,
                '#3542',
            );
            await salesOrderError.$.save({ flushDeferredActions: true });
            assert.deepEqual(salesOrder.$.context.diagnoses, []);

            await salesOrderError.$.set({
                date: DateValue.parse('2020-11-17'),
            });

            assert.deepEqual(salesOrder.$.context.diagnoses, []);

            await assert.isRejected(
                salesOrderError.$.save({ flushDeferredActions: true }),
                'The record was not updated.', // TO CHECK 'The document date 2020-11-17 is earlier than the previous document date 2020-11-18.',
            );
        }));
    /**
     * Issue XT-29273
     */
    it('Create a sales Order from a payload', () =>
        Test.withContext(
            async context => {
                const defaultSite = await context.read(xtremSystem.nodes.Site, { id: 'US001' });
                const orderDate = date.today();

                const createPayload: NodeCreateData<xtremSales.nodes.SalesOrder> = {
                    site: defaultSite,
                    stockSite: defaultSite,
                    billToCustomer: '#US023',
                    date: orderDate,
                    expectedDeliveryDate: date.today().addDays(3),
                    shippingDate: date.today().addDays(1),
                    requestedDeliveryDate: date.today().addDays(3),
                    doNotShipBeforeDate: date.today(),
                    doNotShipAfterDate: date.today().addDays(3),
                    soldToCustomer: '#US023',
                    billToLinkedAddress: '#US019|1500',
                    lines: [
                        {
                            item: '#Chemical D',
                            quantityInStockUnit: 10,
                            quantity: 10,
                            unit: { id: 'GRAM' },
                            grossPrice: 30,
                            shippingDate: date.today().addDays(1),
                        },
                    ],
                };

                const retry = await context.create(xtremSales.nodes.SalesOrder, createPayload);

                await retry.$.trySave();
                assert.deepEqual(context.diagnoses, []);
            },
            {
                today: '2020-06-10',
            },
        ));

    it('Allocation sales order fail - The allocated quantity on the sales order line cannot be larger than the remaining quantity to ship.', () =>
        Test.withContext(async context => {
            const salesOrder = await context.read(
                xtremSales.nodes.SalesOrder,
                { _id: '#SO220001' },
                { forUpdate: true },
            );
            await xtremStockData.nodes.Stock.updateAllocations(
                context,
                {
                    documentLine: salesOrder.lines.elementAt(0),
                    allocationUpdates: [
                        {
                            action: 'create',
                            quantity: 2000,
                            stockRecord: context.read(xtremStockData.nodes.Stock, { _id: '68' }),
                        },
                    ],
                },
                {
                    cannotOverAllocate: false,
                },
            );

            await salesOrder.$.set({
                lines: [
                    {
                        _sortValue: await (await salesOrder.lines.elementAt(0))._sortValue,
                        quantity: 20,
                        _action: 'update',
                    },
                ],
            });

            await assert.isRejected(salesOrder.$.save());

            assert.deepEqual(salesOrder.$.context.diagnoses, [
                {
                    severity: 3,
                    path: ['lines', '1200'],
                    message:
                        'The allocated quantity on the sales order line cannot be larger than the remaining quantity to ship.',
                },
            ]);
        }));

    it('Transfer allocation from sales order to shipment during shipment creation.', () =>
        Test.withContext(async context => {
            const salesOrder = await context.read(xtremSales.nodes.SalesOrder, { _id: '#SO220001' });
            const firstSalesOrderLineAllocation = await (await salesOrder.lines.elementAt(0)).stockAllocations.at(0);
            if (!firstSalesOrderLineAllocation) throw new Error('Sales shipment line allocation not found');
            const qtyToTransferInOrder = (await firstSalesOrderLineAllocation.quantityInStockUnit).valueOf();
            const salesShipments = await xtremSales.nodes.SalesOrder.createShipmentsFromOrder(context, salesOrder);
            if (salesShipments?.length === 0) {
                assert.fail('No documents created');
            }
            assert.equal(salesShipments?.length, 1);
            const salesShipment = await context.read(xtremSales.nodes.SalesShipment, {
                _id: salesShipments?.at(0)?._id,
            });
            const firstShipmentLineAllocation = await (await salesShipment.lines.elementAt(0)).stockAllocations.at(0);
            if (!firstShipmentLineAllocation) throw new Error('Sales shipment line allocation not found');
            const qtyTransferredInShipment = (await firstShipmentLineAllocation.quantityTransferred).valueOf();
            assert.deepEqual(qtyToTransferInOrder, qtyTransferredInShipment);
        }));

    it('Check quantity in stock unit', () =>
        Test.withContext(async context => {
            const createdSalesOrder = await context.create(xtremSales.nodes.SalesOrder, {
                shippingStatus: 'notShipped',
                site: '#US001',
                shipToCustomer: '#US019',
                soldToCustomer: '#US019',
                incoterm: '#EXW',
                shipToCustomerAddress: '#US019|1500',
                billToLinkedAddress: '#US019|1500',
                requestedDeliveryDate: date.parse('2020-11-28', context.currentLocale as any),
                deliveryLeadTime: 1,
                currency: '#ZAR',
                paymentTerm: '#DUE_UPON_RECEIPT_ALL',
                isExternalNote: true,
                lines: [
                    {
                        shipToCustomerAddress: '#US019|1500',
                        item: '#17891',
                        unit: '#LITER',
                        quantity: 600,
                        grossPrice: 2,
                        discountCharges: [
                            {
                                sign: 'increase',
                                valueType: 'percentage',
                                calculationBasis: 'grossPrice',
                                basis: 2,
                                value: 3,
                            },
                        ],
                        discount: 3,
                        isExternalNote: true,
                    },
                ],
            });
            await createdSalesOrder.$.save();
            const firstLine = await createdSalesOrder.lines.at(0);
            const wip = await firstLine?.workInProgress;
            assert.equal(await firstLine?.quantityInStockUnit, Number(0.6), 'Quantity in stock unit');
            assert.equal(await wip?.expectedQuantity, Number(0.6), 'Expected quantity');
        }));

    it('Update sales order line gross profit ', () =>
        Test.withContext(async context => {
            const itemSite = await context.read(
                xtremMasterData.nodes.ItemSite,
                { item: '#STOAVC', site: '#US001' },
                { forUpdate: true },
            );
            const salesOrder = await context.read(xtremSales.nodes.SalesOrder, { _id: '#SO32' }, { forUpdate: true });
            const salesOrderLine = await salesOrder.lines.elementAt(0);
            const unitConversionFactor = await xtremMasterData.functions.getConvertCoefficient(
                await salesOrderLine.unit,
                await salesOrderLine.stockUnit,
            );
            assert.equal(await salesOrderLine.quantity, 16);
            assert.equal(await salesOrderLine.unitToStockUnitConversionFactor, unitConversionFactor);
            await salesOrderLine.$.set({ quantity: 10 });
            await salesOrder.$.save();
            assert.deepEqual(salesOrder.$.context.diagnoses, []);
            assert.equal(
                await salesOrderLine.quantityInStockUnitComputed(),
                Number(await salesOrderLine.quantityInStockUnit),
            );
            assert.equal(await salesOrderLine.stockCostAmountInCompanyCurrency, Number(1202.25)); // 12383.17/103 * 10
            assert.equal(Number(await salesOrder.totalGrossProfit), Number(82.55));

            // Modify the stock valuation in order to check that a new value is retrieved after net price modification
            await itemSite.$.set({ stockValuationAtAverageCost: 4560 });
            await itemSite.$.save();

            await salesOrderLine.$.set({ grossPrice: 125, discount: 20, charge: 10 });
            await salesOrder.$.save();
            assert.deepEqual(salesOrder.$.context.diagnoses, []);
            assert.equal(await salesOrderLine.stockCostAmountInCompanyCurrency, Number(442.718));
        }));

    it('Ship a sales order with some lines not shipped', () =>
        Test.withContext(async context => {
            const salesOrder = await context.read(xtremSales.nodes.SalesOrder, { _id: '#SO8' }, { forUpdate: true });
            assert.equal(await (await salesOrder.lines.elementAt(0)).shippingStatus, 'shipped');
            assert.equal(await (await salesOrder.lines.elementAt(1)).shippingStatus, 'notShipped');

            const salesShipments = await xtremSales.nodes.SalesOrder.createShipmentsFromOrder(context, salesOrder);
            const salesShipment = await context.read(xtremSales.nodes.SalesShipment, {
                _id: salesShipments?.at(0)?._id,
            });
            assert.equal(await salesShipment.lines.length, 1);
        }));

    it('Trying to ship the same order twice - isSafeToRetry = true', () =>
        Test.withContext(async context => {
            const salesOrder = await context.read(xtremSales.nodes.SalesOrder, { _id: '#SO8' }, { forUpdate: true });
            assert.equal(await (await salesOrder.lines.elementAt(0)).shippingStatus, 'shipped');
            assert.equal(await (await salesOrder.lines.elementAt(1)).shippingStatus, 'notShipped');

            const salesShipments = await xtremSales.nodes.SalesOrder.createShipmentsFromOrder(context, salesOrder);
            const salesShipment = await context.read(xtremSales.nodes.SalesShipment, {
                _id: salesShipments?.at(0)?._id,
            });
            assert.equal(await salesShipment.lines.length, 1);
            await assert.isRejected(
                xtremSales.nodes.SalesOrder.createShipmentsFromOrder(context, salesOrder, true, true),
                'No shipments were created for the sales order: SO8.',
            );
        }));

    it('Shipment creation fail because of an allocation request in progress.', () =>
        Test.withContext(async context => {
            const salesOrder = await context.read(xtremSales.nodes.SalesOrder, { _id: '#SO35' }, { forUpdate: true });

            // Without the processOptions parameter
            await assert.isRejected(
                xtremSales.nodes.SalesOrder.createShipmentsFromOrder(context, salesOrder),
                'You can only ship the sales order line after the allocation request is complete.',
            );
        }));

    it('Cannot decrease quantity because an allocation request is in progress ', () =>
        Test.withContext(async context => {
            const salesOrderLine = await context.read(
                xtremSales.nodes.SalesOrderLine,
                { _id: '#SO35|10' },
                { forUpdate: true },
            );
            await salesOrderLine.$.set({ quantity: 10 });
            await assert.isRejected(salesOrderLine.$.save(), 'The record was not updated.');
            assert.equal(
                salesOrderLine.$.context.diagnoses[0].message,
                'You can only reduce the quantity of the sales order line after the allocation request is complete.',
            );
        }));

    it('Returns correct stock shortage value', () =>
        Test.withContext(async context => {
            const salesOrder = await context.read(
                xtremSales.nodes.SalesOrder,
                { _id: '#SO220001' },
                { forUpdate: true },
            );
            const salesOrderLine = await salesOrder.lines.elementAt(0);
            await xtremStockData.nodes.Stock.updateAllocations(
                context,
                {
                    documentLine: salesOrder.lines.elementAt(0),
                    allocationUpdates: [
                        {
                            action: 'create',
                            quantity: 9990,
                            stockRecord: context.read(xtremStockData.nodes.Stock, { _id: '68' }),
                        },
                    ],
                },
                { cannotOverAllocate: false },
            );
            await salesOrder.$.set({
                lines: [
                    {
                        _sortValue: await (await salesOrder.lines.elementAt(0))._sortValue,
                        quantity: 10000,
                        _action: 'update',
                    },
                ],
            });

            assert.equal(await salesOrderLine.stockShortageInStockUnit, 0);
            assert.equal(await salesOrderLine.stockShortageStatus, false);
        }));

    it('On hold control - Confirm mutation sales order for customer on hold and blocking', () =>
        Test.withContext(async context => {
            const billToCustomer = await context.read(
                xtremMasterData.nodes.Customer,
                {
                    businessEntity: '#US019',
                },
                { forUpdate: true },
            );

            await billToCustomer.$.set({ isOnHold: true });
            await billToCustomer.$.save();

            const salesOrder = await context.read(xtremSales.nodes.SalesOrder, { number: 'SO30' }, { forUpdate: true });
            await assert.isRejected(
                xtremSales.nodes.SalesOrder.confirmSalesOrder(context, salesOrder),
                'The record was not updated.',
            );

            const errorMessages = context.diagnoses.map(diagnose => diagnose.message);
            assert.equal(errorMessages[0], 'Bill to customer is on hold.');
        }));

    it('On hold control - update sales order to pending', () =>
        Test.withContext(async context => {
            const billToCustomer = await context.read(
                xtremMasterData.nodes.Customer,
                { businessEntity: '#US019' },
                { forUpdate: true },
            );

            await billToCustomer.$.set({ isOnHold: true });
            await billToCustomer.$.save();

            const salesOrder = await context.read(xtremSales.nodes.SalesOrder, { number: 'SO30' }, { forUpdate: true });

            await salesOrder.$.set({ status: 'pending' });
            await assert.isRejected(salesOrder.$.save());
            assert.deepEqual(salesOrder.$.context.diagnoses, [
                {
                    message: 'Bill to customer is on hold.',
                    path: ['status'],
                    severity: ValidationSeverity.error,
                },
            ]);
        }));

    it('On hold control - update sales order line to pending', () =>
        Test.withContext(async context => {
            const billToCustomer = await context.read(
                xtremMasterData.nodes.Customer,
                {
                    businessEntity: '#US019',
                },
                { forUpdate: true },
            );

            await billToCustomer.$.set({ isOnHold: true });
            await billToCustomer.$.save();

            const salesOrder = await context.read(xtremSales.nodes.SalesOrder, { number: 'SO30' }, { forUpdate: true });

            await salesOrder.$.set({
                lines: [
                    {
                        _sortValue: await (await salesOrder.lines.elementAt(0))._sortValue,
                        status: 'pending',
                        _action: 'update',
                    },

                    {
                        _sortValue: await (await salesOrder.lines.elementAt(1))._sortValue,
                        status: 'pending',
                        _action: 'update',
                    },
                ],
            });

            await assert.isRejected(salesOrder.$.save());
            assert.deepEqual(salesOrder.$.context.diagnoses, [
                {
                    message: 'Bill to customer is on hold.',
                    path: ['lines', '1206', 'status'],
                    severity: ValidationSeverity.error,
                },
                {
                    message: 'Bill to customer is on hold.',
                    path: ['lines', '1207', 'status'],
                    severity: ValidationSeverity.error,
                },
            ]);
        }));

    it.skip('Tax zone on sales order line', () =>
        Test.withContext(async context => {
            let salesOrder = await createSalesNode(
                context,
                { number: 'testTaxZone01' },
                { status: 'quote', shippingStatus: 'notShipped' },
                false,
                '#3542',
            );

            await salesOrder.$.save();
            salesOrder = await context.read(xtremSales.nodes.SalesOrder, { _id: salesOrder._id }, { forUpdate: true });

            assert.equal(await (await salesOrder.lines.at(0))?.taxZone, null);

            const secondDeliveryAddress = await (await salesOrder.billToCustomer).deliveryAddresses.at(1);

            await salesOrder.$.set({
                _id: salesOrder._id,
                lines: [
                    {
                        _id: (await salesOrder.lines.at(0))?._id,
                        _action: 'update',
                        shipToCustomerAddress: secondDeliveryAddress?._id,
                    },
                ],
            });

            await salesOrder.$.save();
            salesOrder = await context.read(xtremSales.nodes.SalesOrder, { _id: salesOrder._id }, { forUpdate: true });

            assert.equal(
                await (
                    await (
                        await (
                            await salesOrder.lines.at(0)
                        )?.shipToCustomerAddress
                    )?.country
                )?.id,
                await (
                    await (
                        await (
                            await salesOrder.lines.at(0)
                        )?.shipToAddress
                    )?.country
                )?.id,
            );
            assert.equal(
                await (
                    await (
                        await salesOrder.lines.at(0)
                    )?.shipToCustomerAddress
                )?.postcode,
                await (
                    await (
                        await salesOrder.lines.at(0)
                    )?.shipToAddress
                )?.postcode,
            );
            assert.equal(
                await (
                    await (
                        await (
                            await (
                                await salesOrder.lines.at(0)
                            )?.shipToCustomerAddress
                        )?.deliveryDetail
                    )?.taxZone
                )?.id,
                'FRTZ',
            );
            assert.equal(await (await (await salesOrder.lines.at(0))?.taxZone)?.id, 'FRTZ');

            await salesOrder.$.set({
                _id: salesOrder._id,
                lines: [
                    {
                        _id: (await salesOrder.lines.at(0))?._id,
                        _action: 'update',
                        shipToAddress: {
                            name: await (await secondDeliveryAddress?.address)?.name,
                            addressLine1: await (await secondDeliveryAddress?.address)?.addressLine1,
                            addressLine2: await (await secondDeliveryAddress?.address)?.addressLine2,
                            city: await (await secondDeliveryAddress?.address)?.city,
                            region: await (await secondDeliveryAddress?.address)?.region,
                            postcode: '123',
                            country: await (await (await secondDeliveryAddress?.address)?.country)?.id,
                            locationPhoneNumber: await (await secondDeliveryAddress?.address)?.locationPhoneNumber,
                        },
                    },
                ],
            });

            await salesOrder.$.save();
            salesOrder = await context.read(xtremSales.nodes.SalesOrder, { _id: salesOrder._id });
            assert.equal(await (await (await salesOrder.lines.at(0))?.taxZone)?.id, undefined);
        }));

    it('Tax determination on sales order line', () =>
        Test.withContext(async context => {
            let salesOrder = await createSalesNode(
                context,
                { number: 'testTaxZone02' },
                { status: 'quote', shippingStatus: 'notShipped' },
                false,
                '#3542',
                '#Pastelaria moderna',
                '#CAS01',
                '#CAS01',
            );

            await salesOrder.$.save();
            salesOrder = await context.read(xtremSales.nodes.SalesOrder, { _id: salesOrder._id }, { forUpdate: true });

            const taxes = (await salesOrder.lines.at(0))?.taxes;

            assert.equal(await taxes?.length, 1);
            assert.equal(await (await taxes?.at(0))?.taxAmount, 210);
        }));

    it('Create sales shipment for one sales order line and delete the other sales order line', () =>
        Test.withContext(async context => {
            let salesOrder = await context.read(xtremSales.nodes.SalesOrder, { _id: '#SO32' }, { forUpdate: true });
            // check SO has two lines
            assert.equal(await salesOrder.lines.length, 2);
            // check 1st line statuses
            assert.equal(await (await salesOrder.lines.elementAt(0)).status, 'quote');
            assert.equal(await (await salesOrder.lines.elementAt(0)).invoiceStatus, 'notInvoiced');
            assert.equal(await (await salesOrder.lines.elementAt(0)).shippingStatus, 'notShipped');
            // check 2nd line statuses
            assert.equal(await (await salesOrder.lines.elementAt(1)).status, 'quote');
            assert.equal(await (await salesOrder.lines.elementAt(1)).invoiceStatus, 'notInvoiced');
            assert.equal(await (await salesOrder.lines.elementAt(1)).shippingStatus, 'notShipped');
            // check header statuses
            assert.equal(await salesOrder.status, 'quote');
            assert.equal(await salesOrder.invoiceStatus, 'notInvoiced');
            assert.equal(await salesOrder.shippingStatus, 'notShipped');
            assert.equal(await salesOrder.displayStatus, 'quote');

            // confirm the SO
            await xtremSales.nodes.SalesOrder.confirmSalesOrder(context, salesOrder);

            salesOrder = await context.read(xtremSales.nodes.SalesOrder, { _id: '#SO32' }, { forUpdate: true });
            // check 1st line statuses
            assert.equal(await (await salesOrder.lines.elementAt(0)).status, 'pending');
            assert.equal(await (await salesOrder.lines.elementAt(0)).invoiceStatus, 'notInvoiced');
            assert.equal(await (await salesOrder.lines.elementAt(0)).shippingStatus, 'notShipped');
            // check 2nd line statuses
            assert.equal(await (await salesOrder.lines.elementAt(1)).status, 'pending');
            assert.equal(await (await salesOrder.lines.elementAt(1)).invoiceStatus, 'notInvoiced');
            assert.equal(await (await salesOrder.lines.elementAt(1)).shippingStatus, 'notShipped');
            // check header statuses
            assert.equal(await salesOrder.status, 'pending');
            assert.equal(await salesOrder.invoiceStatus, 'notInvoiced');
            assert.equal(await salesOrder.shippingStatus, 'notShipped');
            assert.equal(await salesOrder.displayStatus, 'confirmed');

            // create a shipment for the first SO line
            const salesOrderLineToShip = await salesOrder.lines.elementAt(0);
            await xtremSales.nodes.SalesOrder.createSalesShipmentsFromOrderLines(context, [
                {
                    salesDocumentLine: salesOrderLineToShip,
                    quantityToProcess: await salesOrderLineToShip.quantity,
                },
            ]);
            // rollbackCache function will flush deferred actions and saves
            await Test.rollbackCache(context);

            salesOrder = await context.read(xtremSales.nodes.SalesOrder, { _id: '#SO32' }, { forUpdate: true });
            // check SO has two lines
            assert.equal(await salesOrder.lines.length, 2);
            // check 1st line statuses
            assert.equal(await (await salesOrder.lines.elementAt(0)).status, 'closed');
            assert.equal(await (await salesOrder.lines.elementAt(0)).invoiceStatus, 'notInvoiced');
            assert.equal(await (await salesOrder.lines.elementAt(0)).shippingStatus, 'shipped');
            // check 2nd line statuses
            assert.equal(await (await salesOrder.lines.elementAt(1)).status, 'pending');
            assert.equal(await (await salesOrder.lines.elementAt(1)).invoiceStatus, 'notInvoiced');
            assert.equal(await (await salesOrder.lines.elementAt(1)).shippingStatus, 'notShipped');
            // check header statuses
            assert.equal(await salesOrder.status, 'pending');
            assert.equal(await salesOrder.invoiceStatus, 'notInvoiced');
            assert.equal(await salesOrder.shippingStatus, 'partiallyShipped');
            assert.equal(await salesOrder.displayStatus, 'partiallyShipped');

            // delete the 2nd SO line
            await salesOrder.$.set({ lines: [{ _id: 1209, _action: 'delete' }] });
            await salesOrder.$.save();

            salesOrder = await context.read(xtremSales.nodes.SalesOrder, { _id: '#SO32' }, { forUpdate: true });
            // check one unique line
            assert.equal(await salesOrder.lines.length, 1);
            // check line statuses
            assert.equal(await (await salesOrder.lines.elementAt(0)).status, 'closed');
            assert.equal(await (await salesOrder.lines.elementAt(0)).invoiceStatus, 'notInvoiced');
            assert.equal(await (await salesOrder.lines.elementAt(0)).shippingStatus, 'shipped');
            // check header statuses
            assert.equal(await salesOrder.status, 'closed');
            assert.equal(await salesOrder.invoiceStatus, 'notInvoiced');
            assert.equal(await salesOrder.shippingStatus, 'shipped');
            assert.equal(await salesOrder.displayStatus, 'shipped');
        }));

    it('Create sales order default dimensions ans attributes', () =>
        Test.withContext(async context => {
            const salesOrder = await context.create(xtremSales.nodes.SalesOrder, {
                site: '#700',
                stockSite: '#700',
                billToCustomer: '#US023',
                date: date.today(),
                expectedDeliveryDate: date.today().addDays(3),
                shippingDate: date.today().addDays(1),
                requestedDeliveryDate: date.today().addDays(3),
                doNotShipBeforeDate: date.today(),
                doNotShipAfterDate: date.today().addDays(3),
                soldToCustomer: '#US023',
                lines: [
                    {
                        item: '#Muesli',
                        quantityInStockUnit: 500,
                        quantity: 500,
                        unit: '#GRAM',
                        grossPrice: 1.2,
                        shippingDate: date.today().addDays(1),
                    },
                ],
            });
            await salesOrder.$.save();

            assert.equal(
                JSON.stringify(await (await salesOrder.lines.elementAt(0)).storedDimensions),
                JSON.stringify({
                    dimensionType01: '300',
                    dimensionType02: 'CHANNELVALUE1',
                    dimensionType03: 'MANUFACTURING',
                    dimensionType04: 'DIMTYPE2VALUE2',
                }),
            );

            assert.equal(
                JSON.stringify(await (await salesOrder.lines.elementAt(0)).storedAttributes),
                JSON.stringify({
                    project: 'AttPROJ',
                    task: 'Task1',
                }),
            );
        }));

    it('Update tax type on tax node fails', () =>
        Test.withContext(async context => {
            // this test would normally be part of the tax package, but it is here because the control to test
            // is checking the BaseTax node which is in the tax package but abstract and therefore not accessible

            // read existing tax with is referenced in BaseTax
            const tax = await context.read(
                xtremTax.nodes.Tax,
                { _id: '#FR_TVA_EXEMPT_COLLECTED_ON_DEBITS' },
                { forUpdate: true },
            );
            await tax.$.set({ type: 'purchasingAndSales' });
            await assert.isRejected(tax.$.save());
            assert.deepEqual(tax.$.context.diagnoses, [
                {
                    severity: 3,
                    path: [],
                    message: 'You cannot edit the tax type. It is already used in a document.',
                },
            ]);
            await assert.isRejected(tax.$.save(), 'The record was not updated.');
        }));

    it('Update tax type on tax node can be done because Intacct is active', () =>
        Test.withContext(
            async context => {
                // this test would normally be part of the tax package, but it is here because the control to test
                // is checking the BaseTax node which is in the tax package but abstract and therefore not accessible

                // read existing tax with is referenced in BaseTax
                const tax = await context.read(
                    xtremTax.nodes.Tax,
                    { _id: '#FR_TVA_EXEMPT_COLLECTED_ON_DEBITS' },
                    { forUpdate: true },
                );
                await tax.$.set({ type: 'purchasingAndSales' });
                await tax.$.save();
            },
            { testActiveServiceOptions: [xtremStructure.serviceOptions.intacctActivationOption] },
        ));

    it('Control on sales order with wrong tax type', () =>
        Test.withContext(async context => {
            const tax = await context.read(xtremTax.nodes.Tax, { _id: '#FR_TVA_NORMAL_DEDUCTIBLE_ON_FA' });
            const salesOrder = await context.create(xtremSales.nodes.SalesOrder, {
                site: '#700',
                stockSite: '#700',
                billToCustomer: '#US023',
                date: date.today(),
                expectedDeliveryDate: date.today().addDays(3),
                shippingDate: date.today().addDays(1),
                requestedDeliveryDate: date.today().addDays(3),
                doNotShipBeforeDate: date.today(),
                doNotShipAfterDate: date.today().addDays(3),
                soldToCustomer: '#US023',
                lines: [
                    {
                        item: '#Muesli',
                        quantityInStockUnit: 500,
                        quantity: 500,
                        unit: '#GRAM',
                        grossPrice: 1.2,
                        shippingDate: date.today().addDays(1),
                        taxes: [
                            {
                                taxReference: tax,
                                taxRate: 20,
                                taxAmount: 10,
                            },
                        ],
                    },
                ],
            });

            await assert.isRejected(salesOrder.$.save());
            assert.deepEqual(salesOrder.$.context.diagnoses, [
                {
                    severity: 3,
                    path: [],
                    message: "The tax type for all documents needs to be 'Sales' or 'Purchasing and sales'.",
                },
            ]);
        }));

    it('Control on sales order with correct tax type', () =>
        Test.withContext(async context => {
            const tax = await context.read(xtremTax.nodes.Tax, { _id: '#FR_TVA_INTERMEDIATE_COLLECTED_ON_PAYMENT' });
            const salesOrder = await context.create(xtremSales.nodes.SalesOrder, {
                site: '#700',
                stockSite: '#700',
                billToCustomer: '#US023',
                date: date.today(),
                expectedDeliveryDate: date.today().addDays(3),
                shippingDate: date.today().addDays(1),
                requestedDeliveryDate: date.today().addDays(3),
                doNotShipBeforeDate: date.today(),
                doNotShipAfterDate: date.today().addDays(3),
                soldToCustomer: '#US023',
                lines: [
                    {
                        item: '#Muesli',
                        quantityInStockUnit: 500,
                        quantity: 500,
                        unit: '#GRAM',
                        grossPrice: 1.2,
                        shippingDate: date.today().addDays(1),
                        taxes: [{ taxReference: tax, taxRate: 20, taxAmount: 10 }],
                    },
                ],
            });

            assert.isOk(await salesOrder.$.control());
        }));

    it('Create sales order with stock transfer data must fails', () =>
        Test.withContext(async context => {
            const salesOrder = await context.create(xtremSales.nodes.SalesOrder, {
                site: '#CAS01',
                stockSite: '#CAS02',
                soldToCustomer: '#CAS02',
                requestedDeliveryDate: date.today().addDays(3),
                lines: [{ item: '#Milk', quantity: 10 }],
            });
            await assert.isRejected(salesOrder.$.save());
            assert.deepEqual(salesOrder.$.context.diagnoses, [
                {
                    severity: 3,
                    path: ['soldToCustomer'],
                    message:
                        'This value is linked to a stock transfer document and cannot be used for this document. You need to select a different value.',
                },
            ]);
        }));
    it('Confirm sales order - OK', () =>
        Test.withContext(async context => {
            const salesOrder = await context.read(xtremSales.nodes.SalesOrder, { _id: '#SO17' }, { forUpdate: true });

            assert.equal(await salesOrder.lines.length, 1);

            // check 1st line statuses
            const salesOrderLine1 = await salesOrder.lines.elementAt(0);
            assert.equal(await salesOrderLine1.status, 'quote');
            assert.equal(await salesOrderLine1.invoiceStatus, 'notInvoiced');
            assert.equal(await salesOrderLine1.shippingStatus, 'notShipped');

            // check statuses
            assert.equal(await salesOrder.status, 'quote');

            // confirm the SO
            const confirmedSalesOrder = await xtremSales.nodes.SalesOrder.confirmSalesOrder(context, salesOrder);

            // check 1st line statuses
            const confirmedSalesOrderLine1 = await confirmedSalesOrder.lines.elementAt(0);
            assert.equal(await confirmedSalesOrderLine1.status, 'pending');
            assert.equal(await confirmedSalesOrderLine1.invoiceStatus, 'notInvoiced');
            assert.equal(await confirmedSalesOrderLine1.shippingStatus, 'notShipped');

            // check statuses
            assert.equal(await confirmedSalesOrder.status, 'pending');
        }));

    it('Confirm sales order - Error - isSafeToRetry = False', () =>
        Test.withContext(async context => {
            const salesOrder = await context.read(xtremSales.nodes.SalesOrder, { _id: '#SO18' }, { forUpdate: true });

            assert.equal(await salesOrder.lines.length, 1);

            // check 1st line statuses
            assert.equal(await (await salesOrder.lines.elementAt(0)).status, 'pending');

            // check statuses
            assert.equal(await salesOrder.status, 'pending');

            // confirm the SO
            await assert.isRejected(
                xtremSales.nodes.SalesOrder.confirmSalesOrder(context, salesOrder),
                'The sales order is already confirmed.',
            );
        }));

    it('Confirm sales order - Error - isSafeToRetry = True ', () =>
        Test.withContext(async context => {
            const salesOrder = await context.read(xtremSales.nodes.SalesOrder, { _id: '#SO18' }, { forUpdate: true });

            assert.equal(await salesOrder.lines.length, 1);

            // check 1st line statuses
            assert.equal(await (await salesOrder.lines.elementAt(0)).status, 'pending');

            // check statuses
            assert.equal(await salesOrder.status, 'pending');

            const salesOrderAfterConfirm = await xtremSales.nodes.SalesOrder.confirmSalesOrder(
                context,
                salesOrder,
                true,
            );
            assert.equal(await salesOrderAfterConfirm.status, 'pending');
        }));
    it('create sales shipment from sales order - Error - isSafeToRetry = false ', () =>
        Test.withContext(async context => {
            const salesOrder = await context.read(
                xtremSales.nodes.SalesOrder,
                { _id: '#order-to-order-test' },
                { forUpdate: true },
            );
            const item = await context.read(xtremMasterData.nodes.Item, { _id: '#StockItem01' }, { forUpdate: true });
            await item.$.set({ status: 'inDevelopment', maximumSalesQuantity: 14000 });
            await item.$.save();
            assert.isEmpty(item.$.context.diagnoses);

            // should return the sales order without an error
            await assert.isRejected(
                xtremSales.nodes.SalesOrder.createShipmentsFromOrder(context, salesOrder),
                'You need to remove the inactive items before you change the document status.',
            );
        }));
    it('validates tax calculation status is not failed', () =>
        Test.withContext(async context => {
            await salesOrderPrintValidation(context, {
                validationFn: xtremSales.nodes.SalesOrder.beforePrintSalesOrder,
                properties: { taxCalculationStatus: 'failed' },
                expectedError:
                    'The tax calculation for this document failed. You need to correct the tax issues before you print the document.',
            });
        }));

    it('validates order must have at least one line', () =>
        Test.withContext(async (context: Context) => {
            const salesOrder = await readSalesOrder(context, '#SO18', { forUpdate: true });

            let emptyOrder = null;
            try {
                emptyOrder = await context.create(xtremSales.nodes.SalesOrder, {
                    date: date.today(),
                    soldToCustomer: await salesOrder.soldToCustomer,
                    billToCustomer: await salesOrder.billToCustomer,
                    shipToCustomer: await salesOrder.shipToCustomer,
                    site: await salesOrder.site,
                    stockSite: await salesOrder.stockSite,
                });

                await assert.isRejected(
                    xtremSales.nodes.SalesOrder.beforePrintSalesOrder(context, emptyOrder),
                    'There are no lines on this document. You can print the document after you add a line.',
                );
            } finally {
                if (emptyOrder) {
                    await emptyOrder.$.delete();
                }
            }
        }));

    it('Calculate Sales Order Status - 2 Closed Lines', () =>
        Test.withContext(async context => {
            const salesOrder = await context.read(xtremSales.nodes.SalesOrder, { _id: 'SO10' });
            assert.equal(await salesOrder.lines.length, 2);
            assert.equal(await (await salesOrder.lines.elementAt(0)).status, 'closed');
            assert.equal(await (await salesOrder.lines.elementAt(1)).status, 'closed');

            const taxCalculationStatus = 'done'; // Irrelevant for this test.

            const salesOrderStatuses = calculateSalesOrderStatuses(salesOrder.lines, taxCalculationStatus);
            assert.equal((await salesOrderStatuses).status, 'closed');
        }));
    it('Calculate Sales Order Status - 2 Pending Lines', () =>
        Test.withContext(async context => {
            const salesOrder = await context.read(xtremSales.nodes.SalesOrder, { _id: 'SO37' });
            assert.equal(await salesOrder.lines.length, 2);
            assert.equal(await (await salesOrder.lines.elementAt(0)).status, 'pending');
            assert.equal(await (await salesOrder.lines.elementAt(1)).status, 'pending');

            const taxCalculationStatus = 'done'; // Irrelevant for this test.

            const salesOrderStatuses = calculateSalesOrderStatuses(salesOrder.lines, taxCalculationStatus);
            assert.equal((await salesOrderStatuses).status, 'pending');
        }));
    it('Calculate Sales Order Status - 1 Closed Line, 1 Pending Line', () =>
        Test.withContext(async context => {
            const salesOrder = await context.read(xtremSales.nodes.SalesOrder, { _id: 'SO9' });
            assert.equal(await salesOrder.lines.length, 2);
            assert.equal(await (await salesOrder.lines.elementAt(0)).status, 'closed');
            assert.equal(await (await salesOrder.lines.elementAt(1)).status, 'pending');

            const taxCalculationStatus = 'done'; // Irrelevant for this test.

            const salesOrderStatuses = calculateSalesOrderStatuses(salesOrder.lines, taxCalculationStatus);
            assert.equal((await salesOrderStatuses).status, 'pending');
        }));
    it('Calculate Sales Order Status - 1 Closed Line, 1 InProgress Line', () =>
        Test.withContext(async context => {
            const salesOrder = await context.read(xtremSales.nodes.SalesOrder, { _id: 'SO38' });
            assert.equal(await salesOrder.lines.length, 2);
            assert.equal(await (await salesOrder.lines.elementAt(0)).status, 'closed');
            assert.equal(await (await salesOrder.lines.elementAt(1)).status, 'inProgress');

            const taxCalculationStatus = 'done'; // Irrelevant for this test.

            const salesOrderStatuses = calculateSalesOrderStatuses(salesOrder.lines, taxCalculationStatus);
            assert.equal((await salesOrderStatuses).status, 'inProgress');
        }));
    it('Calculate Sales Order Status - 2 Quote Lines', () =>
        Test.withContext(async context => {
            const salesOrder = await context.read(xtremSales.nodes.SalesOrder, { _id: 'SO32' });
            assert.equal(await salesOrder.lines.length, 2);
            assert.equal(await (await salesOrder.lines.elementAt(0)).status, 'quote');
            assert.equal(await (await salesOrder.lines.elementAt(1)).status, 'quote');

            const taxCalculationStatus = 'done'; // Irrelevant for this test.

            const salesOrderStatuses = calculateSalesOrderStatuses(salesOrder.lines, taxCalculationStatus);
            assert.equal((await salesOrderStatuses).status, 'quote');
        }));

    it('Create sales order - Work in progress site is stock site', () =>
        Test.withContext(async context => {
            const salesOrder = await createSalesNode(
                context,
                { number: 'test5' },
                { status: 'quote', shippingStatus: 'notShipped' },
                false,
                '#3542',
                '#CUST07',
                '#US002',
                '#US002',
                {
                    date: date.parse('2020-11-17', context.currentLocale as any),
                    expectedDeliveryDate: null,
                    shippingDate: null,
                    stockSite: '#US001',
                },
            );
            await salesOrder.$.save();
            assert.deepEqual(salesOrder.$.context.diagnoses, []);
            const wipSite = await (await (await salesOrder.lines.elementAt(0)).workInProgress)?.site;
            assert.equal(wipSite?._id, (await context.read(xtremSystem.nodes.Site, { _id: '#US001' }))._id);
        }));
});
