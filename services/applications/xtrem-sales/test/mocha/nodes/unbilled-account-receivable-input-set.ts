import type { Context, decimal, NodeCreateData } from '@sage/xtrem-core';
import { date, Test } from '@sage/xtrem-core';
import { assert } from 'chai';
import * as xtremSales from '../../../lib';

interface Document {
    _id: number;
    number: string;
    info: string;
    type: string;
}

type SalesDocuments =
    | xtremSales.nodes.SalesShipment
    | xtremSales.nodes.SalesInvoice
    | xtremSales.nodes.SalesCreditMemo
    | xtremSales.nodes.SalesReturnRequest
    | xtremSales.nodes.SalesReturnReceipt;

async function addDocument(documents: Document[], document: SalesDocuments, info: string): Promise<void> {
    documents.push({ _id: document._id, number: await document.number, info, type: document.$.factory.name });
}

function getSalesOrderData(currentDate: date): NodeCreateData<xtremSales.nodes.SalesOrder> {
    return {
        site: '#ETS2-S01',
        stockSite: '#ETS2-S01',
        soldToCustomer: '#Pastelaria moderna',
        date: currentDate,
        status: 'pending',
        requestedDeliveryDate: currentDate,
        lines: [
            {
                item: '#Milk',
                status: 'pending',
                site: '#ETS2-S01',
                stockSite: '#ETS2-S01',
                quantity: 40,
                grossPrice: 12,
                storedAttributes: null,
                storedDimensions: { dimensionType01: '300' },
            },
        ],
    };
}

async function createSalesShipment(context: Context): Promise<xtremSales.nodes.SalesShipment> {
    const salesOrder = await context.create(xtremSales.nodes.SalesOrder, getSalesOrderData(date.today()));
    await salesOrder.$.save({ flushDeferredActions: true });
    const orderLines = await salesOrder.lines.toArray();
    await xtremSales.nodes.SalesOrder.createSalesShipmentsFromOrderLines(context, [
        { salesDocumentLine: orderLines[0], quantityToProcess: await orderLines.at(0)?.quantity },
    ]);
    const salesShipment = (
        await context.query(xtremSales.nodes.SalesShipment, { orderBy: { _id: 1 }, forUpdate: true }).toArray()
    ).pop();

    if (!salesShipment) {
        assert.fail('Sales shipment is empty.');
    }

    await salesShipment.$.set({
        status: 'shipped',
        lines: [{ _id: (await salesShipment.lines.elementAt(0))._id, status: 'shipped' }],
    });

    await salesShipment.$.save({ flushDeferredActions: true });

    return salesShipment;
}

async function invoiceShipment(
    context: Context,
    salesShipment: xtremSales.nodes.SalesShipment,
    quantityToProcess?: decimal,
): Promise<xtremSales.nodes.SalesInvoice> {
    const salesShipmentLines = (await salesShipment.lines
        .map(line => {
            return { salesDocumentLine: line, quantityToProcess };
        })
        .toArray()) as [{ salesDocumentLine: xtremSales.nodes.SalesShipmentLine; quantityToProcess?: decimal }];

    const salesShipmentLineToSalesInvoiceLine = (
        await xtremSales.nodes.SalesShipment.createSalesInvoicesFromShipmentLines(context, salesShipmentLines)
    ).documentsCreated[0] as xtremSales.nodes.SalesInvoice;
    const salesShipmentLineToSalesInvoiceLineSysId = salesShipmentLineToSalesInvoiceLine._id;
    let salesInvoice = await context.read(
        xtremSales.nodes.SalesInvoice,
        { _id: salesShipmentLineToSalesInvoiceLineSysId },
        { forUpdate: true },
    );

    await xtremSales.nodes.SalesInvoice.post(context, salesInvoice);

    // enforce the 'posted' status of the invoice (after post it is 'inProgress'). Normally the 'posted'
    // status is set asynchronously on the reply of the finance integration
    salesInvoice = await context.read(xtremSales.nodes.SalesInvoice, { _id: salesInvoice._id }, { forUpdate: true });
    await salesInvoice.$.set({ status: 'posted', forceUpdateForFinance: true });
    await salesInvoice.$.save({ flushDeferredActions: true });

    return salesInvoice;
}

async function creditInvoice(
    context: Context,
    salesInvoice: xtremSales.nodes.SalesInvoice,
    quantityToProcess?: decimal,
): Promise<xtremSales.nodes.SalesCreditMemo> {
    const toInvoiceLines = (await salesInvoice.lines
        .map(line => {
            return { salesDocumentLine: line, quantityToProcess };
        })
        .toArray()) as [{ salesDocumentLine: xtremSales.nodes.SalesInvoiceLine; quantityToProcess?: decimal }];

    const createdSalesCreditMemo = (
        await xtremSales.nodes.SalesInvoice.createSalesCreditMemosFromInvoiceLines(context, toInvoiceLines)
    ).documentsCreated[0] as xtremSales.nodes.SalesCreditMemo;
    const salesInvoiceLineToSalesCreditMemoSysId = createdSalesCreditMemo._id;
    const salesInvoiceLineToSalesCreditMemoLineSysId = (await createdSalesCreditMemo.lines.elementAt(0))._id;

    const salesCreditMemoLine2 = await context.read(
        xtremSales.nodes.SalesCreditMemoLine,
        { _id: salesInvoiceLineToSalesCreditMemoLineSysId },
        { forUpdate: true },
    );
    await salesCreditMemoLine2.$.set({ origin: 'return' });
    await salesCreditMemoLine2.$.save({ flushDeferredActions: true });

    const salesCreditMemo = await context.read(xtremSales.nodes.SalesCreditMemo, {
        _id: salesInvoiceLineToSalesCreditMemoSysId,
    });

    await xtremSales.nodes.SalesCreditMemo.post(context, salesCreditMemo);
    return salesCreditMemo;
}

async function createSalesReturnRequestFromShipment(
    context: Context,
    salesShipment: xtremSales.nodes.SalesShipment,
    quantityToProcess?: decimal,
): Promise<xtremSales.nodes.SalesReturnRequest> {
    const salesShipmentLines = (await salesShipment.lines
        .map(line => {
            return { salesDocumentLine: line, quantityToProcess };
        })
        .toArray()) as [{ salesDocumentLine: xtremSales.nodes.SalesShipmentLine; quantityToProcess?: decimal }];

    await xtremSales.nodes.SalesShipment.createSalesReturnRequestFromShipmentLines(context, salesShipmentLines);

    const salesShipmentLineToSalesReturnRequestLine = (
        await context
            .query(xtremSales.nodes.SalesShipmentLineToSalesReturnRequestLine, {
                filter: { linkedDocument: salesShipmentLines[0].salesDocumentLine._id },
                orderBy: { _id: 1 },
            })
            .toArray()
    ).pop();

    if (!salesShipmentLineToSalesReturnRequestLine) {
        assert.fail('Sales shipment line to sales return request line is empty.');
    }

    const salesReturnRequest = await context.read(xtremSales.nodes.SalesReturnRequest, {
        _id: (await (await salesShipmentLineToSalesReturnRequestLine.document).document)._id,
    });

    return salesReturnRequest;
}

async function createSalesReturnReceiptFromReturnRequest(
    context: Context,
    salesReturnRequest: xtremSales.nodes.SalesReturnRequest,
    quantityToProcess?: decimal,
): Promise<xtremSales.nodes.SalesReturnReceipt> {
    const receipt = await context.create(xtremSales.nodes.SalesReturnReceipt, {
        effectiveDate: date.today(),
        site: '#ETS2-S01',
        shipToCustomer: '#Pastelaria moderna',
        lines: [
            {
                item: '#Milk',
                quantityInStockUnit: await (await salesReturnRequest.lines.at(0))?.quantityInStockUnit,
                quantity: await (await salesReturnRequest.lines.at(0))?.quantity,
                storedDimensions: { dimensionType01: '300' },
                storedAttributes: null,
                toReturnRequestLines: [
                    {
                        linkedDocument: await salesReturnRequest.lines.at(0),
                        quantityInStockUnit: quantityToProcess,
                    },
                ],
            },
        ],
    });

    await receipt.$.save({ flushDeferredActions: true });
    await receipt.$.set({
        lines: [{ _id: (await receipt.lines.toArray())[0]._id, stockTransactionStatus: 'completed' }],
    });
    await receipt.$.save({ flushDeferredActions: true });

    return receipt;
}

/** Generates 2 shipment lines - return created documents */
async function createSalesShipments(context: Context): Promise<Document[]> {
    // Generates 2 shipment lines
    const documents: Document[] = [];

    // Sales shipment, shipped, fully invoiced and fully credited
    const salesShipment = await createSalesShipment(context);
    const salesShipmentInvoice = await invoiceShipment(context, salesShipment, 40);
    const salesShipMentInvoiceCreditMemo = await creditInvoice(context, salesShipmentInvoice, 40);
    await addDocument(documents, salesShipment, 'shipped, fully invoiced and fully credited');
    await addDocument(documents, salesShipmentInvoice, 'shipped, fully invoiced and fully credited');
    await addDocument(documents, salesShipMentInvoiceCreditMemo, 'shipped, fully invoiced and fully credited');

    // Sales shipment, shipped, partially returned
    const salesShipment2 = await createSalesShipment(context);
    const returnRequest = await createSalesReturnRequestFromShipment(context, salesShipment2, 20);
    const salesReturnReceipt = await createSalesReturnReceiptFromReturnRequest(context, returnRequest, 20);
    await addDocument(documents, salesShipment2, 'shipped and returned');
    await addDocument(documents, returnRequest, 'shipped and returned');
    await addDocument(documents, salesReturnReceipt, 'shipped and returned');

    // Sales shipment, shipped, partially invoiced and partially credited
    const salesShipment3 = await createSalesShipment(context);
    const salesShipmentInvoice3 = await invoiceShipment(context, salesShipment3, 20);
    const salesShipMentInvoiceCreditMemo3 = await creditInvoice(context, salesShipmentInvoice3, 10);
    const salesShipMentInvoiceCreditMemo4 = await creditInvoice(context, salesShipmentInvoice3, 10);
    await addDocument(documents, salesShipment3, 'shipped, partially invoiced and partially credited');
    await addDocument(documents, salesShipmentInvoice3, 'shipped, partially invoiced and partially credited');
    await addDocument(documents, salesShipMentInvoiceCreditMemo3, 'shipped, partially invoiced and partially credited');
    await addDocument(documents, salesShipMentInvoiceCreditMemo4, 'shipped, partially invoiced and partially credited');
    return documents;
}

describe('Unbilled accounts receivable inquiry', () => {
    it('asyncMutation unbilledAccountReceivableInquiry with shipment status Ready to process', () =>
        Test.withContext(async context => {
            let inputSet: xtremSales.nodes.UnbilledAccountReceivableInputSet = await context.create(
                xtremSales.nodes.UnbilledAccountReceivableInputSet,
                {
                    company: '#US001',
                    asOfDate: date.today(),
                    fromCustomer: '#US017',
                    toCustomer: '#US017',
                },
            );
            await inputSet.$.save();
            const user = await inputSet.user;
            await xtremSales.nodes.UnbilledAccountReceivableInputSet.unbilledAccountReceivableInquiry(
                context,
                user._id.toString(),
            );
            inputSet = await context.read(xtremSales.nodes.UnbilledAccountReceivableInputSet, { user });
            assert.deepEqual(await (await inputSet.user).email, await user.email);
            assert.deepEqual(await inputSet.lines.length, 0);
        }));

    it('asyncMutation unbilledAccountReceivableInquiry with shipment status shipped', () =>
        Test.withContext(async context => {
            let inputSet: xtremSales.nodes.UnbilledAccountReceivableInputSet = await context.create(
                xtremSales.nodes.UnbilledAccountReceivableInputSet,
                {
                    company: '#US001',
                    asOfDate: date.today(),
                    fromCustomer: '#US020',
                    toCustomer: '#US020',
                },
            );
            await inputSet.$.save();
            const user = await inputSet.user;
            await xtremSales.nodes.UnbilledAccountReceivableInputSet.unbilledAccountReceivableInquiry(
                context,
                user._id.toString(),
            );
            inputSet = await context.read(xtremSales.nodes.UnbilledAccountReceivableInputSet, { user });
            assert.deepEqual(await (await inputSet.user).email, await user.email);
            assert.deepEqual(await inputSet.lines.length, 1);
            assert.deepEqual(await (await inputSet.lines.elementAt(0)).invoiceIssuableQuantity, 10.0);
            assert.deepEqual(await (await inputSet.lines.elementAt(0)).invoiceIssuableAmount, 7000.0);
            assert.deepEqual(await (await inputSet.lines.elementAt(0)).invoiceIssuableAmountInCompanyCurrency, 6986.03);
            assert.deepEqual(
                await (
                    await inputSet.lines.elementAt(0)
                ).invoiceIssuableAmountInCompanyCurrencyAtAsOfDate,
                111090.0,
            );
        }));

    it('asyncMutation unbilledAccountReceivableInquiry - checking quantities', () =>
        Test.withContext(
            async context => {
                await createSalesShipments(context);

                let inputSet: xtremSales.nodes.UnbilledAccountReceivableInputSet = await context.create(
                    xtremSales.nodes.UnbilledAccountReceivableInputSet,
                    {
                        company: '#S1',
                        asOfDate: date.today(),
                        fromCustomer: '#Pastelaria moderna',
                        toCustomer: '#Pastelaria moderna',
                    },
                );
                await inputSet.$.save();
                const user = await inputSet.user;
                await xtremSales.nodes.UnbilledAccountReceivableInputSet.unbilledAccountReceivableInquiry(
                    context,
                    user._id.toString(),
                );
                inputSet = await context.read(xtremSales.nodes.UnbilledAccountReceivableInputSet, { user });

                assert.deepEqual(await (await inputSet.user).email, await user.email);
                assert.deepEqual(await inputSet.lines.length, 3);

                // verify quantities
                assert.equal(await (await inputSet.lines.elementAt(0)).quantity, 40);
                assert.equal(await (await inputSet.lines.elementAt(0)).invoicedQuantity, 40);
                assert.equal(await (await inputSet.lines.elementAt(0)).creditedQuantity, 40);
                assert.equal(await (await inputSet.lines.elementAt(0)).returnedQuantity, 0);

                const salesShipment = await context.read(xtremSales.nodes.SalesShipment, {
                    _id: await (await inputSet.lines.elementAt(0)).shipmentNumber,
                });

                // verify status
                assert.equal(await salesShipment.status, 'shipped');
                assert.equal(await salesShipment.invoiceStatus, 'invoiced');
            },
            { today: '2023-10-05' },
        ));
});
