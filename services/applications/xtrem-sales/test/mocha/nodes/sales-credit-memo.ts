// tslint:disable:no-duplicate-string
import type { Context } from '@sage/xtrem-core';
import { asyncArray, date, Test, TextStream } from '@sage/xtrem-core';
import * as xtremDateTime from '@sage/xtrem-date-time';
import * as xtremFinanceData from '@sage/xtrem-finance-data';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremReporting from '@sage/xtrem-reporting';
import * as xtremStructure from '@sage/xtrem-structure';
import * as xtremTax from '@sage/xtrem-tax';
import { assert } from 'chai';
import * as sinon from 'sinon';
import * as xtremSales from '../../../index';

const { withSequenceNumberContext } = xtremMasterData.functions.testLib;

describe('SalesCreditMemoNode', () => {
    before(() => {});

    it('Update sales creditMemo that is posted', () =>
        Test.withContext(async context => {
            const salesCreditMemoLine = await context.read(
                xtremSales.nodes.SalesCreditMemoLine,
                { _id: '#SCE1210006|160100' },
                { forUpdate: true },
            );

            await salesCreditMemoLine.$.set({ grossPrice: 2.2 });
            await assert.isRejected(salesCreditMemoLine.$.save(), 'The record was not updated.');
            assert.deepEqual(salesCreditMemoLine.$.context.diagnoses, [
                {
                    severity: 3,
                    path: [],
                    message: 'The sales credit memo is posted. You cannot update it.',
                },
            ]);
        }));

    it('Update sales invoice creditStatus to not credited status XT-8822', () =>
        Test.withContext(async context => {
            const salesInvoice: xtremSales.nodes.SalesInvoice = await context.read(
                xtremSales.nodes.SalesInvoice,
                { _id: '#SI3' },
                { forUpdate: true },
            );
            await xtremSales.nodes.SalesInvoice.post(context, salesInvoice);

            // enforce the 'posted' status of the invoice (after post it is 'inProgress'). Normally the 'posted'
            // status is set asynchronously on the reply of the finance integration
            await salesInvoice.$.set({ status: 'posted', forceUpdateForFinance: true });
            await salesInvoice.$.save();

            const salesCreditMemosCreatorClass = await new xtremSales.classes.SalesCreditMemosCreator(
                context,
            ).prepareNodeCreateData([
                { salesDocumentLine: await salesInvoice.lines.elementAt(0), quantityToProcess: 10 },
            ]);
            const salesCreditMemosCreated = await salesCreditMemosCreatorClass.createSalesOutputDocuments();

            assert.isArray(salesCreditMemosCreated.documentsCreated);

            let salesInvoiceLine: xtremSales.nodes.SalesInvoiceLine = await context.read(
                xtremSales.nodes.SalesInvoiceLine,
                { _id: '#SI3|10' },
            );

            assert.equal(await salesInvoiceLine.creditStatus, 'credited');
            assert.equal(await (await salesInvoiceLine.document).creditStatus, 'credited');

            if (!salesCreditMemosCreated.documentsCreated) {
                assert.fail('Failed to create sales credit memos from the provided sales documents.');
            }
            const salesCreditMemo = await context.read(
                xtremSales.nodes.SalesCreditMemo,
                { _id: salesCreditMemosCreated.documentsCreated[0]._id },
                { forUpdate: true },
            );

            await salesCreditMemo.$.delete();

            salesInvoiceLine = await context.read(xtremSales.nodes.SalesInvoiceLine, { _id: '#SI3|10' });
            assert.equal(await salesInvoiceLine.creditStatus, 'notCredited');
            assert.equal(await (await salesInvoiceLine.document).creditStatus, 'notCredited');
        }));

    it('Credit partially credited invoice XT-8823', () =>
        Test.withContext(async context => {
            let salesInvoiceLine: xtremSales.nodes.SalesInvoiceLine = await context.read(
                xtremSales.nodes.SalesInvoiceLine,
                { _id: '#SI4|10' },
            );

            let salesInvoiceLine2: xtremSales.nodes.SalesInvoiceLine = await context.read(
                xtremSales.nodes.SalesInvoiceLine,
                { _id: '#SI4|20' },
            );

            const salesCreditMemosCreatorClass = await new xtremSales.classes.SalesCreditMemosCreator(
                context,
            ).prepareNodeCreateData([
                { salesDocumentLine: salesInvoiceLine, quantityToProcess: 10 },
                { salesDocumentLine: salesInvoiceLine2, quantityToProcess: 10 },
            ]);
            const salesCreditMemosCreated = await salesCreditMemosCreatorClass.createSalesOutputDocuments();
            assert.isArray(salesCreditMemosCreated.documentsCreated);

            salesInvoiceLine = await context.read(xtremSales.nodes.SalesInvoiceLine, { _id: '#SI4|10' });
            salesInvoiceLine2 = await context.read(xtremSales.nodes.SalesInvoiceLine, { _id: '#SI4|20' });
            assert.equal(await salesInvoiceLine.creditStatus, 'credited');
            assert.equal(await salesInvoiceLine2.creditStatus, 'credited');
            assert.equal(await (await salesInvoiceLine.document).creditStatus, 'credited');

            if (!salesCreditMemosCreated.documentsCreated) {
                assert.fail('Failed to create sales credit memos from the provided sales documents.');
            }
            let salesCreditMemo = await context.read(xtremSales.nodes.SalesCreditMemo, {
                _id: salesCreditMemosCreated.documentsCreated[0]._id,
            });

            assert.equal(await salesCreditMemo.lines.length, 2);
            salesCreditMemo = await context.read(
                xtremSales.nodes.SalesCreditMemo,
                { _id: salesCreditMemo._id },
                { forUpdate: true },
            );
            const salesCreditMemoLine = await salesCreditMemo.lines.elementAt(0);
            assert.equal(await salesCreditMemoLine.sourceDocumentNumber, 'SI4');
            assert.equal(await salesCreditMemoLine.sourceDocumentType, 'salesInvoice');
            await salesCreditMemo.$.set({
                lines: [
                    {
                        _id: (await salesCreditMemo.lines.elementAt(0))._id,
                        _action: 'delete',
                    },
                ],
            });

            await salesCreditMemo.$.save();

            salesInvoiceLine = await context.read(xtremSales.nodes.SalesInvoiceLine, { _id: '#SI4|10' });

            assert.equal(await salesInvoiceLine.creditStatus, 'notCredited');
            assert.equal(await (await salesInvoiceLine.document).creditStatus, 'partiallyCredited');

            const salesCreditMemosCreatorClass2 = await new xtremSales.classes.SalesCreditMemosCreator(
                context,
            ).prepareNodeCreateData([{ salesDocumentLine: salesInvoiceLine, quantityToProcess: 10 }]);
            assert.equal((await salesCreditMemosCreatorClass2.createSalesOutputDocuments()).numberOfCreditMemos, 1);
        }));

    it('Update sales credit memo 1', () =>
        Test.withContext(async context => {
            let salesShipment = await context.read(xtremSales.nodes.SalesShipment, { _id: '#SSH3' });
            assert.equal(await salesShipment.returnRequestStatus, 'noReturnRequested');
            assert.equal(await (await salesShipment.lines.elementAt(0)).returnRequestStatus, 'noReturnRequested');

            let salesReturnRequest = await context.read(
                xtremSales.nodes.SalesReturnRequest,
                { _id: '#SRR05' },
                { forUpdate: true },
            );

            await salesReturnRequest.$.set({
                creditStatus: 'notCredited',
                lines: [
                    {
                        _sortValue: await (await salesReturnRequest.lines.elementAt(0))._sortValue,
                        creditStatus: 'notCredited',
                        _action: 'update',
                    },
                ],
            });

            await salesReturnRequest.$.save();

            const salesCreditMemo = await context.read(
                xtremSales.nodes.SalesCreditMemo,
                { _id: '#SCE1210002' },
                { forUpdate: true },
            );

            await salesCreditMemo.$.set({
                lines: [
                    {
                        _sortValue: await (await salesCreditMemo.lines.elementAt(0))._sortValue,
                        quantity: 13,
                        _action: 'update',
                    },
                ],
            });

            await salesCreditMemo.$.save();

            const salesReturnRequestLine = await context.read(xtremSales.nodes.SalesReturnRequestLine, {
                _id: '#SRR05|10',
            });

            const { finalQuantityInSalesUnit, quantityReceived } =
                await xtremSales.classes.SalesReturnRequestCreator.getFinalQuantityInSalesUnit(
                    salesReturnRequestLine,
                    await salesReturnRequestLine.quantity,
                    context,
                );
            assert.equal(String(finalQuantityInSalesUnit), '14');
            assert.equal(String(quantityReceived), '13');

            salesReturnRequest = await context.read(xtremSales.nodes.SalesReturnRequest, {
                _id: '#SRR05',
            });

            assert.equal(await salesReturnRequest.creditStatus, 'partiallyCredited');
            assert.equal(await (await salesReturnRequest.lines.elementAt(0)).creditStatus, 'partiallyCredited');

            salesShipment = await context.read(xtremSales.nodes.SalesShipment, { _id: '#SSH3' });
            assert.equal(await salesShipment.returnRequestStatus, 'returnPartiallyRequested');
            assert.equal(
                await (
                    await salesShipment.lines.elementAt(0)
                ).returnRequestStatus,
                'returnPartiallyRequested',
            );
        }));

    it('Update sales credit memo 2', () =>
        Test.withContext(async context => {
            const salesReturnRequest = await context.read(
                xtremSales.nodes.SalesReturnRequest,
                { _id: '#SRR05' },
                { forUpdate: true },
            );

            await salesReturnRequest.$.set({
                creditStatus: 'notCredited',
                lines: [
                    {
                        _sortValue: await (await salesReturnRequest.lines.elementAt(0))._sortValue,
                        status: 'closed',
                        creditStatus: 'notCredited',
                        _action: 'update',
                    },
                ],
            });

            await salesReturnRequest.$.save();

            const salesCreditMemo = await context.read(
                xtremSales.nodes.SalesCreditMemo,
                { _id: '#SCE1210002' },
                { forUpdate: true },
            );
            await salesCreditMemo.$.set({
                lines: [
                    {
                        _sortValue: await (await salesCreditMemo.lines.elementAt(0))._sortValue,
                        quantity: 13,
                        _action: 'update',
                    },
                ],
            });

            await salesCreditMemo.$.save();

            const salesReturnRequestLine = await context.read(xtremSales.nodes.SalesReturnRequestLine, {
                _id: '#SRR05|10',
            });
            const { finalQuantityInSalesUnit, quantityReceived } =
                await xtremSales.classes.SalesReturnRequestCreator.getFinalQuantityInSalesUnit(
                    salesReturnRequestLine,
                    await salesReturnRequestLine.quantity,
                    context,
                );
            assert.equal(String(finalQuantityInSalesUnit), '13');
            assert.equal(String(quantityReceived), '13');

            const salesShipment = await context.read(xtremSales.nodes.SalesShipment, {
                _id: '#SSH3',
            });
            assert.equal(await salesShipment.returnRequestStatus, 'returnPartiallyRequested');
            assert.equal(
                await (
                    await salesShipment.lines.elementAt(0)
                ).returnRequestStatus,
                'returnPartiallyRequested',
            );
        }));

    it('Update sales credit memo 3', () =>
        Test.withContext(async context => {
            const salesReturnRequest = await context.read(
                xtremSales.nodes.SalesReturnRequest,
                { _id: '#SRR05' },
                { forUpdate: true },
            );

            await salesReturnRequest.$.set({
                creditStatus: 'notCredited',
                approvalStatus: 'draft',
                lines: [
                    {
                        _sortValue: await (await salesReturnRequest.lines.elementAt(0))._sortValue,
                        creditStatus: 'notCredited',
                        _action: 'update',
                    },
                ],
            });

            await salesReturnRequest.$.save();

            const salesCreditMemo = await context.read(
                xtremSales.nodes.SalesCreditMemo,
                { _id: '#SCE1210002' },
                { forUpdate: true },
            );

            await salesCreditMemo.$.set({
                lines: [
                    {
                        _sortValue: await (await salesCreditMemo.lines.elementAt(0))._sortValue,
                        quantity: 13,
                        _action: 'update',
                    },
                ],
            });

            await salesCreditMemo.$.save();

            const salesReturnRequestLine = await context.read(xtremSales.nodes.SalesReturnRequestLine, {
                _id: '#SRR05|10',
            });

            const { finalQuantityInSalesUnit, quantityReceived } =
                await xtremSales.classes.SalesReturnRequestCreator.getFinalQuantityInSalesUnit(
                    salesReturnRequestLine,
                    await salesReturnRequestLine.quantity,
                    context,
                );
            assert.equal(String(finalQuantityInSalesUnit), '14');
            assert.equal(String(quantityReceived), '0');
        }));

    it('Update sales credit memo 4', () =>
        Test.withContext(async context => {
            const salesReturnRequest = await context.read(
                xtremSales.nodes.SalesReturnRequest,
                { _id: '#SRR05' },
                { forUpdate: true },
            );

            await salesReturnRequest.$.set({
                creditStatus: 'notCredited',
                lines: [
                    {
                        _sortValue: await (await salesReturnRequest.lines.elementAt(0))._sortValue,
                        status: 'closed',
                        creditStatus: 'notCredited',
                        isReceiptExpected: true,
                        _action: 'update',
                    },
                ],
            });

            await salesReturnRequest.$.save();

            let salesCreditMemo = await context.read(
                xtremSales.nodes.SalesCreditMemo,
                { _id: '#SCE1210002' },
                { forUpdate: true },
            );
            await salesCreditMemo.$.set({
                lines: [
                    {
                        _sortValue: await (await salesCreditMemo.lines.elementAt(0))._sortValue,
                        quantity: 14,
                        _action: 'update',
                    },
                ],
            });

            await assert.isRejected(
                salesCreditMemo.$.save(),
                'The sales document line quantity cannot be larger than the remaining quantity in the related document.',
            );

            salesCreditMemo = await context.read(
                xtremSales.nodes.SalesCreditMemo,
                { _id: '#SCE1210002' },
                { forUpdate: true },
            );

            await salesCreditMemo.$.set({
                lines: [
                    {
                        _sortValue: await (await salesCreditMemo.lines.elementAt(0))._sortValue,
                        quantity: 10,
                        _action: 'update',
                    },
                ],
            });

            await salesCreditMemo.$.save();

            const salesReturnRequestLine = await context.read(xtremSales.nodes.SalesReturnRequestLine, {
                _id: '#SRR05|10',
            });

            const { finalQuantityInSalesUnit, quantityReceived } =
                await xtremSales.classes.SalesReturnRequestCreator.getFinalQuantityInSalesUnit(
                    salesReturnRequestLine,
                    await salesReturnRequestLine.quantity,
                    context,
                );
            assert.equal(String(finalQuantityInSalesUnit), '13');
            assert.equal(String(quantityReceived), '13');

            const salesShipment = await context.read(xtremSales.nodes.SalesShipment, {
                _id: '#SSH3',
            });
            assert.equal(await salesShipment.returnRequestStatus, 'returnPartiallyRequested');
            assert.equal(
                await (
                    await salesShipment.lines.elementAt(0)
                ).returnRequestStatus,
                'returnPartiallyRequested',
            );
        }));
    it('Get sales credit memo line taxes in UI version', () =>
        Test.withContext(async context => {
            const salesCreditMemo = await context.tryRead(xtremSales.nodes.SalesCreditMemo, { _id: '#SCE1210005' });

            const uiTaxes = await (await salesCreditMemo!.lines.elementAt(0)).uiTaxes;

            assert.equal(uiTaxes!.taxes[0].taxRate, 10);
        }));

    async function createSalesCreditMemoFrLeg(
        context: Context,
        creditDate: xtremDateTime.DateValue, // No shadowing of the date type.
        taxReference: string = '#FR_TVA_REDUCED_COLLECTED_ON_PAYMENT',
        number?: string,
    ): Promise<xtremSales.nodes.SalesCreditMemo> {
        const taxSolution = await context.read(xtremTax.nodes.TaxSolution, { _id: '#FRSOL' }, { forUpdate: true });
        if (await taxSolution.lines.at(1)) {
            await taxSolution.$.set({
                lines: [
                    {
                        _action: 'delete',
                        _id: (await taxSolution.lines.at(1))?._id,
                    },
                ],
            });
            await taxSolution.$.save();
        }

        return context.create(xtremSales.nodes.SalesCreditMemo, {
            lines: [
                {
                    _action: 'create',
                    charge: 0,
                    consumptionLinkedAddress: '#US019|1500',
                    discount: 0,
                    grossPrice: 20,
                    item: '#SalesItem81',
                    itemDescription: 'Sales Item 81',
                    netPrice: 20,
                    origin: 'direct',
                    providerSite: '#ETS1-S01',
                    quantity: 20,
                    quantityInStockUnit: 0.02,
                    site: '#ETS1-S01',
                    unit: { id: 'MILLILITER' },
                    unitToStockUnitConversionFactor: 0.001,
                    stockUnit: { id: 'LITER' },
                    amountExcludingTax: 400,
                    taxes: [
                        {
                            _action: 'create',
                            taxReference,
                            taxRate: 0,
                        },
                    ],
                    amountIncludingTax: 400,
                    storedAttributes: { project: '', task: '', employee: '' },
                    storedDimensions: { dimensionType01: '300' },
                },
            ],
            billToAddress: {
                country: { id: 'US' },
                name: 'Cust address',
                locationPhoneNumber: '+15550123456',
                addressLine1: '1st Avenue',
                addressLine2: '',
                city: 'One',
                region: 'Some region',
                postcode: '0001',
            },
            billToLinkedAddress: '#US019|1500',
            currency: { id: 'USD' },
            incoterm: null,
            paymentTerm: '#DUE_UPON_RECEIPT_ALL',
            billToCustomer: '#US019',
            site: '#ETS1-S01',
            number,
            date: creditDate,
            dueDate: xtremDateTime.date.make(2022, 6, 29),
            totalAmountExcludingTax: 400,
            totalAmountIncludingTax: 400,
            isPrinted: false,
            status: 'draft',
            taxCalculationStatus: 'done',
        });
    }

    async function createSalesCreditMemoFrLegAttributes(
        context: Context,
        creditDate: xtremDateTime.DateValue, // No shadowing of the date type.
        number?: string,
    ): Promise<xtremSales.nodes.SalesCreditMemo> {
        const taxSolution = await context.read(xtremTax.nodes.TaxSolution, { _id: '#FRSOL' }, { forUpdate: true });
        if (await taxSolution.lines.at(1)) {
            await taxSolution.$.set({
                lines: [
                    {
                        _action: 'delete',
                        _id: (await taxSolution.lines.at(1))?._id,
                    },
                ],
            });
            await taxSolution.$.save();
        }

        return context.create(xtremSales.nodes.SalesCreditMemo, {
            lines: [
                {
                    _action: 'create',
                    charge: 0,
                    consumptionLinkedAddress: '#US019|1500',
                    discount: 0,
                    grossPrice: 20,
                    item: '#SalesItem81',
                    itemDescription: 'Sales Item 81',
                    netPrice: 20,
                    origin: 'direct',
                    providerSite: '#ETS1-S01',
                    quantity: 20,
                    quantityInStockUnit: 0.02,
                    site: '#ETS1-S01',
                    unit: { id: 'MILLILITER' },
                    unitToStockUnitConversionFactor: 0.001,
                    stockUnit: { id: 'LITER' },
                    amountExcludingTax: 400,
                    taxes: [
                        {
                            _action: 'create',
                            taxReference: await context.read(xtremTax.nodes.Tax, {
                                _id: '#FR_TVA_REDUCED_COLLECTED_ON_PAYMENT',
                            }),
                            taxRate: 0,
                        },
                    ],
                    amountIncludingTax: 400,
                    storedAttributes: { project: '', task: 'TASK1', employee: '' },
                    storedDimensions: { dimensionType01: '300' },
                },
            ],
            billToAddress: {
                country: { id: 'US' },
                name: 'Cust address',
                locationPhoneNumber: '+15550123456',
                addressLine1: '1st Avenue',
                addressLine2: '',
                city: 'One',
                region: 'Some region',
                postcode: '0001',
            },
            billToLinkedAddress: '#US019|1500',
            currency: '#USD',
            incoterm: null,
            paymentTerm: '#DUE_UPON_RECEIPT_ALL',
            billToCustomer: '#US019',
            site: '#ETS1-S01',
            number,
            date: creditDate,
            dueDate: xtremDateTime.date.make(2022, 6, 29),
            totalAmountExcludingTax: 400,
            totalAmountIncludingTax: 400,
            isPrinted: false,
            status: 'draft',
            taxCalculationStatus: 'done',
        });
    }

    it('Create and validate sales credit memo for FR legislation with correct date', () =>
        Test.withContext(async context => {
            const salesCreditMemo1 = await createSalesCreditMemoFrLeg(context, xtremDateTime.date.make(2022, 1, 1));
            await salesCreditMemo1.$.save();

            const salesCreditMemo2 = await createSalesCreditMemoFrLeg(context, xtremDateTime.date.make(2022, 1, 2));
            await salesCreditMemo2.$.save({ flushDeferredActions: true });

            const salesCreditMemo2Posted: xtremSales.nodes.SalesCreditMemo = await context.read(
                xtremSales.nodes.SalesCreditMemo,
                { number: await salesCreditMemo2.number },
                { forUpdate: true },
            );
            await xtremSales.nodes.SalesCreditMemo.post(context, salesCreditMemo2Posted);
            await assert.isRejected(
                xtremSales.nodes.SalesCreditMemo.post(context, salesCreditMemo1),
                'The document date 2022-01-01 is earlier than the previous document date 2022-01-02.',
            );
        }));

    it('Test if final sequence number has chronological control active for FR legislation', () =>
        withSequenceNumberContext('PostedSalesCreditMemo', { isChronological: false }, async context => {
            const salesCreditMemo = await createSalesCreditMemoFrLeg(context, xtremDateTime.date.make(2022, 1, 1));
            await salesCreditMemo.$.save({ flushDeferredActions: true });

            await assert.isRejected(
                xtremSales.nodes.SalesCreditMemo.post(context, salesCreditMemo),
                'Sequence number PostedSalesCreditMemo: The chronological control must be active for the FR legislation.',
            );
        }));

    it('Test if final sequence number don`t have monthly reset level active for FR legislation', () =>
        withSequenceNumberContext('PostedSalesCreditMemo', { rtzLevel: 'monthly' }, async context => {
            const salesCreditMemo = await createSalesCreditMemoFrLeg(context, xtremDateTime.date.make(2022, 1, 1));
            await salesCreditMemo.$.save({ flushDeferredActions: true });

            await assert.isRejected(
                xtremSales.nodes.SalesCreditMemo.post(context, salesCreditMemo),
                'Sequence number PostedSalesCreditMemo: Monthly sequence numbers are not allowed.',
            );
        }));

    it('XT-18587 -> Issue with Credit button on sales invoice - Lines are missing', () =>
        Test.withContext(
            async context => {
                const salesInvoice = await context.read(
                    xtremSales.nodes.SalesInvoice,
                    { _id: '#SI1' },
                    { forUpdate: true },
                );

                const salesInvoiceFirstLine = await salesInvoice.lines.at(0);

                await salesInvoiceFirstLine!.$.set({ creditStatus: 'partiallyCredited', quantity: 100 });

                let i = 0;

                while (i < 12) {
                    await salesInvoice.$.set({
                        lines: [
                            {
                                _action: 'create',
                                origin: 'direct',
                                item: '#Consulting01',
                                quantity: await salesInvoiceFirstLine!.quantity,
                                siteLinkedAddress: '#US001|7100',
                                stockSiteLinkedAddress: '#US001|7100',
                            },
                        ],
                    });
                    i += 1;
                }
                await salesInvoice.$.set({ taxCalculationStatus: 'done' });
                await salesInvoice.$.save();
                await xtremSales.nodes.SalesInvoice.post(context, salesInvoice);

                assert.equal(
                    await (
                        await xtremSales.nodes.SalesInvoice.post(context, salesInvoice)
                    ).status,
                    'inProgress',
                );

                // enforce the 'posted' status of the invoice (after post it is 'inProgress'). Normally the 'posted'
                // status is set asynchronously on the reply of the finance integration
                await salesInvoice.$.set({
                    status: 'posted',
                    forceUpdateForFinance: true,
                });
                await salesInvoice.$.save();

                const salesCreditMemosCreated = await xtremSales.nodes.SalesInvoice.createSalesCreditMemosFromInvoices(
                    context,
                    [salesInvoice],
                );
                assert.isArray(salesCreditMemosCreated.documentsCreated);

                if (!salesCreditMemosCreated.documentsCreated) {
                    assert.fail('Failed to create sales credit memos from the sales invoices.');
                }
                const salesCreditMemo = await context.read(xtremSales.nodes.SalesCreditMemo, {
                    _id: salesCreditMemosCreated.documentsCreated[0]._id,
                });
                assert.equal(await salesCreditMemo.lines.length, 13);
            },
            { user: { email: '<EMAIL>' } },
        ));

    it('Sales credit memo print and send email fail - no report', () =>
        Test.withContext(async context => {
            const salesCreditMemo = await context.read(
                xtremSales.nodes.SalesCreditMemo,
                { _id: '#SCE1210004' },
                { forUpdate: true },
            );

            const sandbox = sinon.createSandbox();
            sandbox.stub(xtremReporting.nodes.Report, 'generateReports').returns(Promise.resolve([]));

            await assert.isRejected(
                xtremSales.nodes.SalesCreditMemo.printSalesCreditMemoAndEmail(
                    context,
                    salesCreditMemo,
                    'mr',
                    'John',
                    'Doe',
                    '<EMAIL>',
                ),
                'The sales credit memo cannot be sent. No report has been created.',
            );

            sandbox.restore();
        }));

    it('Create sales credit memo and repost', () =>
        Test.withContext(
            async context => {
                const salesInvoice: xtremSales.nodes.SalesInvoice = await context.read(
                    xtremSales.nodes.SalesInvoice,
                    { _id: '#SI3' },
                    { forUpdate: true },
                );
                await xtremSales.nodes.SalesInvoice.post(context, salesInvoice);

                // enforce the 'posted' status of the invoice (after post it is 'inProgress'). Normally the 'posted'
                // status is set asynchronously on the reply of the finance integration
                await salesInvoice.$.set({ status: 'posted', forceUpdateForFinance: true });
                await salesInvoice.$.save();

                const salesCreditMemosCreatorClass = await new xtremSales.classes.SalesCreditMemosCreator(
                    context,
                ).prepareNodeCreateData([
                    { salesDocumentLine: await salesInvoice.lines.elementAt(0), quantityToProcess: 10 },
                ]);
                const salesCreditMemosCreated = await salesCreditMemosCreatorClass.createSalesOutputDocuments();

                assert.isArray(salesCreditMemosCreated.documentsCreated);

                if (!salesCreditMemosCreated.documentsCreated) {
                    assert.fail('Failed to create sales credit memos from the provided sales documents.');
                }
                const salesCreditMemo = await context.read(
                    xtremSales.nodes.SalesCreditMemo,
                    { _id: salesCreditMemosCreated.documentsCreated[0]._id },
                    { forUpdate: true },
                );

                const salesCreditMemoLines = await asyncArray(await salesCreditMemo.lines.toArray())
                    .map(async line => {
                        return {
                            baseDocumentLineSysId: line._id,
                            storedAttributes:
                                (await line.storedAttributes) as xtremMasterData.interfaces.StoredAttributes,
                            storedDimensions: (await line.storedDimensions) || {},
                        };
                    })
                    .toArray();

                await assert.isRejected(
                    xtremSales.nodes.SalesCreditMemo.repost(context, salesCreditMemo, {
                        header: {},
                        lines: salesCreditMemoLines,
                    }),
                    "You can only repost a sales credit memo if the status is 'Failed' or 'Not recorded'.",
                );
            },
            { today: '2023-03-10' },
        ));
    it('Create sales credit memo from invoice to check grossprofit calculation', () =>
        Test.withContext(
            async context => {
                // This shipment contains a line with an average cost managed item, a FIFO managed item and a not managed in stock item but with a standard cost
                const invoice = await context.read(xtremSales.nodes.SalesInvoice, { _id: '#SI13' });

                await xtremSales.nodes.SalesInvoice.createSalesCreditMemosFromInvoices(context, [invoice]);

                const invoice2CreditMemoLines = await context
                    .query(xtremSales.nodes.SalesInvoiceLineToSalesCreditMemoLine, {
                        filter: { linkedDocument: { _in: await invoice.lines.map(e => e._id).toArray() } },
                    })
                    .toArray();

                assert.deepEqual(invoice2CreditMemoLines.length, await invoice.lines.length);
                // Check the grossProfit of the invoice
                await invoice.lines.forEach(async invoiceLine => {
                    const message = `invoice line _id=${invoiceLine._id}`;
                    const invoice2CreditMemo =
                        (await asyncArray(invoice2CreditMemoLines).find(
                            async link => (await link.linkedDocument)._id === invoiceLine._id,
                        )) ?? null;
                    assert.isNotNull(invoice2CreditMemo, message);
                    if (!invoice2CreditMemo) return;
                    const creditMemoLine = await context.read(xtremSales.nodes.SalesCreditMemoLine, {
                        _id: (await invoice2CreditMemo.document)._id,
                    });
                    assert.deepEqual(
                        await creditMemoLine.stockCostAmountInCompanyCurrency,
                        await invoiceLine.stockCostAmountInCompanyCurrency,
                        message,
                    );
                    assert.deepEqual(await creditMemoLine.grossProfit, await invoiceLine.grossProfit, message);
                    assert.deepEqual(
                        await creditMemoLine.grossProfitAmount,
                        await invoiceLine.grossProfitAmount,
                        message,
                    );
                });
                // Change the quantity of the credit memo and check the new gross profit
                const link =
                    (await asyncArray(invoice2CreditMemoLines).find(
                        async l => (await (await (await l.document).item).id) === 'STOAVC',
                    )) ?? null;
                assert.isNotNull(link);
                if (!link) return;
                const creditMemoLine = await link.document;

                await creditMemoLine.$.set({ quantity: 3 }); // Change quantity from 4 to 3
                await creditMemoLine.$.save();

                assert.deepEqual(Number(await creditMemoLine.amountExcludingTax), 1050);
                assert.deepEqual(Number(await creditMemoLine.stockCostAmountInCompanyCurrency), 963);
                assert.deepEqual(Number(await creditMemoLine.amountExcludingTaxInCompanyCurrency), Number(1047.9));
                assert.deepEqual(Number(await creditMemoLine.grossProfitAmount), Number(85.07));
            },
            { today: '2023-06-22' },
        ));

    it('Create sales credit memo from invoice - Note propagation both header and lines', () =>
        Test.withContext(async context => {
            const salesShipment = await context.read(
                xtremSales.nodes.SalesShipment,
                { _id: '#SSH4' },
                { forUpdate: true },
            );

            const line1 = await salesShipment.lines.elementAt(0);
            await line1.$.set({ status: 'shipped' });
            await salesShipment.$.save();

            // create sales return request
            const salesReturnRequestsCreated = await (
                await new xtremSales.classes.SalesReturnRequestCreator(context).prepareNodeCreateData([
                    { salesDocumentLine: line1, quantityToProcess: 10 },
                ])
            ).createSalesOutputDocuments();

            // create sales invoice
            const salesInvoicesCreated = await (
                await new xtremSales.classes.SalesInvoicesCreator(context).prepareNodeCreateData([
                    { salesDocumentLine: line1, quantityToProcess: 10 },
                ])
            ).createSalesOutputDocuments();

            assert.isArray(salesReturnRequestsCreated.documentsCreated);
            assert.isArray(salesInvoicesCreated.documentsCreated);

            if (!salesReturnRequestsCreated.documentsCreated) {
                assert.fail('Failed to create sales return requests from the provided sales documents.');
            }
            if (!salesInvoicesCreated.documentsCreated) {
                assert.fail('Failed to create sales invoices from the provided sales documents.');
            }
            // Approve sales return request
            const salesReturnRequest = await context.read(
                xtremSales.nodes.SalesReturnRequest,
                { _id: salesReturnRequestsCreated.documentsCreated[0]._id },
                { forUpdate: true },
            );
            await salesReturnRequest.$.set({
                approvalStatus: 'approved',
                lines: [
                    {
                        _id: (await salesReturnRequest.lines.elementAt(0))._id,
                        isReceiptExpected: false,
                        _action: 'update',
                    },
                ],
            });

            await salesReturnRequest.$.save();

            // Post sales invoice
            const salesInvoice = await context.read(
                xtremSales.nodes.SalesInvoice,
                { _id: salesInvoicesCreated.documentsCreated[0]._id },
                { forUpdate: true },
            );

            // set header notes
            await salesInvoice.$.set({
                taxCalculationStatus: 'done',
                isExternalNote: true,
                externalNote: TextStream.fromString('header external note'),
                internalNote: TextStream.fromString('header internal note'),
                isTransferHeaderNote: true,
                isTransferLineNote: true,
            });

            await salesInvoice.$.save();

            // set line notes
            const salesInvoiceLine = await context.read(
                xtremSales.nodes.SalesInvoiceLine,
                { _id: (await salesInvoice.lines.elementAt(0))._id },
                { forUpdate: true },
            );

            await salesInvoiceLine.$.set({
                isExternalNote: true,
                externalNote: TextStream.fromString('line external note'),
                internalNote: TextStream.fromString('line internal note'),
            });
            await salesInvoiceLine.$.save();

            await xtremSales.nodes.SalesInvoice.post(context, salesInvoice);

            await salesInvoice.$.set({ status: 'posted', forceUpdateForFinance: true });
            await salesInvoice.$.save();

            const salesCreditMemosCreated = await xtremSales.nodes.SalesInvoice.createSalesCreditMemosFromInvoices(
                context,
                [salesInvoice],
            );
            assert.isArray(salesCreditMemosCreated.documentsCreated);

            if (!salesCreditMemosCreated.documentsCreated) {
                assert.fail('Failed to create sales credit memos from the sales invoices.');
            }
            const salesCreditMemo = await context.read(
                xtremSales.nodes.SalesCreditMemo,
                { _id: salesCreditMemosCreated.documentsCreated[0]._id },
                { forUpdate: true },
            );

            // check if header notes same as invoice
            assert.equal((await salesCreditMemo.externalNote).toString(), (await salesInvoice.externalNote).toString());
            assert.equal((await salesCreditMemo.internalNote).toString(), (await salesInvoice.internalNote).toString());

            const salesCreditMemoLine = await context.read(
                xtremSales.nodes.SalesCreditMemoLine,
                { _id: (await salesCreditMemo.lines.elementAt(0))._id },
                { forUpdate: true },
            );

            // check if lines notes same as invoice
            assert.equal(
                (await salesCreditMemoLine.externalNote).toString(),
                (await salesInvoiceLine.externalNote).toString(),
            );
            assert.equal(
                (await salesCreditMemoLine.internalNote).toString(),
                (await salesInvoiceLine.internalNote).toString(),
            );
        }));

    it('Create sales credit memo from invoice - Note propagation only header', () =>
        Test.withContext(async context => {
            const salesShipment = await context.read(
                xtremSales.nodes.SalesShipment,
                { _id: '#SSH4' },
                { forUpdate: true },
            );

            const line1 = await salesShipment.lines.elementAt(0);
            await line1.$.set({ status: 'shipped' });
            await salesShipment.$.save();

            // create sales return request
            const salesReturnRequestsCreated = await (
                await new xtremSales.classes.SalesReturnRequestCreator(context).prepareNodeCreateData([
                    { salesDocumentLine: line1, quantityToProcess: 10 },
                ])
            ).createSalesOutputDocuments();

            // create sales invoice
            const salesInvoicesCreated = await (
                await new xtremSales.classes.SalesInvoicesCreator(context).prepareNodeCreateData([
                    { salesDocumentLine: line1, quantityToProcess: 10 },
                ])
            ).createSalesOutputDocuments();

            assert.isArray(salesReturnRequestsCreated.documentsCreated);
            assert.isArray(salesInvoicesCreated.documentsCreated);

            if (!salesReturnRequestsCreated.documentsCreated) {
                assert.fail('Failed to create sales return requests from the provided sales documents.');
            }
            if (!salesInvoicesCreated.documentsCreated) {
                assert.fail('Failed to create sales invoices from the provided sales documents.');
            }
            // Approve sales return request
            const salesReturnRequest = await context.read(
                xtremSales.nodes.SalesReturnRequest,
                { _id: salesReturnRequestsCreated.documentsCreated[0]._id },
                { forUpdate: true },
            );
            await salesReturnRequest.$.set({
                approvalStatus: 'approved',
                lines: [
                    {
                        _id: (await salesReturnRequest.lines.elementAt(0))._id,
                        isReceiptExpected: false,
                        _action: 'update',
                    },
                ],
            });

            await salesReturnRequest.$.save();

            // Post sales invoice
            const salesInvoice = await context.read(
                xtremSales.nodes.SalesInvoice,
                { _id: salesInvoicesCreated.documentsCreated[0]._id },
                { forUpdate: true },
            );

            // set header notes
            await salesInvoice.$.set({
                taxCalculationStatus: 'done',
                isExternalNote: true,
                externalNote: TextStream.fromString('header external note'),
                internalNote: TextStream.fromString('header internal note'),
                isTransferHeaderNote: true,
            });

            await salesInvoice.$.save();

            // set line notes
            const salesInvoiceLine = await context.read(
                xtremSales.nodes.SalesInvoiceLine,
                { _id: (await salesInvoice.lines.elementAt(0))._id },
                { forUpdate: true },
            );

            await salesInvoiceLine.$.set({
                isExternalNote: true,
                externalNote: TextStream.fromString('line external note'),
                internalNote: TextStream.fromString('line internal note'),
            });
            await salesInvoiceLine.$.save();

            await xtremSales.nodes.SalesInvoice.post(context, salesInvoice);

            await salesInvoice.$.set({ status: 'posted', forceUpdateForFinance: true });
            await salesInvoice.$.save();

            const salesCreditMemosCreated = await xtremSales.nodes.SalesInvoice.createSalesCreditMemosFromInvoices(
                context,
                [salesInvoice],
            );

            assert.isArray(salesCreditMemosCreated.documentsCreated);

            if (!salesCreditMemosCreated.documentsCreated) {
                assert.fail('Failed to create sales credit memos from the sales invoices.');
            }
            const salesCreditMemo = await context.read(
                xtremSales.nodes.SalesCreditMemo,
                { _id: salesCreditMemosCreated.documentsCreated[0]._id },
                { forUpdate: true },
            );

            // check if header notes same as invoice
            assert.equal((await salesCreditMemo.externalNote).toString(), (await salesInvoice.externalNote).toString());
            assert.equal((await salesCreditMemo.internalNote).toString(), (await salesInvoice.internalNote).toString());

            const salesCreditMemoLine = await context.read(
                xtremSales.nodes.SalesCreditMemoLine,
                { _id: (await salesCreditMemo.lines.elementAt(0))._id },
                { forUpdate: true },
            );

            // check lines notes are empty
            const invoiceInternalLineNote = (await salesCreditMemoLine.internalNote).toString();
            const invoiceIExternalLineNote = (await salesCreditMemoLine.externalNote).toString();
            assert.isEmpty(invoiceInternalLineNote);
            assert.isEmpty(invoiceIExternalLineNote);
        }));

    it('Create sales credit memo from invoice - Note propagation turned off', () =>
        Test.withContext(async context => {
            const salesShipment = await context.read(
                xtremSales.nodes.SalesShipment,
                { _id: '#SSH4' },
                { forUpdate: true },
            );

            const line1 = await salesShipment.lines.elementAt(0);
            await line1.$.set({ status: 'shipped' });
            await salesShipment.$.save();

            // create sales return request
            const salesReturnRequestsCreated = await (
                await new xtremSales.classes.SalesReturnRequestCreator(context).prepareNodeCreateData([
                    { salesDocumentLine: line1, quantityToProcess: 10 },
                ])
            ).createSalesOutputDocuments();

            // create sales invoice
            const salesInvoicesCreated = await (
                await new xtremSales.classes.SalesInvoicesCreator(context).prepareNodeCreateData([
                    { salesDocumentLine: line1, quantityToProcess: 10 },
                ])
            ).createSalesOutputDocuments();

            assert.isArray(salesReturnRequestsCreated.documentsCreated);
            assert.isArray(salesInvoicesCreated.documentsCreated);

            if (!salesReturnRequestsCreated.documentsCreated) {
                assert.fail('Failed to create sales return requests from the provided sales documents.');
            }
            if (!salesInvoicesCreated.documentsCreated) {
                assert.fail('Failed to create sales invoices from the provided sales documents.');
            }
            // Approve sales return request
            const salesReturnRequest = await context.read(
                xtremSales.nodes.SalesReturnRequest,
                { _id: salesReturnRequestsCreated.documentsCreated[0]._id },
                { forUpdate: true },
            );
            await salesReturnRequest.$.set({
                approvalStatus: 'approved',
                lines: [
                    {
                        _id: (await salesReturnRequest.lines.elementAt(0))._id,
                        isReceiptExpected: false,
                        _action: 'update',
                    },
                ],
            });

            await salesReturnRequest.$.save();

            // Post sales invoice
            const salesInvoice = await context.read(
                xtremSales.nodes.SalesInvoice,
                { _id: salesInvoicesCreated.documentsCreated[0]._id },
                { forUpdate: true },
            );

            // set header notes
            await salesInvoice.$.set({
                taxCalculationStatus: 'done',
                isExternalNote: true,
                externalNote: TextStream.fromString('header external note'),
                internalNote: TextStream.fromString('header internal note'),
            });

            await salesInvoice.$.save();

            // set line notes
            const salesInvoiceLine = await context.read(
                xtremSales.nodes.SalesInvoiceLine,
                { _id: (await salesInvoice.lines.elementAt(0))._id },
                { forUpdate: true },
            );

            await salesInvoiceLine.$.set({
                isExternalNote: true,
                externalNote: TextStream.fromString('line external note'),
                internalNote: TextStream.fromString('line internal note'),
            });
            await salesInvoiceLine.$.save();

            await xtremSales.nodes.SalesInvoice.post(context, salesInvoice);

            await salesInvoice.$.set({ status: 'posted', forceUpdateForFinance: true });
            await salesInvoice.$.save();

            const salesCreditMemosCreated = await xtremSales.nodes.SalesInvoice.createSalesCreditMemosFromInvoices(
                context,
                [salesInvoice],
            );

            assert.isArray(salesCreditMemosCreated.documentsCreated);

            if (!salesCreditMemosCreated.documentsCreated) {
                assert.fail('Failed to create sales credit memos from the sales invoices.');
            }
            const salesCreditMemo = await context.read(
                xtremSales.nodes.SalesCreditMemo,
                { _id: salesCreditMemosCreated.documentsCreated[0]._id },
                { forUpdate: true },
            );

            // check header notes are empty
            const creditMemoInternalNote = (await salesCreditMemo.internalNote).toString();
            const creditMemoIExternalNote = (await salesCreditMemo.externalNote).toString();
            assert.isEmpty(creditMemoInternalNote);
            assert.isEmpty(creditMemoIExternalNote);

            const salesCreditMemoLine = await context.read(
                xtremSales.nodes.SalesCreditMemoLine,
                { _id: (await salesCreditMemo.lines.elementAt(0))._id },
                { forUpdate: true },
            );

            // check lines notes are empty
            const creditMemoInternalLineNote = (await salesCreditMemoLine.internalNote).toString();
            const creditMemoIExternalLineNote = (await salesCreditMemoLine.externalNote).toString();
            assert.isEmpty(creditMemoInternalLineNote);
            assert.isEmpty(creditMemoIExternalLineNote);
        }));

    it('Create sales credit memo from sales return request - Note propagation both header and lines', () =>
        Test.withContext(async context => {
            const salesShipment = await context.read(
                xtremSales.nodes.SalesShipment,
                { _id: '#SSH4' },
                { forUpdate: true },
            );

            const line1 = await salesShipment.lines.elementAt(0);
            await line1.$.set({ status: 'shipped' });
            await salesShipment.$.save();

            // create sales return request
            const salesReturnRequestsCreated = await (
                await new xtremSales.classes.SalesReturnRequestCreator(context).prepareNodeCreateData([
                    { salesDocumentLine: line1, quantityToProcess: 10 },
                ])
            ).createSalesOutputDocuments();

            // create sales invoice
            const salesInvoicesCreated = await (
                await new xtremSales.classes.SalesInvoicesCreator(context).prepareNodeCreateData([
                    { salesDocumentLine: line1, quantityToProcess: 10 },
                ])
            ).createSalesOutputDocuments();

            if (!salesReturnRequestsCreated.documentsCreated) {
                assert.fail('Failed to create sales return requests from the provided sales documents.');
            }
            if (!salesInvoicesCreated.documentsCreated) {
                assert.fail('Failed to create sales invoices from the provided sales documents.');
            }
            // Approve sales return request
            const salesReturnRequest = await context.read(
                xtremSales.nodes.SalesReturnRequest,
                { _id: salesReturnRequestsCreated.documentsCreated[0]._id },
                { forUpdate: true },
            );
            await salesReturnRequest.$.set({
                approvalStatus: 'approved',
                receiptStatus: 'received',
                lines: [
                    {
                        _id: (await salesReturnRequest.lines.elementAt(0))._id,
                        receiptStatus: 'received',
                        creditStatus: 'notCredited',
                        isReceiptExpected: false,
                        _action: 'update',
                    },
                ],
            });

            await salesReturnRequest.$.save();

            // Post sales invoice
            const salesInvoice = await context.read(
                xtremSales.nodes.SalesInvoice,
                { _id: salesInvoicesCreated.documentsCreated[0]._id },
                { forUpdate: true },
            );

            // set header notes
            await salesInvoice.$.set({
                taxCalculationStatus: 'done',
                isExternalNote: true,
                externalNote: TextStream.fromString('header external note'),
                internalNote: TextStream.fromString('header internal note'),
                isTransferHeaderNote: true,
                isTransferLineNote: true,
            });

            await salesInvoice.$.save();

            // set line notes
            const salesInvoiceLine = await context.read(
                xtremSales.nodes.SalesInvoiceLine,
                { _id: (await salesInvoice.lines.elementAt(0))._id },
                { forUpdate: true },
            );

            await salesInvoiceLine.$.set({
                isExternalNote: true,
                externalNote: TextStream.fromString('line external note'),
                internalNote: TextStream.fromString('line internal note'),
            });
            await salesInvoiceLine.$.save();

            await xtremSales.nodes.SalesInvoice.post(context, salesInvoice);

            await salesInvoice.$.set({ status: 'posted', forceUpdateForFinance: true });
            await salesInvoice.$.save();

            const salesCreditMemosCreated =
                await xtremSales.nodes.SalesReturnRequest.createSalesCreditMemosFromReturnRequests(context, [
                    salesReturnRequest,
                ]);
            assert.isArray(salesCreditMemosCreated.documentsCreated);

            if (!salesCreditMemosCreated.documentsCreated) {
                assert.fail('Failed to create sales credit memos from the sales return requests.');
            }
            const salesCreditMemo = await context.read(
                xtremSales.nodes.SalesCreditMemo,
                { _id: salesCreditMemosCreated.documentsCreated[0]._id },
                { forUpdate: true },
            );

            // check if header notes same as invoice
            assert.equal((await salesCreditMemo.externalNote).toString(), (await salesInvoice.externalNote).toString());
            assert.equal((await salesCreditMemo.internalNote).toString(), (await salesInvoice.internalNote).toString());

            const salesCreditMemoLine = await context.read(
                xtremSales.nodes.SalesCreditMemoLine,
                { _id: (await salesCreditMemo.lines.elementAt(0))._id },
                { forUpdate: true },
            );

            // check if lines notes same as invoice
            assert.equal(
                (await salesCreditMemoLine.externalNote).toString(),
                (await salesInvoiceLine.externalNote).toString(),
            );
            assert.equal(
                (await salesCreditMemoLine.internalNote).toString(),
                (await salesInvoiceLine.internalNote).toString(),
            );
        }));

    it('Create sales credit memo from sales return request - Note propagation only header', () =>
        Test.withContext(async context => {
            const salesShipment = await context.read(
                xtremSales.nodes.SalesShipment,
                { _id: '#SSH4' },
                { forUpdate: true },
            );

            const line1 = await salesShipment.lines.elementAt(0);
            await line1.$.set({ status: 'shipped' });
            await salesShipment.$.save();

            // create sales return request
            const salesReturnRequestsCreated = await (
                await new xtremSales.classes.SalesReturnRequestCreator(context).prepareNodeCreateData([
                    { salesDocumentLine: line1, quantityToProcess: 10 },
                ])
            ).createSalesOutputDocuments();

            // create sales invoice
            const salesInvoicesCreated = await (
                await new xtremSales.classes.SalesInvoicesCreator(context).prepareNodeCreateData([
                    { salesDocumentLine: line1, quantityToProcess: 10 },
                ])
            ).createSalesOutputDocuments();

            assert.isArray(salesReturnRequestsCreated.documentsCreated);
            assert.isArray(salesInvoicesCreated.documentsCreated);

            if (!salesReturnRequestsCreated.documentsCreated) {
                assert.fail('Failed to create sales return requests from the provided sales documents.');
            }
            if (!salesInvoicesCreated.documentsCreated) {
                assert.fail('Failed to create sales invoices from the provided sales documents.');
            }
            // Approve sales return request
            const salesReturnRequest = await context.read(
                xtremSales.nodes.SalesReturnRequest,
                { _id: salesReturnRequestsCreated.documentsCreated[0]._id },
                { forUpdate: true },
            );
            await salesReturnRequest.$.set({
                approvalStatus: 'approved',
                lines: [
                    {
                        _id: (await salesReturnRequest.lines.elementAt(0))._id,
                        receiptStatus: 'received',
                        creditStatus: 'notCredited',
                        isReceiptExpected: false,
                        _action: 'update',
                    },
                ],
            });

            await salesReturnRequest.$.save();

            // Post sales invoice
            const salesInvoice = await context.read(
                xtremSales.nodes.SalesInvoice,
                { _id: salesInvoicesCreated.documentsCreated[0]._id },
                { forUpdate: true },
            );

            // set header notes
            await salesInvoice.$.set({
                taxCalculationStatus: 'done',
                isExternalNote: true,
                externalNote: TextStream.fromString('header external note'),
                internalNote: TextStream.fromString('header internal note'),
                isTransferHeaderNote: true,
            });
            await salesInvoice.$.save();

            // set line notes
            const salesInvoiceLine = await context.read(
                xtremSales.nodes.SalesInvoiceLine,
                { _id: (await salesInvoice.lines.elementAt(0))._id },
                { forUpdate: true },
            );

            await salesInvoiceLine.$.set({
                isExternalNote: true,
                externalNote: TextStream.fromString('line external note'),
                internalNote: TextStream.fromString('line internal note'),
            });
            await salesInvoiceLine.$.save();

            await xtremSales.nodes.SalesInvoice.post(context, salesInvoice);

            await salesInvoice.$.set({ status: 'posted', forceUpdateForFinance: true });
            await salesInvoice.$.save();

            const salesCreditMemosCreated =
                await xtremSales.nodes.SalesReturnRequest.createSalesCreditMemosFromReturnRequests(context, [
                    salesReturnRequest,
                ]);
            assert.isArray(salesCreditMemosCreated.documentsCreated);

            if (!salesCreditMemosCreated.documentsCreated) {
                assert.fail('Failed to create sales credit memos from the sales return requests.');
            }
            const salesCreditMemo = await context.read(
                xtremSales.nodes.SalesCreditMemo,
                { _id: salesCreditMemosCreated.documentsCreated[0]._id },
                { forUpdate: true },
            );

            // check if header notes same as invoice
            assert.equal((await salesCreditMemo.internalNote).toString(), (await salesInvoice.internalNote).toString());

            const salesCreditMemoLine = await context.read(
                xtremSales.nodes.SalesCreditMemoLine,
                { _id: (await salesCreditMemo.lines.elementAt(0))._id },
                { forUpdate: true },
            );

            // check lines notes are empty
            const invoiceInternalLineNote = (await salesCreditMemoLine.internalNote).toString();
            const invoiceIExternalLineNote = (await salesCreditMemoLine.externalNote).toString();
            assert.isEmpty(invoiceInternalLineNote);
            assert.isEmpty(invoiceIExternalLineNote);
        }));

    it('Create sales credit memo from sales return request - Note propagation turned off', () =>
        Test.withContext(async context => {
            const salesShipment = await context.read(xtremSales.nodes.SalesShipment, { _id: '4' }, { forUpdate: true });

            const line1 = await salesShipment.lines.elementAt(0);
            await line1.$.set({ status: 'shipped' });
            await salesShipment.$.save();

            // create sales return request
            const salesReturnRequestsCreated = await (
                await new xtremSales.classes.SalesReturnRequestCreator(context).prepareNodeCreateData([
                    { salesDocumentLine: line1, quantityToProcess: 10 },
                ])
            ).createSalesOutputDocuments();

            // create sales invoice
            const salesInvoicesCreated = await (
                await new xtremSales.classes.SalesInvoicesCreator(context).prepareNodeCreateData([
                    { salesDocumentLine: line1, quantityToProcess: 10 },
                ])
            ).createSalesOutputDocuments();

            assert.isArray(salesReturnRequestsCreated.documentsCreated);
            assert.isArray(salesInvoicesCreated.documentsCreated);

            if (!salesReturnRequestsCreated.documentsCreated) {
                assert.fail('Failed to create sales return requests from the provided sales documents.');
            }
            if (!salesInvoicesCreated.documentsCreated) {
                assert.fail('Failed to create sales invoices from the provided sales documents.');
            }
            // Approve sales return request
            const salesReturnRequest = await context.read(
                xtremSales.nodes.SalesReturnRequest,
                { _id: salesReturnRequestsCreated.documentsCreated[0]._id },
                { forUpdate: true },
            );
            await salesReturnRequest.$.set({
                approvalStatus: 'approved',
                lines: [
                    {
                        _id: (await salesReturnRequest.lines.elementAt(0))._id,
                        receiptStatus: 'received',
                        creditStatus: 'notCredited',
                        isReceiptExpected: false,
                        _action: 'update',
                    },
                ],
            });

            await salesReturnRequest.$.save();

            // Post sales invoice
            const salesInvoice = await context.read(
                xtremSales.nodes.SalesInvoice,
                { _id: salesInvoicesCreated.documentsCreated[0]._id },
                { forUpdate: true },
            );

            // set header notes
            await salesInvoice.$.set({
                taxCalculationStatus: 'done',
                isExternalNote: true,
                externalNote: TextStream.fromString('header external note'),
                internalNote: TextStream.fromString('header internal note'),
            });

            await salesInvoice.$.save();

            // set line notes
            const salesInvoiceLine = await context.read(
                xtremSales.nodes.SalesInvoiceLine,
                { _id: (await salesInvoice.lines.elementAt(0))._id },
                { forUpdate: true },
            );

            await salesInvoiceLine.$.set({
                isExternalNote: true,
                externalNote: TextStream.fromString('line external note'),
                internalNote: TextStream.fromString('line internal note'),
            });
            await salesInvoiceLine.$.save();

            await xtremSales.nodes.SalesInvoice.post(context, salesInvoice);

            await salesInvoice.$.set({ status: 'posted', forceUpdateForFinance: true });
            await salesInvoice.$.save();

            const salesCreditMemosCreated =
                await xtremSales.nodes.SalesReturnRequest.createSalesCreditMemosFromReturnRequests(context, [
                    salesReturnRequest,
                ]);
            assert.isArray(salesCreditMemosCreated.documentsCreated);

            if (!salesCreditMemosCreated.documentsCreated) {
                assert.fail('Failed to create sales credit memos from the sales return requests.');
            }
            const salesCreditMemo = await context.read(
                xtremSales.nodes.SalesCreditMemo,
                { _id: salesCreditMemosCreated.documentsCreated[0]._id },
                { forUpdate: true },
            );

            // check header notes are empty
            const creditMemoInternalNote = (await salesCreditMemo.internalNote).toString();
            const creditMemoIExternalNote = (await salesCreditMemo.externalNote).toString();
            assert.isEmpty(creditMemoInternalNote);
            assert.isEmpty(creditMemoIExternalNote);

            const salesCreditMemoLine = await context.read(
                xtremSales.nodes.SalesCreditMemoLine,
                { _id: (await salesCreditMemo.lines.elementAt(0))._id },
                { forUpdate: true },
            );

            // check lines notes are empty
            const creditMemoInternalLineNote = (await salesCreditMemoLine.internalNote).toString();
            const creditMemoIExternalLineNote = (await salesCreditMemoLine.externalNote).toString();
            assert.isEmpty(creditMemoInternalLineNote);
            assert.isEmpty(creditMemoIExternalLineNote);
        }));

    it('Create and validate sales credit memo for FR legislation attributes type restricted to', () =>
        Test.withContext(async context => {
            const salesCreditMemo1 = await createSalesCreditMemoFrLegAttributes(
                context,
                xtremDateTime.date.make(2022, 1, 1),
            );
            await assert.isRejected(salesCreditMemo1.$.save());
            assert.deepEqual(salesCreditMemo1.$.context.diagnoses, [
                {
                    severity: 3,
                    path: ['lines', '-1000000003', 'storedAttributes'],
                    message: 'The Project attribute needs to be filled in.',
                },
            ]);
        }));

    it.skip('Test openItem property', () =>
        Test.withContext(async context => {
            let creditMemo = await context.read(xtremSales.nodes.SalesCreditMemo, { number: 'SCPEG210001' });
            const businessEntityPayment = await (await creditMemo.billToCustomer).businessEntity;

            const newBaseOpenItem = await context.create(xtremFinanceData.nodes.BaseOpenItem, {
                dueDate: await creditMemo.dueDate,
                businessEntity: businessEntityPayment,
                businessEntityPayment,
                type: 'customer',
                currency: await creditMemo.currency,
                transactionAmountDue: await creditMemo.totalAmountIncludingTax,
                transactionAmountPaid: 0,
                companyAmountDue: await creditMemo.totalAmountIncludingTax,
                companyAmountPaid: 0,
                financialSiteAmountDue: await creditMemo.totalAmountIncludingTax,
                financialSiteAmountPaid: 0,
                status: 'notPaid',
                documentSysId: creditMemo._id,
                documentNumber: await creditMemo.number,
                documentType: 'salesCreditMemo',
            });
            await newBaseOpenItem.$.save();
            assert.equal(newBaseOpenItem.$.context.diagnoses, []);

            //  Check if the openItem property have value
            creditMemo = await context.read(xtremSales.nodes.SalesCreditMemo, { number: 'SCPEG210001' });
            const openItem = await creditMemo.arOpenItems.elementAt(0);

            assert.equal(await openItem?.documentType, 'salesCreditMemo');
            assert.equal(await openItem?.documentNumber, 'SCPEG210001');
            assert.equal(await openItem?.documentSysId, creditMemo._id);
        }));

    it('Control on sales credit memo with wrong tax type', () =>
        Test.withContext(async context => {
            const salesCreditMemo = await createSalesCreditMemoFrLeg(
                context,
                xtremDateTime.date.make(2022, 1, 1),
                'FR_TVA_NORMAL_DEDUCTIBLE_FA_INTRASTAT',
            );
            await assert.isRejected(salesCreditMemo.$.save());

            assert.deepEqual(salesCreditMemo.$.context.diagnoses, [
                {
                    severity: 3,
                    path: [],
                    message: "The tax type for all documents needs to be 'Sales' or 'Purchasing and sales'.",
                },
            ]);
        }));

    it('Control on sales credit memo with correct tax type', () =>
        Test.withContext(async context => {
            const salesCreditMemo = await createSalesCreditMemoFrLeg(context, xtremDateTime.date.make(2022, 1, 1));
            assert.isOk(await salesCreditMemo.$.control());
        }));
});

describe('validateTaxCategoryAndPaymentTerm on Sales credit memo', () => {
    async function createCreditMemo(context: Context) {
        const invoice = await context.read(
            xtremSales.nodes.SalesInvoice,
            { number: 'SIEF240001' },
            { forUpdate: true },
        );
        assert.equal(await (await xtremSales.nodes.SalesInvoice.post(context, invoice)).status, 'inProgress');

        await invoice.$.set({ status: 'posted' });
        await invoice.$.save();
        assert.deepEqual(invoice.$.context.diagnoses, []);

        await xtremSales.nodes.SalesInvoice.createSalesCreditMemosFromInvoices(context, [invoice]);
        assert.deepEqual(invoice.$.context.diagnoses, []);
    }

    async function readSalesCreditMemo(context: Context): Promise<xtremSales.nodes.SalesCreditMemo | undefined> {
        const creditMemos = await context
            .query(xtremSales.nodes.SalesCreditMemo, { last: 1, forUpdate: true, orderBy: { _id: 1 } })
            .toArray();

        return creditMemos.at(0);
    }

    it('VAT/GST and payment term has discount/penalty', () =>
        Test.withContext(async context => {
            await createCreditMemo(context);
            context.setServiceOptionsEnabledFlag(xtremStructure.serviceOptions.intacctActivationOption, true);

            const creditMemo = await readSalesCreditMemo(context);
            if (!creditMemo) assert.fail('Credit memo not found');
            await creditMemo.$.set({ paymentTerm: '#TEST_NET_15_CUSTOMER' });
            await assert.isRejected(creditMemo.$.save());
            assert.deepEqual(creditMemo.$.context.diagnoses, [
                {
                    message:
                        'Discounts and penalties exist for this payment term: Net 15. You cannot apply discounts and penalties in a VAT or GST transaction.',
                    path: [],
                    severity: 3,
                },
            ]);
        }));
    it('VAT/GST and payment term with no discount/penalty', () =>
        Test.withContext(async context => {
            await createCreditMemo(context);
            context.setServiceOptionsEnabledFlag(xtremStructure.serviceOptions.intacctActivationOption, true);

            const paymentTerm = await context.read(
                xtremMasterData.nodes.PaymentTerm,
                { id: 'TEST_NET_15_CUSTOMER' },
                { forUpdate: true },
            );
            await paymentTerm.$.set({ discountAmount: 0, penaltyAmount: 0 });
            await paymentTerm.$.save();

            const creditMemo = await readSalesCreditMemo(context);
            if (!creditMemo) assert.fail('Credit memo not found');
            await creditMemo.$.set({ paymentTerm });
            assert.isOk(await creditMemo.$.control());
        }));
    it('isIntacctActivationOption is disabled', () =>
        Test.withContext(async context => {
            await createCreditMemo(context);
            const creditMemo = await readSalesCreditMemo(context);
            if (!creditMemo) assert.fail('Credit memo not found');
            await creditMemo.$.set({ paymentTerm: '#TEST_NET_15_CUSTOMER' });
            assert.isOk(await creditMemo.$.control());
        }));
    it('No VAT/GST and payment term has discount/penalty', () =>
        Test.withContext(async context => {
            await createCreditMemo(context);
            context.setServiceOptionsEnabledFlag(xtremStructure.serviceOptions.intacctActivationOption, true);
            const creditMemo = await readSalesCreditMemo(context);
            if (!creditMemo) assert.fail('Credit memo not found');
            const salesCreditMemoTax = await context.read(
                xtremSales.nodes.SalesCreditMemoTax,
                { document: creditMemo._id, _sortValue: 20 },
                { forUpdate: true },
            );
            await salesCreditMemoTax.$.delete();
            assert.deepEqual(salesCreditMemoTax.$.context.diagnoses, []);

            await creditMemo.$.set({ paymentTerm: '#TEST_NET_15_CUSTOMER' });
            assert.isOk(await creditMemo.$.control());
        }));
});

describe('Posting details', () => {
    it('Gets posting details successfully', () =>
        Test.withContext(async context => {
            const scmDocumentNumber = 'SCE1250001';
            const scmDocument = await context.read(xtremSales.nodes.SalesCreditMemo, { number: scmDocumentNumber });

            assert.equal(await scmDocument.postingDetails.length, 1);
            const arInvoice = await scmDocument.postingDetails.at(0);

            assert.equal(await arInvoice?.documentType, 'salesCreditMemo');
            assert.equal(await arInvoice?.documentNumber, scmDocumentNumber);
            assert.equal(await arInvoice?.documentSysId, scmDocument._id);
            assert.equal(await arInvoice?.targetDocumentType, 'accountsReceivableInvoice');
        }));
});

describe('Payment status listener', () => {
    it('Updates payment and display status correctly', () =>
        Test.withContext(
            async context => {
                const salesCreditMemo = await context.read(xtremSales.nodes.SalesCreditMemo, { number: 'SCE1210007' });
                assert.equal(await salesCreditMemo.paymentStatus, 'notPaid');
                assert.equal(await salesCreditMemo.displayStatus, 'posted');
                await xtremSales.nodes.SalesCreditMemo.setSalesCreditMemoPaymentStatus(context, {
                    _id: salesCreditMemo._id,
                    paymentStatus: 'partiallyPaid',
                });
                const updatedSalesCreditMemo = await context.read(xtremSales.nodes.SalesCreditMemo, {
                    number: 'SCE1210007',
                });
                assert.equal(await updatedSalesCreditMemo.paymentStatus, 'partiallyPaid');
                assert.equal(await updatedSalesCreditMemo.displayStatus, 'partiallyPaid');
            },
            {
                testActiveServiceOptions: [xtremFinanceData.serviceOptions.paymentTrackingOption],
            },
        ));
});

describe('Credit memo node', () => {
    it('Create a credit memo with different invoice date and invoice line taxDate - XT-91804', () =>
        Test.withContext(async context => {
            const salesInvoice: xtremSales.nodes.SalesInvoice = await context.read(
                xtremSales.nodes.SalesInvoice,
                { _id: '#SI1' },
                { forUpdate: true },
            );

            const salesInvoiceLine: xtremSales.nodes.SalesInvoiceLine = await salesInvoice.lines.elementAt(0);
            const creator = new xtremSales.classes.SalesCreditMemosCreator(context, date.today());
            await creator.createNewDocumentNode(salesInvoiceLine, 100);
            const objectKeys = Object.keys(creator.salesOutputDocuments);
            const salesCreditMemo = creator.salesOutputDocuments[objectKeys[0]];
            const salesCreditMemoLines = salesCreditMemo.lines;
            assert.deepEqual(salesCreditMemoLines?.length, 1);
            const salesCreditMemoLine = salesCreditMemoLines ? salesCreditMemoLines[0] : undefined;
            assert.deepEqual(salesCreditMemoLine?.taxDate, await salesInvoiceLine.taxDate);
        }));
});

describe('Discount before payment date control', () => {
    it('Failed control due to discountPaymentBeforeDate being after the due date', () =>
        Test.withContext(async (context: Context) => {
            const salesCreditMemo = await context.read(
                xtremSales.nodes.SalesCreditMemo,
                { number: 'SCE1210004' },
                { forUpdate: true },
            );
            const dueDate = await salesCreditMemo.dueDate;
            // Update the discountPaymentBeforeDate to be after the due date
            await salesCreditMemo.$.set({ discountPaymentBeforeDate: dueDate.addDays(1) });
            await assert.isRejected(
                xtremSales.nodes.SalesCreditMemo.post(context, salesCreditMemo),
                'The discount date needs to be on or before the due date.',
            );
        }));
});
