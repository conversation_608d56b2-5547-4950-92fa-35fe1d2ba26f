import { Test } from '@sage/xtrem-core';
import * as xtremFinanceData from '@sage/xtrem-finance-data';
import * as xtremMasterData from '@sage/xtrem-master-data';
import { assert } from 'chai';
import * as sinon from 'sinon';
import * as xtremSales from '../../../index';

/* Test cases
On this test script we want to test 3 cases:
- posting documents
- reposting documents
- resend finance notification for document

Note that in fact the records on accounting staging are no longer created via a notification but directly saved on the database since the accounting staging is now on finance data package
 */

describe('Finance posting for sales documents', () => {
    it('Posting (for finance) a sales shipment', () =>
        Test.withContext(
            async context => {
                const documentNumber = 'SH250003';
                const salesShipment = await context.read(
                    xtremSales.nodes.SalesShipment,
                    {
                        number: documentNumber,
                    },
                    { forUpdate: true },
                );

                const documentFilter = {
                    documentNumber,
                    documentType: 'salesShipment',
                    targetDocumentType: 'journalEntry',
                };

                let accountingStagingRecords = await context
                    .query(xtremFinanceData.nodes.AccountingStaging, { filter: { ...documentFilter } })
                    .toArray();

                assert.equal(accountingStagingRecords.length, 0);

                await xtremSales.nodes.SalesShipment.onceStockCompleted(context, salesShipment);

                const financeTransactions = await context
                    .query(xtremFinanceData.nodes.FinanceTransaction, { filter: { ...documentFilter } })
                    .toArray();

                assert.equal(financeTransactions.length, 1);
                assert.equal(await financeTransactions.at(0)?.message, '');
                assert.equal(await financeTransactions.at(0)?.status, 'pending');

                accountingStagingRecords = await context
                    .query(xtremFinanceData.nodes.AccountingStaging, { filter: { ...documentFilter } })
                    .toArray();

                assert.equal(accountingStagingRecords.length, 2);
            },
            {
                today: '2021-08-23',
            },
        ));

    it('Posting (for finance) a sales return receipt', () =>
        Test.withContext(
            async context => {
                const documentNumber = 'SRR250002';
                const salesReturnReceipt = await context.read(
                    xtremSales.nodes.SalesReturnReceipt,
                    {
                        number: documentNumber,
                    },
                    { forUpdate: true },
                );

                await xtremSales.nodes.SalesReturnReceipt.onceStockCompleted(context, salesReturnReceipt);

                const documentFilter = {
                    documentNumber,
                    documentType: 'salesReturnReceipt',
                    targetDocumentType: 'journalEntry',
                };

                const financeTransactions = await context
                    .query(xtremFinanceData.nodes.FinanceTransaction, { filter: { ...documentFilter } })
                    .toArray();

                assert.equal(financeTransactions.length, 1);
                assert.equal(await financeTransactions.at(0)?.message, '');
                assert.equal(await financeTransactions.at(0)?.status, 'pending');

                const accountingStagingRecords = await context
                    .query(xtremFinanceData.nodes.AccountingStaging, { filter: { ...documentFilter } })
                    .toArray();

                assert.equal(accountingStagingRecords.length, 2);
            },
            {
                today: '2025-04-03',
            },
        ));

    it('Create notifications payload for a credit note with revenue reversal', () =>
        Test.withContext(
            async context => {
                const salesReturnRequest = await context.read(xtremSales.nodes.SalesReturnRequest, { _id: '#SRR21' });

                const salesCreditMemosCreated =
                    await xtremSales.nodes.SalesReturnRequest.createSalesCreditMemosFromReturnRequests(context, [
                        salesReturnRequest,
                    ]);

                assert.isArray(salesCreditMemosCreated.documentsCreated);

                if (!salesCreditMemosCreated.documentsCreated) {
                    assert.fail('Failed to create sales credit memos from the sales return request lines.');
                }
                const salesCreditMemo = await context.read(
                    xtremSales.nodes.SalesCreditMemo,
                    { _id: salesCreditMemosCreated.documentsCreated[0]._id },
                    { forUpdate: true },
                );

                const { accountingStagingCommonPayload, notificationsPayload } =
                    await xtremFinanceData.functions.getPurchaseSalesInvoiceCreditMemoNotificationPayload(context, {
                        document: salesCreditMemo,
                        lines: await salesCreditMemo.lines.toArray(),
                        documentType: 'salesCreditMemo',
                        targetDocumentType: 'accountsReceivableInvoice',
                        isJustChecking: false,
                    });

                await xtremSales.functions.FinanceIntegration.addSalesCreditMemoNotificationPayloadForRevenueRecognitionReversal(
                    accountingStagingCommonPayload,
                    notificationsPayload,
                    salesCreditMemo,
                    await salesCreditMemo.lines.toArray(),
                );

                assert.equal(notificationsPayload.length, 2);
            },
            { today: '2021-08-23' },
        ));
});

describe('Finance repost for sales documents', () => {
    async function getExpectedSIG1250005RepostPayload(
        salesInvoice: xtremSales.nodes.SalesInvoice,
        paymentTerm: xtremMasterData.nodes.PaymentTerm,
        financeTransaction?: xtremFinanceData.nodes.FinanceTransaction,
        unchangedLine?: xtremSales.nodes.SalesInvoiceLine,
    ) {
        return {
            batchId: (await financeTransaction?.batchId) ?? '',
            documentSysId: salesInvoice._id,
            documentNumber: 'SIG1250005',
            financialSiteSysId: (await salesInvoice.financialSite)._id,
            batchSize: 2,
            documentType: 'salesInvoice',
            targetDocumentType: 'accountsReceivableInvoice',
            dueDate: '2025-05-04',
            paymentTerm,
            isJustForPost: true,
            documentLines: [
                {
                    baseDocumentLineSysId: (await salesInvoice.lines.at(0))?._id ?? 0,
                    storedDimensions: {},
                    storedAttributes: {
                        businessSite: 'DES01',
                        financialSite: 'DES01',
                        stockSite: 'DES01',
                        item: 'NonStockManagedItem',
                        customer: 'DECUS01',
                        project: 'AttPROJ',
                        task: 'Task1',
                        employee: '',
                    },
                },
                {
                    baseDocumentLineSysId: unchangedLine?._id ?? 0,
                    storedDimensions: (await unchangedLine?.storedDimensions) ?? {},
                    storedAttributes: {
                        ...((await unchangedLine?.storedAttributes) as xtremMasterData.interfaces.StoredAttributes),
                        ...((await unchangedLine?.computedAttributes) as {}),
                    },
                },
            ],
        };
    }

    it('Sales invoice - repost', () =>
        Test.withContext(async context => {
            const documentNumber = 'SIG1250005';

            const salesInvoice = await context.read(
                xtremSales.nodes.SalesInvoice,
                {
                    number: documentNumber,
                },
                { forUpdate: true },
            );

            const filter = {
                documentNumber,
                documentType: 'salesInvoice',
                targetDocumentType: 'accountsReceivableInvoice',
            };

            const financeTransaction = await context
                .query(xtremFinanceData.nodes.FinanceTransaction, {
                    filter,
                    forUpdate: true,
                })
                .at(0);
            await financeTransaction?.$.set({ documentSysId: salesInvoice._id });
            await financeTransaction?.$.save();

            let accountingStagingRecords = context.query(xtremFinanceData.nodes.AccountingStaging, {
                filter,
            });
            assert.equal(await accountingStagingRecords.length, 2);

            let accountingStagingRecord = await accountingStagingRecords.at(0);
            assert.equal(await (await accountingStagingRecord?.paymentTerm)?.id, 'TEST_NET_15_CUSTOMER');
            assert.deepEqual(await accountingStagingRecord?.storedAttributes, {
                businessSite: 'DES01',
                customer: 'DECUS01',
                financialSite: 'DES01',
                item: 'NonStockManagedItem',
                stockSite: 'DES01',
            } as any);

            const paymentTerm = await context.read(xtremMasterData.nodes.PaymentTerm, {
                id: 'TEST_NET_60_ALL',
            });

            const notifySpy = sinon.spy(context, 'notify');

            const firstSalesInvoiceLine = await salesInvoice.lines.at(0);
            const unchangedLine = await salesInvoice.lines.at(1);

            const repostResult = await xtremSales.nodes.SalesInvoice.repost(context, salesInvoice, {
                header: {
                    paymentTerm,
                },
                lines: [
                    {
                        _action: 'update',
                        baseDocumentLineSysId: firstSalesInvoiceLine?._id ?? 0,
                        storedAttributes: {
                            project: 'AttPROJ',
                            task: 'Task1',
                            employee: '',
                        } as xtremMasterData.interfaces.StoredAttributes,
                        storedDimensions: {},
                    },
                ],
            });

            assert.deepEqual(repostResult, { wasSuccessful: true, message: 'The sales invoice was posted.' });
            assert.equal(notifySpy.callCount, 1);

            const notificationTopic = notifySpy.args[0][0];
            const notificationPayload = notifySpy.args[0][1];
            const notificationReplyTopic = notifySpy.args[0][2];

            assert.deepEqual(
                [notificationTopic, JSON.parse(JSON.stringify(notificationPayload)), notificationReplyTopic],
                [
                    'accountingInterfaceUpdate',
                    JSON.parse(
                        JSON.stringify(
                            await getExpectedSIG1250005RepostPayload(
                                salesInvoice,
                                paymentTerm,
                                financeTransaction,
                                unchangedLine,
                            ),
                        ),
                    ),
                    { replyTopic: 'SalesInvoice/accountingInterface' },
                ],
            );

            accountingStagingRecords = context.query(xtremFinanceData.nodes.AccountingStaging, {
                filter,
            });
            assert.equal(await accountingStagingRecords.length, 2);

            accountingStagingRecord = await accountingStagingRecords.at(0);
            assert.equal(await (await accountingStagingRecord?.paymentTerm)?.id, 'TEST_NET_60_ALL');
            assert.deepEqual(await accountingStagingRecord?.storedAttributes, {
                businessSite: 'DES01',
                customer: 'DECUS01',
                financialSite: 'DES01',
                item: 'NonStockManagedItem',
                stockSite: 'DES01',
                project: 'AttPROJ',
                task: 'Task1',
                employee: '',
            });
        }));
});

describe('Finance resend for sales documents', () => {
    it('Sales return receipt - Successfully resend notification for finance', () =>
        Test.withContext(
            async context => {
                const documentNumber = 'SRR250001';

                const salesReturnReceipt: xtremSales.nodes.SalesReturnReceipt = await context.read(
                    xtremSales.nodes.SalesReturnReceipt,
                    {
                        number: documentNumber,
                    },
                );

                const filter = {
                    documentNumber,
                    documentType: 'salesReturnReceipt',
                    targetDocumentType: 'journalEntry',
                };

                let amountType = await (
                    await (
                        await context
                            .query(xtremFinanceData.nodes.AccountingStaging, {
                                filter,
                            })
                            .at(0)
                    )?.amounts.at(0)
                )?.amountType;

                let financeTransactionRecord = await context
                    .query(xtremFinanceData.nodes.FinanceTransaction, { filter })
                    .at(0);
                let financeTransactionStatus = await financeTransactionRecord?.status;
                let financeTransactionMessage = await financeTransactionRecord?.message;

                assert.equal(amountType, 'landedCostAdjustmentAmount');
                assert.equal(financeTransactionStatus, 'notRecorded');
                assert.notEqual(financeTransactionMessage, '');

                await xtremSales.nodes.SalesReturnReceipt.resendNotificationForFinance(context, salesReturnReceipt);

                amountType = await (
                    await (
                        await context
                            .query(xtremFinanceData.nodes.AccountingStaging, {
                                filter,
                            })
                            .at(0)
                    )?.amounts.at(0)
                )?.amountType;

                financeTransactionRecord = await context
                    .query(xtremFinanceData.nodes.FinanceTransaction, { filter })
                    .at(0);
                financeTransactionStatus = await financeTransactionRecord?.status;
                financeTransactionMessage = await financeTransactionRecord?.message;

                assert.equal(amountType, 'amount');
                assert.equal(financeTransactionStatus, 'pending');
                assert.equal(financeTransactionMessage, '');
            },
            { today: '2025-02-05' },
        ));

    it('Sales credit memo - Successfully resend notification for finance', () =>
        Test.withContext(
            async context => {
                const documentNumber = 'SCE1250001';

                const salesCreditMemo: xtremSales.nodes.SalesCreditMemo = await context.read(
                    xtremSales.nodes.SalesCreditMemo,
                    {
                        number: documentNumber,
                    },
                );

                const filter = {
                    documentNumber,
                    documentType: 'salesCreditMemo',
                    targetDocumentType: 'accountsReceivableInvoice',
                };

                let accountingStagingRecords = context.query(xtremFinanceData.nodes.AccountingStaging, {
                    filter,
                });

                let financeTransactionRecord = await context
                    .query(xtremFinanceData.nodes.FinanceTransaction, { filter })
                    .at(0);
                let financeTransactionStatus = await financeTransactionRecord?.status;
                let financeTransactionMessage = await financeTransactionRecord?.message;

                assert.equal(await accountingStagingRecords?.length, 0);
                assert.equal(financeTransactionStatus, 'error');
                assert.notEqual(financeTransactionMessage, '');

                const notifySpy = sinon.spy(context, 'notify');
                await xtremSales.nodes.SalesCreditMemo.resendNotificationForFinance(context, salesCreditMemo);
                assert.equal(notifySpy.getCalls().length, 1);
                const notificationTopic = notifySpy.args[0][0];
                const notificationPayload = notifySpy.args[0][1];

                financeTransactionRecord = await context
                    .query(xtremFinanceData.nodes.FinanceTransaction, { filter })
                    .at(0);

                const batchId = (await financeTransactionRecord?.batchId) ?? '';

                const expectedPayload = {
                    filter: `{"batchId":"${batchId}","isProcessed":false}`,
                    journalsCreatedData: false,
                };

                assert.deepEqual(
                    [notificationTopic, JSON.parse(JSON.stringify(notificationPayload))],
                    [
                        'AccountingInterfaceListener/createJournalsFromAccountingStagingJob/start',
                        JSON.parse(JSON.stringify(expectedPayload)),
                    ],
                );

                accountingStagingRecords = context.query(xtremFinanceData.nodes.AccountingStaging, {
                    filter,
                });

                financeTransactionStatus = await financeTransactionRecord?.status;
                financeTransactionMessage = await financeTransactionRecord?.message;

                assert.equal(await accountingStagingRecords.length, 1);
                assert.equal(financeTransactionStatus, 'pending');
                assert.equal(financeTransactionMessage, '');
            },
            { today: '2025-02-10' },
        ));

    it('Sales invoice - resendNotificationForFinance', () =>
        Test.withContext(async context => {
            const documentNumber = 'SIG1240002';

            const salesInvoice: xtremSales.nodes.SalesInvoice = await context.read(xtremSales.nodes.SalesInvoice, {
                number: documentNumber,
            });

            const filter = {
                documentNumber,
                documentType: 'salesInvoice',
                targetDocumentType: 'accountsReceivableInvoice',
            };

            let accountingStagingRecords = context.query(xtremFinanceData.nodes.AccountingStaging, {
                filter,
            });

            let financeTransactionRecord = await context
                .query(xtremFinanceData.nodes.FinanceTransaction, { filter })
                .at(0);
            let financeTransactionStatus = await financeTransactionRecord?.status;
            let financeTransactionMessage = await financeTransactionRecord?.message;

            assert.equal(await accountingStagingRecords?.length, 1);
            assert.equal(financeTransactionStatus, 'notRecorded');
            assert.notEqual(financeTransactionMessage, '');

            const notifySpy = sinon.spy(context, 'notify');

            const result = await xtremSales.nodes.SalesInvoice.resendNotificationForFinance(context, salesInvoice);
            assert.equal(notifySpy.getCalls().length, 1);
            const notificationTopic = notifySpy.args[0][0];
            const notificationPayload = notifySpy.args[0][1];
            assert.equal(result, true);

            financeTransactionRecord = await context.query(xtremFinanceData.nodes.FinanceTransaction, { filter }).at(0);

            const batchId = (await financeTransactionRecord?.batchId) ?? '';

            const expectedPayload = {
                filter: `{"batchId":"${batchId}","isProcessed":false}`,
                journalsCreatedData: false,
                batchTrackingId: '',
            };

            assert.deepEqual(
                [notificationTopic, JSON.parse(JSON.stringify(notificationPayload))],
                [
                    'AccountingInterfaceListener/createJournalsFromAccountingStagingJob/start',
                    JSON.parse(JSON.stringify(expectedPayload)),
                ],
            );

            accountingStagingRecords = context.query(xtremFinanceData.nodes.AccountingStaging, {
                filter,
            });

            financeTransactionStatus = await financeTransactionRecord?.status;
            financeTransactionMessage = await financeTransactionRecord?.message;

            assert.equal(await accountingStagingRecords.length, 1);
            assert.equal(financeTransactionStatus, 'pending');
            assert.equal(financeTransactionMessage, '');
        }));
});
