// tslint:disable:no-duplicate-string
import type { Context, decimal, NodeCreateData } from '@sage/xtrem-core';
import { asyncArray, date, Test } from '@sage/xtrem-core';
import * as xtremFinanceData from '@sage/xtrem-finance-data';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremReporting from '@sage/xtrem-reporting';
import * as xtremStockData from '@sage/xtrem-stock-data';
import * as xtremStructure from '@sage/xtrem-structure';
import * as xtremTax from '@sage/xtrem-tax';
import { assert } from 'chai';
import * as sinon from 'sinon';
import * as xtremSales from '../../../index';

const { withSequenceNumberContext } = xtremMasterData.functions.testLib;

describe('SalesInvoiceNode', () => {
    let loggerSpyError: any;
    let loggerSpyInfo: any;

    before(() =>
        Test.withContext(context => {
            loggerSpyError = sinon.spy(context.logger, 'error');
            loggerSpyInfo = sinon.spy(context.logger, 'info');
        }),
    );

    beforeEach(() => {
        loggerSpyError.resetHistory();
        loggerSpyInfo.resetHistory();
    });
    function createSalesInvoice(context: Context, invoiceDate: date, number?: string) {
        return context.create(xtremSales.nodes.SalesInvoice, {
            lines: [
                {
                    _action: 'create',
                    charge: 0,
                    consumptionLinkedAddress: '#US019|1500',
                    discount: 0,
                    grossPrice: 20,
                    item: '#SalesItem81',
                    itemDescription: 'Sales Item 81',
                    netPrice: 20,
                    origin: 'direct',
                    providerSite: '#US001',
                    quantity: 20,
                    quantityInStockUnit: 20,
                    site: '#US001',
                    unit: { id: 'MILLILITER' },
                    unitToStockUnitConversionFactor: 1,
                    stockUnit: { id: 'LITER' },
                    amountExcludingTax: 400,
                    amountIncludingTax: 400,
                },
            ],
            billToAddress: {
                country: { id: 'US' },
                name: 'Cust address',
                locationPhoneNumber: '+15550123456',
                addressLine1: '1st Avenue',
                addressLine2: '',
                city: 'One',
                region: 'Some region',
                postcode: '0001',
            },
            billToLinkedAddress: '#US019|1500',
            currency: { id: 'USD' },
            incoterm: null,
            paymentTerm: '#DUE_UPON_RECEIPT_ALL',
            billToCustomer: '#US019',
            site: '#US001',
            number,
            date: invoiceDate,
            dueDate: date.make(2021, 6, 29),
            totalAmountExcludingTax: 400,
            totalAmountIncludingTax: 400,
            isPrinted: false,
            status: 'draft',
        });
    }

    async function createSalesInvoiceFrLeg(
        context: Context,
        invoiceDate: date,
        taxReference: string = '#FR_TVA_REDUCED_COLLECTED_ON_PAYMENT',
        number?: string,
    ): Promise<xtremSales.nodes.SalesInvoice> {
        const taxSolution = await context.read(xtremTax.nodes.TaxSolution, { _id: '#FRSOL' }, { forUpdate: true });
        if (await taxSolution.lines.at(1)) {
            await taxSolution.$.set({
                lines: [{ _action: 'delete', _id: (await taxSolution.lines.at(1))?._id }],
            });
            await taxSolution.$.save();
        }

        const baseData: NodeCreateData<xtremSales.nodes.SalesInvoice> = {
            lines: [
                {
                    _action: 'create',
                    charge: 0,
                    consumptionLinkedAddress: '#US019|1500',
                    discount: 0,
                    grossPrice: 20,
                    item: '#SalesItem81',
                    itemDescription: 'Sales Item 81',
                    netPrice: 20,
                    origin: 'direct',
                    providerSite: '#ETS1-S01',
                    quantity: 20,
                    quantityInStockUnit: 20,
                    site: '#ETS1-S01',
                    unit: { id: 'MILLILITER' },
                    unitToStockUnitConversionFactor: 1,
                    stockUnit: { id: 'LITER' },
                    amountExcludingTax: 400,
                    amountIncludingTax: 400,
                    taxes: [{ _action: 'create', taxReference, taxRate: 0 }],
                    storedDimensions: { dimensionType01: '300' },
                },
            ],
            billToAddress: {
                country: { id: 'US' },
                name: 'Cust address',
                locationPhoneNumber: '+15550123456',
                addressLine1: '1st Avenue',
                addressLine2: '',
                city: 'One',
                region: 'Some region',
                postcode: '0001',
            },
            billToLinkedAddress: '#US019|1500',
            currency: { id: 'USD' },
            incoterm: null,
            paymentTerm: '#DUE_UPON_RECEIPT_ALL',
            billToCustomer: '#US019',
            site: '#ETS1-S01',
            number,
            date: invoiceDate,
            dueDate: date.make(2021, 6, 29),
            discountPaymentBeforeDate: date.make(2021, 6, 28),
            totalAmountExcludingTax: 400,
            totalAmountIncludingTax: 400,
            isPrinted: false,
            status: 'draft',
            taxCalculationStatus: 'done',
        };
        return context.create(xtremSales.nodes.SalesInvoice, baseData);
    }

    function createSalesInvoiceAttributes(context: Context, invoiceDate: date, number?: string) {
        return context.create(xtremSales.nodes.SalesInvoice, {
            lines: [
                {
                    _action: 'create',
                    charge: 0,
                    consumptionLinkedAddress: '#US019|1500',
                    discount: 0,
                    grossPrice: 20,
                    item: '#SalesItem81',
                    itemDescription: 'Sales Item 81',
                    netPrice: 20,
                    origin: 'direct',
                    providerSite: '#US001',
                    quantity: 20,
                    quantityInStockUnit: 20,
                    site: '#US001',
                    unit: { id: 'MILLILITER' },
                    unitToStockUnitConversionFactor: 1,
                    stockUnit: { id: 'LITER' },
                    amountExcludingTax: 400,
                    amountIncludingTax: 400,
                    storedAttributes: { employee: '', task: 'TASK1', project: '' },
                },
            ],
            billToAddress: {
                country: '#US',
                name: 'Cust address',
                locationPhoneNumber: '+15550123456',
                addressLine1: '1st Avenue',
                addressLine2: '',
                city: 'One',
                region: 'Some region',
                postcode: '0001',
            },
            billToLinkedAddress: '#US019|1500',
            currency: '#USD',
            incoterm: null,
            paymentTerm: '#DUE_UPON_RECEIPT_ALL',
            billToCustomer: '#US019',
            site: '#US001',
            number,
            date: invoiceDate,
            dueDate: date.make(2021, 6, 29),
            totalAmountExcludingTax: 400,
            totalAmountIncludingTax: 400,
            isPrinted: false,
            status: 'draft',
        });
    }

    it('Update sales invoice that is posted', () =>
        Test.withContext(async context => {
            const salesInvoiceLine = await context.read(
                xtremSales.nodes.SalesInvoiceLine,
                { _id: '#SI2|10' },
                { forUpdate: true },
            );
            await salesInvoiceLine.$.set({ grossPrice: 2.2 });
            await assert.isRejected(salesInvoiceLine.$.save());
        }));

    it('Update sales invoice same quantity', () =>
        Test.withContext(async context => {
            const salesInvoiceLine = await context.read(
                xtremSales.nodes.SalesInvoiceLine,
                { _id: '#SI1|10' },
                { forUpdate: true },
            );
            await salesInvoiceLine.$.set({ grossPrice: 2.2 });
            await salesInvoiceLine.$.save();
            assert.equal(String(await salesInvoiceLine.grossPrice), '2.2');
        }));

    it('Create sales invoice and repost', () =>
        Test.withContext(async context => {
            const salesInvoice1 = await createSalesInvoice(context, date.make(2021, 6, 29));
            await salesInvoice1.$.save({ flushDeferredActions: true });

            const toInvoiceLines = await asyncArray(await salesInvoice1.lines.toArray())
                .map(async line => {
                    return {
                        baseDocumentLineSysId: line._id,
                        storedAttributes: (await line.storedAttributes) as xtremMasterData.interfaces.StoredAttributes,
                        storedDimensions: (await line.storedDimensions) || {},
                    };
                })
                .toArray();

            await assert.isRejected(
                xtremSales.nodes.SalesInvoice.repost(context, salesInvoice1, {
                    header: {},
                    lines: toInvoiceLines,
                }),
                "You can only repost a sales invoice if the status is 'Failed' or 'Not recorded'.",
            );
        }));

    it('Create sales invoice proper date passing', () =>
        Test.withContext(async context => {
            const salesInvoice1 = await createSalesInvoice(context, date.make(2021, 6, 29));
            await salesInvoice1.$.save();

            assert.equal(String(await salesInvoice1.date), '2021-06-29');

            const salesInvoice2 = await createSalesInvoice(context, date.make(2021, 6, 29));
            await salesInvoice2.$.save();

            assert.equal(String(await salesInvoice2.date), '2021-06-29');
        }));

    it('Create sales invoice proper date failing', () =>
        Test.withContext(async context => {
            const salesInvoice1 = await createSalesInvoice(context, date.make(2021, 6, 29));
            await salesInvoice1.$.save({ flushDeferredActions: true });

            assert.equal(String(await salesInvoice1.date), '2021-06-29');

            const salesInvoice2 = await createSalesInvoice(context, date.make(2021, 6, 28));
            await assert.isRejected(salesInvoice2.$.save(), 'The record was not created.');
            assert.deepEqual(salesInvoice2.$.context.diagnoses, [
                {
                    severity: 3,
                    path: [],
                    message: 'The document date 2021-06-28 is earlier than the previous document date 2021-06-29.',
                },
            ]);
        }));

    it('Update sales invoice proper date', () =>
        Test.withContext(async context => {
            await (await createSalesInvoice(context, date.make(2021, 6, 25))).$.save();
            let salesInvoice = await createSalesInvoice(context, date.make(2021, 6, 27));
            await salesInvoice.$.save();
            await (await createSalesInvoice(context, date.make(2021, 6, 28))).$.save({ flushDeferredActions: true });

            salesInvoice = await context.read(
                xtremSales.nodes.SalesInvoice,
                { number: await salesInvoice.number },
                { forUpdate: true },
            );
            await salesInvoice!.$.set({ date: date.make(2021, 6, 26) });
            await salesInvoice!.$.save();
            assert.equal(String(await salesInvoice.date), '2021-06-26');
        }));

    it('Update sales invoice improper date 1', () =>
        Test.withContext(async context => {
            let salesInvoice = await createSalesInvoice(context, date.make(2021, 6, 25));
            await salesInvoice.$.save();
            await (await createSalesInvoice(context, date.make(2021, 6, 27))).$.save({ flushDeferredActions: true });

            salesInvoice = await context.read(
                xtremSales.nodes.SalesInvoice,
                { number: await salesInvoice.number },
                { forUpdate: true },
            );
            await salesInvoice.$.set({ date: date.make(2021, 6, 28) });
            await assert.isRejected(salesInvoice.$.save(), 'The record was not updated.');
            assert.deepEqual(salesInvoice.$.context.diagnoses, [
                {
                    severity: 3,
                    path: [],
                    message: 'The document date 2021-06-28 is later than the next document date 2021-06-27.',
                },
            ]);
        }));

    it('Update sales invoice improper date 2', () =>
        Test.withContext(async context => {
            await (await createSalesInvoice(context, date.make(2021, 6, 25))).$.save();
            let salesInvoice = await createSalesInvoice(context, date.make(2021, 6, 27));
            await salesInvoice.$.save({ flushDeferredActions: true });

            salesInvoice = await context.read(
                xtremSales.nodes.SalesInvoice,
                { number: await salesInvoice.number },
                { forUpdate: true },
            );
            await salesInvoice.$.set({ date: date.make(2021, 6, 24) });
            await assert.isRejected(salesInvoice.$.save(), 'The record was not updated.');
            assert.deepEqual(salesInvoice.$.context.diagnoses, [
                {
                    severity: 3,
                    path: [],
                    message: 'The document date 2021-06-24 is earlier than the previous document date 2021-06-25.',
                },
            ]);
        }));

    it('Update sales invoice with sequence number which have maximal value', () =>
        Test.withContext(async context => {
            await (await createSalesInvoice(context, date.make(2021, 6, 25))).$.save();
            let salesInvoice = await createSalesInvoice(context, date.make(2021, 6, 27), 'SINVUS0012021069999');
            await salesInvoice.$.save();

            salesInvoice = await context.read(
                xtremSales.nodes.SalesInvoice,
                { number: await salesInvoice.number },
                { forUpdate: true },
            );
            await salesInvoice!.$.set({ date: date.make(2021, 6, 26) });
            await salesInvoice!.$.save();
            assert.equal(await salesInvoice.number, 'SINVUS0012021069999');
        }));

    it('Update sales invoice with sequence number without chronological control', () =>
        withSequenceNumberContext('SalesInvoiceUSA|US', { isChronological: false }, async context => {
            await (await createSalesInvoice(context, date.make(2021, 6, 25))).$.save();

            let salesInvoice = await createSalesInvoice(context, date.make(2021, 6, 27));
            await salesInvoice.$.save({ flushDeferredActions: true });
            assert.deepEqual(salesInvoice!.$.context.diagnoses, []);

            salesInvoice = await context.read(
                xtremSales.nodes.SalesInvoice,
                { number: await salesInvoice.number },
                { forUpdate: true },
            );

            await salesInvoice!.$.set({ date: date.make(2021, 6, 24) });
            await salesInvoice!.$.save();
            assert.deepEqual(salesInvoice!.$.context.diagnoses, []);
            assert.equal(String(await salesInvoice.date), '2021-06-24');
        }));

    it('Get sales invoice line taxes in UI version', () =>
        Test.withContext(async context => {
            const salesInvoice = await context.read(xtremSales.nodes.SalesInvoice, { _id: '#SI1' });

            const uiTaxes = await (await salesInvoice!.lines.elementAt(0)).uiTaxes;

            assert.equal(uiTaxes!.taxes[0].taxRate, 10);
        }));

    it('Create and validate sales invoice for FR legislation with correct date', () =>
        Test.withContext(async context => {
            const salesInvoice1 = await createSalesInvoiceFrLeg(context, date.make(2022, 1, 1));
            await salesInvoice1.$.save();

            const salesInvoice2 = await createSalesInvoiceFrLeg(context, date.make(2022, 1, 2));
            await salesInvoice2.$.save({ flushDeferredActions: true });

            const salesInvoice2Posted: xtremSales.nodes.SalesInvoice = await context.read(
                xtremSales.nodes.SalesInvoice,
                { number: await salesInvoice2.number },
                { forUpdate: true },
            );
            await xtremSales.nodes.SalesInvoice.post(context, salesInvoice2Posted);
            await assert.isRejected(
                xtremSales.nodes.SalesInvoice.post(context, salesInvoice1),
                'The document date 2022-01-01 is earlier than the previous document date 2022-01-02.',
            );
        }));

    it('Test if final sequence number has chronological control active for FR legislation', () =>
        withSequenceNumberContext('PostedSalesInvoice', { isChronological: false }, async context => {
            const salesInvoice = await createSalesInvoiceFrLeg(context, date.make(2022, 1, 1));
            await salesInvoice.$.save({ flushDeferredActions: true });

            await assert.isRejected(
                xtremSales.nodes.SalesInvoice.post(context, salesInvoice),
                'Sequence number PostedSalesInvoice: The chronological control must be active for the FR legislation.',
            );
        }));

    it('Test if final sequence number don`t have monthly reset level active for FR legislation', () =>
        withSequenceNumberContext('PostedSalesInvoice', { rtzLevel: 'monthly' }, async context => {
            const salesInvoice = await createSalesInvoiceFrLeg(context, date.make(2022, 1, 1));
            await salesInvoice.$.save();

            await assert.isRejected(
                xtremSales.nodes.SalesInvoice.post(context, salesInvoice),
                'Sequence number PostedSalesInvoice: Monthly sequence numbers are not allowed.',
            );
        }));

    it('XT-18577 -> createSalesInvoicesFromShipment ', () =>
        Test.withContext(
            async context => {
                let salesShipment = await context.read(
                    xtremSales.nodes.SalesShipment,
                    { _id: '#SSH1' },
                    { forUpdate: true },
                );

                const salesShipmentFirstLine = await salesShipment.lines.at(0);

                let i = 0;

                while (i < 12) {
                    await salesShipment.$.set({
                        lines: [
                            {
                                _action: 'create',
                                origin: await salesShipmentFirstLine!.origin,
                                status: await salesShipmentFirstLine!.status,
                                invoiceStatus: await salesShipmentFirstLine!.invoiceStatus,
                                item: await salesShipmentFirstLine!.item,
                                quantity: await salesShipmentFirstLine!.quantity,
                                unit: await salesShipmentFirstLine!.unit,
                            },
                        ],
                    });
                    i += 1;
                }

                await salesShipment.$.save();

                const stockRecord = context.read(xtremStockData.nodes.Stock, { _id: '38' });
                await salesShipment.lines.forEach(async line => {
                    await xtremStockData.nodes.Stock.updateAllocations(context, {
                        documentLine: Promise.resolve(line),
                        allocationUpdates: [
                            {
                                action: 'create',
                                quantity: await line.quantityInStockUnit,
                                stockRecord,
                            },
                        ],
                    });
                });

                // re read cause the mutation doesn't update the document loaded here
                salesShipment = await context.read(
                    xtremSales.nodes.SalesShipment,
                    { _id: '#SSH1' },
                    { forUpdate: true },
                );

                assert.equal(await salesShipment.allocationStatus, 'allocated');
                assert.equal(await salesShipment.lines.length, 14);

                await xtremSales.nodes.SalesShipment.confirm(context, salesShipment);

                // re read cause the mutation doesn't update the document loaded here
                salesShipment = await context.read(
                    xtremSales.nodes.SalesShipment,
                    { _id: '#SSH1' },
                    { forUpdate: true },
                );

                const postResult = await xtremSales.nodes.SalesShipment.postToStock(context, salesShipment);
                assert.isNotEmpty(postResult);
                await xtremStockData.functions.testLib.simulateStockTransaction(
                    context,
                    salesShipment._id,
                    xtremSales.nodes.SalesShipment,
                    'issue',
                    xtremSales.nodes.SalesShipment.onStockReply,
                    { stockUpdateResultStatus: 'decreased' },
                );
                await Test.rollbackCache(context);

                salesShipment = await context.read(xtremSales.nodes.SalesShipment, { _id: '#SSH1' });

                const generatedInvoices = await xtremSales.nodes.SalesShipment.createSalesInvoicesFromShipment(
                    context,
                    salesShipment,
                    false,
                    date.make(2021, 2, 1),
                );

                assert.isNotNull(generatedInvoices);
                assert.isArray(generatedInvoices);
                assert.isNotEmpty(generatedInvoices);
            },
            { user: { email: '<EMAIL>' }, today: '2021-02-01' },
        ));

    it('Mass process error management createSalesOutputDocuments()', () =>
        Test.withContext(
            async context => {
                const salesShipment = await context.read(
                    xtremSales.nodes.SalesShipment,
                    { number: 'SSH1' },
                    { forUpdate: true },
                );

                const salesShipment2 = await context.read(
                    xtremSales.nodes.SalesShipment,
                    { number: 'SSH2' },
                    { forUpdate: true },
                );

                const salesShipmentLines: {
                    salesDocumentLine: xtremSales.nodes.SalesShipmentLine;
                    quantityToProcess?: decimal;
                }[] = [];

                let i = 0;

                await salesShipment2.lines.forEach(async line => {
                    const quantityToProcess = i === 1 ? 200 : await line.quantity;
                    salesShipmentLines.push({ salesDocumentLine: line, quantityToProcess });
                    i += 1;
                });

                await salesShipment.lines.forEach(async line => {
                    salesShipmentLines.push({
                        salesDocumentLine: line,
                        quantityToProcess: await line.quantity,
                    });
                });

                const salesInvoicesCreated = await xtremSales.nodes.SalesShipment.createSalesInvoicesFromShipmentLines(
                    context,
                    salesShipmentLines,
                );
                await context.flushDeferredActions();
                assert.isArray(salesInvoicesCreated.documentsCreated);

                if (!salesInvoicesCreated.documentsCreated) {
                    assert.fail('No documents were created from the sales shipment lines.');
                }

                const salesInvoice = await context.read(xtremSales.nodes.SalesInvoice, {
                    _id: salesInvoicesCreated.documentsCreated[0]._id,
                });

                assert.equal(await salesInvoice.number, 'SITE1240002');

                assert.equal(salesInvoicesCreated.numberOfInvoices, 1);
                assert.equal(salesInvoicesCreated.status, 'salesShipmentIsInvoiced');

                const logCallInfo = loggerSpyInfo.getCall(0);
                const logCallError = loggerSpyError.getCall(0);

                assert.equal(
                    String(logCallError.args[0]),
                    `The sales invoice could not be created using the sales shipment numbers: ${await salesShipment2.number}. Details: The sales document line quantity cannot be larger than the remaining quantity in the related document.\n`,
                );

                assert.equal(
                    String(logCallInfo.args[0]),
                    `The sales invoice was created from the following sales shipment numbers: ${await salesShipment.number}.`,
                );
            },
            { user: { email: '<EMAIL>' }, today: '2024-12-15' },
        ));

    it('Mass process error management prepareNodeCreateData', () =>
        Test.withContext(
            async context => {
                let salesShipment = await context.read(
                    xtremSales.nodes.SalesShipment,
                    { number: 'SSH1' },
                    { forUpdate: true },
                );

                const salesShipment2 = await context.read(
                    xtremSales.nodes.SalesShipment,
                    { number: 'SSH2' },
                    { forUpdate: true },
                );

                const salesShipmentLines: [
                    { salesDocumentLine: xtremSales.nodes.SalesShipmentLine; quantityToProcess?: decimal },
                ] = [] as any;

                let i = 0;
                let itemNameFailedLine: String = '';

                await salesShipment2.lines.forEach(async line => {
                    if (i === 1) {
                        await line.$.set({ invoiceStatus: 'invoiced' });
                        itemNameFailedLine = await (await line.item).name;
                    }
                    salesShipmentLines.push({
                        salesDocumentLine: line,
                        quantityToProcess: await line.quantity,
                    });
                    i += 1;
                });
                await salesShipment2.$.save();

                await salesShipment.lines.forEach(async line => {
                    salesShipmentLines.push({
                        salesDocumentLine: line,
                        quantityToProcess: await line.quantity,
                    });
                });

                const salesInvoicesCreated = await xtremSales.nodes.SalesShipment.createSalesInvoicesFromShipmentLines(
                    context,
                    salesShipmentLines,
                );
                assert.isArray(salesInvoicesCreated.documentsCreated);

                if (!salesInvoicesCreated.documentsCreated) {
                    assert.fail('Failed to create sales invoices from the sales shipments.');
                }

                assert.equal(salesInvoicesCreated.numberOfInvoices, 2);
                assert.equal(salesInvoicesCreated.status, 'salesShipmentIsInvoiced');

                await context.flushDeferredActions();

                assert.equal(await salesInvoicesCreated.documentsCreated[0].number, 'SITE1240001');
                assert.equal(await salesInvoicesCreated.documentsCreated[1].number, 'SITE1240002');

                const salesInvoices = await context
                    .query(xtremSales.nodes.SalesInvoice, { last: 2, filter: { date: '2024-12-15' } })
                    .toArray();

                assert.equal(await salesInvoices[0]?.number, 'SITE1240002');
                assert.equal(await salesInvoices[1]?.number, 'SITE1240001');

                const logCallInfo = loggerSpyInfo.getCall(0);
                const logCallInfo2 = loggerSpyInfo.getCall(1);
                const logCallError = loggerSpyError.getCall(0);

                assert.equal(
                    String(logCallError.args[0]),
                    `The sales invoice could not be created using the sales shipment numbers: ${await salesShipment2.number} - ${itemNameFailedLine}. Details: A shipment line is already invoiced.`,
                );

                // rollbackCache function will flush deferred actions and saves
                await Test.rollbackCache(context);

                salesShipment = await context.read(xtremSales.nodes.SalesShipment, { number: 'SSH1' });

                assert.equal(
                    String(logCallInfo.args[0]),
                    `The sales invoice was created from the following sales shipment numbers: ${await salesShipment2.number}.`,
                );

                assert.equal(
                    String(logCallInfo2.args[0]),
                    `The sales invoice was created from the following sales shipment numbers: ${await salesShipment.number}.`,
                );

                assert.equal(await salesInvoices[1].lines.length, 2);
            },
            { user: { email: '<EMAIL>' }, today: '2024-12-15' },
        ));

    it(`Verifing if sequence number properties are correct when giving an SalesInvoice instance`, () =>
        Test.withContext(async context => {
            const nodeInstance = await context.tryRead(xtremSales.nodes.SalesInvoice, { number: 'SI10' });
            if (nodeInstance) {
                const sequenceNumber = 'SalesInvoice';
                const docGenInstance = await xtremMasterData.classes.DocumentNumberGenerator.create(context, {
                    sequenceNumber,
                    nodeInstance,
                });

                assert.equal(docGenInstance.action, 'unchanged');
                assert.equal(docGenInstance.numberField, 'number');
                assert.equal(await docGenInstance.documentNumber, 'SI10');
                assert.equal(docGenInstance.documentDateField, 'documentDate');
                assert.equal((await docGenInstance.currentDate).value, 20211213);
                assert.equal(await (await docGenInstance.company)?.name, 'US Process Manufacturing 001');
                assert.equal(await (await docGenInstance.site)?.name, 'Chem. Atlanta');

                assert.equal(await (await docGenInstance.legislation)?.id, 'US');
            }
        }));

    it(`Verifing if sequence number properties are correct when giving an SalesInvoice instance`, () =>
        Test.withContext(async context => {
            const nodeInstance = await context.tryRead(xtremSales.nodes.SalesInvoice, { number: 'SI10' });
            if (nodeInstance) {
                const docGenInstance = await xtremMasterData.classes.DocumentNumberGenerator.create(context, {
                    nodeInstance,
                });
                assert.equal(docGenInstance.action, 'unchanged');
                assert.equal(docGenInstance.numberField, 'number');
                assert.equal(await docGenInstance.documentNumber, 'SI10');
                assert.equal(docGenInstance.documentDateField, 'documentDate');
                assert.equal((await docGenInstance.currentDate).value, 20211213);
                assert.equal(await (await docGenInstance.company)?.name, 'US Process Manufacturing 001');
                assert.equal(await (await docGenInstance.site)?.name, 'Chem. Atlanta');
                assert.isNotNull(await docGenInstance.legislationSysId);
                assert.equal(await (await docGenInstance.sequenceNumber).id, 'SalesInvoiceUSA');
                assert.equal(await (await (await docGenInstance.sequenceNumber).legislation)?.id, 'US');

                const legislation = await context.read(xtremStructure.nodes.Legislation, {
                    _id: (await docGenInstance.legislationSysId)!,
                });
                assert.equal(await legislation?.id, 'US');
            }
        }));

    it(`Create a Sales Invoice and check if the sequenceNumber assignment is at the company level`, () =>
        Test.withContext(async context => {
            const salesInvoice = await context.create(xtremSales.nodes.SalesInvoice, {
                lines: [
                    {
                        charge: 0,
                        consumptionLinkedAddress: '#US019|1500',
                        discount: 0,
                        grossPrice: 20,
                        item: '#SalesItem81',
                        itemDescription: 'Sales Item 81',
                        netPrice: 20,
                        origin: 'direct',
                        providerSite: '#US001',
                        quantity: 20,
                        quantityInStockUnit: 20,
                        site: '#US001',
                        unit: { id: 'MILLILITER' },
                        unitToStockUnitConversionFactor: 1,
                        stockUnit: { id: 'LITER' },
                        amountExcludingTax: 400,
                        amountIncludingTax: 400,
                    },
                ],
                currency: { id: 'USD' },
                incoterm: null,
                paymentTerm: '#DUE_UPON_RECEIPT_ALL',
                billToCustomer: '#US019',
                site: '#US003',
                number: undefined,
                date: date.make(2021, 6, 29),
                dueDate: date.make(2021, 6, 29),
                totalAmountExcludingTax: 400,
                totalAmountIncludingTax: 400,
                isPrinted: false,
                status: 'draft',
            });

            await salesInvoice.$.save({ flushDeferredActions: true });

            const company = await (await salesInvoice.site).legalCompany;
            assert.equal(await company.id, 'US002');

            const assignementUsed = await context
                .query(xtremMasterData.nodes.SequenceNumberAssignment, {
                    filter: {
                        sequenceNumberAssignmentDocumentType: { nodeFactory: { name: salesInvoice.$.factory.name } },
                        company: { id: await company.id },
                        legislation: await company.legislation,
                    },
                })
                .at(0);
            assert.equal(await (await assignementUsed?.sequenceNumber)?.id, 'SALES_INVOICE_USA2');

            const docGenInstance = await xtremMasterData.classes.DocumentNumberGenerator.create(context, {
                nodeInstance: salesInvoice,
            });

            assert.equal(await (await docGenInstance.sequenceNumber).id, 'SALES_INVOICE_USA2');
            assert.equal(await (await (await docGenInstance.sequenceNumber).legislation)?.id, 'US');
        }));

    it(`Create a Sales Invoice and check if the sequenceNumber assignment is at the legislation level`, () =>
        Test.withContext(async context => {
            const salesInvoice = await context.create(xtremSales.nodes.SalesInvoice, {
                lines: [
                    {
                        _action: 'create',
                        charge: 0,
                        consumptionLinkedAddress: '#US019|1500',
                        discount: 0,
                        grossPrice: 20,
                        item: '#SalesItem81',
                        itemDescription: 'Sales Item 81',
                        netPrice: 20,
                        origin: 'direct',
                        providerSite: '#US001',
                        quantity: 20,
                        quantityInStockUnit: 20,
                        site: '#US001',
                        unit: { id: 'MILLILITER' },
                        unitToStockUnitConversionFactor: 1,
                        stockUnit: { id: 'LITER' },
                        amountExcludingTax: 400,
                        amountIncludingTax: 400,
                    },
                ],
                billToAddress: {
                    country: { id: 'US' },
                    name: 'Cust address',
                    locationPhoneNumber: '+15550123456',
                    addressLine1: '1st Avenue',
                    addressLine2: '',
                    city: 'One',
                    region: 'Some region',
                    postcode: '0001',
                },
                billToLinkedAddress: '#US019|1500',
                currency: { id: 'USD' },
                incoterm: null,
                paymentTerm: '#DUE_UPON_RECEIPT_ALL',
                billToCustomer: '#US019',
                site: '#US005',
                number: undefined,
                date: date.make(2021, 6, 29),
                dueDate: date.make(2021, 6, 29),
                totalAmountExcludingTax: 400,
                totalAmountIncludingTax: 400,
                isPrinted: false,
                status: 'draft',
            });

            await salesInvoice.$.save({ flushDeferredActions: true });
            assert.deepEqual(salesInvoice.$.context.diagnoses, []);

            const nodeInstance = await context.read(xtremSales.nodes.SalesInvoice, {
                number: await salesInvoice.number,
            });

            const docGenInstance = await xtremMasterData.classes.DocumentNumberGenerator.create(context, {
                nodeInstance,
            });

            assert.equal(await (await docGenInstance.sequenceNumber).id, 'SalesInvoiceUSA');
            assert.equal(await (await (await docGenInstance.sequenceNumber).legislation)?.id, 'US');
        }));

    it('Sales invoice print and send email fail - no report', () =>
        Test.withContext(async context => {
            const salesInvoice = await context.read(
                xtremSales.nodes.SalesInvoice,
                { _id: '#SI6' },
                { forUpdate: true },
            );

            const sandbox = sinon.createSandbox();
            sandbox.stub(xtremReporting.nodes.Report, 'generateReports').returns(Promise.resolve([]));

            await assert.isRejected(
                xtremSales.nodes.SalesInvoice.printSalesInvoiceAndEmail(
                    context,
                    salesInvoice,
                    'mr',
                    'John',
                    'Doe',
                    '<EMAIL>',
                ),
                'The sales invoice cannot be sent. No report has been created.',
            );

            sandbox.restore();
        }));

    it('Create sales invoice from shipment to check grossprofit calculation', () =>
        Test.withContext(
            async context => {
                // This shipment contains a line with an average cost managed item, a FIFO managed item and a not managed in stock item but with a standard cost
                const salesShipment = await context.read(
                    xtremSales.nodes.SalesShipment,
                    { _id: '#SSH16' },
                    { forUpdate: true },
                );

                const generatedInvoices = await xtremSales.nodes.SalesShipment.createSalesInvoicesFromShipment(
                    context,
                    salesShipment,
                    false,
                );
                assert.isNotNull(generatedInvoices);
                assert.isArray(generatedInvoices);
                assert.isNotEmpty(generatedInvoices);

                assert.deepEqual(await salesShipment.invoiceStatus, 'invoiced');

                const toInvoiceLines = await context
                    .query(xtremSales.nodes.SalesShipmentLineToSalesInvoiceLine, {
                        filter: { linkedDocument: { _in: await salesShipment.lines.map(e => e._id).toArray() } },
                    })
                    .toArray();

                assert.deepEqual(toInvoiceLines.length, await salesShipment.lines.length);
                // Check the grossProfit of the invoice
                await salesShipment.lines.forEach(async salesShipmentLine => {
                    const message = `shipment line _id=${salesShipmentLine._id}`;
                    const invoiceLineLink =
                        (await asyncArray(toInvoiceLines).find(
                            async il => (await il.linkedDocument)._id === salesShipmentLine._id,
                        )) ?? null;
                    assert.isNotNull(invoiceLineLink, message);
                    if (!invoiceLineLink) return;
                    const invoiceLine = await context.read(xtremSales.nodes.SalesInvoiceLine, {
                        _id: (await invoiceLineLink.document)._id,
                    });
                    assert.deepEqual(
                        await invoiceLine.stockCostAmountInCompanyCurrency,
                        await salesShipmentLine.stockCostAmountInCompanyCurrency,
                        message,
                    );
                    assert.deepEqual(await invoiceLine.grossProfit, await salesShipmentLine.grossProfit, message);
                });
            },
            { today: '2023-06-22' },
        ));

    it('Mass process - note propagation', () =>
        Test.withContext(async context => {
            const ship18 = await context.read(xtremSales.nodes.SalesShipment, { _id: '#SSH18' }, { forUpdate: true });

            const ship19 = await context.read(xtremSales.nodes.SalesShipment, { _id: '#SSH19' }, { forUpdate: true });

            const salesShipmentLines = await ship18.lines
                .map(async line => {
                    return {
                        salesDocumentLine: line,
                        quantityToProcess: await line.quantity,
                    };
                })
                .toArray();
            const salesShipmentLines2 = await ship19.lines
                .map(async line => {
                    return {
                        salesDocumentLine: line,
                        quantityToProcess: await line.quantity,
                    };
                })
                .toArray();

            salesShipmentLines.push(...salesShipmentLines2);

            const { numberOfInvoices, status, documentsCreated } =
                await xtremSales.nodes.SalesShipment.createSalesInvoicesFromShipmentLines(context, salesShipmentLines);

            assert.equal(numberOfInvoices, 2);
            assert.equal(status, 'salesShipmentIsInvoiced');

            if (!documentsCreated) {
                assert.fail('Failed to create sales invoices from the sales shipments.');
            }

            const invoice1 = await context.read(xtremSales.nodes.SalesInvoice, { _id: documentsCreated[0]._id });
            const invoice2 = await context.read(xtremSales.nodes.SalesInvoice, { _id: documentsCreated[1]._id });

            assert.isNotNull(invoice1);

            const invoice1InternalNote = (await invoice1.internalNote).toString();
            const invoice1ExternalNote = (await invoice1.externalNote).toString();
            const invoice1IsExternalNote = await invoice1.isExternalNote;
            const invoice1IsTransferHeaderNote = await invoice1.isTransferHeaderNote;
            const invoiceIsTransferLineNote = await invoice1.isTransferLineNote;

            assert.isNotEmpty(invoice1InternalNote);
            assert.isNotEmpty(invoice1ExternalNote);
            assert.isTrue(invoice1IsExternalNote);
            assert.isTrue(invoice1IsTransferHeaderNote);
            assert.isTrue(invoiceIsTransferLineNote);

            assert.equal(invoice1InternalNote, (await ship18.internalNote).toString());
            assert.equal(invoice1ExternalNote, (await ship18.externalNote).toString());
            assert.equal(invoice1IsExternalNote, await ship18.isExternalNote);
            assert.equal(invoice1IsTransferHeaderNote, await ship18.isTransferHeaderNote);
            assert.equal(invoiceIsTransferLineNote, await ship18.isTransferLineNote);

            const salesInvoice1Line1 = await invoice1.lines.elementAt(0);

            assert.isNotEmpty((await salesInvoice1Line1.internalNote).toString());
            assert.isNotEmpty((await salesInvoice1Line1.externalNote).toString());
            assert.isTrue(await salesInvoice1Line1.isExternalNote);

            assert.equal(
                (await salesInvoice1Line1.internalNote).toString(),
                (await (await ship18.lines.elementAt(0)).internalNote).toString(),
            );
            assert.equal(
                (await salesInvoice1Line1.externalNote).toString(),
                (await (await ship18.lines.elementAt(0)).externalNote).toString(),
            );
            assert.equal(
                await salesInvoice1Line1.isExternalNote,
                await (
                    await ship18.lines.elementAt(0)
                ).isExternalNote,
            );

            const invoice2InternalNote = (await invoice2.internalNote).toString();
            const invoice2ExternalNote = (await invoice2.externalNote).toString();
            const invoice2IsExternalNote = await invoice2.isExternalNote;
            const invoice2IsTransferHeaderNote = await invoice2.isTransferHeaderNote;
            const invoice2IsTransferLineNote = await invoice2.isTransferLineNote;

            assert.isEmpty(invoice2InternalNote);
            assert.isEmpty(invoice2ExternalNote);
            assert.isFalse(invoice2IsExternalNote);
            assert.isFalse(invoice2IsTransferHeaderNote);
            assert.isFalse(invoice2IsTransferLineNote);

            assert.notEqual(invoice2InternalNote, (await ship19.internalNote).toString());
            assert.notEqual(invoice2ExternalNote, (await ship19.externalNote).toString());
            assert.notEqual(invoice2IsExternalNote, await ship19.isExternalNote);
            assert.equal(invoice2IsTransferHeaderNote, await ship19.isTransferHeaderNote);
            assert.equal(invoice2IsTransferLineNote, await ship19.isTransferLineNote);

            const salesInvoice2Line1 = await invoice2.lines.elementAt(0);

            assert.isEmpty((await salesInvoice2Line1.internalNote).toString());
            assert.isEmpty((await salesInvoice2Line1.externalNote).toString());
            assert.isFalse(await salesInvoice2Line1.isExternalNote);

            assert.notEqual(
                (await salesInvoice2Line1.internalNote).toString(),
                (await (await ship19.lines.elementAt(0)).internalNote).toString(),
            );
            assert.notEqual(
                (await salesInvoice2Line1.internalNote).toString(),
                (await (await ship19.lines.elementAt(0)).internalNote).toString(),
            );
            assert.notEqual(
                (await salesInvoice2Line1.internalNote).toString(),
                (await (await ship19.lines.elementAt(0)).internalNote).toString(),
            );
        }));

    it('Create sales invoice attribute type restricted to', () =>
        Test.withContext(async context => {
            const salesInvoice = await createSalesInvoiceAttributes(context, date.make(2021, 6, 29));
            await assert.isRejected(salesInvoice.$.save());
            assert.deepEqual(salesInvoice.$.context.diagnoses, [
                {
                    severity: 3,
                    path: ['lines', '-1000000003', 'storedAttributes'],
                    message: 'The Project attribute needs to be filled in.',
                },
            ]);
        }));

    it('Create sales invoice default attributes', () =>
        Test.withContext(async context => {
            const salesInvoice = await context.create(xtremSales.nodes.SalesInvoice, {
                lines: [
                    {
                        _action: 'create',
                        charge: 0,
                        // consumptionLinkedAddress: '#US019|1500',
                        discount: 0,
                        grossPrice: 20,
                        item: '#Consulting01',
                        itemDescription: 'Consulting service',
                        netPrice: 20,
                        origin: 'direct',
                        providerSite: '#US001',
                        quantity: 20,
                        quantityInStockUnit: 20,
                        site: '#US001',
                        unit: { id: 'EACH' },
                        unitToStockUnitConversionFactor: 1,
                        stockUnit: { id: 'EACH' },
                        amountExcludingTax: 400,
                        amountIncludingTax: 400,
                    },
                ],
                billToAddress: {
                    country: { id: 'US' },
                    name: 'Cust address',
                    locationPhoneNumber: '+15550123456',
                    addressLine1: '1st Avenue',
                    addressLine2: '',
                    city: 'One',
                    region: 'Some region',
                    postcode: '0001',
                },
                // billToLinkedAddress: '#US019|1500',
                currency: { id: 'USD' },
                incoterm: null,
                paymentTerm: '#DUE_UPON_RECEIPT_ALL',
                billToCustomer: '#US019',
                site: '#US001',
                date: date.make(2021, 6, 25),
                dueDate: date.make(2021, 6, 29),
                totalAmountExcludingTax: 400,
                totalAmountIncludingTax: 400,
                isPrinted: false,
                status: 'draft',
            });
            await salesInvoice.$.save();
            assert.equal(
                JSON.stringify(await (await salesInvoice.lines.elementAt(0)).storedAttributes),
                JSON.stringify({
                    project: 'AttPROJ',
                    task: 'Task1',
                }),
            );
        }));

    // TODO: Activate when https://jira.sage.com/browse/XT-79763 is applied
    it.skip('Test openItem property', () =>
        Test.withContext(async context => {
            let invoice = await context.read(xtremSales.nodes.SalesInvoice, { number: 'SIG1240001' });
            const businessEntityPayment = await (await invoice.billToCustomer).businessEntity;

            const newBaseOpenItem = await context.create(xtremFinanceData.nodes.BaseOpenItem, {
                dueDate: await invoice.dueDate,
                businessEntity: businessEntityPayment,
                businessEntityPayment,
                type: 'customer',
                currency: await invoice.currency,
                transactionAmountDue: await invoice.totalAmountIncludingTax,
                transactionAmountPaid: 0,
                companyAmountDue: await invoice.totalAmountIncludingTax,
                companyAmountPaid: 0,
                financialSiteAmountDue: await invoice.totalAmountIncludingTax,
                financialSiteAmountPaid: 0,
                status: 'notPaid',
                documentSysId: invoice._id,
                documentNumber: await invoice.number,
                documentType: 'salesInvoice',
            });
            await newBaseOpenItem.$.save();
            assert.equal(newBaseOpenItem.$.context.diagnoses, []);

            //  Check if the openItem property have value
            invoice = await context.read(xtremSales.nodes.SalesInvoice, { number: 'SIG1240001' });
            const openItem = await invoice.arOpenItems.elementAt(0);

            assert.equal(await openItem?.documentType, 'salesInvoice');
            assert.equal(await openItem?.documentNumber, 'SIG1240001');
            assert.equal(await openItem?.documentSysId, invoice._id);
        }));

    it('Control on sales invoice with wrong tax type', () =>
        Test.withContext(async context => {
            const salesInvoice = await createSalesInvoiceFrLeg(
                context,
                date.make(2022, 1, 1),
                '#FR_TVA_NORMAL_DEDUCTIBLE_FA_INTRASTAT',
            );
            await assert.isRejected(salesInvoice.$.save());
            assert.deepEqual(salesInvoice.$.context.diagnoses, [
                {
                    severity: 3,
                    path: [],
                    message: "The tax type for all documents needs to be 'Sales' or 'Purchasing and sales'.",
                },
            ]);
        }));

    it('Control on sales invoice with correct tax type', () =>
        Test.withContext(async context => {
            const salesInvoice = await createSalesInvoiceFrLeg(context, date.make(2022, 1, 1));
            assert.isOk(await salesInvoice.$.control());
        }));

    it('Creation number on sales invoice with FR legislation', () =>
        Test.withContext(async context => {
            const salesInvoice = await createSalesInvoiceFrLeg(context, date.make(2022, 1, 1));
            await context.flushDeferredActions();
            assert.deepEqual(await salesInvoice.creationNumber, 'SIEE220001');
        }));

    it('No creation number on sales invoice with US legislation', () =>
        Test.withContext(async context => {
            const salesInvoice = await createSalesInvoice(context, date.make(2021, 6, 27));
            await context.flushDeferredActions();
            assert.deepEqual(await salesInvoice.creationNumber, '');
        }));
});

describe('validateTaxCategoryAndPaymentTerm on Sales Invoice', () => {
    function readSalesInvoice(context: Context, number: string): Promise<xtremSales.nodes.SalesInvoice> {
        return context.read(xtremSales.nodes.SalesInvoice, { number }, { forUpdate: true });
    }

    it('VAT/GST and payment term has discount/penalty', () =>
        Test.withContext(async context => {
            context.setServiceOptionsEnabledFlag(xtremStructure.serviceOptions.intacctActivationOption, true);
            const invoice = await readSalesInvoice(context, 'SIEF240001');
            await invoice.$.set({ paymentTerm: '#TEST_NET_15_CUSTOMER' });
            await assert.isRejected(invoice.$.save());
            assert.deepEqual(invoice.$.context.diagnoses, [
                {
                    message:
                        'Discounts and penalties exist for this payment term: Net 15. You cannot apply discounts and penalties in a VAT or GST transaction.',
                    path: [],
                    severity: 3,
                },
            ]);
        }));
    it('VAT/GST and payment term with no discount/penalty', () =>
        Test.withContext(async context => {
            context.setServiceOptionsEnabledFlag(xtremStructure.serviceOptions.intacctActivationOption, true);

            const paymentTerm = await context.read(
                xtremMasterData.nodes.PaymentTerm,
                { id: 'TEST_NET_15_CUSTOMER' },
                { forUpdate: true },
            );
            await paymentTerm.$.set({ discountAmount: 0, penaltyAmount: 0 });
            await paymentTerm.$.save();
            const invoice = await readSalesInvoice(context, 'SIEF240001');

            await invoice.$.set({ paymentTerm });
            assert.isOk(await invoice.$.control());
        }));
    it('isIntacctActivationOption is disabled', () =>
        Test.withContext(async context => {
            const invoice = await readSalesInvoice(context, 'SIEF240001');
            await invoice.$.set({ paymentTerm: '#TEST_NET_15_CUSTOMER' });
            assert.isOk(await invoice.$.control());
        }));
    it('No VAT/GST and payment term has discount/penalty', () =>
        Test.withContext(async context => {
            context.setServiceOptionsEnabledFlag(xtremStructure.serviceOptions.intacctActivationOption, true);
            const invoice = await readSalesInvoice(context, 'SIEF240001');
            const salesInvoiceTax = await context.read(
                xtremSales.nodes.SalesInvoiceTax,
                { document: invoice._id, _sortValue: 20 },
                { forUpdate: true },
            );
            await salesInvoiceTax.$.delete();
            assert.deepEqual(salesInvoiceTax.$.context.diagnoses, []);

            await invoice.$.set({ paymentTerm: '#TEST_NET_15_CUSTOMER' });
            assert.isOk(await invoice.$.control());
        }));
});

describe('Posting details', () => {
    it('Gets posting details successfully', () =>
        Test.withContext(async context => {
            const siDocumentNumber = 'SIG1240001';
            const siDocument = await context.read(xtremSales.nodes.SalesInvoice, { number: siDocumentNumber });
            assert.equal(await siDocument.postingDetails.length, 2);

            const apInvoice = await siDocument.postingDetails.at(0);
            assert.equal(await apInvoice?.documentType, 'salesInvoice');
            assert.equal(await apInvoice?.documentNumber, siDocumentNumber);
            assert.equal(await apInvoice?.targetDocumentType, 'accountsReceivableInvoice');

            const journalEntry = await siDocument.postingDetails.at(1);
            assert.equal(await journalEntry?.documentType, 'arInvoice');
            assert.equal(await journalEntry?.documentNumber, siDocumentNumber);
            assert.equal(await journalEntry?.targetDocumentType, 'journalEntry');
        }));
});

describe('Payment status listener', () => {
    it('Updates payment and display status correctly', () =>
        Test.withContext(
            async context => {
                const salesInvoice = await context.read(xtremSales.nodes.SalesInvoice, { number: 'SI6' });
                assert.equal(await salesInvoice.paymentStatus, 'notPaid');
                assert.equal(await salesInvoice.displayStatus, 'posted');
                await xtremSales.nodes.SalesInvoice.setSalesInvoicePaymentStatus(context, {
                    _id: salesInvoice._id,
                    paymentStatus: 'partiallyPaid',
                });
                const updatedSalesInvoice = await context.read(xtremSales.nodes.SalesInvoice, { number: 'SI6' });
                assert.equal(await updatedSalesInvoice.paymentStatus, 'partiallyPaid');
                assert.equal(await updatedSalesInvoice.displayStatus, 'partiallyPaid');
            },
            {
                testActiveServiceOptions: [xtremFinanceData.serviceOptions.paymentTrackingOption],
            },
        ));
});

describe('Discount before payment date control', () => {
    it('Failed control due to discountPaymentBeforeDate being after the due date', () =>
        Test.withContext(async (context: Context) => {
            const salesInvoice = await context.read(
                xtremSales.nodes.SalesInvoice,
                { number: 'SI3' },
                { forUpdate: true },
            );
            const dueDate = await salesInvoice.dueDate;
            // Update the discountPaymentBeforeDate to be after the due date
            await salesInvoice.$.set({ discountPaymentBeforeDate: dueDate.addDays(1) });
            await assert.isRejected(
                xtremSales.nodes.SalesInvoice.post(context, salesInvoice),
                'The discount date needs to be on or before the due date.',
            );
        }));
});
