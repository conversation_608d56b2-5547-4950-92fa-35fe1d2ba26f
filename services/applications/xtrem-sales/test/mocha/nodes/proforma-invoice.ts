// tslint:disable:no-duplicate-string
import { Test, TextStream, date } from '@sage/xtrem-core';
import * as xtremSystem from '@sage/xtrem-system';
import { assert } from 'chai';
import * as xtremSales from '../../../index';

describe('ProformaInvoice node', () => {
    it('Check if issueDate and createdBy are returning correctly', () =>
        Test.withContext(async context => {
            const newProformaInvoice = await context.create(xtremSales.nodes.ProformaInvoice, {
                salesOrder: '#SO1',
                expirationDate: date.today(),
                customerComment: TextStream.fromString('Customer Comment'),
            });
            await newProformaInvoice.$.save();
            assert.deepEqual(newProformaInvoice.$.context.diagnoses, []);
            const user = await context.read(xtremSystem.nodes.User, { _id: context.userId });

            assert.equal((await newProformaInvoice.issueDate).toString(), date.today().toString());
            assert.equal(await newProformaInvoice.createdBy, await user.displayName);
        }, {}));
});

describe('printProformaInvoice asyncMutation', () => {
    it('Print a proforma invoice with isActive = true', () =>
        Test.withContext(
            async context => {
                const salesOrder = await context.read(xtremSales.nodes.SalesOrder, { _id: '#SO2' });

                assert.isTrue(await xtremSales.nodes.ProformaInvoice.beforePrintProformaInvoice(context, salesOrder));
            },
            { locale: 'en-US' },
        ));

    it('Print a proforma invoice with isActive = false', () =>
        Test.withContext(
            async context => {
                const salesOrder = await context.read(xtremSales.nodes.SalesOrder, { _id: '#SO3' });

                const proformaInvoice = await context.read(
                    xtremSales.nodes.ProformaInvoice,
                    {
                        _id: (await salesOrder.proformaInvoices?.find(invoice => invoice.isActive))?._id,
                    },
                    {
                        forUpdate: true,
                    },
                );

                await proformaInvoice.$.set({
                    isActive: false,
                });

                assert.equal(await proformaInvoice.isActive, false);

                await proformaInvoice.$.save();

                await assert.isRejected(
                    xtremSales.nodes.ProformaInvoice.beforePrintProformaInvoice(context, salesOrder),
                    'Could not print an inactive proforma invoice.',
                );
            },
            { locale: 'en-US' },
        ));
});
