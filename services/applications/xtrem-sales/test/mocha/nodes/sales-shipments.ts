// tslint:disable:no-duplicate-string
import type { Context, decimal, Reference } from '@sage/xtrem-core';
import { asyncArray, date, Test, ValidationSeverity } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremStockData from '@sage/xtrem-stock-data';
import * as xtremSystem from '@sage/xtrem-system';
import { assert } from 'chai';
import * as sinon from 'sinon';
import * as xtremSales from '../../../index';
import * as testHelper from '../../fixtures/lib';

const { withSequenceNumberContext } = xtremMasterData.functions.testLib;

function prepareSalesOrderLines(
    context: Context,
    idArray: Array<{ _id: string; quantityToProcess: decimal }>,
): Promise<{ salesDocumentLine: xtremSales.nodes.SalesOrderLine; quantityToProcess: decimal }[]> {
    const lines = context.query(xtremSales.nodes.SalesOrderLine, {
        filter: { _id: { _in: idArray.map(entry => entry._id) } },
    });

    return lines
        .map((line: xtremSales.nodes.SalesOrderLine, index: number) => {
            return { salesDocumentLine: line, quantityToProcess: idArray[index].quantityToProcess };
        })
        .toArray();
}

describe('SalesShipmentNode', () => {
    afterEach(() => sinon.restore());
    it('Create sales shipment 1', () =>
        Test.withContext(
            async context => {
                const salesShipmentsFromOrderLines =
                    await xtremSales.nodes.SalesOrder.createSalesShipmentsFromOrderLines(
                        context,
                        await prepareSalesOrderLines(context, [{ _id: '#SO6|60600', quantityToProcess: 11 }]),
                    );
                await context.flushDeferredActions();

                const salesShipment = await context.query(xtremSales.nodes.SalesShipment).toArray();

                assert.equal(salesShipmentsFromOrderLines?.documentsCreated?.length, 1);
                assert.equal(await salesShipmentsFromOrderLines?.documentsCreated?.[0].number, 'SH200001');

                assert.equal(await salesShipment[0].number, 'SH200001');
                assert.equal(String(await salesShipment[0].effectiveDate), String(date.make(2020, 12, 24)));
                assert.equal(String(await salesShipment[0].deliveryDate), String(date.make(2020, 12, 31)));

                assert.equal(await (await salesShipment[0].lines.elementAt(0)).quantity, 11);
                assert.equal(
                    await (
                        await (await salesShipment[0].lines.elementAt(0)).salesOrderLines.elementAt(0)
                    ).quantity,
                    11,
                );
                assert.equal(
                    await (
                        await (
                            await (await salesShipment[0].lines.elementAt(0)).salesOrderLines.elementAt(0)
                        ).linkedDocument
                    ).documentNumber,
                    'SO6',
                );
                assert.equal(
                    await (
                        await (
                            await (await salesShipment[0].lines.elementAt(0)).salesOrderLines.elementAt(0)
                        ).linkedDocument
                    )._sortValue,
                    60600,
                );

                const salesOrderLine = await context.read(xtremSales.nodes.SalesOrderLine, { _id: '#SO6|60600' });

                assert.equal(await salesOrderLine.status, 'inProgress');
                assert.equal(await salesOrderLine.shippingStatus, 'partiallyShipped');

                assert.equal(await (await salesOrderLine.document).status, 'inProgress');
                assert.equal(await (await salesOrderLine.document).shippingStatus, 'partiallyShipped');
            },
            { today: '2020-11-24' },
        ));

    it('Create sales shipment 2', () =>
        Test.withContext(
            async context => {
                const orderLines = await prepareSalesOrderLines(context, [
                    { _id: '#SO5|60400', quantityToProcess: 30 },
                    { _id: '#SO5|60500', quantityToProcess: 10 },
                    { _id: '#SO6|60600', quantityToProcess: 12 },
                ]);

                const salesShipmentsCreated = await xtremSales.nodes.SalesOrder.createSalesShipmentsFromOrderLines(
                    context,
                    orderLines,
                );
                await context.flushDeferredActions();

                const salesShipment = await context.query(xtremSales.nodes.SalesShipment).toArray();

                assert.equal(salesShipmentsCreated?.documentsCreated?.length, 3);
                assert.equal(await salesShipmentsCreated?.documentsCreated?.[0].number, 'SH200001');
                assert.equal(await salesShipmentsCreated?.documentsCreated?.[1].number, 'SH200002');
                assert.equal(await salesShipmentsCreated?.documentsCreated?.[2].number, 'SH200003');

                const salesShipmentLines = await salesShipment[0].lines
                    .map(async line => {
                        return {
                            salesOrderLines: line,
                            quantity: await line.quantity,
                        };
                    })
                    .toArray();
                const salesShipmentLines2 = await salesShipment[1].lines
                    .map(async line => {
                        return {
                            salesOrderLines: line,
                            quantity: await line.quantity,
                        };
                    })
                    .toArray();

                const salesShipmentLines3 = await salesShipment[2].lines
                    .map(async line => {
                        return {
                            salesOrderLines: line,
                            quantity: await line.quantity,
                        };
                    })
                    .toArray();
                salesShipmentLines.push(...salesShipmentLines2, ...salesShipmentLines3);

                assert.equal(await salesShipment[1].number, 'SH200002');
                assert.equal(await salesShipment[2].number, 'SH200003');
                assert.equal(String(salesShipmentLines[1].quantity), '10');
                assert.equal(String(salesShipmentLines[2].quantity), '12');
                assert.equal(
                    String(
                        await (
                            await (await salesShipment[1].lines.elementAt(0)).salesOrderLines.elementAt(0)
                        ).quantityInStockUnit,
                    ),
                    '283.495',
                );
                assert.equal(String(await salesShipmentLines[1].salesOrderLines.quantity), '10');
                assert.equal(String(await salesShipmentLines[2].salesOrderLines.quantity), '12');

                const salesOrderLine = await context.read(xtremSales.nodes.SalesOrderLine, { _id: '#SO5|60400' });

                assert.equal(await salesOrderLine.status, 'closed');
                assert.equal(await salesOrderLine.shippingStatus, 'shipped');

                assert.equal(await (await salesOrderLine.document).status, 'closed');
                assert.equal(await (await salesOrderLine.document).shippingStatus, 'shipped');
            },
            { today: '2020-11-24' },
        ));

    it('Create sales shipment 3', () =>
        Test.withContext(
            async context => {
                const salesShipmentsCreated = await xtremSales.nodes.SalesOrder.createSalesShipmentsFromOrderLines(
                    context,
                    await prepareSalesOrderLines(context, [{ _id: '#SO6|60600', quantityToProcess: 11 }]),
                    { processAllShippableLines: true },
                );
                await context.flushDeferredActions();
                assert.isArray(salesShipmentsCreated.documentsCreated);

                if (!salesShipmentsCreated.documentsCreated) {
                    assert.fail('Failed to create sales shipments from the sales order lines.');
                }

                const salesShipment = await context.read(xtremSales.nodes.SalesShipment, {
                    _id: salesShipmentsCreated.documentsCreated[0]._id,
                });

                assert.equal(await salesShipment.number, 'SH200001');
                assert.equal(
                    String(await (await (await salesShipment.lines.elementAt(0)).discountCharges.elementAt(0)).amount),
                    '0.25',
                );
            },
            { today: '2020-11-24' },
        ));

    it('Create sales shipment 4', () =>
        Test.withContext(
            async context => {
                const salesShipmentsCreated = await xtremSales.nodes.SalesOrder.createSalesShipmentsFromOrderLines(
                    context,
                    await prepareSalesOrderLines(context, [{ _id: '#SO5|60500', quantityToProcess: 11 }]),
                    { processAllShippableLines: true },
                );
                await context.flushDeferredActions();
                assert.isArray(salesShipmentsCreated.documentsCreated);

                if (!salesShipmentsCreated.documentsCreated) {
                    assert.fail('Failed to create sales shipments from the sales order lines.');
                }

                const salesShipment = await context.read(xtremSales.nodes.SalesShipment, {
                    _id: salesShipmentsCreated.documentsCreated[0]._id,
                });

                assert.equal(await salesShipment.number, 'SH200001');
            },
            { today: '2020-11-24' },
        ));

    it('Create sales shipment and repost', () =>
        Test.withContext(
            async context => {
                const salesShipmentsCreated = await xtremSales.nodes.SalesOrder.createSalesShipmentsFromOrderLines(
                    context,
                    await prepareSalesOrderLines(context, [{ _id: '#SO6|60600', quantityToProcess: 11 }]),
                );
                await context.flushDeferredActions();
                assert.isArray(salesShipmentsCreated.documentsCreated);

                if (!salesShipmentsCreated.documentsCreated) {
                    assert.fail('Failed to create sales shipments from the sales order lines.');
                }

                const salesShipment = await context.read(xtremSales.nodes.SalesShipment, {
                    _id: salesShipmentsCreated.documentsCreated[0]._id,
                });

                const shipmentLines = await asyncArray(await salesShipment.lines.toArray())
                    .map(async line => {
                        return {
                            baseDocumentLineSysId: line._id,
                            storedAttributes:
                                (await line.storedAttributes) as xtremMasterData.interfaces.StoredAttributes,
                            storedDimensions: (await line.storedDimensions) || {},
                        };
                    })
                    .toArray();

                await assert.isRejected(
                    xtremSales.nodes.SalesShipment.repost(context, salesShipment, shipmentLines),
                    "You can only repost a sales shipment if the status is 'Failed' or 'Not recorded.'",
                );
            },
            { today: '2020-11-24' },
        ));

    it('Create sales shipment fail 1', () =>
        Test.withContext(
            async context => {
                await assert.isRejected(
                    xtremSales.nodes.SalesOrder.createSalesShipmentsFromOrderLines(
                        context,
                        await prepareSalesOrderLines(context, [{ _id: '#SO2|60200', quantityToProcess: 11 }]),
                    ),
                );
            },
            { today: '2020-11-28' },
        ));

    it('Create sales shipment fail 2', () =>
        Test.withContext(
            async context => {
                const salesShipmentsCreator = await new xtremSales.classes.SalesShipmentsCreator(
                    context,
                ).prepareNodeCreateData(
                    await prepareSalesOrderLines(context, [{ _id: '#SO6|60600', quantityToProcess: 11 }]),
                    { processAllShippableLines: true },
                );
                assert.isEmpty(salesShipmentsCreator.salesOutputDocuments);
            },
            { today: '2100-01-01' },
        ));

    it('Create sales shipment fail 3', () =>
        Test.withContext(
            async context => {
                const salesShipmentsCreator = await new xtremSales.classes.SalesShipmentsCreator(
                    context,
                ).prepareNodeCreateData(
                    await prepareSalesOrderLines(context, [{ _id: '#SO2|60200', quantityToProcess: 11 }]),
                    { processAllShippableLines: true },
                );
                assert.isEmpty(salesShipmentsCreator.salesOutputDocuments);
            },
            { today: '2020-11-28' },
        ));

    it('Update sales shipment 1', () =>
        Test.withContext(
            async context => {
                let salesShipment = await context.read(
                    xtremSales.nodes.SalesShipment,
                    { _id: '#SSH1' },
                    { forUpdate: true },
                );

                await salesShipment.$.set({ lines: [{ _sortValue: 130000, _action: 'delete' }] });

                await salesShipment.$.save();
                let salesOrderLine = await context.read(xtremSales.nodes.SalesOrderLine, { _id: '#SO5|60500' });

                assert.isFalse(await context.exists(xtremSales.nodes.SalesShipmentLine, { _id: '#SSH1|130000' }));
                assert.isFalse(
                    await context.exists(xtremSales.nodes.SalesOrderLineToSalesShipmentLine, { _id: '#SSH1|130000' }),
                );

                assert.equal(await salesOrderLine.status, 'inProgress');
                assert.equal(await salesOrderLine.shippingStatus, 'partiallyShipped');

                assert.equal(await (await salesOrderLine.document).status, 'inProgress');
                assert.equal(await (await salesOrderLine.document).shippingStatus, 'partiallyShipped');

                salesShipment = await context.read(
                    xtremSales.nodes.SalesShipment,
                    { _id: '#SSH2' },
                    { forUpdate: true },
                );

                await salesShipment.$.set({ lines: [{ _sortValue: 130100, _action: 'delete' }] });

                await salesShipment.$.save();
                salesOrderLine = await context.read(xtremSales.nodes.SalesOrderLine, { _id: '#SO5|60500' });

                assert.isFalse(await context.exists(xtremSales.nodes.SalesShipmentLine, { _id: '#SSH2|130100' }));
                assert.isFalse(
                    await context.exists(xtremSales.nodes.SalesOrderLineToSalesShipmentLine, { _id: '#SSH2|130100' }),
                );

                assert.equal(await salesOrderLine.status, 'pending');
                assert.equal(await salesOrderLine.shippingStatus, 'notShipped');

                assert.equal(await (await salesOrderLine.document).status, 'pending');
                assert.equal(await (await salesOrderLine.document).shippingStatus, 'notShipped');
            },
            { today: '2020-11-24' },
        ));

    it('Update sales shipment 3', () =>
        Test.withContext(
            async context => {
                const salesShipment = await context.read(
                    xtremSales.nodes.SalesShipment,
                    { _id: '#SSH1' },
                    { forUpdate: true },
                );

                await salesShipment.$.set({
                    lines: [
                        {
                            item: '#Chemical D',
                            quantity: 30,
                            unitToStockUnitConversionFactor: 1.1,
                            salesOrderLines: [{ linkedDocument: '#SO5|60400', quantity: 30 }],
                            _sortValue: 50,
                            _action: 'create',
                        },
                        { _sortValue: 130000, quantity: 20, _action: 'update' },
                    ],
                });

                await salesShipment.$.save();
                const salesOrderLine = await context.read(xtremSales.nodes.SalesOrderLine, { _id: '#SO5|60500' });

                assert.equal(await salesOrderLine.status, 'closed');
                assert.equal(await salesOrderLine.shippingStatus, 'shipped');

                assert.equal(await (await salesOrderLine.document).status, 'closed');
                assert.equal(await (await salesOrderLine.document).shippingStatus, 'shipped');

                const salesOrderLineToSalesShipmentLine = (
                    await context
                        .query(xtremSales.nodes.SalesOrderLineToSalesShipmentLine, { orderBy: { _id: +1 } })
                        .toArray()
                ).pop();

                assert.equal(await salesOrderLineToSalesShipmentLine?.quantity, 30);
            },
            { today: '2020-11-24' },
        ));

    it('Check required quantity after shipment', () =>
        Test.withContext(
            async context => {
                const requiredQuantity = await (
                    await context.read(xtremMasterData.nodes.ItemSite, { item: '#Chemical D', site: '#US002' })
                ).requiredQuantity;
                const salesShipmentsFromOrderLines =
                    await xtremSales.nodes.SalesOrder.createSalesShipmentsFromOrderLines(
                        context,
                        await prepareSalesOrderLines(context, [{ _id: '#SO6|60600', quantityToProcess: 11 }]),
                    );
                await context.flushDeferredActions();

                const salesShipmentId = (await context.query(xtremSales.nodes.SalesShipment).toArray())[0]._id;

                let salesShipment = await context.read(
                    xtremSales.nodes.SalesShipment,
                    { _id: salesShipmentId },
                    { forUpdate: true },
                );

                assert.equal(salesShipmentsFromOrderLines?.documentsCreated?.length, 1);
                assert.equal(await salesShipmentsFromOrderLines?.documentsCreated?.[0].number, 'SH220001');

                assert.equal(await (await salesShipment.lines.elementAt(0)).quantity, 11);

                await xtremStockData.nodes.Stock.updateAllocations(context, {
                    documentLine: salesShipment.lines.elementAt(0),
                    allocationUpdates: [
                        {
                            action: 'create',
                            quantity: await (await salesShipment.lines.elementAt(0)).quantityInStockUnit,
                            stockRecord: context.read(xtremStockData.nodes.Stock, { _id: '46' }),
                        },
                    ],
                });

                await xtremSales.nodes.SalesShipment.confirm(context, salesShipment);

                // re read cause the mutation doesn't update the document loaded here
                salesShipment = await context.read(
                    xtremSales.nodes.SalesShipment,
                    { _id: salesShipment._id },
                    { forUpdate: true },
                );

                const result = await xtremSales.nodes.SalesShipment.postToStock(context, salesShipment);

                assert.isNotEmpty(result);

                await xtremStockData.functions.testLib.simulateStockTransaction(
                    context,
                    salesShipment._id,
                    xtremSales.nodes.SalesShipment,
                    'issue',
                    xtremSales.nodes.SalesShipment.onStockReply,
                    { stockUpdateResultStatus: 'decreased' },
                );

                await Test.rollbackCache(context);

                salesShipment = await context.read(xtremSales.nodes.SalesShipment, { _id: salesShipment._id });

                assert.equal(await salesShipment.status, 'shipped');

                const newRequiredQuantity = await (
                    await context.read(xtremMasterData.nodes.ItemSite, { item: '#Chemical D', site: '#US002' })
                ).requiredQuantity;
                assert.equal(Number(newRequiredQuantity - requiredQuantity), -311.84);
            },
            { today: '2022-08-05' },
        ));

    it('Update sales shipment fail - cannot add line on already shipped shipment', () =>
        Test.withContext(async context => {
            const salesShipment = await context.read(
                xtremSales.nodes.SalesShipment,
                { _id: '#SSH3' },
                { forUpdate: true },
            );

            await salesShipment.$.set({
                lines: [
                    {
                        item: '#Chemical D',
                        quantity: 11,
                        unitToStockUnitConversionFactor: 1.1,
                        _action: 'create',
                    },
                ],
            });
            await assert.isRejected(salesShipment.$.save(), 'The record was not updated.');
            assert.deepEqual(salesShipment.$.context.diagnoses, [
                {
                    message:
                        'Line creation is not allowed. The sales shipment is ready to ship or has already been shipped.',
                    path: ['lines', '-1000000001'],
                    severity: 3,
                },
            ]);
        }));

    it('Update sales shipment fail 3', () =>
        Test.withContext(async context => {
            const salesShipment = await context.read(
                xtremSales.nodes.SalesShipment,
                { _id: '#SSH1' },
                { forUpdate: true },
            );

            assert.equal(await salesShipment.lines.length, 2);

            await salesShipment.$.set({
                lines: [
                    {
                        item: '#Chemical D',
                        quantity: 11,
                        unitToStockUnitConversionFactor: 1.1,
                        salesOrderLines: [{ linkedDocument: '#SO5|60500', quantity: 11 }],
                        _action: 'create',
                    },
                ],
            });
            await assert.isRejected(salesShipment.$.save());
        }));

    it('Update sales shipment fail 4', () =>
        Test.withContext(async context => {
            const salesShipment = await context.read(
                xtremSales.nodes.SalesShipment,
                { _id: '#SSH1' },
                { forUpdate: true },
            );

            await assert.isRejected(
                salesShipment.$.set({
                    lines: [
                        {
                            item: '#Chemical D',
                            quantity: 33,
                            unitToStockUnitConversionFactor: 1.1,
                            salesOrderLines: [{ linkedDocument: '#unknown|000000', quantity: 33 }],
                            _action: 'create',
                        },
                    ],
                }),
            );
        }));

    it('Update sales shipment fail 5', () =>
        Test.withContext(async context => {
            const salesShipment = await context.read(
                xtremSales.nodes.SalesShipment,
                { _id: '#SSH3' },
                { forUpdate: true },
            );

            await salesShipment.$.set({
                lines: [
                    {
                        _sortValue: await (await salesShipment.lines.elementAt(0))._sortValue,
                        quantity: 22,
                        _action: 'update',
                    },
                ],
            });
            await assert.isRejected(salesShipment.$.save());
        }));

    it('Update sales shipment fail 6 - updating an item on an existing shipment line', () =>
        Test.withContext(async context => {
            const salesShipment = await context.read(
                xtremSales.nodes.SalesShipment,
                { _id: '#SSH3' },
                { forUpdate: true },
            );

            await salesShipment.$.set({
                lines: [
                    {
                        _sortValue: await (await salesShipment.lines.elementAt(0))._sortValue,
                        item: '#Chemical B',
                        _action: 'update',
                    },
                ],
            });
            await assert.isRejected(salesShipment.$.save());
        }));

    it('Update sales shipment fail 7 - updating a sales unit on an existing shipment line', () =>
        Test.withContext(async context => {
            const salesShipment = await context.read(
                xtremSales.nodes.SalesShipment,
                { _id: '#SSH3' },
                { forUpdate: true },
            );

            await salesShipment.$.set({
                lines: [
                    {
                        _sortValue: await (await salesShipment.lines.elementAt(0))._sortValue,
                        unit: '#CUBIC_METER',
                        _action: 'update',
                    },
                ],
            });
            await assert.isRejected(salesShipment.$.save());
        }));

    it("Update sales shipment fail 8 - shouldn't be able to have a larger allocated quantity than the shipping quantity.", () =>
        Test.withContext(async context => {
            const salesShipment = await context.read(
                xtremSales.nodes.SalesShipment,
                { _id: '#SSH5' },
                { forUpdate: true },
            );
            await xtremStockData.nodes.Stock.updateAllocations(context, {
                documentLine: salesShipment.lines.elementAt(0),
                allocationUpdates: [
                    {
                        action: 'create',
                        quantity: 850.49,
                        stockRecord: context.read(xtremStockData.nodes.Stock, { _id: '37' }),
                    },
                ],
            });

            await salesShipment.$.set({
                lines: [
                    {
                        _sortValue: await (await salesShipment.lines.elementAt(0))._sortValue,
                        quantity: 20,
                        _action: 'update',
                    },
                ],
            });
            await assert.isRejected(salesShipment.$.save());
        }));

    it('XT-10704 - Issue on return request status on sales shipment', () =>
        Test.withContext(
            async context => {
                const salesShipmentsCreated = await xtremSales.nodes.SalesOrder.createSalesShipmentsFromOrderLines(
                    context,
                    await prepareSalesOrderLines(context, [
                        { _id: '#SO5|60400', quantityToProcess: 11 },
                        { _id: '#SO5|60500', quantityToProcess: 10 },
                    ]),
                );
                await context.flushDeferredActions();
                assert.isArray(salesShipmentsCreated.documentsCreated);

                if (!salesShipmentsCreated.documentsCreated) {
                    assert.fail('Failed to create sales shipments from the sales order lines.');
                }

                let salesShipment = await context.read(xtremSales.nodes.SalesShipment, {
                    _id: salesShipmentsCreated.documentsCreated[0]._id,
                });

                salesShipment = await context.read(
                    xtremSales.nodes.SalesShipment,
                    { _id: salesShipment._id },
                    { forUpdate: true },
                );

                const line1 = await salesShipment.lines.elementAt(0);
                const line2 = await salesShipment.lines.elementAt(1);

                await line1.$.set({ status: 'shipped' });
                await line2.$.set({ status: 'shipped' });

                await salesShipment.$.save();

                const { documentsCreated: salesReturnRequests } = await (
                    await new xtremSales.classes.SalesReturnRequestCreator(context).prepareNodeCreateData([
                        { salesDocumentLine: await salesShipment.lines.elementAt(0) },
                    ])
                ).createSalesOutputDocuments();

                salesShipment = await context.read(xtremSales.nodes.SalesShipment, { _id: salesShipment._id });

                assert.equal(await salesShipment.returnRequestStatus, 'returnPartiallyRequested');

                const salesReturnRequest = await context.read(
                    xtremSales.nodes.SalesReturnRequest,
                    { _id: salesReturnRequests?.[0]?._id },
                    { forUpdate: true },
                );
                await salesReturnRequest.$.set({ approvalStatus: 'pendingApproval' });

                await salesReturnRequest.$.save();

                await xtremSales.nodes.SalesReturnRequest.reject(context, salesReturnRequest);

                salesShipment = await context.read(xtremSales.nodes.SalesShipment, { _id: salesShipment._id });

                assert.equal(await salesShipment.returnRequestStatus, 'noReturnRequested');

                const { documentsCreated: salesReturnRequests2 } = await (
                    await new xtremSales.classes.SalesReturnRequestCreator(context).prepareNodeCreateData([
                        { salesDocumentLine: await salesShipment.lines.elementAt(0) },
                    ])
                ).createSalesOutputDocuments();

                await (
                    await new xtremSales.classes.SalesReturnRequestCreator(context).prepareNodeCreateData([
                        { salesDocumentLine: await salesShipment.lines.elementAt(1) },
                    ])
                ).createSalesOutputDocuments();

                // XT-10704 comment part
                salesShipment = await context.read(xtremSales.nodes.SalesShipment, { _id: salesShipment._id });

                assert.equal(await salesShipment.returnRequestStatus, 'returnRequested');

                const salesReturnRequest2 = await context.read(
                    xtremSales.nodes.SalesReturnRequest,
                    { _id: salesReturnRequests2?.[0]?._id },
                    { forUpdate: true },
                );
                await salesReturnRequest2.$.set({ approvalStatus: 'pendingApproval' });

                await salesReturnRequest2.$.save();

                await xtremSales.nodes.SalesReturnRequest.approve(context, salesReturnRequest2);

                salesShipment = await context.read(xtremSales.nodes.SalesShipment, { _id: salesShipment._id });

                assert.equal(await salesShipment.returnRequestStatus, 'returnRequested');
            },
            { user: { email: '<EMAIL>' } },
        ));

    it('XT-18366 -> createShipmentsFromOrder ', () =>
        Test.withContext(
            async context => {
                const salesOrder = await context.read(
                    xtremSales.nodes.SalesOrder,
                    { _id: '#SO5' },
                    { forUpdate: true },
                );
                const salesOrderFirstLine = await salesOrder.lines.at(0);

                let i = 0;

                while (i < 12) {
                    await salesOrder.$.set({
                        lines: [
                            {
                                _action: 'create',
                                origin: await salesOrderFirstLine!.origin,
                                status: await salesOrderFirstLine!.status,
                                invoiceStatus: await salesOrderFirstLine!.invoiceStatus,
                                item: await salesOrderFirstLine!.item,
                                site: await salesOrderFirstLine!.site,
                                stockSite: await salesOrderFirstLine!.stockSite,
                                shipToCustomerAddress: await salesOrderFirstLine!.shipToCustomerAddress,
                                quantity: await salesOrderFirstLine!.quantity,
                                unit: await salesOrderFirstLine!.unit,
                            },
                        ],
                    });
                    i += 1;
                }

                await salesOrder.$.save();

                const salesShipmentsCreated = await xtremSales.nodes.SalesOrder.createShipmentsFromOrder(
                    context,
                    salesOrder,
                );
                await context.flushDeferredActions();
                assert.isArray(salesShipmentsCreated);

                if (!salesShipmentsCreated) {
                    assert.fail('Failed to create sales shipments from the sales orders.');
                }

                const salesShipment = await context.read(xtremSales.nodes.SalesShipment, {
                    _id: salesShipmentsCreated[0]._id,
                });

                assert.equal(await salesShipment.lines.length, 14);
            },
            { user: { email: '<EMAIL>' } },
        ));
    it("should validate the shipment and change its status to 'readyToShip'", () =>
        Test.withContext(
            async context => {
                let salesShipment = await context.read(
                    xtremSales.nodes.SalesShipment,
                    { _id: '#SSH5' },
                    { forUpdate: true },
                );

                await xtremStockData.nodes.Stock.updateAllocations(context, {
                    documentLine: salesShipment.lines.elementAt(0),
                    allocationUpdates: [
                        {
                            action: 'create',
                            quantity: 850.49,
                            stockRecord: context.read(xtremStockData.nodes.Stock, { _id: '37' }),
                        },
                    ],
                });

                await xtremSales.nodes.SalesShipment.confirm(context, salesShipment);

                // re read cause the mutation doesn't update the document loaded here
                salesShipment = await context.read(xtremSales.nodes.SalesShipment, { _id: '#SSH5' });

                assert.equal(await salesShipment.status, 'readyToShip');

                await salesShipment.lines.forEach(async shipmentLine => {
                    assert.equal(await shipmentLine.status, 'readyToShip');
                });
            },
            { now: '2020-11-27T10:00:00' },
        ));
    it("shouldn't validate shipments if status is not ready to process", () =>
        Test.withContext(
            async context => {
                const salesShipment = await context.read(xtremSales.nodes.SalesShipment, { _id: '#SSH3' });

                await xtremStockData.nodes.Stock.updateAllocations(context, {
                    documentLine: salesShipment.lines.elementAt(0),
                    allocationUpdates: [
                        {
                            action: 'create',
                            quantity: 30,
                            stockRecord: context.read(xtremStockData.nodes.Stock, { _id: '37' }),
                        },
                    ],
                });

                await assert.isRejected(
                    xtremSales.nodes.SalesShipment.confirm(context, salesShipment),
                    "A shipment can only be confirmed if the status is 'Ready to process'.",
                );
            },
            { now: '2020-11-27T10:00:00' },
        ));
    it("shouldn't validate shipments if not all line are allocated", () =>
        Test.withContext(
            async context => {
                const salesShipment = await context.read(
                    xtremSales.nodes.SalesShipment,
                    { _id: '#SSH5' },
                    { forUpdate: true },
                );

                await assert.isRejected(
                    xtremSales.nodes.SalesShipment.confirm(context, salesShipment),
                    'You need to allocate all lines before confirming.',
                );
            },
            { now: '2020-11-27T10:00:00' },
        ));
    it('Verify chronological control between two sales shipments on creation', () =>
        withSequenceNumberContext(
            'SalesShipment',
            { isChronological: true },
            async context => {
                await xtremSales.nodes.SalesOrder.createSalesShipmentsFromOrderLines(
                    context,
                    await prepareSalesOrderLines(context, [{ _id: '#SO6|60600', quantityToProcess: 11 }]),
                );

                // rollbackCache function will flush deferred actions and saves
                await Test.rollbackCache(context);

                const salesShipment = await context.read(
                    xtremSales.nodes.SalesShipment,
                    { number: 'SH200001' },
                    { forUpdate: true },
                );
                await salesShipment.$.set({ date: date.make(2020, 12, 25) });
                await salesShipment.$.save();

                await assert.isRejected(
                    xtremSales.nodes.SalesOrder.createSalesShipmentsFromOrderLines(
                        context,
                        await prepareSalesOrderLines(context, [{ _id: '#SO6|60600', quantityToProcess: 11 }]),
                    ),
                    'The record was not created.',
                );
                assert.deepEqual(context.diagnoses, [
                    {
                        message: 'The document date 2020-12-24 is earlier than the previous document date 2020-12-25.',
                        path: [],
                        severity: ValidationSeverity.error,
                    },
                ]);
            },
            { today: '2020-11-24' },
        ));
    it('Verify chronological control between two sales shipments on change', () =>
        Test.withContext(
            async context => {
                await xtremSales.nodes.SalesOrder.createSalesShipmentsFromOrderLines(
                    context,
                    await prepareSalesOrderLines(context, [{ _id: '#SMOKETEST5|10', quantityToProcess: 11 }]),
                );

                await xtremSales.nodes.SalesOrder.createSalesShipmentsFromOrderLines(
                    context,
                    await prepareSalesOrderLines(context, [{ _id: '#SMOKETEST5|10', quantityToProcess: 11 }]),
                );

                // rollbackCache function will flush deferred actions and saves
                await Test.rollbackCache(context);

                const salesShipment2 = await context.read(
                    xtremSales.nodes.SalesShipment,
                    { number: 'SHYEE200002' },
                    { forUpdate: true },
                );
                await salesShipment2.$.set({ date: date.make(2020, 12, 23) });

                await assert.isRejected(salesShipment2.$.save(), 'The record was not updated.');
                assert.deepEqual(salesShipment2.$.context.diagnoses, [
                    {
                        message: 'The document date 2020-12-23 is earlier than the previous document date 2020-12-24.',
                        path: [],
                        severity: ValidationSeverity.error,
                    },
                ]);
            },
            { today: '2020-11-24' },
        ));
    it('Call postToStock static method shipment with no stock items and stock items', () =>
        Test.withContext(
            async context => {
                const salesOrder = await context.read(
                    xtremSales.nodes.SalesOrder,
                    { _id: '#SO5' },
                    { forUpdate: true },
                );

                const salesOrderFirstLine = await salesOrder.lines.at(0);

                await salesOrder.$.set({
                    lines: [
                        {
                            _action: 'create',
                            origin: await salesOrderFirstLine!.origin,
                            status: await salesOrderFirstLine!.status,
                            invoiceStatus: await salesOrderFirstLine!.invoiceStatus,
                            item: '#Consulting01',
                            site: await salesOrderFirstLine!.site,
                            stockSite: await salesOrderFirstLine!.stockSite,
                            shipToCustomerAddress: await salesOrderFirstLine!.shipToCustomerAddress,
                            quantity: await salesOrderFirstLine!.quantity,
                            unit: '#EACH',
                        },
                    ],
                });

                await salesOrder.$.save();

                const salesShipments = await xtremSales.nodes.SalesOrder.createShipmentsFromOrder(context, salesOrder);
                if (salesShipments.length === 0) {
                    assert.fail('Sales shipment is empty.');
                }
                const salesShipmentSysId = salesShipments[1]._id;

                let salesShipment = await context.read(
                    xtremSales.nodes.SalesShipment,
                    { _id: salesShipmentSysId },
                    { forUpdate: true },
                );

                await xtremStockData.nodes.Stock.updateAllocations(context, {
                    documentLine: salesShipment.lines.elementAt(0),
                    allocationUpdates: [
                        {
                            action: 'create',
                            quantity: await (await salesShipment.lines.elementAt(0)).quantityInStockUnit,
                            stockRecord: context.read(xtremStockData.nodes.Stock, { _id: '37' }),
                        },
                    ],
                });

                await xtremSales.nodes.SalesShipment.confirm(context, salesShipment);

                // rollbackCache function will flush deferred actions and saves
                await Test.rollbackCache(context);

                // re read cause the mutation doesn't update the document loaded here
                salesShipment = await context.read(xtremSales.nodes.SalesShipment, { _id: salesShipmentSysId });

                const result = await xtremSales.nodes.SalesShipment.postToStock(context, salesShipment);

                assert.isNotEmpty(result);

                // rollbackCache function will flush deferred actions and saves
                await Test.rollbackCache(context);

                salesShipment = await context.read(xtremSales.nodes.SalesShipment, { _id: salesShipment._id });

                const nonStockItem = await salesShipment.lines.find(
                    async line => (await (await line.item).isStockManaged) === false,
                );

                assert.equal(await nonStockItem?.status, 'shipped');
            },
            { today: '2020-11-24' },
        ));
    it('Call postToStock static method shipment with only no stock items', () =>
        Test.withContext(
            async context => {
                const salesOrder = await context.read(
                    xtremSales.nodes.SalesOrder,
                    { _id: '#SO220002' },
                    { forUpdate: true },
                );

                const salesShipmentsCreated = await xtremSales.nodes.SalesOrder.createShipmentsFromOrder(
                    context,
                    salesOrder,
                );
                await context.flushDeferredActions();
                assert.isArray(salesShipmentsCreated);

                if (!salesShipmentsCreated) {
                    assert.fail('Failed to create sales shipments from the sales orders.');
                }
                const salesShipmentSysId = salesShipmentsCreated[0]._id;
                let salesShipment = await context.read(xtremSales.nodes.SalesShipment, {
                    _id: salesShipmentSysId,
                });
                await xtremSales.nodes.SalesShipment.confirm(context, salesShipment);

                // rollbackCache function will flush deferred actions and saves
                await Test.rollbackCache(context);

                // re read cause the mutation doesn't update the document loaded here
                salesShipment = await context.read(
                    xtremSales.nodes.SalesShipment,
                    { _id: salesShipmentSysId },
                    { forUpdate: true },
                );

                const result = await xtremSales.nodes.SalesShipment.postToStock(context, salesShipment);

                assert.isNotEmpty(result);

                salesShipment = await context.read(xtremSales.nodes.SalesShipment, { _id: salesShipment._id });

                const nonStockItem = await salesShipment.lines.find(
                    async line => (await (await line.item).isStockManaged) === false,
                );

                assert.equal(await nonStockItem?.status, 'shipped');

                assert.equal(await salesShipment.status, 'shipped');
            },
            { today: '2022-05-20' },
        ));

    it('Call postToStock static method shipment with not managed lines', () =>
        Test.withContext(
            async context => {
                const salesOrder = await context.read(
                    xtremSales.nodes.SalesOrder,
                    { _id: '#SO220002' },
                    { forUpdate: true },
                );

                const salesShipmentsCreated = await xtremSales.nodes.SalesOrder.createSalesShipmentsFromOrders(
                    context,
                    [salesOrder],
                );
                await context.flushDeferredActions();
                assert.equal(salesShipmentsCreated.numberOfShipments, 1);
                assert.equal(salesShipmentsCreated.documentsCreated.length, 1);

                let salesShipment = await context.read(xtremSales.nodes.SalesShipment, {
                    _id: salesShipmentsCreated.documentsCreated[0]._id,
                });
                await xtremSales.nodes.SalesShipment.confirm(context, salesShipment);

                // re read cause the mutation doesn't update the document loaded here
                salesShipment = await context.read(xtremSales.nodes.SalesShipment, { _id: salesShipment._id });
                assert.equal(await salesShipment.lines.length, 1);
                let salesShipmentLine = await salesShipment.lines.elementAt(0);
                assert.isFalse(await (await salesShipmentLine.item).isStockManaged);

                // 2.  Mock stockIssueRequestNotification to observe if it's called
                const notifySpy = sinon.spy(xtremStockData.functions.notificationLib, 'stockIssueRequestNotification');
                const result = await xtremSales.nodes.SalesShipment.postToStock(context, salesShipment);

                assert.equal(result, '{}');
                salesShipment = await context.read(xtremSales.nodes.SalesShipment, { _id: salesShipment._id });
                salesShipmentLine = await salesShipment.lines.elementAt(0);

                assert.equal(await salesShipmentLine.status, 'shipped');

                assert.equal(await salesShipment.status, 'shipped');
                sinon.assert.notCalled(notifySpy);
            },
            { today: '2022-05-20' },
        ));

    it('Call postToStock static method shipment with stock items only', () =>
        Test.withContext(
            async context => {
                const salesOrder = await context.read(
                    xtremSales.nodes.SalesOrder,
                    { _id: '#SO5' },
                    { forUpdate: true },
                );

                await salesOrder.$.save();

                const salesShipmentsCreated = await xtremSales.nodes.SalesOrder.createShipmentsFromOrder(
                    context,
                    salesOrder,
                );
                await context.flushDeferredActions();
                assert.isArray(salesShipmentsCreated);

                if (!salesShipmentsCreated) {
                    assert.fail('Failed to create sales shipments from the sales orders.');
                }

                let salesShipment = await context.read(xtremSales.nodes.SalesShipment, {
                    _id: salesShipmentsCreated[0]._id,
                });

                await xtremStockData.nodes.Stock.updateAllocations(context, {
                    documentLine: salesShipment.lines.elementAt(0),
                    allocationUpdates: [
                        {
                            action: 'create',
                            quantity: await (await salesShipment.lines.elementAt(0)).quantityInStockUnit,
                            stockRecord: context.read(xtremStockData.nodes.Stock, { _id: '37' }),
                        },
                    ],
                });

                await xtremSales.nodes.SalesShipment.confirm(context, salesShipment);

                // re read cause the mutation doesn't update the document loaded here
                salesShipment = await context.read(
                    xtremSales.nodes.SalesShipment,
                    { _id: salesShipment._id },
                    { forUpdate: true },
                );

                const result = await xtremSales.nodes.SalesShipment.postToStock(context, salesShipment);

                assert.isNotEmpty(result);
            },
            { today: '2020-11-24' },
        ));

    it('Call postToStock static method shipment with several shipments', () =>
        Test.withContext(
            async context => {
                const salesOrder1 = await context.read(xtremSales.nodes.SalesOrder, { _id: '#SO5' });
                const salesOrder2 = await context.read(xtremSales.nodes.SalesOrder, { _id: '#SO6' });

                const document1 = await xtremSales.nodes.SalesOrder.createShipmentsFromOrder(context, salesOrder1);
                assert.deepEqual(document1?.length, 1);

                const document2 = await xtremSales.nodes.SalesOrder.createShipmentsFromOrder(context, salesOrder2);
                assert.deepEqual(document2?.length, 1);

                await context.flushDeferredActions();

                assert.isNotNull(document1);
                assert.equal(await document1?.[0]?.number, 'SH210001');

                assert.isNotNull(document2);
                assert.equal(await document2[0]?.number, 'SH210002');

                const salesShipments = [document1[0], document2[0]];

                await asyncArray(salesShipments).forEach(async document => {
                    const salesShipment = await context.read(
                        xtremSales.nodes.SalesShipment,
                        {
                            _id: document._id,
                        },
                        { forUpdate: true },
                    );

                    await salesShipment.lines.forEach(async line => {
                        await xtremStockData.nodes.Stock.updateAllocations(context, {
                            documentLine: Promise.resolve(line),
                            allocationUpdates: [
                                {
                                    action: 'create',
                                    quantity: await line.quantityInStockUnit,
                                    stockRecord: context.read(xtremStockData.nodes.Stock, { _id: '37' }),
                                },
                            ],
                        });
                    });

                    await xtremSales.nodes.SalesShipment.confirm(context, salesShipment);
                });

                let salesShipment1 = await context.read(xtremSales.nodes.SalesShipment, {
                    _id: document1[0]._id,
                });

                const postResultDocument1 = JSON.parse(
                    await xtremSales.nodes.SalesShipment.postToStock(context, salesShipment1),
                ) as xtremStockData.interfaces.PostToStockReturnJsonType<xtremStockData.enums.StockMovementTypeEnum>;
                assert.isNotEmpty(postResultDocument1);
                assert.deepEqual(postResultDocument1.result, 'requested');
                if (postResultDocument1.result === 'requested') {
                    // for typing purpose
                    assert.deepEqual(postResultDocument1.documents[0].lines.length, 2);
                }

                let salesShipment2 = await context.read(xtremSales.nodes.SalesShipment, {
                    _id: document2[0]._id,
                });
                const postResultDocument2 = JSON.parse(
                    await xtremSales.nodes.SalesShipment.postToStock(context, salesShipment2),
                ) as xtremStockData.interfaces.PostToStockReturnJsonType<xtremStockData.enums.StockMovementTypeEnum>;
                assert.isNotEmpty(postResultDocument2);
                assert.deepEqual(postResultDocument2.result, 'requested');
                if (postResultDocument2.result === 'requested') {
                    // for typing purpose
                    assert.deepEqual(postResultDocument2.documents[0].lines.length, 1);
                }

                await asyncArray(salesShipments).forEach(async documentId => {
                    await xtremStockData.functions.testLib.simulateStockTransaction(
                        context,
                        Number(documentId),
                        xtremSales.nodes.SalesShipment,
                        'issue',
                        xtremSales.nodes.SalesShipment.onStockReply,
                        { stockUpdateResultStatus: 'decreased' },
                    );
                });

                await Test.rollbackCache(context);

                salesShipment1 = await context.read(xtremSales.nodes.SalesShipment, { number: 'SH210001' });
                assert.equal(await salesShipment1.status, 'shipped');

                salesShipment2 = await context.read(xtremSales.nodes.SalesShipment, { number: 'SH210002' });
                assert.equal(await salesShipment2.status, 'shipped');
            },
            { today: '2021-01-01' },
        ));

    it('Call postToStock static method shipment with stock items not allocated - fails', () =>
        Test.withContext(
            async context => {
                const salesOrder = await context.read(
                    xtremSales.nodes.SalesOrder,
                    { _id: '#SO5' },
                    { forUpdate: true },
                );

                await salesOrder.$.save();

                const salesShipmentsCreated = await xtremSales.nodes.SalesOrder.createShipmentsFromOrder(
                    context,
                    salesOrder,
                );
                await context.flushDeferredActions();
                assert.isArray(salesShipmentsCreated);

                if (!salesShipmentsCreated) {
                    assert.fail('Failed to create sales shipments from the sales orders.');
                }

                let salesShipment = await context.read(xtremSales.nodes.SalesShipment, {
                    _id: salesShipmentsCreated[0]._id,
                });

                await xtremStockData.nodes.Stock.updateAllocations(context, {
                    documentLine: salesShipment.lines.elementAt(0),
                    allocationUpdates: [
                        {
                            action: 'create',
                            quantity: await (await salesShipment.lines.elementAt(0)).quantityInStockUnit,
                            stockRecord: context.read(xtremStockData.nodes.Stock, { _id: '37' }),
                        },
                    ],
                });

                await xtremSales.nodes.SalesShipment.confirm(context, salesShipment);
                const line0 = await salesShipment.lines.elementAt(0);

                const allocationsToBeDeleted: xtremStockData.interfaces.DataForUpdateAllocationActions[] =
                    await line0.stockAllocations
                        .map(allocation => {
                            return {
                                action: 'delete',
                                allocationRecord: allocation,
                            } as unknown as xtremStockData.interfaces.DataForUpdateAllocationActions;
                        })
                        .toArray();

                await xtremStockData.nodes.Stock.updateAllocations(context, {
                    documentLine:
                        line0 as unknown as Reference<xtremStockData.interfaces.DocumentLineWithStockAllocation>,
                    allocationUpdates: [
                        ...allocationsToBeDeleted,
                    ] as Array<xtremStockData.interfaces.DataForUpdateAllocationMutationActions>,
                });

                // re read cause the mutation doesn't update the document loaded here
                salesShipment = await context.read(
                    xtremSales.nodes.SalesShipment,
                    { _id: salesShipment._id },
                    { forUpdate: true },
                );

                await assert.isRejected(
                    xtremSales.nodes.SalesShipment.postToStock(context, salesShipment),
                    'All the lines must be allocated to ship this document.',
                );

                // Do the same test with isSafeToRetry = true - should return '{}' without throwing
                assert.deepEqual(await xtremSales.nodes.SalesShipment.postToStock(context, salesShipment, true), '{}');
            },
            { today: '2020-11-24' },
        ));

    it('Create sales shipment from order with allocation generating serial numbers on issue', () =>
        Test.withContext(
            async context => {
                const salesOrderAllocation = (
                    await context
                        .query(xtremStockData.nodes.StockAllocation, { filter: { documentLine: 1204 } })
                        .toArray()
                )[0];
                assert.deepEqual(Number(await salesOrderAllocation.quantityInStockUnit), 5);
                assert.deepEqual(Number(await salesOrderAllocation.quantityTransferred), 0);

                await xtremSales.nodes.SalesOrder.createSalesShipmentsFromOrderLines(
                    context,
                    await prepareSalesOrderLines(context, [{ _id: '#SO220004|10', quantityToProcess: 3 }]),
                );

                // rollbackCache function will flush deferred actions and saves
                await Test.rollbackCache(context);

                const salesOrderAllocation2 = (
                    await context
                        .query(xtremStockData.nodes.StockAllocation, { filter: { documentLine: 1204 } })
                        .toArray()
                )[0];

                assert.deepEqual(Number(await salesOrderAllocation2.quantityInStockUnit), 2);
                assert.deepEqual(Number(await salesOrderAllocation2.quantityTransferred), 0);

                const salesShipmentLine = (await context.query(xtremSales.nodes.SalesShipmentLine).toArray())[0];

                assert.equal(await (await salesShipmentLine.document).number, 'SH220001');
                assert.equal(await (await salesShipmentLine.item).id, 'SN_ISSUE');
                const salesShipmentAllocation = (
                    await context
                        .query(xtremStockData.nodes.StockAllocation, {
                            filter: { documentLine: salesShipmentLine },
                        })
                        .toArray()
                )[0];
                assert.deepEqual(Number(await salesShipmentAllocation.quantityInStockUnit), 3);
                assert.deepEqual(Number(await salesShipmentAllocation.quantityTransferred), 3);
            },
            { today: '2022-12-15' },
        ));

    it('Update sales shipment line gross profit ', () =>
        Test.withContext(async context => {
            const itemSite = await context.read(
                xtremMasterData.nodes.ItemSite,
                { item: '#STOAVC', site: '#US001' },
                { forUpdate: true },
            );
            const salesShipment = await context.read(
                xtremSales.nodes.SalesShipment,
                { _id: '#SSH14' },
                { forUpdate: true },
            );
            const salesShipmentLine = await salesShipment.lines.elementAt(1);

            await salesShipmentLine.$.set({ quantity: 5 });
            await salesShipment.$.save();
            assert.deepEqual(salesShipment.$.context.diagnoses, []);

            assert.equal(await salesShipmentLine.stockCostAmountInCompanyCurrency, Number(601.125)); // 12383.17/103 * 5
            assert.equal(Number(await salesShipment.totalGrossProfit), Number(-371));

            // Modify the stock valuation in order to check that a new value is retrieved after net price modification
            await itemSite.$.set({ stockValuationAtAverageCost: 5000 });
            await itemSite.$.save();

            await salesShipmentLine.$.set({ grossPrice: 12.5, discount: 20, charge: 10 });
            await salesShipment.$.save();
            assert.deepEqual(salesShipment.$.context.diagnoses, []);
            assert.equal(await salesShipmentLine.stockCostAmountInCompanyCurrency, Number(242.718));
        }));

    it('Check the update of the gross profit after posting', () =>
        Test.withContext(
            async context => {
                const result = [
                    {
                        itemId: 'STOAVC',
                        stockCostAmountInCompanyCurrency: 2600,
                        grossProfitAmountInCompanyCurrency: Number(194.41),
                        grossProfitAmount: Number(194.8),
                    },
                    {
                        itemId: 'STOFIFO',
                        stockCostAmountInCompanyCurrency: 72,
                        grossProfitAmountInCompanyCurrency: Number(47.76),
                        grossProfitAmount: Number(47.86),
                    },
                    {
                        itemId: 'NonStockManagedItem',
                        stockCostAmountInCompanyCurrency: 1160,
                        grossProfitAmountInCompanyCurrency: Number(37.6),
                        grossProfitAmount: Number(37.68),
                    },
                ];
                let salesShipment = await context.read(
                    xtremSales.nodes.SalesShipment,
                    { _id: '#SSH15' },
                    { forUpdate: true },
                );
                // Allocate line with item STOAVC
                await xtremStockData.nodes.Stock.updateAllocations(context, {
                    documentLine: salesShipment.lines.elementAt(0),
                    allocationUpdates: [
                        {
                            action: 'create',
                            quantity: await (await salesShipment.lines.elementAt(0)).quantityInStockUnit,
                            stockRecord: context.read(xtremStockData.nodes.Stock, { _id: '99' }),
                        },
                    ],
                });

                // Allocate line with item STOFIFO
                await xtremStockData.nodes.Stock.updateAllocations(context, {
                    documentLine: salesShipment.lines.elementAt(1),
                    allocationUpdates: [
                        {
                            action: 'create',
                            quantity: await (await salesShipment.lines.elementAt(1)).quantityInStockUnit,
                            stockRecord: context.read(xtremStockData.nodes.Stock, { _id: '100' }),
                        },
                    ],
                });

                await xtremSales.nodes.SalesShipment.confirm(context, salesShipment);

                // re read cause the mutation doesn't update the document loaded here
                salesShipment = await context.read(xtremSales.nodes.SalesShipment, { _id: salesShipment._id });

                const resultPost = await xtremSales.nodes.SalesShipment.postToStock(context, salesShipment);
                assert.isNotEmpty(resultPost);

                // csv file has been modified to have StockJournal records already created for this shipment
                // At this step, we just have to trigger the notification reply

                await xtremStockData.functions.testLib.simulateStockTransaction(
                    context,
                    salesShipment._id,
                    xtremSales.nodes.SalesShipment,
                    'issue',
                    xtremSales.nodes.SalesShipment.onStockReply,
                    { stockUpdateResultStatus: 'decreased' },
                );

                // rollbackCache function will flush deferred actions and saves
                await Test.rollbackCache(context);

                salesShipment = await context.read(xtremSales.nodes.SalesShipment, { _id: salesShipment._id });

                assert.equal(await salesShipment.status, 'shipped');

                await asyncArray(result).forEach(async (res, index) => {
                    const line = await salesShipment.lines.elementAt(index);
                    assert.deepEqual(await (await line.item).id, res.itemId);
                    assert.deepEqual(
                        Number(await line.stockCostAmountInCompanyCurrency),
                        res.stockCostAmountInCompanyCurrency,
                    );
                    assert.deepEqual(
                        Number(await line.grossProfitAmountInCompanyCurrency),
                        res.grossProfitAmountInCompanyCurrency,
                    );
                    assert.deepEqual(Number(await line.grossProfitAmount), res.grossProfitAmount);
                });
            },
            { today: '2023-06-22' },
        ));

    it('Note propagation - Create sales shipment  with 2 different orders', () =>
        Test.withContext(
            async context => {
                const salesOrder1 = await context.read(
                    xtremSales.nodes.SalesOrder,
                    { _id: '#SO36' },
                    { forUpdate: true },
                );
                await salesOrder1.$.set({ isTransferHeaderNote: true, isTransferLineNote: true });
                await salesOrder1.$.save();

                assert.isTrue(await salesOrder1.isTransferHeaderNote);
                assert.isTrue(await salesOrder1.isTransferLineNote);

                const salesOrder2 = await context.read(
                    xtremSales.nodes.SalesOrder,
                    { _id: '#SO37' },
                    { forUpdate: true },
                );
                await salesOrder2.$.set({ isTransferHeaderNote: true, isTransferLineNote: true });
                await salesOrder2.$.save();

                assert.isTrue(await salesOrder2.isTransferHeaderNote);
                assert.isTrue(await salesOrder2.isTransferLineNote);

                const salesShipmentsCreated = await xtremSales.nodes.SalesOrder.createSalesShipmentsFromOrderLines(
                    context,
                    await prepareSalesOrderLines(context, [
                        { _id: '#SO36|10', quantityToProcess: 12 },
                        { _id: '#SO37|10', quantityToProcess: 12 },
                        { _id: '#SO37|20', quantityToProcess: 12 },
                    ]),
                );
                await context.flushDeferredActions();
                assert.isArray(salesShipmentsCreated.documentsCreated);

                if (!salesShipmentsCreated.documentsCreated) {
                    assert.fail('Failed to create sales shipments from the sales order lines.');
                }

                const salesShipment = await context.read(xtremSales.nodes.SalesShipment, {
                    _id: salesShipmentsCreated.documentsCreated[0]._id,
                });
                assert.equal(await salesShipment.number, 'SH200001');

                // If Sales shipment has 2 different orders we must not copy the header notes but copy the line notes
                assert.isEmpty((await salesShipment.internalNote).toString());
                assert.isFalse(await salesShipment.isExternalNote);
                assert.isEmpty((await salesShipment.externalNote).toString());

                // Setup must also be empty
                assert.isFalse(await salesShipment.isTransferHeaderNote);
                assert.isFalse(await salesShipment.isTransferLineNote);

                const salesOrder1Line = await context.read(xtremSales.nodes.SalesOrderLine, { _id: 'SO36|10' });
                const salesOrder2Line = await context.read(xtremSales.nodes.SalesOrderLine, { _id: 'SO37|10' });

                const salesShipmentLine1 = await salesShipment.lines.elementAt(0);
                const salesShipmentLine2 = await salesShipment.lines.elementAt(1);

                assert.equal(
                    (await salesShipmentLine1.internalNote).toString(),
                    (await salesOrder1Line.internalNote).toString(),
                );
                assert.equal(await salesShipmentLine1.isExternalNote, await salesOrder1Line.isExternalNote);
                assert.equal(
                    (await salesShipmentLine1.externalNote).toString(),
                    (await salesOrder1Line.externalNote).toString(),
                );

                assert.equal(
                    (await salesShipmentLine2.internalNote).toString(),
                    (await salesOrder2Line.internalNote).toString(),
                );
                assert.equal(await salesShipmentLine2.isExternalNote, await salesOrder2Line.isExternalNote);
                assert.equal(
                    (await salesShipmentLine2.externalNote).toString(),
                    (await salesOrder2Line.externalNote).toString(),
                );
            },
            { today: '2020-11-24' },
        ));

    it('Note propagation - Create sales shipment  with 2 different orders', () =>
        Test.withContext(
            async context => {
                const salesOrder = await context.read(
                    xtremSales.nodes.SalesOrder,
                    { _id: '#SO37' },
                    { forUpdate: true },
                );
                await salesOrder.$.set({ isTransferHeaderNote: true, isTransferLineNote: true });
                await salesOrder.$.save();

                assert.isTrue(await salesOrder.isTransferHeaderNote);
                assert.isTrue(await salesOrder.isTransferLineNote);

                const line1 = await salesOrder.lines.takeOne(async line => (await line._sortValue) === 10);
                const line2 = await salesOrder.lines.takeOne(async line => (await line._sortValue) === 20);

                assert.isNotNull(line1);
                assert.isNotNull(line2);

                const orderLines = await prepareSalesOrderLines(context, [
                    { _id: `#${await line1.documentNumber}|${await line1._sortValue}`, quantityToProcess: 12 },
                    { _id: `#${await line2.documentNumber}|${await line2._sortValue}`, quantityToProcess: 12 },
                ]);

                const createReturn = await xtremSales.nodes.SalesOrder.createSalesShipmentsFromOrderLines(
                    context,
                    orderLines,
                );
                assert.equal(createReturn.numberOfShipments, 1);
                await context.flushDeferredActions();
                assert.isArray(createReturn.documentsCreated);

                if (!createReturn.documentsCreated) {
                    assert.fail('Failed to create sales shipments from the sales order lines.');
                }

                const salesShipment = await context.read(xtremSales.nodes.SalesShipment, {
                    _id: createReturn.documentsCreated[0]._id,
                });

                assert.equal(await salesShipment.number, 'SH200001');

                // If Sales shipment has several lines but only from 1 sales order we must copy the header notes but copy the line notes
                assert.isNotEmpty((await salesShipment.internalNote).toString());
                assert.isTrue(await salesShipment.isExternalNote);
                assert.isNotEmpty((await salesShipment.externalNote).toString());
                assert.equal((await salesShipment.internalNote).toString(), (await salesOrder.internalNote).toString());
                assert.equal((await salesShipment.externalNote).toString(), (await salesOrder.externalNote).toString());
                assert.equal(await salesShipment.isExternalNote, await salesOrder.isExternalNote);

                // Setup must be copied
                assert.isTrue(await salesShipment.isTransferHeaderNote);
                assert.isTrue(await salesShipment.isTransferLineNote);
                assert.equal(await salesShipment.isTransferHeaderNote, await salesOrder.isTransferHeaderNote);
                assert.equal(await salesShipment.isTransferLineNote, await salesOrder.isTransferLineNote);

                const salesOrder1Line = await context.read(xtremSales.nodes.SalesOrderLine, { _id: 'SO37|10' });
                const salesOrder2Line = await context.read(xtremSales.nodes.SalesOrderLine, { _id: 'SO37|20' });

                const salesShipmentLine1 = await salesShipment.lines.elementAt(0);
                const salesShipmentLine2 = await salesShipment.lines.elementAt(1);

                assert.equal(
                    (await salesShipmentLine1.internalNote).toString(),
                    (await salesOrder1Line.internalNote).toString(),
                );
                assert.equal(await salesShipmentLine1.isExternalNote, await salesOrder1Line.isExternalNote);
                assert.equal(
                    (await salesShipmentLine1.externalNote).toString(),
                    (await salesOrder1Line.externalNote).toString(),
                );

                assert.equal(
                    (await salesShipmentLine2.internalNote).toString(),
                    (await salesOrder2Line.internalNote).toString(),
                );
                assert.equal(await salesShipmentLine2.isExternalNote, await salesOrder2Line.isExternalNote);
                assert.equal(
                    (await salesShipmentLine2.externalNote).toString(),
                    (await salesOrder2Line.externalNote).toString(),
                );
            },
            { today: '2020-11-24' },
        ));

    it('Sales shipment finance integration check', () =>
        Test.withContext(async context => {
            let salesShipment = await context.read(
                xtremSales.nodes.SalesShipment,
                { _id: '#SSH1' },
                { forUpdate: true },
            );

            let result = await xtremSales.functions.FinanceIntegration.salesShipmentControlAndCreateMutationResult(
                context,
                salesShipment,
            );

            assert.equal(result.wasSuccessful, true);
            assert.equal(result.message, '');

            const itemSysId = (await (await salesShipment?.lines.at(0))?.item)?._id || 0;

            const item = await context.read(xtremMasterData.nodes.Item, { _id: itemSysId }, { forUpdate: true });

            await item.$.set({ postingClass: 'BAD_ITEM' });
            await item.$.save();

            salesShipment = await context.read(xtremSales.nodes.SalesShipment, { _id: '#SSH1' }, { forUpdate: true });

            result = await xtremSales.functions.FinanceIntegration.salesShipmentControlAndCreateMutationResult(
                context,
                salesShipment,
            );

            assert.equal(result.wasSuccessful, false);
            assert.equal(
                result.message,
                '* The account cannot be determined for the Chemical D item, Sales invoice journal entry type and Document movement type.\n* The account cannot be determined for the Chemical D item, Sales invoice journal entry type and Document movement type.',
            );
        }));

    it('On hold control - postToStock mutation sales shipment for customer on hold and blocking', () =>
        Test.withContext(
            async context => {
                const salesShipmentsCreated = await xtremSales.nodes.SalesOrder.createSalesShipmentsFromOrderLines(
                    context,
                    await prepareSalesOrderLines(context, [{ _id: '#SO36|10', quantityToProcess: 12 }]),
                );
                await context.flushDeferredActions();
                assert.isArray(salesShipmentsCreated.documentsCreated);

                if (!salesShipmentsCreated.documentsCreated) {
                    assert.fail('Failed to create sales shipments from the sales order lines.');
                }

                let salesShipment = await context.read(xtremSales.nodes.SalesShipment, {
                    _id: salesShipmentsCreated.documentsCreated[0]._id,
                });

                assert.equal(await salesShipment.number, 'SH230001');

                // Allocate line
                await xtremStockData.nodes.Stock.updateAllocations(context, {
                    documentLine: salesShipment.lines.elementAt(0),
                    allocationUpdates: [
                        {
                            action: 'create',
                            quantity: await (await salesShipment.lines.elementAt(0)).quantityInStockUnit,
                            stockRecord: context.read(xtremStockData.nodes.Stock, { _id: '37' }),
                        },
                    ],
                });

                const billToCustomer = await context.read(
                    xtremMasterData.nodes.Customer,
                    {
                        businessEntity: '#US019',
                    },
                    { forUpdate: true },
                );

                await billToCustomer.$.set({ isOnHold: true });
                await billToCustomer.$.save();

                // No error message on confirming the shipment
                await xtremSales.nodes.SalesShipment.confirm(context, salesShipment);

                salesShipment = await context.read(
                    xtremSales.nodes.SalesShipment,
                    {
                        _id: salesShipmentsCreated.documentsCreated[0]._id,
                    },
                    { forUpdate: true },
                );

                // Error message only on posting
                await assert.isRejected(
                    xtremSales.nodes.SalesShipment.postToStock(context, salesShipment),
                    'The Bill-to customer is on hold. The shipment cannot be posted.',
                );

                // Do the same test with isSafeToRetry = true - should return '{}' without throwing
                assert.deepEqual(await xtremSales.nodes.SalesShipment.postToStock(context, salesShipment, true), '{}');
            },
            { today: '2023-12-15' },
        ));
    it('On hold control - creating sales shipment after customer is on hold', () =>
        Test.withContext(async context => {
            // Customer gets on hold before creation
            const billToCustomer = await context.read(
                xtremMasterData.nodes.Customer,
                { businessEntity: '#US019' },
                { forUpdate: true },
            );
            await billToCustomer.$.set({ isOnHold: true });
            await billToCustomer.$.save();

            // Error message when adding a line if the customer is on hold
            await assert.isRejected(
                xtremSales.nodes.SalesOrder.createSalesShipmentsFromOrderLines(
                    context,
                    await prepareSalesOrderLines(context, [{ _id: '#SO36|10', quantityToProcess: 12 }]),
                ),
                'The record was not created.',
            );
        }));

    it('On hold control - creating sales shipment line after customer is on hold', () =>
        Test.withContext(
            async context => {
                const orderLines = await prepareSalesOrderLines(context, [{ _id: '#SO36|10', quantityToProcess: 12 }]);
                const firstOrderLine = orderLines[0]?.salesDocumentLine;
                assert.equal(await (await firstOrderLine.item).id, 'Chemical D');
                assert.equal(await (await firstOrderLine?.document)?.number, 'SO36');

                const outPutDoc = await xtremSales.nodes.SalesOrder.createSalesShipmentsFromOrderLines(
                    context,
                    orderLines,
                );

                assert.equal(outPutDoc.numberOfShipments, 1);

                await context.flushDeferredActions();
                assert.isArray(outPutDoc.documentsCreated);

                if (!outPutDoc.documentsCreated) {
                    assert.fail('Failed to create sales shipments from the sales order lines.');
                }

                const salesShipmentQuery = await context.read(xtremSales.nodes.SalesShipment, {
                    _id: outPutDoc.documentsCreated[0]._id,
                });

                assert.equal(await salesShipmentQuery.number, 'SH230001');

                const salesShipment = await context.read(
                    xtremSales.nodes.SalesShipment,
                    { number: 'SH230001' },
                    { forUpdate: true },
                );

                // Customer gets on hold after creation
                const billToCustomer = await context.read(
                    xtremMasterData.nodes.Customer,
                    {
                        businessEntity: '#US019',
                    },
                    { forUpdate: true },
                );

                await billToCustomer.$.set({ isOnHold: true });
                await billToCustomer.$.save();

                // Add new line
                await salesShipment.$.set({
                    lines: [
                        {
                            _action: 'create',
                            item: '#Chemical D',
                            origin: 'order',
                            quantity: 12,
                            quantityInStockUnit: 340.19,
                            salesOrderLines: [
                                { linkedDocument: '#SO36|10', quantity: 12, quantityInStockUnit: 340.19 },
                            ],
                            status: 'readyToProcess',
                        },
                    ],
                });

                // Error message when adding a line if the customer is on hold
                await assert.isRejected(salesShipment.$.save(), 'The record was not updated.');
                assert.deepEqual(salesShipment.$.context.diagnoses, [
                    {
                        message: 'Creation is not allowed. The Bill-to customer is on hold.',
                        path: ['lines', '-1000000011'],
                        severity: 3,
                    },
                ]);
            },
            { today: '2023-12-15' },
        ));

    it('Check onceStockCompleted is called only when the document is completed', () =>
        Test.withContext(async context => {
            const result = await xtremStockData.functions.testLib.testOnceStockCompleted(context, {
                clas: xtremSales.nodes.SalesShipment,
                movementType: 'issue',
                documents: [
                    { key: { number: 'SSH1' }, isCompleted: false },
                    { key: { number: 'SSH2' }, isCompleted: true },
                    { key: { number: 'SSH3' }, isCompleted: false },
                    { key: { number: 'SSH4' }, isCompleted: true },
                ],
                returnedProperty: 'number',
            });

            assert.deepEqual(result, ['SSH2', 'SSH4']);
        }));

    it('Checks if checkCustomerOnHoldWhenAdded is called on shipment creation when billToCustomer is on hold', () =>
        Test.withContext(async context => {
            const billToCustomer = await context.read(
                xtremMasterData.nodes.Customer,
                {
                    businessEntity: '#US019',
                },
                { forUpdate: true },
            );

            await billToCustomer.$.set({ isOnHold: true });
            await billToCustomer.$.save();

            const salesShipmentsCreated = await context.create(xtremSales.nodes.SalesShipment, {
                shipToCustomer: '#US020',
                site: '#US001',
            });

            await assert.isRejected(salesShipmentsCreated.$.save());
            assert.deepEqual(salesShipmentsCreated.$.context.diagnoses, [
                {
                    message: 'Creation is not allowed. The Bill-to customer is on hold.',
                    path: [],
                    severity: ValidationSeverity.error,
                },
                {
                    message: 'The document needs at least one line.',
                    path: [],
                    severity: 3,
                },
            ]);
        }));

    it('Checks if checkShipmentStatus is called on shipment update when shipment status is closed', () =>
        Test.withContext(async context => {
            const salesShipment = await context.read(
                xtremSales.nodes.SalesShipment,
                { _id: '#SSH4' },
                { forUpdate: true },
            );

            await salesShipment.$.set({
                deliveryMode: 'RAIL',
            });

            await assert.isRejected(salesShipment.$.save());
            assert.deepEqual(salesShipment.$.context.diagnoses, [
                {
                    message: 'The sales shipment is closed. You cannot update it.',
                    path: [],
                    severity: ValidationSeverity.error,
                },
            ]);
        }));

    it('Checks the frozen property when trying to change the site', () =>
        Test.withContext(async context => {
            const salesShipment = await context.read(
                xtremSales.nodes.SalesShipment,
                { _id: '#SSH1' },
                { forUpdate: true },
            );

            await assert.isRejected(
                salesShipment.$.set({
                    site: '#US002',
                }),
                'SalesShipment.site: cannot set value on frozen property',
            );
        }));

    it('Checks the frozen property when trying to change the stock site', () =>
        Test.withContext(async context => {
            const salesShipment = await context.read(
                xtremSales.nodes.SalesShipment,
                { _id: '#SSH1' },
                { forUpdate: true },
            );

            await assert.isRejected(
                salesShipment.$.set({
                    stockSite: '#US002',
                }),
                'SalesShipment.stockSite: cannot set value on frozen property',
            );
        }));

    it('Checks the frozen property when trying to change shipToCustomer', () =>
        Test.withContext(async context => {
            const salesShipment = await context.read(
                xtremSales.nodes.SalesShipment,
                { _id: '#SSH1' },
                { forUpdate: true },
            );

            await assert.isRejected(
                salesShipment.$.set({
                    shipToCustomer: '#US019',
                    shipToCustomerAddress: '#US019|1500',
                }),
                'SalesShipment.shipToCustomer: cannot set value on frozen property',
            );
        }));
});

describe('Test data fixes resulting from bugs fixed', () => {
    afterEach(() => {
        xtremSystem.TestHelpers.Sinon.removeMocks();
    });

    it('Sales shipment readyToProcess but document already posted to stock', () =>
        Test.withContext(async context => {
            let salesShipment = await context.read(
                xtremSales.nodes.SalesShipment,
                { number: 'SSH16' },
                { forUpdate: true },
            );

            // set incorrect data
            await salesShipment.$.set({
                status: 'readyToProcess',
                displayStatus: 'error',
                forceUpdateForFinance: true,
            });

            await salesShipment.lines.forEach(async salesShipmentLine => {
                await salesShipmentLine.$.set({ status: 'readyToProcess' });
            });

            await salesShipment.$.save();

            salesShipment = await context.read(
                xtremSales.nodes.SalesShipment,
                { number: 'SSH16' },
                { forUpdate: true },
            );

            // sales shipment stuck status
            assert.equal(await salesShipment.status, 'readyToProcess');
            assert.equal(await salesShipment.stockTransactionStatus, 'completed');

            // Fix statuses
            await xtremSales.nodes.SalesShipment.resynchronizeStatus(context, salesShipment);

            salesShipment = await context.read(xtremSales.nodes.SalesShipment, { number: 'SSH16' });

            assert.equal(await salesShipment.status, 'shipped');
            assert.equal(await salesShipment.displayStatus, 'shipped');
        }));

    it('Resynchronize sales shipment posting interrupted during stock update XT-87706', () =>
        Test.withContext(async context => {
            const shipment = await context.read(
                xtremSales.nodes.SalesShipment,
                { number: 'SSH21' },
                { forUpdate: true },
            );
            const [{ mock: onceStockCompletedSpy }] = xtremSystem.TestHelpers.Sinon.registerMocks([
                {
                    type: 'spy',
                    isMethod: true,
                    isStatic: true,
                    class: xtremSales.nodes.SalesShipment,
                    name: 'onceStockCompleted',
                },
            ]);
            // Resynchronize must raise an error if the stockTransactionStatus is not completed
            // The stockTransaction is still considered in progress because is was created less than 1h ago
            await assert.isRejected(
                xtremSales.nodes.SalesShipment.resynchronizeStatus(context, shipment),
                'The stock update is still in progress. You can resync after it has completed.',
            );
            sinon.assert.notCalled(onceStockCompletedSpy);

            // consider that the transaction was created more than 1h ago
            xtremSystem.TestHelpers.Sinon.registerMocks([
                {
                    type: 'stub',
                    reference: xtremStockData.functions.stockTransactionLib,
                    name: 'isTransactionStuckInProgress',
                    returns: Promise.resolve(true),
                },
            ]);
            const result = await xtremSales.nodes.SalesShipment.resynchronizeStatus(context, shipment);

            assert.isTrue(result);

            assert.includeDeepMembers(await testHelper.functions.getActualStatuses(shipment), [
                {
                    lineSortValue: 10,
                    stockTransactionStatus: 'completed',
                    status: 'shipped',
                },
                {
                    lineSortValue: 20,
                    stockTransactionStatus: 'completed',
                    status: 'shipped',
                },
                {
                    lineSortValue: 30,
                    // this line becomes in error because stock was not updated for this line. It should be posted to stock again
                    stockTransactionStatus: 'error',
                    status: 'readyToShip',
                },
            ]);

            assert.equal(await shipment.stockTransactionStatus, 'error');
            assert.equal(await shipment.status, 'readyToShip');
            assert.equal(await shipment.displayStatus, 'error');

            sinon.assert.notCalled(onceStockCompletedSpy);
        }));

    it('Resynchronize sales shipment posting interrupted at the end of stock update XT-87706', () =>
        Test.withContext(async context => {
            const shipment = await context.read(
                xtremSales.nodes.SalesShipment,
                // For this shipment, stock has already been updated for all lines
                // but the StockTransaction records and the lines have not been updated yet
                // The difference with SSH21 is that the finance notification must be sent during this resync
                { number: 'SSH22' },
                { forUpdate: true },
            );

            const [{ mock: onceStockCompletedSpy }] = xtremSystem.TestHelpers.Sinon.registerMocks([
                {
                    type: 'spy',
                    isMethod: true,
                    isStatic: true,
                    class: xtremSales.nodes.SalesShipment,
                    name: 'onceStockCompleted',
                },
            ]);

            // consider that the transaction was created more than 1h ago
            xtremSystem.TestHelpers.Sinon.registerMocks([
                {
                    type: 'stub',
                    reference: xtremStockData.functions.stockTransactionLib,
                    name: 'isTransactionStuckInProgress',
                    returns: Promise.resolve(true),
                },
            ]);

            const result = await xtremSales.nodes.SalesShipment.resynchronizeStatus(context, shipment);

            assert.isTrue(result);

            assert.includeDeepMembers(await testHelper.functions.getActualStatuses(shipment), [
                {
                    lineSortValue: 10,
                    stockTransactionStatus: 'completed',
                    status: 'shipped',
                },
                {
                    lineSortValue: 20,
                    stockTransactionStatus: 'completed',
                    status: 'shipped',
                },
            ]);

            assert.equal(await shipment.stockTransactionStatus, 'completed');
            assert.equal(await shipment.status, 'shipped');
            assert.equal(await shipment.displayStatus, 'shipped');

            sinon.assert.calledOnce(onceStockCompletedSpy);
        }));
});

describe('Posting details', () => {
    it('Gets posting details successfully', () =>
        Test.withContext(async context => {
            const ssDocumentNumber = 'SH240001';
            const ssDocument = await context.read(xtremSales.nodes.SalesShipment, { number: ssDocumentNumber });
            assert.equal(await ssDocument.postingDetails.length, 1);

            const journalEntry = await ssDocument.postingDetails.at(0);
            assert.equal(await journalEntry?.documentType, 'salesShipment');
            assert.equal(await journalEntry?.documentNumber, ssDocumentNumber);
            assert.equal(await journalEntry?.documentSysId, ssDocument._id);
            assert.equal(await journalEntry?.targetDocumentType, 'journalEntry');
        }));
});

describe('Pre/Post printing mutations', () => {
    it('packingSlip - beforePrintPackingSlip - checks the status of the sales shipment before printing the packing slip', () =>
        Test.withContext(async context => {
            const salesShipment = await context.read(xtremSales.nodes.SalesShipment, { _id: '#SSH1' });
            await assert.isRejected(
                xtremSales.nodes.SalesShipment.beforePrintPackingSlip(context, salesShipment),
                'The sales shipment cannot be printed: SSH1. It is in status: Ready to process.',
            );
        }));
    it('packingSlip - afterPrintPackingSlip - sets the isPrinted flag to true', () =>
        Test.withContext(async context => {
            let salesShipment = await context.read(
                xtremSales.nodes.SalesShipment,
                { _id: '#SSHUS0012021050001' },
                { forUpdate: true },
            );
            assert.isFalse(await salesShipment.isPrinted);

            const resultAfterPrint = await xtremSales.nodes.SalesShipment.afterPrintPackingSlip(context, salesShipment);
            assert.isTrue(resultAfterPrint);

            salesShipment = await context.read(xtremSales.nodes.SalesShipment, { _id: '#SSHUS0012021050001' });
            assert.isTrue(await salesShipment.isPrinted);
        }));
    it('packingSlip - beforePrintSalesShipmentPickList - checks the status of the sales shipment before printing the picking list', () =>
        Test.withContext(async context => {
            const salesShipment = await context.read(xtremSales.nodes.SalesShipment, { _id: '#SSHUS0012021050001' });
            await assert.isRejected(
                xtremSales.nodes.SalesShipment.beforePrintSalesShipmentPickList(context, salesShipment),
                'To print the pick list, the sales shipment needs to be ready to process: SSHUS0012021050001',
            );
        }));
});
