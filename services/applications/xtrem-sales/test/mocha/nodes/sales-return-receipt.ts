import type { Context } from '@sage/xtrem-core';
import { asyncArray, date, Test, TextStream } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremStockData from '@sage/xtrem-stock-data';
import * as xtremSystem from '@sage/xtrem-system';
import { assert } from 'chai';
import * as xtremSales from '../../../index';

const { withSequenceNumberContext } = xtremMasterData.functions.testLib;

describe('SalesReturnReceipt node', () => {
    async function prepareShipmentReturn(context: Context): Promise<{
        salesShipment: xtremSales.nodes.SalesShipment;
        salesReturnRequest: xtremSales.nodes.SalesReturnRequest;
    }> {
        const salesShipment: xtremSales.nodes.SalesShipment = await context.read(
            xtremSales.nodes.SalesShipment,
            { _id: '#SSH5' },
            { forUpdate: true },
        );

        const line1 = await salesShipment.lines.elementAt(0);

        await line1.$.set({ status: 'shipped' });

        await salesShipment.$.save();

        const { documentsCreated: salesReturnRequests } = await (
            await new xtremSales.classes.SalesReturnRequestCreator(context).prepareNodeCreateData([
                { salesDocumentLine: line1, quantityToProcess: 30 },
            ])
        ).createSalesOutputDocuments();

        const salesReturnRequest = await context.read(
            xtremSales.nodes.SalesReturnRequest,
            { _id: salesReturnRequests?.[0]?._id },
            { forUpdate: true },
        );

        await salesReturnRequest.$.set({ approvalStatus: 'pendingApproval' });

        await salesReturnRequest.$.save();

        await xtremSales.nodes.SalesReturnRequest.approve(context, salesReturnRequest);

        await context.flushDeferredActions();

        return {
            salesShipment: await context.read(xtremSales.nodes.SalesShipment, { _id: salesShipment._id }),
            salesReturnRequest: await context.read(xtremSales.nodes.SalesReturnRequest, {
                _id: salesReturnRequest._id,
            }),
        };
    }
    // not anymore possible to unit test this because the notification is now needed to trigger stock movement processing...
    // we would need the possibility to send notification in test mode to perform this test
    it.skip('Create test', () =>
        Test.withContext(
            async context => {
                const { salesReturnRequest } = await prepareShipmentReturn(context);
                const site = await context.read(xtremSystem.nodes.Site, { id: 'US001' });
                const item = await context.read(
                    xtremMasterData.nodes.Item,
                    { id: 'LOT_TEST1' },
                    {
                        forUpdate: true,
                    },
                );
                await item.$.set({ isSold: true });
                await item.$.save();
                const stockSearchData: xtremStockData.interfaces.StockSearchData = {
                    item,
                    location: await context.read(xtremMasterData.nodes.Location, { id: 'LOC6' }),
                    lot: await context.read(xtremStockData.nodes.Lot, { id: 'MISCRECEIPT1', item }),
                    status: await context.read(xtremStockData.nodes.StockStatus, { id: 'A' }),
                    site,
                    stockUnit: await item.stockUnit,
                    owner: `${site._id}`,
                };

                const previousStockQuantity = (
                    await (
                        await xtremStockData.functions.stockLib.getStockRecord(context, stockSearchData)
                    )?.quantityInStockUnit
                )?.valueOf();

                const receipt = await context.create(xtremSales.nodes.SalesReturnReceipt, {
                    number: 'TEST',
                    effectiveDate: date.today(),
                    site: site._id,
                    shipToCustomer: '#US019',
                    lines: [
                        {
                            item: stockSearchData.item,
                            quantityInStockUnit: 15,
                            quantity: 15,
                            toReturnRequestLines: [
                                {
                                    linkedDocument: await salesReturnRequest.lines.at(0),
                                    quantity: 15,
                                    amount: 2,
                                    quantityInStockUnit: 15,
                                },
                            ],
                            stockDetails: [
                                xtremStockData.functions.testLib.stockSearchDataToReceiptDetailCreateData(
                                    stockSearchData,
                                    { quantityInStockUnit: 15 },
                                ),
                            ],
                        },
                    ],
                });

                await receipt.$.save();

                const newStockQuantity = (
                    await (await xtremStockData.functions.stockLib.getStockRecord(context, stockSearchData))!
                        .quantityInStockUnit
                ).valueOf();

                // Unable to check the journal as we update asynchronously
                // const producedStockMovement = xtremStockData.functions.stockJournalLib.getLastStockJournal(context, {
                //     item,
                //     site,
                //     effectiveDate: receipt.effectiveDate,
                //     isUpdate: true,
                // });
                // assert.equal(producedStockMovement?.sequence, 99999);

                const receiptLine = await receipt.lines.elementAt(0);

                assert.instanceOf(receipt, xtremSales.nodes.SalesReturnReceipt);
                assert.equal(await (await receiptLine.item).id, 'LOT_TEST1');
                assert.equal(await receiptLine.quantityInStockUnit, 15);

                assert.equal(
                    (newStockQuantity ? +newStockQuantity : 0).valueOf(),
                    (
                        (previousStockQuantity ? +previousStockQuantity : 0) + (await receiptLine.quantityInStockUnit)
                    ).valueOf(),
                );
                assert.isNotEmpty(receiptLine.stockDetails);
            },
            { user: { email: '<EMAIL>' } },
        ));

    // not anymore possible to unit test this because the notification is now needed to trigger stock movement processing...
    // we would need the possibility to send notification in test mode to perform this test
    it.skip('Create test 2 multiple lines', () =>
        Test.withContext(
            async context => {
                const { salesReturnRequest } = await prepareShipmentReturn(context);
                const site = await context.read(xtremSystem.nodes.Site, { id: 'US001' });
                const item = await context.read(
                    xtremMasterData.nodes.Item,
                    { id: 'LOT_TEST1' },
                    {
                        forUpdate: true,
                    },
                );
                await item.$.set({ isSold: true });
                await item.$.save();
                const stockSearchData: xtremStockData.interfaces.StockSearchData = {
                    site,
                    item,
                    location: await context.read(xtremMasterData.nodes.Location, { id: 'LOC6' }),
                    lot: await context.read(xtremStockData.nodes.Lot, { id: 'MISCRECEIPT1', item }),
                    status: await context.read(xtremStockData.nodes.StockStatus, { id: 'A' }),
                    stockUnit: await item.stockUnit,
                    owner: `${site._id}`,
                };

                const previousStockQuantity = (
                    await (
                        await xtremStockData.functions.stockLib.getStockRecord(context, stockSearchData)
                    )?.quantityInStockUnit
                )?.valueOf();

                const receipt = await context.create(xtremSales.nodes.SalesReturnReceipt, {
                    number: 'TEST',
                    effectiveDate: date.today(),
                    site: site._id,
                    shipToCustomer: '#US019',
                    lines: [
                        {
                            item: stockSearchData.item,
                            quantityInStockUnit: 15,
                            quantity: 15,
                            toReturnRequestLines: [
                                {
                                    linkedDocument: await salesReturnRequest.lines.at(0),
                                    quantity: 15,
                                    amount: 2,
                                    quantityInStockUnit: 15,
                                },
                            ],
                            stockDetails: [
                                xtremStockData.functions.testLib.stockSearchDataToReceiptDetailCreateData(
                                    stockSearchData,
                                    {
                                        quantityInStockUnit: 15,
                                    },
                                ),
                            ],
                        },
                        {
                            item: stockSearchData.item,
                            quantityInStockUnit: 15,
                            quantity: 15,
                            toReturnRequestLines: [
                                {
                                    linkedDocument: await salesReturnRequest.lines.at(0),
                                    quantity: 15,
                                    amount: 2,
                                    quantityInStockUnit: 15,
                                },
                            ],
                            stockDetails: [
                                xtremStockData.functions.testLib.stockSearchDataToReceiptDetailCreateData(
                                    stockSearchData,
                                    {
                                        quantityInStockUnit: 15,
                                    },
                                ),
                            ],
                        },
                    ],
                });

                await receipt.$.save();

                const newStockQuantity = (
                    await (await xtremStockData.functions.stockLib.getStockRecord(context, stockSearchData))!
                        .quantityInStockUnit
                ).valueOf();

                // Unable to check the journal as we update asynchronously
                // const lastProducedStockMovement = xtremStockData.functions.stockJournalLib.getLastStockJournal(
                //     context,
                //     {
                //         item,
                //         site,
                //         effectiveDate: receipt.effectiveDate,
                //         isUpdate: true,
                //     },
                // );
                // assert.equal(lastProducedStockMovement?.sequence, 99998);

                const receiptLine1 = await receipt.lines.elementAt(0);
                const receiptLine2 = await receipt.lines.elementAt(0);

                assert.instanceOf(receipt, xtremSales.nodes.SalesReturnReceipt);
                assert.equal(await (await receiptLine1.item).id, 'LOT_TEST1');
                assert.equal(await receiptLine1.quantityInStockUnit, 15);
                assert.equal(await receiptLine1.quantityInStockUnit, 15);

                // Stock update parameter testing

                assert.equal(await (await receiptLine1.document).number, 'TEST');
                assert.deepEqual(await (await receiptLine1.document).effectiveDate, date.today());
                assert.equal(await receiptLine1.quantityInStockUnit, 15);
                assert.instanceOf(receipt, xtremSales.nodes.SalesReturnReceipt);
                assert.equal(await (await receiptLine1.item).id, 'LOT_TEST1');

                // Stock update parameter testing
                assert.equal(await (await receiptLine2.document).number, 'TEST');
                assert.deepEqual(await (await receiptLine2.document).effectiveDate, date.today());
                assert.equal(await receiptLine2.quantityInStockUnit, 15);
                assert.equal(await (await receiptLine2.item).id, 'LOT_TEST1');

                assert.equal(
                    (newStockQuantity ? +newStockQuantity : 0).valueOf(),
                    (
                        (previousStockQuantity ? +previousStockQuantity : 0) +
                        (await receiptLine1.quantityInStockUnit) +
                        (await receiptLine2.quantityInStockUnit)
                    ).valueOf(),
                );
            },
            { user: { email: '<EMAIL>' } },
        ));

    // not anymore possible to unit test this because the notification is now needed to trigger stock movement processing...
    // we would need the possibility to send notification in test mode to perform this test
    // furthermore lot creation shouldn't be allowed it the case of this type of document
    it.skip('Create test with lot generation by sequence counter', () =>
        Test.withContext(
            async context => {
                const { salesReturnRequest } = await prepareShipmentReturn(context);
                const site = await context.read(xtremSystem.nodes.Site, { id: 'US001' });
                const item = await context.read(
                    xtremMasterData.nodes.Item,
                    { id: 'LOT_TEST1' },
                    {
                        forUpdate: true,
                    },
                );
                await item.$.set({ isSold: true });
                await item.$.save();
                const stockSearchData: xtremStockData.interfaces.StockSearchData = {
                    site,
                    item,
                    location: await context.read(xtremMasterData.nodes.Location, { id: 'LOC3' }),
                    status: await context.read(xtremStockData.nodes.StockStatus, { id: 'A' }),
                    lot: null,
                    stockUnit: await item.stockUnit,
                    owner: await site.id,
                };

                const receipt = await context.create(xtremSales.nodes.SalesReturnReceipt, {
                    number: '',
                    effectiveDate: date.today(),
                    site: site._id,
                    shipToCustomer: '#US019',
                    lines: [
                        {
                            item: stockSearchData.item,
                            quantityInStockUnit: 15,
                            quantity: 15,
                            toReturnRequestLines: [
                                {
                                    linkedDocument: await salesReturnRequest.lines.at(0),
                                    quantity: 15,
                                    amount: 2,
                                    quantityInStockUnit: 15,
                                },
                            ],
                            stockDetails: [
                                xtremStockData.functions.testLib.stockSearchDataToReceiptDetailCreateData(
                                    stockSearchData,
                                    {
                                        quantityInStockUnit: 15,
                                        lotCreateData: { expirationDate: date.today(), item },
                                    },
                                ),
                            ],
                        },
                    ],
                });

                await receipt.$.save();

                assert.instanceOf(receipt, xtremSales.nodes.SalesReturnReceipt);
                // Unable to check the journal as we update asynchronously
                // const producedStockMovement = xtremStockData.functions.stockJournalLib.getLastStockJournal(context, {
                //     item,
                //     site,
                //     effectiveDate: receipt.effectiveDate,
                //     isUpdate: true,
                // });
                // assert.isNotEmpty(producedStockMovement?.lot);
            },
            { user: { email: '<EMAIL>' } },
        ));

    it('Create stock receipt return and repost it', () =>
        Test.withContext(
            async context => {
                const { salesReturnRequest } = await prepareShipmentReturn(context);
                const site = await context.read(xtremSystem.nodes.Site, { id: 'US001' });
                const item = await context.read(
                    xtremMasterData.nodes.Item,
                    { id: 'LOT_TEST1' },
                    {
                        forUpdate: true,
                    },
                );
                await item.$.set({ isSold: true });
                await item.$.save();
                const stockSearchData: xtremStockData.interfaces.StockSearchData = {
                    item,
                    location: await context.read(xtremMasterData.nodes.Location, { id: 'LOC3' }),
                    lot: await context.read(xtremStockData.nodes.Lot, { id: 'MISCRECEIPT1', item }),
                    status: await context.read(xtremStockData.nodes.StockStatus, { id: 'A' }),
                    site,
                    stockUnit: await item.stockUnit,
                    owner: `${site._id}`,
                };

                const receipt = await context.create(xtremSales.nodes.SalesReturnReceipt, {
                    number: 'TEST',
                    effectiveDate: date.today(),
                    site: site._id,
                    shipToCustomer: '#US019',
                    lines: [
                        {
                            item: stockSearchData.item,
                            quantityInStockUnit: 15,
                            quantity: 15,
                            toReturnRequestLines: [
                                {
                                    linkedDocument: await salesReturnRequest.lines.at(0),
                                    quantity: 15,
                                    amount: 2,
                                    quantityInStockUnit: 15,
                                },
                            ],
                            stockDetails: [
                                xtremStockData.functions.testLib.stockSearchDataToReceiptDetailCreateData(
                                    stockSearchData,
                                    { quantityInStockUnit: 15 },
                                ),
                            ],
                        },
                    ],
                });

                await receipt.$.save();

                const salesReturnReceipt = await context
                    .query(xtremSales.nodes.SalesReturnReceipt, {
                        last: 1,
                    })
                    .toArray();

                const salesReturnReceiptLines = await asyncArray(await salesReturnReceipt[0].lines.toArray())
                    .map(async line => {
                        return {
                            baseDocumentLineSysId: line._id,
                            storedAttributes:
                                (await line.storedAttributes) as xtremMasterData.interfaces.StoredAttributes,
                            storedDimensions: (await line.storedDimensions) || {},
                        };
                    })
                    .toArray();

                await assert.isRejected(
                    xtremSales.nodes.SalesReturnReceipt.repost(context, salesReturnReceipt[0], salesReturnReceiptLines),
                    "You can only repost a sales return receipt if the status is 'Failed.'",
                );
            },
            { user: { email: '<EMAIL>' } },
        ));

    it('Update return receipt failed - linked request line is closed', () =>
        Test.withContext(
            async context => {
                const salesReturnRequest = await context.read(
                    xtremSales.nodes.SalesReturnRequest,
                    {
                        _id: '#SRR05',
                    },
                    { forUpdate: true },
                );

                await salesReturnRequest.$.set({
                    lines: [
                        {
                            _sortValue: 10,
                            _action: 'update',
                            status: 'closed',
                            creditStatus: 'notCredited',
                        },
                    ],
                });

                await salesReturnRequest.$.save();

                const salesReturnReceipt = await context.read(
                    xtremSales.nodes.SalesReturnReceipt,
                    {
                        _id: '#SRREC05',
                    },
                    { forUpdate: true },
                );

                await salesReturnReceipt.$.set({
                    lines: [
                        {
                            _sortValue: 10,
                            _action: 'update',
                            quantity: 12,
                        },
                    ],
                });

                await assert.isRejected(salesReturnReceipt.$.save(), 'The record was not updated.');
                assert.equal(
                    salesReturnReceipt.$.context.diagnoses[0].message,
                    'The sales return receipt line cannot be updated. The sales return request line associated is closed.',
                );
            },
            { user: { email: '<EMAIL>' } },
        ));

    it('Delete return receipt failed - linked request line is credited', () =>
        Test.withContext(
            async context => {
                const salesReturnReceipt = await context.read(
                    xtremSales.nodes.SalesReturnReceipt,
                    {
                        _id: '#SRREC05',
                    },
                    { forUpdate: true },
                );

                await assert.isRejected(salesReturnReceipt.$.delete(), 'The record was not deleted.');
                assert.equal(
                    context.diagnoses[0].message,
                    'The sales return receipt line cannot be deleted. The sales return request line associated is credited.',
                );
            },
            { user: { email: '<EMAIL>' } },
        ));

    it('Delete return receipt failed - The sales return receipt line is not a draft', () =>
        Test.withContext(
            async context => {
                const salesReturnRequest = await context.read(
                    xtremSales.nodes.SalesReturnRequest,
                    {
                        _id: '#SRR05',
                    },
                    { forUpdate: true },
                );

                await salesReturnRequest.$.set({
                    lines: [
                        {
                            _sortValue: 10,
                            _action: 'update',
                            status: 'inProgress',
                            creditStatus: 'notCredited',
                        },
                    ],
                });

                await salesReturnRequest.$.save();

                const salesReturnReceipt = await context.read(
                    xtremSales.nodes.SalesReturnReceipt,
                    {
                        _id: '#SRREC05',
                    },
                    { forUpdate: true },
                );

                await salesReturnReceipt.$.set({
                    lines: [
                        {
                            _sortValue: 10,
                            _action: 'update',
                            stockTransactionStatus: 'inProgress',
                        },
                    ],
                });
                await salesReturnReceipt.$.save();

                await assert.isRejected(salesReturnReceipt.$.delete(), 'The record was not deleted.');
                assert.equal(
                    context.diagnoses[0].message,
                    'The sales return receipt line cannot be deleted. The sales return receipt line is not a draft.',
                );
            },
            { user: { email: '<EMAIL>' } },
        ));

    it('XT-10766 - Sales shipment not updated with the return receipt', () =>
        Test.withContext(
            async context => {
                let { salesShipment, salesReturnRequest } = await prepareShipmentReturn(context);

                const site = await context.read(xtremSystem.nodes.Site, { id: 'US001' });
                const item = await context.read(
                    xtremMasterData.nodes.Item,
                    { id: 'LOT_TEST1' },
                    {
                        forUpdate: true,
                    },
                );
                await item.$.set({ isSold: true });
                await item.$.save();

                const receipt = await context.create(xtremSales.nodes.SalesReturnReceipt, {
                    number: '',
                    effectiveDate: date.today(),
                    site: site._id,
                    shipToCustomer: '#US019',
                    lines: [
                        {
                            item,
                            quantityInStockUnit: 30,
                            quantity: 30,
                            toReturnRequestLines: [
                                {
                                    linkedDocument: await salesReturnRequest.lines.at(0),
                                    quantity: 30,
                                    amount: 2,
                                    quantityInStockUnit: 30,
                                },
                            ],
                            stockDetails: [
                                {
                                    site,
                                    item,
                                    location: await context.read(xtremMasterData.nodes.Location, { id: 'LOC3' }),
                                    lotCreateData: { expirationDate: date.today(), item },
                                    status: await context.read(xtremStockData.nodes.StockStatus, { id: 'A' }),
                                    stockUnit: await item.stockUnit,
                                    quantityInStockUnit: 30,
                                },
                            ],
                        },
                    ],
                });

                await receipt.$.save();

                await xtremSales.nodes.SalesReturnReceipt.postToStock(context, [receipt._id]);
                await xtremStockData.functions.testLib.simulateStockTransaction(
                    context,
                    receipt._id,
                    xtremSales.nodes.SalesReturnReceipt,
                    'receipt',
                    xtremSales.nodes.SalesReturnReceipt.onStockReply,
                );

                salesShipment = await context.read(xtremSales.nodes.SalesShipment, { _id: salesShipment._id });
                salesReturnRequest = await context.read(xtremSales.nodes.SalesReturnRequest, {
                    _id: salesReturnRequest._id,
                });

                assert.equal(await salesShipment.returnReceiptStatus, 'returned');
                assert.equal(await salesReturnRequest.receiptStatus, 'received');

                const receipt2 = await context.read(
                    xtremSales.nodes.SalesReturnReceipt,
                    { _id: receipt._id },
                    { forUpdate: true },
                );
                await assert.isRejected(receipt2.$.delete(), 'The record was not deleted.');
                assert.deepEqual(context.diagnoses, [
                    {
                        message:
                            'The sales return receipt line cannot be deleted. The sales return receipt line is already posted.',
                        severity: 3,
                        path: ['lines', '2521'],
                    },
                    {
                        message:
                            'The sales return receipt line cannot be deleted. The sales return receipt line is not a draft.',
                        severity: 3,
                        path: ['lines', '2521'],
                    },
                ]);
            },
            { user: { email: '<EMAIL>' } },
        ));

    it('Verify chronological control between two sales return receipts on creation', () =>
        withSequenceNumberContext('SalesReturnReceipt', { isChronological: true }, async context => {
            const salesShipment = await context.read(
                xtremSales.nodes.SalesShipment,
                { _id: '#SSH4' },
                { forUpdate: true },
            );
            const line1 = await salesShipment.lines.elementAt(0);
            await line1.$.set({ status: 'shipped' });
            await salesShipment.$.save();

            await (
                await new xtremSales.classes.SalesReturnRequestCreator(context).prepareNodeCreateData([
                    { salesDocumentLine: line1, quantityToProcess: 10 },
                ])
            ).createSalesOutputDocuments();

            let salesReturnRequest: xtremSales.nodes.SalesReturnRequest = (
                await context
                    .query(xtremSales.nodes.SalesReturnRequest, {
                        last: 1,
                    })
                    .toArray()
            )[0];
            salesReturnRequest = await context.read(
                xtremSales.nodes.SalesReturnRequest,
                { _id: salesReturnRequest._id },
                { forUpdate: true },
            );
            await salesReturnRequest.$.set({
                approvalStatus: 'approved',
                date: date.make(2022, 4, 27),
                lines: [
                    {
                        _sortValue: await (await salesReturnRequest.lines.elementAt(0))._sortValue,
                        isReceiptExpected: true,
                        _action: 'update',
                    },
                ],
            });
            await salesReturnRequest.$.save();

            await (
                await new xtremSales.classes.SalesReturnReceiptCreator(
                    context,
                    date.make(2022, 4, 27),
                ).prepareNodeCreateData([
                    { salesDocumentLine: await salesReturnRequest.lines.elementAt(0), quantityToProcess: 5 },
                ])
            ).createSalesOutputDocuments();

            let salesReturnReceipt: xtremSales.nodes.SalesReturnReceipt = (
                await context
                    .query(xtremSales.nodes.SalesReturnReceipt, {
                        last: 1,
                    })
                    .toArray()
            )[0];
            salesReturnReceipt = await context.read(xtremSales.nodes.SalesReturnReceipt, {
                _id: salesReturnReceipt._id,
            });
            assert.equal(await salesReturnReceipt.status, 'draft');

            await assert.isRejected(
                (
                    await new xtremSales.classes.SalesReturnReceiptCreator(
                        context,
                        date.make(2022, 4, 26),
                    ).prepareNodeCreateData([
                        { salesDocumentLine: await salesReturnRequest.lines.elementAt(0), quantityToProcess: 5 },
                    ])
                ).createSalesOutputDocuments(),
                'The record was not created',
            );
            assert.deepEqual(context.diagnoses, [
                {
                    severity: 3,
                    path: [],
                    message: 'The document date 2022-04-26 is earlier than the previous document date 2022-04-27.',
                },
            ]);
        }));

    it('Verify chronological control between two sales return receipts on change', () =>
        withSequenceNumberContext('SalesReturnReceipt', { isChronological: true }, async context => {
            // create a returnReceipt with a date before the previous one
            const wrongDateReturnReceipt = await context.create(xtremSales.nodes.SalesReturnReceipt, {
                date: date.make(2022, 4, 26),
                site: '#US001',
                shipToCustomer: '#US020',
                lines: [{ item: '#LOT_TEST1', quantity: 600 }],
            });
            await wrongDateReturnReceipt.$.save();

            const wrongDateReturnReceipt2 = await context.create(xtremSales.nodes.SalesReturnReceipt, {
                date: date.make(2022, 4, 25),
                site: '#US001',
                shipToCustomer: '#US020',
                lines: [{ item: '#LOT_TEST1', quantity: 600 }],
            });
            await assert.isRejected(wrongDateReturnReceipt2.$.save(), 'The record was not created.');
            assert.equal(
                wrongDateReturnReceipt2.$.context.diagnoses[0].message,
                'The document date 2022-04-25 is earlier than the previous document date 2022-04-26.',
            );
        }));

    it('Cannot update the ship-to-customer after creation', () =>
        Test.withContext(async context => {
            const returnReceipt = await context.read(
                xtremSales.nodes.SalesReturnReceipt,
                { _id: '#SRREC01' },
                { forUpdate: true },
            );
            await returnReceipt.$.set({ shipToCustomer: '#US020' });
            await assert.isRejected(returnReceipt.$.save(), 'The record was not updated.');
            assert.deepEqual(returnReceipt.$.context.diagnoses, [
                {
                    severity: 3,
                    path: [],
                    message: 'The ship-to-customer cannot be updated after the sales return receipt has been created.',
                },
                {
                    severity: 3,
                    path: ['shipToCustomerAddress'],
                    message: 'The record is not valid. You need to select a different record.',
                },
                {
                    severity: 3,
                    path: ['lines', '2500', 'stockUnit'],
                    message: "value must be equal to 'EACH'",
                },
            ]);
        }));

    it('Cannot update the site after creation', () =>
        Test.withContext(async context => {
            const returnReceipt = await context.read(
                xtremSales.nodes.SalesReturnReceipt,
                { _id: '#SRREC01' },
                { forUpdate: true },
            );
            await returnReceipt.$.set({ site: '#DEP1-S01' });
            await assert.isRejected(returnReceipt.$.save(), 'The record was not updated.');
            assert.deepEqual(returnReceipt.$.context.diagnoses, [
                {
                    severity: 3,
                    path: [],
                    message: 'The stock site cannot be updated after the sales return receipt has been created.',
                },
                {
                    message: 'The site needs to belong to the same legal company.',
                    path: ['stockSite'],
                    severity: 3,
                },
            ]);
        }));

    it('Cannot update the return date after creation', () =>
        Test.withContext(async context => {
            const salesShipment = await context.read(
                xtremSales.nodes.SalesShipment,
                { _id: '#SSH4' },
                { forUpdate: true },
            );
            const line1 = await salesShipment.lines.elementAt(0);
            await line1.$.set({ status: 'shipped' });
            await salesShipment.$.save();

            const { documentsCreated } = await (
                await new xtremSales.classes.SalesReturnRequestCreator(context).prepareNodeCreateData([
                    { salesDocumentLine: line1, quantityToProcess: 10 },
                ])
            ).createSalesOutputDocuments();

            const salesReturnRequest = await context.read(
                xtremSales.nodes.SalesReturnRequest,
                { _id: documentsCreated[0]._id },
                { forUpdate: true },
            );
            await salesReturnRequest.$.set({
                approvalStatus: 'approved',
                date: date.make(2022, 4, 27),
                lines: [
                    {
                        _sortValue: await (await salesReturnRequest.lines.elementAt(0))._sortValue,
                        isReceiptExpected: true,
                        _action: 'update',
                    },
                ],
            });
            await salesReturnRequest.$.save();

            const { documentsCreated: returnReceipt } = await (
                await new xtremSales.classes.SalesReturnReceiptCreator(
                    context,
                    date.make(2022, 4, 27),
                ).prepareNodeCreateData([
                    { salesDocumentLine: await salesReturnRequest.lines.elementAt(0), quantityToProcess: 5 },
                ])
            ).createSalesOutputDocuments();

            assert.isArray(returnReceipt);
            assert.equal(returnReceipt?.length, 1);

            assert.equal(await returnReceipt?.[0].status, 'draft');

            const { documentsCreated: returnReceipt1 } = await (
                await new xtremSales.classes.SalesReturnReceiptCreator(
                    context,
                    date.make(2022, 4, 27),
                ).prepareNodeCreateData([
                    { salesDocumentLine: await salesReturnRequest.lines.elementAt(0), quantityToProcess: 5 },
                ])
            ).createSalesOutputDocuments();

            assert.isArray(returnReceipt1);
            assert.equal(returnReceipt1?.length, 1);

            const salesReturnReceiptError = await context.read(xtremSales.nodes.SalesReturnReceipt, {
                _id: returnReceipt1?.[0]._id,
            });
            await salesReturnReceiptError.$.set({ date: date.make(2022, 4, 26) });
            await assert.isRejected(salesReturnReceiptError.$.save(), 'The record was not updated.');
            assert.deepEqual(salesReturnReceiptError.$.context.diagnoses, [
                {
                    severity: 3,
                    path: [],
                    message: 'The return date cannot be updated after the sales return receipt has been created.',
                },
            ]);
        }));

    it('Test getStockCost for a document', () =>
        Test.withContext(async context => {
            const returnLine = await context.read(xtremSales.nodes.SalesReturnReceiptLine, { _id: '#SRREC12|10' });
            const orderCost = await returnLine.getOrderCost();
            assert.equal(Number(orderCost), 12.23);

            const valuedCost = await returnLine.getValuedCost();
            assert.equal(Number(valuedCost), 12.23);
        }));
    it('Test getStockCost without a document', () =>
        Test.withContext(async context => {
            const item = await context.read(xtremMasterData.nodes.Item, { _id: '#Muesli' });
            const site = await context.read(xtremSystem.nodes.Site, { id: 'US001' });
            const orderCost = await xtremStockData.functions.stockValuationLib.getStockCost(context, {
                costType: 'order',
                movementType: 'issue',
                documentLineId: undefined,
                itemSite: await context.read(xtremMasterData.nodes.ItemSite, {
                    item,
                    site,
                }),
            });

            assert.equal(Number(orderCost), 14.4);
        }));
    it('Updating sales return receipt node fail', () =>
        Test.withContext(
            async context => {
                const { salesReturnRequest } = await prepareShipmentReturn(context);

                const site = await context.read(xtremSystem.nodes.Site, { id: 'US001' });
                const item = await context.read(
                    xtremMasterData.nodes.Item,
                    { id: 'LOT_TEST1' },
                    {
                        forUpdate: true,
                    },
                );
                await item.$.set({ isSold: true });
                await item.$.save();

                const receipt = await context.create(xtremSales.nodes.SalesReturnReceipt, {
                    number: '',
                    effectiveDate: date.today(),
                    site: site._id,
                    shipToCustomer: '#US019',
                    lines: [
                        {
                            item,
                            quantityInStockUnit: 30,
                            quantity: 30,
                            toReturnRequestLines: [
                                {
                                    linkedDocument: await salesReturnRequest.lines.at(0),
                                    quantity: 30,
                                    amount: 2,
                                    quantityInStockUnit: 30,
                                },
                            ],
                            stockDetails: [
                                {
                                    site,
                                    item,
                                    location: await context.read(xtremMasterData.nodes.Location, { id: 'LOC3' }),
                                    lotCreateData: { expirationDate: date.today(), item },
                                    status: await context.read(xtremStockData.nodes.StockStatus, { id: 'A' }),
                                    stockUnit: await item.stockUnit,
                                    quantityInStockUnit: 30,
                                },
                            ],
                        },
                    ],
                });

                await receipt.$.save();

                await xtremSales.nodes.SalesReturnReceipt.postToStock(context, [receipt._id]);
                await xtremStockData.functions.testLib.simulateStockTransaction(
                    context,
                    receipt._id,
                    xtremSales.nodes.SalesReturnReceipt,
                    'receipt',
                    xtremSales.nodes.SalesReturnReceipt.onStockReply,
                );

                const result = await xtremSales.nodes.SalesReturnReceipt.validateQuantityInSalesUnit(
                    context,
                    10,
                    await receipt.lines.at(0),
                );

                assert.equal(result.hasErrors, true);
                assert.equal(
                    result.errorMessage,
                    'The received quantity cannot be lower than the stock quantity already identified.',
                );
            },
            { user: { email: '<EMAIL>' } },
        ));

    it('Updating sales return receipt sales unit fail', () =>
        Test.withContext(
            async context => {
                const { salesReturnRequest } = await prepareShipmentReturn(context);

                const site = await context.read(xtremSystem.nodes.Site, { id: 'US001' });
                const item = await context.read(
                    xtremMasterData.nodes.Item,
                    { id: 'LOT_TEST1' },
                    {
                        forUpdate: true,
                    },
                );
                await item.$.set({ isSold: true });
                await item.$.save();

                const receipt = await context.create(xtremSales.nodes.SalesReturnReceipt, {
                    number: '',
                    effectiveDate: date.today(),
                    site: site._id,
                    shipToCustomer: '#US019',
                    lines: [
                        {
                            item,
                            quantityInStockUnit: 30,
                            quantity: 30,
                            stockTransactionStatus: 'completed',
                            toReturnRequestLines: [
                                {
                                    linkedDocument: await salesReturnRequest.lines.at(0),
                                    quantity: 30,
                                    amount: 2,
                                    quantityInStockUnit: 30,
                                },
                            ],
                            stockDetails: [
                                {
                                    site,
                                    item,
                                    location: await context.read(xtremMasterData.nodes.Location, { id: 'LOC3' }),
                                    lotCreateData: { expirationDate: date.today(), item },
                                    status: await context.read(xtremStockData.nodes.StockStatus, { id: 'A' }),
                                    stockUnit: await item.stockUnit,
                                    quantityInStockUnit: 30,
                                },
                            ],
                        },
                    ],
                });

                await receipt.$.save();

                const receipt2 = await context.read(
                    xtremSales.nodes.SalesReturnReceipt,
                    { _id: receipt._id },
                    { forUpdate: true },
                );
                const receiptLineId = (await receipt.lines.elementAt(0))._id;
                await receipt2.$.set({
                    lines: [
                        {
                            _id: receiptLineId,
                            unit: '#MILLILITER',
                            _action: 'update',
                        },
                    ],
                });
                await assert.isRejected(receipt2.$.save(), 'The record was not updated.');
            },
            { user: { email: '<EMAIL>' } },
        ));

    it('Updating sales return receipt item description fail', () =>
        Test.withContext(
            async context => {
                const { salesReturnRequest } = await prepareShipmentReturn(context);

                const site = await context.read(xtremSystem.nodes.Site, { id: 'US001' });
                const item = await context.read(
                    xtremMasterData.nodes.Item,
                    { id: 'LOT_TEST1' },
                    {
                        forUpdate: true,
                    },
                );
                await item.$.set({ isSold: true });
                await item.$.save();

                const receipt = await context.create(xtremSales.nodes.SalesReturnReceipt, {
                    number: '',
                    effectiveDate: date.today(),
                    site: site._id,
                    shipToCustomer: '#US019',
                    lines: [
                        {
                            item,
                            quantityInStockUnit: 30,
                            quantity: 30,
                            stockTransactionStatus: 'completed',
                            toReturnRequestLines: [
                                {
                                    linkedDocument: await salesReturnRequest.lines.at(0),
                                    quantity: 30,
                                    amount: 2,
                                    quantityInStockUnit: 30,
                                },
                            ],
                            stockDetails: [
                                {
                                    site,
                                    item,
                                    location: await context.read(xtremMasterData.nodes.Location, { id: 'LOC3' }),
                                    lotCreateData: { expirationDate: date.today(), item },
                                    status: await context.read(xtremStockData.nodes.StockStatus, { id: 'A' }),
                                    stockUnit: await item.stockUnit,
                                    quantityInStockUnit: 30,
                                },
                            ],
                        },
                    ],
                });

                await receipt.$.save();

                const receipt2 = await context.read(
                    xtremSales.nodes.SalesReturnReceipt,
                    { _id: receipt._id },
                    { forUpdate: true },
                );
                const receiptLineId = (await receipt.lines.elementAt(0))._id;
                await receipt2.$.set({
                    lines: [
                        {
                            _id: receiptLineId,
                            itemDescription: 'test',
                            _action: 'update',
                        },
                    ],
                });
                await assert.isRejected(receipt2.$.save(), 'The record was not updated.');
            },
            { user: { email: '<EMAIL>' } },
        ));

    it('Updating sales return receipt item fail', () =>
        Test.withContext(
            async context => {
                const { salesReturnRequest } = await prepareShipmentReturn(context);

                const site = await context.read(xtremSystem.nodes.Site, { id: 'US001' });
                const item = await context.read(
                    xtremMasterData.nodes.Item,
                    { id: 'LOT_TEST1' },
                    {
                        forUpdate: true,
                    },
                );
                await item.$.set({ isSold: true });
                await item.$.save();

                const receipt = await context.create(xtremSales.nodes.SalesReturnReceipt, {
                    number: '',
                    effectiveDate: date.today(),
                    site: site._id,
                    shipToCustomer: '#US019',
                    lines: [
                        {
                            item,
                            quantityInStockUnit: 30,
                            quantity: 30,
                            stockTransactionStatus: 'completed',
                            toReturnRequestLines: [
                                {
                                    linkedDocument: await salesReturnRequest.lines.at(0),
                                    quantity: 30,
                                    amount: 2,
                                    quantityInStockUnit: 30,
                                },
                            ],
                            stockDetails: [
                                {
                                    site,
                                    item,
                                    location: await context.read(xtremMasterData.nodes.Location, { id: 'LOC3' }),
                                    lotCreateData: { expirationDate: date.today(), item },
                                    status: await context.read(xtremStockData.nodes.StockStatus, { id: 'A' }),
                                    stockUnit: await item.stockUnit,
                                    quantityInStockUnit: 30,
                                },
                            ],
                        },
                    ],
                });

                await receipt.$.save();

                const receipt2 = await context.read(
                    xtremSales.nodes.SalesReturnReceipt,
                    { _id: receipt._id },
                    { forUpdate: true },
                );
                const receiptLineId = (await receipt.lines.elementAt(0))._id;
                await receipt2.$.set({
                    lines: [
                        {
                            _id: receiptLineId,
                            item: '#Muesli',
                            _action: 'update',
                        },
                    ],
                });
                await assert.isRejected(
                    receipt2.$.save(),
                    'SalesReturnReceiptLine.unitToStockUnitConversionFactor: cannot set value on frozen property',
                );
            },
            { user: { email: '<EMAIL>' } },
        ));

    it('Updating sales return receipt quantity in sales unit fail', () =>
        Test.withContext(
            async context => {
                const { salesReturnRequest } = await prepareShipmentReturn(context);

                const site = await context.read(xtremSystem.nodes.Site, { id: 'US001' });
                const item = await context.read(
                    xtremMasterData.nodes.Item,
                    { id: 'LOT_TEST1' },
                    {
                        forUpdate: true,
                    },
                );
                await item.$.set({ isSold: true });
                await item.$.save();

                const receipt = await context.create(xtremSales.nodes.SalesReturnReceipt, {
                    number: '',
                    effectiveDate: date.today(),
                    site: site._id,
                    shipToCustomer: '#US019',
                    lines: [
                        {
                            item,
                            quantity: 15,
                            stockTransactionStatus: 'completed',
                            toReturnRequestLines: [
                                {
                                    linkedDocument: await salesReturnRequest.lines.at(0),
                                    quantity: 30,
                                    amount: 2,
                                    quantityInStockUnit: 30,
                                },
                            ],
                            stockDetails: [
                                {
                                    site,
                                    item,
                                    location: await context.read(xtremMasterData.nodes.Location, { id: 'LOC3' }),
                                    lotCreateData: { expirationDate: date.today(), item },
                                    status: await context.read(xtremStockData.nodes.StockStatus, { id: 'A' }),
                                    stockUnit: await item.stockUnit,
                                    quantityInStockUnit: 30,
                                },
                            ],
                        },
                    ],
                });

                await receipt.$.save();

                const receipt2 = await context.read(
                    xtremSales.nodes.SalesReturnReceipt,
                    { _id: receipt._id },
                    { forUpdate: true },
                );
                const receiptLineId = (await receipt.lines.elementAt(0))._id;
                await receipt2.$.set({
                    lines: [
                        {
                            _id: receiptLineId,
                            quantity: 30,
                            _action: 'update',
                        },
                    ],
                });
                await assert.isRejected(receipt2.$.save(), 'The record was not updated.');
            },
            { user: { email: '<EMAIL>' } },
        ));

    it('Updating sales return receipt quantity in stock unit fail', () =>
        Test.withContext(
            async context => {
                const { salesReturnRequest } = await prepareShipmentReturn(context);

                const site = await context.read(xtremSystem.nodes.Site, { id: 'US001' });
                const item = await context.read(
                    xtremMasterData.nodes.Item,
                    { id: 'LOT_TEST1' },
                    {
                        forUpdate: true,
                    },
                );
                await item.$.set({ isSold: true });
                await item.$.save();

                const receipt = await context.create(xtremSales.nodes.SalesReturnReceipt, {
                    number: '',
                    effectiveDate: date.today(),
                    site: site._id,
                    shipToCustomer: '#US019',
                    lines: [
                        {
                            item,
                            quantityInStockUnit: 30,
                            quantity: 30,
                            stockTransactionStatus: 'completed',
                            toReturnRequestLines: [
                                {
                                    linkedDocument: await salesReturnRequest.lines.at(0),
                                    quantity: 30,
                                    amount: 2,
                                    quantityInStockUnit: 30,
                                },
                            ],
                            stockDetails: [
                                {
                                    site,
                                    item,
                                    location: await context.read(xtremMasterData.nodes.Location, { id: 'LOC3' }),
                                    lotCreateData: { expirationDate: date.today(), item },
                                    status: await context.read(xtremStockData.nodes.StockStatus, { id: 'A' }),
                                    stockUnit: await item.stockUnit,
                                    quantityInStockUnit: 30,
                                },
                            ],
                        },
                    ],
                });

                await receipt.$.save();

                const receipt2 = await context.read(
                    xtremSales.nodes.SalesReturnReceipt,
                    { _id: receipt._id },
                    { forUpdate: true },
                );
                const receiptLineId = (await receipt.lines.elementAt(0))._id;
                await receipt2.$.set({
                    lines: [
                        {
                            _id: receiptLineId,
                            quantityInStockUnit: 50,
                            _action: 'update',
                        },
                    ],
                });
                await assert.isRejected(receipt2.$.save(), 'The record was not updated.');
            },
            { user: { email: '<EMAIL>' } },
        ));

    it('Updating sales return receipt valued cost fail', () =>
        Test.withContext(
            async context => {
                const { salesReturnRequest } = await prepareShipmentReturn(context);

                const site = await context.read(xtremSystem.nodes.Site, { id: 'US001' });
                const item = await context.read(
                    xtremMasterData.nodes.Item,
                    { id: 'LOT_TEST1' },
                    {
                        forUpdate: true,
                    },
                );
                await item.$.set({ isSold: true });
                await item.$.save();

                const receipt = await context.create(xtremSales.nodes.SalesReturnReceipt, {
                    number: '',
                    effectiveDate: date.today(),
                    site: site._id,
                    shipToCustomer: '#US019',
                    lines: [
                        {
                            item,
                            quantityInStockUnit: 30,
                            quantity: 30,
                            stockTransactionStatus: 'completed',
                            toReturnRequestLines: [
                                {
                                    linkedDocument: await salesReturnRequest.lines.at(0),
                                    quantity: 30,
                                    amount: 2,
                                    quantityInStockUnit: 30,
                                },
                            ],
                            stockDetails: [
                                {
                                    site,
                                    item,
                                    location: await context.read(xtremMasterData.nodes.Location, { id: 'LOC3' }),
                                    lotCreateData: { expirationDate: date.today(), item },
                                    status: await context.read(xtremStockData.nodes.StockStatus, { id: 'A' }),
                                    stockUnit: await item.stockUnit,
                                    quantityInStockUnit: 30,
                                },
                            ],
                        },
                    ],
                });

                await receipt.$.save();

                const receipt2 = await context.read(
                    xtremSales.nodes.SalesReturnReceipt,
                    { _id: receipt._id },
                    { forUpdate: true },
                );
                const receiptLineId = (await receipt.lines.elementAt(0))._id;
                await receipt2.$.set({
                    lines: [
                        {
                            _id: receiptLineId,
                            valuedCost: 20,
                            _action: 'update',
                        },
                    ],
                });
                await assert.isRejected(receipt2.$.save(), 'The record was not updated.');
            },
            { user: { email: '<EMAIL>' } },
        ));

    it('Updating sales return receipt order cost fail', () =>
        Test.withContext(
            async context => {
                const { salesReturnRequest } = await prepareShipmentReturn(context);

                const site = await context.read(xtremSystem.nodes.Site, { id: 'US001' });
                const item = await context.read(
                    xtremMasterData.nodes.Item,
                    { id: 'LOT_TEST1' },
                    {
                        forUpdate: true,
                    },
                );
                await item.$.set({ isSold: true });
                await item.$.save();

                const receipt = await context.create(xtremSales.nodes.SalesReturnReceipt, {
                    number: '',
                    effectiveDate: date.today(),
                    site: site._id,
                    shipToCustomer: '#US019',
                    lines: [
                        {
                            item,
                            quantityInStockUnit: 30,
                            quantity: 30,
                            stockTransactionStatus: 'completed',
                            toReturnRequestLines: [
                                {
                                    linkedDocument: await salesReturnRequest.lines.at(0),
                                    quantity: 30,
                                    amount: 2,
                                    quantityInStockUnit: 30,
                                },
                            ],
                            stockDetails: [
                                {
                                    site,
                                    item,
                                    location: await context.read(xtremMasterData.nodes.Location, { id: 'LOC3' }),
                                    lotCreateData: { expirationDate: date.today(), item },
                                    status: await context.read(xtremStockData.nodes.StockStatus, { id: 'A' }),
                                    stockUnit: await item.stockUnit,
                                    quantityInStockUnit: 30,
                                },
                            ],
                        },
                    ],
                });

                await receipt.$.save();

                const receipt2 = await context.read(
                    xtremSales.nodes.SalesReturnReceipt,
                    { _id: receipt._id },
                    { forUpdate: true },
                );
                const receiptLineId = (await receipt.lines.elementAt(0))._id;
                await receipt2.$.set({
                    lines: [
                        {
                            _id: receiptLineId,
                            orderCost: 20,
                            _action: 'update',
                        },
                    ],
                });
                await assert.isRejected(receipt2.$.save(), 'The record was not updated.');
            },
            { user: { email: '<EMAIL>' } },
        ));

    it('Updating sales return receipt stock unit fail', () =>
        Test.withContext(
            async context => {
                const { salesReturnRequest } = await prepareShipmentReturn(context);

                const site = await context.read(xtremSystem.nodes.Site, { id: 'US001' });
                const item = await context.read(
                    xtremMasterData.nodes.Item,
                    { id: 'LOT_TEST1' },
                    {
                        forUpdate: true,
                    },
                );
                await item.$.set({ isSold: true });
                await item.$.save();

                const receipt = await context.create(xtremSales.nodes.SalesReturnReceipt, {
                    number: '',
                    effectiveDate: date.today(),
                    site: site._id,
                    shipToCustomer: '#US019',
                    lines: [
                        {
                            item,
                            quantityInStockUnit: 30,
                            quantity: 30,
                            stockTransactionStatus: 'inProgress',
                            toReturnRequestLines: [
                                {
                                    linkedDocument: await salesReturnRequest.lines.at(0),
                                    quantity: 30,
                                    amount: 2,
                                    quantityInStockUnit: 30,
                                },
                            ],
                            stockDetails: [
                                {
                                    site,
                                    item,
                                    location: await context.read(xtremMasterData.nodes.Location, { id: 'LOC3' }),
                                    lotCreateData: { expirationDate: date.today(), item },
                                    status: await context.read(xtremStockData.nodes.StockStatus, { id: 'A' }),
                                    stockUnit: await item.stockUnit,
                                    quantityInStockUnit: 30,
                                },
                            ],
                        },
                    ],
                });

                await receipt.$.save();

                const receipt2 = await context.read(
                    xtremSales.nodes.SalesReturnReceipt,
                    { _id: receipt._id },
                    { forUpdate: true },
                );
                const receiptLineId = (await receipt.lines.elementAt(0))._id;
                await receipt2.$.set({
                    lines: [
                        {
                            _id: receiptLineId,
                            stockUnit: '#MILLILITER',
                            _action: 'update',
                        },
                    ],
                });
                await assert.isRejected(receipt2.$.save(), 'The record was not updated.');
            },
            { user: { email: '<EMAIL>' } },
        ));

    it('Updating sales return origin document type fail', () =>
        Test.withContext(
            async context => {
                const { salesReturnRequest } = await prepareShipmentReturn(context);

                const site = await context.read(xtremSystem.nodes.Site, { id: 'US001' });
                const itemLotTest = await context.read(
                    xtremMasterData.nodes.Item,
                    { id: 'LOT_TEST1' },
                    {
                        forUpdate: true,
                    },
                );
                await itemLotTest.$.set({ isSold: true });
                await itemLotTest.$.save();

                const receipt = await context.create(xtremSales.nodes.SalesReturnReceipt, {
                    number: '',
                    effectiveDate: date.today(),
                    site: '#US001',
                    shipToCustomer: '#US019',
                    lines: [
                        {
                            item: itemLotTest,
                            quantityInStockUnit: 30,
                            quantity: 30,
                            stockTransactionStatus: 'completed',
                            toReturnRequestLines: [
                                {
                                    linkedDocument: await salesReturnRequest.lines.at(0),
                                    quantity: 30,
                                    amount: 2,
                                    quantityInStockUnit: 30,
                                },
                            ],
                            stockDetails: [
                                {
                                    site,
                                    item: itemLotTest,
                                    location: await context.read(xtremMasterData.nodes.Location, { id: 'LOC3' }),
                                    lotCreateData: { expirationDate: date.today(), item: itemLotTest },
                                    status: await context.read(xtremStockData.nodes.StockStatus, { id: 'A' }),
                                    stockUnit: await itemLotTest.stockUnit,
                                    quantityInStockUnit: 30,
                                },
                            ],
                        },
                    ],
                });

                await receipt.$.save();

                const receipt2 = await context.read(
                    xtremSales.nodes.SalesReturnReceipt,
                    { _id: receipt._id },
                    { forUpdate: true },
                );
                const receiptLineId = (await receipt.lines.elementAt(0))._id;
                await receipt2.$.set({
                    lines: [
                        {
                            _id: receiptLineId,
                            origin: 'shipment',
                            _action: 'update',
                        },
                    ],
                });
                await assert.isRejected(receipt2.$.save(), 'The record was not updated.');
            },
            { user: { email: '<EMAIL>' } },
        ));

    it('Note propagation - Return Request to Return Receipt - isTransferHeaderNote = true && isTransferLineNote = true', () =>
        Test.withContext(async context => {
            const salesShipment: xtremSales.nodes.SalesShipment = await context.read(
                xtremSales.nodes.SalesShipment,
                { _id: '#SSHUS0022021030003' },
                { forUpdate: true },
            );
            await salesShipment.$.set({ isTransferHeaderNote: true, isTransferLineNote: true });
            const line1 = await salesShipment.lines.elementAt(0);
            await line1.$.set({ status: 'shipped' });
            await salesShipment.$.save();

            const salesReturnCreated = await (
                await new xtremSales.classes.SalesReturnRequestCreator(context).prepareNodeCreateData([
                    { salesDocumentLine: line1, quantityToProcess: 20 },
                ])
            ).createSalesOutputDocuments();
            if (salesReturnCreated.documentsCreated) {
                const salesReturnRequest: xtremSales.nodes.SalesReturnRequest = await context.read(
                    xtremSales.nodes.SalesReturnRequest,
                    { _id: salesReturnCreated.documentsCreated[0]._id },
                );
                const site = await context.read(xtremSystem.nodes.Site, { id: 'US002' });
                const item = await context.read(
                    xtremMasterData.nodes.Item,
                    { id: 'LOT_TEST1' },
                    {
                        forUpdate: true,
                    },
                );
                await item.$.set({ isSold: true });
                await item.$.save();
                const stockSearchData: xtremStockData.interfaces.StockSearchData = {
                    item,
                    location: await context.read(xtremMasterData.nodes.Location, { id: 'LOC3' }),
                    lot: await context.read(xtremStockData.nodes.Lot, { id: 'MISCRECEIPT1', item }),
                    status: await context.read(xtremStockData.nodes.StockStatus, { id: 'A' }),
                    site,
                    stockUnit: await item.stockUnit,
                    owner: `${site._id}`,
                };

                const salesReturnReceipt = await context.create(xtremSales.nodes.SalesReturnReceipt, {
                    number: 'TEST',
                    effectiveDate: date.today(),
                    site: site._id,
                    shipToCustomer: '#US019',
                    lines: [
                        {
                            item: stockSearchData.item,
                            quantityInStockUnit: 20,
                            quantity: 20,
                            toReturnRequestLines: [
                                {
                                    linkedDocument: await salesReturnRequest.lines.at(0),
                                    quantity: 20,
                                    amount: 2,
                                    quantityInStockUnit: 20,
                                },
                            ],
                        },
                    ],
                });
                await salesReturnReceipt.$.save();

                const salesReturnReceiptInternalNote = (await salesReturnReceipt.internalNote).toString();
                assert.isNotEmpty(salesReturnReceiptInternalNote);

                // Notes on return request must be equal on return receipt
                assert.equal(salesReturnReceiptInternalNote, (await salesReturnRequest.internalNote).toString());
                // Lines
                const lineInternalNote = (await (await salesReturnReceipt.lines.elementAt(0)).internalNote).toString();
                assert.isNotEmpty(lineInternalNote);
                assert.equal(
                    lineInternalNote,
                    (await (await salesReturnRequest.lines.elementAt(0)).internalNote).toString(),
                );
            }
        }));
    it('Note propagation - Return Request to Return Receipt - isTransferHeaderNote = false && isTransferLineNote = false', () =>
        Test.withContext(async context => {
            const salesShipment: xtremSales.nodes.SalesShipment = await context.read(
                xtremSales.nodes.SalesShipment,
                { _id: '#SSHUS0022021030003' },
                { forUpdate: true },
            );
            await salesShipment.$.set({ isTransferHeaderNote: false, isTransferLineNote: false });
            const line1 = await salesShipment.lines.elementAt(0);
            await line1.$.set({ status: 'shipped' });
            await salesShipment.$.save();

            const salesReturnCreated = await (
                await new xtremSales.classes.SalesReturnRequestCreator(context).prepareNodeCreateData([
                    { salesDocumentLine: line1, quantityToProcess: 20 },
                ])
            ).createSalesOutputDocuments();
            if (salesReturnCreated.documentsCreated) {
                const salesReturnRequest: xtremSales.nodes.SalesReturnRequest = await context.read(
                    xtremSales.nodes.SalesReturnRequest,
                    { _id: salesReturnCreated.documentsCreated[0]._id },
                );
                const site = await context.read(xtremSystem.nodes.Site, { id: 'US002' });
                const item = await context.read(
                    xtremMasterData.nodes.Item,
                    { id: 'LOT_TEST1' },
                    {
                        forUpdate: true,
                    },
                );
                await item.$.set({ isSold: true });
                await item.$.save();
                const stockSearchData: xtremStockData.interfaces.StockSearchData = {
                    item,
                    location: await context.read(xtremMasterData.nodes.Location, { id: 'LOC3' }),
                    lot: await context.read(xtremStockData.nodes.Lot, { id: 'MISCRECEIPT1', item }),
                    status: await context.read(xtremStockData.nodes.StockStatus, { id: 'A' }),
                    site,
                    stockUnit: await item.stockUnit,
                    owner: `${site._id}`,
                };

                const salesReturnReceipt = await context.create(xtremSales.nodes.SalesReturnReceipt, {
                    number: 'TEST',
                    effectiveDate: date.today(),
                    site: site._id,
                    shipToCustomer: '#US019',
                    lines: [
                        {
                            item: stockSearchData.item,
                            quantityInStockUnit: 20,
                            quantity: 20,
                            toReturnRequestLines: [
                                {
                                    linkedDocument: await salesReturnRequest.lines.at(0),
                                    quantity: 20,
                                    amount: 2,
                                    quantityInStockUnit: 20,
                                },
                            ],
                        },
                    ],
                });
                await salesReturnReceipt.$.save();
                // Notes must be empty in this case
                const salesReturnReceiptInternalNote = (await salesReturnReceipt.internalNote).toString();
                assert.isEmpty(salesReturnReceiptInternalNote);
                const lineInternalNote = (await (await salesReturnRequest.lines.elementAt(0)).internalNote).toString();
                assert.isEmpty(lineInternalNote);
            }
        }));
    it('Note propagation - Return Request to Return Receipt - isTransferHeaderNote = true && isTransferLineNote = false', () =>
        Test.withContext(async context => {
            const salesShipment: xtremSales.nodes.SalesShipment = await context.read(
                xtremSales.nodes.SalesShipment,
                { _id: '#SSHUS0022021030003' },
                { forUpdate: true },
            );
            await salesShipment.$.set({ isTransferHeaderNote: true });
            const line1 = await salesShipment.lines.elementAt(0);
            await line1.$.set({ status: 'shipped' });
            await salesShipment.$.save();

            const salesReturnCreated = await (
                await new xtremSales.classes.SalesReturnRequestCreator(context).prepareNodeCreateData([
                    { salesDocumentLine: line1, quantityToProcess: 20 },
                ])
            ).createSalesOutputDocuments();
            if (salesReturnCreated.documentsCreated) {
                const salesReturnRequest: xtremSales.nodes.SalesReturnRequest = await context.read(
                    xtremSales.nodes.SalesReturnRequest,
                    { _id: salesReturnCreated.documentsCreated[0]._id },
                );
                const site = await context.read(xtremSystem.nodes.Site, { id: 'US002' });
                const item = await context.read(
                    xtremMasterData.nodes.Item,
                    { id: 'LOT_TEST1' },
                    {
                        forUpdate: true,
                    },
                );
                await item.$.set({ isSold: true });
                await item.$.save();
                const stockSearchData: xtremStockData.interfaces.StockSearchData = {
                    item,
                    location: await context.read(xtremMasterData.nodes.Location, { id: 'LOC3' }),
                    lot: await context.read(xtremStockData.nodes.Lot, { id: 'MISCRECEIPT1', item }),
                    status: await context.read(xtremStockData.nodes.StockStatus, { id: 'A' }),
                    site,
                    stockUnit: await item.stockUnit,
                    owner: `${site._id}`,
                };

                const salesReturnReceipt = await context.create(xtremSales.nodes.SalesReturnReceipt, {
                    number: 'TEST',
                    effectiveDate: date.today(),
                    site: site._id,
                    shipToCustomer: '#US019',
                    lines: [
                        {
                            item: stockSearchData.item,
                            quantityInStockUnit: 20,
                            quantity: 20,
                            toReturnRequestLines: [
                                {
                                    linkedDocument: await salesReturnRequest.lines.at(0),
                                    quantity: 20,
                                    amount: 2,
                                    quantityInStockUnit: 20,
                                },
                            ],
                        },
                    ],
                });
                await salesReturnReceipt.$.save();

                // Notes on return request must be equal on return receipt header
                const salesReturnReceiptInternalNote = (await salesReturnReceipt.internalNote).toString();
                assert.isNotEmpty(salesReturnReceiptInternalNote);

                assert.equal(salesReturnReceiptInternalNote, (await salesReturnRequest.internalNote).toString());
                // Lines - notes shouldn't be equal here
                const lineInternalNote = (await (await salesReturnReceipt.lines.elementAt(0)).internalNote).toString();
                assert.isEmpty(lineInternalNote);
            }
        }));
    it('Note propagation - Return Request to Return Receipt - isTransferHeaderNote = false && isTransferLineNote = true', () =>
        Test.withContext(async context => {
            const salesShipment: xtremSales.nodes.SalesShipment = await context.read(
                xtremSales.nodes.SalesShipment,
                { _id: '#SSHUS0022021030003' },
                { forUpdate: true },
            );
            await salesShipment.$.set({ isTransferLineNote: true });
            const line1 = await salesShipment.lines.elementAt(0);
            await line1.$.set({ status: 'shipped' });
            await salesShipment.$.save();

            const salesReturnCreated = await (
                await new xtremSales.classes.SalesReturnRequestCreator(context).prepareNodeCreateData([
                    { salesDocumentLine: line1, quantityToProcess: 20 },
                ])
            ).createSalesOutputDocuments();
            if (salesReturnCreated.documentsCreated) {
                const salesReturnRequest: xtremSales.nodes.SalesReturnRequest = await context.read(
                    xtremSales.nodes.SalesReturnRequest,
                    { _id: salesReturnCreated.documentsCreated[0]._id },
                );

                const site = await context.read(xtremSystem.nodes.Site, { id: 'US002' });
                const item = await context.read(
                    xtremMasterData.nodes.Item,
                    { id: 'LOT_TEST1' },
                    {
                        forUpdate: true,
                    },
                );
                await item.$.set({ isSold: true });
                await item.$.save();
                const stockSearchData: xtremStockData.interfaces.StockSearchData = {
                    item,
                    location: await context.read(xtremMasterData.nodes.Location, { id: 'LOC3' }),
                    lot: await context.read(xtremStockData.nodes.Lot, { id: 'MISCRECEIPT1', item }),
                    status: await context.read(xtremStockData.nodes.StockStatus, { id: 'A' }),
                    site,
                    stockUnit: await item.stockUnit,
                    owner: `${site._id}`,
                };

                const salesReturnReceipt = await context.create(xtremSales.nodes.SalesReturnReceipt, {
                    number: 'TEST',
                    effectiveDate: date.today(),
                    site: site._id,
                    shipToCustomer: '#US019',
                    lines: [
                        {
                            item: stockSearchData.item,
                            quantityInStockUnit: 20,
                            quantity: 20,
                            toReturnRequestLines: [
                                {
                                    linkedDocument: await salesReturnRequest.lines.at(0),
                                    quantity: 20,
                                    amount: 2,
                                    quantityInStockUnit: 20,
                                },
                            ],
                        },
                    ],
                });
                await salesReturnReceipt.$.save();

                // Header notes should be different and Lines notes should be equal
                const salesReturnReceiptInternalNote = (await salesReturnReceipt.internalNote).toString();
                assert.isEmpty(salesReturnReceiptInternalNote);
                // Lines
                const lineInternalNote = (await (await salesReturnReceipt.lines.elementAt(0)).internalNote).toString();
                assert.isNotEmpty(lineInternalNote);
                assert.equal(
                    lineInternalNote,
                    (await (await salesReturnRequest.lines.elementAt(0)).internalNote).toString(),
                );
            }
        }));
    it('Note propagation - Return Request to Return Receipt - isTransferHeaderNote = true && isTransferLineNote = true && is isOverwrite = true', () =>
        Test.withContext(async context => {
            const salesShipment: xtremSales.nodes.SalesShipment = await context.read(
                xtremSales.nodes.SalesShipment,
                { _id: '#SSHUS0022021030003' },
                { forUpdate: true },
            );
            await salesShipment.$.set({ isTransferHeaderNote: true, isTransferLineNote: true });
            const line1 = await salesShipment.lines.elementAt(0);
            await line1.$.set({ status: 'shipped' });
            await salesShipment.$.save();

            const salesReturnCreated = await (
                await new xtremSales.classes.SalesReturnRequestCreator(context).prepareNodeCreateData([
                    { salesDocumentLine: line1, quantityToProcess: 20 },
                ])
            ).createSalesOutputDocuments();
            if (salesReturnCreated.documentsCreated) {
                const salesReturnRequest: xtremSales.nodes.SalesReturnRequest = await context.read(
                    xtremSales.nodes.SalesReturnRequest,
                    { _id: salesReturnCreated.documentsCreated[0]._id },
                );
                const site = await context.read(xtremSystem.nodes.Site, { id: 'US002' });
                const item = await context.read(
                    xtremMasterData.nodes.Item,
                    { id: 'LOT_TEST1' },
                    {
                        forUpdate: true,
                    },
                );
                await item.$.set({ isSold: true });
                await item.$.save();
                const stockSearchData: xtremStockData.interfaces.StockSearchData = {
                    item,
                    location: await context.read(xtremMasterData.nodes.Location, { id: 'LOC3' }),
                    lot: await context.read(xtremStockData.nodes.Lot, { id: 'MISCRECEIPT1', item }),
                    status: await context.read(xtremStockData.nodes.StockStatus, { id: 'A' }),
                    site,
                    stockUnit: await item.stockUnit,
                    owner: `${site._id}`,
                };

                const salesReturnReceipt = await context.create(xtremSales.nodes.SalesReturnReceipt, {
                    number: 'TEST',
                    effectiveDate: date.today(),
                    site: site._id,
                    shipToCustomer: '#US019',
                    lines: [
                        {
                            item: stockSearchData.item,
                            quantityInStockUnit: 20,
                            quantity: 20,
                            toReturnRequestLines: [
                                {
                                    linkedDocument: await salesReturnRequest.lines.at(0),
                                    quantity: 20,
                                    amount: 2,
                                    quantityInStockUnit: 20,
                                },
                            ],
                            internalNote: TextStream.fromString('test'),
                        },
                    ],
                    isOverwriteNote: true,
                    internalNote: TextStream.fromString('test'),
                });
                await salesReturnReceipt.$.save();

                const salesReturnReceiptInternalNote = (await salesReturnReceipt.internalNote).toString();
                assert.isNotEmpty(salesReturnReceiptInternalNote);

                // Notes on return request must be equal on return receipt because isOverwrite = true
                assert.equal(salesReturnReceiptInternalNote, (await salesReturnRequest.internalNote).toString());
                // Lines
                const lineInternalNote = (await (await salesReturnReceipt.lines.elementAt(0)).internalNote).toString();
                assert.isNotEmpty(lineInternalNote);
                assert.equal(
                    lineInternalNote,
                    (await (await salesReturnRequest.lines.elementAt(0)).internalNote).toString(),
                );
            }
        }));
    it('Note propagation - Return Request to Return Receipt - isTransferHeaderNote = true && isTransferLineNote = true && is isOverwrite = false', () =>
        Test.withContext(async context => {
            const salesShipment: xtremSales.nodes.SalesShipment = await context.read(
                xtremSales.nodes.SalesShipment,
                { _id: '#SSHUS0022021030003' },
                { forUpdate: true },
            );
            await salesShipment.$.set({ isTransferHeaderNote: true, isTransferLineNote: true });
            const line1 = await salesShipment.lines.elementAt(0);
            await line1.$.set({ status: 'shipped' });
            await salesShipment.$.save();

            const salesReturnCreated = await (
                await new xtremSales.classes.SalesReturnRequestCreator(context).prepareNodeCreateData([
                    { salesDocumentLine: line1, quantityToProcess: 20 },
                ])
            ).createSalesOutputDocuments();
            if (salesReturnCreated.documentsCreated) {
                const salesReturnRequest: xtremSales.nodes.SalesReturnRequest = await context.read(
                    xtremSales.nodes.SalesReturnRequest,
                    { _id: salesReturnCreated.documentsCreated[0]._id },
                );
                const site = await context.read(xtremSystem.nodes.Site, { id: 'US002' });
                const item = await context.read(
                    xtremMasterData.nodes.Item,
                    { id: 'LOT_TEST1' },
                    {
                        forUpdate: true,
                    },
                );
                await item.$.set({ isSold: true });
                await item.$.save();
                const stockSearchData: xtremStockData.interfaces.StockSearchData = {
                    item,
                    location: await context.read(xtremMasterData.nodes.Location, { id: 'LOC3' }),
                    lot: await context.read(xtremStockData.nodes.Lot, { id: 'MISCRECEIPT1', item }),
                    status: await context.read(xtremStockData.nodes.StockStatus, { id: 'A' }),
                    site,
                    stockUnit: await item.stockUnit,
                    owner: `${site._id}`,
                };

                const salesReturnReceipt = await context.create(xtremSales.nodes.SalesReturnReceipt, {
                    number: 'TEST',
                    effectiveDate: date.today(),
                    site: site._id,
                    shipToCustomer: '#US019',
                    lines: [
                        {
                            item: stockSearchData.item,
                            quantityInStockUnit: 20,
                            quantity: 20,
                            toReturnRequestLines: [
                                {
                                    linkedDocument: await salesReturnRequest.lines.at(0),
                                    quantity: 20,
                                    amount: 2,
                                    quantityInStockUnit: 20,
                                },
                            ],
                            internalNote: TextStream.fromString('test'),
                        },
                    ],
                    isOverwriteNote: false,
                    internalNote: TextStream.fromString('test'),
                });
                await salesReturnReceipt.$.save();

                const salesReturnReceiptInternalNote = (await salesReturnReceipt.internalNote).toString();
                assert.isNotEmpty(salesReturnReceiptInternalNote);

                // Notes should be the ones set before
                assert.equal(salesReturnReceiptInternalNote, 'test');
                // Lines
                const lineInternalNote = (await (await salesReturnReceipt.lines.elementAt(0)).internalNote).toString();
                assert.isNotEmpty(lineInternalNote);
                assert.equal(lineInternalNote, 'test');
            }
        }));

    it('Sales return receipt finance integration check', () =>
        Test.withContext(async context => {
            let salesReturnReceipt = await context.read(
                xtremSales.nodes.SalesReturnReceipt,
                { _id: '#SRREC01' },
                { forUpdate: true },
            );

            let result = await xtremSales.functions.FinanceIntegration.salesReturnReceiptControlAndCreateMutationResult(
                context,
                salesReturnReceipt,
            );

            assert.equal(result.wasSuccessful, true);
            assert.equal(result.message, '');

            const itemSysId = (await (await salesReturnReceipt?.lines.at(0))?.item)?._id || 0;

            const item = await context.read(xtremMasterData.nodes.Item, { _id: itemSysId }, { forUpdate: true });

            await item.$.set({ postingClass: 'BAD_ITEM_1' });
            await item.$.save();

            salesReturnReceipt = await context.read(
                xtremSales.nodes.SalesReturnReceipt,
                { _id: 'SRREC01' },
                { forUpdate: true },
            );

            result = await xtremSales.functions.FinanceIntegration.salesReturnReceiptControlAndCreateMutationResult(
                context,
                salesReturnReceipt,
            );

            assert.equal(result.wasSuccessful, false);
            assert.equal(
                result.message,
                '* You need to select the Account dimension [Dimension type 1] for this item: Chair on document: SRREC01 .',
            );
        }));

    it('Check onceStockCompleted is called only when the document is completed', () =>
        Test.withContext(async context => {
            const result = await xtremStockData.functions.testLib.testOnceStockCompleted(context, {
                clas: xtremSales.nodes.SalesReturnReceipt,
                movementType: 'receipt',
                documents: [
                    { key: { number: 'SRREC01' }, isCompleted: false },
                    { key: { number: 'SRREC02' }, isCompleted: true },
                    { key: { number: 'SRREC03' }, isCompleted: false },
                    { key: { number: 'SRREC04' }, isCompleted: true },
                ],
                returnedProperty: 'number',
            });

            assert.deepEqual(result, ['SRREC02', 'SRREC04']);
        }));
});

describe('Posting details', () => {
    it('Gets posting details successfully', () =>
        Test.withContext(async context => {
            const srrDocumentNumber = 'SRR250001';
            const srrDocument = await context.read(xtremSales.nodes.SalesReturnReceipt, { number: srrDocumentNumber });
            assert.equal(await srrDocument.postingDetails.length, 1);

            const journalEntry = await srrDocument.postingDetails.at(0);
            assert.equal(await journalEntry?.documentType, 'salesReturnReceipt');
            assert.equal(await journalEntry?.documentNumber, srrDocumentNumber);
            assert.equal(await journalEntry?.documentSysId, srrDocument._id);
            assert.equal(await journalEntry?.targetDocumentType, 'journalEntry');
        }));
});
