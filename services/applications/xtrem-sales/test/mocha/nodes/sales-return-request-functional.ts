import { Test } from '@sage/xtrem-core';
import { assert } from 'chai';
import * as sinon from 'sinon';
import * as xtremSales from '../../../index';

describe('Linked to distribution-crud-sales-return-request', () => {
    afterEach(() => {
        sinon.restore();
    });
    it('redo the functionnal unit test ', () =>
        Test.withContext(
            async context => {
                const shipment = await context.read(xtremSales.nodes.SalesShipment, { number: 'SSH17' });
                assert.equal(await shipment.displayStatus, 'invoiced');
                assert.equal(await (await (await shipment.site).legalCompany).id, 'S1');

                assert.equal(await shipment.lines.length, 3);

                // Mock the financeIntegrationCheck function to always return true
                sinon
                    .stub(xtremSales.nodes.SalesReturnRequest, 'financeIntegrationCheck')
                    .resolves({ wasSuccessful: true, message: '' });

                const salesOutputDocuments = xtremSales.nodes.SalesShipment.createSalesReturnRequestFromShipments(
                    context,
                    [shipment],
                );

                await context.flushDeferredActions();

                assert.equal((await salesOutputDocuments).documentsCreated?.length, 1);
                assert.equal(await (await salesOutputDocuments).documentsCreated?.at(0)?.number, 'ST240001');
            },
            { today: '2024-01-01' },
        ));
});
