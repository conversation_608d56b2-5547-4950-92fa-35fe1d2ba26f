import type { Context, Dict } from '@sage/xtrem-core';
import { Test, ValidationSeverity } from '@sage/xtrem-core';
import { DateValue } from '@sage/xtrem-date-time';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremSystem from '@sage/xtrem-system';
import { assert } from 'chai';
import * as xtremSales from '../../../index';

const { withSequenceNumberContext } = xtremMasterData.functions.testLib;

describe('SalesReturnRequestNode', () => {
    function createSalesReturnRequestNode(
        context: Context,
        testKey: Dict<any>,
        statusDict = { status: 'draft', approvalStatus: 'draft', returnType: 'receiptAndCreditMemo' },
        skipLines = false,
        item = '#Chemical C not renewed',
        documentStatus = 'draft',
        originShippingSite = '#US001',
    ): Promise<xtremSales.nodes.SalesReturnRequest> {
        const incoterm = { id: 'EXW' };
        let lines;
        if (skipLines === false) {
            lines = [
                {
                    status: statusDict.status,
                    item,
                    quantity: 10,
                    reason: { name: 'Defective goods' },
                    originShippingSite,
                },
            ];
        }

        const baseData: Dict<any> = {
            site: '#US001',
            stockSite: '#US001',
            soldToCustomer: '#US019',
            shipToCustomer: '#US019',
            incoterm,
            ...testKey,
            lines,
            status: documentStatus,
            approvalStatus: statusDict.approvalStatus,
            returnType: statusDict.returnType,
        };
        return context.create(xtremSales.nodes.SalesReturnRequest, baseData);
    }

    function createSalesReturnRequestNodeAttributes(
        context: Context,
        testKey: Dict<any>,
        statusDict = { status: 'draft', approvalStatus: 'draft', returnType: 'receiptAndCreditMemo' },
        skipLines = false,
        item = '#Chemical C not renewed',
        documentStatus = 'draft',
        originShippingSite = '#US001',
    ): Promise<xtremSales.nodes.SalesReturnRequest> {
        const incoterm = { id: 'EXW' };
        let lines;
        if (skipLines === false) {
            lines = [
                {
                    status: statusDict.status as xtremSales.enums.SalesReturnRequestStatus,
                    item,
                    quantity: 10,
                    reason: { name: 'Defective goods' },
                    originShippingSite,
                    storedAttributes: { project: '', employee: '', task: 'TASK1' },
                },
            ];
        }

        return context.create(xtremSales.nodes.SalesReturnRequest, {
            site: '#US001',
            stockSite: '#US001',
            soldToCustomer: '#US019',
            shipToCustomer: '#US019',
            incoterm,
            ...testKey,
            lines,
            status: documentStatus as xtremSales.enums.SalesReturnRequestStatus,
            approvalStatus: statusDict.approvalStatus as xtremSales.enums.SalesReturnRequestApprovalStatus,
            returnType: statusDict.returnType as xtremSales.enums.SalesReturnRequestReturnType,
        });
    }

    it('Create sales return request - verifications ', () =>
        Test.withContext(async context => {
            const customer1 = await context.read(xtremMasterData.nodes.Customer, { _id: '#US019' });
            const businessEntityOne = await customer1.businessEntity;

            assert.equal(await businessEntityOne.id, 'US019');

            const cust01Addresses = businessEntityOne.addresses;
            const cust01Contacts = businessEntityOne.contacts;

            assert.equal(await cust01Addresses.length, 3, 'The customer should have 3 address');
            assert.equal(await cust01Contacts.length, 3, 'The customer should have 3 contacts');
        }));

    it('Create sales return request node 1', () =>
        Test.withContext(async context => {
            const salesReturnRequest = await createSalesReturnRequestNode(
                context,
                { number: 'test1' },
                { status: 'draft', approvalStatus: 'draft', returnType: 'receiptAndCreditMemo' },
                false,
                '#Chair',
            );
            await salesReturnRequest.$.save();
            assert.deepEqual(salesReturnRequest.$.context.diagnoses, []);
        }));

    it('Create sales return request node 2', () =>
        Test.withContext(async context => {
            const salesReturnRequest = await createSalesReturnRequestNode(
                context,
                { number: 'test1' },
                { status: 'draft', approvalStatus: 'draft', returnType: 'receiptAndNoCreditMemo' },
                false,
                '#Chair',
            );
            await salesReturnRequest.$.save();
            const salesReturnRequest2 = await context.query(xtremSales.nodes.SalesReturnRequest, { last: 1 }).at(0);
            if (!salesReturnRequest2) {
                throw new Error('Sales return request not found');
            }

            assert.equal(await (await salesReturnRequest2.lines.elementAt(0)).isCreditMemoExpected, false);
            assert.equal(await (await salesReturnRequest2.lines.elementAt(0)).isReceiptExpected, true);
            assert.equal(await (await salesReturnRequest2.lines.elementAt(0)).quantityCreditedInProgressInSalesUnit, 0);
            assert.equal(await (await salesReturnRequest2.lines.elementAt(0)).quantityCreditedPostedInSalesUnit, 0);
        }));

    it('Updating sales return request node fail 1', () =>
        Test.withContext(async context => {
            const salesReturnRequest = await context.read(
                xtremSales.nodes.SalesReturnRequest,
                { _id: '#SRR07' },
                { forUpdate: true },
            );
            await salesReturnRequest.$.set({ isPrinted: true });
            await assert.isRejected(salesReturnRequest.$.save(), 'The record was not updated.');
            assert.deepEqual(salesReturnRequest.$.context.diagnoses, [
                {
                    severity: 3,
                    path: [],
                    message: 'The sales return request is closed. You cannot update it.',
                },
            ]);
        }));

    it('Unable to delete a sales request line', () =>
        Test.withContext(
            async context => {
                const salesReturnRequest = await context.read(
                    xtremSales.nodes.SalesReturnRequest,
                    { _id: '#SRR13' },
                    { forUpdate: true },
                );

                await assert.isRejected(
                    salesReturnRequest.$.set({
                        lines: [{ _sortValue: 10, _action: 'delete' }],
                    }),
                    'The record was not deleted.',
                );
                assert.deepEqual(context.diagnoses, [
                    {
                        message: 'Deletion is not allowed. The sales return request line is approved or rejected.',
                        severity: 3,
                        path: ['lines', '1718'],
                    },
                ]);
            },
            { today: '2020-11-24' },
        ));

    it('Updating sales return request node fail 2', () =>
        Test.withContext(async context => {
            const salesReturnRequest = await context.read(
                xtremSales.nodes.SalesReturnRequest,
                { _id: '#SRR05' },
                { forUpdate: true },
            );
            await salesReturnRequest.$.set({
                lines: [{ _sortValue: 10, quantity: 200, _action: 'update' }],
            });
            await assert.isRejected(salesReturnRequest.$.save(), 'The record was not updated.');
            assert.deepEqual(salesReturnRequest.$.context.diagnoses, [
                {
                    severity: 3,
                    path: ['lines', '1704', 'quantity'],
                    message:
                        'The sales return line quantity cannot be larger than the remaining quantity on the related sales shipment line.',
                },
            ]);
        }));

    it('Updating sales return request node fail 3', () =>
        Test.withContext(async context => {
            const salesReturnRequest = await context.read(
                xtremSales.nodes.SalesReturnRequest,
                { _id: '#SRR05' },
                { forUpdate: true },
            );
            await salesReturnRequest.$.set({
                lines: [{ _sortValue: 10, status: 'closed', quantity: 5, _action: 'update' }],
            });
            await assert.isRejected(salesReturnRequest.$.save(), 'The record was not updated.');
            assert.deepEqual(salesReturnRequest.$.context.diagnoses, [
                {
                    severity: 3,
                    path: ['lines', '1704', 'quantity'],
                    message: 'The sales return line quantity cannot be lower than the already received quantity.',
                },
            ]);
        }));

    it('Updating sales return request node fail 4', () =>
        Test.withContext(async context => {
            const salesReturnRequest = await context.read(
                xtremSales.nodes.SalesReturnRequest,
                { _id: '#SRR05' },
                { forUpdate: true },
            );
            await salesReturnRequest.$.set({
                lines: [{ _sortValue: 10, quantity: 15, _action: 'update' }],
            });
            await assert.isRejected(salesReturnRequest.$.save(), 'The record was not updated.');
            assert.deepEqual(salesReturnRequest.$.context.diagnoses, [
                {
                    severity: 3,
                    path: ['lines', '1704', 'quantity'],
                    message:
                        "You cannot increase the line quantity when the sales return request is 'Approved' or 'Rejected'.",
                },
            ]);
        }));

    it('Updating sales return request node fail 5', () =>
        Test.withContext(async context => {
            const salesReturnRequest = await context.read(
                xtremSales.nodes.SalesReturnRequest,
                { _id: '#SRR05' },
                { forUpdate: true },
            );
            await salesReturnRequest.$.set({
                lines: [
                    {
                        _sortValue: 10,
                        quantity: 13,
                        _action: 'update',
                    },
                ],
            });
            await assert.isRejected(salesReturnRequest.$.save(), 'The record was not updated.');
            assert.deepEqual(salesReturnRequest.$.context.diagnoses, [
                {
                    severity: 3,
                    path: ['lines', '1704', 'quantity'],
                    message:
                        "You cannot decrease the line quantity when the sales return request is 'Approved' or 'Rejected' and the quantity is received.",
                },
            ]);
        }));

    it('Updating sales return request node', () =>
        Test.withContext(async context => {
            const salesReturnRequest = await context.read(
                xtremSales.nodes.SalesReturnRequest,
                { _id: '#SRR15' },
                { forUpdate: true },
            );
            await salesReturnRequest.$.set({
                lines: [{ _sortValue: 10, quantity: 9, _action: 'update' }],
            });
            await salesReturnRequest.$.save();
        }));

    it('Create sales return request node fail', () =>
        Test.withContext(async context => {
            const salesReturnRequest = await createSalesReturnRequestNode(
                context,
                { number: 'test1' },
                { status: 'pending', approvalStatus: 'draft', returnType: 'receiptAndCreditMemo' },
                false,
                '#Chemical C not renewed',
                'pending',
            );
            await assert.isRejected(salesReturnRequest.$.save(), 'The record was not created.');
            assert.deepEqual(salesReturnRequest.$.context.diagnoses, [
                {
                    severity: 3,
                    path: ['lines', '-1000000002'],
                    message:
                        "Sales return request line creation not allowed. The sales return request status is not 'Draft', or the approval status is 'Submitted for approval'.",
                },
                {
                    message: 'The record is not valid. You need to select a different record.',
                    path: ['lines', '-1000000002', 'item'],
                    severity: 3,
                },
                {
                    message: 'You need to remove the inactive items before you change the document status.',
                    path: ['lines', '-1000000002', 'item'],
                    severity: 3,
                },
            ]);
        }));

    it('Create sales return request node fail 2', () =>
        Test.withContext(async context => {
            const salesReturnRequest = await createSalesReturnRequestNode(
                context,
                { number: 'test1' },
                { status: 'pending', approvalStatus: 'draft', returnType: 'receiptAndCreditMemo' },
                false,
                '#Chemical C not renewed',
                'draft',
            );
            await assert.isRejected(salesReturnRequest.$.save(), 'The record was not created.');
            assert.deepEqual(salesReturnRequest.$.context.diagnoses, [
                {
                    severity: 3,
                    path: ['lines', '-1000000002'],
                    message: 'The sales return request lines status must have the same status than the header.',
                },
                {
                    message: 'The record is not valid. You need to select a different record.',
                    path: ['lines', '-1000000002', 'item'],
                    severity: 3,
                },
                {
                    message: 'You need to remove the inactive items before you change the document status.',
                    path: ['lines', '-1000000002', 'item'],
                    severity: 3,
                },
            ]);
        }));

    it('Create sales return request node fail 3', () =>
        Test.withContext(async context => {
            const salesReturnRequest = await createSalesReturnRequestNode(
                context,
                { number: 'test1' },
                { status: 'draft', approvalStatus: 'draft', returnType: 'receiptAndCreditMemo' },
                false,
                '#Chemical C not renewed',
                'draft',
                '#US002',
            );
            await assert.isRejected(salesReturnRequest.$.save(), 'The record was not created.');
            assert.deepEqual(salesReturnRequest.$.context.diagnoses, [
                {
                    severity: 3,
                    path: ['lines', '-1000000002'],
                    message:
                        'The origin shipping site on the sales return request lines must be the same as the stock site in the header.',
                },
                {
                    message: 'The record is not valid. You need to select a different record.',
                    path: ['lines', '-1000000002', 'item'],
                    severity: 3,
                },
                {
                    message: 'You need to remove the inactive items before you change the document status.',
                    path: ['lines', '-1000000002', 'item'],
                    severity: 3,
                },
            ]);
        }));

    it('Updating sales return request node fail (line status pending -> draft)', () =>
        Test.withContext(async context => {
            const salesReturnRequest = await context.read(
                xtremSales.nodes.SalesReturnRequest,
                { _id: '#SRR05' },
                { forUpdate: true },
            );

            await salesReturnRequest.$.set({
                status: 'pending',
                lines: [{ _sortValue: 10, status: 'draft', _action: 'update' }],
            });

            await assert.isRejected(salesReturnRequest.$.save(), 'The record was not updated.');
            assert.deepEqual(salesReturnRequest.$.context.diagnoses, [
                {
                    severity: 3,
                    path: ['lines', '1704'],
                    message:
                        'This document is at Pending status. You cannot set the sales return request line to draft.',
                },
            ]);
        }));

    it('Updating sales return request node fail 5', () =>
        Test.withContext(async context => {
            const salesReturnRequest = await context.read(
                xtremSales.nodes.SalesReturnRequest,
                { _id: '#SRR05' },
                { forUpdate: true },
            );
            await salesReturnRequest.$.set({
                lines: [{ _sortValue: 10, status: 'closed', _action: 'update' }],
            });

            await salesReturnRequest.$.save();

            const salesReturnRequest2 = await context.read(
                xtremSales.nodes.SalesReturnRequest,
                { _id: '#SRR05' },
                { forUpdate: true },
            );
            await salesReturnRequest2.$.set({
                shipToCustomer: '#US020',
                shipToCustomerAddress: '#US020|1600',
                lines: [{ _sortValue: 10, itemDescription: 'test', _action: 'update' }],
            });

            await assert.isRejected(salesReturnRequest2.$.save(), 'The record was not updated.');
            assert.deepEqual(salesReturnRequest2.$.context.diagnoses, [
                {
                    severity: 3,
                    path: ['lines', '1704'],
                    message: 'Update not allowed. The sales return request line is closed.',
                },
            ]);
        }));

    it('Updating sales return request node fail 6', () =>
        Test.withContext(async context => {
            const salesReturnRequest = await context.read(
                xtremSales.nodes.SalesReturnRequest,
                { _id: '#SRR05' },
                { forUpdate: true },
            );
            await salesReturnRequest.$.set({
                lines: [{ _sortValue: 10, receiptStatus: 'received', _action: 'update' }],
            });

            await salesReturnRequest.$.save();

            const salesReturnRequest2 = await context.read(
                xtremSales.nodes.SalesReturnRequest,
                { _id: '#SRR05' },
                { forUpdate: true },
            );

            await salesReturnRequest2.$.set({
                lines: [{ _sortValue: 10, itemDescription: 'test', _action: 'update' }],
            });
            await assert.isRejected(salesReturnRequest2.$.save(), 'The record was not updated.');
            assert.deepEqual(salesReturnRequest2.$.context.diagnoses, [
                {
                    severity: 3,
                    path: ['lines', '1704'],
                    message: 'Update not allowed. The sales return request line is received.',
                },
            ]);
        }));

    it('Updating sales return request node fail 7', () =>
        Test.withContext(async context => {
            const salesReturnRequest = await context.read(
                xtremSales.nodes.SalesReturnRequest,
                { _id: '#SRR05' },
                { forUpdate: true },
            );
            await salesReturnRequest.$.set({
                lines: [{ _sortValue: 10, item: '#Aqua', _action: 'update' }],
            });

            await assert.isRejected(salesReturnRequest.$.save(), 'The record was not updated.');
            assert.deepEqual(salesReturnRequest.$.context.diagnoses, [
                {
                    severity: 3,
                    path: ['lines', '1704', 'item'],
                    message: 'The item cannot be updated.',
                },
                {
                    message: 'value must be part of set [MILLILITER,LITER]',
                    path: ['lines', '1704', 'unit'],
                    severity: 3,
                },
            ]);
        }));

    it('Updating sales return request node fail 8', () =>
        Test.withContext(async context => {
            const salesReturnRequest = await context.read(
                xtremSales.nodes.SalesReturnRequest,
                { _id: '#SRR05' },
                { forUpdate: true },
            );
            await salesReturnRequest.$.set({
                lines: [{ _sortValue: 10, unit: '#LITER', _action: 'update' }],
            });

            await assert.isRejected(salesReturnRequest.$.save(), 'The record was not updated.');
            assert.deepEqual(salesReturnRequest.$.context.diagnoses, [
                {
                    severity: 3,
                    path: ['lines', '1704', 'unit'],
                    message: 'The sales unit cannot be updated.',
                },
                {
                    message: 'value must be part of set [OUNCE,GRAM]',
                    path: ['lines', '1704', 'unit'],
                    severity: 3,
                },
            ]);
        }));

    it('Verify chronological control between two sales return request on creation', () =>
        withSequenceNumberContext(
            'SalesReturnRequest',
            { isChronological: true },
            async context => {
                const salesReturnRequest = await createSalesReturnRequestNode(
                    context,
                    { number: '' },
                    { status: 'draft', approvalStatus: 'draft', returnType: 'receiptAndCreditMemo' },
                    false,
                    '#Chair',
                );
                await salesReturnRequest.$.save({ flushDeferredActions: true });
                assert.deepEqual(salesReturnRequest.$.context.diagnoses, []);

                const salesReturnRequestError = await createSalesReturnRequestNode(
                    context,
                    { number: '', date: '2022-04-26' },
                    { status: 'draft', approvalStatus: 'draft', returnType: 'receiptAndCreditMemo' },
                    false,
                    '#Chair',
                );
                await assert.isRejected(salesReturnRequestError.$.save(), 'The record was not created.');
                assert.deepEqual(salesReturnRequestError.$.context.diagnoses, [
                    {
                        message: 'The document date 2022-04-26 is earlier than the previous document date 2022-04-27.',
                        path: [],
                        severity: 3,
                    },
                ]);
            },
            { today: '2022-04-27' },
        ));

    it('Verify chronological control between two sales orders on change', () =>
        withSequenceNumberContext(
            'SalesReturnRequest',
            { isChronological: true },
            async context => {
                const salesReturnRequest = await createSalesReturnRequestNode(
                    context,
                    { number: '', date: '2022-04-27' },
                    { status: 'draft', approvalStatus: 'draft', returnType: 'receiptAndCreditMemo' },
                    false,
                    '#Chair',
                );
                await salesReturnRequest.$.save({ flushDeferredActions: true });
                assert.deepEqual(salesReturnRequest.$.context.diagnoses, []);

                const salesReturnRequestError = await createSalesReturnRequestNode(
                    context,
                    { number: '', date: '2022-04-27' },
                    { status: 'draft', approvalStatus: 'draft', returnType: 'receiptAndNoCreditMemo' },
                    false,
                    '#Chair',
                );
                await salesReturnRequestError.$.save({ flushDeferredActions: true });
                assert.deepEqual(salesReturnRequestError.$.context.diagnoses, []);

                await salesReturnRequestError.$.set({ date: DateValue.parse('2022-04-26') });
                await assert.isRejected(salesReturnRequestError.$.save(), 'The record was not updated.');
                assert.deepEqual(salesReturnRequestError.$.context.diagnoses, [
                    {
                        message: 'The document date 2022-04-26 is earlier than the previous document date 2022-04-27.',
                        path: [],
                        severity: 3,
                    },
                ]);
            },
            { today: '2022-04-27' },
        ));

    it('Ensure the correct behavior of the confirmation workflow', () =>
        Test.withContext(async context => {
            // Try to disable the approval management (enable the confirmation workflow). Failure expected - there's a
            // return request pending approval.
            const site = await context.read(xtremSystem.nodes.Site, { id: 'US001' }, { forUpdate: true });
            await site.$.set({ isSalesReturnRequestApprovalManaged: false });
            await assert.isRejected(site.$.save());
            assert.deepStrictEqual(site.$.context.diagnoses, [
                {
                    severity: ValidationSeverity.error,
                    path: ['isSalesReturnRequestApprovalManaged'],
                    message: 'Pending sales return requests need to be handled before disabling the approval process',
                },
            ]);

            // Get the pending return request and approve it.
            const salesReturnRequestPendingApproval = await context.read(xtremSales.nodes.SalesReturnRequest, {
                number: 'SRR14',
            });
            const approved = await xtremSales.nodes.SalesReturnRequest.approve(
                context,
                salesReturnRequestPendingApproval,
            );
            assert.deepStrictEqual(approved, 'The sales return request has been approved.');

            // Retry the control now that the pending return request is approved.
            assert.isOk(await site.$.control());

            // Create a return request. The approval management is disabled.
            let salesReturnRequest = await createSalesReturnRequestNode(
                context,
                { number: 'test1' },
                { status: 'draft', approvalStatus: 'draft', returnType: 'receiptAndCreditMemo' },
                false,
                '#Chair',
            );
            await salesReturnRequest.$.save();
            assert.deepStrictEqual(site.$.context.diagnoses, []);

            // Confirm the return request.
            const confirmed = await xtremSales.nodes.SalesReturnRequest.confirm(
                context,
                await salesReturnRequest.number,
            );
            assert.isTrue(confirmed);

            // Validate the return request's new statuses.
            salesReturnRequest = await context.read(xtremSales.nodes.SalesReturnRequest, {
                number: await salesReturnRequest.number,
            });
            assert.deepStrictEqual(await salesReturnRequest.status, 'pending');
            assert.deepStrictEqual(await salesReturnRequest.approvalStatus, 'confirmed');
        }));

    it('Create sales return request attributes type restricted to', () =>
        Test.withContext(async context => {
            const salesReturnRequest = await createSalesReturnRequestNodeAttributes(
                context,
                { number: 'test1' },
                { status: 'draft', approvalStatus: 'draft', returnType: 'receiptAndCreditMemo' },
                false,
                '#Chair',
            );
            await assert.isRejected(salesReturnRequest.$.save());
            assert.deepEqual(salesReturnRequest.$.context.diagnoses, [
                {
                    severity: 3,
                    path: ['lines', '-1000000002', 'storedAttributes'],
                    message: 'The Project attribute needs to be filled in.',
                },
                {
                    severity: 3,
                    path: ['lines', '-1000000002'],
                    message: 'The Project attribute needs to be filled in.',
                },
            ]);
        }));
});
