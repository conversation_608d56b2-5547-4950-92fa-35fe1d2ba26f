import type * as xtremSales from '../../../../lib';

export async function getExpectedSRR250001JE(salesReturnReceipt: xtremSales.nodes.SalesReturnReceipt, batchId: string) {
    const financialSite = await salesReturnReceipt.financialSite;
    const customer = await salesReturnReceipt.shipToCustomer;
    const currency = await financialSite.currency;
    const companyCurrency = await (await financialSite.legalCompany).currency;
    const salesReturnReceiptLine1 = await salesReturnReceipt.lines.at(0);

    return {
        documentSysId: salesReturnReceipt._id,
        batchId,
        financialSiteSysId: financialSite._id,
        documentNumber: 'SRR250001',
        documentDate: '2025-02-06',
        currencySysId: currency._id,
        batchSize: 1,
        documentType: 'salesReturnReceipt',
        targetDocumentType: 'journalEntry',
        documentLines: [
            {
                baseDocumentLineSysId: salesReturnReceiptLine1?._id,
                movementType: 'stockJournal',
                sourceDocumentNumber: 'ST250001',
                currencySysId: companyCurrency._id,
                customerSysId: customer._id,
                companyFxRate: '1',
                companyFxRateDivisor: '1',
                sourceDocumentType: 'salesReturnRequest',
                fxRateDate: '2025-02-06',
                itemSysId: (await salesReturnReceiptLine1?.item)?._id,
                amounts: [
                    {
                        amountType: 'amount',
                        amount: '22',
                        documentLineType: 'documentLine',
                    },
                ],
                storedDimensions: {
                    dimensionType01: '300',
                    dimensionType02: 'CHANNELVALUE1',
                    dimensionType04: 'DIMTYPE2VALUE2',
                },
                storedAttributes: {
                    businessSite: 'DEP1-S01',
                    customer: 'DECUS01',
                    project: 'AttPROJ',
                    financialSite: 'DEP1-S01',
                    stockSite: 'DEP1-S01',
                    item: 'AI_ITEM_001',
                },
            },
        ],
    };
}
