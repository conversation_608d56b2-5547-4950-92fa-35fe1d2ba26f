import type * as xtremStockData from '@sage/xtrem-stock-data';
import type * as xtremSales from '../../../../lib';

type ShipmentLineLineStatus = {
    lineSortValue: number;
    stockTransactionStatus: xtremStockData.enums.StockDocumentTransactionStatus;
    status: xtremSales.enums.SalesShipmentStatus;
};

export function getActualStatuses(shipment: xtremSales.nodes.SalesShipment): Promise<ShipmentLineLineStatus[]> {
    return shipment.lines
        .map(async line => ({
            lineSortValue: await line._sortValue,
            stockTransactionStatus: await line.stockTransactionStatus,
            status: await line.status,
        }))
        .toArray();
}
