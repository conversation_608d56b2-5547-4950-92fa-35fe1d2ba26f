import type { Context, NodeUpdateData } from '@sage/xtrem-core';
import { assert } from 'chai';
import * as xtremSales from '../../../../lib';

/**
 * Reads a sales order by ID, with optional update locking.
 */
export function readSalesOrder(
    context: Context,
    id: string = '#SO18',
    options?: { forUpdate: boolean },
): Promise<xtremSales.nodes.SalesOrder> {
    return context.read(xtremSales.nodes.SalesOrder, { _id: id }, options);
}

/**
 * Updates a sales order and returns a fresh copy.
 * @param context - The test context
 * @param input - Update parameters
 * @returns The updated sales order
 */
export async function updateSalesOrder(
    context: Context,
    input: {
        properties: NodeUpdateData<xtremSales.nodes.SalesOrder>;
        id?: string;
    },
): Promise<xtremSales.nodes.SalesOrder> {
    const { properties, id = '#SO18' } = input;
    const salesOrder = await readSalesOrder(context, id, { forUpdate: true });
    await salesOrder.$.set(properties);
    await salesOrder.$.save();
    return readSalesOrder(context, id);
}

/**
 * Validates print operations on a sales order.
 * Updates the sales order, runs the validation, and restores state if needed.
 * @param context - The test context
 * @param params - Validation parameters
 * @returns True if validation passes, undefined if error is expected
 */
export async function salesOrderPrintValidation(
    context: Context,
    params: {
        validationFn: (context: Context, salesOrder: xtremSales.nodes.SalesOrder) => Promise<boolean>;
        properties: NodeUpdateData<xtremSales.nodes.SalesOrder>;
        expectedError?: string;
        restoreProperties?: NodeUpdateData<xtremSales.nodes.SalesOrder>;
        id?: string;
    },
): Promise<boolean | undefined> {
    const { validationFn, properties, expectedError, restoreProperties, id } = params;
    const salesOrder = await updateSalesOrder(context, { properties, id });
    if (expectedError) {
        await assert.isRejected(validationFn(context, salesOrder), expectedError);
    } else {
        const result = await validationFn(context, salesOrder);
        assert.isTrue(result);
        return result;
    }
    if (restoreProperties) {
        await updateSalesOrder(context, { properties: restoreProperties, id });
    }
    return undefined;
}
