import type { Context } from '@sage/xtrem-core';
import { date } from '@sage/xtrem-core';
import * as xtremSales from '../../../../lib';

export async function createReturnRequest(context: Context) {
    const returnRequest = await context.create(xtremSales.nodes.SalesReturnRequest, {
        date: date.make(2020, 8, 10),
        site: '#US001',
        soldToCustomer: '#US019',
        lines: [
            {
                item: '#LOT_TEST1',
                quantity: 10,
                reason: '#TEST_EXCESS_QUANTITY_SHIPPED',
            },
        ],
    });
    await returnRequest.$.save({ flushDeferredActions: true });

    return returnRequest;
}
