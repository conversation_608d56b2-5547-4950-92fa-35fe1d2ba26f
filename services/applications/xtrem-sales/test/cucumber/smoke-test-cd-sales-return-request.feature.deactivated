@xtrem_sales
Feature:  smoke-test-cd-sales-return-request

    Scenario: Sales return request creation from shipment
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-sales/SalesShipment"

        Then the "Sales shipments" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user filters the "Number" labelled column in the table field with value "SSH4"
        And the user clicks the "Number" labelled nested field of row 1 in the table field
        Then the "SSH4" titled page is displayed
        Then the "SALES SHIPMENT" subtitled page is displayed
        And the user selects the "Request return" labelled business action button on the main page
        And the user clicks the "Yes" button of the Message dialog on the main page
        #Verify Creation
        And a toast containing text "Sales return request created" is displayed

    Scenario: Sales return request Deletion
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-sales/SalesReturnRequest"
        Then the "Sales return requests" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user filters the "Number" labelled column in the table field with value "ST220001"
        And the user clicks the "Number" labelled nested field of row 1 in the table field
        Then the "SALES RETURN REQUEST" subtitled page is displayed
        Then the "ST230001" titled page is displayed
        #No navigation panel full width
        And the user clicks on the delete CRUD button on the main page
        And the user clicks the "Delete" button of the Confirm dialog
        #Verify Deletion
        And a toast containing text "Record deleted" is displayed
