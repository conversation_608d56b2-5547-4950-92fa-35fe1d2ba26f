@xtrem_sales
Feature:  smoke-test-pr-cd-sales-invoice

    Scenario: Sales invoice creation
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-sales/SalesInvoice"
        Then the "Sales invoices" titled page is displayed
        #Click Create business action
        When the user clicks the "Create" labelled business action button on the navigation panel
        #Fill in a text field
        And the user selects the "Number" labelled text field on the main page
        And the user writes "SI-test" in the text field
        #Fill in site reference field
        And the user selects the "Site " labelled reference field on the main page
        And the user writes "Chem. Atlanta" in the reference field
        And the user selects "Chem. Atlanta" in the reference field
        #Fill in Customer reference field
        And the user selects the "bill-to customer " labelled reference field on the main page
        And the user writes "Dépot de TOULOUSE - Sud Ouest" in the reference field
        And the user selects "Dépot de TOULOUSE - Sud Ouest" in the reference field
        #We need to unhide the addLine button
        #And scrolls to the "generalSectionLinesBlock" bound block
        #Add a line
        #And the user clicks the "addSalesInvoiceLine" bound action of the "lines" bound table field on the main page
        Then the user selects the "lines" bound table field on the main page
        And the user adds a new table row to the table field using the sidebar
        #Select item on sidebar
        And the user selects the "Item" labelled reference field on the sidebar
        And the user writes "Consulting service" in the reference field
        And the user selects "Consulting service" in the reference field
        #Select Information tab on sidebar
        And selects the "Information" labelled navigation anchor on the sidebar
        #Fill in Quantity on sidebar
        And the user selects the "Quantity in sales unit" labelled numeric field on the sidebar
        And the user writes "10" in the numeric field
        And selects the "Price" labelled navigation anchor on the sidebar
        #Fill in Price on sidebar
        And the user selects the "Gross price" labelled numeric field on the sidebar
        And the user writes "12" in the numeric field
        And the user presses Enter
        #Click Apply button
        And the user clicks the "Apply" button of the dialog on the sidebar
        #Wait 1 second to complete line adding (widout this we have an error)
        And the user waits 4 second
        #Click Save Crud Button on main page
        And the user clicks the "Save" labelled business action button on the main page
        #Verify Creation
        Then a toast containing text "Record created" is displayed

    Scenario: Sales invoice Deletion
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-sales/SalesInvoice"
        Then the "Sales invoices" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        When the user filters the "number" bound column in the table field with value "SI-test"
        And the user selects the row 1 of the table field
        Then the value of the "number" bound nested text field of the selected row in the table field is "SI-test"
        And the user clicks the "Number" labelled nested field of the selected row in the table field
        Then the "Sales invoice SI-test" titled page is displayed
        When the user clicks the "Delete" labelled more actions button in the header
        And the user clicks the "Delete" button of the Confirm dialog
        #Verify Deletion
        Then a toast containing text "Record deleted" is displayed
