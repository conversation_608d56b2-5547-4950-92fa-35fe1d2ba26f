@xtrem_sales
Feature:  smoke-test-pr-cd-sales-credit-memo

    Scenario: Sales credit-memo creation from invoice credit
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-sales/SalesInvoice"
        Then the "Sales invoices" titled page is displayed
        When the user selects the "All statuses" dropdown option in the navigation panel
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        When the user filters the "number" bound column in the table field with value "SI15"
        And the user selects the row with text "SI15" in the "Number" labelled column header of the table field
        Then the value of the "number" bound nested text field of the selected row in the table field is "SI15"
        And the user clicks the "Number" labelled nested field of the selected row in the table field

        #Verifying the page is fully loaded before interacting with the page elements
        Then the titled page containing "Sales invoice SI15" is displayed


        #Credit the selected invoice to create the sales credit memo
        And the user clicks the "Create credit memo" labelled business action button on the main page
        And the user waits for 2 seconds
        And the user clicks the "Create" button of the Confirm dialog
        #Verify Creation
        Then a toast containing text "Record created" is displayed
        And the user waits for 2 seconds

    Scenario: Sales credit-memo Deletion
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-sales/SalesCreditMemo"
        #No navigation panel full width
        Then the "Sales credit memos" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        When the user filters the "number" bound column in the table field with value "SCE40001"
        And the user selects the row 1 of the table field
        Then the value of the "number" bound nested text field of the selected row in the table field is "SCE40001"
        And the user clicks the "Number" labelled nested field of the selected row in the table field
        Then the "Sales credit memo SCE40001" titled page is displayed
        When the user clicks the "Delete" labelled more actions button in the header
        And the user clicks the "Delete" button of the Confirm dialog
        #Verify Deletion
        Then a toast containing text "Record deleted" is displayed
