@xtrem_sales
Feature:  smoke-test-pr-cd-sales-shipment

    Scenario: Sales shipment creation
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-sales/SalesShipment"
        Then the "Sales shipments" titled page is displayed
        #Click Create business action
        When the user clicks the "Create" labelled business action button on the navigation panel
        #Fill in Customer reference field
        And the user selects the "shipToCustomer " labelled reference field on the main page
        And the user writes "Dépot de TOULOUSE - Sud Ouest" in the reference field
        And the user selects "Dépot de TOULOUSE - Sud Ouest" in the reference field
        #Fill in a text field
        And the user selects the "Number" labelled text field on the main page
        And the user writes "SH-test" in the text field
        #Fill in site reference field
        And selects the "Information" labelled navigation anchor on the main page
        And the user selects the "site " labelled reference field on the main page
        And the user writes "Chem. Atlanta" in the reference field
        And the user selects "Chem. Atlanta" in the reference field
        And selects the "Lines" labelled navigation anchor on the main page
        And the user clicks the "Add lines from orders" labelled business action button on the main page
        And the user selects the "$applicationCodeLookup" bound table field on a modal

        #Select the first record in the list returned by the lookup
        When the user selects the row 1 of the table field
        And the user ticks the main checkbox of the selected row in the table field
        And the user waits 1 second
        And the user clicks the "Select" button of the Lookup dialog

        And the user waits 1 second
        #Click Save Crud Button on main page
        And the user clicks the "Save" labelled business action button on the main page
        #Verify Creation
        Then a toast containing text "Record created" is displayed


    Scenario: Sales shipment creation confirmation and post (non-stock item)
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-sales/SalesShipment"
        Then the "Sales shipments" titled page is displayed
        #Click Create business action
        When the user clicks the "Create" labelled business action button on the navigation panel
        #Fill in Customer reference field
        And the user selects the "shipToCustomer " labelled reference field on the main page
        And the user writes "Dépot de TOULOUSE - Sud Ouest" in the reference field
        And the user selects "Dépot de TOULOUSE - Sud Ouest" in the reference field
        #Fill in a text field
        And the user selects the "Number" labelled text field on the main page
        And the user writes "SH-test25" in the text field
        #Fill in site reference field
        And selects the "Information" labelled navigation anchor on the main page
        And the user selects the "site " labelled reference field on the main page
        And the user writes "Chem. Atlanta" in the reference field
        And the user selects "Chem. Atlanta" in the reference field
        #Click Select from order line button
        And selects the "Lines" labelled navigation anchor on the main page
        And the user clicks the "Add lines from orders" labelled business action button on the main page
        And the user selects the "$applicationCodeLookup" bound table field on a modal

        #Select the first record in the list returned by the lookup
        When the user selects the row 1 of the table field
        And the user ticks the main checkbox of the selected row in the table field
        And the user waits 1 second
        And the user clicks the "Select" button of the Lookup dialog
        And the user waits 1 second
        #Click Save Crud Button on main page
        And the user clicks the "Save" labelled business action button on the main page
        #Verify Creation
        And a toast containing text "Record created" is displayed

        # #Click Confirm button
        And the user clicks the "Confirm" labelled business action button on the main page
        And the user clicks the "Confirm" button of the Confirm dialog
        And the user dismisses all the toasts

    Scenario: Sales shipment Deletion SH-test25
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-sales/SalesShipment"
        Then the "Sales shipments" titled page is displayed
        #Search record on navigation panel
        When the user selects the "All statuses" dropdown option in the navigation panel
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        When the user filters the "number" bound column in the table field with value "SH-test"
        And the user selects the row 1 of the table field
        Then the value of the "number" bound nested text field of the selected row in the table field is "SH-test25"
        And the user clicks the "Number" labelled nested field of the selected row in the table field
        Then the "Sales shipment SH-test25" titled page is displayed
        When the user clicks the "Delete" labelled more actions button in the header
        And the user clicks the "Delete" button of the Confirm dialog
        #Verify Deletion
        Then a toast containing text "Record deleted" is displayed
