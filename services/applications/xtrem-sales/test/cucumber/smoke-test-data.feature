@xtrem_sales
Feature: smoke-test-data

    #Case without navigation panel full width
    Scenario Outline: std \ <Page> \ desktop \ check each page loads without any problems
        Given the user opens the application on a desktop using the following link: "<Page>"
        Then the "<Title>" titled page is displayed
        Examples:
            | Page                                                  | Title                      |
            | @sage/xtrem-sales/SalesReturnRequest/eyJfaWQiOiIzMSJ9 | Sales return request SRR01 |
            # Opening pages having extensions in the current package using _id this need to change
            | @sage/xtrem-master-data/Site/eyJfaWQiOiI1In0=         | Site Chem. Austin          |
            | @sage/xtrem-master-data/Site/eyJfaWQiOiIzNiJ9         | Site Canadian site         |
