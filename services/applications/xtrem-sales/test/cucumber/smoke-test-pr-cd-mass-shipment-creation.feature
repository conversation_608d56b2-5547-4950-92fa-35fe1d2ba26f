@xtrem_sales
Feature: smoke-test-pr-cd-mass-shipment-creation

    Scenario: Mass shipment creation
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-sales/SalesOrderShippingMassProcess"
        #No navigation panel full width
        Then the "Mass shipment creation" titled page is displayed
        #Fill in Company reference field
        When the user selects the "Company" labelled reference field on the main page
        And the user writes "US Process Manufacturing 002" in the reference field
        And the user selects "US Process Manufacturing 002" in the reference field
        #Fill in Site reference field
        And the user selects the "Stock site" labelled reference field on the main page
        And the user writes "Chem. Chicago" in the reference field
        And the user selects "Chem. Chicago" in the reference field
        #Click show advanced selection button
        And the user clicks the "Show advanced selection" labelled business action button on the main page
        #Click Create button
        And the user clicks the "Create" labelled business action button on the main page
        #Verify Creation
        Then a toast containing text "Sales shipments created: 1" is displayed

    Scenario: Sales shipment Deletion
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-sales/SalesShipment"
        Then the "Sales shipments" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row 1 of the table field
        When the user clicks the "number" bound nested field of the selected row in the table field
        #When the user opens the navigation panel
        #Search first record on navigation panel
        And the user searches for "SHE40003" in the navigation panel
        And the user clicks the "first" navigation panel's row
        And the user clicks the "Delete" labelled more actions button in the header
        And the user clicks the "Delete" button of the Confirm dialog
        #Verify Deletion
        Then a toast containing text "Record deleted" is displayed
