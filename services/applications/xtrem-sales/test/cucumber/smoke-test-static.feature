@xtrem_sales
Feature: smoke-test-static

    #Case with navigation panel full width without clicking create business action
    Scenario Outline: sts \ <Page> \ desktop \ check each page loads without any problems
        Given the user opens the application on a desktop using the following link: "<Page>"
        Then the "<Title>" titled page is displayed

        Examples:
            | Page                                                | Title                        |
            | @sage/xtrem-sales/SalesReturnRequestReason          | Return request reasons       |
            | @sage/xtrem-sales/SalesCreditMemoReason             | Credit memo reasons          |
            | @sage/xtrem-sales/SalesOrder                        | Sales orders                 |
            | @sage/xtrem-sales/SalesShipment                     | Sales shipments              |
            | @sage/xtrem-sales/SalesOrderShippingMassProcess     | Mass shipment creation       |
            | @sage/xtrem-sales/SalesInvoice                      | Sales invoices               |
            | @sage/xtrem-sales/SalesShipmentInvoicingMassProcess | Mass invoice creation        |
            | @sage/xtrem-sales/SalesReturnRequest                | Sales return requests        |
            | @sage/xtrem-sales/SalesReturnReceipt                | Sales return receipts        |
            | @sage/xtrem-sales/SalesCreditMemo                   | Sales credit memos           |
            | @sage/xtrem-sales/SalesOrderMassAllocation          | Sales order mass allocation  |
            | @sage/xtrem-sales/SalesOrderLineInquiry             | Sales order line             |
            | @sage/xtrem-sales/SalesInvoiceLineInquiry           | Sales invoice line           |
            | @sage/xtrem-sales/UnbilledAccountReceivableInquiry  | Unbilled accounts receivable |

    #Case with navigation panel full width
    Scenario Outline: sts \ <Page> \ desktop \ check each page loads without any problems
        Given the user opens the application on a desktop using the following link: "<Page>"
        Then the "<NavigationPanelTitle>" titled page is displayed
        #Click Create business action
        When the user clicks the "Create" labelled multi action button on the navigation panel
        Then the "<Title>" titled page is displayed

        Examples:
            | Page                         | NavigationPanelTitle | Title |
            # Opening pages having extensions in the current package
            | @sage/xtrem-master-data/Site | Sites                | Site  |


    # Positive test to ensure that the error dialog is definitely exist and open
    Scenario: sts \ xtrem-sales \ desktop \ check an error dialog appears when the page does not load
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/Wazup"
        Then an error dialog appears on the main page
