@xtrem_sales
Feature:  smoke-test-pr-cd-sales-return-receipt


    Scenario: Sales return receipt creation
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-sales/SalesReturnReceipt"
        Then the "Sales return receipts" titled page is displayed
        #Click Create business action
        When the user clicks the "Create" labelled business action button on the navigation panel
        #Fill in a text field
        And the user selects the "Number" labelled text field on the main page
        And the user writes "SO-test" in the text field
        #Fill in site reference field
        And the user selects the "site" labelled reference field on the main page
        And the user writes "Chem. Atlanta" in the reference field
        And the user selects "Chem. Atlanta" in the reference field
        #Fill in Customer reference field

        And the user selects the "shipToCustomer" labelled reference field on the main page
        And the user writes "Dépot de TOULOUSE - Sud Ouest" in the reference field
        And the user selects "Dépot de TOULOUSE - Sud Ouest" in the reference field

        #Select the look up table
        And selects the "Lines" labelled navigation anchor on the main page
        And the user clicks the "Add lines from return requests" labelled business action button on the main page
        And the user selects the "$applicationCodeLookup" bound table field on a modal

        #Select the first record in the list returned by the lookup
        When the user selects the row 1 of the table field
        And the user ticks the main checkbox of the selected row in the table field
        And the user waits 1 second
        And the user clicks the "Select" button of the Lookup dialog
        And the user waits 1 second

        And the user clicks the "Save" labelled business action button on the main page
        #Verify Creation
        Then a toast containing text "Record created" is displayed



#deletion is not possible
# Scenario: Sales shipment Deletion
