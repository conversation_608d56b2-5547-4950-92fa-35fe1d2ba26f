@xtrem_sales
Feature: smoke-test-pr-cd-return-request-reason

    Scenario: Create return request reason
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-sales/SalesReturnRequestReason"
        Then the "Return request reasons" titled page is displayed
        #Click Create business action
        When the user clicks the "Create" labelled business action button on the navigation panel
        #Fill in a text field
        And the user selects the "name" labelled text field on the main page
        And the user writes "Wrong color" in the text field
        And the user selects the "description" bound text field on the main page
        And the user writes "Wrong color" in the text field
        #Click Save Crud Button
        And the user clicks the "Save" labelled business action button on the main page
        #Verify Creation
        Then a toast containing text "Record created" is displayed

    Scenario: Attribute delete
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-sales/SalesReturnRequestReason"
        Then the "Return request reasons" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        When the user filters the "description" bound column in the table field with value "Wrong color"
        And the user selects the row 1 of the table field
        And the user clicks the "Description" labelled nested field of the selected row in the table field
        Then the "Return request reason Wrong color" titled page is displayed
        #Click Delete Crud Button
        When the user clicks the "Delete" labelled more actions button in the header
        And the user clicks the "Delete" button of the Confirm dialog
        #Verify Deletion
        Then a toast containing text "Record deleted" is displayed
