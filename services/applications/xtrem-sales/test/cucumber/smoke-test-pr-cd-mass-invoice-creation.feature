@xtrem_sales
Feature: smoke-test-pr-cd-mass-invoice-creation

    Scenario: Mass invoice creation
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-sales/SalesShipmentInvoicingMassProcess"
        #No navigation panel full width
        Then the "Mass invoice creation" titled page is displayed
        #Fill in Company reference field
        When the user selects the "Company" labelled reference field on the main page
        And the user writes "US Process Manufacturing 002" in the reference field
        And the user selects "US Process Manufacturing 002" in the reference field
        #Fill in Site reference field
        And the user selects the "Site" labelled reference field on the main page
        And the user writes "Chem. Chicago" in the reference field
        And the user selects "Chem. Chicago" in the reference field
        #Click Create button
        And the user clicks the "Create" labelled business action button on the main page
        #Verify Creation
        Then a toast containing text "Sales invoices created: 2" is displayed

    Scenario: Sales invoice Deletion
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-sales/SalesInvoice"
        Then the "Sales invoices" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        When the user filters the "number" bound column in the table field with value "SIE40001"
        And the user selects the row 1 of the table field
        Then the value of the "number" bound nested text field of the selected row in the table field is "SIE40001"
        And the user clicks the "Number" labelled nested field of the selected row in the table field
        Then the "Sales invoice SIE40001" titled page is displayed
        When the user clicks the "Delete" labelled more actions button in the header
        And the user clicks the "Delete" button of the Confirm dialog
        And a toast containing text "Record deleted" is displayed
        And the user dismisses all the toasts
        #Then the user opens the navigation panel
        #Search second record on navigation panel
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        When the user filters the "number" bound column in the table field with value "SIE40002"
        And the user selects the row 1 of the table field
        Then the value of the "number" bound nested text field of the selected row in the table field is "SIE40002"
        And the user clicks the "Number" labelled nested field of the selected row in the table field
        Then the "Sales invoice SIE40002" titled page is displayed
        When the user clicks the "Delete" labelled more actions button in the header
        And the user clicks the "Delete" button of the Confirm dialog
        #Verify Deletion
        Then a toast containing text "Record deleted" is displayed
        And the user dismisses all the toasts
