import { ReferenceDataType, RoundingMode } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremSales from '../index';

export const salesAmountDataType = new xtremMasterData.dataTypes.PriceDataType({
    precision: 20,
    scale: 3,
    roundingMode: RoundingMode.roundUp,
});

export const salesPriceDataType = new xtremMasterData.dataTypes.PriceDataType({
    precision: 15,
    scale: 3,
    roundingMode: RoundingMode.roundHalfUp,
});

export const salesInvoiceDefault = new ReferenceDataType<xtremSales.nodes.SalesInvoice>({
    reference: () => xtremSales.nodes.SalesInvoice,
    lookup: {
        valuePath: 'number',
        helperTextPath: 'number',
        columnPaths: ['number', 'displayStatus', 'date'],
    },
});
