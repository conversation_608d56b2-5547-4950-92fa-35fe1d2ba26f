import type { Site } from '@sage/xtrem-master-data/build/lib/pages/site';
import type { GraphApi } from '@sage/xtrem-sales-api';
import type { User } from '@sage/xtrem-system-api';
import * as ui from '@sage/xtrem-ui';
import { onChangeIsSalesReturnRequestApprovalManaged } from '../client-functions/site';

@ui.decorators.pageExtension<SiteExtension>({
    extends: '@sage/xtrem-master-data/Site',
})
export class SiteExtension extends ui.PageExtension<Site, GraphApi> {
    wasDirtyBeforeChanging: boolean;

    @ui.decorators.block<SiteExtension>({
        parent() {
            return this.managementSection;
        },
        title: 'Sales approval',
    })
    salesDefaultApproverBlock: ui.containers.Block;

    @ui.decorators.switchField<SiteExtension>({
        parent() {
            return this.salesDefaultApproverBlock;
        },
        title: 'Return request',
        onClick() {
            this.wasDirtyBeforeChanging = this.$.isDirty;
        },
        async onChange() {
            await onChangeIsSalesReturnRequestApprovalManaged(this, this.wasDirtyBeforeChanging);
        },
    })
    isSalesReturnRequestApprovalManaged: ui.fields.Switch;

    @ui.decorators.referenceField<SiteExtension, User>({
        parent() {
            return this.salesDefaultApproverBlock;
        },
        title: 'Approver',
        node: '@sage/xtrem-system/User',
        tunnelPage: '@sage/xtrem-authorization/User',
        valueField: 'displayName',
        lookupDialogTitle: 'Select approver',
        minLookupCharacters: 2,
        width: 'large',
        columns: [
            ui.nestedFields.text({ title: 'Last name', bind: 'lastName' }),
            ui.nestedFields.text({ title: 'First name', bind: 'firstName' }),
            ui.nestedFields.text({ title: 'Email', bind: 'email' }),
        ],
        isReadOnly() {
            return !this.isSalesReturnRequestApprovalManaged.value;
        },
    })
    salesReturnRequestDefaultApprover: ui.fields.Reference<User>;

    @ui.decorators.referenceField<SiteExtension, User>({
        parent() {
            return this.salesDefaultApproverBlock;
        },
        title: 'Substitute approver',
        node: '@sage/xtrem-system/User',
        tunnelPage: '@sage/xtrem-authorization/User',
        valueField: 'displayName',
        lookupDialogTitle: 'Select substitute approver',
        minLookupCharacters: 2,
        width: 'large',
        columns: [
            ui.nestedFields.text({ title: 'Last name', bind: 'lastName' }),
            ui.nestedFields.text({ title: 'First name', bind: 'firstName' }),
            ui.nestedFields.text({ title: 'Email', bind: 'email' }),
        ],
        isReadOnly() {
            return !this.isSalesReturnRequestApprovalManaged.value;
        },
    })
    salesReturnRequestSubstituteApprover: ui.fields.Reference<User>;
}

declare module '@sage/xtrem-master-data/build/lib/pages/site' {
    export interface Site extends SiteExtension {}
}
