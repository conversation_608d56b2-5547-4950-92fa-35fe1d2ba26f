import type { Customer as CustomerNode } from '@sage/xtrem-master-data-api';
import type { Customer } from '@sage/xtrem-master-data/build/lib/pages/customer';
import type { GraphApi } from '@sage/xtrem-sales-api';
import * as ui from '@sage/xtrem-ui';

@ui.decorators.pageExtension<CustomerExtension, CustomerNode>({
    extends: '@sage/xtrem-master-data/Customer',
    headerDropDownActions() {
        return [this.createOrder, ui.menuSeparator({ insertAfter: 'createOrder' })];
    },
    navigationPanel: {
        dropdownActions: [
            {
                title: 'Create order',
                icon: 'none',
                insertBefore: 'separator',
                isHidden(_recordId: string, rowItem: ui.PartialNodeWithId<CustomerNode>) {
                    if (rowItem.isActive === false || rowItem.isOnHold === true) return true;
                    return false;
                },
                async onClick(_recordId: string, rowItem: ui.PartialNodeWithId<CustomerNode>) {
                    if (rowItem._id) {
                        await this.$.dialog.page(
                            `@sage/xtrem-sales/SalesOrder`,
                            {
                                _id: '$new',
                                isCustomerDialog: true,
                            },
                            {
                                fullScreen: true,
                                resolveOnCancel: true,
                                values: { soldToCustomer: `_id:${rowItem._id}` },
                            },
                        );
                    }
                },
            },
        ],
    },
})
export class CustomerExtension extends ui.PageExtension<Customer, GraphApi> {
    @ui.decorators.pageAction<CustomerExtension>({
        title: 'Create order',
        icon: 'add',
        insertBefore() {
            return this.putOnHold;
        },
        isHidden() {
            if (this.isActive.value === false || this.isOnHold.value === true) return true;
            return false;
        },
        onClick() {
            const { values } = this.$;
            if (this._id.value) {
                return this.$.dialog.page(
                    `@sage/xtrem-sales/SalesOrder`,
                    {
                        _id: '$new',
                        isCustomerDialog: true,
                    },
                    { fullScreen: true, resolveOnCancel: true, values: { soldToCustomer: `_id:${values._id}` } },
                );
            }
            return undefined;
        },
    })
    createOrder: ui.PageAction;
}
