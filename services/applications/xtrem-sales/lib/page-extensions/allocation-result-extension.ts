import type { Graph<PERSON><PERSON> } from '@sage/xtrem-sales-api';
import type { AllocationResult } from '@sage/xtrem-stock-data/build/lib/pages/allocation-result';
import * as ui from '@sage/xtrem-ui';
import type * as salesSharedInterfaces from '../shared-functions/interfaces';

@ui.decorators.pageExtension<AllocationResultExtension>({
    extends: '@sage/xtrem-stock-data/AllocationResult',
    onLoad() {
        if (this.documentType.value !== 'salesOrder') {
            this.latestShippingDate.isHidden = true;
            this.fromSoldToCustomer.isHidden = true;
            this.toSoldToCustomer.isHidden = true;
            this.incoterm.isHidden = true;
            this.deliveryMode.isHidden = true;
        } else {
            this.fromOrderNumber.title = ui.localize(
                '@sage/xtrem-sales/pages__allocation_result__fromSalesOrderNumberTitle',
                'From sales order',
            );
            this.toOrderNumber.title = ui.localize(
                '@sage/xtrem-sales/pages__allocation_result__toSalesOrderNumberTitle',
                'To sales order',
            );
        }

        const criteria = this.massProcessCriteria.value
            ? (JSON.parse(this.massProcessCriteria.value) as salesSharedInterfaces.SalesAllocationMassProcessCriteria)
            : null;

        if (criteria) {
            this.stockSiteName.value = criteria.stockSite ?? '';
            this.fromItemText.value = criteria.fromItem ?? '';
            this.toItemText.value = criteria.toItem ?? '';
            this.fromOrderNumber.value = criteria.fromOrderNumber ?? '';
            this.toOrderNumber.value = criteria.toOrderNumber ?? '';

            this.fromSoldToCustomer.value = criteria.fromSoldToCustomer ?? '';
            this.toSoldToCustomer.value = criteria.toSoldToCustomer ?? '';
            this.incoterm.value = criteria.incoterm ?? '';
            this.deliveryMode.value = criteria.deliveryMode ?? '';
            this.latestShippingDate.value = criteria.maximumShippingDate ?? '';
        }
    },
})
export class AllocationResultExtension extends ui.PageExtension<AllocationResult, GraphApi> {
    @ui.decorators.dateField<AllocationResultExtension>({
        title: 'Latest shipping date',
        isTransient: true,
        width: 'medium',
        isReadOnly: true,
        parent() {
            return this.criteriaBlock;
        },
        insertAfter() {
            return this.processType;
        },
    })
    latestShippingDate: ui.fields.Date;

    @ui.decorators.textField<AllocationResultExtension>({
        title: 'From sold-to customer',
        isTransient: true,
        width: 'medium',
        isReadOnly: true,
        parent() {
            return this.criteriaBlock;
        },
        insertAfter() {
            return this.toItemText;
        },
    })
    fromSoldToCustomer: ui.fields.Text;

    @ui.decorators.textField<AllocationResultExtension>({
        title: 'To sold-to customer',
        isTransient: true,
        width: 'medium',
        isReadOnly: true,
        parent() {
            return this.criteriaBlock;
        },
        insertAfter() {
            return this.fromSoldToCustomer;
        },
    })
    toSoldToCustomer: ui.fields.Text;

    @ui.decorators.textField<AllocationResultExtension>({
        title: 'Incoterms rule',
        isTransient: true,
        width: 'medium',
        isReadOnly: true,
        parent() {
            return this.criteriaBlock;
        },
        insertAfter() {
            return this.toSoldToCustomer;
        },
    })
    incoterm: ui.fields.Text;

    @ui.decorators.textField<AllocationResultExtension>({
        title: 'Delivery mode',
        isTransient: true,
        width: 'medium',
        isReadOnly: true,
        parent() {
            return this.criteriaBlock;
        },
        insertAfter() {
            return this.incoterm;
        },
    })
    deliveryMode: ui.fields.Text;
}

declare module '@sage/xtrem-stock-data/build/lib/pages/allocation-result' {
    export interface Site extends AllocationResultExtension {}
}
