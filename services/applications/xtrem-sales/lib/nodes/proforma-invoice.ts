import type { Context, Reference, integer } from '@sage/xtrem-core';
import { BusinessRuleError, Node, NodeStatus, TextStream, date, datetime, decorators } from '@sage/xtrem-core';
import * as xtremReporting from '@sage/xtrem-reporting';
import type * as xtremSystem from '@sage/xtrem-system';
import { dataTypes } from '@sage/xtrem-system';
import * as xtremUpload from '@sage/xtrem-upload';
import * as xtremSales from '..';

@decorators.node<ProformaInvoice>({
    isClearedByReset: true,
    package: 'xtrem-sales',
    storage: 'sql',
    isPublished: true,
    canRead: true,
    canSearch: true,
    canCreate: true,
    canUpdate: true,
    indexes: [
        {
            orderBy: { salesOrder: 1, version: 1 },
            isUnique: true,
            isNaturalKey: true,
        },
    ],
    async saveEnd() {
        if (this.$.status === NodeStatus.added) {
            const lastProformaInvoice = await xtremSales.functions.ProformaInvoiceLib.getLastProforma({
                proformaInvoice: this,
                forUpdate: true,
            });
            if (lastProformaInvoice) {
                await lastProformaInvoice.$.set({
                    isActive: false,
                });
                await lastProformaInvoice.$.save();
            }
        }
    },
})
export class ProformaInvoice extends Node {
    _lastProforma: xtremSales.nodes.ProformaInvoice | undefined;

    @decorators.referenceProperty<ProformaInvoice, 'salesOrder'>({
        isStored: true,
        isPublished: true,
        node: () => xtremSales.nodes.SalesOrder,
        lookupAccess: true,
    })
    readonly salesOrder: Reference<xtremSales.nodes.SalesOrder>;

    @decorators.integerProperty<ProformaInvoice, 'version'>({
        isStored: true,
        isPublished: true,
        isFrozen: true,
        lookupAccess: true,
        async defaultValue() {
            const lastProformaInvoice = await xtremSales.functions.ProformaInvoiceLib.getLastProforma({
                proformaInvoice: this,
            });
            return lastProformaInvoice ? (await lastProformaInvoice.version) + 1 : 1;
        },
    })
    readonly version: Promise<integer>;

    @decorators.dateProperty<ProformaInvoice, 'expirationDate'>({
        isStored: true,
        isPublished: true,
        isRequired: true,
        lookupAccess: true,
        async isFrozen() {
            return !(await this.isActive);
        },
        async control(cx, val) {
            await cx.error
                .withMessage(
                    '@sage/xtrem-sales/nodes__proforma_invoice_expiration_date_cant_be_earlier_than_today',
                    `The expiration date needs to be on or after the current date.`,
                )
                .if(
                    ((this.$.status === NodeStatus.modified && val !== (await (await this.$.old).expirationDate)) ||
                        this.$.status === NodeStatus.added) &&
                        (await this.isActive) &&
                        val.compare(date.today()) < 0,
                )
                .is.true();
        },
    })
    readonly expirationDate: Promise<date>;

    @decorators.dateProperty<ProformaInvoice, 'issueDate'>({
        isPublished: true,
        // eslint-disable-next-line @sage/xtrem/property-decorators-errors
        async computeValue() {
            return (await this._createStamp).date;
        },
        lookupAccess: true,
    })
    readonly issueDate: Promise<date>;

    @decorators.stringProperty<ProformaInvoice, 'createdBy'>({
        isPublished: true,
        dataType: () => dataTypes.name,
        // eslint-disable-next-line @sage/xtrem/property-decorators-errors
        async computeValue() {
            return ((await this.$.createdBy) as xtremSystem.nodes.User).displayName;
        },
        lookupAccess: true,
    })
    readonly createdBy: Promise<string>;

    @decorators.booleanProperty<ProformaInvoice, 'isActive'>({
        isStored: true,
        isPublished: true,
        defaultValue: true,
        async control(cx, val) {
            const lastProformaInvoice = await xtremSales.functions.ProformaInvoiceLib.getLastProforma({
                proformaInvoice: this,
            });
            if (this.$.status === NodeStatus.modified && val && (await lastProformaInvoice?.isActive)) {
                cx.error.addLocalized(
                    '@sage/xtrem-sales/nodes__proforma_invoice_only_last_version_is_active',
                    'Only the last version can be active.',
                );
            }
        },
        lookupAccess: true,
    })
    readonly isActive: Promise<boolean>;

    @decorators.booleanProperty<ProformaInvoice, 'isSent'>({
        isStored: true,
        isPublished: true,
        defaultValue: false,
        async isFrozen() {
            return !(await this.isActive);
        },
        lookupAccess: true,
    })
    readonly isSent: Promise<boolean>;

    @decorators.booleanProperty<ProformaInvoice, 'isExpired'>({
        isPublished: true,
        async getValue() {
            return (await this.isActive) && (await this.expirationDate) < date.today();
        },
        lookupAccess: true,
    })
    readonly isExpired: Promise<boolean>;

    @decorators.textStreamProperty<ProformaInvoice, 'customerComment'>({
        isPublished: true,
        isStored: true,
        dataSensitivityLevel: 'personal',
        anonymizeMethod: 'fixed',
        anonymizeValue: TextStream.fromString('*'.repeat(15)),
        dataType: () => xtremSales.dataTypes.comment,
        lookupAccess: true,
        async defaultValue() {
            const lastProformaInvoice = await xtremSales.functions.ProformaInvoiceLib.getLastProforma({
                proformaInvoice: this,
            });
            return lastProformaInvoice ? lastProformaInvoice.customerComment : TextStream.empty;
        },
        async isFrozen() {
            return !(await this.isActive);
        },
    })
    readonly customerComment: Promise<TextStream>;

    @decorators.referenceProperty<ProformaInvoice, 'uploadedFile'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
        lookupAccess: true,
        node: () => xtremUpload.nodes.UploadedFile,
        async isFrozen() {
            return !(await this.isActive);
        },
    })
    uploadedFile: Reference<xtremUpload.nodes.UploadedFile | null>;

    @decorators.booleanProperty<ProformaInvoice, 'isLinkExpired'>({
        isPublished: true,
        lookupAccess: true,
        dependsOn: ['uploadedFile', 'isActive'],
        async computeValue() {
            // eslint-disable-next-line @typescript-eslint/await-thenable
            const linkExpirationDateTime = (await (await this.uploadedFile)?.expirationDate) ?? null;
            return (await this.isActive) && linkExpirationDateTime !== null && datetime.now() > linkExpirationDateTime;
        },
    })
    readonly isLinkExpired: Promise<boolean>;

    static async getProformaInvoiceFromSalesOrder(
        context: Context,
        salesOrder: xtremSales.nodes.SalesOrder,
        forUpdate: boolean = false,
    ) {
        const proformaInvoice = await context.tryRead(
            ProformaInvoice,
            {
                _id: (await salesOrder.proformaInvoices?.find(invoice => invoice.isActive))?._id,
            },
            {
                forUpdate,
            },
        );

        if (!proformaInvoice) {
            const reportName = 'proformaInvoice';
            await xtremReporting.functions.sendUserNotification(context, {
                status: 'rejected',
                rejectReason: context.localize(
                    '@sage/xtrem-sales/nodes__proforma_invoice__cannot_print_inactive_proforma_invoice',
                    'Could not print an inactive proforma invoice.',
                ),
                reportName,
            });

            throw new BusinessRuleError(
                context.localize(
                    '@sage/xtrem-sales/nodes__proforma_invoice__cannot_print_inactive_proforma_invoice',
                    'Could not print an inactive proforma invoice.',
                ),
            );
        }

        return proformaInvoice;
    }

    /**
     *  Checks if the invoice can be printed
     * @param context
     * @param salesOrder
     * @returns true if the proforma invoice can be printed
     * @throws {BusinessRuleError} if the proforma invoice cannot be printed
     */
    @decorators.mutation<typeof ProformaInvoice, 'beforePrintProformaInvoice'>({
        isPublished: true,
        startsReadOnly: true,
        parameters: [
            {
                name: 'salesOrder',
                type: 'reference',
                isMandatory: true,
                node: () => xtremSales.nodes.SalesOrder,
            },
        ],
        return: 'boolean',
    })
    static async beforePrintProformaInvoice(
        context: Context,
        salesOrder: xtremSales.nodes.SalesOrder,
    ): Promise<boolean> {
        await this.getProformaInvoiceFromSalesOrder(context, salesOrder);

        return true;
    }

    @decorators.mutation<typeof ProformaInvoice, 'afterPrintProformaInvoice'>({
        isPublished: true,
        startsReadOnly: true,
        parameters: [
            {
                isMandatory: true,
                name: 'salesOrder',
                type: 'reference',
                node: () => xtremSales.nodes.SalesOrder,
            },
            {
                isMandatory: true,
                name: 'uploadedFile',
                type: 'reference',
                node: () => xtremUpload.nodes.UploadedFile,
            },
            {
                isNullable: true,
                name: 'error',
                type: 'object',
                properties: {
                    message: {
                        isMandatory: true,
                        type: 'string',
                    },
                },
            },
        ],
        return: 'boolean',
    })
    static async afterPrintProformaInvoice(
        readonlyContext: Context,
        salesOrder: xtremSales.nodes.SalesOrder,
        uploadedFile: xtremUpload.nodes.UploadedFile,
        error: { message: string } | null,
    ): Promise<boolean> {
        let innerError: Error | undefined;

        if (!error) {
            const uploadStatus = await uploadedFile.status;

            if (uploadStatus && ['verified', 'created', 'uploaded'].includes(uploadStatus)) {
                await readonlyContext.runInWritableContext(async writableContext => {
                    const proformaInvoice = await this.getProformaInvoiceFromSalesOrder(
                        writableContext,
                        salesOrder,
                        true,
                    );

                    await proformaInvoice.$.set({
                        uploadedFile,
                    });
                    await proformaInvoice.$.save();
                });

                return true;
            }
        } else {
            innerError = new Error(error.message);
        }

        throw new BusinessRuleError(
            readonlyContext.localize(
                '@sage/xtrem-sales/nodes__proforma_invoice__cannot_print_proforma_invoice',
                'Could not print proforma invoice.',
            ),
            innerError,
        );
    }
}
