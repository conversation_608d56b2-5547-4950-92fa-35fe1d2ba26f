import type { date, decimal, Reference } from '@sage/xtrem-core';
import { decorators, Node } from '@sage/xtrem-core';
import * as xtremFinanceData from '@sage/xtrem-finance-data';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremSystem from '@sage/xtrem-system';
import * as xtremSales from '..';

@decorators.node<UnbilledAccountReceivableResultLine>({
    storage: 'sql',
    isPublished: true,
    canRead: true,
    canSearch: true,
    isVitalCollectionChild: true,
})
export class UnbilledAccountReceivableResultLine extends Node {
    @decorators.referenceProperty<UnbilledAccountReceivableResultLine, 'inputSet'>({
        isStored: true,
        isPublished: true,
        isVitalParent: true,
        node: () => xtremSales.nodes.UnbilledAccountReceivableInputSet,
    })
    readonly inputSet: Reference<xtremSales.nodes.UnbilledAccountReceivableInputSet>;

    @decorators.referenceProperty<UnbilledAccountReceivableResultLine, 'customer'>({
        isStored: true,
        isPublished: true,
        isRequired: true,
        ignoreIsActive: true,
        node: () => xtremMasterData.nodes.Customer,
    })
    readonly customer: Reference<xtremMasterData.nodes.Customer>;

    @decorators.referenceProperty<UnbilledAccountReceivableResultLine, 'currency'>({
        isStored: true,
        isPublished: true,
        isRequired: true,
        node: () => xtremMasterData.nodes.Currency,
    })
    readonly currency: Reference<xtremMasterData.nodes.Currency>;

    @decorators.referenceProperty<UnbilledAccountReceivableResultLine, 'financialSite'>({
        isPublished: true,
        isStored: true,
        isRequired: true,
        node: () => xtremSystem.nodes.Site,
        ignoreIsActive: true,
    })
    readonly financialSite: Reference<xtremSystem.nodes.Site>;

    @decorators.referenceProperty<UnbilledAccountReceivableResultLine, 'salesUnit'>({
        isStored: true,
        isPublished: true,
        isRequired: true,
        node: () => xtremMasterData.nodes.UnitOfMeasure,
    })
    readonly salesUnit: Reference<xtremMasterData.nodes.UnitOfMeasure>;

    @decorators.decimalProperty<UnbilledAccountReceivableResultLine, 'netPrice'>({
        isPublished: true,
        isStored: true,
        isRequired: true,
        dataType: () => xtremMasterData.dataTypes.priceInSalesPrice,
    })
    readonly netPrice: Promise<decimal>;

    @decorators.referenceProperty<UnbilledAccountReceivableResultLine, 'item'>({
        isStored: true,
        isPublished: true,
        isRequired: true,
        ignoreIsActive: true,
        node: () => xtremMasterData.nodes.Item,
    })
    readonly item: Reference<xtremMasterData.nodes.Item>;

    @decorators.referenceProperty<UnbilledAccountReceivableResultLine, 'site'>({
        isPublished: true,
        isStored: true,
        isRequired: true,
        node: () => xtremSystem.nodes.Site,
        ignoreIsActive: true,
    })
    readonly site: Reference<xtremSystem.nodes.Site>;

    @decorators.decimalProperty<UnbilledAccountReceivableResultLine, 'quantity'>({
        isPublished: true,
        isStored: true,
        isRequired: true,
        dataType: () => xtremMasterData.dataTypes.quantityInStockUnit,
    })
    readonly quantity: Promise<decimal>;

    @decorators.referenceProperty<UnbilledAccountReceivableResultLine, 'account'>({
        isPublished: true,
        isStored: true,
        node: () => xtremFinanceData.nodes.Account,
        ignoreIsActive: true,
        isNullable: true,
    })
    readonly account: Reference<xtremFinanceData.nodes.Account> | null;

    @decorators.referenceProperty<UnbilledAccountReceivableResultLine, 'accountItem'>({
        isPublished: true,
        isStored: true,
        node: () => xtremFinanceData.nodes.Account,
        ignoreIsActive: true,
        isNullable: true,
    })
    readonly accountItem: Reference<xtremFinanceData.nodes.Account> | null;

    @decorators.decimalProperty<UnbilledAccountReceivableResultLine, 'invoicedQuantity'>({
        isPublished: true,
        isStored: true,
        isRequired: true,
        dataType: () => xtremMasterData.dataTypes.quantityInStockUnit,
    })
    readonly invoicedQuantity: Promise<decimal>;

    @decorators.decimalProperty<UnbilledAccountReceivableResultLine, 'creditedQuantity'>({
        isPublished: true,
        isStored: true,
        isRequired: true,
        dataType: () => xtremMasterData.dataTypes.quantityInStockUnit,
    })
    readonly creditedQuantity: Promise<decimal>;

    @decorators.decimalProperty<UnbilledAccountReceivableResultLine, 'returnedQuantity'>({
        isPublished: true,
        isStored: true,
        isRequired: true,
        dataType: () => xtremMasterData.dataTypes.quantityInStockUnit,
    })
    readonly returnedQuantity: Promise<decimal>;

    @decorators.decimalProperty<UnbilledAccountReceivableResultLine, 'invoiceIssuableQuantity'>({
        isPublished: true,
        isStored: true,
        isRequired: true,
        dataType: () => xtremMasterData.dataTypes.quantityInStockUnit,
    })
    readonly invoiceIssuableQuantity: Promise<decimal>;

    @decorators.decimalProperty<UnbilledAccountReceivableResultLine, 'invoiceIssuableAmount'>({
        isPublished: true,
        isStored: true,
        isRequired: true,
        dataType: () => xtremMasterData.dataTypes.priceInSalesPrice,
    })
    readonly invoiceIssuableAmount: Promise<decimal>;

    @decorators.referenceProperty<UnbilledAccountReceivableResultLine, 'company'>({
        isPublished: true,
        node: () => xtremSystem.nodes.Company,
        ignoreIsActive: true,
        provides: ['company'],
        async getValue() {
            return (await this.financialSite).legalCompany;
        },
    })
    readonly company: Reference<xtremSystem.nodes.Company>;

    @decorators.stringProperty<UnbilledAccountReceivableResultLine, 'shipmentNumber'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremSales.dataTypes.propertyDataType,
    })
    readonly shipmentNumber: Promise<string>;

    // Note: - Even if the property is filled with an internal ID, we need it in the page as a reference to enable
    //         tunneling. The internal ID (_id) is stored as a number.
    //       - We can accept this here for we need this data only for showing data in the inquiry page.
    @decorators.referenceProperty<UnbilledAccountReceivableResultLine, 'shipmentInternalId'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
        node: () => xtremSales.nodes.SalesShipment,
    })
    readonly shipmentInternalId: Reference<xtremSales.nodes.SalesShipment> | null;

    @decorators.dateProperty<UnbilledAccountReceivableResultLine, 'documentDate'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
    })
    readonly documentDate: Promise<date | null>;

    @decorators.referenceProperty<UnbilledAccountReceivableResultLine, 'shipToCustomer'>({
        isStored: true,
        isPublished: true,
        isRequired: true,
        ignoreIsActive: true,
        node: () => xtremMasterData.nodes.Customer,
    })
    readonly shipToCustomer: Reference<xtremMasterData.nodes.Customer>;

    @decorators.decimalProperty<UnbilledAccountReceivableResultLine, 'invoiceIssuableAmountInCompanyCurrency'>({
        isPublished: true,
        isStored: true,
        isRequired: false,
        isNullable: true,
        dataType: () => xtremMasterData.dataTypes.priceInSalesPrice,
    })
    readonly invoiceIssuableAmountInCompanyCurrency: Promise<decimal | null>;

    @decorators.decimalProperty<
        UnbilledAccountReceivableResultLine,
        'invoiceIssuableAmountInCompanyCurrencyAtAsOfDate'
    >({
        isPublished: true,
        isStored: true,
        isRequired: false,
        isNullable: true,
        dataType: () => xtremMasterData.dataTypes.priceInSalesPrice,
    })
    readonly invoiceIssuableAmountInCompanyCurrencyAtAsOfDate: Promise<decimal | null>;

    @decorators.referenceProperty<UnbilledAccountReceivableResultLine, 'companyCurrency'>({
        isStored: true,
        isPublished: true,
        isRequired: false,
        isNullable: true,
        node: () => xtremMasterData.nodes.Currency,
    })
    readonly companyCurrency: Reference<xtremMasterData.nodes.Currency | null>;
}
