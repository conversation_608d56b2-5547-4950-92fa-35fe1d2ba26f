import * as xtremAuthorization from '@sage/xtrem-authorization';
import type { Collection, Context, Dict, Reference, decimal } from '@sage/xtrem-core';
import {
    BusinessRuleError,
    NodeStatus,
    TextStream,
    asyncArray,
    date,
    decorators,
    useDefaultValue,
} from '@sage/xtrem-core';
import type * as xtremFinanceData from '@sage/xtrem-finance-data';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremSystem from '@sage/xtrem-system';
import { isPostedErrorClosed } from '../functions/sales-document-common-lib';
import { getCreditMemoInstance, updateHeaderNotesOnCreation } from '../functions/sales-return-request-lib';
import * as xtremSales from '../index';
import type { FilteredUsers, UserSearchCriteria } from '../shared-functions/interfaces';

@decorators.subNode<SalesReturnRequest>({
    extends: () => xtremMasterData.nodes.BaseDocument,
    isPublished: true,
    canCreate: true,
    canRead: true,
    canUpdate: true,
    canDelete: true,
    hasAttachments: true,
    async controlBegin(cx) {
        await xtremSales.events.controlBegin.returnRequest.checkSiteConsistency(cx, this);
        await xtremSales.events.controlBegin.returnRequest.checkClosedStatusOnUpdate(cx, this);
        await xtremSales.events.controlBegin.returnRequest.checkSiteOnUpdate(cx, this);
        await xtremSales.events.controlBegin.returnRequest.checkSoldToCustomerOnUpdate(cx, this);
        await xtremSales.events.controlBegin.returnRequest.checkReturnTypeOnUpdate(cx, this);
        await xtremMasterData.events.control.baseDocumentControls.atLeastOneLine(cx, this);
    },
    async saveBegin() {
        if ([NodeStatus.modified, NodeStatus.unchanged].includes(this.$.status)) {
            await this.$.set({ isCreditingAllowed: await this.calculateIsCreditingAllowed() });
        }

        await xtremMasterData.functions.controlDocumentNumber(this);
        await updateHeaderNotesOnCreation(this);

        // On control events this throws an error
        if ((await this.status) !== 'closed') {
            if (this.$.status === NodeStatus.added) {
                await this.$.context.flushDeferredActions();
            }
            const financeIntegrationCheckResult = await xtremSales.nodes.SalesReturnRequest.financeIntegrationCheck(
                this.$.context,
                this,
            );
            if (financeIntegrationCheckResult.message) {
                throw new BusinessRuleError(financeIntegrationCheckResult.message);
            }
        }
    },

    async controlDelete(cx): Promise<void> {
        await xtremSales.events.controlDelete.returnRequest.checkReceiptStatusOnDeletion(cx, this);
        await xtremSales.events.controlDelete.returnRequest.checkCreditStatusOnDeletion(cx, this);
        await xtremSales.events.controlDelete.returnRequest.checkApprovalStatusOnDeletion(cx, this);
    },
    async saveEnd() {
        await this.updateRelatedSalesDocumentsStatuses();
    },
    async deleteEnd(): Promise<void> {
        await this.updateRelatedSalesDocumentsStatuses();
    },
})
export class SalesReturnRequest
    extends xtremMasterData.nodes.BaseDocument
    implements xtremMasterData.interfaces.Document
{
    __skipPrepare: boolean;

    __salesShipments: Dict<[xtremSales.interfaces.SalesDocumentLineUpdateInterface]> = {};

    skipIsOnHold = false;

    @decorators.enumPropertyOverride<SalesReturnRequest, 'status'>({
        // dataType: () => xtremSales.enums.salesReturnRequestStatusDataType,
    })
    override readonly status: Promise<xtremSales.enums.SalesReturnRequestStatus>;

    @decorators.enumPropertyOverride<SalesReturnRequest, 'displayStatus'>({
        dependsOn: ['receiptStatus', 'status', 'approvalStatus'],
        async updatedValue() {
            return xtremSales.functions.SalesReturnRequestLib.calculateSalesReturnRequestDisplayStatus(
                await this.status,
                await this.receiptStatus,
                await this.approvalStatus,
            );
        },
    })
    override readonly displayStatus: Promise<xtremSales.enums.SalesReturnRequestDisplayStatus>;

    @decorators.enumProperty<SalesReturnRequest, 'receiptStatus'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremSales.enums.salesDocumentReturnRequestReceiptStatusDataType,
        defaultValue() {
            return 'notReceived';
        },
    })
    readonly receiptStatus: Promise<xtremSales.enums.SalesDocumentReturnRequestReceiptStatus>;

    @decorators.enumProperty<SalesReturnRequest, 'creditStatus'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremSales.enums.salesDocumentCreditStatusDataType,
        defaultValue: () => 'notCredited',
    })
    readonly creditStatus: Promise<xtremSales.enums.SalesDocumentCreditStatus>;

    /** deprecated */
    @decorators.referenceProperty<SalesReturnRequest, 'salesSite'>({
        isPublished: true,
        node: () => xtremSystem.nodes.Site,
        getValue() {
            return this.site;
        },
    })
    readonly salesSite: Reference<xtremSystem.nodes.Site>;

    @decorators.referencePropertyOverride<SalesReturnRequest, 'site'>({
        // isRequired: true,
        filters: { control: { isSales: true } },
    })
    override readonly site: Reference<xtremSystem.nodes.Site>;

    @decorators.referencePropertyOverride<SalesReturnRequest, 'stockSite'>({
        dependsOn: ['site', 'shipToCustomerAddress'],
        defaultValue() {
            return xtremSales.functions.getStockSiteValue(this);
        },
    })
    override readonly stockSite: Reference<xtremSystem.nodes.Site>;

    @decorators.referenceProperty<SalesReturnRequest, 'requester'>({
        isStored: true,
        isPublished: true,
        isRequired: true,
        node: () => xtremSystem.nodes.User,
        async defaultValue() {
            return this.$.context.read(xtremSystem.nodes.User, { email: (await this.$.context.user)!.email });
        },
        filters: {
            lookup: xtremAuthorization.filters.user.interactiveUsers,
        },
    })
    readonly requester: Reference<xtremSystem.nodes.User>;

    /** deprecated */
    @decorators.dateProperty<SalesReturnRequest, 'returnRequestDate'>({
        isPublished: true,
        lookupAccess: true,
        dependsOn: ['date'],
        getValue() {
            return this.date;
        },
    })
    readonly returnRequestDate: Promise<date>;

    @decorators.datePropertyOverride<SalesReturnRequest, 'date'>({
        control(cx, val) {
            if (val.compare(date.today()) > 0) {
                cx.error.addLocalized(
                    '@sage/xtrem-sales/nodes__sales_return_request__return_request_date_cannot_be_future',
                    'The return request date cannot be later than today.',
                );
            }
        },
    })
    override readonly date: Promise<date>;

    @decorators.referenceProperty<SalesReturnRequest, 'soldToCustomer'>({
        isStored: true,
        isPublished: true,
        isRequired: true,
        node: () => xtremMasterData.nodes.Customer,
    })
    readonly soldToCustomer: Reference<xtremMasterData.nodes.Customer>;

    @decorators.referenceProperty<SalesReturnRequest, 'soldToLinkedAddress'>({
        isStored: true,
        isPublished: true,
        isRequired: true,
        dependsOn: ['soldToCustomer'],
        node: () => xtremMasterData.nodes.BusinessEntityAddress,
        async defaultValue() {
            return (await this.soldToCustomer)?.primaryAddress;
        },
    })
    readonly soldToLinkedAddress: Reference<xtremMasterData.nodes.BusinessEntityAddress>;

    @decorators.referenceProperty<SalesReturnRequest, 'soldToAddress'>({
        isPublished: true,
        isStored: true,
        isMutable: true,
        isNullable: true,
        node: () => xtremMasterData.nodes.Address,
        dependsOn: ['soldToLinkedAddress'],
        async defaultValue() {
            return xtremSales.functions.getSalesAddressDetail(await this.soldToLinkedAddress);
        },
        updatedValue: useDefaultValue,
    })
    readonly soldToAddress: Reference<xtremMasterData.nodes.Address | null>;

    @decorators.referenceProperty<SalesReturnRequest, 'soldToContact'>({
        isPublished: true,
        isStored: true,
        isMutable: true,
        isNullable: true,
        dependsOn: ['soldToLinkedAddress'],
        node: () => xtremMasterData.nodes.Contact,
        async defaultValue() {
            return xtremSales.functions.getBusinessEntityContact(await this.soldToLinkedAddress);
        },
        updatedValue: useDefaultValue,
    })
    readonly soldToContact: Reference<xtremMasterData.nodes.Contact | null>;

    @decorators.referenceProperty<SalesReturnRequest, 'shipToCustomer'>({
        isStored: true,
        isPublished: true,
        isRequired: true,
        lookupAccess: true,
        node: () => xtremMasterData.nodes.Customer,
        dependsOn: ['soldToCustomer'],
        defaultValue() {
            return this.soldToCustomer;
        },
    })
    readonly shipToCustomer: Reference<xtremMasterData.nodes.Customer>;

    @decorators.referenceProperty<SalesReturnRequest, 'shipToCustomerAddress'>({
        isStored: true,
        isPublished: true,
        isRequired: true,
        dependsOn: ['shipToCustomer'],
        lookupAccess: true,
        node: () => xtremMasterData.nodes.BusinessEntityAddress,
        async defaultValue() {
            return (await this.shipToCustomer).primaryShipToAddress;
        },
        filters: {
            control: {
                async businessEntity() {
                    return (await this.shipToCustomer).businessEntity;
                },
                deliveryDetail: { _ne: null },
            },
        },
    })
    readonly shipToCustomerAddress: Reference<xtremMasterData.nodes.BusinessEntityAddress>;

    @decorators.referenceProperty<SalesReturnRequest, 'shipToAddress'>({
        isPublished: true,
        isStored: true,
        isMutable: true,
        isNullable: true,
        lookupAccess: true,
        dependsOn: ['shipToCustomerAddress'],
        node: () => xtremMasterData.nodes.Address,
        async defaultValue() {
            return (await this.shipToCustomerAddress).address;
        },
        updatedValue: useDefaultValue,
    })
    readonly shipToAddress: Reference<xtremMasterData.nodes.Address | null>;

    @decorators.referenceProperty<SalesReturnRequest, 'shipToContact'>({
        isPublished: true,
        isStored: true,
        isMutable: true,
        isNullable: true,
        dependsOn: ['shipToCustomerAddress'],
        node: () => xtremMasterData.nodes.Contact,
        async defaultValue() {
            return (await (await this.shipToCustomerAddress).primaryContact)?.contact ?? null;
        },
        updatedValue: useDefaultValue,
    })
    readonly shipToContact: Reference<xtremMasterData.nodes.Contact | null>;

    @decorators.referenceProperty<SalesReturnRequest, 'billToCustomer'>({
        isStored: true,
        isPublished: true,
        isRequired: true,
        lookupAccess: true,
        node: () => xtremMasterData.nodes.Customer,
        dependsOn: ['shipToCustomer'],
        async defaultValue() {
            return (await this.shipToCustomer).billToCustomer;
        },
    })
    readonly billToCustomer: Reference<xtremMasterData.nodes.Customer>;

    @decorators.referenceProperty<SalesReturnRequest, 'billToLinkedAddress'>({
        isStored: true,
        isPublished: true,
        isRequired: true,
        node: () => xtremMasterData.nodes.BusinessEntityAddress,
        dependsOn: ['shipToCustomer', 'billToCustomer'],

        defaultValue() {
            return this.getBillToAddressValue();
        },
    })
    readonly billToLinkedAddress: Reference<xtremMasterData.nodes.BusinessEntityAddress>;

    async getBillToAddressValue(): Promise<xtremMasterData.nodes.BusinessEntityAddress | null> {
        const shipToCustomer = await this.shipToCustomer;
        const billToCustomer = await this.billToCustomer;
        const billToLinkedAddress = await shipToCustomer.billToAddress;
        if (billToCustomer === (await shipToCustomer.billToCustomer) && billToLinkedAddress) {
            return billToLinkedAddress;
        }
        return billToCustomer.primaryAddress;
    }

    @decorators.referenceProperty<SalesReturnRequest, 'billToAddress'>({
        isPublished: true,
        isStored: true,
        isMutable: true,
        isNullable: true,
        node: () => xtremMasterData.nodes.Address,
        dependsOn: ['billToLinkedAddress'],
        async defaultValue() {
            return (await this.billToLinkedAddress).address;
        },
        updatedValue: useDefaultValue,
    })
    readonly billToAddress: Reference<xtremMasterData.nodes.Address | null>;

    @decorators.referenceProperty<SalesReturnRequest, 'billToContact'>({
        isPublished: true,
        isStored: true,
        isMutable: true,
        isNullable: true,
        node: () => xtremMasterData.nodes.Contact,
        dependsOn: ['billToLinkedAddress'],
        async defaultValue() {
            return (await (await this.billToLinkedAddress).primaryContact)?.contact ?? null;
        },
        updatedValue: useDefaultValue,
    })
    readonly billToContact: Reference<xtremMasterData.nodes.Contact | null>;

    @decorators.referenceProperty<SalesReturnRequest, 'deliveryMode'>({
        isStored: true,
        isPublished: true,
        isRequired: true,
        dependsOn: ['shipToCustomerAddress'],
        node: () => xtremMasterData.nodes.DeliveryMode,
        async defaultValue() {
            return (await (await this.shipToCustomerAddress)?.deliveryDetail)?.mode || null;
        },
    })
    readonly deliveryMode: Reference<xtremMasterData.nodes.DeliveryMode>;

    @decorators.referenceProperty<SalesReturnRequest, 'incoterm'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
        dependsOn: ['shipToCustomerAddress'],
        node: () => xtremMasterData.nodes.Incoterm,
        async defaultValue() {
            return (await (await this.shipToCustomerAddress)?.deliveryDetail)?.incoterm || null;
        },
    })
    readonly incoterm: Reference<xtremMasterData.nodes.Incoterm | null>;

    @decorators.enumProperty<SalesReturnRequest, 'returnType'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        dataType: () => xtremSales.enums.salesReturnRequestReturnTypeDataType,
        defaultValue: 'receiptAndCreditMemo',
    })
    readonly returnType: Promise<xtremSales.enums.SalesReturnRequestReturnType>;

    @decorators.collectionPropertyOverride<SalesReturnRequest, 'lines'>({
        dependsOn: ['shipToCustomerAddress', 'site', 'stockSite', 'soldToCustomer', 'deliveryMode'],
        node: () => xtremSales.nodes.SalesReturnRequestLine,
    })
    override readonly lines: Collection<xtremSales.nodes.SalesReturnRequestLine>;

    @decorators.booleanProperty<SalesReturnRequest, 'isCreditingAllowed'>({
        isStored: true,
        isPublished: true,
        defaultValue: false,
    })
    readonly isCreditingAllowed: Promise<boolean>;

    @decorators.textStreamPropertyOverride<SalesReturnRequest, 'internalNote'>({
        async isFrozen() {
            return isPostedErrorClosed(await this.status);
        },
    })
    override readonly internalNote: Promise<TextStream>;

    @decorators.booleanPropertyOverride<SalesReturnRequest, 'isTransferHeaderNote'>({
        async isFrozen() {
            return isPostedErrorClosed(await this.status);
        },
    })
    override readonly isTransferHeaderNote: Promise<boolean>;

    @decorators.booleanPropertyOverride<SalesReturnRequest, 'isTransferLineNote'>({
        async isFrozen() {
            return isPostedErrorClosed(await this.status);
        },
    })
    override readonly isTransferLineNote: Promise<boolean>;

    private async updateRelatedSalesDocumentsStatuses(): Promise<void> {
        const salesShipmentsArray = await this.$.context
            .query(xtremSales.nodes.SalesShipment, {
                filter: { _id: { _in: Object.keys(this.__salesShipments) } } as any,
                forUpdate: true,
            })
            .toArray();
        await xtremSales.classes.SalesReturnRequestCreator.updateRelatedSalesDocumentsStatuses({
            salesDocumentsArray: salesShipmentsArray,
            tempDict: '__salesShipments',
            statusType: 'returnRequestStatus',
            classInstance: this,
            aggregateNode: xtremSales.nodes.SalesShipmentLineToSalesReturnRequestLine,
            enumDataType: xtremSales.enums.salesDocumentReturnStatusDataType,
        });
    }

    /**
     * Method that closes a sales return request
     * @param context
     * @param salesReturnRequestNumber the number of the sales return request
     * @returns enum salesReturnRequestCloseStatusMethod
     */
    @decorators.mutation<typeof SalesReturnRequest, 'setSalesReturnRequestCloseStatus'>({
        isPublished: true,
        parameters: [{ name: 'salesReturnRequestNumber', type: 'string', isMandatory: false }],
        return: {
            type: 'enum',
            dataType: () => xtremSales.enums.salesReturnRequestCloseStatusMethodReturnDataType,
        },
    })
    static async setSalesReturnRequestCloseStatus(
        context: Context,
        salesReturnRequestNumber: string,
    ): Promise<xtremSales.enums.SalesReturnRequestCloseStatusMethodReturn> {
        const isNotClosed = await SalesReturnRequest.checkReturnReceiptHeaderStatusLinkedToReturnRequest(
            context,
            salesReturnRequestNumber,
        );
        if (isNotClosed) {
            return isNotClosed;
        }

        return (await xtremSales.functions.setSalesDocumentCloseStatus(
            context,
            salesReturnRequestNumber,
            xtremSales.nodes.SalesReturnRequest,
            xtremSales.enums.salesReturnRequestCloseStatusMethodReturnDataType,
        )) as xtremSales.enums.SalesReturnRequestCloseStatusMethodReturn;
    }

    static async salesReturnReceiptIsNotClosed(
        context: Context,
        salesReturnRequestLineToSalesReturnReceiptLines: xtremSales.nodes.SalesReturnRequestLineToSalesReturnReceiptLine[],
    ): Promise<boolean> {
        let isNotClosed = false;
        await asyncArray(salesReturnRequestLineToSalesReturnReceiptLines).forEach(
            async (
                salesReturnRequestLineToSalesReturnReceiptLine: xtremSales.nodes.SalesReturnRequestLineToSalesReturnReceiptLine,
            ) => {
                const salesReturnReceiptLine = await context.read(
                    xtremSales.nodes.SalesReturnReceiptLine,
                    {
                        _id: (await salesReturnRequestLineToSalesReturnReceiptLine.document)._id,
                    },
                    { forUpdate: false },
                );
                if (salesReturnReceiptLine && (await (await salesReturnReceiptLine.document).status) !== 'closed') {
                    isNotClosed = true;
                }
            },
        );
        return isNotClosed;
    }

    /**
     * Function that test linked sales return receipt before closing a sales return request
     * @param context
     * @param salesReturnRequestNumber
     * @returns linkedSalesReturnReceiptIsNotClosed or null
     */
    static async checkReturnReceiptHeaderStatusLinkedToReturnRequest(
        context: Context,
        salesReturnRequestNumber: string,
    ): Promise<xtremSales.enums.SalesReturnRequestCloseStatusMethodReturn | null> {
        const salesReturnRequest = await context.read(
            xtremSales.nodes.SalesReturnRequest,
            {
                number: salesReturnRequestNumber,
            },
            { forUpdate: true },
        );

        const isNotClosed: boolean[] = [];
        await salesReturnRequest.lines.forEach(async (line: xtremSales.nodes.SalesReturnRequestLine) => {
            const salesReturnRequestLineToSalesReturnReceiptLines = await context
                .query(xtremSales.nodes.SalesReturnRequestLineToSalesReturnReceiptLine, {
                    filter: {
                        linkedDocument: {
                            _id: line._id,
                        },
                    },
                })
                .toArray();
            isNotClosed.push(
                await SalesReturnRequest.salesReturnReceiptIsNotClosed(
                    context,
                    salesReturnRequestLineToSalesReturnReceiptLines,
                ),
            );
        });
        if (isNotClosed.includes(true)) {
            return 'linkedSalesReturnReceiptIsNotClosed';
        }
        return null;
    }

    /**
     * Function that test linked sales return receipt before closing a sales return request
     * @param context
     * @param salesReturnRequestLine
     * @returns linkedSalesReturnReceiptIsNotClosed or null
     */
    static async checkReturnReceiptLineHeaderStatusLinkedToReturnRequest(
        context: Context,
        salesReturnRequestLine: xtremSales.nodes.SalesReturnRequestLine,
    ): Promise<xtremSales.enums.SalesReturnRequestLineCloseStatusMethodReturn | null> {
        let isNotClosed = false;
        const salesReturnRequestLineToSalesReturnReceiptLines = await context
            .query(xtremSales.nodes.SalesReturnRequestLineToSalesReturnReceiptLine, {
                filter: {
                    linkedDocument: {
                        _id: salesReturnRequestLine._id,
                    },
                },
            })
            .toArray();
        isNotClosed = await SalesReturnRequest.salesReturnReceiptIsNotClosed(
            context,
            salesReturnRequestLineToSalesReturnReceiptLines,
        );
        if (isNotClosed) {
            return 'linkedSalesReturnReceiptIsNotClosed';
        }
        return null;
    }

    /**
     * Method that opens a sales return request
     * @param context
     * @param salesRequestReturnNumber the number of the sales return request
     * @returns enum SalesReturnRequestOpenStatusMethodReturn
     */
    @decorators.mutation<typeof SalesReturnRequest, 'setSalesReturnRequestOpenStatus'>({
        isPublished: true,
        parameters: [{ name: 'salesReturnRequestNumber', type: 'string', isMandatory: false }],
        return: {
            type: 'enum',
            dataType: () => xtremSales.enums.salesReturnRequestOpenStatusMethodReturnDataType,
        },
    })
    static async setSalesReturnRequestOpenStatus(
        context: Context,
        salesReturnRequestNumber: string,
    ): Promise<xtremSales.enums.SalesReturnRequestOpenStatusMethodReturn> {
        return (await xtremSales.functions.setSalesDocumentOpenStatus(
            context,
            salesReturnRequestNumber,
            xtremSales.nodes.SalesReturnRequest,
            xtremSales.enums.salesReturnRequestOpenStatusMethodReturnDataType,
            xtremSales.nodes.SalesReturnRequestLineToSalesReturnReceiptLine,
        )) as xtremSales.enums.SalesReturnRequestOpenStatusMethodReturn;
    }

    /**
     * Method that closes a sales order line of a sales order
     * @param context
     * @param salesReturnRequestLine contains a unique salesReturnRequestLine reference
     * @returns enum SalesOrderLineCloseStatusMethodReturn
     */
    @decorators.mutation<typeof SalesReturnRequest, 'setSalesReturnRequestLineCloseStatus'>({
        isPublished: true,
        parameters: [
            {
                name: 'salesReturnRequestLine',
                type: 'reference',
                node: () => xtremSales.nodes.SalesReturnRequestLine,
                isMandatory: true,
            },
        ],
        return: {
            type: 'enum',
            dataType: () => xtremSales.enums.salesReturnRequestLineCloseStatusMethodReturnDataType,
        },
    })
    static async setSalesReturnRequestLineCloseStatus(
        context: Context,
        salesReturnRequestLine: xtremSales.nodes.SalesReturnRequestLine,
    ): Promise<xtremSales.enums.SalesReturnRequestLineCloseStatusMethodReturn> {
        const isNotClosed = await SalesReturnRequest.checkReturnReceiptLineHeaderStatusLinkedToReturnRequest(
            context,
            salesReturnRequestLine,
        );
        if (isNotClosed) {
            return isNotClosed;
        }

        return (await xtremSales.functions.setSalesDocumentLineCloseStatus<xtremSales.nodes.SalesReturnRequestLine>(
            context,
            salesReturnRequestLine,
            xtremSales.nodes.SalesReturnRequest,
            xtremSales.enums.salesReturnRequestLineCloseStatusMethodReturnDataType,
        )) as xtremSales.enums.SalesReturnRequestLineCloseStatusMethodReturn;
    }

    /**
     * Method that opens a sales return request line of a sales return request
     * @param context
     * @param salesReturnRequestLine contains a unique salesReturnRequestLine reference
     * @param newQuantityInSalesUnit optional parameter that will update de quantity
     * of the line if it is greater than the actual quantity of the line and the quantity already delivered
     * @returns enum SalesReturnRequestLineOpenStatusMethodReturn
     */
    @decorators.mutation<typeof SalesReturnRequest, 'setSalesReturnRequestLineOpenStatus'>({
        isPublished: true,
        parameters: [
            {
                name: 'salesReturnRequestLine',
                type: 'reference',
                node: () => xtremSales.nodes.SalesReturnRequestLine,
                isMandatory: true,
            },
            { name: 'newQuantityInSalesUnit', type: 'decimal', isMandatory: false },
        ],
        return: {
            type: 'enum',
            dataType: () => xtremSales.enums.salesReturnRequestLineOpenStatusMethodReturnDataType,
        },
    })
    static async setSalesReturnRequestLineOpenStatus(
        context: Context,
        salesReturnRequestLine: xtremSales.nodes.SalesReturnRequestLine,
        newQuantityInSalesUnit?: decimal,
    ): Promise<xtremSales.enums.SalesReturnRequestLineOpenStatusMethodReturn> {
        return (await xtremSales.functions.setSalesDocumentLineOpenStatus<xtremSales.nodes.SalesReturnRequestLine>(
            context,
            salesReturnRequestLine,
            xtremSales.nodes.SalesReturnRequest,
            xtremSales.enums.salesReturnRequestLineOpenStatusMethodReturnDataType,
            xtremSales.nodes.SalesReturnRequestLineToSalesReturnReceiptLine,
            newQuantityInSalesUnit,
        )) as xtremSales.enums.SalesReturnRequestLineOpenStatusMethodReturn;
    }

    /**
     * This mutation validate quantity field
     * @param context
     * @param salesReturnRequestLine
     * @param newQuantityInSalesUnit
     */
    @decorators.mutation<typeof SalesReturnRequest, 'validateQuantityInSalesUnit'>({
        isPublished: true,
        parameters: [
            {
                name: 'salesReturnRequestLine',
                type: 'reference',
                node: () => xtremSales.nodes.SalesReturnRequestLine,
            },
            { name: 'newQuantityInSalesUnit', type: 'decimal' },
        ],
        return: {
            type: 'string',
        },
    })
    static async validateQuantityInSalesUnit(
        context: Context,
        salesReturnRequestLine: xtremSales.nodes.SalesReturnRequestLine,
        newQuantityInSalesUnit: decimal,
    ): Promise<string> {
        const errorMessage = await xtremSales.functions.SalesReturnRequestLib.controlQuantityInSalesUnit(
            context,
            salesReturnRequestLine,
            newQuantityInSalesUnit,
        );
        if (errorMessage) {
            throw new BusinessRuleError(errorMessage);
        }
        return '';
    }

    @decorators.stringPropertyOverride<SalesReturnRequest, 'page'>({
        getValue: () => '@sage/xtrem-sales/SalesReturnRequest',
    })
    override readonly page: Promise<string>;

    override beforeSendApprovalRequestMail(
        context: Context,
        user: xtremSystem.nodes.User,
    ): Promise<xtremMasterData.interfaces.ApprovalRequestMail> {
        return xtremSales.functions.getReturnRequestApprovalData(context, this, user);
    }

    @decorators.mutation<typeof SalesReturnRequest, 'approve'>({
        isPublished: true,
        parameters: [
            {
                name: 'returnRequest',
                type: 'reference',
                isMandatory: true,
                node: () => SalesReturnRequest,
            },
        ],
        return: {
            type: 'string',
        },
    })
    static async approve(context: Context, returnRequest: SalesReturnRequest): Promise<string> {
        const document = await SalesReturnRequest.getWritableNode(context, returnRequest);

        if ((await document.approvalStatus) !== 'pendingApproval' || (await document.status) === 'closed') {
            throw new BusinessRuleError(
                context.localize(
                    '@sage/xtrem-sales/cant_approve_return_request_when_not_pending_approval_or_closed',
                    'You can only approve a sales return request if the approval status is {{pendingApproval}}.',
                    {
                        pendingApproval: context.localizeEnumMember(
                            '@sage/xtrem-sales/SalesReturnRequestApprovalStatus',
                            'pendingApproval',
                        ),
                    },
                ),
            );
        }

        await document.$.set({ approvalStatus: 'approved', status: 'pending' });
        await document.lines.forEach(line => line.$.set({ status: 'pending' }));
        await document.$.save();

        return context.localize(
            '@sage/xtrem-sales/nodes__sales_return_request__approved',
            'The sales return request has been approved.',
        );
    }

    @decorators.mutation<typeof SalesReturnRequest, 'reject'>({
        isPublished: true,
        parameters: [
            {
                name: 'returnRequest',
                type: 'reference',
                isMandatory: true,
                node: () => SalesReturnRequest,
            },
        ],
        return: {
            type: 'string',
        },
    })
    static async reject(context: Context, returnRequest: SalesReturnRequest): Promise<string> {
        const document = await SalesReturnRequest.getWritableNode(context, returnRequest);

        if ((await document.approvalStatus) !== 'pendingApproval' || (await document.status) === 'closed') {
            throw new BusinessRuleError(
                context.localize(
                    '@sage/xtrem-sales/cant_reject_return_request_when_not_pending_approval_or_closed',
                    'You can only reject a sales return request if the approval status is {{pendingApproval}}.',
                    {
                        pendingApproval: context.localizeEnumMember(
                            '@sage/xtrem-sales/SalesReturnRequestApprovalStatus',
                            'pendingApproval',
                        ),
                    },
                ),
            );
        }

        await document.$.set({ approvalStatus: 'rejected' });
        await SalesReturnRequest.setSalesReturnRequestCloseStatus(context, await document.number);
        await document.$.save();

        return context.localize(
            '@sage/xtrem-sales/nodes__sales_return_request__rejected',
            'The sales return request has been rejected.',
        );
    }

    private static getWritableNode(context: Context, returnRequest: SalesReturnRequest): Promise<SalesReturnRequest> {
        return context.read(SalesReturnRequest, { _id: returnRequest._id }, { forUpdate: true });
    }

    /**
     * This method generates credit memo for a set of sales return requests not yet credited
     * (sales return request line creditStatus = 'notCredited').
     * @param context
     * @param salesReturnRequests
     */
    @decorators.mutation<typeof SalesReturnRequest, 'createSalesCreditMemosFromReturnRequests'>({
        isPublished: true,
        parameters: [
            {
                name: 'salesDocuments',
                type: 'array',
                item: {
                    type: 'reference',
                    node: () => xtremSales.nodes.SalesReturnRequest,
                },
            },
            {
                name: 'creditMemoDate',
                type: 'date',
                isMandatory: false,
            },
        ],
        return: {
            type: 'object',
            properties: {
                status: 'string',
                numberOfCreditMemos: 'integer',
                documentsCreated: {
                    type: 'array',
                    item: { type: 'reference', node: () => xtremSales.nodes.SalesCreditMemo },
                },
                lineErrors: {
                    type: 'array',
                    item: {
                        type: 'object',
                        properties: {
                            lineNumber: 'integer',
                            linePosition: 'integer',
                            message: 'string',
                        },
                    },
                },
            },
        },
        startsReadOnly: true,
    })
    static async createSalesCreditMemosFromReturnRequests(
        context: Context,
        salesDocuments: xtremSales.nodes.SalesReturnRequest[],
        creditMemoDate: date = date.today(),
    ): Promise<xtremSales.interfaces.CreateSalesOutputDocumentsReturnValues> {
        if (!salesDocuments.length) {
            return xtremSales.classes.SalesCreditMemosCreator.parametersAreIncorrect;
        }
        const salesCreditMemosCreator = this.instantiateSalesCreditMemosCreator(context, creditMemoDate);
        await asyncArray(salesDocuments).forEach(salesReturnRequest =>
            salesReturnRequest.lines.forEach(async line => {
                if (await line.isCreditingAllowed) {
                    await salesCreditMemosCreator.prepareNodeCreateData([{ salesDocumentLine: line }]);
                }
            }),
        );
        if (Object.keys(salesCreditMemosCreator.salesOutputDocuments).length !== 0) {
            return salesCreditMemosCreator.createSalesOutputDocuments();
        }
        return xtremSales.classes.SalesCreditMemosCreator.parametersAreIncorrect;
    }

    /**
     * This method generates credit memo for a set of sales return requests not yet credited
     * (sales return request line creditStatus = 'notCredited').
     * @param context
     * @param toReturnRequestLines
     */
    @decorators.mutation<typeof SalesReturnRequest, 'createSalesCreditMemosFromReturnRequestLines'>({
        isPublished: true,
        parameters: [
            {
                name: 'salesDocumentLines',
                type: 'array',
                item: {
                    type: 'object',
                    properties: {
                        salesDocumentLine: {
                            type: 'reference',
                            node: () => xtremSales.nodes.SalesReturnRequestLine,
                        },
                        quantityToProcess: {
                            type: 'decimal',
                            isMandatory: false,
                        },
                    },
                },
            },
            {
                name: 'creditMemoDate',
                type: 'date',
                isMandatory: false,
            },
        ],
        return: {
            type: 'object',
            properties: {
                status: 'string',
                numberOfCreditMemos: 'integer',
                documentsCreated: 'stringArray',
                lineErrors: {
                    type: 'array',
                    item: {
                        type: 'object',
                        properties: {
                            lineNumber: 'integer',
                            linePosition: 'integer',
                            message: 'string',
                        },
                    },
                },
            },
        },
        startsReadOnly: true,
    })
    static async createSalesCreditMemosFromReturnRequestLines(
        context: Context,
        salesDocumentLines: [
            { salesDocumentLine: xtremSales.nodes.SalesReturnRequestLine; quantityToProcess?: decimal },
        ],
        creditMemoDate: date = date.today(),
    ): Promise<xtremSales.interfaces.CreateSalesOutputDocumentsReturnValues> {
        if (!salesDocumentLines.length) {
            return xtremSales.classes.SalesCreditMemosCreator.parametersAreIncorrect;
        }
        return (
            await this.instantiateSalesCreditMemosCreator(context, creditMemoDate).prepareNodeCreateData(
                salesDocumentLines,
            )
        ).createSalesOutputDocuments();
    }

    static instantiateSalesCreditMemosCreator(
        context: Context,
        creditMemoDate: date = date.today(),
    ): xtremSales.classes.SalesCreditMemosCreator {
        return new xtremSales.classes.SalesCreditMemosCreator(context, creditMemoDate);
    }

    @decorators.mutation<typeof SalesReturnRequest, 'confirm'>({
        isPublished: true,
        parameters: [{ name: 'salesReturnRequestNumber', type: 'string', isMandatory: true }],
        return: 'boolean',
    })
    static async confirm(context: Context, salesReturnRequestNumber: string): Promise<boolean> {
        await xtremSales.functions.SalesReturnRequestLib.confirm(context, salesReturnRequestNumber);
        return true;
    }

    /**
     *  Gets array of filtered users
     * @param criteria search criteria
     * @returns array of users matching criteria
     */
    @decorators.query<typeof SalesReturnRequest, 'getFilteredUsers'>({
        isPublished: true,
        parameters: [
            {
                name: 'criteria',
                type: 'object',
                properties: {
                    userType: {
                        type: 'string',
                        isMandatory: true,
                    },
                    isApiUser: {
                        type: 'boolean',
                        isMandatory: false,
                    },
                    isActive: {
                        type: 'boolean',
                        isMandatory: false,
                    },
                    isOperatorUser: {
                        type: 'boolean',
                        isMandatory: false,
                    },
                },
                isMandatory: true,
            },
        ],
        return: {
            type: 'array',
            item: {
                type: 'object',
                properties: {
                    _id: 'string',
                    email: 'string',
                    firstName: 'string',
                    lastName: 'string',
                },
            },
            isMandatory: true,
        },
    })
    static getFilteredUsers(context: Context, criteria: UserSearchCriteria): Promise<FilteredUsers[]> {
        return context
            .query(xtremSystem.nodes.User, {
                filter: {
                    userType: criteria.userType,
                    isApiUser: criteria.isApiUser,
                    isActive: criteria.isActive,
                    isOperatorUser: criteria.isOperatorUser,
                },
            })
            .map(async (user: xtremSystem.nodes.User) => {
                return {
                    _id: String(user._id),
                    email: await user.email,
                    firstName: await user.firstName,
                    lastName: await user.lastName,
                };
            })
            .toArray();
    }

    async calculateIsCreditingAllowed(): Promise<boolean> {
        let isCreditingAllowed: boolean = false;
        await this.lines.forEach(async line => {
            if (
                ['approved', 'confirmed'].includes(await this.approvalStatus) &&
                (((await this.returnType) === 'creditMemo' &&
                    ['notCredited', 'partiallyCredited'].includes(await this.creditStatus)) ||
                    ((await this.returnType) === 'receiptAndCreditMemo' &&
                        (await line.receiptStatus) !== 'notReceived' &&
                        ((await line.creditStatus) === 'notCredited' ||
                            (await line.quantityReceiptInSalesUnit) >
                                (await line.quantityCreditedInProgressInSalesUnit) +
                                    (await line.quantityCreditedPostedInSalesUnit)))) &&
                !(await line.salesReturnReceiptLines.find(
                    async receiptLine => (await (await (await receiptLine.document)?.document)?.status) !== 'closed',
                ))
            ) {
                await line.$.set({ isCreditingAllowed: true });
                isCreditingAllowed = true;
            } else {
                await line.$.set({ isCreditingAllowed: false });
            }
        });
        return isCreditingAllowed;
    }

    @decorators.mutation<typeof SalesReturnRequest, 'financeIntegrationCheck'>({
        isPublished: true,
        parameters: [
            {
                name: 'salesReturnRequest',
                type: 'reference',
                isMandatory: true,
                isWritable: true,
                node: () => SalesReturnRequest,
            },
        ],
        return: {
            type: 'object',
            properties: {
                wasSuccessful: 'boolean',
                message: 'string',
            },
        },
    })
    static async financeIntegrationCheck(
        context: Context,
        salesReturnRequest: xtremSales.nodes.SalesReturnRequest,
    ): Promise<xtremFinanceData.interfaces.MutationResult> {
        let financeIntegrationCheckResult: xtremFinanceData.interfaces.MutationResult = {
            wasSuccessful: false,
            message: '',
        };

        const salesReturnReceiptCreator = new xtremSales.classes.SalesReturnReceiptCreator(context);

        await salesReturnRequest.lines.forEach(async line => {
            if ((await line.status) !== 'closed') {
                await salesReturnReceiptCreator.prepareNodeCreateData([
                    { salesDocumentLine: line, quantityToProcess: await line.quantityInStockUnit },
                ]);
            }
        });
        if (Object.keys(salesReturnReceiptCreator.salesOutputDocuments).length === 1) {
            const returnReceiptPayload =
                salesReturnReceiptCreator.salesOutputDocuments[
                    Object.keys(salesReturnReceiptCreator.salesOutputDocuments)[0]
                ];
            const returnReceipt: xtremSales.nodes.SalesReturnReceipt = await context.create(
                xtremSales.nodes.SalesReturnReceipt,
                returnReceiptPayload,
                { isTransient: true },
            );
            financeIntegrationCheckResult =
                await xtremSales.functions.FinanceIntegration.salesReturnReceiptControlAndCreateMutationResult(
                    context,
                    returnReceipt,
                );
            if (!financeIntegrationCheckResult.wasSuccessful) {
                return financeIntegrationCheckResult;
            }
        }

        const shipmentLines = await Promise.all(
            (await salesReturnRequest.lines.toArray()).map(async line =>
                (await line.salesShipmentLines.length) > 0
                    ? (await line.salesShipmentLines.elementAt(0))?.linkedDocument
                    : null,
            ),
        );
        const areAllShipmentLinesInvoiced = !shipmentLines.some(
            async line => line && (await line.invoiceStatus) !== 'invoiced',
        );
        if (
            ['receiptAndCreditMemo', 'creditMemo'].includes(await salesReturnRequest.returnType) &&
            areAllShipmentLinesInvoiced
        ) {
            const creditMemo = await getCreditMemoInstance(context, salesReturnRequest);
            if (creditMemo) {
                return xtremSales.nodes.SalesCreditMemo.financeIntegrationCheck(context, creditMemo);
            }
        }

        financeIntegrationCheckResult.wasSuccessful = true;
        return financeIntegrationCheckResult;
    }
}
