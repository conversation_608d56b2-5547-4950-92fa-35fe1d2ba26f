import type { NotificationEnvelope } from '@sage/xtrem-communication';
import type { Collection, Context, Reference, TextStream, decimal, integer } from '@sage/xtrem-core';
import {
    BusinessRuleError,
    Decimal,
    Logger,
    NodeStatus,
    asyncArray,
    date,
    decorators,
    useDefaultValue,
} from '@sage/xtrem-core';
import * as xtremDistribution from '@sage/xtrem-distribution';
import * as xtremFinanceData from '@sage/xtrem-finance-data';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremReporting from '@sage/xtrem-reporting';
import { SuspendException } from '@sage/xtrem-shared';
import * as xtremStockData from '@sage/xtrem-stock-data';
import * as xtremSystem from '@sage/xtrem-system';
import { isPostedErrorClosed } from '../functions/sales-document-common-lib';
import {
    calculateSalesOrderStatuses,
    controlAllocationRequestStatus,
    controlClosedStatus,
    controlInactiveItems,
    controlOrderToOrder,
    controlOrderToOrderQuantity,
    controlSalesOrderLinesAllocated,
    printSalesDocumentAndEmail,
    validateLineItemActive,
    validateOrderHasLines,
    validateTaxCalculation,
} from '../functions/sales-order-lib';
import * as xtremSales from '../index';

const logger = Logger.getLogger(__filename, 'sales-order');

@decorators.subNode<SalesOrder>({
    extends: () => xtremMasterData.nodes.BaseDocument,
    isPublished: true,
    canCreate: true,
    canRead: true,
    canUpdate: true,
    canDelete: true,
    canDuplicate: true,
    isCustomizable: true,
    hasAttachments: true,
    async prepareBegin() {
        await xtremSales.functions.salesDocumentTaxUpdate<
            xtremSales.nodes.SalesOrder,
            xtremSales.classes.SalesOrderTaxCalculator
        >(
            this,
            {
                customer: await this.billToCustomer,
                documentDate: await this.date,
                stockSite: await this.stockSite,
            },
            await xtremSales.classes.SalesOrderTaxCalculator.create(this.$.context, await this.site, await this.status),
        );
    },
    async controlBegin(cx) {
        await xtremSales.events.control.order.controlBegin(cx, this);

        if (this.$.status === NodeStatus.modified) {
            const old = await this.$.old;

            await xtremSales.events.control.order.salesSiteUpdateValidation(cx, this, old);
            await xtremSales.events.control.order.soldToCustomerUpdateValidation(cx, this, old);
            await xtremSales.events.control.order.currencyUpdateValidation(cx, this, old);
        }
    },
    async controlEnd(cx) {
        await cx.error
            .withMessage(
                '@sage/xtrem-sales/nodes__sales_order__lines_mandatory',
                'The sales order must contain at least one line.',
            )
            .if(await this.lines.length)
            .is.zero();
    },
    async saveBegin() {
        if (this.$.status === NodeStatus.modified) {
            const old = await this.$.old;

            await xtremSales.functions.fromQuoteToOrderUpdateLineStatus(this, old);
        }

        const statuses = await calculateSalesOrderStatuses(this.lines, await this.taxCalculationStatus);
        if (statuses?.invoiceStatus && statuses?.shippingStatus && statuses?.status && statuses?.displayStatus) {
            await this.$.set({
                invoiceStatus: statuses.invoiceStatus,
                shippingStatus: statuses.shippingStatus,
                status: statuses.status,
                displayStatus: statuses.displayStatus,
            });
        }

        // On control events this throws an error because the saleshipment node is readonly
        if (['pending', 'inProgress'].includes(await this.status)) {
            if (this.$.status === NodeStatus.added) {
                await this.$.context.flushDeferredActions();
            }
            const financeIntegrationCheckResult = await xtremSales.nodes.SalesOrder.financeIntegrationCheck(
                this.$.context,
                this,
            );
            if (financeIntegrationCheckResult.message) {
                throw new BusinessRuleError(financeIntegrationCheckResult.message);
            }
        }
    },
    async controlDelete(cx): Promise<void> {
        await xtremSales.events.control.order.controlDelete(cx, this);
    },
})
export class SalesOrder
    extends xtremMasterData.nodes.BaseDocument
    implements xtremMasterData.interfaces.Document, xtremStockData.interfaces.DocumentHeaderWithStockAllocation
{
    __skipPrepare: boolean;

    protected _canUpdateIsPrinted = false;

    __salesTaxCalculator: xtremSales.classes.SalesOrderTaxCalculator;

    skipIsOnHold = false;

    @decorators.stringProperty<SalesOrder, 'customerNumber'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremSales.dataTypes.propertyDataType,
        duplicatedValue: '',
    })
    readonly customerNumber: Promise<string | null>;

    @decorators.booleanProperty<SalesOrder, 'isQuote'>({
        isStored: true,
        isPublished: true,
        dependsOn: ['status'],
        duplicatedValue: true,
        defaultValue() {
            return xtremSales.functions.getIsQuoteValue(this);
        },
    })
    readonly isQuote: Promise<boolean>;

    @decorators.enumPropertyOverride<SalesOrder, 'status'>({
        defaultValue: 'quote',
        async control(cx, val) {
            await cx.error
                .withMessage('@sage/xtrem-sales/nodes_sales_on_hold_blocking', 'Bill to customer is on hold.')
                .if(
                    this.$.status === NodeStatus.modified &&
                        xtremSales.events.control.order.onHoldCustomerControl({
                            status: val,
                            prevStatus: await (await this.$.old).status,
                            isOnHold: await this.isOnHold,
                            customerOnHoldCheck: await (await (await this.site).legalCompany).customerOnHoldCheck,
                            skipIsOnHold: this.skipIsOnHold,
                        }),
                )
                .is.true();
        },
    })
    override readonly status: Promise<xtremSales.enums.SalesOrderStatus>;

    @decorators.enumPropertyOverride<SalesOrder, 'displayStatus'>({
        defaultValue: 'quote',
    })
    override readonly displayStatus: Promise<xtremSales.enums.SalesOrderDisplayStatus>;

    @decorators.enumProperty<SalesOrder, 'invoiceStatus'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremSales.enums.salesDocumentInvoiceStatusDataType,
        defaultValue: 'notInvoiced',
        duplicatedValue: useDefaultValue,
    })
    readonly invoiceStatus: Promise<xtremSales.enums.SalesDocumentInvoiceStatus>;

    /** deprecated */
    @decorators.dateProperty<SalesOrder, 'orderDate'>({
        isPublished: true,
        lookupAccess: true,
        getValue() {
            return this.date;
        },
    })
    readonly orderDate: Promise<date>;

    @decorators.datePropertyOverride<SalesOrder, 'date'>({
        control(cx, val) {
            if (val.compare(date.today()) > 0) {
                cx.error.addLocalized(
                    '@sage/xtrem-sales/nodes__sales_order__order_date_cannot_be_future',
                    'The order date cannot be later than today.',
                );
            }
        },
    })
    override readonly date: Promise<date>;

    @decorators.enumProperty<SalesOrder, 'taxEngine'>({
        isPublished: true,
        dataType: () => xtremFinanceData.enums.taxEngineDataType,
        async getValue() {
            // Note: site can be null when running a 'getDefaults' query
            return (await (await this.site).legalCompany).taxEngine;
        },
    })
    readonly taxEngine: Promise<xtremFinanceData.enums.TaxEngine | null>;

    @decorators.referenceProperty<SalesOrder, 'soldToCustomer'>({
        isStored: true,
        isPublished: true,
        isRequired: true,
        node: () => xtremMasterData.nodes.Customer,
        lookupAccess: true,
        async control(cx, val) {
            await xtremDistribution.events.DistributionDocument.controlSitesLegalCompanies(
                cx,
                this.$.context,
                await this.site,
                await val.businessEntity,
            );
        },
    })
    readonly soldToCustomer: Reference<xtremMasterData.nodes.Customer>;

    @decorators.referenceProperty<SalesOrder, 'soldToLinkedAddress'>({
        isStored: true,
        isPublished: true,
        isRequired: true,
        dependsOn: ['soldToCustomer'],
        node: () => xtremMasterData.nodes.BusinessEntityAddress,
        async defaultValue() {
            return (await this.soldToCustomer)?.primaryAddress;
        },
    })
    readonly soldToLinkedAddress: Reference<xtremMasterData.nodes.BusinessEntityAddress>;

    @decorators.referenceProperty<SalesOrder, 'soldToAddress'>({
        isPublished: true,
        isStored: true,
        isMutable: true,
        isNullable: true,
        dependsOn: ['soldToLinkedAddress'],
        node: () => xtremMasterData.nodes.Address,
        async defaultValue() {
            return xtremSales.functions.getSalesAddressDetail(await this.soldToLinkedAddress);
        },
        updatedValue: useDefaultValue,
    })
    readonly soldToAddress: Reference<xtremMasterData.nodes.Address | null>;

    @decorators.referenceProperty<SalesOrder, 'soldToContact'>({
        isPublished: true,
        isStored: true,
        isMutable: true,
        isNullable: true,
        dependsOn: ['soldToLinkedAddress'],
        node: () => xtremMasterData.nodes.Contact,
        async defaultValue() {
            return xtremSales.functions.getBusinessEntityContact(await this.soldToLinkedAddress);
        },
        updatedValue: useDefaultValue,
    })
    readonly soldToContact: Reference<xtremMasterData.nodes.Contact | null>;

    @decorators.referencePropertyOverride<SalesOrder, 'currency'>({
        /**
         * Sales order - currency property :
         * - has to be active
         */
        dependsOn: ['billToCustomer'],
        async defaultValue() {
            // Note: billToCustomer can be null when running a 'getDefaults' query
            return (await (await this.billToCustomer)?.businessEntity)?.currency;
        },
    })
    override readonly currency: Reference<xtremMasterData.nodes.Currency>;

    @decorators.decimalProperty<SalesOrder, 'companyFxRate'>({
        isStored: true,
        isPublished: true,
        isRequired: true,
        dataType: () => xtremMasterData.dataTypes.exchangeRate,
        dependsOn: ['currency', 'site', 'fxRateDate'],
        async defaultValue() {
            // Note: site can be null when running a 'getDefaults' query
            const rate = await xtremMasterData.functions.getRateOrReverseRate(
                this.$.context,
                await this.currency,
                await (
                    await (
                        await (
                            await this.site
                        )?.legalCompany
                    )?.currency
                )?.id,
                await this.fxRateDate,
            );
            return rate.isRateFound ? rate.rate : 0;
        },
        async control(cx, val) {
            await cx.error
                .withMessage('@sage/xtrem-sales/nodes__sales_order__fx_rate_not_found', 'No exchange rate found.')
                .if(val)
                .is.zero();
        },
        duplicatedValue: useDefaultValue,
    })
    readonly companyFxRate: Promise<decimal>;

    @decorators.stringProperty<SalesOrder, 'rateDescription'>({
        isPublished: true,
        dependsOn: ['companyFxRate', 'companyFxRateDivisor'],
        async computeValue() {
            return xtremMasterData.functions.rateDescription(
                await (
                    await this.currency
                ).id,
                await (
                    await (
                        await (
                            await this.site
                        ).legalCompany
                    ).currency
                ).id,
                await this.companyFxRate,
                await this.companyFxRateDivisor,
            );
        },
    })
    readonly rateDescription: Promise<string>;

    @decorators.decimalProperty<SalesOrder, 'companyFxRateDivisor'>({
        isStored: true,
        isPublished: true,
        isRequired: true,
        dataType: () => xtremMasterData.dataTypes.exchangeRate,
        dependsOn: ['currency', 'site', 'fxRateDate'],
        async defaultValue() {
            const rate = await xtremMasterData.functions.getRateOrReverseRate(
                this.$.context,
                await this.currency,
                await (
                    await (
                        await (
                            await this.site
                        ).legalCompany
                    ).currency
                ).id,
                await this.fxRateDate,
            );
            return rate.isRateFound ? rate.divisor : 0;
        },
    })
    readonly companyFxRateDivisor: Promise<decimal>;

    @decorators.dateProperty<SalesOrder, 'fxRateDate'>({
        isStored: true,
        isPublished: true,
        isRequired: true,
        dependsOn: ['date'],
        defaultValue() {
            return this.date;
        },
        duplicatedValue: useDefaultValue,
    })
    readonly fxRateDate: Promise<date>;

    @decorators.dateProperty<SalesOrder, 'requestedDeliveryDate'>({
        isStored: true,
        isPublished: true,
        isRequired: true,
        dependsOn: ['date'],
        duplicatedValue() {
            return this.date;
        },
    })
    readonly requestedDeliveryDate: Promise<date>;

    @decorators.dateProperty<SalesOrder, 'shippingDate'>({
        isPublished: true,
        isStored: true,
        isRequired: true,
        lookupAccess: true,
        dependsOn: [
            'requestedDeliveryDate',
            'date',
            'doNotShipBeforeDate',
            'doNotShipAfterDate',
            'deliveryLeadTime',
            'workDays',
        ],
        async defaultValue() {
            const doNotShipBeforeDate = await this.doNotShipBeforeDate;
            const doNotShipAfterDate = await this.doNotShipAfterDate;

            return xtremDistribution.functions.subWorkDays(this.$.context, {
                requestedDeliveryDate: await this.requestedDeliveryDate,
                orderDate: await this.date,
                doNotShipBeforeDate: doNotShipBeforeDate || null,
                doNotShipAfterDate: doNotShipAfterDate || null,
                deliveryLeadTime: await this.deliveryLeadTime,
                workDaysMask: await this.workDays,
            });
        },
        duplicatedValue: useDefaultValue,
    })
    readonly shippingDate: Promise<date>;

    @decorators.dateProperty<SalesOrder, 'doNotShipBeforeDate'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
        duplicatedValue: null,
    })
    readonly doNotShipBeforeDate: Promise<date | null>;

    @decorators.dateProperty<SalesOrder, 'doNotShipAfterDate'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
        duplicatedValue: null,
    })
    readonly doNotShipAfterDate: Promise<date | null>;

    @decorators.referenceProperty<SalesOrder, 'paymentTerm'>({
        isStored: true,
        isPublished: true,
        isRequired: true,
        lookupAccess: true,
        filters: { control: { businessEntityType: { _ne: 'supplier' } } },
        dependsOn: ['billToCustomer'],
        node: () => xtremMasterData.nodes.PaymentTerm,
        async defaultValue() {
            return (await this.billToCustomer).paymentTerm;
        },
    })
    readonly paymentTerm: Reference<xtremMasterData.nodes.PaymentTerm>;

    @decorators.booleanProperty<SalesOrder, 'isOnHold'>({
        isPublished: true,
        dependsOn: ['billToCustomer'],
        async getValue() {
            // Note: billToCustomer can be null when running a 'getDefaults' query
            return (await this.billToCustomer).isOnHold;
        },
    })
    readonly isOnHold: Promise<boolean>;

    // days availability showed as binary but saved as integer
    @decorators.integerProperty<SalesOrder, 'workDays'>({
        isPublished: true,
        excludedFromPayload: true,
        dependsOn: ['shipToCustomerAddress'],
        async computeValue() {
            const deliveryDetail = await (await this.shipToCustomerAddress).deliveryDetail;
            if (deliveryDetail) {
                return xtremDistribution.functions.computeWorkDays(deliveryDetail);
            }
            return 0;
        },
    })
    readonly workDays: Promise<integer>;

    @decorators.dateProperty<SalesOrder, 'expectedDeliveryDate'>({
        isStored: true,
        isPublished: true,
        isRequired: true,
        dependsOn: ['shippingDate', 'deliveryLeadTime', 'workDays'],
        async defaultValue() {
            return xtremDistribution.functions.addWorkDays(
                await this.shippingDate,
                await this.deliveryLeadTime,
                await this.workDays,
            );
        },
        duplicatedValue: useDefaultValue,
    })
    readonly expectedDeliveryDate: Promise<date>;

    @decorators.enumProperty<SalesOrder, 'shippingStatus'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremSales.enums.salesDocumentShippingStatusDataType,
        defaultValue: () => 'notShipped',
        duplicatedValue: useDefaultValue,
        lookupAccess: true,
    })
    readonly shippingStatus: Promise<xtremSales.enums.SalesDocumentShippingStatus>;

    /** deprecated */
    @decorators.referenceProperty<SalesOrder, 'salesSite'>({
        isPublished: true,
        node: () => xtremSystem.nodes.Site,
        getValue() {
            return this.site;
        },
    })
    readonly salesSite: Reference<xtremSystem.nodes.Site>;

    @decorators.referencePropertyOverride<SalesOrder, 'site'>({
        node: () => xtremSystem.nodes.Site,
        filters: { control: { isSales: true } },
    })
    override readonly site: Reference<xtremSystem.nodes.Site>;

    @decorators.referencePropertyOverride<SalesOrder, 'stockSite'>({
        node: () => xtremSystem.nodes.Site,
        dependsOn: ['site', 'shipToCustomerAddress'],
        defaultValue() {
            return xtremSales.functions.getStockSiteValue(this);
        },
        async control(cx, val) {
            await xtremSales.events.control.order.controlSite(
                cx,
                await (
                    await (
                        await this.site
                    ).legalCompany
                ).id,
                await (
                    await val.legalCompany
                ).id,
            );
        },
    })
    override readonly stockSite: Reference<xtremSystem.nodes.Site>;

    @decorators.referenceProperty<SalesOrder, 'shipToCustomer'>({
        isStored: true,
        isPublished: true,
        isRequired: true,
        lookupAccess: true,
        dependsOn: ['soldToCustomer'],
        node: () => xtremMasterData.nodes.Customer,
        defaultValue() {
            return this.soldToCustomer;
        },
    })
    readonly shipToCustomer: Reference<xtremMasterData.nodes.Customer>;

    @decorators.referenceProperty<SalesOrder, 'shipToCustomerAddress'>({
        isStored: true,
        isPublished: true,
        isRequired: true,
        lookupAccess: true,
        dependsOn: ['shipToCustomer'],
        node: () => xtremMasterData.nodes.BusinessEntityAddress,
        async defaultValue() {
            // Note: shipToCustomer can be null when running a 'getDefaults' query
            return (await this.shipToCustomer)?.primaryShipToAddress;
        },
        filters: {
            control: {
                async businessEntity() {
                    return (await this.shipToCustomer).businessEntity;
                },
                deliveryDetail: { _ne: null },
            },
        },
    })
    readonly shipToCustomerAddress: Reference<xtremMasterData.nodes.BusinessEntityAddress>;

    @decorators.referenceProperty<SalesOrder, 'shipToAddress'>({
        isPublished: true,
        isStored: true,
        isMutable: true,
        isNullable: true,
        lookupAccess: true,
        dependsOn: ['shipToCustomerAddress'],
        node: () => xtremMasterData.nodes.Address,
        async defaultValue() {
            // Note: shipToCustomerAddress can be null when running a 'getDefaults' query
            return (await this.shipToCustomerAddress)?.address;
        },
        updatedValue: useDefaultValue,
    })
    readonly shipToAddress: Reference<xtremMasterData.nodes.Address | null>;

    @decorators.referenceProperty<SalesOrder, 'shipToContact'>({
        isPublished: true,
        isStored: true,
        isMutable: true,
        isNullable: true,
        dependsOn: ['shipToCustomerAddress'],
        node: () => xtremMasterData.nodes.Contact,
        async defaultValue() {
            // Note: shipToCustomerAddress can be null when running a 'getDefaults' query
            return (await (await this.shipToCustomerAddress)?.primaryContact)?.contact ?? null;
        },
        updatedValue: useDefaultValue,
    })
    readonly shipToContact: Reference<xtremMasterData.nodes.Contact | null>;

    @decorators.referenceProperty<SalesOrder, 'billToCustomer'>({
        isStored: true,
        isPublished: true,
        isRequired: true,
        dependsOn: ['soldToCustomer'],
        lookupAccess: true,
        node: () => xtremMasterData.nodes.Customer,
        isFrozen: true,
        async defaultValue() {
            // Note: soldToCustomer can be null when running a 'getDefaults' query
            const billToCustomer = await (await this.soldToCustomer)?.billToCustomer;
            return billToCustomer || this.soldToCustomer;
        },
    })
    readonly billToCustomer: Reference<xtremMasterData.nodes.Customer>;

    @decorators.referenceProperty<SalesOrder, 'billToLinkedAddress'>({
        isStored: true,
        isPublished: true,
        isRequired: true,
        lookupAccess: true,
        dependsOn: ['soldToCustomer', 'billToCustomer'],
        node: () => xtremMasterData.nodes.BusinessEntityAddress,
        defaultValue() {
            return this.getBillToAddressValue();
        },
    })
    readonly billToLinkedAddress: Reference<xtremMasterData.nodes.BusinessEntityAddress>;

    async getBillToAddressValue(): Promise<xtremMasterData.nodes.BusinessEntityAddress | null> {
        const billToCustomer = await this.billToCustomer;
        const shipToCustomer = await this.shipToCustomer;
        if (!shipToCustomer || !billToCustomer) {
            return null;
        }
        const billToLinkedAddress = await shipToCustomer.billToAddress;
        if (billToCustomer === (await shipToCustomer.billToCustomer) && billToLinkedAddress) {
            return billToLinkedAddress;
        }
        return billToCustomer.primaryAddress;
    }

    @decorators.referenceProperty<SalesOrder, 'billToAddress'>({
        isPublished: true,
        isStored: true,
        isMutable: true,
        isNullable: true,
        lookupAccess: true,
        dependsOn: ['billToLinkedAddress'],
        node: () => xtremMasterData.nodes.Address,
        async defaultValue() {
            // Note: billToLinkedAddress can be null when running a 'getDefaults' query
            return (await this.billToLinkedAddress)?.address;
        },
        updatedValue: useDefaultValue,
    })
    readonly billToAddress: Reference<xtremMasterData.nodes.Address | null>;

    @decorators.referenceProperty<SalesOrder, 'billToContact'>({
        isPublished: true,
        isStored: true,
        isMutable: true,
        isNullable: true,
        dependsOn: ['billToLinkedAddress'],
        node: () => xtremMasterData.nodes.Contact,
        async defaultValue() {
            // Note: billToLinkedAddress can be null when running a 'getDefaults' query
            return (await (await this.billToLinkedAddress)?.primaryContact)?.contact ?? null;
        },
        updatedValue: useDefaultValue,
    })
    readonly billToContact: Reference<xtremMasterData.nodes.Contact | null>;

    @decorators.referenceProperty<SalesOrder, 'incoterm'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
        lookupAccess: true,
        dependsOn: ['shipToCustomerAddress'],
        node: () => xtremMasterData.nodes.Incoterm,
        async defaultValue() {
            return (await (await this.shipToCustomerAddress)?.deliveryDetail)?.incoterm || null;
        },
    })
    readonly incoterm: Reference<xtremMasterData.nodes.Incoterm | null>;

    @decorators.referenceProperty<SalesOrder, 'deliveryMode'>({
        isStored: true,
        isPublished: true,
        isRequired: true,
        dependsOn: ['shipToCustomerAddress'],
        node: () => xtremMasterData.nodes.DeliveryMode,
        async defaultValue() {
            return (await (await this.shipToCustomerAddress)?.deliveryDetail)?.mode ?? null;
        },
    })
    readonly deliveryMode: Reference<xtremMasterData.nodes.DeliveryMode>;

    // this field represents number of days.
    @decorators.integerProperty<SalesOrder, 'deliveryLeadTime'>({
        isStored: true,
        isPublished: true,
        dependsOn: ['shipToCustomerAddress'],
        lookupAccess: true,
        async defaultValue() {
            return (await (await this.shipToCustomerAddress)?.deliveryDetail)?.leadTime || 0;
        },
    })
    readonly deliveryLeadTime: Promise<integer>;

    @decorators.booleanProperty<SalesOrder, 'isOrderAssignmentLinked'>({
        isPublished: true,
        defaultValue: false,
        serviceOptions: () => [xtremMasterData.serviceOptions.orderToOrderOption],
        // eslint-disable-next-line @sage/xtrem/property-decorators-errors
        computeValue() {
            return this.lines.some(async line => (await line.uAssignmentOrder) !== '');
        },
    })
    readonly isOrderAssignmentLinked: Promise<boolean>;

    @decorators.collectionPropertyOverride<SalesOrder, 'lines'>({
        dependsOn: [
            'shipToCustomerAddress',
            'shipToAddress',
            'site',
            'soldToCustomer',
            'deliveryMode',
            'requestedDeliveryDate',
            'deliveryLeadTime',
            'doNotShipBeforeDate',
            'doNotShipAfterDate',
            'date',
            'workDays',
            'currency',
        ],
        node: () => xtremSales.nodes.SalesOrderLine,
    })
    override readonly lines: Collection<xtremSales.nodes.SalesOrderLine>;

    @decorators.decimalProperty<SalesOrder, 'totalAmountExcludingTax'>({
        isStoredOutput: true,
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.priceInSalesPrice,
        dependsOn: [{ lines: ['amountExcludingTax'] }],
        defaultValue() {
            return this.calculateTaxExcludedTotalAmount();
        },
        updatedValue: useDefaultValue,
    })
    readonly totalAmountExcludingTax: Promise<decimal>;

    @decorators.decimalProperty<SalesOrder, 'totalTaxAmount'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.priceInSalesPrice,
        dependsOn: ['currency'],
        async control(cx, val) {
            await cx.error.if(val).is.negative();
        },
    })
    readonly totalTaxAmount: Promise<decimal>;

    @decorators.decimalProperty<SalesOrder, 'remainingTotalAmountToShipExcludingTax'>({
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.priceInSalesPrice,
        dependsOn: [{ lines: ['remainingAmountToShipExcludingTax'] }],
        cacheComputedValue: true,
        getValue() {
            return this.lines.sum(line => line.remainingAmountToShipExcludingTax);
        },
    })
    readonly remainingTotalAmountToShipExcludingTax: Promise<decimal>;

    @decorators.decimalProperty<SalesOrder, 'remainingTotalAmountExcludingTaxInCompanyCurrency'>({
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.priceInSalesPrice,
        dependsOn: [{ lines: ['remainingAmountToShipExcludingTaxInCompanyCurrency'] }],
        cacheComputedValue: true,
        getValue() {
            return this.lines.sum(line => line.remainingAmountToShipExcludingTaxInCompanyCurrency);
        },
    })
    readonly remainingTotalAmountExcludingTaxInCompanyCurrency: Promise<decimal>;

    @decorators.decimalProperty<SalesOrder, 'totalTaxableAmount'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.priceInSalesPrice,
        dependsOn: ['currency'],
        async control(cx, val) {
            await cx.error.if(val).is.negative();
        },
    })
    readonly totalTaxableAmount: Promise<decimal>;

    @decorators.decimalProperty<SalesOrder, 'totalExemptAmount'>({
        isStored: true,
        isPublished: true,
        dependsOn: ['currency'],
        dataType: () => xtremMasterData.dataTypes.priceInSalesPrice,
        async control(cx, val) {
            await cx.error.if(val).is.negative();
        },
    })
    readonly totalExemptAmount: Promise<decimal>;

    @decorators.enumProperty<SalesOrder, 'taxCalculationStatus'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremMasterData.enums.taxCalculationStatusDataType,
        defaultValue: 'notDone',
        lookupAccess: true,
    })
    readonly taxCalculationStatus: Promise<xtremMasterData.enums.TaxCalculationStatus>;

    @decorators.decimalProperty<SalesOrder, 'totalAmountIncludingTax'>({
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.priceInSalesPrice,
        async control(cx, val) {
            await cx.error.if(val).is.negative();
        },
        dependsOn: ['currency', 'totalAmountExcludingTax', 'totalTaxAmountAdjusted'],
        cacheComputedValue: true,
        async getValue() {
            return Decimal.roundAt(
                (await this.totalAmountExcludingTax) + (await this.totalTaxAmountAdjusted),
                await (
                    await this.currency
                ).decimalDigits,
            );
        },
    })
    readonly totalAmountIncludingTax: Promise<decimal>;

    @decorators.decimalProperty<SalesOrder, 'totalAmountIncludingTaxInCompanyCurrency'>({
        isPublished: true,
        dataType: () => xtremSales.dataTypes.salesAmountDataType, // TODO: Has to have a computed length depending of the currency decimals !!!
        dependsOn: ['currency', { lines: ['amountIncludingTaxInCompanyCurrency'] }],
        cacheComputedValue: true,
        async getValue() {
            return Decimal.roundAt(
                await this.lines.sum(line => line.amountIncludingTaxInCompanyCurrency),
                await (
                    await (
                        await (
                            await this.site
                        ).legalCompany
                    ).currency
                ).decimalDigits,
            );
        },
    })
    readonly totalAmountIncludingTaxInCompanyCurrency: Promise<decimal>;

    @decorators.decimalProperty<SalesOrder, 'totalTaxAmountAdjusted'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.priceDataType,
        dependsOn: ['currency'],
        async control(cx, val) {
            await cx.error.if(val).is.negative();
        },
    })
    readonly totalTaxAmountAdjusted: Promise<decimal>;

    @decorators.decimalProperty<SalesOrder, 'totalAmountExcludingTaxInCompanyCurrency'>({
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.priceInSalesPrice, // TODO: Has to have a computed length depending of the currency decimals !!!
        dependsOn: [{ lines: ['amountExcludingTaxInCompanyCurrency'] }], // ,'status', 'receiptStatus'],
        cacheComputedValue: true,
        async getValue() {
            return Decimal.roundAt(
                await this.lines.sum(line => line.amountExcludingTaxInCompanyCurrency),
                await (
                    await this.currency
                ).decimalDigits,
            );
        },
    })
    readonly totalAmountExcludingTaxInCompanyCurrency: Promise<decimal>;

    @decorators.collectionProperty<SalesOrder, 'taxes'>({
        isVital: true,
        isPublished: true,
        reverseReference: 'document',
        dependsOn: ['currency', 'status'],
        async isFrozen() {
            return (await this.status) === 'closed';
        },
        node: () => xtremSales.nodes.SalesOrderTax,
    })
    readonly taxes: Collection<xtremSales.nodes.SalesOrderTax>;

    readonly allocableLinesCollectionName = 'lines';

    @decorators.enumProperty<SalesOrder, 'allocationStatus'>({
        isPublished: true,
        dependsOn: [{ lines: ['allocationStatus'] }],
        dataType: () => xtremStockData.enums.stockAllocationStatusDataType,
        cacheComputedValue: true,
        computeValue() {
            return xtremStockData.functions.allocationLib.computeHeaderAllocationStatus(
                this,
                xtremSales.functions.isSalesOrderLineConsideredForAllocationStatus,
            );
        },
    })
    readonly allocationStatus: Promise<xtremStockData.enums.StockAllocationStatus>;

    @decorators.enumProperty<SalesOrder, 'allocationRequestStatus'>({
        isPublished: true,
        dataType: () => xtremStockData.enums.allocationRequestStatusDataType,
        dependsOn: [{ lines: ['allocationRequestStatus'] }],
        computeValue() {
            return xtremStockData.functions.automaticAllocationLib.computeHeaderAllocationRequestStatus(this);
        },
    })
    readonly allocationRequestStatus: Promise<xtremStockData.enums.AllocationRequestStatus>;

    @decorators.textStreamPropertyOverride<SalesOrder, 'internalNote'>({
        dependsOn: ['soldToCustomer'],
        async defaultValue() {
            // Note: soldToCustomer can be null when running a 'getDefaults' query
            return (await this.soldToCustomer)?.internalNote;
        },
        async isFrozen() {
            return isPostedErrorClosed(await this.status);
        },
    })
    override readonly internalNote: Promise<TextStream>;

    @decorators.textStreamPropertyOverride<SalesOrder, 'externalNote'>({
        async isFrozen() {
            return isPostedErrorClosed(await this.status);
        },
    })
    override readonly externalNote: Promise<TextStream>;

    /** to be sure the user know that externalNode will go to external documents */
    @decorators.booleanPropertyOverride<SalesOrder, 'isExternalNote'>({
        async isFrozen() {
            return isPostedErrorClosed(await this.status);
        },
    })
    override readonly isExternalNote: Promise<boolean>;

    @decorators.decimalProperty<SalesOrder, 'totalGrossProfit'>({
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.priceInSalesPrice,
        dependsOn: [{ lines: ['grossProfitAmount'] }],
        cacheComputedValue: true,
        getValue() {
            return this.lines.sum(line => line.grossProfitAmount);
        },
    })
    readonly totalGrossProfit: Promise<decimal>;

    @decorators.booleanPropertyOverride<SalesOrder, 'isTransferHeaderNote'>({
        async isFrozen() {
            return isPostedErrorClosed(await this.status);
        },
    })
    override readonly isTransferHeaderNote: Promise<boolean>;

    @decorators.booleanPropertyOverride<SalesOrder, 'isTransferLineNote'>({
        async isFrozen() {
            return isPostedErrorClosed(await this.status);
        },
    })
    override readonly isTransferLineNote: Promise<boolean>;

    @decorators.booleanProperty<SalesOrder, 'isCloseHidden'>({
        isPublished: true,
        cacheComputedValue: true,
        dependsOn: ['status', 'allocationRequestStatus'],
        // eslint-disable-next-line @sage/xtrem/property-decorators-errors
        async computeValue() {
            return (await this.status) === 'closed' || (await this.allocationRequestStatus) === 'inProgress';
        },
    })
    readonly isCloseHidden: Promise<boolean>;

    @decorators.stringProperty<SalesOrder, 'recordUrl'>({
        isPublished: true,
        computeValue() {
            /**
             * Url to the Sales order page for the current record,
             * initially used for copilot, for other nodes we are implementing this property try to use
             * same property naming convention (recordUrl)
             */
            return xtremSystem.functions.getRecordUrl(this, '@sage/xtrem-sales/SalesOrder');
        },
    })
    readonly recordUrl: Promise<string>;

    @decorators.collectionProperty<SalesOrder, 'proformaInvoices'>({
        isPublished: true,
        reverseReference: 'salesOrder',
        node: () => xtremSales.nodes.ProformaInvoice,
        getFilter() {
            return {
                salesOrder: this._id,
            };
        },
        orderBy: { version: -1 },
    })
    readonly proformaInvoices: Collection<xtremSales.nodes.ProformaInvoice> | null;

    /**
     * This method generates shipments for a set of sales order having lines with a remaining quantity to ship
     * (sales order line quantity - sum(already shipped quantity) in linked node).
     * The sales order lines cannot be 'Quote' or 'Closed'
     * The sales order line properties doNotShipBefore and doNotShipAfter are controlled.
     * The shipping date can't be lower than the doNotShipBefore and can't be higher than the doNotShipAfter dates.
     * @param context
     * @param salesOrderLines
     */
    @decorators.mutation<typeof SalesOrder, 'createSalesShipmentsFromOrderLines'>({
        isPublished: true,
        parameters: [
            {
                name: 'salesDocumentLines',
                type: 'array',
                item: {
                    type: 'object',
                    properties: {
                        salesDocumentLine: { type: 'reference', node: () => xtremSales.nodes.SalesOrderLine },
                        quantityToProcess: { type: 'decimal', isMandatory: false },
                    },
                },
            },
            {
                name: 'processOptions',
                type: 'object',
                isMandatory: false,
                properties: {
                    isForFinanceCheck: { type: 'boolean', isMandatory: false },
                    processAllShippableLines: { type: 'boolean', isMandatory: false },
                },
            },
        ],
        return: {
            type: 'object',
            properties: {
                status: 'string',
                numberOfShipments: 'integer',
                documentsCreated: {
                    type: 'array',
                    item: { type: 'reference', node: () => xtremSales.nodes.SalesShipment },
                },
                lineErrors: {
                    type: 'array',
                    item: {
                        type: 'object',
                        properties: { lineNumber: 'integer', linePosition: 'integer', message: 'string' },
                    },
                },
            },
        },
        startsReadOnly: true,
    })
    static async createSalesShipmentsFromOrderLines(
        context: Context,
        salesDocumentLines: { salesDocumentLine: xtremSales.nodes.SalesOrderLine; quantityToProcess?: decimal }[],
        processOptions?: xtremSales.sharedFunctions.PrepareNodeCreateDataOptions,
    ): Promise<xtremSales.interfaces.CreateSalesOutputDocumentsReturnValues> {
        if (!salesDocumentLines.length) {
            return xtremSales.classes.SalesShipmentsCreator.parametersAreIncorrect;
        }
        return (
            await this.instantiateSalesShipmentsCreator(context).prepareNodeCreateData(
                salesDocumentLines,
                processOptions,
            )
        ).createSalesOutputDocuments();
    }

    @decorators.mutation<typeof SalesOrder, 'createSalesShipmentsFromOrders'>({
        isPublished: true,
        parameters: [
            {
                name: 'salesDocuments',
                type: 'array',
                item: { type: 'reference', node: () => xtremSales.nodes.SalesOrder },
            },
            {
                name: 'processOptions',
                type: 'object',
                isMandatory: false,
                properties: {
                    isForFinanceCheck: { type: 'boolean', isMandatory: false },
                    processAllShippableLines: { type: 'boolean', isMandatory: false },
                },
            },
        ],
        return: {
            type: 'object',
            properties: {
                status: 'string',
                numberOfShipments: 'integer',
                documentsCreated: {
                    type: 'array',
                    item: { type: 'reference', node: () => xtremSales.nodes.SalesShipment },
                },
                lineErrors: {
                    type: 'array',
                    item: {
                        type: 'object',
                        properties: { lineNumber: 'integer', linePosition: 'integer', message: 'string' },
                    },
                },
            },
        },
        startsReadOnly: true,
    })
    static async createSalesShipmentsFromOrders(
        context: Context,
        salesDocuments: xtremSales.nodes.SalesOrder[],
        processOptions?: xtremSales.sharedFunctions.PrepareNodeCreateDataOptions,
    ): Promise<xtremSales.interfaces.CreateSalesOutputDocumentsReturnValues> {
        if (!salesDocuments.length) {
            return xtremSales.classes.SalesShipmentsCreator.parametersAreIncorrect;
        }
        await asyncArray(salesDocuments).forEach(async salesOrder => {
            if (await salesOrder.lines.find(async line => (await (await line.item).status) !== 'active')) {
                throw new BusinessRuleError(
                    context.localize(
                        '@sage/xtrem-sales/nodes__sales_order__cannot_create_shipment',
                        'You need to remove the inactive items before you change the document status.',
                    ),
                );
            }
        });
        const salesShipmentsCreator = this.instantiateSalesShipmentsCreator(context);
        await asyncArray(salesDocuments).forEach(salesOrder =>
            salesOrder.lines.forEach(async salesDocumentLine => {
                await salesShipmentsCreator.prepareNodeCreateData([{ salesDocumentLine }], processOptions);
            }),
        );
        if (Object.keys(salesShipmentsCreator.salesOutputDocuments).length !== 0) {
            return salesShipmentsCreator.createSalesOutputDocuments();
        }
        return {
            ...xtremSales.classes.SalesShipmentsCreator.parametersAreIncorrect,
            lineErrors: salesShipmentsCreator.lineErrors,
        };
    }

    /**
     * Used on sales-order page
     * This method generates shipments for a set of sales order
     * @param context
     * @param salesOrders
     */
    @decorators.mutation<typeof SalesOrder, 'createShipmentsFromOrder'>({
        isPublished: true,
        startsReadOnly: true,
        parameters: [
            { name: 'salesOrder', type: 'reference', node: () => xtremSales.nodes.SalesOrder, isMandatory: true },
            { name: 'isSafeToRetry', type: 'boolean' },
            { name: 'controlOrderLinks', type: 'boolean' },
        ],
        return: {
            type: 'array',
            item: { type: 'reference', node: () => xtremSales.nodes.SalesShipment },
        },
    })
    static async createShipmentsFromOrder(
        context: Context,
        salesOrder: xtremSales.nodes.SalesOrder,
        isSafeToRetry = false,
        controlOrderLinks = false,
    ): Promise<xtremSales.nodes.SalesShipment[]> {
        // Initial check
        await SalesOrder.controlCreateShipmentsFromOrder(salesOrder, controlOrderLinks);

        return xtremSales.functions.createSalesShipment(context, salesOrder, isSafeToRetry);
    }

    static instantiateSalesShipmentsCreator(context: Context): xtremSales.classes.SalesShipmentsCreator {
        return new xtremSales.classes.SalesShipmentsCreator(context);
    }

    /**
     * Method that closes a sales order line of a sales order
     * @param context
     * @param salesOrderLines contains a unique salesOrderLine reference
     * @returns enum SalesOrderLineCloseStatusMethodReturn
     */
    @decorators.mutation<typeof SalesOrder, 'setSalesOrderLineCloseStatus'>({
        isPublished: true,
        parameters: [
            {
                name: 'salesOrderLine',
                type: 'reference',
                node: () => xtremSales.nodes.SalesOrderLine,
                isMandatory: true,
            },
        ],
        return: {
            type: 'enum',
            dataType: () => xtremSales.enums.salesOrderLineCloseStatusMethodReturnDataType,
        },
    })
    static async setSalesOrderLineCloseStatus(
        context: Context,
        salesOrderLine: xtremSales.nodes.SalesOrderLine,
    ): Promise<xtremSales.enums.SalesOrderLineCloseStatusMethodReturn> {
        if (
            (await (await salesOrderLine.item).isStockManaged) &&
            (await salesOrderLine.allocationStatus) !== 'notAllocated'
        ) {
            throw new BusinessRuleError(
                context.localize(
                    '@sage/xtrem-sales/nodes__sales_order__cannot_close_line_quantity_allocated',
                    'Remove the stock allocation before closing the line.',
                ),
            );
        }
        if ((await salesOrderLine.allocationRequestStatus) === 'inProgress') {
            throw new BusinessRuleError(
                context.localize(
                    '@sage/xtrem-sales/nodes__sales_order__cannot_close_line_allocated_request_in_progress',
                    'You cannot close the sales order line. An allocation request is in progress.',
                ),
            );
        }

        return (await xtremSales.functions.setSalesDocumentLineCloseStatus<xtremSales.nodes.SalesOrderLine>(
            context,
            salesOrderLine,
            xtremSales.nodes.SalesOrder,
            xtremSales.enums.salesOrderLineCloseStatusMethodReturnDataType,
        )) as xtremSales.enums.SalesOrderLineCloseStatusMethodReturn;
    }

    /**
     * Method that opens a sales order line of a sales order
     * @param context
     * @param salesOrderLine contains a unique salesOrderLine reference
     * @param newQuantityInSalesUnit optional parameter that will update de quantity
     * of the line if it is greater than the actual quantity of the line and the quantity already delivered
     * @returns enum SalesOrderLineOpenStatusMethodReturn
     */
    @decorators.mutation<typeof SalesOrder, 'setSalesOrderLineOpenStatus'>({
        isPublished: true,
        parameters: [
            {
                name: 'salesOrderLine',
                type: 'reference',
                node: () => xtremSales.nodes.SalesOrderLine,
                isMandatory: true,
            },
            { name: 'newQuantityInSalesUnit', type: 'decimal', isMandatory: false },
        ],
        return: {
            type: 'enum',
            dataType: () => xtremSales.enums.salesOrderLineOpenStatusMethodReturnDataType,
        },
    })
    static async setSalesOrderLineOpenStatus(
        context: Context,
        salesOrderLine: xtremSales.nodes.SalesOrderLine,
        newQuantityInSalesUnit?: decimal,
    ): Promise<xtremSales.enums.SalesOrderLineOpenStatusMethodReturn> {
        return (await xtremSales.functions.setSalesDocumentLineOpenStatus<xtremSales.nodes.SalesOrderLine>(
            context,
            salesOrderLine,
            xtremSales.nodes.SalesOrder,
            xtremSales.enums.salesOrderLineOpenStatusMethodReturnDataType,
            xtremSales.nodes.SalesOrderLineToSalesShipmentLine,
            newQuantityInSalesUnit,
        )) as xtremSales.enums.SalesOrderLineOpenStatusMethodReturn;
    }

    static async controlCreateShipmentsFromOrder(
        salesOrder: xtremSales.nodes.SalesOrder,
        controlOrderLinks: boolean,
    ): Promise<void> {
        if (controlOrderLinks) {
            await controlOrderToOrderQuantity(salesOrder);
        }
        await controlInactiveItems(salesOrder);
    }

    static async controlSalesOrderClose(
        salesOrder: xtremSales.nodes.SalesOrder,
        isSafeToRetry: boolean,
        controlOrderLinks: boolean,
    ): Promise<boolean> {
        const generalControl =
            (await controlSalesOrderLinesAllocated(salesOrder, isSafeToRetry)) &&
            (await controlAllocationRequestStatus(salesOrder, isSafeToRetry)) &&
            controlClosedStatus(salesOrder, isSafeToRetry);
        return controlOrderLinks
            ? (await controlOrderToOrder(salesOrder, isSafeToRetry)) && generalControl
            : generalControl;
    }

    /**
     * Method that closes a sales order
     * @param context
     * @param salesOrderNumber the number of the sales order
     * @returns enum salesOrderCloseStatusMethod
     */
    @decorators.mutation<typeof SalesOrder, 'closeSalesOrder'>({
        isPublished: true,
        parameters: [
            {
                name: 'salesOrder',
                type: 'reference',
                node: () => xtremSales.nodes.SalesOrder,
                isMandatory: true,
                isWritable: true,
            },
            { name: 'isSafeToRetry', type: 'boolean' },
            { name: 'controlOrderLinks', type: 'boolean' },
        ],
        return: { type: 'reference', node: () => xtremSales.nodes.SalesOrder },
    })
    static async closeSalesOrder(
        _context: Context,
        salesOrder: xtremSales.nodes.SalesOrder,
        isSafeToRetry = false,
        controlOrderLinks = false,
    ): Promise<xtremSales.nodes.SalesOrder> {
        if (!(await SalesOrder.controlSalesOrderClose(salesOrder, isSafeToRetry, controlOrderLinks))) {
            return salesOrder;
        }

        await salesOrder.lines.forEach(async line => {
            if ((await line.status) !== 'closed') {
                await line.$.set({ status: 'closed' });
            }
        });
        await salesOrder.$.set({ status: 'closed' });
        await salesOrder.$.save();

        return salesOrder;
    }

    @decorators.query<typeof SalesOrder, 'orderAssignmentQuantitySum'>({
        isPublished: true,
        parameters: [
            {
                name: 'salesOrder',
                type: 'reference',
                node: () => xtremSales.nodes.SalesOrder,
                isMandatory: true,
            },
        ],
        return: {
            type: 'object',
            properties: {
                sumActualQuantity: { type: 'decimal' },
                sumQuantityInStockUnit: { type: 'decimal' },
                filteredAssignedOrders: {
                    type: 'array',
                    item: {
                        type: 'object',
                        properties: {
                            line: { type: 'reference', node: () => xtremSales.nodes.SalesOrderLine },
                            orderAssignments: {
                                type: 'array',
                                item: { type: 'reference', node: () => xtremStockData.nodes.OrderAssignment },
                                isNullable: true,
                            },
                        },
                    },
                },
            },
        },
    })
    static orderAssignmentQuantitySum(
        context: Context,
        salesOrder: xtremSales.nodes.SalesOrder,
    ): Promise<xtremSales.interfaces.AssignedOrdersSum> {
        const assignedOrders = salesOrder.lines.map(async line => {
            const assignedOrder = await xtremSales.nodes.SalesOrderLine.assignedOrderAssignment(
                context,
                line,
                undefined,
                undefined,
            );
            return assignedOrder ?? { line, orderAssignments: null };
        });
        return assignedOrders.reduce<xtremSales.interfaces.AssignedOrdersSum>(
            async (acc, { line, orderAssignments }) => {
                if (
                    orderAssignments !== null &&
                    (await line.quantityAllocated) < (await line.remainingQuantityToShipInStockUnit) &&
                    ['inProgress', 'quote', 'pending'].includes(await line.status)
                ) {
                    acc.sumActualQuantity += +(await line.suppliedQuantity);
                    acc.sumQuantityInStockUnit += +((await orderAssignments.at(0)?.quantityInStockUnit) ?? 0);
                    acc.filteredAssignedOrders.push({ line, orderAssignments });
                }
                return acc;
            },
            { sumActualQuantity: 0, sumQuantityInStockUnit: 0, filteredAssignedOrders: [] },
        );
    }

    /**
     * Method that opens a sales order
     * @param context
     * @param salesOrderNumber the number of the sales order
     * @returns enum SalesOrderOpenStatusMethodReturn
     */
    @decorators.mutation<typeof SalesOrder, 'setSalesOrderOpenStatus'>({
        isPublished: true,
        parameters: [{ name: 'salesOrderNumber', type: 'string', isMandatory: false }],
        return: {
            type: 'enum',
            dataType: () => xtremSales.enums.salesOrderOpenStatusMethodReturnDataType,
        },
    })
    static async setSalesOrderOpenStatus(
        context: Context,
        salesOrderNumber: string,
    ): Promise<xtremSales.enums.SalesOrderOpenStatusMethodReturn> {
        return (await xtremSales.functions.setSalesDocumentOpenStatus(
            context,
            salesOrderNumber,
            xtremSales.nodes.SalesOrder,
            xtremSales.enums.salesOrderOpenStatusMethodReturnDataType,
            xtremSales.nodes.SalesOrderLineToSalesShipmentLine,
        )) as xtremSales.enums.SalesOrderOpenStatusMethodReturn;
    }

    /**
     * Method that change the header status from quote to pending and propagate it to the lines
     * @param context
     * @param salesOrder Sales order reference
     * @returns enum SalesOrderConfirmMethodReturn
     */
    @decorators.mutation<typeof SalesOrder, 'confirmSalesOrder'>({
        isPublished: true,
        parameters: [
            {
                name: 'salesOrder',
                type: 'reference',
                node: () => xtremSales.nodes.SalesOrder,
                isWritable: true,
                isMandatory: true,
            },
            { name: 'isSafeToRetry', type: 'boolean' },
        ],
        return: { type: 'reference', node: () => xtremSales.nodes.SalesOrder },
    })
    static async confirmSalesOrder(
        context: Context,
        salesOrder: SalesOrder,
        isSafeToRetry = false,
    ): Reference<SalesOrder> {
        if ((await salesOrder.status) !== 'quote') {
            const errorMessage = context.localize(
                '@sage/xtrem-sales/nodes__sales_order__sales_order_already_confirmed',
                'The sales order is already confirmed.',
            );
            if (isSafeToRetry) {
                logger.warn(errorMessage);
                return salesOrder;
            }
            throw new BusinessRuleError(errorMessage);
        }

        await salesOrder.lines.forEach(async line => {
            if ((await line.status) !== 'closed') {
                await line.$.set({ status: 'pending' });
            }
        });

        salesOrder._canUpdateIsPrinted = true;
        await salesOrder.$.set({
            status: 'pending',
            isQuote: false,
            isPrinted: false,
            isSent: false,
        });
        salesOrder.__skipPrepare = true;
        await salesOrder.$.save();
        return salesOrder;
    }

    /**
     * Method that calculate subWorkDays -> because not work properly on front-end
     * @param context
     * @param requestedDeliveryDate
     * @param date
     * @param doNotShipBeforeDate
     * @param doNotShipAfterDate
     * @param deliveryLeadTime
     * @param workDaysMask
     * @returns date subWorkDaysMethodReturn
     */
    @decorators.mutation<typeof SalesOrder, 'subWorkDays'>({
        isPublished: true,
        parameters: [
            { name: 'requestedDeliveryDate', type: 'date', isMandatory: true },
            { name: 'orderDate', type: 'date', isMandatory: true },
            { name: 'doNotShipBeforeDate', type: 'date', isMandatory: false },
            { name: 'doNotShipAfterDate', type: 'date', isMandatory: false },
            { name: 'deliveryLeadTime', type: 'integer', isMandatory: true },
            { name: 'workDaysMask', type: 'integer', isMandatory: true },
        ],
        return: { type: 'date', isMandatory: true },
    })
    static subWorkDays(
        context: Context,
        requestedDeliveryDate: date,
        orderDate: date,
        doNotShipBeforeDate: date | null,
        doNotShipAfterDate: date | null,
        deliveryLeadTime: integer,
        workDaysMask: integer,
    ): date {
        return xtremDistribution.functions.subWorkDays(context, {
            requestedDeliveryDate,
            orderDate,
            doNotShipBeforeDate,
            doNotShipAfterDate,
            deliveryLeadTime,
            workDaysMask,
        });
    }

    /**
     * Method that calculate addWorkDays -> because not work properly on front-end
     * @param context
     * @param shippingDate
     * @param deliveryLeadTime
     * @param workDays
     * @returns date addWorkDaysMethodReturn
     */
    @decorators.mutation<typeof SalesOrder, 'addWorkDays'>({
        isPublished: true,
        parameters: [
            { name: 'shippingDate', type: 'date', isMandatory: true },
            { name: 'deliveryLeadTime', type: 'integer', isMandatory: true },
            { name: 'workDays', type: 'integer', isMandatory: true },
        ],
        return: { type: 'date', isMandatory: true },
    })
    static addWorkDays(context: Context, shippingDate: date, deliveryLeadTime: integer, workDays: integer): date {
        return xtremDistribution.functions.addWorkDays(shippingDate, deliveryLeadTime, workDays);
    }

    private calculateTaxExcludedTotalAmount(): Promise<decimal> {
        return this.lines.sum(line => line.amountExcludingTax);
    }

    /**
     * Method that calculate price determination for all lines
     * @param context
     * @param salesDocument
     * @returns boolean
     */
    @decorators.mutation<typeof SalesOrder, 'massPriceDeterminationCalculation'>({
        isPublished: true,
        parameters: [
            {
                name: 'salesDocument',
                type: 'reference',
                node: () => xtremSales.nodes.SalesOrder,
                isMandatory: true,
                isWritable: true,
            },
        ],
        return: { type: 'boolean', isMandatory: true },
    })
    static async massPriceDeterminationCalculation(
        _context: Context,
        salesDocument: xtremSales.nodes.SalesOrder,
    ): Promise<boolean> {
        await salesDocument.lines.forEach(async line =>
            xtremSales.functions.calculateSalesPriceDetermination(
                line as xtremSales.interfaces.SalesLineNode,
                await salesDocument.billToCustomer,
                await salesDocument.date,
                await salesDocument.stockSite,
            ),
        );
        await salesDocument.$.save();
        return true;
    }

    /**
     * Toggle the isPrinted property to true
     * @param _context
     * @param order
     * @returns boolean
     */
    @decorators.mutation<typeof SalesOrder, 'setIsPrintedTrue'>({
        isPublished: true,
        parameters: [
            { name: 'order', type: 'reference', isMandatory: true, isWritable: true, node: () => SalesOrder },
            { name: 'isPrinted', type: 'boolean', isMandatory: false },
        ],
        return: { type: 'boolean' },
    })
    static async setIsPrintedTrue(_context: Context, order: SalesOrder, isPrinted = true): Promise<boolean> {
        if (['quote', 'pending', 'inProgress'].includes(await order.status)) {
            order._canUpdateIsPrinted = true;
            if (!isPrinted) {
                await order.$.set({ isSent: false });
            }
            await order.$.set({ isPrinted });
            await order.$.save();
        }
        return true;
    }

    /**
     * Operation that sends an email with a sales order pdf attached to an email address.
     * @param context
     * @param salesOrder
     * @param contactTitle
     * @param contactLastName
     * @param contactFirstName
     * @param contactEmail
     */
    @decorators.mutation<typeof SalesOrder, 'printSalesOrderAndEmail'>({
        isPublished: true,
        startsReadOnly: true,
        parameters: [
            { name: 'salesOrder', type: 'reference', isMandatory: true, node: () => SalesOrder },
            { name: 'contactTitle', type: 'string', isMandatory: true },
            { name: 'contactLastName', type: 'string', isMandatory: true },
            { name: 'contactFirstName', type: 'string', isMandatory: true },
            { name: 'contactEmail', type: 'string', isMandatory: true },
        ],
        return: 'boolean',
    })
    static printSalesOrderAndEmail(
        context: Context,
        salesOrder: xtremSales.nodes.SalesOrder,
        contactTitle: string,
        contactLastName: string,
        contactFirstName: string,
        contactEmail: string,
    ): Promise<boolean> {
        return printSalesDocumentAndEmail(
            context,
            salesOrder,
            contactTitle,
            contactLastName,
            contactFirstName,
            contactEmail,
        );
    }

    /**
     * This method generates shipments for a set of sales orders during mass creation process
     * The sales shipment cannot be 'Invoiced'
     * @param context
     * @param stockSite site _id
     * @param fromSoldToCustomer customer _id
     * @param toSoldToCustomer customer _id
     * @param fromOrder sales shipment _id
     * @param toOrder sales shipment _id
     * @param shippingUntilDate
     */
    @decorators.mutation<typeof SalesOrder, 'massCreateSalesShipments'>({
        isPublished: true,
        parameters: [
            { name: 'stockSite', type: 'string', isMandatory: true },
            { name: 'fromSoldToCustomer', type: 'string', isMandatory: false },
            { name: 'toSoldToCustomer', type: 'string', isMandatory: false },
            { name: 'fromOrder', type: 'string', isMandatory: false },
            { name: 'toOrder', type: 'string', isMandatory: false },
            { name: 'shippingUntilDate', type: 'date', isMandatory: false, isNullable: true },
            { name: 'incoterm', type: 'string', isMandatory: false, isNullable: true },
            { name: 'deliveryMode', type: 'string', isMandatory: false, isNullable: true },
        ],
        return: { type: 'object', properties: { numberOfShipments: 'integer' } },
        startsReadOnly: true,
    })
    static async massCreateSalesShipments(
        context: Context,
        stockSite: string,
        fromSoldToCustomer?: string,
        toSoldToCustomer?: string,
        fromOrder?: string,
        toOrder?: string,
        shippingUntilDate?: date,
        incoterm?: string,
        deliveryMode?: string,
    ): Promise<{ numberOfShipments?: number }> {
        const filter = xtremSales.sharedFunctions.orderLineShippingFilterCriteria(
            stockSite,
            String(date.today()),
            shippingUntilDate ? String(shippingUntilDate) : null,
            fromSoldToCustomer,
            toSoldToCustomer,
            incoterm,
            deliveryMode,
            fromOrder,
            toOrder,
        );
        logger.debug(() => `massCreateSalesShipments ${JSON.stringify(filter, null, 4)}`);

        const salesOrderLines = context.query(xtremSales.nodes.SalesOrderLine, { filter });
        if (await salesOrderLines.length) {
            const salesShipmentCreator = this.instantiateSalesShipmentsCreator(context);
            salesShipmentCreator.logErrorsProperty = true;

            await salesOrderLines.forEach(async salesDocumentLine => {
                await salesShipmentCreator.prepareNodeCreateData([{ salesDocumentLine }]);
            });

            const result = await salesShipmentCreator.createSalesOutputDocuments();

            return { numberOfShipments: result.numberOfShipments };
        }
        return { numberOfShipments: 0 };
    }

    @decorators.asyncMutation<typeof SalesOrder, 'createTestSalesOrders'>({
        isPublished: true,
        startsReadOnly: false,
        serviceOptions: () => [xtremSystem.serviceOptions.DevTools],
        parameters: [
            {
                isMandatory: false,
                name: 'orderCreation',
                type: 'object',
                properties: {
                    customerId: { type: 'string', isMandatory: true },
                    item: { type: 'reference', node: () => xtremMasterData.nodes.Item, isMandatory: true },
                    itemQuantity: { type: 'integer', isMandatory: true },
                    orderQuantity: { type: 'integer', isMandatory: true },
                    numberOfLinesPerOrder: { type: 'integer', isMandatory: true },
                    orderNumberRoot: { type: 'string', isMandatory: false },
                    fixedNumberOfLines: { type: 'boolean', isMandatory: false },
                    allocateStock: { type: 'boolean', isMandatory: false },
                    randomDeliveryDate: { type: 'boolean', isMandatory: false },
                },
            },
        ],
        return: { type: 'string' },
    })
    static createTestSalesOrders(
        context: Context,
        orderCreation: xtremSales.interfaces.TestOrderCreationParameters,
    ): Promise<string> {
        return xtremSales.functions.createTestSalesOrders(context, orderCreation);
    }

    @decorators.mutation<typeof xtremSales.nodes.SalesOrder, 'beforePrintSalesOrder'>({
        isPublished: true,
        parameters: [
            {
                name: 'order',
                type: 'reference',
                isMandatory: true,
                node: () => xtremSales.nodes.SalesOrder,
            },
        ],
        return: 'boolean',
    })
    static async beforePrintSalesOrder(context: Context, order: xtremSales.nodes.SalesOrder): Promise<boolean> {
        validateOrderHasLines(context, await order.lines.length);
        validateTaxCalculation(context, await order.taxCalculationStatus);

        await order.lines.forEach(line => validateLineItemActive(context, line));

        await context.batch.logMessage(
            'info',
            `Sales order ${await order.number} validation for printing completed successfully.`,
        );
        return true;
    }

    @decorators.bulkMutation<typeof SalesOrder, 'printBulkSalesOrders'>({
        isPublished: true,
        startsReadOnly: true,
        queue: 'reporting',
        async onComplete(context, reports) {
            const reportName = context.localize(
                '@sage/xtrem-sales/nodes__sales_order__bulk_print_report_name',
                'Sales order',
            );
            await xtremMasterData.functions.bulkPrintOnCompleteHandler(context, {
                documentType: 'salesOrder',
                documents: reports,
                reportName,
            });
        },
    })
    static async printBulkSalesOrders(context: Context, document: SalesOrder) {
        const reportName = (await document.status) === 'quote' ? 'salesOrderQuote' : 'salesOrder';
        return xtremReporting.nodes.Report.generateReportPdf(context, reportName, '', {
            variables: JSON.stringify({ order: document._id }),
            isBulk: true,
        });
    }

    @decorators.mutation<typeof xtremSales.nodes.SalesOrder, 'afterPrintSalesOrder'>({
        isPublished: true,
        startsReadOnly: true,
        parameters: [
            {
                name: 'order',
                type: 'reference',
                isMandatory: true,
                node: () => SalesOrder,
            },
        ],
        return: 'boolean',
    })
    static async afterPrintSalesOrder(readonlyContext: Context, order: SalesOrder): Promise<boolean> {
        const number = await order.number;

        if (!(await order.isPrinted)) {
            await readonlyContext.runInWritableContext(async writableContext => {
                const writeableOrder = await writableContext.read(SalesOrder, { _id: order._id }, { forUpdate: true });
                await SalesOrder.setIsPrintedTrue(writableContext, writeableOrder);
            });
            await readonlyContext.batch.logMessage('info', `Sales order ${number} marked as printed.`);
        } else {
            await readonlyContext.batch.logMessage(
                'info',
                `Sales order ${number} is already marked as printed. Skipping update.`,
            );
        }
        return true;
    }

    /* ********************************
    Notification for the allocation engine
    ******************************** */
    @decorators.mutation<typeof SalesOrder, 'requestAutoAllocation'>({
        isPublished: true,
        parameters: [
            {
                name: 'salesOrder',
                type: 'reference',
                node: () => xtremSales.nodes.SalesOrder,
                isMandatory: true,
                isWritable: true,
            },
            {
                name: 'requestType',
                type: 'enum',
                dataType: () => xtremStockData.enums.allocationRequestTypeDataType,
                isMandatory: true,
            },
            { name: 'isSafeToRetry', type: 'boolean' },
        ],
        return: { type: 'string' },
    })
    static requestAutoAllocation(
        context: Context,
        salesOrder: xtremSales.nodes.SalesOrder,
        requestType: xtremStockData.enums.AllocationRequestType,
        isSafeToRetry = false,
    ): Promise<string> {
        return xtremStockData.functions.notificationLib.requestAutoStockAllocation(
            context,
            {
                documentType: 'salesOrder',
                processType: 'document',
                requestType,
                document: salesOrder,
            },
            xtremSales.functions.isSalesOrderLineAllocable,
            isSafeToRetry,
            logger,
            context.batch.trackingId,
        );
    }

    @decorators.notificationListener<typeof SalesOrder>({
        startsReadOnly: true,
        topic: 'SalesOrder/allocation/reply',
        onError(
            context: Context,
            envelope: NotificationEnvelope<xtremStockData.interfaces.AllocationReplyPayload>,
            error: Error,
        ) {
            return xtremStockData.functions.notificationLib.errorHandlerForAutomaticAllocation(
                context,
                envelope,
                error,
                SalesOrder,
            );
        },
    })
    static async onAllocationReply(
        context: Context,
        payload: xtremStockData.interfaces.AllocationReplyPayload,
    ): Promise<void> {
        await xtremStockData.functions.notificationLib.reactToAutomaticAllocationReply(
            context,
            payload,
            SalesOrder,
            xtremSales.functions.isSalesOrderLineAllocable,
        );
    }

    @decorators.asyncMutation<typeof SalesOrder, 'autoAllocate'>({
        isPublished: true,
        parameters: [
            {
                name: 'salesOrder',
                type: 'reference',
                node: () => xtremSales.nodes.SalesOrder,
                isMandatory: true,
                isWritable: true,
            },
            { name: 'requestType', type: 'string' },
            { name: 'isSafeToRetry', type: 'boolean' },
        ],
        return: { type: 'reference', node: () => xtremSales.nodes.SalesOrder },
    })
    static async autoAllocate(
        context: Context,
        salesOrder: xtremSales.nodes.SalesOrder,
        requestType: xtremStockData.enums.AllocationRequestType,
        isSafeToRetry = false,
    ): Promise<xtremSales.nodes.SalesOrder> {
        const processId = await this.requestAutoAllocation(context, salesOrder, requestType, isSafeToRetry);
        // processId is the id of the notification sent to the stock engine.
        // If it is not empty, we throw the special SuspendException so that the framework does not update
        // the SysNotificationState of the async mutation.
        if (processId) throw new SuspendException();
        // If processId is empty, it means that the stock engine has already processed the request.
        return salesOrder;
    }

    @decorators.mutation<typeof SalesOrder, 'financeIntegrationCheck'>({
        isPublished: true,
        parameters: [
            {
                name: 'salesOrder',
                type: 'reference',
                isMandatory: true,
                isWritable: true,
                node: () => SalesOrder,
            },
        ],
        return: { type: 'object', properties: { wasSuccessful: 'boolean', message: 'string' } },
    })
    static async financeIntegrationCheck(
        context: Context,
        salesOrder: xtremSales.nodes.SalesOrder,
    ): Promise<xtremFinanceData.interfaces.MutationResult> {
        const salesShipmentsCreator = this.instantiateSalesShipmentsCreator(context);

        await salesOrder.lines.forEach(async line => {
            if ((await line.shippingStatus) !== 'shipped') {
                await salesShipmentsCreator.prepareNodeCreateData([{ salesDocumentLine: line }], {
                    isForFinanceCheck: true,
                });
            }
        });

        let financeIntegrationCheckResult: xtremFinanceData.interfaces.MutationResult = {
            wasSuccessful: false,
            message: '',
        };

        await asyncArray(Object.keys(salesShipmentsCreator.salesOutputDocuments)).every(
            async salesOutputDocumentsKey => {
                const shipmentPayload = salesShipmentsCreator.salesOutputDocuments[salesOutputDocumentsKey];
                const shipment = await context.create(xtremSales.nodes.SalesShipment, shipmentPayload, {
                    isTransient: true,
                });
                financeIntegrationCheckResult =
                    await xtremSales.functions.FinanceIntegration.salesShipmentControlAndCreateMutationResult(
                        context,
                        shipment,
                    );
                if (!financeIntegrationCheckResult.wasSuccessful) {
                    return false;
                }
                return true;
            },
        );

        return financeIntegrationCheckResult;
    }

    @decorators.query<typeof SalesOrder, 'getLastOrderDate'>({
        isPublished: true,
        parameters: [
            { name: 'customer', type: 'reference', node: () => xtremMasterData.nodes.Customer, isMandatory: true },
        ],
        return: {
            type: 'date',
        },
    })
    static async getLastOrderDate(context: Context, customer: xtremMasterData.nodes.Customer): Promise<date | null> {
        const [order] = await context
            .query(SalesOrder, {
                filter: {
                    soldToCustomer: customer,
                    isQuote: false,
                },
                orderBy: {
                    date: -1,
                },
                first: 1,
            })
            .toArray();
        return (await order?.date) ?? null;
    }

    @decorators.query<typeof SalesOrder, 'getOrderBookAmount'>({
        isPublished: true,
        parameters: [
            { name: 'customer', type: 'reference', node: () => xtremMasterData.nodes.Customer, isMandatory: true },
        ],
        return: {
            type: 'object',
            properties: {
                orderBookTotal: 'decimal',
                currencySymbol: 'string',
                decimalDigits: 'integer',
            },
        },
    })
    static async getOrderBookAmount(
        context: Context,
        customer: xtremMasterData.nodes.Customer,
    ): Promise<{ orderBookTotal: decimal; currencySymbol: string; decimalDigits: number }> {
        let orderBookTotal = 0;
        let currencySymbol = '';
        let decimalDigits = 2;
        if (customer) {
            const customerCurrency = await customer.currency;
            currencySymbol = await customerCurrency.symbol;
            decimalDigits = await customerCurrency.decimalDigits;
            const salesResults = context
                .queryAggregate(SalesOrder, {
                    filter: {
                        soldToCustomer: customer,
                        status: { _in: ['pending', 'inProgress'] },
                    },
                    group: {
                        currency: { id: { _by: 'value' } },
                    },
                    values: {
                        remainingTotalAmountToShipExcludingTax: { sum: true },
                    },
                })
                .toArray();
            orderBookTotal = await SalesOrder.calculateRemainingTotalInRequiredCurrency(
                context,
                await salesResults,
                customerCurrency,
            );

            return { orderBookTotal, currencySymbol, decimalDigits };
        }
        return { orderBookTotal, currencySymbol, decimalDigits };
    }

    private static calculateRemainingTotalInRequiredCurrency(
        context: Context,
        group: Array<xtremSales.interfaces.SalesOrderTotalsResult>,
        customerCurrency: xtremMasterData.nodes.Currency,
    ): Promise<decimal> {
        return asyncArray(group).sum(async (result: xtremSales.interfaces.SalesOrderTotalsResult) => {
            if (result.group) {
                if (result.group.currency.id === (await customerCurrency.id)) {
                    return result.values.remainingTotalAmountToShipExcludingTax.sum;
                }
                return xtremMasterData.functions.convertCurrency(
                    context,
                    result.values.remainingTotalAmountToShipExcludingTax.sum,
                    result.group.currency.id,
                    await customerCurrency.id,
                    date.today(),
                );
            }
            return 0;
        });
    }
}
