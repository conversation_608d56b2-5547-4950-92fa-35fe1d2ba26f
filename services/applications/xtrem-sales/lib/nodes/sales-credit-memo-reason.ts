import { decorators, Node } from '@sage/xtrem-core';
import { dataTypes, functions } from '@sage/xtrem-system';

@decorators.node<SalesCreditMemoReason>({
    package: 'xtrem-sales',
    storage: 'sql',
    isPublished: true,
    canRead: true,
    canDelete: true,
    canCreate: true,
    canUpdate: true,
    canDuplicate: true,
    indexes: [{ orderBy: { id: +1 }, isUnique: true, isNaturalKey: true }],
    isSetupNode: true,
})
export class SalesCreditMemoReason extends Node {
    @decorators.booleanProperty<SalesCreditMemoReason, 'isActive'>({
        isPublished: true,
        isStored: true,
        defaultValue() {
            return true;
        },
        provides: ['isActive'],
        isOwnedByCustomer: true,
        lookupAccess: true,
        duplicateRequiresPrompt: true,
    })
    readonly isActive: Promise<boolean>;

    @decorators.stringProperty<SalesCreditMemoReason, 'id'>({
        isStored: true,
        isPublished: true,
        isNotEmpty: true,
        isFrozen: true,
        duplicateRequiresPrompt: true,
        duplicatedValue: '',
        dataType: () => dataTypes.setupIdDataType,
        defaultValue() {
            return functions.generateNanoId();
        },
        lookupAccess: true,
    })
    readonly id: Promise<string>;

    @decorators.stringProperty<SalesCreditMemoReason, 'name'>({
        isStored: true,
        isPublished: true,
        isNotEmpty: true,
        dataType: () => dataTypes.localizedName,
        lookupAccess: true,
        duplicateRequiresPrompt: true,
    })
    readonly name: Promise<string>;

    @decorators.stringProperty<SalesCreditMemoReason, 'description'>({
        isStored: true,
        isPublished: true,
        dataType: () => dataTypes.localizedDescription,
        lookupAccess: true,
        duplicateRequiresPrompt: true,
    })
    readonly description: Promise<string>;
}
