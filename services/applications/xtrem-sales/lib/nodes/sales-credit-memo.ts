import type { Collection, Context, decimal, Dict, Reference, TextStream } from '@sage/xtrem-core';
import {
    BusinessRuleError,
    date,
    Decimal,
    decorators,
    Logger,
    NodeStatus,
    useDefaultValue,
    ValidationSeverity,
} from '@sage/xtrem-core';
import * as xtremDistribution from '@sage/xtrem-distribution';
import * as xtremFinanceData from '@sage/xtrem-finance-data';
import type * as xtremMailer from '@sage/xtrem-mailer';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremReporting from '@sage/xtrem-reporting';
import * as xtremStructure from '@sage/xtrem-structure';
import * as xtremSystem from '@sage/xtrem-system';
import { dataTypes } from '@sage/xtrem-system';
import * as xtremTax from '@sage/xtrem-tax';
import * as xtremSales from '../index';

const logger = Logger.getLogger(__filename, 'sales-credit-memo');

@decorators.subNode<SalesCreditMemo>({
    extends: () => xtremMasterData.nodes.BaseDocument,
    isPublished: true,
    canCreate: true,
    canRead: true,
    canUpdate: true,
    canDelete: true,
    hasAttachments: true,
    async prepareBegin() {
        await xtremSales.functions.salesDocumentTaxUpdate<
            xtremSales.nodes.SalesCreditMemo,
            xtremSales.classes.SalesTaxCalculator
        >(
            this,
            {
                customer: await this.billToCustomer,
                documentDate: await this.date,
                stockSite: await this.site,
            },
            await xtremSales.classes.SalesTaxCalculator.create(
                this.$.context,
                await this.site,
                await this.status,
                await this.wasTaxDataChanged,
            ),
        );
    },
    async saveBegin() {
        const documentNumberGenerator = await xtremMasterData.functions.getDocumentNumberGenerator(this);
        await documentNumberGenerator.controls();

        const isSalesCreditMemoChronological = await xtremSales.functions.isChronological(
            await (
                await (
                    await this.site
                ).legalCompany
            ).legislation,
            documentNumberGenerator,
        );

        if (isSalesCreditMemoChronological) {
            await this.checkIfCreditMemoDateLaterThanToday();
        }

        if (this.$.status === NodeStatus.modified) {
            await this.saveBeginModified(documentNumberGenerator);
        }

        await xtremSales.functions.SalesCreditMemoLib.updateCreditMemoHeaderNotesOnCreation(this);
    },
    async saveEnd() {
        await this.updateRelatedSalesDocumentsStatuses(
            await xtremSales.functions.SalesCreditMemoLib.linkedInvoiceArray(this),
        );
    },
    async controlDelete(cx): Promise<void> {
        await xtremSales.events.controlDelete.creditMemo.checkStatusForDeletion(cx, this);
    },
    async deleteEnd(): Promise<void> {
        await this.updateRelatedSalesDocumentsStatuses(
            await xtremSales.functions.SalesCreditMemoLib.linkedInvoiceArray(this),
        );
    },
    async controlEnd(cx) {
        await xtremTax.functions.validateTaxCategoryAndPaymentTerm(cx, {
            isIntacctActivationOptionEnabled: await this.$.context.isServiceOptionEnabled(
                xtremStructure.serviceOptions.intacctActivationOption,
            ),
            taxes: await this.taxes.toArray(),
            paymentTerm: await this.paymentTerm,
        });
    },
    async controlBegin(cx) {
        await xtremSales.events.controlBegin.documents.checkWrongTaxType(cx, this);
        await xtremSales.events.controlBegin.documents.checkDocumentStatus(cx, this);
        await xtremMasterData.events.control.baseDocumentControls.atLeastOneLine(cx, this);
    },
})
export class SalesCreditMemo
    extends xtremMasterData.nodes.BaseDocument
    implements xtremMasterData.interfaces.Document, xtremFinanceData.interfaces.SalesFinanceDocumentWithTax
{
    public __skipPrepare: boolean;

    __salesTaxCalculator: xtremSales.classes.SalesTaxCalculator;

    __salesInvoices: Dict<[xtremSales.interfaces.SalesDocumentLineUpdateInterface]> = {};

    __salesReturnRequests: Dict<[xtremSales.interfaces.SalesDocumentLineUpdateInterface]> = {};

    @decorators.booleanProperty<SalesCreditMemo, 'canUpdateIsSent'>({
        defaultValue: false,
    })
    canUpdateIsSent: Promise<boolean>;

    @decorators.enumPropertyOverride<SalesCreditMemo, 'status'>({
        // dataType: () => xtremSales.enums.salesCreditMemoStatusDataType,
    })
    override readonly status: Promise<xtremSales.enums.SalesCreditMemoStatus>;

    @decorators.enumPropertyOverride<SalesCreditMemo, 'displayStatus'>({
        dependsOn: ['status', 'taxCalculationStatus', 'paymentStatus'],
        async updatedValue() {
            return xtremSales.functions.SalesCreditMemoLib.calculateSalesCreditMemoDisplayStatus(
                await this.status,
                await this.taxCalculationStatus,
                await this.paymentStatus,
            );
        },
    })
    override readonly displayStatus: Promise<xtremSales.enums.SalesCreditMemoDisplayStatus>;

    @decorators.enumProperty<SalesCreditMemo, 'paymentStatus'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isNullable: true,
        dataType: () => xtremFinanceData.enums.openItemStatusDataType,
        defaultValue: () => 'notPaid',
        serviceOptions: () => [xtremFinanceData.serviceOptions.paymentTrackingOption],
    })
    readonly paymentStatus: Promise<xtremFinanceData.enums.OpenItemStatus | null>;

    @decorators.enumProperty<SalesCreditMemo, 'taxEngine'>({
        isPublished: true,
        dataType: () => xtremFinanceData.enums.taxEngineDataType,
        async getValue() {
            return (await (await this.site).legalCompany).taxEngine;
        },
    })
    readonly taxEngine: Promise<xtremFinanceData.enums.TaxEngine | null>;

    /** deprecated */
    @decorators.referenceProperty<SalesCreditMemo, 'salesSite'>({
        isPublished: true,
        node: () => xtremSystem.nodes.Site,
        getValue() {
            return this.site;
        },
    })
    readonly salesSite: Reference<xtremSystem.nodes.Site>;

    @decorators.referencePropertyOverride<SalesCreditMemo, 'site'>({
        // isRequired: true,
        filters: { control: { isSales: true } },
    })
    override readonly site: Reference<xtremSystem.nodes.Site>;

    @decorators.stringProperty<SalesCreditMemo, 'salesSiteName'>({
        isStored: true,
        isPublished: true,
        isRequired: true,
        dataSensitivityLevel: 'personal',
        anonymizeMethod: 'perCharRandom',
        dataType: () => dataTypes.name,
        dependsOn: ['site'],
        async defaultValue() {
            return (await this.site).name;
        },
        updatedValue: useDefaultValue,
    })
    readonly salesSiteName: Promise<string>;

    @decorators.stringProperty<SalesCreditMemo, 'salesSiteTaxIdNumber'>({
        isStored: true,
        isPublished: true,
        isRequired: false,
        dataSensitivityLevel: 'personal',
        anonymizeMethod: 'custom',
        anonymizeValue(value) {
            return xtremStructure.functions.anonymizeTaxId(value);
        },
        dataType: () => xtremStructure.dataTypes.taxIdentificationDataType,
        dependsOn: ['site'],
        async defaultValue() {
            return (await (await this.site).businessEntity).taxIdNumber;
        },
        updatedValue: useDefaultValue,
    })
    readonly salesSiteTaxIdNumber: Promise<string>;

    @decorators.referenceProperty<SalesCreditMemo, 'salesSiteLinkedAddress'>({
        isStored: true,
        isPublished: true,
        isRequired: true,
        dependsOn: ['site'],
        node: () => xtremMasterData.nodes.BusinessEntityAddress,
        async defaultValue() {
            return (await this.site).primaryAddress;
        },
    })
    readonly salesSiteLinkedAddress: Reference<xtremMasterData.nodes.BusinessEntityAddress>;

    @decorators.referenceProperty<SalesCreditMemo, 'salesSiteAddress'>({
        isPublished: true,
        isStored: true,
        isMutable: true,
        isNullable: true,
        dependsOn: ['salesSiteLinkedAddress'],
        node: () => xtremMasterData.nodes.Address,
        async defaultValue() {
            return xtremSales.functions.getSalesAddressDetail(await this.salesSiteLinkedAddress);
        },
        updatedValue: useDefaultValue,
    })
    readonly salesSiteAddress: Reference<xtremMasterData.nodes.Address | null>;

    @decorators.referenceProperty<SalesCreditMemo, 'salesSiteContact'>({
        isPublished: true,
        isStored: true,
        isMutable: true,
        isNullable: true,
        dependsOn: ['salesSiteLinkedAddress'],
        node: () => xtremMasterData.nodes.Contact,
        async defaultValue() {
            return xtremSales.functions.getBusinessEntityContact(await this.salesSiteLinkedAddress);
        },
        updatedValue: useDefaultValue,
    })
    readonly salesSiteContact: Reference<xtremMasterData.nodes.Contact | null>;

    @decorators.referenceProperty<SalesCreditMemo, 'reason'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
        node: () => xtremSales.nodes.SalesCreditMemoReason,
    })
    readonly reason: Reference<xtremSales.nodes.SalesCreditMemoReason | null>;

    @decorators.referenceProperty<SalesCreditMemo, 'billToCustomer'>({
        isStored: true,
        isPublished: true,
        isRequired: true,
        node: () => xtremMasterData.nodes.Customer,
    })
    readonly billToCustomer: Reference<xtremMasterData.nodes.Customer>;

    @decorators.stringProperty<SalesCreditMemo, 'billToCustomerName'>({
        isStored: true,
        isPublished: true,
        isRequired: true,
        dataSensitivityLevel: 'personal',
        anonymizeMethod: 'perCharRandom',
        dataType: () => dataTypes.name,
        dependsOn: ['billToCustomer'],
        async defaultValue() {
            return (await (await this.billToCustomer).businessEntity).name;
        },
        updatedValue: useDefaultValue,
    })
    readonly billToCustomerName: Promise<string>;

    @decorators.referenceProperty<SalesCreditMemo, 'billToLinkedAddress'>({
        isStored: true,
        isPublished: true,
        isRequired: true,
        dependsOn: ['billToCustomer'],
        node: () => xtremMasterData.nodes.BusinessEntityAddress,
        async defaultValue() {
            return (await this.billToCustomer).primaryAddress;
        },
    })
    readonly billToLinkedAddress: Reference<xtremMasterData.nodes.BusinessEntityAddress>;

    @decorators.referenceProperty<SalesCreditMemo, 'billToAddress'>({
        isPublished: true,
        isStored: true,
        isMutable: true,
        isNullable: true,
        dependsOn: ['billToLinkedAddress'],
        node: () => xtremMasterData.nodes.Address,
        async defaultValue() {
            return xtremSales.functions.getSalesAddressDetail(await this.billToLinkedAddress);
        },
        updatedValue: useDefaultValue,
    })
    readonly billToAddress: Reference<xtremMasterData.nodes.Address | null>;

    @decorators.referenceProperty<SalesCreditMemo, 'billToContact'>({
        isPublished: true,
        isStored: true,
        isMutable: true,
        isNullable: true,
        dependsOn: ['billToLinkedAddress'],
        node: () => xtremMasterData.nodes.Contact,
        async defaultValue() {
            return xtremSales.functions.getBusinessEntityContact(await this.billToLinkedAddress);
        },
        updatedValue: useDefaultValue,
    })
    readonly billToContact: Reference<xtremMasterData.nodes.Contact | null>;

    @decorators.stringProperty<SalesCreditMemo, 'billToCustomerTaxIdNumber'>({
        isStored: true,
        isPublished: true,
        isRequired: false,
        dataSensitivityLevel: 'personal',
        anonymizeMethod: 'custom',
        anonymizeValue(value) {
            return xtremStructure.functions.anonymizeTaxId(value);
        },
        dataType: () => xtremStructure.dataTypes.taxIdentificationDataType,
        dependsOn: ['billToCustomer'],
        async defaultValue() {
            return (await (await this.billToCustomer).businessEntity).taxIdNumber;
        },
        updatedValue: useDefaultValue,
    })
    readonly billToCustomerTaxIdNumber: Promise<string>;

    @decorators.referencePropertyOverride<SalesCreditMemo, 'currency'>({
        // isRequired: true,
        dependsOn: ['billToCustomer'],
        async defaultValue() {
            return (await (await this.billToCustomer)?.businessEntity)?.currency;
        },
    })
    override readonly currency: Reference<xtremMasterData.nodes.Currency>;

    @decorators.referenceProperty<SalesCreditMemo, 'paymentTerm'>({
        isStored: true,
        isPublished: true,
        isRequired: true,
        dependsOn: ['billToCustomer'],
        node: () => xtremMasterData.nodes.PaymentTerm,
        async defaultValue() {
            return (await this.billToCustomer)?.paymentTerm;
        },
        filters: { control: { businessEntityType: { _in: ['customer', 'all'] } } },
    })
    readonly paymentTerm: Reference<xtremMasterData.nodes.PaymentTerm>;

    @decorators.dateProperty<SalesCreditMemo, 'dueDate'>({
        isStored: true,
        isPublished: true,
        dependsOn: ['paymentTerm', 'date'],
        async defaultValue() {
            const paymentTerm = await this.paymentTerm;
            return xtremMasterData.sharedFunctions.getDueDate({
                dueDateType: await paymentTerm.dueDateType,
                days: (await paymentTerm.days) ?? 0,
                baseDate: await this.date,
            });
        },
        updatedValue: useDefaultValue,
    })
    readonly dueDate: Promise<date>;

    @decorators.referenceProperty<SalesCreditMemo, 'incoterm'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
        node: () => xtremMasterData.nodes.Incoterm,
    })
    readonly incoterm: Reference<xtremMasterData.nodes.Incoterm | null>;

    @decorators.collectionPropertyOverride<SalesCreditMemo, 'lines'>({
        dependsOn: [
            'site',
            'billToCustomer',
            'currency',
            'billToLinkedAddress',
            'salesSiteLinkedAddress',
            'companyFxRate',
        ],
        node: () => xtremSales.nodes.SalesCreditMemoLine,
    })
    override readonly lines: Collection<xtremSales.nodes.SalesCreditMemoLine>;

    @decorators.decimalProperty<SalesCreditMemo, 'totalAmountExcludingTax'>({
        isStoredOutput: true,
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.priceInSalesPrice,
        dependsOn: ['currency', { lines: ['amountExcludingTax'] }],
        defaultValue() {
            return this.calculateTaxExcludedTotalAmount();
        },
        updatedValue: useDefaultValue,
    })
    readonly totalAmountExcludingTax: Promise<decimal>;

    @decorators.decimalProperty<SalesCreditMemo, 'totalTaxAmount'>({
        isStored: true,
        isPublished: true,
        dependsOn: ['currency'],
        dataType: () => xtremMasterData.dataTypes.priceInSalesPrice,
        async control(cx, val) {
            await cx.error.if(val).is.negative();
        },
    })
    readonly totalTaxAmount: Promise<decimal>;

    @decorators.decimalProperty<SalesCreditMemo, 'totalTaxAmountAdjusted'>({
        isStored: true,
        isPublished: true,
        dependsOn: ['currency'],
        dataType: () => xtremMasterData.dataTypes.priceDataType,
        async control(cx, val) {
            await cx.error.if(val).is.negative();
        },
    })
    readonly totalTaxAmountAdjusted: Promise<decimal>;

    @decorators.decimalProperty<SalesCreditMemo, 'totalTaxableAmount'>({
        isStored: true,
        isPublished: true,
        dependsOn: ['currency'],
        dataType: () => xtremMasterData.dataTypes.priceInSalesPrice,
        async control(cx, val) {
            await cx.error.if(val).is.negative();
        },
    })
    readonly totalTaxableAmount: Promise<decimal>;

    @decorators.decimalProperty<SalesCreditMemo, 'totalExemptAmount'>({
        isStored: true,
        isPublished: true,
        dependsOn: ['currency'],
        dataType: () => xtremMasterData.dataTypes.priceInSalesPrice,
        async control(cx, val) {
            await cx.error.if(val).is.negative();
        },
    })
    readonly totalExemptAmount: Promise<decimal>;

    @decorators.enumProperty<SalesCreditMemo, 'taxCalculationStatus'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremMasterData.enums.taxCalculationStatusDataType,
        defaultValue: () => 'notDone',
    })
    readonly taxCalculationStatus: Promise<xtremMasterData.enums.TaxCalculationStatus>;

    @decorators.decimalProperty<SalesCreditMemo, 'totalAmountIncludingTax'>({
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.priceInSalesPrice,
        async control(cx, val) {
            await cx.error.if(val).is.negative();
        },
        dependsOn: ['currency', 'totalAmountExcludingTax', 'totalTaxAmountAdjusted'],
        async getValue() {
            return Decimal.roundAt(
                (await this.totalAmountExcludingTax) + (await this.totalTaxAmountAdjusted),
                await (
                    await this.currency
                ).decimalDigits,
            );
        },
    })
    readonly totalAmountIncludingTax: Promise<decimal>;

    @decorators.decimalProperty<SalesCreditMemo, 'totalAmountIncludingTaxInCompanyCurrency'>({
        isPublished: true,
        dataType: () => xtremSales.dataTypes.salesAmountDataType, // TODO: Has to have a computed length depending of the currency decimals !!!
        dependsOn: ['currency', { lines: ['amountIncludingTaxInCompanyCurrency'] }],
        async getValue() {
            return Decimal.roundAt(
                await this.lines.sum(line => line.amountIncludingTaxInCompanyCurrency),
                await (
                    await this.currency
                ).decimalDigits,
            );
        },
    })
    readonly totalAmountIncludingTaxInCompanyCurrency: Promise<decimal>;

    @decorators.decimalProperty<SalesCreditMemo, 'discountPaymentAmount'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
        dataType: () => xtremMasterData.dataTypes.priceInSalesPrice,
        dependsOn: ['currency', 'paymentTerm'],
        async defaultValue() {
            return (await this.paymentTerm).discountAmount;
        },
        updatedValue: useDefaultValue,
    })
    readonly discountPaymentAmount: Promise<decimal | null>;

    @decorators.decimalProperty<SalesCreditMemo, 'penaltyPaymentAmount'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
        dataType: () => xtremMasterData.dataTypes.priceInSalesPrice,
        dependsOn: ['currency', 'paymentTerm'],
        async defaultValue() {
            return (await this.paymentTerm).penaltyAmount;
        },
        updatedValue: useDefaultValue,
    })
    readonly penaltyPaymentAmount: Promise<decimal | null>;

    @decorators.enumProperty<SalesCreditMemo, 'penaltyPaymentType'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
        dataType: () => xtremMasterData.enums.discountOrPenaltyTypeDataType,
        dependsOn: ['paymentTerm'],
        async defaultValue() {
            return (await this.paymentTerm).penaltyType;
        },
        updatedValue: useDefaultValue,
    })
    readonly penaltyPaymentType: Promise<xtremMasterData.enums.DiscountOrPenaltyType | null>;

    @decorators.enumProperty<SalesCreditMemo, 'discountPaymentType'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
        dataType: () => xtremMasterData.enums.discountOrPenaltyTypeDataType,
        dependsOn: ['paymentTerm'],
        async defaultValue() {
            return (await this.paymentTerm).discountType;
        },
        updatedValue: useDefaultValue,
    })
    readonly discountPaymentType: Promise<xtremMasterData.enums.DiscountOrPenaltyType | null>;

    @decorators.collectionProperty<SalesCreditMemo, 'taxes'>({
        isVital: true,
        isPublished: true,
        reverseReference: 'document',
        dependsOn: ['currency', 'status'],
        async isFrozen() {
            return (await this.status) !== 'draft' && !(await this.forceUpdateForFinance);
        },
        node: () => xtremSales.nodes.SalesCreditMemoTax,
    })
    readonly taxes: Collection<xtremSales.nodes.SalesCreditMemoTax>;

    // property needed for the accounting interface
    @decorators.decimalProperty<SalesCreditMemo, 'companyFxRate'>({
        isStored: true,
        isPublished: true,
        isRequired: true,
        dataType: () => xtremMasterData.dataTypes.exchangeRate,
        dependsOn: ['currency', 'site', 'fxRateDate'],
        async defaultValue() {
            const rate = await xtremMasterData.functions.getRateOrReverseRate(
                this.$.context,
                await (
                    await this.currency
                ).id,
                await (
                    await (
                        await (
                            await this.site
                        ).legalCompany
                    ).currency
                ).id,
                await this.fxRateDate,
            );
            return rate.isRateFound ? rate.rate : 0;
        },
        async control(cx, val) {
            await cx.error
                .withMessage('@sage/xtrem-sales/nodes__sales_credit_memo__fx_rate_not_found', 'No exchange rate found.')
                .if(val)
                .is.zero();
        },
    })
    readonly companyFxRate: Promise<decimal>;

    @decorators.stringProperty<SalesCreditMemo, 'rateDescription'>({
        isPublished: true,
        dependsOn: ['currency', 'site', 'companyFxRate', 'companyFxRateDivisor'],
        async computeValue() {
            return xtremMasterData.functions.rateDescription(
                await (
                    await this.currency
                ).id,
                await (
                    await (
                        await (
                            await this.site
                        ).legalCompany
                    ).currency
                ).id,
                await this.companyFxRate,
                await this.companyFxRateDivisor,
            );
        },
    })
    readonly rateDescription: Promise<string>;

    @decorators.decimalProperty<SalesCreditMemo, 'companyFxRateDivisor'>({
        isStored: true,
        isPublished: true,
        isRequired: true,
        dataType: () => xtremMasterData.dataTypes.exchangeRate,
        dependsOn: ['currency', 'site', 'fxRateDate'],
        async defaultValue() {
            const rate = await xtremMasterData.functions.getRateOrReverseRate(
                this.$.context,
                await (
                    await this.currency
                ).id,
                await (
                    await (
                        await (
                            await this.site
                        ).legalCompany
                    ).currency
                ).id,
                await this.fxRateDate,
            );
            return rate.isRateFound ? rate.divisor : 0;
        },
    })
    readonly companyFxRateDivisor: Promise<decimal>;

    @decorators.dateProperty<SalesCreditMemo, 'fxRateDate'>({
        isStored: true,
        isPublished: true,
        isRequired: true,
        dependsOn: ['date'],
        defaultValue() {
            return this.date;
        },
    })
    readonly fxRateDate: Promise<date>;

    @decorators.decimalProperty<SalesCreditMemo, 'totalAmountExcludingTaxInCompanyCurrency'>({
        isPublished: true,
        dataType: () => xtremSales.dataTypes.salesAmountDataType, // TODO: Has to have a computed length depending of the currency decimals !!!
        dependsOn: ['currency', { lines: ['amountExcludingTaxInCompanyCurrency'] }],
        async getValue() {
            return Decimal.roundAt(
                await this.lines.sum(line => line.amountExcludingTaxInCompanyCurrency),
                await (
                    await this.currency
                ).decimalDigits,
            );
        },
    })
    readonly totalAmountExcludingTaxInCompanyCurrency: Promise<decimal>;

    @decorators.decimalProperty<SalesCreditMemo, 'totalGrossProfit'>({
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.priceInSalesPrice,
        dependsOn: ['currency', { lines: ['grossProfitAmount'] }],
        getValue() {
            return this.lines.sum(line => line.grossProfitAmount);
        },
    })
    readonly totalGrossProfit: Promise<decimal>;

    // property needed for the accounting interface
    @decorators.datePropertyOverride<SalesCreditMemo, 'documentDate'>({
        dependsOn: ['date'],
        getValue() {
            return this.date;
        },
    })
    override readonly documentDate: Promise<date>;

    @decorators.enumProperty<SalesCreditMemo, 'financeIntegrationApp'>({
        isPublished: true,
        isNullable: true,
        dataType: () => xtremFinanceData.enums.financeIntegrationAppDataType,
        computeValue() {
            return xtremFinanceData.functions.getFinanceIntegrationAppFromOrigin(
                this.$.context,
                this._id,
                'salesCreditMemo',
            );
        },
    })
    readonly financeIntegrationApp: Promise<xtremFinanceData.enums.FinanceIntegrationApp | null>;

    @decorators.stringProperty<SalesCreditMemo, 'creationNumber'>({
        isStored: true,
        isPublished: true,
        isFrozen: true,
        dataType: () => xtremSales.dataTypes.propertyDataType,
        dependsOn: ['number'],
        deferredDefaultValue() {
            return xtremSales.functions.getCreationNumber(this);
        },
    })
    readonly creationNumber: Promise<string>;

    @decorators.collectionProperty<SalesCreditMemo, 'arOpenItems'>({
        isPublished: true,
        node: () => xtremFinanceData.nodes.BaseOpenItem,
        join: {
            documentType() {
                return 'salesCreditMemo';
            },
            documentSysId() {
                return this._id;
            },
        },
    })
    readonly arOpenItems: Collection<xtremFinanceData.nodes.BaseOpenItem>;

    @decorators.integerProperty<SalesCreditMemo, 'openItemSysId'>({
        isPublished: true,
        isNullable: true,
        async computeValue() {
            return (await this.arOpenItems.toArray()).at(0)?._id ?? null;
        },
    })
    readonly openItemSysId: Promise<number | null>;

    @decorators.collectionProperty<SalesCreditMemo, 'receipts'>({
        isPublished: true,
        node: () => xtremFinanceData.nodes.PaymentDocumentLine,
        join: {
            originalNodeFactory() {
                return '#SalesCreditMemo';
            },
            async originalOpenItem() {
                const openItem = (await this.arOpenItems.toArray()).at(0);
                return openItem ? { _id: openItem._id } : null;
            },
        },
    })
    readonly receipts: Collection<xtremFinanceData.nodes.PaymentDocumentLine>;

    // Property to show the total amount paid on the sales invoice if the paymentTrackingOption is active (standalone).
    // The properties transactionAmountPaid, companyAmountPaid and financialSiteAmountPaid are used to store the values
    // from Intacct
    @decorators.decimalProperty<SalesCreditMemo, 'totalAmountPaid'>({
        isPublished: true,
        isNullable: true,
        dependsOn: ['currency'],
        dataType: () => xtremMasterData.dataTypes.priceInSalesPrice,
        getValue() {
            return this.arOpenItems.sum(openItem => openItem.transactionAmountPaid);
        },
        serviceOptions: () => [xtremFinanceData.serviceOptions.paymentTrackingOption],
    })
    readonly totalAmountPaid: Promise<decimal | null>;

    // Property to show the forced amount paid on the sales invoice if the paymentTrackingOption is active (standalone).
    @decorators.decimalProperty<SalesCreditMemo, 'forcedAmountPaid'>({
        isPublished: true,
        isNullable: true,
        dependsOn: ['currency'],
        dataType: () => xtremMasterData.dataTypes.priceInSalesPrice,
        async getValue() {
            const openItem = await this.arOpenItems.takeOne(
                async arOpenItem =>
                    (await arOpenItem.documentType) === 'salesCreditMemo' &&
                    (await arOpenItem.documentNumber) === (await this.number),
            );
            return (await openItem?.forcedAmountPaid) ?? null;
        },
        serviceOptions: () => [xtremFinanceData.serviceOptions.paymentTrackingOption],
    })
    readonly forcedAmountPaid: Promise<decimal | null>;

    @decorators.decimalProperty<SalesCreditMemo, 'transactionAmountPaid'>({
        isPublished: true,
        isStored: true,
        dependsOn: ['currency'],
        dataType: () => xtremMasterData.dataTypes.priceInSalesPrice,
    })
    readonly transactionAmountPaid: Promise<decimal>;

    @decorators.decimalProperty<SalesCreditMemo, 'companyAmountPaid'>({
        isPublished: true,
        isStored: true,
        dependsOn: ['currency'],
        dataType: () => xtremMasterData.dataTypes.priceInSalesPrice,
    })
    readonly companyAmountPaid: Promise<decimal>;

    @decorators.decimalProperty<SalesCreditMemo, 'financialSiteAmountPaid'>({
        isPublished: true,
        isStored: true,
        dependsOn: ['currency'],
        dataType: () => xtremMasterData.dataTypes.priceInSalesPrice,
    })
    readonly financialSiteAmountPaid: Promise<decimal>;

    @decorators.textStreamPropertyOverride<SalesCreditMemo, 'internalNote'>({
        async isFrozen() {
            return xtremSales.functions.isPostedErrorClosed(await this.status);
        },
    })
    override readonly internalNote: Promise<TextStream>;

    @decorators.textStreamPropertyOverride<SalesCreditMemo, 'externalNote'>({
        async isFrozen() {
            return xtremSales.functions.isPostedErrorClosed(await this.status);
        },
    })
    override readonly externalNote: Promise<TextStream>;

    /** to be sure the user know that externalNode will go to external documents */
    @decorators.booleanPropertyOverride<SalesCreditMemo, 'isExternalNote'>({
        async isFrozen() {
            return xtremSales.functions.isPostedErrorClosed(await this.status);
        },
    })
    override readonly isExternalNote: Promise<boolean>;

    private calculateTaxExcludedTotalAmount(): Promise<decimal> {
        return this.lines.sum(line => line.amountExcludingTax);
    }

    @decorators.dateProperty<SalesCreditMemo, 'discountPaymentBeforeDate'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
        dependsOn: ['paymentTerm', 'date'],
        async defaultValue() {
            const paymentTerm = await this.paymentTerm;
            return xtremMasterData.sharedFunctions.getDiscountPaymentBeforeDate({
                discountType: (await paymentTerm.discountFrom) ?? 'afterInvoiceDate',
                discountDate: (await paymentTerm.discountDate) ?? 0,
                baseDate: await this.date,
            });
        },
        updatedValue: useDefaultValue,
    })
    readonly discountPaymentBeforeDate: Promise<date | null>;

    @decorators.collectionProperty<SalesCreditMemo, 'financeTransactions'>({
        isPublished: true,
        node: () => xtremFinanceData.nodes.FinanceTransaction,
        join: {
            documentNumber() {
                return this.number;
            },
            documentType: 'salesInvoice',
        },
    })
    readonly financeTransactions: Collection<xtremFinanceData.nodes.FinanceTransaction>;

    @decorators.collectionProperty<SalesCreditMemo, 'postingDetails'>({
        isPublished: true,
        join: {
            documentNumber() {
                return this.number;
            },
            documentType() {
                return { _in: ['salesCreditMemo', 'arInvoice'] };
            },
        },
        node: () => xtremFinanceData.nodes.FinanceTransaction,
        lookupAccess: true,
    })
    readonly postingDetails: Collection<xtremFinanceData.nodes.FinanceTransaction>;

    @decorators.booleanProperty<SalesCreditMemo, 'isOpenItemPageOptionActive'>({
        isPublished: true,
        serviceOptions: () => [xtremStructure.serviceOptions.openItemPageOption],
        getValue() {
            return true;
        },
    })
    readonly isOpenItemPageOptionActive: Promise<boolean>;

    @decorators.decimalProperty<SalesCreditMemo, 'netBalance'>({
        isPublished: true,
        isNullable: true,
        dependsOn: ['currency'],
        dataType: () => xtremMasterData.dataTypes.priceInSalesPrice,
        async getValue() {
            return (await this.totalAmountIncludingTax) - ((await this.totalAmountPaid) ?? 0);
        },
        serviceOptions: () => [xtremFinanceData.serviceOptions.paymentTrackingOption],
    })
    readonly netBalance: Promise<decimal | null>;

    private async updateRelatedSalesDocumentsStatuses(
        salesInvoicesArray: xtremSales.nodes.SalesInvoice[],
    ): Promise<void> {
        await xtremSales.classes.SalesCreditMemosCreator.updateRelatedSalesDocumentsStatuses({
            salesDocumentsArray: salesInvoicesArray,
            tempDict: '__salesInvoices',
            statusType: 'creditStatus',
            classInstance: this,
            aggregateNode: xtremSales.nodes.SalesInvoiceLineToSalesCreditMemoLine,
            enumDataType: xtremSales.enums.salesDocumentCreditStatusDataType,
        });

        const salesReturnRequestsArray = await this.$.context
            .query(xtremSales.nodes.SalesReturnRequest, {
                filter: { _id: { _in: Object.keys(this.__salesReturnRequests) } },
                forUpdate: true,
            })
            .toArray();
        await xtremSales.classes.SalesCreditMemosCreator.updateRelatedSalesDocumentsStatuses({
            salesDocumentsArray: salesReturnRequestsArray,
            tempDict: '__salesReturnRequests',
            statusType: 'creditStatus',
            classInstance: this,
            aggregateNode: xtremSales.nodes.SalesReturnRequestLineSalesCreditMemoLine,
            enumDataType: xtremSales.enums.salesDocumentCreditStatusDataType,
            updateQuantityInSalesUnit: true,
        });
    }

    static async beforePost(
        context: Context,
        creditMemo: SalesCreditMemo,
    ): Promise<{
        postResult: xtremFinanceData.interfaces.MutationResult;
        document: xtremSales.nodes.SalesCreditMemo | null;
    }> {
        const document = await SalesCreditMemo.getWritableNode(context, creditMemo);
        const postResult: xtremFinanceData.interfaces.MutationResult = {
            wasSuccessful: false,
            message: '',
        };

        const salesCreditMemoControlFromNotificationPayloadErrors = (
            await xtremSales.functions.FinanceIntegration.salesCreditMemoControlFromNotificationPayload(
                context,
                creditMemo,
            )
        ).validationMessages.filter(validationMessage => validationMessage.type === ValidationSeverity.error);

        if (salesCreditMemoControlFromNotificationPayloadErrors.length) {
            postResult.message = salesCreditMemoControlFromNotificationPayloadErrors
                .map(message => '* '.concat(message.message))
                .join('\n');
            return { postResult, document: null };
        }

        const status = await document.status;
        const taxCalculationStatus = await document.taxCalculationStatus;
        if (taxCalculationStatus !== 'failed' && status !== 'inProgress') {
            if (status !== 'draft') {
                postResult.message = context.localize(
                    '@sage/xtrem-sales/cant_post_credit_memo_when_status_is_not_draft',
                    'The status is not {{draft}}, the credit memo cannot be posted.',
                    {
                        draft: context.localizeEnumMember('@sage/xtrem-sales/SalesCreditMemoStatus', 'draft'),
                    },
                );
                throw new BusinessRuleError(postResult.message);
            }

            if (taxCalculationStatus !== 'done') {
                postResult.message = context.localize(
                    '@sage/xtrem-sales/cant_post_credit_memo_when_taxCalculationStatus_is_not_done',
                    'The tax calculation is not {{done}}, the credit memo cannot be posted.',
                    {
                        done: context.localizeEnumMember('@sage/xtrem-master-data/TaxCalculationStatus', 'done'),
                    },
                );
                throw new BusinessRuleError(postResult.message);
            }
        }

        xtremDistribution.functions.dateCheckControl(context, {
            discountPaymentBeforeDate: await document.discountPaymentBeforeDate,
            dueDate: await document.dueDate,
        });

        return { postResult, document };
    }

    static async postMainLogic(context: Context, documentWritable: SalesCreditMemo): Promise<void> {
        const status = await documentWritable.status;

        if (
            ['FR', 'ZA'].includes(await (await (await (await documentWritable.site).legalCompany).legislation).id) &&
            ((status !== 'inProgress' && (await documentWritable.taxCalculationStatus) !== 'failed') ||
                status !== 'posted')
        ) {
            const finalCreditMemoNumber = await (
                await xtremMasterData.classes.DocumentNumberGenerator.create(context, {
                    nodeInstance: documentWritable,
                    posting: true,
                })
            ).allocate();
            if (finalCreditMemoNumber) {
                await documentWritable.$.set({ number: finalCreditMemoNumber });
            }
        }

        // send notification in order to create a staging table entry for the accounting engine
        const postingResult = await xtremSales.functions.FinanceIntegration.salesCreditMemoNotification(
            context,
            documentWritable as xtremFinanceData.interfaces.SalesFinanceDocumentWithTax,
            (await documentWritable.lines.toArray()) as xtremSales.interfaces.FinanceIntegration.SalesCreditMemoDocumentLine[],
            'salesCreditMemo',
            'SalesCreditMemo/accountingInterface',
        );
        // if no notification was sent (all amounts=0), the document is considered as posted
        await documentWritable.$.set({
            forceUpdateForFinance: true,
            status: postingResult.notificationsSent ? 'inProgress' : 'posted',
        });
        documentWritable.__skipPrepare = true;
        await documentWritable.$.save();
    }

    @decorators.mutation<typeof SalesCreditMemo, 'post'>({
        isPublished: true,
        parameters: [
            {
                name: 'creditMemo',
                type: 'reference',
                isMandatory: true,
                isWritable: true,
                node: () => SalesCreditMemo,
            },
        ],
        return: {
            type: 'object',
            properties: {
                wasSuccessful: 'boolean',
                message: 'string',
            },
        },
    })
    static async post(
        context: Context,
        creditMemo: SalesCreditMemo,
    ): Promise<xtremFinanceData.interfaces.MutationResult> {
        const { postResult, document } = await this.beforePost(context, creditMemo);

        if (!document) {
            return postResult;
        }

        await this.postMainLogic(context, document);

        postResult.wasSuccessful = true;
        postResult.message = context.localize(
            '@sage/xtrem-sales/nodes__sales_credit_memo__posted',
            'The sales credit memo has been posted.',
        );
        return postResult;
    }

    @decorators.mutation<typeof SalesCreditMemo, 'repost'>({
        isPublished: true,
        parameters: [
            {
                name: 'salesCreditMemo',
                type: 'reference',
                isMandatory: true,
                isWritable: true,
                node: () => SalesCreditMemo,
            },
            {
                name: 'documentData',
                type: 'object',
                isMandatory: true,
                properties: {
                    header: {
                        type: 'object',
                        properties: {
                            paymentTerm: { type: 'reference', node: () => xtremMasterData.nodes.PaymentTerm },
                        },
                    },
                    lines: {
                        type: 'array',
                        item: {
                            type: 'object',
                            properties: {
                                baseDocumentLineSysId: 'integer',
                                storedAttributes: 'json',
                                storedDimensions: 'json',
                                uiTaxes: 'string',
                            },
                        },
                    },
                },
            },
        ],
        return: {
            type: 'object',
            properties: {
                wasSuccessful: 'boolean',
                message: 'string',
            },
        },
    })
    static repost(
        context: Context,
        salesCreditMemo: SalesCreditMemo,
        documentData: xtremSales.interfaces.FinanceIntegration.SalesInvoiceCreditMemoRepost,
    ): Promise<xtremFinanceData.interfaces.MutationResult> {
        return xtremSales.functions.SalesCreditMemoLib.repost(context, salesCreditMemo, documentData);
    }

    @decorators.mutation<typeof SalesCreditMemo, 'financeIntegrationCheck'>({
        isPublished: true,
        parameters: [
            {
                name: 'creditMemo',
                type: 'reference',
                isMandatory: true,
                isWritable: true,
                node: () => SalesCreditMemo,
            },
        ],
        return: {
            type: 'object',
            properties: {
                wasSuccessful: 'boolean',
                message: 'string',
            },
        },
    })
    static async financeIntegrationCheck(
        context: Context,
        creditMemo: SalesCreditMemo,
    ): Promise<xtremFinanceData.interfaces.MutationResult> {
        const financeIntegrationCheckResult: xtremFinanceData.interfaces.MutationResult = {
            wasSuccessful: false,
            message: '',
        };

        const salesCreditMemoControlFromNotificationPayloadErrors = (
            await xtremSales.functions.FinanceIntegration.salesCreditMemoControlFromNotificationPayload(
                context,
                creditMemo,
            )
        ).validationMessages.filter(validationMessage => validationMessage.type === ValidationSeverity.error);

        if (salesCreditMemoControlFromNotificationPayloadErrors.length) {
            financeIntegrationCheckResult.message = salesCreditMemoControlFromNotificationPayloadErrors
                .map(message => '* '.concat(message.message))
                .join('\n');
            return financeIntegrationCheckResult;
        }

        financeIntegrationCheckResult.wasSuccessful = true;
        return financeIntegrationCheckResult;
    }

    @decorators.notificationListener<typeof SalesCreditMemo>({
        topic: 'SalesCreditMemo/accountingInterface',
    })
    static async onFinanceIntegrationReply(
        context: Context,
        payload: xtremFinanceData.interfaces.FinanceTransactionData,
    ): Promise<void> {
        // isJustForStatus indicates that the update of finance transaction node was already done
        const shouldUpdateSalesCreditMemoStatus =
            payload.isJustForStatus ??
            (await context.runInWritableContext(writableContext => {
                return xtremFinanceData.functions.reactToFinanceIntegrationReply(writableContext, payload);
            }));

        if (shouldUpdateSalesCreditMemoStatus) {
            // we need to update the status of the original Sales credit memo depending on the reply from finance integration
            const creditMemo = await context.read(
                xtremSales.nodes.SalesCreditMemo,
                {
                    number: payload.documentNumber,
                },
                { forUpdate: true },
            );

            // the status of the credit memo is updated depending on the calculated field financeIntegrationStatus
            await creditMemo.$.set({
                status: xtremFinanceData.functions.statusMapping(
                    await creditMemo.financeIntegrationStatus,
                    !!payload.financeExternalIntegration,
                ),
                forceUpdateForFinance: true, // make sure the update of the Sales credit memo will not be refused by the control in saveBegin
            });

            await creditMemo.$.save();
        }
    }

    // Listener to update the payment status of a sales credit memo, when the open item is paid or partially paid in finance
    @decorators.notificationListener<typeof SalesCreditMemo>({
        topic: 'SalesCreditMemo/updatePaymentStatus',
        startsReadOnly: true,
    })
    static async setSalesCreditMemoPaymentStatus(
        readOnlyContext: Context,
        payload: { _id: number; paymentStatus: xtremFinanceData.enums.OpenItemStatus },
    ) {
        await readOnlyContext.runInWritableContext(async context => {
            const creditMemoToUpdate = await context.read(SalesCreditMemo, { _id: payload._id }, { forUpdate: true });
            await creditMemoToUpdate.$.set({
                forceUpdateForFinance: true,
                paymentStatus: payload.paymentStatus,
            });
            await creditMemoToUpdate.$.save();
        });
    }

    // Listener to initialize the payment status of a sales credit memo to notPaid, when the service option paymentTrackingOption is activated
    @decorators.notificationListener<typeof SalesCreditMemo>({
        topic: 'SysServiceOptionState/paymentTrackingOption/activate',
    })
    static async paymentTrackingOptionActivate(context: Context): Promise<void> {
        await context.bulkUpdate(xtremSales.nodes.SalesCreditMemo, {
            set: { paymentStatus: 'notPaid' },
            where: { paymentStatus: null },
        });
    }

    private static getWritableNode(
        context: Context,
        creditMemo: SalesCreditMemo,
    ): Promise<xtremSales.nodes.SalesCreditMemo> {
        return context.read(SalesCreditMemo, { _id: creditMemo._id }, { forUpdate: true });
    }

    private static async _setIsPrintedTrue(context: Context, creditMemo: SalesCreditMemo): Promise<void> {
        const updatePaidAmounts =
            (await creditMemo.arOpenItems.length) === 1 && (await creditMemo.financeIntegrationApp) === 'intacct';
        const arOpenItem = updatePaidAmounts && (await creditMemo.arOpenItems.elementAt(0));
        await creditMemo.$.set({
            isPrinted: true,
            transactionAmountPaid: updatePaidAmounts && arOpenItem ? await arOpenItem.transactionAmountPaid : undefined,
            companyAmountPaid: updatePaidAmounts && arOpenItem ? await arOpenItem.companyAmountPaid : undefined,
            financialSiteAmountPaid:
                updatePaidAmounts && arOpenItem ? await arOpenItem.financialSiteAmountPaid : undefined,
        });
        creditMemo.canUpdateIsPrinted = true;

        await creditMemo.$.save();
        // send notification in order to set the ar invoice printing status
        await xtremFinanceData.functions.notifyDocumentIsPrinted(
            context,
            'salesCreditMemo' as xtremFinanceData.enums.FinanceDocumentType,
            creditMemo._id,
        );
    }

    private async checkIfCreditMemoDateLaterThanToday(): Promise<void> {
        if ((await this.date) > date.today()) {
            throw new BusinessRuleError(
                this.$.context.localize(
                    '@sage/xtrem-sales/nodes__sales_credit_memo__future_credit_memo_date_not_allowed',
                    'The credit memo date cannot be later than today.',
                ),
            );
        }
    }

    /**
     * Toggle the isPrinted property to true
     * @param context
     * @param creditMemo
     * @returns boolean
     */
    @decorators.mutation<typeof SalesCreditMemo, 'setIsPrintedTrue'>({
        isPublished: true,
        parameters: [
            {
                name: 'creditMemo',
                type: 'reference',
                isMandatory: true,
                isWritable: true,
                node: () => SalesCreditMemo,
            },
        ],
        return: { type: 'boolean' },
    })
    static async setIsPrintedTrue(context: Context, creditMemo: SalesCreditMemo): Promise<boolean> {
        if (['posted', 'inProgress', 'error'].includes(await creditMemo.status)) {
            await this._setIsPrintedTrue(context, creditMemo);
        }
        return true;
    }

    /**
     * Operation that sends an email with an credit memo pdf attached to an email address.
     * @param context
     * @param creditMemo
     * @param contactTitle
     * @param contactLastName
     * @param contactFirstName
     * @param contactEmail
     */
    @decorators.mutation<typeof SalesCreditMemo, 'printSalesCreditMemoAndEmail'>({
        isPublished: true,
        startsReadOnly: true,
        parameters: [
            {
                name: 'creditMemo',
                type: 'reference',
                isMandatory: true,
                node: () => SalesCreditMemo,
            },
            { name: 'contactTitle', type: 'string', isMandatory: true },
            { name: 'contactLastName', type: 'string', isMandatory: true },
            { name: 'contactFirstName', type: 'string', isMandatory: true },
            { name: 'contactEmail', type: 'string', isMandatory: true },
        ],
        return: 'boolean',
    })
    static async printSalesCreditMemoAndEmail(
        context: Context,
        creditMemo: SalesCreditMemo,
        contactTitle: string,
        contactLastName: string,
        contactFirstName: string,
        contactEmail: string,
    ): Promise<boolean> {
        const reports = await xtremMasterData.functions.reportGeneration(context, 'salesCreditMemo', '', [
            JSON.stringify({ creditMemo: creditMemo._id }),
        ]);

        if (reports.length === 0) {
            throw new BusinessRuleError(
                context.localize(
                    '@sage/xtrem-sales/nodes__sales_credit_memo__nothing_to_send',
                    'The sales credit memo cannot be sent. No report has been created.',
                ),
            );
        }

        // prepare the email template data
        const formattedCreditMemoDate = (await creditMemo.date).format('DD/MM/YYYY');
        const formattedTitle = contactTitle
            ? contactTitle[0].toUpperCase() + contactTitle.substring(1, contactTitle.length).toLowerCase()
            : '';
        const data = {
            creditMemo: {
                number: await creditMemo.number,
                date: formattedCreditMemoDate,
                totalAmountIncludingTax: (await creditMemo.totalAmountIncludingTax).toString(),
                currencySymbol: await (await creditMemo.currency).symbol,
            },
            contact: { title: formattedTitle, firstName: contactFirstName, lastName: contactLastName },
            site: { name: await creditMemo.salesSiteName },
        } as xtremSales.interfaces.SalesCreditMemoEmailTemplateData;
        const subject = context.localize(
            '@sage/xtrem-sales/sales_credit_memo__email_subject',
            '{{creditMemoSiteName}}: Credit memo {{creditMemoNumber}}',
            {
                creditMemoSiteName: data.site.name,
                creditMemoNumber: data.creditMemo.number,
            },
        );

        // email recipients
        const recipients: xtremMailer.interfaces.Recipients = { to: [{ address: contactEmail, name: contactEmail }] };

        // send the email
        await xtremMasterData.functions.sendReportsByMail(
            context,
            'sales_credit_memo_send',
            data,
            subject,
            recipients,
            `Sales credit memo - ${await creditMemo.number}`,
            reports,
            creditMemo,
        );
        await context.runInWritableContext(async writableContext => {
            const creditMemoToUpdate = await writableContext.read(
                SalesCreditMemo,
                { _id: creditMemo._id },
                { forUpdate: true },
            );
            await creditMemoToUpdate.$.set({ canUpdateIsSent: true, isSent: true });
            await creditMemoToUpdate.$.save();
        });

        return true;
    }

    /**
     * Checks if the credit memo can be printed
     * @param context
     * @param creditMemo
     * @returns true if the credit memo can be printed
     */
    @decorators.mutation<typeof xtremSales.nodes.SalesCreditMemo, 'beforePrintSalesCreditMemo'>({
        isPublished: true,
        startsReadOnly: true,
        parameters: [
            {
                name: 'creditMemo',
                type: 'reference',
                isMandatory: true,
                node: () => xtremSales.nodes.SalesCreditMemo,
            },
        ],
        return: 'boolean',
    })
    static async beforePrintSalesCreditMemo(readonlyContext: Context, creditMemo: SalesCreditMemo): Promise<boolean> {
        if (!(await xtremSales.functions.checkIfCanPrintSalesInvoice(readonlyContext, creditMemo, true)).canPrint) {
            return false;
        }
        return readonlyContext.runInWritableContext(async writableContext => {
            const writableCreditMemo = await writableContext.read(
                SalesCreditMemo,
                { _id: creditMemo._id },
                { forUpdate: true },
            );
            return xtremSales.classes.SalesDocumentHooks.beforePrint(writableContext, writableCreditMemo);
        });
    }

    /**
     * Updates the isPrinted flag after the credit memo has been printed
     * @param readonlyContext
     * @param creditMemo
     * @returns boolean
     */
    @decorators.mutation<typeof xtremSales.nodes.SalesCreditMemo, 'afterPrintSalesCreditMemo'>({
        isPublished: true,
        startsReadOnly: true,
        parameters: [
            {
                name: 'creditMemo',
                type: 'reference',
                isMandatory: true,
                node: () => xtremSales.nodes.SalesCreditMemo,
            },
        ],
        return: 'boolean',
    })
    static async afterPrintSalesCreditMemo(readonlyContext: Context, creditMemo: SalesCreditMemo): Promise<boolean> {
        const number = await creditMemo.number;

        if (!(await creditMemo.isPrinted)) {
            await readonlyContext.runInWritableContext(async writableContext => {
                const writableCreditMemo = await writableContext.read(
                    SalesCreditMemo,
                    { _id: creditMemo._id },
                    { forUpdate: true },
                );
                return SalesCreditMemo.setIsPrintedTrue(writableContext, writableCreditMemo);
            });
            await readonlyContext.batch.logMessage('info', `Sales credit memo ${number} marked as printed.`);
        } else {
            await readonlyContext.batch.logMessage(
                'info',
                `Sales credit memo ${number} is already marked as printed. Skipping update.`,
            );
        }
        return true;
    }

    @decorators.mutation<typeof SalesCreditMemo, 'resendNotificationForFinance'>({
        isPublished: true,
        parameters: [{ name: 'salesCreditMemo', type: 'reference', isMandatory: true, node: () => SalesCreditMemo }],
        return: { type: 'boolean' },
    })
    static async resendNotificationForFinance(context: Context, salesCreditMemo: SalesCreditMemo): Promise<boolean> {
        logger.info(
            context.localize(
                '@sage/xtrem-sales/node__sales_credit_memo__resend_notification_for_finance',
                'Resending finance notification for sales credit memo: {{salesCreditMemoNumber}}',
                { salesCreditMemoNumber: await salesCreditMemo.number },
            ),
        );

        if (
            await xtremFinanceData.functions.AccountingEngineCommon.validateResendNotificationForFinance(context, {
                documentNumber: await salesCreditMemo.number,
                documentType: 'salesCreditMemo',
            })
        ) {
            if (await (await (await salesCreditMemo.site).legalCompany).doStockPosting) {
                await xtremSales.functions.FinanceIntegration.salesCreditMemoNotification(
                    context,
                    salesCreditMemo as xtremFinanceData.interfaces.SalesFinanceDocumentWithTax,
                    (await salesCreditMemo.lines.toArray()) as xtremSales.interfaces.FinanceIntegration.SalesCreditMemoDocumentLine[],
                    'salesCreditMemo',
                    'SalesCreditMemo/accountingInterface',
                );
            }
        }

        return true;
    }

    private async saveBeginModified(
        documentNumberGenerator: xtremMasterData.classes.DocumentNumberGenerator,
    ): Promise<void> {
        const oldNode = await this.$.old;
        if (
            !this.canUpdateIsPrinted &&
            !(await this.canUpdateIsSent) &&
            !(await this.forceUpdateForFinance) &&
            ['posted', 'inProgress', 'error'].includes(await oldNode.status)
        ) {
            throw new BusinessRuleError(
                this.$.context.localize(
                    '@sage/xtrem-sales/nodes__sales_credit_memo__update_not_allowed_status_posted',
                    'The sales credit memo is posted. You cannot update it.',
                ),
            );
        }

        const creditMemoDate = await this.date;
        const oldCreditMemoDate = await oldNode.date;
        if (
            creditMemoDate !== oldCreditMemoDate &&
            !(await xtremMasterData.functions.sequenceNumberGeneratorLibrary.areDatesInSamePeriod(
                await documentNumberGenerator.sequenceNumber,
                [creditMemoDate, oldCreditMemoDate],
            ))
        ) {
            throw new BusinessRuleError(
                this.$.context.localize(
                    '@sage/xtrem-sales/nodes__sales_credit_memo__new_credit_memo_date_not_in_right_period',
                    'The new credit memo date must be in the same period.',
                ),
            );
        }
    }

    @decorators.bulkMutation<typeof SalesCreditMemo, 'printBulkSalesCreditMemo'>({
        isPublished: true,
        startsReadOnly: true,
        queue: 'reporting',
        async onComplete(context, reports) {
            const reportName = context.localize(
                '@sage/xtrem-sales/node__sales_credit_memo_bulk_print_report_name',
                'Sales credit memo',
            );
            await xtremMasterData.functions.bulkPrintOnCompleteHandler(context, {
                documentType: 'salesCreditMemo',
                documents: reports,
                reportName,
            });
        },
    })
    static async printBulkSalesCreditMemo(context: Context, creditMemo: SalesCreditMemo) {
        const canPrint = await context.runInWritableContext(async writableContext => {
            const writableCreditMemo = await writableContext.read(
                xtremSales.nodes.SalesCreditMemo,
                { _id: creditMemo._id },
                { forUpdate: true },
            );
            return writableCreditMemo.beforePrintAndMail();
        });

        if (!canPrint) return {};

        const report = await xtremReporting.nodes.Report.generateReportPdf(context, 'salesCreditMemo', '', {
            variables: JSON.stringify({
                creditMemo: creditMemo._id,
            }),
            isBulk: true,
        });

        await context.runInWritableContext(async writableContext => {
            const writableCreditMemo = await writableContext.read(
                xtremSales.nodes.SalesCreditMemo,
                { _id: creditMemo._id },
                { forUpdate: true },
            );

            await xtremSales.nodes.SalesCreditMemo.setIsPrintedTrue(writableContext, writableCreditMemo);
        });

        return report;
    }

    @decorators.notificationListener<typeof SalesCreditMemo>({
        topic: 'SalesCreditMemo/resendNotificationForFinance',
    })
    static async onResendNotificationForFinance(context: Context, document: { number: number }) {
        const salesCreditMemo = await context.read(SalesCreditMemo, { number: document.number });

        await SalesCreditMemo.resendNotificationForFinance(context, salesCreditMemo);
    }

    beforePrintAndMail(): Promise<boolean> {
        return xtremSales.functions.SalesInvoiceLib.beforePrintAndMail(this.$.context, this);
    }

    /**
     * Checks if the credit memo can be printed
     * @returns true if the credit memo can be printed
     */
    @decorators.mutation<typeof xtremSales.nodes.SalesCreditMemo, 'beforePrint'>({
        isPublished: true,
        startsReadOnly: true,
        parameters: [
            {
                name: 'creditMemo',
                type: 'reference',
                isMandatory: true,
                node: () => xtremSales.nodes.SalesCreditMemo,
            },
        ],
        return: 'boolean',
    })
    static beforePrint(readonlyContext: Context, creditMemo: SalesCreditMemo): Promise<boolean> {
        return readonlyContext.runInWritableContext(async writableContext => {
            const writableCreditMemo = await writableContext.read(
                xtremSales.nodes.SalesCreditMemo,
                { _id: creditMemo._id },
                { forUpdate: true },
            );
            return writableCreditMemo.beforePrintAndMail();
        });
    }

    @decorators.mutation<typeof SalesCreditMemo, 'enforceStatusPosted'>({
        isPublished: true,
        parameters: [
            {
                name: 'creditMemo',
                type: 'reference',
                isMandatory: true,
                isWritable: true,
                node: () => SalesCreditMemo,
            },
        ],
        return: 'boolean',
    })
    static async enforceStatusPosted(context: Context, creditMemo: xtremSales.nodes.SalesCreditMemo): Promise<boolean> {
        const status = await creditMemo.status;
        const displayStatus = await creditMemo.displayStatus;
        const allPosted = await creditMemo.postingDetails.every(
            async detail => (await detail.postingStatus) === 'posted',
        );
        if (status === 'inProgress' && displayStatus === 'postingInProgress' && allPosted) {
            await creditMemo.$.set({ status: 'posted', displayStatus: 'posted', forceUpdateForFinance: true });
            await creditMemo.$.save();
        }
        return true;
    }
}
