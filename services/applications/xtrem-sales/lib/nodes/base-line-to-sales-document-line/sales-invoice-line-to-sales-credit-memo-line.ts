import type { Reference } from '@sage/xtrem-core';
import { decorators } from '@sage/xtrem-core';
import * as xtremSales from '../../index';
import { BaseLineToSalesDocumentLine } from './base-line-to-sales-document-line';

@decorators.subNode<SalesInvoiceLineToSalesCreditMemoLine>({
    extends: () => BaseLineToSalesDocumentLine,
    isPublished: true,
    isVitalCollectionChild: true,
    canRead: true,
    canSearch: true,
})
export class SalesInvoiceLineToSalesCreditMemoLine extends BaseLineToSalesDocumentLine {
    @decorators.referenceProperty<SalesInvoiceLineToSalesCreditMemoLine, 'document'>({
        isStored: true,
        isPublished: true,
        isVitalParent: true,
        node: () => xtremSales.nodes.SalesCreditMemoLine,
    })
    readonly document: Reference<xtremSales.nodes.SalesCreditMemoLine>;

    @decorators.referenceProperty<SalesInvoiceLineToSalesCreditMemoLine, 'linkedDocument'>({
        isStored: true,
        isPublished: true,
        node: () => xtremSales.nodes.SalesInvoiceLine,
        lookupAccess: true,
    })
    readonly linkedDocument: Reference<xtremSales.nodes.SalesInvoiceLine>;
}
