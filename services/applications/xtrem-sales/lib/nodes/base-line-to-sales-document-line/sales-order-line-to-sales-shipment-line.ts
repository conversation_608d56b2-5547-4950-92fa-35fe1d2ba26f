import type { Reference } from '@sage/xtrem-core';
import { decorators } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremSales from '../../index';
import { BaseLineToSalesDocumentLine } from './base-line-to-sales-document-line';

@decorators.subNode<SalesOrderLineToSalesShipmentLine>({
    extends: () => BaseLineToSalesDocumentLine,
    isPublished: true,
    isVitalCollectionChild: true,
    canRead: true,
    canSearch: true,
    indexes: [{ orderBy: { linkedDocument: 1 } }],
    async saveEnd() {
        // as the WorkInProgressSalesOrderLine record is not in the same vital graph, the update must be done explicitly
        if ((await (await this.linkedDocument).workInProgress)?._id)
            await xtremMasterData.nodes.WorkInProgress.updateQuantities(
                this.$.context,
                (await (
                    await this.linkedDocument
                ).workInProgress)!._id,
            );
    },
})
export class SalesOrderLineToSalesShipmentLine extends BaseLineToSalesDocumentLine {
    @decorators.referenceProperty<SalesOrderLineToSalesShipmentLine, 'document'>({
        isStored: true,
        isPublished: true,
        isVitalParent: true,
        lookupAccess: true,
        node: () => xtremSales.nodes.SalesShipmentLine,
    })
    readonly document: Reference<xtremSales.nodes.SalesShipmentLine>;

    @decorators.referenceProperty<SalesOrderLineToSalesShipmentLine, 'linkedDocument'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        node: () => xtremSales.nodes.SalesOrderLine,
    })
    readonly linkedDocument: Reference<xtremSales.nodes.SalesOrderLine>;
}
