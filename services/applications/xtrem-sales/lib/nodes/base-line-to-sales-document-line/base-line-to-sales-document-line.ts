import type { decimal } from '@sage/xtrem-core';
import { decorators, Node } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';

@decorators.node<BaseLineToSalesDocumentLine>({
    isClearedByReset: true,
    storage: 'sql',
    isAbstract: true,
    isPublished: true,
    isCustomizable: true,
})
export class BaseLineToSalesDocumentLine extends Node {
    @decorators.decimalProperty<BaseLineToSalesDocumentLine, 'quantityInStockUnit'>({
        isStored: true,
        isPublished: true,
        isNotZero: true,
        dataType: () => xtremMasterData.dataTypes.quantityInStockUnit,
        async control(cx, val) {
            await cx.error.if(val).is.negative();
        },
    })
    readonly quantityInStockUnit: Promise<decimal>;

    @decorators.decimalProperty<BaseLineToSalesDocumentLine, 'quantity'>({
        isStored: true,
        isPublished: true,
        isNotZero: true,
        dataType: () => xtremMasterData.dataTypes.quantityInSalesUnit,
        async control(cx, val) {
            await cx.error.if(val).is.negative();
        },
    })
    readonly quantity: Promise<decimal>;

    // @TODO DELETE AFTER BASE DOCUMENT REFACTORING
    @decorators.decimalProperty<BaseLineToSalesDocumentLine, 'quantityInSalesUnit'>({
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.quantityInSalesUnit,
        getValue() {
            return this.quantity;
        },
    })
    readonly quantityInSalesUnit: Promise<decimal>;

    @decorators.decimalProperty<BaseLineToSalesDocumentLine, 'amount'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
        isRequired: false,
        dataType: () => xtremMasterData.dataTypes.price,
        async control(cx, val) {
            await cx.error.if(val).is.negative();
        },
    })
    readonly amount: Promise<decimal | null>;
}
