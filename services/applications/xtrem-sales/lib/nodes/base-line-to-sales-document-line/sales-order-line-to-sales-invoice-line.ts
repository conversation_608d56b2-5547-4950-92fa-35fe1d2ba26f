import type { Reference } from '@sage/xtrem-core';
import { decorators } from '@sage/xtrem-core';
import * as xtremSales from '../../index';
import { BaseLineToSalesDocumentLine } from './base-line-to-sales-document-line';

@decorators.subNode<SalesOrderLineToSalesInvoiceLine>({
    extends: () => BaseLineToSalesDocumentLine,
    isPublished: true,
    isVitalCollectionChild: true,
    canRead: true,
    canSearch: true,
})
export class SalesOrderLineToSalesInvoiceLine extends BaseLineToSalesDocumentLine {
    @decorators.referenceProperty<SalesOrderLineToSalesInvoiceLine, 'document'>({
        isStored: true,
        isPublished: true,
        isVitalParent: true,
        node: () => xtremSales.nodes.SalesInvoiceLine,
    })
    readonly document: Reference<xtremSales.nodes.SalesInvoiceLine>;

    @decorators.referenceProperty<SalesOrderLineToSalesInvoiceLine, 'linkedDocument'>({
        isStored: true,
        isPublished: true,
        node: () => xtremSales.nodes.SalesOrderLine,
        lookupAccess: true,
    })
    readonly linkedDocument: Reference<xtremSales.nodes.SalesOrderLine>;
}
