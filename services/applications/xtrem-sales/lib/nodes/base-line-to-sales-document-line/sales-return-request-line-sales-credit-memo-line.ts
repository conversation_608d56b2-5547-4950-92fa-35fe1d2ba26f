import type { Reference } from '@sage/xtrem-core';
import { decorators } from '@sage/xtrem-core';
import * as xtremSales from '../../index';
import { BaseLineToSalesDocumentLine } from './base-line-to-sales-document-line';

@decorators.subNode<SalesReturnRequestLineSalesCreditMemoLine>({
    extends: () => BaseLineToSalesDocumentLine,
    isPublished: true,
    isVitalCollectionChild: true,
    canRead: true,
    canSearch: true,
})
export class SalesReturnRequestLineSalesCreditMemoLine extends BaseLineToSalesDocumentLine {
    @decorators.referenceProperty<SalesReturnRequestLineSalesCreditMemoLine, 'document'>({
        isStored: true,
        isPublished: true,
        isVitalParent: true,
        node: () => xtremSales.nodes.SalesCreditMemoLine,
    })
    readonly document: Reference<xtremSales.nodes.SalesCreditMemoLine>;

    @decorators.referenceProperty<SalesReturnRequestLineSalesCreditMemoLine, 'linkedDocument'>({
        isStored: true,
        isPublished: true,
        node: () => xtremSales.nodes.SalesReturnRequestLine,
        lookupAccess: true,
    })
    readonly linkedDocument: Reference<xtremSales.nodes.SalesReturnRequestLine>;
}
