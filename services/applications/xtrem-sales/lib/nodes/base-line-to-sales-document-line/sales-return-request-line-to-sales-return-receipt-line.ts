import type { Reference } from '@sage/xtrem-core';
import { decorators } from '@sage/xtrem-core';
import * as xtremSales from '../../index';
import { BaseLineToSalesDocumentLine } from './base-line-to-sales-document-line';

@decorators.subNode<SalesReturnRequestLineToSalesReturnReceiptLine>({
    extends: () => BaseLineToSalesDocumentLine,
    isPublished: true,
    isVitalCollectionChild: true,
    canRead: true,
    canSearch: true,
})
export class SalesReturnRequestLineToSalesReturnReceiptLine extends BaseLineToSalesDocumentLine {
    @decorators.referenceProperty<SalesReturnRequestLineToSalesReturnReceiptLine, 'document'>({
        isStored: true,
        isPublished: true,
        isVitalParent: true,
        node: () => xtremSales.nodes.SalesReturnReceiptLine,
    })
    readonly document: Reference<xtremSales.nodes.SalesReturnReceiptLine>;

    @decorators.referenceProperty<SalesReturnRequestLineToSalesReturnReceiptLine, 'linkedDocument'>({
        isStored: true,
        isPublished: true,
        node: () => xtremSales.nodes.SalesReturnRequestLine,
        lookupAccess: true,
    })
    readonly linkedDocument: Reference<xtremSales.nodes.SalesReturnRequestLine>;
}
