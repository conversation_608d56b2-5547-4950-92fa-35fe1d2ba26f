import type { Reference } from '@sage/xtrem-core';
import { decorators } from '@sage/xtrem-core';
import * as xtremSales from '../../index';
import { BaseLineToSalesDocumentLine } from './base-line-to-sales-document-line';

@decorators.subNode<SalesShipmentLineToSalesReturnReceiptLine>({
    extends: () => BaseLineToSalesDocumentLine,
    isPublished: true,
    isVitalCollectionChild: true,
    canRead: true,
    canSearch: true,
})
export class SalesShipmentLineToSalesReturnReceiptLine extends BaseLineToSalesDocumentLine {
    @decorators.referenceProperty<SalesShipmentLineToSalesReturnReceiptLine, 'document'>({
        isStored: true,
        isPublished: true,
        isVitalParent: true,
        node: () => xtremSales.nodes.SalesReturnReceiptLine,
    })
    readonly document: Reference<xtremSales.nodes.SalesReturnReceiptLine>;

    @decorators.referenceProperty<SalesShipmentLineToSalesReturnReceiptLine, 'linkedDocument'>({
        isStored: true,
        isPublished: true,
        node: () => xtremSales.nodes.SalesShipmentLine,
        lookupAccess: true,
    })
    readonly linkedDocument: Reference<xtremSales.nodes.SalesShipmentLine>;
}
