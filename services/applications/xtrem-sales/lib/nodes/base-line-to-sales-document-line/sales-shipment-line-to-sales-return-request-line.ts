import type { Reference } from '@sage/xtrem-core';
import { decorators } from '@sage/xtrem-core';
import * as xtremSales from '../../index';
import { BaseLineToSalesDocumentLine } from './base-line-to-sales-document-line';

@decorators.subNode<SalesShipmentLineToSalesReturnRequestLine>({
    extends: () => BaseLineToSalesDocumentLine,
    isPublished: true,
    isVitalCollectionChild: true,
    canRead: true,
    canSearch: true,
})
export class SalesShipmentLineToSalesReturnRequestLine extends BaseLineToSalesDocumentLine {
    @decorators.referenceProperty<SalesShipmentLineToSalesReturnRequestLine, 'document'>({
        isStored: true,
        isPublished: true,
        isVitalParent: true,
        node: () => xtremSales.nodes.SalesReturnRequestLine,
    })
    readonly document: Reference<xtremSales.nodes.SalesReturnRequestLine>;

    @decorators.referenceProperty<SalesShipmentLineToSalesReturnRequestLine, 'linkedDocument'>({
        isStored: true,
        isPublished: true,
        node: () => xtremSales.nodes.SalesShipmentLine,
        lookupAccess: true,
    })
    readonly linkedDocument: Reference<xtremSales.nodes.SalesShipmentLine>;
}
