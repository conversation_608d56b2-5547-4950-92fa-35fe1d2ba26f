import type { date, decimal, integer, Reference } from '@sage/xtrem-core';
import { decorators, useDefaultValue } from '@sage/xtrem-core';
import { Decimal } from '@sage/xtrem-decimal';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremStockData from '@sage/xtrem-stock-data';
import type * as xtremSystem from '@sage/xtrem-system';
import * as xtremSales from '../index';

@decorators.subNode<WorkInProgressSalesOrderLine>({
    isPublished: true,
    extends: () => xtremMasterData.nodes.WorkInProgress,
    canRead: true,
    canSearch: true,
    isVitalReferenceChild: true,
})
export class WorkInProgressSalesOrderLine extends xtremMasterData.nodes.WorkInProgress {
    @decorators.referenceProperty<WorkInProgressSalesOrderLine, 'salesOrderLine'>({
        isPublished: true,
        isStored: true,
        isVitalParent: true,
        node: () => xtremSales.nodes.SalesOrderLine,
    })
    readonly salesOrderLine: Reference<xtremSales.nodes.SalesOrderLine>;

    @decorators.referencePropertyOverride<WorkInProgressSalesOrderLine, 'item'>({
        dependsOn: [{ salesOrderLine: ['item'] }],
        // FIXME: we cannot yet query on computed properties; for now, we set isStored on some and defaultValue to
        async defaultValue() {
            return (await this.salesOrderLine).item;
        },
        updatedValue: useDefaultValue,
    })
    override readonly item: Reference<xtremMasterData.nodes.Item | null>;

    @decorators.referencePropertyOverride<WorkInProgressSalesOrderLine, 'site'>({
        dependsOn: [{ salesOrderLine: ['stockSite'] }],
        // FIXME: we cannot yet query on computed properties; for now, we set isStored on some and defaultValue to
        async defaultValue() {
            return (await this.salesOrderLine).stockSite;
        },
        updatedValue: useDefaultValue,
    })
    override readonly site: Reference<xtremSystem.nodes.Site | null>;

    private async _setStatus(): Promise<xtremMasterData.enums.OrderType> {
        switch (await (await this.salesOrderLine).status) {
            case 'quote':
                return 'suggested';
            case 'pending':
                return 'planned';
            case 'inProgress':
                return 'firm';
            case 'closed':
                return 'closed';
            default:
                return 'closed';
        }
    }

    @decorators.enumPropertyOverride<WorkInProgressSalesOrderLine, 'status'>({
        dependsOn: [{ salesOrderLine: ['status'] }],
        // FIXME: we cannot yet query on computed properties; for now, we set isStored on some and defaultValue to
        defaultValue() {
            return this._setStatus();
        },
        updatedValue: useDefaultValue,
    })
    override readonly status: Promise<xtremMasterData.enums.OrderType | null>;

    @decorators.datePropertyOverride<WorkInProgressSalesOrderLine, 'startDate'>({
        dependsOn: [{ salesOrderLine: [{ document: ['date'] }] }],
        // FIXME: we cannot yet query on computed properties; for now, we set isStored on some and defaultValue to
        async defaultValue() {
            return (await (await this.salesOrderLine).document).date;
        },
        updatedValue: useDefaultValue,
    })
    override readonly startDate: Promise<date | null>;

    @decorators.datePropertyOverride<WorkInProgressSalesOrderLine, 'endDate'>({
        dependsOn: [{ salesOrderLine: ['shippingDate'] }],
        // FIXME: we cannot yet query on computed properties; for now, we set isStored on some and defaultValue to
        async defaultValue() {
            return (await this.salesOrderLine).shippingDate;
        },
        updatedValue: useDefaultValue,
    })
    override readonly endDate: Promise<date | null>;

    /* quantities are expressed in item.stockUnit */
    @decorators.decimalPropertyOverride<WorkInProgressSalesOrderLine, 'expectedQuantity'>({
        dependsOn: [{ salesOrderLine: ['quantityInStockUnit'] }],
        defaultValue() {
            return this.determineExpectedQuantity();
        },
        updatedValue: useDefaultValue,
    })
    override readonly expectedQuantity: Promise<decimal>;

    @decorators.decimalPropertyOverride<WorkInProgressSalesOrderLine, 'actualQuantity'>({
        dependsOn: [
            {
                salesOrderLine: [
                    'shippedQuantityInSalesUnit',
                    'unitToStockUnitConversionFactor',
                    { stockUnit: ['decimalDigits'] },
                ],
            },
        ],
        defaultValue() {
            return this.determineActualQuantity();
        },
        updatedValue: useDefaultValue,
    })
    override readonly actualQuantity: Promise<decimal>;

    @decorators.decimalPropertyOverride<WorkInProgressSalesOrderLine, 'outstandingQuantity'>({
        dependsOn: ['expectedQuantity', 'actualQuantity'],
        async defaultValue() {
            return Math.max((await this.expectedQuantity) - (await this.actualQuantity), 0);
        },
        updatedValue: useDefaultValue,
    })
    override readonly outstandingQuantity: Promise<decimal>;

    @decorators.decimalPropertyOverride<WorkInProgressSalesOrderLine, 'remainingQuantityToAllocate'>({
        dependsOn: [{ salesOrderLine: ['stockSite', 'item', 'quantityInStockUnit'] }],
        computeValue() {
            return this.determineRemainingQuantityToAllocate();
        },
    })
    override readonly remainingQuantityToAllocate: Promise<decimal>;

    @decorators.enumPropertyOverride<WorkInProgressSalesOrderLine, 'documentType'>({
        // FIXME: we cannot yet query on computed properties; for now, we set isStored on some and defaultValue to
        defaultValue() {
            return 'salesOrder';
        },
    })
    override readonly documentType: Promise<xtremMasterData.enums.WorkInProgressDocumentType | null>;

    @decorators.stringPropertyOverride<WorkInProgressSalesOrderLine, 'documentNumber'>({
        dependsOn: [{ salesOrderLine: [{ document: ['number'] }] }],
        async getValue() {
            return (await (await this.salesOrderLine).document).number;
        },
    })
    override readonly documentNumber: Promise<string>;

    @decorators.integerPropertyOverride<WorkInProgressSalesOrderLine, 'documentLine'>({
        dependsOn: [{ salesOrderLine: ['_sortValue'] }],
        async getValue() {
            return (await this.salesOrderLine)._sortValue;
        },
    })
    override readonly documentLine: Promise<integer>;

    @decorators.integerPropertyOverride<WorkInProgressSalesOrderLine, 'documentId'>({
        async getValue() {
            return (await (await this.salesOrderLine).document)._id;
        },
    })
    override readonly documentId: Promise<integer>;

    @decorators.enumPropertyOverride<WorkInProgressSalesOrderLine, 'originDocumentType'>({
        getValue() {
            return null;
        },
    })
    override readonly originDocumentType: Promise<xtremMasterData.enums.WorkInProgressDocumentType | null>;

    static async shippedQuantityInStockUnitComputed(salesOrderLine: xtremSales.nodes.SalesOrderLine): Promise<number> {
        return +new Decimal(
            (await salesOrderLine.shippedQuantityInSalesUnit) * (await salesOrderLine.unitToStockUnitConversionFactor),
        ).toDecimalPlaces((await (await salesOrderLine.stockUnit)?.decimalDigits) || 0);
    }

    override async determineExpectedQuantity(): Promise<decimal> {
        return (await this.salesOrderLine).quantityInStockUnit;
    }

    override async determineActualQuantity(): Promise<decimal> {
        return WorkInProgressSalesOrderLine.shippedQuantityInStockUnitComputed(await this.salesOrderLine);
    }

    override async determineRemainingQuantityToAllocate(): Promise<decimal> {
        const salesOrderLine = await this.salesOrderLine;
        const totalRemainingQuantityToShipInStockUnit = await salesOrderLine.remainingQuantityToShipInStockUnit;
        const totalAllocatedQuantity =
            (
                await this.$.context
                    .queryAggregate(xtremStockData.nodes.StockAllocation, {
                        filter: {
                            stockRecord: {
                                site: (await salesOrderLine.stockSite)._id,
                                item: (await salesOrderLine.item)._id,
                                isInTransit: false,
                            },
                            documentLine: salesOrderLine._id,
                        },
                        group: {},
                        values: { quantityInStockUnit: { sum: true } },
                    })
                    .toArray()
            )[0]?.values.quantityInStockUnit.sum || 0;
        return totalRemainingQuantityToShipInStockUnit - totalAllocatedQuantity;
    }
}
