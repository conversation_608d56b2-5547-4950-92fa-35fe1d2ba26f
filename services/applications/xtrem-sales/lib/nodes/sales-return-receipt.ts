import type { NotificationEnvelope } from '@sage/xtrem-communication';
import type { Collection, Context, decimal, Dict, integer, Node, Reference, TextStream } from '@sage/xtrem-core';
import { asyncArray, BusinessRuleError, date, decorators, Logger, useDefaultValue } from '@sage/xtrem-core';
import * as xtremFinanceData from '@sage/xtrem-finance-data';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremStockData from '@sage/xtrem-stock-data';
import type * as xtremSystem from '@sage/xtrem-system';
import { isPostedErrorClosed } from '../functions/sales-document-common-lib';
import {
    getStockDetailStatus,
    linkedReturnRequestsArray,
    updateHeaderNotesOnCreation,
    validatePost,
} from '../functions/sales-return-receipt-lib';
import * as xtremSales from '../index';

const logger = Logger.getLogger(__filename, 'sales-return-receipt');

@decorators.subNode<SalesReturnReceipt>({
    extends: () => xtremMasterData.nodes.BaseDocument,
    isPublished: true,
    canCreate: true,
    canRead: true,
    canUpdate: true,
    canDelete: true,
    isCustomizable: true,
    hasAttachments: true,
    async controlBegin(cx) {
        await xtremMasterData.events.control.baseDocumentControls.atLeastOneLine(cx, this);
        await xtremSales.events.controlBegin.returnReceipt.checkSiteOnUpdate(cx, this);
        await xtremSales.events.controlBegin.returnReceipt.checkShipToCustomerOnUpdate(cx, this);
        await xtremSales.events.controlBegin.returnReceipt.checkReturnDateOnUpdate(cx, this);
    },
    async saveBegin() {
        await xtremMasterData.functions.controlDocumentNumber(this);

        await xtremSales.events.saveBegin.returnReceipt.checkFinanceIntegration(this);

        await updateHeaderNotesOnCreation(this);
    },
    async saveEnd() {
        await this.updateRelatedSalesDocumentsStatuses();
    },
    async deleteEnd(): Promise<void> {
        await this.updateRelatedSalesDocumentsStatuses();
    },
})
@xtremStockData.functions.staticImplements<xtremStockData.interfaces.StaticDocumentHeaderWithStockPosting>()
export class SalesReturnReceipt
    extends xtremMasterData.nodes.BaseDocument
    implements xtremFinanceData.interfaces.SalesFinanceDocument
{
    __skipPrepare: boolean;

    __salesReturnRequests: Dict<[xtremSales.interfaces.SalesDocumentLineUpdateInterface]> = {};

    __salesShipments: Dict<[xtremSales.interfaces.SalesDocumentLineUpdateInterface]> = {};

    @decorators.referencePropertyOverride<SalesReturnReceipt, 'site'>({
        filters: { control: { isInventory: true } },
    })
    override readonly site: Reference<xtremSystem.nodes.Site>;

    /** deprecated */
    @decorators.dateProperty<SalesReturnReceipt, 'returnDate'>({
        isPublished: true,
        lookupAccess: true,
        dependsOn: ['date'],
        getValue() {
            return this.date;
        },
    })
    readonly returnDate: Promise<date>;

    @decorators.datePropertyOverride<SalesReturnReceipt, 'date'>({
        control(cx, val) {
            if (val.compare(date.today()) > 0) {
                cx.error.addLocalized(
                    '@sage/xtrem-sales/nodes__sales_return_receipt__return_date_date_cannot_be_future',
                    'The return date cannot be later than today.',
                );
            }
        },
    })
    override readonly date: Promise<date>;

    @decorators.dateProperty<SalesReturnReceipt, 'effectiveDate'>({
        isPublished: true,
        dependsOn: ['date'],
        getValue() {
            return this.date;
        },
    })
    readonly effectiveDate: Promise<date>;

    @decorators.referenceProperty<SalesReturnReceipt, 'shipToCustomer'>({
        isStored: true,
        isPublished: true,
        isRequired: true,
        node: () => xtremMasterData.nodes.Customer,
    })
    readonly shipToCustomer: Reference<xtremMasterData.nodes.Customer>;

    @decorators.referenceProperty<SalesReturnReceipt, 'shipToCustomerAddress'>({
        isStored: true,
        isPublished: true,
        isRequired: true,
        dependsOn: ['shipToCustomer'],
        node: () => xtremMasterData.nodes.BusinessEntityAddress,
        async defaultValue() {
            return (await this.shipToCustomer)?.primaryShipToAddress;
        },
        filters: {
            control: {
                async businessEntity() {
                    return (await this.shipToCustomer).businessEntity;
                },
                deliveryDetail: { _ne: null },
            },
        },
    })
    readonly shipToCustomerAddress: Reference<xtremMasterData.nodes.BusinessEntityAddress>;

    @decorators.referenceProperty<SalesReturnReceipt, 'shipToAddress'>({
        isPublished: true,
        isStored: true,
        isMutable: true,
        isNullable: true,
        dependsOn: ['shipToCustomerAddress'],
        node: () => xtremMasterData.nodes.Address,
        async defaultValue() {
            return (await this.shipToCustomerAddress).address;
        },
        updatedValue: useDefaultValue,
    })
    readonly shipToAddress: Reference<xtremMasterData.nodes.Address | null>;

    @decorators.referenceProperty<SalesReturnReceipt, 'shipToContact'>({
        isPublished: true,
        isStored: true,
        isMutable: true,
        isNullable: true,
        dependsOn: ['shipToCustomerAddress'],
        node: () => xtremMasterData.nodes.Contact,
        async defaultValue() {
            return (await (await this.shipToCustomerAddress).primaryContact)?.contact ?? null;
        },
        updatedValue: useDefaultValue,
    })
    readonly shipToContact: Reference<xtremMasterData.nodes.Contact | null>;

    @decorators.collectionPropertyOverride<SalesReturnReceipt, 'lines'>({
        dependsOn: ['site', 'shipToCustomer', 'shipToCustomerAddress'],
        node: () => xtremSales.nodes.SalesReturnReceiptLine,
    })
    override readonly lines: Collection<xtremSales.nodes.SalesReturnReceiptLine>;

    readonly linesWithStockTransactionStatusCollectionName = 'lines';

    @decorators.enumPropertyOverride<SalesReturnReceipt, 'status'>({
        dependsOn: ['stockTransactionStatus', { lines: ['stockTransactionStatus'] }],
        async defaultValue() {
            return xtremStockData.functions.stockDocumentLib.getStockDocumentStatus(await this.stockTransactionStatus);
        },
        updatedValue: useDefaultValue,
    })
    override readonly status: Promise<xtremSales.enums.SalesReturnReceiptStatus>;

    @decorators.enumPropertyOverride<SalesReturnReceipt, 'displayStatus'>({
        dependsOn: [{ lines: ['stockTransactionStatus'] }, 'stockTransactionStatus', 'status'],
        async updatedValue() {
            const status = await this.status;
            logger.debug(() => `status 2 stockTransactionStatus ${status || this.stockTransactionStatus}`);
            return xtremSales.functions.SalesReturnReceiptLib.calculateSalesReturnReceiptDisplayStatus(
                status,
                await this.stockTransactionStatus,
            );
        },
    })
    override readonly displayStatus: Promise<xtremSales.enums.SalesReturnReceiptDisplayStatus>;

    @decorators.enumProperty<SalesReturnReceipt, 'stockTransactionStatus'>({
        isPublished: true,
        dependsOn: [{ lines: ['stockTransactionStatus'] }],
        dataType: () => xtremStockData.enums.StockDocumentTransactionStatusDataType,
        computeValue() {
            return xtremStockData.functions.stockTransactionLib.computeHeaderStatus(this);
        },
    })
    readonly stockTransactionStatus: Promise<xtremStockData.enums.StockDocumentTransactionStatus>;

    @decorators.enumProperty<SalesReturnReceipt, 'stockDetailStatus'>({
        isPublished: true,
        dataType: () => xtremStockData.enums.stockDetailStatusDataType,
        dependsOn: ['lines'],
        computeValue() {
            return getStockDetailStatus(this);
        },
    })
    readonly stockDetailStatus: Promise<xtremStockData.enums.StockDetailStatus>;

    // property needed for the accounting interface
    @decorators.referenceProperty<SalesReturnReceipt, 'billToCustomer'>({
        node: () => xtremMasterData.nodes.Customer,
        dependsOn: ['shipToCustomer'],
        async getValue() {
            const billToCustomer = await (await this.shipToCustomer).billToCustomer;
            return billToCustomer ?? this.shipToCustomer;
        },
    })
    readonly billToCustomer: Reference<xtremMasterData.nodes.Customer>;

    // property needed for the accounting interface
    @decorators.referencePropertyOverride<SalesReturnReceipt, 'transactionCurrency'>({
        dependsOn: ['billToCustomer'],
        async getValue() {
            return (await (await this.billToCustomer).businessEntity).currency;
        },
    })
    override readonly transactionCurrency: Reference<xtremMasterData.nodes.Currency>;

    @decorators.enumPropertyOverride<SalesReturnReceipt, 'financeIntegrationStatus'>({
        async computeValue() {
            return xtremFinanceData.functions.getDocumentIntegrationStatus(
                this.$.context,
                'salesReturnReceipt',
                await this.number,
            );
        },
    })
    override readonly financeIntegrationStatus: Promise<xtremFinanceData.enums.FinanceIntegrationStatus>;

    @decorators.textStreamPropertyOverride<SalesReturnReceipt, 'internalNote'>({
        async isFrozen() {
            return isPostedErrorClosed(await this.status);
        },
    })
    override readonly internalNote: Promise<TextStream>;

    @decorators.booleanPropertyOverride<SalesReturnReceipt, 'forceUpdateForFinance'>({
        defaultValue: false,
    })
    override readonly forceUpdateForFinance: Promise<boolean>;

    @decorators.booleanPropertyOverride<SalesReturnReceipt, 'forceUpdateForStock'>({
        defaultValue: false,
    })
    override readonly forceUpdateForStock: Promise<boolean>;

    @decorators.collectionProperty<SalesReturnReceipt, 'postingDetails'>({
        isPublished: true,
        join: {
            documentSysId() {
                return this._id;
            },
            documentType() {
                return 'salesReturnReceipt';
            },
        },
        node: () => xtremFinanceData.nodes.FinanceTransaction,
        lookupAccess: true,
    })
    readonly postingDetails: Collection<xtremFinanceData.nodes.FinanceTransaction>;

    private async updateRelatedSalesDocumentsStatuses(): Promise<void> {
        const salesReturnRequestsArray = await linkedReturnRequestsArray(this);

        await this.updateReturnRequestStatuses({
            salesDocumentsArray: salesReturnRequestsArray,
            tempDict: '__salesReturnRequests',
            statusType: 'receiptStatus',
            classInstance: this,
            aggregateNode: xtremSales.nodes.SalesReturnRequestLineToSalesReturnReceiptLine,
            enumDataType: xtremSales.enums.salesDocumentReturnRequestReceiptStatusDataType,
        });

        const salesShipmentsArray = await this.$.context
            .query(xtremSales.nodes.SalesShipment, {
                filter: { _id: { _in: Object.keys(this.__salesShipments) } },
                forUpdate: true,
            })
            .toArray();

        await xtremSales.classes.BaseSalesDocumentsCreator.updateRelatedSalesDocumentsStatuses({
            salesDocumentsArray: salesShipmentsArray,
            tempDict: '__salesShipments',
            statusType: 'returnReceiptStatus',
            classInstance: this,
            aggregateNode: xtremSales.nodes.SalesShipmentLineToSalesReturnReceiptLine,
            enumDataType: xtremSales.enums.salesDocumentReceiptStatusDataType,
        });
    }

    @decorators.mutation<typeof SalesReturnReceipt, 'postToStock'>({
        isPublished: true,
        startsReadOnly: true,
        parameters: [
            {
                name: 'documentIds',
                type: 'array',
                item: {
                    type: 'integer',
                },
            },
        ],
        return: {
            type: 'string',
        },
    })
    static async postToStock(readOnlyContext: Context, documentIds: integer[]): Promise<string> {
        // it simulates a stock service reply for all document lines with non-stock item
        await validatePost(readOnlyContext, documentIds);

        await readOnlyContext.runInWritableContext(async childContext => {
            await asyncArray(documentIds).forEach(async documentId => {
                const writableSalesReturnReceipt = await xtremMasterData.functions.getWritableNode(
                    childContext,
                    xtremSales.nodes.SalesReturnReceipt,
                    documentId,
                );
                await writableSalesReturnReceipt.$.set({ status: 'inProgress' });
                await writableSalesReturnReceipt.$.save();
            });
        });

        await asyncArray(documentIds).forEach(async documentID => {
            const document = await readOnlyContext.read(SalesReturnReceipt, { _id: documentID });
            await SalesReturnReceipt.onStockReply(readOnlyContext, {
                requestNotificationId: 'none',
                movementHasBeenSkipped: true,
                updateResults: {
                    receipt: {
                        documents: [
                            {
                                id: documentID,
                                lines: await document.lines
                                    .filter(line =>
                                        xtremStockData.functions.stockDocumentLib.shouldSkipStockManagementForThisLine(
                                            line,
                                        ),
                                    )
                                    .map(async line => {
                                        return {
                                            id: line._id,
                                            sortValue: await line._sortValue,
                                            stockUpdateResultStatus: 'none',
                                            originLineIds: [],
                                            stockJournalRecords: [],
                                        } as xtremStockData.interfaces.StockUpdateResultCommonType['documents'][0]['lines'][0];
                                    })
                                    .toArray(),
                            },
                        ],
                    },
                },
            });
        });
        // it sends a notification to stock service for all document lines with stock item
        return readOnlyContext.runInWritableContext(writableContext =>
            xtremStockData.functions.notificationLib.stockReceiptRequestNotification(writableContext, {
                documentClass: SalesReturnReceipt,
                documentIds,
            }),
        );
    }

    @decorators.notificationListener<typeof SalesReturnReceipt>({
        startsReadOnly: true,
        topic: 'SalesReturnReceipt/stock/receipt/reply',
        onError(
            context: Context,
            envelope: NotificationEnvelope<
                xtremStockData.interfaces.StockReplyPayload<xtremStockData.enums.StockMovementTypeEnum.receipt>
            >,
            error: Error,
        ) {
            return xtremStockData.functions.notificationLib.errorHandler(
                context,
                envelope,
                error,
                xtremSales.nodes.SalesReturnReceipt,
            );
        },
    })
    static async onStockReply(
        readOnlyContext: Context,
        payload: xtremStockData.interfaces.StockReplyPayload<xtremStockData.enums.StockMovementTypeEnum.receipt>,
    ): Promise<void> {
        const updateReceiptResult = await readOnlyContext.runInWritableContext(async writableContext => {
            const stockMovementProcessingReturn =
                await xtremStockData.functions.notificationLib.reactToStockMovementReply(
                    writableContext,
                    payload,
                    SalesReturnReceipt,
                    {
                        beforeDocumentSave: xtremSales.nodes.SalesReturnReceipt.updateHeaderAfterStockUpdate,
                    },
                );

            return stockMovementProcessingReturn.receipt;
        });
        if (!updateReceiptResult) return;

        if (!updateReceiptResult.transactionHadAnError) {
            await updateReceiptResult.documents.forEach(async document => {
                if (document.isStockDocumentCompleted) {
                    await readOnlyContext.runInWritableContext(async writableContext => {
                        const salesReturn = await writableContext.read(
                            SalesReturnReceipt,
                            { _id: document.id },
                            { forUpdate: true },
                        );
                        await SalesReturnReceipt.onceStockCompleted(writableContext, salesReturn);
                    });
                }
            });
        }
    }

    static async updateHeaderAfterStockUpdate(
        document: xtremStockData.interfaces.DocumentHeaderWithStockPosting,
    ): Promise<void> {
        const salesReturn = document as SalesReturnReceipt;

        if ((await salesReturn.stockTransactionStatus) === 'completed') {
            await salesReturn.$.set({ status: 'closed' });
        }

        const existingDisplayStatus = await salesReturn.displayStatus;
        const displayStatus = xtremSales.functions.SalesReturnReceiptLib.calculateSalesReturnReceiptDisplayStatus(
            await salesReturn.status,
            await salesReturn.stockTransactionStatus,
        );
        if (existingDisplayStatus !== displayStatus) {
            await salesReturn.$.set({ displayStatus });
        }
    }

    static async onceStockCompleted(readOnlyContext: Context, receipt: SalesReturnReceipt): Promise<void> {
        // Do whatever needs to be done after stock engine successfully processed all the lines
        if (await (await (await receipt.site).legalCompany).doStockPosting) {
            // Send notification in order to create staging table entries for the accounting engine
            await xtremSales.functions.FinanceIntegration.salesReturnReceiptNotification(
                readOnlyContext,
                receipt as xtremFinanceData.interfaces.SalesFinanceDocument,
                (await receipt.lines.toArray()) as xtremSales.interfaces.FinanceIntegration.SalesReturnDocumentLine[],
            );
        }
    }

    @decorators.notificationListener<typeof SalesReturnReceipt>({
        topic: 'SalesReturnReceipt/accountingInterface',
    })
    static async onFinanceIntegrationReply(
        context: Context,
        payload: xtremFinanceData.interfaces.FinanceTransactionData,
    ): Promise<void> {
        await xtremFinanceData.functions.reactToFinanceIntegrationReply(context, payload);
    }

    private async updateReturnRequestStatuses(
        data: xtremSales.interfaces.UpdateRelatedSalesDocumentsStatuses<
            xtremSales.nodes.SalesReturnReceiptLine,
            xtremSales.nodes.SalesReturnReceipt,
            keyof xtremSales.nodes.SalesReturnReceipt
        >,
    ): Promise<void> {
        let statusNumber: number;
        await asyncArray(data.salesDocumentsArray).forEach(async salesDocument => {
            const tmpDict = (await this[data.tempDict]) as unknown as Dict<
                xtremSales.interfaces.SalesDocumentLineUpdateInterface[]
            >;
            const salesDocumentLines =
                salesDocument.lines as Collection<xtremSales.interfaces.AnyDocumentLineWithRelatedDocuments>;

            await asyncArray(tmpDict[salesDocument._id]).forEach(async dictLine => {
                const line = (await salesDocumentLines.find(
                    findLine => dictLine._id === findLine._id,
                )) as xtremSales.interfaces.AnyInputDocumentLineWithStatus;

                statusNumber = await xtremSales.classes.BaseSalesDocumentsCreator.setDocumentLineStatus(
                    dictLine,
                    line!,
                    data.aggregateNode,
                );
                await line.$.set({ [data.statusType]: data.enumDataType.stringValue(statusNumber) });

                statusNumber =
                    await SalesReturnReceipt.setStatusBasedOnQuantityReceiptInSalesUnitAndQuantityInSalesUnit(
                        statusNumber,
                        // What happens to other line types?
                        line as xtremSales.nodes.SalesReturnRequestLine | xtremSales.nodes.SalesShipmentLine,
                    );

                await line.$.set({
                    status: xtremSales.enums.salesOrderStatusDataType.stringValue(statusNumber + 1) as any,
                });
            });

            const numberOfSalesDocuments = await salesDocument.lines.length;
            const sumOfDocumentsStatuses = await salesDocumentLines.reduce(async (prev, line) => {
                return (
                    prev + +data.enumDataType!.numberValue((await (line as Node).$.getValue<string>(data.statusType))!)
                );
            }, 0);
            // 1 === xtremSales.enums.SalesDocumentInvoiceStatus.notInvoiced and xtremSales.enums.SalesDocumentShippingStatus.notShipped
            if (sumOfDocumentsStatuses / numberOfSalesDocuments === 1) {
                statusNumber = 1;
            } else if (sumOfDocumentsStatuses / numberOfSalesDocuments === 3) {
                statusNumber = 3;
            } else {
                statusNumber = 2;
            }

            await salesDocument.$.set({ [data.statusType]: data.enumDataType.stringValue(statusNumber) });
            statusNumber = await SalesReturnReceipt.setStatusOnHeaderBasedOnLineStatus(
                statusNumber,
                salesDocument as xtremSales.interfaces.AnyInputDocumentHeaderWithLineStatus,
            );
            await salesDocument.$.set({
                status: xtremSales.enums.salesOrderStatusDataType.stringValue(statusNumber + 1) as any,
            });

            await salesDocument.$.save();
        });
    }

    // Repost creates an update notification to the acc engine ir order to update att and dimenions.
    // In fact, the existing finance document headers are preserved while the lines are all rebuilt (old ones deleted and new ones calculated)
    @decorators.mutation<typeof SalesReturnReceipt, 'repost'>({
        isPublished: true,
        parameters: [
            {
                name: 'salesReturnReceipt',
                type: 'reference',
                isMandatory: true,
                isWritable: true,
                node: () => SalesReturnReceipt,
            },
            {
                name: 'documentLines',
                type: 'array',
                item: {
                    type: 'object',
                    properties: {
                        baseDocumentLineSysId: 'integer',
                        storedAttributes: 'json',
                        storedDimensions: 'json',
                    },
                },
            },
        ],
        return: {
            type: 'object',
            properties: {
                wasSuccessful: 'boolean',
                message: 'string',
            },
        },
    })
    static async repost(
        context: Context,
        salesReturnReceipt: SalesReturnReceipt,
        documentLines: xtremFinanceData.interfaces.FinanceIntegrationDocumentLineUpdate[],
    ): Promise<xtremFinanceData.interfaces.MutationResult> {
        if (!xtremFinanceData.functions.canRepost((await salesReturnReceipt.financeIntegrationStatus) || 'submitted')) {
            // submitted used in case of undefined to assure that function will return false
            throw new BusinessRuleError(
                context.localize(
                    '@sage/xtrem-sales/nodes__sales-return-receipt__cant_repost_sales_return_receipt_when_status_is_not_failed',
                    "You can only repost a sales return receipt if the status is 'Failed.'",
                ),
            );
        }

        const updateLines = documentLines.map(documentLine => {
            return {
                _id: documentLine.baseDocumentLineSysId,
                storedAttributes: documentLine.storedAttributes,
                storedDimensions: documentLine.storedDimensions,
            };
        });
        await salesReturnReceipt.$.set({
            forceUpdateForFinance: true,
            lines: updateLines,
        });
        await salesReturnReceipt.$.save();

        // send notification in order to update a staging table entry for the accounting engine
        await xtremFinanceData.functions.financeDocumentUpdateNotification(context, {
            document: salesReturnReceipt,
            lines: await salesReturnReceipt.lines.toArray(),
            documentType: 'salesReturnReceipt',
            replyTopic: 'SalesReturnReceiptUpdate/accountingInterface',
            doNotPostOnUpdate: false,
        });
        return {
            wasSuccessful: true,
            message: context.localize(
                '@sage/xtrem-sales/nodes__sales-return-receipt__document_was_posted',
                'The sales return receipt has been posted.',
            ),
        };
    }

    @decorators.mutation<typeof SalesReturnReceipt, 'resendNotificationForFinance'>({
        isPublished: true,
        parameters: [
            { name: 'salesReturnReceipt', type: 'reference', isMandatory: true, node: () => SalesReturnReceipt },
        ],
        return: { type: 'boolean' },
    })
    static async resendNotificationForFinance(
        context: Context,
        salesReturnReceipt: SalesReturnReceipt,
    ): Promise<boolean> {
        logger.info(
            context.localize(
                '@sage/xtrem-sales/node__sales_return_receipt__resend_notification_for_finance',
                'Resending finance notification for sales return receipt: {{salesReturnReceiptNumber}}.',
                { salesReturnReceiptNumber: await salesReturnReceipt.number },
            ),
        );

        if (
            await xtremFinanceData.functions.AccountingEngineCommon.validateResendNotificationForFinance(context, {
                documentNumber: await salesReturnReceipt.number,
                documentType: 'salesReturnReceipt',
            })
        ) {
            if (await (await (await salesReturnReceipt.site).legalCompany).doStockPosting) {
                await xtremSales.functions.FinanceIntegration.salesReturnReceiptNotification(
                    context,
                    salesReturnReceipt as xtremFinanceData.interfaces.SalesFinanceDocument,
                    (await salesReturnReceipt.lines.toArray()) as xtremSales.interfaces.FinanceIntegration.SalesReturnDocumentLine[],
                );
            }
        }

        return true;
    }

    @decorators.notificationListener<typeof SalesReturnReceipt>({
        topic: 'SalesReturnReceipt/resendNotificationForFinance',
    })
    static async onResendNotificationForFinance(context: Context, document: { number: number }) {
        const salesReturnReceipt = await context.read(SalesReturnReceipt, { number: document.number });

        await SalesReturnReceipt.resendNotificationForFinance(context, salesReturnReceipt);
    }

    private static async setStatusBasedOnQuantityReceiptInSalesUnitAndQuantityInSalesUnit(
        statusNumber: number,
        line: xtremSales.nodes.SalesReturnRequestLine | xtremSales.nodes.SalesShipmentLine,
    ): Promise<number> {
        if (statusNumber === 3 && (await line.quantityReceiptInSalesUnit) !== (await line.quantity)) {
            return statusNumber - 1;
        }
        return statusNumber;
    }

    private static async setStatusOnHeaderBasedOnLineStatus(
        statusNumber: number,
        salesDocument: xtremSales.interfaces.AnyInputDocumentHeaderWithLineStatus,
    ): Promise<number> {
        const salesDocumentLines =
            salesDocument.lines as Collection<xtremSales.interfaces.AnyInputDocumentLineWithStatus>;

        if (statusNumber === 3 && (await salesDocumentLines.some(async line => (await line.status) !== 'closed'))) {
            return statusNumber - 1;
        }
        return statusNumber;
    }

    /**
     * This query validate quantity field
     * @param context
     * @param newQuantityInSalesUnit
     * @param salesReturnReceiptLine
     * @param sumOfStockDetailQuantity
     * @param salesReturnRequestLine
     */
    @decorators.query<typeof SalesReturnReceipt, 'validateQuantityInSalesUnit'>({
        isPublished: true,
        parameters: [
            { name: 'newQuantityInSalesUnit', type: 'decimal' },
            {
                name: 'salesReturnReceiptLine',
                type: 'reference',
                node: () => xtremSales.nodes.SalesReturnReceiptLine,
                isMandatory: false,
            },
            { name: 'sumOfStockDetailQuantity', type: 'decimal', isMandatory: false },
            {
                name: 'salesReturnRequestLine',
                type: 'reference',
                node: () => xtremSales.nodes.SalesReturnRequestLine,
                isMandatory: false,
            },
        ],
        return: {
            type: 'object',
            properties: {
                hasErrors: 'boolean',
                errorMessage: 'string',
            },
        },
    })
    static async validateQuantityInSalesUnit(
        context: Context,
        newQuantityInSalesUnit: decimal,
        salesReturnReceiptLine?: xtremSales.nodes.SalesReturnReceiptLine,
        sumOfStockDetailQuantity?: decimal,
        salesReturnRequestLine?: xtremSales.nodes.SalesReturnRequestLine,
    ): Promise<{ errorMessage?: string; hasErrors: boolean }> {
        let baseSalesReturnRequestLine = salesReturnRequestLine;
        if (!baseSalesReturnRequestLine && salesReturnReceiptLine) {
            baseSalesReturnRequestLine = await (
                await salesReturnReceiptLine.toReturnRequestLines.elementAt(0)
            ).linkedDocument;
        }

        if (baseSalesReturnRequestLine) {
            let filterCriteria: { linkedDocument: { _id: number }; _id?: { _ne: any } } = {
                linkedDocument: { _id: baseSalesReturnRequestLine._id },
            };
            if (salesReturnReceiptLine) {
                filterCriteria = {
                    ...filterCriteria,
                    _id: { _ne: (await salesReturnReceiptLine.toReturnRequestLines.elementAt(0))._id },
                };
            }
            const totalQuantity = await xtremSales.classes.BaseSalesDocumentsCreator.getTotalQuantityFromLines(
                filterCriteria,
                context,
                xtremSales.nodes.SalesReturnRequestLineToSalesReturnReceiptLine,
            );
            let finalSumOfStockDetailQuantity: decimal;
            finalSumOfStockDetailQuantity = 0;
            if (
                salesReturnReceiptLine &&
                (await (
                    await salesReturnReceiptLine.getItem()
                )?.isStockManaged) &&
                !sumOfStockDetailQuantity
            ) {
                if ((await salesReturnReceiptLine.stockDetails.length) > 0) {
                    finalSumOfStockDetailQuantity = await salesReturnReceiptLine.stockDetails.sum(
                        stockLine => stockLine.quantityInStockUnit,
                    );
                }
            } else if (sumOfStockDetailQuantity) {
                finalSumOfStockDetailQuantity = sumOfStockDetailQuantity;
            }

            return SalesReturnReceipt.checkDetailQuantity(
                context,
                finalSumOfStockDetailQuantity,
                newQuantityInSalesUnit,
                baseSalesReturnRequestLine,
                totalQuantity,
            );
        }
        return { hasErrors: false };
    }

    static async checkDetailQuantity(
        context: Context,
        finalSumOfStockDetailQuantity: decimal,
        newQuantityInSalesUnit: decimal,
        baseSalesReturnRequestLine: xtremSales.nodes.SalesReturnRequestLine,
        totalQuantity: decimal,
    ): Promise<{ errorMessage?: string; hasErrors: boolean }> {
        if (finalSumOfStockDetailQuantity && newQuantityInSalesUnit < finalSumOfStockDetailQuantity) {
            return {
                hasErrors: true,
                errorMessage: context.localize(
                    '@sage/xtrem-sales/nodes__sales_return_receipt__remaining_stock_detail_quantity',
                    'The received quantity cannot be lower than the stock quantity already identified.',
                ),
            };
        }

        if (totalQuantity + newQuantityInSalesUnit > (await baseSalesReturnRequestLine.quantity)) {
            return {
                hasErrors: true,
                errorMessage: context.localize(
                    '@sage/xtrem-sales/nodes__sales_return_receipt__remaining_quantity',
                    'The received quantity cannot be greater than the remaining quantity to be returned.',
                ),
            };
        }

        return { hasErrors: false };
    }
}
