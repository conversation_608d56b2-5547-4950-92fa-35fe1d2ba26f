import type { Collection, Reference, decimal } from '@sage/xtrem-core';
import { NodeStatus, TextStream, ValidationSeverity, decorators, useDefaultValue } from '@sage/xtrem-core';
import { Decimal } from '@sage/xtrem-decimal';
import * as xtremFinanceData from '@sage/xtrem-finance-data';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremSystem from '@sage/xtrem-system';
import { isPostedErrorClosed } from '../functions/sales-document-common-lib';
import { updateLineNotesOnCreation } from '../functions/sales-return-request-lib';
import * as xtremSales from '../index';

@decorators.subNode<SalesReturnRequestLine>({
    extends: () => xtremMasterData.nodes.BaseDocumentItemLine,
    isPublished: true,
    isVitalCollectionChild: true,
    canSearch: true,
    canRead: true,
    async controlBegin(cx) {
        await xtremSales.events.controlBegin.returnRequestLine.checkLineStatusOnCreation(cx, this);
        await xtremSales.events.controlBegin.returnRequestLine.checkHeaderStatusOnCreation(cx, this);
        await xtremSales.events.controlBegin.returnRequestLine.checkIfSameStatusOnCreation(cx, this);
        await xtremSales.events.controlBegin.returnRequestLine.checkIfSameOriginShippingSiteOnCreation(cx, this);
        await xtremSales.events.controlBegin.returnRequestLine.checkIfSameOriginShippingSiteLegalCompanyOnCreation(
            cx,
            this,
        );

        await xtremSales.events.controlBegin.returnRequestLine.checkStatusConsistency(cx, this);

        await xtremSales.events.controlBegin.returnRequestLine.checkClosedStatusOnUpdate(cx, this);
        await xtremSales.events.controlBegin.returnRequestLine.checkReceiptStatusOnUpdate(cx, this);
    },
    async createEnd() {
        await this.updateTempLinesDicts('create');
    },
    async saveBegin() {
        if (this.$.status === NodeStatus.modified) {
            await this.updateTempLinesDicts('update');
        }
        await updateLineNotesOnCreation(this);
    },
    async controlDelete(cx): Promise<void> {
        await xtremSales.events.controlDelete.returnRequestLine.checkReceiptStatusForDeletion(cx, this);
        await xtremSales.events.controlDelete.returnRequestLine.checkCreditStatusForDeletion(cx, this);
        await xtremSales.events.controlDelete.returnRequestLine.checkApprovalStatusForDeletion(cx, this);
    },
    async deleteEnd(): Promise<void> {
        await this.updateTempLinesDicts('delete');
    },
    async controlEnd(cx) {
        await xtremFinanceData.functions.attributTypeRestrictedToCheck(cx, this.$.context, await this.storedAttributes);
    },
})
export class SalesReturnRequestLine extends xtremMasterData.nodes.BaseDocumentItemLine {
    @decorators.referencePropertyOverride<SalesReturnRequestLine, 'document'>({
        node: () => xtremSales.nodes.SalesReturnRequest,
    })
    override readonly document: Reference<xtremSales.nodes.SalesReturnRequest>;

    @decorators.enumProperty<SalesReturnRequestLine, 'originDocumentType'>({
        isPublished: true,
        dataType: () => xtremSales.enums.salesOriginDocumentTypeDataType,
        getValue() {
            return this.origin as unknown as xtremSales.enums.SalesOriginDocumentType;
        },
    })
    readonly originDocumentType: Promise<xtremSales.enums.SalesOriginDocumentType>;

    @decorators.enumProperty<SalesReturnRequestLine, 'receiptStatus'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremSales.enums.salesDocumentReturnRequestReceiptStatusDataType,
        defaultValue() {
            return 'notReceived';
        },
        lookupAccess: true,
    })
    readonly receiptStatus: Promise<xtremSales.enums.SalesDocumentReturnRequestReceiptStatus>;

    @decorators.enumProperty<SalesReturnRequestLine, 'creditStatus'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        dataType: () => xtremSales.enums.salesDocumentCreditStatusDataType,
        defaultValue: () => 'notCredited',
    })
    readonly creditStatus: Promise<xtremSales.enums.SalesDocumentCreditStatus>;

    @decorators.booleanProperty<SalesReturnRequestLine, 'isCreditMemoExpected'>({
        isStored: true,
        isPublished: true,
        dependsOn: [{ document: ['returnType'] }],
        defaultValue() {
            return this.checkIsCreditMemoExpected();
        },
        updatedValue: useDefaultValue,
    })
    readonly isCreditMemoExpected: Promise<boolean>;

    @decorators.booleanProperty<SalesReturnRequestLine, 'isReceiptExpected'>({
        isStored: true,
        isPublished: true,
        dependsOn: [{ document: ['returnType'] }],
        defaultValue() {
            return this.checkIsReceiptExpected();
        },
        updatedValue: useDefaultValue,
        lookupAccess: true,
    })
    readonly isReceiptExpected: Promise<boolean>;

    @decorators.referencePropertyOverride<SalesReturnRequestLine, 'item'>({
        // isRequired: true,
        dependsOn: [{ document: ['stockSite'] }],
        async control(cx) {
            if (this.$.status !== NodeStatus.added && (await (await this.$.old).item)._id !== (await this.item)._id) {
                cx.addDiagnose(
                    ValidationSeverity.error,
                    cx.localize(
                        '@sage/xtrem-sales/nodes__sales_return_request_line__modification_of_the_item_is_forbidden',
                        'The item cannot be updated.',
                    ),
                );
            }
        },
        filters: {
            control: {
                isSold: true,
                itemSites: {
                    _atLeast: 1,
                    async site() {
                        return (await this.document).stockSite;
                    },
                },
            },
        },
    })
    override readonly item: Reference<xtremMasterData.nodes.Item>;

    @decorators.decimalPropertyOverride<SalesReturnRequestLine, 'quantity'>({
        // isRequired: true,
        dependsOn: ['unit'],
        async control(cx, value) {
            await cx.error.if(value).is.zero();
            if (this.$.status !== NodeStatus.added && (await (await this.$.old).quantity) !== (await this.quantity)) {
                const errorMessage = await xtremSales.functions.SalesReturnRequestLib.controlQuantityInSalesUnit(
                    this.$.context,
                    this,
                    value,
                );
                if (errorMessage) {
                    cx.addDiagnose(ValidationSeverity.error, errorMessage);
                }
            }
        },
    })
    override readonly quantity: Promise<decimal>;

    // @TODO DELETE AFTER BASE DOCUMENT REFACTORING
    @decorators.decimalProperty<SalesReturnRequestLine, 'quantityInSalesUnit'>({
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.quantityInSalesUnit,
        getValue() {
            return this.quantity;
        },
    })
    readonly quantityInSalesUnit: Promise<decimal>;

    @decorators.referencePropertyOverride<SalesReturnRequestLine, 'unit'>({
        // isRequired: true,
        dependsOn: ['item', { document: ['shipToCustomer'] }],
        async defaultValue() {
            return (await xtremMasterData.functions.getSalesUnitDefaultValue(
                this.$.context,
                await this.item,
                (await (await this.document).shipToCustomer)._id,
            ))!;
        },
        async control(cx, val) {
            if (this.$.status !== NodeStatus.added && (await (await this.$.old).unit)._id !== (await this.unit)._id) {
                cx.addDiagnose(
                    ValidationSeverity.error,
                    cx.localize(
                        '@sage/xtrem-sales/nodes__sales_return_request_line__modification_of_the_sales_unit_is_forbidden',
                        'The sales unit cannot be updated.',
                    ),
                );
            }
            await cx.error
                .if(await val.id)
                .is.not.in(
                    await xtremMasterData.functions.getValidSalesUnitList(
                        this.$.context,
                        await this.item,
                        await (
                            await this.document
                        ).shipToCustomer,
                    ),
                );
        },
    })
    override readonly unit: Reference<xtremMasterData.nodes.UnitOfMeasure>;

    // @TODO DELETE AFTER BASE DOCUMENT REFACTORING
    @decorators.referenceProperty<SalesReturnRequestLine, 'salesUnit'>({
        isPublished: true,
        dependsOn: ['unit'],
        node: () => xtremMasterData.nodes.UnitOfMeasure,
        getValue() {
            return this.unit;
        },
    })
    readonly salesUnit: Reference<xtremMasterData.nodes.UnitOfMeasure>;

    @decorators.decimalPropertyOverride<SalesReturnRequestLine, 'unitToStockUnitConversionFactor'>({
        // isRequired: true,
        dependsOn: ['item', { document: ['shipToCustomer'] }],
        async defaultValue() {
            return xtremMasterData.functions.calculateConversionFactor(
                this.$.context,
                await this.item,
                (await (await this.document).shipToCustomer)?._id,
            );
        },
        updatedValue: useDefaultValue,
    })
    override readonly unitToStockUnitConversionFactor: Promise<decimal>;

    // @TODO SHOULD BE REMOVED AFTER BASE DOCUMENT REFACTORING
    @decorators.decimalProperty<SalesReturnRequestLine, 'salesUnitToStockUnitConversionFactor'>({
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.coefficientDataType,
        getValue() {
            return this.unitToStockUnitConversionFactor;
        },
    })
    readonly salesUnitToStockUnitConversionFactor: Promise<decimal>;

    @decorators.decimalPropertyOverride<SalesReturnRequestLine, 'quantityInStockUnit'>({
        // excludedFromPayload: true,
        dependsOn: ['stockUnit', 'quantity', 'unitToStockUnitConversionFactor'],
        defaultValue() {
            return this.quantityInStockUnitComputed();
        },
        async control(cx, val) {
            await cx.error.if(val).is.zero();
            await cx.error.if(Number(val)).is.not.equal.to(await this.quantityInStockUnitComputed());
        },
    })
    override readonly quantityInStockUnit: Promise<decimal>;

    @decorators.referencePropertyOverride<SalesReturnRequestLine, 'stockUnit'>({
        // excludedFromPayload: true,
        dependsOn: ['item'],
        async control(cx, val) {
            await cx.error.if(await val.id).is.not.equal.to(await (await (await this.item).stockUnit).id);
        },
    })
    override readonly stockUnit: Reference<xtremMasterData.nodes.UnitOfMeasure>;

    @decorators.referenceProperty<SalesReturnRequestLine, 'originShippingSite'>({
        isStored: true,
        isPublished: true,
        isRequired: true,
        filters: {
            control: {
                isSales: true,
                isInventory: true,
            },
        },
        dependsOn: [{ document: ['stockSite'] }],
        node: () => xtremSystem.nodes.Site,
        async defaultValue() {
            return (await this.document).stockSite;
        },
    })
    readonly originShippingSite: Reference<xtremSystem.nodes.Site>;

    @decorators.referenceProperty<SalesReturnRequestLine, 'reason'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
        node: () => xtremSales.nodes.SalesReturnRequestReason,
    })
    readonly reason: Reference<xtremSales.nodes.SalesReturnRequestReason | null>;

    @decorators.textStreamProperty<SalesReturnRequestLine, 'text'>({
        isStored: true,
        isPublished: true,
        dependsOn: ['status'],
        // isFrozen() {
        //     return this._isClosedOrReceived;
        // },
        dataSensitivityLevel: 'personal',
        anonymizeMethod: 'fixed',
        anonymizeValue: TextStream.fromString('*'.repeat(15)),
    })
    readonly text: Promise<TextStream>;

    @decorators.decimalProperty<SalesReturnRequestLine, 'uQuantityReceiptAndToReceiveInSalesUnit'>({
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.quantityInSalesUnit,
        dependsOn: ['quantity', 'quantityReceiptInSalesUnit', 'quantityToReceiveInProgressInSalesUnit'],
        async getValue() {
            return (
                (await this.quantity) -
                (await this.quantityToReceiveInProgressInSalesUnit) -
                (await this.quantityReceiptInSalesUnit)
            );
        },
        lookupAccess: true,
    })
    readonly uQuantityReceiptAndToReceiveInSalesUnit: Promise<decimal>;

    @decorators.jsonPropertyOverride<SalesReturnRequestLine, 'computedAttributes'>({
        dependsOn: ['item', { document: ['site', 'billToCustomer'] }, 'originShippingSite'],
        async computeValue() {
            const document = await this.document;
            return xtremSales.functions.computeAttributes(
                this.$.context,
                await document.site,
                await this.originShippingSite,
                await this.item,
                await document.billToCustomer,
            );
        },
    })
    override readonly computedAttributes: Promise<object>;

    @decorators.collectionProperty<SalesReturnRequestLine, 'salesShipmentLines'>({
        isPublished: true,
        node: () => xtremSales.nodes.SalesShipmentLineToSalesReturnRequestLine,
        isVital: true,
        reverseReference: 'document',
        dependsOn: ['quantity'],
    })
    readonly salesShipmentLines: Collection<xtremSales.nodes.SalesShipmentLineToSalesReturnRequestLine>;

    @decorators.collectionProperty<SalesReturnRequestLine, 'salesReturnReceiptLines'>({
        node: () => xtremSales.nodes.SalesReturnRequestLineToSalesReturnReceiptLine,
        reverseReference: 'linkedDocument',
        isPublished: true,
    })
    readonly salesReturnReceiptLines: Collection<xtremSales.nodes.SalesReturnRequestLineToSalesReturnReceiptLine>;

    @decorators.decimalProperty<SalesReturnRequestLine, 'quantityReceiptInSalesUnit'>({
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.quantityInSalesUnit,
        async getValue() {
            if ((await this.receiptStatus) === 'notReceived') return 0;

            return this.salesReturnReceiptLines
                .where(async line => (await (await line.document).stockTransactionStatus) === 'completed')
                .sum(async line => (await line.document).quantity);
        },
    })
    readonly quantityReceiptInSalesUnit: Promise<decimal>;

    @decorators.decimalProperty<SalesReturnRequestLine, 'quantityToReceiveInProgressInSalesUnit'>({
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.quantityInSalesUnit,
        async getValue() {
            if ((await this.receiptStatus) === 'notReceived') return 0;

            return this.salesReturnReceiptLines
                .where(async line => (await (await line.document).stockTransactionStatus) !== 'completed')
                .sum(async line => (await line.document).quantity);
        },
    })
    readonly quantityToReceiveInProgressInSalesUnit: Promise<decimal>;

    @decorators.collectionProperty<SalesReturnRequestLine, 'salesCreditMemoLines'>({
        // TODO: should we publish it?
        node: () => xtremSales.nodes.SalesReturnRequestLineSalesCreditMemoLine,
        reverseReference: 'linkedDocument',
    })
    readonly salesCreditMemoLines: Collection<xtremSales.nodes.SalesReturnRequestLineSalesCreditMemoLine>;

    @decorators.decimalProperty<SalesReturnRequestLine, 'quantityCreditedInProgressInSalesUnit'>({
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.quantityInSalesUnit,
        dependsOn: ['isCreditMemoExpected', 'creditStatus'],
        async getValue() {
            if (!(await this.isCreditMemoExpected) || (await this.creditStatus) === 'notCredited') return 0;

            return this.salesCreditMemoLines
                .where(async line => ['draft'].includes(await (await (await line.document).document).status))
                .sum(async line => (await line.document).quantity);
        },
    })
    readonly quantityCreditedInProgressInSalesUnit: Promise<decimal>;

    @decorators.decimalProperty<SalesReturnRequestLine, 'quantityCreditedPostedInSalesUnit'>({
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.quantityInSalesUnit,
        dependsOn: ['isCreditMemoExpected', 'creditStatus'],
        async getValue() {
            if (!(await this.isCreditMemoExpected) || (await this.creditStatus) === 'notCredited') return 0;

            return this.salesCreditMemoLines
                .where(async line =>
                    ['posted', 'inProgress', 'error'].includes(await (await (await line.document).document).status),
                )
                .sum(async line => (await line.document).quantity);
        },
    })
    readonly quantityCreditedPostedInSalesUnit: Promise<decimal>;

    @decorators.booleanProperty<SalesReturnRequestLine, 'isCreditingAllowed'>({
        isStored: true,
        isPublished: true,
        excludedFromPayload: true,
        defaultValue: false,
    })
    readonly isCreditingAllowed: Promise<boolean>;

    @decorators.textStreamPropertyOverride<SalesReturnRequestLine, 'internalNote'>({
        async isFrozen() {
            return isPostedErrorClosed(await (await this.document).status);
        },
    })
    override readonly internalNote: Promise<TextStream>;

    async updateTempLinesDicts(action: string): Promise<void> {
        await xtremSales.classes.SalesReturnRequestCreator.fillTempSalesDocumentDicts(
            action,
            'salesShipmentLines',
            '__salesShipments',
            this,
        );
    }

    private async checkIsCreditMemoExpected(): Promise<boolean> {
        if (['receiptAndCreditMemo', 'creditMemo'].includes(await (await this.document).returnType)) {
            return true;
        }
        return false;
    }

    private async checkIsReceiptExpected(): Promise<boolean> {
        if (['receiptAndCreditMemo', 'receiptAndNoCreditMemo'].includes(await (await this.document).returnType)) {
            return true;
        }
        return false;
    }

    async quantityInStockUnitComputed(): Promise<decimal> {
        return +new Decimal((await this.quantity) * (await this.unitToStockUnitConversionFactor)).toDecimalPlaces(
            (await (await this.stockUnit)?.decimalDigits) || 0,
        );
    }
}
