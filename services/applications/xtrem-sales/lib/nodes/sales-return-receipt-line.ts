import type { Collection, JsonType, NodeCreateData, Reference, decimal, integer, TextStream } from '@sage/xtrem-core';
import { NodeStatus, ValidationSeverity, decorators, useDefaultValue } from '@sage/xtrem-core';
import { Decimal } from '@sage/xtrem-decimal';
import * as xtremFinanceData from '@sage/xtrem-finance-data';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremStockData from '@sage/xtrem-stock-data';
import { isPostedErrorClosed } from '../functions/sales-document-common-lib';
import { updateLineNotesOnCreation } from '../functions/sales-return-receipt-lib';
import * as xtremSales from '../index';

@decorators.subNode<SalesReturnReceiptLine>({
    extends: () => xtremMasterData.nodes.BaseDocumentItemLine,
    isPublished: true,
    isVitalCollectionChild: true,
    canSearch: true,
    canRead: true,
    async controlBegin(cx) {
        await xtremSales.events.controlBegin.returnReceiptLine.checkClosedStatus(cx, this);
    },

    async saveBegin() {
        await this.$.set({
            stockDetailStatus: await xtremStockData.functions.stockDetailLib.getStockDetailStatus({
                line: this,
                quantityExpected: await this.quantityInStockUnit,
                checkSerialNumbers: false,
            }),
        });

        if (this.$.status === NodeStatus.modified) {
            const oldNode = await this.$.old;
            if (
                (await oldNode.quantity) !== (await this.quantity) ||
                ((await oldNode.stockTransactionStatus) !== 'completed' &&
                    (await this.stockTransactionStatus) === 'completed')
            ) {
                await this.updateTempLinesDicts('update');
            }
        }
        await updateLineNotesOnCreation(this);
    },
    async createEnd() {
        await this.updateTempLinesDicts('create');
    },
    async controlDelete(cx): Promise<void> {
        await xtremSales.events.controlDelete.returnReceiptLine.checkCreditStatusForDeletion(cx, this);
        await xtremSales.events.controlDelete.returnReceiptLine.checkStockStatusForDeletion(cx, this);
        await xtremSales.events.controlDelete.returnReceiptLine.checkDraftStatusForDeletion(cx, this);
        await this.updateTempLinesDicts('delete');
    },
    async controlEnd(cx) {
        await xtremFinanceData.functions.attributTypeRestrictedToCheck(cx, this.$.context, await this.storedAttributes);
    },
})
@xtremStockData.functions.staticImplements<xtremStockData.interfaces.StaticDocumentLineWithStockPosting>()
export class SalesReturnReceiptLine
    extends xtremMasterData.nodes.BaseDocumentItemLine
    implements xtremFinanceData.interfaces.ReturnDocumentLine
{
    async getOrderCost(): Promise<number> {
        const shipmentLine = await (await this.salesShipmentLines.at(0))?.linkedDocument;

        return xtremStockData.functions.stockValuationLib.getStockCost(this.$.context, {
            costType: 'order',
            movementType: 'receipt',
            documentLineId: shipmentLine?._id,
            itemSite: await this.$.context.read(xtremMasterData.nodes.ItemSite, {
                item: await this.item,
                site: await this.site,
            }),
        });
    }

    getValuedCost() {
        return this.getOrderCost();
    }

    async getValuationParameters(): Promise<xtremStockData.interfaces.ValuationParameter> {
        const shipmentLine = await (await this.salesShipmentLines.at(0))?.linkedDocument;
        return { valuationType: 'negativeIssue', originDocumentLine: shipmentLine };
    }

    @decorators.referencePropertyOverride<SalesReturnReceiptLine, 'document'>({
        node: () => xtremSales.nodes.SalesReturnReceipt,
    })
    override readonly document: Reference<xtremSales.nodes.SalesReturnReceipt>;

    @decorators.stringPropertyOverride<SalesReturnReceiptLine, 'documentNumber'>({
        async getValue() {
            return (await this.document).number;
        },
    })
    override readonly documentNumber: Promise<string>;

    @decorators.integerPropertyOverride<SalesReturnReceiptLine, 'documentId'>({
        async getValue() {
            return (await this.document)._id;
        },
    })
    override readonly documentId: Promise<integer>;

    @decorators.referencePropertyOverride<SalesReturnReceiptLine, 'item'>({
        dependsOn: [{ document: ['site'] }],
        async control(cx) {
            if (this.$.status !== NodeStatus.added && (await (await this.$.old).item)._id !== (await this.item)._id) {
                cx.error.addLocalized(
                    '@sage/xtrem-sales/nodes__sales_return_receipt_line__modification_of_the_item_is_forbidden',
                    'The item cannot be updated.',
                );
            }
        },
        filters: {
            control: {
                isSold: true,
                itemSites: {
                    _atLeast: 1,
                    async site() {
                        return (await this.document).site;
                    },
                },
            },
        },
    })
    override readonly item: Reference<xtremMasterData.nodes.Item>;

    @decorators.stringPropertyOverride<SalesReturnReceiptLine, 'itemDescription'>({
        async defaultValue() {
            return (await (await this.item)?.description) || (await this.item)?.name;
        },
        async control(cx) {
            if (
                this.$.status !== NodeStatus.added &&
                (await (
                    await this.item
                ).isStockManaged) &&
                ['inProgress', 'completed'].includes(await this.stockTransactionStatus) &&
                (await (await this.$.old).itemDescription) !== (await this.itemDescription)
            ) {
                cx.error.addLocalized(
                    '@sage/xtrem-sales/nodes__sales_return_receipt_line__modification_of_the_item_description_is_forbidden',
                    'The item description cannot be updated.',
                );
            }
        },
    })
    override readonly itemDescription: Promise<string>;

    @decorators.decimalPropertyOverride<SalesReturnReceiptLine, 'quantity'>({
        dependsOn: ['item', 'unit'],
        async control(cx, value) {
            await cx.error.if(value).is.negative();
            await cx.error.if(value).is.zero();
            if (this.$.status !== NodeStatus.added && (await (await this.$.old).quantity) !== value) {
                if (
                    (await (await this.item).isStockManaged) &&
                    ['inProgress', 'completed'].includes(await this.stockTransactionStatus)
                ) {
                    cx.error.addLocalized(
                        '@sage/xtrem-sales/nodes__sales_return_receipt_line__modification_of_the_quantity_in_sales_unit_is_forbidden',
                        'The quantity in sales unit cannot be updated.',
                    );
                }
                const result = await xtremSales.nodes.SalesReturnReceipt.validateQuantityInSalesUnit(
                    this.$.context,
                    value,
                    this,
                );
                if (result.hasErrors && result.errorMessage) {
                    cx.addDiagnose(ValidationSeverity.error, result.errorMessage);
                }
            }
        },
    })
    override readonly quantity: Promise<decimal>;

    // @TODO DELETE AFTER BASE DOCUMENT REFACTORING
    @decorators.decimalProperty<SalesReturnReceiptLine, 'quantityInSalesUnit'>({
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.quantityInSalesUnit,
        getValue() {
            return this.quantity;
        },
    })
    readonly quantityInSalesUnit: Promise<decimal>;

    @decorators.referencePropertyOverride<SalesReturnReceiptLine, 'unit'>({
        dependsOn: ['item', { document: ['shipToCustomer'] }],
        async defaultValue() {
            return (await xtremMasterData.functions.getSalesUnitDefaultValue(
                this.$.context,
                await this.item,
                (await (await this.document).shipToCustomer)._id,
            ))!;
        },
        async control(cx, val) {
            const item = await this.item;
            if (
                this.$.status !== NodeStatus.added &&
                (await item.isStockManaged) &&
                ['inProgress', 'completed'].includes(await this.stockTransactionStatus) &&
                (await (await this.$.old).unit)._id !== (await this.unit)._id
            ) {
                cx.error.addLocalized(
                    '@sage/xtrem-sales/nodes__sales_return_receipt_line__modification_of_the_sales_unit_is_forbidden',
                    'The sales unit cannot be updated.',
                );
            }
            await cx.error
                .if(await val.id)
                .is.not.in(
                    await xtremMasterData.functions.getValidSalesUnitList(
                        this.$.context,
                        item,
                        await (
                            await this.document
                        ).shipToCustomer,
                    ),
                );
        },
    })
    override readonly unit: Reference<xtremMasterData.nodes.UnitOfMeasure>;

    // @TODO DELETE AFTER BASE DOCUMENT REFACTORING
    @decorators.referenceProperty<SalesReturnReceiptLine, 'salesUnit'>({
        isPublished: true,
        dependsOn: ['unit'],
        node: () => xtremMasterData.nodes.UnitOfMeasure,
        getValue() {
            return this.unit;
        },
    })
    readonly salesUnit: Reference<xtremMasterData.nodes.UnitOfMeasure>;

    @decorators.decimalPropertyOverride<SalesReturnReceiptLine, 'unitToStockUnitConversionFactor'>({
        dependsOn: ['item', { document: ['shipToCustomer'] }, 'stockTransactionStatus'],
        async control(cx, val) {
            await cx.error.if(val).is.negative();
            await cx.error.if(val).is.zero();
        },
        async defaultValue() {
            return xtremMasterData.functions.calculateConversionFactor(
                this.$.context,
                await this.item,
                (await (await this.document).shipToCustomer)?._id,
            );
        },
        updatedValue: useDefaultValue,
        async isFrozen() {
            return (
                (await (await this.item).isStockManaged) &&
                ['inProgress', 'completed'].includes(await this.stockTransactionStatus)
            );
        },
    })
    override readonly unitToStockUnitConversionFactor: Promise<decimal>;

    // @TODO SHOULD BE REMOVED AFTER BASE DOCUMENT REFACTORING
    @decorators.decimalProperty<SalesReturnReceiptLine, 'salesUnitToStockUnitConversionFactor'>({
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.coefficientDataType,
        getValue() {
            return this.unitToStockUnitConversionFactor;
        },
    })
    readonly salesUnitToStockUnitConversionFactor: Promise<decimal>;

    @decorators.decimalPropertyOverride<SalesReturnReceiptLine, 'quantityInStockUnit'>({
        dependsOn: ['item', 'stockUnit', 'quantity', 'unitToStockUnitConversionFactor', 'stockTransactionStatus'],
        defaultValue() {
            return this.quantityInStockUnitComputed();
        },
        updatedValue: useDefaultValue,
        async control(cx, val) {
            if (
                this.$.status !== NodeStatus.added &&
                (await (
                    await this.item
                ).isStockManaged) &&
                ['inProgress', 'completed'].includes(await this.stockTransactionStatus) &&
                (await (await this.$.old).quantityInStockUnit) !== val
            ) {
                cx.error.addLocalized(
                    '@sage/xtrem-sales/nodes__sales_return_receipt_line__modification_of_the_quantity_in_stock_unit_is_forbidden',
                    'The quantity in stock unit cannot be updated.',
                );
            }
            await cx.error.if(Number(val)).is.not.equal.to(await this.quantityInStockUnitComputed());
        },
    })
    override readonly quantityInStockUnit: Promise<decimal>;

    @decorators.referencePropertyOverride<SalesReturnReceiptLine, 'stockUnit'>({
        async defaultValue() {
            return (await (
                await this.item
            )?.stockUnit)!;
        },
        async control(cx, val) {
            const item = await this.item;
            await cx.error.if(await val.id).is.not.equal.to(await (await item.stockUnit).id);
            if (
                this.$.status !== NodeStatus.added &&
                (await item.isStockManaged) &&
                ['inProgress', 'completed'].includes(await this.stockTransactionStatus) &&
                (await (await this.$.old).stockUnit) !== val
            ) {
                cx.error.addLocalized(
                    '@sage/xtrem-sales/nodes__sales_return_receipt_line__modification_of_the_stock_unit_is_forbidden',
                    'The stock unit cannot be updated.',
                );
            }
        },
    })
    override readonly stockUnit: Reference<xtremMasterData.nodes.UnitOfMeasure>;

    @decorators.enumProperty<SalesReturnReceiptLine, 'originDocumentType'>({
        isPublished: true,
        dataType: () => xtremSales.enums.salesOriginDocumentTypeDataType,
        getValue() {
            return this.origin as unknown as xtremSales.enums.SalesOriginDocumentType;
        },
    })
    readonly originDocumentType: Promise<xtremSales.enums.SalesOriginDocumentType>;

    @decorators.enumPropertyOverride<SalesReturnReceiptLine, 'origin'>({
        dependsOn: ['item'],
        async control(cx, val) {
            if (
                this.$.status !== NodeStatus.added &&
                (await (
                    await this.item
                ).isStockManaged) &&
                ['inProgress', 'completed'].includes(await this.stockTransactionStatus) &&
                (await (await this.$.old).origin) !== val
            ) {
                cx.error.addLocalized(
                    '@sage/xtrem-sales/nodes__sales_return_receipt_line__modification_of_the_origin_document_type_is_forbidden',
                    'The origin document type cannot be updated.',
                );
            }
        },
    })
    override readonly origin: Promise<xtremSales.enums.SalesOriginDocumentType>;

    @decorators.booleanProperty<SalesReturnReceiptLine, 'isReceiptExpected'>({
        isPublished: true,
        dependsOn: [{ toReturnRequestLines: ['linkedDocument'] }],
        async computeValue() {
            if (
                (await this.toReturnRequestLines.length) > 0 &&
                (await (
                    await (
                        await this.toReturnRequestLines.elementAt(0)
                    ).linkedDocument
                ).isReceiptExpected)
            ) {
                return true;
            }
            return false;
        },
    })
    readonly isReceiptExpected: Promise<boolean>;

    @decorators.enumProperty<SalesReturnReceiptLine, 'stockDetailStatus'>({
        isPublished: true,
        isStored: true,
        dataType: () => xtremStockData.enums.stockDetailStatusDataType,
        dependsOn: ['item', 'stockDetails', 'isReceiptExpected', 'quantityInStockUnit'],
        async defaultValue() {
            return xtremStockData.functions.stockDetailLib.getStockDetailStatus({
                line: this,
                quantityExpected: await this.quantityInStockUnit,
                checkSerialNumbers: false,
            });
        },
        updatedValue: useDefaultValue,
    })
    readonly stockDetailStatus: Promise<xtremStockData.enums.StockDetailStatus>;

    @decorators.referenceProperty<SalesReturnReceiptLine, 'location'>({
        isPublished: true,
        node: () => xtremMasterData.nodes.Location,
        isNullable: true,
        dependsOn: ['stockDetails'],
        lookupAccess: true,
        async computeValue() {
            return this.stockDetails.reduce(
                async (previous, stockDetail) =>
                    (await stockDetail.location) === previous ? stockDetail.location : null,
                (await (await this.stockDetails.at(0))?.location) || null,
            );
        },
    })
    readonly location: Reference<xtremMasterData.nodes.Location | null>;

    @decorators.referenceProperty<SalesReturnReceiptLine, 'stockStatus'>({
        isPublished: true,
        node: () => xtremStockData.nodes.StockStatus,
        isNullable: true,
        dependsOn: ['stockDetails'],
        async computeValue() {
            return this.stockDetails.reduce(
                async (previous, stockDetail) => ((await stockDetail.status) === previous ? stockDetail.status : null),
                (await (await this.stockDetails.at(0))?.status) || null,
            );
        },
    })
    readonly stockStatus: Reference<xtremStockData.nodes.StockStatus | null>;

    @decorators.referenceProperty<SalesReturnReceiptLine, 'lot'>({
        isPublished: true,
        isNullable: true,
        node: () => xtremStockData.nodes.Lot,
        dependsOn: ['stockDetails'],
        async computeValue() {
            return this.stockDetails.reduce(
                async (previous, stockDetail) =>
                    (await stockDetail.existingLot) === previous ? stockDetail.existingLot : null,
                (await (await this.stockDetails.at(0))?.existingLot) || null,
            );
        },
    })
    readonly lot: Reference<xtremStockData.nodes.Lot | null>;

    @decorators.decimalProperty<SalesReturnReceiptLine, 'orderCost'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.costDataType,

        async defaultValue() {
            const itemSite = await this.$.context.tryRead(xtremMasterData.nodes.ItemSite, {
                item: await this.item,
                site: await (await this.document).site,
            });
            return (await itemSite?.currentCost) || 0;
        },
        dependsOn: ['item', 'document'],
        async control(cx, val) {
            if (
                this.$.status !== NodeStatus.added &&
                (await (
                    await this.item
                ).isStockManaged) &&
                ['inProgress', 'completed'].includes(await this.stockTransactionStatus) &&
                (await (await this.$.old).orderCost) !== val
            ) {
                cx.error.addLocalized(
                    '@sage/xtrem-sales/nodes__sales_return_receipt_line__modification_of_the_order_cost_is_forbidden',
                    'The order cost cannot be updated.',
                );
            }
        },
    })
    readonly orderCost: Promise<decimal>;

    @decorators.decimalProperty<SalesReturnReceiptLine, 'valuedCost'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.costDataType,

        async defaultValue() {
            const itemSite = await this.$.context.tryRead(xtremMasterData.nodes.ItemSite, {
                item: await this.item,
                site: await (await this.document).site,
            });
            return (await itemSite?.currentCost) || 0;
        },
        dependsOn: ['item', 'document'],
        async control(cx, val) {
            if (
                this.$.status !== NodeStatus.added &&
                (await (
                    await this.item
                ).isStockManaged) &&
                ['inProgress', 'completed'].includes(await this.stockTransactionStatus) &&
                (await (await this.$.old).valuedCost) !== val
            ) {
                cx.error.addLocalized(
                    '@sage/xtrem-sales/nodes__sales_return_receipt_line__modification_of_the_valued_cost_is_forbidden',
                    'The valued cost cannot be updated.',
                );
            }
        },
    })
    readonly valuedCost: Promise<decimal>;

    @decorators.collectionProperty<SalesReturnReceiptLine, 'toReturnRequestLines'>({
        isPublished: true,
        node: () => xtremSales.nodes.SalesReturnRequestLineToSalesReturnReceiptLine,
        isVital: true,
        reverseReference: 'document',
        dependsOn: ['quantity'],
    })
    readonly toReturnRequestLines: Collection<xtremSales.nodes.SalesReturnRequestLineToSalesReturnReceiptLine>;

    @decorators.collectionProperty<SalesReturnReceiptLine, 'salesShipmentLines'>({
        isPublished: true,
        node: () => xtremSales.nodes.SalesShipmentLineToSalesReturnReceiptLine,
        isVital: true,
        reverseReference: 'document',
        dependsOn: ['quantity', 'toReturnRequestLines'],
        defaultValue() {
            return SalesReturnReceiptLine.createSalesShipmentLinkLines(this.toReturnRequestLines);
        },
    })
    readonly salesShipmentLines: Collection<xtremSales.nodes.SalesShipmentLineToSalesReturnReceiptLine>;

    async updateTempLinesDicts(action: string): Promise<void> {
        await xtremSales.classes.BaseSalesDocumentsCreator.fillTempSalesDocumentDicts(
            action,
            'toReturnRequestLines',
            '__salesReturnRequests',
            this,
        );

        await xtremSales.classes.BaseSalesDocumentsCreator.fillTempSalesDocumentDicts(
            action,
            'salesShipmentLines',
            '__salesShipments',
            this,
        );
    }

    @decorators.jsonPropertyOverride<SalesReturnReceiptLine, 'computedAttributes'>({
        dependsOn: ['item', { document: ['site', 'shipToCustomer'] }],
        async computeValue() {
            if ((await this.toReturnRequestLines.length) > 0) {
                const salesReturnRequestHeader = await (
                    await (
                        await this.toReturnRequestLines.elementAt(0)
                    ).linkedDocument
                ).document;
                return xtremSales.functions.computeAttributes(
                    this.$.context,
                    await salesReturnRequestHeader.site,
                    await (
                        await this.document
                    ).site,
                    await this.item,
                    await salesReturnRequestHeader.billToCustomer,
                );
            }
            const document = await this.document;
            // TODO: when enabling direct receipt, change 2d and last parameter
            return xtremSales.functions.computeAttributes(
                this.$.context,
                await document.site,
                await document.site,
                await this.item,
                await document.shipToCustomer,
            );
        },
    })
    override readonly computedAttributes: Promise<object>;

    @decorators.collectionProperty<SalesReturnReceiptLine, 'stockDetails'>({
        isPublished: true,
        reverseReference: 'documentLine',
        dependsOn: ['salesShipmentLines'], // the stock cost comes from the origin shipment
        isVital: true,
        node: () => xtremStockData.nodes.StockReceiptDetail,
    })
    readonly stockDetails: Collection<xtremStockData.nodes.StockReceiptDetail>;

    @decorators.jsonProperty<SalesReturnReceiptLine, 'jsonStockDetails'>({
        isPublished: true,
        dependsOn: ['stockDetails'],
        async setValue(details) {
            if (details && details.length > 0) {
                await this.$.set({
                    stockDetails: xtremStockData.functions.stockDetailLib.parseDetails(
                        details,
                    ) as NodeCreateData<xtremStockData.nodes.StockReceiptDetail>[],
                });
            }
        },
        async computeValue() {
            if ((await this.stockDetails.length) > 0) {
                return this.stockDetails
                    .map(detail =>
                        xtremStockData.functions.stockDetailLib.filterStockDetailProperties(
                            detail,
                            xtremStockData.nodes.StockReceiptDetail,
                            {
                                onlyIds: true,
                            },
                        ),
                    )
                    .toArray();
            }
            // forcing typing to accept an empty object
            return {} as any;
        },
    })
    readonly jsonStockDetails: Promise<Array<JsonType<NodeCreateData<xtremStockData.nodes.StockReceiptDetail>>>>;

    @decorators.collectionProperty<SalesReturnReceiptLine, 'stockMovements'>({
        isPublished: true,
        reverseReference: 'documentLine',
        node: () => xtremStockData.nodes.StockJournal,
    })
    readonly stockMovements: Collection<xtremStockData.nodes.StockJournal>;

    @decorators.collectionProperty<SalesReturnReceiptLine, 'stockTransactions'>({
        isPublished: true,
        reverseReference: 'documentLine',
        isVital: true,
        node: () => xtremStockData.nodes.StockTransaction,
    })
    readonly stockTransactions: Collection<xtremStockData.nodes.StockTransaction>;

    @decorators.enumProperty<SalesReturnReceiptLine, 'stockTransactionStatus'>({
        isStored: true,
        isPublished: true,
        defaultValue: 'draft',
        dataType: () => xtremStockData.enums.StockDocumentTransactionStatusDataType,
    })
    readonly stockTransactionStatus: Promise<xtremStockData.enums.StockDocumentTransactionStatus>;

    // the sourceDocumentNumber property on line level is needed for the accounting interface
    @decorators.stringProperty<SalesReturnReceiptLine, 'sourceDocumentNumber'>({
        dataType: () => xtremMasterData.dataTypes.documentNumber,
        dependsOn: ['origin', 'toReturnRequestLines'],
        async computeValue() {
            return (await this.origin) === 'return' && (await this.toReturnRequestLines.length) > 0
                ? (await (await (await this.toReturnRequestLines.elementAt(0)).linkedDocument).document).number
                : '';
        },
    })
    readonly sourceDocumentNumber: Promise<string>;

    @decorators.enumProperty<SalesReturnReceiptLine, 'sourceDocumentType'>({
        isPublished: true,
        isNullable: true,
        dataType: () => xtremFinanceData.enums.sourceDocumentTypeDataType,
        dependsOn: ['origin'],
        async getValue() {
            return (await this.origin) === 'return' ? 'salesReturnRequest' : null;
        },
    })
    readonly sourceDocumentType: Promise<xtremFinanceData.enums.SourceDocumentType | null>;

    @decorators.textStreamPropertyOverride<SalesReturnReceiptLine, 'internalNote'>({
        async isFrozen() {
            return isPostedErrorClosed(await (await this.document).status);
        },
    })
    override readonly internalNote: Promise<TextStream>;

    private static async createSalesShipmentLinkLines(
        toReturnRequestLines: Collection<xtremSales.nodes.SalesReturnRequestLineToSalesReturnReceiptLine>,
    ): Promise<NodeCreateData<xtremSales.nodes.SalesShipmentLineToSalesReturnReceiptLine>[]> {
        const salesReturnRequestLine = await toReturnRequestLines.at(0);
        if (salesReturnRequestLine && (await (await salesReturnRequestLine.linkedDocument).salesShipmentLines.at(0))) {
            return [
                {
                    _id: salesReturnRequestLine._id - 100000,
                    linkedDocument: await (
                        await (await salesReturnRequestLine.linkedDocument).salesShipmentLines.elementAt(0)
                    ).linkedDocument,
                    amount: await salesReturnRequestLine.amount,
                    quantity: await salesReturnRequestLine.quantity,
                    quantityInStockUnit: await salesReturnRequestLine.quantityInStockUnit,
                },
            ];
        }
        return [];
    }

    async quantityInStockUnitComputed(): Promise<decimal> {
        return +new Decimal((await this.quantity) * (await this.unitToStockUnitConversionFactor)).toDecimalPlaces(
            (await (await this.stockUnit)?.decimalDigits) || 0,
        );
    }
}
