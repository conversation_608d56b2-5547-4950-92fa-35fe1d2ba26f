import type { Collection, Context, Reference, TextStream, ValidationContext, decimal, integer } from '@sage/xtrem-core';
import { BusinessRuleError, NodeStatus, ValidationSeverity, date, decorators, useDefaultValue } from '@sage/xtrem-core';
import { Decimal } from '@sage/xtrem-decimal';
import * as xtremFinanceData from '@sage/xtrem-finance-data';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremStockData from '@sage/xtrem-stock-data';
import * as xtremSystem from '@sage/xtrem-system';
import * as xtremTax from '@sage/xtrem-tax';
import { isPostedErrorClosed } from '../functions/sales-document-common-lib';
import { updateLineNotesOnCreation } from '../functions/sales-invoice-lib';
import * as xtremSales from '../index';

@decorators.subNode<SalesInvoiceLine>({
    extends: () => xtremMasterData.nodes.BaseDocumentItemLine,
    isPublished: true,
    isVitalCollectionChild: true,
    canSearch: true,
    canRead: true,

    async controlBegin(cx: ValidationContext) {
        if (this.$.status === NodeStatus.added) {
            await xtremSales.events.controlBegin.invoiceLine.checkIfInvoiceDocumentIsInDraftStatus(cx, this);

            await xtremSales.events.controlBegin.invoiceLine.checkIfSelectedItemIsStockManaged(cx, this);
        }
        if (this.$.status === NodeStatus.modified) {
            await xtremSales.events.controlBegin.invoiceLine.checkIfInvoiceLineIsLinkedToSalesShipmentLine(cx, this);

            await xtremSales.events.controlBegin.invoiceLine.checkIsInvoiceIsCreatedAmidItemUpdate(cx, this);

            await xtremSales.events.controlBegin.invoiceLine.frozeSaleUnitAfterCreation(cx, this);
        }
    },

    async saveBegin() {
        if (this.$.status === NodeStatus.modified) {
            const document = await this.document;
            const oldNode = await this.$.old;
            if (
                ['posted', 'inProgress', 'error'].includes(await (await document.$.old).status) &&
                (await this.creditStatus) === (await oldNode.creditStatus) &&
                !(await document.forceUpdateForFinance)
            ) {
                throw new BusinessRuleError(
                    this.$.context.localize(
                        '@sage/xtrem-sales/nodes__sales_invoice_line__update_not_allowed_status_posted',
                        'The sales invoice is posted. You cannot update it.',
                    ),
                );
            }

            if ((await oldNode.quantity) !== (await this.quantity)) {
                await this.updateTempLinesDicts('update');
            }
        }
        await updateLineNotesOnCreation(this);
    },

    async createEnd() {
        await this.updateTempLinesDicts('create');
    },

    async controlDelete(cx): Promise<void> {
        const document = await this.document;
        if (['posted', 'inProgress', 'error'].includes(await document.status)) {
            cx.addDiagnose(
                ValidationSeverity.error,
                cx.localize(
                    '@sage/xtrem-sales/nodes__sales_invoice_line__deletion_forbidden_posted',
                    'Line deletion is not allowed. The sales invoice is posted.',
                ),
            );
        }

        if ((await this.salesShipmentLines.length) > 0 && !document.__deleteInvoice) {
            cx.addDiagnose(
                ValidationSeverity.error,
                cx.localize(
                    '@sage/xtrem-sales/nodes__sales_invoice_line__deletion_forbidden_shipment_linked',
                    'Line deletion is not allowed. The sales invoice line is linked to a sales shipment line.',
                ),
            );
        }
        await this.updateTempLinesDicts('delete');
    },
})
export class SalesInvoiceLine
    extends xtremMasterData.nodes.BaseDocumentItemLine
    implements xtremFinanceData.interfaces.InvoiceDocumentLine
{
    @decorators.referencePropertyOverride<SalesInvoiceLine, 'document'>({
        node: () => xtremSales.nodes.SalesInvoice,
    })
    override readonly document: Reference<xtremSales.nodes.SalesInvoice>;

    @decorators.stringPropertyOverride<SalesInvoiceLine, 'documentNumber'>({
        async getValue() {
            return (await this.document).number;
        },
    })
    override readonly documentNumber: Promise<string>;

    @decorators.integerPropertyOverride<SalesInvoiceLine, 'documentId'>({
        async getValue() {
            return (await this.document)._id;
        },
    })
    override readonly documentId: Promise<integer>;

    @decorators.enumProperty<SalesInvoiceLine, 'originDocumentType'>({
        isPublished: true,
        dataType: () => xtremSales.enums.salesOriginDocumentTypeDataType,
        getValue() {
            return this.origin as unknown as xtremSales.enums.SalesOriginDocumentType;
        },
    })
    readonly originDocumentType: Promise<xtremSales.enums.SalesOriginDocumentType>;

    @decorators.referencePropertyOverride<SalesInvoiceLine, 'item'>({
        dependsOn: ['origin'],
        filters: {
            control: {
                isSold: true,
                async itemSites() {
                    return { _atLeast: 1, site: await this.providerSite };
                },
                async isStockManaged() {
                    return (await this.origin) === 'direct' ? false : { _in: [true, false] };
                },
            },
        },
        async isFrozen() {
            return (await this.origin) !== 'direct';
        },
    })
    override readonly item: Reference<xtremMasterData.nodes.Item>;

    /** deprecated */
    @decorators.referenceProperty<SalesInvoiceLine, 'salesSite'>({
        isPublished: true,
        node: () => xtremSystem.nodes.Site,
        getValue() {
            return this.site;
        },
    })
    readonly salesSite: Reference<xtremSystem.nodes.Site>;

    @decorators.referencePropertyOverride<SalesInvoiceLine, 'site'>({
        filters: {
            control: {
                isSales: true,
            },
        },
    })
    override readonly site: Reference<xtremSystem.nodes.Site>;

    @decorators.decimalPropertyOverride<SalesInvoiceLine, 'quantity'>({
        dependsOn: ['item', 'unit', { document: ['billToCustomer'] }],
        async control(cx, val) {
            await xtremSales.events.control.quantityInSalesUnitControls.checkQuantityInSalesUnit(
                this.$.context,
                cx,
                val,
                await this.item,
                await (
                    await this.document
                ).billToCustomer,
                await this.unit,
                {
                    canBeZero: false,
                    excludeMinimumQuantity: true,
                },
            );
        },
    })
    override readonly quantity: Promise<decimal>;

    // @TODO DELETE AFTER BASE DOCUMENT REFACTORING
    @decorators.decimalProperty<SalesInvoiceLine, 'quantityInSalesUnit'>({
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.quantityInSalesUnit,
        getValue() {
            return this.quantity;
        },
    })
    readonly quantityInSalesUnit: Promise<decimal>;

    @decorators.referencePropertyOverride<SalesInvoiceLine, 'unit'>({
        dependsOn: ['item', { document: ['billToCustomer'] }],
        async defaultValue() {
            return (await xtremMasterData.functions.getSalesUnitDefaultValue(
                this.$.context,
                await this.item,
                (await (await this.document).billToCustomer)._id,
            ))!;
        },
        async control(cx, val) {
            await cx.error
                .if(await val.id)
                .is.not.in(
                    await xtremMasterData.functions.getValidSalesUnitList(
                        this.$.context,
                        await this.item,
                        await (
                            await this.document
                        ).billToCustomer,
                    ),
                );
        },
    })
    override readonly unit: Reference<xtremMasterData.nodes.UnitOfMeasure>;

    // @TODO DELETE AFTER BASE DOCUMENT REFACTORING
    @decorators.referenceProperty<SalesInvoiceLine, 'salesUnit'>({
        isPublished: true,
        dependsOn: ['unit'],
        node: () => xtremMasterData.nodes.UnitOfMeasure,
        getValue() {
            return this.unit;
        },
    })
    readonly salesUnit: Reference<xtremMasterData.nodes.UnitOfMeasure>;

    @decorators.decimalPropertyOverride<SalesInvoiceLine, 'unitToStockUnitConversionFactor'>({
        dependsOn: ['item', { document: ['billToCustomer'] }],
        async defaultValue() {
            return xtremMasterData.functions.calculateConversionFactor(
                this.$.context,
                await this.item,
                (await (await this.document).billToCustomer)?._id,
            );
        },
        updatedValue: useDefaultValue,
    })
    override readonly unitToStockUnitConversionFactor: Promise<decimal>;

    // @TODO SHOULD BE REMOVED AFTER BASE DOCUMENT REFACTORING
    @decorators.decimalProperty<SalesInvoiceLine, 'salesUnitToStockUnitConversionFactor'>({
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.coefficientDataType,
        getValue() {
            return this.unitToStockUnitConversionFactor;
        },
    })
    readonly salesUnitToStockUnitConversionFactor: Promise<decimal>;

    @decorators.decimalPropertyOverride<SalesInvoiceLine, 'quantityInStockUnit'>({
        dependsOn: ['stockUnit', 'quantityInSalesUnit', 'unitToStockUnitConversionFactor'],
        defaultValue() {
            return this.quantityInStockUnitComputed();
        },
        updatedValue: useDefaultValue,
        async control(cx, val) {
            await cx.error.if(Number(val)).is.not.equal.to(await this.quantityInStockUnitComputed());
        },
    })
    override readonly quantityInStockUnit: Promise<decimal>;

    @decorators.referencePropertyOverride<SalesInvoiceLine, 'stockUnit'>({
        async control(cx, val) {
            await cx.error.if(await val.id).is.not.equal.to(await (await (await this.item).stockUnit).id);
        },
    })
    override readonly stockUnit: Reference<xtremMasterData.nodes.UnitOfMeasure>;

    @decorators.collectionProperty<SalesInvoiceLine, 'discountCharges'>({
        isVital: true,
        isPublished: true,
        reverseReference: 'document',
        node: () => xtremSales.nodes.SalesInvoiceLineDiscountCharge,
    })
    readonly discountCharges: Collection<xtremSales.nodes.SalesInvoiceLineDiscountCharge>;

    @decorators.decimalProperty<SalesInvoiceLine, 'discount'>({
        excludedFromPayload: true,
        isPublished: true,
        isNullable: true,
        dataType: () => xtremMasterData.dataTypes.baseDecimal,
        dependsOn: ['grossPrice', { discountCharges: ['sign', 'basis', 'valueType', 'value'] }],
        getValue() {
            return xtremMasterData.functions.computeDiscountChargeValue(this.discountCharges, 'decrease');
        },
        async setValue(val: decimal) {
            await xtremMasterData.functions.setDiscountChargeValue(
                this.discountCharges,
                'decrease',
                val,
                (await this.grossPrice) ?? 0,
            );
        },
    })
    readonly discount: Promise<decimal | null>;

    @decorators.decimalProperty<SalesInvoiceLine, 'charge'>({
        excludedFromPayload: true,
        isPublished: true,
        isNullable: true,
        dataType: () => xtremMasterData.dataTypes.baseDecimal,
        dependsOn: ['grossPrice', { discountCharges: ['sign'] }],
        getValue() {
            return xtremMasterData.functions.computeDiscountChargeValue(this.discountCharges, 'increase');
        },
        async setValue(val: decimal) {
            await xtremMasterData.functions.setDiscountChargeValue(
                this.discountCharges,
                'increase',
                val,
                (await this.grossPrice) ?? 0,
            );
        },
    })
    readonly charge: Promise<decimal | null>;

    @decorators.decimalProperty<SalesInvoiceLine, 'grossPrice'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
        dataType: () => xtremMasterData.dataTypes.priceInSalesPrice,
        async control(cx, val) {
            await cx.error.if(val).is.negative();
        },
    })
    readonly grossPrice: Promise<decimal | null>;

    @decorators.decimalProperty<SalesInvoiceLine, 'discountDeterminated'>({
        excludedFromPayload: true,
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.baseDecimal,
        dependsOn: ['grossPrice', { discountCharges: ['sign', 'basis', 'valueType', 'valueDeterminated'] }],
        getValue() {
            return xtremMasterData.functions.computeDiscountChargeValueDeterminated(this.discountCharges, 'decrease');
        },
        async setValue(val: decimal) {
            await xtremMasterData.functions.setDiscountChargeValueDeterminated(this.discountCharges, 'decrease', val);
        },
    })
    readonly discountDeterminated: Promise<decimal>;

    @decorators.decimalProperty<SalesInvoiceLine, 'chargeDeterminated'>({
        excludedFromPayload: true,
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.baseDecimal,
        dependsOn: ['grossPrice', { discountCharges: ['sign', 'basis', 'valueType', 'valueDeterminated'] }],
        getValue() {
            return xtremMasterData.functions.computeDiscountChargeValueDeterminated(this.discountCharges, 'increase');
        },
        async setValue(val: decimal) {
            await xtremMasterData.functions.setDiscountChargeValueDeterminated(this.discountCharges, 'increase', val);
        },
    })
    readonly chargeDeterminated: Promise<decimal>;

    @decorators.decimalProperty<SalesInvoiceLine, 'grossPriceDeterminated'>({
        excludedFromPayload: true,
        isStored: true,
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.priceInSalesPrice,
    })
    readonly grossPriceDeterminated: Promise<decimal>;

    @decorators.decimalProperty<SalesInvoiceLine, 'netPrice'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.priceInSalesPrice,
        dependsOn: [
            'grossPrice',
            'quantity',
            { discountCharges: ['sign', 'basis', 'valueType', 'value'] },
            { document: ['currency'] },
        ],
        async defaultValue() {
            return (await this.currency)
                ? xtremMasterData.functions.calculateNetPrice(
                      this.discountCharges,
                      (await this.grossPrice) ?? 0,
                      await this.quantity,
                      await (
                          await (
                              await this.document
                          ).currency
                      ).decimalDigits,
                  )
                : 0;
        },
        updatedValue: useDefaultValue,
    })
    readonly netPrice: Promise<decimal>;

    @decorators.decimalProperty<SalesInvoiceLine, 'taxableAmount'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.priceInSalesPrice,
        dependsOn: ['taxes'],
        async control(cx, val) {
            await cx.error.if(val).is.negative();
        },
        updatedValue() {
            return this.taxes.sum(tax => tax.taxableAmount);
        },
    })
    readonly taxableAmount: Promise<decimal>;

    @decorators.decimalProperty<SalesInvoiceLine, 'taxAmount'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.priceInSalesPrice,
        dependsOn: ['taxes'],
        async control(cx, val) {
            await cx.error.if(val).is.negative();
        },
        updatedValue() {
            return this.taxes.sum(tax => tax.taxAmount);
        },
    })
    readonly taxAmount: Promise<decimal>;

    @decorators.decimalProperty<SalesInvoiceLine, 'exemptAmount'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.priceInSalesPrice,
        dependsOn: ['taxes'],
        async control(cx, val) {
            await cx.error.if(val).is.negative();
        },
        updatedValue() {
            return this.taxes.sum(tax => tax.exemptAmount);
        },
    })
    readonly exemptAmount: Promise<decimal>;

    @decorators.dateProperty<SalesInvoiceLine, 'taxDate'>({
        isStoredOutput: true,
        isPublished: true,
        dependsOn: [{ document: ['date'] }],
        async defaultValue() {
            return (await this.document).date;
        },
        updatedValue: useDefaultValue,
    })
    readonly taxDate: Promise<date>;

    @decorators.decimalProperty<SalesInvoiceLine, 'netPriceExcludingTax'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.priceInSalesPrice,
        async control(cx, val) {
            await cx.error.if(val).is.negative();
        },
        dependsOn: [
            'grossPrice',
            'quantity',
            { discountCharges: ['sign', 'basis', 'valueType', 'value'] },
            { document: ['currency'] },
        ],
        async defaultValue() {
            return (await this.currency)
                ? xtremMasterData.functions.calculateNetPrice(
                      this.discountCharges,
                      (await this.grossPrice) ?? 0,
                      await this.quantity,
                      await (
                          await (
                              await this.document
                          ).currency
                      ).decimalDigits,
                  )
                : 0;
        },
        updatedValue: useDefaultValue,
    })
    readonly netPriceExcludingTax: Promise<decimal>;

    @decorators.decimalProperty<SalesInvoiceLine, 'netPriceIncludingTax'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.priceInSalesPrice,
        async control(cx, val) {
            await cx.error.if(val).is.negative();
        },
        dependsOn: ['netPriceExcludingTax', 'taxAmount', 'quantity'],
        async defaultValue() {
            return (await this.netPriceExcludingTax) + (await this.taxAmount) / (await this.quantity);
        },
        updatedValue: useDefaultValue,
    })
    readonly netPriceIncludingTax: Promise<decimal>;

    @decorators.decimalProperty<SalesInvoiceLine, 'amountIncludingTax'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.priceInSalesPrice,
        dependsOn: ['amountExcludingTax', 'taxAmountAdjusted'],
        async defaultValue() {
            return (await this.amountExcludingTax) + (await this.taxAmountAdjusted);
        },
        updatedValue: useDefaultValue,
    })
    readonly amountIncludingTax: Promise<decimal>;

    @decorators.decimalProperty<SalesInvoiceLine, 'amountIncludingTaxInCompanyCurrency'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremSales.dataTypes.salesPriceDataType,
        async control(cx, val) {
            await cx.error.if(val).is.negative();
        },
        dependsOn: [
            'quantity',
            'netPrice',
            'amountIncludingTax',
            { document: ['site', 'currency', 'companyFxRateDivisor', 'companyFxRate'] },
        ],
        async defaultValue() {
            const document = await this.document;
            // convert transaction amount into company currency
            return xtremMasterData.sharedFunctions.convertAmount(
                await this.amountIncludingTax,
                await document.companyFxRate,
                await document.companyFxRateDivisor,
                await (
                    await document.currency
                ).decimalDigits,
                await (
                    await (
                        await (
                            await document.site
                        ).legalCompany
                    ).currency
                ).decimalDigits,
            );
        },
        updatedValue: useDefaultValue,
    })
    readonly amountIncludingTaxInCompanyCurrency: Promise<decimal>;

    @decorators.decimalProperty<SalesInvoiceLine, 'taxAmountAdjusted'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.priceDataType,
        dependsOn: ['taxes'],
        async control(cx, val) {
            await cx.error.if(val).is.negative();
        },
        updatedValue() {
            return this.taxes.sum(tax => tax.taxAmountAdjusted);
        },
    })
    readonly taxAmountAdjusted: Promise<decimal>;

    @decorators.decimalProperty<SalesInvoiceLine, 'amountExcludingTax'>({
        isStoredOutput: true,
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.priceInSalesPrice,
        dependsOn: ['quantity', 'netPriceExcludingTax'],
        async defaultValue() {
            return (await this.quantity) * (await this.netPriceExcludingTax);
        },
        updatedValue: useDefaultValue,
    })
    readonly amountExcludingTax: Promise<decimal>;

    @decorators.decimalProperty<SalesInvoiceLine, 'amountExcludingTaxInCompanyCurrency'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremSales.dataTypes.salesPriceDataType,
        async control(cx, val) {
            await cx.error.if(val).is.negative();
        },
        dependsOn: [
            'quantity',
            'netPrice',
            'amountExcludingTax',
            { document: ['site', 'currency', 'companyFxRateDivisor', 'companyFxRate'] },
        ],
        async defaultValue() {
            const document = await this.document;
            // convert transaction amount into company currency
            return xtremMasterData.sharedFunctions.convertAmount(
                await this.amountExcludingTax,
                await document.companyFxRate,
                await document.companyFxRateDivisor,
                await (
                    await document.currency
                ).decimalDigits,
                await (
                    await (
                        await (
                            await document.site
                        ).legalCompany
                    ).currency
                ).decimalDigits,
            );
        },
        updatedValue: useDefaultValue,
    })
    readonly amountExcludingTaxInCompanyCurrency: Promise<decimal>;

    @decorators.decimalProperty<SalesInvoiceLine, 'stockCostAmountInCompanyCurrency'>({
        isStoredOutput: true,
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.amountInCompanyCurrency,
        dependsOn: ['quantityInStockUnit', 'item', 'site', 'netPrice', 'origin', 'salesShipmentLines'],
        defaultValue() {
            return this.getNewStockCostAmount();
        },
        updatedValue: useDefaultValue,
    })
    readonly stockCostAmountInCompanyCurrency: Promise<decimal>;

    @decorators.decimalProperty<SalesInvoiceLine, 'grossProfitAmountInCompanyCurrency'>({
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.amountInCompanyCurrency,
        dependsOn: ['amountExcludingTaxInCompanyCurrency', 'stockCostAmountInCompanyCurrency'],
        async getValue() {
            return (await this.amountExcludingTaxInCompanyCurrency) - (await this.stockCostAmountInCompanyCurrency);
        },
    })
    readonly grossProfitAmountInCompanyCurrency: Promise<decimal>;

    @decorators.decimalProperty<SalesInvoiceLine, 'stockCostAmount'>({
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.amountInTransactionCurrency,
        dependsOn: ['stockCostAmountInCompanyCurrency'],
        async getValue() {
            const document = await this.document;
            // convert from company currency to transaction currency
            return xtremMasterData.sharedFunctions.convertAmount(
                await this.stockCostAmountInCompanyCurrency,
                await document.companyFxRateDivisor,
                await document.companyFxRate,
                await (
                    await (
                        await (
                            await document.site
                        ).legalCompany
                    ).currency
                ).decimalDigits,
                await (
                    await document.currency
                ).decimalDigits,
            );
        },
    })
    readonly stockCostAmount: Promise<decimal>;

    @decorators.decimalProperty<SalesInvoiceLine, 'grossProfitAmount'>({
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.amountInTransactionCurrency,
        dependsOn: ['amountExcludingTax', 'stockCostAmount'],
        async getValue() {
            return (await this.amountExcludingTax) - (await this.stockCostAmount);
        },
    })
    readonly grossProfitAmount: Promise<decimal>;

    @decorators.decimalProperty<SalesInvoiceLine, 'stockCostUnit'>({
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.orderCostDataType,
        dependsOn: ['quantity', 'stockCostAmount'],
        // eslint-disable-next-line @sage/xtrem/property-decorators-errors
        async computeValue() {
            const quantity = await this.quantity;
            return quantity ? (await this.stockCostAmount) / quantity : 0;
        },
    })
    readonly stockCostUnit: Promise<decimal>;

    @decorators.decimalProperty<SalesInvoiceLine, 'grossProfit'>({
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.priceDataType,
        dependsOn: ['netPrice', 'stockCostUnit'],
        // eslint-disable-next-line @sage/xtrem/property-decorators-errors
        async computeValue() {
            return (await this.netPrice) - (await this.stockCostUnit);
        },
    })
    readonly grossProfit: Promise<decimal>;

    @decorators.referenceProperty<SalesInvoiceLine, 'consumptionLinkedAddress'>({
        isStored: true,
        isPublished: true,
        isRequired: true,
        dependsOn: [{ document: ['billToLinkedAddress'] }],
        node: () => xtremMasterData.nodes.BusinessEntityAddress,
        async defaultValue() {
            return (await this.document).billToLinkedAddress;
        },
    })
    readonly consumptionLinkedAddress: Reference<xtremMasterData.nodes.BusinessEntityAddress>;

    @decorators.referenceProperty<SalesInvoiceLine, 'consumptionAddress'>({
        isPublished: true,
        isStored: true,
        isMutable: true,
        isNullable: true,
        dependsOn: ['consumptionLinkedAddress'],
        node: () => xtremMasterData.nodes.Address,

        async defaultValue() {
            const document = await this.document;
            const billToAddress = await document?.billToAddress;
            const consumptionLinkedAddress = await this.consumptionLinkedAddress;
            if ((await document?.billToLinkedAddress) === consumptionLinkedAddress && billToAddress) {
                return billToAddress;
            }
            return xtremSales.functions.getSalesAddressDetail(consumptionLinkedAddress);
        },
        updatedValue: useDefaultValue,
    })
    readonly consumptionAddress: Reference<xtremMasterData.nodes.Address | null>;

    @decorators.referenceProperty<SalesInvoiceLine, 'providerSite'>({
        isStored: true,
        isPublished: true,
        isRequired: true,
        node: () => xtremSystem.nodes.Site,
        dependsOn: [{ site: ['legalCompany'] }, { document: ['site'] }],
        filters: {
            control: {
                isSales: true,
                async legalCompany() {
                    return (await this.site).legalCompany;
                },
                async itemSites() {
                    const item = await this.item;
                    return item ? { _atLeast: 1, item } : { _atLeast: 1 };
                },
            },
        },
        async defaultValue() {
            return (await this.document).site;
        },
        async isFrozen() {
            return (await this.origin) !== 'direct';
        },
    })
    readonly providerSite: Reference<xtremSystem.nodes.Site>;

    @decorators.referenceProperty<SalesInvoiceLine, 'providerSiteLinkedAddress'>({
        isStored: true,
        isPublished: true,
        isRequired: true,
        dependsOn: ['providerSite'],
        node: () => xtremMasterData.nodes.BusinessEntityAddress,
        async defaultValue() {
            return (await this.providerSite).primaryAddress;
        },
    })
    readonly providerSiteLinkedAddress: Reference<xtremMasterData.nodes.BusinessEntityAddress>;

    @decorators.referenceProperty<SalesInvoiceLine, 'providerSiteAddress'>({
        isPublished: true,
        isStored: true,
        isMutable: true,
        isNullable: true,
        dependsOn: ['providerSiteLinkedAddress'],
        node: () => xtremMasterData.nodes.Address,

        async defaultValue() {
            const document = await this.document;
            const salesSiteAddress = await document?.salesSiteAddress;
            const providerSiteLinkedAddress = await this.providerSiteLinkedAddress;
            if ((await document?.salesSiteLinkedAddress) === providerSiteLinkedAddress && salesSiteAddress) {
                return salesSiteAddress;
            }
            return xtremSales.functions.getSalesAddressDetail(providerSiteLinkedAddress);
        },
        updatedValue: useDefaultValue,
    })
    readonly providerSiteAddress: Reference<xtremMasterData.nodes.Address | null>;

    @decorators.collectionProperty<SalesInvoiceLine, 'salesOrderLines'>({
        isPublished: true,
        node: () => xtremSales.nodes.SalesOrderLineToSalesInvoiceLine,
        isVital: true,
        reverseReference: 'document',
        dependsOn: ['quantity'],
    })
    readonly salesOrderLines: Collection<xtremSales.nodes.SalesOrderLineToSalesInvoiceLine>;

    /** we have a collection, but at the moment we can only have one line, and it looks like it's going to last... */
    @decorators.collectionProperty<SalesInvoiceLine, 'salesShipmentLines'>({
        isPublished: true,
        node: () => xtremSales.nodes.SalesShipmentLineToSalesInvoiceLine,
        isVital: true,
        reverseReference: 'document',
        dependsOn: ['quantity'],
    })
    readonly salesShipmentLines: Collection<xtremSales.nodes.SalesShipmentLineToSalesInvoiceLine>;

    @decorators.referenceProperty<SalesInvoiceLine, 'salesShipmentDocument'>({
        isNullable: true,
        node: () => xtremSales.nodes.SalesShipment,
        async getValue() {
            return (
                (await (await this.salesShipmentLines.takeOne(line => line._id > 0))?.linkedDocument)?.document ?? null
            );
        },
    })
    readonly salesShipmentDocument: Reference<xtremSales.nodes.SalesShipment> | null;

    async getSourceDocument(): Promise<xtremSales.nodes.SalesShipment | null> {
        if ((await this.origin) === 'shipment') {
            return this.salesShipmentDocument;
        }
        return null;
    }

    @decorators.stringProperty<SalesInvoiceLine, 'sourceDocumentNumber'>({
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.documentNumber,
        async computeValue() {
            const sourceDocument = await this.getSourceDocument();
            if (!sourceDocument) return '';
            if (sourceDocument.$.isValueDeferred('number')) {
                throw new Error('The source document number is deferred');
            }
            return sourceDocument.number;
        },
    })
    readonly sourceDocumentNumber: Promise<string>;

    @decorators.enumProperty<SalesInvoiceLine, 'sourceDocumentType'>({
        isPublished: true,
        isNullable: true,
        dataType: () => xtremFinanceData.enums.sourceDocumentTypeDataType,
        async getValue() {
            return (await this.origin) === 'shipment' ? 'salesShipment' : null;
        },
    })
    readonly sourceDocumentType: Promise<xtremFinanceData.enums.SourceDocumentType | null>;

    // needed for the accounting engine
    @decorators.integerProperty<SalesInvoiceLine, 'sourceDocumentSysId'>({
        isPublished: true,
        async computeValue() {
            return (await this.getSourceDocument())?._id ?? 0;
        },
    })
    readonly sourceDocumentSysId: Promise<integer>;

    @decorators.jsonPropertyOverride<SalesInvoiceLine, 'storedDimensions'>({
        dependsOn: ['item', { document: ['site', 'billToCustomer'] }],
        async defaultValue() {
            const site = await (await this.document).site;
            return xtremFinanceData.functions.getDefaultDimensions(this.$.context, {
                dimensionDefinitionLevel: 'salesDirect',
                companyId: (await site.legalCompany)._id,
                site,
                customer: await (await this.document).billToCustomer,
                item: await this.item,
            });
        },
    })
    override readonly storedDimensions: Promise<object | null>;

    @decorators.jsonPropertyOverride<SalesInvoiceLine, 'storedAttributes'>({
        dependsOn: ['item', { document: ['site', 'billToCustomer'] }],
        async defaultValue() {
            const site = await (await this.document).site;
            return xtremFinanceData.functions.getDefaultAttributes(this.$.context, {
                dimensionDefinitionLevel: 'salesDirect',
                companyId: (await site.legalCompany)._id,
                site,
                customer: await (await this.document).billToCustomer,
                item: await this.item,
            });
        },
    })
    override readonly storedAttributes: Promise<xtremMasterData.interfaces.StoredAttributes | null>;

    @decorators.jsonPropertyOverride<SalesInvoiceLine, 'computedAttributes'>({
        dependsOn: ['item', { document: ['site', 'billToCustomer'] }, 'providerSite'],
        async computeValue() {
            return xtremSales.functions.computeAttributes(
                this.$.context,
                await this.site,
                await this.providerSite,
                await this.item,
                await (
                    await this.document
                ).billToCustomer,
            );
        },
    })
    override readonly computedAttributes: Promise<object>;

    @decorators.enumProperty<SalesInvoiceLine, 'creditStatus'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremSales.enums.salesDocumentCreditStatusDataType,
        defaultValue() {
            return 'notCredited';
        },
    })
    readonly creditStatus: Promise<xtremSales.enums.SalesDocumentCreditStatus>;

    @decorators.collectionProperty<SalesInvoiceLine, 'salesCreditMemoLines'>({
        // TODO: should we publish it?
        node: () => xtremSales.nodes.SalesInvoiceLineToSalesCreditMemoLine,
        reverseReference: 'linkedDocument',
    })
    readonly salesCreditMemoLines: Collection<xtremSales.nodes.SalesInvoiceLineToSalesCreditMemoLine>;

    @decorators.decimalProperty<SalesInvoiceLine, 'quantityCreditedInProgressInSalesUnit'>({
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.quantityInSalesUnit,
        getValue() {
            return this.salesCreditMemoLines
                .where(async line => ['draft'].includes(await (await (await line.document).document).status))
                .sum(async line => (await line.document).quantity);
        },
    })
    readonly quantityCreditedInProgressInSalesUnit: Promise<decimal>;

    @decorators.decimalProperty<SalesInvoiceLine, 'quantityCreditedPostedInSalesUnit'>({
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.quantityInSalesUnit,
        getValue() {
            return this.salesCreditMemoLines
                .where(async line =>
                    ['posted', 'inProgress', 'error'].includes(await (await (await line.document).document).status),
                )
                .sum(async line => (await line.document).quantity);
        },
    })
    readonly quantityCreditedPostedInSalesUnit: Promise<decimal>;

    @decorators.referenceProperty<SalesInvoiceLine, 'currency'>({
        isPublished: false,
        node: () => xtremMasterData.nodes.Currency,
        dependsOn: [{ document: ['currency'] }],
        async getValue() {
            return (await this.document).currency;
        },
    })
    readonly currency: Reference<xtremMasterData.nodes.Currency>;

    @decorators.enumProperty<SalesInvoiceLine, 'priceOrigin'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
        dataType: () => xtremSales.enums.salesPriceOriginDataType,
    })
    readonly priceOrigin: Promise<xtremSales.enums.SalesPriceOrigin | null>;

    @decorators.referenceProperty<SalesInvoiceLine, 'priceReason'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
        node: () => xtremMasterData.nodes.CustomerPriceReason,
    })
    readonly priceReason: Reference<xtremMasterData.nodes.CustomerPriceReason | null>;

    @decorators.enumProperty<SalesInvoiceLine, 'priceOriginDeterminated'>({
        excludedFromPayload: true,
        isStored: true,
        isPublished: true,
        isNullable: true,
        dataType: () => xtremSales.enums.salesPriceOriginDataType,
        dependsOn: ['priceOrigin'],
        defaultValue() {
            return this.priceOrigin;
        },
    })
    readonly priceOriginDeterminated: Promise<xtremSales.enums.SalesPriceOrigin | null>;

    @decorators.referenceProperty<SalesInvoiceLine, 'priceReasonDeterminated'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
        node: () => xtremMasterData.nodes.CustomerPriceReason,
        dependsOn: ['priceReason'],
        defaultValue() {
            return this.priceReason;
        },
    })
    readonly priceReasonDeterminated: Reference<xtremMasterData.nodes.CustomerPriceReason | null>;

    @decorators.booleanProperty<SalesInvoiceLine, 'isPriceDeterminated'>({
        excludedFromPayload: true,
        isStored: true,
        isPublished: true,
        defaultValue() {
            return false;
        },
    })
    readonly isPriceDeterminated: Promise<boolean>;

    @decorators.collectionProperty<SalesInvoiceLine, 'taxes'>({
        isVital: true,
        isPublished: true,
        reverseReference: 'document',
        dependsOn: ['currency', { document: ['status'] }],
        async isFrozen() {
            const document = await this.document;
            return (
                (await document.status) !== 'draft' &&
                (await (await (await this.salesSite).legalCompany).taxEngine) !== 'avalaraAvaTax' &&
                !(await document.forceUpdateForFinance)
            );
        },
        node: () => xtremSales.nodes.SalesInvoiceLineTax,
    })
    readonly taxes: Collection<xtremSales.nodes.SalesInvoiceLineTax>;

    @decorators.jsonProperty<SalesInvoiceLine, 'uiTaxes'>({
        excludedFromPayload: true,
        isPublished: true,
        dependsOn: ['taxes'],
        async computeValue() {
            return xtremTax.functions.prepareTaxesToUiTaxes<xtremSales.nodes.SalesInvoiceLine>(
                this,
                String(await (await this.document).taxEngine),
            );
        },
        async setValue(val: xtremTax.interfaces.UiTaxes | null) {
            await xtremTax.functions.updateTaxesFromUiTaxes(val, this, String(await (await this.document).taxEngine));
        },
    })
    readonly uiTaxes: Promise<xtremTax.interfaces.UiTaxes | null>;

    @decorators.enumProperty<SalesInvoiceLine, 'taxCalculationStatus'>({
        isPublished: true,
        dataType: () => xtremMasterData.enums.taxCalculationStatusDataType,
        dependsOn: ['taxes', { document: ['taxCalculationStatus'] }],
        async getValue() {
            const document = await this.document;
            const taxCalculationStatus = await document.taxCalculationStatus;
            if (
                taxCalculationStatus === 'failed' &&
                (await this.taxes.some(async tax => (await tax.isTaxMandatory) && !(await tax.taxReference)))
            ) {
                return 'failed';
            }
            if (taxCalculationStatus === 'notDone') {
                return 'notDone';
            }
            return 'done';
        },
    })
    readonly taxCalculationStatus: Promise<xtremMasterData.enums.TaxCalculationStatus>;

    @decorators.textStreamPropertyOverride<SalesInvoiceLine, 'internalNote'>({
        async isFrozen() {
            return isPostedErrorClosed(await (await this.document).status);
        },
    })
    override readonly internalNote: Promise<TextStream>;

    @decorators.textStreamPropertyOverride<SalesInvoiceLine, 'externalNote'>({
        async isFrozen() {
            return isPostedErrorClosed(await (await this.document).status);
        },
    })
    override readonly externalNote: Promise<TextStream>;

    /** to be sure the user know that externalNode will go to external documents */
    @decorators.booleanPropertyOverride<SalesInvoiceLine, 'isExternalNote'>({
        async isFrozen() {
            return isPostedErrorClosed(await (await this.document).status);
        },
    })
    override readonly isExternalNote: Promise<boolean>;

    @decorators.stringProperty<SalesInvoiceLine, 'customerNumber'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremSales.dataTypes.propertyDataType,
    })
    readonly customerNumber: Promise<string>;

    async updateTempLinesDicts(action: string): Promise<void> {
        await xtremSales.classes.BaseSalesDocumentsCreator.fillTempSalesDocumentDicts(
            action,
            'salesOrderLines',
            '__salesOrders',
            this,
        );
        await xtremSales.classes.BaseSalesDocumentsCreator.fillTempSalesDocumentDicts(
            action,
            'salesShipmentLines',
            '__salesShipments',
            this,
        );
    }

    /**
     * Calculates sales invoice line taxes. Mutation can be used by the frontend
     * during creation or update to show taxes to the user before he creates or updates invoice
     * @param context
     * @param site universal name because the same parameter name will be used in purchases.
     *             This mutation name will be passed as a parameter into the tax-panel page
     * @param businessPartner universal name
     * @param item
     * @param currency
     * @param amountExcludingTax
     * @param quantity
     * @param taxes an array of objects with optional parameter taxReference.
     *              If the array is left empty, mutation tries to automatically determine tax determination.
     *
     * @returns object
     */
    @decorators.mutation<typeof SalesInvoiceLine, 'calculateLineTaxes'>({
        // TODO change mutation to query when enhancement XT-19221 is done.
        isPublished: true,
        parameters: [
            {
                name: 'data',
                type: 'object',
                properties: xtremTax.functions.getCalculateLineTaxesParameterPropertyDefinition<
                    typeof SalesInvoiceLine
                >(xtremMasterData.nodes.Customer),
            },
        ],
        return: xtremTax.functions.getCalculateLineTaxesReturnDefinition<typeof SalesInvoiceLine>(),
    })
    static async calculateLineTaxes(
        context: Context,
        data: xtremTax.interfaces.CalculateLineTaxesParameters<xtremMasterData.nodes.Customer>,
    ): Promise<xtremTax.interfaces.CalculateLineTaxesReturn> {
        return xtremTax.functions.calculateLineTaxes(
            await context.create(
                xtremSales.nodes.SalesInvoice,
                { site: data.site, billToCustomer: data.businessPartner },
                { isOnlyForDefaultValues: true },
            ),
            await xtremSales.classes.SalesTaxCalculator.create(context, data.site),
            data,
        );
    }

    async quantityInStockUnitComputed(): Promise<decimal> {
        return +new Decimal((await this.quantity) * (await this.unitToStockUnitConversionFactor)).toDecimalPlaces(
            (await (await this.stockUnit)?.decimalDigits) || 0,
        );
    }

    async getNewStockCostAmount(): Promise<decimal> {
        // The cost comes from the shipment line if the origin is a shipment
        if ((await this.origin) === 'shipment' && (await this.salesShipmentLines.length)) {
            const shipmentLine = await (await this.salesShipmentLines.elementAt(0)).linkedDocument;
            return (
                ((await shipmentLine.stockCostAmountInCompanyCurrency) * (await this.quantityInStockUnit)) /
                (await shipmentLine.quantityInStockUnit)
            );
        }
        // In all other cases we have to calculate the stock cost
        return (
            await xtremStockData.nodeExtensions.ItemSiteExtension.getValuationCost(
                this.$.context,
                await this.item,
                await this.site,
                { quantity: await this.quantityInStockUnit, dateOfValuation: date.today(), valuationType: 'issue' },
            )
        ).amount;
    }
}
