import type { Collection, Context, Reference } from '@sage/xtrem-core';
import { BusinessRuleError, Logger, Node, SystemError, asyncArray, date, datetime, decorators } from '@sage/xtrem-core';
import type * as xtremFinanceData from '@sage/xtrem-finance-data';
import * as xtremMasterData from '@sage/xtrem-master-data';
import { LocalizedError, type InitialNotificationAction } from '@sage/xtrem-shared';
import * as xtremSystem from '@sage/xtrem-system';
import * as xtremSales from '..';
import {
    generateUnbilledAccountReceivableLine,
    getUnbilledAccountReceivableFilter,
} from '../functions/unbilled-account-receivable-lib';

const logger = Logger.getLogger(__filename, 'unbilled-account-receivable-input-set');

@decorators.node<UnbilledAccountReceivableInputSet>({
    isClearedByReset: true,
    storage: 'sql',
    isPublished: true,
    canRead: true,
    canCreate: true,
    canSearch: true,
    canUpdate: true,
    canDelete: true,
    indexes: [
        {
            orderBy: { user: +1 },
            isUnique: true,
            isNaturalKey: true,
        },
    ],
})
export class UnbilledAccountReceivableInputSet extends Node {
    @decorators.referenceProperty<UnbilledAccountReceivableInputSet, 'user'>({
        isStored: true,
        isPublished: true,
        isFrozen: true,
        node: () => xtremSystem.nodes.User,
        async defaultValue() {
            const userInfo = await this.$.context.user;
            if (!userInfo) {
                throw new SystemError('current context does not have a valid user');
            }
            return this.$.context.read(xtremSystem.nodes.User, { email: userInfo.email });
        },
    })
    readonly user: Reference<xtremSystem.nodes.User>;

    @decorators.referenceProperty<UnbilledAccountReceivableInputSet, 'company'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
        provides: ['company'],
        node: () => xtremSystem.nodes.Company,
    })
    readonly company: Reference<xtremSystem.nodes.Company | null>;

    @decorators.referenceArrayProperty<UnbilledAccountReceivableInputSet, 'sites'>({
        isPublished: true,
        isStored: true,
        node: () => xtremSystem.nodes.Site,
        onDelete: 'restrict',
        defaultValue: [],
        isNullable: true,
    })
    readonly sites: Promise<xtremSystem.nodes.Site[] | null>;

    @decorators.referenceProperty<UnbilledAccountReceivableInputSet, 'fromCustomer'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
        ignoreIsActive: true,
        node: () => xtremMasterData.nodes.Customer,
    })
    readonly fromCustomer: Reference<xtremMasterData.nodes.Customer | null>;

    @decorators.referenceProperty<UnbilledAccountReceivableInputSet, 'toCustomer'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
        ignoreIsActive: true,
        node: () => xtremMasterData.nodes.Customer,
    })
    readonly toCustomer: Reference<xtremMasterData.nodes.Customer | null>;

    @decorators.dateProperty<UnbilledAccountReceivableInputSet, 'asOfDate'>({
        isStored: true,
        isPublished: true,
        isRequired: true,
        defaultValue: () => date.today().addMonths(-1).endOfMonth(),
    })
    readonly asOfDate: Promise<date>;

    @decorators.enumProperty<UnbilledAccountReceivableInputSet, 'status'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremSales.enums.unbilledAccountReceivableStatusDataType,
        defaultValue: 'draft',
    })
    readonly status: Promise<xtremSales.enums.UnbilledAccountReceivableStatus>;

    @decorators.datetimeProperty<UnbilledAccountReceivableInputSet, 'executionDate'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
    })
    readonly executionDate: Promise<datetime> | null;

    @decorators.collectionProperty<UnbilledAccountReceivableInputSet, 'lines'>({
        isPublished: true,
        isVital: true,
        reverseReference: 'inputSet',
        node: () => xtremSales.nodes.UnbilledAccountReceivableResultLine,
    })
    readonly lines: Collection<xtremSales.nodes.UnbilledAccountReceivableResultLine>;

    @decorators.asyncMutation<typeof UnbilledAccountReceivableInputSet, 'unbilledAccountReceivableInquiry'>({
        isPublished: true,
        startsReadOnly: true,
        parameters: [{ name: 'userId', type: 'string' }],
        return: 'boolean',
    })
    static async unbilledAccountReceivableInquiry(context: Context, userId: string): Promise<boolean> {
        let message = context.localize(
            '@sage/xtrem-sales/nodes__unbilled_account_receivable-input-set__success_message',
            'Success',
        );

        await context.runInWritableContext(async writableContext => {
            const inputSet: UnbilledAccountReceivableInputSet = await writableContext.read(
                UnbilledAccountReceivableInputSet,
                { user: userId },
                { forUpdate: true },
            );

            // Remove all lines, set status and last execution date
            await inputSet.$.set({ lines: [], status: 'inProgress', executionDate: datetime.now() });
            await inputSet.$.save();
        });

        const result = await context.runInWritableContext(async writableContext => {
            const inputSet = await writableContext.read(
                UnbilledAccountReceivableInputSet,
                { user: userId },
                { forUpdate: true },
            );

            // Do calculation.
            let newStatus: xtremSales.enums.UnbilledAccountReceivableStatus;
            try {
                await UnbilledAccountReceivableInputSet.executeInquiry(writableContext, inputSet);
                if (await inputSet.$.control()) {
                    newStatus = 'completed';
                } else {
                    newStatus = 'error';
                    message = 'error';
                }
            } catch (error) {
                logger.error(() => JSON.stringify(error));
                if (!(error instanceof LocalizedError)) throw error;

                newStatus = 'error';
                message = error.message;
            }

            // Signal completion.
            await inputSet.$.set({ status: newStatus, executionDate: datetime.now() });
            await inputSet.$.save();

            return { sysId: inputSet._id, status: newStatus, message };
        });

        // Notify user.
        await UnbilledAccountReceivableInputSet.sendNotification(context, {
            sysId: result.sysId.toString(),
            success: result.status !== 'error',
            message,
        });

        if (result.status === 'error') {
            throw new BusinessRuleError(result.message);
        }
        return true;
    }

    static async executeInquiry(context: Context, inputSet: UnbilledAccountReceivableInputSet) {
        const siteSysIds = (await inputSet.sites)?.map(site => site._id) || [];

        const searchCriteria: xtremFinanceData.interfaces.FinanceUnbilledAccountReceivableSearch = {
            company: (await inputSet.company) ? await (await inputSet.company)?.id : undefined,
            stockSites: siteSysIds.length ? siteSysIds : undefined,
            fromCustomer: await (await (await inputSet.fromCustomer)?.businessEntity)?.id,
            toCustomer: await (await (await inputSet.toCustomer)?.businessEntity)?.id,
            asOfDate: await inputSet.asOfDate,
        };
        const results: xtremFinanceData.interfaces.FinanceUnbilledAccountReceivable[] =
            await UnbilledAccountReceivableInputSet.unbilledAccountReceivable(context, searchCriteria);

        await asyncArray(results).forEach(async line => {
            await inputSet.lines.append(line);
        });
    }

    static async unbilledAccountReceivable(
        context: Context,
        searchCriteria: xtremFinanceData.interfaces.FinanceUnbilledAccountReceivableSearch,
    ): Promise<xtremFinanceData.interfaces.FinanceUnbilledAccountReceivable[]> {
        const shipmentLines = context.query(xtremSales.nodes.SalesShipmentLine, {
            filter: getUnbilledAccountReceivableFilter(searchCriteria),
        });
        // loop over the array of found sales shipment lines and calculate additional values
        const results = await shipmentLines
            .map(shipmentLine =>
                generateUnbilledAccountReceivableLine({
                    context,
                    shipmentLine,
                    searchCriteria,
                }),
            )
            .toArray();

        // Return only records with an actual unbilled quantity.
        return results.filter(line => line.invoiceIssuableQuantity > 0);
    }

    static async sendNotification(context: Context, param: { sysId: string; success: boolean; message: string }) {
        const actions: InitialNotificationAction[] = [
            {
                link: context.batch.notificationStateLink,
                title: 'History',
                icon: 'link',
                style: 'tertiary',
            },
        ];

        if (param.sysId) {
            actions.push({
                link: `@sage/xtrem-sales/UnbilledAccountReceivableInquiry/${param.sysId}`,
                title: 'Results',
                icon: 'link',
                style: 'tertiary',
            });
        }

        await context.notifyUser({
            title: context.localize(
                '@sage/xtrem-sales/nodes__unbilled_account_receivable-input-set__success_notification_title',
                'Unbilled accounts receivable calculation complete',
            ),
            description: param.message,
            icon: param.success ? 'tick' : 'error',
            level: param.success ? 'success' : 'error',
            shouldDisplayToast: true,
            actions,
        });
    }
}
