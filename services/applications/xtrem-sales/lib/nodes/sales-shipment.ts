import type { NotificationEnvelope } from '@sage/xtrem-communication';
import type { Collection, Context, Dict, Reference, TextStream, decimal, integer } from '@sage/xtrem-core';
import {
    BusinessRuleError,
    Decimal,
    NodeStatus,
    asyncArray,
    date,
    decorators,
    useDefaultValue,
} from '@sage/xtrem-core';
import * as xtremDistribution from '@sage/xtrem-distribution';
import * as xtremFinanceData from '@sage/xtrem-finance-data';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremReporting from '@sage/xtrem-reporting';
import { SuspendException } from '@sage/xtrem-shared';
import * as xtremStockData from '@sage/xtrem-stock-data';
import * as xtremSystem from '@sage/xtrem-system';
import { loggers } from '../functions';
import { isPostedErrorClosed } from '../functions/sales-document-common-lib';
import {
    controlAllLinesAllocated,
    controlNotBlockedOnHold,
    controlReadyToShip,
    controlShippingDate,
    linkedOrderArray,
    updateHeaderNotesOnCreation,
} from '../functions/sales-shipment-lib';
import * as xtremSales from '../index';

@decorators.subNode<SalesShipment>({
    extends: () => xtremMasterData.nodes.BaseDocument,
    isPublished: true,
    canCreate: true,
    canRead: true,
    canUpdate: true,
    canDelete: true,
    hasAttachments: true,

    async controlBegin(cx) {
        if (this.$.status === NodeStatus.added) {
            await xtremSales.events.controlBegin.shipment.checkCustomerOnHoldWhenAdded(cx, this);
        }
        if (this.$.status === NodeStatus.modified) {
            await xtremSales.events.controlBegin.shipment.checkShipmentStatus(cx, this);
        }
        await xtremSales.events.controlBegin.shipment.checkIfStockSiteExists(cx, this);
        await xtremMasterData.events.control.baseDocumentControls.atLeastOneLine(cx, this);
    },

    async saveBegin() {
        await xtremMasterData.functions.controlDocumentNumber(this);

        if (
            this.$.status === NodeStatus.added ||
            (this.$.status === NodeStatus.modified && (await (await this.$.old).status) !== 'shipped')
        ) {
            const postResult =
                await xtremSales.functions.FinanceIntegration.salesShipmentControlAndCreateMutationResult(
                    this.$.context,
                    this,
                );

            if (postResult.message.length) throw new BusinessRuleError(postResult.message);
        }

        await updateHeaderNotesOnCreation(this);
    },

    async saveEnd() {
        await this.updateRelatedSalesDocumentsStatuses(await linkedOrderArray(this));
    },

    async controlDelete(cx): Promise<void> {
        await xtremSales.events.controlDelete.shipment.checkIfShipmentIsSuitableForDeletion(cx, this);
    },

    async deleteEnd(): Promise<void> {
        await this.updateRelatedSalesDocumentsStatuses(await linkedOrderArray(this));
    },
})
@xtremStockData.functions.staticImplements<xtremStockData.interfaces.StaticDocumentHeaderWithStockPosting>()
export class SalesShipment
    extends xtremMasterData.nodes.BaseDocument
    implements
        xtremFinanceData.interfaces.SalesFinanceDocument,
        xtremStockData.interfaces.DocumentHeaderWithStockAllocation
{
    __skipPrepare: boolean;

    __salesOrders: Dict<[xtremSales.interfaces.SalesDocumentLineUpdateInterface]> = {};

    @decorators.stringProperty<SalesShipment, 'reference'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremSales.dataTypes.propertyDataType,
    })
    readonly reference: Promise<string>;

    @decorators.enumPropertyOverride<SalesShipment, 'status'>({
        // dataType: () => xtremSales.enums.salesShipmentStatusDataType,
        defaultValue: 'readyToProcess',
    })
    override readonly status: Promise<xtremSales.enums.SalesShipmentStatus>;

    @decorators.enumPropertyOverride<SalesShipment, 'displayStatus'>({
        // dataType: () => xtremSales.enums.salesShipmentDisplayStatusDataType,
        defaultValue: 'readyToProcess',
        dependsOn: ['invoiceStatus', 'status', 'stockTransactionStatus'],
        async updatedValue() {
            return xtremSales.functions.calculateSalesShipmentDisplayStatus(
                await this.status,
                await this.invoiceStatus,
                await this.stockTransactionStatus,
            );
        },
    })
    override readonly displayStatus: Promise<xtremSales.enums.SalesShipmentDisplayStatus>;

    @decorators.enumProperty<SalesShipment, 'invoiceStatus'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremSales.enums.salesDocumentInvoiceStatusDataType,
        lookupAccess: true,
        defaultValue: 'notInvoiced',
    })
    readonly invoiceStatus: Promise<xtremSales.enums.SalesDocumentInvoiceStatus>;

    @decorators.enumProperty<SalesShipment, 'returnRequestStatus'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        dataType: () => xtremSales.enums.salesDocumentReturnStatusDataType,
        defaultValue: 'noReturnRequested',
    })
    readonly returnRequestStatus: Promise<xtremSales.enums.SalesDocumentReturnStatus>;

    @decorators.enumProperty<SalesShipment, 'returnReceiptStatus'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        dataType: () => xtremSales.enums.salesDocumentReceiptStatusDataType,
        defaultValue: 'notReturned',
    })
    readonly returnReceiptStatus: Promise<xtremSales.enums.SalesDocumentReceiptStatus>;

    @decorators.enumProperty<SalesShipment, 'stockTransactionStatus'>({
        isPublished: true,
        dependsOn: [{ lines: ['stockTransactionStatus'] }],
        dataType: () => xtremStockData.enums.StockDocumentTransactionStatusDataType,
        computeValue() {
            return xtremStockData.functions.stockTransactionLib.computeHeaderStatus(this);
        },
    })
    readonly stockTransactionStatus: Promise<xtremStockData.enums.StockDocumentTransactionStatus>;

    /** deprecated */
    @decorators.referenceProperty<SalesShipment, 'salesSite'>({
        isPublished: true,
        node: () => xtremSystem.nodes.Site,
        getValue() {
            return this.site;
        },
    })
    readonly salesSite: Reference<xtremSystem.nodes.Site>;

    @decorators.referencePropertyOverride<SalesShipment, 'site'>({
        // isRequired: true,
        isFrozen: true,
        filters: { control: { isSales: true } },
    })
    override readonly site: Reference<xtremSystem.nodes.Site>;

    @decorators.referencePropertyOverride<SalesShipment, 'stockSite'>({
        // isRequired: true,
        isFrozen: true,
        filters: { control: { isInventory: true } },
        dependsOn: ['site', 'shipToCustomerAddress'],
        defaultValue() {
            return xtremSales.functions.getStockSiteValue(this);
        },
    })
    override readonly stockSite: Reference<xtremSystem.nodes.Site>;

    /** deprecated */
    @decorators.dateProperty<SalesShipment, 'shippingDate'>({
        isPublished: true,
        dependsOn: ['date'],
        lookupAccess: true,
        getValue() {
            return this.date;
        },
    })
    readonly shippingDate: Promise<date>;

    @decorators.datePropertyOverride<SalesShipment, 'date'>({
        // isRequired: true,
        dependsOn: ['shipToCustomerAddress'],
        async defaultValue() {
            return (await this.getShippingDateDefaultValue()) ?? date.today();
        },
    })
    override readonly date: Promise<date>;

    @decorators.stringProperty<SalesShipment, 'recordUrl'>({
        isPublished: true,
        computeValue() {
            /**
             * Url to the Sales shipment page for the current record,
             * initially used for copilot, for other nodes we are implementing this property try to use
             * same property naming convention (recordUrl)
             */
            return xtremSystem.functions.getRecordUrl(this, '@sage/xtrem-sales/SalesShipment');
        },
    })
    readonly recordUrl: Promise<string>;

    async getShippingDateDefaultValue(): Promise<date | undefined> {
        const deliveryDetail = await (await this.shipToCustomerAddress).deliveryDetail;
        if (deliveryDetail) {
            const myDate = await xtremDistribution.functions.addWorkDaysFromAddress(date.today(), 0, deliveryDetail);
            if (myDate instanceof date) return myDate;
        }
        return undefined;
    }

    // TODO: maybe refactored but is mandatory for stock management
    @decorators.dateProperty<SalesShipment, 'effectiveDate'>({
        isPublished: true,
        getValue() {
            return this.date;
        },
    })
    readonly effectiveDate: Promise<date>;

    @decorators.referenceProperty<SalesShipment, 'shipToCustomer'>({
        isStored: true,
        isPublished: true,
        isRequired: true,
        isFrozen: true,
        node: () => xtremMasterData.nodes.Customer,
        async control(cx, val) {
            await xtremDistribution.events.DistributionDocument.controlSitesLegalCompanies(
                cx,
                this.$.context,
                await this.stockSite,
                await val.businessEntity,
            );
        },
    })
    readonly shipToCustomer: Reference<xtremMasterData.nodes.Customer>;

    @decorators.referenceProperty<SalesShipment, 'shipToCustomerAddress'>({
        isStored: true,
        isPublished: true,
        isRequired: true,
        dependsOn: ['shipToCustomer'],
        node: () => xtremMasterData.nodes.BusinessEntityAddress,
        async defaultValue() {
            return (await this.shipToCustomer)?.primaryShipToAddress;
        },
        filters: {
            control: {
                async businessEntity() {
                    return (await this.shipToCustomer).businessEntity;
                },
                deliveryDetail: { _ne: null },
            },
        },
    })
    readonly shipToCustomerAddress: Reference<xtremMasterData.nodes.BusinessEntityAddress>;

    @decorators.referenceProperty<SalesShipment, 'shipToAddress'>({
        isPublished: true,
        isStored: true,
        isMutable: true,
        isNullable: true,
        dependsOn: ['shipToCustomerAddress'],
        node: () => xtremMasterData.nodes.Address,
        async defaultValue() {
            return (await this.shipToCustomerAddress).address;
        },
        updatedValue: useDefaultValue,
    })
    readonly shipToAddress: Reference<xtremMasterData.nodes.Address | null>;

    @decorators.referenceProperty<SalesShipment, 'shipToContact'>({
        isPublished: true,
        isStored: true,
        isMutable: true,
        isNullable: true,
        dependsOn: ['shipToCustomerAddress'],
        node: () => xtremMasterData.nodes.Contact,
        async defaultValue() {
            return (await (await this.shipToCustomerAddress).primaryContact)?.contact ?? null;
        },
        updatedValue: useDefaultValue,
    })
    readonly shipToContact: Reference<xtremMasterData.nodes.Contact | null>;

    @decorators.referenceProperty<SalesShipment, 'billToCustomer'>({
        isStored: true,
        isPublished: true,
        isRequired: true,
        node: () => xtremMasterData.nodes.Customer,
        dependsOn: ['shipToCustomer'],
        async defaultValue() {
            return (await this.shipToCustomer)?.billToCustomer;
        },
        lookupAccess: true,
    })
    readonly billToCustomer: Reference<xtremMasterData.nodes.Customer>;

    @decorators.referenceProperty<SalesShipment, 'billToLinkedAddress'>({
        isStored: true,
        isPublished: true,
        isRequired: true,
        node: () => xtremMasterData.nodes.BusinessEntityAddress,
        dependsOn: ['shipToCustomer', 'billToCustomer'],
        lookupAccess: true,
        defaultValue() {
            return this.getBillToAddressValue();
        },
    })
    readonly billToLinkedAddress: Reference<xtremMasterData.nodes.BusinessEntityAddress>;

    async getBillToAddressValue(): Promise<xtremMasterData.nodes.BusinessEntityAddress | null> {
        const shipToCustomer = await this.shipToCustomer;
        const billToLinkedAddress = await shipToCustomer.billToAddress;
        if ((await this.billToCustomer) === (await shipToCustomer.billToCustomer) && billToLinkedAddress) {
            return billToLinkedAddress;
        }
        return (await this.billToCustomer).primaryAddress;
    }

    @decorators.referenceProperty<SalesShipment, 'billToAddress'>({
        isPublished: true,
        isStored: true,
        isMutable: true,
        isNullable: true,
        node: () => xtremMasterData.nodes.Address,
        dependsOn: ['billToLinkedAddress'],
        async defaultValue() {
            return xtremSales.functions.getSalesAddressDetail(await this.billToLinkedAddress);
        },
        updatedValue: useDefaultValue,
    })
    readonly billToAddress: Reference<xtremMasterData.nodes.Address | null>;

    @decorators.referenceProperty<SalesShipment, 'billToContact'>({
        isPublished: true,
        isStored: true,
        isMutable: true,
        isNullable: true,
        node: () => xtremMasterData.nodes.Contact,
        dependsOn: ['billToLinkedAddress'],
        async defaultValue() {
            return (await (await this.billToLinkedAddress).primaryContact)?.contact ?? null;
        },
        updatedValue: useDefaultValue,
    })
    readonly billToContact: Reference<xtremMasterData.nodes.Contact | null>;

    @decorators.stringProperty<SalesShipment, 'trackingNumber'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremSales.dataTypes.propertyDataType,
    })
    readonly trackingNumber: Promise<string>;

    @decorators.referenceProperty<SalesShipment, 'incoterm'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
        dependsOn: ['shipToCustomerAddress'],
        node: () => xtremMasterData.nodes.Incoterm,
        async defaultValue() {
            return (await (await this.shipToCustomerAddress).deliveryDetail)?.incoterm || null;
        },
    })
    readonly incoterm: Reference<xtremMasterData.nodes.Incoterm | null>;

    @decorators.referenceProperty<SalesShipment, 'deliveryMode'>({
        isStored: true,
        isPublished: true,
        isRequired: true,
        dependsOn: ['shipToCustomerAddress'],
        node: () => xtremMasterData.nodes.DeliveryMode,
        async defaultValue() {
            return (await (await this.shipToCustomerAddress).deliveryDetail)?.mode || null;
        },
    })
    readonly deliveryMode: Reference<xtremMasterData.nodes.DeliveryMode>;

    @decorators.integerProperty<SalesShipment, 'deliveryLeadTime'>({
        isPublished: true,
        isStored: true,
        dependsOn: ['shipToCustomerAddress'],
        async defaultValue() {
            return (await (await this.shipToCustomerAddress).deliveryDetail)?.leadTime || 0;
        },
    })
    readonly deliveryLeadTime: Promise<integer>;

    // days availability showed as binary but saved as integer
    @decorators.integerProperty<SalesShipment, 'workDays'>({
        isPublished: true,
        excludedFromPayload: true,
        dependsOn: ['shipToCustomerAddress'],
        async computeValue() {
            const deliveryDetail = await (await this.shipToCustomerAddress).deliveryDetail;
            if (deliveryDetail) {
                return xtremDistribution.functions.computeWorkDays(deliveryDetail);
            }
            return 0;
        },
    })
    readonly workDays: Promise<integer>;

    @decorators.dateProperty<SalesShipment, 'deliveryDate'>({
        isStored: true,
        isPublished: true,
        isRequired: true,
        dependsOn: ['date', 'deliveryLeadTime', 'workDays'],
        async defaultValue() {
            return xtremDistribution.functions.addWorkDays(
                await this.date,
                await this.deliveryLeadTime,
                await this.workDays,
            );
        },
    })
    readonly deliveryDate: Promise<date>;

    @decorators.referencePropertyOverride<SalesShipment, 'currency'>({
        // isRequired: true,
        dependsOn: ['billToCustomer'],
        async defaultValue() {
            return (await (await this.billToCustomer)?.businessEntity)?.currency;
        },
    })
    override readonly currency: Reference<xtremMasterData.nodes.Currency>;

    @decorators.decimalProperty<SalesShipment, 'companyFxRate'>({
        isStored: true,
        isPublished: true,
        isRequired: true,
        dataType: () => xtremMasterData.dataTypes.exchangeRate,
        dependsOn: ['currency', 'site', 'fxRateDate'],
        async defaultValue() {
            const rate = await xtremMasterData.functions.getRateOrReverseRate(
                this.$.context,
                await this.currency,
                await (
                    await (
                        await (
                            await this.site
                        ).legalCompany
                    ).currency
                ).id,
                await this.fxRateDate,
            );
            return rate.isRateFound ? rate.rate : 0;
        },
        async control(cx, val) {
            await cx.error
                .withMessage('@sage/xtrem-sales/nodes__sales_shipment__fx_rate_not_found', 'No exchange rate found.')
                .if(val)
                .is.zero();
        },
    })
    readonly companyFxRate: Promise<decimal>;

    @decorators.stringProperty<SalesShipment, 'rateDescription'>({
        isPublished: true,
        dependsOn: ['companyFxRate', 'companyFxRateDivisor'],
        async computeValue() {
            return xtremMasterData.functions.rateDescription(
                await (
                    await this.currency
                ).id,
                await (
                    await (
                        await (
                            await this.site
                        ).legalCompany
                    ).currency
                ).id,
                await this.companyFxRate,
                await this.companyFxRateDivisor,
            );
        },
    })
    readonly rateDescription: Promise<string>;

    @decorators.decimalProperty<SalesShipment, 'companyFxRateDivisor'>({
        isStored: true,
        isPublished: true,
        isRequired: true,
        dataType: () => xtremMasterData.dataTypes.exchangeRate,
        dependsOn: ['currency', 'site', 'fxRateDate'],
        async defaultValue() {
            const rate = await xtremMasterData.functions.getRateOrReverseRate(
                this.$.context,
                await this.currency,
                await (
                    await (
                        await (
                            await this.site
                        ).legalCompany
                    ).currency
                ).id,
                await this.fxRateDate,
            );
            return rate.isRateFound ? rate.divisor : 0;
        },
    })
    readonly companyFxRateDivisor: Promise<decimal>;

    @decorators.dateProperty<SalesShipment, 'fxRateDate'>({
        isStored: true,
        isPublished: true,
        isRequired: true,
        dependsOn: ['date'],
        defaultValue() {
            return this.date;
        },
    })
    readonly fxRateDate: Promise<date>;

    @decorators.referenceProperty<SalesShipment, 'paymentTerm'>({
        isStored: true,
        isPublished: true,
        isRequired: true,
        lookupAccess: true,
        filters: {
            control: {
                businessEntityType: { _ne: 'supplier' },
            },
        },
        dependsOn: ['billToCustomer'],
        node: () => xtremMasterData.nodes.PaymentTerm,
        async defaultValue() {
            return (await this.billToCustomer)?.paymentTerm;
        },
    })
    readonly paymentTerm: Reference<xtremMasterData.nodes.PaymentTerm>;

    @decorators.booleanProperty<SalesShipment, 'isOnHold'>({
        isPublished: true,
        dependsOn: ['billToCustomer'],
        lookupAccess: true,
        async getValue() {
            return (await (await this.billToCustomer).isOnHold) || false;
        },
    })
    readonly isOnHold: Promise<boolean>;

    @decorators.collectionPropertyOverride<SalesShipment, 'lines'>({
        node: () => xtremSales.nodes.SalesShipmentLine,
        dependsOn: ['companyFxRate'],
    })
    override readonly lines: Collection<xtremSales.nodes.SalesShipmentLine>;

    readonly linesWithStockTransactionStatusCollectionName = 'lines';

    readonly allocableLinesCollectionName = 'lines';

    @decorators.enumProperty<SalesShipment, 'allocationStatus'>({
        isPublished: true,
        dataType: () => xtremStockData.enums.stockAllocationStatusDataType,
        dependsOn: [{ lines: ['allocationStatus'] }],
        computeValue() {
            return xtremStockData.functions.allocationLib.computeHeaderAllocationStatus(this);
        },
    })
    readonly allocationStatus: Promise<xtremStockData.enums.StockAllocationStatus>;

    @decorators.decimalProperty<SalesShipment, 'totalAmountExcludingTax'>({
        isStoredOutput: true,
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.priceInSalesPrice,
        dependsOn: [{ lines: ['amountExcludingTax'] }],
        defaultValue() {
            return this.lines.sum(line => line.amountExcludingTax);
        },
        updatedValue: useDefaultValue,
        lookupAccess: true,
    })
    readonly totalAmountExcludingTax: Promise<decimal>;

    @decorators.decimalProperty<SalesShipment, 'totalAmountExcludingTaxInCompanyCurrency'>({
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.priceInSalesPrice, // TODO: Has to have a computed length depending of the currency decimals !!!
        dependsOn: [{ lines: ['amountExcludingTaxInCompanyCurrency'] }], // ,'status', 'receiptStatus'],
        async getValue() {
            return Decimal.roundAt(
                await this.lines.sum(line => line.amountExcludingTaxInCompanyCurrency),
                await (
                    await this.currency
                ).decimalDigits,
            );
        },
    })
    readonly totalAmountExcludingTaxInCompanyCurrency: Promise<decimal>;

    @decorators.decimalProperty<SalesShipment, 'totalGrossProfit'>({
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.priceInSalesPrice,
        dependsOn: [{ lines: ['grossProfitAmount'] }],
        getValue() {
            return this.lines.sum(line => line.grossProfitAmount);
        },
    })
    readonly totalGrossProfit: Promise<decimal>;

    @decorators.textStreamPropertyOverride<SalesShipment, 'internalNote'>({
        async isFrozen() {
            return isPostedErrorClosed(await this.status);
        },
    })
    override readonly internalNote: Promise<TextStream>;

    @decorators.textStreamPropertyOverride<SalesShipment, 'externalNote'>({
        async control(cx) {
            await xtremMasterData.events.control.externalNoteControl.externalNoteEmpty(
                await this.isExternalNote,
                await this.externalNote,
                cx,
            );
        },
        async isFrozen() {
            return isPostedErrorClosed(await this.status);
        },
    })
    override readonly externalNote: Promise<TextStream>;

    /** to be sure the user know that externalNode will go to external documents */
    @decorators.booleanPropertyOverride<SalesShipment, 'isExternalNote'>({
        async isFrozen() {
            return isPostedErrorClosed(await this.status);
        },
    })
    override readonly isExternalNote: Promise<boolean>;

    @decorators.booleanPropertyOverride<SalesShipment, 'isTransferHeaderNote'>({
        async isFrozen() {
            return isPostedErrorClosed(await this.status);
        },
    })
    override readonly isTransferHeaderNote: Promise<boolean>;

    @decorators.booleanPropertyOverride<SalesShipment, 'isTransferLineNote'>({
        async isFrozen() {
            return isPostedErrorClosed(await this.status);
        },
    })
    override readonly isTransferLineNote: Promise<boolean>;

    @decorators.collectionProperty<SalesShipment, 'postingDetails'>({
        isPublished: true,
        join: {
            documentSysId() {
                return this._id;
            },
            documentType() {
                return 'salesShipment';
            },
        },
        node: () => xtremFinanceData.nodes.FinanceTransaction,
        lookupAccess: true,
    })
    readonly postingDetails: Collection<xtremFinanceData.nodes.FinanceTransaction>;

    static async areAllShipmentLinesAllocated(salesShipment: SalesShipment): Promise<boolean> {
        return !(await salesShipment.lines
            .filter(
                async filteredLines =>
                    (await filteredLines.stockTransactionStatus) !== 'completed' &&
                    (await filteredLines.allocationStatus) !== 'notManaged',
            )
            .some(async line => (await line.quantityInStockUnit) - (await line.quantityAllocated) !== 0));
    }

    @decorators.mutation<typeof SalesShipment, 'confirm'>({
        isPublished: true,
        parameters: [
            {
                name: 'salesShipment',
                type: 'reference',
                node: () => xtremSales.nodes.SalesShipment,
                isWritable: true,
                isMandatory: true,
            },
            {
                name: 'isSafeToRetry',
                type: 'boolean',
                isMandatory: false,
            },
        ],
        return: {
            type: 'reference',
            node: () => xtremSales.nodes.SalesShipment,
        },
    })
    static async confirm(
        context: Context,
        salesShipment: SalesShipment,
        isSafeToRetry = false,
    ): Reference<SalesShipment> {
        if ((await salesShipment.status) !== 'readyToProcess') {
            const errorMessage = context.localize(
                '@sage/xtrem-sales/nodes__sales_shipment__shipment_confirmed_ready_to_process',
                "A shipment can only be confirmed if the status is 'Ready to process'.",
            );

            if (isSafeToRetry) {
                loggers.shipment.warn(errorMessage);
                return salesShipment;
            }

            throw new BusinessRuleError(errorMessage);
        }

        const allLinesAllocated = await SalesShipment.areAllShipmentLinesAllocated(salesShipment);

        if (!allLinesAllocated) {
            const errorMessage = context.localize(
                '@sage/xtrem-sales/nodes__sales_shipment__shipment__cannot_confirm_allocate_all_lines',
                'You need to allocate all lines before confirming.',
            );

            if (isSafeToRetry) {
                loggers.shipment.warn(errorMessage);
                return salesShipment;
            }

            throw new BusinessRuleError(errorMessage);
        }

        await salesShipment.$.set({ status: 'readyToShip' });

        await salesShipment.lines.forEach(async (line: xtremSales.nodes.SalesShipmentLine) => {
            await line.$.set({ status: 'readyToShip' });
        });

        await salesShipment.$.save();

        return salesShipment;
    }

    @decorators.mutation<typeof SalesShipment, 'revoke'>({
        isPublished: true,
        parameters: [
            {
                name: '_id',
                type: 'string',
            },
        ],
        return: 'string',
    })
    static async revoke(context: Context, _id: string): Promise<string> {
        const salesShipment = await context.tryRead(xtremSales.nodes.SalesShipment, { _id }, { forUpdate: true });
        if (!salesShipment) {
            return '';
        }
        if ((await salesShipment.status) !== 'readyToShip') {
            throw new BusinessRuleError(
                context.localize(
                    '@sage/xtrem-sales/nodes__sales_shipment__shipment_revoked_ready_to_ship',
                    "A shipment can only be reverted if the status is 'Ready to ship'.",
                ),
            );
        }

        await salesShipment.$.set({ status: 'readyToProcess' });
        await salesShipment.lines.forEach((line: xtremSales.nodes.SalesShipmentLine) =>
            line.$.set({ status: 'readyToProcess' }),
        );
        await salesShipment.$.save();

        return context.localize(
            '@sage/xtrem-sales/nodes__sales_shipment__sales_shipments_revoked_success',
            "The sales shipment status has been reverted and set to 'Ready to process'.",
        );
    }

    /**
     * Method that calculate addWorkDays -> because not work properly on front-end
     * @param context
     * @param shippingDate
     * @param deliveryLeadTime
     * @param workDays
     * @returns date addWorkDaysMethodReturn
     */
    @decorators.mutation<typeof SalesShipment, 'addWorkDays'>({
        isPublished: true,
        parameters: [
            { name: 'shippingDate', type: 'date', isMandatory: true },
            { name: 'deliveryLeadTime', type: 'integer', isMandatory: true },
            { name: 'workDays', type: 'integer', isMandatory: true },
        ],
        return: {
            type: 'date',
            isMandatory: true,
        },
    })
    static addWorkDays(_context: Context, shippingDate: date, deliveryLeadTime: integer, workDays: integer): date {
        return xtremDistribution.functions.addWorkDays(shippingDate, deliveryLeadTime, workDays);
    }

    private async updateRelatedSalesDocumentsStatuses(ordersArray: xtremSales.nodes.SalesOrder[]): Promise<void> {
        await xtremSales.classes.BaseSalesDocumentsCreator.updateRelatedSalesDocumentsStatuses({
            salesDocumentsArray: ordersArray,
            tempDict: '__salesOrders',
            statusType: 'shippingStatus',
            classInstance: this,
            aggregateNode: xtremSales.nodes.SalesOrderLineToSalesShipmentLine,
            enumDataType: xtremSales.enums.salesDocumentShippingStatusDataType,
        });
    }

    /**
     * This method generates invoices for a single sales shipment document having lines with a remaining quantity to ship
     * (sales shipment line quantity - sum(already invoiced quantity) in linked node).
     * The sales shipment lines cannot be 'Invoiced'
     * @param context
     * @param salesShipment
     * @param invoiceDate - The date to use for the invoice
     * @param isSafeToRetry - When true, enables retry mechanism for transient failures
     */
    @decorators.mutation<typeof SalesShipment, 'createSalesInvoicesFromShipment'>({
        startsReadOnly: true,
        isPublished: true,
        parameters: [
            {
                name: 'salesShipment',
                type: 'reference',
                node: () => xtremSales.nodes.SalesShipment,
                isMandatory: true,
            },
            { name: 'isSafeToRetry', type: 'boolean' },
            { name: 'invoiceDate', type: 'date' },
        ],
        return: {
            type: 'array',
            item: { type: 'reference', node: () => xtremSales.nodes.SalesInvoice },
        },
    })
    static async createSalesInvoicesFromShipment(
        context: Context,
        salesShipment: xtremSales.nodes.SalesShipment,
        isSafeToRetry = false,
        invoiceDate: date = date.today(),
    ): Promise<xtremSales.nodes.SalesInvoice[]> {
        await xtremSales.functions.validateShipmentLinesForInvoicing(context, salesShipment);

        const salesInvoicesCreator = this.instantiateSalesInvoicesCreator(context, invoiceDate);
        const lines = await salesShipment.lines.toArray();
        const preparedData = lines.map(line => ({ salesDocumentLine: line }));

        await salesInvoicesCreator.prepareNodeCreateData(preparedData);
        const { errorMessage, documentsCreated } = await salesInvoicesCreator.createSalesOutputDocuments();

        if (!errorMessage?.[0]?.message) {
            if (isSafeToRetry && documentsCreated.length === 0) {
                throw new BusinessRuleError(
                    context.localize(
                        '@sage/xtrem-sales/nodes__sales_shipment__no_invoices_were_created',
                        'No invoices were created for the sales shipment.',
                    ),
                );
            }
            return documentsCreated as xtremSales.nodes.SalesInvoice[];
        }
        throw new BusinessRuleError(errorMessage[0].message);
    }

    /**
     * This method is called after creating the invoices using SalesInvoicesCreator.createSalesOutputDocuments.
     * We loop over the gathered _id's found in the result interface in the 'documentsCreated' array property.
     * For each _id we read the invoice from the database and call the finance checks which returns another
     * interface with 2 properties, a boolean one 'wasSuccessful' and a 'message'.
     * As there are many invoices created, each of them could result in a warning. We create a big string with all
     * of the messages but we avoid adding the same message more than once.
     * If at least one check wasn't successful we return the result.financeMessages string with all the warnings.
     * @param context
     * @param result result coming from SalesInvoicesCreator.createSalesOutputDocuments
     */
    static async doFinanceChecksForMassInvoiceCreation(
        context: Context,
        result: xtremSales.interfaces.CreateSalesOutputDocumentsReturnValuesWithFinanceMessages,
    ): Promise<xtremSales.interfaces.CreateSalesOutputDocumentsReturnValuesWithFinanceMessages> {
        let financeWarnings = '';
        if (result.documentsCreated?.length) {
            await asyncArray(result.documentsCreated).forEach(async document => {
                const invoice = await context.read(xtremSales.nodes.SalesInvoice, {
                    _id: document._id,
                });
                const checkResult =
                    await xtremSales.functions.FinanceIntegration.salesInvoiceControlAndCreateMutationResult(
                        context,
                        invoice,
                    );
                if (checkResult.message.length && !financeWarnings.includes(checkResult.message))
                    financeWarnings = `${financeWarnings}${checkResult.message}\n`;
            });
        }
        result.financeMessages = financeWarnings;
        return result;
    }

    /**
     * This method generates invoices for a set of sales shipments having lines with a remaining quantity to ship
     * (sales shipment line quantity - sum(already invoiced quantity) in linked node).
     * The sales shipment lines cannot be 'Invoiced'
     * @param context
     * @param salesDocumentLines
     */
    @decorators.mutation<typeof SalesShipment, 'createSalesInvoicesFromShipmentLines'>({
        isPublished: true,
        parameters: [
            {
                name: 'salesDocumentLines',
                type: 'array',
                item: {
                    type: 'object',
                    properties: {
                        salesDocumentLine: {
                            type: 'reference',
                            node: () => xtremSales.nodes.SalesShipmentLine,
                        },
                        quantityToProcess: {
                            type: 'decimal',
                            isMandatory: false,
                        },
                    },
                },
            },
            {
                name: 'invoiceDate',
                type: 'date',
                isMandatory: false,
            },
        ],
        return: {
            type: 'object',
            properties: {
                status: 'string',
                numberOfInvoices: 'integer',
                documentsCreated: {
                    type: 'array',
                    item: { type: 'reference', node: () => xtremSales.nodes.SalesInvoice },
                },
                lineErrors: {
                    type: 'array',
                    item: {
                        type: 'object',
                        properties: {
                            lineNumber: 'integer',
                            linePosition: 'integer',
                            message: 'string',
                        },
                    },
                },
                errorMessage: {
                    type: 'array',
                    item: {
                        type: 'object',
                        properties: {
                            loggerMessage: 'string',
                            message: 'string',
                        },
                    },
                },
                financeMessages: 'string',
            },
        },
        startsReadOnly: true,
    })
    static async createSalesInvoicesFromShipmentLines(
        context: Context,
        salesDocumentLines: { salesDocumentLine: xtremSales.nodes.SalesShipmentLine; quantityToProcess?: decimal }[],
        invoiceDate: date = date.today(),
    ): Promise<xtremSales.interfaces.CreateSalesOutputDocumentsReturnValuesWithFinanceMessages> {
        if (!salesDocumentLines.length) {
            return {
                status: 'parametersAreIncorrect',
                numberOfInvoices: 0,
                lineErrors: [],
                errorMessage: [],
                documentsCreated: [],
            };
        }
        const result = (await (
            await this.instantiateSalesInvoicesCreator(context, invoiceDate).prepareNodeCreateData(salesDocumentLines)
        ).createSalesOutputDocuments()) as xtremSales.interfaces.CreateSalesOutputDocumentsReturnValuesWithFinanceMessages;

        // do finance checks on all the created invoices
        return this.doFinanceChecksForMassInvoiceCreation(context, result);
    }

    static instantiateSalesInvoicesCreator(
        context: Context,
        invoiceDate: date = date.today(),
    ): xtremSales.classes.SalesInvoicesCreator {
        return new xtremSales.classes.SalesInvoicesCreator(context, invoiceDate);
    }

    /**
     * This method generates invoices for a set of sales shipments during mass creation process
     * The sales shipment cannot be 'Invoiced'
     * @param context
     * @param site site _id
     * @param fromBillToCustomer customer _id
     * @param toBillToCustomer customer _id
     * @param fromShipment sales shipment _id
     * @param toShipment sales shipment _id
     * @param invoiceDate
     * @param shipmentUntilDate
     */
    @decorators.mutation<typeof SalesShipment, 'massCreateSalesInvoices'>({
        isPublished: true,
        parameters: [
            { name: 'site', type: 'string', isMandatory: true },
            { name: 'fromBillToCustomer', type: 'string', isMandatory: false },
            { name: 'toBillToCustomer', type: 'string', isMandatory: false },
            { name: 'fromShipment', type: 'string', isMandatory: false },
            { name: 'toShipment', type: 'string', isMandatory: false },
            { name: 'shipmentUntilDate', type: 'date', isMandatory: false, isNullable: true },
            { name: 'invoiceDate', type: 'date', isMandatory: false },
        ],
        return: {
            type: 'object',
            properties: {
                numberOfInvoices: 'integer',
                financeMessages: 'string',
                errorMessage: {
                    type: 'array',
                    item: { type: 'object', properties: { loggerMessage: 'string', message: 'string' } },
                },
            },
        },
        startsReadOnly: true,
    })
    static async massCreateSalesInvoices(
        context: Context,
        site: string,
        fromBillToCustomer?: string,
        toBillToCustomer?: string,
        fromShipment?: string,
        toShipment?: string,
        shipmentUntilDate?: date,
        invoiceDate: date = date.today(),
    ): Promise<{
        numberOfInvoices?: number;
        financeMessages?: string;
        errorMessage?: xtremSales.interfaces.OutputDocumentCreationError[];
    }> {
        const salesShipments = context.query(xtremSales.nodes.SalesShipment, {
            filter: xtremSales.sharedFunctions.shipmentInvoicingFilterCriteria(
                site,
                shipmentUntilDate ? String(shipmentUntilDate) : null,
                fromBillToCustomer,
                toBillToCustomer,
                fromShipment,
                toShipment,
                false,
            ),
        });

        if (await salesShipments.length) {
            const salesInvoicesCreator = this.instantiateSalesInvoicesCreator(context, invoiceDate);

            await salesShipments.forEach((salesShipment: xtremSales.nodes.SalesShipment) =>
                salesShipment.lines.forEach(
                    async (salesShipmentLine: xtremSales.nodes.SalesShipmentLine) =>
                        // eslint-disable-next-line no-void
                        void (await salesInvoicesCreator.prepareNodeCreateData([
                            {
                                salesDocumentLine: salesShipmentLine,
                                quantityToProcess: await salesShipmentLine.quantity,
                            },
                        ])),
                ),
            );

            let result =
                (await salesInvoicesCreator.createSalesOutputDocuments()) as xtremSales.interfaces.CreateSalesOutputDocumentsReturnValuesWithFinanceMessages;

            // do finance checks on all the created invoices
            result = await this.doFinanceChecksForMassInvoiceCreation(context, result);
            return {
                numberOfInvoices: result.numberOfInvoices,
                financeMessages: result.financeMessages,
                errorMessage: result.errorMessage,
            };
        }
        return { numberOfInvoices: 0 };
    }

    /**
     * This mutation generates return request for a set of sales shipment lines not yet returned
     * @param context
     * @param salesShipmentLines
     */
    @decorators.mutation<typeof SalesShipment, 'createSalesReturnRequestFromShipmentLines'>({
        isPublished: true,
        parameters: [
            {
                name: 'salesDocumentLines',
                type: 'array',
                item: {
                    type: 'object',
                    properties: {
                        salesDocumentLine: {
                            type: 'reference',
                            node: () => xtremSales.nodes.SalesShipmentLine,
                        },
                        quantityToProcess: {
                            type: 'decimal',
                            isMandatory: false,
                        },
                    },
                },
            },
        ],
        return: {
            type: 'object',
            properties: {
                status: 'string',
                numberOfReturnRequests: 'integer',
                documentsCreated: {
                    type: 'array',
                    item: { type: 'reference', node: () => xtremSales.nodes.SalesReturnRequest },
                },
                lineErrors: {
                    type: 'array',
                    item: {
                        type: 'object',
                        properties: {
                            lineNumber: 'integer',
                            linePosition: 'integer',
                            message: 'string',
                        },
                    },
                },
            },
        },
        startsReadOnly: true,
    })
    static async createSalesReturnRequestFromShipmentLines(
        context: Context,
        salesDocumentLines: [{ salesDocumentLine: xtremSales.nodes.SalesShipmentLine; quantityToProcess?: decimal }],
    ): Promise<xtremSales.interfaces.CreateSalesOutputDocumentsReturnValues> {
        if (!salesDocumentLines.length) {
            return xtremSales.classes.SalesReturnRequestCreator.parametersAreIncorrect;
        }

        return (
            await this.instantiateSalesReturnRequestCreator(context).prepareNodeCreateData(salesDocumentLines)
        ).createSalesOutputDocuments();
    }

    /**
     * This mutation generates return request for a set of sales shipment lines not yet returned
     * @param context
     * @param salesDocuments
     */
    @decorators.mutation<typeof SalesShipment, 'createSalesReturnRequestFromShipments'>({
        isPublished: true,
        parameters: [
            {
                name: 'salesDocuments',
                type: 'array',
                item: {
                    type: 'reference',
                    node: () => xtremSales.nodes.SalesShipment,
                },
            },
        ],
        return: {
            type: 'object',
            properties: {
                status: 'string',
                numberOfReturnRequests: 'integer',
                documentsCreated: {
                    type: 'array',
                    item: { type: 'reference', node: () => xtremSales.nodes.SalesReturnRequest },
                },
                lineErrors: {
                    type: 'array',
                    item: {
                        type: 'object',
                        properties: {
                            lineNumber: 'integer',
                            linePosition: 'integer',
                            message: 'string',
                        },
                    },
                },
            },
        },
        startsReadOnly: true,
    })
    static async createSalesReturnRequestFromShipments(
        context: Context,
        salesDocuments: xtremSales.nodes.SalesShipment[],
    ): Promise<xtremSales.interfaces.CreateSalesOutputDocumentsReturnValues> {
        if (!salesDocuments.length) {
            return xtremSales.classes.SalesReturnRequestCreator.parametersAreIncorrect;
        }

        const salesReturnRequestCreator = this.instantiateSalesReturnRequestCreator(context);
        await asyncArray(salesDocuments).forEach(salesShipment =>
            salesShipment.lines.forEach(async line => {
                if ((await line.returnRequestStatus) !== 'returnRequested') {
                    await salesReturnRequestCreator.prepareNodeCreateData([{ salesDocumentLine: line }]);
                }
            }),
        );
        if (Object.keys(salesReturnRequestCreator.salesOutputDocuments).length !== 0) {
            return salesReturnRequestCreator.createSalesOutputDocuments();
        }
        return xtremSales.classes.SalesReturnRequestCreator.parametersAreIncorrect;
    }

    static instantiateSalesReturnRequestCreator(context: Context): xtremSales.classes.SalesReturnRequestCreator {
        return new xtremSales.classes.SalesReturnRequestCreator(context);
    }

    @decorators.mutation<typeof SalesShipment, 'postToStock'>({
        isPublished: true,
        startsReadOnly: true,
        parameters: [
            {
                name: 'salesShipment',
                type: 'reference',
                node: () => SalesShipment,
                isMandatory: true,
            },
            { name: 'isSafeToRetry', type: 'boolean' },
        ],
        return: { type: 'string' },
    })
    static async postToStock(context: Context, salesShipment: SalesShipment, isSafeToRetry = false): Promise<string> {
        const { notManagedDocumentLines, documentsToBePosted } = await SalesShipment.getDocumentsToPost(
            salesShipment,
            isSafeToRetry,
        );

        if (notManagedDocumentLines.length > 0) {
            await SalesShipment.onStockReply(context, {
                requestNotificationId: 'none',
                movementHasBeenSkipped: true,
                updateResults: { issue: { documents: notManagedDocumentLines } },
            });
        }

        if (documentsToBePosted.length > 0) {
            return context.runInWritableContext(async (childContext: Context) => {
                await childContext.bulkUpdate(xtremMasterData.nodes.BaseDocument, {
                    where: { _id: { _in: documentsToBePosted } },
                    set: { displayStatus: 'postingInProgress' },
                });
                return xtremStockData.functions.notificationLib.stockIssueRequestNotification(childContext, {
                    documentClass: SalesShipment,
                    documentIds: documentsToBePosted,
                    stockUpdateParameters: {
                        stockDetailData: {
                            isDocumentWithoutStockDetails: true,
                        },
                        allocationData: {
                            isUsingAllocations: true,
                        },
                    },
                    batchTrackingId: context.batch.trackingId,
                });
            });
        }
        return '{}';
    }

    // This asyncMutation is used only for workflow to post a sales shipment
    @decorators.asyncMutation<typeof SalesShipment, 'post'>({
        startsReadOnly: true,
        isPublished: true,
        parameters: [
            {
                name: 'salesShipment',
                type: 'reference',
                node: () => SalesShipment,
                isMandatory: true,
            },
            { name: 'isSafeToRetry', type: 'boolean' },
        ],
        return: { type: 'reference', node: () => xtremSales.nodes.SalesShipment },
    })
    static async post(
        context: Context,
        salesShipment: xtremSales.nodes.SalesShipment,
        isSafeToRetry = false,
    ): Promise<xtremSales.nodes.SalesShipment> {
        const result = await this.postToStock(context, salesShipment, isSafeToRetry);
        // result is a string that is returned from postToStock of xtremStockData.interfaces.PostToStockReturnJsonType<xtremStockData.enums.StockMovementTypeEnum>.
        // In case of requested' we throw the special SuspendException so that the framework does not update
        // the SysNotificationState of the async mutation. It will be set to completed or error on onStockReply calls
        const parsedResult = JSON.parse(
            result,
        ) as xtremStockData.interfaces.PostToStockReturnJsonType<xtremStockData.enums.StockMovementTypeEnum>;
        if (parsedResult.result === 'requested') throw new SuspendException();
        return salesShipment;
    }

    static async controlDocumentPosting(
        salesShipment: xtremSales.nodes.SalesShipment,
        isSafeToRetry: boolean,
    ): Promise<boolean> {
        return (
            (await controlReadyToShip(salesShipment, isSafeToRetry)) &&
            (await controlNotBlockedOnHold(salesShipment, isSafeToRetry)) &&
            (await controlShippingDate(salesShipment, isSafeToRetry)) &&
            controlAllLinesAllocated(salesShipment, isSafeToRetry)
        );
    }

    private static async getDocumentsToPost(
        salesShipment: SalesShipment,
        isSafeToRetry = false,
    ): Promise<{
        notManagedDocumentLines: xtremStockData.interfaces.StockUpdateResultCommonType['documents'];
        documentsToBePosted: integer[];
    }> {
        const result: {
            notManagedDocumentLines: xtremStockData.interfaces.StockUpdateResultCommonType['documents'];
            documentsToBePosted: integer[];
        } = { notManagedDocumentLines: [], documentsToBePosted: [] };

        const isShipmentValid = await SalesShipment.controlDocumentPosting(salesShipment, isSafeToRetry);
        if (!isShipmentValid) {
            return result;
        }

        const { withStockUpdateLines, withoutStockUpdateLines: notManagedLines } =
            await SalesShipment.getDocumentLinesToPost(salesShipment);

        if (withStockUpdateLines.length > 0) {
            result.documentsToBePosted.push(salesShipment._id);
        }

        result.notManagedDocumentLines.push({
            id: salesShipment._id,
            lines: await asyncArray(notManagedLines)
                .map(async line => ({
                    id: line._id,
                    sortValue: await line._sortValue,
                    stockUpdateResultStatus: 'none' as xtremStockData.enums.StockUpdateResultAction,
                    originLineIds: [],
                    stockJournalRecords: [],
                }))
                .toArray(),
        });
        return result;
    }

    private static getDocumentLinesToPost(document: SalesShipment): Promise<{
        withStockUpdateLines: Array<xtremSales.nodes.SalesShipmentLine>;
        withoutStockUpdateLines: Array<xtremSales.nodes.SalesShipmentLine>;
    }> {
        return document.lines.reduce(
            async (res, line) => {
                const allocationStatus = await line.allocationStatus;
                if ((await line.stockTransactionStatus) === 'completed') {
                    return res;
                }

                if (allocationStatus === 'notManaged') {
                    res.withoutStockUpdateLines.push(line);
                } else {
                    res.withStockUpdateLines.push(line);
                }

                return res;
            },
            {
                withStockUpdateLines: [] as Array<xtremSales.nodes.SalesShipmentLine>,
                withoutStockUpdateLines: [] as Array<xtremSales.nodes.SalesShipmentLine>,
            },
        );
    }

    @decorators.notificationListener<typeof SalesShipment>({
        startsReadOnly: true,
        topic: 'SalesShipment/stock/issue/reply',
        onError(
            context: Context,
            envelope: NotificationEnvelope<
                xtremStockData.interfaces.StockReplyPayload<xtremStockData.enums.StockMovementTypeEnum.issue>
            >,
            error: Error,
        ) {
            return xtremStockData.functions.notificationLib.errorHandler(
                context,
                envelope,
                error,
                xtremSales.nodes.SalesShipment,
            );
        },
    })
    static async onStockReply(
        readOnlyContext: Context,
        payload: xtremStockData.interfaces.StockReplyPayload<xtremStockData.enums.StockMovementTypeEnum.issue>,
    ): Promise<void> {
        const issueUpdateResult = await readOnlyContext.runInWritableContext(
            async writableContext =>
                (
                    await xtremStockData.functions.notificationLib.reactToStockMovementReply(
                        writableContext,
                        payload,
                        SalesShipment,
                        {
                            onLineSucceeded: xtremSales.nodes.SalesShipment.updateLineOnSuccess,
                            beforeDocumentSave: xtremSales.nodes.SalesShipment.updateHeaderAfterStockUpdate,
                        },
                    )
                ).issue,
        );
        if (!issueUpdateResult) {
            return;
        }

        if (!issueUpdateResult.transactionHadAnError) {
            await issueUpdateResult.documents.forEach(async document => {
                if (document.isStockDocumentCompleted) {
                    await readOnlyContext.runInWritableContext(async writableContext => {
                        const salesShipment = await writableContext.read(xtremSales.nodes.SalesShipment, {
                            _id: document.id,
                        });
                        await xtremSales.nodes.SalesShipment.onceStockCompleted(writableContext, salesShipment);
                    });
                }
            });
        }
    }

    static async updateHeaderAfterStockUpdate(
        document: xtremStockData.interfaces.DocumentHeaderWithStockPosting,
    ): Promise<void> {
        const shipment = document as xtremSales.nodes.SalesShipment;

        if ((await shipment.stockTransactionStatus) === 'completed') {
            if (await shipment.lines.every(async line => (await line.status) === 'shipped')) {
                await shipment.$.set({ status: 'shipped' });
            }
        }

        const existingDisplayStatus = await shipment.displayStatus;
        const displayStatus = xtremSales.functions.calculateSalesShipmentDisplayStatus(
            await shipment.status,
            await shipment.invoiceStatus,
            await shipment.stockTransactionStatus,
        );
        if (existingDisplayStatus !== displayStatus) {
            await shipment.$.set({ displayStatus });
        }
    }

    static async updateLineOnSuccess(_context: Context, line: xtremSales.nodes.SalesShipmentLine): Promise<void> {
        await line.$.set({
            status: 'shipped',
        });
    }

    static async onceStockCompleted(context: Context, shipment: SalesShipment): Promise<void> {
        // Do whatever needs to be done after stock engine successfully processed all the lines
        // Send notification in order to create staging table entries for the accounting engine
        if (await (await (await shipment.stockSite).legalCompany).doStockPosting) {
            await xtremFinanceData.functions.salesShipmentNotification(
                context,
                shipment as xtremFinanceData.interfaces.SalesFinanceDocument,
                (await shipment.lines.toArray()) as xtremFinanceData.interfaces.FinanceOriginDocumentLineWithStock[],
            );
        }
    }

    @decorators.notificationListener<typeof SalesShipment>({
        topic: 'SalesShipment/accountingInterface',
    })
    static async onFinanceIntegrationReply(
        context: Context,
        payload: xtremFinanceData.interfaces.FinanceTransactionData,
    ): Promise<void> {
        await xtremFinanceData.functions.reactToFinanceIntegrationReply(context, payload);
    }

    // Repost creates an update notification to the acc engine ir order to update att and dimenions.
    // In fact, the existing finance document headers are preserved while the lines are all rebuilt (old ones deleted and new ones calculated)
    @decorators.mutation<typeof SalesShipment, 'repost'>({
        isPublished: true,
        parameters: [
            {
                name: 'salesShipment',
                type: 'reference',
                isMandatory: true,
                isWritable: true,
                node: () => SalesShipment,
            },
            {
                name: 'documentLines',
                type: 'array',
                item: {
                    type: 'object',
                    properties: {
                        baseDocumentLineSysId: 'integer',
                        storedAttributes: 'json',
                        storedDimensions: 'json',
                    },
                },
            },
        ],
        return: {
            type: 'object',
            properties: {
                wasSuccessful: 'boolean',
                message: 'string',
            },
        },
    })
    static async repost(
        context: Context,
        salesShipment: SalesShipment,
        documentLines: xtremFinanceData.interfaces.FinanceIntegrationDocumentLineUpdate[],
    ): Promise<xtremFinanceData.interfaces.MutationResult> {
        if (!xtremFinanceData.functions.canRepost(await salesShipment.financeIntegrationStatus)) {
            throw new BusinessRuleError(
                context.localize(
                    '@sage/xtrem-sales/nodes__sales-shipment__cant_repost_sales_shipment_when_status_is_not_failed',
                    "You can only repost a sales shipment if the status is 'Failed' or 'Not recorded.'",
                ),
            );
        }

        const updateLines = documentLines.map(documentLine => {
            return {
                _id: documentLine.baseDocumentLineSysId,
                storedAttributes: documentLine.storedAttributes,
                storedDimensions: documentLine.storedDimensions,
            };
        });
        await salesShipment.$.set({
            forceUpdateForFinance: true,
            lines: updateLines,
        });
        await salesShipment.$.save();

        // send notification in order to update a staging table entry for the accounting engine
        await xtremFinanceData.functions.financeDocumentUpdateNotification(context, {
            document: salesShipment,
            lines: await salesShipment.lines.toArray(),
            documentType: 'salesShipment',
            replyTopic: 'SalesShipmentUpdate/accountingInterface',
            doNotPostOnUpdate: false,
        });
        return {
            wasSuccessful: true,
            message: context.localize(
                '@sage/xtrem-sales/nodes__sales-shipment__document_was_posted',
                'The sales shipment has been posted.',
            ),
        };
    }

    @decorators.bulkMutation<typeof SalesShipment, 'printBulkPackingSlip'>({
        isPublished: true,
        startsReadOnly: true,
        queue: 'reporting',
        async onComplete(context, reports) {
            const reportName = context.localize(
                '@sage/xtrem-sales/node__sales_shipment_bulk_print_report_name',
                'Sales shipment packing slip',
            );
            await xtremMasterData.functions.bulkPrintOnCompleteHandler(context, {
                documentType: 'packingSlip',
                documents: reports,
                reportName,
            });
        },
    })
    static printBulkPackingSlip(context: Context, document: SalesShipment) {
        return xtremReporting.nodes.Report.generateReportPdf(context, 'packingSlip', '', {
            variables: JSON.stringify({
                shipment: document._id,
            }),
            isBulk: true,
        });
    }

    @decorators.bulkMutation<typeof SalesShipment, 'printBulkPickList'>({
        isPublished: true,
        startsReadOnly: true,
        queue: 'reporting',
        async onComplete(context, reports) {
            const reportName = context.localize(
                '@sage/xtrem-sales/node__sales_shipment_bulk_print_pick_list_report_name',
                'Sales shipment pick list',
            );
            await xtremMasterData.functions.bulkPrintOnCompleteHandler(context, {
                documentType: 'salesShipmentPickList',
                documents: reports,
                reportName,
            });
        },
    })
    static printBulkPickList(context: Context, document: SalesShipment) {
        return xtremReporting.nodes.Report.generateReportPdf(context, 'salesShipmentPickList', '', {
            variables: JSON.stringify({
                shipment: document._id,
            }),
            isBulk: true,
        });
    }

    @decorators.mutation<typeof SalesShipment, 'resynchronizeStatus'>({
        isPublished: true,
        parameters: [
            {
                name: 'salesShipment',
                type: 'reference',
                isMandatory: true,
                isWritable: true,
                node: () => SalesShipment,
            },
        ],
        return: 'boolean',
    })
    static async resynchronizeStatus(context: Context, salesShipment: SalesShipment): Promise<boolean> {
        const oldStatus = await salesShipment.status;
        const { countCompletedLines, countInErrorLines } =
            await xtremStockData.functions.stockTransactionLib.resynchronizeStockStatus(context, {
                document: salesShipment,
                logger: loggers.shipment,
                optionalCallbacks: {
                    onLineSucceeded: xtremSales.nodes.SalesShipment.updateLineOnSuccess,
                    beforeDocumentSave: xtremSales.nodes.SalesShipment.updateHeaderAfterStockUpdate,
                },
            });

        if (countInErrorLines > 0) {
            // Some lines are now in error. Need to post the shipment and resynchronize again.'
            // Don't throw an error here to keep the data fixed in resynchronizeStockStatus
            return true;
        }

        // Check if StockJournal have been created. If not -> not managed yet
        const isStockUpdateFinished = await salesShipment.lines.every(
            async line => (await line.stockTransactionStatus) === 'completed',
        );
        if (!isStockUpdateFinished) {
            throw new BusinessRuleError(
                context.localize(
                    '@sage/xtrem-sales/nodes__sales_shipment__stock_update_not_finished',
                    'The stock update is still in progress. You can resync after it has completed.',
                ),
            );
        }

        await xtremSales.functions.resynchronizeShipmentStatus(salesShipment, loggers.shipment);

        if (countCompletedLines > 0) {
            // As all lines are now completed, we can trigger the function onceStockCompleted
            await xtremSales.nodes.SalesShipment.onceStockCompleted(context, salesShipment);
            return true;
        }

        if (oldStatus !== (await salesShipment.status)) return true;

        return false;
    }

    @decorators.mutation<typeof SalesShipment, 'resendNotificationForFinance'>({
        isPublished: true,
        parameters: [{ name: 'salesShipment', type: 'reference', isMandatory: true, node: () => SalesShipment }],
        return: {
            type: 'boolean',
        },
    })
    static async resendNotificationForFinance(context: Context, salesShipment: SalesShipment): Promise<boolean> {
        loggers.shipment.info(
            context.localize(
                '@sage/xtrem-sales/node__sales_shipment__resend_notification_for_finance',
                'Resending finance notification for sales shipment: {{shipmentNumber}}',
                { shipmentNumber: await salesShipment.number },
            ),
        );

        if (
            await xtremFinanceData.functions.AccountingEngineCommon.validateResendNotificationForFinance(context, {
                documentNumber: await salesShipment.number,
                documentType: 'salesShipment',
            })
        ) {
            // send notification in order to create a staging table entry for the accounting engine
            if (await (await (await salesShipment.stockSite).legalCompany).doStockPosting) {
                await xtremFinanceData.functions.salesShipmentNotification(
                    context,
                    salesShipment as xtremFinanceData.interfaces.SalesFinanceDocument,
                    (await salesShipment.lines.toArray()) as xtremFinanceData.interfaces.FinanceOriginDocumentLineWithStock[],
                );
            }
        }

        return true;
    }

    @decorators.notificationListener<typeof SalesShipment>({
        topic: 'SalesShipment/resendNotificationForFinance',
    })
    static async onResendNotificationForFinance(context: Context, document: { number: number }) {
        const salesShipment = await context.read(SalesShipment, { number: document.number });

        await SalesShipment.resendNotificationForFinance(context, salesShipment);
    }

    /**
     * Checks if the packingSlip can be printed
     * @returns true if it can be printed
     * @throws {BusinessRuleError} if cannot be printed
     */
    @decorators.mutation<typeof SalesShipment, 'beforePrintPackingSlip'>({
        isPublished: true,
        startsReadOnly: true,
        parameters: [
            {
                name: 'shipment',
                type: 'reference',
                isMandatory: true,
                node: () => xtremSales.nodes.SalesShipment,
            },
        ],
        return: { type: 'boolean' },
    })
    static async beforePrintPackingSlip(context: Context, shipment: SalesShipment): Promise<boolean> {
        if ((await shipment.status) === 'readyToProcess') {
            throw new BusinessRuleError(
                context.localize(
                    '@sage/xtrem-sales/cant_print_packing_slip_when_status_is_ready_to_process_bulk',
                    'The sales shipment cannot be printed: {{number}}. It is in status: {{shipped}}.',
                    {
                        number: await shipment.number,
                        shipped: context.localizeEnumMember('@sage/xtrem-sales/SalesShipmentStatus', 'readyToProcess'),
                    },
                ),
            );
        }
        return true;
    }

    /**
     * Updates the isPrinted property after successfully printing packingSlip
     * @returns true if the shipment was correctly updated
     */
    @decorators.mutation<typeof SalesShipment, 'afterPrintPackingSlip'>({
        isPublished: true,
        startsReadOnly: true,
        parameters: [
            {
                name: 'shipment',
                type: 'reference',
                isMandatory: true,
                node: () => xtremSales.nodes.SalesShipment,
            },
        ],
        return: { type: 'boolean' },
    })
    static async afterPrintPackingSlip(readonlyContext: Context, shipment: SalesShipment): Promise<boolean> {
        const shipmentNumber = await shipment.number;
        if (!(await shipment.isPrinted)) {
            await readonlyContext.runInWritableContext(async writableContext => {
                const writableShipment = await writableContext.read(
                    SalesShipment,
                    {
                        _id: shipment._id,
                    },
                    {
                        forUpdate: true,
                    },
                );
                writableShipment.canUpdateIsPrinted = true;
                await writableShipment.$.set({ isPrinted: true });
                await writableShipment.$.save();
            });
            await readonlyContext.batch.logMessage('info', `Sales shipment ${shipmentNumber} marked as printed.`);
        } else {
            await readonlyContext.batch.logMessage(
                'info',
                `Sales shipment ${shipmentNumber} is already marked as printed. Skipping update.`,
            );
        }
        return true;
    }

    /**
     * Checks if the salesShipmentPickList can be printed
     * @returns true if it can be printed
     * @throws {BusinessRuleError} if cannot be printed
     */
    @decorators.mutation<typeof SalesShipment, 'beforePrintSalesShipmentPickList'>({
        isPublished: true,
        startsReadOnly: true,
        parameters: [
            {
                name: 'shipment',
                type: 'reference',
                isMandatory: true,
                node: () => xtremSales.nodes.SalesShipment,
            },
        ],
        return: { type: 'boolean' },
    })
    static async beforePrintSalesShipmentPickList(context: Context, shipment: SalesShipment): Promise<boolean> {
        if ((await shipment.status) !== 'readyToProcess') {
            throw new BusinessRuleError(
                context.localize(
                    '@sage/xtrem-sales/cant_print_pick_list_when_status_is_not_ready_to_process_bulk',
                    'To print the pick list, the sales shipment needs to be ready to process: {{number}}.',
                    { number: await shipment.number },
                ),
            );
        }
        return true;
    }
}
