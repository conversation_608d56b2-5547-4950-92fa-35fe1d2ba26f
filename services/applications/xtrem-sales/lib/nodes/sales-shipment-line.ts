import type { Collection, decimal, integer, Reference, TextStream } from '@sage/xtrem-core';
import { date, decorators, NodeStatus, useDefaultValue } from '@sage/xtrem-core';
import { Decimal } from '@sage/xtrem-decimal';
import type * as xtremFinanceData from '@sage/xtrem-finance-data';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremStockData from '@sage/xtrem-stock-data';
import { isPostedErrorClosed } from '../functions/sales-document-common-lib';
import { updateLineNotesOnCreation } from '../functions/sales-shipment-lib';
import * as xtremSales from '../index';

@decorators.subNode<SalesShipmentLine>({
    isPublished: true,
    isVitalCollectionChild: true,
    canSearch: true,
    canRead: true,
    extends: () => xtremMasterData.nodes.BaseDocumentItemLine,
    async controlBegin(cx) {
        await xtremSales.events.controlBegin.shipmentLine.checkStatusOnCreation(cx, this);
        await xtremSales.events.controlBegin.shipmentLine.checkCustomerOnHoldOnCreation(cx, this);
        await xtremSales.events.controlBegin.shipmentLine.checkQuantityOnUpdate(cx, this);
        await xtremSales.events.controlBegin.shipmentLine.checkItemOnUpdate(cx, this);
        await xtremSales.events.controlBegin.shipmentLine.checkUnitOnUpdate(cx, this);
        await xtremSales.events.controlBegin.shipmentLine.checkAllocatedQuantityOnUpdate(cx, this);
    },
    async createEnd() {
        await this.updateTempLinesDicts('create');
    },
    async saveBegin() {
        if (this.$.status === NodeStatus.modified) {
            if ((await (await this.$.old).quantity) !== (await this.quantity)) {
                await this.updateTempLinesDicts('update');
            }

            await this.$.set({ amountExcludingTax: (await this.quantity) * (await this.netPrice) });
        }

        await updateLineNotesOnCreation(this);
    },
    async saveEnd() {
        const quantityInStockUnit = await this.quantityInStockUnit;
        const oldQuantityInStockUnit =
            this.$.status === NodeStatus.modified ? await (await this.$.old).quantityInStockUnit : 0;
        if (
            this.$.status === NodeStatus.added ||
            (this.$.status === NodeStatus.modified && quantityInStockUnit - oldQuantityInStockUnit > 0)
        ) {
            await this.salesOrderLines.forEach(async line => {
                const requiredQuantity =
                    this.$.status === NodeStatus.added
                        ? quantityInStockUnit
                        : quantityInStockUnit - oldQuantityInStockUnit;

                const allocationPropositions = await xtremStockData.nodes.Stock.proposeAllocationsToTransfer(
                    this.$.context,
                    {
                        orderDocumentLine: await this.$.context.read(
                            xtremSales.nodes.SalesOrderLine,
                            { _id: (await line.linkedDocument)._id },
                            { forUpdate: true },
                        ),
                        requiredQuantity,
                        hasAllAllocationsReturned: false,
                    },
                );
                if (allocationPropositions.length > 0) {
                    await xtremStockData.nodes.Stock.updateAllocations(this.$.context, {
                        documentLine: line.document,
                        allocationUpdates: allocationPropositions.map(proposition => {
                            return {
                                action: 'transfer',
                                quantityToTransfer: proposition.quantityToTransfer,
                                allocationRecord:
                                    proposition.allocationRecord as unknown as Promise<xtremStockData.nodes.StockAllocation>,
                                serialNumbers: proposition.serialNumbers,
                            };
                        }),
                    });
                }
            });
        }
    },
    async controlDelete(cx): Promise<void> {
        await xtremSales.events.controlDelete.shipmentLine.checkStatusForDeletion(cx, this);
        await this.updateTempLinesDicts('delete');
        await xtremSales.events.controlDelete.shipmentLine.checkStockAllocationForDeletion(cx, this);
    },
})
@xtremStockData.functions.staticImplements<xtremStockData.interfaces.StaticDocumentLineWithStockPosting>()
export class SalesShipmentLine
    extends xtremMasterData.nodes.BaseDocumentItemLine
    implements
        xtremFinanceData.interfaces.FinanceOriginDocumentLineWithStock,
        xtremStockData.interfaces.DocumentLineWithStockAllocation
{
    getOrderCost(): Promise<decimal> {
        return xtremStockData.functions.stockDocumentLib.defaultDocumentLineCost(
            (this as xtremMasterData.nodes.BaseDocumentLine).$.context,
            this,
            {
                valuationType: 'issue',
            },
        );
    }

    getValuedCost(): Promise<decimal> {
        return this.getOrderCost();
    }

    // eslint-disable-next-line class-methods-use-this
    getValuationParameters(): xtremStockData.interfaces.ValuationParameter {
        return { valuationType: 'issue', canBeReturned: true };
    }

    @decorators.referencePropertyOverride<SalesShipmentLine, 'document'>({
        node: () => xtremSales.nodes.SalesShipment,
    })
    override readonly document: Reference<xtremSales.nodes.SalesShipment>;

    @decorators.stringPropertyOverride<SalesShipmentLine, 'documentNumber'>({
        async getValue() {
            return (await this.document).number;
        },
    })
    override readonly documentNumber: Promise<string>;

    @decorators.integerPropertyOverride<SalesShipmentLine, 'documentId'>({
        async getValue() {
            return (await this.document)._id;
        },
    })
    override readonly documentId: Promise<integer>;

    @decorators.enumProperty<SalesShipmentLine, 'originDocumentType'>({
        isPublished: true,
        dataType: () => xtremSales.enums.salesOriginDocumentTypeDataType,
        getValue() {
            return this.origin as unknown as xtremSales.enums.SalesOriginDocumentType;
        },
    })
    readonly originDocumentType: Promise<xtremSales.enums.SalesOriginDocumentType>;

    @decorators.enumPropertyOverride<SalesShipmentLine, 'status'>({
        // dataType: () => xtremSales.enums.salesShipmentStatusDataType,
        defaultValue: () => 'readyToProcess',
    })
    override readonly status: Promise<xtremSales.enums.SalesShipmentStatus>;

    @decorators.enumProperty<SalesShipmentLine, 'invoiceStatus'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremSales.enums.salesDocumentInvoiceStatusDataType,
        defaultValue() {
            return 'notInvoiced';
        },
    })
    readonly invoiceStatus: Promise<xtremSales.enums.SalesDocumentInvoiceStatus>;

    @decorators.enumProperty<SalesShipmentLine, 'returnRequestStatus'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremSales.enums.salesDocumentReturnStatusDataType,
        defaultValue() {
            return 'noReturnRequested';
        },
    })
    readonly returnRequestStatus: Promise<xtremSales.enums.SalesDocumentReturnStatus>;

    @decorators.enumProperty<SalesShipmentLine, 'returnReceiptStatus'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremSales.enums.salesDocumentReceiptStatusDataType,
        defaultValue() {
            return 'notReturned';
        },
    })
    readonly returnReceiptStatus: Promise<xtremSales.enums.SalesDocumentReceiptStatus>;

    @decorators.enumProperty<SalesShipmentLine, 'allocationStatus'>({
        isPublished: true,
        dataType: () => xtremStockData.enums.stockAllocationStatusDataType,
        dependsOn: ['remainingQuantityToAllocate', 'quantityAllocated', 'item'],
        async getValue() {
            return xtremStockData.functions.allocationLib.getLineAllocationStatus(
                await this.remainingQuantityToAllocate,
                await this.quantityAllocated,
                await (
                    await this.item
                ).isStockManaged,
            );
        },
    })
    readonly allocationStatus: Promise<xtremStockData.enums.StockAllocationStatus>;

    @decorators.enumProperty<SalesShipmentLine, 'stockTransactionStatus'>({
        isStored: true,
        isPublished: true,
        defaultValue() {
            return 'draft';
        },
        dataType: () => xtremStockData.enums.StockDocumentTransactionStatusDataType,
    })
    readonly stockTransactionStatus: Promise<xtremStockData.enums.StockDocumentTransactionStatus>;

    @decorators.referencePropertyOverride<SalesShipmentLine, 'item'>({
        // isRequired: true,
        dependsOn: [{ document: ['stockSite'] }],
        node: () => xtremMasterData.nodes.Item,
        filters: {
            control: {
                isSold: true,
                itemSites: {
                    _atLeast: 1,
                    async site() {
                        return (await this.document).stockSite;
                    },
                },
            },
        },
    })
    override readonly item: Reference<xtremMasterData.nodes.Item>;

    @decorators.decimalPropertyOverride<SalesShipmentLine, 'quantity'>({
        // isRequired: true,
        async control(cx, val) {
            await xtremSales.events.control.quantityInSalesUnitControls.checkQuantityInSalesUnit(
                this.$.context,
                cx,
                val,
                await this.item,
                await (
                    await this.document
                ).shipToCustomer,
                await this.unit,
                {
                    canBeZero: true,
                    excludeMinimumQuantity: true,
                },
            );
        },
    })
    override readonly quantity: Promise<decimal>;

    // @TODO DELETE AFTER BASE DOCUMENT REFACTORING
    @decorators.decimalProperty<SalesShipmentLine, 'quantityInSalesUnit'>({
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.quantityInSalesUnit,
        getValue() {
            return this.quantity;
        },
    })
    readonly quantityInSalesUnit: Promise<decimal>;

    @decorators.decimalProperty<SalesShipmentLine, 'quantityAllocated'>({
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.quantityInStockUnit,
        dependsOn: ['stockAllocations'],
        getValue() {
            return this.stockAllocations.where().sum(line => line.quantityInStockUnit);
        },
    })
    readonly quantityAllocated: Promise<decimal>;

    @decorators.referencePropertyOverride<SalesShipmentLine, 'unit'>({
        // isRequired: true,
        dependsOn: ['item', { document: ['shipToCustomer'] }],
        async defaultValue() {
            return (await xtremMasterData.functions.getSalesUnitDefaultValue(
                this.$.context,
                await this.item,
                (await (await this.document).shipToCustomer)._id,
            ))!;
        },
        async control(cx, val) {
            await cx.error
                .if(await val.id)
                .is.not.in(
                    await xtremMasterData.functions.getValidSalesUnitList(
                        this.$.context,
                        await this.item,
                        await (
                            await this.document
                        ).shipToCustomer,
                    ),
                );
        },
    })
    override readonly unit: Reference<xtremMasterData.nodes.UnitOfMeasure>;

    // @TODO DELETE AFTER BASE DOCUMENT REFACTORING
    @decorators.referenceProperty<SalesShipmentLine, 'salesUnit'>({
        isPublished: true,
        dependsOn: ['unit'],
        node: () => xtremMasterData.nodes.UnitOfMeasure,
        getValue() {
            return this.unit;
        },
    })
    readonly salesUnit: Reference<xtremMasterData.nodes.UnitOfMeasure>;

    @decorators.decimalPropertyOverride<SalesShipmentLine, 'unitToStockUnitConversionFactor'>({
        // isRequired: true,
        dependsOn: ['item', { document: ['shipToCustomer'] }],
        async defaultValue() {
            return xtremMasterData.functions.calculateConversionFactor(
                this.$.context,
                await this.item,
                (await (await this.document).shipToCustomer)?._id,
            );
        },
        updatedValue: useDefaultValue,
    })
    override readonly unitToStockUnitConversionFactor: Promise<decimal>;

    // @TODO SHOULD BE REMOVED AFTER BASE DOCUMENT REFACTORING
    @decorators.decimalProperty<SalesShipmentLine, 'salesUnitToStockUnitConversionFactor'>({
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.coefficientDataType,
        getValue() {
            return this.unitToStockUnitConversionFactor;
        },
    })
    readonly salesUnitToStockUnitConversionFactor: Promise<decimal>;

    @decorators.decimalPropertyOverride<SalesShipmentLine, 'quantityInStockUnit'>({
        // excludedFromPayload: true,
        dependsOn: ['stockUnit', 'quantity', 'unitToStockUnitConversionFactor'],
        defaultValue() {
            return this.quantityInStockUnitComputed();
        },
        updatedValue: useDefaultValue,
        async control(cx, val) {
            await cx.error.if(Number(val)).is.not.equal.to(await this.quantityInStockUnitComputed());
        },
    })
    override readonly quantityInStockUnit: Promise<decimal>;

    @decorators.decimalProperty<SalesShipmentLine, 'remainingQuantity'>({
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.quantityInStockUnit,
        dependsOn: ['quantityInStockUnit', 'quantityAllocated'],
        async computeValue() {
            return Number(Decimal.make(await this.quantityInStockUnit).sub(await this.quantityAllocated));
        },
    })
    readonly remainingQuantity: Promise<decimal>;

    @decorators.referencePropertyOverride<SalesShipmentLine, 'stockUnit'>({
        // excludedFromPayload: true,
        async control(cx, val) {
            await cx.error.if(await val.id).is.not.equal.to(await (await (await this.item).stockUnit).id);
        },
    })
    override readonly stockUnit: Reference<xtremMasterData.nodes.UnitOfMeasure>;

    @decorators.collectionProperty<SalesShipmentLine, 'salesOrderLines'>({
        isPublished: true,
        node: () => xtremSales.nodes.SalesOrderLineToSalesShipmentLine,
        isVital: true,
        forceFullSave: true,
        reverseReference: 'document',
        dependsOn: ['quantity'],
    })
    readonly salesOrderLines: Collection<xtremSales.nodes.SalesOrderLineToSalesShipmentLine>;

    @decorators.collectionProperty<SalesShipmentLine, 'discountCharges'>({
        isVital: true,
        isPublished: true,
        reverseReference: 'document',
        node: () => xtremSales.nodes.SalesShipmentLineDiscountCharge,
        dependsOn: ['grossPrice'],
        defaultValue() {
            return xtremMasterData.functions.getDiscountChargeDefaultValue();
        },
    })
    readonly discountCharges: Collection<xtremSales.nodes.SalesShipmentLineDiscountCharge>;

    @decorators.decimalProperty<SalesShipmentLine, 'discount'>({
        excludedFromPayload: true,
        isPublished: true,
        isNullable: true,
        dataType: () => xtremMasterData.dataTypes.baseDecimal,
        dependsOn: ['grossPrice', { discountCharges: ['sign', 'basis', 'valueType', 'value'] }],
        getValue() {
            return xtremMasterData.functions.computeDiscountChargeValue(this.discountCharges, 'decrease');
        },
        async setValue(val: decimal) {
            await xtremMasterData.functions.setDiscountChargeValue(
                this.discountCharges,
                'decrease',
                val,
                (await this.grossPrice) ?? 0,
            );
        },
    })
    readonly discount: Promise<decimal | null>;

    @decorators.decimalProperty<SalesShipmentLine, 'charge'>({
        excludedFromPayload: true,
        isPublished: true,
        isNullable: true,
        dataType: () => xtremMasterData.dataTypes.baseDecimal,
        dependsOn: ['grossPrice', { discountCharges: ['sign'] }],
        getValue() {
            return xtremMasterData.functions.computeDiscountChargeValue(this.discountCharges, 'increase');
        },
        async setValue(val: decimal) {
            await xtremMasterData.functions.setDiscountChargeValue(
                this.discountCharges,
                'increase',
                val,
                (await this.grossPrice) ?? 0,
            );
        },
    })
    readonly charge: Promise<decimal | null>;

    @decorators.decimalProperty<SalesShipmentLine, 'grossPrice'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
        dataType: () => xtremMasterData.dataTypes.priceInSalesPrice,
        async control(cx, val) {
            await cx.error.if(val).is.negative();
        },
    })
    readonly grossPrice: Promise<decimal | null>;

    @decorators.decimalProperty<SalesShipmentLine, 'netPrice'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.priceInSalesPrice,
        async control(cx, val) {
            await cx.error.if(val).is.negative();
        },
        dependsOn: [
            'grossPrice',
            'quantity',
            { discountCharges: ['sign', 'basis', 'valueType', 'value'] },
            { document: ['currency'] },
            'currency',
            'discountCharges',
        ],
        async defaultValue() {
            return (await this.currency)
                ? xtremMasterData.functions.calculateNetPrice(
                      this.discountCharges,
                      (await this.grossPrice) ?? 0,
                      await this.quantity,
                      await (
                          await (
                              await this.document
                          ).currency
                      ).decimalDigits,
                  )
                : 0;
        },
        updatedValue: useDefaultValue,
    })
    readonly netPrice: Promise<decimal>;

    @decorators.decimalProperty<SalesShipmentLine, 'amountExcludingTax'>({
        isStoredOutput: true,
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.priceInSalesPrice,
        dependsOn: ['quantity', 'netPrice'],
        async defaultValue() {
            return (await this.quantity) * (await this.netPrice);
        },
        updatedValue: useDefaultValue,
    })
    readonly amountExcludingTax: Promise<decimal>;

    @decorators.decimalProperty<SalesShipmentLine, 'amountExcludingTaxInCompanyCurrency'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.priceInSalesPrice,
        async control(cx, val) {
            await cx.error.if(val).is.negative();
        },
        dependsOn: [
            'quantity',
            'netPrice',
            'amountExcludingTax',
            { document: ['site', 'currency', 'companyFxRateDivisor', 'companyFxRate'] },
        ],
        async defaultValue() {
            const document = await this.document;
            // convert transaction amount into company currency
            return xtremMasterData.sharedFunctions.convertAmount(
                await this.amountExcludingTax,
                await document.companyFxRate,
                await document.companyFxRateDivisor,
                await (
                    await document.currency
                ).decimalDigits,
                await (
                    await (
                        await (
                            await document.site
                        ).legalCompany
                    ).currency
                ).decimalDigits,
            );
        },
        updatedValue: useDefaultValue,
    })
    readonly amountExcludingTaxInCompanyCurrency: Promise<decimal>;

    @decorators.jsonPropertyOverride<SalesShipmentLine, 'computedAttributes'>({
        dependsOn: ['item', { document: ['site', 'stockSite', 'billToCustomer'] }],
        async computeValue() {
            const document = await this.document;
            return xtremSales.functions.computeAttributes(
                this.$.context,
                await document.site,
                await document.stockSite,
                await this.item,
                await document.billToCustomer,
            );
        },
    })
    override readonly computedAttributes: Promise<object>;

    @decorators.collectionProperty<SalesShipmentLine, 'toInvoiceLines'>({
        node: () => xtremSales.nodes.SalesShipmentLineToSalesInvoiceLine,
        reverseReference: 'linkedDocument',
    })
    readonly toInvoiceLines: Collection<xtremSales.nodes.SalesShipmentLineToSalesInvoiceLine>;

    @decorators.decimalProperty<SalesShipmentLine, 'quantityInvoicedInProgressInSalesUnit'>({
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.quantityInSalesUnit,
        getValue() {
            return this.toInvoiceLines
                .where(async line => ['draft'].includes(await (await (await line.document).document).status))
                .sum(async line => (await line.document).quantity);
        },
    })
    readonly quantityInvoicedInProgressInSalesUnit: Promise<decimal>;

    @decorators.decimalProperty<SalesShipmentLine, 'quantityInvoicedPostedInSalesUnit'>({
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.quantityInSalesUnit,
        getValue() {
            return this.toInvoiceLines
                .where(async line =>
                    ['posted', 'inProgress', 'error'].includes(await (await (await line.document).document).status),
                )
                .sum(async line => (await line.document).quantity);
        },
    })
    readonly quantityInvoicedPostedInSalesUnit: Promise<decimal>;

    @decorators.decimalProperty<SalesShipmentLine, 'quantityRequestedInSalesUnit'>({
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.quantityInSalesUnit,
        computeValue() {
            return xtremSales.classes.SalesReturnRequestCreator.getTotalQuantityFromLines(
                {
                    linkedDocument: { _id: this._id },
                },
                this.$.context,
                xtremSales.nodes.SalesShipmentLineToSalesReturnRequestLine,
            );
        },
    })
    readonly quantityRequestedInSalesUnit: Promise<decimal>;

    @decorators.collectionProperty<SalesShipmentLine, 'salesReturnReceiptLines'>({
        // TODO: should we publish it?
        node: () => xtremSales.nodes.SalesShipmentLineToSalesReturnReceiptLine,
        reverseReference: 'linkedDocument',
    })
    readonly salesReturnReceiptLines: Collection<xtremSales.nodes.SalesShipmentLineToSalesReturnReceiptLine>;

    @decorators.decimalProperty<SalesShipmentLine, 'quantityReceiptInSalesUnit'>({
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.quantityInSalesUnit,
        getValue() {
            return this.salesReturnReceiptLines.where().sum(async line => (await line.document).quantity);
        },
    })
    readonly quantityReceiptInSalesUnit: Promise<decimal>;

    @decorators.referenceProperty<SalesShipmentLine, 'currency'>({
        isPublished: false,
        node: () => xtremMasterData.nodes.Currency,
        dependsOn: [{ document: ['currency'] }],
        async getValue() {
            return (await this.document).currency;
        },
    })
    readonly currency: Reference<xtremMasterData.nodes.Currency>;

    @decorators.collectionProperty<SalesShipmentLine, 'stockAllocations'>({
        isPublished: true,
        reverseReference: 'documentLine',
        node: () => xtremStockData.nodes.StockAllocation,
    })
    readonly stockAllocations: Collection<xtremStockData.nodes.StockAllocation>;

    @decorators.decimalProperty<SalesShipmentLine, 'remainingQuantityToAllocate'>({
        isPublished: true,
        dependsOn: ['quantityInStockUnit', 'quantityAllocated'],
        dataType: () => xtremMasterData.dataTypes.quantityInStockUnit,
        async getValue() {
            return (await this.quantityInStockUnit) - (await this.quantityAllocated);
        },
    })
    readonly remainingQuantityToAllocate: Promise<decimal>;

    @decorators.enumProperty<SalesShipmentLine, 'priceOrigin'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
        dataType: () => xtremSales.enums.salesPriceOriginDataType,
    })
    readonly priceOrigin: Promise<xtremSales.enums.SalesPriceOrigin | null>;

    @decorators.referenceProperty<SalesShipmentLine, 'priceReason'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
        node: () => xtremMasterData.nodes.CustomerPriceReason,
    })
    readonly priceReason: Reference<xtremMasterData.nodes.CustomerPriceReason | null>;

    @decorators.enumProperty<SalesShipmentLine, 'priceOriginDeterminated'>({
        excludedFromPayload: true,
        isStored: true,
        isPublished: true,
        isNullable: true,
        dataType: () => xtremSales.enums.salesPriceOriginDataType,
        dependsOn: ['priceOrigin'],
        defaultValue() {
            return this.priceOrigin;
        },
    })
    readonly priceOriginDeterminated: Promise<xtremSales.enums.SalesPriceOrigin | null>;

    @decorators.referenceProperty<SalesShipmentLine, 'priceReasonDeterminated'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
        node: () => xtremMasterData.nodes.CustomerPriceReason,
        dependsOn: ['priceReason'],
        defaultValue() {
            return this.priceReason;
        },
    })
    readonly priceReasonDeterminated: Reference<xtremMasterData.nodes.CustomerPriceReason | null>;

    @decorators.booleanProperty<SalesShipmentLine, 'isPriceDeterminated'>({
        excludedFromPayload: true,
        isStored: true,
        isPublished: true,
        defaultValue() {
            return false;
        },
    })
    readonly isPriceDeterminated: Promise<boolean>;

    @decorators.decimalProperty<SalesShipmentLine, 'grossPriceDeterminated'>({
        excludedFromPayload: true,
        isStored: true,
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.priceInSalesPrice,
    })
    readonly grossPriceDeterminated: Promise<decimal>;

    @decorators.decimalProperty<SalesShipmentLine, 'stockCostAmountInCompanyCurrency'>({
        isStoredOutput: true,
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.amountInCompanyCurrency,
        dependsOn: ['quantityInStockUnit', 'item', { document: ['stockSite'] }, 'netPrice', 'status'],
        defaultValue() {
            return this.getNewStockCostAmount();
        },
        updatedValue: useDefaultValue,
    })
    readonly stockCostAmountInCompanyCurrency: Promise<decimal>;

    @decorators.decimalProperty<SalesShipmentLine, 'grossProfitAmountInCompanyCurrency'>({
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.amountInCompanyCurrency,
        dependsOn: ['amountExcludingTaxInCompanyCurrency', 'stockCostAmountInCompanyCurrency'],
        async getValue() {
            return (await this.amountExcludingTaxInCompanyCurrency) - (await this.stockCostAmountInCompanyCurrency);
        },
    })
    readonly grossProfitAmountInCompanyCurrency: Promise<decimal>;

    @decorators.decimalProperty<SalesShipmentLine, 'stockCostAmount'>({
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.amountInTransactionCurrency,
        dependsOn: ['stockCostAmountInCompanyCurrency'],
        async getValue() {
            const document = await this.document;
            // convert from company currency to transaction currency
            return xtremMasterData.sharedFunctions.convertAmount(
                await this.stockCostAmountInCompanyCurrency,
                await document.companyFxRateDivisor,
                await document.companyFxRate,
                await (
                    await (
                        await (
                            await document.site
                        ).legalCompany
                    ).currency
                ).decimalDigits,
                await (
                    await document.currency
                ).decimalDigits,
            );
        },
    })
    readonly stockCostAmount: Promise<decimal>;

    @decorators.decimalProperty<SalesShipmentLine, 'grossProfitAmount'>({
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.amountInTransactionCurrency,
        dependsOn: ['amountExcludingTax', 'stockCostAmount'],
        async getValue() {
            return (await this.amountExcludingTax) - (await this.stockCostAmount);
        },
    })
    readonly grossProfitAmount: Promise<decimal>;

    @decorators.decimalProperty<SalesShipmentLine, 'stockCostUnit'>({
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.orderCostDataType,
        dependsOn: ['quantity', 'stockCostAmount'],
        // eslint-disable-next-line @sage/xtrem/property-decorators-errors
        async computeValue() {
            const quantity = await this.quantity;
            return quantity ? (await this.stockCostAmount) / quantity : 0;
        },
    })
    readonly stockCostUnit: Promise<decimal>;

    @decorators.decimalProperty<SalesShipmentLine, 'grossProfit'>({
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.priceDataType,
        dependsOn: ['netPrice', 'stockCostUnit'],
        // eslint-disable-next-line @sage/xtrem/property-decorators-errors
        async computeValue() {
            return (await this.netPrice) - (await this.stockCostUnit);
        },
    })
    readonly grossProfit: Promise<decimal>;

    @decorators.collectionProperty<SalesShipmentLine, 'stockDetails'>({
        isPublished: true,
        isVital: true,
        reverseReference: 'documentLine',
        dependsOn: ['itemSite'],
        node: () => xtremStockData.nodes.StockIssueDetail,
    })
    readonly stockDetails: Collection<xtremStockData.nodes.StockIssueDetail>;

    @decorators.collectionProperty<SalesShipmentLine, 'stockMovements'>({
        isPublished: true,
        reverseReference: 'documentLine',
        node: () => xtremStockData.nodes.StockJournal,
    })
    readonly stockMovements: Collection<xtremStockData.nodes.StockJournal>;

    @decorators.collectionProperty<SalesShipmentLine, 'stockTransactions'>({
        isPublished: true,
        reverseReference: 'documentLine',
        isVital: true,
        node: () => xtremStockData.nodes.StockTransaction,
    })
    readonly stockTransactions: Collection<xtremStockData.nodes.StockTransaction>;

    @decorators.textStreamPropertyOverride<SalesShipmentLine, 'internalNote'>({
        async isFrozen() {
            return isPostedErrorClosed(await (await this.document).status);
        },
    })
    override readonly internalNote: Promise<TextStream>;

    @decorators.textStreamPropertyOverride<SalesShipmentLine, 'externalNote'>({
        async isFrozen() {
            return isPostedErrorClosed(await (await this.document).status);
        },
    })
    override readonly externalNote: Promise<TextStream>;

    /** to be sure the user know that externalNode will go to external documents */
    @decorators.booleanPropertyOverride<SalesShipmentLine, 'isExternalNote'>({
        async isFrozen() {
            return isPostedErrorClosed(await (await this.document).status);
        },
    })
    override readonly isExternalNote: Promise<boolean>;

    @decorators.stringProperty<SalesShipmentLine, 'customerNumber'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremSales.dataTypes.propertyDataType,
    })
    readonly customerNumber: Promise<string>;

    async updateTempLinesDicts(action: string): Promise<void> {
        await xtremSales.classes.BaseSalesDocumentsCreator.fillTempSalesDocumentDicts(
            action,
            'salesOrderLines',
            '__salesOrders',
            this,
        );
    }

    async quantityInStockUnitComputed(): Promise<decimal> {
        return +new Decimal((await this.quantity) * (await this.unitToStockUnitConversionFactor)).toDecimalPlaces(
            (await (await this.stockUnit)?.decimalDigits) || 0,
        );
    }

    async getNewStockCostAmount(): Promise<decimal> {
        const item = await this.item;
        // When the status becomes 'shipped', it means the stock transactions have been created and the value to use is the sum of movement amounts
        if ((await this.status) === 'shipped' && (await item.isStockManaged)) {
            return -(
                await xtremStockData.functions.stockValuationLib.getDocumentLineStockValue(this.$.context, this._id)
            ).valuedAmount;
        }
        return (
            await xtremStockData.nodeExtensions.ItemSiteExtension.getValuationCost(
                this.$.context,
                item,
                await (
                    await this.document
                ).stockSite,
                { quantity: await this.quantityInStockUnit, dateOfValuation: date.today(), valuationType: 'issue' },
            )
        ).amount;
    }

    async controlDataForUpdateAllocationMutation(
        updateData: xtremStockData.interfaces.DataForUpdateAllocationMutationActions,
        updateIndex: integer,
    ): Promise<Array<string>> {
        if ((await this.status) === 'shipped' && ['create', 'increase', 'transfer'].includes(updateData.action)) {
            return [
                `[allocationUpdate(${updateIndex})] - The ${updateData.action}-allocation` +
                    ` cannot be performed on a shipped shipment line.`,
            ];
        }
        return [];
    }
}
