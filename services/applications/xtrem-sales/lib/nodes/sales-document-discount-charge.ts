/* eslint max-classes-per-file: ["error", 4] */
import type { decimal, Reference } from '@sage/xtrem-core';
import { decorators, useDefaultValue } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremSales from '../index';

@decorators.subNode<SalesOrderLineDiscountCharge>({
    extends: () => xtremMasterData.nodes.BaseLineDiscountCharge,
    isPublished: true,
    isVitalCollectionChild: true,
    canSearch: true,
    canRead: true,
})
export class SalesOrderLineDiscountCharge extends xtremMasterData.nodes.BaseLineDiscountCharge {
    @decorators.referenceProperty<SalesOrderLineDiscountCharge, 'document'>({
        isStored: true,
        isPublished: true,
        isVitalParent: true,
        node: () => xtremSales.nodes.SalesOrderLine,
    })
    override readonly document: Reference<xtremSales.nodes.SalesOrderLine>;

    @decorators.decimalPropertyOverride<SalesOrderLineDiscountCharge, 'basis'>({
        dependsOn: [{ document: ['grossPrice'] }],
        async defaultValue() {
            return (await (await this.document).grossPrice) ?? 0;
        },
        updatedValue: useDefaultValue,
    })
    override readonly basis: Promise<decimal>;

    override calculateAmount(): Promise<number> {
        return super.calculateAmount(3);
    }
}

@decorators.subNode<SalesShipmentLineDiscountCharge>({
    isPublished: true,
    isVitalCollectionChild: true,
    canRead: true,
    extends: () => xtremMasterData.nodes.BaseLineDiscountCharge,
})
export class SalesShipmentLineDiscountCharge extends xtremMasterData.nodes.BaseLineDiscountCharge {
    @decorators.referenceProperty<SalesShipmentLineDiscountCharge, 'document'>({
        isStored: true,
        isPublished: true,
        isVitalParent: true,
        node: () => xtremSales.nodes.SalesShipmentLine,
    })
    override readonly document: Reference<xtremSales.nodes.SalesShipmentLine>;

    @decorators.decimalPropertyOverride<SalesShipmentLineDiscountCharge, 'basis'>({
        dependsOn: [{ document: ['grossPrice'] }],
        async defaultValue() {
            return (await (await this.document).grossPrice) ?? 0;
        },
        updatedValue: useDefaultValue,
    })
    override readonly basis: Promise<decimal>;

    override calculateAmount(): Promise<number> {
        return super.calculateAmount(3);
    }
}

@decorators.subNode<SalesInvoiceLineDiscountCharge>({
    isPublished: true,
    isVitalCollectionChild: true,
    canRead: true,
    extends: () => xtremMasterData.nodes.BaseLineDiscountCharge,
})
export class SalesInvoiceLineDiscountCharge extends xtremMasterData.nodes.BaseLineDiscountCharge {
    @decorators.referenceProperty<SalesInvoiceLineDiscountCharge, 'document'>({
        isStored: true,
        isPublished: true,
        isVitalParent: true,
        node: () => xtremSales.nodes.SalesInvoiceLine,
    })
    override readonly document: Reference<xtremSales.nodes.SalesInvoiceLine>;

    @decorators.decimalPropertyOverride<SalesInvoiceLineDiscountCharge, 'basis'>({
        dependsOn: [{ document: ['grossPrice'] }],
        async defaultValue() {
            return (await (await this.document).grossPrice) ?? 0;
        },
        updatedValue: useDefaultValue,
    })
    override readonly basis: Promise<decimal>;

    override calculateAmount(): Promise<number> {
        return super.calculateAmount(3);
    }
}
@decorators.subNode<SalesCreditMemoLineDiscountCharge>({
    isPublished: true,
    isVitalCollectionChild: true,
    canRead: true,

    extends: () => xtremMasterData.nodes.BaseLineDiscountCharge,
})
export class SalesCreditMemoLineDiscountCharge extends xtremMasterData.nodes.BaseLineDiscountCharge {
    @decorators.referenceProperty<SalesCreditMemoLineDiscountCharge, 'document'>({
        isStored: true,
        isPublished: true,
        isVitalParent: true,
        node: () => xtremSales.nodes.SalesCreditMemoLine,
    })
    override readonly document: Reference<xtremSales.nodes.SalesCreditMemoLine>;

    @decorators.decimalPropertyOverride<SalesCreditMemoLineDiscountCharge, 'basis'>({
        dependsOn: [{ document: ['grossPrice'] }],
        async defaultValue() {
            return (await (await this.document).grossPrice) ?? 0;
        },
        updatedValue: useDefaultValue,
    })
    override readonly basis: Promise<decimal>;

    override calculateAmount(): Promise<number> {
        return super.calculateAmount(3);
    }
}
