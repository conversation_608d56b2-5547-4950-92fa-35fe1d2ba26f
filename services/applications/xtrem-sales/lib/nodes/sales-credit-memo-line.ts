import type { Collection, Context, Reference, TextStream, decimal, integer } from '@sage/xtrem-core';
import { NodeStatus, date, decorators, useDefaultValue } from '@sage/xtrem-core';
import { Decimal } from '@sage/xtrem-decimal';
import * as xtremFinanceData from '@sage/xtrem-finance-data';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremStockData from '@sage/xtrem-stock-data';
import * as xtremSystem from '@sage/xtrem-system';
import * as xtremTax from '@sage/xtrem-tax';
import { updateCreditMemoLineNotesOnCreation } from '../functions/sales-credit-memo-lib';
import { isPostedErrorClosed } from '../functions/sales-document-common-lib';
import * as xtremSales from '../index';

@decorators.subNode<SalesCreditMemoLine>({
    extends: () => xtremMasterData.nodes.BaseDocumentItemLine,
    isPublished: true,
    isVitalCollectionChild: true,
    canSearch: true,
    canRead: true,

    async saveBegin() {
        if (this.$.status === NodeStatus.modified) {
            if ((await (await this.$.old).quantity) !== (await this.quantity)) {
                await this.updateTempLinesDicts('update');
            }
        }

        await updateCreditMemoLineNotesOnCreation(this);
    },

    async createEnd() {
        await this.updateTempLinesDicts('create');
    },

    async controlDelete(cx): Promise<void> {
        await xtremSales.events.controlDelete.creditMemoLine.checkStatusForDeletion(cx, this);
        await this.updateTempLinesDicts('delete');
    },
    async controlBegin(cx) {
        await xtremSales.events.controlBegin.creditMemoLine.checkHeaderStatus(cx, this);
        await xtremSales.events.controlBegin.creditMemoLine.checkPostedStatus(cx, this);
    },
})
export class SalesCreditMemoLine
    extends xtremMasterData.nodes.BaseDocumentItemLine
    implements xtremFinanceData.interfaces.InvoiceDocumentLine
{
    @decorators.referencePropertyOverride<SalesCreditMemoLine, 'document'>({
        node: () => xtremSales.nodes.SalesCreditMemo,
    })
    override readonly document: Reference<xtremSales.nodes.SalesCreditMemo>;

    @decorators.stringPropertyOverride<SalesCreditMemoLine, 'documentNumber'>({
        async getValue() {
            return (await this.document).number;
        },
    })
    override readonly documentNumber: Promise<string>;

    @decorators.integerPropertyOverride<SalesCreditMemoLine, 'documentId'>({
        async getValue() {
            return (await this.document)._id;
        },
    })
    override readonly documentId: Promise<integer>;

    @decorators.enumProperty<SalesCreditMemoLine, 'originDocumentType'>({
        isPublished: true,
        dataType: () => xtremSales.enums.salesOriginDocumentTypeDataType,
        getValue() {
            return this.origin as unknown as xtremSales.enums.SalesOriginDocumentType;
        },
    })
    readonly originDocumentType: Promise<xtremSales.enums.SalesOriginDocumentType>;

    @decorators.referencePropertyOverride<SalesCreditMemoLine, 'item'>({
        dependsOn: ['origin'],
        filters: {
            control: {
                isSold: true,
                async itemSites() {
                    return { _atLeast: 1, site: await this.providerSite };
                },
                async isStockManaged() {
                    return (await this.origin) === 'direct' ? false : { _in: [true, false] };
                },
            },
        },
        async isFrozen() {
            return (await this.origin) !== 'direct';
        },
    })
    override readonly item: Reference<xtremMasterData.nodes.Item>;

    /** deprecated */
    @decorators.referenceProperty<SalesCreditMemoLine, 'salesSite'>({
        isPublished: true,
        node: () => xtremSystem.nodes.Site,
        getValue() {
            return this.site;
        },
    })
    readonly salesSite: Reference<xtremSystem.nodes.Site>;

    @decorators.referencePropertyOverride<SalesCreditMemoLine, 'site'>({
        filters: { control: { isSales: true } },
    })
    override readonly site: Reference<xtremSystem.nodes.Site>;

    // @TODO DELETE AFTER BASE DOCUMENT REFACTORING
    @decorators.decimalProperty<SalesCreditMemoLine, 'quantityInSalesUnit'>({
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.quantityInSalesUnit,
        getValue() {
            return this.quantity;
        },
    })
    readonly quantityInSalesUnit: Promise<decimal>;

    @decorators.referencePropertyOverride<SalesCreditMemoLine, 'unit'>({
        dependsOn: ['item', { document: ['billToCustomer'] }],
        async defaultValue() {
            return (await xtremMasterData.functions.getSalesUnitDefaultValue(
                this.$.context,
                await this.item,
                (await (await this.document).billToCustomer)._id,
            ))!;
        },
        async control(cx, val) {
            await cx.error
                .if(await val.id)
                .is.not.in(
                    await xtremMasterData.functions.getValidSalesUnitList(
                        this.$.context,
                        await this.item,
                        await (
                            await this.document
                        ).billToCustomer,
                    ),
                );
        },
    })
    override readonly unit: Reference<xtremMasterData.nodes.UnitOfMeasure>;

    // @TODO DELETE AFTER BASE DOCUMENT REFACTORING
    @decorators.referenceProperty<SalesCreditMemoLine, 'salesUnit'>({
        isPublished: true,
        dependsOn: ['unit'],
        node: () => xtremMasterData.nodes.UnitOfMeasure,
        getValue() {
            return this.unit;
        },
    })
    readonly salesUnit: Reference<xtremMasterData.nodes.UnitOfMeasure>;

    @decorators.decimalPropertyOverride<SalesCreditMemoLine, 'unitToStockUnitConversionFactor'>({
        dependsOn: ['item', { document: ['billToCustomer'] }],
        async defaultValue() {
            return xtremMasterData.functions.calculateConversionFactor(
                this.$.context,
                await this.item,
                (await (await this.document).billToCustomer)?._id,
            );
        },
        updatedValue: useDefaultValue,
    })
    override readonly unitToStockUnitConversionFactor: Promise<decimal>;

    // @TODO SHOULD BE REMOVED AFTER BASE DOCUMENT REFACTORING
    @decorators.decimalProperty<SalesCreditMemoLine, 'salesUnitToStockUnitConversionFactor'>({
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.coefficientDataType,
        getValue() {
            return this.unitToStockUnitConversionFactor;
        },
    })
    readonly salesUnitToStockUnitConversionFactor: Promise<decimal>;

    @decorators.decimalPropertyOverride<SalesCreditMemoLine, 'quantityInStockUnit'>({
        dependsOn: ['stockUnit', 'quantity', 'unitToStockUnitConversionFactor'],
        defaultValue() {
            return this.quantityInStockUnitComputed();
        },
        async control(cx, val) {
            await cx.error.if(Number(val)).is.not.equal.to(await this.quantityInStockUnitComputed());
        },
    })
    override readonly quantityInStockUnit: Promise<decimal>;

    @decorators.referencePropertyOverride<SalesCreditMemoLine, 'stockUnit'>({
        async control(cx, val) {
            await cx.error.if(await val.id).is.not.equal.to(await (await (await this.item).stockUnit).id);
        },
    })
    override readonly stockUnit: Reference<xtremMasterData.nodes.UnitOfMeasure>;

    @decorators.decimalProperty<SalesCreditMemoLine, 'grossPrice'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
        dataType: () => xtremMasterData.dataTypes.priceInSalesPrice,
        async control(cx, val) {
            await cx.error.if(val).is.negative();
        },
    })
    readonly grossPrice: Promise<decimal | null>;

    @decorators.decimalProperty<SalesCreditMemoLine, 'discountDeterminated'>({
        excludedFromPayload: true,
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.baseDecimal,
        dependsOn: ['grossPrice', { discountCharges: ['sign', 'basis', 'valueType', 'valueDeterminated'] }],
        getValue() {
            return xtremMasterData.functions.computeDiscountChargeValueDeterminated(this.discountCharges, 'decrease');
        },
        async setValue(val: decimal) {
            await xtremMasterData.functions.setDiscountChargeValueDeterminated(this.discountCharges, 'decrease', val);
        },
    })
    readonly discountDeterminated: Promise<decimal>;

    @decorators.decimalProperty<SalesCreditMemoLine, 'chargeDeterminated'>({
        excludedFromPayload: true,
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.baseDecimal,
        dependsOn: ['grossPrice', { discountCharges: ['sign', 'basis', 'valueType', 'valueDeterminated'] }],
        getValue() {
            return xtremMasterData.functions.computeDiscountChargeValueDeterminated(this.discountCharges, 'increase');
        },
        async setValue(val: decimal) {
            await xtremMasterData.functions.setDiscountChargeValueDeterminated(this.discountCharges, 'increase', val);
        },
    })
    readonly chargeDeterminated: Promise<decimal>;

    @decorators.decimalProperty<SalesCreditMemoLine, 'grossPriceDeterminated'>({
        excludedFromPayload: true,
        isStored: true,
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.priceInSalesPrice,
    })
    readonly grossPriceDeterminated: Promise<decimal>;

    @decorators.collectionProperty<SalesCreditMemoLine, 'discountCharges'>({
        isVital: true,
        isPublished: true,
        reverseReference: 'document',
        node: () => xtremSales.nodes.SalesCreditMemoLineDiscountCharge,
    })
    readonly discountCharges: Collection<xtremSales.nodes.SalesCreditMemoLineDiscountCharge>;

    @decorators.decimalProperty<SalesCreditMemoLine, 'discount'>({
        excludedFromPayload: true,
        isPublished: true,
        isNullable: true,
        dataType: () => xtremMasterData.dataTypes.baseDecimal,
        dependsOn: ['grossPrice', { discountCharges: ['sign', 'basis', 'valueType', 'value'] }],
        getValue() {
            return xtremMasterData.functions.computeDiscountChargeValue(this.discountCharges, 'decrease');
        },
        async setValue(val: decimal) {
            await xtremMasterData.functions.setDiscountChargeValue(
                this.discountCharges,
                'decrease',
                val,
                (await this.grossPrice) ?? 0,
            );
        },
    })
    readonly discount: Promise<decimal | null>;

    @decorators.decimalProperty<SalesCreditMemoLine, 'charge'>({
        excludedFromPayload: true,
        isPublished: true,
        isNullable: true,
        dataType: () => xtremMasterData.dataTypes.baseDecimal,
        dependsOn: ['grossPrice', { discountCharges: ['sign'] }],
        getValue() {
            return xtremMasterData.functions.computeDiscountChargeValue(this.discountCharges, 'increase');
        },
        async setValue(val: decimal) {
            await xtremMasterData.functions.setDiscountChargeValue(
                this.discountCharges,
                'increase',
                val,
                (await this.grossPrice) ?? 0,
            );
        },
    })
    readonly charge: Promise<decimal | null>;

    @decorators.decimalProperty<SalesCreditMemoLine, 'netPrice'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.priceInSalesPrice,
        dependsOn: [
            'grossPrice',
            'quantity',
            { discountCharges: ['sign', 'basis', 'valueType', 'value'] },
            { document: ['currency'] },
        ],
        async defaultValue() {
            return (await this.currency)
                ? xtremMasterData.functions.calculateNetPrice(
                      this.discountCharges,
                      (await this.grossPrice) ?? 0,
                      await this.quantity,
                      await (
                          await (
                              await this.document
                          ).currency
                      ).decimalDigits,
                  )
                : 0;
        },
        updatedValue: useDefaultValue,
    })
    readonly netPrice: Promise<decimal>;

    @decorators.decimalProperty<SalesCreditMemoLine, 'taxableAmount'>({
        isStored: true,
        isPublished: true,
        dependsOn: ['taxes'],
        dataType: () => xtremMasterData.dataTypes.priceInSalesPrice,
        async control(cx, val) {
            await cx.error.if(val).is.negative();
        },
        updatedValue() {
            return this.taxes.sum(tax => tax.taxableAmount);
        },
    })
    readonly taxableAmount: Promise<decimal>;

    @decorators.decimalProperty<SalesCreditMemoLine, 'taxAmount'>({
        isStored: true,
        isPublished: true,
        dependsOn: ['taxes'],
        dataType: () => xtremMasterData.dataTypes.priceInSalesPrice,
        async control(cx, val) {
            await cx.error.if(val).is.negative();
        },
        updatedValue() {
            return this.taxes.sum(tax => tax.taxAmount);
        },
    })
    readonly taxAmount: Promise<decimal>;

    @decorators.decimalProperty<SalesCreditMemoLine, 'exemptAmount'>({
        isStored: true,
        isPublished: true,
        dependsOn: ['taxes'],
        dataType: () => xtremMasterData.dataTypes.priceInSalesPrice,
        async control(cx, val) {
            await cx.error.if(val).is.negative();
        },
        updatedValue() {
            return this.taxes.sum(tax => tax.exemptAmount);
        },
    })
    readonly exemptAmount: Promise<decimal>;

    @decorators.dateProperty<SalesCreditMemoLine, 'taxDate'>({
        isStoredOutput: true,
        isPublished: true,
        dependsOn: [{ document: ['date'] }],
        async defaultValue() {
            return (await this.document).date;
        },
        updatedValue: useDefaultValue,
    })
    readonly taxDate: Promise<date>;

    @decorators.decimalProperty<SalesCreditMemoLine, 'netPriceExcludingTax'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.priceInSalesPrice,
        async control(cx, val) {
            await cx.error.if(val).is.negative();
        },
        dependsOn: [
            'grossPrice',
            'quantity',
            { discountCharges: ['sign', 'basis', 'valueType', 'value'] },
            { document: ['currency'] },
        ],
        async defaultValue() {
            return (await this.currency)
                ? xtremMasterData.functions.calculateNetPrice(
                      this.discountCharges,
                      (await this.grossPrice) ?? 0,
                      await this.quantity,
                      await (
                          await (
                              await this.document
                          ).currency
                      ).decimalDigits,
                  )
                : 0;
        },
        updatedValue: useDefaultValue,
    })
    readonly netPriceExcludingTax: Promise<decimal>;

    @decorators.decimalProperty<SalesCreditMemoLine, 'netPriceIncludingTax'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.priceInSalesPrice,
        async control(cx, val) {
            await cx.error.if(val).is.negative();
        },
        dependsOn: ['netPriceExcludingTax', 'taxAmount', 'quantity'],
        async defaultValue() {
            return (await this.netPriceExcludingTax) + (await this.taxAmount) / (await this.quantity);
        },
        updatedValue: useDefaultValue,
    })
    readonly netPriceIncludingTax: Promise<decimal>;

    @decorators.decimalProperty<SalesCreditMemoLine, 'amountIncludingTax'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.priceInSalesPrice,
        dependsOn: ['amountExcludingTax', 'taxAmountAdjusted'],
        async defaultValue() {
            return (await this.amountExcludingTax) + (await this.taxAmountAdjusted);
        },
        updatedValue: useDefaultValue,
    })
    readonly amountIncludingTax: Promise<decimal>;

    @decorators.decimalProperty<SalesCreditMemoLine, 'amountIncludingTaxInCompanyCurrency'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremSales.dataTypes.salesPriceDataType,
        async control(cx, val) {
            await cx.error.if(val).is.negative();
        },
        dependsOn: [
            'quantity',
            'netPrice',
            'amountIncludingTax',
            { document: ['site', 'currency', 'companyFxRateDivisor', 'companyFxRate'] },
        ],
        async defaultValue() {
            const document = await this.document;
            // convert transaction amount into company currency
            return xtremMasterData.sharedFunctions.convertAmount(
                await this.amountIncludingTax,
                await document.companyFxRate,
                await document.companyFxRateDivisor,
                await (
                    await document.currency
                ).decimalDigits,
                await (
                    await (
                        await (
                            await document.site
                        ).legalCompany
                    ).currency
                ).decimalDigits,
            );
        },
        updatedValue: useDefaultValue,
    })
    readonly amountIncludingTaxInCompanyCurrency: Promise<decimal>;

    @decorators.decimalProperty<SalesCreditMemoLine, 'taxAmountAdjusted'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.priceDataType,
        dependsOn: ['taxes'],
        async control(cx, val) {
            await cx.error.if(val).is.negative();
        },
        updatedValue() {
            return this.taxes.sum(tax => tax.taxAmountAdjusted);
        },
    })
    readonly taxAmountAdjusted: Promise<decimal>;

    @decorators.decimalProperty<SalesCreditMemoLine, 'amountExcludingTax'>({
        isStoredOutput: true,
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.priceInSalesPrice,
        dependsOn: ['quantity', 'netPriceExcludingTax'],
        async defaultValue() {
            return (await this.quantity) * (await this.netPriceExcludingTax);
        },
        updatedValue: useDefaultValue,
    })
    readonly amountExcludingTax: Promise<decimal>;

    @decorators.decimalProperty<SalesCreditMemoLine, 'amountExcludingTaxInCompanyCurrency'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremSales.dataTypes.salesPriceDataType,
        async control(cx, val) {
            await cx.error.if(val).is.negative();
        },
        dependsOn: [
            'quantity',
            'netPrice',
            'amountExcludingTax',
            { document: ['site', 'currency', 'companyFxRateDivisor', 'companyFxRate'] },
        ],
        async defaultValue() {
            const document = await this.document;
            // convert transaction amount into company currency
            return xtremMasterData.sharedFunctions.convertAmount(
                await this.amountExcludingTax,
                await document.companyFxRate,
                await document.companyFxRateDivisor,
                await (
                    await document.currency
                ).decimalDigits,
                await (
                    await (
                        await (
                            await document.site
                        ).legalCompany
                    ).currency
                ).decimalDigits,
            );
        },
        updatedValue: useDefaultValue,
    })
    readonly amountExcludingTaxInCompanyCurrency: Promise<decimal>;

    @decorators.decimalProperty<SalesCreditMemoLine, 'stockCostAmountInCompanyCurrency'>({
        isStoredOutput: true,
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.amountInCompanyCurrency,
        dependsOn: ['quantityInStockUnit', 'item', 'site', 'netPrice', 'origin', 'toInvoiceLines'],
        defaultValue() {
            return this.getNewStockCostAmount();
        },
        updatedValue: useDefaultValue,
    })
    readonly stockCostAmountInCompanyCurrency: Promise<decimal>;

    @decorators.decimalProperty<SalesCreditMemoLine, 'grossProfitAmountInCompanyCurrency'>({
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.amountInCompanyCurrency,
        dependsOn: ['amountExcludingTaxInCompanyCurrency', 'stockCostAmountInCompanyCurrency'],
        async getValue() {
            return (await this.amountExcludingTaxInCompanyCurrency) - (await this.stockCostAmountInCompanyCurrency);
        },
    })
    readonly grossProfitAmountInCompanyCurrency: Promise<decimal>;

    @decorators.decimalProperty<SalesCreditMemoLine, 'stockCostAmount'>({
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.amountInTransactionCurrency,
        dependsOn: ['stockCostAmountInCompanyCurrency'],
        async getValue() {
            const document = await this.document;
            // convert from company currency to transaction currency
            return xtremMasterData.sharedFunctions.convertAmount(
                await this.stockCostAmountInCompanyCurrency,
                await document.companyFxRateDivisor,
                await document.companyFxRate,
                await (
                    await (
                        await (
                            await document.site
                        ).legalCompany
                    ).currency
                ).decimalDigits,
                await (
                    await document.currency
                ).decimalDigits,
            );
        },
    })
    readonly stockCostAmount: Promise<decimal>;

    @decorators.decimalProperty<SalesCreditMemoLine, 'grossProfitAmount'>({
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.amountInTransactionCurrency,
        dependsOn: ['amountExcludingTax', 'stockCostAmount'],
        async getValue() {
            return (await this.amountExcludingTax) - (await this.stockCostAmount);
        },
    })
    readonly grossProfitAmount: Promise<decimal>;

    @decorators.decimalProperty<SalesCreditMemoLine, 'stockCostUnit'>({
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.orderCostDataType,
        dependsOn: ['quantity', 'stockCostAmount'],
        // eslint-disable-next-line @sage/xtrem/property-decorators-errors
        async computeValue() {
            const quantity = await this.quantity;
            return quantity ? (await this.stockCostAmount) / quantity : 0;
        },
    })
    readonly stockCostUnit: Promise<decimal>;

    @decorators.decimalProperty<SalesCreditMemoLine, 'grossProfit'>({
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.priceDataType,
        dependsOn: ['netPrice', 'stockCostUnit'],
        // eslint-disable-next-line @sage/xtrem/property-decorators-errors
        async computeValue() {
            return (await this.netPrice) - (await this.stockCostUnit);
        },
    })
    readonly grossProfit: Promise<decimal>;

    @decorators.collectionProperty<SalesCreditMemoLine, 'toInvoiceLines'>({
        isPublished: true,
        node: () => xtremSales.nodes.SalesInvoiceLineToSalesCreditMemoLine,
        isVital: true,
        reverseReference: 'document',
        dependsOn: ['quantity'],
    })
    readonly toInvoiceLines: Collection<xtremSales.nodes.SalesInvoiceLineToSalesCreditMemoLine>;

    @decorators.collectionProperty<SalesCreditMemoLine, 'toReturnRequestLines'>({
        isPublished: true,
        node: () => xtremSales.nodes.SalesReturnRequestLineSalesCreditMemoLine,
        isVital: true,
        reverseReference: 'document',
        dependsOn: ['quantity'],
    })
    readonly toReturnRequestLines: Collection<xtremSales.nodes.SalesReturnRequestLineSalesCreditMemoLine>;

    @decorators.stringProperty<SalesCreditMemoLine, 'sourceDocumentNumber'>({
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.documentNumber,
        async computeValue() {
            if ((await this.origin) === 'invoice') {
                return (await this.toInvoiceLines.length)
                    ? (await (await (await this.toInvoiceLines.elementAt(0)).linkedDocument).document).number
                    : '';
            }
            return (await this.origin) === 'return' && (await this.toReturnRequestLines.length)
                ? (await (await (await this.toReturnRequestLines.elementAt(0)).linkedDocument).document).number
                : '';
        },
    })
    readonly sourceDocumentNumber: Promise<string>;

    // needed for the accounting engine
    @decorators.integerProperty<SalesCreditMemoLine, 'sourceDocumentSysId'>({
        isPublished: true,
        async computeValue() {
            if ((await this.origin) === 'invoice') {
                return (await this.toInvoiceLines.length)
                    ? (await (await (await this.toInvoiceLines.elementAt(0)).linkedDocument).document)._id
                    : 0;
            }
            return (await this.origin) === 'return' && (await this.toReturnRequestLines.length)
                ? (await (await (await this.toReturnRequestLines.elementAt(0)).linkedDocument).document)._id
                : 0;
        },
    })
    readonly sourceDocumentSysId: Promise<integer>;

    // needed for the accounting engine
    @decorators.enumProperty<SalesCreditMemoLine, 'sourceDocumentType'>({
        isPublished: true,
        isNullable: true,
        dataType: () => xtremFinanceData.enums.sourceDocumentTypeDataType,
        async getValue() {
            if ((await this.origin) === 'invoice') {
                return 'salesInvoice';
            }
            return 'salesReturnRequest';
        },
    })
    readonly sourceDocumentType: Promise<xtremFinanceData.enums.SourceDocumentType | null>;

    @decorators.referenceProperty<SalesCreditMemoLine, 'consumptionLinkedAddress'>({
        isStored: true,
        isPublished: true,
        isRequired: true,
        dependsOn: [{ document: ['billToLinkedAddress'] }],
        node: () => xtremMasterData.nodes.BusinessEntityAddress,
        async defaultValue() {
            return (await this.document).billToLinkedAddress;
        },
    })
    readonly consumptionLinkedAddress: Reference<xtremMasterData.nodes.BusinessEntityAddress>;

    @decorators.referenceProperty<SalesCreditMemoLine, 'consumptionAddress'>({
        isPublished: true,
        isStored: true,
        isMutable: true,
        isNullable: true,
        dependsOn: ['consumptionLinkedAddress'],
        node: () => xtremMasterData.nodes.Address,

        async defaultValue() {
            const document = await this.document;
            const billToLinkedAddress = await document?.billToLinkedAddress;
            const consumptionLinkedAddress = await this.consumptionLinkedAddress;
            if ((await document?.billToLinkedAddress) === consumptionLinkedAddress && billToLinkedAddress) {
                return xtremSales.functions.getSalesAddressDetail(billToLinkedAddress);
            }
            return xtremSales.functions.getSalesAddressDetail(consumptionLinkedAddress);
        },
        updatedValue: useDefaultValue,
    })
    readonly consumptionAddress: Reference<xtremMasterData.nodes.Address | null>;

    @decorators.referenceProperty<SalesCreditMemoLine, 'providerSite'>({
        isStored: true,
        isPublished: true,
        isRequired: true,
        node: () => xtremSystem.nodes.Site,
        dependsOn: [{ site: ['legalCompany'] }, { document: ['site'] }, 'item'],
        filters: {
            control: {
                isSales: true,
                async legalCompany() {
                    return (await this.site).legalCompany;
                },
                async itemSites() {
                    return { _atLeast: 1, item: await this.item };
                },
            },
        },
        async defaultValue() {
            return (await this.document).site;
        },
        async isFrozen() {
            return (await this.origin) !== 'direct';
        },
    })
    readonly providerSite: Reference<xtremSystem.nodes.Site>;

    @decorators.referenceProperty<SalesCreditMemoLine, 'providerSiteLinkedAddress'>({
        isStored: true,
        isPublished: true,
        isRequired: true,
        dependsOn: ['providerSite'],
        node: () => xtremMasterData.nodes.BusinessEntityAddress,
        async defaultValue() {
            return (await this.providerSite).primaryAddress;
        },
    })
    readonly providerSiteLinkedAddress: Reference<xtremMasterData.nodes.BusinessEntityAddress>;

    @decorators.referenceProperty<SalesCreditMemoLine, 'providerSiteAddress'>({
        isPublished: true,
        isStored: true,
        isMutable: true,
        isNullable: true,
        dependsOn: ['providerSiteLinkedAddress'],
        node: () => xtremMasterData.nodes.Address,

        async defaultValue() {
            const document = await this.document;
            const salesSiteLinkedAddress = await document?.billToLinkedAddress;
            const providerSiteLinkedAddress = await this.providerSiteLinkedAddress;
            if ((await document?.salesSiteLinkedAddress) === providerSiteLinkedAddress && salesSiteLinkedAddress) {
                return xtremSales.functions.getSalesAddressDetail(salesSiteLinkedAddress);
            }
            return xtremSales.functions.getSalesAddressDetail(providerSiteLinkedAddress);
        },
        updatedValue: useDefaultValue,
    })
    readonly providerSiteAddress: Reference<xtremMasterData.nodes.Address | null>;

    @decorators.jsonPropertyOverride<SalesCreditMemoLine, 'computedAttributes'>({
        dependsOn: ['item', { document: ['site', 'billToCustomer'] }, 'providerSite'],
        async computeValue() {
            return xtremSales.functions.computeAttributes(
                this.$.context,
                await this.site,
                await this.providerSite,
                await this.item,
                await (
                    await this.document
                ).billToCustomer,
            );
        },
    })
    override readonly computedAttributes: Promise<object>;

    @decorators.referenceProperty<SalesCreditMemoLine, 'currency'>({
        isPublished: false,
        node: () => xtremMasterData.nodes.Currency,
        dependsOn: [{ document: ['currency'] }],
        async getValue() {
            return (await this.document).currency;
        },
    })
    readonly currency: Reference<xtremMasterData.nodes.Currency>;

    @decorators.enumProperty<SalesCreditMemoLine, 'priceOrigin'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
        dataType: () => xtremSales.enums.salesPriceOriginDataType,
    })
    readonly priceOrigin: Promise<xtremSales.enums.SalesPriceOrigin | null>;

    @decorators.referenceProperty<SalesCreditMemoLine, 'priceReason'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
        node: () => xtremMasterData.nodes.CustomerPriceReason,
    })
    readonly priceReason: Reference<xtremMasterData.nodes.CustomerPriceReason | null>;

    @decorators.enumProperty<SalesCreditMemoLine, 'priceOriginDeterminated'>({
        excludedFromPayload: true,
        isStored: true,
        isPublished: true,
        isNullable: true,
        dataType: () => xtremSales.enums.salesPriceOriginDataType,
        dependsOn: ['priceOrigin'],
        defaultValue() {
            return this.priceOrigin;
        },
    })
    readonly priceOriginDeterminated: Promise<xtremSales.enums.SalesPriceOrigin | null>;

    @decorators.referenceProperty<SalesCreditMemoLine, 'priceReasonDeterminated'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
        node: () => xtremMasterData.nodes.CustomerPriceReason,
        dependsOn: ['priceReason'],
        defaultValue() {
            return this.priceReason;
        },
    })
    readonly priceReasonDeterminated: Reference<xtremMasterData.nodes.CustomerPriceReason | null>;

    @decorators.booleanProperty<SalesCreditMemoLine, 'isPriceDeterminated'>({
        excludedFromPayload: true,
        isStored: true,
        isPublished: true,
        defaultValue() {
            return false;
        },
    })
    readonly isPriceDeterminated: Promise<boolean>;

    @decorators.collectionProperty<SalesCreditMemoLine, 'taxes'>({
        isVital: true,
        isPublished: true,
        reverseReference: 'document',
        dependsOn: ['currency', { document: ['status'] }],
        async isFrozen() {
            const document = await this.document;
            return (
                (await document.status) !== 'draft' &&
                (await (await (await this.salesSite).legalCompany).taxEngine) !== 'avalaraAvaTax' &&
                !(await document.forceUpdateForFinance)
            );
        },
        node: () => xtremSales.nodes.SalesCreditMemoLineTax,
    })
    readonly taxes: Collection<xtremSales.nodes.SalesCreditMemoLineTax>;

    @decorators.jsonProperty<SalesCreditMemoLine, 'uiTaxes'>({
        excludedFromPayload: true,
        isPublished: true,
        dependsOn: ['taxes'],
        async computeValue() {
            return xtremTax.functions.prepareTaxesToUiTaxes<xtremSales.nodes.SalesCreditMemoLine>(
                this,
                String(await (await this.document).taxEngine),
            );
        },
        async setValue(val: xtremTax.interfaces.UiTaxes | null) {
            await xtremTax.functions.updateTaxesFromUiTaxes(val, this, String(await (await this.document).taxEngine));
        },
    })
    readonly uiTaxes: Promise<xtremTax.interfaces.UiTaxes | null>;

    @decorators.enumProperty<SalesCreditMemoLine, 'taxCalculationStatus'>({
        isPublished: true,
        dataType: () => xtremMasterData.enums.taxCalculationStatusDataType,
        dependsOn: ['taxes', { document: ['taxCalculationStatus'] }],
        async getValue() {
            if (
                (await (await this.document).taxCalculationStatus) === 'failed' &&
                (await this.taxes.some(async tax => (await tax.isTaxMandatory) && !(await tax.taxReference)))
            ) {
                return 'failed';
            }
            if ((await (await this.document).taxCalculationStatus) === 'notDone') {
                return 'notDone';
            }
            return 'done';
        },
    })
    readonly taxCalculationStatus: Promise<xtremMasterData.enums.TaxCalculationStatus>;

    @decorators.textStreamPropertyOverride<SalesCreditMemoLine, 'internalNote'>({
        async isFrozen() {
            return isPostedErrorClosed(await (await this.document).status);
        },
    })
    override readonly internalNote: Promise<TextStream>;

    @decorators.textStreamPropertyOverride<SalesCreditMemoLine, 'externalNote'>({
        async isFrozen() {
            return isPostedErrorClosed(await (await this.document).status);
        },
    })
    override readonly externalNote: Promise<TextStream>;

    /** to be sure the user know that externalNode will go to external documents */
    @decorators.booleanPropertyOverride<SalesCreditMemoLine, 'isExternalNote'>({
        async isFrozen() {
            return isPostedErrorClosed(await (await this.document).status);
        },
    })
    override readonly isExternalNote: Promise<boolean>;

    async updateTempLinesDicts(action: string): Promise<void> {
        await xtremSales.classes.SalesCreditMemosCreator.fillTempSalesDocumentDicts(
            action,
            'toInvoiceLines',
            '__salesInvoices',
            this,
        );

        await xtremSales.classes.SalesCreditMemosCreator.fillTempSalesDocumentDicts(
            action,
            'toReturnRequestLines',
            '__salesReturnRequests',
            this,
        );
    }

    async getNewStockCostAmount(): Promise<decimal> {
        // The cost comes from the invoice line if the origin is an invoice
        if (['invoice', 'return'].includes(await this.origin) && (await this.toInvoiceLines.length)) {
            const invoiceLine = await (await this.toInvoiceLines.elementAt(0)).linkedDocument;
            return (
                ((await invoiceLine.stockCostAmountInCompanyCurrency) * (await this.quantityInStockUnit)) /
                (await invoiceLine.quantityInStockUnit)
            );
        }
        // In all other cases we have to calculate the stock cost
        return (
            await xtremStockData.nodeExtensions.ItemSiteExtension.getValuationCost(
                this.$.context,
                await this.item,
                await this.site,
                { quantity: await this.quantityInStockUnit, dateOfValuation: date.today(), valuationType: 'issue' },
            )
        ).amount;
    }

    /**
     * get the tax determination for a sales credit memo line
     * @param context
     * @param creditMemoLine
     * @returns object
     */
    @decorators.mutation<typeof SalesCreditMemoLine, 'getTaxDetermination'>({
        isPublished: true,
        parameters: [
            {
                name: 'creditMemoLine',
                type: 'reference',
                isMandatory: true,
                isWritable: true,
                node: () => SalesCreditMemoLine,
            },
        ],
        return: {
            type: 'array',
            item: {
                type: 'object',
                properties: {
                    taxCategory: {
                        type: 'reference',
                        // isMandatory: true,
                        node: () => xtremTax.nodes.TaxCategory,
                    },
                    tax: {
                        type: 'reference',
                        // isMandatory: true,
                        node: () => xtremTax.nodes.Tax,
                    },
                    isReverseCharge: 'boolean',
                    rate: 'decimal',
                    deductibleRate: 'decimal',
                },
            },
        },
    })
    static async getTaxDetermination(
        context: Context,
        creditMemoLine: SalesCreditMemoLine,
    ): Promise<xtremTax.interfaces.TaxDeterminationResult[]> {
        return (
            await xtremSales.classes.SalesTaxCalculator.create(context, await creditMemoLine.site)
        ).getTaxDetermination(creditMemoLine);
    }

    async quantityInStockUnitComputed(): Promise<number> {
        return +new Decimal((await this.quantity) * (await this.unitToStockUnitConversionFactor)).toDecimalPlaces(
            (await (await this.stockUnit)?.decimalDigits) || 0,
        );
    }

    /**
     * Calculates sales credit memo line taxes. Mutation can be used by the frontend
     * during creation or update to show taxes to the user before he creates or updates credit memo
     * @param context
     * @param site universal name because the same parameter name will be used in purchases.
     *             This mutation name will be passed as a parameter into the tax-panel page
     * @param businessPartner universal name
     * @param item
     * @param currency
     * @param amountExcludingTax
     * @param quantity
     * @param taxes an array of objects with optional parameter taxReference.
     *              If the array is left empty, mutation tries to automatically determine tax determination.
     *
     * @returns object
     */
    @decorators.mutation<typeof SalesCreditMemoLine, 'calculateLineTaxes'>({
        // TODO change mutation to query when enhancement XT-19221 is done.
        isPublished: true,
        parameters: [
            {
                name: 'data',
                type: 'object',
                properties: xtremTax.functions.getCalculateLineTaxesParameterPropertyDefinition<
                    typeof SalesCreditMemoLine
                >(xtremMasterData.nodes.Customer),
            },
        ],
        return: xtremTax.functions.getCalculateLineTaxesReturnDefinition<typeof SalesCreditMemoLine>(),
    })
    static async calculateLineTaxes(
        context: Context,
        data: xtremTax.interfaces.CalculateLineTaxesParameters<xtremMasterData.nodes.Customer>,
    ): Promise<xtremTax.interfaces.CalculateLineTaxesReturn> {
        return xtremTax.functions.calculateLineTaxes(
            await context.create(
                xtremSales.nodes.SalesCreditMemo,
                { site: data.site, billToCustomer: data.businessPartner },
                { isOnlyForDefaultValues: true },
            ),
            await xtremSales.classes.SalesTaxCalculator.create(context, data.site),
            data,
        );
    }
}
