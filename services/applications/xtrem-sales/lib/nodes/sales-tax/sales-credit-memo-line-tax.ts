import type { Reference } from '@sage/xtrem-core';
import { decorators } from '@sage/xtrem-core';
import type * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremTax from '@sage/xtrem-tax';
import * as xtremSales from '../../index';

@decorators.subNode<SalesCreditMemoLineTax>({
    extends: () => xtremTax.nodes.BaseLineTax,
    isPublished: true,
    isVitalCollectionChild: true,
    canSearch: true,
    canRead: true,
})
export class SalesCreditMemoLineTax
    extends xtremTax.nodes.BaseLineTax
    implements xtremMasterData.interfaces.FinancialSubNode
{
    @decorators.referenceProperty<SalesCreditMemoLineTax, 'document'>({
        isStored: true,
        isPublished: true,
        isVitalParent: true,
        node: () => xtremSales.nodes.SalesCreditMemoLine,
    })
    readonly document: Reference<xtremSales.nodes.SalesCreditMemoLine>;

    @decorators.referencePropertyOverride<SalesCreditMemoLineTax, 'currency'>({
        dependsOn: [{ document: ['currency'] }],
        async getValue() {
            return (await this.document).currency;
        },
    })
    override readonly currency: Reference<xtremMasterData.nodes.Currency>;
}
