import type { Reference } from '@sage/xtrem-core';
import { decorators } from '@sage/xtrem-core';
import type * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremTax from '@sage/xtrem-tax';
import * as xtremSales from '../../index';

@decorators.subNode<SalesCreditMemoTax>({
    extends: () => xtremTax.nodes.BaseTax,
    isPublished: true,
    isVitalCollectionChild: true,
    canSearch: true,
    canRead: true,
})
export class SalesCreditMemoTax extends xtremTax.nodes.BaseTax implements xtremMasterData.interfaces.FinancialSubNode {
    @decorators.referenceProperty<SalesCreditMemoTax, 'document'>({
        isStored: true,
        isPublished: true,
        isVitalParent: true,
        node: () => xtremSales.nodes.SalesCreditMemo,
    })
    readonly document: Reference<xtremSales.nodes.SalesCreditMemo>;

    @decorators.referencePropertyOverride<SalesCreditMemoTax, 'currency'>({
        dependsOn: [{ document: ['currency'] }],
        async getValue() {
            return (await this.document).currency;
        },
    })
    override readonly currency: Reference<xtremMasterData.nodes.Currency>;
}
