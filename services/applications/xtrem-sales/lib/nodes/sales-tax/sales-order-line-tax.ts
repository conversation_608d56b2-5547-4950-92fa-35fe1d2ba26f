import type { Reference } from '@sage/xtrem-core';
import { decorators } from '@sage/xtrem-core';
import type * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremTax from '@sage/xtrem-tax';
import * as xtremSales from '../../index';

@decorators.subNode<SalesOrderLineTax>({
    extends: () => xtremTax.nodes.BaseLineTax,
    isPublished: true,
    isVitalCollectionChild: true,
    canSearch: true,
    canRead: true,
})
export class SalesOrderLineTax
    extends xtremTax.nodes.BaseLineTax
    implements xtremMasterData.interfaces.FinancialSubNode
{
    @decorators.referenceProperty<SalesOrderLineTax, 'document'>({
        isStored: true,
        isPublished: true,
        isVitalParent: true,
        node: () => xtremSales.nodes.SalesOrderLine,
    })
    readonly document: Reference<xtremSales.nodes.SalesOrderLine>;

    @decorators.referencePropertyOverride<SalesOrderLineTax, 'currency'>({
        dependsOn: [{ document: ['currency'] }],
        async getValue() {
            return (await this.document).currency;
        },
    })
    override readonly currency: Reference<xtremMasterData.nodes.Currency>;
}
