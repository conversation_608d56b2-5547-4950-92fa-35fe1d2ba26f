import type { Reference } from '@sage/xtrem-core';
import { decorators } from '@sage/xtrem-core';
import type * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremTax from '@sage/xtrem-tax';
import * as xtremSales from '../../index';

@decorators.subNode<SalesOrderTax>({
    extends: () => xtremTax.nodes.BaseTax,
    isPublished: true,
    isVitalCollectionChild: true,
    canSearch: true,
    canRead: true,
})
export class SalesOrderTax extends xtremTax.nodes.BaseTax implements xtremMasterData.interfaces.FinancialSubNode {
    @decorators.referenceProperty<SalesOrderTax, 'document'>({
        isStored: true,
        isPublished: true,
        isVitalParent: true,
        node: () => xtremSales.nodes.SalesOrder,
    })
    readonly document: Reference<xtremSales.nodes.SalesOrder>;

    @decorators.referencePropertyOverride<SalesOrderTax, 'currency'>({
        dependsOn: [{ document: ['currency'] }],
        async getValue() {
            return (await this.document).currency;
        },
    })
    override readonly currency: Reference<xtremMasterData.nodes.Currency>;
}
