import { decorators, Node } from '@sage/xtrem-core';
import { dataTypes, functions } from '@sage/xtrem-system';

@decorators.node<SalesReturnRequestReason>({
    package: 'xtrem-sales',
    storage: 'sql',
    isPublished: true,
    canRead: true,
    canDelete: true,
    canCreate: true,
    canUpdate: true,
    canDuplicate: true,
    indexes: [
        {
            orderBy: { id: +1 },
            isUnique: true,
            isNaturalKey: true,
        },
    ],
    isSetupNode: true,
})
export class SalesReturnRequestReason extends Node {
    @decorators.booleanProperty<SalesReturnRequestReason, 'isActive'>({
        isPublished: true,
        isStored: true,
        defaultValue() {
            return true;
        },
        provides: ['isActive'],
        isOwnedByCustomer: true,
        duplicateRequiresPrompt: true,
        lookupAccess: true,
    })
    readonly isActive: Promise<boolean>;

    @decorators.stringProperty<SalesReturnRequestReason, 'id'>({
        isStored: true,
        isPublished: true,
        isNotEmpty: true,
        isFrozen: true,
        dataType: () => dataTypes.setupIdDataType,
        defaultValue() {
            return functions.generateNanoId();
        },
        lookupAccess: true,
        duplicateRequiresPrompt: true,
        duplicatedValue: '',
    })
    readonly id: Promise<string>;

    @decorators.stringProperty<SalesReturnRequestReason, 'name'>({
        isStored: true,
        isPublished: true,
        isNotEmpty: true,
        dataType: () => dataTypes.localizedName,
        lookupAccess: true,
        duplicateRequiresPrompt: true,
    })
    readonly name: Promise<string>;

    @decorators.stringProperty<SalesReturnRequestReason, 'description'>({
        isStored: true,
        isPublished: true,
        dataType: () => dataTypes.localizedDescription,
        lookupAccess: true,
        duplicateRequiresPrompt: true,
    })
    readonly description: Promise<string>;
}
