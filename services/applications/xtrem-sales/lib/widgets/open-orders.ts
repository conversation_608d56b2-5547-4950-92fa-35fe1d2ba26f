import type { Filter, OrderBy } from '@sage/xtrem-client';
import type { SalesOrder } from '@sage/xtrem-sales-api';
import * as ui from '@sage/xtrem-ui';

@ui.widgets.table<OpenOrders>({
    title: 'Orders to deliver',
    cacheLifespan: ui.widgets.WidgetCacheLifespan.day,
    content() {
        if (!this.$.data?.xtremSales?.salesOrder?.query?.edges) {
            return [];
        }
        return this.$.data.xtremSales.salesOrder.query.edges.map(({ node, cursor }: any) => ({
            _id: node._id,
            title: node.number,
            titleRight: ui.formatDateToCurrentLocale(node.date),
            line2: `${node.currency.symbol} ${
                Math.round(+node.totalAmountExcludingTax * 10 ** node.currency.decimalDigits) /
                10 ** node.currency.decimalDigits
            }`,
            line2Right: ui.localizeEnumMember('@sage/xtrem-sales/SalesOrderStatus', node.status),
            line3: node.soldToCustomer.name,
            cursor,
        }));
    },
    callToActions: {
        Ship: {
            title: 'Ship',
            isDisabled() {
                return (this.$.options.selectedItems ?? []).length === 0 || this.onlyDraftSelected();
            },
            onClick() {
                const salesOrders = this.$.options.selectedItems?.filter((id: any) => {
                    const salesOrder = this.getDataFromLineIndex(id);

                    if (salesOrder.status !== 'quote' && !salesOrder.isOnHold) {
                        return id;
                    }
                    return '';
                });
                // Need to check if we have orders from different companies
                const singleCompany = this.singleCompanyOrders();
                if (salesOrders?.length && singleCompany) {
                    const filteredOrders = salesOrders.filter(id => id !== '');
                    this.$.router.goTo('@sage/xtrem-sales/SalesOrderShippingMassProcess', {
                        createShipments: 'Y',
                        salesCompany: JSON.stringify(this.getSalesCompany(filteredOrders)._id),
                        salesOrders: JSON.stringify(filteredOrders),
                    });
                } else {
                    this.$.showToast(
                        salesOrders?.length === 0
                            ? ui.localize(
                                  '@sage/xtrem-sales/widgets__open_orders__create_shipment_no_shippable_orders',
                                  'Selected orders cannot be shipped.',
                              )
                            : ui.localize(
                                  '@sage/xtrem-sales/widgets__open_orders__create_shipment_multiple_companies',
                                  'Selected orders must come from a single sales company.',
                              ),
                        { type: 'error' },
                    );
                }
            },
        },
    },
    canSwitchViewMode: true,
    displayMode: 'card',
    canSelect: true,
    dataDropdownMenu: {
        orderBy: {
            number: { title: 'Sort by number' },
            date: { title: 'Sort by order date' },
            status: { title: 'Sort by status' },
        },
    },
    rowDefinition: {
        title: {
            title: 'Number',
            renderedAs: 'link',
            onClick(_id: string) {
                this.$.router.goTo('@sage/xtrem-sales/SalesOrder', { _id });
            },
        },
        titleRight: { title: 'Order date' },
        line2: {
            title: 'Total amount',
        },
        line2Right: {
            title: 'Status',
            renderedAs: 'pill',
            displayOptions: {
                colorVariant(_id: string) {
                    const row = this.getDataFromLineIndex(_id);

                    if (row) {
                        if (row.status === 'closed') {
                            return 'negative';
                        }
                        if (row.status === 'pending') {
                            return 'warning';
                        }
                        if (row.status === 'inProgress') {
                            return 'positive';
                        }
                    }
                    return 'neutral';
                },
            },
        },
        line3: {
            title: 'Customer name',
        },
    },
    getQuery(args) {
        const filter: Filter<SalesOrder> = { status: { _nin: ['closed', 'quote'] } };
        const orderBy: OrderBy<SalesOrder> = {};
        const { dataOptions } = this.$.options;

        if (!dataOptions?.orderBy || dataOptions?.orderBy === 'number') {
            orderBy.number = 1;
        }

        if (dataOptions?.orderBy === 'date') {
            orderBy.date = 1;
        }

        if (dataOptions?.orderBy === 'status') {
            orderBy.status = 1;
        }

        return {
            xtremSales: {
                salesOrder: {
                    query: {
                        __args: {
                            filter: JSON.stringify(filter),
                            orderBy: JSON.stringify(orderBy),
                            ...(args?.after?.cursor && { after: args.after.cursor }),
                        },
                        edges: {
                            cursor: true,
                            node: {
                                _id: true,
                                number: true,
                                currency: {
                                    symbol: true,
                                    decimalDigits: true,
                                },
                                totalAmountExcludingTax: true,
                                status: true,
                                date: true,
                                isOnHold: true,
                                soldToCustomer: {
                                    name: true,
                                },
                                site: {
                                    legalCompany: {
                                        _id: true,
                                        name: true,
                                        id: true,
                                        description: true,
                                        currency: {
                                            _id: true,
                                            id: true,
                                            name: true,
                                            symbol: true,
                                            decimalDigits: true,
                                            rounding: true,
                                        },
                                    },
                                },
                            },
                        },
                    },
                },
            },
        };
    },
})
export class OpenOrders extends ui.widgets.TableWidget {
    private onlyDraftSelected() {
        return !this.$.options.selectedItems?.some((id: string) => {
            return this.getDataFromLineIndex(id).status !== 'quote';
        });
    }

    private singleCompanyOrders() {
        const selection = this.$.options.selectedItems;
        if (selection !== undefined) {
            const firstCompany = this.getDataFromLineIndex(selection[0]).site.legalCompany.id;
            return selection.every((id: string) => this.getDataFromLineIndex(id).site.legalCompany.id === firstCompany);
        }
        return false;
    }

    private getSalesCompany(filteredOrders: Array<string>) {
        return this.getDataFromLineIndex(filteredOrders[0]).site.legalCompany;
    }

    private getDataFromLineIndex(_id: string): any {
        return this.$.data.xtremSales.salesOrder.query.edges.find((edge: any) => edge.node._id === _id).node;
    }
}
