import * as ui from '@sage/xtrem-ui';

@ui.widgets.indicatorTile<YtdSales>({
    title: 'YTD sales',
    cacheLifespan: ui.widgets.WidgetCacheLifespan.day,
    settingsPage: '@sage/xtrem-sales/SalesCurrencySettings',
    color() {
        return this.$.data.xtremSales.salesInvoice.getYtdSales > 0
            ? ui.tokens.colorsSemanticPositive500
            : ui.tokens.colorsSemanticNegative500;
    },
    icon() {
        return 'bag';
    },
    value() {
        if (this.$.settings.salesCurrency) {
            const salesCurrency = JSON.parse(this.$.settings.salesCurrency as string);
            if (salesCurrency) {
                return `${salesCurrency.symbol} ${String(this.$.data.xtremSales.salesInvoice.getYtdSales ?? 0)}`;
            }
        }
        return '0';
    },
    getQuery() {
        return {
            xtremSales: {
                salesInvoice: {
                    getYtdSales: {
                        __args: { currency: this.$.settings.salesCurrency ?? null },
                    },
                },
            },
        };
    },
})
export class YtdSales extends ui.widgets.AbstractWidget {}
