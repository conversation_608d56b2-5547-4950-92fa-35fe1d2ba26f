import * as ui from '@sage/xtrem-ui';

@ui.widgets.visualProcess<SalesVisualProcess>({
    title: 'Sales process',
    cacheLifespan: ui.widgets.WidgetCacheLifespan.day,
    content() {
        const customerLabel = ui.localize('@sage/xtrem-sales/pages__customer____title', 'Customer');
        const salesOrderLabel = ui.localize('@sage/xtrem-sales/pages__sales_order____title', 'Sales order');
        const salesShipmentLabel = ui.localize('@sage/xtrem-sales/pages__sales_shipment____title', 'Sales shipment');
        const salesInvoiceLabel = ui.localize('@sage/xtrem-sales/pages__sales_invoice____title', 'Sales invoice');
        const salesReturnReceiptLabel = ui.localize(
            '@sage/xtrem-sales/pages__sales_return_receipt____title',
            'Sales return receipt',
        );
        const salesReturnRequestLabel = ui.localize(
            '@sage/xtrem-sales/pages__sales_return_request____title',
            'Sales return request',
        );
        const salesCreditMemoLabel = ui.localize(
            '@sage/xtrem-sales/pages__sales_credit_memo____title',
            'Sales credit memo',
        );
        return {
            acts: {},
            layersArr: [
                {
                    xpropsArr: [
                        {
                            xanchors: [
                                {
                                    type: null,
                                    x: 171,
                                    y: 100,
                                },
                                {
                                    type: null,
                                    x: 171,
                                    y: 100,
                                },
                                {
                                    type: null,
                                    x: 171,
                                    y: 25,
                                },
                                {
                                    type: null,
                                    x: 171,
                                    y: 25,
                                },
                            ],
                            xcaptionSize: {
                                xheight: 0,
                                xwidth: 0,
                            },
                            xtext: '',
                            xdrawBehaviorCode: 'K_API_LINE',
                            xstrokeProps: {
                                xtype: 'dottedstroke',
                                xthickness: 2,
                                xcolor: ui.tokens.colorsActionMajor600,
                                xalpha: 50,
                                xstart: {
                                    xtype: 'none',
                                },
                                xend: {
                                    xtype: 'none',
                                },
                            },
                            xshadowProps: {
                                xtype: 'none',
                            },
                            xcaptionPos: {
                                xleft: 171,
                                xtop: 25,
                            },
                            uniqueID: 'z466dj0nra37ujvaubv',
                        },
                        {
                            xanchors: [
                                {
                                    type: null,
                                    x: 171,
                                    y: 220,
                                },
                                {
                                    type: null,
                                    x: 171,
                                    y: 220,
                                },
                                {
                                    type: null,
                                    x: 171,
                                    y: 100,
                                },
                                {
                                    type: null,
                                    x: 171,
                                    y: 100,
                                },
                            ],
                            xcaptionSize: {
                                xheight: 0,
                                xwidth: 0,
                            },
                            xtext: '',
                            xdrawBehaviorCode: 'K_API_LINE',
                            xstrokeProps: {
                                xtype: 'solidstroke',
                                xthickness: 2,
                                xcolor: ui.tokens.colorsActionMajor600,
                                xalpha: 50,
                                xstart: {
                                    xtype: 'none',
                                },
                                xend: {
                                    xtype: 'none',
                                },
                            },
                            xshadowProps: {
                                xtype: 'none',
                            },
                            xcaptionPos: {
                                xleft: 171,
                                xtop: 100,
                            },
                            uniqueID: '4xceqpgaxodookiq5e3',
                        },
                        {
                            xanchors: [
                                {
                                    type: null,
                                    x: 172,
                                    y: 295,
                                },
                                {
                                    type: null,
                                    x: 172,
                                    y: 295,
                                },
                                {
                                    type: null,
                                    x: 172,
                                    y: 220,
                                },
                                {
                                    type: null,
                                    x: 172,
                                    y: 220,
                                },
                            ],
                            xcaptionSize: {
                                xheight: 0,
                                xwidth: 0,
                            },
                            xtext: '',
                            xdrawBehaviorCode: 'K_API_LINE',
                            xstrokeProps: {
                                xtype: 'dottedstroke',
                                xthickness: 2,
                                xcolor: ui.tokens.colorsActionMajor600,
                                xalpha: 50,
                                xstart: {
                                    xtype: 'none',
                                },
                                xend: {
                                    xtype: 'none',
                                },
                            },
                            xshadowProps: {
                                xtype: 'none',
                            },
                            xcaptionPos: {
                                xleft: 172,
                                xtop: 220,
                            },
                            uniqueID: 'tzsx5sp8zwilp3cysrz',
                        },
                        {
                            xanchors: [
                                {
                                    type: null,
                                    x: 171,
                                    y: 415,
                                },
                                {
                                    type: null,
                                    x: 171,
                                    y: 415,
                                },
                                {
                                    type: null,
                                    x: 171,
                                    y: 295,
                                },
                                {
                                    type: null,
                                    x: 171,
                                    y: 295,
                                },
                            ],
                            xcaptionSize: {
                                xheight: 0,
                                xwidth: 0,
                            },
                            xtext: '',
                            xdrawBehaviorCode: 'K_API_LINE',
                            xstrokeProps: {
                                xtype: 'solidstroke',
                                xthickness: 2,
                                xcolor: ui.tokens.colorsActionMajor600,
                                xalpha: 50,
                                xstart: {
                                    xtype: 'none',
                                },
                                xend: {
                                    xtype: 'none',
                                },
                            },
                            xshadowProps: {
                                xtype: 'none',
                            },
                            xcaptionPos: {
                                xleft: 171,
                                xtop: 295,
                            },
                            uniqueID: 'haiahraeynl60we2iuj',
                        },
                        {
                            xanchors: [
                                {
                                    type: null,
                                    x: 233,
                                    y: 0,
                                },
                                {
                                    type: null,
                                    x: 393,
                                    y: 0,
                                },
                                {
                                    type: null,
                                    x: 393,
                                    y: 50,
                                },
                                {
                                    type: null,
                                    x: 233,
                                    y: 50,
                                },
                            ],
                            xtext: `<TEXTFORMAT><P>${customerLabel}</P></TEXTFORMAT>`,
                            xdrawBehaviorCode: 'K_API_RECT',
                            xstrokeProps: {
                                xtype: 'solidstroke',
                                xthickness: 1,
                                xcolor: ui.tokens.colorsYang100,
                                xalpha: 100,
                                xstart: {
                                    xtype: 'none',
                                },
                                xend: {
                                    xtype: 'none',
                                },
                            },
                            xfillProps: {
                                xtype: 'solidfill',
                                xcolor: 'transparent',
                                xalpha: 100,
                                xgtype: 'linear',
                            },
                            xshadowProps: {
                                xtype: 'none',
                            },
                            xcaptionPos: {
                                xleft: 233,
                                xtop: 0,
                            },
                            xtextFormat: {
                                size: 14,
                                align: 'left',
                                fontWeight: 400,
                                lineHeight: 21,
                                fontStyle: 'bold',
                            },
                            uniqueID: 'cpu87djgle577gqb1ba',
                            xlinkProps: {
                                xtype: 'erpfunc',
                                xcode: '@sage/xtrem-master-data/Customer',
                                xlabel: customerLabel,
                            },
                        },
                        {
                            xanchors: [
                                {
                                    type: null,
                                    x: 233,
                                    y: 75,
                                },
                                {
                                    type: null,
                                    x: 393,
                                    y: 75,
                                },
                                {
                                    type: null,
                                    x: 393,
                                    y: 125,
                                },
                                {
                                    type: null,
                                    x: 233,
                                    y: 125,
                                },
                            ],
                            xtext: `<TEXTFORMAT><P>${salesOrderLabel}</P></TEXTFORMAT>`,
                            xdrawBehaviorCode: 'K_API_RECT',
                            xstrokeProps: {
                                xtype: 'solidstroke',
                                xthickness: 1,
                                xcolor: ui.tokens.colorsYang100,
                                xalpha: 100,
                                xstart: {
                                    xtype: 'none',
                                },
                                xend: {
                                    xtype: 'none',
                                },
                            },
                            xfillProps: {
                                xtype: 'solidfill',
                                xcolor: ui.tokens.colorsYang100,
                                xalpha: 100,
                                xgtype: 'linear',
                            },
                            xshadowProps: {
                                xtype: 'none',
                            },
                            xcaptionPos: {
                                xleft: 233,
                                xtop: 75,
                            },
                            xtextFormat: {
                                size: 14,
                                align: 'left',
                                fontWeight: 400,
                                lineHeight: 21,
                                fontStyle: 'bold',
                            },
                            uniqueID: '8nnvr6c0yfqytr13fgv',
                            xlinkProps: {
                                xtype: 'erpfunc',
                                xcode: '@sage/xtrem-sales/SalesOrder',
                                xlabel: salesOrderLabel,
                            },
                        },
                        {
                            xanchors: [
                                {
                                    type: null,
                                    x: 233,
                                    y: 135,
                                },
                                {
                                    type: null,
                                    x: 393,
                                    y: 135,
                                },
                                {
                                    type: null,
                                    x: 393,
                                    y: 185,
                                },
                                {
                                    type: null,
                                    x: 233,
                                    y: 185,
                                },
                            ],
                            xtext: `<TEXTFORMAT><P>${salesShipmentLabel}</P></TEXTFORMAT>`,
                            xdrawBehaviorCode: 'K_API_RECT',
                            xstrokeProps: {
                                xtype: 'solidstroke',
                                xthickness: 1,
                                xcolor: ui.tokens.colorsYang100,
                                xalpha: 100,
                                xstart: {
                                    xtype: 'none',
                                },
                                xend: {
                                    xtype: 'none',
                                },
                            },
                            xfillProps: {
                                xtype: 'solidfill',
                                xcolor: ui.tokens.colorsYang100,
                                xalpha: 100,
                                xgtype: 'linear',
                            },
                            xshadowProps: {
                                xtype: 'none',
                            },
                            xcaptionPos: {
                                xleft: 233,
                                xtop: 135,
                            },
                            xtextFormat: {
                                size: 14,
                                align: 'left',
                                fontWeight: 400,
                                lineHeight: 21,
                                fontStyle: 'bold',
                            },
                            uniqueID: '5guee9vwaz38c5zkdmc',
                            xlinkProps: {
                                xtype: 'erpfunc',
                                xcode: '@sage/xtrem-sales/SalesShipment',
                                xlabel: salesShipmentLabel,
                            },
                        },
                        {
                            xanchors: [
                                {
                                    type: null,
                                    x: 233,
                                    y: 195,
                                },
                                {
                                    type: null,
                                    x: 393,
                                    y: 195,
                                },
                                {
                                    type: null,
                                    x: 393,
                                    y: 245,
                                },
                                {
                                    type: null,
                                    x: 233,
                                    y: 245,
                                },
                            ],
                            xtext: `<TEXTFORMAT><P>${salesInvoiceLabel}</P></TEXTFORMAT>`,
                            xdrawBehaviorCode: 'K_API_RECT',
                            xstrokeProps: {
                                xtype: 'solidstroke',
                                xthickness: 1,
                                xcolor: ui.tokens.colorsYang100,
                                xalpha: 100,
                                xstart: {
                                    xtype: 'none',
                                },
                                xend: {
                                    xtype: 'none',
                                },
                            },
                            xfillProps: {
                                xtype: 'solidfill',
                                xcolor: ui.tokens.colorsYang100,
                                xalpha: 100,
                                xgtype: 'linear',
                            },
                            xshadowProps: {
                                xtype: 'none',
                            },
                            xcaptionPos: {
                                xleft: 233,
                                xtop: 195,
                            },
                            xtextFormat: {
                                size: 14,
                                align: 'left',
                                fontWeight: 400,
                                lineHeight: 21,
                                fontStyle: 'bold',
                            },
                            uniqueID: '1iep4w3dlgq80yb26kb',
                            xlinkProps: {
                                xtype: 'erpfunc',
                                xcode: '@sage/xtrem-sales/SalesInvoice',
                                xlabel: salesInvoiceLabel,
                            },
                        },
                        {
                            xanchors: [
                                {
                                    type: null,
                                    x: 233,
                                    y: 270,
                                },
                                {
                                    type: null,
                                    x: 500,
                                    y: 270,
                                },
                                {
                                    type: null,
                                    x: 500,
                                    y: 320,
                                },
                                {
                                    type: null,
                                    x: 233,
                                    y: 320,
                                },
                            ],
                            xtext: `<TEXTFORMAT><P>${salesReturnRequestLabel}</P></TEXTFORMAT>`,
                            xdrawBehaviorCode: 'K_API_RECT',
                            xstrokeProps: {
                                xtype: 'solidstroke',
                                xthickness: 1,
                                xcolor: ui.tokens.colorsYang100,
                                xalpha: 100,
                                xstart: {
                                    xtype: 'none',
                                },
                                xend: {
                                    xtype: 'none',
                                },
                            },
                            xfillProps: {
                                xtype: 'solidfill',
                                xcolor: ui.tokens.colorsYang100,
                                xalpha: 100,
                                xgtype: 'linear',
                            },
                            xshadowProps: {
                                xtype: 'none',
                            },
                            xcaptionPos: {
                                xleft: 233,
                                xtop: 270,
                            },
                            xtextFormat: {
                                size: 14,
                                fontWeight: 400,
                                lineHeight: 21,
                                fontStyle: 'bold',
                                align: 'left',
                            },
                            xlinkProps: {
                                xtype: 'erpfunc',
                                xcode: '@sage/xtrem-sales/SalesReturnRequest',
                                xlabel: salesReturnRequestLabel,
                            },
                            uniqueID: 'vjtuyngq7x2xi265vhe',
                        },
                        {
                            xanchors: [
                                {
                                    type: null,
                                    x: 233,
                                    y: 330,
                                },
                                {
                                    type: null,
                                    x: 500,
                                    y: 330,
                                },
                                {
                                    type: null,
                                    x: 500,
                                    y: 380,
                                },
                                {
                                    type: null,
                                    x: 233,
                                    y: 380,
                                },
                            ],
                            xtext: `<TEXTFORMAT><P>${salesReturnReceiptLabel}</P></TEXTFORMAT>`,
                            xdrawBehaviorCode: 'K_API_RECT',
                            xstrokeProps: {
                                xtype: 'solidstroke',
                                xthickness: 1,
                                xcolor: ui.tokens.colorsYang100,
                                xalpha: 100,
                                xstart: {
                                    xtype: 'none',
                                },
                                xend: {
                                    xtype: 'none',
                                },
                            },
                            xfillProps: {
                                xtype: 'solidfill',
                                xcolor: ui.tokens.colorsYang100,
                                xalpha: 100,
                                xgtype: 'linear',
                            },
                            xshadowProps: {
                                xtype: 'none',
                            },
                            xcaptionPos: {
                                xleft: 233,
                                xtop: 330,
                            },
                            xtextFormat: {
                                size: 14,
                                align: 'left',
                                fontWeight: 400,
                                lineHeight: 21,
                                fontStyle: 'bold',
                            },
                            uniqueID: '54eqxjcdufwxlj3p7hp',
                            xlinkProps: {
                                xtype: 'erpfunc',
                                xcode: '@sage/xtrem-sales/SalesReturnReceipt',
                                xlabel: salesReturnReceiptLabel,
                            },
                        },
                        {
                            xanchors: [
                                {
                                    type: null,
                                    x: 233,
                                    y: 390,
                                },
                                {
                                    type: null,
                                    x: 393,
                                    y: 390,
                                },
                                {
                                    type: null,
                                    x: 393,
                                    y: 440,
                                },
                                {
                                    type: null,
                                    x: 233,
                                    y: 440,
                                },
                            ],
                            xtext: `<TEXTFORMAT><P>${salesCreditMemoLabel}</P></TEXTFORMAT>`,
                            xdrawBehaviorCode: 'K_API_RECT',
                            xstrokeProps: {
                                xtype: 'solidstroke',
                                xthickness: 1,
                                xcolor: ui.tokens.colorsYang100,
                                xalpha: 100,
                                xstart: {
                                    xtype: 'none',
                                },
                                xend: {
                                    xtype: 'none',
                                },
                            },
                            xfillProps: {
                                xtype: 'solidfill',
                                xcolor: ui.tokens.colorsYang100,
                                xalpha: 100,
                                xgtype: 'linear',
                            },
                            xshadowProps: {
                                xtype: 'none',
                            },
                            xcaptionPos: {
                                xleft: 233,
                                xtop: 390,
                            },
                            xtextFormat: {
                                size: 14,
                                align: 'left',
                                fontWeight: 400,
                                lineHeight: 21,
                                fontStyle: 'bold',
                            },
                            uniqueID: 'oo8ly6ntxc7dk5i63qv',
                            xlinkProps: {
                                xtype: 'erpfunc',
                                xcode: '@sage/xtrem-sales/SalesCreditMemo',
                                xlabel: salesCreditMemoLabel,
                            },
                        },
                        {
                            xanchors: [
                                {
                                    type: null,
                                    x: 155,
                                    y: 26,
                                },
                                {
                                    type: null,
                                    x: 171,
                                    y: 10,
                                },
                                {
                                    type: null,
                                    x: 187,
                                    y: 26,
                                },
                                {
                                    type: null,
                                    x: 171,
                                    y: 42,
                                },
                            ],
                            xtext: '',
                            xdrawBehaviorCode: 'K_API_ELLIPSE',
                            xstrokeProps: {
                                xtype: 'solidstroke',
                                xthickness: 1,
                                xcolor: ui.tokens.colorsActionMajor600,
                                xalpha: 100,
                                xstart: {
                                    xtype: 'none',
                                },
                                xend: {
                                    xtype: 'none',
                                },
                            },
                            xfillProps: {
                                xtype: 'solidfill',
                                xcolor: ui.tokens.colorsActionMajor500,
                                xalpha: 100,
                                xgtype: 'linear',
                            },
                            xshadowProps: {
                                xtype: 'none',
                            },
                            xcaptionPos: {
                                xleft: 155,
                                xtop: 26,
                            },
                            uniqueID: 'av0wnb1uej44sxg9tyk',
                            xlinkProps: {
                                xtype: 'erpfunc',
                                xcode: '@sage/xtrem-master-data/Customer',
                                xlabel: customerLabel,
                            },
                        },
                        {
                            xanchors: [
                                {
                                    type: null,
                                    x: 155,
                                    y: 101,
                                },
                                {
                                    type: null,
                                    x: 171,
                                    y: 85,
                                },
                                {
                                    type: null,
                                    x: 187,
                                    y: 101,
                                },
                                {
                                    type: null,
                                    x: 171,
                                    y: 117,
                                },
                            ],
                            xtext: '',
                            xdrawBehaviorCode: 'K_API_ELLIPSE',
                            xstrokeProps: {
                                xtype: 'solidstroke',
                                xthickness: 1,
                                xcolor: ui.tokens.colorsActionMajor600,
                                xalpha: 100,
                                xstart: {
                                    xtype: 'none',
                                },
                                xend: {
                                    xtype: 'none',
                                },
                            },
                            xfillProps: {
                                xtype: 'solidfill',
                                xcolor: ui.tokens.colorsActionMajor500,
                                xalpha: 100,
                                xgtype: 'linear',
                            },
                            xshadowProps: {
                                xtype: 'none',
                            },
                            xcaptionPos: {
                                xleft: 155,
                                xtop: 101,
                            },
                            uniqueID: 'tsgew1z15rr1i4emh98',
                            xlinkProps: {
                                xtype: 'erpfunc',
                                xcode: '@sage/xtrem-sales/SalesOrder',
                                xlabel: salesOrderLabel,
                            },
                        },
                        {
                            xanchors: [
                                {
                                    type: null,
                                    x: 155,
                                    y: 161,
                                },
                                {
                                    type: null,
                                    x: 171,
                                    y: 145,
                                },
                                {
                                    type: null,
                                    x: 187,
                                    y: 161,
                                },
                                {
                                    type: null,
                                    x: 171,
                                    y: 177,
                                },
                            ],
                            xtext: '',
                            xdrawBehaviorCode: 'K_API_ELLIPSE',
                            xstrokeProps: {
                                xtype: 'solidstroke',
                                xthickness: 1,
                                xcolor: ui.tokens.colorsActionMajor600,
                                xalpha: 100,
                                xstart: {
                                    xtype: 'none',
                                },
                                xend: {
                                    xtype: 'none',
                                },
                            },
                            xfillProps: {
                                xtype: 'solidfill',
                                xcolor: ui.tokens.colorsActionMajor500,
                                xalpha: 100,
                                xgtype: 'linear',
                            },
                            xshadowProps: {
                                xtype: 'none',
                            },
                            xcaptionPos: {
                                xleft: 155,
                                xtop: 161,
                            },
                            uniqueID: '9ww0438hbm86k7xu7dy',
                            xlinkProps: {
                                xtype: 'erpfunc',
                                xcode: '@sage/xtrem-sales/SalesShipment',
                                xlabel: salesShipmentLabel,
                            },
                        },
                        {
                            xanchors: [
                                {
                                    type: null,
                                    x: 155,
                                    y: 221,
                                },
                                {
                                    type: null,
                                    x: 171,
                                    y: 205,
                                },
                                {
                                    type: null,
                                    x: 187,
                                    y: 221,
                                },
                                {
                                    type: null,
                                    x: 171,
                                    y: 237,
                                },
                            ],
                            xtext: '',
                            xdrawBehaviorCode: 'K_API_ELLIPSE',
                            xstrokeProps: {
                                xtype: 'solidstroke',
                                xthickness: 1,
                                xcolor: ui.tokens.colorsActionMajor600,
                                xalpha: 100,
                                xstart: {
                                    xtype: 'none',
                                },
                                xend: {
                                    xtype: 'none',
                                },
                            },
                            xfillProps: {
                                xtype: 'solidfill',
                                xcolor: ui.tokens.colorsActionMajor500,
                                xalpha: 100,
                                xgtype: 'linear',
                            },
                            xshadowProps: {
                                xtype: 'none',
                            },
                            xcaptionPos: {
                                xleft: 155,
                                xtop: 221,
                            },
                            uniqueID: 'h401og7v3hrjurbixqp',
                            xlinkProps: {
                                xtype: 'erpfunc',
                                xcode: '@sage/xtrem-sales/SalesInvoice',
                                xlabel: salesInvoiceLabel,
                            },
                        },
                        {
                            xanchors: [
                                {
                                    type: null,
                                    x: 155,
                                    y: 296,
                                },
                                {
                                    type: null,
                                    x: 171,
                                    y: 280,
                                },
                                {
                                    type: null,
                                    x: 187,
                                    y: 296,
                                },
                                {
                                    type: null,
                                    x: 171,
                                    y: 312,
                                },
                            ],
                            xtext: '',
                            xdrawBehaviorCode: 'K_API_ELLIPSE',
                            xstrokeProps: {
                                xtype: 'solidstroke',
                                xthickness: 1,
                                xcolor: ui.tokens.colorsActionMajor600,
                                xalpha: 100,
                                xstart: {
                                    xtype: 'none',
                                },
                                xend: {
                                    xtype: 'none',
                                },
                            },
                            xfillProps: {
                                xtype: 'solidfill',
                                xcolor: ui.tokens.colorsActionMajor500,
                                xalpha: 100,
                                xgtype: 'linear',
                            },
                            xshadowProps: {
                                xtype: 'none',
                            },
                            xcaptionPos: {
                                xleft: 155,
                                xtop: 296,
                            },
                            xlinkProps: {
                                xtype: 'erpfunc',
                                xcode: '@sage/xtrem-sales/SalesReturnRequest',
                                xlabel: salesReturnRequestLabel,
                            },
                            uniqueID: '5tnifofxfcg9asyijwc',
                        },
                        {
                            xanchors: [
                                {
                                    type: null,
                                    x: 155,
                                    y: 356,
                                },
                                {
                                    type: null,
                                    x: 171,
                                    y: 340,
                                },
                                {
                                    type: null,
                                    x: 187,
                                    y: 356,
                                },
                                {
                                    type: null,
                                    x: 171,
                                    y: 372,
                                },
                            ],
                            xtext: '',
                            xdrawBehaviorCode: 'K_API_ELLIPSE',
                            xstrokeProps: {
                                xtype: 'solidstroke',
                                xthickness: 1,
                                xcolor: ui.tokens.colorsActionMajor600,
                                xalpha: 100,
                                xstart: {
                                    xtype: 'none',
                                },
                                xend: {
                                    xtype: 'none',
                                },
                            },
                            xfillProps: {
                                xtype: 'solidfill',
                                xcolor: ui.tokens.colorsActionMajor500,
                                xalpha: 100,
                                xgtype: 'linear',
                            },
                            xshadowProps: {
                                xtype: 'none',
                            },
                            xcaptionPos: {
                                xleft: 155,
                                xtop: 356,
                            },
                            uniqueID: 'r07c3qsbz10z7llc46l',
                            xlinkProps: {
                                xtype: 'erpfunc',
                                xcode: '@sage/xtrem-sales/SalesReturnReceipt',
                                xlabel: salesReturnReceiptLabel,
                            },
                        },
                        {
                            xanchors: [
                                {
                                    type: null,
                                    x: 155,
                                    y: 416,
                                },
                                {
                                    type: null,
                                    x: 171,
                                    y: 400,
                                },
                                {
                                    type: null,
                                    x: 187,
                                    y: 416,
                                },
                                {
                                    type: null,
                                    x: 171,
                                    y: 432,
                                },
                            ],
                            xtext: '',
                            xdrawBehaviorCode: 'K_API_ELLIPSE',
                            xstrokeProps: {
                                xtype: 'solidstroke',
                                xthickness: 1,
                                xcolor: ui.tokens.colorsActionMajor600,
                                xalpha: 100,
                                xstart: {
                                    xtype: 'none',
                                },
                                xend: {
                                    xtype: 'none',
                                },
                            },
                            xfillProps: {
                                xtype: 'solidfill',
                                xcolor: ui.tokens.colorsActionMajor500,
                                xalpha: 100,
                                xgtype: 'linear',
                            },
                            xshadowProps: {
                                xtype: 'none',
                            },
                            xcaptionPos: {
                                xleft: 155,
                                xtop: 416,
                            },
                            uniqueID: 'kbj4v7tylnu5h02ufm2',
                            xlinkProps: {
                                xtype: 'erpfunc',
                                xcode: '@sage/xtrem-sales/SalesCreditMemo',
                                xlabel: salesCreditMemoLabel,
                            },
                        },
                    ],
                    alpha: 100,
                    visible: true,
                    lock: false,
                    id: 1,
                },
            ],
            reachedGroupNum: 1,
            contentSize: {
                xheight: 454,
                xwidth: 500,
            },
            docDims: {
                xheight: 640,
                xwidth: 860,
                xtop: 0,
                xleft: 0,
            },
            currentLayerId: 1,
        } as any;
    },
    getQuery() {
        return {
            xtremMasterData: {
                customer: {
                    query: {
                        __args: { first: 1 },
                        totalCount: true,
                    },
                },
            },
        };
    },
})
export class SalesVisualProcess extends ui.widgets.AbstractWidget {}
