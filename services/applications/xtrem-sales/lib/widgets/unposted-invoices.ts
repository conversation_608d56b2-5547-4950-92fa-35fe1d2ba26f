import * as ui from '@sage/xtrem-ui';

interface SalesInvoiceQueryResponseNode {
    _id: string;
    number: string;
    billToCustomer: {
        _id: string;
        name: string;
    };
    site: {
        _id: string;
        name: string;
    };
    status: string;
    date: string;
    dueDate: string;
}

interface SalesInvoiceQueryResponse {
    cursor: string;
    node: SalesInvoiceQueryResponseNode;
}

interface SalesInvoiceObject {
    _id: string;
    index: string;
    title: string;
    line2: string;
    titleRight: string;
    line3: string;
    line2Right: string;
    line3Right: string;
    cursor: string;
}

@ui.widgets.table<UnpostedInvoices>({
    title: 'Unposted sales invoices',
    cacheLifespan: ui.widgets.WidgetCacheLifespan.day,
    category: 'Period close validations',
    content() {
        const numberOfInvoices = this.$.data?.xtremSales?.salesInvoice?.query?.edges?.length ?? 0;
        if (numberOfInvoices > 0) {
            return this.$.data.xtremSales?.salesInvoice?.query.edges.map(
                ({ node, cursor }: SalesInvoiceQueryResponse, index: number): SalesInvoiceObject => ({
                    _id: node._id,
                    index: `${index}`,
                    title: node.number,
                    titleRight: node.billToCustomer.name,
                    line2: node.site.name,
                    line2Right: ui.localizeEnumMember('@sage/xtrem-sales/SalesInvoiceStatus', node.status),
                    line3: ui.formatDateToCurrentLocale(node.date),
                    line3Right: ui.formatDateToCurrentLocale(node.dueDate),
                    cursor,
                }),
            );
        }
        return [];
    },
    callToActions: {
        seeAll: {
            title: 'See all',
            onClick() {
                this.$.router.goTo('@sage/xtrem-sales/SalesInvoice');
            },
        },
    },
    canSwitchViewMode: true,
    displayMode: 'table',
    canSelect: false,
    dataDropdownMenu: {
        orderBy: {
            date: { title: 'Sort by invoice date' },
            number: { title: 'Sort by number' },
            customerName: { title: 'Sort by customer name' },
        },
    },
    rowDefinition: {
        title: {
            title: 'Sales invoice',
            renderedAs: 'link',
            onClick(_id: string) {
                this.$.router.goTo('@sage/xtrem-sales/SalesInvoice', { _id });
            },
        },
        titleRight: {
            title: 'Customer',
            renderedAs: 'link',
            onClick(_id: string) {
                const node = this.getDataFromLineIndex(_id);
                this.$.router.goTo('@sage/xtrem-master-data/Customer', { _id: node.billToCustomer._id });
            },
        },
        line2: {
            title: 'Sales site',
            renderedAs: 'link',
            onClick(_id: string) {
                const node = this.getDataFromLineIndex(_id);
                this.$.router.goTo('@sage/xtrem-master-data/Site', { _id: node.site._id });
            },
        },
        line2Right: {
            title: 'Status',
            renderedAs: 'pill',
            displayOptions: {
                colorVariant(_id: string) {
                    const { status } = this.getDataFromLineIndex(_id);
                    switch (status) {
                        case 'error':
                            return 'negative';
                        case 'inProgress':
                            return 'warning';
                        default:
                            return 'neutral';
                    }
                },
            },
        },
        line3: { title: 'Invoice date' },
        line3Right: { title: 'Due date' },
    },
    getQuery(args) {
        const filter = { status: { _ne: 'posted' } };

        const orderBy: { date?: number; number?: number; billToCustomer?: { name: 1 } } = {};
        switch (this.$.options.dataOptions?.orderBy) {
            case 'number':
                orderBy.number = 1;
                break;
            case 'date':
                orderBy.date = 1;
                break;
            case 'customerName':
                orderBy.billToCustomer = { name: 1 };
                break;
            default:
                orderBy.date = 1;
        }

        return {
            xtremSales: {
                salesInvoice: {
                    query: {
                        __args: {
                            filter: JSON.stringify(filter),
                            orderBy: JSON.stringify(orderBy),
                            ...(args?.after?.cursor && { after: args.after.cursor }),
                        },
                        edges: {
                            cursor: true,
                            node: {
                                _id: true,
                                number: true,
                                billToCustomer: {
                                    _id: true,
                                    name: true,
                                },
                                site: {
                                    _id: true,
                                    name: true,
                                },
                                status: true,
                                date: true,
                                dueDate: true,
                            },
                        },
                    },
                },
            },
        };
    },
})
export class UnpostedInvoices extends ui.widgets.TableWidget {
    private getDataFromLineIndex(_id: string): SalesInvoiceQueryResponseNode {
        return this.$.data.xtremSales.salesInvoice.query.edges.find(
            (edge: SalesInvoiceQueryResponse) => edge.node._id === _id,
        ).node;
    }
}
