import * as ui from '@sage/xtrem-ui';

interface SalesShipmentQueryResponseNode {
    _id: string;
    number: string;
    billToCustomer: {
        _id: string;
        name: string;
    };
    site: {
        _id: string;
        name: string;
    };
    status: string;
    shippingDate: string;
    deliveryDate: string;
}

interface SalesShipmentQueryResponse {
    cursor: string;
    node: SalesShipmentQueryResponseNode;
}

interface SalesShipmentObject {
    _id: string;
    index: string;
    title: string;
    line2: string;
    titleRight: string;
    line3: string;
    line2Right: string;
    line3Right: string;
    cursor: string;
}

@ui.widgets.table<UnpostedShipments>({
    title: 'Unposted sales shipments',
    cacheLifespan: ui.widgets.WidgetCacheLifespan.day,
    category: 'Period close validations',
    content() {
        const numberOfShipments = this.$.data?.xtremSales?.salesShipment?.query?.edges?.length ?? 0;
        if (numberOfShipments > 0) {
            return this.$.data.xtremSales?.salesShipment?.query.edges.map(
                ({ node, cursor }: SalesShipmentQueryResponse, index: number): SalesShipmentObject => ({
                    _id: node._id,
                    index: `${index}`,
                    title: node.number,
                    titleRight: node.billToCustomer.name,
                    line2: node.site.name,
                    line2Right: ui.localizeEnumMember('@sage/xtrem-sales/SalesShipmentStatus', node.status),
                    line3: ui.formatDateToCurrentLocale(node.shippingDate),
                    line3Right: ui.formatDateToCurrentLocale(node.deliveryDate),
                    cursor,
                }),
            );
        }
        return [];
    },
    callToActions: {
        seeAll: {
            title: 'See all',
            onClick() {
                this.$.router.goTo('@sage/xtrem-sales/SalesShipment');
            },
        },
    },
    canSwitchViewMode: true,
    displayMode: 'table',
    canSelect: false,
    dataDropdownMenu: {
        orderBy: {
            shippingDate: { title: 'Sort by shipping date' },
            number: { title: 'Sort by number' },
            customerName: { title: 'Sort by customer name' },
        },
    },
    rowDefinition: {
        title: {
            title: 'Sales shipment',
            renderedAs: 'link',
            onClick(_id: string) {
                this.$.router.goTo('@sage/xtrem-sales/SalesShipment', { _id });
            },
        },
        titleRight: {
            title: 'Customer',
            renderedAs: 'link',
            onClick(_id: string) {
                const node = this.getDataFromLineIndex(_id);
                this.$.router.goTo('@sage/xtrem-master-data/Customer', { _id: node.billToCustomer._id });
            },
        },
        line2: {
            title: 'Sales site',
            renderedAs: 'link',
            onClick(_id: string) {
                const node = this.getDataFromLineIndex(_id);
                this.$.router.goTo('@sage/xtrem-master-data/Site', { _id: node.site._id });
            },
        },
        line2Right: {
            title: 'Status',
            renderedAs: 'pill',
            displayOptions: {
                colorVariant(_id: string) {
                    const { status } = this.getDataFromLineIndex(_id);
                    switch (status) {
                        case 'readyToShip':
                            return 'warning';
                        default:
                            return 'neutral';
                    }
                },
            },
        },
        line3: { title: 'Shipping date' },
        line3Right: { title: 'Delivery date' },
    },
    getQuery(args) {
        const filter = { status: { _ne: 'shipped' } };

        const orderBy: { shippingDate?: number; number?: number; billToCustomer?: { name: 1 } } = {};
        switch (this.$.options.dataOptions?.orderBy) {
            case 'number':
                orderBy.number = 1;
                break;
            case 'shippingDate':
                orderBy.shippingDate = 1;
                break;
            case 'customerName':
                orderBy.billToCustomer = { name: 1 };
                break;
            default:
                orderBy.shippingDate = 1;
        }

        return {
            xtremSales: {
                salesShipment: {
                    query: {
                        __args: {
                            filter: JSON.stringify(filter),
                            orderBy: JSON.stringify(orderBy),
                            ...(args?.after?.cursor && { after: args.after.cursor }),
                        },
                        edges: {
                            cursor: true,
                            node: {
                                _id: true,
                                number: true,
                                billToCustomer: {
                                    _id: true,
                                    name: true,
                                },
                                site: {
                                    _id: true,
                                    name: true,
                                },
                                status: true,
                                shippingDate: true,
                                deliveryDate: true,
                            },
                        },
                    },
                },
            },
        };
    },
})
export class UnpostedShipments extends ui.widgets.TableWidget {
    private getDataFromLineIndex(_id: string): SalesShipmentQueryResponseNode {
        return this.$.data.xtremSales.salesShipment.query.edges.find(
            (edge: SalesShipmentQueryResponse) => edge.node._id === _id,
        ).node;
    }
}
