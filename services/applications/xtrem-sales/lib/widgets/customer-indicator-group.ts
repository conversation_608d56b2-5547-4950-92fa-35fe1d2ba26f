import * as ui from '@sage/xtrem-ui';
import { getContext } from '../client-functions/widgets-function';

@ui.widgets.indicatorTileGroup<CustomerIndicatorGroup>({
    title: 'Customer KPIs',
    cacheLifespan: ui.widgets.WidgetCacheLifespan.day,
    group: 'Customer-360',
    category: 'Customer',
    content() {
        const lastOrderDate = this.$.data.xtremSales.salesOrder.getLastOrderDate
            ? ui.formatDateToCurrentLocale(this.$.data.xtremSales.salesOrder.getLastOrderDate)
            : '';
        const orderBookTotal = `${this.$.data.xtremSales.salesOrder.getOrderBookAmount?.currencySymbol ?? ''} ${ui.formatNumberToCurrentLocale(this.$.data.xtremSales.salesOrder.getOrderBookAmount?.orderBookTotal ?? 0)}`;
        const creditLimit = `${this.$.data.xtremMasterData.customer.read?.currency?.symbol ?? ''} ${ui.formatNumberToCurrentLocale(this.$.data.xtremMasterData.customer.read?.creditLimit ?? 0)}`;
        const invoicedTotal = `${this.$.data.xtremSales.salesInvoice.getCustomerYtdSales?.currencySymbol ?? ''} ${ui.formatNumberToCurrentLocale(this.$.data.xtremSales.salesInvoice.getCustomerYtdSales?.ytdTotal ?? 0)} ${' YTD'}`;
        const grossProfit = `${this.$.data.xtremSales.salesInvoice.getCustomerYtdGross?.currencySymbol ?? ''} ${ui.formatNumberToCurrentLocale(this.$.data.xtremSales.salesInvoice.getCustomerYtdGross?.ytdTotal ?? 0)} ${' YTD'}`;

        return getContext({ lastOrderDate, orderBookTotal, creditLimit, invoicedTotal, grossProfit }).map(item => ({
            ...item,
            contentAlignment: item.contentAlignment as 'left' | 'right' | undefined,
            value: item.value ?? '',
        }));
    },
    getQuery() {
        return {
            xtremSales: {
                salesInvoice: {
                    getCustomerYtdSales: {
                        __args: {
                            customer: this.$.contextVariables?.recordId ?? null,
                        },
                        ytdTotal: true,
                        currencySymbol: true,
                        decimalDigits: true,
                    },
                    getCustomerYtdGross: {
                        __args: {
                            customer: this.$.contextVariables?.recordId ?? null,
                        },
                        ytdTotal: true,
                        currencySymbol: true,
                        decimalDigits: true,
                    },
                },
                salesOrder: {
                    getLastOrderDate: {
                        __args: { customer: this.$.contextVariables?.recordId || null },
                    },
                    getOrderBookAmount: {
                        __args: {
                            customer: this.$.contextVariables?.recordId ?? null,
                        },
                        orderBookTotal: true,
                        currencySymbol: true,
                        decimalDigits: true,
                    },
                },
            },
            xtremMasterData: {
                customer: {
                    read: {
                        __args: {
                            _id: this.$.contextVariables?.recordId ?? null,
                        },
                        currency: { symbol: true, decimalDigits: true },
                        creditLimit: true,
                    },
                },
            },
        };
    },
})
export class CustomerIndicatorGroup extends ui.widgets.AbstractWidget {}
