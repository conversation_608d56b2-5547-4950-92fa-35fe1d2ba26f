import { type Filter, type OrderBy } from '@sage/xtrem-client';
import * as Pill<PERSON><PERSON>r<PERSON><PERSON>mon from '@sage/xtrem-master-data/build/lib/client-functions/widget-pill-color';
import type { SalesOrder, SalesOrderStatus } from '@sage/xtrem-sales-api';
import type { GraphApi } from '@sage/xtrem-system-api';
import * as MainListActions from '@sage/xtrem-system/build/lib/client-functions/main-list-actions';
import * as ui from '@sage/xtrem-ui';
import * as MasterDataUtils from '@sage/xtrem-master-data/build/lib/client-functions/utils';
import * as displayButtons from '../client-functions/display-buttons-sales-order';
import * as actionFunctions from '../client-functions/sales-order-actions-functions';
import { getLocalizeOrderSendAction } from '../client-functions/sales-order-actions-functions';
import * as SalesOrderAllocationAction from '../client-functions/sales-order-allocation-action';
import * as SalesOrderConfirmFunction from '../client-functions/sales-order-confirm-action';
import * as SalesOrderPrintFunction from '../client-functions/sales-order-print-action';
import * as SalesOrderProformaAction from '../client-functions/sales-order-proforma-action';
import { getFilteredSalesOrders } from '../client-functions/widgets-function';
import { isAssignmentCheckedOnSalesOrderClose } from '../client-functions/sales-order-assignment';

@ui.widgets.table<OpenSalesOrders>({
    title: 'In progress sales orders',
    group: 'Customer-360',
    category: 'Customer',
    businessIcon: 'data',
    filterMenuType: 'tabs',
    cacheLifespan: ui.widgets.WidgetCacheLifespan.day,
    content() {
        const salesOrdersEdges = this.$.data?.xtremSales.salesOrder.query.edges;
        if (salesOrdersEdges && salesOrdersEdges.length > 0) {
            return (
                salesOrdersEdges.map(edges => {
                    const salesOrder = edges.node;
                    return {
                        _id: salesOrder._id,
                        title: salesOrder.number,
                        titleRight: salesOrder.date,
                        expectedDeliveryDate: salesOrder.expectedDeliveryDate,
                        shippingDate: salesOrder.shippingDate,
                        totalAmount: `${salesOrder.currency.symbol.toString()} ${
                            Math.round(
                                +salesOrder.totalAmountExcludingTax * 10 ** Number(salesOrder.currency.decimalDigits),
                            ) /
                            10 ** Number(salesOrder.currency.decimalDigits)
                        }`,
                        totalGrossProfit: `${salesOrder.currency.symbol.toString()} ${
                            Math.round(+salesOrder.totalGrossProfit * 10 ** Number(salesOrder.currency.decimalDigits)) /
                            10 ** Number(salesOrder.currency.decimalDigits)
                        }`,
                        site: salesOrder.site.name,
                        status: salesOrder.status,
                        allocationRequestStatus: salesOrder.allocationRequestStatus,
                        taxCalculationStatus: salesOrder.taxCalculationStatus,
                        isOrderAssignmentLinked: String(salesOrder.isOrderAssignmentLinked),
                        allocationStatus: salesOrder.allocationStatus,
                        soldToLinkedAddressId: salesOrder.soldToLinkedAddress._id,
                        soldToContactEmail: salesOrder.soldToContact?.email || '',
                        soldToContactFirstName: salesOrder.soldToContact?.firstName || '',
                        soldToContactLastName: salesOrder.soldToContact?.lastName || '',
                        soldToContactTitle: salesOrder.soldToContact?.title || '',
                        shippingStatus: salesOrder.shippingStatus,
                        isCloseHidden: String(salesOrder.isCloseHidden),
                        customerOnHoldCheck: salesOrder.site.legalCompany.customerOnHoldCheck,
                        displayStatus: ui.localizeEnumMember(
                            '@sage/xtrem-sales/SalesOrderDisplayStatus',
                            salesOrder.displayStatus,
                        ),
                        isOnHold: String(salesOrder.isOnHold),
                        displayStatusString: salesOrder.displayStatus,
                        cursor: edges.cursor,
                    };
                }) ?? []
            );
        }
        return [];
    },
    canSwitchViewMode: false,
    displayMode: 'table',
    dataDropdownMenu: {
        userType: {
            open: { title: 'Open sales orders' },
            onTime: { title: 'On-time sales orders' },
            late: { title: 'Late sales orders' },
        },
    },
    rowDefinition: {
        title: {
            title: 'Number',
            renderedAs: 'link',
            async onClick(_id: string) {
                await this.$.dialog.page('@sage/xtrem-sales/SalesOrder', { _id }, { fullScreen: true });
            },
        },
        titleRight: { title: 'Date' },
        expectedDeliveryDate: {
            title: 'Delivery date',
        },
        shippingDate: {
            title: 'Shipping date',
        },
        totalAmount: {
            title: 'Amount',
        },
        totalGrossProfit: {
            title: 'Gross profit',
        },
        site: {
            title: 'Site',
        },
        displayStatus: {
            title: 'Status',
            renderedAs: 'pill',
            displayOptions: {
                colorVariant(_id: string) {
                    const data = this.getDataFromLineIndex(_id);
                    const status = data?.displayStatus ?? '';
                    return PillColorCommon.getWidgetDisplayStatusPillFeatures(status);
                },
            },
        },
    },
    rowActions: [
        {
            title: 'Confirm',
            icon: 'none',
            async onClick(id: string, row) {
                if (row.title && id) {
                    await SalesOrderConfirmFunction.confirmSalesOrderFunction({
                        isCalledFromRecordPage: false,
                        pageOrWidget: this,
                        _id: id,
                        status: row.status as SalesOrderStatus,
                        number: String(row.title),
                        isOnHold: row.isOnHold === 'true',
                        customerOnHoldCheck: String(row.customerOnHoldCheck),
                        taxCalculationStatus: String(row.taxCalculationStatus),
                    });
                    this.$.refreshWidget();
                }
            },
            isHidden(id: string, row) {
                return (
                    displayButtons.isHiddenButtonConfirmAction({
                        parameters: { status: row.status as string },
                        recordId: id,
                        isDirty: false,
                    }) || false
                );
            },
        },
        {
            icon: 'none',
            title: 'Create shipment',
            onClick() {
                // intentionally left empty
            },
            isHidden: true,
        },
        {
            icon: 'none',
            title: 'Close order',
            async onClick(id, row) {
                try {
                    const isAssignmentChecked = await isAssignmentCheckedOnSalesOrderClose({
                        salesOrderPage: this,
                        recordNumber: String(id),
                        isOrderAssignmentLinked: !!row.isOrderAssignmentLinked,
                    });
                    if (isAssignmentChecked) {
                        await actionFunctions.closeAction({
                            isCalledFromRecordPage: false,
                            salesOrderPage: this,
                            recordNumber: String(id),
                        });
                    }
                } catch (error) {
                    this.$.showToast(MasterDataUtils.formatError(this, error), { type: 'error' });
                }
                this.$.refreshWidget();
            },
            isHidden(_recordId, rowItem) {
                return rowItem.status === 'closed' || rowItem.allocationRequestStatus === 'inProgress';
            },
        },
        {
            icon: 'none',
            title: 'Open order',
            onClick() {
                // intentionally left empty
            },
            isHidden: true,
        },
        ui.menuSeparator(),
        {
            title: 'Print',
            icon: 'print',
            async onClick(id: string, row) {
                await SalesOrderPrintFunction.printSalesOrderAction({
                    isCalledFromRecordPage: false,
                    pageOrWidget: this,
                    _id: id,
                    status: String(row.status),
                    number: String(row.title),
                });
            },
            isHidden(id: string, row) {
                return displayButtons.isHiddenButtonPrintAction({
                    parameters: {
                        status: row.status as string,
                    },
                    recordId: id,
                });
            },
            isDisabled(id: string, row) {
                if (id && row) {
                    return (
                        displayButtons.isDisabledButtonPrintAction({
                            parameters: {
                                status: row.status as string,
                            },
                            recordId: id,
                            isDirty: false,
                        }) || false
                    );
                }
                return true;
            },
        },
        {
            title: 'Send',
            icon: 'email',
            async onClick(_id, rowItem) {
                const localizedResults = getLocalizeOrderSendAction({
                    orderStatus: rowItem.status as SalesOrderStatus,
                    email: String(rowItem.soldToContactEmail),
                });

                await this.$.dialog.page(
                    '@sage/xtrem-master-data/SendEmailPanel',
                    {
                        _id: ui.NEW_PAGE,
                        nodeName: JSON.stringify('@sage/xtrem-sales/SalesOrder'),
                        email: JSON.stringify(rowItem.soldToContactEmail ?? ''),
                        firstName: JSON.stringify(rowItem.soldToContactFirstName ?? ''),
                        lastName: JSON.stringify(rowItem.soldToContactLastName ?? ''),
                        title: JSON.stringify(rowItem.soldToContactTitle ?? ''),
                        soldToLinkedAddressId: JSON.stringify(rowItem.soldToLinkedAddressId ?? ''),
                        pageId: JSON.stringify(rowItem._id ?? ''),
                        dialogTitle: JSON.stringify(localizedResults.dialogTitle),
                        confirmTitle: JSON.stringify(localizedResults.confirmTitle),
                        confirmMessage: JSON.stringify(localizedResults.confirmMessage),
                        resultMessage: JSON.stringify(localizedResults.resultMessage),
                    },
                    {
                        resolveOnCancel: true,
                        size: 'extra-large',
                    },
                );
            },
            isHidden(id: string, row) {
                return displayButtons.isHiddenButtonSendMailAction({
                    parameters: {
                        status: row.status as string,
                        taxCalculationStatus: row.taxCalculationStatus as string,
                        shippingStatus: row.shippingStatus as string,
                        taxEngine: row.taxEngine as string,
                    },
                    recordId: id,
                });
            },
            isDisabled(id: string, row) {
                return (
                    displayButtons.isDisabledButtonSendMailAction({
                        parameters: {
                            status: row.status as string,
                        },
                        recordId: id,
                        isDirty: false,
                    }) || false
                );
            },
        },
        {
            icon: 'three_boxes',
            title: 'Allocate stock',
            async onClick(id: string) {
                await SalesOrderAllocationAction.allocationAction({
                    pageOrWidget: this,
                    _id: id,
                    allocationType: 'allocation',
                });
                this.$.refreshWidget();
            },
            isHidden(id: string, row) {
                return displayButtons.isHiddenButtonRequestAllocationAction({
                    parameters: {
                        allocationRequestStatus: row.allocationRequestStatus as string,
                        allocationStatus: row.allocationStatus as string,
                        status: row.status as string,
                    },
                    recordId: id,
                });
            },
            isDisabled(id: string, row) {
                return (
                    displayButtons.isDisabledButtonRequestAllocationAction({
                        parameters: {
                            allocationRequestStatus: row.allocationRequestStatus as string,
                            allocationStatus: row.allocationStatus as string,
                            status: row.status as string,
                        },
                        recordId: id,
                        isDirty: false,
                    }) || false
                );
            },
        },
        {
            icon: 'three_boxes',
            title: 'Deallocate stock',
            async onClick(id: string) {
                await SalesOrderAllocationAction.allocationAction({
                    pageOrWidget: this,
                    _id: id,
                    allocationType: 'deallocation',
                });
                this.$.refreshWidget();
            },
            isHidden(id: string, row) {
                return displayButtons.isHiddenButtonRequestDeallocationAction({
                    parameters: {
                        allocationRequestStatus: row.allocationRequestStatus as string,
                        allocationStatus: row.allocationStatus as string,
                        status: row.status as string,
                    },
                    recordId: id,
                });
            },
            isDisabled(id: string, row) {
                return (
                    displayButtons.isDisabledButtonRequestDeallocationAction({
                        parameters: {
                            allocationRequestStatus: row.allocationRequestStatus as string,
                            allocationStatus: row.allocationStatus as string,
                            status: row.status as string,
                        },
                        recordId: id,
                        isDirty: false,
                    }) || false
                );
            },
        },
        {
            title: 'Create proforma invoice',
            icon: 'add',
            async onClick(_id: string, row) {
                await SalesOrderProformaAction.proformaInvoiceDialog({
                    pageOrWidget: this,
                    option: 'create',
                    dialogParameters: { salesOrder: row as ui.PartialNodeWithId<SalesOrder> },
                });
            },
            isHidden(_id: string, row) {
                return row.displayStatusString !== 'confirmed';
            },
        },
        ui.menuSeparator(),
        {
            title: 'Delete',
            icon: 'bin',
            isDestructive: true,
            async onClick(_id, rowItem) {
                if (
                    await actionFunctions.isSalesOrderAssignmentCheckDelete(
                        this,
                        Boolean(rowItem.isOrderAssignmentLinked) || false,
                    )
                ) {
                    await MainListActions.deleteRecord<GraphApi>(this, {
                        _id: rowItem._id,
                        nodeName: '@sage/xtrem-sales/SalesOrder',
                    });
                }
            },
            isHidden(id: string, row) {
                return displayButtons.isHiddenButtonDeleteAction({
                    parameters: {
                        status: row.status as string,
                        taxCalculationStatus: row.taxCalculationStatus as string,
                        displayStatus: row.displayStatusString as string,
                    },
                    recordId: id,
                });
            },
        },
    ],
    callToActions: {
        selectedData: {
            title: 'See more',
            onClick() {
                this.$.router.goTo('@sage/xtrem-sales/SalesOrder', {
                    [ui.QUERY_PARAM_CUSTOM_MAIN_LIST_FILTER]: JSON.stringify(this.mainListFilter()),
                }); // to filter the list
            },
        },
        createOrder: {
            title: 'Create order',
            async onClick() {
                if (this.$.contextVariables) {
                    await this.$.dialog.page(
                        `@sage/xtrem-sales/SalesOrder`,
                        {
                            _id: ui.NEW_PAGE,
                            isCustomerDialog: true,
                        },
                        {
                            fullScreen: true,
                            resolveOnCancel: true,
                            values: { soldToCustomer: `_id:${this.$.contextVariables.recordId}` },
                        },
                    );
                    this.$.refreshWidget();
                }
            },
        },
    },

    getQuery(args) {
        const mainListFilter = this.mainListFilter();
        const { dataOptions } = this.$.options;
        const filteredSalesOrders = dataOptions?.userType ? getFilteredSalesOrders(dataOptions.userType) : null;
        const filter = filteredSalesOrders ? { ...mainListFilter, ...filteredSalesOrders } : mainListFilter;

        const orderBy: OrderBy<SalesOrder> = { date: -1, number: -1 };

        return {
            xtremSales: {
                salesOrder: {
                    query: {
                        __args: {
                            filter: JSON.stringify(filter),
                            orderBy: JSON.stringify(orderBy),
                            ...(args?.after?.cursor && { after: args.after.cursor }),
                        },
                        edges: {
                            cursor: true,
                            node: {
                                _id: true,
                                number: true,
                                currency: {
                                    symbol: true,
                                    decimalDigits: true,
                                },
                                expectedDeliveryDate: true,
                                shippingDate: true,
                                totalAmountExcludingTax: true,
                                displayStatus: true,
                                status: true,
                                allocationRequestStatus: true,
                                taxCalculationStatus: true,
                                allocationStatus: true,
                                isOrderAssignmentLinked: true,
                                soldToLinkedAddress: {
                                    _id: true,
                                },
                                shippingStatus: true,
                                soldToContact: {
                                    email: true,
                                    firstName: true,
                                    lastName: true,
                                    title: true,
                                },
                                isCloseHidden: true,
                                isOnHold: true,
                                date: true,
                                site: {
                                    _id: true,
                                    name: true,
                                    legalCompany: {
                                        _id: true,
                                        customerOnHoldCheck: true,
                                    },
                                },
                                totalGrossProfit: true,
                            },
                        },
                    },
                },
            },
        };
    },
})
export class OpenSalesOrders extends ui.widgets.TableWidget<
    {
        xtremSales: {
            salesOrder: {
                query: {
                    edges: {
                        node: SalesOrder;
                        cursor: string;
                    }[];
                };
            };
        };
    },
    GraphApi
> {
    private getDataFromLineIndex(_id: string): SalesOrder | undefined {
        return this.$.data?.xtremSales?.salesOrder.query.edges.find(edge => edge.node?._id === _id)?.node;
    }

    private mainListFilter(): Filter<SalesOrder> {
        return {
            soldToCustomer: { _id: { _eq: this.$.contextVariables?.recordId } },
            status: { _nin: ['closed'] },
        };
    }
}
