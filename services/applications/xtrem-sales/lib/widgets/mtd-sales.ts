import * as ui from '@sage/xtrem-ui';

@ui.widgets.indicatorTile<MtdSales>({
    title: 'MTD sales',
    cacheLifespan: ui.widgets.WidgetCacheLifespan.day,
    settingsPage: '@sage/xtrem-sales/SalesCurrencySettings',
    color() {
        if (this.$.data.xtremSales.salesInvoice.getMtdSales) {
            const { previousMonth, currentMonth } = this.$.data.xtremSales.salesInvoice.getMtdSales;
            return +currentMonth > +previousMonth
                ? ui.tokens.colorsSemanticPositive500
                : ui.tokens.colorsSemanticNegative500;
        }
        return ui.tokens.colorsSemanticPositive500;
    },
    icon() {
        if (this.$.data.xtremSales.salesInvoice.getMtdSales) {
            const { previousMonth, currentMonth } = this.$.data.xtremSales.salesInvoice.getMtdSales;
            return +currentMonth > +previousMonth ? 'incline' : 'decline';
        }
        return 'arrow_up';
    },
    value() {
        if (this.$.settings.salesCurrency) {
            const salesCurrency = JSON.parse(this.$.settings.salesCurrency as string);
            if (salesCurrency) {
                return `${salesCurrency.symbol} ${String(
                    this.$.data.xtremSales.salesInvoice.getMtdSales?.currentMonth ?? 0,
                )}`;
            }
        }
        return '0';
    },
    getQuery() {
        return {
            xtremSales: {
                salesInvoice: {
                    getMtdSales: {
                        __args: { currency: this.$.settings.salesCurrency ?? null },
                        currentMonth: true,
                        previousMonth: true,
                    },
                },
            },
        };
    },
})
export class MtdSales extends ui.widgets.AbstractWidget {}
