import * as ui from '@sage/xtrem-ui';

@ui.widgets.indicatorTile<CustomersOnHold>({
    title: 'Customers on hold',
    cacheLifespan: ui.widgets.WidgetCacheLifespan.day,
    settingsPage: '@sage/xtrem-sales/CustomersOnHoldSettings',
    color() {
        return this.$.data?.xtremMasterData?.customer?.query?.totalCount > 10
            ? ui.tokens.colorsSemanticNegative500
            : ui.tokens.colorsActionMajor500;
    },
    icon() {
        return 'person';
    },
    value() {
        return String(this.$.data?.xtremMasterData?.customer?.query?.totalCount ?? 0);
    },
    headerActions: [
        {
            title: 'View list',
            icon: 'bullet_list_dotted',
            onClick() {
                this.$.router.goTo('@sage/xtrem-master-data/Customer');
            },
        },
    ],
    getQuery() {
        const filter: any = { isOnHold: true };
        if (this.$.settings.shouldExcludeInactiveCustomers) {
            filter.isActive = true;
        }

        return {
            xtremMasterData: {
                customer: {
                    query: {
                        __args: { filter: JSON.stringify(filter) },
                        totalCount: true,
                    },
                },
            },
        };
    },
})
export class CustomersOnHold extends ui.widgets.AbstractWidget {}
