import * as ui from '@sage/xtrem-ui';

@ui.widgets.indicatorTile<DealSizeTrend>({
    title: 'Deal size trend',
    cacheLifespan: ui.widgets.WidgetCacheLifespan.day,
    settingsPage: '@sage/xtrem-sales/SalesCurrencySettings',
    color() {
        if (this.$.data.xtremSales.salesInvoice.getDealSizeTrend) {
            return this.$.data.xtremSales.salesInvoice.getDealSizeTrend > 0
                ? ui.tokens.colorsSemanticPositive500
                : ui.tokens.colorsSemanticNegative500;
        }
        return ui.tokens.colorsSemanticPositive500;
    },
    icon() {
        if (this.$.data.xtremSales.salesInvoice.getDealSizeTrend) {
            return this.$.data.xtremSales.salesInvoice.getDealSizeTrend > 0 ? 'incline' : 'decline';
        }
        return 'arrow_up';
    },
    value() {
        if (this.$.settings.salesCurrency) {
            const salesCurrency = JSON.parse(this.$.settings.salesCurrency as string);
            if (salesCurrency) {
                return `${salesCurrency.symbol} ${String(this.$.data.xtremSales.salesInvoice.getDealSizeTrend ?? 0)}`;
            }
        }
        return '0';
    },
    getQuery() {
        return {
            xtremSales: {
                salesInvoice: {
                    getDealSizeTrend: {
                        __args: { currency: this.$.settings.salesCurrency ?? null },
                    },
                },
            },
        };
    },
})
export class DealSizeTrend extends ui.widgets.AbstractWidget {}
