import '@sage/xtrem-date-time';
import * as ui from '@sage/xtrem-ui';

@ui.widgets.indicatorTile<NewCustomers>({
    title: 'New customers',
    cacheLifespan: ui.widgets.WidgetCacheLifespan.day,
    icon() {
        return 'person';
    },
    value() {
        return `${this.$.data.xtremMasterData.customer.readAggregate?._id?.distinctCount || 0}`;
    },
    getQuery() {
        const today = new Date();
        const firstDayOfYear = new Date(today.getFullYear(), 0, 1);
        const filter = `{_createStamp:{ _gte: '${firstDayOfYear.toISOString()}'}}`;
        return {
            xtremMasterData: {
                customer: {
                    readAggregate: {
                        __args: { filter },
                        _id: { distinctCount: true },
                    },
                },
            },
        };
    },
})
export class NewCustomers extends ui.widgets.AbstractWidget {}
