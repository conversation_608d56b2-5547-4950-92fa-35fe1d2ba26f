import * as ui from '@sage/xtrem-ui';

@ui.widgets.table<ShipmentsToInvoice>({
    title: 'Shipments to invoice',
    cacheLifespan: ui.widgets.WidgetCacheLifespan.day,
    content() {
        const shipments = this.$.data?.xtremSales?.salesShipment?.query?.edges?.length ?? 0;
        if (shipments === 0) {
            return [];
        }
        return this.$.data.xtremSales.salesShipment.query.edges.map(({ node, cursor }: any) => ({
            _id: node._id,
            title: node.number,
            titleRight: ui.formatDateToCurrentLocale(node.shippingDate),
            line2: `${node.currency.symbol} ${
                Math.round(+node.totalAmountExcludingTax * 10 ** node.currency.decimalDigits) /
                10 ** node.currency.decimalDigits
            }`,
            line2Right: ui.localizeEnumMember('@sage/xtrem-sales/SalesShipmentStatus', node.status),
            line3: node.billToCustomer.name,
            cursor,
        }));
    },
    callToActions: {
        Ship: {
            title: 'Invoice',
            isDisabled() {
                return (this.$.options.selectedItems ?? []).length === 0 || this.noShippedSelected();
            },
            onClick() {
                const salesShipments = this.$.options.selectedItems?.filter((id: any) => {
                    const salesShipment = this.$.data.xtremSales.salesShipment.query.edges.find(
                        (shipment: any) => shipment.node._id === id,
                    );

                    if (salesShipment.node.status === 'shipped' && !salesShipment.node.isOnHold) {
                        return id;
                    }
                    return '';
                });
                if (salesShipments?.length) {
                    this.$.router.goTo('@sage/xtrem-sales/SalesShipmentInvoicingMassProcess', {
                        createInvoices: 'Y',
                        salesShipments: JSON.stringify(salesShipments.filter(id => id !== '')),
                    });
                } else {
                    this.$.showToast(
                        ui.localize(
                            '@sage/xtrem-sales/widgets__shipment_to_invoice__create_invoices_no_invoiceable_orders',
                            'Selected invoices cannot be invoiced.',
                        ),
                        { type: 'error' },
                    );
                }
            },
        },
    },

    canSwitchViewMode: true,
    displayMode: 'card',
    canSelect: true,
    dataDropdownMenu: {
        orderBy: {
            number: { title: 'Sort by number' },
            shippingDate: { title: 'Sort by shipping date' },
            shippedStatus: { title: 'Sort by shipped status' },
        },
    },
    rowDefinition: {
        title: {
            title: 'Number',
            renderedAs: 'link',
            onClick(_id: string) {
                this.$.router.goTo('@sage/xtrem-sales/SalesShipment', { _id });
            },
        },
        titleRight: { title: 'Shipping date' },
        line2: {
            title: 'Total amount',
        },
        line2Right: {
            title: 'Shipped status',
            renderedAs: 'pill',
            displayOptions: {
                colorVariant(_id: string) {
                    const row = this.$.data.xtremSales.salesShipment.query.edges.find(
                        (item: any) => _id === item.node._id,
                    );

                    if (row) {
                        if (row.node.status === 'shipped') {
                            return 'warning';
                        }
                        if (row.node.status === 'readyToShip') {
                            return 'positive';
                        }
                    }
                    return 'neutral';
                },
            },
        },
        line3: {
            title: 'Customer name',
        },
    },
    getQuery(args) {
        const filter: any = { invoiceStatus: { _ne: 'invoiced' }, status: 'shipped' };
        const orderBy: any = {};
        const { dataOptions } = this.$.options;

        if (!dataOptions?.orderBy || dataOptions?.orderBy === 'number') {
            orderBy.number = 1;
        }

        if (dataOptions?.orderBy === 'shippingDate') {
            orderBy.shippingDate = 1;
        }

        if (dataOptions?.orderBy === 'shippedStatus') {
            orderBy.status = 1;
        }

        return {
            xtremSales: {
                salesShipment: {
                    query: {
                        __args: {
                            filter: JSON.stringify(filter),
                            orderBy: JSON.stringify(orderBy),
                            ...(args?.after?.cursor && { after: args.after.cursor }),
                        },
                        edges: {
                            cursor: true,
                            node: {
                                _id: true,
                                number: true,
                                currency: {
                                    symbol: true,
                                    decimalDigits: true,
                                },
                                totalAmountExcludingTax: true,
                                invoiceStatus: true,
                                status: true,
                                shippingDate: true,
                                isOnHold: true,
                                billToCustomer: {
                                    name: true,
                                },
                            },
                        },
                    },
                },
            },
        };
    },
})
export class ShipmentsToInvoice extends ui.widgets.TableWidget {
    private noShippedSelected() {
        return !this.$.options.selectedItems?.some((id: string) => {
            return (
                this.$.data.xtremSales.salesShipment.query.edges.find((order: any) => order.node._id === id).node
                    .status === 'shipped'
            );
        });
    }
}
