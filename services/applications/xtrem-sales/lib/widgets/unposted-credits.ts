import type { Filter, OrderBy } from '@sage/xtrem-client';
import type { SalesCreditMemo } from '@sage/xtrem-sales-api';
import * as ui from '@sage/xtrem-ui';

interface SalesCreditMemoQueryResponse {
    cursor: string;
    node: SalesCreditMemo;
}

interface SalesCreditMemoObject {
    _id: string;
    index: string;
    title: string;
    line2: string;
    titleRight: string;
    line3: string;
    line2Right: string;
    line3Right: string;
    cursor: string;
}

@ui.widgets.table<UnpostedCredits>({
    title: 'Unposted sales credit memos',
    cacheLifespan: ui.widgets.WidgetCacheLifespan.day,
    category: 'Period close validations',
    content() {
        const numberOfCredits = this.$.data?.xtremSales?.salesCreditMemo?.query?.edges?.length ?? 0;
        if (numberOfCredits > 0) {
            return this.$.data.xtremSales?.salesCreditMemo?.query.edges.map(
                ({ node, cursor }: SalesCreditMemoQueryResponse, index: number): SalesCreditMemoObject => ({
                    _id: node._id,
                    index: `${index}`,
                    title: node.number,
                    titleRight: node.billToCustomer.name,
                    line2: node.site.name,
                    line2Right: ui.localizeEnumMember('@sage/xtrem-sales/SalesCreditMemoStatus', node.status),
                    line3: ui.formatDateToCurrentLocale(node.date),
                    line3Right: ui.formatDateToCurrentLocale(node.dueDate),
                    cursor,
                }),
            );
        }
        return [];
    },
    callToActions: {
        seeAll: {
            title: 'See all',
            onClick() {
                this.$.router.goTo('@sage/xtrem-sales/SalesCreditMemo');
            },
        },
    },
    canSwitchViewMode: true,
    displayMode: 'table',
    canSelect: false,
    dataDropdownMenu: {
        orderBy: {
            date: { title: 'Sort by credit memo date' },
            number: { title: 'Sort by number' },
            customerName: { title: 'Sort by customer name' },
        },
    },
    rowDefinition: {
        title: {
            title: 'Sales credit',
            renderedAs: 'link',
            onClick(_id: string) {
                this.$.router.goTo('@sage/xtrem-sales/SalesCreditMemo', { _id });
            },
        },
        titleRight: {
            title: 'Customer',
            renderedAs: 'link',
            onClick(_id: string) {
                const node = this.getDataFromLineIndex(_id);
                this.$.router.goTo('@sage/xtrem-master-data/Customer', { _id: node.billToCustomer._id });
            },
        },
        line2: {
            title: 'Site',
            renderedAs: 'link',
            onClick(_id: string) {
                const node = this.getDataFromLineIndex(_id);
                this.$.router.goTo('@sage/xtrem-master-data/Site', { _id: node.site._id });
            },
        },
        line2Right: {
            title: 'Status',
            renderedAs: 'pill',
            displayOptions: {
                colorVariant(_id: string) {
                    const { status } = this.getDataFromLineIndex(_id);
                    switch (status) {
                        case 'error':
                            return 'negative';
                        case 'inProgress':
                            return 'warning';
                        default:
                            return 'neutral';
                    }
                },
            },
        },
        line3: { title: 'Credit memo date' },
        line3Right: { title: 'Due date' },
    },
    getQuery(args) {
        const filter: Filter<SalesCreditMemo> = { status: { _ne: 'posted' } };

        const orderBy: OrderBy<SalesCreditMemo> = {};
        switch (this.$.options.dataOptions?.orderBy) {
            case 'number':
                orderBy.number = 1;
                break;
            case 'creditMemoDate':
                orderBy.date = 1;
                break;
            case 'customerName':
                orderBy.billToCustomer = { name: 1 };
                break;
            default:
                orderBy.date = 1;
        }

        return {
            xtremSales: {
                salesCreditMemo: {
                    query: {
                        __args: {
                            filter: JSON.stringify(filter),
                            orderBy: JSON.stringify(orderBy),
                            ...(args?.after?.cursor && { after: args.after.cursor }),
                        },
                        edges: {
                            cursor: true,
                            node: {
                                _id: true,
                                number: true,
                                billToCustomer: {
                                    _id: true,
                                    name: true,
                                },
                                site: {
                                    _id: true,
                                    name: true,
                                },
                                status: true,
                                date: true,
                                dueDate: true,
                            },
                        },
                    },
                },
            },
        };
    },
})
export class UnpostedCredits extends ui.widgets.TableWidget {
    private getDataFromLineIndex(_id: string): SalesCreditMemo {
        return this.$.data.xtremSales.salesCreditMemo.query.edges.find(
            (edge: SalesCreditMemoQueryResponse) => edge.node._id === _id,
        ).node;
    }
}
