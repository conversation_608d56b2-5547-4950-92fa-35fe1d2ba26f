import * as ui from '@sage/xtrem-ui';

@ui.widgets.table<ReturnReasonAnalysis>({
    title: 'Return reason analysis',
    cacheLifespan: ui.widgets.WidgetCacheLifespan.day,
    content() {
        if (!this.$.data?.xtremSales?.salesReturnRequestLine?.queryAggregate) {
            return [];
        }
        const allEntries = this.$.data.xtremSales.salesReturnRequestLine.readAggregate._id.distinctCount;

        return this.$.data.xtremSales.salesReturnRequestLine.queryAggregate.edges.map(
            ({ node }: any, index: number) => ({
                _id: `${index}`,
                line2: node.group.reason.name ?? 'No reason',
                titleRight: `${node.values._id.distinctCount}`,
                line2Right: `${Math.round((node.values._id.distinctCount / allEntries) * 100)}%`,
            }),
        );
    },
    canSwitchViewMode: false,
    displayMode: 'card',
    canSelect: false,
    rowDefinition: {
        title: {
            title: '',
        },
        titleRight: {
            title: 'Returns',
        },
        line2: {
            title: 'Reason name',
            renderedAs: 'link',
            onClick(_id: string) {
                const node = this.getDataFromLineIndex(_id);
                this.$.router.goTo(
                    `@sage/xtrem-sales/SalesReturnRequest/${btoa(
                        JSON.stringify({
                            [ui.QUERY_PARAM_CUSTOM_MAIN_LIST_FILTER]: JSON.stringify({
                                lines: { _atLeast: 1, reason: { _id: node.group.reason._id } },
                            }),
                        }),
                    )}`,
                );
            },
        },
        line2Right: {
            title: '',
            renderedAs: 'pill',
            displayOptions: {
                colorVariant(_id: string) {
                    if (+_id > 29) {
                        return 'warning';
                    }
                    if (+_id > 19) {
                        return 'neutral';
                    }
                    if (+_id > 9) {
                        return 'positive';
                    }
                    return 'negative';
                },
            },
        },
    },
    getQuery() {
        return {
            xtremSales: {
                salesReturnRequestLine: {
                    readAggregate: {
                        _id: { distinctCount: true },
                    },
                    queryAggregate: {
                        edges: {
                            node: {
                                group: {
                                    reason: {
                                        name: true,
                                        _id: true,
                                    },
                                },
                                values: {
                                    _id: {
                                        distinctCount: true,
                                    },
                                },
                            },
                        },
                    },
                },
            },
        };
    },
})
export class ReturnReasonAnalysis extends ui.widgets.TableWidget {
    private getDataFromLineIndex(index: string): any {
        return this.$.data.xtremSales.salesReturnRequestLine.queryAggregate.edges[index].node;
    }
}
