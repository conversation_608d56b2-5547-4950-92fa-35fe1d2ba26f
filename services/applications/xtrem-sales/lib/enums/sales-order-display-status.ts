import { EnumDataType } from '@sage/xtrem-core';

export enum SalesOrderDisplayStatusEnum {
    quote,
    partiallyShipped,
    confirmed,
    shipped,
    closed,
    taxCalculationFailed,
}

export type SalesOrderDisplayStatus = keyof typeof SalesOrderDisplayStatusEnum;

export const salesOrderDisplayStatusDataType = new EnumDataType<SalesOrderDisplayStatus>({
    enum: SalesOrderDisplayStatusEnum,
    filename: __filename,
});
