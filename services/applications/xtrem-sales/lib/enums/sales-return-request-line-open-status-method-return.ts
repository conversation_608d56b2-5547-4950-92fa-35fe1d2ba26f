import { EnumDataType } from '@sage/xtrem-core';

export enum SalesReturnRequestLineOpenStatusMethodReturnEnum {
    parametersAreIncorrect = 1,
    salesReturnRequestLineIsAlreadyOpen = 2,
    salesReturnRequestLineIsReceived = 3,
    salesReturnRequestLineIsNowOpen = 4,
}

export type SalesReturnRequestLineOpenStatusMethodReturn =
    keyof typeof SalesReturnRequestLineOpenStatusMethodReturnEnum;

export const salesReturnRequestLineOpenStatusMethodReturnDataType =
    new EnumDataType<SalesReturnRequestLineOpenStatusMethodReturn>({
        enum: SalesReturnRequestLineOpenStatusMethodReturnEnum,
        filename: __filename,
    });
