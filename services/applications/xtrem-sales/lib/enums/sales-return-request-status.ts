import { EnumDataType } from '@sage/xtrem-core';

export enum SalesReturnRequestStatusEnum {
    draft = 1,
    pending = 2,
    inProgress = 3,
    confirmed = 4,
    closed = 5,
}

export type SalesReturnRequestStatus = keyof typeof SalesReturnRequestStatusEnum;

export const salesReturnRequestStatusDataType = new EnumDataType<SalesReturnRequestStatus>({
    enum: SalesReturnRequestStatusEnum,
    filename: __filename,
});
