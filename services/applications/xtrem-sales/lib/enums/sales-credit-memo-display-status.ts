import { EnumDataType } from '@sage/xtrem-core';

export enum SalesCreditMemoDisplayStatusEnum {
    draft,
    postingInProgress,
    error,
    posted,
    taxCalculationFailed,
    partiallyPaid,
    paid,
}

export type SalesCreditMemoDisplayStatus = keyof typeof SalesCreditMemoDisplayStatusEnum;

export const salesCreditMemoDisplayStatusDataType = new EnumDataType<SalesCreditMemoDisplayStatus>({
    enum: SalesCreditMemoDisplayStatusEnum,
    filename: __filename,
});
