import { EnumDataType } from '@sage/xtrem-core';

export enum SalesReturnRequestLineCloseStatusMethodReturnEnum {
    parametersAreIncorrect = 1,
    salesReturnRequestLineIsAlreadyClosed = 2,
    salesReturnRequestLineIsNowClosed = 3,
    salesReturnRequestHeaderIsNowClosed = 4,
    linkedSalesReturnReceiptIsNotClosed = 5,
}

export type SalesReturnRequestLineCloseStatusMethodReturn =
    keyof typeof SalesReturnRequestLineCloseStatusMethodReturnEnum;

export const salesReturnRequestLineCloseStatusMethodReturnDataType =
    new EnumDataType<SalesReturnRequestLineCloseStatusMethodReturn>({
        enum: SalesReturnRequestLineCloseStatusMethodReturnEnum,
        filename: __filename,
    });
