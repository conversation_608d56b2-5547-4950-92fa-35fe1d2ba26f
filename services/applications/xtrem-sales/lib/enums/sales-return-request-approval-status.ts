import { EnumDataType } from '@sage/xtrem-core';

export enum SalesReturnRequestApprovalStatusEnum {
    draft = 1,
    pendingApproval = 2,
    approved = 3,
    confirmed = 4,
    rejected = 5,
    changeRequested = 6,
}

export type SalesReturnRequestApprovalStatus = keyof typeof SalesReturnRequestApprovalStatusEnum;

export const salesReturnRequestApprovalStatusDataType = new EnumDataType<SalesReturnRequestApprovalStatus>({
    enum: SalesReturnRequestApprovalStatusEnum,
    filename: __filename,
});
