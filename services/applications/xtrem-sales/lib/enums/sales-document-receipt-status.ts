import { EnumDataType } from '@sage/xtrem-core';

export enum SalesDocumentReceiptStatusEnum {
    notReturned = 1,
    partiallyReturned = 2,
    returned = 3,
}

export type SalesDocumentReceiptStatus = keyof typeof SalesDocumentReceiptStatusEnum;

export const salesDocumentReceiptStatusDataType = new EnumDataType<SalesDocumentReceiptStatus>({
    enum: SalesDocumentReceiptStatusEnum,
    filename: __filename,
});
