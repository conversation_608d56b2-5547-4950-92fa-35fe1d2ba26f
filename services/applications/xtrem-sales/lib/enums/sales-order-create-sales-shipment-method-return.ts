import { EnumDataType } from '@sage/xtrem-core';

export enum SalesOrderCreateSalesShipmentMethodReturnEnum {
    parametersAreIncorrect = 1,
    salesOrderIsNotShipped = 2,
    salesOrderIsPartiallyShipped = 3,
    salesOrderIsShipped = 4,
}

export type SalesOrderCreateSalesShipmentMethodReturn = keyof typeof SalesOrderCreateSalesShipmentMethodReturnEnum;

export const salesOrderCreateSalesShipmentMethodReturnDataType =
    new EnumDataType<SalesOrderCreateSalesShipmentMethodReturn>({
        enum: SalesOrderCreateSalesShipmentMethodReturnEnum,
        filename: __filename,
    });
