import { EnumDataType } from '@sage/xtrem-core';

export enum SalesOrderConfirmMethodReturnEnum {
    parametersAreIncorrect = 1,
    salesOrderStatusIsNotDraft = 2,
    salesOrderIsConfirmed = 3,
}

export type SalesOrderConfirmMethodReturn = keyof typeof SalesOrderConfirmMethodReturnEnum;

export const salesOrderConfirmMethodReturnDataType = new EnumDataType<SalesOrderConfirmMethodReturn>({
    enum: SalesOrderConfirmMethodReturnEnum,
    filename: __filename,
});
