import { EnumDataType } from '@sage/xtrem-core';

export enum SalesReturnRequestDisplayStatusEnum {
    draft = 1,
    pendingApproval = 2,
    approved = 3,
    confirmed = 4,
    rejected = 5,
    partiallyReceived = 6,
    received = 7,
    closed = 8,
}

export type SalesReturnRequestDisplayStatus = keyof typeof SalesReturnRequestDisplayStatusEnum;

export const salesReturnRequestDisplayStatusDataType = new EnumDataType<SalesReturnRequestDisplayStatus>({
    enum: SalesReturnRequestDisplayStatusEnum,
    filename: __filename,
});
