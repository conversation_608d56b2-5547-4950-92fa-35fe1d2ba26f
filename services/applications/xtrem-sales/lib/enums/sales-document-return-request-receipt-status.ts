import { EnumDataType } from '@sage/xtrem-core';

export enum SalesDocumentReturnRequestReceiptStatusEnum {
    notReceived = 1,
    partiallyReceived = 2,
    received = 3,
}

export type SalesDocumentReturnRequestReceiptStatus = keyof typeof SalesDocumentReturnRequestReceiptStatusEnum;

export const salesDocumentReturnRequestReceiptStatusDataType =
    new EnumDataType<SalesDocumentReturnRequestReceiptStatus>({
        enum: SalesDocumentReturnRequestReceiptStatusEnum,
        filename: __filename,
    });
