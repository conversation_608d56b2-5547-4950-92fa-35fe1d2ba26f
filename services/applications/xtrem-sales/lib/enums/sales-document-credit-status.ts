import { EnumDataType } from '@sage/xtrem-core';

export enum SalesDocumentCreditStatusEnum {
    notCredited = 1,
    partiallyCredited = 2,
    credited = 3,
}

export type SalesDocumentCreditStatus = keyof typeof SalesDocumentCreditStatusEnum;

export const salesDocumentCreditStatusDataType = new EnumDataType<SalesDocumentCreditStatus>({
    enum: SalesDocumentCreditStatusEnum,
    filename: __filename,
});
