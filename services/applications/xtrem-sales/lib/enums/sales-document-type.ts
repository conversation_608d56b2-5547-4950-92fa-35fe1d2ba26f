import { EnumDataType } from '@sage/xtrem-core';

export enum SalesDocumentTypeEnum {
    salesOrder = 1,
    salesShipment = 2,
    salesInvoice = 3,
    salesReturnRequest = 4,
    salesCreditMemo = 5,
}

export type SalesDocumentType = keyof typeof SalesDocumentTypeEnum;

export const SalesDocumentTypeDataType = new EnumDataType<SalesDocumentType>({
    enum: SalesDocumentTypeEnum,
    filename: __filename,
});
