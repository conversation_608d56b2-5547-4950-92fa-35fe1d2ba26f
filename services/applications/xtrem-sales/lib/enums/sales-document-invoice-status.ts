import { EnumDataType } from '@sage/xtrem-core';

export enum SalesDocumentInvoiceStatusEnum {
    notInvoiced = 1,
    partiallyInvoiced = 2,
    invoiced = 3,
}

export type SalesDocumentInvoiceStatus = keyof typeof SalesDocumentInvoiceStatusEnum;

export const salesDocumentInvoiceStatusDataType = new EnumDataType<SalesDocumentInvoiceStatus>({
    enum: SalesDocumentInvoiceStatusEnum,
    filename: __filename,
});
