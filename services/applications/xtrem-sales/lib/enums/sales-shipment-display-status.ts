import { EnumDataType } from '@sage/xtrem-core';

export enum SalesShipmentDisplayStatusEnum {
    readyToProcess = 1,
    readyToShip = 2,
    shipped = 3,
    partiallyInvoiced = 4,
    invoiced = 5,
    postingInProgress = 6,
    error = 7,
}

export type SalesShipmentDisplayStatus = keyof typeof SalesShipmentDisplayStatusEnum;

export const salesShipmentDisplayStatusDataType = new EnumDataType<SalesShipmentDisplayStatus>({
    enum: SalesShipmentDisplayStatusEnum,
    filename: __filename,
});
