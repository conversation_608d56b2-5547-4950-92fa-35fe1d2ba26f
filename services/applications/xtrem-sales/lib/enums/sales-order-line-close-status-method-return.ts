import { EnumDataType } from '@sage/xtrem-core';

export enum SalesOrderLineCloseStatusMethodReturnEnum {
    parametersAreIncorrect = 1,
    salesOrderLineIsAlreadyClosed = 2,
    salesOrderLineIsNowClosed = 3,
    salesOrderHeaderIsNowClosed = 4,
}

export type SalesOrderLineCloseStatusMethodReturn = keyof typeof SalesOrderLineCloseStatusMethodReturnEnum;

export const salesOrderLineCloseStatusMethodReturnDataType = new EnumDataType<SalesOrderLineCloseStatusMethodReturn>({
    enum: SalesOrderLineCloseStatusMethodReturnEnum,
    filename: __filename,
});
