import { EnumDataType } from '@sage/xtrem-core';

export enum SalesReturnRequestCloseStatusMethodReturnEnum {
    parametersAreIncorrect = 1,
    salesReturnRequestIsAlreadyClosed = 2,
    salesReturnRequestIsNowClosed = 3,
    linkedSalesReturnReceiptIsNotClosed = 4,
}

export type SalesReturnRequestCloseStatusMethodReturn = keyof typeof SalesReturnRequestCloseStatusMethodReturnEnum;

export const salesReturnRequestCloseStatusMethodReturnDataType =
    new EnumDataType<SalesReturnRequestCloseStatusMethodReturn>({
        enum: SalesReturnRequestCloseStatusMethodReturnEnum,
        filename: __filename,
    });
