import { EnumDataType } from '@sage/xtrem-core';

export enum SalesOrderOpenStatusMethodReturnEnum {
    parametersAreIncorrect = 1,
    salesOrderIsShipped = 2,
    salesOrderIsAlreadyOpen = 3,
    salesOrderIsNowOpen = 4,
}

export type SalesOrderOpenStatusMethodReturn = keyof typeof SalesOrderOpenStatusMethodReturnEnum;

export const salesOrderOpenStatusMethodReturnDataType = new EnumDataType<SalesOrderOpenStatusMethodReturn>({
    enum: SalesOrderOpenStatusMethodReturnEnum,
    filename: __filename,
});
