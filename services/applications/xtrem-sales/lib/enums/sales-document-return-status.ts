import { EnumDataType } from '@sage/xtrem-core';

export enum SalesDocumentReturnStatusEnum {
    noReturnRequested = 1,
    returnPartiallyRequested = 2,
    returnRequested = 3,
}

export type SalesDocumentReturnStatus = keyof typeof SalesDocumentReturnStatusEnum;

export const salesDocumentReturnStatusDataType = new EnumDataType<SalesDocumentReturnStatus>({
    enum: SalesDocumentReturnStatusEnum,
    filename: __filename,
});
