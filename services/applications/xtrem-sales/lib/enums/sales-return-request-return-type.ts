import { EnumDataType } from '@sage/xtrem-core';

export enum SalesReturnRequestReturnTypeEnum {
    receiptAndCreditMemo = 1,
    creditMemo = 2,
    receiptAndNoCreditMemo = 3,
}

export type SalesReturnRequestReturnType = keyof typeof SalesReturnRequestReturnTypeEnum;

export const salesReturnRequestReturnTypeDataType = new EnumDataType<SalesReturnRequestReturnType>({
    enum: SalesReturnRequestReturnTypeEnum,
    filename: __filename,
});
