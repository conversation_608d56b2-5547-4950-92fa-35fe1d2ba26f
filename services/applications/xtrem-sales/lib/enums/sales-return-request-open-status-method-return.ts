import { EnumDataType } from '@sage/xtrem-core';

export enum SalesReturnRequestOpenStatusMethodReturnEnum {
    parametersAreIncorrect = 1,
    salesReturnRequestIsReceived = 2,
    salesReturnRequestIsAlreadyOpen = 3,
    salesReturnRequestIsNowOpen = 4,
}

export type SalesReturnRequestOpenStatusMethodReturn = keyof typeof SalesReturnRequestOpenStatusMethodReturnEnum;

export const salesReturnRequestOpenStatusMethodReturnDataType =
    new EnumDataType<SalesReturnRequestOpenStatusMethodReturn>({
        enum: SalesReturnRequestOpenStatusMethodReturnEnum,
        filename: __filename,
    });
