import { EnumDataType } from '@sage/xtrem-core';

export enum SalesOriginDocumentTypeEnum {
    direct = 1,
    shipment = 2,
    order = 3,
    invoice = 4,
    return = 5,
}

export type SalesOriginDocumentType = keyof typeof SalesOriginDocumentTypeEnum;

export const salesOriginDocumentTypeDataType = new EnumDataType<SalesOriginDocumentType>({
    enum: SalesOriginDocumentTypeEnum,
    filename: __filename,
});
