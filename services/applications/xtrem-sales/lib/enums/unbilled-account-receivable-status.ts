import { EnumDataType } from '@sage/xtrem-core';

export enum UnbilledAccountReceivableStatusEnum {
    inProgress,
    completed,
    draft,
    error,
}

export type UnbilledAccountReceivableStatus = keyof typeof UnbilledAccountReceivableStatusEnum;

export const unbilledAccountReceivableStatusDataType = new EnumDataType<UnbilledAccountReceivableStatus>({
    enum: UnbilledAccountReceivableStatusEnum,
    filename: __filename,
});
