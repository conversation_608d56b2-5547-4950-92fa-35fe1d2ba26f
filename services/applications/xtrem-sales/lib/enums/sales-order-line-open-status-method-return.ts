import { EnumDataType } from '@sage/xtrem-core';

export enum SalesOrderLineOpenStatusMethodReturnEnum {
    parametersAreIncorrect = 1,
    salesOrderLineIsAlreadyOpen = 2,
    salesOrderLineIsShipped = 3,
    salesOrderLineIsNowOpen = 4,
}

export type SalesOrderLineOpenStatusMethodReturn = keyof typeof SalesOrderLineOpenStatusMethodReturnEnum;

export const salesOrderLineOpenStatusMethodReturnDataType = new EnumDataType<SalesOrderLineOpenStatusMethodReturn>({
    enum: SalesOrderLineOpenStatusMethodReturnEnum,
    filename: __filename,
});
