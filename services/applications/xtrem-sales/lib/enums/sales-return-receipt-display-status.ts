import { EnumDataType } from '@sage/xtrem-core';

export enum SalesReturnReceiptDisplayStatusEnum {
    draft = 1,
    postingInProgress = 2,
    error = 3,
    closed = 4,
}

export type SalesReturnReceiptDisplayStatus = keyof typeof SalesReturnReceiptDisplayStatusEnum;

export const salesReturnReceiptDisplayStatusDataType = new EnumDataType<SalesReturnReceiptDisplayStatus>({
    enum: SalesReturnReceiptDisplayStatusEnum,
    filename: __filename,
});
