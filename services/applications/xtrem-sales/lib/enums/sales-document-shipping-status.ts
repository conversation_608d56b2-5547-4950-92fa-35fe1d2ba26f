import { EnumDataType } from '@sage/xtrem-core';

export enum SalesDocumentShippingStatusEnum {
    notShipped = 1,
    partiallyShipped = 2,
    shipped = 3,
}

export type SalesDocumentShippingStatus = keyof typeof SalesDocumentShippingStatusEnum;

export const salesDocumentShippingStatusDataType = new EnumDataType<SalesDocumentShippingStatus>({
    enum: SalesDocumentShippingStatusEnum,
    filename: __filename,
});
