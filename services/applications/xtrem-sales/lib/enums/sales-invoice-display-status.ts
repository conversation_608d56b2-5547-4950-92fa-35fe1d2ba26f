import { EnumDataType } from '@sage/xtrem-core';

export enum SalesInvoiceDisplayStatusEnum {
    draft,
    postingInProgress,
    posted,
    partiallyCredited,
    credited,
    taxCalculationFailed,
    error,
    partiallyPaid,
    paid,
}

export type SalesInvoiceDisplayStatus = keyof typeof SalesInvoiceDisplayStatusEnum;

export const salesInvoiceDisplayStatusDataType = new EnumDataType<SalesInvoiceDisplayStatus>({
    enum: SalesInvoiceDisplayStatusEnum,
    filename: __filename,
});
