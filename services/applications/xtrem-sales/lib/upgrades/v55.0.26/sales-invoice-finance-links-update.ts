import { CustomSqlAction } from '@sage/xtrem-system';

export const salesInvoiceFinanceLinksUpdate = new CustomSqlAction({
    description: 'Update references for sales invoice on finance tables',
    fixes: { tables: ['finance_transaction', 'finance_transaction_line', 'accounting_staging', 'base_open_item'] },
    maxAllowedDurationMs: 600_000,
    body: async helper => {
        await helper.executeSql(`
            DO $$ DECLARE
                siSoRecord RECORD;
            BEGIN
                FOR siSoRecord IN (
                    SELECT
                        bd._tenant_id,
                        bd._id,
                        bd.number,
                        bd._constructor as document_type
                    FROM ${helper.schemaName}.base_document bd
                    WHERE bd._constructor = 'SalesInvoice'
                )
                LOOP
                    UPDATE ${helper.schemaName}.finance_transaction
                    SET source_document_sys_id = siSoRecord._id
                    WHERE source_document_number = siSoRecord.number
                      AND _tenant_id = siSoRecord._tenant_id
                      AND source_document_type = 'salesInvoice'::${helper.schemaName}.source_document_type_enum;
                    UPDATE ${helper.schemaName}.finance_transaction_line
                    SET source_document_sys_id = siSoRecord._id
                    WHERE source_document_number = siSoRecord.number
                      AND _tenant_id = siSoRecord._tenant_id
                      AND source_document_type = 'salesInvoice'::${helper.schemaName}.source_document_type_enum;
                    UPDATE ${helper.schemaName}.finance_transaction
                    SET document_sys_id = siSoRecord._id
                    WHERE document_number = siSoRecord.number
                      AND _tenant_id = siSoRecord._tenant_id
                      AND document_type = 'salesInvoice'::${helper.schemaName}.finance_document_type_enum;
                    UPDATE ${helper.schemaName}.accounting_staging
                    SET document_sys_id = siSoRecord._id
                    WHERE document_number = siSoRecord.number
                      AND _tenant_id = siSoRecord._tenant_id
                      AND document_type = 'salesInvoice'::${helper.schemaName}.finance_document_type_enum;
                    UPDATE ${helper.schemaName}.base_open_item
                    SET document_sys_id = siSoRecord._id
                    WHERE document_number = siSoRecord.number
                      AND _tenant_id = siSoRecord._tenant_id
                      AND document_type = 'salesInvoice'::${helper.schemaName}.finance_document_type_enum;
                END LOOP;
            END $$;`);
    },
});
