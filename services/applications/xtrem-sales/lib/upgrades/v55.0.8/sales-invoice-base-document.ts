// base_business_relation
import { CustomSqlAction } from '@sage/xtrem-system';

export const salesInvoiceBaseDocument = new CustomSqlAction({
    description: 'Move the sales invoice to the base_document table',
    fixes: { tables: ['sales_invoice'] },
    body: async helper => {
        await helper.executeSql(`
            DO $$ DECLARE
                siRecord RECORD;
                base_id ${helper.schemaName}.base_document._id%TYPE := 1;
                start_id ${helper.schemaName}.base_document._id%TYPE :=1;
                financial_site_id ${helper.schemaName}.site._id%TYPE;
            BEGIN

                -- There we are dropping the constraints to avoid the constraint violation
                SET CONSTRAINTS ALL DEFERRED;

                start_id  = (SELECT COALESCE(MAX(_id), 0) FROM ${helper.schemaName}.sales_invoice);

                -- We are setting the sequence to the max id of the base_document if it is greater than the current sequence value
                IF start_id > COALESCE((SELECT last_value FROM ${helper.schemaName}.base_document__id_seq) , 0) THEN
                    EXECUTE 'ALTER SEQUENCE ${helper.schemaName}.base_document__id_seq RESTART WITH ' || start_id+1;
                END IF;

                FOR siRecord IN ( SELECT si._id, si._tenant_id, si.number,
                    si.status::text::${helper.schemaName}.base_status_enum,
                    si.display_status::text::${helper.schemaName}.base_display_status_enum, si.site,
                    si.date, si.internal_note, si.is_transfer_header_note, si.is_transfer_line_note,
                    si._create_user, si._create_stamp, si._update_user, si._update_stamp ,
                    site.is_finance, site.financial_site, c.currency
                    FROM ${helper.schemaName}.sales_invoice si
                    INNER JOIN ${helper.schemaName}.site AS site ON site._id = si.site AND si._tenant_id = site._tenant_id
                    INNER JOIN ${helper.schemaName}.company AS c ON c._id = site.legal_company AND c._tenant_id = site._tenant_id )

                LOOP
                    financial_site_id := CASE WHEN siRecord.is_finance = true THEN siRecord.site ELSE siRecord.financial_site END;

                    INSERT INTO ${helper.schemaName}.base_document( _tenant_id, _constructor, number, status, approval_status,
                    display_status, date, is_printed, is_sent, site, currency, financial_site,
                    internal_note, external_note, is_external_note, is_transfer_header_note, is_transfer_line_note, text,
                    _create_user, _create_stamp, _update_user, _update_stamp)
                    VALUES (siRecord._tenant_id, 'SalesInvoice', siRecord.number, siRecord.status, 'draft', siRecord.display_status,
                    siRecord.date, FALSE, FALSE, siRecord.site, siRecord.currency, financial_site_id,
                    siRecord.internal_note, '', FALSE, siRecord.is_transfer_header_note,
                    siRecord.is_transfer_line_note, '',
                    siRecord._create_user, siRecord._create_stamp, siRecord._update_user, siRecord._update_stamp)
                    RETURNING _id INTO base_id;

                    UPDATE ${helper.schemaName}.sales_invoice SET _id = base_id
                        WHERE _id = siRecord._id AND sales_invoice._tenant_id = siRecord._tenant_id;

                    UPDATE ${helper.schemaName}.sales_invoice_line SET document = base_id
                        WHERE document = siRecord._id AND sales_invoice_line._tenant_id = siRecord._tenant_id;

                    UPDATE ${helper.schemaName}.sales_invoice_tax SET document = base_id
                        WHERE document = siRecord._id AND sales_invoice_tax._tenant_id = siRecord._tenant_id;

                    UPDATE ${helper.schemaName}.attachment_association set source_node_id = base_id
                        where source_node_id = siRecord._id and source_node_name = 'SalesInvoice';

                END LOOP;
            END $$;`);
    },
});
