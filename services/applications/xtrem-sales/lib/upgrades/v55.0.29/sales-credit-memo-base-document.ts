// base_business_relation
import { UnsafeCustomSqlAction } from '@sage/xtrem-system';

export const salesCreditMemoBaseDocument = new UnsafeCustomSqlAction({
    description: 'Move the sales credit memo to the base_document table',
    maxAllowedDurationMs: 600_000,
    body: async helper => {
        await helper.executeSql(`
            DO $$ DECLARE
                scmRecord RECORD;
                base_id ${helper.schemaName}.base_document._id%TYPE := 1;
                start_id ${helper.schemaName}.base_document._id%TYPE :=1;
                financial_site_id ${helper.schemaName}.site._id%TYPE;
            BEGIN

                -- There we are dropping the constraints to avoid the constraint violation
                SET CONSTRAINTS ALL DEFERRED;

                start_id  = (SELECT COALESCE(MAX(_id), 0) FROM ${helper.schemaName}.sales_credit_memo);

                -- We are setting the sequence to the max id of the base_document if it is greater than the current sequence value
                IF start_id > COALESCE((SELECT last_value FROM ${helper.schemaName}.base_document__id_seq) , 0) THEN
                    EXECUTE 'ALTER SEQUENCE ${helper.schemaName}.base_document__id_seq RESTART WITH ' || start_id+1;
                END IF;

                FOR scmRecord IN (SELECT
                    scm._id,
                    scm._tenant_id,
                    scm.number,
                    scm.status::text::${helper.schemaName}.base_status_enum,
                    scm.display_status::text::${helper.schemaName}.base_display_status_enum,
                    scm.site,
                    scm.date,
                    scm.internal_note,
                    scm.external_note,
                    scm.is_external_note,
                    scm._create_user,
                    scm._create_stamp,
                    scm._update_user,
                    scm._update_stamp ,
                    site.is_finance,
                    site.financial_site,
                    scm.currency,
                    scm.is_printed,
                    scm.is_sent
                    FROM ${helper.schemaName}.sales_credit_memo scm
                    INNER JOIN ${helper.schemaName}.site AS site ON site._id = scm.site AND scm._tenant_id = site._tenant_id )

                LOOP
                    financial_site_id := CASE WHEN scmRecord.is_finance = true THEN scmRecord.site ELSE scmRecord.financial_site END;

                    INSERT INTO ${helper.schemaName}.base_document(
                        _tenant_id,
                        _constructor,
                        number,
                        status,
                        approval_status,
                        display_status,

                        date,
                        is_printed,
                        is_sent,
                        site,
                        currency,
                        financial_site,

                        internal_note,
                        external_note,
                        is_external_note,
                        is_transfer_header_note,
                        is_transfer_line_note,
                        text,

                        _create_user,
                        _create_stamp,
                        _update_user,
                        _update_stamp
                    )
                    VALUES (
                        scmRecord._tenant_id,
                        'SalesCreditMemo',
                        scmRecord.number,
                        scmRecord.status,
                        'draft',
                        scmRecord.display_status,

                        scmRecord.date,
                        scmRecord.is_printed,
                        scmRecord.is_sent,
                        scmRecord.site,
                        scmRecord.currency,
                        financial_site_id,

                        scmRecord.internal_note,
                        scmRecord.external_note,
                        scmRecord.is_external_note,
                        FALSE,
                        FALSE,
                        '',

                        scmRecord._create_user,
                        scmRecord._create_stamp,
                        scmRecord._update_user,
                        scmRecord._update_stamp
                    )
                    RETURNING _id INTO base_id;

                    UPDATE ${helper.schemaName}.sales_credit_memo SET _id = base_id
                        WHERE _id = scmRecord._id AND sales_credit_memo._tenant_id = scmRecord._tenant_id;

                    UPDATE ${helper.schemaName}.sales_credit_memo_line SET document = base_id
                        WHERE document = scmRecord._id AND sales_credit_memo_line._tenant_id = scmRecord._tenant_id;

                    UPDATE ${helper.schemaName}.sales_credit_memo_tax SET document = base_id
                        WHERE document = scmRecord._id AND sales_credit_memo_tax._tenant_id = scmRecord._tenant_id;

                    UPDATE ${helper.schemaName}.attachment_association set source_node_id = base_id
                        where source_node_id = scmRecord._id and source_node_name = 'SalesCreditMemo';

                    UPDATE ${helper.schemaName}.finance_transaction SET source_document_sys_id = base_id
                        WHERE source_document_sys_id = scmRecord._id AND _tenant_id = scmRecord._tenant_id
                        AND source_document_type = 'salesCreditMemo'::${helper.schemaName}.source_document_type_enum;

                    UPDATE ${helper.schemaName}.finance_transaction_line SET source_document_sys_id = base_id
                        WHERE source_document_sys_id = scmRecord._id AND _tenant_id = scmRecord._tenant_id
                        AND source_document_type = 'salesCreditMemo'::${helper.schemaName}.source_document_type_enum;

                    UPDATE ${helper.schemaName}.finance_transaction SET document_sys_id = base_id
                        WHERE document_sys_id = scmRecord._id AND _tenant_id = scmRecord._tenant_id
                        AND document_type = 'salesCreditMemo'::${helper.schemaName}.finance_document_type_enum;

                    UPDATE ${helper.schemaName}.accounting_staging SET document_sys_id = base_id
                    WHERE document_sys_id = scmRecord._id AND _tenant_id = scmRecord._tenant_id
                    AND document_type = 'salesCreditMemo'::${helper.schemaName}.finance_document_type_enum;

                    UPDATE ${helper.schemaName}.base_open_item SET document_sys_id = base_id
                        WHERE document_sys_id = scmRecord._id AND _tenant_id = scmRecord._tenant_id
                        AND document_type = 'salesCreditMemo'::${helper.schemaName}.finance_document_type_enum;
                END LOOP;
            END $$;`);
    },
});
