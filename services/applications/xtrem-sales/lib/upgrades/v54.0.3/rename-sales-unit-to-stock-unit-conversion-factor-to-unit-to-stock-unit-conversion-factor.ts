import { SchemaRenamePropertyAction } from '@sage/xtrem-system';
import {
    SalesCreditMemoLine,
    SalesInvoiceLine,
    SalesOrderLine,
    SalesReturnReceiptLine,
    SalesReturnRequestLine,
    SalesShipmentLine,
} from '../../nodes';

export const salesCreditMemoLine = new SchemaRenamePropertyAction({
    node: () => SalesCreditMemoLine,
    oldPropertyName: 'salesUnitToStockUnitConversionFactor',
    newPropertyName: 'unitToStockUnitConversionFactor',
});

export const salesInvoiceLine = new SchemaRenamePropertyAction({
    node: () => SalesInvoiceLine,
    oldPropertyName: 'salesUnitToStockUnitConversionFactor',
    newPropertyName: 'unitToStockUnitConversionFactor',
});

export const salesOrderLine = new SchemaRenamePropertyAction({
    node: () => SalesOrderLine,
    oldPropertyName: 'salesUnitToStockUnitConversionFactor',
    newPropertyName: 'unitToStockUnitConversionFactor',
});

export const salesReturnRequestLine = new SchemaRenamePropertyAction({
    node: () => SalesReturnRequestLine,
    oldPropertyName: 'salesUnitToStockUnitConversionFactor',
    newPropertyName: 'unitToStockUnitConversionFactor',
});

export const salesShipmentLine = new SchemaRenamePropertyAction({
    node: () => SalesShipmentLine,
    oldPropertyName: 'salesUnitToStockUnitConversionFactor',
    newPropertyName: 'unitToStockUnitConversionFactor',
});

export const salesReturnReceiptLine = new SchemaRenamePropertyAction({
    node: () => SalesReturnReceiptLine,
    oldPropertyName: 'salesUnitToStockUnitConversionFactor',
    newPropertyName: 'unitToStockUnitConversionFactor',
});
