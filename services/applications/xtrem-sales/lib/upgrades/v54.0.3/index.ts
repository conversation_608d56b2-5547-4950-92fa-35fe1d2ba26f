import { UpgradeSuite } from '@sage/xtrem-system';
import * as renameSalesUnitToStockUnitConversionFactorToUnitToStockUnitConversionFactor from './rename-sales-unit-to-stock-unit-conversion-factor-to-unit-to-stock-unit-conversion-factor';

export const upgradeSuite = new UpgradeSuite({
    actions: [
        renameSalesUnitToStockUnitConversionFactorToUnitToStockUnitConversionFactor.salesCreditMemoLine,
        renameSalesUnitToStockUnitConversionFactorToUnitToStockUnitConversionFactor.salesInvoiceLine,
        renameSalesUnitToStockUnitConversionFactorToUnitToStockUnitConversionFactor.salesOrderLine,
        renameSalesUnitToStockUnitConversionFactorToUnitToStockUnitConversionFactor.salesReturnRequestLine,
        renameSalesUnitToStockUnitConversionFactorToUnitToStockUnitConversionFactor.salesShipmentLine,
        renameSalesUnitToStockUnitConversionFactorToUnitToStockUnitConversionFactor.salesReturnReceiptLine,
    ],
});
