// base_business_relation
import { UnsafeCustomSqlAction } from '@sage/xtrem-system';

export const salesShipmentBaseDocument = new UnsafeCustomSqlAction({
    description: 'Move the sales shipment to the base_document table',
    maxAllowedDurationMs: 900_000,
    body: async helper => {
        await helper.executeSql(`
            DO $$ DECLARE
                shipRecord RECORD;
                base_id ${helper.schemaName}.base_document._id%TYPE := 1;
                start_id ${helper.schemaName}.base_document._id%TYPE :=1;
                financial_site_id ${helper.schemaName}.site._id%TYPE;
            BEGIN

                -- There we are dropping the constraints to avoid the constraint violation
                SET CONSTRAINTS ALL DEFERRED;

                start_id  = (SELECT COALESCE(MAX(_id), 0) FROM ${helper.schemaName}.sales_shipment);

                -- We are setting the sequence to the max id of the base_document if it is greater than the current sequence value
                IF start_id > COALESCE((SELECT last_value FROM ${helper.schemaName}.base_document__id_seq) , 0) THEN
                    EXECUTE 'ALTER SEQUENCE ${helper.schemaName}.base_document__id_seq RESTART WITH ' || start_id+1;
                END IF;

                FOR shipRecord IN (SELECT
                    shp._id,
                    shp._tenant_id,
                    shp.number,
                    shp.status::text::${helper.schemaName}.base_status_enum,
                    shp.display_status::text::${helper.schemaName}.base_display_status_enum,
                    shp.site,
                    shp.stock_site,
                    shp.date,
                    shp.internal_note,
                    shp.external_note,
                    shp.is_external_note,
                    shp.is_transfer_header_note,
                    shp.is_transfer_line_note,
                    shp._create_user,
                    shp._create_stamp,
                    shp._update_user,
                    shp._update_stamp ,
                    site.is_finance,
                    site.financial_site,
                    shp.currency,
                    shp.is_printed
                    FROM ${helper.schemaName}.sales_shipment shp
                    INNER JOIN ${helper.schemaName}.site AS site ON site._id = shp.site AND shp._tenant_id = site._tenant_id )

                LOOP
                    financial_site_id := CASE WHEN shipRecord.is_finance = true THEN shipRecord.site ELSE shipRecord.financial_site END;

                    INSERT INTO ${helper.schemaName}.base_document(
                        _tenant_id,
                        _constructor,
                        number,
                        status,
                        approval_status,
                        display_status,

                        date,
                        is_printed,
                        is_sent,

                        site,
                        stock_site,
                        currency,
                        financial_site,

                        internal_note,
                        external_note,
                        is_external_note,
                        is_transfer_header_note,
                        is_transfer_line_note,
                        text,

                        _create_user,
                        _create_stamp,
                        _update_user,
                        _update_stamp
                    )
                    VALUES (
                        shipRecord._tenant_id,
                        'SalesShipment',
                        shipRecord.number,
                        shipRecord.status,
                        'draft',
                        shipRecord.display_status,

                        shipRecord.date,
                        shipRecord.is_printed,
                        FALSE,

                        shipRecord.site,
                        shipRecord.stock_site,
                        shipRecord.currency,
                        financial_site_id,

                        shipRecord.internal_note,
                        shipRecord.external_note,
                        shipRecord.is_external_note,
                        shipRecord.is_transfer_header_note,
                        shipRecord.is_transfer_line_note,
                        '',

                        shipRecord._create_user,
                        shipRecord._create_stamp,
                        shipRecord._update_user,
                        shipRecord._update_stamp
                    )
                    RETURNING _id INTO base_id;

                    UPDATE ${helper.schemaName}.sales_shipment SET _id = base_id
                        WHERE _id = shipRecord._id AND sales_shipment._tenant_id = shipRecord._tenant_id;

                    UPDATE ${helper.schemaName}.sales_shipment_line SET document = base_id
                        WHERE document = shipRecord._id AND sales_shipment_line._tenant_id = shipRecord._tenant_id;

                    UPDATE ${helper.schemaName}.attachment_association set source_node_id = base_id
                        where source_node_id = shipRecord._id and source_node_name = 'SalesShipment';

                    UPDATE ${helper.schemaName}.finance_transaction SET source_document_sys_id = base_id
                        WHERE source_document_sys_id = shipRecord._id AND _tenant_id = shipRecord._tenant_id
                        AND source_document_type = 'salesShipment'::${helper.schemaName}.source_document_type_enum;

                    UPDATE ${helper.schemaName}.finance_transaction_line SET source_document_sys_id = base_id
                        WHERE source_document_sys_id = shipRecord._id AND _tenant_id = shipRecord._tenant_id
                        AND source_document_type = 'salesShipment'::${helper.schemaName}.source_document_type_enum;

                    UPDATE ${helper.schemaName}.finance_transaction SET document_sys_id = base_id
                        WHERE document_sys_id = shipRecord._id AND _tenant_id = shipRecord._tenant_id
                        AND document_type = 'salesShipment'::${helper.schemaName}.finance_document_type_enum;

                    UPDATE ${helper.schemaName}.accounting_staging SET document_sys_id = base_id
                    WHERE document_sys_id = shipRecord._id AND _tenant_id = shipRecord._tenant_id
                    AND document_type = 'salesShipment'::${helper.schemaName}.finance_document_type_enum;

                    UPDATE ${helper.schemaName}.base_open_item SET document_sys_id = base_id
                        WHERE document_sys_id = shipRecord._id AND _tenant_id = shipRecord._tenant_id
                        AND document_type = 'salesShipment'::${helper.schemaName}.finance_document_type_enum;

                    UPDATE ${helper.schemaName}.unbilled_account_receivable_result_line SET shipment_internal_id = base_id
                        WHERE shipment_internal_id = shipRecord._id AND _tenant_id = shipRecord._tenant_id;
                END LOOP;
            END $$;`);
    },
});
