import { SchemaEnumPropertyDatatypeUpgradeAction } from '@sage/xtrem-system';
import * as xtremSales from '../..';

export const fixSalesCreditMemoPenaltyPaymentTypeProperty = new SchemaEnumPropertyDatatypeUpgradeAction({
    description: 'Change penaltyPaymentType column type on sales credit memo',
    node: () => xtremSales.nodes.SalesCreditMemo,
    propertyName: 'penaltyPaymentType',
    valuesMapping: {
        percentage: 'percentage',
        amount: 'amount',
    },
});

export const fixSalesCreditMemoDiscountPaymentTypeProperty = new SchemaEnumPropertyDatatypeUpgradeAction({
    description: 'Change discountPaymentType column type on sales credit memo',
    node: () => xtremSales.nodes.SalesCreditMemo,
    propertyName: 'discountPaymentType',
    valuesMapping: {
        percentage: 'percentage',
        amount: 'amount',
    },
});

export const fixSalesInvoicePenaltyPaymentTypeProperty = new SchemaEnumPropertyDatatypeUpgradeAction({
    description: 'Change penaltyPaymentType column type on sales invoice',
    node: () => xtremSales.nodes.SalesInvoice,
    propertyName: 'penaltyPaymentType',
    valuesMapping: {
        percentage: 'percentage',
        amount: 'amount',
    },
});

export const fixSalesInvoiceDiscountPaymentTypeProperty = new SchemaEnumPropertyDatatypeUpgradeAction({
    description: 'Change discountPaymentType column type on sales invoice',
    node: () => xtremSales.nodes.SalesInvoice,
    propertyName: 'discountPaymentType',
    valuesMapping: {
        percentage: 'percentage',
        amount: 'amount',
    },
});
