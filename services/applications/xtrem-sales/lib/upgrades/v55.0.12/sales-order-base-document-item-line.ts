import { CustomSqlAction } from '@sage/xtrem-system';

export const salesOrderBaseDocumentItemLine = new CustomSqlAction({
    description: 'Move sales order line to base document item line',
    body: async helper => {
        await helper.executeSql(`
            INSERT INTO ${helper.schemaName}.base_document_item_line (
                _tenant_id,
                _id,
                _constructor,
                _sort_value,
                document,
                status,
                site,
                site_linked_address,
                item,
                item_description,
                stock_site,
                stock_site_linked_address,
                stock_unit,
                unit,
                quantity,
                internal_note,
                external_note,
                is_external_note,
                stored_dimensions,
                stored_attributes,
                analytical_data,
                quantity_in_stock_unit,
                unit_to_stock_unit_conversion_factor,
                _custom_data
            )
            SELECT
                line._tenant_id,
                line._id,
                'SalesOrderLine',
                line._sort_value,
                line.document,
                line.status::text::${helper.schemaName}.base_status_enum,
                line.site,
                addressBase._id,
                line.item,
                line.item_description,
                line.stock_site,
                line.stock_site_linked_address,
                line.stock_unit,
                line.unit,
                line.quantity,
                line.internal_note,
                line.external_note,
                line.is_external_note,
                line.stored_dimensions,
                line.stored_attributes,
                line.analytical_data,
                line.quantity_in_stock_unit,
                line.unit_to_stock_unit_conversion_factor,
                line._custom_data
            FROM ${helper.schemaName}.sales_order_line AS line
            INNER JOIN ${helper.schemaName}.site AS site
                ON line.site = site._id
                AND line._tenant_id = site._tenant_id
            INNER JOIN ${helper.schemaName}.business_entity_address AS businessEntityAddress
                ON site.primary_address = businessEntityAddress._id
                AND site._tenant_id = businessEntityAddress._tenant_id
            INNER JOIN ${helper.schemaName}.address_base AS addressBase
                ON businessEntityAddress._id = addressBase._id
                AND businessEntityAddress._tenant_id = addressBase._tenant_id
                AND addressBase._constructor = 'BusinessEntityAddress'
            INNER JOIN ${helper.schemaName}.address AS address
                ON addressBase.address = address._id
                AND addressBase._tenant_id = address._tenant_id;
`);
    },
});
