import { CustomSqlAction } from '@sage/xtrem-system';

export const salesOrderBaseDocument = new CustomSqlAction({
    maxAllowedDurationMs: 600_000,
    description: 'Move the sales order to the base_document table',
    fixes: { tables: ['sales_order'] },
    body: async helper => {
        await helper.executeSql(`
      DO $$
      DECLARE
        soRecord RECORD;
        base_id ${helper.schemaName}.base_document._id%TYPE := 1;
        start_id ${helper.schemaName}.base_document._id%TYPE := 1;
        financial_site_id ${helper.schemaName}.site._id%TYPE;
      BEGIN
        -- Defer constraints to avoid violations during updates
        SET CONSTRAINTS ALL DEFERRED;
        -- Get the max ID from sales_order table
        start_id := (
          SELECT COALESCE(MAX(_id), 0)
          FROM ${helper.schemaName}.sales_order
        );
        -- Update the base_document sequence if needed
        IF start_id > COALESCE(
          (SELECT last_value FROM ${helper.schemaName}.base_document__id_seq),
          0
        ) THEN
          EXECUTE 'ALTER SEQUENCE ${helper.schemaName}.base_document__id_seq RESTART WITH ' || (start_id + 1);
        END IF;
        -- Iterate over sales orders to insert into base_document
        FOR soRecord IN (
          SELECT
            so._id,
            so._tenant_id,
            so.number,
            so.status::text::${helper.schemaName}.base_status_enum,
            so.display_status::text::${helper.schemaName}.base_display_status_enum,
            so.site,
            so.date AS order_date,
            so.internal_note,
            so.is_transfer_header_note,
            so.is_transfer_line_note,
            so._create_user,
            so._create_stamp,
            so._update_user,
            so._update_stamp,
            site.is_finance,
            site.financial_site,
            c.currency
          FROM ${helper.schemaName}.sales_order so
          INNER JOIN ${helper.schemaName}.site AS site
            ON site._id = so.site AND so._tenant_id = site._tenant_id
          INNER JOIN ${helper.schemaName}.company AS c
            ON c._id = site.legal_company AND c._tenant_id = site._tenant_id
        )
        LOOP
          financial_site_id := CASE
            WHEN soRecord.is_finance = TRUE THEN soRecord.site
            ELSE soRecord.financial_site
          END;
          -- Insert into base_document
          INSERT INTO ${helper.schemaName}.base_document (
            _tenant_id,
            _constructor,
            number,
            status,
            approval_status,
            display_status,
            date,
            is_printed,
            is_sent,
            site,
            currency,
            financial_site,
            internal_note,
            external_note,
            is_external_note,
            is_transfer_header_note,
            is_transfer_line_note,
            text,
            _create_user,
            _create_stamp,
            _update_user,
            _update_stamp
          )
          VALUES (
            soRecord._tenant_id,
            'SalesOrder',
            soRecord.number,
            soRecord.status,
            'draft',
            soRecord.display_status,
            soRecord.order_date,
            FALSE,
            FALSE,
            soRecord.site,
            soRecord.currency,
            financial_site_id,
            soRecord.internal_note,
            '',
            FALSE,
            soRecord.is_transfer_header_note,
            soRecord.is_transfer_line_note,
            '',
            soRecord._create_user,
            soRecord._create_stamp,
            soRecord._update_user,
            soRecord._update_stamp
          )
          RETURNING _id INTO base_id;
          -- Update sales_order with new base ID
          UPDATE ${helper.schemaName}.sales_order
          SET _id = base_id
          WHERE _id = soRecord._id
            AND _tenant_id = soRecord._tenant_id;
          -- Update sales_order_line with new document ID
          UPDATE ${helper.schemaName}.sales_order_line
          SET document = base_id
          WHERE document = soRecord._id
            AND _tenant_id = soRecord._tenant_id;
          -- Update sales_order_tax with new document ID
          UPDATE ${helper.schemaName}.sales_order_tax
          SET document = base_id
          WHERE document = soRecord._id
            AND _tenant_id = soRecord._tenant_id;
        -- Update sales_order_tax with new document ID
          UPDATE ${helper.schemaName}.proforma_invoice
          SET sales_order = base_id
          WHERE sales_order = soRecord._id
            AND _tenant_id = soRecord._tenant_id;
          -- Update attachment association with new source node ID
          UPDATE ${helper.schemaName}.attachment_association
          SET source_node_id = base_id
          WHERE source_node_id = soRecord._id
            AND source_node_name = 'SalesOrder';
        END LOOP;
      END $$;
    `);
    },
});
