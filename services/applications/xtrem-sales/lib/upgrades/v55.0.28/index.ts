import { UpgradeSuite } from '@sage/xtrem-system';
import { dropCakeHrConfiguration } from './drop-cake-hr-configuration';
import { dropCakeHrOptionManagement } from './drop-cake-hr-option-management';
import { dropFrp1000ApiHistory } from './drop-frp-1000-api-history';
import { dropFrp1000Configuration } from './drop-frp-1000-configuration';
import { dropFrp1000Map } from './drop-frp-1000-map';
import { dropFrp1000MapProperty } from './drop-frp-1000-map-property';
import { dropFrp1000OptionManagement } from './drop-frp-1000-option-management';

// Need to put this upgrade suite here as there is no relevant package anymore as it has been removed.
export const upgradeSuite = new UpgradeSuite({
    actions: [
        dropCakeHrConfiguration,
        dropCakeHrOptionManagement,
        dropFrp1000Configuration,
        dropFrp1000OptionManagement,
        dropFrp1000ApiHistory,
        dropFrp1000MapProperty,
        dropFrp1000Map,
    ],
});
