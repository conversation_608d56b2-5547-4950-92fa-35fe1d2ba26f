import { SchemaRenamePropertyAction } from '@sage/xtrem-system';
import {
    BaseLineToSalesDocumentLine,
    SalesCreditMemoLine,
    SalesInvoiceLine,
    SalesOrderLine,
    SalesReturnReceiptLine,
    SalesReturnRequestLine,
    SalesShipmentLine,
} from '../../nodes';

export const salesOrderLine = new SchemaRenamePropertyAction({
    node: () => SalesOrderLine,
    oldPropertyName: 'quantityInSalesUnit',
    newPropertyName: 'quantity',
});

export const salesInvoiceLine = new SchemaRenamePropertyAction({
    node: () => SalesInvoiceLine,
    oldPropertyName: 'quantityInSalesUnit',
    newPropertyName: 'quantity',
});

export const salesCreditMemoLine = new SchemaRenamePropertyAction({
    node: () => SalesCreditMemoLine,
    oldPropertyName: 'quantityInSalesUnit',
    newPropertyName: 'quantity',
});

export const salesReturnReceiptLine = new SchemaRenamePropertyAction({
    node: () => SalesReturnReceiptLine,
    oldPropertyName: 'quantityInSalesUnit',
    newPropertyName: 'quantity',
});

export const salesReturnRequestLine = new SchemaRenamePropertyAction({
    node: () => SalesReturnRequestLine,
    oldPropertyName: 'quantityInSalesUnit',
    newPropertyName: 'quantity',
});

export const salesShipmentLine = new SchemaRenamePropertyAction({
    node: () => SalesShipmentLine,
    oldPropertyName: 'quantityInSalesUnit',
    newPropertyName: 'quantity',
});

export const baseLineToSalesDocumentLine = new SchemaRenamePropertyAction({
    node: () => BaseLineToSalesDocumentLine,
    oldPropertyName: 'quantityInSalesUnit',
    newPropertyName: 'quantity',
});
