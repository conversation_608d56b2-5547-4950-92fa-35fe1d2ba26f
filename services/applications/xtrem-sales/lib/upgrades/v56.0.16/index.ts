import { UpgradeSuite } from '@sage/xtrem-system';
import {
    renamePropertySalesCreditMemoLineForLineAmountExcludingTax,
    renamePropertySalesCreditMemoLineForLineAmountExcludingTaxInCompanyCurrency,
    renamePropertySalesCreditMemoLineForLineAmountIncludingTax,
    renamePropertySalesCreditMemoLineForLineAmountIncludingTaxInCompanyCurrency,
} from './rename-sales-credit-memo-line-property';
import {
    renamePropertySalesInvoiceLineForLineAmountExcludingTax,
    renamePropertySalesInvoiceLineForLineAmountExcludingTaxInCompanyCurrency,
    renamePropertySalesInvoiceLineForLineAmountIncludingTax,
    renamePropertySalesInvoiceLineForLineAmountIncludingTaxInCompanyCurrency,
} from './rename-sales-invoice-line-property';
import {
    renamePropertySalesOrderLineForLineAmountExcludingTax,
    renamePropertySalesOrderLineForLineAmountExcludingTaxInCompanyCurrency,
    renamePropertySalesOrderLineForLineAmountIncludingTax,
    renamePropertySalesOrderLineForLineAmountIncludingTaxInCompanyCurrency,
} from './rename-sales-order-line-property';
import {
    renamePropertySalesShipmentLineForLineAmountExcludingTax,
    renamePropertySalesShipmentLineForLineAmountExcludingTaxInCompanyCurrency,
} from './rename-sales-shipment-line-property';

export const upgradeSuite = new UpgradeSuite({
    actions: [
        renamePropertySalesOrderLineForLineAmountIncludingTax,
        renamePropertySalesOrderLineForLineAmountIncludingTaxInCompanyCurrency,
        renamePropertySalesOrderLineForLineAmountExcludingTax,
        renamePropertySalesOrderLineForLineAmountExcludingTaxInCompanyCurrency,
        renamePropertySalesInvoiceLineForLineAmountIncludingTax,
        renamePropertySalesInvoiceLineForLineAmountIncludingTaxInCompanyCurrency,
        renamePropertySalesInvoiceLineForLineAmountExcludingTax,
        renamePropertySalesInvoiceLineForLineAmountExcludingTaxInCompanyCurrency,
        renamePropertySalesShipmentLineForLineAmountExcludingTax,
        renamePropertySalesShipmentLineForLineAmountExcludingTaxInCompanyCurrency,
        renamePropertySalesCreditMemoLineForLineAmountIncludingTax,
        renamePropertySalesCreditMemoLineForLineAmountIncludingTaxInCompanyCurrency,
        renamePropertySalesCreditMemoLineForLineAmountExcludingTax,
        renamePropertySalesCreditMemoLineForLineAmountExcludingTaxInCompanyCurrency,
    ],
});
