import { SchemaRenamePropertyAction } from '@sage/xtrem-system';
import { SalesShipmentLine } from '../../nodes';

export const renamePropertySalesShipmentLineForLineAmountExcludingTax = new SchemaRenamePropertyAction({
    node: () => SalesShipmentLine,
    oldPropertyName: 'lineAmountExcludingTax',
    newPropertyName: 'amountExcludingTax',
});

export const renamePropertySalesShipmentLineForLineAmountExcludingTaxInCompanyCurrency = new SchemaRenamePropertyAction(
    {
        node: () => SalesShipmentLine,
        oldPropertyName: 'lineAmountExcludingTaxInCompanyCurrency',
        newPropertyName: 'amountExcludingTaxInCompanyCurrency',
    },
);
