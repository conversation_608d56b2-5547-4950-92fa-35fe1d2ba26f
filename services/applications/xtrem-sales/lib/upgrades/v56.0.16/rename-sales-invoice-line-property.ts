import { SchemaRenamePropertyAction } from '@sage/xtrem-system';
import { SalesInvoiceLine } from '../../nodes';

export const renamePropertySalesInvoiceLineForLineAmountIncludingTax = new SchemaRenamePropertyAction({
    node: () => SalesInvoiceLine,
    oldPropertyName: 'lineAmountIncludingTax',
    newPropertyName: 'amountIncludingTax',
});

export const renamePropertySalesInvoiceLineForLineAmountIncludingTaxInCompanyCurrency = new SchemaRenamePropertyAction({
    node: () => SalesInvoiceLine,
    oldPropertyName: 'lineAmountIncludingTaxInCompanyCurrency',
    newPropertyName: 'amountIncludingTaxInCompanyCurrency',
});

export const renamePropertySalesInvoiceLineForLineAmountExcludingTax = new SchemaRenamePropertyAction({
    node: () => SalesInvoiceLine,
    oldPropertyName: 'lineAmountExcludingTax',
    newPropertyName: 'amountExcludingTax',
});

export const renamePropertySalesInvoiceLineForLineAmountExcludingTaxInCompanyCurrency = new SchemaRenamePropertyAction({
    node: () => SalesInvoiceLine,
    oldPropertyName: 'lineAmountExcludingTaxInCompanyCurrency',
    newPropertyName: 'amountExcludingTaxInCompanyCurrency',
});
