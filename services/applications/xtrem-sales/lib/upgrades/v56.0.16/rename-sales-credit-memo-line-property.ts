import { SchemaRenamePropertyAction } from '@sage/xtrem-system';
import { SalesCreditMemoLine } from '../../nodes';

export const renamePropertySalesCreditMemoLineForLineAmountIncludingTax = new SchemaRenamePropertyAction({
    node: () => SalesCreditMemoLine,
    oldPropertyName: 'lineAmountIncludingTax',
    newPropertyName: 'amountIncludingTax',
});

export const renamePropertySalesCreditMemoLineForLineAmountIncludingTaxInCompanyCurrency =
    new SchemaRenamePropertyAction({
        node: () => SalesCreditMemoLine,
        oldPropertyName: 'lineAmountIncludingTaxInCompanyCurrency',
        newPropertyName: 'amountIncludingTaxInCompanyCurrency',
    });

export const renamePropertySalesCreditMemoLineForLineAmountExcludingTax = new SchemaRenamePropertyAction({
    node: () => SalesCreditMemoLine,
    oldPropertyName: 'lineAmountExcludingTax',
    newPropertyName: 'amountExcludingTax',
});

export const renamePropertySalesCreditMemoLineForLineAmountExcludingTaxInCompanyCurrency =
    new SchemaRenamePropertyAction({
        node: () => SalesCreditMemoLine,
        oldPropertyName: 'lineAmountExcludingTaxInCompanyCurrency',
        newPropertyName: 'amountExcludingTaxInCompanyCurrency',
    });
