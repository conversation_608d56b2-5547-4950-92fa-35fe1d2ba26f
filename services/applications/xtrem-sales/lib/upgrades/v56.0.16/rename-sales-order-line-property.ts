import { SchemaRenamePropertyAction } from '@sage/xtrem-system';
import { SalesOrderLine } from '../../nodes';

export const renamePropertySalesOrderLineForLineAmountIncludingTax = new SchemaRenamePropertyAction({
    node: () => SalesOrderLine,
    oldPropertyName: 'lineAmountIncludingTax',
    newPropertyName: 'amountIncludingTax',
});

export const renamePropertySalesOrderLineForLineAmountIncludingTaxInCompanyCurrency = new SchemaRenamePropertyAction({
    node: () => SalesOrderLine,
    oldPropertyName: 'lineAmountIncludingTaxInCompanyCurrency',
    newPropertyName: 'amountIncludingTaxInCompanyCurrency',
});

export const renamePropertySalesOrderLineForLineAmountExcludingTax = new SchemaRenamePropertyAction({
    node: () => SalesOrderLine,
    oldPropertyName: 'lineAmountExcludingTax',
    newPropertyName: 'amountExcludingTax',
});

export const renamePropertySalesOrderLineForLineAmountExcludingTaxInCompanyCurrency = new SchemaRenamePropertyAction({
    node: () => SalesOrderLine,
    oldPropertyName: 'lineAmountExcludingTaxInCompanyCurrency',
    newPropertyName: 'amountExcludingTaxInCompanyCurrency',
});
