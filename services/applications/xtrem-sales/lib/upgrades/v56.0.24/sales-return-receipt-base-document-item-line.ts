import { CustomSqlAction } from '@sage/xtrem-system';

export const salesReturnReceiptBaseDocumentItemLine = new CustomSqlAction({
    description: 'Move sales receipt return line to base document item line',
    body: async helper => {
        await helper.executeSql(`
            INSERT INTO ${helper.schemaName}.base_document_item_line (
                _constructor,
                _sort_value,
                _id,
                _tenant_id,
                document,
                status,
                site,
                site_linked_address,
                item,
                item_description,
                stock_site,
                stock_site_linked_address,
                stock_unit,
                quantity_in_stock_unit,
                unit_to_stock_unit_conversion_factor,
                unit,
                quantity,
                internal_note,
                external_note,
                is_external_note,
                analytical_data,
                stored_dimensions,
                stored_attributes
            )
            SELECT
                'SalesReturnReceiptLine',
                line._sort_value,
                line._id,
                line._tenant_id,
                line.document,
                'draft',
                ref_doc.site,
                addressBase._id,
                line.item,
                line.item_description,
                ref_doc.stock_site,
                addressBase._id,
                line.stock_unit,
                line.quantity_in_stock_unit,
                line.unit_to_stock_unit_conversion_factor,
                line.unit,
                line.quantity,
                line.internal_note,
                '',
                FALSE,
                line.analytical_data,
                line.stored_dimensions,
                line.stored_attributes
            FROM ${helper.schemaName}.sales_return_receipt_line AS line
            INNER JOIN ${helper.schemaName}.base_document AS ref_doc
                ON line.document = ref_doc._id
                AND line._tenant_id = ref_doc._tenant_id
            INNER JOIN ${helper.schemaName}.site AS site
                ON ref_doc.site = site._id
                AND line._tenant_id = site._tenant_id
            INNER JOIN ${helper.schemaName}.business_entity_address AS businessEntityAddress
                ON site.primary_address = businessEntityAddress._id
                AND site._tenant_id = businessEntityAddress._tenant_id
            INNER JOIN ${helper.schemaName}.address_base AS addressBase
                ON businessEntityAddress._id = addressBase._id
                AND businessEntityAddress._tenant_id = addressBase._tenant_id
                AND addressBase._constructor = 'BusinessEntityAddress'
            INNER JOIN ${helper.schemaName}.address AS address
                ON addressBase.address = address._id
                AND addressBase._tenant_id = address._tenant_id;
        `);
    },
});
