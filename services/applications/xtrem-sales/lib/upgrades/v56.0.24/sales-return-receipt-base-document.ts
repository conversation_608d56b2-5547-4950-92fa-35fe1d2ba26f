import { UnsafeCustomSqlAction } from '@sage/xtrem-system';

export const salesReturnReceiptBaseDocument = new UnsafeCustomSqlAction({
    maxAllowedDurationMs: 600_000,
    description: 'Move the sales return receipt to the base_document table',
    fixes: { tables: ['sales_return_receipt'] },
    body: async helper => {
        await helper.executeSql(`
            DO $$ DECLARE
                srrRecord RECORD;
                base_id ${helper.schemaName}.base_document._id%TYPE := 1;
                start_id ${helper.schemaName}.base_document._id%TYPE :=1;
                financial_site_id ${helper.schemaName}.site._id%TYPE;
            BEGIN
                -- There we are dropping the constraints to avoid the constraint violation
                SET CONSTRAINTS ALL DEFERRED;
                start_id  = (SELECT COALESCE(MAX(_id), 0) FROM ${helper.schemaName}.sales_return_receipt);
                -- We are setting the sequence to the max id of the base_document if it is greater than the current sequence value
                IF start_id > COALESCE((SELECT last_value FROM ${helper.schemaName}.base_document__id_seq) , 0) THEN
                    EXECUTE 'ALTER SEQUENCE ${helper.schemaName}.base_document__id_seq RESTART WITH ' || start_id+1;
                END IF;
                FOR srrRecord IN ( SELECT srr._id, srr._tenant_id, srr.number,
                    srr.status::text::${helper.schemaName}.base_status_enum,
                    srr.display_status::text::${helper.schemaName}.base_display_status_enum,
                    srr.site,
                    srr.date,
                    srr.internal_note,
                    srr._create_user,
                    srr._create_stamp,
                    srr._update_user,
                    srr._update_stamp ,
                    site.is_finance,
                    site.financial_site,
                    c.currency
                    FROM ${helper.schemaName}.sales_return_receipt srr
                    INNER JOIN ${helper.schemaName}.site AS site ON site._id = srr.site AND srr._tenant_id = site._tenant_id
                    INNER JOIN ${helper.schemaName}.company AS c ON c._id = site.legal_company AND c._tenant_id = site._tenant_id )
                LOOP
                    financial_site_id := CASE WHEN srrRecord.is_finance = true THEN srrRecord.site ELSE srrRecord.financial_site END;
                    INSERT INTO ${helper.schemaName}.base_document(
                      _tenant_id,
                      _constructor,
                      stock_site,
                      number,
                      status,
                      approval_status,
                      display_status,
                      date,
                      is_printed,
                      is_sent,
                      site,
                      currency,
                      financial_site,
                      internal_note,
                      external_note,
                      is_external_note,
                      is_transfer_header_note,
                      is_transfer_line_note,
                      text,
                      _create_user,
                      _create_stamp,
                      _update_user,
                      _update_stamp
                    )
                    VALUES (
                      srrRecord._tenant_id,
                      'SalesReturnReceipt',
                      srrRecord.site,
                      srrRecord.number,
                      srrRecord.status,
                      'draft',
                      srrRecord.display_status,
                      srrRecord.date,
                      FALSE,
                      FALSE,
                      srrRecord.site,
                      srrRecord.currency,
                      financial_site_id,
                      srrRecord.internal_note,
                      '',
                      FALSE,
                      FALSE,
                      FALSE,
                      '',
                      srrRecord._create_user,
                      srrRecord._create_stamp,
                      srrRecord._update_user,
                      srrRecord._update_stamp
                    )
                    RETURNING _id INTO base_id;
                    UPDATE ${helper.schemaName}.sales_return_receipt SET _id = base_id
                        WHERE
                            _id = srrRecord._id AND
                            sales_return_receipt._tenant_id = srrRecord._tenant_id;
                    UPDATE ${helper.schemaName}.sales_return_receipt_line SET document = base_id
                        WHERE
                            document = srrRecord._id AND
                            sales_return_receipt_line._tenant_id = srrRecord._tenant_id;
                    UPDATE ${helper.schemaName}.attachment_association set source_node_id = base_id
                        where
                            source_node_id = srrRecord._id and
                            source_node_name = 'SalesReturnReceipt';
                    UPDATE ${helper.schemaName}.accounting_staging SET document_sys_id = base_id
                        WHERE
                            document_sys_id = srrRecord._id AND
                            document_type = 'salesReturnReceipt'::${helper.schemaName}.finance_document_type_enum AND
                            _tenant_id = srrRecord._tenant_id;
                    UPDATE ${helper.schemaName}.base_open_item SET document_sys_id = base_id
                        WHERE
                            document_sys_id = srrRecord._id AND
                            document_type = 'salesReturnReceipt'::${helper.schemaName}.finance_document_type_enum AND
                            _tenant_id = srrRecord._tenant_id;
                    UPDATE ${helper.schemaName}.finance_transaction SET document_sys_id = base_id
                        WHERE
                            document_sys_id = srrRecord._id AND
                            document_type = 'salesReturnReceipt'::${helper.schemaName}.finance_document_type_enum AND
                            _tenant_id = srrRecord._tenant_id;
                END LOOP;
            END $$;`);
    },
});
