// base_business_relation
import { UnsafeCustomSqlAction } from '@sage/xtrem-system';

export const salesReturnRequestBaseDocument = new UnsafeCustomSqlAction({
    description: 'Move the sales return request to the base_document table',
    maxAllowedDurationMs: 900_000,
    body: async helper => {
        await helper.executeSql(`
            DO $$ DECLARE
                retReqRecord RECORD;
                base_id ${helper.schemaName}.base_document._id%TYPE := 1;
                start_id ${helper.schemaName}.base_document._id%TYPE :=1;
                financial_site_id ${helper.schemaName}.site._id%TYPE;
            BEGIN

                -- There we are dropping the constraints to avoid the constraint violation
                SET CONSTRAINTS ALL DEFERRED;

                start_id  = (SELECT COALESCE(MAX(_id), 0) FROM ${helper.schemaName}.sales_return_request);

                -- We are setting the sequence to the max id of the base_document if it is greater than the current sequence value
                IF start_id > COALESCE((SELECT last_value FROM ${helper.schemaName}.base_document__id_seq) , 0) THEN
                    EXECUTE 'ALTER SEQUENCE ${helper.schemaName}.base_document__id_seq RESTART WITH ' || start_id+1;
                END IF;

                FOR retReqRecord IN (SELECT
                    retReq._id,
                    retReq._tenant_id,
                    retReq.number,
                    retReq.status::text::${helper.schemaName}.base_status_enum,
                    retReq.approval_status::text::${helper.schemaName}.approval_status_enum,
                    retReq.display_status::text::${helper.schemaName}.base_display_status_enum,
                    retReq.site,
                    retReq.stock_site,
                    retReq.date,
                    retReq.internal_note,
                    retReq.is_transfer_header_note,
                    retReq.is_transfer_line_note,
                    retReq.text,
                    retReq._create_user,
                    retReq._create_stamp,
                    retReq._update_user,
                    retReq._update_stamp ,
                    site.is_finance,
                    site.financial_site,
                    company.currency,
                    retReq.is_printed,
                    retReq.is_sent
                    FROM ${helper.schemaName}.sales_return_request retReq
                    INNER JOIN ${helper.schemaName}.site ON site._id = retReq.site AND retReq._tenant_id = site._tenant_id
                    INNER JOIN ${helper.schemaName}.company ON company._id = site.legal_company AND retReq._tenant_id = company._tenant_id )

                LOOP
                    financial_site_id := CASE WHEN retReqRecord.is_finance = true THEN retReqRecord.site ELSE retReqRecord.financial_site END;

                    INSERT INTO ${helper.schemaName}.base_document(
                        _tenant_id,
                        _constructor,
                        number,
                        status,
                        approval_status,
                        display_status,

                        date,
                        is_printed,
                        is_sent,

                        site,
                        stock_site,
                        currency,
                        financial_site,

                        internal_note,
                        external_note,
                        is_external_note,
                        is_transfer_header_note,
                        is_transfer_line_note,
                        text,

                        _create_user,
                        _create_stamp,
                        _update_user,
                        _update_stamp
                    )
                    VALUES (
                        retReqRecord._tenant_id,
                        'SalesReturnRequest',
                        retReqRecord.number,
                        retReqRecord.status,
                        retReqRecord.approval_status,
                        retReqRecord.display_status,

                        retReqRecord.date,
                        retReqRecord.is_printed,
                        retReqRecord.is_sent,

                        retReqRecord.site,
                        retReqRecord.stock_site,
                        retReqRecord.currency,
                        financial_site_id,

                        retReqRecord.internal_note,
                        '',
                        FALSE,
                        retReqRecord.is_transfer_header_note,
                        retReqRecord.is_transfer_line_note,
                        retReqRecord.text,

                        retReqRecord._create_user,
                        retReqRecord._create_stamp,
                        retReqRecord._update_user,
                        retReqRecord._update_stamp
                    )
                    RETURNING _id INTO base_id;

                    UPDATE ${helper.schemaName}.sales_return_request SET _id = base_id
                        WHERE _id = retReqRecord._id AND sales_return_request._tenant_id = retReqRecord._tenant_id;

                    UPDATE ${helper.schemaName}.sales_return_request_line SET document = base_id
                        WHERE document = retReqRecord._id AND sales_return_request_line._tenant_id = retReqRecord._tenant_id;

                    UPDATE ${helper.schemaName}.attachment_association set source_node_id = base_id
                        where source_node_id = retReqRecord._id and source_node_name = 'salesReturnRequest';
                END LOOP;
            END $$;`);
    },
});
