import { CustomSqlAction } from '@sage/xtrem-system';

export const moveOriginDocumentTypeToOrigin = new CustomSqlAction({
    description: 'Move originDocumentType to origin',
    body: async helper => {
        await helper.executeSql(`
			DO $$
            DECLARE
                tables TEXT[] := ARRAY[
                    'sales_credit_memo_line',
                    'sales_invoice_line',
                    'sales_order_line',
                    'sales_return_receipt_line',
                    'sales_return_request_line',
                    'sales_shipment_line'
                ];
                tbl TEXT;
            BEGIN
                FOREACH tbl IN ARRAY tables
                LOOP
                    EXECUTE format($f$
                        UPDATE ${helper.schemaName}.base_document_item_line AS baseLine
						SET origin = CASE line.origin_document_type
			                WHEN 'direct' THEN 'direct'::${helper.schemaName}.base_origin_enum
			                WHEN 'invoice' THEN 'invoice'::${helper.schemaName}.base_origin_enum
			                WHEN 'shipment' THEN 'shipment'::${helper.schemaName}.base_origin_enum
			                WHEN 'return' THEN 'return'::${helper.schemaName}.base_origin_enum
                            WHEN 'order' THEN 'order'::${helper.schemaName}.base_origin_enum
			                ELSE NULL
			            END
                        FROM ${helper.schemaName}.%I AS line
                        WHERE baseLine._id = line._id AND baseLine._tenant_id = line._tenant_id;
                    $f$, tbl);
                END LOOP;
            END $$;
        `);
    },
});
