import { UpgradeSuite } from '@sage/xtrem-system';
import { renameReturnDateOnReturnReceipt } from './rename-property-return-date-to-date';
import * as renameSalesUnitToUnit from './rename-sales-unit-to-unit';

export const upgradeSuite = new UpgradeSuite({
    actions: [
        renameReturnDateOnReturnReceipt,
        renameSalesUnitToUnit.salesCreditMemoLine,
        renameSalesUnitToUnit.salesInvoiceLine,
        renameSalesUnitToUnit.salesOrderLine,
        renameSalesUnitToUnit.salesReturnRequestLine,
        renameSalesUnitToUnit.salesShipmentLine,
        renameSalesUnitToUnit.salesReturnReceiptLine,
        renameSalesUnitToUnit.renameSalesUnitToUnitFixes,
    ],
});
