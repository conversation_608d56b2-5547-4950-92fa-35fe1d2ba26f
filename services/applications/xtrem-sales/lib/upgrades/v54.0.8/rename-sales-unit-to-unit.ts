import { CustomSqlAction, SchemaRenamePropertyAction } from '@sage/xtrem-system';
import {
    SalesCreditMemoLine,
    SalesInvoiceLine,
    SalesOrderLine,
    SalesReturnReceiptLine,
    SalesReturnRequestLine,
    SalesShipmentLine,
} from '../../nodes';

export const salesCreditMemoLine = new SchemaRenamePropertyAction({
    node: () => SalesCreditMemoLine,
    oldPropertyName: 'salesUnit',
    newPropertyName: 'unit',
});

export const salesInvoiceLine = new SchemaRenamePropertyAction({
    node: () => SalesInvoiceLine,
    oldPropertyName: 'salesUnit',
    newPropertyName: 'unit',
});

export const salesOrderLine = new SchemaRenamePropertyAction({
    node: () => SalesOrderLine,
    oldPropertyName: 'salesUnit',
    newPropertyName: 'unit',
});

export const salesReturnRequestLine = new SchemaRenamePropertyAction({
    node: () => SalesReturnRequestLine,
    oldPropertyName: 'salesUnit',
    newPropertyName: 'unit',
});

export const salesShipmentLine = new SchemaRenamePropertyAction({
    node: () => SalesShipmentLine,
    oldPropertyName: 'salesUnit',
    newPropertyName: 'unit',
});

export const salesReturnReceiptLine = new SchemaRenamePropertyAction({
    node: () => SalesReturnReceiptLine,
    oldPropertyName: 'salesUnit',
    newPropertyName: 'unit',
});

export const renameSalesUnitToUnitFixes = new CustomSqlAction({
    description: 'Fixes not nullable columns when rename salesUnit to unit',
    fixes: {
        notNullableColumns: [
            { table: 'sales_credit_memo_line', column: 'unit' },
            { table: 'sales_order_line', column: 'unit' },
        ],
    },
    body: async () => {},
});
