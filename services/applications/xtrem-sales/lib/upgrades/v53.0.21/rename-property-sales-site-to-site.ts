import { SchemaRenamePropertyAction } from '@sage/xtrem-system';
import {
    SalesCreditMemo,
    SalesCreditMemoLine,
    SalesInvoice,
    SalesInvoiceLine,
    SalesOrder,
    SalesOrderLine,
    SalesReturnReceipt,
    SalesReturnRequest,
    SalesShipment,
} from '../../nodes';

export const creditMemo = new SchemaRenamePropertyAction({
    node: () => SalesCreditMemo,
    oldPropertyName: 'salesSite',
    newPropertyName: 'site',
});

export const creditMemoLine = new SchemaRenamePropertyAction({
    node: () => SalesCreditMemoLine,
    oldPropertyName: 'salesSite',
    newPropertyName: 'site',
});

export const invoice = new SchemaRenamePropertyAction({
    node: () => SalesInvoice,
    oldPropertyName: 'salesSite',
    newPropertyName: 'site',
});

export const invoiceLine = new SchemaRenamePropertyAction({
    node: () => SalesInvoiceLine,
    oldPropertyName: 'salesSite',
    newPropertyName: 'site',
});

export const order = new SchemaRenamePropertyAction({
    node: () => SalesOrder,
    oldPropertyName: 'salesSite',
    newPropertyName: 'site',
});

export const orderLine = new SchemaRenamePropertyAction({
    node: () => SalesOrderLine,
    oldPropertyName: 'salesSite',
    newPropertyName: 'site',
});

export const returnRequest = new SchemaRenamePropertyAction({
    node: () => SalesReturnRequest,
    oldPropertyName: 'salesSite',
    newPropertyName: 'site',
});

export const shipment = new SchemaRenamePropertyAction({
    node: () => SalesShipment,
    oldPropertyName: 'salesSite',
    newPropertyName: 'site',
});

export const returnReceipt = new SchemaRenamePropertyAction({
    node: () => SalesReturnReceipt,
    oldPropertyName: 'stockSite',
    newPropertyName: 'site',
});
