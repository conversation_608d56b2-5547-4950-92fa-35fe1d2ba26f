import { CustomSqlAction } from '@sage/xtrem-system';

export const migrateCustomerDashboardItemPositions = new CustomSqlAction({
    description: 'Migrate customer 360 dashboard item positions',
    fixes: {
        tables: ['dashboard_item_position'],
    },
    body: async helper => {
        await helper.executeSql(`
UPDATE ${helper.schemaName}.dashboard_item_position
SET x = 0,
    y = 0,
    w = 2,
    h = 1
FROM ${helper.schemaName}.dashboard_item,
    ${helper.schemaName}.dashboard
WHERE ${helper.schemaName}.dashboard."group" = 'Customer-360'
    AND ${helper.schemaName}.dashboard_item_position.dashboard_item = ${helper.schemaName}.dashboard_item._id
    AND ${helper.schemaName}.dashboard_item.type = '@sage/xtrem-sales/CustomerIndicatorGroup'
    AND ${helper.schemaName}.dashboard_item_position.breakpoint = 'xxs';
UPDATE ${helper.schemaName}.dashboard_item_position
SET x = 0,
    y = 0,
    w = 4,
    h = 1
FROM ${helper.schemaName}.dashboard_item,
    ${helper.schemaName}.dashboard
WHERE ${helper.schemaName}.dashboard."group" = 'Customer-360'
    AND ${helper.schemaName}.dashboard_item_position.dashboard_item = ${helper.schemaName}.dashboard_item._id
    AND ${helper.schemaName}.dashboard_item.type = '@sage/xtrem-sales/CustomerIndicatorGroup'
    AND ${helper.schemaName}.dashboard_item_position.breakpoint = 'xs';
UPDATE ${helper.schemaName}.dashboard_item_position
SET x = 0,
    y = 0,
    w = 6,
    h = 1
FROM ${helper.schemaName}.dashboard_item,
    ${helper.schemaName}.dashboard
WHERE ${helper.schemaName}.dashboard."group" = 'Customer-360'
    AND ${helper.schemaName}.dashboard_item_position.dashboard_item = ${helper.schemaName}.dashboard_item._id
    AND ${helper.schemaName}.dashboard_item.type = '@sage/xtrem-sales/CustomerIndicatorGroup'
    AND ${helper.schemaName}.dashboard_item_position.breakpoint = 'sm';
UPDATE ${helper.schemaName}.dashboard_item_position
SET x = 0,
    y = 0,
    w = 8,
    h = 1
FROM ${helper.schemaName}.dashboard_item,
    ${helper.schemaName}.dashboard
WHERE ${helper.schemaName}.dashboard."group" = 'Customer-360'
    AND ${helper.schemaName}.dashboard_item_position.dashboard_item = ${helper.schemaName}.dashboard_item._id
    AND ${helper.schemaName}.dashboard_item.type = '@sage/xtrem-sales/CustomerIndicatorGroup'
    AND ${helper.schemaName}.dashboard_item_position.breakpoint = 'md';
UPDATE ${helper.schemaName}.dashboard_item_position
SET x = 0,
    y = 0,
    w = 12,
    h = 1
FROM ${helper.schemaName}.dashboard_item,
    ${helper.schemaName}.dashboard
WHERE ${helper.schemaName}.dashboard."group" = 'Customer-360'
    AND ${helper.schemaName}.dashboard_item_position.dashboard_item = ${helper.schemaName}.dashboard_item._id
    AND ${helper.schemaName}.dashboard_item.type = '@sage/xtrem-sales/CustomerIndicatorGroup'
    AND ${helper.schemaName}.dashboard_item_position.breakpoint = 'lg';
UPDATE ${helper.schemaName}.dashboard_item_position
SET x = 0,
    y = 5,
    w = 2,
    h = 4
FROM ${helper.schemaName}.dashboard_item,
    ${helper.schemaName}.dashboard
WHERE ${helper.schemaName}.dashboard."group" = 'Customer-360'
    AND ${helper.schemaName}.dashboard_item_position.dashboard_item = ${helper.schemaName}.dashboard_item._id
    AND ${helper.schemaName}.dashboard_item.type = '@sage/xtrem-sales/OpenSalesOrders'
    AND ${helper.schemaName}.dashboard_item_position.breakpoint = 'xxs';
UPDATE ${helper.schemaName}.dashboard_item_position
SET x = 2,
    y = 1,
    w = 2,
    h = 4
FROM ${helper.schemaName}.dashboard_item,
    ${helper.schemaName}.dashboard
WHERE ${helper.schemaName}.dashboard."group" = 'Customer-360'
    AND ${helper.schemaName}.dashboard_item_position.dashboard_item = ${helper.schemaName}.dashboard_item._id
    AND ${helper.schemaName}.dashboard_item.type = '@sage/xtrem-sales/OpenSalesOrders'
    AND ${helper.schemaName}.dashboard_item_position.breakpoint = 'xs';
UPDATE ${helper.schemaName}.dashboard_item_position
SET x = 3,
    y = 1,
    w = 3,
    h = 4
FROM ${helper.schemaName}.dashboard_item,
    ${helper.schemaName}.dashboard
WHERE ${helper.schemaName}.dashboard."group" = 'Customer-360'
    AND ${helper.schemaName}.dashboard_item_position.dashboard_item = ${helper.schemaName}.dashboard_item._id
    AND ${helper.schemaName}.dashboard_item.type = '@sage/xtrem-sales/OpenSalesOrders'
    AND ${helper.schemaName}.dashboard_item_position.breakpoint = 'sm';
UPDATE ${helper.schemaName}.dashboard_item_position
SET x = 4,
    y = 1,
    w = 4,
    h = 4
FROM ${helper.schemaName}.dashboard_item,
    ${helper.schemaName}.dashboard
WHERE ${helper.schemaName}.dashboard."group" = 'Customer-360'
    AND ${helper.schemaName}.dashboard_item_position.dashboard_item = ${helper.schemaName}.dashboard_item._id
    AND ${helper.schemaName}.dashboard_item.type = '@sage/xtrem-sales/OpenSalesOrders'
    AND ${helper.schemaName}.dashboard_item_position.breakpoint = 'md';
UPDATE ${helper.schemaName}.dashboard_item_position
SET x = 6,
    y = 1,
    w = 6,
    h = 4
FROM ${helper.schemaName}.dashboard_item,
    ${helper.schemaName}.dashboard
WHERE ${helper.schemaName}.dashboard."group" = 'Customer-360'
    AND ${helper.schemaName}.dashboard_item_position.dashboard_item = ${helper.schemaName}.dashboard_item._id
    AND ${helper.schemaName}.dashboard_item.type = '@sage/xtrem-sales/OpenSalesOrders'
    AND ${helper.schemaName}.dashboard_item_position.breakpoint = 'lg';
UPDATE ${helper.schemaName}.dashboard_item_position
SET x = 0,
    y = 1,
    w = 2,
    h = 4
FROM ${helper.schemaName}.dashboard_item,
    ${helper.schemaName}.dashboard
WHERE ${helper.schemaName}.dashboard."group" = 'Customer-360'
    AND ${helper.schemaName}.dashboard_item_position.dashboard_item = ${helper.schemaName}.dashboard_item._id
    AND ${helper.schemaName}.dashboard_item.type = '@sage/xtrem-master-data/CustomerContactList'
    AND ${helper.schemaName}.dashboard_item_position.breakpoint = 'xxs';
UPDATE ${helper.schemaName}.dashboard_item_position
SET x = 0,
    y = 1,
    w = 2,
    h = 4
FROM ${helper.schemaName}.dashboard_item,
    ${helper.schemaName}.dashboard
WHERE ${helper.schemaName}.dashboard."group" = 'Customer-360'
    AND ${helper.schemaName}.dashboard_item_position.dashboard_item = ${helper.schemaName}.dashboard_item._id
    AND ${helper.schemaName}.dashboard_item.type = '@sage/xtrem-master-data/CustomerContactList'
    AND ${helper.schemaName}.dashboard_item_position.breakpoint = 'xs';
UPDATE ${helper.schemaName}.dashboard_item_position
SET x = 0,
    y = 1,
    w = 3,
    h = 4
FROM ${helper.schemaName}.dashboard_item,
    ${helper.schemaName}.dashboard
WHERE ${helper.schemaName}.dashboard."group" = 'Customer-360'
    AND ${helper.schemaName}.dashboard_item_position.dashboard_item = ${helper.schemaName}.dashboard_item._id
    AND ${helper.schemaName}.dashboard_item.type = '@sage/xtrem-master-data/CustomerContactList'
    AND ${helper.schemaName}.dashboard_item_position.breakpoint = 'sm';
UPDATE ${helper.schemaName}.dashboard_item_position
SET x = 0,
    y = 1,
    w = 4,
    h = 4
FROM ${helper.schemaName}.dashboard_item,
    ${helper.schemaName}.dashboard
WHERE ${helper.schemaName}.dashboard."group" = 'Customer-360'
    AND ${helper.schemaName}.dashboard_item_position.dashboard_item = ${helper.schemaName}.dashboard_item._id
    AND ${helper.schemaName}.dashboard_item.type = '@sage/xtrem-master-data/CustomerContactList'
    AND ${helper.schemaName}.dashboard_item_position.breakpoint = 'md';
UPDATE ${helper.schemaName}.dashboard_item_position
SET x = 0,
    y = 1,
    w = 6,
    h = 4
FROM ${helper.schemaName}.dashboard_item,
    ${helper.schemaName}.dashboard
WHERE ${helper.schemaName}.dashboard."group" = 'Customer-360'
    AND ${helper.schemaName}.dashboard_item_position.dashboard_item = ${helper.schemaName}.dashboard_item._id
    AND ${helper.schemaName}.dashboard_item.type = '@sage/xtrem-master-data/CustomerContactList'
    AND ${helper.schemaName}.dashboard_item_position.breakpoint = 'lg';
            `);
    },
});
