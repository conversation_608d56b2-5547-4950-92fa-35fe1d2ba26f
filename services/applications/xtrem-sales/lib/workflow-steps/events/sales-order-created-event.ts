import * as xtremWorkflow from '@sage/xtrem-workflow';

export class SalesOrderCreatedEvent extends xtremWorkflow.workflowSteps.EntityCreatedEventStep {
    static override readonly descriptor = {
        ...xtremWorkflow.workflowSteps.EntityCreatedEventStep.descriptor,
        key: 'sales-order-created',
        startTopics: 'SalesOrder/created',
        title: 'Sales order created',
        description: 'This event is triggered when a new sales order is created.',
        serviceOptions: () => [xtremWorkflow.serviceOptions.workflowAdvanced],

        defaultConfig: {
            // STDEN TODO LOCALIZE
            localizedTitle: { base: 'Sales order created' },
            topic: 'SalesOrder/created',
            subtitle: '',
            conditions: [],
            entityName: '@sage/xtrem-sales/SalesOrder',
            stepVariables: [
                {
                    node: 'SalesOrder',
                    path: 'salesOrder._id',
                    type: 'IntReference',
                    title: 'Sales order id',
                },
                {
                    path: 'salesOrder.number',
                    type: 'String',
                    title: 'Number',
                },
                {
                    path: 'salesOrder.totalAmountExcludingTax',
                    type: 'Decimal',
                    title: 'Total amount excluding tax',
                },
                {
                    path: 'salesOrder.billToCustomer.creditLimit',
                    type: 'Decimal',
                    title: 'Credit limit',
                },
                {
                    path: 'salesOrder.billToContact.email',
                    type: 'String',
                    title: 'Email',
                },
                {
                    node: 'Contact',
                    path: 'salesOrder.billToContact._id',
                    type: 'IntReference',
                    title: 'Sales order bill to contact id',
                },
                {
                    node: 'Site',
                    path: 'salesOrder.site._id',
                    type: 'IntReference',
                    title: 'Sales order sales site id',
                },
            ],
        },
    };
}
