import * as xtremWorkflow from '@sage/xtrem-workflow';

export class SalesOrderConfirmedEvent extends xtremWorkflow.workflowSteps.EntityUpdatedEventStep {
    static override readonly descriptor = {
        ...xtremWorkflow.workflowSteps.EntityUpdatedEventStep.descriptor,
        key: 'sales-order-confirmed',
        startTopics: 'SalesOrder/updated',
        title: 'Sales order confirmed',
        description: 'This event is triggered when a sales order is confirmed.',
        serviceOptions: () => [xtremWorkflow.serviceOptions.workflowAdvanced],

        defaultConfig: {
            // STDEN TODO LOCALIZE
            localizedTitle: { base: 'Sales order confirmed' },
            topic: 'SalesOrder/updated',
            subtitle: '',
            conditions: [
                {
                    path: '$old.salesOrder.status',
                    value: ['quote'],
                    operator: 'set' as const,
                    useParameter: false,
                },
                {
                    path: 'salesOrder.status',
                    value: ['pending'],
                    operator: 'set' as const,
                    useParameter: false,
                },
            ],
            entityName: '@sage/xtrem-sales/SalesOrder',
            stepVariables: [
                {
                    node: 'SalesOrder',
                    path: 'salesOrder._id',
                    type: 'IntReference',
                    title: 'Sales order new id',
                },
                {
                    path: 'salesOrder.number',
                    type: 'String',
                    title: 'Number',
                },
                {
                    node: '@sage/xtrem-sales/SalesOrderStatus',
                    path: 'salesOrder.status',
                    type: 'Enum',
                    title: 'Status',
                },
                {
                    node: 'SalesOrder',
                    path: '$old.salesOrder._id',
                    type: 'IntReference',
                    title: '(old) _id',
                },
                {
                    path: '$old.salesOrder.number',
                    type: 'String',
                    title: '(old) Number',
                },
                {
                    node: '@sage/xtrem-sales/SalesOrderStatus',
                    path: '$old.salesOrder.status',
                    type: 'Enum',
                    title: '(old) Status',
                },
            ],
        },
    };
}
