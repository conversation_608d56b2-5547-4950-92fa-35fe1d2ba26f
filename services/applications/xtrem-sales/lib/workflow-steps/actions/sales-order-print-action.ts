import * as reporting from '@sage/xtrem-reporting';
import * as xtremWorkflow from '@sage/xtrem-workflow';

export class SalesOrderPrintActioni extends reporting.workflowSteps.PrintDocumentAction {
    static override readonly descriptor = {
        ...reporting.workflowSteps.PrintDocumentAction.descriptor,
        key: 'sales-order-print',
        title: 'Print sales order',
        description: 'Prints the sales order with the default template.',
        serviceOptions: () => [xtremWorkflow.serviceOptions.workflowAdvanced],

        defaultConfig: {
            // STDEN TODO LOCALIZE
            localizedTitle: { base: 'Print sales order' },
            subtitle: '',
            report: 'salesOrder',
            outputVariables: [
                {
                    path: 'salesOrderPrinted.downloadUrl',
                    type: 'String',
                    title: 'Generated report',
                },
            ],
            reportParameters: [
                {
                    name: 'order',
                    value: 'salesOrder._id',
                    isVariable: true,
                },
            ],
            outputVariableName: 'salesOrderPrinted',
        },
    };
}
