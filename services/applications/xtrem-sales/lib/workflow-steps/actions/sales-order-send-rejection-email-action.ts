import * as mailer from '@sage/xtrem-mailer';
import * as xtremWorkflow from '@sage/xtrem-workflow';

export class SalesOrderSendRejectionEmailAction extends mailer.workflowSteps.SendEmailAction {
    static override readonly descriptor = {
        ...mailer.workflowSteps.SendEmailAction.descriptor,
        key: 'sales-order-send-rejection-email',
        title: 'Email order rejected',
        description: 'Sends a rejection email for the sales order.',
        serviceOptions: () => [xtremWorkflow.serviceOptions.workflowAdvanced],

        defaultConfig: {
            // STDEN TODO LOCALIZE
            localizedTitle: { base: 'Email order rejected' },
            subtitle: '',
            report: 'sales_order_send',
            inputVariableName: 'salesOrder',
            // STDEN TODO LOCALIZE
            localizedSubject: {
                base: `Your order {{salesOrder.number}} cannot be processed because it exceeds your credit limit`,
            },
            addressList: [
                {
                    variable: 'salesOrder.billToContact.email',
                    value: '',
                    isVariable: true,
                    addressType: 'to',
                } as const,
            ],
            attachmentList: [],
            outputVariables: [
                {
                    path: 'salesOrderRejectionEmailSent.success',
                    type: 'Boolean',
                    title: 'Mail sent OK',
                },
            ],
            reportParameters: [
                {
                    name: 'salesOrder',
                    value: 'salesOrder._id',
                    isVariable: true,
                },
                {
                    name: 'contact',
                    value: 'salesOrder.billToContact._id',
                    isVariable: true,
                },
                {
                    name: 'site',
                    value: 'salesOrder.site._id',
                    isVariable: true,
                },
            ],
            outputVariableName: 'salesOrderRejectionEmailSent',
        },
    };
}
