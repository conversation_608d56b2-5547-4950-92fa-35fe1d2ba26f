import * as xtremWorkflow from '@sage/xtrem-workflow';

export class SalesOrderWithinCreditLimitCondition extends xtremWorkflow.workflowSteps.ConditionAction {
    static override readonly descriptor = {
        ...xtremWorkflow.workflowSteps.ConditionAction.descriptor,
        key: 'sales-order-within-credit-limit',
        title: 'Is sales order within credit limit',
        description:
            "Compares the sales order's total amount excluding tax with the credit limit of the bill to customer.",
        serviceOptions: () => [xtremWorkflow.serviceOptions.workflowAdvanced],

        defaultConfig: {
            // STDEN TODO LOCALIZE
            title: { base: 'Is within credit limit?' },
            subtitle: '',
            inputVariableName: 'salesOrder',
            conditions: [
                {
                    path: 'salesOrder.totalAmountExcludingTax',
                    value: 'salesOrder.billToCustomer.creditLimit',
                    operator: 'lessThan',
                    useParameter: true,
                } as const,
            ],
            outputVariables: [],
            outputVariableName: '',
        },
    };
}
