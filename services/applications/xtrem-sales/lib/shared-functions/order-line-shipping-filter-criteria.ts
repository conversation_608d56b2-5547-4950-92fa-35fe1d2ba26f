import type { Filter } from '@sage/xtrem-client';
import { fromToCustomerNameFilter, fromToNumberFilter } from './common-filter-criteria';

const filterCriteriaCommon: Filter<any> = (stockSite: string) => {
    return {
        stockSite: { _id: { _eq: stockSite } },
        status: { _in: ['pending', 'inProgress'] },
        shippingStatus: { _in: ['notShipped', 'partiallyShipped'] },
    };
};

export function orderLineShippingFilterCriteria(
    stockSite: string,
    shippingDate: string,
    shippingUntilDate?: string | null,
    fromSoldToCustomer?: string,
    toSoldToCustomer?: string,
    incoterm?: string,
    deliveryMode?: string,
    fromOrder?: string,
    toOrder?: string,
) {
    const document = {
        soldToCustomer: { ...fromToCustomerNameFilter(fromSoldToCustomer, toSoldToCustomer), isOnHold: false },
        taxCalculationStatus: { _nin: ['failed'] },

        ...fromToNumberFilter(fromOrder, toOrder),
        ...(incoterm ? { incoterm } : {}),
    };

    return {
        ...filterCriteriaCommon(stockSite),
        document,
        doNotShipBeforeDate: { _or: [{ _lte: shippingDate }, null] },
        doNotShipAfterDate: { _or: [{ _gte: shippingDate }, null] },
        ...(shippingUntilDate ? { shippingDate: { _lte: shippingUntilDate } } : {}),
        ...(deliveryMode ? { deliveryMode } : {}),
    };
}

export function orderShippingFilterCriteria(
    stockSite: string,
    shippingUntilDate?: string | null,
    fromSoldToCustomer?: string,
    toSoldToCustomer?: string,
    incoterm?: string,
    deliveryMode?: string,
) {
    return {
        ...filterCriteriaCommon(stockSite),
        soldToCustomer: { ...fromToCustomerNameFilter(fromSoldToCustomer, toSoldToCustomer), isOnHold: false },
        ...(incoterm ? { incoterm: { _id: incoterm } } : {}),
        ...(deliveryMode ? { deliveryMode: { _id: deliveryMode } } : {}),
        ...(shippingUntilDate ? { shippingDate: { _lte: shippingUntilDate } } : {}),
    };
}
