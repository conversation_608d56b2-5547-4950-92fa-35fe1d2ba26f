export function fromToCustomerNameFilter(fromCustomer?: string, toCustomer?: string) {
    if (fromCustomer && toCustomer) {
        return { businessEntity: { name: { _gte: fromCustomer, _lte: toCustomer } } };
    }
    if (fromCustomer) {
        return { businessEntity: { name: { _gte: fromCustomer } } };
    }
    if (toCustomer) {
        return { businessEntity: { name: { _lte: toCustomer } } };
    }
    return undefined;
}

export function fromToNumberFilter(fromNumber?: string, toNumber?: string) {
    if (fromNumber && toNumber) {
        return { number: { _gte: fromNumber, _lte: toNumber } };
    }
    if (fromNumber) {
        return { number: { _gte: fromNumber } };
    }
    if (toNumber) {
        return { number: { _lte: toNumber } };
    }
    return {};
}
