import { fromToCustomerNameFilter, fromToNumberFilter } from './common-filter-criteria';

// Todo : refactor this with better parameters
export function shipmentInvoicingFilterCriteria(
    site: string,
    shipmentUntilDate?: string | null,
    fromBillToCustomer?: string,
    toBillToCustomer?: string,
    fromShipment?: string,
    toShipment?: string,
    skipNumberFiltering: boolean = true,
) {
    const customerFilter = fromToCustomerNameFilter(fromBillToCustomer, toBillToCustomer);

    return {
        ...(skipNumberFiltering ? {} : fromToNumberFilter(fromShipment, toShipment)),
        site: { _id: { _eq: site } },
        status: 'shipped',
        invoiceStatus: { _not: { _eq: 'invoiced' } },
        ...(shipmentUntilDate ? { shippingDate: { _lte: shipmentUntilDate } } : {}),
        ...(customerFilter ? { billToCustomer: customerFilter } : {}),
    };
}
