import { BusinessRuleError, NodeStatus } from '@sage/xtrem-core';
import type * as xtremFinanceData from '@sage/xtrem-finance-data';
import * as xtremSales from '../..';

export async function checkFinanceIntegration(returnReceipt: xtremSales.nodes.SalesReturnReceipt) {
    if (
        !(
            returnReceipt.$.status === NodeStatus.added ||
            (returnReceipt.$.status === NodeStatus.modified && (await (await returnReceipt.$.old).status) !== 'closed')
        )
    ) {
        return;
    }
    if (returnReceipt.$.status === NodeStatus.added) {
        await returnReceipt.$.context.flushDeferredActions();
    }
    const postResult: xtremFinanceData.interfaces.MutationResult =
        await xtremSales.functions.FinanceIntegration.salesReturnReceiptControlAndCreateMutationResult(
            returnReceipt.$.context,
            returnReceipt,
        );

    if (postResult.message.length) throw new BusinessRuleError(postResult.message);
}
