import type { Context, decimal, ValidationContext } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';

export async function checkQuantityInSalesUnit(
    context: Context,
    cx: ValidationContext,
    quantity: decimal,
    item: xtremMasterData.nodes.Item,
    customer: xtremMasterData.nodes.Customer | null,
    unit: xtremMasterData.nodes.UnitOfMeasure,
    options: {
        canBeZero: boolean;
        excludeMinimumQuantity: boolean;
    } = { canBeZero: true, excludeMinimumQuantity: false },
): Promise<void> {
    const { canBeZero, excludeMinimumQuantity } = options;
    await cx.error.if(quantity).is.negative();

    const itemCustomer = await context.tryRead(xtremMasterData.nodes.ItemCustomer, {
        item: item._id,
        customer: customer?._id,
    });
    if (itemCustomer && unit._id === (await itemCustomer.salesUnit)._id) {
        if ((await itemCustomer.minimumSalesQuantity) !== 0 && !excludeMinimumQuantity) {
            await cx
                .at('itemCustomer.minimumSalesQuantity')
                .error.if(quantity)
                .is.less.than(await itemCustomer.minimumSalesQuantity);
        }
        if ((await itemCustomer.maximumSalesQuantity) !== 0) {
            await cx
                .at('itemCustomer.maximumSalesQuantity')
                .error.if(quantity)
                .is.greater.than(await itemCustomer.maximumSalesQuantity);
        }
    } else {
        if ((await item.minimumSalesQuantity) !== 0 && !excludeMinimumQuantity) {
            await cx
                .at('item.minimumSalesQuantity')
                .error.if(quantity)
                .is.less.than(await item.minimumSalesQuantity);
        }
        if ((await item.maximumSalesQuantity) !== 0) {
            await cx
                .at('item.maximumSalesQuantity')
                .error.if(quantity)
                .is.greater.than(await item.maximumSalesQuantity);
        }
    }

    if (!canBeZero) {
        await cx.at('item.minimumSalesQuantity').error.if(quantity).is.equal.to(0);
    }

    // TODO: when available check that the quantity can't be lower than the quantity already shipped.
}
