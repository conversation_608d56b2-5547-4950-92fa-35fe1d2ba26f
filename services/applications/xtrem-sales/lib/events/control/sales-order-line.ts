import type { ValidationContext } from '@sage/xtrem-core';
import { ValidationSeverity } from '@sage/xtrem-core';
import type * as xtremSales from '../..';

export async function checkIfAllocatedOrderQuantityIsGreaterThanRemainingQuantity(
    cx: ValidationContext,
    line: xtremSales.nodes.SalesOrderLine,
): Promise<void> {
    await cx.error
        .withMessage(
            '@sage/xtrem-sales/nodes__sales_order_line__too_much_quantity_allocated,',
            'The allocated quantity on the sales order line cannot be larger than the remaining quantity to ship.',
        )
        .if(await line.quantityAllocated)
        .is.greater.than(await line.remainingQuantityToShipInStockUnit);
}

export async function checkIfAllocationCompletedBeforeReducingQuantity(
    cx: ValidationContext,
    line: xtremSales.nodes.SalesOrderLine,
    oldLine: xtremSales.nodes.SalesOrderLine,
) {
    if ((await line.quantity) < (await oldLine.quantity) && (await line.allocationRequestStatus) === 'inProgress') {
        cx.error.addLocalized(
            '@sage/xtrem-sales/nodes__sales_order_line__auto_allocation_cannot_decrease_quantity',
            'You can only reduce the quantity of the sales order line after the allocation request is complete.',
        );
    }
}

export async function checkOrderLineStatus(
    cx: ValidationContext,
    line: xtremSales.nodes.SalesOrderLine,
): Promise<void> {
    await cx.error
        .withMessage(
            '@sage/xtrem-sales/nodes__sales_order_line__improper_status_during_creation',
            'The status for the sales order line needs to be "Quote" or "Pending".',
        )
        .if(await line.status)
        .is.not.in(['quote', 'pending']);
}

export async function checkIfSameStatus(cx: ValidationContext, line: xtremSales.nodes.SalesOrderLine): Promise<void> {
    const documentStatus = await (await line.document).status;
    if (documentStatus !== (await line.status) && documentStatus !== 'inProgress') {
        cx.error.addLocalized(
            '@sage/xtrem-sales/nodes__sales_order_line__sales_order_lines_status_must_have_the_same_status',
            'The sales order lines status must have the same status than the header.',
        );
    }
}

export async function checkIfSameShippingStatus(
    cx: ValidationContext,
    line: xtremSales.nodes.SalesOrderLine,
): Promise<void> {
    const documentShippingStatus = await (await line.document).shippingStatus;
    if (documentShippingStatus !== (await line.shippingStatus) && documentShippingStatus !== 'partiallyShipped') {
        cx.error.addLocalized(
            '@sage/xtrem-sales/nodes__sales_order_line__sales_order_lines_shipping_status_must_have_the_same_shipping_status',
            'The shipping status of the sales order lines must be the same as the shipping status displayed in the header.',
        );
    }
}

export async function checkIfSameSalesSite(
    cx: ValidationContext,
    line: xtremSales.nodes.SalesOrderLine,
): Promise<void> {
    if ((await (await line.document).site) !== (await line.site)) {
        cx.error.addLocalized(
            '@sage/xtrem-sales/nodes__sales_order_line__sales_order_sales_site_must_have_the_same_value',
            'The site on the sales order lines must be the same as in the header.',
        );
    }
}

export async function checkIfSameStockSiteLegalCompany(
    cx: ValidationContext,
    line: xtremSales.nodes.SalesOrderLine,
): Promise<void> {
    if ((await (await line.site).legalCompany) !== (await (await line.stockSite).legalCompany)) {
        cx.error.addLocalized(
            '@sage/xtrem-sales/nodes__sales_order_line__sales_order_sales_stock_and_sales_site_value__during_creation',
            'Incorrect stock site on the sales order line. The sales site and the stock site on the sales order line must have the same company.',
        );
    }
}

export async function checkShippingStatusUponLineDeletion(
    cx: ValidationContext,
    line: xtremSales.nodes.SalesOrderLine,
) {
    if ((await line.shippingStatus) !== 'notShipped') {
        cx.addDiagnose(
            ValidationSeverity.error,
            cx.localize(
                '@sage/xtrem-sales/nodes__sales_order_line__deletion_forbidden_line_exists',
                'Deletion is not allowed. The sales order line is shipped or partially shipped.',
            ),
        );
    }
}

export async function removeStockAllocationUponLineDeletion(
    cx: ValidationContext,
    salesOrderInstance: xtremSales.nodes.SalesOrderLine,
) {
    if (
        (await (await salesOrderInstance.item).isStockManaged) &&
        (await salesOrderInstance.allocationStatus) !== 'notAllocated'
    ) {
        cx.addDiagnose(
            ValidationSeverity.error,
            cx.localize(
                '@sage/xtrem-sales/nodes__sales_order_line__cannot_delete_line_quantity_allocated',
                'Remove the stock allocation before deleting the line.',
            ),
        );
    }
}

export async function checkAllocationStatusBeforeLineDeletion(
    cx: ValidationContext,
    salesOrderInstance: xtremSales.nodes.SalesOrderLine,
) {
    await cx.error
        .withMessage(
            '@sage/xtrem-sales/nodes__sales_order_line__cannot_delete_line_allocation_request_in_progress',
            'Automatic allocation needs to finish before you can delete the line.',
        )
        .if(await salesOrderInstance.allocationRequestStatus)
        .is.equal.to('inProgress');
}
