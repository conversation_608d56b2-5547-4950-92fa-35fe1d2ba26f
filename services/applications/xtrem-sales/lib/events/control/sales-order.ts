import type { ValidationContext } from '@sage/xtrem-core';
import { NodeStatus, ValidationSeverity } from '@sage/xtrem-core';
import type * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremSales from '../..';

async function shippingStatusControl(cx: ValidationContext, shippingStatus: string) {
    await cx.error
        .withMessage(
            '@sage/xtrem-sales/nodes__sales_order__improper_shipping_status_during_creation',
            "Incorrect sales order shipping status. The sales order must have the 'Not shipped' shipping status to be created.",
        )
        .if(shippingStatus)
        .is.not.equal.to('notShipped');
}

export async function controlSite(cx: ValidationContext, site: string, stockSite: string) {
    await cx.error
        .withMessage(
            '@sage/xtrem-sales/nodes__sales_order__improper_stock_and_sales_site_value__during_creation',
            'Incorrect stock site on the sales order. The sales site and the stock site must have the same company.',
        )
        .if(site)
        .is.not.equal.to(stockSite);
}

export async function controlBegin(cx: ValidationContext, salesOrderInstance: xtremSales.nodes.SalesOrder) {
    if (salesOrderInstance.$.status === NodeStatus.added) {
        await shippingStatusControl(cx, await salesOrderInstance.shippingStatus);
    }
    await xtremSales.events.controlBegin.documents.checkWrongTaxType(cx, salesOrderInstance);
}

export function onHoldCustomerControl(document: {
    status: xtremSales.enums.SalesOrderStatus | xtremSales.enums.SalesShipmentStatus;
    prevStatus: xtremSales.enums.SalesOrderStatus | xtremSales.enums.SalesShipmentStatus;
    isOnHold: boolean;
    customerOnHoldCheck: xtremMasterData.enums.CustomerOnHoldType;
    skipIsOnHold: boolean;
}): boolean {
    return (
        document.status === 'pending' &&
        document.prevStatus !== document.status &&
        xtremSales.functions.isBlockedOnHoldCustomer(document.isOnHold, document.customerOnHoldCheck) &&
        !document.skipIsOnHold
    );
}

export async function salesSiteUpdateValidation(
    cx: ValidationContext,
    salesOrderInstance: xtremSales.nodes.SalesOrder,
    old: xtremSales.nodes.SalesOrder,
) {
    await cx.error
        .withMessage(
            '@sage/xtrem-sales/nodes__sales_order__header_sales_site_not_updatable',
            'The sales site of the sales order must not be changed.',
        )
        .if(await old.site)
        .is.not.equal.to(await salesOrderInstance.site);
}

export async function soldToCustomerUpdateValidation(
    cx: ValidationContext,
    salesOrderInstance: xtremSales.nodes.SalesOrder,
    old: xtremSales.nodes.SalesOrder,
) {
    await cx.error
        .withMessage(
            '@sage/xtrem-sales/nodes__sales_order__header_sold_to_customer_not_updatable',
            'The sold-to customer of the sales order must not be changed.',
        )
        .if(await old.soldToCustomer)
        .is.not.equal.to(await salesOrderInstance.soldToCustomer);
}

export async function currencyUpdateValidation(
    cx: ValidationContext,
    salesOrderInstance: xtremSales.nodes.SalesOrder,
    old: xtremSales.nodes.SalesOrder,
) {
    await cx.error
        .withMessage(
            '@sage/xtrem-sales/nodes__sales_order__header_currency_not_updatable',
            'The sales order currency of the sales order must not be changed.',
        )
        .if(await old.currency)
        .is.not.equal.to(await salesOrderInstance.currency);
}

export async function legalCompanyValidation(cx: ValidationContext, salesOrderInstance: xtremSales.nodes.SalesOrder) {
    await cx.error
        .withMessage(
            '@sage/xtrem-sales/nodes__sales_order__improper_stock_and_sales_site_value__during_creation',
            'Incorrect stock site on the sales order. The sales site and the stock site must have the same company.',
        )
        .if(await (await salesOrderInstance.site)?.legalCompany)
        .is.not.equal.to(await (await salesOrderInstance.stockSite)?.legalCompany);
}

export async function closedSalesOrderValidation(
    cx: ValidationContext,
    salesOrderInstance: xtremSales.nodes.SalesOrder,
    old: xtremSales.nodes.SalesOrder,
) {
    await cx.error
        .withMessage(
            '@sage/xtrem-sales/nodes__sales_order__update_not_allowed_status_closed',
            'The sales order is closed. You cannot update it.',
        )
        .if(
            (await old.status) === 'closed' &&
                (await salesOrderInstance.status) === 'closed' &&
                (await salesOrderInstance.invoiceStatus) === (await old.invoiceStatus),
        )
        .is.true();
}

export async function controlDelete(cx: ValidationContext, salesOrderInstance: xtremSales.nodes.SalesOrder) {
    if ((await salesOrderInstance.shippingStatus) !== 'notShipped') {
        cx.addDiagnose(
            ValidationSeverity.error,
            cx.localize(
                '@sage/xtrem-sales/nodes__sales_order__deletion_forbidden_line_exists',
                'Deletion is not allowed. The sales order is shipped or partially shipped.',
            ),
        );
    }
    await cx.error
        .withMessage(
            '@sage/xtrem-sales/nodes__sales_order__deletion_forbidden_proforma_invoices_exists',
            'You cannot delete a sales order when there are linked proforma invoices.',
        )
        .if(await salesOrderInstance.proformaInvoices?.length)
        .is.greater.than(0);
}
