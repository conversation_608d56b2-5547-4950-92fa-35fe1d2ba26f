import type { ValidationContext } from '@sage/xtrem-core';
import type * as xtremSales from '../..';

export async function checkStatusForDeletion(cx: ValidationContext, document: xtremSales.nodes.SalesCreditMemoLine) {
    await cx.error
        .withMessage(
            '@sage/xtrem-sales/nodes__sales_credit_memo_line__deletion_forbidden_posted',
            'Line deletion is not allowed. The sales credit memo is posted.',
        )
        .if(['posted', 'inProgress', 'error'].includes(await (await document.document).status))
        .is.true();
}
