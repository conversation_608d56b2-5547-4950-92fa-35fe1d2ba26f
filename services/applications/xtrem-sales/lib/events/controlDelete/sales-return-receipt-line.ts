import type { ValidationContext } from '@sage/xtrem-core';
import * as xtremSales from '../..';

export async function checkCreditStatusForDeletion(
    cx: ValidationContext,
    returnReceiptLine: xtremSales.nodes.SalesReturnReceiptLine,
) {
    await cx.error
        .withMessage(
            '@sage/xtrem-sales/nodes__sales_return_receipt_line__delete_not_allowed_linked_line_credited',
            'The sales return receipt line cannot be deleted. The sales return request line associated is credited.',
        )
        .if(await xtremSales.functions.SalesReturnReceiptLib.hasExistingCreditedReturnRequestLine(returnReceiptLine))
        .is.true();
}

export async function checkStockStatusForDeletion(
    cx: ValidationContext,
    returnReceiptLine: xtremSales.nodes.SalesReturnReceiptLine,
) {
    await cx.error
        .withMessage(
            '@sage/xtrem-sales/nodes__sales_return_receipt_line__delete_not_allowed_line_already_posted',
            'The sales return receipt line cannot be deleted. The sales return receipt line is already posted.',
        )
        .if((await returnReceiptLine.stockTransactionStatus) === 'completed')
        .is.true();
}

export async function checkDraftStatusForDeletion(
    cx: ValidationContext,
    returnReceiptLine: xtremSales.nodes.SalesReturnReceiptLine,
) {
    await cx.error
        .withMessage(
            '@sage/xtrem-sales/nodes__sales_return_receipt_line__delete_not_allowed_line_not_draft',
            'The sales return receipt line cannot be deleted. The sales return receipt line is not a draft.',
        )
        .if(
            (await (await returnReceiptLine.document).status) === 'closed' ||
                (await returnReceiptLine.stockTransactionStatus) !== 'draft',
        )
        .is.true();
}
