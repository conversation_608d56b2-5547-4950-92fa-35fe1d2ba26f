import type { ValidationContext } from '@sage/xtrem-core';
import { ValidationSeverity } from '@sage/xtrem-core';
import type * as xtremSales from '../..';

export async function checkIfShipmentIsSuitableForDeletion(
    cx: ValidationContext,
    salesShipment: xtremSales.nodes.SalesShipment,
) {
    if ((await salesShipment.status) !== 'readyToProcess') {
        cx.addDiagnose(
            ValidationSeverity.error,
            cx.localize(
                '@sage/xtrem-sales/nodes__sales_shipment__deletion_forbidden_line_exists',
                'Deletion is not allowed. The sales shipment is ready to ship or has already been shipped.',
            ),
        );
    }
}
