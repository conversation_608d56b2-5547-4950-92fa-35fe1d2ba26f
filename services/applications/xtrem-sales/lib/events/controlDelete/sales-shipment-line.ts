import type { ValidationContext } from '@sage/xtrem-core';
import type * as xtremSales from '../..';

export async function checkStatusForDeletion(
    cx: ValidationContext,
    salesShipmentLine: xtremSales.nodes.SalesShipmentLine,
) {
    await cx.error
        .withMessage(
            '@sage/xtrem-sales/nodes__sales_shipment_line__deletion_forbidden_line_exists',
            'Deletion is not allowed. The sales shipment line is ready to ship or has already been shipped.',
        )
        .if(
            (await salesShipmentLine.status) !== 'readyToProcess' ||
                (await (await salesShipmentLine.document).status) !== 'readyToProcess',
        )
        .is.true();
}

export async function checkStockAllocationForDeletion(
    cx: ValidationContext,
    salesShipmentLine: xtremSales.nodes.SalesShipmentLine,
) {
    await cx.error
        .withMessage(
            '@sage/xtrem-sales/pages__sales_shipment_line__cannot_delete_line_quantity_allocated',
            'Remove the stock allocation before deleting the line.',
        )
        .if(
            (await (await salesShipmentLine.item).isStockManaged) &&
                (await salesShipmentLine.allocationStatus) !== 'notAllocated',
        )
        .is.true();
}
