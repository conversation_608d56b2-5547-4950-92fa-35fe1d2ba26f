import type { ValidationContext } from '@sage/xtrem-core';
import type * as xtremSales from '../..';

export async function checkReceiptStatusForDeletion(
    cx: ValidationContext,
    returnRequestLine: xtremSales.nodes.SalesReturnRequestLine,
) {
    await cx.error
        .withMessage(
            '@sage/xtrem-sales/nodes__sales_return_request_line__deletion_forbidden_line_received_or_partially_received',
            'Deletion is not allowed. The sales return request line is received or partially received.',
        )
        .if((await returnRequestLine.receiptStatus) !== 'notReceived')
        .is.true();
}

export async function checkCreditStatusForDeletion(
    cx: ValidationContext,
    returnRequestLine: xtremSales.nodes.SalesReturnRequestLine,
) {
    await cx.error
        .withMessage(
            '@sage/xtrem-sales/nodes__sales_return_request__deletion_forbidden_line_credited_or_partially_credited',
            'Delete not allowed. The sales return request line is credited or partially credited.',
        )
        .if((await returnRequestLine.creditStatus) !== 'notCredited')
        .is.true();
}

export async function checkApprovalStatusForDeletion(
    cx: ValidationContext,
    returnRequestLine: xtremSales.nodes.SalesReturnRequestLine,
) {
    const document = await returnRequestLine.document;
    await cx.error
        .withMessage(
            '@sage/xtrem-sales/nodes__sales_return_request_line__deletion_forbidden_line_approved_or_rejected',
            'Deletion is not allowed. The sales return request line is approved or rejected.',
        )
        .if(['approved', 'rejected'].includes(await document.approvalStatus) && (await document.status) !== 'closed')
        .is.true();
}
