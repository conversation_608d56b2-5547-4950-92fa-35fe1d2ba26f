import type { ValidationContext } from '@sage/xtrem-core';
import type * as xtremSales from '../..';

export async function checkStatusForDeletion(cx: ValidationContext, salesInvoice: xtremSales.nodes.SalesInvoice) {
    await cx.error
        .withMessage(
            '@sage/xtrem-sales/nodes__sales_invoice__deletion_forbidden_posted',
            'Deletion is not allowed. The sales invoice is posted.',
        )
        .if(['posted', 'inProgress', 'error'].includes(await salesInvoice.status))
        .is.true();
}
