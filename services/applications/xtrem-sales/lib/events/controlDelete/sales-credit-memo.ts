import type { ValidationContext } from '@sage/xtrem-core';
import type * as xtremSales from '../..';

export async function checkStatusForDeletion(cx: ValidationContext, document: xtremSales.nodes.SalesCreditMemo) {
    await cx.error
        .withMessage(
            '@sage/xtrem-sales/nodes__sales_credit_memo__deletion_forbidden_posted',
            'Deletion is not allowed. The sales credit memo is posted.',
        )
        .if(['posted', 'inProgress', 'error'].includes(await document.status))
        .is.true();
}
