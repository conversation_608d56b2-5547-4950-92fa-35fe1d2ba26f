import type { ValidationContext } from '@sage/xtrem-core';
import type * as xtremSales from '../..';

export async function checkReceiptStatusOnDeletion(
    cx: ValidationContext,
    returnRequest: xtremSales.nodes.SalesReturnRequest,
) {
    await cx.error
        .withMessage(
            '@sage/xtrem-sales/nodes__sales_return_request__deletion_forbidden_received_or_partially_received',
            'Deletion is not allowed. The sales return request is received or partially received.',
        )
        .if((await returnRequest.receiptStatus) !== 'notReceived')
        .is.true();
}

export async function checkCreditStatusOnDeletion(
    cx: ValidationContext,
    returnRequest: xtremSales.nodes.SalesReturnRequest,
) {
    await cx.error
        .withMessage(
            '@sage/xtrem-sales/nodes__sales_return_request__deletion_forbidden_credited_or_partially_credited',
            'Deletion is not allowed. The sales return request is credited or partially credited.',
        )
        .if((await returnRequest.creditStatus) !== 'notCredited')
        .is.true();
}

export async function checkApprovalStatusOnDeletion(
    cx: ValidationContext,
    returnRequest: xtremSales.nodes.SalesReturnRequest,
) {
    await cx.error
        .withMessage(
            '@sage/xtrem-sales/nodes__sales_return_request__deletion_forbidden_approved_or_rejected',
            'Deletion is not allowed. The sales return request is approved or rejected.',
        )
        .if(['approved', 'rejected'].includes(await returnRequest.approvalStatus))
        .is.true();
}
