import type { ValidationContext } from '@sage/xtrem-core';
import { NodeStatus } from '@sage/xtrem-core';
import * as xtremStockData from '@sage/xtrem-stock-data';
import * as xtremSales from '../..';

export async function checkClosedStatus(
    cx: ValidationContext,
    returnReceiptLine: xtremSales.nodes.SalesReturnReceiptLine,
) {
    if (returnReceiptLine.$.status !== NodeStatus.modified) {
        return;
    }

    await cx.error
        .withMessage(
            '@sage/xtrem-sales/nodes__sales_return_receipt_line__update_not_allowed_linked_line_closed',
            'The sales return receipt line cannot be updated. The sales return request line associated is closed.',
        )
        .if(
            (await xtremStockData.functions.stockTransactionLib.checkIfAnyOfTheTransactionsChanged(
                returnReceiptLine,
            )) &&
                (await xtremSales.functions.SalesReturnReceiptLib.hasExistingClosedReturnRequestLine(
                    returnReceiptLine,
                )) &&
                !(await (
                    await returnReceiptLine.document
                ).forceUpdateForFinance),
        )
        .is.true();
}
