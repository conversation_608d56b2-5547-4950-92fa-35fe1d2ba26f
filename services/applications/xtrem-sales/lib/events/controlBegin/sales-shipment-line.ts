import type { ValidationContext } from '@sage/xtrem-core';
import { NodeStatus } from '@sage/xtrem-core';
import * as xtremSales from '../..';

/** Controls on creation */
export async function checkStatusOnCreation(
    cx: ValidationContext,
    salesShipmentLine: xtremSales.nodes.SalesShipmentLine,
) {
    if (salesShipmentLine.$.status !== NodeStatus.added) {
        return;
    }
    await cx.error
        .withMessage(
            '@sage/xtrem-sales/nodes__sales_shipment_lines__creation_forbidden_improper_status',
            'Line creation is not allowed. The sales shipment is ready to ship or has already been shipped.',
        )
        .if((await (await salesShipmentLine.document).status) !== 'readyToProcess')
        .is.true();
}

export async function checkCustomerOnHoldOnCreation(
    cx: ValidationContext,
    salesShipmentLine: xtremSales.nodes.SalesShipmentLine,
) {
    if (salesShipmentLine.$.status !== NodeStatus.added) {
        return;
    }
    const salesShipment = await salesShipmentLine.document;
    await cx.error
        .withMessage(
            '@sage/xtrem-sales/nodes__sales_shipment_line__creation_impossible_bill_to_customer_is_on_hold',
            'Creation is not allowed. The Bill-to customer is on hold.',
        )
        .if(
            xtremSales.functions.isBlockedOnHoldCustomer(
                await salesShipment.isOnHold,
                await (
                    await (
                        await salesShipment.site
                    ).legalCompany
                ).customerOnHoldCheck,
            ),
        )
        .is.true();
}

/** Controls on update */
export async function checkQuantityOnUpdate(
    cx: ValidationContext,
    salesShipmentLine: xtremSales.nodes.SalesShipmentLine,
) {
    if (salesShipmentLine.$.status !== NodeStatus.modified) {
        return;
    }
    const oldNode = await salesShipmentLine.$.old;
    await cx.error
        .withMessage(
            '@sage/xtrem-sales/nodes__sales_shipment_line__quantity_update_not_allowed_status_readyToShip',
            'The sales shipment is ready to ship. You cannot update the quantity.',
        )
        .if(
            ((await oldNode.status) === 'readyToShip' ||
                (await (await (await salesShipmentLine.document).$.old).status) === 'readyToShip') &&
                (await oldNode.quantity) !== (await salesShipmentLine.quantity),
        )
        .is.true();
}

export async function checkItemOnUpdate(cx: ValidationContext, salesShipmentLine: xtremSales.nodes.SalesShipmentLine) {
    if (salesShipmentLine.$.status !== NodeStatus.modified) {
        return;
    }
    await cx.error
        .withMessage(
            '@sage/xtrem-sales/nodes__sales_shipment_line__item_update_not_allowed',
            'You cannot update the item.',
        )
        .if((await (await salesShipmentLine.$.old).item)._id !== (await salesShipmentLine.item)._id)
        .is.true();
}

export async function checkUnitOnUpdate(cx: ValidationContext, salesShipmentLine: xtremSales.nodes.SalesShipmentLine) {
    if (salesShipmentLine.$.status !== NodeStatus.modified) {
        return;
    }
    await cx.error
        .withMessage(
            '@sage/xtrem-sales/nodes__sales_shipment_line__sales_unit_update_not_allowed',
            'You cannot update the sales unit.',
        )
        .if((await (await salesShipmentLine.$.old).unit)._id !== (await salesShipmentLine.unit)._id)
        .is.true();
}

export async function checkAllocatedQuantityOnUpdate(
    cx: ValidationContext,
    salesShipmentLine: xtremSales.nodes.SalesShipmentLine,
) {
    if (salesShipmentLine.$.status !== NodeStatus.modified) {
        return;
    }
    await cx.error
        .withMessage(
            '@sage/xtrem-sales/nodes__sales_shipment_line__too_much_quantity_allocated,',
            'The allocated quantity on the sales shipment line cannot be larger than the shipping quantity.',
        )
        .if((await salesShipmentLine.quantityInStockUnit) < (await salesShipmentLine.quantityAllocated))
        .is.true();
}
