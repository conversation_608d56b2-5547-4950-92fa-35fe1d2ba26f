import type { ValidationContext } from '@sage/xtrem-core';
import { NodeStatus } from '@sage/xtrem-core';
import type * as xtremSales from '../..';

export async function checkStatusConsistency(
    cx: ValidationContext,
    returnRequestLine: xtremSales.nodes.SalesReturnRequestLine,
) {
    await cx.error
        .withMessage(
            '@sage/xtrem-sales/nodes__sales_return_request_line__update_line_cant_be_in_the_draft_status',
            'This document is at Pending status. You cannot set the sales return request line to draft.',
        )
        .if(
            (await returnRequestLine.status) === 'draft' &&
                (await (await returnRequestLine.document).status) === 'pending',
        )
        .is.true();
}

export async function checkLineStatusOnCreation(
    cx: ValidationContext,
    returnRequestLine: xtremSales.nodes.SalesReturnRequestLine,
) {
    if (returnRequestLine.$.status !== NodeStatus.added) {
        return;
    }

    await cx.error
        .withMessage(
            '@sage/xtrem-sales/nodes__sales_return_request_line__improper_status_during_creation',
            "Incorrect sales return request line status. The sales return request line must have the 'Draft' or 'Pending' status during creation.",
        )
        .if(!['draft', 'pending'].includes(await returnRequestLine.status))
        .is.true();
}

export async function checkHeaderStatusOnCreation(
    cx: ValidationContext,
    returnRequestLine: xtremSales.nodes.SalesReturnRequestLine,
) {
    if (returnRequestLine.$.status !== NodeStatus.added) {
        return;
    }

    const document = await returnRequestLine.document;
    await cx.error
        .withMessage(
            '@sage/xtrem-sales/nodes__sales_return_request_line__status_is_not_draft_or_the_approval_status_is_submitted',
            "Sales return request line creation not allowed. The sales return request status is not 'Draft', or the approval status is 'Submitted for approval'.",
        )
        .if((await document.status) !== 'draft' || (await document.approvalStatus) !== 'draft')
        .is.true();
}

export async function checkIfSameStatusOnCreation(
    cx: ValidationContext,
    returnRequestLine: xtremSales.nodes.SalesReturnRequestLine,
) {
    if (returnRequestLine.$.status !== NodeStatus.added) {
        return;
    }

    const documentStatus = await (await returnRequestLine.document).status;
    await cx.error
        .withMessage(
            '@sage/xtrem-sales/nodes__sales_return_request_line__sales_return_request_lines_status_must_have_the_same_status',
            'The sales return request lines status must have the same status than the header.',
        )
        .if(documentStatus !== (await returnRequestLine.status) && documentStatus !== 'inProgress')
        .is.true();
}

export async function checkIfSameOriginShippingSiteOnCreation(
    cx: ValidationContext,
    returnRequestLine: xtremSales.nodes.SalesReturnRequestLine,
) {
    if (returnRequestLine.$.status !== NodeStatus.added) {
        return;
    }

    await cx.error
        .withMessage(
            '@sage/xtrem-sales/nodes__sales_return_request_line__sales_return_request_origin_shipping_site_must_have_the_same_value',
            'The origin shipping site on the sales return request lines must be the same as the stock site in the header.',
        )
        .if((await (await returnRequestLine.document).stockSite) !== (await returnRequestLine.originShippingSite))
        .is.true();
}

export async function checkIfSameOriginShippingSiteLegalCompanyOnCreation(
    cx: ValidationContext,
    returnRequestLine: xtremSales.nodes.SalesReturnRequestLine,
) {
    if (returnRequestLine.$.status !== NodeStatus.added) {
        return;
    }

    await cx.error
        .withMessage(
            '@sage/xtrem-sales/nodes__sales_return_request_line__sales_return_request_sales_origin_shipping_and_sales_site_value__during_creation',
            'Incorrect origin shipping site on the sales return request line. The origin shipping site on the sales return request line and the the header sales site must have the same company.',
        )
        .if(
            (await (await (await returnRequestLine.document).site).legalCompany) !==
                (await (
                    await returnRequestLine.originShippingSite
                ).legalCompany),
        )
        .is.true();
}

export async function checkClosedStatusOnUpdate(
    cx: ValidationContext,
    returnRequestLine: xtremSales.nodes.SalesReturnRequestLine,
) {
    if (returnRequestLine.$.status !== NodeStatus.modified) {
        return;
    }

    const status = await returnRequestLine.status;
    const oldNode = await returnRequestLine.$.old;
    const creditStatus = await returnRequestLine.creditStatus;
    const receiptStatus = await returnRequestLine.receiptStatus;
    await cx.error
        .withMessage(
            '@sage/xtrem-sales/nodes__sales_return_request_line__update_line_cant_be_in_the_closed_status',
            'Update not allowed. The sales return request line is closed.',
        )
        .if(
            (await oldNode.status) === 'closed' &&
                status === 'closed' &&
                creditStatus === (await oldNode.creditStatus) &&
                receiptStatus === (await oldNode.receiptStatus),
        )
        .is.true();
}

export async function checkReceiptStatusOnUpdate(
    cx: ValidationContext,
    returnRequestLine: xtremSales.nodes.SalesReturnRequestLine,
) {
    if (returnRequestLine.$.status !== NodeStatus.modified) {
        return;
    }

    const status = await returnRequestLine.status;
    const oldNode = await returnRequestLine.$.old;
    const creditStatus = await returnRequestLine.creditStatus;
    const receiptStatus = await returnRequestLine.receiptStatus;
    await cx.error
        .withMessage(
            '@sage/xtrem-sales/nodes__sales_return_request__update_line_cant_be_in_the_received_receipt_status',
            'Update not allowed. The sales return request line is received.',
        )
        .if(
            receiptStatus === 'received' &&
                (await oldNode.receiptStatus) === 'received' &&
                (await oldNode.creditStatus) === creditStatus &&
                (await oldNode.status) === status,
        )
        .is.true();
}
