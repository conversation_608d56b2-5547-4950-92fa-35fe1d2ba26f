import type { ValidationContext } from '@sage/xtrem-core';
import { NodeStatus } from '@sage/xtrem-core';
import type * as xtremSales from '../..';

export async function checkSiteOnUpdate(cx: ValidationContext, returnReceipt: xtremSales.nodes.SalesReturnReceipt) {
    if (returnReceipt.$.status !== NodeStatus.modified) {
        return;
    }

    await cx.error
        .withMessage(
            '@sage/xtrem-sales/nodes__sales_return_receipt__stock_site_update_not_allowed',
            'The stock site cannot be updated after the sales return receipt has been created.',
        )
        .if((await returnReceipt.site) !== (await (await returnReceipt.$.old).site))
        .is.true();
}

export async function checkShipToCustomerOnUpdate(
    cx: ValidationContext,
    returnReceipt: xtremSales.nodes.SalesReturnReceipt,
) {
    if (returnReceipt.$.status !== NodeStatus.modified) {
        return;
    }

    await cx.error
        .withMessage(
            '@sage/xtrem-sales/nodes__sales_return_receipt__ship_to_customer_not_allowed',
            'The ship-to-customer cannot be updated after the sales return receipt has been created.',
        )
        .if((await returnReceipt.shipToCustomer) !== (await (await returnReceipt.$.old).shipToCustomer))
        .is.true();
}

export async function checkReturnDateOnUpdate(
    cx: ValidationContext,
    returnReceipt: xtremSales.nodes.SalesReturnReceipt,
) {
    if (returnReceipt.$.status !== NodeStatus.modified) {
        return;
    }

    await cx.error
        .withMessage(
            '@sage/xtrem-sales/nodes__sales_return_receipt__return_date_update_not_allowed',
            'The return date cannot be updated after the sales return receipt has been created.',
        )
        .if((await returnReceipt.returnDate) !== (await (await returnReceipt.$.old).returnDate))
        .is.true();
}
