import type { ValidationContext } from '@sage/xtrem-core';
import { NodeStatus } from '@sage/xtrem-core';
import * as xtremTax from '@sage/xtrem-tax';
import type * as xtremSales from '../../index';

/** Check if at least one of the given taxes has a wrong type. */
export async function checkWrongTaxType(
    cx: ValidationContext,
    document: xtremSales.nodes.SalesOrder | xtremSales.nodes.SalesInvoice | xtremSales.nodes.SalesCreditMemo,
) {
    await cx.error
        .withMessage(
            '@sage/xtrem-sales/nodes__sales_order__tax_type_validation',
            "The tax type for all documents needs to be 'Sales' or 'Purchasing and sales'.",
        )
        .if(
            await xtremTax.functions.hasWrongTaxType({
                taxes: await document.taxes.toArray(),
                validTypes: ['sales', 'purchasingAndSales'],
            }),
        )
        .is.true();
}

/**
 *  Does the common part of controlBegin for a Sales invoice or credit memo
 * @param document either a Sales invoice or a sales credit memo for which the control should be done
 */
export async function checkDocumentStatus(
    cx: ValidationContext,
    document: xtremSales.nodes.SalesInvoice | xtremSales.nodes.SalesCreditMemo,
): Promise<void> {
    if (document.$.status === NodeStatus.added) {
        return;
    }

    await cx.error
        .withMessage(
            '@sage/xtrem-sales/nodes__sales_document__update_to_status_closed_not_allowed',
            'You cannot update the document status to Posted.',
        )
        .if(
            !(await document.forceUpdateForFinance) &&
                ['posted', 'inProgress', 'error'].includes(await document.status) &&
                !['posted', 'inProgress', 'error'].includes(await (await document.$.old).status),
        )
        .is.true();
}
