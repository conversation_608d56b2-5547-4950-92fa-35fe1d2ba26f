import type { ValidationContext } from '@sage/xtrem-core';
import * as xtremSales from '../..';

/**
 * Returns a error message if the customer is on hold
 * @param salesShipment - sales shipment instance
 * @returns void
 */
export async function checkCustomerOnHoldWhenAdded(
    cx: ValidationContext,
    salesShipment: xtremSales.nodes.SalesShipment,
) {
    if (
        xtremSales.functions.isBlockedOnHoldCustomer(
            await salesShipment.isOnHold,
            await (
                await (
                    await salesShipment.site
                ).legalCompany
            ).customerOnHoldCheck,
        )
    ) {
        cx.error.addLocalized(
            '@sage/xtrem-sales/nodes__sales_shipment__creation_impossible_bill_to_customer_is_on_hold',
            'Creation is not allowed. The Bill-to customer is on hold.',
        );
    }
}

export async function checkShipmentStatus(cx: ValidationContext, salesShipment: xtremSales.nodes.SalesShipment) {
    const old = await salesShipment.$.old;
    if (
        !salesShipment.canUpdateIsPrinted &&
        !(await salesShipment.forceUpdateForFinance) &&
        (await old.status) === 'shipped' &&
        (await salesShipment.invoiceStatus) === (await old.invoiceStatus) &&
        (await salesShipment.returnRequestStatus) === (await old.returnRequestStatus) &&
        (await salesShipment.returnReceiptStatus) === (await old.returnReceiptStatus) &&
        (await salesShipment.trackingNumber) === (await old.trackingNumber)
    ) {
        cx.error.addLocalized(
            '@sage/xtrem-sales/nodes__sales_shipment__update_not_allowed_status_closed',
            'The sales shipment is closed. You cannot update it.',
        );
    }

    const salesShipmentStatus = await salesShipment.status;
    if ((await old.status) === 'shipped' && salesShipmentStatus === 'readyToShip') {
        cx.error.addLocalized(
            '@sage/xtrem-sales/nodes__sales_shipment__update_not_allowed_posting_in_progress',
            'The document is shipped. You cannot revert the status to: {{newStatus}}.',
            {
                newStatus: salesShipment.$.context.localizeEnumMember(
                    '@sage/xtrem-sales/SalesShipmentStatus',
                    salesShipmentStatus,
                ),
            },
        );
    }
}

export async function checkIfStockSiteExists(cx: ValidationContext, salesShipment: xtremSales.nodes.SalesShipment) {
    await cx.error
        .withMessage(
            '@sage/xtrem-sales/nodes__sales_shipment__stock_site_is_required',
            'The stock site of the sales shipment is required.',
        )
        .if(!(await salesShipment.stockSite))
        .is.true();
}
