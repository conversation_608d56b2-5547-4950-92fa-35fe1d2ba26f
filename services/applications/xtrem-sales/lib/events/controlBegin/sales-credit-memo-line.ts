import type { ValidationContext } from '@sage/xtrem-core';
import { NodeStatus } from '@sage/xtrem-core';
import type * as xtremSales from '../..';

export async function checkHeaderStatus(
    cx: ValidationContext,
    salesCreditMemoLine: xtremSales.nodes.SalesCreditMemoLine,
) {
    if (salesCreditMemoLine.$.status !== NodeStatus.added) {
        return;
    }

    await cx.error
        .withMessage(
            '@sage/xtrem-sales/nodes__sales_credit_memo_line__improper_status_during_creation',
            'You can only add a line if the status of the sales credit memo is set to Draft.',
        )
        .if((await (await salesCreditMemoLine.document).status) !== 'draft')
        .is.true();
}

export async function checkPostedStatus(
    cx: ValidationContext,
    salesCreditMemoLine: xtremSales.nodes.SalesCreditMemoLine,
) {
    if (salesCreditMemoLine.$.status !== NodeStatus.modified) {
        return;
    }

    const document = await salesCreditMemoLine.document;

    await cx.error
        .withMessage(
            '@sage/xtrem-sales/nodes__sales_credit_memo_line__update_not_allowed_status_posted',
            'The sales credit memo is posted. You cannot update it.',
        )
        .if(
            ['posted', 'inProgress', 'error'].includes(await (await document.$.old).status) &&
                !(await document.forceUpdateForFinance),
        )
        .is.true();
}
