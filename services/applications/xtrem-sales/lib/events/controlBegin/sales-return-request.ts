import type { ValidationContext } from '@sage/xtrem-core';
import { NodeStatus } from '@sage/xtrem-core';
import type * as xtremSales from '../..';

export async function checkClosedStatusOnUpdate(
    cx: ValidationContext,
    returnRequest: xtremSales.nodes.SalesReturnRequest,
) {
    if (returnRequest.$.status !== NodeStatus.modified) {
        return;
    }

    const oldNode = await returnRequest.$.old;
    await cx.error
        .withMessage(
            '@sage/xtrem-sales/nodes__sales_return__update_not_allowed_status_closed',
            'The sales return request is closed. You cannot update it.',
        )
        .if(
            (await oldNode.status) === 'closed' &&
                (await returnRequest.status) === 'closed' &&
                (await returnRequest.creditStatus) === (await oldNode.creditStatus) &&
                (await returnRequest.receiptStatus) === (await oldNode.receiptStatus) &&
                (await returnRequest.approvalStatus) === (await oldNode.approvalStatus),
        )
        .is.true();
}

export async function checkSiteOnUpdate(cx: ValidationContext, returnRequest: xtremSales.nodes.SalesReturnRequest) {
    if (returnRequest.$.status !== NodeStatus.modified) {
        return;
    }

    await cx.error
        .withMessage(
            '@sage/xtrem-sales/nodes__sales_return__header_sales_site_not_updatable',
            'The sales site of the sales return request must not be changed.',
        )
        .if((await (await returnRequest.$.old).site) !== (await returnRequest.site))
        .is.true();
}

export async function checkSoldToCustomerOnUpdate(
    cx: ValidationContext,
    returnRequest: xtremSales.nodes.SalesReturnRequest,
) {
    if (returnRequest.$.status !== NodeStatus.modified) {
        return;
    }

    await cx.error
        .withMessage(
            '@sage/xtrem-sales/nodes__sales_return__header_sold_to_customer_not_updatable',
            'The sold-to customer of the sales return request must not be changed.',
        )
        .if((await (await returnRequest.$.old).soldToCustomer) !== (await returnRequest.soldToCustomer))
        .is.true();
}

export async function checkReturnTypeOnUpdate(
    cx: ValidationContext,
    returnRequest: xtremSales.nodes.SalesReturnRequest,
) {
    if (returnRequest.$.status !== NodeStatus.modified) {
        return;
    }

    await cx.error
        .withMessage(
            '@sage/xtrem-sales/nodes__sales_return_request_update_not_allowed',
            "You cannot update the return type. You can only update it if the sales return request status is 'Draft' and if it has not been approved.",
        )
        .if(
            (await (await returnRequest.$.old).returnType) !== (await returnRequest.returnType) &&
                (['approved', 'rejected'].includes(await returnRequest.approvalStatus) ||
                    (await returnRequest.status) !== 'draft'),
        )
        .is.true();
}

export async function checkSiteConsistency(cx: ValidationContext, returnRequest: xtremSales.nodes.SalesReturnRequest) {
    const site = await returnRequest.site;
    const stockSite = await returnRequest.stockSite;
    await cx.error
        .withMessage(
            '@sage/xtrem-sales/nodes__sales_return_request__improper_stock_and_sales_site_value__during_creation',
            'Incorrect stock site on the sales return request. The sales site and the stock site must have the same company.',
        )
        .if(site && stockSite && (await site.legalCompany) !== (await stockSite.legalCompany))
        .is.true();
}
