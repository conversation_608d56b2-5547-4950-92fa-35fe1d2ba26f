import type { ValidationContext } from '@sage/xtrem-core';
import type * as xtremSales from '../..';

export async function frozeSaleUnitAfterCreation(cx: ValidationContext, line: xtremSales.nodes.SalesInvoiceLine) {
    if ((await (await line.$.old).salesUnit)._id !== (await line.salesUnit)._id) {
        cx.error.addLocalized(
            '@sage/xtrem-sales/nodes__sales_invoice_line__sales_unit_update_not_allowed',
            'The sales unit cannot be updated after the sales invoice has been created.',
        );
    }
}

export async function checkIsInvoiceIsCreatedAmidItemUpdate(
    cx: ValidationContext,
    line: xtremSales.nodes.SalesInvoiceLine,
) {
    const old = (await (await line.$.old).item)._id;
    const current = (await line.item)._id;
    if (old !== current) {
        cx.error.addLocalized(
            '@sage/xtrem-sales/nodes__sales_invoice_line__item_update_not_allowed',
            'The item cannot be updated after the sales invoice has been created.',
        );
    }
}

export async function checkIfInvoiceLineIsLinkedToSalesShipmentLine(
    cx: ValidationContext,
    line: xtremSales.nodes.SalesInvoiceLine,
) {
    if (
        (await line.quantityInSalesUnit) !== (await (await line.$.old).quantityInSalesUnit) &&
        (await line.salesShipmentLines.length) > 0
    ) {
        cx.error.addLocalized(
            '@sage/xtrem-sales/nodes__sales_invoice_line__update_forbidden_shipment_linked',
            'Quantity update is not allowed. The sales invoice line is linked to a sales shipment line.',
        );
    }
}

export async function checkIfSelectedItemIsStockManaged(
    cx: ValidationContext,
    line: xtremSales.nodes.SalesInvoiceLine,
) {
    if ((await (await line.item).isStockManaged) && (await line.salesShipmentLines.length) === 0) {
        cx.error.addLocalized(
            '@sage/xtrem-sales/nodes__sales_invoice_line__improper_item_selection',
            'Incorrect item. The stock managed items are not allowed.',
        );
    }
}

export async function checkIfInvoiceDocumentIsInDraftStatus(
    cx: ValidationContext,
    line: xtremSales.nodes.SalesInvoiceLine,
) {
    if ((await (await line.document).status) !== 'draft') {
        cx.error.addLocalized(
            '@sage/xtrem-sales/nodes__sales_invoice_line__improper_status_during_creation',
            "Incorrect sales invoice status. The sales invoice must have the 'Draft' status to create the line.",
        );
    }
}
