import type { ValidationContext } from '@sage/xtrem-core';
import { NodeStatus } from '@sage/xtrem-core';
import type * as xtremSales from '../..';

export async function checkPostedStatus(cx: ValidationContext, salesInvoice: xtremSales.nodes.SalesInvoice) {
    if (salesInvoice.$.status !== NodeStatus.modified) {
        return;
    }

    const oldNode = await salesInvoice.$.old;
    await cx.error
        .withMessage(
            '@sage/xtrem-sales/nodes__sales_invoice__update_not_allowed_status_posted',
            'The sales invoice is posted. You cannot update it.',
        )
        .if(
            !salesInvoice.canUpdateIsPrinted &&
                !salesInvoice.canUpdateIsSent &&
                !(await salesInvoice.forceUpdateForFinance) &&
                ['posted', 'inProgress', 'error'].includes(await oldNode.status) &&
                (await salesInvoice.creditStatus) === (await oldNode.creditStatus),
        )
        .is.true();
}
