import type { Context } from '@sage/xtrem-core';
import { BusinessRuleError, LogicError } from '@sage/xtrem-core';
import type * as xtremMasterData from '@sage/xtrem-master-data';
import type * as xtremSales from '..';

export async function getCreationNumber(
    document: xtremSales.nodes.SalesInvoice | xtremSales.nodes.SalesCreditMemo,
): Promise<string> {
    // Question: why not set it for all legislations?
    const legislation = await (await (await (await document.site).legalCompany).legislation)?.id;
    return ['FR', 'ZA'].includes(legislation) ? document.number : '';
}

type SalesDocumentStatus =
    | xtremSales.enums.SalesOrderStatus
    | xtremSales.enums.SalesInvoiceStatus
    | xtremSales.enums.SalesCreditMemoStatus
    | xtremSales.enums.SalesShipmentStatus
    | xtremSales.enums.SalesReturnRequestStatus;

const postedErrorClosed: SalesDocumentStatus[] = ['posted', 'error', 'closed', 'shipped'];

export function isPostedErrorClosed(status: SalesDocumentStatus) {
    return postedErrorClosed.includes(status);
}

/**
 * Check if the setup is to block an on hold customer
 * @param isOnHold - isOnHold field from customer
 * @param customerOnHoldCheck - customerOnHoldCheck from company
 * @returns boolean
 */
export function isBlockedOnHoldCustomer(
    isOnHold: boolean,
    customerOnHoldCheck: xtremMasterData.enums.CustomerOnHoldType,
): boolean {
    return isOnHold && customerOnHoldCheck === 'blocking';
}

/**
 * Check if the sales document is in a status that allows it to be printed
 */
async function checkStatusForPrinting(
    context: Context,
    salesDocument: xtremSales.nodes.SalesInvoice | xtremSales.nodes.SalesCreditMemo,
    throwOnError: boolean,
) {
    const status = await salesDocument.status;
    if ((await salesDocument.taxCalculationStatus) === 'failed') {
        if (throwOnError) {
            throw new BusinessRuleError(
                context.localize(
                    '@sage/xtrem-sales/functions__sales_invoice_lib__tax_calculation_failed',
                    'You need to resolve tax calculation issues to print the document: {{salesDocumentNumber}}.',
                    { salesDocumentNumber: await salesDocument.number },
                ),
            );
        }
        return false;
    }

    if (['posted', 'draft'].includes(status)) {
        return true;
    }

    if (status === 'error' && throwOnError) {
        throw new BusinessRuleError(
            context.localize(
                '@sage/xtrem-sales/functions__sales_invoice_lib__error_invoice_cannot_be_printed',
                'You need to resolve errors to print the document: {{salesDocumentNumber}}.',
                { salesDocumentNumber: await salesDocument.number },
            ),
        );
    }
    return false;
}

async function checkIsFinancePosted(
    context: Context,
    salesDocument: xtremSales.nodes.SalesInvoice | xtremSales.nodes.SalesCreditMemo,
    throwOnError: boolean,
) {
    const isFinancePosted = await salesDocument.financeTransactions.some(
        async financeTransaction => (await financeTransaction.status) === 'posted',
    );

    if (!isFinancePosted && throwOnError) {
        throw new BusinessRuleError(
            context.localize(
                '@sage/xtrem-sales/functions__sales_invoice_lib__in_progress_invoice_cannot_be_printed',
                'You cannot print the document. It is set to In progress: {{salesDocumentNumber}}.',
                { salesDocumentNumber: await salesDocument.number },
            ),
        );
    }
    return isFinancePosted;
}

/**
 * Check the statuses of the sales document to see if it can be printed
 * @param context
 * @param salesDocument - the SalesInvoice or SalesCreditMemo to check
 * @returns ```
 *  { canPrint: boolean;
 *    enablePrintButton: boolean }
 *
 * The document can be printed if its status is Draft,Posted
 * If the status is error or if the Tax calculation status is failed, the button is enabled but an error will be thrown
 */
export async function checkIfCanPrintSalesInvoice(
    context: Context,
    salesDocument: xtremSales.nodes.SalesInvoice | xtremSales.nodes.SalesCreditMemo,
    throwOnError = false,
): Promise<{ canPrint: boolean; enablePrintButton: boolean }> {
    const status = await salesDocument.status;

    if (status === 'inProgress' && (await salesDocument.taxCalculationStatus) === 'done') {
        const isFinancePosted = await checkIsFinancePosted(context, salesDocument, throwOnError);
        return { canPrint: isFinancePosted, enablePrintButton: isFinancePosted };
    }

    if (['draft', 'posted', 'error'].includes(status)) {
        return {
            canPrint: await checkStatusForPrinting(context, salesDocument, throwOnError),
            enablePrintButton: true,
        };
    }

    // case where the status is not draft, posted, inProgress or error
    // or status is inProgress but taxCalculationStatus is not done
    // => should not happen
    if (throwOnError) {
        throw new LogicError(
            `Cannot print the document. Document details: ${JSON.stringify({
                number: await salesDocument.number,
                status,
                taxCalculationStatus: await salesDocument.taxCalculationStatus,
                displayStatus: await salesDocument.displayStatus,
            })}`,
        );
    }

    return { canPrint: false, enablePrintButton: false };
}
