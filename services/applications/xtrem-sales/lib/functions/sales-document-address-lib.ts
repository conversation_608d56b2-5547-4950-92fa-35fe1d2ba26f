import type { Collection } from '@sage/xtrem-core';
import type * as xtremMasterData from '@sage/xtrem-master-data';

// Add here all collections which can be used into getPrimaryOfAnyCollection
type AnyPrimaryCollection = xtremMasterData.nodes.BusinessEntityAddress | xtremMasterData.nodes.BusinessEntityContact;

/**
 * Get the isPrimary and isActive item of the collection & return the primary item
 * @param collection
 */
export async function getPrimaryAndActiveOfAnyCollection<T extends AnyPrimaryCollection>(
    collection: Collection<T>,
): Promise<T | null> {
    return (await collection.length)
        ? ((await collection.find(item => item.isPrimary)) ?? collection.elementAt(0))
        : null;
}

export const getSalesAddressDetail = (
    businessEntityAddress: xtremMasterData.nodes.BusinessEntityAddress | null,
): Promise<xtremMasterData.nodes.Address | null> => {
    return businessEntityAddress?.address ?? Promise.resolve(null);
};

export const getSalesContact = (
    businessEntityContact: xtremMasterData.nodes.BusinessEntityContact,
): Promise<xtremMasterData.nodes.Contact | null> => {
    return businessEntityContact?.contact ?? Promise.resolve(null);
};

export const getBusinessEntityContact = async (
    businessEntityAddress: xtremMasterData.nodes.BusinessEntityAddress,
): Promise<xtremMasterData.nodes.Contact | null> => {
    // Note: businessEntityAddress can be null when running a 'getDefaults' query
    if (!businessEntityAddress?.contacts) {
        return null;
    }
    const customerContact = await getPrimaryAndActiveOfAnyCollection(businessEntityAddress.contacts);

    if (!customerContact) {
        return null;
    }

    return getSalesContact(customerContact);
};
