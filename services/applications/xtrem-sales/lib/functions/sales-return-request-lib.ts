import type { Context, decimal } from '@sage/xtrem-core';
import { BusinessRuleError, date, NodeStatus } from '@sage/xtrem-core';
import * as xtremSales from '../index';
import { setHeaderInternalNote, setHeaderSetupNote, setLineInternalNote } from './common';

export async function controlQuantityInSalesUnit(
    context: Context,
    salesReturnRequestLine: xtremSales.nodes.SalesReturnRequestLine,
    newQuantityInSalesUnit: decimal,
): Promise<string | null> {
    const { finalQuantityInSalesUnit, quantityReceived, quantityCredited } =
        await xtremSales.classes.SalesReturnRequestCreator.getFinalQuantityInSalesUnit(
            salesReturnRequestLine,
            newQuantityInSalesUnit,
            context,
        );
    if ((await salesReturnRequestLine.origin) === 'shipment') {
        const salesShipmentLine = await salesReturnRequestLine.salesShipmentLines.elementAt(0);
        const linkedDocument = await salesShipmentLine.linkedDocument;
        const totalQuantity = await xtremSales.classes.SalesReturnRequestCreator.getTotalQuantityFromLines(
            {
                linkedDocument: { _id: linkedDocument._id },
                _id: { _ne: salesShipmentLine._id },
            },
            context,
            xtremSales.nodes.SalesShipmentLineToSalesReturnRequestLine,
        );
        if ((await linkedDocument.quantity) - totalQuantity < finalQuantityInSalesUnit) {
            return context.localize(
                '@sage/xtrem-sales/nodes__sales_return_request__remaining_quantity',
                'The sales return line quantity cannot be larger than the remaining quantity on the related sales shipment line.',
            );
        }
    }
    if (newQuantityInSalesUnit < quantityReceived) {
        return context.localize(
            '@sage/xtrem-sales/nodes__sales_return_request__under_the_quantity_already_received',
            'The sales return line quantity cannot be lower than the already received quantity.',
        );
    }
    const document = await salesReturnRequestLine.document;
    const oldLine = await salesReturnRequestLine.$.old;
    const oldQuantityInSalesUnit = await oldLine.quantity;
    if ((await document.returnType) === 'creditMemo' && newQuantityInSalesUnit < quantityCredited) {
        return context.localize(
            '@sage/xtrem-sales/nodes__sales_return_request__under_the_quantity_already_credited',
            'The sales return line quantity cannot be lower than the already credited quantity.',
        );
    }
    if (['approved', 'rejected'].includes(await document.approvalStatus)) {
        if (newQuantityInSalesUnit > oldQuantityInSalesUnit) {
            return context.localize(
                '@sage/xtrem-sales/nodes__sales_return_request__not_possible_to_increase_the_line_quantity',
                "You cannot increase the line quantity when the sales return request is 'Approved' or 'Rejected'.",
            );
        }
        if (quantityReceived > 0 && newQuantityInSalesUnit < oldQuantityInSalesUnit) {
            return context.localize(
                '@sage/xtrem-sales/nodes__sales_return_request__not_possible_to_decrease_the_line_quantity',
                "You cannot decrease the line quantity when the sales return request is 'Approved' or 'Rejected' and the quantity is received.",
            );
        }
    }
    return null;
}

/**
 * Calculates the display status of the return request based on the other document status properties
 * @param status
 * @param receiptStatus
 * @param approvalStatus
 * @returns display status enum
 */
export function calculateSalesReturnRequestDisplayStatus(
    status: xtremSales.enums.SalesReturnRequestStatus,
    receiptStatus: xtremSales.enums.SalesDocumentReturnRequestReceiptStatus,
    approvalStatus: xtremSales.enums.SalesReturnRequestApprovalStatus,
): xtremSales.enums.SalesReturnRequestDisplayStatus {
    if (receiptStatus === 'received') {
        return 'received';
    }
    if (approvalStatus === 'rejected') {
        return 'rejected';
    }
    if (status === 'closed') {
        return 'closed';
    }
    if (receiptStatus === 'partiallyReceived') {
        return 'partiallyReceived';
    }
    if (approvalStatus === 'approved') {
        return 'approved';
    }
    if (approvalStatus === 'pendingApproval') {
        return 'pendingApproval';
    }
    if (approvalStatus === 'confirmed') {
        return 'confirmed';
    }
    return 'draft';
}

export async function confirm(context: Context, salesReturnRequestNumber: string): Promise<void> {
    const salesReturnRequest = await context.read(
        xtremSales.nodes.SalesReturnRequest,
        {
            number: salesReturnRequestNumber,
        },
        { forUpdate: true },
    );

    const salesReturnRequestStatus = await salesReturnRequest.status;
    const isSalesReturnRequestApprovalManaged = await (
        await salesReturnRequest.site
    ).isSalesReturnRequestApprovalManaged;

    if (salesReturnRequestStatus !== 'draft' || isSalesReturnRequestApprovalManaged) {
        throw new BusinessRuleError(
            context.localize(
                '@sage/xtrem-sales/status_and_is_purchase_requisition_approval_managed',
                'Document status needs to be "Draft" and approval workflow disabled',
            ),
        );
    }

    await salesReturnRequest.$.set({ approvalStatus: 'confirmed', status: 'pending' });

    await salesReturnRequest.lines.forEach(async line => {
        await line.$.set({ status: 'confirmed' });
    });

    await salesReturnRequest.$.save();
}

export function linkedShipmentsArray(
    instance: xtremSales.nodes.SalesReturnRequest,
): Promise<xtremSales.nodes.SalesShipment[]> {
    return instance.$.context
        .query(xtremSales.nodes.SalesShipment, {
            filter: { _id: { _in: Object.keys(instance.__salesShipments) } },
            forUpdate: true,
        })
        .toArray();
}

export async function updateHeaderNotesOnCreation(instance: xtremSales.nodes.SalesReturnRequest) {
    if (instance.$.status === NodeStatus.added) {
        const salesShipmentArray = await linkedShipmentsArray(instance);
        if (salesShipmentArray.length === 1) {
            const isTransferHeaderNote = await salesShipmentArray[0].isTransferHeaderNote;
            if (isTransferHeaderNote) {
                const currentNote: xtremSales.interfaces.CurrentNote = {
                    isOverwrite: await instance.isOverwriteNote,
                    internalNote: await instance.internalNote,
                };
                await setHeaderInternalNote(instance, salesShipmentArray, currentNote);
            }
            await setHeaderSetupNote(instance, isTransferHeaderNote, await salesShipmentArray[0].isTransferLineNote);
        }
    }
}

export async function updateLineNotesOnCreation(instance: xtremSales.nodes.SalesReturnRequestLine) {
    if (instance.$.status === NodeStatus.added && (await instance.salesShipmentLines.length)) {
        const linkedDocument = await (await instance.salesShipmentLines.elementAt(0)).linkedDocument;
        if (await (await linkedDocument.document).isTransferLineNote) {
            const currentNote: xtremSales.interfaces.CurrentNote = {
                isOverwrite: await (await instance.document).isOverwriteNote,
                internalNote: await instance.internalNote,
            };
            await setLineInternalNote(instance, linkedDocument, currentNote);
        }
    }
}

export async function getCreditMemoInstance(
    context: Context,
    returnRequest: xtremSales.nodes.SalesReturnRequest,
): Promise<xtremSales.nodes.SalesCreditMemo | null> {
    const salesCreditMemoCreator = new xtremSales.classes.SalesCreditMemosCreator(context, date.today());

    await returnRequest.lines.forEach(async line => {
        await salesCreditMemoCreator.prepareNodeCreateData([{ salesDocumentLine: line }]);
    });

    if (Object.keys(salesCreditMemoCreator.salesOutputDocuments).length === 1) {
        const creditMemoPayload =
            salesCreditMemoCreator.salesOutputDocuments[Object.keys(salesCreditMemoCreator.salesOutputDocuments)[0]];
        return context.create(xtremSales.nodes.SalesCreditMemo, creditMemoPayload, { isTransient: true });
    }
    return null;
}
