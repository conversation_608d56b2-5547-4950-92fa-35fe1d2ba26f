import type * as xtremSystem from '@sage/xtrem-system';
import * as xtremSales from '../index';

export async function getStockSiteDefaultValue(
    sourceNode: xtremSales.nodes.SalesReturnRequest | xtremSales.nodes.SalesOrder | xtremSales.nodes.SalesShipment,
): Promise<xtremSystem.nodes.Site | null> {
    const shipToCustomerAddress = await sourceNode.shipToCustomerAddress;
    if (!shipToCustomerAddress) {
        return null;
    }
    const shipmentSite = await (await shipToCustomerAddress.deliveryDetail)?.shipmentSite;
    const site = await sourceNode.site;
    if (shipmentSite && !site) {
        return shipmentSite;
    }
    if (shipmentSite && site && (await shipmentSite.legalCompany) === (await site.legalCompany)) {
        return shipmentSite;
    }
    if ((await site?.isInventory) === true) {
        return site;
    }
    return null;
}

export async function getStockSiteValue(
    sourceNode: xtremSales.nodes.SalesReturnRequest | xtremSales.nodes.SalesOrder | xtremSales.nodes.SalesShipment,
): Promise<xtremSystem.nodes.Site | null> {
    const site = await xtremSales.functions.getStockSiteDefaultValue(sourceNode);
    const sourceNodeOld = await sourceNode.$.old;
    if (site === null && sourceNodeOld && (await sourceNodeOld.stockSite) !== null) {
        return sourceNodeOld.stockSite;
    }
    return site;
}

export async function getIsQuoteValue(sourceNode: xtremSales.nodes.SalesOrder): Promise<boolean> {
    if ((await sourceNode.status) === 'quote') {
        return true;
    }
    return false;
}
