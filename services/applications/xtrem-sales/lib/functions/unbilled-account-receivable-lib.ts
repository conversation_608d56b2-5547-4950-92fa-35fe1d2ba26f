import type { Context, NodeQueryFilter } from '@sage/xtrem-core';
import { Decimal } from '@sage/xtrem-decimal';
import * as xtremFinanceData from '@sage/xtrem-finance-data';
import * as xtremMasterData from '@sage/xtrem-master-data';
import { merge } from 'lodash';
import * as xtremSales from '../index';

function getCompanyFilter(company: string | undefined): NodeQueryFilter<xtremSales.nodes.SalesShipmentLine> {
    return company ? { document: { stockSite: { legalCompany: { id: { _eq: company } } } } } : {};
}

function getStockSitesFilter(stockSitesIds: number[] | undefined): NodeQueryFilter<xtremSales.nodes.SalesShipmentLine> {
    return stockSitesIds && stockSitesIds.length > 0
        ? { document: { stockSite: { _id: { _in: stockSitesIds } } } }
        : {};
}

function getFromCustomerFilter(fromCustomer: string | undefined): NodeQueryFilter<xtremSales.nodes.SalesShipmentLine> {
    return fromCustomer
        ? {
              document: {
                  shipToCustomer: {
                      businessEntity: {
                          id: {
                              _gte: fromCustomer,
                          },
                      },
                  },
              },
          }
        : {};
}

function getToCustomerFilter(toCustomer: string | undefined): NodeQueryFilter<xtremSales.nodes.SalesShipmentLine> {
    return toCustomer
        ? {
              document: {
                  shipToCustomer: {
                      businessEntity: {
                          id: {
                              _lte: toCustomer,
                          },
                      },
                  },
              },
          }
        : {};
}

export function getUnbilledAccountReceivableFilter(
    searchCriteria: xtremFinanceData.interfaces.FinanceUnbilledAccountReceivableSearch,
): NodeQueryFilter<xtremSales.nodes.SalesShipmentLine> {
    // build filter from search criteria
    const filter: NodeQueryFilter<xtremSales.nodes.SalesShipmentLine> = {
        document: { shippingDate: { _lte: searchCriteria.asOfDate } },
        netPrice: { _gt: 0 },
        status: 'shipped',
    };

    merge(filter, getCompanyFilter(searchCriteria.company));
    merge(filter, getStockSitesFilter(searchCriteria.stockSites));
    merge(filter, getFromCustomerFilter(searchCriteria.fromCustomer));
    merge(filter, getToCustomerFilter(searchCriteria.toCustomer));

    return filter;
}

// if shipment line is partially or fully invoiced, loop over linked invoice lines and add invoiced quantity
// to the total invoice quantity.
// Additionally we accumulate the credited quantity for the same invoice lines by looping over linked credit memo
// lines and adding credited quantity to the total credited quantity.
async function getInvoicedAndCreditedQuantity(options: {
    context: Context;
    searchCriteria: xtremFinanceData.interfaces.FinanceUnbilledAccountReceivableSearch;
    shipmentLine: xtremSales.nodes.SalesShipmentLine;
    quantities: xtremFinanceData.interfaces.UnbilledInquiriesQuantities;
}): Promise<xtremFinanceData.interfaces.UnbilledInquiriesQuantities> {
    if (['invoiced', 'partiallyInvoiced'].includes(await options.shipmentLine.invoiceStatus)) {
        const toInvoiceLines = options.context.queryAggregate(xtremSales.nodes.SalesShipmentLineToSalesInvoiceLine, {
            filter: {
                linkedDocument: options.shipmentLine._id,
                document: { document: { status: 'posted', date: { _lte: options.searchCriteria.asOfDate } } },
            },
            group: { document: { _id: { _by: 'value' } } },
            values: { document: { quantity: { sum: true } } },
        });

        const invoiceLineSysIds: number[] = await toInvoiceLines
            .map(shipmentLineToInvoiceLine => shipmentLineToInvoiceLine.group.document._id)
            .toArray();

        const creditMemoLines = options.context.queryAggregate(xtremSales.nodes.SalesInvoiceLineToSalesCreditMemoLine, {
            filter: {
                linkedDocument: { _in: invoiceLineSysIds },
                document: {
                    document: { date: { _lte: options.searchCriteria.asOfDate } },
                    origin: 'return',
                },
            },
            group: {},
            values: { document: { quantity: { sum: true } } },
        });
        return {
            invoiced: (await toInvoiceLines.length)
                ? ((await toInvoiceLines.elementAt(0)).values.document.quantity.sum ?? 0)
                : 0,
            credited: (await creditMemoLines.length)
                ? ((await creditMemoLines.elementAt(0)).values.document.quantity.sum ?? 0)
                : 0,
            returned: options.quantities.returned ?? 0,
        };
    }
    return options.quantities;
}

// if shipment line is partially or fully returned loop over linked return receipt lines and add returned quantity
// to the total returned quantity.
async function getReturnedQuantity(options: {
    context: Context;
    searchCriteria: xtremFinanceData.interfaces.FinanceUnbilledAccountReceivableSearch;
    shipmentLine: xtremSales.nodes.SalesShipmentLine;
}): Promise<number> {
    if (['returned', 'partiallyReturned'].includes(await options.shipmentLine.returnReceiptStatus)) {
        const returnLines = options.context.queryAggregate(xtremSales.nodes.SalesShipmentLineToSalesReturnReceiptLine, {
            filter: {
                linkedDocument: options.shipmentLine._id,
                document: { document: { date: { _lte: options.searchCriteria.asOfDate }, status: 'closed' } },
            },
            group: {},
            values: { document: { quantity: { sum: true } } },
        });
        return (await returnLines.elementAt(0)).values.document.quantity.sum ?? 0;
    }
    return 0;
}

export async function generateUnbilledAccountReceivableLine(parameters: {
    context: Context;
    shipmentLine: xtremSales.nodes.SalesShipmentLine;
    searchCriteria: xtremFinanceData.interfaces.FinanceUnbilledAccountReceivableSearch;
}): Promise<xtremFinanceData.interfaces.FinanceUnbilledAccountReceivable> {
    const { invoiced, credited, returned } = await getInvoicedAndCreditedQuantity({
        ...parameters,
        quantities: {
            invoiced: 0,
            credited: 0,
            returned: await getReturnedQuantity({ ...parameters }),
        },
    });

    const { invoicedQuantity, invoicedAmount } = xtremFinanceData.functions.Common.calculateInvoicedValues({
        lineQuantity: await parameters.shipmentLine.quantity,
        lineNetPrice: await parameters.shipmentLine.netPrice,
        quantities: { invoiced, credited, returned },
    });

    // Performance: Some values not to await more than once
    const document = await parameters.shipmentLine.document;
    const financialSite = await document.financialSite;
    const legalCompany = await financialSite.legalCompany;
    const currency = await parameters.shipmentLine.currency;
    const companyCurrency = await document.companyCurrency;
    const quantity = await parameters.shipmentLine.quantity;

    const amountExcludingTaxInCompanyCurrency = await parameters.shipmentLine.amountExcludingTaxInCompanyCurrency;
    const invoiceIssuableAmountInCompanyCurrency = Decimal.roundAt(
        (amountExcludingTaxInCompanyCurrency * invoicedQuantity) / quantity,
        await companyCurrency.decimalDigits,
    );
    const invoiceIssuableAmountInCompanyCurrencyAtAsOfDate = await xtremMasterData.functions.convertCurrency(
        parameters.context,
        invoicedAmount,
        currency,
        companyCurrency,
        parameters.searchCriteria.asOfDate,
    );

    const tax = await (
        await (await (await parameters.shipmentLine.salesOrderLines.at(0))?.linkedDocument)?.taxes.at(0)
    )?.taxReference;

    return {
        customer: await document.billToCustomer,
        currency,
        financialSite: await document.financialSite,
        salesUnit: await parameters.shipmentLine.unit,
        netPrice: await parameters.shipmentLine.netPrice,
        item: await parameters.shipmentLine.item,
        site: await document.stockSite,
        quantity,
        invoicedQuantity: invoiced,
        creditedQuantity: credited,
        returnedQuantity: returned,
        invoiceIssuableQuantity: invoicedQuantity,
        invoiceIssuableAmount: invoicedAmount,
        invoiceIssuableAmountInCompanyCurrency,
        invoiceIssuableAmountInCompanyCurrencyAtAsOfDate,
        companyCurrency,
        account: await xtremFinanceData.functions.Common.getAccount(parameters.context, {
            chartOfAccount: await legalCompany.chartOfAccount,
            legislation: await legalCompany.legislation,
            type: 'customer',
            id: 'ArGsni',
            postingClassDetailed: await (await document.billToCustomer).postingClass,
            invoicedQuantity,
            tax,
        }),
        accountItem: await xtremFinanceData.functions.Common.getAccount(parameters.context, {
            chartOfAccount: await legalCompany.chartOfAccount,
            legislation: await legalCompany.legislation,
            type: 'item',
            id: 'SalesRevenue',
            postingClassDetailed: await (await parameters.shipmentLine.item).postingClass,
            invoicedQuantity,
            tax,
        }),
        company: await financialSite.legalCompany,
        shipmentNumber: await document.number,
        shipmentInternalId: document._id,
        documentDate: await document.documentDate,
        shipToCustomer: await document.shipToCustomer,
    };
}
