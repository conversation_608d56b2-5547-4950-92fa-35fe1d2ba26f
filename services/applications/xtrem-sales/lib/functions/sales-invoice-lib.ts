import type { Context, NodeCreateData, UpdateAction, date } from '@sage/xtrem-core';
import { BusinessRuleError, Logger, NodeStatus } from '@sage/xtrem-core';
import * as xtremDistribution from '@sage/xtrem-distribution';
import * as xtremFinanceData from '@sage/xtrem-finance-data';
import type * as xtremMailer from '@sage/xtrem-mailer';
import * as xtremMasterData from '@sage/xtrem-master-data';
import type * as xtremStructure from '@sage/xtrem-structure';
import * as xtremSales from '..';
import {
    getCurrentHeaderNote,
    getCurrentLineNote,
    setHeaderExternalNote,
    setHeaderInternalNote,
    setHeaderSetupNote,
    setLineExternalNote,
    setLineInternalNote,
} from './common';

const logger = Logger.getLogger(__filename, 'sales-invoice-lib');

/**
 * Calculates the display status of the order based on the other document status properties
 * @param status
 * @param creditStatus
 * @param taxCalculationStatus
 * @returns display status enum
 */
export function calculateSalesInvoiceDisplayStatus(
    status: xtremSales.enums.SalesInvoiceStatus,
    creditStatus: xtremSales.enums.SalesDocumentCreditStatus,
    taxCalculationStatus: xtremMasterData.enums.TaxCalculationStatus,
    paymentStatus: xtremFinanceData.enums.OpenItemStatus | null,
): xtremSales.enums.SalesInvoiceDisplayStatus {
    if (taxCalculationStatus === 'failed') {
        return 'taxCalculationFailed';
    }
    if (status === 'error') {
        return 'error';
    }
    if (paymentStatus && paymentStatus === 'paid') {
        return 'paid';
    }
    if (paymentStatus && paymentStatus === 'partiallyPaid') {
        return 'partiallyPaid';
    }
    if (creditStatus === 'credited') {
        return 'credited';
    }
    if (creditStatus === 'partiallyCredited') {
        return 'partiallyCredited';
    }
    if (status === 'posted') {
        return 'posted';
    }
    if (status === 'inProgress') {
        return 'postingInProgress';
    }
    return 'draft';
}

export function controlPost(
    context: Context,
    parameters: {
        postResult: xtremFinanceData.interfaces.MutationResult;
        isSafeToRetry: boolean;
        status: xtremSales.enums.SalesInvoiceStatus;
        taxCalculationStatus: xtremMasterData.enums.TaxCalculationStatus;
        discountPaymentBeforeDate: date | null;
        dueDate: date;
    },
): boolean {
    if (parameters.postResult.message.length) {
        if (parameters.isSafeToRetry) {
            return false;
        }
        throw new BusinessRuleError(parameters.postResult.message);
    }

    if (parameters.taxCalculationStatus !== 'failed' && parameters.status !== 'inProgress') {
        if (parameters.status !== 'draft') {
            if (parameters.isSafeToRetry) {
                return false;
            }
            throw new BusinessRuleError(
                context.localize(
                    '@sage/xtrem-sales/cant_post_invoice_when_status_is_not_draft',
                    'The status is not {{draft}}, the invoice cannot be posted.',
                    { draft: context.localizeEnumMember('@sage/xtrem-sales/SalesInvoiceStatus', 'draft') },
                ),
            );
        }

        if (parameters.taxCalculationStatus !== 'done') {
            if (parameters.isSafeToRetry) {
                return false;
            }
            throw new BusinessRuleError(
                context.localize(
                    '@sage/xtrem-sales/cant_post_invoice_when_taxCalculationStatus_is_not_done',
                    'The tax calculation is not {{done}}, the invoice cannot be posted.',
                    { done: context.localizeEnumMember('@sage/xtrem-master-data/TaxCalculationStatus', 'done') },
                ),
            );
        }
    }

    if (xtremDistribution.functions.dateCheckControl(context, parameters)) return false;

    return true;
}

async function isInvoiceValidForNewSequenceNumber(document: {
    legislation: xtremStructure.nodes.Legislation;
    status: xtremSales.enums.SalesInvoiceStatus;
    taxCalculationStatus: xtremMasterData.enums.TaxCalculationStatus;
}): Promise<boolean> {
    return (
        ['FR', 'ZA'].includes(await document.legislation.id) &&
        ((document.status !== 'inProgress' && document.taxCalculationStatus !== 'failed') ||
            document.status !== 'posted')
    );
}

export async function allocateNewSequenceNumber(
    context: Context,
    parameters: {
        invoice: xtremSales.nodes.SalesInvoice;
        status?: xtremSales.enums.SalesInvoiceStatus;
        taxCalculationStatus?: xtremMasterData.enums.TaxCalculationStatus;
    },
): Promise<string> {
    // Set new sequence number for FR/ZA
    if (
        await isInvoiceValidForNewSequenceNumber({
            legislation: await (await (await parameters.invoice.site).legalCompany).legislation,
            status: parameters.status ?? (await parameters.invoice.status),
            taxCalculationStatus: parameters.taxCalculationStatus ?? (await parameters.invoice.taxCalculationStatus),
        })
    ) {
        return (
            await xtremMasterData.classes.DocumentNumberGenerator.create(context, {
                nodeInstance: parameters.invoice,
                posting: true,
            })
        ).allocate();
    }
    return '';
}

async function setIsPrinted(invoice: xtremSales.nodes.SalesInvoice) {
    if ((await xtremSales.functions.checkIfCanPrintSalesInvoice(invoice.$.context, invoice)).canPrint) {
        let paidAmounts = {};
        if ((await invoice.arOpenItems.length) === 1 && (await invoice.financeIntegrationApp) === 'intacct') {
            const openItem = await invoice.arOpenItems.elementAt(0);
            paidAmounts = {
                transactionAmountPaid: await openItem.transactionAmountPaid,
                companyAmountPaid: await openItem.companyAmountPaid,
                financialSiteAmountPaid: await openItem.financialSiteAmountPaid,
            };
        }
        await invoice.$.set({
            isPrinted: true,
            ...paidAmounts,
        });
    }
}

async function setIsPrintedIsSent(context: Context, invoice: xtremSales.nodes.SalesInvoice, isSent: boolean) {
    if ((await invoice.status) === 'posted') {
        await setIsPrinted(invoice);
        if (isSent) {
            await invoice.$.set({ isSent: true });
        }
        await invoice.$.save();
        if (await invoice.isPrinted) {
            // send notification in order to set the ar invoice printing status
            await xtremFinanceData.functions.notifyDocumentIsPrinted(
                context,
                'salesInvoice' as xtremFinanceData.enums.FinanceDocumentType,
                invoice._id,
            );
        }
    }
}

/**
 * Checks if the invoice can be printed
 * A hook can be added to this function to add custom logic. This is the case for Intacct integration, see SalesDocumentHooks.
 * @param writableContext
 * @param writableInvoice the invoice to check
 * @returns true if the invoice can be printed, false otherwise
 */
export async function beforePrintAndMail(
    writableContext: Context,
    writableInvoice: xtremSales.nodes.SalesInvoice | xtremSales.nodes.SalesCreditMemo,
): Promise<boolean> {
    if (!(await xtremSales.functions.checkIfCanPrintSalesInvoice(writableContext, writableInvoice, true)).canPrint)
        return false;

    return xtremSales.classes.SalesDocumentHooks.beforePrint(writableContext, writableInvoice);
}

/**
 * update "isPrinted" flag of the invoice. If specified, updates "isSent" flag as well
 * @param writableContext
 * @param writableInvoice the invoice to update
 * @param isSent if true, the "isSent" flag will be updated as well
 */
export async function afterPrintAndMail(
    writableContext: Context,
    writableInvoice: xtremSales.nodes.SalesInvoice,
    isSent = false,
) {
    // update "isPrinted" flag if the invoice can be printed
    await setIsPrintedIsSent(writableContext, writableInvoice, isSent);
}

export async function repost(
    context: Context,
    salesInvoice: xtremSales.nodes.SalesInvoice,
    documentData: xtremSales.interfaces.FinanceIntegration.SalesInvoiceCreditMemoRepost,
): Promise<xtremFinanceData.interfaces.MutationResult> {
    if (!xtremFinanceData.functions.canRepost((await salesInvoice.financeIntegrationStatus) || 'submitted')) {
        // submitted used in case of undefined to assure that function will return false
        throw new BusinessRuleError(
            context.localize(
                '@sage/xtrem-sales/nodes__sales-invoice__cant_repost_sales_invoice_when_status_is_not_failed',
                "You can only repost a sales invoice if the status is 'Failed' or 'Not recorded'.",
            ),
        );
    }

    logger.info(`Reposting sales invoice ${await salesInvoice.number}`);

    const financeTransactions = (
        await xtremFinanceData.nodes.FinanceTransaction.getPostingStatusData(context, await salesInvoice.number)
    ).filter(
        financeTransaction =>
            ['postingError', 'generationError'].includes(financeTransaction.status) &&
            !financeTransaction.hasSourceForDimensionLines,
    );

    const targetDocumentTypes = financeTransactions.map(financeTransaction => {
        return financeTransaction.documentType;
    });

    logger.info(`Target document types are ${JSON.stringify(targetDocumentTypes)}`);

    const hasTaxes = documentData.lines.every(line => {
        return !!line.uiTaxes;
    });
    const isAvalaraEnabled = (await salesInvoice.taxEngine) === 'avalaraAvaTax';

    const financeTransaction = targetDocumentTypes.includes('accountsReceivableInvoice')
        ? await context.read(xtremFinanceData.nodes.FinanceTransaction, {
              _id:
                  financeTransactions.filter(line => line.documentType === 'accountsReceivableInvoice').at(0)?._id ?? 0,
          })
        : null;

    // If the user changed the tax data, avalara is not enabled and the invoice is already posted, we need to recreate
    // the tax information on the ap-ap invoice and before that we need to remove the accounting staging data
    // related with that ap-ar invoice
    if (hasTaxes && !isAvalaraEnabled && !!financeTransaction) {
        logger.info(`Document has taxes and Avalara is not enabled`);

        await xtremFinanceData.functions.AccountingEngineCommon.removeAccountingStagingForApArInvoice(
            context,
            financeTransaction,
        );
    }

    // Prepare the lines to be updated
    const updateLines = documentData.lines.map(documentLine => {
        return {
            _action: 'update' as UpdateAction,
            _id: documentLine.baseDocumentLineSysId,
            storedAttributes: documentLine.storedAttributes,
            storedDimensions: documentLine.storedDimensions,
            uiTaxes: hasTaxes ? documentLine.uiTaxes : undefined,
        };
    });

    // Update and save the sales invoice
    await salesInvoice.$.set({ forceUpdateForFinance: true, lines: updateLines, wasTaxDataChanged: hasTaxes });
    if (documentData.header.paymentTerm && ((await salesInvoice.paymentStatus) ?? 'notPaid') === 'notPaid') {
        await salesInvoice.$.set({ paymentTerm: documentData.header.paymentTerm });
    }
    await salesInvoice.$.save();

    // If the user changed the tax data, avalara is not enabled and the invoice is already posted, we need to recreate
    // the tax information on the ap-ap invoice on the staging table
    if (hasTaxes && !isAvalaraEnabled && !!financeTransaction) {
        await xtremFinanceData.functions.AccountingEngineCommon.updateAccountingStagingForApArInvoice(context, {
            financeTransaction,
            document: salesInvoice,
            lines: await salesInvoice.lines.toArray(),
            replyTopic: 'SalesInvoice/accountingInterface',
        });
    }

    // send notification in order to update a staging table entry for the accounting engine and recreate the finance documents
    await xtremFinanceData.functions.purchaseSalesInvoiceCreditMemoUpdateNotification(context, {
        targetDocumentTypes,
        document: salesInvoice,
        lines: await salesInvoice.lines.toArray(),
        documentType: 'salesInvoice',
        replyTopic: 'SalesInvoice/accountingInterface',
    });

    return {
        wasSuccessful: true,
        message: context.localize(
            '@sage/xtrem-sales/nodes__sales-invoice__document_was_posted',
            'The sales invoice was posted.',
        ),
    };
}

export async function printSalesDocumentAndEmail(
    context: Context,
    invoice: xtremSales.nodes.SalesInvoice,
    contactTitle: string,
    contactLastName: string,
    contactFirstName: string,
    contactEmail: string,
) {
    const invoiceNumber = await invoice.number;
    const reports = await xtremMasterData.functions.reportGeneration(context, 'salesInvoice', '', [
        JSON.stringify({ invoice: String(invoice._id) }),
    ]);

    if (reports.length === 0) {
        throw new BusinessRuleError(
            context.localize(
                '@sage/xtrem-sales/nodes__sales_invoice__nothing_to_send',
                'The sales invoice cannot be sent. No report has been created.',
            ),
        );
    }

    // prepare the email template data
    const formattedInvoiceDate = (await invoice.date).format('DD/MM/YYYY');
    const formattedTitle = contactTitle
        ? contactTitle[0].toUpperCase() + contactTitle.substring(1, contactTitle.length).toLowerCase()
        : '';
    const data = {
        invoice: {
            number: invoiceNumber,
            date: formattedInvoiceDate,
            totalAmountIncludingTax: (await invoice.totalAmountIncludingTax).toString(),
            currencySymbol: await (await invoice.currency).symbol,
        },
        contact: {
            title: formattedTitle,
            firstName: contactFirstName,
            lastName: contactLastName,
        },
        site: {
            name: await invoice.salesSiteName,
        },
    } as xtremSales.interfaces.SalesInvoiceEmailTemplateData;
    const subject = context.localize(
        '@sage/xtrem-sales/sales_invoice__email_subject',
        '{{invoiceSiteName}}: Invoice {{invoiceNumber}}',
        {
            invoiceSiteName: data.site.name,
            invoiceNumber: data.invoice.number,
        },
    );

    // email recipients
    const recipients: xtremMailer.interfaces.Recipients = {
        to: [{ address: contactEmail, name: contactEmail }],
    };

    // send the email
    await xtremMasterData.functions.sendReportsByMail(
        context,
        'sales_invoice_send',
        data,
        subject,
        recipients,
        `Sales invoice - ${invoiceNumber}`,
        reports,
        invoice,
    );
}

export function linkedShipmentsArray(
    instance: xtremSales.nodes.SalesInvoice,
): Promise<xtremSales.nodes.SalesShipment[]> {
    return instance.$.context
        .query(xtremSales.nodes.SalesShipment, {
            filter: { _id: { _in: Object.keys(instance.__salesShipments) } },
            forUpdate: true,
        })
        .toArray();
}

export async function updateHeaderNotesOnCreation(instance: xtremSales.nodes.SalesInvoice) {
    if (instance.$.status === NodeStatus.added) {
        const salesShipmentArray = await linkedShipmentsArray(instance);
        if (salesShipmentArray.length === 1) {
            const isTransferHeaderNote = await salesShipmentArray[0].isTransferHeaderNote;
            if (isTransferHeaderNote) {
                const currentNote = await getCurrentHeaderNote(instance);
                await setHeaderInternalNote(instance, salesShipmentArray, currentNote);
                await setHeaderExternalNote(instance, salesShipmentArray, currentNote);
            }
            await setHeaderSetupNote(instance, isTransferHeaderNote, await salesShipmentArray[0].isTransferLineNote);
        }
    }
}

export async function updateLineNotesOnCreation(instance: xtremSales.nodes.SalesInvoiceLine) {
    if (instance.$.status === NodeStatus.added && (await instance.salesShipmentLines.length)) {
        const linkedDocument = await (await instance.salesShipmentLines.elementAt(0)).linkedDocument;
        if (await (await linkedDocument.document).isTransferLineNote) {
            const currentNote = await getCurrentLineNote(instance);
            await setLineInternalNote(instance, linkedDocument, currentNote);
            await setLineExternalNote(instance, linkedDocument, currentNote);
        }
    }
}

export async function addNewSalesInvoiceLineTaxDependency(salesOrderLine: xtremSales.nodes.SalesOrderLine): Promise<
    NodeCreateData<
        xtremSales.nodes.SalesInvoiceLineTax & {
            _sortValue?: number;
            _action: UpdateAction;
        }
    >[]
> {
    const taxes: NodeCreateData<
        xtremSales.nodes.SalesInvoiceLineTax & {
            _sortValue?: number;
            _action: UpdateAction;
        }
    >[] = [];

    if (salesOrderLine && salesOrderLine.taxes) {
        await salesOrderLine.taxes.forEach(async tax => {
            taxes.push({
                _action: 'create',
                taxCategoryReference: (await tax.taxCategoryReference)?._id,
                taxReference: (await tax.taxReference)?._id,
                isSubjectToGlTaxExcludedAmount: await tax.isSubjectToGlTaxExcludedAmount,
                isTaxMandatory: await tax.isTaxMandatory,
                _sortValue: await tax._sortValue,
                // taxRate is only to meet field requirements. It will be rewritten to the actual rate
                // for the current date during BaseTaxCalculator execution.
                taxRate: await tax.taxRate,
            });
        });
    }

    return taxes;
}
