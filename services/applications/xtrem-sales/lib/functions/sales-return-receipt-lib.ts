import type { Context, integer } from '@sage/xtrem-core';
import { BusinessRuleError, NodeStatus, asyncArray } from '@sage/xtrem-core';
import type * as xtremStockData from '@sage/xtrem-stock-data';
import * as xtremSales from '../index';
import { setHeaderInternalNote, setLineInternalNote } from './common';

/**
 * Calculates the display status of the order based on the other document status properties
 * @param status
 * @param xtremStockData
 * @returns display status enum
 */
export function calculateSalesReturnReceiptDisplayStatus(
    status: xtremSales.enums.SalesReturnReceiptStatus,
    stockTransactionStatus: xtremStockData.enums.StockDocumentTransactionStatus,
): xtremSales.enums.SalesReturnReceiptDisplayStatus {
    if (stockTransactionStatus === 'error') {
        return 'error';
    }
    if (stockTransactionStatus === 'inProgress') {
        return 'postingInProgress';
    }
    if (status === 'closed') {
        return 'closed';
    }
    return 'draft';
}

export function linkedReturnRequestsArray(
    instance: xtremSales.nodes.SalesReturnReceipt,
): Promise<xtremSales.nodes.SalesReturnRequest[]> {
    return instance.$.context
        .query(xtremSales.nodes.SalesReturnRequest, {
            filter: { _id: { _in: Object.keys(instance.__salesReturnRequests) } },
            forUpdate: true,
        })
        .toArray();
}

export async function updateHeaderNotesOnCreation(instance: xtremSales.nodes.SalesReturnReceipt) {
    if (instance.$.status === NodeStatus.added) {
        const salesReturnRequestsArray = await linkedReturnRequestsArray(instance);
        if (salesReturnRequestsArray.length === 1) {
            const isTransferHeaderNote = await salesReturnRequestsArray[0].isTransferHeaderNote;
            if (isTransferHeaderNote) {
                const currentNote: xtremSales.interfaces.CurrentNote = {
                    isOverwrite: await instance.isOverwriteNote,
                    internalNote: await instance.internalNote,
                };
                await setHeaderInternalNote(instance, salesReturnRequestsArray, currentNote);
            }
        }
    }
}

export async function updateLineNotesOnCreation(instance: xtremSales.nodes.SalesReturnReceiptLine) {
    if (instance.$.status === NodeStatus.added && (await instance.toReturnRequestLines.length)) {
        const linkedDocument = await (await instance.toReturnRequestLines.elementAt(0)).linkedDocument;
        if (await (await linkedDocument.document).isTransferLineNote) {
            const currentNote: xtremSales.interfaces.CurrentNote = {
                isOverwrite: await (await instance.document).isOverwriteNote,
                internalNote: await instance.internalNote,
            };
            await setLineInternalNote(instance, linkedDocument, currentNote);
        }
    }
}

export async function getStockDetailStatus(
    salesReturnReceipt: xtremSales.nodes.SalesReturnReceipt,
): Promise<xtremStockData.enums.StockDetailStatus> {
    if (await salesReturnReceipt.lines.some(async line => (await line.stockDetailStatus) === 'required')) {
        return 'required';
    }
    if (await salesReturnReceipt.lines.some(async line => (await line.stockDetailStatus) === 'entered')) {
        return 'entered';
    }
    return 'notRequired';
}

export async function validatePost(context: Context, documentIds: integer[]) {
    await asyncArray(documentIds).forEach(async documentId => {
        if (
            await context.queryCount(xtremSales.nodes.SalesReturnReceiptLine, {
                filter: {
                    document: documentId,
                    stockDetailStatus: 'required',
                },
            })
        ) {
            throw new BusinessRuleError(
                context.localize(
                    '@sage/xtrem-sales/nodes__sales-return-receipt__cant_post_sales_return_receipt_when_stock_detail_status_is_required',
                    'You need to enter stock details for all lines before you can post.',
                ),
            );
        }

        if (
            (
                await context.select(
                    xtremSales.nodes.SalesReturnReceipt,
                    { status: true },
                    { filter: { _id: documentId } },
                )
            ).at(0)?.status !== 'draft'
        ) {
            throw new BusinessRuleError(
                context.localize(
                    '@sage/xtrem-sales/nodes__sales_return_receipt_post__already_done',
                    'The sales return receipt was already posted.',
                ),
            );
        }
    });
}

export function hasExistingCreditedReturnRequestLine(
    returnReceiptLine: xtremSales.nodes.SalesReturnReceiptLine,
): Promise<boolean> {
    return returnReceiptLine.toReturnRequestLines.some(async returnRequestLine => {
        return (await (await returnRequestLine.linkedDocument).creditStatus) !== 'notCredited';
    });
}

export function hasExistingClosedReturnRequestLine(
    returnReceiptLine: xtremSales.nodes.SalesReturnReceiptLine,
): Promise<boolean> {
    return returnReceiptLine.toReturnRequestLines.some(async returnRequestLine => {
        return (await (await returnRequestLine.linkedDocument).status) === 'closed';
    });
}
