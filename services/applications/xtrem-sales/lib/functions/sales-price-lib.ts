import type { date } from '@sage/xtrem-core';
import { NodeStatus } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import type * as xtremSystem from '@sage/xtrem-system';
import type * as xtremSales from '../index';

export async function calculateSalesPriceDetermination(
    node: xtremSales.interfaces.SalesLineNode,
    customer: xtremMasterData.nodes.Customer,
    documentDate: date,
    stockSite: xtremSystem.nodes.Site,
): Promise<void> {
    const salesPriceDetermined = await xtremMasterData.functions.getSalesPrice(node.$.context, {
        currency: await node.currency,
        customer,
        date: documentDate,
        item: await node.item,
        quantity: await node.quantity,
        unit: await node.unit,
        site: await node.site,
        stockSite,
    });

    let priceOrigin: xtremSales.enums.SalesPriceOrigin | null = null;
    let priceReason: xtremMasterData.nodes.CustomerPriceReason | null = salesPriceDetermined?.priceReason ?? null;
    let isPriceDeterminated = true;

    if (salesPriceDetermined?.priceReason) {
        priceOrigin = 'priceList';
    } else if (salesPriceDetermined?.grossPrice > 0) {
        priceOrigin = 'basePrice';
    } else {
        isPriceDeterminated = false;
    }

    if (node.$.status === NodeStatus.added) {
        if ((await node.discountCharges.length) === 0) {
            await node.$.set({
                discountCharges: xtremMasterData.functions.getDiscountChargeDefaultValue(
                    salesPriceDetermined?.discount,
                    salesPriceDetermined?.charge,
                ),
            });
        } else {
            priceOrigin = 'manual';
            priceReason = null;
        }
    }

    if (
        (NodeStatus.modified, NodeStatus.unchanged).includes(node.$.status) &&
        (Number(salesPriceDetermined?.discount) > 0 || Number(salesPriceDetermined?.charge) > 0) &&
        salesPriceDetermined?.grossPrice > 0
    ) {
        if (Number(salesPriceDetermined?.discount) > 0) {
            await xtremMasterData.functions.setDiscountChargeValue(
                node.discountCharges,
                'decrease',
                Number(salesPriceDetermined?.discount),
                salesPriceDetermined?.grossPrice,
            );
        }
        if (Number(salesPriceDetermined?.charge) > 0) {
            await xtremMasterData.functions.setDiscountChargeValue(
                node.discountCharges,
                'increase',
                Number(salesPriceDetermined?.charge),
                salesPriceDetermined?.grossPrice,
            );
        }
    }

    await node.$.set({
        grossPrice: salesPriceDetermined?.grossPrice,
        priceOrigin,
        isPriceDeterminated,
        priceReason,
    });
}

export async function prepareSalesPrices(
    node: xtremSales.interfaces.SalesLineNode,
    customer: xtremMasterData.nodes.Customer,
    documentDate: date,
    stockSite: xtremSystem.nodes.Site,
): Promise<void> {
    if (node.$.status === NodeStatus.added && !(await node.grossPrice)) {
        await calculateSalesPriceDetermination(node, customer, documentDate, stockSite);
    } else if (node.$.status === NodeStatus.added && (await node.grossPrice) && !(await node.isPriceDeterminated)) {
        await node.$.set({ priceOrigin: 'manual' });
    }

    if (node.$.status === NodeStatus.added && (await node.discountCharges.length) === 0) {
        await node.$.set({
            discountCharges: xtremMasterData.functions.getDiscountChargeDefaultValue(),
        });
    }
}
