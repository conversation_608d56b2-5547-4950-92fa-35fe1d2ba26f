import type { Context } from '@sage/xtrem-core';
import * as xtremFinanceData from '@sage/xtrem-finance-data';
import type * as xtremMasterData from '@sage/xtrem-master-data';
import type * as xtremSystem from '@sage/xtrem-system';

export async function computeAttributes(
    context: Context,
    site: xtremSystem.nodes.Site,
    secondSite: xtremSystem.nodes.Site,
    item: xtremMasterData.nodes.Item,
    billToCustomer: xtremMasterData.nodes.Customer,
): Promise<xtremFinanceData.interfaces.ComputedAttributes> {
    const result = await xtremFinanceData.functions.computeGenericAttributes(context, {
        item,
        stockSite: secondSite,
        financialSite: site,
    });

    result.businessSite = await xtremFinanceData.functions.checkAttributeTypeActive(
        context,
        xtremFinanceData.nodes.AttributeType,
        'businessSite',
        await site.id,
    );
    result.customer = await xtremFinanceData.functions.checkAttributeTypeActive(
        context,
        xtremFinanceData.nodes.AttributeType,
        'customer',
        await (
            await billToCustomer.businessEntity
        ).id,
    );

    return result;
}
