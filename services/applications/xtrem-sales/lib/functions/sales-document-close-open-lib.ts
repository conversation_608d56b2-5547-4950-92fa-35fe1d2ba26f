import type { Context, decimal, EnumDataType, StaticThis } from '@sage/xtrem-core';
import { LogicError } from '@sage/xtrem-core';
import * as xtremSales from '../index';

/**
 * Function that closes any sales document line
 * @param context
 * @param salesDocumentLine sales document line to close
 * @param salesDocumentNode sales static document node used to create instance
 * @param enumDataType closing line enum data type fitted to currently processed sales document line
 * @returns enum status
 */
export async function setSalesDocumentLineCloseStatus<T extends xtremSales.interfaces.CloseOpenDocumentLineNodes>(
    context: Context,
    salesDocumentLine: T,
    salesDocumentNode: StaticThis<xtremSales.interfaces.CloseOpenDocumentNodes>,
    enumDataType: EnumDataType,
): Promise<string> {
    if ((await salesDocumentLine.status) === 'closed') {
        return enumDataType.stringValue(2);
    }

    const salesDocument = await context.read(
        salesDocumentNode,
        {
            _id: (await salesDocumentLine.document)._id,
        },
        { forUpdate: true },
    );
    let numberOfClosedLine: number = 0;

    salesDocument.skipIsOnHold = true;

    await salesDocument.lines.forEach(async (element: xtremSales.interfaces.CloseOpenDocumentLineNodes) => {
        if (element._id === salesDocumentLine._id) {
            await element.$.set({ status: 'closed' });
        }
        if ((await element.status) === 'closed') {
            numberOfClosedLine += 1;
        }
    });

    if ((await salesDocument.status) === 'closed') {
        await salesDocument.$.save();
        return enumDataType.stringValue(2);
    }
    if (numberOfClosedLine === (await salesDocument.lines.length)) {
        await salesDocument.$.set({ status: 'closed' });
    }
    await salesDocument.$.save();

    return enumDataType.stringValue(3);
}

/**
 * Function that opens any sales document line
 * @param context
 * @param salesDocumentLine sales document line to open
 * @param salesDocumentNode sales static document node used to create instance
 * @param enumDataType opening line enum data type fitted to currently processed sales document line
 * @param aggregateNode node used to check how much base documents are assigned to target documents
 * @returns enum status
 */
export async function setSalesDocumentLineOpenStatus<T extends xtremSales.interfaces.CloseOpenDocumentLineNodes>(
    context: Context,
    salesDocumentLine: T,
    salesDocumentNode: StaticThis<xtremSales.interfaces.CloseOpenDocumentNodes>,
    enumDataType: EnumDataType,
    aggregateNode: StaticThis<xtremSales.interfaces.AnyBaseLineToSalesDocumentLine>,
    newQuantityInSalesUnit?: decimal,
): Promise<string> {
    if ((await salesDocumentLine.status) !== 'closed') {
        return enumDataType.stringValue(2);
    }

    const salesDocument = await context.read(
        salesDocumentNode,
        {
            _id: (await salesDocumentLine.document)._id,
        },
        { forUpdate: true },
    );

    salesDocument.skipIsOnHold = true;

    let quantity: decimal = 0;
    await context
        .query(aggregateNode, {
            filter: { linkedDocument: salesDocumentLine._id },
        })
        .forEach(async element => {
            quantity += await element.quantity;
        });
    const line = await salesDocument.lines.find(
        (e: xtremSales.interfaces.CloseOpenDocumentLineNodes) => e._id === salesDocumentLine._id,
    );
    if (!line) throw new LogicError('line not found');

    if (newQuantityInSalesUnit) {
        if (newQuantityInSalesUnit > (await line.quantity) && newQuantityInSalesUnit > quantity) {
            await line.$.set({ quantity: newQuantityInSalesUnit });
        }
    }

    if (salesDocument instanceof xtremSales.nodes.SalesReturnRequest) {
        if (['draft', 'pendingApproval', 'rejected'].includes(await salesDocument.approvalStatus)) {
            if (line instanceof xtremSales.nodes.SalesReturnRequestLine) {
                await line.$.set({ status: 'draft' });
            }
            await salesDocument.$.set({ status: 'draft' });
            await salesDocument.$.save();
            return enumDataType.stringValue(4);
        }
    }

    if (salesDocument instanceof xtremSales.nodes.SalesReturnRequest) {
        if ((await salesDocument.status) === 'draft') {
            if (line instanceof xtremSales.nodes.SalesReturnRequestLine) {
                await line.$.set({ status: 'draft' });
            }
            await salesDocument.$.save();
            return enumDataType.stringValue(4);
        }
    }

    if (salesDocument instanceof xtremSales.nodes.SalesOrder) {
        if ((await salesDocument.status) === 'quote') {
            if (line instanceof xtremSales.nodes.SalesOrderLine) {
                await line.$.set({ status: 'quote' });
            }
            await salesDocument.$.save();
            return enumDataType.stringValue(4);
        }
    }

    // case: line notShipped
    if (quantity === 0) {
        await line.$.set({ status: 'pending' });
        if ((await salesDocument.status) === 'closed') {
            if (
                await salesDocument.lines.find(
                    async (e: xtremSales.interfaces.CloseOpenDocumentLineNodes) => (await e.status) === 'inProgress',
                )
            ) {
                await salesDocument.$.set({ status: 'inProgress' });
            } else {
                await salesDocument.$.set({ status: 'pending' });
            }
        }
        await salesDocument.$.save();
        return enumDataType.stringValue(4);
    }

    // case: line partiallyShipped
    if (quantity < (await line.quantity)) {
        await line.$.set({ status: 'inProgress' });
        if ((await salesDocument.status) === 'closed') {
            await salesDocument.$.set({ status: 'inProgress' });
        }
        await salesDocument.$.save();
        return enumDataType.stringValue(4);
    }

    // case: line shipped
    return enumDataType.stringValue(3);
}

/**
 * Function that closes a sales document
 * @param context
 * @param salesDocumentNumber the number of the sales document
 * @param salesDocumentNode sales static document node used to create instance
 * @param enumDataType closing document enum data type fitted to currently processed sales document
 * @returns enum status
 */
export async function setSalesDocumentCloseStatus(
    context: Context,
    salesDocumentNumber: string,
    salesDocumentNode: StaticThis<xtremSales.interfaces.CloseOpenDocumentNodes>,
    enumDataType: EnumDataType,
): Promise<string> {
    const salesDocument = await context.read(
        salesDocumentNode,
        {
            number: salesDocumentNumber,
        },
        { forUpdate: true },
    );

    if ((await salesDocument.status) === 'closed') {
        return enumDataType.stringValue(2);
    }

    await salesDocument.lines.forEach(async (line: xtremSales.interfaces.CloseOpenDocumentLineNodes) => {
        if ((await line.status) !== 'closed') {
            await line.$.set({ status: 'closed' });
        }
    });

    await salesDocument.$.set({ status: 'closed' });
    await salesDocument.$.save();
    return enumDataType.stringValue(3);
}

/**
 * Function that opens a sales document
 * @param context
 * @param salesDocumentNumber the number of the sales document
 * @param salesDocumentNode sales static document node used to create instance
 * @param enumDataType opening document enum data type fitted to currently processed sales document
 * @param aggregateNode node used to check how much base documents are assigned to target documents
 * @returns enum status
 */
export async function setSalesDocumentOpenStatus(
    context: Context,
    salesDocumentNumber: string,
    salesDocumentNode: StaticThis<xtremSales.interfaces.CloseOpenDocumentNodes>,
    enumDataType: EnumDataType,
    aggregateNode: StaticThis<xtremSales.interfaces.AnyBaseLineToSalesDocumentLine>,
): Promise<string> {
    const salesDocument = await context.read(
        salesDocumentNode,
        {
            number: salesDocumentNumber,
        },
        { forUpdate: true },
    );

    let isQuote: boolean;
    if (salesDocument instanceof xtremSales.nodes.SalesOrder) {
        isQuote = await salesDocument.isQuote;
    }

    salesDocument.skipIsOnHold = true;

    let numberOfSalesDocumentLineNotFinalized: number = 0;
    let numberOfSalesDocumentLineFinalized: number = 0;
    if ((await salesDocument.status) !== 'closed') {
        return enumDataType.stringValue(3);
    }
    let quantity: decimal = 0;

    await salesDocument.lines.forEach(async (line: xtremSales.interfaces.CloseOpenDocumentLineNodes) => {
        if (
            salesDocument instanceof xtremSales.nodes.SalesReturnRequest &&
            ['draft', 'pendingApproval', 'rejected'].includes(await salesDocument.approvalStatus) &&
            line instanceof xtremSales.nodes.SalesReturnRequestLine
        ) {
            await line.$.set({ status: 'draft' });
        } else {
            quantity = 0;
            await context
                .query(aggregateNode, {
                    filter: { linkedDocument: line._id },
                })
                .forEach(async element => {
                    quantity += await element.quantity;
                });

            // case: line notShipped
            if (quantity === 0) {
                if (line instanceof xtremSales.nodes.SalesReturnRequestLine || !isQuote) {
                    await line.$.set({ status: 'pending' });
                    numberOfSalesDocumentLineNotFinalized += 1;
                } else {
                    await line.$.set({ status: 'quote' });
                    numberOfSalesDocumentLineNotFinalized += 1;
                }
            }

            // case: line partiallyShipped
            if (quantity > 0 && quantity < (await line.quantity)) {
                await line.$.set({ status: 'inProgress' });
            }
            // case: line shipped
            if (quantity > 0 && quantity === (await line.quantity)) {
                await line.$.set({ status: 'closed' });
                numberOfSalesDocumentLineFinalized += 1;
            }
        }
    });

    if (numberOfSalesDocumentLineFinalized === (await salesDocument.lines.length)) {
        await salesDocument.$.set({ status: 'closed' });
        await salesDocument.$.save();
        return enumDataType.stringValue(2);
    }

    if (salesDocument instanceof xtremSales.nodes.SalesReturnRequest) {
        if (['draft', 'pendingApproval', 'rejected'].includes(await salesDocument.approvalStatus)) {
            await salesDocument.$.set({ status: 'draft' });
            await salesDocument.$.save();
            return enumDataType.stringValue(4);
        }
    }

    if (numberOfSalesDocumentLineNotFinalized === (await salesDocument.lines.length)) {
        if (salesDocument instanceof xtremSales.nodes.SalesOrder && (await salesDocument.isQuote)) {
            await salesDocument.$.set({ status: 'quote', isQuote: true });
        } else {
            await salesDocument.$.set({ status: 'pending' });
        }
        await salesDocument.$.save();
        return enumDataType.stringValue(4);
    }

    await salesDocument.$.set({ status: 'inProgress' });
    await salesDocument.$.save();
    return enumDataType.stringValue(4);
}
