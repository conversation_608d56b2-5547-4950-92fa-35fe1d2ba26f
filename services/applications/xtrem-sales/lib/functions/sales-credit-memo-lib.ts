import type { Context, UpdateAction } from '@sage/xtrem-core';
import { BusinessRuleError, Logger, NodeStatus } from '@sage/xtrem-core';
import * as xtremFinanceData from '@sage/xtrem-finance-data';
import type * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremSales from '../index';
import {
    getCurrentHeaderNote,
    getCurrentLineNote,
    setHeaderExternalNote,
    setHeaderInternalNote,
    setLineExternalNote,
    setLineInternalNote,
} from './common';

const logger = Logger.getLogger(__filename, 'sales-credit-memo-lib');

/**
 * Calculates the display status of the order based on the other document status properties
 * @param status
 * @param taxCalculationStatus
 * @returns display status enum
 */
export function calculateSalesCreditMemoDisplayStatus(
    status: xtremSales.enums.SalesCreditMemoStatus,
    taxCalculationStatus: xtremMasterData.enums.TaxCalculationStatus,
    paymentStatus: xtremFinanceData.enums.OpenItemStatus | null,
): xtremSales.enums.SalesCreditMemoDisplayStatus {
    if (taxCalculationStatus === 'failed') {
        return 'taxCalculationFailed';
    }
    if (paymentStatus && paymentStatus === 'paid') {
        return 'paid';
    }
    if (paymentStatus && paymentStatus === 'partiallyPaid') {
        return 'partiallyPaid';
    }
    if (status === 'posted') {
        return 'posted';
    }
    if (status === 'error') {
        return 'error';
    }
    if (status === 'inProgress') {
        return 'postingInProgress';
    }
    return 'draft';
}

export function linkedInvoiceArray(
    instance: xtremSales.nodes.SalesCreditMemo,
): Promise<xtremSales.nodes.SalesInvoice[]> {
    return instance.$.context
        .query(xtremSales.nodes.SalesInvoice, {
            filter: { _id: { _in: Object.keys(instance.__salesInvoices) } },
            forUpdate: true,
        })
        .toArray();
}

export async function updateCreditMemoHeaderNotesOnCreation(instance: xtremSales.nodes.SalesCreditMemo) {
    if (instance.$.status === NodeStatus.added) {
        const salesInvoiceArray = await linkedInvoiceArray(instance);
        if (salesInvoiceArray.length === 1) {
            const isTransferHeaderNote = await salesInvoiceArray[0].isTransferHeaderNote;

            if (isTransferHeaderNote) {
                const currentNote = await getCurrentHeaderNote(instance);
                await setHeaderInternalNote(instance, salesInvoiceArray, currentNote);
                await setHeaderExternalNote(instance, salesInvoiceArray, currentNote);
            }
        }
    }
}

export async function updateCreditMemoLineNotesOnCreation(instance: xtremSales.nodes.SalesCreditMemoLine) {
    if (instance.$.status === NodeStatus.added && (await instance.toInvoiceLines.length)) {
        const linkedDocument = await (await instance.toInvoiceLines.elementAt(0)).linkedDocument;
        if (await (await linkedDocument.document).isTransferLineNote) {
            const currentNote = await getCurrentLineNote(instance);
            await setLineInternalNote(instance, linkedDocument, currentNote);
            await setLineExternalNote(instance, linkedDocument, currentNote);
        }
    }
}

export async function repost(
    context: Context,
    salesCreditMemo: xtremSales.nodes.SalesCreditMemo,
    documentData: xtremSales.interfaces.FinanceIntegration.SalesInvoiceCreditMemoRepost,
): Promise<xtremFinanceData.interfaces.MutationResult> {
    if (!xtremFinanceData.functions.canRepost((await salesCreditMemo.financeIntegrationStatus) || 'submitted')) {
        // submitted used in case of undefined to assure that function will return false
        throw new BusinessRuleError(
            context.localize(
                '@sage/xtrem-sales/nodes__sales_credit_memo__cant_repost_sales_credit_memo_when_status_is_not_failed',
                "You can only repost a sales credit memo if the status is 'Failed' or 'Not recorded'.",
            ),
        );
    }

    logger.info(`Reposting sales credit memo ${await salesCreditMemo.number}`);

    const financeTransactions = (
        await xtremFinanceData.nodes.FinanceTransaction.getPostingStatusData(context, await salesCreditMemo.number)
    ).filter(
        financeTransaction =>
            ['postingError', 'generationError'].includes(financeTransaction.status) &&
            !financeTransaction.hasSourceForDimensionLines,
    );

    const targetDocumentTypes = financeTransactions.map(financeTransaction => {
        return financeTransaction.documentType;
    });

    logger.info(`Target document types are ${JSON.stringify(targetDocumentTypes)}`);

    const hasTaxes = documentData.lines.every(line => {
        return !!line.uiTaxes;
    });
    const isAvalaraEnabled = (await salesCreditMemo.taxEngine) === 'avalaraAvaTax';

    const financeTransaction = targetDocumentTypes.includes('accountsReceivableInvoice')
        ? await context.read(xtremFinanceData.nodes.FinanceTransaction, {
              _id:
                  financeTransactions.filter(line => line.documentType === 'accountsReceivableInvoice').at(0)?._id ?? 0,
          })
        : null;

    // If the user changed the tax data, avalara is not enabled and the invoice is already posted, we need to recreate
    // the tax information on the ap-ap invoice and before that we need to remove the accounting staging data
    // related with that ap-ar invoice
    if (hasTaxes && !isAvalaraEnabled && !!financeTransaction) {
        logger.info(`Document has taxes and Avalara is not enabled`);

        await xtremFinanceData.functions.AccountingEngineCommon.removeAccountingStagingForApArInvoice(
            context,
            financeTransaction,
        );
    }

    // Prepare the lines to be updated
    const updateLines = documentData.lines.map(documentLine => {
        return {
            _action: 'update' as UpdateAction,
            _id: documentLine.baseDocumentLineSysId,
            storedAttributes: documentLine.storedAttributes,
            storedDimensions: documentLine.storedDimensions,
            uiTaxes: hasTaxes ? documentLine.uiTaxes : undefined,
        };
    });

    // Update and save the sales credit memo
    await salesCreditMemo.$.set({ forceUpdateForFinance: true, lines: updateLines, wasTaxDataChanged: hasTaxes });
    if (documentData.header.paymentTerm && ((await salesCreditMemo.paymentStatus) ?? 'notPaid') === 'notPaid') {
        await salesCreditMemo.$.set({ paymentTerm: documentData.header.paymentTerm });
    }
    await salesCreditMemo.$.save();

    // If the user changed the tax data, avalara is not enabled and the credit memo is already posted, we need to recreate
    // the tax information on the ap-ap invoice on the staging table
    if (hasTaxes && !isAvalaraEnabled && !!financeTransaction) {
        await xtremFinanceData.functions.AccountingEngineCommon.updateAccountingStagingForApArInvoice(context, {
            financeTransaction,
            document: salesCreditMemo,
            lines: await salesCreditMemo.lines.toArray(),
            replyTopic: 'SalesCreditMemo/accountingInterface',
        });
    }

    // send notification in order to update a staging table entry for the accounting engine
    await xtremFinanceData.functions.purchaseSalesInvoiceCreditMemoUpdateNotification(context, {
        targetDocumentTypes,
        document: salesCreditMemo,
        lines: await salesCreditMemo.lines.toArray(),
        documentType: 'salesCreditMemo',
        replyTopic: 'SalesCreditMemo/accountingInterface',
    });
    return {
        wasSuccessful: true,
        message: context.localize(
            '@sage/xtrem-sales/nodes__sales_credit_memo__document_was_posted',
            'The sales credit memo was posted.',
        ),
    };
}
