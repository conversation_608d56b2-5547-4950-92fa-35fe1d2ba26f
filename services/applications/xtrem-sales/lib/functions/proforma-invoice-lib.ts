import type * as xtremSales from '../index';
import { ProformaInvoice } from '../nodes';
/**
 * Returns a the last proforma invoice for an order
 * @param proformaInvoice - proforma invoice instance
 * @param forUpdate - to update last proforma invoice
 * @returns ProformaInvoice or undefined
 */
export async function getLastProforma(parameters: {
    proformaInvoice: xtremSales.nodes.ProformaInvoice;
    forUpdate?: boolean;
}): Promise<xtremSales.nodes.ProformaInvoice | undefined> {
    return parameters.proformaInvoice.$.context
        .query(ProformaInvoice, {
            filter: {
                salesOrder: (await parameters.proformaInvoice.salesOrder)._id,
                version: { _ne: await parameters.proformaInvoice.version },
            },
            orderBy: { version: 1 },
            last: 1,
            forUpdate: parameters.forUpdate,
        })
        .at(0);
}
