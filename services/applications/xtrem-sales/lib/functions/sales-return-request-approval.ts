import type { Context } from '@sage/xtrem-core';
import { BusinessRuleError } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import type * as xtremSystem from '@sage/xtrem-system';
import type * as xtremSales from '../index';

export async function getApprovalUser(
    context: Context,
    document: xtremSales.nodes.SalesReturnRequest,
): Promise<xtremSystem.nodes.User> {
    const site = await document.site;
    const defaultApprover = await site.salesReturnRequestDefaultApprover;
    const substituteApprover = await site.salesReturnRequestSubstituteApprover;

    if (defaultApprover) return defaultApprover;
    if (substituteApprover) return substituteApprover;

    throw new BusinessRuleError(
        context.localize(
            '@sage/xtrem-sales/functions__send__email__no_default_or_substitute_approver',
            'There is no default or substitute approver for the current document.',
        ),
    );
}

export async function checkApprovalStatus(
    context: Context,
    document: xtremSales.nodes.SalesReturnRequest,
): Promise<void> {
    if (!['draft', 'changeRequested'].includes(await document.approvalStatus) && (await document.status) !== 'closed') {
        throw new BusinessRuleError(
            context.localize(
                '@sage/xtrem-sales/functions__send__email__not_in_submission_for_approval_not_allowed',
                'Submission for approval action not allowed. The sales return request is not in draft status.',
            ),
        );
    }
}

export async function getReturnRequestApprovalData(
    context: Context,
    document: xtremSales.nodes.SalesReturnRequest,
    user: xtremSystem.nodes.User,
): Promise<xtremMasterData.interfaces.ApprovalRequestMail> {
    await checkApprovalStatus(context, document);
    const approvalUser = !(await user.email) ? await getApprovalUser(context, document) : user;
    const number = await document.number;
    const requester = await document.requester;
    const url = await document.page;
    const urlApprovalDocument = await document.documentUrl;
    const { urlReject, urlApprove } = xtremMasterData.functions.generateUrl(url, document._id);

    const subject = context.localize(
        '@sage/xtrem-sales/functions__sales_return_request__approval_email_subject',
        '[Sales return request {{salesReturnRequestNumber}}] approval request',
        { salesReturnRequestNumber: number },
    );

    return {
        data: {
            number,
            requester: `${await requester.lastName} ${await requester.firstName}`,
            site: await (await document.site).name,
            date: (await document.date).toString(),
            urlApprovalDocument,
            urlReject,
            urlApprove,
        },
        mailerUser: approvalUser,
        subject,
        template: 'sales_return_request_approval_mail',
    };
}
