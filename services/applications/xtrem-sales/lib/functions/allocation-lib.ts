import type * as xtremSales from '..';

export function salesConvertFilterToMassProcessCriteria(filter: {
    document: {
        stockSite: {
            id: string;
        };
        incoterm?: {
            id: string;
        };
        deliveryMode?: {
            id: string;
        };
        shippingDate?: {
            _lte: string;
        };
        number?: {
            _gte?: string;
            _lte?: string;
        };
        soldToCustomer?: {
            businessEntity: {
                name: {
                    _gte?: string;
                    _lte?: string;
                };
            };
        };
    };
}) {
    return {
        stockSite: filter.document.stockSite.id,
        ...(filter.document?.number?._gte ? { fromOrderNumber: filter.document.number._gte } : {}),
        ...(filter.document?.number?._lte ? { toOrderNumber: filter.document.number._lte } : {}),

        ...(filter.document?.soldToCustomer?.businessEntity?.name._gte
            ? { fromSoldToCustomer: filter.document.soldToCustomer.businessEntity.name._gte }
            : {}),
        ...(filter.document?.soldToCustomer?.businessEntity?.name._lte
            ? { toSoldToCustomer: filter.document.soldToCustomer.businessEntity.name._lte }
            : {}),
        ...(filter.document?.incoterm?.id ? { incoterm: filter.document.incoterm.id } : {}),
        ...(filter.document?.deliveryMode?.id ? { deliveryMode: filter.document.deliveryMode.id } : {}),

        ...(filter.document?.shippingDate?._lte ? { maximumShippingDate: filter.document.shippingDate._lte } : {}),
    } as xtremSales.sharedFunctions.SalesAllocationMassProcessCriteria;
}
