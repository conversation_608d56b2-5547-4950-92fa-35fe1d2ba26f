import type { date } from '@sage/xtrem-core';
import { NodeStatus } from '@sage/xtrem-core';
import type * as xtremMasterData from '@sage/xtrem-master-data';
import type * as xtremSystem from '@sage/xtrem-system';
import * as xtremSales from '../index';

export async function salesDocumentTaxUpdate<
    T extends xtremSales.nodes.SalesOrder | xtremSales.nodes.SalesInvoice | xtremSales.nodes.SalesCreditMemo,
    G extends xtremSales.classes.SalesTaxCalculator,
>(
    header: T,
    lineData: { customer: xtremMasterData.nodes.Customer; documentDate: date; stockSite: xtremSystem.nodes.Site },
    taxCalculatorInstance: G,
) {
    if (!header.__skipPrepare) {
        if (
            (await header.lines.some(line => line.$.status !== NodeStatus.unchanged)) ||
            (header.$.status === NodeStatus.modified &&
                (await header.lines.length) !== (await (await header.$.old).lines.length))
        ) {
            header.__salesTaxCalculator = taxCalculatorInstance;
            await header.__salesTaxCalculator.prepareHeaderTaxData(header);

            await header.lines
                .filter(
                    async line =>
                        !(line instanceof xtremSales.nodes.SalesOrderLine) || (await line.status) !== 'closed',
                )
                .forEach(async line => {
                    await xtremSales.functions.prepareSalesPrices(
                        line,
                        lineData.customer,
                        lineData.documentDate,
                        lineData.stockSite,
                    );
                    await header.__salesTaxCalculator.updateLineTaxData(line);
                });

            await header.__salesTaxCalculator.updateHeaderTaxData(header);
        }
    }
}
