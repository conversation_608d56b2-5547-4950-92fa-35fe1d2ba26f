import type { Context, OperationGrant } from '@sage/xtrem-core';
import * as xtremFinanceData from '@sage/xtrem-finance-data';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremStructure from '@sage/xtrem-structure';
import * as xtremSystem from '@sage/xtrem-system';
import * as xtremTax from '@sage/xtrem-tax';
import * as xtremUpload from '@sage/xtrem-upload';
import * as xtremSales from '../index';

export async function isChronological(
    legislation: xtremStructure.nodes.Legislation,
    documentNumberGenerator: xtremMasterData.classes.DocumentNumberGenerator,
): Promise<boolean> {
    return !!(
        (await legislation.id) !== 'FR' && (await (await documentNumberGenerator.sequenceNumber).isChronological)
    );
}

export const commonSalesActivities: OperationGrant[] = [
    { operations: ['lookup'], on: [() => xtremSystem.nodes.Company] },
    { operations: ['lookup'], on: [() => xtremSystem.nodes.Site] },
    { operations: ['lookup'], on: [() => xtremMasterData.nodes.BusinessEntity] },
    { operations: ['lookup'], on: [() => xtremMasterData.nodes.Supplier] },
    { operations: ['lookup'], on: [() => xtremStructure.nodes.Legislation] },
    { operations: ['lookup'], on: [() => xtremStructure.nodes.Country] },
    { operations: ['lookup'], on: [() => xtremMasterData.nodes.Currency] },
    { operations: ['lookup', 'getPurchaseUnit', 'convertFromTo'], on: [() => xtremMasterData.nodes.UnitOfMeasure] },
    { operations: ['lookup'], on: [() => xtremMasterData.nodes.Item] },
    { operations: ['lookup'], on: [() => xtremMasterData.nodes.Customer] },
    { operations: ['lookup'], on: [() => xtremMasterData.nodes.Incoterm] },
    { operations: ['lookup'], on: [() => xtremMasterData.nodes.DeliveryMode] },
    { operations: ['lookup'], on: [() => xtremMasterData.nodes.PaymentTerm] },
    { operations: ['lookup'], on: [() => xtremMasterData.nodes.ItemCustomer] },
    { operations: ['lookup'], on: [() => xtremMasterData.nodes.ItemSite] },
    { operations: ['lookup'], on: [() => xtremMasterData.nodes.Location] },
    { operations: ['lookup'], on: [() => xtremMasterData.nodes.LocationType] },
    { operations: ['lookup', 'getSalesPrice'], on: [() => xtremMasterData.nodes.ItemCustomerPrice] },

    { operations: ['getTaxValues'], on: [() => xtremTax.nodes.Tax] },
    { operations: ['lookup'], on: [() => xtremUpload.nodes.AttachmentAssociation] },
    ...xtremFinanceData.functions.dimensionsAndAttributesOperations,
];

export async function getCurrentHeaderNote(
    instance: xtremSales.interfaces.SalesDocumentNotePropagation,
): Promise<xtremSales.interfaces.CurrentNote> {
    return {
        isOverwrite: await instance.isOverwriteNote,
        internalNote: await instance.internalNote,
        isExternalNote: await instance.isExternalNote,
        externalNote: await instance.externalNote,
    };
}

export async function setHeaderInternalNote(
    instance:
        | xtremSales.interfaces.SalesDocumentNotePropagation
        | xtremSales.nodes.SalesReturnRequest
        | xtremSales.nodes.SalesReturnReceipt,
    linkedDocumentArray: xtremSales.interfaces.LinkedSalesDocument[] | xtremSales.nodes.SalesReturnRequest[],
    currentNote: xtremSales.interfaces.CurrentNote,
) {
    await instance.$.set({
        internalNote:
            currentNote.internalNote.toString() && !currentNote.isOverwrite
                ? currentNote.internalNote
                : await linkedDocumentArray[0].internalNote,
    });
}

export async function setHeaderExternalNote(
    instance: xtremSales.interfaces.SalesDocumentNotePropagation,
    linkedDocumentArray: xtremSales.interfaces.LinkedSalesDocument[],
    currentNote: xtremSales.interfaces.CurrentNote,
) {
    await instance.$.set({
        isExternalNote:
            currentNote.isExternalNote && !currentNote.isOverwrite
                ? currentNote.isExternalNote
                : await linkedDocumentArray[0].isExternalNote,
        externalNote:
            currentNote.externalNote && currentNote.externalNote.toString() && !currentNote.isOverwrite
                ? currentNote.externalNote
                : await linkedDocumentArray[0].externalNote,
    });
}

export async function setHeaderSetupNote(
    instance: xtremSales.interfaces.SalesDocumentNotePropagation | xtremSales.nodes.SalesReturnRequest,
    isTransferHeaderNote: boolean,
    isTransferLineNote: boolean,
) {
    await instance.$.set({ isTransferHeaderNote, isTransferLineNote });
}

export async function getCurrentLineNote(
    instance: xtremSales.interfaces.SalesDocumentLineNotePropagation,
): Promise<xtremSales.interfaces.CurrentNote> {
    return {
        isOverwrite: await (await instance.document).isOverwriteNote,
        internalNote: await instance.internalNote,
        isExternalNote: await instance.isExternalNote,
        externalNote: await instance.externalNote,
    };
}

export async function setLineInternalNote(
    instance:
        | xtremSales.interfaces.SalesDocumentLineNotePropagation
        | xtremSales.nodes.SalesReturnRequestLine
        | xtremSales.nodes.SalesReturnReceiptLine,
    linkedDocument: xtremSales.interfaces.LinkedSalesDocumentLine | xtremSales.nodes.SalesReturnRequestLine,
    currentNote: xtremSales.interfaces.CurrentNote,
) {
    await instance.$.set({
        internalNote:
            currentNote.internalNote.toString() && !currentNote.isOverwrite
                ? currentNote.internalNote
                : await linkedDocument.internalNote,
    });
}

export async function setLineExternalNote(
    instance: xtremSales.interfaces.SalesDocumentLineNotePropagation,
    linkedDocument: xtremSales.interfaces.LinkedSalesDocumentLine,
    currentNote: xtremSales.interfaces.CurrentNote,
) {
    await instance.$.set({
        isExternalNote:
            currentNote.isExternalNote && !currentNote.isOverwrite
                ? currentNote.isExternalNote
                : await linkedDocument.isExternalNote,
        externalNote:
            currentNote.externalNote && currentNote.externalNote.toString() && !currentNote.isOverwrite
                ? currentNote.externalNote
                : await linkedDocument.externalNote,
    });
}

/**
 * Checks if there are sales documents connected to a given item-site preventing item-site and item-site-cost from
 * deleting it.
 * @param context current context
 * @param item Item to be selected
 * @param site Site to be selected
 * @returns Boolean
 */
export async function doSalesDocumentsExist(
    context: Context,
    item: xtremMasterData.nodes.Item,
    site: xtremSystem.nodes.Site,
) {
    // Check existence of sales orders for this item-site.
    let count = await context.query(xtremSales.nodes.SalesOrderLine, {
        filter: { _and: [{ item }, { stockSite: site }] },
        first: 1,
    }).length;
    if (count) return true; // Found at least 1.

    // As we can create invoices without order we have to check existence for sales invoices, too..
    count = await context.query(xtremSales.nodes.SalesInvoiceLine, {
        filter: { _and: [{ item }, { providerSite: site }] },
        first: 1,
    }).length;
    if (count) return true; // Found at least 1.

    return false; // None found.
}
