import type { AsyncArray, Context, integer } from '@sage/xtrem-core';
import { NodeStatus, ValidationSeverity, asyncArray } from '@sage/xtrem-core';
import * as xtremFinanceData from '@sage/xtrem-finance-data';
import type * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremSystem from '@sage/xtrem-system';
import * as xtremSales from '../index';
import { getInvoiceInstance } from './sales-shipment-lib';

export async function getSalesInvoiceOrCreditMemo(
    context: Context,
    documentType: xtremFinanceData.enums.FinanceDocumentType,
    documentSysId: integer,
): Promise<xtremSales.nodes.SalesInvoice | xtremSales.nodes.SalesCreditMemo | null> {
    let document: xtremSales.nodes.SalesInvoice | xtremSales.nodes.SalesCreditMemo | null = null;
    if (documentType === 'salesInvoice') {
        document = await context.read(xtremSales.nodes.SalesInvoice, { _id: documentSysId });
    }
    if (documentType === 'salesCreditMemo') {
        document = await context.read(xtremSales.nodes.SalesCreditMemo, {
            _id: documentSysId,
        });
    }
    return document;
}

async function getSalesInvoiceNotificationPayload(
    context: Context,
    salesInvoice: xtremSales.nodes.SalesInvoice,
    isJustChecking = false,
): Promise<xtremFinanceData.interfaces.FinanceIntegrationDocument[]> {
    const invoice = salesInvoice;
    const { lines } = salesInvoice;
    const documentType: xtremFinanceData.enums.FinanceDocumentType = 'salesInvoice';

    const { accountingStagingCommonPayload, notificationsPayload } =
        await xtremFinanceData.functions.getPurchaseSalesInvoiceCreditMemoNotificationPayload(context, {
            document: salesInvoice as xtremFinanceData.interfaces.SalesFinanceDocumentWithTax,
            lines: (await salesInvoice.lines.toArray()) as xtremSales.interfaces.FinanceIntegration.SalesInvoiceDocumentLine[],
            documentType,
            targetDocumentType: 'accountsReceivableInvoice',
            isJustChecking,
        });
    const legislationId = await (await (await (await invoice.financialSite).legalCompany).legislation).id;

    // Revenue recognition
    if (
        !xtremFinanceData.sharedFunctions.common.legislationsWithoutRevenueRecognition.includes(legislationId) &&
        (await lines.some(async line => (await line.origin) === 'shipment'))
    ) {
        // build a unique array of source information (SysId + Number + constant type 'salesShipment'), 1 element for each
        // sales shipment linked to the invoice to add to the finance transaction record later
        const sourceLines = isJustChecking
            ? []
            : await xtremFinanceData.functions.getSourceLines({
                  lines: await lines.filter(async line => !!(await line.salesShipmentLines.length)).toArray(),
                  isConstantSourceDocumentType: true,
                  targetDocumentType: 'journalEntry',
                  type: 'salesShipment',
              });
        notificationsPayload.push({
            ...accountingStagingCommonPayload,
            batchSize: 0,
            documentType,
            targetDocumentType: 'journalEntry',
            documentLines: [],
            sourceLines,
        });
        await lines
            .filter(async line => !!(await line.salesShipmentLines.length))
            .forEach(async line => {
                const shipmentLine = await (await line.salesShipmentLines.at(0))?.linkedDocument;
                if (shipmentLine) {
                    const document = await shipmentLine.document;
                    notificationsPayload[1].documentLines.push({
                        baseDocumentLineSysId: line._id,
                        movementType: 'document',
                        sourceDocumentNumber: isJustChecking ? '' : await document.number,
                        sourceDocumentType: isJustChecking ? undefined : 'salesShipment',
                        currencySysId: (await document.currency)._id,
                        companyFxRate: (await document.companyFxRate) || 1.0,
                        companyFxRateDivisor: (await document.companyFxRateDivisor) || 1.0,
                        fxRateDate: (await document.shippingDate).toString(),
                        itemSysId: (await line.item)._id,
                        customerSysId: (await document.billToCustomer)._id,
                        amounts: [
                            {
                                amountType: 'amountExcludingTax',
                                amount: (await line.quantity) * (await shipmentLine.netPrice),
                                documentLineType: 'documentLine',
                            },
                        ],
                        storedDimensions: (await shipmentLine.storedDimensions) || {},
                        storedAttributes: {
                            ...((await shipmentLine.storedAttributes) as xtremMasterData.interfaces.StoredAttributes),
                            ...((await shipmentLine.computedAttributes) as {}),
                        },
                    });
                }
            });
    }

    return notificationsPayload;
}

/**
 * Creates all the notifications for the finance integration of a Sales Invoice
 * @param context Context
 * @param salesInvoice The sales invoice
 * @return The number of notifications created
 */
export async function salesInvoiceNotification(
    context: Context,
    salesInvoice: xtremSales.nodes.SalesInvoice,
): Promise<xtremFinanceData.interfaces.FinanceOriginDocumentPostReturn> {
    return xtremFinanceData.functions.sendNotificationsToAccountingEngine({
        context,
        notificationsPayload: await getSalesInvoiceNotificationPayload(context, salesInvoice),
        replyTopic: 'SalesInvoice/accountingInterface',
        isUpdate: false,
        batchTrackingId: context.batch.trackingId,
    });
}

/**
 * Adds the data for revenue recognition reversal to the sales credit memo notification (additional notification).
 * This is only done for legislations other than France and for lines linked to a return and originating from a shipment
 * @param accountingStagingCommonPayload: The basic payload for an additional notification payload for reversal
 * @param notificationsPayload: The payload array to add the new payload to (at index 1)
 * @param document An object that implements SalesFinanceDocumentWithTax to get header related properties from a document
 * @param lines An array of objects that implements SalesCreditMemoDocumentLine to get line related properties from a document
 */
export async function addSalesCreditMemoNotificationPayloadForRevenueRecognitionReversal(
    accountingStagingCommonPayload: xtremFinanceData.interfaces.AccountingStagingCommonPayload,
    notificationsPayload: xtremFinanceData.interfaces.FinanceIntegrationDocument[],
    document: xtremFinanceData.interfaces.SalesFinanceDocumentWithTax,
    lines: xtremSales.interfaces.FinanceIntegration.SalesCreditMemoDocumentLine[],
): Promise<void> {
    if (
        xtremFinanceData.sharedFunctions.common.legislationsWithoutRevenueRecognition.includes(
            await (
                await (
                    await (
                        await document.financialSite
                    ).legalCompany
                ).legislation
            ).id,
        )
    )
        return;
    notificationsPayload.push({
        ...accountingStagingCommonPayload,
        batchSize: 0,
        documentType: 'salesCreditMemo',
        targetDocumentType: 'journalEntry',
        documentLines: [],
    });
    await asyncArray(lines)
        .filter(
            async line =>
                !!line.toReturnRequestLines &&
                !!(await line.toReturnRequestLines.length) &&
                (await (await (await line.toReturnRequestLines.at(0))!.linkedDocument).origin) === 'shipment' &&
                (await line.origin) === 'return',
        )
        .forEach(async line => {
            const linkedDocument = await (await line.toReturnRequestLines.toArray())[0].linkedDocument;
            const sourceDocumentNumber = await (await linkedDocument.document).number;
            const salesShipmentLine = await (await linkedDocument.salesShipmentLines.toArray())[0].linkedDocument;
            const salesShipmentLineDocument = await salesShipmentLine.document;
            notificationsPayload[1].documentLines.push({
                baseDocumentLineSysId: line._id,
                movementType: 'document',
                sourceDocumentNumber,
                sourceDocumentType: 'salesReturnRequest',
                fxRateDate: (await salesShipmentLineDocument.fxRateDate)
                    ? (await salesShipmentLineDocument.fxRateDate).toString()
                    : (await salesShipmentLineDocument.documentDate).toString(),
                currencySysId: (await salesShipmentLineDocument.transactionCurrency)._id, // for 'document' take transaction currency
                companyFxRate: (await salesShipmentLineDocument.companyFxRate)
                    ? await salesShipmentLineDocument.companyFxRate
                    : 1.0,
                companyFxRateDivisor: (await salesShipmentLineDocument.companyFxRateDivisor)
                    ? await salesShipmentLineDocument.companyFxRateDivisor
                    : 1.0,
                itemSysId: (await line.item)._id,
                customerSysId: (await document.billToCustomer)._id,
                amounts: [
                    {
                        amountType: 'amountExcludingTax',
                        amount: (await line.quantity) * (await salesShipmentLine.netPrice),
                        documentLineType: 'documentLine',
                    },
                ],
                storedDimensions: (await salesShipmentLine.storedDimensions) || {},
                storedAttributes: {
                    ...((await salesShipmentLine.storedAttributes) as xtremMasterData.interfaces.StoredAttributes),
                    ...((await salesShipmentLine.computedAttributes) as {}),
                },
            });
        });
    // remove additional notification if no lines exist
    if (!notificationsPayload[1].documentLines.length) notificationsPayload.pop();
}

/**
 * Creates all the notifications for the finance integration of a Sales Credit Memo
 * @param context Context
 * @param document An object that implements SalesFinanceDocumentWithTax to get header related properties from a document
 * @param lines An array of objects that implements InvoiceDocumentLine to get line related properties from a document
 * @return The number of notifications created
 */
export async function salesCreditMemoNotification(
    context: Context,
    document: xtremFinanceData.interfaces.SalesFinanceDocumentWithTax,
    lines: xtremSales.interfaces.FinanceIntegration.SalesCreditMemoDocumentLine[],
    documentType: xtremFinanceData.enums.FinanceDocumentType,
    replyTopic: string,
): Promise<xtremFinanceData.interfaces.FinanceOriginDocumentPostReturn> {
    const { accountingStagingCommonPayload, notificationsPayload } =
        await xtremFinanceData.functions.getPurchaseSalesInvoiceCreditMemoNotificationPayload(context, {
            document,
            lines,
            documentType,
            targetDocumentType: 'accountsReceivableInvoice',
            isJustChecking: false,
        });

    // add another notification (same batchId) for revenue recognition reversal
    await addSalesCreditMemoNotificationPayloadForRevenueRecognitionReversal(
        accountingStagingCommonPayload,
        notificationsPayload,
        document,
        lines,
    );

    return xtremFinanceData.functions.sendNotificationsToAccountingEngine({
        context,
        notificationsPayload,
        replyTopic,
        isUpdate: false,
    });
}

/**
 * For a given sales invoice, we run the controls for finance integration.
 * @param context: A context
 * @param salesInvoice The sales invoice
 * @return A CreateFinanceDocumentsReturn object
 */
export async function salesInvoiceControlFromNotificationPayload(
    context: Context,
    salesInvoice: xtremSales.nodes.SalesInvoice,
): Promise<xtremFinanceData.interfaces.CreateFinanceDocumentsReturn> {
    if (await (await (await salesInvoice.financialSite).legalCompany).doStockPosting) {
        return xtremFinanceData.functions.FinanceDocumentGeneration.financeDownstreamDocumentControlFromNotificationPayload(
            context,
            await getSalesInvoiceNotificationPayload(context, salesInvoice, true),
        );
    }
    return {
        documentsCreated: [],
        validationMessages: [],
    };
}

/**
 * For a given sales invoice, we run the controls for finance integration and create the result as a
 * xtremFinanceData.interfaces.MutationResult interface.
 * @param context: A context
 * @param salesInvoice The sales invoice
 * @return A MutationResult object
 */
export async function salesInvoiceControlAndCreateMutationResult(
    context: Context,
    salesInvoice: xtremSales.nodes.SalesInvoice,
): Promise<xtremFinanceData.interfaces.MutationResult> {
    const financeIntegrationCheckResult: xtremFinanceData.interfaces.MutationResult = {
        wasSuccessful: false,
        message: '',
    };

    const salesInvoiceControlFromNotificationPayloadErrors = (
        await xtremSales.functions.FinanceIntegration.salesInvoiceControlFromNotificationPayload(context, salesInvoice)
    ).validationMessages.filter(validationMessage => validationMessage.type === ValidationSeverity.error);

    if (salesInvoiceControlFromNotificationPayloadErrors.length) {
        financeIntegrationCheckResult.message = salesInvoiceControlFromNotificationPayloadErrors
            .map(message => '* '.concat(message.message))
            .join('\n');
        return financeIntegrationCheckResult;
    }

    financeIntegrationCheckResult.wasSuccessful = true;
    return financeIntegrationCheckResult;
}

/**
 * For a given sales credit memo, we run the controls for finance integration.
 * @param context: The payload to add the lines to
 * @param salesCreditMemo An object that implements SalesFinanceDocument to get header related properties from a document
 * @return A CreateFinanceDocumentsReturn object
 */
export async function salesCreditMemoControlFromNotificationPayload(
    context: Context,
    salesCreditMemo: xtremSales.nodes.SalesCreditMemo,
): Promise<xtremFinanceData.interfaces.CreateFinanceDocumentsReturn> {
    if (await (await (await salesCreditMemo.financialSite).legalCompany).doStockPosting) {
        return xtremFinanceData.functions.FinanceDocumentGeneration.financeDownstreamDocumentControlFromNotificationPayload(
            context,
            (
                await xtremFinanceData.functions.getPurchaseSalesInvoiceCreditMemoNotificationPayload(context, {
                    document: salesCreditMemo as xtremFinanceData.interfaces.SalesFinanceDocumentWithTax,
                    lines: await salesCreditMemo.lines.toArray(),
                    documentType: 'salesCreditMemo',
                    targetDocumentType: 'accountsReceivableInvoice',
                    isJustChecking: false,
                })
            ).notificationsPayload,
        );
    }
    return {
        documentsCreated: [],
        validationMessages: [],
    };
}

/**
 * Adds the data for movement type 'document' for stock and non stock items to the sales return receipt notification.
 * This is only done for legislations other than France.
 * Notification for non sales return receipt is done into several functions due to cognitive complexity
 * @param notificationsPayload: The payload to add the lines to
 * @param document An object that implements SalesFinanceDocument to get header related properties from a document
 * @param lines An array of objects that implements ReturnDocumentLine to get line related properties from a document
 * @return The number of notifications created
 */
export async function addSalesReturnReceiptNotificationPayloadForTypeDocument(
    notificationsPayload: xtremFinanceData.interfaces.FinanceIntegrationDocument[],
    document: xtremFinanceData.interfaces.SalesFinanceDocument,
    lines: xtremSales.interfaces.FinanceIntegration.SalesReturnDocumentLine[],
): Promise<void> {
    // Movement type 'document' for non-stock and stock items only for countries other than France
    if (
        xtremFinanceData.sharedFunctions.common.legislationsWithoutRevenueRecognition.includes(
            await (
                await (
                    await (
                        await document.financialSite
                    ).legalCompany
                ).legislation
            ).id,
        )
    )
        return;
    await asyncArray(lines)
        .filter(async line => {
            if (
                line.toReturnRequestLines &&
                (await line.toReturnRequestLines.length) > 0 &&
                (await (
                    await line.toReturnRequestLines.toArray()
                )[0]?.linkedDocument)
            ) {
                const linkedDocument = await (await line.toReturnRequestLines.toArray())[0]?.linkedDocument;
                const returnType = await (await linkedDocument.document).returnType;
                return (
                    (await linkedDocument.origin) === 'shipment' &&
                    (returnType === 'receiptAndCreditMemo' || returnType === 'creditMemo')
                );
            }
            return false;
        })
        .forEach(async line => {
            const sourceDocumentNumber =
                (await line.origin) === 'return'
                    ? await (
                          await (
                              await (
                                  await line.toReturnRequestLines.toArray()
                              )[0].linkedDocument
                          ).document
                      ).number
                    : '';
            const salesShipmentLine = await (await line.salesShipmentLines.toArray())[0].linkedDocument;

            const fxRateDate = await document.fxRateDate;
            const companyFxRate = await document.companyFxRate;
            const companyFxRateDivisor = await document.companyFxRateDivisor;
            notificationsPayload[0].documentLines.push({
                baseDocumentLineSysId: line._id,
                movementType: 'document',
                sourceDocumentNumber,
                sourceDocumentType: sourceDocumentNumber ? 'salesReturnRequest' : undefined,
                fxRateDate: fxRateDate ? fxRateDate.toString() : (await document.documentDate).toString(),
                currencySysId: (await (await salesShipmentLine.document).transactionCurrency)._id, // for 'document' take transaction currency
                companyFxRate: companyFxRate || 1.0,
                companyFxRateDivisor: companyFxRateDivisor || 1.0,
                itemSysId: (await line.item)._id,
                customerSysId: (await document.billToCustomer)._id,
                amounts: [
                    {
                        amountType: 'amountExcludingTax',
                        amount: (await line.quantity) * (await salesShipmentLine.netPrice),
                        documentLineType: 'documentLine',
                    },
                ],
                storedDimensions: (await line.storedDimensions) || {},
                storedAttributes: {
                    ...((await line.storedAttributes) as xtremMasterData.interfaces.StoredAttributes),
                    ...((await line.computedAttributes) as {}),
                },
            });
        });
}

/**
 * Gets a notification payload needed for the finance integration of a sales return receipt
 * Notification for non sales return receipt is done into several functions due to cognitive complexity
 * @param context Context
 * @param document An object that implements SalesFinanceDocument to get header related properties from a document
 * @param lines An array of objects that implements ReturnDocumentLine to get line related properties from a document
 * @return The number of notifications created
 */
export async function getSalesReturnReceiptNotificationPayload(
    document: xtremFinanceData.interfaces.SalesFinanceDocument,
    lines: xtremSales.interfaces.FinanceIntegration.SalesReturnDocumentLine[],
    isJustChecking = false,
): Promise<xtremFinanceData.interfaces.FinanceIntegrationDocument[]> {
    const accountingStagingCommonPayload: xtremFinanceData.interfaces.AccountingStagingCommonPayload =
        await xtremFinanceData.functions.getAccountingStagingCommonPayload({ document, isJustChecking });

    const notificationsPayload: xtremFinanceData.interfaces.FinanceIntegrationDocument[] = [
        {
            ...accountingStagingCommonPayload,
            batchSize: 0,
            documentType: 'salesReturnReceipt',
            targetDocumentType: 'journalEntry',
            documentLines: await asyncArray(lines)
                .filter(async line => (await line.item).isStockManaged)
                .map(async line => {
                    const { movementAmount } = await xtremFinanceData.functions.getStockMovementsAmounts(line);
                    const lineAmount = xtremFinanceData.functions.getStockLineAmount(
                        'SalesReturnReceiptLine',
                        movementAmount,
                    );
                    const fxRateDate = await document.fxRateDate;
                    return {
                        baseDocumentLineSysId: line._id,
                        movementType: 'stockJournal',
                        sourceDocumentNumber: await line.sourceDocumentNumber,
                        sourceDocumentType: await line.sourceDocumentType,
                        fxRateDate: fxRateDate ? fxRateDate.toString() : (await document.documentDate).toString(),
                        currencySysId: (await (await (await document.financialSite).legalCompany).currency)._id, // always take currency of financial site
                        companyFxRate: 1.0, // no currency conversion for 'stockJournal'
                        companyFxRateDivisor: 1.0,
                        itemSysId: (await line.item)._id,
                        customerSysId: (await document.billToCustomer)._id,
                        amounts: [{ amountType: 'amount', amount: lineAmount, documentLineType: 'documentLine' }],
                        storedDimensions: (await line.storedDimensions) || {},
                        storedAttributes: {
                            ...((await line.storedAttributes) as xtremMasterData.interfaces.StoredAttributes),
                            ...((await line.computedAttributes) as {}),
                        },
                    } as xtremFinanceData.interfaces.FinanceIntegrationDocumentLine;
                })
                .toArray(),
        },
    ];

    await addSalesReturnReceiptNotificationPayloadForTypeDocument(notificationsPayload, document, lines);

    return notificationsPayload;
}

/**
 * Creates all the notifications for the finance integration of a Sales return receipt
 * @param context Context
 * @param document An object that implements SalesFinanceDocument to get header related properties from a document
 * @param lines An array of objects that implements ReturnDocumentLine to get line related properties from a document
 * @return The number of notifications created
 */
export async function salesReturnReceiptNotification(
    context: Context,
    document: xtremFinanceData.interfaces.SalesFinanceDocument,
    lines: xtremSales.interfaces.FinanceIntegration.SalesReturnDocumentLine[],
): Promise<xtremFinanceData.interfaces.FinanceOriginDocumentPostReturn> {
    await context.flushDeferredActions();
    return xtremFinanceData.functions.sendNotificationsToAccountingEngine({
        context,
        notificationsPayload: await getSalesReturnReceiptNotificationPayload(document, lines),
        replyTopic: 'SalesReturnReceipt/accountingInterface',
        isUpdate: false,
    });
}

/**
 * For a given sales shipment, we run the controls for finance integration.
 * @param context: A context
 * @param salesShipment The sales shipment
 * @return A CreateFinanceDocumentsReturn object
 */
export async function salesShipmentControlFromNotificationPayload(
    context: Context,
    salesShipment: xtremSales.nodes.SalesShipment,
): Promise<xtremFinanceData.interfaces.CreateFinanceDocumentsReturn> {
    const shipment: xtremFinanceData.interfaces.SalesFinanceDocument = salesShipment;
    const company = await (await shipment.financialSite).legalCompany;
    const doStockPosting = await company.doStockPosting;
    if (!doStockPosting) {
        return {
            documentsCreated: [],
            validationMessages: [],
        };
    }

    const lines = salesShipment.lines as AsyncArray<xtremFinanceData.interfaces.FinanceOriginDocumentLineWithStock>;

    const isCompanyWithNonAbsorbedAmountsPosting = await company.doNonAbsorbedPosting;

    await lines
        .filter(
            async shipLine => (await (await shipLine.item).isStockManaged) && !(await shipLine.stockMovements.length),
        )
        .forEach(async line => {
            line.stockMovementArray = [
                {
                    movementAmount: (await line.amountExcludingTax) || 0,
                    orderAmount: (await line.amountExcludingTax) || 0,
                    nonAbsorbedAmount: !isCompanyWithNonAbsorbedAmountsPosting
                        ? 0
                        : (await line.amountExcludingTax) || 0,
                },
            ];
        });

    return xtremFinanceData.functions.FinanceDocumentGeneration.financeDownstreamDocumentControlFromNotificationPayload(
        context,
        await xtremFinanceData.functions.getSalesShipmentNotificationPayload(
            shipment,
            await lines.toArray(),
            shipment.$.status === NodeStatus.modified ? await shipment.number : 'Unsaved',
        ),
    );
}

/**
 * For a given sales shipment, we run the controls for finance integration, including controlling the future invoice,
 * and create the result as a xtremFinanceData.interfaces.MutationResult interface.
 * @param context: A context
 * @param salesShipment The sales shipment
 * @return A MutationResult object
 */
export async function salesShipmentControlAndCreateMutationResult(
    context: Context,
    salesShipment: xtremSales.nodes.SalesShipment,
): Promise<xtremFinanceData.interfaces.MutationResult> {
    const salesShipmentControlFromNotificationPayloadErrors = (
        await xtremSales.functions.FinanceIntegration.salesShipmentControlFromNotificationPayload(
            context,
            salesShipment,
        )
    ).validationMessages.filter(validationMessage => validationMessage.type === ValidationSeverity.error);

    if (salesShipmentControlFromNotificationPayloadErrors.length) {
        return {
            wasSuccessful: false,
            message: salesShipmentControlFromNotificationPayloadErrors.map(error => `* ${error.message}`).join('\n'),
        };
    }

    const invoice = await getInvoiceInstance(context, salesShipment);
    if (invoice) {
        return xtremSales.nodes.SalesInvoice.financeIntegrationCheck(context, invoice);
    }

    return {
        wasSuccessful: true,
        message: '',
    };
}

/**
 * For a given sales return receipt, we run the controls for finance integration.
 * @param context: A context
 * @param salesReturnReceipt The sales return receipt
 * @return A CreateFinanceDocumentsReturn object
 */
export async function salesReturnReceiptControlFromNotificationPayload(
    context: Context,
    salesReturnReceipt: xtremSales.nodes.SalesReturnReceipt,
): Promise<xtremFinanceData.interfaces.CreateFinanceDocumentsReturn> {
    const returnReceipt: xtremFinanceData.interfaces.SalesFinanceDocument = salesReturnReceipt;

    const lines: xtremSales.interfaces.FinanceIntegration.SalesReturnDocumentLine[] =
        await salesReturnReceipt.lines.toArray();

    const company = await context.read(xtremSystem.nodes.Company, {
        id: await (await (await returnReceipt.financialSite).legalCompany).id,
    });
    const isCompanyWithNonAbsorbedAmountsPosting = await company.doNonAbsorbedPosting;
    const doStockPosting = await company.doStockPosting;

    if (doStockPosting) {
        await asyncArray(lines).forEach(async line => {
            if (await (await line.item).isStockManaged) {
                if (!(await line.stockMovements.length)) {
                    line.stockMovementArray = [
                        {
                            movementAmount: (await line.amountExcludingTax) || 0,
                            orderAmount: (await line.amountExcludingTax) || 0,
                            nonAbsorbedAmount: !isCompanyWithNonAbsorbedAmountsPosting
                                ? 0
                                : (await line.amountExcludingTax) || 0,
                        },
                    ];
                }
            }
        });

        return xtremFinanceData.functions.FinanceDocumentGeneration.financeDownstreamDocumentControlFromNotificationPayload(
            context,
            await xtremSales.functions.FinanceIntegration.getSalesReturnReceiptNotificationPayload(
                returnReceipt,
                lines,
            ),
        );
    }
    return {
        documentsCreated: [],
        validationMessages: [],
    };
}

/**
 * For a given sales return receipt, we run the controls for finance integration
 * and create the result as a xtremFinanceData.interfaces.MutationResult interface.
 * @param context: A context
 * @param salesReturnReceipt The sales return receipt
 * @return A MutationResult object
 */
export async function salesReturnReceiptControlAndCreateMutationResult(
    context: Context,
    salesReturnReceipt: xtremSales.nodes.SalesReturnReceipt,
): Promise<xtremFinanceData.interfaces.MutationResult> {
    const financeIntegrationCheckResult: xtremFinanceData.interfaces.MutationResult = {
        wasSuccessful: false,
        message: '',
    };

    const salesReturnReceiptControlFromNotificationPayloadErrors = (
        await xtremSales.functions.FinanceIntegration.salesReturnReceiptControlFromNotificationPayload(
            context,
            salesReturnReceipt,
        )
    ).validationMessages.filter(validationMessage => validationMessage.type === ValidationSeverity.error);

    if (salesReturnReceiptControlFromNotificationPayloadErrors.length) {
        financeIntegrationCheckResult.message = salesReturnReceiptControlFromNotificationPayloadErrors
            .map(message => '* '.concat(message.message))
            .join('\n');
        return financeIntegrationCheckResult;
    }

    financeIntegrationCheckResult.wasSuccessful = true;
    return financeIntegrationCheckResult;
}
