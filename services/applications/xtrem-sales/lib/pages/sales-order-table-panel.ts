import type { decimal, Dict, ExtractEdgesPartial, Filter } from '@sage/xtrem-client';
import { extractEdges } from '@sage/xtrem-client';
import { confirmDialogWithAcceptButtonText } from '@sage/xtrem-distribution/build/lib/client-functions/common';
import type { Address, CustomerPriceReason, Item, UnitOfMeasure } from '@sage/xtrem-master-data-api';
import type { GraphApi, SalesOrder, SalesOrderLine, SalesOrderLineToSalesShipmentLine } from '@sage/xtrem-sales-api';
import * as ui from '@sage/xtrem-ui';
import type { SalesOrderFiltered } from '../client-functions/interfaces/inquiry';
import { isAssignmentCheckedOnSalesOrderLineShipNotAllocatedQuantity } from '../client-functions/sales-order-assignment';

@ui.decorators.page<SalesOrderTablePanel, SalesOrderLine>({
    title: 'Add lines from orders',
    mode: 'default',
    node: '@sage/xtrem-sales/SalesOrderLine',
    isTransient: true,
    access: { node: '@sage/xtrem-sales/SalesShipment', bind: '$create' },
    module: 'xtrem-sales',
    businessActions() {
        return [this.cancel, this.confirm];
    },
    async onLoad() {
        this.onLoadAvalaraExtension();
        await this.loadSalesOrderLines(
            this.$.queryParameters.shipToCustomer,
            this.$.queryParameters.shipToCustomerAddress,
            this.$.queryParameters.shipToAddress,
            this.$.queryParameters.billToCustomer,
            this.$.queryParameters.billToLinkedAddress,
            this.$.queryParameters.date,
            this.$.queryParameters.site,
            this.$.queryParameters.stockSite,
            this.$.queryParameters.deliveryMode,
            this.$.queryParameters.incoterm,
            this.$.queryParameters.currency,
            this.$.queryParameters.paymentTerm,
            this.$.queryParameters.shipmentLines,
            this.$.queryParameters.shipmentLinesLength,
        );
        this.$.setPageClean();
    },
})
export class SalesOrderTablePanel extends ui.Page<GraphApi> {
    salesOrderLineQueryFields: Dict<any> = {};

    @ui.decorators.pageAction<SalesOrderTablePanel>({
        title: 'Add',
        async onClick() {
            await this.confirmation();
        },
        isDisabled() {
            return this.salesOrderLines.selectedRecords.length === 0;
        },
    })
    confirm: ui.PageAction;

    @ui.decorators.pageAction<SalesOrderTablePanel>({
        title: 'Cancel',
        buttonType: 'tertiary',
        onClick() {
            this.$.finish();
        },
    })
    cancel: ui.PageAction;

    @ui.decorators.section<SalesOrderTablePanel>({})
    lineSection: ui.containers.Section;

    @ui.decorators.block<SalesOrderTablePanel>({
        parent() {
            return this.lineSection;
        },
    })
    lineBlock: ui.containers.Block;

    @ui.decorators.separatorField<SalesOrderTablePanel>({
        parent() {
            return this.lineBlock;
        },
        isFullWidth: true,
        isHidden: true,
    })
    lineStatusSeparator: ui.fields.Separator;

    @ui.decorators.tableField<SalesOrderTablePanel, SalesOrderLine>({
        parent() {
            return this.lineSection;
        },
        title: 'Sales order lines',
        isTitleHidden: true,
        isTransient: true,
        isChangeIndicatorDisabled: true,
        isReadOnly: true,
        orderBy: { document: { number: +1 }, _sortValue: +1 },
        node: '@sage/xtrem-sales/SalesOrderLine',
        cardView: false,
        mobileCard: {
            title: ui.nestedFields.reference<SalesOrderTablePanel, SalesOrderLine, SalesOrder>({
                bind: 'document',
                title: 'Number',
                valueField: 'number',
                node: '@sage/xtrem-sales/SalesOrder',
                tunnelPage: undefined,
            }),
            titleRight: ui.nestedFields.reference<SalesOrderTablePanel, SalesOrderLine, Item>({
                bind: 'item',
                title: 'Item',
                valueField: 'id',
                node: '@sage/xtrem-master-data/Item',
                tunnelPage: undefined,
                isHiddenOnMainField: true,
            }),
            line2: ui.nestedFields.date({ title: 'Shipping date', bind: 'shippingDate' }),
            line2Right: ui.nestedFields.numeric({
                bind: 'quantity',
                title: 'Quantity In Sales Unit',
                postfix: (_rowId, rowData) => rowData?.unit?.symbol,
                scale: (_rowId: any, rowData: any) => rowData?.unit?.decimalDigits || 0,
            }),
            image: ui.nestedFields.image({ bind: { item: { image: true } }, title: 'Image' }),
        },
        columns: [
            ui.nestedFields.image({ bind: { item: { image: true } }, title: 'Image', size: 'small' }),
            ui.nestedFields.technical({ bind: '_sortValue' }),
            ui.nestedFields.reference<SalesOrderTablePanel, SalesOrderLine, Item>({
                bind: 'item',
                title: 'Item',
                valueField: 'name',
                helperTextField: 'id',
                node: '@sage/xtrem-master-data/Item',
                tunnelPage: '@sage/xtrem-master-data/Item',
                columns: [
                    ui.nestedFields.text({ bind: 'name' }),
                    ui.nestedFields.text({ bind: 'id' }),
                    ui.nestedFields.text({ bind: 'description' }),
                    ui.nestedFields.text({ title: 'Category', bind: { category: { name: true } } }),
                    ui.nestedFields.technical({ bind: 'type' }),
                ],
            }),
            ui.nestedFields.text<SalesOrderTablePanel, SalesOrderLine>({
                bind: { item: { id: true } },
                title: 'Item ID',
            }),
            ui.nestedFields.text({
                title: 'Item description',
                bind: 'itemDescription',
                isDisabled: true,
                isHiddenOnMainField: true,
            }),
            ui.nestedFields.text<SalesOrderTablePanel, SalesOrderLine>({
                bind: { document: { number: true } },
                title: 'Number',
            }),
            ui.nestedFields.technical({
                node: '@sage/xtrem-sales/SalesOrder',
                bind: 'document',
                nestedFields: [
                    ui.nestedFields.technical({ bind: 'number' }),
                    ui.nestedFields.technical({ bind: 'isTransferHeaderNote' }),
                    ui.nestedFields.technical({ bind: 'isTransferLineNote' }),
                ],
            }),
            ui.nestedFields.label<SalesOrderTablePanel, SalesOrderLine>({
                title: 'Status',
                bind: 'shippingStatus',
                optionType: '@sage/xtrem-sales/SalesDocumentShippingStatus',
            }),
            ui.nestedFields.numeric<SalesOrderTablePanel, SalesOrderLine>({
                bind: 'quantity',
                title: 'Quantity to ship',
                isDisabled(_rowId, rowData) {
                    return !this.salesOrderLines.selectedRecords.includes(rowData?._id);
                },
                postfix: (_rowId: any, rowData: any) => rowData?.unit?.symbol || '',
                scale: (_rowId: any, rowData: any) => rowData?.unit?.decimalDigits || 0,
                validation(value, rowData) {
                    if (+value <= 0) {
                        return ui.localize(
                            '@sage/xtrem-sales/pages__sales_order_table_panel__error_zero_or_negative',
                            'You cannot enter a quantity less than or equal to zero.',
                        );
                    }
                    if (+value > +rowData.remainingQuantityToShipInSalesUnit) {
                        return ui.localize(
                            '@sage/xtrem-sales/pages__sales_order_table_panel__error_greater_quantity',
                            'You cannot ship a quantity more than the order you select.',
                        );
                    }
                    return undefined;
                },
            }),
            ui.nestedFields.date({ title: 'Shipping date', bind: 'shippingDate' }),
            ui.nestedFields.technical<SalesOrderTablePanel, SalesOrderLine, UnitOfMeasure>({
                node: '@sage/xtrem-master-data/UnitOfMeasure',
                bind: 'unit',
                nestedFields: [
                    ui.nestedFields.technical({ bind: 'name' }),
                    ui.nestedFields.technical({ bind: 'id' }),
                    ui.nestedFields.technical({ bind: 'symbol' }),
                    ui.nestedFields.technical({ bind: 'description' }),
                    ui.nestedFields.technical({ bind: 'decimalDigits' }),
                ],
            }),
            ui.nestedFields.technical({ bind: 'storedAttributes' }),
            ui.nestedFields.technical({ bind: 'storedDimensions' }),
            ui.nestedFields.technical<SalesOrderTablePanel, SalesOrderLine, Address>({
                bind: 'shipToAddress',
                node: '@sage/xtrem-master-data/Address',
                nestedFields: [
                    ui.nestedFields.technical({ bind: 'name' }),
                    ui.nestedFields.technical({ bind: 'addressLine1' }),
                    ui.nestedFields.technical({ bind: 'addressLine2' }),
                    ui.nestedFields.technical({ bind: 'city' }),
                    ui.nestedFields.technical({ bind: 'region' }),
                    ui.nestedFields.technical({ bind: 'postcode' }),
                    ui.nestedFields.technical({
                        bind: 'country',
                        node: '@sage/xtrem-structure/Country',
                        nestedFields: [
                            ui.nestedFields.technical({ bind: 'name' }),
                            ui.nestedFields.technical({ bind: 'id' }),
                        ],
                    }),
                ],
            }),
            ui.nestedFields.label<SalesOrderTablePanel, SalesOrderLine>({
                title: 'Price origin',
                bind: 'priceOrigin',
                optionType: '@sage/xtrem-sales/SalesPriceOrigin',
                isHiddenOnMainField: true,
            }),
            ui.nestedFields.reference<SalesOrderTablePanel, SalesOrderLine, CustomerPriceReason>({
                bind: 'priceReason',
                title: 'Price reason',
                valueField: 'name',
                node: '@sage/xtrem-master-data/CustomerPriceReason',
                isHiddenOnMainField: true,
                columns: [
                    ui.nestedFields.text({ bind: 'name' }),
                    ui.nestedFields.numeric({ bind: 'priority', title: 'Priority' }),
                    ui.nestedFields.technical({ bind: '_id' }),
                    ui.nestedFields.technical({ bind: 'isActive' }),
                    ui.nestedFields.technical({ bind: 'description' }),
                ],
            }),
            ui.nestedFields.technical({
                node: '@sage/xtrem-master-data/ItemSite',
                bind: 'itemSite',
                nestedFields: [
                    ui.nestedFields.technical({ bind: '_id' }),
                    ui.nestedFields.technical({ bind: 'prodLeadTime' }),
                    ui.nestedFields.technical({ bind: 'isOrderToOrder' }),
                    ui.nestedFields.technical({ bind: 'preferredProcess' }),
                ],
            }),
            ui.nestedFields.technical({ bind: 'quantityAllocated' }),
            ui.nestedFields.technical({ bind: 'remainingQuantityToShipInStockUnit' }),
            ui.nestedFields.technical({ bind: 'remainingQuantityToShipInSalesUnit' }),
            ui.nestedFields.technical({ bind: 'suppliedQuantity' }),
            ui.nestedFields.text({ bind: 'itemDescription', title: 'Item description' }),
        ],

        async onRowSelected(rowId: string, rowItem: SalesOrderLine) {
            const isAssignmentChecked = await isAssignmentCheckedOnSalesOrderLineShipNotAllocatedQuantity(
                this,
                rowItem as any, // TODO : remove any
            );
            if (isAssignmentChecked) {
                if (
                    this.salesOrderLines.selectedRecords.length === 1 &&
                    this.$.queryParameters.shipmentLinesLength === 0
                ) {
                    this.$.loader.isHidden = false;

                    this.updateFieldValuesAvalaraExtension(rowItem);

                    await this.loadSalesOrderLines(
                        this.$.queryParameters.shipToCustomer,
                        this.$.queryParameters.shipToCustomerAddress,
                        rowItem.shipToAddress,
                        this.$.queryParameters.billToCustomer,
                        this.$.queryParameters.billToLinkedAddress,
                        this.$.queryParameters.date,
                        this.$.queryParameters.site,
                        this.$.queryParameters.stockSite,
                        this.$.queryParameters.deliveryMode,
                        this.$.queryParameters.incoterm,
                        this.$.queryParameters.currency,
                        this.$.queryParameters.paymentTerm,
                        this.$.queryParameters.shipmentLines,
                        this.$.queryParameters.shipmentLinesLength,
                        rowItem,
                    );
                    this.$.loader.isHidden = true;
                }
            } else {
                this.salesOrderLines.unselectRecord(rowId);
            }
        },

        async onRowUnselected(recordId: string, rowItem: SalesOrderLine) {
            if (this.salesOrderLines.selectedRecords.length === 0 && this.$.queryParameters.shipmentLinesLength === 0) {
                this.$.loader.isHidden = false;
                await this.loadSalesOrderLines(
                    this.$.queryParameters.shipToCustomer,
                    this.$.queryParameters.shipToCustomerAddress,
                    this.$.queryParameters.shipToAddress,
                    this.$.queryParameters.billToCustomer,
                    this.$.queryParameters.billToLinkedAddress,
                    this.$.queryParameters.date,
                    this.$.queryParameters.site,
                    this.$.queryParameters.stockSite,
                    this.$.queryParameters.deliveryMode,
                    this.$.queryParameters.incoterm,
                    this.$.queryParameters.currency,
                    this.$.queryParameters.paymentTerm,
                    this.$.queryParameters.shipmentLines,
                    this.$.queryParameters.shipmentLinesLength,
                );
                this.$.loader.isHidden = true;
            }
            if (rowItem.quantity !== rowItem.remainingQuantityToShipInSalesUnit) {
                const row = this.salesOrderLines.getRecordValue(recordId);
                if (row) {
                    row.quantity = row.remainingQuantityToShipInSalesUnit;
                    this.salesOrderLines.addOrUpdateRecordValue(row);
                }
                await this.salesOrderLines.validate();
            }
        },
    })
    salesOrderLines: ui.fields.Table<SalesOrderLine>;

    /**
     * This asynchronous function loads a list of sales order lines filtered by criteria
     * and calculate the remaining quantity to ship for each of them
     * @param shipToCustomer:
     * @param shipToCustomerAddress:
     * @param billToCustomer:
     * @param billToLinkedAddress:
     * @param date:
     * @param site:
     * @param stockSite:
     * @param deliveryMode:
     * @param incoterm:
     * @param currency:
     * @param paymentTerm:
     * @param shipmentLines:
     * @param shipmentLinesLength:
     * @param rowSelected?:
     */
    private async loadSalesOrderLines(
        shipToCustomer: any,
        shipToCustomerAddress: any,
        shipToAddress: any,
        billToCustomer: any,
        billToLinkedAddress: any,
        date: any,
        site: any,
        stockSite: any,
        deliveryMode: any,
        incoterm: any,
        currency: any,
        paymentTerm: any,
        shipmentLines: any,
        shipmentLinesLength: any,
        rowSelected?: any,
    ) {
        const reqFilter: Filter<SalesOrderLine> = this.getLoadSalesOrderLinesFilter(
            shipToCustomer,
            shipToCustomerAddress,
            billToCustomer,
            billToLinkedAddress,
            site,
            stockSite,
            deliveryMode,
            incoterm,
            currency,
            paymentTerm,
            shipmentLinesLength,
            rowSelected,
        );

        const result = extractEdges(
            await this.$.graph
                .node('@sage/xtrem-sales/SalesOrderLine')
                .query(
                    ui.queryUtils.edgesSelector(
                        {
                            _id: true,
                            _sortValue: true,
                            status: true,
                            shippingStatus: true,
                            item: {
                                stockUnit: { name: true, id: true, _id: true, symbol: true },
                                name: true,
                                id: true,
                                _id: true,
                                isStockManaged: true,
                                minimumSalesQuantity: true,
                                maximumSalesQuantity: true,
                                image: { value: true },
                            },
                            itemDescription: true,
                            unitToStockUnitConversionFactor: true,
                            deliveryMode: { _id: true },
                            requestedDeliveryDate: true,
                            deliveryLeadTime: true,
                            doNotShipBeforeDate: true,
                            doNotShipAfterDate: true,
                            shippingDate: true,
                            expectedDeliveryDate: true,
                            quantity: true,
                            quantityInStockUnit: true,
                            site: { _id: true },
                            unit: { name: true, id: true, decimalDigits: true, _id: true, symbol: true },
                            stockUnit: { name: true, id: true, decimalDigits: true, _id: true, symbol: true },
                            shipToCustomerAddress: { _id: true },
                            shipToAddress: {
                                name: true,
                                addressLine1: true,
                                addressLine2: true,
                                region: true,
                                country: { _id: true },
                                city: true,
                                postcode: true,
                                locationPhoneNumber: true,
                            },
                            discount: true,
                            charge: true,
                            document: {
                                number: true,
                                _id: true,
                                incoterm: { _id: true },
                                soldToCustomer: { _id: true },
                                shipToCustomer: { _id: true },
                                billToCustomer: { _id: true },
                                billToLinkedAddress: { _id: true },
                                currency: { _id: true },
                                paymentTerm: { _id: true },
                                customerNumber: true,
                                isTransferHeaderNote: true,
                                isTransferLineNote: true,
                            },
                            grossPrice: true,
                            netPrice: true,
                            priceOrigin: true,
                            priceReason: {
                                _id: true,
                                name: true,
                                description: true,
                                isActive: true,
                                priority: true,
                            },
                            amountExcludingTax: true,
                            storedAttributes: true,
                            storedDimensions: true,
                            ...this.salesOrderLineQueryFields,
                            itemSite: {
                                prodLeadTime: true,
                                isOrderToOrder: true,
                                preferredProcess: true,
                            },
                            quantityAllocated: true,
                            remainingQuantityToShipInStockUnit: true,
                            remainingQuantityToShipInSalesUnit: true,
                            suppliedQuantity: true,
                            workInProgress: {
                                _id: true,
                            },
                        },
                        {
                            filter: reqFilter,
                            first: 200,
                            orderBy: { document: { number: 1 }, _sortValue: 1 },
                        },
                    ),
                )
                .execute(),
        );

        const filterDeliveryDates = SalesOrderTablePanel.checkInvalidDeliveryDates(result, date);
        const filterDeliveryAddress = this.checkInvalidDeliveryAddress(
            filterDeliveryDates.filteredSalesOrderLines,
            shipmentLinesLength,
            shipToAddress,
        );

        this.salesOrderLines.value.forEach(line => this.salesOrderLines.removeRecord(line._id));

        await this.filterOnRemainingQuantityAndAddRows(filterDeliveryAddress.filteredSalesOrderLines);
        if (shipmentLines) {
            this.addOrRemoveRowsFilteredOnAlreadySelectedSalesOrderLinesToShip(shipmentLines);
        }

        this.showWarnings(filterDeliveryDates.isValid, filterDeliveryAddress.isValid);
    }

    static checkInvalidDeliveryDates(
        salesOrderLines: ExtractEdgesPartial<SalesOrderLine>[],
        date: string,
    ): SalesOrderFiltered {
        const filteredSalesOrderLines = salesOrderLines.filter(
            resultLine =>
                (resultLine.doNotShipBeforeDate &&
                    date >= resultLine.doNotShipBeforeDate &&
                    resultLine.doNotShipAfterDate &&
                    date <= resultLine.doNotShipAfterDate) ||
                (!resultLine.doNotShipBeforeDate &&
                    resultLine.doNotShipAfterDate &&
                    date <= resultLine.doNotShipAfterDate) ||
                (!resultLine.doNotShipAfterDate &&
                    resultLine.doNotShipBeforeDate &&
                    date >= resultLine.doNotShipBeforeDate) ||
                (!resultLine.doNotShipBeforeDate && !resultLine.doNotShipAfterDate),
        );

        return {
            filteredSalesOrderLines,
            isValid: salesOrderLines.length !== filteredSalesOrderLines.length,
        };
    }

    private checkInvalidDeliveryAddress(
        salesOrderLines: ExtractEdgesPartial<SalesOrderLine>[],
        shipmentLinesLength: number,
        shipToAddress: any,
    ): SalesOrderFiltered {
        const filteredSalesOrderLines = salesOrderLines.filter(
            resultLine =>
                (shipmentLinesLength > 0 &&
                    resultLine.shipToAddress &&
                    shipToAddress.name === resultLine.shipToAddress.name &&
                    shipToAddress.addressLine1 === resultLine.shipToAddress.addressLine1 &&
                    shipToAddress.addressLine2 === resultLine.shipToAddress.addressLine2 &&
                    shipToAddress.region === resultLine.shipToAddress.region &&
                    shipToAddress.country._id === resultLine.shipToAddress.country?._id &&
                    shipToAddress.city === resultLine.shipToAddress.city &&
                    shipToAddress.postcode === resultLine.shipToAddress.postcode) ||
                (shipmentLinesLength === 0 &&
                    resultLine.shipToAddress &&
                    this.salesOrderLines.selectedRecords.length === 1 &&
                    shipToAddress.name === resultLine.shipToAddress.name &&
                    shipToAddress.addressLine1 === resultLine.shipToAddress.addressLine1 &&
                    shipToAddress.addressLine2 === resultLine.shipToAddress.addressLine2 &&
                    shipToAddress.region === resultLine.shipToAddress.region &&
                    shipToAddress.country._id === resultLine.shipToAddress.country?._id &&
                    shipToAddress.city === resultLine.shipToAddress.city &&
                    shipToAddress.postcode === resultLine.shipToAddress.postcode) ||
                (shipmentLinesLength === 0 && this.salesOrderLines.selectedRecords.length === 0),
        );

        return {
            filteredSalesOrderLines,
            isValid: salesOrderLines.length !== filteredSalesOrderLines.length,
        };
    }

    private showWarnings(isValidDeliveryDates: boolean, isValidDeliveryAddress: boolean) {
        if (isValidDeliveryDates && isValidDeliveryAddress) {
            this.$.showToast(
                ui.localize(
                    '@sage/xtrem-sales/page__sales_order_table_panel_lines_fail_validation_dates_delivery_address',
                    'If an order line is not in the list, you need to go back to fix any errors in the delivery details and you need to make sure the shipping addresses match.',
                ),
                { type: 'warning' },
            );
        } else if (isValidDeliveryDates) {
            this.$.showToast(
                ui.localize(
                    '@sage/xtrem-sales/page__sales_order_table_panel_lines_fail_validation_dates',
                    'If an order line is not in the list, you need to go back to fix any errors in the delivery details.',
                ),
                { type: 'warning' },
            );
        } else if (isValidDeliveryAddress) {
            this.$.showToast(
                ui.localize(
                    '@sage/xtrem-sales/page__sales_order_table_panel_lines_fail_validation_delivery_address',
                    'If an order line is not in the list, you need to make sure the shipping addresses match.',
                ),
                { type: 'warning' },
            );
        }
    }

    private getLoadSalesOrderLinesFilter(
        shipToCustomer: any,
        shipToCustomerAddress: any,
        billToCustomer: any,
        billToLinkedAddress: any,
        site: any,
        stockSite: any,
        deliveryMode: any,
        incoterm: any,
        currency: any,
        paymentTerm: any,
        shipmentLinesLength: any,
        rowSelected?: any,
    ): Filter<SalesOrderLine> {
        const reqFilter: Filter<SalesOrderLine> = {
            status: { _nin: ['quote', 'closed'] },
            shippingStatus: { _nin: ['shipped'] },
            stockSite: { _id: stockSite._id },
            allocationRequestStatus: { _nin: ['inProgress'] },
        };

        if (site) {
            reqFilter.site = { _id: site._id };
        }
        if (shipToCustomerAddress && shipmentLinesLength > 0) {
            reqFilter.shipToCustomerAddress = { _id: shipToCustomerAddress._id };
        }
        if (deliveryMode && shipmentLinesLength > 0) {
            reqFilter.deliveryMode = { _id: deliveryMode._id };
        }
        if (shipToCustomer) {
            const reqDocFilter: Filter<SalesOrder> = {
                shipToCustomer: { _id: shipToCustomer._id },
                taxCalculationStatus: { _nin: ['failed'] },
            };
            if (incoterm && shipmentLinesLength > 0) {
                reqDocFilter.incoterm = { _id: incoterm._id };
            }
            if (billToCustomer && shipmentLinesLength > 0) {
                reqDocFilter.billToCustomer = { _id: billToCustomer._id };
            }
            if (billToLinkedAddress && shipmentLinesLength > 0) {
                reqDocFilter.billToLinkedAddress = { _id: billToLinkedAddress._id };
            }
            if (currency && shipmentLinesLength > 0) {
                reqDocFilter.currency = { _id: currency._id };
            }
            if (paymentTerm && shipmentLinesLength > 0) {
                reqDocFilter.paymentTerm = { _id: paymentTerm._id };
            }

            reqFilter.document = reqDocFilter;
        }
        if (rowSelected) {
            const reqRowSelectedFilter = this.getLoadSalesOrderLinesSelectedFilter(rowSelected);
            return { ...this.getLoadFilterAvalaraExtension(reqFilter, shipmentLinesLength), ...reqRowSelectedFilter };
        }

        return this.getLoadFilterAvalaraExtension(reqFilter, shipmentLinesLength);
    }

    // To be implemented into the page extension from the avalara-gateway package
    // eslint-disable-next-line @typescript-eslint/no-unused-vars, class-methods-use-this
    getLoadFilterAvalaraExtension(reqFilter: any, shipmentLinesLength: any): any {
        return reqFilter;
    }

    // To be implemented into the page extension from the avalara-gateway package
    // eslint-disable-next-line @typescript-eslint/no-unused-vars, class-methods-use-this
    updateFieldValuesAvalaraExtension(rowItem: any) {}

    // To be implemented into the page extension from the avalara-gateway package
    // eslint-disable-next-line class-methods-use-this
    onLoadAvalaraExtension() {}

    // eslint-disable-next-line class-methods-use-this
    private getLoadSalesOrderLinesSelectedFilter(line: SalesOrderLine): Filter<SalesOrderLine> {
        const reqFilter: Filter<SalesOrderLine> = {};
        if (line) {
            if (line.site) {
                reqFilter.site = { _id: line.site._id };
            }
            if (line.shipToCustomerAddress) {
                reqFilter.shipToCustomerAddress = { _id: line.shipToCustomerAddress._id };
            }
            if (line.deliveryMode) {
                reqFilter.deliveryMode = { _id: line.deliveryMode._id };
            }
            if (line.document.shipToCustomer) {
                const reqDocFilter: Filter<SalesOrder> = {
                    shipToCustomer: { _id: line.document.shipToCustomer._id },
                    taxCalculationStatus: { _nin: ['failed'] },
                };
                if (line.document.incoterm) {
                    reqDocFilter.incoterm = { _id: line.document.incoterm._id };
                }
                if (line.document.billToCustomer) {
                    reqDocFilter.billToCustomer = { _id: line.document.billToCustomer._id };
                }
                if (line.document.billToLinkedAddress) {
                    reqDocFilter.billToLinkedAddress = { _id: line.document.billToLinkedAddress._id };
                }
                if (line.document.currency) {
                    reqDocFilter.currency = { _id: line.document.currency._id };
                }
                if (line.document.paymentTerm) {
                    reqDocFilter.paymentTerm = { _id: line.document.paymentTerm._id };
                }
                reqFilter.document = reqDocFilter;
            }
        }
        return reqFilter;
    }

    private async filterOnRemainingQuantityAndAddRows(result: any) {
        // eslint-disable-next-line no-restricted-syntax
        for (const resultLine of result) {
            const shipmentLinesAlreadyCreated = extractEdges(
                await this.$.graph
                    .node('@sage/xtrem-sales/SalesOrderLineToSalesShipmentLine')
                    .aggregate.query(
                        ui.queryUtils.edgesSelector(
                            {
                                group: {
                                    linkedDocument: {
                                        document: {
                                            number: { _by: 'value' },
                                        },
                                    },
                                },
                                values: {
                                    quantity: {
                                        sum: true,
                                    },
                                },
                            },
                            {
                                filter: { linkedDocument: resultLine._id },
                            },
                        ),
                    )
                    .execute(),
            );

            let remainingShippedQuantity: decimal = 0;
            if (shipmentLinesAlreadyCreated) {
                remainingShippedQuantity =
                    parseFloat(resultLine.quantity) -
                    parseFloat(shipmentLinesAlreadyCreated.map((shipment: any) => shipment.values.quantity.sum)[0]);
                if (remainingShippedQuantity > 0) {
                    resultLine.quantity = remainingShippedQuantity.toString();
                }
            }
            if (resultLine.quantity > 0) {
                this.salesOrderLines.addRecord(resultLine);
            }
        }
    }

    private addOrRemoveRowsFilteredOnAlreadySelectedSalesOrderLinesToShip(shipmentLines: any) {
        this.salesOrderLines.value.forEach((gridLine: ui.PartialCollectionValue<SalesOrderLine>, index: number) => {
            let orderInProgressLineQty = 0;
            (shipmentLines as Dict<any>[])
                .filter(
                    line =>
                        !!line.salesOrderLines &&
                        (line.salesOrderLines as Dict<any>[]).find(elt => elt.linkedDocument === gridLine._id),
                )
                .forEach(shipmentLine =>
                    (shipmentLine.salesOrderLines as Dict<any>[]).forEach(
                        (poLine: Partial<SalesOrderLineToSalesShipmentLine>) => {
                            orderInProgressLineQty += +(poLine.quantity || 0);
                        },
                    ),
                );

            // Use of + prefix to convert from string to number
            if (+(gridLine.quantity || 0) > orderInProgressLineQty) {
                gridLine.quantity = (+(gridLine.quantity || 0) - orderInProgressLineQty).toString();

                this.salesOrderLines.addOrUpdateRecordValue({ ...gridLine, _sortValue: (index + 1) * 10 });
            } else {
                this.salesOrderLines.removeRecord(gridLine._id || '');
            }
        });
    }

    private async confirmation() {
        const validation = await this.$.page.validate();
        if (validation.length === 0) {
            const orderLines = this.salesOrderLines.selectedRecords
                .map(rowId => {
                    const line = this.salesOrderLines.getRecordValue(rowId);
                    return {
                        ...line,
                        date: line?.shippingDate,
                        itemId: line?.item?.id,
                        linkedDocument: {
                            _id: line?._id,
                            document: {
                                _id: line?.document?._id,
                                number: line?.document?.number,
                            },
                            quantity: line?.quantity,
                        },
                    } as ui.PartialCollectionValue<SalesOrderLine>;
                })
                .sort(
                    (l1: ui.PartialCollectionValue<SalesOrderLine>, l2: ui.PartialCollectionValue<SalesOrderLine>) =>
                        (l1._sortValue || 0) - (l2._sortValue || 0),
                );
            const isUnderOrdered = orderLines.some(
                line => +(line.quantity || 0) < +(line.remainingQuantityToShipInSalesUnit || 0),
            );
            let message = '';
            if (isUnderOrdered) {
                message = ui.localize(
                    '@sage/xtrem-sales/pages__sales_order_table_panel__confirm_lower_quantity',
                    'You are about to create one or more order shipment lines with a quantity less than the ordered quantity.',
                );
            }
            if (
                !isUnderOrdered ||
                (await confirmDialogWithAcceptButtonText(
                    this,
                    ui.localize(
                        '@sage/xtrem-sales/pages__sales_order_table_panel__confirm_order_select_confirm_title',
                        'Confirm shipment quantity',
                    ),
                    message,
                    ui.localize('@sage/xtrem-sales/pages-confirm-change', 'Confirm'),
                ))
            ) {
                this.$.finish({
                    lines: orderLines,
                });
            }
        } else {
            this.$.showToast(validation.join('\n'), { type: 'error' });
        }
    }
}
