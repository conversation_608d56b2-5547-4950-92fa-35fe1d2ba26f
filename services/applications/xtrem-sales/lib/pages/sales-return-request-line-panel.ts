import type { ExtractEdgesPartial } from '@sage/xtrem-client';
import * as dimensionPanelHelpers from '@sage/xtrem-finance-data/build/lib/client-functions/dimension-panel-helpers';
import type { Item, UnitOfMeasure } from '@sage/xtrem-master-data-api';
import * as utils from '@sage/xtrem-master-data/build/lib/client-functions/utils';
import type {
    GraphApi,
    SalesOriginDocumentType,
    SalesReturnRequest,
    SalesReturnRequestLine,
    SalesReturnRequestLine as SalesReturnRequestLineNode,
    SalesReturnRequestReason,
    SalesReturnRequestStatus,
} from '@sage/xtrem-sales-api';
import type { Company, Site } from '@sage/xtrem-system-api';
import type { PartialNodeWithId } from '@sage/xtrem-ui';
import * as ui from '@sage/xtrem-ui';
import { merge, noop } from 'lodash';
import * as PillColorSales from '../client-functions/pill-color';

@ui.decorators.page<SalesReturnRequestLinePanel, SalesReturnRequestLineNode>({
    title: 'New sales return request line',
    node: '@sage/xtrem-sales/SalesReturnRequestLine',
    module: 'xtrem-sales',
    mode: 'tabs',
    businessActions() {
        return [this.cancelLine, this.validate];
    },
    onLoad() {
        this.rowId.value = this.$.queryParameters.rowId as string;
        this.document.value = { ...JSON.parse(String(this.$.queryParameters.document)) };
        this.lineData = JSON.parse(this.$.queryParameters.line as string);
        this.setPageValues(this.lineData);
        this.enableDisableValidateActionButton(false);
        this.hideShowStockBlock();
    },
    onDirtyStateUpdated(isDirty: boolean) {
        this.enableDisableValidateActionButton(isDirty);
    },
})
export class SalesReturnRequestLinePanel extends ui.Page<GraphApi> {
    private lineData: SalesReturnRequestLineNode;

    @ui.decorators.pageAction<SalesReturnRequestLinePanel>({
        title: 'OK',
        async onClick() {
            await this.preparePageValues();
        },
    })
    validate: ui.PageAction;

    @ui.decorators.pageAction<SalesReturnRequestLinePanel>({
        title: 'Cancel',
        buttonType: 'tertiary',
        onClick() {
            this.$.setPageClean();
            this.$.finish();
        },
    })
    cancelLine: ui.PageAction;

    @ui.decorators.section<SalesReturnRequestLinePanel>({
        title: 'General',
    })
    generalSection: ui.containers.Section;

    @ui.decorators.block<SalesReturnRequestLinePanel>({
        parent() {
            return this.generalSection;
        },
        title: '',
        isTitleHidden: true,
        isHidden: true,
    })
    hiddenBlock: ui.containers.Block;

    @ui.decorators.block<SalesReturnRequestLinePanel>({
        parent() {
            return this.generalSection;
        },
        title: '',
        isTitleHidden: true,
    })
    generalBlock: ui.containers.Block;

    @ui.decorators.referenceField<SalesReturnRequestLinePanel, SalesReturnRequest>({
        parent() {
            return this.generalBlock;
        },
        node: '@sage/xtrem-sales/SalesReturnRequest',
        title: 'Sales return request',
        lookupDialogTitle: 'Select sales return request',
        valueField: '_id',
        bind: 'document',
        isTransient: true,
        isHidden: true,
    })
    document: ui.fields.Reference;

    @ui.decorators.textField<SalesReturnRequestLinePanel>({
        isHidden: true,
        title: 'ID',
        isTitleHidden: true,
    })
    _id: ui.fields.Text;

    @ui.decorators.textField<SalesReturnRequestLinePanel>({
        isTransient: true,
        title: 'Row ID',
        isTitleHidden: true,
    })
    rowId: ui.fields.Text;

    @ui.decorators.labelField<SalesReturnRequestLinePanel>({
        parent() {
            return this.generalBlock;
        },
        bind: 'status',
        optionType: '@sage/xtrem-sales/SalesReturnRequestStatus',
        width: 'large',
        title: 'Status',
        isTitleHidden: true,
        style() {
            return PillColorSales.getLabelColorByStatus('SalesReturnRequestStatus', this.status.value);
        },
    })
    status: ui.fields.Label<SalesReturnRequestStatus>;

    @ui.decorators.dropdownListField<SalesReturnRequestLinePanel>({
        parent() {
            return this.generalBlock;
        },
        title: 'Origin',
        optionType: '@sage/xtrem-sales/SalesOriginDocumentType',
        isReadOnly: true,
    })
    origin: ui.fields.DropdownList;

    @ui.decorators.separatorField<SalesReturnRequestLinePanel>({
        parent() {
            return this.generalBlock;
        },
        isFullWidth: false,
        isInvisible: true,
    })
    originDocumentTypeSeparator: ui.fields.Separator;

    @ui.decorators.referenceField<SalesReturnRequestLinePanel, Item>({
        parent() {
            return this.generalBlock;
        },
        node: '@sage/xtrem-master-data/Item',
        tunnelPage: '@sage/xtrem-master-data/Item',
        title: 'Item',
        lookupDialogTitle: 'Select item',
        valueField: 'name',
        helperTextField: 'id',
        minLookupCharacters: 3,
        isAutoSelectEnabled: true,
        isMandatory: true,
        columns: [
            ui.nestedFields.text({ bind: 'name' }),
            ui.nestedFields.text({ bind: 'id' }),
            ui.nestedFields.text({ bind: 'description' }),
            ui.nestedFields.text({ title: 'Category', bind: { category: { name: true } } }),
            ui.nestedFields.image({ bind: 'image', isHidden: true }),
            ui.nestedFields.technical({ bind: 'type' }),
            ui.nestedFields.checkbox({ bind: 'isStockManaged', isHidden: true }),
            ui.nestedFields.reference<SalesReturnRequestLinePanel, Item, UnitOfMeasure>({
                bind: 'stockUnit',
                valueField: 'name',
                helperTextField: 'symbol',
                isHidden: true,
                node: '@sage/xtrem-master-data/UnitOfMeasure',
                columns: [
                    ui.nestedFields.text({ bind: 'name' }),
                    ui.nestedFields.text({ bind: 'id' }),
                    ui.nestedFields.text({ bind: 'symbol' }),
                    ui.nestedFields.numeric({ bind: 'decimalDigits' }),
                ],
            }),
            ui.nestedFields.reference<SalesReturnRequestLinePanel, Item, UnitOfMeasure>({
                bind: 'salesUnit',
                valueField: 'name',
                helperTextField: 'symbol',
                isHidden: true,
                node: '@sage/xtrem-master-data/UnitOfMeasure',
                columns: [
                    ui.nestedFields.text({ bind: 'name' }),
                    ui.nestedFields.text({ bind: 'id' }),
                    ui.nestedFields.text({ bind: 'symbol' }),
                    ui.nestedFields.text({ bind: '_id' }),
                    ui.nestedFields.numeric({ bind: 'decimalDigits' }),
                ],
            }),
        ],
        onChange() {
            if (this.item.value) {
                this.itemDescription.value = this.item.value.description ?? '';
                this.unit.value = this.item.value.salesUnit ?? null;
                this.stockUnit.value = this.item.value.stockUnit ?? null;
                this.quantityInStockUnit.postfix = this.item.value.stockUnit?.symbol;
                this.quantityInStockUnit.scale = this.item.value.stockUnit?.decimalDigits || 0;
                this.quantity.postfix = this.item.value.salesUnit?.symbol;
                this.quantity.scale = this.item.value.salesUnit?.decimalDigits || 0;
            }
            this.hideShowStockBlock();
        },
    })
    item: ui.fields.Reference<Item>;

    @ui.decorators.textField<SalesReturnRequestLinePanel>({
        parent() {
            return this.generalBlock;
        },
        title: 'Item ID',
        isReadOnly: true,
        bind: { item: { id: true } },
    })
    itemId: ui.fields.Text;

    @ui.decorators.textField<SalesReturnRequestLinePanel>({
        parent() {
            return this.generalBlock;
        },
        title: 'Item description',
        isMandatory: true,
    })
    itemDescription: ui.fields.Text;

    @ui.decorators.referenceField<SalesReturnRequestLinePanel, SalesReturnRequestReason>({
        parent() {
            return this.generalBlock;
        },
        title: 'Reason',
        lookupDialogTitle: 'Select reason',
        minLookupCharacters: 0,
        bind: 'reason',
        node: '@sage/xtrem-sales/SalesReturnRequestReason',
        tunnelPage: '@sage/xtrem-master-data/ReasonCode',
        valueField: 'name',
        columns: [
            ui.nestedFields.text({ bind: 'name' }),
            ui.nestedFields.text({ bind: 'description', isHidden: true }),
            ui.nestedFields.checkbox({ bind: 'isActive', isHidden: true }),
        ],
    })
    reason: ui.fields.Reference;

    @ui.decorators.referenceField<SalesReturnRequestLinePanel, Site>({
        parent() {
            return this.generalBlock;
        },
        title: 'Origin shipping site',
        lookupDialogTitle: 'Select origin shipping site',
        bind: 'originShippingSite',
        isReadOnly: true,
        isMandatory: true,
        columns: [
            ui.nestedFields.text({ bind: 'name' }),
            ui.nestedFields.text({ bind: 'id' }),
            ui.nestedFields.reference<SalesReturnRequestLinePanel, Site, Company>({
                node: '@sage/xtrem-system/Company',
                tunnelPage: '@sage/xtrem-master-data/Company',
                title: 'Company',
                bind: 'legalCompany',
                valueField: 'name',
                helperTextField: 'id',
                columns: [ui.nestedFields.text({ bind: 'name' }), ui.nestedFields.text({ bind: 'id' })],
            }),
            ui.nestedFields.text({ bind: 'description', isHidden: true }),
            ui.nestedFields.checkbox({ bind: 'isInventory', isHidden: true }),
        ],
    })
    originShippingSite: ui.fields.Reference;

    @ui.decorators.separatorField<SalesReturnRequestLinePanel>({
        parent() {
            return this.generalBlock;
        },
        isFullWidth: true,
        isInvisible: true,
    })
    imageSeparator: ui.fields.Separator;

    @ui.decorators.imageField<SalesReturnRequestLinePanel>({
        parent() {
            return this.generalBlock;
        },
        title: 'Item image',
        isTitleHidden: true,
        size: 'small',
        width: 'small',
        isReadOnly: true,
        bind: { item: { image: true } },
    })
    itemImage: ui.fields.Image;

    @ui.decorators.separatorField<SalesReturnRequestLinePanel>({
        parent() {
            return this.generalBlock;
        },
        isFullWidth: true,
        isInvisible: true,
    })
    sep: ui.fields.Separator;

    @ui.decorators.buttonField<SalesReturnRequestLinePanel>({
        parent() {
            return this.generalBlock;
        },
        isTransient: true,
        map() {
            return ui.localize(
                '@sage/xtrem-sales/pages__sales_shipment_line_panel__dimensions_button_text',
                'Dimensions',
            );
        },
        onError(error) {
            if (!(error instanceof Error) && !(typeof error === 'string')) {
                return noop();
            }
            return utils.formatError(this, error as any);
        },
        async onClick() {
            const { lineData } = this;
            if (!lineData) {
                this.lineData = { status: 'draft' } as SalesReturnRequestLine;
            }

            this.lineData = await dimensionPanelHelpers.editDisplayDimensions(
                this,
                {
                    documentLine: this.lineData,
                },
                {
                    editable: this.lineData.status !== 'closed',
                },
            );
        },
        title: 'Dimensions',
        isTitleHidden: true,
    })
    dimensionsButton: ui.fields.Button;

    @ui.decorators.section<SalesReturnRequestLinePanel>({
        title: 'Quantities',
    })
    quantitySection: ui.containers.Section;

    @ui.decorators.block<SalesReturnRequestLinePanel>({
        parent() {
            return this.quantitySection;
        },
        title: 'Sales',
    })
    salesBlock: ui.containers.Block;

    @ui.decorators.numericField<SalesReturnRequestLinePanel>({
        parent() {
            return this.salesBlock;
        },
        title: 'Quantity',
        isMandatory: true,
        onChange() {
            if (this.quantity.value && this.unitToStockUnitConversionFactor.value) {
                this.quantityInStockUnit.value = Number(
                    +this.quantity.value * +this.unitToStockUnitConversionFactor.value,
                );
            }
        },
        async validation(value) {
            if (this._id.value) {
                await this.$.graph
                    .node('@sage/xtrem-sales/SalesReturnRequest')
                    .mutations.validateQuantityInSalesUnit(true, {
                        salesReturnRequestLine: this._id.value,
                        newQuantityInSalesUnit: value,
                    })
                    .execute();
            }
            return undefined;
        },
    })
    quantity: ui.fields.Numeric;

    @ui.decorators.referenceField<SalesReturnRequestLinePanel, UnitOfMeasure>({
        parent() {
            return this.salesBlock;
        },
        node: '@sage/xtrem-master-data/UnitOfMeasure',
        tunnelPage: '@sage/xtrem-master-data/UnitOfMeasure',
        title: 'Unit',
        lookupDialogTitle: 'Select unit',
        valueField: 'name',
        helperTextField: 'symbol',
        minLookupCharacters: 1,
        isAutoSelectEnabled: true,
        isMandatory: true,
        columns: [
            ui.nestedFields.text({ bind: 'name' }),
            ui.nestedFields.text({ bind: 'id', isHidden: true }),
            ui.nestedFields.text({ bind: 'symbol', title: 'Symbol' }),
            ui.nestedFields.text({ bind: 'description', isHidden: true }),
            ui.nestedFields.numeric({ bind: 'decimalDigits', isHidden: true }),
        ],
        filter() {
            const filterArray: Array<string> = [];
            if (this.unit.value && this.unit.value._id) {
                merge(filterArray, this.unit.value._id);
                if (
                    this.item.value &&
                    this.item.value.salesUnit?._id &&
                    this.item.value.salesUnit?._id !== this.unit.value._id
                ) {
                    merge(filterArray, this.item.value.salesUnit?._id);
                }
            }
            if (this.stockUnit.value && this.stockUnit.value._id) {
                merge(filterArray, this.stockUnit.value._id);
            }
            return {
                _id: { _in: filterArray },
            };
        },
        onChange() {
            this.quantity.scale = this.unit.value?.decimalDigits || 0;
            this.quantity.postfix = this.unit.value?.symbol as string | '';
        },
    })
    unit: ui.fields.Reference<UnitOfMeasure>;

    @ui.decorators.numericField<SalesReturnRequestLinePanel>({
        title: 'Stock unit conversion factor',
        parent() {
            return this.salesBlock;
        },
        isReadOnly: true,
        scale: 10,
    })
    unitToStockUnitConversionFactor: ui.fields.Numeric;

    @ui.decorators.block<SalesReturnRequestLinePanel>({
        parent() {
            return this.quantitySection;
        },
        title: 'Stock',
    })
    stockBlock: ui.containers.Block;

    @ui.decorators.numericField<SalesReturnRequestLinePanel>({
        title: 'Quantity',
        parent() {
            return this.stockBlock;
        },
        isReadOnly: true,
        bind: 'quantityInStockUnit',
    })
    quantityInStockUnit: ui.fields.Numeric;

    @ui.decorators.referenceField<SalesReturnRequestLinePanel, UnitOfMeasure>({
        parent() {
            return this.stockBlock;
        },
        node: '@sage/xtrem-master-data/UnitOfMeasure',
        tunnelPage: '@sage/xtrem-master-data/UnitOfMeasure',
        title: 'Unit',
        lookupDialogTitle: 'Select unit',
        valueField: 'name',
        helperTextField: 'symbol',
        minLookupCharacters: 1,
        isAutoSelectEnabled: true,
        isTransient: true,
        isReadOnly: true,
        isMandatory: true,
        columns: [
            ui.nestedFields.text({ bind: 'name' }),
            ui.nestedFields.text({ bind: 'id', isHidden: true }),
            ui.nestedFields.text({ bind: 'symbol', title: 'Symbol' }),
            ui.nestedFields.text({ bind: 'description', isHidden: true }),
            ui.nestedFields.numeric({ bind: 'decimalDigits', isHidden: true }),
        ],
    })
    stockUnit: ui.fields.Reference<UnitOfMeasure>;

    @ui.decorators.section<SalesReturnRequestLinePanel>({
        title: 'Text',
        isHidden: true,
    })
    textSection: ui.containers.Section;

    @ui.decorators.block<SalesReturnRequestLinePanel>({
        parent() {
            return this.textSection;
        },
        title: '',
        isTitleHidden: true,
    })
    textBlock: ui.containers.Block;

    @ui.decorators.richTextField<SalesReturnRequestLinePanel>({
        parent() {
            return this.textBlock;
        },
        isFullWidth: true,
        isDisabled: true,
        title: 'Text',
        isTitleHidden: true,
    })
    text: ui.fields.RichText;

    private setPageValues(line: SalesReturnRequestLine) {
        if (line) {
            this.$.page.title = ui.localize(
                '@sage/xtrem-sales/pages__sales_return-request_line_panel____update_title',
                'Edit sales return request line',
            );
            this.status.value = line.status;
            this.item.value = line.item as unknown as ExtractEdgesPartial<Item>;
            this.item.isReadOnly = true;
            this.itemDescription.value = line.itemDescription;
            this.quantity.value = +line.quantity;
            this.quantity.scale = line.unit.decimalDigits || 0;
            this.quantity.postfix = line.unit.symbol;
            this.quantityInStockUnit.postfix = line.stockUnit.symbol;
            this.quantityInStockUnit.scale = line.stockUnit.decimalDigits || 0;
            this.unit.isReadOnly = true;
            this.unit.value = line.unit as unknown as ExtractEdgesPartial<UnitOfMeasure>;
            this.stockUnit.value = line.stockUnit as unknown as ExtractEdgesPartial<UnitOfMeasure>;
            this.unitToStockUnitConversionFactor.value = +line.unitToStockUnitConversionFactor;
            this.quantityInStockUnit.value = +line.quantityInStockUnit;
            this.reason.value = line.reason;
            this.originShippingSite.value = line.originShippingSite;

            /**
             * TODO: To be able to retrieve the value from the panel, we need to
             * have the text field here (textStream), but it's not yet implemented
             * by the platform. Tbe implemented soon ...
             */
            // this.text.value = line.text;
        } else {
            this.status.value = 'draft';
            this.quantity.value = 0;

            this.lineData = dimensionPanelHelpers.applyDefaultDimensionsOnNewLineIfNeeded(
                this.lineData as unknown as PartialNodeWithId<SalesReturnRequestLineNode>,
                this.document.value?._defaultDimensionsAttributes,
            ) as unknown as SalesReturnRequestLine;
        }
        if (['approved', 'rejected'].includes(this.document.value?.approvalStatus)) {
            this.reason.isDisabled = true;
        } else {
            this.reason.isDisabled = false;
        }
    }

    async preparePageValues() {
        await new Promise(r => {
            setTimeout(r, 100);
        });
        const validation = await this.$.page.validate();
        if (validation.length === 0) {
            const values: SalesReturnRequestLineNode = {
                ...this.lineData,
                ...this.$.values,
            };
            values._id = this.rowId.value ?? '';
            values.status = (this.status.value as SalesReturnRequestStatus) ?? 'draft';
            values.origin = (this.origin.value as SalesOriginDocumentType) ?? 'direct';
            values.item = this.item.value as unknown as Item;
            values.itemDescription = this.itemDescription.value ?? '';
            values.reason = this.reason.value as SalesReturnRequestReason;
            values.originShippingSite = this.originShippingSite.value as Site;
            values.quantity = this.quantity.value?.toString() ?? '';
            values.quantityInStockUnit = this.quantityInStockUnit.value?.toString() ?? '';
            values.unitToStockUnitConversionFactor = this.unitToStockUnitConversionFactor.value?.toString() ?? '';
            values.unit = this.unit.value as unknown as UnitOfMeasure;
            values.stockUnit = this.stockUnit.value as unknown as UnitOfMeasure;
            /**
             * TODO: To be able to retrieve the value from the panel, we need to
             * have the text field here (textStream), but it's not yet implemented
             * by the platform. Tbe implemented soon ...
             */
            // values.text.value = this.text.value;

            this.lineData = values as unknown as SalesReturnRequestLineNode;
            this.saveLine();
        } else {
            this.$.showToast(validation.join('\n'), { type: 'error' });
        }
    }

    saveLine() {
        this.$.setPageClean();
        this.$.finish({ ...this.lineData });
    }

    enableDisableValidateActionButton(isDirty: boolean) {
        this.validate.isDisabled = !isDirty;
    }

    hideShowStockBlock() {
        if (this.item.value) {
            this.stockBlock.isHidden = this.item.value.type === 'service';
            this.unitToStockUnitConversionFactor.isHidden = this.item.value.type === 'service';
        }
    }
}
