import { featuresSales } from '@sage/xtrem-master-data/build/lib/menu-items/features-sales';
import type { GraphApi, SalesCreditMemoReason as SalesCreditMemoReasonNode } from '@sage/xtrem-sales-api';
import { setApplicativePageCrudActions } from '@sage/xtrem-system/build/lib/client-functions/applicative-crud-actions';
import * as ui from '@sage/xtrem-ui';

@ui.decorators.page<SalesCreditMemoReason, SalesCreditMemoReasonNode>({
    menuItem: featuresSales,
    module: 'sales',
    title: 'Credit memo reason',
    objectTypeSingular: 'Credit memo reason',
    objectTypePlural: 'Credit memo reasons',
    idField() {
        return this.name;
    },
    node: '@sage/xtrem-sales/SalesCreditMemoReason',
    priority: 100,
    areNavigationTabsHidden: true,
    navigationPanel: {
        listItem: {
            title: ui.nestedFields.text({ bind: 'name' }),
            line2: ui.nestedFields.text({ bind: 'description' }),
        },
        optionsMenu: [
            {
                title: 'All',
                graphQLFilter: {},
            },
            {
                title: 'Active',
                graphQLFilter: { isActive: { _eq: true } },
            },
            {
                title: 'Inactive',
                graphQLFilter: { isActive: { _eq: false } },
            },
        ],
    },
    createAction() {
        return this.$standardNewAction;
    },
    headerDropDownActions() {
        return [this.$standardDeleteAction];
    },
    headerQuickActions() {
        return [this.$standardDuplicateAction];
    },
    businessActions() {
        return [this.$standardCancelAction, this.$standardSaveAction];
    },
    onLoad() {
        setApplicativePageCrudActions({
            page: this,
            isDirty: false,
            save: this.$standardSaveAction,
            cancel: this.$standardCancelAction,
            duplicate: this.$standardDuplicateAction,
            remove: this.$standardDeleteAction,
        });
    },

    onDirtyStateUpdated(isDirty: boolean) {
        setApplicativePageCrudActions({
            page: this,
            isDirty,
            save: this.$standardSaveAction,
            cancel: this.$standardCancelAction,
            duplicate: this.$standardDuplicateAction,
            remove: this.$standardDeleteAction,
        });
    },
})
export class SalesCreditMemoReason extends ui.Page<GraphApi> {
    @ui.decorators.section<SalesCreditMemoReason>({
        isTitleHidden: true,
    })
    section: ui.containers.Section;

    @ui.decorators.block<SalesCreditMemoReason>({
        parent() {
            return this.section;
        },
        width: 'large',
    })
    block: ui.containers.Block;

    @ui.decorators.switchField<SalesCreditMemoReason>({
        parent() {
            return this.block;
        },
        fetchesDefaults: true,
    })
    isActive: ui.fields.Switch;

    @ui.decorators.separatorField<SalesCreditMemoReason>({
        parent() {
            return this.block;
        },
        isFullWidth: true,
        isInvisible: true,
    })
    isActiveSeparator: ui.fields.Separator;

    @ui.decorators.textField<SalesCreditMemoReason>({
        parent() {
            return this.block;
        },
        isMandatory: true,
    })
    id: ui.fields.Text;

    @ui.decorators.textField<SalesCreditMemoReason>({
        parent() {
            return this.block;
        },
        isMandatory: true,
    })
    name: ui.fields.Text;

    @ui.decorators.textField<SalesCreditMemoReason>({
        parent() {
            return this.block;
        },
        width: 'large',
    })
    description: ui.fields.Text;
}
