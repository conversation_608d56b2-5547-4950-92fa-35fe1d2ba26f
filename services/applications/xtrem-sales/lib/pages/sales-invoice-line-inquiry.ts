import type { Filter } from '@sage/xtrem-client';
import { extractEdges } from '@sage/xtrem-client';
import type { BusinessEntity, Currency, Customer, Item, UnitOfMeasure } from '@sage/xtrem-master-data-api';
import { setReferenceIfSingleValue } from '@sage/xtrem-master-data/build/lib/client-functions/get-default';
import { formatError } from '@sage/xtrem-master-data/build/lib/client-functions/utils';
import type { GraphApi, SalesCreditMemoLine, SalesInvoiceLine } from '@sage/xtrem-sales-api';
import { SystemError } from '@sage/xtrem-shared';
import type { Country, Legislation } from '@sage/xtrem-structure-api';
import type { Company, Site } from '@sage/xtrem-system-api';
import * as ui from '@sage/xtrem-ui';
import { merge } from 'lodash';
import { fillInquiryDates } from '../client-functions/date-lib';
import type { SalesInvoiceLineInquiryLine } from '../client-functions/interfaces/inquiry';
import { salesInquiries } from '../menu-items/sales-inquiries';

@ui.decorators.page<SalesInvoiceLineInquiry>({
    menuItem: salesInquiries,
    priority: 100,
    title: 'Sales invoice line',
    module: 'sales',
    mode: 'default',
    isTransient: true,
    skipDirtyCheck: true,
    access: { node: '@sage/xtrem-sales/SalesInvoice' },
    onError(error: string | (Error & { errors: Array<any> })) {
        return formatError(this, error);
    },
    async onLoad() {
        this.isCreditMemoIncluded.value = true;
        await setReferenceIfSingleValue([this.company, this.site]);
    },
})
export class SalesInvoiceLineInquiry extends ui.Page<GraphApi> {
    @ui.decorators.section<SalesInvoiceLineInquiry>({
        isOpen: true,
        isTitleHidden: true,
        title: 'General',
    })
    mainSection: ui.containers.Section;

    @ui.decorators.block<SalesInvoiceLineInquiry>({
        parent() {
            return this.mainSection;
        },
        width: 'extra-large',
        title: 'Criteria',
    })
    criteriaBlock: ui.containers.Block;

    @ui.decorators.referenceField<SalesInvoiceLineInquiry, Company>({
        parent() {
            return this.criteriaBlock;
        },
        title: 'Company',
        node: '@sage/xtrem-system/Company',
        tunnelPage: '@sage/xtrem-master-data/Company',
        lookupDialogTitle: 'Select company',
        minLookupCharacters: 0,
        valueField: 'name',
        helperTextField: 'id',
        columns: [
            ui.nestedFields.text({ bind: 'name' }),
            ui.nestedFields.text({ bind: 'id' }),
            ui.nestedFields.technical<SalesInvoiceLineInquiry, Company, Currency>({
                bind: 'currency',
                node: '@sage/xtrem-master-data/Currency',
                nestedFields: [
                    ui.nestedFields.technical({ bind: 'name' }),
                    ui.nestedFields.technical({ bind: 'id' }),
                    ui.nestedFields.technical({ bind: 'symbol' }),
                    ui.nestedFields.technical({ bind: 'decimalDigits' }),
                ],
            }),
        ],
        isMandatory: true,
        placeholder: 'Select company',
        width: 'small',
        filter: { isActive: { _eq: true } },
        onChange() {
            this.site.value = null;
        },
    })
    company: ui.fields.Reference<Company>;

    @ui.decorators.referenceField<SalesInvoiceLineInquiry, Site>({
        parent() {
            return this.criteriaBlock;
        },
        title: 'Site',
        node: '@sage/xtrem-system/Site',
        tunnelPage: '@sage/xtrem-master-data/Site',
        lookupDialogTitle: 'Select site',
        minLookupCharacters: 0,
        valueField: 'name',
        helperTextField: 'id',
        columns: [
            ui.nestedFields.text({ bind: 'name' }),
            ui.nestedFields.text({ bind: 'id' }),
            ui.nestedFields.reference<SalesInvoiceLineInquiry, Site, Company>({
                node: '@sage/xtrem-system/Company',
                title: 'Company',
                bind: 'legalCompany',
                valueField: 'name',
                helperTextField: 'id',
                columns: [
                    ui.nestedFields.text({ bind: 'name' }),
                    ui.nestedFields.text({ bind: 'id' }),
                    ui.nestedFields.technical<SalesInvoiceLineInquiry, Company, Legislation>({
                        node: '@sage/xtrem-structure/Legislation',
                        bind: 'legislation',
                        nestedFields: [
                            ui.nestedFields.technical({ bind: 'name' }),
                            ui.nestedFields.technical({ bind: 'id' }),
                        ],
                    }),
                ],
            }),
        ],
        filter() {
            return {
                isActive: { _eq: true },
                isSales: { _eq: true },
                ...(this.company.value ? { legalCompany: { id: { _eq: this.company.value.id } } } : {}),
            };
        },
        placeholder: 'Select site',
        width: 'small',
        async onChange() {
            if (this.site.value && this.site.value.legalCompany && !this.company.value) {
                this.company.value = this.site.value.legalCompany;
                await this.$.commitValueAndPropertyChanges();
                await this.company.validate();
            }
        },
    })
    site: ui.fields.Reference<Site>;

    @ui.decorators.referenceField<SalesInvoiceLineInquiry, Customer>({
        parent() {
            return this.criteriaBlock;
        },
        node: '@sage/xtrem-master-data/Customer',
        title: 'From bill-to customer',
        lookupDialogTitle: 'Select from bill-to customer',
        minLookupCharacters: 0,
        isAutoSelectEnabled: true,
        shouldSuggestionsIncludeColumns: true,
        columns: [
            ui.nestedFields.text({ bind: { businessEntity: { name: true } }, title: 'Name' }),
            ui.nestedFields.text({ bind: { businessEntity: { id: true } }, title: 'ID' }),
            ui.nestedFields.text({ bind: { businessEntity: { taxIdNumber: true } }, title: 'Tax ID' }),
            ui.nestedFields.technical({ bind: 'isOnHold' }),
            ui.nestedFields.technical<SalesInvoiceLineInquiry, Customer, BusinessEntity>({
                bind: 'businessEntity',
                node: '@sage/xtrem-master-data/BusinessEntity',
                nestedFields: [
                    ui.nestedFields.technical({ bind: 'name' }),
                    ui.nestedFields.technical({ bind: 'id' }),
                    ui.nestedFields.technical<SalesInvoiceLineInquiry, BusinessEntity, Country>({
                        bind: 'country',
                        node: '@sage/xtrem-structure/Country',
                        nestedFields: [ui.nestedFields.text({ bind: 'name' }), ui.nestedFields.text({ bind: 'id' })],
                    }),
                ],
            }),
        ],
        filter() {
            return { isActive: { _eq: true } };
        },
        onChange() {
            if (this.fromBillToCustomer.value?.businessEntity?.name) {
                if (this.toBillToCustomer.value?.businessEntity?.name) {
                    if (
                        this.toBillToCustomer.value.businessEntity.name <
                        this.fromBillToCustomer.value.businessEntity.name
                    ) {
                        this.toBillToCustomer.value = this.fromBillToCustomer.value;
                    }
                } else {
                    this.toBillToCustomer.value = this.fromBillToCustomer.value;
                }
            }
        },
    })
    fromBillToCustomer: ui.fields.Reference<Customer>;

    @ui.decorators.referenceField<SalesInvoiceLineInquiry, Customer>({
        parent() {
            return this.criteriaBlock;
        },
        node: '@sage/xtrem-master-data/Customer',
        title: 'To bill-to customer',
        lookupDialogTitle: 'Select to bill-to customer',
        minLookupCharacters: 0,
        isAutoSelectEnabled: true,
        shouldSuggestionsIncludeColumns: true,
        columns: [
            ui.nestedFields.text({ bind: { businessEntity: { name: true } }, title: 'Name' }),
            ui.nestedFields.text({ bind: { businessEntity: { id: true } }, title: 'ID' }),
            ui.nestedFields.text({ bind: { businessEntity: { taxIdNumber: true } }, title: 'Tax ID' }),
            ui.nestedFields.technical({ bind: 'isOnHold' }),
            ui.nestedFields.technical<SalesInvoiceLineInquiry, Customer, BusinessEntity>({
                bind: 'businessEntity',
                node: '@sage/xtrem-master-data/BusinessEntity',
                nestedFields: [
                    ui.nestedFields.technical({ bind: 'name' }),
                    ui.nestedFields.technical({ bind: 'id' }),
                    ui.nestedFields.technical<SalesInvoiceLineInquiry, BusinessEntity, Country>({
                        bind: 'country',
                        nestedFields: [ui.nestedFields.text({ bind: 'name' }), ui.nestedFields.text({ bind: 'id' })],
                    }),
                ],
            }),
        ],

        filter() {
            if (this.fromBillToCustomer.value?.businessEntity?.name) {
                return { isActive: { _eq: true }, name: { _gte: this.fromBillToCustomer.value?.businessEntity?.name } };
            }
            return {
                isActive: { _eq: true },
            };
        },
    })
    toBillToCustomer: ui.fields.Reference<Customer>;

    @ui.decorators.dateField<SalesInvoiceLineInquiry>({
        parent() {
            return this.criteriaBlock;
        },
        title: 'From date',
        width: 'small',
        onChange() {
            this.toDate.value = fillInquiryDates('to', {
                from: this.fromDate.value || undefined,
                to: this.toDate.value || undefined,
            });
        },
    })
    fromDate: ui.fields.Date;

    @ui.decorators.dateField<SalesInvoiceLineInquiry>({
        parent() {
            return this.criteriaBlock;
        },
        title: 'To date',
        width: 'small',
        onChange() {
            this.fromDate.value = fillInquiryDates('from', {
                from: this.fromDate.value || undefined,
                to: this.toDate.value || undefined,
            });
        },
    })
    toDate: ui.fields.Date;

    @ui.decorators.referenceField<SalesInvoiceLineInquiry, Item>({
        parent() {
            return this.criteriaBlock;
        },
        title: 'From item',
        node: '@sage/xtrem-master-data/Item',
        tunnelPage: '@sage/xtrem-master-data/Item',
        lookupDialogTitle: 'Select from item',
        valueField: 'name',
        helperTextField: 'id',
        columns: [
            ui.nestedFields.text({ bind: 'name' }),
            ui.nestedFields.text({ bind: 'id' }),
            ui.nestedFields.text({ bind: 'description' }),
            ui.nestedFields.text({ title: 'Category', bind: { category: { name: true } } }),
            ui.nestedFields.technical<SalesInvoiceLineInquiry, Item, UnitOfMeasure>({
                bind: 'stockUnit',
                node: '@sage/xtrem-master-data/UnitOfMeasure',
                nestedFields: [
                    ui.nestedFields.technical({ bind: 'name' }),
                    ui.nestedFields.technical({ bind: 'id' }),
                    ui.nestedFields.technical({ bind: 'symbol' }),
                    ui.nestedFields.technical({ bind: 'decimalDigits' }),
                ],
            }),
        ],
        placeholder: 'Select item',
        width: 'small',
        filter() {
            return { isActive: { _eq: true } };
        },
        onChange() {
            if (this.fromItem.value?.name) {
                if (this.toItem.value?.name) {
                    if (this.toItem.value.name < this.fromItem.value.name) {
                        this.toItem.value = this.fromItem.value;
                    }
                } else {
                    this.toItem.value = this.fromItem.value;
                }
            }
        },
    })
    fromItem: ui.fields.Reference<Item>;

    @ui.decorators.referenceField<SalesInvoiceLineInquiry, Item>({
        parent() {
            return this.criteriaBlock;
        },
        title: 'To item',
        node: '@sage/xtrem-master-data/Item',
        tunnelPage: '@sage/xtrem-master-data/Item',
        lookupDialogTitle: 'Select to item',
        valueField: 'name',
        helperTextField: 'id',
        columns: [
            ui.nestedFields.text({ bind: 'name' }),
            ui.nestedFields.text({ bind: 'id' }),
            ui.nestedFields.text({ bind: 'description' }),
            ui.nestedFields.text({ title: 'Category', bind: { category: { name: true } } }),
            ui.nestedFields.technical<SalesInvoiceLineInquiry, Item, UnitOfMeasure>({
                bind: 'stockUnit',
                node: '@sage/xtrem-master-data/UnitOfMeasure',
                nestedFields: [
                    ui.nestedFields.technical({ bind: 'name' }),
                    ui.nestedFields.technical({ bind: 'id' }),
                    ui.nestedFields.technical({ bind: 'symbol' }),
                    ui.nestedFields.technical({ bind: 'decimalDigits' }),
                ],
            }),
        ],
        placeholder: 'Select item',
        width: 'small',
        filter() {
            if (this.fromItem.value) {
                return { isActive: { _eq: true }, name: { _gte: this.fromItem.value.name } };
            }
            return { isActive: { _eq: true } };
        },
    })
    toItem: ui.fields.Reference<Item>;

    @ui.decorators.checkboxField<SalesInvoiceLineInquiry>({
        parent() {
            return this.criteriaBlock;
        },
        title: 'Include sales credit memo',
    })
    isCreditMemoIncluded: ui.fields.Checkbox;

    @ui.decorators.buttonField<SalesInvoiceLineInquiry>({
        parent() {
            return this.criteriaBlock;
        },
        map() {
            return ui.localize('@sage/xtrem-sales/search', 'Search');
        },
        width: 'small',
        async onClick() {
            if (!this.company.value) {
                throw Error(ui.localize('@sage/xtrem-sales/sales-invoice-line-inquiry', 'Enter the company.'));
            }
            this.$.loader.isHidden = false;
            await this.search();
            this.$.loader.isHidden = true;
        },
    })
    searchButton: ui.fields.Button;

    @ui.decorators.tile<SalesInvoiceLineInquiry>({
        parent() {
            return this.mainSection;
        },
    })
    tileContainer: ui.containers.Tile;

    @ui.decorators.aggregateField<SalesInvoiceLineInquiry, SalesInvoiceLineInquiryLine>({
        parent() {
            return this.tileContainer;
        },
        title: 'Number of documents',
        bind: 'lines',
        aggregateOn: 'document',
        aggregationMethod: 'distinctCount',
    })
    totalNumberOfDocument: ui.fields.Aggregate;

    @ui.decorators.aggregateField<SalesInvoiceLineInquiry, SalesInvoiceLineInquiryLine>({
        parent() {
            return this.tileContainer;
        },
        title: 'Number of document lines',
        bind: 'lines',
        aggregateOn: '_id',
        aggregationMethod: 'distinctCount',
    })
    totalNumberOfDocumentLine: ui.fields.Aggregate;

    @ui.decorators.aggregateField<SalesInvoiceLineInquiry, SalesInvoiceLineInquiryLine>({
        parent() {
            return this.tileContainer;
        },
        title: 'Company total amount',
        bind: 'lines',
        aggregateOn: 'amountExcludingTaxInCompanyCurrency',
        aggregationMethod: 'sum',
        prefix() {
            return this.company.value?.currency?.symbol ?? '';
        },
        scale() {
            return this.company.value?.currency?.decimalDigits ?? 2;
        },
    })
    totalAmountInCompanyCurrency: ui.fields.Aggregate;

    @ui.decorators.tableField<SalesInvoiceLineInquiry, SalesInvoiceLineInquiryLine>({
        title: 'Results',
        isReadOnly: true,
        canSelect: false,
        canExport: true,
        isHelperTextHidden: true,
        orderBy: { salesSiteName: +1, customerName: +1, document: +1, date: +1, itemDescription: +1 },
        parent() {
            return this.mainSection;
        },
        columns: [
            ui.nestedFields.text<SalesInvoiceLineInquiry, SalesInvoiceLineInquiryLine>({
                title: 'Sales site',
                bind: 'salesSiteName',
                isHiddenOnMainField: true,
            }),
            ui.nestedFields.text<SalesInvoiceLineInquiry, SalesInvoiceLineInquiryLine>({
                title: 'Customer',
                bind: 'customerName',
            }),
            ui.nestedFields.text<SalesInvoiceLineInquiry, SalesInvoiceLineInquiryLine>({
                title: 'Customer ID',
                bind: 'customerId',
                isHiddenOnMainField: true,
            }),
            ui.nestedFields.select<SalesInvoiceLineInquiry, SalesInvoiceLineInquiryLine>({
                title: 'Document type',
                bind: 'documentType',
                optionType: '@sage/xtrem-finance-data/FinanceDocumentType',
                isHiddenOnMainField: true,
            }),
            ui.nestedFields.text<SalesInvoiceLineInquiry, SalesInvoiceLineInquiryLine>({
                title: 'Document',
                bind: 'document',
            }),
            ui.nestedFields.date<SalesInvoiceLineInquiry, SalesInvoiceLineInquiryLine>({
                title: 'Date',
                bind: 'date',
            }),
            ui.nestedFields.text<SalesInvoiceLineInquiry, SalesInvoiceLineInquiryLine>({
                title: 'Item',
                bind: 'itemName',
            }),
            ui.nestedFields.text<SalesInvoiceLineInquiry, SalesInvoiceLineInquiryLine>({
                title: 'Item ID',
                bind: 'itemId',
            }),
            ui.nestedFields.text<SalesInvoiceLineInquiry, SalesInvoiceLineInquiryLine>({
                title: 'Item description',
                bind: 'itemDescription',
                isHiddenOnMainField: true,
            }),
            ui.nestedFields.numeric<SalesInvoiceLineInquiry, SalesInvoiceLineInquiryLine>({
                title: 'Quantity',
                bind: 'quantity',
                unit: (_rowId, rowData) => rowData?.unit,
                unitMode: 'unitOfMeasure',
                scale: null,
            }),
            ui.nestedFields.reference<SalesInvoiceLineInquiry, SalesInvoiceLineInquiryLine, UnitOfMeasure>({
                title: 'Sales unit',
                bind: 'unit',
                node: '@sage/xtrem-master-data/UnitOfMeasure',
                tunnelPage: '@sage/xtrem-master-data/UnitOfMeasure',
                valueField: 'name',
                helperTextField: 'symbol',
                columns: [
                    ui.nestedFields.text({ bind: 'name' }),
                    ui.nestedFields.technical({ bind: 'id' }),
                    ui.nestedFields.text({ bind: 'symbol', title: 'Symbol' }),
                    ui.nestedFields.technical({ bind: 'decimalDigits' }),
                ],
                isHiddenOnMainField: true,
            }),
            ui.nestedFields.numeric<SalesInvoiceLineInquiry, SalesInvoiceLineInquiryLine>({
                title: 'Net price',
                bind: 'netPrice',
                unit: (_rowId, rowData) => rowData?.currency,
                scale: null,
            }),
            ui.nestedFields.numeric<SalesInvoiceLineInquiry, SalesInvoiceLineInquiryLine>({
                title: 'Amount',
                bind: 'amountExcludingTax',
                unit: (_rowId, rowData) => rowData?.currency,
                scale: null,
            }),
            ui.nestedFields.reference<SalesInvoiceLineInquiry, SalesInvoiceLineInquiryLine, Currency>({
                title: 'Currency',
                bind: 'currency',
                node: '@sage/xtrem-master-data/Currency',
                tunnelPage: '@sage/xtrem-master-data/Currency',
                valueField: 'name',
                helperTextField: 'id',
                columns: [
                    ui.nestedFields.text({ bind: 'name' }),
                    ui.nestedFields.text({ bind: 'id', title: 'ISO 4217' }),
                    ui.nestedFields.text({ bind: 'symbol', title: 'Symbol' }),
                    ui.nestedFields.numeric({ bind: 'decimalDigits', title: 'Decimal digits' }),
                ],
                isHiddenOnMainField: true,
            }),
            ui.nestedFields.numeric<SalesInvoiceLineInquiry, SalesInvoiceLineInquiryLine>({
                title: 'Amount in company currency',
                bind: 'amountExcludingTaxInCompanyCurrency',
                unit: (_rowId, rowData) => rowData?.companyCurrency,
                scale: null,
            }),
            ui.nestedFields.reference<SalesInvoiceLineInquiry, SalesInvoiceLineInquiryLine, Currency>({
                title: 'Company currency',
                bind: 'companyCurrency',
                node: '@sage/xtrem-master-data/Currency',
                tunnelPage: '@sage/xtrem-master-data/Currency',
                valueField: 'name',
                helperTextField: 'id',
                columns: [
                    ui.nestedFields.text({ bind: 'name' }),
                    ui.nestedFields.text({ bind: 'id', title: 'ISO 4217' }),
                    ui.nestedFields.text({ bind: 'symbol', title: 'Symbol' }),
                    ui.nestedFields.numeric({ bind: 'decimalDigits', title: 'Decimal digits' }),
                ],
                isHiddenOnMainField: true,
            }),
            ui.nestedFields.text<SalesInvoiceLineInquiry, SalesInvoiceLineInquiryLine>({
                title: 'Region',
                bind: 'region',
                isHiddenOnMainField: true,
            }),
            ui.nestedFields.text<SalesInvoiceLineInquiry, SalesInvoiceLineInquiryLine>({
                title: 'City',
                bind: 'city',
                isHiddenOnMainField: true,
            }),
            ui.nestedFields.text<SalesInvoiceLineInquiry, SalesInvoiceLineInquiryLine>({
                title: 'Postal code',
                bind: 'postcode',
                isHiddenOnMainField: true,
            }),
        ],
    })
    lines: ui.fields.Table<SalesInvoiceLineInquiryLine>;

    async getSalesInvoiceResults() {
        const filter = this.getSalesFilter() as Filter<SalesInvoiceLine>;
        if (filter?._and) {
            if (this.fromDate.value) {
                filter._and.push({ document: { date: { _gte: this.fromDate.value } } });
            }
            if (this.toDate.value) {
                filter._and.push({ document: { date: { _lte: this.toDate.value } } });
            }
        }

        return extractEdges(
            await this.$.graph
                .node('@sage/xtrem-sales/SalesInvoiceLine')
                .query(
                    ui.queryUtils.edgesSelector(
                        {
                            _id: true,
                            documentNumber: true,
                            documentId: true,
                            site: { _id: true, id: true, name: true, legalCompany: { _id: true } },
                            document: {
                                number: true,
                                date: true,
                                currency: { _id: true, id: true, name: true, decimalDigits: true, symbol: true },
                                billToCustomer: { businessEntity: { id: true, name: true } },
                                companyCurrency: { _id: true, id: true, name: true, decimalDigits: true, symbol: true },
                                billToAddress: { _id: true, city: true, region: true, postcode: true },
                            },
                            itemDescription: true,
                            item: { id: true, name: true },
                            quantity: true,
                            unit: { _id: true, id: true, symbol: true, name: true, decimalDigits: true },
                            netPrice: true,
                            amountExcludingTax: true,
                            amountExcludingTaxInCompanyCurrency: true,
                        },
                        { orderBy: { document: { number: +1 } }, filter, first: 500 },
                    ),
                )
                .execute(),
        ).map(value => ({
            _id: value._id,
            salesSiteName: value.site.name,
            customerId: value.document.billToCustomer.businessEntity.id,
            customerName: value.document.billToCustomer.businessEntity.name,
            documentType: 'salesInvoice',
            document: value.documentNumber,
            date: value.document.date,
            itemId: value.item.id,
            itemDescription: value.itemDescription,
            itemName: value.item.name,
            quantity: value.quantity,
            unit: value.unit,
            netPrice: value.netPrice,
            currency: value.document.currency,
            amountExcludingTax: value.amountExcludingTax,
            companyCurrency: value.document.companyCurrency,
            amountExcludingTaxInCompanyCurrency: value.amountExcludingTaxInCompanyCurrency,
            city: value.document.billToAddress?.city || '',
            region: value.document.billToAddress?.region || '',
            postcode: value.document.billToAddress?.postcode || '',
        }));
    }

    async getSalesCreditMemoResults() {
        const filter = this.getSalesFilter() as Filter<SalesCreditMemoLine>;
        if (filter && this.fromDate.value) {
            merge(filter, { document: { date: { _gte: this.fromDate.value } } });
        }
        if (filter && this.toDate.value) {
            merge(filter, { document: { date: { _lte: this.toDate.value } } });
        }

        return extractEdges(
            await this.$.graph
                .node('@sage/xtrem-sales/SalesCreditMemoLine')
                .query(
                    ui.queryUtils.edgesSelector(
                        {
                            _id: true,
                            documentNumber: true,
                            documentId: true,
                            site: { _id: true, id: true, name: true, legalCompany: { _id: true } },
                            document: {
                                number: true,
                                date: true,
                                currency: { _id: true, id: true, name: true, decimalDigits: true, symbol: true },
                                billToCustomer: { businessEntity: { id: true, name: true } },
                                companyCurrency: { _id: true, id: true, name: true, decimalDigits: true, symbol: true },
                                billToAddress: { _id: true, city: true, region: true, postcode: true },
                            },
                            itemDescription: true,
                            item: { name: true, id: true },
                            quantity: true,
                            unit: { _id: true, id: true, symbol: true, name: true, decimalDigits: true },
                            netPrice: true,
                            amountExcludingTax: true,
                            amountExcludingTaxInCompanyCurrency: true,
                        },
                        { orderBy: { document: { number: +1 } }, filter, first: 500 },
                    ),
                )
                .execute(),
        ).map(value => ({
            _id: value._id,
            salesSiteName: value.site.name,
            customerId: value.document.billToCustomer.businessEntity.id,
            customerName: value.document.billToCustomer.businessEntity.name,
            documentType: 'salesCreditMemo',
            document: value.documentNumber,
            date: value.document.date,
            itemDescription: value.itemDescription,
            itemName: value.item.name,
            itemId: value.item.id,
            quantity: value.quantity,
            unit: value.unit,
            netPrice: value.netPrice,
            currency: value.document.currency,
            amountExcludingTax: value.amountExcludingTax,
            companyCurrency: value.document.companyCurrency,
            amountExcludingTaxInCompanyCurrency: value.amountExcludingTaxInCompanyCurrency,
            city: value.document.billToAddress?.city || '',
            region: value.document.billToAddress?.region || '',
            postcode: value.document.billToAddress?.postcode || '',
        }));
    }

    async search() {
        this.lines.value = [];
        const resultsSalesLineArray = await this.getSalesInvoiceResults();
        if (this.isCreditMemoIncluded.value) {
            const resultsSalesCreditMemoLine = await this.getSalesCreditMemoResults();
            resultsSalesLineArray.push(...resultsSalesCreditMemoLine);
        }

        // HACK: aggregateField doesn't work properly, so values are forced here
        this.totalNumberOfDocument.value = new Set(resultsSalesLineArray.map(line => line.document)).size;
        this.totalNumberOfDocumentLine.value = new Set(
            resultsSalesLineArray.map(line => String(line.document) + String(line._id)),
        ).size;
        this.totalAmountInCompanyCurrency.value = resultsSalesLineArray.reduce(
            (accumulator, line) => accumulator + Number(line.amountExcludingTaxInCompanyCurrency),
            0,
        );

        this.lines.value = resultsSalesLineArray as unknown as ui.PartialNodeWithId<SalesInvoiceLineInquiryLine>[];
        this.$.setPageClean();
        this.$.loader.isHidden = true;
    }

    getSalesFilter(): Filter<SalesInvoiceLine | SalesCreditMemoLine> {
        const filter: Filter<SalesInvoiceLine | SalesCreditMemoLine> = { _and: [{}] };
        if (!filter._and) {
            throw new SystemError('');
        }

        if (this.company.value) {
            filter._and.push({
                site: {
                    ...(this.site.value ? { _id: this.site.value._id } : {}),
                    legalCompany: { _id: this.company.value._id },
                },
            });
        }

        if (this.fromBillToCustomer.value) {
            filter._and.push({
                document: {
                    billToCustomer: {
                        businessEntity: {
                            name: { _gte: this.fromBillToCustomer.value.businessEntity?.name },
                        },
                    },
                },
            });
        }

        if (this.toBillToCustomer.value) {
            filter._and.push({
                document: {
                    billToCustomer: {
                        businessEntity: {
                            name: { _lte: this.toBillToCustomer.value.businessEntity?.name },
                        },
                    },
                },
            });
        }

        if (this.fromItem.value) {
            filter._and.push({ item: { name: { _gte: this.fromItem.value.name } } });
        }

        if (this.toItem.value) {
            filter._and.push({ item: { name: { _lte: this.toItem.value.name } } });
        }

        return filter._and.length ? filter : {};
    }
}
