import type { GraphApi, SalesOrderLine } from '@sage/xtrem-sales-api';
import * as ui from '@sage/xtrem-ui';
import * as PillColorSales from '../client-functions/pill-color';
import { salesInquiries } from '../menu-items/sales-inquiries';

@ui.decorators.page<SalesOrderLineInquiry, SalesOrderLine>({
    menuItem: salesInquiries,
    priority: 50,
    title: 'Sales order line',
    module: 'sales',
    mode: 'default',
    node: '@sage/xtrem-sales/SalesOrderLine',
    access: { node: '@sage/xtrem-sales/SalesOrder', bind: '$read' },
    navigationPanel: {
        onSelect() {
            // We have to prevent selecting a line to avoid opening the "normal" page
            // (which is not even defined).
            return true;
        },
        listItem: {
            // In a nav. panel/main list the title field is mandatory. So, binding the _id was the easiest way to do it.
            title: ui.nestedFields.text({
                bind: '_id',
                isHidden: true,
            }),
            companyName: ui.nestedFields.reference({
                bind: { site: { legalCompany: true } },
                title: 'Company',
                node: '@sage/xtrem-system/Company',
                tunnelPage: '@sage/xtrem-master-data/Company', // Remove line as soon as XT-75278 is fixed.
                valueField: 'name',
            }),
            companyId: ui.nestedFields.text({
                bind: { site: { legalCompany: { id: true } } },
                title: 'Company ID',
                isHiddenOnMainField: true,
            }),
            siteName: ui.nestedFields.reference({
                bind: 'site',
                title: 'Site',
                node: '@sage/xtrem-system/Site',
                tunnelPage: '@sage/xtrem-master-data/Site',
                valueField: 'name',
            }),
            siteId: ui.nestedFields.text({
                bind: { site: { id: true } },
                title: 'Site ID',
                isHiddenOnMainField: true,
            }),
            customerName: ui.nestedFields.reference({
                bind: { document: { soldToCustomer: true } },
                title: 'Sold-to customer',
                tunnelPage: '@sage/xtrem-master-data/Customer',
                valueField: 'name',
            }),
            customerId: ui.nestedFields.text({
                bind: { document: { soldToCustomer: { id: true } } },
                title: 'Sold-to customer ID',
                isHiddenOnMainField: true,
            }),
            salesOrderNumber: ui.nestedFields.reference({
                title: 'Sales order',
                bind: 'document',
                tunnelPage: '@sage/xtrem-sales/SalesOrder',
                valueField: 'number',
            }),
            customerNumber: ui.nestedFields.text({
                bind: { document: { customerNumber: true } },
                title: 'Customer reference',
                isHiddenOnMainField: true,
            }),
            lineStatus: ui.nestedFields.label({
                bind: 'status',
                title: 'Status',
                optionType: '@sage/xtrem-sales/SalesOrderStatus',
                style: (_id, rowData) => PillColorSales.getLabelColorByStatus('SalesOrderStatus', rowData.status),
            }),
            shippingDate: ui.nestedFields.date({ bind: 'shippingDate', title: 'Shipping date' }),
            date: ui.nestedFields.date({ bind: { document: { date: true } }, title: 'Order date' }),
            itemName: ui.nestedFields.reference({
                bind: 'item',
                title: 'Item',
                node: '@sage/xtrem-master-data/Item',
                tunnelPage: '@sage/xtrem-master-data/Item',
                valueField: 'name',
            }),
            itemId: ui.nestedFields.text({
                bind: { item: { id: true } },
                title: 'Item ID',
            }),
            itemDescription: ui.nestedFields.text({
                bind: { item: { description: true } },
                title: 'Item description',
                isHiddenOnMainField: true,
            }),
            quantity: ui.nestedFields.numeric({
                bind: 'quantity',
                title: 'Quantity',
                scale: (_rowId, rowData) => rowData?.unit?.decimalDigits ?? 0,
                postfix: (_rowId, rowData) => rowData?.unit?.symbol ?? '',
                groupAggregationMethod: 'sum',
            }),
            remainingQuantityToShipInSalesUnit: ui.nestedFields.numeric({
                bind: 'remainingQuantityToShipInSalesUnit',
                title: 'Remaining quantity',
                scale: (_rowId, rowData) => rowData?.unit?.decimalDigits ?? 0,
                postfix: (_rowId, rowData) => rowData?.unit?.symbol ?? '',
                groupAggregationMethod: 'sum',
            }),
            unit: ui.nestedFields.reference({
                bind: 'unit',
                title: 'Sales unit',
                node: '@sage/xtrem-master-data/UnitOfMeasure',
                tunnelPage: '@sage/xtrem-master-data/UnitOfMeasure',
                valueField: 'name',
                columns: [
                    ui.nestedFields.technical({ bind: 'symbol' }),
                    ui.nestedFields.technical({ bind: 'decimalDigits' }),
                ],
                isHiddenOnMainField: true,
            }),
            shippingStatus: ui.nestedFields.label({
                bind: 'shippingStatus',
                title: 'Shipping status',
                optionType: '@sage/xtrem-sales/SalesDocumentShippingStatus',
                style: (_id, rowData) =>
                    PillColorSales.getLabelColorByStatus('SalesDocumentShippingStatus', rowData.shippingStatus),
            }),
            invoiceStatus: ui.nestedFields.label({
                bind: 'invoiceStatus',
                title: 'Invoice status',
                optionType: '@sage/xtrem-sales/SalesDocumentInvoiceStatus',
                style: (_id, rowData) =>
                    PillColorSales.getLabelColorByStatus('SalesDocumentInvoiceStatus', rowData.invoiceStatus),
            }),
            currencyName: ui.nestedFields.reference({
                bind: { document: { currency: true } },
                title: 'Currency',
                node: '@sage/xtrem-master-data/Currency',
                tunnelPage: '@sage/xtrem-master-data/Currency',
                isHiddenOnMainField: true,
            }),
            currencyId: ui.nestedFields.text({
                bind: { document: { currency: { id: true } } },
                title: 'Currency ID',
                isHiddenOnMainField: true,
            }),
            companyCurrencyName: ui.nestedFields.reference({
                bind: { document: { companyCurrency: true } },
                title: 'Company currency',
                node: '@sage/xtrem-master-data/Currency',
                tunnelPage: '@sage/xtrem-master-data/Currency',
                isHiddenOnMainField: true,
            }),
            companyCurrencyId: ui.nestedFields.text({
                bind: { document: { companyCurrency: { id: true } } },
                title: 'Company currency ID',
                isHiddenOnMainField: true,
            }),
            netPrice: ui.nestedFields.numeric({
                bind: 'netPrice',
                title: 'Net price',
                unit: (_rowId, rowData) => rowData?.document?.currency,
            }),
            amountExcludingTax: ui.nestedFields.numeric({
                bind: 'amountExcludingTax',
                title: 'Ordered amount',
                unit: (_rowId, rowData) => rowData?.document?.currency,
                groupAggregationMethod: 'sum',
            }),
            amountExcludingTaxInCompanyCurrency: ui.nestedFields.numeric({
                bind: 'amountExcludingTaxInCompanyCurrency',
                title: 'Ordered amount in company currency',
                unit: (_rowId, rowData) => rowData?.document?.companyCurrency,
                groupAggregationMethod: 'sum',
            }),
            remainingAmount: ui.nestedFields.numeric({
                bind: 'remainingAmountToShipExcludingTax',
                title: 'Remaining amount',
                unit: (_rowId, rowData) => rowData?.document?.companyCurrency,
                groupAggregationMethod: 'sum',
                isHiddenOnMainField: true,
            }),
            remainingAmountInCompanyCurrency: ui.nestedFields.numeric({
                bind: 'remainingAmountToShipExcludingTaxInCompanyCurrency',
                title: 'Remaining amount in company currency',
                unit: (_rowId, rowData) => rowData?.document?.companyCurrency,
                groupAggregationMethod: 'sum',
                isHiddenOnMainField: true,
            }),
            grossProfitAmountInCompanyCurrency: ui.nestedFields.numeric({
                title: 'Gross profit amount in company currency',
                bind: 'grossProfitAmountInCompanyCurrency',
                prefix: (_rowId, rowData) => rowData?.document?.companyCurrency?.symbol,
                scale(_rowId, rowData) {
                    return rowData?.document?.companyCurrency?.decimalDigits ?? 2;
                },
                groupAggregationMethod: 'sum',
                isHiddenOnMainField: true,
            }),
        },
        orderBy: {
            document: { site: { id: 1 }, customerNumber: 1, shippingDate: 1 },
            item: { id: 1 },
        },
    },
})
export class SalesOrderLineInquiry extends ui.Page<GraphApi> {}
