import type { Logical } from '@sage/xtrem-client';
import { extractEdges } from '@sage/xtrem-client';
import { DateValue } from '@sage/xtrem-date-time';
import type {
    BusinessEntity,
    BusinessEntityAddress,
    Currency,
    Customer,
    PaymentTerm,
} from '@sage/xtrem-master-data-api';
import { setReferenceIfSingleValue } from '@sage/xtrem-master-data/build/lib/client-functions/get-default';
import { sales } from '@sage/xtrem-master-data/build/lib/menu-items/sales';
import type { GraphApi, SalesShipment } from '@sage/xtrem-sales-api';
import type { Dict } from '@sage/xtrem-shared';
import type { Country } from '@sage/xtrem-structure-api';
import type { Company, Site } from '@sage/xtrem-system-api';
import * as ui from '@sage/xtrem-ui';
import { creationErrorMessages } from '../client-functions/common';
import { getCustomersId } from '../client-functions/sales-mass-process';
import { shipmentInvoicingFilterCriteria } from '../shared-functions/shipment-invoicing-filter-criteria';

interface ResultDict {
    isSelected: boolean;
    quantityToProcess?: number;
    childRows?: Array<string>;
}
@ui.decorators.page<SalesShipmentInvoicingMassProcess>({
    title: 'Mass invoice creation',
    mode: 'default',
    isTransient: true,
    menuItem: sales,
    module: 'sales',
    skipDirtyCheck: true,
    priority: 300,
    access: { node: '@sage/xtrem-sales/SalesInvoice', bind: '$create' },
    businessActions() {
        return [this.chooseShipments, this.cancelDetailedButton, this.createSalesInvoicesFromShipmentLines];
    },
    async onLoad() {
        this.shipmentUntilDate.value = DateValue.today().toString();
        this.invoiceDate.value = DateValue.today().toString();
        if (this.$.queryParameters && this.$.queryParameters.createInvoices === 'Y') {
            const shipments = JSON.parse(this.$.queryParameters.salesShipments as string);
            if (shipments && shipments.length > 0) {
                this.changeMainBlockVisibility(false);
                const filter = {
                    _id: { _in: shipments },
                };
                await this.search(filter, false);
                this.invoiceDate.isDisabled = false;
                this.invoiceDate.focus();
                this.invoiceDate.value = DateValue.today().toString();
                this.selectAllCheckbox.focus();
            }
        } else {
            await setReferenceIfSingleValue([this.salesCompany, this.site]);
        }
        this.customersId = [];
    },
})
export class SalesShipmentInvoicingMassProcess extends ui.Page<GraphApi> {
    customersId: string[];

    private resultsDict: Dict<Dict<ResultDict>>;

    @ui.decorators.section<SalesShipmentInvoicingMassProcess>({
        isOpen: true,
        isTitleHidden: true,
        title: 'General',
    })
    mainSection: ui.containers.Section;

    @ui.decorators.block<SalesShipmentInvoicingMassProcess>({
        parent() {
            return this.mainSection;
        },
        width: 'extra-large',
        title: 'Selection criteria',
    })
    criteriaBlock: ui.containers.Block;

    @ui.decorators.pageAction<SalesShipmentInvoicingMassProcess>({
        title: 'Create',
        isDisabled: true,
        async onClick() {
            const validation = await this.$.page.validate();
            if (validation.length) {
                this.$.showToast(validation.join('\n'), { type: 'error' });
            } else {
                try {
                    this.$.loader.isHidden = false;
                    let response: Dict<any> | null = null;
                    if (!this.mainBlock.isHidden) {
                        const lines: Array<{ salesDocumentLine: string; quantityToProcess: number }> = [];
                        Object.keys(this.resultsDict).forEach((_id: string) => {
                            if (this.resultsDict[_id].level2?.isSelected) {
                                lines.push({
                                    salesDocumentLine: `_id:${_id}`,
                                    quantityToProcess: this.resultsDict[_id].level2.quantityToProcess ?? 0,
                                });
                            }
                        });
                        response = await this.$.graph
                            .node('@sage/xtrem-sales/SalesShipment')
                            .mutations.createSalesInvoicesFromShipmentLines(
                                {
                                    status: true,
                                    numberOfInvoices: true,
                                    lineErrors: { lineNumber: true, linePosition: true, message: true },
                                    errorMessage: { loggerMessage: true, message: true },
                                    financeMessages: true,
                                },
                                { salesDocumentLines: lines, invoiceDate: this.invoiceDate.value ?? '' },
                            )
                            .execute();
                    } else {
                        response = await this.$.graph
                            .node('@sage/xtrem-sales/SalesShipment')
                            .mutations.massCreateSalesInvoices(
                                {
                                    numberOfInvoices: true,
                                    financeMessages: true,
                                    errorMessage: { loggerMessage: true, message: true },
                                },
                                {
                                    site: this.site.value?._id ?? '',
                                    invoiceDate: this.invoiceDate.value ?? '',
                                    shipmentUntilDate: this.shipmentUntilDate.value,
                                    fromBillToCustomer: this.fromBillToCustomer.value?.businessEntity?.name ?? '',
                                    toBillToCustomer: this.toBillToCustomer.value?.businessEntity?.name ?? '',
                                    fromShipment: this.fromShipmentNumber.value?._id ?? null,
                                    toShipment: this.toShipmentNumber.value?._id ?? null,
                                },
                            )
                            .execute();
                    }

                    this.$.loader.isHidden = true;
                    if (response?.numberOfInvoices > 0) {
                        const notificationType =
                            response?.financeMessages?.length || response?.errorMessage?.length ? 'warning' : 'success';
                        this.$.showToast(
                            response?.numberOfInvoices > 1
                                ? `${ui.localize(
                                      '@sage/xtrem-sales/pages__sales_shipment_invoicing_mass_process__creation__success_multi',
                                      '{{num}} sales invoices were created successfully.',
                                      { num: response.numberOfInvoices },
                                  )}\n${creationErrorMessages(response?.errorMessage)}\n${response?.financeMessages}`
                                : `${ui.localize(
                                      '@sage/xtrem-sales/pages__sales_shipment_invoicing_mass_process__creation__success',
                                      '{{num}} sales invoice was created successfully.',
                                      { num: response?.numberOfInvoices },
                                  )}\n${creationErrorMessages(response?.errorMessage)}\n${response?.financeMessages}`,
                            { timeout: 10000, type: notificationType },
                        );
                        this.$.finish();
                        this.$.router.goTo('@sage/xtrem-sales/SalesInvoice/');
                    }
                    this.$.showToast(
                        response?.errorMessage?.length
                            ? creationErrorMessages(response?.errorMessage)
                            : ui.localize(
                                  '@sage/xtrem-sales/pages__sales_shipment_invoicing_mass_process__no_shipment_meet_filtering_criteria',
                                  'No results found.',
                              ),
                        { timeout: 10000, type: response?.errorMessage?.length ? 'error' : 'warning' },
                    );
                    this.$.finish();
                } catch (error) {
                    this.$.loader.isHidden = true;
                    this.$.showToast(error.message, { timeout: 10000, type: 'error' });
                }
                this.$.setPageClean();
            }
        },
        buttonType: 'primary',
    })
    createSalesInvoicesFromShipmentLines: ui.PageAction;

    @ui.decorators.pageAction<SalesShipmentInvoicingMassProcess>({
        title: 'Show advanced selection',
        isDisabled: true,
        async onClick() {
            this.$.loader.isHidden = false;
            this.changeMainBlockVisibility(false);
            await this.search(
                shipmentInvoicingFilterCriteria(
                    this.site.value?._id || '',
                    this.shipmentUntilDate.value,
                    this.fromBillToCustomer.value?.businessEntity?.name,
                    this.toBillToCustomer.value?.businessEntity?.name || '',
                    this.fromShipmentNumber.value?._id,
                    this.toShipmentNumber.value?._id,
                    false,
                ),
                true,
            );
            setTimeout(() => {
                this.selectAllCheckbox.value = true;
                this.selectUnselectCheckboxes();
            }, 100);
            this.$.loader.isHidden = true;
        },
    })
    chooseShipments: ui.PageAction;

    @ui.decorators.pageAction<SalesShipmentInvoicingMassProcess>({
        title: 'Hide advanced selection',
        isHidden: true,
        onClick() {
            this.changeMainBlockVisibility(true);
        },
    })
    cancelDetailedButton: ui.PageAction;

    @ui.decorators.referenceField<SalesShipmentInvoicingMassProcess, Company>({
        parent() {
            return this.criteriaBlock;
        },
        title: 'Company',
        lookupDialogTitle: 'Select company',
        node: '@sage/xtrem-system/Company',
        tunnelPage: '@sage/xtrem-master-data/Company',
        width: 'small',
        columns: [
            ui.nestedFields.text({ bind: 'name' }),
            ui.nestedFields.text({ bind: 'id' }),
            ui.nestedFields.text({ bind: 'description', isHidden: true }),
        ],
        filter() {
            return this.site.value
                ? { sites: { _atLeast: 1, name: this.site.value.name } }
                : { sites: { _atLeast: 1, isSales: true } };
        },
        onChange() {
            this.updateChooseShipmentsStatus();
        },
        isAutoSelectEnabled: true,
        valueField: 'name',
        helperTextField: 'id',
        minLookupCharacters: 0,
        isMandatory: true,
        placeholder: 'Select a company',
    })
    salesCompany: ui.fields.Reference<Company>;

    @ui.decorators.referenceField<SalesShipmentInvoicingMassProcess, Site>({
        parent() {
            return this.criteriaBlock;
        },
        title: 'Site',
        lookupDialogTitle: 'Select site',
        node: '@sage/xtrem-system/Site',
        tunnelPage: '@sage/xtrem-master-data/Site',
        valueField: 'name',
        helperTextField: 'id',
        minLookupCharacters: 0,
        isAutoSelectEnabled: true,
        isMandatory: true,
        columns: [
            ui.nestedFields.text({ bind: 'name' }),
            ui.nestedFields.text({ bind: 'id' }),
            ui.nestedFields.reference<SalesShipmentInvoicingMassProcess, Site, Company>({
                node: '@sage/xtrem-system/Company',
                tunnelPage: '@sage/xtrem-master-data/Company',
                title: 'Company',
                bind: 'legalCompany',
                valueField: 'name',
                helperTextField: 'id',
                columns: [ui.nestedFields.text({ bind: 'name' }), ui.nestedFields.text({ bind: 'id' })],
            }),
            ui.nestedFields.text({ bind: 'description', isHidden: true }),
            ui.nestedFields.checkbox({ bind: 'isInventory', isHidden: true }),
            ui.nestedFields.reference({
                node: '@sage/xtrem-system/Site',
                valueField: '_id',
                bind: 'financialSite',
                isHidden: true,
                columns: [ui.nestedFields.text({ bind: 'id' })],
            }),
            ui.nestedFields.technical({ bind: 'isFinance' }),
        ],
        placeholder: 'Select a site',
        width: 'small',
        async onChange() {
            if (!this.salesCompany.value && this.site.value?.legalCompany) {
                this.salesCompany.value = this.site.value.legalCompany;
            }
            if (this.fromShipmentNumber.value) {
                this.fromShipmentNumber.value = null;
            }

            if (this.toShipmentNumber.value) {
                this.toShipmentNumber.value = null;
            }

            if (this.site.value) {
                this.fromShipmentNumber.isDisabled = false;
                this.toShipmentNumber.isDisabled = false;
            } else {
                this.fromShipmentNumber.isDisabled = true;
                this.toShipmentNumber.isDisabled = true;
            }
            this.updateChooseShipmentsStatus();
            this.customersId = await getCustomersId(this, this.site.value?.legalCompany?._id || null);
        },
        filter() {
            return this.salesCompany.value
                ? { isSales: true, legalCompany: { name: this.salesCompany.value.name } }
                : { isSales: true };
        },
    })
    site: ui.fields.Reference<Site>;

    @ui.decorators.dateField<SalesShipmentInvoicingMassProcess>({
        parent() {
            return this.criteriaBlock;
        },
        title: 'Invoice date',
        isMandatory: true,
        onChange() {
            this.updateChooseShipmentsStatus();
        },
        validation(val) {
            if (Date.parse(val) > Date.now()) {
                return ui.localize(
                    '@sage/xtrem-sales/pages__sales_invoice__invoice_date__cannot__be__future',
                    'The invoice date cannot be later than today.',
                );
            }
            return undefined;
        },
    })
    invoiceDate: ui.fields.Date;

    @ui.decorators.dateField<SalesShipmentInvoicingMassProcess>({
        parent() {
            return this.criteriaBlock;
        },
        title: 'Shipment-until date',
    })
    shipmentUntilDate: ui.fields.Date;

    @ui.decorators.referenceField<SalesShipmentInvoicingMassProcess, Customer>({
        node: '@sage/xtrem-master-data/Customer',
        parent() {
            return this.criteriaBlock;
        },
        title: 'From bill-to customer',
        lookupDialogTitle: 'Select from bill-to customer',
        minLookupCharacters: 0,
        isAutoSelectEnabled: true,
        shouldSuggestionsIncludeColumns: true,
        columns: [
            ui.nestedFields.text({ bind: { businessEntity: { name: true } }, title: 'Name' }),
            ui.nestedFields.text({ bind: { businessEntity: { id: true } }, title: 'ID' }),
            ui.nestedFields.text({ bind: { businessEntity: { taxIdNumber: true } }, title: 'Tax ID' }),
            ui.nestedFields.text({ bind: { businessEntity: { country: { name: true } } }, title: 'Country' }),
            ui.nestedFields.technical<SalesShipmentInvoicingMassProcess, Customer, BusinessEntity>({
                bind: 'businessEntity',
                node: '@sage/xtrem-master-data/BusinessEntity',
                nestedFields: [
                    ui.nestedFields.technical({ bind: 'name' }),
                    ui.nestedFields.technical({ bind: 'id' }),
                    ui.nestedFields.technical<SalesShipmentInvoicingMassProcess, BusinessEntity, Country>({
                        bind: 'country',
                        node: '@sage/xtrem-structure/Country',
                        nestedFields: [
                            ui.nestedFields.technical({ bind: 'name' }),
                            ui.nestedFields.technical({ bind: 'id' }),
                        ],
                    }),
                    ui.nestedFields.technical<SalesShipmentInvoicingMassProcess, BusinessEntity, Currency>({
                        bind: 'currency',
                        node: '@sage/xtrem-master-data/Currency',
                        nestedFields: [
                            ui.nestedFields.technical({ bind: 'name' }),
                            ui.nestedFields.technical({ bind: 'id' }),
                            ui.nestedFields.technical({ bind: 'symbol' }),
                        ],
                    }),
                ],
            }),
            ui.nestedFields.technical({ bind: 'isOnHold' }),
            ui.nestedFields.technical<SalesShipmentInvoicingMassProcess, Customer, PaymentTerm>({
                bind: 'paymentTerm',
                node: '@sage/xtrem-master-data/PaymentTerm',
                nestedFields: [ui.nestedFields.technical({ bind: 'name' })],
            }),
        ],
        filter() {
            return { _id: { _in: this.customersId } };
        },
    })
    fromBillToCustomer: ui.fields.Reference<Customer>;

    @ui.decorators.referenceField<SalesShipmentInvoicingMassProcess, Customer>({
        node: '@sage/xtrem-master-data/Customer',
        parent() {
            return this.criteriaBlock;
        },
        title: 'To bill-to customer',
        lookupDialogTitle: 'Select to bill-to customer',
        minLookupCharacters: 0,
        isAutoSelectEnabled: true,
        shouldSuggestionsIncludeColumns: true,
        columns: [
            ui.nestedFields.text({ bind: { businessEntity: { name: true } }, title: 'Name' }),
            ui.nestedFields.text({ bind: { businessEntity: { id: true } }, title: 'ID' }),
            ui.nestedFields.text({ bind: { businessEntity: { taxIdNumber: true } }, title: 'Tax ID' }),
            ui.nestedFields.technical({ bind: 'isOnHold' }),
            ui.nestedFields.technical<SalesShipmentInvoicingMassProcess, Customer, BusinessEntity>({
                bind: 'businessEntity',
                node: '@sage/xtrem-master-data/BusinessEntity',
                nestedFields: [
                    ui.nestedFields.technical({ bind: 'name' }),
                    ui.nestedFields.technical({ bind: 'id' }),
                    ui.nestedFields.technical<SalesShipmentInvoicingMassProcess, BusinessEntity, Currency>({
                        bind: 'currency',
                        node: '@sage/xtrem-master-data/Currency',
                        nestedFields: [
                            ui.nestedFields.technical({ bind: 'name' }),
                            ui.nestedFields.technical({ bind: 'id' }),
                            ui.nestedFields.technical({ bind: 'symbol' }),
                        ],
                    }),
                ],
            }),
            ui.nestedFields.technical<SalesShipmentInvoicingMassProcess, Customer, PaymentTerm>({
                bind: 'paymentTerm',
                node: '@sage/xtrem-master-data/PaymentTerm',
                nestedFields: [ui.nestedFields.technical({ bind: 'name' })],
            }),
        ],
        filter() {
            return { _id: { _in: this.customersId } };
        },
    })
    toBillToCustomer: ui.fields.Reference<Customer>;

    @ui.decorators.referenceField<SalesShipmentInvoicingMassProcess, SalesShipment>({
        node: '@sage/xtrem-sales/SalesShipment',
        parent() {
            return this.criteriaBlock;
        },
        title: 'From shipment number',
        lookupDialogTitle: 'Select from shipment number',
        valueField: 'number',
        minLookupCharacters: 0,
        isDisabled: true,
        isAutoSelectEnabled: true,
        columns: [
            ui.nestedFields.text({ bind: 'number', title: 'Shipment number' }),
            ui.nestedFields.date({ bind: 'shippingDate', title: 'Shipping date' }),
            ui.nestedFields.reference<SalesShipmentInvoicingMassProcess, SalesShipment, Customer>({
                title: 'Bill-to customer',
                bind: 'billToCustomer',
                tunnelPage: '@sage/xtrem-master-data/Customer',
            }),
        ],
        filter() {
            if (this.site.value) {
                return shipmentInvoicingFilterCriteria(
                    this.site.value?._id || '',
                    this.shipmentUntilDate.value,
                    this.fromBillToCustomer.value?._id,
                    this.toBillToCustomer.value?._id,
                ) as Logical<SalesShipment>;
            }
            return {};
        },
    })
    fromShipmentNumber: ui.fields.Reference;

    @ui.decorators.referenceField<SalesShipmentInvoicingMassProcess, SalesShipment>({
        node: '@sage/xtrem-sales/SalesShipment',
        parent() {
            return this.criteriaBlock;
        },
        title: 'To shipment number',
        lookupDialogTitle: 'Select to shipment number',
        valueField: 'number',
        minLookupCharacters: 0,
        isDisabled: true,
        isAutoSelectEnabled: true,
        columns: [
            ui.nestedFields.text({ bind: 'number', title: 'Shipment number' }),
            ui.nestedFields.date({ bind: 'shippingDate', title: 'Shipping date' }),
            ui.nestedFields.reference<SalesShipmentInvoicingMassProcess, SalesShipment, Customer>({
                title: 'Bill-to customer',
                bind: 'billToCustomer',
                tunnelPage: '@sage/xtrem-master-data/Customer',
            }),
        ],
        filter() {
            if (this.site.value) {
                return shipmentInvoicingFilterCriteria(
                    this.site.value?._id || '',
                    this.shipmentUntilDate.value,
                    this.fromBillToCustomer.value?._id,
                    this.toBillToCustomer.value?._id,
                ) as Logical<SalesShipment>;
            }
            return {};
        },
    })
    toShipmentNumber: ui.fields.Reference;

    @ui.decorators.block<SalesShipmentInvoicingMassProcess>({
        parent() {
            return this.mainSection;
        },
        width: 'extra-large',
        title: 'Advanced selection',
        isHidden: true,
    })
    mainBlock: ui.containers.Block;

    @ui.decorators.checkboxField<SalesShipmentInvoicingMassProcess>({
        parent() {
            return this.mainBlock;
        },
        title: 'Select all',
        onChange() {
            this.selectUnselectCheckboxes();
        },
    })
    selectAllCheckbox: ui.fields.Checkbox;

    @ui.decorators.tableField<SalesShipmentInvoicingMassProcess, SalesShipment>({
        parent() {
            return this.mainSection;
        },
        orderBy: { number: +1, shippingDate: +1 },
        pageSize: 500,
        title: 'Results',
        isTitleHidden: true,
        isHidden: true,
        isChangeIndicatorDisabled: true,
        isTransient: true,
        isReadOnly: true,
        onRowSelected(recordId, rowItem) {
            if (this.results.selectedRecords.length === this.results.value.length) this.selectAllCheckbox.value = true;
            this.createSalesInvoicesFromShipmentLines.isDisabled = false;
            this.onSelectResultsDict(recordId, rowItem);
        },
        onRowUnselected(recordId, rowItem) {
            if (this.results.selectedRecords.length === 0) this.createSalesInvoicesFromShipmentLines.isDisabled = true;
            if (this.selectAllCheckbox.value) this.selectAllCheckbox.value = false;
            this.onSelectResultsDict(recordId, rowItem, false);
        },
        columns: [
            ui.nestedFields.text<SalesShipmentInvoicingMassProcess, SalesShipment>({
                title: 'Shipment number',
                bind: 'number',
            }),
            ui.nestedFields.date({ bind: 'shippingDate', title: 'Shipping date' }),
            ui.nestedFields.reference<SalesShipmentInvoicingMassProcess, SalesShipment, Customer>({
                title: 'Bill-to customer',
                bind: 'billToCustomer',
                tunnelPage: '@sage/xtrem-master-data/Customer',
                isReadOnly: true,
                valueField: { businessEntity: { name: true } },
            }),
            ui.nestedFields.text({
                bind: { billToCustomer: { id: true } },
                title: 'Bill-to customer ID',
                isHiddenOnMainField: true,
            }),
            ui.nestedFields.reference<SalesShipmentInvoicingMassProcess, SalesShipment, BusinessEntityAddress>({
                title: 'Bill-to address',
                bind: 'billToLinkedAddress',
                node: '@sage/xtrem-master-data/BusinessEntityAddress',
                valueField: 'name',
                helperTextField: 'city',
            }),
            ui.nestedFields.reference<SalesShipmentInvoicingMassProcess, SalesShipment, Currency>({
                title: 'Currency',
                bind: 'currency',
                node: '@sage/xtrem-master-data/Currency',
                tunnelPage: '@sage/xtrem-master-data/Currency',
                valueField: 'name',
                helperTextField: 'id',
                columns: [
                    ui.nestedFields.text({ bind: 'name' }),
                    ui.nestedFields.text({ bind: 'id', title: 'ISO 4217 code' }),
                    ui.nestedFields.text({ bind: 'symbol', title: 'Symbol' }),
                    ui.nestedFields.numeric({ bind: 'decimalDigits', isHidden: true }),
                    ui.nestedFields.numeric({ bind: 'rounding', isHidden: true }),
                ],
            }),
            ui.nestedFields.reference<SalesShipmentInvoicingMassProcess, SalesShipment, PaymentTerm>({
                title: 'Payment term',
                bind: 'paymentTerm',
                node: '@sage/xtrem-master-data/PaymentTerm',
                valueField: 'name',
            }),
            ui.nestedFields.checkbox<SalesShipmentInvoicingMassProcess, SalesShipment>({
                title: 'On hold',
                bind: 'isOnHold',
            }),
            ui.nestedFields.numeric<SalesShipmentInvoicingMassProcess, SalesShipment>({
                title: 'Total excluding tax',
                bind: 'totalAmountExcludingTax',
                unit: (_rowId, rowData) => rowData?.currency,
                scale: null,
            }),
        ],
    })
    results: ui.fields.Table<SalesShipment>;

    async search(filter: any, doValidation: boolean) {
        const validation = doValidation ? await this.$.page.validate() : [];

        if (validation.length) {
            this.$.showToast(validation.join('\n'), { type: 'error' });
        } else {
            this.results.unselectAllRecords();
            this.results.value = [];
            this.resultsDict = {};
            this.createSalesInvoicesFromShipmentLines.isDisabled = true;

            this.$.loader.isHidden = false;

            const salesShipments = extractEdges(
                await this.$.graph
                    .node('@sage/xtrem-sales/SalesShipment')
                    .query(
                        ui.queryUtils.edgesSelector(
                            {
                                _id: true,
                                number: true,
                                shippingDate: true,
                                billToCustomer: { businessEntity: { name: true }, id: true },
                                billToLinkedAddress: { name: true },
                                currency: { id: true, decimalDigits: true, rounding: true, symbol: true, name: true },
                                paymentTerm: { name: true },
                                isOnHold: true,
                                totalAmountExcludingTax: true,
                                lines: {
                                    query: {
                                        edges: {
                                            node: {
                                                _id: true,
                                                quantity: true,
                                            },
                                        },
                                    },
                                },
                            },
                            { filter, first: 500 },
                        ),
                    )
                    .execute()
                    .finally(() => {
                        this.$.loader.isHidden = true;
                    }),
            );
            salesShipments.forEach(resultLine => {
                const { lines, ...header } = resultLine;

                this.results.addRecord(header);

                const level1Dict = { level1: { isSelected: false, childRows: [] as Array<string> } };
                this.fillResultsDict(resultLine._id, level1Dict);

                lines.forEach((line: Partial<ui.plugin.Dict<any>>) => {
                    const level2Dict = {
                        level2: { isSelected: false, quantityToProcess: Number(line.quantity) },
                    };
                    this.fillResultsDict(line._id, level2Dict);

                    this.resultsDict[resultLine._id].level1.childRows?.push(line._id);
                });
            });
            this.$.loader.isHidden = true;
        }
    }

    private onSelectResultsDict(recordId: number | string, rowItem: Dict<any>, isSelected = true) {
        if (Object.prototype.hasOwnProperty.call(rowItem, 'number')) {
            this.resultsDict[recordId].level1.childRows?.forEach((rowId: string) => {
                if (
                    isSelected ||
                    rowId === recordId ||
                    (!isSelected && !this.results.selectedRecords.find(id => rowId === id))
                ) {
                    this.resultsDict[rowId].level2.isSelected = isSelected;
                }
            });
            this.resultsDict[recordId].level1.isSelected = isSelected;
        } else {
            this.resultsDict[recordId].level2.isSelected = isSelected;
        }
    }

    private fillResultsDict(_id: string, levelDict: Dict<ResultDict>) {
        this.resultsDict[_id] = Object.prototype.hasOwnProperty.call(this.resultsDict, _id)
            ? { ...this.resultsDict[_id], ...levelDict }
            : levelDict;
    }

    private selectUnselectCheckboxes() {
        if (this.results.value.length > 0) {
            Object.keys(this.resultsDict).forEach((_id: string) => {
                if (this.resultsDict[_id].level1) {
                    if (this.selectAllCheckbox.value) {
                        this.results.selectRecord(_id);
                        this.onSelectResultsDict(_id, { number: null });
                    } else {
                        this.results.unselectRecord(_id);
                        this.onSelectResultsDict(_id, { number: null }, false);
                    }
                }
            });
            this.createSalesInvoicesFromShipmentLines.isDisabled = !this.selectAllCheckbox.value;
        }
    }

    private updateChooseShipmentsStatus() {
        const isDisabled = !this.salesCompany.value || !this.site.value || !this.invoiceDate.value;
        this.chooseShipments.isDisabled = isDisabled;
        this.createSalesInvoicesFromShipmentLines.isDisabled = isDisabled;
    }

    private changeMainBlockVisibility(isHidden: boolean) {
        this.mainBlock.isHidden = isHidden;
        this.results.isHidden = isHidden;
        this.cancelDetailedButton.isHidden = isHidden;
        this.chooseShipments.isHidden = !isHidden;
        this.salesCompany.isDisabled = !isHidden;
        this.site.isDisabled = !isHidden;
        this.invoiceDate.isDisabled = !isHidden;
        this.shipmentUntilDate.isDisabled = !isHidden;
        this.fromBillToCustomer.isDisabled = !isHidden;
        this.toBillToCustomer.isDisabled = !isHidden;
        this.fromShipmentNumber.isDisabled = !isHidden;
        this.toShipmentNumber.isDisabled = !isHidden;
        if (isHidden) {
            this.createSalesInvoicesFromShipmentLines.isDisabled = false;
        }
    }
}
