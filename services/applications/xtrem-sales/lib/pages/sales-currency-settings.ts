import * as ui from '@sage/xtrem-ui';

@ui.decorators.page<SalesCurrencySettings>({
    title: 'Sales currency settings',
    isTransient: true,
    onLoad() {
        if (this.$.queryParameters.salesCurrency) {
            this.salesCurrency.value = JSON.parse(this.$.queryParameters.salesCurrency as string);
        }
    },
    businessActions() {
        return [this.save];
    },
})
export class SalesCurrencySettings extends ui.Page {
    @ui.decorators.section<SalesCurrencySettings>({
        isTitleHidden: true,
    })
    section: ui.containers.Section;

    @ui.decorators.block<SalesCurrencySettings>({
        parent() {
            return this.section;
        },
    })
    fieldBlock: ui.containers.Block;

    @ui.decorators.referenceField<SalesCurrencySettings>({
        parent() {
            return this.fieldBlock;
        },
        node: '@sage/xtrem-master-data/Currency',
        tunnelPage: '@sage/xtrem-master-data/Currency',
        valueField: 'name',
        helperTextField: 'id',
        title: 'Currency for sales values',
        minLookupCharacters: 1,
        isAutoSelectEnabled: true,
        isMandatory: true,
        fetchesDefaults: true,
        columns: [
            ui.nestedFields.text({ bind: 'name' }),
            ui.nestedFields.text({ bind: 'id', title: 'ISO 4217 code' }),
            ui.nestedFields.text({ bind: 'symbol', title: 'Symbol' }),
            ui.nestedFields.numeric({ bind: 'decimalDigits', isHidden: true }),
        ],
    })
    salesCurrency: ui.fields.Reference;

    @ui.decorators.pageAction<SalesCurrencySettings>({
        title: 'Save settings',
        async onClick() {
            const validation = await this.$.page.validate();
            if (validation.length === 0) {
                this.$.finish({
                    salesCurrency: JSON.stringify(this.salesCurrency.value),
                });
            }
        },
    })
    save: ui.PageAction;
}
