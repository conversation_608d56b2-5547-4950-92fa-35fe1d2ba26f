import type { Dict, ExtractEdgesPartial, integer, Logical } from '@sage/xtrem-client';
import { extractEdges } from '@sage/xtrem-client';
import { DateValue } from '@sage/xtrem-date-time';
import { Decimal } from '@sage/xtrem-decimal';
import * as attributesAndDimensions from '@sage/xtrem-finance-data/build/lib/client-functions/attributes-and-dimensions';
import * as dimensionPanelHelpers from '@sage/xtrem-finance-data/build/lib/client-functions/dimension-panel-helpers';
import type * as financeInterfaces from '@sage/xtrem-finance-data/build/lib/client-functions/interfaces/dimension';
import type {
    Address,
    BaseDocumentLine,
    BusinessEntity,
    BusinessEntityAddress,
    Contact,
    Currency,
    Customer,
    CustomerPriceReason,
    DeliveryDetail,
    DeliveryMode,
    Incoterm,
    Item,
    PaymentTerm,
    TaxCalculationStatus,
    UnitOfMeasure,
} from '@sage/xtrem-master-data-api';
import { openItemCustomerPriceList, queryItemSite } from '@sage/xtrem-master-data/build/lib/client-functions/common';
import {
    getCountryPostCodeTitle,
    getCountryRegionTitle,
} from '@sage/xtrem-master-data/build/lib/client-functions/country-fields-title';
import { validCapabilities } from '@sage/xtrem-master-data/build/lib/client-functions/fields-properties';
import * as PillColorCommon from '@sage/xtrem-master-data/build/lib/client-functions/pill-color';
import * as MasterDataUtils from '@sage/xtrem-master-data/build/lib/client-functions/utils';
import { sales } from '@sage/xtrem-master-data/build/lib/menu-items/sales';
import {
    getAddressDetail,
    getConcatenatedAddress,
} from '@sage/xtrem-master-data/build/lib/shared-functions/address-functions';
import { convertAmount, scaleOfCurrent } from '@sage/xtrem-master-data/build/lib/shared-functions/common';
import { checkStockSite } from '@sage/xtrem-master-data/lib/client-functions/page-functions';
import type {
    GraphApi,
    ProformaInvoice,
    SalesDocumentInvoiceStatus,
    SalesInvoice,
    SalesInvoiceLine,
    SalesOrderDisplayStatus,
    SalesOrderLine,
    SalesOrderLineBinding,
    SalesOrder as SalesOrderNode,
    SalesOrderStatus,
    SalesOrderTax,
    SalesPriceOrigin,
    SalesShipment,
    SalesShipmentLine,
} from '@sage/xtrem-sales-api';
import * as PillColorStock from '@sage/xtrem-stock-data/build/lib/client-functions/pill-color';
import * as StockDetailHelper from '@sage/xtrem-stock-data/build/lib/client-functions/stock-details-helper';
import type { Country, Legislation } from '@sage/xtrem-structure-api';
import type { Company, Site } from '@sage/xtrem-system-api';
import {
    setDisplayOfCommonPageActions,
    setOrderOfPageBusinessActions,
    setOrderOfPageHeaderDropDownActions,
    setOrderOfPageHeaderQuickActions,
} from '@sage/xtrem-system/build/lib/client-functions/applicative-crud-actions';
import * as MainListActions from '@sage/xtrem-system/build/lib/client-functions/main-list-actions';
import { TotalTaxCalculator } from '@sage/xtrem-tax/build/lib/client-functions/classes/total-tax-calculator';
import { recalculateTaxCalculationStatus } from '@sage/xtrem-tax/build/lib/client-functions/display-taxes';
import { DocumentTypeEnum } from '@sage/xtrem-tax/build/lib/client-functions/interfaces';
import type { TotalTaxCalculatorState } from '@sage/xtrem-tax/build/lib/client-functions/interfaces/total-tax-calculator';
// TODO: due to XT-60498 we agree to disable this for the moment
// import { resetUiTaxes } from '@sage/xtrem-tax/build/lib/shared-functions/taxes';
import {
    calculateStockCost,
    confirmDialogWithAcceptButtonText,
    isNotWorkDay,
} from '@sage/xtrem-distribution/build/lib/client-functions/common';
import { initDefaultDimensions } from '@sage/xtrem-finance-data/build/lib/client-functions/attributes-and-dimensions';
import type { AllocationRequestStatus, StockAllocationStatus } from '@sage/xtrem-stock-data-api';
import * as ui from '@sage/xtrem-ui';
import { noop } from 'lodash';
import { commonUpdateGrossProfit } from '../client-functions/common';
import * as displayButtons from '../client-functions/display-buttons-sales-order';
import { displayTaxes } from '../client-functions/display-taxes';
import { getSiteAndCustomer } from '../client-functions/finance-integration';
import type { ItemData, SalesOrderStepSequenceStatus } from '../client-functions/interfaces/interfaces';
import {
    calculateLinePrices,
    getLocalizeSalesOrderLineCloseStatusMethodReturn,
    getLocalizeSalesOrderLineOpenStatusMethodReturn,
    getPrices,
    isExchangeRateHidden,
} from '../client-functions/page-functions';
import * as PillColorSales from '../client-functions/pill-color';
import { convertGrossPriceToUnit2, massPriceDeterminationCalculation } from '../client-functions/price-determination';
import { confirmed } from '../client-functions/sales-order';
import * as actionFunctions from '../client-functions/sales-order-actions-functions';
import { deliveryDateValidation, getLocalizeOrderSendAction } from '../client-functions/sales-order-actions-functions';
import * as SalesOrderAllocationAction from '../client-functions/sales-order-allocation-action';
import {
    checkAndCreateOrderAssignment,
    isAssignmentCheckedOnSalesOrderClose,
    isAssignmentCheckedOnSalesOrderDelete,
    isAssignmentCheckedOnSalesOrderLineAllocate,
    isAssignmentCheckedOnSalesOrderLineClose,
    isAssignmentCheckedOnSalesOrderLineDelete,
    isAssignmentCheckedOnSalesOrderLineQuantityChanged,
    isAssignmentCheckedOnSalesOrderShipNotAllocatedQuantity,
    isOrderToOrderServiceOptionActivated,
    openAssignments,
} from '../client-functions/sales-order-assignment';
import * as SalesOrderCommonAction from '../client-functions/sales-order-common-action';
import * as SalesOrderConfirmFunction from '../client-functions/sales-order-confirm-action';
import * as SalesOrderPrintFunction from '../client-functions/sales-order-print-action';
import * as SalesOrderProformaAction from '../client-functions/sales-order-proforma-action';

@ui.decorators.page<SalesOrder, SalesOrderNode>({
    title: 'Sales order',
    objectTypeSingular: 'Sales order',
    objectTypePlural: 'Sales orders',
    idField() {
        return this.number;
    },
    menuItem: sales,
    module: 'sales',
    node: '@sage/xtrem-sales/SalesOrder',
    hasAttachmentsSection: true,
    mode: 'tabs',
    priority: 150,
    headerLabel() {
        return this.displayStatus;
    },
    createAction() {
        return this.$standardNewAction;
    },
    businessActions() {
        return setOrderOfPageBusinessActions<SalesOrder>({
            save: this.save,
            cancel: this.$standardCancelAction,
            businessActions: [this.confirm, this.ship, this.close, this.open],
        });
    },
    headerQuickActions() {
        return setOrderOfPageHeaderQuickActions<SalesOrder>({
            duplicate: [this.$standardDuplicateAction],
            quickActions: [this.print],
        });
    },
    headerDropDownActions() {
        return setOrderOfPageHeaderDropDownActions<SalesOrder>({
            remove: [this.deleteSalesOrder],
            dropDownBusinessActions: [
                this.$standardOpenRecordHistoryAction,
                this.$standardOpenCustomizationPageWizardAction,
                ui.menuSeparator(),
                this.defaultDimension,
                ui.menuSeparator(),
                this.createProformaInvoice,
                this.requestDeallocation,
                this.requestAllocation,
                this.sendEmail,
            ],
        });
    },
    headerSection() {
        return this.headerSection;
    },
    onDirtyStateUpdated(isDirty: boolean) {
        this.orderStepSequence.statuses = this.getDisplayStatusStepSequence();
        this._manageDisplayApplicativePageActions(isDirty);
    },
    async onLoad() {
        await this.initPage();
        this.orderStepSequence.statuses = this.getDisplayStatusStepSequence();
        this._manageDisplayApplicativePageActions(false);
        this.$.setPageClean();
        TotalTaxCalculator.getInstance().initializeTotalTaxData(this.taxDetails, this.taxEngine.value ?? '');
    },
    onError(error: string | (Error & { errors: Array<any> })) {
        this.$.loader.isHidden = true;
        return MasterDataUtils.formatError(this, error);
    },
    navigationPanel: {
        bulkActions: [
            {
                mutation: 'printBulkSalesOrders',
                title: 'Print',
                icon: 'print',
                buttonType: 'primary',
                isDestructive: false,
            },
        ],
        orderBy: { date: -1, number: -1 },
        listItem: {
            title: ui.nestedFields.link({
                bind: 'number',
                title: 'Number',
                isMandatory: true,
                page: '@sage/xtrem-sales/SalesOrder',
                queryParameters(_value, rowData) {
                    return {
                        _id: rowData?._id ?? 0,
                    };
                },
            }),
            line2: ui.nestedFields.reference<SalesOrder, SalesOrderNode>({
                bind: 'soldToCustomer',
                title: 'Sold-to customer',
                groupAggregationMethod: 'distinctCount',
                node: '@sage/xtrem-master-data/Customer',
                tunnelPage: undefined,
                valueField: { businessEntity: { name: true } },
            }),
            soldToCustomerId: ui.nestedFields.text({
                bind: { soldToCustomer: { id: true } },
                title: 'Sold-to customer ID',
                isHiddenOnMainField: true,
            }),
            line2Right: ui.nestedFields.date({
                bind: 'date',
                title: 'Order date',
                isMandatory: true,
            }),
            line_4: ui.nestedFields.reference<SalesOrder, SalesOrderNode>({
                bind: 'site',
                node: '@sage/xtrem-system/Site',
                tunnelPage: undefined,
                valueField: 'name',
                title: 'Site',
                groupAggregationMethod: 'distinctCount',
                columns: [
                    ui.nestedFields.reference<SalesOrder, Site, Company>({
                        node: '@sage/xtrem-system/Company',
                        tunnelPage: '@sage/xtrem-master-data/Company',
                        title: 'Company',
                        bind: 'legalCompany',
                        valueField: 'name',
                        helperTextField: 'id',
                        columns: [ui.nestedFields.technical({ bind: 'customerOnHoldCheck' })],
                    }),
                ],
            }),
            line_5: ui.nestedFields.numeric<SalesOrder, SalesOrderNode>({
                bind: 'totalAmountIncludingTax',
                title: 'Total Including tax',
                unit: (_rowId, rowData) => rowData?.transactionCurrency,
                scale: null,
            }),
            line6: ui.nestedFields.date({ title: 'Delivery date', bind: 'expectedDeliveryDate' }),
            titleRight: ui.nestedFields.label({
                bind: 'displayStatus',
                title: 'Status',
                optionType: '@sage/xtrem-sales/SalesOrderDisplayStatus',
                isMandatory: true,
                style: (_id, rowData) => PillColorCommon.getDisplayStatusPillFeatures(rowData?.displayStatus),
            }),
            line7: ui.nestedFields.text<SalesOrder, SalesOrderNode>({
                bind: 'customerNumber',
                title: 'Customer purchase order number',
                isHiddenOnMainField: true,
            }),
            line8: ui.nestedFields.reference<SalesOrder, SalesOrderNode>({
                bind: 'transactionCurrency',
                node: '@sage/xtrem-master-data/Currency',
                tunnelPage: undefined,
                valueField: 'name',
                title: 'Transaction currency',
                isHiddenOnMainField: true,
                columns: [
                    ui.nestedFields.text({ title: 'Name', bind: 'name' }),
                    ui.nestedFields.technical({ bind: 'id' }),
                    ui.nestedFields.numeric({ title: 'Decimal points', bind: 'decimalDigits', canFilter: false }),
                    ui.nestedFields.numeric({ title: 'Rounding', bind: 'rounding', canFilter: false }),
                    ui.nestedFields.text({ title: 'Symbol', bind: 'symbol', canFilter: false }),
                    ui.nestedFields.technical({ bind: 'isActive' }),
                ],
            }),
            line9: ui.nestedFields.numeric<SalesOrder, SalesOrderNode>({
                bind: 'totalAmountExcludingTax',
                title: 'Total excluding tax',
                groupAggregationMethod: 'sum',
                isHiddenOnMainField: true,
                unit: (_rowId, rowData) => rowData?.transactionCurrency,
                scale: null,
            }),
            line10: ui.nestedFields.numeric<SalesOrder, SalesOrderNode>({
                bind: 'totalGrossProfit',
                title: 'Gross profit',
                isHiddenOnMainField: true,
                unit: (_rowId, rowData) => rowData?.transactionCurrency,
                scale: null,
            }),
            line11: ui.nestedFields.numeric<SalesOrder, SalesOrderNode>({
                bind: 'totalTaxAmount',
                title: 'Tax',
                isHiddenOnMainField: true,
                unit: (_rowId, rowData) => rowData?.transactionCurrency,
                scale: null,
            }),
            line12: ui.nestedFields.icon({
                bind: 'isPrinted',
                title: 'Printed',
                isHiddenOnMainField: true,
                map: (_value, rowData) => (rowData.isPrinted ? 'tick' : 'none'),
            }),
            line13: ui.nestedFields.icon({
                bind: 'isSent',
                title: 'Sent',
                map: (_value, rowData) => (rowData.isSent ? 'tick' : 'none'),
                isHiddenOnMainField: true,
            }),
            line14: ui.nestedFields.reference<SalesOrder, SalesOrderNode>({
                bind: 'shipToCustomer',
                node: '@sage/xtrem-master-data/Customer',
                tunnelPage: undefined,
                valueField: { businessEntity: { name: true } },
                title: 'Ship-to customer',
                isHiddenOnMainField: true,
                groupAggregationMethod: 'distinctCount',
            }),
            shipToCustomerId: ui.nestedFields.text({
                bind: { shipToCustomer: { id: true } },
                title: 'Ship-to customer ID',
                isHiddenOnMainField: true,
            }),
            line15: ui.nestedFields.reference<SalesOrder, SalesOrderNode>({
                bind: 'stockSite',
                node: '@sage/xtrem-system/Site',
                tunnelPage: undefined,
                valueField: 'name',
                title: 'Stock site',
                isHiddenOnMainField: true,
                groupAggregationMethod: 'distinctCount',
            }),
            line16: ui.nestedFields.reference<SalesOrder, SalesOrderNode>({
                bind: 'incoterm',
                title: 'Incoterms® rule',
                isHiddenOnMainField: true,
                tunnelPage: undefined,
            }),
            line17: ui.nestedFields.reference<SalesOrder, SalesOrderNode>({
                bind: 'deliveryMode',
                node: '@sage/xtrem-master-data/DeliveryMode',
                tunnelPage: undefined,
                valueField: 'name',
                title: 'Delivery mode',
                isHiddenOnMainField: true,
            }),
            line18: ui.nestedFields.date({
                title: 'Requested delivery date',
                bind: 'requestedDeliveryDate',
                isHiddenOnMainField: true,
            }),
            line19: ui.nestedFields.numeric({
                bind: 'deliveryLeadTime',
                title: 'Delivery lead time',
                postfix: 'days',
            }),
            line20: ui.nestedFields.date({
                title: 'Shipping date',
                bind: 'shippingDate',
                isHiddenOnMainField: true,
            }),
            line21: ui.nestedFields.date({
                title: 'Do-not-ship-before date',
                bind: 'doNotShipBeforeDate',
                isHiddenOnMainField: true,
            }),
            line22: ui.nestedFields.date({
                title: 'Do-not-ship-after date',
                bind: 'doNotShipAfterDate',
                isHiddenOnMainField: true,
            }),
            line23: ui.nestedFields.reference<SalesOrder, SalesOrderNode>({
                bind: 'billToCustomer',
                node: '@sage/xtrem-master-data/Customer',
                tunnelPage: undefined,
                title: 'Bill-to customer',
                valueField: { businessEntity: { name: true } },
                isHiddenOnMainField: true,
            }),
            billToCustomerId: ui.nestedFields.text({
                bind: { billToCustomer: { id: true } },
                title: 'Bill-to customer ID',
                isHiddenOnMainField: true,
            }),
            line24: ui.nestedFields.reference<SalesOrder, SalesOrderNode>({
                bind: 'paymentTerm',
                isHiddenOnMainField: true,
                tunnelPage: undefined,
            }),
            line25: ui.nestedFields.technical({ bind: 'shippingStatus' }),

            isOnHold: ui.nestedFields.checkbox<SalesOrder, SalesOrderNode>({
                title: 'On hold',
                bind: 'isOnHold',
                isHiddenOnMainField: true,
            }),
            soldToLinkedAddress: ui.nestedFields.technical<SalesOrder, SalesOrderNode, BusinessEntityAddress>({
                bind: 'soldToLinkedAddress',
                node: '@sage/xtrem-master-data/BusinessEntityAddress',
                nestedFields: [ui.nestedFields.technical({ bind: '_id' })],
            }),
            status: ui.nestedFields.technical<SalesOrder, SalesOrderNode>({ bind: 'status' }),
            taxCalculationStatus: ui.nestedFields.technical<SalesOrder, SalesOrderNode>({
                bind: 'taxCalculationStatus',
            }),
            taxEngine: ui.nestedFields.technical<SalesOrder, SalesOrderNode>({ bind: 'taxEngine' }),
            isOrderAssignmentLinked: ui.nestedFields.technical<SalesOrder, SalesOrderNode>({
                bind: 'isOrderAssignmentLinked',
            }),
            isCloseHidden: ui.nestedFields.technical({ bind: 'isCloseHidden' }),
            allocationRequestStatus: ui.nestedFields.technical({ bind: 'allocationRequestStatus' }),
            allocationStatus: ui.nestedFields.technical({ bind: 'allocationStatus' }),
            soldToContact: ui.nestedFields.technical<SalesOrder, SalesOrderNode, Contact>({
                bind: 'soldToContact',
                node: '@sage/xtrem-master-data/Contact',
                nestedFields: [
                    ui.nestedFields.technical({ bind: 'lastName' }),
                    ui.nestedFields.technical({ bind: 'firstName' }),
                    ui.nestedFields.technical({ bind: 'title' }),
                    ui.nestedFields.technical({ bind: 'email' }),
                    ui.nestedFields.technical({ bind: 'preferredName' }),
                    ui.nestedFields.technical({ bind: 'role' }),
                    ui.nestedFields.technical({ bind: 'position' }),
                    ui.nestedFields.technical({ bind: 'locationPhoneNumber' }),
                ],
            }),
        },
        optionsMenu: [
            {
                title: 'All open statuses',
                id: 'allOpenStatuses',
                graphQLFilter: { displayStatus: { _nin: ['closed', 'shipped'] } },
            },
            { title: 'All statuses', id: 'allStatuses', graphQLFilter: {} },
            {
                title: 'Partially shipped',
                id: 'partiallyShipped',
                graphQLFilter: { displayStatus: { _eq: 'partiallyShipped' } },
            },
            { title: 'Quote', id: 'quote', graphQLFilter: { displayStatus: { _eq: 'quote' } } },
            { title: 'Confirmed', id: 'confirmed', graphQLFilter: { displayStatus: { _eq: 'confirmed' } } },
            { title: 'Shipped', id: 'shipped', graphQLFilter: { displayStatus: { _eq: 'shipped' } } },
            { title: 'Closed', id: 'closed', graphQLFilter: { displayStatus: { _eq: 'closed' } } },
            {
                title: 'Tax calculation failed',
                id: 'taxCalculationFailed',
                graphQLFilter: { displayStatus: { _eq: 'taxCalculationFailed' } },
            },
        ],
        inlineActions: [
            {
                title: 'Duplicate',
                icon: 'duplicate',
                id: 'duplicate',
                async onClick(rowId: string) {
                    await this.$standardDuplicateAction.execute(false, rowId);
                },
            },
        ],
        dropdownActions: [
            {
                id: 'confirm',
                title: 'Confirm',
                icon: 'none',
                refreshesMainList: 'list',
                async onClick(recordId: string, rowItem: ui.PartialNodeWithId<SalesOrderNode>) {
                    if (rowItem.number) {
                        await SalesOrderConfirmFunction.confirmSalesOrderFunction({
                            isCalledFromRecordPage: false,
                            pageOrWidget: this,
                            _id: recordId,
                            status: (rowItem.status as SalesOrderStatus) ?? 'closed',
                            number: rowItem.number,
                            isOnHold: rowItem.isOnHold ?? false,
                            customerOnHoldCheck: rowItem?.site?.legalCompany?.customerOnHoldCheck ?? '',
                            taxCalculationStatus: rowItem.taxCalculationStatus,
                        });
                    }
                },
                isHidden(recordId: string, rowItem: ui.PartialNodeWithId<SalesOrderNode>) {
                    return (
                        displayButtons.isHiddenButtonConfirmAction({
                            parameters: { status: rowItem.status },
                            recordId,
                            isDirty: false,
                        }) || false
                    );
                },
            },
            {
                icon: 'none',
                id: 'createShipment',
                title: 'Create shipment',
                refreshesMainList: 'list',
                async onClick(recordId, rowItem: ui.PartialNodeWithId<SalesOrderNode>) {
                    if (await isAssignmentCheckedOnSalesOrderShipNotAllocatedQuantity(this, rowItem._id ?? '')) {
                        await actionFunctions.createShipmentAction({
                            isCalledFromRecordPage: false,
                            salesOrderPage: this,
                            isOnHold: rowItem.isOnHold ?? false,
                            customerOnHoldCheck: rowItem?.site?.legalCompany?.customerOnHoldCheck ?? '',
                            order: recordId,
                            showToast: this.$.showToast,
                        });
                    }
                },
                isHidden(recordId: string, rowItem: ui.PartialNodeWithId<SalesOrderNode>) {
                    return (
                        displayButtons.isHiddenButtonShipAction({
                            parameters: {
                                status: rowItem.status,
                                allocationRequestStatus: rowItem.allocationRequestStatus,
                                taxCalculationStatus: rowItem.taxCalculationStatus,
                            },
                            recordId,
                            isDirty: false,
                        }) || false
                    );
                },
            },
            {
                icon: 'none',
                id: 'closeOrder',
                title: 'Close order',
                refreshesMainList: 'list',
                async onClick(recordId, rowItem: ui.PartialNodeWithId<SalesOrderNode>) {
                    const isAssignmentChecked = await isAssignmentCheckedOnSalesOrderClose({
                        salesOrderPage: this,
                        recordNumber: recordId ?? '',
                        isOrderAssignmentLinked: !!rowItem.isOrderAssignmentLinked,
                    });
                    if (isAssignmentChecked) {
                        await actionFunctions.closeAction({
                            isCalledFromRecordPage: false,
                            salesOrderPage: this,
                            recordNumber: rowItem._id ?? '',
                        });
                    }
                },
                isHidden(_recordId: string, rowItem: ui.PartialNodeWithId<SalesOrderNode>) {
                    return rowItem.isCloseHidden || false;
                },
            },
            {
                icon: 'none',
                id: 'openOrder',
                title: 'Open order',
                refreshesMainList: 'list',
                async onClick(_recordId: string, rowItem: ui.PartialNodeWithId<SalesOrderNode>) {
                    if (rowItem.number) {
                        await actionFunctions.openAction({
                            isCalledFromRecordPage: false,
                            salesOrderPage: this,
                            recordNumber: rowItem.number,
                        });
                    }
                },
                isHidden(recordId: string, rowItem: ui.PartialNodeWithId<SalesOrderNode>) {
                    return (
                        displayButtons.isHiddenButtonOpenAction({
                            parameters: { status: rowItem.status, shippingStatus: rowItem.shippingStatus },
                            recordId,
                            isDirty: false,
                        }) || false
                    );
                },
            },
            ui.menuSeparator(),
            {
                title: 'Print',
                id: 'print',
                icon: 'print',
                refreshesMainList: 'record',
                access: { node: '@sage/xtrem-sales/SalesOrder', bind: 'setIsPrintedTrue' },
                async onClick(recordId: string, rowItem: ui.PartialNodeWithId<SalesOrderNode>) {
                    if (recordId && rowItem.number && rowItem.status) {
                        await SalesOrderPrintFunction.printSalesOrderAction({
                            isCalledFromRecordPage: false,
                            pageOrWidget: this,
                            _id: recordId,
                            status: rowItem.status,
                            number: rowItem.number,
                        });
                    }
                },
                isHidden(recordId: string, rowItem: ui.PartialNodeWithId<SalesOrderNode>) {
                    return displayButtons.isHiddenButtonPrintAction({
                        parameters: {
                            status: rowItem.status,
                        },
                        recordId,
                    });
                },
                isDisabled(recordId: string, rowItem: ui.PartialNodeWithId<SalesOrderNode>) {
                    if (recordId && rowItem) {
                        return (
                            displayButtons.isDisabledButtonPrintAction({
                                parameters: {
                                    status: rowItem.status,
                                },
                                recordId,
                                isDirty: false,
                            }) || false
                        );
                    }
                    return true;
                },
            },
            {
                title: 'Send',
                icon: 'email',
                id: 'sendEmail',
                refreshesMainList: 'list',
                async onClick(_recordId, rowItem) {
                    const localizedResults = getLocalizeOrderSendAction({
                        orderStatus: rowItem.status as SalesOrderStatus,
                        email: rowItem.soldToContact?.email,
                    });
                    await this.$.dialog.page(
                        '@sage/xtrem-master-data/SendEmailPanel',
                        {
                            nodeName: JSON.stringify('@sage/xtrem-sales/SalesOrder'),
                            email: JSON.stringify(rowItem.soldToContact?.email ?? ''),
                            firstName: JSON.stringify(rowItem.soldToContact?.firstName ?? ''),
                            lastName: JSON.stringify(rowItem.soldToContact?.lastName ?? ''),
                            title: JSON.stringify(rowItem.soldToContact?.title ?? ''),
                            soldToLinkedAddressId: JSON.stringify(rowItem.soldToLinkedAddress?._id ?? ''),
                            pageId: JSON.stringify(rowItem._id ?? ''),
                            dialogTitle: JSON.stringify(localizedResults.dialogTitle),
                            confirmTitle: JSON.stringify(localizedResults.confirmTitle),
                            confirmMessage: JSON.stringify(localizedResults.confirmMessage),
                            resultMessage: JSON.stringify(localizedResults.resultMessage),
                        },
                        {
                            resolveOnCancel: true,
                            size: 'extra-large',
                        },
                    );
                },
                isHidden(recordId, rowItem: ui.PartialNodeWithId<SalesOrderNode>) {
                    return displayButtons.isHiddenButtonSendMailAction({
                        parameters: {
                            status: rowItem.status,
                            taxCalculationStatus: rowItem.taxCalculationStatus,
                            shippingStatus: rowItem.shippingStatus,
                            taxEngine: rowItem.taxEngine,
                        },
                        recordId,
                    });
                },
                isDisabled(recordId, rowItem: ui.PartialNodeWithId<SalesOrderNode>) {
                    return (
                        displayButtons.isDisabledButtonSendMailAction({
                            parameters: {
                                status: rowItem.status,
                            },
                            recordId,
                            isDirty: false,
                        }) || false
                    );
                },
            },
            {
                icon: 'three_boxes',
                id: 'requestAllocation',
                title: 'Allocate stock',
                async onClick(rowId) {
                    await SalesOrderAllocationAction.allocationAction({
                        pageOrWidget: this,
                        _id: rowId,
                        allocationType: 'allocation',
                    });
                },
                isHidden(recordId, rowItem: ui.PartialNodeWithId<SalesOrderNode>) {
                    return displayButtons.isHiddenButtonRequestAllocationAction({
                        parameters: {
                            allocationRequestStatus: rowItem.allocationRequestStatus,
                            allocationStatus: rowItem.allocationStatus,
                            status: rowItem.status,
                        },
                        recordId,
                    });
                },
                isDisabled(recordId, rowItem: ui.PartialNodeWithId<SalesOrderNode>) {
                    return (
                        displayButtons.isDisabledButtonRequestAllocationAction({
                            parameters: {
                                allocationRequestStatus: rowItem.allocationRequestStatus,
                                allocationStatus: rowItem.allocationStatus,
                                status: rowItem.status,
                            },
                            recordId,
                            isDirty: false,
                        }) || false
                    );
                },
            },
            {
                icon: 'three_boxes',
                title: 'Deallocate stock',
                id: 'requestDeallocation',
                async onClick(rowId) {
                    await SalesOrderAllocationAction.allocationAction({
                        pageOrWidget: this,
                        _id: rowId,
                        allocationType: 'deallocation',
                    });
                },
                isHidden(recordId, rowItem: ui.PartialNodeWithId<SalesOrderNode>) {
                    return displayButtons.isHiddenButtonRequestDeallocationAction({
                        parameters: {
                            allocationRequestStatus: rowItem.allocationRequestStatus,
                            allocationStatus: rowItem.allocationStatus,
                            status: rowItem.status,
                        },
                        recordId,
                    });
                },
                isDisabled(recordId, rowItem: ui.PartialNodeWithId<SalesOrderNode>) {
                    return (
                        displayButtons.isDisabledButtonRequestDeallocationAction({
                            parameters: {
                                allocationRequestStatus: rowItem.allocationRequestStatus,
                                allocationStatus: rowItem.allocationStatus,
                                status: rowItem.status,
                            },
                            recordId,
                            isDirty: false,
                        }) || false
                    );
                },
            },
            {
                title: 'Create proforma invoice',
                id: 'createProformaInvoice',
                icon: 'add',
                async onClick(_id, salesOrder) {
                    await SalesOrderProformaAction.proformaInvoiceDialog({
                        pageOrWidget: this,
                        option: 'create',
                        dialogParameters: { salesOrder },
                    });
                },
                isHidden(_id, order) {
                    return order.displayStatus !== confirmed;
                },
            },
            ui.menuSeparator(),
            {
                icon: 'none',
                title: 'Set dimensions',
                id: 'setDimension',
                async onClick(_rowId, rowItem: ui.PartialNodeWithId<SalesOrderNode>) {
                    if (rowItem.status !== 'closed') {
                        const { site, customer } = await getSiteAndCustomer({
                            page: this,
                            siteId: rowItem.site?._id ?? '',
                            customerId: rowItem.soldToCustomer?._id ?? '',
                        });
                        await actionFunctions.setDimensions({
                            salesOrderPage: this,
                            recordNumber: rowItem.number ?? '',
                            status: rowItem.status ?? null,
                            site,
                            customer,
                        });
                    }
                },
                isHidden(recordId, rowItem: ui.PartialNodeWithId<SalesOrderNode>) {
                    return displayButtons.isHiddenButtonDefaultDimensionAction({
                        parameters: { status: rowItem.status, shippingStatus: rowItem.shippingStatus },
                        recordId,
                    });
                },
            },
            ui.menuSeparator(),
            {
                title: 'Delete',
                id: 'delete',
                icon: 'bin',
                refreshesMainList: 'list',
                isDestructive: true,
                isHidden(recordId, rowItem: ui.PartialNodeWithId<SalesOrderNode>) {
                    return displayButtons.isHiddenButtonDeleteAction({
                        parameters: {
                            status: rowItem.status,
                            taxCalculationStatus: rowItem.taxCalculationStatus,
                            displayStatus: rowItem.displayStatus,
                        },
                        recordId,
                    });
                },
                async onClick(recordId: string, rowItem: ui.PartialNodeWithId<SalesOrderNode>) {
                    if (
                        await actionFunctions.isSalesOrderAssignmentCheckDelete(
                            this,
                            rowItem.isOrderAssignmentLinked || false,
                        )
                    ) {
                        await MainListActions.deleteRecord<GraphApi>(this, {
                            _id: rowItem._id,
                            nodeName: '@sage/xtrem-sales/SalesOrder',
                        });
                    }
                },
            },
        ],
    },
})
export class SalesOrder
    extends ui.Page<GraphApi, SalesOrderNode>
    implements financeInterfaces.PageWithDefaultDimensions
{
    _defaultDimensionsAttributes: financeInterfaces.DefaultDimensions;

    displayTaxesClicked = false;

    protected _shipToAddressDetailsCounter = -4;

    confirmDialogResponseOnShipToAddressUpdate: boolean;

    initLinesLength: number;

    currentSelectedLineId: string;

    additionalItemId = '0';

    oldSalesUnit?: string;

    itemData: ItemData = { item: {}, itemCustomer: {} };

    minimumSalesQuantity: number;

    maximumSalesQuantity: number;

    allocationFields: ui.SidebarFieldDefinition<SalesOrderLineBinding>[];

    private readonly orderStepSequenceCreate = ui.localize(
        '@sage/xtrem-sales/pages__sales_order__step_sequence_creation',
        'Create',
    );

    private readonly orderStepSequenceConfirm = ui.localize(
        '@sage/xtrem-sales/pages__sales_order__step_sequence_confirm',
        'Confirm',
    );

    private readonly orderStepSequenceShip = ui.localize(
        '@sage/xtrem-sales/pages__sales_order__step_sequence_ship',
        'Ship',
    );

    private readonly orderStepSequenceInvoice = ui.localize(
        '@sage/xtrem-sales/pages__sales_order__step_sequence_invoice',
        'Invoice',
    );

    getDisplayStatusStepSequence(): Dict<ui.StepSequenceStatus> {
        if (this.status.value === 'quote') {
            return this._setStepSequenceStatusObject({
                create: 'current',
                confirm: 'incomplete',
                ship: 'incomplete',
                invoice: 'incomplete',
            });
        }
        if (this.status.value === 'pending') {
            return this._setStepSequenceStatusObject({
                create: 'complete',
                confirm: 'complete',
                ship: 'incomplete',
                invoice: 'incomplete',
            });
        }
        if (this.invoiceStatus.value === 'invoiced') {
            return this._setStepSequenceStatusObject({
                create: 'complete',
                confirm: 'complete',
                ship: 'complete',
                invoice: 'complete',
            });
        }
        if (this.invoiceStatus.value === 'partiallyInvoiced') {
            if (this.shippingStatus.value === 'partiallyShipped') {
                return this._setStepSequenceStatusObject({
                    create: 'complete',
                    confirm: 'complete',
                    ship: 'current',
                    invoice: 'current',
                });
            }
            return this._setStepSequenceStatusObject({
                create: 'complete',
                confirm: 'complete',
                ship: 'complete',
                invoice: 'current',
            });
        }
        if (this.shippingStatus.value === 'shipped') {
            return this._setStepSequenceStatusObject({
                create: 'complete',
                confirm: 'complete',
                ship: 'complete',
                invoice: 'incomplete',
            });
        }
        if (this.shippingStatus.value === 'partiallyShipped') {
            return this._setStepSequenceStatusObject({
                create: 'complete',
                confirm: 'complete',
                ship: 'current',
                invoice: 'incomplete',
            });
        }
        if (this.status.value === 'closed') {
            if (this.isQuote.value === true) {
                return this._setStepSequenceStatusObject({
                    create: 'current',
                    confirm: 'incomplete',
                    ship: 'incomplete',
                    invoice: 'incomplete',
                });
            }
            return this._setStepSequenceStatusObject({
                create: 'complete',
                confirm: 'complete',
                ship: 'incomplete',
                invoice: 'incomplete',
            });
        }
        if (this.status.value !== 'quote') {
            return this._setStepSequenceStatusObject({
                create: 'complete',
                confirm: 'current',
                ship: 'incomplete',
                invoice: 'incomplete',
            });
        }
        return this._setStepSequenceStatusObject({
            create: 'current',
            confirm: 'incomplete',
            ship: 'incomplete',
            invoice: 'incomplete',
        });
    }

    private _manageDisplayApplicativePageActions(isDirty = false) {
        setDisplayOfCommonPageActions({
            page: this,
            isDirty,
            save: this.save,
            cancel: this.$standardCancelAction,
            duplicate: this.$standardDuplicateAction,
            remove: this.deleteSalesOrder,
            businessActions: [
                this.$standardOpenCustomizationPageWizardAction,
                this.confirm,
                this.ship,
                this.close,
                this.open,
            ],
        });
        this._manageDisplayAdditionalPageActions(isDirty);
    }

    private _manageDisplayAdditionalPageActions(isDirty = false) {
        this.manageDisplayButtonCRUDActions(isDirty);
        this.manageDisplayButtonAllOtherActions(isDirty);
    }

    private manageDisplayButtonCRUDActions(isDirty = false) {
        this.save.isDisabled = displayButtons.isDisabledButtonSaveAction({
            parameters: { status: this.status.value },
            recordId: this.$.recordId,
            isDirty,
        });
        this.$standardCancelAction.isDisabled = displayButtons.isDisabledButtonCancelAction({
            parameters: { status: this.status.value },
            recordId: this.$.recordId,
            isDirty,
        });
        this.deleteSalesOrder.isDisabled = displayButtons.isDisabledButtonDeleteAction({
            parameters: { status: this.status.value },
            recordId: this.$.recordId,
        });
        this.deleteSalesOrder.isHidden = displayButtons.isHiddenButtonDeleteAction({
            parameters: {
                status: this.status.value,
                taxCalculationStatus: this.taxCalculationStatus.value,
                displayStatus: this.displayStatus.value,
            },
            recordId: this.$.recordId,
        });
    }

    private manageDisplayButtonAllOtherActions(isDirty = false) {
        // footer business actions
        this.manageDisplayButtonConfirmAction(isDirty);
        this.manageDisplayButtonOpenAction(isDirty);
        this.manageDisplayButtonCloseAction(isDirty);
        this.manageDisplayButtonShipAction(isDirty);
        // other header actions
        this.manageDisplayButtonRequestAllocationAction(isDirty);
        this.manageDisplayButtonRequestDeallocationAction(isDirty);
        this.manageDisplayButtonDefaultDimensionAction();
        this.manageDisplayButtonPrintAction(isDirty);
        this.manageDisplayButtonSendMailAction(isDirty);
        this.manageDisplayLinePhantomRow();
        this.manageDisplayCreateProformaInvoice();
    }

    private manageDisplayCreateProformaInvoice() {
        this.createProformaInvoice.isHidden = this.displayStatus.value !== confirmed;
    }

    private manageDisplayButtonConfirmAction(isDirty: boolean) {
        this.confirm.isHidden = displayButtons.isHiddenButtonConfirmAction({
            parameters: { status: this.status.value },
            recordId: this.$.recordId,
            isDirty,
        });
    }

    private manageDisplayButtonShipAction(isDirty: boolean) {
        this.ship.isHidden = displayButtons.isHiddenButtonShipAction({
            parameters: {
                status: this.status.value,
                allocationRequestStatus: this.allocationRequestStatus.value,
                taxCalculationStatus: this.taxCalculationStatus.value,
            },
            recordId: this.$.recordId,
            isDirty,
        });
    }

    private manageDisplayButtonCloseAction(isDirty: boolean) {
        if (isDirty || !this.$.recordId) {
            this.close.isHidden = true;
        } else {
            this.close.isHidden = this.isCloseHidden.value === true ? this.isCloseHidden.value : false;
        }
    }

    private manageDisplayButtonOpenAction(isDirty: boolean) {
        this.open.isHidden = displayButtons.isHiddenButtonOpenAction({
            parameters: { status: this.status.value, shippingStatus: this.shippingStatus.value },
            recordId: this.$.recordId,
            isDirty,
        });
    }

    private manageDisplayLinePhantomRow() {
        this.lines.isPhantomRowDisabled = displayButtons.isDisabledLinePhantomRow({
            parameters: {
                status: this.status.value,
                site: this.site.value,
                stockSite: this.stockSite.value,
                soldToCustomer: this.soldToCustomer.value,
                date: this.date.value,
                currency: this.currency.value,
            },
        });
    }

    private manageDisplayButtonPrintAction(isDirty: boolean) {
        this.print.isHidden = displayButtons.isHiddenButtonPrintAction({
            parameters: {
                status: this.status.value,
            },
            recordId: this.$.recordId,
        });

        this.print.isDisabled = displayButtons.isDisabledButtonPrintAction({
            parameters: {
                status: this.status.value,
            },
            recordId: this.$.recordId,
            isDirty,
        });
    }

    private manageDisplayButtonSendMailAction(isDirty: boolean) {
        this.sendEmail.isHidden = displayButtons.isHiddenButtonSendMailAction({
            parameters: {
                status: this.status.value,
                taxCalculationStatus: this.taxCalculationStatus.value,
                shippingStatus: this.shippingStatus.value,
                taxEngine: this.taxEngine.value,
            },
            recordId: this.$.recordId,
        });
        this.sendEmail.isDisabled = displayButtons.isDisabledButtonSendMailAction({
            parameters: {
                status: this.status.value,
            },
            recordId: this.$.recordId,
            isDirty,
        });
    }

    private manageDisplayButtonDefaultDimensionAction() {
        this.defaultDimension.isHidden = displayButtons.isHiddenButtonDefaultDimensionAction({
            parameters: { status: this.status.value, shippingStatus: this.shippingStatus.value },
            recordId: this.$.recordId,
        });

        this.defaultDimension.isDisabled = displayButtons.isDisabledButtonDefaultDimensionAction({
            parameters: {
                site: this.site.value,
                stockSite: this.stockSite.value,
                soldToCustomer: this.soldToCustomer.value,
                date: this.date.value,
                currency: this.currency.value,
            },
        });
    }

    private manageDisplayButtonRequestAllocationAction(isDirty: boolean) {
        this.requestAllocation.isHidden = displayButtons.isHiddenButtonRequestAllocationAction({
            parameters: {
                allocationRequestStatus: this.allocationRequestStatus.value,
                allocationStatus: this.allocationStatus.value,
                status: this.status.value,
            },
            recordId: this.$.recordId,
        });

        this.requestAllocation.isDisabled = displayButtons.isDisabledButtonRequestAllocationAction({
            parameters: {
                allocationRequestStatus: this.allocationRequestStatus.value,
                allocationStatus: this.allocationStatus.value,
                status: this.status.value,
            },
            recordId: this.$.recordId,
            isDirty,
        });
    }

    private manageDisplayButtonRequestDeallocationAction(isDirty: boolean) {
        this.requestDeallocation.isHidden = displayButtons.isHiddenButtonRequestDeallocationAction({
            parameters: {
                allocationRequestStatus: this.allocationRequestStatus.value,
                allocationStatus: this.allocationStatus.value,
                status: this.status.value,
            },
            recordId: this.$.recordId,
        });

        this.requestDeallocation.isDisabled = displayButtons.isDisabledButtonRequestDeallocationAction({
            parameters: {
                allocationRequestStatus: this.allocationRequestStatus.value,
                allocationStatus: this.allocationStatus.value,
                status: this.status.value,
            },
            recordId: this.$.recordId,
            isDirty,
        });
    }

    @ui.decorators.pageAction<SalesOrder>({
        icon: 'bin',
        title: 'Delete',
        isHidden: true,
        isDestructive: true,
        async onClick() {
            const isAssignmentChecked = await isAssignmentCheckedOnSalesOrderDelete(this);
            if (isAssignmentChecked) {
                await this.$standardDeleteAction.execute();
            }
        },
    })
    deleteSalesOrder: ui.PageAction;

    @ui.decorators.pageAction<SalesOrder>({
        title: 'Confirm',
        isHidden: true,
        async onClick() {
            await SalesOrderConfirmFunction.confirmSalesOrderFunction({
                isCalledFromRecordPage: true,
                pageOrWidget: this,
                _id: this.$.recordId ?? '',
                status: (this.status.value as SalesOrderStatus) ?? 'closed',
                number: this.number.value ?? '',
                isOnHold: this.isOnHold.value ?? false,
                customerOnHoldCheck: this.site.value?.legalCompany?.customerOnHoldCheck ?? '',
                taxCalculationStatus: this.taxCalculationStatus.value ?? '',
            });
            this.$.loader.isHidden = true;
        },
    })
    confirm: ui.PageAction;

    @ui.decorators.pageAction<SalesOrder>({
        title: 'Close order',
        isHidden: true,
        async onClick() {
            const isAssignmentChecked = await isAssignmentCheckedOnSalesOrderClose({
                salesOrderPage: this,
                recordNumber: this.number.value ?? '',
                isOrderAssignmentLinked: !!this.isOrderAssignmentLinked.value,
            });
            if (isAssignmentChecked && this.number.value) {
                await actionFunctions.closeAction({
                    isCalledFromRecordPage: true,
                    salesOrderPage: this,
                    recordNumber: this.$.recordId ?? '',
                });
                await this.$.refreshNavigationPanel();
            }
        },
    })
    close: ui.PageAction;

    @ui.decorators.pageAction<SalesOrder>({
        title: 'Open order',
        isHidden: true,
        async onClick() {
            if (this.number.value) {
                await actionFunctions.openAction({
                    isCalledFromRecordPage: true,
                    salesOrderPage: this,
                    recordNumber: this.number.value,
                });
            }
        },
    })
    open: ui.PageAction;

    @ui.decorators.pageAction<SalesOrder>({
        title: 'Save',
        buttonType: 'primary',
        access: {
            bind: '$update',
        },
        async onClick() {
            let onHoldWarning = true;
            if (
                this.status.value !== 'quote' &&
                this.isOnHold.value &&
                this.site.value?.legalCompany?.customerOnHoldCheck === 'blocking'
            ) {
                onHoldWarning = await actionFunctions.showOnHoldSaveMessage(this);
            }
            if (onHoldWarning) {
                const isDialog = this.$.isInDialog;
                await this.$standardSaveAction.execute(true);
                if (isDialog) return;

                if (this.$.recordId && this.status.value === 'quote') {
                    const financeIntegrationCheckResult = await this.$.graph
                        .node('@sage/xtrem-sales/SalesOrder')
                        .mutations.financeIntegrationCheck(
                            {
                                wasSuccessful: true,
                                message: true,
                            },
                            { salesOrder: this.$.recordId },
                        )
                        .execute();

                    if (!financeIntegrationCheckResult.wasSuccessful) {
                        this.$.showToast(
                            `**${ui.localize(
                                '@sage/xtrem-sales/pages__sales_order__save_warnings',
                                'Warnings while saving:',
                            )}**\n${financeIntegrationCheckResult.message}`,
                            { type: 'warning', timeout: 20000 },
                        );
                    }
                }
            }
        },
    })
    save: ui.PageAction;

    @ui.decorators.pageAction<SalesOrder>({
        title: 'Allocate stock',
        icon: 'three_boxes',
        onError(error: string | (Error & { errors: Array<any> })) {
            this.$.loader.isHidden = true;
            return MasterDataUtils.formatError(this, error);
        },
        async onClick() {
            await SalesOrderAllocationAction.allocationAction({
                pageOrWidget: this,
                _id: this.$.recordId ?? '',
                allocationType: 'allocation',
            });
        },
    })
    requestAllocation: ui.PageAction;

    @ui.decorators.pageAction<SalesOrder>({
        title: 'Deallocate stock',
        icon: 'three_boxes',
        onError(error: string | (Error & { errors: Array<any> })) {
            this.$.loader.isHidden = true;
            return MasterDataUtils.formatError(this, error);
        },
        async onClick() {
            await SalesOrderAllocationAction.allocationAction({
                pageOrWidget: this,
                _id: this.$.recordId ?? '',
                allocationType: 'deallocation',
            });
        },
    })
    requestDeallocation: ui.PageAction;

    @ui.decorators.pageAction<SalesOrder>({
        icon: 'none',
        title: 'Set dimensions',
        async onClick() {
            const defaultedFromItem = await attributesAndDimensions.getAttributesAndDimensionsFromItem({
                page: this,
                dimensionDefinitionLevel: 'salesDirect',
                companyId: Number(this.site?.value?.legalCompany?._id),
            });

            this._defaultDimensionsAttributes = await dimensionPanelHelpers.editAndApplyDefaultDimensionsIfNeeded(
                this,
                this.lines,
                this._defaultDimensionsAttributes,
                () => true,
                defaultedFromItem,
            );
        },
    })
    defaultDimension: ui.PageAction;

    @ui.decorators.pageAction<SalesOrder>({
        title: 'Print',
        icon: 'print',
        isHidden: true,
        access: { node: '@sage/xtrem-sales/SalesOrder', bind: 'setIsPrintedTrue' },
        async onClick() {
            if (this.number.value && this.$.recordId && this.status.value) {
                await SalesOrderPrintFunction.printSalesOrderAction({
                    isCalledFromRecordPage: true,
                    pageOrWidget: this,
                    _id: this.$.recordId,
                    status: this.status.value,
                    number: this.number.value,
                });
            }
        },
    })
    print: ui.PageAction;

    @ui.decorators.pageAction<SalesOrder>({
        title: 'Create shipment',
        isHidden: true,
        async onClick() {
            if (await isAssignmentCheckedOnSalesOrderShipNotAllocatedQuantity(this, this.$.recordId ?? '')) {
                await actionFunctions.createShipmentAction({
                    isCalledFromRecordPage: true,
                    salesOrderPage: this,
                    isOnHold: this.isOnHold.value,
                    customerOnHoldCheck: this.site.value?.legalCompany?.customerOnHoldCheck ?? '',
                    order: this.$.recordId ?? '',
                    showToast: this.$.showToast,
                });
            }
        },
    })
    ship: ui.PageAction;

    @ui.decorators.pageAction<SalesOrder>({
        isTransient: true,
        isHidden: true,
        title: 'Send',
        icon: 'email',
        async onClick() {
            const localizedResults = getLocalizeOrderSendAction({
                orderStatus: this.status.value as SalesOrderStatus,
                email: this.soldToContact?.value?.email,
            });
            const result = await this.$.dialog.page(
                '@sage/xtrem-master-data/SendEmailPanel',
                {
                    nodeName: JSON.stringify('@sage/xtrem-sales/SalesOrder'),
                    email: JSON.stringify(this.soldToContact?.value?.email ?? ''),
                    firstName: JSON.stringify(this.soldToContact?.value?.firstName ?? ''),
                    lastName: JSON.stringify(this.soldToContact?.value?.lastName ?? ''),
                    title: JSON.stringify(this.soldToContact?.value?.title ?? ''),
                    soldToLinkedAddressId: JSON.stringify(this.soldToLinkedAddress?.value?._id ?? ''),
                    pageId: JSON.stringify(this.$.recordId ?? ''),
                    dialogTitle: JSON.stringify(localizedResults.dialogTitle),
                    confirmTitle: JSON.stringify(localizedResults.confirmTitle),
                    confirmMessage: JSON.stringify(localizedResults.confirmMessage),
                    resultMessage: JSON.stringify(localizedResults.resultMessage),
                },
                {
                    resolveOnCancel: true,
                    size: 'extra-large',
                },
            );
            if (result.success) await this.$.router.refresh();
        },
    })
    sendEmail: ui.PageAction;

    /** Create proforma invoice */
    @ui.decorators.pageAction<SalesOrder>({
        isTransient: true,
        title: 'Create proforma invoice',
        icon: 'add',
        access: {
            node: '@sage/xtrem-sales/ProformaInvoice',
            bind: '$create',
        },
        async onClick() {
            const salesOrder = this.$.values as ui.PartialNodeWithId<SalesOrderNode>;
            await SalesOrderProformaAction.proformaInvoiceDialog({
                pageOrWidget: this,
                option: 'create',
                shouldRefresh: true,
                dialogParameters: { salesOrder },
            });
        },
        isHidden() {
            return this.displayStatus.value !== confirmed;
        },
    })
    createProformaInvoice: ui.PageAction;

    @ui.decorators.referenceField<SalesOrder, Contact>({
        isHidden: true,
        node: '@sage/xtrem-master-data/Contact',
        valueField: 'email',
        columns: [
            ui.nestedFields.text({ title: 'Last name', bind: 'lastName' }),
            ui.nestedFields.text({ title: 'First name', bind: 'firstName' }),
            ui.nestedFields.text({ title: 'Title', bind: 'title' }),
            ui.nestedFields.technical({ bind: 'email' }),
            ui.nestedFields.technical({ bind: 'preferredName' }),
            ui.nestedFields.technical({ bind: 'role' }),
            ui.nestedFields.technical({ bind: 'position' }),
            ui.nestedFields.technical({ bind: 'locationPhoneNumber' }),
        ],
        title: 'Sold-to customer address contact detail',
        isTitleHidden: true,
        lookupDialogTitle: 'Select sold-to customer address contact detail',
    })
    soldToContact: ui.fields.Reference<Contact>;

    @ui.decorators.switchField<SalesOrder>({
        isHidden: true,
        bind: 'isCloseHidden',
    })
    isCloseHidden: ui.fields.Switch;

    @ui.decorators.section<SalesOrder>({
        title: 'Header section',
        isTitleHidden: true,
    })
    headerSection: ui.containers.Section;

    @ui.decorators.section<SalesOrder>({ title: 'Lines' })
    itemSection: ui.containers.Section;

    @ui.decorators.stepSequenceField<SalesOrder>({
        parent() {
            return this.headerStepSequenceBlock;
        },
        options() {
            return [
                this.orderStepSequenceCreate,
                this.orderStepSequenceConfirm,
                this.orderStepSequenceShip,
                this.orderStepSequenceInvoice,
            ];
        },
        width: 'small',
        isTransient: true,
        isFullWidth: true,
    })
    orderStepSequence: ui.fields.StepSequence;

    @ui.decorators.referenceField<SalesOrder, Site>({
        parent() {
            return this.headerBlock;
        },
        title: 'Site',
        width: 'small-medium',
        lookupDialogTitle: 'Select site',
        minLookupCharacters: 0,
        isAutoSelectEnabled: true,
        fetchesDefaults: true,
        isMandatory: true,
        isReadOnly() {
            return !!this.$.recordId || this.lines.value.length > 0;
        },
        columns: [
            ui.nestedFields.text({ bind: 'name', title: 'Name' }),
            ui.nestedFields.text({ bind: 'id', title: 'ID' }),
            ui.nestedFields.technical<SalesOrder, Site, BusinessEntity>({
                bind: 'businessEntity',
                nestedFields: [
                    ui.nestedFields.technical<SalesOrder, BusinessEntity, Currency>({
                        node: '@sage/xtrem-master-data/Currency',
                        bind: 'currency',
                        nestedFields: [
                            ui.nestedFields.technical({ bind: 'name' }),
                            ui.nestedFields.technical({ bind: 'id' }),
                            ui.nestedFields.technical({ bind: 'symbol' }),
                            ui.nestedFields.technical({ bind: 'decimalDigits' }),
                        ],
                    }),
                ],
            }),
            ui.nestedFields.reference<SalesOrder, Site, Company>({
                node: '@sage/xtrem-system/Company',
                tunnelPage: '@sage/xtrem-master-data/Company',
                title: 'Company',
                bind: 'legalCompany',
                valueField: 'name',
                helperTextField: 'id',
                columns: [
                    ui.nestedFields.text({ bind: 'name', title: 'Name' }),
                    ui.nestedFields.text({ bind: 'id', title: 'ID' }),
                    ui.nestedFields.technical<SalesOrder, Company, Legislation>({
                        node: '@sage/xtrem-structure/Legislation',
                        bind: 'legislation',
                        nestedFields: [
                            ui.nestedFields.technical({ bind: 'name' }),
                            ui.nestedFields.technical({ bind: 'id' }),
                        ],
                    }),
                    ui.nestedFields.technical<SalesOrder, Company, Currency>({
                        node: '@sage/xtrem-master-data/Currency',
                        bind: 'currency',
                        nestedFields: [
                            ui.nestedFields.technical({ bind: 'name' }),
                            ui.nestedFields.technical({ bind: 'id' }),
                            ui.nestedFields.technical({ bind: 'symbol' }),
                            ui.nestedFields.technical({ bind: 'decimalDigits' }),
                        ],
                    }),
                    ui.nestedFields.technical({ bind: 'taxEngine' }),
                    ui.nestedFields.technical({ bind: 'customerOnHoldCheck' }),
                ],
            }),
            ui.nestedFields.technical({ bind: 'description' }),
            ui.nestedFields.technical({ bind: 'isInventory' }),
            ui.nestedFields.technical({ bind: 'isSales' }),
            ui.nestedFields.technical({ bind: 'isPurchase' }),
        ],
        filter() {
            return this.stockSite.value
                ? ({
                      isSales: true,
                      legalCompany: this.stockSite.value.legalCompany,
                  } as Logical<Site>)
                : {
                      isSales: true,
                  };
        },
        async onChange() {
            this.setCurrencyUnitsForTotalAmounts();
            await this.fetchDefaultsFromSalesSite();
            await this.stockSite.validate();
            this.manageDisplayLinePhantomRow();
            this.manageDisplayButtonDefaultDimensionAction();
            this.rateDescription.isHidden = isExchangeRateHidden(
                this.currency.value,
                this.site.value,
                this.soldToCustomer.value,
            );
            if (this.site.value) {
                this.taxEngine.value = this.site.value.legalCompany?.taxEngine ?? '';
                TotalTaxCalculator.getInstance().taxEngineProperty = this.taxEngine.value;
            }
            if (this.site.value && this.soldToCustomer.value) {
                this._defaultDimensionsAttributes = await initDefaultDimensions({
                    page: this,
                    dimensionDefinitionLevel: 'salesDirect',
                    site: this.site.value,
                    customer: this.soldToCustomer.value,
                });
            }
        },
    })
    site: ui.fields.Reference<Site>;

    @ui.decorators.referenceField<SalesOrder, Customer>({
        parent() {
            return this.headerBlock;
        },
        width: 'small-medium',

        node: '@sage/xtrem-master-data/Customer',
        title: 'Sold-to customer',
        lookupDialogTitle: 'Select sold-to customer',
        minLookupCharacters: 0,
        isAutoSelectEnabled: true,
        fetchesDefaults: true,
        isMandatory: true,
        shouldSuggestionsIncludeColumns: true,
        isDisabled() {
            return !!this.$.queryParameters.isCustomerDialog;
        },
        isReadOnly() {
            return !!this.$.recordId || this.lines.value.length > 0;
        },
        warningMessage() {
            if (this.isOnHold.value === true) {
                if (this.soldToCustomer.value?._id === this.billToCustomer.value?._id) {
                    return ui.localize(
                        '@sage/xtrem-sales/pages__sales_order__display_customer_is_on_hold_status_credit_limit',
                        'Customer on hold \n\nCredit limit {{currencySymbol}} {{creditLimit}}',
                        {
                            currencySymbol: this.currency.value?.symbol,
                            creditLimit: this.soldToCustomer.value?.creditLimit,
                        },
                    );
                }
                return ui.localize(
                    '@sage/xtrem-sales/pages__sales_order__display_bill_to_customer_is_on_hold_status_credit_limit',
                    'Bill-to customer on hold \n\nCredit limit {{currencySymbol}} {{creditLimit}}',
                    {
                        currencySymbol: this.currency.value?.symbol,
                        creditLimit: this.soldToCustomer.value?.creditLimit,
                    },
                );
            }
            return '';
        },
        columns: [
            ui.nestedFields.text({ bind: { businessEntity: { name: true } }, title: 'Name' }),
            ui.nestedFields.text({ bind: { businessEntity: { id: true } }, title: 'ID' }),
            ui.nestedFields.text({ bind: { businessEntity: { taxIdNumber: true } }, title: 'Tax ID' }),
            ui.nestedFields.text({ bind: { businessEntity: { country: { name: true } } }, title: 'Country' }),
            ui.nestedFields.technical({ bind: 'creditLimit' }),
            ui.nestedFields.technical<SalesOrder, Customer, BusinessEntity>({
                bind: 'businessEntity',
                node: '@sage/xtrem-master-data/BusinessEntity',
                nestedFields: [
                    ui.nestedFields.technical({ bind: 'name' }),
                    ui.nestedFields.technical({ bind: 'id' }),
                    ui.nestedFields.technical<SalesOrder, BusinessEntity, Country>({
                        bind: 'country',
                        node: '@sage/xtrem-structure/Country',
                        nestedFields: [
                            ui.nestedFields.technical({ bind: 'name' }),
                            ui.nestedFields.technical({ bind: 'id' }),
                        ],
                    }),
                ],
            }),
            ui.nestedFields.technical<SalesOrder, Customer, BusinessEntityAddress>({
                bind: 'primaryAddress',
                node: '@sage/xtrem-master-data/BusinessEntityAddress',
                nestedFields: [
                    ui.nestedFields.technical({ bind: 'isActive' }),
                    ui.nestedFields.technical({ bind: 'name' }),
                    ui.nestedFields.technical({ bind: 'addressLine1' }),
                    ui.nestedFields.technical({ bind: 'addressLine2' }),
                    ui.nestedFields.technical({ bind: 'city' }),
                    ui.nestedFields.technical({ bind: 'region' }),
                    ui.nestedFields.technical({ bind: 'postcode' }),
                    ui.nestedFields.technical<SalesOrder, BusinessEntityAddress, Country>({
                        bind: 'country',
                        node: '@sage/xtrem-structure/Country',
                        nestedFields: [
                            ui.nestedFields.technical({ bind: 'name' }),
                            ui.nestedFields.technical({ bind: 'id' }),
                        ],
                    }),
                    ui.nestedFields.technical({ bind: 'locationPhoneNumber' }),
                ],
            }),
        ],
        async onChange() {
            this.setCurrencyUnitsForTotalAmounts();
            await this.fetchDefaultsFromSoldToCustomer();
            this.manageDisplayLinePhantomRow();
            await this.stockSite.validate();
            await this.deliveryLeadTime.validate();
            await this.deliveryMode.validate();
            this.manageDisplayButtonDefaultDimensionAction();
            this.rateDescription.isHidden = isExchangeRateHidden(
                this.currency.value,
                this.site.value,
                this.soldToCustomer.value,
            );
            if (this.site.value && this.soldToCustomer.value) {
                this._defaultDimensionsAttributes = await initDefaultDimensions({
                    page: this,
                    dimensionDefinitionLevel: 'salesDirect',
                    site: this.site.value,
                    customer: this.soldToCustomer.value,
                });
            }
        },
    })
    soldToCustomer: ui.fields.Reference<Customer>;

    @ui.decorators.textField<SalesOrder>({
        parent() {
            return this.headerBlock;
        },
        width: 'small-medium',
        title: 'Number',
        isReadOnly() {
            return !!this.$.recordId;
        },
    })
    number: ui.fields.Text;

    @ui.decorators.dateField<SalesOrder>({
        parent() {
            return this.headerBlock;
        },
        width: 'small-medium',
        title: 'Order date',
        isMandatory: true,
        fetchesDefaults: true,
        onError() {
            this.$.loader.isHidden = true;
        },
        async onChange() {
            if (Date.parse(this.date.value ?? '') > Date.now()) {
                throw new Error(
                    ui.localize(
                        '@sage/xtrem-sales/pages__sales_order__order_date__cannot__be__future',
                        'The order date cannot be later than today.',
                    ),
                );
            }
            this.manageDisplayLinePhantomRow();
            this.manageDisplayButtonDefaultDimensionAction();
            await massPriceDeterminationCalculation(
                this,
                ui.localize(
                    '@sage/xtrem-sales/pages__sales_order__on_price_determination_confirmation_order_date',
                    'The order date has been updated. Do you want to recalculate prices, discounts and charges?',
                ),
                ui.localize(
                    '@sage/xtrem-sales/pages__sales_order__on_price_determination_too_much_lines_need_save',
                    'The sales order contains more than 9 lines. You need to save it before mass calculation. Do you want to save the order and continue?',
                ),
            );
        },
    })
    date: ui.fields.Date;

    @ui.decorators.labelField<SalesOrder>({
        parent() {
            return this.informationBlock;
        },
        title: 'Tax calculation status',
        bind: 'taxCalculationStatus',
        isHidden() {
            return !['notDone', 'inProgress'].includes(this.taxCalculationStatus.value ?? '');
        },
        optionType: '@sage/xtrem-master-data/TaxCalculationStatus',
        style() {
            return PillColorCommon.getLabelColorByStatus('TaxCalculationStatus', this.taxCalculationStatus.value);
        },
    })
    taxCalculationStatus: ui.fields.Label<TaxCalculationStatus>;

    @ui.decorators.dropdownListField<SalesOrder>({
        parent() {
            return this.informationBlock;
        },
        title: 'Tax engine',
        bind: 'taxEngine',
        optionType: '@sage/xtrem-finance-data/TaxEngine',
        isHidden: true,
    })
    taxEngine: ui.fields.DropdownList;

    @ui.decorators.referenceField<SalesOrder, Currency>({
        parent() {
            return this.informationBlock;
        },
        title: 'Company currency',
        lookupDialogTitle: 'Select company currency',
        node: '@sage/xtrem-master-data/Currency',
        tunnelPage: '@sage/xtrem-master-data/Currency',
        valueField: 'name',
        helperTextField: 'id',
        isMandatory: true,
        fetchesDefaults: true,
        isHidden: true,
        columns: [
            ui.nestedFields.text({ bind: 'name', title: 'Name' }),
            ui.nestedFields.text({ bind: 'id', title: 'ISO 4217 code' }),
            ui.nestedFields.text({ title: 'Symbol', bind: 'symbol', canFilter: false }),
            ui.nestedFields.technical({ bind: 'decimalDigits' }),
            ui.nestedFields.technical({ bind: 'rounding' }),
        ],
        minLookupCharacters: 1,
        placeholder: 'Select currency',
        width: 'small',
        onChange() {
            this.applyCompanyCurrency();
        },
        isReadOnly() {
            if (this.currency.value === null) {
                return false;
            }
            return (this.lines.value?.length || 0) !== 0;
        },
    })
    companyCurrency: ui.fields.Reference<Currency>;

    private applyCompanyCurrency() {
        this.totalAmountExcludingTaxInCompanyCurrency.unit = this.salesSiteCurrency;
        this.totalAmountIncludingTaxInCompanyCurrency.unit = this.salesSiteCurrency;
    }

    @ui.decorators.dateField<SalesOrder>({
        parent() {
            return this.informationBlock;
        },
        title: 'Exchange rate date',
        isMandatory: true,
        isReadOnly: true,
        fetchesDefaults: true,
        isHidden() {
            // XT-24062 - Hide exchange rate for now
            // return isExchangeRateHidden(this.currency.value, this.site.value, this.soldToCustomer.value);
            return true;
        },
    })
    fxRateDate: ui.fields.Date;

    @ui.decorators.numericField<SalesOrder>({
        parent() {
            return this.informationBlock;
        },
        title: 'Exchange rate',
        isReadOnly: true,
        scale() {
            return this.companyFxRate.value ? Math.max(scaleOfCurrent(this.companyFxRate.value), 2) : 2;
        },
        isHidden: true,
    })
    companyFxRate: ui.fields.Numeric;

    @ui.decorators.numericField<SalesOrder>({
        parent() {
            return this.informationBlock;
        },
        title: 'Exchange rate divisor',
        isReadOnly: true,
        scale() {
            return this.companyFxRateDivisor.value ? Math.max(scaleOfCurrent(this.companyFxRateDivisor.value), 2) : 2;
        },
        isHidden: true,
    })
    companyFxRateDivisor: ui.fields.Numeric;

    @ui.decorators.checkboxField<SalesOrder>({
        parent() {
            return this.informationBlock;
        },
        isHidden: true,
        // title: 'Quote',
    })
    isQuote: ui.fields.Checkbox;

    @ui.decorators.labelField<SalesOrder>({
        title: 'Display status',
        optionType: '@sage/xtrem-sales/SalesOrderDisplayStatus',
        onClick() {
            if (this.taxCalculationStatus.value === 'failed' && this.taxEngine.value === 'genericTaxCalculation') {
                this.$.showToast(
                    ui.localize(
                        '@sage/xtrem-sales/page__at_least_one_mandatory_tax_code_not_found',
                        'At least one mandatory tax code is missing on the line.',
                    ),
                    { type: 'warning' },
                );
            }
        },
        style() {
            return PillColorCommon.getDisplayStatusPillFeatures(this.displayStatus.value);
        },
    })
    displayStatus: ui.fields.Label<SalesOrderDisplayStatus>;

    @ui.decorators.labelField<SalesOrder>({
        title: 'Status',
        bind: 'status',
        optionType: '@sage/xtrem-sales/SalesOrderStatus',
        width: 'small',
        isHidden: true,
    })
    status: ui.fields.Label;

    @ui.decorators.checkboxField<SalesOrder>({
        parent() {
            return this.informationBlock;
        },
        bind: 'isOnHold',
        title: 'On hold',
        isHidden: true,
    })
    isOnHold: ui.fields.Checkbox;

    @ui.decorators.tableField<SalesOrder, SalesOrderLineBinding>({
        bind: 'lines',
        title: 'Lines',
        isTitleHidden: true,
        canSelect: false,
        canAddNewLine: true,
        hasLineNumbers: true,
        node: '@sage/xtrem-sales/SalesOrderLine',
        orderBy: { _sortValue: +1 },
        parent() {
            return this.itemSection;
        },
        columns: [
            /** Hack for richText not supported to  nestedFields : XT-38752 */
            ui.nestedFields.technical({ bind: { externalNote: { value: true } } }),
            ui.nestedFields.technical({ bind: { internalNote: { value: true } } }),
            ui.nestedFields.switch({
                bind: 'isExternalNote',
                title: 'Add notes to customer document',
                isExcludedFromMainField: true,
            }),
            ui.nestedFields.label({
                title: 'Status',
                bind: 'status',
                optionType: '@sage/xtrem-sales/SalesOrderStatus',
                style: (_id, rowData) => PillColorSales.getLabelColorByStatus('SalesOrderStatus', rowData?.status),
            }),
            ui.nestedFields.dropdownList({
                title: 'Origin',
                bind: 'origin',
                optionType: '@sage/xtrem-sales/salesOriginDocumentType',
                isReadOnly: true,
            }),
            ui.nestedFields.technical({
                node: '@sage/xtrem-master-data/ItemSite',
                bind: 'itemSite',
                nestedFields: [
                    ui.nestedFields.technical({ bind: 'prodLeadTime' }),
                    ui.nestedFields.technical({ bind: 'isOrderToOrder' }),
                    ui.nestedFields.technical({ bind: 'preferredProcess' }),
                    ui.nestedFields.technical({ bind: 'inStockQuantity' }),
                ],
            }),
            ui.nestedFields.reference<SalesOrder, SalesOrderLineBinding, Item>({
                title: 'Item',
                bind: 'item',
                node: '@sage/xtrem-master-data/Item',
                tunnelPage: '@sage/xtrem-master-data/Item',
                valueField: 'name',
                lookupDialogTitle: 'Select item',
                helperTextField: 'id',
                shouldSuggestionsIncludeColumns: true,
                isAutoSelectEnabled: true,
                minLookupCharacters: 3,
                isMandatory: true,
                columns: [
                    ui.nestedFields.text({ bind: 'name' }),
                    ui.nestedFields.text({ bind: 'id' }),
                    ui.nestedFields.text({ bind: 'description' }),
                    ui.nestedFields.text({ title: 'Category', bind: { category: { name: true } } }),
                    ui.nestedFields.image({ bind: 'image', isHidden: true }),
                    ui.nestedFields.technical({ bind: 'type' }),
                    ui.nestedFields.technical({ bind: 'isStockManaged' }),
                    ui.nestedFields.technical({ bind: 'isManufactured' }),
                    ui.nestedFields.technical({ bind: 'isBought' }),
                    ui.nestedFields.technical<SalesOrder, Item, UnitOfMeasure>({
                        bind: 'stockUnit',
                        node: '@sage/xtrem-master-data/UnitOfMeasure',
                        nestedFields: [
                            ui.nestedFields.technical({ bind: 'name' }),
                            ui.nestedFields.technical({ bind: 'id' }),
                            ui.nestedFields.technical({ bind: 'symbol' }),
                            ui.nestedFields.technical({ bind: 'decimalDigits' }),
                        ],
                    }),
                    ui.nestedFields.technical<SalesOrder, Item, UnitOfMeasure>({
                        bind: 'salesUnit',
                        node: '@sage/xtrem-master-data/UnitOfMeasure',
                        nestedFields: [
                            ui.nestedFields.technical({ bind: 'name' }),
                            ui.nestedFields.technical({ bind: 'id' }),
                            ui.nestedFields.technical({ bind: 'symbol' }),
                            ui.nestedFields.technical({ bind: 'decimalDigits' }),
                        ],
                    }),
                    ui.nestedFields.technical<SalesOrder, Item, UnitOfMeasure>({
                        bind: 'purchaseUnit',
                        node: '@sage/xtrem-master-data/UnitOfMeasure',
                        nestedFields: [
                            ui.nestedFields.technical({ bind: 'name' }),
                            ui.nestedFields.technical({ bind: 'id' }),
                            ui.nestedFields.technical({ bind: 'symbol' }),
                            ui.nestedFields.technical({ bind: 'decimalDigits' }),
                        ],
                    }),
                    ui.nestedFields.technical<SalesOrder, Item, UnitOfMeasure>({
                        bind: 'stockUnit',
                        node: '@sage/xtrem-master-data/UnitOfMeasure',
                        nestedFields: [
                            ui.nestedFields.technical({ bind: 'name' }),
                            ui.nestedFields.technical({ bind: 'id' }),
                            ui.nestedFields.technical({ bind: 'symbol' }),
                            ui.nestedFields.technical({ bind: 'decimalDigits' }),
                        ],
                    }),
                    ui.nestedFields.technical({ bind: 'minimumSalesQuantity' }),
                    ui.nestedFields.technical({ bind: 'maximumSalesQuantity' }),
                ],
                filter: { status: 'active' },
                async onChange(rowId, rowData: ui.PartialCollectionValue<SalesOrderLineBinding>) {
                    if (rowData?.item) {
                        rowData.itemDescription = rowData.item.description
                            ? rowData.item.description
                            : rowData.item.name;
                        await this.checkItemCustomer(rowData, true);
                        rowData.stockUnit = rowData.item.stockUnit;
                        if (rowData.quantity && rowData.unitToStockUnitConversionFactor) {
                            rowData.quantityInStockUnit = (
                                +rowData.quantity * +rowData.unitToStockUnitConversionFactor
                            ).toString();
                        }
                        await this.getItemSiteAvailableStockQuantity(rowData);
                        if (rowData.stockSite) {
                            await this.getPriceDetermination(rowData, true);
                            rowData.stockSite = await checkStockSite(this, rowData.item, rowData.stockSite);
                        }

                        await this.updateGrossProfit(rowData);

                        if (this.site.value && this.soldToCustomer.value && rowData.item) {
                            const { storedAttributes, storedDimensions } =
                                await attributesAndDimensions.defaultAttributesAndDimensionsWithItem({
                                    page: this,
                                    _defaultDimensionsAttributes: this._defaultDimensionsAttributes,
                                    dimensionDefinitionLevel: 'salesDirect',
                                    site: this.site.value,
                                    customer: this.soldToCustomer.value,
                                    item: rowData.item,
                                });
                            rowData.storedAttributes = storedAttributes;
                            rowData.storedDimensions = storedDimensions;
                        }
                    }
                    this.lines.addOrUpdateRecordValue(rowData);
                },
                isDisabled(_rowId, rowValue) {
                    if (Number(rowValue?._id) < 0) {
                        return false;
                    }
                    if (rowValue && rowValue._id < 0 && rowValue.status !== 'closed') {
                        return false;
                    }
                    return true;
                },
            }),
            ui.nestedFields.text({
                title: 'Item ID',
                bind: { item: { id: true } },
                isReadOnly: true,
            }),
            ui.nestedFields.text({
                title: 'Item description',
                bind: 'itemDescription',
                width: 'large',
                isHiddenOnMainField: true,
                isDisabled(_rowId, rowItem) {
                    return rowItem?.status === 'closed';
                },
            }),
            ui.nestedFields.image({
                title: 'Image',
                isTitleHidden: true,
                bind: { item: { image: true } },
                width: 'small',
                height: '40px',
                isReadOnly: true,
                isExcludedFromMainField: true,
                isHiddenOnMainField: true,
            }),
            ui.nestedFields.technical<SalesOrder, SalesOrderLine, Site>({
                bind: 'site',
                node: '@sage/xtrem-system/Site',
                nestedFields: [
                    ui.nestedFields.technical({ bind: 'name' }),
                    ui.nestedFields.technical({ bind: 'id' }),
                    ui.nestedFields.technical<SalesOrder, Site, Company>({
                        node: '@sage/xtrem-system/Company',
                        bind: 'legalCompany',
                        nestedFields: [
                            ui.nestedFields.technical({ bind: 'name' }),
                            ui.nestedFields.technical({ bind: 'id' }),
                            ui.nestedFields.technical<SalesOrder, Company, Legislation>({
                                node: '@sage/xtrem-structure/Legislation',
                                bind: 'legislation',
                                nestedFields: [
                                    ui.nestedFields.technical({ bind: 'name' }),
                                    ui.nestedFields.technical({ bind: 'id' }),
                                ],
                            }),
                        ],
                    }),
                    ui.nestedFields.technical({ bind: 'description' }),
                ],
            }),
            ui.nestedFields.reference<SalesOrder, SalesOrderLineBinding, UnitOfMeasure>({
                title: 'Sales unit',
                bind: 'unit',
                node: '@sage/xtrem-master-data/UnitOfMeasure',
                tunnelPage: '@sage/xtrem-master-data/UnitOfMeasure',
                valueField: 'name',
                lookupDialogTitle: 'Select unit',
                helperTextField: 'symbol',
                minLookupCharacters: 1,
                isAutoSelectEnabled: true,
                isMandatory: true,
                columns: [
                    ui.nestedFields.text({ bind: 'name', title: 'Name' }),
                    ui.nestedFields.technical({ bind: 'id' }),
                    ui.nestedFields.text({ bind: 'symbol', title: 'Symbol' }),
                    ui.nestedFields.technical({ bind: 'decimalDigits' }),
                ],
                isDisabled(_value, rowValue) {
                    if (rowValue && rowValue._id < 0 && rowValue.item && rowValue.status !== 'closed') {
                        return false;
                    }
                    return true;
                },
                filter(rowData) {
                    const filterArray: Array<string> = [this.additionalItemId];
                    if (rowData.unit) {
                        filterArray.push(rowData.unit._id);
                        if (rowData.item && rowData.item.salesUnit._id !== rowData.unit._id) {
                            filterArray.push(rowData.item.salesUnit._id);
                        }
                    }
                    if (rowData.stockUnit) {
                        filterArray.push(rowData.stockUnit._id);
                    }
                    return {
                        _id: { _in: filterArray } as any,
                    };
                },
                async onChange(_rowId, rowData: ui.PartialCollectionValue<SalesOrderLineBinding>) {
                    if (rowData.item) {
                        rowData.item.minimumSalesQuantity = this.itemData.item.minimumSalesQuantity?.toString();
                        rowData.item.maximumSalesQuantity = this.itemData.item.maximumSalesQuantity?.toString();
                    }
                    if (rowData?.unit?._id === rowData?.stockUnit?._id) {
                        rowData.unitToStockUnitConversionFactor = '1';
                    } else if (rowData.salesUnit?._id === rowData.item?.salesUnit?._id) {
                        rowData.unitToStockUnitConversionFactor = this.itemData.item.conversionFactor?.toString();
                    } else if (rowData?.unit?._id === this.additionalItemId) {
                        rowData.unitToStockUnitConversionFactor = this.itemData.item.conversionFactor?.toString();
                        if (rowData.item) {
                            rowData.item.minimumSalesQuantity =
                                this.itemData.itemCustomer.minimumSalesQuantity?.toString();
                            rowData.item.maximumSalesQuantity =
                                this.itemData.itemCustomer.maximumSalesQuantity?.toString();
                        }
                    }
                    rowData.quantityInStockUnit = (
                        +(rowData.unitToStockUnitConversionFactor ?? '') * +(rowData.quantity ?? '')
                    )?.toString();

                    const isPriceDetermined = await this.priceDetermination2(
                        rowData,
                        ui.localize(
                            '@sage/xtrem-sales/pages__sales_invoice__on_price_determination_confirmation_sales_unit',
                            'The sales unit has been updated. Do you want to recalculate prices, discounts and charges?',
                        ),
                    );
                    if (!isPriceDetermined) {
                        await convertGrossPriceToUnit2(this, this.itemData, this.additionalItemId, rowData);
                    } else {
                        this.oldSalesUnit = rowData.unit?._id;
                    }
                    await this.updateGrossProfit(rowData);
                    this.lines.addOrUpdateRecordValue(rowData);
                },
            }),
            ui.nestedFields.numeric({
                title: 'Quantity in sales unit',
                bind: 'quantity',
                isMandatory: true,
                unitMode: 'unitOfMeasure',
                unit: (_id, rowData) => rowData?.unit,
                isDisabled(_value, rowValue) {
                    if (rowValue && rowValue.item && rowValue.status !== 'closed') {
                        return false;
                    }
                    return true;
                },
                async validation(val: number, rowData: SalesOrderLineBinding) {
                    if (val <= 0) {
                        return ui.localize(
                            '@sage/xtrem-sales/pages__sales_order_line_panel__quantity_in_sales_unit_negative_value',
                            'The quantity in sales unit must not be less than or equal to 0.',
                        );
                    }

                    if (
                        rowData.item &&
                        parseFloat(rowData.item.minimumSalesQuantity) !== 0 &&
                        val < parseFloat(rowData.item.minimumSalesQuantity)
                    ) {
                        return ui.localize(
                            '@sage/xtrem-sales/pages__sales__quantity_in_sales_unit_minimum_quantity',
                            'The quantity in sales unit ({{value}}) must not be less than {{minValue}}.',
                            {
                                value: val,
                                minValue: rowData.item.minimumSalesQuantity,
                            },
                        );
                    }

                    if (
                        rowData.item &&
                        parseFloat(rowData.item.maximumSalesQuantity) !== 0 &&
                        val > parseFloat(rowData.item.maximumSalesQuantity)
                    ) {
                        return ui.localize(
                            '@sage/xtrem-sales/pages__sales__quantity_in_sales_unit_maximum_quantity',
                            'The quantity in sales unit ({{value}}) must not be larger than {{maxValue}}.',
                            {
                                value: val,
                                maxValue: rowData.item.maximumSalesQuantity,
                            },
                        );
                    }
                    if (rowData.status === 'inProgress') {
                        this.$.loader.isHidden = false;

                        const totalShipped = await this.$.graph
                            .node('@sage/xtrem-sales/SalesOrderLineToSalesShipmentLine')
                            .aggregate.query(
                                ui.queryUtils.edgesSelector(
                                    {
                                        group: { linkedDocument: { _id: { _by: 'value' } } },
                                        values: {
                                            quantity: {
                                                sum: true,
                                                avg: false,
                                                min: false,
                                                max: false,
                                                distinctCount: false,
                                            },
                                        },
                                    },
                                    {
                                        filter: { linkedDocument: rowData._id },
                                    },
                                ),
                            )
                            .execute()
                            .finally(() => {
                                this.$.loader.isHidden = true;
                            });

                        if (
                            totalShipped.edges.length &&
                            Number(totalShipped.edges.shift()?.node.values.quantity?.sum) > Number(val)
                        ) {
                            return ui.localize(
                                '@sage/xtrem-sales/sales_order_line__quantity_bellow_already_shipped_quantity',
                                'The sales order line quantity cannot be lower than the quantity already shipped.',
                            );
                        }
                    }

                    // Automatic allocation in progress : we can't decrease the quantity
                    if (
                        val <
                            Number(rowData.quantityAllocated || 0) + Number(rowData.remainingQuantityToAllocate || 0) &&
                        rowData.allocationRequestStatus === 'inProgress'
                    ) {
                        return ui.localize(
                            '@sage/xtrem-sales/page__sales_order__auto_allocation_cannot_decrease_quantity',
                            'You can only reduce the quantity of the line after the allocation request is complete.',
                        );
                    }

                    return undefined;
                },
                async onChange(_rowId, rowData: ui.PartialCollectionValue<SalesOrderLineBinding>) {
                    if (rowData.quantity && rowData.unitToStockUnitConversionFactor) {
                        rowData.quantityInStockUnit = (
                            +rowData.quantity * +rowData.unitToStockUnitConversionFactor
                        ).toString();
                    }

                    if (
                        rowData._id &&
                        +rowData._id > 0 &&
                        (await isAssignmentCheckedOnSalesOrderLineQuantityChanged(
                            this,
                            rowData,
                            Number(rowData.quantityInStockUnit),
                        ))
                    ) {
                        const isPriceDetermined = await this.priceDetermination2(
                            rowData,
                            ui.localize(
                                '@sage/xtrem-sales/pages__sales_invoice__on_price_determination_confirmation_sales_quantity',
                                'The sales quantity has been updated. Do you want to recalculate prices, discounts and charges?',
                            ),
                        );
                        if (!isPriceDetermined) {
                            await this.calculatePrices(rowData);
                        }
                        await this.getItemSiteAvailableStockQuantity(rowData);
                        await this.updateGrossProfit(rowData);
                        this.lines.addOrUpdateRecordValue(rowData);
                    }
                },
            }),
            ui.nestedFields.reference<SalesOrder, SalesOrderLineBinding, UnitOfMeasure>({
                title: 'Stock unit',
                bind: 'stockUnit',
                node: '@sage/xtrem-master-data/UnitOfMeasure',
                tunnelPage: '@sage/xtrem-master-data/UnitOfMeasure',
                valueField: 'name',
                helperTextField: 'symbol',
                isExcludedFromMainField: true,
                lookupDialogTitle: 'Select unit',
                minLookupCharacters: 1,
                isAutoSelectEnabled: true,
                isMandatory: true,
                isReadOnly: true,
                isHidden(_value, rowData) {
                    return rowData?.item?.type === 'service';
                },
                columns: [
                    ui.nestedFields.text({ bind: 'name', title: 'Name' }),
                    ui.nestedFields.technical({ bind: 'id' }),
                    ui.nestedFields.text({ bind: 'symbol', title: 'Symbol' }),
                    ui.nestedFields.technical({ bind: 'decimalDigits' }),
                ],
            }),

            ui.nestedFields.numeric({
                title: 'Stock on hand',
                bind: 'stockOnHand',
                isReadOnly: true,
                isExcludedFromMainField: true,
                unit: (_id, rowData) => rowData?.stockUnit,
                unitMode: 'unitOfMeasure',
                isHidden: (_value, rowData) =>
                    (rowData?.item?.type === 'service' || rowData?.item?.type === 'good') &&
                    !rowData?.item?.isStockManaged,
            }),
            ui.nestedFields.numeric({
                title: 'Stock available',
                bind: 'availableQuantityInStockUnit',
                isReadOnly: true,
                isExcludedFromMainField: true,
                unit: (_id, rowData) => rowData?.stockUnit,
                unitMode: 'unitOfMeasure',
                isHidden: (_value, rowData) =>
                    (rowData?.item?.type === 'service' || rowData?.item?.type === 'good') &&
                    !rowData?.item?.isStockManaged,
            }),
            ui.nestedFields.numeric({
                title: 'Stock shortage',
                bind: 'stockShortageInStockUnit',
                isReadOnly: true,
                isExcludedFromMainField: true,
                unit: (_id, rowData) => rowData?.stockUnit,
                unitMode: 'unitOfMeasure',
                isHidden: (_value, rowData) =>
                    (rowData?.item?.type === 'service' || rowData?.item?.type === 'good') &&
                    !rowData?.item?.isStockManaged,
            }),
            ui.nestedFields.numeric<SalesOrder, SalesOrderLine>({
                title: 'Stock unit conversion factor',
                bind: 'unitToStockUnitConversionFactor',
                isReadOnly: true,
                scale: 10,
                isHiddenOnMainField: true,
                isHidden: (_value, rowData) => rowData?.item?.type === 'service',
            }),
            ui.nestedFields.numeric<SalesOrder, SalesOrderLine>({
                title: 'Quantity in stock unit',
                bind: 'quantityInStockUnit',
                isReadOnly: true,
                isHiddenOnMainField: true,
                isHidden: (_value, rowData) => rowData?.item?.type === 'service',
                unitMode: 'unitOfMeasure',
                unit: (_id, rowData) => rowData?.unit,
            }),
            ui.nestedFields.numeric({
                title: 'Quantity in progress',
                bind: 'quantityToShipInProgressInSalesUnit',
                isReadOnly: true,
                isExcludedFromMainField: true,
                scale: (_value, rowData) => MasterDataUtils.getScaleValue(2, rowData?.unit?.decimalDigits),
                postfix: (_value, rowData) => rowData?.unit?.symbol,
            }),
            ui.nestedFields.numeric({
                title: 'Shipped quantity',
                bind: 'shippedQuantityInSalesUnit',
                isReadOnly: true,
                isExcludedFromMainField: true,
                scale: (_value, rowData) => MasterDataUtils.getScaleValue(2, rowData?.unit?.decimalDigits),
                postfix: (_value, rowData) => rowData?.unit?.symbol,
            }),
            ui.nestedFields.numeric({
                title: 'Remaining quantity',
                bind: 'remainingQuantityToShipInSalesUnit',
                isReadOnly: true,
                isExcludedFromMainField: true,
                scale: (_value, rowData) => MasterDataUtils.getScaleValue(2, rowData?.unit?.decimalDigits),
                postfix: (_value, rowData) => rowData?.unit?.symbol,
            }),
            ui.nestedFields.numeric({
                title: 'Quantity in progress',
                bind: 'quantityToInvoiceInProgressInSalesUnit',
                isReadOnly: true,
                isExcludedFromMainField: true,
                scale: (_value, rowData) => MasterDataUtils.getScaleValue(2, rowData?.unit?.decimalDigits),
                postfix: (_value, rowData) => rowData?.unit?.symbol,
            }),
            ui.nestedFields.numeric({
                title: 'Invoiced quantity',
                bind: 'invoicedQuantityInSalesUnit',
                isReadOnly: true,
                isExcludedFromMainField: true,
                scale: (_value, rowData) => MasterDataUtils.getScaleValue(2, rowData?.unit?.decimalDigits),
                postfix: (_value, rowData) => rowData?.unit?.symbol,
            }),
            ui.nestedFields.numeric({
                title: 'Remaining quantity',
                bind: 'remainingQuantityToInvoiceInSalesUnit',
                isReadOnly: true,
                isExcludedFromMainField: true,
                scale: (_value, rowData) => MasterDataUtils.getScaleValue(2, rowData?.unit?.decimalDigits),
                postfix: (_value, rowData) => rowData?.unit?.symbol,
            }),

            ui.nestedFields.numeric({
                title: 'Gross price',
                bind: 'grossPrice',
                unit() {
                    return this.currency.value;
                },
                validation(val) {
                    if (val < 0) {
                        return ui.localize(
                            '@sage/xtrem-sales/pages__sales_order_line_panel__gross_price_greater_or_equal_to_0',
                            'The gross price must be higher or equal to 0.',
                        );
                    }
                    return undefined;
                },
                async onChange(_rowId, rowData: ui.PartialCollectionValue<SalesOrderLineBinding>) {
                    if (!rowData.grossPrice) rowData.grossPrice = '0';
                    await this.calculatePrices(rowData);
                    this.setPriceOriginAndReason(rowData);
                    await this.updateGrossProfit(rowData);
                },
                isDisabled: (_rowId, rowItem) => rowItem?.status === 'closed',
            }),
            ui.nestedFields.numeric({
                title: 'Discount',
                bind: 'discount',
                isHiddenOnMainField: true,
                postfix: '%',
                scale: 2,
                async onChange(_rowId, rowData: ui.PartialCollectionValue<SalesOrderLineBinding>) {
                    if (!rowData.discount) rowData.discount = '0';
                    await this.calculatePrices(rowData);
                    this.setPriceOriginAndReason(rowData);
                    await this.updateGrossProfit(rowData);
                },
                isDisabled(_rowId, rowItem) {
                    return rowItem?.status === 'closed';
                },
            }),
            ui.nestedFields.numeric({
                bind: 'charge',
                title: 'Charge',
                isHiddenOnMainField: true,
                postfix: '%',
                scale: 2,
                async onChange(_rowId, rowData: ui.PartialCollectionValue<SalesOrderLineBinding>) {
                    if (!rowData.charge) rowData.charge = '0';
                    await this.calculatePrices(rowData);
                    this.setPriceOriginAndReason(rowData);
                    await this.updateGrossProfit(rowData);
                },
                isDisabled(_rowId, rowItem) {
                    return rowItem?.status === 'closed';
                },
            }),
            ui.nestedFields.numeric({
                isReadOnly: true,
                title: 'Net price',
                bind: 'netPrice',
                unit() {
                    return this.currency.value;
                },
            }),
            ui.nestedFields.numeric({
                bind: 'grossPriceDeterminated',
                isHidden: true,
            }),
            ui.nestedFields.technical({ bind: 'discountDeterminated' }),
            ui.nestedFields.technical({ bind: 'chargeDeterminated' }),
            ui.nestedFields.label<SalesOrder, SalesOrderLine>({ bind: 'priceOriginDeterminated', isHidden: true }),
            ui.nestedFields.technical<SalesOrder, SalesOrderLine, CustomerPriceReason>({
                bind: 'priceReasonDeterminated',
                node: '@sage/xtrem-master-data/CustomerPriceReason',
                nestedFields: [
                    ui.nestedFields.technical({ bind: 'name' }),
                    ui.nestedFields.technical({ bind: 'priority' }),
                    ui.nestedFields.technical({ bind: 'isActive' }),
                    ui.nestedFields.technical({ bind: 'description' }),
                ],
            }),
            ui.nestedFields.technical({ bind: 'isPriceDeterminated' }),
            ui.nestedFields.label<SalesOrder, SalesOrderLine>({
                title: 'Price origin',
                bind: 'priceOrigin',
                optionType: '@sage/xtrem-sales/SalesPriceOrigin',
                isHiddenOnMainField: true,
            }),
            ui.nestedFields.reference<SalesOrder, SalesOrderLineBinding, CustomerPriceReason>({
                title: 'Price reason',
                bind: 'priceReason',
                node: '@sage/xtrem-master-data/CustomerPriceReason',
                valueField: 'name',
                lookupDialogTitle: 'Select price reason',
                minLookupCharacters: 0,
                isHiddenOnMainField: true,
                isAutoSelectEnabled: true,
                isReadOnly: true,
                columns: [
                    ui.nestedFields.text({ bind: 'name', title: 'Name' }),
                    ui.nestedFields.text({ bind: 'priority', title: 'Priority' }),
                    ui.nestedFields.technical({ bind: 'isActive' }),
                    ui.nestedFields.technical({ bind: 'description' }),
                ],
            }),
            ui.nestedFields.numeric({
                title: 'Total excluding tax',
                isReadOnly: true,
                bind: 'amountExcludingTax',
                unit() {
                    return this.currency.value;
                },
                scale: null,
            }),
            ui.nestedFields.numeric({
                title: 'Total excluding tax company currency',
                isReadOnly: true,
                bind: 'amountExcludingTaxInCompanyCurrency',
                isExcludedFromMainField: true,
                unit() {
                    return this.currency.value;
                },
                scale: null,
                isHidden() {
                    return isExchangeRateHidden(this.currency.value, this.site.value, this.soldToCustomer.value);
                },
            }),
            ui.nestedFields.numeric({
                title: 'Total tax',
                bind: 'taxAmount',
                isReadOnly: true,
                isHidden() {
                    return (
                        this.taxCalculationStatus.value === 'failed' && this.taxEngine.value === 'genericTaxCalculation'
                    );
                },
                unit() {
                    return this.currency.value;
                },
                scale: null,
            }),
            ui.nestedFields.technical({ bind: 'taxAmountAdjusted' }),
            ui.nestedFields.numeric({
                title: 'Total including tax',
                bind: 'amountIncludingTax',
                isReadOnly: true,
                unit() {
                    return this.currency.value;
                },
                scale: null,
            }),
            ui.nestedFields.numeric({
                title: 'Total including tax company currency',
                isReadOnly: true,
                bind: 'amountIncludingTaxInCompanyCurrency',
                isExcludedFromMainField: true,
                unit() {
                    return this.salesSiteCurrency;
                },
                scale: null,
                isHidden() {
                    return isExchangeRateHidden(this.currency.value, this.site.value, this.soldToCustomer.value);
                },
            }),
            ui.nestedFields.numeric({
                isReadOnly: true,
                title: 'Stock cost amount',
                bind: 'stockCostAmount',
                unit() {
                    return this.currency.value;
                },
                scale: null,
            }),
            ui.nestedFields.numeric({
                isReadOnly: true,
                title: 'Gross profit amount',
                bind: 'grossProfitAmount',
                unit() {
                    return this.currency.value;
                },
                scale: null,
            }),
            ui.nestedFields.numeric({
                title: 'Available',
                bind: 'availableQuantityInSalesUnit',
                isReadOnly: true,
                unit: (_id, rowData) => rowData?.unit,
                scale: null,
                isHidden: (_value, rowData) =>
                    (rowData?.item?.type === 'service' || rowData?.item?.type === 'good') &&
                    !rowData?.item?.isStockManaged,
            }),
            ui.nestedFields.numeric<SalesOrder, SalesOrderLine>({
                title: 'Stock shortage',
                bind: 'stockShortageInSalesUnit',
                isReadOnly: true,
                isHiddenOnMainField: true,
                unit: (_id, rowData) => rowData?.unit,
                scale: null,
                isHidden: (_value, rowData) =>
                    (rowData?.item?.type === 'service' || rowData?.item?.type === 'good') &&
                    !rowData?.item?.isStockManaged,
            }),
            ui.nestedFields.reference<SalesOrder, SalesOrderLineBinding, Site>({
                title: 'Stock site',
                bind: 'stockSite',
                tunnelPage: '@sage/xtrem-master-data/Site',
                lookupDialogTitle: 'Select stock site',
                minLookupCharacters: 0,
                isAutoSelectEnabled: true,
                isMandatory: true,
                columns: [
                    ui.nestedFields.text({ bind: 'id', title: 'ID' }),
                    ui.nestedFields.text({ bind: 'name', title: 'Name' }),
                    ui.nestedFields.reference<SalesOrder, Site, Company>({
                        node: '@sage/xtrem-system/Company',
                        tunnelPage: '@sage/xtrem-master-data/Company',
                        title: 'Company',
                        bind: 'legalCompany',
                        valueField: 'name',
                        helperTextField: 'id',
                        columns: [
                            ui.nestedFields.text({ bind: 'name', title: 'Name' }),
                            ui.nestedFields.text({ bind: 'id', title: 'ID' }),
                        ],
                    }),
                    ui.nestedFields.technical({ bind: 'description' }),
                    ui.nestedFields.technical({ bind: 'isManufacturing' }),
                ],
                async onChange(_rowId, rowData: ui.PartialCollectionValue<SalesOrderLineBinding>) {
                    await this.priceDetermination2(
                        rowData,
                        ui.localize(
                            '@sage/xtrem-sales/pages__sales_order__on_price_determination_confirmation_stock_site',
                            'The stock site has been updated. Do you want to recalculate prices, discounts and charges?',
                        ),
                    );
                    await this.updateGrossProfit(rowData);
                    this.lines.addOrUpdateRecordValue(rowData);
                },
                isDisabled(_rowId, rowItem) {
                    return (
                        rowItem?.status === 'closed' ||
                        ['partiallyAllocated', 'allocated'].includes(rowItem?.allocationStatus) ||
                        rowItem?.uWorkOrderLine ||
                        rowItem?.uPurchaseOrderLine
                    );
                },
            }),
            ui.nestedFields.date({
                title: 'Shipping date',
                bind: 'shippingDate',
                isMandatory: true,
                validation(val, rowValue) {
                    // TODO: we will control later the working days of the shipping
                    // site when the information will be available on the site
                    // if (isNotWorkDay(+this.workDays.value, DateValue.parse(val))) {
                    //    return ui.localize(
                    //        '@sage/xtrem-sales/pages__sales_order_line_panel__shipping_date_cannot_be_a_not_working_day',
                    //        'The shipping date must fall on a working day.',
                    //    );
                    // }
                    if (this.date.value && val < this.date.value) {
                        return ui.localize(
                            '@sage/xtrem-sales/pages__sales_order__shipping_date_cannot_be_a_less_order_date',
                            'The shipping date ({{value}}) cannot be earlier than the order date ({{date}}).',
                            {
                                value: val,
                                date: this.date.value,
                            },
                        );
                    }
                    if (rowValue.doNotShipBeforeDate && val < rowValue.doNotShipBeforeDate) {
                        return ui.localize(
                            '@sage/xtrem-sales/pages__sales_order__shipping_date_cannot_be_a_earlier_ship_before',
                            "The shipping date ({{value}}) cannot be earlier than the 'Do-not-ship-before' date ({{doNotShipBeforeDate}}).",
                            {
                                value: val,
                                doNotShipBeforeDate: rowValue.doNotShipBeforeDate,
                            },
                        );
                    }

                    if (rowValue.doNotShipAfterDate && val > rowValue.doNotShipAfterDate) {
                        return ui.localize(
                            '@sage/xtrem-sales/pages__sales_order__shipping_date_cannot_be_a_later_ship_after',
                            "The shipping date ({{value}}) cannot be later than the 'Do-not-ship-after' date ({{doNotShipAfterDate}}).",
                            {
                                value: val,
                                doNotShipAfterDate: rowValue.doNotShipAfterDate,
                            },
                        );
                    }
                    return undefined;
                },

                async onChange(_rowId, rowData) {
                    await this.updateExpectedDeliveryDate();
                    this.lines.addOrUpdateRecordValue(rowData);
                },
                isDisabled(_rowId, rowItem) {
                    return rowItem?.status === 'closed';
                },
            }),
            ui.nestedFields.date({
                title: 'Do-not-ship-before date',
                bind: 'doNotShipBeforeDate',
                isHiddenOnMainField: true,
                isDisabled(_rowId, rowItem) {
                    return rowItem?.status === 'closed';
                },
            }),
            ui.nestedFields.date({
                title: 'Do-not-ship-after date',
                bind: 'doNotShipAfterDate',
                isHiddenOnMainField: true,
                isDisabled(_rowId, rowItem) {
                    return rowItem?.status === 'closed';
                },
            }),
            ui.nestedFields.date({
                title: 'Requested delivery date',
                bind: 'requestedDeliveryDate',
                isMandatory: true,
                validation(val) {
                    if (this.workDays.value) {
                        return deliveryDateValidation(val, this.workDays.value);
                    }
                    return undefined;
                },
                async onChange(_rowId, rowData) {
                    await this.recomputeShippingDateAndDeliveryDateLine(rowData);
                    this.lines.addOrUpdateRecordValue(rowData);
                },
                isDisabled(_rowId, rowItem) {
                    return rowItem?.status === 'closed';
                },
            }),
            ui.nestedFields.numeric({
                title: 'Delivery lead time',
                bind: 'deliveryLeadTime',
                isMandatory: true,
                postfix: 'day(s)',
                async onChange(_rowId, rowData) {
                    await this.recomputeShippingDateAndDeliveryDateLine(rowData);
                    this.lines.addOrUpdateRecordValue(rowData);
                },
                isDisabled(_rowId, rowItem) {
                    return rowItem?.status === 'closed';
                },
            }),
            ui.nestedFields.date({
                title: 'Expected delivery date',
                bind: 'expectedDeliveryDate',
                isMandatory: true,
                validation(val) {
                    if (this.workDays.value) {
                        return deliveryDateValidation(val, this.workDays.value);
                    }
                    return undefined;
                },
                isDisabled(_rowId, rowItem) {
                    return rowItem?.status === 'closed';
                },
            }),
            ui.nestedFields.reference<SalesOrder, SalesOrderLineBinding, DeliveryMode>({
                title: 'Delivery mode',
                bind: 'deliveryMode',
                node: '@sage/xtrem-master-data/DeliveryMode',
                tunnelPage: '@sage/xtrem-master-data/DeliveryMode',
                valueField: 'name',
                isMandatory: true,
                columns: [
                    ui.nestedFields.text({ bind: 'name', title: 'Name' }),
                    ui.nestedFields.technical({ bind: 'description' }),
                ],
                isDisabled: (_rowId, rowItem) => rowItem?.status === 'closed',
            }),
            ui.nestedFields.label({
                title: 'Allocation status',
                bind: 'allocationStatus',
                optionType: '@sage/xtrem-stock-data/StockAllocationStatus',
                isHidden() {
                    return ['closed', 'quote'].includes(this.status.value ?? '');
                },
                style: (_id, rowData) =>
                    PillColorStock.getLabelColorByStatus('StockAllocationStatus', rowData?.allocationStatus),
            }),
            ui.nestedFields.label({
                title: 'Allocation request status',
                bind: 'allocationRequestStatus',
                optionType: '@sage/xtrem-stock-data/AllocationRequestStatus',
                isHidden() {
                    return ['noRequest', 'completed'].includes(this.allocationRequestStatus.value || '');
                },
                style: (_id, rowData) =>
                    PillColorStock.getLabelColorByStatus('AllocationRequestStatus', rowData?.allocationRequestStatus),
            }),
            ui.nestedFields.label<SalesOrder, SalesOrderLine>({
                title: 'Stock shortage status',
                bind: 'stockShortageStatus',
                isHiddenOnMainField: true,
                backgroundColor: value =>
                    PillColorSales.setBooleanStatusColors('stockShortageStatus', value, 'backgroundColor'),
                borderColor: value =>
                    PillColorSales.setBooleanStatusColors('stockShortageStatus', value, 'borderColor'),
                color: value => PillColorSales.setBooleanStatusColors('stockShortageStatus', value, 'textColor'),
                map: value =>
                    value === true
                        ? ui.localize(
                              '@sage/xtrem-sales/pages__sales_order_line_panel__display_shortage_status_true',
                              'Stock shortage',
                          )
                        : ui.localize(
                              '@sage/xtrem-sales/pages__sales_order_line_panel__display_shortage_status_false',
                              'Stock available',
                          ),
                isHidden: (_value, rowData) =>
                    (rowData?.item?.type === 'service' || rowData?.item?.type === 'good') &&
                    !rowData?.item?.isStockManaged,
            }),
            ui.nestedFields.label({
                title: 'Shipping status',
                bind: 'shippingStatus',
                isHiddenOnMainField: true,
                optionType: '@sage/xtrem-sales/SalesDocumentShippingStatus',
                style: (_id, rowData) =>
                    PillColorSales.getLabelColorByStatus('SalesDocumentShippingStatus', rowData?.shippingStatus),
            }),
            ui.nestedFields.label({
                title: 'Invoice status',
                bind: 'invoiceStatus',
                isHiddenOnMainField: true,
                optionType: '@sage/xtrem-sales/SalesDocumentInvoiceStatus',
                style: (_id, rowData) =>
                    PillColorSales.getLabelColorByStatus('SalesDocumentInvoiceStatus', rowData?.invoiceStatus),
            }),
            ui.nestedFields.label({
                title: 'Tax status',
                bind: 'taxCalculationStatus',
                isHidden() {
                    return (
                        this.taxCalculationStatus.value !== 'failed' || this.taxEngine.value !== 'genericTaxCalculation'
                    );
                },
                optionType: '@sage/xtrem-master-data/TaxCalculationStatus',
                style: (_id, rowData) =>
                    PillColorCommon.getLabelColorByStatus('TaxCalculationStatus', rowData?.taxCalculationStatus),
                async onClick(_rowId, rowItem) {
                    this.displayTaxesClicked = true;
                    await this.callDisplayTaxes(rowItem);
                    this.displayTaxesClicked = false;
                },
            }),
            ui.nestedFields.technical({ bind: 'taxDate' }),
            ui.nestedFields.numeric({
                title: 'Quantity to allocate',
                bind: 'remainingQuantityToShipInStockUnit',
                isReadOnly: true,
                isExcludedFromMainField: true,
                unit: (_id, rowData) => rowData?.stockUnit,
                unitMode: 'unitOfMeasure',
            }),
            ui.nestedFields.numeric({
                title: 'Allocated quantity',
                bind: 'quantityAllocated',
                isReadOnly: true,
                isExcludedFromMainField: true,
                unit: (_id, rowData) => rowData?.stockUnit,
                unitMode: 'unitOfMeasure',
            }),
            ui.nestedFields.numeric({
                title: 'Remaining quantity',
                bind: 'remainingQuantityToAllocate',
                isReadOnly: true,
                isExcludedFromMainField: true,
                unit: (_id, rowData) => rowData?.stockUnit,
                unitMode: 'unitOfMeasure',
            }),

            ui.nestedFields.reference<SalesOrder, SalesOrderLineBinding, BusinessEntityAddress>({
                bind: 'shipToCustomerAddress',
                title: 'Ship-to address',
                node: '@sage/xtrem-master-data/BusinessEntityAddress',
                valueField: { name: true },
                columns: [
                    ui.nestedFields.checkbox({
                        bind: { deliveryDetail: { isPrimary: true } },
                        title: 'Primary ship-to address',
                    }),
                    ui.nestedFields.checkbox({ bind: 'isActive', title: 'Active' }),
                    ui.nestedFields.text({ bind: 'name', title: 'Name' }),
                    ui.nestedFields.text({ bind: 'addressLine1', title: 'Address line 1' }),
                    ui.nestedFields.text({ bind: 'addressLine2', title: 'Address line 2' }),
                    ui.nestedFields.text({ bind: 'city', title: 'City' }),
                    ui.nestedFields.text({ bind: 'region', title: 'Region' }),
                    ui.nestedFields.text({ bind: 'postcode', title: 'ZIP code' }),
                    ui.nestedFields.reference<SalesOrder, BusinessEntityAddress, Country>({
                        bind: 'country',
                        columns: [
                            ui.nestedFields.text({ bind: 'name', title: 'Name' }),
                            ui.nestedFields.text({ bind: 'id', title: 'ISO 3166-1 alpha-2 code' }),
                            ui.nestedFields.technical({ bind: 'regionLabel' }),
                            ui.nestedFields.technical({ bind: 'zipLabel' }),
                        ],
                    }),
                    ui.nestedFields.text({ bind: 'locationPhoneNumber', title: 'Phone number' }),
                    ui.nestedFields.technical<SalesOrder, BusinessEntityAddress, BusinessEntity>({
                        bind: 'businessEntity',
                        node: '@sage/xtrem-master-data/BusinessEntity',
                        nestedFields: [ui.nestedFields.text({ bind: 'name' }), ui.nestedFields.text({ bind: 'id' })],
                    }),
                    ui.nestedFields.reference<SalesOrder, BusinessEntityAddress, DeliveryDetail>({
                        bind: 'deliveryDetail',
                        title: 'Delivery detail',
                        node: '@sage/xtrem-master-data/DeliveryDetail',
                        valueField: '_id',
                        columns: [
                            ui.nestedFields.reference<SalesOrder, DeliveryDetail, Incoterm>({
                                bind: 'incoterm',
                                title: 'Incoterms® rule',
                            }),
                            ui.nestedFields.reference<SalesOrder, DeliveryDetail, DeliveryMode>({
                                bind: 'mode',
                                title: 'Delivery mode',
                                node: '@sage/xtrem-master-data/DeliveryMode',
                                tunnelPage: '@sage/xtrem-master-data/DeliveryMode',
                                valueField: 'name',
                                columns: [
                                    ui.nestedFields.text({ bind: 'name', title: 'Name' }),
                                    ui.nestedFields.technical({ bind: 'description' }),
                                ],
                            }),
                            ui.nestedFields.numeric({
                                bind: 'leadTime',
                                title: 'Delivery lead time',
                                postfix: 'day(s)',
                            }),
                            ui.nestedFields.reference<SalesOrder, DeliveryDetail, Site>({
                                bind: 'shipmentSite',
                                title: 'Stock site',
                                tunnelPage: '@sage/xtrem-master-data/Site',
                                columns: [
                                    ui.nestedFields.text({ bind: 'name', title: 'Name' }),
                                    ui.nestedFields.text({ bind: 'id', title: 'ID' }),
                                    ui.nestedFields.reference<SalesOrder, Site, Company>({
                                        bind: 'legalCompany',
                                        title: 'Company',
                                        node: '@sage/xtrem-system/Company',
                                        tunnelPage: '@sage/xtrem-master-data/Company',
                                        valueField: 'name',
                                        helperTextField: 'id',
                                        columns: [
                                            ui.nestedFields.text({ bind: 'name', title: 'Name' }),
                                            ui.nestedFields.text({ bind: 'id', title: 'ID' }),
                                        ],
                                    }),
                                ],
                            }),
                        ],
                    }),
                ],
                filter() {
                    return this.shipToCustomer.value?.businessEntity?._id
                        ? { businessEntity: { _id: this.shipToCustomer.value?.businessEntity?._id } }
                        : {};
                },
                async onChange(_rowId, rowData: ui.PartialCollectionValue<SalesOrderLineBinding>) {
                    if (rowData?.shipToCustomerAddress) {
                        rowData.shipToAddress = rowData.shipToCustomerAddress;

                        await this.cascadeAndFetchDefaultsFromShipToAddressLine(rowData);
                        this.lines.addOrUpdateRecordValue(rowData);
                    }
                },
                isDisabled(_rowId, rowItem) {
                    return rowItem?.status === 'closed';
                },
            }),
            ui.nestedFields.label({
                title: 'Item status',
                bind: { item: { status: true } },
                isDisabled: true,
                isHiddenOnMainField: true,
            }),
            ui.nestedFields.technical({ bind: 'computedAttributes' }),
            ui.nestedFields.technical({ bind: 'storedAttributes' }),
            ui.nestedFields.technical({ bind: 'storedDimensions' }),
            ui.nestedFields.technical<SalesOrder, SalesOrderLine, Address>({
                bind: 'shipToAddress',
                node: '@sage/xtrem-master-data/Address',
                nestedFields: [
                    ui.nestedFields.technical({ bind: 'name' }),
                    ui.nestedFields.technical({ bind: 'locationPhoneNumber' }),
                    ui.nestedFields.technical({ bind: 'addressLine1' }),
                    ui.nestedFields.technical({ bind: 'addressLine2' }),
                    ui.nestedFields.technical({ bind: 'city' }),
                    ui.nestedFields.technical({ bind: 'region' }),
                    ui.nestedFields.technical({ bind: 'postcode' }),
                    ui.nestedFields.technical({
                        bind: 'country',
                        node: '@sage/xtrem-structure/Country',
                        nestedFields: [
                            ui.nestedFields.technical({ bind: 'name' }),
                            ui.nestedFields.technical({ bind: 'id' }),
                            ui.nestedFields.technical({ bind: 'regionLabel' }),
                            ui.nestedFields.technical({ bind: 'zipLabel' }),
                        ],
                    }),
                    ui.nestedFields.technical({ bind: 'concatenatedAddress' }),
                ],
            }),
            ui.nestedFields.technical({ bind: 'uiTaxes' }),
            ui.nestedFields.technical<SalesOrder, SalesOrderLine, BaseDocumentLine>({
                bind: 'uWorkOrderLine',
                node: '@sage/xtrem-master-data/BaseDocumentLine',
                nestedFields: [
                    ui.nestedFields.technical({ bind: '_id' }),
                    ui.nestedFields.technical({ bind: 'documentNumber' }),
                    ui.nestedFields.technical({ bind: 'documentId' }),
                ],
            }),
            ui.nestedFields.technical<SalesOrder, SalesOrderLine, BaseDocumentLine>({
                bind: 'uPurchaseOrderLine',
                node: '@sage/xtrem-master-data/BaseDocumentLine',
                nestedFields: [
                    ui.nestedFields.technical({ bind: '_id' }),
                    ui.nestedFields.technical({ bind: 'documentNumber' }),
                    ui.nestedFields.technical({ bind: 'documentId' }),
                ],
            }),
            ui.nestedFields.technical({
                bind: 'workInProgress',
                nestedFields: [ui.nestedFields.technical({ bind: '_id' })],
            }),
            ui.nestedFields.technical({
                bind: 'suppliedQuantity',
            }),
            ui.nestedFields.icon({
                title: 'Order assigned',
                bind: 'uAssignmentOrder',
                map: value => (value ? 'file_generic' : 'null'),
            }),
        ],
        // eslint-disable-next-line @typescript-eslint/no-misused-promises
        async onRowAdded() {
            await TotalTaxCalculator.getInstance().updateTaxDetails(
                this.taxDetails,
                this.totalTaxAmountAdjusted,
                this.totalTaxAmount,
            );
            await this.calculateTaxExcludedTotalAmount();
            recalculateTaxCalculationStatus(this.lines, this.taxCalculationStatus);
            this.disableSomeHeaderPropertiesIfLines();
        },
        optionsMenu: [
            { title: 'All statuses', id: 'allStatuses', graphQLFilter: {} },
            { title: 'All open statuses', id: 'allOpenStatuses', graphQLFilter: { status: { _ne: 'closed' } } },
            {
                title: 'Allocation required',
                id: 'allocationRequired',
                graphQLFilter: { allocationStatus: { _nin: ['notManaged', 'allocated'] } },
            },
            {
                title: 'My selected data',
                id: 'mySelectedData',
                graphQLFilter: { _id: { _in: [ui.QUERY_PARAM_CUSTOM_MAIN_LIST_FILTER] } },
            },
        ],
        optionsMenuType: 'dropdown',
        inlineActions: [
            {
                icon: 'box_arrow_left',
                title: 'Open line panel',
                id: 'openSidebar',
                onClick(rowId: string) {
                    this.lines.openSidebar(rowId);
                },
            },
        ],
        dropdownActions: [
            {
                icon: 'unlocked',
                title: 'Open',
                id: 'open',
                isDisabled(_rowId, rowItem) {
                    if (rowItem.status === 'closed') {
                        return rowItem.shippingStatus === 'shipped' || this.$.isDirty;
                    }
                    return false;
                },
                isHidden(_rowId, rowItem) {
                    return rowItem.status !== 'closed';
                },
                async onClick(rowId, rowItem) {
                    await this.clickOnLineOpenButton(rowId, rowItem);
                },
            },
            {
                icon: 'locked',
                title: 'Close',
                id: 'close',
                isDisabled(_rowId, rowItem) {
                    return (
                        rowItem.status === 'closed' ||
                        this.$.isDirty ||
                        (rowItem.item?.isStockManaged && rowItem.allocationStatus !== 'notAllocated') ||
                        false
                    );
                },
                isHidden(_rowId, rowItem) {
                    return (
                        (rowItem.status === 'closed' &&
                            ['shipped', 'notShipped'].includes(rowItem.shippingStatus ?? '')) ||
                        rowItem.allocationRequestStatus === 'inProgress'
                    );
                },
                async onClick(rowId, rowItem) {
                    const isAssignmentChecked = await isAssignmentCheckedOnSalesOrderLineClose(this, rowItem);
                    if (isAssignmentChecked) {
                        await this.clickOnLineCloseButton(rowId, rowItem);
                    }
                },
            },
            {
                icon: 'refresh',
                title: 'Update price',
                id: 'updatePrice',
                isDisabled(_rowId, rowItem) {
                    return rowItem.status === 'closed';
                },
                isHidden(rowId: string) {
                    return Number(rowId) <= 0;
                },
                async onClick(_rowId: string, rowItem: ui.PartialCollectionValue<SalesOrderLineBinding>) {
                    if (Number(rowItem._id) > 0) {
                        await this.priceDetermination(rowItem);
                        await this.updateGrossProfit(rowItem);
                    }
                },
            },
            {
                icon: 'plus',
                title: 'Create work order',
                id: 'createWorkOrder',
                isDisabled(_rowId, rowItem) {
                    const draftClose: SalesOrderStatus[] = ['quote', 'closed'];
                    return (
                        !this.$.recordId ||
                        this.$.isDirty ||
                        (rowItem.status && draftClose.includes(rowItem.status)) ||
                        Number(rowItem.remainingQuantityToShipInStockUnit) <= 0
                    );
                },
                isHidden(_rowId, rowItem) {
                    return !(
                        rowItem.item &&
                        rowItem.item.isManufactured &&
                        !rowItem.uWorkOrderLine &&
                        !rowItem.uPurchaseOrderLine &&
                        isOrderToOrderServiceOptionActivated(rowItem)
                    );
                },
                async onClick(_rowId: string, rowData: ui.PartialCollectionValue<SalesOrderLine>) {
                    if (this.isOnHold.value) {
                        if (this.site.value?.legalCompany?.customerOnHoldCheck === 'blocking') {
                            SalesOrderCommonAction.showOnHoldBlockingMessage(this);
                            return;
                        }

                        if (this.site.value?.legalCompany?.customerOnHoldCheck === 'warning') {
                            const accept = await actionFunctions.setSalesOrderToWoPoMessage(this);
                            if (!accept) return;
                        }
                    }
                    const isWorkOrderCreated = await checkAndCreateOrderAssignment(
                        this,
                        rowData,
                        'salesOrderLine',
                        'workOrder',
                    );
                    if (isWorkOrderCreated) {
                        await this.$.router.refresh();
                    }
                },
            },
            {
                icon: 'plus',
                title: 'Create purchase order',
                id: 'createPurchaseOrder',
                isDisabled(_rowId, rowItem) {
                    const draftClose: SalesOrderStatus[] = ['quote', 'closed'];
                    return (
                        !this.$.recordId ||
                        this.$.isDirty ||
                        (rowItem.status && draftClose.includes(rowItem.status)) ||
                        Number(rowItem.remainingQuantityToShipInStockUnit) <= 0
                    );
                },
                isHidden(_rowId, rowItem) {
                    return !(
                        rowItem.item &&
                        rowItem.item.isBought &&
                        !rowItem.uWorkOrderLine &&
                        !rowItem.uPurchaseOrderLine &&
                        isOrderToOrderServiceOptionActivated(rowItem)
                    );
                },
                async onClick(_rowId: string, rowData: ui.PartialCollectionValue<SalesOrderLine>) {
                    if (this.isOnHold.value) {
                        if (this.site.value?.legalCompany?.customerOnHoldCheck === 'blocking') {
                            SalesOrderCommonAction.showOnHoldBlockingMessage(this);
                            return;
                        }

                        if (this.site.value?.legalCompany?.customerOnHoldCheck === 'warning') {
                            const accept = await actionFunctions.setSalesOrderToWoPoMessage(this);
                            if (!accept) return;
                        }
                    }

                    const isPurchaseOrderCreated = await checkAndCreateOrderAssignment(
                        this,
                        rowData,
                        'salesOrderLine',
                        'purchaseOrderLine',
                    );
                    if (isPurchaseOrderCreated) {
                        await this.$.router.refresh();
                    }
                },
            },
            {
                icon: 'file_generic',
                title: 'Assign order',
                id: 'assignOrder',
                isHidden(_rowId, rowItem) {
                    const closed: SalesOrderStatus = 'closed';
                    return (
                        !isOrderToOrderServiceOptionActivated(rowItem) ||
                        (rowItem.uAssignmentOrder === '' && rowItem.status === closed) ||
                        (rowItem.uAssignmentOrder === undefined && rowItem.status === closed) ||
                        (this.isOnHold.value && this.site.value?.legalCompany?.customerOnHoldCheck === 'blocking') ||
                        false
                    );
                },
                async onClick(_rowId: string, rowData: ui.PartialCollectionValue<SalesOrderLine>) {
                    if (this.isOnHold.value && this.site.value?.legalCompany?.customerOnHoldCheck === 'warning') {
                        const accept = await actionFunctions.setSalesOrderToAssignedOrderMessage(this);
                        if (!accept) return;
                    }

                    await this.handleAssignOrderButtonClick(rowData);
                    await this.$.router.refresh();
                },
            },
            ui.menuSeparator(),
            {
                icon: 'three_boxes',
                title: 'Allocate stock',
                id: 'allocateStock',
                async onClick(rowId: string, rowItem: ui.PartialCollectionValue<SalesOrderLineBinding>) {
                    const isAssignmentChecked = await isAssignmentCheckedOnSalesOrderLineAllocate(this, rowItem);
                    if (isAssignmentChecked) {
                        const line = MasterDataUtils.removeExtractEdgesPartial(rowItem);
                        const allocationChanged = await MasterDataUtils.confirmDialogToBoolean(
                            StockDetailHelper.editStockDetails(this as any, line, {
                                movementType: 'allocation',
                                data: {
                                    isEditable: ['pending', 'inProgress'].includes(line.status),
                                    needFullAllocation: false,
                                    cannotOverAllocate: true,
                                    documentLineHasAlreadySomeAllocations: Number(line.quantityAllocated) > 0,
                                    effectiveDate: this.shippingDate.value ?? '',
                                    documentLineSortValue: line._sortValue,
                                    documentLine: rowId,
                                    item: line.item?._id,
                                    stockSite: this.stockSite.value?._id,
                                    quantity: +line.remainingQuantityToShipInStockUnit,
                                    unit: line.item.stockUnit?._id,
                                    searchCriteria: {
                                        activeQuantityInStockUnit: +line.remainingQuantityToShipInStockUnit,
                                        item: line.item?._id,
                                        site: this.stockSite.value?._id ?? '',
                                        stockUnit: line.item.stockUnit?._id,
                                        statusList: [{ statusType: 'accepted' }],
                                    },
                                },
                            }),
                        );
                        if (allocationChanged) {
                            await this.lines.refresh();
                            await this.allocationStatus.refresh();
                            this.manageDisplayButtonAllOtherActions(false);
                        }
                    }
                },
                isDisabled(_rowId: string, rowItem: ui.PartialCollectionValue<SalesOrderLineBinding>) {
                    return (
                        this.$.isDirty ||
                        ['closed', 'quote'].includes(rowItem.status ?? '') ||
                        rowItem.allocationRequestStatus === 'inProgress'
                    );
                },
                isHidden(_rowId: string, rowItem: ui.PartialCollectionValue<SalesOrderLineBinding>) {
                    return (
                        rowItem.status === 'closed' ||
                        !rowItem.item?.isStockManaged ||
                        rowItem.allocationRequestStatus === 'inProgress'
                    );
                },
            },
            {
                icon: 'three_boxes',
                title: 'Projected stock',
                id: 'projectedStock',
                async onClick(_rowId: string, rowItem: ui.PartialCollectionValue<SalesOrderLineBinding>) {
                    await this.$.dialog.page(
                        '@sage/xtrem-stock-data/ProjectedStockDialog',
                        {
                            item: JSON.stringify(rowItem.item),
                            site: JSON.stringify(this.stockSite.value),
                        },
                        {
                            rightAligned: false,
                            size: 'extra-large',
                            resolveOnCancel: true,
                        },
                    );
                },
                isHidden(_rowId: string, rowItem: ui.PartialCollectionValue<SalesOrderLineBinding>) {
                    return !rowItem?.item?.isStockManaged;
                },
            },
            {
                icon: 'three_boxes',
                title: 'Manage allocations',
                id: 'manageAllocations',
                async onClick(rowId: string, rowItem: ui.PartialCollectionValue<SalesOrderLineBinding>) {
                    await this.transferAllocationDialog(rowId, rowItem);
                },
                isHidden(_rowId: string, rowItem: ui.PartialCollectionValue<SalesOrderLineBinding>) {
                    const isStockManaged = rowItem?.item?.isStockManaged;
                    return !isStockManaged || this.status.value === 'closed';
                },
                isDisabled(_rowId: string, rowItem: ui.PartialCollectionValue<SalesOrderLineBinding>) {
                    return (
                        rowItem.allocationStatus === 'allocated' ||
                        this.status.value === 'quote' ||
                        rowItem.shippingStatus === 'shipped'
                    );
                },
            },
            ui.menuSeparator(),
            {
                icon: 'none',
                title: 'Dimensions',
                id: 'dimensions',
                async onClick(_rowId, rowItem: ui.PartialCollectionValue<SalesOrderLineBinding>) {
                    const rowData = dimensionPanelHelpers.fromPartialToBaseDocumentLineWithAnalytical(rowItem);
                    await MasterDataUtils.applyPanelToLineIfChanged(
                        this.lines,
                        dimensionPanelHelpers.editDisplayDimensions(
                            this,
                            {
                                documentLine: rowData,
                            },
                            {
                                editable: rowItem.status !== 'closed',
                            },
                        ),
                    );
                },
            },
            {
                title: 'Price list',
                icon: 'none',
                id: 'priceList',
                onError(error) {
                    if (!(error instanceof Error) && typeof error !== 'string') {
                        return noop();
                    }
                    return MasterDataUtils.formatError(this, error as any);
                },
                async onClick(_id, recordValue) {
                    if (!recordValue.priceReason && recordValue.quantity && recordValue.item) {
                        await openItemCustomerPriceList(
                            this,
                            recordValue.item._id ?? '',
                            this.soldToCustomer.value?._id ?? '',
                            this.site.value?._id ?? '',
                            this.stockSite.value?._id ?? '',
                            recordValue.item.name ?? '',
                            this.date.value ?? '',
                        );
                    }
                },
            },
            {
                icon: 'none',
                title: 'Tax details',
                id: 'taxDetails',
                isHidden(_rowId: string, rowItem: ui.PartialCollectionValue<SalesOrderLineBinding>) {
                    return !rowItem.uiTaxes;
                },
                async onClick(_rowId: string, rowItem: ui.PartialCollectionValue<SalesOrderLineBinding>) {
                    if (rowItem.uiTaxes) {
                        await this.callDisplayTaxes(rowItem);
                    }
                },
            },
            ui.menuSeparator(),
            {
                icon: 'bin',
                title: 'Delete',
                id: 'delete',
                isDestructive: true,
                isDisabled(_rowId, rowItem) {
                    return (
                        rowItem.status === 'inProgress' ||
                        this.status.value === 'closed' ||
                        (rowItem.status === 'closed' && (rowItem.shippingStatus || 'notShipped') !== 'notShipped') ||
                        (this.$.recordId &&
                            rowItem.item?.isStockManaged &&
                            (rowItem.allocationStatus || 'notAllocated') !== 'notAllocated') ||
                        false
                    );
                },
                isHidden(_rowId, rowItem) {
                    if (
                        rowItem.status === 'closed' &&
                        ['shipped', 'notShipped'].includes(rowItem.shippingStatus ?? '')
                    ) {
                        return true;
                    }
                    return false;
                },
                async onClick(rowId, rowItem) {
                    if (rowItem.allocationRequestStatus === 'inProgress') {
                        throw new Error(
                            ui.localize(
                                '@sage/xtrem-sales/pages__sales_order__cannot_delete_line_allocation_request_in_progress',
                                'Automatic allocation needs to finish before you can delete the line.',
                            ),
                        );
                    }
                    const isAssignmentChecked = await isAssignmentCheckedOnSalesOrderLineDelete(this, rowItem);
                    if (isAssignmentChecked) {
                        if (
                            await confirmDialogWithAcceptButtonText(
                                this,
                                ui.localize(
                                    '@sage/xtrem-sales/pages__sales_order__line_delete_action_dialog_title',
                                    'Confirm delete',
                                ),
                                ui.localize(
                                    '@sage/xtrem-sales/pages__sales_order__line_delete_action_dialog_content',
                                    'You are about to delete this sales order line. This action cannot be undone.',
                                ),
                                ui.localize('@sage/xtrem-sales/pages-confirm-continue', 'Continue'),
                            )
                        ) {
                            this.lines.removeRecord(rowId);
                            await TotalTaxCalculator.getInstance().updateTaxData(
                                JSON.parse(rowItem.uiTaxes ?? '{}'),
                                null,
                            );
                            this.totalGrossProfit.value =
                                Number(this.totalGrossProfit.value ?? 0) - Number(rowItem.grossProfitAmount ?? 0);
                            await this.calculateTaxExcludedTotalAmount();
                            recalculateTaxCalculationStatus(this.lines, this.taxCalculationStatus);
                            this.disableSomeHeaderPropertiesIfLines();
                        }
                    }
                },
            },
        ],

        mobileCard: {
            // Deep binding of images is not possible - check XT-35767
            image: ui.nestedFields.image({ bind: { item: { image: true } } }),
            title: ui.nestedFields.text({ bind: { item: { name: true } }, title: 'Product', isTitleHidden: true }),
            titleRight: ui.nestedFields.label({
                bind: 'status',
                title: 'Status',
                isTitleHidden: true,
                optionType: '@sage/xtrem-sales/SalesOrderStatus',
                style: (_id, rowData) => PillColorSales.getLabelColorByStatus('SalesOrderStatus', rowData?.status),
            }),
            line2: ui.nestedFields.text({ bind: 'itemDescription', title: 'Description', canFilter: false }),
            line2Right: ui.nestedFields.numeric({
                bind: 'amountIncludingTax',
                title: 'Amount',
                canFilter: false,
                unit() {
                    return this.currency.value;
                },
                scale: null,
            }),
        },
        sidebar: {
            title(_id, recordValue) {
                if (!recordValue || +recordValue._id < 0) {
                    return ui.localize('@sage/xtrem-sales/edit-create-line', 'Add new line');
                }
                return `${recordValue.item.name} - ${recordValue.item.id}`;
            },
            headerDropdownActions: [
                {
                    icon: 'unlocked',
                    title: 'Open',
                    isDisabled(_rowId, rowItem) {
                        if (rowItem.status === 'closed') {
                            return rowItem.shippingStatus === 'shipped' || this.$.isDirty;
                        }
                        return false;
                    },
                    isHidden(_rowId, rowItem) {
                        return rowItem.status !== 'closed';
                    },
                    async onClick(rowId, rowItem) {
                        await this.clickOnLineOpenButton(rowId, rowItem);
                    },
                },
                {
                    icon: 'locked',
                    title: 'Close',
                    isDisabled(_rowId, rowItem) {
                        return (
                            this.$.isDirty ||
                            (rowItem.item?.isStockManaged && rowItem.allocationStatus !== 'notAllocated') ||
                            false
                        );
                    },
                    isHidden(_rowId, rowItem) {
                        return (
                            (rowItem.status === 'closed' &&
                                rowItem.shippingStatus &&
                                ['shipped', 'notShipped'].includes(rowItem.shippingStatus)) ||
                            rowItem.allocationRequestStatus === 'inProgress'
                        );
                    },
                    async onClick(rowId, rowItem) {
                        const isAssignmentChecked = await isAssignmentCheckedOnSalesOrderLineClose(this, rowItem);
                        if (isAssignmentChecked) {
                            await this.clickOnLineCloseButton(rowId, rowItem);
                        }
                    },
                },
                {
                    icon: 'refresh',
                    title: 'Update price',
                    isDisabled(_rowId, rowItem) {
                        return rowItem.status === 'closed';
                    },
                    isHidden(rowId: string) {
                        return Number(rowId) <= 0;
                    },
                    async onClick(_rowId: string, rowItem: ui.PartialCollectionValue<SalesOrderLineBinding>) {
                        if (Number(rowItem._id) > 0) {
                            await this.priceDetermination(rowItem);
                        }
                    },
                },
                {
                    icon: 'plus',
                    title: 'Create work order',
                    isDisabled(_rowId, rowItem) {
                        const draftClose: SalesOrderStatus[] = ['quote', 'closed'];
                        return (
                            !this.$.recordId ||
                            this.$.isDirty ||
                            (rowItem.status && draftClose.includes(rowItem.status)) ||
                            Number(rowItem.remainingQuantityToShipInStockUnit) <= 0
                        );
                    },
                    isHidden(_rowId, rowItem) {
                        return !(
                            rowItem.item &&
                            rowItem.item.isManufactured &&
                            !rowItem.uWorkOrderLine &&
                            !rowItem.uPurchaseOrderLine &&
                            isOrderToOrderServiceOptionActivated(rowItem)
                        );
                    },
                    async onClick(_rowId: string, rowData: ui.PartialCollectionValue<SalesOrderLine>) {
                        if (this.isOnHold.value) {
                            if (this.site.value?.legalCompany?.customerOnHoldCheck === 'blocking') {
                                SalesOrderCommonAction.showOnHoldBlockingMessage(this);
                                return;
                            }

                            if (this.site.value?.legalCompany?.customerOnHoldCheck === 'warning') {
                                const accept = await actionFunctions.setSalesOrderToWoPoMessage(this);
                                if (!accept) return;
                            }
                        }
                        const isWorkOrderCreated = await checkAndCreateOrderAssignment(
                            this,
                            rowData,
                            'salesOrderLine',
                            'workOrder',
                        );
                        if (isWorkOrderCreated) {
                            await this.$.router.refresh();
                        }
                    },
                },
                {
                    icon: 'plus',
                    title: 'Create purchase order',
                    isDisabled(_rowId, rowItem) {
                        const draftClose: SalesOrderStatus[] = ['quote', 'closed'];
                        return (
                            !this.$.recordId ||
                            this.$.isDirty ||
                            (rowItem.status && draftClose.includes(rowItem.status)) ||
                            Number(rowItem.remainingQuantityToShipInStockUnit) <= 0
                        );
                    },
                    isHidden(_rowId, rowItem) {
                        return !(
                            rowItem.item &&
                            rowItem.item.isBought &&
                            !rowItem.uWorkOrderLine &&
                            !rowItem.uPurchaseOrderLine &&
                            isOrderToOrderServiceOptionActivated(rowItem)
                        );
                    },
                    async onClick(_rowId: string, rowData: ui.PartialCollectionValue<SalesOrderLine>) {
                        if (this.isOnHold.value) {
                            if (this.site.value?.legalCompany?.customerOnHoldCheck === 'blocking') {
                                SalesOrderCommonAction.showOnHoldBlockingMessage(this);
                                return;
                            }

                            if (this.site.value?.legalCompany?.customerOnHoldCheck === 'warning') {
                                const accept = await actionFunctions.setSalesOrderToWoPoMessage(this);
                                if (!accept) return;
                            }
                        }
                        const isPurchaseOrderCreated = await checkAndCreateOrderAssignment(
                            this,
                            rowData,
                            'salesOrderLine',
                            'purchaseOrderLine',
                        );
                        if (isPurchaseOrderCreated) {
                            await this.$.router.refresh();
                        }
                    },
                },
                {
                    icon: 'file_generic',
                    title: 'Assign order',
                    isDisabled(_rowId: string, rowItem: any): boolean {
                        const draftClose: SalesOrderStatus[] = ['quote', 'closed'];
                        return (
                            !this.$.recordId ||
                            this.$.isDirty ||
                            (rowItem.status && draftClose.includes(rowItem.status)) ||
                            Number(rowItem.remainingQuantityToShipInStockUnit) <= 0
                        );
                    },
                    isHidden(_rowId, rowItem) {
                        return !(
                            (rowItem.item &&
                                (rowItem.item.isManufactured || rowItem.item.isBought) &&
                                (rowItem.uWorkOrderLine || rowItem.uPurchaseOrderLine) &&
                                isOrderToOrderServiceOptionActivated(rowItem)) ||
                            (this.isOnHold.value && this.site.value?.legalCompany?.customerOnHoldCheck === 'blocking')
                        );
                    },
                    async onClick(_rowId: string, rowData: ui.PartialCollectionValue<SalesOrderLine>) {
                        if (this.isOnHold.value && this.site.value?.legalCompany?.customerOnHoldCheck === 'warning') {
                            const accept = await actionFunctions.setSalesOrderToAssignedOrderMessage(this);
                            if (!accept) return;
                        }

                        await this.handleAssignOrderButtonClick(rowData);
                        await this.$.router.refresh();
                    },
                },
                ui.menuSeparator(),
                {
                    icon: 'three_boxes',
                    title: 'Projected stock',
                    async onClick(_rowId: string, rowItem: ui.PartialCollectionValue<SalesOrderLineBinding>) {
                        await this.$.dialog.page(
                            '@sage/xtrem-stock-data/ProjectedStockDialog',
                            {
                                item: JSON.stringify(rowItem.item),
                                site: JSON.stringify(this.stockSite.value),
                            },
                            {
                                rightAligned: false,
                                size: 'extra-large',
                                resolveOnCancel: true,
                            },
                        );
                    },
                    isHidden(_rowId: string, rowItem: ui.PartialCollectionValue<SalesOrderLineBinding>) {
                        return !rowItem?.item?.isStockManaged;
                    },
                },
                {
                    icon: 'three_boxes',
                    title: 'Manage allocations',
                    async onClick(rowId: string, rowItem: ui.PartialCollectionValue<SalesOrderLineBinding>) {
                        await this.transferAllocationDialog(rowId, rowItem);
                    },
                    isHidden(_rowId: string, rowItem: ui.PartialCollectionValue<SalesOrderLineBinding>) {
                        return !rowItem?.item?.isStockManaged || this.status.value === 'closed';
                    },
                    isDisabled(_rowId: string, rowItem: ui.PartialCollectionValue<SalesOrderLineBinding>) {
                        return (
                            rowItem.allocationStatus === 'allocated' ||
                            this.status.value === 'quote' ||
                            rowItem.shippingStatus === 'shipped'
                        );
                    },
                },
                ui.menuSeparator(),
                {
                    icon: 'none',
                    title: 'Dimensions',
                    async onClick(_rowId, rowItem: ui.PartialCollectionValue<SalesOrderLineBinding>) {
                        const rowData = dimensionPanelHelpers.fromPartialToBaseDocumentLineWithAnalytical(rowItem);
                        await MasterDataUtils.applyPanelToLineIfChanged(
                            this.lines,
                            dimensionPanelHelpers.editDisplayDimensions(
                                this,
                                {
                                    documentLine: rowData,
                                },
                                {
                                    editable: rowItem.status !== 'closed',
                                },
                            ),
                        );
                    },
                },
                {
                    title: 'Price list',
                    icon: 'none',
                    onError(error) {
                        if (!(error instanceof Error) && typeof error !== 'string') {
                            return noop();
                        }
                        return MasterDataUtils.formatError(this, error as any);
                    },
                    async onClick(_id, recordValue) {
                        if (!recordValue.priceReason && recordValue.quantity && recordValue.item) {
                            await openItemCustomerPriceList(
                                this,
                                recordValue.item._id ?? '',
                                this.soldToCustomer.value?._id ?? '',
                                this.site.value?._id ?? '',
                                this.stockSite.value?._id ?? '',
                                recordValue.item.name ?? '',
                                this.date.value ?? '',
                            );
                        }
                    },
                },
                {
                    icon: 'none',
                    title: 'Tax details',
                    isHidden(_rowId: string, rowItem: ui.PartialCollectionValue<SalesOrderLineBinding>) {
                        return !rowItem.uiTaxes;
                    },
                    async onClick(_rowId: string, rowItem: ui.PartialCollectionValue<SalesOrderLineBinding>) {
                        if (rowItem.uiTaxes) {
                            await this.callDisplayTaxes(rowItem);
                        }
                    },
                },
                ui.menuSeparator(),
                {
                    icon: 'bin',
                    title: 'Delete',
                    isDestructive: true,
                    isDisabled(_rowId, rowItem) {
                        return (
                            rowItem.status === 'inProgress' ||
                            this.status.value === 'closed' ||
                            (rowItem.status === 'closed' &&
                                (rowItem.shippingStatus || 'notShipped') !== 'notShipped') ||
                            (this.$.recordId &&
                                rowItem.item?.isStockManaged &&
                                (rowItem.allocationStatus || 'notAllocated') !== 'notAllocated') ||
                            false
                        );
                    },
                    isHidden(_rowId, rowItem) {
                        if (
                            rowItem.shippingStatus &&
                            rowItem.status === 'closed' &&
                            ['shipped', 'notShipped'].includes(rowItem.shippingStatus)
                        ) {
                            return true;
                        }
                        return false;
                    },
                    onClick(id) {
                        this.lines.removeRecord(id);
                    },
                },
            ],
            headerQuickActions: [],
            async onRecordOpened(_id, recordValue: SalesOrderLineBinding | undefined) {
                this.currentSelectedLineId = recordValue?._id ?? '';
                this.internalNoteLine.value = recordValue?.internalNote?.value ?? '';
                this.externalNoteLine.value = recordValue?.externalNote?.value ?? '';
                this.isExternalNoteLine.value = recordValue?.isExternalNote ?? null;
                this.externalNoteLine.isDisabled = !recordValue?.isExternalNote || recordValue.status === 'closed';
                this.internalNoteLine.isDisabled = recordValue?.status === 'closed';
                this.isExternalNoteLine.isDisabled = recordValue?.status === 'closed';
                if (recordValue?.shipToAddress) {
                    this.shipToAddressLineDetail.value = recordValue.shipToAddress;
                    this.shipToAddressLineDetail.value.concatenatedAddress = getConcatenatedAddress(
                        recordValue.shipToAddress,
                    );
                } else {
                    this.shipToAddressLineDetail.value = this.shipToAddress.value;
                    if (this.shipToAddressLineDetail.value && this.shipToAddress.value) {
                        this.shipToAddressLineDetail.value._id = _id;
                        this.shipToAddressLineDetail.value.concatenatedAddress = getConcatenatedAddress(
                            this.shipToAddress.value,
                        );
                    }
                }

                if (Number(recordValue?._id) > 0) {
                    this.oldSalesUnit = recordValue?.unit?._id;
                    await this.checkItemCustomer(recordValue as unknown as ExtractEdgesPartial<SalesOrderLineBinding>);
                    TotalTaxCalculator.getInstance().synchronizeState(
                        JSON.parse(
                            JSON.stringify(TotalTaxCalculator.getInstance().getState()).toString(),
                        ) as TotalTaxCalculatorState,
                    );
                    await this.fillSalesShipmentLines(recordValue?._id ?? '');
                    await this.fillSalesInvoiceLines(recordValue?._id ?? '');
                } else {
                    if (recordValue) {
                        recordValue.site = this.site.value as unknown as Site;
                        recordValue.stockSite = this.stockSite.value as unknown as Site;
                        recordValue.shippingStatus = 'notShipped';
                        recordValue.requestedDeliveryDate = this.requestedDeliveryDate.value ?? '';
                        recordValue.expectedDeliveryDate = this.expectedDeliveryDate.value ?? '';
                        recordValue.doNotShipBeforeDate = this.doNotShipBeforeDate.value ?? '';
                        recordValue.doNotShipAfterDate = this.doNotShipAfterDate.value ?? '';
                        recordValue.deliveryLeadTime = +(this.deliveryLeadTime.value ?? '');
                        recordValue.deliveryMode = this.deliveryMode.value as unknown as DeliveryMode;
                        recordValue.shippingDate = this.shippingDate.value ?? '';
                        recordValue.priceOriginDeterminated = 'manual';
                        recordValue.status = this.status.value === 'quote' ? 'quote' : 'pending';
                    }

                    if (this._defaultDimensionsAttributes && this._defaultDimensionsAttributes.action && recordValue) {
                        const line = dimensionPanelHelpers.applyDefaultDimensionsOnNewLineIfNeeded(
                            {} as ui.PartialNodeWithId<SalesOrderLine>,
                            this._defaultDimensionsAttributes,
                        );
                        recordValue.storedAttributes = line.storedAttributes;
                        recordValue.storedDimensions = line.storedDimensions;
                    }
                    this.lines.addOrUpdateRecordValue(
                        recordValue as unknown as ExtractEdgesPartial<SalesOrderLineBinding>,
                    );
                }
                this.allocationFields = ['allocationStatus'];
                if (!['noRequest', 'completed'].includes(recordValue?.allocationRequestStatus)) {
                    this.allocationFields.push('allocationRequestStatus');
                }
            },
            async onRecordConfirmed(_id, recordValue) {
                if (recordValue) {
                    recordValue.internalNote.value = this.internalNoteLine.value ? this.internalNoteLine.value : '';
                    recordValue.externalNote.value = this.externalNoteLine.value ? this.externalNoteLine.value : '';
                    recordValue.isExternalNote = this.isExternalNoteLine.value || false;

                    // recordValue.deliveryMode = this.deliveryModeLine.value as unknown as DeliveryMode;
                    // recordValue.deliveryLeadTime = this.deliveryLeadTimeLine.value;
                    // recordValue.shipToCustomerAddress = this.shipToAddressLine.value as unknown as CustomerDeliveryAddress;

                    this.shipToAddressLineDetail.isReadOnly = true;
                    if (this.shipToAddressLineDetail.value) {
                        this.shipToAddressLineDetail.value.concatenatedAddress = getConcatenatedAddress(
                            this.shipToAddressLineDetail.value,
                        );
                    }

                    recordValue.shipToAddress = this.shipToAddressLineDetail.value as unknown as Address;

                    // TODO: due to XT-60498 we agree to disable this for the moment
                    // recordValue.uiTaxes = resetUiTaxes(recordValue.uiTaxes);
                    // await this.calculatePrices(recordValue as unknown as ExtractEdgesPartial<SalesOrderLineBinding>);
                    // this.updateGrossProfit(recordValue as unknown as ExtractEdgesPartial<SalesOrderLineBinding>);

                    this.lines.addOrUpdateRecordValue(
                        recordValue as unknown as ExtractEdgesPartial<SalesOrderLineBinding>,
                    );
                    await TotalTaxCalculator.getInstance().updateTaxDetails(
                        this.taxDetails,
                        this.totalTaxAmountAdjusted,
                        this.totalTaxAmount,
                    );
                    await this.updateGrossProfit(recordValue as unknown as ExtractEdgesPartial<SalesOrderLineBinding>);
                    await this.calculateTaxExcludedTotalAmount();
                    recalculateTaxCalculationStatus(this.lines, this.taxCalculationStatus);
                }
            },

            layout() {
                return {
                    information: {
                        title: ui.localize('@sage/xtrem-sales/pages_sidebar_tab_title_information', 'Information'),
                        blocks: {
                            mainBlock: {
                                fields: ['item', 'origin', 'itemDescription'],
                            },
                            siteBlock: {
                                fields: ['stockSite'],
                            },
                            sales: {
                                title: ui.localize('@sage/xtrem-sales/pages_sidebar_block_title_sales', 'Sales'),
                                fields: ['unit', 'quantity'],
                            },
                            stock: {
                                isHidden(_rowId, rowItem) {
                                    return (
                                        (rowItem &&
                                            (!rowItem.item?.isStockManaged || rowItem.item.type === 'service')) ||
                                        false
                                    );
                                },
                                title: ui.localize('@sage/xtrem-sales/pages_sidebar_block_title_stock', 'Stock'),
                                fields: ['stockUnit', 'quantityInStockUnit', 'unitToStockUnitConversionFactor'],
                            },
                        },
                    },
                    price: {
                        title: ui.localize('@sage/xtrem-sales/pages_sidebar_tab_title_price', 'Price'),
                        blocks: {
                            mainBlock: {
                                fields: ['grossPrice', 'discount', 'charge', 'netPrice'],
                            },
                            mainBlock1: {
                                fields: ['priceOrigin', 'priceReason'],
                            },
                            totals: {
                                title: ui.localize('@sage/xtrem-sales/pages_sidebar_block_title_totals', 'Totals'),
                                fields: ['amountExcludingTax', 'taxAmount', 'amountIncludingTax'],
                            },
                            totals2: {
                                fields: [
                                    'stockCostAmount',
                                    'grossProfitAmount',
                                    'amountExcludingTaxInCompanyCurrency',
                                    'amountIncludingTaxInCompanyCurrency',
                                ],
                            },
                        },
                    },
                    stock: {
                        title: ui.localize('@sage/xtrem-sales/pages_sidebar_tab_title_stock', 'Stock'),
                        isHidden(rowId, recordValue) {
                            return SalesOrder.isSidebarStockSectionHidden(rowId, recordValue);
                        },
                        blocks: {
                            stockStatus: {
                                isHidden(_rowId, recordValue) {
                                    return (
                                        +(recordValue?._id ?? 0) <= 0 ||
                                        +(recordValue?.stockShortageInStockUnit ?? 0) === 0
                                    );
                                },
                                fields: ['stockShortageStatus'],
                            },
                            stockQuantity: {
                                fields: ['stockOnHand', 'availableQuantityInStockUnit', 'stockShortageInStockUnit'],
                            },
                            allocationStatus: {
                                isHidden(_rowId, recordValue) {
                                    return SalesOrder.isSidebarAllocationBlockHidden(_rowId, recordValue);
                                },
                                // allocationFields is built in the event 'onRecordOpened'
                                fields: this.allocationFields,
                            },
                            allocationQuantity: {
                                isHidden(_rowId, recordValue) {
                                    return SalesOrder.isSidebarAllocationBlockHidden(_rowId, recordValue);
                                },
                                fields: [
                                    'remainingQuantityToShipInStockUnit',
                                    'quantityAllocated',
                                    'remainingQuantityToAllocate',
                                ],
                            },
                        },
                    },
                    address: {
                        title: ui.localize('@sage/xtrem-sales/pages_sidebar_tab_title_address', 'Address'),
                        blocks: {
                            mainBlock: {
                                fields: ['shipToCustomerAddress', this.shipToAddressLineDetail],
                            },
                        },
                    },
                    delivery: {
                        title: ui.localize('@sage/xtrem-sales/pages_sidebar_tab_title_delivery', 'Delivery'),
                        blocks: {
                            mainBlock: {
                                fields: ['shippingStatus'],
                            },
                            mainBlock2: {
                                fields: ['shippingDate', 'doNotShipBeforeDate', 'doNotShipAfterDate'],
                            },
                            mainBlock3: {
                                title: ui.localize('@sage/xtrem-sales/pages_sidebar_block_title_delivery', 'Delivery'),
                                fields: [
                                    'requestedDeliveryDate',
                                    'deliveryLeadTime',
                                    // this.deliveryLeadTimeLine,
                                    'expectedDeliveryDate',
                                    'deliveryMode',
                                    // this.deliveryModeLine,
                                ],
                            },
                        },
                    },
                    progress: {
                        title: ui.localize('@sage/xtrem-sales/pages_sidebar_tab_title_progress', 'Progress'),
                        isHidden() {
                            return !['inProgress', 'pending', 'closed'].includes(this.status.value ?? '');
                        },
                        blocks: {
                            shipping: {
                                title: ui.localize('@sage/xtrem-sales/pages_sidebar_block_title_shipment', 'Shipment'),
                                fields: [
                                    'shippingStatus',
                                    'quantity',
                                    'remainingQuantityToShipInSalesUnit',
                                    'quantityToShipInProgressInSalesUnit',
                                    'shippedQuantityInSalesUnit',
                                    this.salesShipmentLines,
                                ],
                            },
                            invoicing: {
                                title: ui.localize('@sage/xtrem-sales/pages_sidebar_block_title_invoice', 'Invoice'),
                                fields: [
                                    'invoiceStatus',
                                    'quantityToInvoiceInProgressInSalesUnit',
                                    'invoicedQuantityInSalesUnit',
                                    this.toInvoiceLines,
                                ],
                            },
                        },
                    },
                    notes: {
                        title: ui.localize('@sage/xtrem-sales/pages_sidebar_tab_title_line_notes', 'Line notes'),
                        blocks: {
                            notesBlock: {
                                fields: [this.internalNoteLine, this.isExternalNoteLine, this.externalNoteLine],
                            },
                        },
                    },
                };
            },
        },
    })
    lines: ui.fields.Table<SalesOrderLineBinding>;

    @ui.decorators.section<SalesOrder>({
        title: 'Information',
    })
    informationSection: ui.containers.Section;

    @ui.decorators.block<SalesOrder>({
        parent() {
            return this.informationSection;
        },
        width: 'large',
        title: 'Information',
        isTitleHidden: true,
    })
    informationBlock: ui.containers.Block;

    @ui.decorators.block<SalesOrder>({
        parent() {
            return this.headerSection;
        },
        width: 'large',
    })
    headerStepSequenceBlock: ui.containers.Block;

    @ui.decorators.block<SalesOrder>({
        parent() {
            return this.headerSection;
        },
    })
    headerBlock: ui.containers.Block;

    @ui.decorators.tile<SalesOrder>({
        parent() {
            return this.headerSection;
        },
    })
    tileContainer: ui.containers.Tile;

    @ui.decorators.countField<SalesOrder>({
        parent() {
            return this.tileContainer;
        },
        bind: 'lines',
        title: 'Number of items',
        size: 'medium',
    })
    salesOrderLineCount: ui.fields.Count;

    @ui.decorators.dateField<SalesOrder>({
        parent() {
            return this.tileContainer;
        },
        title: 'Expected delivery date',
        bind: 'expectedDeliveryDate',
        width: 'medium',
    })
    expectedDeliveryDateHeader: ui.fields.Date;

    @ui.decorators.aggregateField<SalesOrder>({
        parent() {
            return this.tileContainer;
        },
        aggregateOn: 'amountExcludingTax',
        aggregationMethod: 'sum',
        title: 'Total excluding tax',
        bind: 'lines',
        unit() {
            return this.currency.value;
        },
        scale: null,
        width: 'medium',
    })
    salesOrderExcludingTax: ui.fields.Aggregate;

    @ui.decorators.aggregateField<SalesOrder>({
        parent() {
            return this.tileContainer;
        },
        aggregateOn: 'amountIncludingTax',
        aggregationMethod: 'sum',
        title: 'Total including tax',
        bind: 'lines',
        unit() {
            return this.currency.value;
        },
        scale: null,
        width: 'medium',
    })
    salesOrderIncludingTax: ui.fields.Aggregate;

    @ui.decorators.checkboxField<SalesOrder>({
        parent() {
            return this.informationBlock;
        },
        title: 'Printed',
        isDisabled: true,
    })
    isPrinted: ui.fields.Checkbox;

    @ui.decorators.checkboxField<SalesOrder>({
        parent() {
            return this.informationBlock;
        },
        title: 'Sent',
        isDisabled: true,
    })
    isSent: ui.fields.Checkbox;

    @ui.decorators.checkboxField<SalesOrder>({
        parent() {
            return this.informationBlock;
        },
        isHidden: true,
    })
    isOrderAssignmentLinked: ui.fields.Checkbox;

    @ui.decorators.labelField<SalesOrder>({
        parent() {
            return this.informationBlock;
        },
        bind: 'allocationStatus',
        title: 'Stock allocation status',
        width: 'large',
        optionType: '@sage/xtrem-stock-data/StockAllocationStatus',
        isHidden() {
            return ['quote', 'closed'].includes(this.status.value ?? '');
        },
        style() {
            return PillColorStock.getLabelColorByStatus('StockAllocationStatus', this.allocationStatus.value);
        },
    })
    allocationStatus: ui.fields.Label<StockAllocationStatus>;

    @ui.decorators.labelField<SalesOrder>({
        parent() {
            return this.informationBlock;
        },
        title: 'Allocation request status',
        optionType: '@sage/xtrem-stock-data/AllocationRequestStatus',
        isHidden() {
            return ['noRequest', 'completed'].includes(this.allocationRequestStatus.value || '');
        },
        style() {
            return PillColorStock.getLabelColorByStatus('AllocationRequestStatus', this.allocationRequestStatus.value);
        },
    })
    allocationRequestStatus: ui.fields.Label<AllocationRequestStatus>;

    @ui.decorators.referenceField<SalesOrder, Currency>({
        parent() {
            return this.informationBlock;
        },
        node: '@sage/xtrem-master-data/Currency',
        tunnelPage: '@sage/xtrem-master-data/Currency',
        title: 'Transaction currency',
        lookupDialogTitle: 'Select currency',
        valueField: 'name',
        helperTextField: 'id',
        minLookupCharacters: 1,
        isAutoSelectEnabled: true,
        isMandatory: true,
        fetchesDefaults: true,
        isReadOnly() {
            return !!this.$.recordId || this.lines.value.length > 0;
        },
        columns: [
            ui.nestedFields.text({ bind: 'name', title: 'Name' }),
            ui.nestedFields.text({ bind: 'id', title: 'ISO 4217 code' }),
            ui.nestedFields.text({ bind: 'symbol', title: 'Symbol' }),
            ui.nestedFields.technical({ bind: 'decimalDigits' }),
        ],
        onChange() {
            this.setCurrencyUnitsForTotalAmounts();
            this.manageDisplayLinePhantomRow();
            this.manageDisplayButtonDefaultDimensionAction();
            this.rateDescription.isHidden = isExchangeRateHidden(
                this.currency.value,
                this.site.value,
                this.soldToCustomer.value,
            );
        },
    })
    currency: ui.fields.Reference<Currency>;

    @ui.decorators.textField<SalesOrder>({
        parent() {
            return this.informationBlock;
        },
        title: 'Exchange rate',
        isReadOnly: true,
        isHidden() {
            return isExchangeRateHidden(this.currency.value, this.site.value, this.soldToCustomer.value);
        },
    })
    rateDescription: ui.fields.Text;

    @ui.decorators.textField<SalesOrder>({
        parent() {
            return this.informationBlock;
        },
        title: 'Customer order reference',
    })
    customerNumber: ui.fields.Text;

    @ui.decorators.referenceField<SalesOrder, BusinessEntityAddress>({
        parent() {
            return this.informationBlock;
        },
        node: '@sage/xtrem-master-data/BusinessEntityAddress',
        title: 'Sold-to address',
        lookupDialogTitle: 'Select sold-to address',
        valueField: 'name',
        isHidden: true,
        columns: [
            ui.nestedFields.checkbox({ bind: 'isActive', title: 'Active' }),
            ui.nestedFields.text({ bind: 'name', title: 'Name' }),
            ui.nestedFields.text({ bind: 'addressLine1', title: 'Address line 1' }),
            ui.nestedFields.text({ bind: 'addressLine2', title: 'Address line 2' }),
            ui.nestedFields.text({ bind: 'city', title: 'City' }),
            ui.nestedFields.text({ bind: 'region', title: 'Region' }),
            ui.nestedFields.text({ bind: 'postcode', title: 'ZIP code' }),
            ui.nestedFields.reference<SalesOrder, BusinessEntityAddress, Country>({
                bind: 'country',

                columns: [
                    ui.nestedFields.text({ bind: 'name', title: 'Name' }),
                    ui.nestedFields.text({ bind: 'id', title: 'ISO 3166-1 alpha-2 code' }),
                    ui.nestedFields.technical({ bind: 'regionLabel' }),
                    ui.nestedFields.technical({ bind: 'zipLabel' }),
                ],
            }),
            ui.nestedFields.text({ bind: 'locationPhoneNumber', title: 'Phone number' }),
            ui.nestedFields.technical<SalesOrder, BusinessEntityAddress, BusinessEntity>({
                bind: 'businessEntity',
                node: '@sage/xtrem-master-data/BusinessEntity',
                nestedFields: [ui.nestedFields.technical({ bind: 'name' }), ui.nestedFields.technical({ bind: 'id' })],
            }),
        ],
        filter() {
            return this.soldToCustomer.value?.businessEntity?.id
                ? { businessEntity: { id: this.soldToCustomer.value.businessEntity.id } }
                : {};
        },
        async onChange() {
            await this.fetchDefaultsFromSoldToAddress();
        },
    })
    soldToLinkedAddress: ui.fields.Reference<BusinessEntityAddress>;

    @ui.decorators.vitalPodField<SalesOrder, Address>({
        parent() {
            return this.informationSection;
        },
        node: '@sage/xtrem-master-data/Address',
        title: 'Sold-to address',
        width: 'small',
        // bind: 'soldToAddress',
        onAddButtonClick() {
            if (this.soldToLinkedAddress.value) {
                const { ...values } = { ...this.soldToLinkedAddress.value };
                delete values.businessEntity;
                return { ...values, _id: null };
            }
            return {};
        },
        dropdownActions: [
            {
                icon: 'lookup',
                title: 'Replace',
                onClick() {
                    this.soldToLinkedAddress.openDialog();
                },
                isDisabled() {
                    return this.status.value === 'closed';
                },
            },
            {
                icon: 'edit',
                title: 'Edit',
                onClick() {
                    this.soldToAddress.isReadOnly = false;
                },
                isDisabled() {
                    return this.status.value === 'closed' || !this.soldToAddress.isReadOnly;
                },
            },
            {
                icon: 'tick',
                title: 'Read-only',
                onClick() {
                    this.soldToAddress.isReadOnly = true;
                    if (this.soldToAddress.value) {
                        this.soldToAddress.value.concatenatedAddress = getConcatenatedAddress(this.soldToAddress.value);
                    }
                },
                isDisabled() {
                    return this.status.value === 'closed' || this.soldToAddress.isReadOnly;
                },
            },
        ],
        isReadOnly: true,
        columns: [
            ui.nestedFields.text({
                bind: 'name',
                title: 'Name',
                isHidden() {
                    return this.soldToAddress.isReadOnly === true;
                },
            }),
            ui.nestedFields.textArea({
                title: 'Bill-to address',
                isTitleHidden: true,
                bind: 'concatenatedAddress',
                width: 'large',
                isHidden() {
                    return this.soldToAddress.isReadOnly === false;
                },
            }),
            ui.nestedFields.text({ bind: 'locationPhoneNumber', title: 'Phone number' }),
            ui.nestedFields.text({
                bind: 'addressLine1',
                title: 'Address line 1',
                width: 'large',
                isHidden() {
                    return this.soldToAddress.isReadOnly === true;
                },
            }),
            ui.nestedFields.text({
                bind: 'addressLine2',
                title: 'Address line 2',
                width: 'large',
                isHidden() {
                    return this.soldToAddress.isReadOnly === true;
                },
            }),
            ui.nestedFields.text({
                bind: 'city',
                title: 'City',
                isHidden() {
                    return this.soldToAddress.isReadOnly === true;
                },
            }),
            ui.nestedFields.text({
                bind: 'region',
                title() {
                    return getCountryRegionTitle(this.soldToAddress.value);
                },
                isHidden() {
                    return this.soldToAddress.isReadOnly === true;
                },
            }),
            ui.nestedFields.text({
                bind: 'postcode',
                title() {
                    return getCountryPostCodeTitle(this.soldToAddress.value);
                },
                isHidden() {
                    return this.soldToAddress.isReadOnly === true;
                },
            }),
            ui.nestedFields.reference({
                bind: 'country',

                columns: [
                    ui.nestedFields.text({ bind: 'name', title: 'Name' }),
                    ui.nestedFields.text({ bind: 'id', title: 'ISO 3166-1 alpha-2 code' }),
                    ui.nestedFields.technical({ bind: 'regionLabel' }),
                    ui.nestedFields.technical({ bind: 'zipLabel' }),
                ],
                isHidden() {
                    return this.soldToAddress.isReadOnly === true;
                },
            }),
        ],
    })
    soldToAddress: ui.fields.VitalPod;

    @ui.decorators.section<SalesOrder>({
        title: 'Shipping',
    })
    shippingSection: ui.containers.Section;

    @ui.decorators.block<SalesOrder>({
        parent() {
            return this.shippingSection;
        },
        width: 'large',
        title: 'Shipping',
        isTitleHidden: true,
    })
    shippingSectionShippingBlock: ui.containers.Block;

    @ui.decorators.referenceField<SalesOrder, BusinessEntityAddress>({
        parent() {
            return this.shippingSectionShippingBlock;
        },
        node: '@sage/xtrem-master-data/BusinessEntityAddress',
        title: 'Ship-to address',
        lookupDialogTitle: 'Select ship-to address',
        valueField: 'name',
        isHidden: true,
        columns: [
            ui.nestedFields.checkbox({
                bind: { deliveryDetail: { isPrimary: true } },
                title: 'Primary address',
            }),
            ui.nestedFields.checkbox({ bind: 'isActive', title: 'Active' }),
            ui.nestedFields.text({ bind: 'name', title: 'Name' }),
            ui.nestedFields.text({ bind: 'addressLine1', title: 'Address line 1' }),
            ui.nestedFields.text({ bind: 'addressLine2', title: 'Address line 2' }),
            ui.nestedFields.text({ bind: 'city', title: 'City' }),
            ui.nestedFields.text({ bind: 'region', title: 'Region' }),
            ui.nestedFields.text({ bind: 'postcode', title: 'ZIP code' }),
            ui.nestedFields.text({ bind: 'locationPhoneNumber', title: 'Phone number' }),
            ui.nestedFields.reference<SalesOrder, BusinessEntityAddress, Country>({
                bind: 'country',

                columns: [
                    ui.nestedFields.text({ bind: 'name', title: 'Name' }),
                    ui.nestedFields.text({ bind: 'id', title: 'ISO 3166-1 alpha-2 code' }),
                    ui.nestedFields.technical({ bind: 'regionLabel' }),
                    ui.nestedFields.technical({ bind: 'zipLabel' }),
                ],
            }),
            ui.nestedFields.technical<SalesOrder, BusinessEntityAddress, BusinessEntity>({
                bind: 'businessEntity',
                node: '@sage/xtrem-master-data/BusinessEntity',
                nestedFields: [ui.nestedFields.technical({ bind: 'name' }), ui.nestedFields.technical({ bind: 'id' })],
            }),
            ui.nestedFields.reference<SalesOrder, BusinessEntityAddress, DeliveryDetail>({
                bind: 'deliveryDetail',
                title: 'Ship-to address',
                node: '@sage/xtrem-master-data/DeliveryDetail',
                valueField: '_id',
                columns: [
                    ui.nestedFields.numeric({ bind: 'leadTime', title: 'Delivery lead time', postfix: 'day(s)' }),
                    ui.nestedFields.reference<SalesOrder, DeliveryDetail, Incoterm>({
                        bind: 'incoterm',
                        title: 'Incoterms® rule',
                        node: '@sage/xtrem-master-data/Incoterm',
                        valueField: 'name',
                        helperTextField: 'id',
                        columns: [
                            ui.nestedFields.text({ bind: 'name', title: 'Name' }),
                            ui.nestedFields.text({ bind: 'id', title: 'ID' }),
                        ],
                    }),
                    ui.nestedFields.reference<SalesOrder, DeliveryDetail, DeliveryMode>({
                        bind: 'mode',
                        title: 'Delivery mode',
                        node: '@sage/xtrem-master-data/DeliveryMode',
                        valueField: 'name',
                        columns: [ui.nestedFields.text({ bind: 'name', title: 'Name' })],
                    }),
                    ui.nestedFields.reference<SalesOrder, DeliveryDetail, Site>({
                        bind: 'shipmentSite',
                        title: 'Stock site',
                        node: '@sage/xtrem-system/Site',
                        valueField: 'name',
                        helperTextField: 'id',
                        columns: [
                            ui.nestedFields.text({ bind: 'name', title: 'Name' }),
                            ui.nestedFields.text({ bind: 'id', title: 'ID' }),
                            ui.nestedFields.reference<SalesOrder, Site, Company>({
                                bind: 'legalCompany',
                                title: 'Company',
                                node: '@sage/xtrem-system/Company',
                                tunnelPage: '@sage/xtrem-master-data/Company',
                                valueField: 'name',
                                helperTextField: 'id',
                                columns: [
                                    ui.nestedFields.text({ bind: 'name', title: 'Name' }),
                                    ui.nestedFields.text({ bind: 'id', title: 'ID' }),
                                ],
                            }),
                        ],
                    }),
                ],
            }),
        ],
        filter() {
            // TODO be able to do { _not: { _eq: null } } & then delete the any
            // https://jira.sage.com/browse/XT-67917
            return this.shipToCustomer.value?.businessEntity?._id
                ? {
                      businessEntity: { _id: { _eq: this.shipToCustomer.value.businessEntity._id } },
                      deliveryDetail: { _not: { _eq: null } } as any,
                  }
                : {};
        },
        async onChange() {
            await this.cascadeAndFetchDefaultsFromShipToAddress();
            this.manageDisplayLinePhantomRow();
        },
    })
    shipToCustomerAddress: ui.fields.Reference<BusinessEntityAddress>;

    @ui.decorators.referenceField<SalesOrder, Site>({
        parent() {
            return this.shippingSectionShippingBlock;
        },
        title: 'Stock site',
        lookupDialogTitle: 'Select stock site',
        minLookupCharacters: 0,
        isAutoSelectEnabled: true,
        fetchesDefaults: true,
        isMandatory: true,
        columns: [
            ui.nestedFields.text({ bind: { businessEntity: { name: true } }, title: 'Name' }),
            ui.nestedFields.text({ bind: { businessEntity: { id: true } }, title: 'ID' }),
            ui.nestedFields.reference<SalesOrder, Site, Company>({
                node: '@sage/xtrem-system/Company',
                tunnelPage: '@sage/xtrem-master-data/Company',
                title: 'Company',
                bind: 'legalCompany',
                valueField: 'name',
                helperTextField: 'id',
                columns: [
                    ui.nestedFields.text({ bind: 'name', title: 'Name' }),
                    ui.nestedFields.text({ bind: 'id', title: 'ID' }),
                ],
            }),
            ui.nestedFields.technical({ bind: 'id' }),
            ui.nestedFields.technical({ bind: 'name' }),
            ui.nestedFields.technical({ bind: 'description' }),
            ui.nestedFields.technical({ bind: 'isInventory' }),
            ui.nestedFields.technical<SalesOrder, Site, BusinessEntityAddress>({
                bind: 'primaryAddress',
                node: '@sage/xtrem-master-data/BusinessEntityAddress',
                nestedFields: [
                    ui.nestedFields.technical<SalesOrder, BusinessEntityAddress, Country>({
                        bind: 'country',
                        node: '@sage/xtrem-structure/Country',
                        nestedFields: [
                            ui.nestedFields.technical({ bind: 'name' }),
                            ui.nestedFields.technical({ bind: 'id' }),
                        ],
                    }),
                ],
            }),
        ],
        filter() {
            return this.site.value
                ? ({
                      isInventory: true,
                      legalCompany: this.site.value.legalCompany,
                  } as Logical<Site>)
                : {
                      isInventory: true,
                  };
        },
        onChange() {
            this.manageDisplayLinePhantomRow();
        },
    })
    stockSite: ui.fields.Reference<Site>;

    @ui.decorators.referenceField<SalesOrder, Customer>({
        parent() {
            return this.shippingSectionShippingBlock;
        },
        node: '@sage/xtrem-master-data/Customer',
        title: 'Ship-to customer',
        lookupDialogTitle: 'Select ship-to customer',
        minLookupCharacters: 0,
        isAutoSelectEnabled: true,
        fetchesDefaults: true,
        isMandatory: true,
        isReadOnly: true,
        shouldSuggestionsIncludeColumns: true,
        columns: [
            ui.nestedFields.text({ bind: { businessEntity: { name: true } }, title: 'Name' }),
            ui.nestedFields.text({ bind: { businessEntity: { id: true } }, title: 'ID' }),
            ui.nestedFields.text({ bind: { businessEntity: { taxIdNumber: true } }, title: 'Tax ID' }),
            ui.nestedFields.text({ bind: { businessEntity: { country: { name: true } } }, title: 'Country' }),
            ui.nestedFields.technical<SalesOrder, Customer, BusinessEntity>({
                bind: 'businessEntity',
                node: '@sage/xtrem-master-data/BusinessEntity',
                nestedFields: [
                    ui.nestedFields.technical({ bind: 'name' }),
                    ui.nestedFields.technical({ bind: 'id' }),
                    ui.nestedFields.technical<SalesOrder, BusinessEntity, Country>({
                        bind: 'country',
                        node: '@sage/xtrem-structure/Country',
                        nestedFields: [
                            ui.nestedFields.technical({ bind: 'name' }),
                            ui.nestedFields.technical({ bind: 'id' }),
                        ],
                    }),
                ],
            }),
        ],
        async onChange() {
            await this.fetchDefaultsFromShipToCustomer();
            await this.cascadeAndFetchDefaultsFromShipToAddress();
            this.manageDisplayLinePhantomRow();
        },
    })
    shipToCustomer: ui.fields.Reference<Customer>;

    @ui.decorators.vitalPodField<SalesOrder, Address>({
        parent() {
            return this.shippingSection;
        },
        node: '@sage/xtrem-master-data/Address',
        title: 'Ship-to address',
        width: 'small',
        onAddButtonClick() {
            if (this.shipToCustomerAddress.value) {
                const { ...values } = { ...this.shipToCustomerAddress.value };
                delete values.businessEntity;
                return { ...values, _id: null };
            }
            return {};
        },
        dropdownActions: [
            {
                icon: 'lookup',
                title: 'Replace',
                onClick() {
                    this.shipToCustomerAddress.openDialog();
                },
                isDisabled() {
                    return this.status.value === 'closed';
                },
            },
            {
                icon: 'edit',
                title: 'Edit',
                onClick() {
                    this.shipToAddress.isReadOnly = false;
                },
                isDisabled() {
                    return this.status.value === 'closed' || !this.shipToAddress.isReadOnly;
                },
            },
            {
                icon: 'tick',
                title: 'Read-only',
                onClick() {
                    this.shipToAddress.isReadOnly = true;
                    if (this.shipToAddress.value) {
                        this.shipToAddress.value.concatenatedAddress = getConcatenatedAddress(this.shipToAddress.value);
                    }
                },
                isDisabled() {
                    return this.status.value === 'closed' || this.shipToAddress.isReadOnly;
                },
            },
        ],
        isReadOnly: true,
        columns: [
            ui.nestedFields.text({
                bind: 'name',
                title: 'Name',
                isHidden() {
                    return this.shipToAddress.isReadOnly === true;
                },
            }),
            ui.nestedFields.textArea({
                title: 'Bill-to address',
                isTitleHidden: true,
                bind: 'concatenatedAddress',
                width: 'large',
                isHidden() {
                    return this.shipToAddress.isReadOnly === false;
                },
            }),
            ui.nestedFields.text({ bind: 'locationPhoneNumber', title: 'Phone number' }),
            ui.nestedFields.text({
                bind: 'addressLine1',
                title: 'Address line 1',
                width: 'large',
                isHidden() {
                    return this.shipToAddress.isReadOnly === true;
                },
            }),
            ui.nestedFields.text({
                bind: 'addressLine2',
                title: 'Address line 2',
                width: 'large',
                isHidden() {
                    return this.shipToAddress.isReadOnly === true;
                },
            }),
            ui.nestedFields.text({
                bind: 'city',
                title: 'City',
                isHidden() {
                    return this.shipToAddress.isReadOnly === true;
                },
            }),
            ui.nestedFields.text({
                bind: 'region',
                title() {
                    return getCountryRegionTitle(this.shipToAddress.value);
                },
                isHidden() {
                    return this.shipToAddress.isReadOnly === true;
                },
            }),
            ui.nestedFields.text({
                bind: 'postcode',
                title() {
                    return getCountryPostCodeTitle(this.shipToAddress.value);
                },
                isHidden() {
                    return this.shipToAddress.isReadOnly === true;
                },
            }),
            ui.nestedFields.reference({
                bind: 'country',

                columns: [
                    ui.nestedFields.text({ bind: 'name', title: 'Name' }),
                    ui.nestedFields.text({ bind: 'id', title: 'ISO 3166-1 alpha-2 code' }),
                    ui.nestedFields.technical({ bind: 'regionLabel' }),
                    ui.nestedFields.technical({ bind: 'zipLabel' }),
                ],
                isHidden() {
                    return this.shipToAddress.isReadOnly === true;
                },
            }),
        ],
    })
    shipToAddress: ui.fields.VitalPod<Address>;

    @ui.decorators.referenceField<SalesOrder, Incoterm>({
        parent() {
            return this.shippingSectionShippingBlock;
        },
        title: 'Incoterms® rule',
        lookupDialogTitle: 'Select incoterms rule',
        minLookupCharacters: 0,
        isAutoSelectEnabled: true,
    })
    incoterm: ui.fields.Reference<Incoterm>;

    @ui.decorators.referenceField<SalesOrder, DeliveryMode>({
        parent() {
            return this.shippingSectionShippingBlock;
        },
        node: '@sage/xtrem-master-data/DeliveryMode',
        tunnelPage: '@sage/xtrem-master-data/DeliveryMode',
        title: 'Delivery mode',
        lookupDialogTitle: 'Select delivery mode',
        valueField: 'name',
        minLookupCharacters: 0,
        isAutoSelectEnabled: true,
        isMandatory: true,
        columns: [
            ui.nestedFields.text({ bind: 'name', title: 'Name' }),
            ui.nestedFields.technical({ bind: 'description' }),
        ],
    })
    deliveryMode: ui.fields.Reference<DeliveryMode>;

    @ui.decorators.dateField<SalesOrder>({
        parent() {
            return this.shippingSectionShippingBlock;
        },
        title: 'Requested delivery date',
        isMandatory: true,
        validation(val) {
            if (this.workDays.value) {
                return deliveryDateValidation(val, this.workDays.value);
            }
            return undefined;
        },
        async onChange() {
            await this.recomputeShippingDateAndDeliveryDate();
        },
    })
    requestedDeliveryDate: ui.fields.Date;

    @ui.decorators.numericField<SalesOrder>({
        parent() {
            return this.shippingSectionShippingBlock;
        },
        title: 'Delivery lead time',
        isMandatory: true,
        async onChange() {
            await this.recomputeShippingDateAndDeliveryDate();
        },
        postfix: 'day(s)',
    })
    deliveryLeadTime: ui.fields.Numeric;

    @ui.decorators.numericField<SalesOrder>({
        parent() {
            return this.shippingSectionShippingBlock;
        },
        title: 'Work days',
        isReadOnly: true,
        isHidden: true,
    })
    workDays: ui.fields.Numeric;

    @ui.decorators.dateField<SalesOrder>({
        parent() {
            return this.shippingSectionShippingBlock;
        },
        title: 'Shipping date',
        isMandatory: true,
        validation(val) {
            // TODO: we will control later the working days of the shipping
            // site when the information will be available on the site
            // if (isNotWorkDay(+this.workDays.value, new Date(val))) {
            //    return ui.localize(
            //        '@sage/xtrem-sales/pages__sales_order__shipping_date_cannot_be_a_not_working_day',
            //        'The shipping date must fall on a working day.',
            //     );
            // }
            if (this.date.value && val < this.date.value) {
                return ui.localize(
                    '@sage/xtrem-sales/pages__sales_order__shipping_date_cannot_be_a_less_order_date',
                    'The shipping date ({{value}}) cannot be earlier than the order date ({{date}}).',
                    {
                        value: val,
                        date: this.date.value,
                    },
                );
            }
            if (this.doNotShipBeforeDate.value && val < this.doNotShipBeforeDate.value) {
                return ui.localize(
                    '@sage/xtrem-sales/pages__sales_order__shipping_date_cannot_be_a_earlier_ship_before',
                    "The shipping date ({{value}}) cannot be earlier than the 'Do-not-ship-before' date ({{doNotShipBeforeDate}}).",
                    {
                        value: val,
                        doNotShipBeforeDate: this.doNotShipBeforeDate.value,
                    },
                );
            }

            if (this.doNotShipAfterDate.value && val > this.doNotShipAfterDate.value) {
                return ui.localize(
                    '@sage/xtrem-sales/pages__sales_order__shipping_date_cannot_be_a_later_ship_after',
                    "The shipping date ({{value}}) cannot be later than the 'Do-not-ship-after' date ({{doNotShipAfterDate}}).",
                    {
                        value: val,
                        doNotShipAfterDate: this.doNotShipAfterDate.value,
                    },
                );
            }
            return undefined;
        },
        async onChange() {
            await this.updateExpectedDeliveryDate();
        },
    })
    shippingDate: ui.fields.Date;

    @ui.decorators.dateField<SalesOrder>({
        parent() {
            return this.shippingSectionShippingBlock;
        },
        title: 'Expected delivery date',
        isMandatory: true,
        validation(val) {
            if (this.workDays.value) {
                return deliveryDateValidation(val, this.workDays.value);
            }
            return undefined;
        },
    })
    expectedDeliveryDate: ui.fields.Date;

    @ui.decorators.separatorField<SalesOrder>({
        parent() {
            return this.shippingSectionShippingBlock;
        },
        isFullWidth: true,
        isHidden: true,
    })
    expectedDeliveryDateSeparator: ui.fields.Separator;

    @ui.decorators.dateField<SalesOrder>({
        parent() {
            return this.shippingSectionShippingBlock;
        },
        title: 'Do-not-ship-before date',
    })
    doNotShipBeforeDate: ui.fields.Date;

    @ui.decorators.dateField<SalesOrder>({
        parent() {
            return this.shippingSectionShippingBlock;
        },
        title: 'Do-not-ship-after date',
    })
    doNotShipAfterDate: ui.fields.Date;

    @ui.decorators.separatorField<SalesOrder>({
        parent() {
            return this.shippingSectionShippingBlock;
        },
        isFullWidth: true,
        isInvisible: true,
    })
    shippingStatusSeparator: ui.fields.Separator;

    @ui.decorators.labelField<SalesOrder>({
        parent() {
            return this.shippingSectionShippingBlock;
        },
        title: 'Shipping status',
        bind: 'shippingStatus',
        isHidden: true,
        optionType: '@sage/xtrem-sales/SalesDocumentShippingStatus',
    })
    shippingStatus: ui.fields.Label;

    @ui.decorators.section<SalesOrder>({
        title: 'Financial',
    })
    financialSection: ui.containers.Section;

    @ui.decorators.block<SalesOrder>({
        parent() {
            return this.financialSection;
        },
        width: 'large',
        title: 'Financial',
        isTitleHidden: true,
    })
    financialSectionFinancialBlock: ui.containers.Block;

    @ui.decorators.labelField<SalesOrder>({
        parent() {
            return this.financialSectionFinancialBlock;
        },
        title: 'Invoice status',
        bind: 'invoiceStatus',
        optionType: '@sage/xtrem-sales/SalesDocumentInvoiceStatus',
        width: 'small',
        style() {
            return PillColorSales.getLabelColorByStatus('SalesDocumentInvoiceStatus', this.invoiceStatus.value);
        },
    })
    invoiceStatus: ui.fields.Label<SalesDocumentInvoiceStatus>;

    @ui.decorators.referenceField<SalesOrder, Customer>({
        parent() {
            return this.financialSectionFinancialBlock;
        },
        node: '@sage/xtrem-master-data/Customer',
        title: 'Bill-to customer',
        lookupDialogTitle: 'Select bill-to customer',
        minLookupCharacters: 0,
        isAutoSelectEnabled: true,
        fetchesDefaults: true,
        isMandatory: true,
        isReadOnly: true,
        shouldSuggestionsIncludeColumns: true,
        warningMessage() {
            if (this.isOnHold.value) {
                return ui.localize(
                    '@sage/xtrem-sales/pages__sales_order__display_customer_is_on_hold_status_bill_to_credit_limit',
                    'Customer on hold \n\nCredit limit {{currencySymbol}} {{creditLimit}}',
                    {
                        currencySymbol: this.billToCustomer.value?.businessEntity?.currency?.symbol,
                        creditLimit: this.billToCustomer.value?.creditLimit,
                    },
                );
            }
            return '';
        },
        columns: [
            ui.nestedFields.text({ bind: { businessEntity: { name: true } }, title: 'Name' }),
            ui.nestedFields.text({ bind: { businessEntity: { id: true } }, title: 'ID' }),
            ui.nestedFields.text({ bind: { businessEntity: { taxIdNumber: true } }, title: 'Tax ID' }),
            ui.nestedFields.text({ bind: { businessEntity: { country: { name: true } } }, title: 'Country' }),
            ui.nestedFields.technical<SalesOrder, Customer, BusinessEntity>({
                bind: 'businessEntity',
                node: '@sage/xtrem-master-data/BusinessEntity',
                nestedFields: [
                    ui.nestedFields.technical({ bind: 'name' }),
                    ui.nestedFields.technical({ bind: 'id' }),
                    ui.nestedFields.technical<SalesOrder, BusinessEntity, Currency>({
                        bind: 'currency',
                        node: '@sage/xtrem-master-data/Currency',
                        nestedFields: [
                            ui.nestedFields.technical({ bind: 'name' }),
                            ui.nestedFields.technical({ bind: 'id' }),
                            ui.nestedFields.technical({ bind: 'symbol' }),
                        ],
                    }),
                    ui.nestedFields.technical<SalesOrder, BusinessEntity, Country>({
                        bind: 'country',
                        node: '@sage/xtrem-structure/Country',
                        nestedFields: [
                            ui.nestedFields.technical({ bind: 'name' }),
                            ui.nestedFields.technical({ bind: 'id' }),
                        ],
                    }),
                ],
            }),
            ui.nestedFields.technical({ bind: 'creditLimit' }),
            ui.nestedFields.technical({ bind: 'isOnHold' }),
            ui.nestedFields.technical<SalesOrder, Customer, PaymentTerm>({
                bind: 'paymentTerm',
                node: '@sage/xtrem-master-data/PaymentTerm',
                nestedFields: [ui.nestedFields.technical({ bind: 'name' })],
            }),
        ],
        async onChange() {
            await this.fetchDefaultsFromBillToCustomer();
            if (this.billToCustomer.value?.businessEntity?.currency) {
                this.currency.value = this.billToCustomer.value.businessEntity.currency;
            }
        },
    })
    billToCustomer: ui.fields.Reference<Customer>;

    @ui.decorators.referenceField<SalesOrder, BusinessEntityAddress>({
        parent() {
            return this.financialSectionFinancialBlock;
        },
        node: '@sage/xtrem-master-data/BusinessEntityAddress',
        title: 'Bill-to address',
        lookupDialogTitle: 'Select bill-to address',
        valueField: 'name',
        isHidden: true,
        fetchesDefaults: true,
        columns: [
            ui.nestedFields.checkbox({ bind: 'isActive', title: 'Active' }),
            ui.nestedFields.text({ bind: 'name', title: 'Name' }),
            ui.nestedFields.text({ bind: 'addressLine1', title: 'Address line 1' }),
            ui.nestedFields.text({ bind: 'addressLine2', title: 'Address line 2' }),
            ui.nestedFields.text({ bind: 'city', title: 'City' }),
            ui.nestedFields.text({ bind: 'region', title: 'Region' }),
            ui.nestedFields.text({ bind: 'postcode', title: 'ZIP code' }),
            ui.nestedFields.reference<SalesOrder, BusinessEntityAddress, Country>({
                bind: 'country',
                columns: [
                    ui.nestedFields.text({ bind: 'name', title: 'Name' }),
                    ui.nestedFields.text({ bind: 'id', title: 'ISO 3166-1 alpha-2 code' }),
                    ui.nestedFields.technical({ bind: 'regionLabel' }),
                    ui.nestedFields.technical({ bind: 'zipLabel' }),
                ],
            }),
            ui.nestedFields.text({ bind: 'locationPhoneNumber', title: 'Phone number' }),
            ui.nestedFields.technical<SalesOrder, BusinessEntityAddress, BusinessEntity>({
                bind: 'businessEntity',
                node: '@sage/xtrem-master-data/BusinessEntity',
                nestedFields: [ui.nestedFields.technical({ bind: 'name' }), ui.nestedFields.technical({ bind: 'id' })],
            }),
        ],
        filter() {
            return this.billToCustomer.value?.businessEntity?.id
                ? { businessEntity: { id: this.billToCustomer.value.businessEntity.id } }
                : {};
        },
        async onChange() {
            await this.fetchDefaultsFromBillToAddress();
        },
    })
    billToLinkedAddress: ui.fields.Reference<BusinessEntityAddress>;

    @ui.decorators.referenceField<SalesOrder, PaymentTerm>({
        parent() {
            return this.financialSectionFinancialBlock;
        },
        lookupDialogTitle: 'Select payment term',
        width: 'small',
        minLookupCharacters: 0,
        isAutoSelectEnabled: true,
        isMandatory: true,
    })
    paymentTerm: ui.fields.Reference<PaymentTerm>;

    @ui.decorators.separatorField<SalesOrder>({
        parent() {
            return this.financialSectionFinancialBlock;
        },
        isFullWidth: true,
        isInvisible: true,
    })
    invoicingStatusSeparator: ui.fields.Separator;

    @ui.decorators.vitalPodField<SalesOrder, Address>({
        parent() {
            return this.financialSection;
        },
        node: '@sage/xtrem-master-data/Address',
        title: 'Bill-to address',
        width: 'small',
        onAddButtonClick() {
            if (this.billToLinkedAddress.value) {
                const { ...values } = { ...this.billToLinkedAddress.value };
                delete values.businessEntity;
                return { ...values, _id: null };
            }
            return {};
        },
        dropdownActions: [
            {
                icon: 'lookup',
                title: 'Replace',
                onClick() {
                    this.billToLinkedAddress.openDialog();
                },
                isDisabled() {
                    return this.status.value === 'closed';
                },
            },
            {
                icon: 'edit',
                title: 'Edit',
                onClick() {
                    this.billToAddress.isReadOnly = false;
                },
                isDisabled() {
                    return this.status.value === 'closed' || !this.billToAddress.isReadOnly;
                },
            },
            {
                icon: 'tick',
                title: 'Read-only',
                onClick() {
                    this.billToAddress.isReadOnly = true;
                    if (this.billToAddress.value) {
                        this.billToAddress.value.concatenatedAddress = getConcatenatedAddress(this.billToAddress.value);
                    }
                },
                isDisabled() {
                    return this.status.value === 'closed' || this.billToAddress.isReadOnly;
                },
            },
        ],
        isReadOnly: true,
        columns: [
            ui.nestedFields.text({
                bind: 'name',
                title: 'Name',
                isHidden() {
                    return this.billToAddress.isReadOnly === true;
                },
            }),
            ui.nestedFields.textArea({
                title: 'Bill-to address',
                isTitleHidden: true,
                bind: 'concatenatedAddress',
                width: 'large',
                isHidden() {
                    return this.billToAddress.isReadOnly === false;
                },
            }),
            ui.nestedFields.text({ bind: 'locationPhoneNumber', title: 'Phone number' }),
            ui.nestedFields.text({
                bind: 'addressLine1',
                title: 'Address line 1',
                width: 'large',
                isHidden() {
                    return this.billToAddress.isReadOnly === true;
                },
            }),
            ui.nestedFields.text({
                bind: 'addressLine2',
                title: 'Address line 2',
                width: 'large',
                isHidden() {
                    return this.billToAddress.isReadOnly === true;
                },
            }),
            ui.nestedFields.text({
                bind: 'city',
                title: 'City',
                isHidden() {
                    return this.billToAddress.isReadOnly === true;
                },
            }),
            ui.nestedFields.text({
                bind: 'region',
                title() {
                    return getCountryRegionTitle(this.billToAddress.value);
                },
                isHidden() {
                    return this.billToAddress.isReadOnly === true;
                },
            }),
            ui.nestedFields.text({
                bind: 'postcode',
                title() {
                    return getCountryPostCodeTitle(this.billToAddress.value);
                },
                isHidden() {
                    return this.billToAddress.isReadOnly === true;
                },
            }),
            ui.nestedFields.reference({
                bind: 'country',

                columns: [
                    ui.nestedFields.text({ bind: 'name', title: 'Name' }),
                    ui.nestedFields.text({ bind: 'id', title: 'ISO 3166-1 alpha-2 code' }),
                    ui.nestedFields.technical({ bind: 'regionLabel' }),
                    ui.nestedFields.technical({ bind: 'zipLabel' }),
                ],
                isHidden() {
                    return this.billToAddress.isReadOnly === true;
                },
            }),
        ],
    })
    billToAddress: ui.fields.VitalPod;

    @ui.decorators.richTextField<SalesOrder>({
        isFullWidth: true,
        title: 'Internal line notes',
        capabilities: validCapabilities,
        isTransient: true,
        helperText: 'Notes display on internal documents.',
        isDisabled() {
            return this.status.value === 'posted' || this.status.value === 'closed';
        },
    })
    internalNoteLine: ui.fields.RichText;

    @ui.decorators.switchField<SalesOrder>({
        title: 'Add notes to customer document',
        isFullWidth: true,
        isTransient: true,
        onChange() {
            this.externalNoteLine.isDisabled = !(this.isExternalNoteLine.value || false);
            this.externalNoteLine.value = '';
        },
        isDisabled() {
            return this.status.value === 'posted' || this.status.value === 'closed';
        },
    })
    isExternalNoteLine: ui.fields.Switch;

    @ui.decorators.richTextField<SalesOrder>({
        isFullWidth: true,
        title: 'Customer line notes',
        capabilities: validCapabilities,
        isTransient: true,
        helperText: 'Notes display on customer documents.',
        isDisabled() {
            return !this.isExternalNoteLine.value || this.status.value === 'posted' || this.status.value === 'closed';
        },
    })
    externalNoteLine: ui.fields.RichText;

    @ui.decorators.vitalPodField<SalesOrder, Address>({
        node: '@sage/xtrem-master-data/Address',
        title: 'Ship-to address',
        width: 'small',
        isTransient: true,
        // bind: 'shipToAddress',
        onAddButtonClick() {
            return {};
        },
        dropdownActions: [],
        isReadOnly: true,
        columns: [
            ui.nestedFields.text({
                bind: 'name',
                title: 'Name',
                isHidden() {
                    return this.salesOrderLineShipToAddressDetailPanel.isReadOnly === true;
                },
            }),
            ui.nestedFields.textArea({
                title: 'Bill-to address',
                isTitleHidden: true,
                bind: 'concatenatedAddress',
                width: 'large',
                isHidden() {
                    return this.salesOrderLineShipToAddressDetailPanel.isReadOnly === false;
                },
            }),
            ui.nestedFields.text({ bind: 'locationPhoneNumber', title: 'Phone number' }),
            ui.nestedFields.text({
                bind: 'addressLine1',
                title: 'Address line 1',
                width: 'large',
                isHidden() {
                    return this.salesOrderLineShipToAddressDetailPanel.isReadOnly === true;
                },
            }),
            ui.nestedFields.text({
                bind: 'addressLine2',
                title: 'Address line 2',
                width: 'large',
                isHidden() {
                    return this.salesOrderLineShipToAddressDetailPanel.isReadOnly === true;
                },
            }),
            ui.nestedFields.text({
                bind: 'city',
                title: 'City',
                isHidden() {
                    return this.salesOrderLineShipToAddressDetailPanel.isReadOnly === true;
                },
            }),
            ui.nestedFields.text({
                bind: 'region',
                title() {
                    return getCountryRegionTitle(this.shipToCustomerAddress.value);
                },
                isHidden() {
                    return this.salesOrderLineShipToAddressDetailPanel.isReadOnly === true;
                },
            }),
            ui.nestedFields.text({
                bind: 'postcode',
                title() {
                    return getCountryPostCodeTitle(this.shipToCustomerAddress.value);
                },
                isHidden() {
                    return this.salesOrderLineShipToAddressDetailPanel.isReadOnly === true;
                },
            }),
            ui.nestedFields.reference({
                bind: 'country',

                columns: [
                    ui.nestedFields.text({ bind: 'name', title: 'Name' }),
                    ui.nestedFields.text({ bind: 'id', title: 'ISO 3166-1 alpha-2 code' }),
                    ui.nestedFields.technical({ bind: 'regionLabel' }),
                    ui.nestedFields.technical({ bind: 'zipLabel' }),
                ],
                isHidden() {
                    return this.salesOrderLineShipToAddressDetailPanel.isReadOnly === true;
                },
            }),
        ],
    })
    salesOrderLineShipToAddressDetailPanel: ui.fields.VitalPod<Address>;

    @ui.decorators.tableField<SalesOrder, SalesShipmentLine>({
        title: 'Shipment lines',
        isTitleHidden: true,
        isTransient: true,
        canSelect: false,
        node: '@sage/xtrem-sales/SalesShipmentLine',
        orderBy: { _sortValue: +1 },
        columns: [
            ui.nestedFields.link({
                title: 'Shipment number',
                bind: '_id', // Dummy link to a string; value is set in the map, as we cannot use valueField like syntax in bind of link field type
                width: 'large',
                isTransient: true,
                map(_fieldValue, rowData) {
                    return `${rowData.document.number}`;
                },
                page: '@sage/xtrem-sales/SalesShipment',
                queryParameters(_value, rowData) {
                    return {
                        _id: rowData?.document._id ?? '',
                    };
                },
            }),
            ui.nestedFields.technical<SalesOrder, SalesShipmentLine, SalesShipment>({
                bind: 'document',
                node: '@sage/xtrem-sales/SalesShipment',
                nestedFields: [ui.nestedFields.technical({ bind: 'number' })],
            }),
            ui.nestedFields.reference<SalesOrder, SalesShipmentLine, Item>({
                bind: 'item',
                node: '@sage/xtrem-master-data/Item',
                valueField: 'name',
                helperTextField: 'id',
                isHidden: true,
                columns: [
                    ui.nestedFields.technical({ bind: 'name' }),
                    ui.nestedFields.technical({ bind: 'id' }),
                    ui.nestedFields.technical({ bind: 'description' }),
                ],
            }),
            ui.nestedFields.label({
                title: 'Shipment status',
                bind: 'status',
                optionType: '@sage/xtrem-sales/SalesShipmentStatus',
                isTitleHidden: true,
                style: (_id, rowData) => PillColorSales.getLabelColorByStatus('SalesShipmentStatus', rowData?.status),
            }),
            ui.nestedFields.reference<SalesOrder, SalesShipmentLine, UnitOfMeasure>({
                bind: 'unit',
                node: '@sage/xtrem-master-data/UnitOfMeasure',
                valueField: 'name',
                helperTextField: 'symbol',
                isHidden: true,
                columns: [
                    ui.nestedFields.text({ bind: 'name' }),
                    ui.nestedFields.technical({ bind: 'id' }),
                    ui.nestedFields.text({ bind: 'symbol' }),
                    ui.nestedFields.technical({ bind: 'decimalDigits' }),
                ],
            }),
            ui.nestedFields.numeric({
                title: 'Quantity in sales unit',
                bind: 'quantity',
                isReadOnly: true,
                width: 'large',
                unitMode: 'unitOfMeasure',
                unit: (_id, rowData) => rowData?.unit,
            }),
        ],
    })
    salesShipmentLines: ui.fields.Table<SalesShipmentLine>;

    @ui.decorators.tableField<SalesOrder, SalesInvoiceLine>({
        title: 'Invoice lines',
        isTitleHidden: true,
        isTransient: true,
        canSelect: false,
        node: '@sage/xtrem-sales/SalesInvoiceLine',
        orderBy: { _sortValue: +1 },
        columns: [
            ui.nestedFields.link({
                title: 'Invoice number',
                bind: '_id', // Dummy link to a string; value is set in the map, as we cannot use valueField like syntax in bind of link field type
                width: 'large',
                isTransient: true,
                map(_fieldValue, rowData) {
                    return `${rowData.document.number}`;
                },
                page: '@sage/xtrem-sales/SalesInvoice',
                queryParameters(_value, rowData) {
                    return {
                        _id: rowData?.document._id ?? '',
                    };
                },
            }),
            ui.nestedFields.technical<SalesOrder, SalesInvoiceLine, SalesInvoice>({
                bind: 'document',
                node: '@sage/xtrem-sales/SalesInvoice',
                nestedFields: [
                    ui.nestedFields.technical({ bind: 'number' }),
                    ui.nestedFields.technical({ bind: 'status' }),
                ],
            }),
            ui.nestedFields.technical<SalesOrder, SalesInvoiceLine, Item>({
                bind: 'item',
                node: '@sage/xtrem-master-data/Item',
                nestedFields: [
                    ui.nestedFields.technical({ bind: 'name' }),
                    ui.nestedFields.technical({ bind: 'id' }),
                    ui.nestedFields.technical({ bind: 'description' }),
                ],
            }),
            ui.nestedFields.label({
                title: 'Invoice status',
                bind: { document: { status: true } },
                // Dummy link to a string; value is set in the map, as we cannot use valueField like syntax in bind of link field type
                // optionType: '@sage/xtrem-sales/SalesInvoiceStatus',
                map(_value: any, rowData: any) {
                    switch (rowData.document.status) {
                        case 'draft':
                            return ui.localize(
                                '@sage/xtrem-sales/enums__sales_invoice_status__draft',
                                rowData.document.status,
                            );
                        case 'posted':
                            return ui.localize(
                                '@sage/xtrem-sales/enums__sales_invoice_status__posted',
                                rowData.document.status,
                            );
                        case 'inProgress':
                            return ui.localize(
                                '@sage/xtrem-sales/enums__sales_invoice_status__inProgress',
                                rowData.document.status,
                            );
                        case 'error':
                            return ui.localize(
                                '@sage/xtrem-sales/enums__sales_invoice_status__error',
                                rowData.document.status,
                            );
                        default:
                            return '';
                    }
                },
                style: (_id, rowData) =>
                    PillColorSales.getLabelColorByStatus('SalesInvoiceStatus', rowData?.document?.status),
            }),
            ui.nestedFields.reference<SalesOrder, SalesInvoiceLine, UnitOfMeasure>({
                bind: 'unit',
                node: '@sage/xtrem-master-data/UnitOfMeasure',
                valueField: 'name',
                helperTextField: 'symbol',
                isHidden: true,
                columns: [
                    ui.nestedFields.text({ bind: 'name' }),
                    ui.nestedFields.technical({ bind: 'id' }),
                    ui.nestedFields.text({ bind: 'symbol' }),
                    ui.nestedFields.technical({ bind: 'decimalDigits' }),
                ],
            }),
            ui.nestedFields.numeric({
                title: 'Quantity in sales unit',
                bind: 'quantity',
                isReadOnly: true,
                width: 'large',
                unitMode: 'unitOfMeasure',
                unit: (_id, rowData) => rowData?.unit,
            }),
            ui.nestedFields.numeric({
                title: 'Amount invoiced',
                bind: 'amountExcludingTax',
                isReadOnly: true,
                width: 'large',
                unit() {
                    return this.currency.value;
                },
            }),
        ],
    })
    toInvoiceLines: ui.fields.Table<SalesInvoiceLine>;
    // Here ends the detailPanel description

    @ui.decorators.section<SalesOrder>({
        title: 'Totals',
    })
    totalsSection: ui.containers.Section;

    @ui.decorators.block<SalesOrder>({
        parent() {
            return this.totalsSection;
        },
        width: 'large',
        title: '',
        isTitleHidden: true,
    })
    totalsSectionTaxTotalsBlock: ui.containers.Block;

    @ui.decorators.numericField<SalesOrder>({
        title: 'Excluding tax',
        parent() {
            return this.totalsSectionTaxTotalsBlock;
        },
        unit() {
            return this.currency.value;
        },
        scale: null,
        isReadOnly: true,
    })
    totalAmountExcludingTax: ui.fields.Numeric;

    @ui.decorators.numericField<SalesOrder>({
        title: 'Tax',
        parent() {
            return this.totalsSectionTaxTotalsBlock;
        },
        unit() {
            return this.currency.value;
        },
        scale: null,
        isReadOnly: true,
    })
    totalTaxAmount: ui.fields.Numeric;

    @ui.decorators.numericField<SalesOrder>({
        title: 'Total tax adjusted',
        parent() {
            return this.totalsSectionTaxTotalsBlock;
        },
        unit() {
            return this.currency.value;
        },
        scale: null,
        isHidden: true,
    })
    totalTaxAmountAdjusted: ui.fields.Numeric;

    @ui.decorators.numericField<SalesOrder>({
        title: 'Including tax',
        parent() {
            return this.totalsSectionTaxTotalsBlock;
        },
        unit() {
            return this.currency.value;
        },
        scale: null,
        isReadOnly: true,
    })
    totalAmountIncludingTax: ui.fields.Numeric;

    @ui.decorators.numericField<SalesOrder>({
        title: 'Gross profit',
        parent() {
            return this.totalsSectionTaxTotalsBlock;
        },
        unit() {
            return this.currency.value;
        },
        scale: null,
        isReadOnly: true,
    })
    totalGrossProfit: ui.fields.Numeric;

    @ui.decorators.block<SalesOrder>({
        parent() {
            return this.totalsSection;
        },
        title: 'Summary by tax',
        width: 'large',
    })
    totalsSectionTaxDetailsBlock: ui.containers.Block;

    @ui.decorators.tableField<SalesOrder, SalesOrderTax>({
        bind: 'taxes',
        title: 'Taxes',
        isTitleHidden: true,
        canSelect: false,
        pageSize: 10,
        node: '@sage/xtrem-sales/SalesOrderTax',
        orderBy: { _sortValue: +1, tax: +1 },
        parent() {
            return this.totalsSectionTaxDetailsBlock;
        },
        columns: [
            ui.nestedFields.technical({ bind: '_sortValue' }),
            ui.nestedFields.text({
                title: 'Category',
                bind: 'taxCategory',
                isReadOnly: true,
            }),
            ui.nestedFields.text({
                title: 'Tax',
                bind: 'tax',
                isReadOnly: true,
            }),
            ui.nestedFields.numeric({
                title: 'Taxable base',
                unit() {
                    return this.currency.value;
                },
                scale: null,
                bind: 'taxableAmount',
                isReadOnly: true,
            }),
            ui.nestedFields.numeric({
                title: 'Rate',
                postfix: '%',
                scale: 2,
                bind: 'taxRate',
                isReadOnly: true,
            }),
            ui.nestedFields.numeric({
                title: 'Amount',
                unit() {
                    return this.currency.value;
                },
                scale: null,
                bind: 'taxAmount',
                isReadOnly: true,
            }),
            ui.nestedFields.technical({ bind: 'taxAmountAdjusted' }),
            ui.nestedFields.technical({ bind: 'isReverseCharge' }),
        ],
    })
    taxDetails: ui.fields.Table<SalesOrderTax>;

    @ui.decorators.block<SalesOrder>({
        parent() {
            return this.totalsSection;
        },
        title: 'Amounts company currency',
        width: 'large',
    })
    totalsSectionCompanyCurrencyDetailsBlock: ui.containers.Block;

    @ui.decorators.numericField<SalesOrder>({
        title: 'Excluding tax',
        parent() {
            return this.totalsSectionCompanyCurrencyDetailsBlock;
        },
        unit() {
            return this.salesSiteCurrency;
        },
        scale: null,
        isReadOnly: true,
    })
    totalAmountExcludingTaxInCompanyCurrency: ui.fields.Numeric;

    @ui.decorators.numericField<SalesOrder>({
        title: 'Including tax',
        parent() {
            return this.totalsSectionCompanyCurrencyDetailsBlock;
        },
        unit() {
            return this.salesSiteCurrency;
        },
        scale: null,
        isReadOnly: true,
    })
    totalAmountIncludingTaxInCompanyCurrency: ui.fields.Numeric;

    @ui.decorators.section<SalesOrder>({ title: 'Notes', isTitleHidden: true })
    notesSection: ui.containers.Section;

    @ui.decorators.block<SalesOrder>({
        title: 'Notes',
        isTitleHidden: true,
        parent() {
            return this.notesSection;
        },
    })
    noteBlock: ui.containers.Block;

    @ui.decorators.richTextField<SalesOrder>({
        parent() {
            return this.noteBlock;
        },
        isFullWidth: true,
        title: 'Internal notes',
        capabilities: validCapabilities,
        helperText: 'Notes display on internal documents.',
        isDisabled() {
            return this.status.value === 'closed';
        },
    })
    internalNote: ui.fields.RichText;

    @ui.decorators.switchField<SalesOrder>({
        parent() {
            return this.noteBlock;
        },
        title: 'Add notes to customer document',
        onChange() {
            this.externalNote.isDisabled = !this.isExternalNote.value;
            this.externalNote.value = '';
        },
        isDisabled() {
            return this.status.value === 'closed';
        },
        width: 'large',
    })
    isExternalNote: ui.fields.Switch;

    @ui.decorators.richTextField<SalesOrder>({
        parent() {
            return this.noteBlock;
        },
        isFullWidth: true,
        title: 'Customer notes',
        capabilities: validCapabilities,
        helperText: 'Notes display on customer documents.',
        isDisabled() {
            return !this.isExternalNote.value || this.status.value === 'closed';
        },
    })
    externalNote: ui.fields.RichText;

    @ui.decorators.switchField<SalesOrder>({
        parent() {
            return this.noteBlock;
        },
        title: 'Repeat the document notes on new documents.',
        isDisabled() {
            return this.status.value === 'closed';
        },
        width: 'large',
    })
    isTransferHeaderNote: ui.fields.Switch;

    @ui.decorators.switchField<SalesOrder>({
        parent() {
            return this.noteBlock;
        },
        title: 'Repeat all the line notes on new documents.',
        isDisabled() {
            return this.status.value === 'closed';
        },
        width: 'large',
    })
    isTransferLineNote: ui.fields.Switch;

    @ui.decorators.section<SalesOrder>({
        title: 'Proforma invoices',
        isHidden() {
            return this.proformaInvoices.value.length === 0;
        },
    })
    proformaInvoicesSection: ui.containers.Section;

    @ui.decorators.tableField<SalesOrder, ProformaInvoice>({
        bind: 'proformaInvoices',
        title: 'Proforma invoices',
        isTitleHidden: true,
        pageSize: 10,
        node: '@sage/xtrem-sales/ProformaInvoice',
        isReadOnly: true,
        canSelect: false,
        parent() {
            return this.proformaInvoicesSection;
        },
        columns: [
            ui.nestedFields.numeric({ title: 'Version', bind: { version: true } }),
            ui.nestedFields.date({ title: 'Issue date', bind: { issueDate: true } }),
            ui.nestedFields.date({ title: 'Expiration date', bind: { expirationDate: true } }),
            ui.nestedFields.text({ title: 'Created by', bind: { createdBy: true } }),
            ui.nestedFields.checkbox({ title: 'Sent', bind: { isSent: true }, isHidden: true }),
            ui.nestedFields.date({ title: 'Link expiration date', bind: { uploadedFile: { expirationDate: true } } }),
            ui.nestedFields.technical({ bind: { _id: true } }),
            ui.nestedFields.technical({ bind: { customerComment: { value: true } } }),
            ui.nestedFields.technical({ bind: { isActive: true } }),
            ui.nestedFields.technical({ bind: { uploadedFile: { filename: true } } }),
            ui.nestedFields.technical({ bind: { uploadedFile: { downloadUrl: true } } }),
            ui.nestedFields.technical({ bind: { isLinkExpired: true } }),
            ui.nestedFields.link({
                title: 'Link',
                bind: { uploadedFile: { downloadUrl: true } },
                icon: 'pdf',
                map: (_value, rowData) => `${rowData.uploadedFile?.filename}`,
                onClick(_recordId, rowData) {
                    return this.$.router.goToExternal(rowData.uploadedFile?.downloadUrl ?? '');
                },
                isDisabled: (_recordId, rowData) =>
                    rowData && (!rowData.isActive || !rowData.uploadedFile.expirationDate || rowData.isLinkExpired),
            }),
        ],
    })
    proformaInvoices: ui.fields.Table<ProformaInvoice>;

    @ui.decorators.vitalPodField<SalesOrder, Address>({
        node: '@sage/xtrem-master-data/Address',
        title: 'Ship-to address',
        isFullWidth: true,
        isTransient: true,
        onAddButtonClick() {
            return {};
        },
        dropdownActions: [
            {
                icon: 'lookup',
                title: 'Replace',
                onClick() {
                    // rowData.shipToCustomerAddress.openDialog(); // intentionally left commented for the moment
                },
                isDisabled() {
                    return this.status.value === 'closed';
                },
                isHidden() {
                    return true; // intentionally left hidden for the moment
                },
            },
            {
                icon: 'edit',
                title: 'Edit',
                onClick() {
                    this.shipToAddressLineDetail.isReadOnly = false;
                },
                isDisabled() {
                    return this.status.value === 'closed' || !this.shipToAddressLineDetail.isReadOnly;
                },
            },
            {
                icon: 'tick',
                title: 'Read-only',
                onClick() {
                    this.shipToAddressLineDetail.isReadOnly = true;
                    if (this.shipToAddressLineDetail.value) {
                        this.shipToAddressLineDetail.value.concatenatedAddress = getConcatenatedAddress(
                            this.shipToAddressLineDetail.value,
                        );
                    }
                },
                isDisabled() {
                    return this.status.value === 'closed' || this.shipToAddressLineDetail.isReadOnly;
                },
            },
        ],
        isReadOnly: true,
        columns: [
            ui.nestedFields.text({
                bind: 'name',
                title: 'Name',
                isHidden() {
                    return this.shipToAddressLineDetail.isReadOnly === true;
                },
            }),
            ui.nestedFields.textArea({
                title: 'Bill-to address',
                isTitleHidden: true,
                bind: 'concatenatedAddress',
                width: 'large',
                isHidden() {
                    return this.shipToAddressLineDetail.isReadOnly === false;
                },
            }),
            ui.nestedFields.text({ bind: 'locationPhoneNumber', title: 'Phone number' }),
            ui.nestedFields.text({
                bind: 'addressLine1',
                title: 'Address line 1',
                width: 'large',
                isHidden() {
                    return this.shipToAddressLineDetail.isReadOnly === true;
                },
            }),
            ui.nestedFields.text({
                bind: 'addressLine2',
                title: 'Address line 2',
                width: 'large',
                isHidden() {
                    return this.shipToAddressLineDetail.isReadOnly === true;
                },
            }),
            ui.nestedFields.text({
                bind: 'city',
                title: 'City',
                isHidden() {
                    return this.shipToAddressLineDetail.isReadOnly === true;
                },
            }),
            ui.nestedFields.text({
                bind: 'region',
                title() {
                    return getCountryRegionTitle(this.shipToAddressLineDetail.value);
                },
                isHidden() {
                    return this.shipToAddressLineDetail.isReadOnly === true;
                },
            }),
            ui.nestedFields.text({
                bind: 'postcode',
                title() {
                    return getCountryPostCodeTitle(this.shipToAddressLineDetail.value);
                },
                isHidden() {
                    return this.shipToAddressLineDetail.isReadOnly === true;
                },
            }),
            ui.nestedFields.reference({
                bind: 'country',

                columns: [
                    ui.nestedFields.text({ bind: 'name', title: 'Name' }),
                    ui.nestedFields.text({ bind: 'id', title: 'ISO 3166-1 alpha-2 code' }),
                    ui.nestedFields.dropdownList({
                        bind: 'regionLabel',
                        isHidden: true,
                    }),
                    ui.nestedFields.dropdownList({
                        bind: 'zipLabel',
                        isHidden: true,
                    }),
                ],
                isHidden() {
                    return this.shipToAddressLineDetail.isReadOnly === true;
                },
            }),
        ],
    })
    shipToAddressLineDetail: ui.fields.VitalPod;

    async recomputeShippingDateAndDeliveryDate() {
        if (
            this.shippingDate.value &&
            !isNotWorkDay(+(this.workDays.value ?? ''), DateValue.parse(this.requestedDeliveryDate.value ?? ''))
        ) {
            if (
                await confirmDialogWithAcceptButtonText(
                    this,
                    ui.localize(
                        '@sage/xtrem-sales/pages__sales_order__recompute_shipping_and_delivery_dates_dialog_title',
                        'Confirm update',
                    ),
                    ui.localize(
                        '@sage/xtrem-sales/pages__sales_order__recompute_shipping_and_delivery_dates_dialog_content',
                        'You are about to update the shipping date and the delivery date.',
                    ),
                    ui.localize('@sage/xtrem-sales/pages-confirm-update', 'Update'),
                )
            ) {
                await this.updateShippingDate();
            }
        } else {
            await this.updateShippingDate();
        }
    }

    async recomputeShippingDateAndDeliveryDateLine(rowData: ui.PartialCollectionValue<SalesOrderLineBinding>) {
        if (
            rowData &&
            rowData.shippingDate &&
            !isNotWorkDay(+(this.workDays.value ?? ''), DateValue.parse(rowData.requestedDeliveryDate ?? ''))
        ) {
            if (
                await confirmDialogWithAcceptButtonText(
                    this,
                    ui.localize(
                        '@sage/xtrem-sales/pages__sales_order__recompute_shipping_and_delivery_dates_dialog_title',
                        'Confirm update',
                    ),
                    ui.localize(
                        '@sage/xtrem-sales/pages__sales_order__recompute_shipping_and_delivery_dates_dialog_content',
                        'You are about to update the shipping date and the delivery date.',
                    ),
                    ui.localize('@sage/xtrem-sales/pages-confirm-update', 'Update'),
                )
            ) {
                await this.updateShippingDateLine(rowData);
            }
        } else {
            await this.updateShippingDateLine(rowData);
        }
    }

    async updateShippingDate() {
        if (this.requestedDeliveryDate.value && this.date.value) {
            const mutationInput: {
                requestedDeliveryDate: string;
                orderDate: string;
                doNotShipBeforeDate?: string;
                doNotShipAfterDate?: string;
                deliveryLeadTime: integer | string;
                workDaysMask: integer | string;
            } = {
                requestedDeliveryDate: this.requestedDeliveryDate.value,
                orderDate: this.date.value,
                deliveryLeadTime: this.deliveryLeadTime.value ?? '',
                workDaysMask: this.workDays.value ?? '',
            };
            if (this.doNotShipBeforeDate.value) {
                mutationInput.doNotShipBeforeDate = this.doNotShipBeforeDate.value;
            }
            if (this.doNotShipAfterDate.value) {
                mutationInput.doNotShipAfterDate = this.doNotShipAfterDate.value;
            }
            this.shippingDate.value = await this.$.graph
                .node('@sage/xtrem-sales/SalesOrder')
                .mutations.subWorkDays(true, mutationInput)
                .execute();
            await this.updateExpectedDeliveryDate();
        }
    }

    async updateExpectedDeliveryDate() {
        this.expectedDeliveryDate.value = await this.$.graph
            .node('@sage/xtrem-sales/SalesOrder')
            .mutations.addWorkDays(true, {
                shippingDate: this.shippingDate.value ?? '',
                deliveryLeadTime: this.deliveryLeadTime.value ?? '',
                workDays: this.workDays.value ?? '',
            })
            .execute();
    }

    async initPage() {
        this.soldToAddress.isReadOnly = true;
        this.billToAddress.isReadOnly = true;
        this.shipToAddress.isReadOnly = true;
        if (this.$.recordId) {
            if (this.status.value === 'closed') {
                this.shippingDate.isDisabled = true;
                this.requestedDeliveryDate.isDisabled = true;
                this.expectedDeliveryDate.isDisabled = true;
                this.doNotShipBeforeDate.isDisabled = true;
                this.doNotShipAfterDate.isDisabled = true;
                this.date.isDisabled = true;
                this.customerNumber.isDisabled = true;
                this.paymentTerm.isDisabled = true;
                this.shipToCustomer.isDisabled = true;
                this.shipToCustomerAddress.isDisabled = true;
                this.stockSite.isDisabled = true;
                this.incoterm.isDisabled = true;
                this.deliveryMode.isDisabled = true;
                this.deliveryLeadTime.isDisabled = true;
                this.soldToAddress.isDisabled = true;
                this.shipToAddress.isDisabled = true;
                this.billToAddress.isDisabled = true;
                this.site.isDisabled = true;
                this.soldToCustomer.isReadOnly = true;
                this.currency.isDisabled = true;
            } else {
                this.shippingDate.isDisabled = false;
                this.requestedDeliveryDate.isDisabled = false;
                this.expectedDeliveryDate.isDisabled = false;
                this.doNotShipBeforeDate.isDisabled = false;
                this.doNotShipAfterDate.isDisabled = false;
                this.date.isDisabled = this.shippingStatus.value !== 'notShipped';
                this.customerNumber.isDisabled = false;
                this.paymentTerm.isDisabled = false;
                this.shipToCustomer.isDisabled = false;
                this.shipToCustomerAddress.isDisabled = false;
                this.stockSite.isDisabled = false;
                this.incoterm.isDisabled = false;
                this.deliveryMode.isDisabled = false;
                this.deliveryLeadTime.isDisabled = false;
                this.soldToAddress.isDisabled = false;
                this.shipToAddress.isDisabled = false;
                this.billToAddress.isDisabled = false;
                this.disableSomeHeaderPropertiesIfLines();
            }
            this.setCurrencyUnitsForTotalAmounts();
            this.initLinesLength = Number(this.lines.value.length);
        } else {
            this.date.value = DateValue.today().toString();
            this.status.value = 'quote';
            this.isQuote.value = true;
            this.shippingStatus.value = 'notShipped';
        }

        if (this.site.value && this.soldToCustomer.value) {
            this._defaultDimensionsAttributes = await initDefaultDimensions({
                page: this,
                dimensionDefinitionLevel: 'salesDirect',
                site: this.site.value,
                customer: this.soldToCustomer.value,
            });
        }

        this.confirmDialogResponseOnShipToAddressUpdate = false;
    }

    /**
     * Runs the setSalesOrderLineCloseStatus action
     */
    async clickOnLineCloseButton(_rowId: any, rowItem: any) {
        if (
            await confirmDialogWithAcceptButtonText(
                this,
                ui.localize('@sage/xtrem-sales/pages__sales_order__line_close_action_dialog_title', 'Confirm update'),
                ui.localize(
                    '@sage/xtrem-sales/pages__sales_order__line_close_action_dialog_content',
                    'You are about to close this sales order line.',
                ),
                ui.localize('@sage/xtrem-sales/pages-confirm-continue', 'Continue'),
            )
        ) {
            this.$.loader.isHidden = false;
            const returnStatus: any = await this.executeSetSalesOrderLineCloseStatusMutation(rowItem);
            const returnValue = getLocalizeSalesOrderLineCloseStatusMethodReturn(returnStatus);
            this.$.showToast(returnValue.content, returnValue.option);
            if (returnValue.severity === 'success') {
                await this.$.refreshNavigationPanel();
                await this.$.router.refresh();
            }
            this.$.loader.isHidden = true;
        }
    }

    /**
     * Runs the setSalesOrderLineCloseStatus mutation with parameters
     */
    executeSetSalesOrderLineCloseStatusMutation(row: any) {
        return this.$.graph
            .node('@sage/xtrem-sales/SalesOrder')
            .mutations.setSalesOrderLineCloseStatus(true, { salesOrderLine: `_id:${row._id}` })
            .execute();
    }

    /**
     * Runs the setSalesOrderLineOpenStatus action
     */
    async clickOnLineOpenButton(_rowId: any, rowItem: any) {
        if (
            await confirmDialogWithAcceptButtonText(
                this,
                ui.localize('@sage/xtrem-sales/pages__sales_order__line_open_action_dialog_title', 'Confirm update'),
                ui.localize(
                    '@sage/xtrem-sales/pages__sales_order__line_open_action_dialog_content',
                    'You are about to open this sales order line.',
                ),
                ui.localize('@sage/xtrem-sales/pages-confirm-open', 'Open'),
            )
        ) {
            this.$.loader.isHidden = false;
            const returnStatus: any = await this.executeSetSalesOrderLineOpenStatusMutation(rowItem);
            const returnValue = getLocalizeSalesOrderLineOpenStatusMethodReturn(returnStatus);
            this.$.showToast(returnValue.content, returnValue.option);
            if (returnValue.severity === 'success') {
                await this.$.refreshNavigationPanel();
                await this.$.router.refresh();
            }
            this.$.loader.isHidden = true;
        }
    }

    /**
     * Runs the setSalesOrderLineOpenStatus mutation with parameters
     */
    executeSetSalesOrderLineOpenStatusMutation(row: any) {
        return this.$.graph
            .node('@sage/xtrem-sales/SalesOrder')
            .mutations.setSalesOrderLineOpenStatus(true, { salesOrderLine: `_id:${row._id}` })
            .execute();
    }

    private disableSomeHeaderPropertiesIfLines() {
        const isDisabled = this.lines.value.length > 0;
        this.site.isDisabled = isDisabled;
        this.soldToCustomer.isReadOnly = isDisabled;
        this.currency.isDisabled = isDisabled;
    }

    async cascadeAndFetchDefaultsFromShipToAddress() {
        if (
            this.incoterm.value ||
            this.deliveryMode.value ||
            this.deliveryLeadTime.value ||
            this.shippingDate.value ||
            this.expectedDeliveryDate.value
        ) {
            if (
                await confirmDialogWithAcceptButtonText(
                    this,
                    ui.localize(
                        '@sage/xtrem-sales/pages__sales_order__cascade_ship_to_address_update_dialog_title',
                        'Confirm update',
                    ),
                    ui.localize(
                        '@sage/xtrem-sales/pages__sales_order__cascade_ship_to_address_update_dialog_content',
                        'You are about to update the delivery information.',
                    ),
                    ui.localize('@sage/xtrem-sales/pages-confirm-update', 'Update'),
                )
            ) {
                await this.fetchDefaultsFromShipToAddress();
                await this.updateShippingDate();
            } else {
                await this.fetchDefaultsFromShipToAddressOnly();
            }
        } else {
            await this.fetchDefaultsFromShipToAddress();
            await this.updateShippingDate();
        }
    }

    async cascadeAndFetchDefaultsFromShipToAddressLine(rowItem: ui.PartialCollectionValue<SalesOrderLineBinding>) {
        if (
            this.deliveryMode.value ||
            this.deliveryLeadTime.value ||
            this.shippingDate.value ||
            this.expectedDeliveryDate.value
        ) {
            if (
                await confirmDialogWithAcceptButtonText(
                    this,
                    ui.localize(
                        '@sage/xtrem-sales/pages__sales_order__cascade_ship_to_address_update_dialog_title',
                        'Confirm update',
                    ),
                    ui.localize(
                        '@sage/xtrem-sales/pages__sales_order__cascade_ship_to_address_update_dialog_content',
                        'You are about to update the delivery information.',
                    ),
                    ui.localize('@sage/xtrem-sales/pages-confirm-update', 'Update'),
                )
            ) {
                if (rowItem.shipToCustomerAddress) {
                    this.shipToAddressLineDetail.value = getAddressDetail(rowItem.shipToCustomerAddress);
                    rowItem.deliveryMode = rowItem.shipToCustomerAddress?.deliveryDetail?.mode;
                    rowItem.deliveryLeadTime = +(rowItem.shipToCustomerAddress?.deliveryDetail?.leadTime || 0);
                }
                await this.updateShippingDateLine(rowItem);
            } else if (rowItem.shipToCustomerAddress) {
                this.shipToAddressLineDetail.value = getAddressDetail(rowItem.shipToCustomerAddress);
            }
        } else {
            if (rowItem.shipToCustomerAddress) {
                this.shipToAddressLineDetail.value = getAddressDetail(rowItem.shipToCustomerAddress);
                rowItem.deliveryMode = rowItem.shipToCustomerAddress?.deliveryDetail?.mode;
                rowItem.deliveryLeadTime = +(rowItem.shipToCustomerAddress?.deliveryDetail?.leadTime || 0);
            }
            await this.updateShippingDateLine(rowItem);
            this.lines.addOrUpdateRecordValue(rowItem);
        }
    }

    // fetchDefaultForEntityUseFromExtension() {}

    async updateShippingDateLine(rowItem: ui.PartialCollectionValue<SalesOrderLineBinding>) {
        if (rowItem.requestedDeliveryDate && this.date.value && rowItem.deliveryLeadTime) {
            rowItem.shippingDate = await this.$.graph
                .node('@sage/xtrem-sales/SalesOrder')
                .mutations.subWorkDays(true, {
                    requestedDeliveryDate: rowItem.requestedDeliveryDate,
                    orderDate: this.date.value,
                    doNotShipBeforeDate: rowItem.doNotShipBeforeDate,
                    doNotShipAfterDate: rowItem.doNotShipAfterDate,
                    deliveryLeadTime: rowItem.deliveryLeadTime,
                    workDaysMask: this.workDays.value ?? '',
                })
                .execute();
            await this.updateExpectedDeliveryDateLine(rowItem);
        }
        this.lines.addOrUpdateRecordValue(rowItem);
    }

    async updateExpectedDeliveryDateLine(rowItem: ui.PartialCollectionValue<SalesOrderLineBinding>) {
        rowItem.expectedDeliveryDate = await this.$.graph
            .node('@sage/xtrem-sales/SalesOrder')
            .mutations.addWorkDays(true, {
                shippingDate: rowItem.shippingDate ?? '',
                deliveryLeadTime: rowItem.deliveryLeadTime ?? '',
                workDays: this.workDays.value ?? '',
            })
            .execute();
        this.lines.addOrUpdateRecordValue(rowItem);
    }

    async calculateTaxExcludedTotalAmount(withTaxDetails = true) {
        if (withTaxDetails) {
            await TotalTaxCalculator.getInstance().updateTaxDetails(
                this.taxDetails,
                this.totalTaxAmountAdjusted,
                this.totalTaxAmount,
            );
        }
        this.totalAmountExcludingTax.value = this.lines.value.reduce((accumulator: number, agLine) => {
            if (
                !this.lines.value.find((pageLine: any) => pageLine._id === agLine._id && pageLine._action === 'delete')
            ) {
                return Number(accumulator + +(agLine.amountExcludingTax ?? 0));
            }
            return accumulator;
        }, 0);
        this.totalAmountIncludingTax.value =
            this.totalAmountExcludingTax.value + (this.totalTaxAmountAdjusted?.value ?? 0);

        // Company currency amounts
        this.totalAmountExcludingTaxInCompanyCurrency.value = Decimal.roundAt(
            this.lines.value.reduce((amount: any, line) => amount + line.amountExcludingTaxInCompanyCurrency, 0),
            this.currency?.value?.decimalDigits,
        );
        this.totalAmountIncludingTaxInCompanyCurrency.value = Decimal.roundAt(
            this.lines.value.reduce((amount: any, line) => amount + line.amountIncludingTaxInCompanyCurrency, 0),
            this.currency?.value?.decimalDigits,
        );
    }

    setCurrencyUnitsForTotalAmounts() {
        this.totalAmountExcludingTax.unit = this.currency.value;
        this.totalAmountIncludingTax.unit = this.currency.value;
        this.totalTaxAmount.unit = this.currency.value;
        this.totalTaxAmountAdjusted.unit = this.currency.value;
        this.totalAmountExcludingTaxInCompanyCurrency.unit = this.salesSiteCurrency;
        this.totalAmountIncludingTaxInCompanyCurrency.unit = this.salesSiteCurrency;
    }

    async fillSalesShipmentLines(rowId: string) {
        const oldIsDirty = this.$.isDirty;
        this.salesShipmentLines.value.forEach(podLine => {
            this.salesShipmentLines.removeRecord(podLine._id);
        });
        const result = await this.$.graph
            .node('@sage/xtrem-sales/SalesOrderLineToSalesShipmentLine')
            .query(
                ui.queryUtils.edgesSelector(
                    {
                        document: {
                            _id: true,
                            item: {
                                _id: true,
                                name: true,
                            },
                            status: true,
                            quantity: true,
                            quantityInStockUnit: true,
                            unit: {
                                _id: true,
                                name: true,
                                decimalDigits: true,
                                id: true,
                                symbol: true,
                            },
                            document: {
                                _id: true,
                                number: true,
                            },
                        },
                    },
                    {
                        filter: { linkedDocument: { _id: rowId } },
                    },
                ),
            )
            .execute();
        this.salesShipmentLines.value = result.edges.map(e => e.node.document);
        this.salesShipmentLines.isHidden = this.salesShipmentLines.value.length === 0;
        if (!oldIsDirty && this.$.isDirty) {
            this.$.setPageClean();
        }
    }

    async fillSalesInvoiceLines(rowId: string) {
        const oldIsDirty = this.$.isDirty;
        this.toInvoiceLines.value.forEach(podLine => {
            this.toInvoiceLines.removeRecord(podLine._id);
        });
        const result = await this.$.graph
            .node('@sage/xtrem-sales/SalesOrderLineToSalesInvoiceLine')
            .query(
                ui.queryUtils.edgesSelector(
                    {
                        document: {
                            _id: true,
                            item: {
                                _id: true,
                                name: true,
                            },
                            quantity: true,
                            quantityInStockUnit: true,
                            amountExcludingTax: true,
                            unit: {
                                _id: true,
                                name: true,
                                decimalDigits: true,
                                id: true,
                                symbol: true,
                            },
                            document: {
                                _id: true,
                                number: true,
                                status: true,
                            },
                        },
                    },
                    {
                        filter: { linkedDocument: { _id: rowId } },
                    },
                ),
            )
            .execute();
        this.toInvoiceLines.value = result.edges.map(e => e.node.document);
        this.toInvoiceLines.isHidden = this.toInvoiceLines.value.length === 0;
        if (!oldIsDirty && this.$.isDirty) {
            this.$.setPageClean();
        }
    }

    async fetchDefaultsFromSoldToCustomer() {
        const fields = [
            'soldToLinkedAddress',
            'soldToAddress',
            'soldToContact',
            'shipToCustomer',
            'shipToCustomerAddress',
            'shipToAddress',
            ...(this.$.recordId ? ['billToAddress', 'billToCustomer', 'billToLinkedAddress'] : []),
            'currency',
            'paymentTerm',
            'isOnHold',
            'incoterm',
            'deliveryMode',
            'deliveryLeadTime',
            'workDays',
            'stockSite',
            'internalNote',
        ];
        await this.$.fetchDefaults(fields);
    }

    async fetchDefaultsFromSoldToAddress() {
        await this.$.fetchDefaults(['soldToAddress', 'soldToContact']);
    }

    async fetchDefaultsFromBillToCustomer() {
        await this.$.fetchDefaults(['billToLinkedAddress', 'billToAddress', 'currency', 'paymentTerm', 'isOnHold']);
    }

    async fetchDefaultsFromBillToAddress() {
        await this.$.fetchDefaults(['billToAddress']);
    }

    async fetchDefaultsFromShipToCustomer() {
        await this.$.fetchDefaults([
            'shipToCustomerAddress',
            'shipToAddress',
            'incoterm',
            'deliveryMode',
            'deliveryLeadTime',
            'workDays',
            'stockSite',
        ]);
    }

    async fetchDefaultsFromShipToAddress() {
        if (!this.shipToCustomerAddress.value?.deliveryDetail?.shipmentSite && this.stockSite.value) {
            await this.$.fetchDefaults(['shipToAddress', 'incoterm', 'deliveryMode', 'deliveryLeadTime', 'workDays']);
        } else {
            await this.$.fetchDefaults([
                'shipToAddress',
                'incoterm',
                'deliveryMode',
                'deliveryLeadTime',
                'workDays',
                'stockSite',
            ]);
        }
    }

    async fetchDefaultsFromShipToAddressOnly() {
        await this.$.fetchDefaults(['shipToAddress']);
    }

    async fetchDefaultsFromSalesSite() {
        await this.$.fetchDefaults(['stockSite']);
    }

    async callDisplayTaxes(rowItem: ui.PartialCollectionValue<SalesOrderLine>) {
        const taxCalculationResult = await displayTaxes(this, rowItem, {
            currency: this.currency.value as ExtractEdgesPartial<Currency>,
            taxAmount: +(rowItem.taxAmount ?? 0),
            taxAmountAdjusted: +(rowItem.taxAmountAdjusted ?? 0),
            amountExcludingTax: +(rowItem.amountExcludingTax ?? 0),
            amountIncludingTax: +(rowItem.amountIncludingTax ?? 0),
            documentType: DocumentTypeEnum.salesOrderLine,
            taxDate: rowItem.taxDate ?? this.date.value ?? '',
            legislation: rowItem.site?.legalCompany?.legislation,
            editable: this.status.value !== 'closed',
            destinationCountry: rowItem.shipToAddress?.country as ExtractEdgesPartial<Country>,
            originCountry: this.stockSite.value?.primaryAddress?.country as ExtractEdgesPartial<Country>,
            validTypes: ['sales', 'purchasingAndSales'],
        });
        if (taxCalculationResult) {
            rowItem.amountIncludingTaxInCompanyCurrency = convertAmount(
                Number(taxCalculationResult.amountIncludingTax),
                this.companyFxRate.value ?? 1,
                this.companyFxRateDivisor.value ?? 1,
                this.currency.value?.decimalDigits ?? 2,
                this.salesSiteCurrency?.decimalDigits ?? 2,
            ).toString();
            this.lines.addOrUpdateRecordValue({
                _id: rowItem._id,
                amountIncludingTaxInCompanyCurrency: rowItem.amountIncludingTaxInCompanyCurrency,
            });
            await this.calculateTaxExcludedTotalAmount(false);
        }
    }

    async priceDetermination(rowItem: ui.PartialCollectionValue<SalesOrderLine>) {
        const response = await this.$.dialog.confirmation(
            'info',
            ui.localize('@sage/xtrem-sales/pages__sales_order__price_determination_title', 'Update price'),
            ui.localize(
                '@sage/xtrem-sales/pages__sales_order__on_price_determination_confirmation',
                'Do you want to recalculate prices, discounts and charges to reflect the latest price list?',
            ),
            {
                cancelButton: { isHidden: false },
                acceptButton: { text: ui.localize('@sage/xtrem-sales/pages__sales_order__recalculate', 'Recalculate') },
            },
        );
        if (response) {
            await this.getPriceDetermination(rowItem);
        }
    }

    async priceDetermination2(
        rowItem: ui.PartialCollectionValue<SalesOrderLine>,
        confirmationMessage: string,
    ): Promise<boolean> {
        const errorOnQuantityInSalesUnit = false; // await rowItem.quantity!.validate();
        if (
            !errorOnQuantityInSalesUnit &&
            Number(rowItem.quantity) !== 0 &&
            rowItem.item !== null &&
            rowItem.stockSite !== null
        ) {
            const shouldShowDialog = this.status.value === 'pending' || rowItem.isPriceDeterminated;
            if (!shouldShowDialog) {
                await this.getPriceDetermination(rowItem);
                rowItem.isPriceDeterminated = true;
                return true;
            }

            const response = await this.$.dialog.confirmation(
                'info',
                ui.localize('@sage/xtrem-sales/pages__sales_order__on_price_determination_title', 'Update price'),
                confirmationMessage,
                {
                    cancelButton: { isHidden: false },
                    acceptButton: {
                        text: ui.localize('@sage/xtrem-sales/pages__sales_order__recalculate', 'Recalculate'),
                    },
                },
            );
            if (response) {
                await this.getPriceDetermination(rowItem);
                return true;
            }
        }
        return false;
    }

    async getPriceDetermination(
        rowItem: ui.PartialCollectionValue<SalesOrderLine>,
        isWithMessage = false,
    ): Promise<void> {
        const prices = await getPrices(this.$.graph, {
            salesSiteId: rowItem.site?._id ?? '',
            stockSiteId: rowItem.stockSite?._id ?? '',
            soldToCustomerId: this.soldToCustomer.value?._id ?? '',
            currencyId: this.currency.value?._id ?? '',
            itemId: rowItem.item?._id ?? '',
            unitId: rowItem.unit?._id ?? '',
            quantity: Number(rowItem.quantity),
            date: new Date(this.date.value ?? 0),
        });

        rowItem.priceReason = prices.priceReason;
        rowItem.priceOrigin = prices.priceOrigin as SalesPriceOrigin;
        rowItem.grossPrice = prices.grossPrice.toString();
        rowItem.discount = prices.discount.toString();
        rowItem.charge = prices.charge.toString();
        rowItem.priceReasonDeterminated = prices.priceReason;
        rowItem.priceOriginDeterminated = prices.priceOrigin as SalesPriceOrigin;
        rowItem.grossPriceDeterminated = prices.grossPrice.toString();
        rowItem.discountDeterminated = prices.discount.toString();
        rowItem.chargeDeterminated = prices.charge.toString();

        await this.calculatePrices(rowItem);

        this.lines.addOrUpdateRecordValue(rowItem);
        await this.calculateTaxExcludedTotalAmount();
        recalculateTaxCalculationStatus(this.lines, this.taxCalculationStatus);
        this.disableSomeHeaderPropertiesIfLines();

        if (prices.error !== '' && isWithMessage === true) {
            this.$.showToast(
                ui.localize(
                    '@sage/xtrem-sales/page__sales_order_line_panel__no_rate_found',
                    'The price could not been determined from the item sales base price. The currency rate between the sales base price currency and the document currency is missing.',
                ),
                { type: 'warning', timeout: 5000 },
            );
        }
    }

    async calculatePrices(rowItem: ui.PartialCollectionValue<SalesOrderLine>) {
        const prices = await calculateLinePrices({
            grossPrice: Number(rowItem.grossPrice),
            charge: Number(rowItem.charge),
            discount: Number(rowItem.discount),
            netPriceScale: this.currency.value?.decimalDigits ?? 2,
            quantity: Number(rowItem.quantity),
            amountExcludingTax: Number(rowItem.amountExcludingTax),
            amountIncludingTax: Number(rowItem.amountIncludingTax),
            taxAmount: Number(rowItem.taxAmount),
            taxAmountAdjusted: Number(rowItem.taxAmountAdjusted),
            rateMultiplication: this.companyFxRate.value ?? 1,
            rateDivision: this.companyFxRateDivisor.value ?? 1,
            fromDecimals: this.currency.value?.decimalDigits ?? 2,
            toDecimals: this.salesSiteCurrency?.decimalDigits ?? 2,
            taxes: {
                site: rowItem.stockSite?._id,
                businessPartner: this.soldToCustomer.value?._id,
                addresses: {
                    consumerCountry: rowItem.shipToAddress?.country?._id,
                    consumerPostcode: rowItem.shipToAddress?.postcode,
                    shipToCustomerAddress: rowItem.shipToCustomerAddress?._id,
                },
                item: rowItem.item?._id,
                currency: this.currency.value?._id,
                lineNodeName: '@sage/xtrem-sales/SalesOrderLine',
                taxEngine: this.taxEngine.value ?? '',
                uiTaxes: rowItem.uiTaxes ?? '',
                graphObject: this.$.graph,
                taxDate: rowItem.taxDate ?? this.date.value ?? '',
            },
        });
        rowItem.netPrice = prices.netPrice.toString();
        rowItem.amountExcludingTax = prices.amountExcludingTax.toString();
        rowItem.amountIncludingTax = prices.amountIncludingTax.toString();
        rowItem.taxAmount = prices.taxAmount.toString();
        rowItem.taxAmountAdjusted = prices.taxAmountAdjusted.toString();
        rowItem.uiTaxes = prices.uiTaxes.toString();
        rowItem.taxCalculationStatus = prices.taxCalculationStatus;
        rowItem.amountExcludingTaxInCompanyCurrency = prices.amountExcludingTaxInCompanyCurrency.toString();
        rowItem.amountIncludingTaxInCompanyCurrency = prices.amountIncludingTaxInCompanyCurrency.toString();
        this.lines.addOrUpdateRecordValue(rowItem);
    }

    private handleAssignOrderButtonClick = async (
        rowData?: ui.PartialCollectionValue<SalesOrderLineBinding>,
    ): Promise<void> => {
        if (rowData && isOrderToOrderServiceOptionActivated(rowData)) {
            await openAssignments(this, rowData);
            await this.lines.refreshRecord(rowData._id ?? '');
        }
    };

    async checkItemCustomer(rowData: ui.PartialCollectionValue<SalesOrderLineBinding>, includeSalesUnit = false) {
        const result = extractEdges(
            await this.$.graph
                .node('@sage/xtrem-master-data/ItemCustomer')
                .query(
                    ui.queryUtils.edgesSelector(
                        {
                            _id: true,
                            salesUnit: {
                                _id: true,
                                name: true,
                                id: true,
                                symbol: true,
                                decimalDigits: true,
                            },
                            minimumSalesQuantity: true,
                            maximumSalesQuantity: true,
                            salesUnitToStockUnitConversion: true,
                        },
                        {
                            filter: {
                                item: rowData.item?._id,
                                customer: this.soldToCustomer.value?._id?.toString(),
                            },
                        },
                    ),
                )
                .execute(),
        );
        const salesToStock = await this.$.graph
            .node('@sage/xtrem-master-data/UnitOfMeasure')
            .queries.convertFromTo(false, {
                fromUnit: rowData.item?.salesUnit?._id ?? '',
                toUnit: rowData.item?.stockUnit?._id ?? '',
                type: 'sales',
                item: rowData.item?._id,
                quantity: 1,
                formatToUnitDecimalDigits: false,
            })
            .execute();
        if (result.length) {
            const itemCustomer = result.shift();
            if (includeSalesUnit) {
                rowData.unit = itemCustomer?.salesUnit;
                rowData.unitToStockUnitConversionFactor = itemCustomer?.salesUnitToStockUnitConversion?.toString();
                this.minimumSalesQuantity = +(itemCustomer?.minimumSalesQuantity ?? 0);
                this.maximumSalesQuantity = +(itemCustomer?.maximumSalesQuantity ?? 0);
            }
            if (itemCustomer) {
                this.additionalItemId = itemCustomer.salesUnit._id;
                this.itemData.itemCustomer.conversionFactor = +itemCustomer.salesUnitToStockUnitConversion;
                this.itemData.itemCustomer.minimumSalesQuantity = +itemCustomer.minimumSalesQuantity;
                this.itemData.itemCustomer.maximumSalesQuantity = +itemCustomer.maximumSalesQuantity;
            }
        } else if (includeSalesUnit) {
            rowData.unit = rowData.item?.salesUnit;
            rowData.unitToStockUnitConversionFactor = (salesToStock || 1).toString();
            this.minimumSalesQuantity = +(rowData.item?.minimumSalesQuantity ?? 0);
            this.maximumSalesQuantity = +(rowData.item?.maximumSalesQuantity ?? 0);
        }
        this.itemData.item.conversionFactor = Number(salesToStock || 1);
        this.itemData.item.minimumSalesQuantity = +(rowData.item?.minimumSalesQuantity ?? 0);
        this.itemData.item.maximumSalesQuantity = +(rowData.item?.maximumSalesQuantity ?? 0);
        this.oldSalesUnit = rowData.unit?._id;
        this.lines.addOrUpdateRecordValue(rowData);
    }

    // eslint-disable-next-line class-methods-use-this
    setPriceOriginAndReason(rowData: ui.PartialCollectionValue<SalesOrderLineBinding>) {
        if (Number(rowData.grossPrice) === 0) {
            rowData.priceOrigin = 'manual';
            rowData.priceReason = undefined;
            return;
        }
        if (
            Number(rowData.grossPriceDeterminated) === Number(rowData.grossPrice) &&
            Number(rowData.discountDeterminated) === Number(rowData.discount) &&
            Number(rowData.chargeDeterminated) === Number(rowData.charge)
        ) {
            rowData.priceOrigin = rowData.priceOriginDeterminated;
            rowData.priceReason = rowData.priceReasonDeterminated;
        } else {
            rowData.priceOrigin = 'manual';
            rowData.priceReason = undefined;
        }
    }

    async getItemSiteAvailableStockQuantity(rowData: ui.PartialCollectionValue<SalesOrderLineBinding>) {
        const { item, stockSite } = rowData;
        if (item?._id && stockSite?._id) {
            const [{ acceptedStockQuantity, allocatedQuantity, inStockQuantity }] = await queryItemSite(
                this,
                item,
                stockSite,
            );

            if (acceptedStockQuantity && allocatedQuantity && inStockQuantity) {
                const availableQuantityInStockUnit = +acceptedStockQuantity - +allocatedQuantity;
                const unitToStockUnitConversionFactor = +(rowData.unitToStockUnitConversionFactor ?? 1);

                Object.assign(rowData, {
                    availableQuantityInStockUnit: availableQuantityInStockUnit.toString(),
                    availableQuantityInSalesUnit: (
                        availableQuantityInStockUnit / unitToStockUnitConversionFactor
                    ).toString(),
                    stockShortageInStockUnit: Math.max(
                        +(rowData.quantityInStockUnit ?? 0) - (availableQuantityInStockUnit + +allocatedQuantity),
                        0,
                    ).toString(),
                    stockShortageInSalesUnit: Math.max(
                        +(rowData.stockShortageInStockUnit ?? 0) * unitToStockUnitConversionFactor,
                        0,
                    ).toString(),
                    stockShortageStatus: +(rowData.stockShortageInStockUnit ?? 0) > 0,
                    stockOnHand: inStockQuantity.toString(),
                });
            }
        } else {
            Object.assign(rowData, {
                availableQuantityInStockUnit: '0',
                availableQuantityInSalesUnit: '0',
                stockShortageInStockUnit: '0',
                stockShortageInSalesUnit: '0',
                stockShortageStatus: false,
                quantityToShipInProgressInSalesUnit: '0',
                shippedQuantityInSalesUnit: '0',
                remainingQuantityToShipInSalesUnit: '0',
                quantityToInvoiceInProgressInSalesUnit: '0',
                invoicedQuantityInSalesUnit: '0',
                remainingQuantityToInvoiceInSalesUnit: '0',
                stockOnHand: '0',
            });
        }
    }

    _setStepSequenceStatusObject(stepSequenceValues: SalesOrderStepSequenceStatus): Dict<ui.StepSequenceStatus> {
        return {
            [this.orderStepSequenceCreate]: stepSequenceValues.create,
            [this.orderStepSequenceConfirm]: stepSequenceValues.confirm,
            [this.orderStepSequenceShip]: stepSequenceValues.ship,
            [this.orderStepSequenceInvoice]: stepSequenceValues.invoice,
        };
    }

    get salesSiteCurrency() {
        return this.site.value?.legalCompany?.currency;
    }

    async updateGrossProfit(rowData: ExtractEdgesPartial<SalesOrderLineBinding>) {
        const newCostInCompanyCurrency = await calculateStockCost(
            this,
            rowData.item?._id ?? '',
            rowData.stockSite?._id ?? '',
            Number(rowData.quantityInStockUnit),
        );
        commonUpdateGrossProfit(
            this,
            {
                itemId: rowData.item?._id ?? '',
                siteId: rowData.stockSite?._id ?? '',
                rowData: rowData as unknown as SalesOrderLineBinding,
                quantityInStockUnit: Number(rowData.quantityInStockUnit),
                currencyInfo: {
                    rateMultiplication: this.companyFxRateDivisor?.value ?? 0,
                    rateDivision: this.companyFxRate?.value ?? 0,
                    fromDecimals: this.salesSiteCurrency?.decimalDigits ?? 2,
                    toDecimals: this.currency?.value?.decimalDigits ?? 2,
                },
            },
            newCostInCompanyCurrency,
        );
    }

    static isSidebarStockSectionHidden(_rowId: string, recordValue?: SalesOrderLineBinding | undefined): boolean {
        return (
            !['inProgress', 'pending', 'quote'].includes(recordValue?.status ?? '') || !recordValue?.item.isStockManaged
        );
    }

    static isSidebarAllocationBlockHidden(_rowId: string, recordValue?: SalesOrderLineBinding | undefined): boolean {
        return !recordValue || ['closed', 'quote'].includes(recordValue.status) || !recordValue.item.isStockManaged;
    }

    async transferAllocationDialog(
        rowId: string,
        rowItem: ui.PartialCollectionValue<SalesOrderLineBinding>,
    ): Promise<void> {
        const isAssignmentChecked = await isAssignmentCheckedOnSalesOrderLineAllocate(this, rowItem);
        if (isAssignmentChecked) {
            await this.$.dialog.page(
                '@sage/xtrem-stock-data/TransferAllocationsDialog',
                {
                    item: JSON.stringify(rowItem.item),
                    site: JSON.stringify(this.stockSite.value),
                    shippingDate: JSON.stringify(this.shippingDate.value),
                    lineId: rowId,
                    requiredQuantity: rowItem.remainingQuantityToShipInStockUnit || 0,
                    allocatedQuantity: rowItem.quantityAllocated || 0,
                    documentTypes: JSON.stringify(['SalesOrderLine', 'SalesShipmentLine']),
                    documentNumber: this.number.value ?? 0,
                },
                {
                    rightAligned: false,
                    size: 'extra-large',
                    resolveOnCancel: true,
                },
            );
            await this.lines.refreshRecord(rowId);
            await this.allocationStatus.refresh();
            this.manageDisplayButtonAllOtherActions(false);
        }
    }
}
