import type { Dict, ExtractEdgesPartial, integer } from '@sage/xtrem-client';
import { extractEdges } from '@sage/xtrem-client';
import { DateValue, date } from '@sage/xtrem-date-time';
import * as attributesAndDimensions from '@sage/xtrem-finance-data/build/lib/client-functions/attributes-and-dimensions';
import { initDefaultDimensions } from '@sage/xtrem-finance-data/build/lib/client-functions/attributes-and-dimensions';
import * as dimensionPanelHelpers from '@sage/xtrem-finance-data/build/lib/client-functions/dimension-panel-helpers';
import * as displayButtonsFinance from '@sage/xtrem-finance-data/build/lib/client-functions/display-buttons-finance';
import type * as financeInterfaces from '@sage/xtrem-finance-data/build/lib/client-functions/interfaces/dimension';
import * as PillColorFinance from '@sage/xtrem-finance-data/build/lib/client-functions/pill-color';
import type { PartialCollectionValueWithIds } from '@sage/xtrem-ui/build/lib/component/types';
// Had to do this \(override from /lib/interfaces in /client-functions/interfaces\) to avoid using server entities (from /lib/nodes or enums etc) from some of the interface properties
import { Decimal } from '@sage/xtrem-decimal';
import {
    assignEmailContactFrom,
    calculateStockCost,
    confirmDialogWithAcceptButtonText,
    isPostedInProgressError,
    loadContacts,
} from '@sage/xtrem-distribution/build/lib/client-functions/common';
import type { BankAccount, BaseOpenItem, FinanceTransactionBinding } from '@sage/xtrem-finance-data-api';
import type {
    Address,
    BusinessEntity,
    BusinessEntityAddress,
    BusinessEntityContact,
    BusinessRelationType,
    Contact,
    Currency,
    Customer,
    CustomerPriceReason,
    Incoterm,
    Item,
    ItemCustomer,
    PaymentTerm,
    TaxCalculationStatus,
    UnitOfMeasure,
} from '@sage/xtrem-master-data-api';
import {
    getCountryPostCodeTitle,
    getCountryRegionTitle,
} from '@sage/xtrem-master-data/build/lib/client-functions/country-fields-title';
import { validCapabilities } from '@sage/xtrem-master-data/build/lib/client-functions/fields-properties';
import * as PillColorCommon from '@sage/xtrem-master-data/build/lib/client-functions/pill-color';
import * as MasterDataUtils from '@sage/xtrem-master-data/build/lib/client-functions/utils';
import { sales } from '@sage/xtrem-master-data/build/lib/menu-items/sales';
import { getConcatenatedAddress } from '@sage/xtrem-master-data/build/lib/shared-functions/address-functions';
import { convertAmount, scaleOfCurrent } from '@sage/xtrem-master-data/build/lib/shared-functions/common';
import { fetchDefaultsForDueDate } from '@sage/xtrem-master-data/lib/client-functions/common';
import { checkStockSite } from '@sage/xtrem-master-data/lib/client-functions/page-functions';
import type {
    GraphApi,
    SalesCreditMemo,
    SalesCreditMemoLine,
    SalesInvoiceDisplayStatus,
    SalesInvoiceLine,
    SalesInvoiceLineBinding,
    SalesInvoice as SalesInvoiceNode,
    SalesInvoiceTax,
    SalesPriceOrigin,
    SalesShipment,
} from '@sage/xtrem-sales-api';
import type { Country, Legislation } from '@sage/xtrem-structure-api';
import type { Company, Site } from '@sage/xtrem-system-api';
import {
    setDisplayOfCommonPageActions,
    setOrderOfPageBusinessActions,
    setOrderOfPageHeaderDropDownActions,
    setOrderOfPageHeaderQuickActions,
} from '@sage/xtrem-system/build/lib/client-functions/applicative-crud-actions';
import * as MainListActions from '@sage/xtrem-system/build/lib/client-functions/main-list-actions';
import { validEmail } from '@sage/xtrem-system/build/lib/shared-functions/email-validation';
import { TotalTaxCalculator } from '@sage/xtrem-tax/build/lib/client-functions/classes/total-tax-calculator';
import { recalculateTaxCalculationStatus } from '@sage/xtrem-tax/build/lib/client-functions/display-taxes';
import { DocumentTypeEnum } from '@sage/xtrem-tax/build/lib/client-functions/interfaces';
import type { TotalTaxCalculatorState } from '@sage/xtrem-tax/build/lib/client-functions/interfaces/total-tax-calculator';
import * as ui from '@sage/xtrem-ui';
import { noop } from 'lodash';
import {
    commonUpdateGrossProfit,
    getFullyPaidFilter,
    getNotFullyPaidFilter,
    showErrorsBlockingMessage,
    showTaxCalcFiledPostBlockingMessage,
    showTaxCalcFiledPrintBlockingMessage,
} from '../client-functions/common';
import * as displayButtons from '../client-functions/display-buttons-sales-invoice';
import { displayTaxes } from '../client-functions/display-taxes';
import {
    getSiteAndCustomer,
    openRecordPage,
    openRecordPageFromMainList,
} from '../client-functions/finance-integration';
import type { ItemData, SalesInvoiceStepSequenceStatus } from '../client-functions/interfaces/interfaces';
import { calculateLinePrices, getPrices, isExchangeRateHidden } from '../client-functions/page-functions';
import * as PillColorSales from '../client-functions/pill-color';
import { convertGrossPriceToUnit2, massPriceDeterminationCalculation } from '../client-functions/price-determination';
import * as actionFunctions from '../client-functions/sales-invoice-actions-functions';

@ui.decorators.page<SalesInvoice, SalesInvoiceNode>({
    title: 'Sales invoice',
    objectTypeSingular: 'Sales invoice',
    objectTypePlural: 'Sales invoices',
    idField() {
        return this.number;
    },
    menuItem: sales,
    module: 'sales',
    node: '@sage/xtrem-sales/SalesInvoice',
    hasAttachmentsSection: true,
    mode: 'tabs',
    priority: 250,
    headerLabel() {
        return this.displayStatus;
    },
    createAction() {
        return this.$standardNewAction;
    },
    businessActions() {
        return setOrderOfPageBusinessActions({
            save: this.save,
            cancel: this.$standardCancelAction,
            businessActions: [
                this.avalara,
                this.post,
                this.repost,
                this.createSalesCreditMemosFromInvoices,
                this.recordReceipt,
            ],
        });
    },
    headerQuickActions() {
        return setOrderOfPageHeaderQuickActions({
            duplicate: [],
            quickActions: [this.print],
        });
    },

    headerDropDownActions() {
        return setOrderOfPageHeaderDropDownActions({
            remove: [this.$standardDeleteAction],
            dropDownBusinessActions: [
                this.$standardOpenRecordHistoryAction,
                this.$standardOpenCustomizationPageWizardAction,
                ui.menuSeparator(),
                this.defaultDimension,
                ui.menuSeparator(),
                this.sendEmail,
            ],
        });
    },
    headerSection() {
        return this.headerSection;
    },
    onDirtyStateUpdated(isDirty: boolean) {
        this.invoiceStepSequence.statuses = this.getDisplayStatusStepSequence();
        this._manageDisplayApplicativePageActions(isDirty);
    },
    async onLoad() {
        this.invoiceStepSequence.statuses = this.getDisplayStatusStepSequence();
        this.initParams();
        await this.initPage();
        this._manageDisplayApplicativePageActions(false);
        this.$.setPageClean();
        TotalTaxCalculator.getInstance().initializeTotalTaxData(this.taxDetails, this.taxEngine.value ?? '');
    },
    onError(error: string | (Error & { errors: Array<any> })) {
        return MasterDataUtils.formatError(this, error);
    },
    navigationPanel: {
        bulkActions: [
            {
                mutation: 'printBulkSalesInvoice',
                title: 'Print',
                icon: 'print',
                buttonType: 'primary',
                isDestructive: false,
            },
        ],
        orderBy: { date: -1, number: -1 },
        listItem: {
            title: ui.nestedFields.link({
                bind: 'number',
                title: 'Number',
                isMandatory: true,
                page: '@sage/xtrem-sales/SalesInvoice',
                queryParameters(_value, rowData) {
                    return {
                        _id: rowData?._id ?? '',
                    };
                },
            }),
            line2: ui.nestedFields.reference<SalesInvoice, SalesInvoiceNode>({
                bind: 'billToCustomer',
                title: 'Bill-to customer',
                node: '@sage/xtrem-master-data/Customer',
                tunnelPage: undefined,
                valueField: { businessEntity: { name: true } },
                columns: [
                    ui.nestedFields.technical({ bind: 'name' }),
                    ui.nestedFields.technical({ bind: 'id' }),
                    ui.nestedFields.technical({ bind: { businessEntity: { _id: true } } }),
                ],
            }),
            billToCustomerId: ui.nestedFields.text({
                bind: { billToCustomer: { id: true } },
                title: 'Bill-to customer ID',
                isHiddenOnMainField: true,
            }),
            line2Right: ui.nestedFields.date({
                bind: 'date',
                title: 'Invoice date',
                isMandatory: true,
            }),
            line_4: ui.nestedFields.reference<SalesInvoice, SalesInvoiceNode>({
                bind: 'site',
                node: '@sage/xtrem-system/Site',
                tunnelPage: undefined,
                valueField: 'name',
                title: 'Site',
                columns: [
                    ui.nestedFields.technical({ bind: 'isFinance' }),
                    ui.nestedFields.technical<SalesInvoice, Site, Site>({
                        node: '@sage/xtrem-system/Site',
                        bind: 'financialSite',
                        nestedFields: [
                            ui.nestedFields.technical({ bind: 'name' }),
                            ui.nestedFields.technical({ bind: 'id' }),
                        ],
                    }),
                    ui.nestedFields.technical<SalesInvoice, Site, Company>({
                        node: '@sage/xtrem-system/Company',
                        bind: 'legalCompany',
                        nestedFields: [
                            ui.nestedFields.technical<SalesInvoice, Company, BankAccount>({
                                bind: 'bankAccount',
                                node: '@sage/xtrem-finance-data/BankAccount',
                                nestedFields: [
                                    ui.nestedFields.technical({ bind: 'name' }),
                                    ui.nestedFields.technical({ bind: 'id' }),
                                    ui.nestedFields.technical({ bind: { financialSite: { _id: true } } }),
                                    ui.nestedFields.technical<SalesInvoice, BankAccount, Currency>({
                                        bind: 'currency',
                                        node: '@sage/xtrem-master-data/Currency',
                                        nestedFields: [
                                            ui.nestedFields.technical({ bind: '_id' }),
                                            ui.nestedFields.technical({ bind: 'id' }),
                                            ui.nestedFields.technical({ bind: 'symbol' }),
                                            ui.nestedFields.technical({ bind: 'decimalDigits' }),
                                        ],
                                    }),
                                ],
                            }),
                        ],
                    }),
                ],
            }),
            line_5: ui.nestedFields.numeric<SalesInvoice, SalesInvoiceNode>({
                bind: 'totalAmountIncludingTax',
                title: 'Total including tax',
                unit: (_rowId, rowData) => rowData?.currency,
                scale: null,
            }),
            line6: ui.nestedFields.date({
                bind: 'dueDate',
                title: 'Due date',
            }),
            titleRight: ui.nestedFields.label({
                bind: 'displayStatus',
                title: 'Status',
                optionType: '@sage/xtrem-sales/SalesInvoiceDisplayStatus',
                isMandatory: true,
                style: (_id, rowData) => PillColorCommon.getDisplayStatusPillFeatures(rowData?.displayStatus),
            }),
            line7: ui.nestedFields.reference<SalesInvoice, SalesInvoiceNode>({
                bind: 'paymentTerm',
                isHiddenOnMainField: true,
                tunnelPage: undefined,
            }),
            line8: ui.nestedFields.reference<SalesInvoice, SalesInvoiceNode>({
                bind: 'incoterm',
                title: 'Incoterms® rule',
                isHiddenOnMainField: true,
                tunnelPage: undefined,
            }),
            line9: ui.nestedFields.reference<SalesInvoice, SalesInvoiceNode>({
                bind: 'currency',
                node: '@sage/xtrem-master-data/Currency',
                tunnelPage: undefined,
                valueField: 'name',
                title: 'Transaction currency',
                isHiddenOnMainField: true,
                columns: [
                    ui.nestedFields.text({ title: 'Name', bind: 'name' }),
                    ui.nestedFields.numeric({ title: 'Decimal digits', bind: 'decimalDigits', canFilter: false }),
                    ui.nestedFields.numeric({ title: 'Rounding', bind: 'rounding', canFilter: false }),
                    ui.nestedFields.text({ title: 'Symbol', bind: 'symbol', canFilter: false }),
                ],
            }),
            line10: ui.nestedFields.text({
                bind: 'creationNumber',
                title: 'Creation number',
                isHiddenOnMainField: true,
            }),
            line11: ui.nestedFields.icon({
                bind: 'isPrinted',
                title: 'Printed',
                isHiddenOnMainField: true,
                map: (_value, rowData) => (rowData.isPrinted ? 'tick' : 'none'),
            }),
            line12: ui.nestedFields.icon({
                bind: 'isSent' as any,
                title: 'Sent',
                isHiddenOnMainField: true,
                map: (_value, rowData) => (rowData.isSent ? 'tick' : 'none'),
            }),
            line13: ui.nestedFields.numeric<SalesInvoice, SalesInvoiceNode>({
                bind: 'totalAmountExcludingTax',
                title: 'Total excluding tax',
                isHiddenOnMainField: true,
                unit: (_rowId, rowData) => rowData?.currency,
                scale: null,
            }),
            line14: ui.nestedFields.numeric<SalesInvoice, SalesInvoiceNode>({
                bind: 'totalGrossProfit',
                title: 'Gross profit',
                isHiddenOnMainField: true,
                unit: (_rowId, rowData) => rowData?.currency,
                scale: null,
            }),
            line15: ui.nestedFields.numeric<SalesInvoice, SalesInvoiceNode>({
                bind: 'totalTaxAmount',
                title: 'Tax',
                isHiddenOnMainField: true,
                unit: (_rowId, rowData) => rowData?.currency,
                scale: null,
            }),
            paymentStatus: ui.nestedFields.label<SalesInvoice, SalesInvoiceNode>({
                bind: 'paymentStatus',
                title: 'Payment status',
                optionType: '@sage/xtrem-finance-data/OpenItemStatus',
                isHiddenOnMainField: true,
                style: (_id, rowData) => PillColorCommon.getDisplayStatusPillFeatures(rowData?.paymentStatus),
            }),
            totalPayments: ui.nestedFields.numeric<SalesInvoice, SalesInvoiceNode>({
                bind: 'totalAmountPaid',
                title: 'Total payments',
                isHiddenOnMainField: true,
                unit: (_rowId, rowData) => rowData?.currency,
            }),
            netBalance: ui.nestedFields.numeric<SalesInvoice, SalesInvoiceNode>({
                bind: 'netBalance',
                title: 'Net balance',
                isHiddenOnMainField: true,
                unit: (_rowId, rowData) => rowData?.currency,
            }),
            billToLinkedAddress: ui.nestedFields.technical<SalesInvoice, SalesInvoiceNode, BusinessEntityAddress>({
                bind: 'billToLinkedAddress',
                node: '@sage/xtrem-master-data/BusinessEntityAddress',
                nestedFields: [ui.nestedFields.technical({ bind: '_id' })],
            }),
            enablePrintButton: ui.nestedFields.technical<SalesInvoice, SalesInvoiceNode>({
                bind: 'enablePrintButton',
            }),
            status: ui.nestedFields.technical<SalesInvoice, SalesInvoiceNode>({
                bind: 'status',
            }),
            taxCalculationStatus: ui.nestedFields.technical<SalesInvoice, SalesInvoiceNode>({
                bind: 'taxCalculationStatus',
            }),
            creditStatus: ui.nestedFields.technical({ bind: 'creditStatus' }),
            billToContact: ui.nestedFields.technical<SalesInvoice, SalesInvoiceNode, Contact>({
                bind: 'billToContact',
                node: '@sage/xtrem-master-data/Contact',
                nestedFields: [
                    ui.nestedFields.technical({ bind: 'lastName' }),
                    ui.nestedFields.technical({ bind: 'firstName' }),
                    ui.nestedFields.technical({ bind: 'title' }),
                    ui.nestedFields.technical({ bind: 'email' }),
                    ui.nestedFields.technical({ bind: 'preferredName' }),
                    ui.nestedFields.technical({ bind: 'role' }),
                    ui.nestedFields.technical({ bind: 'position' }),
                    ui.nestedFields.technical({ bind: 'locationPhoneNumber' }),
                ],
            }),
            openItemSysId: ui.nestedFields.technical<SalesInvoice, SalesInvoiceNode>({ bind: 'openItemSysId' }),
            isOpenItemPageOptionActive: ui.nestedFields.technical<SalesInvoice, SalesInvoiceNode>({
                bind: 'isOpenItemPageOptionActive',
            }),
        },
        optionsMenu(_graph, _storage, _queryParam, _username, _userCode, serviceOptions) {
            return Promise.resolve([
                {
                    title: 'All open statuses',
                    graphQLFilter: {
                        displayStatus: { _nin: ['posted', 'partiallyCredited', 'credited', 'partiallyPaid', 'paid'] },
                    },
                },
                { title: 'All statuses', graphQLFilter: {} },
                { title: 'Draft', graphQLFilter: { displayStatus: { _eq: 'draft' } } },
                { title: 'Posted', graphQLFilter: { displayStatus: { _eq: 'posted' } } },
                { title: 'Posting in progress', graphQLFilter: { displayStatus: { _eq: 'postingInProgress' } } },
                ...(serviceOptions.paymentTrackingOption ? [getNotFullyPaidFilter()] : []),
                ...(serviceOptions.paymentTrackingOption ? [getFullyPaidFilter()] : []),
                { title: 'Error', graphQLFilter: { displayStatus: { _eq: 'error' } } },
                { title: 'Partially credited', graphQLFilter: { creditStatus: { _eq: 'partiallyCredited' } } },
                { title: 'Credited', graphQLFilter: { creditStatus: { _eq: 'credited' } } },
                { title: 'Tax calculation failed', graphQLFilter: { displayStatus: { _eq: 'taxCalculationFailed' } } },
            ] as ui.containers.OptionsMenuItemType<SalesInvoiceNode>[]);
        },
        dropdownActions: [
            {
                title: 'Post',
                icon: 'none',
                refreshesMainList: 'list',
                async onClick(recordId: string, rowItem: ui.PartialNodeWithId<SalesInvoiceNode>) {
                    if (rowItem.taxCalculationStatus === 'failed') {
                        showTaxCalcFiledPostBlockingMessage(this);
                        return;
                    }
                    await actionFunctions.postAction({
                        salesInvoicePage: this,
                        recordId,
                        isCalledFromRecordPage: false,
                        taxCalculationStatus: rowItem.taxCalculationStatus,
                        status: rowItem.status ?? '',
                    });
                },
                isHidden(recordId: string, rowItem: ui.PartialNodeWithId<SalesInvoiceNode>) {
                    return (
                        displayButtons.isHiddenButtonPostAction({
                            parameters: { status: rowItem.status, taxCalculationStatus: rowItem.taxCalculationStatus },
                            recordId,
                            isDirty: false,
                        }) || false
                    );
                },
            },
            {
                title: 'Open item',
                icon: 'none',
                async onClick(_recordId: string, rowItem: ui.PartialNodeWithId<SalesInvoiceNode>) {
                    await this.$.dialog.page(
                        `@sage/xtrem-finance/AccountsReceivableOpenItem`,
                        { _id: rowItem.openItemSysId ?? '', fromSales: true },
                        { fullScreen: true, resolveOnCancel: true },
                    );
                },
                isHidden(_recordId: string, rowItem: ui.PartialNodeWithId<SalesInvoiceNode>) {
                    return rowItem.status !== 'posted' || !rowItem.isOpenItemPageOptionActive;
                },
            },
            {
                title: 'Record receipt',
                icon: 'none',
                refreshesMainList: 'list',
                async onClick(_recordId: string, rowItem: ui.PartialNodeWithId<SalesInvoiceNode>) {
                    const receiptNumber = await openRecordPageFromMainList({
                        page: this,
                        openParams: JSON.stringify({
                            bankAccount: rowItem.site?.legalCompany?.bankAccount,
                            financialSite: rowItem.site?.isFinance ? rowItem.site : rowItem.site?.financialSite,
                            baseBusinessRelation: rowItem.billToCustomer,
                            type: 'customer' as BusinessRelationType,
                            date: DateValue.today().toString(),
                            currency: rowItem.currency,
                            amount: rowItem.netBalance ?? 0,
                            number: rowItem.number,
                        }),
                    });
                    if (typeof receiptNumber === 'string') {
                        this.$.showToast(
                            ui.localize(
                                '@sage/xtrem-sales/pages__sales_invoice__receipt_created',
                                'The following receipt was created: {{receiptNumber}}.',
                                { receiptNumber },
                            ),
                            { timeout: 10000 },
                        );
                    }
                    await this.$.refreshNavigationPanel();
                },
                isHidden(recordId: string, rowItem: ui.PartialNodeWithId<SalesInvoiceNode>) {
                    return (
                        !this.$.isServiceOptionEnabled('paymentTrackingOption') ||
                        displayButtons.isHiddenButtonRecordReceiptAction({
                            parameters: {
                                status: rowItem.status,
                                paymentStatus: rowItem.paymentStatus,
                                taxCalculationStatus: rowItem.taxCalculationStatus,
                            },
                            recordId,
                            isDirty: false,
                        }) ||
                        false
                    );
                },
            },
            {
                title: 'Create credit memo',
                icon: 'none',
                refreshesMainList: 'list',
                async onClick(recordId) {
                    await actionFunctions.createCreditAction({
                        salesInvoicePage: this,
                        recordId,
                        isCalledFromRecordPage: false,
                    });
                },
                isHidden(recordId: string, rowItem: ui.PartialNodeWithId<SalesInvoiceNode>) {
                    return (
                        displayButtons.isHiddenButtonCreateSalesCreditMemosFromInvoicesAction({
                            parameters: {
                                status: rowItem.status,
                                creditStatus: rowItem.creditStatus,
                                taxCalculationStatus: rowItem.taxCalculationStatus,
                            },
                            recordId,
                            isDirty: false,
                        }) || false
                    );
                },
            },
            ui.menuSeparator(),
            {
                title: 'Print',
                icon: 'print',
                refreshesMainList: 'record',
                access: { node: '@sage/xtrem-sales/SalesInvoice', bind: 'beforePrint' },
                async onClick(recordId: string, rowItem: ui.PartialNodeWithId<SalesInvoiceNode>) {
                    if (rowItem.taxCalculationStatus === 'failed') {
                        showTaxCalcFiledPrintBlockingMessage(this);
                        return;
                    }
                    if (rowItem.status === 'error') {
                        showErrorsBlockingMessage(this);
                        return;
                    }
                    if (rowItem.number) {
                        if (recordId) {
                            await actionFunctions.printSalesInvoiceAction(this, recordId);
                        }
                    }
                },
                isHidden(recordId, rowItem: ui.PartialNodeWithId<SalesInvoiceNode>) {
                    return displayButtons.isHiddenButtonPrintAction({
                        parameters: {
                            status: rowItem.status,
                            taxCalculationStatus: rowItem.taxCalculationStatus,
                            displayStatus: rowItem.displayStatus ?? '',
                            enablePrintButton: rowItem.enablePrintButton,
                        },
                        recordId,
                    });
                },
                isDisabled(recordId: string, rowItem: ui.PartialNodeWithId<SalesInvoiceNode>) {
                    return (
                        displayButtons.isDisabledButtonPrintAction({
                            parameters: {
                                status: rowItem.status,
                                enablePrintButton: rowItem.enablePrintButton,
                            },
                            recordId,
                            isDirty: false,
                        }) || false
                    );
                },
            },
            {
                title: 'Send',
                icon: 'email',
                refreshesMainList: 'list',
                async onClick(recordId, rowItem) {
                    this._id.value = recordId;
                    this.sendEmailSection.isHidden = false;

                    await loadContacts(this, rowItem.billToLinkedAddress?._id || '', rowItem.billToContact);

                    await this.$.dialog.custom('info', this.sendEmailSection, {
                        cancelButton: { isHidden: true },
                        acceptButton: { isHidden: true },
                        resolveOnCancel: true,
                    });
                    this.sendEmailSection.isHidden = true;
                },
                isHidden(recordId, rowItem: ui.PartialNodeWithId<SalesInvoiceNode>) {
                    return displayButtons.isHiddenButtonSendMailAction({
                        parameters: {
                            status: rowItem.status,
                            taxCalculationStatus: rowItem.taxCalculationStatus,
                            displayStatus: rowItem.displayStatus ?? '',
                            enablePrintButton: rowItem.enablePrintButton,
                        },
                        recordId,
                    });
                },
                isDisabled(recordId, rowItem: ui.PartialNodeWithId<SalesInvoiceNode>) {
                    return (
                        displayButtons.isDisabledButtonSendMailAction({
                            parameters: {
                                status: rowItem.status,
                                enablePrintButton: rowItem.enablePrintButton,
                            },
                            recordId,
                            isDirty: false,
                        }) || false
                    );
                },
            },
            ui.menuSeparator(),
            {
                icon: 'none',
                title: 'Set dimensions',
                async onClick(_rowId, rowItem: ui.PartialNodeWithId<SalesInvoiceNode>) {
                    if (
                        !['posted', 'inProgress', 'error'].includes(rowItem?.status ?? '') ||
                        this.fromNotificationHistory
                    ) {
                        const { site, customer } = await getSiteAndCustomer({
                            page: this,
                            siteId: rowItem.site?._id ?? '',
                            customerId: rowItem.billToCustomer?._id ?? '',
                        });
                        await actionFunctions.setDimensions({
                            salesInvoicePage: this,
                            recordNumber: rowItem.number ?? '',
                            status: rowItem.status ?? null,
                            site,
                            customer,
                        });
                    }
                },
                isHidden(recordId, rowItem: ui.PartialNodeWithId<SalesInvoiceNode>) {
                    return displayButtons.isHiddenButtonDefaultDimensionAction({
                        parameters: { status: rowItem.status },
                        recordId,
                    });
                },
            },
            ui.menuSeparator(),
            {
                title: 'Delete',
                icon: 'bin',
                refreshesMainList: 'list',
                isDestructive: true,
                onError(error: string | (Error & { errors: Array<any> })) {
                    this.$.loader.isHidden = true;
                    return MasterDataUtils.formatError(this, error);
                },
                async onClick(_recordId: string, rowItem: ui.PartialNodeWithId<SalesInvoiceNode>) {
                    await MainListActions.deleteRecord<GraphApi>(this, {
                        _id: rowItem._id,
                        nodeName: '@sage/xtrem-sales/SalesInvoice',
                    });
                },
                isHidden(recordId, rowItem: ui.PartialNodeWithId<SalesInvoiceNode>) {
                    return displayButtons.isHiddenButtonDeleteAction({
                        parameters: { status: rowItem.status, taxCalculationStatus: rowItem.taxCalculationStatus },
                        recordId,
                    });
                },
            },
        ],
    },
})
export class SalesInvoice
    extends ui.Page<GraphApi, SalesInvoiceNode>
    implements financeInterfaces.PageWithDefaultDimensions
{
    _defaultDimensionsAttributes: financeInterfaces.DefaultDimensions;

    displayTaxesClicked = false;

    protected billToAddressDetailCounter = -4;

    initLinesLength = 0;

    fromNotificationHistory: boolean;

    canEditPaymentDataAfterPost: boolean;

    canEditTaxDataAfterPost: boolean;

    wereTaxesEdited: boolean;

    additionalItemId = '0';

    oldSalesUnit?: string;

    currentSelectedLineId: string;

    itemData: ItemData = { item: {}, itemCustomer: {} };

    private readonly orderStepSequenceCreate = ui.localize(
        '@sage/xtrem-sales/pages__sales_invoice__step_sequence_creation',
        'Create',
    );

    private readonly orderStepSequencePost = ui.localize(
        '@sage/xtrem-sales/pages__sales_invoice__step_sequence_post',
        'Post',
    );

    private readonly orderStepSequenceCredit = ui.localize(
        '@sage/xtrem-sales/pages__sales_invoice__step_sequence_credit',
        'Credit',
    );

    private readonly orderStepSequencePay = ui.localize(
        '@sage/xtrem-sales/pages__sales_invoice__step_sequence_pay',
        'Pay',
    );

    getDisplayStatusStepSequence(): Dict<ui.StepSequenceStatus> {
        if (this.paymentStatus.value === 'paid') {
            return this._setStepSequenceStatusObject({
                create: 'complete',
                post: 'complete',
                pay: 'complete',
            });
        }
        if (this.paymentStatus.value === 'partiallyPaid') {
            return this._setStepSequenceStatusObject({
                create: 'complete',
                post: 'complete',
                pay: 'current',
            });
        }
        if (this.creditStatus.value === 'credited') {
            return this._setStepSequenceStatusObject({
                create: 'complete',
                post: 'complete',
                credit: 'complete',
            });
        }
        if (this.creditStatus.value === 'partiallyCredited') {
            return this._setStepSequenceStatusObject({
                create: 'complete',
                post: 'complete',
                credit: 'current',
            });
        }
        if (['inProgress', 'error'].includes(this.status.value ?? '')) {
            return this._setStepSequenceStatusObject({
                create: 'complete',
                post: 'current',
                credit: 'incomplete',
            });
        }
        if (this.status.value === 'posted') {
            return this._setStepSequenceStatusObject({
                create: 'complete',
                post: 'complete',
                credit: 'incomplete',
            });
        }

        return this._setStepSequenceStatusObject({
            create: 'current',
            post: 'incomplete',
            credit: 'incomplete',
        });
    }

    private _manageDisplayApplicativePageActions(isDirty = false) {
        setDisplayOfCommonPageActions({
            page: this,
            isDirty,
            save: this.save,
            cancel: this.$standardCancelAction,
            duplicate: [],
            remove: this.$standardDeleteAction,
            businessActions: [
                this.$standardOpenCustomizationPageWizardAction,
                this.avalara,
                this.post,
                this.repost,
                this.createSalesCreditMemosFromInvoices,
            ],
        });
        this._manageDisplayAdditionalPageActions(isDirty);
    }

    private _manageDisplayAdditionalPageActions(isDirty = false) {
        this.manageDisplayButtonCRUDActions(isDirty);
        this.manageDisplayButtonAllOtherActions(isDirty);
    }

    private manageDisplayButtonCRUDActions(isDirty = false) {
        this.save.isDisabled = displayButtons.isDisabledButtonSaveAction({
            parameters: { status: this.status.value },
            recordId: this.$.recordId,
            isDirty,
        });
        this.$standardCancelAction.isDisabled = displayButtons.isDisabledButtonCancelAction({
            parameters: { status: this.status.value },
            recordId: this.$.recordId,
            isDirty,
        });
        this.$standardDeleteAction.isHidden = displayButtons.isHiddenButtonDeleteAction({
            parameters: { status: this.status.value, taxCalculationStatus: this.taxCalculationStatus.value },
            recordId: this.$.recordId,
        });
    }

    private manageDisplayButtonAllOtherActions(isDirty = false) {
        // footer business actions
        this.manageDisplayButtonAvalaraAction(isDirty, this.status.value);
        this.manageDisplayButtonPostAction(isDirty);
        this.manageDisplayButtonRepostAction(isDirty);
        this.manageDisplayButtonCreateSalesCreditMemosFromInvoicesAction(isDirty);
        this.manageDisplayButtonRecordReceipt(isDirty);
        // other header actions
        this.manageDisplayButtonPrintAction(isDirty);
        this.manageDisplayButtonSendMailAction(isDirty);
        this.manageDisplayButtonDefaultDimensionAction();
        this.manageDisplayLinePhantomRow();
        this.manageDisplayButtonGoToSysNotificationPageAction();
    }

    manageDisplayButtonAvalaraAction(isDirty: boolean, status: string | null) {
        noop(this._id.value, isDirty, status);
    }

    private manageDisplayButtonPostAction(isDirty: boolean) {
        this.post.isHidden = displayButtons.isHiddenButtonPostAction({
            parameters: { status: this.status.value, taxCalculationStatus: this.taxCalculationStatus.value },
            recordId: this.$.recordId,
            isDirty,
        });
    }

    private manageDisplayButtonRepostAction(isDirty: boolean) {
        this.repost.isHidden = displayButtons.isHiddenButtonRepostAction({
            parameters: { fromNotificationHistory: this.fromNotificationHistory },
            recordId: this.$.recordId,
            isDirty,
        });
    }

    private manageDisplayButtonCreateSalesCreditMemosFromInvoicesAction(isDirty: boolean) {
        this.createSalesCreditMemosFromInvoices.isHidden =
            displayButtons.isHiddenButtonCreateSalesCreditMemosFromInvoicesAction({
                parameters: {
                    status: this.status.value,
                    creditStatus: this.creditStatus.value,
                    taxCalculationStatus: this.taxCalculationStatus.value,
                },
                recordId: this.$.recordId,
                isDirty,
            });
    }

    private manageDisplayButtonRecordReceipt(isDirty: boolean) {
        this.recordReceipt.isHidden =
            !this.$.isServiceOptionEnabled('paymentTrackingOption') ||
            displayButtons.isHiddenButtonRecordReceiptAction({
                parameters: {
                    status: this.status.value,
                    paymentStatus: this.paymentStatus.value,
                    taxCalculationStatus: this.taxCalculationStatus.value,
                },
                recordId: this.$.recordId,
                isDirty,
            });
    }

    private manageDisplayButtonPrintAction(isDirty: boolean) {
        this.print.isHidden = displayButtons.isHiddenButtonPrintAction({
            parameters: {
                status: this.status.value,
                taxCalculationStatus: this.taxCalculationStatus.value,
                displayStatus: this.displayStatus.value,
                enablePrintButton: this.enablePrintButton.value ?? false,
            },
            recordId: this.$.recordId,
        });

        this.print.isDisabled = displayButtons.isDisabledButtonPrintAction({
            parameters: {
                status: this.status.value,
                enablePrintButton: this.enablePrintButton.value ?? false,
            },
            recordId: this.$.recordId,
            isDirty,
        });
    }

    private manageDisplayButtonSendMailAction(isDirty: boolean) {
        this.sendEmail.isHidden = displayButtons.isHiddenButtonSendMailAction({
            parameters: {
                status: this.status.value,
                taxCalculationStatus: this.taxCalculationStatus.value,
                displayStatus: this.displayStatus.value,
                enablePrintButton: this.enablePrintButton.value ?? false,
            },
            recordId: this.$.recordId,
        });

        this.sendEmail.isDisabled = displayButtons.isDisabledButtonSendMailAction({
            parameters: {
                status: this.status.value,
                enablePrintButton: this.enablePrintButton.value ?? false,
            },
            recordId: this.$.recordId,
            isDirty,
        });
    }

    private manageDisplayButtonGoToSysNotificationPageAction() {
        this.goToSysNotificationPage.isHidden = displayButtonsFinance.isHiddenButtonGoToSysNotificationPageAction({
            parameters: {
                financeIntegrationStatus: this.postingDetails.value.map(
                    financeDocument => financeDocument.postingStatus ?? 'posted',
                ),
            },
            recordId: this.$.recordId,
        });
    }

    private manageDisplayLinePhantomRow() {
        this.lines.isPhantomRowDisabled = displayButtons.isDisabledLinePhantomRow({
            parameters: {
                status: this.status.value,
                site: this.site.value,
                billToCustomer: this.billToCustomer.value,
                date: this.date.value,
                dueDate: this.dueDate.value,
                currency: this.currency.value,
            },
        });
    }

    private manageDisplayButtonDefaultDimensionAction() {
        this.defaultDimension.isHidden = displayButtons.isHiddenButtonDefaultDimensionAction({
            parameters: { status: this.status.value },
            recordId: this.$.recordId,
        });

        this.defaultDimension.isDisabled = displayButtons.isDisabledButtonDefaultDimensionAction({
            parameters: {
                site: this.site.value,
                dueDate: this.dueDate.value,
                paymentTerm: this.paymentTerm.value,
                date: this.date.value,
                currency: this.currency.value,
            },
        });
    }

    getSerializedValues() {
        const { values } = this.$;
        if (Number(values.billToAddress._id) < 0) {
            delete values.billToAddress._id;
        }

        this.getLineValues(values.lines);
        return values;
    }

    // TODO: todo when implementing addressesDetail on lines
    // eslint-disable-next-line class-methods-use-this
    getLineValues(lines?: Partial<Dict<any>>[]) {
        lines?.forEach(line => {
            if (line.consumptionAddress && Number(line.consumptionAddress._id) < 0) {
                delete line.consumptionAddress._id;
            }
        });
    }

    @ui.decorators.pageAction<SalesInvoice>({
        icon: 'none',
        title: 'Set dimensions',
        async onClick() {
            const defaultedFromItem = await attributesAndDimensions.getAttributesAndDimensionsFromItem({
                page: this,
                dimensionDefinitionLevel: 'salesDirect',
                companyId: Number(this.site?.value?.legalCompany?._id),
            });
            this._defaultDimensionsAttributes = await dimensionPanelHelpers.editAndApplyDefaultDimensionsIfNeeded(
                this,
                this.lines,
                this._defaultDimensionsAttributes,
                () => true,
                defaultedFromItem,
            );
        },
    })
    defaultDimension: ui.PageAction;

    @ui.decorators.pageAction<SalesInvoice>({
        title: 'Print',
        icon: 'print',
        access: { node: '@sage/xtrem-sales/SalesInvoice', bind: 'beforePrint' },
        async onClick() {
            if (this.taxCalculationStatus.value === 'failed') {
                showTaxCalcFiledPrintBlockingMessage(this);
                return;
            }
            if (this.status.value === 'error') {
                showErrorsBlockingMessage(this);
                return;
            }
            if (this.$.recordId) {
                await actionFunctions.printSalesInvoiceAction(this, this.$.recordId);
            }
        },
    })
    print: ui.PageAction;

    @ui.decorators.pageAction<SalesInvoice>({
        // To be implemented into the page extension from the avalara-gateway package
        onClick() {},
        isHidden: true,
    })
    avalara: ui.PageAction;

    @ui.decorators.textField<SalesInvoice>({}) _id: ui.fields.Text;

    @ui.decorators.section<SalesInvoice>({ title: 'Lines' }) itemsSection: ui.containers.Section;

    @ui.decorators.section<SalesInvoice>({ title: 'Header section', isTitleHidden: true })
    headerSection: ui.containers.Section;

    @ui.decorators.block<SalesInvoice>({
        parent() {
            return this.headerSection;
        },
        width: 'large',
    })
    headerStepSequenceBlock: ui.containers.Block;

    @ui.decorators.block<SalesInvoice>({
        parent() {
            return this.headerSection;
        },
    })
    headerBlock: ui.containers.Block;

    @ui.decorators.stepSequenceField<SalesInvoice>({
        parent() {
            return this.headerStepSequenceBlock;
        },
        options() {
            return !['partiallyPaid', 'paid'].includes(this.paymentStatus.value ?? 'notPaid')
                ? [this.orderStepSequenceCreate, this.orderStepSequencePost, this.orderStepSequenceCredit]
                : [this.orderStepSequenceCreate, this.orderStepSequencePost, this.orderStepSequencePay];
        },
        width: 'small',
        isTransient: true,
        isFullWidth: true,
    })
    invoiceStepSequence: ui.fields.StepSequence;

    @ui.decorators.referenceField<SalesInvoice, Site>({
        parent() {
            return this.headerBlock;
        },
        width: 'small-medium',
        title: 'Site',
        lookupDialogTitle: 'Select site',
        minLookupCharacters: 0,
        isAutoSelectEnabled: true,
        fetchesDefaults: true,
        isMandatory: true,
        isReadOnly() {
            return !!this.$.recordId || this.lines.value?.length > 0;
        },
        columns: [
            ui.nestedFields.text({ bind: { businessEntity: { name: true } }, title: 'Name' }),
            ui.nestedFields.text({ bind: { businessEntity: { id: true } }, title: 'ID' }),
            ui.nestedFields.technical<SalesInvoice, Site, BusinessEntity>({
                node: '@sage/xtrem-master-data/BusinessEntity',
                bind: 'businessEntity',
                nestedFields: [
                    ui.nestedFields.technical({ bind: 'name' }),
                    ui.nestedFields.technical({ bind: 'id' }),
                    ui.nestedFields.technical<SalesInvoice, BusinessEntity, Currency>({
                        node: '@sage/xtrem-master-data/Currency',
                        bind: 'currency',
                        nestedFields: [
                            ui.nestedFields.technical({ bind: 'name' }),
                            ui.nestedFields.technical({ bind: 'id' }),
                            ui.nestedFields.technical({ bind: 'symbol' }),
                            ui.nestedFields.technical({ bind: 'decimalDigits' }),
                        ],
                    }),
                ],
            }),
            ui.nestedFields.reference<SalesInvoice, Site, Company>({
                node: '@sage/xtrem-system/Company',
                tunnelPage: '@sage/xtrem-master-data/Company',
                title: 'Company',
                bind: 'legalCompany',
                valueField: 'name',
                helperTextField: 'id',
                columns: [
                    ui.nestedFields.text({ bind: 'name' }),
                    ui.nestedFields.text({ bind: 'id' }),
                    ui.nestedFields.technical<SalesInvoice, Company, Legislation>({
                        node: '@sage/xtrem-structure/Legislation',
                        bind: 'legislation',
                        nestedFields: [
                            ui.nestedFields.technical({ bind: 'name' }),
                            ui.nestedFields.technical({ bind: 'id' }),
                        ],
                    }),
                    ui.nestedFields.technical<SalesInvoice, Company, Currency>({
                        bind: 'currency',
                        node: '@sage/xtrem-master-data/Currency',
                        nestedFields: [
                            ui.nestedFields.technical({ bind: 'name' }),
                            ui.nestedFields.technical({ bind: 'id' }),
                            ui.nestedFields.technical({ bind: 'symbol' }),
                            ui.nestedFields.technical({ bind: 'decimalDigits' }),
                        ],
                    }),
                    ui.nestedFields.technical<SalesInvoice, Company, BankAccount>({
                        bind: 'bankAccount',
                        node: '@sage/xtrem-finance-data/BankAccount',
                        nestedFields: [
                            ui.nestedFields.technical({ bind: 'name' }),
                            ui.nestedFields.technical({ bind: 'id' }),
                            ui.nestedFields.technical({ bind: { financialSite: { _id: true } } }),
                            ui.nestedFields.technical<SalesInvoice, BankAccount, Currency>({
                                bind: 'currency',
                                node: '@sage/xtrem-master-data/Currency',
                                nestedFields: [
                                    ui.nestedFields.technical({ bind: '_id' }),
                                    ui.nestedFields.technical({ bind: 'id' }),
                                    ui.nestedFields.technical({ bind: 'symbol' }),
                                    ui.nestedFields.technical({ bind: 'decimalDigits' }),
                                ],
                            }),
                        ],
                    }),
                    ui.nestedFields.technical({ bind: 'taxEngine' }),
                ],
            }),
            ui.nestedFields.technical<SalesInvoice, Site, BusinessEntityAddress>({
                bind: 'primaryAddress',
                node: '@sage/xtrem-master-data/BusinessEntityAddress',
                nestedFields: [
                    ui.nestedFields.technical<SalesInvoice, BusinessEntityAddress, Country>({
                        bind: 'country',
                        node: '@sage/xtrem-structure/Country',
                        nestedFields: [
                            ui.nestedFields.technical({ bind: 'name' }),
                            ui.nestedFields.technical({ bind: 'id' }),
                        ],
                    }),
                ],
            }),
            ui.nestedFields.technical({ bind: 'description' }),
            ui.nestedFields.technical({ bind: 'isInventory' }),
            ui.nestedFields.technical({ bind: 'isSales' }),
            ui.nestedFields.technical({ bind: 'isFinance' }),
            ui.nestedFields.technical<SalesInvoice, Site, Site>({
                node: '@sage/xtrem-system/Site',
                bind: 'financialSite',
                nestedFields: [ui.nestedFields.technical({ bind: 'name' }), ui.nestedFields.technical({ bind: 'id' })],
            }),
        ],
        filter() {
            return { isSales: true };
        },
        async onChange() {
            this.manageDisplayButtonDefaultDimensionAction();
            this.manageDisplayLinePhantomRow();
            this.rateDescription.isHidden = isExchangeRateHidden(
                this.currency.value,
                this.site.value,
                this.billToCustomer.value,
            );
            if (this.site.value?.legalCompany?.taxEngine) {
                this.taxEngine.value = this.site.value.legalCompany.taxEngine;
                TotalTaxCalculator.getInstance().taxEngineProperty = this.taxEngine.value;
            }
            if (this.site.value && this.billToCustomer.value) {
                this._defaultDimensionsAttributes = await initDefaultDimensions({
                    page: this,
                    dimensionDefinitionLevel: 'salesDirect',
                    site: this.site.value,
                    customer: this.billToCustomer.value,
                });
            }
        },
    })
    site: ui.fields.Reference<Site>;

    @ui.decorators.referenceField<SalesInvoice, Customer>({
        parent() {
            return this.headerBlock;
        },
        width: 'small-medium',
        node: '@sage/xtrem-master-data/Customer',
        title: 'Bill-to customer',
        lookupDialogTitle: 'Select bill-to customer',
        minLookupCharacters: 0,
        isAutoSelectEnabled: true,
        fetchesDefaults: true,
        shouldSuggestionsIncludeColumns: true,
        isMandatory: true,
        isReadOnly() {
            return !!this.$.recordId || this.lines.value?.length > 0;
        },
        columns: [
            ui.nestedFields.text({ bind: { businessEntity: { name: true } }, title: 'Name' }),
            ui.nestedFields.text({ bind: { businessEntity: { id: true } }, title: 'ID' }),
            ui.nestedFields.text({ bind: { businessEntity: { taxIdNumber: true } }, title: 'Tax ID' }),
            ui.nestedFields.text({ bind: { businessEntity: { country: { name: true } } }, title: 'Country' }),
            ui.nestedFields.technical({ bind: 'isOnHold' }),
            ui.nestedFields.technical<SalesInvoice, Customer, BusinessEntity>({
                bind: 'businessEntity',
                node: '@sage/xtrem-master-data/BusinessEntity',
                nestedFields: [
                    ui.nestedFields.technical({ bind: 'name' }),
                    ui.nestedFields.technical({ bind: 'id' }),
                    ui.nestedFields.technical<SalesInvoice, BusinessEntity, Country>({
                        bind: 'country',
                        node: '@sage/xtrem-structure/Country',
                        nestedFields: [
                            ui.nestedFields.technical({ bind: 'name' }),
                            ui.nestedFields.technical({ bind: 'id' }),
                        ],
                    }),
                    ui.nestedFields.technical<SalesInvoice, BusinessEntity, Currency>({
                        bind: 'currency',
                        node: '@sage/xtrem-master-data/Currency',
                        nestedFields: [
                            ui.nestedFields.technical({ bind: 'name' }),
                            ui.nestedFields.technical({ bind: 'id' }),
                            ui.nestedFields.technical({ bind: 'symbol' }),
                            ui.nestedFields.technical({ bind: 'decimalDigits' }),
                        ],
                    }),
                ],
            }),
            ui.nestedFields.technical<SalesInvoice, Customer, PaymentTerm>({
                bind: 'paymentTerm',
                node: '@sage/xtrem-master-data/PaymentTerm',
                nestedFields: [
                    ui.nestedFields.technical({ bind: 'name' }),
                    ui.nestedFields.technical({ bind: 'description' }),
                ],
            }),
            ui.nestedFields.technical({ bind: 'name' }),
            ui.nestedFields.technical({ bind: 'id' }),
        ],
        async onChange() {
            await this.fetchDefaultsFromBillToCustomer();
            this.manageDisplayLinePhantomRow();
            if (this.billToCustomer.value?.businessEntity?.currency) {
                this.currency.value = this.billToCustomer.value.businessEntity.currency;
            }
            this.updatePrefixScaleOnCurrencyFields();
            this.manageDisplayButtonDefaultDimensionAction();
            this.rateDescription.isHidden = isExchangeRateHidden(
                this.currency.value,
                this.site.value,
                this.billToCustomer.value,
            );
            if (this.site.value && this.billToCustomer.value) {
                this._defaultDimensionsAttributes = await initDefaultDimensions({
                    page: this,
                    dimensionDefinitionLevel: 'salesDirect',
                    site: this.site.value,
                    customer: this.billToCustomer.value,
                });
            }
        },
    })
    billToCustomer: ui.fields.Reference<Customer>;

    @ui.decorators.textField<SalesInvoice>({
        parent() {
            return this.headerBlock;
        },
        width: 'small-medium',
        title: 'Number',
        isReadOnly() {
            return !!this.$.recordId;
        },
    })
    number: ui.fields.Text;

    @ui.decorators.dateField<SalesInvoice>({
        parent() {
            return this.headerBlock;
        },
        width: 'small-medium',
        title: 'Invoice date',
        isMandatory: true,
        fetchesDefaults: true,
        onError() {
            this.$.loader.isHidden = true;
        },
        async onChange() {
            this.dueDateDefault();
            this.manageDisplayButtonDefaultDimensionAction();
            this.manageDisplayLinePhantomRow();
            await massPriceDeterminationCalculation(
                // TS 5.2 added any cast
                this as any,
                ui.localize(
                    '@sage/xtrem-sales/pages__sales_invoice__on_price_determination_confirmation_invoice_date',
                    'The invoice date has been updated. Do you want to recalculate prices, discounts and charges?',
                ),
                ui.localize(
                    '@sage/xtrem-sales/pages__sales_invoice__on_price_determination_too_much_lines_need_save',
                    'The sales invoice contains more than 9 lines. You need to save it before mass calculation. Do you want to save the invoice and continue?',
                ),
            );
        },
    })
    date: ui.fields.Date;

    @ui.decorators.tile<SalesInvoice>({
        parent() {
            return this.headerSection;
        },
    })
    tileContainer: ui.containers.Tile;

    @ui.decorators.countField<SalesInvoice>({
        parent() {
            return this.tileContainer;
        },
        bind: 'lines',
        title: 'Number of items',
        size: 'medium',
    })
    salesInvoiceLineCount: ui.fields.Count;

    @ui.decorators.aggregateField<SalesInvoice>({
        parent() {
            return this.tileContainer;
        },
        aggregateOn: 'amountExcludingTax',
        aggregationMethod: 'sum',
        title: 'Total excluding tax',
        bind: 'lines',
        unit() {
            return this.currency.value;
        },
        scale: null,
        width: 'medium',
    })
    salesInvoiceExcludingTax: ui.fields.Aggregate;

    @ui.decorators.aggregateField<SalesInvoice>({
        parent() {
            return this.tileContainer;
        },
        aggregateOn: 'amountIncludingTax',
        aggregationMethod: 'sum',
        title: 'Total including tax',
        bind: 'lines',
        unit() {
            return this.currency.value;
        },
        scale: null,
        width: 'medium',
    })
    salesInvoiceIncludingTax: ui.fields.Aggregate;

    @ui.decorators.numericField<SalesInvoice>({
        parent() {
            return this.tileContainer;
        },
        bind: 'totalAmountPaid',
        title: 'Total payments',
        size: 'medium',
        unit() {
            return this.currency.value;
        },
        scale: null,
        isHidden() {
            return !this.$.isServiceOptionEnabled('paymentTrackingOption');
        },
    })
    amountPaid: ui.fields.Numeric;

    @ui.decorators.numericField<SalesInvoice>({
        parent() {
            return this.tileContainer;
        },
        title: 'Net balance',
        size: 'medium',
        unit() {
            return this.currency.value;
        },
        isHidden() {
            return !this.$.isServiceOptionEnabled('paymentTrackingOption');
        },
    })
    netBalance: ui.fields.Numeric;

    @ui.decorators.referenceField<SalesInvoice, Currency>({
        parent() {
            return this.informationBlock;
        },
        title: 'Company currency',
        lookupDialogTitle: 'Select company currency',
        node: '@sage/xtrem-master-data/Currency',
        tunnelPage: '@sage/xtrem-master-data/Currency',
        valueField: 'name',
        helperTextField: 'id',
        isMandatory: true,
        fetchesDefaults: true,
        isHidden: true,
        columns: [
            ui.nestedFields.text({ bind: 'name' }),
            ui.nestedFields.text({ bind: 'id', title: 'ISO 4217 code' }),
            ui.nestedFields.text({ title: 'Symbol', bind: 'symbol', canFilter: false }),
            ui.nestedFields.technical({ bind: 'decimalDigits' }),
            ui.nestedFields.technical({ bind: 'rounding' }),
        ],
        minLookupCharacters: 1,
        placeholder: 'Select currency',
        width: 'small',
        onChange() {
            this.applyCompanyCurrency();
        },
        isReadOnly() {
            if (this.currency.value === null) {
                return false;
            }
            return (this.lines.value?.length || 0) !== 0;
        },
    })
    companyCurrency: ui.fields.Reference<Currency>;

    get salesSiteCurrency() {
        return this.site.value?.legalCompany?.currency;
    }

    private applyCompanyCurrency() {
        this.totalAmountExcludingTaxInCompanyCurrency.scale = this.salesSiteCurrency?.decimalDigits ?? 2;
        this.totalAmountExcludingTaxInCompanyCurrency.prefix = this.salesSiteCurrency?.symbol || '';
        this.totalAmountIncludingTaxInCompanyCurrency.scale = this.salesSiteCurrency?.decimalDigits ?? 2;
        this.totalAmountIncludingTaxInCompanyCurrency.prefix = this.salesSiteCurrency?.symbol || '';
    }

    @ui.decorators.dateField<SalesInvoice>({
        parent() {
            return this.informationBlock;
        },
        title: 'Exchange rate date',
        isMandatory: true,
        isReadOnly: true,
        fetchesDefaults: true,
        isHidden() {
            // XT-24062 - Hide exchange rate for now
            // return isExchangeRateHidden(this.currency.value, this.site.value, this.billToCustomer.value);
            return true;
        },
    })
    fxRateDate: ui.fields.Date;

    @ui.decorators.numericField<SalesInvoice>({
        parent() {
            return this.informationBlock;
        },
        title: 'Exchange rate',
        isReadOnly: true,
        scale() {
            return this.companyFxRate.value ? Math.max(scaleOfCurrent(this.companyFxRate.value), 2) : 2;
        },
        isHidden: true,
    })
    companyFxRate: ui.fields.Numeric;

    @ui.decorators.numericField<SalesInvoice>({
        parent() {
            return this.informationBlock;
        },
        title: 'Exchange rate divisor',
        isReadOnly: true,
        scale() {
            return this.companyFxRateDivisor.value ? Math.max(scaleOfCurrent(this.companyFxRateDivisor.value), 2) : 2;
        },
        isHidden: true,
    })
    companyFxRateDivisor: ui.fields.Numeric;

    @ui.decorators.dropdownListField<SalesInvoice>({
        parent() {
            return this.informationBlock;
        },
        title: 'Tax engine',
        bind: 'taxEngine',
        optionType: '@sage/xtrem-finance-data/TaxEngine',
        isHidden: true,
    })
    taxEngine: ui.fields.DropdownList;

    @ui.decorators.labelField<SalesInvoice>({
        parent() {
            return this.informationBlock;
        },
        title: 'Posting status',
        bind: 'status',
        optionType: '@sage/xtrem-sales/SalesInvoiceStatus',
        isHidden: true,
    })
    status: ui.fields.Label;

    @ui.decorators.labelField<SalesInvoice>({
        title: 'Display status',
        bind: 'displayStatus',
        optionType: '@sage/xtrem-sales/SalesInvoiceDisplayStatus',
        style() {
            return PillColorCommon.getDisplayStatusPillFeatures(this.displayStatus.value);
        },
        async onClick() {
            const allPosted = this.postingDetails.value.every(detail => detail.postingStatus === 'posted');
            if (this.displayStatus.value === 'postingInProgress' && this.status.value === 'inProgress' && allPosted) {
                await this.$.graph
                    .node('@sage/xtrem-sales/SalesInvoice')
                    .mutations.enforceStatusPosted(true, {
                        invoice: this._id.value || '',
                    })
                    .execute();
                await this.$.router.refresh();
            }
        },
    })
    displayStatus: ui.fields.Label<SalesInvoiceDisplayStatus>;

    @ui.decorators.switchField<SalesInvoice>({ isHidden: true })
    enablePrintButton: ui.fields.Switch;

    @ui.decorators.labelField<SalesInvoice>({
        parent() {
            return this.informationBlock;
        },
        title: 'Credit status',
        bind: 'creditStatus',
        isHidden: true,
        optionType: '@sage/xtrem-sales/SalesDocumentCreditStatus',
    })
    creditStatus: ui.fields.Label;

    @ui.decorators.referenceField<SalesInvoice, BusinessEntityAddress>({
        parent() {
            return this.informationBlock;
        },
        node: '@sage/xtrem-master-data/BusinessEntityAddress',
        title: 'Bill-to address',
        lookupDialogTitle: 'Select bill-to address',
        valueField: 'name',
        minLookupCharacters: 0,
        isAutoSelectEnabled: true,
        fetchesDefaults: true,
        isMandatory: true,
        isHidden: true,
        columns: [
            ui.nestedFields.checkbox({ bind: 'isActive', title: 'Active' }),
            ui.nestedFields.text({ bind: 'name' }),
            ui.nestedFields.text({ bind: 'addressLine1', title: 'Address line 1' }),
            ui.nestedFields.text({ bind: 'addressLine2', title: 'Address line 2' }),
            ui.nestedFields.text({ bind: 'city', title: 'City' }),
            ui.nestedFields.text({ bind: 'region', title: 'Region' }),
            ui.nestedFields.text({ bind: 'postcode', title: 'ZIP code' }),
            ui.nestedFields.reference<SalesInvoice, BusinessEntityAddress, Country>({
                bind: 'country',

                columns: [
                    ui.nestedFields.text({ bind: 'name' }),
                    ui.nestedFields.text({ bind: 'id', title: 'ISO 3166-1 alpha-2 code' }),
                    ui.nestedFields.technical({ bind: 'regionLabel' }),
                    ui.nestedFields.technical({ bind: 'zipLabel' }),
                ],
            }),
            ui.nestedFields.text({ bind: 'locationPhoneNumber', title: 'Phone number' }),
            ui.nestedFields.technical({
                bind: 'businessEntity',
                node: '@sage/xtrem-master-data/BusinessEntity',
                nestedFields: [ui.nestedFields.technical({ bind: 'name' }), ui.nestedFields.technical({ bind: 'id' })],
            }),
        ],
        filter() {
            return this.billToCustomer.value
                ? { businessEntity: { id: this.billToCustomer.value.businessEntity?.id } }
                : {};
        },
        async onChange() {
            await this.fetchDefaultsFromBillToAddress();
        },
    })
    billToLinkedAddress: ui.fields.Reference<BusinessEntityAddress>;

    @ui.decorators.tableField<SalesInvoice, SalesInvoiceLineBinding>({
        bind: 'lines',
        title: 'Lines',
        isTitleHidden: true,
        canSelect: false,
        canAddNewLine: true,
        hasLineNumbers: true,
        node: '@sage/xtrem-sales/SalesInvoiceLine',
        orderBy: { _sortValue: +1 },
        parent() {
            return this.itemsSection;
        },
        columns: [
            ui.nestedFields.image({ bind: { item: { image: true } }, isExcludedFromMainField: true }),
            ui.nestedFields.technical({ bind: { externalNote: { value: true } } }),
            ui.nestedFields.technical({ bind: { internalNote: { value: true } } }),
            ui.nestedFields.switch({
                bind: 'isExternalNote',
                title: 'Add notes to customer document',
                isExcludedFromMainField: true,
                isDisabled() {
                    return ['posted', 'inProgress', 'error'].includes(this.status.value || '');
                },
            }),
            ui.nestedFields.dropdownList({
                title: 'Origin',
                bind: 'origin',
                optionType: '@sage/xtrem-sales/salesOriginDocumentType',
                isReadOnly: true,
            }),
            ui.nestedFields.label({
                title: 'Tax status',
                bind: 'taxCalculationStatus',
                isExcludedFromMainField: true,
                isHidden() {
                    return (
                        this.taxCalculationStatus.value !== 'failed' || this.taxEngine.value !== 'genericTaxCalculation'
                    );
                },
                optionType: '@sage/xtrem-master-data/TaxCalculationStatus',
                style: (_id, rowData) =>
                    PillColorCommon.getLabelColorByStatus('TaxCalculationStatus', rowData?.taxCalculationStatus),
                async onClick(_rowId, rowItem) {
                    this.displayTaxesClicked = true;
                    await this.callDisplayTaxes(rowItem);
                    this.displayTaxesClicked = false;
                },
            }),
            ui.nestedFields.technical({ bind: 'taxDate' }),
            ui.nestedFields.reference<SalesInvoice, SalesInvoiceLineBinding, Item>({
                title: 'Item',
                bind: 'item',
                node: '@sage/xtrem-master-data/Item',
                tunnelPage: '@sage/xtrem-master-data/Item',
                lookupDialogTitle: 'Select item',
                valueField: 'name',
                helperTextField: 'id',
                minLookupCharacters: 3,
                shouldSuggestionsIncludeColumns: true,
                isAutoSelectEnabled: true,
                isMandatory: true,
                isDisabled() {
                    return !!this.$.recordId;
                },
                columns: [
                    ui.nestedFields.text({ bind: 'name' }),
                    ui.nestedFields.text({ bind: 'id' }),
                    ui.nestedFields.text({ bind: 'description' }),
                    ui.nestedFields.text({ title: 'Category', bind: { category: { name: true } } }),
                    ui.nestedFields.image({ bind: 'image', isHidden: true }),
                    ui.nestedFields.technical({ bind: 'type' }),
                    ui.nestedFields.technical({ bind: 'isStockManaged' }),
                    ui.nestedFields.technical<SalesInvoice, Item, UnitOfMeasure>({
                        bind: 'stockUnit',
                        node: '@sage/xtrem-master-data/UnitOfMeasure',
                        nestedFields: [
                            ui.nestedFields.technical({ bind: 'id' }),
                            ui.nestedFields.technical({ bind: 'symbol' }),
                            ui.nestedFields.technical({ bind: 'decimalDigits' }),
                        ],
                    }),
                    ui.nestedFields.technical<SalesInvoice, Item, UnitOfMeasure>({
                        bind: 'salesUnit',
                        node: '@sage/xtrem-master-data/UnitOfMeasure',
                        nestedFields: [
                            ui.nestedFields.technical({ bind: 'id' }),
                            ui.nestedFields.technical({ bind: 'symbol' }),
                            ui.nestedFields.technical({ bind: 'decimalDigits' }),
                        ],
                    }),
                    ui.nestedFields.technical({ bind: 'minimumSalesQuantity' }),
                    ui.nestedFields.technical({ bind: 'maximumSalesQuantity' }),
                ],
                async onChange(_rowId, rowData: ui.PartialCollectionValue<SalesInvoiceLine>) {
                    if (rowData?.item) {
                        rowData.itemDescription = rowData.item.description
                            ? rowData.item.description
                            : rowData.item.name;
                        await this.checkItemCustomer(rowData, true);
                        rowData.stockUnit = rowData.item.stockUnit;
                        const quantity = rowData.quantity ? +rowData.quantity : 0;
                        const unitToStockUnitConversionFactor = rowData.unitToStockUnitConversionFactor
                            ? +rowData.unitToStockUnitConversionFactor
                            : 0;
                        rowData.quantityInStockUnit = String(quantity * unitToStockUnitConversionFactor);
                        await this.retrieveItemSites(rowData);
                        if (rowData.providerSite) {
                            await this.getPriceDetermination(rowData, true);
                            rowData.providerSite =
                                (await checkStockSite(this, rowData.item, rowData.providerSite)) || undefined;
                        }
                        await this.updateGrossProfit(rowData);
                    }
                    if (this.site.value && this.billToCustomer.value && rowData.item) {
                        const { storedAttributes, storedDimensions } =
                            await attributesAndDimensions.defaultAttributesAndDimensionsWithItem({
                                page: this,
                                _defaultDimensionsAttributes: this._defaultDimensionsAttributes,
                                dimensionDefinitionLevel: 'salesDirect',
                                site: this.site.value,
                                customer: this.billToCustomer.value,
                                item: rowData.item,
                            });
                        rowData.storedAttributes = storedAttributes;
                        rowData.storedDimensions = storedDimensions;
                    }
                    this.lines.addOrUpdateRecordValue(rowData);
                },
            }),
            ui.nestedFields.text({
                title: 'Item ID',
                bind: { item: { id: true } },
                isReadOnly: true,
            }),
            ui.nestedFields.text({
                title: 'Item description',
                bind: 'itemDescription',
                width: 'large',
                isHiddenOnMainField: true,
                isDisabled() {
                    return ['posted', 'inProgress', 'error'].includes(this.status.value || '');
                },
            }),
            ui.nestedFields.reference<SalesInvoice, SalesInvoiceLineBinding, UnitOfMeasure>({
                title: 'Sales unit',
                bind: 'unit',
                node: '@sage/xtrem-master-data/UnitOfMeasure',
                tunnelPage: '@sage/xtrem-master-data/UnitOfMeasure',
                valueField: 'symbol',
                helperTextField: 'name',
                lookupDialogTitle: 'Select unit',
                minLookupCharacters: 1,
                isAutoSelectEnabled: true,
                isMandatory: true,
                isReadOnly: (_value, rowValue) => rowValue?.origin !== 'direct',
                isDisabled() {
                    return ['posted', 'inProgress', 'error'].includes(this.status.value || '');
                },
                columns: [
                    ui.nestedFields.text({ bind: 'name' }),
                    ui.nestedFields.technical({ bind: 'id' }),
                    ui.nestedFields.text({ bind: 'symbol', title: 'Symbol' }),
                    ui.nestedFields.technical({ bind: 'decimalDigits' }),
                ],
                filter(rowData) {
                    const filterArray: Array<String> = [this.additionalItemId];
                    if (rowData.unit) {
                        filterArray.push(rowData.unit._id);
                        if (rowData.item && rowData.item.salesUnit._id !== rowData.unit._id) {
                            filterArray.push(rowData.item.salesUnit._id);
                        }
                    }
                    if (rowData.stockUnit) {
                        filterArray.push(rowData.stockUnit._id);
                    }
                    return {
                        _id: {
                            _in: filterArray,
                        } as any,
                    };
                },
                async onChange(_rowId, rowData: ui.PartialCollectionValue<SalesInvoiceLine>) {
                    if (rowData.item) {
                        rowData.item.minimumSalesQuantity = String(this.itemData.item.minimumSalesQuantity);
                        rowData.item.maximumSalesQuantity = String(this.itemData.item.maximumSalesQuantity);
                    }
                    if (rowData.unit?._id === rowData.stockUnit?._id) {
                        rowData.unitToStockUnitConversionFactor = '1';
                    } else if (rowData.unit?._id === rowData.item?.salesUnit?._id) {
                        rowData.unitToStockUnitConversionFactor = String(this.itemData.item.conversionFactor);
                    } else if (rowData.item && rowData.salesUnit?._id === this.additionalItemId) {
                        rowData.unitToStockUnitConversionFactor = String(this.itemData.item.conversionFactor);
                        rowData.item.minimumSalesQuantity = String(this.itemData.itemCustomer.minimumSalesQuantity);
                        rowData.item.maximumSalesQuantity = String(this.itemData.itemCustomer.maximumSalesQuantity);
                    }
                    rowData.quantityInStockUnit = String(
                        +(rowData.unitToStockUnitConversionFactor ?? 1) * +(rowData.quantity ?? 0),
                    );

                    const isPriceDetermined = await this.priceDetermination2(
                        rowData,
                        ui.localize(
                            '@sage/xtrem-sales/pages__sales_invoice__on_price_determination_confirmation_sales_unit',
                            'The sales unit has been updated. Do you want to recalculate prices, discounts and charges?',
                        ),
                    );
                    if (!isPriceDetermined) {
                        await convertGrossPriceToUnit2(this, this.itemData, this.additionalItemId, rowData);
                    } else {
                        this.oldSalesUnit = rowData.unit?._id;
                    }
                    await this.updateGrossProfit(rowData);
                },
            }),
            ui.nestedFields.numeric({
                title: 'Quantity in sales unit',
                bind: 'quantity',
                isMandatory: true,
                isReadOnly: (_value, rowValue) => rowValue?.origin !== 'direct',
                isDisabled() {
                    return ['posted', 'inProgress', 'error'].includes(this.status.value || '');
                },
                scale: (_value, rowData) => MasterDataUtils.getScaleValue(2, rowData?.unit?.decimalDigits),
                postfix: (_value, rowData) => rowData?.unit?.symbol,
                validation(val, rowData: SalesInvoiceLine) {
                    if (val <= 0) {
                        return ui.localize(
                            '@sage/xtrem-sales/pages__sales_invoice__quantity_in_sales_unit_negative_value',
                            'The quantity in sales unit must not be less than or equal to 0.',
                        );
                    }

                    if (
                        parseFloat(rowData.item.minimumSalesQuantity) !== 0 &&
                        val < parseFloat(rowData.item.minimumSalesQuantity)
                    ) {
                        return ui.localize(
                            '@sage/xtrem-sales/pages__sales__quantity_in_sales_unit_minimum_quantity',
                            'The quantity in sales unit ({{value}}) must not be less than {{minValue}}.',
                            {
                                value: val,
                                minValue: rowData.item.minimumSalesQuantity,
                            },
                        );
                    }

                    if (
                        parseFloat(rowData.item.maximumSalesQuantity) !== 0 &&
                        val > parseFloat(rowData.item.maximumSalesQuantity)
                    ) {
                        return ui.localize(
                            '@sage/xtrem-sales/pages__sales__quantity_in_sales_unit_maximum_quantity',
                            'The quantity in sales unit ({{value}}) must not be larger than {{maxValue}}.',
                            {
                                value: val,
                                maxValue: rowData.item.maximumSalesQuantity,
                            },
                        );
                    }
                    return undefined;
                },
                async onChange(rowId, rowData: ui.PartialCollectionValue<SalesInvoiceLine>) {
                    const oldQuantityInStockUnit = rowData.quantityInStockUnit;
                    if (rowData.quantity && rowData.unitToStockUnitConversionFactor) {
                        rowData.quantityInStockUnit = String(
                            +rowData.quantity * +rowData.unitToStockUnitConversionFactor,
                        );
                    }
                    if (
                        (rowData.quantityInStockUnit ? parseFloat(rowData.quantityInStockUnit) : 0) !==
                        (oldQuantityInStockUnit ? parseFloat(oldQuantityInStockUnit) : 0)
                    ) {
                        const isPriceDetermined = await this.priceDetermination2(
                            rowData,
                            ui.localize(
                                '@sage/xtrem-sales/pages__sales_invoice__on_price_determination_confirmation_sales_quantity',
                                'The sales quantity has been updated. Do you want to recalculate prices, discounts and charges?',
                            ),
                        );
                        if (!isPriceDetermined) {
                            await this.calculatePrices(rowData);
                        }
                        await this.updateGrossProfit(rowData);
                    }
                },
            }),
            ui.nestedFields.numeric({
                title: 'Stock unit conversion factor',
                bind: 'unitToStockUnitConversionFactor',
                isReadOnly: true,
                scale: 10,
                isHidden: (_value, rowData) => rowData?.item?.type === 'service',
                isHiddenOnMainField: true,
            }),
            ui.nestedFields.numeric({
                title: 'Quantity in stock unit',
                bind: 'quantityInStockUnit',
                isReadOnly: true,
                scale: (_value, rowData) => rowData?.stockUnit?.decimalDigits || 0,
                isHidden: (_value, rowData) => rowData?.item?.type === 'service',
                isHiddenOnMainField: true,
            }),
            ui.nestedFields.numeric({
                title: 'Gross price',
                bind: 'grossPrice',
                isDisabled() {
                    return ['posted', 'inProgress', 'error'].includes(this.status.value || '');
                },
                unit() {
                    return this.currency.value;
                },
                validation(val) {
                    if (val < 0) {
                        return ui.localize(
                            '@sage/xtrem-sales/pages__sales_invoice__gross_price_greater_or_equal_to_0',
                            'The gross price needs to be greater than or equal to 0.',
                        );
                    }
                    return undefined;
                },
                async onChange(_rowId, rowData: ui.PartialCollectionValue<SalesInvoiceLine>) {
                    if (!rowData.grossPrice) rowData.grossPrice = '0';
                    await this.calculatePrices(rowData);
                    this.setPriceOriginAndReason(rowData);
                    await this.updateGrossProfit(rowData);
                    this.lines.addOrUpdateRecordValue(rowData);
                },
            }),
            ui.nestedFields.numeric({
                title: 'Discount',
                bind: 'discount',
                postfix: '%',
                scale: 2,
                isHiddenOnMainField: true,
                isDisabled() {
                    return ['posted', 'inProgress', 'error'].includes(this.status.value || '');
                },
                validation(val) {
                    if (val < 0) {
                        return ui.localize(
                            '@sage/xtrem-sales/pages__sales_invoice__percentage_is_negative',
                            'The percentage cannot be negative.',
                        );
                    }
                    if (val > 100) {
                        return ui.localize(
                            '@sage/xtrem-sales/pages__sales_invoice__percentage_greater_than_100',
                            'The percentage cannot exceed 100.',
                        );
                    }
                    return undefined;
                },
                async onChange(_rowId, rowData: ui.PartialCollectionValue<SalesInvoiceLine>) {
                    if (!rowData.discount) rowData.discount = '0';
                    await this.calculatePrices(rowData);
                    this.setPriceOriginAndReason(rowData);
                    await this.updateGrossProfit(rowData);
                },
            }),
            ui.nestedFields.numeric({
                bind: 'charge',
                title: 'Charge',
                postfix: '%',
                scale: 2,
                isHiddenOnMainField: true,
                isDisabled() {
                    return ['posted', 'inProgress', 'error'].includes(this.status.value || '');
                },
                validation(val) {
                    if (val < 0) {
                        return ui.localize(
                            '@sage/xtrem-sales/pages__sales_invoice__percentage_is_negative',
                            'The percentage cannot be negative.',
                        );
                    }
                    if (val > 100) {
                        return ui.localize(
                            '@sage/xtrem-sales/pages__sales_invoice__percentage_greater_than_100',
                            'The percentage cannot exceed 100.',
                        );
                    }
                    return undefined;
                },
                async onChange(rowId, rowData: ui.PartialCollectionValue<SalesInvoiceLine>) {
                    if (!rowData.charge) rowData.charge = '0';
                    await this.calculatePrices(rowData);
                    this.setPriceOriginAndReason(rowData);
                    await this.updateGrossProfit(rowData);
                },
            }),
            ui.nestedFields.numeric({
                title: 'Net price',
                bind: 'netPrice',
                isReadOnly: true,
                unit() {
                    return this.currency.value;
                },
            }),
            ui.nestedFields.label<SalesInvoice, SalesInvoiceLine>({
                title: 'Price origin',
                bind: 'priceOrigin',
                optionType: '@sage/xtrem-sales/SalesPriceOrigin',
                isHiddenOnMainField: true,
            }),
            ui.nestedFields.reference<SalesInvoice, SalesInvoiceLineBinding, CustomerPriceReason>({
                title: 'Price reason',
                bind: 'priceReason',
                node: '@sage/xtrem-master-data/CustomerPriceReason',
                valueField: 'name',
                isHiddenOnMainField: true,
                isReadOnly: true,
                columns: [
                    ui.nestedFields.text({ bind: 'name' }),
                    ui.nestedFields.numeric({ bind: 'priority', title: 'Priority' }),
                    ui.nestedFields.technical({ bind: 'isActive' }),
                    ui.nestedFields.technical({ bind: 'description' }),
                ],
            }),
            ui.nestedFields.numeric({
                title: 'Total excluding tax',
                bind: 'amountExcludingTax',
                isReadOnly: true,
                unit() {
                    return this.currency.value;
                },
                scale: null,
            }),
            ui.nestedFields.numeric({
                title: 'Total excluding tax company currency',
                bind: 'amountExcludingTaxInCompanyCurrency',
                unit() {
                    return this.salesSiteCurrency;
                },
                scale: null,
                isReadOnly: true,
                isExcludedFromMainField: true,
                isHidden() {
                    return isExchangeRateHidden(this.currency.value, this.site.value, this.billToCustomer.value);
                },
            }),
            ui.nestedFields.numeric({
                title: 'Total including tax company currency',
                bind: 'amountIncludingTaxInCompanyCurrency',
                scale: null,
                unit() {
                    return this.salesSiteCurrency;
                },
                isReadOnly: true,
                isExcludedFromMainField: true,
                isHidden() {
                    return isExchangeRateHidden(this.currency.value, this.site.value, this.billToCustomer.value);
                },
            }),
            ui.nestedFields.numeric({
                title: 'Total tax',
                bind: 'taxAmount',
                isReadOnly: true,
                isHidden() {
                    return (
                        this.taxCalculationStatus.value === 'failed' && this.taxEngine.value === 'genericTaxCalculation'
                    );
                },
                unit() {
                    return this.currency.value;
                },
                scale: null,
            }),
            ui.nestedFields.technical({ bind: 'taxAmountAdjusted' }),
            ui.nestedFields.numeric({
                title: 'Total including tax',
                bind: 'amountIncludingTax',
                isReadOnly: true,
                unit() {
                    return this.currency.value;
                },
                scale: null,
            }),
            ui.nestedFields.numeric({
                isReadOnly: true,
                title: 'Stock cost amount',
                bind: 'stockCostAmount',
                unit() {
                    return this.currency.value;
                },
                scale: null,
            }),
            ui.nestedFields.numeric({
                isReadOnly: true,
                title: 'Gross profit amount',
                bind: 'grossProfitAmount',
                unit() {
                    return this.currency.value;
                },
                scale: null,
            }),
            ui.nestedFields.reference<SalesInvoice, SalesInvoiceLineBinding, Site>({
                title: 'Provider site',
                lookupDialogTitle: 'Select provider site',
                bind: 'providerSite',
                tunnelPage: '@sage/xtrem-master-data/Site',
                minLookupCharacters: 0,
                isAutoSelectEnabled: true,
                isHiddenOnMainField: true,
                isReadOnly: (_value, rowValue) => rowValue?.origin !== 'direct',
                isDisabled() {
                    return ['posted', 'inProgress', 'error'].includes(this.status.value || '');
                },
                columns: [
                    ui.nestedFields.text({ bind: 'name' }),
                    ui.nestedFields.text({ bind: 'id' }),
                    ui.nestedFields.reference<SalesInvoice, Site, Company>({
                        node: '@sage/xtrem-system/Company',
                        tunnelPage: '@sage/xtrem-master-data/Company',
                        title: 'Company',
                        bind: 'legalCompany',
                        valueField: 'name',
                        helperTextField: 'id',
                        columns: [ui.nestedFields.text({ bind: 'name' }), ui.nestedFields.text({ bind: 'id' })],
                    }),
                    ui.nestedFields.technical({ bind: 'description' }),
                ],
                async onChange(_rowId, rowData: ui.PartialCollectionValue<SalesInvoiceLine>) {
                    await this.priceDetermination2(
                        rowData,
                        ui.localize(
                            '@sage/xtrem-sales/pages__sales_invoice__on_price_determination_confirmation_provider_site',
                            'The provider site has been updated. Do you want to recalculate prices, discounts and charges?',
                        ),
                    );
                },
            }),
            ui.nestedFields.reference<SalesInvoice, SalesInvoiceLineBinding, Site>({
                bind: 'site',
                node: '@sage/xtrem-system/Site',
                valueField: 'name',
                helperTextField: 'id',
                isHidden: true,
                columns: [
                    ui.nestedFields.text({ bind: 'name' }),
                    ui.nestedFields.text({ bind: 'id' }),
                    ui.nestedFields.reference<SalesInvoice, Site, Company>({
                        node: '@sage/xtrem-system/Company',
                        bind: 'legalCompany',
                        valueField: 'name',
                        helperTextField: 'id',
                        columns: [
                            ui.nestedFields.text({ bind: 'name' }),
                            ui.nestedFields.text({ bind: 'id' }),
                            ui.nestedFields.technical<SalesInvoice, Company, Legislation>({
                                node: '@sage/xtrem-structure/Legislation',
                                bind: 'legislation',
                                nestedFields: [
                                    ui.nestedFields.technical({ bind: 'name' }),
                                    ui.nestedFields.technical({ bind: 'id' }),
                                ],
                            }),
                        ],
                    }),
                    ui.nestedFields.technical({ bind: 'description' }),
                ],
            }),
            ui.nestedFields.reference<SalesInvoice, SalesInvoiceLineBinding, UnitOfMeasure>({
                title: 'Stock unit',
                bind: 'stockUnit',
                node: '@sage/xtrem-master-data/UnitOfMeasure',
                tunnelPage: '@sage/xtrem-master-data/UnitOfMeasure',
                valueField: 'symbol',
                helperTextField: 'name',
                isMandatory: true,
                isReadOnly: true,
                isExcludedFromMainField: true,
                columns: [
                    ui.nestedFields.text({ bind: 'name' }),
                    ui.nestedFields.technical({ bind: 'id' }),
                    ui.nestedFields.text({ bind: 'symbol', title: 'Symbol' }),
                    ui.nestedFields.technical({ bind: 'decimalDigits' }),
                ],
                isHidden: (_value, rowData) => rowData?.item?.type === 'service',
            }),
            ui.nestedFields.numeric({
                isTitleHidden: true,
                title: 'Gross price',
                bind: 'grossPriceDeterminated',
                isExcludedFromMainField: true,
                unit() {
                    return this.currency.value;
                },
                isDisabled() {
                    return ['posted', 'inProgress', 'error'].includes(this.status.value || '');
                },
            }),
            ui.nestedFields.technical({ bind: 'discountDeterminated' }),
            ui.nestedFields.technical({ bind: 'chargeDeterminated' }),
            ui.nestedFields.technical<SalesInvoice, SalesInvoiceLine>({ bind: 'priceOriginDeterminated' }),
            ui.nestedFields.technical<SalesInvoice, SalesInvoiceLine, CustomerPriceReason>({
                bind: 'priceReasonDeterminated',
                node: '@sage/xtrem-master-data/CustomerPriceReason',
                nestedFields: [
                    ui.nestedFields.technical({ bind: 'name' }),
                    ui.nestedFields.technical({ bind: 'priority' }),
                    ui.nestedFields.technical({ bind: 'description' }),
                    ui.nestedFields.technical({ bind: 'isActive' }),
                ],
            }),
            ui.nestedFields.technical({ bind: 'isPriceDeterminated' }),
            ui.nestedFields.reference<SalesInvoice, SalesInvoiceLineBinding, BusinessEntityAddress>({
                title: 'Consumption address',
                bind: 'consumptionLinkedAddress',
                node: '@sage/xtrem-master-data/BusinessEntityAddress',
                valueField: 'name',
                isMandatory: true,
                isReadOnly: true,
                isExcludedFromMainField: true,
                columns: [
                    ui.nestedFields.checkbox({ bind: 'isActive', title: 'Active' }),
                    ui.nestedFields.text({ bind: 'name' }),
                    ui.nestedFields.text({ bind: 'addressLine1', title: 'Address line 1' }),
                    ui.nestedFields.text({ bind: 'addressLine2', title: 'Address line 2' }),
                    ui.nestedFields.text({ bind: 'city', title: 'City' }),
                    ui.nestedFields.text({ bind: 'region', title: 'Region' }),
                    ui.nestedFields.text({ bind: 'postcode', title: 'ZIP code' }),
                    ui.nestedFields.reference<SalesInvoice, BusinessEntityAddress, Country>({
                        bind: 'country',
                        columns: [ui.nestedFields.text({ bind: 'name' }), ui.nestedFields.text({ bind: 'id' })],
                    }),
                    ui.nestedFields.text({ bind: 'locationPhoneNumber', title: 'Phone number' }),
                    ui.nestedFields.technical<SalesInvoice, BusinessEntityAddress, BusinessEntity>({
                        bind: 'businessEntity',
                        node: '@sage/xtrem-master-data/BusinessEntity',
                        nestedFields: [
                            ui.nestedFields.technical({ bind: 'name' }),
                            ui.nestedFields.technical({ bind: 'id' }),
                        ],
                    }),
                ],
                filter() {
                    return this.billToCustomer.value
                        ? { businessEntity: { id: this.billToCustomer.value?.businessEntity?._id } }
                        : {};
                },
            }),
            ui.nestedFields.technical({ bind: 'storedAttributes' }),
            ui.nestedFields.technical({ bind: 'storedDimensions' }),
            ui.nestedFields.label({
                title: 'Credit status',
                bind: 'creditStatus',
                isExcludedFromMainField: true,
                optionType: '@sage/xtrem-sales/SalesDocumentCreditStatus',
                style: (_id, rowData) =>
                    PillColorSales.getLabelColorByStatus('SalesDocumentCreditStatus', rowData?.creditStatus),
                width: 'large',
            }),
            ui.nestedFields.technical<SalesInvoice, SalesInvoiceLine, Address>({
                bind: 'consumptionAddress',
                node: '@sage/xtrem-master-data/Address',
                nestedFields: [
                    ui.nestedFields.technical({ bind: 'name' }),
                    ui.nestedFields.technical({ bind: 'locationPhoneNumber' }),
                    ui.nestedFields.technical({ bind: 'addressLine1' }),
                    ui.nestedFields.technical({ bind: 'addressLine2' }),
                    ui.nestedFields.technical({ bind: 'city' }),
                    ui.nestedFields.technical({ bind: 'region' }),
                    ui.nestedFields.technical({ bind: 'postcode' }),
                    ui.nestedFields.technical({
                        bind: 'country',
                        node: '@sage/xtrem-structure/Country',
                        nestedFields: [
                            ui.nestedFields.technical({ bind: '_id' }),
                            ui.nestedFields.technical({ bind: 'name' }),
                            ui.nestedFields.technical({ bind: 'id' }),
                            ui.nestedFields.technical({ bind: 'regionLabel' }),
                            ui.nestedFields.technical({ bind: 'zipLabel' }),
                        ],
                    }),
                    ui.nestedFields.technical({ bind: 'concatenatedAddress' }),
                ],
            }),
            ui.nestedFields.numeric({
                title: 'Quantity in progress',
                bind: 'quantityCreditedInProgressInSalesUnit',
                isReadOnly: true,
                isExcludedFromMainField: true,
                scale: (_value, rowData) => rowData?.unit?.decimalDigits || 0,
                postfix: (_value, rowData) => rowData?.unit?.symbol,
            }),
            ui.nestedFields.numeric({
                title: 'Credited quantity',
                bind: 'quantityCreditedPostedInSalesUnit',
                isReadOnly: true,
                isExcludedFromMainField: true,
                scale: (_value, rowData) => rowData?.unit?.decimalDigits || 0,
                postfix: (_value, rowData) => rowData?.unit?.symbol,
            }),
            ui.nestedFields.technical({ bind: 'uiTaxes' }),
            ui.nestedFields.text({
                title: 'Customer order reference',
                bind: 'customerNumber',
                isReadOnly: true,
                isHiddenOnMainField: true,
            }),
        ],
        inlineActions: [
            {
                icon: 'box_arrow_left',
                title: 'Open line panel',
                onClick(rowId: string) {
                    this.lines.openSidebar(rowId);
                },
            },
        ],
        dropdownActions: [
            {
                icon: 'refresh',
                title: 'Update price',
                isDisabled() {
                    return ['posted', 'inProgress', 'error'].includes(this.status.value || '');
                },
                isHidden: rowId => +rowId <= 0,
                async onClick(_rowId, rowItem: ui.PartialCollectionValue<SalesInvoiceLine>) {
                    if (Number(rowItem._id) > 0) {
                        await this.priceDetermination(rowItem);
                    }
                },
            },
            ui.menuSeparator(),
            {
                icon: 'none',
                title: 'Dimensions',
                async onClick(_rowId, rowItem: ui.PartialCollectionValue<SalesInvoiceLine>) {
                    const rowData = dimensionPanelHelpers.fromPartialToBaseDocumentLineWithAnalytical(rowItem);
                    await MasterDataUtils.applyPanelToLineIfChanged(
                        this.lines,
                        dimensionPanelHelpers.editDisplayDimensions(
                            this,
                            {
                                documentLine: rowData,
                            },
                            {
                                editable:
                                    !['posted', 'inProgress', 'error'].includes(this.status.value || '') ||
                                    this.fromNotificationHistory,
                            },
                        ),
                    );
                },
            },
            {
                icon: 'none',
                title: 'Tax details',
                isHidden: (_rowId, rowItem: ui.PartialCollectionValue<SalesInvoiceLine>) => !rowItem.uiTaxes,
                async onClick(_rowId, rowItem: ui.PartialCollectionValue<SalesInvoiceLine>) {
                    if (rowItem.uiTaxes) {
                        await this.callDisplayTaxes(rowItem);
                    }
                },
            },
            ui.menuSeparator(),
            {
                icon: 'bin',
                title: 'Delete',
                isDestructive: true,
                isHidden(_rowId, rowItem) {
                    return (
                        rowItem.origin === 'shipment' ||
                        ['posted', 'inProgress', 'error'].includes(this.status.value || '')
                    );
                },
                async onClick(rowId, rowItem) {
                    if (
                        await confirmDialogWithAcceptButtonText(
                            this,
                            ui.localize(
                                '@sage/xtrem-sales/pages__sales_invoice__line_delete_action_dialog_title',
                                'Confirm delete',
                            ),
                            ui.localize(
                                '@sage/xtrem-sales/pages__sales_invoice__line_delete_action_dialog_content',
                                'You are about to delete this sales invoice line. This action cannot be undone.',
                            ),
                            ui.localize('@sage/xtrem-sales/pages-confirm-continue', 'Continue'),
                        )
                    ) {
                        this.lines.removeRecord(rowId);
                        await TotalTaxCalculator.getInstance().updateTaxData(JSON.parse(rowItem.uiTaxes ?? '{}'), null);
                        await this.calculateTaxExcludedTotalAmount();
                        recalculateTaxCalculationStatus(this.lines, this.taxCalculationStatus);
                        this.disableSomeHeaderPropertiesIfLines();
                    }
                },
            },
        ],
        mobileCard: {
            image: ui.nestedFields.image({ bind: { item: { image: true } } }),
            title: ui.nestedFields.text({ bind: { item: { name: true } }, title: 'Product', isTitleHidden: true }),
            line2: ui.nestedFields.text({ bind: 'itemDescription', title: 'Description' }),
            line2Right: ui.nestedFields.numeric({
                bind: 'amountIncludingTax',
                title: 'Amount',
                canFilter: false,
                unit() {
                    return this.currency.value;
                },
                scale: null,
            }),
        },
        sidebar: {
            title(_id, recordValue) {
                if (recordValue && +recordValue < 0) {
                    return ui.localize('@sage/xtrem-sales/edit-create-line', 'Add new line');
                }
                return `${recordValue?.item.name} - ${recordValue?.item.id}`;
            },
            headerDropdownActions: [
                {
                    icon: 'refresh',
                    title: 'Update price',
                    isDisabled() {
                        return ['posted', 'inProgress', 'error'].includes(this.status.value || '');
                    },
                    isHidden: rowId => +rowId <= 0,
                    async onClick(_rowId, rowItem: ui.PartialCollectionValue<SalesInvoiceLine>) {
                        if (Number(rowItem._id) > 0) {
                            await this.priceDetermination(rowItem);
                        }
                    },
                },
                ui.menuSeparator(),
                {
                    icon: 'none',
                    title: 'Dimensions',
                    async onClick(_rowId, rowItem: ui.PartialCollectionValue<SalesInvoiceLine>) {
                        const rowData = dimensionPanelHelpers.fromPartialToBaseDocumentLineWithAnalytical(rowItem);
                        await MasterDataUtils.applyPanelToLineIfChanged(
                            this.lines,
                            dimensionPanelHelpers.editDisplayDimensions(
                                this,
                                { documentLine: rowData },
                                {
                                    editable:
                                        !['posted', 'inProgress', 'error'].includes(this.status.value || '') ||
                                        this.fromNotificationHistory,
                                },
                            ),
                        );
                    },
                },
                {
                    icon: 'none',
                    title: 'Tax details',
                    isHidden: (_rowId, rowItem: ui.PartialCollectionValue<SalesInvoiceLine>) => !rowItem.uiTaxes,
                    async onClick(_rowId, rowItem: ui.PartialCollectionValue<SalesInvoiceLine>) {
                        if (rowItem.uiTaxes) {
                            await this.callDisplayTaxes(rowItem);
                        }
                    },
                },
                ui.menuSeparator(),
                {
                    icon: 'bin',
                    title: 'Delete',
                    isDestructive: true,
                    isHidden(_rowId, rowItem) {
                        return (
                            rowItem.origin === 'shipment' ||
                            ['posted', 'inProgress', 'error'].includes(this.status.value || '')
                        );
                    },
                    onClick(id) {
                        this.lines.removeRecord(id);
                    },
                },
            ],
            async onRecordOpened(_id, recordValue) {
                this.currentSelectedLineId = recordValue?._id ?? '';
                this.internalNoteLine.value = recordValue?.internalNote?.value ?? '';
                this.externalNoteLine.value = recordValue?.externalNote?.value ?? '';
                this.isExternalNoteLine.value = recordValue?.isExternalNote ?? null;
                this.externalNoteLine.isDisabled = !this.isExternalNoteLine.value || this.status.value === 'posted';
                this.consumptionAddress.value ??= recordValue?.consumptionAddress ?? null;
                const recordValueId = recordValue?._id ?? '';
                if (+recordValueId > 0) {
                    await this.fillSalesLinkedDocuments(recordValue?._id ?? '');
                    await this.fillSalesCreditMemoLines(recordValue?._id);
                    TotalTaxCalculator.getInstance().synchronizeState(
                        JSON.parse(
                            String(JSON.stringify(TotalTaxCalculator.getInstance().getState())),
                        ) as TotalTaxCalculatorState,
                    );
                } else if (recordValue?.site) {
                    recordValue.site = this.site.value as unknown as Site;
                    this.lines.addOrUpdateRecordValue(recordValue as unknown as ExtractEdgesPartial<SalesInvoiceLine>);
                }
            },
            async onRecordConfirmed(_id, recordValue) {
                if (recordValue) {
                    recordValue.internalNote.value = this.internalNoteLine.value ? this.internalNoteLine.value : '';
                    recordValue.externalNote.value = this.externalNoteLine.value ? this.externalNoteLine.value : '';
                    recordValue.isExternalNote = this.isExternalNoteLine.value || false;
                    recordValue.consumptionAddress = this.consumptionAddress.value as Address;

                    this.lines.addOrUpdateRecordValue(recordValue as unknown as ExtractEdgesPartial<SalesInvoiceLine>);
                    await TotalTaxCalculator.getInstance().updateTaxDetails(
                        this.taxDetails,
                        this.totalTaxAmountAdjusted,
                        this.totalTaxAmount,
                    );
                    await this.calculateTaxExcludedTotalAmount();
                    recalculateTaxCalculationStatus(this.lines, this.taxCalculationStatus);
                }
            },
            layout() {
                return {
                    information: {
                        title: ui.localize('@sage/xtrem-sales/pages_sidebar_tab_title_information', 'Information'),
                        blocks: {
                            mainBlock: {
                                fields: ['item', 'origin', 'itemDescription', 'providerSite'],
                            },
                            sales: {
                                title: ui.localize('@sage/xtrem-sales/pages_sidebar_block_title_sales', 'Sales'),
                                fields: ['unit', 'quantity'],
                            },
                            stock: {
                                isHidden(_rowId, rowItem) {
                                    return !rowItem?.item.isStockManaged || rowItem.item.type === 'service';
                                },
                                title: ui.localize('@sage/xtrem-sales/pages_sidebar_block_title_stock', 'Stock'),
                                fields: ['stockUnit', 'quantityInStockUnit', 'unitToStockUnitConversionFactor'],
                            },
                        },
                    },
                    price: {
                        title: ui.localize('@sage/xtrem-sales/pages_sidebar_tab_title_price', 'Price'),
                        blocks: {
                            mainBlock: {
                                fields: ['grossPrice', 'discount', 'charge', 'netPrice'],
                            },
                            mainBlock1: {
                                fields: ['priceOrigin', 'priceReason'],
                            },
                            totals: {
                                title: ui.localize('@sage/xtrem-sales/pages_sidebar_block_title_totals', 'Totals'),
                                fields: ['amountExcludingTax', 'taxAmount', 'amountIncludingTax'],
                            },
                            totals2: {
                                fields: [
                                    'stockCostAmount',
                                    'grossProfitAmount',
                                    'amountExcludingTaxInCompanyCurrency',
                                    'amountIncludingTaxInCompanyCurrency',
                                ],
                            },
                        },
                    },
                    address: {
                        title: ui.localize('@sage/xtrem-sales/pages_sidebar_tab_title_address', 'Address'),
                        blocks: {
                            mainBlock: {
                                fields: [this.consumptionAddress],
                            },
                        },
                    },
                    origin: {
                        title: ui.localize('@sage/xtrem-sales/pages_sidebar_tab_title_origin', 'Origin'),
                        isHidden() {
                            return this.salesLinkedDocuments.value.length === 0;
                        },
                        blocks: {
                            mainBlock: {
                                fields: [this.salesLinkedDocuments],
                            },
                        },
                    },
                    progress: {
                        title: ui.localize('@sage/xtrem-sales/pages_sidebar_tab_title_progress', 'Progress'),
                        isHidden() {
                            return !['posted', 'inProgress'].includes(this.status.value || '');
                        },
                        blocks: {
                            mainBlock: {
                                fields: [
                                    'creditStatus',
                                    'quantityCreditedInProgressInSalesUnit',
                                    'quantityCreditedPostedInSalesUnit',
                                    this.salesCreditMemoLines,
                                ],
                            },
                        },
                    },
                    notes: {
                        title: ui.localize('@sage/xtrem-sales/pages_sidebar_tab_title_line_notes', 'Line notes'),
                        blocks: {
                            notesBlock: {
                                fields: [this.internalNoteLine, this.isExternalNoteLine, this.externalNoteLine],
                            },
                        },
                    },
                };
            },
        },
    })
    lines: ui.fields.Table<SalesInvoiceLineBinding>;

    @ui.decorators.pageAction<SalesInvoice>({
        title: 'Record receipt',
        isHidden: true,
        async onClick() {
            if (this._id.value) {
                const receiptNumber = await openRecordPage(this);
                if (typeof receiptNumber === 'string') {
                    this.$.showToast(
                        ui.localize(
                            '@sage/xtrem-sales/pages__sales_invoice__receipt_created',
                            'The following receipt was created: {{receiptNumber}}.',
                            { receiptNumber },
                        ),
                        { timeout: 10000 },
                    );
                }
                await this.$.router.refresh();
                await this.$.refreshNavigationPanel();
            }
        },
    })
    recordReceipt: ui.PageAction;

    @ui.decorators.pageAction<SalesInvoice>({
        title: 'Create credit memo',
        isHidden: true,
        async onClick() {
            if (this._id.value) {
                await actionFunctions.createCreditAction({
                    salesInvoicePage: this,
                    recordId: this._id.value,
                    isCalledFromRecordPage: true,
                });
            }
        },
    })
    createSalesCreditMemosFromInvoices: ui.PageAction;

    @ui.decorators.pageAction<SalesInvoice>({
        title: 'Save',
        buttonType: 'primary',
        access: {
            bind: '$update',
        },
        async onClick() {
            const isDialog = this.$.isInDialog;
            await this.$standardSaveAction.execute(true);
            if (isDialog) return;
            if (this.lines.value.length !== 0 && this._id.value) {
                const financeIntegrationCheckResult = await this.$.graph
                    .node('@sage/xtrem-sales/SalesInvoice')
                    .mutations.financeIntegrationCheck(
                        {
                            wasSuccessful: true,
                            message: true,
                        },
                        { invoice: this._id.value },
                    )
                    .execute();

                if (!financeIntegrationCheckResult.wasSuccessful) {
                    this.$.showToast(
                        `**${ui.localize(
                            '@sage/xtrem-sales/pages__sales_invoice__save_warnings',
                            'Warnings while saving:',
                        )}**\n${financeIntegrationCheckResult.message}`,
                        { type: 'warning', timeout: 20000 },
                    );
                }
            }
        },
    })
    save: ui.PageAction;

    @ui.decorators.pageAction<SalesInvoice>({
        title: 'Post',
        isHidden: true,
        async onClick() {
            if (this.taxCalculationStatus.value === 'failed') {
                showTaxCalcFiledPostBlockingMessage(this);
                return;
            }
            if (this._id.value && this.taxCalculationStatus.value && this.status.value) {
                await actionFunctions.postAction({
                    salesInvoicePage: this,
                    recordId: this._id.value,
                    isCalledFromRecordPage: true,
                    taxCalculationStatus: this.taxCalculationStatus.value,
                    status: this.status.value,
                });
            }
        },
    })
    post: ui.PageAction;

    @ui.decorators.pageAction<SalesInvoice>({
        title: 'Repost',
        isHidden: true,
        async onClick() {
            await actionFunctions.postDetails(this, this.postingDetails.value);

            const documentLines = this.lines.value
                .filter((line: PartialCollectionValueWithIds<SalesInvoiceLineBinding>) => Number(line._id) > 0)
                .map((line: PartialCollectionValueWithIds<SalesInvoiceLineBinding>) => {
                    const lineData = {
                        baseDocumentLineSysId: line._id,
                        storedAttributes: line.storedAttributes,
                        storedDimensions: line.storedDimensions,
                    };
                    return this.canEditTaxDataAfterPost && this.wereTaxesEdited
                        ? { ...lineData, uiTaxes: line.uiTaxes }
                        : { ...lineData };
                });

            this.$.loader.isHidden = false;

            const repostArgs: {
                salesInvoice: string;
                documentData: {
                    header?: {
                        paymentTerm?: integer | string;
                    };
                    lines?: {
                        baseDocumentLineSysId?: integer | string;
                        storedAttributes?: string;
                        storedDimensions?: string;
                        uiTaxes?: any;
                    }[];
                };
            } = { salesInvoice: this._id.value || '', documentData: { lines: documentLines } };

            if (this.canEditPaymentDataAfterPost) {
                repostArgs.documentData = {
                    ...repostArgs.documentData,
                    header: { paymentTerm: this.paymentTerm?.value?._id ?? 0 },
                };
            }

            const postResult = await this.$.graph
                .node('@sage/xtrem-sales/SalesInvoice')
                .mutations.repost(
                    {
                        wasSuccessful: true,
                        message: true,
                    },
                    { ...repostArgs },
                )
                .execute();

            this.$.loader.isHidden = true;
            if (!postResult.wasSuccessful) {
                this.$.showToast(
                    `**${ui.localize(
                        '@sage/xtrem-sales/pages__sales_invoice__repost_errors',
                        'Errors occurred while reposting:',
                    )}**\n${postResult.message}`,
                    { type: 'error', timeout: 20000 },
                );
            } else {
                this.$.showToast(postResult.message, { type: 'success' });
            }

            this.$.finish();
        },
    })
    repost: ui.PageAction;

    @ui.decorators.vitalPodField<SalesInvoice, Address>({
        node: '@sage/xtrem-master-data/Address',
        title: 'Consumption address',
        isFullWidth: true,
        isTransient: true,
        onAddButtonClick() {
            return {};
        },
        dropdownActions: [
            {
                icon: 'edit',
                title: 'Edit',
                onClick() {
                    this.consumptionAddress.isReadOnly = false;
                },

                isDisabled() {
                    const isStatusDraft = this.status.value === 'draft';
                    const currentLine = this.lines.getRecordValue?.(this.currentSelectedLineId);
                    const isOriginDirect = currentLine?.origin === 'direct';
                    return !(isStatusDraft && isOriginDirect);
                },
            },
            {
                icon: 'tick',
                title: 'Read-only',
                onClick() {
                    this.consumptionAddress.isReadOnly = true;
                    if (this.consumptionAddress.value) {
                        this.consumptionAddress.value.concatenatedAddress = getConcatenatedAddress(
                            this.consumptionAddress.value,
                        );
                    }
                },
                isDisabled() {
                    return this.status.value !== 'draft';
                },
            },
        ],
        isReadOnly: true,
        columns: [
            ui.nestedFields.text({
                bind: 'name',
                title: 'Name',
                isHidden() {
                    return this.consumptionAddress.isReadOnly === true;
                },
            }),
            ui.nestedFields.textArea({
                title: 'Bill-to address',
                isTitleHidden: true,
                bind: 'concatenatedAddress',
                width: 'large',
                isHidden() {
                    return this.consumptionAddress.isReadOnly === false;
                },
            }),
            ui.nestedFields.text({ bind: 'locationPhoneNumber', title: 'Phone number' }),
            ui.nestedFields.text({
                bind: 'addressLine1',
                title: 'Address line 1',
                width: 'large',
                isHidden() {
                    return this.consumptionAddress.isReadOnly === true;
                },
            }),
            ui.nestedFields.text({
                bind: 'addressLine2',
                title: 'Address line 2',
                width: 'large',
                isHidden() {
                    return this.consumptionAddress.isReadOnly === true;
                },
            }),
            ui.nestedFields.text({
                bind: 'city',
                title: 'City',
                isHidden() {
                    return this.consumptionAddress.isReadOnly === true;
                },
            }),
            ui.nestedFields.text({
                bind: 'region',
                title() {
                    const rowItem = this.lines.getRecordValue(this.currentSelectedLineId);
                    return getCountryRegionTitle(rowItem?.consumptionAddress);
                },
                isHidden() {
                    return this.consumptionAddress.isReadOnly === true;
                },
            }),
            ui.nestedFields.text({
                bind: 'postcode',
                title() {
                    const rowItem = this.lines.getRecordValue(this.currentSelectedLineId);
                    return getCountryPostCodeTitle(rowItem?.consumptionAddress);
                },
                isHidden() {
                    return this.consumptionAddress.isReadOnly === true;
                },
            }),
            ui.nestedFields.reference({
                bind: 'country',

                columns: [
                    ui.nestedFields.text({ bind: 'name' }),
                    ui.nestedFields.text({ bind: 'id', title: 'ISO 3166-1 alpha-2 code' }),
                    ui.nestedFields.technical({ bind: 'regionLabel' }),
                    ui.nestedFields.technical({ bind: 'zipLabel' }),
                ],
                isHidden() {
                    return this.consumptionAddress.isReadOnly === true;
                },
            }),
        ],
    })
    consumptionAddress: ui.fields.VitalPod;

    @ui.decorators.tableField<SalesInvoice, SalesShipment & { documentType: string; customerNumber: string }>({
        title: 'Shipment',
        isTitleHidden: true,
        width: 'large',
        isTransient: true,
        node: '@sage/xtrem-sales/SalesShipment',
        canSelect: false,
        columns: [
            ui.nestedFields.link({
                title: 'Origin document number',
                bind: '_id',
                map(_fieldValue, rowData) {
                    return `${rowData.number}`;
                },
                page: '@sage/xtrem-sales/SalesShipment',
                queryParameters(_value, rowData) {
                    return { _id: rowData?._id ?? '' };
                },
            }),
            ui.nestedFields.text({
                title: 'Document type',
                bind: 'documentType',
                isDisabled: true,
            }),
            ui.nestedFields.label({
                title: 'Status',
                bind: 'displayStatus',
                optionType: '@sage/xtrem-sales/SalesShipmentDisplayStatus',
                style: (_id, rowData) => PillColorCommon.getDisplayStatusPillFeatures(rowData?.displayStatus),
            }),
            ui.nestedFields.text({
                title: 'Customer order reference',
                bind: 'customerNumber',
                isDisabled: true,
            }),
        ],
    })
    salesLinkedDocuments: ui.fields.Table<SalesShipment & { documentType: string; customerNumber: string }>;

    @ui.decorators.tableField<SalesInvoice, SalesCreditMemoLine>({
        title: 'Credit memo lines',
        isTitleHidden: true,
        width: 'large',
        isTransient: true,
        canSelect: false,
        node: '@sage/xtrem-sales/SalesCreditMemoLine',
        orderBy: { _sortValue: +1 },
        columns: [
            ui.nestedFields.link({
                title: 'Credit memo number',
                bind: '_id', // Dummy link to a string; value is set in the map, as we cannot use valueField like syntax in bind of link field type
                width: 'large',
                isTransient: true,
                map(_fieldValue, rowData) {
                    return `${rowData.document.number}`;
                },
                page: '@sage/xtrem-sales/SalesCreditMemo',
                queryParameters(_value, rowData) {
                    return {
                        _id: rowData?.document._id ?? '',
                    };
                },
            }),
            ui.nestedFields.technical<SalesInvoice, SalesCreditMemoLine, SalesCreditMemo>({
                bind: 'document',
                node: '@sage/xtrem-sales/SalesCreditMemo',
                nestedFields: [ui.nestedFields.text({ title: 'Number', bind: 'number', isHidden: false })],
            }),
            ui.nestedFields.technical<SalesInvoice, SalesCreditMemoLine, UnitOfMeasure>({
                bind: 'unit',
                node: '@sage/xtrem-master-data/UnitOfMeasure',
                nestedFields: [
                    ui.nestedFields.technical({ bind: 'name' }),
                    ui.nestedFields.technical({ bind: 'id' }),
                    ui.nestedFields.technical({ bind: 'symbol' }),
                    ui.nestedFields.technical({ bind: 'decimalDigits' }),
                ],
            }),
            ui.nestedFields.label({
                title: 'Credit memo status',
                bind: '_id', // Dummy link to a string; value is set in the map, as we cannot use valueField like syntax in bind of link field type
                map(_value, rowData) {
                    switch (rowData.document.status) {
                        case 'draft':
                            return ui.localize(
                                '@sage/xtrem-sales/enums__sales_credit_memo_status__draft',
                                rowData.document.status,
                            );
                        case 'posted':
                            return ui.localize(
                                '@sage/xtrem-sales/enums__sales_credit_memo_status__posted',
                                rowData.document.status,
                            );
                        case 'inProgress':
                            return ui.localize(
                                '@sage/xtrem-sales/enums__sales_credit_memo_status__inProgress',
                                rowData.document.status,
                            );
                        case 'error':
                            return ui.localize(
                                '@sage/xtrem-sales/enums__sales_credit_memo_status__error',
                                rowData.document.status,
                            );
                        default:
                            return '';
                    }
                },
                style: (_id, rowData) =>
                    PillColorSales.getLabelColorByStatus('SalesCreditMemoStatus', rowData?.document?.status),
            }),
            ui.nestedFields.numeric({
                title: 'Quantity credited',
                bind: 'quantity',
                isReadOnly: true,
                width: 'large',
                scale: (_value, rowData) => rowData?.unit?.decimalDigits || 0,
                postfix: (_value, rowData) => rowData?.unit?.symbol,
            }),
            ui.nestedFields.numeric({
                title: 'Amount credited',
                bind: 'netPrice',
                isReadOnly: true,
                width: 'large',
                unit() {
                    return this.currency.value;
                },
            }),
        ],
    })
    salesCreditMemoLines: ui.fields.Table<SalesCreditMemoLine>;

    @ui.decorators.richTextField<SalesInvoice>({
        isFullWidth: true,
        title: 'Internal line notes',
        capabilities: validCapabilities,
        isTransient: true,
        helperText: 'Notes display on internal documents.',
        isDisabled() {
            return this.status.value === 'posted';
        },
    })
    internalNoteLine: ui.fields.RichText;

    @ui.decorators.switchField<SalesInvoice>({
        title: 'Add notes to customer document',
        isFullWidth: true,
        isTransient: true,
        onChange() {
            this.externalNoteLine.isDisabled = !(this.isExternalNoteLine.value || false);
            this.externalNoteLine.value = '';
        },
        isDisabled() {
            return this.status.value === 'posted';
        },
    })
    isExternalNoteLine: ui.fields.Switch;

    @ui.decorators.richTextField<SalesInvoice>({
        isFullWidth: true,
        title: 'Customer line notes',
        capabilities: validCapabilities,
        isTransient: true,
        helperText: 'Notes display on customer documents.',
        isDisabled() {
            return !this.isExternalNoteLine.value || this.status.value === 'posted';
        },
    })
    externalNoteLine: ui.fields.RichText;

    @ui.decorators.section<SalesInvoice>({ title: 'Information' })
    informationSection: ui.containers.Section;

    @ui.decorators.block<SalesInvoice>({
        parent() {
            return this.informationSection;
        },
        width: 'large',
        title: 'Information',
        isTitleHidden: true,
    })
    informationBlock: ui.containers.Block;

    @ui.decorators.labelField<SalesInvoice>({
        parent() {
            return this.informationBlock;
        },
        title: 'Tax calculation status',
        bind: 'taxCalculationStatus',
        isHidden() {
            return !['notDone', 'inProgress'].includes(this.taxCalculationStatus.value || '');
        },
        optionType: '@sage/xtrem-master-data/TaxCalculationStatus',
        style() {
            return PillColorCommon.getLabelColorByStatus('TaxCalculationStatus', this.taxCalculationStatus.value);
        },
    })
    taxCalculationStatus: ui.fields.Label<TaxCalculationStatus>;

    @ui.decorators.checkboxField<SalesInvoice>({
        parent() {
            return this.informationBlock;
        },
        title: 'Printed',
        isDisabled: true,
    })
    isPrinted: ui.fields.Checkbox;

    @ui.decorators.checkboxField<SalesInvoice>({
        parent() {
            return this.informationBlock;
        },
        title: 'Sent',
        isDisabled: true,
    })
    isSent: ui.fields.Checkbox;

    @ui.decorators.textField<SalesInvoice>({
        parent() {
            return this.informationBlock;
        },
        title: 'Creation number',
        isReadOnly: true,
        isHidden() {
            return this.site.value?.legalCompany?.legislation?.id !== 'FR';
        },
    })
    creationNumber: ui.fields.Text;

    @ui.decorators.referenceField<SalesInvoice, Currency>({
        parent() {
            return this.informationBlock;
        },
        node: '@sage/xtrem-master-data/Currency',
        tunnelPage: '@sage/xtrem-master-data/Currency',
        title: 'Transaction currency',
        lookupDialogTitle: 'Select currency',
        valueField: 'name',
        helperTextField: 'id',
        minLookupCharacters: 1,
        isAutoSelectEnabled: true,
        isMandatory: true,
        fetchesDefaults: true,
        isReadOnly() {
            return !!this.$.recordId || this.lines.value?.length > 0;
        },
        columns: [
            ui.nestedFields.text({ bind: 'name' }),
            ui.nestedFields.text({ bind: 'id', title: 'ISO 4217 code' }),
            ui.nestedFields.text({ bind: 'symbol', title: 'Symbol' }),
            ui.nestedFields.technical({ bind: 'decimalDigits' }),
        ],
        onChange() {
            this.updatePrefixScaleOnCurrencyFields();
            this.manageDisplayButtonDefaultDimensionAction();
            this.manageDisplayLinePhantomRow();
            this.rateDescription.isHidden = isExchangeRateHidden(
                this.currency.value,
                this.site.value,
                this.billToCustomer.value,
            );
        },
    })
    currency: ui.fields.Reference<Currency>;

    @ui.decorators.textField<SalesInvoice>({
        parent() {
            return this.informationBlock;
        },
        title: 'Exchange rate',
        isReadOnly: true,
        isHidden() {
            return isExchangeRateHidden(this.currency.value, this.site.value, this.billToCustomer.value);
        },
    })
    rateDescription: ui.fields.Text;

    @ui.decorators.referenceField<SalesInvoice, Incoterm>({
        parent() {
            return this.informationBlock;
        },
        title: 'Incoterms® rule',
        lookupDialogTitle: 'Select incoterms',
        minLookupCharacters: 0,
        isAutoSelectEnabled: true,
    })
    incoterm: ui.fields.Reference<Incoterm>;

    @ui.decorators.vitalPodField<SalesInvoice, Address>({
        parent() {
            return this.informationSection;
        },
        node: '@sage/xtrem-master-data/Address',
        title: 'Bill-to address',
        width: 'small',
        onAddButtonClick() {
            if (this.billToLinkedAddress.value) {
                const { ...values } = { ...this.billToLinkedAddress.value };
                delete values.businessEntity;
                return { ...values, _id: null };
            }
            return {};
        },
        dropdownActions: [
            {
                icon: 'lookup',
                title: 'Replace',
                onClick() {
                    this.billToLinkedAddress.openDialog();
                },
                isDisabled() {
                    return ['posted', 'inProgress', 'error'].includes(this.status.value || '');
                },
            },
            {
                icon: 'edit',
                title: 'Edit',
                onClick() {
                    this.billToAddress.isReadOnly = false;
                },
                isDisabled() {
                    return (
                        ['posted', 'inProgress', 'error'].includes(this.status.value || '') ||
                        !this.billToAddress.isReadOnly
                    );
                },
            },
            {
                icon: 'tick',
                title: 'Read-only',
                onClick() {
                    this.billToAddress.isReadOnly = true;
                    if (this.billToAddress.value) {
                        this.billToAddress.value.concatenatedAddress = getConcatenatedAddress(this.billToAddress.value);
                    }
                },
                isDisabled() {
                    return (
                        ['posted', 'inProgress', 'error'].includes(this.status.value || '') ||
                        this.billToAddress.isReadOnly
                    );
                },
            },
        ],
        isReadOnly: true,
        columns: [
            ui.nestedFields.text({
                bind: 'name',
                title: 'Name',
                isHidden() {
                    return this.billToAddress.isReadOnly === true;
                },
            }),
            ui.nestedFields.textArea({
                bind: 'concatenatedAddress',
                title: 'Bill-to address',
                isTitleHidden: true,
                width: 'large',
                isHidden() {
                    return this.billToAddress.isReadOnly === false;
                },
            }),
            ui.nestedFields.text({ bind: 'locationPhoneNumber', title: 'Phone number' }),
            ui.nestedFields.text({
                bind: 'addressLine1',
                title: 'Address line 1',
                width: 'large',
                isHidden() {
                    return this.billToAddress.isReadOnly === true;
                },
            }),
            ui.nestedFields.text({
                bind: 'addressLine2',
                title: 'Address line 2',
                width: 'large',
                isHidden() {
                    return this.billToAddress.isReadOnly === true;
                },
            }),
            ui.nestedFields.text({
                bind: 'city',
                title: 'City',
                isHidden() {
                    return this.billToAddress.isReadOnly === true;
                },
            }),
            ui.nestedFields.text({
                bind: 'region',
                title() {
                    return getCountryRegionTitle(this.billToAddress.value);
                },
                isHidden() {
                    return this.billToAddress.isReadOnly === true;
                },
            }),
            ui.nestedFields.text({
                bind: 'postcode',
                title() {
                    return getCountryPostCodeTitle(this.billToAddress.value);
                },
                isHidden() {
                    return this.billToAddress.isReadOnly === true;
                },
            }),
            ui.nestedFields.reference({
                bind: 'country',

                columns: [
                    ui.nestedFields.text({ bind: 'name' }),
                    ui.nestedFields.text({ bind: 'id', title: 'ISO 3166-1 alpha-2 code' }),
                    ui.nestedFields.technical({ bind: 'regionLabel' }),
                    ui.nestedFields.technical({ bind: 'zipLabel' }),
                ],
                isHidden() {
                    return this.billToAddress.isReadOnly === true;
                },
            }),
        ],
    })
    billToAddress: ui.fields.VitalPod<Address>;

    @ui.decorators.section<SalesInvoice>({ title: 'Totals' })
    totalsSection: ui.containers.Section;

    @ui.decorators.block<SalesInvoice>({
        parent() {
            return this.totalsSection;
        },
        width: 'large',
        title: 'Calculated amounts',
        isTitleHidden: true,
    })
    totalsSectionTaxTotalsBlock: ui.containers.Block;

    @ui.decorators.numericField<SalesInvoice>({
        title: 'Excluding tax',
        parent() {
            return this.totalsSectionTaxTotalsBlock;
        },
        unit() {
            return this.currency.value;
        },
        scale: null,
        isReadOnly: true,
    })
    totalAmountExcludingTax: ui.fields.Numeric;

    @ui.decorators.numericField<SalesInvoice>({
        title: 'Excluding tax',
        parent() {
            return this.totalsSectionCompanyCurrencyDetailsBlock;
        },
        unit() {
            return this.salesSiteCurrency;
        },
        scale: null,
        isReadOnly: true,
    })
    totalAmountExcludingTaxInCompanyCurrency: ui.fields.Numeric;

    @ui.decorators.numericField<SalesInvoice>({
        title: 'Tax',
        parent() {
            return this.totalsSectionTaxTotalsBlock;
        },
        unit() {
            return this.currency.value;
        },
        scale: null,
        isReadOnly: true,
    })
    totalTaxAmount: ui.fields.Numeric;

    @ui.decorators.numericField<SalesInvoice>({
        title: 'Total tax adjusted',
        parent() {
            return this.totalsSectionTaxTotalsBlock;
        },
        unit() {
            return this.currency.value;
        },
        scale: null,
        isHidden: true,
    })
    totalTaxAmountAdjusted: ui.fields.Numeric;

    @ui.decorators.numericField<SalesInvoice>({
        title: 'Including tax',
        parent() {
            return this.totalsSectionTaxTotalsBlock;
        },
        unit() {
            return this.currency.value;
        },
        scale: null,
        isReadOnly: true,
    })
    totalAmountIncludingTax: ui.fields.Numeric;

    @ui.decorators.numericField<SalesInvoice>({
        title: 'Including tax',
        parent() {
            return this.totalsSectionCompanyCurrencyDetailsBlock;
        },
        unit() {
            return this.salesSiteCurrency;
        },
        scale: null,
        isReadOnly: true,
    })
    totalAmountIncludingTaxInCompanyCurrency: ui.fields.Numeric;

    @ui.decorators.numericField<SalesInvoice>({
        title: 'Gross profit',
        parent() {
            return this.totalsSectionTaxTotalsBlock;
        },
        unit() {
            return this.currency.value;
        },
        scale: null,
        isReadOnly: true,
    })
    totalGrossProfit: ui.fields.Numeric;

    @ui.decorators.block<SalesInvoice>({
        parent() {
            return this.totalsSection;
        },
        title: 'Summary by tax',
        width: 'large',
    })
    totalsSectionTaxDetailsBlock: ui.containers.Block;

    @ui.decorators.block<SalesInvoice>({
        parent() {
            return this.totalsSection;
        },
        title: 'Amounts company currency',
        width: 'large',
    })
    totalsSectionCompanyCurrencyDetailsBlock: ui.containers.Block;

    @ui.decorators.tableField<SalesInvoice, SalesInvoiceTax>({
        bind: 'taxes',
        title: 'Taxes',
        isTitleHidden: true,
        canSelect: false,
        isReadOnly: true,
        pageSize: 10,
        node: '@sage/xtrem-sales/SalesInvoiceTax',
        orderBy: { _sortValue: +1, tax: +1 },
        parent() {
            return this.totalsSectionTaxDetailsBlock;
        },
        columns: [
            ui.nestedFields.technical({ bind: '_sortValue' }),
            ui.nestedFields.text({ title: 'Category', bind: 'taxCategory' }),
            ui.nestedFields.text({ title: 'Tax', bind: 'tax' }),
            ui.nestedFields.numeric({
                title: 'Taxable base',
                unit() {
                    return this.currency.value;
                },
                bind: 'taxableAmount',
                scale: null,
            }),
            ui.nestedFields.numeric({ title: 'Rate', postfix: '%', scale: 2, bind: 'taxRate' }),
            ui.nestedFields.numeric({
                title: 'Amount',
                unit() {
                    return this.currency.value;
                },
                scale: null,
                bind: 'taxAmount',
            }),
            ui.nestedFields.technical({ bind: 'taxAmountAdjusted' }),
            ui.nestedFields.technical({ bind: 'isReverseCharge' }),
        ],
    })
    taxDetails: ui.fields.Table<SalesInvoiceTax>;

    // Tab for posting information (accounting integration)
    @ui.decorators.section<SalesInvoice>({
        title: 'Posting',
        isHidden() {
            return !this.postingDetails.value.length;
        },
    })
    postingSection: ui.containers.Section;

    @ui.decorators.block<SalesInvoice>({
        parent() {
            return this.postingSection;
        },
        title: 'Error details',
    })
    postingMessageBlock: ui.containers.Block;

    @ui.decorators.tableField<SalesInvoice, FinanceTransactionBinding>({
        title: 'Posting',
        isTitleHidden: true,
        canSelect: false,
        bind: 'postingDetails',
        canUserHideColumns: false,
        isHelperTextHidden: true,
        pageSize: 10,
        parent() {
            return this.postingSection;
        },
        columns: [
            ui.nestedFields.technical({ bind: '_id' }),
            ui.nestedFields.select({
                title: 'Document type',
                bind: 'targetDocumentType',
                optionType: '@sage/xtrem-finance-data/TargetDocumentType',
                isReadOnly: true,
            }),
            ui.nestedFields.link({
                title: 'Document number',
                bind: 'targetDocumentNumber',
                page(_value, rowData) {
                    switch (rowData?.targetDocumentType) {
                        case 'journalEntry':
                            return '@sage/xtrem-finance/JournalEntry';
                        case 'accountsReceivableInvoice':
                            return '@sage/xtrem-finance/AccountsReceivableInvoice';
                        default:
                            return '@sage/xtrem-finance/AccountsReceivableInvoice';
                    }
                },
                queryParameters(_value, rowData) {
                    return {
                        _id: rowData?.targetDocumentSysId ?? '',
                        number: rowData?.targetDocumentNumber ?? '',
                    };
                },
            }),
            ui.nestedFields.link({
                title: 'Accounting integration reference',
                bind: 'financeIntegrationAppRecordId',
                page: '',
                isHidden() {
                    return true;
                },
            }),
            ui.nestedFields.text({
                title: 'Accounting integration reference',
                bind: 'financeIntegrationAppRecordId',
                isHidden() {
                    return false;
                },
                isReadOnly: true,
            }),
            ui.nestedFields.label({
                title: 'Status',
                bind: 'postingStatus',
                optionType: '@sage/xtrem-finance-data/PostingStatus',
                style: (_id, rowData) =>
                    PillColorFinance.getLabelColorByStatus('FinanceDocumentPostingStatus', rowData?.postingStatus),
            }),
            ui.nestedFields.technical({ bind: 'message' }),
            ui.nestedFields.technical({ bind: 'financeIntegrationApp' }),
            ui.nestedFields.technical({ bind: 'financeIntegrationAppUrl' }),
            ui.nestedFields.technical({ bind: 'documentSysId' }),
        ],
        onRowClick(_id: string, rowData) {
            this.messages.value = rowData.message;
            this.postingMessageBlock.isHidden = this.messages.value === '';
        },
        fieldActions() {
            return [this.goToSysNotificationPage];
        },
    })
    postingDetails: ui.fields.Table<FinanceTransactionBinding>;

    @ui.decorators.textAreaField<SalesInvoice>({
        parent() {
            return this.postingMessageBlock;
        },
        isTransient: true,
        isReadOnly: true,
        isFullWidth: true,
        rows: 10,
    })
    messages: ui.fields.TextArea;

    @ui.decorators.pageAction<SalesInvoice>({
        icon: 'u_turn_right',
        title: 'Retry',
        isTitleHidden: true,
        isHidden: true,
        onClick() {
            return this.$.dialog.page(
                '@sage/xtrem-communication/SysNotificationHistory',
                {
                    recordFilter: JSON.stringify({
                        documentType: 'salesInvoice',
                        documentNumber: this.number.value,
                        _id: {
                            _nin: this.postingDetails.value
                                .filter(financeTransaction =>
                                    ['toBeGenerated', 'posted'].includes(financeTransaction.postingStatus ?? ''),
                                )
                                .map(financeTransaction => financeTransaction._id),
                        },
                    }),
                },
                { fullScreen: true, resolveOnCancel: true },
            );
        },
    })
    goToSysNotificationPage: ui.PageAction;

    @ui.decorators.section<SalesInvoice>({ title: 'Payments' })
    paymentsSection: ui.containers.Section;

    @ui.decorators.block<SalesInvoice>({
        parent() {
            return this.paymentsSection;
        },
        width: 'extra-large',
        title: 'Payments',
        isTitleHidden: true,
    })
    paymentsBlock: ui.containers.Block;

    @ui.decorators.referenceField<SalesInvoice, PaymentTerm>({
        parent() {
            return this.paymentsBlock;
        },
        lookupDialogTitle: 'Select payment term',
        minLookupCharacters: 0,
        isAutoSelectEnabled: true,
        isMandatory: true,
        columns: [
            ui.nestedFields.text({ bind: 'name' }),
            ui.nestedFields.text({ bind: 'description' }),
            ui.nestedFields.technical({ bind: 'dueDateType' }),
            ui.nestedFields.technical({ bind: 'days' }),
        ],
        filter: { businessEntityType: { _in: ['customer', 'all'] } },
        onChange() {
            this.dueDateDefault();
            this.manageDisplayButtonDefaultDimensionAction();
        },
    })
    paymentTerm: ui.fields.Reference<PaymentTerm>;

    @ui.decorators.dateField<SalesInvoice>({
        parent() {
            return this.paymentsBlock;
        },
        title: 'Due date',
        isMandatory: true,
        onChange() {
            this.manageDisplayButtonDefaultDimensionAction();
            this.manageDisplayLinePhantomRow();
        },
    })
    dueDate: ui.fields.Date;

    @ui.decorators.tableField<SalesInvoice, BaseOpenItem>({
        bind: { arOpenItems: true },
        isHidden: true,
        node: '@sage/xtrem-finance-data/BaseOpenItem',
        columns: [
            ui.nestedFields.technical({ bind: { _id: true } }),
            ui.nestedFields.technical({ bind: { documentNumber: true } }),
        ],
    })
    arOpenItems: ui.fields.Table<BaseOpenItem>;

    @ui.decorators.checkboxField<SalesInvoice>({ isHidden: true })
    isOpenItemPageOptionActive: ui.fields.Checkbox;

    @ui.decorators.linkField<SalesInvoice>({
        parent() {
            return this.paymentsBlock;
        },
        title: 'Open item',
        width: 'small',
        isTransient: true,
        async onClick() {
            await this.$.dialog.page(
                `@sage/xtrem-finance/AccountsReceivableOpenItem`,
                { _id: this.arOpenItems.value?.at(0)?._id ?? '', fromSales: true },
                { fullScreen: true, resolveOnCancel: true },
            );
        },
        map() {
            return this.arOpenItems.value?.at(0)?.documentNumber ?? '';
        },
        isHidden() {
            return this.status.value !== 'posted' || !this.isOpenItemPageOptionActive.value;
        },
        isDisabled() {
            return this.$.queryParameters.fromFinance?.toString() === 'true';
        },
    })
    documentNumberLink: ui.fields.Link;

    @ui.decorators.labelField<SalesInvoice>({
        bind: 'paymentStatus',
        isHidden: true,
        optionType: '@sage/xtrem-finance-data/OpenItemStatus',
    })
    paymentStatus: ui.fields.Label;

    @ui.decorators.numericField<SalesInvoice>({
        title: 'Total amount paid',
        parent() {
            return this.paymentsBlock;
        },
        unit() {
            return this.currency.value;
        },
        scale: null,
        isReadOnly: true,
        width: 'small',
        isHidden() {
            return this.status.value !== 'posted' || !this.$.isServiceOptionEnabled('paymentTrackingOption');
        },
    })
    totalAmountPaid: ui.fields.Numeric;

    @ui.decorators.numericField<SalesInvoice>({
        title: 'Forced amount paid',
        parent() {
            return this.paymentsBlock;
        },
        unit() {
            return this.currency.value;
        },
        isReadOnly: true,
        width: 'small',
        isHidden() {
            return (
                this.status.value !== 'posted' ||
                !this.$.isServiceOptionEnabled('paymentTrackingOption') ||
                !this.forcedAmountPaid.value ||
                this.forcedAmountPaid.value === 0
            );
        },
    })
    forcedAmountPaid: ui.fields.Numeric;

    @ui.decorators.block<SalesInvoice>({
        parent() {
            return this.paymentsSection;
        },
        width: 'extra-large',
    })
    receiptsBlock: ui.containers.Block;

    @ui.decorators.fragmentFields<SalesInvoice>({
        isTitleHidden: true,
        parent() {
            return this.receiptsBlock;
        },
        fragment: '@sage/xtrem-sales/SalesReceiptLine',
    })
    receipts: ui.containers.FragmentFields;

    @ui.decorators.section<SalesInvoice>({ title: 'Notes', isTitleHidden: true })
    notesSection: ui.containers.Section;

    @ui.decorators.block<SalesInvoice>({
        title: 'Notes',
        isTitleHidden: true,
        parent() {
            return this.notesSection;
        },
    })
    noteBlock: ui.containers.Block;

    @ui.decorators.richTextField<SalesInvoice>({
        parent() {
            return this.noteBlock;
        },
        isFullWidth: true,
        title: 'Internal notes',
        capabilities: validCapabilities,
        helperText: 'Notes display on internal documents.',
        isDisabled() {
            return this.status.value === 'posted';
        },
    })
    internalNote: ui.fields.RichText;

    @ui.decorators.switchField<SalesInvoice>({
        parent() {
            return this.noteBlock;
        },
        title: 'Add notes to customer document',
        onChange() {
            this.externalNote.isDisabled = !this.isExternalNote.value;
            this.externalNote.value = '';
        },
        isDisabled() {
            return this.status.value === 'posted';
        },
        width: 'large',
    })
    isExternalNote: ui.fields.Switch;

    @ui.decorators.richTextField<SalesInvoice>({
        parent() {
            return this.noteBlock;
        },
        isFullWidth: true,
        title: 'Customer notes',
        capabilities: validCapabilities,
        helperText: 'Notes display on customer documents.',
        isDisabled() {
            return !this.isExternalNote.value || this.status.value === 'posted';
        },
    })
    externalNote: ui.fields.RichText;

    @ui.decorators.switchField<SalesInvoice>({
        parent() {
            return this.noteBlock;
        },
        title: 'Repeat the document notes on new documents.',
        isDisabled() {
            return this.status.value === 'posted';
        },
        width: 'large',
    })
    isTransferHeaderNote: ui.fields.Switch;

    @ui.decorators.switchField<SalesInvoice>({
        parent() {
            return this.noteBlock;
        },
        title: 'Repeat all the line notes on new documents.',
        isDisabled() {
            return this.status.value === 'posted';
        },
        width: 'large',
    })
    isTransferLineNote: ui.fields.Switch;

    // Send invoice by email start here
    @ui.decorators.pageAction<SalesInvoice>({
        isTransient: true,
        isHidden: true,
        title: 'Send',
        icon: 'email',
        async onClick() {
            this.sendEmailSection.isHidden = false;
            await loadContacts(this, this.billToLinkedAddress?.value?._id || '', this.billToContact.value);
            await this.$.dialog.custom('info', this.sendEmailSection, {
                cancelButton: { isHidden: true },
                acceptButton: { isHidden: true },
                resolveOnCancel: true,
            });
            this.sendEmailSection.isHidden = true;
            this.$.setPageClean();
        },
    })
    sendEmail: ui.PageAction;

    @ui.decorators.section<SalesInvoice>({ isHidden: true, title: 'Send sales invoice' })
    sendEmailSection: ui.containers.Section;

    @ui.decorators.block<SalesInvoice>({
        parent() {
            return this.sendEmailSection;
        },
        title: 'To',
    })
    sendEmailBlock: ui.containers.Block;

    @ui.decorators.referenceField<SalesInvoice, Contact>({
        parent() {
            return this.contactSelectionBlock;
        },
        isHidden: true,
        node: '@sage/xtrem-master-data/Contact',
        valueField: 'email',
        columns: [
            ui.nestedFields.text({ title: 'Last name', bind: 'lastName' }),
            ui.nestedFields.text({ title: 'First name', bind: 'firstName' }),
            ui.nestedFields.text({ title: 'Title', bind: 'title' }),
            ui.nestedFields.technical({ bind: 'email' }),
            ui.nestedFields.technical({ bind: 'preferredName' }),
            ui.nestedFields.technical({ bind: 'role' }),
            ui.nestedFields.technical({ bind: 'position' }),
            ui.nestedFields.technical({ bind: 'locationPhoneNumber' }),
        ],
        title: 'Bill-to customer address contact detail',
        isTitleHidden: true,
        lookupDialogTitle: 'Select bill-to address contact detail',
        minLookupCharacters: 0,
    })
    billToContact: ui.fields.Reference<Contact>;

    @ui.decorators.dropdownListField<SalesInvoice>({
        parent() {
            return this.sendEmailBlock;
        },
        title: 'Title',
        optionType: '@sage/xtrem-master-data/Title',
        isTransient: true,
        isFullWidth: true,
    })
    emailTitle: ui.fields.DropdownList;

    @ui.decorators.textField<SalesInvoice>({
        parent() {
            return this.sendEmailBlock;
        },
        title: 'First name',
        maxLength: 30,
        isTransient: true,
        isFullWidth: true,
    })
    emailFirstName: ui.fields.Text;

    @ui.decorators.textField<SalesInvoice>({
        parent() {
            return this.sendEmailBlock;
        },
        title: 'Last name',
        maxLength: 30,
        isMandatory: true,
        isTransient: true,
        isFullWidth: true,
    })
    emailLastName: ui.fields.Text;

    @ui.decorators.textField<SalesInvoice>({
        parent() {
            return this.sendEmailBlock;
        },
        title: 'Email',
        helperText: 'A sales invoice will be sent to this address.',
        isTransient: true,
        isFullWidth: true,
        isMandatory: true,
        validation(val) {
            if (val.length > 0 && !validEmail(val)) {
                return ui.localize(
                    '@sage/xtrem-sales/pages__sales_invoice__invalid-email',
                    'Email address incorrect: {{email}}',
                    {
                        email: val,
                    },
                );
            }
            return undefined;
        },
    })
    emailAddress: ui.fields.Text;

    @ui.decorators.buttonField<SalesInvoice>({
        isTransient: true,
        width: 'small',
        parent() {
            return this.sendEmailBlock;
        },
        map() {
            return ui.localize(
                '@sage/xtrem-sales/pages__sales_invoice__select_bill_to_contact_button_text',
                'Select bill-to customer contact',
            );
        },
        onError() {
            // Intentionally left empty
        },
        async onClick() {
            this.contactSelectionSection.isHidden = false;
            await this.$.dialog
                .custom('info', this.contactSelectionSection, { resolveOnCancel: true })
                .then(() => {
                    if (this.selectedContact.value) assignEmailContactFrom(this, this.selectedContact.value);
                })
                .finally(() => {
                    this.selectedContact.value = null;
                    this.contactSelectionSection.isHidden = false;
                });
        },
        title: 'Select bill-to customer contact',
        isTitleHidden: true,
    })
    selectBillToContact: ui.fields.Button;

    @ui.decorators.buttonField<SalesInvoice>({
        isTransient: true,
        parent() {
            return this.sendEmailBlock;
        },
        map() {
            return ui.localize('@sage/xtrem-sales/pages__sales_invoice__send_invoice_button_text', 'Send');
        },
        onError(error) {
            this.$.loader.isHidden = true;
            this.$.showToast(
                ui.localize(
                    '@sage/xtrem-sales/pages__sales_invoice__email_exception',
                    'Could not send sales invoice email. ({{exception}})',
                    { exception: error.message },
                ),
                { type: 'error' },
            );
        },
        async onClick() {
            if (
                await confirmDialogWithAcceptButtonText(
                    this,
                    ui.localize(
                        '@sage/xtrem-sales/pages__sales_invoice__send_invoice_dialog_title',
                        'Sales invoice email sending confirmation',
                    ),
                    ui.localize(
                        '@sage/xtrem-sales/pages__sales_invoice__send_invoice_dialog_content',
                        'You are about to send the sales invoice email.',
                    ),
                    ui.localize('@sage/xtrem-sales/pages-confirm-send', 'Send'),
                )
            ) {
                await actionFunctions.sendSalesInvoiceAction({
                    salesInvoicePage: this,
                    invoice: {
                        _id: this._id.value ?? '',
                        isPrinted: !!this.isPrinted.value,
                        number: this.number.value ?? '',
                    },
                    arInvoiceId: this.postingDetails.value
                        .find(
                            financeTransaction =>
                                financeTransaction.financeIntegrationApp === 'intacct' &&
                                financeTransaction.targetDocumentType === 'accountsReceivableInvoice' &&
                                Number(financeTransaction.financeIntegrationAppRecordId) > 0,
                        )
                        ?.documentSysId?.toString(),
                    email: {
                        contactTitle: this.emailTitle.value ?? '',
                        contactFirstName: this.emailFirstName.value ?? '',
                        contactLastName: this.emailLastName.value ?? '',
                        contactEmail: this.emailAddress.value ?? '',
                    },
                });
                this.sendEmailSection.isHidden = true;
            }
        },
        title: 'Send invoice',
        isTitleHidden: true,
    })
    sendInvoiceButton: ui.fields.Button;

    @ui.decorators.section<SalesInvoice>({ isHidden: true, isTitleHidden: true })
    contactSelectionSection: ui.containers.Section;

    @ui.decorators.block<SalesInvoice>({
        parent() {
            return this.contactSelectionSection;
        },
        title: '',
        isTitleHidden: true,
    })
    contactSelectionBlock: ui.containers.Block;

    @ui.decorators.tableField<SalesInvoice, BusinessEntityContact>({
        parent() {
            return this.contactSelectionSection;
        },
        node: '@sage/xtrem-master-data/BusinessEntityContact',
        canSelect: false,
        title: 'Contacts',
        isTitleHidden: true,
        isTransient: true,
        pageSize: 10,
        orderBy: { lastName: +1, firstName: +1 },
        columns: [
            ui.nestedFields.dropdownList({
                title: 'Title',
                bind: 'title',
                optionType: '@sage/xtrem-master-data/enums/title',
            }),
            ui.nestedFields.text({ title: 'First name', bind: 'firstName' }),
            ui.nestedFields.text({ title: 'Last name', bind: 'lastName' }),
            ui.nestedFields.text({ title: 'Email', bind: 'email' }),
            ui.nestedFields.technical({ bind: 'preferredName' }),
            ui.nestedFields.technical({ bind: 'role' }),
            ui.nestedFields.technical({ bind: 'position' }),
            ui.nestedFields.technical({ bind: 'locationPhoneNumber' }),
        ],
        onRowClick(_rowId, rowData) {
            this.selectedContact.value = rowData as unknown as ExtractEdgesPartial<BusinessEntityContact>;
        },
    })
    contacts: ui.fields.Table<BusinessEntityContact>;

    @ui.decorators.referenceField<SalesInvoice, BusinessEntityContact>({
        parent() {
            return this.contactSelectionBlock;
        },
        isReadOnly: true,
        node: '@sage/xtrem-master-data/BusinessEntityContact',
        valueField: 'email',
        isTransient: true,
        isFullWidth: true,
        columns: [
            ui.nestedFields.text({ title: 'Last name', bind: 'lastName' }),
            ui.nestedFields.text({ title: 'First name', bind: 'firstName' }),
            ui.nestedFields.text({ title: 'Title', bind: 'title' }),
            ui.nestedFields.technical({ bind: 'email' }),
            ui.nestedFields.technical({ bind: 'preferredName' }),
            ui.nestedFields.technical({ bind: 'role' }),
            ui.nestedFields.technical({ bind: 'position' }),
            ui.nestedFields.technical({ bind: 'locationPhoneNumber' }),
        ],
        title: 'Selected contact',
        isTitleHidden: true,
        lookupDialogTitle: 'Select selected contact',
    })
    selectedContact: ui.fields.Reference<BusinessEntityContact>;

    // Send invoice by email end here

    async initPage() {
        this.billToAddress.isReadOnly = true;
        if (this.$.recordId) {
            if (['posted', 'inProgress', 'error'].includes(this.status.value || '')) {
                this.paymentTerm.isDisabled = !this.canEditPaymentDataAfterPost;
                this.incoterm.isDisabled = true;
                this.dueDate.isDisabled = true;
                this.site.isDisabled = true;
                this.billToCustomer.isDisabled = true;
                this.billToLinkedAddress.isDisabled = true;
                this.billToAddress.isDisabled = true;
                this.currency.isDisabled = true;
                this.date.isDisabled = true;
            } else {
                this.paymentTerm.isDisabled = false;
                this.incoterm.isDisabled = false;
                this.dueDate.isDisabled = false;
                this.date.isDisabled = false;
                this.billToLinkedAddress.isDisabled = false;
                this.billToAddress.isDisabled = false;
                this.disableSomeHeaderPropertiesIfLines();
            }
            this.updatePrefixScaleOnCurrencyFields();
            this.initLinesLength = Number(this.lines.value.length);
            this.documentNumberLink.value = this.arOpenItems.value?.at(0)?.documentNumber ?? '';
        } else {
            this.date.value = DateValue.today().toString();
            this.date.isReadOnly = false;
        }

        if (!this.status.value) {
            this.status.value = 'draft';
        }
        if (this.site.value && this.billToCustomer.value) {
            this._defaultDimensionsAttributes = await initDefaultDimensions({
                page: this,
                dimensionDefinitionLevel: 'salesDirect',
                site: this.site.value,
                customer: this.billToCustomer.value,
            });
        }
        this.postingMessageBlock.isHidden = true;
        if (this.postingDetails.value.length === 1) {
            this.messages.value = this.postingDetails.value[0].message ?? '';
            this.postingMessageBlock.isHidden = this.messages.value === '';
        }
    }

    initParams() {
        this.fromNotificationHistory = this.$.queryParameters.fromNotificationHistory?.toString() === 'true' || false;
        this.canEditPaymentDataAfterPost =
            this.fromNotificationHistory && (this.paymentStatus.value ?? 'notPaid') === 'notPaid';
        this.canEditTaxDataAfterPost = this.canEditPaymentDataAfterPost && this.taxEngine.value !== 'avalaraAvaTax';
    }

    private disableSomeHeaderPropertiesIfLines() {
        const isDisabled = this.lines.value.length > 0;
        this.site.isDisabled = isDisabled;
        this.billToCustomer.isDisabled = isDisabled;
        this.currency.isDisabled = isDisabled;
    }

    async calculateTaxExcludedTotalAmount(withTaxDetails = true) {
        if (withTaxDetails) {
            await TotalTaxCalculator.getInstance().updateTaxDetails(
                this.taxDetails,
                this.totalTaxAmountAdjusted,
                this.totalTaxAmount,
            );
        }
        this.totalAmountExcludingTax.value = this.lines.value.reduce((accumulator: number, agLine) => {
            if (
                !this.$.values.lines?.find(
                    (pageLine: any) => pageLine._id === agLine._id && pageLine._action === 'delete',
                )
            ) {
                return Number(accumulator + +(agLine.amountExcludingTax ?? ''));
            }
            return accumulator;
        }, 0);
        const totalTaxAmountAdjusted = this.totalTaxAmountAdjusted.value ?? 0;
        this.totalAmountIncludingTax.value = this.totalAmountExcludingTax.value + totalTaxAmountAdjusted;

        // Company currency amounts
        this.totalAmountExcludingTaxInCompanyCurrency.value = Decimal.roundAt(
            this.lines.value.reduce((amount: any, line) => amount + line.amountExcludingTaxInCompanyCurrency, 0),
            this.currency.value?.decimalDigits,
        );
        this.totalAmountIncludingTaxInCompanyCurrency.value = Decimal.roundAt(
            this.lines.value.reduce((amount: any, line) => amount + line.amountIncludingTaxInCompanyCurrency, 0),
            this.currency.value?.decimalDigits,
        );
    }

    updatePrefixScaleOnCurrencyFields() {
        this.totalAmountExcludingTax.unit = this.currency.value;
        this.totalAmountIncludingTax.unit = this.currency.value;
        this.totalTaxAmount.unit = this.currency.value;
        this.totalTaxAmountAdjusted.unit = this.currency.value;
    }

    async fetchDefaultsFromBillToCustomer() {
        await this.$.fetchDefaults([
            'billToLinkedAddress',
            'billToAddress',
            'billToContact',
            'currency',
            'paymentTerm',
            'internalNote',
        ]);
    }

    async fetchDefaultsFromBillToAddress() {
        await this.$.fetchDefaults(['billToAddress', 'billToContact']);
    }

    dueDateDefault() {
        this.dueDate.value = fetchDefaultsForDueDate({
            paymentTerm: this.paymentTerm.value,
            baseDate: this.date.value ? date.parse(this.date.value) : null,
            dueDateValue: this.dueDate.value,
        });
    }

    async invoicePosting() {
        this.$.loader.isHidden = false;

        await this.callPostMutation();
        this.$.loader.isHidden = true;
        this.$.showToast(
            ui.localize('@sage/xtrem-sales/pages__sales_invoice__posted', 'The sales invoice was posted.'),
            { type: 'success' },
        );
        await this.$.router.refresh(true);
    }

    callPostMutation() {
        return this.$.graph
            .node('@sage/xtrem-sales/SalesInvoice')
            .mutations.post({ status: true }, { invoice: this.$.recordId ?? '' })
            .execute();
    }

    async fillSalesLinkedDocuments(rowId: string) {
        const oldIsDirty = this.$.isDirty;
        this.salesLinkedDocuments.value.forEach(tableLine => {
            this.salesLinkedDocuments.removeRecord(tableLine._id);
        });
        const LinkDocument = await this.$.graph
            .node('@sage/xtrem-sales/SalesShipmentLineToSalesInvoiceLine')
            .query(
                ui.queryUtils.edgesSelector(
                    {
                        _id: true,
                        document: { _id: true },
                        linkedDocument: {
                            _id: true,
                            document: {
                                _id: true,
                                number: true,
                                status: true,
                                displayStatus: true,
                            },
                            customerNumber: true,
                        },
                    },
                    {
                        filter: { document: { _id: rowId } },
                    },
                ),
            )
            .execute();

        if (LinkDocument.edges.length) {
            this.salesLinkedDocuments.value = [
                {
                    ...LinkDocument.edges[0].node.linkedDocument.document,
                    documentType: ui.localizeEnumMember('@sage/xtrem-sales/SalesDocumentType', 'salesShipment'),
                    customerNumber: LinkDocument.edges[0].node.linkedDocument.customerNumber,
                } as ui.PartialNodeWithId<SalesShipment & { documentType: string }>,
            ];
        }
        this.salesLinkedDocuments.isHidden = this.salesLinkedDocuments.value.length === 0;
        if (!oldIsDirty && this.$.isDirty) {
            this.$.setPageClean();
        }
    }

    async fillSalesCreditMemoLines(rowId: any) {
        const oldIsDirty = this.$.isDirty;
        this.salesCreditMemoLines.value.forEach(tableLine => {
            this.salesCreditMemoLines.removeRecord(tableLine._id);
        });
        const result = await this.$.graph
            .node('@sage/xtrem-sales/SalesInvoiceLineToSalesCreditMemoLine')
            .query(
                ui.queryUtils.edgesSelector(
                    {
                        document: {
                            _id: true,
                            item: { _id: true, name: true },
                            quantity: true,
                            unit: { _id: true, name: true, decimalDigits: true, id: true, symbol: true },
                            netPrice: true,
                            document: { _id: true, number: true, status: true },
                        },
                    },
                    {
                        filter: { linkedDocument: { _id: rowId } },
                    },
                ),
            )
            .execute();
        this.salesCreditMemoLines.value = result.edges.map(e => e.node.document);
        if (!oldIsDirty && this.$.isDirty) {
            this.$.setPageClean();
        }
    }

    async callDisplayTaxes(rowItem: ui.PartialCollectionValue<SalesInvoiceLine>) {
        const result = await displayTaxes(this, rowItem, {
            currency: this.currency.value as ExtractEdgesPartial<Currency>,
            taxAmount: Number(rowItem.taxAmount),
            taxAmountAdjusted: Number(rowItem.taxAmountAdjusted),
            amountExcludingTax: Number(rowItem.amountExcludingTax),
            amountIncludingTax: Number(rowItem.amountIncludingTax),
            documentType: DocumentTypeEnum.salesInvoiceLine,
            taxDate: rowItem.taxDate ?? this.date.value ?? '',
            legislation: rowItem?.site?.legalCompany?.legislation,
            editable: !isPostedInProgressError(this.status.value) || this.canEditTaxDataAfterPost,
            destinationCountry: rowItem.consumptionAddress?.country as ExtractEdgesPartial<Country>,
            originCountry: this.site.value?.primaryAddress?.country as ExtractEdgesPartial<Country>,
            validTypes: ['sales', 'purchasingAndSales'],
        });

        if (result) {
            this.wereTaxesEdited = true;
            rowItem.amountIncludingTaxInCompanyCurrency = convertAmount(
                Number(rowItem.amountIncludingTax),
                this.companyFxRate.value ?? 1,
                this.companyFxRateDivisor.value ?? 1,
                this.currency.value?.decimalDigits ?? 2,
                this.salesSiteCurrency?.decimalDigits ?? 2,
            ).toString();
            this.lines.addOrUpdateRecordValue({
                _id: rowItem._id,
                amountIncludingTaxInCompanyCurrency: rowItem.amountIncludingTaxInCompanyCurrency,
            });
            await this.calculateTaxExcludedTotalAmount(false);
        }
    }

    async priceDetermination(rowItem: ui.PartialCollectionValue<SalesInvoiceLine>) {
        const response = await this.$.dialog
            .confirmation(
                'info',
                ui.localize('@sage/xtrem-sales/pages__sales_order__on_price_determination_title', 'Search price'),
                ui.localize(
                    '@sage/xtrem-sales/pages__sales_invoice__on_price_determination_confirmation',
                    'Do you want to recalculate prices, discounts and charges to reflect the latest price list?',
                ),
                {
                    cancelButton: { isHidden: false },
                    acceptButton: { isHidden: false },
                },
            )
            .then(() => true)
            .catch(() => false);
        if (response) {
            await this.getPriceDetermination(rowItem);
        }
    }

    async priceDetermination2(
        rowItem: ui.PartialCollectionValue<SalesInvoiceLine>,
        confirmationMessage: string,
    ): Promise<boolean> {
        const errorOnQuantityInSalesUnit = false;
        if (
            !errorOnQuantityInSalesUnit &&
            Number(rowItem.quantity) !== 0 &&
            rowItem.item !== null &&
            rowItem.providerSite !== null
        ) {
            if (!rowItem.isPriceDeterminated) {
                await this.getPriceDetermination(rowItem);
                rowItem.isPriceDeterminated = true;
                return true;
            }
            const response = await this.$.dialog
                .confirmation(
                    'info',
                    ui.localize('@sage/xtrem-sales/pages__sales_Invoice__on_price_determination_title', 'Search price'),
                    confirmationMessage,
                    {
                        cancelButton: { isHidden: false },
                        acceptButton: { isHidden: false },
                    },
                )
                .then(() => true)
                .catch(() => false);
            if (response) {
                await this.getPriceDetermination(rowItem);
                return true;
            }
        }
        return false;
    }

    async retrieveItemSites(rowData: ui.PartialCollectionValue<SalesInvoiceLine>) {
        rowData.item = {
            ...rowData.item,
            itemSites: extractEdges(
                await this.$.graph
                    .node('@sage/xtrem-master-data/Item')
                    .query(
                        ui.queryUtils.edgesSelector(
                            {
                                itemSites: {
                                    query: {
                                        edges: {
                                            node: {
                                                site: {
                                                    _id: true,
                                                },
                                            },
                                        },
                                    },
                                },
                            },
                            {
                                filter: {
                                    _id: rowData.item?._id,
                                },
                            },
                        ),
                    )
                    .execute(),
            ).pop()?.itemSites,
        };
    }

    async getPriceDetermination(rowItem: ui.PartialCollectionValue<SalesInvoiceLine>, isWithMessage?: boolean) {
        const prices = await getPrices(this.$.graph, {
            salesSiteId: rowItem.providerSite?._id ?? '',
            stockSiteId: rowItem.providerSite?._id ?? '',
            soldToCustomerId: this.billToCustomer.value?._id ?? '',
            currencyId: this.currency.value?._id ?? '',
            itemId: rowItem.item?._id ?? '',
            unitId: rowItem.unit?._id ?? '',
            quantity: Number(rowItem.quantity),
            date: new Date(this.date.value ?? ''),
        });

        rowItem.priceReason = prices.priceReason;
        rowItem.priceOrigin = prices.priceOrigin as SalesPriceOrigin;
        rowItem.grossPrice = prices.grossPrice.toString();
        rowItem.discount = prices.discount.toString();
        rowItem.charge = prices.charge.toString();
        rowItem.priceReasonDeterminated = prices.priceReason;
        rowItem.priceOriginDeterminated = prices.priceOrigin as SalesPriceOrigin;
        rowItem.grossPriceDeterminated = prices.grossPrice.toString();
        rowItem.discountDeterminated = prices.discount.toString();
        rowItem.chargeDeterminated = prices.charge.toString();

        await this.calculatePrices(rowItem);

        this.lines.addOrUpdateRecordValue(rowItem);
        await this.calculateTaxExcludedTotalAmount();
        recalculateTaxCalculationStatus(this.lines, this.taxCalculationStatus);
        this.disableSomeHeaderPropertiesIfLines();
        if (prices.error !== '' && isWithMessage) {
            this.$.showToast(
                ui.localize(
                    '@sage/xtrem-sales/page__sales_invoice__no_rate_found',
                    'The price could not been determined from the item sales base price. The currency rate between the sales base price currency and the document currency is missing.',
                ),
                { type: 'warning', timeout: 5000 },
            );
        }
    }

    async calculatePrices(rowItem: ui.PartialCollectionValue<SalesInvoiceLine>) {
        const prices = await calculateLinePrices({
            grossPrice: Number(rowItem.grossPrice),
            charge: Number(rowItem.charge),
            discount: Number(rowItem.discount),
            netPriceScale: this.currency.value?.decimalDigits ?? 2,
            quantity: Number(rowItem.quantity),
            amountExcludingTax: Number(rowItem.amountExcludingTax),
            amountIncludingTax: Number(rowItem.amountIncludingTax),
            taxAmount: Number(rowItem.taxAmount),
            taxAmountAdjusted: Number(rowItem.taxAmountAdjusted),
            rateMultiplication: this.companyFxRate.value ?? 1,
            rateDivision: this.companyFxRateDivisor.value ?? 1,
            fromDecimals: this.currency.value?.decimalDigits ?? 2,
            toDecimals: this.salesSiteCurrency?.decimalDigits ?? 2,
            taxes: {
                site: rowItem?.providerSite?._id,
                businessPartner: this.billToCustomer.value?._id,
                item: rowItem?.item?._id,
                currency: this.currency.value?._id,
                lineNodeName: '@sage/xtrem-sales/SalesInvoiceLine',
                taxEngine: this.taxEngine.value ?? '',
                uiTaxes: rowItem.uiTaxes ?? '',
                graphObject: this.$.graph,
                taxDate: rowItem.taxDate ?? this.date.value ?? '',
            },
        });
        rowItem.netPrice = prices.netPrice.toString();
        rowItem.amountExcludingTax = prices.amountExcludingTax.toString();
        rowItem.amountIncludingTax = prices.amountIncludingTax.toString();
        rowItem.taxAmount = prices.taxAmount.toString();
        rowItem.taxAmountAdjusted = prices.taxAmountAdjusted.toString();
        rowItem.uiTaxes = prices.uiTaxes.toString();
        rowItem.taxCalculationStatus = prices.taxCalculationStatus;
        rowItem.amountExcludingTaxInCompanyCurrency = prices.amountExcludingTaxInCompanyCurrency.toString();
        rowItem.amountIncludingTaxInCompanyCurrency = prices.amountIncludingTaxInCompanyCurrency.toString();
        this.lines.addOrUpdateRecordValue(rowItem);
    }

    async checkItemCustomer(rowData: ui.PartialCollectionValue<SalesInvoiceLine>, includeSalesUnit: boolean = false) {
        const result = extractEdges(
            await this.$.graph
                .node('@sage/xtrem-master-data/ItemCustomer')
                .query(
                    ui.queryUtils.edgesSelector<ItemCustomer>(
                        {
                            _id: true,
                            salesUnit: { _id: true, name: true, id: true, symbol: true, decimalDigits: true },
                            minimumSalesQuantity: true,
                            maximumSalesQuantity: true,
                            salesUnitToStockUnitConversion: true,
                        },
                        {
                            filter: { item: rowData.item?._id, customer: String(this.billToCustomer.value?._id) },
                        },
                    ),
                )
                .execute(),
        );
        const salesToStock = await this.$.graph
            .node('@sage/xtrem-master-data/UnitOfMeasure')
            .queries.convertFromTo(false, {
                fromUnit: rowData.item?.salesUnit?._id ?? '',
                toUnit: rowData.item?.stockUnit?._id ?? '',
                type: 'sales',
                item: rowData.item?._id,
                quantity: 1,
                formatToUnitDecimalDigits: false,
            })
            .execute();
        if (result.length) {
            const itemCustomer = result.shift();
            if (includeSalesUnit) {
                rowData.unit = itemCustomer?.salesUnit;
                rowData.unitToStockUnitConversionFactor = itemCustomer?.salesUnitToStockUnitConversion;
            }
        } else if (includeSalesUnit) {
            rowData.unit = rowData.item?.salesUnit;
            rowData.unitToStockUnitConversionFactor = String(salesToStock || 1);
        }
        this.lines.addOrUpdateRecordValue(rowData);
    }

    setPriceOriginAndReason(rowData: ui.PartialCollectionValue<SalesInvoiceLine>) {
        if (
            Number(rowData.grossPriceDeterminated) === Number(rowData.grossPrice) &&
            Number(rowData.discountDeterminated) === Number(rowData.discount) &&
            Number(rowData.chargeDeterminated) === Number(rowData.charge)
        ) {
            rowData.priceOrigin = rowData.priceOriginDeterminated;
            rowData.priceReason = rowData.priceReasonDeterminated;
        } else {
            rowData.priceOrigin = 'manual';
            rowData.priceReason = undefined;
        }
        this.lines.addOrUpdateRecordValue(rowData);
    }

    _setStepSequenceStatusObject(stepSequenceValues: SalesInvoiceStepSequenceStatus): Dict<ui.StepSequenceStatus> {
        const creditOrPayStepSequence = stepSequenceValues.credit
            ? { [this.orderStepSequenceCredit]: stepSequenceValues.credit }
            : { [this.orderStepSequencePay]: stepSequenceValues.pay };
        return {
            [this.orderStepSequenceCreate]: stepSequenceValues.create,
            [this.orderStepSequencePost]: stepSequenceValues.post,
            ...(creditOrPayStepSequence as Dict<ui.StepSequenceStatus>),
        };
    }

    async updateGrossProfit(rowData: ExtractEdgesPartial<SalesInvoiceLineBinding>) {
        const newCostInCompanyCurrency = await calculateStockCost(
            this,
            rowData.item?._id ?? '',
            rowData.site?._id ?? '',
            Number(rowData.quantityInStockUnit),
        );
        commonUpdateGrossProfit(
            this,
            {
                itemId: rowData.item?._id ?? '',
                siteId: rowData.site?._id ?? '',
                rowData: rowData as unknown as SalesInvoiceLineBinding,
                quantityInStockUnit: Number(rowData.quantityInStockUnit),
                currencyInfo: {
                    rateMultiplication: this.companyFxRateDivisor?.value ?? 0,
                    rateDivision: this.companyFxRate?.value ?? 0,
                    fromDecimals: this.salesSiteCurrency?.decimalDigits ?? 2,
                    toDecimals: this.currency?.value?.decimalDigits ?? 2,
                },
            },
            newCostInCompanyCurrency,
        );
    }
}
