import { featuresSales } from '@sage/xtrem-master-data/build/lib/menu-items/features-sales';
import type { GraphApi } from '@sage/xtrem-sales-api';
import {
    setApplicativePageCrudActions,
    setOrderOfPageHeaderDropDownActions,
} from '@sage/xtrem-system/build/lib/client-functions/applicative-crud-actions';
import * as ui from '@sage/xtrem-ui';

@ui.decorators.page<SalesReturnRequestReason>({
    menuItem: featuresSales,
    module: 'sales',
    title: 'Return request reason',
    objectTypeSingular: 'Return request reason',
    objectTypePlural: 'Return request reasons',
    idField() {
        return this.name;
    },
    node: '@sage/xtrem-sales/SalesReturnRequestReason',
    priority: 200,
    areNavigationTabsHidden: true,
    mode: 'tabs',
    navigationPanel: {
        listItem: {
            title: ui.nestedFields.text({ bind: 'name' }),
            line2: ui.nestedFields.text({ bind: 'description' }),
        },
        optionsMenu: [
            {
                title: 'All',
                graphQLFilter: {},
            },
            {
                title: 'Active',
                graphQLFilter: { isActive: { _eq: true } },
            },
            {
                title: 'Inactive',
                graphQLFilter: { isActive: { _eq: false } },
            },
        ],
    },
    createAction() {
        return this.$standardNewAction;
    },
    headerDropDownActions() {
        return setOrderOfPageHeaderDropDownActions({
            remove: [this.$standardDeleteAction],
            dropDownBusinessActions: [this.$standardOpenRecordHistoryAction],
        });
    },
    headerQuickActions() {
        return [this.$standardDuplicateAction];
    },
    businessActions() {
        return [this.$standardCancelAction, this.$standardSaveAction];
    },
    onLoad() {
        setApplicativePageCrudActions({
            page: this,
            isDirty: false,
            save: this.$standardSaveAction,
            cancel: this.$standardCancelAction,
            duplicate: this.$standardDuplicateAction,
            remove: this.$standardDeleteAction,
        });
    },

    onDirtyStateUpdated(isDirty: boolean) {
        setApplicativePageCrudActions({
            page: this,
            isDirty,
            save: this.$standardSaveAction,
            cancel: this.$standardCancelAction,
            duplicate: this.$standardDuplicateAction,
            remove: this.$standardDeleteAction,
        });
    },
})
export class SalesReturnRequestReason extends ui.Page<GraphApi> {
    @ui.decorators.section<SalesReturnRequestReason>({
        title: 'General',
        isTitleHidden: true,
    })
    mainSection: ui.containers.Section;

    @ui.decorators.block<SalesReturnRequestReason>({
        parent() {
            return this.mainSection;
        },
        width: 'large',
    })
    mainBlock: ui.containers.Block;

    @ui.decorators.numericField<SalesReturnRequestReason>({
        isHidden: true,
        title: 'ID',
        isTitleHidden: true,
    })
    _id: ui.fields.Numeric;

    @ui.decorators.switchField<SalesReturnRequestReason>({
        parent() {
            return this.mainBlock;
        },
        title: 'Active',
        fetchesDefaults: true,
    })
    isActive: ui.fields.Switch;

    @ui.decorators.separatorField<SalesReturnRequestReason>({
        parent() {
            return this.mainBlock;
        },
        isFullWidth: true,
        isInvisible: true,
    })
    isActiveSeparator: ui.fields.Separator;

    @ui.decorators.textField<SalesReturnRequestReason>({
        parent() {
            return this.mainBlock;
        },
        title: 'ID',
        isMandatory: true,
    })
    id: ui.fields.Text;

    @ui.decorators.textField<SalesReturnRequestReason>({
        parent() {
            return this.mainBlock;
        },
        title: 'Name',
        width: 'large',
        isMandatory: true,
    })
    name: ui.fields.Text;

    @ui.decorators.textField<SalesReturnRequestReason>({
        parent() {
            return this.mainBlock;
        },
        title: 'Description',
        width: 'large',
    })
    description: ui.fields.Text;
}
