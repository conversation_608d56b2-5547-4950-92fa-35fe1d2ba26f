import type { Dict, ExtractEdgesPartial, Logical } from '@sage/xtrem-client';
import { extractEdges } from '@sage/xtrem-client';
import { DateValue } from '@sage/xtrem-date-time';
import { confirmDialogWithAcceptButtonText } from '@sage/xtrem-distribution/build/lib/client-functions/common';
import * as attributesAndDimensions from '@sage/xtrem-finance-data/build/lib/client-functions/attributes-and-dimensions';
import * as dimensionPanelHelpers from '@sage/xtrem-finance-data/build/lib/client-functions/dimension-panel-helpers';
import type * as financeInterfaces from '@sage/xtrem-finance-data/build/lib/client-functions/interfaces/dimension';
import type {
    Address,
    BusinessEntity,
    BusinessEntityAddress,
    Currency,
    Customer,
    DeliveryDetail,
    DeliveryMode,
    Incoterm,
    Item,
    PaymentTerm,
    UnitOfMeasure,
} from '@sage/xtrem-master-data-api';
import {
    getCountryPostCodeTitle,
    getCountryRegionTitle,
} from '@sage/xtrem-master-data/build/lib/client-functions/country-fields-title';
import { validCapabilities } from '@sage/xtrem-master-data/build/lib/client-functions/fields-properties';
import * as PillColorCommon from '@sage/xtrem-master-data/build/lib/client-functions/pill-color';
import * as utils from '@sage/xtrem-master-data/build/lib/client-functions/utils';
import { sales } from '@sage/xtrem-master-data/build/lib/menu-items/sales';
import { getConcatenatedAddress } from '@sage/xtrem-master-data/build/lib/shared-functions/address-functions';
import type {
    GraphApi,
    SalesCreditMemo,
    SalesCreditMemoLine,
    SalesDocumentCreditStatus,
    SalesReturnReceipt,
    SalesReturnReceiptLine,
    SalesReturnRequestDisplayStatus,
    SalesReturnRequestLine,
    SalesReturnRequest as SalesReturnRequestNode,
    SalesReturnRequestReason,
    SalesShipment,
} from '@sage/xtrem-sales-api';
import type { Country } from '@sage/xtrem-structure-api';
import type { Company, Site, User } from '@sage/xtrem-system-api';
import {
    setDisplayOfCommonPageActions,
    setOrderOfPageBusinessActions,
    setOrderOfPageHeaderDropDownActions,
    setOrderOfPageTableFieldActions,
} from '@sage/xtrem-system/build/lib/client-functions/applicative-crud-actions';
import * as MainListActions from '@sage/xtrem-system/build/lib/client-functions/main-list-actions';
import { validEmail } from '@sage/xtrem-system/build/lib/shared-functions/email-validation';
import * as ui from '@sage/xtrem-ui';
import * as displayButtons from '../client-functions/display-buttons-sales-return-request';
import { getSiteAndCustomer } from '../client-functions/finance-integration';
import type { SalesReturnRequestStepSequenceStatus } from '../client-functions/interfaces/interfaces';
import {
    getLocalizeSalesReturnRequestLineCloseStatusMethodReturn,
    getLocalizeSalesReturnRequestLineOpenStatusMethodReturn,
} from '../client-functions/page-functions';
import * as PillColorSales from '../client-functions/pill-color';
import * as actionFunctions from '../client-functions/sales-return-request-actions-functions';

@ui.decorators.page<SalesReturnRequest, SalesReturnRequestNode>({
    title: 'Sales return request',
    objectTypeSingular: 'Sales return request',
    objectTypePlural: 'Sales return requests',
    idField() {
        return this.number;
    },
    menuItem: sales,
    module: 'sales',
    node: '@sage/xtrem-sales/SalesReturnRequest',
    hasAttachmentsSection: true,
    mode: 'tabs',
    priority: 350,
    headerLabel() {
        return this.displayStatus;
    },
    businessActions() {
        return setOrderOfPageBusinessActions({
            save: this.$standardSaveAction,
            cancel: this.$standardCancelAction,
            businessActions: [
                this.requestApproval,
                this.approve,
                this.reject,
                this.createSalesCreditMemosFromReturnRequests,
                this.confirm,
                this.close,
                this.open,
            ],
        });
    },
    headerDropDownActions() {
        return setOrderOfPageHeaderDropDownActions({
            remove: [this.$standardDeleteAction],
            dropDownBusinessActions: [
                this.$standardOpenRecordHistoryAction,
                this.$standardOpenCustomizationPageWizardAction,
                ui.menuSeparator(),
                this.defaultDimension,
            ],
        });
    },
    headerSection() {
        return this.headerSection;
    },
    onDirtyStateUpdated(isDirty: boolean) {
        this.returnRequestStepSequence.statuses = this.getDisplayStatusStepSequence();
        this._manageDisplayApplicativePageActions(isDirty);
    },
    async onLoad() {
        this.returnRequestStepSequence.statuses = this.getDisplayStatusStepSequence();
        await this.initPage();
        this._manageDisplayApplicativePageActions(false);
        this.$.setPageClean();
    },
    onError(error: string | (Error & { errors: Array<any> })) {
        return utils.formatError(this, error);
    },
    navigationPanel: {
        orderBy: { date: -1, number: -1 },
        listItem: {
            title: ui.nestedFields.link({
                bind: 'number',
                title: 'Number',
                isMandatory: true,
                page: '@sage/xtrem-sales/SalesReturnRequest',
                queryParameters(value, rowData) {
                    return {
                        _id: rowData?._id ?? '',
                    };
                },
            }),
            line2: ui.nestedFields.reference<SalesReturnRequest, SalesReturnRequestNode>({
                bind: 'soldToCustomer',
                title: 'Sold-to customer',
                node: '@sage/xtrem-master-data/Customer',
                tunnelPage: undefined,
                valueField: { businessEntity: { name: true } },
            }),
            soldToCustomerId: ui.nestedFields.text({
                bind: { soldToCustomer: { id: true } },
                title: 'Sold-to customer ID',
                isHiddenOnMainField: true,
            }),
            line2Right: ui.nestedFields.date({
                bind: 'date',
                title: 'Return request date',
                isMandatory: true,
            }),
            line_4: ui.nestedFields.reference<SalesReturnRequest, SalesReturnRequestNode>({
                bind: 'site',
                node: '@sage/xtrem-system/Site',
                tunnelPage: undefined,
                valueField: 'name',
                title: 'Site',
                columns: [ui.nestedFields.technical({ bind: 'isSalesReturnRequestApprovalManaged' })],
            }),
            line_5: ui.nestedFields.reference<SalesReturnRequest, SalesReturnRequestNode>({
                bind: 'stockSite',
                node: '@sage/xtrem-system/Site',
                tunnelPage: undefined,
                valueField: 'name',
                title: 'Stock site',
            }),
            line6: ui.nestedFields.reference<SalesReturnRequest, SalesReturnRequestNode>({
                bind: 'requester',
                title: 'Requester',
                node: '@sage/xtrem-system/User',
                tunnelPage: undefined,
                valueField: 'displayName',
            }),
            line7: ui.nestedFields.dropdownList({
                title: 'Return type',
                bind: 'returnType',
                optionType: '@sage/xtrem-sales/SalesReturnRequestReturnType',
            }),
            titleRight: ui.nestedFields.label({
                bind: 'displayStatus',
                title: 'Status',
                optionType: '@sage/xtrem-sales/SalesReturnRequestDisplayStatus',
                isMandatory: true,
                style: (_id, rowData) => PillColorCommon.getDisplayStatusPillFeatures(rowData?.displayStatus),
            }),
            line9: ui.nestedFields.reference<SalesReturnRequest, SalesReturnRequestNode>({
                bind: 'deliveryMode',
                node: '@sage/xtrem-master-data/DeliveryMode',
                tunnelPage: undefined,
                valueField: 'name',
                title: 'Delivery mode',
                isHiddenOnMainField: true,
            }),
            line10: ui.nestedFields.reference<SalesReturnRequest, SalesReturnRequestNode>({
                bind: 'shipToCustomer',
                node: '@sage/xtrem-master-data/Customer',
                tunnelPage: undefined,
                valueField: { businessEntity: { name: true } },
                title: 'Ship-to customer',
                isHiddenOnMainField: true,
            }),
            shipToCustomerId: ui.nestedFields.text({
                bind: { shipToCustomer: { id: true } },
                title: 'Ship-to customer ID',
                isHiddenOnMainField: true,
            }),
            line12: ui.nestedFields.reference<SalesReturnRequest, SalesReturnRequestNode>({
                bind: 'billToCustomer',
                title: 'Bill-to customer',
                node: '@sage/xtrem-master-data/Customer',
                tunnelPage: undefined,
                valueField: { businessEntity: { name: true } },
                isHiddenOnMainField: true,
            }),
            billToCustomerId: ui.nestedFields.text({
                bind: { billToCustomer: { id: true } },
                title: 'Bill-to customer ID',
                isHiddenOnMainField: true,
            }),
            line13: ui.nestedFields.label({
                title: 'Credit status',
                bind: 'creditStatus',
                optionType: '@sage/xtrem-sales/SalesDocumentCreditStatus',
                isHiddenOnMainField: true,
                style: (_id, rowData) =>
                    PillColorSales.getLabelColorByStatus('SalesDocumentCreditStatus', rowData.creditStatus),
            }),
            line14: ui.nestedFields.technical({ bind: 'status' }),
            line15: ui.nestedFields.technical({ bind: 'approvalStatus' }),
            isCreditingAllowed: ui.nestedFields.technical({ bind: 'isCreditingAllowed' }),
            receiptStatus: ui.nestedFields.technical({ bind: 'receiptStatus' }),
        },
        dropdownActions: [
            {
                title: 'Confirm',
                icon: 'none',
                refreshesMainList: 'list',
                async onClick(recordId: string, rowItem: ui.PartialNodeWithId<SalesReturnRequestNode>) {
                    if (rowItem.number && recordId) {
                        await actionFunctions.confirmSalesReturnRequest({
                            salesReturnRequestPage: this,
                            recordNumber: rowItem.number,
                        });
                    }
                },
                isHidden(recordId: string, rowItem: ui.PartialNodeWithId<SalesReturnRequestNode>) {
                    return (
                        displayButtons.isHiddenButtonConfirmAction({
                            parameters: {
                                status: rowItem.status,
                                approvalStatus: rowItem.approvalStatus,
                                isApprovalManaged: rowItem.site?.isSalesReturnRequestApprovalManaged,
                            },
                            recordId,
                            isDirty: false,
                        }) || false
                    );
                },
            },
            {
                title: 'Approve',
                icon: 'none',
                refreshesMainList: 'list',
                async onClick(recordId: string, rowItem: ui.PartialNodeWithId<SalesReturnRequestNode>) {
                    if (rowItem.number && recordId) {
                        await actionFunctions.approve({
                            salesReturnRequestPage: this,
                            recordId,
                        });
                    }
                },
                isHidden(recordId: string, rowItem: ui.PartialNodeWithId<SalesReturnRequestNode>) {
                    return (
                        displayButtons.isHiddenButtonApproveAction({
                            parameters: {
                                status: rowItem.status,
                                approvalStatus: rowItem.approvalStatus,
                            },
                            recordId,
                            isDirty: false,
                        }) || false
                    );
                },
            },
            {
                title: 'Reject',
                icon: 'none',
                refreshesMainList: 'list',
                async onClick(recordId: string, rowItem: ui.PartialNodeWithId<SalesReturnRequestNode>) {
                    if (rowItem.number && recordId) {
                        await actionFunctions.reject({
                            salesReturnRequestPage: this,
                            recordId,
                        });
                    }
                },
                isHidden(recordId: string, rowItem: ui.PartialNodeWithId<SalesReturnRequestNode>) {
                    return (
                        displayButtons.isHiddenButtonRejectAction({
                            parameters: {
                                status: rowItem.status,
                                approvalStatus: rowItem.approvalStatus,
                            },
                            recordId,
                            isDirty: false,
                        }) || false
                    );
                },
            },
            {
                title: 'Submit for approval',
                icon: 'none',
                refreshesMainList: 'list',
                async onClick(recordId: string, rowItem: ui.PartialNodeWithId<SalesReturnRequestNode>) {
                    if (recordId && rowItem.site?._id) {
                        this._id.value = recordId;
                        await this.submitForApproval(rowItem.site?._id);
                    }
                },
                isHidden(recordId, rowItem: ui.PartialNodeWithId<SalesReturnRequestNode>) {
                    return (
                        displayButtons.isHiddenButtonRequestApprovalAction({
                            parameters: {
                                status: rowItem.status,
                                approvalStatus: rowItem.approvalStatus,
                                isApprovalManaged: rowItem.site?.isSalesReturnRequestApprovalManaged,
                            },
                            recordId,
                            isDirty: false,
                        }) || false
                    );
                },
            },
            {
                title: 'Create credit memo',
                icon: 'none',
                refreshesMainList: 'list',
                async onClick(recordId) {
                    await actionFunctions.createCreditMemoAction({
                        salesReturnRequestPage: this,
                        recordId,
                        isCalledFromRecordPage: false,
                    });
                },
                isHidden(recordId, rowItem: ui.PartialNodeWithId<SalesReturnRequestNode>) {
                    return (
                        displayButtons.isHiddenButtonCreateSalesReturnRequestFromShipmentsAction({
                            parameters: {
                                status: rowItem.status,
                                isCreditingAllowed: rowItem.isCreditingAllowed,
                            },
                            recordId,
                            isDirty: false,
                        }) || false
                    );
                },
            },
            {
                title: 'Close request',
                icon: 'none',
                refreshesMainList: 'list',
                async onClick(_recordId: string, rowItem: ui.PartialNodeWithId<SalesReturnRequestNode>) {
                    if (rowItem.number) {
                        await actionFunctions.close({
                            isCalledFromRecordPage: false,
                            salesReturnRequestPage: this,
                            recordNumber: rowItem.number,
                        });
                    }
                },
                isHidden(recordId: string, rowItem: ui.PartialNodeWithId<SalesReturnRequestNode>) {
                    return (
                        displayButtons.isHiddenButtonCloseAction({
                            parameters: {
                                status: rowItem.status,
                            },
                            recordId,
                            isDirty: false,
                        }) || false
                    );
                },
            },
            {
                icon: 'none',
                title: 'Open request',
                refreshesMainList: 'list',
                async onClick(_recordId: string, rowItem: ui.PartialNodeWithId<SalesReturnRequestNode>) {
                    if (rowItem.number) {
                        await actionFunctions.openAction({
                            isCalledFromRecordPage: false,
                            salesReturnRequestPage: this,
                            recordNumber: rowItem.number,
                        });
                    }
                },
                isHidden(recordId: string, rowItem: ui.PartialNodeWithId<SalesReturnRequestNode>) {
                    return (
                        displayButtons.isHiddenButtonOpenAction({
                            parameters: {
                                status: rowItem.status,
                                receiptStatus: rowItem.receiptStatus,
                                creditStatus: rowItem.creditStatus,
                                approvalStatus: rowItem.approvalStatus,
                            },
                            recordId,
                            isDirty: false,
                        }) || false
                    );
                },
            },
            ui.menuSeparator(),
            {
                icon: 'none',
                title: 'Set dimensions',
                async onClick(_rowId, rowItem: ui.PartialNodeWithId<SalesReturnRequestNode>) {
                    if (rowItem.status !== 'closed') {
                        const { site, customer } = await getSiteAndCustomer({
                            page: this,
                            siteId: rowItem.site?._id ?? '',
                            customerId: rowItem.soldToCustomer?._id ?? '',
                        });
                        await actionFunctions.setDimensions({
                            salesReturnRequestPage: this,
                            recordNumber: rowItem.number ?? '',
                            status: rowItem.status ?? null,
                            site,
                            customer,
                        });
                    }
                },
                isHidden(recordId, rowItem: ui.PartialNodeWithId<SalesReturnRequestNode>) {
                    return displayButtons.isHiddenButtonDefaultDimensionAction({
                        parameters: { status: rowItem.status, receiptStatus: rowItem.receiptStatus ?? '' },
                        recordId,
                    });
                },
            },
            ui.menuSeparator(),
            {
                title: 'Delete',
                icon: 'bin',
                refreshesMainList: 'list',
                isDestructive: true,
                onError(error: string | (Error & { errors: Array<any> })) {
                    this.$.loader.isHidden = true;
                    return utils.formatError(this, error);
                },
                async onClick(_recordId: string, rowItem: ui.PartialNodeWithId<SalesReturnRequestNode>) {
                    await MainListActions.deleteRecord<GraphApi>(this, {
                        _id: rowItem._id,
                        nodeName: '@sage/xtrem-sales/SalesReturnRequest',
                    });
                },
                isHidden(recordId, rowItem: ui.PartialNodeWithId<SalesReturnRequestNode>) {
                    return displayButtons.isHiddenButtonDeleteAction({
                        parameters: { status: rowItem.status },
                        recordId,
                    });
                },
            },
        ],
        optionsMenu: [
            {
                title: 'All open statuses',
                graphQLFilter: { displayStatus: { _nin: ['closed', 'rejected', 'received'] } },
            },
            { title: 'All statuses', graphQLFilter: {} },
            { title: 'Draft', graphQLFilter: { displayStatus: { _eq: 'draft' } } },
            { title: 'Pending approval', graphQLFilter: { displayStatus: { _eq: 'pendingApproval' } } },
            { title: 'Approved', graphQLFilter: { displayStatus: { _eq: 'approved' } } },
            { title: 'Closed', graphQLFilter: { displayStatus: { _eq: 'closed' } } },
            { title: 'Rejected', graphQLFilter: { displayStatus: { _eq: 'rejected' } } },
            { title: 'Partially received', graphQLFilter: { displayStatus: { _eq: 'partiallyReceived' } } },
            { title: 'Received', graphQLFilter: { displayStatus: { _eq: 'received' } } },
            { title: 'Confirmed', graphQLFilter: { displayStatus: { _eq: 'confirmed' } } },
        ],
    },
})
export class SalesReturnRequest extends ui.Page<GraphApi> implements financeInterfaces.PageWithDefaultDimensions {
    _defaultDimensionsAttributes: financeInterfaces.DefaultDimensions;

    private readonly returnRequestStepSequenceCreate = ui.localize(
        '@sage/xtrem-sales/pages__sales_return_request__step_sequence_creation',
        'Create',
    );

    private readonly returnRequestStepSequenceApprove = ui.localize(
        '@sage/xtrem-sales/pages__sales_return_request__step_sequence_approve',
        'Approve',
    );

    private readonly returnRequestStepSequenceConfirm = ui.localize(
        '@sage/xtrem-sales/pages__sales_return_request__step_sequence_confirm',
        'Confirm',
    );

    private readonly returnRequestStepSequenceReceive = ui.localize(
        '@sage/xtrem-sales/pages__sales_return_request__step_sequence_receive',
        'Receive',
    );

    private readonly returnRequestStepSequenceCredit = ui.localize(
        '@sage/xtrem-sales/pages__sales_return_request__step_sequence_credit',
        'Credit',
    );

    getDisplayStatusStepSequence(): Dict<ui.StepSequenceStatus> {
        if (this.creditStatus.value === 'credited') {
            return this._setStepSequenceStatusObject({
                create: 'complete',
                approve: 'complete',
                confirm: 'complete',
                receive: 'complete',
                credit: 'complete',
            });
        }

        if (this.creditStatus.value === 'partiallyCredited') {
            if (this.receiptStatus.value === 'partiallyReceived') {
                return this._setStepSequenceStatusObject({
                    create: 'complete',
                    approve: 'complete',
                    confirm: 'complete',
                    receive: 'current',
                    credit: 'current',
                });
            }
            return this._setStepSequenceStatusObject({
                create: 'complete',
                approve: 'complete',
                confirm: 'complete',
                receive: 'complete',
                credit: 'current',
            });
        }

        if (['approved', 'rejected', 'confirmed'].includes(this.approvalStatus.value ?? '')) {
            if (this.receiptStatus.value === 'partiallyReceived') {
                return this._setStepSequenceStatusObject({
                    create: 'complete',
                    approve: 'complete',
                    confirm: 'complete',
                    receive: 'current',
                    credit: 'incomplete',
                });
            }

            if (this.receiptStatus.value === 'received') {
                return this._setStepSequenceStatusObject({
                    create: 'complete',
                    approve: 'complete',
                    confirm: 'complete',
                    receive: 'complete',
                    credit: 'incomplete',
                });
            }

            return this._setStepSequenceStatusObject({
                create: 'complete',
                approve: 'complete',
                confirm: 'complete',
                receive: 'incomplete',
                credit: 'incomplete',
            });
        }

        if (this.approvalStatus.value === 'pendingApproval') {
            return this._setStepSequenceStatusObject({
                create: 'complete',
                approve: 'current',
                confirm: 'current',
                receive: 'incomplete',
                credit: 'incomplete',
            });
        }

        return this._setStepSequenceStatusObject({
            create: 'current',
            approve: 'incomplete',
            confirm: 'incomplete',
            receive: 'incomplete',
            credit: 'incomplete',
        });
    }

    private _manageDisplayApplicativePageActions(isDirty = false) {
        setDisplayOfCommonPageActions({
            page: this,
            isDirty,
            save: this.$standardSaveAction,
            cancel: this.$standardCancelAction,
            remove: this.$standardDeleteAction,
            businessActions: [
                this.$standardOpenCustomizationPageWizardAction,
                this.requestApproval,
                this.approve,
                this.reject,
                this.createSalesCreditMemosFromReturnRequests,
                this.confirm,
                this.close,
                this.open,
            ],
        });
        this._manageDisplayAdditionalPageActions(isDirty);
    }

    private _manageDisplayAdditionalPageActions(isDirty = false) {
        this.manageDisplayButtonCRUDActions(isDirty);
        this.manageDisplayButtonAllOtherActions(isDirty);
    }

    private manageDisplayButtonCRUDActions(isDirty = false) {
        this.$standardSaveAction.isDisabled = displayButtons.isDisabledButtonSaveAction({
            parameters: { status: this.status.value },
            recordId: this.$.recordId,
            isDirty,
        });
        this.$standardCancelAction.isDisabled = displayButtons.isDisabledButtonCancelAction({
            parameters: { status: this.status.value },
            recordId: this.$.recordId,
            isDirty,
        });
        this.$standardDeleteAction.isHidden = displayButtons.isHiddenButtonDeleteAction({
            parameters: { status: this.status.value },
            recordId: this.$.recordId,
        });
    }

    private manageDisplayButtonAllOtherActions(isDirty = false) {
        // footer business actions
        this.manageDisplayButtonOpenAction(isDirty);
        this.manageDisplayButtonCloseAction(isDirty);
        this.manageDisplayButtonApproveAction(isDirty);
        this.manageDisplayButtonRejectAction(isDirty);
        this.manageDisplayButtonRequestApprovalAction(isDirty);
        this.manageDisplayButtonCreateSalesCreditMemosFromReturnRequestAction(isDirty);
        this.manageDisplayButtonConfirmAction(isDirty);
        // other header actions
        this.manageDisplayButtonDefaultDimensionAction();
        this.manageDisplayButtonAddSalesReturnRequestLineAction();
    }

    private manageDisplayButtonOpenAction(isDirty: boolean) {
        this.open.isHidden = displayButtons.isHiddenButtonOpenAction({
            parameters: {
                status: this.status.value,
                receiptStatus: this.receiptStatus.value,
                creditStatus: this.creditStatus.value,
                approvalStatus: this.approvalStatus.value,
            },
            recordId: this.$.recordId,
            isDirty,
        });
    }

    private manageDisplayButtonCloseAction(isDirty: boolean) {
        this.close.isHidden = displayButtons.isHiddenButtonCloseAction({
            parameters: { status: this.status.value },
            recordId: this.$.recordId,
            isDirty,
        });
    }

    manageDisplayButtonApproveAction(isDirty: boolean) {
        this.approve.isHidden = displayButtons.isHiddenButtonApproveAction({
            parameters: { status: this.status.value, approvalStatus: this.approvalStatus.value },
            recordId: this.$.recordId,
            isDirty,
        });
    }

    manageDisplayButtonConfirmAction(isDirty: boolean) {
        this.confirm.isHidden = displayButtons.isHiddenButtonConfirmAction({
            parameters: {
                status: this.status.value,
                approvalStatus: this.approvalStatus.value,
                isApprovalManaged: this.isApprovalManaged.value,
            },
            recordId: this.$.recordId,
            isDirty,
        });
    }

    manageDisplayButtonRejectAction(isDirty: boolean) {
        this.reject.isHidden = displayButtons.isHiddenButtonRejectAction({
            parameters: { status: this.status.value, approvalStatus: this.approvalStatus.value },
            recordId: this.$.recordId,
            isDirty,
        });
    }

    manageDisplayButtonRequestApprovalAction(isDirty: boolean) {
        this.requestApproval.isHidden = displayButtons.isHiddenButtonRequestApprovalAction({
            parameters: {
                status: this.status.value,
                approvalStatus: this.approvalStatus.value,
                isApprovalManaged: this.isApprovalManaged.value,
            },
            recordId: this.$.recordId,
            isDirty,
        });
    }

    manageDisplayButtonCreateSalesCreditMemosFromReturnRequestAction(isDirty: boolean) {
        this.createSalesCreditMemosFromReturnRequests.isHidden =
            displayButtons.isHiddenButtonCreateSalesReturnRequestFromShipmentsAction({
                parameters: {
                    status: this.status.value,
                    isCreditingAllowed: !!this.isCreditingAllowed.value,
                },
                recordId: this.$.recordId,
                isDirty,
            });
    }

    manageDisplayButtonAddSalesReturnRequestLineAction() {
        this.addSalesReturnRequestLine.isDisabled = displayButtons.isDisabledButtonAddSalesReturnRequestLineAction({
            parameters: {
                site: this.site.value,
                stockSite: this.stockSite.value,
                soldToCustomer: this.soldToCustomer.value,
            },
            recordId: this.$.recordId,
        });
    }

    private manageDisplayButtonDefaultDimensionAction() {
        this.defaultDimension.isHidden = displayButtons.isHiddenButtonDefaultDimensionAction({
            parameters: { status: this.status.value, receiptStatus: this.receiptStatus.value },
            recordId: this.$.recordId,
        });
        this.defaultDimension.isDisabled = displayButtons.isDisabledButtonDefaultDimensionAction({
            parameters: {
                site: this.site.value,
                stockSite: this.stockSite.value,
                soldToCustomer: this.soldToCustomer.value,
                date: this.date.value,
                requester: this.requester.value,
            },
        });
    }

    getSerializedValues() {
        const { values } = this.$;
        if (Number(values.billToAddress._id) < 0) {
            delete values.billToAddress._id;
        }
        if (Number(values.soldToAddress._id) < 0) {
            delete values.soldToAddress._id;
        }
        if (Number(values.shipToAddress._id) < 0) {
            delete values.shipToAddress._id;
        }
        this.getLineValues(values.lines);
        return values;
    }

    // TODO: todo when implementing addressesDetail on lines
    // eslint-disable-next-line class-methods-use-this
    getLineValues(lines: Partial<Dict<any>>[]) {
        // eslint-disable-next-line @typescript-eslint/no-unused-expressions
        lines;
        // &&
        //     lines.forEach(async line => {
        //         delete line.soldToAddress;
        //     });
    }

    @ui.decorators.pageAction<SalesReturnRequest>({
        title: 'Close request',
        isHidden: true,
        async onClick() {
            if (this.number.value) {
                await actionFunctions.close({
                    isCalledFromRecordPage: true,
                    salesReturnRequestPage: this,
                    recordNumber: this.number.value,
                });
            }
        },
    })
    close: ui.PageAction;

    @ui.decorators.pageAction<SalesReturnRequest>({
        title: 'Open request',
        isHidden: true,
        async onClick() {
            if (this.number.value) {
                await actionFunctions.openAction({
                    isCalledFromRecordPage: true,
                    salesReturnRequestPage: this,
                    recordNumber: this.number.value,
                });
            }
        },
    })
    open: ui.PageAction;

    // TODO: Button is not yet in the page, it must be implemented soon
    // must check isHidden and isDisabled methods and functions
    @ui.decorators.pageAction<SalesReturnRequest>({
        icon: 'add',
        title: 'Add',
    })
    addSalesReturnRequestLine: ui.PageAction;

    @ui.decorators.pageAction<SalesReturnRequest>({
        icon: 'none',
        title: 'Set dimensions',
        async onClick() {
            const defaultedFromItem = await attributesAndDimensions.getAttributesAndDimensionsFromItem({
                page: this,
                dimensionDefinitionLevel: 'salesDirect',
                companyId: Number(this.site?.value?.legalCompany?._id),
            });
            this._defaultDimensionsAttributes = await dimensionPanelHelpers.editAndApplyDefaultDimensionsIfNeeded(
                this,
                this.lines,
                this._defaultDimensionsAttributes,
                () => true,
                defaultedFromItem,
            );
        },
    })
    defaultDimension: ui.PageAction;

    @ui.decorators.pageAction<SalesReturnRequest>({
        title: 'Approve',
        isHidden: true,
        async onClick() {
            if (this.number.value && this._id.value) {
                await actionFunctions.approve({
                    salesReturnRequestPage: this,
                    recordId: this._id.value,
                });
            }
            this.manageDisplayButtonApproveAction(false);
            this.manageDisplayButtonRejectAction(false);
            this.$.setPageClean();
            await this.$.refreshNavigationPanel();
            await this.$.router.refresh();
        },
    })
    approve: ui.PageAction;

    @ui.decorators.pageAction<SalesReturnRequest>({
        title: 'Reject',
        isHidden: true,
        async onClick() {
            if (this.number.value && this._id.value) {
                await actionFunctions.reject({
                    salesReturnRequestPage: this,
                    recordId: this._id.value,
                });
            }
            this.manageDisplayButtonApproveAction(false);
            this.manageDisplayButtonRejectAction(false);
            this.$.setPageClean();
            await this.$.refreshNavigationPanel();
            await this.$.router.refresh();
        },
    })
    reject: ui.PageAction;

    @ui.decorators.pageAction<SalesReturnRequest>({
        isTransient: true,
        isHidden: true,
        title: 'Submit for approval',
        async onClick() {
            if (this.site.value?._id && this.$.recordId) {
                await this.submitForApproval(this.site.value._id);
            }
        },
    })
    requestApproval: ui.PageAction;

    @ui.decorators.pageAction<SalesReturnRequest>({
        title: 'Create credit memo',
        isHidden: true,
        async onClick() {
            if (this._id.value) {
                await actionFunctions.createCreditMemoAction({
                    salesReturnRequestPage: this,
                    recordId: this._id.value,
                    isCalledFromRecordPage: true,
                });
            }
        },
        onError(error: string | (Error & { errors: Array<any> })) {
            return ui.localize(
                '@sage/xtrem-sales/pages__sales_return_request__credit_exception',
                'The credit memo could not be created: {{exception}}',
                { exception: utils.formatError(this, error) },
            );
        },
    })
    createSalesCreditMemosFromReturnRequests: ui.PageAction;

    @ui.decorators.pageAction<SalesReturnRequest>({
        title: 'Confirm',
        isHidden: true,
        isTransient: true,
        isDisabled() {
            return this && this.$.isDirty;
        },
        async onClick() {
            await actionFunctions.confirmSalesReturnRequest({
                salesReturnRequestPage: this,
                recordNumber: this.number.value ?? '',
            });
        },
    })
    confirm: ui.PageAction;

    @ui.decorators.textField<SalesReturnRequest>({
        isHidden: true,
        title: 'ID',
        isTitleHidden: true,
    })
    _id: ui.fields.Text;

    @ui.decorators.checkboxField<SalesReturnRequest>({
        isHidden: true,
        title: 'Credit allowed',
        isTitleHidden: true,
    })
    isCreditingAllowed: ui.fields.Checkbox;

    @ui.decorators.section<SalesReturnRequest>({
        title: 'Lines',
    })
    itemSection: ui.containers.Section;

    @ui.decorators.section<SalesReturnRequest>({
        title: 'Header section',
        isTitleHidden: true,
    })
    headerSection: ui.containers.Section;

    @ui.decorators.block<SalesReturnRequest>({
        parent() {
            return this.headerSection;
        },
    })
    headerBlock: ui.containers.Block;

    @ui.decorators.stepSequenceField<SalesReturnRequest>({
        parent() {
            return this.headerBlock;
        },
        options() {
            return [
                this.returnRequestStepSequenceCreate,
                (this.isApprovalManaged.value ||
                    ['pendingApproval', 'approved', 'rejected', 'changeRequested'].includes(
                        this.approvalStatus.value ?? '',
                    )) &&
                this.approvalStatus.value !== 'confirmed'
                    ? this.returnRequestStepSequenceApprove
                    : this.returnRequestStepSequenceConfirm,
                this.returnRequestStepSequenceReceive,
                this.returnRequestStepSequenceCredit,
            ];
        },
        width: 'small',
        isTransient: true,
        isFullWidth: true,
    })
    returnRequestStepSequence: ui.fields.StepSequence;

    @ui.decorators.referenceField<SalesReturnRequest, Site>({
        parent() {
            return this.headerBlock;
        },
        title: 'Site',
        lookupDialogTitle: 'Select site',
        isAutoSelectEnabled: true,
        minLookupCharacters: 0,
        fetchesDefaults: true,
        isMandatory: true,
        isReadOnly() {
            return !!this.$.recordId || this.lines.value?.length > 0;
        },
        columns: [
            ui.nestedFields.text({ bind: 'name' }),
            ui.nestedFields.text({ bind: 'id' }),
            ui.nestedFields.reference<SalesReturnRequest, Site, Company>({
                node: '@sage/xtrem-system/Company',
                tunnelPage: '@sage/xtrem-master-data/Company',
                title: 'Company',
                bind: 'legalCompany',
                valueField: 'name',
                helperTextField: 'id',
                columns: [ui.nestedFields.text({ bind: 'name' }), ui.nestedFields.text({ bind: 'id' })],
            }),
            ui.nestedFields.technical({ bind: 'description' }),
            ui.nestedFields.technical({ bind: 'isInventory' }),
            ui.nestedFields.technical<SalesReturnRequest, Site, User>({
                bind: 'salesReturnRequestDefaultApprover',
                node: '@sage/xtrem-system/User',
                nestedFields: [
                    ui.nestedFields.technical({ bind: 'email' }),
                    ui.nestedFields.technical({ bind: 'firstName' }),
                    ui.nestedFields.technical({ bind: 'lastName' }),
                ],
            }),
            ui.nestedFields.technical<SalesReturnRequest, Site, User>({
                bind: 'salesReturnRequestSubstituteApprover',
                node: '@sage/xtrem-system/User',
                nestedFields: [
                    ui.nestedFields.technical({ bind: 'lastName' }),
                    ui.nestedFields.technical({ bind: 'firstName' }),
                    ui.nestedFields.technical({ bind: 'email' }),
                ],
            }),
            ui.nestedFields.technical({ bind: 'isSales' }),
        ],
        filter() {
            return this.stockSite.value
                ? ({
                      isSales: true,
                      legalCompany: this.stockSite.value.legalCompany,
                  } as Logical<Site>)
                : {
                      isSales: true,
                  };
        },
        async onChange() {
            await this.fetchDefaultsFromSalesSite();
            this.manageDisplayButtonDefaultDimensionAction();
            this.manageDisplayButtonAddSalesReturnRequestLineAction();
        },
    })
    site: ui.fields.Reference<Site>;

    @ui.decorators.referenceField<SalesReturnRequest, Customer>({
        parent() {
            return this.headerBlock;
        },
        node: '@sage/xtrem-master-data/Customer',
        title: 'Sold-to customer',
        lookupDialogTitle: 'Select sold-to customer',
        minLookupCharacters: 0,
        isAutoSelectEnabled: true,
        fetchesDefaults: true,
        isMandatory: true,
        shouldSuggestionsIncludeColumns: true,
        isReadOnly() {
            return !!this.$.recordId || this.lines.value?.length > 0;
        },
        columns: [
            ui.nestedFields.text({ bind: { businessEntity: { name: true } }, title: 'Name' }),
            ui.nestedFields.text({ bind: { businessEntity: { id: true } }, title: 'ID' }),
            ui.nestedFields.text({ bind: { businessEntity: { taxIdNumber: true } }, title: 'Tax ID' }),
            ui.nestedFields.text({ bind: { businessEntity: { country: { name: true } } }, title: 'Country' }),
            ui.nestedFields.technical<SalesReturnRequest, Customer, BusinessEntity>({
                bind: 'businessEntity',
                node: '@sage/xtrem-master-data/BusinessEntity',
                nestedFields: [
                    ui.nestedFields.technical({ bind: 'name' }),
                    ui.nestedFields.technical({ bind: 'id' }),
                    ui.nestedFields.technical<SalesReturnRequest, BusinessEntity, Country>({
                        bind: 'country',
                        node: '@sage/xtrem-structure/Country',
                        nestedFields: [
                            ui.nestedFields.technical({ bind: 'name' }),
                            ui.nestedFields.technical({ bind: 'id' }),
                        ],
                    }),
                ],
            }),
        ],
        orderBy: { isActive: +1 },
        async onChange() {
            await this.fetchDefaultsFromSoldToCustomer();
            this.manageDisplayButtonAddSalesReturnRequestLineAction();
            this.manageDisplayButtonDefaultDimensionAction();
        },
    })
    soldToCustomer: ui.fields.Reference<Customer>;

    @ui.decorators.textField<SalesReturnRequest>({
        parent() {
            return this.headerBlock;
        },
        title: 'Number',
        isReadOnly() {
            return !!this.$.recordId;
        },
        onChange() {
            this.manageDisplayButtonDefaultDimensionAction();
        },
    })
    number: ui.fields.Text;

    @ui.decorators.dateField<SalesReturnRequest>({
        parent() {
            return this.headerBlock;
        },
        title: 'Return request date',
        isMandatory: true,
        onChange() {
            if (this.date.value && Date.parse(this.date.value) > Date.now()) {
                throw new Error(
                    ui.localize(
                        '@sage/xtrem-sales/pages__sales_return_request__return_request_date__cannot__be__future',
                        'The return request date cannot be later than today.',
                    ),
                );
            }
            this.manageDisplayButtonDefaultDimensionAction();
        },
    })
    date: ui.fields.Date;

    @ui.decorators.referenceField<SalesReturnRequest, User>({
        parent() {
            return this.headerBlock;
        },
        title: 'Requester',
        lookupDialogTitle: 'Select requester',
        node: '@sage/xtrem-system/User',
        tunnelPage: '@sage/xtrem-authorization/User',
        valueField: 'displayName',
        helperTextField: 'email',
        columns: [
            ui.nestedFields.text({ title: 'Last name', bind: 'lastName' }),
            ui.nestedFields.text({ title: 'Name', bind: 'firstName' }),
            ui.nestedFields.text({ title: 'Email', bind: 'email' }),
        ],
        isMandatory: true,
        minLookupCharacters: 2,
        isDisabled() {
            return !!this.$.recordId;
        },
    })
    requester: ui.fields.Reference<User>;

    @ui.decorators.tile<SalesReturnRequest>({
        parent() {
            return this.headerSection;
        },
    })
    tileContainer: ui.containers.Tile;

    @ui.decorators.countField<SalesReturnRequest>({
        parent() {
            return this.tileContainer;
        },
        bind: 'lines',
        title: 'Number of items',
        size: 'large',
    })
    salesReturnRequestLineCount: ui.fields.Count;

    @ui.decorators.section<SalesReturnRequest>({
        title: 'Information',
    })
    informationSection: ui.containers.Section;

    @ui.decorators.block<SalesReturnRequest>({
        parent() {
            return this.informationSection;
        },
        width: 'large',
        title: 'Information',
        isTitleHidden: true,
    })
    informationBlock: ui.containers.Block;

    @ui.decorators.dropdownListField<SalesReturnRequest>({
        parent() {
            return this.informationBlock;
        },
        title: 'Return type',
        bind: 'returnType',
        optionType: '@sage/xtrem-sales/SalesReturnRequestReturnType',
        isDisabled() {
            return this.status.value !== 'draft';
        },
    })
    returnType: ui.fields.DropdownList;

    @ui.decorators.referenceField<SalesReturnRequest, BusinessEntityAddress>({
        parent() {
            return this.informationBlock;
        },
        node: '@sage/xtrem-master-data/BusinessEntityAddress',
        title: 'Sold-to address',
        lookupDialogTitle: 'Select sold-to address',
        valueField: 'name',
        minLookupCharacters: 0,
        isAutoSelectEnabled: true,
        fetchesDefaults: true,
        isMandatory: true,
        isHidden: true,
        columns: [
            ui.nestedFields.checkbox({ bind: 'isActive', title: 'Active' }),
            ui.nestedFields.text({ bind: 'name' }),
            ui.nestedFields.text({ bind: 'addressLine1', title: 'Address line 1' }),
            ui.nestedFields.text({ bind: 'addressLine2', title: 'Address line 2' }),
            ui.nestedFields.text({ bind: 'city', title: 'City' }),
            ui.nestedFields.text({ bind: 'region', title: 'Region' }),
            ui.nestedFields.text({ bind: 'postcode', title: 'ZIP code' }),
            ui.nestedFields.reference<SalesReturnRequest, BusinessEntityAddress, Country>({
                bind: 'country',
                columns: [
                    ui.nestedFields.text({ bind: 'name' }),
                    ui.nestedFields.text({ bind: 'id', title: 'ISO 3166-1 alpha-2 code' }),
                    ui.nestedFields.dropdownList({
                        bind: 'regionLabel',
                        title: 'Region label',
                    }),
                    ui.nestedFields.technical({
                        bind: 'zipLabel',
                    }),
                ],
            }),
            ui.nestedFields.text({ bind: 'locationPhoneNumber', title: 'Phone number' }),
            ui.nestedFields.technical<SalesReturnRequest, BusinessEntityAddress, BusinessEntity>({
                bind: 'businessEntity',
                node: '@sage/xtrem-master-data/BusinessEntity',
                nestedFields: [ui.nestedFields.technical({ bind: 'name' }), ui.nestedFields.technical({ bind: 'id' })],
            }),
        ],
        filter() {
            return this.soldToCustomer.value
                ? { businessEntity: { id: this.soldToCustomer.value.businessEntity?.id } }
                : {};
        },
        async onChange() {
            await this.fetchDefaultsFromSoldToAddress();
        },
    })
    soldToLinkedAddress: ui.fields.Reference<BusinessEntityAddress>;

    @ui.decorators.checkboxField<SalesReturnRequest>({
        parent() {
            return this.headerBlock;
        },
        title: 'Printed',
        isDisabled: true,
        isHidden: true,
    })
    isPrinted: ui.fields.Checkbox;

    @ui.decorators.separatorField<SalesReturnRequest>({
        parent() {
            return this.headerBlock;
        },
        isFullWidth: true,
        isInvisible: true,
    })
    statusSeparatorGeneral: ui.fields.Separator;

    @ui.decorators.labelField<SalesReturnRequest>({
        parent() {
            return this.headerBlock;
        },
        title: 'Status',
        bind: 'status',
        optionType: '@sage/xtrem-sales/SalesReturnRequestStatus',
        isHidden: true,
    })
    status: ui.fields.Label;

    @ui.decorators.labelField<SalesReturnRequest>({
        title: 'Display status',
        optionType: '@sage/xtrem-sales/SalesReturnRequestDisplayStatus',
        style() {
            return PillColorCommon.getDisplayStatusPillFeatures(this.displayStatus.value);
        },
    })
    displayStatus: ui.fields.Label<SalesReturnRequestDisplayStatus>;

    @ui.decorators.labelField<SalesReturnRequest>({
        parent() {
            return this.headerBlock;
        },
        title: 'Approval status',
        bind: 'approvalStatus',
        optionType: '@sage/xtrem-sales/SalesReturnRequestApprovalStatus',
        isHidden: true,
    })
    approvalStatus: ui.fields.Label;

    @ui.decorators.tableField<SalesReturnRequest, SalesReturnRequestLine>({
        parent() {
            return this.itemSection;
        },
        title: 'Lines',
        hasLineNumbers: true,
        isTitleHidden: true,
        canSelect: false,
        node: '@sage/xtrem-sales/SalesReturnRequestLine',
        orderBy: { _sortValue: +1 },
        columns: [
            ui.nestedFields.label({
                title: 'Status',
                bind: 'status',
                optionType: '@sage/xtrem-sales/SalesReturnRequestStatus',
                style: (_id, rowData) =>
                    PillColorSales.getLabelColorByStatus('SalesReturnRequestStatus', rowData.status),
            }),
            ui.nestedFields.dropdownList({
                title: 'Origin',
                bind: 'origin',
                optionType: '@sage/xtrem-sales/salesOriginDocumentType',
                isReadOnly: true,
            }),
            ui.nestedFields.technical<SalesReturnRequest, SalesReturnRequestLine>({
                bind: 'document',
                node: '@sage/xtrem-sales/SalesReturnRequest',
                nestedFields: [],
            }),
            ui.nestedFields.reference<SalesReturnRequest, SalesReturnRequestLine, Item>({
                title: 'Item',
                bind: 'item',
                node: '@sage/xtrem-master-data/Item',
                tunnelPage: '@sage/xtrem-master-data/Item',
                valueField: 'name',
                isReadOnly: true,
                helperTextField: 'id',
                columns: [
                    ui.nestedFields.text({ bind: 'name' }),
                    ui.nestedFields.text({ bind: 'id' }),
                    ui.nestedFields.text({ bind: 'description' }),
                    ui.nestedFields.text({ title: 'Category', bind: { category: { name: true } } }),
                    ui.nestedFields.technical({ bind: 'type' }),
                    ui.nestedFields.technical({ bind: 'isStockManaged' }),
                    ui.nestedFields.technical<SalesReturnRequest, Item, UnitOfMeasure>({
                        bind: 'stockUnit',
                        node: '@sage/xtrem-master-data/UnitOfMeasure',
                        nestedFields: [
                            ui.nestedFields.technical({ bind: 'name' }),
                            ui.nestedFields.technical({ bind: 'id' }),
                            ui.nestedFields.technical({ bind: 'symbol' }),
                            ui.nestedFields.technical({ bind: 'decimalDigits' }),
                        ],
                    }),
                    ui.nestedFields.technical<SalesReturnRequest, Item, UnitOfMeasure>({
                        bind: 'salesUnit',
                        node: '@sage/xtrem-master-data/UnitOfMeasure',
                        nestedFields: [
                            ui.nestedFields.technical({ bind: 'name' }),
                            ui.nestedFields.technical({ bind: 'id' }),
                            ui.nestedFields.technical({ bind: 'symbol' }),
                            ui.nestedFields.technical({ bind: 'decimalDigits' }),
                        ],
                    }),
                    ui.nestedFields.technical({ bind: 'lotManagement' }),
                ],
            }),
            ui.nestedFields.text({
                title: 'Item ID',
                bind: { item: { id: true } },
                isReadOnly: true,
            }),
            ui.nestedFields.text({
                title: 'Item description',
                bind: 'itemDescription',
                width: 'large',
                isHiddenOnMainField: true,
                isReadOnly() {
                    return ['closed', 'received'].includes(this.status.value ?? '');
                },
            }),
            ui.nestedFields.reference<SalesReturnRequest, SalesReturnRequestLine, UnitOfMeasure>({
                title: 'Sales unit',
                bind: 'unit',
                node: '@sage/xtrem-master-data/UnitOfMeasure',
                tunnelPage: '@sage/xtrem-master-data/UnitOfMeasure',
                valueField: 'name',
                helperTextField: 'symbol',
                isReadOnly: true,
                isHiddenOnMainField: true,
                columns: [
                    ui.nestedFields.text({ bind: 'name' }),
                    ui.nestedFields.technical({ bind: 'id' }),
                    ui.nestedFields.text({ bind: 'symbol', title: 'Symbol' }),
                    ui.nestedFields.technical({ bind: 'decimalDigits' }),
                ],
            }),
            ui.nestedFields.numeric({
                title: 'Quantity in sales unit',
                bind: 'quantity',
                isReadOnly() {
                    return this.status.value === 'closed' || this.receiptStatus.value === 'received';
                },
                isMandatory: true,
                scale: (_rowId, rowData) => utils.getScaleValue(2, rowData?.unit?.decimalDigits),
                postfix: (_value, rowData) => rowData?.unit?.symbol,
                onChange(rowId, rowData) {
                    if (rowData.quantity && rowData.unitToStockUnitConversionFactor) {
                        rowData.quantityInStockUnit = String(
                            rowData.quantity * rowData.unitToStockUnitConversionFactor,
                        );
                        this.lines.addOrUpdateRecordValue(rowData);
                    }
                },
            }),
            ui.nestedFields.numeric({
                title: 'Stock unit conversion factor',
                bind: 'unitToStockUnitConversionFactor',
                isReadOnly: true,
                scale: 10,
                isHiddenOnMainField: true,
                isHidden(value, rowData) {
                    return rowData?.item?.type === 'service';
                },
            }),
            ui.nestedFields.image({
                title: 'Image',
                isTitleHidden: true,
                bind: { item: { image: true } },
                width: 'small',
                height: '40px',
                isReadOnly: true,
                isExcludedFromMainField: true,
            }),

            ui.nestedFields.numeric({
                title: 'Quantity in stock unit',
                bind: 'quantityInStockUnit',
                isReadOnly: true,
                scale: (_value, rowData) => utils.getScaleValue(2, rowData?.stockUnit?.decimalDigits),
                isExcludedFromMainField: true,
            }),
            ui.nestedFields.reference<SalesReturnRequest, SalesReturnRequestLine, UnitOfMeasure>({
                title: 'Stock unit',
                bind: 'stockUnit',
                node: '@sage/xtrem-master-data/UnitOfMeasure',
                tunnelPage: '@sage/xtrem-master-data/UnitOfMeasure',
                valueField: 'name',
                helperTextField: 'symbol',
                isMandatory: true,
                isReadOnly: true,
                isExcludedFromMainField: true,
                columns: [
                    ui.nestedFields.text({ bind: 'name' }),
                    ui.nestedFields.technical({ bind: 'id' }),
                    ui.nestedFields.text({ bind: 'symbol', title: 'Symbol' }),
                    ui.nestedFields.technical({ bind: 'decimalDigits' }),
                ],
            }),

            // Because we want to display in the line table 2 fields for quantity (value and unit)
            // and we want to display the quantity value + unit in a unique field in the detailPanel,
            // we must set a technical _ property in node to be bound to the _ field in the page
            /*  ui.nestedFields.reference<SalesReturnRequest, SalesReturnRequestLine, UnitOfMeasure>({
                title: 'Unit',
                bind: 'uStockUnit',
                node: '@sage/xtrem-master-data/UnitOfMeasure',
                tunnelPage: '@sage/xtrem-master-data/UnitOfMeasure',
                valueField: 'name',
                helperTextField: 'symbol',
                isExcludedFromMainField: true,
                isMandatory: true,
                isReadOnly: true,
                columns: [
                    ui.nestedFields.text({ bind: 'name' }),
                    ui.nestedFields.technical({ bind: 'id' }),
                    ui.nestedFields.text({ bind: 'symbol', title: 'Symbol' }),
                            ui.nestedFields.technical({ bind: 'decimalDigits' }),
                ],
            }), */
            ui.nestedFields.reference<SalesReturnRequest, SalesReturnRequestLine, Site>({
                title: 'Origin shipping site',
                bind: 'originShippingSite',
                node: '@sage/xtrem-system/Site',
                valueField: 'name',
                helperTextField: 'id',
                isReadOnly: true,
                columns: [
                    ui.nestedFields.text({ bind: 'name' }),
                    ui.nestedFields.text({ bind: 'id' }),
                    ui.nestedFields.reference<SalesReturnRequest, Site, Company>({
                        node: '@sage/xtrem-system/Company',
                        tunnelPage: '@sage/xtrem-master-data/Company',
                        title: 'Company',
                        bind: 'legalCompany',
                        valueField: 'name',
                        helperTextField: 'id',
                        columns: [ui.nestedFields.text({ bind: 'name' }), ui.nestedFields.text({ bind: 'id' })],
                    }),
                    ui.nestedFields.technical({ bind: 'description' }),
                    ui.nestedFields.technical({ bind: 'isInventory' }),
                ],
            }),
            ui.nestedFields.reference<SalesReturnRequest, SalesReturnRequestLine, SalesReturnRequestReason>({
                title: 'Reason',
                bind: 'reason',
                node: '@sage/xtrem-sales/SalesReturnRequestReason',
                valueField: 'name',
                isReadOnly() {
                    return ['closed', 'received'].includes(this.status.value ?? '');
                },
                columns: [
                    ui.nestedFields.text({ bind: 'name' }),
                    ui.nestedFields.technical({ bind: 'description' }),
                    ui.nestedFields.technical({ bind: 'isActive' }),
                ],
            }),
            ui.nestedFields.label({
                title: 'Receipt status',
                width: 'large',
                bind: 'receiptStatus',
                optionType: '@sage/xtrem-sales/SalesDocumentReturnRequestReceiptStatus',
                isHiddenOnMainField: true,
                style: (_id, rowData) =>
                    PillColorSales.getLabelColorByStatus(
                        'SalesDocumentReturnRequestReceiptStatus',
                        rowData.receiptStatus,
                    ),
            }),
            ui.nestedFields.label({
                title: 'Credit memo status',
                bind: 'creditStatus',
                width: 'large',
                optionType: '@sage/xtrem-sales/SalesDocumentCreditStatus',
                isHiddenOnMainField: true,
                style: (_id, rowData) =>
                    PillColorSales.getLabelColorByStatus('SalesDocumentCreditStatus', rowData.creditStatus),
            }),
            ui.nestedFields.technical({ bind: 'computedAttributes' }),
            ui.nestedFields.technical({ bind: 'storedAttributes' }),
            ui.nestedFields.technical({ bind: 'storedDimensions' }),
            ui.nestedFields.technical({ bind: 'storedDimensions' }),
            ui.nestedFields.technical({ bind: 'storedDimensions' }),
            ui.nestedFields.technical({ bind: 'isCreditMemoExpected' }),
            ui.nestedFields.technical({ bind: 'isReceiptExpected' }),
            ui.nestedFields.technical({ bind: 'isCreditingAllowed' }),
            ui.nestedFields.numeric({
                title: 'Quantity in progress',
                bind: 'quantityToReceiveInProgressInSalesUnit',
                isReadOnly: true,
                isExcludedFromMainField: true,
                scale: (_value, rowData) => utils.getScaleValue(2, rowData?.unit?.decimalDigits),
                postfix: (_value, rowData) => rowData?.unit?.symbol,
            }),

            ui.nestedFields.numeric({
                title: 'Receipt quantity',
                bind: 'quantityReceiptInSalesUnit',
                isReadOnly: true,
                isExcludedFromMainField: true,
                scale: (_value, rowData) => utils.getScaleValue(2, rowData?.unit?.decimalDigits),
                postfix: (_value, rowData) => rowData?.unit?.symbol,
            }),

            ui.nestedFields.numeric({
                title: 'Quantity in progress',
                bind: 'quantityCreditedInProgressInSalesUnit',
                isReadOnly: true,
                isExcludedFromMainField: true,
                scale: (_value, rowData) => utils.getScaleValue(2, rowData?.unit?.decimalDigits),
                postfix: (_value, rowData) => rowData?.unit?.symbol,
            }),

            ui.nestedFields.numeric({
                title: 'Credited quantity',
                bind: 'quantityCreditedPostedInSalesUnit',
                isReadOnly: true,
                isExcludedFromMainField: true,
                scale: (_value, rowData) => utils.getScaleValue(2, rowData?.unit?.decimalDigits),
                postfix: (_value, rowData) => rowData?.unit?.symbol,
            }),
            ui.nestedFields.technical({ bind: { internalNote: { value: true } } }),
        ],
        optionsMenu: [
            {
                title: 'All statuses',
                graphQLFilter: {},
            },
            {
                title: 'All open statuses',
                graphQLFilter: { status: { _ne: 'closed' } },
            },
        ],
        optionsMenuType: 'dropdown',
        inlineActions: [
            {
                icon: 'box_arrow_left',
                title: 'Open line panel',
                onClick(rowId) {
                    this.lines.openSidebar(rowId);
                },
            },
        ],
        dropdownActions: [
            {
                icon: 'locked',
                title: 'Close',
                isDisabled(rowId, rowItem) {
                    return rowItem.status === 'closed';
                },
                isHidden(rowId, rowItem) {
                    return rowItem.status === 'closed';
                },
                async onClick(rowId, rowItem) {
                    await this.clickOnLineCloseButton(rowId, rowItem);
                },
            },
            {
                icon: 'unlocked',
                title: 'Open',
                isDisabled(rowId, rowItem) {
                    return rowItem.status !== 'closed';
                },
                isHidden(rowId, rowItem) {
                    return (
                        rowItem.status !== 'closed' ||
                        (rowItem.isCreditMemoExpected &&
                            !rowItem.isReceiptExpected &&
                            (rowItem.quantityCreditedPostedInSalesUnit ?? '') >= (rowItem.quantity ?? '')) ||
                        false
                    );
                },
                async onClick(rowId, rowItem) {
                    await this.clickOnLineOpenButton(rowId, rowItem);
                },
            },
            ui.menuSeparator(),
            {
                icon: 'none',
                title: 'Dimensions',
                async onClick(rowId, rowItem: ui.PartialCollectionValue<SalesReturnRequestLine>) {
                    const rowData = dimensionPanelHelpers.fromPartialToBaseDocumentLineWithAnalytical(rowItem);
                    await utils.applyPanelToLineIfChanged(
                        this.lines,
                        dimensionPanelHelpers.editDisplayDimensions(
                            this,
                            {
                                documentLine: rowData,
                            },
                            {
                                editable: rowItem.status !== 'closed',
                            },
                        ),
                    );
                },
            },
            ui.menuSeparator(),
            {
                icon: 'bin',
                title: 'Delete',
                isDestructive: true,
                isHidden(_rowId, rowItem) {
                    return (
                        this.status.value === 'closed' ||
                        ['approved', 'rejected'].includes(this.approvalStatus.value ?? '') ||
                        rowItem.receiptStatus !== 'notReceived' ||
                        rowItem.creditStatus !== 'notCredited'
                    );
                },
                isDisabled() {
                    return this.status.value !== 'draft';
                },
                async onClick(rowId) {
                    if (
                        await confirmDialogWithAcceptButtonText(
                            this,
                            ui.localize(
                                '@sage/xtrem-sales/pages__sales_return_request__line_delete_action_dialog_title',
                                'Confirm delete',
                            ),
                            ui.localize(
                                '@sage/xtrem-sales/pages__sales_return_request__line_delete_action_dialog_content',
                                'You are about to delete this sales return request line. This action cannot be undone.',
                            ),
                            ui.localize('@sage/xtrem-sales/pages-confirm-continue', 'Continue'),
                        )
                    ) {
                        this.lines.removeRecord(rowId);
                        this.disableSomeHeaderPropertiesIfLines();
                    }
                },
            },
        ],
        fieldActions() {
            return setOrderOfPageTableFieldActions({
                actions: [],
            });
        },

        mobileCard: {
            image: ui.nestedFields.image({ bind: { item: { image: true } } }),
            title: ui.nestedFields.text({ bind: { item: { name: true } }, title: 'Product', isTitleHidden: true }),
            titleRight: ui.nestedFields.label({
                bind: 'status',
                title: 'Status',
                optionType: '@sage/xtrem-sales/SalesReturnRequestStatus',
                style: (_id, rowData) =>
                    PillColorSales.getLabelColorByStatus('SalesReturnRequestStatus', rowData.status),
            }),
            line2: ui.nestedFields.text({ bind: 'itemDescription', title: 'Description' }),
            line3: ui.nestedFields.reference<SalesReturnRequest, SalesReturnRequestLine, SalesReturnRequestReason>({
                title: 'Reason',
                bind: 'reason',
                node: '@sage/xtrem-sales/SalesReturnRequestReason',
                valueField: 'name',
                tunnelPage: undefined,
            }),
        },
        sidebar: {
            title(_id, recordValue) {
                return `${recordValue?.item.name} - ${recordValue?.item.id}`;
            },
            headerDropdownActions: [
                {
                    icon: 'locked',
                    title: 'Close',
                    isDisabled(rowId, rowItem) {
                        return rowItem.status === 'closed';
                    },
                    isHidden(rowId, rowItem) {
                        return rowItem.status === 'closed';
                    },
                    async onClick(rowId, rowItem) {
                        await this.clickOnLineCloseButton(rowId, rowItem);
                    },
                },
                {
                    icon: 'unlocked',
                    title: 'Open',
                    isDisabled(rowId, rowItem) {
                        return rowItem.status !== 'closed';
                    },
                    isHidden(rowId, rowItem) {
                        return (
                            rowItem.status !== 'closed' ||
                            (rowItem.isCreditMemoExpected &&
                                !rowItem.isReceiptExpected &&
                                (rowItem.quantityCreditedPostedInSalesUnit ?? '') >= (rowItem.quantity ?? '')) ||
                            false
                        );
                    },
                    async onClick(rowId, rowItem) {
                        await this.clickOnLineOpenButton(rowId, rowItem);
                    },
                },
                ui.menuSeparator(),
                {
                    icon: 'none',
                    title: 'Dimensions',
                    async onClick(rowId, rowItem: ui.PartialCollectionValue<SalesReturnRequestLine>) {
                        const rowData = dimensionPanelHelpers.fromPartialToBaseDocumentLineWithAnalytical(rowItem);
                        await utils.applyPanelToLineIfChanged(
                            this.lines,
                            dimensionPanelHelpers.editDisplayDimensions(
                                this,
                                {
                                    documentLine: rowData,
                                },
                                {
                                    editable: rowItem.status !== 'closed',
                                },
                            ),
                        );
                    },
                },
                ui.menuSeparator(),
                {
                    icon: 'bin',
                    title: 'Delete',
                    isDestructive: true,
                    isHidden(rowId, rowItem) {
                        return (
                            this.status.value === 'closed' ||
                            ['approved', 'rejected'].includes(this.approvalStatus.value ?? '') ||
                            rowItem.receiptStatus !== 'notReceived' ||
                            rowItem.creditStatus !== 'notCredited'
                        );
                    },
                    isDisabled() {
                        return this.status.value !== 'draft';
                    },
                    onClick(id) {
                        this.lines.removeRecord(id);
                    },
                },
            ],
            headerQuickActions: [],
            async onRecordOpened(_id, recordValue) {
                if (recordValue && +recordValue._id > 0) {
                    this.internalNoteLine.value = recordValue.internalNote.value;
                    await this.fillSalesLinkedDocument(recordValue._id);
                    await this.fillSalesReturnReceiptLines(recordValue._id);
                    await this.fillSalesCreditMemoLines(recordValue._id);
                }
            },
            // @ts-ignore:next-line: Type 'void' is not assignable to type 'Promise<void>'
            onRecordConfirmed(_id, recordValue) {
                if (recordValue) {
                    recordValue.internalNote.value = this.internalNoteLine.value ? this.internalNoteLine.value : '';
                    this.lines.addOrUpdateRecordValue(
                        recordValue as unknown as ExtractEdgesPartial<SalesReturnRequestLine>,
                    );
                }
            },
            layout() {
                return {
                    information: {
                        title: ui.localize('@sage/xtrem-sales/pages_sidebar_tab_title_information', 'Information'),
                        blocks: {
                            mainBlock: {
                                fields: ['item', 'origin', 'itemDescription', 'originShippingSite'],
                            },
                            reasonBlock: {
                                fields: ['reason'],
                            },
                            salesBlock: {
                                title: ui.localize('@sage/xtrem-sales/pages_sidebar_block_title_sales', 'Sales'),
                                fields: ['unit', 'quantity'],
                            },
                            stockBlock: {
                                isHidden(rowId, rowItem) {
                                    return !rowItem?.item.isStockManaged;
                                },
                                title: ui.localize('@sage/xtrem-sales/pages_sidebar_block_title_stock', 'Stock'),
                                fields: ['stockUnit', 'quantityInStockUnit', 'unitToStockUnitConversionFactor'],
                            },
                        },
                    },
                    origin: {
                        title: ui.localize('@sage/xtrem-sales/pages_sidebar_tab_title_origin', 'Origin'),
                        isHidden() {
                            return this.salesLinkedDocuments.value.length === 0;
                        },
                        blocks: {
                            mainBlock: {
                                fields: [this.salesLinkedDocuments],
                            },
                        },
                    },
                    Progress: {
                        title: ui.localize('@sage/xtrem-sales/pages_sidebar_tab_title_progress', 'Progress'),
                        blocks: {
                            mainBlock: {
                                title: ui.localize(
                                    '@sage/xtrem-sales/pages_sidebar_block_title_return_receipt',
                                    'Return receipt',
                                ),
                                fields: [
                                    'receiptStatus',
                                    'quantityToReceiveInProgressInSalesUnit',
                                    'quantityReceiptInSalesUnit',
                                    this.salesReturnReceiptLines,
                                ],
                            },
                            creditBlock: {
                                title: ui.localize(
                                    '@sage/xtrem-sales/pages_sidebar_block_title_credit_memo',
                                    'Credit memo',
                                ),
                                fields: [
                                    'creditStatus',
                                    'quantityCreditedInProgressInSalesUnit',
                                    'quantityCreditedPostedInSalesUnit',
                                    this.salesCreditMemoLines,
                                ],
                            },
                        },
                    },
                    notes: {
                        title: ui.localize('@sage/xtrem-sales/pages_sidebar_tab_title_line_notes', 'Line notes'),
                        blocks: {
                            notesBlock: {
                                fields: [this.internalNoteLine],
                            },
                        },
                    },
                };
            },
        },
    })
    lines: ui.fields.Table<SalesReturnRequestLine>;

    @ui.decorators.richTextField<SalesReturnRequest>({
        isFullWidth: true,
        title: 'Internal line notes',
        capabilities: validCapabilities,
        isTransient: true,
        helperText: 'Notes display on internal documents.',
        isDisabled() {
            return this.status.value === 'closed';
        },
    })
    internalNoteLine: ui.fields.RichText;

    @ui.decorators.tableField<SalesReturnRequest, SalesShipment & { documentType: string }>({
        title: 'Shipment',
        isTitleHidden: true,
        width: 'large',
        isTransient: true,
        node: '@sage/xtrem-sales/SalesShipment',
        canSelect: false,
        columns: [
            ui.nestedFields.link({
                title: 'Origin document number',
                bind: '_id',
                map: (_fieldValue, rowData) => `${rowData.number}`,
                page: '@sage/xtrem-sales/SalesShipment',
                queryParameters(_value, rowData) {
                    return {
                        _id: rowData?._id ?? '',
                    };
                },
            }),
            ui.nestedFields.text({
                title: 'Document type',
                bind: 'documentType',
                isDisabled: true,
            }),
            ui.nestedFields.label({
                title: 'Status',
                bind: 'displayStatus',
                optionType: '@sage/xtrem-sales/SalesShipmentDisplayStatus',
                style: (_id, rowData) => PillColorCommon.getDisplayStatusPillFeatures(rowData?.displayStatus),
            }),
        ],
    })
    salesLinkedDocuments: ui.fields.Table<SalesShipment & { documentType: string }>;

    @ui.decorators.tableField<SalesReturnRequest, SalesReturnReceiptLine>({
        title: 'Receipt lines',
        isTitleHidden: true,
        width: 'large',
        isTransient: true,
        node: '@sage/xtrem-sales/SalesReturnReceiptLine',
        orderBy: { _sortValue: +1 },
        canSelect: false,
        columns: [
            ui.nestedFields.link({
                title: 'Return receipt number',
                bind: '_id', // Dummy link to a string; value is set in the map, as we cannot use valueField like syntax in bind of link field type
                width: 'large',
                isTransient: true,
                map: (_fieldValue, rowData) => `${rowData.document.number}`,
                page: '@sage/xtrem-sales/SalesReturnReceipt',
                queryParameters(_value, rowData) {
                    return { _id: rowData?.document._id ?? '' };
                },
            }),
            ui.nestedFields.label({
                title: 'Return receipt status',
                bind: '_id', // Dummy link to a string; value is set in the map, as we cannot use valueField like syntax in bind of link field type
                // optionType: '@sage/xtrem-sales/SalesReturnReceiptStatus',
                map(value, rowData) {
                    switch (rowData.document.status) {
                        case 'draft':
                            return ui.localize(
                                '@sage/xtrem-sales/enums__sales_return_receipt_status__draft',
                                rowData.document.status,
                            );
                        case 'inProgress':
                            return ui.localize(
                                '@sage/xtrem-sales/enums__sales_return_receipt_status__in_progress',
                                rowData.document.status,
                            );
                        case 'closed':
                            return ui.localize(
                                '@sage/xtrem-sales/enums__sales_return_receipt_status__closed',
                                rowData.document.status,
                            );
                        default:
                            return '';
                    }
                },
                style: (_id, rowData) =>
                    PillColorSales.getLabelColorByStatus('SalesReturnReceiptStatus', rowData?.document?.status),
            }),
            ui.nestedFields.technical<SalesReturnRequest, SalesReturnReceiptLine, SalesReturnReceipt>({
                bind: 'document',
                node: '@sage/xtrem-sales/SalesReturnReceipt',
                nestedFields: [ui.nestedFields.technical({ bind: 'number' })],
            }),
            ui.nestedFields.technical<SalesReturnRequest, SalesReturnReceiptLine, Item>({
                bind: 'item',
                node: '@sage/xtrem-master-data/Item',
                nestedFields: [
                    ui.nestedFields.technical({ bind: 'name' }),
                    ui.nestedFields.technical({ bind: 'id' }),
                    ui.nestedFields.technical({ bind: 'description' }),
                ],
            }),
            ui.nestedFields.technical<SalesReturnRequest, SalesReturnReceiptLine>({
                bind: 'unit',
                node: '@sage/xtrem-master-data/UnitOfMeasure',
                nestedFields: [
                    ui.nestedFields.technical({ bind: 'name' }),
                    ui.nestedFields.technical({ bind: 'id' }),
                    ui.nestedFields.technical({ bind: 'symbol' }),
                    ui.nestedFields.technical({ bind: 'decimalDigits' }),
                ],
            }),
            ui.nestedFields.numeric({
                title: 'Quantity in sales unit',
                bind: 'quantity',
                isReadOnly: true,
                scale: (_value, rowData) => utils.getScaleValue(2, rowData?.unit?.decimalDigits),
                postfix: (_value, rowData) => rowData?.unit?.symbol,
            }),
            ui.nestedFields.numeric({
                title: 'Quantity in stock unit',
                bind: 'quantityInStockUnit',
                isReadOnly: true,
                width: 'large',
                unit: (_id, rowData) => rowData?.stockUnit,
                unitMode: 'unitOfMeasure',
            }),
        ],
    })
    salesReturnReceiptLines: ui.fields.Table<SalesReturnReceiptLine>;

    @ui.decorators.tableField<SalesReturnRequest, SalesCreditMemoLine>({
        title: 'Credit memo lines',
        isTransient: true,
        isTitleHidden: true,
        canSelect: false,
        node: '@sage/xtrem-sales/SalesCreditMemoLine',
        orderBy: { _sortValue: +1 },
        columns: [
            ui.nestedFields.link({
                title: 'Credit memo number',
                bind: '_id', // Dummy link to a string; value is set in the map, as we cannot use valueField like syntax in bind of link field type
                width: 'large',
                map: (_fieldValue, rowData) => `${rowData.document.number}`,
                page: '@sage/xtrem-sales/SalesCreditMemo',
                queryParameters(_value, rowData) {
                    return {
                        _id: rowData?.document._id ?? '',
                    };
                },
            }),
            ui.nestedFields.technical<SalesReturnRequest, SalesCreditMemoLine, SalesCreditMemo>({
                bind: 'document',
                node: '@sage/xtrem-sales/SalesCreditMemo',
                nestedFields: [
                    ui.nestedFields.technical({ bind: 'number' }),
                    ui.nestedFields.technical<SalesReturnRequest, SalesCreditMemo, Currency>({
                        bind: 'currency',
                        node: '@sage/xtrem-master-data/Currency',
                        nestedFields: [
                            ui.nestedFields.technical({ bind: 'name' }),
                            ui.nestedFields.technical({ bind: 'id' }),
                            ui.nestedFields.technical({ bind: 'symbol' }),
                        ],
                    }),
                ],
            }),
            ui.nestedFields.label({
                title: 'Credit memo status',
                bind: '_id',
                map(_value, rowData) {
                    switch (rowData.document.status) {
                        case 'draft':
                            return ui.localize(
                                '@sage/xtrem-sales/enums__sales_credit_memo_status__draft',
                                rowData.document.status,
                            );
                        case 'posted':
                            return ui.localize(
                                '@sage/xtrem-sales/enums__sales_credit_memo_status__posted',
                                rowData.document.status,
                            );
                        case 'inProgress':
                            return ui.localize(
                                '@sage/xtrem-sales/enums__sales_credit_memo_status__inProgress',
                                rowData.document.status,
                            );
                        case 'error':
                            return ui.localize(
                                '@sage/xtrem-sales/enums__sales_credit_memo_status__error',
                                rowData.document.status,
                            );
                        default:
                            return '';
                    }
                },
                style: (_id, rowData) =>
                    PillColorSales.getLabelColorByStatus('SalesCreditMemoStatus', rowData?.document?.status),
            }),
            ui.nestedFields.technical<SalesReturnRequest, SalesCreditMemoLine, Item>({
                bind: 'item',
                node: '@sage/xtrem-master-data/Item',
                nestedFields: [
                    ui.nestedFields.technical({ bind: 'name' }),
                    ui.nestedFields.technical({ bind: 'id' }),
                    ui.nestedFields.technical({ bind: 'description' }),
                ],
            }),
            ui.nestedFields.technical<SalesReturnRequest, SalesCreditMemoLine, UnitOfMeasure>({
                bind: 'unit',
                node: '@sage/xtrem-master-data/UnitOfMeasure',
                nestedFields: [
                    ui.nestedFields.technical({ bind: 'name' }),
                    ui.nestedFields.technical({ bind: 'id' }),
                    ui.nestedFields.technical({ bind: 'symbol' }),
                    ui.nestedFields.technical({ bind: 'decimalDigits' }),
                ],
            }),
            ui.nestedFields.numeric({
                title: 'Quantity in sales unit credited',
                bind: 'quantity',
                isReadOnly: true,
                scale: (_value, rowData) => utils.getScaleValue(2, rowData?.stockUnit?.decimalDigits),
                postfix: (_value, rowData) => rowData?.unit?.symbol,
            }),
            ui.nestedFields.numeric({
                title: 'Amount credited',
                bind: 'netPrice',
                isReadOnly: true,
                width: 'large',
                prefix: (_value, rowData) => rowData?.document?.currency?.symbol ?? '',
                scale: (_value, rowData) => utils.getScaleValue(2, rowData?.document?.currency?.decimalDigits),
            }),
        ],
    })
    salesCreditMemoLines: ui.fields.Table<SalesCreditMemoLine>;

    @ui.decorators.vitalPodField<SalesReturnRequest, Address>({
        parent() {
            return this.informationSection;
        },
        node: '@sage/xtrem-master-data/Address',
        title: 'Sold-to address',
        width: 'small',
        onAddButtonClick() {
            if (this.soldToLinkedAddress.value) {
                const { ...values } = { ...this.soldToLinkedAddress.value };
                delete values.businessEntity;
                return { ...values, _id: null };
            }
            return {};
        },
        dropdownActions: [
            {
                icon: 'lookup',
                title: 'Replace',
                onClick() {
                    this.soldToLinkedAddress.openDialog();
                },
                isDisabled() {
                    return this.status.value === 'closed';
                },
            },
            {
                icon: 'edit',
                title: 'Edit',
                onClick() {
                    this.soldToAddress.isReadOnly = false;
                },
                isDisabled() {
                    return this.status.value === 'closed' || !this.soldToAddress.isReadOnly;
                },
            },
            {
                icon: 'tick',
                title: 'Read-only',
                onClick() {
                    this.soldToAddress.isReadOnly = true;
                    if (this.soldToAddress.value) {
                        this.soldToAddress.value.concatenatedAddress = getConcatenatedAddress(this.soldToAddress.value);
                    }
                },
                isDisabled() {
                    return this.status.value === 'closed' || this.soldToAddress.isReadOnly;
                },
            },
        ],
        isReadOnly: true,
        columns: [
            ui.nestedFields.text({
                bind: 'name',
                title: 'Name',
                isHidden() {
                    return this.soldToAddress.isReadOnly === true;
                },
            }),
            ui.nestedFields.textArea({
                title: 'Bill-to address',
                isTitleHidden: true,
                bind: 'concatenatedAddress',
                width: 'large',
                isHidden() {
                    return this.soldToAddress.isReadOnly === false;
                },
            }),
            ui.nestedFields.text({ bind: 'locationPhoneNumber', title: 'Phone number' }),
            ui.nestedFields.text({
                bind: 'addressLine1',
                title: 'Address line 1',
                width: 'large',
                isHidden() {
                    return this.soldToAddress.isReadOnly === true;
                },
            }),
            ui.nestedFields.text({
                bind: 'addressLine2',
                title: 'Address line 2',
                width: 'large',
                isHidden() {
                    return this.soldToAddress.isReadOnly === true;
                },
            }),
            ui.nestedFields.text({
                bind: 'city',
                title: 'City',
                isHidden() {
                    return this.soldToAddress.isReadOnly === true;
                },
            }),
            ui.nestedFields.text({
                bind: 'region',
                title() {
                    return getCountryRegionTitle(this.soldToAddress.value);
                },
                isHidden() {
                    return this.soldToAddress.isReadOnly === true;
                },
            }),
            ui.nestedFields.text({
                bind: 'postcode',
                title() {
                    return getCountryPostCodeTitle(this.soldToAddress.value);
                },
                isHidden() {
                    return this.soldToAddress.isReadOnly === true;
                },
            }),
            ui.nestedFields.reference({
                bind: 'country',
                columns: [
                    ui.nestedFields.text({ bind: 'name' }),
                    ui.nestedFields.text({ bind: 'id', title: 'ISO 3166-1 alpha-2 code' }),
                    ui.nestedFields.technical({ bind: 'regionLabel' }),
                    ui.nestedFields.technical({ bind: 'zipLabel' }),
                ],
                isHidden() {
                    return this.soldToAddress.isReadOnly === true;
                },
            }),
        ],
    })
    soldToAddress: ui.fields.VitalPod<Address>;

    @ui.decorators.section<SalesReturnRequest>({
        title: 'Shipping',
    })
    shippingSection: ui.containers.Section;

    @ui.decorators.block<SalesReturnRequest>({
        parent() {
            return this.shippingSection;
        },
        width: 'large',
        title: 'Shipping',
        isTitleHidden: true,
    })
    shippingBlock: ui.containers.Block;

    @ui.decorators.referenceField<SalesReturnRequest, Site>({
        parent() {
            return this.shippingBlock;
        },
        title: 'Stock site',
        lookupDialogTitle: 'Select stock site',
        minLookupCharacters: 0,
        isAutoSelectEnabled: true,
        fetchesDefaults: true,
        isMandatory: true,
        isReadOnly() {
            return !!this.$.recordId || this.lines.value?.length > 0;
        },
        columns: [
            ui.nestedFields.text({ bind: 'name' }),
            ui.nestedFields.text({ bind: 'id' }),
            ui.nestedFields.reference<SalesReturnRequest, Site, Company>({
                node: '@sage/xtrem-system/Company',
                tunnelPage: '@sage/xtrem-master-data/Company',
                title: 'Company',
                bind: 'legalCompany',
                valueField: 'name',
                helperTextField: 'id',
                columns: [ui.nestedFields.text({ bind: 'name' }), ui.nestedFields.text({ bind: 'id' })],
            }),
            ui.nestedFields.technical({ bind: 'description' }),
            ui.nestedFields.technical({ bind: 'isLocationManaged' }),
            ui.nestedFields.technical({ bind: 'isInventory' }),
        ],
        filter() {
            return this.site.value
                ? ({
                      isInventory: true,
                      legalCompany: this.site.value.legalCompany,
                  } as Logical<Site>)
                : {
                      isInventory: true,
                  };
        },
        onChange() {
            this.manageDisplayButtonAddSalesReturnRequestLineAction();
        },
    })
    stockSite: ui.fields.Reference<Site>;

    @ui.decorators.referenceField<SalesReturnRequest, Customer>({
        parent() {
            return this.shippingBlock;
        },
        node: '@sage/xtrem-master-data/Customer',
        title: 'Ship-to customer',
        lookupDialogTitle: 'Select ship-to customer',
        minLookupCharacters: 0,
        isAutoSelectEnabled: true,
        fetchesDefaults: true,
        isMandatory: true,
        shouldSuggestionsIncludeColumns: true,
        isReadOnly() {
            return !!this.$.recordId || this.lines.value?.length > 0;
        },
        columns: [
            ui.nestedFields.text({ bind: { businessEntity: { name: true } }, title: 'Name' }),
            ui.nestedFields.text({ bind: { businessEntity: { id: true } }, title: 'ID' }),
            ui.nestedFields.text({ bind: { businessEntity: { taxIdNumber: true } }, title: 'Tax ID' }),
            ui.nestedFields.text({ bind: { businessEntity: { country: { name: true } } }, title: 'Tax ID' }),
            ui.nestedFields.technical<SalesReturnRequest, Customer, BusinessEntity>({
                bind: 'businessEntity',
                node: '@sage/xtrem-master-data/BusinessEntity',
                nestedFields: [
                    ui.nestedFields.technical({ bind: 'name' }),
                    ui.nestedFields.technical({ bind: 'id' }),
                    ui.nestedFields.technical<SalesReturnRequest, BusinessEntity, Country>({
                        bind: 'country',
                        node: '@sage/xtrem-structure/Country',
                        nestedFields: [
                            ui.nestedFields.technical({ bind: 'name' }),
                            ui.nestedFields.technical({ bind: 'id' }),
                        ],
                    }),
                ],
            }),
        ],
        orderBy: { isActive: +1 },
        async onChange() {
            await this.fetchDefaultsFromShipToCustomer();
        },
    })
    shipToCustomer: ui.fields.Reference<Customer>;

    @ui.decorators.referenceField<SalesReturnRequest, DeliveryMode>({
        parent() {
            return this.shippingBlock;
        },
        node: '@sage/xtrem-master-data/DeliveryMode',
        tunnelPage: '@sage/xtrem-master-data/DeliveryMode',
        title: 'Delivery mode',
        lookupDialogTitle: 'Select delivery mode',
        valueField: 'name',
        minLookupCharacters: 0,
        isAutoSelectEnabled: true,
        isMandatory: true,
        columns: [ui.nestedFields.text({ bind: 'name' }), ui.nestedFields.technical({ bind: 'description' })],
    })
    deliveryMode: ui.fields.Reference<DeliveryMode>;

    @ui.decorators.separatorField<SalesReturnRequest>({
        parent() {
            return this.shippingBlock;
        },
        isFullWidth: true,
        isInvisible: true,
    })
    statusSeparatorShipping: ui.fields.Separator;

    @ui.decorators.labelField<SalesReturnRequest>({
        parent() {
            return this.shippingBlock;
        },
        title: 'Receipt statusR',
        bind: 'receiptStatus',
        optionType: '@sage/xtrem-sales/SalesDocumentReturnRequestReceiptStatus',
        isHidden: true,
    })
    receiptStatus: ui.fields.Label;

    @ui.decorators.referenceField<SalesReturnRequest, BusinessEntityAddress>({
        parent() {
            return this.shippingBlock;
        },
        node: '@sage/xtrem-master-data/BusinessEntityAddress',
        title: 'Ship-to address',
        lookupDialogTitle: 'Select ship-to address',
        valueField: 'name',
        minLookupCharacters: 0,
        isAutoSelectEnabled: true,
        fetchesDefaults: true,
        isMandatory: true,
        isHidden: true,
        columns: [
            ui.nestedFields.checkbox({
                bind: { deliveryDetail: { isPrimary: true } },
                title: 'Primary ship-to address',
            }),
            ui.nestedFields.checkbox({ bind: 'isActive', title: 'Active' }),
            ui.nestedFields.text({ bind: 'name' }),
            ui.nestedFields.text({ bind: 'addressLine1', title: 'Address line 1' }),
            ui.nestedFields.text({ bind: 'addressLine2', title: 'Address line 2' }),
            ui.nestedFields.text({ bind: 'city', title: 'City' }),
            ui.nestedFields.text({ bind: 'region', title: 'Region' }),
            ui.nestedFields.text({ bind: 'postcode', title: 'ZIP code' }),
            ui.nestedFields.reference<SalesReturnRequest, BusinessEntityAddress, Country>({
                bind: 'country',
                columns: [
                    ui.nestedFields.text({ bind: 'name' }),
                    ui.nestedFields.text({ bind: 'id', title: 'ISO 3166-1 alpha-2 code' }),
                    ui.nestedFields.technical({ bind: 'regionLabel' }),
                    ui.nestedFields.technical({ bind: 'zipLabel' }),
                ],
            }),
            ui.nestedFields.text({ bind: 'locationPhoneNumber', title: 'Phone number' }),
            ui.nestedFields.technical<SalesReturnRequest, BusinessEntityAddress, BusinessEntity>({
                bind: 'businessEntity',
                node: '@sage/xtrem-master-data/BusinessEntity',
                nestedFields: [ui.nestedFields.technical({ bind: 'name' }), ui.nestedFields.technical({ bind: 'id' })],
            }),
            ui.nestedFields.reference<SalesReturnRequest, BusinessEntityAddress, DeliveryDetail>({
                bind: 'deliveryDetail',
                title: 'Ship-to address',
                node: '@sage/xtrem-master-data/DeliveryDetail',
                valueField: '_id',
                columns: [
                    ui.nestedFields.reference<SalesReturnRequest, DeliveryDetail, Incoterm>({
                        bind: 'incoterm',
                        title: 'Incoterms® rule',
                        node: '@sage/xtrem-master-data/Incoterm',
                        valueField: 'name',
                        helperTextField: 'id',
                        columns: [ui.nestedFields.text({ bind: 'name' }), ui.nestedFields.text({ bind: 'id' })],
                    }),
                    ui.nestedFields.reference<SalesReturnRequest, DeliveryDetail, DeliveryMode>({
                        bind: 'mode',
                        title: 'Delivery mode',
                        node: '@sage/xtrem-master-data/DeliveryMode',
                        valueField: 'name',
                        columns: [ui.nestedFields.text({ bind: 'name' })],
                    }),
                    ui.nestedFields.numeric({ bind: 'leadTime', title: 'Delivery lead time', postfix: 'day(s)' }),
                    ui.nestedFields.reference<SalesReturnRequest, DeliveryDetail, Site>({
                        bind: 'shipmentSite',
                        title: 'Stock site',
                        node: '@sage/xtrem-system/Site',
                        valueField: 'name',
                        helperTextField: 'id',
                        columns: [
                            ui.nestedFields.text({ bind: 'name' }),
                            ui.nestedFields.text({ bind: 'id' }),
                            ui.nestedFields.reference<SalesReturnRequest, Site, Company>({
                                bind: 'legalCompany',
                                title: 'Company',
                                node: '@sage/xtrem-system/Company',
                                tunnelPage: '@sage/xtrem-master-data/Company',
                                valueField: 'name',
                                helperTextField: 'id',
                                columns: [ui.nestedFields.text({ bind: 'name' }), ui.nestedFields.text({ bind: 'id' })],
                            }),
                        ],
                    }),
                ],
            }),
        ],
        filter() {
            // TODO be able to do { _not: { _eq: null } } & then delete the any
            // https://jira.sage.com/browse/XT-67917
            return this.shipToCustomer.value?.businessEntity?._id
                ? {
                      businessEntity: { _id: this.shipToCustomer.value?.businessEntity?._id },
                      deliveryDetail: { _not: { _eq: null } } as any,
                  }
                : {};
        },
        async onChange() {
            await this.cascadeAndFetchDefaultsFromShipToAddress();
        },
    })
    shipToCustomerAddress: ui.fields.Reference<BusinessEntityAddress>;

    @ui.decorators.vitalPodField<SalesReturnRequest, Address>({
        parent() {
            return this.shippingSection;
        },
        node: '@sage/xtrem-master-data/Address',
        title: 'Ship-to address',
        width: 'small',
        onAddButtonClick() {
            if (this.shipToCustomerAddress.value) {
                const { ...values } = { ...this.shipToCustomerAddress.value };
                delete values.businessEntity;
                return { ...values, _id: null };
            }
            return {};
        },
        dropdownActions: [
            {
                icon: 'lookup',
                title: 'Replace',
                onClick() {
                    this.shipToCustomerAddress.openDialog();
                },
                isDisabled() {
                    return this.status.value === 'closed';
                },
            },
            {
                icon: 'edit',
                title: 'Edit',
                onClick() {
                    this.shipToAddress.isReadOnly = false;
                },
                isDisabled() {
                    return this.status.value === 'closed' || !this.shipToAddress.isReadOnly;
                },
            },
            {
                icon: 'tick',
                title: 'Read-only',
                onClick() {
                    this.shipToAddress.isReadOnly = true;
                    if (this.shipToAddress.value) {
                        this.shipToAddress.value.concatenatedAddress = getConcatenatedAddress(this.shipToAddress.value);
                    }
                },
                isDisabled() {
                    return this.status.value === 'closed' || this.shipToAddress.isReadOnly;
                },
            },
        ],
        isReadOnly: true,
        columns: [
            ui.nestedFields.text({
                bind: 'name',
                title: 'Name',
                isHidden() {
                    return this.shipToAddress.isReadOnly === true;
                },
            }),
            ui.nestedFields.textArea({
                title: 'Bill-to address',
                isTitleHidden: true,
                bind: 'concatenatedAddress',
                width: 'large',
                isHidden() {
                    return this.shipToAddress.isReadOnly === false;
                },
            }),
            ui.nestedFields.text({ bind: 'locationPhoneNumber', title: 'Phone number' }),
            ui.nestedFields.text({
                bind: 'addressLine1',
                title: 'Address line 1',
                width: 'large',
                isHidden() {
                    return this.shipToAddress.isReadOnly === true;
                },
            }),
            ui.nestedFields.text({
                bind: 'addressLine2',
                title: 'Address line 2',
                width: 'large',
                isHidden() {
                    return this.shipToAddress.isReadOnly === true;
                },
            }),
            ui.nestedFields.text({
                bind: 'city',
                title: 'City',
                isHidden() {
                    return this.shipToAddress.isReadOnly === true;
                },
            }),
            ui.nestedFields.text({
                bind: 'region',
                title() {
                    return getCountryRegionTitle(this.shipToCustomerAddress.value);
                },
                isHidden() {
                    return this.shipToAddress.isReadOnly === true;
                },
            }),
            ui.nestedFields.text({
                bind: 'postcode',
                title() {
                    return getCountryPostCodeTitle(this.shipToCustomerAddress.value);
                },
                isHidden() {
                    return this.shipToAddress.isReadOnly === true;
                },
            }),
            ui.nestedFields.reference({
                bind: 'country',
                columns: [
                    ui.nestedFields.text({ bind: 'name' }),
                    ui.nestedFields.text({ bind: 'id', title: 'ISO 3166-1 alpha-2 code' }),
                    ui.nestedFields.technical({
                        bind: 'regionLabel',
                    }),
                    ui.nestedFields.technical({
                        bind: 'zipLabel',
                    }),
                ],
                isHidden() {
                    return this.shipToAddress.isReadOnly === true;
                },
            }),
        ],
    })
    shipToAddress: ui.fields.VitalPod<Address>;

    @ui.decorators.section<SalesReturnRequest>({
        title: 'Financial',
    })
    financialSection: ui.containers.Section;

    @ui.decorators.block<SalesReturnRequest>({
        parent() {
            return this.financialSection;
        },
        width: 'large',
        title: 'Financial',
        isTitleHidden: true,
    })
    financialSectionFinancialBlock: ui.containers.Block;

    @ui.decorators.labelField<SalesReturnRequest>({
        parent() {
            return this.financialSectionFinancialBlock;
        },
        title: 'Credit status',
        bind: 'creditStatus',
        optionType: '@sage/xtrem-sales/SalesDocumentCreditStatus',
        style() {
            return PillColorSales.getLabelColorByStatus('SalesDocumentCreditStatus', this.creditStatus.value);
        },
    })
    creditStatus: ui.fields.Label<SalesDocumentCreditStatus>;

    @ui.decorators.referenceField<SalesReturnRequest, Customer>({
        parent() {
            return this.financialSectionFinancialBlock;
        },
        node: '@sage/xtrem-master-data/Customer',
        title: 'Bill-to customer',
        lookupDialogTitle: 'Select bill-to customer',
        minLookupCharacters: 0,
        isAutoSelectEnabled: true,
        fetchesDefaults: true,
        isMandatory: true,
        isReadOnly: true,
        shouldSuggestionsIncludeColumns: true,
        columns: [
            ui.nestedFields.text({ bind: { businessEntity: { name: true } }, title: 'Name' }),
            ui.nestedFields.text({ bind: { businessEntity: { id: true } }, title: 'ID' }),
            ui.nestedFields.text({ bind: { businessEntity: { taxIdNumber: true } }, title: 'Tax ID' }),
            ui.nestedFields.text({ bind: { businessEntity: { country: { name: true } } }, title: 'Country' }),
            ui.nestedFields.technical<SalesReturnRequest, Customer, BusinessEntity>({
                bind: 'businessEntity',
                node: '@sage/xtrem-master-data/BusinessEntity',
                nestedFields: [
                    ui.nestedFields.technical({ bind: 'name' }),
                    ui.nestedFields.technical({ bind: 'id' }),
                    ui.nestedFields.technical<SalesReturnRequest, BusinessEntity, Country>({
                        bind: 'country',
                        node: '@sage/xtrem-structure/Country',
                        nestedFields: [
                            ui.nestedFields.technical({ bind: 'name' }),
                            ui.nestedFields.technical({ bind: 'id' }),
                        ],
                    }),
                    ui.nestedFields.technical<SalesReturnRequest, BusinessEntity, Currency>({
                        bind: 'currency',
                        node: '@sage/xtrem-master-data/Currency',
                        nestedFields: [
                            ui.nestedFields.technical({ bind: 'name' }),
                            ui.nestedFields.technical({ bind: 'id' }),
                            ui.nestedFields.technical({ bind: 'symbol' }),
                        ],
                    }),
                ],
            }),
            ui.nestedFields.technical({ bind: 'isOnHold' }),
            ui.nestedFields.technical<SalesReturnRequest, Customer, PaymentTerm>({
                bind: 'paymentTerm',
                node: '@sage/xtrem-master-data/PaymentTerm',
                nestedFields: [ui.nestedFields.technical({ bind: 'name' })],
            }),
        ],
        orderBy: { isActive: +1 },
        async onChange() {
            await this.fetchDefaultsFromBillToCustomer();
        },
    })
    billToCustomer: ui.fields.Reference<Customer>;

    @ui.decorators.referenceField<SalesReturnRequest, BusinessEntityAddress>({
        parent() {
            return this.financialSectionFinancialBlock;
        },
        node: '@sage/xtrem-master-data/BusinessEntityAddress',
        title: 'Bill-to address',
        lookupDialogTitle: 'Select bill-to address',
        valueField: 'name',
        minLookupCharacters: 0,
        isAutoSelectEnabled: true,
        fetchesDefaults: true,
        isMandatory: true,
        isHidden: true,
        columns: [
            ui.nestedFields.checkbox({ bind: 'isActive', title: 'Active' }),
            ui.nestedFields.text({ bind: 'name' }),
            ui.nestedFields.text({ bind: 'addressLine1', title: 'Address line 1' }),
            ui.nestedFields.text({ bind: 'addressLine2', title: 'Address line 2' }),
            ui.nestedFields.text({ bind: 'city', title: 'City' }),
            ui.nestedFields.text({ bind: 'region', title: 'Region' }),
            ui.nestedFields.text({ bind: 'postcode', title: 'ZIP code' }),
            ui.nestedFields.reference<SalesReturnRequest, BusinessEntityAddress, Country>({
                bind: 'country',
                columns: [
                    ui.nestedFields.text({ bind: 'name' }),
                    ui.nestedFields.text({ bind: 'id', title: 'ISO 3166-1 alpha-2 code' }),
                    ui.nestedFields.technical({ bind: 'regionLabel' }),
                    ui.nestedFields.technical({ bind: 'zipLabel' }),
                ],
            }),
            ui.nestedFields.text({ bind: 'locationPhoneNumber', title: 'Phone number' }),
            ui.nestedFields.technical<SalesReturnRequest, BusinessEntityAddress, BusinessEntity>({
                bind: 'businessEntity',
                node: '@sage/xtrem-master-data/BusinessEntity',
                nestedFields: [ui.nestedFields.technical({ bind: 'name' }), ui.nestedFields.technical({ bind: 'id' })],
            }),
        ],
        filter() {
            return this.billToCustomer.value?.businessEntity?.id
                ? { businessEntity: { id: this.billToCustomer.value.businessEntity.id } }
                : {};
        },
        async onChange() {
            await this.fetchDefaultsFromBillToAddress();
        },
    })
    billToLinkedAddress: ui.fields.Reference<BusinessEntityAddress>;

    @ui.decorators.vitalPodField<SalesReturnRequest, Address>({
        parent() {
            return this.financialSection;
        },
        node: '@sage/xtrem-master-data/Address',
        title: 'Bill-to address',
        width: 'small',
        onAddButtonClick() {
            if (this.billToLinkedAddress.value) {
                const { businessEntity, ...values } = this.billToLinkedAddress.value;
                return { ...values, _id: null };
            }
            return {};
        },
        dropdownActions: [
            {
                icon: 'lookup',
                title: 'Replace',
                onClick() {
                    this.billToLinkedAddress.openDialog();
                },
                isDisabled() {
                    return this.status.value === 'closed';
                },
            },
            {
                icon: 'edit',
                title: 'Edit',
                onClick() {
                    this.billToAddress.isReadOnly = false;
                },
                isDisabled() {
                    return this.status.value === 'closed' || !this.billToAddress.isReadOnly;
                },
            },
            {
                icon: 'tick',
                title: 'Read-only',
                onClick() {
                    this.billToAddress.isReadOnly = true;
                    if (this.billToAddress.value) {
                        this.billToAddress.value.concatenatedAddress = getConcatenatedAddress(this.billToAddress.value);
                    }
                },
                isDisabled() {
                    return this.status.value === 'shipped' || this.billToAddress.isReadOnly;
                },
            },
        ],
        isReadOnly: true,
        columns: [
            ui.nestedFields.text({
                bind: 'name',
                title: 'Name',
                isHidden() {
                    return this.billToAddress.isReadOnly === true;
                },
            }),
            ui.nestedFields.textArea({
                title: 'Bill-to address',
                isTitleHidden: true,
                bind: 'concatenatedAddress',
                width: 'large',
                isHidden() {
                    return this.billToAddress.isReadOnly === false;
                },
            }),
            ui.nestedFields.text({ bind: 'locationPhoneNumber', title: 'Phone number' }),
            ui.nestedFields.text({
                bind: 'addressLine1',
                title: 'Address line 1',
                width: 'large',
                isHidden() {
                    return this.billToAddress.isReadOnly === true;
                },
            }),
            ui.nestedFields.text({
                bind: 'addressLine2',
                title: 'Address line 2',
                width: 'large',
                isHidden() {
                    return this.billToAddress.isReadOnly === true;
                },
            }),
            ui.nestedFields.text({
                bind: 'city',
                title: 'City',
                isHidden() {
                    return this.billToAddress.isReadOnly === true;
                },
            }),
            ui.nestedFields.text({
                bind: 'region',
                title() {
                    return getCountryRegionTitle(this.billToAddress.value);
                },
                isHidden() {
                    return this.billToAddress.isReadOnly === true;
                },
            }),
            ui.nestedFields.text({
                bind: 'postcode',
                title() {
                    return getCountryPostCodeTitle(this.billToAddress.value);
                },
                isHidden() {
                    return this.billToAddress.isReadOnly === true;
                },
            }),
            ui.nestedFields.reference({
                bind: 'country',
                columns: [
                    ui.nestedFields.text({ bind: 'name' }),
                    ui.nestedFields.text({ bind: 'id', title: 'ISO 3166-1 alpha-2 code' }),
                    ui.nestedFields.technical({ bind: 'regionLabel' }),
                    ui.nestedFields.technical({ bind: 'zipLabel' }),
                ],
                isHidden() {
                    return this.billToAddress.isReadOnly === true;
                },
            }),
        ],
    })
    billToAddress: ui.fields.VitalPod<Address>;

    @ui.decorators.separatorField<SalesReturnRequest>({
        parent() {
            return this.financialSectionFinancialBlock;
        },
        isFullWidth: true,
        isInvisible: true,
    })
    invoiceStatusSeparator: ui.fields.Separator;

    @ui.decorators.section<SalesReturnRequest>({
        title: 'Text',
        isHidden: true,
    })
    textSection: ui.containers.Section;

    @ui.decorators.block<SalesReturnRequest>({
        parent() {
            return this.textSection;
        },
        title: '',
        isTitleHidden: true,
    })
    textSectionTextBlock: ui.containers.Block;

    @ui.decorators.richTextField<SalesReturnRequest>({
        parent() {
            return this.textSectionTextBlock;
        },
        title: 'Text',
        isFullWidth: true,
        isDisabled: true,
    })
    text: ui.fields.RichText;

    @ui.decorators.section<SalesReturnRequest>({ title: 'Notes', isTitleHidden: true })
    notesSection: ui.containers.Section;

    @ui.decorators.block<SalesReturnRequest>({
        title: 'Notes',
        isTitleHidden: true,
        parent() {
            return this.notesSection;
        },
    })
    noteBlock: ui.containers.Block;

    @ui.decorators.richTextField<SalesReturnRequest>({
        parent() {
            return this.noteBlock;
        },
        isFullWidth: true,
        title: 'Internal notes',
        capabilities: validCapabilities,
        helperText: 'Notes display on internal documents.',
        isDisabled() {
            return this.status.value === 'closed';
        },
    })
    internalNote: ui.fields.RichText;

    @ui.decorators.switchField<SalesReturnRequest>({
        parent() {
            return this.noteBlock;
        },
        title: 'Repeat the document notes on new documents.',
        isDisabled() {
            return this.status.value === 'closed';
        },
        width: 'large',
    })
    isTransferHeaderNote: ui.fields.Switch;

    @ui.decorators.switchField<SalesReturnRequest>({
        parent() {
            return this.noteBlock;
        },
        title: 'Repeat all the line notes on new documents.',
        isDisabled() {
            return this.status.value === 'closed';
        },
        width: 'large',
    })
    isTransferLineNote: ui.fields.Switch;

    // Request approver email table starts here
    @ui.decorators.section<SalesReturnRequest>({
        isHidden: true,
        title: 'Request for approval',
    })
    requestApprovalSection: ui.containers.Section;

    @ui.decorators.block<SalesReturnRequest>({
        parent() {
            return this.requestApprovalSection;
        },
        title: '',
        isTitleHidden: true,
    })
    requestApprovalBlock: ui.containers.Block;

    @ui.decorators.textField<SalesReturnRequest>({
        parent() {
            return this.requestApprovalBlock;
        },
        title: 'To',
        helperText: 'A request for approval email will be sent to this address',
        isTransient: true,
        isFullWidth: true,
        validation(val) {
            if (val.length > 0 && !validEmail(val)) {
                return ui.localize(
                    '@sage/xtrem-sales/pages__sales_return_request__invalid-email',
                    'Invalid email address {{value}}',
                    {
                        value: val,
                    },
                );
            }
            return undefined;
        },
    })
    emailAddressApproval: ui.fields.Text;

    @ui.decorators.buttonField<SalesReturnRequest>({
        isTransient: true,
        width: 'small',
        parent() {
            return this.requestApprovalBlock;
        },
        map() {
            return ui.localize(
                '@sage/xtrem-sales/pages__sales_return_request__select_approval_button_text',
                'Select approver',
            );
        },
        async onClick() {
            await this.$.dialog
                .custom('info', this.approverSelectionBlock)
                .then(() => {
                    this.emailAddressApproval.value = this.selectedUser.value?.email
                        ? this.selectedUser.value?.email
                        : this.emailAddressApproval.value;
                })
                .finally(() => {
                    this.selectedUser.value = null;
                });
        },
        title: 'Select approver',
        isTitleHidden: true,
    })
    selectApprover: ui.fields.Button;

    @ui.decorators.buttonField<SalesReturnRequest>({
        isTransient: true,
        parent() {
            return this.requestApprovalBlock;
        },
        map() {
            return ui.localize(
                '@sage/xtrem-sales/pages__sales_return_request__send_approval_request_button_text',
                'Send',
            );
        },
        async onClick() {
            if (
                await confirmDialogWithAcceptButtonText(
                    this,
                    ui.localize(
                        '@sage/xtrem-sales/pages__sales_return_request__send_approval_request_dialog_title',
                        'Confirm send approval email',
                    ),
                    ui.localize(
                        '@sage/xtrem-sales/pages__sales_return_request__send_approval_request_dialog_content',
                        'You are about to send the approval email.',
                    ),
                    ui.localize('@sage/xtrem-sales/pages-confirm-send', 'Send'),
                )
            ) {
                if (!this.emailAddressApproval.value) {
                    return;
                }
                this.$.loader.isHidden = false;
                try {
                    const isSend = await this.$.graph
                        .node('@sage/xtrem-sales/SalesReturnRequest')
                        .mutations.sendApprovalRequestMail(true, {
                            document: this.$.recordId ?? this._id.value ?? '',
                            user: { email: this.emailAddressApproval.value },
                        })
                        .execute();

                    if (isSend) {
                        this.$.showToast(
                            ui.localize(
                                '@sage/xtrem-sales/pages__sales_return_request__email_sent',
                                'Email sent to {{value}} for approval.',
                                { value: this.emailAddressApproval.value },
                            ),
                            { type: 'success' },
                        );
                    }
                    this.requestApprovalSection.isHidden = true;
                    this.$.loader.isHidden = true;
                    this.$.setPageClean();
                    await this.$.refreshNavigationPanel();
                    await this.$.router.refresh();
                    this.$.finish();
                } catch (error) {
                    this.$.loader.isHidden = true;
                    const message = error.message.substring(error.message.lastIndexOf(':') + 2);
                    this.$.showToast(
                        ui.localize(
                            '@sage/xtrem-sales/pages__sales_return_request__email_exception',
                            'Could not send request email. ({{exception}})',
                            { exception: message },
                        ),
                        { type: 'error' },
                    );
                }
            }
        },
        title: 'Send',
        isTitleHidden: true,
    })
    sendApprovalRequestButton: ui.fields.Button;

    @ui.decorators.block<SalesReturnRequest>({
        title: 'Select approver',
    })
    approverSelectionBlock: ui.containers.Block;

    @ui.decorators.tableField<SalesReturnRequest, User & { sortOrder: number; type: string }>({
        parent() {
            return this.approverSelectionBlock;
        },
        canSelect: false,
        title: 'Users',
        isTitleHidden: true,
        isTransient: true,
        pageSize: 10,
        isReadOnly: true,
        orderBy: { sortOrder: +1 },
        columns: [
            ui.nestedFields.text({ title: 'Last name', bind: 'lastName' }),
            ui.nestedFields.text({ title: 'Name', bind: 'firstName' }),
            ui.nestedFields.text({ title: 'Email', bind: 'email' }),
            ui.nestedFields.label({
                title: 'Approver',
                bind: 'type',
                backgroundColor: value => {
                    const isDefaultUser =
                        value ===
                        ui.localize('@sage/xtrem-sales/pages__sales_return_request__default_approver', 'Default');
                    return PillColorCommon.setBooleanStatusColors('isDefaultUser', isDefaultUser, 'backgroundColor');
                },
                borderColor: value => {
                    const isDefaultUser =
                        value ===
                        ui.localize('@sage/xtrem-sales/pages__sales_return_request__default_approver', 'Default');
                    return PillColorCommon.setBooleanStatusColors('isDefaultUser', isDefaultUser, 'borderColor');
                },
                color: value => {
                    const isDefaultUser =
                        value ===
                        ui.localize('@sage/xtrem-sales/pages__sales_return_request__default_approver', 'Default');
                    return PillColorCommon.setBooleanStatusColors('isDefaultUser', isDefaultUser, 'textColor');
                },
            }),
            ui.nestedFields.technical({ bind: 'sortOrder' }),
        ],
        onRowClick(_rowId, rowData) {
            this.selectedUser.value = rowData as unknown as ExtractEdgesPartial<User>;
        },
    })
    users: ui.fields.Table<User & { sortOrder: number; type: string }>;

    @ui.decorators.referenceField<SalesReturnRequest, User>({
        parent() {
            return this.approverSelectionBlock;
        },
        title: 'Selected user',
        lookupDialogTitle: 'Select selected user',
        isTitleHidden: true,
        isReadOnly: true,
        node: '@sage/xtrem-system/User',
        tunnelPage: '@sage/xtrem-authorization/User',
        valueField: 'email',
        helperTextField: 'displayName',
        isTransient: true,
        isFullWidth: true,
        columns: [
            ui.nestedFields.text({ title: 'Last name', bind: 'lastName' }),
            ui.nestedFields.text({ title: 'Name', bind: 'firstName' }),
            ui.nestedFields.text({ title: 'Email', bind: 'email' }),
        ],
    })
    selectedUser: ui.fields.Reference<User>;
    // Request approver email table end here

    @ui.decorators.switchField<SalesReturnRequest>({
        isHidden: true,
        bind: { site: { isSalesReturnRequestApprovalManaged: true } },
    })
    isApprovalManaged: ui.fields.Switch;

    async initPage() {
        // TODO: when enabling direct creation, remove this line below

        this.billToAddress.isReadOnly = true;
        this.soldToAddress.isReadOnly = true;
        this.shipToAddress.isReadOnly = true;
        if (this.$.recordId) {
            const isHeaderDisabled = this.status.value === 'closed';
            // Disable header fields if necessary
            this.number.isDisabled = isHeaderDisabled;
            this.stockSite.isDisabled = isHeaderDisabled;
            this.site.isDisabled = isHeaderDisabled;
            this.date.isDisabled = isHeaderDisabled;
            this.soldToCustomer.isDisabled = isHeaderDisabled;
            this.soldToLinkedAddress.isDisabled = isHeaderDisabled;
            this.soldToAddress.isDisabled = isHeaderDisabled;
            this.shipToCustomerAddress.isDisabled = isHeaderDisabled;
            this.shipToAddress.isDisabled = isHeaderDisabled;
            this.billToCustomer.isDisabled = isHeaderDisabled;
            this.billToLinkedAddress.isDisabled = isHeaderDisabled;
            this.billToAddress.isDisabled = isHeaderDisabled;

            // Disable shipment fields if necessary
            this.deliveryMode.isDisabled = isHeaderDisabled;
        } else {
            this.date.value = DateValue.today().toString();

            // TODO: when enabling direct creation, remove these lines below
            this.number.isDisabled = true;
            this.site.isDisabled = true;
            this.stockSite.isDisabled = true;
            this.date.isDisabled = true;
            this.soldToCustomer.isDisabled = true;
            this.soldToLinkedAddress.isDisabled = true;
            this.soldToAddress.isDisabled = true;
            this.shipToCustomerAddress.isDisabled = true;
            this.shipToAddress.isDisabled = true;
            this.billToCustomer.isDisabled = true;
            this.billToLinkedAddress.isDisabled = true;
            this.billToAddress.isDisabled = true;
            this.deliveryMode.isDisabled = true;
        }

        if (!this.status.value) {
            this.status.value = 'draft';
        }

        this._defaultDimensionsAttributes = dimensionPanelHelpers.initDefaultDimensions();

        if (this.$.queryParameters.action) {
            switch (this.$.queryParameters.action) {
                case 'approved': {
                    await this.returnRequestApproving();
                    break;
                }
                case 'rejected': {
                    await this.returnRequestRejecting();
                    break;
                }
                default: {
                    throw new Error(
                        ui.localize('@sage/xtrem-sales/pages__sales_return_request___no_parameters', 'No action set.'),
                    );
                }
            }
        }
    }

    async addEditSalesReturnRequestLine(salesReturnRequestLineId: string, line: Dict<any>, rowId: string) {
        let _id: string | null = salesReturnRequestLineId;
        if (Number(rowId) < 0) _id = null;
        const { values } = this.$;

        const changedLine = await this.$.dialog.page(
            '@sage/xtrem-sales/SalesReturnRequestLinePanel',
            {
                _id: _id ?? '',
                rowId,
                document: values ? JSON.stringify(values) : '',
                line: line ? JSON.stringify(line) : '',
                status: this.status.value ?? '',
            },
            {
                rightAligned: true,
                size: 'large',
            },
        );

        if (changedLine) {
            this.lines.addOrUpdateRecordValue(changedLine);
            this.disableSomeHeaderPropertiesIfLines();
        }
    }

    private disableSomeHeaderPropertiesIfLines() {
        const isDisabled = this.lines.value.length > 0;
        this.site.isDisabled = isDisabled;
        this.stockSite.isDisabled = isDisabled;
        this.soldToCustomer.isDisabled = isDisabled;
        this.soldToLinkedAddress.isDisabled = isDisabled;
        this.soldToAddress.isDisabled = isDisabled;
    }

    async cascadeAndFetchDefaultsFromShipToAddress() {
        if (this.deliveryMode.value) {
            if (
                await confirmDialogWithAcceptButtonText(
                    this,
                    ui.localize(
                        '@sage/xtrem-sales/pages__sales_return-request__cascade_ship_to_address_update_dialog_title',
                        'Confirm update',
                    ),
                    ui.localize(
                        '@sage/xtrem-sales/pages__sales_return-request__cascade_ship_to_address_update_dialog_content',
                        'You are about to update delivery information.',
                    ),
                    ui.localize('@sage/xtrem-sales/pages-confirm-update', 'Update'),
                )
            ) {
                await this.fetchDefaultsFromShipToAddress();
            }
        } else {
            await this.fetchDefaultsFromShipToAddress();
        }
    }

    async errorOnQuantity(message: string) {
        await this.$.dialog.message(
            'error',
            ui.localize(
                '@sage/xtrem-sales/pages__sales_return-request__on_return-request_quantity_changed_error_title',
                'Error on quantity',
            ),
            message,
        );
    }

    async fillSalesCreditMemoLines(rowId: string) {
        const oldIsDirty = this.$.isDirty;

        const resultCreditMemoLines = extractEdges(
            await this.$.graph
                .node('@sage/xtrem-sales/SalesReturnRequestLineSalesCreditMemoLine')
                .query(
                    ui.queryUtils.edgesSelector(
                        {
                            document: {
                                _id: true,
                                item: { _id: true, name: true },
                                quantity: true,
                                quantityInStockUnit: true,
                                unit: { _id: true, name: true, decimalDigits: true, id: true, symbol: true },
                                document: {
                                    _id: true,
                                    number: true,
                                    status: true,
                                    currency: { name: true, symbol: true, id: true },
                                },
                            },
                            amount: true,
                        },
                        { filter: { linkedDocument: { _id: rowId } } },
                    ),
                )
                .execute(),
        );

        this.salesCreditMemoLines.value = resultCreditMemoLines.map(e => ({ ...e.document, netPrice: e.amount }));
        if (!oldIsDirty && this.$.isDirty) {
            this.$.setPageClean();
        }
    }

    async fillSalesReturnReceiptLines(rowId: string) {
        const oldIsDirty = this.$.isDirty;
        this.salesReturnReceiptLines.value = [];
        const result = await this.$.graph
            .node('@sage/xtrem-sales/SalesReturnRequestLineToSalesReturnReceiptLine')
            .query(
                ui.queryUtils.edgesSelector(
                    {
                        document: {
                            _id: true,
                            item: {
                                _id: true,
                                name: true,
                            },
                            quantity: true,
                            quantityInStockUnit: true,
                            unit: {
                                _id: true,
                                name: true,
                                decimalDigits: true,
                                id: true,
                                symbol: true,
                            },
                            stockUnit: {
                                _id: true,
                                name: true,
                                decimalDigits: true,
                                id: true,
                                symbol: true,
                            },
                            document: {
                                _id: true,
                                number: true,
                                status: true,
                            },
                        },
                    },
                    {
                        filter: { linkedDocument: { _id: rowId } },
                    },
                ),
            )
            .execute();
        this.salesReturnReceiptLines.value = result.edges.map(e => e.node.document);
        if (!oldIsDirty && this.$.isDirty) {
            this.$.setPageClean();
        }
    }

    async fillSalesLinkedDocument(rowId: string) {
        const LinkDocument = await this.$.graph
            .node('@sage/xtrem-sales/SalesShipmentLineToSalesReturnRequestLine')
            .query(
                ui.queryUtils.edgesSelector(
                    {
                        _id: true,
                        document: { _id: true },
                        linkedDocument: {
                            _id: true,
                            document: { _id: true, number: true, status: true, displayStatus: true },
                        },
                    },
                    { filter: { document: { _id: rowId } } },
                ),
            )
            .execute();

        if (LinkDocument.edges.length) {
            this.salesLinkedDocuments.value = [
                {
                    ...LinkDocument.edges[0].node.linkedDocument.document,
                    documentType: ui.localizeEnumMember('@sage/xtrem-sales/SalesDocumentType', 'salesShipment'),
                } as ui.PartialNodeWithId<SalesShipment & { documentType: string }>,
            ];
        }

        this.salesLinkedDocuments.isHidden = this.salesLinkedDocuments.value.length === 0;
    }

    async fetchDefaultsFromSoldToCustomer() {
        await this.$.fetchDefaults([
            'soldToLinkedAddress',
            'soldToAddress',
            'shipToCustomer',
            'shipToCustomerAddress',
            'shipToAddress',
            'billToCustomer',
            'billToLinkedAddress',
            'billToAddress',
            'stockSite',
        ]);
    }

    async fetchDefaultsFromSoldToAddress() {
        await this.$.fetchDefaults(['soldToAddress']);
    }

    async fetchDefaultsFromShipToCustomer() {
        await this.$.fetchDefaults(['shipToCustomerAddress', 'shipToAddress', 'stockSite']);
    }

    async fetchDefaultsFromShipToAddress() {
        if (!this.shipToCustomerAddress.value?.deliveryDetail?.shipmentSite && this.stockSite.value) {
            await this.$.fetchDefaults(['shipToAddress']);
        } else {
            await this.$.fetchDefaults(['shipToAddress', 'stockSite']);
        }
    }

    async fetchDefaultsFromBillToCustomer() {
        await this.$.fetchDefaults(['billToLinkedAddress', 'billToAddress']);
    }

    async fetchDefaultsFromBillToAddress() {
        await this.$.fetchDefaults(['billToAddress']);
    }

    async fetchDefaultsFromSalesSite() {
        await this.$.fetchDefaults(['stockSite']);
    }

    /**
     * Runs the setSalesReturnRequestLineCloseStatus action
     */
    async clickOnLineCloseButton(rowId: any, rowItem: any) {
        if (
            await confirmDialogWithAcceptButtonText(
                this,
                ui.localize(
                    '@sage/xtrem-sales/pages__sales_return_request__line_close_action_dialog_title',
                    'Confirm update',
                ),
                ui.localize(
                    '@sage/xtrem-sales/pages__sales_return_request__line_close_action_dialog_content',
                    'You are about to close this sales return request line.',
                ),
                ui.localize('@sage/xtrem-sales/pages-confirm-continue', 'Continue'),
            )
        ) {
            this.$.loader.isHidden = false;
            const returnStatus: any = await this.executeSetSalesReturnRequestLineCloseStatusMutation(rowItem);
            const returnValue = getLocalizeSalesReturnRequestLineCloseStatusMethodReturn(returnStatus);
            this.$.showToast(returnValue.content, returnValue.option);
            if (returnValue.severity === 'success') {
                await this.$.router.refresh();
                await this.$.refreshNavigationPanel();
            }
            this.$.loader.isHidden = true;
        }
    }

    /**
     * Runs the setSalesReturnRequestLineCloseStatus mutation with parameters
     */
    executeSetSalesReturnRequestLineCloseStatusMutation(row: any) {
        return this.$.graph
            .node('@sage/xtrem-sales/SalesReturnRequest')
            .mutations.setSalesReturnRequestLineCloseStatus(true, { salesReturnRequestLine: `_id:${row._id}` })
            .execute();
    }

    /**
     * Runs the setSalesReturnRequestLineOpenStatus action
     */
    async clickOnLineOpenButton(rowId: any, rowItem: any) {
        if (
            await confirmDialogWithAcceptButtonText(
                this,
                ui.localize(
                    '@sage/xtrem-sales/pages__sales_return_request__line_open_action_dialog_title',
                    'Confirm update',
                ),
                ui.localize(
                    '@sage/xtrem-sales/pages__sales_return_request__line_open_action_dialog_content',
                    'You are about to open this sales return request line.',
                ),
                ui.localize('@sage/xtrem-sales/pages-confirm-open', 'Open'),
            )
        ) {
            this.$.loader.isHidden = false;
            const returnStatus: any = await this.executeSetSalesReturnRequestLineOpenStatusMutation(rowItem);
            const returnValue = getLocalizeSalesReturnRequestLineOpenStatusMethodReturn(returnStatus);
            this.$.showToast(returnValue.content, returnValue.option);
            if (returnValue.severity === 'success') {
                await this.$.router.refresh();
                await this.$.refreshNavigationPanel();
            }
            this.$.loader.isHidden = true;
        }
    }

    /**
     * Runs the setSalesReturnRequestLineOpenStatus mutation with parameters
     */
    executeSetSalesReturnRequestLineOpenStatusMutation(row: any) {
        return this.$.graph
            .node('@sage/xtrem-sales/SalesReturnRequest')
            .mutations.setSalesReturnRequestLineOpenStatus(true, { salesReturnRequestLine: `_id:${row._id}` })
            .execute();
    }

    async returnRequestApproving() {
        this.$.loader.isHidden = false;

        const result = await this.$.graph
            .node('@sage/xtrem-sales/SalesReturnRequest')
            .mutations.approve(true, { returnRequest: this._id.value ?? '' })
            .execute();

        this.$.showToast(result, { type: 'success' });
        this.$.router.goTo(`@sage/xtrem-sales/SalesReturnRequest`, { _id: this._id.value ?? '' });
        this.$.loader.isHidden = true;
    }

    async returnRequestRejecting() {
        this.$.loader.isHidden = false;

        const result = await this.$.graph
            .node('@sage/xtrem-sales/SalesReturnRequest')
            .mutations.reject(true, { returnRequest: this._id.value ?? '' })
            .execute();

        this.$.showToast(result, { type: 'success' });
        this.$.router.goTo(`@sage/xtrem-sales/SalesReturnRequest`, { _id: this._id.value ?? '' });
        this.$.loader.isHidden = true;
    }

    _setStepSequenceStatusObject(
        stepSequenceValues: SalesReturnRequestStepSequenceStatus,
    ): Dict<ui.StepSequenceStatus> {
        return {
            [this.returnRequestStepSequenceCreate]: stepSequenceValues.create,
            [this.returnRequestStepSequenceApprove]: stepSequenceValues.approve,
            [this.returnRequestStepSequenceConfirm]: stepSequenceValues.confirm,
            [this.returnRequestStepSequenceReceive]: stepSequenceValues.receive,
            [this.returnRequestStepSequenceCredit]: stepSequenceValues.credit,
        };
    }

    private async submitForApproval(receivingSite: string) {
        const { usersList, emailAddressApproval } = await actionFunctions.loadApprovers({
            salesReturnRequestPage: this,
            siteId: receivingSite,
        });
        this.users.value = usersList;
        this.emailAddressApproval.value = emailAddressApproval;

        this.requestApprovalSection.isHidden = false;
        await this.$.dialog.custom('info', this.requestApprovalSection, {
            cancelButton: { isHidden: true },
            acceptButton: { isHidden: true },
            resolveOnCancel: true,
        });
        this.$.setPageClean();
        this.requestApprovalSection.isHidden = true;
    }
}
