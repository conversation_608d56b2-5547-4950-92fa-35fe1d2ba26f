import type { Filter } from '@sage/xtrem-client';
import type {
    BusinessEntity,
    BusinessEntityAddress,
    Customer,
    DeliveryMode,
    Incoterm,
    Item,
} from '@sage/xtrem-master-data-api';
import { setReferenceIfSingleValue } from '@sage/xtrem-master-data/build/lib/client-functions/get-default';
import * as MasterDataUtils from '@sage/xtrem-master-data/build/lib/client-functions/utils';
import { sales } from '@sage/xtrem-master-data/build/lib/menu-items/sales';
import type { GraphApi, SalesOrder, SalesOrderLine } from '@sage/xtrem-sales-api';
import type { GraphApi as SchedulerGraphApi } from '@sage/xtrem-scheduler-api';
import { once, recurring } from '@sage/xtrem-scheduler/build/lib/client-functions/job-execution';
import { scheduleWizard } from '@sage/xtrem-scheduler/build/lib/client-functions/job-schedule';
import type { Country } from '@sage/xtrem-structure-api';
import type { Company, Site } from '@sage/xtrem-system-api';
import * as ui from '@sage/xtrem-ui';
import * as _ from 'lodash';

@ui.decorators.page<SalesOrderMassAllocation>({
    isTransient: true,
    title: 'Sales order mass allocation',
    menuItem: sales,
    priority: 450,
    access: { node: '@sage/xtrem-sales/SalesOrder', bind: '$create' },
    skipDirtyCheck: true,
    async onLoad() {
        this.action.value = 'allocation';
        this.actionButtonDefaultTitle = this.allocate.title as string;
        await setReferenceIfSingleValue([this.company, this.stockSite]);
        this.allocate.isDisabled = true;
    },
    onError(error: string | (Error & { errors: Array<any> })) {
        this.$.loader.isHidden = true;
        return MasterDataUtils.formatError(this, error);
    },
    businessActions() {
        return [this.allocate, this.schedule];
    },
    onDirtyStateUpdated(isDirty) {
        if (isDirty) {
            this.allocate.isDisabled = false;
        }
    },
})
export class SalesOrderMassAllocation extends ui.Page<GraphApi> {
    @ui.decorators.section<SalesOrderMassAllocation>({
        title: 'General',
        isTitleHidden: true,
    })
    mainSection: ui.containers.Section;

    @ui.decorators.block<SalesOrderMassAllocation>({
        parent() {
            return this.mainSection;
        },
    })
    criteriaBlock: ui.containers.Block;

    actionButtonDefaultTitle: string;

    @ui.decorators.selectField<SalesOrderMassAllocation>({
        title: 'Action',
        optionType: '@sage/xtrem-stock-data/AllocationRequestType',
        isMandatory: true,
        parent() {
            return this.criteriaBlock;
        },
        onChange() {
            if (this.action.value === 'deallocation') {
                this.allocate.title = ui.localize(
                    '@sage/xtrem-sales/pages__sales_order_mass_allocation__deallocate____title',
                    'Deallocate',
                );
            } else {
                this.allocate.title = this.actionButtonDefaultTitle;
            }
        },
    })
    action: ui.fields.Select;

    @ui.decorators.textField<SalesOrderMassAllocation>({
        title: 'Description',
        parent() {
            return this.criteriaBlock;
        },
    })
    description: ui.fields.Text;

    @ui.decorators.referenceField<SalesOrderMassAllocation, Company>({
        title: 'Company',
        lookupDialogTitle: 'Select company',
        isMandatory: true,
        node: '@sage/xtrem-system/Company',
        tunnelPage: '@sage/xtrem-master-data/Company',
        valueField: 'name',
        helperTextField: 'id',
        columns: [
            ui.nestedFields.text({ title: 'Name', bind: 'name' }),
            ui.nestedFields.text({ title: 'ID', bind: 'id' }),
        ],
        parent() {
            return this.criteriaBlock;
        },
        onChange() {
            if (
                this.company.value &&
                this.stockSite.value &&
                this.stockSite.value.legalCompany?.id !== this.company.value.id
            ) {
                this.stockSite.value = null;
            }
        },
    })
    company: ui.fields.Reference<Company>;

    @ui.decorators.referenceField<SalesOrderMassAllocation, Site>({
        title: 'Site',
        lookupDialogTitle: 'Select site',
        isMandatory: true,
        node: '@sage/xtrem-system/Site',
        tunnelPage: '@sage/xtrem-master-data/Site',
        valueField: 'name',
        helperTextField: 'id',
        columns: [
            ui.nestedFields.text({ title: 'ID', bind: 'id' }),
            ui.nestedFields.text({ title: 'Name', bind: 'name' }),
            ui.nestedFields.reference<SalesOrderMassAllocation, Site, Company>({
                title: 'Company',
                bind: 'legalCompany',
                valueField: 'name',
                node: '@sage/xtrem-system/Company',
                tunnelPage: '@sage/xtrem-master-data/Company',
                columns: [
                    ui.nestedFields.text({ title: 'Name', bind: 'name' }),
                    ui.nestedFields.text({ title: 'ID', bind: 'id' }),
                ],
                isHidden() {
                    return !!this.company.value;
                },
            }),
        ],
        parent() {
            return this.criteriaBlock;
        },
        filter() {
            return {
                isInventory: { _eq: true },
                ...(this.company.value ? { legalCompany: { _id: this.company.value._id } } : {}),
            };
        },
        async onChange() {
            if (!this.company.value && this.stockSite.value?.legalCompany) {
                this.company.value = this.stockSite.value.legalCompany;
                await this.$.commitValueAndPropertyChanges();
                await this.company.validate();
            }
        },
    })
    stockSite: ui.fields.Reference<Site>;

    @ui.decorators.dateField<SalesOrderMassAllocation>({
        title: 'Latest shipping date',
        parent() {
            return this.criteriaBlock;
        },
    })
    latestShippingDate: ui.fields.Date;

    @ui.decorators.referenceField<SalesOrderMassAllocation, Item>({
        title: 'From item',
        lookupDialogTitle: 'Select from item',
        node: '@sage/xtrem-master-data/Item',
        tunnelPage: '@sage/xtrem-master-data/Item',
        valueField: 'name',
        helperTextField: 'id',
        columns: [
            ui.nestedFields.text({ bind: 'name' }),
            ui.nestedFields.text({ bind: 'id' }),
            ui.nestedFields.text({ bind: 'description' }),
            ui.nestedFields.text({ title: 'Category', bind: { category: { name: true } } }),
        ],
        parent() {
            return this.criteriaBlock;
        },
        filter() {
            return {
                isSold: true,
                isStockManaged: true,
                ...(this.stockSite.value ? { itemSites: { _atLeast: 1, site: this.stockSite.value._id } } : {}),
                ...(this.toItem.value ? { name: { _lte: this.toItem.value.name } } : {}),
            };
        },
    })
    fromItem: ui.fields.Reference<Item>;

    @ui.decorators.referenceField<SalesOrderMassAllocation, Item>({
        title: 'To item',
        lookupDialogTitle: 'Select to item',
        node: '@sage/xtrem-master-data/Item',
        tunnelPage: '@sage/xtrem-master-data/Item',
        valueField: 'name',
        helperTextField: 'id',
        columns: [
            ui.nestedFields.text({ bind: 'name' }),
            ui.nestedFields.text({ bind: 'id' }),
            ui.nestedFields.text({ bind: 'description' }),
            ui.nestedFields.text({ title: 'Category', bind: { category: { name: true } } }),
        ],
        parent() {
            return this.criteriaBlock;
        },
        filter() {
            return {
                isSold: true,
                isStockManaged: true,
                ...(this.stockSite.value ? { itemSites: { _atLeast: 1, site: this.stockSite.value._id } } : {}),
                ...(this.fromItem.value ? { name: { _gte: this.fromItem.value.name } } : {}),
            };
        },
    })
    toItem: ui.fields.Reference<Item>;

    @ui.decorators.referenceField<SalesOrderMassAllocation, Customer>({
        parent() {
            return this.criteriaBlock;
        },
        node: '@sage/xtrem-master-data/Customer',
        title: 'From sold-to customer',
        lookupDialogTitle: 'Select from sold-to customer',
        shouldSuggestionsIncludeColumns: true,
        columns: [
            ui.nestedFields.text({ bind: { businessEntity: { name: true } }, title: 'Name' }),
            ui.nestedFields.text({ bind: { businessEntity: { id: true } }, title: 'ID' }),
            ui.nestedFields.text({ bind: { businessEntity: { taxIdNumber: true } }, title: 'Tax ID' }),
            ui.nestedFields.text({ bind: { businessEntity: { country: { name: true } } }, title: 'Country' }),
            ui.nestedFields.technical<SalesOrderMassAllocation, Customer, BusinessEntity>({
                bind: 'businessEntity',
                node: '@sage/xtrem-master-data/BusinessEntity',
                nestedFields: [
                    ui.nestedFields.technical({ bind: 'name' }),
                    ui.nestedFields.technical({ bind: 'id' }),
                    ui.nestedFields.technical<SalesOrderMassAllocation, BusinessEntity, Country>({
                        bind: 'country',
                        node: '@sage/xtrem-structure/Country',
                        nestedFields: [
                            ui.nestedFields.technical({ bind: 'name' }),
                            ui.nestedFields.technical({ bind: 'id' }),
                        ],
                    }),
                ],
            }),
            ui.nestedFields.technical<SalesOrderMassAllocation, Customer, BusinessEntityAddress>({
                bind: 'primaryAddress',
                node: '@sage/xtrem-master-data/BusinessEntityAddress',
                nestedFields: [
                    ui.nestedFields.technical({ bind: 'isActive' }),
                    ui.nestedFields.technical({ bind: 'name' }),
                    ui.nestedFields.technical({ bind: 'addressLine1' }),
                    ui.nestedFields.technical({ bind: 'addressLine2' }),
                    ui.nestedFields.technical({ bind: 'city' }),
                    ui.nestedFields.technical({ bind: 'region' }),
                    ui.nestedFields.technical({ bind: 'postcode' }),
                    ui.nestedFields.technical<SalesOrderMassAllocation, BusinessEntityAddress, Country>({
                        bind: 'country',
                        node: '@sage/xtrem-structure/Country',
                        nestedFields: [
                            ui.nestedFields.technical({ bind: 'name' }),
                            ui.nestedFields.technical({ bind: 'id' }),
                        ],
                    }),
                    ui.nestedFields.technical({ bind: 'locationPhoneNumber' }),
                ],
            }),
        ],
        filter() {
            return {
                ...(this.toSoldToCustomer.value?.businessEntity?.name
                    ? { businessEntity: { name: { _lte: this.toSoldToCustomer.value.businessEntity.name } } }
                    : {}),
            };
        },
    })
    fromSoldToCustomer: ui.fields.Reference<Customer>;

    @ui.decorators.referenceField<SalesOrderMassAllocation, Customer>({
        parent() {
            return this.criteriaBlock;
        },
        node: '@sage/xtrem-master-data/Customer',
        title: 'To sold-to customer',
        lookupDialogTitle: 'Select to sold-to customer',
        shouldSuggestionsIncludeColumns: true,
        columns: [
            ui.nestedFields.text({ bind: { businessEntity: { name: true } }, title: 'Name' }),
            ui.nestedFields.text({ bind: { businessEntity: { id: true } }, title: 'ID' }),
            ui.nestedFields.text({ bind: { businessEntity: { taxIdNumber: true } }, title: 'Tax ID' }),
            ui.nestedFields.technical<SalesOrderMassAllocation, Customer, BusinessEntity>({
                bind: 'businessEntity',
                node: '@sage/xtrem-master-data/BusinessEntity',
                nestedFields: [
                    ui.nestedFields.technical({ bind: 'name' }),
                    ui.nestedFields.technical({ bind: 'id' }),
                    ui.nestedFields.technical<SalesOrderMassAllocation, BusinessEntity, Country>({
                        bind: 'country',
                        node: '@sage/xtrem-structure/Country',
                        nestedFields: [
                            ui.nestedFields.technical({ bind: 'name' }),
                            ui.nestedFields.technical({ bind: 'id' }),
                        ],
                    }),
                ],
            }),
            ui.nestedFields.technical<SalesOrderMassAllocation, Customer, BusinessEntityAddress>({
                bind: 'primaryAddress',
                node: '@sage/xtrem-master-data/BusinessEntityAddress',
                nestedFields: [
                    ui.nestedFields.technical({ bind: 'isActive' }),
                    ui.nestedFields.technical({ bind: 'name' }),
                    ui.nestedFields.technical({ bind: 'addressLine1' }),
                    ui.nestedFields.technical({ bind: 'addressLine2' }),
                    ui.nestedFields.technical({ bind: 'city' }),
                    ui.nestedFields.technical({ bind: 'region' }),
                    ui.nestedFields.technical({ bind: 'postcode' }),
                    ui.nestedFields.technical<SalesOrderMassAllocation, BusinessEntityAddress, Country>({
                        bind: 'country',
                        node: '@sage/xtrem-structure/Country',
                        nestedFields: [
                            ui.nestedFields.technical({ bind: 'name' }),
                            ui.nestedFields.technical({ bind: 'id' }),
                        ],
                    }),
                    ui.nestedFields.technical({ bind: 'locationPhoneNumber' }),
                ],
            }),
        ],
        filter() {
            return {
                ...(this.fromSoldToCustomer.value?.businessEntity?.name
                    ? { businessEntity: { name: { _gte: this.fromSoldToCustomer.value.businessEntity.name } } }
                    : {}),
            };
        },
    })
    toSoldToCustomer: ui.fields.Reference<Customer>;

    @ui.decorators.referenceField<SalesOrderMassAllocation, Incoterm>({
        parent() {
            return this.criteriaBlock;
        },
        node: '@sage/xtrem-master-data/Incoterm',
        tunnelPage: '@sage/xtrem-master-data/Incoterm',
        title: 'Incoterms® rule',
        lookupDialogTitle: 'Select Incoterms rule',
        valueField: 'name',
        helperTextField: 'id',
        columns: [
            ui.nestedFields.text({ bind: 'name' }),
            ui.nestedFields.text({ bind: 'id' }),
            ui.nestedFields.technical({ bind: 'description' }),
        ],
    })
    incoterm: ui.fields.Reference<Incoterm>;

    @ui.decorators.referenceField<SalesOrderMassAllocation, DeliveryMode>({
        parent() {
            return this.criteriaBlock;
        },
        node: '@sage/xtrem-master-data/DeliveryMode',
        tunnelPage: '@sage/xtrem-master-data/DeliveryMode',
        title: 'Delivery mode',
        lookupDialogTitle: 'Select delivery mode',
        valueField: 'name',
        helperTextField: 'id',
        columns: [ui.nestedFields.text({ bind: 'name' }), ui.nestedFields.technical({ bind: 'description' })],
    })
    deliveryMode: ui.fields.Reference<DeliveryMode>;

    @ui.decorators.referenceField<SalesOrderMassAllocation, SalesOrder>({
        title: 'From sales order',
        lookupDialogTitle: 'Select from sales order',
        node: '@sage/xtrem-sales/SalesOrder',
        valueField: 'number',
        columns: [
            ui.nestedFields.text({ title: 'Number', bind: 'number' }),
            ui.nestedFields.date({ title: 'Order date', bind: 'date' }),
        ],
        parent() {
            return this.criteriaBlock;
        },
        filter() {
            return {
                displayStatus: { _nin: ['shipped', 'closed'] },
                ...(this.stockSite.value ? { stockSite: { _id: this.stockSite.value._id } } : {}),
                ...(this.toSalesOrder.value ? { number: { _lte: this.toSalesOrder.value.number } } : {}),
            };
        },
    })
    fromSalesOrder: ui.fields.Reference<SalesOrder>;

    @ui.decorators.referenceField<SalesOrderMassAllocation, SalesOrder>({
        title: 'To sales order',
        lookupDialogTitle: 'Select to sales order',
        node: '@sage/xtrem-sales/SalesOrder',
        valueField: 'number',
        columns: [
            ui.nestedFields.text({ title: 'Number', bind: 'number' }),
            ui.nestedFields.date({ title: 'Order date', bind: 'date' }),
        ],
        parent() {
            return this.criteriaBlock;
        },
        filter() {
            return {
                displayStatus: { _nin: ['shipped', 'closed'] },
                ...(this.stockSite.value ? { stockSite: { _id: this.stockSite.value._id } } : {}),
                ...(this.fromSalesOrder.value ? { number: { _gte: this.fromSalesOrder.value.number } } : {}),
            };
        },
    })
    toSalesOrder: ui.fields.Reference<SalesOrder>;

    @ui.decorators.pageAction<SalesOrderMassAllocation>({
        title: 'Allocate',
        async onClick() {
            const validationError = await this.$.page.validate();

            if (validationError.length) {
                return;
            }
            this.allocate.isDisabled = true; // Disable the button to prevent multiple clicks
            this.$.setPageClean();

            this.$.loader.isHidden = false;

            const { massAllocationFilter, massAllocationData } = this.getMassAllocationParameters();

            await this.$.graph
                .node('@sage/xtrem-sales/SalesOrderLine')
                .asyncOperations.massAutoAllocation.start(
                    { trackingId: true },
                    {
                        filter: `${JSON.stringify(massAllocationFilter)}`,
                        data: massAllocationData,
                    },
                )
                .execute();
            this.$.loader.isHidden = true;

            await this.$.dialog.message(
                'info',
                this.action.value === 'allocation'
                    ? ui.localize(
                          '@sage/xtrem-sales/pages__sales_order_mass_allocation__title',
                          'Allocation request submitted',
                      )
                    : ui.localize(
                          '@sage/xtrem-sales/pages__sales_order_mass_deallocation__title',
                          'Deallocation request submitted',
                      ),
                this.action.value === 'allocation'
                    ? ui.localize(
                          '@sage/xtrem-sales/pages__sales_order_mass_allocation__message',
                          'The allocation request was submitted.',
                      )
                    : ui.localize(
                          '@sage/xtrem-sales/pages__sales_order_mass_deallocation__message',
                          'The deallocation request was submitted.',
                      ),
            );
        },
    })
    allocate: ui.PageAction;

    @ui.decorators.pageAction<SalesOrderMassAllocation>({
        title: 'Schedule',
        async onClick() {
            const validationError = await this.$.page.validate();

            if (validationError.length) {
                return;
            }

            const { massAllocationFilter, massAllocationData } = this.getMassAllocationParameters();

            /** as unknow as graphApi ... because actualy we are not able to import api of the package only  */
            await scheduleWizard(this as unknown as ui.Page<SchedulerGraphApi>, {
                jobSchedule: [once, recurring],
                filter: JSON.stringify(massAllocationFilter),
                operationKey: 'SalesOrderLine|massAutoAllocation|start',
                additionalParameters: {
                    data: JSON.stringify(massAllocationData),
                },
                isParametersHidden: true,
            });
        },
    })
    schedule: ui.PageAction;

    getItemFilter(): Filter<Item> {
        const itemFilter: {
            name?: {
                _gte?: string;
                _lte?: string;
            };
            itemSites?: {
                _atLeast: 1;
                site: { id: string };
            };
        } = {};

        if (this.fromItem.value) {
            itemFilter.name = {
                _gte: this.fromItem.value.name as string,
            };
        }

        if (this.toItem.value) {
            itemFilter.name = {
                ...itemFilter.name,
                _lte: this.toItem.value.name as string,
            };
        }

        return itemFilter;
    }

    getSalesOrderFilter(): Filter<SalesOrder> {
        let salesOrderFilter: {
            number?: {
                _gte?: string;
                _lte?: string;
            };
            stockSite?: {
                id: string;
            };
            shippingDate?: {
                _lte: string;
            };
            soldToCustomer?: {
                businessEntity: {
                    name: {
                        _gte?: string;
                        _lte?: string;
                    };
                };
            };
            incoterm?: {
                id: string;
            };
            deliveryMode?: {
                id: string;
            };
        } = {};

        if (this.stockSite.value) {
            salesOrderFilter = {
                stockSite: {
                    id: this.stockSite.value.id as string,
                },
            };
        }

        if (this.incoterm.value) {
            salesOrderFilter = {
                ...salesOrderFilter,
                incoterm: {
                    id: this.incoterm.value.id as string,
                },
            };
        }

        if (this.deliveryMode.value) {
            salesOrderFilter = {
                ...salesOrderFilter,
                deliveryMode: {
                    id: this.deliveryMode.value.id as string,
                },
            };
        }

        if (this.latestShippingDate.value) {
            salesOrderFilter = {
                ...salesOrderFilter,
                shippingDate: { _lte: this.latestShippingDate.value },
            };
        }

        if (this.fromSalesOrder.value) {
            salesOrderFilter.number = {
                _gte: this.fromSalesOrder.value.number as string,
            };
        }

        if (this.toSalesOrder.value) {
            salesOrderFilter.number = {
                ...salesOrderFilter.number,
                _lte: this.toSalesOrder.value.number as string,
            };
        }

        if (this.fromSoldToCustomer.value) {
            salesOrderFilter.soldToCustomer = {
                businessEntity: {
                    name: { _gte: this.fromSoldToCustomer.value.businessEntity?.name as string },
                },
            };
        }

        if (this.toSoldToCustomer.value) {
            salesOrderFilter.soldToCustomer = {
                businessEntity: {
                    name: {
                        ...salesOrderFilter.soldToCustomer?.businessEntity?.name,
                        _lte: this.toSoldToCustomer.value.businessEntity?.name as string,
                    },
                },
            };
        }

        return salesOrderFilter;
    }

    getMassAllocationParameters() {
        const itemFilter = this.getItemFilter();
        const salesOrderFilter = this.getSalesOrderFilter();

        const massAllocationFilter: Filter<SalesOrderLine> = {
            ...(!_.isEmpty(itemFilter) ? { item: itemFilter } : {}),
            ...(!_.isEmpty(salesOrderFilter) ? { document: salesOrderFilter } : {}),
        };

        const massAllocationData = {
            requestType: this.action.value,
            ...(this.description.value && { requestDescription: this.description.value }),
        };

        return { massAllocationFilter, massAllocationData };
    }
}
