import { validCapabilities } from '@sage/xtrem-master-data/build/lib/client-functions/fields-properties';
import type { GraphApi, ProformaInvoice as ProformaInvoiceNode, SalesOrder } from '@sage/xtrem-sales-api';
import * as ui from '@sage/xtrem-ui';
import type { ProformaInvoiceDialogParameters } from '../client-functions/interfaces/sales-order-actions-functions';

@ui.decorators.page<ProformaInvoice, ProformaInvoiceNode>({
    title: 'Proforma invoice',
    mode: 'default',
    module: 'xtrem-sales',
    node: '@sage/xtrem-sales/ProformaInvoice',
    async onLoad() {
        await this.init();
    },
    businessActions() {
        return [this.cancel, this.save];
    },
})
export class ProformaInvoice extends ui.Page<GraphApi> {
    @ui.decorators.pageAction<ProformaInvoice>({
        title: 'Save',
        buttonType: 'primary',
        isDisabled() {
            return !this.$.isDirty;
        },
        isHidden() {
            return this.$.queryParameters.option === 'read';
        },
        async onClick() {
            await this.$standardSaveAction.execute(true);
        },
    })
    save: ui.PageAction;

    @ui.decorators.pageAction<ProformaInvoice>({
        title: 'Cancel',
        buttonType: 'tertiary',
        isHidden() {
            return this.$.queryParameters.option === 'read';
        },
        onClick() {
            return this.$.finish();
        },
    })
    cancel: ui.PageAction;

    @ui.decorators.section<ProformaInvoice>({
        isTitleHidden: true,
        isDisabled() {
            return this.$.queryParameters.option === 'read';
        },
    })
    headerSection: ui.containers.Section;

    @ui.decorators.block<ProformaInvoice>({
        parent() {
            return this.headerSection;
        },
        width: 'large',
    })
    headerBlock: ui.containers.Block;

    @ui.decorators.referenceField<ProformaInvoice, SalesOrder>({
        parent() {
            return this.headerBlock;
        },
        node: '@sage/xtrem-sales/SalesOrder',
        valueField: 'number',
        isReadOnly: true,
        isHidden: true,
    })
    salesOrder: ui.fields.Reference<SalesOrder>;

    @ui.decorators.numericField<ProformaInvoice>({
        parent() {
            return this.headerBlock;
        },
        bind: '_id',
        isHidden: true,
        isReadOnly: true,
    })
    _id: ui.fields.Numeric;

    @ui.decorators.numericField<ProformaInvoice>({
        parent() {
            return this.headerBlock;
        },
        isHidden: true,
        isReadOnly: true,
    })
    version: ui.fields.Numeric;

    @ui.decorators.dateField<ProformaInvoice>({
        parent() {
            return this.headerBlock;
        },
        isMandatory: true,
    })
    expirationDate: ui.fields.Date;

    @ui.decorators.richTextField<ProformaInvoice>({
        parent() {
            return this.headerBlock;
        },
        isFullWidth: true,
        capabilities: validCapabilities,
    })
    customerComment: ui.fields.RichText;

    getCreationDefaultValues() {
        return this.$.graph
            .node('@sage/xtrem-sales/ProformaInvoice')
            .getDefaults(
                {
                    customerComment: { value: true },
                    expirationDate: true,
                    version: true,
                },
                {
                    data: {
                        salesOrder: this.salesOrder.value?._id,
                    },
                },
            )
            .execute();
    }

    async setCreationDefaultValues() {
        const defaultValues = await this.getCreationDefaultValues();
        this.version.value = defaultValues.version;
        this.customerComment.value = defaultValues.customerComment.value;
    }

    setPageValues(dialogParameters: ProformaInvoiceDialogParameters<'URLReady'>) {
        this.salesOrder.value = { ...JSON.parse(String(dialogParameters.salesOrder)) };
        this.version.value = (dialogParameters.version || 0) as number;
        this.customerComment.value = (dialogParameters.customerComment || '') as string;
        this.expirationDate.value = dialogParameters.expirationDate as string;
    }

    async init() {
        const dialogParametersParsed: ProformaInvoiceDialogParameters<'URLReady'> = JSON.parse(
            this.$.queryParameters.dialogParameters as string,
        );
        this.setPageValues(dialogParametersParsed);
        switch (this.$.queryParameters.option) {
            case 'create':
                await this.setCreationDefaultValues();
                this.$.page.title = ui.localize(
                    '@sage/xtrem-sales/pages__proforma_invoice__generate_page_title',
                    'Generate proforma {{orderNumber}}',
                    { orderNumber: this.salesOrder.value?.number },
                );
                this.save.title = ui.localize(
                    '@sage/xtrem-sales/pages__proforma_invoice__generate_button_title',
                    'Generate',
                );
                break;
            case 'read':
                this.$.page.title = ui.localize(
                    '@sage/xtrem-sales/pages__proforma_invoice__edit_page_title',
                    'Proforma {{orderNumber}}, version {{versionNumber}}',
                    { orderNumber: this.salesOrder.value?.number, versionNumber: this.version.value },
                );
                break;
            default:
        }
    }
}
