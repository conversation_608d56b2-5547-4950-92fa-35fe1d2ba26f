import type { UnitOfMeasure } from '@sage/xtrem-master-data-api';
import * as MasterDataUtils from '@sage/xtrem-master-data/build/lib/client-functions/utils';
import { getScaleValue } from '@sage/xtrem-master-data/lib/client-functions/utils';
import type { GraphApi, SalesOrderStatus } from '@sage/xtrem-sales-api';
import * as ui from '@sage/xtrem-ui';
import type { OrderAssignmentTable } from '../client-functions/interfaces/order-assignment';
import { queryOrderAssignment } from '../client-functions/sales-order-assignment';

@ui.decorators.page<SalesOrderLineAssignmentDetailsPanel>({
    title: 'Assigned orders',
    mode: 'default',
    node: '@sage/xtrem-stock-data/OrderAssignment',
    module: 'xtrem-sales',
    isTransient: true,
    access: { node: '@sage/xtrem-sales/SalesOrder', bind: '$create' },
    businessActions() {
        return [this.cancel, this.confirm];
    },
    async onLoad() {
        const { itemStockUnit, lineId, quantityInStockUnit, remainingQuantityToShipInStockUnit, status } = JSON.parse(
            this.$.queryParameters.line as string,
        );
        const orderAssignmentLines = await queryOrderAssignment({
            orderAssignmentPage: this,
            salesOrderLineId: lineId,
            demandType: 'salesOrderLine',
        });
        orderAssignmentLines.forEach(orderAssignmentLine => {
            this.assignmentLines.addOrUpdateRecordValue({
                ...orderAssignmentLine,
                quantityNotAssignedOnSupplyOrder:
                    +orderAssignmentLine.supplyWorkInProgress.expectedQuantity -
                    +orderAssignmentLine.quantityInStockUnit,
            });
        });
        this.oldAssignedQuantities = orderAssignmentLines.map(orderAssignmentLine => ({
            _id: orderAssignmentLine._id,
            quantityInStockUnit: parseFloat(orderAssignmentLine.quantityInStockUnit),
            originalQuantityInStockUnit: parseFloat(quantityInStockUnit),
        }));
        this.itemStockUnit = itemStockUnit;
        this.requiredQuantity.value = +quantityInStockUnit;
        this.remainingQuantityToShipInStockUnit.value = remainingQuantityToShipInStockUnit;
        this.demandOrderStatus = status;
        this.$.setPageClean();
        this.updateHeaderQuantities();
    },

    onError(error: string | (Error & { errors: Array<any> })) {
        return MasterDataUtils.formatError(this, error);
    },
})
export class SalesOrderLineAssignmentDetailsPanel extends ui.Page<GraphApi> {
    supplyOrderIdsToDelete: string[] = [];

    oldAssignedQuantities: { _id: string; quantityInStockUnit: number; originalQuantityInStockUnit: number }[] = [];

    itemStockUnit: Partial<UnitOfMeasure> = {
        decimalDigits: 0,
        id: '',
        symbol: '',
        _id: '',
    };

    demandOrderStatus: SalesOrderStatus;

    @ui.decorators.pageAction<SalesOrderLineAssignmentDetailsPanel>({
        title: 'Save',
        async onClick() {
            const updateLinePromises = this.assignmentLines.value
                .filter(line => line.action === 'update')
                .map(line =>
                    // We should use bulkUpdate here to update https://jira.sage.com/browse/XT-47259
                    // Call update method and return a promise
                    this.$.graph
                        .node('@sage/xtrem-stock-data/OrderAssignment')
                        .update(
                            {
                                _id: true,
                            },
                            {
                                data: {
                                    _id: line._id,
                                    quantityInStockUnit: line.quantityInStockUnit,
                                },
                            },
                        )
                        .execute(),
                );
            // Map the supplyOrderIdsToDelete to an array of delete promises
            const deleteLinePromises = this.supplyOrderIdsToDelete.map(supplyOrderId =>
                // Call update method and return a promise
                this.$.graph.delete({ _id: supplyOrderId, nodeName: '@sage/xtrem-stock-data/OrderAssignment' }),
            );
            // Wait for all the update and delete promises to resolve
            await Promise.all([...updateLinePromises, ...deleteLinePromises]);
            this.$.finish(this.oldAssignedQuantities.length);
        },
        onError(error) {
            this.$.showToast(error.message, { timeout: 0, type: 'error' });
        },
    })
    confirm: ui.PageAction;

    @ui.decorators.pageAction<SalesOrderLineAssignmentDetailsPanel>({
        title: 'Cancel',
        buttonType: 'tertiary',
        onClick() {
            this.$.finish(true);
        },
    })
    cancel: ui.PageAction;

    @ui.decorators.section<SalesOrderLineAssignmentDetailsPanel>({
        isOpen: true,
        isTitleHidden: true,
    })
    componentSection: ui.containers.Section;

    @ui.decorators.block<SalesOrderLineAssignmentDetailsPanel>({
        parent() {
            return this.componentSection;
        },
        width: 'large',
        title: 'General',
    })
    componentBlock: ui.containers.Block;

    @ui.decorators.numericField<SalesOrderLineAssignmentDetailsPanel>({
        parent() {
            return this.componentBlock;
        },
        title: 'Required quantity',
        bind: 'quantityInStockUnit',
        isDisabled: true,
        scale() {
            return getScaleValue(2, this.itemStockUnit.decimalDigits);
        },
        postfix() {
            return this.itemStockUnit?.symbol ?? '';
        },
    })
    requiredQuantity: ui.fields.Numeric;

    @ui.decorators.numericField<SalesOrderLineAssignmentDetailsPanel>({
        parent() {
            return this.componentBlock;
        },
        title: 'Assigned quantity',
        isDisabled: true,
        scale() {
            return getScaleValue(2, this.itemStockUnit?.decimalDigits);
        },
        postfix() {
            return this.itemStockUnit?.symbol ?? '';
        },
    })
    assignedQuantity: ui.fields.Numeric;

    @ui.decorators.numericField<SalesOrderLineAssignmentDetailsPanel>({
        parent() {
            return this.componentBlock;
        },
        title: 'Remaining quantity',
        bind: 'quantityInStockUnit',
        isDisabled: true,
        scale() {
            return getScaleValue(2, this.itemStockUnit?.decimalDigits);
        },
        postfix() {
            return this.itemStockUnit?.symbol ?? '';
        },
    })
    remainingQuantity: ui.fields.Numeric;

    @ui.decorators.numericField<SalesOrderLineAssignmentDetailsPanel>({
        parent() {
            return this.componentBlock;
        },
        title: 'Remaining quantity to ship',
        bind: 'remainingQuantityToShipInStockUnit',
        isDisabled: true,
        scale() {
            return getScaleValue(2, this.itemStockUnit?.decimalDigits);
        },
        postfix() {
            return this.itemStockUnit?.symbol ?? '';
        },
    })
    remainingQuantityToShipInStockUnit: ui.fields.Numeric;

    @ui.decorators.tableField<SalesOrderLineAssignmentDetailsPanel, OrderAssignmentTable>({
        parent() {
            return this.componentSection;
        },
        title: 'Assigned orders',
        node: '@sage/xtrem-stock-data/OrderAssignment',
        pageSize: 10,
        canSelect: false,
        columns: [
            ui.nestedFields.select({
                title: 'Order assigned',
                bind: 'supplyType',
                optionType: '@sage/xtrem-stock-data/OrderToOrderSupplyType',
                isDisabled: true,
            }),
            ui.nestedFields.technical({
                bind: { supplyDocumentLine: { _id: true } },
                node: '@sage/xtrem-master-data/BaseDocumentLine',
            }),
            ui.nestedFields.link({
                title: 'Order',
                bind: 'supplyDocumentLine',
                width: 'large',
                isTransient: true,
                map(_fieldValue, rowData: OrderAssignmentTable) {
                    return `${rowData.supplyDocumentLine.documentNumber}`;
                },
                page(_value, rowData) {
                    const assignmentTableRowData = rowData as OrderAssignmentTable; // avoid compiler errors
                    const isWorkOrder = assignmentTableRowData.supplyType === 'workOrder';
                    return isWorkOrder ? `@sage/xtrem-manufacturing/WorkOrder` : `@sage/xtrem-purchasing/PurchaseOrder`;
                },
                queryParameters(_value, rowData) {
                    const assignmentTableRowData = rowData as OrderAssignmentTable; // avoid compiler errors
                    return {
                        _id: assignmentTableRowData.supplyDocumentLine.documentId || '',
                    };
                },
            }),
            ui.nestedFields.technical({
                bind: { supplyDocumentLine: { documentId: true } },
                node: '@sage/xtrem-master-data/BaseDocumentLine',
            }),
            ui.nestedFields.numeric({
                title: 'Quantity on order',
                isDisabled: true,
                bind: { supplyWorkInProgress: { expectedQuantity: true } },
                scale() {
                    return getScaleValue(2, this.itemStockUnit.decimalDigits);
                },
                postfix() {
                    return this.itemStockUnit?.symbol ?? '';
                },
            }),
            ui.nestedFields.numeric({
                title: 'Assigned quantity',
                bind: 'quantityInStockUnit',
                scale() {
                    return getScaleValue(2, this.itemStockUnit.decimalDigits);
                },
                postfix() {
                    return this.itemStockUnit?.symbol ?? '';
                },
                isDisabled() {
                    return this.demandOrderStatus === 'closed';
                },
                onChange(_rowId: string, rowData: ui.PartialCollectionValue<OrderAssignmentTable>) {
                    const { _id: itemId } = rowData;
                    const quantityInStockUnit = +(rowData.quantityInStockUnit ?? 0);
                    const expectedQuantity = +(rowData.supplyWorkInProgress?.expectedQuantity ?? 0);
                    const assignment = this.oldAssignedQuantities.find(({ _id }) => _id === itemId);
                    const originalQuantityInStockUnit = +(assignment?.originalQuantityInStockUnit ?? 0);

                    if (!assignment || quantityInStockUnit < 0) {
                        this.confirm.isDisabled = true;
                        return;
                    }
                    if (quantityInStockUnit > expectedQuantity || quantityInStockUnit > originalQuantityInStockUnit) {
                        this.confirm.isDisabled = true; // Disable saving due to error in quantity
                        this.assignmentLines.addOrUpdateRecordValue(rowData);
                        return;
                    }
                    rowData.quantityNotAssignedOnSupplyOrder = expectedQuantity - +(rowData.quantityInStockUnit ?? 0);
                    assignment.quantityInStockUnit = +(rowData.quantityInStockUnit ?? 0); // Set the oldAssignedQuantities to the updated value
                    rowData.action = 'update';
                    this.assignmentLines.addOrUpdateRecordValue(rowData); // Update the header quantities
                    this.updateHeaderQuantities();
                },
                validation(newValue: number, rowData: OrderAssignmentTable) {
                    const { _id: itemId } = rowData;
                    const quantityInStockUnit = +rowData.quantityInStockUnit;
                    const expectedQuantity = +(rowData.supplyWorkInProgress?.expectedQuantity ?? 0);
                    const assignment = this.oldAssignedQuantities.find(({ _id }) => _id === itemId);
                    const originalQuantityInStockUnit = +(assignment?.originalQuantityInStockUnit ?? 0);

                    if (quantityInStockUnit > expectedQuantity || quantityInStockUnit > originalQuantityInStockUnit) {
                        return newValue > originalQuantityInStockUnit
                            ? ui.localize(
                                  '@sage/xtrem-sales/pages__sales_order_line_assignment_details_panel__assigned_quantity_higher_than_quantity_in_stock_unit',
                                  'You have assigned more than the sales order quantity. You need to reduce the assigned quantity.',
                              )
                            : ui.localize(
                                  '@sage/xtrem-sales/pages__sales_order_line_assignment_details_panel__assigned_quantity_higher_than_quantity_on_order',
                                  'You have assigned more than the quantity on order. You need to reduce the assigned quantity.',
                              );
                    }
                    return undefined;
                },
            }),
            ui.nestedFields.technical({
                bind: 'action',
            }),
            ui.nestedFields.numeric({
                title: 'Quantity not assigned',
                bind: 'quantityNotAssignedOnSupplyOrder',
                isDisabled: true,
                scale() {
                    return getScaleValue(2, this.itemStockUnit.decimalDigits);
                },
                postfix() {
                    return this.itemStockUnit?.symbol ?? '';
                },
            }),
        ],
        dropdownActions: [
            {
                icon: 'bin',
                title: 'Delete',
                isDestructive: true,
                async onClick(assignmentLineId, assignmentLineData) {
                    if (
                        await this.$.dialog
                            .confirmation(
                                'warn',
                                ui.localize(
                                    '@sage/xtrem-sales/pages__sales_order_line_assignment_details_panel__line_deletion_dialog_title',
                                    'Delete assigned line',
                                ),
                                ui.localize(
                                    '@sage/xtrem-sales/pages__sales_order_line_assignment_details_panel__line_deletion_dialog_content',
                                    'You are about to delete the link to the order.',
                                ),
                                {
                                    acceptButton: {
                                        text: ui.localize(
                                            '@sage/xtrem-sales/pages__sales_order_line_assignment_details_panel__line-delete',
                                            'Delete',
                                        ),
                                    },
                                    cancelButton: {
                                        text: ui.localize(
                                            '@sage/xtrem-sales/pages__sales_order_line_assignment_details_panel__line-cancel',
                                            'Cancel',
                                        ),
                                    },
                                },
                            )
                            .then(() => true)
                            .catch(() => false)
                    ) {
                        this.supplyOrderIdsToDelete.push(assignmentLineId);
                        this.assignmentLines.removeRecord(assignmentLineId);
                        const index = this.oldAssignedQuantities.findIndex(obj => obj._id === assignmentLineData._id);
                        if (index >= 0) {
                            this.oldAssignedQuantities.splice(index, 1);
                        }
                        this.updateHeaderQuantities();
                        this.confirm.isDisabled = false;
                    }
                },
            },
        ],
    })
    assignmentLines: ui.fields.Table<OrderAssignmentTable>;

    private updateHeaderQuantities() {
        this.assignedQuantity.value = this.oldAssignedQuantities.reduce(
            (acc, val) => acc + val.quantityInStockUnit,
            0.0,
        );
        if (this.requiredQuantity.value) {
            this.remainingQuantity.value = this.requiredQuantity.value - this.assignedQuantity.value;
        }
        this.confirm.isDisabled = !this.$.isDirty;
    }
}
