import type { Logical } from '@sage/xtrem-client';
import { extractEdges } from '@sage/xtrem-client';
import { DateValue } from '@sage/xtrem-date-time';
import { Decimal } from '@sage/xtrem-decimal';
import type {
    Address,
    BusinessEntity,
    Currency,
    Customer,
    DeliveryMode,
    Incoterm,
    PaymentTerm,
} from '@sage/xtrem-master-data-api';
import { sales } from '@sage/xtrem-master-data/build/lib/menu-items/sales';
import { setReferenceIfSingleValue } from '@sage/xtrem-master-data/lib/client-functions/get-default';
import { getScaleValue } from '@sage/xtrem-master-data/lib/client-functions/utils';
import type { GraphApi, SalesOrder, SalesOrderLine } from '@sage/xtrem-sales-api';
import type { Dict } from '@sage/xtrem-shared';
import type { Country } from '@sage/xtrem-structure-api';
import type { ColoredElement, Company, Site } from '@sage/xtrem-system-api';
import { colorfulPillPattern } from '@sage/xtrem-system/build/lib/client-functions/color-pattern';
import * as ui from '@sage/xtrem-ui';
import { getCustomersId } from '../client-functions/sales-mass-process';
import {
    orderLineShippingFilterCriteria,
    orderShippingFilterCriteria,
} from '../shared-functions/order-line-shipping-filter-criteria';

@ui.decorators.page<SalesOrderShippingMassProcess>({
    title: 'Mass shipment creation',
    mode: 'default',
    isTransient: true,
    menuItem: sales,
    module: 'sales',
    skipDirtyCheck: true,
    priority: 225,
    access: { node: '@sage/xtrem-sales/SalesShipment', bind: '$create' },
    businessActions() {
        return [this.chooseOrders, this.cancelDetailedButton, this.createSalesShipmentsFromOrderLines];
    },
    async onLoad() {
        this.shippingUntilDate.value = DateValue.today().toString();
        if (this.$.detailPanel) {
            this.$.detailPanel.isHidden = true;
        }
        if (this.$.queryParameters && this.$.queryParameters.createShipments === 'Y') {
            const orders = JSON.parse(this.$.queryParameters.salesOrders as string);
            if (orders && orders.length > 0) {
                this.changeMainBlockVisibility(false);
                const filter = {
                    document: { _id: { _in: orders } },
                };
                const companyId = JSON.parse(this.$.queryParameters.salesCompany as string);
                const company = await this.$.graph
                    .node('@sage/xtrem-system/Company')
                    .read(
                        {
                            _id: true,
                            name: true,
                            id: true,
                            description: true,
                            currency: {
                                _id: true,
                                id: true,
                                name: true,
                                symbol: true,
                                decimalDigits: true,
                                rounding: true,
                            },
                        },
                        companyId,
                    )
                    .execute();
                this.salesCompany.value = company;
                await this.search(filter, false);
            }
        } else {
            await setReferenceIfSingleValue([this.salesCompany, this.stockSite]);
        }
        this.customersId = [];
    },
    detailPanel() {
        return {
            activeSection: 'detailPanelQuantitySection',
            header: this.detailPanelHeaderSection,
            sections: [this.detailPanelQuantitySection, this.detailPanelDeliverySection],
        };
    },
})
export class SalesOrderShippingMassProcess extends ui.Page<GraphApi> {
    customersId: string[];

    private stockAvailability: {
        [Key: string]: {
            availableQuantityInStockUnit: number;
            quantityAlreadyProposedInStockUnit: number;
            requiredQuantityInStockUnit: number;
            confirmedQuantityInStockUnit: number;
            sumAllocatedQuantityInStockUnit: number;
            shortageQuantityInStockUnit: number;
            oldRemainingQuantity: number;
        };
    } = {};

    private calculateOnSelect = true;

    quantityConfirmedClicked = false;

    @ui.decorators.section<SalesOrderShippingMassProcess>({
        isOpen: true,
        isTitleHidden: true,
        title: 'General',
    })
    mainSection: ui.containers.Section;

    // Here starts the detailPanel description
    @ui.decorators.section<SalesOrderShippingMassProcess>({ title: 'Sales order line detail panel' })
    detailPanelHeaderSection: ui.containers.Section;

    @ui.decorators.block<SalesOrderShippingMassProcess>({
        parent() {
            return this.detailPanelHeaderSection;
        },
        title: '',
    })
    detailPanelHeaderBlock: ui.containers.Block;

    @ui.decorators.cardField<SalesOrderShippingMassProcess, SalesOrderLine & { itemName: string }>({
        title: 'Sales order line',
        isFullWidth: true,
        isTitleHidden: true,
        parent() {
            return this.detailPanelHeaderBlock;
        },
        cardDefinition: {
            title: ui.nestedFields.text({ title: 'Item', isTransient: true, bind: 'itemName' }),
            image: ui.nestedFields.image({ bind: { item: { image: true } }, title: 'Item image' }),
        },
    })
    detailPanelSalesOrderLine: ui.fields.Card<SalesOrderLine>;

    @ui.decorators.section<SalesOrderShippingMassProcess>({ title: 'Quantities' })
    detailPanelQuantitySection: ui.containers.Section;

    @ui.decorators.gridRowBlock<SalesOrderShippingMassProcess>({
        parent() {
            return this.detailPanelQuantitySection;
        },
        title: 'Quantities',
        isTitleHidden: true,
        boundTo() {
            return this.results;
        },
        fieldFilter(columnId: string) {
            return [
                'stockAvailability',
                'quantityInStockUnit',
                'quantityConfirmedInStockUnit',
                'stockShortageInStockUnit',
                'availableQuantityInStockUnit',
            ].includes(columnId);
        },
    })
    detailPanelQuantityStockUnitBlock: ui.containers.GridRowBlock;

    @ui.decorators.section<SalesOrderShippingMassProcess>({ title: 'Delivery' })
    detailPanelDeliverySection: ui.containers.Section;

    @ui.decorators.gridRowBlock<SalesOrderShippingMassProcess>({
        parent() {
            return this.detailPanelDeliverySection;
        },
        title: 'Shipping',
        boundTo() {
            return this.results;
        },
        fieldFilter(columnId: string) {
            return ['soldToCustomerName', 'shippingDate', 'doNotShipBeforeDate', 'doNotShipAfterDate'].includes(
                columnId,
            );
        },
    })
    detailPanelDeliveryShippingSectionShippingBlock: ui.containers.GridRowBlock;

    @ui.decorators.vitalPodField<SalesOrderShippingMassProcess, Address>({
        parent() {
            return this.detailPanelDeliverySection;
        },
        node: '@sage/xtrem-master-data/Address',
        title: 'Ship-to address',
        width: 'large',
        onAddButtonClick() {
            return {};
        },
        dropdownActions: [],
        isReadOnly: true,
        columns: [
            ui.nestedFields.textArea({
                title: 'Bill-to address',
                isTitleHidden: true,
                bind: 'concatenatedAddress',
                width: 'large',
            }),
            ui.nestedFields.text({ bind: 'locationPhoneNumber', title: 'Phone number' }),
        ],
    })
    salesOrderLineShipToAddressDetailPanel: ui.fields.VitalPod;

    @ui.decorators.gridRowBlock<SalesOrderShippingMassProcess>({
        parent() {
            return this.detailPanelDeliverySection;
        },
        title: 'Delivery',
        boundTo() {
            return this.results;
        },
        fieldFilter(columnId: string) {
            return [
                'requestedDeliveryDate',
                'deliveryLeadTime',
                'expectedDeliveryDate',
                'deliveryMode',
                'incoterm',
            ].includes(columnId);
        },
    })
    detailPanelDeliveryDeliveryBlock: ui.containers.GridRowBlock;

    @ui.decorators.block<SalesOrderShippingMassProcess>({
        parent() {
            return this.mainSection;
        },
        width: 'extra-large',
        title: 'Selection criteria',
    })
    criteriaBlock: ui.containers.Block;

    @ui.decorators.block<SalesOrderShippingMassProcess>({
        parent() {
            return this.mainSection;
        },
        width: 'extra-large',
        title: 'Advanced selection',
        isHidden: true,
    })
    mainBlock: ui.containers.Block;

    @ui.decorators.pageAction<SalesOrderShippingMassProcess>({
        title: 'Create',
        isDisabled: true,
        async onClick() {
            const validation = await this.$.page.validate();
            if (validation.length) {
                this.$.showToast(validation.join('\n'), { type: 'error' });
            } else {
                try {
                    this.$.loader.isHidden = false;
                    let response: Dict<any> | null = null;
                    if (!this.mainBlock.isHidden) {
                        const lines: Array<{ salesDocumentLine: string; quantityToProcess: number }> = [];
                        const selectedRecords = this.results.selectedRecords.sort((a, b) => (+a > +b ? 1 : -1));
                        selectedRecords.forEach(_id => {
                            const rowData = this.results.getRecordValue(_id);
                            lines.push({
                                salesDocumentLine: `_id:${_id}`,
                                quantityToProcess: Number(
                                    +(rowData?.quantityConfirmedInStockUnit ?? 1) /
                                        +(rowData?.salesUnitToStockUnitConversionFactor ?? 1),
                                ),
                            });
                        });
                        response = await this.$.graph
                            .node('@sage/xtrem-sales/SalesOrder')
                            .mutations.createSalesShipmentsFromOrderLines(
                                {
                                    status: true,
                                    numberOfShipments: true,
                                    lineErrors: { lineNumber: true, linePosition: true, message: true },
                                },
                                { salesDocumentLines: lines },
                            )
                            .execute();
                    } else {
                        response = await this.$.graph
                            .node('@sage/xtrem-sales/SalesOrder')
                            .mutations.massCreateSalesShipments(
                                { numberOfShipments: true },
                                {
                                    stockSite: this.stockSite.value?._id || '',
                                    shippingUntilDate: this.shippingUntilDate.value,
                                    fromSoldToCustomer: this.fromSoldToCustomer.value?.businessEntity?.name ?? '',
                                    toSoldToCustomer: this.toSoldToCustomer.value?.businessEntity?.name ?? '',
                                    fromOrder: this.fromOrderNumber.value?._id ?? null,
                                    toOrder: this.toOrderNumber.value?._id ?? '',
                                    incoterm: this.incoterm.value?._id ?? null,
                                    deliveryMode: this.deliveryMode.value?._id ?? null,
                                },
                            )
                            .execute();
                    }

                    this.$.loader.isHidden = true;
                    if (response && response.numberOfShipments > 0) {
                        this.$.showToast(
                            ui.localize(
                                '@sage/xtrem-sales/pages__sales_order_shipping_mass_process__creation__success',
                                'Sales shipments created: {{num}}.',
                                { num: response.numberOfShipments },
                            ),
                            { timeout: 10000, type: 'success' },
                        );
                        this.$.setPageClean();
                        this.$.router.goTo('@sage/xtrem-sales/SalesOrderShippingMassProcess/');
                    } else {
                        this.$.showToast(
                            ui.localize(
                                '@sage/xtrem-sales/pages__sales_order_shipping_mass_process__no_shipment_meet_filtering_criteria',
                                'No results found.',
                            ),
                            { timeout: 10000, type: 'warning' },
                        );
                    }
                    this.$.finish();
                } catch (error) {
                    this.$.loader.isHidden = true;
                    this.$.showToast(error.message, { timeout: 10000, type: 'error' });
                }
            }
        },
        buttonType: 'primary',
    })
    createSalesShipmentsFromOrderLines: ui.PageAction;

    @ui.decorators.pageAction<SalesOrderShippingMassProcess>({
        title: 'Show advanced selection',
        isDisabled: true,
        async onClick() {
            this.$.loader.isHidden = false;
            this.changeMainBlockVisibility(false);
            await this.search(
                orderLineShippingFilterCriteria(
                    this.stockSite.value?._id || '',
                    DateValue.today().toString(),
                    this.shippingUntilDate.value,
                    this.fromSoldToCustomer.value?.businessEntity?.name,
                    this.toSoldToCustomer.value?.businessEntity?.name,
                    this.incoterm.value?._id,
                    this.deliveryMode.value?._id,
                    this.fromOrderNumber.value?.number,
                    this.toOrderNumber.value?.number,
                ),
                true,
            );
            await this.onSelectCalculation();
            setTimeout(() => {
                this.results.value
                    .filter(row => row.quantityConfirmedInStockUnit > 0)
                    .forEach(row => this.results.selectRecord(row._id));
                this.controlSelectionState();
            }, 100);
            this.$.loader.isHidden = true;
        },
    })
    chooseOrders: ui.PageAction;

    @ui.decorators.pageAction<SalesOrderShippingMassProcess>({
        title: 'Hide advanced selection',
        isHidden: true,
        onClick() {
            this.changeMainBlockVisibility(true);
            this.calculateOnSelect = false;
            this.results.unselectAllRecords();
            this.calculateOnSelect = true;
            this.results.value = [];
            this.revenue.value = 0;
            this.customerSatisfaction.value = '';
            this.selectAllCheckbox.value = false;
        },
    })
    cancelDetailedButton: ui.PageAction;

    @ui.decorators.referenceField<SalesOrderShippingMassProcess, Company>({
        parent() {
            return this.criteriaBlock;
        },
        title: 'Company',
        lookupDialogTitle: 'Select company',
        node: '@sage/xtrem-system/Company',
        tunnelPage: '@sage/xtrem-master-data/Company',
        width: 'small',
        columns: [
            ui.nestedFields.text({ bind: 'name' }),
            ui.nestedFields.text({ bind: 'id' }),
            ui.nestedFields.technical({ bind: 'description' }),
            ui.nestedFields.technical<SalesOrderShippingMassProcess, Company, Currency>({
                bind: 'currency',
                node: '@sage/xtrem-master-data/Currency',
                nestedFields: [
                    ui.nestedFields.technical({ bind: 'name' }),
                    ui.nestedFields.technical({ bind: 'id' }),
                    ui.nestedFields.technical({ bind: 'symbol' }),
                    ui.nestedFields.technical({ bind: 'decimalDigits' }),
                    ui.nestedFields.technical({ bind: 'rounding' }),
                ],
            }),
        ],
        filter() {
            return this.stockSite.value
                ? { sites: { _atLeast: 1, name: this.stockSite.value.name } }
                : { sites: { _atLeast: 1, isInventory: true } };
        },
        onChange() {
            this.updateChooseOrdersStatus();
        },
        isAutoSelectEnabled: true,
        valueField: 'name',
        helperTextField: 'id',
        minLookupCharacters: 0,
        isMandatory: true,
        placeholder: 'Select...',
    })
    salesCompany: ui.fields.Reference<Company>;

    @ui.decorators.referenceField<SalesOrderShippingMassProcess, Site>({
        parent() {
            return this.criteriaBlock;
        },
        title: 'Stock site',
        lookupDialogTitle: 'Select stock site',
        node: '@sage/xtrem-system/Site',
        tunnelPage: '@sage/xtrem-master-data/Site',
        valueField: 'name',
        helperTextField: 'id',
        minLookupCharacters: 0,
        isAutoSelectEnabled: true,
        isMandatory: true,
        columns: [
            ui.nestedFields.text({ bind: 'name' }),
            ui.nestedFields.text({ bind: 'id' }),
            ui.nestedFields.reference<SalesOrderShippingMassProcess, Site, Company>({
                node: '@sage/xtrem-system/Company',
                tunnelPage: '@sage/xtrem-master-data/Company',
                title: 'Company',
                bind: 'legalCompany',
                valueField: 'name',
                helperTextField: 'id',
                columns: [
                    ui.nestedFields.text({ bind: 'name' }),
                    ui.nestedFields.text({ bind: 'id' }),
                    ui.nestedFields.technical({
                        bind: 'currency',
                        node: '@sage/xtrem-master-data/Currency',
                        nestedFields: [
                            ui.nestedFields.technical({ bind: 'name' }),
                            ui.nestedFields.technical({ bind: 'id' }),
                            ui.nestedFields.technical({ bind: 'symbol' }),
                            ui.nestedFields.technical({ bind: 'decimalDigits' }),
                            ui.nestedFields.technical({ bind: 'rounding' }),
                        ],
                    }),
                ],
            }),
            ui.nestedFields.technical({ bind: 'description' }),
            ui.nestedFields.technical({ bind: 'isInventory' }),

            ui.nestedFields.reference({
                node: '@sage/xtrem-system/Site',
                valueField: '_id',
                bind: 'financialSite',
                isHidden: true,
                columns: [ui.nestedFields.text({ bind: 'id' })],
            }),
            ui.nestedFields.technical({ bind: 'isFinance' }),
        ],
        placeholder: 'Select...',
        width: 'small',
        async onChange() {
            if (!this.salesCompany.value && this.stockSite.value) {
                this.salesCompany.value = this.stockSite.value.legalCompany ?? null;
            }
            if (this.fromOrderNumber.value) {
                this.fromOrderNumber.value = null;
            }

            if (this.toOrderNumber.value) {
                this.toOrderNumber.value = null;
            }

            if (this.stockSite.value) {
                this.fromOrderNumber.isDisabled = false;
                this.toOrderNumber.isDisabled = false;
            } else {
                this.fromOrderNumber.isDisabled = true;
                this.toOrderNumber.isDisabled = true;
            }
            this.updateChooseOrdersStatus();
            this.customersId = await getCustomersId(this, this.stockSite.value?.legalCompany?._id || null);
        },
        filter() {
            return this.salesCompany.value
                ? { isInventory: true, legalCompany: { name: this.salesCompany.value.name } }
                : { isInventory: true };
        },
    })
    stockSite: ui.fields.Reference<Site>;

    @ui.decorators.dateField<SalesOrderShippingMassProcess>({
        parent() {
            return this.criteriaBlock;
        },
        title: 'Maximum shipping date',
    })
    shippingUntilDate: ui.fields.Date;

    @ui.decorators.referenceField<SalesOrderShippingMassProcess, Customer>({
        node: '@sage/xtrem-master-data/Customer',
        parent() {
            return this.criteriaBlock;
        },
        title: 'From sold-to customer',
        lookupDialogTitle: 'Select from sold-to customer',
        minLookupCharacters: 0,
        isAutoSelectEnabled: true,
        shouldSuggestionsIncludeColumns: true,
        columns: [
            ui.nestedFields.text({ bind: { businessEntity: { name: true } }, title: 'Name' }),
            ui.nestedFields.text({ bind: { businessEntity: { id: true } }, title: 'ID' }),
            ui.nestedFields.text({ bind: { businessEntity: { taxIdNumber: true } }, title: 'Tax ID' }),
            ui.nestedFields.text({ bind: { businessEntity: { country: { name: true } } }, title: 'Country' }),
            ui.nestedFields.technical<SalesOrderShippingMassProcess, Customer, BusinessEntity>({
                bind: 'businessEntity',
                node: '@sage/xtrem-master-data/BusinessEntity',
                nestedFields: [
                    ui.nestedFields.technical({ bind: 'name' }),
                    ui.nestedFields.technical({ bind: 'id' }),
                    ui.nestedFields.technical<SalesOrderShippingMassProcess, BusinessEntity, Country>({
                        bind: 'country',
                        node: '@sage/xtrem-structure/Country',
                        nestedFields: [
                            ui.nestedFields.technical({ bind: 'name' }),
                            ui.nestedFields.technical({ bind: 'id' }),
                        ],
                    }),
                    ui.nestedFields.technical<SalesOrderShippingMassProcess, BusinessEntity, Currency>({
                        bind: 'currency',
                        node: '@sage/xtrem-master-data/Currency',
                        nestedFields: [
                            ui.nestedFields.technical({ bind: 'name' }),
                            ui.nestedFields.technical({ bind: 'id' }),
                            ui.nestedFields.technical({ bind: 'symbol' }),
                            ui.nestedFields.technical({ bind: 'decimalDigits' }),
                            ui.nestedFields.technical({ bind: 'rounding' }),
                        ],
                    }),
                ],
            }),
            ui.nestedFields.technical({ bind: 'isOnHold' }),
            ui.nestedFields.technical<SalesOrderShippingMassProcess, Customer, PaymentTerm>({
                bind: 'paymentTerm',
                node: '@sage/xtrem-master-data/PaymentTerm',
                nestedFields: [ui.nestedFields.technical({ bind: 'name' }), ui.nestedFields.technical({ bind: '_id' })],
            }),
        ],
        filter() {
            return { _id: { _in: this.customersId } };
        },
    })
    fromSoldToCustomer: ui.fields.Reference<Customer>;

    @ui.decorators.referenceField<SalesOrderShippingMassProcess, Customer>({
        node: '@sage/xtrem-master-data/Customer',
        parent() {
            return this.criteriaBlock;
        },
        title: 'To sold-to customer',
        lookupDialogTitle: 'Select to sold-to customer',
        minLookupCharacters: 0,
        isAutoSelectEnabled: true,
        shouldSuggestionsIncludeColumns: true,
        columns: [
            ui.nestedFields.text({ bind: { businessEntity: { name: true } }, title: 'Name' }),
            ui.nestedFields.text({ bind: { businessEntity: { id: true } }, title: 'ID' }),
            ui.nestedFields.text({ bind: { businessEntity: { taxIdNumber: true } }, title: 'Tax ID' }),
            ui.nestedFields.technical({ bind: 'isOnHold' }),
            ui.nestedFields.technical<SalesOrderShippingMassProcess, Customer, BusinessEntity>({
                bind: 'businessEntity',
                node: '@sage/xtrem-master-data/BusinessEntity',
                nestedFields: [
                    ui.nestedFields.technical({ bind: 'name' }),
                    ui.nestedFields.technical({ bind: 'id' }),
                    ui.nestedFields.technical<SalesOrderShippingMassProcess, BusinessEntity, Country>({
                        bind: 'country',
                        node: '@sage/xtrem-structure/Country',
                        nestedFields: [
                            ui.nestedFields.technical({ bind: 'name' }),
                            ui.nestedFields.technical({ bind: 'id' }),
                        ],
                    }),
                    ui.nestedFields.technical<SalesOrderShippingMassProcess, BusinessEntity, Currency>({
                        bind: 'currency',
                        node: '@sage/xtrem-master-data/Currency',
                        nestedFields: [
                            ui.nestedFields.technical({ bind: 'name' }),
                            ui.nestedFields.technical({ bind: 'id' }),
                            ui.nestedFields.technical({ bind: 'symbol' }),
                            ui.nestedFields.technical({ bind: 'decimalDigits' }),
                            ui.nestedFields.technical({ bind: 'rounding' }),
                        ],
                    }),
                ],
            }),
            ui.nestedFields.technical<SalesOrderShippingMassProcess, Customer, PaymentTerm>({
                bind: 'paymentTerm',
                node: '@sage/xtrem-master-data/PaymentTerm',
                nestedFields: [ui.nestedFields.technical({ bind: 'name' }), ui.nestedFields.technical({ bind: '_id' })],
            }),
        ],
        filter() {
            return { _id: { _in: this.customersId } };
        },
    })
    toSoldToCustomer: ui.fields.Reference<Customer>;

    @ui.decorators.referenceField<SalesOrderShippingMassProcess, Incoterm>({
        parent() {
            return this.criteriaBlock;
        },
        node: '@sage/xtrem-master-data/Incoterm',
        tunnelPage: '@sage/xtrem-master-data/Incoterm',
        title: 'Incoterms® rule',
        lookupDialogTitle: 'Select incoterms',
        valueField: 'name',
        helperTextField: 'id',
        minLookupCharacters: 0,
        isAutoSelectEnabled: true,
        columns: [
            ui.nestedFields.text({ bind: 'name' }),
            ui.nestedFields.text({ bind: 'id' }),
            ui.nestedFields.technical({ bind: 'description' }),
        ],
    })
    incoterm: ui.fields.Reference<Incoterm>;

    @ui.decorators.referenceField<SalesOrderShippingMassProcess, DeliveryMode>({
        parent() {
            return this.criteriaBlock;
        },
        node: '@sage/xtrem-master-data/DeliveryMode',
        tunnelPage: '@sage/xtrem-master-data/DeliveryMode',
        title: 'Delivery mode',
        lookupDialogTitle: 'Select delivery mode',
        valueField: 'name',
        minLookupCharacters: 0,
        isAutoSelectEnabled: true,
        columns: [ui.nestedFields.text({ bind: 'name' }), ui.nestedFields.technical({ bind: 'description' })],
    })
    deliveryMode: ui.fields.Reference<DeliveryMode>;

    @ui.decorators.referenceField<SalesOrderShippingMassProcess, SalesOrder>({
        node: '@sage/xtrem-sales/SalesOrder',
        parent() {
            return this.criteriaBlock;
        },
        title: 'From order number',
        lookupDialogTitle: 'Select from order number',
        valueField: 'number',
        minLookupCharacters: 0,
        isDisabled: true,
        isAutoSelectEnabled: true,
        columns: [
            ui.nestedFields.text({ bind: 'number', title: 'Order number' }),
            ui.nestedFields.date({ bind: 'date', title: 'Order date' }),
            ui.nestedFields.reference<SalesOrderShippingMassProcess, SalesOrder, Customer>({
                title: 'Sold-to customer',
                bind: 'soldToCustomer',
                tunnelPage: '@sage/xtrem-master-data/Customer',
            }),
        ],
        filter() {
            if (this.stockSite.value?._id) {
                return orderShippingFilterCriteria(
                    this.stockSite.value._id,
                    this.shippingUntilDate.value,
                    this.fromSoldToCustomer.value?.businessEntity?.name,
                    this.toSoldToCustomer.value?.businessEntity?.name,
                    this.incoterm.value?._id,
                    this.deliveryMode.value?._id,
                ) as Logical<SalesOrder>;
            }
            return {};
        },
    })
    fromOrderNumber: ui.fields.Reference;

    @ui.decorators.referenceField<SalesOrderShippingMassProcess, SalesOrder>({
        node: '@sage/xtrem-sales/SalesOrder',
        parent() {
            return this.criteriaBlock;
        },
        title: 'To order number',
        lookupDialogTitle: 'Select to order number',
        valueField: 'number',
        minLookupCharacters: 0,
        isDisabled: true,
        isAutoSelectEnabled: true,
        columns: [
            ui.nestedFields.text({ bind: 'number', title: 'Order number' }),
            ui.nestedFields.date({ bind: 'date', title: 'Order date' }),
            ui.nestedFields.reference<SalesOrderShippingMassProcess, SalesOrder, Customer>({
                title: 'Sold-to customer',
                bind: 'soldToCustomer',
                tunnelPage: '@sage/xtrem-master-data/Customer',
            }),
        ],
        filter() {
            if (this.stockSite.value?._id) {
                return orderShippingFilterCriteria(
                    this.stockSite.value._id,
                    this.shippingUntilDate.value,
                    this.fromSoldToCustomer.value?.businessEntity?.name,
                    this.toSoldToCustomer.value?.businessEntity?.name,
                    this.incoterm.value?._id,
                    this.deliveryMode.value?._id,
                ) as Logical<SalesOrder>;
            }
            return {};
        },
    })
    toOrderNumber: ui.fields.Reference<SalesOrder>;

    @ui.decorators.checkboxField<SalesOrderShippingMassProcess>({
        parent() {
            return this.mainBlock;
        },
        title: 'Select all',
        async onChange() {
            await this.selectUnselectCheckboxes();
        },
    })
    selectAllCheckbox: ui.fields.Checkbox;

    @ui.decorators.separatorField<SalesOrderShippingMassProcess>({
        parent() {
            return this.mainBlock;
        },
        isInvisible: true,
        isFullWidth: true,
    })
    separator: ui.fields.Separator;

    @ui.decorators.textField<SalesOrderShippingMassProcess>({
        parent() {
            return this.mainBlock;
        },
        title: 'Customer satisfaction',
        isReadOnly: true,
    })
    customerSatisfaction: ui.fields.Text;

    @ui.decorators.numericField<SalesOrderShippingMassProcess>({
        parent() {
            return this.mainBlock;
        },
        title: 'Shipment amount including tax',
        unit() {
            return this.salesCompany.value;
        },
        scale: null,
        isReadOnly: true,
    })
    revenue: ui.fields.Numeric;

    @ui.decorators.tableField<SalesOrderShippingMassProcess>({
        pageSize: 500,
        async onRowSelected(recordId, rowItem) {
            this.controlSelectionState();

            const {
                quantityInStockUnit,
                uAssignmentOrder,
                suppliedQuantity,
                assignedQuantity,
                remainingQuantityToShipInSalesUnit,
                quantityAllocated,
            } = rowItem;

            if (this.calculateOnSelect) {
                const quantityConfirmedInStockUnit = uAssignmentOrder
                    ? Math.min(
                          +suppliedQuantity,
                          +assignedQuantity,
                          +remainingQuantityToShipInSalesUnit,
                          +quantityAllocated,
                      )
                    : quantityInStockUnit;
                await this.updateRowValuesAfterConfirmedQuantityChange(+quantityConfirmedInStockUnit, rowItem);
            }
        },
        async onRowUnselected(recordId, rowItem) {
            this.controlSelectionState();
            if (this.calculateOnSelect) {
                await this.updateRowValuesAfterConfirmedQuantityChange(0, rowItem);
            }
        },
        orderBy: { shippingDateOrderBy: +1, number: +1, itemName: +1 },
        columns: [
            ui.nestedFields.technical<SalesOrderShippingMassProcess>({ bind: 'shippingDateOrderBy' }),
            ui.nestedFields.technical<SalesOrderShippingMassProcess>({ bind: '_id' }),
            ui.nestedFields.text<SalesOrderShippingMassProcess>({
                title: 'Order number',
                bind: 'number',
                isReadOnly: true,
            }),
            ui.nestedFields.text<SalesOrderShippingMassProcess>({
                title: 'Sold-to customer',
                bind: 'soldToCustomerName',
                isReadOnly: true,
            }),
            ui.nestedFields.technical<SalesOrderShippingMassProcess>({
                bind: 'soldToCustomer',
                node: '@sage/xtrem-master-data/Customer',
                nestedFields: [
                    ui.nestedFields.technical({ bind: 'name' }),
                    ui.nestedFields.technical({ bind: 'id' }),
                    ui.nestedFields.technical({
                        bind: 'businessEntity',
                        node: '@sage/xtrem-master-data/BusinessEntity',
                        nestedFields: [
                            ui.nestedFields.technical({ bind: 'name' }),
                            ui.nestedFields.technical({ bind: 'id' }),
                        ],
                    }),
                ],
            }),
            ui.nestedFields.text({ title: 'Item', bind: { item: { name: true } }, isReadOnly: true }),
            ui.nestedFields.text({
                title: 'Item ID',
                bind: { item: { id: true } },
                isReadOnly: true,
            }),
            ui.nestedFields.text({
                title: 'Item description',
                bind: 'itemDescription',
                isReadOnly: true,
                isHiddenOnMainField: true,
            }),
            ui.nestedFields.image({
                title: 'Image',
                isTitleHidden: true,
                bind: { item: { image: true } },
                width: 'small',
                height: '40px',
                isReadOnly: true,
                isExcludedFromMainField: true,
            }),
            ui.nestedFields.date<SalesOrderShippingMassProcess>({
                bind: 'shippingDate',
                title: 'Shipping date',
                isReadOnly: true,
            }),
            ui.nestedFields.date({
                title: 'Do-not-ship-before date',
                bind: 'doNotShipBeforeDate',
                isReadOnly: true,
                isExcludedFromMainField: true,
                width: 'large',
            }),
            ui.nestedFields.date({
                title: 'Do-not-ship-after date',
                bind: 'doNotShipAfterDate',
                isReadOnly: true,
                isExcludedFromMainField: true,
                width: 'large',
            }),
            ui.nestedFields.technical<SalesOrderShippingMassProcess>({
                bind: 'stockUnit',
                node: '@sage/xtrem-master-data/UnitOfMeasure',
                nestedFields: [
                    ui.nestedFields.technical({ bind: 'name' }),
                    ui.nestedFields.technical({ bind: 'id' }),
                    ui.nestedFields.technical({ bind: 'symbol' }),
                    ui.nestedFields.technical({ bind: 'decimalDigits' }),
                ],
            }),
            ui.nestedFields.technical<SalesOrderShippingMassProcess>({ bind: 'stockAvailabilityStatus' }),
            ui.nestedFields.label<SalesOrderShippingMassProcess>({
                title: 'Stock availability',
                bind: 'stockAvailability',
                backgroundColor: (_value, rowData) =>
                    SalesOrderShippingMassProcess.stockAvailabilityColor(
                        'backgroundColor',
                        rowData?.stockAvailabilityStatus,
                    ),
                borderColor: (_value, rowData) =>
                    SalesOrderShippingMassProcess.stockAvailabilityColor(
                        'borderColor',
                        rowData?.stockAvailabilityStatus,
                    ),
                color: (_value, rowData) =>
                    SalesOrderShippingMassProcess.stockAvailabilityColor('textColor', rowData?.stockAvailabilityStatus),
                width: 'large',
            }),
            ui.nestedFields.numeric<SalesOrderShippingMassProcess>({
                title: 'Qty. to ship',
                bind: 'quantityInStockUnit',
                isReadOnly: true,
                unit: (_id, rowData) => rowData?.stockUnit,
                unitMode: 'unitOfMeasure',
            }),
            ui.nestedFields.numeric<SalesOrderShippingMassProcess>({
                title: 'Available stock',
                bind: 'availableQuantityInStockUnit',
                isReadOnly: true,
                unit: (_id, rowData) => rowData?.stockUnit,
                unitMode: 'unitOfMeasure',
            }),
            ui.nestedFields.numeric<SalesOrderShippingMassProcess>({
                title: 'Allocated quantity',
                bind: 'quantityAllocated',
                isReadOnly: true,
                scale: (_value, rowData) => getScaleValue(0, rowData?.stockUnit?.decimalDigits),
                unit: (_id, rowData) => rowData?.stockUnit,
                unitMode: 'unitOfMeasure',
            }),
            ui.nestedFields.numeric<SalesOrderShippingMassProcess>({
                title: 'Shortage',
                bind: 'stockShortageInStockUnit',
                isReadOnly: true,
                unit: (_id, rowData) => rowData?.stockUnit,
                unitMode: 'unitOfMeasure',
            }),
            ui.nestedFields.numeric<SalesOrderShippingMassProcess>({
                title: 'Remaining stock',
                bind: 'remainingQuantityInStockUnit',
                isReadOnly: true,
                unit: (_id, rowData) => rowData?.stockUnit,
                unitMode: 'unitOfMeasure',
            }),
            ui.nestedFields.numeric<SalesOrderShippingMassProcess>({
                title: 'Total demand',
                bind: 'totalQuantityOnDemand',
                isReadOnly: true,
                unit: (_id, rowData) => rowData?.stockUnit,
                unitMode: 'unitOfMeasure',
            }),
            ui.nestedFields.technical({ bind: 'oldQuantityConfirmedInStockUnit' }),
            ui.nestedFields.numeric({
                title: 'Confirmed qty.',
                bind: 'quantityConfirmedInStockUnit',
                unit: (_id, rowData) => rowData?.stockUnit,
                unitMode: 'unitOfMeasure',
                validation(val, rowData) {
                    const decimalDigits = rowData?.stockUnit?.decimalDigits || 0;
                    const roundedVal = Decimal.make(val).toDP(decimalDigits).toNumber();
                    const quantityInStockUnit = Decimal.make(rowData.quantityInStockUnit)
                        .toDP(decimalDigits)
                        .toNumber();
                    if (roundedVal > quantityInStockUnit) {
                        return ui.localize(
                            '@sage/xtrem-sales/pages__sales_order_shipping_mass_process__quantityConfirmedInStockUnit_error',
                            'The confirmed quantity in stock unit ({{value}}) cannot be greater than the quantity to ship in stock unit ({{quantityInStockUnit}}).',
                            {
                                value: roundedVal,
                                quantityInStockUnit,
                            },
                        );
                    }
                    if (roundedVal < 0) {
                        return ui.localize(
                            '@sage/xtrem-sales/pages__pages__sales_order_shipping_mass_process__quantity_confirmed_in_stock_unit_negative_value',
                            'Enter a positive value for the confirmed quantity in stock unit.',
                        );
                    }
                    if (
                        !Decimal.mod(roundedVal, rowData.salesUnitToStockUnitConversionFactor).eq(0) &&
                        Decimal.make(roundedVal).greaterThan(quantityInStockUnit)
                    ) {
                        return ui.localize(
                            '@sage/xtrem-sales/pages__pages__sales_order_shipping_mass_process__quantity_confirmed_multiplication_of_stock_unit_conversion_factor',
                            'The confirmed quantity in the stock unit needs to be equal to the quantity in the sales unit multiplied by the conversion factor.',
                        );
                    }
                    return undefined;
                },
                async onChange(_value, rowData) {
                    const { quantityConfirmedInStockUnit, uAssignmentOrder, quantityAllocated, suppliedQuantity, _id } =
                        rowData;
                    const isQuantityAboveAllocationThreshold =
                        uAssignmentOrder && quantityConfirmedInStockUnit > quantityAllocated;
                    const isQuantityAboveSuppliedThreshold = quantityConfirmedInStockUnit > suppliedQuantity;
                    const dialogTitle = isQuantityAboveAllocationThreshold
                        ? ui.localize(
                              '@sage/xtrem-sales/pages_confirm_assignment_sales_order_line_ship_remaining_quantity_not_allocated_dialog_title',
                              'Ship sales order line',
                          )
                        : '';
                    const dialogContent = isQuantityAboveSuppliedThreshold
                        ? ui.localize(
                              '@sage/xtrem-sales/pages_confirm_assignment_sales_order_line_ship_required_quantity_is_not_received',
                              'This sales order line is linked to a supply order. The required quantity is not received.',
                          )
                        : ui.localize(
                              '@sage/xtrem-sales/pages_confirm_assignment_sales_order_line_ship_remaining_quantity_not_allocated_dialog_content',
                              'This sales order line is linked to a released supply order. The remaining quantity to ship is not allocated.',
                          );

                    const shouldProceed =
                        uAssignmentOrder && dialogTitle && dialogContent
                            ? Boolean(
                                  await this.$.dialog
                                      .confirmation('warn', dialogTitle, dialogContent, {
                                          acceptButton: {
                                              text: ui.localize(
                                                  '@sage/xtrem-sales/pages__sales_order_shipping_mass_process__continue',
                                                  'Continue',
                                              ),
                                          },
                                          cancelButton: {
                                              text: ui.localize(
                                                  '@sage/xtrem-sales/pages__sales_order_shipping_mass_process__cancel',
                                                  'Cancel',
                                              ),
                                          },
                                      })
                                      .then(() => true)
                                      .catch(() => false),
                              )
                            : true;

                    if (!shouldProceed) {
                        rowData.quantityConfirmedInStockUnit = rowData.oldQuantityConfirmedInStockUnit;
                        this.results.addOrUpdateRecordValue(rowData);
                        return;
                    }
                    if (shouldProceed) {
                        const confirmedQuantity = quantityConfirmedInStockUnit ?? 0;
                        await this.updateRowValuesAfterConfirmedQuantityChange(confirmedQuantity, rowData);
                        if (confirmedQuantity <= 0) {
                            this.results.unselectRecord(_id);
                        } else {
                            this.results.selectRecord(_id);
                        }
                        this.controlSelectionState();
                    }
                },
                onClick() {
                    this.quantityConfirmedClicked = true;
                },
            }),
            ui.nestedFields.technical({ bind: 'uAssignmentOrder' }),
            ui.nestedFields.technical({ bind: 'suppliedQuantity' }),
            ui.nestedFields.technical({ bind: 'salesUnitToStockUnitConversionFactor' }),
            ui.nestedFields.technical<SalesOrderShippingMassProcess>({
                bind: 'currency',
                node: '@sage/xtrem-master-data/Currency',
                nestedFields: [
                    ui.nestedFields.technical({ bind: 'name' }),
                    ui.nestedFields.technical({ bind: 'id' }),
                    ui.nestedFields.technical({ bind: 'symbol' }),
                    ui.nestedFields.technical({ bind: 'decimalDigits' }),
                    ui.nestedFields.technical({ bind: 'rounding' }),
                ],
            }),
            ui.nestedFields.technical<SalesOrderShippingMassProcess>({ bind: 'netPrice' }),
            ui.nestedFields.technical<SalesOrderShippingMassProcess>({ bind: 'netPriceIncludingTax' }),
            ui.nestedFields.numeric<SalesOrderShippingMassProcess>({
                title: 'Amount excl. tax',
                bind: 'amountExcludingTax',
                prefix: (_rowId, rowData) => rowData?.currency?.symbol || '',
                isReadOnly: true,
            }),
            ui.nestedFields.numeric<SalesOrderShippingMassProcess>({
                title: 'Amount incl. tax',
                bind: 'amountIncludingTax',
                prefix: (_rowId, rowData) => rowData?.currency?.symbol || '',
                isReadOnly: true,
            }),
            ui.nestedFields.technical<SalesOrderShippingMassProcess>({
                bind: 'shipToAddress',
                node: '@sage/xtrem-master-data/Address',
                nestedFields: [
                    ui.nestedFields.text({ title: 'ID', bind: '_id' }),
                    ui.nestedFields.text({ title: 'Name', bind: 'name' }),
                    ui.nestedFields.text({ title: 'Phone number', bind: 'locationPhoneNumber' }),
                    ui.nestedFields.textArea({
                        bind: 'concatenatedAddress',
                        isTitleHidden: true,
                        title: 'Bill-to address',
                    }),
                ],
            }),
            ui.nestedFields.date({
                title: 'Requested delivery date',
                bind: 'requestedDeliveryDate',
                isReadOnly: true,
                isExcludedFromMainField: true,
                width: 'large',
            }),
            ui.nestedFields.numeric({
                title: 'Delivery lead time',
                bind: 'deliveryLeadTime',
                isReadOnly: true,
                isExcludedFromMainField: true,
                width: 'large',
                prefix: 'Number of days: ',
            }),
            ui.nestedFields.date({
                title: 'Expected delivery date',
                bind: 'expectedDeliveryDate',
                isReadOnly: true,
                isExcludedFromMainField: true,
                width: 'large',
            }),
            ui.nestedFields.reference<SalesOrderShippingMassProcess>({
                title: 'Delivery mode',
                bind: 'deliveryMode',
                node: '@sage/xtrem-master-data/DeliveryMode',
                tunnelPage: '@sage/xtrem-master-data/DeliveryMode',
                valueField: 'name',
                isExcludedFromMainField: true,
                isReadOnly: true,
                columns: [ui.nestedFields.text({ bind: 'name' }), ui.nestedFields.technical({ bind: 'description' })],
            }),
            ui.nestedFields.reference<SalesOrderShippingMassProcess>({
                bind: 'incoterm',
                title: 'Incoterms® rule',
                node: '@sage/xtrem-master-data/Incoterm',
                tunnelPage: '@sage/xtrem-master-data/Incoterm',
                valueField: 'name',
                isReadOnly: true,
                isExcludedFromMainField: true,
                helperTextField: 'id',
                columns: [ui.nestedFields.text({ bind: 'name' }), ui.nestedFields.text({ bind: 'id' })],
            }),
        ],
        onRowClick(rowId: string, rowItem) {
            if (!this.quantityConfirmedClicked) {
                if (this.$.detailPanel) {
                    this.$.detailPanel.isHidden = false;
                }

                // This manages the blocks display of the header section
                this.detailPanelSalesOrderLine.value = rowItem;
                this.detailPanelHeaderBlock.title = rowItem.item.id;
                this.detailPanelHeaderBlock.isHidden = false;

                // This manages the blocks display of the "Quantity" tab
                this.detailPanelQuantityStockUnitBlock.selectedRecordId = rowId;
                this.detailPanelQuantityStockUnitBlock.isHidden = false;

                // This manages the blocks display of the "Delivery" tab
                this.detailPanelDeliveryShippingSectionShippingBlock.selectedRecordId = rowId;
                this.detailPanelDeliveryDeliveryBlock.selectedRecordId = rowId;
                this.salesOrderLineShipToAddressDetailPanel.value = rowItem.shipToAddress;
                this.detailPanelDeliveryShippingSectionShippingBlock.isHidden = false;
                this.detailPanelDeliveryDeliveryBlock.isHidden = false;
            }
            this.quantityConfirmedClicked = false;
        },
        parent() {
            return this.mainSection;
        },
        title: 'Results',
        isTitleHidden: true,
        isHidden: true,
    })
    results: ui.fields.Table;

    async search(filter: any, validate: boolean) {
        let validation: string[] = [];
        if (validate) {
            validation = await this.$.page.validate();
        }
        if (validation.length) {
            this.$.showToast(validation.join('\n'), { type: 'error' });
        } else {
            this.results.value = [];
            this.revenue.value = 0;
            this.customerSatisfaction.value = '';
            this.stockAvailability = {};
            this.createSalesShipmentsFromOrderLines.isDisabled = true;

            this.$.loader.isHidden = false;

            const results = extractEdges(
                await this.$.graph
                    .node('@sage/xtrem-sales/SalesOrderLine')
                    .query(
                        ui.queryUtils.edgesSelector(
                            {
                                document: {
                                    number: true,
                                    soldToCustomer: {
                                        isOnHold: true,
                                        businessEntity: { name: true, id: true },
                                        id: true,
                                    },
                                    incoterm: { name: true, id: true, _id: true },
                                    currency: {
                                        _id: true,
                                        id: true,
                                        decimalDigits: true,
                                        rounding: true,
                                        symbol: true,
                                    },
                                },
                                item: {
                                    id: true,
                                    name: true,
                                    _id: true,
                                    isStockManaged: true,
                                    image: { value: true },
                                },
                                _id: true,
                                itemDescription: true,
                                shippingDate: true,
                                doNotShipBeforeDate: true,
                                doNotShipAfterDate: true,
                                remainingQuantityToShipInSalesUnit: true,
                                salesUnitToStockUnitConversionFactor: true,
                                stockUnit: { id: true, name: true, _id: true, decimalDigits: true, symbol: true },
                                netPrice: true,
                                netPriceIncludingTax: true,
                                amountExcludingTax: true,
                                availableQuantityInStockUnit: true,
                                requestedDeliveryDate: true,
                                deliveryLeadTime: true,
                                expectedDeliveryDate: true,
                                assignedQuantity: true,
                                suppliedQuantity: true,
                                uAssignmentOrder: true,
                                quantityAllocated: true,
                                deliveryMode: { name: true, _id: true, description: true },
                                shipToAddress: {
                                    _id: true,
                                    name: true,
                                    locationPhoneNumber: true,
                                    concatenatedAddress: true,
                                },
                            },
                            {
                                filter,
                                orderBy: { shippingDate: +1, document: { number: +1 }, item: { name: +1 } },
                                first: 500,
                            },
                        ),
                    )
                    .execute()
                    .finally(() => {
                        this.$.loader.isHidden = true;
                    }),
            );

            this.calculateStockAvailability(results);

            this.fillResultsTable(results);

            this.$.loader.isHidden = true;
        }
    }

    private async selectUnselectCheckboxes() {
        if (this.results.value.length > 0) {
            this.calculateOnSelect = false;

            // eslint-disable-next-line no-restricted-syntax
            for (const row of this.results.value) {
                if (this.selectAllCheckbox.value) {
                    this.results.selectRecord(row._id);

                    const quantityConfirmedInStockUnit = row.uAssignmentOrder
                        ? Math.min(
                              +row.suppliedQuantity,
                              +row.assignedQuantity,
                              +row.remainingQuantityToShipInSalesUnit,
                              +row.quantityAllocated,
                          )
                        : +row.quantityInStockUnit;
                    await this.updateRowValuesAfterConfirmedQuantityChange(+quantityConfirmedInStockUnit, row);
                } else {
                    this.results.unselectRecord(row._id);

                    await this.updateRowValuesAfterConfirmedQuantityChange(0, row);
                }
            }
            this.createSalesShipmentsFromOrderLines.isDisabled = !this.selectAllCheckbox.value;
            this.calculateOnSelect = true;
            if (this.selectAllCheckbox.value) {
                await this.onSelectCalculation();
            } else {
                this.revenue.value = 0;
                this.customerSatisfaction.value = '';
            }
        }
    }

    private async onSelectCalculation() {
        if (this.calculateOnSelect) {
            let quantityConfirmed = 0;
            let quantityToShip = 0;
            this.revenue.value = 0;
            // eslint-disable-next-line no-restricted-syntax
            for (const row of this.results.value) {
                await this.updateRevenueValue(row);
                quantityConfirmed += +(row?.quantityConfirmedInStockUnit ?? 0);
                quantityToShip += +(row?.quantityInStockUnit ?? 0);
            }
            if (quantityToShip === 0) {
                this.customerSatisfaction.value = '0 %';
            } else {
                let customerSatisfaction = Number((100 * quantityConfirmed) / quantityToShip);
                customerSatisfaction = Math.round((customerSatisfaction + Number.EPSILON) * 100) / 100;
                this.customerSatisfaction.value = `${customerSatisfaction} %`;
            }
        }
    }

    private updateChooseOrdersStatus() {
        const isDisabled = !this.salesCompany.value || !this.stockSite.value;
        this.chooseOrders.isDisabled = isDisabled;
        this.createSalesShipmentsFromOrderLines.isDisabled = isDisabled;
    }

    private changeMainBlockVisibility(isHidden: boolean) {
        this.mainBlock.isHidden = isHidden;
        this.results.isHidden = isHidden;
        this.cancelDetailedButton.isHidden = isHidden;
        this.chooseOrders.isHidden = !isHidden;
        this.salesCompany.isDisabled = !isHidden;
        this.stockSite.isDisabled = !isHidden;
        this.shippingUntilDate.isDisabled = !isHidden;
        this.fromSoldToCustomer.isDisabled = !isHidden;
        this.toSoldToCustomer.isDisabled = !isHidden;
        this.fromOrderNumber.isDisabled = !isHidden;
        this.toOrderNumber.isDisabled = !isHidden;
        this.incoterm.isDisabled = !isHidden;
        this.deliveryMode.isDisabled = !isHidden;
        if (isHidden) {
            this.createSalesShipmentsFromOrderLines.isDisabled = false;
        }
    }

    private calculateStockAvailability(results: Array<any>): void {
        results.forEach(resultLine => {
            SalesOrderShippingMassProcess.calculateQuantitiesAndAmounts(resultLine);
            const dictKey = resultLine.item._id;

            if (dictKey in this.stockAvailability) {
                this.stockAvailability[dictKey].requiredQuantityInStockUnit += +resultLine.quantityInStockUnit;
                this.stockAvailability[dictKey].sumAllocatedQuantityInStockUnit += +resultLine.quantityAllocated;
            } else {
                this.stockAvailability[dictKey] = {
                    availableQuantityInStockUnit: +resultLine.availableQuantityInStockUnit,
                    requiredQuantityInStockUnit: +resultLine.quantityInStockUnit,
                    quantityAlreadyProposedInStockUnit: 0,
                    confirmedQuantityInStockUnit: +resultLine.availableQuantityInStockUnit,
                    sumAllocatedQuantityInStockUnit: +resultLine.quantityAllocated,
                    shortageQuantityInStockUnit: 0,
                    oldRemainingQuantity: +resultLine.availableQuantityInStockUnit,
                };
            }
            this.stockAvailability[dictKey].shortageQuantityInStockUnit = Math.max(
                this.stockAvailability[dictKey].requiredQuantityInStockUnit -
                    this.stockAvailability[dictKey].sumAllocatedQuantityInStockUnit -
                    this.stockAvailability[dictKey].availableQuantityInStockUnit,
                0,
            );
        });
    }

    private fillResultsTable(results: Array<any>) {
        let oldConfirmedQty = 0;
        let oldAllocationQty = 0;

        results.forEach(resultLine => {
            const processedLine = SalesOrderShippingMassProcess.adjustSalesOrderData(resultLine);
            const dictKey = String(processedLine.item._id);

            if (
                this.stockAvailability[dictKey].availableQuantityInStockUnit === 0 &&
                processedLine.item.isStockManaged
            ) {
                processedLine.stockAvailability = ui.localize(
                    '@sage/xtrem-sales/pages__sales_order_shipping_mass_process__not_available',
                    'Not available',
                );
                processedLine.quantityConfirmedInStockUnit = 0;
                processedLine.amountExcludingTax = 0;
                processedLine.amountIncludingTax = 0;
                processedLine.remainingQuantityInStockUnit = 0;
                processedLine.totalQuantityOnDemand = 0;
                processedLine.stockShortageInStockUnit = this.stockAvailability[dictKey].requiredQuantityInStockUnit;
            } else if (
                this.stockAvailability[dictKey].availableQuantityInStockUnit >=
                    this.stockAvailability[dictKey].requiredQuantityInStockUnit ||
                !processedLine.item.isStockManaged ||
                this.stockAvailability[dictKey].shortageQuantityInStockUnit === 0
            ) {
                processedLine.stockAvailabilityStatus = processedLine.item.isStockManaged
                    ? 'available'
                    : 'notApplicable';
                processedLine.stockAvailability = processedLine.item.isStockManaged
                    ? ui.localize('@sage/xtrem-sales/pages__sales_order_shipping_mass_process__available', 'Available')
                    : ui.localize(
                          '@sage/xtrem-sales/pages__sales_order_shipping_mass_process__not_applicable',
                          'Not applicable',
                      );
                processedLine.quantityConfirmedInStockUnit = resultLine.uAssignmentOrder
                    ? Math.min(
                          +processedLine.suppliedQuantity,
                          +processedLine.assignedQuantity,
                          +processedLine.remainingQuantityToShipInSalesUnit,
                          +processedLine.quantityAllocated,
                      )
                    : +processedLine.quantityInStockUnit;

                processedLine.totalQuantityOnDemand = +this.stockAvailability[dictKey].requiredQuantityInStockUnit;

                processedLine.stockShortageInStockUnit = 0;
            } else {
                this.prepareShortageEntry(processedLine, dictKey);
            }
            processedLine.totalQuantityOnDemand = this.stockAvailability[dictKey].requiredQuantityInStockUnit;
            processedLine.availableQuantityInStockUnit = this.stockAvailability[dictKey].availableQuantityInStockUnit;
            processedLine.stockShortageInStockUnit = this.stockAvailability[dictKey].shortageQuantityInStockUnit;
            processedLine.oldQuantityConfirmedInStockUnit = +processedLine.quantityConfirmedInStockUnit;

            this.calculateAndUpdateRemainingQuantity(processedLine, oldConfirmedQty, oldAllocationQty);
            oldConfirmedQty = +processedLine.quantityConfirmedInStockUnit || 0;
            oldAllocationQty = +processedLine.quantityAllocated || 0;
            this.results.addOrUpdateRecordValue(processedLine);
        });
    }

    private prepareShortageEntry(processedLine: Partial<ui.plugin.Dict<any>>, dictKey: string) {
        SalesOrderShippingMassProcess.updateShortageLabel(processedLine);
        processedLine.stockShortageInStockUnit =
            this.stockAvailability[dictKey].requiredQuantityInStockUnit -
            this.stockAvailability[dictKey].availableQuantityInStockUnit;
        processedLine.quantityConfirmedInStockUnit = 0;
        processedLine.totalQuantityOnDemand = 0;
        if (
            this.stockAvailability[dictKey].quantityAlreadyProposedInStockUnit <
            this.stockAvailability[dictKey].availableQuantityInStockUnit
        ) {
            if (
                this.stockAvailability[dictKey].availableQuantityInStockUnit -
                    this.stockAvailability[dictKey].quantityAlreadyProposedInStockUnit <
                processedLine.quantityInStockUnit
            ) {
                processedLine.quantityConfirmedInStockUnit = processedLine.uAssignmentOrder
                    ? Math.min(
                          processedLine.suppliedQuantity,
                          processedLine.assignedQuantity,
                          processedLine.remainingQuantityToShipInSalesUnit,
                          processedLine.quantityAllocated,
                      )
                    : +this.stockAvailability[dictKey].availableQuantityInStockUnit -
                      +this.stockAvailability[dictKey].quantityAlreadyProposedInStockUnit;
                this.stockAvailability[dictKey].quantityAlreadyProposedInStockUnit =
                    this.stockAvailability[dictKey].availableQuantityInStockUnit;
                processedLine.totalQuantityOnDemand = +this.stockAvailability[dictKey].requiredQuantityInStockUnit;
                const oneUnitNetPrice = +(processedLine.amountExcludingTax / processedLine.quantityInStockUnit);
                const oneUnitGrossPrice = +(processedLine.amountIncludingTax / processedLine.quantityInStockUnit);
                processedLine.amountExcludingTax = +(oneUnitNetPrice * processedLine.quantityConfirmedInStockUnit);
                processedLine.amountIncludingTax = +(oneUnitGrossPrice * processedLine.quantityConfirmedInStockUnit);
            } else {
                processedLine.quantityConfirmedInStockUnit = processedLine.uAssignmentOrder
                    ? Math.min(
                          processedLine.suppliedQuantity,
                          processedLine.assignedQuantity,
                          processedLine.remainingQuantityToShipInSalesUnit,
                          processedLine.quantityAllocated,
                      )
                    : processedLine.quantityInStockUnit;
                this.stockAvailability[dictKey].quantityAlreadyProposedInStockUnit +=
                    +processedLine.quantityInStockUnit;
                processedLine.totalQuantityOnDemand = +this.stockAvailability[dictKey].requiredQuantityInStockUnit;
            }
        } else {
            processedLine.amountExcludingTax = 0;
            processedLine.amountIncludingTax = 0;
        }
        processedLine.remainingQuantityInStockUnit =
            +this.stockAvailability[dictKey].availableQuantityInStockUnit - processedLine.quantityConfirmedInStockUnit;
    }

    private static updateShortageLabel(processedLine: Partial<ui.plugin.Dict<any>>) {
        processedLine.stockAvailabilityStatus = 'shortage';
        processedLine.stockAvailability = ui.localize(
            '@sage/xtrem-sales/pages__sales_order_shipping_mass_process__shortage',
            'Shortage',
        );
    }

    private updateAllShortageLabelsRelatedToItem(
        quantityConfirmedInStockUnit: number,
        rowData: Partial<ui.plugin.Dict<any>>,
    ) {
        const dictKey = String(rowData.item._id);
        if (
            this.stockAvailability[dictKey].availableQuantityInStockUnit <
                this.stockAvailability[dictKey].requiredQuantityInStockUnit &&
            rowData.item.isStockManaged &&
            this.stockAvailability[dictKey].availableQuantityInStockUnit > 0
        ) {
            this.stockAvailability[dictKey].confirmedQuantityInStockUnit =
                this.stockAvailability[dictKey].confirmedQuantityInStockUnit -
                +rowData.oldQuantityConfirmedInStockUnit +
                quantityConfirmedInStockUnit;
            this.results.value
                .filter(row => rowData.item._id === row.item._id)
                .forEach(row => {
                    SalesOrderShippingMassProcess.updateShortageLabel(row);
                    this.results.setRecordValue({
                        _id: row._id,
                        stockAvailability: row.stockAvailability,
                    });
                });
        }
        this.results.setRecordValue({
            _id: rowData._id,
            oldQuantityConfirmedInStockUnit: quantityConfirmedInStockUnit,
        });
    }

    private async updateRevenueValue(processedLine: Partial<ui.plugin.Dict<any>> | null) {
        if (processedLine?.currency && this.salesCompany.value?.currency?.id !== processedLine.currency.id) {
            const amountIncludingTax = await this.$.graph
                .node('@sage/xtrem-master-data/ExchangeRate')
                .queries.convertRate(undefined!, {
                    base: processedLine.currency.id,
                    destination: this.salesCompany.value?.currency?.id,
                    amount: processedLine.amountIncludingTax.toString(),
                })
                .execute();
            this.revenue.value = +(this.revenue.value ?? 0) + +amountIncludingTax;
        } else {
            this.revenue.value = +(this.revenue.value ?? 0) + +(processedLine?.amountIncludingTax ?? 0);
        }
    }

    private static stockAvailabilityColor(coloredElement: ColoredElement, status?: string) {
        switch (status) {
            case 'available':
            case 'notApplicable':
                return colorfulPillPattern.filledPositive[coloredElement];
            case 'shortage':
                return colorfulPillPattern.filledCaution[coloredElement];
            default:
                return colorfulPillPattern.filledNegative[coloredElement];
        }
    }

    private static calculateQuantitiesAndAmounts(resultLine: Partial<ui.plugin.Dict<any>>) {
        resultLine.quantityInStockUnit =
            +resultLine.remainingQuantityToShipInSalesUnit * +resultLine.salesUnitToStockUnitConversionFactor;
        resultLine.amountExcludingTax = +resultLine.netPrice * +resultLine.remainingQuantityToShipInSalesUnit;
        resultLine.amountIncludingTax =
            +resultLine.netPriceIncludingTax * +resultLine.remainingQuantityToShipInSalesUnit;
    }

    private static adjustSalesOrderData(resultLine: Partial<ui.plugin.Dict<any>>) {
        const { document } = resultLine;
        delete resultLine.document;
        document.soldToCustomerName = document.soldToCustomer.businessEntity.name;
        resultLine.itemName = resultLine.item.name;
        resultLine.shippingDateOrderBy = resultLine.shippingDate;
        return { ...resultLine, ...document };
    }

    private async updateRowValuesAfterConfirmedQuantityChange(
        quantityConfirmedInStockUnit: number,
        rowData: Partial<ui.plugin.Dict<any>>,
    ) {
        this.results.setRecordValue({
            _id: rowData._id,
            quantityConfirmedInStockUnit,
            amountExcludingTax:
                (+quantityConfirmedInStockUnit / +rowData.salesUnitToStockUnitConversionFactor) * +rowData.netPrice,
            amountIncludingTax:
                (+quantityConfirmedInStockUnit / +rowData.salesUnitToStockUnitConversionFactor) *
                +rowData.netPriceIncludingTax,
        });

        this.updateAllRemainingRelatedToItem(rowData);
        await this.onSelectCalculation();
    }

    private controlSelectionState() {
        this.selectAllCheckbox.value = this.results.selectedRecords.length === this.results.value.length;
        this.createSalesShipmentsFromOrderLines.isDisabled = this.results.selectedRecords.length === 0;
    }

    private calculateAndUpdateRemainingQuantity(
        processedLine: { item: { _id: string }; remainingQuantityInStockUnit: number },
        oldConfirmedQuantity: number,
        oldAllocationQuantity: number,
    ) {
        const itemId = processedLine.item._id;
        const { oldRemainingQuantity } = this.stockAvailability[itemId];
        const remainingQuantity = Math.max(oldRemainingQuantity - oldConfirmedQuantity + oldAllocationQuantity, 0);
        processedLine.remainingQuantityInStockUnit = remainingQuantity;
        this.stockAvailability[itemId].oldRemainingQuantity = remainingQuantity;
    }

    private updateAllRemainingRelatedToItem(rowData: Partial<ui.plugin.Dict<any>>) {
        const {
            item: { _id: dictKey },
            availableQuantityInStockUnit: oldRemainingQuantity,
        } = rowData;

        this.results.value
            .filter(
                ({ item: { _id }, quantityConfirmedInStockUnit }) =>
                    dictKey === _id && quantityConfirmedInStockUnit > 0,
            )
            .reduce(
                (
                    { remainingQuantity, confirmedQuantity, allocationQuantity },
                    { _id, quantityConfirmedInStockUnit, quantityAllocated },
                ) => {
                    const newRemainingQuantity = Math.max(
                        remainingQuantity - (+confirmedQuantity || 0) + (+allocationQuantity || 0),
                        0,
                    );
                    this.results.setRecordValue({ _id, remainingQuantityInStockUnit: newRemainingQuantity });
                    return {
                        remainingQuantity: newRemainingQuantity,
                        confirmedQuantity: +quantityConfirmedInStockUnit || 0,
                        allocationQuantity: +quantityAllocated || 0,
                    };
                },
                { remainingQuantity: oldRemainingQuantity, confirmedQuantity: 0, allocationQuantity: 0 },
            );
    }
}
