import { withoutEdges } from '@sage/xtrem-client';
import { DateValue } from '@sage/xtrem-date-time';
import type { Account } from '@sage/xtrem-finance-data-api';
import type { BusinessEntity, Currency, Customer, Item, UnitOfMeasure } from '@sage/xtrem-master-data-api';
import { setReferenceIfSingleValue } from '@sage/xtrem-master-data/build/lib/client-functions/get-default';
import { finance } from '@sage/xtrem-master-data/build/lib/menu-items/finance';
import { setMultiReferenceIfSingleValue } from '@sage/xtrem-master-data/lib/client-functions/get-default';
import type {
    GraphApi,
    SalesShipment,
    UnbilledAccountReceivableInputSet,
    UnbilledAccountReceivableResultLine,
    UnbilledAccountReceivableStatus,
} from '@sage/xtrem-sales-api';
import type { Company, Site, User } from '@sage/xtrem-system-api';
import * as ui from '@sage/xtrem-ui';
import * as PillColor from '../client-functions/pill-color';

@ui.decorators.page<UnbilledAccountReceivableInquiry, UnbilledAccountReceivableInputSet>({
    title: 'Unbilled accounts receivable',
    module: 'finance',
    mode: 'tabs',
    menuItem: finance,
    priority: 550,
    skipDirtyCheck: true,
    node: '@sage/xtrem-sales/UnbilledAccountReceivableInputSet',
    createAction() {
        return this.$standardNewAction;
    },
    headerDropDownActions() {
        return [this.$standardDeleteAction];
    },
    businessActions() {
        return [this.runUnbilledAccountReceivableInquiry];
    },
    async defaultEntry() {
        // As there is only one record per user try to load this record.
        //
        // NOTE (for local Cucumber script test on local development instances): Please make sure that the user
        // e-mail address in the top field is set to the e-mail address of the user configured in xtrem-config.yml!
        const inputSet = withoutEdges(
            await this.$.graph
                .node('@sage/xtrem-sales/UnbilledAccountReceivableInputSet')
                .query(
                    ui.queryUtils.edgesSelector(
                        { _id: true },
                        { filter: { user: { email: this.$.username } }, first: 1 },
                    ),
                )
                .execute(),
        );

        // If there is such record open it. Otherwise open work with an empty page in creation mode.
        return inputSet.at(0)?._id || '$new';
    },
    async onLoad() {
        if (!this.$.recordId) {
            // In case we have no record for this user pre-set the calculation user field.
            await this.$.fetchDefaults(['user']);
        }
        if (!this.status.value || this.status.value === 'draft') {
            this.asOfDate.value = DateValue.today().addMonths(-1).endOfMonth().toString();
        }
        await setReferenceIfSingleValue([this.company]);
        await setMultiReferenceIfSingleValue([this.sites]);
    },
    navigationPanel: undefined,
    headerSection() {
        return this.mainSection;
    },
})
export class UnbilledAccountReceivableInquiry extends ui.Page<GraphApi, UnbilledAccountReceivableInputSet> {
    @ui.decorators.pageAction<UnbilledAccountReceivableInquiry>({
        title: 'Run',
        isDisabled() {
            return this.status.value === 'inProgress';
        },
        async onClick() {
            this.$.loader.isHidden = false;
            this.status.value = 'inProgress';
            if (await this.saveInquiry()) {
                await this.runInquiry();
                await this.$.router.refresh();
            } else {
                this.status.value = 'error';
            }
            this.$.loader.isHidden = true;
        },
        buttonType: 'primary',
    })
    runUnbilledAccountReceivableInquiry: ui.PageAction;

    getSerializedValues() {
        const { values } = this.$;
        values.sites = values.sites ?? [];
        return values;
    }

    async saveInquiry() {
        const validation = await this.$.page.validate();
        if (validation.length !== 0) {
            this.$.showToast(validation.join('\n'), { type: 'error' });
            return false;
        }
        await this.$standardSaveAction.execute(true);
        // To be sure that the user is refreshed before we continue
        return true;
    }

    async runInquiry() {
        if (!this.user.value?._id) {
            return;
        }

        // Notify the user that a calculation request is on the way.
        this.$.showToast(
            ui.localize(
                '@sage/xtrem-sales/pages__unbilled_account_receivable_inquiry__notification_request_sent',
                'Unbilled accounts receivable request sent.',
            ),
            { type: 'success' },
        );

        // Request calculation in an asynchronous mutation.
        const isFinished = await this.$.graph
            .node('@sage/xtrem-sales/UnbilledAccountReceivableInputSet')
            .asyncOperations.unbilledAccountReceivableInquiry.runToCompletion(true, {
                userId: this.user.value._id,
            })
            .execute();
        if (isFinished) {
            this.$.showToast(
                ui.localize(
                    '@sage/xtrem-sales/pages__unbilled_account_receivable_inquiry__notification_inquiry_finished',
                    'Unbilled accounts receivable finished.',
                ),
                { type: 'success' },
            );
        }
    }

    @ui.decorators.section<UnbilledAccountReceivableInquiry>({ isOpen: true, isTitleHidden: true, title: 'General' })
    mainSection: ui.containers.Section;

    @ui.decorators.block<UnbilledAccountReceivableInquiry>({
        parent() {
            return this.mainSection;
        },
        width: 'extra-large',
        title: 'Criteria',
    })
    criteriaBlock: ui.containers.Block;

    @ui.decorators.referenceField<UnbilledAccountReceivableInquiry, User>({
        parent() {
            return this.criteriaBlock;
        },
        title: 'User',
        isReadOnly: true,
        width: 'small',
        fetchesDefaults: true,
        valueField: 'displayName',
        node: '@sage/xtrem-system/User',
        tunnelPage: '@sage/xtrem-authorization/User',
    })
    user: ui.fields.Reference<User>;

    @ui.decorators.referenceField<UnbilledAccountReceivableInquiry, Company>({
        parent() {
            return this.criteriaBlock;
        },
        title: 'Company',
        node: '@sage/xtrem-system/Company',
        tunnelPage: '@sage/xtrem-master-data/Company',
        lookupDialogTitle: 'Select company',
        minLookupCharacters: 0,
        valueField: 'name',
        helperTextField: 'id',
        columns: [
            ui.nestedFields.text({ title: 'Name', bind: 'name' }),
            ui.nestedFields.text({ title: 'ID', bind: 'id' }),
        ],
        filter: { isActive: { _eq: true } },
        placeholder: 'Select...',
        width: 'small',
        onChange() {
            this.sites.value = [];
        },
    })
    company: ui.fields.Reference<Company>;

    @ui.decorators.multiReferenceField<UnbilledAccountReceivableInquiry, Site>({
        node: '@sage/xtrem-system/Site',
        title: 'Site',
        placeholder: 'Select site',
        minLookupCharacters: 0,
        lookupDialogTitle: 'Select site',
        valueField: { businessEntity: { name: true } },
        helperTextField: { businessEntity: { id: true } },
        columns: [
            ui.nestedFields.text({ bind: 'name' }),
            ui.nestedFields.text({ bind: 'id' }),
            ui.nestedFields.reference<UnbilledAccountReceivableInquiry, Site, Company>({
                node: '@sage/xtrem-system/Company',
                tunnelPage: '@sage/xtrem-master-data/Company',
                title: 'Company',
                bind: 'legalCompany',
                valueField: 'name',
                columns: [ui.nestedFields.text({ bind: 'name' }), ui.nestedFields.technical({ bind: 'id' })],
            }),
            ui.nestedFields.technical({ bind: 'isActive' }),
            ui.nestedFields.technical({ bind: 'isInventory' }),
        ],
        parent() {
            return this.criteriaBlock;
        },
        filter() {
            return {
                isActive: { _eq: true },
                isInventory: { _eq: true },
                ...(this.company.value ? { legalCompany: { id: { _eq: this.company.value?.id ?? '' } } } : {}),
            };
        },
    })
    sites: ui.fields.MultiReference<Site>;

    @ui.decorators.referenceField<UnbilledAccountReceivableInquiry, Customer>({
        parent() {
            return this.criteriaBlock;
        },
        lookupDialogTitle: 'Select from customer',
        orderBy: { businessEntity: { name: +1 } },
        columns: [
            ui.nestedFields.text({ title: 'Name', bind: { businessEntity: { name: true } } }),
            ui.nestedFields.text({ title: 'ID', bind: { businessEntity: { id: true } } }),
            ui.nestedFields.text({ title: 'Tax ID', bind: { businessEntity: { taxIdNumber: true } } }),
            ui.nestedFields.text({ title: 'Country', bind: { businessEntity: { country: { name: true } } } }),
            ui.nestedFields.technical<UnbilledAccountReceivableInquiry, Customer, BusinessEntity>({
                node: '@sage/xtrem-master-data/BusinessEntity',
                bind: 'businessEntity',
                nestedFields: [
                    ui.nestedFields.technical({ bind: '_id' }),
                    ui.nestedFields.technical({ bind: 'name' }),
                    ui.nestedFields.technical({ bind: 'id' }),
                    ui.nestedFields.technical<UnbilledAccountReceivableInquiry, BusinessEntity, Currency>({
                        node: '@sage/xtrem-master-data/Currency',
                        bind: 'currency',
                        nestedFields: [
                            ui.nestedFields.technical({ bind: 'name' }),
                            ui.nestedFields.technical({ bind: 'id' }),
                        ],
                    }),
                ],
            }),
        ],
    })
    fromCustomer: ui.fields.Reference<Customer>;

    @ui.decorators.referenceField<UnbilledAccountReceivableInquiry, Customer>({
        parent() {
            return this.criteriaBlock;
        },
        lookupDialogTitle: 'Select to customer',
        orderBy: { businessEntity: { name: +1 } },
        helperTextField: { businessEntity: { id: true } },
        columns: [
            ui.nestedFields.text({ title: 'Name', bind: { businessEntity: { name: true } } }),
            ui.nestedFields.text({ title: 'ID', bind: { businessEntity: { id: true } } }),
            ui.nestedFields.text({ title: 'Tax ID', bind: { businessEntity: { taxIdNumber: true } } }),
            ui.nestedFields.text({ title: 'Country', bind: { businessEntity: { country: { name: true } } } }),
            ui.nestedFields.technical<UnbilledAccountReceivableInquiry, Customer, BusinessEntity>({
                node: '@sage/xtrem-master-data/BusinessEntity',
                bind: 'businessEntity',
                nestedFields: [
                    ui.nestedFields.technical({ bind: 'name' }),
                    ui.nestedFields.technical({ bind: 'id' }),
                    ui.nestedFields.technical<UnbilledAccountReceivableInquiry, BusinessEntity, Currency>({
                        node: '@sage/xtrem-master-data/Currency',
                        bind: 'currency',
                        nestedFields: [
                            ui.nestedFields.technical({ bind: 'name' }),
                            ui.nestedFields.technical({ bind: 'id' }),
                        ],
                    }),
                ],
            }),
        ],
    })
    toCustomer: ui.fields.Reference<Customer>;

    @ui.decorators.dateField<UnbilledAccountReceivableInquiry>({
        parent() {
            return this.criteriaBlock;
        },
        title: 'As of date',
        isMandatory: true,
    })
    asOfDate: ui.fields.Date;

    @ui.decorators.relativeDateField<UnbilledAccountReceivableInquiry>({
        parent() {
            return this.criteriaBlock;
        },
        title: 'Last run date',
    })
    executionDate: ui.fields.RelativeDate;

    @ui.decorators.labelField<UnbilledAccountReceivableInquiry>({
        title: 'Status',
        optionType: '@sage/xtrem-stock/UnbilledAccountReceivableStatus',
        parent() {
            return this.criteriaBlock;
        },
        style() {
            return PillColor.getLabelColorByStatus('UnbilledAccountReceivableStatus', this.status.value);
        },
    })
    status: ui.fields.Label<UnbilledAccountReceivableStatus>;

    @ui.decorators.section({ title: 'Results' }) resultsSection: ui.containers.Section;

    // Note: Infinite scrolling is automatically enabled to a table field if you apply the following:
    //       - A section (see resultsSection) that only contains this table field without any block inbetween.
    //       - The page mode decorator is set to "mode: 'tabs'".
    //       - To prevent having tabs on the screen we have to set a header section.
    @ui.decorators.tableField<UnbilledAccountReceivableInquiry, UnbilledAccountReceivableResultLine>({
        node: '@sage/xtrem-sales/UnbilledAccountReceivableResultLine',
        canResizeColumns: true,
        canSelect: false,
        isHelperTextHidden: true,
        canExport: true,
        title: 'Results',
        isReadOnly: true,
        orderBy: {
            financialSite: { legalCompany: { id: 1 }, id: 1 },
            customer: { businessEntity: { id: 1 } },
            site: { id: 1 },
            shipmentNumber: 1,
        },
        parent() {
            return this.resultsSection;
        },
        columns: [
            ui.nestedFields.reference<UnbilledAccountReceivableInquiry, UnbilledAccountReceivableResultLine, Customer>({
                title: 'Bill-to customer',
                bind: 'customer',
                tunnelPage: '@sage/xtrem-master-data/Customer',
            }),
            ui.nestedFields.text({
                title: 'Bill-to customer ID',
                bind: { customer: { businessEntity: { id: true } } },
            }),
            ui.nestedFields.reference<UnbilledAccountReceivableInquiry, UnbilledAccountReceivableResultLine, Currency>({
                bind: 'currency',
                title: 'Currency',
                node: '@sage/xtrem-master-data/Currency',
                tunnelPage: '@sage/xtrem-master-data/Currency',
                valueField: 'id',
                columns: [
                    ui.nestedFields.text({ bind: 'name' }),
                    ui.nestedFields.technical({ bind: 'id' }),
                    ui.nestedFields.technical({ bind: 'symbol' }),
                    ui.nestedFields.technical({ bind: 'decimalDigits' }),
                ],
                isHiddenOnMainField: true,
            }),
            ui.nestedFields.text({ title: 'Currency ID', bind: { currency: { id: true } }, isHiddenOnMainField: true }),
            ui.nestedFields.reference<UnbilledAccountReceivableInquiry, UnbilledAccountReceivableResultLine, Company>({
                title: 'Company',
                bind: 'company',
                node: '@sage/xtrem-system/Company',
                tunnelPage: '@sage/xtrem-master-data/Company',
                valueField: 'name',
                isHiddenOnMainField: true,
            }),
            ui.nestedFields.text({ title: 'Company ID', bind: { company: { id: true } } }),
            ui.nestedFields.reference<UnbilledAccountReceivableInquiry, UnbilledAccountReceivableResultLine, Site>({
                title: 'Financial site',
                bind: 'financialSite',
                tunnelPage: '@sage/xtrem-master-data/Site',
                isHiddenOnMainField: true,
            }),
            ui.nestedFields.text({ title: 'Financial site ID', bind: { financialSite: { id: true } } }),
            ui.nestedFields.numeric({
                bind: 'invoiceIssuableQuantity',
                title: 'Shipped not invoiced quantity',
                scale(_rowId, rowData) {
                    return rowData?.salesUnit?.decimalDigits ?? 0;
                },
                postfix(_rowId, rowData) {
                    return rowData?.salesUnit?.symbol ?? '';
                },
                groupAggregationMethod: 'sum',
            }),
            ui.nestedFields.reference<
                UnbilledAccountReceivableInquiry,
                UnbilledAccountReceivableResultLine,
                UnitOfMeasure
            >({
                bind: 'salesUnit',
                title: 'Sales unit',
                node: '@sage/xtrem-master-data/UnitOfMeasure',
                tunnelPage: '@sage/xtrem-master-data/UnitOfMeasure',
                valueField: 'name',
                columns: [
                    ui.nestedFields.technical({ bind: 'symbol' }),
                    ui.nestedFields.technical({ bind: 'decimalDigits' }),
                ],
                isHiddenOnMainField: true,
            }),
            ui.nestedFields.numeric({
                bind: 'netPrice',
                title: 'Net price',
                prefix: (_value, rowData) => rowData?.currency?.symbol ?? '',
                scale: (_value, rowData) => rowData?.currency?.decimalDigits ?? 2,
            }),
            ui.nestedFields.numeric({
                bind: 'invoiceIssuableAmount',
                title: 'Unbilled amount',
                prefix: (_value, rowData) => rowData?.currency?.symbol ?? '',
                scale: (_value, rowData) => rowData?.currency?.decimalDigits ?? 2,
            }),
            ui.nestedFields.numeric({
                bind: 'invoiceIssuableAmountInCompanyCurrency',
                title: 'Unbilled in company currency',
                prefix: (_value, rowData) => rowData?.companyCurrency?.symbol ?? '',
                scale: (_value, rowData) => rowData?.companyCurrency?.decimalDigits ?? 2,
                isHiddenOnMainField: true,
            }),
            ui.nestedFields.numeric({
                bind: 'invoiceIssuableAmountInCompanyCurrencyAtAsOfDate',
                title: 'Unbilled in company currency per the As of date',
                prefix: (_value, rowData) => rowData?.companyCurrency?.symbol ?? '',
                scale: (_value, rowData) => rowData?.companyCurrency?.decimalDigits ?? 2,
                isHiddenOnMainField: true,
            }),
            ui.nestedFields.reference<UnbilledAccountReceivableInquiry, UnbilledAccountReceivableResultLine, Currency>({
                bind: 'companyCurrency',
                title: 'Company currency',
                node: '@sage/xtrem-master-data/Currency',
                tunnelPage: '@sage/xtrem-master-data/Currency',
                valueField: 'name',
                columns: [
                    ui.nestedFields.text({ bind: 'name' }),
                    ui.nestedFields.technical({ bind: 'id' }),
                    ui.nestedFields.technical({ bind: 'symbol' }),
                    ui.nestedFields.technical({ bind: 'decimalDigits' }),
                ],
                isHiddenOnMainField: true,
            }),
            ui.nestedFields.text({
                title: 'Company currency ID',
                bind: { companyCurrency: { id: true } },
                isHiddenOnMainField: true,
            }),
            ui.nestedFields.text<UnbilledAccountReceivableInquiry, UnbilledAccountReceivableResultLine>({
                bind: { item: { name: true } },
                title: 'Item',
                isHiddenOnMainField: true,
            }),
            ui.nestedFields.reference<UnbilledAccountReceivableInquiry, UnbilledAccountReceivableResultLine, Item>({
                bind: 'item',
                title: 'Item ID',
                node: '@sage/xtrem-master-data/Item',
                tunnelPage: '@sage/xtrem-master-data/Item',
                valueField: 'id',
            }),
            ui.nestedFields.reference<UnbilledAccountReceivableInquiry, UnbilledAccountReceivableResultLine, Site>({
                title: 'Stock site',
                bind: 'site',
                tunnelPage: '@sage/xtrem-master-data/Site',
            }),
            ui.nestedFields.text<UnbilledAccountReceivableInquiry, UnbilledAccountReceivableResultLine>({
                bind: { site: { id: true } },
                title: 'Stock site ID',
                isHiddenOnMainField: true,
            }),
            ui.nestedFields.reference<UnbilledAccountReceivableInquiry, UnbilledAccountReceivableResultLine, Account>({
                title: 'GSNI account',
                bind: 'account',
                node: '@sage/xtrem-finance-data/Account',
                valueField: 'id',
                isHiddenOnMainField: true,
            }),
            ui.nestedFields.reference<UnbilledAccountReceivableInquiry, UnbilledAccountReceivableResultLine, Account>({
                title: 'Account name',
                bind: 'account',
                node: '@sage/xtrem-finance-data/Account',
                valueField: 'name',
                isHiddenOnMainField: true,
            }),
            ui.nestedFields.reference<UnbilledAccountReceivableInquiry, UnbilledAccountReceivableResultLine, Account>({
                title: 'Account item',
                bind: 'accountItem',
                node: '@sage/xtrem-finance-data/Account',
                valueField: 'name',
            }),
            ui.nestedFields.text({ bind: { accountItem: { id: true } }, title: 'Account item ID' }),
            ui.nestedFields.reference<
                UnbilledAccountReceivableInquiry,
                UnbilledAccountReceivableResultLine,
                SalesShipment
            >({
                title: 'Shipment number',
                bind: 'shipmentInternalId',
                node: '@sage/xtrem-sales/SalesShipment',
                tunnelPage: '@sage/xtrem-sales/SalesShipment',
            }),

            ui.nestedFields.numeric({
                bind: 'quantity',
                title: 'Shipped quantity',
                scale(_rowId, rowData) {
                    return rowData?.salesUnit?.decimalDigits ?? 0;
                },
                postfix(_rowId, rowData) {
                    return rowData?.salesUnit?.symbol ?? '';
                },
            }),
            ui.nestedFields.date({ bind: 'documentDate', title: 'Document date' }),
            ui.nestedFields.numeric({
                bind: 'returnedQuantity',
                title: 'Returned quantity',
                scale(_rowId, rowData) {
                    return rowData?.salesUnit?.decimalDigits ?? 0;
                },
                postfix(_rowId, rowData) {
                    return rowData?.salesUnit?.symbol ?? '';
                },
            }),
            ui.nestedFields.reference<UnbilledAccountReceivableInquiry, UnbilledAccountReceivableResultLine, Customer>({
                title: 'Ship-to customer',
                bind: 'shipToCustomer',
                tunnelPage: '@sage/xtrem-master-data/Customer',
                isReadOnly: true,
                isHiddenOnMainField: true,
            }),
            ui.nestedFields.reference<UnbilledAccountReceivableInquiry, UnbilledAccountReceivableResultLine, Customer>({
                title: 'Ship-to customer name',
                bind: 'shipToCustomer',
                tunnelPage: '@sage/xtrem-master-data/Customer',
            }),
            ui.nestedFields.text({
                title: 'Ship-to customer ID',
                bind: { shipToCustomer: { businessEntity: { id: true } } },
                isHiddenOnMainField: true,
            }),
        ],
    })
    lines: ui.fields.Table<UnbilledAccountReceivableResultLine>;
}
