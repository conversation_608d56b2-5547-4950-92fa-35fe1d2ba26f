import * as ui from '@sage/xtrem-ui';

@ui.decorators.page<CustomersOnHoldSettings>({
    title: 'Customers on hold settings',
    isTransient: true,
    onLoad() {
        this.shouldExcludeInactiveCustomers.value = !!this.$.queryParameters.shouldExcludeInactiveCustomers;
    },
    businessActions() {
        return [this.save];
    },
})
export class CustomersOnHoldSettings extends ui.Page {
    @ui.decorators.section<CustomersOnHoldSettings>({
        isTitleHidden: true,
    })
    section: ui.containers.Section;

    @ui.decorators.block<CustomersOnHoldSettings>({
        parent() {
            return this.section;
        },
    })
    fieldBlock: ui.containers.Block;

    @ui.decorators.switchField<CustomersOnHoldSettings>({
        parent() {
            return this.fieldBlock;
        },
        isFullWidth: true,
        title: 'Inactive customers excluded',
    })
    shouldExcludeInactiveCustomers: ui.fields.Switch;

    @ui.decorators.pageAction<CustomersOnHoldSettings>({
        title: 'Save settings',
        onClick() {
            this.$.finish({
                shouldExcludeInactiveCustomers: !!this.shouldExcludeInactiveCustomers.value,
            });
        },
    })
    save: ui.PageAction;
}
