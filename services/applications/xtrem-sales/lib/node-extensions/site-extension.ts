import * as xtremAuthorization from '@sage/xtrem-authorization';
import type { Reference } from '@sage/xtrem-core';
import { decorators, NodeExtension } from '@sage/xtrem-core';
import * as xtremSystem from '@sage/xtrem-system';
import { SalesReturnRequest } from '../nodes';

@decorators.nodeExtension<SiteExtension>({
    extends: () => xtremSystem.nodes.Site,
})
export class SiteExtension extends NodeExtension<xtremSystem.nodes.Site> {
    @decorators.booleanProperty<SiteExtension, 'isSalesReturnRequestApprovalManaged'>({
        defaultValue: true,
        isPublished: true,
        isStored: true,
        lookupAccess: true,
        async control(cx, val) {
            // If approval managed, no need to check for sales return requests pending approval.
            if (val) {
                return;
            }

            const pendingApprovalCount = await this.$.context.queryCount(SalesReturnRequest, {
                filter: {
                    _and: [{ site: this._id }, { approvalStatus: 'pendingApproval' }],
                },
            });
            if (pendingApprovalCount) {
                cx.error.addLocalized(
                    '@sage/xtrem-sales/nodes__site_extension__check_pending_sales_return_requests',
                    'Pending sales return requests need to be handled before disabling the approval process',
                );
            }
        },
    })
    readonly isSalesReturnRequestApprovalManaged: Promise<boolean>;

    @decorators.referenceProperty<SiteExtension, 'salesReturnRequestDefaultApprover'>({
        node: () => xtremSystem.nodes.User,
        isNullable: true,
        isPublished: true,
        isStored: true,
        lookupAccess: true,
        filters: {
            lookup: xtremAuthorization.filters.user.interactiveUsers,
        },
    })
    readonly salesReturnRequestDefaultApprover: Reference<xtremSystem.nodes.User | null>;

    @decorators.referenceProperty<SiteExtension, 'salesReturnRequestSubstituteApprover'>({
        node: () => xtremSystem.nodes.User,
        isNullable: true,
        isPublished: true,
        isStored: true,
        lookupAccess: true,
        filters: {
            lookup: xtremAuthorization.filters.user.interactiveUsers,
        },
    })
    readonly salesReturnRequestSubstituteApprover: Reference<xtremSystem.nodes.User | null>;
}
declare module '@sage/xtrem-system/lib/nodes/site' {
    export interface Site extends SiteExtension {}
}
