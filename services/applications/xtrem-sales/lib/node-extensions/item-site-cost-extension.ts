import { NodeExtension, ValidationSeverity, decorators } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import { doSalesDocumentsExist } from '../functions/common';

@decorators.nodeExtension<ItemSiteCostExtension>({
    extends: () => xtremMasterData.nodes.ItemSiteCost,

    async controlDelete(cx) {
        if (!this.allowDeletion) return; // Item-site-cost cannot be deleted because an error has already been detected.
        if ((await this.itemSite).deletionInProgress) return; // Item-site can be deleted. So can item-site-cost.

        if (
            !(await (await (await this.itemSite).item).isStockManaged) &&
            (await doSalesDocumentsExist(
                this.$.context,
                await (
                    await this.itemSite
                ).item,
                await (
                    await this.itemSite
                ).site,
            ))
        ) {
            cx.addDiagnose(
                ValidationSeverity.error,
                cx.localize(
                    '@sage/xtrem-sales/nodes__item-site-cost__failed_deletion_impossible_if_documents',
                    'Delete not allowed. Sales documents exist for this item-site.',
                ),
            );
        }
    },
})
export class ItemSiteCostExtension extends NodeExtension<xtremMasterData.nodes.ItemSiteCost> {}

declare module '@sage/xtrem-master-data/lib/nodes/item-site-cost' {
    interface ItemSiteCost extends ItemSiteCostExtension {}
}
