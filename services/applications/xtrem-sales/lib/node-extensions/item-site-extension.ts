import { NodeExtension, ValidationSeverity, decorators } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import { doSalesDocumentsExist } from '../functions/common';

@decorators.nodeExtension<ItemSiteExtension>({
    extends: () => xtremMasterData.nodes.ItemSite,

    async controlDelete(cx) {
        if (
            !(await (await this.item).isStockManaged) &&
            (await doSalesDocumentsExist(this.$.context, await this.item, await this.site))
        ) {
            cx.addDiagnose(
                ValidationSeverity.error,
                cx.localize(
                    '@sage/xtrem-sales/nodes__item-site-cost__failed_deletion_impossible_if_documents',
                    'Delete not allowed. Sales documents exist for this item-site.',
                ),
            );
        }
    },
})
export class ItemSiteExtension extends NodeExtension<xtremMasterData.nodes.ItemSite> {}

declare module '@sage/xtrem-master-data/lib/nodes/item-site' {
    interface ItemSite extends ItemSiteExtension {}
}
