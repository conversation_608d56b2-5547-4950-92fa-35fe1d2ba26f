import { decorators, NodeExtension, NodeStatus } from '@sage/xtrem-core';
import * as xtremFinanceData from '@sage/xtrem-finance-data';
import * as xtremSales from '..';

@decorators.nodeExtension<BaseOpenItemExtension>({
    extends: () => xtremFinanceData.nodes.BaseOpenItem,
    async saveBegin() {
        if (this.$.status === NodeStatus.added) {
            const document = await xtremSales.functions.FinanceIntegration.getSalesInvoiceOrCreditMemo(
                this.$.context,
                await this.documentType,
                await this.documentSysId,
            );
            if (document) {
                const paymentTerm = await document.paymentTerm;
                await this.$.set({
                    discountFrom: await paymentTerm.discountFrom,
                    discountDate: await paymentTerm.discountDate,
                    discountType: await document.discountPaymentType,
                    discountAmount: await document.discountPaymentAmount,
                    discountPaymentBeforeDate: await document.discountPaymentBeforeDate,
                    penaltyPaymentType: await document.penaltyPaymentType,
                    penaltyAmount: await document.penaltyPaymentAmount,
                });
            }
        }
    },
})
export class BaseOpenItemExtension extends NodeExtension<xtremFinanceData.nodes.BaseOpenItem> {}
declare module '@sage/xtrem-finance-data/lib/nodes/base-open-item' {
    export interface BaseOpenItem extends BaseOpenItemExtension {}
}
