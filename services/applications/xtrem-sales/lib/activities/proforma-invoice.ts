import { Activity } from '@sage/xtrem-core';
import * as xtremReporting from '@sage/xtrem-reporting';
import * as xtremSales from '..';

export const proformaInvoice = new Activity({
    description: 'Proforma invoice',
    node: () => xtremSales.nodes.ProformaInvoice,
    __filename,
    permissions: ['print'],
    operationGrants: {
        print: [
            {
                operations: ['create', 'update', 'beforePrintProformaInvoice', 'afterPrintProformaInvoice'],
                on: [() => xtremSales.nodes.ProformaInvoice],
            },
            {
                operations: ['generateReportPdf', 'generateReportZip', 'generateReports'],
                on: [() => xtremReporting.nodes.Report],
            },
        ],
    },
});
