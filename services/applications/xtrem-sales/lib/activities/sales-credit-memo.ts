import * as xtremCommunication from '@sage/xtrem-communication';
import { Activity } from '@sage/xtrem-core';
import * as xtremFinanceData from '@sage/xtrem-finance-data';
import * as xtremReporting from '@sage/xtrem-reporting';
import * as xtremSales from '..';
import { commonSalesActivities } from '../functions/common';

export const salesCreditMemo = new Activity({
    description: 'Sales credit memo',
    node: () => xtremSales.nodes.SalesCreditMemo,
    __filename,
    permissions: ['read', 'manage', 'print', 'post'],
    operationGrants: {
        read: [
            ...commonSalesActivities,
            {
                operations: ['lookup'],
                on: [
                    () => xtremSales.nodes.SalesInvoice,
                    () => xtremSales.nodes.SalesCreditMemoLine,
                    () => xtremSales.nodes.SalesInvoiceLineToSalesCreditMemoLine,
                ],
            },
        ],
        manage: [
            ...commonSalesActivities,
            {
                operations: ['read', 'create', 'update', 'delete', 'enforceStatusPosted', 'financeIntegrationCheck'],
                on: [() => xtremSales.nodes.SalesCreditMemo],
            },
            {
                operations: ['lookup', 'createSalesCreditMemosFromInvoiceLines', 'createSalesCreditMemosFromInvoices'],
                on: [() => xtremSales.nodes.SalesInvoice],
            },
            {
                operations: [
                    'createSalesCreditMemosFromReturnRequestLines',
                    'createSalesCreditMemosFromReturnRequests',
                ],
                on: [() => xtremSales.nodes.SalesReturnRequest],
            },
            {
                operations: ['lookup', 'getTaxDetermination', 'calculateLineTaxes', 'setDimension'],
                on: [() => xtremSales.nodes.SalesCreditMemoLine],
            },
            {
                operations: ['lookup'],
                on: [() => xtremSales.nodes.SalesInvoiceLineToSalesCreditMemoLine],
            },
        ],
        print: [
            ...commonSalesActivities,
            {
                operations: ['lookup'],
                on: [
                    () => xtremSales.nodes.SalesInvoice,
                    () => xtremSales.nodes.SalesCreditMemoLine,
                    () => xtremSales.nodes.SalesInvoiceLineToSalesCreditMemoLine,
                ],
            },
            {
                operations: [
                    'read',
                    'printSalesCreditMemoAndEmail',
                    'setIsPrintedTrue',
                    'printBulkSalesCreditMemo',
                    'beforePrint',
                    'beforePrintSalesCreditMemo',
                    'afterPrintSalesCreditMemo',
                ],
                on: [() => xtremSales.nodes.SalesCreditMemo],
            },
            {
                operations: ['generateReportPdf', 'generateReportZip', 'generateReports'],
                on: [() => xtremReporting.nodes.Report],
            },
            {
                operations: ['getPostingStatusData'],
                on: [() => xtremFinanceData.nodes.FinanceTransaction],
            },
        ],
        post: [
            ...commonSalesActivities,
            {
                operations: ['lookup'],
                on: [
                    () => xtremSales.nodes.SalesInvoice,
                    () => xtremSales.nodes.SalesCreditMemoLine,
                    () => xtremSales.nodes.SalesInvoiceLineToSalesCreditMemoLine,
                ],
            },
            {
                operations: ['read', 'repost', 'resendNotificationForFinance'],
                on: [() => xtremSales.nodes.SalesCreditMemo],
            },
            {
                operations: ['read'],
                on: [() => xtremCommunication.nodes.SysNotificationHistory],
            },
        ],
    },
});
