import * as xtremCommunication from '@sage/xtrem-communication';
import { Activity } from '@sage/xtrem-core';
import * as xtremFinanceData from '@sage/xtrem-finance-data';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremReporting from '@sage/xtrem-reporting';
import * as xtremSales from '..';
import { commonSalesActivities } from '../functions/common';

export const salesInvoice = new Activity({
    description: 'Sales invoice',
    node: () => xtremSales.nodes.SalesInvoice,
    __filename,
    permissions: ['read', 'manage', 'post', 'print'],
    operationGrants: {
        read: [
            ...commonSalesActivities,
            {
                operations: ['lookup'],
                on: [() => xtremSales.nodes.SalesInvoiceLineToSalesCreditMemoLine],
            },
            {
                operations: ['lookup'],
                on: [() => xtremSales.nodes.SalesShipment],
            },
            {
                operations: [
                    'getDealSizeTrend',
                    'getYtdSales',
                    'getMtdSales',
                    'getCustomerYtdSales',
                    'getCustomerYtdGross',
                ],
                on: [() => xtremSales.nodes.SalesInvoice],
            },
        ],
        manage: [
            {
                operations: [
                    'read',
                    'create',
                    'update',
                    'delete',
                    'massPriceDeterminationCalculation',
                    'enforceStatusPosted',
                    'financeIntegrationCheck',
                    'getMtdSales',
                    'getYtdSales',
                ],
                on: [() => xtremSales.nodes.SalesInvoice],
            },
            {
                operations: [
                    'lookup',
                    'massCreateSalesInvoices',
                    'createSalesInvoicesFromShipmentLines',
                    'createSalesInvoicesFromShipment',
                ],
                on: [() => xtremSales.nodes.SalesShipment],
            },
            {
                operations: ['setDimension', 'calculateLineTaxes'],
                on: [() => xtremSales.nodes.SalesInvoiceLine],
            },
            {
                operations: ['lookup'],
                on: [() => xtremSales.nodes.SalesInvoiceLineToSalesCreditMemoLine],
            },
            { operations: ['getValuationCost'], on: [() => xtremMasterData.nodes.ItemSite] },
            ...commonSalesActivities,
        ],

        post: [
            {
                operations: ['lookup'],
                on: [() => xtremSales.nodes.SalesShipment],
            },
            {
                operations: ['read', 'repost', 'resendNotificationForFinance', 'getMtdSales', 'getYtdSales'],
                on: [() => xtremSales.nodes.SalesInvoice],
            },
            {
                operations: ['read'],
                on: [() => xtremCommunication.nodes.SysNotificationHistory],
            },
            {
                operations: ['lookup'],
                on: [() => xtremSales.nodes.SalesInvoiceLineToSalesCreditMemoLine],
            },
            {
                operations: ['getPostingStatusData'],
                on: [() => xtremFinanceData.nodes.FinanceTransaction],
            },
            ...commonSalesActivities,
        ],
        print: [
            {
                operations: [
                    'read',
                    'printSalesInvoiceAndEmail',
                    'printBulkSalesInvoice',
                    'beforePrint',
                    'afterPrint',
                    'getMtdSales',
                    'getYtdSales',
                ],
                on: [() => xtremSales.nodes.SalesInvoice],
            },
            {
                operations: ['generateReportPdf', 'generateReportZip', 'generateReports'],
                on: [() => xtremReporting.nodes.Report],
            },
            {
                operations: ['lookup'],
                on: [() => xtremSales.nodes.SalesInvoiceLineToSalesCreditMemoLine],
            },
            {
                operations: ['lookup'],
                on: [() => xtremSales.nodes.SalesShipment],
            },
            ...commonSalesActivities,
        ],
    },
});
