import * as xtremCommunication from '@sage/xtrem-communication';
import { Activity } from '@sage/xtrem-core';
import * as xtremStockData from '@sage/xtrem-stock-data';
import { commonSalesActivities } from '../functions/common';
import * as xtremSales from '../index';

export const salesReturnReceipt = new Activity({
    description: 'Sales return receipt',
    node: () => xtremSales.nodes.SalesReturnReceipt,
    __filename,
    permissions: ['read', 'manage', 'post'],
    operationGrants: {
        read: [
            ...commonSalesActivities,
            ...xtremStockData.functions.stockDetailLib.stockReceiptOperations,
            { operations: ['lookup'], on: [() => xtremSales.nodes.SalesReturnRequest] },
            {
                operations: ['validateQuantityInSalesUnit'],
                on: [() => xtremSales.nodes.SalesReturnReceipt],
            },
        ],
        manage: [
            ...commonSalesActivities,
            ...xtremStockData.functions.stockDetailLib.stockReceiptOperations,
            {
                operations: ['read', 'create', 'update', 'delete', 'validateQuantityInSalesUnit'],
                on: [() => xtremSales.nodes.SalesReturnReceipt],
            },

            { operations: ['setDimension'], on: [() => xtremSales.nodes.SalesReturnReceiptLine] },
            { operations: ['lookup'], on: [() => xtremSales.nodes.SalesReturnRequest] },
        ],
        post: [
            ...commonSalesActivities,
            ...xtremStockData.functions.stockDetailLib.stockReceiptOperations,
            {
                operations: ['read', 'postToStock', 'repost', 'validateQuantityInSalesUnit'],
                on: [() => xtremSales.nodes.SalesReturnReceipt],
            },

            {
                operations: ['read'],
                on: [() => xtremCommunication.nodes.SysNotificationHistory],
            },
            { operations: ['lookup'], on: [() => xtremSales.nodes.SalesReturnRequest] },
        ],
    },
});
