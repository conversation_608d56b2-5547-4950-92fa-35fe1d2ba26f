import { Activity } from '@sage/xtrem-core';
import * as xtremSales from '..';
import { commonSalesActivities } from '../functions/common';

export const salesReturnRequest = new Activity({
    description: 'Sales return request',
    node: () => xtremSales.nodes.SalesReturnRequest,
    __filename,
    permissions: ['read', 'manage', 'approval', 'changeStatus', 'sendApprovalRequestMail'],
    operationGrants: {
        read: [
            ...commonSalesActivities,
            {
                operations: ['lookup'],
                on: [() => xtremSales.nodes.SalesReturnRequestLineToSalesReturnReceiptLine],
            },
            {
                operations: ['lookup'],
                on: [() => xtremSales.nodes.SalesReturnRequestLineSalesCreditMemoLine],
            },
            {
                operations: ['lookup'],
                on: [() => xtremSales.nodes.SalesCreditMemo],
            },
            {
                operations: ['validateQuantityInSalesUnit'],
                on: [() => xtremSales.nodes.SalesReturnRequest],
            },
        ],
        manage: [
            ...commonSalesActivities,
            {
                operations: [
                    'read',
                    'create',
                    'update',
                    'delete',
                    'validateQuantityInSalesUnit',
                    'confirm',
                    'getFilteredUsers',
                    'financeIntegrationCheck',
                ],
                on: [() => xtremSales.nodes.SalesReturnRequest],
            },
            {
                operations: ['createSalesReturnRequestFromShipmentLines', 'createSalesReturnRequestFromShipments'],
                on: [() => xtremSales.nodes.SalesShipment],
            },
            {
                operations: ['setDimension'],
                on: [() => xtremSales.nodes.SalesReturnRequestLine],
            },
            {
                operations: ['lookup'],
                on: [() => xtremSales.nodes.SalesReturnRequestLineToSalesReturnReceiptLine],
            },
            {
                operations: ['lookup'],
                on: [() => xtremSales.nodes.SalesReturnRequestLineSalesCreditMemoLine],
            },
            {
                operations: ['lookup'],
                on: [() => xtremSales.nodes.SalesCreditMemo],
            },
        ],
        approval: [
            ...commonSalesActivities,
            {
                operations: ['read', 'approve', 'reject', 'validateQuantityInSalesUnit'],
                on: [() => xtremSales.nodes.SalesReturnRequest],
            },
            {
                operations: ['lookup'],
                on: [() => xtremSales.nodes.SalesReturnRequestLineToSalesReturnReceiptLine],
            },
            {
                operations: ['lookup'],
                on: [() => xtremSales.nodes.SalesReturnRequestLineSalesCreditMemoLine],
            },
            {
                operations: ['lookup'],
                on: [() => xtremSales.nodes.SalesCreditMemo],
            },
        ],
        changeStatus: [
            ...commonSalesActivities,
            {
                operations: [
                    'read',
                    'setSalesReturnRequestCloseStatus',
                    'setSalesReturnRequestOpenStatus',
                    'setSalesReturnRequestLineCloseStatus',
                    'setSalesReturnRequestLineOpenStatus',
                    'validateQuantityInSalesUnit',
                ],
                on: [() => xtremSales.nodes.SalesReturnRequest],
            },
            {
                operations: ['lookup'],
                on: [() => xtremSales.nodes.SalesReturnRequestLineToSalesReturnReceiptLine],
            },
            {
                operations: ['lookup'],
                on: [() => xtremSales.nodes.SalesReturnRequestLineSalesCreditMemoLine],
            },
            {
                operations: ['lookup'],
                on: [() => xtremSales.nodes.SalesCreditMemo],
            },
        ],
        sendApprovalRequestMail: [
            ...commonSalesActivities,
            {
                operations: ['read', 'validateQuantityInSalesUnit'],
                on: [() => xtremSales.nodes.SalesReturnRequest],
            },
            {
                operations: ['lookup'],
                on: [() => xtremSales.nodes.SalesReturnRequestLineToSalesReturnReceiptLine],
            },
            {
                operations: ['lookup'],
                on: [() => xtremSales.nodes.SalesReturnRequestLineSalesCreditMemoLine],
            },
            {
                operations: ['lookup'],
                on: [() => xtremSales.nodes.SalesCreditMemo],
            },
        ],
    },
});
