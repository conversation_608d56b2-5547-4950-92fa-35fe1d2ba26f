import { Activity } from '@sage/xtrem-core';
import { SalesCreditMemoReason } from '../nodes/sales-credit-memo-reason';

export const salesCreditMemoReason = new Activity({
    description: 'Sales memo credit reason',
    node: () => SalesCreditMemoReason,
    __filename,
    permissions: ['read', 'manage'],
    operationGrants: {
        manage: [
            {
                operations: ['create', 'update', 'delete'],
                on: [() => SalesCreditMemoReason],
            },
        ],
    },
});
