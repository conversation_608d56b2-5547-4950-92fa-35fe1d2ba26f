import type { OperationGrant } from '@sage/xtrem-core';
import { Activity } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremReporting from '@sage/xtrem-reporting';
import * as xtremStockData from '@sage/xtrem-stock-data';
import { commonSalesActivities } from '../functions/common';
import { SalesOrder, SalesOrderLine } from '../nodes';
import {
    SalesOrderLineToSalesInvoiceLine,
    SalesOrderLineToSalesShipmentLine,
} from '../nodes/base-line-to-sales-document-line';

const { ItemSite } = xtremMasterData.nodes;
const { Report } = xtremReporting.nodes;

const commonOperations: OperationGrant[] = [
    ...commonSalesActivities,
    {
        operations: ['lookup'],
        on: [() => SalesOrderLineToSalesShipmentLine, () => SalesOrderLineToSalesInvoiceLine],
    },
    {
        operations: ['orderAssignmentQuantitySum'],
        on: [() => SalesOrder],
    },
];

export const salesOrder = new Activity({
    description: 'Sales order',
    node: () => SalesOrder,
    __filename,
    permissions: ['read', 'manage', 'confirm', 'changeStatus', 'print'],
    operationGrants: {
        read: commonOperations,
        manage: [
            ...commonOperations,
            {
                operations: [
                    'read',
                    'create',
                    'update',
                    'delete',
                    'subWorkDays',
                    'addWorkDays',
                    'financeIntegrationCheck',
                    'requestAutoAllocation',
                    'autoAllocate',
                    'massPriceDeterminationCalculation',
                    'getLastOrderDate',
                    'getOrderBookAmount',
                ],
                on: [() => SalesOrder],
            },
            ...xtremStockData.functions.allocationLib.allocationOperation,
            {
                operations: ['getValuationCost', 'getProjectedStock'],
                on: [() => ItemSite],
            },
            {
                operations: ['massAutoAllocation', 'calculateLineTaxes', 'setDimension', 'assignedOrderAssignment'],
                on: [() => SalesOrderLine],
            },
        ],
        confirm: [
            ...commonOperations,
            {
                operations: ['read', 'confirmSalesOrder'],
                on: [() => SalesOrder],
            },
        ],
        changeStatus: [
            ...commonOperations,
            {
                operations: [
                    'read',
                    'setSalesOrderLineCloseStatus',
                    'setSalesOrderLineOpenStatus',
                    'closeSalesOrder',
                    'setSalesOrderOpenStatus',
                ],
                on: [() => SalesOrder],
            },
        ],
        print: [
            ...commonOperations,
            {
                operations: [
                    'read',
                    'setIsPrintedTrue',
                    'printSalesOrderAndEmail',
                    'printBulkSalesOrders',
                    'beforePrintSalesOrder',
                    'afterPrintSalesOrder',
                ],
                on: [() => SalesOrder],
            },
            {
                operations: ['generateReportPdf', 'generateReportZip', 'generateReports'],
                on: [() => Report],
            },
        ],
    },
});
