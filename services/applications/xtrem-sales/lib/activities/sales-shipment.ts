import * as xtremCommunication from '@sage/xtrem-communication';
import { Activity } from '@sage/xtrem-core';
import * as xtremReporting from '@sage/xtrem-reporting';
import * as xtremStockData from '@sage/xtrem-stock-data';
import { commonSalesActivities } from '../functions/common';
import * as xtremSales from '../index';

export const salesShipment = new Activity({
    description: 'Sales shipment',
    node: () => xtremSales.nodes.SalesShipment,
    __filename,
    permissions: ['read', 'manage', 'post', 'confirm', 'print'],
    operationGrants: {
        read: [
            {
                operations: ['lookup'],
                on: [() => xtremSales.nodes.SalesOrderLineToSalesShipmentLine],
            },
            {
                operations: ['lookup'],
                on: [() => xtremSales.nodes.SalesOrder],
            },
            {
                operations: ['lookup'],
                on: [() => xtremSales.nodes.SalesShipmentLineToSalesInvoiceLine],
            },

            {
                operations: ['lookup'],
                on: [() => xtremSales.nodes.SalesShipmentLineToSalesReturnReceiptLine],
            },
            {
                operations: ['lookup'],
                on: [() => xtremSales.nodes.SalesShipmentLineToSalesReturnRequestLine],
            },
            ...commonSalesActivities,
            ...xtremStockData.functions.allocationLib.allocationOperation,
        ],
        manage: [
            {
                operations: ['read', 'create', 'update', 'delete', 'addWorkDays', 'resynchronizeStatus'],
                on: [() => xtremSales.nodes.SalesShipment],
            },
            {
                operations: [
                    'lookup',
                    'massCreateSalesShipments',
                    'createShipmentsFromOrder',
                    'createSalesShipmentsFromOrderLines',
                ],
                on: [() => xtremSales.nodes.SalesOrder],
            },
            {
                operations: ['setDimension'],
                on: [() => xtremSales.nodes.SalesShipmentLine],
            },
            ...commonSalesActivities,
            ...xtremStockData.functions.allocationLib.allocationOperation,

            {
                operations: ['lookup'],
                on: [() => xtremSales.nodes.SalesOrderLineToSalesShipmentLine],
            },
            {
                operations: ['lookup'],
                on: [() => xtremSales.nodes.SalesShipmentLineToSalesInvoiceLine],
            },

            {
                operations: ['lookup'],
                on: [() => xtremSales.nodes.SalesShipmentLineToSalesReturnReceiptLine],
            },
            {
                operations: ['lookup'],
                on: [() => xtremSales.nodes.SalesShipmentLineToSalesReturnRequestLine],
            },
            {
                operations: ['read', 'create', 'update', 'unbilledAccountReceivableInquiry'],
                on: [() => xtremSales.nodes.UnbilledAccountReceivableInputSet],
            },
        ],
        post: [
            ...commonSalesActivities,
            ...xtremStockData.functions.allocationLib.allocationOperation,
            {
                operations: ['read', 'repost', 'resynchronizeStatus', 'resendNotificationForFinance', 'postToStock'],
                on: [() => xtremSales.nodes.SalesShipment],
            },
            {
                operations: ['read'],
                on: [() => xtremCommunication.nodes.SysNotificationHistory],
            },
            {
                operations: ['lookup'],
                on: [() => xtremSales.nodes.SalesOrderLineToSalesShipmentLine],
            },
            {
                operations: ['lookup'],
                on: [() => xtremSales.nodes.SalesOrder],
            },
            {
                operations: ['lookup'],
                on: [() => xtremSales.nodes.SalesShipmentLineToSalesInvoiceLine],
            },

            {
                operations: ['lookup'],
                on: [() => xtremSales.nodes.SalesShipmentLineToSalesReturnReceiptLine],
            },
            {
                operations: ['lookup'],
                on: [() => xtremSales.nodes.SalesShipmentLineToSalesReturnRequestLine],
            },
        ],
        confirm: [
            { operations: ['read', 'revoke'], on: [() => xtremSales.nodes.SalesShipment] },
            ...commonSalesActivities,
            ...xtremStockData.functions.allocationLib.allocationOperation,
            {
                operations: ['lookup'],
                on: [() => xtremSales.nodes.SalesOrderLineToSalesShipmentLine],
            },
            {
                operations: ['lookup'],
                on: [() => xtremSales.nodes.SalesOrder],
            },
            {
                operations: ['lookup'],
                on: [() => xtremSales.nodes.SalesShipmentLineToSalesInvoiceLine],
            },

            {
                operations: ['lookup'],
                on: [() => xtremSales.nodes.SalesShipmentLineToSalesReturnReceiptLine],
            },
            {
                operations: ['lookup'],
                on: [() => xtremSales.nodes.SalesShipmentLineToSalesReturnRequestLine],
            },
        ],
        print: [
            {
                operations: [
                    'read',
                    'printBulkPackingSlip',
                    'afterPrintPackingSlip',
                    'beforePrintPackingSlip',
                    'beforePrintSalesShipmentPickList',
                ],
                on: [() => xtremSales.nodes.SalesShipment],
            },
            {
                operations: ['generateReportPdf', 'generateReportZip', 'generateReports'],
                on: [() => xtremReporting.nodes.Report],
            },
            ...commonSalesActivities,
            ...xtremStockData.functions.allocationLib.allocationOperation,
            {
                operations: ['lookup'],
                on: [() => xtremSales.nodes.SalesOrderLineToSalesShipmentLine],
            },
            {
                operations: ['lookup'],
                on: [() => xtremSales.nodes.SalesOrder],
            },
            {
                operations: ['lookup'],
                on: [() => xtremSales.nodes.SalesShipmentLineToSalesInvoiceLine],
            },

            {
                operations: ['lookup'],
                on: [() => xtremSales.nodes.SalesShipmentLineToSalesReturnReceiptLine],
            },
            {
                operations: ['lookup'],
                on: [() => xtremSales.nodes.SalesShipmentLineToSalesReturnRequestLine],
            },
        ],
    },
});
