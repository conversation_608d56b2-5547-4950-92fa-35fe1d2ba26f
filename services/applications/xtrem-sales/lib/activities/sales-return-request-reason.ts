import { Activity } from '@sage/xtrem-core';
import { SalesReturnRequestReason } from '../nodes/sales-return-request-reason';

export const salesReturnRequestReason = new Activity({
    description: 'Sales return request reason',
    node: () => SalesReturnRequestReason,
    __filename,
    permissions: ['read', 'manage'],

    operationGrants: {
        manage: [
            {
                operations: ['create', 'update', 'delete'],
                on: [() => SalesReturnRequestReason],
            },
        ],
    },
});
