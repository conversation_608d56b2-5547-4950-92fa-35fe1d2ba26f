import { SalesOrder } from '../pages/sales-order';
import type { ProformaSalesOrderParameters } from './interfaces/sales-order-actions-functions';

/**
 * Opens the proforma invoice dialog and the async mutation to print the proformaInvoice report on backend
 * @param option : create or read the record
 * @param dialogParameters : parameters to be sent into the proforma invoice dialog
 * @param shouldRefresh : refresh the proformaInvoices tab
 */
export async function proformaInvoiceDialog(parameters: ProformaSalesOrderParameters<'Object'>) {
    const proformaInvoicePageInstance = parameters.pageOrWidget;
    const result = await parameters.pageOrWidget.$.dialog.page(
        '@sage/xtrem-sales/ProformaInvoice',
        {
            option: parameters.option,
            dialogParameters: JSON.stringify({
                ...parameters.dialogParameters,
                salesOrder: JSON.stringify(parameters.dialogParameters.salesOrder),
            }),
        },
        {
            resolveOnCancel: true,
            size: 'large',
        },
    );

    if (result._id) {
        proformaInvoicePageInstance.$.loader.isHidden = false;
        await parameters.pageOrWidget.$.graph
            .node('@sage/xtrem-reporting/Report')
            .asyncOperations.generateReportAndNotifyUser.runToCompletion(true, {
                reportName: 'proformaInvoice',
                reportTemplateName: '',
                reportSettings: {
                    variables: JSON.stringify({
                        salesOrder: parameters.dialogParameters.salesOrder._id,
                    }),
                    documentTitle: result._id,
                },
            })
            .execute();

        proformaInvoicePageInstance.$.loader.isHidden = true;
    }

    if (parameters.shouldRefresh && parameters.pageOrWidget instanceof SalesOrder) {
        await parameters.pageOrWidget.proformaInvoices.refresh();
    }
}
