import { SalesOrder } from '../pages/sales-order';
import type { PrintSalesOrderParameters } from './interfaces/sales-order-actions-functions';

export async function printSalesOrderAction(parameters: PrintSalesOrderParameters) {
    if (parameters.number) {
        const reportName = parameters.status === 'quote' ? 'salesOrderQuote' : 'salesOrder';

        await parameters.pageOrWidget.$.dialog.page(
            '@sage/xtrem-reporting/PrintDocument',
            {
                reportName,
                order: parameters._id,
                documentTitle: parameters.number,
            },
            {
                size: 'extra-large',
                resolveOnCancel: true,
            },
        );
    }

    parameters.pageOrWidget.$.loader.isHidden = false;
    if (parameters.isCalledFromRecordPage && parameters.pageOrWidget instanceof SalesOrder) {
        await parameters.pageOrWidget.$.refreshNavigationPanel();
        await parameters.pageOrWidget.$.router.refresh();
    }
    parameters.pageOrWidget.$.loader.isHidden = true;
}
