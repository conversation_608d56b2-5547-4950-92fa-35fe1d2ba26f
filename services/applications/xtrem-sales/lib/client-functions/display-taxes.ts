import { TotalTaxCalculator } from '@sage/xtrem-tax/build/lib/client-functions/classes/total-tax-calculator';
import { displayTaxes as displayTaxesBase } from '@sage/xtrem-tax/build/lib/client-functions/display-taxes';
import type { DisplayTaxesDataCountryRequired } from '@sage/xtrem-tax/build/lib/client-functions/interfaces/display-taxes';
import type * as ui from '@sage/xtrem-ui';

/**
 * Gets you into the tax panel,
 * it handle the tax per document line which concerns: the opening of the panel
 * @param pageInstance the calling page's this (used to interact with the framework)
 * @param taxes a string containing stringified taxes collection
 * @returns Promise
 */
export function displayTaxes(
    pageInstance: ui.Page & {
        taxDetails?: ui.fields.Table;
        totalTaxAmountAdjusted?: ui.fields.Numeric;
        totalTaxAmount?: ui.fields.Numeric;
    },
    rowItem: { uiTaxes?: string; _id?: string | number | boolean },
    data: DisplayTaxesDataCountryRequired,
): Promise<void | any> {
    const additionalLogic = async (result: any) => {
        const totalTaxCalculator = TotalTaxCalculator.getInstance();
        await totalTaxCalculator.updateTaxData(
            JSON.parse(rowItem.uiTaxes ?? '{taxEngine:"", taxes:[]}'),
            result.uiTaxes,
        );
        if (pageInstance.taxDetails && pageInstance.totalTaxAmountAdjusted && pageInstance.totalTaxAmount) {
            await totalTaxCalculator.updateTaxDetails(
                pageInstance.taxDetails,
                pageInstance.totalTaxAmountAdjusted,
                pageInstance.totalTaxAmount,
            );
        }
    };
    return displayTaxesBase(pageInstance, rowItem, data, additionalLogic);
}
