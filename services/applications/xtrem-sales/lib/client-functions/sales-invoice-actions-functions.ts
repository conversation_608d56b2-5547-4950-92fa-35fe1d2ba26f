import { asyncArray } from '@sage/xtrem-async-helper';
import { extractEdges } from '@sage/xtrem-client';
import { confirmDialogWithAcceptButtonText } from '@sage/xtrem-distribution/build/lib/client-functions/common';
import type { FinanceTransactionBinding } from '@sage/xtrem-finance-data-api';
import type {
    SalesInvoiceLineToSalesCreditMemoLine as SalesInvoiceLineNode,
    SalesInvoice as SalesInvoiceNode,
} from '@sage/xtrem-sales-api';
import type { GraphApi } from '@sage/xtrem-system-api';
import * as ui from '@sage/xtrem-ui';
import type { PartialCollectionValueWithIds } from '@sage/xtrem-ui/build/lib/component/types';
import { getDimensionsForSalesLines, getValuesForSetDimensionsFromMainList } from './finance-integration';
import type {
    CreditParameter,
    GetDocumentSysIdParameters,
    GetSalesInvoiceLinesParameter,
    InvoicePageParameter,
    InvoicePostingParameter,
    PostParameter,
    SendSalesInvoiceParameters,
    SetDimensionActionParameter,
} from './interfaces/sales-invoice-actions-functions';

export async function getDocumentSysId(parameters: GetDocumentSysIdParameters) {
    const arInvoices = await parameters.salesInvoicePage.$.graph
        .node('@sage/xtrem-finance-data/FinanceTransaction')
        .queries.getPostingStatusData(
            {
                documentSysId: true,
            },
            {
                documentNumber: parameters.number || '',
            },
        )
        .execute();

    if (arInvoices.length > 0) {
        return arInvoices[0].documentSysId;
    }
    return 0;
}

export async function printSalesInvoiceAction(salesPage: ui.Page<GraphApi>, recordId: string) {
    if (
        !(await salesPage.$.graph
            .node('@sage/xtrem-sales/SalesInvoice')
            .mutations.beforePrint(true, { invoice: recordId })
            .execute())
    )
        return false;

    await salesPage.$.dialog.page(
        '@sage/xtrem-reporting/PrintDocument',
        {
            reportName: 'salesInvoice',
            invoice: recordId,
        },
        {
            size: 'extra-large',
            resolveOnCancel: true,
        },
    );

    await salesPage.$.graph
        .node('@sage/xtrem-sales/SalesInvoice')
        .mutations.afterPrint(true, { invoice: recordId })
        .execute();

    salesPage.$.loader.isHidden = false;
    await salesPage.$.router.refresh();
    await salesPage.$.refreshNavigationPanel();
    salesPage.$.loader.isHidden = true;
    return true;
}

export async function sendSalesInvoiceAction(parameters: SendSalesInvoiceParameters) {
    parameters.salesInvoicePage.$.loader.isHidden = false;
    if (
        !(await parameters.salesInvoicePage.$.graph
            .node('@sage/xtrem-sales/SalesInvoice')
            .mutations.printSalesInvoiceAndEmail(true, {
                ...parameters.email,
                invoice: parameters.invoice._id,
            })
            .execute())
    ) {
        parameters.salesInvoicePage.$.showToast(
            ui.localize('@sage/xtrem-sales/pages__sales_invoice__email_not_sent', 'Sales invoice was not sent.'),
            { type: 'error' },
        );
    }

    parameters.salesInvoicePage.$.showToast(
        ui.localize('@sage/xtrem-sales/pages__sales_invoice__email_sent', 'Sales invoice sent to {{email}}.', {
            email: parameters.email.contactEmail,
        }),
        { type: 'success' },
    );

    parameters.salesInvoicePage.$.setPageClean();
    await parameters.salesInvoicePage.$.refreshNavigationPanel();
    await parameters.salesInvoicePage.$.router.refresh();
    parameters.salesInvoicePage.$.loader.isHidden = true;
    return true;
}

async function getSalesInvoiceLinesForDimensions(parameters: GetSalesInvoiceLinesParameter) {
    return extractEdges(
        await parameters.salesInvoicePage.$.graph
            .node('@sage/xtrem-sales/SalesInvoiceLine')
            .query(
                ui.queryUtils.edgesSelector(
                    {
                        _id: true,
                        storedDimensions: true,
                        storedAttributes: true,
                        document: {
                            number: true,
                        },
                    },

                    {
                        filter: {
                            document: {
                                number: parameters.recordNumber,
                            },
                        },
                    },
                ),
            )
            .execute(),
    );
}

export async function setDimensions(parameters: SetDimensionActionParameter) {
    let dataDetermined = false;
    let dimensionsToSet = '';
    let attributesToSet = '';
    const linesToSetDimensions = await getSalesInvoiceLinesForDimensions({
        salesInvoicePage: parameters.salesInvoicePage,
        recordNumber: parameters.recordNumber,
    });

    await asyncArray(linesToSetDimensions).some(async lineToSetDimensions => {
        const loopResponse = false;
        if (!dataDetermined) {
            const { defaultedFromItem, defaultDimensionsAttributes } = await getValuesForSetDimensionsFromMainList({
                page: parameters.salesInvoicePage as ui.Page<GraphApi>,
                site: parameters.site || null,
                customer: parameters.customer || null,
            });
            const { resultDimensionsToSet, resultAttributesToSet, resultDataDetermined } =
                await getDimensionsForSalesLines({
                    line: lineToSetDimensions,
                    salesPage: parameters.salesInvoicePage as ui.Page<GraphApi>,
                    defaultDimensionsAttributes,
                    defaultedFromItem,
                });
            if (!resultDataDetermined) {
                return true;
            }
            dimensionsToSet = resultDimensionsToSet;
            attributesToSet = resultAttributesToSet;
            dataDetermined = resultDataDetermined;
        }
        if (dataDetermined) {
            await parameters.salesInvoicePage.$.graph
                .node('@sage/xtrem-sales/SalesInvoiceLine')
                .mutations.setDimension(true, {
                    baseDocumentItemLine: lineToSetDimensions._id,
                    storedDimensions: dimensionsToSet,
                    storedAttributes: attributesToSet,
                })
                .execute();
        }
        return loopResponse;
    });
    parameters.salesInvoicePage.$.showToast(
        ui.localize('@sage/xtrem-sales/pages__sales_invoice__apply_dimensions_success', 'Dimensions applied.'),
        { type: 'success' },
    );
}

async function getDisplayStatus(parameters: InvoicePageParameter) {
    const salesInvoice = extractEdges(
        await parameters.salesInvoicePage.$.graph
            .node('@sage/xtrem-sales/SalesInvoice')
            .query(
                ui.queryUtils.edgesSelector(
                    {
                        displayStatus: true,
                    },

                    {
                        filter: {
                            _id: parameters.recordId,
                        },
                    },
                ),
            )
            .execute(),
    ) as SalesInvoiceNode[];
    return salesInvoice[0].displayStatus;
}

async function checkForUpdate(parameters: InvoicePageParameter) {
    let refreshCounter: number;
    refreshCounter = 0;
    const checkForDisplayStatus = async () => {
        const displayStatus = await getDisplayStatus({
            salesInvoicePage: parameters.salesInvoicePage,
            recordId: parameters.recordId,
        });
        if (displayStatus === 'posted') {
            await parameters.salesInvoicePage.$.router.refresh(true);
            await parameters.salesInvoicePage.$.refreshNavigationPanel();
            parameters.salesInvoicePage.$.loader.isHidden = true;
            return;
        }
        refreshCounter += 1;
        if (refreshCounter < 10) {
            await new Promise(resolve => {
                setTimeout(() => resolve(checkForDisplayStatus()), 1000);
            });
        }
    };
    await checkForDisplayStatus();
}

export async function invoicePostingAction(parameters: InvoicePostingParameter) {
    // Disable post button so the user cannot post twice
    parameters.salesInvoicePage.post.isDisabled = true;
    parameters.salesInvoicePage.$.loader.isHidden = false;

    const salesInvoiceAfterPosting = await parameters.salesInvoicePage.$.graph
        .node('@sage/xtrem-sales/SalesInvoice')
        .mutations.post({ status: true }, { invoice: parameters.recordId })
        .execute();

    if (['inProgress', 'posted'].includes(salesInvoiceAfterPosting.status))
        parameters.salesInvoicePage.$.showToast(
            ui.localize('@sage/xtrem-sales/pages__sales_invoice__posted', 'The sales invoice was posted.'),
            { type: 'success' },
        );
    await checkForUpdate({ salesInvoicePage: parameters.salesInvoicePage, recordId: parameters.recordId });
    parameters.salesInvoicePage.$.loader.isHidden = true;
}

export async function postAction(parameters: PostParameter) {
    if (
        parameters.taxCalculationStatus === 'done' ||
        (parameters.taxCalculationStatus === 'failed' && parameters.status === 'inProgress')
    ) {
        if (
            await confirmDialogWithAcceptButtonText(
                parameters.salesInvoicePage,
                ui.localize('@sage/xtrem-sales/pages__sales_invoice__post_action_dialog_title', 'Confirm posting'),
                ui.localize(
                    '@sage/xtrem-sales/pages__sales_invoice__post_action_dialog_content',
                    'You are about to post this sales invoice.',
                ),
                ui.localize('@sage/xtrem-sales/pages-confirm-post', 'Post'),
            )
        ) {
            await invoicePostingAction({
                salesInvoicePage: parameters.salesInvoicePage,
                recordId: parameters.recordId,
                isCalledFromRecordPage: parameters.isCalledFromRecordPage,
            });
        }
    }
}

async function getLastCreditMemoCreatedFromSalesInvoice(parameters: InvoicePageParameter) {
    const creditCreated = extractEdges(
        await parameters.salesInvoicePage.$.graph
            .node('@sage/xtrem-sales/SalesInvoiceLineToSalesCreditMemoLine')
            .query(
                ui.queryUtils.edgesSelector(
                    {
                        document: {
                            document: {
                                _id: true,
                                number: true,
                            },
                        },
                    },
                    {
                        filter: { linkedDocument: { document: { _in: [parameters.recordId] } } },
                    },
                ),
            )
            .execute(),
    ) as SalesInvoiceLineNode[];
    return creditCreated[0]?.document.document._id;
}

function createCreditInvoiceMutation(parameters: InvoicePageParameter) {
    return parameters.salesInvoicePage.$.graph
        .node('@sage/xtrem-sales/SalesInvoice')
        .mutations.createSalesCreditMemosFromInvoices(
            {
                status: true,
                numberOfCreditMemos: true,
                lineErrors: { lineNumber: true, linePosition: true, message: true },
            },
            { salesDocuments: [`_id:${parameters.recordId}`] },
        )
        .execute();
}
export async function createCreditAction(parameters: CreditParameter) {
    if (
        await confirmDialogWithAcceptButtonText(
            parameters.salesInvoicePage,
            ui.localize(
                '@sage/xtrem-sales/pages__sales_invoice__create_credit_memo_dialog_title',
                'Confirm credit memo creation',
            ),
            ui.localize(
                '@sage/xtrem-sales/pages__sales_invoice__create_credit_memo_dialog_content',
                'You are about to create a credit memo from this sales invoice.',
            ),
            ui.localize('@sage/xtrem-sales/pages-confirm-create', 'Create'),
        )
    ) {
        parameters.salesInvoicePage.$.loader.isHidden = false;
        const creditMemo = await createCreditInvoiceMutation(parameters);

        if (creditMemo.numberOfCreditMemos > 0) {
            parameters.salesInvoicePage.$.showToast(
                ui.localize('@sage/xtrem-sales/pages__sales_invoice__credit_memo_created', 'Record created'),
                { type: 'success' },
            );
            if (parameters.isCalledFromRecordPage) {
                parameters.salesInvoicePage.$.setPageClean();
                const creditCreated = await getLastCreditMemoCreatedFromSalesInvoice(parameters);
                parameters.salesInvoicePage.$.router.goTo(`@sage/xtrem-sales/SalesCreditMemo`, {
                    _id: creditCreated,
                });
            }
        } else {
            parameters.salesInvoicePage.$.showToast(
                ui.localize(
                    '@sage/xtrem-sales/pages__sales_invoice__credit_exception',
                    'The credit memo could not be created.',
                ),
                { type: 'error' },
            );
        }
        if (!parameters.isCalledFromRecordPage) {
            await parameters.salesInvoicePage.$.router.refresh(true);
            await parameters.salesInvoicePage.$.refreshNavigationPanel();
        }
        parameters.salesInvoicePage.$.loader.isHidden = true;
    }
}

export async function postDetails(
    pageInstance: ui.Page,
    postData: PartialCollectionValueWithIds<FinanceTransactionBinding>[],
) {
    await asyncArray(postData.filter(document => document.status === 'posted')).forEach(async postedDocument => {
        switch (postedDocument?.targetDocumentType) {
            case 'journalEntry':
                await pageInstance.$.dialog.message(
                    'info',
                    ui.localize(
                        '@sage/xtrem-sales/pages__sales_invoice__repost_documents_already_posted_title',
                        'Repost',
                    ),
                    ui.localize(
                        '@sage/xtrem-sales/pages__sales_invoice__repost_journal_entry_already_posted',
                        'The journal entry was posted successfully. You can access this document in your financial solution to take further action.',
                    ),
                );
                break;
            case 'accountsReceivableInvoice':
                await pageInstance.$.dialog.message(
                    'info',
                    ui.localize(
                        '@sage/xtrem-sales/pages__sales_invoice__repost_documents_already_posted_title',
                        'Repost',
                    ),
                    ui.localize(
                        '@sage/xtrem-sales/pages__sales_invoice__repost_accounts_receivable_invoice_already_posted',
                        'The accounts receivable invoice was posted successfully. You can access this document in your financial solution to take further action.',
                    ),
                );
                break;
            default:
                break;
        }
    });
}
