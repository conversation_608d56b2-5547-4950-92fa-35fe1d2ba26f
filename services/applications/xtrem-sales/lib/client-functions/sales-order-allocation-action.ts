import * as MasterDataUtils from '@sage/xtrem-master-data/build/lib/client-functions/utils';
import * as ui from '@sage/xtrem-ui';
import { SalesOrder } from '../pages/sales-order';
import type { AllocationActionParameters } from './interfaces/sales-order-actions-functions';

export async function allocationAction(parameters: AllocationActionParameters) {
    parameters.pageOrWidget.$.loader.isHidden = false;
    const requestAutoAllocation = parameters.pageOrWidget.$.graph
        .node('@sage/xtrem-sales/SalesOrder')
        .mutations.requestAutoAllocation(true, {
            salesOrder: parameters._id,
            requestType: parameters.allocationType,
        });

    if (parameters.pageOrWidget instanceof SalesOrder) {
        await requestAutoAllocation.execute();
    } else {
        try {
            await requestAutoAllocation.execute();
        } catch (error) {
            parameters.pageOrWidget.$.showToast(MasterDataUtils.formatError(parameters.pageOrWidget, error), {
                type: 'error',
            });
        }
    }
    parameters.pageOrWidget.$.loader.isHidden = true;

    if (parameters.allocationType === 'deallocation') {
        await parameters.pageOrWidget.$.dialog.message(
            'info',
            ui.localize(
                '@sage/xtrem-sales/pages__sales_order__auto_deallocate_message_title',
                'Deallocation request submitted',
            ),
            ui.localize(
                '@sage/xtrem-sales/pages__sales_order__auto_deallocate_message',
                'The deallocation request was submitted.',
            ),
        );
    } else {
        await parameters.pageOrWidget.$.dialog.message(
            'info',
            ui.localize(
                '@sage/xtrem-sales/pages__sales_order__auto_allocate_message_title',
                'Allocation request submitted',
            ),
            ui.localize(
                '@sage/xtrem-sales/pages__sales_order__auto_allocate_message',
                'The allocation request was submitted.',
            ),
        );
    }
    if (parameters.pageOrWidget instanceof SalesOrder) {
        await parameters.pageOrWidget.$.router.refresh();
        await parameters.pageOrWidget.$.refreshNavigationPanel();
    }
}
