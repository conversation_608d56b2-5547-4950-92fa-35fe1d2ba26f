import type * as isDisabledButtonsCommon from '@sage/xtrem-system/build/lib/client-functions/interfaces/is-disabled-buttons';
import type * as isHiddenButtonsCommon from '@sage/xtrem-system/build/lib/client-functions/interfaces/is-hidden-buttons';
import type * as isDisabledButtons from './interfaces/is-disabled-buttons-sales-credit-memo';
import type * as isHiddenButtons from './interfaces/is-hidden-buttons-sales-credit-memo';

export function isDisabledButtonSaveAction(
    data: isDisabledButtonsCommon.SaveOrCancelParameters<isDisabledButtons.StatusParameters>,
) {
    if (data.recordId) {
        if (data.parameters.status === 'draft') {
            return !data.isDirty;
        }
        return true;
    }
    return !data.isDirty;
}

export function isDisabledButtonCancelAction(
    data: isDisabledButtonsCommon.SaveOrCancelParameters<isDisabledButtons.StatusParameters>,
) {
    if (data.recordId) {
        if (data.parameters.status === 'draft') {
            return !data.isDirty;
        }
        return true;
    }
    return !data.isDirty;
}

export function isHiddenButtonDeleteAction(
    data: isHiddenButtonsCommon.DeleteParameters<isHiddenButtons.StatusParameters>,
) {
    if (data.recordId && data.parameters.status === 'draft') {
        return false;
    }
    return true;
}

/* -------------------*/

export function isHiddenButtonPostAction(
    data: isHiddenButtonsCommon.BusinessButtonParameters<isHiddenButtons.StatusTaxCalcStatusParameters>,
) {
    if (
        data.recordId &&
        ((data.parameters.status === 'draft' && data.parameters.taxCalculationStatus === 'done') ||
            (data.parameters.status === 'inProgress' && data.parameters.taxCalculationStatus === 'failed'))
    ) {
        return data.isDirty;
    }
    return true;
}

export function isDisabledButtonPostAction(
    data: isDisabledButtonsCommon.BusinessButtonParameters<isDisabledButtons.PostPrintParameters>,
) {
    return isHiddenButtonPostAction(data);
}

/* -------------------*/

export function isHiddenButtonRepostAction(
    data: isDisabledButtonsCommon.BusinessButtonParameters<isDisabledButtons.RepostParameters>,
) {
    if (data.recordId && data.parameters.fromNotificationHistory) {
        return !data.isDirty;
    }
    return true;
}

export function isDisabledButtonRepostAction(
    data: isHiddenButtonsCommon.BusinessButtonParameters<isHiddenButtons.RepostParameters>,
) {
    return isHiddenButtonRepostAction(data);
}
/* -------------------*/

export function isDisabledLinePhantomRow(
    data: isDisabledButtonsCommon.PhantomRowButtonParameters<isDisabledButtons.LinePhantomRowParameters>,
) {
    if (
        data.parameters &&
        data.parameters.status !== 'posted' &&
        data.parameters.status !== 'inProgress' &&
        data.parameters.status !== 'error' &&
        data.parameters.site &&
        data.parameters.billToCustomer &&
        data.parameters.currency
    ) {
        return false;
    }
    return true;
}

/* -------------------*/

export function isHiddenButtonPrintAction(
    data: isHiddenButtonsCommon.HeaderQuickActionButtonParameters<isHiddenButtons.StatusTaxCalcStatusParameters>,
) {
    return !data.recordId || data.parameters.status === 'inProgress';
}

export function isDisabledButtonPrintAction(
    data: isDisabledButtonsCommon.HeaderQuickActionButtonParameters<isDisabledButtons.PostPrintParameters>,
) {
    return !data.recordId || data.isDirty || (data.parameters && data.parameters.status === 'inProgress');
}

/* -------------------*/

export function isHiddenButtonSendMailAction(
    data: isHiddenButtonsCommon.HeaderQuickActionButtonParameters<isHiddenButtons.StatusTaxCalcStatusParameters>,
) {
    if (
        data.recordId &&
        data.parameters.status &&
        ['posted', 'inProgress'].includes(data.parameters.status) &&
        data.parameters.taxCalculationStatus !== 'failed'
    ) {
        return false;
    }
    return true;
}

export function isDisabledButtonSendMailAction(
    data: isDisabledButtonsCommon.HeaderQuickActionButtonParameters<isDisabledButtons.StatusParameters>,
) {
    return !data.recordId || data.isDirty;
}

export function isHiddenButtonDefaultDimensionAction(
    data: isHiddenButtonsCommon.HeaderDropDownActionButtonParameters<isHiddenButtons.StatusTaxCalcStatusParameters>,
) {
    if (
        data.recordId &&
        data.parameters &&
        data.parameters.status &&
        ['inProgress', 'posted', 'error'].includes(data.parameters.status)
    ) {
        return true;
    }
    return false;
}

export function isDisabledButtonDefaultDimensionAction(
    data: isDisabledButtonsCommon.HeaderDropDownActionButtonParameters<isDisabledButtons.DefaultDimensionParameters>,
) {
    if (
        data.parameters &&
        data.parameters.site &&
        data.parameters.dueDate &&
        data.parameters.paymentTerm &&
        data.parameters.date &&
        data.parameters.currency
    ) {
        return false;
    }
    return true;
}
