import { asyncArray } from '@sage/xtrem-async-helper';
import { extractEdges } from '@sage/xtrem-client';
import { confirmDialogWithAcceptButtonText } from '@sage/xtrem-distribution/build/lib/client-functions/common';
import type { SalesCreditMemo as SalesCreditMemoNode } from '@sage/xtrem-sales-api';
import type { GraphApi } from '@sage/xtrem-system-api';
import * as ui from '@sage/xtrem-ui';
import type { PartialCollectionValueWithIds } from '@sage/xtrem-ui/build/lib/component/types';
import { getDimensionsForSalesLines, getValuesForSetDimensionsFromMainList } from './finance-integration';
import type {
    CreditMemoPageParameter,
    CreditMemoPostingParameter,
    GetSalesCreditMemoLinesParameter,
    PostDetailsParameter,
    PostParameter,
    PrintSalesCreditMemoParameters,
    SetDimensionActionParameter,
    UpdatePrintStatusParameters,
} from './interfaces/sales-credit-memo-actions-functions';

export async function updatePrintStatusAction(parameters: UpdatePrintStatusParameters) {
    const isSetIsPrintedTrue = await parameters.salesCreditMemoPage.$.graph
        .node('@sage/xtrem-sales/SalesCreditMemo')
        .mutations.setIsPrintedTrue(true, { creditMemo: parameters._id })
        .execute();
    if (!isSetIsPrintedTrue) {
        throw new Error(
            ui.localize(
                '@sage/xtrem-sales/pages__sales_credit_memo__printed_status_not_updated',
                'Unable to update credit memo {{number}} printed status.',
                { number: parameters.number },
            ),
        );
    }
    if (parameters.isPrinted && isSetIsPrintedTrue) {
        parameters.salesCreditMemoPage.$.showToast(
            ui.localize(
                '@sage/xtrem-sales/pages__sales_credit_memo__printed',
                'The sales credit memo has been printed.',
            ),
            { type: 'success' },
        );
    }
}

export async function printSalesCreditMemoAction(parameters: PrintSalesCreditMemoParameters) {
    if (parameters._id) {
        await parameters.salesCreditMemoPage.$.dialog.page(
            '@sage/xtrem-reporting/PrintDocument',
            {
                reportName: 'salesCreditMemo',
                creditMemo: parameters._id,
            },
            {
                size: 'extra-large',
                resolveOnCancel: true,
            },
        );
    }
    parameters.salesCreditMemoPage.$.loader.isHidden = false;
    if (parameters._id) {
        await updatePrintStatusAction({
            salesCreditMemoPage: parameters.salesCreditMemoPage,
            _id: parameters._id,
            status: parameters.status,
            isPrinted: true,
            number: parameters.number,
        });
    }
    await parameters.salesCreditMemoPage.$.router.refresh();
    await parameters.salesCreditMemoPage.$.refreshNavigationPanel();
    parameters.salesCreditMemoPage.$.loader.isHidden = true;
}

export function canPrintCreditMemo(parameters: PrintSalesCreditMemoParameters) {
    return parameters.salesCreditMemoPage.$.graph
        .node('@sage/xtrem-sales/SalesCreditMemo')
        .mutations.beforePrint(true, { creditMemo: parameters._id })
        .execute();
}

async function getSalesCreditMemoLinesForDimensions(parameters: GetSalesCreditMemoLinesParameter) {
    return extractEdges(
        await parameters.salesCreditMemoPage.$.graph
            .node('@sage/xtrem-sales/SalesCreditMemoLine')
            .query(
                ui.queryUtils.edgesSelector(
                    {
                        _id: true,
                        storedDimensions: true,
                        storedAttributes: true,
                        document: {
                            number: true,
                        },
                    },

                    {
                        filter: {
                            document: {
                                number: parameters.recordNumber,
                            },
                        },
                    },
                ),
            )
            .execute(),
    );
}

export async function setDimensions(parameters: SetDimensionActionParameter) {
    let dataDetermined = false;
    let dimensionsToSet = '';
    let attributesToSet = '';
    const linesToSetDimensions = await getSalesCreditMemoLinesForDimensions({
        salesCreditMemoPage: parameters.salesCreditMemoPage,
        recordNumber: parameters.recordNumber,
    });

    await asyncArray(linesToSetDimensions).some(async lineToSetDimensions => {
        const loopResponse = false;
        if (!dataDetermined) {
            const { defaultedFromItem, defaultDimensionsAttributes } = await getValuesForSetDimensionsFromMainList({
                page: parameters.salesCreditMemoPage as ui.Page<GraphApi>,
                site: parameters.site || null,
                customer: parameters.customer || null,
            });
            const { resultDimensionsToSet, resultAttributesToSet, resultDataDetermined } =
                await getDimensionsForSalesLines({
                    line: lineToSetDimensions,
                    salesPage: parameters.salesCreditMemoPage as ui.Page<GraphApi>,
                    defaultDimensionsAttributes,
                    defaultedFromItem,
                });
            if (!resultDataDetermined) {
                return true;
            }
            dimensionsToSet = resultDimensionsToSet;
            attributesToSet = resultAttributesToSet;
            dataDetermined = resultDataDetermined;
        }
        if (dataDetermined) {
            await parameters.salesCreditMemoPage.$.graph
                .node('@sage/xtrem-sales/SalesCreditMemoLine')
                .mutations.setDimension(true, {
                    baseDocumentItemLine: lineToSetDimensions._id,
                    storedDimensions: dimensionsToSet,
                    storedAttributes: attributesToSet,
                })
                .execute();
        }

        return loopResponse;
    });
    parameters.salesCreditMemoPage.$.showToast(
        ui.localize('@sage/xtrem-sales/pages__sales_credit_memo__apply_dimensions_success', 'Dimensions applied.'),
        { type: 'success' },
    );
}

async function getDisplayStatus(parameters: CreditMemoPageParameter) {
    const salesCreditMemo = extractEdges(
        await parameters.creditMemoPage.$.graph
            .node('@sage/xtrem-sales/SalesCreditMemo')
            .query(
                ui.queryUtils.edgesSelector(
                    {
                        displayStatus: true,
                    },

                    {
                        filter: {
                            _id: parameters.recordId,
                        },
                    },
                ),
            )
            .execute(),
    ) as SalesCreditMemoNode[];
    return salesCreditMemo[0].displayStatus;
}

async function checkForUpdate(parameters: CreditMemoPageParameter) {
    let refreshCounter: number;
    refreshCounter = 0;
    const checkForDisplayStatus = async () => {
        const displayStatus = await getDisplayStatus({
            creditMemoPage: parameters.creditMemoPage,
            recordId: parameters.recordId,
        });
        if (displayStatus === 'posted') {
            await parameters.creditMemoPage.$.router.refresh(true);
            await parameters.creditMemoPage.$.refreshNavigationPanel();
            parameters.creditMemoPage.$.loader.isHidden = true;
            return;
        }
        refreshCounter += 1;
        if (refreshCounter < 10) {
            await new Promise(resolve => {
                setTimeout(() => resolve(checkForDisplayStatus()), 1000);
            });
        }
    };
    await checkForDisplayStatus();
}

export async function creditMemoPostingAction(parameters: CreditMemoPostingParameter) {
    // Disable post button so the user cannot post twice
    parameters.creditMemoPage.post.isHidden = true;
    parameters.creditMemoPage.$.loader.isHidden = false;

    const postResult = await parameters.creditMemoPage.$.graph
        .node('@sage/xtrem-sales/SalesCreditMemo')
        .mutations.post(
            {
                wasSuccessful: true,
                message: true,
            },
            { creditMemo: parameters.recordId },
        )
        .execute();

    if (!postResult.wasSuccessful) {
        parameters.creditMemoPage.$.showToast(
            `**${ui.localize('@sage/xtrem-sales/pages__sales_invoice__post_errors', 'Posting errors:')}**\n${
                postResult.message
            }`,
            { type: 'error', timeout: 20000 },
        );
    } else {
        parameters.creditMemoPage.$.showToast(postResult.message, { type: 'success' });
    }
    await checkForUpdate({ creditMemoPage: parameters.creditMemoPage, recordId: parameters.recordId });
    parameters.creditMemoPage.$.loader.isHidden = true;
}

export async function postAction(parameters: PostParameter) {
    if (
        parameters.taxCalculationStatus === 'done' ||
        (parameters.taxCalculationStatus === 'failed' && parameters.status === 'inProgress')
    ) {
        if (
            await confirmDialogWithAcceptButtonText(
                parameters.creditMemoPage,
                ui.localize('@sage/xtrem-sales/pages__sales_credit_memo__post_action_dialog_title', 'Confirm posting'),
                ui.localize(
                    '@sage/xtrem-sales/pages__sales_credit_memo__post_action_dialog_content',
                    'You are about to post this sales credit memo.',
                ),
                ui.localize('@sage/xtrem-sales/pages-confirm-post', 'Post'),
            )
        ) {
            await creditMemoPostingAction({
                creditMemoPage: parameters.creditMemoPage,
                recordId: parameters.recordId,
                isCalledFromRecordPage: parameters.isCalledFromRecordPage,
            });
        }
    }
}

export async function postDetails(
    pageInstance: ui.Page,
    postData: PartialCollectionValueWithIds<PostDetailsParameter>[],
) {
    await asyncArray(postData.filter(document => document.status === 'posted')).forEach(async postedDocument => {
        switch (postedDocument?.documentType) {
            case 'journalEntry': {
                await pageInstance.$.dialog.message(
                    'info',
                    ui.localize(
                        '@sage/xtrem-sales/pages__sales_credit_memo__repost_documents_already_posted_title',
                        'Repost',
                    ),
                    ui.localize(
                        '@sage/xtrem-sales/pages__sales_credit_memo__repost_journal_entry_already_posted',
                        'The journal entry was posted successfully. You can access this document in your financial solution to take further action.',
                    ),
                );
                break;
            }
            case 'accountsReceivableInvoice': {
                await pageInstance.$.dialog.message(
                    'info',
                    ui.localize(
                        '@sage/xtrem-sales/pages__sales_credit_memo__repost_documents_already_posted_title',
                        'Repost',
                    ),
                    ui.localize(
                        '@sage/xtrem-sales/pages__sales_credit_memo__repost_accounts_receivable_credit_memo_already_posted',
                        'The accounts receivable credit memo was posted successfully. You can access this document in your financial solution to take further action.',
                    ),
                );
                break;
            }
            default: {
                break;
            }
        }
    });
}
