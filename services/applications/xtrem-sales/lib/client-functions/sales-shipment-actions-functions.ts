import { asyncArray } from '@sage/xtrem-async-helper';
import { extractEdges, withoutEdges } from '@sage/xtrem-client';
import { confirmDialogWithAcceptButtonText } from '@sage/xtrem-distribution/build/lib/client-functions/common';
import type { CustomerOnHoldType, GraphApi as MasterApi } from '@sage/xtrem-master-data-api';
import * as MasterDataUtils from '@sage/xtrem-master-data/build/lib/client-functions/utils';
import type { GraphApi, SalesShipmentLine, SalesShipment as SalesShipmentNode } from '@sage/xtrem-sales-api';
import * as StockDocumentHelper from '@sage/xtrem-stock-data/build/lib/client-functions/stock-document-helper';
import type { GraphApi as SystemApi } from '@sage/xtrem-system-api';
import * as ui from '@sage/xtrem-ui';
import { getDimensionsForSalesLines, getValuesForSetDimensionsFromMainList } from './finance-integration';
import type {
    ActionParameters,
    ActionParametersWithId,
    AllLinesAreAllocatedParameters,
    ConfirmActionParameters,
    CreateInvoiceParameters,
    CreateSalesOutputDocumentsReturnValues,
    ErrorMessageParameters,
    GetSalesShipmentLinesForAllocationsParameter,
    PostActionParameters,
    PrintShipmentParameters,
    SalesShipmentPageWithId,
    SetDimensionActionParameter,
} from './interfaces/sales-shipment-actions-functions';

export async function printPackingSlip(parameters: ActionParametersWithId) {
    await parameters.salesShipmentPage.$.dialog.page(
        '@sage/xtrem-reporting/PrintDocument',
        { reportName: 'packingSlip', shipment: parameters.recordId },
        { size: 'extra-large', resolveOnCancel: true },
    );

    parameters.salesShipmentPage.$.loader.isHidden = false;
    parameters.salesShipmentPage.$.showToast(
        ui.localize('@sage/xtrem-sales/pages__sales_shipment__printed', 'The sales shipment has been printed.'),
        { type: 'success' },
    );
    await parameters.salesShipmentPage.$.router.refresh();
    await parameters.salesShipmentPage.$.refreshNavigationPanel();
    parameters.salesShipmentPage.$.loader.isHidden = true;
}

export async function printShipmentAction(parameters: PrintShipmentParameters) {
    if (parameters.status === 'readyToProcess') {
        await parameters.salesShipmentPage.$.dialog.page(
            '@sage/xtrem-reporting/PrintDocument',
            { reportName: 'salesShipmentPickList', shipment: parameters.recordId },
            { size: 'extra-large' },
        );
    } else {
        await printPackingSlip({
            salesShipmentPage: parameters.salesShipmentPage,
            recordNumber: parameters.recordNumber,
            recordId: parameters.recordId,
        });
    }
}

async function getSalesShipmentLinesForDimensions(parameters: ActionParameters) {
    return extractEdges(
        await parameters.salesShipmentPage.$.graph
            .node('@sage/xtrem-sales/SalesShipmentLine')
            .query(
                ui.queryUtils.edgesSelector(
                    {
                        _id: true,
                        storedDimensions: true,
                        storedAttributes: true,
                        document: {
                            number: true,
                        },
                    },

                    {
                        filter: {
                            document: {
                                number: parameters.recordNumber,
                            },
                        },
                    },
                ),
            )
            .execute(),
    );
}

export async function setDimensions(parameters: SetDimensionActionParameter) {
    let dataDetermined = false;
    let dimensionsToSet = '';
    let attributesToSet = '';
    const linesToSetDimensions = await getSalesShipmentLinesForDimensions({
        salesShipmentPage: parameters.salesShipmentPage,
        recordNumber: parameters.recordNumber,
    });

    await asyncArray(linesToSetDimensions).some(async lineToSetDimensions => {
        const loopResponse = false;
        if (!dataDetermined) {
            const { defaultedFromItem, defaultDimensionsAttributes } = await getValuesForSetDimensionsFromMainList({
                page: parameters.salesShipmentPage as unknown as ui.Page<SystemApi>,
                site: parameters.site || null,
                customer: parameters.customer || null,
            });
            const { resultDimensionsToSet, resultAttributesToSet, resultDataDetermined } =
                await getDimensionsForSalesLines({
                    line: lineToSetDimensions,
                    salesPage: parameters.salesShipmentPage as unknown as ui.Page<MasterApi>,
                    defaultDimensionsAttributes,
                    defaultedFromItem,
                });
            if (!resultDataDetermined) {
                return true;
            }
            dimensionsToSet = resultDimensionsToSet;
            attributesToSet = resultAttributesToSet;
            dataDetermined = resultDataDetermined;
        }
        if (dataDetermined) {
            await parameters.salesShipmentPage.$.graph
                .node('@sage/xtrem-sales/SalesShipmentLine')
                .mutations.setDimension(true, {
                    baseDocumentItemLine: lineToSetDimensions._id,
                    storedDimensions: dimensionsToSet,
                    storedAttributes: attributesToSet,
                })
                .execute();
        }
        return loopResponse;
    });
    parameters.salesShipmentPage.$.showToast(
        ui.localize('@sage/xtrem-sales/pages__sales_Shipment__apply_dimensions_success', 'Dimensions applied.'),
        { type: 'success' },
    );
}

async function getSalesShipmentLinesForAllocations(parameters: GetSalesShipmentLinesForAllocationsParameter) {
    return withoutEdges<Partial<SalesShipmentLine>>(
        await parameters.salesShipmentPage.$.graph
            .node('@sage/xtrem-sales/SalesShipmentLine')
            .query(
                ui.queryUtils.edgesSelector(
                    {
                        quantityInStockUnit: true,
                        quantityAllocated: true,
                        stockTransactionStatus: true,
                        allocationStatus: true,
                    },

                    {
                        filter: {
                            document: {
                                number: parameters.recordNumber,
                            },
                        },
                    },
                ),
            )
            .execute(),
    );
}

export async function allLinesAreAllocated(parameters: AllLinesAreAllocatedParameters): Promise<boolean> {
    const linesToAllocation = await getSalesShipmentLinesForAllocations({
        salesShipmentPage: parameters.salesShipmentPage,
        recordNumber: parameters.recordNumber,
    });
    return linesToAllocation
        .filter(
            lineFilter =>
                lineFilter.stockTransactionStatus !== 'completed' && lineFilter.allocationStatus !== 'notManaged',
        )
        .every(line => Number(line.quantityInStockUnit) - Number(line.quantityAllocated) === 0);
}

export async function confirmAction(parameters: ConfirmActionParameters) {
    if (
        parameters.recordId !== null &&
        (await confirmDialogWithAcceptButtonText(
            parameters.salesShipmentPage,
            ui.localize('@sage/xtrem-sales/pages__sales_shipment__confirm_dialog_title', 'Confirm sales shipment'),
            ui.localize(
                '@sage/xtrem-sales/pages__sales_shipment__confirm_dialog_content',
                "You are about to set this sales shipment to 'Confirmed'.",
            ),
            ui.localize('@sage/xtrem-sales/pages_confirm_button', 'Confirm'),
        ))
    ) {
        parameters.salesShipmentPage.$.loader.isHidden = false;
        const result = await parameters.salesShipmentPage.$.graph
            .node('@sage/xtrem-sales/SalesShipment')
            .mutations.confirm(
                { status: true },
                {
                    salesShipment: parameters.recordId,
                    isSafeToRetry: false,
                },
            )
            .execute();

        if (result.status === 'readyToShip') {
            parameters.salesShipmentPage.$.showToast(
                ui.localize('@sage/xtrem-sales/pages__sales_shipment__confirm_status_updated', 'Status updated.'),
                { type: 'success' },
            );
            if (parameters.isCalledFromRecordPage && parameters.recordId) {
                parameters.salesShipmentPage.$.setPageClean();
                parameters.salesShipmentPage.$.router.goTo(`@sage/xtrem-sales/SalesShipment`, {
                    _id: parameters.recordId,
                });
            }
        }

        parameters.salesShipmentPage.$.loader.isHidden = true;
    }
}

async function getDisplayStatus(parameters: SalesShipmentPageWithId) {
    const salesShipment = extractEdges(
        await parameters.salesShipmentPage.$.graph
            .node('@sage/xtrem-sales/SalesShipment')
            .query(
                ui.queryUtils.edgesSelector(
                    {
                        displayStatus: true,
                    },

                    {
                        filter: {
                            _id: parameters.recordId,
                        },
                    },
                ),
            )
            .execute(),
    ) as SalesShipmentNode[];
    return salesShipment[0].displayStatus;
}

async function checkForUpdate(parameters: SalesShipmentPageWithId) {
    // TODO: This is a hack. It can be removed when XT-23948 is ready.
    let refreshCounter: number;
    refreshCounter = 0;
    const checkForDisplayStatus = async () => {
        const displayStatus = await getDisplayStatus({
            salesShipmentPage: parameters.salesShipmentPage,
            recordId: parameters.recordId,
        });
        if (displayStatus === 'shipped') {
            await parameters.salesShipmentPage.$.router.refresh(true);
            await parameters.salesShipmentPage.$.refreshNavigationPanel();
            parameters.salesShipmentPage.$.loader.isHidden = true;
            return;
        }
        refreshCounter += 1;
        if (refreshCounter < 5) {
            await new Promise(resolve => {
                setTimeout(() => resolve(checkForDisplayStatus()), 1000);
            });
        }
    };
    await checkForDisplayStatus();
}

export function showOnHoldPostMessage(salesShipmentPage: ui.Page) {
    return confirmDialogWithAcceptButtonText(
        salesShipmentPage,
        ui.localize('@sage/xtrem-sales/pages__sales_shipment_post_on_hold_action_dialog_title', 'Customer on hold'),
        ui.localize(
            '@sage/xtrem-sales/pages__sales_shipment___post_on_hold_action__action_dialog_content',
            'You are about to post the sales shipment for an on hold customer.',
        ),
        ui.localize('@sage/xtrem-sales/pages-confirm-continue', 'Continue'),
    );
}

export function showPostMessage(salesShipmentPage: ui.Page) {
    return confirmDialogWithAcceptButtonText(
        salesShipmentPage,
        ui.localize('@sage/xtrem-sales/pages__sales_shipment__post_action_dialog_title', 'Confirm posting'),
        ui.localize(
            '@sage/xtrem-sales/pages__sales_shipment__post_action_dialog_content',
            "You are about to set this sales shipment to 'Shipped'.",
        ),
        ui.localize('@sage/xtrem-sales/pages-confirm-confirm', 'Confirm'),
    );
}

export async function postAction(parameters: PostActionParameters) {
    const post =
        parameters.isOnHold && parameters.customerOnHoldCheck === 'warning'
            ? await showOnHoldPostMessage(parameters.salesShipmentPage)
            : await showPostMessage(parameters.salesShipmentPage);

    if (!post) {
        return;
    }

    // Disable post button so the user cannot post twice
    parameters.salesShipmentPage.post.isDisabled = true;
    parameters.salesShipmentPage.revert.isDisabled = true;
    parameters.salesShipmentPage.revert.isHidden = true;

    const postResult = await parameters.salesShipmentPage.$.graph
        .node('@sage/xtrem-sales/SalesShipment')
        .mutations.postToStock(true, { salesShipment: parameters.recordId })
        .execute();

    StockDocumentHelper.catchPostingError(postResult);

    parameters.salesShipmentPage.$.showToast(
        ui.localize('@sage/xtrem-sales/pages__sales_shipment__posted', 'The sales shipment has been posted.'),
        { type: 'success' },
    );

    await checkForUpdate({ salesShipmentPage: parameters.salesShipmentPage, recordId: parameters.recordId });
}

export async function revertAction(parameters: SalesShipmentPageWithId) {
    if (
        await confirmDialogWithAcceptButtonText(
            parameters.salesShipmentPage,
            ui.localize(
                '@sage/xtrem-sales/pages__sales_shipment__revert_action_dialog_title',
                'Confirm status reversal',
            ),
            ui.localize(
                '@sage/xtrem-sales/pages__sales_shipment__revert_action_dialog_content',
                "You are about to revert the status of this sales shipment to 'Ready to process'.",
            ),
            ui.localize('@sage/xtrem-sales/pages-confirm-confirm', 'Confirm'),
        )
    ) {
        parameters.salesShipmentPage.$.loader.isHidden = false;
        const result = await parameters.salesShipmentPage.$.graph
            .node('@sage/xtrem-sales/SalesShipment')
            .mutations.revoke(true, { _id: parameters.recordId })
            .execute();
        if (result) {
            parameters.salesShipmentPage.$.showToast(String(result), { timeout: 5000, type: 'success' });
            await parameters.salesShipmentPage.$.router.refresh();
            parameters.salesShipmentPage.$.setPageClean();
            await parameters.salesShipmentPage.$.refreshNavigationPanel();
            parameters.salesShipmentPage.$.loader.isHidden = true;
        }
    }
}

function createSalesInvoiceMutation(parameters: SalesShipmentPageWithId) {
    return parameters.salesShipmentPage.$.graph
        .node('@sage/xtrem-sales/SalesShipment')
        .mutations.createSalesInvoicesFromShipment({ _id: true }, { salesShipment: parameters.recordId })
        .execute();
}

export async function createInvoiceAction(parameters: CreateInvoiceParameters) {
    if (
        await confirmDialogWithAcceptButtonText(
            parameters.salesShipmentPage,
            ui.localize(
                '@sage/xtrem-sales/pages__sales_shipment__create_invoice_dialog_title',
                'Confirm invoice creation',
            ),
            ui.localize(
                '@sage/xtrem-sales/pages__sales_shipment__create_invoice_dialog_content',
                'You are about to create an invoice from this sales shipment.',
            ),
            ui.localize('@sage/xtrem-sales/pages-confirm-create', 'Create'),
        )
    ) {
        parameters.salesShipmentPage.$.loader.isHidden = false;
        const createdInvoices = await createSalesInvoiceMutation(parameters);

        if (createdInvoices && createdInvoices.length > 0) {
            parameters.salesShipmentPage.$.showToast(
                ui.localize('@sage/xtrem-sales/pages__sales_shipment__invoice_created', 'Sales invoice created'),
                { type: 'success' },
            );
            parameters.salesShipmentPage.$.setPageClean();

            if (parameters.isCalledFromRecordPage) {
                parameters.salesShipmentPage.$.router.goTo(`@sage/xtrem-sales/SalesInvoice`, {
                    _id: createdInvoices[0]._id,
                });
            }
        }

        if (!parameters.isCalledFromRecordPage) {
            await parameters.salesShipmentPage.$.router.refresh(true);
            await parameters.salesShipmentPage.$.refreshNavigationPanel();
        }
        parameters.salesShipmentPage.$.loader.isHidden = true;
    }
}

/**
 * Runs the createSalesReturnRequestFromShipments mutation with parameters
 * Returns a promise of return request created
 */
function executeCreateSalesReturnRequestMutation(
    parameters: SalesShipmentPageWithId,
): Promise<CreateSalesOutputDocumentsReturnValues> {
    return parameters.salesShipmentPage.$.graph
        .node('@sage/xtrem-sales/SalesShipment')
        .mutations.createSalesReturnRequestFromShipments(
            {
                status: true,
                numberOfReturnRequests: true,
                documentsCreated: { number: true, _id: true },
                lineErrors: { lineNumber: true, linePosition: true, message: true },
            },
            { salesDocuments: [`_id:${parameters.recordId}`] },
        )
        .execute();
}

export async function requestReturnAction(parameters: SalesShipmentPageWithId) {
    if (
        await confirmDialogWithAcceptButtonText(
            parameters.salesShipmentPage,
            ui.localize(
                '@sage/xtrem-sales/pages__sales_shipment__create_return_request_dialog_title',
                'Confirm return request creation',
            ),
            ui.localize(
                '@sage/xtrem-sales/pages__sales_shipment__create_return_request_dialog_content',
                'You are about to create a return request from this sales shipment.',
            ),
            ui.localize('@sage/xtrem-sales/pages-confirm-create', 'Create'),
        )
    ) {
        parameters.salesShipmentPage.$.loader.isHidden = false;
        await executeCreateSalesReturnRequestMutation(parameters);
        parameters.salesShipmentPage.$.showToast(
            ui.localize(
                '@sage/xtrem-sales/pages__sales_shipment__return_request_created',
                'Sales return request created',
            ),
            { type: 'success' },
        );
        parameters.salesShipmentPage.$.setPageClean();
        await parameters.salesShipmentPage.$.router.refresh(true);
        await parameters.salesShipmentPage.$.refreshNavigationPanel();
        parameters.salesShipmentPage.$.loader.isHidden = true;
    }
}

export function showErrorMessage(parameters: ErrorMessageParameters) {
    parameters.salesShipmentPage.$.loader.isHidden = true;
    parameters.salesShipmentPage.$.showToast(
        ui.localize(
            '@sage/xtrem-sales/pages__sales_shipment__return_request_exception',
            'The return request creation failed: {{exception}}',
            {
                exception: MasterDataUtils.formatError(
                    parameters.salesShipmentPage,
                    parameters.error as string | (Error & { errors: Array<any> }),
                ),
            },
        ),
        { type: 'error' },
    );
}

/**
 * Returns a error message or a question if the customer is hold
 * @param isOnHold - isOnHold field from customer
 * @param customerOnHoldCheck - customerOnHoldCheck from company
 * @returns boolean
 */
export function addLinesOnHoldCustomer(
    salesShipmentPage: ui.Page<GraphApi>,
    isOnHold: boolean,
    customerOnHoldCheck: CustomerOnHoldType | undefined,
): Promise<boolean> {
    if (isOnHold) {
        switch (customerOnHoldCheck) {
            case 'blocking':
                throw new Error(
                    ui.localize(
                        '@sage/xtrem-sales/pages__sales_shipment__selection_impossible_because_on_hold_bill_to_customer',
                        'The sales order lines cannot be selected: The Bill-to customer is on hold.',
                    ),
                );

            case 'warning':
                return confirmDialogWithAcceptButtonText(
                    salesShipmentPage,
                    ui.localize(
                        '@sage/xtrem-sales/pages__sales_shipment__add_lines_on_hold_action_dialog_title',
                        'Customer on hold',
                    ),
                    ui.localize(
                        '@sage/xtrem-sales/pages__sales_shipment__add_lines_on_hold_action__action_dialog_content',
                        'You are about to select sales orders for an on hold customer.',
                    ),
                    ui.localize('@sage/xtrem-sales/pages-confirm-continue', 'Continue'),
                );
            default:
                break;
        }
    }
    return Promise.resolve(true);
}

export async function resynchronizeShipmentStatus(pageInstance: ui.Page<GraphApi>, salesShipment: string) {
    if (
        await confirmDialogWithAcceptButtonText(
            pageInstance,
            ui.localize('@sage/xtrem-sales/client_functions__sales__resync_status_title', 'Check and update status'),
            ui.localize(
                '@sage/xtrem-sales/client_functions__sales__resync_status_message',
                'You are about to update the status.',
            ),
            ui.localize('@sage/xtrem-sales/client_functions__sales__resync_status_continue', 'Continue'),
        )
    ) {
        const statusRefreshed = await pageInstance.$.graph
            .node('@sage/xtrem-sales/SalesShipment')
            .mutations.resynchronizeStatus(true, { salesShipment })
            .execute();
        if (statusRefreshed) {
            await pageInstance.$.router.refresh();
        }
    }
}
