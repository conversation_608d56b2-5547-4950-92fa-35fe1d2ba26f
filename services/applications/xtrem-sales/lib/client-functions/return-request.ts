import { extractEdges } from '@sage/xtrem-client';
import type { GraphApi } from '@sage/xtrem-sales-api';
import * as ui from '@sage/xtrem-ui';

/**
 * Checks if a site has sales return requests pending approval.
 * @param _id : the id of the site.
 * @param graph
 */
export async function salesReturnRequestsPendingApproval(
    graph: ui.GraphQLApi<GraphApi>,
    sysIdSite: string,
): Promise<boolean> {
    const salesReturnRequestsPendingApprovalQuery = extractEdges(
        await graph
            .node('@sage/xtrem-sales/SalesReturnRequest')
            .aggregate.query(
                ui.queryUtils.edgesSelector(
                    { group: { _id: { _by: 'value' } } },
                    { filter: { site: { _eq: sysIdSite }, approvalStatus: { _eq: 'pendingApproval' } }, first: 1 },
                ),
            )
            .execute(),
    );

    return !!salesReturnRequestsPendingApprovalQuery.length;
}
