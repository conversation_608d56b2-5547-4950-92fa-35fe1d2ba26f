import { asyncArray } from '@sage/xtrem-async-helper';
import { extractEdges } from '@sage/xtrem-client';
import type { SalesReturnReceipt as SalesReturnReceiptNode } from '@sage/xtrem-sales-api';
import * as StockDocumentHelper from '@sage/xtrem-stock-data/build/lib/client-functions/stock-document-helper';
import type { GraphApi } from '@sage/xtrem-system-api';
import * as ui from '@sage/xtrem-ui';
import { getDimensionsForSalesLines, getValuesForSetDimensionsFromMainList } from './finance-integration';
import type {
    GetSalesReturnReceiptLinesParameter,
    PostActionParameter,
    SetDimensionActionParameter,
} from './interfaces/sales-return-receipt-actions-functions';

async function getSalesReturnReceiptLinesForDimensions(parameters: GetSalesReturnReceiptLinesParameter) {
    return extractEdges(
        await parameters.salesReturnReceiptPage.$.graph
            .node('@sage/xtrem-sales/SalesReturnReceiptLine')
            .query(
                ui.queryUtils.edgesSelector(
                    {
                        _id: true,
                        storedDimensions: true,
                        storedAttributes: true,
                        document: {
                            number: true,
                        },
                    },

                    {
                        filter: {
                            document: {
                                number: parameters.recordNumber,
                            },
                        },
                    },
                ),
            )
            .execute(),
    );
}

export async function setDimensions(parameters: SetDimensionActionParameter) {
    let dataDetermined = false;
    let dimensionsToSet = '';
    let attributesToSet = '';
    const linesToSetDimensions = await getSalesReturnReceiptLinesForDimensions({
        salesReturnReceiptPage: parameters.salesReturnReceiptPage,
        recordNumber: parameters.recordNumber,
    });

    await asyncArray(linesToSetDimensions).some(async lineToSetDimensions => {
        const loopResponse = false;
        if (!dataDetermined) {
            const { defaultedFromItem, defaultDimensionsAttributes } = await getValuesForSetDimensionsFromMainList({
                page: parameters.salesReturnReceiptPage as ui.Page<GraphApi>,
                site: parameters.site || null,
                customer: parameters.customer || null,
            });
            const { resultDimensionsToSet, resultAttributesToSet, resultDataDetermined } =
                await getDimensionsForSalesLines({
                    line: lineToSetDimensions,
                    salesPage: parameters.salesReturnReceiptPage as ui.Page<GraphApi>,
                    defaultDimensionsAttributes,
                    defaultedFromItem,
                });
            if (!resultDataDetermined) {
                return true;
            }
            dimensionsToSet = resultDimensionsToSet;
            attributesToSet = resultAttributesToSet;
            dataDetermined = resultDataDetermined;
        }
        if (dataDetermined) {
            await parameters.salesReturnReceiptPage.$.graph
                .node('@sage/xtrem-sales/SalesReturnReceiptLine')
                .mutations.setDimension(true, {
                    baseDocumentItemLine: lineToSetDimensions._id,
                    storedDimensions: dimensionsToSet,
                    storedAttributes: attributesToSet,
                })
                .execute();
        }
        return loopResponse;
    });
    parameters.salesReturnReceiptPage.$.showToast(
        ui.localize('@sage/xtrem-sales/pages__sales_return_receipt__apply_dimensions_success', 'Dimensions applied.'),
        { type: 'success' },
    );
}

async function getStockTransactionStatus(parameters: PostActionParameter) {
    const [{ stockTransactionStatus }] = extractEdges(
        await parameters.salesReturnReceiptPage.$.graph
            .node('@sage/xtrem-sales/SalesReturnReceipt')
            .query(
                ui.queryUtils.edgesSelector<SalesReturnReceiptNode>(
                    {
                        stockTransactionStatus: true,
                    },

                    {
                        filter: {
                            _id: parameters.recordId,
                        },
                    },
                ),
            )
            .execute(),
    );
    return stockTransactionStatus;
}

async function checkForUpdate(parameters: PostActionParameter) {
    // TODO: This is a hack. It can be removed when XT-23948 is ready.
    let refreshCounter = 0;
    const checkForStockTransactionStatus = async () => {
        try {
            const stockTransactionStatus = await getStockTransactionStatus({
                salesReturnReceiptPage: parameters.salesReturnReceiptPage,
                recordId: parameters.recordId,
            });

            if (['completed', 'error'].includes(stockTransactionStatus)) {
                await parameters.salesReturnReceiptPage.$.router.refresh(true);
                await parameters.salesReturnReceiptPage.$.refreshNavigationPanel();
                parameters.salesReturnReceiptPage.$.loader.isHidden = true;
                return;
            }

            refreshCounter += 1;
            if (refreshCounter <= 10) {
                setTimeout(() => {
                    checkForStockTransactionStatus().catch(e => {
                        ui.console.error(e);
                    });
                }, 1000);
            }
        } catch (e) {
            parameters.salesReturnReceiptPage.$.loader.isHidden = true;
            ui.console.error(e);
        }
    };

    await checkForStockTransactionStatus();
}

export async function post(parameters: PostActionParameter) {
    StockDocumentHelper.catchPostingError(
        await parameters.salesReturnReceiptPage.$.graph
            .node('@sage/xtrem-sales/SalesReturnReceipt')
            .mutations.postToStock(true, { documentIds: [parameters.recordId] })
            .execute(),
        parameters.salesReturnReceiptPage,
    );
    await checkForUpdate({ salesReturnReceiptPage: parameters.salesReturnReceiptPage, recordId: parameters.recordId });
    parameters.salesReturnReceiptPage.$.showToast(
        ui.localize('@sage/xtrem-sales/pages__sales_return_receipt__post_from_main_row', 'Receipt posted'),
        { type: 'success' },
    );
}
