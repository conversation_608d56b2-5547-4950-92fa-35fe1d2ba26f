/**
 * Updates the dates fields in inquiry pages to get fromDate and toDate consistency
 * @param update the field to be updated: from or to
 * @param dateField object that contains the values of the from field and to field
 * @returns string | null
 */
export function fillInquiryDates(update: 'from' | 'to', dateField: { from?: string; to?: string }): string | null {
    // Case we have both fields
    if (dateField.to && dateField.from) {
        if (dateField.from > dateField.to) {
            switch (update) {
                case 'from':
                    return dateField.to;
                case 'to':
                    return dateField.from;
                default:
                    return null;
            }
        }
        // In case one of the fields is undefined
    } else if (dateField.from && update === 'to' && !dateField.to) {
        return dateField.from;
    } else if (dateField.to && update === 'from' && !dateField.from) {
        return dateField.to;
    }

    // If none of the above, we should do any change, so we must return the original value
    switch (update) {
        case 'from':
            return dateField.from || null;
        case 'to':
            return dateField.to || null;
        default:
            return null;
    }
}
