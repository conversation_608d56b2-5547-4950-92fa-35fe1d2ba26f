import { asyncArray } from '@sage/xtrem-async-helper';
import type { ExtractEdgesPartial, Filter } from '@sage/xtrem-client';
import { DateValue } from '@sage/xtrem-date-time';
import * as dimensionPanelHelpers from '@sage/xtrem-finance-data/build/lib/client-functions/dimension-panel-helpers';
import { getConcatenatedAddress } from '@sage/xtrem-master-data/build/lib/shared-functions/address-functions';
import type { SalesOrderLine, SalesOrderLineToSalesShipmentLine, SalesShipmentLine } from '@sage/xtrem-sales-api';
import * as ui from '@sage/xtrem-ui';
import { isArray } from 'lodash';
import type { SalesShipment as SalesShipmentPage } from '../pages/sales-shipment';
import { selectedQuantityValidation } from './common';
import * as PillColorPurchase from './pill-color';

export function convertOrderLineToOrderToShipmentLine(
    salesOrderLine: ui.PartialCollectionValue<SalesOrderLine> & { selectedQuantity: number },
): ui.PartialCollectionValue<SalesOrderLineToSalesShipmentLine> {
    return {
        quantityInStockUnit: String(
            (salesOrderLine.selectedQuantity ? +salesOrderLine.selectedQuantity : 0) *
                (salesOrderLine.unitToStockUnitConversionFactor ? +salesOrderLine.unitToStockUnitConversionFactor : 0),
        ),
        quantity: salesOrderLine.quantity,
        linkedDocument: salesOrderLine != null ? salesOrderLine : undefined,
    };
}

function convertOrderLineToShipmentLine(
    shipmentPage: SalesShipmentPage,
    orderLine: ui.PartialCollectionValue<SalesOrderLine & { selectedQuantity: number }>,
): ui.PartialCollectionValue<SalesShipmentLine> {
    const salesOrderLine = convertOrderLineToOrderToShipmentLine({
        ...orderLine,
        selectedQuantity: orderLine.selectedQuantity ?? 0,
    });
    const currency = shipmentPage.currency.value;
    if (!currency) {
        throw new Error('Currency is required');
    }
    const line = dimensionPanelHelpers.applyDefaultDimensionsOnNewLineIfNeeded(
        { ...orderLine, _id: orderLine._id ?? '' },
        shipmentPage._defaultDimensionsAttributes,
    );

    const customerNumber = orderLine.document?.customerNumber ?? '';
    delete orderLine._id;
    delete orderLine._sortValue;
    delete orderLine.document;

    return {
        ...orderLine,
        status: 'readyToProcess',
        originDocumentType: 'order',
        quantityAllocated: '0',
        allocationStatus: orderLine.item?.isStockManaged ? 'notAllocated' : 'notManaged',
        item: orderLine.item,
        itemDescription: orderLine.itemDescription,
        site: shipmentPage.site,
        stockSite: orderLine.stockSite,
        unit: orderLine.unit,
        unitToStockUnitConversionFactor: orderLine.unitToStockUnitConversionFactor,
        stockUnit: orderLine.stockUnit,
        quantity: orderLine.selectedQuantity !== undefined ? String(orderLine.selectedQuantity) : undefined,
        quantityInStockUnit: String(
            Number(orderLine.selectedQuantity ?? 0) * Number(orderLine.unitToStockUnitConversionFactor ?? 1),
        ),
        grossPrice: orderLine.grossPrice,
        priceReason: orderLine.priceReason,
        priceOrigin: orderLine.priceOrigin,
        netPrice: orderLine.netPrice,
        amountExcludingTax: orderLine.amountExcludingTax,
        amountExcludingTaxInCompanyCurrency: orderLine.amountExcludingTaxInCompanyCurrency,
        customerNumber,
        discount: orderLine.discount,
        charge: orderLine.charge,
        salesOrderLines: [salesOrderLine],
        storedAttributes: line.storedAttributes,
        storedDimensions: line.storedDimensions,
        internalNote: { value: '' },
        externalNote: { value: '' },
        isExternalNote: false,
    };
}

function filterForOrderLine(shipmentPage: SalesShipmentPage): Filter<SalesOrderLine> {
    const shipmentLinesSysId = shipmentPage.lines.value.map(line => line._id);
    const salesShipmentLines = shipmentPage.lines.value.map(
        soLine => soLine.salesOrderLines?.at(0)?.linkedDocument?._id ?? '',
    );
    return {
        _id: { _nin: salesShipmentLines },
        status: { _nin: ['quote', 'closed'] },
        shippingStatus: { _nin: ['shipped'] },
        stockSite: { _id: shipmentPage.stockSite.value?._id ?? '' },
        allocationRequestStatus: { _nin: ['inProgress'] },
        shipToCustomerAddress: { _id: shipmentPage.shipToCustomerAddress.value?._id ?? '' },
        deliveryMode: { _id: shipmentPage.deliveryMode.value?._id ?? '' },
        document: {
            shipToCustomer: { _id: shipmentPage.shipToCustomer?.value?._id ?? '' },
            taxCalculationStatus: { _nin: ['failed'] },
            ...(shipmentPage.incoterm.value?._id ? { incoterm: { _id: shipmentPage.incoterm.value._id } } : {}),
            ...(shipmentPage.billToCustomer.value?._id
                ? { billToCustomer: { _id: shipmentPage.billToCustomer.value._id } }
                : {}),
            ...(shipmentPage.billToLinkedAddress.value?._id
                ? { billToLinkedAddress: { _id: shipmentPage.billToLinkedAddress.value._id } }
                : {}),
            ...(shipmentPage.currency.value?._id ? { currency: { _id: shipmentPage.currency.value._id } } : {}),
            ...(shipmentPage.paymentTerm.value?._id
                ? { paymentTerm: { _id: shipmentPage.paymentTerm.value._id } }
                : {}),
        },
        salesShipmentLines: { _atMost: 0, document: { _id: { _in: shipmentLinesSysId } } },
    };
}

export type OrderLineLookup = Awaited<ReturnType<typeof lookupOrderLine>>[0];

function underOrdered(line: ExtractEdgesPartial<SalesOrderLine & { selectedQuantity: number }>) {
    return +(line.selectedQuantity ?? 0) < +(line.remainingQuantityToShipInStockUnit ?? 0);
}
export function lookupOrderLine(
    shipmentPage: SalesShipmentPage,
    selectedLines?: { _id: string; selectedQuantity: number }[],
) {
    return shipmentPage.$.dialog.lookup<
        SalesShipmentLine & { selectedQuantity: number; remainingQuantityToShipInStockUnit?: number }
    >({
        resolveOnCancel: true,
        isEditable: true,
        isMultiSelect: true,
        size: 'extra-large',
        acceptButton: { text: 'Select' },
        dialogTitle: 'Select order lines',
        node: '@sage/xtrem-sales/SalesOrderLine',
        selectedRecordId: selectedLines?.map(line => line._id),
        filter: filterForOrderLine(shipmentPage),
        orderBy: { document: { number: +1 }, _sortValue: +1 },
        mapServerRecord(record) {
            const { remainingQuantityToShipInStockUnit } = record;
            const selectedLine = selectedLines?.find(line => line._id === record._id);
            const quantity = +(selectedLine?.selectedQuantity ?? remainingQuantityToShipInStockUnit ?? 0);
            return { ...record, selectedQuantity: record.selectedQuantity ?? quantity };
        },
        columns: [
            ui.nestedFields.image({ bind: { item: { image: true } }, title: 'Image', size: 'small' }),
            ui.nestedFields.technical({ bind: '_id' }),
            ui.nestedFields.reference({
                bind: 'item',
                valueField: 'name',
                node: '@sage/xtrem-master-data/Item',
                isReadOnly: true,
                columns: [
                    ui.nestedFields.text({ bind: 'name' }),
                    ui.nestedFields.text({ bind: 'id' }),
                    ui.nestedFields.text({ bind: 'description' }),
                    ui.nestedFields.text({ title: 'Category', bind: { category: { name: true } } }),
                ],
            }),
            ui.nestedFields.text({
                title: 'Item ID',
                bind: { item: { id: true } },
                isDisabled: true,
            }),
            ui.nestedFields.text({
                title: 'Item description',
                bind: 'itemDescription',
                isDisabled: true,
            }),
            ui.nestedFields.reference({
                bind: 'document',
                valueField: 'number',
                node: '@sage/xtrem-purchasing/PurchaseOrder',
                columns: [
                    ui.nestedFields.date({
                        title: 'Shipping date',
                        bind: 'shippingDate',
                    }),
                    ui.nestedFields.technical({
                        bind: 'currency',
                        node: '@sage/xtrem-master-data/Currency',
                        nestedFields: [
                            ui.nestedFields.technical({ bind: 'symbol' }),
                            ui.nestedFields.technical({ bind: 'decimalDigits' }),
                        ],
                    }),
                    ui.nestedFields.technical({
                        bind: 'shipToAddress',
                        nestedFields: [
                            ui.nestedFields.technical({ bind: 'name' }),
                            ui.nestedFields.technical({ bind: 'addressLine1' }),
                            ui.nestedFields.technical({ bind: 'addressLine2' }),
                            ui.nestedFields.technical({ bind: 'city' }),
                            ui.nestedFields.technical({ bind: 'region' }),
                            ui.nestedFields.technical({ bind: 'postcode' }),
                            ui.nestedFields.technical({
                                bind: 'country',
                                nestedFields: [
                                    ui.nestedFields.technical({ bind: 'name' }),
                                    ui.nestedFields.technical({ bind: 'id' }),
                                    ui.nestedFields.technical({ bind: 'regionLabel' }),
                                    ui.nestedFields.technical({ bind: 'zipLabel' }),
                                ],
                            }),
                            ui.nestedFields.technical({ bind: 'locationPhoneNumber' }),
                        ],
                    }),
                    ui.nestedFields.technical({
                        bind: 'billToLinkedAddress',
                        node: '@sage/xtrem-master-data/CustomerAddress',
                        nestedFields: [
                            ui.nestedFields.technical({ bind: 'name' }),
                            ui.nestedFields.technical({ bind: 'addressLine1' }),
                            ui.nestedFields.technical({ bind: 'addressLine2' }),
                            ui.nestedFields.technical({ bind: 'city' }),
                            ui.nestedFields.technical({ bind: 'region' }),
                            ui.nestedFields.technical({ bind: 'postcode' }),
                            ui.nestedFields.technical({
                                bind: 'country',
                                nestedFields: [
                                    ui.nestedFields.technical({ bind: 'name' }),
                                    ui.nestedFields.technical({ bind: 'id' }),
                                    ui.nestedFields.technical({ bind: 'regionLabel' }),
                                    ui.nestedFields.technical({ bind: 'zipLabel' }),
                                ],
                            }),
                            ui.nestedFields.technical({ bind: 'locationPhoneNumber' }),
                        ],
                    }),
                    ui.nestedFields.technical({
                        bind: 'billToAddress',
                        node: '@sage/xtrem-master-data/CustomerAddress',
                        nestedFields: [
                            ui.nestedFields.technical({ bind: 'name' }),
                            ui.nestedFields.technical({ bind: 'addressLine1' }),
                            ui.nestedFields.technical({ bind: 'addressLine2' }),
                            ui.nestedFields.technical({ bind: 'city' }),
                            ui.nestedFields.technical({ bind: 'region' }),
                            ui.nestedFields.technical({ bind: 'postcode' }),
                            ui.nestedFields.technical({
                                bind: 'country',
                                nestedFields: [
                                    ui.nestedFields.technical({ bind: 'name' }),
                                    ui.nestedFields.technical({ bind: 'id' }),
                                    ui.nestedFields.technical({ bind: 'regionLabel' }),
                                    ui.nestedFields.technical({ bind: 'zipLabel' }),
                                ],
                            }),
                            ui.nestedFields.technical({ bind: 'locationPhoneNumber' }),
                        ],
                    }),
                    ui.nestedFields.technical({
                        bind: 'billToCustomer',
                        node: '@sage/xtrem-master-data/Customer',
                        nestedFields: [
                            ui.nestedFields.technical({ bind: '_id' }),
                            ui.nestedFields.technical({ bind: 'name' }),
                            ui.nestedFields.technical({ bind: 'id' }),
                            ui.nestedFields.technical({ bind: 'isActive' }),
                        ],
                    }),
                    ui.nestedFields.technical({
                        bind: 'shipToAddress',
                        node: '@sage/xtrem-master-data/CustomerAddress',
                        nestedFields: [
                            ui.nestedFields.technical({ bind: 'name' }),
                            ui.nestedFields.technical({ bind: '_id' }),
                            ui.nestedFields.technical({ bind: 'concatenatedAddress' }),
                        ],
                    }),
                    ui.nestedFields.technical({
                        bind: 'shipToCustomerAddress',
                        node: '@sage/xtrem-master-data/BusinessEntityAddress',
                        nestedFields: [
                            ui.nestedFields.technical({ bind: '_id' }),
                            ui.nestedFields.technical({ bind: 'isActive' }),
                            ui.nestedFields.technical({ bind: 'name' }),
                            ui.nestedFields.technical({ bind: 'addressLine1' }),
                            ui.nestedFields.technical({ bind: 'addressLine2' }),
                            ui.nestedFields.technical({ bind: 'city' }),
                            ui.nestedFields.technical({ bind: 'region' }),
                            ui.nestedFields.technical({ bind: 'postcode' }),
                            ui.nestedFields.technical({
                                bind: 'country',
                                nestedFields: [
                                    ui.nestedFields.technical({ bind: 'name' }),
                                    ui.nestedFields.technical({ bind: 'id' }),
                                    ui.nestedFields.technical({ bind: 'regionLabel' }),
                                    ui.nestedFields.technical({ bind: 'zipLabel' }),
                                ],
                            }),
                            ui.nestedFields.text({ bind: 'locationPhoneNumber', title: 'Phone number' }),
                            ui.nestedFields.technical({
                                bind: 'businessEntity',
                                node: '@sage/xtrem-master-data/BusinessEntity',
                                nestedFields: [
                                    ui.nestedFields.technical({ bind: 'name' }),
                                    ui.nestedFields.technical({ bind: 'id' }),
                                ],
                            }),
                            ui.nestedFields.technical({
                                bind: 'deliveryDetail',
                                node: '@sage/xtrem-master-data/DeliveryDetail',
                                nestedFields: [
                                    ui.nestedFields.technical({
                                        bind: 'mode',
                                        node: '@sage/xtrem-master-data/DeliveryMode',
                                        nestedFields: [
                                            ui.nestedFields.technical({ bind: 'name' }),
                                            ui.nestedFields.technical({ bind: 'description' }),
                                        ],
                                    }),
                                    ui.nestedFields.technical({
                                        bind: 'leadTime',
                                    }),
                                    ui.nestedFields.technical({
                                        bind: 'shipmentSite',
                                        nestedFields: [
                                            ui.nestedFields.technical({ bind: 'name' }),
                                            ui.nestedFields.technical({ bind: 'id' }),
                                            ui.nestedFields.technical({
                                                bind: 'legalCompany',
                                                node: '@sage/xtrem-system/Company',
                                                nestedFields: [
                                                    ui.nestedFields.text({ bind: 'name', title: 'Name' }),
                                                    ui.nestedFields.text({ bind: 'id', title: 'ID' }),
                                                ],
                                            }),
                                        ],
                                    }),
                                ],
                            }),
                        ],
                    }),
                    ui.nestedFields.reference({
                        bind: 'incoterm',
                    }),
                    ui.nestedFields.technical({
                        bind: 'shippingDate',
                    }),
                    ui.nestedFields.technical({
                        bind: 'deliveryLeadTime',
                    }),
                    ui.nestedFields.technical({
                        bind: 'paymentTerm',
                        node: '@sage/xtrem-master-data/PaymentTerm',
                        nestedFields: [ui.nestedFields.technical({ bind: 'name' })],
                    }),
                    ui.nestedFields.technical({ bind: 'isOnHold' }),
                    ui.nestedFields.technical({
                        bind: 'deliveryMode',
                        node: '@sage/xtrem-master-data/DeliveryMode',
                        nestedFields: [
                            ui.nestedFields.technical({ bind: 'name' }),
                            ui.nestedFields.technical({ bind: 'description' }),
                        ],
                    }),
                    ui.nestedFields.technical({
                        bind: 'stockSite',
                        node: '@sage/xtrem-system/Site',
                        nestedFields: [
                            ui.nestedFields.technical({ bind: '_id' }),

                            ui.nestedFields.technical({ bind: 'name' }),
                            ui.nestedFields.technical({ bind: 'id' }),
                        ],
                    }),
                    ui.nestedFields.technical({
                        bind: 'site',
                        node: '@sage/xtrem-system/Site',
                        nestedFields: [
                            ui.nestedFields.technical({ bind: 'name' }),
                            ui.nestedFields.technical({ bind: 'id' }),
                        ],
                    }),
                    ui.nestedFields.technical({ bind: 'customerNumber' }),
                ],
                isReadOnly: true,
            }),
            ui.nestedFields.label({
                bind: 'status',
                optionType: '@sage/xtrem-sales/SalesDocumentShippingStatus',
                style: (_id, rowData) =>
                    PillColorPurchase.getLabelColorByStatus('PurchaseDocumentStatus', rowData?.status),
            }),
            ui.nestedFields.numeric({
                bind: 'selectedQuantity',
                title: 'Quantity to ship',
                isTransient: true,
                unitMode: 'unitOfMeasure',
                unit: (_id, row) => row?.unit,
                validation(value: number, rowData) {
                    return selectedQuantityValidation(value, String(rowData.remainingQuantityToShipInStockUnit ?? ''));
                },
            }),
            ui.nestedFields.technical({
                node: '@sage/xtrem-master-data/ItemSite',
                bind: 'itemSite',
                nestedFields: [
                    ui.nestedFields.technical({ bind: '_id' }),
                    ui.nestedFields.technical({ bind: 'prodLeadTime' }),
                    ui.nestedFields.technical({ bind: 'isOrderToOrder' }),
                    ui.nestedFields.technical({ bind: 'preferredProcess' }),
                ],
            }),
            ui.nestedFields.technical({ bind: 'quantityAllocated' }),
            ui.nestedFields.technical({ bind: 'remainingQuantityToShipInStockUnit' }),
            ui.nestedFields.technical({
                node: '@sage/xtrem-master-data/UnitOfMeasure',
                bind: 'unit',
                nestedFields: [
                    ui.nestedFields.technical({ bind: 'name' }),
                    ui.nestedFields.technical({ bind: 'id' }),
                    ui.nestedFields.technical({ bind: 'symbol' }),
                    ui.nestedFields.technical({ bind: 'description' }),
                    ui.nestedFields.technical({ bind: 'decimalDigits' }),
                ],
            }),
            ui.nestedFields.technical({ bind: 'unitToStockUnitConversionFactor' }),
            ui.nestedFields.technical({ bind: 'grossPrice' }),
            ui.nestedFields.technical({
                bind: 'priceReason',
                node: '@sage/xtrem-master-data/CustomerPriceReason',
                nestedFields: [
                    ui.nestedFields.technical({ bind: 'name' }),
                    ui.nestedFields.technical({ bind: 'priority' }),
                    ui.nestedFields.technical({ bind: 'isActive' }),
                    ui.nestedFields.technical({ bind: 'description' }),
                ],
            }),
            ui.nestedFields.technical({ bind: 'priceOrigin' }),
            ui.nestedFields.technical({ bind: 'netPrice' }),
            ui.nestedFields.technical({ bind: 'amountExcludingTax' }),
            ui.nestedFields.technical({ bind: 'amountExcludingTaxInCompanyCurrency' }),
            ui.nestedFields.technical({ bind: 'storedAttributes' }),
            ui.nestedFields.technical({ bind: 'storedDimensions' }),
            ui.nestedFields.technical({ bind: 'discount' }),
            ui.nestedFields.technical({ bind: 'charge' }),
        ],
    });
}

function checkOrderLinesMessage(lines: ui.PartialCollectionValue<SalesOrderLine & { selectedQuantity: number }>[]) {
    const isUnderOrdered = lines.some(underOrdered);

    if (isUnderOrdered) {
        return ui.localize(
            '@sage/xtrem-sales/order_to_shipment__confirm_lower_quantity',
            'You are about to create one or more order shipment lines with a quantity less than the ordered quantity.',
        );
    }

    return null;
}

function confirmOrderQuantity(shipmentPage: SalesShipmentPage, message: string) {
    return shipmentPage.$.dialog
        .confirmation(
            'warn',
            ui.localize('@sage/xtrem-sales/order_to_order__create_purchase_dialog_title', 'Confirm order quantity'),
            message,
        )
        .then(() => true)
        .catch(() => false);
}

async function updateHeaderDocument(
    shipmentPage: SalesShipmentPage,
    orderLine: ExtractEdgesPartial<
        SalesOrderLine & {
            selectedQuantity: number;
        }
    >,
): Promise<void> {
    shipmentPage.shipToAddress.value = orderLine.document?.shipToAddress ?? null;
    if (shipmentPage.shipToAddress.value?.concatenatedAddress) {
        shipmentPage.shipToAddress.value.concatenatedAddress = getConcatenatedAddress(shipmentPage.shipToAddress.value);
    }

    shipmentPage.date.value = orderLine.document?.shippingDate ?? DateValue.today().toString();

    shipmentPage.isOnHold.value = orderLine.document?.isOnHold ?? null;
    shipmentPage.billToLinkedAddress.value = orderLine.document?.billToLinkedAddress ?? null;
    shipmentPage.currency.value = orderLine.document?.currency ?? null;
    shipmentPage.paymentTerm.value = orderLine.document?.paymentTerm ?? null;
    shipmentPage.incoterm.value = orderLine.document?.incoterm ?? null;
    shipmentPage.deliveryLeadTime.value = orderLine.document?.deliveryLeadTime ?? null;
    shipmentPage.billToAddress.value = orderLine.document?.billToAddress ?? null;
    shipmentPage.shipToAddress.value = orderLine.document?.shipToAddress ?? null;
    shipmentPage.site.value = orderLine.document?.site ?? null;
    shipmentPage.deliveryMode.value = orderLine.document?.deliveryMode ?? null;
    shipmentPage.shipToCustomerAddress.value = orderLine.document?.shipToCustomerAddress ?? null;

    await shipmentPage.updateDeliveryDate();
}

export async function addLineFromOrder(
    shipmentPage: SalesShipmentPage,
    selectedLines?: { _id: string; selectedQuantity: number }[],
) {
    const orderLines = (await lookupOrderLine(shipmentPage, selectedLines)) as unknown as ui.PartialCollectionValue<
        SalesShipmentLine & { selectedQuantity: number }
    >[];
    if (!orderLines || !isArray(orderLines) || orderLines.length === 0) {
        return;
    }

    const message = checkOrderLinesMessage(orderLines);
    if (message && !(await confirmOrderQuantity(shipmentPage, message))) {
        await addLineFromOrder(
            shipmentPage,
            orderLines.map(line => ({ _id: line._id ?? '', selectedQuantity: line.selectedQuantity ?? 0 })),
        );
        return;
    }

    await asyncArray(orderLines).forEach(async orderLine => {
        if (shipmentPage.lines.value.length === 0) {
            await updateHeaderDocument(shipmentPage, orderLine);
            if (orderLine.salesOrderLines) {
                shipmentPage.salesOrderLineToSalesShipmentLines.addOrUpdateRecordValue(orderLine.salesOrderLines[0]);
            }
        }
        const shipmentLine = convertOrderLineToShipmentLine(shipmentPage, orderLine);

        shipmentPage.lines.addRecord(shipmentLine);
    });
}
