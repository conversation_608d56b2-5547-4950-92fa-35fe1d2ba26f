import type { Dict, ExtractEdgesPartial } from '@sage/xtrem-client';
import { Decimal } from '@sage/xtrem-decimal';
import type { Currency, Customer } from '@sage/xtrem-master-data-api';
import { convertAmount } from '@sage/xtrem-master-data/build/lib/shared-functions/common';
import type {
    GraphApi,
    SalesOrderLineCloseStatusMethodReturn,
    SalesOrderLineOpenStatusMethodReturn,
    SalesReturnRequestLineCloseStatusMethodReturn,
    SalesReturnRequestLineOpenStatusMethodReturn,
} from '@sage/xtrem-sales-api';
import type { Site } from '@sage/xtrem-system-api';
import { calculateTaxes } from '@sage/xtrem-tax/build/lib/client-functions/calculate-taxes';
import * as ui from '@sage/xtrem-ui';
import type { SalesOrder as SalesOrderPage } from '../pages/sales-order';
import { getSalesPrice } from './common';
import type {
    CalculatePriceInputValues,
    CalculatePriceOutputValues,
    PriceDeterminationInputValues,
    PriceDeterminationOutputValues,
} from './interfaces/interfaces';

export function confirmDialog(page: Dict<any>, title: string, message: string) {
    const options = {
        acceptButton: {
            text: ui.localize('@sage/xtrem-sales/pages-confirm-yes', 'Yes'),
        },
        cancelButton: {
            text: ui.localize('@sage/xtrem-sales/pages-confirm-no', 'No'),
        },
    };
    return page.$.dialog
        .confirmation('warn', title, message, options)
        .then(() => true)
        .catch(() => false);
}

type SalesOrderMethodReturnMessage = {
    content: string;
    option: any;
    severity: string;
};

type SalesReturnRequestMethodReturnMessage = {
    content: string;
    option: any;
    severity: string;
};

export function getLocalizeSalesOrderLineCloseStatusMethodReturn(
    returnStatus: SalesOrderLineCloseStatusMethodReturn,
): SalesOrderMethodReturnMessage {
    const listOfMethodReturn = {
        parametersAreIncorrect: {
            content: ui.localize(
                '@sage/xtrem-sales/pages_functions__sales_order_line_close_action_not_allowed_incorrect_parameters',
                'The close action on the line is not allowed. The parameters are incorrect.',
            ),
            option: { type: 'error' },
            severity: 'error',
        },
        salesOrderLineIsAlreadyClosed: {
            content: ui.localize(
                '@sage/xtrem-sales/pages_functions__sales_order_line_close_action_not_allowed_line_already_closed',
                'The close action on the line is not allowed. The sales order line is already closed.',
            ),
            option: { type: 'warning' },
            severity: 'warning',
        },
        salesOrderLineIsNowClosed: {
            content: ui.localize(
                '@sage/xtrem-sales/pages_functions__sales_order_line_close_action_allowed_line_closed',
                'The sales order line is closed.',
            ),
            option: { type: 'success' },
            severity: 'success',
        },
        salesOrderHeaderIsNowClosed: {
            content: ui.localize(
                '@sage/xtrem-sales/pages_functions__sales_order_line_close_action_allowed_header_closed',
                'The sales order is closed',
            ),
            option: { type: 'success' },
            severity: 'success',
        },
    };
    return listOfMethodReturn[returnStatus];
}

export function getLocalizeSalesOrderLineOpenStatusMethodReturn(
    returnStatus: SalesOrderLineOpenStatusMethodReturn,
): SalesOrderMethodReturnMessage {
    const listOfMethodReturn = {
        parametersAreIncorrect: {
            content: ui.localize(
                '@sage/xtrem-sales/pages_functions__sales_order_line_open_action_not_allowed_incorrect_parameters',
                'The open action on the line is not allowed. The parameters are incorrect.',
            ),
            option: { type: 'error' },
            severity: 'error',
        },
        salesOrderLineIsAlreadyOpen: {
            content: ui.localize(
                '@sage/xtrem-sales/pages_functions__sales_order_line_open_action_not_allowed_sales_order_line_is_already_open',
                'The open action on the line is not allowed. The sales order line is already open.',
            ),
            option: { type: 'warning' },
            severity: 'warning',
        },
        salesOrderLineIsShipped: {
            content: ui.localize(
                '@sage/xtrem-sales/pages_functions__sales_order_line_open_action_not_allowed_sales_order_line_is_shipped',
                'The open action on the line is not allowed. The sales order line is shipped.',
            ),
            option: { type: 'error' },
            severity: 'error',
        },
        salesOrderLineIsNowOpen: {
            content: ui.localize(
                '@sage/xtrem-sales/pages_functions__sales_order_line_open_action_allowed',
                'The sales order line is opened.',
            ),
            option: { type: 'success' },
            severity: 'success',
        },
    };
    return listOfMethodReturn[returnStatus];
}

export async function responseOrReject<T>(
    page: SalesOrderPage & { _id: ui.fields.Numeric },
    promise: Promise<T>,
    message?: string,
    path?: string,
    toCurrent = false,
    skipValidation = false,
) {
    try {
        let validation: string[] = [];
        if (!skipValidation) {
            validation = await page.$.page.validate();
        }

        if (validation.length === 0) {
            const result = await promise;
            let queryParameters = {};
            if (toCurrent) {
                queryParameters = { _id: page._id.value, skipDirtyCheck: true };
            }
            if (message) {
                page.$.showToast(message, { type: 'success' });
            }
            if (path) {
                page.$.router.goTo(path, queryParameters);
            } else {
                return result;
            }
        } else {
            page.$.showToast(validation.join('\n'), { type: 'error' });
        }
    } catch (e) {
        page.$.showToast(e.message, { timeout: 30000, type: 'error' });
    }
    return undefined;
}

export function getLocalizeSalesReturnRequestLineCloseStatusMethodReturn(
    returnStatus: SalesReturnRequestLineCloseStatusMethodReturn,
): SalesReturnRequestMethodReturnMessage {
    const listOfMethodReturn = {
        parametersAreIncorrect: {
            content: ui.localize(
                '@sage/xtrem-sales/pages_functions__sales_return_request_line_close_action_not_allowed_incorrect_parameters',
                'The close action on the line is not allowed. The parameters are incorrect.',
            ),
            option: { type: 'error' },
            severity: 'error',
        },
        salesReturnRequestLineIsAlreadyClosed: {
            content: ui.localize(
                '@sage/xtrem-sales/pages_functions__sales_return_request_line_close_action_not_allowed_line_already_closed',
                'The close action on the line is not allowed. The sales return request line is already closed.',
            ),
            option: { type: 'warning' },
            severity: 'warning',
        },
        salesReturnRequestLineIsNowClosed: {
            content: ui.localize(
                '@sage/xtrem-sales/pages_functions__sales_return_request_line_close_action_allowed_line_closed',
                'The sales return request line is closed.',
            ),
            option: { type: 'success' },
            severity: 'success',
        },
        salesReturnRequestHeaderIsNowClosed: {
            content: ui.localize(
                '@sage/xtrem-sales/pages_functions__sales_return_request_line_close_action_allowed_header_closed',
                'The sales return request is closed',
            ),
            option: { type: 'success' },
            severity: 'success',
        },
        linkedSalesReturnReceiptIsNotClosed: {
            content: ui.localize(
                '@sage/xtrem-sales/pages_functions__sales_return_request_line_close_action_not_allowed_receipt_not_closed',
                'The close action on the line is not allowed. The linked return receipt is not closed',
            ),
            option: { type: 'error' },
            severity: 'error',
        },
    };
    return listOfMethodReturn[returnStatus];
}

export function getLocalizeSalesReturnRequestLineOpenStatusMethodReturn(
    returnStatus: SalesReturnRequestLineOpenStatusMethodReturn,
): SalesReturnRequestMethodReturnMessage {
    const listOfMethodReturn = {
        parametersAreIncorrect: {
            content: ui.localize(
                '@sage/xtrem-sales/pages_functions__sales_return_request_line_open_action_not_allowed_incorrect_parameters',
                'The open action on the line is not allowed. The parameters are incorrect.',
            ),
            option: { type: 'error' },
            severity: 'error',
        },
        salesReturnRequestLineIsAlreadyOpen: {
            content: ui.localize(
                '@sage/xtrem-sales/pages_functions__sales_return_request_line_open_action_not_allowed_sales_return_request_line_is_already_open',
                'The open action on the line is not allowed. The sales return request line is already open.',
            ),
            option: { type: 'warning' },
            severity: 'warning',
        },
        salesReturnRequestLineIsReceived: {
            content: ui.localize(
                '@sage/xtrem-sales/pages_functions__sales_return_request_line_open_action_not_allowed_sales_return_request_line_is_shipped',
                'The open action on the line is not allowed. The sales return request line is received.',
            ),
            option: { type: 'error' },
            severity: 'error',
        },
        salesReturnRequestLineIsNowOpen: {
            content: ui.localize(
                '@sage/xtrem-sales/pages_functions__sales_return_request_line_open_action_allowed',
                'The sales return request line is opened.',
            ),
            option: { type: 'success' },
            severity: 'success',
        },
    };
    return listOfMethodReturn[returnStatus];
}

// Show/hide Exchange rate from Sales pages
export function isExchangeRateHidden(
    currency: ExtractEdgesPartial<Currency> | null,
    site: ExtractEdgesPartial<Site> | null,
    customer: ExtractEdgesPartial<Customer> | null,
) {
    if (site?.legalCompany?.currency?.id && currency?.id && customer) {
        return site.legalCompany.currency.id === currency.id;
    }
    return true;
}

export async function calculateLinePrices(inputValues: CalculatePriceInputValues): Promise<CalculatePriceOutputValues> {
    const outputValues: CalculatePriceOutputValues = {
        netPrice: 0,
        amountExcludingTax: 0,
        amountIncludingTax: 0,
        taxAmount: inputValues.taxAmount,
        taxAmountAdjusted: inputValues.taxAmountAdjusted,
        uiTaxes: inputValues.taxes.uiTaxes,
        amountExcludingTaxInCompanyCurrency: 0,
        amountIncludingTaxInCompanyCurrency: 0,
        taxCalculationStatus: 'failed',
    };

    if (inputValues.grossPrice >= 0) {
        outputValues.netPrice = Decimal.roundAt(
            inputValues.grossPrice * (1 + (inputValues.charge || 0) / 100 - (inputValues.discount || 0) / 100),
            inputValues.netPriceScale,
        );

        outputValues.amountExcludingTax = Number(outputValues.netPrice * (inputValues.quantity || 0));
        const { uiTaxes, taxAmount, taxAmountAdjusted, taxCalculationStatus } = await calculateTaxes({
            ...inputValues.taxes,
            amountExcludingTax: outputValues.amountExcludingTax,
            quantity: inputValues.quantity,
            taxAmount: inputValues.taxAmount,
            taxAmountAdjusted: inputValues.taxAmountAdjusted,
        });

        outputValues.uiTaxes = uiTaxes;
        outputValues.taxAmount = taxAmount;
        outputValues.taxAmountAdjusted = taxAmountAdjusted;
        outputValues.taxCalculationStatus = taxCalculationStatus;
        outputValues.amountIncludingTax = Number(outputValues.amountExcludingTax + outputValues.taxAmountAdjusted);

        // Company currency amounts
        outputValues.amountExcludingTaxInCompanyCurrency = convertAmount(
            outputValues.amountExcludingTax,
            inputValues.rateMultiplication,
            inputValues.rateDivision,
            inputValues.fromDecimals,
            inputValues.toDecimals,
        );
        outputValues.amountIncludingTaxInCompanyCurrency = convertAmount(
            outputValues.amountIncludingTax,
            inputValues.rateMultiplication,
            inputValues.rateDivision,
            inputValues.fromDecimals,
            inputValues.toDecimals,
        );
    }
    return outputValues;
}

export async function getPrices(
    graph: ui.GraphQLApi<GraphApi>,
    inputValues: PriceDeterminationInputValues,
): Promise<PriceDeterminationOutputValues> {
    const outputValues: PriceDeterminationOutputValues = {
        priceReason: { _id: '', name: '' },
        priceOrigin: '',
        grossPrice: 0,
        discount: 0,
        charge: 0,
        priceReasonDeterminated: { _id: '', name: '' },
        priceOriginDeterminated: '',
        grossPriceDeterminated: 0,
        discountDeterminated: 0,
        chargeDeterminated: 0,
        error: '',
    };

    const result = await getSalesPrice(
        graph,
        inputValues.salesSiteId,
        inputValues.stockSiteId,
        inputValues.soldToCustomerId,
        inputValues.currencyId,
        inputValues.itemId,
        inputValues.unitId,
        inputValues.quantity,
        inputValues.date,
    );

    outputValues.priceOrigin = '';
    if (result) {
        if (result.error && result.error !== '') outputValues.error = result.error;
        outputValues.priceReason = result.priceReason;
        if (result.priceReason) {
            outputValues.priceOrigin = 'priceList';
        } else if (+result.grossPrice > 0) {
            outputValues.priceOrigin = 'basePrice';
        }
        outputValues.grossPrice = +result.grossPrice;
        outputValues.discount = +result.discount;
        outputValues.charge = +result.charge;
        outputValues.priceOriginDeterminated = outputValues.priceOrigin;
        outputValues.priceReasonDeterminated = result.priceReason;
        outputValues.grossPriceDeterminated = +result.grossPrice;
        outputValues.discountDeterminated = +result.discount;
        outputValues.chargeDeterminated = +result.charge;
    } else {
        outputValues.priceOriginDeterminated = outputValues.priceOrigin;
    }

    return outputValues;
}
