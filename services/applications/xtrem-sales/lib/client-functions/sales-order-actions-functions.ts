import { asyncArray } from '@sage/xtrem-async-helper';
import { extractEdges } from '@sage/xtrem-client';
import { DateValue } from '@sage/xtrem-date-time';
import {
    confirmDialogWithAcceptButtonText,
    isNotWorkDay,
} from '@sage/xtrem-distribution/build/lib/client-functions/common';
import type { GraphApi, SalesOrderOpenStatusMethodReturn } from '@sage/xtrem-sales-api';
import * as ui from '@sage/xtrem-ui';
import { getDimensionsForSalesLines, getValuesForSetDimensionsFromMainList } from './finance-integration';
import type {
    CloseActionParameters,
    CreateShipmentActionActionParameters,
    ExecuteSalesShipmentsMutationParameters,
    GetSalesOrderLinesParameter,
    LocalizeOrderActionsParameters,
    NotificationContentOptions,
    OpenActionParameters,
    SalesOrderPageParameter,
    SalesOrderPageWidgetParameter,
    SetDimensionActionParameter,
} from './interfaces/sales-order-actions-functions';

async function openAssignmentDialog(
    salesOrderPage: ui.Page | ui.widgets.TableWidget,
    dialogTitle: string,
    dialogText: string,
) {
    if (
        await salesOrderPage.$.dialog
            .confirmation('warn', dialogTitle, dialogText, {
                acceptButton: {
                    text: ui.localize('@sage/xtrem-sales/pages-sales-order-dialog-assignment-continue', 'Continue'),
                },
                cancelButton: {
                    text: ui.localize('@sage/xtrem-sales/pages-sales-order-dialog-assignment-cancel', 'Cancel'),
                },
            })
            .then(() => true)
            .catch(() => false)
    ) {
        return true;
    }
    return false;
}

export function isSalesOrderAssignmentCheckDelete(
    salesOrderPage: ui.Page | ui.widgets.TableWidget,
    orderAssignmentLinked: boolean,
) {
    if (orderAssignmentLinked) {
        return openAssignmentDialog(
            salesOrderPage,
            ui.localize(
                '@sage/xtrem-sales/pages_confirm_assignment_sales_order_deletion_dialog_title',
                'Delete sales order',
            ),
            ui.localize(
                '@sage/xtrem-sales/pages_confirm_assignment_sales_order_deletion_dialog_content',
                'When you delete this sales order, the links to the supply orders are also deleted.',
            ),
        );
    }
    return true;
}

export function showOnHoldSaveMessage(salesOrderPage: ui.Page) {
    return confirmDialogWithAcceptButtonText(
        salesOrderPage,
        ui.localize('@sage/xtrem-sales/pages__sales_order_save_on_hold_action_dialog_title', 'Customer on hold'),
        ui.localize(
            '@sage/xtrem-sales/pages__sales_order___save_on_hold_action__action_dialog_content',
            'You are about to update a confirmed sales order for an on hold customer.',
        ),
        ui.localize('@sage/xtrem-sales/pages-save-continue', 'Continue'),
    );
}

export function setSalesOrderToWoPoMessage(salesOrderPage: ui.Page) {
    return confirmDialogWithAcceptButtonText(
        salesOrderPage,
        ui.localize('@sage/xtrem-sales/pages__sales_order__wo_po_dialog_title', 'Confirm update'),
        ui.localize(
            '@sage/xtrem-sales/pages__sales_order__wo_po_order_action_dialog_content',
            'You are about to create an assigned order for an on hold customer.',
        ),
        ui.localize('@sage/xtrem-sales/pages-wo_po_order_confirm', 'Confirm'),
    );
}

export function setSalesOrderToAssignedOrderMessage(salesOrderPage: ui.Page) {
    return confirmDialogWithAcceptButtonText(
        salesOrderPage,
        ui.localize('@sage/xtrem-sales/pages__sales_order__assigned_order_dialog_title', 'Confirm update'),
        ui.localize(
            '@sage/xtrem-sales/pages__sales_order__assigned_order_action_dialog_content',
            'You are about to manage an assigned order for an on hold customer.',
        ),
        ui.localize('@sage/xtrem-sales/pages-assigned_order_confirm', 'Confirm'),
    );
}

export function setSalesOrderAllocateStockMessage(salesOrderPage: ui.Page) {
    return confirmDialogWithAcceptButtonText(
        salesOrderPage,
        ui.localize('@sage/xtrem-sales/pages__sales_order__allocate_stock_dialog_title', 'Confirm update'),
        ui.localize(
            '@sage/xtrem-sales/pages__sales_order__allocate_stock_action_dialog_content',
            'You are about to allocate the sales order for an on hold customer.',
        ),
        ui.localize('@sage/xtrem-sales/pages-allocate_stock_confirm', 'Confirm'),
    );
}

export function confirmShipment(salesOrderPage: ui.Page) {
    return confirmDialogWithAcceptButtonText(
        salesOrderPage,
        ui.localize('@sage/xtrem-sales/pages__sales_order__create_shipment_dialog_title', 'Confirm shipment creation'),
        ui.localize(
            '@sage/xtrem-sales/pages__sales_order__create_shipment_dialog_content',
            'You are about to create a shipment from this sales order.',
        ),
        ui.localize('@sage/xtrem-sales/pages-confirm-create', 'Create'),
    );
}

export function confirmShipmentIsOnHold(salesOrderPage: ui.Page) {
    return confirmDialogWithAcceptButtonText(
        salesOrderPage,
        ui.localize(
            '@sage/xtrem-sales/pages__sales_order__create_shipment_on_hold_dialog_title',
            'Confirm shipment creation',
        ),
        ui.localize(
            '@sage/xtrem-sales/pages__sales_order__create_shipment_on_hold__dialog_content',
            'You are about to ship the sales order for an on hold customer.',
        ),
        ui.localize('@sage/xtrem-sales/pages-confirm-create_on_hold', 'Create'),
    );
}

export async function getSalesOrderLines(parameters: GetSalesOrderLinesParameter) {
    return extractEdges(
        await parameters.salesOrderPage.$.graph
            .node('@sage/xtrem-sales/SalesOrderLine')
            .query(
                ui.queryUtils.edgesSelector(
                    {
                        _id: true,
                        storedDimensions: true,
                        storedAttributes: true,
                        document: {
                            number: true,
                        },
                    },

                    {
                        filter: {
                            document: {
                                number: parameters.recordNumber,
                            },
                        },
                    },
                ),
            )
            .execute(),
    );
}

export async function setDimensions(parameters: SetDimensionActionParameter) {
    let dataDetermined = false;
    let dimensionsToSet = '';
    let attributesToSet = '';
    const linesToSetDimensions = await getSalesOrderLines({
        salesOrderPage: parameters.salesOrderPage,
        recordNumber: parameters.recordNumber,
    });

    await asyncArray(linesToSetDimensions).some(async lineToSetDimensions => {
        const loopResponse = false;
        if (!dataDetermined) {
            const { defaultedFromItem, defaultDimensionsAttributes } = await getValuesForSetDimensionsFromMainList({
                page: parameters.salesOrderPage as ui.Page<GraphApi>,
                site: parameters.site || null,
                customer: parameters.customer || null,
            });
            const { resultDimensionsToSet, resultAttributesToSet, resultDataDetermined } =
                await getDimensionsForSalesLines({
                    line: lineToSetDimensions,
                    salesPage: parameters.salesOrderPage as ui.Page<GraphApi>,
                    defaultDimensionsAttributes,
                    defaultedFromItem,
                });
            if (!resultDataDetermined) {
                return true;
            }
            dimensionsToSet = resultDimensionsToSet;
            attributesToSet = resultAttributesToSet;
            dataDetermined = resultDataDetermined;
        }
        if (dataDetermined) {
            await parameters.salesOrderPage.$.graph
                .node('@sage/xtrem-sales/SalesOrderLine')
                .mutations.setDimension(true, {
                    baseDocumentItemLine: lineToSetDimensions._id,
                    storedDimensions: dimensionsToSet,
                    storedAttributes: attributesToSet,
                })
                .execute();
        }
        return loopResponse;
    });
    parameters.salesOrderPage.$.showToast(
        ui.localize('@sage/xtrem-sales/pages__sales_order__apply_dimensions_success', 'Dimensions applied.'),
        { type: 'success' },
    );
}

function setSalesOrderCloseStatus(parameters: SalesOrderPageWidgetParameter) {
    return parameters.salesOrderPage.$.graph
        .node('@sage/xtrem-sales/SalesOrder')
        .mutations.closeSalesOrder({ status: true }, { salesOrder: parameters.recordNumber })
        .execute();
}

export async function closeAction(parameters: CloseActionParameters) {
    if (
        await confirmDialogWithAcceptButtonText(
            parameters.salesOrderPage,
            ui.localize(
                '@sage/xtrem-sales/pages__sales_order__header_close_action_dialog_title',
                'Confirm status change',
            ),
            ui.localize(
                '@sage/xtrem-sales/pages__sales_order__header_close_action_dialog_content',
                'You are about to change the status of this order to closed. You can reopen this order later.',
            ),
            ui.localize('@sage/xtrem-sales/closeOrder____title', 'Close order'),
        )
    ) {
        parameters.salesOrderPage.$.loader.isHidden = false;
        const salesOrder = await setSalesOrderCloseStatus(parameters);
        if (salesOrder.status === 'closed') {
            parameters.salesOrderPage.$.showToast(
                ui.localize(
                    '@sage/xtrem-sales/pages_functions__sales_order_close_action_allowed_sales_order_is_closed',
                    'The sales order is closed.',
                ),
                { type: 'success' },
            );
            if (parameters.isCalledFromRecordPage) {
                await parameters.salesOrderPage.$.router.refresh();
            }
        }
        parameters.salesOrderPage.$.loader.isHidden = true;
    }
}

function setSalesOrderOpenStatus(parameters: SalesOrderPageParameter) {
    return parameters.salesOrderPage.$.graph
        .node('@sage/xtrem-sales/SalesOrder')
        .mutations.setSalesOrderOpenStatus(true, { salesOrderNumber: parameters.recordNumber })
        .execute();
}

export function getLocalizeSalesOrderOpenStatusMethodReturn(
    returnStatus: SalesOrderOpenStatusMethodReturn,
): NotificationContentOptions {
    switch (returnStatus) {
        case 'parametersAreIncorrect':
            return {
                content: ui.localize(
                    '@sage/xtrem-sales/pages_functions__sales_order_open_action_not_allowed_incorrect_parameters',
                    'The open action on the line is not allowed. The parameters are incorrect.',
                ),
                severity: 'error',
            };
        case 'salesOrderIsShipped':
            return {
                content: ui.localize(
                    '@sage/xtrem-sales/pages_functions__sales_order_open_action_not_allowed_the_sales_order_is_fully_shipped',
                    'The open action is not allowed. The sales order is fully shipped',
                ),
                severity: 'error',
            };
        case 'salesOrderIsAlreadyOpen':
            return {
                content: ui.localize(
                    '@sage/xtrem-sales/pages_functions__sales_order_open_action_not_allowed_the_sales_order_is_already_open',
                    'The open action is not allowed. The sales order is already open',
                ),
                severity: 'warning',
            };
        default:
            return {
                content: ui.localize(
                    '@sage/xtrem-sales/pages_functions__sales_order_open_action_allowed_header_open',
                    'The sales order is opened.',
                ),
                severity: 'success',
            };
    }
}

export async function openAction(parameters: OpenActionParameters) {
    if (
        await confirmDialogWithAcceptButtonText(
            parameters.salesOrderPage,
            ui.localize(
                '@sage/xtrem-sales/pages__sales_order__header_open_action_dialog_title',
                'Confirm status change',
            ),
            ui.localize(
                '@sage/xtrem-sales/pages__sales_order__header_open_action_dialog_content',
                'You are about to change the status of this order to open.',
            ),
            ui.localize('@sage/xtrem-sales/openOrder____title', 'Open order'),
        )
    ) {
        parameters.salesOrderPage.$.loader.isHidden = false;
        const returnStatus = await setSalesOrderOpenStatus(parameters);
        const returnValue = getLocalizeSalesOrderOpenStatusMethodReturn(returnStatus);
        parameters.salesOrderPage.$.showToast(returnValue.content, {
            type: returnValue.severity as Required<Parameters<typeof parameters.salesOrderPage.$.showToast>>[1]['type'],
        });
        if (returnValue.severity === 'success' && parameters.isCalledFromRecordPage) {
            await parameters.salesOrderPage.$.refreshNavigationPanel();
            await parameters.salesOrderPage.$.router.refresh();
        }
        parameters.salesOrderPage.$.loader.isHidden = true;
    }
}

export function executeCreateShipmentFromOrderMutation(
    parameters: ExecuteSalesShipmentsMutationParameters,
): Promise<{ _id: string }[]> {
    if (parameters.order !== '') {
        return parameters.salesOrderPage.$.graph
            .node('@sage/xtrem-sales/SalesOrder')
            .mutations.createShipmentsFromOrder({ _id: true }, { salesOrder: parameters.order })
            .execute();
    }
    return Promise.resolve([]);
}

export async function createShipmentAction(parameters: CreateShipmentActionActionParameters) {
    const confirmAction =
        parameters.isOnHold && parameters.customerOnHoldCheck === 'warning'
            ? await confirmShipmentIsOnHold(parameters.salesOrderPage)
            : await confirmShipment(parameters.salesOrderPage);

    if (confirmAction) {
        const shipments = await executeCreateShipmentFromOrderMutation(parameters);
        if (shipments.length === 0) return;
        parameters.salesOrderPage.$.showToast(
            ui.localize(
                '@sage/xtrem-sales/pages_functions__sales_order_create_shipment_action_is_shipped',
                'Sales shipment created ({{numberOfShipments}})',
                { numberOfShipments: shipments.length },
            ),
            { type: 'success' },
        );
        if (parameters.isCalledFromRecordPage) {
            parameters.salesOrderPage.$.setPageClean();
            parameters.salesOrderPage.$.router.goTo(`@sage/xtrem-sales/SalesShipment`, {
                _id: shipments[0]._id,
            });
        }
        parameters.salesOrderPage.$.loader.isHidden = true;
    }
}

export function deliveryDateValidation(val: string, workDays: number) {
    if (isNotWorkDay(+workDays, DateValue.parse(val))) {
        return ui.localize(
            '@sage/xtrem-sales/pages__sales_order__requested_delivery_cannot_be_a_not_working_day',
            'The requested delivery date must fall on a working day.',
        );
    }
    return undefined;
}

export function getLocalizeOrderSendAction(orderSend: LocalizeOrderActionsParameters) {
    if (orderSend.orderStatus === 'quote') {
        return {
            dialogTitle: ui.localize(
                '@sage/xtrem-sales/pages__main_list_sales_quote__section_title_send_sales_order_dialog_',
                'Send sales quote',
            ),
            confirmTitle: ui.localize(
                '@sage/xtrem-sales/pages__sales_quote__send_sales_order_dialog_title',
                'Sales quote email send confirmation',
            ),
            confirmMessage: ui.localize(
                '@sage/xtrem-sales/pages__sales_quote__send_sales_order_dialog_content',
                'You are about to send the sales quote email.',
            ),
            resultMessage: ui.localize('@sage/xtrem-sales/pages__sales_quote__email_sent', 'Sales quote sent to: '),
        };
    }
    return {
        dialogTitle: ui.localize(
            '@sage/xtrem-sales/pages__main_list_sales_order_section_title__send_sales_order_dialog',
            'Send sales order',
        ),
        confirmTitle: ui.localize(
            '@sage/xtrem-sales/pages__sales_order__send_sales_order_dialog_title',
            'Sales order email send confirmation',
        ),
        confirmMessage: ui.localize(
            '@sage/xtrem-sales/pages__sales_order__send_sales_order_dialog_content',
            'You are about to send the sales order email.',
        ),
        resultMessage: ui.localize('@sage/xtrem-sales/pages__sales_order__email_sent', 'Sales order sent to: '),
    };
}
