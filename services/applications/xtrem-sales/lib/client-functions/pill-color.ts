import type {
    SalesCreditMemoStatus,
    SalesDocumentCreditStatus,
    SalesDocumentInvoiceStatus,
    SalesDocumentReceiptStatus,
    SalesDocumentReturnRequestReceiptStatus,
    SalesDocumentReturnStatus,
    SalesDocumentShippingStatus,
    SalesInvoiceStatus,
    SalesOrderStatus,
    SalesReturnReceiptStatus,
    SalesReturnRequestApprovalStatus,
    SalesReturnRequestStatus,
    SalesShipmentStatus,
    UnbilledAccountReceivableStatus,
} from '@sage/xtrem-sales-api';
import type { ColoredElement } from '@sage/xtrem-system-api';
import {
    colorfulPillPattern,
    colorfulPillPatternDefaulted,
} from '@sage/xtrem-system/build/lib/client-functions/color-pattern';

// document statuses
function salesDocumentShippingStatusColor(status: SalesDocumentShippingStatus, coloredElement: ColoredElement) {
    switch (status) {
        case 'notShipped':
            return colorfulPillPattern.filledNeutral[coloredElement];
        case 'partiallyShipped':
            return colorfulPillPattern.outlinedCaution[coloredElement];
        case 'shipped':
            return colorfulPillPattern.filledPositive[coloredElement];
        default:
            return colorfulPillPattern.default[coloredElement];
    }
}

function salesDocumentReturnStatusColor(status: SalesDocumentReturnStatus, coloredElement: ColoredElement) {
    switch (status) {
        case 'noReturnRequested':
            return colorfulPillPattern.filledNeutral[coloredElement];
        case 'returnPartiallyRequested':
            return colorfulPillPattern.outlinedInformation[coloredElement];
        case 'returnRequested':
            return colorfulPillPattern.filledInformation[coloredElement];
        default:
            return colorfulPillPattern.default[coloredElement];
    }
}

function salesDocumentReceiptStatusColor(status: SalesDocumentReceiptStatus, coloredElement: ColoredElement) {
    switch (status) {
        case 'notReturned':
            return colorfulPillPattern.filledNeutral[coloredElement];
        case 'partiallyReturned':
            return colorfulPillPattern.outlinedInformation[coloredElement];
        case 'returned':
            return colorfulPillPattern.filledPositive[coloredElement];
        default:
            return colorfulPillPattern.default[coloredElement];
    }
}

function salesDocumentReturnRequestReceiptStatusColor(
    status: SalesDocumentReturnRequestReceiptStatus,
    coloredElement: ColoredElement,
) {
    switch (status) {
        case 'notReceived':
            return colorfulPillPattern.filledNeutral[coloredElement];
        case 'partiallyReceived':
            return colorfulPillPattern.outlinedInformation[coloredElement];
        case 'received':
            return colorfulPillPattern.filledPositive[coloredElement];
        default:
            return colorfulPillPattern.default[coloredElement];
    }
}

function salesDocumentInvoiceStatusColor(status: SalesDocumentInvoiceStatus, coloredElement: ColoredElement) {
    switch (status) {
        case 'notInvoiced':
            return colorfulPillPattern.filledCaution[coloredElement];
        case 'partiallyInvoiced':
            return colorfulPillPattern.outlinedCaution[coloredElement];
        case 'invoiced':
            return colorfulPillPattern.filledPositive[coloredElement];
        default:
            return colorfulPillPattern.default[coloredElement];
    }
}

function salesDocumentCreditStatusColor(status: SalesDocumentCreditStatus, coloredElement: ColoredElement) {
    switch (status) {
        case 'notCredited':
            return colorfulPillPattern.filledCaution[coloredElement];
        case 'partiallyCredited':
            return colorfulPillPattern.outlinedCaution[coloredElement];
        case 'credited':
            return colorfulPillPattern.filledPositive[coloredElement];
        default:
            return colorfulPillPattern.default[coloredElement];
    }
}

function salesReturnRequestApprovalStatusColor(
    status: SalesReturnRequestApprovalStatus,
    coloredElement: ColoredElement,
) {
    switch (status) {
        case 'draft':
            return colorfulPillPattern.filledNeutral[coloredElement];
        case 'pendingApproval':
            return colorfulPillPattern.filledCaution[coloredElement];
        case 'approved':
            return colorfulPillPattern.filledPositive[coloredElement];
        case 'rejected':
            return colorfulPillPattern.filledNegative[coloredElement];
        case 'changeRequested':
            return colorfulPillPattern.outlinedCaution[coloredElement];
        default:
            return colorfulPillPattern.default[coloredElement];
    }
}

// header's document statuses
function salesOrderStatusColor(status: SalesOrderStatus, coloredElement: ColoredElement) {
    switch (status) {
        case 'quote':
            return colorfulPillPattern.filledNeutral[coloredElement];
        case 'pending':
            return colorfulPillPattern.filledCaution[coloredElement];
        case 'inProgress':
            return colorfulPillPattern.filledInformation[coloredElement];
        case 'closed':
            return colorfulPillPattern.filledClosing[coloredElement];
        default:
            return colorfulPillPattern.default[coloredElement];
    }
}

function salesShipmentStatusColor(status: SalesShipmentStatus, coloredElement: ColoredElement) {
    switch (status) {
        case 'readyToProcess':
            return colorfulPillPattern.filledInformation[coloredElement];
        case 'readyToShip':
            return colorfulPillPattern.filledCaution[coloredElement];
        case 'shipped':
            return colorfulPillPattern.filledPositive[coloredElement];
        default:
            return colorfulPillPattern.default[coloredElement];
    }
}

function salesReturnRequestStatusColor(status: SalesReturnRequestStatus, coloredElement: ColoredElement) {
    switch (status) {
        case 'draft':
            return colorfulPillPattern.filledNeutral[coloredElement];
        case 'pending':
            return colorfulPillPattern.filledCaution[coloredElement];
        case 'inProgress':
            return colorfulPillPattern.filledInformation[coloredElement];
        case 'confirmed':
            return colorfulPillPattern.filledCaution[coloredElement];
        case 'closed':
            return colorfulPillPattern.filledClosing[coloredElement];
        default:
            return colorfulPillPattern.default[coloredElement];
    }
}

function salesReturnReceiptStatusColor(status: SalesReturnReceiptStatus, coloredElement: ColoredElement) {
    switch (status) {
        case 'draft':
            return colorfulPillPattern.filledNeutral[coloredElement];
        case 'inProgress':
            return colorfulPillPattern.filledInformation[coloredElement];
        case 'closed':
            return colorfulPillPattern.filledClosing[coloredElement];
        default:
            return colorfulPillPattern.default[coloredElement];
    }
}

function salesInvoiceStatusColor(status: SalesInvoiceStatus, coloredElement: ColoredElement) {
    switch (status) {
        case 'draft':
            return colorfulPillPattern.filledNeutral[coloredElement];
        case 'posted':
            return colorfulPillPattern.filledPositive[coloredElement];
        case 'inProgress':
            return colorfulPillPattern.filledInformation[coloredElement];
        case 'error':
            return colorfulPillPattern.filledNegative[coloredElement];
        default:
            return colorfulPillPattern.default[coloredElement];
    }
}

function salesCreditMemoStatusColor(status: SalesCreditMemoStatus, coloredElement: ColoredElement) {
    switch (status) {
        case 'draft':
            return colorfulPillPattern.filledNeutral[coloredElement];
        case 'posted':
            return colorfulPillPattern.filledPositive[coloredElement];
        case 'inProgress':
            return colorfulPillPattern.filledInformation[coloredElement];
        case 'error':
            return colorfulPillPattern.filledNegative[coloredElement];
        default:
            return colorfulPillPattern.default[coloredElement];
    }
}

function unbilledAccountReceivableStatusColor(status: UnbilledAccountReceivableStatus, coloredElement: ColoredElement) {
    switch (status) {
        case 'draft':
            return colorfulPillPattern.filledNeutral[coloredElement];
        case 'completed':
            return colorfulPillPattern.filledPositive[coloredElement];
        case 'inProgress':
            return colorfulPillPattern.filledInformation[coloredElement];
        case 'error':
            return colorfulPillPattern.filledNegative[coloredElement];
        default:
            return colorfulPillPattern.default[coloredElement];
    }
}

export function getLabelColorByStatus(
    enumEntry: string,
    status?:
        | SalesShipmentStatus
        | SalesDocumentInvoiceStatus
        | SalesDocumentCreditStatus
        | SalesReturnRequestStatus
        | SalesReturnRequestApprovalStatus
        | SalesDocumentReturnRequestReceiptStatus
        | SalesDocumentReceiptStatus
        | SalesInvoiceStatus
        | SalesCreditMemoStatus
        | SalesOrderStatus
        | SalesDocumentShippingStatus
        | SalesReturnReceiptStatus
        | SalesDocumentReturnStatus
        | UnbilledAccountReceivableStatus
        | null,
): {
    backgroundColor: string;
    borderColor: string;
    textColor: string;
} {
    const getColor = (coloredElement: ColoredElement) => {
        switch (enumEntry) {
            case 'SalesDocumentShippingStatus':
                return salesDocumentShippingStatusColor(status as SalesDocumentShippingStatus, coloredElement);
            case 'SalesDocumentReturnStatus':
                return salesDocumentReturnStatusColor(status as SalesDocumentReturnStatus, coloredElement);
            case 'SalesDocumentReceiptStatus':
                return salesDocumentReceiptStatusColor(status as SalesDocumentReceiptStatus, coloredElement);
            case 'SalesDocumentReturnRequestReceiptStatus':
                return salesDocumentReturnRequestReceiptStatusColor(
                    status as SalesDocumentReturnRequestReceiptStatus,
                    coloredElement,
                );
            case 'SalesDocumentInvoiceStatus':
                return salesDocumentInvoiceStatusColor(status as SalesDocumentInvoiceStatus, coloredElement);
            case 'SalesDocumentCreditStatus':
                return salesDocumentCreditStatusColor(status as SalesDocumentCreditStatus, coloredElement);
            case 'SalesReturnRequestApprovalStatus':
                return salesReturnRequestApprovalStatusColor(
                    status as SalesReturnRequestApprovalStatus,
                    coloredElement,
                );
            case 'SalesOrderStatus':
                return salesOrderStatusColor(status as SalesOrderStatus, coloredElement);
            case 'SalesShipmentStatus':
                return salesShipmentStatusColor(status as SalesShipmentStatus, coloredElement);
            case 'SalesReturnRequestStatus':
                return salesReturnRequestStatusColor(status as SalesReturnRequestStatus, coloredElement);
            case 'SalesReturnReceiptStatus':
                return salesReturnReceiptStatusColor(status as SalesReturnReceiptStatus, coloredElement);
            case 'SalesInvoiceStatus':
                return salesInvoiceStatusColor(status as SalesInvoiceStatus, coloredElement);
            case 'SalesCreditMemoStatus':
                return salesCreditMemoStatusColor(status as SalesCreditMemoStatus, coloredElement);
            case 'UnbilledAccountReceivableStatus':
                return unbilledAccountReceivableStatusColor(status as UnbilledAccountReceivableStatus, coloredElement);

            default:
                return colorfulPillPatternDefaulted(coloredElement);
        }
    };
    return {
        backgroundColor: getColor('backgroundColor'),
        borderColor: getColor('borderColor'),
        textColor: getColor('textColor'),
    };
}

export function stockShortageStatusColor(status: boolean, coloredElement: ColoredElement) {
    switch (status) {
        case false:
            return colorfulPillPattern.filledPositive[coloredElement];
        case true:
            return colorfulPillPattern.filledCaution[coloredElement];
        default:
            return colorfulPillPattern.default[coloredElement];
    }
}

export function setBooleanStatusColors(booleanEntry: string, status: boolean, coloredElement: ColoredElement): string {
    switch (booleanEntry) {
        case 'stockShortageStatus':
            return stockShortageStatusColor(status, coloredElement);
        default:
            return colorfulPillPatternDefaulted(coloredElement);
    }
}
