import type { decimal } from '@sage/xtrem-client';
import type { BaseDocumentLine } from '@sage/xtrem-master-data-api';
import type { OrderAssignment, OrderToOrderDemandType, OrderToOrderSupplyType } from '@sage/xtrem-stock-data-api';

export interface OrderAssignmentTable extends OrderAssignment {
    supplyOrder: BaseDocumentLine['documentNumber'];
    action: 'update' | 'delete';
    quantityOnSupplyOrder: decimal;
    quantityAssignedOnSupplyOrder: decimal;
    quantityNotAssignedOnSupplyOrder: decimal;
}

export interface SalesOrderLineAssignment {
    _id: string;
    demandDocumentLine: {
        _id: string;
    };
    demandType: OrderToOrderDemandType;
    quantityInStockUnit: string;
    supplyDocumentLine: {
        _id: string;
        documentId: number;
        documentNumber: string;
    };
    supplyWorkInProgress: {
        expectedQuantity: string;
    };
    supplyType: OrderToOrderSupplyType;
}

export interface SalesOrderLineAssignmentData {
    lineId: string;
    quantityInStockUnit: string;
    remainingQuantityToShipInStockUnit: number;
    supplyOrderType: OrderToOrderSupplyType;
}
