import type { ExtractEdgesPartial } from '@sage/xtrem-client';
import type { Currency, Customer } from '@sage/xtrem-master-data-api';
import type { SalesOrderLineBinding } from '@sage/xtrem-sales-api';
import type { Site } from '@sage/xtrem-system-api';
import type * as ui from '@sage/xtrem-ui';

export interface StatusParameters {
    status?: string | null;
}

export interface CloseParameters extends StatusParameters {
    lines: ui.PartialNodeWithId<SalesOrderLineBinding>[];
}

export interface OpenDeleteParameters extends StatusParameters {
    shippingStatus?: string | null;
}

export interface LinePhantomRowParameters extends StatusParameters {
    site?: ExtractEdgesPartial<Site> | null;
    stockSite?: ExtractEdgesPartial<Site> | null;
    soldToCustomer?: ExtractEdgesPartial<Customer> | null;
    date?: string | null;
    currency?: ExtractEdgesPartial<Currency> | null;
}

export interface AddSalesOrderLineParameters extends StatusParameters {
    site?: ExtractEdgesPartial<Site> | null;
    stockSite?: ExtractEdgesPartial<Site> | null;
    soldToCustomer?: ExtractEdgesPartial<Customer> | null;
    date?: string | null;
    currency?: ExtractEdgesPartial<Currency> | null;
}

export interface DefaultDimensionParameters {
    site?: ExtractEdgesPartial<Site> | null;
    stockSite?: ExtractEdgesPartial<Site> | null;
    soldToCustomer?: ExtractEdgesPartial<Customer> | null;
    date?: string | null;
    currency?: ExtractEdgesPartial<Currency> | null;
}

export interface RequestAllocationParameters extends StatusParameters {
    allocationRequestStatus?: string | null;
    allocationStatus?: string | null;
}
