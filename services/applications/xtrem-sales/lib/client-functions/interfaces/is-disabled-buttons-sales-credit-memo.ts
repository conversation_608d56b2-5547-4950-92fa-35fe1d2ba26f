import type { ExtractEdgesPartial } from '@sage/xtrem-client';
import type { Currency, Customer, PaymentTerm } from '@sage/xtrem-master-data-api';
import type { Site } from '@sage/xtrem-system-api';

export interface StatusParameters {
    status?: string | null;
}

export interface PostPrintParameters extends StatusParameters {
    taxCalculationStatus?: string | null;
}

export interface RepostParameters {
    fromNotificationHistory?: boolean;
}

export interface LinePhantomRowParameters extends StatusParameters {
    site?: ExtractEdgesPartial<Site> | null;
    billToCustomer?: ExtractEdgesPartial<Customer> | null;
    currency?: ExtractEdgesPartial<Currency> | null;
}

export interface AddSalesCreditMemoLineActionParameters extends StatusParameters {
    site?: ExtractEdgesPartial<Site> | null;
    billToCustomer?: ExtractEdgesPartial<Customer> | null;
    currency?: ExtractEdgesPartial<Currency> | null;
}

export interface DefaultDimensionParameters {
    site?: ExtractEdgesPartial<Site> | null;
    dueDate?: string | null;
    paymentTerm?: ExtractEdgesPartial<PaymentTerm> | null;
    date?: string | null | undefined;
    currency?: ExtractEdgesPartial<Currency> | null;
}
