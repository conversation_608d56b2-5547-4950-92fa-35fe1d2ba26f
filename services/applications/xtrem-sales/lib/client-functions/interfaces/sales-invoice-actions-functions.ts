import type { ExtractEdgesPartial } from '@sage/xtrem-client';
import type {
    FinanceIntegrationApp,
    PostingStatus,
    SourceDocumentType,
    TargetDocumentType,
} from '@sage/xtrem-finance-data-api';
import type { Customer } from '@sage/xtrem-master-data-api';
import type { GraphApi } from '@sage/xtrem-sales-api';
import type { Site } from '@sage/xtrem-system-api';
import type * as ui from '@sage/xtrem-ui';

export interface SendSalesInvoiceParameters {
    salesInvoicePage: ui.Page<GraphApi>;
    invoice: {
        _id: string;
        isPrinted: boolean;
        number: string;
    };
    arInvoiceId?: string; // if not set, a query will be made to get the AR invoice id (see canPrintInvoice)
    email: {
        contactTitle: string;
        contactLastName: string;
        contactFirstName: string;
        contactEmail: string;
    };
}

export interface GetDocumentSysIdParameters {
    salesInvoicePage: ui.Page<GraphApi>;
    number: string;
}

export interface GetSalesInvoiceLinesParameter {
    salesInvoicePage: ui.Page<GraphApi>;
    recordNumber: string;
}

export interface SetDimensionActionParameter {
    salesInvoicePage: ui.Page<GraphApi>;
    recordNumber: string;
    status: string | null;
    site?: ExtractEdgesPartial<Site> | null;
    customer?: ExtractEdgesPartial<Customer> | null;
}

interface InvoicePageWithPost extends ui.Page<GraphApi> {
    post: ui.PageAction;
}

export interface InvoicePageParameter {
    salesInvoicePage: InvoicePageWithPost;
    recordId: string;
}

export interface PostParameter extends InvoicePageParameter {
    isCalledFromRecordPage: boolean;
    taxCalculationStatus: string;
    status: string;
}

export interface InvoicePostingParameter extends InvoicePageParameter {
    isCalledFromRecordPage: boolean;
}

export interface CreditParameter extends InvoicePageParameter {
    isCalledFromRecordPage: boolean;
}

export interface PostDetailsParameter {
    _id: string;
    documentType: TargetDocumentType;
    documentNumber: string;
    documentSysId: number;
    status: PostingStatus;
    message: string;
    hasFinanceIntegrationApp: boolean;
    financeIntegrationApp: FinanceIntegrationApp | null;
    financeIntegrationAppRecordId: string;
    financeIntegrationAppUrl: string;
    externalLink: boolean;
    sourceDocumentType?: SourceDocumentType;
}
