import type { ExtractEdgesPartial } from '@sage/xtrem-client';
import type { Customer } from '@sage/xtrem-master-data-api';
import type { GraphApi } from '@sage/xtrem-sales-api';
import type { Site } from '@sage/xtrem-system-api';
import type * as ui from '@sage/xtrem-ui';

interface SalesShipmentPageWithPost extends ui.Page<GraphApi> {
    post: ui.PageAction;
    revert: ui.PageAction;
}

export interface SalesShipmentPage {
    salesShipmentPage: SalesShipmentPageWithPost;
}

export interface SalesShipmentPageWithId extends SalesShipmentPage {
    recordId: string;
}

export interface ActionParameters extends SalesShipmentPage {
    recordNumber: string;
}
export interface ActionParametersWithId extends ActionParameters {
    recordId: string;
}

export interface PrintShipmentParameters extends ActionParametersWithId {
    status: string;
}

export interface SetDimensionActionParameter extends ActionParameters {
    status: string | null;
    site?: ExtractEdgesPartial<Site> | null;
    customer?: ExtractEdgesPartial<Customer> | null;
}

export interface ConfirmActionParameters extends ActionParameters {
    isCalledFromRecordPage: boolean;
    recordId: string;
    isConfirmed: boolean;
}

export interface SetIsPrintedTrueParameters extends SalesShipmentPage {
    number: string;
    _id: string;
}

export interface GetSalesShipmentLinesParameter extends SalesShipmentPage {
    recordNumber: string;
}

export interface PostActionParameters extends PrintShipmentParameters {
    isOnHold: boolean;
    date: string;
    customerOnHoldCheck: string;
}

export interface AllLinesAreAllocatedParameters extends ActionParameters {}

export interface GetSalesShipmentLinesForAllocationsParameter extends ActionParameters {}

export interface CreateInvoiceParameters extends SalesShipmentPageWithId {
    isCalledFromRecordPage: boolean;
}
export interface CreateSalesOutputDocumentsLineErrorReturnValues {
    lineNumber: number;
    linePosition: number;
    message: string;
}

export interface CreateSalesOutputDocumentsReturnValues {
    status: string;
    numberOfShipments?: number;
    numberOfInvoices?: number;
    numberOfCreditMemos?: number;
    numberOfReturnRequests?: number;
    documentsCreated?: { number: string; _id: string }[];
    lineErrors: CreateSalesOutputDocumentsLineErrorReturnValues[];
}
export interface ErrorMessageParameters extends SalesShipmentPage {
    error: string | Error;
}
