import type { ExtractEdgesPartial } from '@sage/xtrem-client';
import type { Customer } from '@sage/xtrem-master-data-api';
import type { Site } from '@sage/xtrem-system-api';

export interface SaveParameters {
    status?: string | null;
}

export interface CancelParameters {
    status?: string | null;
}

export interface ConfirmParameters {
    status?: string | null;
}

export interface ShipParameters {
    status?: string | null;
}

export interface CloseParameters {
    status?: string | null;
}

export interface OpenParameters {
    status?: string | null;
    receiptStatus?: string | null;
    creditStatus?: string | null;
    approvalStatus?: string | null;
}

export interface ApproveParameters {
    status?: string | null;
    approvalStatus?: string | null;
}

export interface RejectParameters {
    status?: string | null;
    approvalStatus?: string | null;
}

export interface RequestApprovalParameters {
    status?: string | null;
    approvalStatus?: string | null;
}

export interface CreateSalesReturnRequestFromShipmentsParameters {
    status?: string | null;
    isCreditingAllowed?: boolean;
}

export interface LinePhantomRowParameters {
    status?: string | null;
    site?: ExtractEdgesPartial<Site> | null;
    stockSite?: ExtractEdgesPartial<Site> | null;
    soldToCustomer?: ExtractEdgesPartial<Customer> | null;
}

export interface AddSalesReturnRequestLineParameters {
    status?: string | null;
    site?: ExtractEdgesPartial<Site> | null;
    stockSite?: ExtractEdgesPartial<Site> | null;
    soldToCustomer?: ExtractEdgesPartial<Customer> | null;
}

export interface DefaultDimensionParameters {
    site?: ExtractEdgesPartial<Site> | null;
    stockSite?: ExtractEdgesPartial<Site> | null;
    soldToCustomer?: ExtractEdgesPartial<Customer> | null;
    date?: string | null;
    requester?: ExtractEdgesPartial<any> | null;
}
