import type { ExtractEdgesPartial } from '@sage/xtrem-client';
import type { Customer } from '@sage/xtrem-master-data-api';
import type { Site } from '@sage/xtrem-system-api';

export interface PostParameters {
    status?: string | null;
}

export interface DeleteParameters {
    status?: string | null;
    stockTransactionStatus?: string | null;
}

export interface RepostParameters {
    fromNotificationHistory?: boolean;
}

export interface SelectFromSalesReturnRequestLinesParameters {
    site?: ExtractEdgesPartial<Site> | null;
    shipToCustomer?: ExtractEdgesPartial<Customer> | null;
    status?: string | null;
}

export interface DefaultDimensionParameters {
    status?: string | null;
}
