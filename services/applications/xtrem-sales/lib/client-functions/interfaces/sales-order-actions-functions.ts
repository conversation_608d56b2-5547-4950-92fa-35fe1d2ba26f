import type { ExtractEdgesPartial } from '@sage/xtrem-client';
import type { Customer } from '@sage/xtrem-master-data-api';
import type { GraphApi, SalesOrder, SalesOrderStatus } from '@sage/xtrem-sales-api';
import type { Site } from '@sage/xtrem-system-api';
import type * as ui from '@sage/xtrem-ui';
import type { PageOrWidgetType } from './common';

export interface UpdatePrintStatusParameters {
    pageOrWidget: PageOrWidgetType;
    _id: string;
    status: string;
    isPrinted: boolean;
    number: string;
}

export interface PrintSalesOrderParameters {
    isCalledFromRecordPage: boolean;
    pageOrWidget: PageOrWidgetType;
    _id: string;
    status: string;
    number: string;
}

export interface ConfirmSalesOrderParameters {
    isCalledFromRecordPage: boolean;
    pageOrWidget: PageOrWidgetType;
    _id: string;
    status: SalesOrderStatus;
    number: string;
    isOnHold: boolean;
    customerOnHoldCheck: string;
    taxCalculationStatus: string;
}

export interface NotificationContentOptions {
    // TODO:
    // https://jira.sage.com/browse/XT-56067
    // option: NotificationOptions;
    content: string;
    severity: string;
}

export interface GetSalesOrderLinesParameter {
    salesOrderPage: ui.Page<GraphApi>;
    recordNumber: string;
}

export interface SetDimensionActionParameter {
    salesOrderPage: ui.Page<GraphApi>;
    recordNumber: string;
    status: string | null;
    site?: ExtractEdgesPartial<Site> | null;
    customer?: ExtractEdgesPartial<Customer> | null;
}

export interface SalesOrderPageParameter {
    salesOrderPage: ui.Page<GraphApi>;
    recordNumber: string;
}

export interface SalesOrderPageWidgetParameter {
    salesOrderPage: PageOrWidgetType | ui.Page<GraphApi>;
    recordNumber: string;
    isOrderAssignmentLinked?: boolean;
}

export interface CloseActionParameters {
    salesOrderPage: PageOrWidgetType;
    recordNumber: string;
    isCalledFromRecordPage: boolean;
}

export interface OpenActionParameters extends SalesOrderPageParameter {
    isCalledFromRecordPage: boolean;
}

export interface CreateShipmentActionActionParameters extends ExecuteSalesShipmentsMutationParameters {
    customerOnHoldCheck: string;
    isOnHold: boolean | null;
    isCalledFromRecordPage: boolean;
    showToast: ui.Page['$']['showToast'];
}

export interface ExecuteSalesShipmentsMutationParameters {
    salesOrderPage: ui.Page<GraphApi>;
    order: string;
}

export interface ProformaInvoiceDialogParameters<Format extends 'URLReady' | 'Object'> {
    salesOrder: Format extends 'URLReady' ? string : ui.PartialNodeWithId<SalesOrder>;
    version?: number;
    customerComment?: string;
    expirationDate?: string;
}

export interface AllocationActionParameters {
    pageOrWidget: PageOrWidgetType;
    _id: string;
    allocationType: 'deallocation' | 'allocation';
}

export interface ProformaSalesOrderParameters<Format extends 'URLReady' | 'Object'> {
    pageOrWidget: PageOrWidgetType;
    option: 'create' | 'read';
    shouldRefresh?: boolean;
    dialogParameters: ProformaInvoiceDialogParameters<Format>;
}

export interface LocalizeOrderActionsParameters {
    orderStatus: SalesOrderStatus;
    email?: string;
}
