import type { ExtractEdgesPartial } from '@sage/xtrem-client';
import type { Currency, UnitOfMeasure } from '@sage/xtrem-master-data-api';
import type { SalesOrderLine } from '@sage/xtrem-sales-api';
import type { decimal } from '@sage/xtrem-shared';

export interface SalesInvoiceLineInquiryLine {
    _id: string;
    salesSiteName: string;
    customerId: string;
    customerName: string;
    documentType: string;
    document: string;
    date: Date;
    itemId: string;
    itemDescription: string;
    itemName: string;
    quantity: decimal;
    unit: UnitOfMeasure;
    netPrice: decimal;
    currency: Currency;
    amountExcludingTax: decimal;
    companyCurrency: Currency;
    amountExcludingTaxInCompanyCurrency: decimal;
    city: string;
    region: string;
    postcode: string;
}

export interface SalesOrderLineQuery {
    _id: string;
    /** salesOrderLine.site.name */ salesSiteName: string;
    /** salesOrder.soldToCustomer.id */ customerId: string;
    /** salesOrder.soldToCustomer.name */ customerName: string;
    /** salesOrder.number */ salesOrderSysId: string;
    /** salesOrder.number */ salesOrderNumber: string;
    /** salesOrder.customerReference */ customerNumber: string;
    /** salesOrder.orderDate */ date: string;
    /** salesOrderLine.shippingDate */ shippingDate: string;
    /** salesOrderLine.item.id */ itemID: string;
    /** salesOrderLine.item.name */ itemName: string;
    /** salesOrderLine.description */ itemDescription: string;
    /** salesOrderLine.unit.name */ salesUnitName: string;
    /** salesOrderLine.unit.id */ salesUnitId: string;
    /** salesOrderLine.unit.symbol */ salesUnitSymbol: string;
    /** salesOrderLine.quantity */ quantity: number;
    /** salesOrderLine.netPrice */ netPrice: number;
    /** salesOrderLine.amountExcludingTax */ amountExcludingTax: number;
    /** salesOrderLine.remainingQuantityToShipInSalesUnit */ remainingQuantityToShipInSalesUnit: number;
    /** Computed : salesOrderLine.netPrice * salesOrderLine.remainingQuantityToShipInSalesUnit */ remainingAmount: number;
    /** salesOrder.currency.symbol & name */ currencyId: string;
    /** salesOrder.currency.symbol & name */ currencySymbol: string;
    /** salesOrder.currency.symbol & name */ currencyName: string;
    /** salesOrderLine.amountExcludingTaxInCompanyCurrency */ amountExcludingTaxInCompanyCurrency: number;
    /** Computed :
     * 	if the salesOrder.currency is equal to the company currency
             salesOrderLine.netPrice * salesOrderLine.remainingQuantityToShipInSalesUnit
        else salesOrderLine.netPrice * salesOrderLine.remainingQuantityToShipInSalesUnit
                converted in company curency with the rate on the sales order header */
    remainingAmountInCompanyCurrency: number;
    /** salesOrder.companyCurrency.symbol */ companyCurrencySymbol: string;
}

export interface SalesOrderFiltered {
    filteredSalesOrderLines: ExtractEdgesPartial<SalesOrderLine>[];
    isValid: boolean;
}
