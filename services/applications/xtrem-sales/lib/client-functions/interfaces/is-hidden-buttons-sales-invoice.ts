export interface StatusParameters {
    status?: string | null;
}

export interface StatusTaxCalcStatusParameters extends StatusParameters {
    taxCalculationStatus?: string | null;
}

export interface RepostParameters {
    fromNotificationHistory?: boolean;
}

export interface CreateSalesCreditMemosFromInvoicesParameters extends StatusTaxCalcStatusParameters {
    creditStatus?: string | null;
}

export interface RecordReceiptParameters extends StatusTaxCalcStatusParameters {
    paymentStatus?: string | null;
}

export interface PrintSendMailParameters extends StatusTaxCalcStatusParameters {
    displayStatus: string | null;
    enablePrintButton?: boolean;
}
