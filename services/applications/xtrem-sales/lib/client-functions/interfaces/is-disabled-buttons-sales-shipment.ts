import type { ExtractEdgesPartial } from '@sage/xtrem-client';
import type { Customer } from '@sage/xtrem-master-data-api';
import type { Site } from '@sage/xtrem-system-api';

export interface SaveParameters {
    status?: string | null;
}

export interface CancelParameters {
    status?: string | null;
}

export interface ConfirmParameters {
    status?: string | null;
}

export interface RevertParameters {
    status?: string | null;
    stockTransactionStatus?: string | null;
}

export interface PostParameters {
    status?: string | null;
    stockTransactionStatus?: string | null;
}

export interface RepostParameters {
    fromNotificationHistory?: boolean;
}

export interface CreateSalesInvoicesFromShipmentsParameters {
    status?: string | null;
    invoiceStatus?: string | null;
}

export interface CreateSalesReturnRequestFromShipmentsParameters {
    status?: string | null;
    returnRequestStatus?: string | null;
}

export interface PrintParameters {
    status?: string | null;
}

export interface SelectFromSalesOrderLinesParameters {
    stockSite?: ExtractEdgesPartial<Site> | null;
    date?: string | null;
    shipToCustomer?: ExtractEdgesPartial<Customer> | null;
    status?: string | null;
}

export interface DefaultDimensionParameters {
    site?: ExtractEdgesPartial<Site> | null;
    stockSite?: ExtractEdgesPartial<Site> | null;
    shipToCustomer?: ExtractEdgesPartial<Customer> | null;
    date?: string | null;
}
