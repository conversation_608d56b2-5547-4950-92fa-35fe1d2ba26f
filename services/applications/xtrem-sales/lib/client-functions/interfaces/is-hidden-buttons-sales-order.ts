export interface StatusParameters {
    status?: string | null;
}
export interface ShipmentAndStatusParameters extends StatusParameters {
    shippingStatus?: string | null;
}

export interface ShipParameters extends StatusParameters {
    allocationRequestStatus?: string | null;
    taxCalculationStatus?: string | null;
}

export interface DeleteParameters<T> {
    parameters: T;
    recordId?: string;
}

export interface OpenDeleteParameters extends StatusParameters {
    taxCalculationStatus?: string | null;
    displayStatus?: string | null;
}

export interface RequestAllocationParameters extends StatusParameters {
    allocationRequestStatus?: string | null;
    allocationStatus?: string | null;
}

export interface SendMailParameters extends ShipmentAndStatusParameters {
    taxCalculationStatus?: string | null;
    taxEngine?: string | null;
}
