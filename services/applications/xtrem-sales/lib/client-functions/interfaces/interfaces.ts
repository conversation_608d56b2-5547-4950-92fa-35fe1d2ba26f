import type { decimal, ExtractEdgesPartial, integer } from '@sage/xtrem-client';
import type {
    BaseDocumentLineBinding,
    Item,
    Location,
    TaxCalculationStatus,
    UnitOfMeasure,
} from '@sage/xtrem-master-data-api';
import type { StockStatus } from '@sage/xtrem-stock-data-api';
import type { Taxes } from '@sage/xtrem-tax/build/lib/client-functions/interfaces/calculate-taxes';
import type * as ui from '@sage/xtrem-ui';

export interface StockItemInfo {
    _id: string;
    id: string;
    lotManagement: string;
    lotSequenceNumber: { _id: string; id: string };
    stockUnit: { _id: string; id: string; name: string; decimalDigits: number; symbol: string };
}

// duplicated from inventory interfaces (server side)
export type LotSearchData = {
    item: Item;
    id: string;
    sublot?: string;
};

// duplicated from inventory interfaces (server side)
export interface LotCreateData extends Omit<LotSearchData, 'id'> {
    id?: string;
    supplierLot?: string | null;
    expirationDate?: string;
    potency?: number;
    documentLine?: BaseDocumentLineBinding['_id'];
    creationDate?: string;
    expirationReferenceDate?: string;
    shelfLife?: number;
}

// duplicated from inventory interfaces (server side)
export interface LotData extends LotCreateData {
    _id?: integer;
}

// duplicated from inventory interfaces (server side) - use a common when possible by platform
export type StockDetail = {
    stockDetailQuantityInStockUnit: decimal;
};

// duplicated from inventory interfaces (server side)
export interface StockReceiptDetail extends StockDetail {
    status: StockStatus;
    stockUnit: UnitOfMeasure;
    location?: Location;
    lot?: LotData | null;
    owner: string;
    stockDetailQuantity: decimal;
}

// duplicated from inventory interfaces (server side)
export interface StockReceiptDetailJson {
    detailLines: ExtractEdgesPartial<StockReceiptDetail>[];
}

export interface PriceDeterminationInputValues {
    salesSiteId: string;
    stockSiteId: string;
    soldToCustomerId: string;
    currencyId: string;
    itemId: string;
    unitId: string;
    quantity: number;
    date: Date;
}

export interface PriceDeterminationOutputValues {
    priceReason: {
        _id: string;
        name: string;
    };
    priceOrigin: string;
    grossPrice: number;
    discount: number;
    charge: number;
    priceReasonDeterminated: {
        _id: string;
        name: string;
    };
    priceOriginDeterminated: string;
    grossPriceDeterminated: number;
    discountDeterminated: number;
    chargeDeterminated: number;
    error?: string;
}

export interface CalculatePriceInputValues {
    grossPrice: number;
    charge: number;
    discount: number;
    netPriceScale: number;
    quantity: number;
    amountExcludingTax: number;
    amountIncludingTax: number;
    taxAmount: number;
    taxAmountAdjusted: number;
    rateMultiplication: number;
    rateDivision: number;
    fromDecimals: number;
    toDecimals: number;
    taxes: Taxes;
}

export interface CalculatePriceOutputValues {
    netPrice: number;
    taxAmount: number;
    taxAmountAdjusted: number;
    uiTaxes: string;
    amountExcludingTax: number;
    amountIncludingTax: number;
    amountExcludingTaxInCompanyCurrency: number;
    amountIncludingTaxInCompanyCurrency: number;
    taxCalculationStatus: TaxCalculationStatus;
}

export interface ItemData {
    item: {
        minimumSalesQuantity?: number;
        maximumSalesQuantity?: number;
        conversionFactor?: number;
    };
    itemCustomer: {
        minimumSalesQuantity?: number;
        maximumSalesQuantity?: number;
        conversionFactor?: number;
    };
}

export interface WorkOrderReturned {
    _id: string;
    number: string;
    name: string;
    productionItems: {
        query: {
            edges: {
                node: {
                    _id: string;
                    releasedQuantity: number;
                    workInProgress: {
                        _id: string | null;
                    };
                };
            }[];
        };
    };
}

export interface PurchaseOrderReturned {
    _id: string;
    number: string;
    name: string;
    lines: {
        query: {
            edges: {
                node: {
                    _id: string;
                    quantityInStockUnit: number;
                    workInProgress: {
                        _id: string | null;
                    };
                };
            }[];
        };
    };
}

export interface SalesOrderStepSequenceStatus {
    create: ui.StepSequenceStatus;
    confirm: ui.StepSequenceStatus;
    ship: ui.StepSequenceStatus;
    invoice: ui.StepSequenceStatus;
}

export interface SalesShipmentStepSequenceStatus {
    create: ui.StepSequenceStatus;
    confirm: ui.StepSequenceStatus;
    post: ui.StepSequenceStatus;
    invoice: ui.StepSequenceStatus;
    requestReturn: ui.StepSequenceStatus;
    receiveReturn: ui.StepSequenceStatus;
}

export interface SalesReturnRequestStepSequenceStatus {
    create: ui.StepSequenceStatus;
    approve: ui.StepSequenceStatus;
    confirm: ui.StepSequenceStatus;
    receive: ui.StepSequenceStatus;
    credit: ui.StepSequenceStatus;
}

export interface SalesReturnReceiptStepSequenceStatus {
    create: ui.StepSequenceStatus;
    post: ui.StepSequenceStatus;
}

export interface SalesInvoiceStepSequenceStatus {
    create: ui.StepSequenceStatus;
    post: ui.StepSequenceStatus;
    credit?: ui.StepSequenceStatus;
    pay?: ui.StepSequenceStatus;
}

export interface SalesCreditMemoStepSequenceStatus {
    create: ui.StepSequenceStatus;
    post: ui.StepSequenceStatus;
    pay?: ui.StepSequenceStatus;
}

export interface JsonDimensions {
    [type: string]: string;
}
