import type { ExtractEdgesPartial } from '@sage/xtrem-client';
import type { Customer } from '@sage/xtrem-master-data-api';
import type { GraphApi } from '@sage/xtrem-sales-api';
import type { Site } from '@sage/xtrem-system-api';
import type * as ui from '@sage/xtrem-ui';

export interface GetSalesReturnReceiptLinesParameter {
    salesReturnReceiptPage: ui.Page<GraphApi>;
    recordNumber: string;
}

export interface SetDimensionActionParameter {
    salesReturnReceiptPage: ui.Page<GraphApi>;
    recordNumber: string;
    status: string | null;
    site?: ExtractEdgesPartial<Site> | null;
    customer?: ExtractEdgesPartial<Customer> | null;
}

export interface PostActionParameter {
    salesReturnReceiptPage: ui.Page<GraphApi>;
    recordId: string;
}
