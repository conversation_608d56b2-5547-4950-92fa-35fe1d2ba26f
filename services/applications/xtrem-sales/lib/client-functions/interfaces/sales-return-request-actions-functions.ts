import type { ExtractEdgesPartial } from '@sage/xtrem-client';
import type { Customer } from '@sage/xtrem-master-data-api';
import type { GraphApi } from '@sage/xtrem-sales-api';
import type { Site, User } from '@sage/xtrem-system-api';
import type * as ui from '@sage/xtrem-ui';

export interface SalesReturnRequestPageParameter {
    salesReturnRequestPage: ui.Page<GraphApi>;
}

export interface ActionParameter extends SalesReturnRequestPageParameter {
    recordNumber: string;
}
export interface SetDimensionActionParameter extends ActionParameter {
    status: string | null;
    site?: ExtractEdgesPartial<Site> | null;
    customer?: ExtractEdgesPartial<Customer> | null;
}

export interface CloseActionParameter extends ActionParameter {
    isCalledFromRecordPage: boolean;
}

export interface ApproveRejectActionParameter extends SalesReturnRequestPageParameter {
    recordId: string;
}

export interface OpenActionParameters extends ActionParameter {
    isCalledFromRecordPage: boolean;
}
export interface UserApprover extends ui.PartialNodeWithId<User> {
    type: string;
    sortOrder: number;
}

interface ApproverUser {
    _id: string;
    email: string;
    firstName: string;
    lastName: string;
}
export interface SiteApprover {
    id: string;
    salesReturnRequestDefaultApprover: ApproverUser;
    salesReturnRequestSubstituteApprover: ApproverUser;
}

export interface LoadApproversParameters {
    salesReturnRequestPage: ui.Page<GraphApi>;
    siteId: string;
}

export interface SalesReturnRequestPageRecordIdParameter extends SalesReturnRequestPageParameter {
    recordId: string;
}
export interface CreateCreditMemoParameters extends SalesReturnRequestPageRecordIdParameter {
    isCalledFromRecordPage: boolean;
}
