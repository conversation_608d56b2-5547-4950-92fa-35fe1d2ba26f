export interface ConfirmParameters {
    status?: string | null;
    approvalStatus?: string | null;
    isApprovalManaged?: boolean | null;
}

export interface DeleteParameters {
    status?: string | null;
}

export interface ShipParameters {
    status?: string | null;
}

export interface CloseParameters {
    status?: string | null;
}

export interface OpenParameters {
    status?: string | null;
    receiptStatus?: string | null;
    creditStatus?: string | null;
    approvalStatus?: string | null;
}

export interface ApproveParameters {
    status?: string | null;
    approvalStatus?: string | null;
}

export interface RejectParameters {
    status?: string | null;
    approvalStatus?: string | null;
}

export interface RequestApprovalParameters {
    status?: string | null;
    approvalStatus?: string | null;
    isApprovalManaged?: boolean | null;
}

export interface CreateSalesReturnRequestFromShipmentsParameters {
    status?: string | null;
    isCreditingAllowed?: boolean;
}

export interface DefaultDimensionParameters {
    status?: string | null;
    receiptStatus: string | null;
}
