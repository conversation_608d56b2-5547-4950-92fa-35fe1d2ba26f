import type { ExtractEdgesPartial } from '@sage/xtrem-client';
import type {
    FinanceIntegrationApp,
    PostingStatus,
    SourceDocumentType,
    TargetDocumentType,
} from '@sage/xtrem-finance-data-api';
import type { Customer } from '@sage/xtrem-master-data-api';
import type { GraphApi } from '@sage/xtrem-sales-api';
import type { Site } from '@sage/xtrem-system-api';
import type * as ui from '@sage/xtrem-ui';

export interface UpdatePrintStatusParameters {
    salesCreditMemoPage: ui.Page<GraphApi>;
    _id: string;
    status: string;
    isPrinted: boolean;
    number: string;
}

export interface PrintSalesCreditMemoParameters {
    isCalledFromRecordPage: boolean;
    salesCreditMemoPage: ui.Page<GraphApi>;
    _id: string;
    status: string;
    number: string;
}

export interface GetDocumentSysIdParameters {
    salesCreditMemoPage: ui.Page<GraphApi>;
    number: string;
}
export interface GetSalesCreditMemoLinesParameter {
    salesCreditMemoPage: ui.Page<GraphApi>;
    recordNumber: string;
}

export interface SetDimensionActionParameter {
    salesCreditMemoPage: ui.Page<GraphApi>;
    recordNumber: string;
    status: string | null;
    site?: ExtractEdgesPartial<Site> | null;
    customer?: ExtractEdgesPartial<Customer> | null;
}

export interface CreditMemoPageWithPost extends ui.Page<GraphApi> {
    post: ui.PageAction;
}

export interface CreditMemoPageParameter {
    creditMemoPage: CreditMemoPageWithPost;
    recordId: string;
}

export interface PostParameter extends CreditMemoPageParameter {
    isCalledFromRecordPage: boolean;
    taxCalculationStatus: string;
    status: string;
}

export interface CreditMemoPostingParameter extends CreditMemoPageParameter {
    isCalledFromRecordPage: boolean;
}

export interface PostDetailsParameter {
    _id: string;
    documentType: TargetDocumentType;
    documentNumber: string;
    documentSysId: number;
    status: PostingStatus;
    message: string;
    hasFinanceIntegrationApp: boolean;
    financeIntegrationApp: FinanceIntegrationApp | null;
    financeIntegrationAppRecordId: string;
    financeIntegrationAppUrl: string;
    externalLink: boolean;
    sourceDocumentType?: SourceDocumentType;
}
