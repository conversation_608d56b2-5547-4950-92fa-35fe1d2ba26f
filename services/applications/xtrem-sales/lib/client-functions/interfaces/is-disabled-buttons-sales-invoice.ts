import type { ExtractEdgesPartial } from '@sage/xtrem-client';
import type { Currency, Customer, PaymentTerm } from '@sage/xtrem-master-data-api';
import type { Site } from '@sage/xtrem-system-api';

export interface SaveParameters {
    status?: string | null;
}

export interface CancelParameters {
    status?: string | null;
}

export interface PostParameters {
    status?: string | null;
    taxCalculationStatus?: string | null;
}

export interface RepostParameters {
    fromNotificationHistory?: boolean;
}

export interface CreateSalesCreditMemosFromInvoicesParameters {
    status?: string | null;
}

export interface LinePhantomRowParameters {
    status?: string | null;
    site?: ExtractEdgesPartial<Site> | null;
    billToCustomer?: ExtractEdgesPartial<Customer> | null;
    date?: string | null;
    dueDate?: string | null;
    currency?: ExtractEdgesPartial<Currency> | null;
}

export interface AddSalesInvoiceLineParameters {
    status?: string | null;
    site?: ExtractEdgesPartial<Site> | null;
    billToCustomer?: ExtractEdgesPartial<Customer> | null;
    date?: string | null;
    dueDate?: string | null;
    currency?: ExtractEdgesPartial<Currency> | null;
}

export interface PrintParameters {
    status?: string | null;
    enablePrintButton?: boolean;
}

export interface SendMailParameters {
    status?: string | null;
    enablePrintButton?: boolean;
}

export interface DefaultDimensionParameters {
    site?: ExtractEdgesPartial<Site> | null;
    dueDate?: string | null;
    paymentTerm?: ExtractEdgesPartial<PaymentTerm> | null;
    date?: string | null | undefined;
    currency?: ExtractEdgesPartial<Currency> | null;
}
