import type * as isDisabledButtonsCommon from '@sage/xtrem-system/build/lib/client-functions/interfaces/is-disabled-buttons';
import type * as isHiddenButtonsCommon from '@sage/xtrem-system/build/lib/client-functions/interfaces/is-hidden-buttons';
import type * as isDisabledButtons from './interfaces/is-disabled-buttons-sales-invoice';
import type * as isHiddenButtons from './interfaces/is-hidden-buttons-sales-invoice';

export function isDisabledButtonSaveAction(
    data: isDisabledButtonsCommon.SaveOrCancelParameters<isDisabledButtons.SaveParameters>,
) {
    if (data.recordId) {
        if (data.parameters.status === 'draft') {
            return !data.isDirty;
        }
        return true;
    }
    return !data.isDirty;
}

export function isDisabledButtonCancelAction(
    data: isDisabledButtonsCommon.SaveOrCancelParameters<isDisabledButtons.SaveParameters>,
) {
    if (data.recordId) {
        if (data.parameters.status === 'draft') {
            return !data.isDirty;
        }
        return true;
    }
    return !data.isDirty;
}

export function isHiddenButtonDeleteAction(
    data: isHiddenButtonsCommon.DeleteParameters<isHiddenButtons.StatusTaxCalcStatusParameters>,
) {
    if ((data.recordId && data.parameters.status === 'draft') || data.parameters.taxCalculationStatus === 'failed') {
        return false;
    }
    return true;
}

/* -------------------*/

export function isHiddenButtonPostAction(
    data: isHiddenButtonsCommon.BusinessButtonParameters<isHiddenButtons.StatusTaxCalcStatusParameters>,
) {
    if (
        data.recordId &&
        ((data.parameters.status === 'draft' && data.parameters.taxCalculationStatus === 'done') ||
            data.parameters.taxCalculationStatus === 'failed')
    ) {
        return data.isDirty;
    }
    return true;
}

export function isDisabledButtonPostAction(
    data: isDisabledButtonsCommon.BusinessButtonParameters<isDisabledButtons.PostParameters>,
) {
    return isHiddenButtonPostAction(data);
}

/* -------------------*/

export function isHiddenButtonRepostAction(
    data: isDisabledButtonsCommon.BusinessButtonParameters<isDisabledButtons.RepostParameters>,
) {
    if (data.recordId && data.parameters.fromNotificationHistory) {
        return !data.isDirty;
    }
    return true;
}

export function isDisabledButtonRepostAction(
    data: isHiddenButtonsCommon.BusinessButtonParameters<isHiddenButtons.RepostParameters>,
) {
    return isHiddenButtonRepostAction(data);
}

/* -------------------*/

export function isHiddenButtonCreateSalesCreditMemosFromInvoicesAction(
    data: isHiddenButtonsCommon.BusinessButtonParameters<isHiddenButtons.CreateSalesCreditMemosFromInvoicesParameters>,
) {
    if (
        data.recordId &&
        data.parameters.status &&
        ['posted'].includes(data.parameters.status) &&
        data.parameters.creditStatus &&
        ['notCredited', 'partiallyCredited'].includes(data.parameters.creditStatus) &&
        data.parameters.taxCalculationStatus !== 'failed'
    ) {
        return data.isDirty;
    }
    return true;
}

export function isDisabledButtonCreateSalesCreditMemosFromInvoicesAction(
    data: isDisabledButtonsCommon.BusinessButtonParameters<isDisabledButtons.CreateSalesCreditMemosFromInvoicesParameters>,
) {
    return isHiddenButtonCreateSalesCreditMemosFromInvoicesAction(data);
}

/* -------------------*/

export function isHiddenButtonRecordReceiptAction(
    data: isHiddenButtonsCommon.BusinessButtonParameters<isHiddenButtons.RecordReceiptParameters>,
) {
    if (
        data.recordId &&
        data.parameters.status &&
        ['posted'].includes(data.parameters.status) &&
        data.parameters.paymentStatus &&
        ['notPaid', 'partiallyPaid'].includes(data.parameters.paymentStatus) &&
        data.parameters.taxCalculationStatus !== 'failed'
    ) {
        return data.isDirty;
    }
    return true;
}

/* -------------------*/

export function isDisabledLinePhantomRow(
    data: isDisabledButtonsCommon.PhantomRowButtonParameters<isDisabledButtons.LinePhantomRowParameters>,
) {
    if (
        data.parameters &&
        !['posted', 'inProgress', 'error'].includes(data.parameters.status || '') &&
        data.parameters.site &&
        data.parameters.billToCustomer &&
        data.parameters.date &&
        data.parameters.dueDate &&
        data.parameters.currency
    ) {
        return false;
    }
    return true;
}

export function isDisabledButtonAddSalesInvoiceLineAction(
    data: isDisabledButtonsCommon.TableFieldActionsActionButtonParameters<isDisabledButtons.AddSalesInvoiceLineParameters>,
) {
    if (
        data.parameters &&
        data.parameters.status !== 'posted' &&
        data.parameters.status !== 'inProgress' &&
        data.parameters.status !== 'error' &&
        data.parameters.site &&
        data.parameters.billToCustomer &&
        data.parameters.date &&
        data.parameters.dueDate &&
        data.parameters.currency
    ) {
        return false;
    }
    return true;
}

/* -------------------*/

function isHiddenPrintAction(
    data: isHiddenButtonsCommon.HeaderQuickActionButtonParameters<isHiddenButtons.PrintSendMailParameters>,
) {
    return !data.recordId || data.parameters.status === 'inProgress' || !data.parameters.enablePrintButton;
}

export function isHiddenButtonPrintAction(
    data: isHiddenButtonsCommon.HeaderQuickActionButtonParameters<isHiddenButtons.PrintSendMailParameters>,
) {
    return isHiddenPrintAction(data);
}

export function isDisabledButtonPrintAction(
    data: isDisabledButtonsCommon.HeaderQuickActionButtonParameters<isDisabledButtons.PrintParameters>,
) {
    return !data.parameters?.enablePrintButton || data.isDirty;
}

/* -------------------*/

export function isHiddenButtonSendMailAction(
    data: isHiddenButtonsCommon.HeaderQuickActionButtonParameters<isHiddenButtons.PrintSendMailParameters>,
) {
    return (
        !data.recordId ||
        ['inProgress', 'draft'].includes(data.parameters.status ?? '') ||
        data.parameters.taxCalculationStatus === 'failed' ||
        data.parameters.displayStatus === 'error' ||
        !data.parameters.enablePrintButton
    );
}

export function isDisabledButtonSendMailAction(
    data: isDisabledButtonsCommon.HeaderQuickActionButtonParameters<isDisabledButtons.SendMailParameters>,
) {
    return data.parameters?.status === 'error' || !data.parameters?.enablePrintButton || data.isDirty;
}

/* -------------------*/

export function isHiddenButtonDefaultDimensionAction(
    data: isHiddenButtonsCommon.HeaderDropDownActionButtonParameters<isHiddenButtons.StatusParameters>,
) {
    if (
        data.recordId &&
        data.parameters &&
        data.parameters.status &&
        ['inProgress', 'error', 'posted'].includes(data.parameters.status)
    ) {
        return true;
    }
    return false;
}

export function isDisabledButtonDefaultDimensionAction(
    data: isDisabledButtonsCommon.HeaderDropDownActionButtonParameters<isDisabledButtons.DefaultDimensionParameters>,
) {
    if (
        data.parameters &&
        data.parameters.site &&
        data.parameters.dueDate &&
        data.parameters.paymentTerm &&
        data.parameters.date &&
        data.parameters.currency
    ) {
        return false;
    }
    return true;
}
/* -------------------*/
