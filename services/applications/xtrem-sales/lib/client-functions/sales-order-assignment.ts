import type { ExtractEdgesPartial } from '@sage/xtrem-client';
import { withoutEdges } from '@sage/xtrem-client';
import { date } from '@sage/xtrem-date-time';
import type { PreferredProcess } from '@sage/xtrem-master-data-api';
import type { GraphApi, SalesOrderLine } from '@sage/xtrem-sales-api';
import type { OrderToOrderDemandType, OrderToOrderSupplyType } from '@sage/xtrem-stock-data-api';
import type { Site } from '@sage/xtrem-system-api';
import * as ui from '@sage/xtrem-ui';
import type { SalesOrder as SalesOrderPage } from '../pages/sales-order';
import type { SalesOrderLineAssignmentDetailsPanel } from '../pages/sales-order-line-assignment-details-panel';
import type { SalesOrderTablePanel as SalesOrderTablePanelPage } from '../pages/sales-order-table-panel';
import type { PurchaseOrderReturned, WorkOrderReturned } from './interfaces/interfaces';
import type { SalesOrderLineAssignment } from './interfaces/order-assignment';
import type { SalesOrderPageWidgetParameter } from './interfaces/sales-order-actions-functions';

async function hasOrderAssignment(
    page: SalesOrderPage,
    demandDocumentLine: string,
    demandType: OrderToOrderDemandType,
    supplyType: OrderToOrderSupplyType,
): Promise<boolean> {
    const orderAssignment = await page.$.graph
        .node('@sage/xtrem-stock-data/OrderAssignment')
        .queries.getDemandAssignment({ _id: true }, { demandType, demandDocumentLine, supplyType })
        .execute();
    if (orderAssignment) {
        page.$.showToast(
            ui.localize('@sage/xtrem-sales/pages__assignment__already_exists', 'Assignment already exists.'),
            { type: 'error' },
        );
        return true;
    }
    return false;
}

function createWorkOrder(
    page: SalesOrderPage,
    line: ui.PartialCollectionValue<SalesOrderLine>,
): Promise<WorkOrderReturned | null> {
    const prodLeadTime = line.itemSite?.prodLeadTime ?? 0;
    if (line.shippingDate && line.itemSite) {
        let startDate = date.parse(line.shippingDate).addDays(-prodLeadTime);
        if (startDate.daysDiff(date.today()) < 0) {
            startDate = date.today();
        }
        return page.$.dialog.page(
            '@sage/xtrem-manufacturing/WorkOrderPanel',
            {
                recommendation: JSON.stringify({
                    item: {
                        name: line.item?.name,
                        id: line.item?.id,
                        item: line.item,
                        site: line.stockSite,
                    },
                    site: line.stockSite,
                    quantity: line.remainingQuantityToShipInStockUnit,
                    startDate,
                    endDate: line.shippingDate,
                    isFromSalesOrder: true,
                    storedDimensions: line.storedDimensions,
                    storedAttributes: line.storedAttributes,
                }),
            },
            {
                rightAligned: true,
                size: 'large',
                resolveOnCancel: true,
            },
        );
    }
    return Promise.resolve(null);
}

function getDefaultPurchasingSite(
    page: SalesOrderPage,
    line: ui.PartialCollectionValue<SalesOrderLine>,
): ExtractEdgesPartial<Site> | null {
    if (page.site.value?.isPurchase) {
        return page.site.value;
    }
    if (line.stockSite?.isPurchase) {
        return line.stockSite;
    }
    return null;
}

function createPurchaseOrder(
    page: SalesOrderPage,
    line: ui.PartialCollectionValue<SalesOrderLine>,
): Promise<PurchaseOrderReturned | null> | null {
    const defaultPurchasingSite = getDefaultPurchasingSite(page, line);
    if (line.shippingDate && line.itemSite && line.stockSite) {
        return page.$.dialog.page(
            '@sage/xtrem-purchasing/ReorderPurchaseOrderPanel',
            {
                recommendation: JSON.stringify({
                    item: line.item,
                    site: defaultPurchasingSite,
                    stockSite: line.stockSite,
                    quantity: line.remainingQuantityToShipInStockUnit,
                    startDate: date.today(),
                    endDate: line.shippingDate,
                    isFromSalesOrder: true,
                    legalCompany: line.stockSite.legalCompany,
                    storedDimensions: line.storedDimensions,
                    storedAttributes: line.storedAttributes,
                }),
            },
            {
                rightAligned: true,
                size: 'large',
                resolveOnCancel: true,
            },
        );
    }
    return null;
}

async function createWorkOrderAssignment(
    page: SalesOrderPage,
    line: ui.PartialCollectionValue<SalesOrderLine>,
    newWorkOrder: WorkOrderReturned,
    demandType: OrderToOrderDemandType,
    supplyType: OrderToOrderSupplyType,
): Promise<boolean> {
    if (supplyType !== 'workOrder') {
        return false;
    }

    let supplyOrderQuantityInStockUnit = newWorkOrder.productionItems.query.edges[0].node.releasedQuantity;
    if (supplyOrderQuantityInStockUnit >= Number(line.remainingQuantityToShipInStockUnit)) {
        supplyOrderQuantityInStockUnit = Number(line.remainingQuantityToShipInStockUnit);
    }

    const orderAssignment = await page.$.graph
        .node('@sage/xtrem-stock-data/OrderAssignment')
        .create(
            { _id: true },
            {
                data: {
                    item: line.item?._id,
                    stockSite: line.stockSite?._id,
                    quantityInStockUnit: supplyOrderQuantityInStockUnit,
                    demandType,
                    demandDocumentLine: line._id,
                    demandWorkInProgress: line.workInProgress?._id ?? '',
                    supplyType,
                    supplyDocumentLine: newWorkOrder.productionItems.query.edges.at(0)?.node._id,
                    supplyWorkInProgress:
                        newWorkOrder.productionItems.query.edges.at(0)?.node.workInProgress?._id ?? '',
                },
            },
        )
        .execute();

    if (!orderAssignment) {
        page.$.showToast(
            ui.localize('@sage/xtrem-sales/pages__sales_order_assignment_already_exists', 'Assignment already exists.'),
            { type: 'error' },
        );
        return false;
    }

    page.$.showToast(
        ui.localize(
            '@sage/xtrem-sales/pages__sales_order__work_order_assignment_created',
            'Work order {{num}} created.',
            {
                num: newWorkOrder.number,
            },
        ),
        { type: 'success' },
    );

    return true;
}

async function createPurchaseOrderAssignment(
    page: SalesOrderPage,
    line: ui.PartialCollectionValue<SalesOrderLine>,
    newPurchaseOrder: PurchaseOrderReturned,
    demandType: OrderToOrderDemandType,
    supplyType: OrderToOrderSupplyType,
): Promise<boolean> {
    if (supplyType !== 'purchaseOrderLine') {
        return false;
    }

    let supplyOrderQuantityInStockUnit = newPurchaseOrder.lines.query.edges[0].node.quantityInStockUnit;
    if (supplyOrderQuantityInStockUnit >= Number(line.remainingQuantityToShipInStockUnit)) {
        supplyOrderQuantityInStockUnit = Number(line.remainingQuantityToShipInStockUnit);
    }

    const orderAssignment = await page.$.graph
        .node('@sage/xtrem-stock-data/OrderAssignment')
        .create(
            { _id: true },
            {
                data: {
                    item: line.item?._id,
                    stockSite: line.stockSite?._id,
                    quantityInStockUnit: supplyOrderQuantityInStockUnit,
                    demandType,
                    demandDocumentLine: line._id,
                    demandWorkInProgress: line.workInProgress?._id ?? '',
                    supplyType,
                    supplyDocumentLine: newPurchaseOrder.lines.query.edges.at(0)?.node._id,
                    supplyWorkInProgress: newPurchaseOrder.lines.query.edges.at(0)?.node.workInProgress?._id ?? '',
                },
            },
        )
        .execute();

    if (!orderAssignment) {
        page.$.showToast(
            ui.localize('@sage/xtrem-sales/pages__sales_order_assignment_already_exists', 'Assignment already exists.'),
            { type: 'error' },
        );
        return false;
    }

    page.$.showToast(
        ui.localize(
            '@sage/xtrem-sales/pages__sales_order__purchase_order_assignment_created',
            'Purchase order {{num}} created.',
            { num: newPurchaseOrder.number },
        ),
        { type: 'success' },
    );

    return true;
}

export async function checkAndCreateOrderAssignment(
    page: SalesOrderPage,
    line: ExtractEdgesPartial<SalesOrderLine>,
    demandType: OrderToOrderDemandType,
    supplyType: OrderToOrderSupplyType,
): Promise<boolean | null> {
    if (supplyType === 'workOrder') {
        if (!line.stockSite?.isManufacturing) {
            page.$.showToast(
                ui.localize(
                    '@sage/xtrem-sales/pages__sales_order__work_order_stock_site_is_not_manufacturing_site',
                    'The stock site on the line is not a manufacturing site.',
                ),
                { type: 'error' },
            );
            return null;
        }
        if (
            line._id &&
            line.itemSite &&
            line.shippingDate &&
            line.item &&
            line.item._id &&
            line.stockSite &&
            line.stockSite._id
        ) {
            const isAlreadyCreated = await hasOrderAssignment(page, line._id, demandType, supplyType);
            if (!isAlreadyCreated) {
                const newWorkOrder = await createWorkOrder(page, line);
                if (newWorkOrder && newWorkOrder._id) {
                    return createWorkOrderAssignment(page, line, newWorkOrder, demandType, supplyType);
                }
            }
        }
        return false;
    }
    if (supplyType === 'purchaseOrderLine') {
        if (
            line._id &&
            line.itemSite &&
            line.shippingDate &&
            line.item &&
            line.item._id &&
            line.stockSite &&
            line.stockSite._id &&
            page.site &&
            page.site.value?._id
        ) {
            const isAlreadyCreated = await hasOrderAssignment(page, line._id, demandType, supplyType);
            if (!isAlreadyCreated) {
                const newPurchaseOrder = await createPurchaseOrder(page, line);
                if (newPurchaseOrder && newPurchaseOrder._id) {
                    return createPurchaseOrderAssignment(page, line, newPurchaseOrder, demandType, supplyType);
                }
            }
        }
        return false;
    }
    return false;
}

export function isOrderToOrderServiceOptionActivated(
    line: ui.PartialCollectionValue<SalesOrderLine>,
    preferredProcess?: PreferredProcess,
): boolean {
    const { _id, itemSite } = line ?? {};
    const { isOrderToOrder, preferredProcess: linePreferredProcess } = itemSite ?? {};

    return !!(_id && itemSite && isOrderToOrder && (!preferredProcess || linePreferredProcess === preferredProcess));
}

export async function openAssignmentDialog(page: ui.Page<GraphApi>, dialogTitle: string, dialogText: string) {
    if (
        await page.$.dialog
            .confirmation('warn', dialogTitle, dialogText, {
                acceptButton: {
                    text: ui.localize('@sage/xtrem-sales/pages-sales-order-dialog-assignment-continue', 'Continue'),
                },
                cancelButton: {
                    text: ui.localize('@sage/xtrem-sales/pages-sales-order-dialog-assignment-cancel', 'Cancel'),
                },
            })
            .then(() => true)
            .catch(() => false)
    ) {
        return true;
    }
    return false;
}

export async function isAssignmentCheckedOnSalesOrderLineQuantityChanged(
    page: SalesOrderPage,
    line: ui.PartialCollectionValue<SalesOrderLine>,
    quantity: number,
) {
    const assignedOrder = await page.$.graph
        .node('@sage/xtrem-sales/SalesOrderLine')
        .queries.assignedOrderAssignment(
            { line: { _id: true }, orderAssignments: { _id: true, quantityInStockUnit: true } },
            { salesOrderLine: line._id ?? '' },
        )
        .execute();

    if (
        assignedOrder.orderAssignments !== null &&
        quantity < Number(assignedOrder.orderAssignments[0].quantityInStockUnit)
    ) {
        return openAssignmentDialog(
            page,
            ui.localize(
                '@sage/xtrem-sales/pages_confirm_assignment_sales_order_line_quantity_changed_dialog_title',
                'Decrease sales order line quantity',
            ),
            ui.localize(
                '@sage/xtrem-sales/pages_confirm_assignment_sales_order_line_quantity_changed_dialog_content',
                'When you decrease this sales order line quantity, the quantity decreases on the linked supply order.',
            ),
        );
    }
    return true;
}

export function isAssignmentCheckedOnSalesOrderLineDelete(
    page: SalesOrderPage,
    line: ui.PartialCollectionValue<SalesOrderLine>,
) {
    if (line.uAssignmentOrder) {
        return openAssignmentDialog(
            page,
            ui.localize(
                '@sage/xtrem-sales/pages_confirm_assignment_sales_order_line_deletion_dialog_title',
                'Delete sales order line',
            ),
            ui.localize(
                '@sage/xtrem-sales/pages_confirm_assignment_sales_order_line_deletion_dialog_content',
                'When you delete this sales order line, the link to the supply order is also deleted.',
            ),
        );
    }
    return true;
}

export function isAssignmentCheckedOnSalesOrderLineClose(
    page: SalesOrderPage,
    line: ui.PartialCollectionValue<SalesOrderLine>,
) {
    if (line.uAssignmentOrder) {
        return openAssignmentDialog(
            page,
            ui.localize(
                '@sage/xtrem-sales/pages_confirm_assignment_sales_order_line_closing_dialog_title',
                'Close sales order line',
            ),
            ui.localize(
                '@sage/xtrem-sales/pages_confirm_assignment_sales_order_line_closing_dialog_content',
                'When you close this sales order line, the link to the supply order remains.',
            ),
        );
    }
    return true;
}

export function isAssignmentCheckedOnSalesOrderDelete(page: SalesOrderPage) {
    if (page.isOrderAssignmentLinked.value) {
        return openAssignmentDialog(
            page,
            ui.localize(
                '@sage/xtrem-sales/pages_confirm_assignment_sales_order_deletion_dialog_title',
                'Delete sales order',
            ),
            ui.localize(
                '@sage/xtrem-sales/pages_confirm_assignment_sales_order_deletion_dialog_content',
                'When you delete this sales order, the links to the supply orders are also deleted.',
            ),
        );
    }
    return true;
}

export function isAssignmentCheckedOnSalesOrderClose(parameters: SalesOrderPageWidgetParameter) {
    if (parameters.isOrderAssignmentLinked) {
        return openAssignmentDialog(
            parameters.salesOrderPage as SalesOrderPage,
            ui.localize(
                '@sage/xtrem-sales/pages_confirm_assignment_sales_order_close_dialog_title',
                'Close sales order',
            ),
            ui.localize(
                '@sage/xtrem-sales/pages_confirm_assignment_sales_order_close_dialog_content',
                'When you close this sales order, the links to the supply orders remain.',
            ),
        );
    }
    return true;
}

export function isAssignmentCheckedOnSalesOrderLineAllocate(
    page: SalesOrderPage,
    line: ui.PartialCollectionValue<SalesOrderLine>,
) {
    if (line.uAssignmentOrder) {
        return openAssignmentDialog(
            page,
            ui.localize(
                '@sage/xtrem-sales/pages_confirm_assignment_sales_order_line_allocate_dialog_title',
                'Allocate sales order line',
            ),
            ui.localize(
                '@sage/xtrem-sales/pages_confirm_assignment_sales_order_line_allocate_dialog_content',
                'This sales order line is linked to a supply order. The stock allocation is performed from the supply order stock entry.',
            ),
        );
    }
    return true;
}

export async function isAssignmentCheckedOnSalesOrderShipNotAllocatedQuantity(
    page: ui.Page<GraphApi>,
    salesOrderSysId: string,
) {
    const { sumActualQuantity, sumQuantityInStockUnit, filteredAssignedOrders } = await page.$.graph
        .node('@sage/xtrem-sales/SalesOrder')
        .queries.orderAssignmentQuantitySum(
            {
                sumActualQuantity: true,
                sumQuantityInStockUnit: true,
                filteredAssignedOrders: { line: { _id: true }, orderAssignments: { _id: true } },
            },
            { salesOrder: salesOrderSysId },
        )
        .execute();
    if (!filteredAssignedOrders.length) {
        return true;
    }
    const dialogTitle = ui.localize(
        '@sage/xtrem-sales/pages_confirm_assignment_sales_order_ship_remaining_quantity_not_allocated_dialog_title',
        'Ship sales order',
    );
    const dialogContent =
        sumActualQuantity < sumQuantityInStockUnit
            ? ui.localize(
                  '@sage/xtrem-sales/pages_confirm_assignment_sales_order_ship_required_quantity_is_not_received',
                  'This sales order line is linked to a supply order. The required quantity is not received.',
              )
            : ui.localize(
                  '@sage/xtrem-sales/pages_confirm_assignment_sales_order_ship_remaining_quantity_not_allocated_dialog_content',
                  'This sales order is linked to a released supply order. The remaining quantity to ship is not allocated.',
              );
    return !!(await openAssignmentDialog(page, dialogTitle, dialogContent));
}

export async function isAssignmentCheckedOnSalesOrderLineShipNotAllocatedQuantity(
    page: SalesOrderTablePanelPage,
    line: ui.PartialCollectionValue<SalesOrderLine>,
) {
    const assignedOrder = await page.$.graph
        .node('@sage/xtrem-sales/SalesOrderLine')
        .queries.assignedOrderAssignment(
            { line: { _id: true, suppliedQuantity: true }, orderAssignments: { _id: true, quantityInStockUnit: true } },
            { salesOrderLine: line._id ?? '' },
        )
        .execute();

    if (
        assignedOrder.orderAssignments === null ||
        Number(line.quantityAllocated) >= Number(line.remainingQuantityToShipInStockUnit)
    ) {
        return true;
    }

    const dialogTitle = ui.localize(
        '@sage/xtrem-sales/pages_confirm_assignment_sales_order_ship_remaining_quantity_not_allocated_dialog_title',
        'Ship sales order',
    );
    const dialogContent =
        Number(assignedOrder.line.suppliedQuantity) < Number(assignedOrder.orderAssignments[0].quantityInStockUnit)
            ? ui.localize(
                  '@sage/xtrem-sales/pages_confirm_assignment_sales_order_ship_required_quantity_is_not_received',
                  'This sales order line is linked to a supply order. The required quantity is not received.',
              )
            : ui.localize(
                  '@sage/xtrem-sales/pages_confirm_assignment_sales_order_ship_remaining_quantity_not_allocated_dialog_content',
                  'This sales order is linked to a released supply order. The remaining quantity to ship is not allocated.',
              );

    return openAssignmentDialog(page, dialogTitle, dialogContent) ?? false;
}

export async function openAssignments(
    salesOrderPage: SalesOrderPage,
    saleOrderLine: ui.PartialCollectionValue<SalesOrderLine>,
): Promise<void> {
    if (!saleOrderLine?._id || (!saleOrderLine.uWorkOrderLine && !saleOrderLine.uPurchaseOrderLine)) {
        return;
    }
    const supplyOrderType: OrderToOrderSupplyType = saleOrderLine.uWorkOrderLine ? 'workOrder' : 'purchaseOrderLine';

    await salesOrderPage.$.dialog.page(
        '@sage/xtrem-sales/SalesOrderLineAssignmentDetailsPanel',
        {
            line: JSON.stringify({
                lineId: saleOrderLine._id,
                quantityInStockUnit: saleOrderLine.quantityInStockUnit,
                supplyOrderType: `${supplyOrderType}`,
                remainingQuantityToShipInStockUnit: saleOrderLine.remainingQuantityToShipInStockUnit,
                itemStockUnit: saleOrderLine.stockUnit,
                status: saleOrderLine.status,
            }),
        },
        {
            rightAligned: false,
            size: 'large',
            resolveOnCancel: true,
        },
    );
}

export async function queryOrderAssignment({
    orderAssignmentPage,
    salesOrderLineId,
    demandType,
}: {
    orderAssignmentPage: SalesOrderLineAssignmentDetailsPanel;
    salesOrderLineId: string;
    demandType?: string;
}): Promise<SalesOrderLineAssignment[]> {
    return withoutEdges(
        await orderAssignmentPage.$.graph
            .node('@sage/xtrem-stock-data/OrderAssignment')
            .query(
                ui.queryUtils.edgesSelector(
                    {
                        _id: true,
                        demandType: true,
                        supplyType: true,
                        demandDocumentLine: { _id: true },
                        supplyDocumentLine: {
                            _id: true,
                            documentId: true,
                            documentNumber: true,
                        },
                        quantityInStockUnit: true,
                        supplyWorkInProgress: {
                            expectedQuantity: true,
                        },
                    },
                    {
                        filter: {
                            demandType: { _eq: demandType },
                            demandDocumentLine: { _eq: salesOrderLineId },
                        },
                    },
                ),
            )
            .execute(),
    );
}
