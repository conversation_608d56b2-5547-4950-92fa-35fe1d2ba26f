import { confirmDialogWithAcceptButtonTextWidgetPage } from '@sage/xtrem-distribution/build/lib/client-functions/common';
import * as MasterDataUtils from '@sage/xtrem-master-data/build/lib/client-functions/utils';
import type { SalesOrderConfirmMethodReturn } from '@sage/xtrem-sales-api';
import * as ui from '@sage/xtrem-ui';
import { SalesOrder as SalesOrderPage } from '../pages/sales-order';
import type { PageOrWidgetType } from './interfaces/common';
import type {
    ConfirmSalesOrderParameters,
    NotificationContentOptions,
} from './interfaces/sales-order-actions-functions';
import * as SalesOrderCommonAction from './sales-order-common-action';

export function showOnHoldConfirmMessage(pageOrWidget: PageOrWidgetType) {
    return confirmDialogWithAcceptButtonTextWidgetPage(pageOrWidget.$.dialog, {
        title: ui.localize(
            '@sage/xtrem-sales/pages__sales_order_confirm_on_hold_action_dialog_title',
            'Customer on hold',
        ),
        message: ui.localize(
            '@sage/xtrem-sales/pages__sales_order___confirm_on_hold_action__action_dialog_content',
            'You are about to confirm the sales order for an on hold customer.',
        ),
        acceptButtonText: ui.localize('@sage/xtrem-sales/pages-confirm-continue', 'Continue'),
    });
}

export function showTaxCalcFiledBlockingMessage(pageOrWidget: PageOrWidgetType) {
    return pageOrWidget.$.showToast(
        ui.localize(
            '@sage/xtrem-sales/pages__sales_order__tax_calculation_failed_blocking',
            'You need to resolve tax calculation issues before confirming.',
        ),
        { type: 'error' },
    );
}

function setSalesOrderToConfirmedMessage(pageOrWidget: PageOrWidgetType) {
    return confirmDialogWithAcceptButtonTextWidgetPage(pageOrWidget.$.dialog, {
        title: ui.localize('@sage/xtrem-sales/pages__sales_order__confirm_action_dialog_title', 'Confirm update'),
        message: ui.localize(
            '@sage/xtrem-sales/pages__sales_order__confirm_action_dialog_content',
            "You are about to set this sales order to 'Confirmed'.",
        ),
        acceptButtonText: ui.localize('@sage/xtrem-sales/pages-confirm-confirm', 'Confirm'),
    });
}

/**
 * Runs the confirmSalesOrder mutation
 */
async function executeConfirmSalesOrderMutation(
    parameters: ConfirmSalesOrderParameters,
): Promise<SalesOrderConfirmMethodReturn | boolean> {
    const confirmMutation = parameters.pageOrWidget.$.graph
        .node('@sage/xtrem-sales/SalesOrder')
        .mutations.confirmSalesOrder({ status: true }, { salesOrder: parameters._id });

    if (parameters.pageOrWidget instanceof SalesOrderPage) {
        return (await confirmMutation.execute()).status !== 'quote' ? 'salesOrderIsConfirmed' : false;
    }
    try {
        return (await confirmMutation.execute()).status !== 'quote' ? 'salesOrderIsConfirmed' : false;
    } catch (error) {
        parameters.pageOrWidget.$.showToast(MasterDataUtils.formatError(parameters.pageOrWidget, error), {
            type: 'error',
        });
        return false;
    }
}

export function getLocalizeSalesOrderConfirmMethodReturn(
    returnStatus: SalesOrderConfirmMethodReturn,
): NotificationContentOptions {
    switch (returnStatus) {
        case 'parametersAreIncorrect':
            return {
                content: ui.localize(
                    '@sage/xtrem-sales/pages_functions__sales_order_confirm_action_not_allowed_incorrect_parameters',
                    'The confirmation failed. The parameters are incorrect.',
                ),
                severity: 'error',
            };
        case 'salesOrderIsConfirmed':
            return {
                content: ui.localize(
                    '@sage/xtrem-sales/pages_functions__sales_order_confirm_action_is_confirmed',
                    'The sales order was confirmed.',
                ),
                severity: 'success',
            };
        default:
            return {
                content: ui.localize(
                    '@sage/xtrem-sales/pages_functions__sales_order_confirm_action_status_is_not_draft',
                    "The confirmation failed. The sales order is not 'Quote'.",
                ),
                severity: 'error',
            };
    }
}

function getReturnStatusForToast(
    parameters: ConfirmSalesOrderParameters,
): Promise<SalesOrderConfirmMethodReturn | boolean> | SalesOrderConfirmMethodReturn {
    if (parameters.status !== 'quote') {
        return 'salesOrderStatusIsNotDraft';
    }
    return executeConfirmSalesOrderMutation(parameters);
}

export async function confirmSalesOrderAction(parameters: ConfirmSalesOrderParameters) {
    const confirmAction =
        parameters.isOnHold && parameters.customerOnHoldCheck === 'warning'
            ? await showOnHoldConfirmMessage(parameters.pageOrWidget)
            : await setSalesOrderToConfirmedMessage(parameters.pageOrWidget);

    if (confirmAction) {
        parameters.pageOrWidget.$.loader.isHidden = false;
        const confirmStatus = await getReturnStatusForToast(parameters);
        if (typeof confirmStatus !== 'boolean') {
            const returnValue = getLocalizeSalesOrderConfirmMethodReturn(confirmStatus);
            // TODO:
            // https://jira.sage.com/browse/XT-56067
            // parameters.salesOrderPage.$.showToast(returnValue.content, returnValue.options);
            parameters.pageOrWidget.$.showToast(returnValue.content, {
                type: returnValue.severity === 'error' ? 'error' : 'success',
            });
        }
        parameters.pageOrWidget.$.loader.isHidden = true;
        if (parameters.pageOrWidget instanceof SalesOrderPage) {
            await parameters.pageOrWidget.$.refreshNavigationPanel();
            await parameters.pageOrWidget.$.router.refresh();
        }
    }
}

export async function confirmSalesOrderFunction(parameters: ConfirmSalesOrderParameters) {
    if (parameters.isOnHold && parameters.customerOnHoldCheck === 'blocking') {
        SalesOrderCommonAction.showOnHoldBlockingMessage(parameters.pageOrWidget);
        return;
    }
    if (parameters.taxCalculationStatus === 'failed') {
        showTaxCalcFiledBlockingMessage(parameters.pageOrWidget);
        return;
    }
    await confirmSalesOrderAction(parameters);
}
