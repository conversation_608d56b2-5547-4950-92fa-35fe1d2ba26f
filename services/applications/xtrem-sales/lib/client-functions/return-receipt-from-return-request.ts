import { asyncArray } from '@sage/xtrem-async-helper';
import type { ExtractEdgesPartial, Filter } from '@sage/xtrem-client';
import * as dimensionPanelHelpers from '@sage/xtrem-finance-data/build/lib/client-functions/dimension-panel-helpers';
import type { Address } from '@sage/xtrem-master-data-api';
import { getConcatenatedAddress } from '@sage/xtrem-master-data/build/lib/shared-functions/address-functions';
import type {
    SalesReturnReceiptLine,
    SalesReturnRequestLine,
    SalesReturnRequestLineToSalesReturnReceiptLine,
} from '@sage/xtrem-sales-api';
import * as StockDocumentHelper from '@sage/xtrem-stock-data/build/lib/client-functions/stock-document-helper';
import * as ui from '@sage/xtrem-ui';
import { isArray } from 'lodash';
import type { SalesReturnReceipt as SalesReturnReceiptPage } from '../pages/sales-return-receipt';
import * as PillColorSales from './pill-color';

function convertRequestLineToReturnRequestLines(
    returnRequestLine: ExtractEdgesPartial<SalesReturnRequestLine & { selectedQuantity: number }>,
): ui.PartialCollectionValue<SalesReturnRequestLineToSalesReturnReceiptLine> {
    return {
        linkedDocument: returnRequestLine != null ? returnRequestLine : undefined,
        quantity: String(returnRequestLine.selectedQuantity),
        quantityInSalesUnit: String(
            (returnRequestLine.selectedQuantity ? +returnRequestLine.selectedQuantity : 0) *
                (returnRequestLine.unitToStockUnitConversionFactor
                    ? +returnRequestLine.unitToStockUnitConversionFactor
                    : 0),
        ),
    };
}

function matchingAddressLine(
    firstReturnRequestLineAddress: ExtractEdgesPartial<Address>,
    returnRequestLineAddress: ExtractEdgesPartial<Address>,
): boolean {
    return (
        firstReturnRequestLineAddress &&
        returnRequestLineAddress &&
        firstReturnRequestLineAddress._id === returnRequestLineAddress._id
    );
}

function convertRequestLinetoReceiptLine(
    returnReceiptPage: SalesReturnReceiptPage,
    returnRequestLine: ExtractEdgesPartial<SalesReturnRequestLine & { selectedQuantity: number }>,
): ui.PartialCollectionValue<SalesReturnReceiptLine> {
    const toReturnRequestLine = convertRequestLineToReturnRequestLines(returnRequestLine);

    const line = dimensionPanelHelpers.applyDefaultDimensionsOnNewLineIfNeeded(
        { ...returnRequestLine, _id: returnRequestLine._id ?? '' },
        returnReceiptPage._defaultDimensionsAttributes,
    );

    return {
        origin: 'return',
        item: returnRequestLine.item,
        itemDescription: returnRequestLine.itemDescription,
        unit: returnRequestLine.unit,
        unitToStockUnitConversionFactor: returnRequestLine.unitToStockUnitConversionFactor,
        stockUnit: returnRequestLine.stockUnit,
        quantity: `${returnRequestLine.selectedQuantity}`,
        quantityInStockUnit: (
            (returnRequestLine.selectedQuantity ? +returnRequestLine.selectedQuantity : 0) *
            (returnRequestLine.unitToStockUnitConversionFactor ? +returnRequestLine.unitToStockUnitConversionFactor : 0)
        ).toString(),
        storedAttributes: line.storedAttributes,
        storedDimensions: line.storedDimensions,
        toReturnRequestLines: [toReturnRequestLine],
        isReceiptExpected: returnRequestLine.isReceiptExpected,
        stockDetailStatus:
            returnRequestLine.item?.isStockManaged && returnRequestLine.isReceiptExpected
                ? StockDocumentHelper.getStockDetailStatus(Number(returnRequestLine?.selectedQuantity), null)
                : 'notRequired',
    };
}

function getToReturnRequestLineIds(returnReceiptPage: SalesReturnReceiptPage): string[] {
    return returnReceiptPage.lines.value
        .flatMap(line => line.toReturnRequestLines ?? [])
        .map(toReturnRequestLine => toReturnRequestLine.linkedDocument?._id)
        .filter((line): line is string => typeof line === 'string' && line !== undefined);
}

function filterForReturnRequestLine(returnReceiptPage: SalesReturnReceiptPage): Filter<SalesReturnRequestLine> {
    const returnReceiptLinesSysId = returnReceiptPage.lines.value.map(line => line._id);
    return {
        _id: { _nin: getToReturnRequestLineIds(returnReceiptPage) },
        document: {
            approvalStatus: { _in: ['approved', 'confirmed'] },
            returnType: { _ne: 'creditMemo' },
            stockSite: { _id: returnReceiptPage.site.value?._id },
            shipToCustomerAddress: { _id: returnReceiptPage.shipToCustomerAddress.value?._id },
            shipToCustomer: { _id: returnReceiptPage.shipToCustomer.value?._id },
        },
        status: { _in: ['pending', 'inProgress', 'confirmed'] },
        receiptStatus: { _ne: 'received' },
        isReceiptExpected: true,
        salesReturnReceiptLines: { _atMost: 0, document: { _id: { _in: returnReceiptLinesSysId } } },
    };
}

function selectedQuantityValidation(
    value: number,
    uQuantityReceiptAndToReceiveInSalesUnit: string,
): string | undefined {
    if (value <= 0) {
        return ui.localize(
            '@sage/xtrem-sales/return-request-to-receipt__zero_quantity_error',
            'You cannot enter a quantity less than or equal to zero.',
        );
    }
    if (value > +uQuantityReceiptAndToReceiveInSalesUnit) {
        return ui.localize(
            '@sage/xtrem-sales/return-request-to-receipt__confirm_greater_quantity',
            'You cannot receive a quantity more than the return request you select.',
        );
    }
    return undefined;
}

// return type of lookupOrderLine function
export type ReturnRequestLineLookup = Awaited<ReturnType<typeof lookupReturnRequestLine>>[0];

export function lookupReturnRequestLine(returnReceiptPage: SalesReturnReceiptPage) {
    return returnReceiptPage.$.dialog.lookup<SalesReturnRequestLine & { selectedQuantity: number }>({
        resolveOnCancel: true,
        isEditable: true,
        isMultiSelect: true,
        size: 'extra-large',
        acceptButton: { text: 'Select' },
        dialogTitle: 'Select return request lines',
        node: '@sage/xtrem-sales/SalesReturnRequestLine',
        filter: filterForReturnRequestLine(returnReceiptPage),
        orderBy: { document: { number: +1 }, _sortValue: +1 },
        mapServerRecord(record) {
            const { uQuantityReceiptAndToReceiveInSalesUnit } = record;
            // Use a simple addition instead of typesLib.plus
            const quantity = Number(uQuantityReceiptAndToReceiveInSalesUnit ?? 0);
            return { selectedQuantity: quantity, ...record };
        },
        columns: [
            ui.nestedFields.image({ bind: { item: { image: true } }, size: 'small' }),
            ui.nestedFields.reference({
                bind: 'item',
                valueField: 'name',
                node: '@sage/xtrem-master-data/Item',
                isReadOnly: true,
                columns: [
                    ui.nestedFields.text({ bind: 'name' }),
                    ui.nestedFields.text({ bind: 'id' }),
                    ui.nestedFields.text({ bind: 'description' }),
                    ui.nestedFields.text({ title: 'Category', bind: { category: { name: true } } }),
                    ui.nestedFields.technical({ bind: 'isStockManaged' }),
                ],
            }),
            ui.nestedFields.technical({ bind: 'itemDescription' }),
            ui.nestedFields.reference({
                bind: 'document',
                title: 'Number',
                valueField: 'number',
                node: '@sage/xtrem-sales/SalesReturnRequest',
                isReadOnly: true,
                columns: [
                    ui.nestedFields.technical({
                        bind: 'shipToAddress',
                        nestedFields: [
                            ui.nestedFields.technical({ bind: 'name' }),
                            ui.nestedFields.technical({ bind: 'addressLine1' }),
                            ui.nestedFields.technical({ bind: 'addressLine2' }),
                            ui.nestedFields.technical({ bind: 'city' }),
                            ui.nestedFields.technical({ bind: 'region' }),
                            ui.nestedFields.technical({ bind: 'postcode' }),
                            ui.nestedFields.technical({
                                bind: 'country',
                                nestedFields: [
                                    ui.nestedFields.technical({ bind: 'name' }),
                                    ui.nestedFields.technical({ bind: 'id' }),
                                    ui.nestedFields.technical({ bind: 'regionLabel' }),
                                    ui.nestedFields.technical({ bind: 'zipLabel' }),
                                ],
                            }),
                            ui.nestedFields.technical({ bind: 'locationPhoneNumber' }),
                        ],
                    }),
                ],
            }),
            ui.nestedFields.label({
                title: 'Status',
                bind: 'receiptStatus',
                optionType: '@sage/xtrem-sales/SalesDocumentReturnRequestReceiptStatus',
                style: (_id: string, rowData: any) =>
                    PillColorSales.getLabelColorByStatus(
                        'SalesDocumentReturnRequestReceiptStatus',
                        rowData?.receiptStatus,
                    ),
            }),
            ui.nestedFields.reference({
                bind: 'document',
                title: 'Return request date',
                valueField: 'date',
                node: '@sage/xtrem-sales/SalesReturnRequest',
                isReadOnly: true,
            }),
            ui.nestedFields.technical({ bind: 'uQuantityReceiptAndToReceiveInSalesUnit' }),
            ui.nestedFields.technical({ bind: '_sortValue' }),
            ui.nestedFields.technical({
                bind: 'unit',
                nestedFields: [
                    ui.nestedFields.technical({ bind: 'name' }),
                    ui.nestedFields.technical({ bind: 'id' }),
                    ui.nestedFields.technical({ bind: 'symbol' }),
                    ui.nestedFields.technical({ bind: 'description' }),
                    ui.nestedFields.technical({ bind: 'decimalDigits' }),
                ],
            }),
            ui.nestedFields.technical({ bind: 'storedAttributes' }),
            ui.nestedFields.technical({ bind: 'storedDimensions' }),
            ui.nestedFields.technical({ bind: 'isReceiptExpected' }),
            ui.nestedFields.technical({
                node: '@sage/xtrem-sales/SalesReturnRequest',
                bind: 'document',
                nestedFields: [
                    ui.nestedFields.technical({ bind: 'number' }),
                    ui.nestedFields.technical({ bind: 'isTransferHeaderNote' }),
                    ui.nestedFields.technical({ bind: 'isTransferLineNote' }),
                ],
            }),
            ui.nestedFields.numeric({
                bind: 'selectedQuantity',
                title: 'Quantity to receive',
                isTransient: true,
                unitMode: 'unitOfMeasure',
                unit: (_id, row) => row?.unit,
                validation(value: number, rowData) {
                    return selectedQuantityValidation(value, rowData.uQuantityReceiptAndToReceiveInSalesUnit);
                },
            }),
            ui.nestedFields.technical({ bind: 'unitToStockUnitConversionFactor' }),
        ],
    });
}

function updateHeaderDocument(
    returnReceiptPage: SalesReturnReceiptPage,
    returnRequestLine: ExtractEdgesPartial<
        SalesReturnRequestLine & {
            selectedQuantity: number;
        }
    >,
): void {
    returnReceiptPage.shipToAddress.value = returnRequestLine.document?.shipToAddress ?? null;
    if (returnReceiptPage.shipToAddress.value?.concatenatedAddress) {
        returnReceiptPage.shipToAddress.value.concatenatedAddress = getConcatenatedAddress(
            returnReceiptPage.shipToAddress.value,
        );
    }
    returnReceiptPage.customSave.isDisabled = false;
}

function updateLines(
    returnReceiptPage: SalesReturnReceiptPage,
    returnReceiptLine: ExtractEdgesPartial<SalesReturnReceiptLine>,
) {
    if (returnReceiptLine.toReturnRequestLines) {
        returnReceiptPage.salesReturnRequestLineToSalesReturnReceiptLines.addOrUpdateRecordValue(
            returnReceiptLine.toReturnRequestLines[0],
        );
    }
    returnReceiptPage.lines.addRecord(returnReceiptLine);
}

export async function addLineFromReturnRequest(returnReceiptPage: SalesReturnReceiptPage) {
    const returnRequestLines = (await lookupReturnRequestLine(
        returnReceiptPage,
    )) as unknown as ui.PartialCollectionValue<SalesReturnRequestLine & { selectedQuantity: number }>[];
    if (!returnRequestLines || !isArray(returnRequestLines) || returnRequestLines.length === 0) {
        return;
    }

    let differentAddresses = false;

    const firstReturnRequestLineAddress =
        returnReceiptPage.lines.value.length === 0
            ? (returnRequestLines[0].document?.shipToAddress ?? null)
            : (returnReceiptPage.shipToAddress?.value ?? null);

    await asyncArray(returnRequestLines).forEach(
        (returnRequestLine: ExtractEdgesPartial<SalesReturnRequestLine & { selectedQuantity: number }>) => {
            const returnReceiptLine = convertRequestLinetoReceiptLine(returnReceiptPage, returnRequestLine);

            if (returnReceiptPage.lines.value.length === 0) {
                updateHeaderDocument(returnReceiptPage, returnRequestLine);
                updateLines(returnReceiptPage, returnReceiptLine);
                return;
            }

            const shipToAddress = returnRequestLine.document?.shipToAddress ?? null;

            const isMatchingLine =
                firstReturnRequestLineAddress && shipToAddress
                    ? matchingAddressLine(firstReturnRequestLineAddress, shipToAddress)
                    : true;

            if (isMatchingLine) {
                updateLines(returnReceiptPage, returnReceiptLine);
            } else {
                differentAddresses = true;
            }
        },
    );

    if (differentAddresses) {
        returnReceiptPage.$.showToast(
            ui.localize(
                '@sage/xtrem-sales/pages__return_request_to_return_receipt__warning_different_information',
                'Some lines were not added as they have different shipping details.',
            ),
            { timeout: 3000, type: 'warning' },
        );
    }
}
