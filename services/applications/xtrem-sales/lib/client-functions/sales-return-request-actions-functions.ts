import { asyncArray } from '@sage/xtrem-async-helper';
import * as authorizationFilters from '@sage/xtrem-authorization/build/lib/client-functions/filters';
import { extractEdges, withoutEdges } from '@sage/xtrem-client';
import { confirmDialogWithAcceptButtonText } from '@sage/xtrem-distribution/build/lib/client-functions/common';
import type {
    SalesReturnRequestCloseStatusMethodReturn,
    SalesReturnRequestOpenStatusMethodReturn,
} from '@sage/xtrem-sales-api';
import { BusinessRuleError } from '@sage/xtrem-shared';
import type { GraphApi } from '@sage/xtrem-system-api';
import * as ui from '@sage/xtrem-ui';
import type * as userInterfaces from '../shared-functions/interfaces';
import { getDimensionsForSalesLines, getValuesForSetDimensionsFromMainList } from './finance-integration';
import type {
    ActionParameter,
    ApproveRejectActionParameter,
    CloseActionParameter,
    CreateCreditMemoParameters,
    LoadApproversParameters,
    OpenActionParameters,
    SalesReturnRequestPageRecordIdParameter,
    SetDimensionActionParameter,
    SiteApprover,
    UserApprover,
} from './interfaces/sales-return-request-actions-functions';

async function getSalesReturnReceiptLinesForDimensions(parameters: ActionParameter) {
    return extractEdges(
        await parameters.salesReturnRequestPage.$.graph
            .node('@sage/xtrem-sales/SalesReturnRequestLine')
            .query(
                ui.queryUtils.edgesSelector(
                    {
                        _id: true,
                        storedDimensions: true,
                        storedAttributes: true,
                        document: {
                            number: true,
                        },
                    },

                    {
                        filter: {
                            document: {
                                number: parameters.recordNumber,
                            },
                        },
                    },
                ),
            )
            .execute(),
    );
}

export async function setDimensions(parameters: SetDimensionActionParameter) {
    let dataDetermined = false;
    let dimensionsToSet = '';
    let attributesToSet = '';
    const linesToSetDimensions = await getSalesReturnReceiptLinesForDimensions({
        salesReturnRequestPage: parameters.salesReturnRequestPage,
        recordNumber: parameters.recordNumber,
    });

    await asyncArray(linesToSetDimensions).some(async lineToSetDimensions => {
        const loopResponse = false;
        if (!dataDetermined) {
            const { defaultedFromItem, defaultDimensionsAttributes } = await getValuesForSetDimensionsFromMainList({
                page: parameters.salesReturnRequestPage as ui.Page<GraphApi>,
                site: parameters.site || null,
                customer: parameters.customer || null,
            });
            const { resultDimensionsToSet, resultAttributesToSet, resultDataDetermined } =
                await getDimensionsForSalesLines({
                    line: lineToSetDimensions,
                    salesPage: parameters.salesReturnRequestPage as ui.Page<GraphApi>,
                    defaultDimensionsAttributes,
                    defaultedFromItem,
                });
            if (!resultDataDetermined) {
                return true;
            }
            dimensionsToSet = resultDimensionsToSet;
            attributesToSet = resultAttributesToSet;
            dataDetermined = resultDataDetermined;
        }
        if (dataDetermined) {
            await parameters.salesReturnRequestPage.$.graph
                .node('@sage/xtrem-sales/SalesReturnRequestLine')
                .mutations.setDimension(true, {
                    baseDocumentItemLine: lineToSetDimensions._id,
                    storedDimensions: dimensionsToSet,
                    storedAttributes: attributesToSet,
                })
                .execute();
        }
        return loopResponse;
    });
    parameters.salesReturnRequestPage.$.showToast(
        ui.localize('@sage/xtrem-sales/pages__sales_return_request__apply_dimensions_success', 'Dimensions applied.'),
        { type: 'success' },
    );
}

export async function confirmSalesReturnRequest(parameters: ActionParameter) {
    if (
        await confirmDialogWithAcceptButtonText(
            parameters.salesReturnRequestPage,
            ui.localize(
                '@sage/xtrem-sales/pages__sales_return_request__confirm_dialog_title',
                'Confirm sales return request',
            ),
            ui.localize(
                '@sage/xtrem-sales/pages__sales_return_request__confirm_dialog_content',
                'You are about to set this sales return request to "Confirmed"',
            ),
            ui.localize('@sage/xtrem-sales/pages_confirm_button', 'Confirm'),
        )
    ) {
        parameters.salesReturnRequestPage.$.loader.isHidden = false;

        const confirmationSuccessful = await parameters.salesReturnRequestPage.$.graph
            .node('@sage/xtrem-sales/SalesReturnRequest')
            .mutations.confirm(true, { salesReturnRequestNumber: parameters.recordNumber ?? '' })
            .execute();

        if (confirmationSuccessful) {
            parameters.salesReturnRequestPage.$.showToast(
                ui.localize(
                    '@sage/xtrem-sales/pages__sales_return_request__confirmation_successful',
                    'Confirmation successful',
                ),
                { type: 'success' },
            );
        }
        await parameters.salesReturnRequestPage.$.router.refresh();
        await parameters.salesReturnRequestPage.$.refreshNavigationPanel();
        parameters.salesReturnRequestPage.$.loader.isHidden = true;
    }
}

export async function loadApprovers(
    parameters: LoadApproversParameters,
): Promise<{ usersList: UserApprover[]; emailAddressApproval: string }> {
    let salesReturnRequestDefaultApprover: userInterfaces.FilteredUsers | null = null;
    let salesReturnRequestSubstituteApprover: userInterfaces.FilteredUsers | null = null;
    let emailAddressApproval = '';
    const usersList: UserApprover[] = [];
    let sortOrder = 0;

    const usersQuery = (await parameters.salesReturnRequestPage.$.graph
        .node('@sage/xtrem-sales/SalesReturnRequest')
        .queries.getFilteredUsers(
            {
                _id: true,
                email: true,
                firstName: true,
                lastName: true,
            },
            {
                criteria: authorizationFilters.user.activeApplicationUsers,
            },
        )
        .execute()) as userInterfaces.FilteredUsers[];

    const siteApprovers: SiteApprover[] = withoutEdges(
        await parameters.salesReturnRequestPage.$.graph
            .node('@sage/xtrem-system/Site')
            .query(
                ui.queryUtils.edgesSelector(
                    {
                        id: true,
                        salesReturnRequestDefaultApprover: {
                            _id: true,
                            email: true,
                            firstName: true,
                            lastName: true,
                        },
                        salesReturnRequestSubstituteApprover: {
                            _id: true,
                            email: true,
                            firstName: true,
                            lastName: true,
                        },
                    },
                    {
                        filter: {
                            _id: parameters.siteId,
                        },
                    },
                ),
            )
            .execute(),
    );

    if (siteApprovers.length > 0) {
        salesReturnRequestDefaultApprover = siteApprovers[0].salesReturnRequestDefaultApprover
            ? siteApprovers[0].salesReturnRequestDefaultApprover
            : null;
        salesReturnRequestSubstituteApprover = siteApprovers[0].salesReturnRequestSubstituteApprover
            ? siteApprovers[0].salesReturnRequestSubstituteApprover
            : null;

        if (salesReturnRequestDefaultApprover?._id) {
            usersList.push({
                ...salesReturnRequestDefaultApprover,
                type: ui.localize('@sage/xtrem-sales/pages__sales_return_request__default_approver', 'Default'),
                sortOrder,
            });
            sortOrder += 1;
            emailAddressApproval = salesReturnRequestDefaultApprover.email;
        }

        if (
            salesReturnRequestSubstituteApprover &&
            salesReturnRequestSubstituteApprover?._id !== salesReturnRequestDefaultApprover?._id
        ) {
            usersList.push({
                ...salesReturnRequestSubstituteApprover,
                type: ui.localize('@sage/xtrem-sales/pages__sales_return_request__substitute_approver', 'Substitute'),
                sortOrder,
            });
            sortOrder += 1;
            if (!salesReturnRequestDefaultApprover) {
                emailAddressApproval = salesReturnRequestSubstituteApprover.email;
            }
        }
    }
    usersList.push(
        ...usersQuery
            .filter(
                userFilter =>
                    ![salesReturnRequestDefaultApprover?._id, salesReturnRequestSubstituteApprover?._id].includes(
                        userFilter._id,
                    ),
            )
            .map((user, index) => ({
                ...user,
                type: '',
                sortOrder: sortOrder + index,
            })),
    );
    return { usersList, emailAddressApproval };
}

export async function approve(parameters: ApproveRejectActionParameter) {
    if (
        await confirmDialogWithAcceptButtonText(
            parameters.salesReturnRequestPage,
            ui.localize(
                '@sage/xtrem-sales/pages__sales_return_request__approve_action_dialog_title',
                'Confirm approving',
            ),
            ui.localize(
                '@sage/xtrem-sales/pages__sales_return_request__approve_action_dialog_content',
                'You are about to approve this sales return request.',
            ),
            ui.localize('@sage/xtrem-sales/pages-confirm-approve', 'Approve'),
        )
    ) {
        parameters.salesReturnRequestPage.$.loader.isHidden = false;
        const isApproved = await parameters.salesReturnRequestPage.$.graph
            .node('@sage/xtrem-sales/SalesReturnRequest')
            .mutations.approve(true, { returnRequest: parameters.recordId })
            .execute();
        parameters.salesReturnRequestPage.$.showToast(isApproved, { type: 'success' });
        await parameters.salesReturnRequestPage.$.router.refresh();
        await parameters.salesReturnRequestPage.$.refreshNavigationPanel();
        parameters.salesReturnRequestPage.$.loader.isHidden = true;
    }
}

export async function reject(parameters: ApproveRejectActionParameter) {
    if (
        await confirmDialogWithAcceptButtonText(
            parameters.salesReturnRequestPage,
            ui.localize(
                '@sage/xtrem-sales/pages__sales_return_request__reject_action_dialog_title',
                'Confirm rejecting',
            ),
            ui.localize(
                '@sage/xtrem-sales/pages__sales_return_request__reject_action_dialog_content',
                'You are about to reject this sales return request.',
            ),
            ui.localize('@sage/xtrem-sales/pages-confirm-reject', 'Reject'),
        )
    ) {
        parameters.salesReturnRequestPage.$.loader.isHidden = false;
        const isRejected = await parameters.salesReturnRequestPage.$.graph
            .node('@sage/xtrem-sales/SalesReturnRequest')
            .mutations.reject(true, { returnRequest: parameters.recordId })
            .execute();
        parameters.salesReturnRequestPage.$.showToast(isRejected, { type: 'success' });
        await parameters.salesReturnRequestPage.$.router.refresh();
        await parameters.salesReturnRequestPage.$.refreshNavigationPanel();
        parameters.salesReturnRequestPage.$.loader.isHidden = true;
    }
}

export function getLocalizeSalesReturnRequestCloseStatusMethodReturn(
    returnStatus: SalesReturnRequestCloseStatusMethodReturn,
) {
    switch (returnStatus) {
        case 'parametersAreIncorrect':
            return {
                content: ui.localize(
                    '@sage/xtrem-sales/pages_functions__sales_return_request_close_action_not_allowed_incorrect_parameters',
                    'The close action on the line is not allowed. The parameters are incorrect.',
                ),
                option: { type: 'error' },
                severity: 'error',
            };

        case 'salesReturnRequestIsAlreadyClosed':
            return {
                content: ui.localize(
                    '@sage/xtrem-sales/pages_functions__sales_return_request_close_action_not_allowed_sales_return_request_is_already_closed',
                    'The close action on the line is not allowed. The sales return request is already closed.',
                ),
                option: { type: 'warning' },
                severity: 'warning',
            };
        case 'salesReturnRequestIsNowClosed':
            return {
                content: ui.localize(
                    '@sage/xtrem-sales/pages_functions__sales_return_request_close_action_allowed_sales_return_request_is_closed',
                    'The sales return request is closed.',
                ),
                option: { type: 'success' },
                severity: 'success',
            };
        case 'linkedSalesReturnReceiptIsNotClosed':
            return {
                content: ui.localize(
                    '@sage/xtrem-sales/pages_functions__sales_return_request_close_action_not_allowed_receipt_not_closed',
                    'The close action is not allowed. A linked return receipt is not closed',
                ),
                option: { type: 'error' },
                severity: 'error',
            };
        default:
            throw new BusinessRuleError('returnStatus not valid ');
    }
}

export async function close(parameters: CloseActionParameter) {
    if (
        await confirmDialogWithAcceptButtonText(
            parameters.salesReturnRequestPage,
            ui.localize(
                '@sage/xtrem-sales/pages__sales_return_request__header_close_action_dialog_title',
                'Confirm status change',
            ),
            ui.localize(
                '@sage/xtrem-sales/pages__sales_return_request__header_close_action_dialog_content',
                'You are about to change the status of this request to closed. You can reopen this request later.',
            ),
            ui.localize('@sage/xtrem-sales/closeRequest____title', 'Close request'),
        )
    ) {
        if (parameters.isCalledFromRecordPage) {
            parameters.salesReturnRequestPage.$.setPageClean();
        }
        parameters.salesReturnRequestPage.$.loader.isHidden = false;
        const closeStatus = (await parameters.salesReturnRequestPage.$.graph
            .node('@sage/xtrem-sales/SalesReturnRequest')
            .mutations.setSalesReturnRequestCloseStatus(true, { salesReturnRequestNumber: parameters.recordNumber })
            .execute()) as SalesReturnRequestCloseStatusMethodReturn;

        const closeMessage = getLocalizeSalesReturnRequestCloseStatusMethodReturn(closeStatus);
        parameters.salesReturnRequestPage.$.showToast(closeMessage.content, closeMessage.option as ui.ToastOptions);
        if (closeMessage.severity === 'success' && parameters.isCalledFromRecordPage) {
            await parameters.salesReturnRequestPage.$.router.refresh();
            await parameters.salesReturnRequestPage.$.refreshNavigationPanel();
        }
        parameters.salesReturnRequestPage.$.loader.isHidden = true;
    }
}

function setSalesReturnRequestOpenStatusMutation(parameters: ActionParameter) {
    return parameters.salesReturnRequestPage.$.graph
        .node('@sage/xtrem-sales/SalesReturnRequest')
        .mutations.setSalesReturnRequestOpenStatus(true, { salesReturnRequestNumber: parameters.recordNumber })
        .execute();
}

export function getLocalizeSalesReturnRequestOpenStatusMethodReturn(
    returnStatus: SalesReturnRequestOpenStatusMethodReturn,
) {
    switch (returnStatus) {
        case 'parametersAreIncorrect':
            return {
                content: ui.localize(
                    '@sage/xtrem-sales/pages_functions__sales_return_request_open_action_not_allowed_incorrect_parameters',
                    'The open action on the line is not allowed. The parameters are incorrect.',
                ),
                severity: 'error',
            };
        case 'salesReturnRequestIsReceived':
            return {
                content: ui.localize(
                    '@sage/xtrem-sales/pages_functions__sales_return_request_open_action_not_allowed_the_sales_return_request_is_fully_received',
                    'The open action is not allowed. The sales return request is fully received',
                ),
                severity: 'error',
            };
        case 'salesReturnRequestIsAlreadyOpen':
            return {
                content: ui.localize(
                    '@sage/xtrem-sales/pages_functions__sales_return_request_open_action_not_allowed_the_sales_return_request_is_already_open',
                    'The open action is not allowed. The sales return request is already open',
                ),
                severity: 'warning',
            };
        default:
            return {
                content: ui.localize(
                    '@sage/xtrem-sales/pages_functions__sales_return_request_open_action_allowed_header_open',
                    'The sales return request is opened.',
                ),
                severity: 'success',
            };
    }
}

export async function openAction(parameters: OpenActionParameters) {
    if (
        await confirmDialogWithAcceptButtonText(
            parameters.salesReturnRequestPage,
            ui.localize(
                '@sage/xtrem-sales/pages__sales_return_request__header_open_action_dialog_title',
                'Confirm status change',
            ),
            ui.localize(
                '@sage/xtrem-sales/pages__sales_return_request__header_open_action_dialog_content',
                'You are about to change the status of this request to open.',
            ),
            ui.localize('@sage/xtrem-sales/openRequest____title', 'Open request'),
        )
    ) {
        parameters.salesReturnRequestPage.$.loader.isHidden = false;
        const returnStatus = await setSalesReturnRequestOpenStatusMutation(parameters);
        const returnValue = getLocalizeSalesReturnRequestOpenStatusMethodReturn(returnStatus);
        parameters.salesReturnRequestPage.$.showToast(returnValue.content, {
            type: returnValue.severity as Required<
                Parameters<typeof parameters.salesReturnRequestPage.$.showToast>
            >[1]['type'],
        });
        if (returnValue.severity === 'success' && parameters.isCalledFromRecordPage) {
            await parameters.salesReturnRequestPage.$.router.refresh();
            await parameters.salesReturnRequestPage.$.refreshNavigationPanel();
        }
        parameters.salesReturnRequestPage.$.loader.isHidden = true;
    }
}

function createCreditMemoMutation(parameters: SalesReturnRequestPageRecordIdParameter) {
    return parameters.salesReturnRequestPage.$.graph
        .node('@sage/xtrem-sales/SalesReturnRequest')
        .mutations.createSalesCreditMemosFromReturnRequests(
            {
                status: true,
            },
            { salesDocuments: [`_id:${parameters.recordId}`] },
        )
        .execute();
}

async function getLastCreditMemoCreatedFromSalesReturnRequest(parameters: SalesReturnRequestPageRecordIdParameter) {
    const creditMemoCreated = extractEdges(
        await parameters.salesReturnRequestPage.$.graph
            .node('@sage/xtrem-sales/SalesReturnRequestLineSalesCreditMemoLine')
            .query(
                ui.queryUtils.edgesSelector(
                    {
                        document: {
                            document: {
                                _id: true,
                            },
                        },
                    },
                    {
                        filter: { linkedDocument: { document: { _in: [parameters.recordId] } } },
                    },
                ),
            )
            .execute(),
    );
    return creditMemoCreated[0]?.document.document._id;
}

export async function createCreditMemoAction(parameters: CreateCreditMemoParameters) {
    if (
        await confirmDialogWithAcceptButtonText(
            parameters.salesReturnRequestPage,
            ui.localize(
                '@sage/xtrem-sales/pages__sales_return_request__create_credit_memo_dialog_title',
                'Confirm credit memo creation',
            ),
            ui.localize(
                '@sage/xtrem-sales/pages__sales_return_request__create_credit_memo_dialog_content',
                'You are about to create a credit memo from this sales return request.',
            ),
            ui.localize('@sage/xtrem-sales/pages-confirm-create', 'Create'),
        )
    ) {
        parameters.salesReturnRequestPage.$.loader.isHidden = false;
        const { status } = await createCreditMemoMutation(parameters);

        if (status !== 'parametersAreIncorrect') {
            parameters.salesReturnRequestPage.$.showToast(
                ui.localize(
                    '@sage/xtrem-sales/pages__sales_return_request__credit_memo_created',
                    'The sales credit memo was created.',
                ),
                { type: 'success' },
            );

            parameters.salesReturnRequestPage.$.setPageClean();
            if (parameters.isCalledFromRecordPage) {
                parameters.salesReturnRequestPage.$.router.goTo(`@sage/xtrem-sales/SalesCreditMemo`, {
                    _id: await getLastCreditMemoCreatedFromSalesReturnRequest(parameters),
                });
            }
        }
        if (!parameters.isCalledFromRecordPage) {
            await parameters.salesReturnRequestPage.$.router.refresh(true);
            await parameters.salesReturnRequestPage.$.refreshNavigationPanel();
        }
        parameters.salesReturnRequestPage.$.loader.isHidden = true;
    }
}
