import type * as isDisabledButtonsCommon from '@sage/xtrem-system/build/lib/client-functions/interfaces/is-disabled-buttons';
import type * as isHiddenButtonsCommon from '@sage/xtrem-system/build/lib/client-functions/interfaces/is-hidden-buttons';
import type * as isDisabledButtons from './interfaces/is-disabled-buttons-sales-order';
import type * as isHiddenButtons from './interfaces/is-hidden-buttons-sales-order';

export function isDisabledButtonSaveAction(
    data: isDisabledButtonsCommon.SaveOrCancelParameters<isDisabledButtons.StatusParameters>,
) {
    if (data.recordId) {
        if (
            data.parameters.status === 'quote' ||
            data.parameters.status === 'pending' ||
            data.parameters.status === 'inProgress'
        ) {
            return !data.isDirty;
        }
        return true;
    }
    return !data.isDirty;
}

export function isDisabledButtonCancelAction(
    data: isDisabledButtonsCommon.SaveOrCancelParameters<isDisabledButtons.StatusParameters>,
) {
    if (data.recordId) {
        if (
            data.parameters.status === 'quote' ||
            data.parameters.status === 'pending' ||
            data.parameters.status === 'inProgress'
        ) {
            return !data.isDirty;
        }
        return true;
    }
    return !data.isDirty;
}

export function isDisabledButtonDeleteAction(
    data: isDisabledButtonsCommon.DeleteParameters<isDisabledButtons.OpenDeleteParameters>,
) {
    if (
        data.recordId &&
        (data.parameters.status === 'quote' ||
            data.parameters.status === 'pending' ||
            (data.parameters.status === 'closed' && data.parameters.shippingStatus === 'notShipped'))
    ) {
        return false;
    }
    return true;
}

/* -------------------*/

export function isHiddenButtonDeleteAction(
    data: isHiddenButtonsCommon.DeleteParameters<isHiddenButtons.OpenDeleteParameters>,
) {
    if (
        data.recordId &&
        (data.parameters.displayStatus === 'confirmed' ||
            data.parameters.taxCalculationStatus === 'failed' ||
            data.parameters.status === 'quote')
    ) {
        return false;
    }
    return true;
}

export function isHiddenButtonConfirmAction(
    data: isHiddenButtonsCommon.BusinessButtonParameters<isHiddenButtons.StatusParameters>,
) {
    if (data.recordId && data.parameters.status === 'quote') {
        return data.isDirty;
    }
    return true;
}

export function isDisabledButtonConfirmAction(
    data: isDisabledButtonsCommon.BusinessButtonParameters<isDisabledButtons.StatusParameters>,
) {
    return isHiddenButtonConfirmAction(data);
}

/* -------------------*/

export function isHiddenButtonShipAction(
    data: isHiddenButtonsCommon.BusinessButtonParameters<isHiddenButtons.ShipParameters>,
) {
    if (
        data.recordId &&
        data.parameters.status !== 'quote' &&
        data.parameters.status !== 'closed' &&
        data.parameters.allocationRequestStatus !== 'inProgress' &&
        data.parameters.taxCalculationStatus !== 'failed'
    ) {
        return data.isDirty;
    }
    return true;
}

export function isDisabledButtonShipAction(
    data: isDisabledButtonsCommon.BusinessButtonParameters<isDisabledButtons.StatusParameters>,
) {
    return isHiddenButtonShipAction(data);
}

/* -------------------*/

export function isHiddenButtonOpenAction(
    data: isHiddenButtonsCommon.BusinessButtonParameters<isHiddenButtons.ShipmentAndStatusParameters>,
) {
    if (data.recordId && data.parameters.status === 'closed' && data.parameters.shippingStatus !== 'shipped') {
        return data.isDirty;
    }
    return true;
}

export function isDisabledButtonOpenAction(
    data: isDisabledButtonsCommon.BusinessButtonParameters<isDisabledButtons.OpenDeleteParameters>,
) {
    return isHiddenButtonOpenAction(data);
}

/* -------------------*/

export function isDisabledLinePhantomRow(
    data: isDisabledButtonsCommon.PhantomRowButtonParameters<isDisabledButtons.LinePhantomRowParameters>,
) {
    if (
        data.parameters &&
        data.parameters.status !== 'closed' &&
        data.parameters.site &&
        data.parameters.stockSite &&
        data.parameters.soldToCustomer &&
        data.parameters.date &&
        data.parameters.currency
    ) {
        return false;
    }
    return true;
}

/* -------------------*/

export function isHiddenButtonDefaultDimensionAction(
    data: isHiddenButtonsCommon.HeaderDropDownActionButtonParameters<isHiddenButtons.ShipmentAndStatusParameters>,
) {
    if (
        data.recordId &&
        data.parameters &&
        data.parameters.status &&
        (['inProgress', 'error', 'closed'].includes(data.parameters.status) ||
            (data.parameters.shippingStatus &&
                ['partiallyShipped', 'shipped'].includes(data.parameters.shippingStatus)))
    ) {
        return true;
    }
    return false;
}

export function isDisabledButtonDefaultDimensionAction(
    data: isDisabledButtonsCommon.HeaderDropDownActionButtonParameters<isDisabledButtons.DefaultDimensionParameters>,
) {
    if (
        data.parameters &&
        data.parameters.site &&
        data.parameters.stockSite &&
        data.parameters.soldToCustomer &&
        data.parameters.date &&
        data.parameters.currency
    ) {
        return false;
    }
    return true;
}

export function isHiddenButtonRequestAllocationAction(
    data: isHiddenButtonsCommon.HeaderDropDownActionButtonParameters<isHiddenButtons.RequestAllocationParameters>,
) {
    return (
        !data.parameters ||
        !data.recordId ||
        data.parameters.allocationRequestStatus === 'inProgress' ||
        !['pending', 'inProgress'].includes(data.parameters.status || '') ||
        !['notAllocated', 'partiallyAllocated'].includes(data.parameters.allocationStatus || '')
    );
}

export function isDisabledButtonRequestAllocationAction(
    data: isDisabledButtonsCommon.HeaderDropDownActionButtonParameters<isDisabledButtons.RequestAllocationParameters>,
) {
    return data.isDirty;
}

export function isHiddenButtonRequestDeallocationAction(
    data: isHiddenButtonsCommon.HeaderDropDownActionButtonParameters<isHiddenButtons.RequestAllocationParameters>,
) {
    return (
        !data.parameters ||
        !data.recordId ||
        data.parameters.allocationRequestStatus === 'inProgress' ||
        !['pending', 'inProgress'].includes(data.parameters.status || '') ||
        !['allocated', 'partiallyAllocated'].includes(data.parameters.allocationStatus || '')
    );
}

export function isDisabledButtonRequestDeallocationAction(
    data: isDisabledButtonsCommon.HeaderDropDownActionButtonParameters<isDisabledButtons.RequestAllocationParameters>,
) {
    return data.isDirty;
}

/* -------------------*/

export function isHiddenButtonPrintAction(
    data: isHiddenButtonsCommon.HeaderQuickActionButtonParameters<isHiddenButtons.StatusParameters>,
) {
    return !data.recordId;
}

export function isDisabledButtonPrintAction(
    data: isDisabledButtonsCommon.HeaderQuickActionButtonParameters<isDisabledButtons.StatusParameters>,
) {
    return !data.recordId || data.isDirty;
}

/* -------------------*/

export function isHiddenButtonSendMailAction(
    data: isHiddenButtonsCommon.HeaderQuickActionButtonParameters<isHiddenButtons.SendMailParameters>,
) {
    return (
        !data.recordId ||
        data.parameters.shippingStatus === 'shipped' ||
        data.parameters.shippingStatus === 'partiallyShipped' ||
        data.parameters.taxCalculationStatus === 'failed' ||
        data.parameters.status === 'closed' ||
        (!(data.parameters.status === 'pending' || data.parameters.status === 'quote') &&
            data.parameters.taxCalculationStatus !== 'done' &&
            data.parameters.taxEngine !== 'avalaraAvaTax')
    );
}

export function isDisabledButtonSendMailAction(
    data: isDisabledButtonsCommon.HeaderQuickActionButtonParameters<isDisabledButtons.StatusParameters>,
) {
    return !data.recordId || data.isDirty;
}

/* -------------------*/
