import type { Filter } from '@sage/xtrem-client';
import { date } from '@sage/xtrem-date-time';
import type { SalesOrder } from '@sage/xtrem-sales-api';
import * as ui from '@sage/xtrem-ui';
import type { CustomerIndicatorParameters } from './interfaces/widget-interfaces';

export function getFilteredSalesOrders(type: string): Filter<SalesOrder> {
    switch (type) {
        case 'open':
            return { status: { _nin: ['closed'] } };
        case 'onTime':
            return {
                shippingDate: { _gte: `${date.today()}` },
                status: { _in: ['pending', 'inProgress'] },
            };
        case 'late':
            return {
                shippingDate: { _lt: `${date.today()}` },
                status: { _in: ['pending', 'inProgress'] },
            };
        default:
            break;
    }
    return {};
}

export function getContext(parameters: CustomerIndicatorParameters) {
    return [
        {
            title: ui.localize('@sage/xtrem-sales/widgets__customer_kpi_last_order_title', 'LAST ORDER'),
            value: parameters.lastOrderDate,
            valueColor: 'black',
            contentAlignment: 'left',
        },
        {
            title: ui.localize('@sage/xtrem-sales/widgets__customer_kpi_order_book_title', 'ORDER BOOK'),
            value: parameters.orderBookTotal,
            valueColor: 'black',
            hasSeparatorAfter: true,
            contentAlignment: 'left',
        },
        {
            title: ui.localize('@sage/xtrem-sales/widgets__customer_kpi_credit_limit_title', 'CREDIT LIMIT'),
            value: parameters.creditLimit,
            valueColor: 'black',
            hasSeparatorAfter: true,
            contentAlignment: 'left',
        },
        {
            title: ui.localize('@sage/xtrem-sales/widgets__customer_kpi_total_invoiced_title', 'TOTAL INVOICED'),
            value: parameters.invoicedTotal,
            valueColor: 'black',
            contentAlignment: 'left',
        },
        {
            title: ui.localize('@sage/xtrem-sales/widgets__customer_kpi_gross_profit_title', 'GROSS PROFIT'),
            value: parameters.grossProfit,
            valueColor: 'black',
            contentAlignment: 'left',
        },
    ];
}
