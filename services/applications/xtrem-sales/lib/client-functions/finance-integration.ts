import type { ExtractEdgesPartial } from '@sage/xtrem-client';
import { DateValue } from '@sage/xtrem-date-time';
import * as attributesAndDimensions from '@sage/xtrem-finance-data/build/lib/client-functions/attributes-and-dimensions';
import * as dimensionPanelHelpers from '@sage/xtrem-finance-data/build/lib/client-functions/dimension-panel-helpers';
import type * as dimensionPanelInterfaces from '@sage/xtrem-finance-data/build/lib/client-functions/interfaces/index';
import type { BusinessRelationType, Customer, GraphApi } from '@sage/xtrem-master-data-api';
import type { Site, GraphApi as SystemApi } from '@sage/xtrem-system-api';
import type * as ui from '@sage/xtrem-ui';
import type { SalesInvoice as SalesInvoicePage } from '../pages/sales-invoice';

/**
 * Get dimensions and attributes to set them in sales lines from main grid
 * @param line
 * @param salesPage
 */
export async function getDimensionsForSalesLines(options: {
    line: ui.PartialNodeWithId<Node>;
    salesPage: ui.Page<GraphApi>;
    defaultDimensionsAttributes: dimensionPanelInterfaces.DefaultDimensions;
    defaultedFromItem?: string;
}) {
    const salesInvoiceBase = dimensionPanelHelpers.fromPartialToBaseDocumentLineWithAnalytical(options.line);
    const rowWithDimensions = dimensionPanelHelpers.editDisplayDimensions(
        options.salesPage,
        {
            documentLine: salesInvoiceBase,
        },
        {
            editable: true,
            calledFromMainGrid: true,
        },
        options.defaultDimensionsAttributes,
        options.defaultedFromItem,
    );
    if ((await rowWithDimensions).storedAttributes !== '' || (await rowWithDimensions).storedDimensions !== '') {
        return {
            resultDimensionsToSet: (await rowWithDimensions).storedDimensions,
            resultAttributesToSet: (await rowWithDimensions).storedAttributes,
            resultDataDetermined: true,
        };
    }
    return {
        resultDimensionsToSet: '',
        resultAttributesToSet: '',
        resultDataDetermined: false,
    };
}

/**
 * Get site and customer as partial nodes
 * @param page
 * @param siteId _id of the site to read from DB
 * @param customerId _id of the customer to read from DB
 * @returns Promise<{ site: ExtractEdgesPartial<Site>; customer: ExtractEdgesPartial<Customer> }>
 */
export async function getSiteAndCustomer(options: {
    page: ui.Page<SystemApi>;
    siteId: string;
    customerId: string;
}): Promise<{ site: ExtractEdgesPartial<Site>; customer: ExtractEdgesPartial<Customer> | null }> {
    const site = await options.page.$.graph
        .node('@sage/xtrem-system/Site')
        .read(
            {
                _id: true,
                id: true,
                legalCompany: {
                    _id: true,
                },
                storedAttributes: true,
                storedDimensions: true,
            },
            options.siteId,
        )
        .execute();
    const customer = options.customerId
        ? await options.page.$.graph
              .node('@sage/xtrem-master-data/Customer')
              .read(
                  {
                      _id: true,
                      storedAttributes: true,
                      storedDimensions: true,
                  },
                  options.customerId,
              )
              .execute()
        : null;
    return { site, customer };
}

/**
 * Get a string with all attributes defaulted from item and default dimensions attributes to show on 'Set dimensions'
 * dialog called from main list
 * @param page
 * @param site
 * @param customer
 * @returns Promise<{ defaultedFromItem: string; defaultDimensionsAttributes: dimensionPanelInterfaces.DefaultDimensions }>
 */
export async function getValuesForSetDimensionsFromMainList(options: {
    page: ui.Page<SystemApi>;
    site: ExtractEdgesPartial<Site> | null;
    customer: ExtractEdgesPartial<Customer> | null;
}): Promise<{ defaultedFromItem: string; defaultDimensionsAttributes: dimensionPanelInterfaces.DefaultDimensions }> {
    const defaultedFromItem = await attributesAndDimensions.getAttributesAndDimensionsFromItem({
        page: options.page,
        dimensionDefinitionLevel: 'salesDirect',
        companyId: Number(options.site?.legalCompany?._id),
    });
    const defaultDimensionsAttributes = await attributesAndDimensions.initDefaultDimensions({
        page: options.page,
        dimensionDefinitionLevel: 'salesDirect',
        site: options.site || null,
        customer: options.customer,
    });

    return { defaultedFromItem, defaultDimensionsAttributes };
}

export function openRecordPage(page: SalesInvoicePage): Promise<string> {
    return page.$.dialog.page(
        '@sage/xtrem-finance/RecordReceipt',
        {
            openParams: JSON.stringify({
                bankAccount: page.site.value?.legalCompany?.bankAccount,
                financialSite: page.site.value?.isFinance ? page.site.value : page.site.value?.financialSite,
                baseBusinessRelation: page.billToCustomer.value,
                type: 'customer' as BusinessRelationType,
                date: DateValue.today().toString(),
                currency: page.currency.value,
                amount: page.netBalance.value ?? 0,
                number: page.number.value,
            }),
        },
        { size: 'extra-large', resolveOnCancel: true },
    );
}

export function openRecordPageFromMainList(parameters: {
    page: SalesInvoicePage;
    openParams: string;
}): Promise<string> {
    return parameters.page.$.dialog.page(
        '@sage/xtrem-finance/RecordReceipt',
        {
            openParams: parameters.openParams,
        },
        { size: 'extra-large', resolveOnCancel: true },
    );
}
