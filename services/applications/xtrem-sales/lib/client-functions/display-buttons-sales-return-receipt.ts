import type * as isDisabledButtonsCommon from '@sage/xtrem-system/build/lib/client-functions/interfaces/is-disabled-buttons';
import type * as isHiddenButtonsCommon from '@sage/xtrem-system/build/lib/client-functions/interfaces/is-hidden-buttons';
import type * as isDisabledButtons from './interfaces/is-disabled-buttons-sales-return-receipt';
import type * as isHiddenButtons from './interfaces/is-hidden-buttons-sales-return-receipt';

export function isDisabledButtonSaveAction(
    data: isDisabledButtonsCommon.SaveOrCancelParameters<isDisabledButtons.SaveParameters>,
) {
    if (data.recordId) {
        if (data.parameters.status === 'draft' || data.parameters.status === 'inProgress') {
            return !data.isDirty;
        }
        return true;
    }
    return !data.isDirty;
}

export function isDisabledButtonCancelAction(
    data: isDisabledButtonsCommon.SaveOrCancelParameters<isDisabledButtons.SaveParameters>,
) {
    if (data.recordId) {
        if (data.parameters.status === 'draft' || data.parameters.status === 'inProgress') {
            return !data.isDirty;
        }
        return true;
    }
    return !data.isDirty;
}

export function isHiddenButtonDeleteAction(
    data: isHiddenButtonsCommon.DeleteParameters<isHiddenButtons.DeleteParameters>,
) {
    if (data.recordId && data.parameters.status !== 'closed' && data.parameters.stockTransactionStatus === 'draft') {
        return false;
    }
    return true;
}

/* -------------------*/

export function isHiddenButtonPostAction(
    data: isHiddenButtonsCommon.BusinessButtonParameters<isHiddenButtons.PostParameters>,
) {
    if (data.recordId && data.parameters.status !== 'closed') {
        return data.isDirty;
    }
    return true;
}

export function isDisabledButtonPostAction(
    data: isDisabledButtonsCommon.BusinessButtonParameters<isDisabledButtons.PostParameters>,
) {
    return isHiddenButtonPostAction(data);
}

/* -------------------*/

export function isHiddenButtonRepostAction(
    data: isDisabledButtonsCommon.BusinessButtonParameters<isDisabledButtons.RepostParameters>,
) {
    if (data.recordId && data.parameters.fromNotificationHistory) {
        return !data.isDirty;
    }
    return true;
}

export function isDisabledButtonRepostAction(
    data: isHiddenButtonsCommon.BusinessButtonParameters<isHiddenButtons.RepostParameters>,
) {
    return isHiddenButtonRepostAction(data);
}

/* -------------------*/

export function isHiddenButtonDefaultDimensionAction(
    data: isHiddenButtonsCommon.HeaderDropDownActionButtonParameters<isHiddenButtons.DefaultDimensionParameters>,
) {
    if (
        data.recordId &&
        data.parameters &&
        data.parameters.status &&
        ['postingInProgress', 'error', 'closed'].includes(data.parameters.status)
    ) {
        return true;
    }
    return false;
}

export function isDisabledButtonDefaultDimensionAction(
    data: isDisabledButtonsCommon.HeaderDropDownActionButtonParameters<isDisabledButtons.DefaultDimensionParameters>,
) {
    if (data.parameters && data.parameters.site && data.parameters.shipToCustomer && data.parameters.date) {
        return false;
    }
    return true;
}

/* -------------------*/

export function isHiddenButtonSelectFromSalesReturnRequestLinesAction(
    data: isHiddenButtonsCommon.HeaderDropDownActionButtonParameters<isHiddenButtons.SelectFromSalesReturnRequestLinesParameters>,
) {
    if (
        data.parameters &&
        data.parameters.site &&
        data.parameters.shipToCustomer &&
        data.parameters.status &&
        data.parameters.status === 'draft'
    ) {
        return false;
    }
    return true;
}

export function isDisabledButtonSelectFromSalesReturnRequestLinesAction(
    data: isDisabledButtonsCommon.HeaderDropDownActionButtonParameters<isDisabledButtons.SelectFromSalesReturnRequestLinesParameters>,
) {
    return isHiddenButtonSelectFromSalesReturnRequestLinesAction(data);
}
