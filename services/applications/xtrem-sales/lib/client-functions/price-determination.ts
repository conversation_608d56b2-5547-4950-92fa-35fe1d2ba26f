import type { Item, UnitOfMeasure } from '@sage/xtrem-master-data-api';
import type { SalesInvoiceLineBinding, SalesOrderLineBinding } from '@sage/xtrem-sales-api';
import * as ui from '@sage/xtrem-ui';
import type { ItemData } from './interfaces/interfaces';

export async function convertGrossPriceToUnit(
    pageInstance: ui.Page & {
        oldSalesUnit?: string;
        unit: ui.fields.Reference<UnitOfMeasure>;
        stockUnit: ui.fields.Reference<UnitOfMeasure>;
        itemData: ItemData;
        additionalItemId: string;
        item: ui.fields.Reference<Item>;
        grossPrice: ui.fields.Numeric;
        calculatePrices(): Promise<void>;
        setPriceOriginAndReason(): void;
    },
) {
    if (pageInstance.oldSalesUnit) {
        if (pageInstance.oldSalesUnit === pageInstance.stockUnit.value?._id) {
            if (pageInstance.unit.value?._id === pageInstance.item.value?.salesUnit?._id) {
                pageInstance.grossPrice.value = Number(
                    Number(pageInstance.grossPrice.value) * Number(pageInstance.itemData.item.conversionFactor),
                );
            } else if (pageInstance.unit.value?._id === pageInstance.additionalItemId) {
                pageInstance.grossPrice.value = Number(
                    Number(pageInstance.grossPrice.value) * Number(pageInstance.itemData.itemCustomer.conversionFactor),
                );
            }
        } else if (pageInstance.oldSalesUnit === pageInstance.item.value?.salesUnit?._id) {
            pageInstance.grossPrice.value = Number(
                Number(pageInstance.grossPrice.value) / Number(pageInstance.itemData.item.conversionFactor),
            );
            if (pageInstance.unit.value?._id === pageInstance.additionalItemId) {
                pageInstance.grossPrice.value = Number(
                    Number(pageInstance.grossPrice.value) * Number(pageInstance.itemData.itemCustomer.conversionFactor),
                );
            }
        } else if (pageInstance.oldSalesUnit === pageInstance.additionalItemId) {
            pageInstance.grossPrice.value = Number(
                Number(pageInstance.grossPrice.value) / Number(pageInstance.itemData.itemCustomer.conversionFactor),
            );
            if (pageInstance.unit.value?._id === pageInstance.item.value?.salesUnit?._id) {
                pageInstance.grossPrice.value = Number(
                    Number(pageInstance.grossPrice.value) * Number(pageInstance.itemData.item.conversionFactor),
                );
            }
        }
        await pageInstance.calculatePrices();
        pageInstance.setPriceOriginAndReason();
    }

    pageInstance.oldSalesUnit = pageInstance.unit.value?._id;
}

export async function convertGrossPriceToUnit2(
    pageInstance: ui.Page & {
        oldSalesUnit?: string;
        calculatePrices(
            rowData:
                | ui.PartialCollectionValue<SalesOrderLineBinding>
                | ui.PartialCollectionValue<SalesInvoiceLineBinding>,
        ): Promise<void>;
        setPriceOriginAndReason(
            rowData:
                | ui.PartialCollectionValue<SalesOrderLineBinding>
                | ui.PartialCollectionValue<SalesInvoiceLineBinding>,
        ): void;
    },
    itemData: ItemData,
    additionalItemId: string,
    rowData: ui.PartialCollectionValue<SalesOrderLineBinding> | ui.PartialCollectionValue<SalesInvoiceLineBinding>,
) {
    if (rowData && rowData.stockUnit && rowData.unit && rowData.item && rowData.item.salesUnit) {
        if (pageInstance.oldSalesUnit) {
            if (pageInstance.oldSalesUnit === rowData.stockUnit._id) {
                if (rowData.unit._id === rowData.item.salesUnit._id) {
                    rowData.grossPrice = String(Number(rowData.grossPrice) * Number(itemData.item.conversionFactor));
                } else if (rowData.unit._id === additionalItemId) {
                    rowData.grossPrice = String(
                        Number(rowData.grossPrice) * Number(itemData.itemCustomer.conversionFactor),
                    );
                }
            } else if (pageInstance.oldSalesUnit === rowData.item.salesUnit._id) {
                rowData.grossPrice = String(Number(rowData.grossPrice) / Number(itemData.item.conversionFactor));
                if (rowData.unit._id === additionalItemId) {
                    rowData.grossPrice = String(
                        Number(rowData.grossPrice) * Number(itemData.itemCustomer.conversionFactor),
                    );
                }
            } else if (pageInstance.oldSalesUnit === additionalItemId) {
                rowData.grossPrice = String(
                    Number(rowData.grossPrice) / Number(itemData.itemCustomer.conversionFactor),
                );
                if (rowData.unit._id === rowData.item.salesUnit._id) {
                    rowData.grossPrice = String(Number(rowData.grossPrice) * Number(itemData.item.conversionFactor));
                }
            }
            await pageInstance.calculatePrices(rowData);
            pageInstance.setPriceOriginAndReason(rowData);
        }

        pageInstance.oldSalesUnit = String(rowData.unit._id);
    }
}

export async function massPriceDeterminationCalculation(
    pageInstance: ui.Page & {
        getPriceDetermination(rowItem: ui.PartialCollectionValue<any>): Promise<void>;
        lines: ui.fields.Table;
        status: ui.fields.Label;
        initLinesLength: number;
    },
    dateChangedMessage: string,
    moreThanNineLinesMessage: string,
) {
    if (pageInstance.lines.value.length > 0 && pageInstance.status.value !== 'closed') {
        const response = await pageInstance.$.dialog
            .confirmation(
                'info',
                ui.localize('@sage/xtrem-sales/price_determination__on_price_determination_title', 'Search price'),
                dateChangedMessage,
                {
                    cancelButton: { isHidden: false },
                    acceptButton: { isHidden: false },
                },
            )
            .then(() => true)
            .catch(() => false);

        if (response && (!pageInstance.$.values._id || pageInstance.initLinesLength < 10)) {
            pageInstance.$.loader.isHidden = false;
            await Promise.all(
                pageInstance.lines.value.map(async line => {
                    if (line.status !== 'closed') {
                        await pageInstance.getPriceDetermination(line);
                    }
                }),
            );
            pageInstance.$.loader.isHidden = true;
        } else if (response && pageInstance.$.values._id) {
            const responseSave = await pageInstance.$.dialog
                .confirmation(
                    'info',
                    ui.localize('@sage/xtrem-sales/price_determination__on_price_determination_title', 'Search price'),
                    moreThanNineLinesMessage,
                    {
                        cancelButton: { isHidden: false },
                        acceptButton: { isHidden: false },
                    },
                )
                .then(() => true)
                .catch(() => false);
            if (responseSave) {
                await pageInstance.$standardSaveAction.execute();
                pageInstance.$.loader.isHidden = false;
                await pageInstance.$.graph
                    .node(pageInstance.$.page.node)
                    .mutations.massPriceDeterminationCalculation(true, {
                        salesDocument: pageInstance.$.values._id,
                    })
                    .execute();
                await pageInstance.$.router.refresh();
                pageInstance.$.loader.isHidden = true;
            }
        }
    }
}
