import type * as isDisabledButtonsCommon from '@sage/xtrem-system/build/lib/client-functions/interfaces/is-disabled-buttons';
import type * as isHiddenButtonsCommon from '@sage/xtrem-system/build/lib/client-functions/interfaces/is-hidden-buttons';
import type * as isDisabledButtons from './interfaces/is-disabled-buttons-sales-return-request';
import type * as isHiddenButtons from './interfaces/is-hidden-buttons-sales-return-request';

export function isDisabledButtonSaveAction(
    data: isDisabledButtonsCommon.SaveOrCancelParameters<isDisabledButtons.SaveParameters>,
) {
    if (data.recordId) {
        if (
            data.parameters.status === 'draft' ||
            data.parameters.status === 'pending' ||
            data.parameters.status === 'inProgress'
        ) {
            return !data.isDirty;
        }
        return true;
    }
    return !data.isDirty;
}

export function isDisabledButtonCancelAction(
    data: isDisabledButtonsCommon.SaveOrCancelParameters<isDisabledButtons.SaveParameters>,
) {
    if (data.recordId) {
        if (
            data.parameters.status === 'draft' ||
            data.parameters.status === 'pending' ||
            data.parameters.status === 'inProgress'
        ) {
            return !data.isDirty;
        }
        return true;
    }
    return !data.isDirty;
}

export function isHiddenButtonDeleteAction(
    data: isHiddenButtonsCommon.DeleteParameters<isHiddenButtons.DeleteParameters>,
) {
    if (data.recordId && (data.parameters.status === 'draft' || data.parameters.status === 'pendingApproval')) {
        return false;
    }
    return true;
}

/* -------------------*/

export function isHiddenButtonCloseAction(
    data: isHiddenButtonsCommon.BusinessButtonParameters<isHiddenButtons.CloseParameters>,
) {
    if (data.recordId && data.parameters.status !== 'closed') {
        return data.isDirty;
    }
    return true;
}

export function isDisabledButtonCloseAction(
    data: isDisabledButtonsCommon.BusinessButtonParameters<isDisabledButtons.CloseParameters>,
) {
    return isHiddenButtonCloseAction(data);
}

/* -------------------*/

export function isHiddenButtonOpenAction(
    data: isHiddenButtonsCommon.BusinessButtonParameters<isHiddenButtons.OpenParameters>,
) {
    if (
        data.recordId &&
        data.parameters.status === 'closed' &&
        data.parameters.receiptStatus !== 'received' &&
        data.parameters.creditStatus !== 'credited' &&
        data.parameters.approvalStatus !== 'rejected'
    ) {
        return data.isDirty;
    }
    return true;
}

export function isDisabledButtonOpenAction(
    data: isDisabledButtonsCommon.BusinessButtonParameters<isDisabledButtons.OpenParameters>,
) {
    return isHiddenButtonOpenAction(data);
}

/* -------------------*/

export function isHiddenButtonApproveAction(
    data: isHiddenButtonsCommon.BusinessButtonParameters<isHiddenButtons.ApproveParameters>,
) {
    if (data.recordId && data.parameters.status !== 'closed' && data.parameters.approvalStatus === 'pendingApproval') {
        return data.isDirty;
    }
    return true;
}

export function isDisabledButtonApproveAction(
    data: isDisabledButtonsCommon.BusinessButtonParameters<isDisabledButtons.ApproveParameters>,
) {
    return isHiddenButtonApproveAction(data);
}

/* -------------------*/

export function isHiddenButtonRejectAction(
    data: isHiddenButtonsCommon.BusinessButtonParameters<isHiddenButtons.RejectParameters>,
) {
    if (data.recordId && data.parameters.status !== 'closed' && data.parameters.approvalStatus === 'pendingApproval') {
        return data.isDirty;
    }
    return true;
}

export function isDisabledButtonRejectAction(
    data: isDisabledButtonsCommon.BusinessButtonParameters<isDisabledButtons.RejectParameters>,
) {
    return isHiddenButtonRejectAction(data);
}

/* -------------------*/

export function isHiddenButtonRequestApprovalAction(
    data: isHiddenButtonsCommon.BusinessButtonParameters<isHiddenButtons.RequestApprovalParameters>,
) {
    if (
        data.recordId &&
        data.parameters.status !== 'closed' &&
        data.parameters.isApprovalManaged &&
        !['pendingApproval', 'approved', 'rejected', 'confirmed'].includes(data.parameters.approvalStatus || '')
    ) {
        return data.isDirty;
    }
    return true;
}

export function isDisabledButtonRequestApprovalAction(
    data: isDisabledButtonsCommon.BusinessButtonParameters<isDisabledButtons.RequestApprovalParameters>,
) {
    return isHiddenButtonRequestApprovalAction(data);
}

/* -------------------*/

export function isHiddenButtonCreateSalesReturnRequestFromShipmentsAction(
    data: isHiddenButtonsCommon.BusinessButtonParameters<isHiddenButtons.CreateSalesReturnRequestFromShipmentsParameters>,
) {
    if (data.recordId && data.parameters.isCreditingAllowed) {
        return data.isDirty;
    }
    return true;
}

export function isDisabledButtonCreateSalesReturnRequestFromShipmentsAction(
    data: isDisabledButtonsCommon.BusinessButtonParameters<isDisabledButtons.CreateSalesReturnRequestFromShipmentsParameters>,
) {
    return isHiddenButtonCreateSalesReturnRequestFromShipmentsAction(data);
}

/* -------------------*/

export function isDisabledLinePhantomRow(
    data: isDisabledButtonsCommon.PhantomRowButtonParameters<isDisabledButtons.LinePhantomRowParameters>,
) {
    if (
        data.parameters &&
        data.parameters.status !== 'closed' &&
        data.parameters.site &&
        data.parameters.stockSite &&
        data.parameters.soldToCustomer
    ) {
        return false;
    }
    return true;
}

export function isDisabledButtonAddSalesReturnRequestLineAction(
    data: isDisabledButtonsCommon.TableFieldActionsActionButtonParameters<isDisabledButtons.AddSalesReturnRequestLineParameters>,
) {
    if (
        data.parameters &&
        data.parameters.status !== 'closed' &&
        data.parameters.site &&
        data.parameters.stockSite &&
        data.parameters.soldToCustomer
    ) {
        return false;
    }
    return true;
}

/* -------------------*/

export function isHiddenButtonDefaultDimensionAction(
    data: isHiddenButtonsCommon.HeaderDropDownActionButtonParameters<isHiddenButtons.DefaultDimensionParameters>,
) {
    if (
        data.recordId &&
        data.parameters &&
        data.parameters.status &&
        ['inProgress', 'closed'].includes(data.parameters.status) &&
        data.parameters.receiptStatus !== 'partiallyReceived'
    ) {
        return true;
    }
    return false;
}

export function isDisabledButtonDefaultDimensionAction(
    data: isDisabledButtonsCommon.HeaderDropDownActionButtonParameters<isDisabledButtons.DefaultDimensionParameters>,
) {
    if (
        data.parameters &&
        data.parameters.site &&
        data.parameters.stockSite &&
        data.parameters.soldToCustomer &&
        data.parameters.date &&
        data.parameters.requester
    ) {
        return false;
    }
    return true;
}

/* -------------------*/

export function isHiddenButtonConfirmAction(
    data: isHiddenButtonsCommon.BusinessButtonParameters<isHiddenButtons.ConfirmParameters>,
) {
    if (
        data.recordId &&
        data.parameters.status !== 'closed' &&
        !data.parameters.isApprovalManaged &&
        !['pendingApproval', 'approved', 'rejected', 'changeRequested', 'confirmed'].includes(
            data.parameters.approvalStatus || '',
        )
    ) {
        return data.isDirty;
    }
    return true;
}

export function isDisabledButtonConfirmAction(
    data: isDisabledButtonsCommon.BusinessButtonParameters<isDisabledButtons.ConfirmParameters>,
) {
    return isHiddenButtonConfirmAction(data);
}
