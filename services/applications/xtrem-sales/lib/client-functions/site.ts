import type { Site } from '@sage/xtrem-master-data/build/lib/pages/site';
import * as ui from '@sage/xtrem-ui';
import type { ExtensionMembers } from '@sage/xtrem-ui/build/lib/service/page-extension';
import type { SiteExtension } from '../page-extensions/site-extension';
import { salesReturnRequestsPendingApproval } from './return-request';

export async function onChangeIsSalesReturnRequestApprovalManaged(
    sitePage: ExtensionMembers<Site & SiteExtension>,
    wasDirtyBeforeChanging: boolean,
) {
    if (sitePage.isSalesReturnRequestApprovalManaged.value) {
        sitePage.salesReturnRequestDefaultApprover.isReadOnly = false;
        sitePage.salesReturnRequestSubstituteApprover.isReadOnly = false;
        return;
    }

    const _anySalesReturnRequestsPendingApproval =
        sitePage.$.recordId && (await salesReturnRequestsPendingApproval(sitePage.$.graph, sitePage.$.recordId));

    if (_anySalesReturnRequestsPendingApproval) {
        sitePage.$.showToast(
            ui.localize(
                '@sage/xtrem-sales/pages__site_extension__control_PR_approval',
                'You need to approve or reject pending documents before disabling the approval process.',
            ),
            { type: 'warning' },
        );
        sitePage.isSalesReturnRequestApprovalManaged.value = true;
        if (!wasDirtyBeforeChanging) {
            sitePage.$.setPageClean();
        }
        return;
    }

    sitePage.salesReturnRequestDefaultApprover.isReadOnly = true;
    sitePage.salesReturnRequestSubstituteApprover.isReadOnly = true;
}
