import { asyncArray } from '@sage/xtrem-async-helper';
import { withoutEdges } from '@sage/xtrem-client';
import * as ui from '@sage/xtrem-ui';
import type { SalesOrderShippingMassProcess as SalesOrderShippingMassProcessPage } from '../pages/sales-order-shipping-mass-process';
import type { SalesShipmentInvoicingMassProcess as SalesShipmentInvoicingMassProcessPage } from '../pages/sales-shipment-invoicing-mass-process';

async function getSite(
    pageInstance: SalesOrderShippingMassProcessPage | SalesShipmentInvoicingMassProcessPage,
    businessEntityId: string,
) {
    return withoutEdges(
        await pageInstance.$.graph
            .node('@sage/xtrem-system/Site')
            .query(
                ui.queryUtils.edgesSelector(
                    {
                        _id: true,
                        isFinance: true,
                        financialSite: { _id: true },
                        businessEntity: { _id: true },
                        legalCompany: { _id: true },
                    },
                    {
                        filter: {
                            businessEntity: { _id: businessEntityId },
                        },
                        first: 1,
                    },
                ),
            )
            .execute(),
    ).map(site => ({
        _id: site._id,
        legalCompanyId: site.legalCompany._id,
    }))[0];
}

async function getCustomers(pageInstance: SalesOrderShippingMassProcessPage | SalesShipmentInvoicingMassProcessPage) {
    return withoutEdges(
        await pageInstance.$.graph
            .node('@sage/xtrem-master-data/Customer')
            .query(
                ui.queryUtils.edgesSelector(
                    {
                        _id: true,
                        businessEntity: {
                            isSite: true,
                            _id: true,
                        },
                    },
                    {
                        filter: {
                            isActive: true,
                        },
                    },
                ),
            )
            .execute(),
    );
}

export async function getCustomersId(
    pageInstance: SalesOrderShippingMassProcessPage | SalesShipmentInvoicingMassProcessPage,
    legalCompanyId: string | null,
) {
    if (!legalCompanyId) {
        return [];
    }

    return (
        await asyncArray(await getCustomers(pageInstance))
            .filter(async customer => {
                if (customer.businessEntity.isSite) {
                    const site = await getSite(pageInstance, customer.businessEntity._id);
                    if (site && site.legalCompanyId === legalCompanyId) {
                        return false;
                    }
                }
                return true;
            })
            .toArray()
    ).map(customer => customer._id);
}
