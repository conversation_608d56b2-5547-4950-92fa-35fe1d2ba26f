import type * as isDisabledButtonsCommon from '@sage/xtrem-system/build/lib/client-functions/interfaces/is-disabled-buttons';
import type * as isHiddenButtonsCommon from '@sage/xtrem-system/build/lib/client-functions/interfaces/is-hidden-buttons';
import type * as isDisabledButtons from './interfaces/is-disabled-buttons-sales-shipment';
import type * as isHiddenButtons from './interfaces/is-hidden-buttons-sales-shipment';

export function isDisabledButtonSaveAction(
    data: isDisabledButtonsCommon.SaveOrCancelParameters<isDisabledButtons.SaveParameters>,
) {
    if (data.recordId) {
        if (
            data.parameters.status === 'readyToProcess' ||
            data.parameters.status === 'readyToShip' ||
            data.parameters.status === 'shipped'
        ) {
            return !data.isDirty;
        }
        return true;
    }
    return !data.isDirty;
}

export function isDisabledButtonCancelAction(
    data: isDisabledButtonsCommon.SaveOrCancelParameters<isDisabledButtons.SaveParameters>,
) {
    if (data.recordId) {
        if (
            data.parameters.status === 'readyToProcess' ||
            data.parameters.status === 'readyToShip' ||
            data.parameters.status === 'shipped'
        ) {
            return !data.isDirty;
        }
        return true;
    }
    return !data.isDirty;
}

export function isHiddenButtonDeleteAction(
    data: isHiddenButtonsCommon.DeleteParameters<isHiddenButtons.DeleteParameters>,
) {
    if (data.recordId && data.parameters.status === 'readyToProcess' && data.parameters.displayStatus !== 'error') {
        return false;
    }
    return true;
}

/* -------------------*/

export function isHiddenButtonConfirmAction(
    data: isHiddenButtonsCommon.BusinessButtonParameters<isHiddenButtons.ConfirmParameters>,
) {
    if (data.recordId && data.parameters.status === 'readyToProcess') {
        return data.isDirty;
    }
    return true;
}

export function isDisabledButtonConfirmAction(
    data: isDisabledButtonsCommon.BusinessButtonParameters<isDisabledButtons.ConfirmParameters>,
) {
    return isHiddenButtonConfirmAction(data);
}

/* -------------------*/

export function isHiddenButtonRevertAction(
    data: isHiddenButtonsCommon.BusinessButtonParameters<isHiddenButtons.RevertParameters>,
) {
    if (
        data.recordId &&
        data.parameters.status === 'readyToShip' &&
        data.parameters.stockTransactionStatus !== 'inProgress'
    ) {
        return data.isDirty;
    }
    return true;
}

export function isDisabledButtonRevertAction(
    data: isDisabledButtonsCommon.BusinessButtonParameters<isDisabledButtons.RevertParameters>,
) {
    return isHiddenButtonRevertAction(data);
}

/* -------------------*/

export function isHiddenButtonPostAction(
    data: isHiddenButtonsCommon.BusinessButtonParameters<isHiddenButtons.PostParameters>,
) {
    if (
        data.recordId &&
        data.parameters.status === 'readyToShip' &&
        data.parameters.stockTransactionStatus !== 'inProgress'
    ) {
        return data.isDirty;
    }
    return true;
}

export function isDisabledButtonPostAction(
    data: isDisabledButtonsCommon.BusinessButtonParameters<isDisabledButtons.PostParameters>,
) {
    return isHiddenButtonPostAction(data);
}

/* -------------------*/

export function isHiddenButtonRepostAction(
    data: isDisabledButtonsCommon.BusinessButtonParameters<isDisabledButtons.RepostParameters>,
) {
    if (data.recordId && data.parameters.fromNotificationHistory) {
        return !data.isDirty;
    }
    return true;
}

export function isDisabledButtonRepostAction(
    data: isHiddenButtonsCommon.BusinessButtonParameters<isHiddenButtons.RepostParameters>,
) {
    return isHiddenButtonRepostAction(data);
}

/* -------------------*/

export function isHiddenButtonCreateSalesInvoicesFromShipmentsAction(
    data: isHiddenButtonsCommon.BusinessButtonParameters<isHiddenButtons.CreateSalesInvoicesFromShipmentsParameters>,
) {
    if (data.recordId && data.parameters.status === 'shipped' && data.parameters.invoiceStatus !== 'invoiced') {
        return data.isDirty;
    }
    return true;
}

export function isDisabledButtonCreateSalesInvoicesFromShipmentsAction(
    data: isDisabledButtonsCommon.BusinessButtonParameters<isDisabledButtons.CreateSalesInvoicesFromShipmentsParameters>,
) {
    return isHiddenButtonCreateSalesInvoicesFromShipmentsAction(data);
}

/* -------------------*/

export function isHiddenButtonCreateSalesReturnRequestFromShipmentsAction(
    data: isHiddenButtonsCommon.BusinessButtonParameters<isHiddenButtons.CreateSalesReturnRequestFromShipmentsParameters>,
) {
    if (
        data.recordId &&
        data.parameters.status === 'shipped' &&
        data.parameters.returnRequestStatus !== 'returnRequested'
    ) {
        return data.isDirty;
    }
    return true;
}

export function isDisabledButtonCreateSalesReturnRequestFromShipmentsAction(
    data: isDisabledButtonsCommon.BusinessButtonParameters<isDisabledButtons.PrintParameters>,
) {
    return isHiddenButtonCreateSalesReturnRequestFromShipmentsAction(data);
}

/* -------------------*/

export function isHiddenButtonPrintAction(
    data: isHiddenButtonsCommon.HeaderQuickActionButtonParameters<isHiddenButtons.PrintParameters>,
) {
    return !data.recordId || data.parameters.status === 'closed';
}

export function isDisabledButtonPrintAction(
    data: isDisabledButtonsCommon.HeaderQuickActionButtonParameters<isDisabledButtons.PrintParameters>,
) {
    return !data.recordId || data.isDirty || (data.parameters && data.parameters.status === 'closed');
}

/* -------------------*/

export function isHiddenButtonDefaultDimensionAction(
    data: isHiddenButtonsCommon.HeaderDropDownActionButtonParameters<isHiddenButtons.DefaultDimensionParameters>,
) {
    if (
        data.recordId &&
        data.parameters &&
        (data.parameters.status === 'shipped' || data.parameters.displayStatus === 'postingInProgress')
    ) {
        return true;
    }
    return false;
}

export function isDisabledButtonDefaultDimensionAction(
    data: isDisabledButtonsCommon.HeaderDropDownActionButtonParameters<isDisabledButtons.DefaultDimensionParameters>,
) {
    if (
        data.parameters &&
        data.parameters.site &&
        data.parameters.stockSite &&
        data.parameters.shipToCustomer &&
        data.parameters.date
    ) {
        return false;
    }
    return true;
}

/* -------------------*/

export function isHiddenButtonSelectFromSalesOrderLinesAction(
    data: isHiddenButtonsCommon.HeaderDropDownActionButtonParameters<isHiddenButtons.SelectFromSalesOrderLinesParameters>,
) {
    if (
        data.parameters &&
        data.parameters.stockSite &&
        data.parameters.shipToCustomer &&
        data.parameters.date &&
        data.parameters.status === 'readyToProcess'
    ) {
        return false;
    }
    return true;
}

export function isDisabledButtonSelectFromSalesOrderLinesAction(
    data: isDisabledButtonsCommon.HeaderDropDownActionButtonParameters<isDisabledButtons.SelectFromSalesOrderLinesParameters>,
) {
    return isHiddenButtonSelectFromSalesOrderLinesAction(data);
}
