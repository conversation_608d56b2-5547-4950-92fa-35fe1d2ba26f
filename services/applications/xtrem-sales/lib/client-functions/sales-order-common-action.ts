import * as MasterDataUtils from '@sage/xtrem-master-data/build/lib/client-functions/utils';
import * as ui from '@sage/xtrem-ui';
import { SalesOrder } from '../pages/sales-order';
import type { UpdatePrintStatusParameters } from './interfaces/sales-order-actions-functions';

async function executeSetIsPrintedTrueMutation(parameters: UpdatePrintStatusParameters): Promise<boolean> {
    const requestIsPrintedTrue = parameters.pageOrWidget.$.graph
        .node('@sage/xtrem-sales/SalesOrder')
        .mutations.setIsPrintedTrue(true, { order: parameters._id, isPrinted: parameters.isPrinted });
    if (parameters.pageOrWidget instanceof SalesOrder) {
        return requestIsPrintedTrue.execute();
    }
    // Need to do try/catch for widget the errors are not managed by the framwork
    try {
        return await requestIsPrintedTrue.execute();
    } catch (error) {
        parameters.pageOrWidget.$.showToast(MasterDataUtils.formatError(parameters.pageOrWidget, error), {
            type: 'error',
        });
        return false;
    }
}

export async function updatePrintStatusAction(parameters: UpdatePrintStatusParameters) {
    parameters.pageOrWidget.$.loader.isHidden = false;
    const isSetIsPrintedTrue = await executeSetIsPrintedTrueMutation(parameters);

    if (parameters.isPrinted && isSetIsPrintedTrue) {
        const displaySuccess =
            parameters.status === 'quote'
                ? ui.localize('@sage/xtrem-sales/pages__sales_quote__printed', 'The sales quote was printed')
                : ui.localize('@sage/xtrem-sales/pages__sales_order__printed', 'The sales order was printed');

        parameters.pageOrWidget.$.showToast(displaySuccess, { type: 'success' });
    }
    // hide the loader helper, so it hidden in all cases (called by printSalesOrderAction )
    parameters.pageOrWidget.$.loader.isHidden = true;
    if (!isSetIsPrintedTrue) {
        throw new Error(
            ui.localize(
                '@sage/xtrem-sales/pages__sales_order__printed_status_not_updated',
                'Unable to update order {{number}} to printed status.',
                { number: parameters.number },
            ),
        );
    }
}

export function showOnHoldBlockingMessage(pageOrWidget: ui.Page | ui.widgets.TableWidget) {
    return pageOrWidget.$.showToast(
        ui.localize(
            '@sage/xtrem-sales/pages__sales_order__bill_to_customer_on_hold_blocking',
            'The bill-to customer is on hold.',
        ),
        { type: 'error' },
    );
}
