import type { decimal, ExtractEdgesPartial, OperationParamType } from '@sage/xtrem-client';
import type { ItemCustomerPrice$Queries } from '@sage/xtrem-master-data-api-partial';
import { convertAmount } from '@sage/xtrem-master-data/build/lib/shared-functions/common';
import type {
    GraphApi,
    SalesCreditMemoLineBinding,
    SalesInvoiceLineBinding,
    SalesOrderLineBinding,
} from '@sage/xtrem-sales-api';
import * as ui from '@sage/xtrem-ui';

export async function getSalesPrice(
    graph: ui.GraphQLApi<GraphApi>,
    siteId: string,
    stockSiteId: string,
    customerId: string,
    currencyId: string,
    itemId: string,
    unitId: string,
    quantity: decimal,
    date: Date,
) {
    const params: OperationParamType<ItemCustomerPrice$Queries['getSalesPrice']> = {
        priceParameters: {
            site: siteId,
            stockSite: stockSiteId,
            customer: customerId,
            currency: currencyId,
            item: itemId,
            quantity,
            unit: unitId,
            date: date.toISOString().substring(0, 10),
        },
    };

    const salesPriceResponse =
        currencyId && itemId && unitId
            ? (await graph
                  .node('@sage/xtrem-master-data/ItemCustomerPrice')
                  .queries.getSalesPrice(
                      {
                          grossPrice: true,
                          discount: true,
                          charge: true,
                          priceReason: {
                              _id: true,
                              name: true,
                          },
                          error: true,
                      },
                      params,
                  )
                  .execute()) || null
            : null;

    return salesPriceResponse;
}

export function commonUpdateGrossProfit<
    LineBinding extends SalesOrderLineBinding | SalesInvoiceLineBinding | SalesCreditMemoLineBinding,
>(
    page: ui.Page & {
        lines: ui.fields.TableControlObject<LineBinding>;
        totalGrossProfit: ui.fields.NumericControlObject;
    },
    lineToUpdate: {
        itemId: string;
        siteId: string;
        quantityInStockUnit: decimal;
        rowData: LineBinding & { amountExcludingTax: string; stockCostAmount: string; grossProfitAmount: string };
        currencyInfo: {
            rateMultiplication: decimal;
            rateDivision: decimal;
            fromDecimals: decimal;
            toDecimals: decimal;
        };
    },
    newCostInCompanyCurrency: decimal,
) {
    const oldProfit: decimal = Number(lineToUpdate.rowData.grossProfitAmount ?? 0);
    lineToUpdate.rowData.stockCostAmount = convertAmount(
        newCostInCompanyCurrency,
        lineToUpdate.currencyInfo.rateMultiplication,
        lineToUpdate.currencyInfo.rateDivision,
        lineToUpdate.currencyInfo.fromDecimals,
        lineToUpdate.currencyInfo.toDecimals,
    ).toString();
    lineToUpdate.rowData.grossProfitAmount = (
        +lineToUpdate.rowData.amountExcludingTax - +lineToUpdate.rowData.stockCostAmount
    ).toString();
    page.lines.addOrUpdateRecordValue(lineToUpdate.rowData as unknown as ExtractEdgesPartial<LineBinding>);

    page.totalGrossProfit.value =
        (page.totalGrossProfit.value ?? 0) + +lineToUpdate.rowData.grossProfitAmount - oldProfit;
}

export function creationErrorMessages(errorMessages: { message: string; loggerMessage: string }[]): string {
    if (errorMessages.length) {
        return errorMessages.map(errorMessage => errorMessage.loggerMessage).join('\n\n');
    }
    return '';
}

export function showTaxCalcFiledPostBlockingMessage(page: ui.Page) {
    return page.$.showToast(
        ui.localize(
            '@sage/xtrem-sales/pages__tax_calculation_failed_post_blocking',
            'You need to resolve tax calculation issues before posting.',
        ),
        { type: 'error' },
    );
}

export function showTaxCalcFiledPrintBlockingMessage(page: ui.Page) {
    return page.$.showToast(
        ui.localize(
            '@sage/xtrem-sales/pages__tax_calculation_failed_print_blocking',
            'You need to resolve tax calculation issues before printing.',
        ),
        { type: 'error' },
    );
}

export function showErrorsBlockingMessage(page: ui.Page) {
    return page.$.showToast(
        ui.localize('@sage/xtrem-sales/pages__errors_blocking', 'You need to resolve errors before printing.'),
        { type: 'error' },
    );
}

export function getNotFullyPaidFilter(withCredit = true) {
    return {
        title: 'Not fully paid',
        graphQLFilter: withCredit
            ? { displayStatus: { _in: ['posted', 'partiallyCredited', 'credited', 'partiallyPaid'] } }
            : { displayStatus: { _in: ['posted', 'partiallyPaid'] } },
    };
}

export function getFullyPaidFilter() {
    return {
        title: 'Fully paid',
        graphQLFilter: { displayStatus: { _eq: 'paid' } },
    };
}

export function selectedQuantityValidation(value: number, quantity: string): string | undefined {
    if (+value <= 0) {
        return ui.localize(
            '@sage/xtrem-sales/shipment__error_zero_or_negative',
            'You need to enter a quantity greater than zero.',
        );
    }
    if (+value > +quantity) {
        return ui.localize(
            '@sage/xtrem-sales/pages__sales_order_table_panel__error_greater_quantity',
            'You cannot ship a quantity more than the order you select.',
        );
    }
    return undefined;
}
