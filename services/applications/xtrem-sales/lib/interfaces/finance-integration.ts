import type { Collection, decimal } from '@sage/xtrem-core';
import type * as xtremFinanceData from '@sage/xtrem-finance-data';
import type * as xtremMasterData from '@sage/xtrem-master-data';
import type * as xtremSales from '../index';

export interface SalesInvoiceDocumentLine extends xtremFinanceData.interfaces.InvoiceDocumentLine {
    origin: Promise<xtremMasterData.enums.BaseOrigin>;
    salesShipmentLines: Collection<xtremSales.nodes.SalesShipmentLineToSalesInvoiceLine>;
    quantity: Promise<decimal>;
}

export interface SalesReturnDocumentLine extends xtremFinanceData.interfaces.ReturnDocumentLine {
    origin: Promise<xtremMasterData.enums.BaseOrigin>;
    toReturnRequestLines: Collection<xtremSales.nodes.SalesReturnRequestLineToSalesReturnReceiptLine>;
    salesShipmentLines: Collection<xtremSales.nodes.SalesShipmentLineToSalesReturnReceiptLine>;
    quantity: Promise<decimal>;
}

export interface SalesCreditMemoDocumentLine extends xtremFinanceData.interfaces.InvoiceDocumentLine {
    origin: Promise<xtremMasterData.enums.BaseOrigin>;
    toReturnRequestLines: Collection<xtremSales.nodes.SalesReturnRequestLineSalesCreditMemoLine>;
    quantity: Promise<decimal>;
}

export interface SalesInvoiceCreditMemoRepost {
    header: {
        paymentTerm?: xtremMasterData.nodes.PaymentTerm;
    };
    lines: xtremFinanceData.interfaces.FinanceIntegrationDocumentLineUpdate[];
}
