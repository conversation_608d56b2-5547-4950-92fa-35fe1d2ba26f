import type { Collection, decimal, Node, Reference } from '@sage/xtrem-core';
import type * as xtremMasterData from '@sage/xtrem-master-data';
import type * as xtremSystem from '@sage/xtrem-system';
import type * as xtremSales from '../index';

export interface SalesLineNode extends Node {
    priceOrigin: Promise<xtremSales.enums.SalesPriceOrigin | null>;
    isPriceDeterminated: Promise<boolean>;
    priceReason: Reference<xtremMasterData.nodes.CustomerPriceReason | null>;
    discountCharges: Collection<xtremMasterData.nodes.BaseLineDiscountCharge>;
    grossPrice?: Promise<decimal | null>;
    currency: Reference<xtremMasterData.nodes.Currency>;
    item: Reference<xtremMasterData.nodes.Item>;
    quantity: Promise<decimal>;
    unit: Reference<xtremMasterData.nodes.UnitOfMeasure>;
    site: Reference<xtremSystem.nodes.Site>;
}
