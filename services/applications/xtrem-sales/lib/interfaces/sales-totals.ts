import type { date, decimal } from '@sage/xtrem-core';

export interface SalesOrderTotalsResult {
    group: {
        currency: {
            id: string;
        };
    };
    values: {
        remainingTotalAmountToShipExcludingTax: {
            sum: decimal;
        };
    };
}

export interface SalesInvoiceTotalsResult {
    group: {
        date: date;
        currency: {
            id: string;
        };
    };
    values: {
        totalAmountExcludingTax: {
            sum: decimal;
        };
    };
}

export interface SalesInvoiceTotalsGrossResult {
    group: {
        date: date;
        currency: {
            id: string;
        };
    };
    values: {
        totalGrossProfit: {
            sum: decimal;
        };
    };
}
