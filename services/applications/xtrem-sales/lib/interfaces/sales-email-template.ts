export interface SalesInvoiceEmailTemplateData {
    invoice: {
        number: string;
        date: string;
        totalAmountIncludingTax: string;
        currencySymbol: string;
    };
    contact: {
        title: string;
        firstName: string;
        lastName: string;
    };
    site: {
        name: string;
    };
}

export interface SalesCreditMemoEmailTemplateData {
    creditMemo: {
        number: string;
        date: string;
        totalAmountIncludingTax: string;
        currencySymbol: string;
    };
    contact: {
        title: string;
        firstName: string;
        lastName: string;
    };
    site: {
        name: string;
    };
}

export interface SalesOrderEmailTemplateData {
    salesOrder: {
        number: string;
        date: string;
    };
    contact: {
        title: string;
        firstName: string;
        lastName: string;
    };
    site: {
        name: string;
    };
}
