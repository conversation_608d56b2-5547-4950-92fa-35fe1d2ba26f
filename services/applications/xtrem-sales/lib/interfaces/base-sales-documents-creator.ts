import type { Context, EnumDataType, StaticThis, UnPromised } from '@sage/xtrem-core';
import type * as xtremSales from '../index';

export interface CheckTotalQuantityBaseOnStatus {
    context: Context;
    linkedDocument: xtremSales.interfaces.AnyInputDocumentLine | xtremSales.nodes.SalesReturnRequestLine;
    aggregateNode: StaticThis<xtremSales.interfaces.AnyBaseLineToSalesDocumentLine>;
    dictLine: xtremSales.interfaces.SalesDocumentLineUpdateInterface;
}

export interface UpdateRelatedSalesDocumentsStatuses<
    LineT extends AnyDocumentLineWithRelatedDocuments,
    DocumentT extends AnyDocumentHeaderWithRelatedDocuments = UnPromised<LineT['document']>,
    DictNameT extends keyof DocumentT = keyof DocumentT,
> {
    salesDocumentsArray: Array<xtremSales.interfaces.AnyInputDocumentHeader | xtremSales.nodes.SalesReturnRequest>;
    tempDict: DictNameT; // 'salesOrders' | 'salesShipments' | 'salesInvoices' | 'salesReturnRequests';
    statusType:
        | 'invoiceStatus'
        | 'shippingStatus'
        | 'creditStatus'
        | 'returnRequestStatus'
        | 'receiptStatus'
        | 'returnReceiptStatus';
    classInstance: DocumentT;
    aggregateNode: StaticThis<xtremSales.interfaces.AnyBaseLineToSalesDocumentLine>;
    enumDataType: EnumDataType;
    updateQuantityInSalesUnit?: boolean;
}

export interface CreateSalesOutputDocumentsLineErrorReturnValues {
    lineNumber: number;
    linePosition: number;
    message: string;
}

export interface CreateSalesOutputDocumentsReturnValues {
    status: string;
    numberOfShipments?: number;
    numberOfInvoices?: number;
    numberOfCreditMemos?: number;
    numberOfReturnRequests?: number;
    numberOfReturnReceipts?: number;
    documentsCreated: xtremSales.interfaces.AnyOutputDocumentHeader[];
    lineErrors: CreateSalesOutputDocumentsLineErrorReturnValues[];
    errorMessage: OutputDocumentCreationError[];
}

export interface OutputDocumentCreationError {
    loggerMessage?: string;
    message?: string;
}

export interface CreateSalesOutputDocumentsReturnValuesWithFinanceMessages
    extends CreateSalesOutputDocumentsReturnValues {
    financeMessages?: string;
}

export type AnyOutputDocumentHeader =
    | xtremSales.nodes.SalesCreditMemo
    | xtremSales.nodes.SalesInvoice
    | xtremSales.nodes.SalesShipment
    | xtremSales.nodes.SalesReturnRequest
    | xtremSales.nodes.SalesReturnReceipt;

export type AnyOutputDocumentLine =
    | xtremSales.nodes.SalesCreditMemoLine
    | xtremSales.nodes.SalesInvoiceLine
    | xtremSales.nodes.SalesShipmentLine
    | xtremSales.nodes.SalesReturnRequestLine
    | xtremSales.nodes.SalesReturnReceiptLine;

export type AnyOutputDocumentLineWithDiscountCharges =
    | xtremSales.nodes.SalesCreditMemoLine
    | xtremSales.nodes.SalesInvoiceLine
    | xtremSales.nodes.SalesShipmentLine;

export type AnyInputDocumentLine =
    | xtremSales.nodes.SalesOrderLine
    | xtremSales.nodes.SalesShipmentLine
    | xtremSales.nodes.SalesInvoiceLine
    | xtremSales.nodes.SalesReturnRequestLine;

export type AnyInputDocumentLineWithStatus = xtremSales.nodes.SalesOrderLine | xtremSales.nodes.SalesShipmentLine;

export type AnyInputDocumentHeader =
    | xtremSales.nodes.SalesOrder
    | xtremSales.nodes.SalesShipment
    | xtremSales.nodes.SalesInvoice
    | xtremSales.nodes.SalesReturnRequest;

export type AnyInputDocumentHeaderWithLineStatus = xtremSales.nodes.SalesOrder | xtremSales.nodes.SalesShipment;

export type AnyDocumentHeaderWithRelatedDocuments =
    | xtremSales.nodes.SalesCreditMemo
    | xtremSales.nodes.SalesInvoice
    | xtremSales.nodes.SalesReturnReceipt
    | xtremSales.nodes.SalesReturnRequest
    | xtremSales.nodes.SalesShipment
    | xtremSales.nodes.SalesReturnReceipt;

export type AnyDocumentLineWithRelatedDocuments =
    | xtremSales.nodes.SalesCreditMemoLine
    | xtremSales.nodes.SalesInvoiceLine
    | xtremSales.nodes.SalesReturnReceiptLine
    | xtremSales.nodes.SalesReturnRequestLine
    | xtremSales.nodes.SalesShipmentLine
    | xtremSales.nodes.SalesReturnReceiptLine;

export type AnyBaseLineToSalesDocumentLine =
    | xtremSales.nodes.SalesOrderLineToSalesInvoiceLine
    | xtremSales.nodes.SalesShipmentLineToSalesInvoiceLine
    | xtremSales.nodes.SalesOrderLineToSalesShipmentLine
    | xtremSales.nodes.SalesInvoiceLineToSalesCreditMemoLine
    | xtremSales.nodes.SalesShipmentLineToSalesReturnRequestLine
    | xtremSales.nodes.SalesReturnRequestLineSalesCreditMemoLine
    | xtremSales.nodes.SalesReturnRequestLineToSalesReturnReceiptLine
    | xtremSales.nodes.SalesShipmentLineToSalesReturnReceiptLine;
