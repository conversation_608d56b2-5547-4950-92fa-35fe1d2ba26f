import type { integer, TextStream } from '@sage/xtrem-core';
import type * as xtremMasterData from '@sage/xtrem-master-data';
import type * as xtremStockData from '@sage/xtrem-stock-data';
import type * as xtremSales from '../index';

export type SalesDocument =
    | xtremSales.nodes.SalesOrder
    | xtremSales.nodes.SalesShipment
    | xtremSales.nodes.SalesInvoice
    | xtremSales.nodes.SalesReturnRequest
    | xtremSales.nodes.SalesCreditMemo;

// export type SalesApprovalDocument = xtremSales.nodes.SalesReturnRequest;

export interface SalesDocumentLineUpdateInterface {
    _id: number;
    action: 'create' | 'update' | 'delete';
    aggregateNodeId: string;
}

export type AnyDiscountChargeCollection =
    | xtremSales.nodes.SalesOrderLineDiscountCharge
    | xtremSales.nodes.SalesShipmentLineDiscountCharge
    | xtremSales.nodes.SalesInvoiceLineDiscountCharge
    | xtremSales.nodes.SalesCreditMemoLineDiscountCharge;

export interface DefaultDiscountChargeReturnValues {
    sign: xtremMasterData.enums.DiscountChargeSign;
    valueType: xtremMasterData.enums.DiscountChargeValueType;
    calculationRule: xtremMasterData.enums.DiscountChargeCalculationRule;
    value: number;
}

export interface SalesOrderMail {
    subject: string;
    emailTemplate: string;
    filePrefix: string;
}

export interface TestOrderCreationParameters {
    customerId: string;
    item: xtremMasterData.nodes.Item;
    itemQuantity: integer;
    orderQuantity: integer;
    numberOfLinesPerOrder: integer;
    orderNumberRoot?: string;
    fixedNumberOfLines?: boolean;
    allocateStock?: boolean;
    randomDeliveryDate?: boolean;
}

export type SalesDocumentNotePropagation =
    | xtremSales.nodes.SalesShipment
    | xtremSales.nodes.SalesInvoice
    | xtremSales.nodes.SalesCreditMemo;
export type SalesDocumentLineNotePropagation =
    | xtremSales.nodes.SalesShipmentLine
    | xtremSales.nodes.SalesInvoiceLine
    | xtremSales.nodes.SalesCreditMemoLine;

export type LinkedSalesDocument =
    | xtremSales.nodes.SalesOrder
    | xtremSales.nodes.SalesShipment
    | xtremSales.nodes.SalesInvoice;

export type LinkedSalesDocumentLine =
    | xtremSales.nodes.SalesOrderLine
    | xtremSales.nodes.SalesShipmentLine
    | xtremSales.nodes.SalesInvoiceLine;

export interface CurrentNote {
    isOverwrite: boolean;
    internalNote: TextStream;
    isExternalNote?: boolean;
    externalNote?: TextStream;
}

export interface AssignedOrderAssignments {
    line: xtremSales.nodes.SalesOrderLine;
    orderAssignments: xtremStockData.nodes.OrderAssignment[] | null;
}

export interface AssignedOrdersSum {
    sumActualQuantity: number;
    sumQuantityInStockUnit: number;
    filteredAssignedOrders: AssignedOrderAssignments[];
}
