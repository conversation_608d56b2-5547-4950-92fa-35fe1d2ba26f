{"@sage/xtrem-sales/activity__proforma_invoice__name": "Proforma invoice", "@sage/xtrem-sales/activity__sales_credit_memo__name": "Sales credit memo", "@sage/xtrem-sales/activity__sales_credit_memo_reason__name": "Sales credit memo reason", "@sage/xtrem-sales/activity__sales_invoice__name": "Sales invoice", "@sage/xtrem-sales/activity__sales_order__name": "Sales order", "@sage/xtrem-sales/activity__sales_return_receipt__name": "Sales return receipt", "@sage/xtrem-sales/activity__sales_return_request__name": "Sales return request", "@sage/xtrem-sales/activity__sales_return_request_reason__name": "Sales return request reason", "@sage/xtrem-sales/activity__sales_shipment__name": "Sales shipment", "@sage/xtrem-sales/cant_approve_return_request_when_not_pending_approval_or_closed": "You can only approve a sales return request if the approval status is {{pendingApproval}}.", "@sage/xtrem-sales/cant_post_credit_memo_when_status_is_not_draft": "The status is not {{draft}}, the credit memo cannot be posted.", "@sage/xtrem-sales/cant_post_credit_memo_when_taxCalculationStatus_is_not_done": "The tax calculation is not {{done}}, the credit memo cannot be posted.", "@sage/xtrem-sales/cant_post_invoice_when_status_is_not_draft": "The status is not {{draft}}, the invoice cannot be posted.", "@sage/xtrem-sales/cant_post_invoice_when_taxCalculationStatus_is_not_done": "The tax calculation is not {{done}}, the invoice cannot be posted.", "@sage/xtrem-sales/cant_post_shipment_when_bill_to_customer_is_on_hold": "The Bill-to customer is on hold. The shipment cannot be posted.", "@sage/xtrem-sales/cant_post_shipment_when_not_all_lines_are_allocated": "All the lines must be allocated to ship this document.", "@sage/xtrem-sales/cant_post_shipment_when_shipping_date_is_in_the_future": "The shipping date is in the future, the shipment cannot be posted.", "@sage/xtrem-sales/cant_post_shipment_when_status_is_not_ready_to_ship": "The status is not {{readyToShipStatus}}, the shipment cannot be posted.", "@sage/xtrem-sales/cant_print_packing_slip_when_status_is_ready_to_process_bulk": "The sales shipment cannot be printed: {{number}}. It is in status: {{shipped}}.", "@sage/xtrem-sales/cant_print_pick_list_when_status_is_not_ready_to_process_bulk": "To print the pick list, the sales shipment needs to be ready to process: {{number}}.", "@sage/xtrem-sales/cant_reject_return_request_when_not_pending_approval_or_closed": "You can only reject a sales return request if the approval status is {{pendingApproval}}.", "@sage/xtrem-sales/class__base_sales_documents_creator__no_quantity_left": "No quantity in sales unit left.", "@sage/xtrem-sales/class__base_sales_documents_creator__remaining_quantity": "The sales document line quantity cannot be larger than the remaining quantity in the related document.", "@sage/xtrem-sales/class__sales_credit_memo_creator__console_log_message": "The {{outputDocumentNumber}} sales credit memo number was created from the following sales invoice numbers: {{inputDocumentsNumbers}}.", "@sage/xtrem-sales/class__sales_credit_memos_creator__console_log_error_message": "The sales credit memo could not be created using the sales invoice numbers: {{inputDocumentsNumbers}}. Details: {{message}}", "@sage/xtrem-sales/class__sales_invoice_creator__console_log_error_message": "The sales invoice could not be created using the sales shipment numbers: {{inputDocumentsNumbers}}. Details: {{message}}", "@sage/xtrem-sales/class__sales_invoice_creator__console_log_message": "The sales invoice was created from the following sales shipment numbers: {{inputDocumentsNumbers}}.", "@sage/xtrem-sales/class__sales_return_receipt_creator__console_log_error_message": "The sales return receipt could not be created using the sales return request numbers: {{inputDocumentsNumbers}}. Details: {{message}}", "@sage/xtrem-sales/class__sales_return_receipt_creator__console_log_message": "The {{outputDocumentNumber}} sales return receipt number was created from the following sales return request numbers: {{inputDocumentsNumbers}}.", "@sage/xtrem-sales/class__sales_return_request_creator__console_log_error_message": "The sales return request could not be created using the sales shipment numbers: {{inputDocumentsNumbers}}. Details: {{message}}", "@sage/xtrem-sales/class__sales_shipments_creator__console_log_error_message": "The sales shipment could not be created using the sales order numbers: {{inputDocumentsNumbers}}. Details: {{message}}", "@sage/xtrem-sales/classes__sales_credit_memo_creator__error_selected_line_document_status": "You cannot create a credit memo if there is a posting error.", "@sage/xtrem-sales/classes__sales_credit_memo_creator__in_progress_selected_line_document_status": "You can only credit a credit memo for a posted invoice.", "@sage/xtrem-sales/classes__sales_credit_memo_creator__invalid_selected_is_credit_memo_expected": "A sales credit memo is not expected for this return request line.", "@sage/xtrem-sales/classes__sales_credit_memo_creator__invalid_selected_line_document_status": "Post an invoice first.", "@sage/xtrem-sales/classes__sales_credit_memo_creator__invalid_selected_lines_credit_status": "An invoice line is already credited.", "@sage/xtrem-sales/classes__sales_credit_memo_creator__invalid_selected_lines_credit_status_return": "A return request line is already credited.", "@sage/xtrem-sales/classes__sales_credit_memo_creator__invalid_selected_sales_shipment_not_invoiced": "The related sales shipment line is not invoiced.", "@sage/xtrem-sales/classes__sales_credit_memo_creator__is_receipt_expected_not_received": "A sales return receipt is expected but not yet received.", "@sage/xtrem-sales/classes__sales_credit_memo_creator__sales_return_request_not_approved": "A return request is not approved.", "@sage/xtrem-sales/classes__sales_credit_memo_creator__sales_return_request_not_confirmed": "A return request is not confirmed.", "@sage/xtrem-sales/classes__sales_invoice_creator__invalid_selected_lines_invoice_status": "A shipment line is already invoiced.", "@sage/xtrem-sales/classes__sales_order__allocation_request_status_exception": "You can only ship the sales order line after the allocation request is complete.", "@sage/xtrem-sales/classes__sales_order__improper_bill_to_customer_on_hold_exception": "The Bill-to customer is on hold on line {{linePosition}}.", "@sage/xtrem-sales/classes__sales_order__improper_shipping_date": "The shipping date cannot be lower than the doNotShipBefore field date and cannot be higher than the doNotShipAfter field date.", "@sage/xtrem-sales/classes__sales_order__incorrect_shipping_dates": "The shipping date {{todaysDate}} needs to be between the \"Do-not-ship-before date\" and \"Do-not-ship-after date\" on line {{linePosition}}.", "@sage/xtrem-sales/classes__sales_order__invalid_selected_lines__allocation_request_status": "You can only ship the sales order line after the allocation request is complete.", "@sage/xtrem-sales/classes__sales_order__invalid_selected_lines__shipping_status": "The sales order line shipped already.", "@sage/xtrem-sales/classes__sales_order__invalid_selected_lines_status": "The status for selected lines cannot be  \"Quote\" or \"Closed\".", "@sage/xtrem-sales/classes__sales_order__line_quote_exception": "The line status cannot be  \"Quote\".", "@sage/xtrem-sales/classes__sales_order__shipping_status_status_exception": "The sales order line shipped already.", "@sage/xtrem-sales/classes__sales_order__tax_calculation_status_status_exception": "The sales order tax calculation failed.", "@sage/xtrem-sales/classes__sales_return_receipt_creator__sales_return_request_line_closed": "A return request line is already closed.", "@sage/xtrem-sales/classes__sales_return_request_creator__sales_shipment_line_already_returned": "A shipment line is already returned.", "@sage/xtrem-sales/classes__sales_return_request_creator__sales_shipment_line_not_yet_posted": "A shipment line is not posted.", "@sage/xtrem-sales/client_functions__sales__resync_status_continue": "Continue", "@sage/xtrem-sales/client_functions__sales__resync_status_message": "You are about to update the status.", "@sage/xtrem-sales/client_functions__sales__resync_status_title": "Check and update status", "@sage/xtrem-sales/closeOrder____title": "Close order", "@sage/xtrem-sales/closeRequest____title": "Close request", "@sage/xtrem-sales/data_types__comment__name": "Comment", "@sage/xtrem-sales/data_types__price_origin_enum__name": "Price origin enum", "@sage/xtrem-sales/data_types__property_data_type__name": "Property data type", "@sage/xtrem-sales/data_types__sales_credit_memo_display_status_enum__name": "Sales credit memo display status enum", "@sage/xtrem-sales/data_types__sales_credit_memo_status_enum__name": "Sales credit memo status enum", "@sage/xtrem-sales/data_types__sales_document_credit_status_enum__name": "Sales document credit status enum", "@sage/xtrem-sales/data_types__sales_document_invoice_status_enum__name": "Sales document invoice status enum", "@sage/xtrem-sales/data_types__sales_document_receipt_status_enum__name": "Sales document receipt status enum", "@sage/xtrem-sales/data_types__sales_document_return_request_receipt_status_enum__name": "Sales document return request receipt status enum", "@sage/xtrem-sales/data_types__sales_document_return_status_enum__name": "Sales document return status enum", "@sage/xtrem-sales/data_types__sales_document_shipping_status_enum__name": "Sales document shipping status enum", "@sage/xtrem-sales/data_types__sales_document_type_enum__name": "Sales document type enum", "@sage/xtrem-sales/data_types__sales_invoice_default__name": "Sales invoice default", "@sage/xtrem-sales/data_types__sales_invoice_display_status_enum__name": "Sales invoice display status enum", "@sage/xtrem-sales/data_types__sales_invoice_status_enum__name": "Sales invoice status enum", "@sage/xtrem-sales/data_types__sales_order_confirm_method_return_enum__name": "Sales order confirm method return enum", "@sage/xtrem-sales/data_types__sales_order_create_sales_shipment_method_return_enum__name": "Sales order create sales shipment method return enum", "@sage/xtrem-sales/data_types__sales_order_display_status_enum__name": "Sales order display status enum", "@sage/xtrem-sales/data_types__sales_order_line_close_status_method_return_enum__name": "Sales order line close status method return enum", "@sage/xtrem-sales/data_types__sales_order_line_open_status_method_return_enum__name": "Sales order line open status method return enum", "@sage/xtrem-sales/data_types__sales_order_open_status_method_return_enum__name": "Sales order open status method return enum", "@sage/xtrem-sales/data_types__sales_order_status_enum__name": "Sales order status enum", "@sage/xtrem-sales/data_types__sales_origin_document_type_enum__name": "Sales origin document type enum", "@sage/xtrem-sales/data_types__sales_price_origin_enum__name": "Sales price origin enum", "@sage/xtrem-sales/data_types__sales_return_receipt_display_status_enum__name": "Sales return receipt display status enum", "@sage/xtrem-sales/data_types__sales_return_receipt_status_enum__name": "Sales return receipt status enum", "@sage/xtrem-sales/data_types__sales_return_request_approval_status_enum__name": "Sales return request approval status enum", "@sage/xtrem-sales/data_types__sales_return_request_close_status_method_return_enum__name": "Sales return request close status method return enum", "@sage/xtrem-sales/data_types__sales_return_request_display_status_enum__name": "Sales return request display status enum", "@sage/xtrem-sales/data_types__sales_return_request_line_close_status_method_return_enum__name": "Sales return request line close status method return enum", "@sage/xtrem-sales/data_types__sales_return_request_line_open_status_method_return_enum__name": "Sales return request line open status method return enum", "@sage/xtrem-sales/data_types__sales_return_request_open_status_method_return_enum__name": "Sales return request open status method return enum", "@sage/xtrem-sales/data_types__sales_return_request_return_type_enum__name": "Sales return request return type enum", "@sage/xtrem-sales/data_types__sales_return_request_status_enum__name": "Sales return request status enum", "@sage/xtrem-sales/data_types__sales_shipment_display_status_enum__name": "Sales shipment display status enum", "@sage/xtrem-sales/data_types__sales_shipment_status_enum__name": "Sales shipment status enum", "@sage/xtrem-sales/data_types__unbilled_account_receivable_status_enum__name": "Unbilled account receivable status enum", "@sage/xtrem-sales/edit-create-line": "Add new line", "@sage/xtrem-sales/enums__price_origin__basePrice": "Base price", "@sage/xtrem-sales/enums__price_origin__manual": "Manual", "@sage/xtrem-sales/enums__price_origin__priceList": "Price list", "@sage/xtrem-sales/enums__sales_credit_memo_display_status__draft": "Draft", "@sage/xtrem-sales/enums__sales_credit_memo_display_status__error": "Error", "@sage/xtrem-sales/enums__sales_credit_memo_display_status__paid": "Paid", "@sage/xtrem-sales/enums__sales_credit_memo_display_status__partiallyPaid": "Partially paid", "@sage/xtrem-sales/enums__sales_credit_memo_display_status__posted": "Posted", "@sage/xtrem-sales/enums__sales_credit_memo_display_status__postingInProgress": "Posting in progress", "@sage/xtrem-sales/enums__sales_credit_memo_display_status__taxCalculationFailed": "Tax calculation failed", "@sage/xtrem-sales/enums__sales_credit_memo_status__draft": "Draft", "@sage/xtrem-sales/enums__sales_credit_memo_status__error": "Error", "@sage/xtrem-sales/enums__sales_credit_memo_status__inProgress": "In progress", "@sage/xtrem-sales/enums__sales_credit_memo_status__posted": "Posted", "@sage/xtrem-sales/enums__sales_document_credit_status__credited": "Credited", "@sage/xtrem-sales/enums__sales_document_credit_status__notCredited": "Not credited", "@sage/xtrem-sales/enums__sales_document_credit_status__partiallyCredited": "Partially credited", "@sage/xtrem-sales/enums__sales_document_invoice_status__invoiced": "Invoiced", "@sage/xtrem-sales/enums__sales_document_invoice_status__notInvoiced": "Not invoiced", "@sage/xtrem-sales/enums__sales_document_invoice_status__partiallyInvoiced": "Partially invoiced", "@sage/xtrem-sales/enums__sales_document_receipt_status__notReturned": "Not returned", "@sage/xtrem-sales/enums__sales_document_receipt_status__partiallyReturned": "Partially returned", "@sage/xtrem-sales/enums__sales_document_receipt_status__returned": "Returned", "@sage/xtrem-sales/enums__sales_document_return_request_receipt_status__notReceived": "Not received", "@sage/xtrem-sales/enums__sales_document_return_request_receipt_status__partiallyReceived": "Partially received", "@sage/xtrem-sales/enums__sales_document_return_request_receipt_status__received": "Received", "@sage/xtrem-sales/enums__sales_document_return_status__noReturnRequested": "No return requested", "@sage/xtrem-sales/enums__sales_document_return_status__returnPartiallyRequested": "Return partially requested", "@sage/xtrem-sales/enums__sales_document_return_status__returnRequested": "Return requested", "@sage/xtrem-sales/enums__sales_document_shipping_status__notShipped": "Not shipped", "@sage/xtrem-sales/enums__sales_document_shipping_status__partiallyShipped": "Partially shipped", "@sage/xtrem-sales/enums__sales_document_shipping_status__shipped": "Shipped", "@sage/xtrem-sales/enums__sales_document_type__salesCreditMemo": "Sales credit memo", "@sage/xtrem-sales/enums__sales_document_type__salesInvoice": "Sales invoice", "@sage/xtrem-sales/enums__sales_document_type__salesOrder": "Sales order", "@sage/xtrem-sales/enums__sales_document_type__salesReturnRequest": "Sales return request", "@sage/xtrem-sales/enums__sales_document_type__salesShipment": "Sales shipment", "@sage/xtrem-sales/enums__sales_invoice_display_status__credited": "Credited", "@sage/xtrem-sales/enums__sales_invoice_display_status__draft": "Draft", "@sage/xtrem-sales/enums__sales_invoice_display_status__error": "Error", "@sage/xtrem-sales/enums__sales_invoice_display_status__paid": "Paid", "@sage/xtrem-sales/enums__sales_invoice_display_status__partiallyCredited": "Partially credited", "@sage/xtrem-sales/enums__sales_invoice_display_status__partiallyPaid": "Partially paid", "@sage/xtrem-sales/enums__sales_invoice_display_status__posted": "Posted", "@sage/xtrem-sales/enums__sales_invoice_display_status__postingInProgress": "Posting in progress", "@sage/xtrem-sales/enums__sales_invoice_display_status__taxCalculationFailed": "Tax calculation failed", "@sage/xtrem-sales/enums__sales_invoice_status__draft": "Draft", "@sage/xtrem-sales/enums__sales_invoice_status__error": "Error", "@sage/xtrem-sales/enums__sales_invoice_status__inProgress": "In progress", "@sage/xtrem-sales/enums__sales_invoice_status__posted": "Posted", "@sage/xtrem-sales/enums__sales_order_confirm_method_return__parametersAreIncorrect": "Parameters are incorrect", "@sage/xtrem-sales/enums__sales_order_confirm_method_return__salesOrderIsConfirmed": "Sales order is confirmed", "@sage/xtrem-sales/enums__sales_order_confirm_method_return__salesOrderStatusIsNotDraft": "Sales order status is not draft", "@sage/xtrem-sales/enums__sales_order_create_sales_shipment_method_return__parametersAreIncorrect": "Parameters are incorrect", "@sage/xtrem-sales/enums__sales_order_create_sales_shipment_method_return__salesOrderIsNotShipped": "Sales order is not shipped", "@sage/xtrem-sales/enums__sales_order_create_sales_shipment_method_return__salesOrderIsPartiallyShipped": "Sales order is partially shipped", "@sage/xtrem-sales/enums__sales_order_create_sales_shipment_method_return__salesOrderIsShipped": "Sales order is shipped", "@sage/xtrem-sales/enums__sales_order_display_status__closed": "Closed", "@sage/xtrem-sales/enums__sales_order_display_status__confirmed": "Confirmed", "@sage/xtrem-sales/enums__sales_order_display_status__partiallyShipped": "Partially shipped", "@sage/xtrem-sales/enums__sales_order_display_status__quote": "Quote", "@sage/xtrem-sales/enums__sales_order_display_status__shipped": "Shipped", "@sage/xtrem-sales/enums__sales_order_display_status__taxCalculationFailed": "Tax calculation failed", "@sage/xtrem-sales/enums__sales_order_line_close_status_method_return__parametersAreIncorrect": "Parameters are incorrect", "@sage/xtrem-sales/enums__sales_order_line_close_status_method_return__salesOrderHeaderIsNowClosed": "Sales order header is now closed", "@sage/xtrem-sales/enums__sales_order_line_close_status_method_return__salesOrderLineIsAlreadyClosed": "Sales order line is already closed", "@sage/xtrem-sales/enums__sales_order_line_close_status_method_return__salesOrderLineIsNowClosed": "Sales order line is now closed", "@sage/xtrem-sales/enums__sales_order_line_open_status_method_return__parametersAreIncorrect": "Parameters are incorrect", "@sage/xtrem-sales/enums__sales_order_line_open_status_method_return__salesOrderLineIsAlreadyOpen": "Sales order line is already open", "@sage/xtrem-sales/enums__sales_order_line_open_status_method_return__salesOrderLineIsNowOpen": "Sales order line is now open", "@sage/xtrem-sales/enums__sales_order_line_open_status_method_return__salesOrderLineIsShipped": "Sales order line is shipped", "@sage/xtrem-sales/enums__sales_order_open_status_method_return__parametersAreIncorrect": "Parameters are incorrect", "@sage/xtrem-sales/enums__sales_order_open_status_method_return__salesOrderIsAlreadyOpen": "Sales order is already open", "@sage/xtrem-sales/enums__sales_order_open_status_method_return__salesOrderIsNowOpen": "Sales order is now open", "@sage/xtrem-sales/enums__sales_order_open_status_method_return__salesOrderIsShipped": "Sales order is shipped", "@sage/xtrem-sales/enums__sales_order_status__closed": "Closed", "@sage/xtrem-sales/enums__sales_order_status__inProgress": "In progress", "@sage/xtrem-sales/enums__sales_order_status__pending": "Pending", "@sage/xtrem-sales/enums__sales_order_status__quote": "Quote", "@sage/xtrem-sales/enums__sales_origin_document_type__direct": "Direct", "@sage/xtrem-sales/enums__sales_origin_document_type__invoice": "Invoice", "@sage/xtrem-sales/enums__sales_origin_document_type__order": "Order", "@sage/xtrem-sales/enums__sales_origin_document_type__return": "Return", "@sage/xtrem-sales/enums__sales_origin_document_type__shipment": "Shipment", "@sage/xtrem-sales/enums__sales_price_origin__basePrice": "Base price", "@sage/xtrem-sales/enums__sales_price_origin__manual": "Manual", "@sage/xtrem-sales/enums__sales_price_origin__priceList": "Price list", "@sage/xtrem-sales/enums__sales_return_receipt_display_status__closed": "Closed", "@sage/xtrem-sales/enums__sales_return_receipt_display_status__draft": "Draft", "@sage/xtrem-sales/enums__sales_return_receipt_display_status__error": "Error", "@sage/xtrem-sales/enums__sales_return_receipt_display_status__postingInProgress": "Posting in progress", "@sage/xtrem-sales/enums__sales_return_receipt_status__closed": "Closed", "@sage/xtrem-sales/enums__sales_return_receipt_status__draft": "Draft", "@sage/xtrem-sales/enums__sales_return_receipt_status__inProgress": "In progress", "@sage/xtrem-sales/enums__sales_return_request_approval_status__approved": "Approved", "@sage/xtrem-sales/enums__sales_return_request_approval_status__changeRequested": "Change requested", "@sage/xtrem-sales/enums__sales_return_request_approval_status__confirmed": "Confirmed", "@sage/xtrem-sales/enums__sales_return_request_approval_status__draft": "Draft", "@sage/xtrem-sales/enums__sales_return_request_approval_status__pendingApproval": "Pending approval", "@sage/xtrem-sales/enums__sales_return_request_approval_status__rejected": "Rejected", "@sage/xtrem-sales/enums__sales_return_request_close_status_method_return__linkedSalesReturnReceiptIsNotClosed": "Linked sales return receipt is not closed", "@sage/xtrem-sales/enums__sales_return_request_close_status_method_return__parametersAreIncorrect": "Parameters are incorrect", "@sage/xtrem-sales/enums__sales_return_request_close_status_method_return__salesReturnRequestIsAlreadyClosed": "Sales return request is already closed", "@sage/xtrem-sales/enums__sales_return_request_close_status_method_return__salesReturnRequestIsNowClosed": "Sales return request is now closed", "@sage/xtrem-sales/enums__sales_return_request_display_status__approved": "Approved", "@sage/xtrem-sales/enums__sales_return_request_display_status__closed": "Closed", "@sage/xtrem-sales/enums__sales_return_request_display_status__confirmed": "Confirmed", "@sage/xtrem-sales/enums__sales_return_request_display_status__draft": "Draft", "@sage/xtrem-sales/enums__sales_return_request_display_status__partiallyReceived": "Partially received", "@sage/xtrem-sales/enums__sales_return_request_display_status__pendingApproval": "Pending approval", "@sage/xtrem-sales/enums__sales_return_request_display_status__received": "Received", "@sage/xtrem-sales/enums__sales_return_request_display_status__rejected": "Rejected", "@sage/xtrem-sales/enums__sales_return_request_line_close_status_method_return__linkedSalesReturnReceiptIsNotClosed": "Linked sales return receipt is not closed", "@sage/xtrem-sales/enums__sales_return_request_line_close_status_method_return__parametersAreIncorrect": "Parameters are incorrect", "@sage/xtrem-sales/enums__sales_return_request_line_close_status_method_return__salesReturnRequestHeaderIsNowClosed": "Sales return request header is now closed", "@sage/xtrem-sales/enums__sales_return_request_line_close_status_method_return__salesReturnRequestLineIsAlreadyClosed": "Sales return request line is already closed", "@sage/xtrem-sales/enums__sales_return_request_line_close_status_method_return__salesReturnRequestLineIsNowClosed": "Sales return request line is now closed", "@sage/xtrem-sales/enums__sales_return_request_line_open_status_method_return__parametersAreIncorrect": "Parameters are incorrect", "@sage/xtrem-sales/enums__sales_return_request_line_open_status_method_return__salesReturnRequestLineIsAlreadyOpen": "Sales return request line is already open", "@sage/xtrem-sales/enums__sales_return_request_line_open_status_method_return__salesReturnRequestLineIsNowOpen": "Sales return request line is now open", "@sage/xtrem-sales/enums__sales_return_request_line_open_status_method_return__salesReturnRequestLineIsReceived": "Sales return request line is received", "@sage/xtrem-sales/enums__sales_return_request_open_status_method_return__parametersAreIncorrect": "Parameters are incorrect", "@sage/xtrem-sales/enums__sales_return_request_open_status_method_return__salesReturnRequestIsAlreadyOpen": "Sales return request is already open", "@sage/xtrem-sales/enums__sales_return_request_open_status_method_return__salesReturnRequestIsNowOpen": "Sales return request is now open", "@sage/xtrem-sales/enums__sales_return_request_open_status_method_return__salesReturnRequestIsReceived": "Sales return request is received", "@sage/xtrem-sales/enums__sales_return_request_return_type__creditMemo": "Credit memo", "@sage/xtrem-sales/enums__sales_return_request_return_type__receiptAndCreditMemo": "Receipt and credit memo", "@sage/xtrem-sales/enums__sales_return_request_return_type__receiptAndNoCreditMemo": "Receipt and no credit memo", "@sage/xtrem-sales/enums__sales_return_request_status__closed": "Closed", "@sage/xtrem-sales/enums__sales_return_request_status__confirmed": "Confirmed", "@sage/xtrem-sales/enums__sales_return_request_status__draft": "Draft", "@sage/xtrem-sales/enums__sales_return_request_status__inProgress": "In progress", "@sage/xtrem-sales/enums__sales_return_request_status__pending": "Pending", "@sage/xtrem-sales/enums__sales_shipment_display_status__error": "Error", "@sage/xtrem-sales/enums__sales_shipment_display_status__invoiced": "Invoiced", "@sage/xtrem-sales/enums__sales_shipment_display_status__partiallyInvoiced": "Partially invoiced", "@sage/xtrem-sales/enums__sales_shipment_display_status__postingInProgress": "Posting in progress", "@sage/xtrem-sales/enums__sales_shipment_display_status__readyToProcess": "Ready to process", "@sage/xtrem-sales/enums__sales_shipment_display_status__readyToShip": "Ready to ship", "@sage/xtrem-sales/enums__sales_shipment_display_status__shipped": "Shipped", "@sage/xtrem-sales/enums__sales_shipment_status__readyToProcess": "Ready to process", "@sage/xtrem-sales/enums__sales_shipment_status__readyToShip": "Ready to ship", "@sage/xtrem-sales/enums__sales_shipment_status__shipped": "Shipped", "@sage/xtrem-sales/enums__unbilled_account_receivable_status__completed": "Completed", "@sage/xtrem-sales/enums__unbilled_account_receivable_status__draft": "Draft", "@sage/xtrem-sales/enums__unbilled_account_receivable_status__error": "Error", "@sage/xtrem-sales/enums__unbilled_account_receivable_status__inProgress": "In progress", "@sage/xtrem-sales/functions__sales_invoice_lib__error_invoice_cannot_be_printed": "You need to resolve errors to print the document: {{salesDocumentNumber}}.", "@sage/xtrem-sales/functions__sales_invoice_lib__in_progress_invoice_cannot_be_printed": "You cannot print the document. It is set to In progress: {{salesDocumentNumber}}.", "@sage/xtrem-sales/functions__sales_invoice_lib__tax_calculation_failed": "You need to resolve tax calculation issues to print the document: {{salesDocumentNumber}}.", "@sage/xtrem-sales/functions__sales_return_request__approval_email_subject": "[Sales return request {{salesReturnRequestNumber}}] approval request", "@sage/xtrem-sales/functions__send__email__no_default_or_substitute_approver": "There is no default or substitute approver for the current document.", "@sage/xtrem-sales/functions__send__email__not_in_submission_for_approval_not_allowed": "Submission for approval action not allowed. The sales return request is not in draft status.", "@sage/xtrem-sales/menu_item__sales-inquiries": "Sales inquiries", "@sage/xtrem-sales/node__sales_credit_memo__resend_notification_for_finance": "Resending finance notification for sales credit memo: {{salesCreditMemoNumber}}", "@sage/xtrem-sales/node__sales_credit_memo_bulk_print_report_name": "Sales credit memo", "@sage/xtrem-sales/node__sales_invoice__resend_notification_for_finance": "Resending finance notification for sales invoice number {{invoiceNumber}}", "@sage/xtrem-sales/node__sales_invoice_bulk_print_report_name": "Sales invoice", "@sage/xtrem-sales/node__sales_return_receipt__resend_notification_for_finance": "Resending finance notification for sales return receipt: {{salesReturnReceiptNumber}}.", "@sage/xtrem-sales/node__sales_shipment__resend_notification_for_finance": "Resending finance notification for sales shipment: {{shipmentNumber}}", "@sage/xtrem-sales/node__sales_shipment_bulk_print_pick_list_report_name": "Sales shipment pick list", "@sage/xtrem-sales/node__sales_shipment_bulk_print_report_name": "Sales shipment packing slip", "@sage/xtrem-sales/node-extensions__site_extension__property__isSalesReturnRequestApprovalManaged": "Is sales return request approval managed", "@sage/xtrem-sales/node-extensions__site_extension__property__salesReturnRequestDefaultApprover": "Sales return request default approver", "@sage/xtrem-sales/node-extensions__site_extension__property__salesReturnRequestSubstituteApprover": "Sales return request substitute approver", "@sage/xtrem-sales/nodes__base_line_to_sales_document_line__node_name": "Base line to sales document line", "@sage/xtrem-sales/nodes__base_line_to_sales_document_line__property__amount": "Amount", "@sage/xtrem-sales/nodes__base_line_to_sales_document_line__property__quantity": "Quantity", "@sage/xtrem-sales/nodes__base_line_to_sales_document_line__property__quantityInSalesUnit": "Quantity in sales unit", "@sage/xtrem-sales/nodes__base_line_to_sales_document_line__property__quantityInStockUnit": "Quantity in stock unit", "@sage/xtrem-sales/nodes__item-site-cost__failed_deletion_impossible_if_documents": "Delete not allowed. Sales documents exist for this item-site.", "@sage/xtrem-sales/nodes__proforma_invoice__asyncMutation__asyncExport": "Export", "@sage/xtrem-sales/nodes__proforma_invoice__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-sales/nodes__proforma_invoice__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-sales/nodes__proforma_invoice__cannot_print_inactive_proforma_invoice": "Could not print an inactive proforma invoice.", "@sage/xtrem-sales/nodes__proforma_invoice__cannot_print_proforma_invoice": "Could not print proforma invoice.", "@sage/xtrem-sales/nodes__proforma_invoice__mutation__afterPrintProformaInvoice": "After print proforma invoice", "@sage/xtrem-sales/nodes__proforma_invoice__mutation__afterPrintProformaInvoice__failed": "After print proforma invoice failed.", "@sage/xtrem-sales/nodes__proforma_invoice__mutation__afterPrintProformaInvoice__parameter__error": "Error", "@sage/xtrem-sales/nodes__proforma_invoice__mutation__afterPrintProformaInvoice__parameter__salesOrder": "Sales order", "@sage/xtrem-sales/nodes__proforma_invoice__mutation__afterPrintProformaInvoice__parameter__uploadedFile": "Uploaded file", "@sage/xtrem-sales/nodes__proforma_invoice__mutation__beforePrintProformaInvoice": "Before print proforma invoice", "@sage/xtrem-sales/nodes__proforma_invoice__mutation__beforePrintProformaInvoice__failed": "Before print proforma invoice failed.", "@sage/xtrem-sales/nodes__proforma_invoice__mutation__beforePrintProformaInvoice__parameter__salesOrder": "Sales order", "@sage/xtrem-sales/nodes__proforma_invoice__node_name": "Proforma invoice", "@sage/xtrem-sales/nodes__proforma_invoice__property__createdBy": "Created by", "@sage/xtrem-sales/nodes__proforma_invoice__property__customerComment": "Customer comment", "@sage/xtrem-sales/nodes__proforma_invoice__property__expirationDate": "Expiration date", "@sage/xtrem-sales/nodes__proforma_invoice__property__isActive": "Is active", "@sage/xtrem-sales/nodes__proforma_invoice__property__isExpired": "Is expired", "@sage/xtrem-sales/nodes__proforma_invoice__property__isLinkExpired": "Is link expired", "@sage/xtrem-sales/nodes__proforma_invoice__property__isSent": "Is sent", "@sage/xtrem-sales/nodes__proforma_invoice__property__issueDate": "Issue date", "@sage/xtrem-sales/nodes__proforma_invoice__property__salesOrder": "Sales order", "@sage/xtrem-sales/nodes__proforma_invoice__property__uploadedFile": "Uploaded file", "@sage/xtrem-sales/nodes__proforma_invoice__property__version": "Version", "@sage/xtrem-sales/nodes__proforma_invoice_expiration_date_cant_be_earlier_than_today": "The expiration date needs to be on or after the current date.", "@sage/xtrem-sales/nodes__proforma_invoice_only_last_version_is_active": "Only the last version can be active.", "@sage/xtrem-sales/nodes__sales_credit_memo__asyncMutation__asyncExport": "Export", "@sage/xtrem-sales/nodes__sales_credit_memo__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-sales/nodes__sales_credit_memo__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-sales/nodes__sales_credit_memo__bulkMutation__printBulkSalesCreditMemo": "Print bulk sales credit memo", "@sage/xtrem-sales/nodes__sales_credit_memo__bulkMutation__printBulkSalesCreditMemo__failed": "Print bulk sales credit memo failed.", "@sage/xtrem-sales/nodes__sales_credit_memo__cant_repost_sales_credit_memo_when_status_is_not_failed": "You can only repost a sales credit memo if the status is 'Failed' or 'Not recorded'.", "@sage/xtrem-sales/nodes__sales_credit_memo__deletion_forbidden_posted": "Deletion is not allowed. The sales credit memo is posted.", "@sage/xtrem-sales/nodes__sales_credit_memo__document_was_posted": "The sales credit memo was posted.", "@sage/xtrem-sales/nodes__sales_credit_memo__future_credit_memo_date_not_allowed": "The credit memo date cannot be later than today.", "@sage/xtrem-sales/nodes__sales_credit_memo__fx_rate_not_found": "No exchange rate found.", "@sage/xtrem-sales/nodes__sales_credit_memo__mutation__afterPrintSalesCreditMemo": "After print sales credit memo", "@sage/xtrem-sales/nodes__sales_credit_memo__mutation__afterPrintSalesCreditMemo__failed": "After print sales credit memo failed.", "@sage/xtrem-sales/nodes__sales_credit_memo__mutation__afterPrintSalesCreditMemo__parameter__creditMemo": "Credit memo", "@sage/xtrem-sales/nodes__sales_credit_memo__mutation__beforePrint": "Before print", "@sage/xtrem-sales/nodes__sales_credit_memo__mutation__beforePrint__failed": "Before print failed.", "@sage/xtrem-sales/nodes__sales_credit_memo__mutation__beforePrint__parameter__creditMemo": "Credit memo", "@sage/xtrem-sales/nodes__sales_credit_memo__mutation__beforePrintSalesCreditMemo": "Before print sales credit memo", "@sage/xtrem-sales/nodes__sales_credit_memo__mutation__beforePrintSalesCreditMemo__failed": "Before print sales credit memo failed.", "@sage/xtrem-sales/nodes__sales_credit_memo__mutation__beforePrintSalesCreditMemo__parameter__creditMemo": "Credit memo", "@sage/xtrem-sales/nodes__sales_credit_memo__mutation__enforceStatusPosted": "Enforce status posted", "@sage/xtrem-sales/nodes__sales_credit_memo__mutation__enforceStatusPosted__failed": "Enforce status posted failed.", "@sage/xtrem-sales/nodes__sales_credit_memo__mutation__enforceStatusPosted__parameter__creditMemo": "Credit memo", "@sage/xtrem-sales/nodes__sales_credit_memo__mutation__financeIntegrationCheck": "Finance integration check", "@sage/xtrem-sales/nodes__sales_credit_memo__mutation__financeIntegrationCheck__failed": "Finance integration check failed.", "@sage/xtrem-sales/nodes__sales_credit_memo__mutation__financeIntegrationCheck__parameter__creditMemo": "Credit memo", "@sage/xtrem-sales/nodes__sales_credit_memo__mutation__post": "Post", "@sage/xtrem-sales/nodes__sales_credit_memo__mutation__post__failed": "Post failed.", "@sage/xtrem-sales/nodes__sales_credit_memo__mutation__post__parameter__creditMemo": "Credit memo", "@sage/xtrem-sales/nodes__sales_credit_memo__mutation__printSalesCreditMemoAndEmail": "Print sales credit memo and email", "@sage/xtrem-sales/nodes__sales_credit_memo__mutation__printSalesCreditMemoAndEmail__failed": "Print sales credit memo and email failed.", "@sage/xtrem-sales/nodes__sales_credit_memo__mutation__printSalesCreditMemoAndEmail__parameter__contactEmail": "Contact email", "@sage/xtrem-sales/nodes__sales_credit_memo__mutation__printSalesCreditMemoAndEmail__parameter__contactFirstName": "Contact first name", "@sage/xtrem-sales/nodes__sales_credit_memo__mutation__printSalesCreditMemoAndEmail__parameter__contactLastName": "Contact last name", "@sage/xtrem-sales/nodes__sales_credit_memo__mutation__printSalesCreditMemoAndEmail__parameter__contactTitle": "Contact title", "@sage/xtrem-sales/nodes__sales_credit_memo__mutation__printSalesCreditMemoAndEmail__parameter__creditMemo": "Credit memo", "@sage/xtrem-sales/nodes__sales_credit_memo__mutation__repost": "Repost", "@sage/xtrem-sales/nodes__sales_credit_memo__mutation__repost__failed": "Repost failed.", "@sage/xtrem-sales/nodes__sales_credit_memo__mutation__repost__parameter__documentData": "Document data", "@sage/xtrem-sales/nodes__sales_credit_memo__mutation__repost__parameter__salesCreditMemo": "Sales credit memo", "@sage/xtrem-sales/nodes__sales_credit_memo__mutation__resendNotificationForFinance": "Resend notification for finance", "@sage/xtrem-sales/nodes__sales_credit_memo__mutation__resendNotificationForFinance__failed": "Resend notification for finance failed.", "@sage/xtrem-sales/nodes__sales_credit_memo__mutation__resendNotificationForFinance__parameter__salesCreditMemo": "Sales credit memo", "@sage/xtrem-sales/nodes__sales_credit_memo__mutation__setIsPrintedTrue": "Set is printed true", "@sage/xtrem-sales/nodes__sales_credit_memo__mutation__setIsPrintedTrue__failed": "Set is printed true failed.", "@sage/xtrem-sales/nodes__sales_credit_memo__mutation__setIsPrintedTrue__parameter__creditMemo": "Credit memo", "@sage/xtrem-sales/nodes__sales_credit_memo__new_credit_memo_date_not_in_right_period": "The new credit memo date must be in the same period.", "@sage/xtrem-sales/nodes__sales_credit_memo__node_name": "Sales credit memo", "@sage/xtrem-sales/nodes__sales_credit_memo__nothing_to_send": "The sales credit memo cannot be sent. No report has been created.", "@sage/xtrem-sales/nodes__sales_credit_memo__posted": "The sales credit memo has been posted.", "@sage/xtrem-sales/nodes__sales_credit_memo__property__arOpenItems": "Ar open items", "@sage/xtrem-sales/nodes__sales_credit_memo__property__billToAddress": "Bill to address", "@sage/xtrem-sales/nodes__sales_credit_memo__property__billToContact": "Bill to contact", "@sage/xtrem-sales/nodes__sales_credit_memo__property__billToCustomer": "Bill to customer", "@sage/xtrem-sales/nodes__sales_credit_memo__property__billToCustomerName": "Bill to customer name", "@sage/xtrem-sales/nodes__sales_credit_memo__property__billToCustomerTaxIdNumber": "Bill to customer tax id number", "@sage/xtrem-sales/nodes__sales_credit_memo__property__billToLinkedAddress": "Bill to linked address", "@sage/xtrem-sales/nodes__sales_credit_memo__property__canUpdateIsSent": "Can update is sent", "@sage/xtrem-sales/nodes__sales_credit_memo__property__companyAmountPaid": "Company amount paid", "@sage/xtrem-sales/nodes__sales_credit_memo__property__companyFxRate": "Company fx rate", "@sage/xtrem-sales/nodes__sales_credit_memo__property__companyFxRateDivisor": "Company fx rate divisor", "@sage/xtrem-sales/nodes__sales_credit_memo__property__creationNumber": "Creation number", "@sage/xtrem-sales/nodes__sales_credit_memo__property__currency": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-sales/nodes__sales_credit_memo__property__discountPaymentAmount": "Discount payment amount", "@sage/xtrem-sales/nodes__sales_credit_memo__property__discountPaymentBeforeDate": "Discount payment before date", "@sage/xtrem-sales/nodes__sales_credit_memo__property__discountPaymentType": "Discount payment type", "@sage/xtrem-sales/nodes__sales_credit_memo__property__displayStatus": "Display status", "@sage/xtrem-sales/nodes__sales_credit_memo__property__documentDate": "Document date", "@sage/xtrem-sales/nodes__sales_credit_memo__property__dueDate": "Due date", "@sage/xtrem-sales/nodes__sales_credit_memo__property__externalNote": "External note", "@sage/xtrem-sales/nodes__sales_credit_memo__property__financeIntegrationApp": "Finance integration app", "@sage/xtrem-sales/nodes__sales_credit_memo__property__financeTransactions": "Finance transactions", "@sage/xtrem-sales/nodes__sales_credit_memo__property__financialSiteAmountPaid": "Financial site amount paid", "@sage/xtrem-sales/nodes__sales_credit_memo__property__forcedAmountPaid": "Forced amount paid", "@sage/xtrem-sales/nodes__sales_credit_memo__property__fxRateDate": "Fx rate date", "@sage/xtrem-sales/nodes__sales_credit_memo__property__incoterm": "Incoterm", "@sage/xtrem-sales/nodes__sales_credit_memo__property__internalNote": "Internal note", "@sage/xtrem-sales/nodes__sales_credit_memo__property__isExternalNote": "Is external note", "@sage/xtrem-sales/nodes__sales_credit_memo__property__isOpenItemPageOptionActive": "Is open item page option active", "@sage/xtrem-sales/nodes__sales_credit_memo__property__lines": "Lines", "@sage/xtrem-sales/nodes__sales_credit_memo__property__netBalance": "Net balance", "@sage/xtrem-sales/nodes__sales_credit_memo__property__openItemSysId": "Open item sys id", "@sage/xtrem-sales/nodes__sales_credit_memo__property__paymentStatus": "Payment status", "@sage/xtrem-sales/nodes__sales_credit_memo__property__paymentTerm": "Payment term", "@sage/xtrem-sales/nodes__sales_credit_memo__property__penaltyPaymentAmount": "Penalty payment amount", "@sage/xtrem-sales/nodes__sales_credit_memo__property__penaltyPaymentType": "Penalty payment type", "@sage/xtrem-sales/nodes__sales_credit_memo__property__postingDetails": "Posting details", "@sage/xtrem-sales/nodes__sales_credit_memo__property__rateDescription": "Rate description", "@sage/xtrem-sales/nodes__sales_credit_memo__property__reason": "Reason", "@sage/xtrem-sales/nodes__sales_credit_memo__property__receipts": "Receipts", "@sage/xtrem-sales/nodes__sales_credit_memo__property__salesSite": "Sales site", "@sage/xtrem-sales/nodes__sales_credit_memo__property__salesSiteAddress": "Sales site address", "@sage/xtrem-sales/nodes__sales_credit_memo__property__salesSiteContact": "Sales site contact", "@sage/xtrem-sales/nodes__sales_credit_memo__property__salesSiteLinkedAddress": "Sales site linked address", "@sage/xtrem-sales/nodes__sales_credit_memo__property__salesSiteName": "Sales site name", "@sage/xtrem-sales/nodes__sales_credit_memo__property__salesSiteTaxIdNumber": "Sales site tax id number", "@sage/xtrem-sales/nodes__sales_credit_memo__property__site": "Site", "@sage/xtrem-sales/nodes__sales_credit_memo__property__status": "Status", "@sage/xtrem-sales/nodes__sales_credit_memo__property__taxCalculationStatus": "Tax calculation status", "@sage/xtrem-sales/nodes__sales_credit_memo__property__taxEngine": "Tax engine", "@sage/xtrem-sales/nodes__sales_credit_memo__property__taxes": "Taxes", "@sage/xtrem-sales/nodes__sales_credit_memo__property__totalAmountExcludingTax": "Total amount excluding tax", "@sage/xtrem-sales/nodes__sales_credit_memo__property__totalAmountExcludingTaxInCompanyCurrency": "Total amount excluding tax in company currency", "@sage/xtrem-sales/nodes__sales_credit_memo__property__totalAmountIncludingTax": "Total amount including tax", "@sage/xtrem-sales/nodes__sales_credit_memo__property__totalAmountIncludingTaxInCompanyCurrency": "Total amount including tax in company currency", "@sage/xtrem-sales/nodes__sales_credit_memo__property__totalAmountPaid": "Total amount paid", "@sage/xtrem-sales/nodes__sales_credit_memo__property__totalExemptAmount": "Total exempt amount", "@sage/xtrem-sales/nodes__sales_credit_memo__property__totalGrossProfit": "Total gross profit", "@sage/xtrem-sales/nodes__sales_credit_memo__property__totalTaxableAmount": "Total taxable amount", "@sage/xtrem-sales/nodes__sales_credit_memo__property__totalTaxAmount": "Total tax amount", "@sage/xtrem-sales/nodes__sales_credit_memo__property__totalTaxAmountAdjusted": "Total tax amount adjusted", "@sage/xtrem-sales/nodes__sales_credit_memo__property__transactionAmountPaid": "Transaction amount paid", "@sage/xtrem-sales/nodes__sales_credit_memo__update_not_allowed_status_posted": "The sales credit memo is posted. You cannot update it.", "@sage/xtrem-sales/nodes__sales_credit_memo_line__asyncMutation__asyncExport": "Export", "@sage/xtrem-sales/nodes__sales_credit_memo_line__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-sales/nodes__sales_credit_memo_line__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-sales/nodes__sales_credit_memo_line__deletion_forbidden_posted": "Line deletion is not allowed. The sales credit memo is posted.", "@sage/xtrem-sales/nodes__sales_credit_memo_line__improper_status_during_creation": "You can only add a line if the status of the sales credit memo is set to Draft.", "@sage/xtrem-sales/nodes__sales_credit_memo_line__mutation__calculateLineTaxes": "Calculate line taxes", "@sage/xtrem-sales/nodes__sales_credit_memo_line__mutation__calculateLineTaxes__failed": "Calculate line taxes failed.", "@sage/xtrem-sales/nodes__sales_credit_memo_line__mutation__calculateLineTaxes__parameter__data": "Data", "@sage/xtrem-sales/nodes__sales_credit_memo_line__mutation__getTaxDetermination": "Get tax determination", "@sage/xtrem-sales/nodes__sales_credit_memo_line__mutation__getTaxDetermination__failed": "Get tax determination failed.", "@sage/xtrem-sales/nodes__sales_credit_memo_line__mutation__getTaxDetermination__parameter__creditMemoLine": "Credit memo line", "@sage/xtrem-sales/nodes__sales_credit_memo_line__node_name": "Sales credit memo line", "@sage/xtrem-sales/nodes__sales_credit_memo_line__property__amountExcludingTax": "Amount excluding tax", "@sage/xtrem-sales/nodes__sales_credit_memo_line__property__amountExcludingTaxInCompanyCurrency": "Amount excluding tax in company currency", "@sage/xtrem-sales/nodes__sales_credit_memo_line__property__amountIncludingTax": "Amount including tax", "@sage/xtrem-sales/nodes__sales_credit_memo_line__property__amountIncludingTaxInCompanyCurrency": "Amount including tax in company currency", "@sage/xtrem-sales/nodes__sales_credit_memo_line__property__charge": "Charge", "@sage/xtrem-sales/nodes__sales_credit_memo_line__property__chargeDeterminated": "Charge determinated", "@sage/xtrem-sales/nodes__sales_credit_memo_line__property__computedAttributes": "Computed attributes", "@sage/xtrem-sales/nodes__sales_credit_memo_line__property__consumptionAddress": "Consumption address", "@sage/xtrem-sales/nodes__sales_credit_memo_line__property__consumptionLinkedAddress": "Consumption linked address", "@sage/xtrem-sales/nodes__sales_credit_memo_line__property__currency": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-sales/nodes__sales_credit_memo_line__property__discount": "Discount", "@sage/xtrem-sales/nodes__sales_credit_memo_line__property__discountCharges": "Discount charges", "@sage/xtrem-sales/nodes__sales_credit_memo_line__property__discountDeterminated": "Discount determinated", "@sage/xtrem-sales/nodes__sales_credit_memo_line__property__document": "Document", "@sage/xtrem-sales/nodes__sales_credit_memo_line__property__documentId": "Document id", "@sage/xtrem-sales/nodes__sales_credit_memo_line__property__documentNumber": "Document number", "@sage/xtrem-sales/nodes__sales_credit_memo_line__property__exemptAmount": "Exempt amount", "@sage/xtrem-sales/nodes__sales_credit_memo_line__property__externalNote": "External note", "@sage/xtrem-sales/nodes__sales_credit_memo_line__property__grossPrice": "Gross price", "@sage/xtrem-sales/nodes__sales_credit_memo_line__property__grossPriceDeterminated": "Gross price determinated", "@sage/xtrem-sales/nodes__sales_credit_memo_line__property__grossProfit": "Gross profit", "@sage/xtrem-sales/nodes__sales_credit_memo_line__property__grossProfitAmount": "Gross profit amount", "@sage/xtrem-sales/nodes__sales_credit_memo_line__property__grossProfitAmountInCompanyCurrency": "Gross profit amount in company currency", "@sage/xtrem-sales/nodes__sales_credit_memo_line__property__internalNote": "Internal note", "@sage/xtrem-sales/nodes__sales_credit_memo_line__property__isExternalNote": "Is external note", "@sage/xtrem-sales/nodes__sales_credit_memo_line__property__isPriceDeterminated": "Is price determinated", "@sage/xtrem-sales/nodes__sales_credit_memo_line__property__item": "<PERSON><PERSON>", "@sage/xtrem-sales/nodes__sales_credit_memo_line__property__netPrice": "Net price", "@sage/xtrem-sales/nodes__sales_credit_memo_line__property__netPriceExcludingTax": "Net price excluding tax", "@sage/xtrem-sales/nodes__sales_credit_memo_line__property__netPriceIncludingTax": "Net price including tax", "@sage/xtrem-sales/nodes__sales_credit_memo_line__property__originDocumentType": "Origin document type", "@sage/xtrem-sales/nodes__sales_credit_memo_line__property__priceOrigin": "Price origin", "@sage/xtrem-sales/nodes__sales_credit_memo_line__property__priceOriginDeterminated": "Price origin determinated", "@sage/xtrem-sales/nodes__sales_credit_memo_line__property__priceReason": "Price reason", "@sage/xtrem-sales/nodes__sales_credit_memo_line__property__priceReasonDeterminated": "Price reason determinated", "@sage/xtrem-sales/nodes__sales_credit_memo_line__property__providerSite": "Provider site", "@sage/xtrem-sales/nodes__sales_credit_memo_line__property__providerSiteAddress": "Provider site address", "@sage/xtrem-sales/nodes__sales_credit_memo_line__property__providerSiteLinkedAddress": "Provider site linked address", "@sage/xtrem-sales/nodes__sales_credit_memo_line__property__quantityInSalesUnit": "Quantity in sales unit", "@sage/xtrem-sales/nodes__sales_credit_memo_line__property__quantityInStockUnit": "Quantity in stock unit", "@sage/xtrem-sales/nodes__sales_credit_memo_line__property__salesSite": "Sales site", "@sage/xtrem-sales/nodes__sales_credit_memo_line__property__salesUnit": "Sales unit", "@sage/xtrem-sales/nodes__sales_credit_memo_line__property__salesUnitToStockUnitConversionFactor": "Sales unit to stock unit conversion factor", "@sage/xtrem-sales/nodes__sales_credit_memo_line__property__site": "Site", "@sage/xtrem-sales/nodes__sales_credit_memo_line__property__sourceDocumentNumber": "Source document number", "@sage/xtrem-sales/nodes__sales_credit_memo_line__property__sourceDocumentSysId": "Source document sys id", "@sage/xtrem-sales/nodes__sales_credit_memo_line__property__sourceDocumentType": "Source document type", "@sage/xtrem-sales/nodes__sales_credit_memo_line__property__stockCostAmount": "Stock cost amount", "@sage/xtrem-sales/nodes__sales_credit_memo_line__property__stockCostAmountInCompanyCurrency": "Stock cost amount in company currency", "@sage/xtrem-sales/nodes__sales_credit_memo_line__property__stockCostUnit": "Stock cost unit", "@sage/xtrem-sales/nodes__sales_credit_memo_line__property__stockUnit": "Stock unit", "@sage/xtrem-sales/nodes__sales_credit_memo_line__property__taxableAmount": "Taxable amount", "@sage/xtrem-sales/nodes__sales_credit_memo_line__property__taxAmount": "Tax amount", "@sage/xtrem-sales/nodes__sales_credit_memo_line__property__taxAmountAdjusted": "Tax amount adjusted", "@sage/xtrem-sales/nodes__sales_credit_memo_line__property__taxCalculationStatus": "Tax calculation status", "@sage/xtrem-sales/nodes__sales_credit_memo_line__property__taxDate": "Tax date", "@sage/xtrem-sales/nodes__sales_credit_memo_line__property__taxes": "Taxes", "@sage/xtrem-sales/nodes__sales_credit_memo_line__property__toInvoiceLines": "To invoice lines", "@sage/xtrem-sales/nodes__sales_credit_memo_line__property__toReturnRequestLines": "To return request lines", "@sage/xtrem-sales/nodes__sales_credit_memo_line__property__uiTaxes": "Ui taxes", "@sage/xtrem-sales/nodes__sales_credit_memo_line__property__unit": "Unit", "@sage/xtrem-sales/nodes__sales_credit_memo_line__property__unitToStockUnitConversionFactor": "Unit to stock unit conversion factor", "@sage/xtrem-sales/nodes__sales_credit_memo_line__update_not_allowed_status_posted": "The sales credit memo is posted. You cannot update it.", "@sage/xtrem-sales/nodes__sales_credit_memo_line_discount_charge__asyncMutation__asyncExport": "Export", "@sage/xtrem-sales/nodes__sales_credit_memo_line_discount_charge__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-sales/nodes__sales_credit_memo_line_discount_charge__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-sales/nodes__sales_credit_memo_line_discount_charge__node_name": "Sales credit memo line discount charge", "@sage/xtrem-sales/nodes__sales_credit_memo_line_discount_charge__property__basis": "<PERSON><PERSON>", "@sage/xtrem-sales/nodes__sales_credit_memo_line_discount_charge__property__document": "Document", "@sage/xtrem-sales/nodes__sales_credit_memo_line_tax__asyncMutation__asyncExport": "Export", "@sage/xtrem-sales/nodes__sales_credit_memo_line_tax__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-sales/nodes__sales_credit_memo_line_tax__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-sales/nodes__sales_credit_memo_line_tax__node_name": "Sales credit memo line tax", "@sage/xtrem-sales/nodes__sales_credit_memo_line_tax__property__currency": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-sales/nodes__sales_credit_memo_line_tax__property__document": "Document", "@sage/xtrem-sales/nodes__sales_credit_memo_reason__asyncMutation__asyncExport": "Export", "@sage/xtrem-sales/nodes__sales_credit_memo_reason__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-sales/nodes__sales_credit_memo_reason__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-sales/nodes__sales_credit_memo_reason__node_name": "Sales credit memo reason", "@sage/xtrem-sales/nodes__sales_credit_memo_reason__property__description": "Description", "@sage/xtrem-sales/nodes__sales_credit_memo_reason__property__id": "Id", "@sage/xtrem-sales/nodes__sales_credit_memo_reason__property__isActive": "Is active", "@sage/xtrem-sales/nodes__sales_credit_memo_reason__property__name": "Name", "@sage/xtrem-sales/nodes__sales_credit_memo_tax__asyncMutation__asyncExport": "Export", "@sage/xtrem-sales/nodes__sales_credit_memo_tax__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-sales/nodes__sales_credit_memo_tax__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-sales/nodes__sales_credit_memo_tax__node_name": "Sales credit memo tax", "@sage/xtrem-sales/nodes__sales_credit_memo_tax__property__currency": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-sales/nodes__sales_credit_memo_tax__property__document": "Document", "@sage/xtrem-sales/nodes__sales_document__update_to_status_closed_not_allowed": "You cannot update the document status to Posted.", "@sage/xtrem-sales/nodes__sales_invoice__asyncMutation__asyncExport": "Export", "@sage/xtrem-sales/nodes__sales_invoice__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-sales/nodes__sales_invoice__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-sales/nodes__sales_invoice__asyncMutation__postInvoice": "Post invoice", "@sage/xtrem-sales/nodes__sales_invoice__asyncMutation__postInvoice__failed": "Post invoice failed.", "@sage/xtrem-sales/nodes__sales_invoice__asyncMutation__postInvoice__parameter__invoice": "Invoice", "@sage/xtrem-sales/nodes__sales_invoice__asyncMutation__postInvoice__parameter__isSafeToRetry": "Is safe to retry", "@sage/xtrem-sales/nodes__sales_invoice__bulkMutation__printBulkSalesInvoice": "Print bulk sales invoice", "@sage/xtrem-sales/nodes__sales_invoice__bulkMutation__printBulkSalesInvoice__failed": "Print bulk sales invoice failed.", "@sage/xtrem-sales/nodes__sales_invoice__cannot_print": "The sales invoice cannot be printed.", "@sage/xtrem-sales/nodes__sales_invoice__deletion_forbidden_posted": "Deletion is not allowed. The sales invoice is posted.", "@sage/xtrem-sales/nodes__sales_invoice__future_invoice_date_not_allowed": "The invoice date cannot be later than today.", "@sage/xtrem-sales/nodes__sales_invoice__fx_rate_not_found": "No exchange rate found.", "@sage/xtrem-sales/nodes__sales_invoice__lines_mandatory": "The sales invoice must contain at least one line.", "@sage/xtrem-sales/nodes__sales_invoice__mutation__afterPrint": "After print", "@sage/xtrem-sales/nodes__sales_invoice__mutation__afterPrint__failed": "After print failed.", "@sage/xtrem-sales/nodes__sales_invoice__mutation__afterPrint__parameter__invoice": "Invoice", "@sage/xtrem-sales/nodes__sales_invoice__mutation__beforePrint": "Before print", "@sage/xtrem-sales/nodes__sales_invoice__mutation__beforePrint__failed": "Before print failed.", "@sage/xtrem-sales/nodes__sales_invoice__mutation__beforePrint__parameter__invoice": "Invoice", "@sage/xtrem-sales/nodes__sales_invoice__mutation__createSalesCreditMemosFromInvoiceLines": "Create sales credit memos from invoice lines", "@sage/xtrem-sales/nodes__sales_invoice__mutation__createSalesCreditMemosFromInvoiceLines__failed": "Create sales credit memos from invoice lines failed.", "@sage/xtrem-sales/nodes__sales_invoice__mutation__createSalesCreditMemosFromInvoiceLines__parameter__creditMemoDate": "Credit memo date", "@sage/xtrem-sales/nodes__sales_invoice__mutation__createSalesCreditMemosFromInvoiceLines__parameter__salesDocumentLines": "Sales document lines", "@sage/xtrem-sales/nodes__sales_invoice__mutation__createSalesCreditMemosFromInvoices": "Create sales credit memos from invoices", "@sage/xtrem-sales/nodes__sales_invoice__mutation__createSalesCreditMemosFromInvoices__failed": "Create sales credit memos from invoices failed.", "@sage/xtrem-sales/nodes__sales_invoice__mutation__createSalesCreditMemosFromInvoices__parameter__creditMemoDate": "Credit memo date", "@sage/xtrem-sales/nodes__sales_invoice__mutation__createSalesCreditMemosFromInvoices__parameter__salesDocuments": "Sales documents", "@sage/xtrem-sales/nodes__sales_invoice__mutation__enforceStatusPosted": "Enforce status posted", "@sage/xtrem-sales/nodes__sales_invoice__mutation__enforceStatusPosted__failed": "Enforce status posted failed.", "@sage/xtrem-sales/nodes__sales_invoice__mutation__enforceStatusPosted__parameter__invoice": "Invoice", "@sage/xtrem-sales/nodes__sales_invoice__mutation__financeIntegrationCheck": "Finance integration check", "@sage/xtrem-sales/nodes__sales_invoice__mutation__financeIntegrationCheck__failed": "Finance integration check failed.", "@sage/xtrem-sales/nodes__sales_invoice__mutation__financeIntegrationCheck__parameter__invoice": "Invoice", "@sage/xtrem-sales/nodes__sales_invoice__mutation__massPriceDeterminationCalculation": "Mass price determination calculation", "@sage/xtrem-sales/nodes__sales_invoice__mutation__massPriceDeterminationCalculation__failed": "Mass price determination calculation failed.", "@sage/xtrem-sales/nodes__sales_invoice__mutation__massPriceDeterminationCalculation__parameter__salesDocument": "Sales document", "@sage/xtrem-sales/nodes__sales_invoice__mutation__post": "Post", "@sage/xtrem-sales/nodes__sales_invoice__mutation__post__failed": "Post failed.", "@sage/xtrem-sales/nodes__sales_invoice__mutation__post__parameter__invoice": "Invoice", "@sage/xtrem-sales/nodes__sales_invoice__mutation__post__parameter__isSafeToRetry": "Is safe to retry", "@sage/xtrem-sales/nodes__sales_invoice__mutation__printSalesInvoiceAndEmail": "Print sales invoice and email", "@sage/xtrem-sales/nodes__sales_invoice__mutation__printSalesInvoiceAndEmail__failed": "Print sales invoice and email failed.", "@sage/xtrem-sales/nodes__sales_invoice__mutation__printSalesInvoiceAndEmail__parameter__contactEmail": "Contact email", "@sage/xtrem-sales/nodes__sales_invoice__mutation__printSalesInvoiceAndEmail__parameter__contactFirstName": "Contact first name", "@sage/xtrem-sales/nodes__sales_invoice__mutation__printSalesInvoiceAndEmail__parameter__contactLastName": "Contact last name", "@sage/xtrem-sales/nodes__sales_invoice__mutation__printSalesInvoiceAndEmail__parameter__contactTitle": "Contact title", "@sage/xtrem-sales/nodes__sales_invoice__mutation__printSalesInvoiceAndEmail__parameter__invoice": "Invoice", "@sage/xtrem-sales/nodes__sales_invoice__mutation__repost": "Repost", "@sage/xtrem-sales/nodes__sales_invoice__mutation__repost__failed": "Repost failed.", "@sage/xtrem-sales/nodes__sales_invoice__mutation__repost__parameter__documentData": "Document data", "@sage/xtrem-sales/nodes__sales_invoice__mutation__repost__parameter__salesInvoice": "Sales invoice", "@sage/xtrem-sales/nodes__sales_invoice__mutation__resendNotificationForFinance": "Resend notification for finance", "@sage/xtrem-sales/nodes__sales_invoice__mutation__resendNotificationForFinance__failed": "Resend notification for finance failed.", "@sage/xtrem-sales/nodes__sales_invoice__mutation__resendNotificationForFinance__parameter__salesInvoice": "Sales invoice", "@sage/xtrem-sales/nodes__sales_invoice__new_invoice_date_not_in_right_period": "The new invoice date must be in the same period.", "@sage/xtrem-sales/nodes__sales_invoice__node_name": "Sales invoice", "@sage/xtrem-sales/nodes__sales_invoice__nothing_to_send": "The sales invoice cannot be sent. No report has been created.", "@sage/xtrem-sales/nodes__sales_invoice__property__arOpenItems": "Ar open items", "@sage/xtrem-sales/nodes__sales_invoice__property__billToAddress": "Bill to address", "@sage/xtrem-sales/nodes__sales_invoice__property__billToContact": "Bill to contact", "@sage/xtrem-sales/nodes__sales_invoice__property__billToCustomer": "Bill to customer", "@sage/xtrem-sales/nodes__sales_invoice__property__billToCustomerName": "Bill to customer name", "@sage/xtrem-sales/nodes__sales_invoice__property__billToCustomerTaxIdNumber": "Bill to customer tax id number", "@sage/xtrem-sales/nodes__sales_invoice__property__billToLinkedAddress": "Bill to linked address", "@sage/xtrem-sales/nodes__sales_invoice__property__companyAmountPaid": "Company amount paid", "@sage/xtrem-sales/nodes__sales_invoice__property__companyFxRate": "Company fx rate", "@sage/xtrem-sales/nodes__sales_invoice__property__companyFxRateDivisor": "Company fx rate divisor", "@sage/xtrem-sales/nodes__sales_invoice__property__creationNumber": "Creation number", "@sage/xtrem-sales/nodes__sales_invoice__property__creditStatus": "Credit status", "@sage/xtrem-sales/nodes__sales_invoice__property__currency": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-sales/nodes__sales_invoice__property__discountPaymentAmount": "Discount payment amount", "@sage/xtrem-sales/nodes__sales_invoice__property__discountPaymentBeforeDate": "Discount payment before date", "@sage/xtrem-sales/nodes__sales_invoice__property__discountPaymentType": "Discount payment type", "@sage/xtrem-sales/nodes__sales_invoice__property__displayStatus": "Display status", "@sage/xtrem-sales/nodes__sales_invoice__property__documentDate": "Document date", "@sage/xtrem-sales/nodes__sales_invoice__property__dueDate": "Due date", "@sage/xtrem-sales/nodes__sales_invoice__property__enablePrintButton": "Enable print button", "@sage/xtrem-sales/nodes__sales_invoice__property__externalNote": "External note", "@sage/xtrem-sales/nodes__sales_invoice__property__financeIntegrationApp": "Finance integration app", "@sage/xtrem-sales/nodes__sales_invoice__property__financeTransactions": "Finance transactions", "@sage/xtrem-sales/nodes__sales_invoice__property__financialSiteAmountPaid": "Financial site amount paid", "@sage/xtrem-sales/nodes__sales_invoice__property__forcedAmountPaid": "Forced amount paid", "@sage/xtrem-sales/nodes__sales_invoice__property__fxRateDate": "Fx rate date", "@sage/xtrem-sales/nodes__sales_invoice__property__incoterm": "Incoterm", "@sage/xtrem-sales/nodes__sales_invoice__property__internalNote": "Internal note", "@sage/xtrem-sales/nodes__sales_invoice__property__invoiceDate": "Invoice date", "@sage/xtrem-sales/nodes__sales_invoice__property__isExternalNote": "Is external note", "@sage/xtrem-sales/nodes__sales_invoice__property__isOpenItemPageOptionActive": "Is open item page option active", "@sage/xtrem-sales/nodes__sales_invoice__property__isTransferHeaderNote": "Is transfer header note", "@sage/xtrem-sales/nodes__sales_invoice__property__isTransferLineNote": "Is transfer line note", "@sage/xtrem-sales/nodes__sales_invoice__property__lines": "Lines", "@sage/xtrem-sales/nodes__sales_invoice__property__netBalance": "Net balance", "@sage/xtrem-sales/nodes__sales_invoice__property__openItemSysId": "Open item sys id", "@sage/xtrem-sales/nodes__sales_invoice__property__paymentStatus": "Payment status", "@sage/xtrem-sales/nodes__sales_invoice__property__paymentTerm": "Payment term", "@sage/xtrem-sales/nodes__sales_invoice__property__penaltyPaymentAmount": "Penalty payment amount", "@sage/xtrem-sales/nodes__sales_invoice__property__penaltyPaymentType": "Penalty payment type", "@sage/xtrem-sales/nodes__sales_invoice__property__postingDetails": "Posting details", "@sage/xtrem-sales/nodes__sales_invoice__property__rateDescription": "Rate description", "@sage/xtrem-sales/nodes__sales_invoice__property__receipts": "Receipts", "@sage/xtrem-sales/nodes__sales_invoice__property__salesSite": "Sales site", "@sage/xtrem-sales/nodes__sales_invoice__property__salesSiteAddress": "Sales site address", "@sage/xtrem-sales/nodes__sales_invoice__property__salesSiteContact": "Sales site contact", "@sage/xtrem-sales/nodes__sales_invoice__property__salesSiteLinkedAddress": "Sales site linked address", "@sage/xtrem-sales/nodes__sales_invoice__property__salesSiteName": "Sales site name", "@sage/xtrem-sales/nodes__sales_invoice__property__salesSiteTaxIdNumber": "Sales site tax id number", "@sage/xtrem-sales/nodes__sales_invoice__property__site": "Site", "@sage/xtrem-sales/nodes__sales_invoice__property__status": "Status", "@sage/xtrem-sales/nodes__sales_invoice__property__taxCalculationStatus": "Tax calculation status", "@sage/xtrem-sales/nodes__sales_invoice__property__taxEngine": "Tax engine", "@sage/xtrem-sales/nodes__sales_invoice__property__taxes": "Taxes", "@sage/xtrem-sales/nodes__sales_invoice__property__totalAmountExcludingTax": "Total amount excluding tax", "@sage/xtrem-sales/nodes__sales_invoice__property__totalAmountExcludingTaxInCompanyCurrency": "Total amount excluding tax in company currency", "@sage/xtrem-sales/nodes__sales_invoice__property__totalAmountIncludingTax": "Total amount including tax", "@sage/xtrem-sales/nodes__sales_invoice__property__totalAmountIncludingTaxInCompanyCurrency": "Total amount including tax in company currency", "@sage/xtrem-sales/nodes__sales_invoice__property__totalAmountPaid": "Total amount paid", "@sage/xtrem-sales/nodes__sales_invoice__property__totalExemptAmount": "Total exempt amount", "@sage/xtrem-sales/nodes__sales_invoice__property__totalGrossProfit": "Total gross profit", "@sage/xtrem-sales/nodes__sales_invoice__property__totalTaxableAmount": "Total taxable amount", "@sage/xtrem-sales/nodes__sales_invoice__property__totalTaxAmount": "Total tax amount", "@sage/xtrem-sales/nodes__sales_invoice__property__totalTaxAmountAdjusted": "Total tax amount adjusted", "@sage/xtrem-sales/nodes__sales_invoice__property__transactionAmountPaid": "Transaction amount paid", "@sage/xtrem-sales/nodes__sales_invoice__query__getCustomerYtdGross": "Get customer ytd gross", "@sage/xtrem-sales/nodes__sales_invoice__query__getCustomerYtdGross__failed": "Get customer ytd gross failed.", "@sage/xtrem-sales/nodes__sales_invoice__query__getCustomerYtdGross__parameter__customer": "Customer", "@sage/xtrem-sales/nodes__sales_invoice__query__getCustomerYtdSales": "Get customer ytd sales", "@sage/xtrem-sales/nodes__sales_invoice__query__getCustomerYtdSales__failed": "Get customer ytd sales failed.", "@sage/xtrem-sales/nodes__sales_invoice__query__getCustomerYtdSales__parameter__customer": "Customer", "@sage/xtrem-sales/nodes__sales_invoice__query__getDealSizeTrend": "Get deal size trend", "@sage/xtrem-sales/nodes__sales_invoice__query__getDealSizeTrend__failed": "Get deal size trend failed.", "@sage/xtrem-sales/nodes__sales_invoice__query__getDealSizeTrend__parameter__currency": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-sales/nodes__sales_invoice__query__getMtdSales": "Get mtd sales", "@sage/xtrem-sales/nodes__sales_invoice__query__getMtdSales__failed": "Get mtd sales failed.", "@sage/xtrem-sales/nodes__sales_invoice__query__getMtdSales__parameter__currency": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-sales/nodes__sales_invoice__query__getYtdSales": "Get ytd sales", "@sage/xtrem-sales/nodes__sales_invoice__query__getYtdSales__failed": "Get ytd sales failed.", "@sage/xtrem-sales/nodes__sales_invoice__query__getYtdSales__parameter__currency": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-sales/nodes__sales_invoice__update_not_allowed_status_posted": "The sales invoice is posted. You cannot update it.", "@sage/xtrem-sales/nodes__sales_invoice_line__asyncMutation__asyncExport": "Export", "@sage/xtrem-sales/nodes__sales_invoice_line__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-sales/nodes__sales_invoice_line__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-sales/nodes__sales_invoice_line__deletion_forbidden_posted": "Line deletion is not allowed. The sales invoice is posted.", "@sage/xtrem-sales/nodes__sales_invoice_line__deletion_forbidden_shipment_linked": "Line deletion is not allowed. The sales invoice line is linked to a sales shipment line.", "@sage/xtrem-sales/nodes__sales_invoice_line__improper_item_selection": "Incorrect item. The stock managed items are not allowed.", "@sage/xtrem-sales/nodes__sales_invoice_line__improper_status_during_creation": "Incorrect sales invoice status. The sales invoice must have the 'Draft' status to create the line.", "@sage/xtrem-sales/nodes__sales_invoice_line__item_update_not_allowed": "The item cannot be updated after the sales invoice has been created.", "@sage/xtrem-sales/nodes__sales_invoice_line__mutation__calculateLineTaxes": "Calculate line taxes", "@sage/xtrem-sales/nodes__sales_invoice_line__mutation__calculateLineTaxes__failed": "Calculate line taxes failed.", "@sage/xtrem-sales/nodes__sales_invoice_line__mutation__calculateLineTaxes__parameter__data": "Data", "@sage/xtrem-sales/nodes__sales_invoice_line__node_name": "Sales invoice line", "@sage/xtrem-sales/nodes__sales_invoice_line__property__amountExcludingTax": "Amount excluding tax", "@sage/xtrem-sales/nodes__sales_invoice_line__property__amountExcludingTaxInCompanyCurrency": "Amount excluding tax in company currency", "@sage/xtrem-sales/nodes__sales_invoice_line__property__amountIncludingTax": "Amount including tax", "@sage/xtrem-sales/nodes__sales_invoice_line__property__amountIncludingTaxInCompanyCurrency": "Amount including tax in company currency", "@sage/xtrem-sales/nodes__sales_invoice_line__property__charge": "Charge", "@sage/xtrem-sales/nodes__sales_invoice_line__property__chargeDeterminated": "Charge determinated", "@sage/xtrem-sales/nodes__sales_invoice_line__property__computedAttributes": "Computed attributes", "@sage/xtrem-sales/nodes__sales_invoice_line__property__consumptionAddress": "Consumption address", "@sage/xtrem-sales/nodes__sales_invoice_line__property__consumptionLinkedAddress": "Consumption linked address", "@sage/xtrem-sales/nodes__sales_invoice_line__property__creditStatus": "Credit status", "@sage/xtrem-sales/nodes__sales_invoice_line__property__currency": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-sales/nodes__sales_invoice_line__property__customerNumber": "Customer number", "@sage/xtrem-sales/nodes__sales_invoice_line__property__discount": "Discount", "@sage/xtrem-sales/nodes__sales_invoice_line__property__discountCharges": "Discount charges", "@sage/xtrem-sales/nodes__sales_invoice_line__property__discountDeterminated": "Discount determinated", "@sage/xtrem-sales/nodes__sales_invoice_line__property__document": "Document", "@sage/xtrem-sales/nodes__sales_invoice_line__property__documentId": "Document id", "@sage/xtrem-sales/nodes__sales_invoice_line__property__documentNumber": "Document number", "@sage/xtrem-sales/nodes__sales_invoice_line__property__exemptAmount": "Exempt amount", "@sage/xtrem-sales/nodes__sales_invoice_line__property__externalNote": "External note", "@sage/xtrem-sales/nodes__sales_invoice_line__property__grossPrice": "Gross price", "@sage/xtrem-sales/nodes__sales_invoice_line__property__grossPriceDeterminated": "Gross price determinated", "@sage/xtrem-sales/nodes__sales_invoice_line__property__grossProfit": "Gross profit", "@sage/xtrem-sales/nodes__sales_invoice_line__property__grossProfitAmount": "Gross profit amount", "@sage/xtrem-sales/nodes__sales_invoice_line__property__grossProfitAmountInCompanyCurrency": "Gross profit amount in company currency", "@sage/xtrem-sales/nodes__sales_invoice_line__property__internalNote": "Internal note", "@sage/xtrem-sales/nodes__sales_invoice_line__property__isExternalNote": "Is external note", "@sage/xtrem-sales/nodes__sales_invoice_line__property__isPriceDeterminated": "Is price determinated", "@sage/xtrem-sales/nodes__sales_invoice_line__property__item": "<PERSON><PERSON>", "@sage/xtrem-sales/nodes__sales_invoice_line__property__netPrice": "Net price", "@sage/xtrem-sales/nodes__sales_invoice_line__property__netPriceExcludingTax": "Net price excluding tax", "@sage/xtrem-sales/nodes__sales_invoice_line__property__netPriceIncludingTax": "Net price including tax", "@sage/xtrem-sales/nodes__sales_invoice_line__property__originDocumentType": "Origin document type", "@sage/xtrem-sales/nodes__sales_invoice_line__property__priceOrigin": "Price origin", "@sage/xtrem-sales/nodes__sales_invoice_line__property__priceOriginDeterminated": "Price origin determinated", "@sage/xtrem-sales/nodes__sales_invoice_line__property__priceReason": "Price reason", "@sage/xtrem-sales/nodes__sales_invoice_line__property__priceReasonDeterminated": "Price reason determinated", "@sage/xtrem-sales/nodes__sales_invoice_line__property__providerSite": "Provider site", "@sage/xtrem-sales/nodes__sales_invoice_line__property__providerSiteAddress": "Provider site address", "@sage/xtrem-sales/nodes__sales_invoice_line__property__providerSiteLinkedAddress": "Provider site linked address", "@sage/xtrem-sales/nodes__sales_invoice_line__property__quantity": "Quantity", "@sage/xtrem-sales/nodes__sales_invoice_line__property__quantityCreditedInProgressInSalesUnit": "Quantity credited in progress in sales unit", "@sage/xtrem-sales/nodes__sales_invoice_line__property__quantityCreditedPostedInSalesUnit": "Quantity credited posted in sales unit", "@sage/xtrem-sales/nodes__sales_invoice_line__property__quantityInSalesUnit": "Quantity in sales unit", "@sage/xtrem-sales/nodes__sales_invoice_line__property__quantityInStockUnit": "Quantity in stock unit", "@sage/xtrem-sales/nodes__sales_invoice_line__property__salesCreditMemoLines": "Sales credit memo lines", "@sage/xtrem-sales/nodes__sales_invoice_line__property__salesOrderLines": "Sales order lines", "@sage/xtrem-sales/nodes__sales_invoice_line__property__salesShipmentDocument": "Sales shipment document", "@sage/xtrem-sales/nodes__sales_invoice_line__property__salesShipmentLines": "Sales shipment lines", "@sage/xtrem-sales/nodes__sales_invoice_line__property__salesSite": "Sales site", "@sage/xtrem-sales/nodes__sales_invoice_line__property__salesUnit": "Sales unit", "@sage/xtrem-sales/nodes__sales_invoice_line__property__salesUnitToStockUnitConversionFactor": "Sales unit to stock unit conversion factor", "@sage/xtrem-sales/nodes__sales_invoice_line__property__site": "Site", "@sage/xtrem-sales/nodes__sales_invoice_line__property__sourceDocumentNumber": "Source document number", "@sage/xtrem-sales/nodes__sales_invoice_line__property__sourceDocumentSysId": "Source document sys id", "@sage/xtrem-sales/nodes__sales_invoice_line__property__sourceDocumentType": "Source document type", "@sage/xtrem-sales/nodes__sales_invoice_line__property__stockCostAmount": "Stock cost amount", "@sage/xtrem-sales/nodes__sales_invoice_line__property__stockCostAmountInCompanyCurrency": "Stock cost amount in company currency", "@sage/xtrem-sales/nodes__sales_invoice_line__property__stockCostUnit": "Stock cost unit", "@sage/xtrem-sales/nodes__sales_invoice_line__property__stockUnit": "Stock unit", "@sage/xtrem-sales/nodes__sales_invoice_line__property__storedAttributes": "Stored attributes", "@sage/xtrem-sales/nodes__sales_invoice_line__property__storedDimensions": "Stored dimensions", "@sage/xtrem-sales/nodes__sales_invoice_line__property__taxableAmount": "Taxable amount", "@sage/xtrem-sales/nodes__sales_invoice_line__property__taxAmount": "Tax amount", "@sage/xtrem-sales/nodes__sales_invoice_line__property__taxAmountAdjusted": "Tax amount adjusted", "@sage/xtrem-sales/nodes__sales_invoice_line__property__taxCalculationStatus": "Tax calculation status", "@sage/xtrem-sales/nodes__sales_invoice_line__property__taxDate": "Tax date", "@sage/xtrem-sales/nodes__sales_invoice_line__property__taxes": "Taxes", "@sage/xtrem-sales/nodes__sales_invoice_line__property__uiTaxes": "Ui taxes", "@sage/xtrem-sales/nodes__sales_invoice_line__property__unit": "Unit", "@sage/xtrem-sales/nodes__sales_invoice_line__property__unitToStockUnitConversionFactor": "Unit to stock unit conversion factor", "@sage/xtrem-sales/nodes__sales_invoice_line__sales_unit_update_not_allowed": "The sales unit cannot be updated after the sales invoice has been created.", "@sage/xtrem-sales/nodes__sales_invoice_line__update_forbidden_shipment_linked": "Quantity update is not allowed. The sales invoice line is linked to a sales shipment line.", "@sage/xtrem-sales/nodes__sales_invoice_line__update_not_allowed_status_posted": "The sales invoice is posted. You cannot update it.", "@sage/xtrem-sales/nodes__sales_invoice_line_discount_charge__asyncMutation__asyncExport": "Export", "@sage/xtrem-sales/nodes__sales_invoice_line_discount_charge__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-sales/nodes__sales_invoice_line_discount_charge__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-sales/nodes__sales_invoice_line_discount_charge__node_name": "Sales invoice line discount charge", "@sage/xtrem-sales/nodes__sales_invoice_line_discount_charge__property__basis": "<PERSON><PERSON>", "@sage/xtrem-sales/nodes__sales_invoice_line_discount_charge__property__document": "Document", "@sage/xtrem-sales/nodes__sales_invoice_line_tax__asyncMutation__asyncExport": "Export", "@sage/xtrem-sales/nodes__sales_invoice_line_tax__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-sales/nodes__sales_invoice_line_tax__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-sales/nodes__sales_invoice_line_tax__node_name": "Sales invoice line tax", "@sage/xtrem-sales/nodes__sales_invoice_line_tax__property__currency": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-sales/nodes__sales_invoice_line_tax__property__document": "Document", "@sage/xtrem-sales/nodes__sales_invoice_line_to_sales_credit_memo_line__asyncMutation__asyncExport": "Export", "@sage/xtrem-sales/nodes__sales_invoice_line_to_sales_credit_memo_line__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-sales/nodes__sales_invoice_line_to_sales_credit_memo_line__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-sales/nodes__sales_invoice_line_to_sales_credit_memo_line__node_name": "Sales invoice line to sales credit memo line", "@sage/xtrem-sales/nodes__sales_invoice_line_to_sales_credit_memo_line__property__document": "Document", "@sage/xtrem-sales/nodes__sales_invoice_line_to_sales_credit_memo_line__property__linkedDocument": "Linked document", "@sage/xtrem-sales/nodes__sales_invoice_tax__asyncMutation__asyncExport": "Export", "@sage/xtrem-sales/nodes__sales_invoice_tax__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-sales/nodes__sales_invoice_tax__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-sales/nodes__sales_invoice_tax__node_name": "Sales invoice tax", "@sage/xtrem-sales/nodes__sales_invoice_tax__property__currency": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-sales/nodes__sales_invoice_tax__property__document": "Document", "@sage/xtrem-sales/nodes__sales_order__asyncMutation__asyncExport": "Export", "@sage/xtrem-sales/nodes__sales_order__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-sales/nodes__sales_order__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-sales/nodes__sales_order__asyncMutation__autoAllocate": "Auto allocate", "@sage/xtrem-sales/nodes__sales_order__asyncMutation__autoAllocate__failed": "Auto allocate failed.", "@sage/xtrem-sales/nodes__sales_order__asyncMutation__autoAllocate__parameter__isSafeToRetry": "Is safe to retry", "@sage/xtrem-sales/nodes__sales_order__asyncMutation__autoAllocate__parameter__requestType": "Request type", "@sage/xtrem-sales/nodes__sales_order__asyncMutation__autoAllocate__parameter__salesOrder": "Sales order", "@sage/xtrem-sales/nodes__sales_order__asyncMutation__createTestSalesOrders": "Create test sales orders", "@sage/xtrem-sales/nodes__sales_order__asyncMutation__createTestSalesOrders__failed": "Create test sales orders failed.", "@sage/xtrem-sales/nodes__sales_order__asyncMutation__createTestSalesOrders__parameter__orderCreation": "Order creation", "@sage/xtrem-sales/nodes__sales_order__bulk_print_report_name": "Sales order", "@sage/xtrem-sales/nodes__sales_order__bulkMutation__printBulkSalesOrders": "Print bulk sales orders", "@sage/xtrem-sales/nodes__sales_order__bulkMutation__printBulkSalesOrders__failed": "Print bulk sales orders failed.", "@sage/xtrem-sales/nodes__sales_order__cannot_close_allocated_request_in_progress": "You can only close the sales order after the allocation request is complete for the lines.", "@sage/xtrem-sales/nodes__sales_order__cannot_close_line_allocated_request_in_progress": "You cannot close the sales order line. An allocation request is in progress.", "@sage/xtrem-sales/nodes__sales_order__cannot_close_line_quantity_allocated": "Remove the stock allocation before closing the line.", "@sage/xtrem-sales/nodes__sales_order__cannot_close_order_because_of_a_link": "Remove the supply order links before closing the sales order.", "@sage/xtrem-sales/nodes__sales_order__cannot_close_order_quantity_allocated": "Remove the stock allocation before closing the order.", "@sage/xtrem-sales/nodes__sales_order__cannot_create_shipment": "You need to remove the inactive items before you change the document status.", "@sage/xtrem-sales/nodes__sales_order__cannot_send_invalid_email": "The sales order cannot be sent. The email is not valid.", "@sage/xtrem-sales/nodes__sales_order__cannot_send_invalid_status": "You can only send a \"Quote\" or a \"Pending\" sales order.", "@sage/xtrem-sales/nodes__sales_order__cannot_send_invalid_tax_status": "The tax calculation needs to be done before you can send the sales order.", "@sage/xtrem-sales/nodes__sales_order__cannot_ship_order_quantity_linked_is_not_equal": "The supply order quantities and sales order quantities need to be the same. You cannot ship the sales order.", "@sage/xtrem-sales/nodes__sales_order__deletion_forbidden_line_exists": "Deletion is not allowed. The sales order is shipped or partially shipped.", "@sage/xtrem-sales/nodes__sales_order__deletion_forbidden_proforma_invoices_exists": "You cannot delete a sales order when there are linked proforma invoices.", "@sage/xtrem-sales/nodes__sales_order__fx_rate_not_found": "No exchange rate found.", "@sage/xtrem-sales/nodes__sales_order__header_currency_not_updatable": "The sales order currency of the sales order must not be changed.", "@sage/xtrem-sales/nodes__sales_order__header_sales_site_not_updatable": "The sales site of the sales order must not be changed.", "@sage/xtrem-sales/nodes__sales_order__header_sold_to_customer_not_updatable": "The sold-to customer of the sales order must not be changed.", "@sage/xtrem-sales/nodes__sales_order__improper_shipping_status_during_creation": "Incorrect sales order shipping status. The sales order must have the 'Not shipped' shipping status to be created.", "@sage/xtrem-sales/nodes__sales_order__improper_stock_and_sales_site_value__during_creation": "Incorrect stock site on the sales order. The sales site and the stock site must have the same company.", "@sage/xtrem-sales/nodes__sales_order__lines_mandatory": "The sales order must contain at least one line.", "@sage/xtrem-sales/nodes__sales_order__mutation__addWorkDays": "Add work days", "@sage/xtrem-sales/nodes__sales_order__mutation__addWorkDays__failed": "Add work days failed.", "@sage/xtrem-sales/nodes__sales_order__mutation__addWorkDays__parameter__deliveryLeadTime": "Delivery lead time", "@sage/xtrem-sales/nodes__sales_order__mutation__addWorkDays__parameter__shippingDate": "Shipping date", "@sage/xtrem-sales/nodes__sales_order__mutation__addWorkDays__parameter__workDays": "Work days", "@sage/xtrem-sales/nodes__sales_order__mutation__afterPrintSalesOrder": "After print sales order", "@sage/xtrem-sales/nodes__sales_order__mutation__afterPrintSalesOrder__failed": "After print sales order failed.", "@sage/xtrem-sales/nodes__sales_order__mutation__afterPrintSalesOrder__parameter__order": "Order", "@sage/xtrem-sales/nodes__sales_order__mutation__beforePrintSalesOrder": "Before print sales order", "@sage/xtrem-sales/nodes__sales_order__mutation__beforePrintSalesOrder__failed": "Before print sales order failed.", "@sage/xtrem-sales/nodes__sales_order__mutation__beforePrintSalesOrder__parameter__order": "Order", "@sage/xtrem-sales/nodes__sales_order__mutation__closeSalesOrder": "Close sales order", "@sage/xtrem-sales/nodes__sales_order__mutation__closeSalesOrder__failed": "Close sales order failed.", "@sage/xtrem-sales/nodes__sales_order__mutation__closeSalesOrder__parameter__controlOrderLinks": "Control order links", "@sage/xtrem-sales/nodes__sales_order__mutation__closeSalesOrder__parameter__isSafeToRetry": "Is safe to retry", "@sage/xtrem-sales/nodes__sales_order__mutation__closeSalesOrder__parameter__salesOrder": "Sales order", "@sage/xtrem-sales/nodes__sales_order__mutation__confirmSalesOrder": "Confirm sales order", "@sage/xtrem-sales/nodes__sales_order__mutation__confirmSalesOrder__failed": "Confirm sales order failed.", "@sage/xtrem-sales/nodes__sales_order__mutation__confirmSalesOrder__parameter__isSafeToRetry": "Is safe to retry", "@sage/xtrem-sales/nodes__sales_order__mutation__confirmSalesOrder__parameter__salesOrder": "Sales order", "@sage/xtrem-sales/nodes__sales_order__mutation__createSalesShipmentsFromOrderLines": "Create sales shipments from order lines", "@sage/xtrem-sales/nodes__sales_order__mutation__createSalesShipmentsFromOrderLines__failed": "Create sales shipments from order lines failed.", "@sage/xtrem-sales/nodes__sales_order__mutation__createSalesShipmentsFromOrderLines__parameter__processOptions": "Process options", "@sage/xtrem-sales/nodes__sales_order__mutation__createSalesShipmentsFromOrderLines__parameter__salesDocumentLines": "Sales document lines", "@sage/xtrem-sales/nodes__sales_order__mutation__createSalesShipmentsFromOrders": "Create sales shipments from orders", "@sage/xtrem-sales/nodes__sales_order__mutation__createSalesShipmentsFromOrders__failed": "Create sales shipments from orders failed.", "@sage/xtrem-sales/nodes__sales_order__mutation__createSalesShipmentsFromOrders__parameter__processOptions": "Process options", "@sage/xtrem-sales/nodes__sales_order__mutation__createSalesShipmentsFromOrders__parameter__salesDocuments": "Sales documents", "@sage/xtrem-sales/nodes__sales_order__mutation__createShipmentsFromOrder": "Create shipments from order", "@sage/xtrem-sales/nodes__sales_order__mutation__createShipmentsFromOrder__failed": "Create shipments from order failed.", "@sage/xtrem-sales/nodes__sales_order__mutation__createShipmentsFromOrder__parameter__controlOrderLinks": "Control order links", "@sage/xtrem-sales/nodes__sales_order__mutation__createShipmentsFromOrder__parameter__isSafeToRetry": "Is safe to retry", "@sage/xtrem-sales/nodes__sales_order__mutation__createShipmentsFromOrder__parameter__salesOrder": "Sales order", "@sage/xtrem-sales/nodes__sales_order__mutation__financeIntegrationCheck": "Finance integration check", "@sage/xtrem-sales/nodes__sales_order__mutation__financeIntegrationCheck__failed": "Finance integration check failed.", "@sage/xtrem-sales/nodes__sales_order__mutation__financeIntegrationCheck__parameter__salesOrder": "Sales order", "@sage/xtrem-sales/nodes__sales_order__mutation__massCreateSalesShipments": "Mass create sales shipments", "@sage/xtrem-sales/nodes__sales_order__mutation__massCreateSalesShipments__failed": "Mass create sales shipments failed.", "@sage/xtrem-sales/nodes__sales_order__mutation__massCreateSalesShipments__parameter__deliveryMode": "Delivery mode", "@sage/xtrem-sales/nodes__sales_order__mutation__massCreateSalesShipments__parameter__fromOrder": "From order", "@sage/xtrem-sales/nodes__sales_order__mutation__massCreateSalesShipments__parameter__fromSoldToCustomer": "From sold to customer", "@sage/xtrem-sales/nodes__sales_order__mutation__massCreateSalesShipments__parameter__incoterm": "Incoterm", "@sage/xtrem-sales/nodes__sales_order__mutation__massCreateSalesShipments__parameter__shippingUntilDate": "Shipping until date", "@sage/xtrem-sales/nodes__sales_order__mutation__massCreateSalesShipments__parameter__stockSite": "Stock site", "@sage/xtrem-sales/nodes__sales_order__mutation__massCreateSalesShipments__parameter__toOrder": "To order", "@sage/xtrem-sales/nodes__sales_order__mutation__massCreateSalesShipments__parameter__toSoldToCustomer": "To sold to customer", "@sage/xtrem-sales/nodes__sales_order__mutation__massPriceDeterminationCalculation": "Mass price determination calculation", "@sage/xtrem-sales/nodes__sales_order__mutation__massPriceDeterminationCalculation__failed": "Mass price determination calculation failed.", "@sage/xtrem-sales/nodes__sales_order__mutation__massPriceDeterminationCalculation__parameter__salesDocument": "Sales document", "@sage/xtrem-sales/nodes__sales_order__mutation__printSalesOrderAndEmail": "Print sales order and email", "@sage/xtrem-sales/nodes__sales_order__mutation__printSalesOrderAndEmail__failed": "Print sales order and email failed.", "@sage/xtrem-sales/nodes__sales_order__mutation__printSalesOrderAndEmail__parameter__contactEmail": "Contact email", "@sage/xtrem-sales/nodes__sales_order__mutation__printSalesOrderAndEmail__parameter__contactFirstName": "Contact first name", "@sage/xtrem-sales/nodes__sales_order__mutation__printSalesOrderAndEmail__parameter__contactLastName": "Contact last name", "@sage/xtrem-sales/nodes__sales_order__mutation__printSalesOrderAndEmail__parameter__contactTitle": "Contact title", "@sage/xtrem-sales/nodes__sales_order__mutation__printSalesOrderAndEmail__parameter__salesOrder": "Sales order", "@sage/xtrem-sales/nodes__sales_order__mutation__requestAutoAllocation": "Request auto allocation", "@sage/xtrem-sales/nodes__sales_order__mutation__requestAutoAllocation__failed": "Request auto allocation failed.", "@sage/xtrem-sales/nodes__sales_order__mutation__requestAutoAllocation__parameter__isSafeToRetry": "Is safe to retry", "@sage/xtrem-sales/nodes__sales_order__mutation__requestAutoAllocation__parameter__requestType": "Request type", "@sage/xtrem-sales/nodes__sales_order__mutation__requestAutoAllocation__parameter__salesOrder": "Sales order", "@sage/xtrem-sales/nodes__sales_order__mutation__setIsPrintedTrue": "Set is printed true", "@sage/xtrem-sales/nodes__sales_order__mutation__setIsPrintedTrue__failed": "Set is printed true failed.", "@sage/xtrem-sales/nodes__sales_order__mutation__setIsPrintedTrue__parameter__isPrinted": "Is printed", "@sage/xtrem-sales/nodes__sales_order__mutation__setIsPrintedTrue__parameter__order": "Order", "@sage/xtrem-sales/nodes__sales_order__mutation__setSalesOrderLineCloseStatus": "Set sales order line close status", "@sage/xtrem-sales/nodes__sales_order__mutation__setSalesOrderLineCloseStatus__failed": "Set sales order line close status failed.", "@sage/xtrem-sales/nodes__sales_order__mutation__setSalesOrderLineCloseStatus__parameter__salesOrderLine": "Sales order line", "@sage/xtrem-sales/nodes__sales_order__mutation__setSalesOrderLineOpenStatus": "Set sales order line open status", "@sage/xtrem-sales/nodes__sales_order__mutation__setSalesOrderLineOpenStatus__failed": "Set sales order line open status failed.", "@sage/xtrem-sales/nodes__sales_order__mutation__setSalesOrderLineOpenStatus__parameter__newQuantityInSalesUnit": "New quantity in sales unit", "@sage/xtrem-sales/nodes__sales_order__mutation__setSalesOrderLineOpenStatus__parameter__salesOrderLine": "Sales order line", "@sage/xtrem-sales/nodes__sales_order__mutation__setSalesOrderOpenStatus": "Set sales order open status", "@sage/xtrem-sales/nodes__sales_order__mutation__setSalesOrderOpenStatus__failed": "Set sales order open status failed.", "@sage/xtrem-sales/nodes__sales_order__mutation__setSalesOrderOpenStatus__parameter__salesOrderNumber": "Sales order number", "@sage/xtrem-sales/nodes__sales_order__mutation__subWorkDays": "Sub work days", "@sage/xtrem-sales/nodes__sales_order__mutation__subWorkDays__failed": "Sub work days failed.", "@sage/xtrem-sales/nodes__sales_order__mutation__subWorkDays__parameter__deliveryLeadTime": "Delivery lead time", "@sage/xtrem-sales/nodes__sales_order__mutation__subWorkDays__parameter__doNotShipAfterDate": "Do not ship after date", "@sage/xtrem-sales/nodes__sales_order__mutation__subWorkDays__parameter__doNotShipBeforeDate": "Do not ship before date", "@sage/xtrem-sales/nodes__sales_order__mutation__subWorkDays__parameter__orderDate": "Order date", "@sage/xtrem-sales/nodes__sales_order__mutation__subWorkDays__parameter__requestedDeliveryDate": "Requested delivery date", "@sage/xtrem-sales/nodes__sales_order__mutation__subWorkDays__parameter__workDaysMask": "Work days mask", "@sage/xtrem-sales/nodes__sales_order__no_shipment_created": "No shipments were created for the sales order: {{salesOrderNumber}}.", "@sage/xtrem-sales/nodes__sales_order__node_name": "Sales order", "@sage/xtrem-sales/nodes__sales_order__nothing_to_send": "The sales order cannot be sent. No report has been created.", "@sage/xtrem-sales/nodes__sales_order__order_date_cannot_be_future": "The order date cannot be later than today.", "@sage/xtrem-sales/nodes__sales_order__print_inactive_items": "The document contains inactive items. You need to remove the inactive items before you print the document.", "@sage/xtrem-sales/nodes__sales_order__print_no_lines": "There are no lines on this document. You can print the document after you add a line.", "@sage/xtrem-sales/nodes__sales_order__print_tax_calculation_failed": "The tax calculation for this document failed. You need to correct the tax issues before you print the document.", "@sage/xtrem-sales/nodes__sales_order__property__allocationRequestStatus": "Allocation request status", "@sage/xtrem-sales/nodes__sales_order__property__allocationStatus": "Allocation status", "@sage/xtrem-sales/nodes__sales_order__property__billToAddress": "Bill to address", "@sage/xtrem-sales/nodes__sales_order__property__billToContact": "Bill to contact", "@sage/xtrem-sales/nodes__sales_order__property__billToCustomer": "Bill to customer", "@sage/xtrem-sales/nodes__sales_order__property__billToLinkedAddress": "Bill to linked address", "@sage/xtrem-sales/nodes__sales_order__property__companyFxRate": "Company fx rate", "@sage/xtrem-sales/nodes__sales_order__property__companyFxRateDivisor": "Company fx rate divisor", "@sage/xtrem-sales/nodes__sales_order__property__currency": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-sales/nodes__sales_order__property__customerNumber": "Customer number", "@sage/xtrem-sales/nodes__sales_order__property__date": "Date", "@sage/xtrem-sales/nodes__sales_order__property__deliveryLeadTime": "Delivery lead time", "@sage/xtrem-sales/nodes__sales_order__property__deliveryMode": "Delivery mode", "@sage/xtrem-sales/nodes__sales_order__property__displayStatus": "Display status", "@sage/xtrem-sales/nodes__sales_order__property__doNotShipAfterDate": "Do not ship after date", "@sage/xtrem-sales/nodes__sales_order__property__doNotShipBeforeDate": "Do not ship before date", "@sage/xtrem-sales/nodes__sales_order__property__expectedDeliveryDate": "Expected delivery date", "@sage/xtrem-sales/nodes__sales_order__property__externalNote": "External note", "@sage/xtrem-sales/nodes__sales_order__property__fxRateDate": "Fx rate date", "@sage/xtrem-sales/nodes__sales_order__property__incoterm": "Incoterm", "@sage/xtrem-sales/nodes__sales_order__property__internalNote": "Internal note", "@sage/xtrem-sales/nodes__sales_order__property__invoiceStatus": "Invoice status", "@sage/xtrem-sales/nodes__sales_order__property__isCloseHidden": "Is close hidden", "@sage/xtrem-sales/nodes__sales_order__property__isExternalNote": "Is external note", "@sage/xtrem-sales/nodes__sales_order__property__isOnHold": "Is on hold", "@sage/xtrem-sales/nodes__sales_order__property__isOrderAssignmentLinked": "Is order assignment linked", "@sage/xtrem-sales/nodes__sales_order__property__isQuote": "Is quote", "@sage/xtrem-sales/nodes__sales_order__property__isTransferHeaderNote": "Is transfer header note", "@sage/xtrem-sales/nodes__sales_order__property__isTransferLineNote": "Is transfer line note", "@sage/xtrem-sales/nodes__sales_order__property__lines": "Lines", "@sage/xtrem-sales/nodes__sales_order__property__orderDate": "Order date", "@sage/xtrem-sales/nodes__sales_order__property__paymentTerm": "Payment term", "@sage/xtrem-sales/nodes__sales_order__property__proformaInvoices": "Proforma invoices", "@sage/xtrem-sales/nodes__sales_order__property__rateDescription": "Rate description", "@sage/xtrem-sales/nodes__sales_order__property__recordUrl": "Record url", "@sage/xtrem-sales/nodes__sales_order__property__remainingTotalAmountExcludingTaxInCompanyCurrency": "Remaining total amount excluding tax in company currency", "@sage/xtrem-sales/nodes__sales_order__property__remainingTotalAmountToShipExcludingTax": "Remaining total amount to ship excluding tax", "@sage/xtrem-sales/nodes__sales_order__property__requestedDeliveryDate": "Requested delivery date", "@sage/xtrem-sales/nodes__sales_order__property__salesSite": "Sales site", "@sage/xtrem-sales/nodes__sales_order__property__shippingDate": "Shipping date", "@sage/xtrem-sales/nodes__sales_order__property__shippingStatus": "Shipping status", "@sage/xtrem-sales/nodes__sales_order__property__shipToAddress": "Ship to address", "@sage/xtrem-sales/nodes__sales_order__property__shipToContact": "Ship to contact", "@sage/xtrem-sales/nodes__sales_order__property__shipToCustomer": "Ship to customer", "@sage/xtrem-sales/nodes__sales_order__property__shipToCustomerAddress": "Ship to customer address", "@sage/xtrem-sales/nodes__sales_order__property__site": "Site", "@sage/xtrem-sales/nodes__sales_order__property__soldToAddress": "Sold to address", "@sage/xtrem-sales/nodes__sales_order__property__soldToContact": "Sold to contact", "@sage/xtrem-sales/nodes__sales_order__property__soldToCustomer": "Sold to customer", "@sage/xtrem-sales/nodes__sales_order__property__soldToLinkedAddress": "Sold to linked address", "@sage/xtrem-sales/nodes__sales_order__property__status": "Status", "@sage/xtrem-sales/nodes__sales_order__property__stockSite": "Stock site", "@sage/xtrem-sales/nodes__sales_order__property__taxCalculationStatus": "Tax calculation status", "@sage/xtrem-sales/nodes__sales_order__property__taxEngine": "Tax engine", "@sage/xtrem-sales/nodes__sales_order__property__taxes": "Taxes", "@sage/xtrem-sales/nodes__sales_order__property__totalAmountExcludingTax": "Total amount excluding tax", "@sage/xtrem-sales/nodes__sales_order__property__totalAmountExcludingTaxInCompanyCurrency": "Total amount excluding tax in company currency", "@sage/xtrem-sales/nodes__sales_order__property__totalAmountIncludingTax": "Total amount including tax", "@sage/xtrem-sales/nodes__sales_order__property__totalAmountIncludingTaxInCompanyCurrency": "Total amount including tax in company currency", "@sage/xtrem-sales/nodes__sales_order__property__totalExemptAmount": "Total exempt amount", "@sage/xtrem-sales/nodes__sales_order__property__totalGrossProfit": "Total gross profit", "@sage/xtrem-sales/nodes__sales_order__property__totalTaxableAmount": "Total taxable amount", "@sage/xtrem-sales/nodes__sales_order__property__totalTaxAmount": "Total tax amount", "@sage/xtrem-sales/nodes__sales_order__property__totalTaxAmountAdjusted": "Total tax amount adjusted", "@sage/xtrem-sales/nodes__sales_order__property__workDays": "Work days", "@sage/xtrem-sales/nodes__sales_order__query__getLastOrderDate": "Get last order date", "@sage/xtrem-sales/nodes__sales_order__query__getLastOrderDate__failed": "Get last order date failed.", "@sage/xtrem-sales/nodes__sales_order__query__getLastOrderDate__parameter__customer": "Customer", "@sage/xtrem-sales/nodes__sales_order__query__getOrderBookAmount": "Get order book amount", "@sage/xtrem-sales/nodes__sales_order__query__getOrderBookAmount__failed": "Get order book amount failed.", "@sage/xtrem-sales/nodes__sales_order__query__getOrderBookAmount__parameter__customer": "Customer", "@sage/xtrem-sales/nodes__sales_order__query__orderAssignmentQuantitySum": "Order assignment quantity sum", "@sage/xtrem-sales/nodes__sales_order__query__orderAssignmentQuantitySum__failed": "Order assignment quantity sum failed.", "@sage/xtrem-sales/nodes__sales_order__query__orderAssignmentQuantitySum__parameter__salesOrder": "Sales order", "@sage/xtrem-sales/nodes__sales_order__sales_order_already_confirmed": "The sales order is already confirmed.", "@sage/xtrem-sales/nodes__sales_order__sales_order_is_already_closed": "The sales order is closed. You cannot close the line.", "@sage/xtrem-sales/nodes__sales_order__tax_type_validation": "The tax type for all documents needs to be 'Sales' or 'Purchasing and sales'.", "@sage/xtrem-sales/nodes__sales_order__update_not_allowed_status_closed": "The sales order is closed. You cannot update it.", "@sage/xtrem-sales/nodes__sales_order_line__asyncMutation__asyncExport": "Export", "@sage/xtrem-sales/nodes__sales_order_line__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-sales/nodes__sales_order_line__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-sales/nodes__sales_order_line__auto_allocation_cannot_decrease_quantity": "You can only reduce the quantity of the sales order line after the allocation request is complete.", "@sage/xtrem-sales/nodes__sales_order_line__bulkMutation__massAutoAllocation": "Mass auto allocation", "@sage/xtrem-sales/nodes__sales_order_line__bulkMutation__massAutoAllocation__failed": "Mass auto allocation failed.", "@sage/xtrem-sales/nodes__sales_order_line__bulkMutation__massAutoAllocation__parameter__data": "Data", "@sage/xtrem-sales/nodes__sales_order_line__bulkMutation__massAutoAllocation__parameter__stockAllocationParameters": "Stock allocation parameters", "@sage/xtrem-sales/nodes__sales_order_line__cannot_delete_line_allocation_request_in_progress": "Automatic allocation needs to finish before you can delete the line.", "@sage/xtrem-sales/nodes__sales_order_line__cannot_delete_line_quantity_allocated": "Remove the stock allocation before deleting the line.", "@sage/xtrem-sales/nodes__sales_order_line__deletion_forbidden_line_exists": "Deletion is not allowed. The sales order line is shipped or partially shipped.", "@sage/xtrem-sales/nodes__sales_order_line__improper_status_during_creation": "The status for the sales order line needs to be \"Quote\" or \"Pending\".", "@sage/xtrem-sales/nodes__sales_order_line__mutation__calculateLineTaxes": "Calculate line taxes", "@sage/xtrem-sales/nodes__sales_order_line__mutation__calculateLineTaxes__failed": "Calculate line taxes failed.", "@sage/xtrem-sales/nodes__sales_order_line__mutation__calculateLineTaxes__parameter__data": "Data", "@sage/xtrem-sales/nodes__sales_order_line__node_name": "Sales order line", "@sage/xtrem-sales/nodes__sales_order_line__property__allocationRequestStatus": "Allocation request status", "@sage/xtrem-sales/nodes__sales_order_line__property__allocationStatus": "Allocation status", "@sage/xtrem-sales/nodes__sales_order_line__property__amountExcludingTax": "Amount excluding tax", "@sage/xtrem-sales/nodes__sales_order_line__property__amountExcludingTaxInCompanyCurrency": "Amount excluding tax in company currency", "@sage/xtrem-sales/nodes__sales_order_line__property__amountIncludingTax": "Amount including tax", "@sage/xtrem-sales/nodes__sales_order_line__property__amountIncludingTaxInCompanyCurrency": "Amount including tax in company currency", "@sage/xtrem-sales/nodes__sales_order_line__property__analyticalData": "Analytical data", "@sage/xtrem-sales/nodes__sales_order_line__property__assignedQuantity": "Assigned quantity", "@sage/xtrem-sales/nodes__sales_order_line__property__assignments": "Assignments", "@sage/xtrem-sales/nodes__sales_order_line__property__availableQuantityInSalesUnit": "Available quantity in sales unit", "@sage/xtrem-sales/nodes__sales_order_line__property__availableQuantityInStockUnit": "Available quantity in stock unit", "@sage/xtrem-sales/nodes__sales_order_line__property__charge": "Charge", "@sage/xtrem-sales/nodes__sales_order_line__property__chargeDeterminated": "Charge determinated", "@sage/xtrem-sales/nodes__sales_order_line__property__computedAttributes": "Computed attributes", "@sage/xtrem-sales/nodes__sales_order_line__property__currency": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-sales/nodes__sales_order_line__property__deliveryLeadTime": "Delivery lead time", "@sage/xtrem-sales/nodes__sales_order_line__property__deliveryMode": "Delivery mode", "@sage/xtrem-sales/nodes__sales_order_line__property__discount": "Discount", "@sage/xtrem-sales/nodes__sales_order_line__property__discountCharges": "Discount charges", "@sage/xtrem-sales/nodes__sales_order_line__property__discountDeterminated": "Discount determinated", "@sage/xtrem-sales/nodes__sales_order_line__property__document": "Document", "@sage/xtrem-sales/nodes__sales_order_line__property__documentId": "Document id", "@sage/xtrem-sales/nodes__sales_order_line__property__documentNumber": "Document number", "@sage/xtrem-sales/nodes__sales_order_line__property__doNotShipAfterDate": "Do not ship after date", "@sage/xtrem-sales/nodes__sales_order_line__property__doNotShipBeforeDate": "Do not ship before date", "@sage/xtrem-sales/nodes__sales_order_line__property__exemptAmount": "Exempt amount", "@sage/xtrem-sales/nodes__sales_order_line__property__expectedDeliveryDate": "Expected delivery date", "@sage/xtrem-sales/nodes__sales_order_line__property__grossPrice": "Gross price", "@sage/xtrem-sales/nodes__sales_order_line__property__grossPriceDeterminated": "Gross price determinated", "@sage/xtrem-sales/nodes__sales_order_line__property__grossProfit": "Gross profit", "@sage/xtrem-sales/nodes__sales_order_line__property__grossProfitAmount": "Gross profit amount", "@sage/xtrem-sales/nodes__sales_order_line__property__grossProfitAmountInCompanyCurrency": "Gross profit amount in company currency", "@sage/xtrem-sales/nodes__sales_order_line__property__invoicedQuantityInSalesUnit": "Invoiced quantity in sales unit", "@sage/xtrem-sales/nodes__sales_order_line__property__invoiceStatus": "Invoice status", "@sage/xtrem-sales/nodes__sales_order_line__property__isPriceDeterminated": "Is price determinated", "@sage/xtrem-sales/nodes__sales_order_line__property__item": "<PERSON><PERSON>", "@sage/xtrem-sales/nodes__sales_order_line__property__netPrice": "Net price", "@sage/xtrem-sales/nodes__sales_order_line__property__netPriceExcludingTax": "Net price excluding tax", "@sage/xtrem-sales/nodes__sales_order_line__property__netPriceIncludingTax": "Net price including tax", "@sage/xtrem-sales/nodes__sales_order_line__property__originDocumentType": "Origin document type", "@sage/xtrem-sales/nodes__sales_order_line__property__priceOrigin": "Price origin", "@sage/xtrem-sales/nodes__sales_order_line__property__priceOriginDeterminated": "Price origin determinated", "@sage/xtrem-sales/nodes__sales_order_line__property__priceReason": "Price reason", "@sage/xtrem-sales/nodes__sales_order_line__property__priceReasonDeterminated": "Price reason determinated", "@sage/xtrem-sales/nodes__sales_order_line__property__quantity": "Quantity", "@sage/xtrem-sales/nodes__sales_order_line__property__quantityAllocated": "Quantity allocated", "@sage/xtrem-sales/nodes__sales_order_line__property__quantityInSalesUnit": "Quantity in sales unit", "@sage/xtrem-sales/nodes__sales_order_line__property__quantityInStockUnit": "Quantity in stock unit", "@sage/xtrem-sales/nodes__sales_order_line__property__quantityToInvoiceInProgressInSalesUnit": "Quantity to invoice in progress in sales unit", "@sage/xtrem-sales/nodes__sales_order_line__property__quantityToShipInProgressInSalesUnit": "Quantity to ship in progress in sales unit", "@sage/xtrem-sales/nodes__sales_order_line__property__remainingAmountToShipExcludingTax": "Remaining amount to ship excluding tax", "@sage/xtrem-sales/nodes__sales_order_line__property__remainingAmountToShipExcludingTaxInCompanyCurrency": "Remaining amount to ship excluding tax in company currency", "@sage/xtrem-sales/nodes__sales_order_line__property__remainingQuantityToAllocate": "Remaining quantity to allocate", "@sage/xtrem-sales/nodes__sales_order_line__property__remainingQuantityToInvoiceInSalesUnit": "Remaining quantity to invoice in sales unit", "@sage/xtrem-sales/nodes__sales_order_line__property__remainingQuantityToShipInSalesUnit": "Remaining quantity to ship in sales unit", "@sage/xtrem-sales/nodes__sales_order_line__property__remainingQuantityToShipInStockUnit": "Remaining quantity to ship in stock unit", "@sage/xtrem-sales/nodes__sales_order_line__property__requestedDeliveryDate": "Requested delivery date", "@sage/xtrem-sales/nodes__sales_order_line__property__salesShipmentLines": "Sales shipment lines", "@sage/xtrem-sales/nodes__sales_order_line__property__salesSite": "Sales site", "@sage/xtrem-sales/nodes__sales_order_line__property__salesUnit": "Sales unit", "@sage/xtrem-sales/nodes__sales_order_line__property__salesUnitToStockUnitConversionFactor": "Sales unit to stock unit conversion factor", "@sage/xtrem-sales/nodes__sales_order_line__property__shippedQuantityInSalesUnit": "Shipped quantity in sales unit", "@sage/xtrem-sales/nodes__sales_order_line__property__shippingDate": "Shipping date", "@sage/xtrem-sales/nodes__sales_order_line__property__shippingStatus": "Shipping status", "@sage/xtrem-sales/nodes__sales_order_line__property__shipToAddress": "Ship to address", "@sage/xtrem-sales/nodes__sales_order_line__property__shipToContact": "Ship to contact", "@sage/xtrem-sales/nodes__sales_order_line__property__shipToCustomerAddress": "Ship to customer address", "@sage/xtrem-sales/nodes__sales_order_line__property__status": "Status", "@sage/xtrem-sales/nodes__sales_order_line__property__stockAllocations": "Stock allocations", "@sage/xtrem-sales/nodes__sales_order_line__property__stockCostAmount": "Stock cost amount", "@sage/xtrem-sales/nodes__sales_order_line__property__stockCostAmountInCompanyCurrency": "Stock cost amount in company currency", "@sage/xtrem-sales/nodes__sales_order_line__property__stockCostUnit": "Stock cost unit", "@sage/xtrem-sales/nodes__sales_order_line__property__stockDetails": "Stock details", "@sage/xtrem-sales/nodes__sales_order_line__property__stockOnHand": "Stock on hand", "@sage/xtrem-sales/nodes__sales_order_line__property__stockShortageInSalesUnit": "Stock shortage in sales unit", "@sage/xtrem-sales/nodes__sales_order_line__property__stockShortageInStockUnit": "Stock shortage in stock unit", "@sage/xtrem-sales/nodes__sales_order_line__property__stockShortageStatus": "Stock shortage status", "@sage/xtrem-sales/nodes__sales_order_line__property__stockSite": "Stock site", "@sage/xtrem-sales/nodes__sales_order_line__property__stockSiteAddress": "Stock site address", "@sage/xtrem-sales/nodes__sales_order_line__property__stockUnit": "Stock unit", "@sage/xtrem-sales/nodes__sales_order_line__property__storedAttributes": "Stored attributes", "@sage/xtrem-sales/nodes__sales_order_line__property__storedDimensions": "Stored dimensions", "@sage/xtrem-sales/nodes__sales_order_line__property__suppliedQuantity": "Supplied quantity", "@sage/xtrem-sales/nodes__sales_order_line__property__taxableAmount": "Taxable amount", "@sage/xtrem-sales/nodes__sales_order_line__property__taxAmount": "Tax amount", "@sage/xtrem-sales/nodes__sales_order_line__property__taxAmountAdjusted": "Tax amount adjusted", "@sage/xtrem-sales/nodes__sales_order_line__property__taxCalculationStatus": "Tax calculation status", "@sage/xtrem-sales/nodes__sales_order_line__property__taxDate": "Tax date", "@sage/xtrem-sales/nodes__sales_order_line__property__taxes": "Taxes", "@sage/xtrem-sales/nodes__sales_order_line__property__taxZone": "Tax zone", "@sage/xtrem-sales/nodes__sales_order_line__property__toInvoiceLines": "To invoice lines", "@sage/xtrem-sales/nodes__sales_order_line__property__uAssignmentOrder": "U assignment order", "@sage/xtrem-sales/nodes__sales_order_line__property__uiTaxes": "Ui taxes", "@sage/xtrem-sales/nodes__sales_order_line__property__unit": "Unit", "@sage/xtrem-sales/nodes__sales_order_line__property__unitToStockUnitConversionFactor": "Unit to stock unit conversion factor", "@sage/xtrem-sales/nodes__sales_order_line__property__uPurchaseOrderLine": "U purchase order line", "@sage/xtrem-sales/nodes__sales_order_line__property__uWorkOrderLine": "U work order line", "@sage/xtrem-sales/nodes__sales_order_line__property__workInProgress": "Work in progress", "@sage/xtrem-sales/nodes__sales_order_line__query__assignedOrderAssignment": "Assigned order assignment", "@sage/xtrem-sales/nodes__sales_order_line__query__assignedOrderAssignment__failed": "Assigned order assignment failed.", "@sage/xtrem-sales/nodes__sales_order_line__query__assignedOrderAssignment__parameter__preferredProcess": "Preferred process", "@sage/xtrem-sales/nodes__sales_order_line__query__assignedOrderAssignment__parameter__salesOrderLine": "Sales order line", "@sage/xtrem-sales/nodes__sales_order_line__query__assignedOrderAssignment__parameter__supplyType": "Supply type", "@sage/xtrem-sales/nodes__sales_order_line__sales_order_lines_control_shippingdate_with_doNotShipAfterDate": "The shipping date needs to be before the Do not ship after date.", "@sage/xtrem-sales/nodes__sales_order_line__sales_order_lines_control_shippingdate_with_doNotShipBeforeDate": "The shipping date needs to be after the Do not ship before date.", "@sage/xtrem-sales/nodes__sales_order_line__sales_order_lines_control_shippingdate_with_orderdate": "The shipping date needs to be after the order date.", "@sage/xtrem-sales/nodes__sales_order_line__sales_order_lines_shipping_status_must_have_the_same_shipping_status": "The shipping status of the sales order lines must be the same as the shipping status displayed in the header.", "@sage/xtrem-sales/nodes__sales_order_line__sales_order_lines_status_must_have_the_same_status": "The sales order lines status must have the same status than the header.", "@sage/xtrem-sales/nodes__sales_order_line__sales_order_sales_site_must_have_the_same_value": "The site on the sales order lines must be the same as in the header.", "@sage/xtrem-sales/nodes__sales_order_line__sales_order_sales_stock_and_sales_site_value__during_creation": "Incorrect stock site on the sales order line. The sales site and the stock site on the sales order line must have the same company.", "@sage/xtrem-sales/nodes__sales_order_line__too_much_quantity_allocated,": "The allocated quantity on the sales order line cannot be larger than the remaining quantity to ship.", "@sage/xtrem-sales/nodes__sales_order_line_discount_charge__asyncMutation__asyncExport": "Export", "@sage/xtrem-sales/nodes__sales_order_line_discount_charge__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-sales/nodes__sales_order_line_discount_charge__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-sales/nodes__sales_order_line_discount_charge__node_name": "Sales order line discount charge", "@sage/xtrem-sales/nodes__sales_order_line_discount_charge__property__basis": "<PERSON><PERSON>", "@sage/xtrem-sales/nodes__sales_order_line_discount_charge__property__document": "Document", "@sage/xtrem-sales/nodes__sales_order_line_tax__asyncMutation__asyncExport": "Export", "@sage/xtrem-sales/nodes__sales_order_line_tax__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-sales/nodes__sales_order_line_tax__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-sales/nodes__sales_order_line_tax__node_name": "Sales order line tax", "@sage/xtrem-sales/nodes__sales_order_line_tax__property__currency": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-sales/nodes__sales_order_line_tax__property__document": "Document", "@sage/xtrem-sales/nodes__sales_order_line_to_sales_invoice_line__asyncMutation__asyncExport": "Export", "@sage/xtrem-sales/nodes__sales_order_line_to_sales_invoice_line__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-sales/nodes__sales_order_line_to_sales_invoice_line__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-sales/nodes__sales_order_line_to_sales_invoice_line__node_name": "Sales order line to sales invoice line", "@sage/xtrem-sales/nodes__sales_order_line_to_sales_invoice_line__property__document": "Document", "@sage/xtrem-sales/nodes__sales_order_line_to_sales_invoice_line__property__linkedDocument": "Linked document", "@sage/xtrem-sales/nodes__sales_order_line_to_sales_shipment_line__asyncMutation__asyncExport": "Export", "@sage/xtrem-sales/nodes__sales_order_line_to_sales_shipment_line__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-sales/nodes__sales_order_line_to_sales_shipment_line__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-sales/nodes__sales_order_line_to_sales_shipment_line__node_name": "Sales order line to sales shipment line", "@sage/xtrem-sales/nodes__sales_order_line_to_sales_shipment_line__property__document": "Document", "@sage/xtrem-sales/nodes__sales_order_line_to_sales_shipment_line__property__linkedDocument": "Linked document", "@sage/xtrem-sales/nodes__sales_order_tax__asyncMutation__asyncExport": "Export", "@sage/xtrem-sales/nodes__sales_order_tax__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-sales/nodes__sales_order_tax__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-sales/nodes__sales_order_tax__node_name": "Sales order tax", "@sage/xtrem-sales/nodes__sales_order_tax__property__currency": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-sales/nodes__sales_order_tax__property__document": "Document", "@sage/xtrem-sales/nodes__sales_return__header_sales_site_not_updatable": "The sales site of the sales return request must not be changed.", "@sage/xtrem-sales/nodes__sales_return__header_sold_to_customer_not_updatable": "The sold-to customer of the sales return request must not be changed.", "@sage/xtrem-sales/nodes__sales_return__update_not_allowed_status_closed": "The sales return request is closed. You cannot update it.", "@sage/xtrem-sales/nodes__sales_return_receipt__asyncMutation__asyncExport": "Export", "@sage/xtrem-sales/nodes__sales_return_receipt__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-sales/nodes__sales_return_receipt__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-sales/nodes__sales_return_receipt__mutation__postToStock": "Post to stock", "@sage/xtrem-sales/nodes__sales_return_receipt__mutation__postToStock__failed": "Post to stock failed.", "@sage/xtrem-sales/nodes__sales_return_receipt__mutation__postToStock__parameter__documentIds": "Document ids", "@sage/xtrem-sales/nodes__sales_return_receipt__mutation__repost": "Repost", "@sage/xtrem-sales/nodes__sales_return_receipt__mutation__repost__failed": "Repost failed.", "@sage/xtrem-sales/nodes__sales_return_receipt__mutation__repost__parameter__documentLines": "Document lines", "@sage/xtrem-sales/nodes__sales_return_receipt__mutation__repost__parameter__salesReturnReceipt": "Sales return receipt", "@sage/xtrem-sales/nodes__sales_return_receipt__mutation__resendNotificationForFinance": "Resend notification for finance", "@sage/xtrem-sales/nodes__sales_return_receipt__mutation__resendNotificationForFinance__failed": "Resend notification for finance failed.", "@sage/xtrem-sales/nodes__sales_return_receipt__mutation__resendNotificationForFinance__parameter__salesReturnReceipt": "Sales return receipt", "@sage/xtrem-sales/nodes__sales_return_receipt__node_name": "Sales return receipt", "@sage/xtrem-sales/nodes__sales_return_receipt__property__billToCustomer": "Bill to customer", "@sage/xtrem-sales/nodes__sales_return_receipt__property__date": "Date", "@sage/xtrem-sales/nodes__sales_return_receipt__property__displayStatus": "Display status", "@sage/xtrem-sales/nodes__sales_return_receipt__property__effectiveDate": "Effective date", "@sage/xtrem-sales/nodes__sales_return_receipt__property__financeIntegrationStatus": "Finance integration status", "@sage/xtrem-sales/nodes__sales_return_receipt__property__forceUpdateForFinance": "Force update for finance", "@sage/xtrem-sales/nodes__sales_return_receipt__property__forceUpdateForStock": "Force update for stock", "@sage/xtrem-sales/nodes__sales_return_receipt__property__internalNote": "Internal note", "@sage/xtrem-sales/nodes__sales_return_receipt__property__lines": "Lines", "@sage/xtrem-sales/nodes__sales_return_receipt__property__postingDetails": "Posting details", "@sage/xtrem-sales/nodes__sales_return_receipt__property__returnDate": "Return date", "@sage/xtrem-sales/nodes__sales_return_receipt__property__shipToAddress": "Ship to address", "@sage/xtrem-sales/nodes__sales_return_receipt__property__shipToContact": "Ship to contact", "@sage/xtrem-sales/nodes__sales_return_receipt__property__shipToCustomer": "Ship to customer", "@sage/xtrem-sales/nodes__sales_return_receipt__property__shipToCustomerAddress": "Ship to customer address", "@sage/xtrem-sales/nodes__sales_return_receipt__property__site": "Site", "@sage/xtrem-sales/nodes__sales_return_receipt__property__status": "Status", "@sage/xtrem-sales/nodes__sales_return_receipt__property__stockDetailStatus": "Stock detail status", "@sage/xtrem-sales/nodes__sales_return_receipt__property__stockTransactionStatus": "Stock transaction status", "@sage/xtrem-sales/nodes__sales_return_receipt__property__transactionCurrency": "Transaction currency", "@sage/xtrem-sales/nodes__sales_return_receipt__query__validateQuantityInSalesUnit": "Validate quantity in sales unit", "@sage/xtrem-sales/nodes__sales_return_receipt__query__validateQuantityInSalesUnit__failed": "Validate quantity in sales unit failed.", "@sage/xtrem-sales/nodes__sales_return_receipt__query__validateQuantityInSalesUnit__parameter__newQuantityInSalesUnit": "New quantity in sales unit", "@sage/xtrem-sales/nodes__sales_return_receipt__query__validateQuantityInSalesUnit__parameter__salesReturnReceiptLine": "Sales return receipt line", "@sage/xtrem-sales/nodes__sales_return_receipt__query__validateQuantityInSalesUnit__parameter__salesReturnRequestLine": "Sales return request line", "@sage/xtrem-sales/nodes__sales_return_receipt__query__validateQuantityInSalesUnit__parameter__sumOfStockDetailQuantity": "Sum of stock detail quantity", "@sage/xtrem-sales/nodes__sales_return_receipt__remaining_quantity": "The received quantity cannot be greater than the remaining quantity to be returned.", "@sage/xtrem-sales/nodes__sales_return_receipt__remaining_stock_detail_quantity": "The received quantity cannot be lower than the stock quantity already identified.", "@sage/xtrem-sales/nodes__sales_return_receipt__return_date_date_cannot_be_future": "The return date cannot be later than today.", "@sage/xtrem-sales/nodes__sales_return_receipt__return_date_update_not_allowed": "The return date cannot be updated after the sales return receipt has been created.", "@sage/xtrem-sales/nodes__sales_return_receipt__ship_to_customer_not_allowed": "The ship-to-customer cannot be updated after the sales return receipt has been created.", "@sage/xtrem-sales/nodes__sales_return_receipt__stock_site_update_not_allowed": "The stock site cannot be updated after the sales return receipt has been created.", "@sage/xtrem-sales/nodes__sales_return_receipt_line__asyncMutation__asyncExport": "Export", "@sage/xtrem-sales/nodes__sales_return_receipt_line__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-sales/nodes__sales_return_receipt_line__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-sales/nodes__sales_return_receipt_line__delete_not_allowed_line_already_posted": "The sales return receipt line cannot be deleted. The sales return receipt line is already posted.", "@sage/xtrem-sales/nodes__sales_return_receipt_line__delete_not_allowed_line_not_draft": "The sales return receipt line cannot be deleted. The sales return receipt line is not a draft.", "@sage/xtrem-sales/nodes__sales_return_receipt_line__delete_not_allowed_linked_line_credited": "The sales return receipt line cannot be deleted. The sales return request line associated is credited.", "@sage/xtrem-sales/nodes__sales_return_receipt_line__modification_of_the_item_description_is_forbidden": "The item description cannot be updated.", "@sage/xtrem-sales/nodes__sales_return_receipt_line__modification_of_the_item_is_forbidden": "The item cannot be updated.", "@sage/xtrem-sales/nodes__sales_return_receipt_line__modification_of_the_order_cost_is_forbidden": "The order cost cannot be updated.", "@sage/xtrem-sales/nodes__sales_return_receipt_line__modification_of_the_origin_document_type_is_forbidden": "The origin document type cannot be updated.", "@sage/xtrem-sales/nodes__sales_return_receipt_line__modification_of_the_quantity_in_sales_unit_is_forbidden": "The quantity in sales unit cannot be updated.", "@sage/xtrem-sales/nodes__sales_return_receipt_line__modification_of_the_quantity_in_stock_unit_is_forbidden": "The quantity in stock unit cannot be updated.", "@sage/xtrem-sales/nodes__sales_return_receipt_line__modification_of_the_sales_unit_is_forbidden": "The sales unit cannot be updated.", "@sage/xtrem-sales/nodes__sales_return_receipt_line__modification_of_the_stock_unit_is_forbidden": "The stock unit cannot be updated.", "@sage/xtrem-sales/nodes__sales_return_receipt_line__modification_of_the_valued_cost_is_forbidden": "The valued cost cannot be updated.", "@sage/xtrem-sales/nodes__sales_return_receipt_line__node_name": "Sales return receipt line", "@sage/xtrem-sales/nodes__sales_return_receipt_line__property__computedAttributes": "Computed attributes", "@sage/xtrem-sales/nodes__sales_return_receipt_line__property__document": "Document", "@sage/xtrem-sales/nodes__sales_return_receipt_line__property__documentId": "Document id", "@sage/xtrem-sales/nodes__sales_return_receipt_line__property__documentNumber": "Document number", "@sage/xtrem-sales/nodes__sales_return_receipt_line__property__internalNote": "Internal note", "@sage/xtrem-sales/nodes__sales_return_receipt_line__property__isReceiptExpected": "Is receipt expected", "@sage/xtrem-sales/nodes__sales_return_receipt_line__property__item": "<PERSON><PERSON>", "@sage/xtrem-sales/nodes__sales_return_receipt_line__property__itemDescription": "Item description", "@sage/xtrem-sales/nodes__sales_return_receipt_line__property__jsonStockDetails": "Json stock details", "@sage/xtrem-sales/nodes__sales_return_receipt_line__property__location": "Location", "@sage/xtrem-sales/nodes__sales_return_receipt_line__property__lot": "Lot", "@sage/xtrem-sales/nodes__sales_return_receipt_line__property__orderCost": "Order cost", "@sage/xtrem-sales/nodes__sales_return_receipt_line__property__origin": "Origin", "@sage/xtrem-sales/nodes__sales_return_receipt_line__property__originDocumentType": "Origin document type", "@sage/xtrem-sales/nodes__sales_return_receipt_line__property__quantity": "Quantity", "@sage/xtrem-sales/nodes__sales_return_receipt_line__property__quantityInSalesUnit": "Quantity in sales unit", "@sage/xtrem-sales/nodes__sales_return_receipt_line__property__quantityInStockUnit": "Quantity in stock unit", "@sage/xtrem-sales/nodes__sales_return_receipt_line__property__salesShipmentLines": "Sales shipment lines", "@sage/xtrem-sales/nodes__sales_return_receipt_line__property__salesUnit": "Sales unit", "@sage/xtrem-sales/nodes__sales_return_receipt_line__property__salesUnitToStockUnitConversionFactor": "Sales unit to stock unit conversion factor", "@sage/xtrem-sales/nodes__sales_return_receipt_line__property__sourceDocumentNumber": "Source document number", "@sage/xtrem-sales/nodes__sales_return_receipt_line__property__sourceDocumentType": "Source document type", "@sage/xtrem-sales/nodes__sales_return_receipt_line__property__stockDetails": "Stock details", "@sage/xtrem-sales/nodes__sales_return_receipt_line__property__stockDetailStatus": "Stock detail status", "@sage/xtrem-sales/nodes__sales_return_receipt_line__property__stockMovements": "Stock movements", "@sage/xtrem-sales/nodes__sales_return_receipt_line__property__stockStatus": "Stock status", "@sage/xtrem-sales/nodes__sales_return_receipt_line__property__stockTransactions": "Stock transactions", "@sage/xtrem-sales/nodes__sales_return_receipt_line__property__stockTransactionStatus": "Stock transaction status", "@sage/xtrem-sales/nodes__sales_return_receipt_line__property__stockUnit": "Stock unit", "@sage/xtrem-sales/nodes__sales_return_receipt_line__property__toReturnRequestLines": "To return request lines", "@sage/xtrem-sales/nodes__sales_return_receipt_line__property__unit": "Unit", "@sage/xtrem-sales/nodes__sales_return_receipt_line__property__unitToStockUnitConversionFactor": "Unit to stock unit conversion factor", "@sage/xtrem-sales/nodes__sales_return_receipt_line__property__valuedCost": "Valued cost", "@sage/xtrem-sales/nodes__sales_return_receipt_line__update_not_allowed_linked_line_closed": "The sales return receipt line cannot be updated. The sales return request line associated is closed.", "@sage/xtrem-sales/nodes__sales_return_receipt_post__already_done": "The sales return receipt was already posted.", "@sage/xtrem-sales/nodes__sales_return_request__approved": "The sales return request has been approved.", "@sage/xtrem-sales/nodes__sales_return_request__asyncMutation__asyncExport": "Export", "@sage/xtrem-sales/nodes__sales_return_request__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-sales/nodes__sales_return_request__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-sales/nodes__sales_return_request__deletion_forbidden_approved_or_rejected": "Deletion is not allowed. The sales return request is approved or rejected.", "@sage/xtrem-sales/nodes__sales_return_request__deletion_forbidden_credited_or_partially_credited": "Deletion is not allowed. The sales return request is credited or partially credited.", "@sage/xtrem-sales/nodes__sales_return_request__deletion_forbidden_line_credited_or_partially_credited": "Delete not allowed. The sales return request line is credited or partially credited.", "@sage/xtrem-sales/nodes__sales_return_request__deletion_forbidden_received_or_partially_received": "Deletion is not allowed. The sales return request is received or partially received.", "@sage/xtrem-sales/nodes__sales_return_request__improper_stock_and_sales_site_value__during_creation": "Incorrect stock site on the sales return request. The sales site and the stock site must have the same company.", "@sage/xtrem-sales/nodes__sales_return_request__mutation__approve": "Approve", "@sage/xtrem-sales/nodes__sales_return_request__mutation__approve__failed": "Approve failed.", "@sage/xtrem-sales/nodes__sales_return_request__mutation__approve__parameter__returnRequest": "Return request", "@sage/xtrem-sales/nodes__sales_return_request__mutation__confirm": "Confirm", "@sage/xtrem-sales/nodes__sales_return_request__mutation__confirm__failed": "Confirm failed.", "@sage/xtrem-sales/nodes__sales_return_request__mutation__confirm__parameter__salesReturnRequestNumber": "Sales return request number", "@sage/xtrem-sales/nodes__sales_return_request__mutation__createSalesCreditMemosFromReturnRequestLines": "Create sales credit memos from return request lines", "@sage/xtrem-sales/nodes__sales_return_request__mutation__createSalesCreditMemosFromReturnRequestLines__failed": "Create sales credit memos from return request lines failed.", "@sage/xtrem-sales/nodes__sales_return_request__mutation__createSalesCreditMemosFromReturnRequestLines__parameter__creditMemoDate": "Credit memo date", "@sage/xtrem-sales/nodes__sales_return_request__mutation__createSalesCreditMemosFromReturnRequestLines__parameter__salesDocumentLines": "Sales document lines", "@sage/xtrem-sales/nodes__sales_return_request__mutation__createSalesCreditMemosFromReturnRequests": "Create sales credit memos from return requests", "@sage/xtrem-sales/nodes__sales_return_request__mutation__createSalesCreditMemosFromReturnRequests__failed": "Create sales credit memos from return requests failed.", "@sage/xtrem-sales/nodes__sales_return_request__mutation__createSalesCreditMemosFromReturnRequests__parameter__creditMemoDate": "Credit memo date", "@sage/xtrem-sales/nodes__sales_return_request__mutation__createSalesCreditMemosFromReturnRequests__parameter__salesDocuments": "Sales documents", "@sage/xtrem-sales/nodes__sales_return_request__mutation__financeIntegrationCheck": "Finance integration check", "@sage/xtrem-sales/nodes__sales_return_request__mutation__financeIntegrationCheck__failed": "Finance integration check failed.", "@sage/xtrem-sales/nodes__sales_return_request__mutation__financeIntegrationCheck__parameter__salesReturnRequest": "Sales return request", "@sage/xtrem-sales/nodes__sales_return_request__mutation__reject": "Reject", "@sage/xtrem-sales/nodes__sales_return_request__mutation__reject__failed": "Reject failed.", "@sage/xtrem-sales/nodes__sales_return_request__mutation__reject__parameter__returnRequest": "Return request", "@sage/xtrem-sales/nodes__sales_return_request__mutation__setSalesReturnRequestCloseStatus": "Set sales return request close status", "@sage/xtrem-sales/nodes__sales_return_request__mutation__setSalesReturnRequestCloseStatus__failed": "Set sales return request close status failed.", "@sage/xtrem-sales/nodes__sales_return_request__mutation__setSalesReturnRequestCloseStatus__parameter__salesReturnRequestNumber": "Sales return request number", "@sage/xtrem-sales/nodes__sales_return_request__mutation__setSalesReturnRequestLineCloseStatus": "Set sales return request line close status", "@sage/xtrem-sales/nodes__sales_return_request__mutation__setSalesReturnRequestLineCloseStatus__failed": "Set sales return request line close status failed.", "@sage/xtrem-sales/nodes__sales_return_request__mutation__setSalesReturnRequestLineCloseStatus__parameter__salesReturnRequestLine": "Sales return request line", "@sage/xtrem-sales/nodes__sales_return_request__mutation__setSalesReturnRequestLineOpenStatus": "Set sales return request line open status", "@sage/xtrem-sales/nodes__sales_return_request__mutation__setSalesReturnRequestLineOpenStatus__failed": "Set sales return request line open status failed.", "@sage/xtrem-sales/nodes__sales_return_request__mutation__setSalesReturnRequestLineOpenStatus__parameter__newQuantityInSalesUnit": "New quantity in sales unit", "@sage/xtrem-sales/nodes__sales_return_request__mutation__setSalesReturnRequestLineOpenStatus__parameter__salesReturnRequestLine": "Sales return request line", "@sage/xtrem-sales/nodes__sales_return_request__mutation__setSalesReturnRequestOpenStatus": "Set sales return request open status", "@sage/xtrem-sales/nodes__sales_return_request__mutation__setSalesReturnRequestOpenStatus__failed": "Set sales return request open status failed.", "@sage/xtrem-sales/nodes__sales_return_request__mutation__setSalesReturnRequestOpenStatus__parameter__salesReturnRequestNumber": "Sales return request number", "@sage/xtrem-sales/nodes__sales_return_request__mutation__validateQuantityInSalesUnit": "Validate quantity in sales unit", "@sage/xtrem-sales/nodes__sales_return_request__mutation__validateQuantityInSalesUnit__failed": "Validate quantity in sales unit failed.", "@sage/xtrem-sales/nodes__sales_return_request__mutation__validateQuantityInSalesUnit__parameter__newQuantityInSalesUnit": "New quantity in sales unit", "@sage/xtrem-sales/nodes__sales_return_request__mutation__validateQuantityInSalesUnit__parameter__salesReturnRequestLine": "Sales return request line", "@sage/xtrem-sales/nodes__sales_return_request__node_name": "Sales return request", "@sage/xtrem-sales/nodes__sales_return_request__not_possible_to_decrease_the_line_quantity": "You cannot decrease the line quantity when the sales return request is 'Approved' or 'Rejected' and the quantity is received.", "@sage/xtrem-sales/nodes__sales_return_request__not_possible_to_increase_the_line_quantity": "You cannot increase the line quantity when the sales return request is 'Approved' or 'Rejected'.", "@sage/xtrem-sales/nodes__sales_return_request__property__billToAddress": "Bill to address", "@sage/xtrem-sales/nodes__sales_return_request__property__billToContact": "Bill to contact", "@sage/xtrem-sales/nodes__sales_return_request__property__billToCustomer": "Bill to customer", "@sage/xtrem-sales/nodes__sales_return_request__property__billToLinkedAddress": "Bill to linked address", "@sage/xtrem-sales/nodes__sales_return_request__property__creditStatus": "Credit status", "@sage/xtrem-sales/nodes__sales_return_request__property__date": "Date", "@sage/xtrem-sales/nodes__sales_return_request__property__deliveryMode": "Delivery mode", "@sage/xtrem-sales/nodes__sales_return_request__property__displayStatus": "Display status", "@sage/xtrem-sales/nodes__sales_return_request__property__incoterm": "Incoterm", "@sage/xtrem-sales/nodes__sales_return_request__property__internalNote": "Internal note", "@sage/xtrem-sales/nodes__sales_return_request__property__isCreditingAllowed": "Is crediting allowed", "@sage/xtrem-sales/nodes__sales_return_request__property__isTransferHeaderNote": "Is transfer header note", "@sage/xtrem-sales/nodes__sales_return_request__property__isTransferLineNote": "Is transfer line note", "@sage/xtrem-sales/nodes__sales_return_request__property__lines": "Lines", "@sage/xtrem-sales/nodes__sales_return_request__property__page": "Page", "@sage/xtrem-sales/nodes__sales_return_request__property__receiptStatus": "Receipt status", "@sage/xtrem-sales/nodes__sales_return_request__property__requester": "Requester", "@sage/xtrem-sales/nodes__sales_return_request__property__returnRequestDate": "Return request date", "@sage/xtrem-sales/nodes__sales_return_request__property__returnType": "Return type", "@sage/xtrem-sales/nodes__sales_return_request__property__salesSite": "Sales site", "@sage/xtrem-sales/nodes__sales_return_request__property__shipToAddress": "Ship to address", "@sage/xtrem-sales/nodes__sales_return_request__property__shipToContact": "Ship to contact", "@sage/xtrem-sales/nodes__sales_return_request__property__shipToCustomer": "Ship to customer", "@sage/xtrem-sales/nodes__sales_return_request__property__shipToCustomerAddress": "Ship to customer address", "@sage/xtrem-sales/nodes__sales_return_request__property__site": "Site", "@sage/xtrem-sales/nodes__sales_return_request__property__soldToAddress": "Sold to address", "@sage/xtrem-sales/nodes__sales_return_request__property__soldToContact": "Sold to contact", "@sage/xtrem-sales/nodes__sales_return_request__property__soldToCustomer": "Sold to customer", "@sage/xtrem-sales/nodes__sales_return_request__property__soldToLinkedAddress": "Sold to linked address", "@sage/xtrem-sales/nodes__sales_return_request__property__status": "Status", "@sage/xtrem-sales/nodes__sales_return_request__property__stockSite": "Stock site", "@sage/xtrem-sales/nodes__sales_return_request__query__getFilteredUsers": "Get filtered users", "@sage/xtrem-sales/nodes__sales_return_request__query__getFilteredUsers__failed": "Get filtered users failed.", "@sage/xtrem-sales/nodes__sales_return_request__query__getFilteredUsers__parameter__criteria": "Criteria", "@sage/xtrem-sales/nodes__sales_return_request__rejected": "The sales return request has been rejected.", "@sage/xtrem-sales/nodes__sales_return_request__remaining_quantity": "The sales return line quantity cannot be larger than the remaining quantity on the related sales shipment line.", "@sage/xtrem-sales/nodes__sales_return_request__return_request_date_cannot_be_future": "The return request date cannot be later than today.", "@sage/xtrem-sales/nodes__sales_return_request__under_the_quantity_already_credited": "The sales return line quantity cannot be lower than the already credited quantity.", "@sage/xtrem-sales/nodes__sales_return_request__under_the_quantity_already_received": "The sales return line quantity cannot be lower than the already received quantity.", "@sage/xtrem-sales/nodes__sales_return_request__update_line_cant_be_in_the_received_receipt_status": "Update not allowed. The sales return request line is received.", "@sage/xtrem-sales/nodes__sales_return_request_line__asyncMutation__asyncExport": "Export", "@sage/xtrem-sales/nodes__sales_return_request_line__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-sales/nodes__sales_return_request_line__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-sales/nodes__sales_return_request_line__deletion_forbidden_line_approved_or_rejected": "Deletion is not allowed. The sales return request line is approved or rejected.", "@sage/xtrem-sales/nodes__sales_return_request_line__deletion_forbidden_line_received_or_partially_received": "Deletion is not allowed. The sales return request line is received or partially received.", "@sage/xtrem-sales/nodes__sales_return_request_line__improper_status_during_creation": "Incorrect sales return request line status. The sales return request line must have the 'Draft' or 'Pending' status during creation.", "@sage/xtrem-sales/nodes__sales_return_request_line__modification_of_the_item_is_forbidden": "The item cannot be updated.", "@sage/xtrem-sales/nodes__sales_return_request_line__modification_of_the_sales_unit_is_forbidden": "The sales unit cannot be updated.", "@sage/xtrem-sales/nodes__sales_return_request_line__node_name": "Sales return request line", "@sage/xtrem-sales/nodes__sales_return_request_line__property__computedAttributes": "Computed attributes", "@sage/xtrem-sales/nodes__sales_return_request_line__property__creditStatus": "Credit status", "@sage/xtrem-sales/nodes__sales_return_request_line__property__document": "Document", "@sage/xtrem-sales/nodes__sales_return_request_line__property__internalNote": "Internal note", "@sage/xtrem-sales/nodes__sales_return_request_line__property__isCreditingAllowed": "Is crediting allowed", "@sage/xtrem-sales/nodes__sales_return_request_line__property__isCreditMemoExpected": "Is credit memo expected", "@sage/xtrem-sales/nodes__sales_return_request_line__property__isReceiptExpected": "Is receipt expected", "@sage/xtrem-sales/nodes__sales_return_request_line__property__item": "<PERSON><PERSON>", "@sage/xtrem-sales/nodes__sales_return_request_line__property__originDocumentType": "Origin document type", "@sage/xtrem-sales/nodes__sales_return_request_line__property__originShippingSite": "Origin shipping site", "@sage/xtrem-sales/nodes__sales_return_request_line__property__quantity": "Quantity", "@sage/xtrem-sales/nodes__sales_return_request_line__property__quantityCreditedInProgressInSalesUnit": "Quantity credited in progress in sales unit", "@sage/xtrem-sales/nodes__sales_return_request_line__property__quantityCreditedPostedInSalesUnit": "Quantity credited posted in sales unit", "@sage/xtrem-sales/nodes__sales_return_request_line__property__quantityInSalesUnit": "Quantity in sales unit", "@sage/xtrem-sales/nodes__sales_return_request_line__property__quantityInStockUnit": "Quantity in stock unit", "@sage/xtrem-sales/nodes__sales_return_request_line__property__quantityReceiptInSalesUnit": "Quantity receipt in sales unit", "@sage/xtrem-sales/nodes__sales_return_request_line__property__quantityToReceiveInProgressInSalesUnit": "Quantity to receive in progress in sales unit", "@sage/xtrem-sales/nodes__sales_return_request_line__property__reason": "Reason", "@sage/xtrem-sales/nodes__sales_return_request_line__property__receiptStatus": "Receipt status", "@sage/xtrem-sales/nodes__sales_return_request_line__property__salesCreditMemoLines": "Sales credit memo lines", "@sage/xtrem-sales/nodes__sales_return_request_line__property__salesReturnReceiptLines": "Sales return receipt lines", "@sage/xtrem-sales/nodes__sales_return_request_line__property__salesShipmentLines": "Sales shipment lines", "@sage/xtrem-sales/nodes__sales_return_request_line__property__salesUnit": "Sales unit", "@sage/xtrem-sales/nodes__sales_return_request_line__property__salesUnitToStockUnitConversionFactor": "Sales unit to stock unit conversion factor", "@sage/xtrem-sales/nodes__sales_return_request_line__property__stockUnit": "Stock unit", "@sage/xtrem-sales/nodes__sales_return_request_line__property__text": "Text", "@sage/xtrem-sales/nodes__sales_return_request_line__property__unit": "Unit", "@sage/xtrem-sales/nodes__sales_return_request_line__property__unitToStockUnitConversionFactor": "Unit to stock unit conversion factor", "@sage/xtrem-sales/nodes__sales_return_request_line__property__uQuantityReceiptAndToReceiveInSalesUnit": "U quantity receipt and to receive in sales unit", "@sage/xtrem-sales/nodes__sales_return_request_line__sales_return_request_lines_status_must_have_the_same_status": "The sales return request lines status must have the same status than the header.", "@sage/xtrem-sales/nodes__sales_return_request_line__sales_return_request_origin_shipping_site_must_have_the_same_value": "The origin shipping site on the sales return request lines must be the same as the stock site in the header.", "@sage/xtrem-sales/nodes__sales_return_request_line__sales_return_request_sales_origin_shipping_and_sales_site_value__during_creation": "Incorrect origin shipping site on the sales return request line. The origin shipping site on the sales return request line and the the header sales site must have the same company.", "@sage/xtrem-sales/nodes__sales_return_request_line__status_is_not_draft_or_the_approval_status_is_submitted": "Sales return request line creation not allowed. The sales return request status is not 'Draft', or the approval status is 'Submitted for approval'.", "@sage/xtrem-sales/nodes__sales_return_request_line__update_line_cant_be_in_the_closed_status": "Update not allowed. The sales return request line is closed.", "@sage/xtrem-sales/nodes__sales_return_request_line__update_line_cant_be_in_the_draft_status": "This document is at Pending status. You cannot set the sales return request line to draft.", "@sage/xtrem-sales/nodes__sales_return_request_line_sales_credit_memo_line__asyncMutation__asyncExport": "Export", "@sage/xtrem-sales/nodes__sales_return_request_line_sales_credit_memo_line__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-sales/nodes__sales_return_request_line_sales_credit_memo_line__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-sales/nodes__sales_return_request_line_sales_credit_memo_line__node_name": "Sales return request line sales credit memo line", "@sage/xtrem-sales/nodes__sales_return_request_line_sales_credit_memo_line__property__document": "Document", "@sage/xtrem-sales/nodes__sales_return_request_line_sales_credit_memo_line__property__linkedDocument": "Linked document", "@sage/xtrem-sales/nodes__sales_return_request_line_to_sales_return_receipt_line__asyncMutation__asyncExport": "Export", "@sage/xtrem-sales/nodes__sales_return_request_line_to_sales_return_receipt_line__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-sales/nodes__sales_return_request_line_to_sales_return_receipt_line__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-sales/nodes__sales_return_request_line_to_sales_return_receipt_line__node_name": "Sales return request line to sales return receipt line", "@sage/xtrem-sales/nodes__sales_return_request_line_to_sales_return_receipt_line__property__document": "Document", "@sage/xtrem-sales/nodes__sales_return_request_line_to_sales_return_receipt_line__property__linkedDocument": "Linked document", "@sage/xtrem-sales/nodes__sales_return_request_reason__asyncMutation__asyncExport": "Export", "@sage/xtrem-sales/nodes__sales_return_request_reason__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-sales/nodes__sales_return_request_reason__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-sales/nodes__sales_return_request_reason__node_name": "Sales return request reason", "@sage/xtrem-sales/nodes__sales_return_request_reason__property__description": "Description", "@sage/xtrem-sales/nodes__sales_return_request_reason__property__id": "Id", "@sage/xtrem-sales/nodes__sales_return_request_reason__property__isActive": "Is active", "@sage/xtrem-sales/nodes__sales_return_request_reason__property__name": "Name", "@sage/xtrem-sales/nodes__sales_return_request_update_not_allowed": "You cannot update the return type. You can only update it if the sales return request status is 'Draft' and if it has not been approved.", "@sage/xtrem-sales/nodes__sales_shipment__asyncMutation__asyncExport": "Export", "@sage/xtrem-sales/nodes__sales_shipment__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-sales/nodes__sales_shipment__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-sales/nodes__sales_shipment__asyncMutation__post": "Post", "@sage/xtrem-sales/nodes__sales_shipment__asyncMutation__post__failed": "Post failed.", "@sage/xtrem-sales/nodes__sales_shipment__asyncMutation__post__parameter__isSafeToRetry": "Is safe to retry", "@sage/xtrem-sales/nodes__sales_shipment__asyncMutation__post__parameter__salesShipment": "Sales shipment", "@sage/xtrem-sales/nodes__sales_shipment__bulkMutation__printBulkPackingSlip": "Print bulk packing slip", "@sage/xtrem-sales/nodes__sales_shipment__bulkMutation__printBulkPackingSlip__failed": "Print bulk packing slip failed.", "@sage/xtrem-sales/nodes__sales_shipment__bulkMutation__printBulkPickList": "Print bulk pick list", "@sage/xtrem-sales/nodes__sales_shipment__bulkMutation__printBulkPickList__failed": "Print bulk pick list failed.", "@sage/xtrem-sales/nodes__sales_shipment__create_sales_invoices_from_shipments__already_invoiced_or_not_posted": "All shipment lines must not be invoiced and must be posted.", "@sage/xtrem-sales/nodes__sales_shipment__creation_impossible_bill_to_customer_is_on_hold": "Creation is not allowed. The Bill-to customer is on hold.", "@sage/xtrem-sales/nodes__sales_shipment__deletion_forbidden_line_exists": "Deletion is not allowed. The sales shipment is ready to ship or has already been shipped.", "@sage/xtrem-sales/nodes__sales_shipment__fx_rate_not_found": "No exchange rate found.", "@sage/xtrem-sales/nodes__sales_shipment__mutation__addWorkDays": "Add work days", "@sage/xtrem-sales/nodes__sales_shipment__mutation__addWorkDays__failed": "Add work days failed.", "@sage/xtrem-sales/nodes__sales_shipment__mutation__addWorkDays__parameter__deliveryLeadTime": "Delivery lead time", "@sage/xtrem-sales/nodes__sales_shipment__mutation__addWorkDays__parameter__shippingDate": "Shipping date", "@sage/xtrem-sales/nodes__sales_shipment__mutation__addWorkDays__parameter__workDays": "Work days", "@sage/xtrem-sales/nodes__sales_shipment__mutation__afterPrintPackingSlip": "After print packing slip", "@sage/xtrem-sales/nodes__sales_shipment__mutation__afterPrintPackingSlip__failed": "After print packing slip failed.", "@sage/xtrem-sales/nodes__sales_shipment__mutation__afterPrintPackingSlip__parameter__shipment": "Shipment", "@sage/xtrem-sales/nodes__sales_shipment__mutation__beforePrintPackingSlip": "Before print packing slip", "@sage/xtrem-sales/nodes__sales_shipment__mutation__beforePrintPackingSlip__failed": "Before print packing slip failed.", "@sage/xtrem-sales/nodes__sales_shipment__mutation__beforePrintPackingSlip__parameter__shipment": "Shipment", "@sage/xtrem-sales/nodes__sales_shipment__mutation__beforePrintSalesShipmentPickList": "Before print sales shipment pick list", "@sage/xtrem-sales/nodes__sales_shipment__mutation__beforePrintSalesShipmentPickList__failed": "Before print sales shipment pick list failed.", "@sage/xtrem-sales/nodes__sales_shipment__mutation__beforePrintSalesShipmentPickList__parameter__shipment": "Shipment", "@sage/xtrem-sales/nodes__sales_shipment__mutation__confirm": "Confirm", "@sage/xtrem-sales/nodes__sales_shipment__mutation__confirm__failed": "Confirm failed.", "@sage/xtrem-sales/nodes__sales_shipment__mutation__confirm__parameter__isSafeToRetry": "Is safe to retry", "@sage/xtrem-sales/nodes__sales_shipment__mutation__confirm__parameter__salesShipment": "Sales shipment", "@sage/xtrem-sales/nodes__sales_shipment__mutation__createSalesInvoicesFromShipment": "Create sales invoices from shipment", "@sage/xtrem-sales/nodes__sales_shipment__mutation__createSalesInvoicesFromShipment__failed": "Create sales invoices from shipment failed.", "@sage/xtrem-sales/nodes__sales_shipment__mutation__createSalesInvoicesFromShipment__parameter__invoiceDate": "Invoice date", "@sage/xtrem-sales/nodes__sales_shipment__mutation__createSalesInvoicesFromShipment__parameter__isSafeToRetry": "Is safe to retry", "@sage/xtrem-sales/nodes__sales_shipment__mutation__createSalesInvoicesFromShipment__parameter__salesShipment": "Sales shipment", "@sage/xtrem-sales/nodes__sales_shipment__mutation__createSalesInvoicesFromShipmentLines": "Create sales invoices from shipment lines", "@sage/xtrem-sales/nodes__sales_shipment__mutation__createSalesInvoicesFromShipmentLines__failed": "Create sales invoices from shipment lines failed.", "@sage/xtrem-sales/nodes__sales_shipment__mutation__createSalesInvoicesFromShipmentLines__parameter__invoiceDate": "Invoice date", "@sage/xtrem-sales/nodes__sales_shipment__mutation__createSalesInvoicesFromShipmentLines__parameter__salesDocumentLines": "Sales document lines", "@sage/xtrem-sales/nodes__sales_shipment__mutation__createSalesReturnRequestFromShipmentLines": "Create sales return request from shipment lines", "@sage/xtrem-sales/nodes__sales_shipment__mutation__createSalesReturnRequestFromShipmentLines__failed": "Create sales return request from shipment lines failed.", "@sage/xtrem-sales/nodes__sales_shipment__mutation__createSalesReturnRequestFromShipmentLines__parameter__salesDocumentLines": "Sales document lines", "@sage/xtrem-sales/nodes__sales_shipment__mutation__createSalesReturnRequestFromShipments": "Create sales return request from shipments", "@sage/xtrem-sales/nodes__sales_shipment__mutation__createSalesReturnRequestFromShipments__failed": "Create sales return request from shipments failed.", "@sage/xtrem-sales/nodes__sales_shipment__mutation__createSalesReturnRequestFromShipments__parameter__salesDocuments": "Sales documents", "@sage/xtrem-sales/nodes__sales_shipment__mutation__massCreateSalesInvoices": "Mass create sales invoices", "@sage/xtrem-sales/nodes__sales_shipment__mutation__massCreateSalesInvoices__failed": "Mass create sales invoices failed.", "@sage/xtrem-sales/nodes__sales_shipment__mutation__massCreateSalesInvoices__parameter__fromBillToCustomer": "From bill to customer", "@sage/xtrem-sales/nodes__sales_shipment__mutation__massCreateSalesInvoices__parameter__fromShipment": "From shipment", "@sage/xtrem-sales/nodes__sales_shipment__mutation__massCreateSalesInvoices__parameter__invoiceDate": "Invoice date", "@sage/xtrem-sales/nodes__sales_shipment__mutation__massCreateSalesInvoices__parameter__shipmentUntilDate": "Shipment until date", "@sage/xtrem-sales/nodes__sales_shipment__mutation__massCreateSalesInvoices__parameter__site": "Site", "@sage/xtrem-sales/nodes__sales_shipment__mutation__massCreateSalesInvoices__parameter__toBillToCustomer": "To bill to customer", "@sage/xtrem-sales/nodes__sales_shipment__mutation__massCreateSalesInvoices__parameter__toShipment": "To shipment", "@sage/xtrem-sales/nodes__sales_shipment__mutation__postToStock": "Post to stock", "@sage/xtrem-sales/nodes__sales_shipment__mutation__postToStock__failed": "Post to stock failed.", "@sage/xtrem-sales/nodes__sales_shipment__mutation__postToStock__parameter__isSafeToRetry": "Is safe to retry", "@sage/xtrem-sales/nodes__sales_shipment__mutation__postToStock__parameter__salesShipment": "Sales shipment", "@sage/xtrem-sales/nodes__sales_shipment__mutation__repost": "Repost", "@sage/xtrem-sales/nodes__sales_shipment__mutation__repost__failed": "Repost failed.", "@sage/xtrem-sales/nodes__sales_shipment__mutation__repost__parameter__documentLines": "Document lines", "@sage/xtrem-sales/nodes__sales_shipment__mutation__repost__parameter__salesShipment": "Sales shipment", "@sage/xtrem-sales/nodes__sales_shipment__mutation__resendNotificationForFinance": "Resend notification for finance", "@sage/xtrem-sales/nodes__sales_shipment__mutation__resendNotificationForFinance__failed": "Resend notification for finance failed.", "@sage/xtrem-sales/nodes__sales_shipment__mutation__resendNotificationForFinance__parameter__salesShipment": "Sales shipment", "@sage/xtrem-sales/nodes__sales_shipment__mutation__resynchronizeStatus": "Resynchronize status", "@sage/xtrem-sales/nodes__sales_shipment__mutation__resynchronizeStatus__failed": "Resynchronize status failed.", "@sage/xtrem-sales/nodes__sales_shipment__mutation__resynchronizeStatus__parameter__salesShipment": "Sales shipment", "@sage/xtrem-sales/nodes__sales_shipment__mutation__revoke": "Revoke", "@sage/xtrem-sales/nodes__sales_shipment__mutation__revoke__failed": "Revoke failed.", "@sage/xtrem-sales/nodes__sales_shipment__mutation__revoke__parameter___id": "Id", "@sage/xtrem-sales/nodes__sales_shipment__no_invoices_were_created": "No invoices were created for the sales shipment.", "@sage/xtrem-sales/nodes__sales_shipment__node_name": "Sales shipment", "@sage/xtrem-sales/nodes__sales_shipment__property__allocationStatus": "Allocation status", "@sage/xtrem-sales/nodes__sales_shipment__property__billToAddress": "Bill to address", "@sage/xtrem-sales/nodes__sales_shipment__property__billToContact": "Bill to contact", "@sage/xtrem-sales/nodes__sales_shipment__property__billToCustomer": "Bill to customer", "@sage/xtrem-sales/nodes__sales_shipment__property__billToLinkedAddress": "Bill to linked address", "@sage/xtrem-sales/nodes__sales_shipment__property__companyFxRate": "Company fx rate", "@sage/xtrem-sales/nodes__sales_shipment__property__companyFxRateDivisor": "Company fx rate divisor", "@sage/xtrem-sales/nodes__sales_shipment__property__currency": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-sales/nodes__sales_shipment__property__date": "Date", "@sage/xtrem-sales/nodes__sales_shipment__property__deliveryDate": "Delivery date", "@sage/xtrem-sales/nodes__sales_shipment__property__deliveryLeadTime": "Delivery lead time", "@sage/xtrem-sales/nodes__sales_shipment__property__deliveryMode": "Delivery mode", "@sage/xtrem-sales/nodes__sales_shipment__property__displayStatus": "Display status", "@sage/xtrem-sales/nodes__sales_shipment__property__effectiveDate": "Effective date", "@sage/xtrem-sales/nodes__sales_shipment__property__externalNote": "External note", "@sage/xtrem-sales/nodes__sales_shipment__property__fxRateDate": "Fx rate date", "@sage/xtrem-sales/nodes__sales_shipment__property__incoterm": "Incoterm", "@sage/xtrem-sales/nodes__sales_shipment__property__internalNote": "Internal note", "@sage/xtrem-sales/nodes__sales_shipment__property__invoiceStatus": "Invoice status", "@sage/xtrem-sales/nodes__sales_shipment__property__isExternalNote": "Is external note", "@sage/xtrem-sales/nodes__sales_shipment__property__isOnHold": "Is on hold", "@sage/xtrem-sales/nodes__sales_shipment__property__isTransferHeaderNote": "Is transfer header note", "@sage/xtrem-sales/nodes__sales_shipment__property__isTransferLineNote": "Is transfer line note", "@sage/xtrem-sales/nodes__sales_shipment__property__lines": "Lines", "@sage/xtrem-sales/nodes__sales_shipment__property__paymentTerm": "Payment term", "@sage/xtrem-sales/nodes__sales_shipment__property__postingDetails": "Posting details", "@sage/xtrem-sales/nodes__sales_shipment__property__rateDescription": "Rate description", "@sage/xtrem-sales/nodes__sales_shipment__property__recordUrl": "Record url", "@sage/xtrem-sales/nodes__sales_shipment__property__reference": "Reference", "@sage/xtrem-sales/nodes__sales_shipment__property__returnReceiptStatus": "Return receipt status", "@sage/xtrem-sales/nodes__sales_shipment__property__returnRequestStatus": "Return request status", "@sage/xtrem-sales/nodes__sales_shipment__property__salesSite": "Sales site", "@sage/xtrem-sales/nodes__sales_shipment__property__shippingDate": "Shipping date", "@sage/xtrem-sales/nodes__sales_shipment__property__shipToAddress": "Ship to address", "@sage/xtrem-sales/nodes__sales_shipment__property__shipToContact": "Ship to contact", "@sage/xtrem-sales/nodes__sales_shipment__property__shipToCustomer": "Ship to customer", "@sage/xtrem-sales/nodes__sales_shipment__property__shipToCustomerAddress": "Ship to customer address", "@sage/xtrem-sales/nodes__sales_shipment__property__site": "Site", "@sage/xtrem-sales/nodes__sales_shipment__property__status": "Status", "@sage/xtrem-sales/nodes__sales_shipment__property__stockSite": "Stock site", "@sage/xtrem-sales/nodes__sales_shipment__property__stockTransactionStatus": "Stock transaction status", "@sage/xtrem-sales/nodes__sales_shipment__property__totalAmountExcludingTax": "Total amount excluding tax", "@sage/xtrem-sales/nodes__sales_shipment__property__totalAmountExcludingTaxInCompanyCurrency": "Total amount excluding tax in company currency", "@sage/xtrem-sales/nodes__sales_shipment__property__totalGrossProfit": "Total gross profit", "@sage/xtrem-sales/nodes__sales_shipment__property__trackingNumber": "Tracking number", "@sage/xtrem-sales/nodes__sales_shipment__property__workDays": "Work days", "@sage/xtrem-sales/nodes__sales_shipment__sales_shipments_revoked_success": "The sales shipment status has been reverted and set to 'Ready to process'.", "@sage/xtrem-sales/nodes__sales_shipment__shipment__cannot_confirm_allocate_all_lines": "You need to allocate all lines before confirming.", "@sage/xtrem-sales/nodes__sales_shipment__shipment_confirmed_ready_to_process": "A shipment can only be confirmed if the status is 'Ready to process'.", "@sage/xtrem-sales/nodes__sales_shipment__shipment_revoked_ready_to_ship": "A shipment can only be reverted if the status is 'Ready to ship'.", "@sage/xtrem-sales/nodes__sales_shipment__stock_site_is_required": "The stock site of the sales shipment is required.", "@sage/xtrem-sales/nodes__sales_shipment__stock_update_not_finished": "The stock update is still in progress. You can resync after it has completed.", "@sage/xtrem-sales/nodes__sales_shipment__update_not_allowed_posting_in_progress": "The document is shipped. You cannot revert the status to: {{newStatus}}.", "@sage/xtrem-sales/nodes__sales_shipment__update_not_allowed_status_closed": "The sales shipment is closed. You cannot update it.", "@sage/xtrem-sales/nodes__sales_shipment_line__asyncMutation__asyncExport": "Export", "@sage/xtrem-sales/nodes__sales_shipment_line__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-sales/nodes__sales_shipment_line__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-sales/nodes__sales_shipment_line__creation_impossible_bill_to_customer_is_on_hold": "Creation is not allowed. The Bill-to customer is on hold.", "@sage/xtrem-sales/nodes__sales_shipment_line__deletion_forbidden_line_exists": "Deletion is not allowed. The sales shipment line is ready to ship or has already been shipped.", "@sage/xtrem-sales/nodes__sales_shipment_line__item_update_not_allowed": "You cannot update the item.", "@sage/xtrem-sales/nodes__sales_shipment_line__node_name": "Sales shipment line", "@sage/xtrem-sales/nodes__sales_shipment_line__property__allocationStatus": "Allocation status", "@sage/xtrem-sales/nodes__sales_shipment_line__property__amountExcludingTax": "Amount excluding tax", "@sage/xtrem-sales/nodes__sales_shipment_line__property__amountExcludingTaxInCompanyCurrency": "Amount excluding tax in company currency", "@sage/xtrem-sales/nodes__sales_shipment_line__property__charge": "Charge", "@sage/xtrem-sales/nodes__sales_shipment_line__property__computedAttributes": "Computed attributes", "@sage/xtrem-sales/nodes__sales_shipment_line__property__currency": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-sales/nodes__sales_shipment_line__property__customerNumber": "Customer number", "@sage/xtrem-sales/nodes__sales_shipment_line__property__discount": "Discount", "@sage/xtrem-sales/nodes__sales_shipment_line__property__discountCharges": "Discount charges", "@sage/xtrem-sales/nodes__sales_shipment_line__property__document": "Document", "@sage/xtrem-sales/nodes__sales_shipment_line__property__documentId": "Document id", "@sage/xtrem-sales/nodes__sales_shipment_line__property__documentNumber": "Document number", "@sage/xtrem-sales/nodes__sales_shipment_line__property__externalNote": "External note", "@sage/xtrem-sales/nodes__sales_shipment_line__property__grossPrice": "Gross price", "@sage/xtrem-sales/nodes__sales_shipment_line__property__grossPriceDeterminated": "Gross price determinated", "@sage/xtrem-sales/nodes__sales_shipment_line__property__grossProfit": "Gross profit", "@sage/xtrem-sales/nodes__sales_shipment_line__property__grossProfitAmount": "Gross profit amount", "@sage/xtrem-sales/nodes__sales_shipment_line__property__grossProfitAmountInCompanyCurrency": "Gross profit amount in company currency", "@sage/xtrem-sales/nodes__sales_shipment_line__property__internalNote": "Internal note", "@sage/xtrem-sales/nodes__sales_shipment_line__property__invoiceStatus": "Invoice status", "@sage/xtrem-sales/nodes__sales_shipment_line__property__isExternalNote": "Is external note", "@sage/xtrem-sales/nodes__sales_shipment_line__property__isPriceDeterminated": "Is price determinated", "@sage/xtrem-sales/nodes__sales_shipment_line__property__item": "<PERSON><PERSON>", "@sage/xtrem-sales/nodes__sales_shipment_line__property__netPrice": "Net price", "@sage/xtrem-sales/nodes__sales_shipment_line__property__originDocumentType": "Origin document type", "@sage/xtrem-sales/nodes__sales_shipment_line__property__priceOrigin": "Price origin", "@sage/xtrem-sales/nodes__sales_shipment_line__property__priceOriginDeterminated": "Price origin determinated", "@sage/xtrem-sales/nodes__sales_shipment_line__property__priceReason": "Price reason", "@sage/xtrem-sales/nodes__sales_shipment_line__property__priceReasonDeterminated": "Price reason determinated", "@sage/xtrem-sales/nodes__sales_shipment_line__property__quantity": "Quantity", "@sage/xtrem-sales/nodes__sales_shipment_line__property__quantityAllocated": "Quantity allocated", "@sage/xtrem-sales/nodes__sales_shipment_line__property__quantityInSalesUnit": "Quantity in sales unit", "@sage/xtrem-sales/nodes__sales_shipment_line__property__quantityInStockUnit": "Quantity in stock unit", "@sage/xtrem-sales/nodes__sales_shipment_line__property__quantityInvoicedInProgressInSalesUnit": "Quantity invoiced in progress in sales unit", "@sage/xtrem-sales/nodes__sales_shipment_line__property__quantityInvoicedPostedInSalesUnit": "Quantity invoiced posted in sales unit", "@sage/xtrem-sales/nodes__sales_shipment_line__property__quantityReceiptInSalesUnit": "Quantity receipt in sales unit", "@sage/xtrem-sales/nodes__sales_shipment_line__property__quantityRequestedInSalesUnit": "Quantity requested in sales unit", "@sage/xtrem-sales/nodes__sales_shipment_line__property__remainingQuantity": "Remaining quantity", "@sage/xtrem-sales/nodes__sales_shipment_line__property__remainingQuantityToAllocate": "Remaining quantity to allocate", "@sage/xtrem-sales/nodes__sales_shipment_line__property__returnReceiptStatus": "Return receipt status", "@sage/xtrem-sales/nodes__sales_shipment_line__property__returnRequestStatus": "Return request status", "@sage/xtrem-sales/nodes__sales_shipment_line__property__salesOrderLines": "Sales order lines", "@sage/xtrem-sales/nodes__sales_shipment_line__property__salesReturnReceiptLines": "Sales return receipt lines", "@sage/xtrem-sales/nodes__sales_shipment_line__property__salesUnit": "Sales unit", "@sage/xtrem-sales/nodes__sales_shipment_line__property__salesUnitToStockUnitConversionFactor": "Sales unit to stock unit conversion factor", "@sage/xtrem-sales/nodes__sales_shipment_line__property__status": "Status", "@sage/xtrem-sales/nodes__sales_shipment_line__property__stockAllocations": "Stock allocations", "@sage/xtrem-sales/nodes__sales_shipment_line__property__stockCostAmount": "Stock cost amount", "@sage/xtrem-sales/nodes__sales_shipment_line__property__stockCostAmountInCompanyCurrency": "Stock cost amount in company currency", "@sage/xtrem-sales/nodes__sales_shipment_line__property__stockCostUnit": "Stock cost unit", "@sage/xtrem-sales/nodes__sales_shipment_line__property__stockDetails": "Stock details", "@sage/xtrem-sales/nodes__sales_shipment_line__property__stockMovements": "Stock movements", "@sage/xtrem-sales/nodes__sales_shipment_line__property__stockTransactions": "Stock transactions", "@sage/xtrem-sales/nodes__sales_shipment_line__property__stockTransactionStatus": "Stock transaction status", "@sage/xtrem-sales/nodes__sales_shipment_line__property__stockUnit": "Stock unit", "@sage/xtrem-sales/nodes__sales_shipment_line__property__toInvoiceLines": "To invoice lines", "@sage/xtrem-sales/nodes__sales_shipment_line__property__unit": "Unit", "@sage/xtrem-sales/nodes__sales_shipment_line__property__unitToStockUnitConversionFactor": "Unit to stock unit conversion factor", "@sage/xtrem-sales/nodes__sales_shipment_line__quantity_update_not_allowed_status_readyToShip": "The sales shipment is ready to ship. You cannot update the quantity.", "@sage/xtrem-sales/nodes__sales_shipment_line__sales_unit_update_not_allowed": "You cannot update the sales unit.", "@sage/xtrem-sales/nodes__sales_shipment_line__too_much_quantity_allocated,": "The allocated quantity on the sales shipment line cannot be larger than the shipping quantity.", "@sage/xtrem-sales/nodes__sales_shipment_line_discount_charge__asyncMutation__asyncExport": "Export", "@sage/xtrem-sales/nodes__sales_shipment_line_discount_charge__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-sales/nodes__sales_shipment_line_discount_charge__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-sales/nodes__sales_shipment_line_discount_charge__node_name": "Sales shipment line discount charge", "@sage/xtrem-sales/nodes__sales_shipment_line_discount_charge__property__basis": "<PERSON><PERSON>", "@sage/xtrem-sales/nodes__sales_shipment_line_discount_charge__property__document": "Document", "@sage/xtrem-sales/nodes__sales_shipment_line_to_sales_invoice_line__asyncMutation__asyncExport": "Export", "@sage/xtrem-sales/nodes__sales_shipment_line_to_sales_invoice_line__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-sales/nodes__sales_shipment_line_to_sales_invoice_line__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-sales/nodes__sales_shipment_line_to_sales_invoice_line__node_name": "Sales shipment line to sales invoice line", "@sage/xtrem-sales/nodes__sales_shipment_line_to_sales_invoice_line__property__document": "Document", "@sage/xtrem-sales/nodes__sales_shipment_line_to_sales_invoice_line__property__linkedDocument": "Linked document", "@sage/xtrem-sales/nodes__sales_shipment_line_to_sales_return_receipt_line__asyncMutation__asyncExport": "Export", "@sage/xtrem-sales/nodes__sales_shipment_line_to_sales_return_receipt_line__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-sales/nodes__sales_shipment_line_to_sales_return_receipt_line__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-sales/nodes__sales_shipment_line_to_sales_return_receipt_line__node_name": "Sales shipment line to sales return receipt line", "@sage/xtrem-sales/nodes__sales_shipment_line_to_sales_return_receipt_line__property__document": "Document", "@sage/xtrem-sales/nodes__sales_shipment_line_to_sales_return_receipt_line__property__linkedDocument": "Linked document", "@sage/xtrem-sales/nodes__sales_shipment_line_to_sales_return_request_line__asyncMutation__asyncExport": "Export", "@sage/xtrem-sales/nodes__sales_shipment_line_to_sales_return_request_line__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-sales/nodes__sales_shipment_line_to_sales_return_request_line__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-sales/nodes__sales_shipment_line_to_sales_return_request_line__node_name": "Sales shipment line to sales return request line", "@sage/xtrem-sales/nodes__sales_shipment_line_to_sales_return_request_line__property__document": "Document", "@sage/xtrem-sales/nodes__sales_shipment_line_to_sales_return_request_line__property__linkedDocument": "Linked document", "@sage/xtrem-sales/nodes__sales_shipment_lines__creation_forbidden_improper_status": "Line creation is not allowed. The sales shipment is ready to ship or has already been shipped.", "@sage/xtrem-sales/nodes__sales-invoice__cant_repost_sales_invoice_when_status_is_not_failed": "You can only repost a sales invoice if the status is 'Failed' or 'Not recorded'.", "@sage/xtrem-sales/nodes__sales-invoice__document_was_posted": "The sales invoice was posted.", "@sage/xtrem-sales/nodes__sales-return-receipt__cant_post_sales_return_receipt_when_stock_detail_status_is_required": "You need to enter stock details for all lines before you can post.", "@sage/xtrem-sales/nodes__sales-return-receipt__cant_repost_sales_return_receipt_when_status_is_not_failed": "You can only repost a sales return receipt if the status is 'Failed.'", "@sage/xtrem-sales/nodes__sales-return-receipt__document_was_posted": "The sales return receipt has been posted.", "@sage/xtrem-sales/nodes__sales-shipment__cant_repost_sales_shipment_when_status_is_not_failed": "You can only repost a sales shipment if the status is 'Failed' or 'Not recorded.'", "@sage/xtrem-sales/nodes__sales-shipment__document_was_posted": "The sales shipment has been posted.", "@sage/xtrem-sales/nodes__site_extension__check_pending_sales_return_requests": "Pending sales return requests need to be handled before disabling the approval process", "@sage/xtrem-sales/nodes__unbilled_account_receivable_input_set__asyncMutation__asyncExport": "Export", "@sage/xtrem-sales/nodes__unbilled_account_receivable_input_set__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-sales/nodes__unbilled_account_receivable_input_set__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-sales/nodes__unbilled_account_receivable_input_set__asyncMutation__unbilledAccountReceivableInquiry": "Unbilled account receivable inquiry", "@sage/xtrem-sales/nodes__unbilled_account_receivable_input_set__asyncMutation__unbilledAccountReceivableInquiry__failed": "Unbilled account receivable inquiry failed.", "@sage/xtrem-sales/nodes__unbilled_account_receivable_input_set__asyncMutation__unbilledAccountReceivableInquiry__parameter__userId": "User id", "@sage/xtrem-sales/nodes__unbilled_account_receivable_input_set__node_name": "Unbilled account receivable input set", "@sage/xtrem-sales/nodes__unbilled_account_receivable_input_set__property__asOfDate": "As of date", "@sage/xtrem-sales/nodes__unbilled_account_receivable_input_set__property__company": "Company", "@sage/xtrem-sales/nodes__unbilled_account_receivable_input_set__property__executionDate": "Execution date", "@sage/xtrem-sales/nodes__unbilled_account_receivable_input_set__property__fromCustomer": "From customer", "@sage/xtrem-sales/nodes__unbilled_account_receivable_input_set__property__lines": "Lines", "@sage/xtrem-sales/nodes__unbilled_account_receivable_input_set__property__sites": "Sites", "@sage/xtrem-sales/nodes__unbilled_account_receivable_input_set__property__status": "Status", "@sage/xtrem-sales/nodes__unbilled_account_receivable_input_set__property__toCustomer": "To customer", "@sage/xtrem-sales/nodes__unbilled_account_receivable_input_set__property__user": "User", "@sage/xtrem-sales/nodes__unbilled_account_receivable_result_line__asyncMutation__asyncExport": "Export", "@sage/xtrem-sales/nodes__unbilled_account_receivable_result_line__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-sales/nodes__unbilled_account_receivable_result_line__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-sales/nodes__unbilled_account_receivable_result_line__node_name": "Unbilled account receivable result line", "@sage/xtrem-sales/nodes__unbilled_account_receivable_result_line__property__account": "Account", "@sage/xtrem-sales/nodes__unbilled_account_receivable_result_line__property__accountItem": "Account item", "@sage/xtrem-sales/nodes__unbilled_account_receivable_result_line__property__company": "Company", "@sage/xtrem-sales/nodes__unbilled_account_receivable_result_line__property__companyCurrency": "Company currency", "@sage/xtrem-sales/nodes__unbilled_account_receivable_result_line__property__creditedQuantity": "Credited quantity", "@sage/xtrem-sales/nodes__unbilled_account_receivable_result_line__property__currency": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-sales/nodes__unbilled_account_receivable_result_line__property__customer": "Customer", "@sage/xtrem-sales/nodes__unbilled_account_receivable_result_line__property__documentDate": "Document date", "@sage/xtrem-sales/nodes__unbilled_account_receivable_result_line__property__financialSite": "Financial site", "@sage/xtrem-sales/nodes__unbilled_account_receivable_result_line__property__inputSet": "Input set", "@sage/xtrem-sales/nodes__unbilled_account_receivable_result_line__property__invoicedQuantity": "Invoiced quantity", "@sage/xtrem-sales/nodes__unbilled_account_receivable_result_line__property__invoiceIssuableAmount": "Invoice issuable amount", "@sage/xtrem-sales/nodes__unbilled_account_receivable_result_line__property__invoiceIssuableAmountInCompanyCurrency": "Invoice issuable amount in company currency", "@sage/xtrem-sales/nodes__unbilled_account_receivable_result_line__property__invoiceIssuableAmountInCompanyCurrencyAtAsOfDate": "Invoice issuable amount in company currency at as of date", "@sage/xtrem-sales/nodes__unbilled_account_receivable_result_line__property__invoiceIssuableQuantity": "Invoice issuable quantity", "@sage/xtrem-sales/nodes__unbilled_account_receivable_result_line__property__item": "<PERSON><PERSON>", "@sage/xtrem-sales/nodes__unbilled_account_receivable_result_line__property__netPrice": "Net price", "@sage/xtrem-sales/nodes__unbilled_account_receivable_result_line__property__quantity": "Quantity", "@sage/xtrem-sales/nodes__unbilled_account_receivable_result_line__property__returnedQuantity": "Returned quantity", "@sage/xtrem-sales/nodes__unbilled_account_receivable_result_line__property__salesUnit": "Sales unit", "@sage/xtrem-sales/nodes__unbilled_account_receivable_result_line__property__shipmentInternalId": "Shipment internal id", "@sage/xtrem-sales/nodes__unbilled_account_receivable_result_line__property__shipmentNumber": "Shipment number", "@sage/xtrem-sales/nodes__unbilled_account_receivable_result_line__property__shipToCustomer": "Ship to customer", "@sage/xtrem-sales/nodes__unbilled_account_receivable_result_line__property__site": "Site", "@sage/xtrem-sales/nodes__unbilled_account_receivable-input-set__success_message": "Success", "@sage/xtrem-sales/nodes__unbilled_account_receivable-input-set__success_notification_title": "Unbilled accounts receivable calculation complete", "@sage/xtrem-sales/nodes__work_in_progress_sales_order_line__asyncMutation__asyncExport": "Export", "@sage/xtrem-sales/nodes__work_in_progress_sales_order_line__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-sales/nodes__work_in_progress_sales_order_line__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-sales/nodes__work_in_progress_sales_order_line__node_name": "Work in progress sales order line", "@sage/xtrem-sales/nodes__work_in_progress_sales_order_line__property__actualQuantity": "Actual quantity", "@sage/xtrem-sales/nodes__work_in_progress_sales_order_line__property__documentId": "Document id", "@sage/xtrem-sales/nodes__work_in_progress_sales_order_line__property__documentLine": "Document line", "@sage/xtrem-sales/nodes__work_in_progress_sales_order_line__property__documentNumber": "Document number", "@sage/xtrem-sales/nodes__work_in_progress_sales_order_line__property__documentType": "Document type", "@sage/xtrem-sales/nodes__work_in_progress_sales_order_line__property__endDate": "End date", "@sage/xtrem-sales/nodes__work_in_progress_sales_order_line__property__expectedQuantity": "Expected quantity", "@sage/xtrem-sales/nodes__work_in_progress_sales_order_line__property__item": "<PERSON><PERSON>", "@sage/xtrem-sales/nodes__work_in_progress_sales_order_line__property__originDocumentType": "Origin document type", "@sage/xtrem-sales/nodes__work_in_progress_sales_order_line__property__outstandingQuantity": "Outstanding quantity", "@sage/xtrem-sales/nodes__work_in_progress_sales_order_line__property__remainingQuantityToAllocate": "Remaining quantity to allocate", "@sage/xtrem-sales/nodes__work_in_progress_sales_order_line__property__salesOrderLine": "Sales order line", "@sage/xtrem-sales/nodes__work_in_progress_sales_order_line__property__site": "Site", "@sage/xtrem-sales/nodes__work_in_progress_sales_order_line__property__startDate": "Start date", "@sage/xtrem-sales/nodes__work_in_progress_sales_order_line__property__status": "Status", "@sage/xtrem-sales/nodes_sales_line_on_hold_blocking": "Bill to customer is on hold.", "@sage/xtrem-sales/nodes_sales_on_hold_blocking": "Bill to customer is on hold.", "@sage/xtrem-sales/openOrder____title": "Open order", "@sage/xtrem-sales/openRequest____title": "Open request", "@sage/xtrem-sales/order_to_order__create_purchase_dialog_title": "Confirm order quantity", "@sage/xtrem-sales/order_to_shipment__confirm_lower_quantity": "You are about to create one or more order shipment lines with a quantity less than the ordered quantity.", "@sage/xtrem-sales/package__name": "Sage xtrem sales", "@sage/xtrem-sales/page__at_least_one_mandatory_tax_code_not_found": "At least one mandatory tax code is missing on the line.", "@sage/xtrem-sales/page__sales_invoice__no_rate_found": "The price could not been determined from the item sales base price. The currency rate between the sales base price currency and the document currency is missing.", "@sage/xtrem-sales/page__sales_order__auto_allocation_cannot_decrease_quantity": "You can only reduce the quantity of the line after the allocation request is complete.", "@sage/xtrem-sales/page__sales_order_line_panel__no_rate_found": "The price could not been determined from the item sales base price. The currency rate between the sales base price currency and the document currency is missing.", "@sage/xtrem-sales/page__sales_order_table_panel_lines_fail_validation_dates": "If an order line is not in the list, you need to go back to fix any errors in the delivery details.", "@sage/xtrem-sales/page__sales_order_table_panel_lines_fail_validation_dates_delivery_address": "If an order line is not in the list, you need to go back to fix any errors in the delivery details and you need to make sure the shipping addresses match.", "@sage/xtrem-sales/page__sales_order_table_panel_lines_fail_validation_delivery_address": "If an order line is not in the list, you need to make sure the shipping addresses match.", "@sage/xtrem-sales/page__sales_shipment_line__salesUnit_update_not_allowed_status_readyToShip": "The sales shipment is ready to ship. You cannot update the sales unit.", "@sage/xtrem-sales/page-extensions__allocation_result_extension__deliveryMode____title": "Delivery mode", "@sage/xtrem-sales/page-extensions__allocation_result_extension__fromSoldToCustomer____title": "From sold-to customer", "@sage/xtrem-sales/page-extensions__allocation_result_extension__incoterm____title": "Incoterms rule", "@sage/xtrem-sales/page-extensions__allocation_result_extension__latestShippingDate____title": "Latest shipping date", "@sage/xtrem-sales/page-extensions__allocation_result_extension__toSoldToCustomer____title": "To sold-to customer", "@sage/xtrem-sales/page-extensions__customer_extension____navigationPanel__dropdownActions__title": "Create order", "@sage/xtrem-sales/page-extensions__customer_extension__createOrder____title": "Create order", "@sage/xtrem-sales/page-extensions__site_extension__isSalesReturnRequestApprovalManaged____title": "Return request", "@sage/xtrem-sales/page-extensions__site_extension__salesDefaultApproverBlock____title": "Sales approval", "@sage/xtrem-sales/page-extensions__site_extension__salesReturnRequestDefaultApprover____columns__title__email": "Email", "@sage/xtrem-sales/page-extensions__site_extension__salesReturnRequestDefaultApprover____columns__title__firstName": "First name", "@sage/xtrem-sales/page-extensions__site_extension__salesReturnRequestDefaultApprover____columns__title__lastName": "Last name", "@sage/xtrem-sales/page-extensions__site_extension__salesReturnRequestDefaultApprover____lookupDialogTitle": "Select approver", "@sage/xtrem-sales/page-extensions__site_extension__salesReturnRequestDefaultApprover____title": "Approver", "@sage/xtrem-sales/page-extensions__site_extension__salesReturnRequestSubstituteApprover____columns__title__email": "Email", "@sage/xtrem-sales/page-extensions__site_extension__salesReturnRequestSubstituteApprover____columns__title__firstName": "First name", "@sage/xtrem-sales/page-extensions__site_extension__salesReturnRequestSubstituteApprover____columns__title__lastName": "Last name", "@sage/xtrem-sales/page-extensions__site_extension__salesReturnRequestSubstituteApprover____lookupDialogTitle": "Select substitute approver", "@sage/xtrem-sales/page-extensions__site_extension__salesReturnRequestSubstituteApprover____title": "Substitute approver", "@sage/xtrem-sales/page-fragments__sales_receipt_line__receipts____columns__title__adjustmentAmount": "Adjustment amount", "@sage/xtrem-sales/page-fragments__sales_receipt_line__receipts____columns__title__amount": "Amount", "@sage/xtrem-sales/page-fragments__sales_receipt_line__receipts____columns__title__discountAmount": "Discount amount", "@sage/xtrem-sales/page-fragments__sales_receipt_line__receipts____columns__title__document__bankAccount__name": "Bank account", "@sage/xtrem-sales/page-fragments__sales_receipt_line__receipts____columns__title__document__isVoided": "Voided", "@sage/xtrem-sales/page-fragments__sales_receipt_line__receipts____columns__title__document__number": "Receipt number", "@sage/xtrem-sales/page-fragments__sales_receipt_line__receipts____columns__title__document__paymentDate": "Receipt date", "@sage/xtrem-sales/page-fragments__sales_receipt_line__receipts____columns__title__document__paymentMethod": "Payment method", "@sage/xtrem-sales/page-fragments__sales_receipt_line__receipts____columns__title__document__reference": "Transaction information", "@sage/xtrem-sales/page-fragments__sales_receipt_line__receipts____columns__title__penaltyAmount": "Penalty amount", "@sage/xtrem-sales/page-fragments__sales_receipt_line__receipts____title": "Receipts", "@sage/xtrem-sales/pages__allocation_result__fromSalesOrderNumberTitle": "From sales order", "@sage/xtrem-sales/pages__allocation_result__toSalesOrderNumberTitle": "To sales order", "@sage/xtrem-sales/pages__assignment__already_exists": "Assignment already exists.", "@sage/xtrem-sales/pages__customer____title": "Customer", "@sage/xtrem-sales/pages__customers_on_hold_settings____title": "Customers on hold settings", "@sage/xtrem-sales/pages__customers_on_hold_settings__save____title": "Save settings", "@sage/xtrem-sales/pages__customers_on_hold_settings__shouldExcludeInactiveCustomers____title": "Inactive customers excluded", "@sage/xtrem-sales/pages__errors_blocking": "You need to resolve errors before printing.", "@sage/xtrem-sales/pages__main_list_sales_order_section_title__send_sales_order_dialog": "Send sales order", "@sage/xtrem-sales/pages__main_list_sales_quote__section_title_send_sales_order_dialog_": "Send sales quote", "@sage/xtrem-sales/pages__pages__sales_order_shipping_mass_process__quantity_confirmed_in_stock_unit_negative_value": "Enter a positive value for the confirmed quantity in stock unit.", "@sage/xtrem-sales/pages__pages__sales_order_shipping_mass_process__quantity_confirmed_multiplication_of_stock_unit_conversion_factor": "The confirmed quantity in the stock unit needs to be equal to the quantity in the sales unit multiplied by the conversion factor.", "@sage/xtrem-sales/pages__proforma_invoice____title": "Proforma invoice", "@sage/xtrem-sales/pages__proforma_invoice__cancel____title": "Cancel", "@sage/xtrem-sales/pages__proforma_invoice__edit_page_title": "Proforma {{orderNumber}}, version {{versionNumber}}", "@sage/xtrem-sales/pages__proforma_invoice__generate_button_title": "Generate", "@sage/xtrem-sales/pages__proforma_invoice__generate_page_title": "Generate proforma {{orderNumber}}", "@sage/xtrem-sales/pages__proforma_invoice__save____title": "Save", "@sage/xtrem-sales/pages__return_request_to_return_receipt__warning_different_information": "Some lines were not added as they have different shipping details.", "@sage/xtrem-sales/pages__sales__quantity_in_sales_unit_maximum_quantity": "The quantity in sales unit ({{value}}) must not be larger than {{maxValue}}.", "@sage/xtrem-sales/pages__sales__quantity_in_sales_unit_minimum_quantity": "The quantity in sales unit ({{value}}) must not be less than {{minValue}}.", "@sage/xtrem-sales/pages__sales_credit_memo____navigationPanel__bulkActions__title": "Print", "@sage/xtrem-sales/pages__sales_credit_memo____navigationPanel__dropdownActions__title": "Post", "@sage/xtrem-sales/pages__sales_credit_memo____navigationPanel__dropdownActions__title__2": "Open item", "@sage/xtrem-sales/pages__sales_credit_memo____navigationPanel__dropdownActions__title__3": "Print", "@sage/xtrem-sales/pages__sales_credit_memo____navigationPanel__dropdownActions__title__4": "Send", "@sage/xtrem-sales/pages__sales_credit_memo____navigationPanel__dropdownActions__title__5": "Set dimensions", "@sage/xtrem-sales/pages__sales_credit_memo____navigationPanel__dropdownActions__title__6": "Delete", "@sage/xtrem-sales/pages__sales_credit_memo____navigationPanel__listItem__billToCustomerId__title": "Bill-to customer ID", "@sage/xtrem-sales/pages__sales_credit_memo____navigationPanel__listItem__line_4__title": "Site", "@sage/xtrem-sales/pages__sales_credit_memo____navigationPanel__listItem__line_5__title": "Total including tax", "@sage/xtrem-sales/pages__sales_credit_memo____navigationPanel__listItem__line10__title": "Printed", "@sage/xtrem-sales/pages__sales_credit_memo____navigationPanel__listItem__line11__title": "<PERSON><PERSON>", "@sage/xtrem-sales/pages__sales_credit_memo____navigationPanel__listItem__line12__title": "Total excluding tax", "@sage/xtrem-sales/pages__sales_credit_memo____navigationPanel__listItem__line13__title": "Gross profit", "@sage/xtrem-sales/pages__sales_credit_memo____navigationPanel__listItem__line14__title": "Due date", "@sage/xtrem-sales/pages__sales_credit_memo____navigationPanel__listItem__line15__title": "Tax", "@sage/xtrem-sales/pages__sales_credit_memo____navigationPanel__listItem__line2__title": "Bill-to customer", "@sage/xtrem-sales/pages__sales_credit_memo____navigationPanel__listItem__line2Right__title": "Credit memo date", "@sage/xtrem-sales/pages__sales_credit_memo____navigationPanel__listItem__line8__title": "Creation number", "@sage/xtrem-sales/pages__sales_credit_memo____navigationPanel__listItem__line9__title": "Transaction currency", "@sage/xtrem-sales/pages__sales_credit_memo____navigationPanel__listItem__netBalance__title": "Net balance", "@sage/xtrem-sales/pages__sales_credit_memo____navigationPanel__listItem__paymentStatus__title": "Payment status", "@sage/xtrem-sales/pages__sales_credit_memo____navigationPanel__listItem__title__title": "Number", "@sage/xtrem-sales/pages__sales_credit_memo____navigationPanel__listItem__titleRight__title": "Status", "@sage/xtrem-sales/pages__sales_credit_memo____navigationPanel__listItem__totalPayments__title": "Total payments", "@sage/xtrem-sales/pages__sales_credit_memo____objectTypePlural": "Sales credit memos", "@sage/xtrem-sales/pages__sales_credit_memo____objectTypeSingular": "Sales credit memo", "@sage/xtrem-sales/pages__sales_credit_memo____title": "Sales credit memo", "@sage/xtrem-sales/pages__sales_credit_memo___id____title": "ID", "@sage/xtrem-sales/pages__sales_credit_memo__amountPaid____title": "Total payments", "@sage/xtrem-sales/pages__sales_credit_memo__apply_dimensions_success": "Dimensions applied.", "@sage/xtrem-sales/pages__sales_credit_memo__billToAddress____columns__columns__country__title": "ISO 3166-1 alpha-2 code", "@sage/xtrem-sales/pages__sales_credit_memo__billToAddress____columns__title__addressLine1": "Address line 1", "@sage/xtrem-sales/pages__sales_credit_memo__billToAddress____columns__title__addressLine2": "Address line 2", "@sage/xtrem-sales/pages__sales_credit_memo__billToAddress____columns__title__city": "City", "@sage/xtrem-sales/pages__sales_credit_memo__billToAddress____columns__title__concatenatedAddress": "Bill-to address", "@sage/xtrem-sales/pages__sales_credit_memo__billToAddress____columns__title__locationPhoneNumber": "Phone number", "@sage/xtrem-sales/pages__sales_credit_memo__billToAddress____columns__title__name": "Name", "@sage/xtrem-sales/pages__sales_credit_memo__billToAddress____dropdownActions__title": "Replace", "@sage/xtrem-sales/pages__sales_credit_memo__billToAddress____dropdownActions__title__2": "Edit", "@sage/xtrem-sales/pages__sales_credit_memo__billToAddress____dropdownActions__title__3": "Read-only", "@sage/xtrem-sales/pages__sales_credit_memo__billToAddress____title": "Bill-to address", "@sage/xtrem-sales/pages__sales_credit_memo__billToContact____columns__title__firstName": "First name", "@sage/xtrem-sales/pages__sales_credit_memo__billToContact____columns__title__lastName": "Last name", "@sage/xtrem-sales/pages__sales_credit_memo__billToContact____columns__title__title": "Title", "@sage/xtrem-sales/pages__sales_credit_memo__billToContact____lookupDialogTitle": "Select bill-to customer address contact detail", "@sage/xtrem-sales/pages__sales_credit_memo__billToContact____title": "Bill-to customer address contact detail", "@sage/xtrem-sales/pages__sales_credit_memo__billToCustomer____columns__title__businessEntity__country__name": "Country", "@sage/xtrem-sales/pages__sales_credit_memo__billToCustomer____columns__title__businessEntity__id": "ID", "@sage/xtrem-sales/pages__sales_credit_memo__billToCustomer____columns__title__businessEntity__name": "Name", "@sage/xtrem-sales/pages__sales_credit_memo__billToCustomer____columns__title__businessEntity__taxIdNumber": "Tax ID", "@sage/xtrem-sales/pages__sales_credit_memo__billToCustomer____lookupDialogTitle": "Select bill-to customer", "@sage/xtrem-sales/pages__sales_credit_memo__billToCustomer____title": "Bill-to customer", "@sage/xtrem-sales/pages__sales_credit_memo__billToLinkedAddress____columns__columns__country__title": "ISO 3166-1 alpha-2 code", "@sage/xtrem-sales/pages__sales_credit_memo__billToLinkedAddress____columns__title__addressLine1": "Address line 1", "@sage/xtrem-sales/pages__sales_credit_memo__billToLinkedAddress____columns__title__addressLine2": "Address line 2", "@sage/xtrem-sales/pages__sales_credit_memo__billToLinkedAddress____columns__title__city": "City", "@sage/xtrem-sales/pages__sales_credit_memo__billToLinkedAddress____columns__title__isActive": "Active", "@sage/xtrem-sales/pages__sales_credit_memo__billToLinkedAddress____columns__title__locationPhoneNumber": "Phone number", "@sage/xtrem-sales/pages__sales_credit_memo__billToLinkedAddress____columns__title__postcode": "ZIP code", "@sage/xtrem-sales/pages__sales_credit_memo__billToLinkedAddress____columns__title__region": "Region", "@sage/xtrem-sales/pages__sales_credit_memo__billToLinkedAddress____lookupDialogTitle": "Select bill-to address", "@sage/xtrem-sales/pages__sales_credit_memo__billToLinkedAddress____title": "Bill-to address", "@sage/xtrem-sales/pages__sales_credit_memo__companyCurrency____columns__title__id": "ISO 4217 code", "@sage/xtrem-sales/pages__sales_credit_memo__companyCurrency____columns__title__symbol": "Symbol", "@sage/xtrem-sales/pages__sales_credit_memo__companyCurrency____lookupDialogTitle": "Select company currency", "@sage/xtrem-sales/pages__sales_credit_memo__companyCurrency____placeholder": "Select currency", "@sage/xtrem-sales/pages__sales_credit_memo__companyCurrency____title": "Company currency", "@sage/xtrem-sales/pages__sales_credit_memo__companyFxRate____title": "Exchange rate", "@sage/xtrem-sales/pages__sales_credit_memo__companyFxRateDivisor____title": "Exchange rate divisor", "@sage/xtrem-sales/pages__sales_credit_memo__consumptionAddress____columns__columns__country__name__title": "ISO 3166-1 alpha-2 code", "@sage/xtrem-sales/pages__sales_credit_memo__consumptionAddress____columns__title__addressLine1": "Address line 1", "@sage/xtrem-sales/pages__sales_credit_memo__consumptionAddress____columns__title__addressLine2": "Address line 2", "@sage/xtrem-sales/pages__sales_credit_memo__consumptionAddress____columns__title__city": "City", "@sage/xtrem-sales/pages__sales_credit_memo__consumptionAddress____columns__title__concatenatedAddress": "Bill-to address", "@sage/xtrem-sales/pages__sales_credit_memo__consumptionAddress____columns__title__country__name": "Country", "@sage/xtrem-sales/pages__sales_credit_memo__consumptionAddress____columns__title__locationPhoneNumber": "Phone number", "@sage/xtrem-sales/pages__sales_credit_memo__consumptionAddress____columns__title__name": "Name", "@sage/xtrem-sales/pages__sales_credit_memo__consumptionAddress____title": "Consumption address", "@sage/xtrem-sales/pages__sales_credit_memo__contacts____columns__title__email": "Email", "@sage/xtrem-sales/pages__sales_credit_memo__contacts____columns__title__firstName": "First name", "@sage/xtrem-sales/pages__sales_credit_memo__contacts____columns__title__lastName": "Last name", "@sage/xtrem-sales/pages__sales_credit_memo__contacts____columns__title__title": "Title", "@sage/xtrem-sales/pages__sales_credit_memo__contacts____title": "Contacts", "@sage/xtrem-sales/pages__sales_credit_memo__contactSelectionBlock____title": "", "@sage/xtrem-sales/pages__sales_credit_memo__creationNumber____title": "Creation number", "@sage/xtrem-sales/pages__sales_credit_memo__credit_memo_date__cannot__be__future": "The credit memo date cannot be later than today.", "@sage/xtrem-sales/pages__sales_credit_memo__currency____columns__title__id": "ISO 4217 code", "@sage/xtrem-sales/pages__sales_credit_memo__currency____columns__title__symbol": "Symbol", "@sage/xtrem-sales/pages__sales_credit_memo__currency____lookupDialogTitle": "Select currency", "@sage/xtrem-sales/pages__sales_credit_memo__currency____title": "Transaction currency", "@sage/xtrem-sales/pages__sales_credit_memo__date____title": "Credit memo date", "@sage/xtrem-sales/pages__sales_credit_memo__defaultDimension____title": "Set dimensions", "@sage/xtrem-sales/pages__sales_credit_memo__displayStatus____title": "Display status", "@sage/xtrem-sales/pages__sales_credit_memo__documentNumberLink____title": "Open item", "@sage/xtrem-sales/pages__sales_credit_memo__dueDate____title": "Due date", "@sage/xtrem-sales/pages__sales_credit_memo__email_exception": "Could not send sales credit memo email. ({{exception}})", "@sage/xtrem-sales/pages__sales_credit_memo__email_sent": "Sales credit memo sent to {{email}}.", "@sage/xtrem-sales/pages__sales_credit_memo__emailAddress____helperText": "A sales credit memo will be sent to this address.", "@sage/xtrem-sales/pages__sales_credit_memo__emailAddress____title": "Email", "@sage/xtrem-sales/pages__sales_credit_memo__emailFirstName____title": "First name", "@sage/xtrem-sales/pages__sales_credit_memo__emailLastName____title": "Last name", "@sage/xtrem-sales/pages__sales_credit_memo__emailTitle____title": "Title", "@sage/xtrem-sales/pages__sales_credit_memo__externalNote____helperText": "Notes display on customer documents.", "@sage/xtrem-sales/pages__sales_credit_memo__externalNote____title": "Customer notes", "@sage/xtrem-sales/pages__sales_credit_memo__externalNoteLine____helperText": "Notes display on customer documents.", "@sage/xtrem-sales/pages__sales_credit_memo__externalNoteLine____title": "Customer notes", "@sage/xtrem-sales/pages__sales_credit_memo__forcedAmountPaid____title": "Forced amount paid", "@sage/xtrem-sales/pages__sales_credit_memo__fxRateDate____title": "Exchange rate date", "@sage/xtrem-sales/pages__sales_credit_memo__goToSysNotificationPage____title": "Retry", "@sage/xtrem-sales/pages__sales_credit_memo__gross_price_greater_or_equal_to_0": "The gross price needs to be more than or equal to 0.", "@sage/xtrem-sales/pages__sales_credit_memo__headerSection____title": "Header section", "@sage/xtrem-sales/pages__sales_credit_memo__informationBlock____title": "Information", "@sage/xtrem-sales/pages__sales_credit_memo__informationSection____title": "Information", "@sage/xtrem-sales/pages__sales_credit_memo__internalNote____helperText": "Notes display on internal documents.", "@sage/xtrem-sales/pages__sales_credit_memo__internalNote____title": "Internal notes", "@sage/xtrem-sales/pages__sales_credit_memo__internalNoteLine____helperText": "Notes display on internal documents.", "@sage/xtrem-sales/pages__sales_credit_memo__internalNoteLine____title": "Internal line notes", "@sage/xtrem-sales/pages__sales_credit_memo__invalid-email": "Email address incorrect: {{email}}", "@sage/xtrem-sales/pages__sales_credit_memo__isExternalNote____title": "Add notes to customer document", "@sage/xtrem-sales/pages__sales_credit_memo__isExternalNoteLine____title": "Add notes to customer document", "@sage/xtrem-sales/pages__sales_credit_memo__isPrinted____title": "Printed", "@sage/xtrem-sales/pages__sales_credit_memo__isSent____title": "<PERSON><PERSON>", "@sage/xtrem-sales/pages__sales_credit_memo__itemsSection____title": "Lines", "@sage/xtrem-sales/pages__sales_credit_memo__lines____columns__columns__item__name__title": "Category", "@sage/xtrem-sales/pages__sales_credit_memo__lines____columns__columns__priceReason__name__title": "Priority", "@sage/xtrem-sales/pages__sales_credit_memo__lines____columns__columns__providerSite__title": "Company", "@sage/xtrem-sales/pages__sales_credit_memo__lines____columns__columns__stockUnit__name__title": "Symbol", "@sage/xtrem-sales/pages__sales_credit_memo__lines____columns__columns__unit__name__title": "Symbol", "@sage/xtrem-sales/pages__sales_credit_memo__lines____columns__lookupDialogTitle__providerSite": "Select provider site", "@sage/xtrem-sales/pages__sales_credit_memo__lines____columns__postfix__charge": "%", "@sage/xtrem-sales/pages__sales_credit_memo__lines____columns__postfix__discount": "%", "@sage/xtrem-sales/pages__sales_credit_memo__lines____columns__title__amountExcludingTax": "Total excluding tax", "@sage/xtrem-sales/pages__sales_credit_memo__lines____columns__title__amountExcludingTaxInCompanyCurrency": "Total excluding tax company currency", "@sage/xtrem-sales/pages__sales_credit_memo__lines____columns__title__amountIncludingTax": "Total including tax", "@sage/xtrem-sales/pages__sales_credit_memo__lines____columns__title__amountIncludingTaxInCompanyCurrency": "Total including tax company currency", "@sage/xtrem-sales/pages__sales_credit_memo__lines____columns__title__charge": "Charge", "@sage/xtrem-sales/pages__sales_credit_memo__lines____columns__title__discount": "Discount", "@sage/xtrem-sales/pages__sales_credit_memo__lines____columns__title__grossPrice": "Gross price", "@sage/xtrem-sales/pages__sales_credit_memo__lines____columns__title__grossProfitAmount": "Gross profit amount", "@sage/xtrem-sales/pages__sales_credit_memo__lines____columns__title__isExternalNote": "Add notes to customer document", "@sage/xtrem-sales/pages__sales_credit_memo__lines____columns__title__item__id": "Item ID", "@sage/xtrem-sales/pages__sales_credit_memo__lines____columns__title__item__name": "<PERSON><PERSON>", "@sage/xtrem-sales/pages__sales_credit_memo__lines____columns__title__itemDescription": "Item description", "@sage/xtrem-sales/pages__sales_credit_memo__lines____columns__title__netPrice": "Net price", "@sage/xtrem-sales/pages__sales_credit_memo__lines____columns__title__origin": "Origin", "@sage/xtrem-sales/pages__sales_credit_memo__lines____columns__title__priceOrigin": "Price origin", "@sage/xtrem-sales/pages__sales_credit_memo__lines____columns__title__priceReason__name": "Price reason", "@sage/xtrem-sales/pages__sales_credit_memo__lines____columns__title__providerSite": "Provider site", "@sage/xtrem-sales/pages__sales_credit_memo__lines____columns__title__quantity": "Quantity in sales unit", "@sage/xtrem-sales/pages__sales_credit_memo__lines____columns__title__quantityInStockUnit": "Quantity in stock unit", "@sage/xtrem-sales/pages__sales_credit_memo__lines____columns__title__stockCostAmount": "Stock cost amount", "@sage/xtrem-sales/pages__sales_credit_memo__lines____columns__title__stockUnit__name": "Stock unit", "@sage/xtrem-sales/pages__sales_credit_memo__lines____columns__title__taxAmount": "Total tax", "@sage/xtrem-sales/pages__sales_credit_memo__lines____columns__title__taxCalculationStatus": "Tax status", "@sage/xtrem-sales/pages__sales_credit_memo__lines____columns__title__unit__name": "Sales unit", "@sage/xtrem-sales/pages__sales_credit_memo__lines____columns__title__unitToStockUnitConversionFactor": "Stock unit conversion factor", "@sage/xtrem-sales/pages__sales_credit_memo__lines____dropdownActions__title": "Dimensions", "@sage/xtrem-sales/pages__sales_credit_memo__lines____dropdownActions__title__2": "Tax details", "@sage/xtrem-sales/pages__sales_credit_memo__lines____dropdownActions__title__3": "Delete", "@sage/xtrem-sales/pages__sales_credit_memo__lines____inlineActions__title": "Open line panel", "@sage/xtrem-sales/pages__sales_credit_memo__lines____mobileCard__line2__title": "Description", "@sage/xtrem-sales/pages__sales_credit_memo__lines____mobileCard__line2Right__title": "Amount", "@sage/xtrem-sales/pages__sales_credit_memo__lines____mobileCard__title__title": "Product", "@sage/xtrem-sales/pages__sales_credit_memo__lines____sidebar__headerDropdownActions__title": "Dimensions", "@sage/xtrem-sales/pages__sales_credit_memo__lines____sidebar__headerDropdownActions__title__2": "Tax details", "@sage/xtrem-sales/pages__sales_credit_memo__lines____sidebar__headerDropdownActions__title__3": "Delete", "@sage/xtrem-sales/pages__sales_credit_memo__lines____title": "Lines", "@sage/xtrem-sales/pages__sales_credit_memo__lines_delete_action_dialog_title": "Confirm delete", "@sage/xtrem-sales/pages__sales_credit_memo__netBalance____title": "Net balance", "@sage/xtrem-sales/pages__sales_credit_memo__noteBlock____title": "Notes", "@sage/xtrem-sales/pages__sales_credit_memo__notesSection____title": "Notes", "@sage/xtrem-sales/pages__sales_credit_memo__number____title": "Number", "@sage/xtrem-sales/pages__sales_credit_memo__on_price_determination_confirmation": "Do you want to recalculate prices, discounts, and charges?", "@sage/xtrem-sales/pages__sales_credit_memo__paymentsBlock____title": "Payments", "@sage/xtrem-sales/pages__sales_credit_memo__paymentsSection____title": "Payments", "@sage/xtrem-sales/pages__sales_credit_memo__paymentTerm____lookupDialogTitle": "Select payment term", "@sage/xtrem-sales/pages__sales_credit_memo__percentage_greater_than_100": "The percentage cannot exceed 100.", "@sage/xtrem-sales/pages__sales_credit_memo__percentage_is_negative": "The percentage cannot be negative.", "@sage/xtrem-sales/pages__sales_credit_memo__post____title": "Post", "@sage/xtrem-sales/pages__sales_credit_memo__post_action_dialog_content": "You are about to post this sales credit memo.", "@sage/xtrem-sales/pages__sales_credit_memo__post_action_dialog_title": "Confirm posting", "@sage/xtrem-sales/pages__sales_credit_memo__postingDetails____columns__title__financeIntegrationAppRecordId": "Accounting integration reference", "@sage/xtrem-sales/pages__sales_credit_memo__postingDetails____columns__title__postingStatus": "Status", "@sage/xtrem-sales/pages__sales_credit_memo__postingDetails____columns__title__targetDocumentNumber": "Document number", "@sage/xtrem-sales/pages__sales_credit_memo__postingDetails____columns__title__targetDocumentType": "Document type", "@sage/xtrem-sales/pages__sales_credit_memo__postingDetails____title": "Posting", "@sage/xtrem-sales/pages__sales_credit_memo__postingMessageBlock____title": "Error details", "@sage/xtrem-sales/pages__sales_credit_memo__postingSection____title": "Posting", "@sage/xtrem-sales/pages__sales_credit_memo__print____title": "Print", "@sage/xtrem-sales/pages__sales_credit_memo__printed": "The sales credit memo has been printed.", "@sage/xtrem-sales/pages__sales_credit_memo__printed_status_not_updated": "Unable to update credit memo {{number}} printed status.", "@sage/xtrem-sales/pages__sales_credit_memo__quantity_in_sales_unit_negative_value": "The quantity in sales unit cannot be less than or equal to 0.", "@sage/xtrem-sales/pages__sales_credit_memo__rateDescription____title": "Exchange rate", "@sage/xtrem-sales/pages__sales_credit_memo__reason____lookupDialogTitle": "Select reason", "@sage/xtrem-sales/pages__sales_credit_memo__reason____title": "Reason", "@sage/xtrem-sales/pages__sales_credit_memo__repost____title": "Repost", "@sage/xtrem-sales/pages__sales_credit_memo__repost_accounts_receivable_credit_memo_already_posted": "The accounts receivable credit memo was posted successfully. You can access this document in your financial solution to take further action.", "@sage/xtrem-sales/pages__sales_credit_memo__repost_documents_already_posted_title": "Repost", "@sage/xtrem-sales/pages__sales_credit_memo__repost_errors": "Errors occurred while reposting:", "@sage/xtrem-sales/pages__sales_credit_memo__repost_journal_entry_already_posted": "The journal entry was posted successfully. You can access this document in your financial solution to take further action.", "@sage/xtrem-sales/pages__sales_credit_memo__salesCreditMemoExcludingTax____title": "Total excluding tax", "@sage/xtrem-sales/pages__sales_credit_memo__salesCreditMemoIncludingTax____title": "Total including tax", "@sage/xtrem-sales/pages__sales_credit_memo__salesCreditMemoLineCount____title": "Number of items", "@sage/xtrem-sales/pages__sales_credit_memo__salesLinkedDocuments____columns__title___id": "Origin document number", "@sage/xtrem-sales/pages__sales_credit_memo__salesLinkedDocuments____columns__title__documentStatus": "Status", "@sage/xtrem-sales/pages__sales_credit_memo__salesLinkedDocuments____columns__title__documentType": "Document type", "@sage/xtrem-sales/pages__sales_credit_memo__salesLinkedDocuments____title": "Origin document", "@sage/xtrem-sales/pages__sales_credit_memo__save____title": "Save", "@sage/xtrem-sales/pages__sales_credit_memo__save_warnings": "Warnings while saving:", "@sage/xtrem-sales/pages__sales_credit_memo__select_bill_to_contact_button_text": "Select bill-to customer contact", "@sage/xtrem-sales/pages__sales_credit_memo__selectBillToContact____title": "Bill-to customer contact", "@sage/xtrem-sales/pages__sales_credit_memo__selectedContact____columns__title___id": "ID", "@sage/xtrem-sales/pages__sales_credit_memo__selectedContact____columns__title__firstName": "First name", "@sage/xtrem-sales/pages__sales_credit_memo__selectedContact____columns__title__lastName": "Last name", "@sage/xtrem-sales/pages__sales_credit_memo__selectedContact____columns__title__title": "Title", "@sage/xtrem-sales/pages__sales_credit_memo__selectedContact____lookupDialogTitle": "Select selected contact", "@sage/xtrem-sales/pages__sales_credit_memo__selectedContact____title": "Selected contact", "@sage/xtrem-sales/pages__sales_credit_memo__send_credit_memo_button_text": "Send", "@sage/xtrem-sales/pages__sales_credit_memo__send_credit_memo_dialog_content": "You are about to send the sales credit memo email.", "@sage/xtrem-sales/pages__sales_credit_memo__send_credit_memo_dialog_title": "Sales credit memo email sending confirmation", "@sage/xtrem-sales/pages__sales_credit_memo__sendCreditMemoButton____title": "Send credit memo", "@sage/xtrem-sales/pages__sales_credit_memo__sendEmail____title": "Send", "@sage/xtrem-sales/pages__sales_credit_memo__sendEmailBlock____title": "To", "@sage/xtrem-sales/pages__sales_credit_memo__sendEmailSection____title": "Send sales credit memo", "@sage/xtrem-sales/pages__sales_credit_memo__site____columns__title__legalCompany__name": "Company", "@sage/xtrem-sales/pages__sales_credit_memo__site____lookupDialogTitle": "Select site", "@sage/xtrem-sales/pages__sales_credit_memo__site____title": "Site", "@sage/xtrem-sales/pages__sales_credit_memo__status____title": "Posting status", "@sage/xtrem-sales/pages__sales_credit_memo__step_sequence_creation": "Create", "@sage/xtrem-sales/pages__sales_credit_memo__step_sequence_pay": "Pay", "@sage/xtrem-sales/pages__sales_credit_memo__step_sequence_post": "Post", "@sage/xtrem-sales/pages__sales_credit_memo__taxCalculationStatus____title": "Tax calculation status", "@sage/xtrem-sales/pages__sales_credit_memo__taxDetails____columns__postfix__taxRate": "%", "@sage/xtrem-sales/pages__sales_credit_memo__taxDetails____columns__title__tax": "Tax", "@sage/xtrem-sales/pages__sales_credit_memo__taxDetails____columns__title__taxableAmount": "Taxable base", "@sage/xtrem-sales/pages__sales_credit_memo__taxDetails____columns__title__taxAmount": "Amount", "@sage/xtrem-sales/pages__sales_credit_memo__taxDetails____columns__title__taxCategory": "Category", "@sage/xtrem-sales/pages__sales_credit_memo__taxDetails____columns__title__taxRate": "Rate", "@sage/xtrem-sales/pages__sales_credit_memo__taxDetails____title": "Taxes", "@sage/xtrem-sales/pages__sales_credit_memo__taxEngine____title": "Tax engine", "@sage/xtrem-sales/pages__sales_credit_memo__totalAmountExcludingTax____title": "Excluding tax", "@sage/xtrem-sales/pages__sales_credit_memo__totalAmountExcludingTaxInCompanyCurrency____title": "Excluding tax", "@sage/xtrem-sales/pages__sales_credit_memo__totalAmountIncludingTax____title": "Including tax", "@sage/xtrem-sales/pages__sales_credit_memo__totalAmountIncludingTaxInCompanyCurrency____title": "Including tax", "@sage/xtrem-sales/pages__sales_credit_memo__totalAmountPaid____title": "Total amount paid", "@sage/xtrem-sales/pages__sales_credit_memo__totalGrossProfit____title": "Gross profit", "@sage/xtrem-sales/pages__sales_credit_memo__totalsSection____title": "Totals", "@sage/xtrem-sales/pages__sales_credit_memo__totalsSectionCompanyCurrencyDetailsBlock____title": "Amounts company currency", "@sage/xtrem-sales/pages__sales_credit_memo__totalsSectionTaxDetailsBlock____title": "Summary by tax", "@sage/xtrem-sales/pages__sales_credit_memo__totalsSectionTaxTotalsBlock____title": "", "@sage/xtrem-sales/pages__sales_credit_memo__totalTaxAmount____title": "Tax", "@sage/xtrem-sales/pages__sales_credit_memo__totalTaxAmountAdjusted____title": "Tax adjusted", "@sage/xtrem-sales/pages__sales_credit_memo_delete_action_dialog_content": "You are about to delete this sales credit memo line. This action cannot be undone.", "@sage/xtrem-sales/pages__sales_credit_memo_reason____navigationPanel__optionsMenu__title": "All", "@sage/xtrem-sales/pages__sales_credit_memo_reason____navigationPanel__optionsMenu__title__2": "Active", "@sage/xtrem-sales/pages__sales_credit_memo_reason____navigationPanel__optionsMenu__title__3": "Inactive", "@sage/xtrem-sales/pages__sales_credit_memo_reason____objectTypePlural": "Credit memo reasons", "@sage/xtrem-sales/pages__sales_credit_memo_reason____objectTypeSingular": "Credit memo reason", "@sage/xtrem-sales/pages__sales_credit_memo_reason____title": "Credit memo reason", "@sage/xtrem-sales/pages__sales_currency_settings____title": "Sales currency settings", "@sage/xtrem-sales/pages__sales_currency_settings__salesCurrency____columns__title__id": "ISO 4217 code", "@sage/xtrem-sales/pages__sales_currency_settings__salesCurrency____columns__title__symbol": "Symbol", "@sage/xtrem-sales/pages__sales_currency_settings__salesCurrency____title": "Currency for sales values", "@sage/xtrem-sales/pages__sales_currency_settings__save____title": "Save settings", "@sage/xtrem-sales/pages__sales_invoice____navigationPanel__bulkActions__title": "Print", "@sage/xtrem-sales/pages__sales_invoice____navigationPanel__dropdownActions__title": "Post", "@sage/xtrem-sales/pages__sales_invoice____navigationPanel__dropdownActions__title__2": "Open item", "@sage/xtrem-sales/pages__sales_invoice____navigationPanel__dropdownActions__title__3": "Record receipt", "@sage/xtrem-sales/pages__sales_invoice____navigationPanel__dropdownActions__title__4": "Create credit memo", "@sage/xtrem-sales/pages__sales_invoice____navigationPanel__dropdownActions__title__5": "Print", "@sage/xtrem-sales/pages__sales_invoice____navigationPanel__dropdownActions__title__6": "Send", "@sage/xtrem-sales/pages__sales_invoice____navigationPanel__dropdownActions__title__7": "Set dimensions", "@sage/xtrem-sales/pages__sales_invoice____navigationPanel__dropdownActions__title__8": "Delete", "@sage/xtrem-sales/pages__sales_invoice____navigationPanel__listItem__billToCustomerId__title": "Bill-to customer ID", "@sage/xtrem-sales/pages__sales_invoice____navigationPanel__listItem__line_4__title": "Site", "@sage/xtrem-sales/pages__sales_invoice____navigationPanel__listItem__line_5__title": "Total including tax", "@sage/xtrem-sales/pages__sales_invoice____navigationPanel__listItem__line10__title": "Creation number", "@sage/xtrem-sales/pages__sales_invoice____navigationPanel__listItem__line11__title": "Printed", "@sage/xtrem-sales/pages__sales_invoice____navigationPanel__listItem__line12__title": "<PERSON><PERSON>", "@sage/xtrem-sales/pages__sales_invoice____navigationPanel__listItem__line13__title": "Total excluding tax", "@sage/xtrem-sales/pages__sales_invoice____navigationPanel__listItem__line14__title": "Gross profit", "@sage/xtrem-sales/pages__sales_invoice____navigationPanel__listItem__line15__title": "Tax", "@sage/xtrem-sales/pages__sales_invoice____navigationPanel__listItem__line2__title": "Bill-to customer", "@sage/xtrem-sales/pages__sales_invoice____navigationPanel__listItem__line2Right__title": "Invoice date", "@sage/xtrem-sales/pages__sales_invoice____navigationPanel__listItem__line6__title": "Due date", "@sage/xtrem-sales/pages__sales_invoice____navigationPanel__listItem__line8__title": "Incoterms® rule", "@sage/xtrem-sales/pages__sales_invoice____navigationPanel__listItem__line9__columns__title__decimalDigits": "Decimal digits", "@sage/xtrem-sales/pages__sales_invoice____navigationPanel__listItem__line9__columns__title__name": "Name", "@sage/xtrem-sales/pages__sales_invoice____navigationPanel__listItem__line9__columns__title__rounding": "Rounding", "@sage/xtrem-sales/pages__sales_invoice____navigationPanel__listItem__line9__columns__title__symbol": "Symbol", "@sage/xtrem-sales/pages__sales_invoice____navigationPanel__listItem__line9__title": "Transaction currency", "@sage/xtrem-sales/pages__sales_invoice____navigationPanel__listItem__netBalance__title": "Net balance", "@sage/xtrem-sales/pages__sales_invoice____navigationPanel__listItem__paymentStatus__title": "Payment status", "@sage/xtrem-sales/pages__sales_invoice____navigationPanel__listItem__title__title": "Number", "@sage/xtrem-sales/pages__sales_invoice____navigationPanel__listItem__titleRight__title": "Status", "@sage/xtrem-sales/pages__sales_invoice____navigationPanel__listItem__totalPayments__title": "Total payments", "@sage/xtrem-sales/pages__sales_invoice____objectTypePlural": "Sales invoices", "@sage/xtrem-sales/pages__sales_invoice____objectTypeSingular": "Sales invoice", "@sage/xtrem-sales/pages__sales_invoice____title": "Sales invoice", "@sage/xtrem-sales/pages__sales_invoice__amountPaid____title": "Total payments", "@sage/xtrem-sales/pages__sales_invoice__apply_dimensions_success": "Dimensions applied.", "@sage/xtrem-sales/pages__sales_invoice__billToAddress____columns__columns__country__title": "ISO 3166-1 alpha-2 code", "@sage/xtrem-sales/pages__sales_invoice__billToAddress____columns__title__addressLine1": "Address line 1", "@sage/xtrem-sales/pages__sales_invoice__billToAddress____columns__title__addressLine2": "Address line 2", "@sage/xtrem-sales/pages__sales_invoice__billToAddress____columns__title__city": "City", "@sage/xtrem-sales/pages__sales_invoice__billToAddress____columns__title__concatenatedAddress": "Bill-to address", "@sage/xtrem-sales/pages__sales_invoice__billToAddress____columns__title__locationPhoneNumber": "Phone number", "@sage/xtrem-sales/pages__sales_invoice__billToAddress____columns__title__name": "Name", "@sage/xtrem-sales/pages__sales_invoice__billToAddress____dropdownActions__title": "Replace", "@sage/xtrem-sales/pages__sales_invoice__billToAddress____dropdownActions__title__2": "Edit", "@sage/xtrem-sales/pages__sales_invoice__billToAddress____dropdownActions__title__3": "Read-only", "@sage/xtrem-sales/pages__sales_invoice__billToAddress____title": "Bill-to address", "@sage/xtrem-sales/pages__sales_invoice__billToContact____columns__title__firstName": "First name", "@sage/xtrem-sales/pages__sales_invoice__billToContact____columns__title__lastName": "Last name", "@sage/xtrem-sales/pages__sales_invoice__billToContact____columns__title__title": "Title", "@sage/xtrem-sales/pages__sales_invoice__billToContact____lookupDialogTitle": "Select bill-to address contact detail", "@sage/xtrem-sales/pages__sales_invoice__billToContact____title": "Bill-to customer address contact detail", "@sage/xtrem-sales/pages__sales_invoice__billToCustomer____columns__title__businessEntity__country__name": "Country", "@sage/xtrem-sales/pages__sales_invoice__billToCustomer____columns__title__businessEntity__id": "ID", "@sage/xtrem-sales/pages__sales_invoice__billToCustomer____columns__title__businessEntity__name": "Name", "@sage/xtrem-sales/pages__sales_invoice__billToCustomer____columns__title__businessEntity__taxIdNumber": "Tax ID", "@sage/xtrem-sales/pages__sales_invoice__billToCustomer____lookupDialogTitle": "Select bill-to customer", "@sage/xtrem-sales/pages__sales_invoice__billToCustomer____title": "Bill-to customer", "@sage/xtrem-sales/pages__sales_invoice__billToLinkedAddress____columns__columns__country__title": "ISO 3166-1 alpha-2 code", "@sage/xtrem-sales/pages__sales_invoice__billToLinkedAddress____columns__title__addressLine1": "Address line 1", "@sage/xtrem-sales/pages__sales_invoice__billToLinkedAddress____columns__title__addressLine2": "Address line 2", "@sage/xtrem-sales/pages__sales_invoice__billToLinkedAddress____columns__title__city": "City", "@sage/xtrem-sales/pages__sales_invoice__billToLinkedAddress____columns__title__isActive": "Active", "@sage/xtrem-sales/pages__sales_invoice__billToLinkedAddress____columns__title__locationPhoneNumber": "Phone number", "@sage/xtrem-sales/pages__sales_invoice__billToLinkedAddress____columns__title__postcode": "ZIP code", "@sage/xtrem-sales/pages__sales_invoice__billToLinkedAddress____columns__title__region": "Region", "@sage/xtrem-sales/pages__sales_invoice__billToLinkedAddress____lookupDialogTitle": "Select bill-to address", "@sage/xtrem-sales/pages__sales_invoice__billToLinkedAddress____title": "Bill-to address", "@sage/xtrem-sales/pages__sales_invoice__companyCurrency____columns__title__id": "ISO 4217 code", "@sage/xtrem-sales/pages__sales_invoice__companyCurrency____columns__title__symbol": "Symbol", "@sage/xtrem-sales/pages__sales_invoice__companyCurrency____lookupDialogTitle": "Select company currency", "@sage/xtrem-sales/pages__sales_invoice__companyCurrency____placeholder": "Select currency", "@sage/xtrem-sales/pages__sales_invoice__companyCurrency____title": "Company currency", "@sage/xtrem-sales/pages__sales_invoice__companyFxRate____title": "Exchange rate", "@sage/xtrem-sales/pages__sales_invoice__companyFxRateDivisor____title": "Exchange rate divisor", "@sage/xtrem-sales/pages__sales_invoice__consumptionAddress____columns__columns__country__title": "ISO 3166-1 alpha-2 code", "@sage/xtrem-sales/pages__sales_invoice__consumptionAddress____columns__title__addressLine1": "Address line 1", "@sage/xtrem-sales/pages__sales_invoice__consumptionAddress____columns__title__addressLine2": "Address line 2", "@sage/xtrem-sales/pages__sales_invoice__consumptionAddress____columns__title__city": "City", "@sage/xtrem-sales/pages__sales_invoice__consumptionAddress____columns__title__concatenatedAddress": "Bill-to address", "@sage/xtrem-sales/pages__sales_invoice__consumptionAddress____columns__title__locationPhoneNumber": "Phone number", "@sage/xtrem-sales/pages__sales_invoice__consumptionAddress____columns__title__name": "Name", "@sage/xtrem-sales/pages__sales_invoice__consumptionAddress____dropdownActions__title": "Edit", "@sage/xtrem-sales/pages__sales_invoice__consumptionAddress____dropdownActions__title__2": "Read-only", "@sage/xtrem-sales/pages__sales_invoice__consumptionAddress____title": "Consumption address", "@sage/xtrem-sales/pages__sales_invoice__contacts____columns__title__email": "Email", "@sage/xtrem-sales/pages__sales_invoice__contacts____columns__title__firstName": "First name", "@sage/xtrem-sales/pages__sales_invoice__contacts____columns__title__lastName": "Last name", "@sage/xtrem-sales/pages__sales_invoice__contacts____columns__title__title": "Title", "@sage/xtrem-sales/pages__sales_invoice__contacts____title": "Contacts", "@sage/xtrem-sales/pages__sales_invoice__contactSelectionBlock____title": "", "@sage/xtrem-sales/pages__sales_invoice__create_credit_memo_dialog_content": "You are about to create a credit memo from this sales invoice.", "@sage/xtrem-sales/pages__sales_invoice__create_credit_memo_dialog_title": "Confirm credit memo creation", "@sage/xtrem-sales/pages__sales_invoice__createSalesCreditMemosFromInvoices____title": "Create credit memo", "@sage/xtrem-sales/pages__sales_invoice__creationNumber____title": "Creation number", "@sage/xtrem-sales/pages__sales_invoice__credit_exception": "The credit memo could not be created.", "@sage/xtrem-sales/pages__sales_invoice__credit_memo_created": "Record created", "@sage/xtrem-sales/pages__sales_invoice__creditStatus____title": "Credit status", "@sage/xtrem-sales/pages__sales_invoice__currency____columns__title__id": "ISO 4217 code", "@sage/xtrem-sales/pages__sales_invoice__currency____columns__title__symbol": "Symbol", "@sage/xtrem-sales/pages__sales_invoice__currency____lookupDialogTitle": "Select currency", "@sage/xtrem-sales/pages__sales_invoice__currency____title": "Transaction currency", "@sage/xtrem-sales/pages__sales_invoice__date____title": "Invoice date", "@sage/xtrem-sales/pages__sales_invoice__defaultDimension____title": "Set dimensions", "@sage/xtrem-sales/pages__sales_invoice__displayStatus____title": "Display status", "@sage/xtrem-sales/pages__sales_invoice__documentNumberLink____title": "Open item", "@sage/xtrem-sales/pages__sales_invoice__dueDate____title": "Due date", "@sage/xtrem-sales/pages__sales_invoice__email_exception": "Could not send sales invoice email. ({{exception}})", "@sage/xtrem-sales/pages__sales_invoice__email_not_sent": "Sales invoice was not sent.", "@sage/xtrem-sales/pages__sales_invoice__email_sent": "Sales invoice sent to {{email}}.", "@sage/xtrem-sales/pages__sales_invoice__emailAddress____helperText": "A sales invoice will be sent to this address.", "@sage/xtrem-sales/pages__sales_invoice__emailAddress____title": "Email", "@sage/xtrem-sales/pages__sales_invoice__emailFirstName____title": "First name", "@sage/xtrem-sales/pages__sales_invoice__emailLastName____title": "Last name", "@sage/xtrem-sales/pages__sales_invoice__emailTitle____title": "Title", "@sage/xtrem-sales/pages__sales_invoice__externalNote____helperText": "Notes display on customer documents.", "@sage/xtrem-sales/pages__sales_invoice__externalNote____title": "Customer notes", "@sage/xtrem-sales/pages__sales_invoice__externalNoteLine____helperText": "Notes display on customer documents.", "@sage/xtrem-sales/pages__sales_invoice__externalNoteLine____title": "Customer line notes", "@sage/xtrem-sales/pages__sales_invoice__forcedAmountPaid____title": "Forced amount paid", "@sage/xtrem-sales/pages__sales_invoice__fxRateDate____title": "Exchange rate date", "@sage/xtrem-sales/pages__sales_invoice__goToSysNotificationPage____title": "Retry", "@sage/xtrem-sales/pages__sales_invoice__gross_price_greater_or_equal_to_0": "The gross price needs to be greater than or equal to 0.", "@sage/xtrem-sales/pages__sales_invoice__headerSection____title": "Header section", "@sage/xtrem-sales/pages__sales_invoice__incoterm____lookupDialogTitle": "Select incoterms", "@sage/xtrem-sales/pages__sales_invoice__incoterm____title": "Incoterms® rule", "@sage/xtrem-sales/pages__sales_invoice__informationBlock____title": "Information", "@sage/xtrem-sales/pages__sales_invoice__informationSection____title": "Information", "@sage/xtrem-sales/pages__sales_invoice__internalNote____helperText": "Notes display on internal documents.", "@sage/xtrem-sales/pages__sales_invoice__internalNote____title": "Internal notes", "@sage/xtrem-sales/pages__sales_invoice__internalNoteLine____helperText": "Notes display on internal documents.", "@sage/xtrem-sales/pages__sales_invoice__internalNoteLine____title": "Internal line notes", "@sage/xtrem-sales/pages__sales_invoice__invalid-email": "Email address incorrect: {{email}}", "@sage/xtrem-sales/pages__sales_invoice__invoice_date__cannot__be__future": "The invoice date cannot be later than today.", "@sage/xtrem-sales/pages__sales_invoice__isExternalNote____title": "Add notes to customer document", "@sage/xtrem-sales/pages__sales_invoice__isExternalNoteLine____title": "Add notes to customer document", "@sage/xtrem-sales/pages__sales_invoice__isPrinted____title": "Printed", "@sage/xtrem-sales/pages__sales_invoice__isSent____title": "<PERSON><PERSON>", "@sage/xtrem-sales/pages__sales_invoice__isTransferHeaderNote____title": "Repeat the document notes on new documents.", "@sage/xtrem-sales/pages__sales_invoice__isTransferLineNote____title": "Repeat all the line notes on new documents.", "@sage/xtrem-sales/pages__sales_invoice__itemsSection____title": "Lines", "@sage/xtrem-sales/pages__sales_invoice__line_delete_action_dialog_content": "You are about to delete this sales invoice line. This action cannot be undone.", "@sage/xtrem-sales/pages__sales_invoice__line_delete_action_dialog_title": "Confirm delete", "@sage/xtrem-sales/pages__sales_invoice__lines____columns__columns__consumptionLinkedAddress__name__title": "Active", "@sage/xtrem-sales/pages__sales_invoice__lines____columns__columns__consumptionLinkedAddress__name__title__2": "Address line 1", "@sage/xtrem-sales/pages__sales_invoice__lines____columns__columns__consumptionLinkedAddress__name__title__3": "Address line 2", "@sage/xtrem-sales/pages__sales_invoice__lines____columns__columns__consumptionLinkedAddress__name__title__4": "City", "@sage/xtrem-sales/pages__sales_invoice__lines____columns__columns__consumptionLinkedAddress__name__title__5": "Region", "@sage/xtrem-sales/pages__sales_invoice__lines____columns__columns__consumptionLinkedAddress__name__title__6": "ZIP code", "@sage/xtrem-sales/pages__sales_invoice__lines____columns__columns__consumptionLinkedAddress__name__title__7": "Phone number", "@sage/xtrem-sales/pages__sales_invoice__lines____columns__columns__item__name__title": "Category", "@sage/xtrem-sales/pages__sales_invoice__lines____columns__columns__priceReason__name__title": "Priority", "@sage/xtrem-sales/pages__sales_invoice__lines____columns__columns__providerSite__title": "Company", "@sage/xtrem-sales/pages__sales_invoice__lines____columns__columns__stockUnit__symbol__title": "Symbol", "@sage/xtrem-sales/pages__sales_invoice__lines____columns__columns__unit__symbol__title": "Symbol", "@sage/xtrem-sales/pages__sales_invoice__lines____columns__lookupDialogTitle__item__name": "Select item", "@sage/xtrem-sales/pages__sales_invoice__lines____columns__lookupDialogTitle__providerSite": "Select provider site", "@sage/xtrem-sales/pages__sales_invoice__lines____columns__lookupDialogTitle__unit__symbol": "Select unit", "@sage/xtrem-sales/pages__sales_invoice__lines____columns__postfix__charge": "%", "@sage/xtrem-sales/pages__sales_invoice__lines____columns__postfix__discount": "%", "@sage/xtrem-sales/pages__sales_invoice__lines____columns__title__amountExcludingTax": "Total excluding tax", "@sage/xtrem-sales/pages__sales_invoice__lines____columns__title__amountExcludingTaxInCompanyCurrency": "Total excluding tax company currency", "@sage/xtrem-sales/pages__sales_invoice__lines____columns__title__amountIncludingTax": "Total including tax", "@sage/xtrem-sales/pages__sales_invoice__lines____columns__title__amountIncludingTaxInCompanyCurrency": "Total including tax company currency", "@sage/xtrem-sales/pages__sales_invoice__lines____columns__title__charge": "Charge", "@sage/xtrem-sales/pages__sales_invoice__lines____columns__title__consumptionLinkedAddress__name": "Consumption address", "@sage/xtrem-sales/pages__sales_invoice__lines____columns__title__creditStatus": "Credit status", "@sage/xtrem-sales/pages__sales_invoice__lines____columns__title__customerNumber": "Customer order reference", "@sage/xtrem-sales/pages__sales_invoice__lines____columns__title__discount": "Discount", "@sage/xtrem-sales/pages__sales_invoice__lines____columns__title__grossPrice": "Gross price", "@sage/xtrem-sales/pages__sales_invoice__lines____columns__title__grossPriceDeterminated": "Gross price", "@sage/xtrem-sales/pages__sales_invoice__lines____columns__title__grossProfitAmount": "Gross profit amount", "@sage/xtrem-sales/pages__sales_invoice__lines____columns__title__isExternalNote": "Add notes to customer document", "@sage/xtrem-sales/pages__sales_invoice__lines____columns__title__item__id": "Item ID", "@sage/xtrem-sales/pages__sales_invoice__lines____columns__title__item__name": "<PERSON><PERSON>", "@sage/xtrem-sales/pages__sales_invoice__lines____columns__title__itemDescription": "Item description", "@sage/xtrem-sales/pages__sales_invoice__lines____columns__title__netPrice": "Net price", "@sage/xtrem-sales/pages__sales_invoice__lines____columns__title__origin": "Origin", "@sage/xtrem-sales/pages__sales_invoice__lines____columns__title__priceOrigin": "Price origin", "@sage/xtrem-sales/pages__sales_invoice__lines____columns__title__priceReason__name": "Price reason", "@sage/xtrem-sales/pages__sales_invoice__lines____columns__title__providerSite": "Provider site", "@sage/xtrem-sales/pages__sales_invoice__lines____columns__title__quantity": "Quantity in sales unit", "@sage/xtrem-sales/pages__sales_invoice__lines____columns__title__quantityCreditedInProgressInSalesUnit": "Quantity in progress", "@sage/xtrem-sales/pages__sales_invoice__lines____columns__title__quantityCreditedPostedInSalesUnit": "Credited quantity", "@sage/xtrem-sales/pages__sales_invoice__lines____columns__title__quantityInStockUnit": "Quantity in stock unit", "@sage/xtrem-sales/pages__sales_invoice__lines____columns__title__stockCostAmount": "Stock cost amount", "@sage/xtrem-sales/pages__sales_invoice__lines____columns__title__stockUnit__symbol": "Stock unit", "@sage/xtrem-sales/pages__sales_invoice__lines____columns__title__taxAmount": "Total tax", "@sage/xtrem-sales/pages__sales_invoice__lines____columns__title__taxCalculationStatus": "Tax status", "@sage/xtrem-sales/pages__sales_invoice__lines____columns__title__unit__symbol": "Sales unit", "@sage/xtrem-sales/pages__sales_invoice__lines____columns__title__unitToStockUnitConversionFactor": "Stock unit conversion factor", "@sage/xtrem-sales/pages__sales_invoice__lines____dropdownActions__title": "Update price", "@sage/xtrem-sales/pages__sales_invoice__lines____dropdownActions__title__2": "Dimensions", "@sage/xtrem-sales/pages__sales_invoice__lines____dropdownActions__title__3": "Tax details", "@sage/xtrem-sales/pages__sales_invoice__lines____dropdownActions__title__4": "Delete", "@sage/xtrem-sales/pages__sales_invoice__lines____inlineActions__title": "Open line panel", "@sage/xtrem-sales/pages__sales_invoice__lines____mobileCard__line2__title": "Description", "@sage/xtrem-sales/pages__sales_invoice__lines____mobileCard__line2Right__title": "Amount", "@sage/xtrem-sales/pages__sales_invoice__lines____mobileCard__title__title": "Product", "@sage/xtrem-sales/pages__sales_invoice__lines____sidebar__headerDropdownActions__title": "Update price", "@sage/xtrem-sales/pages__sales_invoice__lines____sidebar__headerDropdownActions__title__2": "Dimensions", "@sage/xtrem-sales/pages__sales_invoice__lines____sidebar__headerDropdownActions__title__3": "Tax details", "@sage/xtrem-sales/pages__sales_invoice__lines____sidebar__headerDropdownActions__title__4": "Delete", "@sage/xtrem-sales/pages__sales_invoice__lines____title": "Lines", "@sage/xtrem-sales/pages__sales_invoice__netBalance____title": "Net balance", "@sage/xtrem-sales/pages__sales_invoice__noteBlock____title": "Notes", "@sage/xtrem-sales/pages__sales_invoice__notesSection____title": "Notes", "@sage/xtrem-sales/pages__sales_invoice__number____title": "Number", "@sage/xtrem-sales/pages__sales_invoice__on_price_determination_confirmation": "Do you want to recalculate prices, discounts and charges to reflect the latest price list?", "@sage/xtrem-sales/pages__sales_invoice__on_price_determination_confirmation_invoice_date": "The invoice date has been updated. Do you want to recalculate prices, discounts and charges?", "@sage/xtrem-sales/pages__sales_invoice__on_price_determination_confirmation_provider_site": "The provider site has been updated. Do you want to recalculate prices, discounts and charges?", "@sage/xtrem-sales/pages__sales_invoice__on_price_determination_confirmation_sales_quantity": "The sales quantity has been updated. Do you want to recalculate prices, discounts and charges?", "@sage/xtrem-sales/pages__sales_invoice__on_price_determination_confirmation_sales_unit": "The sales unit has been updated. Do you want to recalculate prices, discounts and charges?", "@sage/xtrem-sales/pages__sales_Invoice__on_price_determination_title": "Search price", "@sage/xtrem-sales/pages__sales_invoice__on_price_determination_too_much_lines_need_save": "The sales invoice contains more than 9 lines. You need to save it before mass calculation. Do you want to save the invoice and continue?", "@sage/xtrem-sales/pages__sales_invoice__open_items_update_error": "The total amount paid could not be updated from Sage Intacct, the sales invoice cannot be printed.", "@sage/xtrem-sales/pages__sales_invoice__paymentsBlock____title": "Payments", "@sage/xtrem-sales/pages__sales_invoice__paymentsSection____title": "Payments", "@sage/xtrem-sales/pages__sales_invoice__paymentTerm____lookupDialogTitle": "Select payment term", "@sage/xtrem-sales/pages__sales_invoice__percentage_greater_than_100": "The percentage cannot exceed 100.", "@sage/xtrem-sales/pages__sales_invoice__percentage_is_negative": "The percentage cannot be negative.", "@sage/xtrem-sales/pages__sales_invoice__post____title": "Post", "@sage/xtrem-sales/pages__sales_invoice__post_action_dialog_content": "You are about to post this sales invoice.", "@sage/xtrem-sales/pages__sales_invoice__post_action_dialog_title": "Confirm posting", "@sage/xtrem-sales/pages__sales_invoice__post_errors": "Posting errors:", "@sage/xtrem-sales/pages__sales_invoice__posted": "The sales invoice was posted.", "@sage/xtrem-sales/pages__sales_invoice__postingDetails____columns__title__financeIntegrationAppRecordId": "Accounting integration reference", "@sage/xtrem-sales/pages__sales_invoice__postingDetails____columns__title__financeIntegrationAppRecordId__2": "Accounting integration reference", "@sage/xtrem-sales/pages__sales_invoice__postingDetails____columns__title__postingStatus": "Status", "@sage/xtrem-sales/pages__sales_invoice__postingDetails____columns__title__targetDocumentNumber": "Document number", "@sage/xtrem-sales/pages__sales_invoice__postingDetails____columns__title__targetDocumentType": "Document type", "@sage/xtrem-sales/pages__sales_invoice__postingDetails____title": "Posting", "@sage/xtrem-sales/pages__sales_invoice__postingMessageBlock____title": "Error details", "@sage/xtrem-sales/pages__sales_invoice__postingSection____title": "Posting", "@sage/xtrem-sales/pages__sales_invoice__print____title": "Print", "@sage/xtrem-sales/pages__sales_invoice__quantity_in_sales_unit_negative_value": "The quantity in sales unit must not be less than or equal to 0.", "@sage/xtrem-sales/pages__sales_invoice__rateDescription____title": "Exchange rate", "@sage/xtrem-sales/pages__sales_invoice__receipt_created": "The following receipt was created: {{receiptNumber}}.", "@sage/xtrem-sales/pages__sales_invoice__recordReceipt____title": "Record receipt", "@sage/xtrem-sales/pages__sales_invoice__repost____title": "Repost", "@sage/xtrem-sales/pages__sales_invoice__repost_accounts_receivable_invoice_already_posted": "The accounts receivable invoice was posted successfully. You can access this document in your financial solution to take further action.", "@sage/xtrem-sales/pages__sales_invoice__repost_documents_already_posted_title": "Repost", "@sage/xtrem-sales/pages__sales_invoice__repost_errors": "Errors occurred while reposting:", "@sage/xtrem-sales/pages__sales_invoice__repost_journal_entry_already_posted": "The journal entry was posted successfully. You can access this document in your financial solution to take further action.", "@sage/xtrem-sales/pages__sales_invoice__salesCreditMemoLines____columns__nestedFields__document__title": "Number", "@sage/xtrem-sales/pages__sales_invoice__salesCreditMemoLines____columns__title___id": "Credit memo number", "@sage/xtrem-sales/pages__sales_invoice__salesCreditMemoLines____columns__title___id__2": "Credit memo status", "@sage/xtrem-sales/pages__sales_invoice__salesCreditMemoLines____columns__title__netPrice": "Amount credited", "@sage/xtrem-sales/pages__sales_invoice__salesCreditMemoLines____columns__title__quantity": "Quantity credited", "@sage/xtrem-sales/pages__sales_invoice__salesCreditMemoLines____title": "Credit memo lines", "@sage/xtrem-sales/pages__sales_invoice__salesInvoiceExcludingTax____title": "Total excluding tax", "@sage/xtrem-sales/pages__sales_invoice__salesInvoiceIncludingTax____title": "Total including tax", "@sage/xtrem-sales/pages__sales_invoice__salesInvoiceLineCount____title": "Number of items", "@sage/xtrem-sales/pages__sales_invoice__salesLinkedDocuments____columns__title___id": "Origin document number", "@sage/xtrem-sales/pages__sales_invoice__salesLinkedDocuments____columns__title__customerNumber": "Customer order reference", "@sage/xtrem-sales/pages__sales_invoice__salesLinkedDocuments____columns__title__displayStatus": "Status", "@sage/xtrem-sales/pages__sales_invoice__salesLinkedDocuments____columns__title__documentType": "Document type", "@sage/xtrem-sales/pages__sales_invoice__salesLinkedDocuments____title": "Shipment", "@sage/xtrem-sales/pages__sales_invoice__save____title": "Save", "@sage/xtrem-sales/pages__sales_invoice__save_warnings": "Warnings while saving:", "@sage/xtrem-sales/pages__sales_invoice__select_bill_to_contact_button_text": "Select bill-to customer contact", "@sage/xtrem-sales/pages__sales_invoice__selectBillToContact____title": "Select bill-to customer contact", "@sage/xtrem-sales/pages__sales_invoice__selectedContact____columns__title__firstName": "First name", "@sage/xtrem-sales/pages__sales_invoice__selectedContact____columns__title__lastName": "Last name", "@sage/xtrem-sales/pages__sales_invoice__selectedContact____columns__title__title": "Title", "@sage/xtrem-sales/pages__sales_invoice__selectedContact____lookupDialogTitle": "Select selected contact", "@sage/xtrem-sales/pages__sales_invoice__selectedContact____title": "Selected contact", "@sage/xtrem-sales/pages__sales_invoice__send_invoice_button_text": "Send", "@sage/xtrem-sales/pages__sales_invoice__send_invoice_dialog_content": "You are about to send the sales invoice email.", "@sage/xtrem-sales/pages__sales_invoice__send_invoice_dialog_title": "Sales invoice email sending confirmation", "@sage/xtrem-sales/pages__sales_invoice__sendEmail____title": "Send", "@sage/xtrem-sales/pages__sales_invoice__sendEmailBlock____title": "To", "@sage/xtrem-sales/pages__sales_invoice__sendEmailSection____title": "Send sales invoice", "@sage/xtrem-sales/pages__sales_invoice__sendInvoiceButton____title": "Send invoice", "@sage/xtrem-sales/pages__sales_invoice__site____columns__title__businessEntity__id": "ID", "@sage/xtrem-sales/pages__sales_invoice__site____columns__title__businessEntity__name": "Name", "@sage/xtrem-sales/pages__sales_invoice__site____columns__title__legalCompany__name": "Company", "@sage/xtrem-sales/pages__sales_invoice__site____lookupDialogTitle": "Select site", "@sage/xtrem-sales/pages__sales_invoice__site____title": "Site", "@sage/xtrem-sales/pages__sales_invoice__status____title": "Posting status", "@sage/xtrem-sales/pages__sales_invoice__step_sequence_creation": "Create", "@sage/xtrem-sales/pages__sales_invoice__step_sequence_credit": "Credit", "@sage/xtrem-sales/pages__sales_invoice__step_sequence_pay": "Pay", "@sage/xtrem-sales/pages__sales_invoice__step_sequence_post": "Post", "@sage/xtrem-sales/pages__sales_invoice__taxCalculationStatus____title": "Tax calculation status", "@sage/xtrem-sales/pages__sales_invoice__taxDetails____columns__postfix__taxRate": "%", "@sage/xtrem-sales/pages__sales_invoice__taxDetails____columns__title__tax": "Tax", "@sage/xtrem-sales/pages__sales_invoice__taxDetails____columns__title__taxableAmount": "Taxable base", "@sage/xtrem-sales/pages__sales_invoice__taxDetails____columns__title__taxAmount": "Amount", "@sage/xtrem-sales/pages__sales_invoice__taxDetails____columns__title__taxCategory": "Category", "@sage/xtrem-sales/pages__sales_invoice__taxDetails____columns__title__taxRate": "Rate", "@sage/xtrem-sales/pages__sales_invoice__taxDetails____title": "Taxes", "@sage/xtrem-sales/pages__sales_invoice__taxEngine____title": "Tax engine", "@sage/xtrem-sales/pages__sales_invoice__totalAmountExcludingTax____title": "Excluding tax", "@sage/xtrem-sales/pages__sales_invoice__totalAmountExcludingTaxInCompanyCurrency____title": "Excluding tax", "@sage/xtrem-sales/pages__sales_invoice__totalAmountIncludingTax____title": "Including tax", "@sage/xtrem-sales/pages__sales_invoice__totalAmountIncludingTaxInCompanyCurrency____title": "Including tax", "@sage/xtrem-sales/pages__sales_invoice__totalAmountPaid____title": "Total amount paid", "@sage/xtrem-sales/pages__sales_invoice__totalGrossProfit____title": "Gross profit", "@sage/xtrem-sales/pages__sales_invoice__totalsSection____title": "Totals", "@sage/xtrem-sales/pages__sales_invoice__totalsSectionCompanyCurrencyDetailsBlock____title": "Amounts company currency", "@sage/xtrem-sales/pages__sales_invoice__totalsSectionTaxDetailsBlock____title": "Summary by tax", "@sage/xtrem-sales/pages__sales_invoice__totalsSectionTaxTotalsBlock____title": "Calculated amounts", "@sage/xtrem-sales/pages__sales_invoice__totalTaxAmount____title": "Tax", "@sage/xtrem-sales/pages__sales_invoice__totalTaxAmountAdjusted____title": "Total tax adjusted", "@sage/xtrem-sales/pages__sales_invoice_line_inquiry____title": "Sales invoice line", "@sage/xtrem-sales/pages__sales_invoice_line_inquiry__company____lookupDialogTitle": "Select company", "@sage/xtrem-sales/pages__sales_invoice_line_inquiry__company____placeholder": "Select company", "@sage/xtrem-sales/pages__sales_invoice_line_inquiry__company____title": "Company", "@sage/xtrem-sales/pages__sales_invoice_line_inquiry__criteriaBlock____title": "Criteria", "@sage/xtrem-sales/pages__sales_invoice_line_inquiry__fromBillToCustomer____columns__title__businessEntity__id": "ID", "@sage/xtrem-sales/pages__sales_invoice_line_inquiry__fromBillToCustomer____columns__title__businessEntity__name": "Name", "@sage/xtrem-sales/pages__sales_invoice_line_inquiry__fromBillToCustomer____columns__title__businessEntity__taxIdNumber": "Tax ID", "@sage/xtrem-sales/pages__sales_invoice_line_inquiry__fromBillToCustomer____lookupDialogTitle": "Select from bill-to customer", "@sage/xtrem-sales/pages__sales_invoice_line_inquiry__fromBillToCustomer____title": "From bill-to customer", "@sage/xtrem-sales/pages__sales_invoice_line_inquiry__fromDate____title": "From date", "@sage/xtrem-sales/pages__sales_invoice_line_inquiry__fromItem____columns__title__category__name": "Category", "@sage/xtrem-sales/pages__sales_invoice_line_inquiry__fromItem____lookupDialogTitle": "Select from item", "@sage/xtrem-sales/pages__sales_invoice_line_inquiry__fromItem____placeholder": "Select item", "@sage/xtrem-sales/pages__sales_invoice_line_inquiry__fromItem____title": "From item", "@sage/xtrem-sales/pages__sales_invoice_line_inquiry__isCreditMemoIncluded____title": "Include sales credit memo", "@sage/xtrem-sales/pages__sales_invoice_line_inquiry__lines____columns__columns__companyCurrency__name__title": "ISO 4217", "@sage/xtrem-sales/pages__sales_invoice_line_inquiry__lines____columns__columns__companyCurrency__name__title__2": "Symbol", "@sage/xtrem-sales/pages__sales_invoice_line_inquiry__lines____columns__columns__companyCurrency__name__title__3": "Decimal digits", "@sage/xtrem-sales/pages__sales_invoice_line_inquiry__lines____columns__columns__currency__name__title": "ISO 4217", "@sage/xtrem-sales/pages__sales_invoice_line_inquiry__lines____columns__columns__currency__name__title__2": "Symbol", "@sage/xtrem-sales/pages__sales_invoice_line_inquiry__lines____columns__columns__currency__name__title__3": "Decimal digits", "@sage/xtrem-sales/pages__sales_invoice_line_inquiry__lines____columns__columns__unit__name__title": "Symbol", "@sage/xtrem-sales/pages__sales_invoice_line_inquiry__lines____columns__title__amountExcludingTax": "Amount", "@sage/xtrem-sales/pages__sales_invoice_line_inquiry__lines____columns__title__amountExcludingTaxInCompanyCurrency": "Amount in company currency", "@sage/xtrem-sales/pages__sales_invoice_line_inquiry__lines____columns__title__city": "City", "@sage/xtrem-sales/pages__sales_invoice_line_inquiry__lines____columns__title__companyCurrency__name": "Company currency", "@sage/xtrem-sales/pages__sales_invoice_line_inquiry__lines____columns__title__currency__name": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-sales/pages__sales_invoice_line_inquiry__lines____columns__title__customerId": "Customer ID", "@sage/xtrem-sales/pages__sales_invoice_line_inquiry__lines____columns__title__customerName": "Customer", "@sage/xtrem-sales/pages__sales_invoice_line_inquiry__lines____columns__title__date": "Date", "@sage/xtrem-sales/pages__sales_invoice_line_inquiry__lines____columns__title__document": "Document", "@sage/xtrem-sales/pages__sales_invoice_line_inquiry__lines____columns__title__documentType": "Document type", "@sage/xtrem-sales/pages__sales_invoice_line_inquiry__lines____columns__title__itemDescription": "Item description", "@sage/xtrem-sales/pages__sales_invoice_line_inquiry__lines____columns__title__itemId": "Item ID", "@sage/xtrem-sales/pages__sales_invoice_line_inquiry__lines____columns__title__itemName": "<PERSON><PERSON>", "@sage/xtrem-sales/pages__sales_invoice_line_inquiry__lines____columns__title__netPrice": "Net price", "@sage/xtrem-sales/pages__sales_invoice_line_inquiry__lines____columns__title__postcode": "Postal code", "@sage/xtrem-sales/pages__sales_invoice_line_inquiry__lines____columns__title__quantity": "Quantity", "@sage/xtrem-sales/pages__sales_invoice_line_inquiry__lines____columns__title__region": "Region", "@sage/xtrem-sales/pages__sales_invoice_line_inquiry__lines____columns__title__salesSiteName": "Sales site", "@sage/xtrem-sales/pages__sales_invoice_line_inquiry__lines____columns__title__unit__name": "Sales unit", "@sage/xtrem-sales/pages__sales_invoice_line_inquiry__lines____title": "Results", "@sage/xtrem-sales/pages__sales_invoice_line_inquiry__mainSection____title": "General", "@sage/xtrem-sales/pages__sales_invoice_line_inquiry__site____columns__title__legalCompany__name": "Company", "@sage/xtrem-sales/pages__sales_invoice_line_inquiry__site____lookupDialogTitle": "Select site", "@sage/xtrem-sales/pages__sales_invoice_line_inquiry__site____placeholder": "Select site", "@sage/xtrem-sales/pages__sales_invoice_line_inquiry__site____title": "Site", "@sage/xtrem-sales/pages__sales_invoice_line_inquiry__toBillToCustomer____columns__title__businessEntity__id": "ID", "@sage/xtrem-sales/pages__sales_invoice_line_inquiry__toBillToCustomer____columns__title__businessEntity__name": "Name", "@sage/xtrem-sales/pages__sales_invoice_line_inquiry__toBillToCustomer____columns__title__businessEntity__taxIdNumber": "Tax ID", "@sage/xtrem-sales/pages__sales_invoice_line_inquiry__toBillToCustomer____lookupDialogTitle": "Select to bill-to customer", "@sage/xtrem-sales/pages__sales_invoice_line_inquiry__toBillToCustomer____title": "To bill-to customer", "@sage/xtrem-sales/pages__sales_invoice_line_inquiry__toDate____title": "To date", "@sage/xtrem-sales/pages__sales_invoice_line_inquiry__toItem____columns__title__category__name": "Category", "@sage/xtrem-sales/pages__sales_invoice_line_inquiry__toItem____lookupDialogTitle": "Select to item", "@sage/xtrem-sales/pages__sales_invoice_line_inquiry__toItem____placeholder": "Select item", "@sage/xtrem-sales/pages__sales_invoice_line_inquiry__toItem____title": "To item", "@sage/xtrem-sales/pages__sales_invoice_line_inquiry__totalAmountInCompanyCurrency____title": "Company total amount", "@sage/xtrem-sales/pages__sales_invoice_line_inquiry__totalNumberOfDocument____title": "Number of documents", "@sage/xtrem-sales/pages__sales_invoice_line_inquiry__totalNumberOfDocumentLine____title": "Number of document lines", "@sage/xtrem-sales/pages__sales_order____navigationPanel__bulkActions__title": "Print", "@sage/xtrem-sales/pages__sales_order____navigationPanel__dropdownActions__title__closeOrder": "Close order", "@sage/xtrem-sales/pages__sales_order____navigationPanel__dropdownActions__title__confirm": "Confirm", "@sage/xtrem-sales/pages__sales_order____navigationPanel__dropdownActions__title__createProformaInvoice": "Create proforma invoice", "@sage/xtrem-sales/pages__sales_order____navigationPanel__dropdownActions__title__createShipment": "Create shipment", "@sage/xtrem-sales/pages__sales_order____navigationPanel__dropdownActions__title__delete": "Delete", "@sage/xtrem-sales/pages__sales_order____navigationPanel__dropdownActions__title__openOrder": "Open order", "@sage/xtrem-sales/pages__sales_order____navigationPanel__dropdownActions__title__print": "Print", "@sage/xtrem-sales/pages__sales_order____navigationPanel__dropdownActions__title__requestAllocation": "Allocate stock", "@sage/xtrem-sales/pages__sales_order____navigationPanel__dropdownActions__title__requestDeallocation": "Deallocate stock", "@sage/xtrem-sales/pages__sales_order____navigationPanel__dropdownActions__title__sendEmail": "Send", "@sage/xtrem-sales/pages__sales_order____navigationPanel__dropdownActions__title__setDimension": "Set dimensions", "@sage/xtrem-sales/pages__sales_order____navigationPanel__inlineActions__title__duplicate": "Duplicate", "@sage/xtrem-sales/pages__sales_order____navigationPanel__listItem__billToCustomerId__title": "Bill-to customer ID", "@sage/xtrem-sales/pages__sales_order____navigationPanel__listItem__isOnHold__title": "On hold", "@sage/xtrem-sales/pages__sales_order____navigationPanel__listItem__line_4__columns__title__legalCompany__name": "Company", "@sage/xtrem-sales/pages__sales_order____navigationPanel__listItem__line_4__title": "Site", "@sage/xtrem-sales/pages__sales_order____navigationPanel__listItem__line_5__title": "Total Including tax", "@sage/xtrem-sales/pages__sales_order____navigationPanel__listItem__line10__title": "Gross profit", "@sage/xtrem-sales/pages__sales_order____navigationPanel__listItem__line11__title": "Tax", "@sage/xtrem-sales/pages__sales_order____navigationPanel__listItem__line12__title": "Printed", "@sage/xtrem-sales/pages__sales_order____navigationPanel__listItem__line13__title": "<PERSON><PERSON>", "@sage/xtrem-sales/pages__sales_order____navigationPanel__listItem__line14__title": "Ship-to customer", "@sage/xtrem-sales/pages__sales_order____navigationPanel__listItem__line15__title": "Stock site", "@sage/xtrem-sales/pages__sales_order____navigationPanel__listItem__line16__title": "Incoterms® rule", "@sage/xtrem-sales/pages__sales_order____navigationPanel__listItem__line17__title": "Delivery mode", "@sage/xtrem-sales/pages__sales_order____navigationPanel__listItem__line18__title": "Requested delivery date", "@sage/xtrem-sales/pages__sales_order____navigationPanel__listItem__line19__postfix": "days", "@sage/xtrem-sales/pages__sales_order____navigationPanel__listItem__line19__title": "Delivery lead time", "@sage/xtrem-sales/pages__sales_order____navigationPanel__listItem__line2__title": "Sold-to customer", "@sage/xtrem-sales/pages__sales_order____navigationPanel__listItem__line20__title": "Shipping date", "@sage/xtrem-sales/pages__sales_order____navigationPanel__listItem__line21__title": "Do-not-ship-before date", "@sage/xtrem-sales/pages__sales_order____navigationPanel__listItem__line22__title": "Do-not-ship-after date", "@sage/xtrem-sales/pages__sales_order____navigationPanel__listItem__line23__title": "Bill-to customer", "@sage/xtrem-sales/pages__sales_order____navigationPanel__listItem__line2Right__title": "Order date", "@sage/xtrem-sales/pages__sales_order____navigationPanel__listItem__line6__title": "Delivery date", "@sage/xtrem-sales/pages__sales_order____navigationPanel__listItem__line7__title": "Customer purchase order number", "@sage/xtrem-sales/pages__sales_order____navigationPanel__listItem__line8__columns__title__decimalDigits": "Decimal points", "@sage/xtrem-sales/pages__sales_order____navigationPanel__listItem__line8__columns__title__name": "Name", "@sage/xtrem-sales/pages__sales_order____navigationPanel__listItem__line8__columns__title__rounding": "Rounding", "@sage/xtrem-sales/pages__sales_order____navigationPanel__listItem__line8__columns__title__symbol": "Symbol", "@sage/xtrem-sales/pages__sales_order____navigationPanel__listItem__line8__title": "Transaction currency", "@sage/xtrem-sales/pages__sales_order____navigationPanel__listItem__line9__title": "Total excluding tax", "@sage/xtrem-sales/pages__sales_order____navigationPanel__listItem__shipToCustomerId__title": "Ship-to customer ID", "@sage/xtrem-sales/pages__sales_order____navigationPanel__listItem__soldToCustomerId__title": "Sold-to customer ID", "@sage/xtrem-sales/pages__sales_order____navigationPanel__listItem__title__title": "Number", "@sage/xtrem-sales/pages__sales_order____navigationPanel__listItem__titleRight__title": "Status", "@sage/xtrem-sales/pages__sales_order____navigationPanel__optionsMenu__title": "All open statuses", "@sage/xtrem-sales/pages__sales_order____navigationPanel__optionsMenu__title__2": "All statuses", "@sage/xtrem-sales/pages__sales_order____navigationPanel__optionsMenu__title__3": "Partially shipped", "@sage/xtrem-sales/pages__sales_order____navigationPanel__optionsMenu__title__4": "Quote", "@sage/xtrem-sales/pages__sales_order____navigationPanel__optionsMenu__title__5": "Confirmed", "@sage/xtrem-sales/pages__sales_order____navigationPanel__optionsMenu__title__6": "Shipped", "@sage/xtrem-sales/pages__sales_order____navigationPanel__optionsMenu__title__7": "Closed", "@sage/xtrem-sales/pages__sales_order____navigationPanel__optionsMenu__title__8": "Tax calculation failed", "@sage/xtrem-sales/pages__sales_order____objectTypePlural": "Sales orders", "@sage/xtrem-sales/pages__sales_order____objectTypeSingular": "Sales order", "@sage/xtrem-sales/pages__sales_order____title": "Sales order", "@sage/xtrem-sales/pages__sales_order___confirm_on_hold_action__action_dialog_content": "You are about to confirm the sales order for an on hold customer.", "@sage/xtrem-sales/pages__sales_order___save_on_hold_action__action_dialog_content": "You are about to update a confirmed sales order for an on hold customer.", "@sage/xtrem-sales/pages__sales_order__allocate_stock_action_dialog_content": "You are about to allocate the sales order for an on hold customer.", "@sage/xtrem-sales/pages__sales_order__allocate_stock_dialog_title": "Confirm update", "@sage/xtrem-sales/pages__sales_order__allocationRequestStatus____title": "Allocation request status", "@sage/xtrem-sales/pages__sales_order__allocationStatus____title": "Stock allocation status", "@sage/xtrem-sales/pages__sales_order__apply_dimensions_success": "Dimensions applied.", "@sage/xtrem-sales/pages__sales_order__assigned_order_action_dialog_content": "You are about to manage an assigned order for an on hold customer.", "@sage/xtrem-sales/pages__sales_order__assigned_order_dialog_title": "Confirm update", "@sage/xtrem-sales/pages__sales_order__auto_allocate_message": "The allocation request was submitted.", "@sage/xtrem-sales/pages__sales_order__auto_allocate_message_title": "Allocation request submitted", "@sage/xtrem-sales/pages__sales_order__auto_deallocate_message": "The deallocation request was submitted.", "@sage/xtrem-sales/pages__sales_order__auto_deallocate_message_title": "Deallocation request submitted", "@sage/xtrem-sales/pages__sales_order__bill_to_customer_on_hold_blocking": "The bill-to customer is on hold.", "@sage/xtrem-sales/pages__sales_order__billToAddress____columns__columns__country__title": "Name", "@sage/xtrem-sales/pages__sales_order__billToAddress____columns__columns__country__title__2": "ISO 3166-1 alpha-2 code", "@sage/xtrem-sales/pages__sales_order__billToAddress____columns__title__addressLine1": "Address line 1", "@sage/xtrem-sales/pages__sales_order__billToAddress____columns__title__addressLine2": "Address line 2", "@sage/xtrem-sales/pages__sales_order__billToAddress____columns__title__city": "City", "@sage/xtrem-sales/pages__sales_order__billToAddress____columns__title__concatenatedAddress": "Bill-to address", "@sage/xtrem-sales/pages__sales_order__billToAddress____columns__title__locationPhoneNumber": "Phone number", "@sage/xtrem-sales/pages__sales_order__billToAddress____columns__title__name": "Name", "@sage/xtrem-sales/pages__sales_order__billToAddress____dropdownActions__title": "Replace", "@sage/xtrem-sales/pages__sales_order__billToAddress____dropdownActions__title__2": "Edit", "@sage/xtrem-sales/pages__sales_order__billToAddress____dropdownActions__title__3": "Read-only", "@sage/xtrem-sales/pages__sales_order__billToAddress____title": "Bill-to address", "@sage/xtrem-sales/pages__sales_order__billToCustomer____columns__title__businessEntity__country__name": "Country", "@sage/xtrem-sales/pages__sales_order__billToCustomer____columns__title__businessEntity__id": "ID", "@sage/xtrem-sales/pages__sales_order__billToCustomer____columns__title__businessEntity__name": "Name", "@sage/xtrem-sales/pages__sales_order__billToCustomer____columns__title__businessEntity__taxIdNumber": "Tax ID", "@sage/xtrem-sales/pages__sales_order__billToCustomer____lookupDialogTitle": "Select bill-to customer", "@sage/xtrem-sales/pages__sales_order__billToCustomer____title": "Bill-to customer", "@sage/xtrem-sales/pages__sales_order__billToLinkedAddress____columns__columns__country__title": "Name", "@sage/xtrem-sales/pages__sales_order__billToLinkedAddress____columns__columns__country__title__2": "ISO 3166-1 alpha-2 code", "@sage/xtrem-sales/pages__sales_order__billToLinkedAddress____columns__title__addressLine1": "Address line 1", "@sage/xtrem-sales/pages__sales_order__billToLinkedAddress____columns__title__addressLine2": "Address line 2", "@sage/xtrem-sales/pages__sales_order__billToLinkedAddress____columns__title__city": "City", "@sage/xtrem-sales/pages__sales_order__billToLinkedAddress____columns__title__isActive": "Active", "@sage/xtrem-sales/pages__sales_order__billToLinkedAddress____columns__title__locationPhoneNumber": "Phone number", "@sage/xtrem-sales/pages__sales_order__billToLinkedAddress____columns__title__name": "Name", "@sage/xtrem-sales/pages__sales_order__billToLinkedAddress____columns__title__postcode": "ZIP code", "@sage/xtrem-sales/pages__sales_order__billToLinkedAddress____columns__title__region": "Region", "@sage/xtrem-sales/pages__sales_order__billToLinkedAddress____lookupDialogTitle": "Select bill-to address", "@sage/xtrem-sales/pages__sales_order__billToLinkedAddress____title": "Bill-to address", "@sage/xtrem-sales/pages__sales_order__cannot_delete_line_allocation_request_in_progress": "Automatic allocation needs to finish before you can delete the line.", "@sage/xtrem-sales/pages__sales_order__cascade_ship_to_address_update_dialog_content": "You are about to update the delivery information.", "@sage/xtrem-sales/pages__sales_order__cascade_ship_to_address_update_dialog_title": "Confirm update", "@sage/xtrem-sales/pages__sales_order__close____title": "Close order", "@sage/xtrem-sales/pages__sales_order__companyCurrency____columns__title__id": "ISO 4217 code", "@sage/xtrem-sales/pages__sales_order__companyCurrency____columns__title__name": "Name", "@sage/xtrem-sales/pages__sales_order__companyCurrency____columns__title__symbol": "Symbol", "@sage/xtrem-sales/pages__sales_order__companyCurrency____lookupDialogTitle": "Select company currency", "@sage/xtrem-sales/pages__sales_order__companyCurrency____placeholder": "Select currency", "@sage/xtrem-sales/pages__sales_order__companyCurrency____title": "Company currency", "@sage/xtrem-sales/pages__sales_order__companyFxRate____title": "Exchange rate", "@sage/xtrem-sales/pages__sales_order__companyFxRateDivisor____title": "Exchange rate divisor", "@sage/xtrem-sales/pages__sales_order__confirm____title": "Confirm", "@sage/xtrem-sales/pages__sales_order__confirm_action_dialog_content": "You are about to set this sales order to 'Confirmed'.", "@sage/xtrem-sales/pages__sales_order__confirm_action_dialog_title": "Confirm update", "@sage/xtrem-sales/pages__sales_order__create_shipment_dialog_content": "You are about to create a shipment from this sales order.", "@sage/xtrem-sales/pages__sales_order__create_shipment_dialog_title": "Confirm shipment creation", "@sage/xtrem-sales/pages__sales_order__create_shipment_on_hold__dialog_content": "You are about to ship the sales order for an on hold customer.", "@sage/xtrem-sales/pages__sales_order__create_shipment_on_hold_dialog_title": "Confirm shipment creation", "@sage/xtrem-sales/pages__sales_order__createProformaInvoice____title": "Create proforma invoice", "@sage/xtrem-sales/pages__sales_order__currency____columns__title__id": "ISO 4217 code", "@sage/xtrem-sales/pages__sales_order__currency____columns__title__name": "Name", "@sage/xtrem-sales/pages__sales_order__currency____columns__title__symbol": "Symbol", "@sage/xtrem-sales/pages__sales_order__currency____lookupDialogTitle": "Select currency", "@sage/xtrem-sales/pages__sales_order__currency____title": "Transaction currency", "@sage/xtrem-sales/pages__sales_order__customerNumber____title": "Customer order reference", "@sage/xtrem-sales/pages__sales_order__date____title": "Order date", "@sage/xtrem-sales/pages__sales_order__defaultDimension____title": "Set dimensions", "@sage/xtrem-sales/pages__sales_order__deleteSalesOrder____title": "Delete", "@sage/xtrem-sales/pages__sales_order__deliveryLeadTime____postfix": "day(s)", "@sage/xtrem-sales/pages__sales_order__deliveryLeadTime____title": "Delivery lead time", "@sage/xtrem-sales/pages__sales_order__deliveryMode____columns__title__name": "Name", "@sage/xtrem-sales/pages__sales_order__deliveryMode____lookupDialogTitle": "Select delivery mode", "@sage/xtrem-sales/pages__sales_order__deliveryMode____title": "Delivery mode", "@sage/xtrem-sales/pages__sales_order__display_bill_to_customer_is_on_hold_status_credit_limit": "Bill-to customer on hold \n\nCredit limit {{currencySymbol}} {{creditLimit}}", "@sage/xtrem-sales/pages__sales_order__display_customer_is_on_hold_status_bill_to_credit_limit": "Customer on hold \n\nCredit limit {{currencySymbol}} {{creditLimit}}", "@sage/xtrem-sales/pages__sales_order__display_customer_is_on_hold_status_credit_limit": "Customer on hold \n\nCredit limit {{currencySymbol}} {{creditLimit}}", "@sage/xtrem-sales/pages__sales_order__displayStatus____title": "Display status", "@sage/xtrem-sales/pages__sales_order__doNotShipAfterDate____title": "Do-not-ship-after date", "@sage/xtrem-sales/pages__sales_order__doNotShipBeforeDate____title": "Do-not-ship-before date", "@sage/xtrem-sales/pages__sales_order__email_sent": "Sales order sent to: ", "@sage/xtrem-sales/pages__sales_order__expectedDeliveryDate____title": "Expected delivery date", "@sage/xtrem-sales/pages__sales_order__expectedDeliveryDateHeader____title": "Expected delivery date", "@sage/xtrem-sales/pages__sales_order__externalNote____helperText": "Notes display on customer documents.", "@sage/xtrem-sales/pages__sales_order__externalNote____title": "Customer notes", "@sage/xtrem-sales/pages__sales_order__externalNoteLine____helperText": "Notes display on customer documents.", "@sage/xtrem-sales/pages__sales_order__externalNoteLine____title": "Customer line notes", "@sage/xtrem-sales/pages__sales_order__financialSection____title": "Financial", "@sage/xtrem-sales/pages__sales_order__financialSectionFinancialBlock____title": "Financial", "@sage/xtrem-sales/pages__sales_order__fxRateDate____title": "Exchange rate date", "@sage/xtrem-sales/pages__sales_order__header_close_action_dialog_content": "You are about to change the status of this order to closed. You can reopen this order later.", "@sage/xtrem-sales/pages__sales_order__header_close_action_dialog_title": "Confirm status change", "@sage/xtrem-sales/pages__sales_order__header_open_action_dialog_content": "You are about to change the status of this order to open.", "@sage/xtrem-sales/pages__sales_order__header_open_action_dialog_title": "Confirm status change", "@sage/xtrem-sales/pages__sales_order__headerSection____title": "Header section", "@sage/xtrem-sales/pages__sales_order__incoterm____lookupDialogTitle": "Select incoterms rule", "@sage/xtrem-sales/pages__sales_order__incoterm____title": "Incoterms® rule", "@sage/xtrem-sales/pages__sales_order__informationBlock____title": "Information", "@sage/xtrem-sales/pages__sales_order__informationSection____title": "Information", "@sage/xtrem-sales/pages__sales_order__internalNote____helperText": "Notes display on internal documents.", "@sage/xtrem-sales/pages__sales_order__internalNote____title": "Internal notes", "@sage/xtrem-sales/pages__sales_order__internalNoteLine____helperText": "Notes display on internal documents.", "@sage/xtrem-sales/pages__sales_order__internalNoteLine____title": "Internal line notes", "@sage/xtrem-sales/pages__sales_order__invoiceStatus____title": "Invoice status", "@sage/xtrem-sales/pages__sales_order__isExternalNote____title": "Add notes to customer document", "@sage/xtrem-sales/pages__sales_order__isExternalNoteLine____title": "Add notes to customer document", "@sage/xtrem-sales/pages__sales_order__isOnHold____title": "On hold", "@sage/xtrem-sales/pages__sales_order__isPrinted____title": "Printed", "@sage/xtrem-sales/pages__sales_order__isSent____title": "<PERSON><PERSON>", "@sage/xtrem-sales/pages__sales_order__isTransferHeaderNote____title": "Repeat the document notes on new documents.", "@sage/xtrem-sales/pages__sales_order__isTransferLineNote____title": "Repeat all the line notes on new documents.", "@sage/xtrem-sales/pages__sales_order__itemSection____title": "Lines", "@sage/xtrem-sales/pages__sales_order__line_close_action_dialog_content": "You are about to close this sales order line.", "@sage/xtrem-sales/pages__sales_order__line_close_action_dialog_title": "Confirm update", "@sage/xtrem-sales/pages__sales_order__line_delete_action_dialog_content": "You are about to delete this sales order line. This action cannot be undone.", "@sage/xtrem-sales/pages__sales_order__line_delete_action_dialog_title": "Confirm delete", "@sage/xtrem-sales/pages__sales_order__line_open_action_dialog_content": "You are about to open this sales order line.", "@sage/xtrem-sales/pages__sales_order__line_open_action_dialog_title": "Confirm update", "@sage/xtrem-sales/pages__sales_order__lines____columns__columns__deliveryMode__name__title": "Name", "@sage/xtrem-sales/pages__sales_order__lines____columns__columns__item__name__title": "Category", "@sage/xtrem-sales/pages__sales_order__lines____columns__columns__priceReason__name__title": "Name", "@sage/xtrem-sales/pages__sales_order__lines____columns__columns__priceReason__name__title__2": "Priority", "@sage/xtrem-sales/pages__sales_order__lines____columns__columns__shipToCustomerAddress__name__columns__columns__mode__name__title": "Name", "@sage/xtrem-sales/pages__sales_order__lines____columns__columns__shipToCustomerAddress__name__columns__columns__shipmentSite__columns__title__id": "ID", "@sage/xtrem-sales/pages__sales_order__lines____columns__columns__shipToCustomerAddress__name__columns__columns__shipmentSite__columns__title__name": "Name", "@sage/xtrem-sales/pages__sales_order__lines____columns__columns__shipToCustomerAddress__name__columns__columns__shipmentSite__title": "Name", "@sage/xtrem-sales/pages__sales_order__lines____columns__columns__shipToCustomerAddress__name__columns__columns__shipmentSite__title__2": "ID", "@sage/xtrem-sales/pages__sales_order__lines____columns__columns__shipToCustomerAddress__name__columns__columns__shipmentSite__title__3": "Company", "@sage/xtrem-sales/pages__sales_order__lines____columns__columns__shipToCustomerAddress__name__columns__postfix__leadTime": "day(s)", "@sage/xtrem-sales/pages__sales_order__lines____columns__columns__shipToCustomerAddress__name__columns__title__id": "ISO 3166-1 alpha-2 code", "@sage/xtrem-sales/pages__sales_order__lines____columns__columns__shipToCustomerAddress__name__columns__title__incoterm": "Incoterms® rule", "@sage/xtrem-sales/pages__sales_order__lines____columns__columns__shipToCustomerAddress__name__columns__title__leadTime": "Delivery lead time", "@sage/xtrem-sales/pages__sales_order__lines____columns__columns__shipToCustomerAddress__name__columns__title__mode__name": "Delivery mode", "@sage/xtrem-sales/pages__sales_order__lines____columns__columns__shipToCustomerAddress__name__columns__title__name": "Name", "@sage/xtrem-sales/pages__sales_order__lines____columns__columns__shipToCustomerAddress__name__columns__title__shipmentSite": "Stock site", "@sage/xtrem-sales/pages__sales_order__lines____columns__columns__shipToCustomerAddress__name__title": "Primary ship-to address", "@sage/xtrem-sales/pages__sales_order__lines____columns__columns__shipToCustomerAddress__name__title__10": "Delivery detail", "@sage/xtrem-sales/pages__sales_order__lines____columns__columns__shipToCustomerAddress__name__title__2": "Active", "@sage/xtrem-sales/pages__sales_order__lines____columns__columns__shipToCustomerAddress__name__title__3": "Name", "@sage/xtrem-sales/pages__sales_order__lines____columns__columns__shipToCustomerAddress__name__title__4": "Address line 1", "@sage/xtrem-sales/pages__sales_order__lines____columns__columns__shipToCustomerAddress__name__title__5": "Address line 2", "@sage/xtrem-sales/pages__sales_order__lines____columns__columns__shipToCustomerAddress__name__title__6": "City", "@sage/xtrem-sales/pages__sales_order__lines____columns__columns__shipToCustomerAddress__name__title__7": "Region", "@sage/xtrem-sales/pages__sales_order__lines____columns__columns__shipToCustomerAddress__name__title__8": "ZIP code", "@sage/xtrem-sales/pages__sales_order__lines____columns__columns__shipToCustomerAddress__name__title__9": "Phone number", "@sage/xtrem-sales/pages__sales_order__lines____columns__columns__stockSite__columns__title__id": "ID", "@sage/xtrem-sales/pages__sales_order__lines____columns__columns__stockSite__columns__title__name": "Name", "@sage/xtrem-sales/pages__sales_order__lines____columns__columns__stockSite__title": "ID", "@sage/xtrem-sales/pages__sales_order__lines____columns__columns__stockSite__title__2": "Name", "@sage/xtrem-sales/pages__sales_order__lines____columns__columns__stockSite__title__3": "Company", "@sage/xtrem-sales/pages__sales_order__lines____columns__columns__stockUnit__name__title": "Name", "@sage/xtrem-sales/pages__sales_order__lines____columns__columns__stockUnit__name__title__2": "Symbol", "@sage/xtrem-sales/pages__sales_order__lines____columns__columns__unit__name__title": "Name", "@sage/xtrem-sales/pages__sales_order__lines____columns__columns__unit__name__title__2": "Symbol", "@sage/xtrem-sales/pages__sales_order__lines____columns__lookupDialogTitle__item__name": "Select item", "@sage/xtrem-sales/pages__sales_order__lines____columns__lookupDialogTitle__priceReason__name": "Select price reason", "@sage/xtrem-sales/pages__sales_order__lines____columns__lookupDialogTitle__stockSite": "Select stock site", "@sage/xtrem-sales/pages__sales_order__lines____columns__lookupDialogTitle__stockUnit__name": "Select unit", "@sage/xtrem-sales/pages__sales_order__lines____columns__lookupDialogTitle__unit__name": "Select unit", "@sage/xtrem-sales/pages__sales_order__lines____columns__postfix__charge": "%", "@sage/xtrem-sales/pages__sales_order__lines____columns__postfix__deliveryLeadTime": "day(s)", "@sage/xtrem-sales/pages__sales_order__lines____columns__postfix__discount": "%", "@sage/xtrem-sales/pages__sales_order__lines____columns__title__allocationRequestStatus": "Allocation request status", "@sage/xtrem-sales/pages__sales_order__lines____columns__title__allocationStatus": "Allocation status", "@sage/xtrem-sales/pages__sales_order__lines____columns__title__amountExcludingTax": "Total excluding tax", "@sage/xtrem-sales/pages__sales_order__lines____columns__title__amountExcludingTaxInCompanyCurrency": "Total excluding tax company currency", "@sage/xtrem-sales/pages__sales_order__lines____columns__title__amountIncludingTax": "Total including tax", "@sage/xtrem-sales/pages__sales_order__lines____columns__title__amountIncludingTaxInCompanyCurrency": "Total including tax company currency", "@sage/xtrem-sales/pages__sales_order__lines____columns__title__availableQuantityInSalesUnit": "Available", "@sage/xtrem-sales/pages__sales_order__lines____columns__title__availableQuantityInStockUnit": "Stock available", "@sage/xtrem-sales/pages__sales_order__lines____columns__title__charge": "Charge", "@sage/xtrem-sales/pages__sales_order__lines____columns__title__deliveryLeadTime": "Delivery lead time", "@sage/xtrem-sales/pages__sales_order__lines____columns__title__deliveryMode__name": "Delivery mode", "@sage/xtrem-sales/pages__sales_order__lines____columns__title__discount": "Discount", "@sage/xtrem-sales/pages__sales_order__lines____columns__title__doNotShipAfterDate": "Do-not-ship-after date", "@sage/xtrem-sales/pages__sales_order__lines____columns__title__doNotShipBeforeDate": "Do-not-ship-before date", "@sage/xtrem-sales/pages__sales_order__lines____columns__title__expectedDeliveryDate": "Expected delivery date", "@sage/xtrem-sales/pages__sales_order__lines____columns__title__grossPrice": "Gross price", "@sage/xtrem-sales/pages__sales_order__lines____columns__title__grossProfitAmount": "Gross profit amount", "@sage/xtrem-sales/pages__sales_order__lines____columns__title__invoicedQuantityInSalesUnit": "Invoiced quantity", "@sage/xtrem-sales/pages__sales_order__lines____columns__title__invoiceStatus": "Invoice status", "@sage/xtrem-sales/pages__sales_order__lines____columns__title__isExternalNote": "Add notes to customer document", "@sage/xtrem-sales/pages__sales_order__lines____columns__title__item__id": "Item ID", "@sage/xtrem-sales/pages__sales_order__lines____columns__title__item__image": "Image", "@sage/xtrem-sales/pages__sales_order__lines____columns__title__item__name": "<PERSON><PERSON>", "@sage/xtrem-sales/pages__sales_order__lines____columns__title__item__status": "Item status", "@sage/xtrem-sales/pages__sales_order__lines____columns__title__itemDescription": "Item description", "@sage/xtrem-sales/pages__sales_order__lines____columns__title__netPrice": "Net price", "@sage/xtrem-sales/pages__sales_order__lines____columns__title__origin": "Origin", "@sage/xtrem-sales/pages__sales_order__lines____columns__title__priceOrigin": "Price origin", "@sage/xtrem-sales/pages__sales_order__lines____columns__title__priceReason__name": "Price reason", "@sage/xtrem-sales/pages__sales_order__lines____columns__title__quantity": "Quantity in sales unit", "@sage/xtrem-sales/pages__sales_order__lines____columns__title__quantityAllocated": "Allocated quantity", "@sage/xtrem-sales/pages__sales_order__lines____columns__title__quantityInStockUnit": "Quantity in stock unit", "@sage/xtrem-sales/pages__sales_order__lines____columns__title__quantityToInvoiceInProgressInSalesUnit": "Quantity in progress", "@sage/xtrem-sales/pages__sales_order__lines____columns__title__quantityToShipInProgressInSalesUnit": "Quantity in progress", "@sage/xtrem-sales/pages__sales_order__lines____columns__title__remainingQuantityToAllocate": "Remaining quantity", "@sage/xtrem-sales/pages__sales_order__lines____columns__title__remainingQuantityToInvoiceInSalesUnit": "Remaining quantity", "@sage/xtrem-sales/pages__sales_order__lines____columns__title__remainingQuantityToShipInSalesUnit": "Remaining quantity", "@sage/xtrem-sales/pages__sales_order__lines____columns__title__remainingQuantityToShipInStockUnit": "Quantity to allocate", "@sage/xtrem-sales/pages__sales_order__lines____columns__title__requestedDeliveryDate": "Requested delivery date", "@sage/xtrem-sales/pages__sales_order__lines____columns__title__shippedQuantityInSalesUnit": "Shipped quantity", "@sage/xtrem-sales/pages__sales_order__lines____columns__title__shippingDate": "Shipping date", "@sage/xtrem-sales/pages__sales_order__lines____columns__title__shippingStatus": "Shipping status", "@sage/xtrem-sales/pages__sales_order__lines____columns__title__shipToCustomerAddress__name": "Ship-to address", "@sage/xtrem-sales/pages__sales_order__lines____columns__title__status": "Status", "@sage/xtrem-sales/pages__sales_order__lines____columns__title__stockCostAmount": "Stock cost amount", "@sage/xtrem-sales/pages__sales_order__lines____columns__title__stockOnHand": "Stock on hand", "@sage/xtrem-sales/pages__sales_order__lines____columns__title__stockShortageInSalesUnit": "Stock shortage", "@sage/xtrem-sales/pages__sales_order__lines____columns__title__stockShortageInStockUnit": "Stock shortage", "@sage/xtrem-sales/pages__sales_order__lines____columns__title__stockShortageStatus": "Stock shortage status", "@sage/xtrem-sales/pages__sales_order__lines____columns__title__stockSite": "Stock site", "@sage/xtrem-sales/pages__sales_order__lines____columns__title__stockUnit__name": "Stock unit", "@sage/xtrem-sales/pages__sales_order__lines____columns__title__taxAmount": "Total tax", "@sage/xtrem-sales/pages__sales_order__lines____columns__title__taxCalculationStatus": "Tax status", "@sage/xtrem-sales/pages__sales_order__lines____columns__title__uAssignmentOrder": "Order assigned", "@sage/xtrem-sales/pages__sales_order__lines____columns__title__unit__name": "Sales unit", "@sage/xtrem-sales/pages__sales_order__lines____columns__title__unitToStockUnitConversionFactor": "Stock unit conversion factor", "@sage/xtrem-sales/pages__sales_order__lines____dropdownActions__title__allocateStock": "Allocate stock", "@sage/xtrem-sales/pages__sales_order__lines____dropdownActions__title__assignOrder": "Assign order", "@sage/xtrem-sales/pages__sales_order__lines____dropdownActions__title__close": "Close", "@sage/xtrem-sales/pages__sales_order__lines____dropdownActions__title__createPurchaseOrder": "Create purchase order", "@sage/xtrem-sales/pages__sales_order__lines____dropdownActions__title__createWorkOrder": "Create work order", "@sage/xtrem-sales/pages__sales_order__lines____dropdownActions__title__delete": "Delete", "@sage/xtrem-sales/pages__sales_order__lines____dropdownActions__title__dimensions": "Dimensions", "@sage/xtrem-sales/pages__sales_order__lines____dropdownActions__title__manageAllocations": "Manage allocations", "@sage/xtrem-sales/pages__sales_order__lines____dropdownActions__title__open": "Open", "@sage/xtrem-sales/pages__sales_order__lines____dropdownActions__title__priceList": "Price list", "@sage/xtrem-sales/pages__sales_order__lines____dropdownActions__title__projectedStock": "Projected stock", "@sage/xtrem-sales/pages__sales_order__lines____dropdownActions__title__taxDetails": "Tax details", "@sage/xtrem-sales/pages__sales_order__lines____dropdownActions__title__updatePrice": "Update price", "@sage/xtrem-sales/pages__sales_order__lines____inlineActions__title__openSidebar": "Open line panel", "@sage/xtrem-sales/pages__sales_order__lines____mobileCard__line2__title": "Description", "@sage/xtrem-sales/pages__sales_order__lines____mobileCard__line2Right__title": "Amount", "@sage/xtrem-sales/pages__sales_order__lines____mobileCard__title__title": "Product", "@sage/xtrem-sales/pages__sales_order__lines____mobileCard__titleRight__title": "Status", "@sage/xtrem-sales/pages__sales_order__lines____optionsMenu__title": "All statuses", "@sage/xtrem-sales/pages__sales_order__lines____optionsMenu__title__2": "All open statuses", "@sage/xtrem-sales/pages__sales_order__lines____optionsMenu__title__3": "Allocation required", "@sage/xtrem-sales/pages__sales_order__lines____optionsMenu__title__4": "My selected data", "@sage/xtrem-sales/pages__sales_order__lines____sidebar__headerDropdownActions__title": "Open", "@sage/xtrem-sales/pages__sales_order__lines____sidebar__headerDropdownActions__title__10": "Price list", "@sage/xtrem-sales/pages__sales_order__lines____sidebar__headerDropdownActions__title__11": "Tax details", "@sage/xtrem-sales/pages__sales_order__lines____sidebar__headerDropdownActions__title__12": "Delete", "@sage/xtrem-sales/pages__sales_order__lines____sidebar__headerDropdownActions__title__2": "Close", "@sage/xtrem-sales/pages__sales_order__lines____sidebar__headerDropdownActions__title__3": "Update price", "@sage/xtrem-sales/pages__sales_order__lines____sidebar__headerDropdownActions__title__4": "Create work order", "@sage/xtrem-sales/pages__sales_order__lines____sidebar__headerDropdownActions__title__5": "Create purchase order", "@sage/xtrem-sales/pages__sales_order__lines____sidebar__headerDropdownActions__title__6": "Assign order", "@sage/xtrem-sales/pages__sales_order__lines____sidebar__headerDropdownActions__title__7": "Projected stock", "@sage/xtrem-sales/pages__sales_order__lines____sidebar__headerDropdownActions__title__8": "Manage allocations", "@sage/xtrem-sales/pages__sales_order__lines____sidebar__headerDropdownActions__title__9": "Dimensions", "@sage/xtrem-sales/pages__sales_order__lines____title": "Lines", "@sage/xtrem-sales/pages__sales_order__noteBlock____title": "Notes", "@sage/xtrem-sales/pages__sales_order__notesSection____title": "Notes", "@sage/xtrem-sales/pages__sales_order__number____title": "Number", "@sage/xtrem-sales/pages__sales_order__on_price_determination_confirmation": "Do you want to recalculate prices, discounts and charges to reflect the latest price list?", "@sage/xtrem-sales/pages__sales_order__on_price_determination_confirmation_order_date": "The order date has been updated. Do you want to recalculate prices, discounts and charges?", "@sage/xtrem-sales/pages__sales_order__on_price_determination_confirmation_stock_site": "The stock site has been updated. Do you want to recalculate prices, discounts and charges?", "@sage/xtrem-sales/pages__sales_order__on_price_determination_title": "Search price", "@sage/xtrem-sales/pages__sales_order__on_price_determination_too_much_lines_need_save": "The sales order contains more than 9 lines. You need to save it before mass calculation. Do you want to save the order and continue?", "@sage/xtrem-sales/pages__sales_order__open____title": "Open order", "@sage/xtrem-sales/pages__sales_order__order_date__cannot__be__future": "The order date cannot be later than today.", "@sage/xtrem-sales/pages__sales_order__paymentTerm____lookupDialogTitle": "Select payment term", "@sage/xtrem-sales/pages__sales_order__price_determination_title": "Update price", "@sage/xtrem-sales/pages__sales_order__print____title": "Print", "@sage/xtrem-sales/pages__sales_order__printed": "The sales order was printed", "@sage/xtrem-sales/pages__sales_order__printed_status_not_updated": "Unable to update order {{number}} to printed status.", "@sage/xtrem-sales/pages__sales_order__proformaInvoices____columns__title__createdBy": "Created by", "@sage/xtrem-sales/pages__sales_order__proformaInvoices____columns__title__expirationDate": "Expiration date", "@sage/xtrem-sales/pages__sales_order__proformaInvoices____columns__title__isSent": "<PERSON><PERSON>", "@sage/xtrem-sales/pages__sales_order__proformaInvoices____columns__title__issueDate": "Issue date", "@sage/xtrem-sales/pages__sales_order__proformaInvoices____columns__title__uploadedFile__downloadUrl": "Link", "@sage/xtrem-sales/pages__sales_order__proformaInvoices____columns__title__uploadedFile__expirationDate": "Link expiration date", "@sage/xtrem-sales/pages__sales_order__proformaInvoices____columns__title__version": "Version", "@sage/xtrem-sales/pages__sales_order__proformaInvoices____title": "Proforma invoices", "@sage/xtrem-sales/pages__sales_order__proformaInvoicesSection____title": "Proforma invoices", "@sage/xtrem-sales/pages__sales_order__purchase_order_assignment_created": "Purchase order {{num}} created.", "@sage/xtrem-sales/pages__sales_order__rateDescription____title": "Exchange rate", "@sage/xtrem-sales/pages__sales_order__recalculate": "Recalculate", "@sage/xtrem-sales/pages__sales_order__recompute_shipping_and_delivery_dates_dialog_content": "You are about to update the shipping date and the delivery date.", "@sage/xtrem-sales/pages__sales_order__recompute_shipping_and_delivery_dates_dialog_title": "Confirm update", "@sage/xtrem-sales/pages__sales_order__requestAllocation____title": "Allocate stock", "@sage/xtrem-sales/pages__sales_order__requestDeallocation____title": "Deallocate stock", "@sage/xtrem-sales/pages__sales_order__requested_delivery_cannot_be_a_not_working_day": "The requested delivery date must fall on a working day.", "@sage/xtrem-sales/pages__sales_order__requestedDeliveryDate____title": "Requested delivery date", "@sage/xtrem-sales/pages__sales_order__salesOrderExcludingTax____title": "Total excluding tax", "@sage/xtrem-sales/pages__sales_order__salesOrderIncludingTax____title": "Total including tax", "@sage/xtrem-sales/pages__sales_order__salesOrderLineCount____title": "Number of items", "@sage/xtrem-sales/pages__sales_order__salesOrderLineShipToAddressDetailPanel____columns__columns__country__title": "Name", "@sage/xtrem-sales/pages__sales_order__salesOrderLineShipToAddressDetailPanel____columns__columns__country__title__2": "ISO 3166-1 alpha-2 code", "@sage/xtrem-sales/pages__sales_order__salesOrderLineShipToAddressDetailPanel____columns__title__addressLine1": "Address line 1", "@sage/xtrem-sales/pages__sales_order__salesOrderLineShipToAddressDetailPanel____columns__title__addressLine2": "Address line 2", "@sage/xtrem-sales/pages__sales_order__salesOrderLineShipToAddressDetailPanel____columns__title__city": "City", "@sage/xtrem-sales/pages__sales_order__salesOrderLineShipToAddressDetailPanel____columns__title__concatenatedAddress": "Bill-to address", "@sage/xtrem-sales/pages__sales_order__salesOrderLineShipToAddressDetailPanel____columns__title__locationPhoneNumber": "Phone number", "@sage/xtrem-sales/pages__sales_order__salesOrderLineShipToAddressDetailPanel____columns__title__name": "Name", "@sage/xtrem-sales/pages__sales_order__salesOrderLineShipToAddressDetailPanel____title": "Ship-to address", "@sage/xtrem-sales/pages__sales_order__salesShipmentLines____columns__title___id": "Shipment number", "@sage/xtrem-sales/pages__sales_order__salesShipmentLines____columns__title__quantity": "Quantity in sales unit", "@sage/xtrem-sales/pages__sales_order__salesShipmentLines____columns__title__status": "Shipment status", "@sage/xtrem-sales/pages__sales_order__salesShipmentLines____title": "Shipment lines", "@sage/xtrem-sales/pages__sales_order__save____title": "Save", "@sage/xtrem-sales/pages__sales_order__save_warnings": "Warnings while saving:", "@sage/xtrem-sales/pages__sales_order__send_sales_order_dialog_content": "You are about to send the sales order email.", "@sage/xtrem-sales/pages__sales_order__send_sales_order_dialog_title": "Sales order email send confirmation", "@sage/xtrem-sales/pages__sales_order__sendEmail____title": "Send", "@sage/xtrem-sales/pages__sales_order__ship____title": "Create shipment", "@sage/xtrem-sales/pages__sales_order__shipping_date_cannot_be_a_earlier_ship_before": "The shipping date ({{value}}) cannot be earlier than the 'Do-not-ship-before' date ({{doNotShipBeforeDate}}).", "@sage/xtrem-sales/pages__sales_order__shipping_date_cannot_be_a_later_ship_after": "The shipping date ({{value}}) cannot be later than the 'Do-not-ship-after' date ({{doNotShipAfterDate}}).", "@sage/xtrem-sales/pages__sales_order__shipping_date_cannot_be_a_less_order_date": "The shipping date ({{value}}) cannot be earlier than the order date ({{date}}).", "@sage/xtrem-sales/pages__sales_order__shippingDate____title": "Shipping date", "@sage/xtrem-sales/pages__sales_order__shippingSection____title": "Shipping", "@sage/xtrem-sales/pages__sales_order__shippingSectionShippingBlock____title": "Shipping", "@sage/xtrem-sales/pages__sales_order__shippingStatus____title": "Shipping status", "@sage/xtrem-sales/pages__sales_order__shipToAddress____columns__columns__country__title": "Name", "@sage/xtrem-sales/pages__sales_order__shipToAddress____columns__columns__country__title__2": "ISO 3166-1 alpha-2 code", "@sage/xtrem-sales/pages__sales_order__shipToAddress____columns__title__addressLine1": "Address line 1", "@sage/xtrem-sales/pages__sales_order__shipToAddress____columns__title__addressLine2": "Address line 2", "@sage/xtrem-sales/pages__sales_order__shipToAddress____columns__title__city": "City", "@sage/xtrem-sales/pages__sales_order__shipToAddress____columns__title__concatenatedAddress": "Bill-to address", "@sage/xtrem-sales/pages__sales_order__shipToAddress____columns__title__locationPhoneNumber": "Phone number", "@sage/xtrem-sales/pages__sales_order__shipToAddress____columns__title__name": "Name", "@sage/xtrem-sales/pages__sales_order__shipToAddress____dropdownActions__title": "Replace", "@sage/xtrem-sales/pages__sales_order__shipToAddress____dropdownActions__title__2": "Edit", "@sage/xtrem-sales/pages__sales_order__shipToAddress____dropdownActions__title__3": "Read-only", "@sage/xtrem-sales/pages__sales_order__shipToAddress____title": "Ship-to address", "@sage/xtrem-sales/pages__sales_order__shipToAddressLineDetail____columns__columns__country__title": "Name", "@sage/xtrem-sales/pages__sales_order__shipToAddressLineDetail____columns__columns__country__title__2": "ISO 3166-1 alpha-2 code", "@sage/xtrem-sales/pages__sales_order__shipToAddressLineDetail____columns__title__addressLine1": "Address line 1", "@sage/xtrem-sales/pages__sales_order__shipToAddressLineDetail____columns__title__addressLine2": "Address line 2", "@sage/xtrem-sales/pages__sales_order__shipToAddressLineDetail____columns__title__city": "City", "@sage/xtrem-sales/pages__sales_order__shipToAddressLineDetail____columns__title__concatenatedAddress": "Bill-to address", "@sage/xtrem-sales/pages__sales_order__shipToAddressLineDetail____columns__title__locationPhoneNumber": "Phone number", "@sage/xtrem-sales/pages__sales_order__shipToAddressLineDetail____columns__title__name": "Name", "@sage/xtrem-sales/pages__sales_order__shipToAddressLineDetail____dropdownActions__title": "Replace", "@sage/xtrem-sales/pages__sales_order__shipToAddressLineDetail____dropdownActions__title__2": "Edit", "@sage/xtrem-sales/pages__sales_order__shipToAddressLineDetail____dropdownActions__title__3": "Read-only", "@sage/xtrem-sales/pages__sales_order__shipToAddressLineDetail____title": "Ship-to address", "@sage/xtrem-sales/pages__sales_order__shipToCustomer____columns__title__businessEntity__country__name": "Country", "@sage/xtrem-sales/pages__sales_order__shipToCustomer____columns__title__businessEntity__id": "ID", "@sage/xtrem-sales/pages__sales_order__shipToCustomer____columns__title__businessEntity__name": "Name", "@sage/xtrem-sales/pages__sales_order__shipToCustomer____columns__title__businessEntity__taxIdNumber": "Tax ID", "@sage/xtrem-sales/pages__sales_order__shipToCustomer____lookupDialogTitle": "Select ship-to customer", "@sage/xtrem-sales/pages__sales_order__shipToCustomer____title": "Ship-to customer", "@sage/xtrem-sales/pages__sales_order__shipToCustomerAddress____columns__columns__country__title": "Name", "@sage/xtrem-sales/pages__sales_order__shipToCustomerAddress____columns__columns__country__title__2": "ISO 3166-1 alpha-2 code", "@sage/xtrem-sales/pages__sales_order__shipToCustomerAddress____columns__columns__deliveryDetail___id__columns__columns__legalCompany__name__title": "Name", "@sage/xtrem-sales/pages__sales_order__shipToCustomerAddress____columns__columns__deliveryDetail___id__columns__columns__legalCompany__name__title__2": "ID", "@sage/xtrem-sales/pages__sales_order__shipToCustomerAddress____columns__columns__deliveryDetail___id__columns__title__id": "ID", "@sage/xtrem-sales/pages__sales_order__shipToCustomerAddress____columns__columns__deliveryDetail___id__columns__title__id__2": "ID", "@sage/xtrem-sales/pages__sales_order__shipToCustomerAddress____columns__columns__deliveryDetail___id__columns__title__legalCompany__name": "Company", "@sage/xtrem-sales/pages__sales_order__shipToCustomerAddress____columns__columns__deliveryDetail___id__columns__title__name": "Name", "@sage/xtrem-sales/pages__sales_order__shipToCustomerAddress____columns__columns__deliveryDetail___id__columns__title__name__2": "Name", "@sage/xtrem-sales/pages__sales_order__shipToCustomerAddress____columns__columns__deliveryDetail___id__columns__title__name__3": "Name", "@sage/xtrem-sales/pages__sales_order__shipToCustomerAddress____columns__columns__deliveryDetail___id__postfix": "day(s)", "@sage/xtrem-sales/pages__sales_order__shipToCustomerAddress____columns__columns__deliveryDetail___id__title": "Delivery lead time", "@sage/xtrem-sales/pages__sales_order__shipToCustomerAddress____columns__columns__deliveryDetail___id__title__2": "Incoterms® rule", "@sage/xtrem-sales/pages__sales_order__shipToCustomerAddress____columns__columns__deliveryDetail___id__title__3": "Delivery mode", "@sage/xtrem-sales/pages__sales_order__shipToCustomerAddress____columns__columns__deliveryDetail___id__title__4": "Stock site", "@sage/xtrem-sales/pages__sales_order__shipToCustomerAddress____columns__title__addressLine1": "Address line 1", "@sage/xtrem-sales/pages__sales_order__shipToCustomerAddress____columns__title__addressLine2": "Address line 2", "@sage/xtrem-sales/pages__sales_order__shipToCustomerAddress____columns__title__city": "City", "@sage/xtrem-sales/pages__sales_order__shipToCustomerAddress____columns__title__deliveryDetail___id": "Ship-to address", "@sage/xtrem-sales/pages__sales_order__shipToCustomerAddress____columns__title__deliveryDetail__isPrimary": "Primary address", "@sage/xtrem-sales/pages__sales_order__shipToCustomerAddress____columns__title__isActive": "Active", "@sage/xtrem-sales/pages__sales_order__shipToCustomerAddress____columns__title__locationPhoneNumber": "Phone number", "@sage/xtrem-sales/pages__sales_order__shipToCustomerAddress____columns__title__name": "Name", "@sage/xtrem-sales/pages__sales_order__shipToCustomerAddress____columns__title__postcode": "ZIP code", "@sage/xtrem-sales/pages__sales_order__shipToCustomerAddress____columns__title__region": "Region", "@sage/xtrem-sales/pages__sales_order__shipToCustomerAddress____lookupDialogTitle": "Select ship-to address", "@sage/xtrem-sales/pages__sales_order__shipToCustomerAddress____title": "Ship-to address", "@sage/xtrem-sales/pages__sales_order__site____columns__columns__legalCompany__name__title": "Name", "@sage/xtrem-sales/pages__sales_order__site____columns__columns__legalCompany__name__title__2": "ID", "@sage/xtrem-sales/pages__sales_order__site____columns__title__id": "ID", "@sage/xtrem-sales/pages__sales_order__site____columns__title__legalCompany__name": "Company", "@sage/xtrem-sales/pages__sales_order__site____columns__title__name": "Name", "@sage/xtrem-sales/pages__sales_order__site____lookupDialogTitle": "Select site", "@sage/xtrem-sales/pages__sales_order__site____title": "Site", "@sage/xtrem-sales/pages__sales_order__soldToAddress____columns__columns__country__title": "Name", "@sage/xtrem-sales/pages__sales_order__soldToAddress____columns__columns__country__title__2": "ISO 3166-1 alpha-2 code", "@sage/xtrem-sales/pages__sales_order__soldToAddress____columns__title__addressLine1": "Address line 1", "@sage/xtrem-sales/pages__sales_order__soldToAddress____columns__title__addressLine2": "Address line 2", "@sage/xtrem-sales/pages__sales_order__soldToAddress____columns__title__city": "City", "@sage/xtrem-sales/pages__sales_order__soldToAddress____columns__title__concatenatedAddress": "Bill-to address", "@sage/xtrem-sales/pages__sales_order__soldToAddress____columns__title__locationPhoneNumber": "Phone number", "@sage/xtrem-sales/pages__sales_order__soldToAddress____columns__title__name": "Name", "@sage/xtrem-sales/pages__sales_order__soldToAddress____dropdownActions__title": "Replace", "@sage/xtrem-sales/pages__sales_order__soldToAddress____dropdownActions__title__2": "Edit", "@sage/xtrem-sales/pages__sales_order__soldToAddress____dropdownActions__title__3": "Read-only", "@sage/xtrem-sales/pages__sales_order__soldToAddress____title": "Sold-to address", "@sage/xtrem-sales/pages__sales_order__soldToContact____columns__title__firstName": "First name", "@sage/xtrem-sales/pages__sales_order__soldToContact____columns__title__lastName": "Last name", "@sage/xtrem-sales/pages__sales_order__soldToContact____columns__title__title": "Title", "@sage/xtrem-sales/pages__sales_order__soldToContact____lookupDialogTitle": "Select sold-to customer address contact detail", "@sage/xtrem-sales/pages__sales_order__soldToContact____title": "Sold-to customer address contact detail", "@sage/xtrem-sales/pages__sales_order__soldToCustomer____columns__title__businessEntity__country__name": "Country", "@sage/xtrem-sales/pages__sales_order__soldToCustomer____columns__title__businessEntity__id": "ID", "@sage/xtrem-sales/pages__sales_order__soldToCustomer____columns__title__businessEntity__name": "Name", "@sage/xtrem-sales/pages__sales_order__soldToCustomer____columns__title__businessEntity__taxIdNumber": "Tax ID", "@sage/xtrem-sales/pages__sales_order__soldToCustomer____lookupDialogTitle": "Select sold-to customer", "@sage/xtrem-sales/pages__sales_order__soldToCustomer____title": "Sold-to customer", "@sage/xtrem-sales/pages__sales_order__soldToLinkedAddress____columns__columns__country__title": "Name", "@sage/xtrem-sales/pages__sales_order__soldToLinkedAddress____columns__columns__country__title__2": "ISO 3166-1 alpha-2 code", "@sage/xtrem-sales/pages__sales_order__soldToLinkedAddress____columns__title__addressLine1": "Address line 1", "@sage/xtrem-sales/pages__sales_order__soldToLinkedAddress____columns__title__addressLine2": "Address line 2", "@sage/xtrem-sales/pages__sales_order__soldToLinkedAddress____columns__title__city": "City", "@sage/xtrem-sales/pages__sales_order__soldToLinkedAddress____columns__title__isActive": "Active", "@sage/xtrem-sales/pages__sales_order__soldToLinkedAddress____columns__title__locationPhoneNumber": "Phone number", "@sage/xtrem-sales/pages__sales_order__soldToLinkedAddress____columns__title__name": "Name", "@sage/xtrem-sales/pages__sales_order__soldToLinkedAddress____columns__title__postcode": "ZIP code", "@sage/xtrem-sales/pages__sales_order__soldToLinkedAddress____columns__title__region": "Region", "@sage/xtrem-sales/pages__sales_order__soldToLinkedAddress____lookupDialogTitle": "Select sold-to address", "@sage/xtrem-sales/pages__sales_order__soldToLinkedAddress____title": "Sold-to address", "@sage/xtrem-sales/pages__sales_order__status____title": "Status", "@sage/xtrem-sales/pages__sales_order__step_sequence_confirm": "Confirm", "@sage/xtrem-sales/pages__sales_order__step_sequence_creation": "Create", "@sage/xtrem-sales/pages__sales_order__step_sequence_invoice": "Invoice", "@sage/xtrem-sales/pages__sales_order__step_sequence_ship": "Ship", "@sage/xtrem-sales/pages__sales_order__stockSite____columns__columns__legalCompany__name__title": "Name", "@sage/xtrem-sales/pages__sales_order__stockSite____columns__columns__legalCompany__name__title__2": "ID", "@sage/xtrem-sales/pages__sales_order__stockSite____columns__title__businessEntity__id": "ID", "@sage/xtrem-sales/pages__sales_order__stockSite____columns__title__businessEntity__name": "Name", "@sage/xtrem-sales/pages__sales_order__stockSite____columns__title__legalCompany__name": "Company", "@sage/xtrem-sales/pages__sales_order__stockSite____lookupDialogTitle": "Select stock site", "@sage/xtrem-sales/pages__sales_order__stockSite____title": "Stock site", "@sage/xtrem-sales/pages__sales_order__tax_calculation_failed_blocking": "You need to resolve tax calculation issues before confirming.", "@sage/xtrem-sales/pages__sales_order__taxCalculationStatus____title": "Tax calculation status", "@sage/xtrem-sales/pages__sales_order__taxDetails____columns__postfix__taxRate": "%", "@sage/xtrem-sales/pages__sales_order__taxDetails____columns__title__tax": "Tax", "@sage/xtrem-sales/pages__sales_order__taxDetails____columns__title__taxableAmount": "Taxable base", "@sage/xtrem-sales/pages__sales_order__taxDetails____columns__title__taxAmount": "Amount", "@sage/xtrem-sales/pages__sales_order__taxDetails____columns__title__taxCategory": "Category", "@sage/xtrem-sales/pages__sales_order__taxDetails____columns__title__taxRate": "Rate", "@sage/xtrem-sales/pages__sales_order__taxDetails____title": "Taxes", "@sage/xtrem-sales/pages__sales_order__taxEngine____title": "Tax engine", "@sage/xtrem-sales/pages__sales_order__toInvoiceLines____columns__title___id": "Invoice number", "@sage/xtrem-sales/pages__sales_order__toInvoiceLines____columns__title__amountExcludingTax": "Amount invoiced", "@sage/xtrem-sales/pages__sales_order__toInvoiceLines____columns__title__document__status": "Invoice status", "@sage/xtrem-sales/pages__sales_order__toInvoiceLines____columns__title__quantity": "Quantity in sales unit", "@sage/xtrem-sales/pages__sales_order__toInvoiceLines____title": "Invoice lines", "@sage/xtrem-sales/pages__sales_order__totalAmountExcludingTax____title": "Excluding tax", "@sage/xtrem-sales/pages__sales_order__totalAmountExcludingTaxInCompanyCurrency____title": "Excluding tax", "@sage/xtrem-sales/pages__sales_order__totalAmountIncludingTax____title": "Including tax", "@sage/xtrem-sales/pages__sales_order__totalAmountIncludingTaxInCompanyCurrency____title": "Including tax", "@sage/xtrem-sales/pages__sales_order__totalGrossProfit____title": "Gross profit", "@sage/xtrem-sales/pages__sales_order__totalsSection____title": "Totals", "@sage/xtrem-sales/pages__sales_order__totalsSectionCompanyCurrencyDetailsBlock____title": "Amounts company currency", "@sage/xtrem-sales/pages__sales_order__totalsSectionTaxDetailsBlock____title": "Summary by tax", "@sage/xtrem-sales/pages__sales_order__totalsSectionTaxTotalsBlock____title": "", "@sage/xtrem-sales/pages__sales_order__totalTaxAmount____title": "Tax", "@sage/xtrem-sales/pages__sales_order__totalTaxAmountAdjusted____title": "Total tax adjusted", "@sage/xtrem-sales/pages__sales_order__wo_po_dialog_title": "Confirm update", "@sage/xtrem-sales/pages__sales_order__wo_po_order_action_dialog_content": "You are about to create an assigned order for an on hold customer.", "@sage/xtrem-sales/pages__sales_order__work_order_assignment_created": "Work order {{num}} created.", "@sage/xtrem-sales/pages__sales_order__work_order_stock_site_is_not_manufacturing_site": "The stock site on the line is not a manufacturing site.", "@sage/xtrem-sales/pages__sales_order__workDays____title": "Work days", "@sage/xtrem-sales/pages__sales_order_assignment_already_exists": "Assignment already exists.", "@sage/xtrem-sales/pages__sales_order_confirm_on_hold_action_dialog_title": "Customer on hold", "@sage/xtrem-sales/pages__sales_order_line_assignment_details_panel____title": "Assigned orders", "@sage/xtrem-sales/pages__sales_order_line_assignment_details_panel__assigned_quantity_higher_than_quantity_in_stock_unit": "You have assigned more than the sales order quantity. You need to reduce the assigned quantity.", "@sage/xtrem-sales/pages__sales_order_line_assignment_details_panel__assigned_quantity_higher_than_quantity_on_order": "You have assigned more than the quantity on order. You need to reduce the assigned quantity.", "@sage/xtrem-sales/pages__sales_order_line_assignment_details_panel__assignedQuantity____title": "Assigned quantity", "@sage/xtrem-sales/pages__sales_order_line_assignment_details_panel__assignmentLines____columns__title__quantityInStockUnit": "Assigned quantity", "@sage/xtrem-sales/pages__sales_order_line_assignment_details_panel__assignmentLines____columns__title__quantityNotAssignedOnSupplyOrder": "Quantity not assigned", "@sage/xtrem-sales/pages__sales_order_line_assignment_details_panel__assignmentLines____columns__title__supplyDocumentLine": "Order", "@sage/xtrem-sales/pages__sales_order_line_assignment_details_panel__assignmentLines____columns__title__supplyType": "Order assigned", "@sage/xtrem-sales/pages__sales_order_line_assignment_details_panel__assignmentLines____columns__title__supplyWorkInProgress__expectedQuantity": "Quantity on order", "@sage/xtrem-sales/pages__sales_order_line_assignment_details_panel__assignmentLines____dropdownActions__title": "Delete", "@sage/xtrem-sales/pages__sales_order_line_assignment_details_panel__assignmentLines____title": "Assigned orders", "@sage/xtrem-sales/pages__sales_order_line_assignment_details_panel__cancel____title": "Cancel", "@sage/xtrem-sales/pages__sales_order_line_assignment_details_panel__componentBlock____title": "General", "@sage/xtrem-sales/pages__sales_order_line_assignment_details_panel__confirm____title": "Save", "@sage/xtrem-sales/pages__sales_order_line_assignment_details_panel__line_deletion_dialog_content": "You are about to delete the link to the order.", "@sage/xtrem-sales/pages__sales_order_line_assignment_details_panel__line_deletion_dialog_title": "Delete assigned line", "@sage/xtrem-sales/pages__sales_order_line_assignment_details_panel__line-cancel": "Cancel", "@sage/xtrem-sales/pages__sales_order_line_assignment_details_panel__line-delete": "Delete", "@sage/xtrem-sales/pages__sales_order_line_assignment_details_panel__remainingQuantity____title": "Remaining quantity", "@sage/xtrem-sales/pages__sales_order_line_assignment_details_panel__remainingQuantityToShipInStockUnit____title": "Remaining quantity to ship", "@sage/xtrem-sales/pages__sales_order_line_assignment_details_panel__requiredQuantity____title": "Required quantity", "@sage/xtrem-sales/pages__sales_order_line_inquiry____navigationPanel__listItem__amountExcludingTax__title": "Ordered amount", "@sage/xtrem-sales/pages__sales_order_line_inquiry____navigationPanel__listItem__amountExcludingTaxInCompanyCurrency__title": "Ordered amount in company currency", "@sage/xtrem-sales/pages__sales_order_line_inquiry____navigationPanel__listItem__companyCurrencyId__title": "Company currency ID", "@sage/xtrem-sales/pages__sales_order_line_inquiry____navigationPanel__listItem__companyCurrencyName__title": "Company currency", "@sage/xtrem-sales/pages__sales_order_line_inquiry____navigationPanel__listItem__companyId__title": "Company ID", "@sage/xtrem-sales/pages__sales_order_line_inquiry____navigationPanel__listItem__companyName__title": "Company", "@sage/xtrem-sales/pages__sales_order_line_inquiry____navigationPanel__listItem__currencyId__title": "Currency ID", "@sage/xtrem-sales/pages__sales_order_line_inquiry____navigationPanel__listItem__currencyName__title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-sales/pages__sales_order_line_inquiry____navigationPanel__listItem__customerId__title": "Sold-to customer ID", "@sage/xtrem-sales/pages__sales_order_line_inquiry____navigationPanel__listItem__customerName__title": "Sold-to customer", "@sage/xtrem-sales/pages__sales_order_line_inquiry____navigationPanel__listItem__customerNumber__title": "Customer reference", "@sage/xtrem-sales/pages__sales_order_line_inquiry____navigationPanel__listItem__date__title": "Order date", "@sage/xtrem-sales/pages__sales_order_line_inquiry____navigationPanel__listItem__grossProfitAmountInCompanyCurrency__title": "Gross profit amount in company currency", "@sage/xtrem-sales/pages__sales_order_line_inquiry____navigationPanel__listItem__invoiceStatus__title": "Invoice status", "@sage/xtrem-sales/pages__sales_order_line_inquiry____navigationPanel__listItem__itemDescription__title": "Item description", "@sage/xtrem-sales/pages__sales_order_line_inquiry____navigationPanel__listItem__itemId__title": "Item ID", "@sage/xtrem-sales/pages__sales_order_line_inquiry____navigationPanel__listItem__itemName__title": "<PERSON><PERSON>", "@sage/xtrem-sales/pages__sales_order_line_inquiry____navigationPanel__listItem__lineStatus__title": "Status", "@sage/xtrem-sales/pages__sales_order_line_inquiry____navigationPanel__listItem__netPrice__title": "Net price", "@sage/xtrem-sales/pages__sales_order_line_inquiry____navigationPanel__listItem__quantity__title": "Quantity", "@sage/xtrem-sales/pages__sales_order_line_inquiry____navigationPanel__listItem__remainingAmount__title": "Remaining amount", "@sage/xtrem-sales/pages__sales_order_line_inquiry____navigationPanel__listItem__remainingAmountInCompanyCurrency__title": "Remaining amount in company currency", "@sage/xtrem-sales/pages__sales_order_line_inquiry____navigationPanel__listItem__remainingQuantityToShipInSalesUnit__title": "Remaining quantity", "@sage/xtrem-sales/pages__sales_order_line_inquiry____navigationPanel__listItem__salesOrderNumber__title": "Sales order", "@sage/xtrem-sales/pages__sales_order_line_inquiry____navigationPanel__listItem__shippingDate__title": "Shipping date", "@sage/xtrem-sales/pages__sales_order_line_inquiry____navigationPanel__listItem__shippingStatus__title": "Shipping status", "@sage/xtrem-sales/pages__sales_order_line_inquiry____navigationPanel__listItem__siteId__title": "Site ID", "@sage/xtrem-sales/pages__sales_order_line_inquiry____navigationPanel__listItem__siteName__title": "Site", "@sage/xtrem-sales/pages__sales_order_line_inquiry____navigationPanel__listItem__unit__title": "Sales unit", "@sage/xtrem-sales/pages__sales_order_line_inquiry____title": "Sales order line", "@sage/xtrem-sales/pages__sales_order_line_panel__display_shortage_status_false": "Stock available", "@sage/xtrem-sales/pages__sales_order_line_panel__display_shortage_status_true": "Stock shortage", "@sage/xtrem-sales/pages__sales_order_line_panel__gross_price_greater_or_equal_to_0": "The gross price must be higher or equal to 0.", "@sage/xtrem-sales/pages__sales_order_line_panel__quantity_in_sales_unit_negative_value": "The quantity in sales unit must not be less than or equal to 0.", "@sage/xtrem-sales/pages__sales_order_mass_allocation____title": "Sales order mass allocation", "@sage/xtrem-sales/pages__sales_order_mass_allocation__action____title": "Action", "@sage/xtrem-sales/pages__sales_order_mass_allocation__allocate____title": "Allocate", "@sage/xtrem-sales/pages__sales_order_mass_allocation__company____columns__title__id": "ID", "@sage/xtrem-sales/pages__sales_order_mass_allocation__company____columns__title__name": "Name", "@sage/xtrem-sales/pages__sales_order_mass_allocation__company____lookupDialogTitle": "Select company", "@sage/xtrem-sales/pages__sales_order_mass_allocation__company____title": "Company", "@sage/xtrem-sales/pages__sales_order_mass_allocation__deallocate____title": "Deallocate", "@sage/xtrem-sales/pages__sales_order_mass_allocation__deliveryMode____lookupDialogTitle": "Select delivery mode", "@sage/xtrem-sales/pages__sales_order_mass_allocation__deliveryMode____title": "Delivery mode", "@sage/xtrem-sales/pages__sales_order_mass_allocation__description____title": "Description", "@sage/xtrem-sales/pages__sales_order_mass_allocation__fromItem____columns__title__category__name": "Category", "@sage/xtrem-sales/pages__sales_order_mass_allocation__fromItem____lookupDialogTitle": "Select from item", "@sage/xtrem-sales/pages__sales_order_mass_allocation__fromItem____title": "From item", "@sage/xtrem-sales/pages__sales_order_mass_allocation__fromSalesOrder____columns__title__date": "Order date", "@sage/xtrem-sales/pages__sales_order_mass_allocation__fromSalesOrder____columns__title__number": "Number", "@sage/xtrem-sales/pages__sales_order_mass_allocation__fromSalesOrder____lookupDialogTitle": "Select from sales order", "@sage/xtrem-sales/pages__sales_order_mass_allocation__fromSalesOrder____title": "From sales order", "@sage/xtrem-sales/pages__sales_order_mass_allocation__fromSoldToCustomer____columns__title__businessEntity__country__name": "Country", "@sage/xtrem-sales/pages__sales_order_mass_allocation__fromSoldToCustomer____columns__title__businessEntity__id": "ID", "@sage/xtrem-sales/pages__sales_order_mass_allocation__fromSoldToCustomer____columns__title__businessEntity__name": "Name", "@sage/xtrem-sales/pages__sales_order_mass_allocation__fromSoldToCustomer____columns__title__businessEntity__taxIdNumber": "Tax ID", "@sage/xtrem-sales/pages__sales_order_mass_allocation__fromSoldToCustomer____lookupDialogTitle": "Select from sold-to customer", "@sage/xtrem-sales/pages__sales_order_mass_allocation__fromSoldToCustomer____title": "From sold-to customer", "@sage/xtrem-sales/pages__sales_order_mass_allocation__incoterm____lookupDialogTitle": "Select Incoterms rule", "@sage/xtrem-sales/pages__sales_order_mass_allocation__incoterm____title": "Incoterms® rule", "@sage/xtrem-sales/pages__sales_order_mass_allocation__latestShippingDate____title": "Latest shipping date", "@sage/xtrem-sales/pages__sales_order_mass_allocation__mainSection____title": "General", "@sage/xtrem-sales/pages__sales_order_mass_allocation__message": "The allocation request was submitted.", "@sage/xtrem-sales/pages__sales_order_mass_allocation__schedule____title": "Schedule", "@sage/xtrem-sales/pages__sales_order_mass_allocation__stockSite____columns__columns__legalCompany__name__title": "Name", "@sage/xtrem-sales/pages__sales_order_mass_allocation__stockSite____columns__columns__legalCompany__name__title__2": "ID", "@sage/xtrem-sales/pages__sales_order_mass_allocation__stockSite____columns__title__id": "ID", "@sage/xtrem-sales/pages__sales_order_mass_allocation__stockSite____columns__title__legalCompany__name": "Company", "@sage/xtrem-sales/pages__sales_order_mass_allocation__stockSite____columns__title__name": "Name", "@sage/xtrem-sales/pages__sales_order_mass_allocation__stockSite____lookupDialogTitle": "Select site", "@sage/xtrem-sales/pages__sales_order_mass_allocation__stockSite____title": "Site", "@sage/xtrem-sales/pages__sales_order_mass_allocation__title": "Allocation request submitted", "@sage/xtrem-sales/pages__sales_order_mass_allocation__toItem____columns__title__category__name": "Category", "@sage/xtrem-sales/pages__sales_order_mass_allocation__toItem____lookupDialogTitle": "Select to item", "@sage/xtrem-sales/pages__sales_order_mass_allocation__toItem____title": "To item", "@sage/xtrem-sales/pages__sales_order_mass_allocation__toSalesOrder____columns__title__date": "Order date", "@sage/xtrem-sales/pages__sales_order_mass_allocation__toSalesOrder____columns__title__number": "Number", "@sage/xtrem-sales/pages__sales_order_mass_allocation__toSalesOrder____lookupDialogTitle": "Select to sales order", "@sage/xtrem-sales/pages__sales_order_mass_allocation__toSalesOrder____title": "To sales order", "@sage/xtrem-sales/pages__sales_order_mass_allocation__toSoldToCustomer____columns__title__businessEntity__id": "ID", "@sage/xtrem-sales/pages__sales_order_mass_allocation__toSoldToCustomer____columns__title__businessEntity__name": "Name", "@sage/xtrem-sales/pages__sales_order_mass_allocation__toSoldToCustomer____columns__title__businessEntity__taxIdNumber": "Tax ID", "@sage/xtrem-sales/pages__sales_order_mass_allocation__toSoldToCustomer____lookupDialogTitle": "Select to sold-to customer", "@sage/xtrem-sales/pages__sales_order_mass_allocation__toSoldToCustomer____title": "To sold-to customer", "@sage/xtrem-sales/pages__sales_order_mass_deallocation__message": "The deallocation request was submitted.", "@sage/xtrem-sales/pages__sales_order_mass_deallocation__title": "Deallocation request submitted", "@sage/xtrem-sales/pages__sales_order_save_on_hold_action_dialog_title": "Customer on hold", "@sage/xtrem-sales/pages__sales_order_shipping_mass_process____title": "Mass shipment creation", "@sage/xtrem-sales/pages__sales_order_shipping_mass_process__available": "Available", "@sage/xtrem-sales/pages__sales_order_shipping_mass_process__cancel": "Cancel", "@sage/xtrem-sales/pages__sales_order_shipping_mass_process__cancelDetailedButton____title": "Hide advanced selection", "@sage/xtrem-sales/pages__sales_order_shipping_mass_process__chooseOrders____title": "Show advanced selection", "@sage/xtrem-sales/pages__sales_order_shipping_mass_process__continue": "Continue", "@sage/xtrem-sales/pages__sales_order_shipping_mass_process__createSalesShipmentsFromOrderLines____title": "Create", "@sage/xtrem-sales/pages__sales_order_shipping_mass_process__creation__success": "Sales shipments created: {{num}}.", "@sage/xtrem-sales/pages__sales_order_shipping_mass_process__criteriaBlock____title": "Selection criteria", "@sage/xtrem-sales/pages__sales_order_shipping_mass_process__customerSatisfaction____title": "Customer satisfaction", "@sage/xtrem-sales/pages__sales_order_shipping_mass_process__deliveryMode____lookupDialogTitle": "Select delivery mode", "@sage/xtrem-sales/pages__sales_order_shipping_mass_process__deliveryMode____title": "Delivery mode", "@sage/xtrem-sales/pages__sales_order_shipping_mass_process__detailPanelDeliveryDeliveryBlock____title": "Delivery", "@sage/xtrem-sales/pages__sales_order_shipping_mass_process__detailPanelDeliverySection____title": "Delivery", "@sage/xtrem-sales/pages__sales_order_shipping_mass_process__detailPanelDeliveryShippingSectionShippingBlock____title": "Shipping", "@sage/xtrem-sales/pages__sales_order_shipping_mass_process__detailPanelHeaderBlock____title": "", "@sage/xtrem-sales/pages__sales_order_shipping_mass_process__detailPanelHeaderSection____title": "Sales order line detail panel", "@sage/xtrem-sales/pages__sales_order_shipping_mass_process__detailPanelQuantitySection____title": "Quantities", "@sage/xtrem-sales/pages__sales_order_shipping_mass_process__detailPanelQuantityStockUnitBlock____title": "Quantities", "@sage/xtrem-sales/pages__sales_order_shipping_mass_process__detailPanelSalesOrderLine____cardDefinition__image__title": "Item image", "@sage/xtrem-sales/pages__sales_order_shipping_mass_process__detailPanelSalesOrderLine____cardDefinition__title__title": "<PERSON><PERSON>", "@sage/xtrem-sales/pages__sales_order_shipping_mass_process__detailPanelSalesOrderLine____title": "Sales order line", "@sage/xtrem-sales/pages__sales_order_shipping_mass_process__fromOrderNumber____columns__title__date": "Order date", "@sage/xtrem-sales/pages__sales_order_shipping_mass_process__fromOrderNumber____columns__title__number": "Order number", "@sage/xtrem-sales/pages__sales_order_shipping_mass_process__fromOrderNumber____columns__title__soldToCustomer": "Sold-to customer", "@sage/xtrem-sales/pages__sales_order_shipping_mass_process__fromOrderNumber____lookupDialogTitle": "Select from order number", "@sage/xtrem-sales/pages__sales_order_shipping_mass_process__fromOrderNumber____title": "From order number", "@sage/xtrem-sales/pages__sales_order_shipping_mass_process__fromSoldToCustomer____columns__title__businessEntity__country__name": "Country", "@sage/xtrem-sales/pages__sales_order_shipping_mass_process__fromSoldToCustomer____columns__title__businessEntity__id": "ID", "@sage/xtrem-sales/pages__sales_order_shipping_mass_process__fromSoldToCustomer____columns__title__businessEntity__name": "Name", "@sage/xtrem-sales/pages__sales_order_shipping_mass_process__fromSoldToCustomer____columns__title__businessEntity__taxIdNumber": "Tax ID", "@sage/xtrem-sales/pages__sales_order_shipping_mass_process__fromSoldToCustomer____lookupDialogTitle": "Select from sold-to customer", "@sage/xtrem-sales/pages__sales_order_shipping_mass_process__fromSoldToCustomer____title": "From sold-to customer", "@sage/xtrem-sales/pages__sales_order_shipping_mass_process__incoterm____lookupDialogTitle": "Select incoterms", "@sage/xtrem-sales/pages__sales_order_shipping_mass_process__incoterm____title": "Incoterms® rule", "@sage/xtrem-sales/pages__sales_order_shipping_mass_process__mainBlock____title": "Advanced selection", "@sage/xtrem-sales/pages__sales_order_shipping_mass_process__mainSection____title": "General", "@sage/xtrem-sales/pages__sales_order_shipping_mass_process__no_shipment_meet_filtering_criteria": "No results found.", "@sage/xtrem-sales/pages__sales_order_shipping_mass_process__not_applicable": "Not applicable", "@sage/xtrem-sales/pages__sales_order_shipping_mass_process__not_available": "Not available", "@sage/xtrem-sales/pages__sales_order_shipping_mass_process__quantityConfirmedInStockUnit_error": "The confirmed quantity in stock unit ({{value}}) cannot be greater than the quantity to ship in stock unit ({{quantityInStockUnit}}).", "@sage/xtrem-sales/pages__sales_order_shipping_mass_process__results____columns__nestedFields__shipToAddress__title": "ID", "@sage/xtrem-sales/pages__sales_order_shipping_mass_process__results____columns__nestedFields__shipToAddress__title__2": "Name", "@sage/xtrem-sales/pages__sales_order_shipping_mass_process__results____columns__nestedFields__shipToAddress__title__3": "Phone number", "@sage/xtrem-sales/pages__sales_order_shipping_mass_process__results____columns__nestedFields__shipToAddress__title__4": "Bill-to address", "@sage/xtrem-sales/pages__sales_order_shipping_mass_process__results____columns__prefix__deliveryLeadTime": "Number of days: ", "@sage/xtrem-sales/pages__sales_order_shipping_mass_process__results____columns__title__amountExcludingTax": "Amount excl. tax", "@sage/xtrem-sales/pages__sales_order_shipping_mass_process__results____columns__title__amountIncludingTax": "Amount incl. tax", "@sage/xtrem-sales/pages__sales_order_shipping_mass_process__results____columns__title__availableQuantityInStockUnit": "Available stock", "@sage/xtrem-sales/pages__sales_order_shipping_mass_process__results____columns__title__deliveryLeadTime": "Delivery lead time", "@sage/xtrem-sales/pages__sales_order_shipping_mass_process__results____columns__title__deliveryMode__name": "Delivery mode", "@sage/xtrem-sales/pages__sales_order_shipping_mass_process__results____columns__title__doNotShipAfterDate": "Do-not-ship-after date", "@sage/xtrem-sales/pages__sales_order_shipping_mass_process__results____columns__title__doNotShipBeforeDate": "Do-not-ship-before date", "@sage/xtrem-sales/pages__sales_order_shipping_mass_process__results____columns__title__expectedDeliveryDate": "Expected delivery date", "@sage/xtrem-sales/pages__sales_order_shipping_mass_process__results____columns__title__incoterm__name": "Incoterms® rule", "@sage/xtrem-sales/pages__sales_order_shipping_mass_process__results____columns__title__item__id": "Item ID", "@sage/xtrem-sales/pages__sales_order_shipping_mass_process__results____columns__title__item__image": "Image", "@sage/xtrem-sales/pages__sales_order_shipping_mass_process__results____columns__title__item__name": "<PERSON><PERSON>", "@sage/xtrem-sales/pages__sales_order_shipping_mass_process__results____columns__title__itemDescription": "Item description", "@sage/xtrem-sales/pages__sales_order_shipping_mass_process__results____columns__title__number": "Order number", "@sage/xtrem-sales/pages__sales_order_shipping_mass_process__results____columns__title__quantityAllocated": "Allocated quantity", "@sage/xtrem-sales/pages__sales_order_shipping_mass_process__results____columns__title__quantityConfirmedInStockUnit": "Confirmed qty.", "@sage/xtrem-sales/pages__sales_order_shipping_mass_process__results____columns__title__quantityInStockUnit": "Qty. to ship", "@sage/xtrem-sales/pages__sales_order_shipping_mass_process__results____columns__title__remainingQuantityInStockUnit": "Remaining stock", "@sage/xtrem-sales/pages__sales_order_shipping_mass_process__results____columns__title__requestedDeliveryDate": "Requested delivery date", "@sage/xtrem-sales/pages__sales_order_shipping_mass_process__results____columns__title__shippingDate": "Shipping date", "@sage/xtrem-sales/pages__sales_order_shipping_mass_process__results____columns__title__soldToCustomerName": "Sold-to customer", "@sage/xtrem-sales/pages__sales_order_shipping_mass_process__results____columns__title__stockAvailability": "Stock availability", "@sage/xtrem-sales/pages__sales_order_shipping_mass_process__results____columns__title__stockShortageInStockUnit": "Shortage", "@sage/xtrem-sales/pages__sales_order_shipping_mass_process__results____columns__title__totalQuantityOnDemand": "Total demand", "@sage/xtrem-sales/pages__sales_order_shipping_mass_process__results____title": "Results", "@sage/xtrem-sales/pages__sales_order_shipping_mass_process__revenue____title": "Shipment amount including tax", "@sage/xtrem-sales/pages__sales_order_shipping_mass_process__salesCompany____lookupDialogTitle": "Select company", "@sage/xtrem-sales/pages__sales_order_shipping_mass_process__salesCompany____placeholder": "Select...", "@sage/xtrem-sales/pages__sales_order_shipping_mass_process__salesCompany____title": "Company", "@sage/xtrem-sales/pages__sales_order_shipping_mass_process__salesOrderLineShipToAddressDetailPanel____columns__title__concatenatedAddress": "Bill-to address", "@sage/xtrem-sales/pages__sales_order_shipping_mass_process__salesOrderLineShipToAddressDetailPanel____columns__title__locationPhoneNumber": "Phone number", "@sage/xtrem-sales/pages__sales_order_shipping_mass_process__salesOrderLineShipToAddressDetailPanel____title": "Ship-to address", "@sage/xtrem-sales/pages__sales_order_shipping_mass_process__selectAllCheckbox____title": "Select all", "@sage/xtrem-sales/pages__sales_order_shipping_mass_process__shippingUntilDate____title": "Maximum shipping date", "@sage/xtrem-sales/pages__sales_order_shipping_mass_process__shortage": "Shortage", "@sage/xtrem-sales/pages__sales_order_shipping_mass_process__stockSite____columns__title__legalCompany__name": "Company", "@sage/xtrem-sales/pages__sales_order_shipping_mass_process__stockSite____lookupDialogTitle": "Select stock site", "@sage/xtrem-sales/pages__sales_order_shipping_mass_process__stockSite____placeholder": "Select...", "@sage/xtrem-sales/pages__sales_order_shipping_mass_process__stockSite____title": "Stock site", "@sage/xtrem-sales/pages__sales_order_shipping_mass_process__toOrderNumber____columns__title__date": "Order date", "@sage/xtrem-sales/pages__sales_order_shipping_mass_process__toOrderNumber____columns__title__number": "Order number", "@sage/xtrem-sales/pages__sales_order_shipping_mass_process__toOrderNumber____columns__title__soldToCustomer": "Sold-to customer", "@sage/xtrem-sales/pages__sales_order_shipping_mass_process__toOrderNumber____lookupDialogTitle": "Select to order number", "@sage/xtrem-sales/pages__sales_order_shipping_mass_process__toOrderNumber____title": "To order number", "@sage/xtrem-sales/pages__sales_order_shipping_mass_process__toSoldToCustomer____columns__title__businessEntity__id": "ID", "@sage/xtrem-sales/pages__sales_order_shipping_mass_process__toSoldToCustomer____columns__title__businessEntity__name": "Name", "@sage/xtrem-sales/pages__sales_order_shipping_mass_process__toSoldToCustomer____columns__title__businessEntity__taxIdNumber": "Tax ID", "@sage/xtrem-sales/pages__sales_order_shipping_mass_process__toSoldToCustomer____lookupDialogTitle": "Select to sold-to customer", "@sage/xtrem-sales/pages__sales_order_shipping_mass_process__toSoldToCustomer____title": "To sold-to customer", "@sage/xtrem-sales/pages__sales_order_table_panel____title": "Add lines from orders", "@sage/xtrem-sales/pages__sales_order_table_panel__cancel____title": "Cancel", "@sage/xtrem-sales/pages__sales_order_table_panel__confirm____title": "Add", "@sage/xtrem-sales/pages__sales_order_table_panel__confirm_lower_quantity": "You are about to create one or more order shipment lines with a quantity less than the ordered quantity.", "@sage/xtrem-sales/pages__sales_order_table_panel__confirm_order_select_confirm_title": "Confirm shipment quantity", "@sage/xtrem-sales/pages__sales_order_table_panel__error_greater_quantity": "You cannot ship a quantity more than the order you select.", "@sage/xtrem-sales/pages__sales_order_table_panel__error_zero_or_negative": "You cannot enter a quantity less than or equal to zero.", "@sage/xtrem-sales/pages__sales_order_table_panel__salesOrderLines____columns__columns__item__name__title": "Category", "@sage/xtrem-sales/pages__sales_order_table_panel__salesOrderLines____columns__columns__priceReason__name__title": "Priority", "@sage/xtrem-sales/pages__sales_order_table_panel__salesOrderLines____columns__title__document__number": "Number", "@sage/xtrem-sales/pages__sales_order_table_panel__salesOrderLines____columns__title__item__id": "Item ID", "@sage/xtrem-sales/pages__sales_order_table_panel__salesOrderLines____columns__title__item__image": "Image", "@sage/xtrem-sales/pages__sales_order_table_panel__salesOrderLines____columns__title__item__name": "<PERSON><PERSON>", "@sage/xtrem-sales/pages__sales_order_table_panel__salesOrderLines____columns__title__itemDescription": "Item description", "@sage/xtrem-sales/pages__sales_order_table_panel__salesOrderLines____columns__title__itemDescription__2": "Item description", "@sage/xtrem-sales/pages__sales_order_table_panel__salesOrderLines____columns__title__priceOrigin": "Price origin", "@sage/xtrem-sales/pages__sales_order_table_panel__salesOrderLines____columns__title__priceReason__name": "Price reason", "@sage/xtrem-sales/pages__sales_order_table_panel__salesOrderLines____columns__title__quantity": "Quantity to ship", "@sage/xtrem-sales/pages__sales_order_table_panel__salesOrderLines____columns__title__shippingDate": "Shipping date", "@sage/xtrem-sales/pages__sales_order_table_panel__salesOrderLines____columns__title__shippingStatus": "Status", "@sage/xtrem-sales/pages__sales_order_table_panel__salesOrderLines____mobileCard__image__title": "Image", "@sage/xtrem-sales/pages__sales_order_table_panel__salesOrderLines____mobileCard__line2__title": "Shipping date", "@sage/xtrem-sales/pages__sales_order_table_panel__salesOrderLines____mobileCard__line2Right__title": "Quantity In Sales Unit", "@sage/xtrem-sales/pages__sales_order_table_panel__salesOrderLines____mobileCard__title__title": "Number", "@sage/xtrem-sales/pages__sales_order_table_panel__salesOrderLines____mobileCard__titleRight__title": "<PERSON><PERSON>", "@sage/xtrem-sales/pages__sales_order_table_panel__salesOrderLines____title": "Sales order lines", "@sage/xtrem-sales/pages__sales_quote__email_sent": "Sales quote sent to: ", "@sage/xtrem-sales/pages__sales_quote__printed": "The sales quote was printed", "@sage/xtrem-sales/pages__sales_quote__send_sales_order_dialog_content": "You are about to send the sales quote email.", "@sage/xtrem-sales/pages__sales_quote__send_sales_order_dialog_title": "Sales quote email send confirmation", "@sage/xtrem-sales/pages__sales_return_receipt____navigationPanel__dropdownActions__title": "Post stock", "@sage/xtrem-sales/pages__sales_return_receipt____navigationPanel__dropdownActions__title__2": "Set dimensions", "@sage/xtrem-sales/pages__sales_return_receipt____navigationPanel__dropdownActions__title__3": "Delete", "@sage/xtrem-sales/pages__sales_return_receipt____navigationPanel__listItem__line_4__title": "Site", "@sage/xtrem-sales/pages__sales_return_receipt____navigationPanel__listItem__line2__title": "Ship-to customer", "@sage/xtrem-sales/pages__sales_return_receipt____navigationPanel__listItem__line2Right__title": "Return receipt date", "@sage/xtrem-sales/pages__sales_return_receipt____navigationPanel__listItem__shipToCustomerId__title": "Ship-to customer ID", "@sage/xtrem-sales/pages__sales_return_receipt____navigationPanel__listItem__title__title": "Number", "@sage/xtrem-sales/pages__sales_return_receipt____navigationPanel__listItem__titleRight__title": "Status", "@sage/xtrem-sales/pages__sales_return_receipt____navigationPanel__optionsMenu__title": "All open statuses", "@sage/xtrem-sales/pages__sales_return_receipt____navigationPanel__optionsMenu__title__2": "All statuses", "@sage/xtrem-sales/pages__sales_return_receipt____navigationPanel__optionsMenu__title__3": "Draft", "@sage/xtrem-sales/pages__sales_return_receipt____navigationPanel__optionsMenu__title__4": "Posting in progress", "@sage/xtrem-sales/pages__sales_return_receipt____navigationPanel__optionsMenu__title__5": "Closed", "@sage/xtrem-sales/pages__sales_return_receipt____navigationPanel__optionsMenu__title__6": "Error", "@sage/xtrem-sales/pages__sales_return_receipt____objectTypePlural": "Sales return receipts", "@sage/xtrem-sales/pages__sales_return_receipt____objectTypeSingular": "Sales return receipt", "@sage/xtrem-sales/pages__sales_return_receipt____title": "Sales return receipt", "@sage/xtrem-sales/pages__sales_return_receipt__actualOnRequestQuantity____title": "Actual quantity", "@sage/xtrem-sales/pages__sales_return_receipt__apply_dimensions_success": "Dimensions applied.", "@sage/xtrem-sales/pages__sales_return_receipt__baseDocumentLine____title": "Return request lines", "@sage/xtrem-sales/pages__sales_return_receipt__customSave____title": "Save", "@sage/xtrem-sales/pages__sales_return_receipt__date____title": "Return receipt date", "@sage/xtrem-sales/pages__sales_return_receipt__defaultDimension____title": "Set dimensions", "@sage/xtrem-sales/pages__sales_return_receipt__displayStatus____title": "Display status", "@sage/xtrem-sales/pages__sales_return_receipt__goToSysNotificationPage____title": "Retry", "@sage/xtrem-sales/pages__sales_return_receipt__headerSection____title": "Header section", "@sage/xtrem-sales/pages__sales_return_receipt__informationBlock____title": "Information", "@sage/xtrem-sales/pages__sales_return_receipt__informationSection____title": "Information", "@sage/xtrem-sales/pages__sales_return_receipt__internalNote____helperText": "Notes display on internal documents.", "@sage/xtrem-sales/pages__sales_return_receipt__internalNote____title": "Internal notes", "@sage/xtrem-sales/pages__sales_return_receipt__internalNoteLine____helperText": "Notes display on internal documents.", "@sage/xtrem-sales/pages__sales_return_receipt__internalNoteLine____title": "Internal line notes", "@sage/xtrem-sales/pages__sales_return_receipt__itemsSection____title": "Lines", "@sage/xtrem-sales/pages__sales_return_receipt__line_delete_action_dialog_content": "You are about to delete this sales return receipt line. This action cannot be undone.", "@sage/xtrem-sales/pages__sales_return_receipt__line_delete_action_dialog_title": "Confirm delete", "@sage/xtrem-sales/pages__sales_return_receipt__lines____columns__columns__item__name__title": "Category", "@sage/xtrem-sales/pages__sales_return_receipt__lines____columns__columns__location__name__columns__title__locationCategory": "Category", "@sage/xtrem-sales/pages__sales_return_receipt__lines____columns__columns__location__name__title": "Type", "@sage/xtrem-sales/pages__sales_return_receipt__lines____columns__columns__lot__id__title": "Expiration date", "@sage/xtrem-sales/pages__sales_return_receipt__lines____columns__columns__stockUnit__name__title": "Symbol", "@sage/xtrem-sales/pages__sales_return_receipt__lines____columns__columns__unit__name__title": "Symbol", "@sage/xtrem-sales/pages__sales_return_receipt__lines____columns__lookupDialogTitle__item__name": "Select item", "@sage/xtrem-sales/pages__sales_return_receipt__lines____columns__lookupDialogTitle__location__name": "Select location", "@sage/xtrem-sales/pages__sales_return_receipt__lines____columns__lookupDialogTitle__lot__id": "Select lot", "@sage/xtrem-sales/pages__sales_return_receipt__lines____columns__lookupDialogTitle__stockStatus": "Select status", "@sage/xtrem-sales/pages__sales_return_receipt__lines____columns__lookupDialogTitle__stockUnit__name": "Select unit", "@sage/xtrem-sales/pages__sales_return_receipt__lines____columns__lookupDialogTitle__unit__name": "Select unit", "@sage/xtrem-sales/pages__sales_return_receipt__lines____columns__title__item__id": "Item ID", "@sage/xtrem-sales/pages__sales_return_receipt__lines____columns__title__item__image": "Image", "@sage/xtrem-sales/pages__sales_return_receipt__lines____columns__title__item__name": "<PERSON><PERSON>", "@sage/xtrem-sales/pages__sales_return_receipt__lines____columns__title__itemDescription": "Item description", "@sage/xtrem-sales/pages__sales_return_receipt__lines____columns__title__location__name": "Stock location", "@sage/xtrem-sales/pages__sales_return_receipt__lines____columns__title__lot__id": "Lot", "@sage/xtrem-sales/pages__sales_return_receipt__lines____columns__title__origin": "Origin", "@sage/xtrem-sales/pages__sales_return_receipt__lines____columns__title__quantity": "Quantity in sales unit", "@sage/xtrem-sales/pages__sales_return_receipt__lines____columns__title__quantityInStockUnit": "Quantity in stock unit", "@sage/xtrem-sales/pages__sales_return_receipt__lines____columns__title__stockDetailStatus": "Stock detail status", "@sage/xtrem-sales/pages__sales_return_receipt__lines____columns__title__stockTransactionStatus": "Stock status", "@sage/xtrem-sales/pages__sales_return_receipt__lines____columns__title__stockUnit__name": "Stock unit", "@sage/xtrem-sales/pages__sales_return_receipt__lines____columns__title__unit__name": "Sales unit", "@sage/xtrem-sales/pages__sales_return_receipt__lines____columns__title__unitToStockUnitConversionFactor": "Stock unit conversion factor", "@sage/xtrem-sales/pages__sales_return_receipt__lines____dropdownActions__title": "Stock details", "@sage/xtrem-sales/pages__sales_return_receipt__lines____dropdownActions__title__2": "Dimensions", "@sage/xtrem-sales/pages__sales_return_receipt__lines____dropdownActions__title__3": "Delete", "@sage/xtrem-sales/pages__sales_return_receipt__lines____inlineActions__title": "Open line panel", "@sage/xtrem-sales/pages__sales_return_receipt__lines____mobileCard__line2__title": "Description", "@sage/xtrem-sales/pages__sales_return_receipt__lines____mobileCard__title__title": "Product", "@sage/xtrem-sales/pages__sales_return_receipt__lines____sidebar__headerDropdownActions__title": "Stock details", "@sage/xtrem-sales/pages__sales_return_receipt__lines____sidebar__headerDropdownActions__title__2": "Dimensions", "@sage/xtrem-sales/pages__sales_return_receipt__lines____sidebar__headerDropdownActions__title__3": "Delete", "@sage/xtrem-sales/pages__sales_return_receipt__lines____title": "Lines", "@sage/xtrem-sales/pages__sales_return_receipt__newOnRequestQuantity____title": "New quantity", "@sage/xtrem-sales/pages__sales_return_receipt__noteBlock____title": "Notes", "@sage/xtrem-sales/pages__sales_return_receipt__notesSection____title": "Notes", "@sage/xtrem-sales/pages__sales_return_receipt__number____title": "Number", "@sage/xtrem-sales/pages__sales_return_receipt__on_receipt_quantity_changed_error_title": "Error on quantity", "@sage/xtrem-sales/pages__sales_return_receipt__on_return_receipt_quantity_changed_confirmation": "Update the quantity from the related return receipt line?", "@sage/xtrem-sales/pages__sales_return_receipt__on_return_receipt_quantity_changed_confirmation_title": "Update return receipt line quantity confirmation?", "@sage/xtrem-sales/pages__sales_return_receipt__onRequestQuantityChangeBlock____title": "", "@sage/xtrem-sales/pages__sales_return_receipt__onRequestQuantityChangeSection____title": "On request quantity change", "@sage/xtrem-sales/pages__sales_return_receipt__podReturnRequestLines____columns__title___id": "Sales return request", "@sage/xtrem-sales/pages__sales_return_receipt__podReturnRequestLines____title": "Return request lines", "@sage/xtrem-sales/pages__sales_return_receipt__post____title": "Post stock", "@sage/xtrem-sales/pages__sales_return_receipt__post_from_main_row": "Receipt posted", "@sage/xtrem-sales/pages__sales_return_receipt__postingDetails____columns__title__financeIntegrationAppRecordId": "Accounting integration reference", "@sage/xtrem-sales/pages__sales_return_receipt__postingDetails____columns__title__postingStatus": "Status", "@sage/xtrem-sales/pages__sales_return_receipt__postingDetails____columns__title__targetDocumentNumber": "Document number", "@sage/xtrem-sales/pages__sales_return_receipt__postingDetails____columns__title__targetDocumentType": "Document type", "@sage/xtrem-sales/pages__sales_return_receipt__postingDetails____title": "Posting", "@sage/xtrem-sales/pages__sales_return_receipt__postingMessageBlock____title": "Error details", "@sage/xtrem-sales/pages__sales_return_receipt__postingSection____title": "Posting", "@sage/xtrem-sales/pages__sales_return_receipt__remainingOnRequestQuantity____title": "Remaining quantity", "@sage/xtrem-sales/pages__sales_return_receipt__repost____title": "Repost", "@sage/xtrem-sales/pages__sales_return_receipt__repost_errors": "Errors occurred while reposting:", "@sage/xtrem-sales/pages__sales_return_receipt__return_date__cannot__be__future": "The return date cannot be later than today.", "@sage/xtrem-sales/pages__sales_return_receipt__returnRequestLineLink____title": "Origin document number", "@sage/xtrem-sales/pages__sales_return_receipt__salesLinkedDocuments____columns__title___id": "Origin document number", "@sage/xtrem-sales/pages__sales_return_receipt__salesLinkedDocuments____columns__title__displayStatus": "Status", "@sage/xtrem-sales/pages__sales_return_receipt__salesLinkedDocuments____columns__title__documentType": "Document type", "@sage/xtrem-sales/pages__sales_return_receipt__salesLinkedDocuments____title": "Return request", "@sage/xtrem-sales/pages__sales_return_receipt__salesReturnReceiptLineCount____title": "Number of items", "@sage/xtrem-sales/pages__sales_return_receipt__salesReturnRequestLineToSalesReturnReceiptLines____columns__columns__document___id__columns__title__symbol": "Symbol", "@sage/xtrem-sales/pages__sales_return_receipt__salesReturnRequestLineToSalesReturnReceiptLines____columns__columns__document___id__title": "Sales unit", "@sage/xtrem-sales/pages__sales_return_receipt__salesReturnRequestLineToSalesReturnReceiptLines____columns__columns__linkedDocument___id__columns__title__category__name": "Category", "@sage/xtrem-sales/pages__sales_return_receipt__salesReturnRequestLineToSalesReturnReceiptLines____columns__columns__linkedDocument___id__title": "<PERSON><PERSON>", "@sage/xtrem-sales/pages__sales_return_receipt__salesReturnRequestLineToSalesReturnReceiptLines____columns__title__document___id": "Sales return receipt line", "@sage/xtrem-sales/pages__sales_return_receipt__salesReturnRequestLineToSalesReturnReceiptLines____columns__title__document___id__2": "Unit", "@sage/xtrem-sales/pages__sales_return_receipt__salesReturnRequestLineToSalesReturnReceiptLines____columns__title__linkedDocument___id": "Sales return request line", "@sage/xtrem-sales/pages__sales_return_receipt__salesReturnRequestLineToSalesReturnReceiptLines____columns__title__quantity": "Quantity to return", "@sage/xtrem-sales/pages__sales_return_receipt__salesReturnRequestLineToSalesReturnReceiptLines____dropdownActions__title": "Quantities", "@sage/xtrem-sales/pages__sales_return_receipt__salesReturnRequestLineToSalesReturnReceiptLines____mobileCard__image__title": "Image", "@sage/xtrem-sales/pages__sales_return_receipt__salesReturnRequestLineToSalesReturnReceiptLines____mobileCard__line2Right__title": "Quantity to return", "@sage/xtrem-sales/pages__sales_return_receipt__salesReturnRequestLineToSalesReturnReceiptLines____mobileCard__title__title": "Sales return request", "@sage/xtrem-sales/pages__sales_return_receipt__salesReturnRequestLineToSalesReturnReceiptLines____mobileCard__titleRight__title": "<PERSON><PERSON>", "@sage/xtrem-sales/pages__sales_return_receipt__salesReturnRequestLineToSalesReturnReceiptLines____title": "Sales return request lines - ordering in progress", "@sage/xtrem-sales/pages__sales_return_receipt__selectFromSalesReturnRequestsLookup____title": "Add lines from return requests", "@sage/xtrem-sales/pages__sales_return_receipt__shipToAddress____columns__columns__country__title": "ISO 3166-1 alpha-2 code", "@sage/xtrem-sales/pages__sales_return_receipt__shipToAddress____columns__title__addressLine1": "Address line 1", "@sage/xtrem-sales/pages__sales_return_receipt__shipToAddress____columns__title__addressLine2": "Address line 2", "@sage/xtrem-sales/pages__sales_return_receipt__shipToAddress____columns__title__city": "City", "@sage/xtrem-sales/pages__sales_return_receipt__shipToAddress____columns__title__concatenatedAddress": "Bill-to address", "@sage/xtrem-sales/pages__sales_return_receipt__shipToAddress____columns__title__locationPhoneNumber": "Phone number", "@sage/xtrem-sales/pages__sales_return_receipt__shipToAddress____columns__title__name": "Name", "@sage/xtrem-sales/pages__sales_return_receipt__shipToAddress____dropdownActions__title": "Replace", "@sage/xtrem-sales/pages__sales_return_receipt__shipToAddress____dropdownActions__title__2": "Edit", "@sage/xtrem-sales/pages__sales_return_receipt__shipToAddress____dropdownActions__title__3": "Read-only", "@sage/xtrem-sales/pages__sales_return_receipt__shipToAddress____title": "Ship-to address", "@sage/xtrem-sales/pages__sales_return_receipt__shipToCustomer____columns__title__businessEntity__country__name": "Country", "@sage/xtrem-sales/pages__sales_return_receipt__shipToCustomer____columns__title__businessEntity__id": "ID", "@sage/xtrem-sales/pages__sales_return_receipt__shipToCustomer____columns__title__businessEntity__name": "Name", "@sage/xtrem-sales/pages__sales_return_receipt__shipToCustomer____columns__title__businessEntity__taxIdNumber": "Tax ID", "@sage/xtrem-sales/pages__sales_return_receipt__shipToCustomer____lookupDialogTitle": "Select ship-to customer", "@sage/xtrem-sales/pages__sales_return_receipt__shipToCustomer____title": "Ship-to customer", "@sage/xtrem-sales/pages__sales_return_receipt__shipToCustomerAddress____columns__columns__country__title": "ISO 3166-1 alpha-2 code", "@sage/xtrem-sales/pages__sales_return_receipt__shipToCustomerAddress____columns__columns__deliveryDetail___id__columns__title__legalCompany__name": "Company", "@sage/xtrem-sales/pages__sales_return_receipt__shipToCustomerAddress____columns__columns__deliveryDetail___id__postfix": "day(s)", "@sage/xtrem-sales/pages__sales_return_receipt__shipToCustomerAddress____columns__columns__deliveryDetail___id__title": "Incoterms® rule", "@sage/xtrem-sales/pages__sales_return_receipt__shipToCustomerAddress____columns__columns__deliveryDetail___id__title__2": "Delivery mode", "@sage/xtrem-sales/pages__sales_return_receipt__shipToCustomerAddress____columns__columns__deliveryDetail___id__title__3": "Delivery lead time", "@sage/xtrem-sales/pages__sales_return_receipt__shipToCustomerAddress____columns__columns__deliveryDetail___id__title__4": "Stock site", "@sage/xtrem-sales/pages__sales_return_receipt__shipToCustomerAddress____columns__title__addressLine1": "Address line 1", "@sage/xtrem-sales/pages__sales_return_receipt__shipToCustomerAddress____columns__title__addressLine2": "Address line 2", "@sage/xtrem-sales/pages__sales_return_receipt__shipToCustomerAddress____columns__title__city": "City", "@sage/xtrem-sales/pages__sales_return_receipt__shipToCustomerAddress____columns__title__deliveryDetail___id": "Ship-to address", "@sage/xtrem-sales/pages__sales_return_receipt__shipToCustomerAddress____columns__title__deliveryDetail__isPrimary": "Primary ship-to address", "@sage/xtrem-sales/pages__sales_return_receipt__shipToCustomerAddress____columns__title__isActive": "Active", "@sage/xtrem-sales/pages__sales_return_receipt__shipToCustomerAddress____columns__title__locationPhoneNumber": "Phone number", "@sage/xtrem-sales/pages__sales_return_receipt__shipToCustomerAddress____columns__title__postcode": "ZIP code", "@sage/xtrem-sales/pages__sales_return_receipt__shipToCustomerAddress____columns__title__region": "Region", "@sage/xtrem-sales/pages__sales_return_receipt__shipToCustomerAddress____lookupDialogTitle": "Select ship-to address", "@sage/xtrem-sales/pages__sales_return_receipt__shipToCustomerAddress____title": "Ship-to address", "@sage/xtrem-sales/pages__sales_return_receipt__site____columns__title__legalCompany__name": "Company", "@sage/xtrem-sales/pages__sales_return_receipt__site____lookupDialogTitle": "Select site", "@sage/xtrem-sales/pages__sales_return_receipt__site____title": "Site", "@sage/xtrem-sales/pages__sales_return_receipt__step_sequence_creation": "Create", "@sage/xtrem-sales/pages__sales_return_receipt__step_sequence_post": "Post", "@sage/xtrem-sales/pages__sales_return_receipt__stockDetailStatus____title": "Stock details status", "@sage/xtrem-sales/pages__sales_return_receipt__stockTransactionStatus____title": "Stock status", "@sage/xtrem-sales/pages__sales_return_receipt__unit____columns__title__symbol": "Symbol", "@sage/xtrem-sales/pages__sales_return_receipt__unit____lookupDialogTitle": "Select unit", "@sage/xtrem-sales/pages__sales_return_receipt__unit____title": "Unit", "@sage/xtrem-sales/pages__sales_return_request____navigationPanel__dropdownActions__title": "Confirm", "@sage/xtrem-sales/pages__sales_return_request____navigationPanel__dropdownActions__title__2": "Approve", "@sage/xtrem-sales/pages__sales_return_request____navigationPanel__dropdownActions__title__3": "Reject", "@sage/xtrem-sales/pages__sales_return_request____navigationPanel__dropdownActions__title__4": "Submit for approval", "@sage/xtrem-sales/pages__sales_return_request____navigationPanel__dropdownActions__title__5": "Create credit memo", "@sage/xtrem-sales/pages__sales_return_request____navigationPanel__dropdownActions__title__6": "Close request", "@sage/xtrem-sales/pages__sales_return_request____navigationPanel__dropdownActions__title__7": "Open request", "@sage/xtrem-sales/pages__sales_return_request____navigationPanel__dropdownActions__title__8": "Set dimensions", "@sage/xtrem-sales/pages__sales_return_request____navigationPanel__dropdownActions__title__9": "Delete", "@sage/xtrem-sales/pages__sales_return_request____navigationPanel__listItem__billToCustomerId__title": "Bill-to customer ID", "@sage/xtrem-sales/pages__sales_return_request____navigationPanel__listItem__line_4__title": "Site", "@sage/xtrem-sales/pages__sales_return_request____navigationPanel__listItem__line_5__title": "Stock site", "@sage/xtrem-sales/pages__sales_return_request____navigationPanel__listItem__line10__title": "Ship-to customer", "@sage/xtrem-sales/pages__sales_return_request____navigationPanel__listItem__line12__title": "Bill-to customer", "@sage/xtrem-sales/pages__sales_return_request____navigationPanel__listItem__line13__title": "Credit status", "@sage/xtrem-sales/pages__sales_return_request____navigationPanel__listItem__line2__title": "Sold-to customer", "@sage/xtrem-sales/pages__sales_return_request____navigationPanel__listItem__line2Right__title": "Return request date", "@sage/xtrem-sales/pages__sales_return_request____navigationPanel__listItem__line6__title": "Requester", "@sage/xtrem-sales/pages__sales_return_request____navigationPanel__listItem__line7__title": "Return type", "@sage/xtrem-sales/pages__sales_return_request____navigationPanel__listItem__line9__title": "Delivery mode", "@sage/xtrem-sales/pages__sales_return_request____navigationPanel__listItem__shipToCustomerId__title": "Ship-to customer ID", "@sage/xtrem-sales/pages__sales_return_request____navigationPanel__listItem__soldToCustomerId__title": "Sold-to customer ID", "@sage/xtrem-sales/pages__sales_return_request____navigationPanel__listItem__title__title": "Number", "@sage/xtrem-sales/pages__sales_return_request____navigationPanel__listItem__titleRight__title": "Status", "@sage/xtrem-sales/pages__sales_return_request____navigationPanel__optionsMenu__title": "All open statuses", "@sage/xtrem-sales/pages__sales_return_request____navigationPanel__optionsMenu__title__10": "Confirmed", "@sage/xtrem-sales/pages__sales_return_request____navigationPanel__optionsMenu__title__2": "All statuses", "@sage/xtrem-sales/pages__sales_return_request____navigationPanel__optionsMenu__title__3": "Draft", "@sage/xtrem-sales/pages__sales_return_request____navigationPanel__optionsMenu__title__4": "Pending approval", "@sage/xtrem-sales/pages__sales_return_request____navigationPanel__optionsMenu__title__5": "Approved", "@sage/xtrem-sales/pages__sales_return_request____navigationPanel__optionsMenu__title__6": "Closed", "@sage/xtrem-sales/pages__sales_return_request____navigationPanel__optionsMenu__title__7": "Rejected", "@sage/xtrem-sales/pages__sales_return_request____navigationPanel__optionsMenu__title__8": "Partially received", "@sage/xtrem-sales/pages__sales_return_request____navigationPanel__optionsMenu__title__9": "Received", "@sage/xtrem-sales/pages__sales_return_request____objectTypePlural": "Sales return requests", "@sage/xtrem-sales/pages__sales_return_request____objectTypeSingular": "Sales return request", "@sage/xtrem-sales/pages__sales_return_request____title": "Sales return request", "@sage/xtrem-sales/pages__sales_return_request___id____title": "ID", "@sage/xtrem-sales/pages__sales_return_request___no_parameters": "No action set.", "@sage/xtrem-sales/pages__sales_return_request__addSalesReturnRequestLine____title": "Add", "@sage/xtrem-sales/pages__sales_return_request__apply_dimensions_success": "Dimensions applied.", "@sage/xtrem-sales/pages__sales_return_request__approvalStatus____title": "Approval status", "@sage/xtrem-sales/pages__sales_return_request__approve____title": "Approve", "@sage/xtrem-sales/pages__sales_return_request__approve_action_dialog_content": "You are about to approve this sales return request.", "@sage/xtrem-sales/pages__sales_return_request__approve_action_dialog_title": "Confirm approving", "@sage/xtrem-sales/pages__sales_return_request__approverSelectionBlock____title": "Select approver", "@sage/xtrem-sales/pages__sales_return_request__billToAddress____columns__columns__country__title": "ISO 3166-1 alpha-2 code", "@sage/xtrem-sales/pages__sales_return_request__billToAddress____columns__title__addressLine1": "Address line 1", "@sage/xtrem-sales/pages__sales_return_request__billToAddress____columns__title__addressLine2": "Address line 2", "@sage/xtrem-sales/pages__sales_return_request__billToAddress____columns__title__city": "City", "@sage/xtrem-sales/pages__sales_return_request__billToAddress____columns__title__concatenatedAddress": "Bill-to address", "@sage/xtrem-sales/pages__sales_return_request__billToAddress____columns__title__locationPhoneNumber": "Phone number", "@sage/xtrem-sales/pages__sales_return_request__billToAddress____columns__title__name": "Name", "@sage/xtrem-sales/pages__sales_return_request__billToAddress____dropdownActions__title": "Replace", "@sage/xtrem-sales/pages__sales_return_request__billToAddress____dropdownActions__title__2": "Edit", "@sage/xtrem-sales/pages__sales_return_request__billToAddress____dropdownActions__title__3": "Read-only", "@sage/xtrem-sales/pages__sales_return_request__billToAddress____title": "Bill-to address", "@sage/xtrem-sales/pages__sales_return_request__billToCustomer____columns__title__businessEntity__country__name": "Country", "@sage/xtrem-sales/pages__sales_return_request__billToCustomer____columns__title__businessEntity__id": "ID", "@sage/xtrem-sales/pages__sales_return_request__billToCustomer____columns__title__businessEntity__name": "Name", "@sage/xtrem-sales/pages__sales_return_request__billToCustomer____columns__title__businessEntity__taxIdNumber": "Tax ID", "@sage/xtrem-sales/pages__sales_return_request__billToCustomer____lookupDialogTitle": "Select bill-to customer", "@sage/xtrem-sales/pages__sales_return_request__billToCustomer____title": "Bill-to customer", "@sage/xtrem-sales/pages__sales_return_request__billToLinkedAddress____columns__columns__country__title": "ISO 3166-1 alpha-2 code", "@sage/xtrem-sales/pages__sales_return_request__billToLinkedAddress____columns__title__addressLine1": "Address line 1", "@sage/xtrem-sales/pages__sales_return_request__billToLinkedAddress____columns__title__addressLine2": "Address line 2", "@sage/xtrem-sales/pages__sales_return_request__billToLinkedAddress____columns__title__city": "City", "@sage/xtrem-sales/pages__sales_return_request__billToLinkedAddress____columns__title__isActive": "Active", "@sage/xtrem-sales/pages__sales_return_request__billToLinkedAddress____columns__title__locationPhoneNumber": "Phone number", "@sage/xtrem-sales/pages__sales_return_request__billToLinkedAddress____columns__title__postcode": "ZIP code", "@sage/xtrem-sales/pages__sales_return_request__billToLinkedAddress____columns__title__region": "Region", "@sage/xtrem-sales/pages__sales_return_request__billToLinkedAddress____lookupDialogTitle": "Select bill-to address", "@sage/xtrem-sales/pages__sales_return_request__billToLinkedAddress____title": "Bill-to address", "@sage/xtrem-sales/pages__sales_return_request__close____title": "Close request", "@sage/xtrem-sales/pages__sales_return_request__confirm____title": "Confirm", "@sage/xtrem-sales/pages__sales_return_request__confirm_dialog_content": "You are about to set this sales return request to \"Confirmed\"", "@sage/xtrem-sales/pages__sales_return_request__confirm_dialog_title": "Confirm sales return request", "@sage/xtrem-sales/pages__sales_return_request__confirmation_successful": "Confirmation successful", "@sage/xtrem-sales/pages__sales_return_request__create_credit_memo_dialog_content": "You are about to create a credit memo from this sales return request.", "@sage/xtrem-sales/pages__sales_return_request__create_credit_memo_dialog_title": "Confirm credit memo creation", "@sage/xtrem-sales/pages__sales_return_request__createSalesCreditMemosFromReturnRequests____title": "Create credit memo", "@sage/xtrem-sales/pages__sales_return_request__credit_exception": "The credit memo could not be created: {{exception}}", "@sage/xtrem-sales/pages__sales_return_request__credit_memo_created": "The sales credit memo was created.", "@sage/xtrem-sales/pages__sales_return_request__creditStatus____title": "Credit status", "@sage/xtrem-sales/pages__sales_return_request__date____title": "Return request date", "@sage/xtrem-sales/pages__sales_return_request__default_approver": "<PERSON><PERSON><PERSON>", "@sage/xtrem-sales/pages__sales_return_request__defaultDimension____title": "Set dimensions", "@sage/xtrem-sales/pages__sales_return_request__deliveryMode____lookupDialogTitle": "Select delivery mode", "@sage/xtrem-sales/pages__sales_return_request__deliveryMode____title": "Delivery mode", "@sage/xtrem-sales/pages__sales_return_request__displayStatus____title": "Display status", "@sage/xtrem-sales/pages__sales_return_request__email_exception": "Could not send request email. ({{exception}})", "@sage/xtrem-sales/pages__sales_return_request__email_sent": "Email sent to {{value}} for approval.", "@sage/xtrem-sales/pages__sales_return_request__emailAddressApproval____helperText": "A request for approval email will be sent to this address", "@sage/xtrem-sales/pages__sales_return_request__emailAddressApproval____title": "To", "@sage/xtrem-sales/pages__sales_return_request__financialSection____title": "Financial", "@sage/xtrem-sales/pages__sales_return_request__financialSectionFinancialBlock____title": "Financial", "@sage/xtrem-sales/pages__sales_return_request__header_close_action_dialog_content": "You are about to change the status of this request to closed. You can reopen this request later.", "@sage/xtrem-sales/pages__sales_return_request__header_close_action_dialog_title": "Confirm status change", "@sage/xtrem-sales/pages__sales_return_request__header_open_action_dialog_content": "You are about to change the status of this request to open.", "@sage/xtrem-sales/pages__sales_return_request__header_open_action_dialog_title": "Confirm status change", "@sage/xtrem-sales/pages__sales_return_request__headerSection____title": "Header section", "@sage/xtrem-sales/pages__sales_return_request__informationBlock____title": "Information", "@sage/xtrem-sales/pages__sales_return_request__informationSection____title": "Information", "@sage/xtrem-sales/pages__sales_return_request__internalNote____helperText": "Notes display on internal documents.", "@sage/xtrem-sales/pages__sales_return_request__internalNote____title": "Internal notes", "@sage/xtrem-sales/pages__sales_return_request__internalNoteLine____helperText": "Notes display on internal documents.", "@sage/xtrem-sales/pages__sales_return_request__internalNoteLine____title": "Internal line notes", "@sage/xtrem-sales/pages__sales_return_request__invalid-email": "Invalid email address {{value}}", "@sage/xtrem-sales/pages__sales_return_request__isCreditingAllowed____title": "Credit allowed", "@sage/xtrem-sales/pages__sales_return_request__isPrinted____title": "Printed", "@sage/xtrem-sales/pages__sales_return_request__isTransferHeaderNote____title": "Repeat the document notes on new documents.", "@sage/xtrem-sales/pages__sales_return_request__isTransferLineNote____title": "Repeat all the line notes on new documents.", "@sage/xtrem-sales/pages__sales_return_request__itemSection____title": "Lines", "@sage/xtrem-sales/pages__sales_return_request__line_close_action_dialog_content": "You are about to close this sales return request line.", "@sage/xtrem-sales/pages__sales_return_request__line_close_action_dialog_title": "Confirm update", "@sage/xtrem-sales/pages__sales_return_request__line_delete_action_dialog_content": "You are about to delete this sales return request line. This action cannot be undone.", "@sage/xtrem-sales/pages__sales_return_request__line_delete_action_dialog_title": "Confirm delete", "@sage/xtrem-sales/pages__sales_return_request__line_open_action_dialog_content": "You are about to open this sales return request line.", "@sage/xtrem-sales/pages__sales_return_request__line_open_action_dialog_title": "Confirm update", "@sage/xtrem-sales/pages__sales_return_request__lines____columns__columns__item__name__title": "Category", "@sage/xtrem-sales/pages__sales_return_request__lines____columns__columns__originShippingSite__name__title": "Company", "@sage/xtrem-sales/pages__sales_return_request__lines____columns__columns__stockUnit__name__title": "Symbol", "@sage/xtrem-sales/pages__sales_return_request__lines____columns__columns__unit__name__title": "Symbol", "@sage/xtrem-sales/pages__sales_return_request__lines____columns__title__creditStatus": "Credit memo status", "@sage/xtrem-sales/pages__sales_return_request__lines____columns__title__item__id": "Item ID", "@sage/xtrem-sales/pages__sales_return_request__lines____columns__title__item__image": "Image", "@sage/xtrem-sales/pages__sales_return_request__lines____columns__title__item__name": "<PERSON><PERSON>", "@sage/xtrem-sales/pages__sales_return_request__lines____columns__title__itemDescription": "Item description", "@sage/xtrem-sales/pages__sales_return_request__lines____columns__title__origin": "Origin", "@sage/xtrem-sales/pages__sales_return_request__lines____columns__title__originShippingSite__name": "Origin shipping site", "@sage/xtrem-sales/pages__sales_return_request__lines____columns__title__quantity": "Quantity in sales unit", "@sage/xtrem-sales/pages__sales_return_request__lines____columns__title__quantityCreditedInProgressInSalesUnit": "Quantity in progress", "@sage/xtrem-sales/pages__sales_return_request__lines____columns__title__quantityCreditedPostedInSalesUnit": "Credited quantity", "@sage/xtrem-sales/pages__sales_return_request__lines____columns__title__quantityInStockUnit": "Quantity in stock unit", "@sage/xtrem-sales/pages__sales_return_request__lines____columns__title__quantityReceiptInSalesUnit": "Receipt quantity", "@sage/xtrem-sales/pages__sales_return_request__lines____columns__title__quantityToReceiveInProgressInSalesUnit": "Quantity in progress", "@sage/xtrem-sales/pages__sales_return_request__lines____columns__title__reason__name": "Reason", "@sage/xtrem-sales/pages__sales_return_request__lines____columns__title__receiptStatus": "Receipt status", "@sage/xtrem-sales/pages__sales_return_request__lines____columns__title__status": "Status", "@sage/xtrem-sales/pages__sales_return_request__lines____columns__title__stockUnit__name": "Stock unit", "@sage/xtrem-sales/pages__sales_return_request__lines____columns__title__unit__name": "Sales unit", "@sage/xtrem-sales/pages__sales_return_request__lines____columns__title__unitToStockUnitConversionFactor": "Stock unit conversion factor", "@sage/xtrem-sales/pages__sales_return_request__lines____dropdownActions__title": "Close", "@sage/xtrem-sales/pages__sales_return_request__lines____dropdownActions__title__2": "Open", "@sage/xtrem-sales/pages__sales_return_request__lines____dropdownActions__title__3": "Dimensions", "@sage/xtrem-sales/pages__sales_return_request__lines____dropdownActions__title__4": "Delete", "@sage/xtrem-sales/pages__sales_return_request__lines____inlineActions__title": "Open line panel", "@sage/xtrem-sales/pages__sales_return_request__lines____mobileCard__line2__title": "Description", "@sage/xtrem-sales/pages__sales_return_request__lines____mobileCard__line3__title": "Reason", "@sage/xtrem-sales/pages__sales_return_request__lines____mobileCard__title__title": "Product", "@sage/xtrem-sales/pages__sales_return_request__lines____mobileCard__titleRight__title": "Status", "@sage/xtrem-sales/pages__sales_return_request__lines____optionsMenu__title": "All statuses", "@sage/xtrem-sales/pages__sales_return_request__lines____optionsMenu__title__2": "All open statuses", "@sage/xtrem-sales/pages__sales_return_request__lines____sidebar__headerDropdownActions__title": "Close", "@sage/xtrem-sales/pages__sales_return_request__lines____sidebar__headerDropdownActions__title__2": "Open", "@sage/xtrem-sales/pages__sales_return_request__lines____sidebar__headerDropdownActions__title__3": "Dimensions", "@sage/xtrem-sales/pages__sales_return_request__lines____sidebar__headerDropdownActions__title__4": "Delete", "@sage/xtrem-sales/pages__sales_return_request__lines____title": "Lines", "@sage/xtrem-sales/pages__sales_return_request__noteBlock____title": "Notes", "@sage/xtrem-sales/pages__sales_return_request__notesSection____title": "Notes", "@sage/xtrem-sales/pages__sales_return_request__number____title": "Number", "@sage/xtrem-sales/pages__sales_return_request__open____title": "Open request", "@sage/xtrem-sales/pages__sales_return_request__receiptStatus____title": "Receipt statusR", "@sage/xtrem-sales/pages__sales_return_request__reject____title": "Reject", "@sage/xtrem-sales/pages__sales_return_request__reject_action_dialog_content": "You are about to reject this sales return request.", "@sage/xtrem-sales/pages__sales_return_request__reject_action_dialog_title": "Confirm rejecting", "@sage/xtrem-sales/pages__sales_return_request__requestApproval____title": "Submit for approval", "@sage/xtrem-sales/pages__sales_return_request__requestApprovalBlock____title": "", "@sage/xtrem-sales/pages__sales_return_request__requestApprovalSection____title": "Request for approval", "@sage/xtrem-sales/pages__sales_return_request__requester____columns__title__email": "Email", "@sage/xtrem-sales/pages__sales_return_request__requester____columns__title__firstName": "Name", "@sage/xtrem-sales/pages__sales_return_request__requester____columns__title__lastName": "Last name", "@sage/xtrem-sales/pages__sales_return_request__requester____lookupDialogTitle": "Select requester", "@sage/xtrem-sales/pages__sales_return_request__requester____title": "Requester", "@sage/xtrem-sales/pages__sales_return_request__return_request_date__cannot__be__future": "The return request date cannot be later than today.", "@sage/xtrem-sales/pages__sales_return_request__returnType____title": "Return type", "@sage/xtrem-sales/pages__sales_return_request__salesCreditMemoLines____columns__title___id": "Credit memo number", "@sage/xtrem-sales/pages__sales_return_request__salesCreditMemoLines____columns__title___id__2": "Credit memo status", "@sage/xtrem-sales/pages__sales_return_request__salesCreditMemoLines____columns__title__netPrice": "Amount credited", "@sage/xtrem-sales/pages__sales_return_request__salesCreditMemoLines____columns__title__quantity": "Quantity in sales unit credited", "@sage/xtrem-sales/pages__sales_return_request__salesCreditMemoLines____title": "Credit memo lines", "@sage/xtrem-sales/pages__sales_return_request__salesLinkedDocuments____columns__title___id": "Origin document number", "@sage/xtrem-sales/pages__sales_return_request__salesLinkedDocuments____columns__title__displayStatus": "Status", "@sage/xtrem-sales/pages__sales_return_request__salesLinkedDocuments____columns__title__documentType": "Document type", "@sage/xtrem-sales/pages__sales_return_request__salesLinkedDocuments____title": "Shipment", "@sage/xtrem-sales/pages__sales_return_request__salesReturnReceiptLines____columns__title___id": "Return receipt number", "@sage/xtrem-sales/pages__sales_return_request__salesReturnReceiptLines____columns__title___id__2": "Return receipt status", "@sage/xtrem-sales/pages__sales_return_request__salesReturnReceiptLines____columns__title__quantity": "Quantity in sales unit", "@sage/xtrem-sales/pages__sales_return_request__salesReturnReceiptLines____columns__title__quantityInStockUnit": "Quantity in stock unit", "@sage/xtrem-sales/pages__sales_return_request__salesReturnReceiptLines____title": "Receipt lines", "@sage/xtrem-sales/pages__sales_return_request__salesReturnRequestLineCount____title": "Number of items", "@sage/xtrem-sales/pages__sales_return_request__select_approval_button_text": "Select approver", "@sage/xtrem-sales/pages__sales_return_request__selectApprover____title": "Select approver", "@sage/xtrem-sales/pages__sales_return_request__selectedUser____columns__title__email": "Email", "@sage/xtrem-sales/pages__sales_return_request__selectedUser____columns__title__firstName": "Name", "@sage/xtrem-sales/pages__sales_return_request__selectedUser____columns__title__lastName": "Last name", "@sage/xtrem-sales/pages__sales_return_request__selectedUser____lookupDialogTitle": "Select selected user", "@sage/xtrem-sales/pages__sales_return_request__selectedUser____title": "Selected user", "@sage/xtrem-sales/pages__sales_return_request__send_approval_request_button_text": "Send", "@sage/xtrem-sales/pages__sales_return_request__send_approval_request_dialog_content": "You are about to send the approval email.", "@sage/xtrem-sales/pages__sales_return_request__send_approval_request_dialog_title": "Confirm send approval email", "@sage/xtrem-sales/pages__sales_return_request__sendApprovalRequestButton____title": "Send", "@sage/xtrem-sales/pages__sales_return_request__shippingBlock____title": "Shipping", "@sage/xtrem-sales/pages__sales_return_request__shippingSection____title": "Shipping", "@sage/xtrem-sales/pages__sales_return_request__shipToAddress____columns__columns__country__title": "ISO 3166-1 alpha-2 code", "@sage/xtrem-sales/pages__sales_return_request__shipToAddress____columns__title__addressLine1": "Address line 1", "@sage/xtrem-sales/pages__sales_return_request__shipToAddress____columns__title__addressLine2": "Address line 2", "@sage/xtrem-sales/pages__sales_return_request__shipToAddress____columns__title__city": "City", "@sage/xtrem-sales/pages__sales_return_request__shipToAddress____columns__title__concatenatedAddress": "Bill-to address", "@sage/xtrem-sales/pages__sales_return_request__shipToAddress____columns__title__locationPhoneNumber": "Phone number", "@sage/xtrem-sales/pages__sales_return_request__shipToAddress____columns__title__name": "Name", "@sage/xtrem-sales/pages__sales_return_request__shipToAddress____dropdownActions__title": "Replace", "@sage/xtrem-sales/pages__sales_return_request__shipToAddress____dropdownActions__title__2": "Edit", "@sage/xtrem-sales/pages__sales_return_request__shipToAddress____dropdownActions__title__3": "Read-only", "@sage/xtrem-sales/pages__sales_return_request__shipToAddress____title": "Ship-to address", "@sage/xtrem-sales/pages__sales_return_request__shipToCustomer____columns__title__businessEntity__country__name": "Tax ID", "@sage/xtrem-sales/pages__sales_return_request__shipToCustomer____columns__title__businessEntity__id": "ID", "@sage/xtrem-sales/pages__sales_return_request__shipToCustomer____columns__title__businessEntity__name": "Name", "@sage/xtrem-sales/pages__sales_return_request__shipToCustomer____columns__title__businessEntity__taxIdNumber": "Tax ID", "@sage/xtrem-sales/pages__sales_return_request__shipToCustomer____lookupDialogTitle": "Select ship-to customer", "@sage/xtrem-sales/pages__sales_return_request__shipToCustomer____title": "Ship-to customer", "@sage/xtrem-sales/pages__sales_return_request__shipToCustomerAddress____columns__columns__country__title": "ISO 3166-1 alpha-2 code", "@sage/xtrem-sales/pages__sales_return_request__shipToCustomerAddress____columns__columns__deliveryDetail___id__columns__title__legalCompany__name": "Company", "@sage/xtrem-sales/pages__sales_return_request__shipToCustomerAddress____columns__columns__deliveryDetail___id__postfix": "day(s)", "@sage/xtrem-sales/pages__sales_return_request__shipToCustomerAddress____columns__columns__deliveryDetail___id__title": "Incoterms® rule", "@sage/xtrem-sales/pages__sales_return_request__shipToCustomerAddress____columns__columns__deliveryDetail___id__title__2": "Delivery mode", "@sage/xtrem-sales/pages__sales_return_request__shipToCustomerAddress____columns__columns__deliveryDetail___id__title__3": "Delivery lead time", "@sage/xtrem-sales/pages__sales_return_request__shipToCustomerAddress____columns__columns__deliveryDetail___id__title__4": "Stock site", "@sage/xtrem-sales/pages__sales_return_request__shipToCustomerAddress____columns__title__addressLine1": "Address line 1", "@sage/xtrem-sales/pages__sales_return_request__shipToCustomerAddress____columns__title__addressLine2": "Address line 2", "@sage/xtrem-sales/pages__sales_return_request__shipToCustomerAddress____columns__title__city": "City", "@sage/xtrem-sales/pages__sales_return_request__shipToCustomerAddress____columns__title__deliveryDetail___id": "Ship-to address", "@sage/xtrem-sales/pages__sales_return_request__shipToCustomerAddress____columns__title__deliveryDetail__isPrimary": "Primary ship-to address", "@sage/xtrem-sales/pages__sales_return_request__shipToCustomerAddress____columns__title__isActive": "Active", "@sage/xtrem-sales/pages__sales_return_request__shipToCustomerAddress____columns__title__locationPhoneNumber": "Phone number", "@sage/xtrem-sales/pages__sales_return_request__shipToCustomerAddress____columns__title__postcode": "ZIP code", "@sage/xtrem-sales/pages__sales_return_request__shipToCustomerAddress____columns__title__region": "Region", "@sage/xtrem-sales/pages__sales_return_request__shipToCustomerAddress____lookupDialogTitle": "Select ship-to address", "@sage/xtrem-sales/pages__sales_return_request__shipToCustomerAddress____title": "Ship-to address", "@sage/xtrem-sales/pages__sales_return_request__site____columns__title__legalCompany__name": "Company", "@sage/xtrem-sales/pages__sales_return_request__site____lookupDialogTitle": "Select site", "@sage/xtrem-sales/pages__sales_return_request__site____title": "Site", "@sage/xtrem-sales/pages__sales_return_request__soldToAddress____columns__columns__country__title": "ISO 3166-1 alpha-2 code", "@sage/xtrem-sales/pages__sales_return_request__soldToAddress____columns__title__addressLine1": "Address line 1", "@sage/xtrem-sales/pages__sales_return_request__soldToAddress____columns__title__addressLine2": "Address line 2", "@sage/xtrem-sales/pages__sales_return_request__soldToAddress____columns__title__city": "City", "@sage/xtrem-sales/pages__sales_return_request__soldToAddress____columns__title__concatenatedAddress": "Bill-to address", "@sage/xtrem-sales/pages__sales_return_request__soldToAddress____columns__title__locationPhoneNumber": "Phone number", "@sage/xtrem-sales/pages__sales_return_request__soldToAddress____columns__title__name": "Name", "@sage/xtrem-sales/pages__sales_return_request__soldToAddress____dropdownActions__title": "Replace", "@sage/xtrem-sales/pages__sales_return_request__soldToAddress____dropdownActions__title__2": "Edit", "@sage/xtrem-sales/pages__sales_return_request__soldToAddress____dropdownActions__title__3": "Read-only", "@sage/xtrem-sales/pages__sales_return_request__soldToAddress____title": "Sold-to address", "@sage/xtrem-sales/pages__sales_return_request__soldToCustomer____columns__title__businessEntity__country__name": "Country", "@sage/xtrem-sales/pages__sales_return_request__soldToCustomer____columns__title__businessEntity__id": "ID", "@sage/xtrem-sales/pages__sales_return_request__soldToCustomer____columns__title__businessEntity__name": "Name", "@sage/xtrem-sales/pages__sales_return_request__soldToCustomer____columns__title__businessEntity__taxIdNumber": "Tax ID", "@sage/xtrem-sales/pages__sales_return_request__soldToCustomer____lookupDialogTitle": "Select sold-to customer", "@sage/xtrem-sales/pages__sales_return_request__soldToCustomer____title": "Sold-to customer", "@sage/xtrem-sales/pages__sales_return_request__soldToLinkedAddress____columns__columns__country__title": "ISO 3166-1 alpha-2 code", "@sage/xtrem-sales/pages__sales_return_request__soldToLinkedAddress____columns__columns__country__title__2": "Region label", "@sage/xtrem-sales/pages__sales_return_request__soldToLinkedAddress____columns__title__addressLine1": "Address line 1", "@sage/xtrem-sales/pages__sales_return_request__soldToLinkedAddress____columns__title__addressLine2": "Address line 2", "@sage/xtrem-sales/pages__sales_return_request__soldToLinkedAddress____columns__title__city": "City", "@sage/xtrem-sales/pages__sales_return_request__soldToLinkedAddress____columns__title__isActive": "Active", "@sage/xtrem-sales/pages__sales_return_request__soldToLinkedAddress____columns__title__locationPhoneNumber": "Phone number", "@sage/xtrem-sales/pages__sales_return_request__soldToLinkedAddress____columns__title__postcode": "ZIP code", "@sage/xtrem-sales/pages__sales_return_request__soldToLinkedAddress____columns__title__region": "Region", "@sage/xtrem-sales/pages__sales_return_request__soldToLinkedAddress____lookupDialogTitle": "Select sold-to address", "@sage/xtrem-sales/pages__sales_return_request__soldToLinkedAddress____title": "Sold-to address", "@sage/xtrem-sales/pages__sales_return_request__status____title": "Status", "@sage/xtrem-sales/pages__sales_return_request__step_sequence_approve": "Approve", "@sage/xtrem-sales/pages__sales_return_request__step_sequence_confirm": "Confirm", "@sage/xtrem-sales/pages__sales_return_request__step_sequence_creation": "Create", "@sage/xtrem-sales/pages__sales_return_request__step_sequence_credit": "Credit", "@sage/xtrem-sales/pages__sales_return_request__step_sequence_receive": "Receive", "@sage/xtrem-sales/pages__sales_return_request__stockSite____columns__title__legalCompany__name": "Company", "@sage/xtrem-sales/pages__sales_return_request__stockSite____lookupDialogTitle": "Select stock site", "@sage/xtrem-sales/pages__sales_return_request__stockSite____title": "Stock site", "@sage/xtrem-sales/pages__sales_return_request__substitute_approver": "Substitute", "@sage/xtrem-sales/pages__sales_return_request__text____title": "Text", "@sage/xtrem-sales/pages__sales_return_request__textSection____title": "Text", "@sage/xtrem-sales/pages__sales_return_request__textSectionTextBlock____title": "", "@sage/xtrem-sales/pages__sales_return_request__users____columns__title__email": "Email", "@sage/xtrem-sales/pages__sales_return_request__users____columns__title__firstName": "Name", "@sage/xtrem-sales/pages__sales_return_request__users____columns__title__lastName": "Last name", "@sage/xtrem-sales/pages__sales_return_request__users____columns__title__type": "Approver", "@sage/xtrem-sales/pages__sales_return_request__users____title": "Users", "@sage/xtrem-sales/pages__sales_return_request_line_panel____title": "New sales return request line", "@sage/xtrem-sales/pages__sales_return_request_line_panel___id____title": "ID", "@sage/xtrem-sales/pages__sales_return_request_line_panel__cancelLine____title": "Cancel", "@sage/xtrem-sales/pages__sales_return_request_line_panel__dimensionsButton____title": "Dimensions", "@sage/xtrem-sales/pages__sales_return_request_line_panel__document____lookupDialogTitle": "Select sales return request", "@sage/xtrem-sales/pages__sales_return_request_line_panel__document____title": "Sales return request", "@sage/xtrem-sales/pages__sales_return_request_line_panel__generalBlock____title": "", "@sage/xtrem-sales/pages__sales_return_request_line_panel__generalSection____title": "General", "@sage/xtrem-sales/pages__sales_return_request_line_panel__hiddenBlock____title": "", "@sage/xtrem-sales/pages__sales_return_request_line_panel__item____columns__title__category__name": "Category", "@sage/xtrem-sales/pages__sales_return_request_line_panel__item____lookupDialogTitle": "Select item", "@sage/xtrem-sales/pages__sales_return_request_line_panel__item____title": "<PERSON><PERSON>", "@sage/xtrem-sales/pages__sales_return_request_line_panel__itemDescription____title": "Item description", "@sage/xtrem-sales/pages__sales_return_request_line_panel__itemId____title": "Item ID", "@sage/xtrem-sales/pages__sales_return_request_line_panel__itemImage____title": "Item image", "@sage/xtrem-sales/pages__sales_return_request_line_panel__origin____title": "Origin", "@sage/xtrem-sales/pages__sales_return_request_line_panel__originShippingSite____columns__title__legalCompany__name": "Company", "@sage/xtrem-sales/pages__sales_return_request_line_panel__originShippingSite____lookupDialogTitle": "Select origin shipping site", "@sage/xtrem-sales/pages__sales_return_request_line_panel__originShippingSite____title": "Origin shipping site", "@sage/xtrem-sales/pages__sales_return_request_line_panel__quantity____title": "Quantity", "@sage/xtrem-sales/pages__sales_return_request_line_panel__quantityInStockUnit____title": "Quantity", "@sage/xtrem-sales/pages__sales_return_request_line_panel__quantitySection____title": "Quantities", "@sage/xtrem-sales/pages__sales_return_request_line_panel__reason____lookupDialogTitle": "Select reason", "@sage/xtrem-sales/pages__sales_return_request_line_panel__reason____title": "Reason", "@sage/xtrem-sales/pages__sales_return_request_line_panel__rowId____title": "Row ID", "@sage/xtrem-sales/pages__sales_return_request_line_panel__salesBlock____title": "Sales", "@sage/xtrem-sales/pages__sales_return_request_line_panel__status____title": "Status", "@sage/xtrem-sales/pages__sales_return_request_line_panel__stockBlock____title": "Stock", "@sage/xtrem-sales/pages__sales_return_request_line_panel__stockUnit____columns__title__symbol": "Symbol", "@sage/xtrem-sales/pages__sales_return_request_line_panel__stockUnit____lookupDialogTitle": "Select unit", "@sage/xtrem-sales/pages__sales_return_request_line_panel__stockUnit____title": "Unit", "@sage/xtrem-sales/pages__sales_return_request_line_panel__text____title": "Text", "@sage/xtrem-sales/pages__sales_return_request_line_panel__textBlock____title": "", "@sage/xtrem-sales/pages__sales_return_request_line_panel__textSection____title": "Text", "@sage/xtrem-sales/pages__sales_return_request_line_panel__unit____columns__title__symbol": "Symbol", "@sage/xtrem-sales/pages__sales_return_request_line_panel__unit____lookupDialogTitle": "Select unit", "@sage/xtrem-sales/pages__sales_return_request_line_panel__unit____title": "Unit", "@sage/xtrem-sales/pages__sales_return_request_line_panel__unitToStockUnitConversionFactor____title": "Stock unit conversion factor", "@sage/xtrem-sales/pages__sales_return_request_line_panel__validate____title": "OK", "@sage/xtrem-sales/pages__sales_return_request_reason____navigationPanel__optionsMenu__title": "All", "@sage/xtrem-sales/pages__sales_return_request_reason____navigationPanel__optionsMenu__title__2": "Active", "@sage/xtrem-sales/pages__sales_return_request_reason____navigationPanel__optionsMenu__title__3": "Inactive", "@sage/xtrem-sales/pages__sales_return_request_reason____objectTypePlural": "Return request reasons", "@sage/xtrem-sales/pages__sales_return_request_reason____objectTypeSingular": "Return request reason", "@sage/xtrem-sales/pages__sales_return_request_reason____title": "Return request reason", "@sage/xtrem-sales/pages__sales_return_request_reason___id____title": "ID", "@sage/xtrem-sales/pages__sales_return_request_reason__description____title": "Description", "@sage/xtrem-sales/pages__sales_return_request_reason__id____title": "ID", "@sage/xtrem-sales/pages__sales_return_request_reason__isActive____title": "Active", "@sage/xtrem-sales/pages__sales_return_request_reason__mainSection____title": "General", "@sage/xtrem-sales/pages__sales_return_request_reason__name____title": "Name", "@sage/xtrem-sales/pages__sales_return-_receipt__on_return_receipt_quantity_changed_error": "The new quantity cannot be greater than the actual quantity", "@sage/xtrem-sales/pages__sales_return-request__cascade_ship_to_address_update_dialog_content": "You are about to update delivery information.", "@sage/xtrem-sales/pages__sales_return-request__cascade_ship_to_address_update_dialog_title": "Confirm update", "@sage/xtrem-sales/pages__sales_return-request__on_return-request_quantity_changed_error_title": "Error on quantity", "@sage/xtrem-sales/pages__sales_return-request_line_panel____update_title": "Edit sales return request line", "@sage/xtrem-sales/pages__sales_shipment____navigationPanel__bulkActions__title": "Print packing slip", "@sage/xtrem-sales/pages__sales_shipment____navigationPanel__bulkActions__title__2": "Print pick list", "@sage/xtrem-sales/pages__sales_shipment____navigationPanel__dropdownActions__title": "Confirm", "@sage/xtrem-sales/pages__sales_shipment____navigationPanel__dropdownActions__title__2": "Post stock", "@sage/xtrem-sales/pages__sales_shipment____navigationPanel__dropdownActions__title__3": "<PERSON><PERSON>", "@sage/xtrem-sales/pages__sales_shipment____navigationPanel__dropdownActions__title__4": "Create invoice", "@sage/xtrem-sales/pages__sales_shipment____navigationPanel__dropdownActions__title__5": "Create return request", "@sage/xtrem-sales/pages__sales_shipment____navigationPanel__dropdownActions__title__6": "Print", "@sage/xtrem-sales/pages__sales_shipment____navigationPanel__dropdownActions__title__7": "Set dimensions", "@sage/xtrem-sales/pages__sales_shipment____navigationPanel__dropdownActions__title__8": "Delete", "@sage/xtrem-sales/pages__sales_shipment____navigationPanel__listItem__billToCustomerId__title": "Bill-to customer ID", "@sage/xtrem-sales/pages__sales_shipment____navigationPanel__listItem__isOnHold__title": "On hold", "@sage/xtrem-sales/pages__sales_shipment____navigationPanel__listItem__line_4__title": "Site", "@sage/xtrem-sales/pages__sales_shipment____navigationPanel__listItem__line_5__title": "Reference", "@sage/xtrem-sales/pages__sales_shipment____navigationPanel__listItem__line10__title": "Tracking number", "@sage/xtrem-sales/pages__sales_shipment____navigationPanel__listItem__line11__title": "Incoterms® rule", "@sage/xtrem-sales/pages__sales_shipment____navigationPanel__listItem__line12__title": "Delivery mode", "@sage/xtrem-sales/pages__sales_shipment____navigationPanel__listItem__line13__postfix": "days", "@sage/xtrem-sales/pages__sales_shipment____navigationPanel__listItem__line13__title": "Delivery lead time", "@sage/xtrem-sales/pages__sales_shipment____navigationPanel__listItem__line14__title": "Delivery date", "@sage/xtrem-sales/pages__sales_shipment____navigationPanel__listItem__line15__title": "Bill-to customer", "@sage/xtrem-sales/pages__sales_shipment____navigationPanel__listItem__line16__title": "Payment term", "@sage/xtrem-sales/pages__sales_shipment____navigationPanel__listItem__line2__title": "Ship-to customer", "@sage/xtrem-sales/pages__sales_shipment____navigationPanel__listItem__line2Right__title": "Shipping date", "@sage/xtrem-sales/pages__sales_shipment____navigationPanel__listItem__line6__title": "Printed", "@sage/xtrem-sales/pages__sales_shipment____navigationPanel__listItem__line7__title": "Return request status", "@sage/xtrem-sales/pages__sales_shipment____navigationPanel__listItem__line8__title": "Return receipt status", "@sage/xtrem-sales/pages__sales_shipment____navigationPanel__listItem__line9__columns__title__legalCompany__name": "Company", "@sage/xtrem-sales/pages__sales_shipment____navigationPanel__listItem__line9__title": "Stock site", "@sage/xtrem-sales/pages__sales_shipment____navigationPanel__listItem__shipToCustomerId__title": "Ship-to customer ID", "@sage/xtrem-sales/pages__sales_shipment____navigationPanel__listItem__title__title": "Number", "@sage/xtrem-sales/pages__sales_shipment____navigationPanel__listItem__titleRight__title": "Status", "@sage/xtrem-sales/pages__sales_shipment____navigationPanel__optionsMenu__title": "All open statuses", "@sage/xtrem-sales/pages__sales_shipment____navigationPanel__optionsMenu__title__2": "All statuses", "@sage/xtrem-sales/pages__sales_shipment____navigationPanel__optionsMenu__title__3": "Ready to process", "@sage/xtrem-sales/pages__sales_shipment____navigationPanel__optionsMenu__title__4": "Ready to ship", "@sage/xtrem-sales/pages__sales_shipment____navigationPanel__optionsMenu__title__5": "Shipped", "@sage/xtrem-sales/pages__sales_shipment____navigationPanel__optionsMenu__title__6": "Partially invoiced", "@sage/xtrem-sales/pages__sales_shipment____navigationPanel__optionsMenu__title__7": "Invoiced", "@sage/xtrem-sales/pages__sales_shipment____navigationPanel__optionsMenu__title__8": "Posting in progress", "@sage/xtrem-sales/pages__sales_shipment____navigationPanel__optionsMenu__title__9": "Error", "@sage/xtrem-sales/pages__sales_shipment____objectTypePlural": "Sales shipments", "@sage/xtrem-sales/pages__sales_shipment____objectTypeSingular": "Sales shipment", "@sage/xtrem-sales/pages__sales_shipment____title": "Sales shipment", "@sage/xtrem-sales/pages__sales_shipment___post_on_hold_action__action_dialog_content": "You are about to post the sales shipment for an on hold customer.", "@sage/xtrem-sales/pages__sales_shipment__actualOnOrderQuantity____title": "Actual quantity", "@sage/xtrem-sales/pages__sales_shipment__add_lines_on_hold_action__action_dialog_content": "You are about to select sales orders for an on hold customer.", "@sage/xtrem-sales/pages__sales_shipment__add_lines_on_hold_action_dialog_title": "Customer on hold", "@sage/xtrem-sales/pages__sales_Shipment__apply_dimensions_success": "Dimensions applied.", "@sage/xtrem-sales/pages__sales_shipment__baseDocumentLine____title": "Order lines", "@sage/xtrem-sales/pages__sales_shipment__billToAddress____columns__columns__country__title": "ISO 3166-1 alpha-2 code", "@sage/xtrem-sales/pages__sales_shipment__billToAddress____columns__title__addressLine1": "Address line 1", "@sage/xtrem-sales/pages__sales_shipment__billToAddress____columns__title__addressLine2": "Address line 2", "@sage/xtrem-sales/pages__sales_shipment__billToAddress____columns__title__city": "City", "@sage/xtrem-sales/pages__sales_shipment__billToAddress____columns__title__concatenatedAddress": "Bill-to address", "@sage/xtrem-sales/pages__sales_shipment__billToAddress____columns__title__locationPhoneNumber": "Phone number", "@sage/xtrem-sales/pages__sales_shipment__billToAddress____columns__title__name": "Name", "@sage/xtrem-sales/pages__sales_shipment__billToAddress____dropdownActions__title": "Replace", "@sage/xtrem-sales/pages__sales_shipment__billToAddress____dropdownActions__title__2": "Edit", "@sage/xtrem-sales/pages__sales_shipment__billToAddress____dropdownActions__title__3": "Read-only", "@sage/xtrem-sales/pages__sales_shipment__billToAddress____title": "Bill-to address", "@sage/xtrem-sales/pages__sales_shipment__billToCustomer____columns__columns__businessEntity__country__title": "ISO 3166-1 alpha-2 code", "@sage/xtrem-sales/pages__sales_shipment__billToCustomer____columns__title__businessEntity__id": "ID", "@sage/xtrem-sales/pages__sales_shipment__billToCustomer____columns__title__businessEntity__name": "Name", "@sage/xtrem-sales/pages__sales_shipment__billToCustomer____columns__title__businessEntity__taxIdNumber": "Tax ID", "@sage/xtrem-sales/pages__sales_shipment__billToCustomer____lookupDialogTitle": "Select bill-to customer", "@sage/xtrem-sales/pages__sales_shipment__billToCustomer____title": "Bill-to customer", "@sage/xtrem-sales/pages__sales_shipment__billToLinkedAddress____columns__columns__country__title": "ISO 3166-1 alpha-2 code", "@sage/xtrem-sales/pages__sales_shipment__billToLinkedAddress____columns__title__addressLine1": "Address line 1", "@sage/xtrem-sales/pages__sales_shipment__billToLinkedAddress____columns__title__addressLine2": "Address line 2", "@sage/xtrem-sales/pages__sales_shipment__billToLinkedAddress____columns__title__city": "City", "@sage/xtrem-sales/pages__sales_shipment__billToLinkedAddress____columns__title__isActive": "Active", "@sage/xtrem-sales/pages__sales_shipment__billToLinkedAddress____columns__title__locationPhoneNumber": "Phone number", "@sage/xtrem-sales/pages__sales_shipment__billToLinkedAddress____columns__title__postcode": "ZIP code", "@sage/xtrem-sales/pages__sales_shipment__billToLinkedAddress____columns__title__region": "Region", "@sage/xtrem-sales/pages__sales_shipment__billToLinkedAddress____lookupDialogTitle": "Select bill-to address", "@sage/xtrem-sales/pages__sales_shipment__billToLinkedAddress____title": "Bill-to address", "@sage/xtrem-sales/pages__sales_shipment__cannot_delete_line_quantity_allocated": "Remove the stock allocation before deleting the line.", "@sage/xtrem-sales/pages__sales_shipment__cascade_ship_to_address_update_dialog_content": "You are about to update delivery information.", "@sage/xtrem-sales/pages__sales_shipment__cascade_ship_to_address_update_dialog_title": "Confirm update", "@sage/xtrem-sales/pages__sales_shipment__confirm____title": "Confirm", "@sage/xtrem-sales/pages__sales_shipment__confirm_dialog_content": "You are about to set this sales shipment to 'Confirmed'.", "@sage/xtrem-sales/pages__sales_shipment__confirm_dialog_title": "Confirm sales shipment", "@sage/xtrem-sales/pages__sales_shipment__confirm_status_updated": "Status updated.", "@sage/xtrem-sales/pages__sales_shipment__create_invoice_dialog_content": "You are about to create an invoice from this sales shipment.", "@sage/xtrem-sales/pages__sales_shipment__create_invoice_dialog_title": "Confirm invoice creation", "@sage/xtrem-sales/pages__sales_shipment__create_return_request_dialog_content": "You are about to create a return request from this sales shipment.", "@sage/xtrem-sales/pages__sales_shipment__create_return_request_dialog_title": "Confirm return request creation", "@sage/xtrem-sales/pages__sales_shipment__createSalesInvoicesFromShipments____title": "Create invoice", "@sage/xtrem-sales/pages__sales_shipment__createSalesReturnRequestFromShipments____title": "Create return request", "@sage/xtrem-sales/pages__sales_shipment__currency____columns__title__id": "ISO 4217 code", "@sage/xtrem-sales/pages__sales_shipment__currency____columns__title__symbol": "Symbol", "@sage/xtrem-sales/pages__sales_shipment__currency____lookupDialogTitle": "Select currency", "@sage/xtrem-sales/pages__sales_shipment__currency____title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-sales/pages__sales_shipment__customSave____title": "Save", "@sage/xtrem-sales/pages__sales_shipment__date____title": "Shipping date", "@sage/xtrem-sales/pages__sales_shipment__defaultDimension____title": "Set dimensions", "@sage/xtrem-sales/pages__sales_shipment__deliveryDate____title": "Delivery date", "@sage/xtrem-sales/pages__sales_shipment__deliveryDateHeader____title": "Estimated delivery date", "@sage/xtrem-sales/pages__sales_shipment__deliveryLeadTime____postfix": "day(s)", "@sage/xtrem-sales/pages__sales_shipment__deliveryLeadTime____title": "Delivery lead time", "@sage/xtrem-sales/pages__sales_shipment__deliveryMode____lookupDialogTitle": "Select delivery mode", "@sage/xtrem-sales/pages__sales_shipment__deliveryMode____title": "Delivery mode", "@sage/xtrem-sales/pages__sales_shipment__display_bill_to_customer_is_on_hold_status_credit_limit": "Bill-to customer on hold \n\nCredit limit {{currencySymbol}} {{creditLimit}}", "@sage/xtrem-sales/pages__sales_shipment__display_customer_is_on_hold_status_credit_limit": "Customer on hold \n\nCredit limit {{currencySymbol}} {{creditLimit}}", "@sage/xtrem-sales/pages__sales_shipment__displayStatus____title": "Display status", "@sage/xtrem-sales/pages__sales_shipment__externalNote____helperText": "Notes display on customer documents.", "@sage/xtrem-sales/pages__sales_shipment__externalNote____title": "Customer notes", "@sage/xtrem-sales/pages__sales_shipment__externalNoteLine____helperText": "Notes display on customer documents.", "@sage/xtrem-sales/pages__sales_shipment__externalNoteLine____title": "Customer line notes", "@sage/xtrem-sales/pages__sales_shipment__financialSection____title": "Financial", "@sage/xtrem-sales/pages__sales_shipment__financialSectionFinancialBlock____title": "Financial", "@sage/xtrem-sales/pages__sales_shipment__goToSysNotificationPage____title": "Retry", "@sage/xtrem-sales/pages__sales_shipment__headerSection____title": "Header section", "@sage/xtrem-sales/pages__sales_shipment__incoterm____lookupDialogTitle": "Select incoterms rule", "@sage/xtrem-sales/pages__sales_shipment__incoterm____title": "Incoterms® rule", "@sage/xtrem-sales/pages__sales_shipment__informationBlock____title": "Information", "@sage/xtrem-sales/pages__sales_shipment__informationSection____title": "Information", "@sage/xtrem-sales/pages__sales_shipment__internalNote____helperText": "Notes display on internal documents.", "@sage/xtrem-sales/pages__sales_shipment__internalNote____title": "Internal notes", "@sage/xtrem-sales/pages__sales_shipment__internalNoteLine____helperText": "Notes display on internal documents.", "@sage/xtrem-sales/pages__sales_shipment__internalNoteLine____title": "Internal line notes", "@sage/xtrem-sales/pages__sales_shipment__invoice_created": "Sales invoice created", "@sage/xtrem-sales/pages__sales_shipment__invoice_exception": "The invoice creation failed: {{exception}}", "@sage/xtrem-sales/pages__sales_shipment__invoiceStatus____title": "Invoice status", "@sage/xtrem-sales/pages__sales_shipment__isExternalNote____title": "Add notes to customer document", "@sage/xtrem-sales/pages__sales_shipment__isExternalNoteLine____title": "Add notes to customer document", "@sage/xtrem-sales/pages__sales_shipment__isPrinted____title": "Printed", "@sage/xtrem-sales/pages__sales_shipment__isTransferHeaderNote____title": "Repeat the document notes on new documents.", "@sage/xtrem-sales/pages__sales_shipment__isTransferLineNote____title": "Repeat all the line notes on new documents.", "@sage/xtrem-sales/pages__sales_shipment__itemsSection____title": "Lines", "@sage/xtrem-sales/pages__sales_shipment__line_delete_action_dialog_content": "You are about to delete this sales shipment line. This action cannot be undone.", "@sage/xtrem-sales/pages__sales_shipment__line_delete_action_dialog_title": "Confirm delete", "@sage/xtrem-sales/pages__sales_shipment__lines____columns__columns__item__name__title": "Category", "@sage/xtrem-sales/pages__sales_shipment__lines____columns__columns__item__name__title__2": "Expiration managed", "@sage/xtrem-sales/pages__sales_shipment__lines____columns__columns__stockUnit__name__title": "Symbol", "@sage/xtrem-sales/pages__sales_shipment__lines____columns__columns__unit__name__title": "Symbol", "@sage/xtrem-sales/pages__sales_shipment__lines____columns__postfix__charge": "%", "@sage/xtrem-sales/pages__sales_shipment__lines____columns__postfix__discount": "%", "@sage/xtrem-sales/pages__sales_shipment__lines____columns__title__allocationStatus": "Allocation status", "@sage/xtrem-sales/pages__sales_shipment__lines____columns__title__charge": "Charge", "@sage/xtrem-sales/pages__sales_shipment__lines____columns__title__customerNumber": "Customer order reference", "@sage/xtrem-sales/pages__sales_shipment__lines____columns__title__discount": "Discount", "@sage/xtrem-sales/pages__sales_shipment__lines____columns__title__invoiceStatus": "Invoice status", "@sage/xtrem-sales/pages__sales_shipment__lines____columns__title__isExternalNote": "Add notes to customer document", "@sage/xtrem-sales/pages__sales_shipment__lines____columns__title__item__id": "Item ID", "@sage/xtrem-sales/pages__sales_shipment__lines____columns__title__item__name": "<PERSON><PERSON>", "@sage/xtrem-sales/pages__sales_shipment__lines____columns__title__itemDescription": "Item description", "@sage/xtrem-sales/pages__sales_shipment__lines____columns__title__origin": "Origin", "@sage/xtrem-sales/pages__sales_shipment__lines____columns__title__priceOrigin": "Price origin", "@sage/xtrem-sales/pages__sales_shipment__lines____columns__title__quantity": "Quantity in sales unit", "@sage/xtrem-sales/pages__sales_shipment__lines____columns__title__quantityAllocated": "Allocated quantity", "@sage/xtrem-sales/pages__sales_shipment__lines____columns__title__quantityInStockUnit": "Quantity in stock unit", "@sage/xtrem-sales/pages__sales_shipment__lines____columns__title__quantityInvoicedInProgressInSalesUnit": "In Progress quantity", "@sage/xtrem-sales/pages__sales_shipment__lines____columns__title__quantityInvoicedPostedInSalesUnit": "Invoiced quantity", "@sage/xtrem-sales/pages__sales_shipment__lines____columns__title__quantityReceiptInSalesUnit": "Receipt quantity", "@sage/xtrem-sales/pages__sales_shipment__lines____columns__title__quantityRequestedInSalesUnit": "Requested quantity", "@sage/xtrem-sales/pages__sales_shipment__lines____columns__title__remainingQuantity": "Remaining quantity", "@sage/xtrem-sales/pages__sales_shipment__lines____columns__title__remainingQuantityToAllocate": "Remaining quantity to allocate", "@sage/xtrem-sales/pages__sales_shipment__lines____columns__title__returnReceiptStatus": "Return receipt status", "@sage/xtrem-sales/pages__sales_shipment__lines____columns__title__returnRequestStatus": "Return request status", "@sage/xtrem-sales/pages__sales_shipment__lines____columns__title__status": "Status", "@sage/xtrem-sales/pages__sales_shipment__lines____columns__title__stockTransactionStatus": "Stock status", "@sage/xtrem-sales/pages__sales_shipment__lines____columns__title__stockUnit__name": "Stock unit", "@sage/xtrem-sales/pages__sales_shipment__lines____columns__title__unit__name": "Sales unit", "@sage/xtrem-sales/pages__sales_shipment__lines____columns__title__unitToStockUnitConversionFactor": "Stock unit conversion factor", "@sage/xtrem-sales/pages__sales_shipment__lines____dropdownActions__title": "Allocate stock", "@sage/xtrem-sales/pages__sales_shipment__lines____dropdownActions__title__2": "Issued stock", "@sage/xtrem-sales/pages__sales_shipment__lines____dropdownActions__title__3": "Manage allocations", "@sage/xtrem-sales/pages__sales_shipment__lines____dropdownActions__title__4": "Dimensions", "@sage/xtrem-sales/pages__sales_shipment__lines____dropdownActions__title__5": "Delete", "@sage/xtrem-sales/pages__sales_shipment__lines____inlineActions__title": "Open line panel", "@sage/xtrem-sales/pages__sales_shipment__lines____mobileCard__line2__title": "Description", "@sage/xtrem-sales/pages__sales_shipment__lines____mobileCard__title__title": "Product", "@sage/xtrem-sales/pages__sales_shipment__lines____mobileCard__titleRight__title": "Status", "@sage/xtrem-sales/pages__sales_shipment__lines____optionsMenu__title": "All statuses", "@sage/xtrem-sales/pages__sales_shipment__lines____optionsMenu__title__2": "Allocation required", "@sage/xtrem-sales/pages__sales_shipment__lines____sidebar__headerDropdownActions__title": "Issued stock", "@sage/xtrem-sales/pages__sales_shipment__lines____sidebar__headerDropdownActions__title__2": "Manage allocations", "@sage/xtrem-sales/pages__sales_shipment__lines____sidebar__headerDropdownActions__title__3": "Dimensions", "@sage/xtrem-sales/pages__sales_shipment__lines____sidebar__headerDropdownActions__title__4": "Delete", "@sage/xtrem-sales/pages__sales_shipment__lines____title": "Lines", "@sage/xtrem-sales/pages__sales_shipment__newOnOrderQuantity____title": "New quantity", "@sage/xtrem-sales/pages__sales_shipment__noteBlock____title": "Notes", "@sage/xtrem-sales/pages__sales_shipment__notesSection____title": "Notes", "@sage/xtrem-sales/pages__sales_shipment__number____title": "Number", "@sage/xtrem-sales/pages__sales_shipment__on_shipment_quantity_changed_confirmation": "Update the quantity from the related shipment line?", "@sage/xtrem-sales/pages__sales_shipment__on_shipment_quantity_changed_confirmation_title": "Update shipment line quantity confirmation?", "@sage/xtrem-sales/pages__sales_shipment__on_shipment_quantity_changed_error": "The new quantity cannot be greater than the actual quantity", "@sage/xtrem-sales/pages__sales_shipment__on_shipment_quantity_changed_error_title": "Error on quantity", "@sage/xtrem-sales/pages__sales_shipment__onOrderQuantityChangeBlock____title": "", "@sage/xtrem-sales/pages__sales_shipment__onOrderQuantityChangeSection____title": "On order quantity change", "@sage/xtrem-sales/pages__sales_shipment__paymentTerm____lookupDialogTitle": "Select payment term", "@sage/xtrem-sales/pages__sales_shipment__paymentTerm____title": "Payment term", "@sage/xtrem-sales/pages__sales_shipment__post____title": "Post stock", "@sage/xtrem-sales/pages__sales_shipment__post_action_dialog_content": "You are about to set this sales shipment to 'Shipped'.", "@sage/xtrem-sales/pages__sales_shipment__post_action_dialog_title": "Confirm posting", "@sage/xtrem-sales/pages__sales_shipment__posted": "The sales shipment has been posted.", "@sage/xtrem-sales/pages__sales_shipment__postingDetails____columns__title__financeIntegrationAppRecordId": "Accounting integration reference", "@sage/xtrem-sales/pages__sales_shipment__postingDetails____columns__title__postingStatus": "Status", "@sage/xtrem-sales/pages__sales_shipment__postingDetails____columns__title__targetDocumentNumber": "Document number", "@sage/xtrem-sales/pages__sales_shipment__postingDetails____columns__title__targetDocumentType": "Document type", "@sage/xtrem-sales/pages__sales_shipment__postingDetails____title": "Results", "@sage/xtrem-sales/pages__sales_shipment__postingMessageBlock____title": "Error detail", "@sage/xtrem-sales/pages__sales_shipment__postingSection____title": "Posting", "@sage/xtrem-sales/pages__sales_shipment__print____title": "Print", "@sage/xtrem-sales/pages__sales_shipment__printed": "The sales shipment has been printed.", "@sage/xtrem-sales/pages__sales_shipment__reference____title": "Reference", "@sage/xtrem-sales/pages__sales_shipment__remainingOnOrderQuantity____title": "Remaining quantity", "@sage/xtrem-sales/pages__sales_shipment__repost____title": "Repost", "@sage/xtrem-sales/pages__sales_shipment__repost_errors": "Errors while reposting:", "@sage/xtrem-sales/pages__sales_shipment__return_request_created": "Sales return request created", "@sage/xtrem-sales/pages__sales_shipment__return_request_exception": "The return request creation failed: {{exception}}", "@sage/xtrem-sales/pages__sales_shipment__returnReceiptStatus____title": "Return receipt status", "@sage/xtrem-sales/pages__sales_shipment__returnRequestStatus____title": "Return request status", "@sage/xtrem-sales/pages__sales_shipment__revert____title": "<PERSON><PERSON>", "@sage/xtrem-sales/pages__sales_shipment__revert_action_dialog_content": "You are about to revert the status of this sales shipment to 'Ready to process'.", "@sage/xtrem-sales/pages__sales_shipment__revert_action_dialog_title": "Confirm status reversal", "@sage/xtrem-sales/pages__sales_shipment__salesLinkedDocuments____columns__title___id": "Origin document number", "@sage/xtrem-sales/pages__sales_shipment__salesLinkedDocuments____columns__title__customerNumber": "Customer order reference", "@sage/xtrem-sales/pages__sales_shipment__salesLinkedDocuments____columns__title__displayStatus": "Status", "@sage/xtrem-sales/pages__sales_shipment__salesLinkedDocuments____columns__title__documentType": "Document type", "@sage/xtrem-sales/pages__sales_shipment__salesLinkedDocuments____title": "Order", "@sage/xtrem-sales/pages__sales_shipment__salesOrderLineCount____title": "Number of items", "@sage/xtrem-sales/pages__sales_shipment__salesOrderLineToSalesShipmentLines____columns__columns__document___id__columns__title__symbol": "Symbol", "@sage/xtrem-sales/pages__sales_shipment__salesOrderLineToSalesShipmentLines____columns__columns__document___id__title": "Sales unit", "@sage/xtrem-sales/pages__sales_shipment__salesOrderLineToSalesShipmentLines____columns__columns__linkedDocument___id__columns__title__category__name": "Category", "@sage/xtrem-sales/pages__sales_shipment__salesOrderLineToSalesShipmentLines____columns__columns__linkedDocument___id__title": "<PERSON><PERSON>", "@sage/xtrem-sales/pages__sales_shipment__salesOrderLineToSalesShipmentLines____columns__columns__linkedDocument___id__title__2": "Shipping date", "@sage/xtrem-sales/pages__sales_shipment__salesOrderLineToSalesShipmentLines____columns__title__document___id": "Unit", "@sage/xtrem-sales/pages__sales_shipment__salesOrderLineToSalesShipmentLines____columns__title__linkedDocument___id": "Sales order line", "@sage/xtrem-sales/pages__sales_shipment__salesOrderLineToSalesShipmentLines____columns__title__quantity": "Quantity to ship", "@sage/xtrem-sales/pages__sales_shipment__salesOrderLineToSalesShipmentLines____dropdownActions__title": "Quantities", "@sage/xtrem-sales/pages__sales_shipment__salesOrderLineToSalesShipmentLines____mobileCard__image__title": "Image", "@sage/xtrem-sales/pages__sales_shipment__salesOrderLineToSalesShipmentLines____mobileCard__line2__title": "Shipping date", "@sage/xtrem-sales/pages__sales_shipment__salesOrderLineToSalesShipmentLines____mobileCard__line2Right__title": "Quantity to ship", "@sage/xtrem-sales/pages__sales_shipment__salesOrderLineToSalesShipmentLines____mobileCard__title__title": "Sales order", "@sage/xtrem-sales/pages__sales_shipment__salesOrderLineToSalesShipmentLines____mobileCard__titleRight__title": "<PERSON><PERSON>", "@sage/xtrem-sales/pages__sales_shipment__salesOrderLineToSalesShipmentLines____title": "Sales order lines - ordering in progress", "@sage/xtrem-sales/pages__sales_shipment__salesReturnReceiptLines____columns__title___id": "Return receipt number", "@sage/xtrem-sales/pages__sales_shipment__salesReturnReceiptLines____columns__title___id__2": "Return receipt status", "@sage/xtrem-sales/pages__sales_shipment__salesReturnReceiptLines____columns__title__quantity": "Quantity in sales unit", "@sage/xtrem-sales/pages__sales_shipment__salesReturnReceiptLines____title": "Receipt lines", "@sage/xtrem-sales/pages__sales_shipment__selectFromSalesOrderLines____title": "Add lines from orders", "@sage/xtrem-sales/pages__sales_shipment__selection_impossible_because_on_hold_bill_to_customer": "The sales order lines cannot be selected: The Bill-to customer is on hold.", "@sage/xtrem-sales/pages__sales_shipment__shippingBlock____title": "Shipping", "@sage/xtrem-sales/pages__sales_shipment__shippingSection____title": "Shipping", "@sage/xtrem-sales/pages__sales_shipment__shipToAddress____columns__columns__country__title": "ISO 3166-1 alpha-2 code", "@sage/xtrem-sales/pages__sales_shipment__shipToAddress____columns__title__addressLine1": "Address line 1", "@sage/xtrem-sales/pages__sales_shipment__shipToAddress____columns__title__addressLine2": "Address line 2", "@sage/xtrem-sales/pages__sales_shipment__shipToAddress____columns__title__city": "City", "@sage/xtrem-sales/pages__sales_shipment__shipToAddress____columns__title__concatenatedAddress": "Bill-to address", "@sage/xtrem-sales/pages__sales_shipment__shipToAddress____columns__title__locationPhoneNumber": "Phone number", "@sage/xtrem-sales/pages__sales_shipment__shipToAddress____columns__title__name": "Name", "@sage/xtrem-sales/pages__sales_shipment__shipToAddress____dropdownActions__title": "Replace", "@sage/xtrem-sales/pages__sales_shipment__shipToAddress____dropdownActions__title__2": "Edit", "@sage/xtrem-sales/pages__sales_shipment__shipToAddress____dropdownActions__title__3": "Read-only", "@sage/xtrem-sales/pages__sales_shipment__shipToAddress____title": "Ship-to address", "@sage/xtrem-sales/pages__sales_shipment__shipToCustomer____columns__title__businessEntity__country__name": "Country", "@sage/xtrem-sales/pages__sales_shipment__shipToCustomer____columns__title__businessEntity__id": "ID", "@sage/xtrem-sales/pages__sales_shipment__shipToCustomer____columns__title__businessEntity__name": "Name", "@sage/xtrem-sales/pages__sales_shipment__shipToCustomer____columns__title__businessEntity__taxIdNumber": "Tax ID", "@sage/xtrem-sales/pages__sales_shipment__shipToCustomer____lookupDialogTitle": "Select ship-to customer", "@sage/xtrem-sales/pages__sales_shipment__shipToCustomer____title": "Ship-to customer", "@sage/xtrem-sales/pages__sales_shipment__shipToCustomerAddress____columns__columns__country__title": "ISO 3166-1 alpha-2 code", "@sage/xtrem-sales/pages__sales_shipment__shipToCustomerAddress____columns__columns__deliveryDetail___id__columns__title__legalCompany__name": "Company", "@sage/xtrem-sales/pages__sales_shipment__shipToCustomerAddress____columns__columns__deliveryDetail___id__postfix": "day(s)", "@sage/xtrem-sales/pages__sales_shipment__shipToCustomerAddress____columns__columns__deliveryDetail___id__title": "Incoterms® rule", "@sage/xtrem-sales/pages__sales_shipment__shipToCustomerAddress____columns__columns__deliveryDetail___id__title__2": "Delivery mode", "@sage/xtrem-sales/pages__sales_shipment__shipToCustomerAddress____columns__columns__deliveryDetail___id__title__3": "Delivery lead time", "@sage/xtrem-sales/pages__sales_shipment__shipToCustomerAddress____columns__columns__deliveryDetail___id__title__4": "Stock site", "@sage/xtrem-sales/pages__sales_shipment__shipToCustomerAddress____columns__title__addressLine1": "Address line 1", "@sage/xtrem-sales/pages__sales_shipment__shipToCustomerAddress____columns__title__addressLine2": "Address line 2", "@sage/xtrem-sales/pages__sales_shipment__shipToCustomerAddress____columns__title__city": "City", "@sage/xtrem-sales/pages__sales_shipment__shipToCustomerAddress____columns__title__deliveryDetail___id": "Ship-to address", "@sage/xtrem-sales/pages__sales_shipment__shipToCustomerAddress____columns__title__deliveryDetail__isPrimary": "Primary ship-to address", "@sage/xtrem-sales/pages__sales_shipment__shipToCustomerAddress____columns__title__isActive": "Active", "@sage/xtrem-sales/pages__sales_shipment__shipToCustomerAddress____columns__title__locationPhoneNumber": "Phone number", "@sage/xtrem-sales/pages__sales_shipment__shipToCustomerAddress____columns__title__postcode": "ZIP code", "@sage/xtrem-sales/pages__sales_shipment__shipToCustomerAddress____columns__title__region": "Region", "@sage/xtrem-sales/pages__sales_shipment__shipToCustomerAddress____lookupDialogTitle": "Select ship-to address", "@sage/xtrem-sales/pages__sales_shipment__shipToCustomerAddress____title": "Ship-to address", "@sage/xtrem-sales/pages__sales_shipment__site____columns__title__legalCompany__name": "Company", "@sage/xtrem-sales/pages__sales_shipment__site____lookupDialogTitle": "Select site", "@sage/xtrem-sales/pages__sales_shipment__site____title": "Site", "@sage/xtrem-sales/pages__sales_shipment__status____title": "Status", "@sage/xtrem-sales/pages__sales_shipment__step_sequence_confirm": "Confirm", "@sage/xtrem-sales/pages__sales_shipment__step_sequence_creation": "Create", "@sage/xtrem-sales/pages__sales_shipment__step_sequence_invoice": "Invoice", "@sage/xtrem-sales/pages__sales_shipment__step_sequence_post": "Post", "@sage/xtrem-sales/pages__sales_shipment__step_sequence_return_receipt": "Receive return", "@sage/xtrem-sales/pages__sales_shipment__step_sequence_return_request": "Request return", "@sage/xtrem-sales/pages__sales_shipment__stockSite____columns__title__businessEntity__id": "ID", "@sage/xtrem-sales/pages__sales_shipment__stockSite____columns__title__businessEntity__name": "Name", "@sage/xtrem-sales/pages__sales_shipment__stockSite____columns__title__legalCompany__name": "Company", "@sage/xtrem-sales/pages__sales_shipment__stockSite____lookupDialogTitle": "Select stock site", "@sage/xtrem-sales/pages__sales_shipment__stockSite____title": "Stock site", "@sage/xtrem-sales/pages__sales_shipment__stockTransactionStatus____title": "Stock status", "@sage/xtrem-sales/pages__sales_shipment__toInvoiceLines____columns__title___id": "Invoice number", "@sage/xtrem-sales/pages__sales_shipment__toInvoiceLines____columns__title__document__status": "Invoice status", "@sage/xtrem-sales/pages__sales_shipment__toInvoiceLines____columns__title__netPrice": "Invoiced amount", "@sage/xtrem-sales/pages__sales_shipment__toInvoiceLines____columns__title__quantity": "Invoiced quantity in sales unit", "@sage/xtrem-sales/pages__sales_shipment__toInvoiceLines____title": "Invoice lines", "@sage/xtrem-sales/pages__sales_shipment__toReturnRequestLines____columns__title___id": "Return request number", "@sage/xtrem-sales/pages__sales_shipment__toReturnRequestLines____columns__title___id__2": "Return request status", "@sage/xtrem-sales/pages__sales_shipment__toReturnRequestLines____columns__title__quantity": "Quantity in sales unit", "@sage/xtrem-sales/pages__sales_shipment__toReturnRequestLines____title": "Return lines", "@sage/xtrem-sales/pages__sales_shipment__trackingNumber____title": "Tracking number", "@sage/xtrem-sales/pages__sales_shipment__unit____columns__title__symbol": "Symbol", "@sage/xtrem-sales/pages__sales_shipment__unit____lookupDialogTitle": "Select sales unit", "@sage/xtrem-sales/pages__sales_shipment__unit____title": "Sales unit", "@sage/xtrem-sales/pages__sales_shipment__workDays____title": "Work days", "@sage/xtrem-sales/pages__sales_shipment_invoicing_mass_process____title": "Mass invoice creation", "@sage/xtrem-sales/pages__sales_shipment_invoicing_mass_process__cancelDetailedButton____title": "Hide advanced selection", "@sage/xtrem-sales/pages__sales_shipment_invoicing_mass_process__chooseShipments____title": "Show advanced selection", "@sage/xtrem-sales/pages__sales_shipment_invoicing_mass_process__createSalesInvoicesFromShipmentLines____title": "Create", "@sage/xtrem-sales/pages__sales_shipment_invoicing_mass_process__creation__success": "{{num}} sales invoice was created successfully.", "@sage/xtrem-sales/pages__sales_shipment_invoicing_mass_process__creation__success_multi": "{{num}} sales invoices were created successfully.", "@sage/xtrem-sales/pages__sales_shipment_invoicing_mass_process__criteriaBlock____title": "Selection criteria", "@sage/xtrem-sales/pages__sales_shipment_invoicing_mass_process__fromBillToCustomer____columns__title__businessEntity__country__name": "Country", "@sage/xtrem-sales/pages__sales_shipment_invoicing_mass_process__fromBillToCustomer____columns__title__businessEntity__id": "ID", "@sage/xtrem-sales/pages__sales_shipment_invoicing_mass_process__fromBillToCustomer____columns__title__businessEntity__name": "Name", "@sage/xtrem-sales/pages__sales_shipment_invoicing_mass_process__fromBillToCustomer____columns__title__businessEntity__taxIdNumber": "Tax ID", "@sage/xtrem-sales/pages__sales_shipment_invoicing_mass_process__fromBillToCustomer____lookupDialogTitle": "Select from bill-to customer", "@sage/xtrem-sales/pages__sales_shipment_invoicing_mass_process__fromBillToCustomer____title": "From bill-to customer", "@sage/xtrem-sales/pages__sales_shipment_invoicing_mass_process__fromShipmentNumber____columns__title__billToCustomer": "Bill-to customer", "@sage/xtrem-sales/pages__sales_shipment_invoicing_mass_process__fromShipmentNumber____columns__title__number": "Shipment number", "@sage/xtrem-sales/pages__sales_shipment_invoicing_mass_process__fromShipmentNumber____columns__title__shippingDate": "Shipping date", "@sage/xtrem-sales/pages__sales_shipment_invoicing_mass_process__fromShipmentNumber____lookupDialogTitle": "Select from shipment number", "@sage/xtrem-sales/pages__sales_shipment_invoicing_mass_process__fromShipmentNumber____title": "From shipment number", "@sage/xtrem-sales/pages__sales_shipment_invoicing_mass_process__invoiceDate____title": "Invoice date", "@sage/xtrem-sales/pages__sales_shipment_invoicing_mass_process__mainBlock____title": "Advanced selection", "@sage/xtrem-sales/pages__sales_shipment_invoicing_mass_process__mainSection____title": "General", "@sage/xtrem-sales/pages__sales_shipment_invoicing_mass_process__no_shipment_meet_filtering_criteria": "No results found.", "@sage/xtrem-sales/pages__sales_shipment_invoicing_mass_process__results____columns__columns__currency__name__title": "ISO 4217 code", "@sage/xtrem-sales/pages__sales_shipment_invoicing_mass_process__results____columns__columns__currency__name__title__2": "Symbol", "@sage/xtrem-sales/pages__sales_shipment_invoicing_mass_process__results____columns__title__billToCustomer__businessEntity__name": "Bill-to customer", "@sage/xtrem-sales/pages__sales_shipment_invoicing_mass_process__results____columns__title__billToCustomer__id": "Bill-to customer ID", "@sage/xtrem-sales/pages__sales_shipment_invoicing_mass_process__results____columns__title__billToLinkedAddress__name": "Bill-to address", "@sage/xtrem-sales/pages__sales_shipment_invoicing_mass_process__results____columns__title__currency__name": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-sales/pages__sales_shipment_invoicing_mass_process__results____columns__title__isOnHold": "On hold", "@sage/xtrem-sales/pages__sales_shipment_invoicing_mass_process__results____columns__title__number": "Shipment number", "@sage/xtrem-sales/pages__sales_shipment_invoicing_mass_process__results____columns__title__paymentTerm__name": "Payment term", "@sage/xtrem-sales/pages__sales_shipment_invoicing_mass_process__results____columns__title__shippingDate": "Shipping date", "@sage/xtrem-sales/pages__sales_shipment_invoicing_mass_process__results____columns__title__totalAmountExcludingTax": "Total excluding tax", "@sage/xtrem-sales/pages__sales_shipment_invoicing_mass_process__results____title": "Results", "@sage/xtrem-sales/pages__sales_shipment_invoicing_mass_process__salesCompany____lookupDialogTitle": "Select company", "@sage/xtrem-sales/pages__sales_shipment_invoicing_mass_process__salesCompany____placeholder": "Select a company", "@sage/xtrem-sales/pages__sales_shipment_invoicing_mass_process__salesCompany____title": "Company", "@sage/xtrem-sales/pages__sales_shipment_invoicing_mass_process__selectAllCheckbox____title": "Select all", "@sage/xtrem-sales/pages__sales_shipment_invoicing_mass_process__shipmentUntilDate____title": "Shipment-until date", "@sage/xtrem-sales/pages__sales_shipment_invoicing_mass_process__site____columns__title__legalCompany__name": "Company", "@sage/xtrem-sales/pages__sales_shipment_invoicing_mass_process__site____lookupDialogTitle": "Select site", "@sage/xtrem-sales/pages__sales_shipment_invoicing_mass_process__site____placeholder": "Select a site", "@sage/xtrem-sales/pages__sales_shipment_invoicing_mass_process__site____title": "Site", "@sage/xtrem-sales/pages__sales_shipment_invoicing_mass_process__toBillToCustomer____columns__title__businessEntity__id": "ID", "@sage/xtrem-sales/pages__sales_shipment_invoicing_mass_process__toBillToCustomer____columns__title__businessEntity__name": "Name", "@sage/xtrem-sales/pages__sales_shipment_invoicing_mass_process__toBillToCustomer____columns__title__businessEntity__taxIdNumber": "Tax ID", "@sage/xtrem-sales/pages__sales_shipment_invoicing_mass_process__toBillToCustomer____lookupDialogTitle": "Select to bill-to customer", "@sage/xtrem-sales/pages__sales_shipment_invoicing_mass_process__toBillToCustomer____title": "To bill-to customer", "@sage/xtrem-sales/pages__sales_shipment_invoicing_mass_process__toShipmentNumber____columns__title__billToCustomer": "Bill-to customer", "@sage/xtrem-sales/pages__sales_shipment_invoicing_mass_process__toShipmentNumber____columns__title__number": "Shipment number", "@sage/xtrem-sales/pages__sales_shipment_invoicing_mass_process__toShipmentNumber____columns__title__shippingDate": "Shipping date", "@sage/xtrem-sales/pages__sales_shipment_invoicing_mass_process__toShipmentNumber____lookupDialogTitle": "Select to shipment number", "@sage/xtrem-sales/pages__sales_shipment_invoicing_mass_process__toShipmentNumber____title": "To shipment number", "@sage/xtrem-sales/pages__sales_shipment_line__cannot_delete_line_quantity_allocated": "Remove the stock allocation before deleting the line.", "@sage/xtrem-sales/pages__sales_shipment_line_panel__dimensions_button_text": "Dimensions", "@sage/xtrem-sales/pages__sales_shipment_post_on_hold_action_dialog_title": "Customer on hold", "@sage/xtrem-sales/pages__site_extension__control_PR_approval": "You need to approve or reject pending documents before disabling the approval process.", "@sage/xtrem-sales/pages__tax_calculation_failed_post_blocking": "You need to resolve tax calculation issues before posting.", "@sage/xtrem-sales/pages__tax_calculation_failed_print_blocking": "You need to resolve tax calculation issues before printing.", "@sage/xtrem-sales/pages__unbilled_account_receivable_inquiry____title": "Unbilled accounts receivable", "@sage/xtrem-sales/pages__unbilled_account_receivable_inquiry__asOfDate____title": "As of date", "@sage/xtrem-sales/pages__unbilled_account_receivable_inquiry__company____columns__title__id": "ID", "@sage/xtrem-sales/pages__unbilled_account_receivable_inquiry__company____columns__title__name": "Name", "@sage/xtrem-sales/pages__unbilled_account_receivable_inquiry__company____lookupDialogTitle": "Select company", "@sage/xtrem-sales/pages__unbilled_account_receivable_inquiry__company____placeholder": "Select...", "@sage/xtrem-sales/pages__unbilled_account_receivable_inquiry__company____title": "Company", "@sage/xtrem-sales/pages__unbilled_account_receivable_inquiry__criteriaBlock____title": "Criteria", "@sage/xtrem-sales/pages__unbilled_account_receivable_inquiry__executionDate____title": "Last run date", "@sage/xtrem-sales/pages__unbilled_account_receivable_inquiry__fromCustomer____columns__title__businessEntity__country__name": "Country", "@sage/xtrem-sales/pages__unbilled_account_receivable_inquiry__fromCustomer____columns__title__businessEntity__id": "ID", "@sage/xtrem-sales/pages__unbilled_account_receivable_inquiry__fromCustomer____columns__title__businessEntity__name": "Name", "@sage/xtrem-sales/pages__unbilled_account_receivable_inquiry__fromCustomer____columns__title__businessEntity__taxIdNumber": "Tax ID", "@sage/xtrem-sales/pages__unbilled_account_receivable_inquiry__fromCustomer____lookupDialogTitle": "Select from customer", "@sage/xtrem-sales/pages__unbilled_account_receivable_inquiry__lines____columns__title__account__id": "GSNI account", "@sage/xtrem-sales/pages__unbilled_account_receivable_inquiry__lines____columns__title__account__name": "Account name", "@sage/xtrem-sales/pages__unbilled_account_receivable_inquiry__lines____columns__title__accountItem__id": "Account item ID", "@sage/xtrem-sales/pages__unbilled_account_receivable_inquiry__lines____columns__title__accountItem__name": "Account item", "@sage/xtrem-sales/pages__unbilled_account_receivable_inquiry__lines____columns__title__company__id": "Company ID", "@sage/xtrem-sales/pages__unbilled_account_receivable_inquiry__lines____columns__title__company__name": "Company", "@sage/xtrem-sales/pages__unbilled_account_receivable_inquiry__lines____columns__title__companyCurrency__id": "Company currency ID", "@sage/xtrem-sales/pages__unbilled_account_receivable_inquiry__lines____columns__title__companyCurrency__name": "Company currency", "@sage/xtrem-sales/pages__unbilled_account_receivable_inquiry__lines____columns__title__currency__id": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-sales/pages__unbilled_account_receivable_inquiry__lines____columns__title__currency__id__2": "Currency ID", "@sage/xtrem-sales/pages__unbilled_account_receivable_inquiry__lines____columns__title__customer": "Bill-to customer", "@sage/xtrem-sales/pages__unbilled_account_receivable_inquiry__lines____columns__title__customer__businessEntity__id": "Bill-to customer ID", "@sage/xtrem-sales/pages__unbilled_account_receivable_inquiry__lines____columns__title__documentDate": "Document date", "@sage/xtrem-sales/pages__unbilled_account_receivable_inquiry__lines____columns__title__financialSite": "Financial site", "@sage/xtrem-sales/pages__unbilled_account_receivable_inquiry__lines____columns__title__financialSite__id": "Financial site ID", "@sage/xtrem-sales/pages__unbilled_account_receivable_inquiry__lines____columns__title__invoiceIssuableAmount": "Unbilled amount", "@sage/xtrem-sales/pages__unbilled_account_receivable_inquiry__lines____columns__title__invoiceIssuableAmountInCompanyCurrency": "Unbilled in company currency", "@sage/xtrem-sales/pages__unbilled_account_receivable_inquiry__lines____columns__title__invoiceIssuableAmountInCompanyCurrencyAtAsOfDate": "Unbilled in company currency per the As of date", "@sage/xtrem-sales/pages__unbilled_account_receivable_inquiry__lines____columns__title__invoiceIssuableQuantity": "Shipped not invoiced quantity", "@sage/xtrem-sales/pages__unbilled_account_receivable_inquiry__lines____columns__title__item__id": "Item ID", "@sage/xtrem-sales/pages__unbilled_account_receivable_inquiry__lines____columns__title__item__name": "<PERSON><PERSON>", "@sage/xtrem-sales/pages__unbilled_account_receivable_inquiry__lines____columns__title__netPrice": "Net price", "@sage/xtrem-sales/pages__unbilled_account_receivable_inquiry__lines____columns__title__quantity": "Shipped quantity", "@sage/xtrem-sales/pages__unbilled_account_receivable_inquiry__lines____columns__title__returnedQuantity": "Returned quantity", "@sage/xtrem-sales/pages__unbilled_account_receivable_inquiry__lines____columns__title__salesUnit__name": "Sales unit", "@sage/xtrem-sales/pages__unbilled_account_receivable_inquiry__lines____columns__title__shipmentInternalId": "Shipment number", "@sage/xtrem-sales/pages__unbilled_account_receivable_inquiry__lines____columns__title__shipToCustomer": "Ship-to customer", "@sage/xtrem-sales/pages__unbilled_account_receivable_inquiry__lines____columns__title__shipToCustomer__2": "Ship-to customer name", "@sage/xtrem-sales/pages__unbilled_account_receivable_inquiry__lines____columns__title__shipToCustomer__businessEntity__id": "Ship-to customer ID", "@sage/xtrem-sales/pages__unbilled_account_receivable_inquiry__lines____columns__title__site": "Stock site", "@sage/xtrem-sales/pages__unbilled_account_receivable_inquiry__lines____columns__title__site__id": "Stock site ID", "@sage/xtrem-sales/pages__unbilled_account_receivable_inquiry__lines____title": "Results", "@sage/xtrem-sales/pages__unbilled_account_receivable_inquiry__mainSection____title": "General", "@sage/xtrem-sales/pages__unbilled_account_receivable_inquiry__notification_inquiry_finished": "Unbilled accounts receivable finished.", "@sage/xtrem-sales/pages__unbilled_account_receivable_inquiry__notification_request_sent": "Unbilled accounts receivable request sent.", "@sage/xtrem-sales/pages__unbilled_account_receivable_inquiry__resultsSection____title": "Results", "@sage/xtrem-sales/pages__unbilled_account_receivable_inquiry__runUnbilledAccountReceivableInquiry____title": "Run", "@sage/xtrem-sales/pages__unbilled_account_receivable_inquiry__sites____columns__title__legalCompany__name": "Company", "@sage/xtrem-sales/pages__unbilled_account_receivable_inquiry__sites____lookupDialogTitle": "Select site", "@sage/xtrem-sales/pages__unbilled_account_receivable_inquiry__sites____placeholder": "Select site", "@sage/xtrem-sales/pages__unbilled_account_receivable_inquiry__sites____title": "Site", "@sage/xtrem-sales/pages__unbilled_account_receivable_inquiry__status____title": "Status", "@sage/xtrem-sales/pages__unbilled_account_receivable_inquiry__toCustomer____columns__title__businessEntity__country__name": "Country", "@sage/xtrem-sales/pages__unbilled_account_receivable_inquiry__toCustomer____columns__title__businessEntity__id": "ID", "@sage/xtrem-sales/pages__unbilled_account_receivable_inquiry__toCustomer____columns__title__businessEntity__name": "Name", "@sage/xtrem-sales/pages__unbilled_account_receivable_inquiry__toCustomer____columns__title__businessEntity__taxIdNumber": "Tax ID", "@sage/xtrem-sales/pages__unbilled_account_receivable_inquiry__toCustomer____lookupDialogTitle": "Select to customer", "@sage/xtrem-sales/pages__unbilled_account_receivable_inquiry__user____title": "User", "@sage/xtrem-sales/pages_confirm_assignment_sales_order_close_dialog_content": "When you close this sales order, the links to the supply orders remain.", "@sage/xtrem-sales/pages_confirm_assignment_sales_order_close_dialog_title": "Close sales order", "@sage/xtrem-sales/pages_confirm_assignment_sales_order_deletion_dialog_content": "When you delete this sales order, the links to the supply orders are also deleted.", "@sage/xtrem-sales/pages_confirm_assignment_sales_order_deletion_dialog_title": "Delete sales order", "@sage/xtrem-sales/pages_confirm_assignment_sales_order_line_allocate_dialog_content": "This sales order line is linked to a supply order. The stock allocation is performed from the supply order stock entry.", "@sage/xtrem-sales/pages_confirm_assignment_sales_order_line_allocate_dialog_title": "Allocate sales order line", "@sage/xtrem-sales/pages_confirm_assignment_sales_order_line_closing_dialog_content": "When you close this sales order line, the link to the supply order remains.", "@sage/xtrem-sales/pages_confirm_assignment_sales_order_line_closing_dialog_title": "Close sales order line", "@sage/xtrem-sales/pages_confirm_assignment_sales_order_line_deletion_dialog_content": "When you delete this sales order line, the link to the supply order is also deleted.", "@sage/xtrem-sales/pages_confirm_assignment_sales_order_line_deletion_dialog_title": "Delete sales order line", "@sage/xtrem-sales/pages_confirm_assignment_sales_order_line_quantity_changed_dialog_content": "When you decrease this sales order line quantity, the quantity decreases on the linked supply order.", "@sage/xtrem-sales/pages_confirm_assignment_sales_order_line_quantity_changed_dialog_title": "Decrease sales order line quantity", "@sage/xtrem-sales/pages_confirm_assignment_sales_order_line_ship_remaining_quantity_not_allocated_dialog_content": "This sales order line is linked to a released supply order. The remaining quantity to ship is not allocated.", "@sage/xtrem-sales/pages_confirm_assignment_sales_order_line_ship_remaining_quantity_not_allocated_dialog_title": "Ship sales order line", "@sage/xtrem-sales/pages_confirm_assignment_sales_order_line_ship_required_quantity_is_not_received": "This sales order line is linked to a supply order. The required quantity is not received.", "@sage/xtrem-sales/pages_confirm_assignment_sales_order_ship_remaining_quantity_not_allocated_dialog_content": "This sales order is linked to a released supply order. The remaining quantity to ship is not allocated.", "@sage/xtrem-sales/pages_confirm_assignment_sales_order_ship_remaining_quantity_not_allocated_dialog_title": "Ship sales order", "@sage/xtrem-sales/pages_confirm_assignment_sales_order_ship_required_quantity_is_not_received": "This sales order line is linked to a supply order. The required quantity is not received.", "@sage/xtrem-sales/pages_confirm_button": "Confirm", "@sage/xtrem-sales/pages_functions__sales_order_close_action_allowed_sales_order_is_closed": "The sales order is closed.", "@sage/xtrem-sales/pages_functions__sales_order_confirm_action_is_confirmed": "The sales order was confirmed.", "@sage/xtrem-sales/pages_functions__sales_order_confirm_action_not_allowed_incorrect_parameters": "The confirmation failed. The parameters are incorrect.", "@sage/xtrem-sales/pages_functions__sales_order_confirm_action_status_is_not_draft": "The confirmation failed. The sales order is not 'Quote'.", "@sage/xtrem-sales/pages_functions__sales_order_create_shipment_action_is_shipped": "Sales shipment created ({{numberOfShipments}})", "@sage/xtrem-sales/pages_functions__sales_order_line_close_action_allowed_header_closed": "The sales order is closed", "@sage/xtrem-sales/pages_functions__sales_order_line_close_action_allowed_line_closed": "The sales order line is closed.", "@sage/xtrem-sales/pages_functions__sales_order_line_close_action_not_allowed_incorrect_parameters": "The close action on the line is not allowed. The parameters are incorrect.", "@sage/xtrem-sales/pages_functions__sales_order_line_close_action_not_allowed_line_already_closed": "The close action on the line is not allowed. The sales order line is already closed.", "@sage/xtrem-sales/pages_functions__sales_order_line_open_action_allowed": "The sales order line is opened.", "@sage/xtrem-sales/pages_functions__sales_order_line_open_action_not_allowed_incorrect_parameters": "The open action on the line is not allowed. The parameters are incorrect.", "@sage/xtrem-sales/pages_functions__sales_order_line_open_action_not_allowed_sales_order_line_is_already_open": "The open action on the line is not allowed. The sales order line is already open.", "@sage/xtrem-sales/pages_functions__sales_order_line_open_action_not_allowed_sales_order_line_is_shipped": "The open action on the line is not allowed. The sales order line is shipped.", "@sage/xtrem-sales/pages_functions__sales_order_open_action_allowed_header_open": "The sales order is opened.", "@sage/xtrem-sales/pages_functions__sales_order_open_action_not_allowed_incorrect_parameters": "The open action on the line is not allowed. The parameters are incorrect.", "@sage/xtrem-sales/pages_functions__sales_order_open_action_not_allowed_the_sales_order_is_already_open": "The open action is not allowed. The sales order is already open", "@sage/xtrem-sales/pages_functions__sales_order_open_action_not_allowed_the_sales_order_is_fully_shipped": "The open action is not allowed. The sales order is fully shipped", "@sage/xtrem-sales/pages_functions__sales_return_request_close_action_allowed_sales_return_request_is_closed": "The sales return request is closed.", "@sage/xtrem-sales/pages_functions__sales_return_request_close_action_not_allowed_incorrect_parameters": "The close action on the line is not allowed. The parameters are incorrect.", "@sage/xtrem-sales/pages_functions__sales_return_request_close_action_not_allowed_receipt_not_closed": "The close action is not allowed. A linked return receipt is not closed", "@sage/xtrem-sales/pages_functions__sales_return_request_close_action_not_allowed_sales_return_request_is_already_closed": "The close action on the line is not allowed. The sales return request is already closed.", "@sage/xtrem-sales/pages_functions__sales_return_request_line_close_action_allowed_header_closed": "The sales return request is closed", "@sage/xtrem-sales/pages_functions__sales_return_request_line_close_action_allowed_line_closed": "The sales return request line is closed.", "@sage/xtrem-sales/pages_functions__sales_return_request_line_close_action_not_allowed_incorrect_parameters": "The close action on the line is not allowed. The parameters are incorrect.", "@sage/xtrem-sales/pages_functions__sales_return_request_line_close_action_not_allowed_line_already_closed": "The close action on the line is not allowed. The sales return request line is already closed.", "@sage/xtrem-sales/pages_functions__sales_return_request_line_close_action_not_allowed_receipt_not_closed": "The close action on the line is not allowed. The linked return receipt is not closed", "@sage/xtrem-sales/pages_functions__sales_return_request_line_open_action_allowed": "The sales return request line is opened.", "@sage/xtrem-sales/pages_functions__sales_return_request_line_open_action_not_allowed_incorrect_parameters": "The open action on the line is not allowed. The parameters are incorrect.", "@sage/xtrem-sales/pages_functions__sales_return_request_line_open_action_not_allowed_sales_return_request_line_is_already_open": "The open action on the line is not allowed. The sales return request line is already open.", "@sage/xtrem-sales/pages_functions__sales_return_request_line_open_action_not_allowed_sales_return_request_line_is_shipped": "The open action on the line is not allowed. The sales return request line is received.", "@sage/xtrem-sales/pages_functions__sales_return_request_open_action_allowed_header_open": "The sales return request is opened.", "@sage/xtrem-sales/pages_functions__sales_return_request_open_action_not_allowed_incorrect_parameters": "The open action on the line is not allowed. The parameters are incorrect.", "@sage/xtrem-sales/pages_functions__sales_return_request_open_action_not_allowed_the_sales_return_request_is_already_open": "The open action is not allowed. The sales return request is already open", "@sage/xtrem-sales/pages_functions__sales_return_request_open_action_not_allowed_the_sales_return_request_is_fully_received": "The open action is not allowed. The sales return request is fully received", "@sage/xtrem-sales/pages_sidebar_block_title_allocation": "Allocation", "@sage/xtrem-sales/pages_sidebar_block_title_credit_memo": "Credit memo", "@sage/xtrem-sales/pages_sidebar_block_title_delivery": "Delivery", "@sage/xtrem-sales/pages_sidebar_block_title_invoice": "Invoice", "@sage/xtrem-sales/pages_sidebar_block_title_return_receipt": "Return receipt", "@sage/xtrem-sales/pages_sidebar_block_title_return_request": "Return request", "@sage/xtrem-sales/pages_sidebar_block_title_sales": "Sales", "@sage/xtrem-sales/pages_sidebar_block_title_shipment": "Shipment", "@sage/xtrem-sales/pages_sidebar_block_title_stock": "Stock", "@sage/xtrem-sales/pages_sidebar_block_title_totals": "Totals", "@sage/xtrem-sales/pages_sidebar_tab_title_address": "Address", "@sage/xtrem-sales/pages_sidebar_tab_title_delivery": "Delivery", "@sage/xtrem-sales/pages_sidebar_tab_title_information": "Information", "@sage/xtrem-sales/pages_sidebar_tab_title_line_notes": "Line notes", "@sage/xtrem-sales/pages_sidebar_tab_title_origin": "Origin", "@sage/xtrem-sales/pages_sidebar_tab_title_price": "Price", "@sage/xtrem-sales/pages_sidebar_tab_title_progress": "Progress", "@sage/xtrem-sales/pages_sidebar_tab_title_stock": "Stock", "@sage/xtrem-sales/pages-allocate_stock_confirm": "Confirm", "@sage/xtrem-sales/pages-assigned_order_confirm": "Confirm", "@sage/xtrem-sales/pages-confirm-approve": "Approve", "@sage/xtrem-sales/pages-confirm-change": "Confirm", "@sage/xtrem-sales/pages-confirm-confirm": "Confirm", "@sage/xtrem-sales/pages-confirm-continue": "Continue", "@sage/xtrem-sales/pages-confirm-create": "Create", "@sage/xtrem-sales/pages-confirm-create_on_hold": "Create", "@sage/xtrem-sales/pages-confirm-no": "No", "@sage/xtrem-sales/pages-confirm-open": "Open", "@sage/xtrem-sales/pages-confirm-post": "Post", "@sage/xtrem-sales/pages-confirm-reject": "Reject", "@sage/xtrem-sales/pages-confirm-send": "Send", "@sage/xtrem-sales/pages-confirm-update": "Update", "@sage/xtrem-sales/pages-confirm-yes": "Yes", "@sage/xtrem-sales/pages-sales-order-dialog-assignment-cancel": "Cancel", "@sage/xtrem-sales/pages-sales-order-dialog-assignment-continue": "Continue", "@sage/xtrem-sales/pages-save-continue": "Continue", "@sage/xtrem-sales/pages-wo_po_order_confirm": "Confirm", "@sage/xtrem-sales/permission__approval__name": "Approval", "@sage/xtrem-sales/permission__change_status__name": "Change status", "@sage/xtrem-sales/permission__confirm__name": "Confirm", "@sage/xtrem-sales/permission__manage__name": "Manage", "@sage/xtrem-sales/permission__post__name": "Post", "@sage/xtrem-sales/permission__print__name": "Print", "@sage/xtrem-sales/permission__read__name": "Read", "@sage/xtrem-sales/permission__send_approval_request_mail__name": "Send approval request mail", "@sage/xtrem-sales/price_determination__on_price_determination_title": "Search price", "@sage/xtrem-sales/return-request-to-receipt__confirm_greater_quantity": "You cannot receive a quantity more than the return request you select.", "@sage/xtrem-sales/return-request-to-receipt__zero_quantity_error": "You cannot enter a quantity less than or equal to zero.", "@sage/xtrem-sales/sales_credit_memo__email_subject": "{{creditMemoSiteName}}: Credit memo {{creditMemoNumber}}", "@sage/xtrem-sales/sales_invoice__email_subject": "{{invoiceSiteName}}: Invoice {{invoiceNumber}}", "@sage/xtrem-sales/sales_order__email_subject": "{{salesOrderSiteName}}: Order {{salesOrderNumber}} confirmation", "@sage/xtrem-sales/sales_order_email_file_prefix": "Sales order - {{salesOrderNumber}}", "@sage/xtrem-sales/sales_order_line__quantity_bellow_already_shipped_quantity": "The sales order line quantity cannot be lower than the quantity already shipped.", "@sage/xtrem-sales/sales_order_quote_email_file_prefix": "Sales quote - {{salesOrderNumber}}", "@sage/xtrem-sales/sales_order_quote_email_subject": "{{salesOrderSiteName}}: Sales quote {{salesOrderNumber}}", "@sage/xtrem-sales/sales-invoice-line-inquiry": "Enter the company.", "@sage/xtrem-sales/search": "Search", "@sage/xtrem-sales/shipment__error_zero_or_negative": "You need to enter a quantity greater than zero.", "@sage/xtrem-sales/status_and_is_purchase_requisition_approval_managed": "Document status needs to be \"Draft\" and approval workflow disabled", "@sage/xtrem-sales/widgets__customer_indicator_group____title": "Customer KPIs", "@sage/xtrem-sales/widgets__customer_kpi_credit_limit_title": "CREDIT LIMIT", "@sage/xtrem-sales/widgets__customer_kpi_gross_profit_title": "GROSS PROFIT", "@sage/xtrem-sales/widgets__customer_kpi_last_order_title": "LAST ORDER", "@sage/xtrem-sales/widgets__customer_kpi_order_book_title": "ORDER BOOK", "@sage/xtrem-sales/widgets__customer_kpi_total_invoiced_title": "TOTAL INVOICED", "@sage/xtrem-sales/widgets__customers_on_hold____headerActions__title": "View list", "@sage/xtrem-sales/widgets__customers_on_hold____title": "Customers on hold", "@sage/xtrem-sales/widgets__deal_size_trend____title": "Deal size trend", "@sage/xtrem-sales/widgets__mtd_sales____title": "MTD sales", "@sage/xtrem-sales/widgets__new_customers____title": "New customers", "@sage/xtrem-sales/widgets__open_orders____callToActions__Ship__title": "Ship", "@sage/xtrem-sales/widgets__open_orders____dataDropdownMenu__orderBy__date__title": "Sort by order date", "@sage/xtrem-sales/widgets__open_orders____dataDropdownMenu__orderBy__number__title": "Sort by number", "@sage/xtrem-sales/widgets__open_orders____dataDropdownMenu__orderBy__status__title": "Sort by status", "@sage/xtrem-sales/widgets__open_orders____rowDefinition__line2__title": "Total amount", "@sage/xtrem-sales/widgets__open_orders____rowDefinition__line2Right__title": "Status", "@sage/xtrem-sales/widgets__open_orders____rowDefinition__line3__title": "Customer name", "@sage/xtrem-sales/widgets__open_orders____rowDefinition__title__title": "Number", "@sage/xtrem-sales/widgets__open_orders____rowDefinition__titleRight__title": "Order date", "@sage/xtrem-sales/widgets__open_orders____title": "Orders to deliver", "@sage/xtrem-sales/widgets__open_orders__create_shipment_multiple_companies": "Selected orders must come from a single sales company.", "@sage/xtrem-sales/widgets__open_orders__create_shipment_no_shippable_orders": "Selected orders cannot be shipped.", "@sage/xtrem-sales/widgets__open_sales_orders____callToActions__createOrder__title": "Create order", "@sage/xtrem-sales/widgets__open_sales_orders____callToActions__selectedData__title": "See more", "@sage/xtrem-sales/widgets__open_sales_orders____dataDropdownMenu__userType__late__title": "Late sales orders", "@sage/xtrem-sales/widgets__open_sales_orders____dataDropdownMenu__userType__onTime__title": "On-time sales orders", "@sage/xtrem-sales/widgets__open_sales_orders____dataDropdownMenu__userType__open__title": "Open sales orders", "@sage/xtrem-sales/widgets__open_sales_orders____rowActions__title": "Confirm", "@sage/xtrem-sales/widgets__open_sales_orders____rowActions__title__10": "Delete", "@sage/xtrem-sales/widgets__open_sales_orders____rowActions__title__2": "Create shipment", "@sage/xtrem-sales/widgets__open_sales_orders____rowActions__title__3": "Close order", "@sage/xtrem-sales/widgets__open_sales_orders____rowActions__title__4": "Open order", "@sage/xtrem-sales/widgets__open_sales_orders____rowActions__title__5": "Print", "@sage/xtrem-sales/widgets__open_sales_orders____rowActions__title__6": "Send", "@sage/xtrem-sales/widgets__open_sales_orders____rowActions__title__7": "Allocate stock", "@sage/xtrem-sales/widgets__open_sales_orders____rowActions__title__8": "Deallocate stock", "@sage/xtrem-sales/widgets__open_sales_orders____rowActions__title__9": "Create proforma invoice", "@sage/xtrem-sales/widgets__open_sales_orders____rowDefinition__displayStatus__title": "Status", "@sage/xtrem-sales/widgets__open_sales_orders____rowDefinition__expectedDeliveryDate__title": "Delivery date", "@sage/xtrem-sales/widgets__open_sales_orders____rowDefinition__shippingDate__title": "Shipping date", "@sage/xtrem-sales/widgets__open_sales_orders____rowDefinition__site__title": "Site", "@sage/xtrem-sales/widgets__open_sales_orders____rowDefinition__title__title": "Number", "@sage/xtrem-sales/widgets__open_sales_orders____rowDefinition__titleRight__title": "Date", "@sage/xtrem-sales/widgets__open_sales_orders____rowDefinition__totalAmount__title": "Amount", "@sage/xtrem-sales/widgets__open_sales_orders____rowDefinition__totalGrossProfit__title": "Gross profit", "@sage/xtrem-sales/widgets__open_sales_orders____title": "In progress sales orders", "@sage/xtrem-sales/widgets__return_reason_analysis____rowDefinition__line2__title": "Reason name", "@sage/xtrem-sales/widgets__return_reason_analysis____rowDefinition__line2Right__title": "", "@sage/xtrem-sales/widgets__return_reason_analysis____rowDefinition__title__title": "", "@sage/xtrem-sales/widgets__return_reason_analysis____rowDefinition__titleRight__title": "Returns", "@sage/xtrem-sales/widgets__return_reason_analysis____title": "Return reason analysis", "@sage/xtrem-sales/widgets__sales_visual_process____title": "Sales process", "@sage/xtrem-sales/widgets__shipment_to_invoice__create_invoices_no_invoiceable_orders": "Selected invoices cannot be invoiced.", "@sage/xtrem-sales/widgets__shipments_to_invoice____callToActions__Ship__title": "Invoice", "@sage/xtrem-sales/widgets__shipments_to_invoice____dataDropdownMenu__orderBy__number__title": "Sort by number", "@sage/xtrem-sales/widgets__shipments_to_invoice____dataDropdownMenu__orderBy__shippedStatus__title": "Sort by shipped status", "@sage/xtrem-sales/widgets__shipments_to_invoice____dataDropdownMenu__orderBy__shippingDate__title": "Sort by shipping date", "@sage/xtrem-sales/widgets__shipments_to_invoice____rowDefinition__line2__title": "Total amount", "@sage/xtrem-sales/widgets__shipments_to_invoice____rowDefinition__line2Right__title": "Shipped status", "@sage/xtrem-sales/widgets__shipments_to_invoice____rowDefinition__line3__title": "Customer name", "@sage/xtrem-sales/widgets__shipments_to_invoice____rowDefinition__title__title": "Number", "@sage/xtrem-sales/widgets__shipments_to_invoice____rowDefinition__titleRight__title": "Shipping date", "@sage/xtrem-sales/widgets__shipments_to_invoice____title": "Shipments to invoice", "@sage/xtrem-sales/widgets__unposted_credits____callToActions__seeAll__title": "See all", "@sage/xtrem-sales/widgets__unposted_credits____dataDropdownMenu__orderBy__customerName__title": "Sort by customer name", "@sage/xtrem-sales/widgets__unposted_credits____dataDropdownMenu__orderBy__date__title": "Sort by credit memo date", "@sage/xtrem-sales/widgets__unposted_credits____dataDropdownMenu__orderBy__number__title": "Sort by number", "@sage/xtrem-sales/widgets__unposted_credits____rowDefinition__line2__title": "Site", "@sage/xtrem-sales/widgets__unposted_credits____rowDefinition__line2Right__title": "Status", "@sage/xtrem-sales/widgets__unposted_credits____rowDefinition__line3__title": "Credit memo date", "@sage/xtrem-sales/widgets__unposted_credits____rowDefinition__line3Right__title": "Due date", "@sage/xtrem-sales/widgets__unposted_credits____rowDefinition__title__title": "Sales credit", "@sage/xtrem-sales/widgets__unposted_credits____rowDefinition__titleRight__title": "Customer", "@sage/xtrem-sales/widgets__unposted_credits____title": "Unposted sales credit memos", "@sage/xtrem-sales/widgets__unposted_invoices____callToActions__seeAll__title": "See all", "@sage/xtrem-sales/widgets__unposted_invoices____dataDropdownMenu__orderBy__customerName__title": "Sort by customer name", "@sage/xtrem-sales/widgets__unposted_invoices____dataDropdownMenu__orderBy__date__title": "Sort by invoice date", "@sage/xtrem-sales/widgets__unposted_invoices____dataDropdownMenu__orderBy__number__title": "Sort by number", "@sage/xtrem-sales/widgets__unposted_invoices____rowDefinition__line2__title": "Sales site", "@sage/xtrem-sales/widgets__unposted_invoices____rowDefinition__line2Right__title": "Status", "@sage/xtrem-sales/widgets__unposted_invoices____rowDefinition__line3__title": "Invoice date", "@sage/xtrem-sales/widgets__unposted_invoices____rowDefinition__line3Right__title": "Due date", "@sage/xtrem-sales/widgets__unposted_invoices____rowDefinition__title__title": "Sales invoice", "@sage/xtrem-sales/widgets__unposted_invoices____rowDefinition__titleRight__title": "Customer", "@sage/xtrem-sales/widgets__unposted_invoices____title": "Unposted sales invoices", "@sage/xtrem-sales/widgets__unposted_shipments____callToActions__seeAll__title": "See all", "@sage/xtrem-sales/widgets__unposted_shipments____dataDropdownMenu__orderBy__customerName__title": "Sort by customer name", "@sage/xtrem-sales/widgets__unposted_shipments____dataDropdownMenu__orderBy__number__title": "Sort by number", "@sage/xtrem-sales/widgets__unposted_shipments____dataDropdownMenu__orderBy__shippingDate__title": "Sort by shipping date", "@sage/xtrem-sales/widgets__unposted_shipments____rowDefinition__line2__title": "Sales site", "@sage/xtrem-sales/widgets__unposted_shipments____rowDefinition__line2Right__title": "Status", "@sage/xtrem-sales/widgets__unposted_shipments____rowDefinition__line3__title": "Shipping date", "@sage/xtrem-sales/widgets__unposted_shipments____rowDefinition__line3Right__title": "Delivery date", "@sage/xtrem-sales/widgets__unposted_shipments____rowDefinition__title__title": "Sales shipment", "@sage/xtrem-sales/widgets__unposted_shipments____rowDefinition__titleRight__title": "Customer", "@sage/xtrem-sales/widgets__unposted_shipments____title": "Unposted sales shipments", "@sage/xtrem-sales/widgets__ytd_sales____title": "YTD sales"}