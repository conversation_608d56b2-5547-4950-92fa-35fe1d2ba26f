import * as xtremCommunication from '@sage/xtrem-communication';
import { ActivityExtension } from '@sage/xtrem-core';
import * as xtremSales from '..';

export const sysNotificationHistoryExtension = new ActivityExtension({
    extends: xtremCommunication.activities.sysNotificationHistory,
    __filename,
    permissions: [],
    operationGrants: {
        read: [
            {
                operations: ['lookup'],
                on: [() => xtremSales.nodes.SalesShipment, () => xtremSales.nodes.SalesReturnReceipt],
            },
        ],
    },
});
