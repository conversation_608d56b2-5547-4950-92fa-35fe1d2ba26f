import type { Context, decimal, Dict, NodeCreateData, StaticThis, UpdateAction } from '@sage/xtrem-core';
import { asyncArray, BusinessRuleError, date, LocalizedError } from '@sage/xtrem-core';
import * as xtremSales from '../index';
import { BaseSalesDocumentsCreator } from './base-sales-documents-creator';

export class SalesReturnReceiptCreator extends BaseSalesDocumentsCreator {
    salesOutputDocuments: Dict<NodeCreateData<xtremSales.nodes.SalesReturnReceipt>> = {};

    outputNode = xtremSales.nodes.SalesReturnReceipt;

    salesDocumentLineDependency: 'toReturnRequestLines' = 'toReturnRequestLines';

    numberOfName: 'numberOfReturnReceipts' = 'numberOfReturnReceipts';

    hasDiscountCharges = true;

    aggregateNode: StaticThis<
        | xtremSales.nodes.SalesShipmentLineToSalesReturnReceiptLine
        | xtremSales.nodes.SalesReturnRequestLineToSalesReturnReceiptLine
    > = xtremSales.nodes.SalesReturnRequestLineToSalesReturnReceiptLine;

    protected salesShipmentLine: xtremSales.nodes.SalesShipmentLine | null = null;

    static override readonly parametersAreIncorrect: xtremSales.interfaces.CreateSalesOutputDocumentsReturnValues = {
        ...BaseSalesDocumentsCreator.parametersAreIncorrect,
        numberOfReturnReceipts: 0,
    };

    constructor(
        public classContext: Context,
        public returnDate: date = date.today(),
    ) {
        super();
    }

    override async prepareNodeCreateData(
        salesDocumentLines: Array<{
            salesDocumentLine: xtremSales.nodes.SalesReturnRequestLine;
            quantityToProcess?: decimal;
        }>,
    ): Promise<this> {
        await asyncArray(salesDocumentLines).forEach(async line => {
            this.salesShipmentLine =
                (await line.salesDocumentLine.salesShipmentLines.length) > 0
                    ? await (
                          await line.salesDocumentLine.salesShipmentLines?.elementAt(0)
                      )?.linkedDocument
                    : null;

            const header = await line.salesDocumentLine.document;
            await this.createDictKey(line.salesDocumentLine, header);
            this.lastAddedLineIndex = this.salesOutputDocuments[this.dictKey]?.lines?.length ?? 0;

            try {
                await this.controlInputLine(line.salesDocumentLine);
                await this.createOrAppendNewDocument(line.salesDocumentLine, line.quantityToProcess);
            } catch (error) {
                if (!(error instanceof LocalizedError)) throw error;

                await this.processInputErrors(error, line.salesDocumentLine);
            }
        });
        return this;
    }

    override async createDictKey(
        line: xtremSales.nodes.SalesReturnRequestLine,
        document: xtremSales.nodes.SalesReturnRequest,
    ): Promise<void> {
        await super.createDictKey(line, document);
        const shipToAddress = await document.shipToAddress;
        const shipToContact = await document.shipToContact;
        this.dictKey +=
            String(await document.number) +
            String((await document.site)._id) +
            String((await document.shipToCustomer)._id) +
            String((await document.shipToCustomerAddress)._id) +
            String((await document.deliveryMode)._id) +
            String(shipToAddress?._id) +
            String(shipToContact?._id);
    }

    async controlInputLine(salesDocumentLine: xtremSales.nodes.SalesReturnRequestLine): Promise<void> {
        if ((await salesDocumentLine.status) === 'closed') {
            throw new BusinessRuleError(
                this.classContext.localize(
                    '@sage/xtrem-sales/classes__sales_return_receipt_creator__sales_return_request_line_closed',
                    'A return request line is already closed.',
                ),
            );
        }
    }

    override async calculateMissingQuantity(
        salesReturnRequestLine: xtremSales.nodes.SalesReturnRequestLine,
    ): Promise<number> {
        return Math.abs(
            (await SalesReturnReceiptCreator.calculateInputDocumentQuantity(
                this.classContext,
                salesReturnRequestLine,
            )) -
                (await (this.constructor as typeof BaseSalesDocumentsCreator).getTotalQuantityFromLines(
                    {
                        linkedDocument: { _id: salesReturnRequestLine._id },
                    },
                    this.classContext,
                    this.aggregateNode,
                )),
        );
    }

    override async addHeaderDataToDocumentNode(
        salesReturnRequestLine: xtremSales.nodes.SalesReturnRequestLine,
    ): Promise<void> {
        const commonProperties = {
            date: this.returnDate,
        };

        await super.addHeaderDataToDocumentNode(salesReturnRequestLine);
        const header = await salesReturnRequestLine.document;
        this.salesOutputDocuments[this.dictKey] = {
            ...this.salesOutputDocuments[this.dictKey],
            site: (await header.site)?._id,
            shipToCustomer: (await header.shipToCustomer)?._id,
            shipToCustomerAddress: (await header.shipToCustomerAddress)?._id,
            shipToAddress: await header.shipToAddress,
            shipToContact: await header.shipToContact,
            lines: [],
            ...commonProperties,
        };
    }

    override async addNewLineToDocumentNode(
        salesReturnRequestLine: xtremSales.nodes.SalesReturnRequestLine,
        outputQuantity: decimal,
    ): Promise<decimal> {
        const outputDocumentLinesLength: number = await super.addNewLineToDocumentNode(
            salesReturnRequestLine,
            outputQuantity,
        );

        return outputDocumentLinesLength;
    }

    override async prepareNewLineToDocumentNode(
        salesReturnRequestLine: xtremSales.nodes.SalesReturnRequestLine,
        outputQuantity: decimal,
    ): Promise<number> {
        const salesReturnReceiptLinesLength = await super.prepareNewLineToDocumentNode(
            salesReturnRequestLine,
            outputQuantity,
        );
        const lineData: NodeCreateData<xtremSales.nodes.SalesCreditMemoLine> = {
            origin: 'return',
        };

        const lines = this.salesOutputDocuments[this.dictKey].lines || null;
        if (lines && salesReturnReceiptLinesLength > 0) {
            lines[salesReturnReceiptLinesLength - 1] = {
                ...lines[salesReturnReceiptLinesLength - 1],
                ...(lineData as any),
            };
        }
        return salesReturnReceiptLinesLength;
    }

    override async addNewBaseDocumentLineToSalesDocumentLineDependency(
        outputDocumentLinesLength: number,
        salesReturnRequestLine: xtremSales.nodes.SalesReturnRequestLine,
        outputQuantity: decimal,
    ): Promise<void> {
        await super.addNewBaseDocumentLineToSalesDocumentLineDependency(
            outputDocumentLinesLength,
            salesReturnRequestLine,
            outputQuantity,
        );
        const lines = this.salesOutputDocuments[this.dictKey].lines || null;
        if (this.salesShipmentLine !== null && lines && outputDocumentLinesLength > 0) {
            lines[outputDocumentLinesLength - 1].salesShipmentLines = [] as NodeCreateData<
                xtremSales.nodes.SalesShipmentLineToSalesReturnReceiptLine & {
                    _sortValue?: number;
                    _action: UpdateAction;
                }
            >[];
            lines[outputDocumentLinesLength - 1]?.salesShipmentLines?.push({
                linkedDocument: this.salesShipmentLine?._id,
                quantity: outputQuantity,
                quantityInStockUnit: outputQuantity * (await this.salesShipmentLine.unitToStockUnitConversionFactor),
            });
        }
    }

    // eslint-disable-next-line class-methods-use-this
    getCreateSalesOutputDocumentsStatus = () => 'salesReturnIsReceived';

    override createConsoleLog(outputDocumentNumber: string, inputDocumentsNumbers: string) {
        this.classContext.logger.info(
            this.classContext.localize(
                '@sage/xtrem-sales/class__sales_return_receipt_creator__console_log_message',
                `The {{outputDocumentNumber}} sales return receipt number was created from the following sales return request numbers: {{inputDocumentsNumbers}}.`,
                {
                    outputDocumentNumber,
                    inputDocumentsNumbers,
                },
            ),
        );
    }

    createErrorConsoleLog(message: string, inputDocumentsNumbers: string): string | undefined {
        const errorMessage = this.classContext.localize(
            '@sage/xtrem-sales/class__sales_return_receipt_creator__console_log_error_message',
            `The sales return receipt could not be created using the sales return request numbers: {{inputDocumentsNumbers}}. Details: {{message}}`,
            {
                message,
                inputDocumentsNumbers,
            },
        );
        this.classContext.logger.error(errorMessage);
        return errorMessage;
    }

    static override async validateQuantityAmount(
        data: xtremSales.interfaces.CheckTotalQuantityBaseOnStatus,
        totalQuantity: decimal,
    ): Promise<void> {
        if (
            data.dictLine.action !== 'delete' &&
            totalQuantity >
                (await this.calculateInputDocumentQuantity(
                    data.context,
                    data.linkedDocument as xtremSales.nodes.SalesReturnRequestLine,
                ))
        ) {
            throw new BusinessRuleError(
                data.context.localize(
                    '@sage/xtrem-sales/class__base_sales_documents_creator__remaining_quantity',
                    'The sales document line quantity cannot be larger than the remaining quantity in the related document.',
                ),
            );
        }
    }

    private static async calculateInputDocumentQuantity(
        context: Context,
        linkedDocument: xtremSales.nodes.SalesReturnRequestLine,
    ): Promise<decimal> {
        if (await linkedDocument.isReceiptExpected) {
            return (
                await context
                    .queryAggregate(xtremSales.nodes.SalesReturnRequestLineToSalesReturnReceiptLine, {
                        filter: {
                            linkedDocument: { _id: linkedDocument._id },
                        },
                        group: {
                            linkedDocument: { _id: { _by: 'value' } },
                        },
                        values: {
                            quantity: { sum: true },
                        },
                    })
                    .toArray()
            )[0]?.values.quantity.sum;
        }
        return linkedDocument.quantity;
    }
}
