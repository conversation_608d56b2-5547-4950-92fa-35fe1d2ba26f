import type { Context } from '@sage/xtrem-core';
import { asyncArray, NodeStatus } from '@sage/xtrem-core';
import type * as xtremSystem from '@sage/xtrem-system';
import * as xtremTax from '@sage/xtrem-tax';
import type * as xtremSales from '../index';
import { SalesTaxCalculator } from './sales-tax-calculator';

export class SalesOrderTaxCalculator extends SalesTaxCalculator {
    // eslint-disable-next-line class-methods-use-this
    override async shouldOverwriteTax(lineInstance: xtremSales.nodes.SalesOrderLine): Promise<boolean> {
        return (await lineInstance.shipToAddress)?.$.status === NodeStatus.modified;
    }

    // eslint-disable-next-line class-methods-use-this
    override async getTaxDeterminationDocumentLineParameters(
        lineInstance: xtremTax.interfaces.DocumentLine,
    ): Promise<xtremTax.interfaces.TaxDeterminationDocumentLineParameters> {
        const line = lineInstance as xtremSales.nodes.SalesOrderLine;
        return {
            flow: 'sales',
            providerAddress: (await line.stockSiteAddress)!,
            consumerAddress: (await line.shipToAddress)!,
            item: await line.item,
            site: await line.stockSite,
            taxDate: await line.taxDate,
            legalEntity: await (await (await (await line.document).billToCustomer).businessEntity).legalEntity,
            taxSolution: await (await (await (await line.site).legalCompany).country).taxSolution,
        };
    }

    private constructor(public override classContext: Context) {
        super(classContext);
    }

    protected override async init(
        site: xtremSystem.nodes.Site,
        status: xtremSales.enums.SalesOrderStatus = 'quote',
    ): Promise<this> {
        await super.init(site, status);
        this.isGenericTaxCalculationEnabled = (await (await site.legalCompany).taxEngine) === 'genericTaxCalculation';
        this.canEditTaxes = status !== 'closed';
        return this;
    }

    static override create(
        classContext: Context,
        site: xtremSystem.nodes.Site,
        status: xtremSales.enums.SalesOrderStatus = 'quote',
    ): Promise<SalesOrderTaxCalculator> {
        return new SalesOrderTaxCalculator(classContext).init(site, status);
    }

    override async getTaxDetermination(
        lineInstance: xtremSales.nodes.SalesOrderLine,
    ): Promise<xtremTax.interfaces.TaxDeterminationResult[]> {
        const taxDetermination = await super.getTaxDetermination(lineInstance);

        if (
            taxDetermination.every(taxDeterminationLine => !taxDeterminationLine.tax) &&
            (await xtremTax.classes.BaseTaxCalculator.getIsTaxable(
                await lineInstance.itemSite,
                await lineInstance.item,
            ))
        ) {
            const taxZone = await lineInstance.taxZone;
            if (taxZone) {
                const taxDeterminationDocumentLineParameters =
                    await this.getTaxDeterminationDocumentLineParameters(lineInstance);
                return asyncArray(taxDetermination)
                    .map(async taxDeterminationLine => {
                        const tax =
                            (await (
                                await taxZone.lines.find(
                                    async taxZoneLine =>
                                        (await taxZoneLine.taxCategory) === taxDeterminationLine.taxCategory &&
                                        !!(await taxZoneLine.tax),
                                )
                            )?.tax) || null;

                        if (tax) {
                            return xtremTax.functions.getDeterminatedTaxValues(
                                this.classContext,
                                taxDeterminationDocumentLineParameters.taxDate,
                                tax,
                                {
                                    isSubjectToGlTaxExcludedAmount: taxDeterminationLine.isSubjectToGlTaxExcludedAmount,
                                    isTaxMandatory: taxDeterminationLine.isTaxMandatory,
                                    displayOrder: taxDeterminationLine.displayOrder,
                                },
                            );
                        }
                        return taxDeterminationLine;
                    })
                    .toArray();
            }
        }

        return taxDetermination;
    }
}
