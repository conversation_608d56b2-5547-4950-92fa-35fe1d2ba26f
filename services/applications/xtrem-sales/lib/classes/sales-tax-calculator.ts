import type { Context } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import type * as xtremSystem from '@sage/xtrem-system';
import * as xtremTax from '@sage/xtrem-tax';
import type * as xtremSales from '../index';

export class SalesTaxCalculator extends xtremTax.classes.BaseTaxCalculator {
    // eslint-disable-next-line class-methods-use-this
    async shouldOverwriteTax(lineInstance: xtremTax.interfaces.DocumentLine): Promise<boolean> {
        const line = lineInstance as xtremSales.nodes.SalesCreditMemoLine | xtremSales.nodes.SalesInvoiceLine;
        if (line.$.status !== 'modified') return false;
        const oldConsumptionAddress = await (await line.$.old).consumptionAddress;
        const newConsumptionAddress = await line.consumptionAddress;
        return newConsumptionAddress?._id !== oldConsumptionAddress?._id;
    }

    // eslint-disable-next-line class-methods-use-this
    async updatedValues(
        lineInstance: xtremSales.nodes.SalesCreditMemoLine | xtremSales.nodes.SalesInvoiceLine,
    ): Promise<void> {
        const grossPrice = await lineInstance.grossPrice;
        if ((await lineInstance.currency) && grossPrice && grossPrice >= 0) {
            await lineInstance.$.set({
                netPrice: await xtremMasterData.functions.calculateNetPrice(
                    lineInstance.discountCharges,
                    grossPrice,
                    await lineInstance.quantity,
                    await (
                        await (
                            await lineInstance.document
                        ).currency
                    ).decimalDigits,
                ),
            });
            await lineInstance.$.set({
                netPriceExcludingTax: await lineInstance.netPrice,
                amountExcludingTax: (await lineInstance.quantity) * (await lineInstance.netPriceExcludingTax),
            });
        }
    }

    // eslint-disable-next-line class-methods-use-this
    async getTaxDeterminationDocumentLineParameters(
        lineInstance: xtremTax.interfaces.DocumentLine,
    ): Promise<xtremTax.interfaces.TaxDeterminationDocumentLineParameters> {
        const line = lineInstance as xtremSales.nodes.SalesCreditMemoLine | xtremSales.nodes.SalesInvoiceLine;
        return {
            flow: 'sales',
            providerAddress: (await line.providerSiteAddress)!,
            consumerAddress: (await line.consumptionAddress)!,
            item: await line.item,
            site: await line.providerSite,
            taxDate: await line.taxDate,
            legalEntity: await (await (await (await line.document).billToCustomer).businessEntity).legalEntity,
            taxSolution: await (
                await (
                    await (
                        await (
                            await line.document
                        ).financialSite
                    ).legalCompany
                ).country
            ).taxSolution,
        };
    }

    includeAllTotals = true;

    protected constructor(public classContext: Context) {
        super();
    }

    protected async init(
        site: xtremSystem.nodes.Site,
        status:
            | xtremSales.enums.SalesOrderStatus
            | xtremSales.enums.SalesInvoiceStatus
            | xtremSales.enums.SalesCreditMemoStatus = 'draft',
        forceUpdateForFinance: boolean = false,
    ): Promise<this> {
        this.isGenericTaxCalculationEnabled = (await (await site.legalCompany).taxEngine) === 'genericTaxCalculation';
        this.canEditTaxes = status === 'draft' || forceUpdateForFinance;
        return this;
    }

    static create(
        classContext: Context,
        site: xtremSystem.nodes.Site,
        status:
            | xtremSales.enums.SalesOrderStatus
            | xtremSales.enums.SalesInvoiceStatus
            | xtremSales.enums.SalesCreditMemoStatus = 'draft',
        forceUpdateForFinance?: boolean,
    ): Promise<SalesTaxCalculator> {
        return new SalesTaxCalculator(classContext).init(site, status, forceUpdateForFinance);
    }
}
