/* eslint-disable @typescript-eslint/no-unused-vars */
import type { Context } from '@sage/xtrem-core';
import type * as xtremSales from '..';

export abstract class SalesDocumentHooks {
    public static beforePrint(
        _writableContext: Context,
        _writableInvoice: xtremSales.nodes.SalesInvoice | xtremSales.nodes.SalesCreditMemo,
    ): Promise<boolean> {
        return Promise.resolve(true);
    }
}
