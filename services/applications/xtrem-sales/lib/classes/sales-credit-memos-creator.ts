import type { Context, decimal, Dict, NodeCreateData, StaticThis, UpdateAction } from '@sage/xtrem-core';
import { asyncArray, BusinessRuleError, date, LocalizedError } from '@sage/xtrem-core';
import * as xtremSales from '../index';
import type { SalesCreditMemo } from '../nodes';
import { BaseSalesDocumentsCreator } from './base-sales-documents-creator';

export class SalesCreditMemosCreator extends BaseSalesDocumentsCreator {
    salesOutputDocuments: Dict<NodeCreateData<xtremSales.nodes.SalesCreditMemo>> = {};

    outputNode = xtremSales.nodes.SalesCreditMemo;

    salesDocumentLineDependency: 'toInvoiceLines' = 'toInvoiceLines';

    numberOfName: 'numberOfCreditMemos' = 'numberOfCreditMemos';

    hasDiscountCharges = true;

    aggregateNode: StaticThis<
        | xtremSales.nodes.SalesInvoiceLineToSalesCreditMemoLine
        | xtremSales.nodes.SalesReturnRequestLineSalesCreditMemoLine
    > = xtremSales.nodes.SalesInvoiceLineToSalesCreditMemoLine;

    protected salesReturnRequestLine: xtremSales.nodes.SalesReturnRequestLine | null = null;

    static override readonly parametersAreIncorrect: xtremSales.interfaces.CreateSalesOutputDocumentsReturnValues = {
        ...BaseSalesDocumentsCreator.parametersAreIncorrect,
        numberOfCreditMemos: 0,
    };

    constructor(
        public classContext: Context,
        public creditMemoDate: date = date.today(),
    ) {
        super();
    }

    override async prepareNodeCreateData(
        salesDocumentLines: Array<{
            salesDocumentLine: xtremSales.interfaces.AnyInputDocumentLine | xtremSales.nodes.SalesReturnRequestLine;
            quantityToProcess?: decimal;
        }>,
    ): Promise<this> {
        let salesInvoiceLine: xtremSales.nodes.SalesInvoiceLine;
        await asyncArray(salesDocumentLines).forEach(async line => {
            if (line.salesDocumentLine instanceof xtremSales.nodes.SalesReturnRequestLine) {
                this.salesReturnRequestLine = line.salesDocumentLine;
                const salesShipmentLine = await (
                    await this.salesReturnRequestLine.salesShipmentLines.elementAt(0)
                ).linkedDocument;
                if ((await salesShipmentLine.invoiceStatus) !== 'invoiced') {
                    await this.processInputErrors(
                        new BusinessRuleError(
                            this.classContext.localize(
                                '@sage/xtrem-sales/classes__sales_credit_memo_creator__invalid_selected_sales_shipment_not_invoiced',
                                'The related sales shipment line is not invoiced.',
                            ),
                        ),
                        this.salesReturnRequestLine as any,
                    );
                }

                salesInvoiceLine = await (
                    await this.classContext
                        .query(xtremSales.nodes.SalesShipmentLineToSalesInvoiceLine, {
                            filter: { linkedDocument: salesShipmentLine._id },
                        })
                        .elementAt(0)
                ).document;
            } else {
                salesInvoiceLine = line.salesDocumentLine as xtremSales.nodes.SalesInvoiceLine;
            }

            const header = await salesInvoiceLine.document;
            await this.createDictKey(salesInvoiceLine, header);
            this.lastAddedLineIndex = this.salesOutputDocuments[this.dictKey]?.lines!.length ?? 0;

            try {
                await this.controlInputLine(salesInvoiceLine);
                await this.createOrAppendNewDocument(salesInvoiceLine, line.quantityToProcess);
            } catch (error) {
                if (!(error instanceof LocalizedError)) throw error;

                await this.processInputErrors(error, salesInvoiceLine);
            }
        });
        return this;
    }

    async controlInputLine(salesDocumentLine: xtremSales.nodes.SalesInvoiceLine): Promise<void> {
        if (this.salesReturnRequestLine) {
            const isApprovalManaged = await (
                await (
                    await this.salesReturnRequestLine.document
                ).site
            ).isSalesReturnRequestApprovalManaged;

            const approvalStatus = await (await this.salesReturnRequestLine.document).approvalStatus;

            if (isApprovalManaged && approvalStatus !== 'approved') {
                throw new BusinessRuleError(
                    this.classContext.localize(
                        '@sage/xtrem-sales/classes__sales_credit_memo_creator__sales_return_request_not_approved',
                        'A return request is not approved.',
                    ),
                );
            }

            if (!isApprovalManaged && approvalStatus !== 'confirmed') {
                throw new BusinessRuleError(
                    this.classContext.localize(
                        '@sage/xtrem-sales/classes__sales_credit_memo_creator__sales_return_request_not_confirmed',
                        'A return request is not confirmed.',
                    ),
                );
            }

            if ((await this.salesReturnRequestLine.creditStatus) === 'credited') {
                throw new BusinessRuleError(
                    this.classContext.localize(
                        '@sage/xtrem-sales/classes__sales_credit_memo_creator__invalid_selected_lines_credit_status_return',
                        'A return request line is already credited.',
                    ),
                );
            }

            if (!(await this.salesReturnRequestLine.isCreditMemoExpected)) {
                throw new BusinessRuleError(
                    this.classContext.localize(
                        '@sage/xtrem-sales/classes__sales_credit_memo_creator__invalid_selected_is_credit_memo_expected',
                        'A sales credit memo is not expected for this return request line.',
                    ),
                );
            }

            if (
                (await this.salesReturnRequestLine.isReceiptExpected) &&
                (await this.salesReturnRequestLine.receiptStatus) === 'notReceived'
            ) {
                throw new BusinessRuleError(
                    this.classContext.localize(
                        '@sage/xtrem-sales/classes__sales_credit_memo_creator__is_receipt_expected_not_received',
                        'A sales return receipt is expected but not yet received.',
                    ),
                );
            }
        }
        if ((await salesDocumentLine.creditStatus) === 'credited') {
            throw new BusinessRuleError(
                this.classContext.localize(
                    '@sage/xtrem-sales/classes__sales_credit_memo_creator__invalid_selected_lines_credit_status',
                    'An invoice line is already credited.',
                ),
            );
        }
        const document = await salesDocumentLine.document;
        if (!['posted', 'inProgress', 'error'].includes(await document.status)) {
            throw new BusinessRuleError(
                this.classContext.localize(
                    '@sage/xtrem-sales/classes__sales_credit_memo_creator__invalid_selected_line_document_status',
                    'Post an invoice first.',
                ),
            );
        }
        if ((await document.status) === 'error') {
            throw new BusinessRuleError(
                this.classContext.localize(
                    '@sage/xtrem-sales/classes__sales_credit_memo_creator__error_selected_line_document_status',
                    'You cannot create a credit memo if there is a posting error.',
                ),
            );
        }
        if ((await document.status) === 'inProgress') {
            throw new BusinessRuleError(
                this.classContext.localize(
                    '@sage/xtrem-sales/classes__sales_credit_memo_creator__in_progress_selected_line_document_status',
                    'You can only credit a credit memo for a posted invoice.',
                ),
            );
        }
    }

    override async calculateMissingQuantity(salesInvoiceLine: xtremSales.nodes.SalesInvoiceLine): Promise<number> {
        let line: xtremSales.interfaces.AnyInputDocumentLine | xtremSales.nodes.SalesReturnRequestLine;
        if (!this.salesReturnRequestLine) {
            line = salesInvoiceLine;
        } else {
            line = this.salesReturnRequestLine;
            this.aggregateNode = xtremSales.nodes.SalesReturnRequestLineSalesCreditMemoLine;
        }
        return Math.abs(
            (await SalesCreditMemosCreator.calculateInputDocumentQuantity(this.classContext, line)) -
                (await (<typeof BaseSalesDocumentsCreator>this.constructor).getTotalQuantityFromLines(
                    {
                        linkedDocument: { _id: line._id },
                    },
                    this.classContext,
                    this.aggregateNode,
                )),
        );
    }

    override async createDictKey(
        salesInvoiceLine: xtremSales.nodes.SalesInvoiceLine,
        salesInvoice: xtremSales.nodes.SalesInvoice,
    ): Promise<void> {
        const header = this.salesReturnRequestLine ? await this.salesReturnRequestLine.document : salesInvoice;
        await super.createDictKey(salesInvoiceLine, header);
        this.dictKey =
            String(header._id) +
            String((await header.site)._id) +
            String((await salesInvoice.currency)._id) +
            String((await salesInvoice.paymentTerm)._id);
    }

    override async addHeaderDataToDocumentNode(salesInvoiceLine: xtremSales.nodes.SalesInvoiceLine): Promise<void> {
        const salesInvoice = await salesInvoiceLine.document;
        const commonProperties: NodeCreateData<SalesCreditMemo> = {
            currency: (await salesInvoice.currency)?._id,
            paymentTerm: (await salesInvoice.paymentTerm)?._id,
            date: this.creditMemoDate,
        };

        if (!this.salesReturnRequestLine) {
            await super.addHeaderDataToDocumentNode(salesInvoiceLine);
            this.salesOutputDocuments[this.dictKey] = {
                ...this.salesOutputDocuments[this.dictKey],
                site: (await salesInvoice.site)?._id,
                ...commonProperties,
            };
        } else {
            const salesReturnRequest = await this.salesReturnRequestLine.document;
            this.salesOutputDocuments[this.dictKey] = {
                billToCustomer: (await salesReturnRequest.billToCustomer)?._id,
                billToLinkedAddress: (await salesReturnRequest.billToLinkedAddress)?._id,
                incoterm: (await salesInvoice.incoterm)?._id,
                billToAddress: await salesReturnRequest.billToAddress,
                billToContact: await salesReturnRequest.billToContact,
                lines: [],
                site: (await salesReturnRequest.site)?._id,
                ...commonProperties,
            };
        }
    }

    override async addNewLineToDocumentNode(
        salesInvoiceLine: xtremSales.nodes.SalesInvoiceLine,
        outputQuantity: decimal,
    ): Promise<decimal> {
        const outputDocumentLinesLength: number = await super.addNewLineToDocumentNode(
            salesInvoiceLine,
            outputQuantity,
        );
        await this.addNewSalesMemoLineTaxDependency(outputDocumentLinesLength, salesInvoiceLine);

        return outputDocumentLinesLength;
    }

    /**
     * We use this method to copy tax references from sales invoices. It is important especially in the case
     * if the user chose a different tax determination than that suggested by the system.
     * @param outputDocumentLinesLength
     * @param linkedDocument
     */
    async addNewSalesMemoLineTaxDependency(
        outputDocumentLinesLength: number,
        linkedDocument: xtremSales.nodes.SalesInvoiceLine,
    ): Promise<void> {
        this.salesOutputDocuments[this.dictKey].lines![outputDocumentLinesLength - 1].taxes = [] as NodeCreateData<
            xtremSales.nodes.SalesCreditMemoLineTax & {
                _sortValue?: number;
                _action: UpdateAction;
            }
        >[];
        await linkedDocument.taxes.forEach(async (tax: xtremSales.nodes.SalesInvoiceLineTax) => {
            this.salesOutputDocuments[this.dictKey].lines![outputDocumentLinesLength - 1].taxes!.push({
                taxCategoryReference: (await tax.taxCategoryReference)?._id,
                taxReference: (await tax.taxReference)?._id,
                isSubjectToGlTaxExcludedAmount: await tax.isSubjectToGlTaxExcludedAmount,
                isTaxMandatory: await tax.isTaxMandatory,
                _sortValue: await tax._sortValue,
                // taxRate is only to meet field requirements. It will be rewritten to the actual rate
                // for the current date during BaseTaxCalculator execution.
                taxRate: await tax.taxRate,
            });
        });
    }

    override async prepareNewLineToDocumentNode(
        salesInvoiceLine: xtremSales.nodes.SalesInvoiceLine,
        outputQuantity: decimal,
    ): Promise<number> {
        let salesCreditMemoLinesLength: decimal;
        if (!this.salesReturnRequestLine) {
            salesCreditMemoLinesLength = await super.prepareNewLineToDocumentNode(salesInvoiceLine, outputQuantity);
        } else {
            salesCreditMemoLinesLength = await super.prepareNewLineToDocumentNode(
                this.salesReturnRequestLine,
                outputQuantity,
            );
        }
        const lineData: NodeCreateData<xtremSales.nodes.SalesCreditMemoLine> = {
            consumptionLinkedAddress: (await salesInvoiceLine.consumptionLinkedAddress)?._id,
            consumptionAddress: await salesInvoiceLine.consumptionAddress,
            providerSite: (await salesInvoiceLine.providerSite)?._id,
            providerSiteLinkedAddress: (await salesInvoiceLine.providerSiteLinkedAddress)?._id,
            providerSiteAddress: await salesInvoiceLine.providerSiteAddress,
            origin: !this.salesReturnRequestLine ? 'invoice' : 'return',
            grossPrice: await salesInvoiceLine.grossPrice,
            priceOrigin: await salesInvoiceLine.priceOrigin,
            priceReason: (await salesInvoiceLine.priceReason)?._id,
            isPriceDeterminated: await salesInvoiceLine.isPriceDeterminated,
            grossPriceDeterminated: await salesInvoiceLine.grossPriceDeterminated,
            priceOriginDeterminated: await salesInvoiceLine.priceOriginDeterminated,
            priceReasonDeterminated: (await salesInvoiceLine.priceReasonDeterminated)?._id,
            taxDate: await salesInvoiceLine.taxDate,
        };

        this.salesOutputDocuments[this.dictKey].lines![salesCreditMemoLinesLength - 1] = {
            ...this.salesOutputDocuments[this.dictKey].lines![salesCreditMemoLinesLength - 1],
            ...(lineData as any),
        };
        return salesCreditMemoLinesLength;
    }

    override async addNewBaseDocumentLineToSalesDocumentLineDependency(
        outputDocumentLinesLength: number,
        salesInvoiceLine: xtremSales.nodes.SalesInvoiceLine,
        outputQuantity: decimal,
    ): Promise<void> {
        await super.addNewBaseDocumentLineToSalesDocumentLineDependency(
            outputDocumentLinesLength,
            salesInvoiceLine,
            outputQuantity,
        );
        if (this.salesReturnRequestLine !== null) {
            this.salesOutputDocuments[this.dictKey].lines![outputDocumentLinesLength - 1].toReturnRequestLines =
                [] as NodeCreateData<
                    xtremSales.nodes.SalesReturnRequestLineSalesCreditMemoLine & {
                        _sortValue?: number;
                        _action: UpdateAction;
                    }
                >[];
            this.salesOutputDocuments[this.dictKey].lines![outputDocumentLinesLength - 1].toReturnRequestLines?.push({
                linkedDocument: this.salesReturnRequestLine?._id,
                quantity: outputQuantity,
                quantityInStockUnit:
                    outputQuantity * (await this.salesReturnRequestLine.unitToStockUnitConversionFactor),
                amount: (await salesInvoiceLine.netPrice) * outputQuantity,
            });
        }
    }

    getCreateSalesOutputDocumentsStatus() {
        if (!this.salesReturnRequestLine) {
            return 'salesInvoiceIsCredited';
        }
        return 'salesReturnRequestIsCredited';
    }

    override createConsoleLog(outputDocumentNumber: string, inputDocumentsNumbers: string) {
        this.classContext.logger.info(
            this.classContext.localize(
                '@sage/xtrem-sales/class__sales_credit_memo_creator__console_log_message',
                `The {{outputDocumentNumber}} sales credit memo number was created from the following sales invoice numbers: {{inputDocumentsNumbers}}.`,
                {
                    outputDocumentNumber,
                    inputDocumentsNumbers,
                },
            ),
        );
    }

    createErrorConsoleLog(message: string, inputDocumentsNumbers: string): string | undefined {
        const errorMessage = this.classContext.localize(
            '@sage/xtrem-sales/class__sales_credit_memos_creator__console_log_error_message',
            `The sales credit memo could not be created using the sales invoice numbers: {{inputDocumentsNumbers}}. Details: {{message}}`,
            {
                message,
                inputDocumentsNumbers,
            },
        );
        this.classContext.logger.error(errorMessage);
        return errorMessage;
    }

    static override async validateQuantityAmount(
        data: xtremSales.interfaces.CheckTotalQuantityBaseOnStatus,
        totalQuantity: decimal,
    ): Promise<void> {
        if (
            data.dictLine.action !== 'delete' &&
            totalQuantity > (await this.calculateInputDocumentQuantity(data.context, data.linkedDocument))
        ) {
            throw new BusinessRuleError(
                data.context.localize(
                    '@sage/xtrem-sales/class__base_sales_documents_creator__remaining_quantity',
                    'The sales document line quantity cannot be larger than the remaining quantity in the related document.',
                ),
            );
        }
    }

    private static async calculateInputDocumentQuantity(
        context: Context,
        linkedDocument: xtremSales.interfaces.AnyInputDocumentLine | xtremSales.nodes.SalesReturnRequestLine,
    ): Promise<decimal> {
        if (
            linkedDocument instanceof xtremSales.nodes.SalesReturnRequestLine &&
            (await linkedDocument.isReceiptExpected)
        ) {
            return (
                await context
                    .queryAggregate(xtremSales.nodes.SalesReturnRequestLineToSalesReturnReceiptLine, {
                        filter: {
                            linkedDocument: { _id: linkedDocument._id },
                        },
                        group: {
                            linkedDocument: { _id: { _by: 'value' } },
                        },
                        values: {
                            quantity: { sum: true },
                        },
                    })
                    .toArray()
            )[0]?.values.quantity.sum;
        }
        return linkedDocument.quantity;
    }
}
