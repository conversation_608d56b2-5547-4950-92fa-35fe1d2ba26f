import type { Context, decimal, Dict, NodeCreateData, StaticThis } from '@sage/xtrem-core';
import { asyncArray, BusinessRuleError, date } from '@sage/xtrem-core';
import * as xtremSales from '../index';
import { BaseSalesDocumentsCreator } from './base-sales-documents-creator';

export class SalesReturnRequestCreator extends BaseSalesDocumentsCreator {
    salesOutputDocuments: Dict<NodeCreateData<xtremSales.nodes.SalesReturnRequest>> = {};

    outputNode = xtremSales.nodes.SalesReturnRequest;

    salesDocumentLineDependency: 'salesShipmentLines' = 'salesShipmentLines';

    numberOfName: 'numberOfReturnRequests' = 'numberOfReturnRequests';

    hasDiscountCharges = false;

    // eslint-disable-next-line class-methods-use-this
    getCreateSalesOutputDocumentsStatus = () => 'salesShipmentReturnIsRequested';

    aggregateNode = xtremSales.nodes.SalesShipmentLineToSalesReturnRequestLine;

    static override readonly parametersAreIncorrect: xtremSales.interfaces.CreateSalesOutputDocumentsReturnValues = {
        ...BaseSalesDocumentsCreator.parametersAreIncorrect,
        numberOfReturnRequests: 0,
    };

    constructor(
        public classContext: Context,
        public returnRequestDate: date = date.today(),
    ) {
        super();
    }

    override async createDictKey(
        line: xtremSales.nodes.SalesShipmentLine,
        header: xtremSales.nodes.SalesShipment,
    ): Promise<void> {
        await super.createDictKey(line, header);
        const shipToAddress = await header.shipToAddress;
        const shipToContact = await header.shipToContact;
        this.dictKey +=
            String(await header.number) +
            String((await header.site)._id) +
            String((await header.stockSite)._id) +
            String((await header.shipToCustomer)._id) +
            String((await header.deliveryMode)._id) +
            String(shipToAddress?._id) +
            String(shipToContact?._id);
    }

    async controlInputLine(salesDocumentLine: xtremSales.nodes.SalesShipmentLine): Promise<void> {
        if ((await salesDocumentLine.returnRequestStatus) === 'returnRequested') {
            throw new BusinessRuleError(
                this.classContext.localize(
                    '@sage/xtrem-sales/classes__sales_return_request_creator__sales_shipment_line_already_returned',
                    'A shipment line is already returned.',
                ),
            );
        }
        if ((await salesDocumentLine.status) !== 'shipped') {
            throw new BusinessRuleError(
                this.classContext.localize(
                    '@sage/xtrem-sales/classes__sales_return_request_creator__sales_shipment_line_not_yet_posted',
                    'A shipment line is not posted.',
                ),
            );
        }
    }

    override async addHeaderDataToDocumentNode(line: xtremSales.nodes.SalesShipmentLine): Promise<void> {
        await super.addHeaderDataToDocumentNode(line);
        const header = await line.document;
        const salesOrderAssociated = await (await (await line.salesOrderLines.elementAt(0)).linkedDocument).document;

        this.salesOutputDocuments[this.dictKey] = {
            ...this.salesOutputDocuments[this.dictKey],
            site: (await header.site)?._id,
            stockSite: (await header.stockSite)?._id,
            soldToCustomer: (await salesOrderAssociated.soldToCustomer)?._id,
            soldToLinkedAddress: (await salesOrderAssociated.soldToLinkedAddress)?._id,
            soldToAddress: await salesOrderAssociated.soldToAddress,
            soldToContact: await salesOrderAssociated.soldToContact,
            shipToCustomer: (await header.shipToCustomer)?._id,
            shipToCustomerAddress: (await header.shipToCustomerAddress)?._id,
            shipToAddress: await header.shipToAddress,
            shipToContact: await header.shipToContact,
            date: this.returnRequestDate,
        };
    }

    override async prepareNewLineToDocumentNode(
        line: xtremSales.nodes.SalesShipmentLine,
        outputQuantity: decimal,
    ): Promise<number> {
        const SalesShipmentLinesLength = await super.prepareNewLineToDocumentNode(line, outputQuantity);
        const lineData: NodeCreateData<xtremSales.nodes.SalesReturnRequestLine> = {
            origin: 'shipment',
            originShippingSite: (await (await line.document).stockSite)?._id,
        };

        this.salesOutputDocuments[this.dictKey].lines![SalesShipmentLinesLength - 1] = {
            ...this.salesOutputDocuments[this.dictKey].lines![SalesShipmentLinesLength - 1],
            ...(lineData as any),
        };
        return SalesShipmentLinesLength;
    }

    createErrorConsoleLog(message: string, inputDocumentsNumbers: string) {
        const errorMessage = this.classContext.localize(
            '@sage/xtrem-sales/class__sales_return_request_creator__console_log_error_message',
            `The sales return request could not be created using the sales shipment numbers: {{inputDocumentsNumbers}}. Details: {{message}}`,
            {
                message,
                inputDocumentsNumbers,
            },
        );
        this.classContext.logger.error(errorMessage);
        return errorMessage;
    }

    static override async getTotalQuantityFromLines(
        filterArguments: any,
        context: Context,
        aggregateNode: StaticThis<xtremSales.interfaces.AnyBaseLineToSalesDocumentLine>,
    ): Promise<decimal> {
        let totalQuantity: decimal = 0;

        const salesShipmentLineToSalesReturnRequestLines = await context
            .query(aggregateNode, {
                filter: {
                    ...filterArguments,
                },
            })
            .toArray();
        await asyncArray(salesShipmentLineToSalesReturnRequestLines).forEach(
            async (
                salesShipmentLineToSalesReturnRequestLine: xtremSales.nodes.SalesShipmentLineToSalesReturnRequestLine,
            ) => {
                totalQuantity += (
                    await this.getFinalQuantityInSalesUnit(
                        await salesShipmentLineToSalesReturnRequestLine.document,
                        await salesShipmentLineToSalesReturnRequestLine.quantity,
                        context,
                    )
                ).finalQuantityInSalesUnit;
            },
        );
        return totalQuantity;
    }

    static async getFinalQuantityInSalesUnit(
        salesReturnRequestLine: xtremSales.nodes.SalesReturnRequestLine,
        baseQuantityInSalesUnit: decimal,
        context: Context,
    ): Promise<{ finalQuantityInSalesUnit: decimal; quantityReceived: decimal; quantityCredited: decimal }> {
        let quantityReceived: decimal = 0;
        let quantityCredited: decimal = 0;
        let finalQuantityInSalesUnit: decimal = 0;
        if ((await salesReturnRequestLine.status) !== 'closed') {
            finalQuantityInSalesUnit = baseQuantityInSalesUnit;
        }
        if ((await (await salesReturnRequestLine.document).approvalStatus) === 'approved') {
            if (await salesReturnRequestLine.isCreditMemoExpected) {
                quantityCredited =
                    (
                        await context
                            .queryAggregate(xtremSales.nodes.SalesReturnRequestLineSalesCreditMemoLine, {
                                filter: {
                                    linkedDocument: { _id: salesReturnRequestLine._id },
                                },
                                group: {
                                    linkedDocument: { _id: { _by: 'value' } },
                                },
                                values: {
                                    quantity: { sum: true },
                                },
                            })
                            .toArray()
                    )[0]?.values.quantity.sum || 0;
            }
            if (await salesReturnRequestLine.isReceiptExpected) {
                quantityReceived =
                    (
                        await context
                            .queryAggregate(xtremSales.nodes.SalesReturnRequestLineToSalesReturnReceiptLine, {
                                filter: {
                                    linkedDocument: { _id: salesReturnRequestLine._id },
                                },
                                group: {
                                    linkedDocument: { _id: { _by: 'value' } },
                                },
                                values: {
                                    quantity: { sum: true },
                                },
                            })
                            .toArray()
                    )[0]?.values.quantity.sum || 0;
            }
            if (
                (await salesReturnRequestLine.status) === 'closed' &&
                (await salesReturnRequestLine.isReceiptExpected)
            ) {
                finalQuantityInSalesUnit = quantityReceived;
            } else {
                finalQuantityInSalesUnit = baseQuantityInSalesUnit;
            }
        }
        return { finalQuantityInSalesUnit, quantityReceived, quantityCredited };
    }
}
