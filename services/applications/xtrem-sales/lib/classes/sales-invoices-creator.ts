import type { Context, decimal, Dict, NodeCreateData, UpdateAction } from '@sage/xtrem-core';
import { BusinessRuleError, date } from '@sage/xtrem-core';
import * as xtremSales from '../index';
import { BaseSalesDocumentsCreator } from './base-sales-documents-creator';

export class SalesInvoicesCreator extends BaseSalesDocumentsCreator {
    salesOutputDocuments: Dict<NodeCreateData<xtremSales.nodes.SalesInvoice>> = {};

    outputNode = xtremSales.nodes.SalesInvoice;

    salesDocumentLineDependency: 'salesOrderLines' | 'salesShipmentLines' = 'salesShipmentLines';

    numberOfName: 'numberOfInvoices' = 'numberOfInvoices';

    hasDiscountCharges = true;

    aggregateNode = xtremSales.nodes.SalesShipmentLineToSalesInvoiceLine;

    override logErrors = true;

    static override readonly parametersAreIncorrect: xtremSales.interfaces.CreateSalesOutputDocumentsReturnValues = {
        ...BaseSalesDocumentsCreator.parametersAreIncorrect,
        numberOfInvoices: 0,
    };

    constructor(
        public classContext: Context,
        public invoiceDate: date = date.today(),
    ) {
        super();
    }

    async controlInputLine(salesDocumentLine: xtremSales.nodes.SalesShipmentLine): Promise<void> {
        if ((await salesDocumentLine.invoiceStatus) === 'invoiced') {
            throw new BusinessRuleError(
                this.classContext.localize(
                    '@sage/xtrem-sales/classes__sales_invoice_creator__invalid_selected_lines_invoice_status',
                    'A shipment line is already invoiced.',
                ),
            );
        }
    }

    override async createDictKey(
        line: xtremSales.nodes.SalesShipmentLine,
        header: xtremSales.nodes.SalesShipment,
    ): Promise<void> {
        await super.createDictKey(line, header);
        if (await line.customerNumber) this.dictKey += String(await line.customerNumber);
        const shipToAddress = await header.shipToAddress;
        this.dictKey +=
            String(header._id) +
            String((await header.site)._id) +
            String((await header.shipToCustomer)._id) +
            String((await header.deliveryMode)._id) +
            String((await header.shipToCustomerAddress)._id) +
            String(shipToAddress?._id);
    }

    override async addHeaderDataToDocumentNode(line: xtremSales.nodes.SalesShipmentLine): Promise<void> {
        await super.addHeaderDataToDocumentNode(line);
        const header = await line.document;
        this.salesOutputDocuments[this.dictKey] = {
            ...this.salesOutputDocuments[this.dictKey],
            site: (await header.site)?._id,
            date: this.invoiceDate,
            currency: (await header.currency)?._id,
            paymentTerm: (await header.paymentTerm)?._id,
        };
    }

    override async prepareNewLineToDocumentNode(
        line: xtremSales.nodes.SalesShipmentLine,
        outputQuantity: decimal,
    ): Promise<number> {
        const SalesInvoiceLinesLength = await super.prepareNewLineToDocumentNode(line, outputQuantity);
        const lineDocument = await line.document;
        const stockSite = await lineDocument.stockSite;
        const primaryAddress = await stockSite.primaryAddress;
        const isTransferLineNote = await (await line.document).isTransferLineNote;

        let taxes: NodeCreateData<
            xtremSales.nodes.SalesInvoiceLineTax & {
                _sortValue?: number | undefined;
                _action: 'create' | 'update' | 'delete';
            }
        >[] = [];
        if (
            line.salesOrderLines &&
            (await line.salesOrderLines.length) > 0 &&
            (await (
                await line.salesOrderLines?.elementAt(0)
            )?.linkedDocument)
        ) {
            taxes =
                line.salesOrderLines && (await (await line.salesOrderLines?.elementAt(0))?.linkedDocument)
                    ? await xtremSales.functions.SalesInvoiceLib.addNewSalesInvoiceLineTaxDependency(
                          await (
                              await line.salesOrderLines?.elementAt(0)
                          )?.linkedDocument,
                      )
                    : [];
        }

        const lineData: NodeCreateData<xtremSales.nodes.SalesInvoiceLine> = {
            consumptionLinkedAddress: (await lineDocument.shipToCustomerAddress)?._id,
            consumptionAddress: await lineDocument.shipToAddress,
            providerSite: (await lineDocument.stockSite)?._id,
            providerSiteLinkedAddress: primaryAddress?._id,
            providerSiteAddress: (await primaryAddress?.address) ?? null,
            origin: 'shipment',
            grossPrice: await line.grossPrice,
            priceOrigin: await line.priceOrigin,
            priceReason: (await line.priceReason)?._id,
            isPriceDeterminated: await line.isPriceDeterminated,
            grossPriceDeterminated: await line.grossPriceDeterminated,
            priceOriginDeterminated: await line.priceOriginDeterminated,
            priceReasonDeterminated: (await line.priceReasonDeterminated)?._id,
            isExternalNote: isTransferLineNote ? await line.isExternalNote : false,
            externalNote: isTransferLineNote ? await line.externalNote : undefined,
            taxDate: await (await line.document).shippingDate,
            taxes,
            customerNumber: await line.customerNumber,
        };

        this.salesOutputDocuments[this.dictKey].lines![SalesInvoiceLinesLength - 1] = {
            ...this.salesOutputDocuments[this.dictKey].lines![SalesInvoiceLinesLength - 1],
            ...(lineData as any),
        };
        return SalesInvoiceLinesLength;
    }

    override async addNewBaseDocumentLineToSalesDocumentLineDependency(
        outputDocumentLinesLength: number,
        salesShipmentLine: xtremSales.nodes.SalesShipmentLine,
        outputQuantity: decimal,
    ): Promise<void> {
        await super.addNewBaseDocumentLineToSalesDocumentLineDependency(
            outputDocumentLinesLength,
            salesShipmentLine,
            outputQuantity,
        );
        this.salesOutputDocuments[this.dictKey].lines![outputDocumentLinesLength - 1].salesOrderLines =
            [] as NodeCreateData<
                xtremSales.nodes.SalesOrderLineToSalesInvoiceLine & {
                    _sortValue?: number;
                    _action: UpdateAction;
                }
            >[];
        const salesOrderLine = await salesShipmentLine.salesOrderLines?.find(
            async e => (await e.document)._id === salesShipmentLine._id,
        );
        const linkedDocument = await salesOrderLine?.linkedDocument;
        if (linkedDocument) {
            this.salesOutputDocuments[this.dictKey].lines![outputDocumentLinesLength - 1].salesOrderLines?.push({
                linkedDocument: linkedDocument?._id,
                quantity: outputQuantity,
                quantityInStockUnit: outputQuantity * (await linkedDocument.unitToStockUnitConversionFactor),
                amount: (await linkedDocument.netPrice) * outputQuantity,
            });
        }
    }

    // eslint-disable-next-line class-methods-use-this
    getCreateSalesOutputDocumentsStatus = () => 'salesShipmentIsInvoiced';

    override createConsoleLog(outputDocumentNumber: string, inputDocumentsNumbers: string) {
        this.classContext.logger.info(
            this.classContext.localize(
                '@sage/xtrem-sales/class__sales_invoice_creator__console_log_message',
                `The sales invoice was created from the following sales shipment numbers: {{inputDocumentsNumbers}}.`,
                {
                    outputDocumentNumber,
                    inputDocumentsNumbers,
                },
            ),
        );
    }

    createErrorConsoleLog(message: string, inputDocumentsNumbers: string): string | undefined {
        const errorMessage = this.classContext.localize(
            '@sage/xtrem-sales/class__sales_invoice_creator__console_log_error_message',
            `The sales invoice could not be created using the sales shipment numbers: {{inputDocumentsNumbers}}. Details: {{message}}`,
            {
                message,
                inputDocumentsNumbers,
            },
        );
        this.classContext.logger.error(errorMessage);
        return errorMessage;
    }
}
