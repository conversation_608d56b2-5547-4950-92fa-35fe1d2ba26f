import type {
    AnyN<PERSON>,
    AnyRecord,
    AsyncResponse,
    Collection,
    Context,
    decimal,
    Dict,
    Node,
    NodeCreateData,
    Reference,
    StaticThis,
    UnPromised,
    UpdateAction,
} from '@sage/xtrem-core';
import { asyncArray, BusinessRuleError, LocalizedError } from '@sage/xtrem-core';
import type * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremSales from '../index';
import { SalesReturnRequest, SalesReturnRequestLine } from '../nodes';

type NumberOfNameType =
    | 'numberOfShipments'
    | 'numberOfInvoices'
    | 'numberOfCreditMemos'
    | 'numberOfReturnRequests'
    | 'numberOfReturnReceipts';

export abstract class BaseSalesDocumentsCreator {
    abstract salesOutputDocuments: Dict<NodeCreateData<xtremSales.interfaces.AnyOutputDocumentHeader>>;

    abstract outputNode: StaticThis<xtremSales.interfaces.AnyOutputDocumentHeader>;

    abstract classContext: Context;

    abstract salesDocumentLineDependency:
        | 'salesOrderLines'
        | 'salesShipmentLines'
        | 'toInvoiceLines'
        | 'toReturnRequestLines';

    abstract numberOfName: NumberOfNameType;

    abstract getCreateSalesOutputDocumentsStatus(): string;

    abstract controlInputLine(
        salesDocumentLine: xtremSales.interfaces.AnyInputDocumentLine,
        options?: xtremSales.sharedFunctions.PrepareNodeCreateDataOptions,
    ): AsyncResponse<void>;

    abstract hasDiscountCharges: boolean;

    abstract aggregateNode: StaticThis<xtremSales.interfaces.AnyBaseLineToSalesDocumentLine>;

    inputDocumentsNumbers: Dict<Dict<string>> = {};

    protected logErrors: boolean = false;

    protected dictKey: string;

    protected lastAddedLineIndex: decimal;

    abstract createErrorConsoleLog(message: string, inputDocumentsNumbers: string): string | undefined;

    lineErrors: Array<xtremSales.interfaces.CreateSalesOutputDocumentsLineErrorReturnValues> = [];

    public set logErrorsProperty(logErrors: boolean) {
        this.logErrors = logErrors;
    }

    public get logErrorsProperty(): boolean {
        return this.logErrors;
    }

    static readonly parametersAreIncorrect: xtremSales.interfaces.CreateSalesOutputDocumentsReturnValues = {
        status: 'parametersAreIncorrect',
        lineErrors: [],
        documentsCreated: [],
        errorMessage: [],
    };

    /**
     * In this method we are preparing dicts and arrays that will be used to
     * create new output document
     * @param salesDocumentLines
     */
    async prepareNodeCreateData(
        salesDocumentLines: Array<{
            salesDocumentLine: xtremSales.interfaces.AnyInputDocumentLine;
            quantityToProcess?: decimal;
        }>,
        options?: xtremSales.sharedFunctions.PrepareNodeCreateDataOptions,
    ): Promise<this> {
        await asyncArray(salesDocumentLines).forEach(async line => {
            const header = await line.salesDocumentLine.document;
            await this.createDictKey(line.salesDocumentLine, header);
            this.lastAddedLineIndex = this.salesOutputDocuments[this.dictKey]?.lines!.length ?? 0;
            try {
                await this.controlInputLine(line.salesDocumentLine, options);
                await this.createOrAppendNewDocument(line.salesDocumentLine, line.quantityToProcess, options);
            } catch (error) {
                if (!(error instanceof LocalizedError)) throw error;

                await this.processInputErrors(error, line.salesDocumentLine);
            }
        });
        return this;
    }

    async createOrAppendNewDocument(
        line: xtremSales.interfaces.AnyInputDocumentLine,
        providedQuantity?: number,
        options?: xtremSales.sharedFunctions.PrepareNodeCreateDataOptions,
    ): Promise<void> {
        const outputQuantity = !providedQuantity ? await this.calculateMissingQuantity(line) : providedQuantity;

        if (outputQuantity <= 0) {
            if (!options?.isForFinanceCheck) {
                throw new BusinessRuleError(
                    this.classContext.localize(
                        '@sage/xtrem-sales/class__base_sales_documents_creator__no_quantity_left',
                        'No quantity in sales unit left.',
                    ),
                );
            }
            return;
        }

        const documentNumber = (await line.document).$.isValueDeferred('number')
            ? (await line.document)._id.toString()
            : await (
                  await line.document
              ).number;

        if (Object.prototype.hasOwnProperty.call(this.salesOutputDocuments, this.dictKey)) {
            await this.addNewLineToDocumentNode(line, outputQuantity);
            this.inputDocumentsNumbers[this.dictKey][documentNumber] = documentNumber;
        } else {
            await this.createNewDocumentNode(line, outputQuantity);
            this.inputDocumentsNumbers[this.dictKey] = {
                [documentNumber]: documentNumber,
            };
        }
    }

    async calculateMissingQuantity(line: xtremSales.interfaces.AnyInputDocumentLine): Promise<number> {
        return Math.abs(
            (await line.quantity) -
                (await (<typeof BaseSalesDocumentsCreator>this.constructor).getTotalQuantityFromLines(
                    {
                        linkedDocument: { _id: line._id },
                    },
                    this.classContext,
                    this.aggregateNode,
                )),
        );
    }

    async createDictKey(
        _line: xtremSales.interfaces.AnyInputDocumentLine,
        header: xtremSales.interfaces.AnyInputDocumentHeader,
    ): Promise<void> {
        const billToAddress = await header.billToAddress;
        const billToContact = await header.billToContact;
        this.dictKey =
            String((await header.incoterm)?._id) +
            String((await header.billToCustomer)?._id) +
            String((await header.billToLinkedAddress)?._id) +
            String(billToAddress?._id) +
            String(billToContact?._id) +
            String(!(header instanceof SalesReturnRequest) ? (await header.currency)?._id : undefined) +
            String(!(header instanceof SalesReturnRequest) ? (await header.paymentTerm)?._id : undefined);
    }

    async addNewBaseDocumentLineDiscountChargeDependency(
        outputDocumentLinesLength: number,
        salesDocumentLine: xtremSales.interfaces.AnyInputDocumentLine,
    ): Promise<void> {
        const currentLine = this.salesOutputDocuments[this.dictKey].lines![outputDocumentLinesLength - 1];
        if (!(salesDocumentLine instanceof SalesReturnRequestLine) && this.hasDiscountCharges) {
            const currentLineWithDiscountCharges =
                currentLine as NodeCreateData<xtremSales.interfaces.AnyOutputDocumentLineWithDiscountCharges>;
            currentLineWithDiscountCharges.discountCharges = [] as NodeCreateData<
                xtremSales.nodes.SalesShipmentLineDiscountCharge & {
                    _sortValue?: number;
                    _action: UpdateAction;
                }
            >[];
            const discountCharges =
                salesDocumentLine.discountCharges as Collection<xtremMasterData.nodes.BaseLineDiscountCharge>;
            await discountCharges.forEach(async discountCharge => {
                currentLineWithDiscountCharges.discountCharges?.push({
                    sign: await discountCharge.sign,
                    valueType: await discountCharge.valueType,
                    calculationBasis: await discountCharge.calculationBasis,
                    calculationRule: await discountCharge.calculationRule,
                    basis: await discountCharge.basis,
                    value: await discountCharge.value,
                    basisDeterminated: await discountCharge.basisDeterminated,
                    valueDeterminated: await discountCharge.valueDeterminated,
                    amount: await discountCharge.amount,
                });
            });
        }
    }

    async addNewLineToDocumentNode(
        line: xtremSales.interfaces.AnyInputDocumentLine,
        outputQuantity: decimal,
    ): Promise<decimal> {
        const outputDocumentLinesLength: number = await this.prepareNewLineToDocumentNode(line, outputQuantity);

        await this.addNewBaseDocumentLineToSalesDocumentLineDependency(outputDocumentLinesLength, line, outputQuantity);
        await this.addNewBaseDocumentLineDiscountChargeDependency(outputDocumentLinesLength, line);

        return outputDocumentLinesLength;
    }

    async prepareNewLineToDocumentNode(
        line: xtremSales.interfaces.AnyInputDocumentLine | xtremSales.nodes.SalesReturnRequestLine,
        outputQuantity: decimal,
    ): Promise<number> {
        const isTransferLineNote = await (await line.document).isTransferLineNote;
        const lineData: NodeCreateData<xtremSales.interfaces.AnyOutputDocumentLine> = {
            item: (await line.item)?._id,
            itemDescription: await line.itemDescription,
            quantity: outputQuantity,
            unit: (await line.unit)?._id,
            unitToStockUnitConversionFactor: await line.unitToStockUnitConversionFactor,
            stockUnit: (await line.stockUnit)?._id,
            storedAttributes: await line.storedAttributes,
            storedDimensions: await line.storedDimensions,
            internalNote: isTransferLineNote ? await line.internalNote : undefined,
        };
        const outputLines = this.salesOutputDocuments[this.dictKey]
            .lines as NodeCreateData<xtremSales.interfaces.AnyOutputDocumentLine>[];
        return outputLines.push(lineData);
    }

    async createNewDocumentNode(
        line: xtremSales.interfaces.AnyInputDocumentLine,
        outputQuantity: decimal,
    ): Promise<void> {
        await this.addHeaderDataToDocumentNode(line);
        await this.addNewLineToDocumentNode(line, outputQuantity);
    }

    async addHeaderDataToDocumentNode(line: xtremSales.interfaces.AnyInputDocumentLine): Promise<void> {
        const header = await line.document;
        this.salesOutputDocuments[this.dictKey] = {
            billToCustomer: (await header.billToCustomer)?._id,
            billToLinkedAddress: (await header.billToLinkedAddress)?._id,
            incoterm: (await header.incoterm)?._id,
            billToAddress: await header.billToAddress,
            billToContact: await header.billToContact,
            lines: [],
        } as NodeCreateData<xtremSales.interfaces.AnyOutputDocumentHeader>;
    }

    async addNewBaseDocumentLineToSalesDocumentLineDependency(
        outputDocumentLinesLength: number,
        linkedDocument: xtremSales.interfaces.AnyInputDocumentLine,
        outputQuantity: decimal,
    ): Promise<void> {
        const salesOutputDocumentLines = this.salesOutputDocuments[this.dictKey]
            .lines as NodeCreateData<xtremSales.interfaces.AnyOutputDocumentLine>[];
        const lastSalesOuputDocumentLine = salesOutputDocumentLines[outputDocumentLinesLength - 1];
        (lastSalesOuputDocumentLine as AnyRecord)[this.salesDocumentLineDependency] = [
            {
                linkedDocument: linkedDocument._id,
                quantity: outputQuantity,
                quantityInStockUnit: outputQuantity * (await linkedDocument.unitToStockUnitConversionFactor),
                amount: !(linkedDocument instanceof SalesReturnRequestLine)
                    ? (await linkedDocument.netPrice) * outputQuantity
                    : 0,
            } as NodeCreateData<
                Node & {
                    _sortValue?: number;
                    _action: UpdateAction;
                }
            >,
        ];
    }

    /**
     * This method is used to mass creation of sales documents
     */
    async createSalesOutputDocuments(): Promise<xtremSales.interfaces.CreateSalesOutputDocumentsReturnValues> {
        let successNumber = 0;
        const documentsCreated: xtremSales.interfaces.AnyOutputDocumentHeader[] = [];
        const errorMessage: xtremSales.interfaces.OutputDocumentCreationError[] = [];
        await asyncArray(Object.keys(this.salesOutputDocuments)).forEach(async key => {
            const inputDocumentsNumber = Object.keys(this.inputDocumentsNumbers[key]).reduce(
                (prev, number) => (prev ? `${prev}, ${number}` : number),
                '',
            );
            await this.classContext.runInWritableContext(async writableContext => {
                try {
                    const documentCreated = await writableContext.create(this.outputNode, {
                        ...this.salesOutputDocuments[key],
                    });
                    await documentCreated.$.save();
                    this.createConsoleLog('createSalesOutputDocuments', inputDocumentsNumber);
                    documentsCreated.push(documentCreated);
                    successNumber += 1;
                } catch (error) {
                    if (!(error instanceof LocalizedError)) throw error;

                    if (this.logErrors) {
                        const message = error.getMessageAndDiagnosesText(writableContext.diagnoses);
                        errorMessage.push({
                            message,
                            loggerMessage: this.createErrorConsoleLog(message, inputDocumentsNumber) || '',
                        });
                    } else {
                        throw error;
                    }
                }
            });
        });
        return {
            status: this.getCreateSalesOutputDocumentsStatus(),
            lineErrors: this.lineErrors,
            documentsCreated,
            [this.numberOfName]: successNumber,
            errorMessage,
        };
    }

    protected createConsoleLog(outputDocumentNumber: string, inputDocumentsNumbers: string) {
        this.classContext.logger.info(
            `Document number ${outputDocumentNumber} was created from document numbers: ${inputDocumentsNumbers}.`,
        );
    }

    protected async processInputErrors(error: Error, line: xtremSales.interfaces.AnyInputDocumentLine): Promise<void> {
        const number = (await line.document).$.isValueDeferred('number')
            ? (await line.document)._id
            : await (
                  await line.document
              ).number;
        if (this.logErrors) {
            this.createErrorConsoleLog(
                error.message ?? String(error),
                `${number} - ${String(await (await line.item).name)}`,
            );
        } else {
            throw error;
        }
    }

    static async fillTempSalesDocumentDicts<
        LineT extends xtremSales.interfaces.AnyDocumentLineWithRelatedDocuments,
        DocumentT extends xtremSales.interfaces.AnyDocumentHeaderWithRelatedDocuments = UnPromised<LineT['document']>,
        DictNameT extends keyof DocumentT = keyof DocumentT,
        LinesNameT extends keyof LineT = keyof LineT,
    >(
        action: string,
        linesName: LinesNameT, // 'salesOrderLines' | 'salesShipmentLines' | 'toInvoiceLines' | 'salesReturnRequestLines',
        tempDict: DictNameT, //  '__salesOrders' | '__salesShipments' | '__salesInvoices' | '__salesReturnRequests',
        line: LineT,
    ): Promise<void> {
        const lines = line[linesName] as unknown as Collection<
            Node & {
                quantity: Promise<decimal>;
                quantityInStockUnit: Promise<decimal>;
                document: Reference<DocumentT>;
                linkedDocument: Reference<Node & { document: Reference<Node> }>;
            }
        >;
        if ((await lines.length) > 0) {
            const salesDocumentLine = await lines.find(async e => (await e.document)?._id === line._id);
            if (action !== 'delete') {
                await salesDocumentLine!.$.set({
                    quantity: await line.quantity,
                    quantityInStockUnit: (await line.quantity) * (await line.unitToStockUnitConversionFactor),
                });
            }

            const linkedDocument = await salesDocumentLine!.linkedDocument;
            const salesDocumentLineDict = {
                _id: linkedDocument._id,
                action,
                aggregateNodeId: salesDocumentLine!._id,
            };
            const dict = ((await line.document) as DocumentT)![tempDict] as unknown as Dict<
                (typeof salesDocumentLineDict)[]
            >;
            const dictKey = (await linkedDocument.document)._id;
            if (Object.prototype.hasOwnProperty.call(dict, dictKey)) {
                dict[dictKey].push(salesDocumentLineDict);
            } else {
                dict[dictKey] = [salesDocumentLineDict];
            }
        }
    }

    static async checkTotalQuantityBaseOnStatus(
        data: xtremSales.interfaces.CheckTotalQuantityBaseOnStatus,
    ): Promise<decimal> {
        let filterArguments: any = {
            linkedDocument: { _id: data.linkedDocument._id },
        };

        if (data.dictLine.action === 'delete') {
            filterArguments = {
                linkedDocument: { _id: data.linkedDocument._id },
                _id: { _ne: data.dictLine.aggregateNodeId },
            };
        }

        const totalQuantity = await this.getTotalQuantityFromLines(filterArguments, data.context, data.aggregateNode);

        await this.validateQuantityAmount(data, totalQuantity);

        return totalQuantity;
    }

    static async validateQuantityAmount(
        data: xtremSales.interfaces.CheckTotalQuantityBaseOnStatus,
        totalQuantity: decimal,
    ): Promise<void> {
        if (data.dictLine.action !== 'delete' && totalQuantity > (await data.linkedDocument.quantity)) {
            throw new BusinessRuleError(
                data.context.localize(
                    '@sage/xtrem-sales/class__base_sales_documents_creator__remaining_quantity',
                    'The sales document line quantity cannot be larger than the remaining quantity in the related document.',
                ),
            );
        }
    }

    static async setDocumentLineStatus(
        dictLine: xtremSales.interfaces.SalesDocumentLineUpdateInterface,
        line: xtremSales.interfaces.AnyInputDocumentLineWithStatus,
        aggregateNode: StaticThis<xtremSales.interfaces.AnyBaseLineToSalesDocumentLine>,
    ): Promise<number> {
        const total = await this.checkTotalQuantityBaseOnStatus({
            dictLine,
            context: line.$.context,
            linkedDocument: line,
            aggregateNode,
        });
        if (dictLine.action === 'delete') {
            if (!total) {
                return 1; // xtremSales.enums.SalesDocumentInvoiceStatus.notInvoiced || xtremSales.enums.SalesDocumentShippingStatus.notShipped;
            }
            return 2;
        }
        if (total === (await line.quantity)) {
            return 3;
        }
        if (!total) {
            return 1;
        }
        return 2;
    }

    static async updateRelatedSalesDocumentsStatuses<
        LineT extends xtremSales.interfaces.AnyDocumentLineWithRelatedDocuments,
        DocumentT extends xtremSales.interfaces.AnyDocumentHeaderWithRelatedDocuments = UnPromised<LineT['document']>,
        DictNameT extends keyof DocumentT = keyof DocumentT,
    >(data: xtremSales.interfaces.UpdateRelatedSalesDocumentsStatuses<LineT, DocumentT, DictNameT>): Promise<void> {
        let statusNumber: number;
        // waiting for bulk update
        await asyncArray(data.salesDocumentsArray).forEach(async salesDocument => {
            const tmpDict = data.classInstance[data.tempDict] as unknown as Dict<
                xtremSales.interfaces.SalesDocumentLineUpdateInterface[]
            >;
            const salesDocumentLines =
                salesDocument.lines as Collection<xtremSales.interfaces.AnyDocumentLineWithRelatedDocuments>;
            await asyncArray(tmpDict[salesDocument._id]).forEach(async dictLine => {
                const line = (await salesDocumentLines.find(
                    findLine => dictLine._id === findLine._id,
                )) as xtremSales.interfaces.AnyInputDocumentLineWithStatus;
                statusNumber = await this.setDocumentLineStatus(dictLine, line, data.aggregateNode);
                await line.$.set({ [data.statusType]: data.enumDataType.stringValue(statusNumber) });
                if (data.statusType === 'shippingStatus') {
                    await line.$.set({
                        status: xtremSales.enums.salesOrderStatusDataType.stringValue(statusNumber + 1) as any,
                    });
                }
            });

            const numberOfSalesDocuments = await salesDocument.lines.length;
            const sumOfDocumentsStatuses = await salesDocumentLines.reduce(async (prev, line) => {
                return (
                    prev +
                    +data.enumDataType!.numberValue(
                        (await (line as unknown as AnyNode).$.getValue(data.statusType)) as string,
                    )
                );
            }, 0);
            // 1 === xtremSales.enums.SalesDocumentInvoiceStatus.notInvoiced and xtremSales.enums.SalesDocumentShippingStatus.notShipped
            if (sumOfDocumentsStatuses / numberOfSalesDocuments === 1) {
                statusNumber = 1;
            } else if (sumOfDocumentsStatuses / numberOfSalesDocuments === 3) {
                statusNumber = 3;
            } else {
                statusNumber = 2;
            }

            await salesDocument.$.set({ [data.statusType]: data.enumDataType.stringValue(statusNumber) });
            if (data.statusType === 'shippingStatus') {
                await salesDocument.$.set({
                    status: xtremSales.enums.salesOrderStatusDataType.stringValue(statusNumber + 1) as any,
                });
            }

            salesDocument.__skipPrepare = true;
            await salesDocument.$.save();
        });
    }

    static async getTotalQuantityFromLines(
        filterArguments: any,
        context: Context,
        aggregateNode: StaticThis<xtremSales.interfaces.AnyBaseLineToSalesDocumentLine>,
    ): Promise<decimal> {
        return (
            (
                await context
                    .queryAggregate(aggregateNode, {
                        filter: {
                            ...filterArguments,
                        },
                        group: {
                            linkedDocument: { _id: { _by: 'value' } },
                        },
                        values: {
                            quantity: { sum: true },
                        },
                    })
                    .toArray()
            )[0]?.values.quantity.sum ?? 0
        );
    }
}
