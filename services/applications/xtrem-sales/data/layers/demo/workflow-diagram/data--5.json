{"edges": [{"id": "oke5suygbr8", "source": "lenjk7oaxer", "target": "kw3j9myixfg", "sourceHandle": "out"}, {"id": "3didtvrn3wa", "source": "kw3j9myixfg", "target": "k5tiaqja82k", "sourceHandle": "out"}, {"id": "ag1ujibjjom", "source": "k5tiaqja82k", "target": "ov1mqnak3o7", "sourceHandle": "out"}, {"id": "27guzs5wlju", "source": "ov1mqnak3o7", "target": "zf1h553nb1t", "sourceHandle": "out"}, {"id": "6zistg7mwdc", "source": "zf1h553nb1t", "target": "96i6qugwcwf", "sourceHandle": "out-false"}, {"id": "kgff4aymmp1", "source": "zf1h553nb1t", "target": "qr0g2a3gb78", "sourceHandle": "out-true"}, {"id": "zlb29k89jq5", "source": "qr0g2a3gb78", "target": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sourceHandle": "out"}], "nodes": [{"id": "lenjk7oaxer", "data": {"node": "@sage/xtrem-sales/SalesOrder", "topic": "SalesOrder/created", "subtitle": "@sage/xtrem-sales/SalesOrder", "dataEntity": "_id:294", "localizedTitle": {"base": "New sales order", "fr-FR": "Nouvelle commande de vente"}, "stepVariables": [{"path": "salesOrder._id", "type": "String", "title": "Created SalesOrder ID"}], "outputVariableName": "salesOrder"}, "type": "node-created", "width": 236, "height": 68, "origin": [0.5, 0], "dragging": true, "position": {"x": 353, "y": 71}, "selected": false, "positionAbsolute": {"x": 353, "y": 71}}, {"id": "kw3j9myixfg", "data": {"node": "@sage/xtrem-sales/SalesOrder", "subtitle": "@sage/xtrem-sales/SalesOrder", "dataEntity": "_id:294", "localizedTitle": {"base": "Read sales order details", "fr-FR": "<PERSON><PERSON> les détails de la commande de vente"}, "stepVariables": [{"path": "salesOrder.number", "type": "String", "title": "Number"}, {"path": "salesOrder.status", "type": "@sage/xtrem-sales/SalesOrderStatus", "title": "Status"}], "inputVariableName": "salesOrder._id", "useNullIfNotFound": null, "outputVariableName": "salesOrder", "selectedProperties": {"number": {"id": "number", "key": "number", "data": {"kind": "SCALAR", "name": "number", "type": "String", "label": "Number", "canSort": true, "canFilter": true, "isCollection": false}, "label": "Number", "labelKey": "Number", "labelPath": "Number"}, "status": {"id": "status", "key": "status", "data": {"kind": "ENUM", "name": "status", "type": "@sage/xtrem-sales/SalesOrderStatus", "label": "Status", "canSort": true, "canFilter": true, "enumValues": ["quote", "pending", "inProgress", "closed"], "isCollection": false}, "label": "Status", "labelKey": "Status", "labelPath": "Status"}}}, "type": "entity-read", "width": 281, "height": 68, "origin": [0.5, 0], "dragging": true, "position": {"x": 330.0969238641752, "y": 185.74140566699612}, "selected": false, "positionAbsolute": {"x": 330.0969238641752, "y": 185.74140566699612}}, {"id": "k5tiaqja82k", "data": {"subtitle": null, "localizedTitle": {"base": "Confirm", "fr-FR": "Confirmer"}, "stepVariables": [], "inputVariableName": "salesOrder.number", "outputVariableName": "confirmation<PERSON>tatus"}, "type": "sales-order-confirm", "width": 180, "height": 64, "origin": [0.5, 0], "dragging": true, "position": {"x": 381.4323758897941, "y": 297.8909040127071}, "selected": false, "positionAbsolute": {"x": 381.4323758897941, "y": 297.8909040127071}}, {"id": "ov1mqnak3o7", "data": {"subtitle": null, "localizedTitle": {"base": "Allocate", "fr-FR": "<PERSON><PERSON><PERSON>"}, "stepVariables": [{"path": "salesOrderAllocation.allocationStatus", "type": "@sage/xtrem-stock-data/StockAllocationStatusEnum", "title": "Allocation status"}], "inputVariableName": "salesOrder._id", "outputVariableName": "salesOrderAllocation"}, "type": "sales-order-allocate", "width": 180, "height": 64, "origin": [0.5, 0], "dragging": true, "position": {"x": 381.66849039174724, "y": 411.2901080869219}, "selected": false, "positionAbsolute": {"x": 381.66849039174724, "y": 411.2901080869219}}, {"id": "zf1h553nb1t", "data": {"filters": {"filters": [{"id": "salesOrderAllocation", "_id": "1", "data": {"kind": "SCALAR", "name": "salesOrderAllocation", "type": "@sage/xtrem-stock-data/StockAllocationStatusEnum", "label": "Sales order allocation", "canSort": false, "canFilter": true}, "type": "@sage/xtrem-stock-data/StockAllocationStatusEnum", "label": "Sales order allocation", "property": {"id": "salesOrderAllocation.allocationStatus", "key": "salesOrderAllocation", "data": {"kind": "SCALAR", "name": "allocationStatus", "type": "String", "label": "Allocation status", "canSort": false, "canFilter": true}, "label": "Allocation status", "title": "Allocation status", "labelKey": "Allocation status", "labelPath": "Allocation status", "canBeExpanded": false, "canBeSelected": true}, "labelPath": "Allocation status", "filterType": "equals", "filterValue": "allocated"}], "parameters": []}, "subtitle": null, "localizedTitle": {"base": "Allocated?", "fr-FR": "Allouée ?"}, "stepVariables": []}, "type": "condition", "width": 150, "height": 80, "origin": [0.5, 0], "dragging": true, "position": {"x": 397.90277099609375, "y": 512.1406097412109}, "selected": false, "positionAbsolute": {"x": 397.90277099609375, "y": 512.1406097412109}}, {"id": "96i6qugwcwf", "data": {"node": null, "report": "sales_order_send", "subtitle": "sales_order_send", "addressList": [{"_id": "-1", "value": "<EMAIL>", "_action": "create", "addressType": "To"}], "stepVariables": [], "localizedSubject": {"base": "Allocation failed", "fr-FR": "Échec d'allocation"}, "reportParameters": {}}, "type": "send-email", "width": 207, "height": 68, "origin": [0.5, 0], "dragging": true, "position": {"x": 691.9027709960938, "y": 617.1406097412109}, "selected": false, "positionAbsolute": {"x": 691.9027709960938, "y": 617.1406097412109}}, {"id": "qr0g2a3gb78", "data": {"subtitle": null, "localizedTitle": {"base": "Create shipment", "fr-FR": "Créer l'expédition"}, "stepVariables": [], "inputVariableName": "salesOrder._id", "outputVariableName": "salesOrderShipment"}, "type": "sales-order-create-shipment", "width": 228, "height": 64, "origin": [0.5, 0], "dragging": true, "position": {"x": 359.1917346017358, "y": 653.9395471464884}, "selected": false, "positionAbsolute": {"x": 359.1917346017358, "y": 653.9395471464884}}, {"id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "data": {"node": null, "report": "sales_order_send", "subtitle": "sales_order_send", "addressList": [{"_id": "-1", "value": "<EMAIL>", "_action": "create", "addressType": "To"}], "stepVariables": [], "localizedSubject": {"base": "Order is ready to ship", "fr-FR": "La commande est prête à être expédiée"}, "reportParameters": {}}, "type": "send-email", "width": 207, "height": 68, "origin": [0.5, 0], "dragging": true, "position": {"x": 369.90277099609375, "y": 765.8647979736329}, "selected": true, "positionAbsolute": {"x": 369.90277099609375, "y": 765.8647979736329}}]}