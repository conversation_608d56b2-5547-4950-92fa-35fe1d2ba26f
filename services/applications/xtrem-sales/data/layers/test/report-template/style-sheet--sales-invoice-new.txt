@page {
    size: auto;
    margin: 15mm 15mm 15mm 15mm;
}

body {
    font-size: 12px;
    margin: 0px;
    color: var(--textAndLabels);
    -webkit-print-color-adjust: exact;
}

.watermark {
    position: fixed;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    z-index: -1;

    color: #8d8f8e;

    font-family: 'Arial';
    font-size: 200px;
    font-weight: bolder;
    display: grid;
    justify-content: center;
    align-content: center;
    opacity: 0.2;
    transform: rotate(-45deg);
}

.address-frame {
    /*  border-style:solid;*/
    border-style: none;
    position: fixed;
    top: 25mm;
    left: 85mm;
    width: 10cm;
    height: 45mm;
}

.po-title {
    padding: 65px 0px 10px 0px;
}

.main {
    font-family: Helvetica;
}

h1,
h2,
h3,
h4,
th {
    color: var(--themePrimary);
}

table {
    font-size: 12px;
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 8px;
}

table tbody tr {
    border-bottom: 1px solid var(--tableSeparator);
}

table th {
    border-bottom: 2px solid var(--themePrimary);
    border-right: 1px solid var(--tableSeparator);
    padding: 4px;
    font-size: 12px;
}

table th:last-child {
    border-right: none;
}

table th:first-child {
    padding-left: 0;
}

table td {
    font-size: 12px;
    vertical-align: top;
    border-bottom: 1px solid var(--tableSeparator);
    border-right: 1px solid var(--tableSeparator);
    padding: 4px;
}

table td:last-child {
    border-right: none;
    padding-right: 0;
}
table td:first-child {
    padding-left: 0;
}

table .column-left {
    text-align: left;
}

table .column-right {
    text-align: right;
    white-space: nowrap;
}

table .column-center {
    text-align: center;
}

.header-table {
    width: 100%;
    margin: 25px 0px 25px 0px;
}

.header-table td {
    vertical-align: top;
}

.lines-table {
    margin: 0px 0px 25px 0px;
}

.normal-black {
    font-weight: normal;
    color: black;
    font-family: Helvetica;
    border-bottom: none;
}

table.report-container {
    page-break-after: always;
}
thead.report-header {
    display: table-header-group;
}
tfoot.report-footer {
    display: table-footer-group;
}

table.report-container div.article {
    page-break-inside: avoid;
}
