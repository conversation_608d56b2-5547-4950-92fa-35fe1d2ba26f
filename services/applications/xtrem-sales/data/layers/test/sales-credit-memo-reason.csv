"id";"_vendor";"is_active";"name";"description"
"TEST_POS_EXCHANGE";;"Y";"{""en"":""POS Exchange""}";"{""en"":""Reason to use when doing a POS exchange""}"
"TEST_REFERRAL_CREDIT";;"Y";"{""en"":""Referral Credit""}";"{""en"":""Reason to use when creating a referral credit""}"
"TEST_REFUND";;"Y";"{""en"":""Refund""}";"{""en"":""Buyer overpaid an original invoice""}"
"TEST_UNAPPLIED_PAYMENT";;"Y";"{""en"":""Unapplied Payment""}";"{""en"":""Reason to use when payment is unapplied""}"
"TEST_CORRECTION";;"Y";"{""en"":""Correction""}";"{""en"":""Correction of an invoice error""}"
"TEST_PRICE_CHANGE";;"Y";"{""en"":""Price Change""}";"{""en"":""Purchase price discounted""}"
"TEST_INCOMPLETE_ORDER";;"Y";"{""en"":""Incomplete Order""}";"{""en"":""Incomplete order shipped""}"
"TEST_WARRANTY";;"Y";"{""en"":""Warranty""}";"{""en"":""Goods faulty within warranty peroiod""}"
