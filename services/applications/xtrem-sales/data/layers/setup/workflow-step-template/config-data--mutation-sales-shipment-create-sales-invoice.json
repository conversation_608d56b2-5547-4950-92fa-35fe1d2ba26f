{"selector": "{ _id number}", "stepVariables": [{"node": "SalesShipment", "path": "salesShipment._id", "type": "IntReference", "title": "Sales shipment / 🆔", "isCustom": false}, {"path": "salesInvoices._id", "type": "array", "title": "salesInvoices"}], "localizedTitle": {"base": "Sales shipment - create sales invoice", "en-US": "Sales shipment - create sales invoice"}, "actionParameters": [], "mutationArguments": [{"name": "salesShipment", "node": "SalesShipment", "type": "reference", "value": "salesShipment._id", "origin": "fromVariable", "isMandatory": true}, {"name": "isSafeToRetry", "type": "boolean", "value": true, "origin": "manual"}, {"name": "invoiceDate", "type": "date", "origin": "fromParameter"}], "mutationNaturalKey": "SalesShipment|createSalesInvoicesFromShipment", "outputVariableName": "salesInvoices"}