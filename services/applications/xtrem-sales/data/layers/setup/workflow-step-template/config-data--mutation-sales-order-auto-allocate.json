{"selector": "{_id status allocationStatus}", "localizedTitle": {"base": "Sales Order - allocate", "en-US": "Sales Order - allocate"}, "stepVariables": [{"node": "SalesOrder", "path": "salesOrder._id", "type": "reference", "title": "salesOrder"}], "actionParameters": [], "mutationArguments": [{"name": "salesOrder", "node": "SalesOrder", "type": "reference", "value": "salesOrder._id", "origin": "fromVariable"}, {"name": "requestType", "type": "enum", "value": "allocation", "origin": "manual", "enumType": "@sage/xtrem-stock-data/AllocationRequestType", "isMandatory": true}, {"name": "isSafeToRetry", "type": "boolean", "value": true, "origin": "manual", "isMandatory": false}], "mutationNaturalKey": "SalesOrder|autoAllocate|start", "outputVariableName": "salesOrder"}