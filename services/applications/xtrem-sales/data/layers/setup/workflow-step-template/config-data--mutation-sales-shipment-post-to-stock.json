{"selector": "{ _id status }", "stepVariables": [{"node": "SalesShipment", "path": "salesShipment._id", "type": "IntReference", "title": "Sales shipment / 🆔", "isCustom": false}, {"path": "salesShipment._id", "type": "object", "title": "salesShipment"}], "localizedTitle": {"base": "Sales shipment - Post to stock", "en-US": "Sales shipment - Post to stock"}, "actionParameters": [], "mutationArguments": [{"name": "salesShipment", "node": "SalesShipment", "type": "reference", "value": "salesShipment._id", "origin": "fromVariable", "isMandatory": true}, {"name": "isSafeToRetry", "type": "boolean", "value": true, "origin": "manual", "isMandatory": false}], "mutationNaturalKey": "SalesShipment|post|start", "outputVariableName": "salesShipment"}