{"selector": "{_id status}", "localizedTitle": {"base": "Sales Order - confirm", "en-US": "Sales Order - confirm"}, "stepVariables": [{"node": "SalesOrder", "path": "salesOrder._id", "type": "reference", "title": "salesOrder"}], "actionParameters": [], "mutationArguments": [{"name": "salesOrder", "node": "SalesOrder", "type": "reference", "value": "salesOrder._id", "origin": "fromVariable"}, {"name": "isSafeToRetry", "type": "boolean", "value": true, "origin": "manual", "isMandatory": false}], "mutationNaturalKey": "SalesOrder|confirmSalesOrder", "outputVariableName": "salesOrder"}