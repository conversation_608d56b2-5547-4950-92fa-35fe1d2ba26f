{"selector": "{ _id status}", "stepVariables": [{"node": "SalesOrder", "path": "salesOrder._id", "type": "IntReference", "title": "Sales order / 🆔", "isCustom": false}, {"node": "SalesOrder", "path": "salesOrder._id", "type": "reference", "title": "salesOrder"}], "localizedTitle": {"base": "Sales order - closes", "en-US": "Sales order - close"}, "actionParameters": [], "mutationArguments": [{"name": "salesOrder", "node": "SalesOrder", "type": "reference", "value": "salesOrder._id", "origin": "fromVariable", "isMandatory": true}, {"name": "isSafeToRetry", "type": "boolean", "value": true, "origin": "manual", "isMandatory": false}, {"name": "controlOrderLinks", "type": "boolean", "value": true, "origin": "manual", "isMandatory": false}], "mutationNaturalKey": "SalesOrder|closeSalesOrder", "outputVariableName": "salesOrder"}