{"selector": "{ _id status }", "stepVariables": [{"node": "SalesInvoice", "path": "salesInvoice._id", "type": "IntReference", "title": "Sales invoice / 🆔", "isCustom": false}, {"path": "salesInvoice._id", "type": "object", "title": "salesInvoice"}], "localizedTitle": {"base": "Sales invoice - post", "en-US": "Sales invoice - post"}, "actionParameters": [], "mutationArguments": [{"name": "invoice", "node": "SalesInvoice", "type": "reference", "value": "salesInvoice._id", "origin": "fromVariable", "isMandatory": true}, {"name": "isSafeToRetry", "type": "boolean", "value": true, "origin": "manual", "isMandatory": false}], "mutationNaturalKey": "SalesInvoice|postInvoice|start", "outputVariableName": "salesInvoice"}