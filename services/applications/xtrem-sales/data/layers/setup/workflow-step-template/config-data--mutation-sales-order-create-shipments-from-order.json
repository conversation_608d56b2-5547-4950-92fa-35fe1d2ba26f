{"selector": "{ _id status}", "stepVariables": [{"node": "SalesOrder", "path": "salesOrder._id", "type": "IntReference", "title": "Sales order / 🆔", "isCustom": false}, {"path": "salesShipments._id", "type": "array", "title": "salesShipments"}], "localizedTitle": {"base": "Sales order - create shipments from order", "en-US": "Sales order - create shipments from order"}, "actionParameters": [], "mutationArguments": [{"name": "salesOrder", "node": "SalesOrder", "type": "reference", "value": "salesOrder._id", "origin": "fromVariable", "isMandatory": true}, {"name": "isSafeToRetry", "type": "boolean", "value": true, "origin": "manual", "isMandatory": false}, {"name": "controlOrderLinks", "type": "boolean", "value": true, "origin": "manual", "isMandatory": false}], "mutationNaturalKey": "SalesOrder|createShipmentsFromOrder", "outputVariableName": "salesShipments"}