{"selector": "{ purchaseOrders { _id number } workOrders { _id number } }", "stepVariables": [{"node": "SalesOrder", "path": "salesOrder._id", "type": "IntReference", "title": "Sales order / 🆔", "isCustom": false}], "localizedTitle": {"base": "Create back-to-back order", "en-US": "Sales Order: create back-to-back order"}, "actionParameters": [], "mutationArguments": [{"name": "salesOrder", "node": "SalesOrder", "type": "reference", "value": "salesOrder._id", "origin": "fromVariable", "isMandatory": true}], "mutationNaturalKey": "SalesOrder|createBackToBackOrderFromSalesOrder", "outputVariableName": "backTobackOrders"}