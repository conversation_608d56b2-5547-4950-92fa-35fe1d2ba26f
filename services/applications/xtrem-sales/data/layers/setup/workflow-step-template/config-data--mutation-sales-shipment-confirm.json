{"selector": "{ _id status}", "stepVariables": [{"node": "SalesShipment", "path": "salesShipment._id", "type": "IntReference", "title": "Sales shipment / 🆔", "isCustom": false}, {"node": "SalesShipment", "path": "salesShipment._id", "type": "reference", "title": "salesShipment"}], "localizedTitle": {"base": "Sales shipment - confirm", "en-US": "Sales shipment - confirm"}, "actionParameters": [], "mutationArguments": [{"name": "salesShipment", "node": "SalesShipment", "type": "reference", "value": "salesShipment._id", "origin": "fromVariable"}, {"name": "isSafeToRetry", "type": "boolean", "value": true, "origin": "manual", "isMandatory": false}], "mutationNaturalKey": "SalesShipment|confirm", "outputVariableName": "salesShipment"}