query {
    XwAeLdnM: xtremSales {
        salesShipment {
            query (filter: "{\"_id\":{\"_and\":[{\"_eq\":\"{{shipment}}\"}]}}", orderBy: "{}", first: 1) {
                edges {
                    node {
                        number
                        date
                        shipToAddress {
                            name
                            addressLine1
                            addressLine2
                            country {
                                iso31661Alpha3
                                name
                            }
                            region
                            city
                            postcode
                        }
                        shipToCustomerAddress {
                            name
                        }
                        shipToCustomer {
                            id
                        }
                        deliveryMode {
                            description
                        }
                        FbGiwZjk: lines {
                            query (filter: "{}", orderBy: "{\"item\":{\"id\":1},\"itemDescription\":1,\"documentNumber\":1,\"quantityInSalesUnit\":1,\"quantityInStockUnit\":1,\"quantityReceiptInSalesUnit\":1,\"_id\":1}") {
                                edges {
                                    node {
                                        _id
                                        item {
                                            id
                                        }
                                        itemDescription
                                        ixJPvdQb: salesOrderLines {
                                            query (filter: "{}", orderBy: "{}", first: 1) {
                                                edges {
                                                    node {
                                                        linkedDocument {
                                                            documentNumber
                                                        }
                                                        _id
                                                    }
                                                }
                                            }
                                        }
                                        quantity
                                        unit {
                                            name
                                        }
                                        quantityInStockUnit
                                        stockUnit {
                                            name
                                        }
                                        CewqieYg: stockAllocations {
                                            query (filter: "{}", orderBy: "{}", first: 1) {
                                                edges {
                                                    node {
                                                        quantityInStockUnit
                                                        stockRecord {
                                                            stockUnit {
                                                                name
                                                            }
                                                        }
                                                        _id
                                                    }
                                                }
                                            }
                                        }
                                        DiXoVUyB: stockAllocations {
                                            query (filter: "{}", orderBy: "{\"stockRecord\":{\"location\":{\"name\":1},\"lot\":{\"id\":1},\"quantityInStockUnit\":1}}") {
                                                edges {
                                                    node {
                                                        _id
                                                        stockRecord {
                                                            location {
                                                                name
                                                            }
                                                            lot {
                                                                id
                                                            }
                                                            stockUnit {
                                                                name
                                                            }
                                                        }
                                                        quantityInStockUnit
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                        _id
                    }
                }
            }
        }
    }
}
