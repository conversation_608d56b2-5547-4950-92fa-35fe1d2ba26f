query {
    HzaiCSRh: xtremSales {
        salesOrder {
            query (filter: "{\"_id\":{\"_and\":[{\"_eq\":\"{{salesOrder}}\"}]}}", orderBy: "{}", first: 1) {
                edges {
                    node {
                        site {
                            name
                            primaryAddress {
                                addressLine1
                                addressLine2
                                city
                                region
                                postcode
                                country {
                                    name
                                }
                            }
                            siret
                            taxIdNumber
                            legalCompany {
                                legalForm
                                name
                                naf
                                rcs
                                primaryAddress {
                                    addressLine1
                                    addressLine2
                                    city
                                    region
                                    postcode
                                    country {
                                        name
                                    }
                                }
                                legislation {
                                    id
                                }
                            }
                        }
                        number
                        LcUhipQb: proformaInvoices {
                            query (filter: "{}", orderBy: "{}", first: 1) {
                                edges {
                                    node {
                                        issueDate
                                        expirationDate
                                        _id
                                    }
                                }
                            }
                        }
                        billToCustomer {
                            id
                            name
                            siret
                            taxIdNumber
                        }
                        expectedDeliveryDate
                        deliveryMode {
                            name
                        }
                        incoterm {
                            name
                        }
                        shipToAddress {
                            name
                            addressLine1
                            addressLine2
                            city
                            region
                            postcode
                            country {
                                name
                            }
                        }
                        billToAddress {
                            name
                            addressLine1
                            addressLine2
                            city
                            region
                            postcode
                            country {
                                name
                                iso31661Alpha3
                            }
                        }
                        UcIhHaQl: lines {
                            query (filter: "{}", orderBy: "{\"item\":{\"id\": 1},\"itemDescription\":1,\"quantityInSalesUnit\":1,\"unit\":{\"name\":1},\"netPrice\":1,\"discount\":1,\"charge\":1,\"amountExcludingTax\":1,\"amountIncludingTax\":1}") {
                                edges {
                                    node {
                                        _id
                                        item { id }
                                        itemDescription
                                        quantity
                                        unit {
                                            name
                                        }
                                        document {
                                            transactionCurrency {
                                                symbol
                                            }
                                        }
                                        netPrice
                                        discount
                                        charge
                                        amountExcludingTax
                                        amountIncludingTax
                                    }
                                }
                            }
                        }
                        paymentTerm {
                            name
                            discountType
                            discountAmount
                            penaltyType
                            penaltyAmount
                        }
                        XFPQqMhi: taxes {
                            query (filter: "{}", orderBy: "{\"tax\":1,\"taxRate\":1,\"taxableAmount\":1,\"taxAmount\":1}") {
                                edges {
                                    node {
                                        _id
                                        tax
                                        currency {
                                            symbol
                                        }
                                        taxableAmount
                                        taxRate
                                        taxAmount
                                    }
                                }
                            }
                        }
                        transactionCurrency {
                            symbol
                        }
                        totalAmountExcludingTax
                        totalTaxAmount
                        totalAmountIncludingTax
                        isExternalNote
                        _id
                    }
                }
            }
        }
    }
}
