query {
    zaALNweA: xtremSales {
        salesCreditMemo {
            query (filter: "{\"_id\":{\"_and\":[{\"_eq\":\"{{creditMemo}}\"}]}}", orderBy: "{}", first: 1) {
                edges {
                    node {
                        salesSite {
                            name
                            siret
                            taxIdNumber
                            legalCompany {
                                naf
                                legalForm
                                name
                                rcs
                            }
                        }
                        salesSiteAddress {
                            addressLine1
                            addressLine2
                            postcode
                            city
                            region
                            country {
                                name
                            }
                        }
                        number
                        date
                        billToCustomer {
                            name
                            id
                            businessEntity {
                                siret
                                taxIdNumber
                            }
                        }
                        VXTJkjpl: lines {
                            query (filter: "{}", orderBy: "{}", first: 1) {
                                edges {
                                    node {
                                        consumptionAddress {
                                            addressLine1
                                            addressLine2
                                            postcode
                                            city
                                            region
                                            country {
                                                name
                                            }
                                        }
                                        _id
                                    }
                                }
                            }
                        }
                        billToAddress {
                            addressLine1
                            addressLine2
                            postcode
                            city
                            region
                            country {
                                name
                            }
                        }
                        QCXJMBON: lines {
                            query (filter: "{}", orderBy: "{\"document\":{\"number\":1},"{\"item\":"{\"id\":-1}},\"itemDescription\":1,\"quantityInSalesUnit\":1,\"unit\":{\"name\":1},\"netPrice\":1,\"amountExcludingTax\":1,\"amountExcludingTaxInCompanyCurrency\":1,\"amountIncludingTax\":1}") {
                                edges {
                                    node {
                                        _id
                                        document {
                                            number
                                            currency {
                                                symbol
                                            }
                                        }
                                        item{
                                            id
                                        }
                                        itemDescription
                                        quantity
                                        unit {
                                            name
                                        }
                                        netPrice
                                        amountExcludingTax
                                        BeKKQqqr: taxes {
                                            query (filter: "{}", orderBy: "{}", first: 1) {
                                                edges {
                                                    node {
                                                        taxRate
                                                        _id
                                                    }
                                                }
                                            }
                                        }
                                        amountIncludingTax
                                    }
                                }
                            }
                        }
                        MDpowSPm: taxes {
                            query (filter: "{}", orderBy: "{\"tax\":1,\"taxableAmount\":1,\"taxRate\":1,\"taxAmount\":1}") {
                                edges {
                                    node {
                                        _id
                                        tax
                                        currency {
                                            symbol
                                        }
                                        taxableAmount
                                        taxRate
                                        taxAmount
                                    }
                                }
                            }
                        }
                        currency {
                            symbol
                        }
                        totalAmountExcludingTax
                        totalTaxAmount
                        totalAmountIncludingTax
                        vNmgtdmU: salesSite {
                            legalCompany {
                                addresses {
                                    query (filter: "{}", orderBy: "{}", first: 1) {
                                        edges {
                                            node {
                                                addressLine1
                                                addressLine2
                                                postcode
                                                city
                                                region
                                                country {
                                                    name
                                                }
                                                _id
                                            }
                                        }
                                    }
                                }
                            }
                        }
                        _id
                    }
                }
            }
        }
    }
}
