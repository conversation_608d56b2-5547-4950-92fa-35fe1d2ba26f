<section
  class="record-context"
  data-context-object-type="SalesInvoice"
  data-context-object-path="xtremSales.salesInvoice.query.edges.0.node"
  data-context-filter='[{"_id":"1","label":"_id","filterType":"matches","filterValue":"invoice","data":{"type":"IntOrString","kind":"SCALAR","name":"_id","canFilter":true,"canSort":true,"label":"_id","isOnInputType":false,"isOnOutputType":true,"dataType":"","targetNode":"","enumType":null,"isCustom":false,"isMutable":false,"iconType":"csv"},"id":"_id","labelPath":"_id","property":{"type":"IntOrString","kind":"SCALAR","name":"_id","canFilter":true,"canSort":true,"label":"_id","isOnInputType":false,"isOnOutputType":true,"dataType":"","targetNode":"","enumType":null,"isCustom":false,"isMutable":false,"iconType":"csv","id":"_id","data":{"type":"IntOrString","kind":"SCALAR","name":"_id","canFilter":true,"canSort":true,"label":"_id","isOnInputType":false,"isOnOutputType":true,"dataType":"","targetNode":"","enumType":null,"isCustom":false,"isMutable":false,"iconType":"csv"},"key":"_id","labelKey":"_id","labelPath":"_id"},"parameter":true}]'
  data-context-list-order="{}"
  data-alias="tvpoNdBW"
>
  <!--{{#with tvpoNdBW.salesInvoice.query.edges.0.node}}-->
  <div class="report-context-body">
    <section class="unbreakable-block">
      <div class="unbreakable-block-body">
        <figure class="table" style="width: 100%">
          <table class="ck-table-resized">
            <colgroup>
              <col style="width: 33.56%" />
              <col style="width: 66.44%" />
            </colgroup>
            <tbody>
              <tr>
                <td>
                  <section
                    class="conditional-block"
                    data-context-condition='[{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"1","value1":{"label":"Name","data":{"name":"name","title":"Name","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"name","targetNode":"","isCollection":false,"kind":"SCALAR","label":"Name","node":"String","iconType":"csv"},"id":"salesSite.name","key":"salesSite.name","labelKey":"Name","labelPath":"Sales site > Name"},"value2":null,"key":"1","operator":"notEmpty"}]'
                  >
                    <!--{{#if ( not ( or ( eq salesSite.name null ) ( eq salesSite.name "" ) ( eq salesSite.name undefined ) ) )}}-->
                    <div class="conditional-block-body">
                      <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                        ><strong
                          ><span
                            class="property"
                            data-property-display-label="Name"
                            data-property-data-type="String"
                            data-property-name="salesSite.name"
                            data-property-data-format=""
                            data-property-parent-context="SalesInvoice"
                            >{{salesSite.name}}</span
                          ></strong
                        ></span
                      >
                    </div>
                    <!--{{/if}}-->
                    <div class="conditional-block-footer">&nbsp;</div>
                  </section>
                  <section
                    class="conditional-block"
                    data-context-condition='[{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"1","value1":{"label":"Address line 1","data":{"name":"addressLine1","title":"Address line 1","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"addressLineDataType","targetNode":"","isCollection":false,"kind":"SCALAR","label":"Address line 1","node":"String","iconType":"csv"},"id":"salesSiteAddress.addressLine1","key":"salesSiteAddress.addressLine1","labelKey":"Address line 1","labelPath":"Sales site address > Address line 1"},"value2":null,"key":"1","operator":"notEmpty"}]'
                  >
                    <!--{{#if ( not ( or ( eq salesSiteAddress.addressLine1 null ) ( eq salesSiteAddress.addressLine1 "" ) ( eq salesSiteAddress.addressLine1 undefined ) ) )}}-->
                    <div class="conditional-block-body">
                      <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                        ><span
                          class="property"
                          data-property-display-label="Address line 1"
                          data-property-data-type="String"
                          data-property-name="salesSiteAddress.addressLine1"
                          data-property-data-format=""
                          data-property-parent-context="SalesInvoice"
                          >{{salesSiteAddress.addressLine1}}</span
                        ></span
                      >
                    </div>
                    <!--{{/if}}-->
                    <div class="conditional-block-footer">&nbsp;</div>
                  </section>
                  <section
                    class="conditional-block"
                    data-context-condition='[{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"1","value1":{"label":"Address line 2","data":{"name":"addressLine2","title":"Address line 2","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"addressLineDataType","targetNode":"","isCollection":false,"kind":"SCALAR","label":"Address line 2","node":"String","iconType":"csv"},"id":"salesSiteAddress.addressLine2","key":"salesSiteAddress.addressLine2","labelKey":"Address line 2","labelPath":"Sales site address > Address line 2"},"value2":null,"key":"1","operator":"notEmpty"}]'
                  >
                    <!--{{#if ( not ( or ( eq salesSiteAddress.addressLine2 null ) ( eq salesSiteAddress.addressLine2 "" ) ( eq salesSiteAddress.addressLine2 undefined ) ) )}}-->
                    <div class="conditional-block-body">
                      <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                        ><span
                          class="property"
                          data-property-display-label="Address line 2"
                          data-property-data-type="String"
                          data-property-name="salesSiteAddress.addressLine2"
                          data-property-data-format=""
                          data-property-parent-context="SalesInvoice"
                          >{{salesSiteAddress.addressLine2}}</span
                        ></span
                      >
                    </div>
                    <!--{{/if}}-->
                    <div class="conditional-block-footer">&nbsp;</div>
                  </section>
                  <section
                    class="conditional-block"
                    data-context-condition='[{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"1","value1":{"label":"Postal code","data":{"name":"postcode","title":"Postal code","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"postcodeDataType","targetNode":"","isCollection":false,"kind":"SCALAR","label":"Postal code","node":"String","iconType":"csv"},"id":"salesSiteAddress.postcode","key":"salesSiteAddress.postcode","labelKey":"Postal code","labelPath":"Sales site address > Postal code"},"value2":null,"key":"1","operator":"notEmpty"},{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"2","value1":{"label":"City","data":{"name":"city","title":"City","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"cityDataType","targetNode":"","isCollection":false,"kind":"SCALAR","label":"City","node":"String","iconType":"csv"},"id":"salesSiteAddress.city","key":"salesSiteAddress.city","labelKey":"City","labelPath":"Sales site address > City"},"value2":null,"key":"2","operator":"notEmpty"}]'
                  >
                    <!--{{#if ( and ( not ( or ( eq salesSiteAddress.postcode null ) ( eq salesSiteAddress.postcode "" ) ( eq salesSiteAddress.postcode undefined ) ) ) ( not ( or ( eq salesSiteAddress.city null ) ( eq salesSiteAddress.city "" ) ( eq salesSiteAddress.city undefined ) ) ) )}}-->
                    <div class="conditional-block-body">
                      <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                        ><span
                          class="property"
                          data-property-display-label="Postal code"
                          data-property-data-type="String"
                          data-property-name="salesSiteAddress.postcode"
                          data-property-data-format=""
                          data-property-parent-context="SalesInvoice"
                          >{{salesSiteAddress.postcode}}</span
                        ><span
                          class="property"
                          data-property-display-label="City"
                          data-property-data-type="String"
                          data-property-name="salesSiteAddress.city"
                          data-property-data-format=""
                          data-property-parent-context="SalesInvoice"
                          >{{salesSiteAddress.city}}</span
                        ></span
                      >
                    </div>
                    <!--{{/if}}-->
                    <div class="conditional-block-footer">&nbsp;</div>
                  </section>
                  <section
                    class="conditional-block"
                    data-context-condition='[{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"1","value1":{"label":"Name","data":{"name":"name","title":"Name","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"localizedCountryPropertyDataType","targetNode":"","isCollection":false,"kind":"SCALAR","label":"Name","node":"String","iconType":"csv"},"id":"salesSiteAddress.country.name","key":"salesSiteAddress.country.name","labelKey":"Name","labelPath":"Sales site address > Country > Name"},"value2":null,"key":"1","operator":"notEmpty"}]'
                  >
                    <!--{{#if ( not ( or ( eq salesSiteAddress.country.name null ) ( eq salesSiteAddress.country.name "" ) ( eq salesSiteAddress.country.name undefined ) ) )}}-->
                    <div class="conditional-block-body">
                      <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                        ><span
                          class="property"
                          data-property-display-label="Name"
                          data-property-data-type="String"
                          data-property-name="salesSiteAddress.country.name"
                          data-property-data-format=""
                          data-property-parent-context="SalesInvoice"
                          >{{salesSiteAddress.country.name}}</span
                        ></span
                      >
                    </div>
                    <!--{{/if}}-->
                    <div class="conditional-block-footer">&nbsp;</div>
                  </section>
                  <section
                    class="conditional-block"
                    data-context-condition='[{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"1","value1":{"label":"SIRET","data":{"name":"siret","title":"SIRET","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":false,"isOnOutputType":true,"enumType":null,"dataType":"description","targetNode":"","isCollection":false,"kind":"SCALAR","label":"SIRET","node":"String","iconType":"csv"},"id":"salesSite.siret","key":"salesSite.siret","labelKey":"SIRET","labelPath":"Sales site > SIRET"},"value2":null,"key":"1","operator":"notEmpty"}]'
                  >
                    <!--{{#if ( not ( or ( eq salesSite.siret null ) ( eq salesSite.siret "" ) ( eq salesSite.siret undefined ) ) )}}-->
                    <div class="conditional-block-body">
                      <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                        ><strong>&nbsp;{{ translatedContent "d04e2c1b67f3ef0d475409516b812e8b" }}&nbsp;</strong
                        ><span
                          class="property"
                          data-property-display-label="SIRET"
                          data-property-data-type="String"
                          data-property-name="salesSite.siret"
                          data-property-data-format=""
                          data-property-parent-context="SalesInvoice"
                          >{{salesSite.siret}}</span
                        ></span
                      >
                    </div>
                    <!--{{/if}}-->
                    <div class="conditional-block-footer">&nbsp;</div>
                  </section>
                  <section
                    class="conditional-block"
                    data-context-condition='[{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"1","value1":{"label":"Tax ID number","data":{"name":"taxIdNumber","title":"Tax ID number","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":false,"isOnOutputType":true,"enumType":null,"dataType":"taxIdentificationDataType","targetNode":"","isCollection":false,"kind":"SCALAR","label":"Tax ID number","node":"String","iconType":"csv"},"id":"salesSite.taxIdNumber","key":"salesSite.taxIdNumber","labelKey":"Tax ID number","labelPath":"Sales site > Tax ID number"},"value2":null,"key":"1","operator":"notEmpty"}]'
                  >
                    <!--{{#if ( not ( or ( eq salesSite.taxIdNumber null ) ( eq salesSite.taxIdNumber "" ) ( eq salesSite.taxIdNumber undefined ) ) )}}-->
                    <div class="conditional-block-body">
                      <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                        ><strong>&nbsp;{{ translatedContent "37a7a58f6fdc66ce34c2bf56389a2aac" }}</strong>&nbsp;<span
                          class="property"
                          data-property-display-label="Tax ID number"
                          data-property-data-type="String"
                          data-property-name="salesSite.taxIdNumber"
                          data-property-data-format=""
                          data-property-parent-context="SalesInvoice"
                          >{{salesSite.taxIdNumber}}</span
                        ></span
                      >
                    </div>
                    <!--{{/if}}-->
                    <div class="conditional-block-footer">&nbsp;</div>
                  </section>
                </td>
                <td style="background-color: #dfdfdf; text-align: right">
                  <h2>
                    <span style="color: #198e59; font-family: 'Sage UI', Geneva, sans-serif"
                      ><strong>{{ translatedContent "466eadd40b3c10580e3ab4e8061161ce" }}</strong></span
                    >
                  </h2>
                  <p>
                    <span style="color: #198e59; font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                      ><strong>{{ translatedContent "2a39f204d4eac05604c2059e5ac2f9f7" }}</strong></span
                    ><span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                      >&nbsp;<strong
                        ><span
                          class="property"
                          data-property-display-label="Number"
                          data-property-data-type="String"
                          data-property-name="number"
                          data-property-data-format=""
                          data-property-parent-context="SalesInvoice"
                          >{{number}}</span
                        ></strong
                      ></span
                    >
                  </p>
                  <p>
                    <span style="color: #198e59; font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                      ><strong>{{ translatedContent "5cee430b4c2820b7e6021df1dfc5df42" }}</strong></span
                    ><span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                      >&nbsp;<strong
                        ><span
                          class="property"
                          data-property-display-label="Invoice date"
                          data-property-data-type="Date"
                          data-property-name="date"
                          data-property-data-format="FullDate"
                          data-property-parent-context="SalesInvoice"
                          >{{formatDate date 'FullDate'}}</span
                        ></strong
                      ></span
                    >
                  </p>
                </td>
              </tr>
              <tr>
                <td style="background-color: transparent; border-color: transparent">
                  <section
                    class="conditional-block"
                    data-context-condition='[{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"1","value1":{"label":"Name","data":{"name":"name","title":"Name","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":false,"isOnOutputType":true,"enumType":null,"dataType":"","targetNode":"","isCollection":false,"kind":"SCALAR","label":"Name","node":"String","iconType":"csv"},"id":"billToCustomer.name","key":"billToCustomer.name","labelKey":"Name","labelPath":"Bill-to customer > Name"},"value2":null,"key":"1","operator":"notEmpty"}]'
                  >
                    <!--{{#if ( not ( or ( eq billToCustomer.name null ) ( eq billToCustomer.name "" ) ( eq billToCustomer.name undefined ) ) )}}-->
                    <div class="conditional-block-body">
                      <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                        ><strong>{{ translatedContent "5eea367ea73b909880393bd1ae79fc67" }}</strong>&nbsp;</span
                      ><span style="color: #198e59; font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                        ><strong
                          ><span
                            class="property"
                            data-property-display-label="Name"
                            data-property-data-type="String"
                            data-property-name="billToCustomer.name"
                            data-property-data-format=""
                            data-property-parent-context="SalesInvoice"
                            >{{billToCustomer.name}}</span
                          ></strong
                        ></span
                      >
                    </div>
                    <!--{{/if}}-->
                    <div class="conditional-block-footer">&nbsp;</div>
                  </section>
                  <section
                    class="conditional-block"
                    data-context-condition='[{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"1","value1":{"label":"ID","data":{"name":"id","title":"ID","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":false,"isOnOutputType":true,"enumType":null,"dataType":"","targetNode":"","isCollection":false,"kind":"SCALAR","label":"ID","node":"String","iconType":"csv"},"id":"billToCustomer.id","key":"billToCustomer.id","labelKey":"ID","labelPath":"Bill-to customer > ID"},"value2":null,"key":"1","operator":"notEmpty"}]'
                  >
                    <!--{{#if ( not ( or ( eq billToCustomer.id null ) ( eq billToCustomer.id "" ) ( eq billToCustomer.id undefined ) ) )}}-->
                    <div class="conditional-block-body">
                      <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                        ><strong>{{ translatedContent "d13d8380c3f4de07fef91a42fe6c60d7" }}&nbsp;</strong
                        ><span
                          class="property"
                          data-property-display-label="ID"
                          data-property-data-type="String"
                          data-property-name="billToCustomer.id"
                          data-property-data-format=""
                          data-property-parent-context="SalesInvoice"
                          >{{billToCustomer.id}}</span
                        ></span
                      >
                    </div>
                    <!--{{/if}}-->
                    <div class="conditional-block-footer">&nbsp;</div>
                  </section>
                  <section
                    class="conditional-block"
                    data-context-condition='[{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"1","value1":{"label":"Tax ID number","data":{"name":"taxIdNumber","title":"Tax ID number","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":false,"isOnOutputType":true,"enumType":null,"dataType":"taxIdentificationDataType","targetNode":"","isCollection":false,"kind":"SCALAR","label":"Tax ID number","node":"String","iconType":"csv"},"id":"billToCustomer.taxIdNumber","key":"billToCustomer.taxIdNumber","labelKey":"Tax ID number","labelPath":"Bill-to customer > Tax ID number"},"value2":null,"key":"1","operator":"notEmpty"}]'
                  >
                    <!--{{#if ( not ( or ( eq billToCustomer.taxIdNumber null ) ( eq billToCustomer.taxIdNumber "" ) ( eq billToCustomer.taxIdNumber undefined ) ) )}}-->
                    <div class="conditional-block-body">
                      <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                        ><strong>{{ translatedContent "37a7a58f6fdc66ce34c2bf56389a2aac" }}</strong>&nbsp;<span
                          class="property"
                          data-property-display-label="Tax ID number"
                          data-property-data-type="String"
                          data-property-name="billToCustomer.taxIdNumber"
                          data-property-data-format=""
                          data-property-parent-context="SalesInvoice"
                          >{{billToCustomer.taxIdNumber}}</span
                        ></span
                      >
                    </div>
                    <!--{{/if}}-->
                    <div class="conditional-block-footer">&nbsp;</div>
                  </section>
                </td>
                <td style="border-color: transparent; text-align: right">&nbsp;</td>
              </tr>
            </tbody>
          </table>
        </figure>
        <figure class="table">
          <table>
            <tbody>
              <tr>
                <td style="background-color: #dfdfdf; border-color: #dfdfdf">
                  <span style="color: #198e59; font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                    ><strong>{{ translatedContent "84e839915aa016435941dda50c367e0c" }}</strong></span
                  >
                </td>
                <td style="background-color: #dfdfdf; border-color: #dfdfdf">
                  <span style="color: #198e59; font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                    ><strong>{{ translatedContent "6fd791a834ec2f5a235b58bdea1746fc" }}</strong></span
                  >
                </td>
              </tr>
              <tr>
                <td style="border-color: transparent">
                  <p>
                    <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                      ><strong
                        ><span
                          class="property"
                          data-property-display-label="Name"
                          data-property-data-type="String"
                          data-property-name="billToCustomer.name"
                          data-property-data-format=""
                          data-property-parent-context="SalesInvoice"
                          >{{billToCustomer.name}}</span
                        ></strong
                      ></span
                    >
                  </p>
                  <section
                    class="record-context"
                    data-context-object-type="SalesInvoiceLine"
                    data-context-object-path="lines.query.edges.0.node"
                    data-context-filter="[]"
                    data-context-list-order="{}"
                    data-alias="RlOSvszC"
                  >
                    <!--{{#with RlOSvszC.query.edges.0.node}}-->
                    <div class="report-context-body">
                      <section
                        class="conditional-block"
                        data-context-condition='[{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"1","value1":{"label":"Address line 1","data":{"name":"addressLine1","title":"Address line 1","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"addressLineDataType","targetNode":"","isCollection":false,"kind":"SCALAR","label":"Address line 1","node":"String","iconType":"csv"},"id":"consumptionAddress.addressLine1","key":"consumptionAddress.addressLine1","labelKey":"Address line 1","labelPath":"Consumption address > Address line 1"},"value2":null,"key":"1","operator":"notEmpty"}]'
                      >
                        <!--{{#if ( not ( or ( eq consumptionAddress.addressLine1 null ) ( eq consumptionAddress.addressLine1 "" ) ( eq consumptionAddress.addressLine1 undefined ) ) )}}-->
                        <div class="conditional-block-body">
                          <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                            ><span
                              class="property"
                              data-property-display-label="Address line 1"
                              data-property-data-type="String"
                              data-property-name="consumptionAddress.addressLine1"
                              data-property-data-format=""
                              data-property-parent-context="SalesInvoiceLine"
                              >{{consumptionAddress.addressLine1}}</span
                            ></span
                          >
                        </div>
                        <!--{{/if}}-->
                        <div class="conditional-block-footer">&nbsp;</div>
                      </section>
                      <section
                        class="conditional-block"
                        data-context-condition='[{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"1","value1":{"label":"Address line 2","data":{"name":"addressLine2","title":"Address line 2","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"addressLineDataType","targetNode":"","isCollection":false,"kind":"SCALAR","label":"Address line 2","node":"String","iconType":"csv"},"id":"consumptionAddress.addressLine2","key":"consumptionAddress.addressLine2","labelKey":"Address line 2","labelPath":"Consumption address > Address line 2"},"value2":null,"key":"1","operator":"notEmpty"}]'
                      >
                        <!--{{#if ( not ( or ( eq consumptionAddress.addressLine2 null ) ( eq consumptionAddress.addressLine2 "" ) ( eq consumptionAddress.addressLine2 undefined ) ) )}}-->
                        <div class="conditional-block-body">
                          <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                            ><span
                              class="property"
                              data-property-display-label="Address line 2"
                              data-property-data-type="String"
                              data-property-name="consumptionAddress.addressLine2"
                              data-property-data-format=""
                              data-property-parent-context="SalesInvoiceLine"
                              >{{consumptionAddress.addressLine2}}</span
                            ></span
                          >
                        </div>
                        <!--{{/if}}-->
                        <div class="conditional-block-footer">&nbsp;</div>
                      </section>
                      <p>
                        <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                          ><span
                            class="property"
                            data-property-display-label="Postal code"
                            data-property-data-type="String"
                            data-property-name="consumptionAddress.postcode"
                            data-property-data-format=""
                            data-property-parent-context="SalesInvoiceLine"
                            >{{consumptionAddress.postcode}}</span
                          ><span
                            class="property"
                            data-property-display-label="City"
                            data-property-data-type="String"
                            data-property-name="consumptionAddress.city"
                            data-property-data-format=""
                            data-property-parent-context="SalesInvoiceLine"
                            >{{consumptionAddress.city}}</span
                          ><span
                            class="property"
                            data-property-display-label="Region"
                            data-property-data-type="String"
                            data-property-name="consumptionAddress.region"
                            data-property-data-format=""
                            data-property-parent-context="SalesInvoiceLine"
                            >{{consumptionAddress.region}}</span
                          ></span
                        >
                      </p>
                      <section
                        class="conditional-block"
                        data-context-condition='[{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"1","value1":{"label":"Name","data":{"name":"name","title":"Name","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"localizedCountryPropertyDataType","targetNode":"","isCollection":false,"kind":"SCALAR","label":"Name","node":"String","iconType":"csv"},"id":"consumptionAddress.country.name","key":"consumptionAddress.country.name","labelKey":"Name","labelPath":"Consumption address > Country > Name"},"value2":null,"key":"1","operator":"notEmpty"}]'
                      >
                        <!--{{#if ( not ( or ( eq consumptionAddress.country.name null ) ( eq consumptionAddress.country.name "" ) ( eq consumptionAddress.country.name undefined ) ) )}}-->
                        <div class="conditional-block-body">
                          <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                            ><span
                              class="property"
                              data-property-display-label="Name"
                              data-property-data-type="String"
                              data-property-name="consumptionAddress.country.name"
                              data-property-data-format=""
                              data-property-parent-context="SalesInvoiceLine"
                              >{{consumptionAddress.country.name}}</span
                            ></span
                          >
                        </div>
                        <!--{{/if}}-->
                        <div class="conditional-block-footer">&nbsp;</div>
                      </section>
                    </div>
                    <!--{{/with}}--><span class="report-context-footer">&nbsp;</span>
                  </section>
                </td>
                <td style="border-color: transparent">
                  <p>
                    <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                      ><strong
                        ><span
                          class="property"
                          data-property-display-label="Name"
                          data-property-data-type="String"
                          data-property-name="billToCustomer.name"
                          data-property-data-format=""
                          data-property-parent-context="SalesInvoice"
                          >{{billToCustomer.name}}</span
                        ></strong
                      ></span
                    >
                  </p>
                  <section
                    class="conditional-block"
                    data-context-condition='[{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"1","value1":{"label":"Address line 1","data":{"name":"addressLine1","title":"Address line 1","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"addressLineDataType","targetNode":"","isCollection":false,"kind":"SCALAR","label":"Address line 1","node":"String","iconType":"csv"},"id":"billToAddress.addressLine1","key":"billToAddress.addressLine1","labelKey":"Address line 1","labelPath":"Bill-to address > Address line 1"},"value2":null,"key":"1","operator":"notEmpty"}]'
                  >
                    <!--{{#if ( not ( or ( eq billToAddress.addressLine1 null ) ( eq billToAddress.addressLine1 "" ) ( eq billToAddress.addressLine1 undefined ) ) )}}-->
                    <div class="conditional-block-body">
                      <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                        ><span
                          class="property"
                          data-property-display-label="Address line 1"
                          data-property-data-type="String"
                          data-property-name="billToAddress.addressLine1"
                          data-property-data-format=""
                          data-property-parent-context="SalesInvoice"
                          >{{billToAddress.addressLine1}}</span
                        ></span
                      >
                    </div>
                    <!--{{/if}}-->
                    <div class="conditional-block-footer">&nbsp;</div>
                  </section>
                  <section
                    class="conditional-block"
                    data-context-condition='[{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"1","value1":{"label":"Address line 2","data":{"name":"addressLine2","title":"Address line 2","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"addressLineDataType","targetNode":"","isCollection":false,"kind":"SCALAR","label":"Address line 2","node":"String","iconType":"csv"},"id":"billToAddress.addressLine2","key":"billToAddress.addressLine2","labelKey":"Address line 2","labelPath":"Bill-to address > Address line 2"},"value2":null,"key":"1","operator":"notEmpty"}]'
                  >
                    <!--{{#if ( not ( or ( eq billToAddress.addressLine2 null ) ( eq billToAddress.addressLine2 "" ) ( eq billToAddress.addressLine2 undefined ) ) )}}-->
                    <div class="conditional-block-body">
                      <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                        ><span
                          class="property"
                          data-property-display-label="Address line 2"
                          data-property-data-type="String"
                          data-property-name="billToAddress.addressLine2"
                          data-property-data-format=""
                          data-property-parent-context="SalesInvoice"
                          >{{billToAddress.addressLine2}}</span
                        ></span
                      >
                    </div>
                    <!--{{/if}}-->
                    <div class="conditional-block-footer">&nbsp;</div>
                  </section>
                  <p>
                    <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                      ><span
                        class="property"
                        data-property-display-label="Postal code"
                        data-property-data-type="String"
                        data-property-name="billToAddress.postcode"
                        data-property-data-format=""
                        data-property-parent-context="SalesInvoice"
                        >{{billToAddress.postcode}}</span
                      ><span
                        class="property"
                        data-property-display-label="City"
                        data-property-data-type="String"
                        data-property-name="billToAddress.city"
                        data-property-data-format=""
                        data-property-parent-context="SalesInvoice"
                        >{{billToAddress.city}}</span
                      ><span
                        class="property"
                        data-property-display-label="Region"
                        data-property-data-type="String"
                        data-property-name="billToAddress.region"
                        data-property-data-format=""
                        data-property-parent-context="SalesInvoice"
                        >{{billToAddress.region}}</span
                      ></span
                    >
                  </p>
                  <section
                    class="conditional-block"
                    data-context-condition='[{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"1","value1":{"label":"Name","data":{"name":"name","title":"Name","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"localizedCountryPropertyDataType","targetNode":"","isCollection":false,"kind":"SCALAR","label":"Name","node":"String","iconType":"csv"},"id":"billToAddress.country.name","key":"billToAddress.country.name","labelKey":"Name","labelPath":"Bill-to address > Country > Name"},"value2":null,"key":"1","operator":"notEmpty"}]'
                  >
                    <!--{{#if ( not ( or ( eq billToAddress.country.name null ) ( eq billToAddress.country.name "" ) ( eq billToAddress.country.name undefined ) ) )}}-->
                    <div class="conditional-block-body">
                      <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                        ><span
                          class="property"
                          data-property-display-label="Name"
                          data-property-data-type="String"
                          data-property-name="billToAddress.country.name"
                          data-property-data-format=""
                          data-property-parent-context="SalesInvoice"
                          >{{billToAddress.country.name}}</span
                        ></span
                      >
                    </div>
                    <!--{{/if}}-->
                    <div class="conditional-block-footer">&nbsp;</div>
                  </section>
                </td>
              </tr>
            </tbody>
          </table>
        </figure>
        <table
          class="query-table"
          data-context-object-type="SalesInvoiceLine"
          data-context-object-path="lines.query.edges"
          data-context-filter="[]"
          data-context-list-order="{}"
          data-alias="PDqEDyGT"
        >
          <thead class="query-table-head">
            <tr class="query-table-row">
              <td class="query-table-cell" style="background-color: #0000001a; border: 1px solid #dfdfdf; padding: 2px">
                <p>
                  <span style="color: #198e59; font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                    ><strong>{{ translatedContent "a240fa27925a635b08dc28c9e4f9216d" }}</strong></span
                  >
                </p>
              </td>
              <td class="query-table-cell" style="background-color: #0000001a; border: 1px solid #dfdfdf; padding: 2px">
                <p>
                  <span style="color: #198e59; font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                    ><strong>{{ translatedContent "6bb311efd788bb4b3123896667e767a7" }}</strong></span
                  >
                </p>
              </td>
              <td class="query-table-cell" style="background-color: #0000001a; border: 1px solid #dfdfdf; padding: 2px">
                <p>
                  <span style="color: #198e59; font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                    ><strong>{{ translatedContent "7d74f3b92b19da5e606d737d339a9679" }}&nbsp;</strong></span
                  >
                </p>
              </td>
              <td class="query-table-cell" style="background-color: #0000001a; border: 1px solid #dfdfdf; padding: 2px">
                <p>
                  <span style="color: #198e59; font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                    ><strong>{{ translatedContent "b5a7adde1af5c87d7fd797b6245c2a39" }}</strong></span
                  >
                </p>
              </td>
              <td class="query-table-cell" style="background-color: #0000001a; border: 1px solid #dfdfdf; padding: 2px">
                <p>
                  <span style="color: #198e59; font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                    ><strong>{{ translatedContent "694e8d1f2ee056f98ee488bdc4982d73" }}&nbsp;</strong></span
                  >
                </p>
              </td>
              <td class="query-table-cell" style="background-color: #0000001a; border: 1px solid #dfdfdf; padding: 2px">
                <p>
                  <span style="color: #198e59; font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                    ><strong>{{ translatedContent "19c562a36aeb455d09534f93b4f5236f" }}</strong></span
                  >
                </p>
              </td>
              <td class="query-table-cell" style="background-color: #0000001a; border: 1px solid #dfdfdf; padding: 2px">
                <p>
                  <span style="color: #198e59; font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                    ><strong>{{ translatedContent "9554ce40e7959ce198210376c97150b3" }}</strong></span
                  >
                </p>
              </td>
              <td class="query-table-cell" style="background-color: #0000001a; border: 1px solid #dfdfdf; padding: 2px">
                <p>
                  <span style="color: #198e59; font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                    ><strong>{{ translatedContent "c17f9183c57045cfb614e02bbb233ee1" }}</strong></span
                  >
                </p>
              </td>
              <td class="query-table-cell" style="background-color: #0000001a; border: 1px solid #dfdfdf; padding: 2px">
                <p>
                  <span style="color: #198e59; font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                    ><strong>{{ translatedContent "20a34c4e30c5bbe1d3f870ac55f0d831" }}</strong></span
                  >
                </p>
              </td>
              <td class="query-table-cell" style="background-color: #0000001a; border: 1px solid #dfdfdf; padding: 2px">
                <p>
                  <span style="color: #198e59; font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                    ><strong>{{ translatedContent "41dd1e26b4321ab2f837f69a7aae8e91" }}</strong></span
                  >
                </p>
              </td>
            </tr>
          </thead>
          <tbody class="query-table-body">
            <!--{{#each PDqEDyGT.query.edges}}{{#with node}}-->
            <tr class="query-table-row">
              <td class="query-table-cell" style="border: 1px solid #dfdfdf; padding: 2px">
                <section
                  class="record-context"
                  data-context-object-type="SalesOrderLineToSalesInvoiceLine"
                  data-context-object-path="salesOrderLines.query.edges.0.node"
                  data-context-filter="[]"
                  data-context-list-order="{}"
                  data-alias="nbYrAXFj"
                >
                  <!--{{#with nbYrAXFj.query.edges.0.node}}-->
                  <div class="report-context-body">
                    <p><span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt">&nbsp;</span></p>
                    <p>
                      <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                        ><span
                          class="property"
                          data-property-display-label="Number"
                          data-property-data-type="String"
                          data-property-name="linkedDocument.document.number"
                          data-property-data-format=""
                          data-property-parent-context="SalesOrderLineToSalesInvoiceLine"
                          >{{linkedDocument.document.number}}</span
                        ></span
                      >
                    </p>
                  </div>
                  <!--{{/with}}--><span class="report-context-footer">&nbsp;</span>
                </section>
              </td>
              <td class="query-table-cell" style="border: 1px solid #dfdfdf; padding: 2px">
                <section
                  class="record-context"
                  data-context-object-type="SalesShipmentLineToSalesInvoiceLine"
                  data-context-object-path="salesShipmentLines.query.edges.0.node"
                  data-context-filter="[]"
                  data-context-list-order="{}"
                  data-alias="pGcbXxMd"
                >
                  <!--{{#with pGcbXxMd.query.edges.0.node}}-->
                  <div class="report-context-body">
                    <p>&nbsp;</p>
                    <p>
                      <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                        ><span
                          class="property"
                          data-property-display-label="Number"
                          data-property-data-type="String"
                          data-property-name="linkedDocument.document.number"
                          data-property-data-format=""
                          data-property-parent-context="SalesShipmentLineToSalesInvoiceLine"
                          >{{linkedDocument.document.number}}</span
                        ></span
                      >
                    </p>
                  </div>
                  <!--{{/with}}--><span class="report-context-footer">&nbsp;</span>
                </section>
              </td>
              <td class="query-table-cell" style="border: 1px solid #dfdfdf; padding: 2px">
                <p>
                  <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                    ><span
                      class="property"
                      data-property-display-label="Item ID"
                      data-property-data-type="String"
                      data-property-name="item.id"
                      data-property-data-format=""
                      data-property-parent-context="SalesInvoiceLine"
                      >{{item.id}}</span
                    ></span
                  >
                </p>
              </td>
              <td class="query-table-cell" style="border: 1px solid #dfdfdf; padding: 2px">
                <p>
                  <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                    ><span
                      class="property"
                      data-property-display-label="Item description"
                      data-property-data-type="String"
                      data-property-name="itemDescription"
                      data-property-data-format=""
                      data-property-parent-context="SalesInvoiceLine"
                      >{{itemDescription}}</span
                    ></span
                  >
                </p>
              </td>
              <td class="query-table-cell" style="border: 1px solid #dfdfdf; padding: 2px; text-align: right">
                <p>
                  <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                    ><span
                      class="property"
                      data-property-display-label="Quantity in sales unit"
                      data-property-data-type="Decimal"
                      data-property-name="quantity"
                      data-property-data-format="2"
                      data-property-parent-context="SalesInvoiceLine"
                      >{{formatNumber quantity 2}}</span
                    ></span
                  >
                </p>
              </td>
              <td class="query-table-cell" style="border: 1px solid #dfdfdf; padding: 2px">
                <p>
                  <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                    ><span
                      class="property"
                      data-property-display-label="Name"
                      data-property-data-type="String"
                      data-property-name="unit.name"
                      data-property-data-format=""
                      data-property-parent-context="SalesInvoiceLine"
                      >{{unit.name}}</span
                    ></span
                  >
                </p>
              </td>
              <td class="query-table-cell" style="border: 1px solid #dfdfdf; padding: 2px; text-align: right">
                <p>
                  <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                    ><span
                      class="property"
                      data-property-display-label="Symbol"
                      data-property-data-type="String"
                      data-property-name="document.currency.symbol"
                      data-property-data-format=""
                      data-property-parent-context="SalesInvoiceLine"
                      >{{document.currency.symbol}}</span
                    ><span
                      class="property"
                      data-property-display-label="Net price"
                      data-property-data-type="Decimal"
                      data-property-name="netPrice"
                      data-property-data-format="2"
                      data-property-parent-context="SalesInvoiceLine"
                      >{{formatNumber netPrice 2}}</span
                    ></span
                  >
                </p>
              </td>
              <td class="query-table-cell" style="border: 1px solid #dfdfdf; padding: 2px; text-align: right">
                <p>
                  <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                    ><span
                      class="property"
                      data-property-display-label="Symbol"
                      data-property-data-type="String"
                      data-property-name="document.currency.symbol"
                      data-property-data-format=""
                      data-property-parent-context="SalesInvoiceLine"
                      >{{document.currency.symbol}}</span
                    ><span
                      class="property"
                      data-property-display-label="Line amount excluding tax"
                      data-property-data-type="Decimal"
                      data-property-name="amountExcludingTax"
                      data-property-data-format="2"
                      data-property-parent-context="SalesInvoiceLine"
                      >{{formatNumber amountExcludingTax 2}}</span
                    ></span
                  >
                </p>
              </td>
              <td class="query-table-cell" style="border: 1px solid #dfdfdf; padding: 2px; text-align: right">
                <section
                  class="record-context"
                  data-context-object-type="SalesInvoiceLineTax"
                  data-context-object-path="taxes.query.edges.0.node"
                  data-context-filter="[]"
                  data-context-list-order="{}"
                  data-alias="cCgdCeBx"
                >
                  <!--{{#with cCgdCeBx.query.edges.0.node}}-->
                  <div class="report-context-body">
                    <p>&nbsp;</p>
                    <p>
                      <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                        ><span
                          class="property"
                          data-property-display-label="Tax rate"
                          data-property-data-type="Decimal"
                          data-property-name="taxRate"
                          data-property-data-format="2"
                          data-property-parent-context="SalesInvoiceLineTax"
                          >{{formatNumber taxRate 2}}</span
                        >{{ translatedContent "0bcef9c45bd8a48eda1b26eb0c61c869" }}</span
                      >
                    </p>
                  </div>
                  <!--{{/with}}--><span class="report-context-footer">&nbsp;</span>
                </section>
              </td>
              <td class="query-table-cell" style="border: 1px solid #dfdfdf; padding: 2px; text-align: right">
                <p>
                  <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                    ><span
                      class="property"
                      data-property-display-label="Symbol"
                      data-property-data-type="String"
                      data-property-name="document.currency.symbol"
                      data-property-data-format=""
                      data-property-parent-context="SalesInvoiceLine"
                      >{{document.currency.symbol}}</span
                    ><span
                      class="property"
                      data-property-display-label="Line amount including tax"
                      data-property-data-type="Decimal"
                      data-property-name="amountIncludingTax"
                      data-property-data-format="2"
                      data-property-parent-context="SalesInvoiceLine"
                      >{{formatNumber amountIncludingTax 2}}</span
                    ></span
                  >
                </p>
              </td>
            </tr>
            <!--{{#printBreakIfLast  'netPrice' 'sum' 'amountExcludingTax' 'sum' 'amountIncludingTax' 'sum'}}-->
            <tr class="query-table-row" data-footer-group="footer">
              <td class="query-table-cell" style="background-color: #dfdfdf; border: 1px solid #dfdfdf; padding: 2px; vertical-align: middle">
                <p>
                  <span style="color: #198e59; font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                    ><strong>{{ translatedContent "edd291d1439a6c1c18fe38bee411580f" }}</strong></span
                  >
                </p>
              </td>
              <td class="query-table-cell" style="background-color: #dfdfdf; border: 1px solid #dfdfdf; padding: 2px; text-align: right">
                <p>&nbsp;</p>
              </td>
              <td class="query-table-cell" style="background-color: #dfdfdf; border: 1px solid #dfdfdf; padding: 2px; text-align: right">
                <p>&nbsp;</p>
              </td>
              <td class="query-table-cell" style="background-color: #dfdfdf; border: 1px solid #dfdfdf; padding: 2px; text-align: right">
                <p>&nbsp;</p>
              </td>
              <td class="query-table-cell" style="background-color: #dfdfdf; border: 1px solid #dfdfdf; padding: 2px; text-align: right">
                <p>&nbsp;</p>
              </td>
              <td class="query-table-cell" style="background-color: #dfdfdf; border: 1px solid #dfdfdf; padding: 2px; text-align: right">
                <p>&nbsp;</p>
              </td>
              <td class="query-table-cell" style="background-color: #dfdfdf; border: 1px solid #dfdfdf; padding: 2px; text-align: right">
                <p>
                  <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                    ><strong
                      ><span
                        class="property"
                        data-property-display-label="Symbol"
                        data-property-data-type="String"
                        data-property-name="document.currency.symbol"
                        data-property-data-format=""
                        data-property-parent-context="SalesInvoiceLine"
                        >{{document.currency.symbol}}</span
                      ><span
                        class="property"
                        data-property-display-label="Net price"
                        data-property-data-type="Decimal"
                        data-property-name="_blockAggregatedData.netPrice.sum"
                        data-property-data-format="2"
                        data-property-parent-context="SalesInvoiceLine"
                        >{{formatNumber _blockAggregatedData.netPrice.sum 2}}</span
                      ></strong
                    ></span
                  >
                </p>
              </td>
              <td class="query-table-cell" style="background-color: #dfdfdf; border: 1px solid #dfdfdf; padding: 2px; text-align: right">
                <p>
                  <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                    ><strong
                      ><span
                        class="property"
                        data-property-display-label="Symbol"
                        data-property-data-type="String"
                        data-property-name="document.currency.symbol"
                        data-property-data-format=""
                        data-property-parent-context="SalesInvoiceLine"
                        >{{document.currency.symbol}}</span
                      ><span
                        class="property"
                        data-property-display-label="Line amount excluding tax"
                        data-property-data-type="Decimal"
                        data-property-name="_blockAggregatedData.amountExcludingTax.sum"
                        data-property-data-format="2"
                        data-property-parent-context="SalesInvoiceLine"
                        >{{formatNumber _blockAggregatedData.amountExcludingTax.sum 2}}</span
                      ></strong
                    ></span
                  >
                </p>
              </td>
              <td class="query-table-cell" style="background-color: #dfdfdf; border: 1px solid #dfdfdf; padding: 2px; text-align: right">
                <p>&nbsp;</p>
              </td>
              <td class="query-table-cell" style="background-color: #dfdfdf; border: 1px solid #dfdfdf; padding: 2px; text-align: right">
                <p>
                  <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                    ><strong
                      ><span
                        class="property"
                        data-property-display-label="Symbol"
                        data-property-data-type="String"
                        data-property-name="document.currency.symbol"
                        data-property-data-format=""
                        data-property-parent-context="SalesInvoiceLine"
                        >{{document.currency.symbol}}</span
                      ><span
                        class="property"
                        data-property-display-label="Line amount including tax"
                        data-property-data-type="Decimal"
                        data-property-name="_blockAggregatedData.amountIncludingTax.sum"
                        data-property-data-format="2"
                        data-property-parent-context="SalesInvoiceLine"
                        >{{formatNumber _blockAggregatedData.amountIncludingTax.sum 2}}</span
                      ></strong
                    ></span
                  >
                </p>
              </td>
            </tr>
            <!--{{/printBreakIfLast}}-->
            <tr class="query-table-row" data-hidden="1">
              <td class="query-table-cell" colspan="10">
                <p>&nbsp;</p>
              </td>
            </tr>
            <tr class="query-table-row" data-hidden="1">
              <td class="query-table-cell" colspan="10">
                <p>&nbsp;</p>
              </td>
            </tr>
            <!--{{/with}}{{/each}}-->
            <tr class="query-table-row" data-hidden="1">
              <td class="query-table-cell" colspan="10">
                <p>&nbsp;</p>
              </td>
            </tr>
          </tbody>
          <tfoot class="query-table-footer">
            <tr class="query-table-row">
              <td class="query-table-cell">
                <p>&nbsp;</p>
              </td>
              <td class="query-table-cell">
                <p>&nbsp;</p>
              </td>
              <td class="query-table-cell">
                <p>&nbsp;</p>
              </td>
              <td class="query-table-cell">
                <p>&nbsp;</p>
              </td>
              <td class="query-table-cell">
                <p>&nbsp;</p>
              </td>
              <td class="query-table-cell">
                <p>&nbsp;</p>
              </td>
              <td class="query-table-cell">
                <p>&nbsp;</p>
              </td>
              <td class="query-table-cell">
                <p>&nbsp;</p>
              </td>
              <td class="query-table-cell">
                <p>&nbsp;</p>
              </td>
              <td class="query-table-cell">
                <p>&nbsp;</p>
              </td>
            </tr>
          </tfoot>
        </table>
        <section class="unbreakable-block">
          <div class="unbreakable-block-body">
            <figure class="table" style="width: 100%">
              <table class="ck-table-resized">
                <colgroup>
                  <col style="width: 50.6%" />
                  <col style="width: 49.4%" />
                </colgroup>
                <tbody>
                  <tr>
                    <td>
                      <p>&nbsp;</p>
                      <section
                        class="conditional-block"
                        data-context-condition='[{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"1","value1":{"label":"Name","data":{"name":"name","title":"Name","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"localizedName","targetNode":"","isCollection":false,"kind":"SCALAR","label":"Name","node":"String","iconType":"csv"},"id":"paymentTerm.name","key":"paymentTerm.name","labelKey":"Name","labelPath":"Payment term > Name"},"value2":null,"key":"1","operator":"notEmpty"}]'
                      >
                        <!--{{#if ( not ( or ( eq paymentTerm.name null ) ( eq paymentTerm.name "" ) ( eq paymentTerm.name undefined ) ) )}}-->
                        <div class="conditional-block-body">
                          <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                            ><strong>{{ translatedContent "6af6aa3c07e3f393b7c213a6a0edd35f" }}</strong>&nbsp;<span
                              class="property"
                              data-property-display-label="Name"
                              data-property-data-type="String"
                              data-property-name="paymentTerm.name"
                              data-property-data-format=""
                              data-property-parent-context="SalesInvoice"
                              >{{paymentTerm.name}}</span
                            ></span
                          >
                        </div>
                        <!--{{/if}}-->
                        <div class="conditional-block-footer">&nbsp;</div>
                      </section>
                      <section
                        class="conditional-block"
                        data-context-condition='[{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"1","value1":{"label":"Due date","data":{"name":"dueDate","title":"Due date","canSort":true,"canFilter":true,"type":"Date","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"","targetNode":"","isCollection":false,"kind":"SCALAR","label":"Due date","node":"Date","iconType":"csv"},"id":"dueDate","key":"dueDate","labelKey":"Due date","labelPath":"Due date"},"value2":null,"key":"1","operator":"notEmpty"}]'
                      >
                        <!--{{#if ( not ( or ( eq dueDate null ) ( eq dueDate "" ) ( eq dueDate undefined ) ) )}}-->
                        <div class="conditional-block-body">
                          <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                            ><strong>{{ translatedContent "e14cfbfb2298cf2aa5aa8fdc717d06c6" }}&nbsp;</strong
                            ><span
                              class="property"
                              data-property-display-label="Due date"
                              data-property-data-type="Date"
                              data-property-name="dueDate"
                              data-property-data-format="FullDate"
                              data-property-parent-context="SalesInvoice"
                              >{{formatDate dueDate 'FullDate'}}</span
                            ></span
                          >
                        </div>
                        <!--{{/if}}-->
                        <div class="conditional-block-footer">&nbsp;</div>
                      </section>
                      <p>&nbsp;</p>
                      <section
                        class="conditional-block"
                        data-context-condition='[{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"1","value1":{"label":"Discount payment type","data":{"name":"discountPaymentType","title":"Discount payment type","canSort":true,"canFilter":true,"type":"Enum","isCustom":false,"isMutable":false,"isStored":true,"isOnInputType":true,"isOnOutputType":true,"enumType":"@sage/xtrem-master-data/DiscountOrPenaltyType","dataType":"discountOrPenaltyTypeDataType","targetNode":"","isCollection":false,"kind":"SCALAR","label":"Discount payment type","enumValues":["percentage","amount"],"node":"Enum","iconType":"csv"},"id":"discountPaymentType","key":"discountPaymentType","labelKey":"Discount payment type","labelPath":"Discount payment type"},"value2":["percentage"],"key":"1","operator":"set"},{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"2","value1":{"label":"Discount payment amount","data":{"name":"discountPaymentAmount","title":"Discount payment amount","canSort":true,"canFilter":true,"type":"Decimal","isCustom":false,"isMutable":false,"isStored":true,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"priceInSalesPrice","targetNode":"","isCollection":false,"kind":"SCALAR","label":"Discount payment amount","node":"Decimal","iconType":"csv"},"id":"discountPaymentAmount","key":"discountPaymentAmount","labelKey":"Discount payment amount","labelPath":"Discount payment amount"},"value2":null,"key":"2","operator":"notEmpty"},{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"3","value1":{"label":"Discount payment amount","data":{"name":"discountPaymentAmount","title":"Discount payment amount","canSort":true,"canFilter":true,"type":"Decimal","isCustom":false,"isMutable":false,"isStored":true,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"priceInSalesPrice","targetNode":"","isCollection":false,"kind":"SCALAR","label":"Discount payment amount","node":"Decimal","iconType":"csv"},"id":"discountPaymentAmount","key":"discountPaymentAmount","labelKey":"Discount payment amount","labelPath":"Discount payment amount"},"value2":"0","key":"3","operator":"notEqual"}]'
                      >
                        <!--{{#if ( and (or ( eq discountPaymentType "percentage" )) ( not ( or ( eq discountPaymentAmount null ) ( eq discountPaymentAmount "" ) ( eq discountPaymentAmount undefined ) ) ) ( not ( eq discountPaymentAmount "0" ) ) )}}-->
                        <div class="conditional-block-body">
                          <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                            ><span
                              class="property"
                              data-property-display-label="Discount payment amount"
                              data-property-data-type="Decimal"
                              data-property-name="discountPaymentAmount"
                              data-property-data-format="2"
                              data-property-parent-context="SalesInvoice"
                              >{{formatNumber discountPaymentAmount 2}}&nbsp;</span
                            >{{ translatedContent "54dc979dbdbbf0af917f81c453d0189d" }}&nbsp;<span
                              class="property"
                              data-property-display-label="Discount payment before date"
                              data-property-data-type="Date"
                              data-property-name="discountPaymentBeforeDate"
                              data-property-data-format="FullDate"
                              data-property-parent-context="SalesInvoice"
                              >{{formatDate discountPaymentBeforeDate 'FullDate'}}</span
                            ></span
                          >
                        </div>
                        <!--{{/if}}-->
                        <div class="conditional-block-footer">&nbsp;</div>
                      </section>
                      <section
                        class="conditional-block"
                        data-context-condition='[{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"1","value1":{"label":"Discount payment type","data":{"name":"discountPaymentType","title":"Discount payment type","canSort":true,"canFilter":true,"type":"Enum","isCustom":false,"isMutable":false,"isStored":true,"isOnInputType":true,"isOnOutputType":true,"enumType":"@sage/xtrem-master-data/DiscountOrPenaltyType","dataType":"discountOrPenaltyTypeDataType","targetNode":"","isCollection":false,"kind":"SCALAR","label":"Discount payment type","enumValues":["percentage","amount"],"node":"Enum","iconType":"csv"},"id":"discountPaymentType","key":"discountPaymentType","labelKey":"Discount payment type","labelPath":"Discount payment type"},"value2":["amount"],"key":"1","operator":"set"},{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"2","value1":{"label":"Discount payment amount","data":{"name":"discountPaymentAmount","title":"Discount payment amount","canSort":true,"canFilter":true,"type":"Decimal","isCustom":false,"isMutable":false,"isStored":true,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"priceInSalesPrice","targetNode":"","isCollection":false,"kind":"SCALAR","label":"Discount payment amount","node":"Decimal","iconType":"csv"},"id":"discountPaymentAmount","key":"discountPaymentAmount","labelKey":"Discount payment amount","labelPath":"Discount payment amount"},"value2":null,"key":"2","operator":"notEmpty"},{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"3","value1":{"label":"Discount payment amount","data":{"name":"discountPaymentAmount","title":"Discount payment amount","canSort":true,"canFilter":true,"type":"Decimal","isCustom":false,"isMutable":false,"isStored":true,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"priceInSalesPrice","targetNode":"","isCollection":false,"kind":"SCALAR","label":"Discount payment amount","node":"Decimal","iconType":"csv"},"id":"discountPaymentAmount","key":"discountPaymentAmount","labelKey":"Discount payment amount","labelPath":"Discount payment amount"},"value2":"0","key":"3","operator":"notEqual"}]'
                      >
                        <!--{{#if ( and (or ( eq discountPaymentType "amount" )) ( not ( or ( eq discountPaymentAmount null ) ( eq discountPaymentAmount "" ) ( eq discountPaymentAmount undefined ) ) ) ( not ( eq discountPaymentAmount "0" ) ) )}}-->
                        <div class="conditional-block-body">
                          <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                            ><span
                              class="property"
                              data-property-display-label="Symbol"
                              data-property-data-type="String"
                              data-property-name="currency.symbol"
                              data-property-data-format=""
                              data-property-parent-context="SalesInvoice"
                              >{{currency.symbol}}</span
                            ><span
                              class="property"
                              data-property-display-label="Discount payment amount"
                              data-property-data-type="Decimal"
                              data-property-name="discountPaymentAmount"
                              data-property-data-format="2"
                              data-property-parent-context="SalesInvoice"
                              >{{formatNumber discountPaymentAmount 2}}&nbsp;</span
                            >{{ translatedContent "54dc979dbdbbf0af917f81c453d0189e" }}&nbsp;<span
                              class="property"
                              data-property-display-label="Discount payment before date"
                              data-property-data-type="Date"
                              data-property-name="discountPaymentBeforeDate"
                              data-property-data-format="FullDate"
                              data-property-parent-context="SalesInvoice"
                              >{{formatDate discountPaymentBeforeDate 'FullDate'}}</span
                            ></span
                          >
                        </div>
                        <!--{{/if}}-->
                        <div class="conditional-block-footer">&nbsp;</div>
                      </section>
                      <section
                        class="conditional-block"
                        data-context-condition='[{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"1","value1":{"label":"Discount payment type","data":{"name":"discountPaymentType","title":"Discount payment type","canSort":true,"canFilter":true,"type":"Enum","isCustom":false,"isMutable":false,"isStored":true,"isOnInputType":true,"isOnOutputType":true,"enumType":"@sage/xtrem-master-data/DiscountOrPenaltyType","dataType":"discountOrPenaltyTypeDataType","targetNode":"","isCollection":false,"kind":"SCALAR","label":"Discount payment type","enumValues":["percentage","amount"],"node":"Enum","iconType":"csv"},"id":"discountPaymentType","key":"discountPaymentType","labelKey":"Discount payment type","labelPath":"Discount payment type"},"value2":null,"key":"1","operator":"empty"},{"conjunction":"or","valueType1":"property","valueType2":"constant","_id":"2","value1":{"label":"Discount payment amount","data":{"name":"discountPaymentAmount","title":"Discount payment amount","canSort":true,"canFilter":true,"type":"Decimal","isCustom":false,"isMutable":false,"isStored":true,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"priceInSalesPrice","targetNode":"","isCollection":false,"kind":"SCALAR","label":"Discount payment amount","node":"Decimal","iconType":"csv"},"id":"discountPaymentAmount","key":"discountPaymentAmount","labelKey":"Discount payment amount","labelPath":"Discount payment amount"},"value2":"0","key":"2","operator":"equals"},{"conjunction":"or","valueType1":"property","valueType2":"constant","_id":"3","value1":{"label":"Discount payment amount","data":{"name":"discountPaymentAmount","title":"Discount payment amount","canSort":true,"canFilter":true,"type":"Decimal","isCustom":false,"isMutable":false,"isStored":true,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"priceInSalesPrice","targetNode":"","isCollection":false,"kind":"SCALAR","label":"Discount payment amount","node":"Decimal","iconType":"csv"},"id":"discountPaymentAmount","key":"discountPaymentAmount","labelKey":"Discount payment amount","labelPath":"Discount payment amount"},"value2":null,"key":"3","operator":"empty"}]'
                      >
                        <!--{{#if ( and (or ( eq discountPaymentType null )) ( or ( eq discountPaymentAmount null ) ( eq discountPaymentAmount "" ) ( eq discountPaymentAmount undefined ) ) ( not ( eq discountPaymentAmount "0" ) ) )}}-->
                        <div class="conditional-block-body">
                          <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                            >{{ translatedContent "345951578b0088131bd0a7bfbab1e8b7" }}</span
                          >
                        </div>
                        <!--{{/if}}-->
                        <div class="conditional-block-footer">&nbsp;</div>
                      </section>
                      <section
                        class="conditional-block"
                        data-context-condition='[{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"1","value1":{"label":"Penalty payment type","data":{"name":"penaltyPaymentType","title":"Penalty payment type","canSort":true,"canFilter":true,"type":"Enum","isCustom":false,"isMutable":false,"isStored":true,"isOnInputType":true,"isOnOutputType":true,"enumType":"@sage/xtrem-master-data/DiscountOrPenaltyType","dataType":"discountOrPenaltyTypeDataType","targetNode":"","isCollection":false,"kind":"SCALAR","label":"Penalty payment type","enumValues":["percentage","amount"],"node":"Enum","iconType":"csv"},"id":"penaltyPaymentType","key":"penaltyPaymentType","labelKey":"Penalty payment type","labelPath":"Penalty payment type"},"value2":["percentage"],"key":"1","operator":"set"},{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"2","value1":{"label":"Penalty payment amount","data":{"name":"penaltyPaymentAmount","title":"Penalty payment amount","canSort":true,"canFilter":true,"type":"Decimal","isCustom":false,"isMutable":false,"isStored":true,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"priceInSalesPrice","targetNode":"","isCollection":false,"kind":"SCALAR","label":"Penalty payment amount","node":"Decimal","iconType":"csv"},"id":"penaltyPaymentAmount","key":"penaltyPaymentAmount","labelKey":"Penalty payment amount","labelPath":"Penalty payment amount"},"value2":null,"key":"2","operator":"notEmpty"},{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"3","value1":{"label":"Penalty payment amount","data":{"name":"penaltyPaymentAmount","title":"Penalty payment amount","canSort":true,"canFilter":true,"type":"Decimal","isCustom":false,"isMutable":false,"isStored":true,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"priceInSalesPrice","targetNode":"","isCollection":false,"kind":"SCALAR","label":"Penalty payment amount","node":"Decimal","iconType":"csv"},"id":"penaltyPaymentAmount","key":"penaltyPaymentAmount","labelKey":"Penalty payment amount","labelPath":"Penalty payment amount"},"value2":"0","key":"3","operator":"notEqual"}]'
                      >
                        <!--{{#if ( and (or ( eq penaltyPaymentType "percentage" )) ( not ( or ( eq penaltyPaymentAmount null ) ( eq penaltyPaymentAmount "" ) ( eq penaltyPaymentAmount undefined ) ) ) ( not ( eq penaltyPaymentAmount "0" ) ) )}}-->
                        <div class="conditional-block-body">
                          <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                            ><span
                              class="property"
                              data-property-display-label="Penalty payment amount"
                              data-property-data-type="Decimal"
                              data-property-name="penaltyPaymentAmount"
                              data-property-data-format="2"
                              data-property-parent-context="SalesInvoice"
                              >{{formatNumber penaltyPaymentAmount 2}}&nbsp;</span
                            >{{ translatedContent "a229634623ed01d6fec61f1d47de427b" }}</span
                          >
                        </div>
                        <!--{{/if}}-->
                        <div class="conditional-block-footer">&nbsp;</div>
                      </section>
                      <section
                        class="conditional-block"
                        data-context-condition='[{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"1","value1":{"label":"Penalty payment type","data":{"name":"penaltyPaymentType","title":"Penalty payment type","canSort":true,"canFilter":true,"type":"Enum","isCustom":false,"isMutable":false,"isStored":true,"isOnInputType":true,"isOnOutputType":true,"enumType":"@sage/xtrem-master-data/DiscountOrPenaltyType","dataType":"discountOrPenaltyTypeDataType","targetNode":"","isCollection":false,"kind":"SCALAR","label":"Penalty payment type","enumValues":["percentage","amount"],"node":"Enum","iconType":"csv"},"id":"penaltyPaymentType","key":"penaltyPaymentType","labelKey":"Penalty payment type","labelPath":"Penalty payment type"},"value2":["amount"],"key":"1","operator":"set"},{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"2","value1":{"label":"Penalty payment amount","data":{"name":"penaltyPaymentAmount","title":"Penalty payment amount","canSort":true,"canFilter":true,"type":"Decimal","isCustom":false,"isMutable":false,"isStored":true,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"priceInSalesPrice","targetNode":"","isCollection":false,"kind":"SCALAR","label":"Penalty payment amount","node":"Decimal","iconType":"csv"},"id":"penaltyPaymentAmount","key":"penaltyPaymentAmount","labelKey":"Penalty payment amount","labelPath":"Penalty payment amount"},"value2":null,"key":"2","operator":"notEmpty"},{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"3","value1":{"label":"Penalty payment amount","data":{"name":"penaltyPaymentAmount","title":"Penalty payment amount","canSort":true,"canFilter":true,"type":"Decimal","isCustom":false,"isMutable":false,"isStored":true,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"priceInSalesPrice","targetNode":"","isCollection":false,"kind":"SCALAR","label":"Penalty payment amount","node":"Decimal","iconType":"csv"},"id":"penaltyPaymentAmount","key":"penaltyPaymentAmount","labelKey":"Penalty payment amount","labelPath":"Penalty payment amount"},"value2":"0","key":"3","operator":"notEqual"}]'
                      >
                        <!--{{#if ( and (or ( eq penaltyPaymentType "amount" )) ( not ( or ( eq penaltyPaymentAmount null ) ( eq penaltyPaymentAmount "" ) ( eq penaltyPaymentAmount undefined ) ) ) ( not ( eq penaltyPaymentAmount "0" ) ) )}}-->
                        <div class="conditional-block-body">
                          <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                            ><span
                              class="property"
                              data-property-display-label="Symbol"
                              data-property-data-type="String"
                              data-property-name="currency.symbol"
                              data-property-data-format=""
                              data-property-parent-context="SalesInvoice"
                              >{{currency.symbol}}</span
                            ><span
                              class="property"
                              data-property-display-label="Penalty payment amount"
                              data-property-data-type="Decimal"
                              data-property-name="penaltyPaymentAmount"
                              data-property-data-format="2"
                              data-property-parent-context="SalesInvoice"
                              >{{formatNumber penaltyPaymentAmount 2}}&nbsp;</span
                            >{{ translatedContent "e82b32385b756e8476ac5de39323aa09" }}</span
                          >
                        </div>
                        <!--{{/if}}-->
                        <div class="conditional-block-footer">&nbsp;</div>
                      </section>
                      <section
                        class="conditional-block"
                        data-context-condition='[{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"1","value1":{"label":"ID","data":{"name":"id","title":"ID","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isStored":true,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"","targetNode":"","isCollection":false,"kind":"SCALAR","label":"ID","node":"String","iconType":"csv"},"id":"salesSite.legalCompany.legislation.id","key":"salesSite.legalCompany.legislation.id","labelKey":"ID","labelPath":"Sales site > Legal company > Legislation > ID"},"value2":"FR","key":"1","operator":"equals"}]'
                      >
                        <!--{{#if ( eq salesSite.legalCompany.legislation.id "FR" )}}-->
                        <div class="conditional-block-body">
                          <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                            >{{ translatedContent "213317e9b791d3e46beb61b27650d6c6" }}</span
                          >
                        </div>
                        <!--{{/if}}-->
                        <div class="conditional-block-footer">&nbsp;</div>
                      </section>
                    </td>
                    <td style="border-color: #dfdfdf">
                      <table
                        class="query-table"
                        data-context-object-type="SalesInvoiceTax"
                        data-context-object-path="taxes.query.edges"
                        data-context-filter="[]"
                        data-context-list-order='{"tax":"ascending","taxableAmount":"ascending","taxRate":"ascending","taxAmount":"ascending"}'
                        data-alias="JMxxnOeV"
                      >
                        <thead class="query-table-head">
                          <tr class="query-table-row">
                            <td class="query-table-cell" style="background-color: #0000001a; border: 1px solid #dfdfdf; padding: 2px">
                              <p>
                                <span style="color: #198e59; font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                                  ><strong>{{ translatedContent "4b78ac8eb158840e9638a3aeb26c4a9d" }}</strong></span
                                >
                              </p>
                            </td>
                            <td class="query-table-cell" style="background-color: #0000001a; border: 1px solid #dfdfdf; padding: 2px">
                              <p>
                                <span style="color: #198e59; font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                                  ><strong>{{ translatedContent "59a99666437cbc6e878c5d4ef3d1c993" }}</strong></span
                                >
                              </p>
                            </td>
                            <td class="query-table-cell" style="background-color: #0000001a; border: 1px solid #dfdfdf; padding: 2px">
                              <p>
                                <span style="color: #198e59; font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                                  ><strong>{{ translatedContent "20a34c4e30c5bbe1d3f870ac55f0d831" }}</strong></span
                                >
                              </p>
                            </td>
                            <td class="query-table-cell" style="background-color: #0000001a; border: 1px solid #dfdfdf; padding: 2px">
                              <p>
                                <span style="color: #198e59; font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                                  ><strong>{{ translatedContent "617fe2857b30555c036a380e07ce16e3" }}</strong></span
                                >
                              </p>
                            </td>
                          </tr>
                        </thead>
                        <tbody class="query-table-body">
                          <!--{{#each JMxxnOeV.query.edges}}{{#with node}}-->
                          <tr class="query-table-row">
                            <td class="query-table-cell" style="border: 1px solid #dfdfdf; padding: 2px; vertical-align: middle">
                              <p>
                                <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                                  ><span
                                    class="property"
                                    data-property-display-label="Tax"
                                    data-property-data-type="String"
                                    data-property-name="tax"
                                    data-property-data-format=""
                                    data-property-parent-context="SalesInvoiceTax"
                                    >{{tax}}</span
                                  ></span
                                >
                              </p>
                            </td>
                            <td class="query-table-cell" style="border: 1px solid #dfdfdf; padding: 2px; text-align: right; vertical-align: middle">
                              <p>
                                <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                                  ><span
                                    class="property"
                                    data-property-display-label="Symbol"
                                    data-property-data-type="String"
                                    data-property-name="currency.symbol"
                                    data-property-data-format=""
                                    data-property-parent-context="SalesInvoiceTax"
                                    >{{currency.symbol}}</span
                                  ><span
                                    class="property"
                                    data-property-display-label="Taxable amount"
                                    data-property-data-type="Decimal"
                                    data-property-name="taxableAmount"
                                    data-property-data-format="2"
                                    data-property-parent-context="SalesInvoiceTax"
                                    >{{formatNumber taxableAmount 2}}</span
                                  ></span
                                >
                              </p>
                            </td>
                            <td class="query-table-cell" style="border: 1px solid #dfdfdf; padding: 2px; text-align: right; vertical-align: middle">
                              <p>
                                <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                                  ><span
                                    class="property"
                                    data-property-display-label="Tax rate"
                                    data-property-data-type="Decimal"
                                    data-property-name="taxRate"
                                    data-property-data-format="2"
                                    data-property-parent-context="SalesInvoiceTax"
                                    >{{formatNumber taxRate 2}}</span
                                  >{{ translatedContent "0bcef9c45bd8a48eda1b26eb0c61c869" }}</span
                                >
                              </p>
                            </td>
                            <td class="query-table-cell" style="border: 1px solid #dfdfdf; padding: 2px; text-align: right; vertical-align: middle">
                              <p>
                                <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                                  ><span
                                    class="property"
                                    data-property-display-label="Symbol"
                                    data-property-data-type="String"
                                    data-property-name="currency.symbol"
                                    data-property-data-format=""
                                    data-property-parent-context="SalesInvoiceTax"
                                    >{{currency.symbol}}</span
                                  ><span
                                    class="property"
                                    data-property-display-label="Tax amount"
                                    data-property-data-type="Decimal"
                                    data-property-name="taxAmount"
                                    data-property-data-format="2"
                                    data-property-parent-context="SalesInvoiceTax"
                                    >{{formatNumber taxAmount 2}}</span
                                  ></span
                                >
                              </p>
                            </td>
                          </tr>
                          <tr class="query-table-row" data-hidden="1">
                            <td class="query-table-cell" colspan="4">
                              <p>&nbsp;</p>
                            </td>
                          </tr>
                          <!--{{/with}}{{/each}}-->
                          <tr class="query-table-row" data-hidden="1">
                            <td class="query-table-cell" colspan="4">
                              <p>&nbsp;</p>
                            </td>
                          </tr>
                        </tbody>
                        <tfoot class="query-table-footer">
                          <tr class="query-table-row">
                            <td class="query-table-cell">
                              <p>&nbsp;</p>
                            </td>
                            <td class="query-table-cell">
                              <p>&nbsp;</p>
                            </td>
                            <td class="query-table-cell">
                              <p>&nbsp;</p>
                            </td>
                            <td class="query-table-cell">
                              <p>&nbsp;</p>
                            </td>
                          </tr>
                        </tfoot>
                      </table>
                      <figure class="table">
                        <table>
                          <tbody>
                            <tr>
                              <td style="background-color: #dfdfdf; border-width: 2px">
                                <span style="color: #198e59; font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                                  ><strong>{{ translatedContent "d0c24d46a10ca7d48fdddf1bd6768e4e" }}</strong></span
                                >
                              </td>
                              <td style="background-color: #dfdfdf; border-width: 2px">
                                <span style="color: #198e59; font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                                  ><strong>{{ translatedContent "ff9e6ead51b6937f49449b030045d815" }}</strong></span
                                >
                              </td>
                              <td style="background-color: #dfdfdf; border-width: 2px">
                                <span style="color: #198e59; font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                                  ><strong>{{ translatedContent "ba12e59b20a9720f70fb907a55cd2620" }}</strong></span
                                >
                              </td>
                              <td style="background-color: #dfdfdf; border-width: 2px">
                                <span style="color: #198e59; font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                                  ><strong>{{ translatedContent "69bed661e6196fb4ee5b7d4b2ed0c3e6" }}</strong></span
                                >
                              </td>
                            </tr>
                            <tr>
                              <td style="text-align: right">
                                <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                                  ><span
                                    class="property"
                                    data-property-display-label="Symbol"
                                    data-property-data-type="String"
                                    data-property-name="currency.symbol"
                                    data-property-data-format=""
                                    data-property-parent-context="SalesInvoice"
                                    >{{currency.symbol}}</span
                                  ><span
                                    class="property"
                                    data-property-display-label="Total amount excluding tax"
                                    data-property-data-type="Decimal"
                                    data-property-name="totalAmountExcludingTax"
                                    data-property-data-format="2"
                                    data-property-parent-context="SalesInvoice"
                                    >{{formatNumber totalAmountExcludingTax 2}}</span
                                  ></span
                                >
                              </td>
                              <td style="text-align: right">
                                <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                                  ><span
                                    class="property"
                                    data-property-display-label="Symbol"
                                    data-property-data-type="String"
                                    data-property-name="currency.symbol"
                                    data-property-data-format=""
                                    data-property-parent-context="SalesInvoice"
                                    >{{currency.symbol}}</span
                                  ><span
                                    class="property"
                                    data-property-display-label="Total tax amount"
                                    data-property-data-type="Decimal"
                                    data-property-name="totalTaxAmount"
                                    data-property-data-format="2"
                                    data-property-parent-context="SalesInvoice"
                                    >{{formatNumber totalTaxAmount 2}}</span
                                  ></span
                                >
                              </td>
                              <td style="text-align: right">
                                <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                                  ><span
                                    class="property"
                                    data-property-display-label="Symbol"
                                    data-property-data-type="String"
                                    data-property-name="currency.symbol"
                                    data-property-data-format=""
                                    data-property-parent-context="SalesInvoice"
                                    >{{currency.symbol}}</span
                                  ><span
                                    class="property"
                                    data-property-display-label="Total amount including tax"
                                    data-property-data-type="Decimal"
                                    data-property-name="totalAmountIncludingTax"
                                    data-property-data-format="2"
                                    data-property-parent-context="SalesInvoice"
                                    >{{formatNumber totalAmountIncludingTax 2}}</span
                                  ></span
                                >
                              </td>
                              <td style="text-align: right">
                                <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                                  ><strong
                                    ><span
                                      class="property"
                                      data-property-display-label="Symbol"
                                      data-property-data-type="String"
                                      data-property-name="currency.symbol"
                                      data-property-data-format=""
                                      data-property-parent-context="SalesInvoice"
                                      >{{currency.symbol}}</span
                                    ><span
                                      class="property"
                                      data-property-display-label="Total amount including tax"
                                      data-property-data-type="Decimal"
                                      data-property-name="totalAmountIncludingTax"
                                      data-property-data-format="2"
                                      data-property-parent-context="SalesInvoice"
                                      >{{formatNumber totalAmountIncludingTax 2}}</span
                                    ></strong
                                  ></span
                                >
                              </td>
                            </tr>
                          </tbody>
                        </table>
                      </figure>
                    </td>
                  </tr>
                </tbody>
              </table>
            </figure>
          </div>
        </section>
        <section class="unbreakable-block">
          <div class="unbreakable-block-body">
            <figure class="table">
              <table>
                <tbody>
                  <tr>
                    <td style="text-align: center">
                      <section
                        class="conditional-block"
                        data-context-condition='[{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"1","value1":{"label":"Legal form","data":{"name":"legalForm","title":"Legal form","canSort":true,"canFilter":true,"type":"Enum","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":"@sage/xtrem-structure/LegalForm","dataType":"legalFormDataType","targetNode":"","isCollection":false,"kind":"SCALAR","label":"Legal form","enumValues":["SARL","EURL","SELARL","SA","SAS","SASU","SNC","SCP"],"node":"Enum","iconType":"csv"},"id":"salesSite.legalCompany.legalForm","key":"salesSite.legalCompany.legalForm","labelKey":"Legal form","labelPath":"Sales site > Legal company > Legal form"},"value2":null,"key":"1","operator":"notEmpty"},{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"2","value1":{"label":"Name","data":{"name":"name","title":"Name","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"name","targetNode":"","isCollection":false,"kind":"SCALAR","label":"Name","node":"String","iconType":"csv"},"id":"salesSite.legalCompany.name","key":"salesSite.legalCompany.name","labelKey":"Name","labelPath":"Sales site > Legal company > Name"},"value2":null,"key":"2","operator":"notEmpty"},{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"3","value1":{"label":"RCS","data":{"name":"rcs","title":"RCS","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"description","targetNode":"","isCollection":false,"kind":"SCALAR","label":"RCS","node":"String","iconType":"csv"},"id":"salesSite.legalCompany.rcs","key":"salesSite.legalCompany.rcs","labelKey":"RCS","labelPath":"Sales site > Legal company > RCS"},"value2":null,"key":"3","operator":"notEmpty"},{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"4","value1":{"label":"NAF","data":{"name":"naf","title":"NAF","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"description","targetNode":"","isCollection":false,"kind":"SCALAR","label":"NAF","node":"String","iconType":"csv"},"id":"salesSite.legalCompany.naf","key":"salesSite.legalCompany.naf","labelKey":"NAF","labelPath":"Sales site > Legal company > NAF"},"value2":null,"key":"4","operator":"notEmpty"}]'
                      >
                        <!--{{#if ( and ( not ( or ( eq salesSite.legalCompany.legalForm null ) ( eq salesSite.legalCompany.legalForm "" ) ( eq salesSite.legalCompany.legalForm undefined ) ) ) ( not ( or ( eq salesSite.legalCompany.name null ) ( eq salesSite.legalCompany.name "" ) ( eq salesSite.legalCompany.name undefined ) ) ) ( not ( or ( eq salesSite.legalCompany.rcs null ) ( eq salesSite.legalCompany.rcs "" ) ( eq salesSite.legalCompany.rcs undefined ) ) ) ( not ( or ( eq salesSite.legalCompany.naf null ) ( eq salesSite.legalCompany.naf "" ) ( eq salesSite.legalCompany.naf undefined ) ) ) )}}-->
                        <div class="conditional-block-body">
                          <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                            ><strong
                              ><span
                                class="property"
                                data-property-display-label="Legal form"
                                data-property-data-type="Enum"
                                data-property-name="salesSite.legalCompany.legalForm"
                                data-property-data-format=""
                                data-property-parent-context="SalesInvoice"
                                >{{salesSite.legalCompany.legalForm}}</span
                              ><span
                                class="property"
                                data-property-display-label="Name"
                                data-property-data-type="String"
                                data-property-name="salesSite.legalCompany.name"
                                data-property-data-format=""
                                data-property-parent-context="SalesInvoice"
                                >{{salesSite.legalCompany.name}}</span
                              ></strong
                            ><span
                              class="property"
                              data-property-display-label="RCS"
                              data-property-data-type="String"
                              data-property-name="salesSite.legalCompany.rcs"
                              data-property-data-format=""
                              data-property-parent-context="SalesInvoice"
                              >{{salesSite.legalCompany.rcs}}</span
                            >{{ translatedContent "7137a85d9b8d9e6d09f47767d2ca31ee" }}<span
                              class="property"
                              data-property-display-label="NAF"
                              data-property-data-type="String"
                              data-property-name="salesSite.legalCompany.naf"
                              data-property-data-format=""
                              data-property-parent-context="SalesInvoice"
                              >{{salesSite.legalCompany.naf}}</span
                            ></span
                          >
                        </div>
                        <!--{{/if}}-->
                        <div class="conditional-block-footer">&nbsp;</div>
                      </section>
                      <section
                        class="conditional-block"
                        data-context-condition='[{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"1","value1":{"label":"NAF","data":{"name":"naf","title":"NAF","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"description","targetNode":"","isCollection":false,"kind":"SCALAR","label":"NAF","node":"String","iconType":"csv"},"id":"salesSite.legalCompany.naf","key":"salesSite.legalCompany.naf","labelKey":"NAF","labelPath":"Sales site > Legal company > NAF"},"value2":null,"key":"1","operator":"empty"}]'
                      >
                        <!--{{#if ( or ( eq salesSite.legalCompany.naf null ) ( eq salesSite.legalCompany.naf "" ) ( eq salesSite.legalCompany.naf undefined ) )}}-->
                        <div class="conditional-block-body">
                          <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                            ><strong
                              ><span
                                class="property"
                                data-property-display-label="Legal form"
                                data-property-data-type="Enum"
                                data-property-name="salesSite.legalCompany.legalForm"
                                data-property-data-format=""
                                data-property-parent-context="SalesInvoice"
                                >{{salesSite.legalCompany.legalForm}}</span
                              ><span
                                class="property"
                                data-property-display-label="Name"
                                data-property-data-type="String"
                                data-property-name="salesSite.legalCompany.name"
                                data-property-data-format=""
                                data-property-parent-context="SalesInvoice"
                                >{{salesSite.legalCompany.name}}</span
                              ></strong
                            ><span
                              class="property"
                              data-property-display-label="RCS"
                              data-property-data-type="String"
                              data-property-name="salesSite.legalCompany.rcs"
                              data-property-data-format=""
                              data-property-parent-context="SalesInvoice"
                              >{{salesSite.legalCompany.rcs}}</span
                            ></span
                          >
                        </div>
                        <!--{{/if}}-->
                        <div class="conditional-block-footer">&nbsp;</div>
                      </section>
                      <section
                        class="record-context"
                        data-context-object-type="CompanyAddress"
                        data-context-object-path="salesSite.legalCompany.addresses.query.edges.0.node"
                        data-context-filter="[]"
                        data-context-list-order="{}"
                        data-alias="FxHRewKl"
                      >
                        <!--{{#with FxHRewKl.legalCompany.addresses.query.edges.0.node}}-->
                        <div class="report-context-body">
                          <p>
                            <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                              ><span
                                class="property"
                                data-property-display-label="Address line 1"
                                data-property-data-type="String"
                                data-property-name="addressLine1"
                                data-property-data-format=""
                                data-property-parent-context="CompanyAddress"
                                >{{addressLine1}}</span
                              ><span
                                class="property"
                                data-property-display-label="Address line 2"
                                data-property-data-type="String"
                                data-property-name="addressLine2"
                                data-property-data-format=""
                                data-property-parent-context="CompanyAddress"
                                >{{addressLine2}}</span
                              ><span
                                class="property"
                                data-property-display-label="Postal code"
                                data-property-data-type="String"
                                data-property-name="postcode"
                                data-property-data-format=""
                                data-property-parent-context="CompanyAddress"
                                >{{postcode}}</span
                              ><span
                                class="property"
                                data-property-display-label="City"
                                data-property-data-type="String"
                                data-property-name="city"
                                data-property-data-format=""
                                data-property-parent-context="CompanyAddress"
                                >{{city}}</span
                              ><span
                                class="property"
                                data-property-display-label="Region"
                                data-property-data-type="String"
                                data-property-name="region"
                                data-property-data-format=""
                                data-property-parent-context="CompanyAddress"
                                >{{region}}</span
                              ><span
                                class="property"
                                data-property-display-label="Name"
                                data-property-data-type="String"
                                data-property-name="country.name"
                                data-property-data-format=""
                                data-property-parent-context="CompanyAddress"
                                >{{country.name}}</span
                              ></span
                            >
                          </p>
                        </div>
                        <!--{{/with}}--><span class="report-context-footer">&nbsp;</span>
                      </section>
                    </td>
                  </tr>
                </tbody>
              </table>
            </figure>
          </div>
        </section>
        <section
          class="conditional-block"
          data-context-condition='[{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"1","value1":{"label":"External note","data":{"name":"isExternalNote","title":"External note","canSort":true,"canFilter":true,"type":"Boolean","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"","targetNode":"","isCollection":false,"kind":"SCALAR","label":"External note","node":"Boolean","iconType":"csv"},"id":"isExternalNote","key":"isExternalNote","labelKey":"External note","labelPath":"External note"},"value2":"true","key":"1","operator":"equals"}]'
        >
          <!--{{#if ( eq isExternalNote true )}}-->
          <div class="conditional-block-body">
            <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
              ><strong>{{ translatedContent "fc9d3df613063ee57906c461320744e2" }}</strong> &nbsp;<span
                class="property"
                data-property-display-label="External note"
                data-property-data-type="_OutputTextStream"
                data-property-name="externalNote.value"
                data-property-data-format=""
                data-property-parent-context="SalesInvoice"
                >{{{externalNote.value}}}</span
              ></span
            >
          </div>
          <!--{{/if}}-->
          <div class="conditional-block-footer">&nbsp;</div>
        </section>
      </div>
    </section>
  </div>
  <!--{{/with}}--><span class="report-context-footer">&nbsp;</span>
</section>
