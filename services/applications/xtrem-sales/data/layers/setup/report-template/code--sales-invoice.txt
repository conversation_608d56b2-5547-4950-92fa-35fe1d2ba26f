{
    const isInvoiceDraft = queryResponse.xtremSales.salesInvoice.query.edges.filter(edge => edge.node.status === 'draft');
    const penaltyAmount = queryResponse.xtremSales.salesInvoice.query.edges[0].node.penaltyPaymentAmount;
    const penaltyType = queryResponse.xtremSales.salesInvoice.query.edges[0].node.penaltyPaymentType;
    const discountAmount = queryResponse.xtremSales.salesInvoice.query.edges[0].node.discountPaymentAmount;
    const discountType = queryResponse.xtremSales.salesInvoice.query.edges[0].node.discountPaymentType;
    const isDiscountPercentage = discountAmount && discountAmount > 0 && discountType && discountType === 'percentage';
    const isDiscountAmount = discountAmount && discountAmount > 0 && discountType && discountType === 'amount';
    const isPenaltyPercentage = penaltyAmount && penaltyAmount > 0 && penaltyType && penaltyType === 'percentage';
    const isPenaltyAmount = penaltyAmount && penaltyAmount > 0 && penaltyType && penaltyType === 'amount';

    return {
        isInvoiceDraft,
        isDiscountAmount,
        isDiscountPercentage,
        isPenaltyAmount,
        isPenaltyPercentage,
    };
}
