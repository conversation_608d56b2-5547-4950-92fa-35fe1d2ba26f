<section class="unbreakable-block">
  <div class="unbreakable-block-body">
    <section
      class="record-context"
      data-context-object-type="SalesOrder"
      data-context-object-path="xtremSales.salesOrder.query.edges.0.node"
      data-context-filter='[{"_id":"1","label":"_id","filterType":"matches","filterValue":"order","data":{"name":"_id","title":"ID","canSort":true,"canFilter":true,"type":"IntOrString","isCustom":false,"isMutable":false,"isOnInputType":false,"isOnOutputType":true,"enumType":null,"dataType":"","targetNode":"","isCollection":false,"kind":"SCALAR","label":"_id","iconType":"csv"},"id":"_id","labelPath":"_id","property":{"name":"_id","title":"ID","canSort":true,"canFilter":true,"type":"IntOrString","isCustom":false,"isMutable":false,"isOnInputType":false,"isOnOutputType":true,"enumType":null,"dataType":"","targetNode":"","isCollection":false,"kind":"SCALAR","label":"_id","iconType":"csv","id":"_id","data":{"name":"_id","title":"ID","canSort":true,"canFilter":true,"type":"IntOrString","isCustom":false,"isMutable":false,"isOnInputType":false,"isOnOutputType":true,"enumType":null,"dataType":"","targetNode":"","isCollection":false,"kind":"SCALAR","label":"_id","iconType":"csv"},"key":"_id","labelKey":"_id","labelPath":"_id"},"parameter":true}]'
      data-context-list-order="{}"
      data-alias="ERtqegrm"
    >
      <!--{{#with ERtqegrm.salesOrder.query.edges.0.node}}-->
      <div class="report-context-body">
        <figure class="table" style="width: 100%">
          <table class="ck-table-resized">
            <colgroup>
              <col style="width: 32.94%" />
              <col style="width: 67.06%" />
            </colgroup>
            <tbody>
              <tr>
                <td>
                  <p>
                    <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                      ><strong
                        ><span
                          class="property"
                          data-property-display-label="Name"
                          data-property-data-type="String"
                          data-property-name="site.name"
                          data-property-data-format=""
                          data-property-parent-context="SalesOrder"
                          >{{site.name}}</span
                        ></strong
                      ></span
                    >
                  </p>
                  <section
                    class="conditional-block"
                    data-context-condition='[{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"1","value1":{"label":"Address line 1","data":{"name":"addressLine1","title":"Address line 1","canSort":false,"canFilter":false,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"","targetNode":"","isCollection":false,"kind":"SCALAR","label":"Address line 1","node":"String","iconType":"csv"},"id":"site.primaryAddress.addressLine1","key":"site.primaryAddress.addressLine1","labelKey":"Address line 1","labelPath":"Sales site > Primary address > Address line 1"},"value2":null,"key":"1","operator":"notEmpty"}]'
                  >
                    <!--{{#if ( not ( or ( eq site.primaryAddress.addressLine1 null ) ( eq site.primaryAddress.addressLine1 "" ) ( eq site.primaryAddress.addressLine1 undefined ) ) )}}-->
                    <div class="conditional-block-body">
                      <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                        ><span
                          class="property"
                          data-property-display-label="Address line 1"
                          data-property-data-type="String"
                          data-property-name="site.primaryAddress.addressLine1"
                          data-property-data-format=""
                          data-property-parent-context="SalesOrder"
                          >{{site.primaryAddress.addressLine1}}</span
                        ></span
                      >
                    </div>
                    <!--{{/if}}-->
                    <div class="conditional-block-footer">&nbsp;</div>
                  </section>
                  <section
                    class="conditional-block"
                    data-context-condition='[{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"1","value1":{"label":"Address line 2","data":{"name":"addressLine2","title":"Address line 2","canSort":false,"canFilter":false,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"","targetNode":"","isCollection":false,"kind":"SCALAR","label":"Address line 2","node":"String","iconType":"csv"},"id":"site.primaryAddress.addressLine2","key":"site.primaryAddress.addressLine2","labelKey":"Address line 2","labelPath":"Sales site > Primary address > Address line 2"},"value2":null,"key":"1","operator":"notEmpty"}]'
                  >
                    <!--{{#if ( not ( or ( eq site.primaryAddress.addressLine2 null ) ( eq site.primaryAddress.addressLine2 "" ) ( eq site.primaryAddress.addressLine2 undefined ) ) )}}-->
                    <div class="conditional-block-body">
                      <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                        ><span
                          class="property"
                          data-property-display-label="Address line 2"
                          data-property-data-type="String"
                          data-property-name="site.primaryAddress.addressLine2"
                          data-property-data-format=""
                          data-property-parent-context="SalesOrder"
                          >{{site.primaryAddress.addressLine2}}</span
                        ></span
                      >
                    </div>
                    <!--{{/if}}-->
                    <div class="conditional-block-footer">&nbsp;</div>
                  </section>
                  <section
                    class="conditional-block"
                    data-context-condition='[{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"1","value1":{"label":"ISO 3166-1 alpha 3","data":{"name":"iso31661Alpha3","title":"ISO 3166-1 alpha 3","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"","targetNode":"","isCollection":false,"kind":"SCALAR","label":"ISO 3166-1 alpha 3","node":"String","iconType":"csv"},"id":"site.primaryAddress.country.iso31661Alpha3","key":"site.primaryAddress.country.iso31661Alpha3","labelKey":"ISO 3166-1 alpha 3","labelPath":"Sales site > Primary address > Country > ISO 3166-1 alpha 3"},"value2":"GBR","key":"1","operator":"equals"},{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"2","value1":{"label":"Region","data":{"name":"region","title":"Region","canSort":false,"canFilter":false,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"","targetNode":"","isCollection":false,"kind":"SCALAR","label":"Region","node":"String","iconType":"csv"},"id":"site.primaryAddress.region","key":"site.primaryAddress.region","labelKey":"Region","labelPath":"Sales site > Primary address > Region"},"value2":null,"key":"2","operator":"notEmpty"}]'
                  >
                    <!--{{#if ( and ( eq site.primaryAddress.country.iso31661Alpha3 "GBR" ) ( not ( or ( eq site.primaryAddress.region null ) ( eq site.primaryAddress.region "" ) ( eq site.primaryAddress.region undefined ) ) ) )}}-->
                    <div class="conditional-block-body">
                      <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                        ><span
                          class="property"
                          data-property-display-label="City"
                          data-property-data-type="String"
                          data-property-name="site.primaryAddress.city"
                          data-property-data-format=""
                          data-property-parent-context="SalesOrder"
                          >{{site.primaryAddress.city}}</span
                        ><span
                          class="property"
                          data-property-display-label="Region"
                          data-property-data-type="String"
                          data-property-name="site.primaryAddress.region"
                          data-property-data-format=""
                          data-property-parent-context="SalesOrder"
                          >{{site.primaryAddress.region}}</span
                        ><span
                          class="property"
                          data-property-display-label="Postal code"
                          data-property-data-type="String"
                          data-property-name="site.primaryAddress.postcode"
                          data-property-data-format=""
                          data-property-parent-context="SalesOrder"
                          >{{site.primaryAddress.postcode}}</span
                        ></span
                      >
                    </div>
                    <!--{{/if}}-->
                    <div class="conditional-block-footer">&nbsp;</div>
                  </section>
                  <section
                    class="conditional-block"
                    data-context-condition='[{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"1","value1":{"label":"ISO 3166-1 alpha 3","data":{"name":"iso31661Alpha3","title":"ISO 3166-1 alpha 3","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"","targetNode":"","isCollection":false,"kind":"SCALAR","label":"ISO 3166-1 alpha 3","node":"String","iconType":"csv"},"id":"site.primaryAddress.country.iso31661Alpha3","key":"site.primaryAddress.country.iso31661Alpha3","labelKey":"ISO 3166-1 alpha 3","labelPath":"Sales site > Primary address > Country > ISO 3166-1 alpha 3"},"value2":"ZAF","key":"1","operator":"equals"},{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"2","value1":{"label":"Region","data":{"name":"region","title":"Region","canSort":false,"canFilter":false,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"","targetNode":"","isCollection":false,"kind":"SCALAR","label":"Region","node":"String","iconType":"csv"},"id":"site.primaryAddress.region","key":"site.primaryAddress.region","labelKey":"Region","labelPath":"Sales site > Primary address > Region"},"value2":null,"key":"2","operator":"notEmpty"}]'
                  >
                    <!--{{#if ( and ( eq site.primaryAddress.country.iso31661Alpha3 "ZAF" ) ( not ( or ( eq site.primaryAddress.region null ) ( eq site.primaryAddress.region "" ) ( eq site.primaryAddress.region undefined ) ) ) )}}-->
                    <div class="conditional-block-body">
                      <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                        ><span
                          class="property"
                          data-property-display-label="City"
                          data-property-data-type="String"
                          data-property-name="site.primaryAddress.city"
                          data-property-data-format=""
                          data-property-parent-context="SalesOrder"
                          >{{site.primaryAddress.city}}</span
                        ><span
                          class="property"
                          data-property-display-label="Region"
                          data-property-data-type="String"
                          data-property-name="site.primaryAddress.region"
                          data-property-data-format=""
                          data-property-parent-context="SalesOrder"
                          >{{site.primaryAddress.region}}</span
                        ><span
                          class="property"
                          data-property-display-label="Postal code"
                          data-property-data-type="String"
                          data-property-name="site.primaryAddress.postcode"
                          data-property-data-format=""
                          data-property-parent-context="SalesOrder"
                          >{{site.primaryAddress.postcode}}</span
                        ></span
                      >
                    </div>
                    <!--{{/if}}-->
                    <div class="conditional-block-footer">&nbsp;</div>
                  </section>
                  <section
                    class="conditional-block"
                    data-context-condition='[{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"1","value1":{"label":"ISO 3166-1 alpha 3","data":{"name":"iso31661Alpha3","title":"ISO 3166-1 alpha 3","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"","targetNode":"","isCollection":false,"kind":"SCALAR","label":"ISO 3166-1 alpha 3","node":"String","iconType":"csv"},"id":"site.primaryAddress.country.iso31661Alpha3","key":"site.primaryAddress.country.iso31661Alpha3","labelKey":"ISO 3166-1 alpha 3","labelPath":"Sales site > Primary address > Country > ISO 3166-1 alpha 3"},"value2":"USA","key":"1","operator":"equals"},{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"2","value1":{"label":"Region","data":{"name":"region","title":"Region","canSort":false,"canFilter":false,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"","targetNode":"","isCollection":false,"kind":"SCALAR","label":"Region","node":"String","iconType":"csv"},"id":"site.primaryAddress.region","key":"site.primaryAddress.region","labelKey":"Region","labelPath":"Sales site > Primary address > Region"},"value2":null,"key":"2","operator":"notEmpty"}]'
                  >
                    <!--{{#if ( and ( eq site.primaryAddress.country.iso31661Alpha3 "USA" ) ( not ( or ( eq site.primaryAddress.region null ) ( eq site.primaryAddress.region "" ) ( eq site.primaryAddress.region undefined ) ) ) )}}-->
                    <div class="conditional-block-body">
                      <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                        ><span
                          class="property"
                          data-property-display-label="City"
                          data-property-data-type="String"
                          data-property-name="site.primaryAddress.city"
                          data-property-data-format=""
                          data-property-parent-context="SalesOrder"
                          >{{site.primaryAddress.city}}</span
                        ><span
                          class="property"
                          data-property-display-label="Region"
                          data-property-data-type="String"
                          data-property-name="site.primaryAddress.region"
                          data-property-data-format=""
                          data-property-parent-context="SalesOrder"
                          >{{site.primaryAddress.region}}</span
                        ><span
                          class="property"
                          data-property-display-label="Postal code"
                          data-property-data-type="String"
                          data-property-name="site.primaryAddress.postcode"
                          data-property-data-format=""
                          data-property-parent-context="SalesOrder"
                          >{{site.primaryAddress.postcode}}</span
                        ></span
                      >
                    </div>
                    <!--{{/if}}-->
                    <div class="conditional-block-footer">&nbsp;</div>
                  </section>
                  <section
                    class="conditional-block"
                    data-context-condition='[{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"1","value1":{"label":"ISO 3166-1 alpha 3","data":{"name":"iso31661Alpha3","title":"ISO 3166-1 alpha 3","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"","targetNode":"","isCollection":false,"kind":"SCALAR","label":"ISO 3166-1 alpha 3","node":"String","iconType":"csv"},"id":"site.primaryAddress.country.iso31661Alpha3","key":"site.primaryAddress.country.iso31661Alpha3","labelKey":"ISO 3166-1 alpha 3","labelPath":"Sales site > Primary address > Country > ISO 3166-1 alpha 3"},"value2":"GBR","key":"1","operator":"notEqual"},{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"2","value1":{"label":"ISO 3166-1 alpha 3","data":{"name":"iso31661Alpha3","title":"ISO 3166-1 alpha 3","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"","targetNode":"","isCollection":false,"kind":"SCALAR","label":"ISO 3166-1 alpha 3","node":"String","iconType":"csv"},"id":"site.primaryAddress.country.iso31661Alpha3","key":"site.primaryAddress.country.iso31661Alpha3","labelKey":"ISO 3166-1 alpha 3","labelPath":"Sales site > Primary address > Country > ISO 3166-1 alpha 3"},"value2":"ZAF","key":"2","operator":"notEqual"},{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"3","value1":{"label":"ISO 3166-1 alpha 3","data":{"name":"iso31661Alpha3","title":"ISO 3166-1 alpha 3","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"","targetNode":"","isCollection":false,"kind":"SCALAR","label":"ISO 3166-1 alpha 3","node":"String","iconType":"csv"},"id":"site.primaryAddress.country.iso31661Alpha3","key":"site.primaryAddress.country.iso31661Alpha3","labelKey":"ISO 3166-1 alpha 3","labelPath":"Sales site > Primary address > Country > ISO 3166-1 alpha 3"},"value2":"USA","key":"3","operator":"notEqual"}]'
                  >
                    <!--{{#if ( and ( not ( eq site.primaryAddress.country.iso31661Alpha3 "GBR" ) ) ( not ( eq site.primaryAddress.country.iso31661Alpha3 "ZAF" ) ) ( not ( eq site.primaryAddress.country.iso31661Alpha3 "USA" ) ) )}}-->
                    <div class="conditional-block-body">
                      <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                        ><span
                          class="property"
                          data-property-display-label="Postal code"
                          data-property-data-type="String"
                          data-property-name="site.primaryAddress.postcode"
                          data-property-data-format=""
                          data-property-parent-context="SalesOrder"
                          >{{site.primaryAddress.postcode}}</span
                        ><span
                          class="property"
                          data-property-display-label="City"
                          data-property-data-type="String"
                          data-property-name="site.primaryAddress.city"
                          data-property-data-format=""
                          data-property-parent-context="SalesOrder"
                          >{{site.primaryAddress.city}}</span
                        ></span
                      >
                    </div>
                    <!--{{/if}}-->
                    <div class="conditional-block-footer">&nbsp;</div>
                  </section>
                  <section
                    class="conditional-block"
                    data-context-condition='[{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"1","value1":{"label":"Name","data":{"name":"name","title":"Name","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"localizedCountryPropertyDataType","targetNode":"","isCollection":false,"kind":"SCALAR","label":"Name","node":"String","iconType":"csv"},"id":"site.primaryAddress.country.name","key":"site.primaryAddress.country.name","labelKey":"Name","labelPath":"Sales site > Primary address > Country > Name"},"value2":null,"key":"1","operator":"notEmpty"}]'
                  >
                    <!--{{#if ( not ( or ( eq site.primaryAddress.country.name null ) ( eq site.primaryAddress.country.name "" ) ( eq site.primaryAddress.country.name undefined ) ) )}}-->
                    <div class="conditional-block-body">
                      <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                        ><span
                          class="property"
                          data-property-display-label="Name"
                          data-property-data-type="String"
                          data-property-name="site.primaryAddress.country.name"
                          data-property-data-format=""
                          data-property-parent-context="SalesOrder"
                          >{{site.primaryAddress.country.name}}</span
                        ></span
                      >
                    </div>
                    <!--{{/if}}-->
                    <div class="conditional-block-footer">&nbsp;</div>
                  </section>
                  <section
                    class="conditional-block"
                    data-context-condition='[{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"1","value1":{"label":"SIRET","data":{"name":"siret","title":"SIRET","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":false,"isOnOutputType":true,"enumType":null,"dataType":"description","targetNode":"","isCollection":false,"kind":"SCALAR","label":"SIRET","node":"String","iconType":"csv"},"id":"site.siret","key":"site.siret","labelKey":"SIRET","labelPath":"Sales site > SIRET"},"value2":null,"key":"1","operator":"notEmpty"}]'
                  >
                    <!--{{#if ( not ( or ( eq site.siret null ) ( eq site.siret "" ) ( eq site.siret undefined ) ) )}}-->
                    <div class="conditional-block-body">
                      <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                        ><strong>&nbsp;{{ translatedContent "d04e2c1b67f3ef0d475409516b812e8b" }}</strong>&nbsp;<span
                          class="property"
                          data-property-display-label="SIRET"
                          data-property-data-type="String"
                          data-property-name="site.siret"
                          data-property-data-format=""
                          data-property-parent-context="SalesOrder"
                          >{{site.siret}}</span
                        ></span
                      >
                    </div>
                    <!--{{/if}}-->
                    <div class="conditional-block-footer">&nbsp;</div>
                  </section>
                  <section
                    class="conditional-block"
                    data-context-condition='[{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"1","value1":{"label":"Tax ID number","data":{"name":"taxIdNumber","title":"Tax ID number","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":false,"isOnOutputType":true,"enumType":null,"dataType":"taxIdentificationDataType","targetNode":"","isCollection":false,"kind":"SCALAR","label":"Tax ID number","node":"String","iconType":"csv"},"id":"site.taxIdNumber","key":"site.taxIdNumber","labelKey":"Tax ID number","labelPath":"Sales site > Tax ID number"},"value2":null,"key":"1","operator":"notEmpty"}]'
                  >
                    <!--{{#if ( not ( or ( eq site.taxIdNumber null ) ( eq site.taxIdNumber "" ) ( eq site.taxIdNumber undefined ) ) )}}-->
                    <div class="conditional-block-body">
                      <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                        ><strong>&nbsp;{{ translatedContent "37a7a58f6fdc66ce34c2bf56389a2aac" }}</strong>&nbsp;<span
                          class="property"
                          data-property-display-label="Tax ID number"
                          data-property-data-type="String"
                          data-property-name="site.taxIdNumber"
                          data-property-data-format=""
                          data-property-parent-context="SalesOrder"
                          >{{site.taxIdNumber}}</span
                        ></span
                      >
                    </div>
                    <!--{{/if}}-->
                    <div class="conditional-block-footer">&nbsp;</div>
                  </section>
                </td>
                <td style="background-color: #dfdfdf; text-align: right">
                  <h2>
                    <span style="color: #198e59; font-family: 'Sage UI', Geneva, sans-serif; font-size: 16pt"
                      ><strong>{{ translatedContent "9f4d4ca59896eb8516937249b13b5e60" }}</strong></span
                    >
                  </h2>
                  <p>
                    <span style="color: #198e59; font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                      ><strong>{{ translatedContent "2a39f204d4eac05604c2059e5ac2f9f7" }}</strong></span
                    ><span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                      >&nbsp;<span
                        class="property"
                        data-property-display-label="Number"
                        data-property-data-type="String"
                        data-property-name="number"
                        data-property-data-format=""
                        data-property-parent-context="SalesOrder"
                        >{{number}}</span
                      ></span
                    >
                  </p>
                  <p>
                    <span style="color: #198e59; font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                      ><strong>{{ translatedContent "f4d5b114119bfdbdc5f3d9472a46fc7b" }}</strong></span
                    ><span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                      >&nbsp;<span
                        class="property"
                        data-property-display-label="Order date"
                        data-property-data-type="Date"
                        data-property-name="date"
                        data-property-data-format="FullDate"
                        data-property-parent-context="SalesOrder"
                        >{{formatDate date 'FullDate'}}</span
                      ></span
                    >
                  </p>
                </td>
              </tr>
              <tr>
                <td style="border-color: #ffffff">
                  <section
                    class="conditional-block"
                    data-context-condition='[{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"1","value1":{"label":"Customer number","data":{"name":"customerNumber","title":"Customer number","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"propertyDataType","targetNode":"","isCollection":false,"kind":"SCALAR","label":"Customer number","node":"String","iconType":"csv"},"id":"customerNumber","key":"customerNumber","labelKey":"Customer number","labelPath":"Customer number"},"value2":null,"key":"1","operator":"notEmpty"}]'
                  >
                    <!--{{#if ( not ( or ( eq customerNumber null ) ( eq customerNumber "" ) ( eq customerNumber undefined ) ) )}}-->
                    <div class="conditional-block-body">
                      <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                        ><strong>{{ translatedContent "0d866af0435bceb34ff8a9f9e3494368" }}</strong>&nbsp;<span
                          class="property"
                          data-property-display-label="Customer number"
                          data-property-data-type="String"
                          data-property-name="customerNumber"
                          data-property-data-format=""
                          data-property-parent-context="SalesOrder"
                          >{{customerNumber}}</span
                        ></span
                      >
                    </div>
                    <!--{{/if}}-->
                    <div class="conditional-block-footer">&nbsp;</div>
                  </section>
                  <section
                    class="conditional-block"
                    data-context-condition='[{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"1","value1":{"label":"Name","data":{"name":"name","title":"Name","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":false,"isOnOutputType":true,"enumType":null,"dataType":"","targetNode":"","isCollection":false,"kind":"SCALAR","label":"Name","node":"String","iconType":"csv"},"id":"billToCustomer.name","key":"billToCustomer.name","labelKey":"Name","labelPath":"Bill-to customer > Name"},"value2":null,"key":"1","operator":"notEmpty"}]'
                  >
                    <!--{{#if ( not ( or ( eq billToCustomer.name null ) ( eq billToCustomer.name "" ) ( eq billToCustomer.name undefined ) ) )}}-->
                    <div class="conditional-block-body">
                      <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                        ><strong>{{ translatedContent "5eea367ea73b909880393bd1ae79fc67" }}</strong>&nbsp;</span
                      ><span style="color: #198e59; font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                        ><strong
                          ><span
                            class="property"
                            data-property-display-label="Name"
                            data-property-data-type="String"
                            data-property-name="billToCustomer.name"
                            data-property-data-format=""
                            data-property-parent-context="SalesOrder"
                            >{{billToCustomer.name}}</span
                          ></strong
                        ></span
                      >
                    </div>
                    <!--{{/if}}-->
                    <div class="conditional-block-footer">&nbsp;</div>
                  </section>
                  <section
                    class="conditional-block"
                    data-context-condition='[{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"1","value1":{"label":"ID","data":{"name":"id","title":"ID","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":false,"isOnOutputType":true,"enumType":null,"dataType":"","targetNode":"","isCollection":false,"kind":"SCALAR","label":"ID","node":"String","iconType":"csv"},"id":"billToCustomer.id","key":"billToCustomer.id","labelKey":"ID","labelPath":"Bill-to customer > ID"},"value2":null,"key":"1","operator":"notEmpty"}]'
                  >
                    <!--{{#if ( not ( or ( eq billToCustomer.id null ) ( eq billToCustomer.id "" ) ( eq billToCustomer.id undefined ) ) )}}-->
                    <div class="conditional-block-body">
                      <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                        ><strong>{{ translatedContent "d13d8380c3f4de07fef91a42fe6c60d7" }}</strong>&nbsp;<span
                          class="property"
                          data-property-display-label="ID"
                          data-property-data-type="String"
                          data-property-name="billToCustomer.id"
                          data-property-data-format=""
                          data-property-parent-context="SalesOrder"
                          >{{billToCustomer.id}}</span
                        ></span
                      >
                    </div>
                    <!--{{/if}}-->
                    <div class="conditional-block-footer">&nbsp;</div>
                  </section>
                  <section
                    class="conditional-block"
                    data-context-condition='[{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"1","value1":{"label":"SIRET","data":{"name":"siret","title":"SIRET","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":false,"isOnOutputType":true,"enumType":null,"dataType":"description","targetNode":"","isCollection":false,"kind":"SCALAR","label":"SIRET","node":"String","iconType":"csv"},"id":"billToCustomer.siret","key":"billToCustomer.siret","labelKey":"SIRET","labelPath":"Bill-to customer > SIRET"},"value2":null,"key":"1","operator":"notEmpty"}]'
                  >
                    <!--{{#if ( not ( or ( eq billToCustomer.siret null ) ( eq billToCustomer.siret "" ) ( eq billToCustomer.siret undefined ) ) )}}-->
                    <div class="conditional-block-body">
                      <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                        ><strong>{{ translatedContent "d04e2c1b67f3ef0d475409516b812e8b" }}</strong>&nbsp;<span
                          class="property"
                          data-property-display-label="SIRET"
                          data-property-data-type="String"
                          data-property-name="billToCustomer.siret"
                          data-property-data-format=""
                          data-property-parent-context="SalesOrder"
                          >{{billToCustomer.siret}}</span
                        ></span
                      >
                    </div>
                    <!--{{/if}}-->
                    <div class="conditional-block-footer">&nbsp;</div>
                  </section>
                  <section
                    class="conditional-block"
                    data-context-condition='[{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"1","value1":{"label":"Tax ID number","data":{"name":"taxIdNumber","title":"Tax ID number","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":false,"isOnOutputType":true,"enumType":null,"dataType":"taxIdentificationDataType","targetNode":"","isCollection":false,"kind":"SCALAR","label":"Tax ID number","node":"String","iconType":"csv"},"id":"billToCustomer.taxIdNumber","key":"billToCustomer.taxIdNumber","labelKey":"Tax ID number","labelPath":"Bill-to customer > Tax ID number"},"value2":null,"key":"1","operator":"notEmpty"}]'
                  >
                    <!--{{#if ( not ( or ( eq billToCustomer.taxIdNumber null ) ( eq billToCustomer.taxIdNumber "" ) ( eq billToCustomer.taxIdNumber undefined ) ) )}}-->
                    <div class="conditional-block-body">
                      <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                        ><strong>{{ translatedContent "37a7a58f6fdc66ce34c2bf56389a2aac" }}</strong>&nbsp;<span
                          class="property"
                          data-property-display-label="Tax ID number"
                          data-property-data-type="String"
                          data-property-name="billToCustomer.taxIdNumber"
                          data-property-data-format=""
                          data-property-parent-context="SalesOrder"
                          >{{billToCustomer.taxIdNumber}}</span
                        ></span
                      >
                    </div>
                    <!--{{/if}}-->
                    <div class="conditional-block-footer">&nbsp;</div>
                  </section>
                </td>
                <td style="text-align: right">
                  <section
                    class="conditional-block"
                    data-context-condition='[{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"1","value1":{"label":"Shipping date","data":{"name":"shippingDate","title":"Shipping date","canSort":true,"canFilter":true,"type":"Date","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"","targetNode":"","isCollection":false,"kind":"SCALAR","label":"Shipping date","node":"Date","iconType":"csv"},"id":"shippingDate","key":"shippingDate","labelKey":"Shipping date","labelPath":"Shipping date"},"value2":null,"key":"1","operator":"notEmpty"}]'
                  >
                    <!--{{#if ( not ( or ( eq shippingDate null ) ( eq shippingDate "" ) ( eq shippingDate undefined ) ) )}}-->
                    <div class="conditional-block-body">
                      <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                        ><strong>{{ translatedContent "a3a79b28bc45b7fd393effd056b917e1" }}</strong>&nbsp;<span
                          class="property"
                          data-property-display-label="Shipping date"
                          data-property-data-type="Date"
                          data-property-name="shippingDate"
                          data-property-data-format="FullDate"
                          data-property-parent-context="SalesOrder"
                          >{{formatDate shippingDate 'FullDate'}}</span
                        ></span
                      >
                    </div>
                    <!--{{/if}}-->
                    <div class="conditional-block-footer">&nbsp;</div>
                  </section>
                  <section
                    class="conditional-block"
                    data-context-condition='[{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"1","value1":{"label":"Name","data":{"name":"name","title":"Name","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"localizedName","targetNode":"","isCollection":false,"kind":"SCALAR","label":"Name","node":"String","iconType":"csv"},"id":"deliveryMode.name","key":"deliveryMode.name","labelKey":"Name","labelPath":"Delivery mode > Name"},"value2":null,"key":"1","operator":"notEmpty"}]'
                  >
                    <!--{{#if ( not ( or ( eq deliveryMode.name null ) ( eq deliveryMode.name "" ) ( eq deliveryMode.name undefined ) ) )}}-->
                    <div class="conditional-block-body">
                      <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                        ><strong>{{ translatedContent "0f55fedcee89c8d9407351716835247e" }}</strong>&nbsp;<span
                          class="property"
                          data-property-display-label="Name"
                          data-property-data-type="String"
                          data-property-name="deliveryMode.name"
                          data-property-data-format=""
                          data-property-parent-context="SalesOrder"
                          >{{deliveryMode.name}}</span
                        ></span
                      >
                    </div>
                    <!--{{/if}}-->
                    <div class="conditional-block-footer">&nbsp;</div>
                  </section>
                  <section
                    class="conditional-block"
                    data-context-condition='[{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"1","value1":{"label":"Name","data":{"name":"name","title":"Name","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"localizedName","targetNode":"","isCollection":false,"kind":"SCALAR","label":"Name","node":"String","iconType":"csv"},"id":"incoterm.name","key":"incoterm.name","labelKey":"Name","labelPath":"Incoterms rule > Name"},"value2":null,"key":"1","operator":"notEmpty"}]'
                  >
                    <!--{{#if ( not ( or ( eq incoterm.name null ) ( eq incoterm.name "" ) ( eq incoterm.name undefined ) ) )}}-->
                    <div class="conditional-block-body">
                      <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                        ><strong>{{ translatedContent "c42d7a1f13e2fd7ab2c829f3662f736f" }}</strong>&nbsp;<span
                          class="property"
                          data-property-display-label="Name"
                          data-property-data-type="String"
                          data-property-name="site.name"
                          data-property-data-format=""
                          data-property-parent-context="SalesOrder"
                          >{{site.name}}</span
                        ></span
                      >
                    </div>
                    <!--{{/if}}-->
                    <div class="conditional-block-footer">&nbsp;</div>
                  </section>
                  <section
                    class="conditional-block"
                    data-context-condition='[{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"1","value1":{"label":"Name","data":{"name":"name","title":"Name","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"localizedName","targetNode":"","isCollection":false,"kind":"SCALAR","label":"Name","node":"String","iconType":"csv"},"id":"paymentTerm.name","key":"paymentTerm.name","labelKey":"Name","labelPath":"Payment term > Name"},"value2":null,"key":"1","operator":"notEmpty"}]'
                  >
                    <!--{{#if ( not ( or ( eq paymentTerm.name null ) ( eq paymentTerm.name "" ) ( eq paymentTerm.name undefined ) ) )}}-->
                    <div class="conditional-block-body">
                      <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                        ><strong>{{ translatedContent "6af6aa3c07e3f393b7c213a6a0edd35f" }}</strong>&nbsp;<span
                          class="property"
                          data-property-display-label="Name"
                          data-property-data-type="String"
                          data-property-name="billToCustomer.paymentTerm.name"
                          data-property-data-format=""
                          data-property-parent-context="SalesOrder"
                          >{{billToCustomer.paymentTerm.name}}</span
                        ></span
                      >
                    </div>
                    <!--{{/if}}-->
                    <div class="conditional-block-footer">&nbsp;</div>
                  </section>
                </td>
              </tr>
            </tbody>
          </table>
        </figure>
        <figure class="table">
          <table>
            <tbody>
              <tr>
                <td style="background-color: #dfdfdf; border-color: #dfdfdf">
                  <span style="color: #198e59; font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                    ><strong>{{ translatedContent "0129928de90b02ecbd5281a15d66ecdf" }}</strong></span
                  >
                </td>
                <td style="background-color: #dfdfdf">
                  <span style="color: #198e59; font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                    ><strong>{{ translatedContent "84e839915aa016435941dda50c367e0c" }}</strong></span
                  >
                </td>
              </tr>
              <tr>
                <td style="border-color: #ffffff">
                  <p>
                    <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                      ><strong
                        ><span
                          class="property"
                          data-property-display-label="Name"
                          data-property-data-type="String"
                          data-property-name="soldToCustomer.name"
                          data-property-data-format=""
                          data-property-parent-context="SalesOrder"
                          >{{soldToCustomer.name}}</span
                        ></strong
                      ></span
                    >
                  </p>
                  <section
                    class="conditional-block"
                    data-context-condition='[{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"1","value1":{"label":"Address line 1","data":{"name":"addressLine1","title":"Address line 1","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"addressLineDataType","targetNode":"","isCollection":false,"kind":"SCALAR","label":"Address line 1","node":"String","iconType":"csv"},"id":"soldToAddress.addressLine1","key":"soldToAddress.addressLine1","labelKey":"Address line 1","labelPath":"Sold-to address > Address line 1"},"value2":null,"key":"1","operator":"notEmpty"}]'
                  >
                    <!--{{#if ( not ( or ( eq soldToAddress.addressLine1 null ) ( eq soldToAddress.addressLine1 "" ) ( eq soldToAddress.addressLine1 undefined ) ) )}}-->
                    <div class="conditional-block-body">
                      <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                        ><span
                          class="property"
                          data-property-display-label="Address line 1"
                          data-property-data-type="String"
                          data-property-name="soldToAddress.addressLine1"
                          data-property-data-format=""
                          data-property-parent-context="SalesOrder"
                          >{{soldToAddress.addressLine1}}</span
                        ></span
                      >
                    </div>
                    <!--{{/if}}-->
                    <div class="conditional-block-footer">&nbsp;</div>
                  </section>
                  <section
                    class="conditional-block"
                    data-context-condition='[{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"1","value1":{"label":"Address line 2","data":{"name":"addressLine2","title":"Address line 2","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"addressLineDataType","targetNode":"","isCollection":false,"kind":"SCALAR","label":"Address line 2","node":"String","iconType":"csv"},"id":"soldToAddress.addressLine2","key":"soldToAddress.addressLine2","labelKey":"Address line 2","labelPath":"Sold-to address > Address line 2"},"value2":null,"key":"1","operator":"notEmpty"}]'
                  >
                    <!--{{#if ( not ( or ( eq soldToAddress.addressLine2 null ) ( eq soldToAddress.addressLine2 "" ) ( eq soldToAddress.addressLine2 undefined ) ) )}}-->
                    <div class="conditional-block-body">
                      <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                        ><span
                          class="property"
                          data-property-display-label="Address line 2"
                          data-property-data-type="String"
                          data-property-name="soldToAddress.addressLine2"
                          data-property-data-format=""
                          data-property-parent-context="SalesOrder"
                          >{{soldToAddress.addressLine2}}</span
                        ></span
                      >
                    </div>
                    <!--{{/if}}-->
                    <div class="conditional-block-footer">&nbsp;</div>
                  </section>
                  <section
                    class="conditional-block"
                    data-context-condition='[{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"1","value1":{"label":"ISO 3166-1 alpha 3","data":{"name":"iso31661Alpha3","title":"ISO 3166-1 alpha 3","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"","targetNode":"","isCollection":false,"kind":"SCALAR","label":"ISO 3166-1 alpha 3","node":"String","iconType":"csv"},"id":"soldToAddress.country.iso31661Alpha3","key":"soldToAddress.country.iso31661Alpha3","labelKey":"ISO 3166-1 alpha 3","labelPath":"Sold-to address > Country > ISO 3166-1 alpha 3"},"value2":"GBR","key":"1","operator":"equals"}]'
                  >
                    <!--{{#if ( eq soldToAddress.country.iso31661Alpha3 "GBR" )}}-->
                    <div class="conditional-block-body">
                      <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                        ><span
                          class="property"
                          data-property-display-label="City"
                          data-property-data-type="String"
                          data-property-name="soldToAddress.city"
                          data-property-data-format=""
                          data-property-parent-context="SalesOrder"
                          >{{soldToAddress.city}}</span
                        ><span
                          class="property"
                          data-property-display-label="Region"
                          data-property-data-type="String"
                          data-property-name="soldToAddress.region"
                          data-property-data-format=""
                          data-property-parent-context="SalesOrder"
                          >{{soldToAddress.region}}</span
                        ><span
                          class="property"
                          data-property-display-label="Postal code"
                          data-property-data-type="String"
                          data-property-name="soldToAddress.postcode"
                          data-property-data-format=""
                          data-property-parent-context="SalesOrder"
                          >{{soldToAddress.postcode}}</span
                        ></span
                      >
                    </div>
                    <!--{{/if}}-->
                    <div class="conditional-block-footer">&nbsp;</div>
                  </section>
                  <section
                    class="conditional-block"
                    data-context-condition='[{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"1","value1":{"label":"ISO 3166-1 alpha 3","data":{"name":"iso31661Alpha3","title":"ISO 3166-1 alpha 3","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"","targetNode":"","isCollection":false,"kind":"SCALAR","label":"ISO 3166-1 alpha 3","node":"String","iconType":"csv"},"id":"soldToAddress.country.iso31661Alpha3","key":"soldToAddress.country.iso31661Alpha3","labelKey":"ISO 3166-1 alpha 3","labelPath":"Sold-to address > Country > ISO 3166-1 alpha 3"},"value2":"ZAF","key":"1","operator":"equals"}]'
                  >
                    <!--{{#if ( eq soldToAddress.country.iso31661Alpha3 "ZAF" )}}-->
                    <div class="conditional-block-body">
                      <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                        ><span
                          class="property"
                          data-property-display-label="City"
                          data-property-data-type="String"
                          data-property-name="soldToAddress.city"
                          data-property-data-format=""
                          data-property-parent-context="SalesOrder"
                          >{{soldToAddress.city}}</span
                        ><span
                          class="property"
                          data-property-display-label="Region"
                          data-property-data-type="String"
                          data-property-name="soldToAddress.region"
                          data-property-data-format=""
                          data-property-parent-context="SalesOrder"
                          >{{soldToAddress.region}}</span
                        ><span
                          class="property"
                          data-property-display-label="Postal code"
                          data-property-data-type="String"
                          data-property-name="soldToAddress.postcode"
                          data-property-data-format=""
                          data-property-parent-context="SalesOrder"
                          >{{soldToAddress.postcode}}</span
                        ></span
                      >
                    </div>
                    <!--{{/if}}-->
                    <div class="conditional-block-footer">&nbsp;</div>
                  </section>
                  <section
                    class="conditional-block"
                    data-context-condition='[{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"1","value1":{"label":"ISO 3166-1 alpha 3","data":{"name":"iso31661Alpha3","title":"ISO 3166-1 alpha 3","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"","targetNode":"","isCollection":false,"kind":"SCALAR","label":"ISO 3166-1 alpha 3","node":"String","iconType":"csv"},"id":"soldToAddress.country.iso31661Alpha3","key":"soldToAddress.country.iso31661Alpha3","labelKey":"ISO 3166-1 alpha 3","labelPath":"Sold-to address > Country > ISO 3166-1 alpha 3"},"value2":"USA","key":"1","operator":"equals"}]'
                  >
                    <!--{{#if ( eq soldToAddress.country.iso31661Alpha3 "USA" )}}-->
                    <div class="conditional-block-body">
                      <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                        ><span
                          class="property"
                          data-property-display-label="City"
                          data-property-data-type="String"
                          data-property-name="soldToAddress.city"
                          data-property-data-format=""
                          data-property-parent-context="SalesOrder"
                          >{{soldToAddress.city}}</span
                        ><span
                          class="property"
                          data-property-display-label="Region"
                          data-property-data-type="String"
                          data-property-name="soldToAddress.region"
                          data-property-data-format=""
                          data-property-parent-context="SalesOrder"
                          >{{soldToAddress.region}}</span
                        ><span
                          class="property"
                          data-property-display-label="Postal code"
                          data-property-data-type="String"
                          data-property-name="soldToAddress.postcode"
                          data-property-data-format=""
                          data-property-parent-context="SalesOrder"
                          >{{soldToAddress.postcode}}</span
                        ></span
                      >
                    </div>
                    <!--{{/if}}-->
                    <div class="conditional-block-footer">&nbsp;</div>
                  </section>
                  <section
                    class="conditional-block"
                    data-context-condition='[{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"1","value1":{"label":"ISO 3166-1 alpha 3","data":{"name":"iso31661Alpha3","title":"ISO 3166-1 alpha 3","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"","targetNode":"","isCollection":false,"kind":"SCALAR","label":"ISO 3166-1 alpha 3","node":"String","iconType":"csv"},"id":"soldToAddress.country.iso31661Alpha3","key":"soldToAddress.country.iso31661Alpha3","labelKey":"ISO 3166-1 alpha 3","labelPath":"Sold-to address > Country > ISO 3166-1 alpha 3"},"value2":"GBR","key":"1","operator":"notEqual"},{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"2","value1":{"label":"ISO 3166-1 alpha 3","data":{"name":"iso31661Alpha3","title":"ISO 3166-1 alpha 3","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"","targetNode":"","isCollection":false,"kind":"SCALAR","label":"ISO 3166-1 alpha 3","node":"String","iconType":"csv"},"id":"soldToAddress.country.iso31661Alpha3","key":"soldToAddress.country.iso31661Alpha3","labelKey":"ISO 3166-1 alpha 3","labelPath":"Sold-to address > Country > ISO 3166-1 alpha 3"},"value2":"ZAF","key":"2","operator":"notEqual"},{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"3","value1":{"label":"ISO 3166-1 alpha 3","data":{"name":"iso31661Alpha3","title":"ISO 3166-1 alpha 3","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"","targetNode":"","isCollection":false,"kind":"SCALAR","label":"ISO 3166-1 alpha 3","node":"String","iconType":"csv"},"id":"soldToAddress.country.iso31661Alpha3","key":"soldToAddress.country.iso31661Alpha3","labelKey":"ISO 3166-1 alpha 3","labelPath":"Sold-to address > Country > ISO 3166-1 alpha 3"},"value2":"USA","key":"3","operator":"notEqual"}]'
                  >
                    <!--{{#if ( and ( not ( eq soldToAddress.country.iso31661Alpha3 "GBR" ) ) ( not ( eq soldToAddress.country.iso31661Alpha3 "ZAF" ) ) ( not ( eq soldToAddress.country.iso31661Alpha3 "USA" ) ) )}}-->
                    <div class="conditional-block-body">
                      <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                        ><span
                          class="property"
                          data-property-display-label="Postal code"
                          data-property-data-type="String"
                          data-property-name="soldToAddress.postcode"
                          data-property-data-format=""
                          data-property-parent-context="SalesOrder"
                          >{{soldToAddress.postcode}}</span
                        ><span
                          class="property"
                          data-property-display-label="City"
                          data-property-data-type="String"
                          data-property-name="soldToAddress.city"
                          data-property-data-format=""
                          data-property-parent-context="SalesOrder"
                          >{{soldToAddress.city}}</span
                        ></span
                      >
                    </div>
                    <!--{{/if}}-->
                    <div class="conditional-block-footer">&nbsp;</div>
                  </section>
                  <section
                    class="conditional-block"
                    data-context-condition='[{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"1","value1":{"label":"Name","data":{"name":"name","title":"Name","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"localizedCountryPropertyDataType","targetNode":"","isCollection":false,"kind":"SCALAR","label":"Name","node":"String","iconType":"csv"},"id":"soldToAddress.country.name","key":"soldToAddress.country.name","labelKey":"Name","labelPath":"Sold-to address > Country > Name"},"value2":null,"key":"1","operator":"notEmpty"}]'
                  >
                    <!--{{#if ( not ( or ( eq soldToAddress.country.name null ) ( eq soldToAddress.country.name "" ) ( eq soldToAddress.country.name undefined ) ) )}}-->
                    <div class="conditional-block-body">
                      <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                        ><span
                          class="property"
                          data-property-display-label="Name"
                          data-property-data-type="String"
                          data-property-name="soldToAddress.country.name"
                          data-property-data-format=""
                          data-property-parent-context="SalesOrder"
                          >{{soldToAddress.country.name}}</span
                        ></span
                      >
                    </div>
                    <!--{{/if}}-->
                    <div class="conditional-block-footer">&nbsp;</div>
                  </section>
                </td>
                <td>
                  <p>
                    <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                      ><strong
                        ><span
                          class="property"
                          data-property-display-label="Name"
                          data-property-data-type="String"
                          data-property-name="shipToCustomer.name"
                          data-property-data-format=""
                          data-property-parent-context="SalesOrder"
                          >{{shipToCustomer.name}}</span
                        ></strong
                      ></span
                    >
                  </p>
                  <section
                    class="conditional-block"
                    data-context-condition='[{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"1","value1":{"label":"Address line 1","data":{"name":"addressLine1","title":"Address line 1","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"addressLineDataType","targetNode":"","isCollection":false,"kind":"SCALAR","label":"Address line 1","node":"String","iconType":"csv"},"id":"shipToAddress.addressLine1","key":"shipToAddress.addressLine1","labelKey":"Address line 1","labelPath":"Ship-to address > Address line 1"},"value2":null,"key":"1","operator":"notEmpty"}]'
                  >
                    <!--{{#if ( not ( or ( eq shipToAddress.addressLine1 null ) ( eq shipToAddress.addressLine1 "" ) ( eq shipToAddress.addressLine1 undefined ) ) )}}-->
                    <div class="conditional-block-body">
                      <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                        ><span
                          class="property"
                          data-property-display-label="Address line 1"
                          data-property-data-type="String"
                          data-property-name="shipToAddress.addressLine1"
                          data-property-data-format=""
                          data-property-parent-context="SalesOrder"
                          >{{shipToAddress.addressLine1}}</span
                        ></span
                      >
                    </div>
                    <!--{{/if}}-->
                    <div class="conditional-block-footer">&nbsp;</div>
                  </section>
                  <section
                    class="conditional-block"
                    data-context-condition='[{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"1","value1":{"label":"Address line 2","data":{"name":"addressLine2","title":"Address line 2","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"addressLineDataType","targetNode":"","isCollection":false,"kind":"SCALAR","label":"Address line 2","node":"String","iconType":"csv"},"id":"shipToAddress.addressLine2","key":"shipToAddress.addressLine2","labelKey":"Address line 2","labelPath":"Ship-to address > Address line 2"},"value2":null,"key":"1","operator":"notEmpty"}]'
                  >
                    <!--{{#if ( not ( or ( eq shipToAddress.addressLine2 null ) ( eq shipToAddress.addressLine2 "" ) ( eq shipToAddress.addressLine2 undefined ) ) )}}-->
                    <div class="conditional-block-body">
                      <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                        ><span
                          class="property"
                          data-property-display-label="Address line 2"
                          data-property-data-type="String"
                          data-property-name="shipToAddress.addressLine2"
                          data-property-data-format=""
                          data-property-parent-context="SalesOrder"
                          >{{shipToAddress.addressLine2}}</span
                        ></span
                      >
                    </div>
                    <!--{{/if}}-->
                    <div class="conditional-block-footer">&nbsp;</div>
                  </section>
                  <section
                    class="conditional-block"
                    data-context-condition='[{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"1","value1":{"label":"ISO 3166-1 alpha 3","data":{"name":"iso31661Alpha3","title":"ISO 3166-1 alpha 3","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"","targetNode":"","isCollection":false,"kind":"SCALAR","label":"ISO 3166-1 alpha 3","node":"String","iconType":"csv"},"id":"shipToAddress.country.iso31661Alpha3","key":"shipToAddress.country.iso31661Alpha3","labelKey":"ISO 3166-1 alpha 3","labelPath":"Ship-to address > Country > ISO 3166-1 alpha 3"},"value2":"GBR","key":"1","operator":"equals"}]'
                  >
                    <!--{{#if ( eq shipToAddress.country.iso31661Alpha3 "GBR" )}}-->
                    <div class="conditional-block-body">
                      <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                        ><span
                          class="property"
                          data-property-display-label="City"
                          data-property-data-type="String"
                          data-property-name="shipToAddress.city"
                          data-property-data-format=""
                          data-property-parent-context="SalesOrder"
                          >{{shipToAddress.city}}</span
                        ><span
                          class="property"
                          data-property-display-label="Region"
                          data-property-data-type="String"
                          data-property-name="shipToAddress.region"
                          data-property-data-format=""
                          data-property-parent-context="SalesOrder"
                          >{{shipToAddress.region}}</span
                        ><span
                          class="property"
                          data-property-display-label="Postal code"
                          data-property-data-type="String"
                          data-property-name="shipToAddress.postcode"
                          data-property-data-format=""
                          data-property-parent-context="SalesOrder"
                          >{{shipToAddress.postcode}}</span
                        ></span
                      >
                    </div>
                    <!--{{/if}}-->
                    <div class="conditional-block-footer">&nbsp;</div>
                  </section>
                  <section
                    class="conditional-block"
                    data-context-condition='[{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"1","value1":{"label":"ISO 3166-1 alpha 3","data":{"name":"iso31661Alpha3","title":"ISO 3166-1 alpha 3","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"","targetNode":"","isCollection":false,"kind":"SCALAR","label":"ISO 3166-1 alpha 3","node":"String","iconType":"csv"},"id":"shipToAddress.country.iso31661Alpha3","key":"shipToAddress.country.iso31661Alpha3","labelKey":"ISO 3166-1 alpha 3","labelPath":"Ship-to address > Country > ISO 3166-1 alpha 3"},"value2":"ZAF","key":"1","operator":"equals"}]'
                  >
                    <!--{{#if ( eq shipToAddress.country.iso31661Alpha3 "ZAF" )}}-->
                    <div class="conditional-block-body">
                      <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                        ><span
                          class="property"
                          data-property-display-label="City"
                          data-property-data-type="String"
                          data-property-name="shipToAddress.city"
                          data-property-data-format=""
                          data-property-parent-context="SalesOrder"
                          >{{shipToAddress.city}}</span
                        ><span
                          class="property"
                          data-property-display-label="Region"
                          data-property-data-type="String"
                          data-property-name="shipToAddress.region"
                          data-property-data-format=""
                          data-property-parent-context="SalesOrder"
                          >{{shipToAddress.region}}</span
                        ><span
                          class="property"
                          data-property-display-label="Postal code"
                          data-property-data-type="String"
                          data-property-name="shipToAddress.postcode"
                          data-property-data-format=""
                          data-property-parent-context="SalesOrder"
                          >{{shipToAddress.postcode}}</span
                        ></span
                      >
                    </div>
                    <!--{{/if}}-->
                    <div class="conditional-block-footer">&nbsp;</div>
                  </section>
                  <section
                    class="conditional-block"
                    data-context-condition='[{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"1","value1":{"label":"ISO 3166-1 alpha 3","data":{"name":"iso31661Alpha3","title":"ISO 3166-1 alpha 3","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"","targetNode":"","isCollection":false,"kind":"SCALAR","label":"ISO 3166-1 alpha 3","node":"String","iconType":"csv"},"id":"shipToAddress.country.iso31661Alpha3","key":"shipToAddress.country.iso31661Alpha3","labelKey":"ISO 3166-1 alpha 3","labelPath":"Ship-to address > Country > ISO 3166-1 alpha 3"},"value2":"USA","key":"1","operator":"equals"}]'
                  >
                    <!--{{#if ( eq shipToAddress.country.iso31661Alpha3 "USA" )}}-->
                    <div class="conditional-block-body">
                      <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                        ><span
                          class="property"
                          data-property-display-label="City"
                          data-property-data-type="String"
                          data-property-name="shipToAddress.city"
                          data-property-data-format=""
                          data-property-parent-context="SalesOrder"
                          >{{shipToAddress.city}}</span
                        ><span
                          class="property"
                          data-property-display-label="Region"
                          data-property-data-type="String"
                          data-property-name="shipToAddress.region"
                          data-property-data-format=""
                          data-property-parent-context="SalesOrder"
                          >{{shipToAddress.region}}</span
                        ><span
                          class="property"
                          data-property-display-label="Postal code"
                          data-property-data-type="String"
                          data-property-name="shipToAddress.postcode"
                          data-property-data-format=""
                          data-property-parent-context="SalesOrder"
                          >{{shipToAddress.postcode}}</span
                        ></span
                      >
                    </div>
                    <!--{{/if}}-->
                    <div class="conditional-block-footer">&nbsp;</div>
                  </section>
                  <section
                    class="conditional-block"
                    data-context-condition='[{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"1","value1":{"label":"ISO 3166-1 alpha 3","data":{"name":"iso31661Alpha3","title":"ISO 3166-1 alpha 3","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"","targetNode":"","isCollection":false,"kind":"SCALAR","label":"ISO 3166-1 alpha 3","node":"String","iconType":"csv"},"id":"shipToAddress.country.iso31661Alpha3","key":"shipToAddress.country.iso31661Alpha3","labelKey":"ISO 3166-1 alpha 3","labelPath":"Ship-to address > Country > ISO 3166-1 alpha 3"},"value2":"GBR","key":"1","operator":"notEqual"},{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"2","value1":{"label":"ISO 3166-1 alpha 3","data":{"name":"iso31661Alpha3","title":"ISO 3166-1 alpha 3","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"","targetNode":"","isCollection":false,"kind":"SCALAR","label":"ISO 3166-1 alpha 3","node":"String","iconType":"csv"},"id":"shipToAddress.country.iso31661Alpha3","key":"shipToAddress.country.iso31661Alpha3","labelKey":"ISO 3166-1 alpha 3","labelPath":"Ship-to address > Country > ISO 3166-1 alpha 3"},"value2":"ZAF","key":"2","operator":"notEqual"},{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"3","value1":{"label":"ISO 3166-1 alpha 3","data":{"name":"iso31661Alpha3","title":"ISO 3166-1 alpha 3","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"","targetNode":"","isCollection":false,"kind":"SCALAR","label":"ISO 3166-1 alpha 3","node":"String","iconType":"csv"},"id":"shipToAddress.country.iso31661Alpha3","key":"shipToAddress.country.iso31661Alpha3","labelKey":"ISO 3166-1 alpha 3","labelPath":"Ship-to address > Country > ISO 3166-1 alpha 3"},"value2":"USA","key":"3","operator":"notEqual"}]'
                  >
                    <!--{{#if ( and ( not ( eq shipToAddress.country.iso31661Alpha3 "GBR" ) ) ( not ( eq shipToAddress.country.iso31661Alpha3 "ZAF" ) ) ( not ( eq shipToAddress.country.iso31661Alpha3 "USA" ) ) )}}-->
                    <div class="conditional-block-body">
                      <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                        ><span
                          class="property"
                          data-property-display-label="Postal code"
                          data-property-data-type="String"
                          data-property-name="shipToAddress.postcode"
                          data-property-data-format=""
                          data-property-parent-context="SalesOrder"
                          >{{shipToAddress.postcode}}</span
                        ><span
                          class="property"
                          data-property-display-label="City"
                          data-property-data-type="String"
                          data-property-name="shipToAddress.city"
                          data-property-data-format=""
                          data-property-parent-context="SalesOrder"
                          >{{shipToAddress.city}}</span
                        ></span
                      >
                    </div>
                    <!--{{/if}}-->
                    <div class="conditional-block-footer">&nbsp;</div>
                  </section>
                  <section
                    class="conditional-block"
                    data-context-condition='[{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"1","value1":{"label":"Name","data":{"name":"name","title":"Name","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"localizedCountryPropertyDataType","targetNode":"","isCollection":false,"kind":"SCALAR","label":"Name","node":"String","iconType":"csv"},"id":"shipToAddress.country.name","key":"shipToAddress.country.name","labelKey":"Name","labelPath":"Ship-to address > Country > Name"},"value2":null,"key":"1","operator":"notEmpty"}]'
                  >
                    <!--{{#if ( not ( or ( eq shipToAddress.country.name null ) ( eq shipToAddress.country.name "" ) ( eq shipToAddress.country.name undefined ) ) )}}-->
                    <div class="conditional-block-body">
                      <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                        ><span
                          class="property"
                          data-property-display-label="Name"
                          data-property-data-type="String"
                          data-property-name="shipToAddress.country.name"
                          data-property-data-format=""
                          data-property-parent-context="SalesOrder"
                          >{{shipToAddress.country.name}}</span
                        ></span
                      >
                    </div>
                    <!--{{/if}}-->
                    <div class="conditional-block-footer">&nbsp;</div>
                  </section>
                </td>
              </tr>
            </tbody>
          </table>
        </figure>
        <section class="unbreakable-block">
          <div class="unbreakable-block-body">
            <table
              class="query-table"
              data-context-object-type="SalesOrderLine"
              data-context-object-path="lines.query.edges"
              data-context-filter="[]"
              data-context-list-order='{"item":{"id":"ascending"},"itemDescription":"ascending","quantity":"ascending","unit.name":"ascending","grossPrice":"ascending","netPrice":"ascending","amountExcludingTax":"ascending","amountExcludingTaxInCompanyCurrency":"ascending","amountIncludingTaxInCompanyCurrency":"ascending","amountIncludingTax":"ascending"}'
              data-alias="YpbYkrws"
            >
              <thead class="query-table-head">
                <tr class="query-table-row">
                  <td class="query-table-cell" style="background-color: #0000001a; border: 1px solid #dfdfdf; padding: 2px">
                    <p>
                      <span style="color: #198e59; font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                        ><strong>{{ translatedContent "7d74f3b92b19da5e606d737d339a9679" }}&nbsp;</strong></span
                      >
                    </p>
                  </td>
                  <td class="query-table-cell" style="background-color: #0000001a; border: 1px solid #dfdfdf; padding: 2px">
                    <p>
                      <span style="color: #198e59; font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                        ><strong>{{ translatedContent "b5a7adde1af5c87d7fd797b6245c2a39" }}</strong></span
                      >
                    </p>
                  </td>
                  <td class="query-table-cell" style="background-color: #0000001a; border: 1px solid #dfdfdf; padding: 2px">
                    <p>
                      <span style="color: #198e59; font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                        ><strong>{{ translatedContent "694e8d1f2ee056f98ee488bdc4982d73" }}</strong></span
                      >
                    </p>
                  </td>
                  <td class="query-table-cell" style="background-color: #0000001a; border: 1px solid #dfdfdf; padding: 2px">
                    <p>
                      <span style="color: #198e59; font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                        ><strong>{{ translatedContent "19c562a36aeb455d09534f93b4f5236f" }}</strong></span
                      >
                    </p>
                  </td>
                  <td class="query-table-cell" style="background-color: #0000001a; border: 1px solid #dfdfdf; padding: 2px">
                    <p>
                      <span style="color: #198e59; font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                        ><strong>{{ translatedContent "0170ef61ec11c8b9dcf4d4a63782515c" }}</strong></span
                      >
                    </p>
                  </td>
                  <td class="query-table-cell" style="background-color: #0000001a; border: 1px solid #dfdfdf; padding: 2px">
                    <p>
                      <span style="color: #198e59; font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                        ><strong>{{ translatedContent "104d9898c04874d0fbac36e125fa1369" }}</strong></span
                      >
                    </p>
                  </td>
                  <td class="query-table-cell" style="background-color: #0000001a; border: 1px solid #dfdfdf; padding: 2px">
                    <p>
                      <span style="color: #198e59; font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                        ><strong>{{ translatedContent "9554ce40e7959ce198210376c97150b3" }}</strong></span
                      >
                    </p>
                  </td>
                  <td class="query-table-cell" style="background-color: #0000001a; border: 1px solid #dfdfdf; padding: 2px">
                    <p>
                      <span style="color: #198e59; font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                        ><strong>{{ translatedContent "c17f9183c57045cfb614e02bbb233ee1" }}</strong></span
                      >
                    </p>
                  </td>
                  <td class="query-table-cell" style="background-color: #0000001a; border: 1px solid #dfdfdf; padding: 2px">
                    <p>
                      <span style="color: #198e59; font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                        ><strong>{{ translatedContent "20a34c4e30c5bbe1d3f870ac55f0d831" }}</strong></span
                      >
                    </p>
                  </td>
                  <td class="query-table-cell" style="background-color: #0000001a; border: 1px solid #dfdfdf; padding: 2px">
                    <p>
                      <span style="color: #198e59; font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                        ><strong>{{ translatedContent "41dd1e26b4321ab2f837f69a7aae8e91" }}</strong></span
                      >
                    </p>
                  </td>
                </tr>
              </thead>
              <tbody class="query-table-body">
                <!--{{#each YpbYkrws.query.edges}}{{#with node}}-->
                <tr class="query-table-row">
                  <td class="query-table-cell" style="border: 1px solid #dfdfdf; padding: 2px">
                    <p>
                      <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                        ><span
                          class="property"
                          data-property-display-label="ID"
                          data-property-data-type="String"
                          data-property-name="item.id"
                          data-property-data-format=""
                          data-property-parent-context="SalesOrderLine"
                          >{{item.id}}</span
                        ></span
                      >
                    </p>
                  </td>
                  <td class="query-table-cell" style="border: 1px solid #dfdfdf; padding: 2px">
                    <p>
                      <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                        ><span
                          class="property"
                          data-property-display-label="Item description"
                          data-property-data-type="String"
                          data-property-name="itemDescription"
                          data-property-data-format=""
                          data-property-parent-context="SalesOrderLine"
                          >{{itemDescription}}</span
                        ></span
                      >
                    </p>
                  </td>
                  <td class="query-table-cell" style="border: 1px solid #dfdfdf; padding: 2px; text-align: right">
                    <p>
                      <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                        ><span
                          class="property"
                          data-property-display-label="Quantity in sales unit"
                          data-property-data-type="Decimal"
                          data-property-name="quantity"
                          data-property-data-format="2"
                          data-property-parent-context="SalesOrderLine"
                          >{{formatNumber quantity 2}}</span
                        ></span
                      >
                    </p>
                  </td>
                  <td class="query-table-cell" style="border: 1px solid #dfdfdf; padding: 2px">
                    <p>
                      <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                        ><span
                          class="property"
                          data-property-display-label="Name"
                          data-property-data-type="String"
                          data-property-name="unit.name"
                          data-property-data-format=""
                          data-property-parent-context="SalesOrderLine"
                          >{{unit.name}}</span
                        ></span
                      >
                    </p>
                  </td>
                  <td class="query-table-cell" style="border: 1px solid #dfdfdf; padding: 2px; text-align: right">
                    <p>
                      <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                        ><span
                          class="property"
                          data-property-display-label="Symbol"
                          data-property-data-type="String"
                          data-property-name="document.currency.symbol"
                          data-property-data-format=""
                          data-property-parent-context="SalesOrderLine"
                          >{{document.currency.symbol}}</span
                        ><span
                          class="property"
                          data-property-display-label="Gross price"
                          data-property-data-type="Decimal"
                          data-property-name="grossPrice"
                          data-property-data-format="2"
                          data-property-parent-context="SalesOrderLine"
                          >{{formatNumber grossPrice 2}}</span
                        ></span
                      >
                    </p>
                  </td>
                  <td class="query-table-cell" style="border: 1px solid #dfdfdf; padding: 2px; text-align: right">
                    <p>
                      <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                        ><span
                          class="property"
                          data-property-display-label="Discount"
                          data-property-data-type="Decimal"
                          data-property-name="discount"
                          data-property-data-format="2"
                          data-property-parent-context="SalesOrderLine"
                          >{{formatNumber discount 2}}</span
                        >{{ translatedContent "0bcef9c45bd8a48eda1b26eb0c61c869" }}</span
                      >
                    </p>
                  </td>
                  <td class="query-table-cell" style="border: 1px solid #dfdfdf; padding: 2px; text-align: right">
                    <p>
                      <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                        ><span
                          class="property"
                          data-property-display-label="Symbol"
                          data-property-data-type="String"
                          data-property-name="document.currency.symbol"
                          data-property-data-format=""
                          data-property-parent-context="SalesOrderLine"
                          >{{document.currency.symbol}}</span
                        ><span
                          class="property"
                          data-property-display-label="Net price"
                          data-property-data-type="Decimal"
                          data-property-name="netPrice"
                          data-property-data-format="2"
                          data-property-parent-context="SalesOrderLine"
                          >{{formatNumber netPrice 2}}</span
                        ></span
                      >
                    </p>
                  </td>
                  <td class="query-table-cell" style="border: 1px solid #dfdfdf; padding: 2px; text-align: right">
                    <p>
                      <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                        ><span
                          class="property"
                          data-property-display-label="Symbol"
                          data-property-data-type="String"
                          data-property-name="document.currency.symbol"
                          data-property-data-format=""
                          data-property-parent-context="SalesOrderLine"
                          >{{document.currency.symbol}}</span
                        ><span
                          class="property"
                          data-property-display-label="Line amount excluding tax"
                          data-property-data-type="Decimal"
                          data-property-name="amountExcludingTax"
                          data-property-data-format="2"
                          data-property-parent-context="SalesOrderLine"
                          >{{formatNumber amountExcludingTax 2}}</span
                        ></span
                      >
                    </p>
                  </td>
                  <td class="query-table-cell" style="border: 1px solid #dfdfdf; padding: 2px; text-align: right">
                    <section
                      class="record-context"
                      data-context-object-type="SalesOrderLineTax"
                      data-context-object-path="taxes.query.edges.0.node"
                      data-context-filter="[]"
                      data-context-list-order="{}"
                      data-alias="GvOHQCzv"
                    >
                      <!--{{#with GvOHQCzv.query.edges.0.node}}-->
                      <div class="report-context-body">
                        <p>&nbsp;</p>
                        <p>
                          <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                            ><span
                              class="property"
                              data-property-display-label="Tax rate"
                              data-property-data-type="Decimal"
                              data-property-name="taxRate"
                              data-property-data-format="2"
                              data-property-parent-context="SalesOrderLineTax"
                              >{{formatNumber taxRate 2}}</span
                            >{{ translatedContent "0bcef9c45bd8a48eda1b26eb0c61c869" }}</span
                          >
                        </p>
                      </div>
                      <!--{{/with}}--><span class="report-context-footer">&nbsp;</span>
                    </section>
                  </td>
                  <td class="query-table-cell" style="border: 1px solid #dfdfdf; padding: 2px; text-align: right">
                    <p>
                      <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                        ><span
                          class="property"
                          data-property-display-label="Symbol"
                          data-property-data-type="String"
                          data-property-name="document.currency.symbol"
                          data-property-data-format=""
                          data-property-parent-context="SalesOrderLine"
                          >{{document.currency.symbol}}</span
                        ><span
                          class="property"
                          data-property-display-label="Line amount including tax"
                          data-property-data-type="Decimal"
                          data-property-name="amountIncludingTax"
                          data-property-data-format="2"
                          data-property-parent-context="SalesOrderLine"
                          >{{formatNumber amountIncludingTax 2}}</span
                        ></span
                      >
                    </p>
                  </td>
                </tr>
                <!--{{#printBreakIfLast  'grossPrice' 'sum' 'netPrice' 'sum' 'amountExcludingTax' 'sum' 'amountIncludingTax' 'sum'}}-->
                <tr class="query-table-row" data-footer-group="footer">
                  <td class="query-table-cell" style="background-color: #dfdfdf; border: 1px solid #dfdfdf; padding: 2px">
                    <p>
                      <span style="color: #198e59; font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                        ><strong>{{ translatedContent "edd291d1439a6c1c18fe38bee411580f" }}</strong></span
                      >
                    </p>
                  </td>
                  <td class="query-table-cell" style="background-color: #dfdfdf; border: 1px solid #dfdfdf; padding: 2px">
                    <p>&nbsp;</p>
                  </td>
                  <td class="query-table-cell" style="background-color: #dfdfdf; border: 1px solid #dfdfdf; padding: 2px">
                    <p>&nbsp;</p>
                  </td>
                  <td class="query-table-cell" style="background-color: #dfdfdf; border: 1px solid #dfdfdf; padding: 2px">
                    <p>&nbsp;</p>
                  </td>
                  <td class="query-table-cell" style="background-color: #dfdfdf; border: 1px solid #dfdfdf; padding: 2px; text-align: right">
                    <p>
                      <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                        ><strong
                          ><span
                            class="property"
                            data-property-display-label="Symbol"
                            data-property-data-type="String"
                            data-property-name="document.currency.symbol"
                            data-property-data-format=""
                            data-property-parent-context="SalesOrderLine"
                            >{{document.currency.symbol}}</span
                          ><span
                            class="property"
                            data-property-display-label="Gross price"
                            data-property-data-type="Decimal"
                            data-property-name="_blockAggregatedData.grossPrice.sum"
                            data-property-data-format="2"
                            data-property-parent-context="SalesOrderLine"
                            >{{formatNumber _blockAggregatedData.grossPrice.sum 2}}</span
                          ></strong
                        ></span
                      >
                    </p>
                  </td>
                  <td class="query-table-cell" style="background-color: #dfdfdf; border: 1px solid #dfdfdf; padding: 2px; text-align: right">
                    <p>&nbsp;</p>
                  </td>
                  <td class="query-table-cell" style="background-color: #dfdfdf; border: 1px solid #dfdfdf; padding: 2px; text-align: right">
                    <p>
                      <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                        ><strong
                          ><span
                            class="property"
                            data-property-display-label="Symbol"
                            data-property-data-type="String"
                            data-property-name="document.currency.symbol"
                            data-property-data-format=""
                            data-property-parent-context="SalesOrderLine"
                            >{{document.currency.symbol}}</span
                          ><span
                            class="property"
                            data-property-display-label="Net price"
                            data-property-data-type="Decimal"
                            data-property-name="_blockAggregatedData.netPrice.sum"
                            data-property-data-format="2"
                            data-property-parent-context="SalesOrderLine"
                            >{{formatNumber _blockAggregatedData.netPrice.sum 2}}</span
                          ></strong
                        ></span
                      >
                    </p>
                  </td>
                  <td class="query-table-cell" style="background-color: #dfdfdf; border: 1px solid #dfdfdf; padding: 2px; text-align: right">
                    <p>
                      <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                        ><strong
                          ><span
                            class="property"
                            data-property-display-label="Symbol"
                            data-property-data-type="String"
                            data-property-name="document.currency.symbol"
                            data-property-data-format=""
                            data-property-parent-context="SalesOrderLine"
                            >{{document.currency.symbol}}</span
                          ><span
                            class="property"
                            data-property-display-label="Line amount excluding tax"
                            data-property-data-type="Decimal"
                            data-property-name="_blockAggregatedData.amountExcludingTax.sum"
                            data-property-data-format="2"
                            data-property-parent-context="SalesOrderLine"
                            >{{formatNumber _blockAggregatedData.amountExcludingTax.sum 2}}</span
                          ></strong
                        ></span
                      >
                    </p>
                  </td>
                  <td class="query-table-cell" style="background-color: #dfdfdf; border: 1px solid #dfdfdf; padding: 2px; text-align: right">
                    <p>&nbsp;</p>
                  </td>
                  <td class="query-table-cell" style="background-color: #dfdfdf; border: 1px solid #dfdfdf; padding: 2px; text-align: right">
                    <p>
                      <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                        ><strong
                          ><span
                            class="property"
                            data-property-display-label="Symbol"
                            data-property-data-type="String"
                            data-property-name="document.currency.symbol"
                            data-property-data-format=""
                            data-property-parent-context="SalesOrderLine"
                            >{{document.currency.symbol}}</span
                          ><span
                            class="property"
                            data-property-display-label="Line amount including tax"
                            data-property-data-type="Decimal"
                            data-property-name="_blockAggregatedData.amountIncludingTax.sum"
                            data-property-data-format="2"
                            data-property-parent-context="SalesOrderLine"
                            >{{formatNumber _blockAggregatedData.amountIncludingTax.sum 2}}</span
                          ></strong
                        ></span
                      >
                    </p>
                  </td>
                </tr>
                <!--{{/printBreakIfLast}}-->
                <tr class="query-table-row" data-hidden="1">
                  <td class="query-table-cell" colspan="10">
                    <p>&nbsp;</p>
                  </td>
                </tr>
                <tr class="query-table-row" data-hidden="1">
                  <td class="query-table-cell" colspan="10">
                    <p>&nbsp;</p>
                  </td>
                </tr>
                <!--{{/with}}{{/each}}-->
                <tr class="query-table-row" data-hidden="1">
                  <td class="query-table-cell" colspan="10">
                    <p>&nbsp;</p>
                  </td>
                </tr>
              </tbody>
              <tfoot class="query-table-footer">
                <tr class="query-table-row">
                  <td class="query-table-cell">
                    <p>&nbsp;</p>
                  </td>
                  <td class="query-table-cell">
                    <p>&nbsp;</p>
                  </td>
                  <td class="query-table-cell">
                    <p>&nbsp;</p>
                  </td>
                  <td class="query-table-cell">
                    <p>&nbsp;</p>
                  </td>
                  <td class="query-table-cell">
                    <p>&nbsp;</p>
                  </td>
                  <td class="query-table-cell">
                    <p>&nbsp;</p>
                  </td>
                  <td class="query-table-cell">
                    <p>&nbsp;</p>
                  </td>
                  <td class="query-table-cell">
                    <p>&nbsp;</p>
                  </td>
                  <td class="query-table-cell">
                    <p>&nbsp;</p>
                  </td>
                  <td class="query-table-cell">
                    <p>&nbsp;</p>
                  </td>
                </tr>
              </tfoot>
            </table>
          </div>
        </section>
        <section class="unbreakable-block">
          <div class="unbreakable-block-body">
            <figure class="table" style="width: 100%">
              <table class="ck-table-resized">
                <colgroup>
                  <col style="width: 41.29%" />
                  <col style="width: 58.71%" />
                </colgroup>
                <tbody>
                  <tr>
                    <td>
                      <section
                        class="conditional-block"
                        data-context-condition='[{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"1","value1":{"label":"ID","data":{"name":"id","title":"ID","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"setupIdDataType","targetNode":"","isCollection":false,"kind":"SCALAR","label":"ID","node":"String","iconType":"csv"},"id":"site.legalCompany.legislation.id","key":"site.legalCompany.legislation.id","labelKey":"ID","labelPath":"Sales site > Legal company > Legislation > ID"},"value2":"FR","key":"1","operator":"equals"}]'
                      >
                        <!--{{#if ( eq site.legalCompany.legislation.id "FR" )}}-->
                        <div class="conditional-block-body">
                          <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                            >{{ translatedContent "451af268818e3beece67b76d45d10927" }}</span
                          ><br /><span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                            >{{ translatedContent "20dd7bc9f2831afb8b16ced2b5b770c7" }}</span
                          ><br /><span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                            >{{ translatedContent "edd0ef9a0be2114268a333224b95a8e2" }}</span
                          >
                        </div>
                        <!--{{/if}}-->
                        <div class="conditional-block-footer">&nbsp;</div>
                      </section>
                    </td>
                    <td>
                      <table
                        class="query-table"
                        data-context-object-type="SalesOrderTax"
                        data-context-object-path="taxes.query.edges"
                        data-context-filter="[]"
                        data-context-list-order='{"tax":"ascending","taxableAmount":"ascending","taxRate":"ascending","taxAmount":"ascending"}'
                        data-alias="ClLELCUx"
                      >
                        <thead class="query-table-head">
                          <tr class="query-table-row">
                            <td class="query-table-cell" style="background-color: #0000001a; border: 1px solid #dfdfdf; padding: 2px">
                              <p>
                                <span style="color: #198e59; font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                                  ><strong>{{ translatedContent "4b78ac8eb158840e9638a3aeb26c4a9d" }}</strong></span
                                >
                              </p>
                            </td>
                            <td class="query-table-cell" style="background-color: #0000001a; border: 1px solid #dfdfdf; padding: 2px">
                              <p>
                                <span style="color: #198e59; font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                                  ><strong>{{ translatedContent "59a99666437cbc6e878c5d4ef3d1c993" }}</strong></span
                                >
                              </p>
                            </td>
                            <td class="query-table-cell" style="background-color: #0000001a; border: 1px solid #dfdfdf; padding: 2px">
                              <p>
                                <span style="color: #198e59; font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                                  ><strong>{{ translatedContent "20a34c4e30c5bbe1d3f870ac55f0d831" }}</strong></span
                                >
                              </p>
                            </td>
                            <td class="query-table-cell" style="background-color: #0000001a; border: 1px solid #dfdfdf; padding: 2px">
                              <p>
                                <span style="color: #198e59; font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                                  ><strong>{{ translatedContent "617fe2857b30555c036a380e07ce16e3" }}</strong></span
                                >
                              </p>
                            </td>
                          </tr>
                        </thead>
                        <tbody class="query-table-body">
                          <!--{{#each ClLELCUx.query.edges}}{{#with node}}-->
                          <tr class="query-table-row">
                            <td class="query-table-cell" style="border: 1px solid #dfdfdf; padding: 2px">
                              <p>
                                <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                                  ><span
                                    class="property"
                                    data-property-display-label="Tax"
                                    data-property-data-type="String"
                                    data-property-name="tax"
                                    data-property-data-format=""
                                    data-property-parent-context="SalesOrderTax"
                                    >{{tax}}</span
                                  ></span
                                >
                              </p>
                            </td>
                            <td class="query-table-cell" style="border: 1px solid #dfdfdf; padding: 2px; text-align: right">
                              <p>
                                <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                                  ><span
                                    class="property"
                                    data-property-display-label="Symbol"
                                    data-property-data-type="String"
                                    data-property-name="currency.symbol"
                                    data-property-data-format=""
                                    data-property-parent-context="SalesOrderTax"
                                    >{{currency.symbol}}</span
                                  ><span
                                    class="property"
                                    data-property-display-label="Taxable amount"
                                    data-property-data-type="Decimal"
                                    data-property-name="taxableAmount"
                                    data-property-data-format="2"
                                    data-property-parent-context="SalesOrderTax"
                                    >{{formatNumber taxableAmount 2}}</span
                                  ></span
                                >
                              </p>
                            </td>
                            <td class="query-table-cell" style="border: 1px solid #dfdfdf; padding: 2px; text-align: right">
                              <p>
                                <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                                  ><span
                                    class="property"
                                    data-property-display-label="Tax rate"
                                    data-property-data-type="Decimal"
                                    data-property-name="taxRate"
                                    data-property-data-format="2"
                                    data-property-parent-context="SalesOrderTax"
                                    >{{formatNumber taxRate 2}}</span
                                  >{{ translatedContent "0bcef9c45bd8a48eda1b26eb0c61c869" }}</span
                                >
                              </p>
                            </td>
                            <td class="query-table-cell" style="border: 1px solid #dfdfdf; padding: 2px; text-align: right">
                              <p>
                                <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                                  ><span
                                    class="property"
                                    data-property-display-label="Symbol"
                                    data-property-data-type="String"
                                    data-property-name="currency.symbol"
                                    data-property-data-format=""
                                    data-property-parent-context="SalesOrderTax"
                                    >{{currency.symbol}}</span
                                  ><span
                                    class="property"
                                    data-property-display-label="Tax amount"
                                    data-property-data-type="Decimal"
                                    data-property-name="taxAmount"
                                    data-property-data-format="2"
                                    data-property-parent-context="SalesOrderTax"
                                    >{{formatNumber taxAmount 2}}</span
                                  ></span
                                >
                              </p>
                            </td>
                          </tr>
                          <tr class="query-table-row" data-hidden="1">
                            <td class="query-table-cell" colspan="4">
                              <p>&nbsp;</p>
                            </td>
                          </tr>
                          <!--{{/with}}{{/each}}-->
                          <tr class="query-table-row" data-hidden="1">
                            <td class="query-table-cell" colspan="4">
                              <p>&nbsp;</p>
                            </td>
                          </tr>
                        </tbody>
                        <tfoot class="query-table-footer">
                          <tr class="query-table-row">
                            <td class="query-table-cell">
                              <p>&nbsp;</p>
                            </td>
                            <td class="query-table-cell">
                              <p>&nbsp;</p>
                            </td>
                            <td class="query-table-cell">
                              <p>&nbsp;</p>
                            </td>
                            <td class="query-table-cell">
                              <p>&nbsp;</p>
                            </td>
                          </tr>
                        </tfoot>
                      </table>
                      <figure class="table" style="width: 100%">
                        <table class="ck-table-resized">
                          <colgroup>
                            <col style="width: 25%" />
                            <col style="width: 25%" />
                            <col style="width: 25%" />
                            <col style="width: 25%" />
                          </colgroup>
                          <tbody>
                            <tr>
                              <td style="background-color: #dfdfdf; border-width: 2px">
                                <span style="color: #198e59; font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                                  ><strong>{{ translatedContent "25b77b23fda3d051fb72598c952a82d7" }}</strong></span
                                >
                              </td>
                              <td style="background-color: #dfdfdf; border-width: 2px">
                                <span style="color: #198e59; font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                                  ><strong>{{ translatedContent "ff9e6ead51b6937f49449b030045d815" }}</strong></span
                                >
                              </td>
                              <td style="background-color: #dfdfdf; border-width: 2px">
                                <span style="color: #198e59; font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                                  ><strong>{{ translatedContent "ba12e59b20a9720f70fb907a55cd2620" }}</strong></span
                                >
                              </td>
                              <td style="background-color: #dfdfdf; border-width: 2px">
                                <span style="color: #198e59; font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                                  ><strong>{{ translatedContent "6c9e96de2efca781734a1ce231dca9f3" }}</strong></span
                                >
                              </td>
                            </tr>
                            <tr>
                              <td style="text-align: right">
                                <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                                  ><span
                                    class="property"
                                    data-property-display-label="Symbol"
                                    data-property-data-type="String"
                                    data-property-name="currency.symbol"
                                    data-property-data-format=""
                                    data-property-parent-context="SalesOrder"
                                    >{{currency.symbol}}</span
                                  ><span
                                    class="property"
                                    data-property-display-label="Total amount excluding tax"
                                    data-property-data-type="Decimal"
                                    data-property-name="totalAmountExcludingTax"
                                    data-property-data-format="2"
                                    data-property-parent-context="SalesOrder"
                                    >{{formatNumber totalAmountExcludingTax 2}}</span
                                  ></span
                                >
                              </td>
                              <td style="text-align: right">
                                <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                                  ><span
                                    class="property"
                                    data-property-display-label="Symbol"
                                    data-property-data-type="String"
                                    data-property-name="currency.symbol"
                                    data-property-data-format=""
                                    data-property-parent-context="SalesOrder"
                                    >{{currency.symbol}}</span
                                  ><span
                                    class="property"
                                    data-property-display-label="Total tax amount"
                                    data-property-data-type="Decimal"
                                    data-property-name="totalTaxAmount"
                                    data-property-data-format="2"
                                    data-property-parent-context="SalesOrder"
                                    >{{formatNumber totalTaxAmount 2}}</span
                                  ></span
                                >
                              </td>
                              <td style="text-align: right">
                                <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                                  ><span
                                    class="property"
                                    data-property-display-label="Symbol"
                                    data-property-data-type="String"
                                    data-property-name="currency.symbol"
                                    data-property-data-format=""
                                    data-property-parent-context="SalesOrder"
                                    >{{currency.symbol}}</span
                                  ><span
                                    class="property"
                                    data-property-display-label="Total amount including tax"
                                    data-property-data-type="Decimal"
                                    data-property-name="totalAmountIncludingTax"
                                    data-property-data-format="2"
                                    data-property-parent-context="SalesOrder"
                                    >{{formatNumber totalAmountIncludingTax 2}}</span
                                  ></span
                                >
                              </td>
                              <td style="text-align: right">
                                <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                                  ><strong
                                    ><span
                                      class="property"
                                      data-property-display-label="Symbol"
                                      data-property-data-type="String"
                                      data-property-name="currency.symbol"
                                      data-property-data-format=""
                                      data-property-parent-context="SalesOrder"
                                      >{{currency.symbol}}</span
                                    ><span
                                      class="property"
                                      data-property-display-label="Total amount including tax"
                                      data-property-data-type="Decimal"
                                      data-property-name="totalAmountIncludingTax"
                                      data-property-data-format="2"
                                      data-property-parent-context="SalesOrder"
                                      >{{formatNumber totalAmountIncludingTax 2}}</span
                                    ></strong
                                  ></span
                                >
                              </td>
                            </tr>
                          </tbody>
                        </table>
                      </figure>
                      <p>&nbsp;</p>
                    </td>
                  </tr>
                </tbody>
              </table>
            </figure>
          </div>
        </section>
        <section class="unbreakable-block">
          <div class="unbreakable-block-body">
            <figure class="table">
              <table>
                <tbody>
                  <tr>
                    <td style="text-align: center">
                      <section
                        class="conditional-block"
                        data-context-condition='[{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"1","value1":{"label":"NAF","data":{"name":"naf","title":"NAF","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"description","targetNode":"","isCollection":false,"kind":"SCALAR","label":"NAF","node":"String","iconType":"csv"},"id":"site.legalCompany.naf","key":"site.legalCompany.naf","labelKey":"NAF","labelPath":"Sales site > Legal company > NAF"},"value2":null,"key":"1","operator":"notEmpty"}]'
                      >
                        <!--{{#if ( not ( or ( eq site.legalCompany.naf null ) ( eq site.legalCompany.naf "" ) ( eq site.legalCompany.naf undefined ) ) )}}-->
                        <div class="conditional-block-body">
                          <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                            ><strong
                              ><span
                                class="property"
                                data-property-display-label="Legal form"
                                data-property-data-type="Enum"
                                data-property-name="site.legalCompany.legalForm"
                                data-property-data-format=""
                                data-property-parent-context="SalesOrder"
                                >{{site.legalCompany.legalForm}}</span
                              ><span
                                class="property"
                                data-property-display-label="Name"
                                data-property-data-type="String"
                                data-property-name="site.legalCompany.name"
                                data-property-data-format=""
                                data-property-parent-context="SalesOrder"
                                >{{site.legalCompany.name}}</span
                              ></strong
                            ><span
                              class="property"
                              data-property-display-label="RCS"
                              data-property-data-type="String"
                              data-property-name="site.legalCompany.rcs"
                              data-property-data-format=""
                              data-property-parent-context="SalesOrder"
                              >{{site.legalCompany.rcs}}</span
                            >{{ translatedContent "7137a85d9b8d9e6d09f47767d2ca31ee" }}<span
                              class="property"
                              data-property-display-label="NAF"
                              data-property-data-type="String"
                              data-property-name="site.legalCompany.naf"
                              data-property-data-format=""
                              data-property-parent-context="SalesOrder"
                              >{{site.legalCompany.naf}}</span
                            ></span
                          >
                        </div>
                        <!--{{/if}}-->
                        <div class="conditional-block-footer">&nbsp;</div>
                      </section>
                      <section
                        class="conditional-block"
                        data-context-condition='[{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"1","value1":{"label":"NAF","data":{"name":"naf","title":"NAF","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"description","targetNode":"","isCollection":false,"kind":"SCALAR","label":"NAF","node":"String","iconType":"csv"},"id":"site.legalCompany.naf","key":"site.legalCompany.naf","labelKey":"NAF","labelPath":"Sales site > Legal company > NAF"},"value2":null,"key":"1","operator":"empty"}]'
                      >
                        <!--{{#if ( or ( eq site.legalCompany.naf null ) ( eq site.legalCompany.naf "" ) ( eq site.legalCompany.naf undefined ) )}}-->
                        <div class="conditional-block-body">
                          <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                            ><strong
                              ><span
                                class="property"
                                data-property-display-label="Legal form"
                                data-property-data-type="Enum"
                                data-property-name="site.legalCompany.legalForm"
                                data-property-data-format=""
                                data-property-parent-context="SalesOrder"
                                >{{site.legalCompany.legalForm}}</span
                              ><span
                                class="property"
                                data-property-display-label="Name"
                                data-property-data-type="String"
                                data-property-name="site.legalCompany.name"
                                data-property-data-format=""
                                data-property-parent-context="SalesOrder"
                                >{{site.legalCompany.name}}</span
                              ></strong
                            ><span
                              class="property"
                              data-property-display-label="RCS"
                              data-property-data-type="String"
                              data-property-name="site.legalCompany.rcs"
                              data-property-data-format=""
                              data-property-parent-context="SalesOrder"
                              >{{site.legalCompany.rcs}}</span
                            ></span
                          >
                        </div>
                        <!--{{/if}}-->
                        <div class="conditional-block-footer">&nbsp;</div>
                      </section>
                      <section
                        class="record-context"
                        data-context-object-type="CompanyAddress"
                        data-context-object-path="site.legalCompany.addresses.query.edges.0.node"
                        data-context-filter="[]"
                        data-context-list-order="{}"
                        data-alias="UptVjTbI"
                      >
                        <!--{{#with UptVjTbI.legalCompany.addresses.query.edges.0.node}}-->
                        <div class="report-context-body">
                          <section
                            class="conditional-block"
                            data-context-condition='[{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"1","value1":{"label":"ISO 3166-1 alpha 3","data":{"name":"iso31661Alpha3","title":"ISO 3166-1 alpha 3","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"","targetNode":"","isCollection":false,"kind":"SCALAR","label":"ISO 3166-1 alpha 3","node":"String","iconType":"csv"},"id":"country.iso31661Alpha3","key":"country.iso31661Alpha3","labelKey":"ISO 3166-1 alpha 3","labelPath":"Country > ISO 3166-1 alpha 3"},"value2":"GBR","key":"1","operator":"equals"},{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"2","value1":{"label":"Region","data":{"name":"region","title":"Region","canSort":false,"canFilter":false,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"","targetNode":"","isCollection":false,"kind":"SCALAR","label":"Region","node":"String","iconType":"csv"},"id":"region","key":"region","labelKey":"Region","labelPath":"Region"},"value2":null,"key":"2","operator":"notEmpty"}]'
                          >
                            <!--{{#if ( and ( eq country.iso31661Alpha3 "GBR" ) ( not ( or ( eq region null ) ( eq region "" ) ( eq region undefined ) ) ) )}}-->
                            <div class="conditional-block-body">
                              <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                                ><span
                                  class="property"
                                  data-property-display-label="Address line 1"
                                  data-property-data-type="String"
                                  data-property-name="address.addressLine1"
                                  data-property-data-format=""
                                  data-property-parent-context="CompanyAddress"
                                  >{{address.addressLine1}}</span
                                ><span
                                  class="property"
                                  data-property-display-label="Address line 2"
                                  data-property-data-type="String"
                                  data-property-name="address.addressLine2"
                                  data-property-data-format=""
                                  data-property-parent-context="CompanyAddress"
                                  >{{address.addressLine2}}</span
                                ><span
                                  class="property"
                                  data-property-display-label="City"
                                  data-property-data-type="String"
                                  data-property-name="address.city"
                                  data-property-data-format=""
                                  data-property-parent-context="CompanyAddress"
                                  >{{address.city}}</span
                                ><span
                                  class="property"
                                  data-property-display-label="Region"
                                  data-property-data-type="String"
                                  data-property-name="address.region"
                                  data-property-data-format=""
                                  data-property-parent-context="CompanyAddress"
                                  >{{address.region}}</span
                                ><span
                                  class="property"
                                  data-property-display-label="Postal code"
                                  data-property-data-type="String"
                                  data-property-name="address.postcode"
                                  data-property-data-format=""
                                  data-property-parent-context="CompanyAddress"
                                  >{{address.postcode}}</span
                                ></span
                              >
                            </div>
                            <!--{{/if}}-->
                            <div class="conditional-block-footer">&nbsp;</div>
                          </section>
                          <section
                            class="conditional-block"
                            data-context-condition='[{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"1","value1":{"label":"ISO 3166-1 alpha 3","data":{"name":"iso31661Alpha3","title":"ISO 3166-1 alpha 3","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"","targetNode":"","isCollection":false,"kind":"SCALAR","label":"ISO 3166-1 alpha 3","node":"String","iconType":"csv"},"id":"country.iso31661Alpha3","key":"country.iso31661Alpha3","labelKey":"ISO 3166-1 alpha 3","labelPath":"Country > ISO 3166-1 alpha 3"},"value2":"ZAF","key":"1","operator":"equals"},{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"2","value1":{"label":"Region","data":{"name":"region","title":"Region","canSort":false,"canFilter":false,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"","targetNode":"","isCollection":false,"kind":"SCALAR","label":"Region","node":"String","iconType":"csv"},"id":"region","key":"region","labelKey":"Region","labelPath":"Region"},"value2":null,"key":"2","operator":"notEmpty"}]'
                          >
                            <!--{{#if ( and ( eq country.iso31661Alpha3 "ZAF" ) ( not ( or ( eq region null ) ( eq region "" ) ( eq region undefined ) ) ) )}}-->
                            <div class="conditional-block-body">
                              <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                                ><span
                                  class="property"
                                  data-property-display-label="Address line 1"
                                  data-property-data-type="String"
                                  data-property-name="address.addressLine1"
                                  data-property-data-format=""
                                  data-property-parent-context="CompanyAddress"
                                  >{{address.addressLine1}}</span
                                ><span
                                  class="property"
                                  data-property-display-label="Address line 2"
                                  data-property-data-type="String"
                                  data-property-name="address.addressLine2"
                                  data-property-data-format=""
                                  data-property-parent-context="CompanyAddress"
                                  >{{address.addressLine2}}</span
                                ><span
                                  class="property"
                                  data-property-display-label="City"
                                  data-property-data-type="String"
                                  data-property-name="address.city"
                                  data-property-data-format=""
                                  data-property-parent-context="CompanyAddress"
                                  >{{address.city}}</span
                                ><span
                                  class="property"
                                  data-property-display-label="Region"
                                  data-property-data-type="String"
                                  data-property-name="address.region"
                                  data-property-data-format=""
                                  data-property-parent-context="CompanyAddress"
                                  >{{address.region}}</span
                                ><span
                                  class="property"
                                  data-property-display-label="Postal code"
                                  data-property-data-type="String"
                                  data-property-name="address.postcode"
                                  data-property-data-format=""
                                  data-property-parent-context="CompanyAddress"
                                  >{{address.postcode}}</span
                                ></span
                              >
                            </div>
                            <!--{{/if}}-->
                            <div class="conditional-block-footer">&nbsp;</div>
                          </section>
                          <section
                            class="conditional-block"
                            data-context-condition='[{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"1","value1":{"label":"ISO 3166-1 alpha 3","data":{"name":"iso31661Alpha3","title":"ISO 3166-1 alpha 3","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"","targetNode":"","isCollection":false,"kind":"SCALAR","label":"ISO 3166-1 alpha 3","node":"String","iconType":"csv"},"id":"country.iso31661Alpha3","key":"country.iso31661Alpha3","labelKey":"ISO 3166-1 alpha 3","labelPath":"Country > ISO 3166-1 alpha 3"},"value2":"USA","key":"1","operator":"equals"},{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"2","value1":{"label":"Region","data":{"name":"region","title":"Region","canSort":false,"canFilter":false,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"","targetNode":"","isCollection":false,"kind":"SCALAR","label":"Region","node":"String","iconType":"csv"},"id":"region","key":"region","labelKey":"Region","labelPath":"Region"},"value2":null,"key":"2","operator":"notEmpty"}]'
                          >
                            <!--{{#if ( and ( eq country.iso31661Alpha3 "USA" ) ( not ( or ( eq region null ) ( eq region "" ) ( eq region undefined ) ) ) )}}-->
                            <div class="conditional-block-body">
                              <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                                ><span
                                  class="property"
                                  data-property-display-label="Address line 1"
                                  data-property-data-type="String"
                                  data-property-name="address.addressLine1"
                                  data-property-data-format=""
                                  data-property-parent-context="CompanyAddress"
                                  >{{address.addressLine1}}</span
                                ><span
                                  class="property"
                                  data-property-display-label="Address line 2"
                                  data-property-data-type="String"
                                  data-property-name="address.addressLine2"
                                  data-property-data-format=""
                                  data-property-parent-context="CompanyAddress"
                                  >{{address.addressLine2}}</span
                                ><span
                                  class="property"
                                  data-property-display-label="City"
                                  data-property-data-type="String"
                                  data-property-name="address.city"
                                  data-property-data-format=""
                                  data-property-parent-context="CompanyAddress"
                                  >{{address.city}}</span
                                ><span
                                  class="property"
                                  data-property-display-label="Region"
                                  data-property-data-type="String"
                                  data-property-name="address.region"
                                  data-property-data-format=""
                                  data-property-parent-context="CompanyAddress"
                                  >{{address.region}}</span
                                ><span
                                  class="property"
                                  data-property-display-label="Postal code"
                                  data-property-data-type="String"
                                  data-property-name="address.postcode"
                                  data-property-data-format=""
                                  data-property-parent-context="CompanyAddress"
                                  >{{address.postcode}}</span
                                ></span
                              >
                            </div>
                            <!--{{/if}}-->
                            <div class="conditional-block-footer">&nbsp;</div>
                          </section>
                          <p>
                            <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                              ><span
                                class="property"
                                data-property-display-label="Address line 1"
                                data-property-data-type="String"
                                data-property-name="address.addressLine1"
                                data-property-data-format=""
                                data-property-parent-context="CompanyAddress"
                                >{{address.addressLine1}}</span
                              ><span
                                class="property"
                                data-property-display-label="Postal code"
                                data-property-data-type="String"
                                data-property-name="address.postcode"
                                data-property-data-format=""
                                data-property-parent-context="CompanyAddress"
                                >{{address.postcode}}</span
                              ><span
                                class="property"
                                data-property-display-label="City"
                                data-property-data-type="String"
                                data-property-name="address.city"
                                data-property-data-format=""
                                data-property-parent-context="CompanyAddress"
                                >{{address.city}}</span
                              ><span
                                class="property"
                                data-property-display-label="Name"
                                data-property-data-type="String"
                                data-property-name="address.country.name"
                                data-property-data-format=""
                                data-property-parent-context="CompanyAddress"
                                >{{address.country.name}}</span
                              ></span
                            >
                          </p>
                        </div>
                        <!--{{/with}}--><span class="report-context-footer">&nbsp;</span>
                      </section>
                    </td>
                  </tr>
                </tbody>
              </table>
            </figure>
          </div>
        </section>
        <section
          class="conditional-block"
          data-context-condition='[{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"1","value1":{"label":"External note","data":{"name":"isExternalNote","title":"External note","canSort":true,"canFilter":true,"type":"Boolean","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"","targetNode":"","isCollection":false,"kind":"SCALAR","label":"External note","node":"Boolean","iconType":"csv"},"id":"isExternalNote","key":"isExternalNote","labelKey":"External note","labelPath":"External note"},"value2":"true","key":"1","operator":"equals"}]'
        >
          <!--{{#if ( eq isExternalNote true )}}-->
          <div class="conditional-block-body">
            <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
              ><strong>{{ translatedContent "fc9d3df613063ee57906c461320744e2" }}</strong>&nbsp;<span
                class="property"
                data-property-display-label="External note"
                data-property-data-type="_OutputTextStream"
                data-property-name="externalNote.value"
                data-property-data-format=""
                data-property-parent-context="SalesOrder"
                >{{{externalNote.value}}}</span
              ></span
            >
          </div>
          <!--{{/if}}-->
          <div class="conditional-block-footer">&nbsp;</div>
        </section>
      </div>
      <!--{{/with}}--><span class="report-context-footer">&nbsp;</span>
    </section>
  </div>
</section>
