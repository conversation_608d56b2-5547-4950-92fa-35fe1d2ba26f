{
  xtremSales{
    salesShipment{
      query (filter: "{status: {_ne: 'readyToProcess'}, _id:'{{shipment}}' }") {
        edges {
          node {
            number
            status
            deliveryDate
            externalNote {
              value
            }
            stockSite{
              name
              primaryAddress{
                name
                addressLine1
                addressLine2
                city
                region
                postcode
                country{
                  name
                  iso31661Alpha3
                }
              }
              legalCompany{
                name
                addresses{
                  query (filter: "{isPrimary:true}") {
                    edges {
                      node {
                        name
                        addressLine1
                        addressLine2
                        city
                        region
                        postcode
                        country {
                          name
                          iso31661Alpha3
                        }
                      }
                    }
                  }
                }
                legalForm
                siren
                rcs
                naf
              }
              siret
              taxIdNumber
            }
            shipToCustomer{
              name
              id
              businessEntity{
                siret
                taxIdNumber
              }
            }
            shipToAddress{
              name
              addressLine1
              addressLine2
              city
              region
              postcode
              country {
                name
                iso31661Alpha3
              }
            }
            billToCustomer{
              name
              id
              businessEntity{
                siret
                taxIdNumber
              }
            }
            billToAddress{
              name
              addressLine1
              addressLine2
              city
              region
              postcode
              country{
                name
                iso31661Alpha3
              }
            }
            date
            deliveryMode{
              name
            }
            incoterm{
              name
            }
            trackingNumber
            lines{
              query(first:500){
                edges {
                  node {
										salesOrderLines{
                      query{
                        edges {
                          node {
                            linkedDocument{
                              document {
                                number
                              }
                            }
                          }
                        }
                      }
                    }
                    item { id }
                    itemDescription
                    unit{
                      name
                    }
                    externalNote {
                      value
                    }
                    quantity
                  }
                }

              }
            }
          }
        }
      }
    }
  }
}
