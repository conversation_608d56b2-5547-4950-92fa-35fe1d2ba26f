query {
    CRLIHmep: xtremSales {
        salesOrder {
            query (filter: "{\"_id\":{\"_and\":[{\"_eq\":\"{{order}}\"}]}}", orderBy: "{}", first: 1) {
                edges {
                    node {
                        site {
                            name
                            primaryAddress {
                                addressLine1
                                addressLine2
                                country {
                                    iso31661Alpha3
                                }
                                region
                                city
                                postcode
                            }
                            country {
                                name
                            }
                            siret
                            taxIdNumber
                            legalCompany {
                                legislation {
                                    id
                                }
                                naf
                                legalForm
                                name
                                rcs
                            }
                        }
                        number
                        date
                        billToCustomer {
                            name
                            id
                            businessEntity {
                                siret
                                taxIdNumber
                            }
                        }
                        shippingDate
                        deliveryMode {
                            name
                        }
                        incoterm {
                            name
                        }
                        paymentTerm {
                            name
                        }
                        soldToCustomer {
                            name
                        }
                        soldToAddress {
                            addressLine1
                            addressLine2
                            country {
                                iso31661Alpha3
                                name
                            }
                            region
                            city
                            postcode
                        }
                        shipToCustomer {
                            name
                        }
                        shipToAddress {
                            addressLine1
                            addressLine2
                            country {
                                iso31661Alpha3
                                name
                            }
                            region
                            city
                            postcode
                        }
                        aURNLzsw: lines {
                            query (filter: "{}", orderBy: "{\"item\": {\"id\":1},\"itemDescription\":1,\"grossPrice\":1,\"netPrice\":1,\"amountIncludingTax\":1,\"amountExcludingTax\":1}") {
                                edges {
                                    node {
                                        _id
                                        item {
                                            id
                                        }
                                        itemDescription
                                        quantity
                                        unit {
                                            name
                                        }
                                        shipToAddress {
                                            country {
                                                currency {
                                                    symbol
                                                }
                                            }
                                        }
                                        grossPrice
                                        discount
                                        netPrice
                                        amountExcludingTax
                                        wtxVhBsC: taxes {
                                            query (filter: "{}", orderBy: "{}", first: 1) {
                                                edges {
                                                    node {
                                                        taxRate
                                                        _id
                                                    }
                                                }
                                            }
                                        }
                                        amountIncludingTax
                                    }
                                }
                            }
                        }
                        uDRxuJkJ: taxes {
                            query (filter: "{}", orderBy: "{\"tax\":1,\"taxableAmount\":1,\"taxRate\":1,\"taxAmount\":1}") {
                                edges {
                                    node {
                                        _id
                                        tax
                                        currency {
                                            symbol
                                        }
                                        taxableAmount
                                        taxRate
                                        taxAmount
                                    }
                                }
                            }
                        }
                        currency {
                            symbol
                        }
                        totalAmountExcludingTax
                        totalTaxAmount
                        totalAmountIncludingTax
                        isExternalNote
                        externalNote {
                            value
                        }
                        tNnCRnWT: site {
                            legalCompany {
                                addresses {
                                    query (filter: "{}", orderBy: "{}", first: 1) {
                                        edges {
                                            node {
                                                addressLine2
                                                country {
                                                    iso31661Alpha3
                                                    name
                                                }
                                                region
                                                addressLine1
                                                city
                                                postcode
                                                _id
                                            }
                                        }
                                    }
                                }
                            }
                        }
                        _id
                    }
                }
            }
        }
    }
}
