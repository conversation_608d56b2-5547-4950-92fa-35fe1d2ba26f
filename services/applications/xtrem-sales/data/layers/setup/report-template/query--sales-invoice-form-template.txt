query {
    tvpoNdBW: xtremSales {
        salesInvoice {
            query (filter: "{\"_id\":{\"_and\":[{\"_eq\":\"{{invoice}}\"}]}}", orderBy: "{}", first: 1) {
                edges {
                    node {
                        salesSite {
                            name
                            siret
                            taxIdNumber
                            legalCompany {
                                legislation {
                                    id
                                }
                                legalForm
                                name
                                rcs
                                naf
                            }
                        }
                        salesSiteAddress {
                            addressLine1
                            addressLine2
                            postcode
                            city
                            country {
                                name
                            }
                        }
                        number
                        date
                        billToCustomer {
                            name
                            id
                            taxIdNumber
                        }
                        RlOSvszC: lines {
                            query (filter: "{}", orderBy: "{}", first: 1) {
                                edges {
                                    node {
                                        consumptionAddress {
                                            addressLine1
                                            addressLine2
                                            postcode
                                            city
                                            region
                                            country {
                                                name
                                            }
                                        }
                                        _id
                                    }
                                }
                            }
                        }
                        billToAddress {
                            addressLine1
                            addressLine2
                            postcode
                            city
                            region
                            country {
                                name
                                iso31661Alpha3
                            }
                        }
                        PDqEDyGT: lines {
                            query (filter: "{}", orderBy: "{}") {
                                edges {
                                    node {
                                        _id
                                        nbYrAXFj: salesOrderLines {
                                            query (filter: "{}", orderBy: "{}", first: 1) {
                                                edges {
                                                    node {
                                                        linkedDocument {
                                                            document {
                                                                number
                                                            }
                                                        }
                                                        _id
                                                    }
                                                }
                                            }
                                        }
                                        pGcbXxMd: salesShipmentLines {
                                            query (filter: "{}", orderBy: "{}", first: 1) {
                                                edges {
                                                    node {
                                                        linkedDocument {
                                                            document {
                                                                number
                                                            }
                                                        }
                                                        _id
                                                    }
                                                }
                                            }
                                        }
                                        item {
                                            id
                                        }
                                        itemDescription
                                        quantity
                                        unit {
                                            name
                                        }
                                        document {
                                            currency {
                                                symbol
                                            }
                                        }
                                        netPrice
                                        amountExcludingTax
                                        cCgdCeBx: taxes {
                                            query (filter: "{}", orderBy: "{}", first: 1) {
                                                edges {
                                                    node {
                                                        taxRate
                                                        _id
                                                    }
                                                }
                                            }
                                        }
                                        amountIncludingTax
                                    }
                                }
                            }
                        }
                        discountPaymentBeforeDate
                        discountPaymentType
                        discountPaymentAmount
                        penaltyPaymentType
                        penaltyPaymentAmount
                        paymentTerm {
                            name
                        }
                        dueDate
                        JMxxnOeV: taxes {
                            query (filter: "{}", orderBy: "{\"tax\":1,\"taxableAmount\":1,\"taxRate\":1,\"taxAmount\":1}") {
                                edges {
                                    node {
                                        _id
                                        tax
                                        currency {
                                            symbol
                                        }
                                        taxableAmount
                                        taxRate
                                        taxAmount
                                    }
                                }
                            }
                        }
                        currency {
                            symbol
                        }
                        totalAmountExcludingTax
                        totalTaxAmount
                        totalAmountIncludingTax
                        FxHRewKl: salesSite {
                            legalCompany {
                                addresses {
                                    query (filter: "{}", orderBy: "{}", first: 1) {
                                        edges {
                                            node {
                                                addressLine1
                                                addressLine2
                                                postcode
                                                city
                                                region
                                                country {
                                                    name
                                                }
                                                _id
                                            }
                                        }
                                    }
                                }
                            }
                        }
                        isExternalNote
                        externalNote {
                            value
                        }
                        _id
                    }
                }
            }
        }
    }
}
