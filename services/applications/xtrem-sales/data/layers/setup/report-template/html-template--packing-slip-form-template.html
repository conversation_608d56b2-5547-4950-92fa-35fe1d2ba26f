<section class="record-context" data-context-object-type="SalesShipment"
  data-context-object-path="xtremSales.salesShipment.query.edges.0.node"
  data-context-filter='[{"_id":"1","label":"_id","filterType":"matches","filterValue":"shipment","data":{"name":"_id","title":"ID","canSort":true,"canFilter":true,"type":"IntOrString","isCustom":false,"isMutable":false,"isOnInputType":false,"isOnOutputType":true,"enumType":null,"dataType":"","targetNode":"","isCollection":false,"kind":"SCALAR","label":"_id","iconType":"csv"},"id":"_id","labelPath":"_id","property":{"name":"_id","title":"ID","canSort":true,"canFilter":true,"type":"IntOrString","isCustom":false,"isMutable":false,"isOnInputType":false,"isOnOutputType":true,"enumType":null,"dataType":"","targetNode":"","isCollection":false,"kind":"SCALAR","label":"_id","iconType":"csv","id":"_id","data":{"name":"_id","title":"ID","canSort":true,"canFilter":true,"type":"IntOrString","isCustom":false,"isMutable":false,"isOnInputType":false,"isOnOutputType":true,"enumType":null,"dataType":"","targetNode":"","isCollection":false,"kind":"SCALAR","label":"_id","iconType":"csv"},"key":"_id","labelKey":"_id","labelPath":"_id"},"parameter":true}]'
  data-context-list-order="{}" data-alias="UruauABL">
  <!--{{#with UruauABL.salesShipment.query.edges.0.node}}-->
  <div class="report-context-body">
    <figure class="table" style="width: 100%">
      <table class="ck-table-resized">
        <colgroup>
          <col style="width: 34.53%" />
          <col style="width: 65.47%" />
        </colgroup>
        <tbody>
          <tr>
            <td>
              <p>
                <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"><strong><span class="property"
                      data-property-display-label="Name" data-property-data-type="String"
                      data-property-name="stockSite.name" data-property-data-format=""
                      data-property-parent-context="SalesShipment">{{stockSite.name}}</span></strong></span>
              </p>
              <p>
                <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"><span class="property"
                    data-property-display-label="Address line 1" data-property-data-type="String"
                    data-property-name="stockSite.primaryAddress.addressLine1" data-property-data-format=""
                    data-property-parent-context="SalesShipment">{{stockSite.primaryAddress.addressLine1}}</span></span>
              </p>
              <section class="conditional-block"
                data-context-condition='[{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"1","value1":{"label":"Address line 2","data":{"name":"addressLine2","title":"Address line 2","canSort":false,"canFilter":false,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"","targetNode":"","isCollection":false,"kind":"SCALAR","label":"Address line 2","node":"String","iconType":"csv"},"id":"stockSite.primaryAddress.addressLine2","key":"stockSite.primaryAddress.addressLine2","labelKey":"Address line 2","labelPath":"Stock site > Primary address > Address line 2"},"value2":null,"key":"1","operator":"notEmpty"}]'>
                <!--{{#if ( not ( or ( eq stockSite.primaryAddress.addressLine2 null ) ( eq stockSite.primaryAddress.addressLine2 "" ) ( eq stockSite.primaryAddress.addressLine2 undefined ) ) )}}-->
                <div class="conditional-block-body">
                  <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"><span class="property"
                      data-property-display-label="Address line 2" data-property-data-type="String"
                      data-property-name="stockSite.primaryAddress.addressLine2" data-property-data-format=""
                      data-property-parent-context="SalesShipment">{{stockSite.primaryAddress.addressLine2}}</span></span>
                </div>
                <!--{{/if}}-->
                <div class="conditional-block-footer">&nbsp;</div>
              </section>
              <section class="conditional-block"
                data-context-condition='[{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"1","value1":{"label":"ISO 3166-1 alpha 3","data":{"name":"iso31661Alpha3","title":"ISO 3166-1 alpha 3","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"","targetNode":"","isCollection":false,"kind":"SCALAR","label":"ISO 3166-1 alpha 3","node":"String","iconType":"csv"},"id":"stockSite.country.iso31661Alpha3","key":"stockSite.country.iso31661Alpha3","labelKey":"ISO 3166-1 alpha 3","labelPath":"Stock site > Country > ISO 3166-1 alpha 3"},"value2":"GBR","key":"1","operator":"equals"},{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"2","value1":{"label":"City","data":{"name":"city","title":"City","canSort":false,"canFilter":false,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"","targetNode":"","isCollection":false,"kind":"SCALAR","label":"City","node":"String","iconType":"csv"},"id":"stockSite.primaryAddress.city","key":"stockSite.primaryAddress.city","labelKey":"City","labelPath":"Stock site > Primary address > City"},"value2":null,"key":"2","operator":"notEmpty"},{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"3","value1":{"label":"Postal code","data":{"name":"postcode","title":"Postal code","canSort":false,"canFilter":false,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"","targetNode":"","isCollection":false,"kind":"SCALAR","label":"Postal code","node":"String","iconType":"csv"},"id":"stockSite.primaryAddress.postcode","key":"stockSite.primaryAddress.postcode","labelKey":"Postal code","labelPath":"Stock site > Primary address > Postal code"},"value2":null,"key":"3","operator":"notEmpty"}]'>
                <!--{{#if ( and ( eq stockSite.country.iso31661Alpha3 "GBR" ) ( not ( or ( eq stockSite.primaryAddress.city null ) ( eq stockSite.primaryAddress.city "" ) ( eq stockSite.primaryAddress.city undefined ) ) ) ( not ( or ( eq stockSite.primaryAddress.postcode null ) ( eq stockSite.primaryAddress.postcode "" ) ( eq stockSite.primaryAddress.postcode undefined ) ) ) )}}-->
                <div class="conditional-block-body">
                  <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"><span class="property"
                      data-property-display-label="City" data-property-data-type="String"
                      data-property-name="stockSite.primaryAddress.city" data-property-data-format=""
                      data-property-parent-context="SalesShipment">{{stockSite.primaryAddress.city}}</span><span
                      class="property" data-property-display-label="Postal code" data-property-data-type="String"
                      data-property-name="stockSite.primaryAddress.postcode" data-property-data-format=""
                      data-property-parent-context="SalesShipment">{{stockSite.primaryAddress.postcode}}</span></span>
                </div>
                <!--{{/if}}-->
                <div class="conditional-block-footer">&nbsp;</div>
              </section>
              <section class="conditional-block"
                data-context-condition='[{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"1","value1":{"label":"ISO 3166-1 alpha 3","data":{"name":"iso31661Alpha3","title":"ISO 3166-1 alpha 3","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"","targetNode":"","isCollection":false,"kind":"SCALAR","label":"ISO 3166-1 alpha 3","node":"String","iconType":"csv"},"id":"stockSite.country.iso31661Alpha3","key":"stockSite.country.iso31661Alpha3","labelKey":"ISO 3166-1 alpha 3","labelPath":"Stock site > Country > ISO 3166-1 alpha 3"},"value2":"ZAF","key":"1","operator":"equals"},{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"2","value1":{"label":"City","data":{"name":"city","title":"City","canSort":false,"canFilter":false,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"","targetNode":"","isCollection":false,"kind":"SCALAR","label":"City","node":"String","iconType":"csv"},"id":"stockSite.primaryAddress.city","key":"stockSite.primaryAddress.city","labelKey":"City","labelPath":"Stock site > Primary address > City"},"value2":null,"key":"2","operator":"notEmpty"},{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"3","value1":{"label":"Postal code","data":{"name":"postcode","title":"Postal code","canSort":false,"canFilter":false,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"","targetNode":"","isCollection":false,"kind":"SCALAR","label":"Postal code","node":"String","iconType":"csv"},"id":"stockSite.primaryAddress.postcode","key":"stockSite.primaryAddress.postcode","labelKey":"Postal code","labelPath":"Stock site > Primary address > Postal code"},"value2":null,"key":"3","operator":"notEmpty"}]'>
                <!--{{#if ( and ( eq stockSite.country.iso31661Alpha3 "ZAF" ) ( not ( or ( eq stockSite.primaryAddress.city null ) ( eq stockSite.primaryAddress.city "" ) ( eq stockSite.primaryAddress.city undefined ) ) ) ( not ( or ( eq stockSite.primaryAddress.postcode null ) ( eq stockSite.primaryAddress.postcode "" ) ( eq stockSite.primaryAddress.postcode undefined ) ) ) )}}-->
                <div class="conditional-block-body">
                  <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"><span class="property"
                      data-property-display-label="City" data-property-data-type="String"
                      data-property-name="stockSite.primaryAddress.city" data-property-data-format=""
                      data-property-parent-context="SalesShipment">{{stockSite.primaryAddress.city}}</span><span
                      class="property" data-property-display-label="Postal code" data-property-data-type="String"
                      data-property-name="stockSite.primaryAddress.postcode" data-property-data-format=""
                      data-property-parent-context="SalesShipment">{{stockSite.primaryAddress.postcode}}</span></span>
                </div>
                <!--{{/if}}-->
                <div class="conditional-block-footer">&nbsp;</div>
              </section>
              <section class="conditional-block"
                data-context-condition='[{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"1","value1":{"label":"ISO 3166-1 alpha 3","data":{"name":"iso31661Alpha3","title":"ISO 3166-1 alpha 3","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"","targetNode":"","isCollection":false,"kind":"SCALAR","label":"ISO 3166-1 alpha 3","node":"String","iconType":"csv"},"id":"stockSite.country.iso31661Alpha3","key":"stockSite.country.iso31661Alpha3","labelKey":"ISO 3166-1 alpha 3","labelPath":"Stock site > Country > ISO 3166-1 alpha 3"},"value2":"USA","key":"1","operator":"equals"},{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"2","value1":{"label":"City","data":{"name":"city","title":"City","canSort":false,"canFilter":false,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"","targetNode":"","isCollection":false,"kind":"SCALAR","label":"City","node":"String","iconType":"csv"},"id":"stockSite.primaryAddress.city","key":"stockSite.primaryAddress.city","labelKey":"City","labelPath":"Stock site > Primary address > City"},"value2":null,"key":"2","operator":"notEmpty"},{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"3","value1":{"label":"Postal code","data":{"name":"postcode","title":"Postal code","canSort":false,"canFilter":false,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"","targetNode":"","isCollection":false,"kind":"SCALAR","label":"Postal code","node":"String","iconType":"csv"},"id":"stockSite.primaryAddress.postcode","key":"stockSite.primaryAddress.postcode","labelKey":"Postal code","labelPath":"Stock site > Primary address > Postal code"},"value2":null,"key":"3","operator":"notEmpty"}]'>
                <!--{{#if ( and ( eq stockSite.country.iso31661Alpha3 "USA" ) ( not ( or ( eq stockSite.primaryAddress.city null ) ( eq stockSite.primaryAddress.city "" ) ( eq stockSite.primaryAddress.city undefined ) ) ) ( not ( or ( eq stockSite.primaryAddress.postcode null ) ( eq stockSite.primaryAddress.postcode "" ) ( eq stockSite.primaryAddress.postcode undefined ) ) ) )}}-->
                <div class="conditional-block-body">
                  <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"><span class="property"
                      data-property-display-label="City" data-property-data-type="String"
                      data-property-name="stockSite.primaryAddress.city" data-property-data-format=""
                      data-property-parent-context="SalesShipment">{{stockSite.primaryAddress.city}}</span><span
                      class="property" data-property-display-label="Postal code" data-property-data-type="String"
                      data-property-name="stockSite.primaryAddress.postcode" data-property-data-format=""
                      data-property-parent-context="SalesShipment">{{stockSite.primaryAddress.postcode}}</span></span>
                </div>
                <!--{{/if}}-->
                <div class="conditional-block-footer">&nbsp;</div>
              </section>
              <section class="conditional-block"
                data-context-condition='[{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"1","value1":{"label":"ISO 3166-1 alpha 3","data":{"name":"iso31661Alpha3","title":"ISO 3166-1 alpha 3","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"","targetNode":"","isCollection":false,"kind":"SCALAR","label":"ISO 3166-1 alpha 3","node":"String","iconType":"csv"},"id":"stockSite.country.iso31661Alpha3","key":"stockSite.country.iso31661Alpha3","labelKey":"ISO 3166-1 alpha 3","labelPath":"Stock site > Country > ISO 3166-1 alpha 3"},"value2":"ZAF","key":"1","operator":"notEqual"},{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"2","value1":{"label":"ISO 3166-1 alpha 3","data":{"name":"iso31661Alpha3","title":"ISO 3166-1 alpha 3","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"","targetNode":"","isCollection":false,"kind":"SCALAR","label":"ISO 3166-1 alpha 3","node":"String","iconType":"csv"},"id":"stockSite.country.iso31661Alpha3","key":"stockSite.country.iso31661Alpha3","labelKey":"ISO 3166-1 alpha 3","labelPath":"Stock site > Country > ISO 3166-1 alpha 3"},"value2":"USA","key":"2","operator":"notEqual"},{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"3","value1":{"label":"City","data":{"name":"city","title":"City","canSort":false,"canFilter":false,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"","targetNode":"","isCollection":false,"kind":"SCALAR","label":"City","node":"String","iconType":"csv"},"id":"stockSite.primaryAddress.city","key":"stockSite.primaryAddress.city","labelKey":"City","labelPath":"Stock site > Primary address > City"},"value2":null,"key":"3","operator":"notEmpty"}]'>
                <!--{{#if ( and ( not ( eq stockSite.country.iso31661Alpha3 "ZAF" ) ) ( not ( eq stockSite.country.iso31661Alpha3 "USA" ) ) ( not ( or ( eq stockSite.primaryAddress.city null ) ( eq stockSite.primaryAddress.city "" ) ( eq stockSite.primaryAddress.city undefined ) ) ) )}}-->
                <div class="conditional-block-body">
                  <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"><span class="property"
                      data-property-display-label="City" data-property-data-type="String"
                      data-property-name="stockSite.primaryAddress.city" data-property-data-format=""
                      data-property-parent-context="SalesShipment">{{stockSite.primaryAddress.city}}</span><span
                      class="property" data-property-display-label="Postal code" data-property-data-type="String"
                      data-property-name="stockSite.primaryAddress.postcode" data-property-data-format=""
                      data-property-parent-context="SalesShipment">{{stockSite.primaryAddress.postcode}}</span></span>
                </div>
                <!--{{/if}}-->
                <div class="conditional-block-footer">&nbsp;</div>
              </section>
              <p>
                <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"><span class="property"
                    data-property-display-label="Name" data-property-data-type="String"
                    data-property-name="stockSite.country.name" data-property-data-format=""
                    data-property-parent-context="SalesShipment">{{stockSite.country.name}}</span></span>
              </p>
              <section class="conditional-block"
                data-context-condition='[{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"1","value1":{"label":"SIRET","data":{"name":"siret","title":"SIRET","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":false,"isOnOutputType":true,"enumType":null,"dataType":"description","targetNode":"","isCollection":false,"kind":"SCALAR","label":"SIRET","node":"String","iconType":"csv"},"id":"stockSite.siret","key":"stockSite.siret","labelKey":"SIRET","labelPath":"Stock site > SIRET"},"value2":null,"key":"1","operator":"notEmpty"}]'>
                <!--{{#if ( not ( or ( eq stockSite.siret null ) ( eq stockSite.siret "" ) ( eq stockSite.siret undefined ) ) )}}-->
                <div class="conditional-block-body">
                  <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"><strong>&nbsp;{{
                      translatedContent "d04e2c1b67f3ef0d475409516b812e8b" }}&nbsp;</strong><span class="property"
                      data-property-display-label="SIRET" data-property-data-type="String"
                      data-property-name="stockSite.siret" data-property-data-format=""
                      data-property-parent-context="SalesShipment">{{stockSite.siret}}</span></span>
                </div>
                <!--{{/if}}-->
                <div class="conditional-block-footer">&nbsp;</div>
              </section>
              <section class="conditional-block"
                data-context-condition='[{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"1","value1":{"label":"Tax ID number","data":{"name":"taxIdNumber","title":"Tax ID number","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":false,"isOnOutputType":true,"enumType":null,"dataType":"taxIdentificationDataType","targetNode":"","isCollection":false,"kind":"SCALAR","label":"Tax ID number","node":"String","iconType":"csv"},"id":"stockSite.taxIdNumber","key":"stockSite.taxIdNumber","labelKey":"Tax ID number","labelPath":"Stock site > Tax ID number"},"value2":null,"key":"1","operator":"notEmpty"}]'>
                <!--{{#if ( not ( or ( eq stockSite.taxIdNumber null ) ( eq stockSite.taxIdNumber "" ) ( eq stockSite.taxIdNumber undefined ) ) )}}-->
                <div class="conditional-block-body">
                  <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"><strong>&nbsp;{{
                      translatedContent "37a7a58f6fdc66ce34c2bf56389a2aac" }}&nbsp;</strong><span class="property"
                      data-property-display-label="Tax ID number" data-property-data-type="String"
                      data-property-name="stockSite.taxIdNumber" data-property-data-format=""
                      data-property-parent-context="SalesShipment">{{stockSite.taxIdNumber}}</span></span>
                </div>
                <!--{{/if}}-->
                <div class="conditional-block-footer">&nbsp;</div>
              </section>
            </td>
            <td style="background-color: #dfdfdf; text-align: right">
              <p>&nbsp;</p>
              <h2>
                <span style="color: #198e59; font-family: 'Sage UI', Geneva, sans-serif">{{ translatedContent
                  "3c43d2c05c45b4109447e21047136ae3" }}</span>
              </h2>
              <p>
                <span style="color: #198e59; font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"><strong>{{
                    translatedContent "2a39f204d4eac05604c2059e5ac2f9f7" }}</strong></span><span
                  style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"><strong><span class="property"
                      data-property-display-label="Number" data-property-data-type="String" data-property-name="number"
                      data-property-data-format=""
                      data-property-parent-context="SalesShipment">{{number}}</span></strong></span>
              </p>
              <p>
                <span style="color: #198e59; font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"><strong>{{
                    translatedContent "a3a79b28bc45b7fd393effd056b917e1" }}</strong></span><span
                  style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"><strong>&nbsp;<span
                      class="property" data-property-display-label="Shipping date" data-property-data-type="Date"
                      data-property-name="date" data-property-data-format="FullDate"
                      data-property-parent-context="SalesShipment">{{formatDate date 'FullDate'}}</span></strong></span>
              </p>
            </td>
          </tr>
          <tr>
            <td style="border-color: #ffffff">
              <p>
                <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"><strong>{{ translatedContent
                    "5eea367ea73b909880393bd1ae79fc67" }}&nbsp;</strong></span><span
                  style="color: #198e59; font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"><strong><span
                      class="property" data-property-display-label="Name" data-property-data-type="String"
                      data-property-name="billToCustomer.name" data-property-data-format=""
                      data-property-parent-context="SalesShipment">{{billToCustomer.name}}</span></strong></span>
              </p>
              <section class="conditional-block"
                data-context-condition='[{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"1","value1":{"label":"ID","data":{"name":"id","title":"ID","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":false,"isOnOutputType":true,"enumType":null,"dataType":"","targetNode":"","isCollection":false,"kind":"SCALAR","label":"ID","node":"String","iconType":"csv"},"id":"billToCustomer.id","key":"billToCustomer.id","labelKey":"ID","labelPath":"Bill-to customer > ID"},"value2":null,"key":"1","operator":"notEmpty"}]'>
                <!--{{#if ( not ( or ( eq billToCustomer.id null ) ( eq billToCustomer.id "" ) ( eq billToCustomer.id undefined ) ) )}}-->
                <div class="conditional-block-body">
                  <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"><strong>{{ translatedContent
                      "d13d8380c3f4de07fef91a42fe6c60d7" }}&nbsp;</strong><span class="property"
                      data-property-display-label="ID" data-property-data-type="String"
                      data-property-name="billToCustomer.id" data-property-data-format=""
                      data-property-parent-context="SalesShipment">{{billToCustomer.id}}</span></span>
                </div>
                <!--{{/if}}-->
                <div class="conditional-block-footer">&nbsp;</div>
              </section>
              <section class="conditional-block"
                data-context-condition='[{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"1","value1":{"label":"SIRET","data":{"name":"siret","title":"SIRET","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":false,"isOnOutputType":true,"enumType":null,"dataType":"description","targetNode":"","isCollection":false,"kind":"SCALAR","label":"SIRET","node":"String","iconType":"csv"},"id":"billToCustomer.siret","key":"billToCustomer.siret","labelKey":"SIRET","labelPath":"Bill-to customer > SIRET"},"value2":null,"key":"1","operator":"notEmpty"}]'>
                <!--{{#if ( not ( or ( eq billToCustomer.siret null ) ( eq billToCustomer.siret "" ) ( eq billToCustomer.siret undefined ) ) )}}-->
                <div class="conditional-block-body">
                  <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"><strong>{{ translatedContent
                      "d04e2c1b67f3ef0d475409516b812e8b" }}&nbsp;</strong><span class="property"
                      data-property-display-label="SIRET" data-property-data-type="String"
                      data-property-name="billToCustomer.siret" data-property-data-format=""
                      data-property-parent-context="SalesShipment">{{billToCustomer.siret}}</span></span>
                </div>
                <!--{{/if}}-->
                <div class="conditional-block-footer">&nbsp;</div>
              </section>
              <section class="conditional-block"
                data-context-condition='[{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"1","value1":{"label":"Tax ID number","data":{"name":"taxIdNumber","title":"Tax ID number","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":false,"isOnOutputType":true,"enumType":null,"dataType":"taxIdentificationDataType","targetNode":"","isCollection":false,"kind":"SCALAR","label":"Tax ID number","node":"String","iconType":"csv"},"id":"billToCustomer.taxIdNumber","key":"billToCustomer.taxIdNumber","labelKey":"Tax ID number","labelPath":"Bill-to customer > Tax ID number"},"value2":null,"key":"1","operator":"notEmpty"}]'>
                <!--{{#if ( not ( or ( eq billToCustomer.taxIdNumber null ) ( eq billToCustomer.taxIdNumber "" ) ( eq billToCustomer.taxIdNumber undefined ) ) )}}-->
                <div class="conditional-block-body">
                  <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"><strong>{{ translatedContent
                      "37a7a58f6fdc66ce34c2bf56389a2aac" }}&nbsp;</strong><span class="property"
                      data-property-display-label="Tax ID number" data-property-data-type="String"
                      data-property-name="billToCustomer.taxIdNumber" data-property-data-format=""
                      data-property-parent-context="SalesShipment">{{billToCustomer.taxIdNumber}}</span></span>
                </div>
                <!--{{/if}}-->
                <div class="conditional-block-footer">&nbsp;</div>
              </section>
            </td>
            <td style="text-align: right">
              <p>
                <span style="color: #000000; font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"><strong>{{
                    translatedContent "5d2465f709d5a1a3e91dd31102058284" }}</strong></span><span
                  style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"><span class="property"
                    data-property-display-label="Delivery date" data-property-data-type="Date"
                    data-property-name="deliveryDate" data-property-data-format="FullDate"
                    data-property-parent-context="SalesShipment">{{formatDate deliveryDate
                    'FullDate'}}</span></span><span
                  style="color: #198e59; font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"><strong>&nbsp;</strong></span>
              </p>
              <section class="conditional-block"
                data-context-condition='[{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"1","value1":{"label":"Name","data":{"name":"name","title":"Name","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"localizedName","targetNode":"","isCollection":false,"kind":"SCALAR","label":"Name","node":"String","iconType":"csv"},"id":"deliveryMode.name","key":"deliveryMode.name","labelKey":"Name","labelPath":"Delivery mode > Name"},"value2":null,"key":"1","operator":"notEmpty"}]'>
                <!--{{#if ( not ( or ( eq deliveryMode.name null ) ( eq deliveryMode.name "" ) ( eq deliveryMode.name undefined ) ) )}}-->
                <div class="conditional-block-body">
                  <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"><strong>{{ translatedContent
                      "0f55fedcee89c8d9407351716835247e" }}&nbsp;</strong><span class="property"
                      data-property-display-label="Name" data-property-data-type="String"
                      data-property-name="deliveryMode.name" data-property-data-format=""
                      data-property-parent-context="SalesShipment">{{deliveryMode.name}}</span></span>
                </div>
                <!--{{/if}}-->
                <div class="conditional-block-footer">&nbsp;</div>
              </section>
              <section class="conditional-block"
                data-context-condition='[{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"1","value1":{"label":"Name","data":{"name":"name","title":"Name","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"localizedName","targetNode":"","isCollection":false,"kind":"SCALAR","label":"Name","node":"String","iconType":"csv"},"id":"incoterm.name","key":"incoterm.name","labelKey":"Name","labelPath":"Incoterms rule > Name"},"value2":null,"key":"1","operator":"notEmpty"}]'>
                <!--{{#if ( not ( or ( eq incoterm.name null ) ( eq incoterm.name "" ) ( eq incoterm.name undefined ) ) )}}-->
                <div class="conditional-block-body">
                  <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"><strong>{{ translatedContent
                      "c42d7a1f13e2fd7ab2c829f3662f736f" }}&nbsp;</strong><span class="property"
                      data-property-display-label="Name" data-property-data-type="String"
                      data-property-name="incoterm.name" data-property-data-format=""
                      data-property-parent-context="SalesShipment">{{incoterm.name}}</span></span>
                </div>
                <!--{{/if}}-->
                <div class="conditional-block-footer">&nbsp;</div>
              </section>
              <section class="conditional-block"
                data-context-condition='[{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"1","value1":{"label":"Tracking number","data":{"name":"trackingNumber","title":"Tracking number","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"propertyDataType","targetNode":"","isCollection":false,"kind":"SCALAR","label":"Tracking number","node":"String","iconType":"csv"},"id":"trackingNumber","key":"trackingNumber","labelKey":"Tracking number","labelPath":"Tracking number"},"value2":null,"key":"1","operator":"notEmpty"}]'>
                <!--{{#if ( not ( or ( eq trackingNumber null ) ( eq trackingNumber "" ) ( eq trackingNumber undefined ) ) )}}-->
                <div class="conditional-block-body">
                  <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"><strong>{{ translatedContent
                      "f3ebde38fb84b9d5677bf2600e4270da" }}&nbsp;</strong><span class="property"
                      data-property-display-label="Tracking number" data-property-data-type="String"
                      data-property-name="trackingNumber" data-property-data-format=""
                      data-property-parent-context="SalesShipment">{{trackingNumber}}</span></span>
                </div>
                <!--{{/if}}-->
                <div class="conditional-block-footer">&nbsp;</div>
              </section>
            </td>
          </tr>
        </tbody>
      </table>
    </figure>
    <figure class="table">
      <table>
        <tbody>
          <tr>
            <td style="background-color: #dfdfdf; border-color: #dfdfdf">
              <span style="color: #198e59; font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"><strong>{{
                  translatedContent "6fd791a834ec2f5a235b58bdea1746fc" }}</strong></span>
            </td>
            <td style="background-color: #dfdfdf; border-color: #dfdfdf">
              <span style="color: #198e59; font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"><strong>{{
                  translatedContent "84e839915aa016435941dda50c367e0c" }}</strong></span>
            </td>
          </tr>
          <tr>
            <td style="border-color: #ffffff">
              <p>
                <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"><strong><span class="property"
                      data-property-display-label="Name" data-property-data-type="String"
                      data-property-name="billToAddress.name" data-property-data-format=""
                      data-property-parent-context="SalesShipment">{{billToAddress.name}}</span></strong></span>
              </p>
              <p>
                <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"><span class="property"
                    data-property-display-label="Address line 1" data-property-data-type="String"
                    data-property-name="billToAddress.addressLine1" data-property-data-format=""
                    data-property-parent-context="SalesShipment">{{billToAddress.addressLine1}}</span></span>
              </p>
              <section class="conditional-block"
                data-context-condition='[{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"1","value1":{"label":"Address line 2","data":{"name":"addressLine2","title":"Address line 2","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"addressLineDataType","targetNode":"","isCollection":false,"kind":"SCALAR","label":"Address line 2","node":"String","iconType":"csv"},"id":"billToAddress.addressLine2","key":"billToAddress.addressLine2","labelKey":"Address line 2","labelPath":"Bill-to address > Address line 2"},"value2":null,"key":"1","operator":"notEmpty"}]'>
                <!--{{#if ( not ( or ( eq billToAddress.addressLine2 null ) ( eq billToAddress.addressLine2 "" ) ( eq billToAddress.addressLine2 undefined ) ) )}}-->
                <div class="conditional-block-body">
                  <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"><span class="property"
                      data-property-display-label="Address line 2" data-property-data-type="String"
                      data-property-name="billToAddress.addressLine2" data-property-data-format=""
                      data-property-parent-context="SalesShipment">{{billToAddress.addressLine2}}</span></span>
                </div>
                <!--{{/if}}-->
                <div class="conditional-block-footer">&nbsp;</div>
              </section>
              <section class="conditional-block"
                data-context-condition='[{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"1","value1":{"label":"ISO 3166-1 alpha 3","data":{"name":"iso31661Alpha3","title":"ISO 3166-1 alpha 3","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"","targetNode":"","isCollection":false,"kind":"SCALAR","label":"ISO 3166-1 alpha 3","node":"String","iconType":"csv"},"id":"billToAddress.country.iso31661Alpha3","key":"billToAddress.country.iso31661Alpha3","labelKey":"ISO 3166-1 alpha 3","labelPath":"Bill-to address > Country > ISO 3166-1 alpha 3"},"value2":"GBR","key":"1","operator":"equals"},{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"2","value1":{"label":"City","data":{"name":"city","title":"City","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"cityDataType","targetNode":"","isCollection":false,"kind":"SCALAR","label":"City","node":"String","iconType":"csv"},"id":"billToAddress.city","key":"billToAddress.city","labelKey":"City","labelPath":"Bill-to address > City"},"value2":null,"key":"2","operator":"notEmpty"},{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"3","value1":{"label":"Postal code","data":{"name":"postcode","title":"Postal code","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"postcodeDataType","targetNode":"","isCollection":false,"kind":"SCALAR","label":"Postal code","node":"String","iconType":"csv"},"id":"billToAddress.postcode","key":"billToAddress.postcode","labelKey":"Postal code","labelPath":"Bill-to address > Postal code"},"value2":null,"key":"3","operator":"notEmpty"}]'>
                <!--{{#if ( and ( eq billToAddress.country.iso31661Alpha3 "GBR" ) ( not ( or ( eq billToAddress.city null ) ( eq billToAddress.city "" ) ( eq billToAddress.city undefined ) ) ) ( not ( or ( eq billToAddress.postcode null ) ( eq billToAddress.postcode "" ) ( eq billToAddress.postcode undefined ) ) ) )}}-->
                <div class="conditional-block-body">
                  <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"><span class="property"
                      data-property-display-label="City" data-property-data-type="String"
                      data-property-name="billToAddress.city" data-property-data-format=""
                      data-property-parent-context="SalesShipment">{{billToAddress.city}}</span><span class="property"
                      data-property-display-label="Postal code" data-property-data-type="String"
                      data-property-name="billToAddress.postcode" data-property-data-format=""
                      data-property-parent-context="SalesShipment">{{billToAddress.postcode}}</span></span>
                </div>
                <!--{{/if}}-->
                <div class="conditional-block-footer">&nbsp;</div>
              </section>
              <section class="conditional-block"
                data-context-condition='[{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"1","value1":{"label":"ISO 3166-1 alpha 3","data":{"name":"iso31661Alpha3","title":"ISO 3166-1 alpha 3","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"","targetNode":"","isCollection":false,"kind":"SCALAR","label":"ISO 3166-1 alpha 3","node":"String","iconType":"csv"},"id":"billToAddress.country.iso31661Alpha3","key":"billToAddress.country.iso31661Alpha3","labelKey":"ISO 3166-1 alpha 3","labelPath":"Bill-to address > Country > ISO 3166-1 alpha 3"},"value2":"ZAF","key":"1","operator":"equals"},{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"2","value1":{"label":"City","data":{"name":"city","title":"City","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"cityDataType","targetNode":"","isCollection":false,"kind":"SCALAR","label":"City","node":"String","iconType":"csv"},"id":"billToAddress.city","key":"billToAddress.city","labelKey":"City","labelPath":"Bill-to address > City"},"value2":null,"key":"2","operator":"notEmpty"},{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"3","value1":{"label":"Postal code","data":{"name":"postcode","title":"Postal code","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"postcodeDataType","targetNode":"","isCollection":false,"kind":"SCALAR","label":"Postal code","node":"String","iconType":"csv"},"id":"billToAddress.postcode","key":"billToAddress.postcode","labelKey":"Postal code","labelPath":"Bill-to address > Postal code"},"value2":null,"key":"3","operator":"notEmpty"}]'>
                <!--{{#if ( and ( eq billToAddress.country.iso31661Alpha3 "ZAF" ) ( not ( or ( eq billToAddress.city null ) ( eq billToAddress.city "" ) ( eq billToAddress.city undefined ) ) ) ( not ( or ( eq billToAddress.postcode null ) ( eq billToAddress.postcode "" ) ( eq billToAddress.postcode undefined ) ) ) )}}-->
                <div class="conditional-block-body">
                  <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"><span class="property"
                      data-property-display-label="City" data-property-data-type="String"
                      data-property-name="billToAddress.city" data-property-data-format=""
                      data-property-parent-context="SalesShipment">{{billToAddress.city}}</span><span class="property"
                      data-property-display-label="Postal code" data-property-data-type="String"
                      data-property-name="billToAddress.postcode" data-property-data-format=""
                      data-property-parent-context="SalesShipment">{{billToAddress.postcode}}</span></span>
                </div>
                <!--{{/if}}-->
                <div class="conditional-block-footer">&nbsp;</div>
              </section>
              <section class="conditional-block"
                data-context-condition='[{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"1","value1":{"label":"ISO 3166-1 alpha 3","data":{"name":"iso31661Alpha3","title":"ISO 3166-1 alpha 3","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"","targetNode":"","isCollection":false,"kind":"SCALAR","label":"ISO 3166-1 alpha 3","node":"String","iconType":"csv"},"id":"billToAddress.country.iso31661Alpha3","key":"billToAddress.country.iso31661Alpha3","labelKey":"ISO 3166-1 alpha 3","labelPath":"Bill-to address > Country > ISO 3166-1 alpha 3"},"value2":"USA","key":"1","operator":"equals"},{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"2","value1":{"label":"City","data":{"name":"city","title":"City","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"cityDataType","targetNode":"","isCollection":false,"kind":"SCALAR","label":"City","node":"String","iconType":"csv"},"id":"billToAddress.city","key":"billToAddress.city","labelKey":"City","labelPath":"Bill-to address > City"},"value2":null,"key":"2","operator":"notEmpty"},{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"3","value1":{"label":"Postal code","data":{"name":"postcode","title":"Postal code","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"postcodeDataType","targetNode":"","isCollection":false,"kind":"SCALAR","label":"Postal code","node":"String","iconType":"csv"},"id":"billToAddress.postcode","key":"billToAddress.postcode","labelKey":"Postal code","labelPath":"Bill-to address > Postal code"},"value2":null,"key":"3","operator":"notEmpty"}]'>
                <!--{{#if ( and ( eq billToAddress.country.iso31661Alpha3 "USA" ) ( not ( or ( eq billToAddress.city null ) ( eq billToAddress.city "" ) ( eq billToAddress.city undefined ) ) ) ( not ( or ( eq billToAddress.postcode null ) ( eq billToAddress.postcode "" ) ( eq billToAddress.postcode undefined ) ) ) )}}-->
                <div class="conditional-block-body">
                  <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"><span class="property"
                      data-property-display-label="City" data-property-data-type="String"
                      data-property-name="billToAddress.city" data-property-data-format=""
                      data-property-parent-context="SalesShipment">{{billToAddress.city}}</span><span class="property"
                      data-property-display-label="Postal code" data-property-data-type="String"
                      data-property-name="billToAddress.postcode" data-property-data-format=""
                      data-property-parent-context="SalesShipment">{{billToAddress.postcode}}</span></span>
                </div>
                <!--{{/if}}-->
                <div class="conditional-block-footer">&nbsp;</div>
              </section>
              <section class="conditional-block"
                data-context-condition='[{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"1","value1":{"label":"ISO 3166-1 alpha 3","data":{"name":"iso31661Alpha3","title":"ISO 3166-1 alpha 3","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"","targetNode":"","isCollection":false,"kind":"SCALAR","label":"ISO 3166-1 alpha 3","node":"String","iconType":"csv"},"id":"stockSite.country.iso31661Alpha3","key":"stockSite.country.iso31661Alpha3","labelKey":"ISO 3166-1 alpha 3","labelPath":"Stock site > Country > ISO 3166-1 alpha 3"},"value2":"GBR","key":"1","operator":"notEqual"},{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"2","value1":{"label":"ISO 3166-1 alpha 3","data":{"name":"iso31661Alpha3","title":"ISO 3166-1 alpha 3","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"","targetNode":"","isCollection":false,"kind":"SCALAR","label":"ISO 3166-1 alpha 3","node":"String","iconType":"csv"},"id":"stockSite.country.iso31661Alpha3","key":"stockSite.country.iso31661Alpha3","labelKey":"ISO 3166-1 alpha 3","labelPath":"Stock site > Country > ISO 3166-1 alpha 3"},"value2":"ZAF","key":"2","operator":"notEqual"},{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"3","value1":{"label":"ISO 3166-1 alpha 3","data":{"name":"iso31661Alpha3","title":"ISO 3166-1 alpha 3","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"","targetNode":"","isCollection":false,"kind":"SCALAR","label":"ISO 3166-1 alpha 3","node":"String","iconType":"csv"},"id":"stockSite.country.iso31661Alpha3","key":"stockSite.country.iso31661Alpha3","labelKey":"ISO 3166-1 alpha 3","labelPath":"Stock site > Country > ISO 3166-1 alpha 3"},"value2":"USA","key":"3","operator":"notEqual"},{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"4","value1":{"label":"City","data":{"name":"city","title":"City","canSort":false,"canFilter":false,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"","targetNode":"","isCollection":false,"kind":"SCALAR","label":"City","node":"String","iconType":"csv"},"id":"stockSite.primaryAddress.city","key":"stockSite.primaryAddress.city","labelKey":"City","labelPath":"Stock site > Primary address > City"},"value2":null,"key":"4","operator":"notEmpty"},{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"5","value1":{"label":"Postal code","data":{"name":"postcode","title":"Postal code","canSort":false,"canFilter":false,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"","targetNode":"","isCollection":false,"kind":"SCALAR","label":"Postal code","node":"String","iconType":"csv"},"id":"stockSite.primaryAddress.postcode","key":"stockSite.primaryAddress.postcode","labelKey":"Postal code","labelPath":"Stock site > Primary address > Postal code"},"value2":null,"key":"5","operator":"notEmpty"}]'>
                <!--{{#if ( and ( not ( eq stockSite.country.iso31661Alpha3 "GBR" ) ) ( not ( eq stockSite.country.iso31661Alpha3 "ZAF" ) ) ( not ( eq stockSite.country.iso31661Alpha3 "USA" ) ) ( not ( or ( eq stockSite.primaryAddress.city null ) ( eq stockSite.primaryAddress.city "" ) ( eq stockSite.primaryAddress.city undefined ) ) ) ( not ( or ( eq stockSite.primaryAddress.postcode null ) ( eq stockSite.primaryAddress.postcode "" ) ( eq stockSite.primaryAddress.postcode undefined ) ) ) )}}-->
                <div class="conditional-block-body">
                  <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"><span class="property"
                      data-property-display-label="Postal code" data-property-data-type="String"
                      data-property-name="stockSite.primaryAddress.postcode" data-property-data-format=""
                      data-property-parent-context="SalesShipment">{{stockSite.primaryAddress.postcode}}</span><span
                      class="property" data-property-display-label="City" data-property-data-type="String"
                      data-property-name="stockSite.primaryAddress.city" data-property-data-format=""
                      data-property-parent-context="SalesShipment">{{stockSite.primaryAddress.city}}</span></span>
                </div>
                <!--{{/if}}-->
                <div class="conditional-block-footer">&nbsp;</div>
              </section>
              <p>
                <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"><span class="property"
                    data-property-display-label="Name" data-property-data-type="String"
                    data-property-name="billToAddress.country.name" data-property-data-format=""
                    data-property-parent-context="SalesShipment">{{billToAddress.country.name}}</span></span>
              </p>
            </td>
            <td>
              <p>
                <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"><strong><span class="property"
                      data-property-display-label="Name" data-property-data-type="String"
                      data-property-name="shipToCustomer.name" data-property-data-format=""
                      data-property-parent-context="SalesShipment">{{shipToCustomer.name}}</span></strong></span>
              </p>
              <p>
                <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"><span class="property"
                    data-property-display-label="Address line 1" data-property-data-type="String"
                    data-property-name="shipToCustomer.primaryAddress.addressLine1" data-property-data-format=""
                    data-property-parent-context="SalesShipment">{{shipToCustomer.primaryAddress.addressLine1}}</span></span>
              </p>
              <section class="conditional-block"
                data-context-condition='[{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"1","value1":{"label":"Address line 2","data":{"name":"addressLine2","title":"Address line 2","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"addressLineDataType","targetNode":"","isCollection":false,"kind":"SCALAR","label":"Address line 2","node":"String","iconType":"csv"},"id":"billToAddress.addressLine2","key":"billToAddress.addressLine2","labelKey":"Address line 2","labelPath":"Bill-to address > Address line 2"},"value2":null,"key":"1","operator":"notEmpty"}]'>
                <!--{{#if ( not ( or ( eq billToAddress.addressLine2 null ) ( eq billToAddress.addressLine2 "" ) ( eq billToAddress.addressLine2 undefined ) ) )}}-->
                <div class="conditional-block-body">
                  <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"><span class="property"
                      data-property-display-label="Address line 2" data-property-data-type="String"
                      data-property-name="billToAddress.addressLine2" data-property-data-format=""
                      data-property-parent-context="SalesShipment">{{billToAddress.addressLine2}}</span></span>
                </div>
                <!--{{/if}}-->
                <div class="conditional-block-footer">&nbsp;</div>
              </section>
              <section class="conditional-block"
                data-context-condition='[{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"1","value1":{"label":"ISO 3166-1 alpha 3","data":{"name":"iso31661Alpha3","title":"ISO 3166-1 alpha 3","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"","targetNode":"","isCollection":false,"kind":"SCALAR","label":"ISO 3166-1 alpha 3","node":"String","iconType":"csv"},"id":"shipToCustomer.country.iso31661Alpha3","key":"shipToCustomer.country.iso31661Alpha3","labelKey":"ISO 3166-1 alpha 3","labelPath":"Ship-to customer > Country > ISO 3166-1 alpha 3"},"value2":"GBR","key":"1","operator":"equals"},{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"2","value1":{"label":"City","data":{"name":"city","title":"City","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"cityDataType","targetNode":"","isCollection":false,"kind":"SCALAR","label":"City","node":"String","iconType":"csv"},"id":"shipToAddress.city","key":"shipToAddress.city","labelKey":"City","labelPath":"Ship-to address > City"},"value2":null,"key":"2","operator":"notEmpty"},{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"3","value1":{"label":"Postal code","data":{"name":"postcode","title":"Postal code","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"postcodeDataType","targetNode":"","isCollection":false,"kind":"SCALAR","label":"Postal code","node":"String","iconType":"csv"},"id":"shipToAddress.postcode","key":"shipToAddress.postcode","labelKey":"Postal code","labelPath":"Ship-to address > Postal code"},"value2":null,"key":"3","operator":"notEmpty"}]'>
                <!--{{#if ( and ( eq shipToCustomer.country.iso31661Alpha3 "GBR" ) ( not ( or ( eq shipToAddress.city null ) ( eq shipToAddress.city "" ) ( eq shipToAddress.city undefined ) ) ) ( not ( or ( eq shipToAddress.postcode null ) ( eq shipToAddress.postcode "" ) ( eq shipToAddress.postcode undefined ) ) ) )}}-->
                <div class="conditional-block-body">
                  <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"><span class="property"
                      data-property-display-label="City" data-property-data-type="String"
                      data-property-name="shipToAddress.city" data-property-data-format=""
                      data-property-parent-context="SalesShipment">{{shipToAddress.city}}</span><span class="property"
                      data-property-display-label="Postal code" data-property-data-type="String"
                      data-property-name="shipToAddress.postcode" data-property-data-format=""
                      data-property-parent-context="SalesShipment">{{shipToAddress.postcode}}</span></span>
                </div>
                <!--{{/if}}-->
                <div class="conditional-block-footer">&nbsp;</div>
              </section>
              <section class="conditional-block"
                data-context-condition='[{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"1","value1":{"label":"ISO 3166-1 alpha 3","data":{"name":"iso31661Alpha3","title":"ISO 3166-1 alpha 3","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"","targetNode":"","isCollection":false,"kind":"SCALAR","label":"ISO 3166-1 alpha 3","node":"String","iconType":"csv"},"id":"shipToAddress.country.iso31661Alpha3","key":"shipToAddress.country.iso31661Alpha3","labelKey":"ISO 3166-1 alpha 3","labelPath":"Ship-to address > Country > ISO 3166-1 alpha 3"},"value2":"ZAF","key":"1","operator":"equals"},{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"2","value1":{"label":"City","data":{"name":"city","title":"City","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"cityDataType","targetNode":"","isCollection":false,"kind":"SCALAR","label":"City","node":"String","iconType":"csv"},"id":"shipToAddress.city","key":"shipToAddress.city","labelKey":"City","labelPath":"Ship-to address > City"},"value2":null,"key":"2","operator":"empty"},{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"3","value1":{"label":"Postal code","data":{"name":"postcode","title":"Postal code","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"postcodeDataType","targetNode":"","isCollection":false,"kind":"SCALAR","label":"Postal code","node":"String","iconType":"csv"},"id":"shipToAddress.postcode","key":"shipToAddress.postcode","labelKey":"Postal code","labelPath":"Ship-to address > Postal code"},"value2":null,"key":"3","operator":"notEmpty"}]'>
                <!--{{#if ( and ( eq shipToAddress.country.iso31661Alpha3 "ZAF" ) ( or ( eq shipToAddress.city null ) ( eq shipToAddress.city "" ) ( eq shipToAddress.city undefined ) ) ( not ( or ( eq shipToAddress.postcode null ) ( eq shipToAddress.postcode "" ) ( eq shipToAddress.postcode undefined ) ) ) )}}-->
                <div class="conditional-block-body">
                  <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"><span class="property"
                      data-property-display-label="City" data-property-data-type="String"
                      data-property-name="shipToAddress.city" data-property-data-format=""
                      data-property-parent-context="SalesShipment">{{shipToAddress.city}}</span><span class="property"
                      data-property-display-label="Postal code" data-property-data-type="String"
                      data-property-name="shipToAddress.postcode" data-property-data-format=""
                      data-property-parent-context="SalesShipment">{{shipToAddress.postcode}}</span></span>
                </div>
                <!--{{/if}}-->
                <div class="conditional-block-footer">&nbsp;</div>
              </section>
              <section class="conditional-block"
                data-context-condition='[{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"1","value1":{"label":"ISO 3166-1 alpha 3","data":{"name":"iso31661Alpha3","title":"ISO 3166-1 alpha 3","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"","targetNode":"","isCollection":false,"kind":"SCALAR","label":"ISO 3166-1 alpha 3","node":"String","iconType":"csv"},"id":"shipToAddress.country.iso31661Alpha3","key":"shipToAddress.country.iso31661Alpha3","labelKey":"ISO 3166-1 alpha 3","labelPath":"Ship-to address > Country > ISO 3166-1 alpha 3"},"value2":"USA","key":"1","operator":"equals"},{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"2","value1":{"label":"City","data":{"name":"city","title":"City","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"cityDataType","targetNode":"","isCollection":false,"kind":"SCALAR","label":"City","node":"String","iconType":"csv"},"id":"shipToAddress.city","key":"shipToAddress.city","labelKey":"City","labelPath":"Ship-to address > City"},"value2":null,"key":"2","operator":"notEmpty"},{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"3","value1":{"label":"Postal code","data":{"name":"postcode","title":"Postal code","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"postcodeDataType","targetNode":"","isCollection":false,"kind":"SCALAR","label":"Postal code","node":"String","iconType":"csv"},"id":"shipToAddress.postcode","key":"shipToAddress.postcode","labelKey":"Postal code","labelPath":"Ship-to address > Postal code"},"value2":null,"key":"3","operator":"notEmpty"}]'>
                <!--{{#if ( and ( eq shipToAddress.country.iso31661Alpha3 "USA" ) ( not ( or ( eq shipToAddress.city null ) ( eq shipToAddress.city "" ) ( eq shipToAddress.city undefined ) ) ) ( not ( or ( eq shipToAddress.postcode null ) ( eq shipToAddress.postcode "" ) ( eq shipToAddress.postcode undefined ) ) ) )}}-->
                <div class="conditional-block-body">
                  <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"><span class="property"
                      data-property-display-label="City" data-property-data-type="String"
                      data-property-name="shipToAddress.city" data-property-data-format=""
                      data-property-parent-context="SalesShipment">{{shipToAddress.city}}</span><span class="property"
                      data-property-display-label="Postal code" data-property-data-type="String"
                      data-property-name="shipToAddress.postcode" data-property-data-format=""
                      data-property-parent-context="SalesShipment">{{shipToAddress.postcode}}</span></span>
                </div>
                <!--{{/if}}-->
                <div class="conditional-block-footer">&nbsp;</div>
              </section>
              <section class="conditional-block"
                data-context-condition='[{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"1","value1":{"label":"ISO 3166-1 alpha 3","data":{"name":"iso31661Alpha3","title":"ISO 3166-1 alpha 3","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"","targetNode":"","isCollection":false,"kind":"SCALAR","label":"ISO 3166-1 alpha 3","node":"String","iconType":"csv"},"id":"stockSite.country.iso31661Alpha3","key":"stockSite.country.iso31661Alpha3","labelKey":"ISO 3166-1 alpha 3","labelPath":"Stock site > Country > ISO 3166-1 alpha 3"},"value2":"GBR","key":"1","operator":"notEqual"},{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"2","value1":{"label":"ISO 3166-1 alpha 3","data":{"name":"iso31661Alpha3","title":"ISO 3166-1 alpha 3","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"","targetNode":"","isCollection":false,"kind":"SCALAR","label":"ISO 3166-1 alpha 3","node":"String","iconType":"csv"},"id":"stockSite.country.iso31661Alpha3","key":"stockSite.country.iso31661Alpha3","labelKey":"ISO 3166-1 alpha 3","labelPath":"Stock site > Country > ISO 3166-1 alpha 3"},"value2":"ZAF","key":"2","operator":"notEqual"},{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"3","value1":{"label":"ISO 3166-1 alpha 3","data":{"name":"iso31661Alpha3","title":"ISO 3166-1 alpha 3","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"","targetNode":"","isCollection":false,"kind":"SCALAR","label":"ISO 3166-1 alpha 3","node":"String","iconType":"csv"},"id":"stockSite.country.iso31661Alpha3","key":"stockSite.country.iso31661Alpha3","labelKey":"ISO 3166-1 alpha 3","labelPath":"Stock site > Country > ISO 3166-1 alpha 3"},"value2":"USA","key":"3","operator":"notEqual"},{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"4","value1":{"label":"City","data":{"name":"city","title":"City","canSort":false,"canFilter":false,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"","targetNode":"","isCollection":false,"kind":"SCALAR","label":"City","node":"String","iconType":"csv"},"id":"stockSite.primaryAddress.city","key":"stockSite.primaryAddress.city","labelKey":"City","labelPath":"Stock site > Primary address > City"},"value2":null,"key":"4","operator":"notEmpty"},{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"5","value1":{"label":"Postal code","data":{"name":"postcode","title":"Postal code","canSort":false,"canFilter":false,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"","targetNode":"","isCollection":false,"kind":"SCALAR","label":"Postal code","node":"String","iconType":"csv"},"id":"stockSite.primaryAddress.postcode","key":"stockSite.primaryAddress.postcode","labelKey":"Postal code","labelPath":"Stock site > Primary address > Postal code"},"value2":null,"key":"5","operator":"notEmpty"}]'>
                <!--{{#if ( and ( not ( eq stockSite.country.iso31661Alpha3 "GBR" ) ) ( not ( eq stockSite.country.iso31661Alpha3 "ZAF" ) ) ( not ( eq stockSite.country.iso31661Alpha3 "USA" ) ) ( not ( or ( eq stockSite.primaryAddress.city null ) ( eq stockSite.primaryAddress.city "" ) ( eq stockSite.primaryAddress.city undefined ) ) ) ( not ( or ( eq stockSite.primaryAddress.postcode null ) ( eq stockSite.primaryAddress.postcode "" ) ( eq stockSite.primaryAddress.postcode undefined ) ) ) )}}-->
                <div class="conditional-block-body">
                  <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"><span class="property"
                      data-property-display-label="Postal code" data-property-data-type="String"
                      data-property-name="stockSite.primaryAddress.postcode" data-property-data-format=""
                      data-property-parent-context="SalesShipment">{{stockSite.primaryAddress.postcode}}</span><span
                      class="property" data-property-display-label="City" data-property-data-type="String"
                      data-property-name="stockSite.primaryAddress.city" data-property-data-format=""
                      data-property-parent-context="SalesShipment">{{stockSite.primaryAddress.city}}</span></span>
                </div>
                <!--{{/if}}-->
                <div class="conditional-block-footer">&nbsp;</div>
              </section>
              <p>
                <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"><span class="property"
                    data-property-display-label="Name" data-property-data-type="String"
                    data-property-name="shipToAddress.country.name" data-property-data-format=""
                    data-property-parent-context="SalesShipment">{{shipToAddress.country.name}}</span></span>
              </p>
            </td>
          </tr>
        </tbody>
      </table>
    </figure>
    <table class="query-table" data-context-object-type="SalesShipmentLine" data-context-object-path="lines.query.edges"
      data-context-filter="[]"
      data-context-list-order='{"quantityInStockUnit":"ascending","item.id":"ascending","itemDescription":"ascending","unit.name":"ascending","quantity":"ascending"}'
      data-alias="DxKaGqbS">
      <thead class="query-table-head">
        <tr class="query-table-row">
          <td class="query-table-cell"
            style="background-color: #0000001a; border: 1px solid #dfdfdf; padding: 2px; vertical-align: top">
            <p>
              <span style="color: #198e59; font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"><strong>{{
                  translatedContent "4049d979b8e6b7d78194e96c3208a5a5" }}</strong></span>
            </p>
          </td>
          <td class="query-table-cell"
            style="background-color: #0000001a; border: 1px solid #dfdfdf; padding: 2px; vertical-align: top">
            <p>
              <span style="color: #198e59; font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"><strong>{{
                  translatedContent "7d74f3b92b19da5e606d737d339a9679" }}</strong></span>
            </p>
          </td>
          <td class="query-table-cell"
            style="background-color: #0000001a; border: 1px solid #dfdfdf; padding: 2px; vertical-align: top">
            <p>
              <span style="color: #198e59; font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"><strong>&nbsp;{{
                  translatedContent "b5a7adde1af5c87d7fd797b6245c2a39" }}</strong></span>
            </p>
          </td>
          <td class="query-table-cell"
            style="background-color: #0000001a; border: 1px solid #dfdfdf; padding: 2px; vertical-align: top">
            <p>
              <span style="color: #198e59; font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"><strong>&nbsp;{{
                  translatedContent "694e8d1f2ee056f98ee488bdc4982d73" }}</strong></span>
            </p>
          </td>
          <td class="query-table-cell"
            style="background-color: #0000001a; border: 1px solid #dfdfdf; padding: 2px; vertical-align: top">
            <p>
              <span style="color: #198e59; font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"><strong>&nbsp;{{
                  translatedContent "19c562a36aeb455d09534f93b4f5236f" }}&nbsp;</strong></span>
            </p>
          </td>
        </tr>
      </thead>
      <tbody class="query-table-body">
        <!--{{#each DxKaGqbS.query.edges}}{{#with node}}-->
        <tr class="query-table-row">
          <td class="query-table-cell e-right-align"
            style="border: 1px solid #dfdfdf; height: 14px; padding: 2px; text-align: left; vertical-align: top">
            <section class="record-context" data-context-object-type="SalesOrderLineToSalesShipmentLine"
              data-context-object-path="salesOrderLines.query.edges.0.node" data-context-filter="[]"
              data-context-list-order="{}" data-alias="VyjXdwRu">
              <!--{{#with VyjXdwRu.query.edges.0.node}}-->
              <div class="report-context-body">
                <p>
                  <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"><span class="property"
                      data-property-display-label="Document number" data-property-data-type="String"
                      data-property-name="linkedDocument.documentNumber" data-property-data-format=""
                      data-property-parent-context="SalesOrderLineToSalesShipmentLine">{{linkedDocument.documentNumber}}</span></span>
                </p>
              </div>
              <!--{{/with}}--><span class="report-context-footer">&nbsp;</span>
            </section>
          </td>
          <td class="query-table-cell"
            style="border: 1px solid #dfdfdf; height: 14px; padding: 2px; text-align: left; vertical-align: top">
            <p>
              <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"><span class="property"
                  data-property-display-label="Item ID" data-property-data-type="String" data-property-name="item.id"
                  data-property-data-format=""
                  data-property-parent-context="SalesShipmentLine">{{item.id}}</span></span>
            </p>
          </td>
          <td class="query-table-cell"
            style="border: 1px solid #dfdfdf; height: 14px; padding: 2px; text-align: left; vertical-align: top">
            <p>
              <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"><span class="property"
                  data-property-display-label="Item description" data-property-data-type="String"
                  data-property-name="itemDescription" data-property-data-format=""
                  data-property-parent-context="SalesShipmentLine">{{itemDescription}}</span></span>
            </p>
          </td>
          <td class="query-table-cell"
            style="border: 1px solid #dfdfdf; height: 14px; padding: 2px; text-align: left; vertical-align: top">
            <p>
              <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"><span class="property"
                  data-property-display-label="Quantity in sales unit" data-property-data-type="Decimal"
                  data-property-name="quantity" data-property-data-format="2"
                  data-property-parent-context="SalesShipmentLine">{{formatNumber quantity 2}}</span></span>
            </p>
          </td>
          <td class="query-table-cell e-right-align"
            style="border: 1px solid #dfdfdf; height: 14px; padding: 2px; text-align: left; vertical-align: top">
            <p>
              <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"><span class="property"
                  data-property-display-label="Name" data-property-data-type="String" data-property-name="unit.name"
                  data-property-data-format=""
                  data-property-parent-context="SalesShipmentLine">{{unit.name}}</span></span>
            </p>
          </td>
        </tr>
        <tr class="query-table-row" data-hidden="1">
          <td class="query-table-cell" colspan="5">
            <p>&nbsp;</p>
          </td>
        </tr>
        <!--{{/with}}{{/each}}-->
        <tr class="query-table-row" data-hidden="1">
          <td class="query-table-cell" colspan="5">
            <p>&nbsp;</p>
          </td>
        </tr>
      </tbody>
      <tfoot class="query-table-footer">
        <tr class="query-table-row">
          <td class="query-table-cell">
            <p>&nbsp;</p>
          </td>
          <td class="query-table-cell">
            <p>&nbsp;</p>
          </td>
          <td class="query-table-cell">
            <p>&nbsp;</p>
          </td>
          <td class="query-table-cell">
            <p>&nbsp;</p>
          </td>
          <td class="query-table-cell">
            <p>&nbsp;</p>
          </td>
        </tr>
      </tfoot>
    </table>
    <section class="unbreakable-block">
      <div class="unbreakable-block-body">
        <figure class="table">
          <table>
            <tbody>
              <tr>
                <td style="text-align: center">
                  <section class="conditional-block"
                    data-context-condition='[{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"1","value1":{"label":"NAF","data":{"name":"naf","title":"NAF","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"description","targetNode":"","isCollection":false,"kind":"SCALAR","label":"NAF","node":"String","iconType":"csv"},"id":"stockSite.legalCompany.naf","key":"stockSite.legalCompany.naf","labelKey":"NAF","labelPath":"Stock site > Legal company > NAF"},"value2":null,"key":"1","operator":"notEmpty"}]'>
                    <!--{{#if ( not ( or ( eq stockSite.legalCompany.naf null ) ( eq stockSite.legalCompany.naf "" ) ( eq stockSite.legalCompany.naf undefined ) ) )}}-->
                    <div class="conditional-block-body">
                      <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"><strong><span
                            class="property" data-property-display-label="Legal form" data-property-data-type="Enum"
                            data-property-name="stockSite.legalCompany.legalForm" data-property-data-format=""
                            data-property-parent-context="SalesShipment">{{stockSite.legalCompany.legalForm}}</span><span
                            class="property" data-property-display-label="Name" data-property-data-type="String"
                            data-property-name="stockSite.legalCompany.name" data-property-data-format=""
                            data-property-parent-context="SalesShipment">{{stockSite.legalCompany.name}}</span></strong><span
                          class="property" data-property-display-label="RCS" data-property-data-type="String"
                          data-property-name="stockSite.legalCompany.rcs" data-property-data-format=""
                          data-property-parent-context="SalesShipment">{{stockSite.legalCompany.rcs}}</span>{{
                        translatedContent "7137a85d9b8d9e6d09f47767d2ca31ee" }}<span class="property"
                          data-property-display-label="NAF" data-property-data-type="String"
                          data-property-name="stockSite.legalCompany.naf" data-property-data-format=""
                          data-property-parent-context="SalesShipment">{{stockSite.legalCompany.naf}}</span></span>
                    </div>
                    <!--{{/if}}-->
                    <div class="conditional-block-footer">&nbsp;</div>
                  </section>
                  <section class="conditional-block"
                    data-context-condition='[{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"1","value1":{"label":"NAF","data":{"name":"naf","title":"NAF","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"description","targetNode":"","isCollection":false,"kind":"SCALAR","label":"NAF","node":"String","iconType":"csv"},"id":"stockSite.legalCompany.naf","key":"stockSite.legalCompany.naf","labelKey":"NAF","labelPath":"Stock site > Legal company > NAF"},"value2":null,"key":"1","operator":"empty"}]'>
                    <!--{{#if ( or ( eq stockSite.legalCompany.naf null ) ( eq stockSite.legalCompany.naf "" ) ( eq stockSite.legalCompany.naf undefined ) )}}-->
                    <div class="conditional-block-body">
                      <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"><strong><span
                            class="property" data-property-display-label="Legal form" data-property-data-type="Enum"
                            data-property-name="stockSite.legalCompany.legalForm" data-property-data-format=""
                            data-property-parent-context="SalesShipment">{{stockSite.legalCompany.legalForm}}</span><span
                            class="property" data-property-display-label="Name" data-property-data-type="String"
                            data-property-name="stockSite.legalCompany.name" data-property-data-format=""
                            data-property-parent-context="SalesShipment">{{stockSite.legalCompany.name}}</span></strong><span
                          class="property" data-property-display-label="NAF" data-property-data-type="String"
                          data-property-name="stockSite.legalCompany.naf" data-property-data-format=""
                          data-property-parent-context="SalesShipment">{{stockSite.legalCompany.naf}}</span></span>
                    </div>
                    <!--{{/if}}-->
                    <div class="conditional-block-footer">&nbsp;</div>
                  </section>
                  <section class="record-context" data-context-object-type="CompanyAddress"
                    data-context-object-path="stockSite.legalCompany.addresses.query.edges.0.node"
                    data-context-filter="[]" data-context-list-order="{}" data-alias="ZiTboSOR">
                    <!--{{#with ZiTboSOR.legalCompany.addresses.query.edges.0.node}}-->
                    <div class="report-context-body">
                      <section class="conditional-block"
                        data-context-condition='[{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"1","value1":{"label":"Address line 1","data":{"name":"addressLine1","title":"Address line 1","canSort":false,"canFilter":false,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"","targetNode":"","isCollection":false,"kind":"SCALAR","label":"Address line 1","node":"String","iconType":"csv"},"id":"addressLine1","key":"addressLine1","labelKey":"Address line 1","labelPath":"Address line 1"},"value2":null,"key":"1","operator":"notEmpty"},{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"2","value1":{"label":"ISO 3166-1 alpha 3","data":{"name":"iso31661Alpha3","title":"ISO 3166-1 alpha 3","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"","targetNode":"","isCollection":false,"kind":"SCALAR","label":"ISO 3166-1 alpha 3","node":"String","iconType":"csv"},"id":"country.iso31661Alpha3","key":"country.iso31661Alpha3","labelKey":"ISO 3166-1 alpha 3","labelPath":"Country > ISO 3166-1 alpha 3"},"value2":"GBR","key":"2","operator":"equals"},{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"3","value1":{"label":"City","data":{"name":"city","title":"City","canSort":false,"canFilter":false,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"","targetNode":"","isCollection":false,"kind":"SCALAR","label":"City","node":"String","iconType":"csv"},"id":"city","key":"city","labelKey":"City","labelPath":"City"},"value2":null,"key":"3","operator":"notEmpty"},{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"4","value1":{"label":"Postal code","data":{"name":"postcode","title":"Postal code","canSort":false,"canFilter":false,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"","targetNode":"","isCollection":false,"kind":"SCALAR","label":"Postal code","node":"String","iconType":"csv"},"id":"postcode","key":"postcode","labelKey":"Postal code","labelPath":"Postal code"},"value2":null,"key":"4","operator":"notEmpty"},{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"5","value1":{"label":"Name","data":{"name":"name","title":"Name","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"localizedCountryPropertyDataType","targetNode":"","isCollection":false,"kind":"SCALAR","label":"Name","node":"String","iconType":"csv"},"id":"country.name","key":"country.name","labelKey":"Name","labelPath":"Country > Name"},"value2":null,"key":"5","operator":"notEmpty"}]'>
                        <!--{{#if ( and ( not ( or ( eq addressLine1 null ) ( eq addressLine1 "" ) ( eq addressLine1 undefined ) ) ) ( eq country.iso31661Alpha3 "GBR" ) ( not ( or ( eq city null ) ( eq city "" ) ( eq city undefined ) ) ) ( not ( or ( eq postcode null ) ( eq postcode "" ) ( eq postcode undefined ) ) ) ( not ( or ( eq country.name null ) ( eq country.name "" ) ( eq country.name undefined ) ) ) )}}-->
                        <div class="conditional-block-body">
                          <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"><span
                              class="property" data-property-display-label="Address line 1"
                              data-property-data-type="String" data-property-name="addressLine1"
                              data-property-data-format=""
                              data-property-parent-context="CompanyAddress">{{addressLine1}}</span><span
                              class="property" data-property-display-label="Address line 2"
                              data-property-data-type="String" data-property-name="addressLine2"
                              data-property-data-format=""
                              data-property-parent-context="CompanyAddress">{{addressLine2}}</span><span
                              class="property" data-property-display-label="City" data-property-data-type="String"
                              data-property-name="city" data-property-data-format=""
                              data-property-parent-context="CompanyAddress">{{city}}</span><span class="property"
                              data-property-display-label="Postal code" data-property-data-type="String"
                              data-property-name="postcode" data-property-data-format=""
                              data-property-parent-context="CompanyAddress">{{postcode}}</span><span class="property"
                              data-property-display-label="Name" data-property-data-type="String"
                              data-property-name="country.name" data-property-data-format=""
                              data-property-parent-context="CompanyAddress">{{country.name}}</span></span>
                        </div>
                        <!--{{/if}}-->
                        <div class="conditional-block-footer">&nbsp;</div>
                      </section>
                      <section class="conditional-block"
                        data-context-condition='[{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"1","value1":{"label":"ISO 3166-1 alpha 3","data":{"name":"iso31661Alpha3","title":"ISO 3166-1 alpha 3","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"","targetNode":"","isCollection":false,"kind":"SCALAR","label":"ISO 3166-1 alpha 3","node":"String","iconType":"csv"},"id":"country.iso31661Alpha3","key":"country.iso31661Alpha3","labelKey":"ISO 3166-1 alpha 3","labelPath":"Country > ISO 3166-1 alpha 3"},"value2":"USA","key":"1","operator":"notEqual"},{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"2","value1":{"label":"ISO 3166-1 alpha 3","data":{"name":"iso31661Alpha3","title":"ISO 3166-1 alpha 3","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"","targetNode":"","isCollection":false,"kind":"SCALAR","label":"ISO 3166-1 alpha 3","node":"String","iconType":"csv"},"id":"address.country.iso31661Alpha3","key":"address.country.iso31661Alpha3","labelKey":"ISO 3166-1 alpha 3","labelPath":"Address > Country > ISO 3166-1 alpha 3"},"value2":"ZAF","key":"2","operator":"notEqual"}]'>
                        <!--{{#if ( and ( not ( eq country.iso31661Alpha3 "USA" ) ) ( not ( eq address.country.iso31661Alpha3 "ZAF" ) ) )}}-->
                        <div class="conditional-block-body">
                          <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"><span
                              class="property" data-property-display-label="Address line 1"
                              data-property-data-type="String" data-property-name="addressLine1"
                              data-property-data-format=""
                              data-property-parent-context="CompanyAddress">{{addressLine1}}</span><span
                              class="property" data-property-display-label="Address line 2"
                              data-property-data-type="String" data-property-name="addressLine2"
                              data-property-data-format=""
                              data-property-parent-context="CompanyAddress">{{addressLine2}}</span><span
                              class="property" data-property-display-label="City" data-property-data-type="String"
                              data-property-name="city" data-property-data-format=""
                              data-property-parent-context="CompanyAddress">{{city}}</span><span class="property"
                              data-property-display-label="Postal code" data-property-data-type="String"
                              data-property-name="postcode" data-property-data-format=""
                              data-property-parent-context="CompanyAddress">{{postcode}}</span><span class="property"
                              data-property-display-label="Name" data-property-data-type="String"
                              data-property-name="country.name" data-property-data-format=""
                              data-property-parent-context="CompanyAddress">{{country.name}}</span></span>
                        </div>
                        <!--{{/if}}-->
                        <div class="conditional-block-footer">&nbsp;</div>
                      </section>
                      <section class="conditional-block"
                        data-context-condition='[{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"1","value1":{"label":"Address line 1","data":{"name":"addressLine1","title":"Address line 1","canSort":false,"canFilter":false,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"","targetNode":"","isCollection":false,"kind":"SCALAR","label":"Address line 1","node":"String","iconType":"csv"},"id":"addressLine1","key":"addressLine1","labelKey":"Address line 1","labelPath":"Address line 1"},"value2":null,"key":"1","operator":"notEmpty"},{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"2","value1":{"label":"ISO 3166-1 alpha 3","data":{"name":"iso31661Alpha3","title":"ISO 3166-1 alpha 3","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"","targetNode":"","isCollection":false,"kind":"SCALAR","label":"ISO 3166-1 alpha 3","node":"String","iconType":"csv"},"id":"country.iso31661Alpha3","key":"country.iso31661Alpha3","labelKey":"ISO 3166-1 alpha 3","labelPath":"Country > ISO 3166-1 alpha 3"},"value2":"ZAF","key":"2","operator":"equals"},{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"3","value1":{"label":"City","data":{"name":"city","title":"City","canSort":false,"canFilter":false,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"","targetNode":"","isCollection":false,"kind":"SCALAR","label":"City","node":"String","iconType":"csv"},"id":"city","key":"city","labelKey":"City","labelPath":"City"},"value2":null,"key":"3","operator":"notEmpty"},{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"4","value1":{"label":"Postal code","data":{"name":"postcode","title":"Postal code","canSort":false,"canFilter":false,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"","targetNode":"","isCollection":false,"kind":"SCALAR","label":"Postal code","node":"String","iconType":"csv"},"id":"postcode","key":"postcode","labelKey":"Postal code","labelPath":"Postal code"},"value2":null,"key":"4","operator":"notEmpty"},{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"5","value1":{"label":"Name","data":{"name":"name","title":"Name","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"localizedCountryPropertyDataType","targetNode":"","isCollection":false,"kind":"SCALAR","label":"Name","node":"String","iconType":"csv"},"id":"country.name","key":"country.name","labelKey":"Name","labelPath":"Country > Name"},"value2":null,"key":"5","operator":"notEmpty"}]'>
                        <!--{{#if ( and ( not ( or ( eq addressLine1 null ) ( eq addressLine1 "" ) ( eq addressLine1 undefined ) ) ) ( eq country.iso31661Alpha3 "ZAF" ) ( not ( or ( eq city null ) ( eq city "" ) ( eq city undefined ) ) ) ( not ( or ( eq postcode null ) ( eq postcode "" ) ( eq postcode undefined ) ) ) ( not ( or ( eq country.name null ) ( eq country.name "" ) ( eq country.name undefined ) ) ) )}}-->
                        <div class="conditional-block-body">
                          <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"><span
                              class="property" data-property-display-label="Address line 1"
                              data-property-data-type="String" data-property-name="addressLine1"
                              data-property-data-format=""
                              data-property-parent-context="CompanyAddress">{{addressLine1}}</span><span
                              class="property" data-property-display-label="Address line 2"
                              data-property-data-type="String" data-property-name="addressLine2"
                              data-property-data-format=""
                              data-property-parent-context="CompanyAddress">{{addressLine2}}</span><span
                              class="property" data-property-display-label="City" data-property-data-type="String"
                              data-property-name="city" data-property-data-format=""
                              data-property-parent-context="CompanyAddress">{{city}}</span><span class="property"
                              data-property-display-label="Postal code" data-property-data-type="String"
                              data-property-name="postcode" data-property-data-format=""
                              data-property-parent-context="CompanyAddress">{{postcode}}</span><span class="property"
                              data-property-display-label="Name" data-property-data-type="String"
                              data-property-name="country.name" data-property-data-format=""
                              data-property-parent-context="CompanyAddress">{{country.name}}</span></span>
                        </div>
                        <!--{{/if}}-->
                        <div class="conditional-block-footer">&nbsp;</div>
                      </section>
                      <section class="conditional-block"
                        data-context-condition='[{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"1","value1":{"label":"Address line 1","data":{"name":"addressLine1","title":"Address line 1","canSort":false,"canFilter":false,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"","targetNode":"","isCollection":false,"kind":"SCALAR","label":"Address line 1","node":"String","iconType":"csv"},"id":"addressLine1","key":"addressLine1","labelKey":"Address line 1","labelPath":"Address line 1"},"value2":null,"key":"1","operator":"notEmpty"},{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"2","value1":{"label":"ISO 3166-1 alpha 3","data":{"name":"iso31661Alpha3","title":"ISO 3166-1 alpha 3","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"","targetNode":"","isCollection":false,"kind":"SCALAR","label":"ISO 3166-1 alpha 3","node":"String","iconType":"csv"},"id":"country.iso31661Alpha3","key":"country.iso31661Alpha3","labelKey":"ISO 3166-1 alpha 3","labelPath":"Country > ISO 3166-1 alpha 3"},"value2":"USA","key":"2","operator":"equals"},{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"3","value1":{"label":"City","data":{"name":"city","title":"City","canSort":false,"canFilter":false,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"","targetNode":"","isCollection":false,"kind":"SCALAR","label":"City","node":"String","iconType":"csv"},"id":"city","key":"city","labelKey":"City","labelPath":"City"},"value2":null,"key":"3","operator":"notEmpty"},{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"4","value1":{"label":"Postal code","data":{"name":"postcode","title":"Postal code","canSort":false,"canFilter":false,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"","targetNode":"","isCollection":false,"kind":"SCALAR","label":"Postal code","node":"String","iconType":"csv"},"id":"postcode","key":"postcode","labelKey":"Postal code","labelPath":"Postal code"},"value2":null,"key":"4","operator":"notEmpty"},{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"5","value1":{"label":"Name","data":{"name":"name","title":"Name","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"localizedCountryPropertyDataType","targetNode":"","isCollection":false,"kind":"SCALAR","label":"Name","node":"String","iconType":"csv"},"id":"country.name","key":"country.name","labelKey":"Name","labelPath":"Country > Name"},"value2":null,"key":"5","operator":"notEmpty"}]'>
                        <!--{{#if ( and ( not ( or ( eq addressLine1 null ) ( eq addressLine1 "" ) ( eq addressLine1 undefined ) ) ) ( eq country.iso31661Alpha3 "USA" ) ( not ( or ( eq city null ) ( eq city "" ) ( eq city undefined ) ) ) ( not ( or ( eq postcode null ) ( eq postcode "" ) ( eq postcode undefined ) ) ) ( not ( or ( eq country.name null ) ( eq country.name "" ) ( eq country.name undefined ) ) ) )}}-->
                        <div class="conditional-block-body">
                          <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"><span
                              class="property" data-property-display-label="Address line 1"
                              data-property-data-type="String" data-property-name="addressLine1"
                              data-property-data-format=""
                              data-property-parent-context="CompanyAddress">{{addressLine1}}</span><span
                              class="property" data-property-display-label="Address line 2"
                              data-property-data-type="String" data-property-name="addressLine2"
                              data-property-data-format=""
                              data-property-parent-context="CompanyAddress">{{addressLine2}}</span><span
                              class="property" data-property-display-label="City" data-property-data-type="String"
                              data-property-name="city" data-property-data-format=""
                              data-property-parent-context="CompanyAddress">{{city}}</span><span class="property"
                              data-property-display-label="Postal code" data-property-data-type="String"
                              data-property-name="postcode" data-property-data-format=""
                              data-property-parent-context="CompanyAddress">{{postcode}}</span><span class="property"
                              data-property-display-label="Name" data-property-data-type="String"
                              data-property-name="country.name" data-property-data-format=""
                              data-property-parent-context="CompanyAddress">{{country.name}}</span></span>
                        </div>
                        <!--{{/if}}-->
                        <div class="conditional-block-footer">&nbsp;</div>
                      </section>
                    </div>
                    <!--{{/with}}--><span class="report-context-footer">&nbsp;</span>
                  </section>
                </td>
              </tr>
            </tbody>
          </table>
        </figure>
        <section class="conditional-block"
          data-context-condition='[{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"1","value1":{"label":"External note","data":{"name":"isExternalNote","title":"External note","canSort":true,"canFilter":true,"type":"Boolean","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"","targetNode":"","isCollection":false,"kind":"SCALAR","label":"External note","node":"Boolean","iconType":"csv"},"id":"isExternalNote","key":"isExternalNote","labelKey":"External note","labelPath":"External note"},"value2":"true","key":"1","operator":"equals"}]'>
          <!--{{#if ( eq isExternalNote true )}}-->
          <div class="conditional-block-body">
            <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"><strong>{{ translatedContent
                "fc9d3df613063ee57906c461320744e2" }}</strong></span><span class="property"
              data-property-display-label="External note" data-property-data-type="Boolean"
              data-property-name="isExternalNote" data-property-data-format=""
              data-property-parent-context="SalesShipment">{{isExternalNote}}</span>
          </div>
          <!--{{/if}}-->
          <div class="conditional-block-footer">&nbsp;</div>
        </section>
      </div>
    </section>
  </div>
  <!--{{/with}}--><span class="report-context-footer">&nbsp;</span>
</section>
