{
  xtremSales {
    salesShipment {
      query(
        filter: "{status: {_ne:'shipped'}, _id:'{{shipment}}'}"
      ) {
        edges {
          node {
            number
            shipToCustomer{
              id
              name
            }
            shipToAddress {
              addressLine1
              addressLine2
              city
              postcode
              region
              country {
                name
                iso31661Alpha3
              }
            }
            date
            deliveryMode {
              description
            }

            lines {
              query (first: 500) {
                edges {
                  node {
                    salesOrderLines{
                      query{
                        edges {
                          node {
                            linkedDocument{
                              document {
                                number
                              }
                            }
                          }
                        }
                      }
                    }
                    item {
                      id
                    }
                    itemDescription
                    quantity
                    unit {
                      name
                    }
                    quantityInStockUnit
                    stockUnit {
                      name
                    }
                    stockAllocations {
                      queryAggregate {
                        edges {
                          node {
                            group {
                              documentLine {
                                _id
                              }
                            }
                            values {
                              quantityInStockUnit {
                                sum
                              }
                            }
                          }
                        }
                      }
                    }
                    stockAllocations {
                      query {
                        edges {
                          node {
                            quantityInStockUnit
                            serialNumberRanges
                            stockRecord {
                              location {
                                name
                              }
                              lot {
                                id
                              }
                            }
                          }
                        }
                      }
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
  }
}
