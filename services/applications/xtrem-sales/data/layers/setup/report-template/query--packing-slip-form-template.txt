query {
    UruauABL: xtremSales {
        salesShipment {
            query (filter: "{\"_id\":{\"_and\":[{\"_eq\":\"{{shipment}}\"}]}}", orderBy: "{}", first: 1) {
                edges {
                    node {
                        stockSite {
                            name
                            primaryAddress {
                                addressLine1
                                addressLine2
                                city
                                postcode
                            }
                            country {
                                iso31661Alpha3
                                name
                            }
                            siret
                            taxIdNumber
                            legalCompany {
                                naf
                                legalForm
                                name
                                rcs
                            }
                        }
                        number
                        date
                        billToCustomer {
                            name
                            id
                            siret
                            taxIdNumber
                        }
                        deliveryDate
                        deliveryMode {
                            name
                        }
                        incoterm {
                            name
                        }
                        trackingNumber
                        billToAddress {
                            name
                            addressLine1
                            addressLine2
                            country {
                                iso31661Alpha3
                                name
                            }
                            city
                            postcode
                        }
                        shipToCustomer {
                            name
                            primaryAddress {
                                addressLine1
                            }
                            country {
                                iso31661Alpha3
                            }
                        }
                        shipToAddress {
                            city
                            postcode
                            country {
                                iso31661Alpha3
                                name
                            }
                        }
                        DxKaGqbS: lines {
                            query (filter: "{}", orderBy: "{\"quantityInStockUnit\":1,\"item\":{\"id\":1},\"itemDescription\":1,\"unit\":{\"name\":1},\"quantityInSalesUnit\":1}") {
                                edges {
                                    node {
                                        _id
                                        VyjXdwRu: salesOrderLines {
                                            query (filter: "{}", orderBy: "{}", first: 1) {
                                                edges {
                                                    node {
                                                        linkedDocument {
                                                            documentNumber
                                                        }
                                                        _id
                                                    }
                                                }
                                            }
                                        }
                                        item { id }
                                        itemDescription
                                        quantity
                                        unit {
                                            name
                                        }
                                    }
                                }
                            }
                        }
                        ZiTboSOR: stockSite {
                            legalCompany {
                                addresses {
                                    query (filter: "{}", orderBy: "{}", first: 1) {
                                        edges {
                                            node {
                                                addressLine1
                                                country {
                                                    iso31661Alpha3
                                                    name
                                                }
                                                city
                                                postcode
                                                addressLine2
                                                address {
                                                    country {
                                                        iso31661Alpha3
                                                    }
                                                }
                                                _id
                                            }
                                        }
                                    }
                                }
                            }
                        }
                        isExternalNote
                        _id
                    }
                }
            }
        }
    }
}
