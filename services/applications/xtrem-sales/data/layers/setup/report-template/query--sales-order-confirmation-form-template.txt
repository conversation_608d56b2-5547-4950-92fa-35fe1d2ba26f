query {
    ERtqegrm: xtremSales {
        salesOrder {
            query (filter: "{\"_id\":{\"_and\":[{\"_eq\":\"{{order}}\"}]}}", orderBy: "{}", first: 1) {
                edges {
                    node {
                        site {
                            name
                            primaryAddress {
                                addressLine1
                                addressLine2
                                country {
                                    iso31661Alpha3
                                    name
                                }
                                region
                                city
                                postcode
                            }
                            siret
                            taxIdNumber
                            legalCompany {
                                legislation {
                                    id
                                }
                                naf
                                legalForm
                                name
                                rcs
                            }
                        }
                        number
                        date
                        customerNumber
                        billToCustomer {
                            name
                            id
                            siret
                            taxIdNumber
                            paymentTerm {
                                name
                            }
                        }
                        shippingDate
                        deliveryMode {
                            name
                        }
                        incoterm {
                            name
                        }
                        paymentTerm {
                            name
                        }
                        soldToCustomer {
                            name
                        }
                        soldToAddress {
                            addressLine1
                            addressLine2
                            country {
                                iso31661Alpha3
                                name
                            }
                            city
                            region
                            postcode
                        }
                        shipToCustomer {
                            name
                        }
                        shipToAddress {
                            addressLine1
                            addressLine2
                            country {
                                iso31661Alpha3
                                name
                            }
                            city
                            region
                            postcode
                        }
                        YpbYkrws: lines {
                            query (filter: "{}", orderBy: "{\"item\":{\"id\":1},\"itemDescription\":1,\"unit\":{\"name\":1},\"grossPrice\":1,\"netPrice\":1,\"amountExcludingTax\":1,\"amountExcludingTaxInCompanyCurrency\":1,\"amountIncludingTaxInCompanyCurrency\":1,\"amountIncludingTax\":1}") {
                                edges {
                                    node {
                                        _id
                                        item {
                                            id
                                        }
                                        quantityInSalesUnit
                                        itemDescription
                                        unit {
                                            name
                                        }
                                        document {
                                            currency {
                                                symbol
                                            }
                                        }
                                        grossPrice
                                        discount
                                        netPrice
                                        amountExcludingTax
                                        GvOHQCzv: taxes {
                                            query (filter: "{}", orderBy: "{}", first: 1) {
                                                edges {
                                                    node {
                                                        taxRate
                                                        _id
                                                    }
                                                }
                                            }
                                        }
                                        amountIncludingTax
                                    }
                                }
                            }
                        }
                        ClLELCUx: taxes {
                            query (filter: "{}", orderBy: "{\"tax\":1,\"taxableAmount\":1,\"taxRate\":1,\"taxAmount\":1}") {
                                edges {
                                    node {
                                        _id
                                        tax
                                        currency {
                                            symbol
                                        }
                                        taxableAmount
                                        taxRate
                                        taxAmount
                                    }
                                }
                            }
                        }
                        currency {
                            symbol
                        }
                        totalAmountExcludingTax
                        totalTaxAmount
                        totalAmountIncludingTax
                        UptVjTbI: site {
                            legalCompany {
                                addresses {
                                    query (filter: "{}", orderBy: "{}", first: 1) {
                                        edges {
                                            node {
                                                country {
                                                    iso31661Alpha3
                                                }
                                                region
                                                address {
                                                    addressLine1
                                                    addressLine2
                                                    city
                                                    region
                                                    postcode
                                                    country {
                                                        name
                                                    }
                                                }
                                                _id
                                            }
                                        }
                                    }
                                }
                            }
                        }
                        isExternalNote
                        externalNote {
                            value
                        }
                        _id
                    }
                }
            }
        }
    }
}
