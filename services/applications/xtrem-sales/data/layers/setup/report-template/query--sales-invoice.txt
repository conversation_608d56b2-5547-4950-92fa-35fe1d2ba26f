{
  xtremSales{
    salesInvoice{
      query (filter: "{_id:'{{invoice}}' }") {
        edges {
          node {
            number
            status
            isPrinted
            date
            externalNote {
              value
            }
            salesSite{
              name
              legalCompany{
                name
                addresses{
                  query (filter: "{isPrimary:true}") {
                    edges {
                      node {
                        name
                        addressLine1
                        addressLine2
                        city
                        region
                        postcode
                        country {
                          name
                          iso31661Alpha3
                        }
                      }
                    }
                  }
                }
                legalForm
                rcs
                naf
                siren
                legislation {
                  id
                }
              }
              siret
              taxIdNumber
            }
            salesSiteAddress{
              name
                        addressLine1
                        addressLine2
                        city
                        region
                        postcode
                        country {
                          name
                          iso31661Alpha3
                        }
            }
            currency{
              symbol
            }
            billToCustomer{
              name
              id
              businessEntity{
                siret
                taxIdNumber
              }
            }
            billToAddress{
              name
              addressLine1
              addressLine2
              city
              region
              postcode
              country{
                name
                iso31661Alpha3
              }
            }
            dueDate
            paymentTerm{
              name
              description

            }
            totalAmountExcludingTax
            totalTaxAmount
            totalAmountIncludingTax
            taxes{
              query{
                edges{
                  node {
                    tax
                    taxableAmount
                    taxRate
                    taxAmount
                    taxReference {
                      legalMention
                    }
                  }
                }
              }
            }
            discountPaymentBeforeDate
            discountPaymentType
            discountPaymentAmount
            penaltyPaymentType
            penaltyPaymentAmount
            lines{
              query(first:500){
                edges {
                  node {
										salesOrderLines{
                      query{
                        edges {
                          node {
                            linkedDocument{
                              document {
                                number
                              }
                            }
                          }
                        }
                      }
                    }
                    salesShipmentLines{
                      query{
                        edges{
                          node{
                            linkedDocument{
                              document{
                                number
                              }
                            }
                          }
                        }
                      }
                    }
                    consumptionAddress{
                      name
                      addressLine1
                      addressLine2
                      postcode
                      city
                      region
                      country{
                        name
                        iso31661Alpha3
                      }
                    }
                    item{
                      id
                    }
                    itemDescription
                    quantity
                    unit{
                      name
                    }
                    netPrice
                    externalNote {
                      value
                    }
                    amountExcludingTax
                    amountIncludingTax
                    taxes{
                      query{
                        edges{
                          node {
                            taxRate
                          }
                        }
                      }
                    }
                  }
                }

              }
            }
          }
        }
      }
    }
  }
}
