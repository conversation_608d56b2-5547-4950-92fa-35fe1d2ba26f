<section class="unbreakable-block">
  <div class="unbreakable-block-body">
    <section
      class="record-context"
      data-context-object-type="SalesCreditMemo"
      data-context-object-path="xtremSales.salesCreditMemo.query.edges.0.node"
      data-context-filter='[{"_id":"1","label":"_id","filterType":"matches","filterValue":"creditMemo","data":{"type":"IntOrString","kind":"SCALAR","name":"_id","canFilter":true,"canSort":true,"label":"_id","isOnInputType":false,"isOnOutputType":true,"dataType":"","targetNode":"","enumType":null,"isCustom":false,"isMutable":false,"iconType":"csv"},"id":"_id","labelPath":"_id","property":{"type":"IntOrString","kind":"SCALAR","name":"_id","canFilter":true,"canSort":true,"label":"_id","isOnInputType":false,"isOnOutputType":true,"dataType":"","targetNode":"","enumType":null,"isCustom":false,"isMutable":false,"iconType":"csv","id":"_id","data":{"type":"IntOrString","kind":"SCALAR","name":"_id","canFilter":true,"canSort":true,"label":"_id","isOnInputType":false,"isOnOutputType":true,"dataType":"","targetNode":"","enumType":null,"isCustom":false,"isMutable":false,"iconType":"csv"},"key":"_id","labelKey":"_id","labelPath":"_id"},"parameter":true}]'
      data-context-list-order="{}"
      data-alias="zaALNweA"
    >
      <!--{{#with zaALNweA.salesCreditMemo.query.edges.0.node}}-->
      <div class="report-context-body">
        <figure class="table" style="width: 100%">
          <table class="ck-table-resized">
            <colgroup>
              <col style="width: 34.5%" />
              <col style="width: 65.5%" />
            </colgroup>
            <tbody>
              <tr>
                <td>
                  <p>
                    <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                      ><strong
                        ><span
                          class="property"
                          data-property-display-label="Name"
                          data-property-data-type="String"
                          data-property-name="salesSite.name"
                          data-property-data-format=""
                          data-property-parent-context="SalesCreditMemo"
                          >{{salesSite.name}}</span
                        ></strong
                      ></span
                    >
                  </p>
                  <p>
                    <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                      ><span
                        class="property"
                        data-property-display-label="Address line 1"
                        data-property-data-type="String"
                        data-property-name="salesSiteAddress.addressLine1"
                        data-property-data-format=""
                        data-property-parent-context="SalesCreditMemo"
                        >{{salesSiteAddress.addressLine1}}</span
                      ></span
                    >
                  </p>
                  <section
                    class="conditional-block"
                    data-context-condition='[{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"1","value1":{"label":"Address line 2","data":{"name":"addressLine2","title":"Address line 2","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"addressLineDataType","targetNode":"","isCollection":false,"kind":"SCALAR","label":"Address line 2","node":"String","iconType":"csv"},"id":"salesSiteAddress.addressLine2","key":"salesSiteAddress.addressLine2","labelKey":"Address line 2","labelPath":"Sales site address > Address line 2"},"value2":null,"key":"1","operator":"notEmpty"}]'
                  >
                    <!--{{#if ( not ( or ( eq salesSiteAddress.addressLine2 null ) ( eq salesSiteAddress.addressLine2 "" ) ( eq salesSiteAddress.addressLine2 undefined ) ) )}}-->
                    <div class="conditional-block-body">
                      <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                        ><span
                          class="property"
                          data-property-display-label="Address line 2"
                          data-property-data-type="String"
                          data-property-name="salesSiteAddress.addressLine2"
                          data-property-data-format=""
                          data-property-parent-context="SalesCreditMemo"
                          >{{salesSiteAddress.addressLine2}}</span
                        ></span
                      >
                    </div>
                    <!--{{/if}}-->
                    <div class="conditional-block-footer">&nbsp;</div>
                  </section>
                  <p>
                    <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                      ><span
                        class="property"
                        data-property-display-label="Postal code"
                        data-property-data-type="String"
                        data-property-name="salesSiteAddress.postcode"
                        data-property-data-format=""
                        data-property-parent-context="SalesCreditMemo"
                        >{{salesSiteAddress.postcode}}</span
                      ><span
                        class="property"
                        data-property-display-label="City"
                        data-property-data-type="String"
                        data-property-name="salesSiteAddress.city"
                        data-property-data-format=""
                        data-property-parent-context="SalesCreditMemo"
                        >{{salesSiteAddress.city}}</span
                      ><span
                        class="property"
                        data-property-display-label="Region"
                        data-property-data-type="String"
                        data-property-name="salesSiteAddress.region"
                        data-property-data-format=""
                        data-property-parent-context="SalesCreditMemo"
                        >{{salesSiteAddress.region}}</span
                      ></span
                    >
                  </p>
                  <p>
                    <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                      ><span
                        class="property"
                        data-property-display-label="Name"
                        data-property-data-type="String"
                        data-property-name="salesSiteAddress.country.name"
                        data-property-data-format=""
                        data-property-parent-context="SalesCreditMemo"
                        >{{salesSiteAddress.country.name}}</span
                      ></span
                    >
                  </p>
                  <section
                    class="conditional-block"
                    data-context-condition='[{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"1","value1":{"label":"SIRET","data":{"name":"siret","title":"SIRET","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":false,"isOnOutputType":true,"enumType":null,"dataType":"description","targetNode":"","isCollection":false,"kind":"SCALAR","label":"SIRET","node":"String","iconType":"csv"},"id":"salesSite.siret","key":"salesSite.siret","labelKey":"SIRET","labelPath":"Sales site > SIRET"},"value2":null,"key":"1","operator":"notEmpty"}]'
                  >
                    <!--{{#if ( not ( or ( eq salesSite.siret null ) ( eq salesSite.siret "" ) ( eq salesSite.siret undefined ) ) )}}-->
                    <div class="conditional-block-body">
                      <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                        ><strong>&nbsp;{{ translatedContent "d04e2c1b67f3ef0d475409516b812e8b" }}&nbsp;</strong>&nbsp;<span
                          class="property"
                          data-property-display-label="SIRET"
                          data-property-data-type="String"
                          data-property-name="salesSite.siret"
                          data-property-data-format=""
                          data-property-parent-context="SalesCreditMemo"
                          >{{salesSite.siret}}</span
                        ></span
                      >
                    </div>
                    <!--{{/if}}-->
                    <div class="conditional-block-footer">&nbsp;</div>
                  </section>
                  <section
                    class="conditional-block"
                    data-context-condition='[{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"1","value1":{"label":"Tax ID number","data":{"name":"taxIdNumber","title":"Tax ID number","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":false,"isOnOutputType":true,"enumType":null,"dataType":"taxIdentificationDataType","targetNode":"","isCollection":false,"kind":"SCALAR","label":"Tax ID number","node":"String","iconType":"csv"},"id":"salesSite.taxIdNumber","key":"salesSite.taxIdNumber","labelKey":"Tax ID number","labelPath":"Sales site > Tax ID number"},"value2":null,"key":"1","operator":"notEmpty"}]'
                  >
                    <!--{{#if ( not ( or ( eq salesSite.taxIdNumber null ) ( eq salesSite.taxIdNumber "" ) ( eq salesSite.taxIdNumber undefined ) ) )}}-->
                    <div class="conditional-block-body">
                      <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                        ><strong>&nbsp;{{ translatedContent "37a7a58f6fdc66ce34c2bf56389a2aac" }}&nbsp;</strong>&nbsp;<span
                          class="property"
                          data-property-display-label="Tax ID number"
                          data-property-data-type="String"
                          data-property-name="salesSite.taxIdNumber"
                          data-property-data-format=""
                          data-property-parent-context="SalesCreditMemo"
                          >{{salesSite.taxIdNumber}}</span
                        ></span
                      >
                    </div>
                    <!--{{/if}}-->
                    <div class="conditional-block-footer">&nbsp;</div>
                  </section>
                </td>
                <td style="background-color: #dfdfdf; text-align: right">
                  <h2>
                    <span style="color: #198e59; font-family: 'Sage UI', Geneva, sans-serif"
                      ><strong>{{ translatedContent "48ff14cbdaebf2cac64e8adade5b1d77" }}</strong></span
                    >
                  </h2>
                  <p>
                    <span style="color: #198e59; font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                      ><strong>{{ translatedContent "2a39f204d4eac05604c2059e5ac2f9f7" }}&nbsp;</strong></span
                    ><span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                      ><strong
                        ><span
                          class="property"
                          data-property-display-label="Number"
                          data-property-data-type="String"
                          data-property-name="number"
                          data-property-data-format=""
                          data-property-parent-context="SalesCreditMemo"
                          >{{number}}</span
                        ></strong
                      ></span
                    >
                  </p>
                  <p>
                    <span style="color: #198e59; font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                      ><strong>{{ translatedContent "664a94f92e44bfcbc136dff160551b7b" }}&nbsp;</strong></span
                    ><span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                      ><strong
                        ><span
                          class="property"
                          data-property-display-label="Credit memo date"
                          data-property-data-type="Date"
                          data-property-name="date"
                          data-property-data-format="FullDate"
                          data-property-parent-context="SalesCreditMemo"
                          >{{formatDate date 'FullDate'}}</span
                        ></strong
                      ></span
                    >
                  </p>
                </td>
              </tr>
              <tr>
                <td style="border-color: #ffffff">
                  <p>
                    <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                      ><strong>{{ translatedContent "5eea367ea73b909880393bd1ae79fc67" }}</strong>&nbsp;</span
                    ><span style="color: #198e59; font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                      ><strong
                        ><span
                          class="property"
                          data-property-display-label="Name"
                          data-property-data-type="String"
                          data-property-name="billToCustomer.name"
                          data-property-data-format=""
                          data-property-parent-context="SalesCreditMemo"
                          >{{billToCustomer.name}}</span
                        ></strong
                      ></span
                    >
                  </p>
                  <section
                    class="conditional-block"
                    data-context-condition='[{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"1","value1":{"label":"ID","data":{"name":"id","title":"ID","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":false,"isOnOutputType":true,"enumType":null,"dataType":"","targetNode":"","isCollection":false,"kind":"SCALAR","label":"ID","node":"String","iconType":"csv"},"id":"billToCustomer.id","key":"billToCustomer.id","labelKey":"ID","labelPath":"Bill-to customer > ID"},"value2":null,"key":"1","operator":"notEmpty"}]'
                  >
                    <!--{{#if ( not ( or ( eq billToCustomer.id null ) ( eq billToCustomer.id "" ) ( eq billToCustomer.id undefined ) ) )}}-->
                    <div class="conditional-block-body">
                      <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                        ><strong>{{ translatedContent "d13d8380c3f4de07fef91a42fe6c60d7" }}</strong>&nbsp;<span
                          class="property"
                          data-property-display-label="ID"
                          data-property-data-type="String"
                          data-property-name="billToCustomer.id"
                          data-property-data-format=""
                          data-property-parent-context="SalesCreditMemo"
                          >{{billToCustomer.id}}</span
                        ></span
                      >
                    </div>
                    <!--{{/if}}-->
                    <div class="conditional-block-footer">&nbsp;</div>
                  </section>
                  <section
                    class="conditional-block"
                    data-context-condition='[{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"1","value1":{"label":"SIRET","data":{"name":"siret","title":"SIRET","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"description","targetNode":"","isCollection":false,"kind":"SCALAR","label":"SIRET","node":"String","iconType":"csv"},"id":"billToCustomer.businessEntity.siret","key":"billToCustomer.businessEntity.siret","labelKey":"SIRET","labelPath":"Bill-to customer > Business entity > SIRET"},"value2":null,"key":"1","operator":"notEmpty"}]'
                  >
                    <!--{{#if ( not ( or ( eq billToCustomer.businessEntity.siret null ) ( eq billToCustomer.businessEntity.siret "" ) ( eq billToCustomer.businessEntity.siret undefined ) ) )}}-->
                    <div class="conditional-block-body">
                      <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                        ><strong>{{ translatedContent "d04e2c1b67f3ef0d475409516b812e8b" }}&nbsp;</strong
                        ><span
                          class="property"
                          data-property-display-label="SIRET"
                          data-property-data-type="String"
                          data-property-name="billToCustomer.businessEntity.siret"
                          data-property-data-format=""
                          data-property-parent-context="SalesCreditMemo"
                          >{{billToCustomer.businessEntity.siret}}</span
                        ></span
                      >
                    </div>
                    <!--{{/if}}-->
                    <div class="conditional-block-footer">&nbsp;</div>
                  </section>
                  <section
                    class="conditional-block"
                    data-context-condition='[{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"1","value1":{"label":"Tax ID","data":{"name":"taxIdNumber","title":"Tax ID","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"taxIdentificationDataType","targetNode":"","isCollection":false,"kind":"SCALAR","label":"Tax ID","node":"String","iconType":"csv"},"id":"billToCustomer.businessEntity.taxIdNumber","key":"billToCustomer.businessEntity.taxIdNumber","labelKey":"Tax ID","labelPath":"Bill-to customer > Business entity > Tax ID"},"value2":null,"key":"1","operator":"notEmpty"}]'
                  >
                    <!--{{#if ( not ( or ( eq billToCustomer.businessEntity.taxIdNumber null ) ( eq billToCustomer.businessEntity.taxIdNumber "" ) ( eq billToCustomer.businessEntity.taxIdNumber undefined ) ) )}}-->
                    <div class="conditional-block-body">
                      <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                        ><strong>{{ translatedContent "37a7a58f6fdc66ce34c2bf56389a2aac" }}&nbsp;</strong
                        ><span
                          class="property"
                          data-property-display-label="Tax ID"
                          data-property-data-type="String"
                          data-property-name="billToCustomer.businessEntity.taxIdNumber"
                          data-property-data-format=""
                          data-property-parent-context="SalesCreditMemo"
                          >{{billToCustomer.businessEntity.taxIdNumber}}</span
                        ></span
                      >
                    </div>
                    <!--{{/if}}-->
                    <div class="conditional-block-footer">&nbsp;</div>
                  </section>
                </td>
                <td>&nbsp;</td>
              </tr>
            </tbody>
          </table>
        </figure>
        <figure class="table" style="width: 100%">
          <table class="ck-table-resized">
            <colgroup>
              <col style="width: 49.92%" />
              <col style="width: 50.08%" />
            </colgroup>
            <tbody>
              <tr>
                <td style="background-color: #dfdfdf; border-color: #dfdfdf">
                  <span style="color: #198e59; font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                    ><strong>{{ translatedContent "84e839915aa016435941dda50c367e0c" }}</strong></span
                  >
                </td>
                <td style="background-color: #dfdfdf; border-color: #dfdfdf">
                  <span style="color: #198e59; font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                    ><strong>{{ translatedContent "6fd791a834ec2f5a235b58bdea1746fc" }}</strong></span
                  >
                </td>
              </tr>
              <tr>
                <td style="border-color: #ffffff">
                  <p>
                    <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                      ><strong
                        ><span
                          class="property"
                          data-property-display-label="Name"
                          data-property-data-type="String"
                          data-property-name="billToCustomer.name"
                          data-property-data-format=""
                          data-property-parent-context="SalesCreditMemo"
                          >{{billToCustomer.name}}</span
                        ></strong
                      ></span
                    >
                  </p>
                  <section
                    class="record-context"
                    data-context-object-type="SalesCreditMemoLine"
                    data-context-object-path="lines.query.edges.0.node"
                    data-context-filter="[]"
                    data-context-list-order="{}"
                    data-alias="VXTJkjpl"
                  >
                    <!--{{#with VXTJkjpl.query.edges.0.node}}-->
                    <div class="report-context-body">
                      <p>
                        <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                          ><span
                            class="property"
                            data-property-display-label="Address line 1"
                            data-property-data-type="String"
                            data-property-name="consumptionAddress.addressLine1"
                            data-property-data-format=""
                            data-property-parent-context="SalesCreditMemoLine"
                            >{{consumptionAddress.addressLine1}}</span
                          ></span
                        >
                      </p>
                      <section
                        class="conditional-block"
                        data-context-condition='[{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"1","value1":{"label":"Address line 2","data":{"name":"addressLine2","title":"Address line 2","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"addressLineDataType","targetNode":"","isCollection":false,"kind":"SCALAR","label":"Address line 2","node":"String","iconType":"csv"},"id":"consumptionAddress.addressLine2","key":"consumptionAddress.addressLine2","labelKey":"Address line 2","labelPath":"Consumption address > Address line 2"},"value2":null,"key":"1","operator":"notEmpty"}]'
                      >
                        <!--{{#if ( not ( or ( eq consumptionAddress.addressLine2 null ) ( eq consumptionAddress.addressLine2 "" ) ( eq consumptionAddress.addressLine2 undefined ) ) )}}-->
                        <div class="conditional-block-body">
                          <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                            ><span
                              class="property"
                              data-property-display-label="Address line 2"
                              data-property-data-type="String"
                              data-property-name="consumptionAddress.addressLine2"
                              data-property-data-format=""
                              data-property-parent-context="SalesCreditMemoLine"
                              >{{consumptionAddress.addressLine2}}</span
                            ></span
                          >
                        </div>
                        <!--{{/if}}-->
                        <div class="conditional-block-footer">&nbsp;</div>
                      </section>
                      <p>
                        <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                          ><span
                            class="property"
                            data-property-display-label="Postal code"
                            data-property-data-type="String"
                            data-property-name="consumptionAddress.postcode"
                            data-property-data-format=""
                            data-property-parent-context="SalesCreditMemoLine"
                            >{{consumptionAddress.postcode}}</span
                          ><span
                            class="property"
                            data-property-display-label="City"
                            data-property-data-type="String"
                            data-property-name="consumptionAddress.city"
                            data-property-data-format=""
                            data-property-parent-context="SalesCreditMemoLine"
                            >{{consumptionAddress.city}}</span
                          ><span
                            class="property"
                            data-property-display-label="Region"
                            data-property-data-type="String"
                            data-property-name="consumptionAddress.region"
                            data-property-data-format=""
                            data-property-parent-context="SalesCreditMemoLine"
                            >{{consumptionAddress.region}}</span
                          ></span
                        >
                      </p>
                      <p>
                        <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                          ><span
                            class="property"
                            data-property-display-label="Name"
                            data-property-data-type="String"
                            data-property-name="consumptionAddress.country.name"
                            data-property-data-format=""
                            data-property-parent-context="SalesCreditMemoLine"
                            >{{consumptionAddress.country.name}}</span
                          ></span
                        >
                      </p>
                    </div>
                    <!--{{/with}}--><span class="report-context-footer">&nbsp;</span>
                  </section>
                </td>
                <td>
                  <section
                    class="conditional-block"
                    data-context-condition='[{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"1","value1":{"label":"Name","data":{"name":"name","title":"Name","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":false,"isOnOutputType":true,"enumType":null,"dataType":"","targetNode":"","isCollection":false,"kind":"SCALAR","label":"Name","node":"String","iconType":"csv"},"id":"billToCustomer.name","key":"billToCustomer.name","labelKey":"Name","labelPath":"Bill-to customer > Name"},"value2":null,"key":"1","operator":"notEmpty"}]'
                  >
                    <div class="conditional-block-body">
                      <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                        ><strong
                          ><span
                            class="property"
                            data-property-display-label="Name"
                            data-property-data-type="String"
                            data-property-name="billToCustomer.name"
                            data-property-data-format=""
                            data-property-parent-context="SalesCreditMemo"
                            >{{billToCustomer.name}}</span
                          ></strong
                        ></span
                      >
                    </div>
                    <div class="conditional-block-footer">&nbsp;</div>
                  </section>
                  <p>
                    <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                      ><span
                        class="property"
                        data-property-display-label="Address line 1"
                        data-property-data-type="String"
                        data-property-name="billToAddress.addressLine1"
                        data-property-data-format=""
                        data-property-parent-context="SalesCreditMemo"
                        >{{billToAddress.addressLine1}}</span
                      ></span
                    >
                  </p>
                  <section
                    class="conditional-block"
                    data-context-condition='[{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"1","value1":{"label":"Address line 2","data":{"name":"addressLine2","title":"Address line 2","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"addressLineDataType","targetNode":"","isCollection":false,"kind":"SCALAR","label":"Address line 2","node":"String","iconType":"csv"},"id":"billToAddress.addressLine2","key":"billToAddress.addressLine2","labelKey":"Address line 2","labelPath":"Bill-to address > Address line 2"},"value2":null,"key":"1","operator":"notEmpty"}]'
                  >
                    <!--{{#if ( not ( or ( eq billToAddress.addressLine2 null ) ( eq billToAddress.addressLine2 "" ) ( eq billToAddress.addressLine2 undefined ) ) )}}-->
                    <div class="conditional-block-body">
                      <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                        ><span
                          class="property"
                          data-property-display-label="Address line 2"
                          data-property-data-type="String"
                          data-property-name="billToAddress.addressLine2"
                          data-property-data-format=""
                          data-property-parent-context="SalesCreditMemo"
                          >{{billToAddress.addressLine2}}</span
                        ></span
                      >
                    </div>
                    <!--{{/if}}-->
                    <div class="conditional-block-footer">&nbsp;</div>
                  </section>
                  <p>
                    <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                      ><span
                        class="property"
                        data-property-display-label="Postal code"
                        data-property-data-type="String"
                        data-property-name="billToAddress.postcode"
                        data-property-data-format=""
                        data-property-parent-context="SalesCreditMemo"
                        >{{billToAddress.postcode}}</span
                      ><span
                        class="property"
                        data-property-display-label="City"
                        data-property-data-type="String"
                        data-property-name="billToAddress.city"
                        data-property-data-format=""
                        data-property-parent-context="SalesCreditMemo"
                        >{{billToAddress.city}}</span
                      ><span
                        class="property"
                        data-property-display-label="Region"
                        data-property-data-type="String"
                        data-property-name="billToAddress.region"
                        data-property-data-format=""
                        data-property-parent-context="SalesCreditMemo"
                        >{{billToAddress.region}}</span
                      ></span
                    >
                  </p>
                  <p>
                    <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                      ><span
                        class="property"
                        data-property-display-label="Name"
                        data-property-data-type="String"
                        data-property-name="billToAddress.country.name"
                        data-property-data-format=""
                        data-property-parent-context="SalesCreditMemo"
                        >{{billToAddress.country.name}}</span
                      ></span
                    >
                  </p>
                </td>
              </tr>
            </tbody>
          </table>
        </figure>
        <table
          class="query-table"
          data-context-object-type="SalesCreditMemoLine"
          data-context-object-path="lines.query.edges"
          data-context-filter="[]"
          data-context-list-order='{"document.number":"ascending","item":{ "id":"ascending"},"itemDescription":"ascending","quantity":"ascending","unit.name":"ascending","netPrice":"ascending","amountExcludingTax":"ascending","amountExcludingTaxInCompanyCurrency":"ascending","amountIncludingTax":"ascending"}'
          data-alias="QCXJMBON"
        >
          <thead class="query-table-head">
            <tr class="query-table-row">
              <td class="query-table-cell" style="background-color: #0000001a; border: 1px solid #dfdfdf; padding: 2px">
                <p>
                  <span style="color: #198e59; font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                    ><strong>{{ translatedContent "466eadd40b3c10580e3ab4e8061161ce" }}</strong></span
                  >
                </p>
              </td>
              <td class="query-table-cell" style="background-color: #0000001a; border: 1px solid #dfdfdf; padding: 2px">
                <p>
                  <span style="color: #198e59; font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                    ><strong>{{ translatedContent "7d74f3b92b19da5e606d737d339a9679" }}</strong></span
                  >
                </p>
              </td>
              <td class="query-table-cell" style="background-color: #0000001a; border: 1px solid #dfdfdf; padding: 2px">
                <p>
                  <span style="color: #198e59; font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                    ><strong>{{ translatedContent "b5a7adde1af5c87d7fd797b6245c2a39" }}</strong></span
                  >
                </p>
              </td>
              <td class="query-table-cell" style="background-color: #0000001a; border: 1px solid #dfdfdf; padding: 2px">
                <p>
                  <span style="color: #198e59; font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                    ><strong>{{ translatedContent "694e8d1f2ee056f98ee488bdc4982d73" }}</strong></span
                  >
                </p>
              </td>
              <td class="query-table-cell" style="background-color: #0000001a; border: 1px solid #dfdfdf; padding: 2px">
                <p>
                  <span style="color: #198e59; font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                    ><strong>{{ translatedContent "19c562a36aeb455d09534f93b4f5236f" }}</strong></span
                  >
                </p>
              </td>
              <td class="query-table-cell" style="background-color: #0000001a; border: 1px solid #dfdfdf; padding: 2px">
                <p>
                  <span style="color: #198e59; font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                    ><strong>{{ translatedContent "9554ce40e7959ce198210376c97150b3" }}</strong></span
                  >
                </p>
              </td>
              <td class="query-table-cell" style="background-color: #0000001a; border: 1px solid #dfdfdf; padding: 2px">
                <p>
                  <span style="color: #198e59; font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                    ><strong>{{ translatedContent "c17f9183c57045cfb614e02bbb233ee1" }}</strong></span
                  >
                </p>
              </td>
              <td class="query-table-cell" style="background-color: #0000001a; border: 1px solid #dfdfdf; padding: 2px">
                <p>
                  <span style="color: #198e59; font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                    ><strong>{{ translatedContent "20a34c4e30c5bbe1d3f870ac55f0d831" }}</strong></span
                  >
                </p>
              </td>
              <td class="query-table-cell" style="background-color: #0000001a; border: 1px solid #dfdfdf; padding: 2px">
                <p>
                  <span style="color: #198e59; font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                    ><strong>{{ translatedContent "41dd1e26b4321ab2f837f69a7aae8e91" }}</strong></span
                  >
                </p>
              </td>
            </tr>
          </thead>
          <tbody class="query-table-body">
            <!--{{#each QCXJMBON.query.edges}}{{#with node}}-->
            <tr class="query-table-row">
              <td class="query-table-cell" style="border: 1px solid #dfdfdf; padding: 2px">
                <p>
                  <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                    ><span
                      class="property"
                      data-property-display-label="Invoice"
                      data-property-data-type="String"
                      data-property-name="document.number"
                      data-property-data-format=""
                      data-property-parent-context="SalesCreditMemoLine"
                      >{{document.number}}</span
                    ></span
                  >
                </p>
              </td>
              <td class="query-table-cell" style="border: 1px solid #dfdfdf; padding: 2px">
                <p>
                  <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                    ><span
                      class="property"
                      data-property-display-label="Item"
                      data-property-data-type="String"
                      data-property-name="item.id"
                      data-property-data-format=""
                      data-property-parent-context="SalesCreditMemoLine"
                      >{{item.id}}</span
                    ></span
                  >
                </p>
              </td>
              <td class="query-table-cell" style="border: 1px solid #dfdfdf; padding: 2px">
                <p>
                  <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                    ><span
                      class="property"
                      data-property-display-label="Description"
                      data-property-data-type="String"
                      data-property-name="itemDescription"
                      data-property-data-format=""
                      data-property-parent-context="SalesCreditMemoLine"
                      >{{itemDescription}}</span
                    ></span
                  >
                </p>
              </td>
              <td class="query-table-cell" style="border: 1px solid #dfdfdf; padding: 2px; text-align: right">
                <p>
                  <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                    ><span
                      class="property"
                      data-property-display-label="Quantity"
                      data-property-data-type="Decimal"
                      data-property-name="quantity"
                      data-property-data-format="2"
                      data-property-parent-context="SalesCreditMemoLine"
                      >{{formatNumber quantity 2}}</span
                    ></span
                  >
                </p>
              </td>
              <td class="query-table-cell" style="border: 1px solid #dfdfdf; padding: 2px; text-align: right">
                <p>
                  <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                    ><span
                      class="property"
                      data-property-display-label="Unit"
                      data-property-data-type="String"
                      data-property-name="unit.name"
                      data-property-data-format=""
                      data-property-parent-context="SalesCreditMemoLine"
                      >{{unit.name}}</span
                    ></span
                  >
                </p>
              </td>
              <td class="query-table-cell" style="border: 1px solid #dfdfdf; padding: 2px; text-align: right">
                <p>
                  <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                    ><span
                      class="property"
                      data-property-display-label="Symbol"
                      data-property-data-type="String"
                      data-property-name="document.currency.symbol"
                      data-property-data-format=""
                      data-property-parent-context="SalesCreditMemoLine"
                      >{{document.currency.symbol}}</span
                    ><span
                      class="property"
                      data-property-display-label="Net price"
                      data-property-data-type="Decimal"
                      data-property-name="netPrice"
                      data-property-data-format="2"
                      data-property-parent-context="SalesCreditMemoLine"
                      >{{formatNumber netPrice 2}}</span
                    ></span
                  >
                </p>
              </td>
              <td class="query-table-cell" style="border: 1px solid #dfdfdf; padding: 2px; text-align: right">
                <p>
                  <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                    ><span
                      class="property"
                      data-property-display-label="Symbol"
                      data-property-data-type="String"
                      data-property-name="document.currency.symbol"
                      data-property-data-format=""
                      data-property-parent-context="SalesCreditMemoLine"
                      >{{document.currency.symbol}}</span
                    ><span
                      class="property"
                      data-property-display-label="Excl. tax"
                      data-property-data-type="Decimal"
                      data-property-name="amountExcludingTax"
                      data-property-data-format="2"
                      data-property-parent-context="SalesCreditMemoLine"
                      >{{formatNumber amountExcludingTax 2}}</span
                    ></span
                  >
                </p>
              </td>
              <td class="query-table-cell" style="border: 1px solid #dfdfdf; padding: 2px; text-align: right">
                <section
                  class="record-context"
                  data-context-object-type="SalesCreditMemoLineTax"
                  data-context-object-path="taxes.query.edges.0.node"
                  data-context-filter="[]"
                  data-context-list-order="{}"
                  data-alias="BeKKQqqr"
                >
                  <!--{{#with BeKKQqqr.query.edges.0.node}}-->
                  <div class="report-context-body">
                    <p>&nbsp;</p>
                    <p>
                      <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                        ><span
                          class="property"
                          data-property-display-label="Tax rate"
                          data-property-data-type="Decimal"
                          data-property-name="taxRate"
                          data-property-data-format="2"
                          data-property-parent-context="SalesCreditMemoLineTax"
                          >{{formatNumber taxRate 2}}</span
                        >{{ translatedContent "0bcef9c45bd8a48eda1b26eb0c61c869" }}</span
                      >
                    </p>
                  </div>
                  <!--{{/with}}--><span class="report-context-footer">&nbsp;</span>
                </section>
              </td>
              <td class="query-table-cell" style="border: 1px solid #dfdfdf; padding: 2px; text-align: right">
                <p>
                  <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                    ><span
                      class="property"
                      data-property-display-label="Symbol"
                      data-property-data-type="String"
                      data-property-name="document.currency.symbol"
                      data-property-data-format=""
                      data-property-parent-context="SalesCreditMemoLine"
                      >{{document.currency.symbol}}</span
                    ><span
                      class="property"
                      data-property-display-label="Incl. tax"
                      data-property-data-type="Decimal"
                      data-property-name="amountIncludingTax"
                      data-property-data-format="2"
                      data-property-parent-context="SalesCreditMemoLine"
                      >{{formatNumber amountIncludingTax 2}}</span
                    ></span
                  >
                </p>
              </td>
            </tr>
            <!--{{#printBreakIfLast  'netPrice' 'sum' 'amountExcludingTax' 'sum' 'amountIncludingTax' 'sum'}}-->
            <tr class="query-table-row" data-footer-group="footer">
              <td class="query-table-cell" style="background-color: #dfdfdf; border: 1px solid #dfdfdf; padding: 2px">
                <p>
                  <span style="color: #198e59; font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                    ><strong>{{ translatedContent "edd291d1439a6c1c18fe38bee411580f" }}</strong></span
                  >
                </p>
              </td>
              <td class="query-table-cell" style="background-color: #dfdfdf; border: 1px solid #dfdfdf; padding: 2px">
                <p>&nbsp;</p>
              </td>
              <td class="query-table-cell" style="background-color: #dfdfdf; border: 1px solid #dfdfdf; padding: 2px">
                <p>&nbsp;</p>
              </td>
              <td class="query-table-cell" style="background-color: #dfdfdf; border: 1px solid #dfdfdf; padding: 2px">
                <p>&nbsp;</p>
              </td>
              <td class="query-table-cell" style="background-color: #dfdfdf; border: 1px solid #dfdfdf; padding: 2px">
                <p>&nbsp;</p>
              </td>
              <td class="query-table-cell" style="background-color: #dfdfdf; border: 1px solid #dfdfdf; padding: 2px; text-align: right">
                <p>
                  <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                    ><strong
                      ><span
                        class="property"
                        data-property-display-label="Symbol"
                        data-property-data-type="String"
                        data-property-name="document.currency.symbol"
                        data-property-data-format=""
                        data-property-parent-context="SalesCreditMemoLine"
                        >{{document.currency.symbol}}</span
                      ><span
                        class="property"
                        data-property-display-label="Net price"
                        data-property-data-type="Decimal"
                        data-property-name="_blockAggregatedData.netPrice.sum"
                        data-property-data-format="2"
                        data-property-parent-context="SalesCreditMemoLine"
                        >{{formatNumber _blockAggregatedData.netPrice.sum 2}}</span
                      ></strong
                    ></span
                  >
                </p>
              </td>
              <td class="query-table-cell" style="background-color: #dfdfdf; border: 1px solid #dfdfdf; padding: 2px; text-align: right">
                <p>
                  <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                    ><strong
                      ><span
                        class="property"
                        data-property-display-label="Symbol"
                        data-property-data-type="String"
                        data-property-name="document.currency.symbol"
                        data-property-data-format=""
                        data-property-parent-context="SalesCreditMemoLine"
                        >{{document.currency.symbol}}</span
                      ><span
                        class="property"
                        data-property-display-label="Excl. tax"
                        data-property-data-type="Decimal"
                        data-property-name="_blockAggregatedData.amountExcludingTax.sum"
                        data-property-data-format="2"
                        data-property-parent-context="SalesCreditMemoLine"
                        >{{formatNumber _blockAggregatedData.amountExcludingTax.sum 2}}</span
                      ></strong
                    ></span
                  >
                </p>
              </td>
              <td class="query-table-cell" style="background-color: #dfdfdf; border: 1px solid #dfdfdf; padding: 2px">
                <p>&nbsp;</p>
              </td>
              <td class="query-table-cell" style="background-color: #dfdfdf; border: 1px solid #dfdfdf; padding: 2px; text-align: right">
                <p>
                  <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                    ><strong
                      ><span
                        class="property"
                        data-property-display-label="Symbol"
                        data-property-data-type="String"
                        data-property-name="document.currency.symbol"
                        data-property-data-format=""
                        data-property-parent-context="SalesCreditMemoLine"
                        >{{document.currency.symbol}}</span
                      ><span
                        class="property"
                        data-property-display-label="Incl. tax"
                        data-property-data-type="Decimal"
                        data-property-name="_blockAggregatedData.amountIncludingTax.sum"
                        data-property-data-format="2"
                        data-property-parent-context="SalesCreditMemoLine"
                        >{{formatNumber _blockAggregatedData.amountIncludingTax.sum 2}}</span
                      ></strong
                    ></span
                  >
                </p>
              </td>
            </tr>
            <!--{{/printBreakIfLast}}-->
            <tr class="query-table-row" data-hidden="1">
              <td class="query-table-cell" colspan="9">
                <p>&nbsp;</p>
              </td>
            </tr>
            <tr class="query-table-row" data-hidden="1">
              <td class="query-table-cell" colspan="9">
                <p>&nbsp;</p>
              </td>
            </tr>
            <!--{{/with}}{{/each}}-->
            <tr class="query-table-row" data-hidden="1">
              <td class="query-table-cell" colspan="9">
                <p>&nbsp;</p>
              </td>
            </tr>
          </tbody>
          <tfoot class="query-table-footer">
            <tr class="query-table-row">
              <td class="query-table-cell">
                <p>&nbsp;</p>
              </td>
              <td class="query-table-cell">
                <p>&nbsp;</p>
              </td>
              <td class="query-table-cell">
                <p>&nbsp;</p>
              </td>
              <td class="query-table-cell">
                <p>&nbsp;</p>
              </td>
              <td class="query-table-cell">
                <p>&nbsp;</p>
              </td>
              <td class="query-table-cell">
                <p>&nbsp;</p>
              </td>
              <td class="query-table-cell">
                <p>&nbsp;</p>
              </td>
              <td class="query-table-cell">
                <p>&nbsp;</p>
              </td>
              <td class="query-table-cell">
                <p>&nbsp;</p>
              </td>
            </tr>
          </tfoot>
        </table>
        <section class="unbreakable-block">
          <div class="unbreakable-block-body">
            <figure class="table" style="width: 100%">
              <table class="ck-table-resized">
                <colgroup>
                  <col style="width: 37.84%" />
                  <col style="width: 62.16%" />
                </colgroup>
                <tbody>
                  <tr>
                    <td>&nbsp;</td>
                    <td style="border-style: solid">
                      <table
                        class="query-table"
                        data-context-object-type="SalesCreditMemoTax"
                        data-context-object-path="taxes.query.edges"
                        data-context-filter="[]"
                        data-context-list-order='{"tax":"ascending","taxableAmount":"ascending","taxRate":"ascending","taxAmount":"ascending"}'
                        data-alias="MDpowSPm"
                      >
                        <thead class="query-table-head">
                          <tr class="query-table-row">
                            <td class="query-table-cell" style="background-color: #0000001a; border: 1px solid #dfdfdf; padding: 2px; vertical-align: top">
                              <p>
                                <span style="color: #198e59; font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                                  ><strong>{{ translatedContent "4b78ac8eb158840e9638a3aeb26c4a9d" }}</strong></span
                                >
                              </p>
                            </td>
                            <td class="query-table-cell" style="background-color: #0000001a; border: 1px solid #dfdfdf; padding: 2px; vertical-align: top">
                              <p>
                                <span style="color: #198e59; font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                                  ><strong>{{ translatedContent "59a99666437cbc6e878c5d4ef3d1c993" }}</strong></span
                                >
                              </p>
                            </td>
                            <td class="query-table-cell" style="background-color: #0000001a; border: 1px solid #dfdfdf; padding: 2px; vertical-align: top">
                              <p>
                                <span style="color: #198e59; font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                                  ><strong>{{ translatedContent "20a34c4e30c5bbe1d3f870ac55f0d831" }}</strong></span
                                >
                              </p>
                            </td>
                            <td class="query-table-cell" style="background-color: #0000001a; border: 1px solid #dfdfdf; padding: 2px; vertical-align: top">
                              <p>
                                <span style="color: #198e59; font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                                  ><strong>{{ translatedContent "617fe2857b30555c036a380e07ce16e3" }}</strong></span
                                >
                              </p>
                            </td>
                          </tr>
                        </thead>
                        <tbody class="query-table-body">
                          <!--{{#each MDpowSPm.query.edges}}{{#with node}}-->
                          <tr class="query-table-row">
                            <td class="query-table-cell" style="border: 1px solid #dfdfdf; padding: 2px; vertical-align: top">
                              <p>
                                <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                                  ><span
                                    class="property"
                                    data-property-display-label="Tax"
                                    data-property-data-type="String"
                                    data-property-name="tax"
                                    data-property-data-format=""
                                    data-property-parent-context="SalesCreditMemoTax"
                                    >{{tax}}</span
                                  ></span
                                >
                              </p>
                            </td>
                            <td class="query-table-cell" style="border: 1px solid #dfdfdf; padding: 2px; text-align: right; vertical-align: top">
                              <p>
                                <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                                  ><span
                                    class="property"
                                    data-property-display-label="Symbol"
                                    data-property-data-type="String"
                                    data-property-name="currency.symbol"
                                    data-property-data-format=""
                                    data-property-parent-context="SalesCreditMemoTax"
                                    >{{currency.symbol}}</span
                                  ><span
                                    class="property"
                                    data-property-display-label="Taxable amount"
                                    data-property-data-type="Decimal"
                                    data-property-name="taxableAmount"
                                    data-property-data-format="2"
                                    data-property-parent-context="SalesCreditMemoTax"
                                    >{{formatNumber taxableAmount 2}}</span
                                  ></span
                                >
                              </p>
                            </td>
                            <td class="query-table-cell" style="border: 1px solid #dfdfdf; padding: 2px; text-align: right; vertical-align: top">
                              <p>
                                <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                                  ><span
                                    class="property"
                                    data-property-display-label="Tax rate"
                                    data-property-data-type="Decimal"
                                    data-property-name="taxRate"
                                    data-property-data-format="2"
                                    data-property-parent-context="SalesCreditMemoTax"
                                    >{{formatNumber taxRate 2}}</span
                                  >{{ translatedContent "0bcef9c45bd8a48eda1b26eb0c61c869" }}</span
                                >
                              </p>
                            </td>
                            <td class="query-table-cell" style="border: 1px solid #dfdfdf; padding: 2px; text-align: right; vertical-align: top">
                              <p>
                                <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                                  ><span
                                    class="property"
                                    data-property-display-label="Symbol"
                                    data-property-data-type="String"
                                    data-property-name="currency.symbol"
                                    data-property-data-format=""
                                    data-property-parent-context="SalesCreditMemoTax"
                                    >{{currency.symbol}}</span
                                  ><span
                                    class="property"
                                    data-property-display-label="Tax amount"
                                    data-property-data-type="Decimal"
                                    data-property-name="taxAmount"
                                    data-property-data-format="2"
                                    data-property-parent-context="SalesCreditMemoTax"
                                    >{{formatNumber taxAmount 2}}</span
                                  ></span
                                >
                              </p>
                            </td>
                          </tr>
                          <tr class="query-table-row" data-hidden="1">
                            <td class="query-table-cell" colspan="4">
                              <p>&nbsp;</p>
                            </td>
                          </tr>
                          <!--{{/with}}{{/each}}-->
                          <tr class="query-table-row" data-hidden="1">
                            <td class="query-table-cell" colspan="4">
                              <p>&nbsp;</p>
                            </td>
                          </tr>
                        </tbody>
                        <tfoot class="query-table-footer">
                          <tr class="query-table-row">
                            <td class="query-table-cell">
                              <p>&nbsp;</p>
                            </td>
                            <td class="query-table-cell">
                              <p>&nbsp;</p>
                            </td>
                            <td class="query-table-cell">
                              <p>&nbsp;</p>
                            </td>
                            <td class="query-table-cell">
                              <p>&nbsp;</p>
                            </td>
                          </tr>
                        </tfoot>
                      </table>
                      <section class="unbreakable-block">
                        <div class="unbreakable-block-body">
                          <figure class="table">
                            <table>
                              <tbody>
                                <tr>
                                  <td style="background-color: #dfdfdf; border-style: solid; border-width: 1px">
                                    <span style="color: #198e59; font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                                      ><strong>{{ translatedContent "25b77b23fda3d051fb72598c952a82d7" }}</strong></span
                                    >
                                  </td>
                                  <td style="background-color: #dfdfdf; border-style: solid; border-width: 1px">
                                    <span style="color: #198e59; font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                                      ><strong>{{ translatedContent "ff9e6ead51b6937f49449b030045d815" }}</strong></span
                                    >
                                  </td>
                                  <td style="background-color: #dfdfdf; border-style: solid; border-width: 1px">
                                    <span style="color: #198e59; font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                                      ><strong>{{ translatedContent "ba12e59b20a9720f70fb907a55cd2620" }}</strong></span
                                    >
                                  </td>
                                </tr>
                                <tr>
                                  <td style="text-align: right">
                                    <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                                      ><span
                                        class="property"
                                        data-property-display-label="Symbol"
                                        data-property-data-type="String"
                                        data-property-name="currency.symbol"
                                        data-property-data-format=""
                                        data-property-parent-context="SalesCreditMemo"
                                        >{{currency.symbol}}</span
                                      ><span
                                        class="property"
                                        data-property-display-label="Total amount excluding tax"
                                        data-property-data-type="Decimal"
                                        data-property-name="totalAmountExcludingTax"
                                        data-property-data-format="2"
                                        data-property-parent-context="SalesCreditMemo"
                                        >{{formatNumber totalAmountExcludingTax 2}}</span
                                      ></span
                                    >
                                  </td>
                                  <td style="text-align: right">
                                    <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                                      ><span
                                        class="property"
                                        data-property-display-label="Symbol"
                                        data-property-data-type="String"
                                        data-property-name="currency.symbol"
                                        data-property-data-format=""
                                        data-property-parent-context="SalesCreditMemo"
                                        >{{currency.symbol}}</span
                                      ><span
                                        class="property"
                                        data-property-display-label="Total tax amount"
                                        data-property-data-type="Decimal"
                                        data-property-name="totalTaxAmount"
                                        data-property-data-format="2"
                                        data-property-parent-context="SalesCreditMemo"
                                        >{{formatNumber totalTaxAmount 2}}</span
                                      ></span
                                    >
                                  </td>
                                  <td style="text-align: right">
                                    <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                                      ><strong
                                        ><span
                                          class="property"
                                          data-property-display-label="Symbol"
                                          data-property-data-type="String"
                                          data-property-name="currency.symbol"
                                          data-property-data-format=""
                                          data-property-parent-context="SalesCreditMemo"
                                          >{{currency.symbol}}</span
                                        ><span
                                          class="property"
                                          data-property-display-label="Total amount including tax"
                                          data-property-data-type="Decimal"
                                          data-property-name="totalAmountIncludingTax"
                                          data-property-data-format="2"
                                          data-property-parent-context="SalesCreditMemo"
                                          >{{formatNumber totalAmountIncludingTax 2}}</span
                                        ></strong
                                      ></span
                                    >
                                  </td>
                                </tr>
                              </tbody>
                            </table>
                          </figure>
                        </div>
                      </section>
                    </td>
                  </tr>
                </tbody>
              </table>
            </figure>
          </div>
        </section>
        <figure class="table">
          <table>
            <tbody>
              <tr>
                <td style="text-align: center">
                  <section
                    class="conditional-block"
                    data-context-condition='[{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"1","value1":{"label":"NAF","data":{"name":"naf","title":"NAF","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"description","targetNode":"","isCollection":false,"kind":"SCALAR","label":"NAF","node":"String","iconType":"csv"},"id":"salesSite.legalCompany.naf","key":"salesSite.legalCompany.naf","labelKey":"NAF","labelPath":"Sales site > Legal company > NAF"},"value2":null,"key":"1","operator":"notEmpty"}]'
                  >
                    <!--{{#if ( not ( or ( eq salesSite.legalCompany.naf null ) ( eq salesSite.legalCompany.naf "" ) ( eq salesSite.legalCompany.naf undefined ) ) )}}-->
                    <div class="conditional-block-body">
                      <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                        ><strong
                          ><span
                            class="property"
                            data-property-display-label="Legal form"
                            data-property-data-type="Enum"
                            data-property-name="salesSite.legalCompany.legalForm"
                            data-property-data-format=""
                            data-property-parent-context="SalesCreditMemo"
                            >{{salesSite.legalCompany.legalForm}}</span
                          ><span
                            class="property"
                            data-property-display-label="Name"
                            data-property-data-type="String"
                            data-property-name="salesSite.legalCompany.name"
                            data-property-data-format=""
                            data-property-parent-context="SalesCreditMemo"
                            >{{salesSite.legalCompany.name}}</span
                          ></strong
                        ><span
                          class="property"
                          data-property-display-label="RCS"
                          data-property-data-type="String"
                          data-property-name="salesSite.legalCompany.rcs"
                          data-property-data-format=""
                          data-property-parent-context="SalesCreditMemo"
                          >{{salesSite.legalCompany.rcs}}</span
                        >{{ translatedContent "7137a85d9b8d9e6d09f47767d2ca31ee" }}<span
                          class="property"
                          data-property-display-label="NAF"
                          data-property-data-type="String"
                          data-property-name="salesSite.legalCompany.naf"
                          data-property-data-format=""
                          data-property-parent-context="SalesCreditMemo"
                          >{{salesSite.legalCompany.naf}}</span
                        ></span
                      >
                    </div>
                    <!--{{/if}}-->
                    <div class="conditional-block-footer">&nbsp;</div>
                  </section>
                  <section
                    class="conditional-block"
                    data-context-condition='[{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"1","value1":{"label":"NAF","data":{"name":"naf","title":"NAF","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"description","targetNode":"","isCollection":false,"kind":"SCALAR","label":"NAF","node":"String","iconType":"csv"},"id":"salesSite.legalCompany.naf","key":"salesSite.legalCompany.naf","labelKey":"NAF","labelPath":"Sales site > Legal company > NAF"},"value2":null,"key":"1","operator":"empty"}]'
                  >
                    <!--{{#if ( or ( eq salesSite.legalCompany.naf null ) ( eq salesSite.legalCompany.naf "" ) ( eq salesSite.legalCompany.naf undefined ) )}}-->
                    <div class="conditional-block-body">
                      <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                        ><strong
                          ><span
                            class="property"
                            data-property-display-label="Legal form"
                            data-property-data-type="Enum"
                            data-property-name="salesSite.legalCompany.legalForm"
                            data-property-data-format=""
                            data-property-parent-context="SalesCreditMemo"
                            >{{salesSite.legalCompany.legalForm}}</span
                          ><span
                            class="property"
                            data-property-display-label="Name"
                            data-property-data-type="String"
                            data-property-name="salesSite.legalCompany.name"
                            data-property-data-format=""
                            data-property-parent-context="SalesCreditMemo"
                            >{{salesSite.legalCompany.name}}</span
                          ></strong
                        ><span
                          class="property"
                          data-property-display-label="RCS"
                          data-property-data-type="String"
                          data-property-name="salesSite.legalCompany.rcs"
                          data-property-data-format=""
                          data-property-parent-context="SalesCreditMemo"
                          >{{salesSite.legalCompany.rcs}}</span
                        ></span
                      >
                    </div>
                    <!--{{/if}}-->
                    <div class="conditional-block-footer">&nbsp;</div>
                  </section>
                  <section
                    class="record-context"
                    data-context-object-type="CompanyAddress"
                    data-context-object-path="salesSite.legalCompany.addresses.query.edges.0.node"
                    data-context-filter="[]"
                    data-context-list-order="{}"
                    data-alias="vNmgtdmU"
                  >
                    <!--{{#with vNmgtdmU.legalCompany.addresses.query.edges.0.node}}-->
                    <div class="report-context-body">
                      <p>
                        <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                          ><span
                            class="property"
                            data-property-display-label="Address line 1"
                            data-property-data-type="String"
                            data-property-name="addressLine1"
                            data-property-data-format=""
                            data-property-parent-context="CompanyAddress"
                            >{{addressLine1}}</span
                          ><span
                            class="property"
                            data-property-display-label="Address line 2"
                            data-property-data-type="String"
                            data-property-name="addressLine2"
                            data-property-data-format=""
                            data-property-parent-context="CompanyAddress"
                            >{{addressLine2}}</span
                          ><span
                            class="property"
                            data-property-display-label="Postal code"
                            data-property-data-type="String"
                            data-property-name="postcode"
                            data-property-data-format=""
                            data-property-parent-context="CompanyAddress"
                            >{{postcode}}</span
                          ><span
                            class="property"
                            data-property-display-label="City"
                            data-property-data-type="String"
                            data-property-name="city"
                            data-property-data-format=""
                            data-property-parent-context="CompanyAddress"
                            >{{city}}</span
                          ><span
                            class="property"
                            data-property-display-label="Region"
                            data-property-data-type="String"
                            data-property-name="region"
                            data-property-data-format=""
                            data-property-parent-context="CompanyAddress"
                            >{{region}}</span
                          ><span
                            class="property"
                            data-property-display-label="Name"
                            data-property-data-type="String"
                            data-property-name="country.name"
                            data-property-data-format=""
                            data-property-parent-context="CompanyAddress"
                            >{{country.name}}</span
                          ></span
                        >
                      </p>
                    </div>
                    <!--{{/with}}--><span class="report-context-footer">&nbsp;</span>
                  </section>
                </td>
              </tr>
            </tbody>
          </table>
        </figure>
      </div>
      <!--{{/with}}--><span class="report-context-footer">&nbsp;</span>
    </section>
  </div>
</section>
