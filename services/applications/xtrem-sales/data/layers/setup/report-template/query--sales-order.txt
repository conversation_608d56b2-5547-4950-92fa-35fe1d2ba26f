{
  xtremSales {
    salesOrder {
      query(filter: "{_id:'{{order}}'}") {
        edges {
          node {
            taxEngine
            site {
              name
              primaryAddress {
                name
                addressLine1
                addressLine2
                city
                region
                postcode
                locationPhoneNumber
                country {
                  name
                  iso31661Alpha3
                }
              }
              siret
              taxIdNumber
              legalCompany {
                name
                addresses {
                  query(filter: "{isPrimary:true}") {
                    edges {
                      node {
                        name
                        addressLine1
                        addressLine2
                        city
                        region
                        postcode
                        country {
                          name
                          iso31661Alpha3
                        }
                      }
                    }
                  }
                }
                legalForm
                rcs
                naf
                siren
                legislation {
                  id
                }
              }
            }
            number
            date
            soldToCustomer {
              name
            }
            soldToAddress {
              name
              addressLine1
              addressLine2
              city
              region
              postcode
              country {
                name
                iso31661Alpha3
              }
            }
            shipToCustomer {
              name
            }
            shipToAddress {
              name
              addressLine1
              addressLine2
              city
              region
              postcode
              country {
                name
                iso31661Alpha3
              }
            }
            currency {
              symbol
            }
            customerNumber
            billToCustomer {
              name
              id
              businessEntity {
                siret
                taxIdNumber
              }
            }
            billToAddress {
              name
              addressLine1
              addressLine2
              city
              region
              postcode
              country {
                name
                iso31661Alpha3
              }
            }
            shippingDate
            incoterm {
              name
            }
            deliveryMode {
              name
            }
            paymentTerm {
              name
              businessEntityType
              description
            }
            externalNote{
              value
            }
            lines {
              query(first: 500) {
                edges {
                  node {
                    item {
                      id
                      name
                    }
                    itemDescription
                    quantity
                    unit {
                      name
                    }
                    grossPrice
                    discount
                    netPrice
                    amountExcludingTax
                    amountIncludingTax
                    taxes{
                      query{
                        edges{
                          node{
                            taxRate
                          }
                        }
                      }
                    }
                    externalNote{
                     value
                    }
                  }
                }
              }
            }
            totalAmountExcludingTax
            totalTaxAmount
            totalAmountIncludingTax
            taxes {
              query {
                edges {
                  node {
                    tax
                    taxRate
                    taxAmount
                    taxableAmount
                  }
                }
              }
            }

          }
        }
      }
    }
  }
}
