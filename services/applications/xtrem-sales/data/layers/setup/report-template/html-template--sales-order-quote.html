<div>
  <div>
    {{#with xtremSales.salesOrder.query.edges.0.node}}
    <table class="report-container">
      <thead class="report-header">
        <tr>
          <th class="normal-black">
            <div class="header-info" style="position: relative; top: -8px">
              <table>
                <tbody>
                  <tr>
                    <td class="column-left">
                      <strong>{{site.name}}</strong><br />
                      {{#with site.primaryAddress}} {{addressLine1}}<br />
                      {{#if addressLine2}}{{addressLine2}}<br />{{/if}} {{#if (is country.iso31661Alpha3 "GBR")}}{{city}}{{#if region}} {{region}}{{/if}}
                      {{postcode}}<br />
                      {{else}} {{#if (is country.iso31661Alpha3 "ZAF")}}{{city}}{{#if region}} {{region}}{{/if}} {{postcode}}<br />
                      {{else}} {{#if (is country.iso31661Alpha3 "USA")}}{{city}}{{#if region}} {{region}}{{/if}} {{postcode}}<br />
                      {{else}} {{postcode}} {{city}}<br />
                      {{/if}} {{/if}} {{/if}} {{country.name}} {{/with}} {{#if site.siret}}<br />{{ translatedContent "d04e2c1b67f3ef0d475409516b812e8b" }}
                      {{site.siret}}{{/if}} {{#if site.taxIdNumber}}<br />{{ translatedContent "37a7a58f6fdc66ce34c2bf56389a2aac" }} {{site.taxIdNumber}}{{/if}}
                    </td>
                    <td class="column-right">
                      <div style="position: relative; top: -20px">
                        <h1>{{ translatedContent "bb792d5c3b120967c4a133a67b57f205" }}</h1>
                        <br />
                      </div>
                      <div style="text-align: left; padding-left: 300px; position: relative; top: -15px">
                        {{ translatedContent "2a39f204d4eac05604c2059e5ac2f9f7" }}
                        <strong>{{number}}</strong><br />
                        {{ translatedContent "68ce0b3158f5665908aebc65128e0216" }}
                        <strong>{{formatDate date}}</strong>
                      </div>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </th>
        </tr>
      </thead>
      <tfoot class="report-footer">
        <tr>
          <td class="report-footer-cell">
            <div class="footer-info">
              <table class="header-table">
                <tbody>
                  <tr>
                    <td class="column-center" colspan="2">
                      <strong
                        >{{#if site.legalCompany.legalForm}}<span style="text-transform: uppercase">{{site.legalCompany.legalForm}}</span>
                        {{/if}}{{site.legalCompany.name}}</strong
                      >{{#if site.legalCompany.rcs}} {{site.legalCompany.rcs}}{{/if}}{{#if site.legalCompany.naf}} APE: {{site.legalCompany.naf}}{{/if}}<br />
                      {{#with site.legalCompany.addresses.query.edges.0.node}} {{addressLine1}} {{#if addressLine2}}{{addressLine2}}{{/if}} {{#if (is
                      country.iso31661Alpha3 "GBR")}}{{city}}{{#if region}} {{region}}{{/if}} {{postcode}} {{else}} {{#if (is country.iso31661Alpha3
                      "ZAF")}}{{city}}{{#if region}} {{region}}{{/if}} {{postcode}} {{else}} {{#if (is country.iso31661Alpha3 "USA")}}{{city}}{{#if region}}
                      {{region}}{{/if}} {{postcode}} {{else}} {{postcode}} {{city}} {{/if}} {{/if}} {{/if}} {{country.name}} {{/with}}
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </td>
        </tr>
      </tfoot>

      <tbody class="report-content">
        <tr>
          <td class="report-content-cell">
            <div class="main">
              <div class="address-frame"></div>
              <div>
                <div style="position: relative; top: -10px">
                  <table>
                    <thead>
                      <tr>
                        <th class="column-left">{{ translatedContent "7f126a80bed92b90edbf843371fcea9c" }}</th>
                        <th class="column-left">{{ translatedContent "1549a8f3c91b29a6ab37605ced6eacb3" }}</th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr>
                        <td style="width: 60%">
                          <strong>{{soldToCustomer.name}}</strong>
                          <br />
                          {{#with soldToAddress}} {{addressLine1}}<br />
                          {{#if addressLine2}}{{addressLine2}}<br />{{/if}} {{#if (is country.iso31661Alpha3 "GBR")}}{{city}}{{#if region}} {{region}}{{/if}}
                          {{postcode}}<br />
                          {{else}} {{#if (is country.iso31661Alpha3 "ZAF")}}{{city}}{{#if region}} {{region}}{{/if}} {{postcode}}<br />
                          {{else}} {{#if (is country.iso31661Alpha3 "USA")}}{{city}}{{#if region}} {{region}}{{/if}} {{postcode}}<br />
                          {{else}} {{postcode}} {{city}}<br />
                          {{/if}} {{/if}} {{/if}} {{country.name}} {{/with}}
                        </td>
                        <td>
                          <strong>{{shipToCustomer.name}}</strong>
                          <br />
                          {{#with shipToAddress}} {{addressLine1}}<br />
                          {{#if addressLine2}}{{addressLine2}}<br />{{/if}} {{#if (is country.iso31661Alpha3 "GBR")}}{{city}}{{#if region}} {{region}}{{/if}}
                          {{postcode}}<br />
                          {{else}} {{#if (is country.iso31661Alpha3 "ZAF")}}{{city}}{{#if region}} {{region}}{{/if}} {{postcode}}<br />
                          {{else}} {{#if (is country.iso31661Alpha3 "USA")}}{{city}}{{#if region}} {{region}}{{/if}} {{postcode}}<br />
                          {{else}} {{postcode}} {{city}}<br />
                          {{/if}} {{/if}} {{/if}} {{country.name}} {{/with}}
                        </td>
                      </tr>
                    </tbody>
                  </table>
                </div>
                <div style="position: relative; top: 10px">
                  <p style="text-align: left">
                    {{ translatedContent "5eea367ea73b909880393bd1ae79fc67" }}
                    <strong>{{billToCustomer.name}}</strong> {{ translatedContent "e0f0b0564d3d29a93fad7a4178b7b1ca" }} {{billToCustomer.id}} {{#if
                    billToCustomer.businessEntity.siret}}<br />{{ translatedContent "d04e2c1b67f3ef0d475409516b812e8b" }}
                    {{billToCustomer.businessEntity.siret}}{{/if}} {{#if billToCustomer.businessEntity.taxIdNumber}}<br />{{ translatedContent
                    "37a7a58f6fdc66ce34c2bf56389a2aac" }} {{billToCustomer.businessEntity.taxIdNumber}}{{/if}}
                  </p>
                </div>
                <div style="position: relative; top: 5px"><br /></div>
                <div class="avoidBreakInside bottomSpace">
                  <table class="lines-table">
                    <thead>
                      <tr>
                        <th class="column-left">{{ translatedContent "5c0a951f54c0c7bf4c66dd2e6d0fe2de" }}</th>
                        <th class="column-left">{{ translatedContent "201bdb87f8e278b943d07ae924d5de6e" }}</th>
                        <th class="column-left">{{ translatedContent "a5b3fb70bcf24e1800c3beef76de3bc5" }}</th>
                        <th class="column-left">{{ translatedContent "0b8f0c09c020c7079eae810280952aee" }}</th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr>
                        <td class="column-left">{{formatDate shippingDate}}</td>
                        <td class="column-left">{{deliveryMode.name}}</td>
                        <td class="column-left">{{incoterm.name}}</td>
                        <td class="column-left">{{paymentTerm.name}}</td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </div>
              <div>
                <div style="position: relative; top: 5px"><br /></div>
                <div class="avoidBreakInside bottomSpace">
                  <table class="lines-table">
                    <thead>
                      <tr>
                        <th class="column-left">{{ translatedContent "7d74f3b92b19da5e606d737d339a9679" }}</th>
                        <th class="column-left">{{ translatedContent "b5a7adde1af5c87d7fd797b6245c2a39" }}</th>
                        <th class="column-left">{{ translatedContent "694e8d1f2ee056f98ee488bdc4982d73" }}</th>
                        <th class="column-left">{{ translatedContent "19c562a36aeb455d09534f93b4f5236f" }}</th>
                        <th class="column-right">{{ translatedContent "0170ef61ec11c8b9dcf4d4a63782515c" }}</th>
                        <th class="column-left">{{ translatedContent "104d9898c04874d0fbac36e125fa1369" }}</th>
                        <th class="column-right">{{ translatedContent "9554ce40e7959ce198210376c97150b3" }}</th>
                        <th class="column-right">{{ translatedContent "e2773b5f3378c679f529106712a9b132" }}</th>
                        {{#if (isnt taxEngine "avalaraAvaTax")}}
                        <th class="column-right">{{ translatedContent "20a34c4e30c5bbe1d3f870ac55f0d831" }}</th>
                        <th class="column-right">{{ translatedContent "dd8085978ee9ea0602122eb2e2983ab3" }}</th>
                        {{/if}}
                      </tr>
                    </thead>
                    <tbody>
                      {{#each lines.query.edges}}
                      <tr>
                        <td class="column-left">{{node.item.id}}</td>
                        <td class="column-left">{{node.itemDescription}}</td>
                        <td class="column-right">{{formatNumber node.quantity 2}}</td>
                        <td class="column-left">{{node.unit.name}}</td>
                        <td class="column-right">{{formatNumber node.grossPrice 2}} {{../currency.symbol}}</td>
                        <td class="column-right">{{formatNumber node.discount 2}}{{ translatedContent "0bcef9c45bd8a48eda1b26eb0c61c869" }}</td>
                        <td class="column-right">{{formatNumber node.netPrice 2}} {{../currency.symbol}}</td>
                        <td class="column-right">{{formatNumber node.amountExcludingTax 2}} {{../currency.symbol}}</td>
                        {{#if (isnt ../taxEngine "avalaraAvaTax")}}
                        <td class="column-right">
                          {{formatNumber node.taxes.query.edges.0.node.taxRate 2}}{{ translatedContent "0bcef9c45bd8a48eda1b26eb0c61c869" }}
                        </td>
                        <td class="column-right">{{formatNumber node.amountIncludingTax 2}} {{../currency.symbol}}</td>
                        {{/if}}
                      </tr>
                      {{#if node.externalNote.value}}
                      <tr>
                        <td class="column-center">
                          <p>
                            <b>{{ translatedContent "fc9d3df613063ee57906c461320744e2" }}</b>
                          </p>
                        </td>
                        <td class="column-left" colspan="9">{{{node.externalNote.value}}}</td>
                      </tr>
                      {{/if}} {{/each}}
                    </tbody>
                  </table>
                </div>
              </div>
              <div class="article">
                <table class="header-table">
                  <tbody>
                    <tr>
                      <td class="column-left" width="40%">
                        {{#if (is site.legalCompany.legislation.id "FR")}}
                        <p>
                          {{ translatedContent "451af268818e3beece67b76d45d10927" }}
                          <br />{{ translatedContent "20dd7bc9f2831afb8b16ced2b5b770c7" }} <br />{{ translatedContent "edd0ef9a0be2114268a333224b95a8e2" }}
                        </p>
                        {{/if}}
                      </td>
                      <td class="column-right">
                        {{#if (isnt taxEngine "avalaraAvaTax")}}
                        <table class="header-table">
                          <thead>
                            <tr>
                              <th class="column-left">{{ translatedContent "4b78ac8eb158840e9638a3aeb26c4a9d" }}</th>
                              <th class="column-left">{{ translatedContent "095a1b43effec73955e31e790438de49" }}</th>
                              <th class="column-left">{{ translatedContent "20a34c4e30c5bbe1d3f870ac55f0d831" }}</th>
                              <th class="column-left">{{ translatedContent "b2f40690858b404ed10e62bdf422c704" }}</th>
                            </tr>
                          </thead>
                          <tbody>
                            {{#each taxes.query.edges}}
                            <tr>
                              <td class="column-left">{{node.tax}}</td>
                              <td class="column-right">{{formatNumber node.taxableAmount 2}} {{../currency.symbol}}</td>
                              <td class="column-right">{{formatNumber node.taxRate 2}}{{ translatedContent "0bcef9c45bd8a48eda1b26eb0c61c869" }}</td>
                              <td class="column-right">{{formatNumber node.taxAmount 2}} {{../currency.symbol}}</td>
                            </tr>
                            <tr>
                              <td colspan="4" class="column-left">{{node.taxReference.legalMention}}</td>
                            </tr>
                            {{/each}}
                          </tbody>
                        </table>
                        {{/if}}
                        <table class="header-table">
                          <thead>
                            <tr>
                              <th class="column-left">{{ translatedContent "d41cb50311de5b1572758d2233c1a66a" }}</th>
                              {{#if (isnt taxEngine "avalaraAvaTax")}}
                              <th class="column-left">{{ translatedContent "c87c9e5cac56d73f55fab44ab6c01c64" }}</th>
                              <th class="column-left">{{ translatedContent "feaec14f665d58545b3630df9b07fb6b" }}</th>
                              {{/if}}
                              <th class="column-left">{{ translatedContent "794f78b2fa875549a7cfc9753718fb24" }}</th>
                            </tr>
                          </thead>
                          <tbody>
                            <tr>
                              <td class="column-right">{{formatNumber totalAmountExcludingTax 2}} {{currency.symbol}}</td>
                              {{#if (isnt taxEngine "avalaraAvaTax")}}
                              <td class="column-right">{{formatNumber totalTaxAmount 2}} {{currency.symbol}}</td>
                              <td class="column-right">{{formatNumber totalAmountIncludingTax 2}} {{currency.symbol}}</td>
                              {{/if}}
                              <td class="column-right">
                                <strong>{{formatNumber totalAmountIncludingTax 2}} {{currency.symbol}}</strong>
                              </td>
                            </tr>
                          </tbody>
                        </table>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </td>
        </tr>
        <tr>
          <td>
            {{#if externalNote.value}}
            <table style="width: 100%" border="0">
              <tbody>
                <tr>
                  <td style="text-align: center; width: 5%; border: 0">
                    <p><b>{{ translatedContent "fc9d3df613063ee57906c461320744e2" }}</b></p>
                  </td>
                  <td style="text-align: left; border: 0">{{{externalNote.value}}}</td>
                </tr>
              </tbody>
            </table>
            {{/if}}
          </td>
        </tr>
      </tbody>
    </table>
    {{/with}}
  </div>
</div>
