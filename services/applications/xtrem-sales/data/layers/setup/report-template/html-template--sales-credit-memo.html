<div>
  {{#if codeBlockResult.isCreditMemoDraft}} {{#if (is xtremSales.salesCreditMemo.query.edges.0.node.billToAddress.country.iso31661Alpha3 "FRA")}}
  <div class="watermark">{{ translatedContent "540e360892172e34f1d10ba632cebb01" }}</div>
  {{else}}
  <div class="watermark">{{ translatedContent "f03ab16cd58372c77ba45a3d9a5a1cb9" }}</div>
  {{/if}} {{else if xtremSales.salesCreditMemo.query.edges.0.node.isPrinted}} {{#if (is
  xtremSales.salesInvoice.query.edges.0.node.billToAddress.country.iso31661Alpha3 "FRA")}}
  <div class="watermark">{{ translatedContent "6cfe95a54561410a8acbaf655296bd7b" }}</div>
  {{else}}
  <div class="watermark">{{ translatedContent "ed75712b0eb1913c28a3872731ffd48d" }}</div>
  {{/if}} {{else}}
  <div>
    {{/if}} {{#with xtremSales.salesCreditMemo.query.edges.0.node}}
    <table class="report-container">
      <thead class="report-header">
        <tr>
          <th class="normal-black">
            <div class="header-info" style="position: relative; top: -8px">
              <table>
                <tbody>
                  <tr>
                    <td class="column-left">
                      <strong>{{salesSite.name}}</strong><br />
                      {{#with salesSiteAddress}} {{addressLine1}}<br />
                      {{#if addressLine2}}{{addressLine2}}<br />{{/if}} {{postcode}} {{city}} {{region}}<br />
                      {{country.name}} {{/with}} {{#if salesSite.siret}}<br />{{ translatedContent "d04e2c1b67f3ef0d475409516b812e8b" }}
                      {{salesSite.siret}}{{/if}} {{#if salesSite.taxIdNumber}}<br />{{ translatedContent "9484d9e54beb6cc09ea063b26a0b6aa7" }}
                      {{salesSite.taxIdNumber}}{{/if}}
                    </td>
                    <td class="column-right">
                      <div style="position: relative; top: -20px">
                        <h1>{{ translatedContent "48ff14cbdaebf2cac64e8adade5b1d77" }}</h1>
                        <br />
                      </div>
                      <div style="text-align: left; padding-left: 300px; position: relative; top: -15px">
                        {{ translatedContent "2a39f204d4eac05604c2059e5ac2f9f7" }}
                        <strong>{{number}}</strong><br />
                        {{ translatedContent "664a94f92e44bfcbc136dff160551b7b" }}
                        <strong>{{formatDate date}}</strong>
                      </div>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </th>
        </tr>
      </thead>
      <tfoot class="report-footer">
        <tr>
          <td class="report-footer-cell">
            <div class="footer-info">
              <table class="header-table">
                <tbody>
                  <tr>
                    <td class="column-center">
                      <strong
                        >{{#if salesSite.legalCompany.legalForm}}<span style="text-transform: uppercase">{{salesSite.legalCompany.legalForm}}</span>
                        {{/if}}{{salesSite.legalCompany.name}}</strong
                      >{{#if salesSite.legalCompany.rcs}} {{salesSite.legalCompany.rcs}}{{/if}}{{#if salesSite.legalCompany.naf}} APE:
                      {{salesSite.legalCompany.naf}}{{/if}}<br />
                      {{#with salesSite.legalCompany.addresses.query.edges.0.node}} {{addressLine1}} {{addressLine2}} {{postcode}} {{city}} {{region}}
                      {{country.name}} {{/with}}
                    </td>
                  </tr>
                </tbody>
              </table>
              {{#if externalNote.value}}
              <table style="width: 100%">
                <tbody>
                  <tr>
                    <td style="text-align: center; width: 5%; border: 0">
                      <p><b>{{ translatedContent "fc9d3df613063ee57906c461320744e2" }}</b></p>
                    </td>
                    <td style="text-align: left; border: 0">{{{externalNote.value}}}</td>
                  </tr>
                </tbody>
              </table>
              {{/if}}
            </div>
          </td>
        </tr>
      </tfoot>
      <tbody class="report-content">
        <tr>
          <td class="report-content-cell">
            <div class="main">
              <div class="address-frame"></div>
              <div>
                <div style="position: relative; top: -10px">
                  <table>
                    <thead>
                      <tr>
                        <th class="column-left">{{ translatedContent "84e839915aa016435941dda50c367e0c" }}</th>
                        <th class="column-left">{{ translatedContent "6fd791a834ec2f5a235b58bdea1746fc" }}</th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr>
                        <td style="width: 60%">
                          <strong>{{billToCustomer.name}}</strong>
                          <br />
                          {{#with lines.query.edges.0.node.consumptionAddress}} {{addressLine1}}<br />
                          {{#if addressLine2}}{{addressLine2}}<br />{{/if}} {{postcode}} {{city}} {{region}}<br />
                          {{country.name}} {{/with}}
                        </td>
                        <td>
                          <strong>{{billToCustomer.name}}</strong>
                          <br />
                          {{#with billToAddress}} {{addressLine1}}<br />
                          {{#if addressLine2}}{{addressLine2}}<br />{{/if}} {{postcode}} {{city}} {{region}}<br />
                          {{country.name}} {{/with}}
                        </td>
                      </tr>
                    </tbody>
                  </table>
                </div>
                <div style="position: relative; top: 10px">
                  <p style="text-align: left">
                    {{ translatedContent "5eea367ea73b909880393bd1ae79fc67" }}
                    <strong>{{billToCustomer.name}}</strong> {{ translatedContent "e0f0b0564d3d29a93fad7a4178b7b1ca" }} {{billToCustomer.id}} {{#if
                    billToCustomer.businessEntity.siret}}<br />{{ translatedContent "d04e2c1b67f3ef0d475409516b812e8b" }}
                    {{billToCustomer.businessEntity.siret}}{{/if}} {{#if billToCustomer.businessEntity.taxIdNumber}}<br />{{ translatedContent
                    "9484d9e54beb6cc09ea063b26a0b6aa7" }} {{billToCustomer.businessEntity.taxIdNumber}}{{/if}}
                  </p>
                </div>
                <div style="position: relative; top: 5px"><br /></div>
                <div class="avoidBreakInside bottomSpace">
                  <table class="lines-table">
                    <thead>
                      <tr>
                        <th class="column-left">{{ translatedContent "466eadd40b3c10580e3ab4e8061161ce" }}</th>
                        <th class="column-left">{{ translatedContent "7d74f3b92b19da5e606d737d339a9679" }}</th>
                        <th class="column-left">{{ translatedContent "b5a7adde1af5c87d7fd797b6245c2a39" }}</th>
                        <th class="column-right">{{ translatedContent "694e8d1f2ee056f98ee488bdc4982d73" }}</th>
                        <th class="column-left">{{ translatedContent "19c562a36aeb455d09534f93b4f5236f" }}</th>
                        <th class="column-right">{{ translatedContent "9554ce40e7959ce198210376c97150b3" }}</th>
                        <th class="column-right">{{ translatedContent "d41cb50311de5b1572758d2233c1a66a" }}</th>
                        <th class="column-right">{{ translatedContent "20a34c4e30c5bbe1d3f870ac55f0d831" }}</th>
                        <th class="column-right">{{ translatedContent "0361f1d89f6da06f7ecbaff9290dacdf" }}</th>
                      </tr>
                    </thead>
                    <tbody>
                      {{#each lines.query.edges}}
                      <tr>
                        <td class="column-left">{{node.toInvoiceLines.query.edges.0.node.linkedDocument.document.number}}</td>
                        <td class="column-left">{{node.item.id}}</td>
                        <td class="column-left">{{node.itemDescription}}</td>
                        <td class="column-right">{{formatNumber node.quantity 2}}</td>
                        <td class="column-left">{{node.unit.name}}</td>
                        <td class="column-right">{{formatNumber node.netPrice 2}} {{../currency.symbol}}</td>
                        <td class="column-right">{{formatNumber node.amountExcludingTax 2}} {{../currency.symbol}}</td>
                        <td class="column-right">
                          {{formatNumber node.taxes.query.edges.0.node.taxRate 2}}{{ translatedContent "0bcef9c45bd8a48eda1b26eb0c61c869" }}
                        </td>
                        <td class="column-right">{{formatNumber node.amountIncludingTax 2}} {{../currency.symbol}}</td>
                      </tr>
                      {{/each}}
                    </tbody>
                  </table>
                </div>
              </div>
              <div class="article">
                <table class="header-table">
                  <tbody>
                    <tr>
                      <td style="width: 50%"></td>
                      <td class="column-right">
                        <table class="header-table">
                          <thead>
                            <tr>
                              <th class="column-left">{{ translatedContent "4b78ac8eb158840e9638a3aeb26c4a9d" }}</th>
                              <th class="column-left">{{ translatedContent "59a99666437cbc6e878c5d4ef3d1c993" }}</th>
                              <th class="column-left">{{ translatedContent "20a34c4e30c5bbe1d3f870ac55f0d831" }}</th>
                              <th class="column-left">{{ translatedContent "617fe2857b30555c036a380e07ce16e3" }}</th>
                            </tr>
                          </thead>
                          <tbody>
                            {{#each taxes.query.edges}}
                            <tr>
                              <td class="column-left">{{node.tax}}</td>
                              <td class="column-right">{{formatNumber node.taxableAmount 2}} {{../currency.symbol}}</td>
                              <td class="column-right">{{formatNumber node.taxRate 2}}{{ translatedContent "0bcef9c45bd8a48eda1b26eb0c61c869" }}</td>
                              <td class="column-right">{{formatNumber node.taxAmount 2}} {{../currency.symbol}}</td>
                            </tr>
                            <tr>
                              <td colspan="4" class="column-left">{{node.taxReference.legalMention}}</td>
                            </tr>
                            {{/each}}
                          </tbody>
                        </table>
                        <table class="header-table">
                          <thead>
                            <tr>
                              <th class="column-left">{{ translatedContent "d41cb50311de5b1572758d2233c1a66a" }}</th>
                              <th class="column-left">{{ translatedContent "ff9e6ead51b6937f49449b030045d815" }}</th>
                              <th class="column-left">{{ translatedContent "feaec14f665d58545b3630df9b07fb6b" }}</th>
                            </tr>
                          </thead>
                          <tbody>
                            <tr>
                              <td class="column-right">{{formatNumber totalAmountExcludingTax 2}} {{currency.symbol}}</td>
                              <td class="column-right">{{formatNumber totalTaxAmount 2}} {{currency.symbol}}</td>
                              <td class="column-right">{{formatNumber totalAmountIncludingTax 2}} {{currency.symbol}}</td>
                            </tr>
                          </tbody>
                        </table>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </td>
        </tr>
      </tbody>
    </table>
    {{/with}}
  </div>
</div>
