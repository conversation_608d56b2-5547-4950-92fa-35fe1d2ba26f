<section class="record-context" data-context-object-type="SalesShipment"
  data-context-object-path="xtremSales.salesShipment.query.edges.0.node"
  data-context-filter='[{"_id":"1","label":"_id","filterType":"matches","filterValue":"shipment","data":{"name":"_id","title":"ID","canSort":true,"canFilter":true,"type":"IntOrString","isCustom":false,"isMutable":false,"isStored":true,"isOnInputType":false,"isOnOutputType":true,"enumType":null,"dataType":"","targetNode":"","isCollection":false,"kind":"SCALAR","label":"_id","iconType":"csv"},"id":"_id","labelPath":"_id","property":{"name":"_id","title":"ID","canSort":true,"canFilter":true,"type":"IntOrString","isCustom":false,"isMutable":false,"isStored":true,"isOnInputType":false,"isOnOutputType":true,"enumType":null,"dataType":"","targetNode":"","isCollection":false,"kind":"SCALAR","label":"_id","iconType":"csv","id":"_id","data":{"name":"_id","title":"ID","canSort":true,"canFilter":true,"type":"IntOrString","isCustom":false,"isMutable":false,"isStored":true,"isOnInputType":false,"isOnOutputType":true,"enumType":null,"dataType":"","targetNode":"","isCollection":false,"kind":"SCALAR","label":"_id","iconType":"csv"},"key":"_id","labelKey":"_id","labelPath":"_id"},"parameter":true}]'
  data-context-list-order="{}" data-alias="XwAeLdnM">
  <!--{{#with XwAeLdnM.salesShipment.query.edges.0.node}}-->
  <div class="report-context-body">
    <figure class="table">
      <table>
        <tbody>
          <tr>
            <td style="background-color: #dfdfdf; text-align: right">
              <h2>
                <span style="color: #198e59; font-family: 'Sage UI', Geneva, sans-serif"><strong>{{ translatedContent
                    "4dc2dd5c2a26d089c2b5c1acf0decaa3" }}</strong></span>
              </h2>
              <p>
                <span style="color: #198e59; font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"><strong>{{
                    translatedContent "2a39f204d4eac05604c2059e5ac2f9f7" }}&nbsp;</strong></span><span
                  style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"><strong><span class="property"
                      data-property-display-label="Number" data-property-data-type="String" data-property-name="number"
                      data-property-data-format=""
                      data-property-parent-context="SalesShipment">{{number}}</span></strong></span><span
                  style="color: #198e59; font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"><strong>&nbsp;</strong></span>
              </p>
              <p>
                <span style="color: #198e59; font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"><strong>{{
                    translatedContent "a3a79b28bc45b7fd393effd056b917e1" }}&nbsp;</strong></span><span
                  style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"><strong><span class="property"
                      data-property-display-label="Shipping date" data-property-data-type="Date"
                      data-property-name="date" data-property-data-format="FullDate"
                      data-property-parent-context="SalesShipment">{{formatDate date 'FullDate'}}</span></strong></span>
              </p>
            </td>
          </tr>
        </tbody>
      </table>
    </figure>
    <figure class="table" style="width: 100%">
      <table class="ck-table-resized">
        <colgroup>
          <col style="width: 47.95%" />
          <col style="width: 52.05%" />
        </colgroup>
        <tbody>
          <tr>
            <td style="background-color: #dfdfdf; border-color: #dfdfdf">
              <span style="color: #198e59; font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"><strong>{{
                  translatedContent "5390da28367249488d76646a531ab249" }}</strong></span>
            </td>
            <td style="background-color: #dfdfdf; border-color: #dfdfdf">&nbsp;</td>
          </tr>
          <tr>
            <td style="border-color: transparent">
              <section class="conditional-block"
                data-context-condition='[{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"1","value1":{"label":"Name","data":{"name":"name","title":"Name","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isStored":true,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"name","targetNode":"","isCollection":false,"kind":"SCALAR","label":"Name","node":"String","iconType":"csv"},"id":"shipToAddress.name","key":"shipToAddress.name","labelKey":"Name","labelPath":"Ship-to address > Name"},"value2":null,"key":"1","operator":"notEmpty"}]'>
                <!--{{#if ( not ( or ( eq shipToAddress.name null ) ( eq shipToAddress.name "" ) ( eq shipToAddress.name undefined ) ) )}}-->
                <div class="conditional-block-body">
                  <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"><strong><span
                        class="property" data-property-display-label="Name" data-property-data-type="String"
                        data-property-name="shipToCustomerAddress.name" data-property-data-format=""
                        data-property-parent-context="SalesShipment">{{shipToCustomerAddress.name}}</span></strong></span>
                </div>
                <!--{{/if}}-->
                <div class="conditional-block-footer">&nbsp;</div>
              </section>
              <section class="conditional-block"
                data-context-condition='[{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"1","value1":{"label":"Address line 1","data":{"name":"addressLine1","title":"Address line 1","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isStored":true,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"addressLineDataType","targetNode":"","isCollection":false,"kind":"SCALAR","label":"Address line 1","node":"String","iconType":"csv"},"id":"shipToAddress.addressLine1","key":"shipToAddress.addressLine1","labelKey":"Address line 1","labelPath":"Ship-to address > Address line 1"},"value2":null,"key":"1","operator":"notEmpty"}]'>
                <!--{{#if ( not ( or ( eq shipToAddress.addressLine1 null ) ( eq shipToAddress.addressLine1 "" ) ( eq shipToAddress.addressLine1 undefined ) ) )}}-->
                <div class="conditional-block-body">
                  <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"><span class="property"
                      data-property-display-label="Address line 1" data-property-data-type="String"
                      data-property-name="shipToAddress.addressLine1" data-property-data-format=""
                      data-property-parent-context="SalesShipment">{{shipToAddress.addressLine1}}</span></span>
                </div>
                <!--{{/if}}-->
                <div class="conditional-block-footer">&nbsp;</div>
              </section>
              <section class="conditional-block"
                data-context-condition='[{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"1","value1":{"label":"Address line 2","data":{"name":"addressLine2","title":"Address line 2","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isStored":true,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"addressLineDataType","targetNode":"","isCollection":false,"kind":"SCALAR","label":"Address line 2","node":"String","iconType":"csv"},"id":"shipToAddress.addressLine2","key":"shipToAddress.addressLine2","labelKey":"Address line 2","labelPath":"Ship-to address > Address line 2"},"value2":null,"key":"1","operator":"notEmpty"}]'>
                <!--{{#if ( not ( or ( eq shipToAddress.addressLine2 null ) ( eq shipToAddress.addressLine2 "" ) ( eq shipToAddress.addressLine2 undefined ) ) )}}-->
                <div class="conditional-block-body">
                  <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"><span class="property"
                      data-property-display-label="Address line 2" data-property-data-type="String"
                      data-property-name="shipToAddress.addressLine2" data-property-data-format=""
                      data-property-parent-context="SalesShipment">{{shipToAddress.addressLine2}}</span></span>
                </div>
                <!--{{/if}}-->
                <div class="conditional-block-footer">&nbsp;</div>
              </section>
              <section class="conditional-block"
                data-context-condition='[{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"1","value1":{"label":"ISO 3166-1 alpha 3","data":{"name":"iso31661Alpha3","title":"ISO 3166-1 alpha 3","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isStored":true,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"","targetNode":"","isCollection":false,"kind":"SCALAR","label":"ISO 3166-1 alpha 3","node":"String","iconType":"csv"},"id":"shipToAddress.country.iso31661Alpha3","key":"shipToAddress.country.iso31661Alpha3","labelKey":"ISO 3166-1 alpha 3","labelPath":"Ship-to address > Country > ISO 3166-1 alpha 3"},"value2":"GBR","key":"1","operator":"equals"},{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"2","value1":{"label":"Region","data":{"name":"region","title":"Region","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isStored":true,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"regionDataType","targetNode":"","isCollection":false,"kind":"SCALAR","label":"Region","node":"String","iconType":"csv"},"id":"shipToAddress.region","key":"shipToAddress.region","labelKey":"Region","labelPath":"Ship-to address > Region"},"value2":null,"key":"2","operator":"notEmpty"},{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"3","value1":{"label":"City","data":{"name":"city","title":"City","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isStored":true,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"cityDataType","targetNode":"","isCollection":false,"kind":"SCALAR","label":"City","node":"String","iconType":"csv"},"id":"shipToAddress.city","key":"shipToAddress.city","labelKey":"City","labelPath":"Ship-to address > City"},"value2":null,"key":"3","operator":"notEmpty"},{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"4","value1":{"label":"Postal code","data":{"name":"postcode","title":"Postal code","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isStored":true,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"postcodeDataType","targetNode":"","isCollection":false,"kind":"SCALAR","label":"Postal code","node":"String","iconType":"csv"},"id":"shipToAddress.postcode","key":"shipToAddress.postcode","labelKey":"Postal code","labelPath":"Ship-to address > Postal code"},"value2":null,"key":"4","operator":"notEmpty"}]'>
                <!--{{#if ( and ( eq shipToAddress.country.iso31661Alpha3 "GBR" ) ( not ( or ( eq shipToAddress.region null ) ( eq shipToAddress.region "" ) ( eq shipToAddress.region undefined ) ) ) ( not ( or ( eq shipToAddress.city null ) ( eq shipToAddress.city "" ) ( eq shipToAddress.city undefined ) ) ) ( not ( or ( eq shipToAddress.postcode null ) ( eq shipToAddress.postcode "" ) ( eq shipToAddress.postcode undefined ) ) ) )}}-->
                <div class="conditional-block-body">
                  <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"><span class="property"
                      data-property-display-label="City" data-property-data-type="String"
                      data-property-name="shipToAddress.city" data-property-data-format=""
                      data-property-parent-context="SalesShipment">{{shipToAddress.city}}</span><span class="property"
                      data-property-display-label="Region" data-property-data-type="String"
                      data-property-name="shipToAddress.region" data-property-data-format=""
                      data-property-parent-context="SalesShipment">{{shipToAddress.region}}</span><span class="property"
                      data-property-display-label="Postal code" data-property-data-type="String"
                      data-property-name="shipToAddress.postcode" data-property-data-format=""
                      data-property-parent-context="SalesShipment">{{shipToAddress.postcode}}</span></span>
                </div>
                <!--{{/if}}-->
                <div class="conditional-block-footer">&nbsp;</div>
              </section>
              <section class="conditional-block"
                data-context-condition='[{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"1","value1":{"label":"ISO 3166-1 alpha 3","data":{"name":"iso31661Alpha3","title":"ISO 3166-1 alpha 3","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isStored":true,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"","targetNode":"","isCollection":false,"kind":"SCALAR","label":"ISO 3166-1 alpha 3","node":"String","iconType":"csv"},"id":"shipToAddress.country.iso31661Alpha3","key":"shipToAddress.country.iso31661Alpha3","labelKey":"ISO 3166-1 alpha 3","labelPath":"Ship-to address > Country > ISO 3166-1 alpha 3"},"value2":"GBR","key":"1","operator":"equals"}]'>
                <!--{{#if ( eq shipToAddress.country.iso31661Alpha3 "GBR" )}}-->
                <div class="conditional-block-body">
                  <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"><span class="property"
                      data-property-display-label="City" data-property-data-type="String"
                      data-property-name="shipToAddress.city" data-property-data-format=""
                      data-property-parent-context="SalesShipment">{{shipToAddress.city}}</span><span class="property"
                      data-property-display-label="Region" data-property-data-type="String"
                      data-property-name="shipToAddress.region" data-property-data-format=""
                      data-property-parent-context="SalesShipment">{{shipToAddress.region}}</span><span class="property"
                      data-property-display-label="Postal code" data-property-data-type="String"
                      data-property-name="shipToAddress.postcode" data-property-data-format=""
                      data-property-parent-context="SalesShipment">{{shipToAddress.postcode}}</span></span>
                </div>
                <!--{{/if}}-->
                <div class="conditional-block-footer">&nbsp;</div>
              </section>
              <section class="conditional-block"
                data-context-condition='[{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"1","value1":{"label":"ISO 3166-1 alpha 3","data":{"name":"iso31661Alpha3","title":"ISO 3166-1 alpha 3","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isStored":true,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"","targetNode":"","isCollection":false,"kind":"SCALAR","label":"ISO 3166-1 alpha 3","node":"String","iconType":"csv"},"id":"shipToAddress.country.iso31661Alpha3","key":"shipToAddress.country.iso31661Alpha3","labelKey":"ISO 3166-1 alpha 3","labelPath":"Ship-to address > Country > ISO 3166-1 alpha 3"},"value2":"ZAF","key":"1","operator":"equals"},{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"2","value1":{"label":"City","data":{"name":"city","title":"City","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isStored":true,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"cityDataType","targetNode":"","isCollection":false,"kind":"SCALAR","label":"City","node":"String","iconType":"csv"},"id":"shipToAddress.city","key":"shipToAddress.city","labelKey":"City","labelPath":"Ship-to address > City"},"value2":null,"key":"2","operator":"notEmpty"},{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"3","value1":{"label":"Region","data":{"name":"region","title":"Region","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isStored":true,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"regionDataType","targetNode":"","isCollection":false,"kind":"SCALAR","label":"Region","node":"String","iconType":"csv"},"id":"shipToAddress.region","key":"shipToAddress.region","labelKey":"Region","labelPath":"Ship-to address > Region"},"value2":null,"key":"3","operator":"notEmpty"},{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"4","value1":{"label":"Postal code","data":{"name":"postcode","title":"Postal code","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isStored":true,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"postcodeDataType","targetNode":"","isCollection":false,"kind":"SCALAR","label":"Postal code","node":"String","iconType":"csv"},"id":"shipToAddress.postcode","key":"shipToAddress.postcode","labelKey":"Postal code","labelPath":"Ship-to address > Postal code"},"value2":null,"key":"4","operator":"notEmpty"}]'>
                <!--{{#if ( and ( eq shipToAddress.country.iso31661Alpha3 "ZAF" ) ( not ( or ( eq shipToAddress.city null ) ( eq shipToAddress.city "" ) ( eq shipToAddress.city undefined ) ) ) ( not ( or ( eq shipToAddress.region null ) ( eq shipToAddress.region "" ) ( eq shipToAddress.region undefined ) ) ) ( not ( or ( eq shipToAddress.postcode null ) ( eq shipToAddress.postcode "" ) ( eq shipToAddress.postcode undefined ) ) ) )}}-->
                <div class="conditional-block-body">
                  <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"><span class="property"
                      data-property-display-label="City" data-property-data-type="String"
                      data-property-name="shipToAddress.city" data-property-data-format=""
                      data-property-parent-context="SalesShipment">{{shipToAddress.city}}</span><span class="property"
                      data-property-display-label="Region" data-property-data-type="String"
                      data-property-name="shipToAddress.region" data-property-data-format=""
                      data-property-parent-context="SalesShipment">{{shipToAddress.region}}</span><span class="property"
                      data-property-display-label="Postal code" data-property-data-type="String"
                      data-property-name="shipToAddress.postcode" data-property-data-format=""
                      data-property-parent-context="SalesShipment">{{shipToAddress.postcode}}</span></span>
                </div>
                <!--{{/if}}-->
                <div class="conditional-block-footer">&nbsp;</div>
              </section>
              <section class="conditional-block"
                data-context-condition='[{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"1","value1":{"label":"ISO 3166-1 alpha 3","data":{"name":"iso31661Alpha3","title":"ISO 3166-1 alpha 3","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isStored":true,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"","targetNode":"","isCollection":false,"kind":"SCALAR","label":"ISO 3166-1 alpha 3","node":"String","iconType":"csv"},"id":"shipToAddress.country.iso31661Alpha3","key":"shipToAddress.country.iso31661Alpha3","labelKey":"ISO 3166-1 alpha 3","labelPath":"Ship-to address > Country > ISO 3166-1 alpha 3"},"value2":"USA","key":"1","operator":"equals"},{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"2","value1":{"label":"City","data":{"name":"city","title":"City","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isStored":true,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"cityDataType","targetNode":"","isCollection":false,"kind":"SCALAR","label":"City","node":"String","iconType":"csv"},"id":"shipToAddress.city","key":"shipToAddress.city","labelKey":"City","labelPath":"Ship-to address > City"},"value2":null,"key":"2","operator":"notEmpty"},{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"3","value1":{"label":"Region","data":{"name":"region","title":"Region","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isStored":true,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"regionDataType","targetNode":"","isCollection":false,"kind":"SCALAR","label":"Region","node":"String","iconType":"csv"},"id":"shipToAddress.region","key":"shipToAddress.region","labelKey":"Region","labelPath":"Ship-to address > Region"},"value2":null,"key":"3","operator":"notEmpty"},{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"4","value1":{"label":"Postal code","data":{"name":"postcode","title":"Postal code","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isStored":true,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"postcodeDataType","targetNode":"","isCollection":false,"kind":"SCALAR","label":"Postal code","node":"String","iconType":"csv"},"id":"shipToAddress.postcode","key":"shipToAddress.postcode","labelKey":"Postal code","labelPath":"Ship-to address > Postal code"},"value2":null,"key":"4","operator":"notEmpty"}]'>
                <!--{{#if ( and ( eq shipToAddress.country.iso31661Alpha3 "USA" ) ( not ( or ( eq shipToAddress.city null ) ( eq shipToAddress.city "" ) ( eq shipToAddress.city undefined ) ) ) ( not ( or ( eq shipToAddress.region null ) ( eq shipToAddress.region "" ) ( eq shipToAddress.region undefined ) ) ) ( not ( or ( eq shipToAddress.postcode null ) ( eq shipToAddress.postcode "" ) ( eq shipToAddress.postcode undefined ) ) ) )}}-->
                <div class="conditional-block-body">
                  <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"><span class="property"
                      data-property-display-label="City" data-property-data-type="String"
                      data-property-name="shipToAddress.city" data-property-data-format=""
                      data-property-parent-context="SalesShipment">{{shipToAddress.city}}</span><span class="property"
                      data-property-display-label="Region" data-property-data-type="String"
                      data-property-name="shipToAddress.region" data-property-data-format=""
                      data-property-parent-context="SalesShipment">{{shipToAddress.region}}</span><span class="property"
                      data-property-display-label="Postal code" data-property-data-type="String"
                      data-property-name="shipToAddress.postcode" data-property-data-format=""
                      data-property-parent-context="SalesShipment">{{shipToAddress.postcode}}</span></span>
                </div>
                <!--{{/if}}-->
                <div class="conditional-block-footer">&nbsp;</div>
              </section>
              <section class="conditional-block"
                data-context-condition='[{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"1","value1":{"label":"ISO 3166-1 alpha 3","data":{"name":"iso31661Alpha3","title":"ISO 3166-1 alpha 3","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isStored":true,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"","targetNode":"","isCollection":false,"kind":"SCALAR","label":"ISO 3166-1 alpha 3","node":"String","iconType":"csv"},"id":"shipToAddress.country.iso31661Alpha3","key":"shipToAddress.country.iso31661Alpha3","labelKey":"ISO 3166-1 alpha 3","labelPath":"Ship-to address > Country > ISO 3166-1 alpha 3"},"value2":"GBR","key":"1","operator":"notEqual"},{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"2","value1":{"label":"ISO 3166-1 alpha 3","data":{"name":"iso31661Alpha3","title":"ISO 3166-1 alpha 3","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isStored":true,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"","targetNode":"","isCollection":false,"kind":"SCALAR","label":"ISO 3166-1 alpha 3","node":"String","iconType":"csv"},"id":"shipToAddress.country.iso31661Alpha3","key":"shipToAddress.country.iso31661Alpha3","labelKey":"ISO 3166-1 alpha 3","labelPath":"Ship-to address > Country > ISO 3166-1 alpha 3"},"value2":"ZAF","key":"2","operator":"notEqual"},{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"3","value1":{"label":"ISO 3166-1 alpha 3","data":{"name":"iso31661Alpha3","title":"ISO 3166-1 alpha 3","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isStored":true,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"","targetNode":"","isCollection":false,"kind":"SCALAR","label":"ISO 3166-1 alpha 3","node":"String","iconType":"csv"},"id":"shipToAddress.country.iso31661Alpha3","key":"shipToAddress.country.iso31661Alpha3","labelKey":"ISO 3166-1 alpha 3","labelPath":"Ship-to address > Country > ISO 3166-1 alpha 3"},"value2":"USA","key":"3","operator":"notEqual"},{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"4","value1":{"label":"Postal code","data":{"name":"postcode","title":"Postal code","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isStored":true,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"postcodeDataType","targetNode":"","isCollection":false,"kind":"SCALAR","label":"Postal code","node":"String","iconType":"csv"},"id":"shipToAddress.postcode","key":"shipToAddress.postcode","labelKey":"Postal code","labelPath":"Ship-to address > Postal code"},"value2":null,"key":"4","operator":"notEmpty"},{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"5","value1":{"label":"City","data":{"name":"city","title":"City","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isStored":true,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"cityDataType","targetNode":"","isCollection":false,"kind":"SCALAR","label":"City","node":"String","iconType":"csv"},"id":"shipToAddress.city","key":"shipToAddress.city","labelKey":"City","labelPath":"Ship-to address > City"},"value2":null,"key":"5","operator":"notEmpty"}]'>
                <!--{{#if ( and ( not ( eq shipToAddress.country.iso31661Alpha3 "GBR" ) ) ( not ( eq shipToAddress.country.iso31661Alpha3 "ZAF" ) ) ( not ( eq shipToAddress.country.iso31661Alpha3 "USA" ) ) ( not ( or ( eq shipToAddress.postcode null ) ( eq shipToAddress.postcode "" ) ( eq shipToAddress.postcode undefined ) ) ) ( not ( or ( eq shipToAddress.city null ) ( eq shipToAddress.city "" ) ( eq shipToAddress.city undefined ) ) ) )}}-->
                <div class="conditional-block-body">
                  <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"><span class="property"
                      data-property-display-label="Postal code" data-property-data-type="String"
                      data-property-name="shipToAddress.postcode" data-property-data-format=""
                      data-property-parent-context="SalesShipment">{{shipToAddress.postcode}}</span><span
                      class="property" data-property-display-label="City" data-property-data-type="String"
                      data-property-name="shipToAddress.city" data-property-data-format=""
                      data-property-parent-context="SalesShipment">{{shipToAddress.city}}</span></span>
                </div>
                <!--{{/if}}-->
                <div class="conditional-block-footer">&nbsp;</div>
              </section>
              <section class="conditional-block"
                data-context-condition='[{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"1","value1":{"label":"Name","data":{"name":"name","title":"Name","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isStored":true,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"localizedCountryPropertyDataType","targetNode":"","isCollection":false,"kind":"SCALAR","label":"Name","node":"String","iconType":"csv"},"id":"shipToAddress.country.name","key":"shipToAddress.country.name","labelKey":"Name","labelPath":"Ship-to address > Country > Name"},"value2":null,"key":"1","operator":"notEmpty"}]'>
                <!--{{#if ( not ( or ( eq shipToAddress.country.name null ) ( eq shipToAddress.country.name "" ) ( eq shipToAddress.country.name undefined ) ) )}}-->
                <div class="conditional-block-body">
                  <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"><span class="property"
                      data-property-display-label="Name" data-property-data-type="String"
                      data-property-name="shipToAddress.country.name" data-property-data-format=""
                      data-property-parent-context="SalesShipment">{{shipToAddress.country.name}}</span></span>
                </div>
                <!--{{/if}}-->
                <div class="conditional-block-footer">&nbsp;</div>
              </section>
            </td>
            <td style="border-color: transparent; text-align: right">
              <p>
                <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"><strong>{{ translatedContent
                    "d13d8380c3f4de07fef91a42fe6c60d7" }}&nbsp;</strong><span class="property"
                    data-property-display-label="ID" data-property-data-type="String"
                    data-property-name="shipToCustomer.id" data-property-data-format=""
                    data-property-parent-context="SalesShipment">{{shipToCustomer.id}}</span><strong>&nbsp;</strong></span>
              </p>
              <p>
                <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"><strong>{{ translatedContent
                    "0f55fedcee89c8d9407351716835247e" }}&nbsp;</strong><span class="property"
                    data-property-display-label="Description" data-property-data-type="String"
                    data-property-name="deliveryMode.description" data-property-data-format=""
                    data-property-parent-context="SalesShipment">{{deliveryMode.description}}</span></span>
              </p>
            </td>
          </tr>
        </tbody>
      </table>
    </figure>
    <table class="query-table" data-context-object-type="SalesShipmentLine" data-context-object-path="lines.query.edges"
      data-context-filter="[]"
      data-context-list-order='{"item.id":"ascending","itemDescription":"ascending","documentNumber":"ascending","quantity":"ascending","quantityInStockUnit":"ascending","quantityReceiptInSalesUnit":"ascending","_id":"ascending"}'
      data-alias="FbGiwZjk">
      <thead class="query-table-head">
        <tr class="query-table-row">
          <td class="query-table-cell"
            style="background-color: #0000001a; border: 1px solid #dfdfdf; padding: 2px; vertical-align: middle">
            <p>
              <span style="color: #198e59; font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"><strong>{{
                  translatedContent "7d74f3b92b19da5e606d737d339a9679" }}</strong></span>
            </p>
          </td>
          <td class="query-table-cell"
            style="background-color: #0000001a; border: 1px solid #dfdfdf; padding: 2px; vertical-align: middle">
            <p>
              <span style="color: #198e59; font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"><strong>{{
                  translatedContent "72f353170d7448746582602abc351a7f" }}</strong></span>
            </p>
          </td>
          <td class="query-table-cell"
            style="background-color: #0000001a; border: 1px solid #dfdfdf; padding: 2px; vertical-align: middle">
            <p>
              <span style="color: #198e59; font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"><strong>{{
                  translatedContent "14c44ffb79e2c416100024bd491b48b9" }}</strong></span>
            </p>
          </td>
          <td class="query-table-cell"
            style="background-color: #0000001a; border: 1px solid #dfdfdf; padding: 2px; vertical-align: middle">
            <p>
              <span style="color: #198e59; font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"><strong>{{
                  translatedContent "9a11fc7fe57baf69000cfd5a7b8eb653" }}</strong></span>
            </p>
          </td>
          <td class="query-table-cell"
            style="background-color: #0000001a; border: 1px solid #dfdfdf; padding: 2px; vertical-align: middle">
            <p>
              <span style="color: #198e59; font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"><strong>{{
                  translatedContent "637d3443078374cb0307388def8c38e3" }}</strong></span>
            </p>
          </td>
          <td class="query-table-cell"
            style="background-color: #0000001a; border: 1px solid #dfdfdf; padding: 2px; vertical-align: middle">
            <p>
              <span style="color: #198e59; font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"><strong>{{
                  translatedContent "465960461e87b78cdffeedd8d1930d69" }}</strong></span>
            </p>
          </td>
          <td class="query-table-cell" style="background-color: #0000001a; border: 1px solid #dfdfdf; padding: 2px">
            <p>&nbsp;</p>
          </td>
        </tr>
      </thead>
      <tbody class="query-table-body">
        <!--{{#each FbGiwZjk.query.edges}}{{#with node}}-->
        <tr class="query-table-row">
          <td class="query-table-cell" style="border: 1px solid #dfdfdf; padding: 2px; vertical-align: top">
            <p>
              <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"><span class="property"
                  data-property-display-label="ID" data-property-data-type="String" data-property-name="item.id"
                  data-property-data-format=""
                  data-property-parent-context="SalesShipmentLine">{{item.id}}</span></span>
            </p>
          </td>
          <td class="query-table-cell" style="border: 1px solid #dfdfdf; padding: 2px; vertical-align: top">
            <p>
              <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"><span class="property"
                  data-property-display-label="Item description" data-property-data-type="String"
                  data-property-name="itemDescription" data-property-data-format=""
                  data-property-parent-context="SalesShipmentLine">{{itemDescription}}</span></span>
            </p>
          </td>
          <td class="query-table-cell" style="border: 1px solid #dfdfdf; padding: 2px; vertical-align: top">
            <section class="record-context" data-context-object-type="SalesOrderLineToSalesShipmentLine"
              data-context-object-path="salesOrderLines.query.edges.0.node" data-context-filter="[]"
              data-context-list-order="{}" data-alias="ixJPvdQb">
              <!--{{#with ixJPvdQb.query.edges.0.node}}-->
              <div class="report-context-body">
                <p>
                  <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"><span class="property"
                      data-property-display-label="Document number" data-property-data-type="String"
                      data-property-name="linkedDocument.documentNumber" data-property-data-format=""
                      data-property-parent-context="SalesOrderLineToSalesShipmentLine">{{linkedDocument.documentNumber}}</span></span>
                </p>
              </div>
              <!--{{/with}}--><span class="report-context-footer">&nbsp;</span>
            </section>
          </td>
          <td class="query-table-cell" style="border: 1px solid #dfdfdf; padding: 2px; vertical-align: top">
            <p>
              <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"><span class="property"
                  data-property-display-label="Quantity in sales unit" data-property-data-type="Decimal"
                  data-property-name="quantity" data-property-data-format="2"
                  data-property-parent-context="SalesShipmentLine">{{formatNumber quantity 2}}</span><span
                  class="property" data-property-display-label="Name" data-property-data-type="String"
                  data-property-name="unit.name" data-property-data-format=""
                  data-property-parent-context="SalesShipmentLine">{{unit.name}}</span></span>
            </p>
          </td>
          <td class="query-table-cell" style="border: 1px solid #dfdfdf; padding: 2px; vertical-align: top">
            <p>
              <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"><span class="property"
                  data-property-display-label="Quantity in stock unit" data-property-data-type="Decimal"
                  data-property-name="quantityInStockUnit" data-property-data-format="2"
                  data-property-parent-context="SalesShipmentLine">{{formatNumber quantityInStockUnit 2}}</span><span
                  class="property" data-property-display-label="Name" data-property-data-type="String"
                  data-property-name="stockUnit.name" data-property-data-format=""
                  data-property-parent-context="SalesShipmentLine">{{stockUnit.name}}</span></span>
            </p>
          </td>
          <td class="query-table-cell" style="border: 1px solid #dfdfdf; padding: 2px; vertical-align: top">
            <section class="record-context" data-context-object-type="StockAllocation"
              data-context-object-path="stockAllocations.query.edges.0.node" data-context-filter="[]"
              data-context-list-order="{}" data-alias="CewqieYg">
              <!--{{#with CewqieYg.query.edges.0.node}}-->
              <div class="report-context-body">
                <section class="conditional-block"
                  data-context-condition='[{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"1","value1":{"label":"Quantity in stock unit","data":{"name":"quantityInStockUnit","title":"Quantity in stock unit","canSort":true,"canFilter":true,"type":"Decimal","isCustom":false,"isMutable":false,"isStored":true,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"quantityInStockUnit","targetNode":"","isCollection":false,"kind":"SCALAR","label":"Quantity in stock unit","node":"Decimal","iconType":"csv"},"id":"quantityInStockUnit","key":"quantityInStockUnit","labelKey":"Quantity in stock unit","labelPath":"Quantity in stock unit"},"value2":null,"key":"1","operator":"notEmpty"}]'>
                  <!--{{#if ( not ( or ( eq quantityInStockUnit null ) ( eq quantityInStockUnit "" ) ( eq quantityInStockUnit undefined ) ) )}}-->
                  <div class="conditional-block-body">
                    <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"><span class="property"
                        data-property-display-label="Quantity in stock unit" data-property-data-type="Decimal"
                        data-property-name="quantityInStockUnit" data-property-data-format="2"
                        data-property-parent-context="StockAllocation">{{formatNumber quantityInStockUnit
                        2}}</span><span class="property" data-property-display-label="Name"
                        data-property-data-type="String" data-property-name="stockRecord.stockUnit.name"
                        data-property-data-format=""
                        data-property-parent-context="StockAllocation">{{stockRecord.stockUnit.name}}</span></span>
                  </div>
                  <!--{{/if}}-->
                  <div class="conditional-block-footer">&nbsp;</div>
                </section>
              </div>
              <!--{{/with}}--><span class="report-context-footer">&nbsp;</span>
            </section>
            <section class="conditional-block"
              data-context-condition='[{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"1","value1":{"label":"Quantity in stock unit","data":{"name":"quantityInStockUnit","title":"Quantity in stock unit","canSort":true,"canFilter":true,"type":"Decimal","isCustom":false,"isMutable":false,"isStored":true,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"quantityInStockUnit","targetNode":"","isCollection":false,"kind":"SCALAR","label":"Quantity in stock unit","node":"Decimal","iconType":"csv"},"id":"quantityInStockUnit","key":"quantityInStockUnit","labelKey":"Quantity in stock unit","labelPath":"Quantity in stock unit"},"value2":null,"key":"1","operator":"notEmpty"}]'>
              <!--{{#if ( not ( or ( eq quantityInStockUnit null ) ( eq quantityInStockUnit "" ) ( eq quantityInStockUnit undefined ) ) )}}-->
              <div class="conditional-block-body">&nbsp;</div>
              <!--{{/if}}-->
              <div class="conditional-block-footer">&nbsp;</div>
            </section>
          </td>
          <td class="query-table-cell" style="border: 1px solid #dfdfdf; padding: 2px">
            <table class="query-table" data-context-object-type="StockAllocation"
              data-context-object-path="stockAllocations.query.edges" data-context-filter="[]"
              data-context-list-order='{"stockRecord.location.name":"ascending","stockRecord.lot.id":"ascending","stockRecord.quantityInStockUnit":"ascending"}'
              data-alias="DiXoVUyB">
              <thead class="query-table-head">
                <tr class="query-table-row">
                  <td class="query-table-cell"
                    style="background-color: #0000001a; border: 1px solid #dfdfdf; padding: 2px; vertical-align: middle">
                    <p>
                      <span
                        style="color: #198e59; font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"><strong>{{
                          translatedContent "ce5bf551379459c1c61d2a204061c455" }}</strong></span>
                    </p>
                  </td>
                  <td class="query-table-cell"
                    style="background-color: #0000001a; border: 1px solid #dfdfdf; padding: 2px; vertical-align: middle">
                    <p>
                      <span
                        style="color: #198e59; font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"><strong>{{
                          translatedContent "eeec6c7a9d2b475c23650b202208b892" }}</strong></span>
                    </p>
                  </td>
                  <td class="query-table-cell"
                    style="background-color: #0000001a; border: 1px solid #dfdfdf; padding: 2px; vertical-align: middle">
                    <p>
                      <span
                        style="color: #198e59; font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"><strong>{{
                          translatedContent "465960461e87b78cdffeedd8d1930d69" }}</strong></span>
                    </p>
                  </td>
                </tr>
              </thead>
              <tbody class="query-table-body">
                <!--{{#each DiXoVUyB.query.edges}}{{#with node}}-->
                <tr class="query-table-row">
                  <td class="query-table-cell" style="border: 1px solid #dfdfdf; padding: 2px">
                    <p>
                      <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"><span class="property"
                          data-property-display-label="Location" data-property-data-type="String"
                          data-property-name="stockRecord.location.name" data-property-data-format=""
                          data-property-parent-context="StockAllocation">{{stockRecord.location.name}}</span></span>
                    </p>
                  </td>
                  <td class="query-table-cell" style="border: 1px solid #dfdfdf; padding: 2px">
                    <p>
                      <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"><span class="property"
                          data-property-display-label="Lot" data-property-data-type="String"
                          data-property-name="stockRecord.lot.id" data-property-data-format=""
                          data-property-parent-context="StockAllocation">{{stockRecord.lot.id}}</span></span>
                    </p>
                  </td>
                  <td class="query-table-cell" style="border: 1px solid #dfdfdf; padding: 2px">
                    <p>
                      <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"><span class="property"
                          data-property-display-label="Quantity in stock unit" data-property-data-type="Decimal"
                          data-property-name="quantityInStockUnit" data-property-data-format="2"
                          data-property-parent-context="StockAllocation">{{formatNumber quantityInStockUnit
                          2}}</span><span class="property" data-property-display-label="Name"
                          data-property-data-type="String" data-property-name="stockRecord.stockUnit.name"
                          data-property-data-format=""
                          data-property-parent-context="StockAllocation">{{stockRecord.stockUnit.name}}</span></span>
                    </p>
                  </td>
                </tr>
                <tr class="query-table-row" data-hidden="1">
                  <td class="query-table-cell" colspan="3">
                    <p>&nbsp;</p>
                  </td>
                </tr>
                <!--{{/with}}{{/each}}-->
                <tr class="query-table-row" data-hidden="1">
                  <td class="query-table-cell" colspan="3">
                    <p>&nbsp;</p>
                  </td>
                </tr>
              </tbody>
              <tfoot class="query-table-footer">
                <tr class="query-table-row">
                  <td class="query-table-cell">
                    <p>&nbsp;</p>
                  </td>
                  <td class="query-table-cell">
                    <p>&nbsp;</p>
                  </td>
                  <td class="query-table-cell">
                    <p>&nbsp;</p>
                  </td>
                </tr>
              </tfoot>
            </table>
            <p>&nbsp;</p>
            <p>&nbsp;</p>
          </td>
        </tr>
        <tr class="query-table-row" data-hidden="1">
          <td class="query-table-cell" colspan="7">
            <p>&nbsp;</p>
          </td>
        </tr>
        <!--{{/with}}{{/each}}-->
        <tr class="query-table-row" data-hidden="1">
          <td class="query-table-cell" colspan="7">
            <p>&nbsp;</p>
          </td>
        </tr>
      </tbody>
      <tfoot class="query-table-footer">
        <tr class="query-table-row">
          <td class="query-table-cell">
            <p>&nbsp;</p>
          </td>
          <td class="query-table-cell">
            <p>&nbsp;</p>
          </td>
          <td class="query-table-cell">
            <p>&nbsp;</p>
          </td>
          <td class="query-table-cell">
            <p>&nbsp;</p>
          </td>
          <td class="query-table-cell">
            <p>&nbsp;</p>
          </td>
          <td class="query-table-cell">
            <p>&nbsp;</p>
          </td>
          <td class="query-table-cell">
            <p>&nbsp;</p>
          </td>
        </tr>
      </tfoot>
    </table>
  </div>
  <!--{{/with}}--><span class="report-context-footer">&nbsp;</span>
</section>
