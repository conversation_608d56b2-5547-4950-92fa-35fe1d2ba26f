{"data": [{"_id": "0", "path": "number", "locale": "", "dataType": "string", "isCustom": false, "description": "number"}, {"_id": "10", "path": "customerNumber", "locale": "", "dataType": "string", "isCustom": false, "description": "customer number"}, {"_id": "20", "path": "status", "locale": "", "dataType": "enum(quote,pending,inProgress,closed)", "isCustom": false, "description": "status"}, {"_id": "30", "path": "invoiceStatus", "locale": "", "dataType": "enum(notInvoiced,partiallyInvoiced,invoiced)", "isCustom": false, "description": "invoice status"}, {"_id": "40", "path": "date", "locale": "", "dataType": "date", "isCustom": false, "description": "order date (YYYY-MM-DD)"}, {"_id": "50", "path": "isPrinted", "locale": "", "dataType": "boolean", "isCustom": false, "description": "is printed (false/true)"}, {"_id": "60", "path": "isSent", "locale": "", "dataType": "boolean", "isCustom": false, "description": "is sent (false/true)"}, {"_id": "70", "path": "*soldToCustomer", "locale": "", "dataType": "reference", "isCustom": false, "description": "sold to customer (#businessEntity)"}, {"_id": "80", "path": "soldToLinkedAddress", "locale": "", "dataType": "reference", "isCustom": false, "description": "sold to linked address (#businessEntity|_sortValue)"}, {"_id": "90", "path": "fxRateDate", "locale": "", "dataType": "date", "isCustom": false, "description": "fx rate date (YYYY-MM-DD)"}, {"_id": "100", "path": "*requestedDeliveryDate", "locale": "", "dataType": "date", "isCustom": false, "description": "requested delivery date (YYYY-MM-DD)"}, {"_id": "110", "path": "doNotShipBeforeDate", "locale": "", "dataType": "date", "isCustom": false, "description": "do not ship before date (YYYY-MM-DD)"}, {"_id": "120", "path": "doNotShipAfterDate", "locale": "", "dataType": "date", "isCustom": false, "description": "do not ship after date (YYYY-MM-DD)"}, {"_id": "130", "path": "shippingStatus", "locale": "", "dataType": "enum(notShipped,partiallyShipped,shipped)", "isCustom": false, "description": "shipping status"}, {"_id": "140", "path": "*site", "locale": "", "dataType": "reference", "isCustom": false, "description": "sales site (#id)"}, {"_id": "150", "path": "shipToCustomer", "locale": "", "dataType": "reference", "isCustom": false, "description": "ship to customer (#businessEntity)"}, {"_id": "160", "path": "shipToCustomerAddress", "locale": "", "dataType": "reference", "isCustom": false, "description": "ship to customer address (#customer|_sortValue)"}, {"_id": "170", "path": "billToCustomer", "locale": "", "dataType": "reference", "isCustom": false, "description": "bill to customer (#businessEntity)"}, {"_id": "180", "path": "billToLinkedAddress", "locale": "", "dataType": "reference", "isCustom": false, "description": "bill to linked address (#businessEntity|_sortValue)"}, {"_id": "190", "path": "incoterm", "locale": "", "dataType": "reference", "isCustom": false, "description": "incoterm (#id)"}, {"_id": "200", "path": "deliveryMode", "locale": "", "dataType": "reference", "isCustom": false, "description": "delivery mode (#id)"}, {"_id": "210", "path": "deliveryLeadTime", "locale": "", "dataType": "integer", "isCustom": false, "description": "delivery lead time"}, {"_id": "220", "path": "taxCalculationStatus", "locale": "", "dataType": "enum(notDone,inProgress,done,failed)", "isCustom": false, "description": "tax calculation status"}, {"_id": "230", "path": "internalNote", "locale": "", "dataType": "textStream", "isCustom": false, "description": "internal note"}, {"_id": "240", "path": "externalNote", "locale": "", "dataType": "textStream", "isCustom": false, "description": "external note"}, {"_id": "250", "path": "isExternalNote", "locale": "", "dataType": "boolean", "isCustom": false, "description": "is external note (false/true)"}, {"_id": "260", "path": "isTransferHeaderNote", "locale": "", "dataType": "boolean", "isCustom": false, "description": "is transfer header note (false/true)"}, {"_id": "270", "path": "isTransferLineNote", "locale": "", "dataType": "boolean", "isCustom": false, "description": "is transfer line note (false/true)"}, {"_id": "280", "path": "intacctId", "locale": "", "dataType": "string", "isCustom": false, "description": "intacct id"}, {"_id": "290", "path": "shopifyId", "locale": "", "dataType": "string", "isCustom": false, "description": "shopify id"}, {"_id": "300", "path": "isQuote", "locale": "", "dataType": "boolean", "isCustom": false, "description": "is quote (false/true)"}, {"_id": "310", "path": "currency", "locale": "", "dataType": "reference", "isCustom": false, "description": "currency (#id)"}, {"_id": "320", "path": "companyFxRate", "locale": "", "dataType": "decimal", "isCustom": false, "description": "company fx rate"}, {"_id": "330", "path": "companyFxRateDivisor", "locale": "", "dataType": "decimal", "isCustom": false, "description": "company fx rate divisor"}, {"_id": "340", "path": "paymentTerm", "locale": "", "dataType": "reference", "isCustom": false, "description": "payment term (#id)"}, {"_id": "350", "path": "stockSite", "locale": "", "dataType": "reference", "isCustom": false, "description": "stock site (#id)"}, {"_id": "360", "path": "totalTaxAmount", "locale": "", "dataType": "decimal", "isCustom": false, "description": "total tax amount"}, {"_id": "370", "path": "totalTaxableAmount", "locale": "", "dataType": "decimal", "isCustom": false, "description": "total taxable amount"}, {"_id": "380", "path": "totalExemptAmount", "locale": "", "dataType": "decimal", "isCustom": false, "description": "total exempt amount"}, {"_id": "390", "path": "totalTaxAmountAdjusted", "locale": "", "dataType": "decimal", "isCustom": false, "description": "total tax amount adjusted"}, {"_id": "400", "path": "displayStatus", "locale": "", "dataType": "enum(quote,partiallyShipped,confirmed,shipped,closed,taxCalculationFailed)", "isCustom": false, "description": "display status"}, {"_id": "410", "path": "shippingDate", "locale": "", "dataType": "date", "isCustom": false, "description": "shipping date (YYYY-MM-DD)"}, {"_id": "420", "path": "expectedDeliveryDate", "locale": "", "dataType": "date", "isCustom": false, "description": "expected delivery date (YYYY-MM-DD)"}, {"_id": "430", "path": "/soldToAddress", "locale": "", "dataType": "reference", "isCustom": false, "description": "sold to address"}, {"_id": "440", "path": "*name", "locale": "", "dataType": "string", "isCustom": false, "description": "name"}, {"_id": "450", "path": "addressLine1", "locale": "", "dataType": "string", "isCustom": false, "description": "address line 1"}, {"_id": "460", "path": "addressLine2", "locale": "", "dataType": "string", "isCustom": false, "description": "address line 2"}, {"_id": "470", "path": "city", "locale": "", "dataType": "string", "isCustom": false, "description": "city"}, {"_id": "480", "path": "region", "locale": "", "dataType": "string", "isCustom": false, "description": "region"}, {"_id": "490", "path": "postcode", "locale": "", "dataType": "string", "isCustom": false, "description": "postcode"}, {"_id": "500", "path": "country", "locale": "", "dataType": "reference", "isCustom": false, "description": "country (#id)"}, {"_id": "510", "path": "locationPhoneNumber", "locale": "", "dataType": "string", "isCustom": false, "description": "location phone number"}, {"_id": "520", "path": "/soldToContact", "locale": "", "dataType": "reference", "isCustom": false, "description": "sold to contact"}, {"_id": "530", "path": "*title", "locale": "", "dataType": "enum(ms,mr,dr,mrs)", "isCustom": false, "description": "title"}, {"_id": "540", "path": "*firstName", "locale": "", "dataType": "string", "isCustom": false, "description": "first name"}, {"_id": "550", "path": "*lastName", "locale": "", "dataType": "string", "isCustom": false, "description": "last name"}, {"_id": "560", "path": "preferredName", "locale": "", "dataType": "string", "isCustom": false, "description": "preferred name"}, {"_id": "570", "path": "role", "locale": "", "dataType": "enum(mainContact,commercialContact,financialContact)", "isCustom": false, "description": "role"}, {"_id": "580", "path": "position", "locale": "", "dataType": "string", "isCustom": false, "description": "position"}, {"_id": "590", "path": "locationPhoneNumber#1", "locale": "", "dataType": "string", "isCustom": false, "description": "location phone number"}, {"_id": "600", "path": "email", "locale": "", "dataType": "string", "isCustom": false, "description": "email"}, {"_id": "610", "path": "/shipToAddress", "locale": "", "dataType": "reference", "isCustom": false, "description": "ship to address"}, {"_id": "620", "path": "*name#1", "locale": "", "dataType": "string", "isCustom": false, "description": "name"}, {"_id": "630", "path": "addressLine1#1", "locale": "", "dataType": "string", "isCustom": false, "description": "address line 1"}, {"_id": "640", "path": "addressLine2#1", "locale": "", "dataType": "string", "isCustom": false, "description": "address line 2"}, {"_id": "650", "path": "city#1", "locale": "", "dataType": "string", "isCustom": false, "description": "city"}, {"_id": "660", "path": "region#1", "locale": "", "dataType": "string", "isCustom": false, "description": "region"}, {"_id": "670", "path": "postcode#1", "locale": "", "dataType": "string", "isCustom": false, "description": "postcode"}, {"_id": "680", "path": "country#1", "locale": "", "dataType": "reference", "isCustom": false, "description": "country (#id)"}, {"_id": "690", "path": "locationPhoneNumber#2", "locale": "", "dataType": "string", "isCustom": false, "description": "location phone number"}, {"_id": "700", "path": "/shipToContact", "locale": "", "dataType": "reference", "isCustom": false, "description": "ship to contact"}, {"_id": "710", "path": "*title#1", "locale": "", "dataType": "enum(ms,mr,dr,mrs)", "isCustom": false, "description": "title"}, {"_id": "720", "path": "*firstName#1", "locale": "", "dataType": "string", "isCustom": false, "description": "first name"}, {"_id": "730", "path": "*lastName#1", "locale": "", "dataType": "string", "isCustom": false, "description": "last name"}, {"_id": "740", "path": "preferredName#1", "locale": "", "dataType": "string", "isCustom": false, "description": "preferred name"}, {"_id": "750", "path": "role#1", "locale": "", "dataType": "enum(mainContact,commercialContact,financialContact)", "isCustom": false, "description": "role"}, {"_id": "760", "path": "position#1", "locale": "", "dataType": "string", "isCustom": false, "description": "position"}, {"_id": "770", "path": "locationPhoneNumber#3", "locale": "", "dataType": "string", "isCustom": false, "description": "location phone number"}, {"_id": "780", "path": "email#1", "locale": "", "dataType": "string", "isCustom": false, "description": "email"}, {"_id": "790", "path": "/billTo<PERSON>ddress", "locale": "", "dataType": "reference", "isCustom": false, "description": "bill to address"}, {"_id": "800", "path": "*name#2", "locale": "", "dataType": "string", "isCustom": false, "description": "name"}, {"_id": "810", "path": "addressLine1#2", "locale": "", "dataType": "string", "isCustom": false, "description": "address line 1"}, {"_id": "820", "path": "addressLine2#2", "locale": "", "dataType": "string", "isCustom": false, "description": "address line 2"}, {"_id": "830", "path": "city#2", "locale": "", "dataType": "string", "isCustom": false, "description": "city"}, {"_id": "840", "path": "region#2", "locale": "", "dataType": "string", "isCustom": false, "description": "region"}, {"_id": "850", "path": "postcode#2", "locale": "", "dataType": "string", "isCustom": false, "description": "postcode"}, {"_id": "860", "path": "country#2", "locale": "", "dataType": "reference", "isCustom": false, "description": "country (#id)"}, {"_id": "870", "path": "locationPhoneNumber#4", "locale": "", "dataType": "string", "isCustom": false, "description": "location phone number"}, {"_id": "880", "path": "/billToContact", "locale": "", "dataType": "reference", "isCustom": false, "description": "bill to contact"}, {"_id": "890", "path": "*title#2", "locale": "", "dataType": "enum(ms,mr,dr,mrs)", "isCustom": false, "description": "title"}, {"_id": "900", "path": "*firstName#2", "locale": "", "dataType": "string", "isCustom": false, "description": "first name"}, {"_id": "910", "path": "*lastName#2", "locale": "", "dataType": "string", "isCustom": false, "description": "last name"}, {"_id": "920", "path": "preferredName#2", "locale": "", "dataType": "string", "isCustom": false, "description": "preferred name"}, {"_id": "930", "path": "role#2", "locale": "", "dataType": "enum(mainContact,commercialContact,financialContact)", "isCustom": false, "description": "role"}, {"_id": "940", "path": "position#2", "locale": "", "dataType": "string", "isCustom": false, "description": "position"}, {"_id": "950", "path": "locationPhoneNumber#5", "locale": "", "dataType": "string", "isCustom": false, "description": "location phone number"}, {"_id": "960", "path": "email#2", "locale": "", "dataType": "string", "isCustom": false, "description": "email"}, {"_id": "970", "path": "#lines", "locale": "", "dataType": "collection", "isCustom": false, "description": "lines"}, {"_id": "980", "path": "originDocumentType", "locale": "", "dataType": "enum(direct,shipment,order,invoice,return)", "isCustom": false, "description": "origin document type"}, {"_id": "990", "path": "status#1", "locale": "", "dataType": "enum(quote,pending,inProgress,closed)", "isCustom": false, "description": "status"}, {"_id": "1000", "path": "invoiceStatus#1", "locale": "", "dataType": "enum(notInvoiced,partiallyInvoiced,invoiced)", "isCustom": false, "description": "invoice status"}, {"_id": "1010", "path": "shippingStatus#1", "locale": "", "dataType": "enum(notShipped,partiallyShipped,shipped)", "isCustom": false, "description": "shipping status"}, {"_id": "1020", "path": "*item", "locale": "", "dataType": "reference", "isCustom": false, "description": "item (#id)"}, {"_id": "1030", "path": "itemDescription", "locale": "", "dataType": "string", "isCustom": false, "description": "item description"}, {"_id": "1040", "path": "site", "locale": "", "dataType": "reference", "isCustom": false, "description": "sales site (#id)"}, {"_id": "1050", "path": "shipToCustomerAddress#1", "locale": "", "dataType": "reference", "isCustom": false, "description": "ship to customer address (#customer|_sortValue)"}, {"_id": "1060", "path": "unit", "locale": "", "dataType": "reference", "isCustom": false, "description": "sales unit (#id)"}, {"_id": "1070", "path": "unitToStockUnitConversionFactor", "locale": "", "dataType": "decimal", "isCustom": false, "description": "sales unit to stock unit conversion factor"}, {"_id": "1080", "path": "stockUnit", "locale": "", "dataType": "reference", "isCustom": false, "description": "stock unit (#id)"}, {"_id": "1090", "path": "deliveryMode#1", "locale": "", "dataType": "reference", "isCustom": false, "description": "delivery mode (#id)"}, {"_id": "1100", "path": "requestedDeliveryDate", "locale": "", "dataType": "date", "isCustom": false, "description": "requested delivery date (YYYY-MM-DD)"}, {"_id": "1110", "path": "deliveryLeadTime#1", "locale": "", "dataType": "integer", "isCustom": false, "description": "delivery lead time"}, {"_id": "1120", "path": "doNotShipBeforeDate#1", "locale": "", "dataType": "date", "isCustom": false, "description": "do not ship before date (YYYY-MM-DD)"}, {"_id": "1130", "path": "doNotShipAfterDate#1", "locale": "", "dataType": "date", "isCustom": false, "description": "do not ship after date (YYYY-MM-DD)"}, {"_id": "1140", "path": "shippingDate#1", "locale": "", "dataType": "date", "isCustom": false, "description": "shipping date (YYYY-MM-DD)"}, {"_id": "1150", "path": "expectedDeliveryDate#1", "locale": "", "dataType": "date", "isCustom": false, "description": "expected delivery date (YYYY-MM-DD)"}, {"_id": "1160", "path": "grossPrice", "locale": "", "dataType": "decimal", "isCustom": false, "description": "gross price"}, {"_id": "1170", "path": "grossPriceDeterminated", "locale": "", "dataType": "decimal", "isCustom": false, "description": "gross price determinated"}, {"_id": "1180", "path": "taxableAmount", "locale": "", "dataType": "decimal", "isCustom": false, "description": "taxable amount"}, {"_id": "1190", "path": "taxAmount", "locale": "", "dataType": "decimal", "isCustom": false, "description": "tax amount"}, {"_id": "1200", "path": "exemptAmount", "locale": "", "dataType": "decimal", "isCustom": false, "description": "exempt amount"}, {"_id": "1210", "path": "storedDimensions", "locale": "", "dataType": "json", "isCustom": false, "description": "stored dimensions"}, {"_id": "1220", "path": "storedAttributes", "locale": "", "dataType": "json", "isCustom": false, "description": "stored attributes"}, {"_id": "1230", "path": "priceOrigin", "locale": "", "dataType": "enum(manual,priceList,basePrice)", "isCustom": false, "description": "price origin"}, {"_id": "1240", "path": "priceReason", "locale": "", "dataType": "reference", "isCustom": false, "description": "price reason (#id)"}, {"_id": "1250", "path": "priceOriginDeterminated", "locale": "", "dataType": "enum(manual,priceList,basePrice)", "isCustom": false, "description": "price origin determinated"}, {"_id": "1260", "path": "priceReasonDeterminated", "locale": "", "dataType": "reference", "isCustom": false, "description": "price reason determinated (#id)"}, {"_id": "1270", "path": "isPriceDeterminated", "locale": "", "dataType": "boolean", "isCustom": false, "description": "is price determinated (false/true)"}, {"_id": "1280", "path": "allocationRequestStatus", "locale": "", "dataType": "enum(noRequest,inProgress,error,completed)", "isCustom": false, "description": "allocation request status"}, {"_id": "1290", "path": "internalNote#1", "locale": "", "dataType": "textStream", "isCustom": false, "description": "internal note"}, {"_id": "1300", "path": "externalNote#1", "locale": "", "dataType": "textStream", "isCustom": false, "description": "external note"}, {"_id": "1310", "path": "isExternalNote#1", "locale": "", "dataType": "boolean", "isCustom": false, "description": "is external note (false/true)"}, {"_id": "1320", "path": "intacctId#1", "locale": "", "dataType": "string", "isCustom": false, "description": "intacct id"}, {"_id": "1330", "path": "*quantity", "locale": "", "dataType": "decimal", "isCustom": false, "description": "quantity in sales unit"}, {"_id": "1340", "path": "quantityInStockUnit", "locale": "", "dataType": "decimal", "isCustom": false, "description": "quantity in stock unit"}, {"_id": "1350", "path": "netPrice", "locale": "", "dataType": "decimal", "isCustom": false, "description": "net price"}, {"_id": "1360", "path": "netPriceExcludingTax", "locale": "", "dataType": "decimal", "isCustom": false, "description": "net price excluding tax"}, {"_id": "1370", "path": "netPriceIncludingTax", "locale": "", "dataType": "decimal", "isCustom": false, "description": "net price including tax"}, {"_id": "1380", "path": "taxAmountAdjusted", "locale": "", "dataType": "decimal", "isCustom": false, "description": "tax amount adjusted"}, {"_id": "1390", "path": "amountExcludingTaxInCompanyCurrency", "locale": "", "dataType": "decimal", "isCustom": false, "description": "line amount excluding tax in company currency"}, {"_id": "1400", "path": "amountIncludingTax", "locale": "", "dataType": "decimal", "isCustom": false, "description": "line amount including tax"}, {"_id": "1410", "path": "amountIncludingTaxInCompanyCurrency", "locale": "", "dataType": "decimal", "isCustom": false, "description": "line amount including tax in company currency"}, {"_id": "1420", "path": "stockSite#1", "locale": "", "dataType": "reference", "isCustom": false, "description": "stock site (#id)"}, {"_id": "1430", "path": "stockSiteLinkedAddress", "locale": "", "dataType": "reference", "isCustom": false, "description": "stock site linked address (#businessEntity|_sortValue)"}, {"_id": "1440", "path": "//shipToAddress", "locale": "", "dataType": "reference", "isCustom": false, "description": "ship to address"}, {"_id": "1450", "path": "*name#3", "locale": "", "dataType": "string", "isCustom": false, "description": "name"}, {"_id": "1460", "path": "addressLine1#3", "locale": "", "dataType": "string", "isCustom": false, "description": "address line 1"}, {"_id": "1470", "path": "addressLine2#3", "locale": "", "dataType": "string", "isCustom": false, "description": "address line 2"}, {"_id": "1480", "path": "city#3", "locale": "", "dataType": "string", "isCustom": false, "description": "city"}, {"_id": "1490", "path": "region#3", "locale": "", "dataType": "string", "isCustom": false, "description": "region"}, {"_id": "1500", "path": "postcode#3", "locale": "", "dataType": "string", "isCustom": false, "description": "postcode"}, {"_id": "1510", "path": "country#3", "locale": "", "dataType": "reference", "isCustom": false, "description": "country (#id)"}, {"_id": "1520", "path": "locationPhoneNumber#6", "locale": "", "dataType": "string", "isCustom": false, "description": "location phone number"}, {"_id": "1530", "path": "//shipToContact", "locale": "", "dataType": "reference", "isCustom": false, "description": "ship to contact"}, {"_id": "1540", "path": "*title#3", "locale": "", "dataType": "enum(ms,mr,dr,mrs)", "isCustom": false, "description": "title"}, {"_id": "1550", "path": "*firstName#3", "locale": "", "dataType": "string", "isCustom": false, "description": "first name"}, {"_id": "1560", "path": "*lastName#3", "locale": "", "dataType": "string", "isCustom": false, "description": "last name"}, {"_id": "1570", "path": "preferredName#3", "locale": "", "dataType": "string", "isCustom": false, "description": "preferred name"}, {"_id": "1580", "path": "role#3", "locale": "", "dataType": "enum(mainContact,commercialContact,financialContact)", "isCustom": false, "description": "role"}, {"_id": "1590", "path": "position#3", "locale": "", "dataType": "string", "isCustom": false, "description": "position"}, {"_id": "1600", "path": "locationPhoneNumber#7", "locale": "", "dataType": "string", "isCustom": false, "description": "location phone number"}, {"_id": "1610", "path": "email#3", "locale": "", "dataType": "string", "isCustom": false, "description": "email"}, {"_id": "1620", "path": "##discountCharges", "locale": "", "dataType": "collection", "isCustom": false, "description": "discount charges"}, {"_id": "1630", "path": "sign", "locale": "", "dataType": "enum(increase,decrease)", "isCustom": false, "description": "sign"}, {"_id": "1640", "path": "valueType", "locale": "", "dataType": "enum(percentage,amount)", "isCustom": false, "description": "value type"}, {"_id": "1650", "path": "calculationBasis", "locale": "", "dataType": "enum(grossPrice,grossPriceAndCompound)", "isCustom": false, "description": "calculation basis"}, {"_id": "1660", "path": "calculationRule", "locale": "", "dataType": "enum(byUnit,byLine)", "isCustom": false, "description": "calculation rule"}, {"_id": "1670", "path": "basisDeterminated", "locale": "", "dataType": "decimal", "isCustom": false, "description": "basis determinated"}, {"_id": "1680", "path": "*value", "locale": "", "dataType": "decimal", "isCustom": false, "description": "value"}, {"_id": "1690", "path": "valueDeterminated", "locale": "", "dataType": "decimal", "isCustom": false, "description": "value determinated"}, {"_id": "1700", "path": "basis", "locale": "", "dataType": "decimal", "isCustom": false, "description": "basis"}, {"_id": "1710", "path": "amount", "locale": "", "dataType": "decimal", "isCustom": false, "description": "amount"}, {"_id": "1720", "path": "##taxes", "locale": "", "dataType": "collection", "isCustom": false, "description": "taxes"}, {"_id": "1730", "path": "taxRate", "locale": "", "dataType": "decimal", "isCustom": false, "description": "tax rate"}, {"_id": "1740", "path": "taxCategoryReference", "locale": "", "dataType": "reference", "isCustom": false, "description": "tax category reference (#id)"}, {"_id": "1750", "path": "taxReference", "locale": "", "dataType": "reference", "isCustom": false, "description": "tax reference (#id)"}, {"_id": "1760", "path": "deductibleTaxRate", "locale": "", "dataType": "decimal", "isCustom": false, "description": "deductible tax rate"}, {"_id": "1770", "path": "isReverseCharge", "locale": "", "dataType": "boolean", "isCustom": false, "description": "is reverse charge (false/true)"}, {"_id": "1780", "path": "isTaxMandatory", "locale": "", "dataType": "boolean", "isCustom": false, "description": "is tax mandatory (true/false)"}, {"_id": "1790", "path": "isSubjectToGlTaxExcludedAmount", "locale": "", "dataType": "boolean", "isCustom": false, "description": "is subject to gl tax excluded amount (false/true)"}, {"_id": "1800", "path": "jurisdictionName", "locale": "", "dataType": "string", "isCustom": false, "description": "jurisdiction name"}, {"_id": "1810", "path": "numberOfTaxableUnits", "locale": "", "dataType": "decimal", "isCustom": false, "description": "number of taxable units"}, {"_id": "1820", "path": "numberOfNonTaxableUnits", "locale": "", "dataType": "decimal", "isCustom": false, "description": "number of non taxable units"}, {"_id": "1830", "path": "numberOfExemptUnits", "locale": "", "dataType": "decimal", "isCustom": false, "description": "number of exempt units"}, {"_id": "1840", "path": "country#4", "locale": "", "dataType": "string", "isCustom": false, "description": "country"}, {"_id": "1850", "path": "region#4", "locale": "", "dataType": "string", "isCustom": false, "description": "region"}, {"_id": "1860", "path": "jurisdictionCode", "locale": "", "dataType": "string", "isCustom": false, "description": "jurisdiction code"}, {"_id": "1870", "path": "jurisdictionType", "locale": "", "dataType": "string", "isCustom": false, "description": "jurisdiction type"}, {"_id": "1880", "path": "taxCategory", "locale": "base", "dataType": "localized text", "isCustom": false, "description": "\"default locale: base, other locales can be specified with (locale-name), for example \"\"tax category (de-DE)\"\". Available locales are (en-GB, fr-FR, ...). You can also duplicate the columns to import several translations\""}, {"_id": "1890", "path": "tax", "locale": "base", "dataType": "localized text", "isCustom": false, "description": "\"default locale: base, other locales can be specified with (locale-name), for example \"\"tax (de-DE)\"\". Available locales are (en-GB, fr-FR, ...). You can also duplicate the columns to import several translations\""}, {"_id": "1900", "path": "deductibleTaxAmount", "locale": "", "dataType": "decimal", "isCustom": false, "description": "deductible tax amount"}, {"_id": "1910", "path": "taxAmountAdjusted#1", "locale": "", "dataType": "decimal", "isCustom": false, "description": "tax amount adjusted"}, {"_id": "1920", "path": "nonTaxableAmount", "locale": "", "dataType": "decimal", "isCustom": false, "description": "non taxable amount"}, {"_id": "1930", "path": "taxAmount#1", "locale": "", "dataType": "decimal", "isCustom": false, "description": "tax amount"}, {"_id": "1940", "path": "exemptAmount#1", "locale": "", "dataType": "decimal", "isCustom": false, "description": "exempt amount"}, {"_id": "1950", "path": "taxableAmount#1", "locale": "", "dataType": "decimal", "isCustom": false, "description": "taxable amount"}, {"_id": "1960", "path": "//workInProgress", "locale": "", "dataType": "reference", "isCustom": false, "description": "work in progress"}, {"_id": "1970", "path": "documentType", "locale": "", "dataType": "enum(workOrder,materialNeed,purchaseOrder,purchaseReceipt,purchaseReturn,salesOrder)", "isCustom": false, "description": "document type"}, {"_id": "1980", "path": "item", "locale": "", "dataType": "reference", "isCustom": false, "description": "item (#id)"}, {"_id": "1990", "path": "site", "locale": "", "dataType": "reference", "isCustom": false, "description": "site (#id)"}, {"_id": "2000", "path": "status#2", "locale": "", "dataType": "enum(firm,planned,suggested,closed)", "isCustom": false, "description": "status"}, {"_id": "2010", "path": "startDate", "locale": "", "dataType": "date", "isCustom": false, "description": "start date (YYYY-MM-DD)"}, {"_id": "2020", "path": "endDate", "locale": "", "dataType": "date", "isCustom": false, "description": "end date (YYYY-MM-DD)"}, {"_id": "2030", "path": "expectedQuantity", "locale": "", "dataType": "decimal", "isCustom": false, "description": "expected quantity"}, {"_id": "2040", "path": "actualQuantity", "locale": "", "dataType": "decimal", "isCustom": false, "description": "actual quantity"}, {"_id": "2050", "path": "outstandingQuantity", "locale": "", "dataType": "decimal", "isCustom": false, "description": "outstanding quantity"}, {"_id": "2060", "path": "##stockDetails", "locale": "", "dataType": "collection", "isCustom": false, "description": "stock details"}, {"_id": "2070", "path": "movementType", "locale": "", "dataType": "enum(receipt,issue,change,adjustment,correction,valueChange)", "isCustom": false, "description": "movement type"}, {"_id": "2080", "path": "supplier", "locale": "", "dataType": "reference", "isCustom": false, "description": "supplier (#businessEntity)"}, {"_id": "2090", "path": "effectiveDate", "locale": "", "dataType": "date", "isCustom": false, "description": "effective date (YYYY-MM-DD)"}, {"_id": "2100", "path": "orderCost", "locale": "", "dataType": "decimal", "isCustom": false, "description": "order cost"}, {"_id": "2110", "path": "valuedCost", "locale": "", "dataType": "decimal", "isCustom": false, "description": "valued cost"}, {"_id": "2120", "path": "deletedStockRecordData", "locale": "", "dataType": "json", "isCustom": false, "description": "deleted stock record data"}, {"_id": "2130", "path": "site#1", "locale": "", "dataType": "reference", "isCustom": false, "description": "site (#id)"}, {"_id": "2140", "path": "item#1", "locale": "", "dataType": "reference", "isCustom": false, "description": "item (#id)"}, {"_id": "2150", "path": "stockUnit#1", "locale": "", "dataType": "reference", "isCustom": false, "description": "stock unit (#id)"}, {"_id": "2160", "path": "quantityInStockUnit#1", "locale": "", "dataType": "decimal", "isCustom": false, "description": "quantity in stock unit"}, {"_id": "2170", "path": "status#3", "locale": "", "dataType": "reference", "isCustom": false, "description": "status (#id)"}, {"_id": "2180", "path": "///stockDetailLot", "locale": "", "dataType": "reference", "isCustom": false, "description": "stock detail lot"}, {"_id": "2190", "path": "lot", "locale": "", "dataType": "reference", "isCustom": false, "description": "lot (#item|id|sublot)"}, {"_id": "2200", "path": "lotNumber", "locale": "", "dataType": "string", "isCustom": false, "description": "lot number"}, {"_id": "2210", "path": "supplierLot", "locale": "", "dataType": "string", "isCustom": false, "description": "supplier lot"}, {"_id": "2220", "path": "expirationDate", "locale": "", "dataType": "date", "isCustom": false, "description": "expiration date (YYYY-MM-DD)"}, {"_id": "2230", "path": "sublot", "locale": "", "dataType": "string", "isCustom": false, "description": "sublot"}, {"_id": "2240", "path": "//stockSiteAddress", "locale": "", "dataType": "reference", "isCustom": false, "description": "stock site address"}, {"_id": "2250", "path": "*name#4", "locale": "", "dataType": "string", "isCustom": false, "description": "name"}, {"_id": "2260", "path": "addressLine1#4", "locale": "", "dataType": "string", "isCustom": false, "description": "address line 1"}, {"_id": "2270", "path": "addressLine2#4", "locale": "", "dataType": "string", "isCustom": false, "description": "address line 2"}, {"_id": "2280", "path": "city#4", "locale": "", "dataType": "string", "isCustom": false, "description": "city"}, {"_id": "2290", "path": "region#5", "locale": "", "dataType": "string", "isCustom": false, "description": "region"}, {"_id": "2300", "path": "postcode#4", "locale": "", "dataType": "string", "isCustom": false, "description": "postcode"}, {"_id": "2310", "path": "country#5", "locale": "", "dataType": "reference", "isCustom": false, "description": "country (#id)"}, {"_id": "2320", "path": "locationPhoneNumber#8", "locale": "", "dataType": "string", "isCustom": false, "description": "location phone number"}, {"_id": "2330", "path": "#taxes", "locale": "", "dataType": "collection", "isCustom": false, "description": "taxes"}, {"_id": "2340", "path": "taxRate#1", "locale": "", "dataType": "decimal", "isCustom": false, "description": "tax rate"}, {"_id": "2350", "path": "taxCategoryReference#1", "locale": "", "dataType": "reference", "isCustom": false, "description": "tax category reference (#id)"}, {"_id": "2360", "path": "taxReference#1", "locale": "", "dataType": "reference", "isCustom": false, "description": "tax reference (#id)"}, {"_id": "2370", "path": "deductibleTaxRate#1", "locale": "", "dataType": "decimal", "isCustom": false, "description": "deductible tax rate"}, {"_id": "2380", "path": "isReverseCharge#1", "locale": "", "dataType": "boolean", "isCustom": false, "description": "is reverse charge (false/true)"}, {"_id": "2390", "path": "isTaxMandatory#1", "locale": "", "dataType": "boolean", "isCustom": false, "description": "is tax mandatory (true/false)"}, {"_id": "2400", "path": "isSubjectToGlTaxExcludedAmount#1", "locale": "", "dataType": "boolean", "isCustom": false, "description": "is subject to gl tax excluded amount (false/true)"}, {"_id": "2410", "path": "jurisdictionName#1", "locale": "", "dataType": "string", "isCustom": false, "description": "jurisdiction name"}, {"_id": "2420", "path": "country#6", "locale": "", "dataType": "string", "isCustom": false, "description": "country"}, {"_id": "2430", "path": "region#6", "locale": "", "dataType": "string", "isCustom": false, "description": "region"}, {"_id": "2440", "path": "jurisdictionCode#1", "locale": "", "dataType": "string", "isCustom": false, "description": "jurisdiction code"}, {"_id": "2450", "path": "jurisdictionType#1", "locale": "", "dataType": "string", "isCustom": false, "description": "jurisdiction type"}, {"_id": "2460", "path": "taxCategory#1", "locale": "base", "dataType": "localized text", "isCustom": false, "description": "\"default locale: base, other locales can be specified with (locale-name), for example \"\"tax category (de-DE)\"\". Available locales are (en-GB, fr-FR, ...). You can also duplicate the columns to import several translations\""}, {"_id": "2470", "path": "tax#1", "locale": "base", "dataType": "localized text", "isCustom": false, "description": "\"default locale: base, other locales can be specified with (locale-name), for example \"\"tax (de-DE)\"\". Available locales are (en-GB, fr-FR, ...). You can also duplicate the columns to import several translations\""}, {"_id": "2480", "path": "deductibleTaxAmount#1", "locale": "", "dataType": "decimal", "isCustom": false, "description": "deductible tax amount"}, {"_id": "2490", "path": "taxAmountAdjusted#2", "locale": "", "dataType": "decimal", "isCustom": false, "description": "tax amount adjusted"}, {"_id": "2500", "path": "nonTaxableAmount#1", "locale": "", "dataType": "decimal", "isCustom": false, "description": "non taxable amount"}, {"_id": "2510", "path": "taxAmount#2", "locale": "", "dataType": "decimal", "isCustom": false, "description": "tax amount"}, {"_id": "2520", "path": "exemptAmount#2", "locale": "", "dataType": "decimal", "isCustom": false, "description": "exempt amount"}, {"_id": "2530", "path": "taxableAmount#2", "locale": "", "dataType": "decimal", "isCustom": false, "description": "taxable amount"}]}