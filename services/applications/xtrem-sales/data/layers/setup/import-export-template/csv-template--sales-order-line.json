{"data": [{"_id": "0", "path": "!document", "locale": "", "dataType": "reference", "isCustom": false, "description": "document (#number)"}, {"_id": "620", "path": "document.site", "locale": "", "dataType": "reference", "description": "sales site (#id)"}, {"_id": "630", "path": "document.site.id", "locale": "", "dataType": "string", "description": "id"}, {"_id": "640", "path": "document.site.name", "locale": "", "dataType": "string", "description": "name"}, {"_id": "650", "path": "document.site.legalCompany", "locale": "", "dataType": "reference", "description": "legal company (#id)"}, {"_id": "660", "path": "document.site.legalCompany.id", "locale": "", "dataType": "string", "description": "id"}, {"_id": "670", "path": "document.site.legalCompany.name", "locale": "", "dataType": "string", "description": "name"}, {"_id": "680", "path": "document.soldToCustomer", "locale": "", "dataType": "reference", "description": "sold to customer (#businessEntity)"}, {"_id": "560", "path": "document.currency", "locale": "", "dataType": "reference", "description": "currency (#id)"}, {"_id": "570", "path": "document.currency.id", "locale": "", "dataType": "string", "description": "id"}, {"_id": "540", "path": "document.number", "locale": "", "dataType": "string", "description": "number"}, {"_id": "550", "path": "document.customerNumber", "locale": "", "dataType": "string", "description": "customer number"}, {"_id": "690", "path": "document.status", "locale": "", "dataType": "enum(quote,pending,inProgress,closed)", "description": "status"}, {"_id": "700", "path": "document.shippingDate", "locale": "", "dataType": "date", "description": "shipping date (yyyy-MM-dd)"}, {"_id": "710", "path": "document.date", "locale": "", "dataType": "date", "description": "order date (yyyy-MM-dd)"}, {"_id": "50", "path": "*item", "locale": "", "dataType": "reference", "isCustom": false, "description": "item (#id)"}, {"_id": "500", "path": "item.name", "locale": "en", "dataType": "localized text", "description": "\"default locale: base, other locales can be specified with (locale-name), for example \"\"name (de-DE)\"\". Available locales are (en-GB, fr-FR, ...). You can also duplicate the columns to import several translations\""}, {"_id": "510", "path": "item.id", "locale": "", "dataType": "string", "description": "id"}, {"_id": "520", "path": "item.description", "locale": "en", "dataType": "localized text", "description": "\"default locale: base, other locales can be specified with (locale-name), for example \"\"description (de-DE)\"\". Available locales are (en-GB, fr-FR, ...). You can also duplicate the columns to import several translations\""}, {"_id": "720", "path": "*quantity", "locale": "", "dataType": "decimal", "isCustom": false, "description": "quantity in sales unit"}, {"_id": "90", "path": "unit", "locale": "", "dataType": "reference", "isCustom": false, "description": "sales unit (#id)"}, {"_id": "750", "path": "unit.name", "locale": "en", "dataType": "localized text", "description": "\"default locale: base, other locales can be specified with (locale-name), for example \"\"name (de-DE)\"\". Available locales are (en-GB, fr-FR, ...). You can also duplicate the columns to import several translations\""}, {"_id": "730", "path": "shippingStatus", "locale": "", "dataType": "enum(notShipped,partiallyShipped,shipped)", "isCustom": false, "description": "shipping status"}, {"_id": "740", "path": "invoiceStatus", "locale": "", "dataType": "enum(notInvoiced,partiallyInvoiced,invoiced)", "isCustom": false, "description": "invoice status"}, {"_id": "380", "path": "netPrice", "locale": "", "dataType": "decimal", "isCustom": false, "description": "net price"}, {"_id": "420", "path": "amountExcludingTaxInCompanyCurrency", "locale": "", "dataType": "decimal", "isCustom": false, "description": "line amount excluding tax in company currency"}]}