"step_constructor";"variant";"_vendor";"factory";"is_active";"title";"description";"icon";"service_options";"config_data"
"mutation";"_sales_order_confirm";"sage";"SalesOrder";"Y";"{""base"":""Sales order: Confirm""}";"{""base"":""Confirms a sales order.""}";"connected";"[]";"file:config-data--mutation-sales-order-confirm.json"
"mutation";"_sales_order_auto_allocate";"sage";"SalesOrder";"Y";"{""base"":""Sales order: Allocate stock""}";"{""base"":""Allocate sales order stock.""}";"connected";"[]";"file:config-data--mutation-sales-order-auto-allocate.json"
"mutation";"_sales_shipment_confirm";"sage";"SalesShipment";"Y";"{""base"":""Sales shipment: Confirm""}";"{""en"":""Confirms a sales shipment."",""base"":""Confirms a sales shipment."",""en-US"":""Confirms a sales shipment.""}";"connected";"[]";"file:config-data--mutation-sales-shipment-confirm.json"
"mutation";"_sales_shipment_create_sales_invoice";"sage";"SalesShipment";"Y";"{""base"":""Sales shipment: Create sales invoice""}";"{""en"":""Creates a sales invoice from a sales shipment."",""base"":""Creates a sales invoice from a sales shipment."",""en-US"":""Creates a sales invoice from a sales shipment.""}";"connected";"[]";"file:config-data--mutation-sales-shipment-create-sales-invoice.json"
"mutation";"_sales_order_close";"sage";"SalesOrder";"Y";"{""en"":""Sales order - close"",""base"":""Sales order - close"",""en-US"":""Sales order - close""}";"{""en"":""Closes a sales order."",""base"":""Closes a sales order."",""en-US"":""Closes a sales order.""}";"connected";"[]";"file:config-data--mutation-sales-order-close.json"
"mutation";"_sales_order_create_shipments_from_order";"sage";"SalesOrder";"Y";"{""en"":""Sales order - create shipments from order"",""base"":""Sales order - create shipments from order"",""en-US"":""Sales order - create shipments from order""}";"{""en"":""Creates sales shipments from a sales order."",""base"":""Creates sales shipments from a sales order."",""en-US"":""Creates sales shipments from a sales order.""}";"connected";"[]";"file:config-data--mutation-sales-order-create-shipments-from-order.json"
"mutation";"_sales_invoice_post";"sage";"SalesInvoice";"Y";"{""en"":""Sales invoice - post"",""base"":""Sales invoice - post"",""en-US"":""Sales invoice - post""}";"{""en"":""Posts a sales invoice."",""base"":""Posts a sales invoice."",""en-US"":""Posts a sales invoice.""}";"connected";"[""workflowAdvanced""]";"file:config-data--mutation-sales-invoice-post.json"
"mutation";"_sales_shipment_post_to_stock";"sage";"SalesShipment";"Y";"{""en"":""Sales shipment - Post to stock"",""base"":""Sales shipment - Post to stock"",""en-US"":""Sales shipment - Post to stock""}";"{""en"":""Posts a sales shipment."",""base"":""Posts a sales shipment."",""en-US"":""Posts a sales shipment.""}";"connected";"[]";"file:config-data--mutation-sales-shipment-post-to-stock.json"
"mutation";"_create_backtoback_order";"sage";"SalesOrder";"Y";"{""en"":""Sales Order: create back-to-back order"",""base"":""Create back-to-back order"",""en-US"":""Sales Order: create back-to-back order""}";"{""en"":""Create back-to-back orders from a sales order."",""base"":""Create back-to-back orders from a sales order."",""en-US"":""Create back-to-back orders from a sales order.""}";"connected";"[""orderToOrderOption""]";"file:config-data--mutation-create-backtoback-order.json"
