{"@sage/xtrem-stock": [{"topic": "AllocationListener/allocate/start", "queue": "stock", "sourceFileName": "allocation-listener.ts"}, {"topic": "AllocationListener/asyncExport/start", "queue": "import-export", "sourceFileName": "allocation-listener.ts"}, {"topic": "CostRollUpInputSet/asyncExport/start", "queue": "import-export", "sourceFileName": "cost-roll-up-input-set.ts"}, {"topic": "CostRollUpResultLine/asyncExport/start", "queue": "import-export", "sourceFileName": "cost-roll-up-result-line.ts"}, {"topic": "CostRollUpResultLine/createItemSiteCostsFromCostRollUpResults/start", "queue": "stock", "sourceFileName": "cost-roll-up-result-line.ts"}, {"topic": "CostRollUpSubAssembly/asyncExport/start", "queue": "import-export", "sourceFileName": "cost-roll-up-sub-assembly.ts"}, {"topic": "Item/createTestItems/start", "queue": "stock", "sourceFileName": "item-extension.ts"}, {"topic": "ItemSiteCost/standardCostRollUpCalculation/start", "queue": "stock", "sourceFileName": "item-site-cost-extension.ts"}, {"topic": "ItemSiteCost/syncStockValueChange/start", "queue": "stock", "sourceFileName": "item-site-cost-extension.ts"}, {"topic": "MiscellaneousStockIssue/accountingInterface", "queue": "stock", "sourceFileName": "stock-issue.ts"}, {"topic": "MiscellaneousStockReceipt/accountingInterface", "queue": "stock", "sourceFileName": "stock-receipt.ts"}, {"topic": "MrpCalculation/deleteOldMrpCalculations/start", "queue": "stock", "sourceFileName": "mrp-calculation-extension.ts"}, {"topic": "Stock/adjust/start", "queue": "stock", "sourceFileName": "stock-extension.ts"}, {"topic": "Stock/change/start", "queue": "stock", "sourceFileName": "stock-extension.ts"}, {"topic": "Stock/changeValue/start", "queue": "stock", "sourceFileName": "stock-extension.ts"}, {"topic": "Stock/correct/start", "queue": "stock", "sourceFileName": "stock-extension.ts"}, {"topic": "Stock/issue/start", "queue": "stock", "sourceFileName": "stock-extension.ts"}, {"topic": "Stock/receive/start", "queue": "stock", "sourceFileName": "stock-extension.ts"}, {"topic": "Stock/transfer/start", "queue": "stock", "sourceFileName": "stock-extension.ts"}, {"topic": "StockAdjustment/accountingInterface", "queue": "stock", "sourceFileName": "stock-adjustment.ts"}, {"topic": "StockAdjustment/asyncExport/start", "queue": "import-export", "sourceFileName": "stock-adjustment.ts"}, {"topic": "StockAdjustment/resendNotificationForFinance", "queue": "stock", "sourceFileName": "stock-adjustment.ts"}, {"topic": "StockAdjustment/stock/adjustment/reply", "queue": "stock", "sourceFileName": "stock-adjustment.ts"}, {"topic": "StockAdjustmentLine/asyncExport/start", "queue": "import-export", "sourceFileName": "stock-adjustment-line.ts"}, {"topic": "StockChange/asyncExport/start", "queue": "import-export", "sourceFileName": "stock-change.ts"}, {"topic": "StockChange/stock/change/reply", "queue": "stock", "sourceFileName": "stock-change.ts"}, {"topic": "StockChangeLine/asyncExport/start", "queue": "import-export", "sourceFileName": "stock-change-line.ts"}, {"topic": "StockCount/accountingInterface", "queue": "stock", "sourceFileName": "stock-count.ts"}, {"topic": "StockCount/asyncExport/start", "queue": "import-export", "sourceFileName": "stock-count.ts"}, {"topic": "StockCount/confirmStockCount/start", "queue": "stock", "sourceFileName": "stock-count.ts"}, {"topic": "StockCount/confirmZeroQuantity/start", "queue": "stock", "sourceFileName": "stock-count.ts"}, {"topic": "StockCount/resendNotificationForFinance", "queue": "stock", "sourceFileName": "stock-count.ts"}, {"topic": "StockCount/stock/adjustment/reply", "queue": "stock", "sourceFileName": "stock-count.ts"}, {"topic": "StockCountLine/asyncExport/start", "queue": "import-export", "sourceFileName": "stock-count-line.ts"}, {"topic": "StockCountLineSerialNumber/asyncExport/start", "queue": "import-export", "sourceFileName": "stock-count-line-serial-number.ts"}, {"topic": "StockIssue/accountingInterface", "queue": "stock", "sourceFileName": "stock-issue.ts"}, {"topic": "StockIssue/asyncExport/start", "queue": "import-export", "sourceFileName": "stock-issue.ts"}, {"topic": "StockIssue/resendNotificationForFinance", "queue": "stock", "sourceFileName": "stock-issue.ts"}, {"topic": "StockIssue/stock/issue/reply", "queue": "stock", "sourceFileName": "stock-issue.ts"}, {"topic": "StockIssueLine/asyncExport/start", "queue": "import-export", "sourceFileName": "stock-issue-line.ts"}, {"topic": "StockReceipt/accountingInterface", "queue": "stock", "sourceFileName": "stock-receipt.ts"}, {"topic": "StockReceipt/asyncExport/start", "queue": "import-export", "sourceFileName": "stock-receipt.ts"}, {"topic": "StockReceipt/createTestStockReceipt/start", "queue": "stock", "sourceFileName": "stock-receipt.ts"}, {"topic": "StockReceipt/resendNotificationForFinance", "queue": "stock", "sourceFileName": "stock-receipt.ts"}, {"topic": "StockReceipt/stock/receipt/reply", "queue": "stock", "sourceFileName": "stock-receipt.ts"}, {"topic": "StockReceiptLine/asyncExport/start", "queue": "import-export", "sourceFileName": "stock-receipt-line.ts"}, {"topic": "StockReorderCalculationInputSet/asyncExport/start", "queue": "import-export", "sourceFileName": "stock-reorder-calculation-input-set.ts"}, {"topic": "StockReorderCalculationInputSet/reorderCalculation/start", "queue": "stock", "sourceFileName": "stock-reorder-calculation-input-set.ts"}, {"topic": "StockReorderCalculationResultLine/asyncExport/start", "queue": "import-export", "sourceFileName": "stock-reorder-calculation-result-line.ts"}, {"topic": "StockValuationInputSet/asyncExport/start", "queue": "import-export", "sourceFileName": "stock-valuation-input-set.ts"}, {"topic": "StockValuationInputSet/stockValuation/start", "queue": "stock", "sourceFileName": "stock-valuation-input-set.ts"}, {"topic": "StockValuationInputSetToSite/asyncExport/start", "queue": "import-export", "sourceFileName": "stock-valuation-input-set-to-site.ts"}, {"topic": "StockValuationResultLine/asyncExport/start", "queue": "import-export", "sourceFileName": "stock-valuation-result-line.ts"}, {"topic": "StockValueChange/accountingInterface", "queue": "stock", "sourceFileName": "stock-value-change.ts"}, {"topic": "StockValueChange/asyncExport/start", "queue": "import-export", "sourceFileName": "stock-value-change.ts"}, {"topic": "StockValueChange/resendNotificationForFinance", "queue": "stock", "sourceFileName": "stock-value-change.ts"}, {"topic": "StockValueChange/stock/valueChange/reply", "queue": "stock", "sourceFileName": "stock-value-change.ts"}, {"topic": "StockValueChangeLine/asyncExport/start", "queue": "import-export", "sourceFileName": "stock-value-change-line.ts"}, {"topic": "StockValueCorrection/accountingInterface", "queue": "stock", "sourceFileName": "stock-value-correction.ts"}, {"topic": "StockValueCorrection/asyncExport/start", "queue": "import-export", "sourceFileName": "stock-value-correction.ts"}, {"topic": "StockValueCorrection/stock/correction/reply", "queue": "stock", "sourceFileName": "stock-value-correction.ts"}, {"topic": "StockValueCorrectionLine/asyncExport/start", "queue": "import-export", "sourceFileName": "stock-value-correction-line.ts"}]}