import type { Collection, decimal, integer, Reference } from '@sage/xtrem-core';
import { decorators } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremStockData from '@sage/xtrem-stock-data';
import * as xtremStock from '../../../../lib';
import { FakeStockNegativeReceipt } from './fake-stock-negative-receipt';

@decorators.subNode<FakeStockNegativeReceiptLine>({
    canCreate: true,
    canDelete: true,
    canRead: true,
    canSearch: true,
    canUpdate: true,
    isPublished: true,
    isVitalCollectionChild: true,
    extends: () => xtremMasterData.nodes.BaseDocumentLine,
})
@xtremStockData.functions.staticImplements<xtremStockData.interfaces.StaticDocumentLineWithStockPosting>()
export class FakeStockNegativeReceiptLine
    extends xtremMasterData.nodes.BaseDocumentLine
    implements xtremStockData.interfaces.DocumentLineWithStockAllocation
{
    // eslint-disable-next-line class-methods-use-this
    getOrderCost() {
        return 0;
    }

    // eslint-disable-next-line class-methods-use-this
    getValuedCost() {
        return 0;
    }

    async getValuationParameters(): Promise<xtremStockData.interfaces.ValuationParameter> {
        return {
            valuationType: 'negativeReceipt',
            originDocumentLine: (await this.originReceiptLine) ?? undefined,
        };
    }

    getItem() {
        return this.item;
    }

    @decorators.booleanProperty<FakeStockNegativeReceiptLine, 'canBeReturned'>({
        isStored: true,
        defaultValue: false,
    })
    canBeReturned: boolean;

    @decorators.decimalProperty<FakeStockNegativeReceiptLine, 'lineNumber'>({
        isStored: true,
        dataType: () => xtremMasterData.dataTypes.baseDecimal,
    })
    readonly lineNumber: Promise<decimal>;

    @decorators.referenceProperty<FakeStockNegativeReceiptLine, 'document'>({
        isStored: true,
        isVitalParent: true,
        node: () => FakeStockNegativeReceipt,
    })
    override readonly document: Reference<FakeStockNegativeReceipt>;

    @decorators.stringPropertyOverride<FakeStockNegativeReceiptLine, 'documentNumber'>({
        async getValue() {
            return (await this.document).number;
        },
    })
    override readonly documentNumber: Promise<string>;

    @decorators.integerPropertyOverride<FakeStockNegativeReceiptLine, 'documentId'>({
        async getValue() {
            return (await this.document)._id;
        },
    })
    override readonly documentId: Promise<integer>;

    @decorators.referenceProperty<FakeStockNegativeReceiptLine, 'item'>({
        isStored: true,
        node: () => xtremMasterData.nodes.Item,
        defaultValue() {
            return this.$.context.read(xtremMasterData.nodes.Item, { _id: 1 });
        },
    })
    readonly item: Reference<xtremMasterData.nodes.Item>;

    @decorators.decimalProperty<FakeStockNegativeReceiptLine, 'quantityInStockUnit'>({
        isStored: true,
        dataType: () => xtremMasterData.dataTypes.baseDecimal,
    })
    readonly quantityInStockUnit: Promise<decimal>;

    @decorators.enumProperty<FakeStockNegativeReceiptLine, 'stockTransactionStatus'>({
        isStored: true,
        defaultValue() {
            return 'draft';
        },
        dataType: () => xtremStockData.enums.StockDocumentTransactionStatusDataType,
    })
    readonly stockTransactionStatus: Promise<xtremStockData.enums.StockDocumentTransactionStatus>;

    @decorators.collectionProperty<FakeStockNegativeReceiptLine, 'stockDetails'>({
        isVital: true,
        reverseReference: 'documentLine',
        node: () => xtremStockData.nodes.StockIssueDetail,
    })
    readonly stockDetails: Collection<xtremStockData.nodes.StockIssueDetail>;

    @decorators.collectionProperty<FakeStockNegativeReceiptLine, 'stockTransactions'>({
        isPublished: true,
        reverseReference: 'documentLine',
        isVital: true,
        node: () => xtremStockData.nodes.StockTransaction,
    })
    readonly stockTransactions: Collection<xtremStockData.nodes.StockTransaction>;

    @decorators.decimalProperty<FakeStockNegativeReceiptLine, 'quantityAllocated'>({
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.quantityInStockUnit,
        computeValue() {
            return xtremStockData.functions.allocationLib.getDocumentLineAllocatedQuantity(this);
        },
    })
    readonly quantityAllocated: Promise<decimal>;

    @decorators.decimalProperty<FakeStockNegativeReceiptLine, 'remainingQuantityToAllocate'>({
        isPublished: true,
        dependsOn: ['quantityInStockUnit', 'quantityAllocated'],
        dataType: () => xtremMasterData.dataTypes.quantityInStockUnit,
        async getValue() {
            return (await this.quantityInStockUnit) - (await this.quantityAllocated);
        },
    })
    readonly remainingQuantityToAllocate: Promise<decimal>;

    @decorators.enumProperty<FakeStockNegativeReceiptLine, 'allocationStatus'>({
        isPublished: true,
        dataType: () => xtremStockData.enums.stockAllocationStatusDataType,
        dependsOn: ['remainingQuantityToAllocate', 'quantityAllocated', 'item'],
        async getValue() {
            const item = await this.item;
            return xtremStockData.functions.allocationLib.getLineAllocationStatus(
                await this.remainingQuantityToAllocate,
                await this.quantityAllocated,
                await item.isStockManaged,
            );
        },
    })
    readonly allocationStatus: Promise<xtremStockData.enums.StockAllocationStatus>;

    @decorators.collectionProperty<FakeStockNegativeReceiptLine, 'stockAllocations'>({
        isPublished: true,
        reverseReference: 'documentLine',
        node: () => xtremStockData.nodes.StockAllocation,
    })
    readonly stockAllocations: Collection<xtremStockData.nodes.StockAllocation>;

    @decorators.referenceProperty<FakeStockNegativeReceiptLine, 'originReceiptLine'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
        node: () => xtremStock.nodes.StockReceiptLine,
    })
    readonly originReceiptLine: Reference<xtremStock.nodes.StockReceiptLine> | null;
}
