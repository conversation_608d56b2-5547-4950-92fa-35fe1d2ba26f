import type { NotificationEnvelope } from '@sage/xtrem-communication';
import type { Collection, Context, integer } from '@sage/xtrem-core';
import { Logger, Node, date, decorators } from '@sage/xtrem-core';
import * as xtremStockData from '@sage/xtrem-stock-data';
import * as xtremSystem from '@sage/xtrem-system';
import { FakeStockReceiptLine } from './fake-stock-receipt-line';

const logger = Logger.getLogger(__filename, 'StockReceipt');

@decorators.node<FakeStockReceipt>({
    storage: 'sql',
    canCreate: true,
    canDelete: true,
    canRead: true,
    canSearch: true,
    canUpdate: true,
    isPublished: true,
})
@xtremStockData.functions.staticImplements<xtremStockData.interfaces.StaticDocumentHeaderWithStockPosting>()
export class FakeStockReceipt extends Node {
    // eslint-disable-next-line class-methods-use-this
    getEffectiveDate() {
        return date.today();
    }

    getStockSite() {
        return this.$.context.read(xtremSystem.nodes.Site, { id: 'US001' });
    }

    @decorators.stringProperty<FakeStockReceipt, 'number'>({
        isStored: true,
        dataType: () => xtremSystem.dataTypes.code,
    })
    readonly number: Promise<string>;

    @decorators.booleanProperty<FakeStockReceipt, 'forceUpdateForStock'>({
        defaultValue: false,
    })
    readonly forceUpdateForStock: Promise<boolean>;

    @decorators.enumProperty<FakeStockReceipt, 'stockTransactionStatus'>({
        isPublished: true,
        dataType: () => xtremStockData.enums.StockDocumentTransactionStatusDataType,
        computeValue() {
            return xtremStockData.functions.stockTransactionLib.computeHeaderStatus(this);
        },
    })
    readonly stockTransactionStatus: Promise<xtremStockData.enums.StockDocumentTransactionStatus>;

    @decorators.collectionProperty<FakeStockReceipt, 'lines'>({
        isVital: true,
        reverseReference: 'document',
        node: () => FakeStockReceiptLine,
    })
    readonly lines: Collection<FakeStockReceiptLine>;

    readonly linesWithStockTransactionStatusCollectionName = 'lines';

    @decorators.mutation<typeof FakeStockReceipt, 'postToStock'>({
        isPublished: true,
        parameters: [
            {
                name: 'documentIds',
                type: 'array',
                item: {
                    type: 'integer',
                },
            },
        ],
        return: {
            type: 'string',
        },
    })
    static postToStock(context: Context, documentIds: integer[]): Promise<string> {
        return xtremStockData.functions.notificationLib.stockReceiptRequestNotification(context, {
            documentClass: FakeStockReceipt,
            documentIds,
        });
    }

    @decorators.notificationListener<typeof FakeStockReceipt>({
        startsReadOnly: true,
        topic: 'FakeStockReceipt/stock/receipt/reply',
        onError(
            context: Context,
            envelope: NotificationEnvelope<
                xtremStockData.interfaces.StockReplyPayload<xtremStockData.enums.StockMovementTypeEnum.receipt>
            >,
            error: Error,
        ) {
            return xtremStockData.functions.notificationLib.errorHandler(context, envelope, error, FakeStockReceipt);
        },
    })
    static async onStockReply(
        readOnlyContext: Context,
        payload: xtremStockData.interfaces.StockReplyPayload<xtremStockData.enums.StockMovementTypeEnum.receipt>,
    ): Promise<void> {
        logger.info(`reply received: \n ${JSON.stringify(payload)}`);
        await xtremStockData.functions.notificationLib.reactToStockMovementReply(
            readOnlyContext,
            payload,
            FakeStockReceipt,
        );
    }
}
