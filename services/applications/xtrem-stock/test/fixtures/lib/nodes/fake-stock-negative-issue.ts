import type { NotificationEnvelope } from '@sage/xtrem-communication';
import type { Collection, Context, integer } from '@sage/xtrem-core';
import { Logger, decorators } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremStockData from '@sage/xtrem-stock-data';
import * as xtremSystem from '@sage/xtrem-system';
import { FakeStockNegativeIssueLine } from './fake-stock-negative-issue-line';

const logger = Logger.getLogger(__filename, 'StockReceipt');

@decorators.subNode<FakeStockNegativeIssue>({
    canCreate: true,
    canDelete: true,
    canRead: true,
    canSearch: true,
    canUpdate: true,
    isPublished: true,
    extends: () => xtremMasterData.nodes.BaseDocument,
})
@xtremStockData.functions.staticImplements<xtremStockData.interfaces.StaticDocumentHeaderWithStockPosting>()
export class FakeStockNegativeIssue extends xtremMasterData.nodes.BaseDocument {
    override async getStockSite() {
        return (await this.stockSite) ?? this.$.context.read(xtremSystem.nodes.Site, { id: 'US001' });
    }

    @decorators.enumProperty<FakeStockNegativeIssue, 'stockTransactionStatus'>({
        isPublished: true,
        dataType: () => xtremStockData.enums.StockDocumentTransactionStatusDataType,
        computeValue() {
            return xtremStockData.functions.stockTransactionLib.computeHeaderStatus(this);
        },
    })
    readonly stockTransactionStatus: Promise<xtremStockData.enums.StockDocumentTransactionStatus>;

    @decorators.collectionPropertyOverride<FakeStockNegativeIssue, 'lines'>({
        node: () => FakeStockNegativeIssueLine,
    })
    override readonly lines: Collection<FakeStockNegativeIssueLine>;

    readonly linesWithStockTransactionStatusCollectionName = 'lines';

    @decorators.mutation<typeof FakeStockNegativeIssue, 'postToStock'>({
        isPublished: true,
        parameters: [
            {
                name: 'documentIds',
                type: 'array',
                item: {
                    type: 'integer',
                },
            },
        ],
        return: {
            type: 'string',
        },
    })
    static postToStock(context: Context, documentIds: integer[]): Promise<string> {
        return xtremStockData.functions.notificationLib.stockReceiptRequestNotification(context, {
            documentClass: FakeStockNegativeIssue,
            documentIds,
        });
    }

    @decorators.notificationListener<typeof FakeStockNegativeIssue>({
        startsReadOnly: true,
        topic: 'FakeStockNegativeIssue/stock/receipt/reply',
        onError(
            context: Context,
            envelope: NotificationEnvelope<
                xtremStockData.interfaces.StockReplyPayload<xtremStockData.enums.StockMovementTypeEnum.receipt>
            >,
            error: Error,
        ) {
            return xtremStockData.functions.notificationLib.errorHandler(
                context,
                envelope,
                error,
                FakeStockNegativeIssue,
            );
        },
    })
    static async onStockReply(
        readOnlyContext: Context,
        payload: xtremStockData.interfaces.StockReplyPayload<xtremStockData.enums.StockMovementTypeEnum.receipt>,
    ): Promise<void> {
        logger.info(`reply received: \n ${JSON.stringify(payload)}`);
        await readOnlyContext.runInWritableContext(writableContext =>
            xtremStockData.functions.notificationLib.reactToStockMovementReply(
                writableContext,
                payload,
                FakeStockNegativeIssue,
            ),
        );
    }
}
