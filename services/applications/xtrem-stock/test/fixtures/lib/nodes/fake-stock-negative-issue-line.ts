import type { Collection, decimal, integer, Reference } from '@sage/xtrem-core';
import { decorators } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremStockData from '@sage/xtrem-stock-data';
import * as xtremStock from '../../../../lib';
import { FakeStockNegativeIssue } from './fake-stock-negative-issue';

/**
 * A SalesReturnLine-like, where an "originIssueLine" can be assigned
 */
@decorators.subNode<FakeStockNegativeIssueLine>({
    canCreate: true,
    canDelete: true,
    canRead: true,
    canSearch: true,
    canUpdate: true,
    isPublished: true,
    isVitalCollectionChild: true,
    extends: () => xtremMasterData.nodes.BaseDocumentItemLine,
})
@xtremStockData.functions.staticImplements<xtremStockData.interfaces.StaticDocumentLineWithStockPosting>()
export class FakeStockNegativeIssueLine extends xtremMasterData.nodes.BaseDocumentItemLine {
    // eslint-disable-next-line class-methods-use-this
    getOrderCost() {
        return 0;
    }

    // eslint-disable-next-line class-methods-use-this
    getValuedCost() {
        return 0;
    }

    async getValuationParameters(): Promise<xtremStockData.interfaces.ValuationParameter> {
        return { valuationType: 'negativeIssue', originDocumentLine: (await this.originIssueLine) ?? undefined };
    }

    override async getItem() {
        return (await this.item) ?? this.$.context.read(xtremMasterData.nodes.Item, { _id: 1 });
    }

    @decorators.decimalProperty<FakeStockNegativeIssueLine, 'lineNumber'>({
        isStored: true,
        dataType: () => xtremMasterData.dataTypes.baseDecimal,
    })
    readonly lineNumber: Promise<decimal>;

    @decorators.referencePropertyOverride<FakeStockNegativeIssueLine, 'document'>({
        node: () => FakeStockNegativeIssue,
    })
    override readonly document: Reference<FakeStockNegativeIssue>;

    @decorators.stringPropertyOverride<FakeStockNegativeIssueLine, 'documentNumber'>({
        async getValue() {
            return (await this.document).number;
        },
    })
    override readonly documentNumber: Promise<string>;

    @decorators.integerPropertyOverride<FakeStockNegativeIssueLine, 'documentId'>({
        async getValue() {
            return (await this.document)._id;
        },
    })
    override readonly documentId: Promise<integer>;

    @decorators.enumProperty<FakeStockNegativeIssueLine, 'stockTransactionStatus'>({
        isStored: true,
        defaultValue() {
            return 'draft';
        },
        dataType: () => xtremStockData.enums.StockDocumentTransactionStatusDataType,
    })
    readonly stockTransactionStatus: Promise<xtremStockData.enums.StockDocumentTransactionStatus>;

    @decorators.collectionProperty<FakeStockNegativeIssueLine, 'stockDetails'>({
        isVital: true,
        reverseReference: 'documentLine',
        node: () => xtremStockData.nodes.StockReceiptDetail,
    })
    readonly stockDetails: Collection<xtremStockData.nodes.StockReceiptDetail>;

    @decorators.collectionProperty<FakeStockNegativeIssueLine, 'stockTransactions'>({
        isPublished: true,
        reverseReference: 'documentLine',
        isVital: true,
        node: () => xtremStockData.nodes.StockTransaction,
    })
    readonly stockTransactions: Collection<xtremStockData.nodes.StockTransaction>;

    @decorators.referenceProperty<FakeStockNegativeIssueLine, 'originIssueLine'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
        node: () => xtremStock.nodes.StockIssueLine,
    })
    readonly originIssueLine: Reference<xtremStock.nodes.StockIssueLine> | null;
}
