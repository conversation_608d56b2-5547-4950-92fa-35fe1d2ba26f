import type { Collection, decimal, integer, Reference } from '@sage/xtrem-core';
import { decorators } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremStockData from '@sage/xtrem-stock-data';
import { FakeStockReceipt } from './fake-stock-receipt';

@decorators.subNode<FakeStockReceiptLine>({
    canCreate: true,
    canDelete: true,
    canRead: true,
    canSearch: true,
    canUpdate: true,
    isPublished: true,
    isVitalCollectionChild: true,
    extends: () => xtremMasterData.nodes.BaseDocumentLine,
})
@xtremStockData.functions.staticImplements<xtremStockData.interfaces.StaticDocumentLineWithStockPosting>()
export class FakeStockReceiptLine extends xtremMasterData.nodes.BaseDocumentLine {
    // eslint-disable-next-line class-methods-use-this
    getOrderCost() {
        return 0;
    }

    // eslint-disable-next-line class-methods-use-this
    getValuedCost() {
        return 0;
    }

    // eslint-disable-next-line class-methods-use-this
    getValuationParameters(): xtremStockData.interfaces.ValuationParameter {
        return { valuationType: 'receipt' };
    }

    getItem() {
        return this.$.context.read(xtremMasterData.nodes.Item, { _id: 1 });
    }

    @decorators.decimalProperty<FakeStockReceiptLine, 'lineNumber'>({
        isStored: true,
        dataType: () => xtremMasterData.dataTypes.baseDecimal,
    })
    readonly lineNumber: Promise<decimal>;

    @decorators.referenceProperty<FakeStockReceiptLine, 'document'>({
        isStored: true,
        isVitalParent: true,
        node: () => FakeStockReceipt,
    })
    override readonly document: Reference<FakeStockReceipt>;

    @decorators.stringPropertyOverride<FakeStockReceiptLine, 'documentNumber'>({
        async getValue() {
            return (await this.document).number;
        },
    })
    override readonly documentNumber: Promise<string>;

    @decorators.integerPropertyOverride<FakeStockReceiptLine, 'documentId'>({
        async getValue() {
            return (await this.document)._id;
        },
    })
    override readonly documentId: Promise<integer>;

    @decorators.decimalProperty<FakeStockReceiptLine, 'quantityInStockUnit'>({
        isStored: true,
        dataType: () => xtremMasterData.dataTypes.baseDecimal,
    })
    readonly quantityInStockUnit: Promise<decimal>;

    @decorators.enumProperty<FakeStockReceiptLine, 'stockTransactionStatus'>({
        isStored: true,
        defaultValue() {
            return 'draft';
        },
        dataType: () => xtremStockData.enums.StockDocumentTransactionStatusDataType,
    })
    readonly stockTransactionStatus: Promise<xtremStockData.enums.StockDocumentTransactionStatus>;

    @decorators.collectionProperty<FakeStockReceiptLine, 'stockDetails'>({
        isVital: true,
        reverseReference: 'documentLine',
        node: () => xtremStockData.nodes.StockReceiptDetail,
    })
    readonly stockDetails: Collection<xtremStockData.nodes.StockReceiptDetail>;

    @decorators.collectionProperty<FakeStockReceiptLine, 'stockTransactions'>({
        isPublished: true,
        reverseReference: 'documentLine',
        isVital: true,
        node: () => xtremStockData.nodes.StockTransaction,
    })
    readonly stockTransactions: Collection<xtremStockData.nodes.StockTransaction>;
}
