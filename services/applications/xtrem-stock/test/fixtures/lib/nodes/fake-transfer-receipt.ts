import type { NotificationEnvelope } from '@sage/xtrem-communication';
import type { Collection, Context, integer } from '@sage/xtrem-core';
import { Logger, Node, date, decorators } from '@sage/xtrem-core';
import * as xtremStockData from '@sage/xtrem-stock-data';
import * as xtremSystem from '@sage/xtrem-system';
import { FakeTransferReceiptLine } from './fake-transfer-receipt-line';

const logger = Logger.getLogger(__filename, 'StockReceipt');

@decorators.node<FakeTransferReceipt>({
    storage: 'sql',
    canCreate: true,
    canDelete: true,
    canRead: true,
    canSearch: true,
    canUpdate: true,
    isPublished: true,
    indexes: [
        {
            orderBy: { number: 1 },
            isUnique: true,
            isNaturalKey: true,
        },
    ],
})
@xtremStockData.functions.staticImplements<xtremStockData.interfaces.StaticDocumentHeaderWithStockPosting>()
export class FakeTransferReceipt extends Node {
    getEffectiveDate() {
        return this.effectiveDate;
    }

    getStockSite() {
        return this.stockSite;
    }

    @decorators.stringProperty<FakeTransferReceipt, 'number'>({
        isPublished: true,
        isStored: true,
        dataType: () => xtremSystem.dataTypes.code,
    })
    readonly number: Promise<string>;

    @decorators.referenceProperty<FakeTransferReceipt, 'stockSite'>({
        isStored: true,
        node: () => xtremSystem.nodes.Site,
        defaultValue() {
            return this.$.context.read(xtremSystem.nodes.Site, { id: 'US001' });
        },
    })
    readonly stockSite: Promise<xtremSystem.nodes.Site>;

    @decorators.booleanProperty<FakeTransferReceipt, 'forceUpdateForStock'>({
        defaultValue: false,
    })
    readonly forceUpdateForStock: Promise<boolean>;

    @decorators.referenceProperty<FakeTransferReceipt, 'shippingSite'>({
        isStored: true,
        node: () => xtremSystem.nodes.Site,
        defaultValue() {
            return this.$.context.read(xtremSystem.nodes.Site, { id: 'US001' });
        },
    })
    readonly shippingSite: Promise<xtremSystem.nodes.Site>;

    @decorators.dateProperty<FakeTransferReceipt, 'effectiveDate'>({
        isStored: true,
        defaultValue() {
            return date.today();
        },
    })
    readonly effectiveDate: Promise<date>;

    @decorators.enumProperty<FakeTransferReceipt, 'stockTransactionStatus'>({
        isPublished: true,
        dataType: () => xtremStockData.enums.StockDocumentTransactionStatusDataType,
        computeValue() {
            return xtremStockData.functions.stockTransactionLib.computeHeaderStatus(this);
        },
    })
    readonly stockTransactionStatus: Promise<xtremStockData.enums.StockDocumentTransactionStatus>;

    @decorators.collectionProperty<FakeTransferReceipt, 'lines'>({
        isVital: true,
        reverseReference: 'document',
        node: () => FakeTransferReceiptLine,
    })
    readonly lines: Collection<FakeTransferReceiptLine>;

    readonly linesWithStockTransactionStatusCollectionName = 'lines';

    @decorators.mutation<typeof FakeTransferReceipt, 'postToStock'>({
        isPublished: true,
        parameters: [
            {
                name: 'documentIds',
                type: 'array',
                item: {
                    type: 'integer',
                },
            },
        ],
        return: {
            type: 'string',
        },
    })
    static postToStock(context: Context, documentIds: integer[]): Promise<string> {
        return xtremStockData.functions.notificationLib.stockTransferRequestNotification(context, {
            documentClass: FakeTransferReceipt,
            documentIds,
            stockUpdateParameters: {
                intersiteTransferData: {
                    type: 'internalReceipt',
                },
                stockDetailData: { isDocumentWithoutStockDetails: true },
                allocationData: { isUsingAllocations: true },
            },
        });
    }

    @decorators.notificationListener<typeof FakeTransferReceipt>({
        startsReadOnly: true,
        topic: 'FakeTransferReceipt/stock/transfer/reply',
        onError(
            context: Context,
            envelope: NotificationEnvelope<
                xtremStockData.interfaces.StockReplyPayload<xtremStockData.enums.StockMovementTypeEnum.transfer>
            >,
            error: Error,
        ) {
            return xtremStockData.functions.notificationLib.errorHandler(context, envelope, error, FakeTransferReceipt);
        },
    })
    static async onStockReply(
        readOnlyContext: Context,
        payload: xtremStockData.interfaces.StockReplyPayload<xtremStockData.enums.StockMovementTypeEnum.transfer>,
    ): Promise<void> {
        logger.info(`reply received: \n ${JSON.stringify(payload)}`);
        await xtremStockData.functions.notificationLib.reactToStockMovementReply(
            readOnlyContext,
            payload,
            FakeTransferReceipt,
        );
    }
}
