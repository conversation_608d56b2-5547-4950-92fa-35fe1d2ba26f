import type { Collection, decimal, integer, Reference } from '@sage/xtrem-core';
import { decorators } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremStockData from '@sage/xtrem-stock-data';
import { FakeTransferReceipt } from './fake-transfer-receipt';
import { FakeTransferShipmentLine } from './fake-transfer-shipment-line';

@decorators.subNode<FakeTransferReceiptLine>({
    canCreate: true,
    canDelete: true,
    canRead: true,
    canSearch: true,
    canUpdate: true,
    isPublished: true,
    isVitalCollectionChild: true,
    extends: () => xtremMasterData.nodes.BaseDocumentLine,
})
@xtremStockData.functions.staticImplements<xtremStockData.interfaces.StaticDocumentLineWithStockPosting>()
export class FakeTransferReceiptLine extends xtremMasterData.nodes.BaseDocumentLine {
    async getOrderCost() {
        return (await (await this.shipmentLine)?.orderCost) ?? 0;
    }

    // eslint-disable-next-line class-methods-use-this
    getValuedCost() {
        return 0;
    }

    // eslint-disable-next-line class-methods-use-this
    getValuationParameters(): xtremStockData.interfaces.ValuationParameter {
        return { valuationType: 'receipt' };
    }

    getItem() {
        return this.item;
    }

    @decorators.referenceProperty<FakeTransferReceiptLine, 'shipmentLine'>({
        isPublished: true,
        isNullable: true,
        node: () => FakeTransferShipmentLine,
        isStored: true,
    })
    readonly shipmentLine: Reference<FakeTransferShipmentLine | null>;

    @decorators.decimalProperty<FakeTransferReceiptLine, 'orderCost'>({
        isStored: true,
        dataType: () => xtremMasterData.dataTypes.baseDecimal,
    })
    readonly orderCost: Promise<decimal>;

    @decorators.decimalProperty<FakeTransferReceiptLine, 'lineNumber'>({
        isStored: true,
        dataType: () => xtremMasterData.dataTypes.baseDecimal,
    })
    readonly lineNumber: Promise<decimal>;

    @decorators.referenceProperty<FakeTransferReceiptLine, 'document'>({
        isPublished: true,
        isStored: true,
        isVitalParent: true,
        node: () => FakeTransferReceipt,
    })
    override readonly document: Reference<FakeTransferReceipt>;

    @decorators.stringPropertyOverride<FakeTransferReceiptLine, 'documentNumber'>({
        async getValue() {
            return (await this.document).number;
        },
    })
    override readonly documentNumber: Promise<string>;

    @decorators.integerPropertyOverride<FakeTransferReceiptLine, 'documentId'>({
        async getValue() {
            return (await this.document)._id;
        },
    })
    override readonly documentId: Promise<integer>;

    @decorators.referenceProperty<FakeTransferReceiptLine, 'item'>({
        isStored: true,
        node: () => xtremMasterData.nodes.Item,
        defaultValue() {
            return this.$.context.read(xtremMasterData.nodes.Item, { _id: 1 });
        },
    })
    readonly item: Reference<xtremMasterData.nodes.Item>;

    @decorators.decimalProperty<FakeTransferReceiptLine, 'quantityInStockUnit'>({
        isStored: true,
        dataType: () => xtremMasterData.dataTypes.baseDecimal,
    })
    readonly quantityInStockUnit: Promise<decimal>;

    @decorators.enumProperty<FakeTransferReceiptLine, 'stockTransactionStatus'>({
        isStored: true,
        defaultValue() {
            return 'draft';
        },
        dataType: () => xtremStockData.enums.StockDocumentTransactionStatusDataType,
    })
    readonly stockTransactionStatus: Promise<xtremStockData.enums.StockDocumentTransactionStatus>;

    @decorators.collectionProperty<FakeTransferReceiptLine, 'stockDetails'>({
        isVital: true,
        reverseReference: 'documentLine',
        node: () => xtremStockData.nodes.StockChangeDetail,
    })
    readonly stockDetails: Collection<xtremStockData.nodes.StockChangeDetail>;

    @decorators.collectionProperty<FakeTransferReceiptLine, 'stockTransactions'>({
        isPublished: true,
        reverseReference: 'documentLine',
        isVital: true,
        node: () => xtremStockData.nodes.StockTransaction,
    })
    readonly stockTransactions: Collection<xtremStockData.nodes.StockTransaction>;
}
