import type { Collection, decimal, integer, Reference } from '@sage/xtrem-core';
import { decorators } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremStockData from '@sage/xtrem-stock-data';
import { FakeStockIssue } from './fake-stock-issue';

@decorators.subNode<FakeStockIssueLine>({
    canCreate: true,
    canDelete: true,
    canRead: true,
    canSearch: true,
    canUpdate: true,
    isPublished: true,
    isVitalCollectionChild: true,
    extends: () => xtremMasterData.nodes.BaseDocumentLine,
})
@xtremStockData.functions.staticImplements<xtremStockData.interfaces.StaticDocumentLineWithStockPosting>()
export class FakeStockIssueLine
    extends xtremMasterData.nodes.BaseDocumentLine
    implements xtremStockData.interfaces.DocumentLineWithStockAllocation
{
    // eslint-disable-next-line class-methods-use-this
    getOrderCost() {
        return 0;
    }

    // eslint-disable-next-line class-methods-use-this
    getValuedCost() {
        return 0;
    }

    getValuationParameters(): xtremStockData.interfaces.ValuationParameter {
        return { valuationType: 'issue', canBeReturned: this.canBeReturned };
    }

    getItem() {
        return this.item;
    }

    @decorators.booleanProperty<FakeStockIssueLine, 'canBeReturned'>({
        isStored: true,
        defaultValue: false,
    })
    canBeReturned: boolean;

    @decorators.decimalProperty<FakeStockIssueLine, 'lineNumber'>({
        isStored: true,
        dataType: () => xtremMasterData.dataTypes.baseDecimal,
    })
    readonly lineNumber: Promise<decimal>;

    @decorators.referenceProperty<FakeStockIssueLine, 'document'>({
        isStored: true,
        isVitalParent: true,
        node: () => FakeStockIssue,
    })
    override readonly document: Reference<FakeStockIssue>;

    @decorators.stringPropertyOverride<FakeStockIssueLine, 'documentNumber'>({
        async getValue() {
            return (await this.document).number;
        },
    })
    override readonly documentNumber: Promise<string>;

    @decorators.integerPropertyOverride<FakeStockIssueLine, 'documentId'>({
        async getValue() {
            return (await this.document)._id;
        },
    })
    override readonly documentId: Promise<integer>;

    @decorators.referenceProperty<FakeStockIssueLine, 'item'>({
        isStored: true,
        node: () => xtremMasterData.nodes.Item,
        defaultValue() {
            return this.$.context.read(xtremMasterData.nodes.Item, { _id: 1 });
        },
    })
    readonly item: Reference<xtremMasterData.nodes.Item>;

    @decorators.decimalProperty<FakeStockIssueLine, 'quantityInStockUnit'>({
        isStored: true,
        dataType: () => xtremMasterData.dataTypes.baseDecimal,
    })
    readonly quantityInStockUnit: Promise<decimal>;

    @decorators.enumProperty<FakeStockIssueLine, 'stockTransactionStatus'>({
        isStored: true,
        defaultValue() {
            return 'draft';
        },
        dataType: () => xtremStockData.enums.StockDocumentTransactionStatusDataType,
    })
    readonly stockTransactionStatus: Promise<xtremStockData.enums.StockDocumentTransactionStatus>;

    @decorators.collectionProperty<FakeStockIssueLine, 'stockDetails'>({
        isVital: true,
        reverseReference: 'documentLine',
        node: () => xtremStockData.nodes.StockIssueDetail,
    })
    readonly stockDetails: Collection<xtremStockData.nodes.StockIssueDetail>;

    @decorators.collectionProperty<FakeStockIssueLine, 'stockTransactions'>({
        isPublished: true,
        reverseReference: 'documentLine',
        isVital: true,
        node: () => xtremStockData.nodes.StockTransaction,
    })
    readonly stockTransactions: Collection<xtremStockData.nodes.StockTransaction>;

    @decorators.decimalProperty<FakeStockIssueLine, 'quantityAllocated'>({
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.quantityInStockUnit,
        computeValue() {
            return xtremStockData.functions.allocationLib.getDocumentLineAllocatedQuantity(this);
        },
    })
    readonly quantityAllocated: Promise<decimal>;

    @decorators.decimalProperty<FakeStockIssueLine, 'remainingQuantityToAllocate'>({
        isPublished: true,
        dependsOn: ['quantityInStockUnit', 'quantityAllocated'],
        dataType: () => xtremMasterData.dataTypes.quantityInStockUnit,
        async getValue() {
            return (await this.quantityInStockUnit) - (await this.quantityAllocated);
        },
    })
    readonly remainingQuantityToAllocate: Promise<decimal>;

    @decorators.enumProperty<FakeStockIssueLine, 'allocationStatus'>({
        isPublished: true,
        dataType: () => xtremStockData.enums.stockAllocationStatusDataType,
        dependsOn: ['remainingQuantityToAllocate', 'quantityAllocated', 'item'],
        async getValue() {
            const item = await this.item;
            return xtremStockData.functions.allocationLib.getLineAllocationStatus(
                await this.remainingQuantityToAllocate,
                await this.quantityAllocated,
                await item.isStockManaged,
            );
        },
    })
    readonly allocationStatus: Promise<xtremStockData.enums.StockAllocationStatus>;

    @decorators.collectionProperty<FakeStockIssueLine, 'stockAllocations'>({
        isPublished: true,
        reverseReference: 'documentLine',
        node: () => xtremStockData.nodes.StockAllocation,
    })
    readonly stockAllocations: Collection<xtremStockData.nodes.StockAllocation>;
}
