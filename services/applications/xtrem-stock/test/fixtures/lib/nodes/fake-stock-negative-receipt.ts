import type { NotificationEnvelope } from '@sage/xtrem-communication';
import type { Collection, Context, integer } from '@sage/xtrem-core';
import { Logger, Node, date, decorators } from '@sage/xtrem-core';
import * as xtremStockData from '@sage/xtrem-stock-data';
import * as xtremSystem from '@sage/xtrem-system';
import { FakeStockNegativeReceiptLine } from './fake-stock-negative-receipt-line';

const logger = Logger.getLogger(__filename, 'StockIssue');

@decorators.node<FakeStockNegativeReceipt>({
    storage: 'sql',
    canCreate: true,
    canDelete: true,
    canRead: true,
    canSearch: true,
    canUpdate: true,
    isPublished: true,
})
@xtremStockData.functions.staticImplements<xtremStockData.interfaces.StaticDocumentHeaderWithStockPosting>()
export class FakeStockNegativeReceipt extends Node {
    getEffectiveDate() {
        return this.effectiveDate;
    }

    getStockSite() {
        return this.stockSite;
    }

    @decorators.stringProperty<FakeStockNegativeReceipt, 'number'>({
        isStored: true,
        dataType: () => xtremSystem.dataTypes.code,
    })
    readonly number: Promise<string>;

    @decorators.referenceProperty<FakeStockNegativeReceipt, 'stockSite'>({
        isStored: true,
        node: () => xtremSystem.nodes.Site,
        defaultValue() {
            return this.$.context.read(xtremSystem.nodes.Site, { id: 'US001' });
        },
    })
    readonly stockSite: Promise<xtremSystem.nodes.Site>;

    @decorators.booleanProperty<FakeStockNegativeReceipt, 'forceUpdateForStock'>({
        defaultValue: false,
    })
    readonly forceUpdateForStock: Promise<boolean>;

    @decorators.dateProperty<FakeStockNegativeReceipt, 'effectiveDate'>({
        isStored: true,
        defaultValue() {
            return date.today();
        },
    })
    readonly effectiveDate: Promise<date>;

    @decorators.enumProperty<FakeStockNegativeReceipt, 'stockTransactionStatus'>({
        isPublished: true,
        dataType: () => xtremStockData.enums.StockDocumentTransactionStatusDataType,
        computeValue() {
            return xtremStockData.functions.stockTransactionLib.computeHeaderStatus(this);
        },
    })
    readonly stockTransactionStatus: Promise<xtremStockData.enums.StockDocumentTransactionStatus>;

    @decorators.collectionProperty<FakeStockNegativeReceipt, 'lines'>({
        isVital: true,
        reverseReference: 'document',
        node: () => FakeStockNegativeReceiptLine,
    })
    readonly lines: Collection<FakeStockNegativeReceiptLine>;

    readonly linesWithStockTransactionStatusCollectionName = 'lines';

    @decorators.mutation<typeof FakeStockNegativeReceipt, 'postToStock'>({
        isPublished: true,
        parameters: [
            {
                name: 'documentIds',
                type: 'array',
                item: {
                    type: 'integer',
                },
            },
        ],
        return: {
            type: 'string',
        },
    })
    static postToStock(context: Context, documentIds: integer[]): Promise<string> {
        return xtremStockData.functions.notificationLib.stockIssueRequestNotification(context, {
            documentClass: FakeStockNegativeReceipt,
            documentIds,
        });
    }

    @decorators.notificationListener<typeof FakeStockNegativeReceipt>({
        startsReadOnly: true,
        topic: 'FakeStockNegativeReceipt/stock/issue/reply',
        onError(
            context: Context,
            envelope: NotificationEnvelope<
                xtremStockData.interfaces.StockReplyPayload<xtremStockData.enums.StockMovementTypeEnum.issue>
            >,
            error: Error,
        ) {
            return xtremStockData.functions.notificationLib.errorHandler(
                context,
                envelope,
                error,
                FakeStockNegativeReceipt,
            );
        },
    })
    static async onStockReply(
        readOnlyContext: Context,
        payload: xtremStockData.interfaces.StockReplyPayload<xtremStockData.enums.StockMovementTypeEnum.issue>,
    ): Promise<void> {
        logger.info(`reply received: \n ${JSON.stringify(payload)}`);
        await readOnlyContext.runInWritableContext(writableContext =>
            xtremStockData.functions.notificationLib.reactToStockMovementReply(
                writableContext,
                payload,
                FakeStockNegativeReceipt,
            ),
        );
    }
}
