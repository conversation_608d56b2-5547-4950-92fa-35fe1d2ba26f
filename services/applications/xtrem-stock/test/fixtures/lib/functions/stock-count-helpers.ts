import type { Context, decimal, NodeCreateData } from '@sage/xtrem-core';
import { assert } from 'chai';
import * as xtremStock from '../../../../lib';

export type StockCountFilterResult = { itemId: string; quantity: decimal };

export function sortResult(results: Array<StockCountFilterResult>) {
    return results.sort((a, b) => {
        const compareId = a.itemId.localeCompare(b.itemId);
        if (compareId === 0) return a.quantity - b.quantity;
        return compareId;
    });
}
export async function testStockCountFilter(
    context: Context,
    args: {
        stockCountCreateData: NodeCreateData<xtremStock.nodes.StockCount>;
        confirmOptions: {
            allSelected: boolean;
            stockRecords: number[] | ((stockCount: xtremStock.nodes.StockCount) => Promise<number[]>);
            itemSites: number[] | ((stockCount: xtremStock.nodes.StockCount) => Promise<number[]>);
        };
        expectedResults: {
            beforeConfirmation: StockCountFilterResult[];
            afterConfirmation: StockCountFilterResult[];
        };
    },
) {
    const stockCount = await context.create(xtremStock.nodes.StockCount, args.stockCountCreateData);
    await stockCount.$.save();
    assert.deepEqual(stockCount.$.context.diagnoses, []);
    assert.equal(await stockCount.number, args.stockCountCreateData.number);

    const itemSiteStocks: StockCountFilterResult[] = [];
    await stockCount.itemSites.forEach(async itemSite => {
        if ((await itemSite.countStockRecords.length) === 0) {
            itemSiteStocks.push({
                itemId: await (await itemSite.item).id,
                quantity: 0.0,
            });
            return;
        }
        await itemSite.countStockRecords.forEach(async stockRecord => {
            itemSiteStocks.push({
                itemId: await (await itemSite.item).id,
                quantity: await stockRecord.quantityInStockUnit,
            });
        });
    });

    assert.deepEqual(sortResult(itemSiteStocks), args.expectedResults.beforeConfirmation);
    assert.equal(await stockCount.status, 'draft');

    const confirmOptions: Parameters<typeof xtremStock.nodes.StockCount.confirmStockCount>[2] = {
        ...args.confirmOptions,
        stockRecords:
            typeof args.confirmOptions.stockRecords === 'function'
                ? await args.confirmOptions.stockRecords(stockCount)
                : args.confirmOptions.stockRecords,
        itemSites:
            typeof args.confirmOptions.itemSites === 'function'
                ? await args.confirmOptions.itemSites(stockCount)
                : args.confirmOptions.itemSites,
    };
    const started = await xtremStock.nodes.StockCount.confirmStockCount(context, stockCount, confirmOptions);
    assert.isTrue(started);
    const stockCountUpdate = await context.read(
        xtremStock.nodes.StockCount,
        { number: args.stockCountCreateData.number },
        { forUpdate: true },
    );
    assert.equal(await stockCountUpdate.status, 'toBeCounted');
    assert.deepEqual(
        sortResult(
            await stockCount.lines
                .map(async itemSite => ({
                    itemId: await (await itemSite.item).id,
                    quantity: await itemSite.quantityInStockUnit,
                }))
                .toArray(),
        ),
        args.expectedResults.afterConfirmation,
    );
    return stockCountUpdate;
}
