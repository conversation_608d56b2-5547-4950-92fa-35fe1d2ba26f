import type { AsyncArrayReader, Context, Node, NodeCreateData, decimal, integer } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremStockData from '@sage/xtrem-stock-data';
import { assert } from 'chai';
import * as fakePackage from '..';
import * as xtremStock from '../../../../lib';

export function stockSearchDataToDetailCreateData<Detail extends xtremStockData.nodes.StockMovementDetail>(
    searchData: xtremStockData.interfaces.StockSearchData,
    additionalOrOverrideProperties: NodeCreateData<Detail>,
) {
    return {
        _action: 'create',
        item: searchData.item,
        location: searchData.location,
        existingLot: searchData.lot,
        site: searchData.site,
        status: searchData.status,
        stockUnit: searchData.stockUnit,
        owner: searchData.owner,
        ...additionalOrOverrideProperties,
    } as NodeCreateData<
        Detail & {
            _sortValue?: number | undefined;
            _action: 'create' | 'update' | 'delete';
        }
    >;
}

type RequestListenerFunctionType = (
    context: Context,
    payload: xtremStockData.interfaces.StockPostingPayload<xtremStockData.enums.StockMovementTypeEnum>,
) => Promise<void>;

type FilteredRequestListenerFunction<T> = {
    [Key in keyof T]: T[Key] extends RequestListenerFunctionType ? Key : never;
};

type AllowedRequestListenerFunction<T> = FilteredRequestListenerFunction<T>[keyof T];

export async function processStockUpdate<MovementType extends xtremStockData.enums.StockMovementTypeEnum>(
    context: Context,
    document: xtremStockData.interfaces.StockDocumentHeaderWithValuedLines & Node,
    requestListenerFunctionName: AllowedRequestListenerFunction<
        Omit<typeof xtremStock.nodeExtensions.StockExtension, 'change'>
    >,
    stockUpdateParameters: xtremStockData.interfaces.StockUpdateParameters<MovementType>,
    replyListenerFunction: (
        context: Context,
        payload: xtremStockData.interfaces.StockReplyPayload<MovementType>,
    ) => Promise<void>,
    lineData: Array<{
        stockUpdateResultStatus: xtremStockData.enums.StockUpdateResultAction;
        stockTransactionStatus: xtremStockData.enums.StockTransactionStatus;
        message?: string;
    }>,
    lineFilterCallback?: (
        line: xtremStockData.interfaces.AnyStockDocumentLineWithoutStockDetail & Node,
    ) => Promise<boolean>,
): Promise<void> {
    const idStr = document._id.toString().padStart(4, '0');
    const notificationId = `fake${idStr.slice(-4)}${Date.now()}`;

    // mimic the state of the context when been triggered by a notification for the lines
    (context as any)._contextValues.notificationId = notificationId;

    const lines = await document.lines
        .filter(line => !lineFilterCallback || lineFilterCallback(line))
        .map(async line => {
            return {
                id: line._id,
                sortValue: await line._sortValue,
                ...{},
            };
        })
        .toArray();

    await (xtremStock.nodeExtensions.StockExtension[requestListenerFunctionName] as RequestListenerFunctionType)(
        context,
        {
            replyTopic: 'fakeTopic',
            stockUpdateParameters,
            documents: [
                {
                    id: document._id,
                    lines,
                },
            ],
        },
    );

    // simulate the reply notification
    await replyListenerFunction(context, {
        requestNotificationId: notificationId,
        updateResults: {
            [stockUpdateParameters.movementType]: {
                documents: [
                    {
                        id: document._id,
                        lines: lines.map((line, i) => ({
                            ...line,
                            stockUpdateResultStatus: lineData[i].stockUpdateResultStatus,
                            errorMessage: lineData[i].message,
                            originLineIds: [],
                            // TODO: Add the stockJournalRecords :/
                            stockJournalRecords: [],
                        })),
                    } as xtremStockData.interfaces.StockUpdateResultCommonType['documents'][0],
                ],
            },
        } as xtremStockData.interfaces.StockUpdateResults<xtremStockData.enums.StockMovementTypeEnum>,
    });
}

export async function processStockChange(
    context: Context,
    document: xtremStockData.interfaces.StockDocumentHeaderWithoutStockDetail & Node,
    stockUpdateParameter: xtremStockData.interfaces.StockUpdateParameters<xtremStockData.enums.StockMovementTypeEnum.change>,
    replyListenerFunction: (
        context: Context,
        payload: xtremStockData.interfaces.StockReplyPayload<xtremStockData.enums.StockMovementTypeEnum.change>,
    ) => Promise<void>,
    lineData: Array<{
        stockUpdateResultStatus: xtremStockData.enums.StockUpdateResultAction;
        stockTransactionStatus: xtremStockData.enums.StockTransactionStatus;
        message?: string;
    }>,
): Promise<void> {
    await processStockUpdate(
        context,
        document as any,
        'change' as any,
        stockUpdateParameter,
        replyListenerFunction,
        lineData,
    );
}

// Create a fake issue document with several lines
export async function createFakeIssueDocument(
    context: Context,
    documentNumber: string,
    numberOfLines: number,
    item?: xtremMasterData.nodes.Item,
    quantityPerLine: decimal = 3,
): Promise<fakePackage.nodes.FakeStockIssue> {
    let itemToUse = item as xtremMasterData.nodes.Item;
    if (!itemToUse) {
        itemToUse = await context.read(xtremMasterData.nodes.Item, { id: 'Chemical D' });
    }
    const lines: Array<{
        quantity: number;
        item: xtremMasterData.nodes.Item;
    }> = Array.from({ length: numberOfLines }, () => ({ item: itemToUse, quantity: quantityPerLine }));

    const document = await context.create(fakePackage.nodes.FakeStockIssue, {
        number: documentNumber,
        lines: lines.map((line, index) => {
            return {
                lineNumber: index + 1,
                quantityInStockUnit: line.quantity,
                item: line.item,
            };
        }),
    });
    await document.$.save();
    return document;
}

// Create a single record on the AllocationQueue table, from a documentLine
export async function createAllocationQueueRecord(
    context: Context,
    processId: string,
    requestType: xtremStockData.enums.AllocationRequestType,
    documentLine: fakePackage.nodes.FakeStockIssueLine,
    quantityToProcess?: decimal,
): Promise<xtremStockData.nodes.AllocationQueue> {
    const allocationQueueRecord = await context.create(xtremStockData.nodes.AllocationQueue, {
        processId,
        stockSite: await (await documentLine.document).getStockSite(),
        documentLine,
        requestType,
        documentType: 'salesOrder',
        item: await documentLine.item,
        quantityToProcess:
            quantityToProcess ||
            (requestType === 'allocation'
                ? await documentLine.remainingQuantityToAllocate
                : await documentLine.quantityAllocated),
    });
    await allocationQueueRecord.$.save();
    return allocationQueueRecord;
}

// Create a single record on the AllocationQueue table, from a documentLine
export async function createAllocationResultRecordFromQueue(
    context: Context,
    processType: xtremStockData.enums.AllocationProcessType,
    queue: AsyncArrayReader<xtremStockData.nodes.AllocationQueue>,
): Promise<xtremStockData.nodes.AllocationResult> {
    const firstQueueLine = await queue.elementAt(0);
    const requestType = await firstQueueLine.requestType;
    const allocationResultRecord = await context.create(xtremStockData.nodes.AllocationResult, {
        processId: await firstQueueLine.processId,
        processType,
        requestType,
        documentType: await firstQueueLine.documentType,
        lines: await queue
            .map(
                async queueLine =>
                    ({
                        status: 'notStarted',
                        documentLine: await queueLine.documentLine,
                        stockSite: await queueLine.stockSite,
                        requestType,
                        documentType: await queueLine.documentType,
                        item: await queueLine.item,
                        quantityToProcess: await queueLine.quantityToProcess,
                    }) as NodeCreateData<xtremStockData.nodes.AllocationResultLine>,
            )
            .toArray(),
    });

    await allocationResultRecord.$.save();
    return allocationResultRecord;
}

export async function createStockValueChangeDocument(
    context: Context,
    data: {
        itemSite: xtremMasterData.nodes.ItemSite;
        lineAmounts: Array<{
            oldAmount: decimal;
            newAmount: decimal;
            quantity: decimal;
            fifoTier?: xtremStockData.nodes.FifoValuationTier;
        }>;
    },
): Promise<xtremStock.nodes.StockValueChange>;
export async function createStockValueChangeDocument(
    context: Context,
    data: {
        item: string;
        site: string;
        lineAmounts: Array<{
            oldAmount: decimal;
            newAmount: decimal;
            quantity: decimal;
            fifoTier?: xtremStockData.nodes.FifoValuationTier;
        }>;
    },
): Promise<xtremStock.nodes.StockValueChange>;
export async function createStockValueChangeDocument(
    context: Context,
    data: {
        item?: string;
        site?: string;
        itemSite?: xtremMasterData.nodes.ItemSite;
        lineAmounts: Array<{
            oldAmount: decimal;
            newAmount: decimal;
            quantity: decimal;
            fifoTier?: xtremStockData.nodes.FifoValuationTier;
        }>;
    },
): Promise<xtremStock.nodes.StockValueChange> {
    const itemSite =
        data.itemSite ?? (await context.read(xtremMasterData.nodes.ItemSite, { item: data.item, site: data.site }));
    const item = await itemSite.item;
    const site = await itemSite.site;
    const valuationMethod = await itemSite.valuationMethod;
    const lines: Array<NodeCreateData<xtremStock.nodes.StockValueChangeLine>> = data.lineAmounts.map(lineAmount => {
        return {
            unitCost: lineAmount.oldAmount / lineAmount.quantity,
            amount: lineAmount.oldAmount,
            quantity: lineAmount.quantity,
            newUnitCost: lineAmount.newAmount / lineAmount.quantity,
            newAmount: lineAmount.newAmount,
            stockDetails: [
                {
                    _action: 'create',
                    item,
                    site,
                    valuationMethod,
                    amount: lineAmount.newAmount - lineAmount.oldAmount,
                    quantityInStockUnit: lineAmount.quantity,
                    fifoValuationTier: lineAmount.fifoTier,
                },
            ],
        } as NodeCreateData<xtremStock.nodes.StockValueChangeLine>;
    });
    const document = await context.create(xtremStock.nodes.StockValueChange, {
        number: 'TEST',
        item,
        site,
        description: 'SVCTEST',
        valuationMethod,
        lines,
    });
    await document.$.save();
    return document;
}

// simulate the correction of document line with 2 stock details
// pass the variance as parameter and the 2 stock expected results
export async function simulateStockReceiptCorrection(
    context: Context,
    args: {
        variance: decimal;
        correctedDocumentNumber: string;
        itemLineToCorrect: string;
        correctorDocumentNumber: string;
    },
) {
    const document = await context.read(xtremStock.nodes.StockReceipt, { number: args.correctedDocumentNumber });
    const documentLine = await document.lines.find(
        async line => (await (await line.item).id) === args.itemLineToCorrect,
    );

    if (!documentLine) {
        throw new Error(`Document line with itemLine ${args.itemLineToCorrect} not found`);
    }
    const fakeCorrectorDocument = await context.read(xtremStock.nodes.StockReceipt, {
        number: args.correctorDocumentNumber,
    });
    const fakeCorrectorDocumentLine = await fakeCorrectorDocument.lines.elementAt(0);

    // Create some StockCorrectionDetail records
    const stockCorrectionDetails =
        await xtremStockData.functions.stockDetailLib.createStockCorrectionDetailFromStockDetail(
            context,
            documentLine.stockDetails,
            {
                amountToAbsorb: args.variance,
                absorbedAmount: args.variance,
                nonAbsorbedAmount: 0,
                correctedDocumentLineID: documentLine._id,
                correctorDocumentLine: fakeCorrectorDocumentLine,
                impactedQuantity: await documentLine.quantityInStockUnit,
                reasonCodeID: 'R1',
            },
        );

    const valuationManager = await xtremStock.classes.StockValuationManager.getInstance(context, {
        action: 'correctValue',
        stockDetails: stockCorrectionDetails,
    });

    return { valuationManager, documentLine, stockCorrectionDetails };
}

export function checkStockValuationManagerValues(
    entryParams: {
        valuationManager: xtremStock.classes.StockValuationManager;
        documentLine: xtremMasterData.nodes.BaseDocumentLine;
        quantity?: decimal;
        stockDetailId?: integer;
    },
    expectedValues: { orderedAmount: decimal; valuedAmount: decimal; nonAbsorbedAmount: decimal },
) {
    const actualOrderAmount = entryParams.valuationManager.getOrderAmount(
        entryParams.documentLine,
        entryParams.quantity,
        entryParams.stockDetailId,
    );
    const actualValuedAmount = entryParams.valuationManager.getValuedAmount(
        entryParams.documentLine,
        entryParams.quantity,
        entryParams.stockDetailId,
    );
    const actualNonAbsorbedAmount = entryParams.valuationManager.getNonAbsorbedAmount(
        entryParams.documentLine,
        entryParams.quantity,
        entryParams.stockDetailId,
    );
    assert.deepEqual(
        {
            orderedAmount: actualOrderAmount,
            valuedAmount: actualValuedAmount,
            nonAbsorbedAmount: actualNonAbsorbedAmount,
        },
        expectedValues,
    );
}
