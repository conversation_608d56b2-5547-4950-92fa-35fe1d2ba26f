{"Create a stock change with incorrect lines ": {"input": {"properties": {"stockSite": "#US001", "item": "#Mu<PERSON>li", "stockStatus": "#A", "location": 1, "lot": 28}, "arrayProperties": {"lines": [{"quantityInStockUnit": "200", "location": "1", "stockStatus": "#A"}, {"quantityInStockUnit": "200", "location": "1", "stockStatus": "#Q"}, {"quantityInStockUnit": "-200", "location": "1", "stockStatus": "#Q"}, {"quantityInStockUnit": "0", "location": "1", "stockStatus": "#Q"}, {"quantityInStockUnit": "1", "location": "4", "stockStatus": "#Q"}]}}, "output": {"errors": [{"extensions": {"code": "operation-error", "diagnoses": [{"message": "No change to process on one line. Use a different stock status or location for this line.", "path": ["lines", "-1000000002"], "severity": 3}, {"message": "value must not be negative", "path": ["lines", "-1000000004", "quantityInStockUnit"], "severity": 3}, {"message": "property cannot be equal to 0", "path": ["lines", "-1000000005", "quantityInStockUnit"], "severity": 3}, {"message": "The location site and the stock site must be the same.", "path": ["lines", "-1000000006", "location"], "severity": 3}]}, "locations": [{"column": 13, "line": 4}], "message": "The record was not created.", "path": ["xtremStock", "stockChange", "create"]}]}}}