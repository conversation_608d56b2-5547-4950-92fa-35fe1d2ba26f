mutation {
    xtremStock {
        stockChange {
            create(
                data: 
                   { {{#each properties}}
                        {{#unless this.[0]}}
                            {{@key}} : {{this}}
                        {{else}}
                            {{@key}} : "{{this}}"
                        {{/unless}}
                    {{/each}}
                     {{#each arrayProperties}}
                        {{@key}} :
                        [
                            {{#each this}}
                            {
                                {{#each this}}
                                {{#unless this.[0]}}
                                    {{@key}} : {{this}}
                                {{else}}
                                    {{@key}} : "{{this}}"
                                {{/unless}}
                                {{/each}}
                            }
                        {{/each}}
                        ]
                    {{/each}}  
                }) {
                _id               
            }
        }
    }
}
