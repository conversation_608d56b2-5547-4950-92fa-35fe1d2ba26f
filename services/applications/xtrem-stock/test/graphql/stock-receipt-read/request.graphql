{
    xtremStock {
        stockReceipt {
            query(filter: "{_id : 5}") {
                edges {
                    node {
                        number
                        description
                        effectiveDate
                        documentDate
                        stockTransactionStatus
                        status
                        transactionCurrency {
                            id
                        }
                        financialSite {
                            id
                        }
                        documentDate
                        stockSite {
                            id
                        }
                        displayStatus
                        lines {
                            query {
                                edges {
                                    node {
                                        quantityInStockUnit
                                        storedDimensions
                                        storedAttributes
                                        computedAttributes
                                        jsonStockDetails
                                        stockTransactionStatus
                                        orderCost
                                        valuedCost
                                        site {
                                            id
                                        }
                                        displayStatus
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}
