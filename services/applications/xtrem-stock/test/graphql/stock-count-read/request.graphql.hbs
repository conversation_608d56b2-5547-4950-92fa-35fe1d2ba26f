query stockCount($filter: String, $orderBy: String) {
  xtremStock {
    stockCount {
      query(filter: $filter, orderBy: $orderBy) {
        edges {
          node {
            number
            lines {
              query {
                edges {
                  node {
                    item {
                      id
                    }
                    stockDetail {
                      stockDetailLot {
                        lot {
                          id
                        }
                        lotNumber
                        sublot
                        supplierLot
                      }
                    }
                    countedQuantityInStockUnit
                    quantityInStockUnit
                  }
                }
              }
            }
            hasLotInLines
            hasSublotInLines
            hasExpiryManagementInLines
            postingDetails {
              query {
                edges {
                  node {
                    documentNumber
                    documentType
                    targetDocumentType
                    targetDocumentNumber
                    message
                  }
                }
              }
            }
          }
        }
      }
    }
  }
}
