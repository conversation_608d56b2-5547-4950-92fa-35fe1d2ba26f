{
    xtremStockData {
        stockAllocation {
            query(first: 3) {
                edges {
                    node {
                        _id
                        quantityInStockUnit
                        stockRecord {
                            _id
                            item {
                                name
                            }
                        }
                        serialNumbers {
                            query {
                                edges {
                                    node {
                                        id
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}
