{"data": {"xtremStock": {"stockValueChange": {"create": {"number": "TEST_GRAPHQL", "postedDate": "2023-06-01", "description": "Stock value change test desc", "site": {"id": "US001"}, "item": {"id": "<PERSON><PERSON><PERSON>"}, "lines": {"query": {"edges": [{"node": {"newUnitCost": "33", "newAmount": "21", "amount": "150", "unitCost": "23", "quantity": "2", "stockTransactionStatus": "draft", "stockDetails": {"query": {"edges": [{"node": {"documentLine": {"documentNumber": "TEST_GRAPHQL"}, "site": {"id": "US001"}, "amount": "-129"}}]}}, "storedAttributes": "{\"project\":\"AttPROJ\",\"task\":\"Task1\"}", "storedDimensions": "{\"dimensionType02\":\"CHANNELVALUE1\"}"}}]}}}}}}}