mutation {
    xtremStock {
        stockReceipt {
            create(
                data: {
                    number: null
                    stockSite: "#US001"
                    effectiveDate: "2023-03-02"
                    description: null
                    lines: [
                        {
                            _action: "create"
                            _id: "-1"
                            _sortValue: "10"
                            existingLot: null
                            item: "#ITEM_NEWLOT"
                            location: "_id:1"
                            lotCreateData: null
                            orderCost: 8
                            quantityInStockUnit: 10
                            stockStatus: "#A"
                            stockTransactionStatus: "draft"
                            storedAttributes: null
                            storedDimensions: null
                            valuedCost: 0
                            stockDetails: [
                                {
                                    _action: "create"
                                    _id: "-1"
                                    site: "#US001"
                                    item: "#ITEM_NEWLOT"
                                    quantityInStockUnit: 10
                                    location: "#LOC1|US001|Loading dock"
                                    status: "#A"
                                    stockDetailLot: { _id: "-1", lot: "#ITEM_NEWLOT|LOTN0001|SLOT001" }
                                }
                            ]
                        }
                    ]
                }
            ) {
                number
                lines {
                    query {
                        edges {
                            node {
                                item {
                                    id
                                }
                                stockDetails {
                                    query {
                                        edges {
                                            node {
                                                quantityInStockUnit
                                                item {
                                                    id
                                                    name
                                                }
                                                location {
                                                    id
                                                }
                                                stockDetailLot {
                                                    lot {
                                                        id
                                                        sublot
                                                    }
                                                    lotNumber
                                                    sublot
                                                    expirationDate
                                                    supplierLot
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}
