{"increase allocation with serial numbers issue only": {"executionMode": "normal", "envConfigs": {"testActiveServiceOptions": ["serialNumberOption"]}, "input": {"properties": {"documentLine": "1912", "allocationUpdates": [{"action": "increase", "allocationRecord": 191201, "quantity": 3, "serialNumbers": ["#SN_ISSUE|ALLOC-GEN-01", "#SN_ISSUE|ALLOC-GEN-02", "#SN_ISSUE|ALLOC-GEN-03"]}]}}, "output": {"updateAllocations": [{"resultAction": "increased"}]}}}