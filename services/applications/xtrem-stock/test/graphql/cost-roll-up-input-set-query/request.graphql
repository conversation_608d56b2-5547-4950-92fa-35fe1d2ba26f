{
    xtremStock {
        costRollUpInputSet {
            query(filter: "{user: {email: '<EMAIL>'}}") {
                edges {
                    node {
                        user {
                            email
                        }
                        itemCategory {
                            id
                        }
                        commodityCode
                        costCategory {
                            id
                        }
                        items {
                            id
                        }
                        fromDate
                        quantity
                        includesRouting
                        usesComponentStandardCost
                        status
                        resultLines {
                            query {
                                edges {
                                    node {
                                        item {
                                            id
                                        }
                                        creationStatus
                                        currentMaterialCost
                                        materialCost
                                        currentMachineCost
                                        machineCost
                                        currentLaborCost
                                        laborCost
                                        currentToolCost
                                        toolCost
                                        currentTotalCost
                                        totalCost
                                        storedDimensions
                                        storedAttributes
                                        subAssemblyLines {
                                            query {
                                                edges {
                                                    node {
                                                        componentNumber
                                                        calculationQuantity
                                                        currentMaterialCost
                                                        materialCost
                                                        currentMachineCost
                                                        machineCost
                                                        currentLaborCost
                                                        laborCost
                                                        currentToolCost
                                                        toolCost
                                                        currentTotalCost
                                                        totalCost
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}
