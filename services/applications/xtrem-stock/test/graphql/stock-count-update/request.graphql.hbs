mutation {
  xtremStock {
    stockCount {
      update( {{inputData}} ) {
        number
        lines {
          query{
            edges{
              node{
                stockStatus {
                  id
                }
                location {
                  id
                }
                stockDetail {
                  stockDetailLot{
                    lotNumber
                  }
                }
                isNonStockItem

              }
            }
          }
        }
      }
    }
  }
}
