{"Set values of line created without stock": {"executionMode": "normal", "comment1": "The item has no stock => isNonStockItem is true. By adding stock information", "comment2": "isNonStockItem should be set to false and the stockDetail is created.", "input": {"properties": {"data": {"_id": "#STOCK_COUNT11", "lines": [{"_action": "update", "_sortValue": "10", "location": "#LOC1|US001|Loading dock", "stockTransactionStatus": "draft", "stockStatus": "#R"}]}}}, "output": {"data": {"xtremStock": {"stockCount": {"update": {"number": "STOCK_COUNT11", "lines": {"query": {"edges": [{"node": {"isNonStockItem": false, "location": {"id": "LOC1"}, "stockDetail": {"stockDetailLot": null}, "stockStatus": {"id": "R"}}}]}}}}}}}}}