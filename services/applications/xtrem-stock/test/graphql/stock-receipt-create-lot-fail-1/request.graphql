mutation {
    xtremStock {
        stockReceipt {
            create(
                data: {
                    number: null
                    stockSite: "_id:3"
                    effectiveDate: "2023-03-02"
                    description: null
                    lines: [
                        {
                            _action: "create"
                            _id: "-1"
                            _sortValue: "10"
                            existingLot: null
                            item: "#ITEM_NEWLOT"
                            location: "_id:1"
                            lotCreateData: null
                            orderCost: 8
                            quantityInStockUnit: 10
                            stockStatus: "#A"
                            stockTransactionStatus: "draft"
                            storedAttributes: null
                            storedDimensions: null
                            valuedCost: 0
                            stockDetails: [
                                {
                                    _action: "create"
                                    _id: "-1"
                                    site: "_id:3"
                                    item: "#ITEM_NEWLOT"
                                    quantityInStockUnit: 3
                                    location: "_id:1"
                                    status: "#A"
                                    stockDetailLot: {
                                        _id: "-1"
                                        lot: null
                                        stockDetailLot: null
                                        lotNumber: "LOTN0001"
                                        sublot: "SLOT001"
                                        supplierLot: "NEWLOT"
                                        expirationDate: "2025-07-31"
                                    }
                                }
                                {
                                    _action: "create"
                                    _id: "-2"
                                    site: "_id:3"
                                    item: "#ITEM_NEWLOT"
                                    quantityInStockUnit: 2
                                    location: "_id:1"
                                    status: "#A"
                                    stockDetailLot: {
                                        _id: "-2"
                                        lot: null
                                        stockDetailLot: "_id:-1"
                                        lotNumber: "LOTN0001"
                                        sublot: "SLOT001"
                                        supplierLot: "NEWLOT"
                                        expirationDate: "2025-07-31"
                                    }
                                }
                            ]
                        }
                    ]
                }
            ) {
                number
                lines {
                    query {
                        edges {
                            node {
                                item {
                                    id
                                }
                                stockDetails {
                                    query {
                                        edges {
                                            node {
                                                quantityInStockUnit
                                                item {
                                                    id
                                                    name
                                                }
                                                location {
                                                    id
                                                }
                                                stockDetailLot {
                                                    lot {
                                                        id
                                                    }
                                                    lotNumber
                                                    sublot
                                                    expirationDate
                                                    supplierLot
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}
