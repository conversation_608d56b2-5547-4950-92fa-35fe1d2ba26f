{"creation of allocation with serial numbers issue only": {"executionMode": "normal", "envConfigs": {"testActiveServiceOptions": ["serialNumberOption"]}, "input": {"properties": {"documentLine": "1912", "allocationUpdates": [{"action": "create", "stockRecord": 78, "quantity": 3, "serialNumbers": ["#SN_ISSUE|ALLOC-GEN-01", "#SN_ISSUE|ALLOC-GEN-02", "#SN_ISSUE|ALLOC-GEN-03"]}]}}, "output": [{"resultAction": "allocated", "allocationRecord": {"stockRecord": {"_id": "78", "totalAllocated": "8"}, "quantityInStockUnit": "3", "quantityTransferred": "0", "serialNumbers": {"query": {"edges": [{"node": {"id": "ALLOC-GEN-01", "isUsable": true, "supplierSerialNumber": ""}}, {"node": {"id": "ALLOC-GEN-02", "isUsable": true, "supplierSerialNumber": ""}}, {"node": {"id": "ALLOC-GEN-03", "isUsable": true, "supplierSerialNumber": ""}}]}}}}]}}