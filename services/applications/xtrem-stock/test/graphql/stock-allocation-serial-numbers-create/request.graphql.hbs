mutation {
    xtremStockData {
        stock {
          updateAllocations(allocationData: {{inputParameters}}) {
            resultAction
            allocationRecord{
              stockRecord {
                _id
                totalAllocated
              }
              quantityInStockUnit
              quantityTransferred
              serialNumbers {
                query {
                  edges {
                    node {
                      id
                      supplierSerialNumber
                      isUsable
                    }
                  }
                }
              }
            }
          }
        }
    }
}
