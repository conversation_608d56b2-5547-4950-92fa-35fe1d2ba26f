mutation {
    xtremStock {
        costRollUpInputSet {
            create(data: { site: "#US001", quantity: 1, costCategory: "Standard" }) {
                user {
                    email
                }
                items {
                    id
                }
                itemCategory {
                    id
                }
                commodityCode
                costCategory {
                    id
                }
                fromDate
                quantity
                includesRouting
                usesComponentStandardCost
                status
                resultLines {
                    query {
                        edges {
                            node {
                                currentMaterialCost
                                materialCost
                                currentMachineCost
                                machineCost
                                currentLaborCost
                                laborCost
                                currentToolCost
                                toolCost
                                storedDimensions
                                storedAttributes
                            }
                        }
                    }
                }
            }
        }
    }
}
