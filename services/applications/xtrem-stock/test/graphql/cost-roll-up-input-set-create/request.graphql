mutation {
    xtremStock {
        costRollUpInputSet {
            create(
                data: {
                    user: "<EMAIL>"
                    site: "#US001"
                    items: ["#COST_A", "#COST_D"]
                    itemCategory: "#CHEMICAL"
                    commodityCode: "Test commodity code"
                    costCategory: "#Standard"
                    fromDate: "2024-01-01"
                    quantity: 159
                    includesRouting: true
                    usesComponentStandardCost: true
                    status: "draft"
                }
            ) {
                user {
                    email
                }
                items {
                    id
                }
                itemCategory {
                    id
                }
                commodityCode
                costCategory {
                    id
                }
                fromDate
                quantity
                includesRouting
                usesComponentStandardCost
                status
                resultLines {
                    query {
                        edges {
                            node {
                                currentMaterialCost
                                materialCost
                                currentMachineCost
                                machineCost
                                currentLaborCost
                                laborCost
                                currentToolCost
                                toolCost
                                storedDimensions
                                storedAttributes
                            }
                        }
                    }
                }
            }
        }
    }
}
