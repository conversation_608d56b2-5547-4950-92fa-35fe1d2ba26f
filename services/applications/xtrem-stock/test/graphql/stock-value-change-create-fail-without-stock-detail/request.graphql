mutation {
    xtremStock {
        stockValueChange {
            create(
                data: {
                    number: "TEST_GRAPHQL"
                    description: "Stock value change test desc"
                    site: "#US001"
                    item: "#Muesli"
                    postedDate: "2023-06-01"
                    lines: [
                        {
                            newUnitCost: 33
                            newAmount: 21
                            amount: 150
                            unitCost: 23
                            quantity: 2
                            stockTransactionStatus: "draft"
                        }
                    ]
                }
            ) {
                number
                postedDate
                description
                site {
                    id
                }
                item {
                    id
                }
                lines {
                    query {
                        edges {
                            node {
                                newUnitCost
                                newAmount
                                amount
                                unitCost
                                quantity
                                stockTransactionStatus
                                stockDetails {
                                    query {
                                        edges {
                                            node {
                                                documentLine {
                                                    documentNumber
                                                    documentId
                                                }
                                                site {
                                                    id
                                                }
                                                amount
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}
