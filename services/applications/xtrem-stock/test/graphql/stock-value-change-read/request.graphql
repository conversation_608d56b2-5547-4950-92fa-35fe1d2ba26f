{
    xtremStock {
        stockValueChange {
            query(filter: "{_id : 2}") {
                edges {
                    node {
                        number
                        description
                        postedDate
                        item {
                            id
                        }
                        site {
                            id
                        }
                        lines {
                            query {
                                edges {
                                    node {
                                        fifoCost {
                                            _id
                                        }
                                        newUnitCost
                                        newAmount
                                        amount
                                        unitCost
                                        storedAttributes
                                        storedDimensions
                                        stockTransactionStatus
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}
