{
    xtremStock {
        stockAdjustment {
            query(filter: "{_id : 1}") {
                edges {
                    node {
                        number
                        description
                        effectiveDate
                        documentDate
                        stockTransactionStatus
                        status
                        transactionCurrency {
                            id
                        }
                        financialSite {
                            id
                        }
                        documentDate
                        stockSite {
                            id
                        }
                        displayStatus
                        lines {
                            query {
                                edges {
                                    node {
                                        quantityInStockUnit
                                        storedDimensions
                                        storedAttributes
                                        computedAttributes
                                        stockTransactionStatus
                                        orderCost
                                        valuedCost
                                        site {
                                            id
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}
