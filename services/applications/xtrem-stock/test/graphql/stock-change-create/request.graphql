mutation {
    xtremStock {
        stockChange {
            create(
                data: {
                    effectiveDate: "2021-02-05"
                    description: "Transfer to location2 with status change"
                    stockSite: "#US001"
                    item: "#Muesli"
                    stockStatus: "#A"
                    location: 1
                    lot: 28
                    lines: [
                        { quantityInStockUnit: 100, location: 1, stockStatus: "#Q" }
                        { quantityInStockUnit: 200, location: 1, stockStatus: "#R" }
                    ]
                }
            ) {
                number
                effectiveDate
                description
                stockSite {
                    id
                }
                item {
                    id
                }
                stockUnit {
                    id
                }
                location {
                    _id
                    site {
                        id
                    }
                }
                stockStatus {
                    id
                }
                lot {
                    _id
                    item {
                        id
                    }
                }
                owner
                status
                stockTransactionStatus
                lines {
                    query {
                        edges {
                            node {
                                quantityInStockUnit
                                location {
                                    _id
                                    site {
                                        id
                                    }
                                }
                                stockStatus {
                                    id
                                }
                                orderCost
                                valuedCost
                            }
                        }
                    }
                }
            }
        }
    }
}
