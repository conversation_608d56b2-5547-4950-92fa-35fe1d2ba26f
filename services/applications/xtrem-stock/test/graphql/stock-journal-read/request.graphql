query {
    xtremStockData {
        stockJournal {
            read(_id: "110001") {
                item {
                    id
                }
                location {
                    id
                }
                lot {
                    id
                }
                sequence
                quantityInStockUnit
                activeQuantityInStockUnit
                site {
                    id
                    legalCompany {
                        id
                        description
                        currency {
                            id
                            symbol
                        }
                    }
                }
                status {
                    name
                }
                stockUnit {
                    id
                    decimalDigits
                }
                effectiveDate
                orderAmount
                movementAmount
                costVariance
                amountVariance
            }
        }
    }
}
