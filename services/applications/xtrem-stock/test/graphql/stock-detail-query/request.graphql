{
    xtremStockData {
        stockMovementDetail {
            query(filter: "{ item:'#Chair' , site:'#US001' }") {
                edges {
                    node {
                        _id
                        stockUnit {
                            id
                        }
                        quantityInStockUnit
                        effectiveDate
                        ... on StockReceiptDetail {
                            status {
                                id
                            }
                            owner
                            location {
                                name
                            }
                        }
                        ... on StockIssueDetail {
                            stockRecord {
                                _id
                            }
                        }
                        ... on StockChangeDetail {
                            stockRecord {
                                _id
                            }
                            status {
                                id
                            }
                            location {
                                name
                            }
                        }
                        ... on StockAdjustmentDetail {
                            stockRecord {
                                _id
                            }
                            status {
                                id
                            }
                            owner
                            location {
                                name
                            }
                            reasonCode {
                                id
                                quantityIncreaseAdjustment
                            }
                        }
                        ... on StockCorrectionDetail {
                            correctedStockDetail {
                                _id
                            }
                            reasonCode {
                                id
                                quantityIncreaseAdjustment
                            }
                        }
                    }
                }
            }
        }
    }
}
