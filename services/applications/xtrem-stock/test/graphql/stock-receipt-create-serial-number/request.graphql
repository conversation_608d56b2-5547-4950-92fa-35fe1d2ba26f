mutation {
    xtremStock {
        stockReceipt {
            create(
                data: {
                    number: null
                    stockSite: "_id:3"
                    effectiveDate: "2023-03-02"
                    description: null
                    lines: [
                        {
                            _action: "create"
                            _id: "-1"
                            _sortValue: "10"
                            existingLot: null
                            item: "#ISSUESERIAL"
                            location: "_id:3"
                            lotCreateData: null
                            orderCost: 8
                            quantityInStockUnit: 5
                            stockStatus: "#A"
                            stockTransactionStatus: "draft"
                            storedAttributes: null
                            storedDimensions: null
                            valuedCost: 0
                            stockDetails: [
                                {
                                    _action: "create"
                                    _id: "-1"
                                    site: "_id:3"
                                    item: "#ISSUESERIAL"
                                    quantityInStockUnit: 5
                                    location: "_id:3"
                                    status: "#A"
                                    stockDetailSerialNumbers: [
                                        { _action: "create", newSerialNumberId: "SN01" }
                                        { _action: "create", newSerialNumberId: "SN02" }
                                    ]
                                }
                            ]
                        }
                    ]
                }
            ) {
                number
                lines {
                    query {
                        edges {
                            node {
                                item {
                                    id
                                }
                                stockDetails {
                                    query {
                                        edges {
                                            node {
                                                quantityInStockUnit
                                                item {
                                                    id
                                                    name
                                                }
                                                location {
                                                    id
                                                }
                                                stockDetailLot {
                                                    lot {
                                                        id
                                                    }
                                                    lotNumber
                                                    sublot
                                                    expirationDate
                                                    supplierLot
                                                }
                                                stockDetailSerialNumbers {
                                                    query {
                                                        edges {
                                                            node {
                                                                serialNumber {
                                                                    id
                                                                }
                                                            }
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}
