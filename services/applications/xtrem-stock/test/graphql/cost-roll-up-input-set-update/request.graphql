mutation {
    xtremStock {
        costRollUpInputSet {
            update(data: { _id: "1", items: ["#COST_A", "#COST_D"] }) {
                user {
                    email
                }
                items {
                    id
                }
                itemCategory {
                    id
                }
                commodityCode
                costCategory {
                    id
                }
                fromDate
                quantity
                includesRouting
                usesComponentStandardCost
                status
            }
        }
    }
}
