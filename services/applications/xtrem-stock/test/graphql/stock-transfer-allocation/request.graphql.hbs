mutation {
    xtremStockData {
         stock {
          updateAllocations(allocationData: {{inputParameters}}) {
            allocationRecord {
              _id
              quantityInStockUnit
            }
            oldAllocationRecord {
              _id
              quantityInStockUnit
            }
            resultAction
            stockRecord {
              _id
              quantityInStockUnit
              owner
              totalAllocated
              activeQuantityInStockUnit
            }
          }
        }
    }
}
