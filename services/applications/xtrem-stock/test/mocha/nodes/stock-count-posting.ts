import type { Context, NodeUpdateData } from '@sage/xtrem-core';
import { Test, asyncArray, date } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import type { Dict, decimal, integer } from '@sage/xtrem-shared';
import * as xtremStockData from '@sage/xtrem-stock-data';
import { assert } from 'chai';
import * as xtremStock from '../../../lib';
import * as testLib from '../../fixtures/lib';

const lineResults: Dict<
    Array<{
        lines: {
            _sortValue: integer; // sort value of the line
            oldQuantity: decimal; // quantity of the stock record when starting the count
            newQuantity: decimal; // quantity counted
            updateStock: boolean; // will stock be updated because of this line Y/N
            newLastCountDate: date | null; // the new last count date recorded in the item-site
            orderCost?: decimal; // the order cost to check in StockJournal
            orderAmount?: decimal; // the order amount to check in StockJournal
        }[];
        // result of the posting
        expectedPostedLines: {
            sortValue: integer;
        }[];
    }>
> = {
    STOCK_COUNT04: [
        {
            lines: [
                {
                    // line excluded
                    _sortValue: 10,
                    oldQuantity: 0,
                    newQuantity: 0,
                    updateStock: false,
                    newLastCountDate: date.make(2021, 8, 5),
                },
                {
                    _sortValue: 20,
                    oldQuantity: 100,
                    newQuantity: 103,
                    updateStock: true,
                    newLastCountDate: date.make(2022, 9, 6),
                    orderCost: 0,
                    orderAmount: 0,
                },
                {
                    // line excluded but we force the new quantity => nothing should happen
                    _sortValue: 30,
                    oldQuantity: 200,
                    newQuantity: 204,
                    updateStock: false,
                    newLastCountDate: null,
                },
                {
                    // quantity doesn't change
                    _sortValue: 40,
                    oldQuantity: 100000,
                    newQuantity: 100000,
                    updateStock: false,
                    newLastCountDate: date.make(2022, 9, 6),
                },
                {
                    _sortValue: 50,
                    oldQuantity: 200,
                    newQuantity: 195,
                    updateStock: true,
                    newLastCountDate: date.make(2022, 9, 6),
                    orderCost: 14.4,
                    orderAmount: -72,
                },
            ],
            // only 2 lines are posted (5 lines but 2 excluded + 1 without variance)
            expectedPostedLines: [
                {
                    sortValue: 20,
                },
                {
                    sortValue: 50,
                },
            ],
        },
    ],
    STOCK_COUNT06: [
        {
            // testCase=0 : the price of the added line is modified
            lines: [
                {
                    _sortValue: 10,
                    oldQuantity: 1000,
                    newQuantity: 990,
                    updateStock: true,
                    newLastCountDate: date.make(2022, 10, 20),
                    orderCost: 45.6,
                    orderAmount: -456,
                },
                {
                    // line added with an order price
                    _sortValue: 20,
                    oldQuantity: 0,
                    newQuantity: 10,
                    updateStock: true,
                    newLastCountDate: date.make(2022, 10, 20),
                    orderCost: 45.67, // the cost is modified in the scenario from 55.67 to 45.67
                    orderAmount: 456.7,
                },
                {
                    // line added without specifying a price
                    _sortValue: 30,
                    oldQuantity: 0,
                    newQuantity: 20,
                    updateStock: true,
                    newLastCountDate: date.make(2022, 10, 20),
                    orderCost: 45.6007, // = (45600 - 456 (line 1) + 456.7 (line 2) )/1000
                    orderAmount: 912.01,
                },
            ],
            expectedPostedLines: [
                {
                    sortValue: 10,
                },
                {
                    sortValue: 20,
                },
                {
                    sortValue: 30,
                },
            ],
        },
        // testCase=1 : exclude all lines and add a line with a standard cost item
        {
            lines: [
                {
                    // excluded line
                    _sortValue: 10,
                    oldQuantity: 1000,
                    newQuantity: 0,
                    updateStock: false,
                    newLastCountDate: date.make(2022, 10, 20),
                },
                {
                    // excluded line
                    _sortValue: 20,
                    oldQuantity: 0,
                    newQuantity: 0,
                    updateStock: false,
                    newLastCountDate: date.make(2022, 10, 20),
                },
                {
                    // line excluded
                    _sortValue: 30,
                    oldQuantity: 0,
                    newQuantity: 0,
                    updateStock: false,
                    newLastCountDate: date.make(2022, 10, 20),
                },
                {
                    // line excluded
                    _sortValue: 40,
                    oldQuantity: 0,
                    newQuantity: 10,
                    updateStock: true,
                    newLastCountDate: date.make(2022, 10, 20),
                    orderCost: 12.36,
                    orderAmount: 123.6,
                },
            ],
            expectedPostedLines: [
                {
                    sortValue: 40,
                },
            ],
        },
    ],
    STOCK_COUNT07: [
        {
            lines: [
                {
                    // line excluded
                    _sortValue: 10,
                    oldQuantity: 3,
                    newQuantity: 0,
                    updateStock: false,
                    newLastCountDate: date.make(2023, 1, 27),
                },
                {
                    _sortValue: 20,
                    oldQuantity: 5,
                    newQuantity: 3,
                    updateStock: true,
                    newLastCountDate: date.make(2023, 1, 27),
                },
                {
                    // line excluded
                    _sortValue: 30,
                    oldQuantity: 7,
                    newQuantity: 0,
                    updateStock: false,
                    newLastCountDate: date.make(2023, 1, 27),
                },
            ],
            expectedPostedLines: [
                {
                    sortValue: 20,
                },
            ],
        },
    ],
};

async function checkLineResults(context: Context, document: string, testCase: integer): Promise<void> {
    const lineIds = (
        await context.select(
            xtremStock.nodes.StockCount,
            { lines: { _id: true, _sortValue: true } },
            { filter: { number: document } },
        )
    )[0];
    await asyncArray(lineResults[document][testCase].lines).forEach(async lineQuantity => {
        const message = `document: ${document} line _sortValue:${lineQuantity._sortValue}`;

        const lineId = lineIds.lines.find(line => line._sortValue === lineQuantity._sortValue)?._id;
        // Control StockTransaction records
        const stockTransactions = await context
            .query(xtremStockData.nodes.StockTransaction, {
                filter: { documentLine: lineId },
            })
            .toArray();
        if (lineQuantity.updateStock) {
            assert.deepEqual(stockTransactions.length, 1, message);
            const stockTransaction = stockTransactions[0];
            assert.isTrue(await stockTransaction.hasSucceeded, message);
            assert.deepEqual(await stockTransaction.resultAction, 'adjusted', message);
        } else {
            assert.deepEqual(stockTransactions.length, 0, message);
        }

        // Control StockJournal records
        const stockJournals = await context
            .query(xtremStockData.nodes.StockJournal, {
                filter: { documentLine: lineId },
            })
            .toArray();
        if (lineQuantity.updateStock) {
            assert.deepEqual(stockJournals.length, 1, message);
            const stockJournal = stockJournals[0];
            assert.deepEqual(
                Number(await stockJournal.quantityInStockUnit),
                Number(lineQuantity.newQuantity - lineQuantity.oldQuantity),
                message,
            );
            if (lineQuantity.orderCost !== undefined) {
                assert.deepEqual(Number(await stockJournal.orderCost), Number(lineQuantity.orderCost), message);
            }
            if (lineQuantity.orderAmount !== undefined) {
                assert.deepEqual(Number(await stockJournal.orderAmount), Number(lineQuantity.orderAmount), message);
            }
        } else {
            assert.deepEqual(stockJournals.length, 0, message);
        }

        // Control update of Stock
        const documentLine = await context.read(xtremStock.nodes.StockCountLine, {
            _id: lineId,
        });
        const stockRecord = await xtremStockData.functions.stockLib.getStockRecord(context, {
            item: await documentLine.item,
            location: await documentLine.location,
            lot: await documentLine.lot,
            status: await documentLine.stockStatus,
            stockUnit: await (await documentLine.item).stockUnit,
            site: (await documentLine.site)._id,
            owner: await documentLine.owner,
        });

        if (lineQuantity.updateStock) {
            assert.isNotNull(stockRecord, message);
            assert.deepEqual(Number(await stockRecord.quantityInStockUnit), Number(lineQuantity.newQuantity), message);
        }
        // if the stock has not been updated and oldQuantity=0 => the stock record didn't exist and still mustn't exist
        else if (lineQuantity.oldQuantity === 0) assert.isNull(stockRecord, message);
        // as the stock has not been updated, the stock quantity mustn't have changed
        else
            assert.deepEqual(Number(await stockRecord?.quantityInStockUnit), Number(lineQuantity.oldQuantity), message);

        assert.deepEqual(await (await documentLine.itemSite).lastCountDate, lineQuantity.newLastCountDate, message);
    });
}

/**
 * set the counted quantities in the stock count
 * @param context
 * @param stockCount
 * @param quantities quantities to set
 */
async function setNewQuantities(
    context: Context,
    stockCount: xtremStock.nodes.StockCount,
    quantities: { _sortValue: number; oldQuantity: number; newQuantity: number; updateStock: boolean }[],
) {
    // Set all countedQuantityInStockUnit
    await stockCount.$.set({
        lines: (await stockCount.lines
            .map(async stockCountLine => {
                const line = await asyncArray(quantities).find(
                    async lineQuantity => lineQuantity._sortValue === (await stockCountLine._sortValue),
                );
                assert.notDeepEqual(
                    line,
                    undefined,
                    `stockCountLine not found _sortValue = ${await stockCountLine._sortValue}`,
                );
                if (!line) return undefined;
                assert.equal(
                    await stockCountLine.quantityInStockUnit,
                    line.oldQuantity,
                    `stockCountLine._sortValue = ${await stockCountLine._sortValue}`,
                );
                return {
                    _action: 'update',
                    _id: stockCountLine._id,
                    countedQuantityInStockUnit: line.newQuantity,
                } as NodeUpdateData<xtremStock.nodes.StockCountLine>;
            })
            .filter(stockCountLine => !!stockCountLine)
            .toArray()) as NodeUpdateData<xtremStock.nodes.StockCount>['lines'],
    });

    await stockCount.$.save();

    const stockCountId = stockCount._id;

    await Test.rollbackCache(context);

    const updatedStockCount = await context.read(
        xtremStock.nodes.StockCount,
        { _id: stockCountId },
        { forUpdate: true },
    );
    assert.deepEqual(await updatedStockCount.status, 'counted');

    return updatedStockCount;
}

/**
 * Run the posting of a stockCount
 * @param context
 * @param stockCount the stock count to post
 * @param lineData answer that should actually be sent by the stock service. It is used to be able to simulate this answer.
 * @returns the stockCount after the posting
 */
async function postStockCount(
    context: Context,
    stockCountId: xtremStock.nodes.StockCount['_id'],
    stockCountNumber: Awaited<xtremStock.nodes.StockCount['number']>,
    testCase: integer,
    lineData: Array<{
        stockUpdateResultStatus: xtremStockData.enums.StockUpdateResultAction;
        stockTransactionStatus: xtremStockData.enums.StockTransactionStatus;
        message?: string;
    }>,
): Promise<xtremStock.nodes.StockCount> {
    const lineIds: Record<integer, integer> = {};
    (
        await context.select(
            xtremStock.nodes.StockCount,
            { lines: { _id: true, _sortValue: true } },
            { filter: { _id: stockCountId } },
        )
    )[0].lines.forEach(line => {
        lineIds[line._sortValue] = line._id;
    });

    // 1st step: run post mutation of StockCount => excluded lines are handled

    // Post the stock count
    const posted = JSON.parse(
        await xtremStock.nodes.StockCount.postToStock(context, [stockCountId]),
    ) as xtremStockData.interfaces.PostToStockReturnJsonType<xtremStockData.enums.StockMovementTypeEnum.adjustment>;

    assert.notEqual(
        posted.result,
        'error',
        `stockCount.number= ${stockCountNumber} posted=${JSON.stringify(posted, null, 4)}`,
    );

    // this ensure typescript infer correctly the type of posted for the rest of the function.
    if (posted.result === 'error') {
        return {} as unknown as xtremStock.nodes.StockCount;
    }

    const expectedPostedLines = lineResults[stockCountNumber][testCase].expectedPostedLines.map(line => ({
        sortValue: line.sortValue,
        id: lineIds[line.sortValue],
    }));

    // check that the number of posted lines is the same as expected
    assert.deepEqual(
        posted.documents[0].lines.length,
        expectedPostedLines?.length,
        `stockCount.number= ${stockCountNumber} posted=${JSON.stringify(
            posted,
            null,
            3,
        )} expectedPostedLines=${JSON.stringify(expectedPostedLines, null, 3)}`,
    );
    expectedPostedLines?.forEach(expectedPostedLine =>
        assert.deepInclude(
            posted.documents[0].lines as any,
            expectedPostedLine,
            `expected posted line not found : ${JSON.stringify(
                expectedPostedLine,
                null,
                4,
            )} in actual posted lines: ${JSON.stringify(posted.documents[0].lines, null, 4)}`,
        ),
    );

    assert.deepEqual(posted.result, 'requested');

    // rollbackCache function will flush deferred actions and saves
    await Test.rollbackCache(context);

    let stockCountPosted = await context.read(xtremStock.nodes.StockCount, { number: stockCountNumber });

    // 2nd step: run update on stock-engine side
    await testLib.functions.TestHelpers.processStockUpdate(
        context,
        stockCountPosted,
        'adjust',
        {
            movementType: 'adjustment',
            stockCountData: {
                canBypassCountingInProgressControl: true,
            },
            stockDetailData: {
                isDocumentWithoutStockDetails: true,
            },
        },
        xtremStock.nodes.StockCount.onStockReply,
        lineData,
        xtremStock.nodes.StockCountLine.isLineUpdatingStock,
    );
    // rollbackCache function will flush deferred actions and saves
    await Test.rollbackCache(context);

    stockCountPosted = await context.read(xtremStock.nodes.StockCount, { number: stockCountNumber });

    return stockCountPosted;
}

describe('StockCount posting', () => {
    it('Post success', () =>
        Test.withContext(
            async context => {
                const stockCountNumber = 'STOCK_COUNT04';
                const testCase = 0;

                let stockCount = await context.read(
                    xtremStock.nodes.StockCount,
                    { number: stockCountNumber },
                    { forUpdate: true },
                );
                const stockCountId = stockCount._id;

                assert.equal(await stockCount.status, 'toBeCounted');

                // Try to Post a stock count not started -> error
                let posted = JSON.parse(await xtremStock.nodes.StockCount.postToStock(context, [stockCountId]));

                assert.deepEqual(
                    posted.error,
                    'You cannot post a stock count when the count is not finished: stock count STOCK_COUNT04.',
                );
                assert.deepEqual(posted.result, 'error');

                // Trigger the start of the stock count
                const started = await xtremStock.nodes.StockCount.start(context, stockCount);
                assert.isTrue(started);
                assert.equal(await stockCount.status, 'countInProgress');

                // Try to Post a stock count not 'counted' yet -> error
                posted = JSON.parse(await xtremStock.nodes.StockCount.postToStock(context, [stockCountId]));

                assert.deepEqual(
                    posted.error,
                    'You cannot post a stock count when the count is not finished: stock count STOCK_COUNT04.',
                );
                assert.deepEqual(posted.result, 'error');

                // Set all countedQuantityInStockUnit
                stockCount = await setNewQuantities(context, stockCount, lineResults[stockCountNumber][testCase].lines);

                // Set the 5th line with small quantity (< allocated quantity)
                const allocatedStockCountLine =
                    (await stockCount.lines.find(async line => (await line._sortValue) === 50)) ?? null;
                assert.isNotNull(allocatedStockCountLine);
                assert.deepEqual(
                    (await (await (await allocatedStockCountLine.stockDetail)?.stockRecord)?.totalAllocated) ?? 0,
                    100.0,
                );
                await allocatedStockCountLine.$.set({ countedQuantityInStockUnit: 1 });
                await stockCount.$.save();
                posted = JSON.parse(await xtremStock.nodes.StockCount.postToStock(context, [stockCountId]));
                assert.deepEqual(
                    posted.error,
                    'For stock count STOCK_COUNT04, the counted quantity 1 for Muesli cannot be less than the allocated quantity 100.',
                );
                assert.deepEqual(posted.result, 'error');

                // Set all countedQuantityInStockUnit for successful posting
                await setNewQuantities(context, stockCount, lineResults[stockCountNumber][testCase].lines);
                // Post the stock count
                stockCount = await postStockCount(context, stockCountId, stockCountNumber, testCase, [
                    { stockUpdateResultStatus: 'adjusted', stockTransactionStatus: 'succeeded' },
                    { stockUpdateResultStatus: 'adjusted', stockTransactionStatus: 'succeeded' },
                ]);

                await stockCount.lines.forEach(async line =>
                    assert.deepEqual(
                        await line.stockTransactionStatus,
                        'completed',
                        `stock count line _id:${line._id}`,
                    ),
                );
                // Check header after posting
                assert.deepEqual(await stockCount.status, 'closed');

                // Check lines after posting
                await checkLineResults(context, stockCountNumber, testCase);
            },
            { today: '2022-10-11' },
        ));

    it('Post stock count with lines added', () =>
        Test.withContext(
            async context => {
                const stockCountNumber = 'STOCK_COUNT06';
                const testCase = 0;

                let stockCount = await context.read(
                    xtremStock.nodes.StockCount,
                    { number: stockCountNumber },
                    { forUpdate: true },
                );
                const stockCountId = stockCount._id;

                assert.equal(await stockCount.status, 'counted');

                const stockCountLines = await stockCount.lines.toArray();
                assert.equal(stockCountLines.length, 3);

                assert.equal(await stockCountLines[0].status, 'counted');
                assert.equal(await stockCountLines[0].quantityInStockUnit, 1000);
                assert.equal(await stockCountLines[0].countedQuantityInStockUnit, 990);
                assert.equal((await stockCountLines[0].quantityVariance).valueOf(), -10);
                assert.isNull(await stockCountLines[0].newLineOrderCost);

                assert.equal(await stockCountLines[1].status, 'counted');
                assert.equal(await stockCountLines[1].quantityInStockUnit, 0);
                assert.equal(await stockCountLines[1].countedQuantityInStockUnit, 10);
                assert.equal((await stockCountLines[1].quantityVariance).valueOf(), 10);
                assert.equal((await stockCountLines[1].newLineOrderCost)?.valueOf(), 55.67);

                assert.equal(await stockCountLines[2].status, 'counted');
                assert.equal(await stockCountLines[2].quantityInStockUnit, 0);
                assert.equal(await stockCountLines[2].countedQuantityInStockUnit, 20);
                assert.equal((await stockCountLines[2].quantityVariance).valueOf(), 20);
                assert.isNull(await stockCountLines[2].newLineOrderCost);

                // modification of the cost to be sure that at the end, the cost is correct in StockJournal
                await stockCount.$.set({
                    lines: [{ _id: stockCountLines[1]._id, _action: 'update', newLineOrderCost: 45.67 }],
                });
                await stockCount.$.save();

                // Post the stock count
                stockCount = await postStockCount(context, stockCountId, stockCountNumber, testCase, [
                    { stockUpdateResultStatus: 'adjusted', stockTransactionStatus: 'succeeded' },
                    { stockUpdateResultStatus: 'adjusted', stockTransactionStatus: 'succeeded' },
                    { stockUpdateResultStatus: 'adjusted', stockTransactionStatus: 'succeeded' },
                ]);

                await stockCount.lines.forEach(async line =>
                    assert.deepEqual(
                        await line.stockTransactionStatus,
                        'completed',
                        `stock count line _id:${line._id}`,
                    ),
                );
                assert.deepEqual(await stockCount.status, 'closed');

                // Check lines after posting
                await checkLineResults(context, stockCountNumber, testCase);
            },
            { today: '2022-10-23' },
        ));

    it('Add line with standard cost item and post the stock', () =>
        Test.withContext(
            async context => {
                const stockCountNumber = 'STOCK_COUNT06';
                const testCase = 1;

                let stockCount = await context.read(
                    xtremStock.nodes.StockCount,
                    { number: stockCountNumber },
                    { forUpdate: true },
                );
                const stockCountId = stockCount._id;
                const stockCountLines = await stockCount.lines.toArray();
                assert.equal(stockCountLines.length, 3);

                await stockCount.$.set({
                    lines: [
                        {
                            _id: stockCountLines[0]._id,
                            _action: 'update',
                            status: 'excluded',
                            countedQuantityInStockUnit: 0,
                        },
                        {
                            _id: stockCountLines[1]._id,
                            _action: 'update',
                            status: 'excluded',
                            countedQuantityInStockUnit: 0,
                        },
                        {
                            _id: stockCountLines[2]._id,
                            _action: 'update',
                            status: 'excluded',
                            countedQuantityInStockUnit: 0,
                        },
                        {
                            _action: 'create',
                            status: 'counted',
                            item: await context.read(xtremMasterData.nodes.Item, { _id: '#STOSTD' }),
                            stockStatus: await context.read(xtremStockData.nodes.StockStatus, { _id: '#R' }),
                            location: await context.read(xtremMasterData.nodes.Location, {
                                _id: '#LOC3|US001|Loading dock',
                            }),
                            countedQuantityInStockUnit: 10,
                            newLineOrderCost: 12.36,
                        },
                    ],
                });
                await stockCount.$.save();

                assert.equal(await stockCount.status, 'counted');

                // modification of the cost to be sure that at the end, the cost is correct in StockJournal
                await stockCount.$.set({
                    lines: [{ _id: stockCountLines[1]._id, _action: 'update', newLineOrderCost: 21.54 }],
                });
                await stockCount.$.save();

                const newLineId = (await stockCount.lines.find(async line => (await (await line.item).id) === 'STOSTD'))
                    ?._id;
                assert.isNotNull(newLineId);

                // Post the stock count
                stockCount = await postStockCount(context, stockCountId, stockCountNumber, testCase, [
                    { stockUpdateResultStatus: 'adjusted', stockTransactionStatus: 'succeeded' },
                ]);

                await stockCount.lines.forEach(async line =>
                    assert.deepEqual(
                        await line.stockTransactionStatus,
                        'completed',
                        `stock count line _id:${line._id}`,
                    ),
                );
                assert.deepEqual(await stockCount.status, 'closed');

                // Check lines after posting
                const stockJournals = await context
                    .query(xtremStockData.nodes.StockJournal, {
                        filter: { documentLine: newLineId },
                    })
                    .toArray();
                assert.deepEqual(stockJournals.length, 1);
                const stockJournal = stockJournals[0];
                assert.deepEqual(await stockJournal.valuedCost, 112.222); // standard cost at 2022-10-23
                assert.deepEqual(await stockJournal.movementAmount, 1122.22);
                assert.deepEqual(await stockJournal.nonAbsorbedAmount, -998.62);
            },
            { today: '2022-10-23' },
        ));

    it('Post error', () =>
        Test.withContext(async context => {
            const stockCountNumber = 'STOCK_COUNT04';
            const testCase = 0;

            let stockCount = await context.read(
                xtremStock.nodes.StockCount,
                { number: stockCountNumber },
                { forUpdate: true },
            );
            const stockCountId = stockCount._id;

            assert.equal(await stockCount.status, 'toBeCounted');

            // Trigger the start of the stock count
            const started = await xtremStock.nodes.StockCount.start(context, stockCount);
            assert.isTrue(started);
            assert.equal(await stockCount.status, 'countInProgress');

            // Set all countedQuantityInStockUnit
            await setNewQuantities(context, stockCount, lineResults[stockCountNumber][testCase].lines);

            // Force modification of Stock before stock count posting => posting will trigger an error
            const documentLine = await context
                .query(xtremStock.nodes.StockCountLine, { filter: { document: stockCountId, _sortValue: 20 } })
                .elementAt(0);
            const stockRecord = await xtremStockData.functions.stockLib.getStockRecord(context, {
                item: await documentLine.item,
                location: await documentLine.location,
                lot: await documentLine.lot,
                status: await documentLine.stockStatus,
                stockUnit: await (await documentLine.item).stockUnit,
                site: (await documentLine.site)._id,
                owner: await documentLine.owner,
            });
            assert.isNotNull(stockRecord);
            await stockRecord.$.set({ quantityInStockUnit: (await stockRecord.quantityInStockUnit) + 10 });
            /* does not throw */ await stockRecord.$.save();

            // Post the stock count
            stockCount = await postStockCount(context, stockCountId, stockCountNumber, testCase, [
                { stockUpdateResultStatus: 'none', stockTransactionStatus: 'error' },
                { stockUpdateResultStatus: 'adjusted', stockTransactionStatus: 'succeeded' },
            ]);

            const stockCountLineIds: Record<integer, integer> = {};
            await stockCount.lines.forEach(async line => {
                const sortValue = await line._sortValue;
                assert.deepEqual(
                    await line.stockTransactionStatus,
                    sortValue === 20 ? 'error' : 'completed',
                    `stock count line _id:${line._id}`,
                );
                stockCountLineIds[sortValue] = line._id;
            });
            assert.deepEqual(await stockCount.status, 'counted');

            const transactionExpectedResults = [
                { _sortValue: 10, content: undefined },
                {
                    _sortValue: 20,
                    content: {
                        status: 'error',
                        message:
                            'The quantity of the stock record to count has been modified since the stock count started.\n\nItem: 17890\n\nStatus: A\n\nLot: MISCRECEIPT4\n\nLocation: LOC3\n\nCurrent quantity in stock: 110',
                        resultAction: 'none',
                    },
                },
                { _sortValue: 30, content: undefined },
                { _sortValue: 40, content: undefined },
                { _sortValue: 50, content: { status: 'succeeded', message: '', resultAction: 'adjusted' } },
            ];

            await asyncArray(transactionExpectedResults).forEach(async transactionExpectedResult => {
                const message = `line _sortValue: ${transactionExpectedResult._sortValue} _id:${stockCountLineIds[transactionExpectedResult._sortValue]}`;

                // Control StockTransactionRecords
                const stockTransactions = await context
                    .query(xtremStockData.nodes.StockTransaction, {
                        filter: { documentLine: stockCountLineIds[transactionExpectedResult._sortValue] },
                    })
                    .toArray();

                if (transactionExpectedResult.content) {
                    // only 1 transaction is expected
                    assert.deepEqual(stockTransactions.length, 1, message);
                    assert.deepEqual(
                        await stockTransactions[0].status,
                        transactionExpectedResult.content.status,
                        message,
                    );
                    assert.deepEqual(
                        await stockTransactions[0].message,
                        transactionExpectedResult.content.message,
                        message,
                    );
                    assert.deepEqual(
                        await stockTransactions[0].resultAction,
                        transactionExpectedResult.content.resultAction,
                        message,
                    );
                }
                // no transaction expected
                else assert.deepEqual(stockTransactions.length, 0, message);
            });
        }));

    it('Post success with serial number issued', () =>
        Test.withContext(
            async context => {
                const stockCountNumber = 'STOCK_COUNT07';
                const testCase = 0;

                let stockCount = await context.read(
                    xtremStock.nodes.StockCount,
                    { number: stockCountNumber },
                    { forUpdate: true },
                );

                const stockCountId = stockCount._id;

                // Before the update of the 2nd line
                assert.equal(await stockCount.status, 'countInProgress');
                const stockCountLines = await stockCount.lines.toArray();
                assert.equal(await stockCountLines[1].status, 'countInProgress');
                assert.equal(await stockCountLines[1].quantityInStockUnit, 5);
                assert.equal(await stockCountLines[1].countedQuantityInStockUnit, 5);

                // Focus on the 2nd line and its 5 serials
                const serials = await stockCountLines[1].stockCountLineSerialNumbers.toArray();
                assert.equal(await serials[0].isCounted, true);
                assert.equal(await serials[1].isCounted, false);
                assert.equal(await serials[2].isCounted, false);
                assert.equal(await serials[3].isCounted, false);
                assert.equal(await serials[4].isCounted, false);
                assert.isNotNull(await (await serials[0].serialNumber).stockRecord);
                assert.isNotNull(await (await serials[1].serialNumber).stockRecord);
                assert.isNotNull(await (await serials[2].serialNumber).stockRecord);
                assert.isNotNull(await (await serials[3].serialNumber).stockRecord);
                assert.isNotNull(await (await serials[4].serialNumber).stockRecord);

                // Update the 2nd line to counted 3/5 (2 SN not counted)
                await stockCountLines[1].$.set({
                    countedQuantityInStockUnit: 3,
                    status: 'counted',
                    jsonSerialNumbers: {
                        ranges: [
                            {
                                quantity: 2,
                                numericStart: 5,
                                numericEnd: 6,
                                originalStartId: 'STK-SN-0005',
                                startingSerialNumberId: 'STK-SN-0005',
                                endingSerialNumberId: 'STK-SN-0006',
                            },
                            {
                                quantity: 1,
                                numericStart: 8,
                                numericEnd: 8,
                                originalStartId: 'STK-SN-0008',
                                startingSerialNumberId: 'STK-SN-0008',
                                endingSerialNumberId: 'STK-SN-0008',
                            },
                        ],
                    },
                });

                // Set the 1st and 3rd lines excluded
                await stockCountLines[0].$.set({ status: 'excluded', countedQuantityInStockUnit: 0 });
                await stockCountLines[2].$.set({ status: 'excluded', countedQuantityInStockUnit: 0 });

                // Save the updated stock count
                await (() => stockCount.$.save())();

                // Post the stock count
                stockCount = await postStockCount(context, stockCountId, stockCountNumber, testCase, [
                    { stockUpdateResultStatus: 'adjusted', stockTransactionStatus: 'succeeded' },
                ]);

                // Check header after posting
                assert.equal(await stockCount.status, 'closed');

                // Check that issued SN are not linked to a stock record
                const stockCountLinesAfter = await stockCount.lines.toArray();
                const serialsAfter = await stockCountLinesAfter[1].stockCountLineSerialNumbers.toArray();

                assert.isNull(await (await serialsAfter[0].serialNumber).stockRecord);
                assert.isNull(await (await serialsAfter[3].serialNumber).stockRecord);

                // Check lines after posting
                await checkLineResults(context, stockCountNumber, testCase);
            },
            {
                today: '2023-02-13',
                testActiveServiceOptions: [xtremMasterData.serviceOptions.serialNumberOption],
            },
        ));

    it('Create a stock count and repost it', () =>
        Test.withContext(
            async context => {
                const stockCountNumber = 'STOCK_COUNT04';
                const testCase = 0;

                let stockCount = await context.read(
                    xtremStock.nodes.StockCount,
                    { number: stockCountNumber },
                    { forUpdate: true },
                );
                const stockCountId = stockCount._id;

                assert.equal(await stockCount.status, 'toBeCounted');

                // Trigger the start of the stock count
                const started = await xtremStock.nodes.StockCount.start(context, stockCount);
                assert.isTrue(started);
                assert.equal(await stockCount.status, 'countInProgress');

                // Set all countedQuantityInStockUnit
                await setNewQuantities(context, stockCount, lineResults[stockCountNumber][testCase].lines);

                // Post the stock count
                stockCount = await postStockCount(context, stockCountId, stockCountNumber, testCase, [
                    { stockUpdateResultStatus: 'adjusted', stockTransactionStatus: 'succeeded' },
                    { stockUpdateResultStatus: 'adjusted', stockTransactionStatus: 'succeeded' },
                ]);

                // Check header after posting
                await stockCount.lines.forEach(async line =>
                    assert.deepEqual(
                        await (line as xtremStock.nodes.StockCountLine).stockTransactionStatus,
                        'completed',
                        `stock count line _id:${line._id}`,
                    ),
                );
                assert.deepEqual(await stockCount.status, 'closed');

                // Check lines after posting
                await checkLineResults(context, stockCountNumber, testCase);

                const stockCountLines = await asyncArray(await stockCount.lines.toArray())
                    .map(async line => {
                        return {
                            baseDocumentLineSysId: line._id,
                            storedAttributes:
                                (await line.storedAttributes) as xtremMasterData.interfaces.StoredAttributes,
                            storedDimensions: (await line.storedDimensions) || {},
                        };
                    })
                    .toArray();

                await assert.isRejected(
                    xtremStock.nodes.StockCount.repost(context, stockCount, stockCountLines),
                    "You can only repost a stock count if the status is 'Failed' or 'Not recorded'.",
                );
            },
            { today: '2022-10-11' },
        ));

    it('Repost an existing stock count notRecorded in finance', () =>
        Test.withContext(
            async context => {
                const stockCountNumber = 'STOCK_COUNT08';

                const stockCount = await context.read(
                    xtremStock.nodes.StockCount,
                    { number: stockCountNumber },
                    { forUpdate: true },
                );
                assert.equal(await stockCount.financeIntegrationStatus, 'notRecorded');

                const stockCountLines = await asyncArray(await stockCount.lines.toArray())
                    .map(async line => {
                        return {
                            baseDocumentLineSysId: line._id,
                            storedAttributes:
                                (await line.storedAttributes) as xtremMasterData.interfaces.StoredAttributes,
                            storedDimensions: (await line.storedDimensions) || {},
                        };
                    })
                    .toArray();

                const repostResult = await xtremStock.nodes.StockCount.repost(context, stockCount, stockCountLines);
                assert.equal(repostResult.wasSuccessful, true);
            },
            { today: '2024-01-11' },
        ));

    it('Check onceStockCompleted is called only when the document is completed', () =>
        Test.withContext(async context => {
            const result = await xtremStockData.functions.testLib.testOnceStockCompleted(context, {
                clas: xtremStock.nodes.StockCount,
                movementType: 'adjustment',
                documents: [
                    { key: { number: 'STOCK_COUNT01' }, isCompleted: false },
                    { key: { number: 'STOCK_COUNT02' }, isCompleted: true },
                    { key: { number: 'STOCK_COUNT03' }, isCompleted: false },
                    { key: { number: 'STOCK_COUNT04' }, isCompleted: true },
                ],
                returnedProperty: 'number',
            });

            assert.deepEqual(result, ['STOCK_COUNT02', 'STOCK_COUNT04']);
        }));

    it('Post error when stock record has been removed from linked stock count.', () =>
        Test.withContext(
            async context => {
                const stockCountNumber = 'STOCK_COUNT06';

                const stockCount = await context.read(
                    xtremStock.nodes.StockCount,
                    { number: stockCountNumber },
                    { forUpdate: true },
                );

                assert.equal(await stockCount.status, 'counted');

                await stockCount.$.set({
                    lines: [
                        {
                            _action: 'update',
                            _id: (await stockCount.lines.elementAt(0))._id,
                            stockRecord: null,
                        },
                    ],
                });

                await stockCount.$.save();

                const stockCountId = stockCount._id;
                const posted = JSON.parse(await xtremStock.nodes.StockCount.postToStock(context, [stockCountId]));

                assert.deepEqual(
                    posted.error,
                    'The stock count record for item STKCOUNT3 has changed since the stock count started. Please exclude this line from the stock count.',
                );
                assert.deepEqual(posted.result, 'error');
            },
            { today: '2022-10-11' },
        ));
});

describe('Posting details', () => {
    it('Gets posting details successfully', () =>
        Test.withContext(async context => {
            const scDocumentNumber = 'CS250001';
            const scDocument = await context.read(xtremStock.nodes.StockCount, { number: scDocumentNumber });
            assert.equal(await scDocument.postingDetails.length, 1);

            const journalEntry = await scDocument.postingDetails.at(0);
            assert.equal(await journalEntry?.documentType, 'stockCount');
            assert.equal(await journalEntry?.documentNumber, scDocumentNumber);
            assert.equal(await journalEntry?.documentSysId, scDocument._id);
            assert.equal(await journalEntry?.targetDocumentType, 'journalEntry');
        }));
});
