import { Test, date } from '@sage/xtrem-core';
import { assert } from 'chai';
import * as xtremStock from '../../../lib';

describe('Stock reorder calculation', () => {
    it('setReorderCalculationInputSetStatus Method ', () =>
        Test.withContext(async context => {
            const inputSet = await context.create(xtremStock.nodes.StockReorderCalculationInputSet, {
                company: '#US001',
                fromItem: '#Juice',
                endDate: date.today(),
                toItem: '#Milk',
            });
            await inputSet.$.save();
            assert.equal('draft', await inputSet.status);

            await xtremStock.nodes.StockReorderCalculationInputSet.setReorderCalculationInputSetStatus(context, {
                userId: context.userId.toString(),
                status: 'inProgress',
            });

            const result = await context
                .query(xtremStock.nodes.StockReorderCalculationInputSet, {
                    filter: { user: context.userId.toString() },
                })
                .toArray();

            assert.lengthOf(result, 1);
            assert.equal('inProgress', await result[0].status);
        }));
});
