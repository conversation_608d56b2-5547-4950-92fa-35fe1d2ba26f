import { asyncArray, date, Test } from '@sage/xtrem-core';
import * as xtremFinanceData from '@sage/xtrem-finance-data';
import * as xtremMasterData from '@sage/xtrem-master-data';
import { ValidationSeverity } from '@sage/xtrem-shared';
import * as xtremStockData from '@sage/xtrem-stock-data';
import * as xtremSystem from '@sage/xtrem-system';
import { assert } from 'chai';
import * as xtremStock from '../../../lib';
import { getLastStockJournal } from '../../../lib/functions/stock-journal-lib';
import * as testLib from '../../fixtures/lib';

const { withSequenceNumberContext } = xtremMasterData.functions.testLib;

describe('StockReceipt node', () => {
    afterEach(() => {
        xtremSystem.TestHelpers.Sinon.removeMocks();
    });

    it('Read receipt', () =>
        Test.withContext(async context => {
            const receipt = await context.read(xtremStock.nodes.StockReceipt, {
                number: 'MISC_RECEIPT1',
            });

            assert.instanceOf(receipt, xtremStock.nodes.StockReceipt);
            assert.equal(await receipt.number, 'MISC_RECEIPT1');
            assert.equal((await receipt.effectiveDate).toString(), '2020-05-10');

            const receiptLines = await receipt.lines.toArray();
            assert.equal(await (await receiptLines[0].item).id, 'SIMPLE_TEST');
            assert.equal(await receiptLines[0].quantityInStockUnit, 10);
            assert.equal(await (await receiptLines[0].document).number, 'MISC_RECEIPT1');
            assert.equal((await (await receiptLines[0].document).effectiveDate).toString(), '2020-05-10');

            assert.equal(await (await receiptLines[1].item).id, 'LOT_TEST');
            assert.equal(await receiptLines[1].quantityInStockUnit, 15);
            assert.equal(await (await receiptLines[1].document).number, 'MISC_RECEIPT1');
            assert.equal((await (await receiptLines[1].document).effectiveDate).toString(), '2020-05-10');
            assert.equal(await receipt.displayStatus, 'detailsRequired');
        }));

    it('Delete receipt with status draft', () =>
        Test.withContext(async context => {
            const receipt = await context.read(
                xtremStock.nodes.StockReceipt,
                {
                    number: 'MISC_RECEIPT1',
                },
                { forUpdate: true },
            );

            assert.instanceOf(receipt, xtremStock.nodes.StockReceipt);
            assert.equal(await receipt.status, 'draft');

            await receipt.$.delete();
        }));

    it.skip('Cannot delete receipt with status closed', () =>
        Test.withContext(async context => {
            const site = await context.read(xtremSystem.nodes.Site, { id: 'US001' });
            const item = await context.read(xtremMasterData.nodes.Item, { id: 'LOT_TEST1' });
            const stockSearchData: xtremStockData.interfaces.StockSearchData = {
                item: item._id,
                location: (await context.read(xtremMasterData.nodes.Location, { id: 'LOC3' }))._id,
                lot: (await context.read(xtremStockData.nodes.Lot, { id: 'MISCRECEIPT1', item }))._id,
                status: (await context.read(xtremStockData.nodes.StockStatus, { id: 'A' }))._id,
                stockUnit: (await item.stockUnit)._id,
                site: site._id,
                owner: await site.id,
            };

            let receipt = await context.create(xtremStock.nodes.StockReceipt, {
                effectiveDate: date.today(),
                stockSite: site,
                lines: [
                    {
                        item: stockSearchData.item,
                        stockStatus: stockSearchData.status,
                        quantityInStockUnit: 15,
                        // try to set a wrong stockDetailStatus status
                        stockDetailStatus: 'notRequired',
                        stockDetails: [
                            testLib.functions.TestHelpers.stockSearchDataToDetailCreateData(stockSearchData, {
                                quantityInStockUnit: 15,
                            }),
                        ],
                    },
                ],
            });

            await receipt.$.save();

            assert.equal(await (await receipt.lines.elementAt(0)).stockDetailStatus, 'entered');

            await testLib.functions.TestHelpers.processStockUpdate(
                context,
                receipt,
                'receive',
                { movementType: 'receipt' },
                xtremStock.nodes.StockReceipt.onStockReply,
                [{ stockUpdateResultStatus: 'increased', stockTransactionStatus: 'succeeded' }],
            );

            assert.instanceOf(receipt, xtremStock.nodes.StockReceipt);

            receipt = await context.read(xtremStock.nodes.StockReceipt, { _id: receipt._id });
            assert.equal(await receipt.status, 'closed');

            await assert.isRejected(
                receipt.$.delete(),
                'Dimension type: delete failed',
                'The current stock receipt cannot be deleted.',
            );
        }));

    it('Create receipt default dimensions', () =>
        Test.withContext(async context => {
            const site = await context.read(xtremSystem.nodes.Site, { id: 'US001' });
            const item = await context.read(xtremMasterData.nodes.Item, { id: 'LOT_TEST1' });
            const stockSearchData: xtremStockData.interfaces.StockSearchData = {
                item: item._id,
                location: (await context.read(xtremMasterData.nodes.Location, { id: 'LOC3' }))._id,
                lot: (await context.read(xtremStockData.nodes.Lot, { id: 'MISCRECEIPT1', item }))._id,
                status: (await context.read(xtremStockData.nodes.StockStatus, { id: 'A' }))._id,
                stockUnit: (await item.stockUnit)._id,
                site: site._id,
                owner: await site.id,
            };

            const previousStockQuantity = (
                await (
                    await xtremStockData.functions.stockLib.getStockRecord(context, stockSearchData)
                )?.quantityInStockUnit
            )?.valueOf();

            let receipt = await context.create(xtremStock.nodes.StockReceipt, {
                number: 'TEST',
                effectiveDate: date.today(),
                stockSite: site,
                lines: [
                    {
                        item: stockSearchData.item,
                        stockStatus: stockSearchData.status,
                        quantityInStockUnit: 15,
                        stockDetails: [
                            testLib.functions.TestHelpers.stockSearchDataToDetailCreateData(stockSearchData, {
                                quantityInStockUnit: 15,
                                movementType: 'receipt',
                                stockDetailLot: {
                                    lot: await context.read(xtremStockData.nodes.Lot, {
                                        id: 'MISCRECEIPT1',
                                        sublot: '',
                                        item,
                                    }),
                                },
                            }),
                        ],
                    },
                ],
            });

            const stockJournalSearchData = {
                item: item._id,
                site: site._id,
                effectiveDate: await receipt.effectiveDate,
            };

            await receipt.$.save();

            assert.equal(await (await receipt.financialSite).id, await site.id);
            assert.equal(
                await (
                    await receipt.transactionCurrency
                ).id,
                await (
                    await (
                        await (
                            await receipt.financialSite
                        ).businessEntity
                    ).currency
                ).id,
            );
            assert.equal(await receipt.documentDate, await receipt.effectiveDate);
            assert.equal(await (await receipt.lines.elementAt(0)).stockDetailStatus, 'entered');
            assert.equal(await receipt.displayStatus, 'detailsEntered');

            assert.equal(
                JSON.stringify(await (await receipt.lines.elementAt(0)).storedAttributes),
                '{"project":"AttPROJ","task":"Task1"}',
            );
            assert.equal(
                JSON.stringify(await (await receipt.lines.elementAt(0)).storedDimensions),
                '{"dimensionType02":"CHANNELVALUE1"}',
            );

            const documentID = receipt._id;

            // rollbackCache function will flush deferred actions and saves
            await Test.rollbackCache(context);

            receipt = await context.read(xtremStock.nodes.StockReceipt, { _id: documentID });
            const firstReceiptLine = await receipt.lines.elementAt(0);

            await testLib.functions.TestHelpers.processStockUpdate(
                context,
                receipt,
                'receive',
                { movementType: 'receipt' },
                xtremStock.nodes.StockReceipt.onStockReply,
                [{ stockUpdateResultStatus: 'increased', stockTransactionStatus: 'succeeded' }],
            );

            const newStockQuantity = (
                await (
                    await xtremStockData.functions.stockLib.getStockRecord(context, stockSearchData)
                )?.quantityInStockUnit
            )?.valueOf();

            const producedStockMovement = await getLastStockJournal(context, stockJournalSearchData);

            assert.instanceOf(receipt, xtremStock.nodes.StockReceipt);
            assert.equal(await (await firstReceiptLine.item).id, 'LOT_TEST1');
            assert.equal(await firstReceiptLine.quantityInStockUnit, 15);
            assert.equal(
                (newStockQuantity ? +newStockQuantity : 0).valueOf(),
                (
                    (previousStockQuantity ? +previousStockQuantity : 0) + (await firstReceiptLine.quantityInStockUnit)
                ).valueOf(),
            );
            assert.equal(await producedStockMovement?.sequence, 99999);
            assert.equal((await producedStockMovement?.documentLine)?._id, firstReceiptLine._id);
            assert.isNotEmpty(await firstReceiptLine.stockDetails.toArray());
            assert.equal(await (await firstReceiptLine.site).id, await (await receipt.stockSite).id);
        }));

    it('Create receipt dimensions added', () =>
        Test.withContext(async context => {
            const site = await context.read(xtremSystem.nodes.Site, { id: 'US001' });
            const item = await context.read(xtremMasterData.nodes.Item, { id: 'LOT_TEST1' });
            const stockSearchData: xtremStockData.interfaces.StockSearchData = {
                item: item._id,
                location: (await context.read(xtremMasterData.nodes.Location, { id: 'LOC3' }))._id,
                lot: (await context.read(xtremStockData.nodes.Lot, { id: 'MISCRECEIPT1', item }))._id,
                status: (await context.read(xtremStockData.nodes.StockStatus, { id: 'A' }))._id,
                stockUnit: (await item.stockUnit)._id,
                site: site._id,
                owner: await site.id,
            };

            const previousStockQuantity = (
                await (
                    await xtremStockData.functions.stockLib.getStockRecord(context, stockSearchData)
                )?.quantityInStockUnit
            )?.valueOf();

            let receipt = await context.create(xtremStock.nodes.StockReceipt, {
                number: 'TEST',
                effectiveDate: date.today(),
                stockSite: site,
                lines: [
                    {
                        item: stockSearchData.item,
                        stockStatus: stockSearchData.status,
                        quantityInStockUnit: 15,
                        storedAttributes: {
                            project: 'AttPROJ',
                            task: 'Task1',
                            employee: '',
                        },
                        storedDimensions: {
                            dimensionType02: 'CHANNELVALUE1',
                            dimensionType03: 'DIMTYPE1VALUE1',
                            dimensionType04: 'DIMTYPE2VALUE2',
                        },
                        stockDetails: [
                            testLib.functions.TestHelpers.stockSearchDataToDetailCreateData(stockSearchData, {
                                quantityInStockUnit: 15,
                                movementType: 'receipt',
                                stockDetailLot: {
                                    lot: await context.read(xtremStockData.nodes.Lot, {
                                        id: 'MISCRECEIPT1',
                                        sublot: '',
                                        item,
                                    }),
                                },
                            }),
                        ],
                    },
                ],
            });

            const stockJournalSearchData = {
                item: item._id,
                site: site._id,
                effectiveDate: await receipt.effectiveDate,
            };

            await receipt.$.save();

            assert.equal(await (await receipt.financialSite).id, await site.id);
            assert.equal(
                await (
                    await receipt.transactionCurrency
                ).id,
                await (
                    await (
                        await (
                            await receipt.financialSite
                        ).businessEntity
                    ).currency
                ).id,
            );
            assert.equal(await receipt.documentDate, await receipt.effectiveDate);
            assert.equal(await (await receipt.lines.elementAt(0)).stockDetailStatus, 'entered');
            assert.equal(await receipt.displayStatus, 'detailsEntered');

            assert.equal(
                JSON.stringify(await (await receipt.lines.elementAt(0)).storedAttributes),
                '{"project":"AttPROJ","task":"Task1","employee":""}',
            );
            assert.equal(
                JSON.stringify(await (await receipt.lines.elementAt(0)).storedDimensions),
                '{"dimensionType02":"CHANNELVALUE1","dimensionType03":"DIMTYPE1VALUE1","dimensionType04":"DIMTYPE2VALUE2"}',
            );

            const documentID = receipt._id;

            // rollbackCache function will flush deferred actions and saves
            await Test.rollbackCache(context);

            receipt = await context.read(xtremStock.nodes.StockReceipt, { _id: documentID });
            const firstReceiptLine = await receipt.lines.elementAt(0);

            await testLib.functions.TestHelpers.processStockUpdate(
                context,
                receipt,
                'receive',
                { movementType: 'receipt' },
                xtremStock.nodes.StockReceipt.onStockReply,
                [{ stockUpdateResultStatus: 'increased', stockTransactionStatus: 'succeeded' }],
            );

            const newStockQuantity = (
                await (
                    await xtremStockData.functions.stockLib.getStockRecord(context, stockSearchData)
                )?.quantityInStockUnit
            )?.valueOf();

            const producedStockMovement = await getLastStockJournal(context, stockJournalSearchData);

            assert.instanceOf(receipt, xtremStock.nodes.StockReceipt);
            assert.equal(await (await firstReceiptLine.item).id, 'LOT_TEST1');
            assert.equal(await firstReceiptLine.quantityInStockUnit, 15);
            assert.equal(
                (newStockQuantity ? +newStockQuantity : 0).valueOf(),
                (
                    (previousStockQuantity ? +previousStockQuantity : 0) + (await firstReceiptLine.quantityInStockUnit)
                ).valueOf(),
            );
            assert.equal(await producedStockMovement?.sequence, 99999);
            assert.equal((await producedStockMovement?.documentLine)?._id, firstReceiptLine._id);
            assert.isNotEmpty(await firstReceiptLine.stockDetails.toArray());
            assert.equal(await (await firstReceiptLine.site).id, await (await receipt.stockSite).id);
        }));

    it('Create receipt with sequence number for receipt number', () =>
        Test.withContext(
            async context => {
                const site = await context.read(xtremSystem.nodes.Site, { id: 'US001' });
                const item = await context.read(xtremMasterData.nodes.Item, { id: 'LOT_TEST1' });
                const stockSearchData: xtremStockData.interfaces.StockSearchData = {
                    item: item._id,
                    location: (await context.read(xtremMasterData.nodes.Location, { id: 'LOC3' }))._id,
                    lot: (await context.read(xtremStockData.nodes.Lot, { id: 'MISCRECEIPT1', item }))._id,
                    status: (await context.read(xtremStockData.nodes.StockStatus, { id: 'A' }))._id,
                    stockUnit: (await item.stockUnit)._id,
                    site: site._id,
                    owner: await site.id,
                };

                let receipt = await context.create(xtremStock.nodes.StockReceipt, {
                    effectiveDate: date.today(),
                    stockSite: site,
                    lines: [
                        {
                            item: stockSearchData.item,
                            stockStatus: stockSearchData.status,
                            quantityInStockUnit: 15,
                            stockDetails: [
                                testLib.functions.TestHelpers.stockSearchDataToDetailCreateData(stockSearchData, {
                                    quantityInStockUnit: 15,
                                }),
                            ],
                        },
                    ],
                });

                await receipt.$.save({ flushDeferredActions: true });

                assert.equal(await (await receipt.financialSite).id, await site.id);
                assert.equal(
                    await (
                        await receipt.transactionCurrency
                    ).id,
                    await (
                        await (
                            await (
                                await receipt.financialSite
                            ).businessEntity
                        ).currency
                    ).id,
                );
                assert.equal(await receipt.documentDate, await receipt.effectiveDate);

                const documentID = receipt._id;

                // rollbackCache function will flush deferred actions and saves
                await Test.rollbackCache(context);

                receipt = await context.read(xtremStock.nodes.StockReceipt, { _id: documentID });

                await testLib.functions.TestHelpers.processStockUpdate(
                    context,
                    receipt,
                    'receive',
                    { movementType: 'receipt' },
                    xtremStock.nodes.StockReceipt.onStockReply,
                    [{ stockUpdateResultStatus: 'increased', stockTransactionStatus: 'succeeded' }],
                );

                assert.instanceOf(receipt, xtremStock.nodes.StockReceipt);

                assert.equal(
                    await receipt.number,
                    // Seq number component : MSI year month sequenceNumber
                    `SR200001`,
                );
            },
            { today: '2020-05-12', testActiveServiceOptions: [xtremMasterData.serviceOptions.serialNumberOption] },
        ));

    it('Create receipt with multiple (2) lines', () =>
        Test.withContext(
            async context => {
                const site = await context.read(xtremSystem.nodes.Site, { id: 'US001' });
                const item = await context.read(xtremMasterData.nodes.Item, { id: 'LOT_TEST1' });
                const stockSearchDataForLine1: xtremStockData.interfaces.StockSearchData = {
                    item: item._id,
                    location: (await context.read(xtremMasterData.nodes.Location, { id: 'LOC3' }))._id,
                    lot: (await context.read(xtremStockData.nodes.Lot, { id: 'MISCRECEIPT1', item }))._id,
                    status: (await context.read(xtremStockData.nodes.StockStatus, { id: 'A' }))._id,
                    stockUnit: (await item.stockUnit)._id,
                    site: site._id,
                    owner: await site.id,
                };
                const stockSearchDataForLine2: xtremStockData.interfaces.StockSearchData = {
                    ...stockSearchDataForLine1,
                    status: (await context.read(xtremStockData.nodes.StockStatus, { id: 'A1' }))._id,
                };

                const previousStockQuantityForLine1 = (
                    await (
                        await xtremStockData.functions.stockLib.getStockRecord(context, stockSearchDataForLine1)
                    )?.quantityInStockUnit
                )?.valueOf();

                const previousStockQuantityForLine2 = (
                    await (
                        await xtremStockData.functions.stockLib.getStockRecord(context, stockSearchDataForLine2)
                    )?.quantityInStockUnit
                )?.valueOf();

                let receipt = await context.create(xtremStock.nodes.StockReceipt, {
                    number: 'TEST',
                    effectiveDate: date.today(),
                    stockSite: site,
                    lines: [
                        {
                            _sortValue: 10,
                            item: stockSearchDataForLine1.item,
                            stockStatus: stockSearchDataForLine1.status,
                            quantityInStockUnit: 15,
                            stockDetails: [
                                testLib.functions.TestHelpers.stockSearchDataToDetailCreateData(
                                    stockSearchDataForLine1,
                                    {
                                        quantityInStockUnit: 15,
                                        movementType: 'receipt',
                                        stockDetailLot: {
                                            lot: await context.read(xtremStockData.nodes.Lot, {
                                                id: 'MISCRECEIPT1',
                                                sublot: '',
                                                item,
                                            }),
                                        },
                                    },
                                ),
                            ],
                        },
                        {
                            _sortValue: 20,
                            item: stockSearchDataForLine2.item,
                            stockStatus: stockSearchDataForLine2.status,
                            quantityInStockUnit: 15,
                            stockDetails: [
                                testLib.functions.TestHelpers.stockSearchDataToDetailCreateData(
                                    stockSearchDataForLine2,
                                    {
                                        quantityInStockUnit: 15,
                                        movementType: 'receipt',
                                        stockDetailLot: {
                                            lot: await context.read(xtremStockData.nodes.Lot, {
                                                id: 'MISCRECEIPT1',
                                                sublot: '',
                                                item,
                                            }),
                                        },
                                    },
                                ),
                            ],
                        },
                    ],
                });

                const stockJournalSearchData = {
                    item: item._id,
                    site: site._id,
                    effectiveDate: await receipt.effectiveDate,
                };

                await receipt.$.save();

                assert.equal(await (await receipt.financialSite).id, await site.id);
                assert.equal(
                    await (
                        await receipt.transactionCurrency
                    ).id,
                    await (
                        await (
                            await (
                                await receipt.financialSite
                            ).businessEntity
                        ).currency
                    ).id,
                );
                assert.equal(await receipt.documentDate, await receipt.effectiveDate);

                const documentID = receipt._id;

                // rollbackCache function will flush deferred actions and saves
                await Test.rollbackCache(context);

                receipt = await context.read(xtremStock.nodes.StockReceipt, { _id: documentID });

                await testLib.functions.TestHelpers.processStockUpdate(
                    context,
                    receipt,
                    'receive',
                    { movementType: 'receipt' },
                    xtremStock.nodes.StockReceipt.onStockReply,
                    [
                        { stockUpdateResultStatus: 'increased', stockTransactionStatus: 'succeeded' },
                        { stockUpdateResultStatus: 'increased', stockTransactionStatus: 'succeeded' },
                    ],
                );

                const newStockQuantityForLine1 = (
                    await (
                        await xtremStockData.functions.stockLib.getStockRecord(context, stockSearchDataForLine1)
                    )?.quantityInStockUnit
                )?.valueOf();

                const newStockQuantityForLine2 = (
                    await (
                        await xtremStockData.functions.stockLib.getStockRecord(context, stockSearchDataForLine2)
                    )?.quantityInStockUnit
                )?.valueOf();

                const lastProducedStockMovement = await getLastStockJournal(context, stockJournalSearchData);

                const receiptLine1 = await receipt.lines.elementAt(0);
                const receiptLine2 = await receipt.lines.elementAt(1);

                assert.equal(await receipt.displayStatus, 'detailsEntered');

                // Stock update parameter testing
                assert.equal(await (await receiptLine1.document).number, 'TEST');
                assert.deepEqual(await (await receiptLine1.document).effectiveDate, date.today());
                assert.equal(await receiptLine1.quantityInStockUnit, 15);
                assert.instanceOf(receipt, xtremStock.nodes.StockReceipt);
                assert.equal(await (await receiptLine1.item).id, 'LOT_TEST1');
                assert.equal(await (await receiptLine1.site).id, await (await receipt.stockSite).id);

                // Stock update parameter testing
                assert.equal(await (await receiptLine2.document).number, 'TEST');
                assert.deepEqual(await (await receiptLine2.document).effectiveDate, date.today());
                assert.equal(await receiptLine2.quantityInStockUnit, 15);
                assert.equal(await (await receiptLine2.item).id, 'LOT_TEST1');
                assert.equal(await (await receiptLine2.site).id, await (await receipt.stockSite).id);

                assert.equal(
                    (newStockQuantityForLine1 ? +newStockQuantityForLine1 : 0).valueOf(),
                    (
                        (previousStockQuantityForLine1 ? +previousStockQuantityForLine1 : 0) +
                        (await receiptLine1.quantityInStockUnit)
                    ).valueOf(),
                );
                assert.equal(
                    (newStockQuantityForLine2 ? +newStockQuantityForLine2 : 0).valueOf(),
                    (
                        (previousStockQuantityForLine2 ? +previousStockQuantityForLine2 : 0) +
                        (await receiptLine1.quantityInStockUnit)
                    ).valueOf(),
                );
                assert.equal(await lastProducedStockMovement?.sequence, 99998);
                assert.equal((await lastProducedStockMovement?.documentLine)?._id, receiptLine2._id);
            },
            { testActiveServiceOptions: [xtremMasterData.serviceOptions.serialNumberOption] },
        ));

    it('Create test with defaulted and given order cost', () =>
        Test.withContext(
            async context => {
                const site = await context.read(xtremSystem.nodes.Site, { id: 'US002' });
                const item = await context.read(xtremMasterData.nodes.Item, { id: 'Milk' });
                const stockSearchData: xtremStockData.interfaces.StockSearchData = {
                    item: item._id,
                    location: (await context.read(xtremMasterData.nodes.Location, { id: 'LOC4' }))._id,
                    lot: null,
                    status: (await context.read(xtremStockData.nodes.StockStatus, { id: 'A' }))._id,
                    stockUnit: (await item.stockUnit)._id,
                    site: site._id,
                    owner: await site.id,
                };
                const itemSiteCurrentCost = 1.12;
                const receiptLineOrderCost = 1.2;
                const itemSite = await context.read(xtremMasterData.nodes.ItemSite, { site: site._id, item: item._id });
                assert.equal(await itemSite.valuationMethod, 'standardCost');
                assert.equal(await itemSite.stdCostValue, itemSiteCurrentCost.valueOf());

                let receipt = await context.create(xtremStock.nodes.StockReceipt, {
                    number: 'TEST',
                    effectiveDate: date.today(),
                    stockSite: site,
                    lines: [
                        {
                            _sortValue: 10,
                            item: stockSearchData.item,
                            stockStatus: stockSearchData.status,
                            quantityInStockUnit: 15,
                            stockDetails: [
                                testLib.functions.TestHelpers.stockSearchDataToDetailCreateData(stockSearchData, {
                                    quantityInStockUnit: 10,
                                }),
                            ],
                        },
                        {
                            _sortValue: 20,
                            item: stockSearchData.item,
                            stockStatus: stockSearchData.status,
                            quantityInStockUnit: 11,
                            orderCost: receiptLineOrderCost,
                            stockDetails: [
                                testLib.functions.TestHelpers.stockSearchDataToDetailCreateData(stockSearchData, {
                                    quantityInStockUnit: 11,
                                }),
                            ],
                        },
                    ],
                });

                await receipt.$.save();

                assert.instanceOf(receipt, xtremStock.nodes.StockReceipt);

                const documentID = receipt._id;

                // rollbackCache function will flush deferred actions and saves
                await Test.rollbackCache(context);

                receipt = await context.read(xtremStock.nodes.StockReceipt, { _id: documentID });

                await testLib.functions.TestHelpers.processStockUpdate(
                    context,
                    receipt,
                    'receive',
                    { movementType: 'receipt' },
                    xtremStock.nodes.StockReceipt.onStockReply,
                    [
                        { stockUpdateResultStatus: 'increased', stockTransactionStatus: 'succeeded' },
                        { stockUpdateResultStatus: 'increased', stockTransactionStatus: 'succeeded' },
                    ],
                );

                // 1st line with defaulted order cost
                let receiptLine = await receipt.lines.elementAt(0);
                let expectedOrderCost = itemSiteCurrentCost.valueOf();

                assert.equal(await receiptLine.orderCost, expectedOrderCost);
                let producedStockMovement =
                    (await context
                        .query(xtremStockData.nodes.StockJournal, {
                            filter: {
                                stockDetail: { documentLine: receiptLine._id },
                            },
                        })
                        .at(0)) || null;

                assert.equal(await producedStockMovement?.orderCost, expectedOrderCost);

                // 2nd line with given order cost
                receiptLine = await receipt.lines.elementAt(1);
                expectedOrderCost = receiptLineOrderCost.valueOf();

                assert.equal(await receiptLine.orderCost, expectedOrderCost);
                producedStockMovement =
                    (await context
                        .query(xtremStockData.nodes.StockJournal, {
                            filter: {
                                stockDetail: { documentLine: receiptLine._id },
                            },
                        })
                        .at(0)) || null;
                assert.equal(await producedStockMovement?.orderCost, expectedOrderCost);
            },
            { testActiveServiceOptions: [xtremMasterData.serviceOptions.serialNumberOption] },
        ));

    it('cannot delete a dimension/attribute type or value that is in use', () =>
        Test.withContext(async context => {
            const receiptLine = await context.read(xtremStock.nodes.StockReceiptLine, { _id: '1110' });
            assert.deepEqual(await receiptLine.storedDimensions, {
                dimensionType03: 'DIMTYPE1VALUE1',
                dimensionType04: 'DIMTYPE2VALUE2',
            });
            await asyncArray(Object.keys((await receiptLine.storedDimensions) || {})).forEach(async type => {
                const typeNode = await context.read(
                    xtremFinanceData.nodes.DimensionType,
                    { docProperty: type as xtremFinanceData.enums.DocProperty },
                    { forUpdate: true },
                );

                await assert.isRejected(typeNode.$.delete(), 'The record was not deleted.');
                assert.deepEqual(context.diagnoses, [
                    { message: 'Cannot delete dimension type, it is already in use.', severity: 3, path: [] },
                ]);

                const valueNode = await context.read(
                    xtremFinanceData.nodes.Dimension,
                    { id: ((await receiptLine.storedDimensions) as any)[type] },
                    { forUpdate: true },
                );

                await assert.isRejected(valueNode.$.delete(), 'The record was not deleted.');
                assert.deepEqual(context.diagnoses, [
                    { message: 'Cannot delete dimension, it is already in use.', severity: 3, path: [] },
                ]);
            });

            assert.deepEqual(await receiptLine.storedAttributes, { project: 'AttPROJ' } as any);
            await asyncArray(Object.keys((await receiptLine.storedAttributes) || {})).forEach(async type => {
                const typeNode = await context.read(
                    xtremFinanceData.nodes.AttributeType,
                    { id: type },
                    { forUpdate: true },
                );

                await assert.isRejected(typeNode.$.delete(), 'The record was not deleted.');
                assert.deepEqual(context.diagnoses, [
                    { message: 'Cannot delete attribute type, it is already in use.', severity: 3, path: [] },
                ]);

                const valueNode = await context.read(
                    xtremFinanceData.nodes.Attribute,
                    { id: ((await receiptLine.storedAttributes) as any)[type] },
                    { forUpdate: true },
                );

                await assert.isRejected(valueNode.$.delete(), 'The record was not deleted.');
                assert.deepEqual(context.diagnoses, [
                    { message: 'Cannot delete attribute, it is already in use.', severity: 3, path: [] },
                ]);
            });
        }));

    it('Create receipt with lot generation by sequence counter', () =>
        Test.withContext(
            async context => {
                const site = await context.read(xtremSystem.nodes.Site, { id: 'US001' });
                const item = await context.read(xtremMasterData.nodes.Item, { id: 'LOT_TEST1' });
                const stockSearchData: xtremStockData.interfaces.StockSearchData = {
                    item: item._id,
                    location: (await context.read(xtremMasterData.nodes.Location, { id: 'LOC3' }))._id,
                    lot: null,
                    status: (await context.read(xtremStockData.nodes.StockStatus, { id: 'A' }))._id,
                    stockUnit: (await item.stockUnit)._id,
                    site: site._id,
                    owner: await site.id,
                };

                let receipt = await context.create(xtremStock.nodes.StockReceipt, {
                    effectiveDate: date.today(),
                    stockSite: site,
                    lines: [
                        {
                            _sortValue: 10,
                            item: stockSearchData.item,
                            stockStatus: stockSearchData.status,
                            quantityInStockUnit: 15,
                            stockDetails: [
                                testLib.functions.TestHelpers.stockSearchDataToDetailCreateData<xtremStockData.nodes.StockReceiptDetail>(
                                    stockSearchData,
                                    {
                                        quantityInStockUnit: 15,
                                        movementType: 'receipt',
                                        stockDetailLot: {
                                            _id: -1,
                                            supplierLot: 'SLOT001',
                                            expirationDate: date.make(2023, 2, 28),
                                        },
                                    },
                                ),
                            ],
                        },
                    ],
                });

                const stockJournalSearchData = {
                    item: item._id,
                    site: site._id,
                    effectiveDate: await receipt.effectiveDate,
                };

                await receipt.$.save();

                const documentID = receipt._id;

                // rollbackCache function will flush deferred actions and saves
                await Test.rollbackCache(context);

                receipt = await context.read(xtremStock.nodes.StockReceipt, { _id: documentID });
                const firstReceiptLine = await receipt.lines.elementAt(0);

                await testLib.functions.TestHelpers.processStockUpdate(
                    context,
                    receipt,
                    'receive',
                    { movementType: 'receipt' },
                    xtremStock.nodes.StockReceipt.onStockReply,
                    [{ stockUpdateResultStatus: 'increased', stockTransactionStatus: 'succeeded' }],
                );

                const producedStockMovement = await getLastStockJournal(context, stockJournalSearchData);

                assert.instanceOf(receipt, xtremStock.nodes.StockReceipt);
                assert.isNotNull(await producedStockMovement?.lot);
                assert.equal((await producedStockMovement?.documentLine)?._id, firstReceiptLine._id);
            },
            { testActiveServiceOptions: [xtremMasterData.serviceOptions.serialNumberOption], today: '2020-05-12' },
        ));

    it('Create receipt with lot generation fail when no sequence counter is defined', () =>
        Test.withContext(async context => {
            const site = await context.read(xtremSystem.nodes.Site, { id: 'US001' });
            const item = await context.read(xtremMasterData.nodes.Item, { id: 'LOT_TEST' });
            const stockSearchData: xtremStockData.interfaces.StockSearchData = {
                item: item._id,
                location: (await context.read(xtremMasterData.nodes.Location, { id: 'LOC3' }))._id,
                lot: null,
                status: (await context.read(xtremStockData.nodes.StockStatus, { id: 'A' }))._id,
                stockUnit: (await item.stockUnit)._id,
                site: site._id,
                owner: await site.id,
            };

            const receipt = await context.create(xtremStock.nodes.StockReceipt, {
                effectiveDate: date.today(),
                stockSite: site,
                lines: [
                    {
                        _sortValue: 10,
                        item: stockSearchData.item,
                        stockStatus: stockSearchData.status,
                        quantityInStockUnit: 15,
                        stockDetails: [
                            testLib.functions.TestHelpers.stockSearchDataToDetailCreateData<xtremStockData.nodes.StockReceiptDetail>(
                                stockSearchData,
                                {
                                    quantityInStockUnit: 15,
                                    movementType: 'receipt',
                                    stockDetailLot: {
                                        _id: -1,
                                        supplierLot: 'SLOT001',
                                        expirationDate: date.make(2023, 2, 28),
                                    },
                                },
                            ),
                        ],
                    },
                ],
            });

            await assert.isRejected(receipt.$.save(), 'The record was not created.');
        }));

    it('Create receipt with multiple (2) lines with costs', () =>
        Test.withContext(
            async context => {
                const site = await context.read(xtremSystem.nodes.Site, { id: 'US001' });
                const itemForLine1 = await context.read(xtremMasterData.nodes.Item, { id: 'LOT_TEST1' });
                const itemForLine2 = await context.read(xtremMasterData.nodes.Item, { id: 'LOT_TEST' });
                const stockSearchDataForLine1: xtremStockData.interfaces.StockSearchData = {
                    item: itemForLine1._id,
                    location: (await context.read(xtremMasterData.nodes.Location, { id: 'LOC3' }))._id,
                    lot: (await context.read(xtremStockData.nodes.Lot, { id: 'MISCRECEIPT1', item: itemForLine1 }))._id,
                    status: (await context.read(xtremStockData.nodes.StockStatus, { id: 'A' }))._id,
                    stockUnit: (await itemForLine1.stockUnit)._id,
                    site: site._id,
                    owner: await site.id,
                };
                const stockSearchDataForLine2: xtremStockData.interfaces.StockSearchData = {
                    ...stockSearchDataForLine1,
                    item: itemForLine2._id,
                    location: (
                        await context.read(xtremMasterData.nodes.Location, {
                            id: 'LOC1',
                            locationZone: '#US001|Loading dock',
                        })
                    )._id,
                    lot: (await context.read(xtremStockData.nodes.Lot, { id: 'TESTLOT00001', item: itemForLine2 }))._id,
                    stockUnit: (await itemForLine2.stockUnit)._id,
                };

                const previousStockQuantityForLine1 = (
                    await (
                        await xtremStockData.functions.stockLib.getStockRecord(context, stockSearchDataForLine1)
                    )?.quantityInStockUnit
                )?.valueOf();

                let receipt = await context.create(xtremStock.nodes.StockReceipt, {
                    effectiveDate: date.today(),
                    stockSite: site,
                    lines: [
                        {
                            item: itemForLine1,
                            stockStatus: stockSearchDataForLine1.status,
                            quantityInStockUnit: 15,
                            stockDetails: [
                                testLib.functions.TestHelpers.stockSearchDataToDetailCreateData(
                                    stockSearchDataForLine1,
                                    {
                                        quantityInStockUnit: 15,
                                        movementType: 'receipt',
                                        stockDetailLot: {
                                            lot: await context.read(xtremStockData.nodes.Lot, {
                                                id: 'MISCRECEIPT1',
                                                sublot: '',
                                                item: itemForLine1,
                                            }),
                                        },
                                    },
                                ),
                            ],
                        },
                        {
                            item: itemForLine2,
                            stockStatus: stockSearchDataForLine2.status,
                            quantityInStockUnit: 15,
                            orderCost: 5,
                            valuedCost: 6,
                            stockDetails: [
                                testLib.functions.TestHelpers.stockSearchDataToDetailCreateData(
                                    stockSearchDataForLine2,
                                    {
                                        quantityInStockUnit: 15,
                                        movementType: 'receipt',
                                        stockDetailLot: {
                                            lot: await context.read(xtremStockData.nodes.Lot, {
                                                id: 'TESTLOT00001',
                                                sublot: '',
                                                item: itemForLine2,
                                            }),
                                        },
                                    },
                                ),
                            ],
                        },
                    ],
                });

                const stockJournalSearchData = {
                    item: itemForLine1._id,
                    site: site._id,
                    effectiveDate: await receipt.effectiveDate,
                };

                await receipt.$.save({ flushDeferredActions: true });

                const documentID = receipt._id;

                // rollbackCache function will flush deferred actions and saves
                await Test.rollbackCache(context);

                receipt = await context.read(xtremStock.nodes.StockReceipt, { _id: documentID });

                await testLib.functions.TestHelpers.processStockUpdate(
                    context,
                    receipt,
                    'receive',
                    { movementType: 'receipt' },
                    xtremStock.nodes.StockReceipt.onStockReply,
                    [
                        { stockUpdateResultStatus: 'increased', stockTransactionStatus: 'succeeded' },
                        { stockUpdateResultStatus: 'increased', stockTransactionStatus: 'succeeded' },
                    ],
                );

                const newStockQuantityForLine1 = (
                    await (await xtremStockData.functions.stockLib.getStockRecord(context, stockSearchDataForLine1))!
                        .quantityInStockUnit
                ).valueOf();

                const newStockQuantityForLine2 = (
                    await (await xtremStockData.functions.stockLib.getStockRecord(context, stockSearchDataForLine2))!
                        .quantityInStockUnit
                ).valueOf();

                const producedStockMovementForLine1 = await getLastStockJournal(context, stockJournalSearchData);

                const receiptLine1 = await receipt.lines.elementAt(0);
                const receiptLine2 = await receipt.lines.elementAt(1);

                // Stock update parameter testing
                assert.instanceOf(receipt, xtremStock.nodes.StockReceipt);
                assert.equal(await receipt.number, 'SR210001');
                assert.deepEqual(await receipt.effectiveDate, date.today());
                assert.equal(await receipt.displayStatus, 'detailsEntered');

                assert.equal(await (await receiptLine1.item).id, 'LOT_TEST1');
                assert.equal((await receiptLine1.quantityInStockUnit).valueOf(), 15);
                assert.equal((await receiptLine1.orderCost).valueOf(), 0);
                assert.equal((await receiptLine1.valuedCost).valueOf(), 0);

                assert.equal(await (await receiptLine2.item).id, 'LOT_TEST');
                assert.equal(await receiptLine2.quantityInStockUnit, 15);
                assert.equal(await (await receiptLine2.item).id, 'LOT_TEST');
                assert.equal((await receiptLine2.orderCost).valueOf(), 5);
                assert.equal((await receiptLine2.valuedCost).valueOf(), 6);

                assert.equal(
                    (newStockQuantityForLine1 ? +newStockQuantityForLine1 : 0).valueOf(),
                    (
                        (previousStockQuantityForLine1 ? +previousStockQuantityForLine1 : 0) +
                        (await receiptLine1.quantityInStockUnit)
                    ).valueOf(),
                );
                assert.equal(newStockQuantityForLine2, await receiptLine2.quantityInStockUnit);
                assert.equal(await producedStockMovementForLine1?.sequence, 99999);
                assert.equal((await producedStockMovementForLine1?.documentLine)?._id, receiptLine1._id);
            },
            { today: '2021-01-31', testActiveServiceOptions: [xtremMasterData.serviceOptions.serialNumberOption] },
        ));

    it('Create receipt with multiple (2) lines with stock details captured on line 1 and stock details not captured on line 2.  Check display status', () =>
        Test.withContext(
            async context => {
                const site = await context.read(xtremSystem.nodes.Site, { id: 'US001' });
                const itemForLine1 = await context.read(xtremMasterData.nodes.Item, { id: 'LOT_TEST1' });
                const itemForLine2 = await context.read(xtremMasterData.nodes.Item, { id: 'LOT_TEST' });
                const stockSearchDataForLine1: xtremStockData.interfaces.StockSearchData = {
                    item: itemForLine1._id,
                    location: (await context.read(xtremMasterData.nodes.Location, { id: 'LOC3' }))._id,
                    lot: (await context.read(xtremStockData.nodes.Lot, { id: 'MISCRECEIPT1', item: itemForLine1 }))._id,
                    status: (await context.read(xtremStockData.nodes.StockStatus, { id: 'A' }))._id,
                    stockUnit: (await itemForLine1.stockUnit)._id,
                    site: site._id,
                    owner: await site.id,
                };
                const stockSearchDataForLine2: xtremStockData.interfaces.StockSearchData = {
                    ...stockSearchDataForLine1,
                    item: itemForLine2._id,
                    location: (
                        await context.read(xtremMasterData.nodes.Location, {
                            id: 'LOC1',
                            locationZone: '#US001|Loading dock',
                        })
                    )._id,
                    lot: (await context.read(xtremStockData.nodes.Lot, { id: 'TESTLOT00001', item: itemForLine2 }))._id,
                    stockUnit: (await itemForLine2.stockUnit)._id,
                };

                const receipt = await context.create(xtremStock.nodes.StockReceipt, {
                    effectiveDate: date.today(),
                    stockSite: site,
                    lines: [
                        {
                            item: itemForLine1,
                            stockStatus: stockSearchDataForLine1.status,
                            quantityInStockUnit: 15,
                            stockDetails: [
                                testLib.functions.TestHelpers.stockSearchDataToDetailCreateData(
                                    stockSearchDataForLine1,
                                    {
                                        quantityInStockUnit: 15,
                                        movementType: 'receipt',
                                        stockDetailLot: {
                                            lot: await context.read(xtremStockData.nodes.Lot, {
                                                id: 'MISCRECEIPT1',
                                                sublot: '',
                                                item: itemForLine1,
                                            }),
                                        },
                                    },
                                ),
                            ],
                        },
                        {
                            item: itemForLine2,
                            stockStatus: stockSearchDataForLine2.status,
                            quantityInStockUnit: 10,
                            orderCost: 5,
                            valuedCost: 6,
                        },
                    ],
                });

                await receipt.$.save({ flushDeferredActions: true });

                assert.equal(await receipt.displayStatus, 'detailsRequired');

                let receiptLine = await receipt.lines.elementAt(1);

                await receiptLine.$.set({
                    stockDetails: [
                        testLib.functions.TestHelpers.stockSearchDataToDetailCreateData(stockSearchDataForLine2, {
                            quantityInStockUnit: 10,
                            movementType: 'receipt',
                            stockDetailLot: {
                                lot: await context.read(xtremStockData.nodes.Lot, {
                                    id: 'TESTLOT00001',
                                    sublot: '',
                                    item: itemForLine2,
                                }),
                            },
                        }),
                    ],
                });

                await receipt.$.save();

                assert.equal(await receipt.displayStatus, 'detailsEntered');

                receiptLine = await receipt.lines.elementAt(0);

                await receiptLine.$.set({ stockDetails: [] });

                await receipt.$.save();

                assert.equal(await receipt.displayStatus, 'detailsRequired');
            },
            { today: '2021-01-31' },
        ));

    it('Create receipt fail with a not active dimension for a line', () =>
        Test.withContext(async context => {
            const site = await context.read(xtremSystem.nodes.Site, { id: 'US001' });
            const item = await context.read(xtremMasterData.nodes.Item, { id: 'LOT_TEST1' });
            const stockSearchData: xtremStockData.interfaces.StockSearchData = {
                item: item._id,
                location: (await context.read(xtremMasterData.nodes.Location, { id: 'LOC3' }))._id,
                lot: (await context.read(xtremStockData.nodes.Lot, { id: 'MISCRECEIPT1', item }))._id,
                status: (await context.read(xtremStockData.nodes.StockStatus, { id: 'A' }))._id,
                stockUnit: (await item.stockUnit)._id,
                site: site._id,
                owner: await site.id,
            };

            const receipt = await context.create(xtremStock.nodes.StockReceipt, {
                effectiveDate: date.today(),
                stockSite: site,
                lines: [
                    {
                        _sortValue: 10,
                        item: stockSearchData.item,
                        stockStatus: stockSearchData.status,
                        quantityInStockUnit: 15,
                        storedDimensions: { dimensionType04: 'DIMTYPE2VALUE1' },
                    },
                ],
            });

            await assert.isRejected(receipt.$.save());
            assert.deepEqual(receipt.$.context.diagnoses, [
                {
                    severity: 3,
                    path: ['lines', '-1000000002', 'storedDimensions'],
                    message: 'The DIMTYPE2VALUE1 dimension or "dimensionType04" dimension type is inactive.',
                },
                {
                    message: 'The record cannot be referenced because it is inactive.',
                    path: ['lines', '-1000000002', 'analyticalData', 'dimension04'],
                    severity: 3,
                },
            ]);
        }));

    it('Verify chronological control between two stock receipts on creation', () =>
        withSequenceNumberContext(
            'MiscStockReceipt',
            { isChronological: true },
            async context => {
                const site = await context.read(xtremSystem.nodes.Site, { id: 'US001' });
                const item = await context.read(xtremMasterData.nodes.Item, { id: 'LOT_TEST1' });
                const stockSearchData: xtremStockData.interfaces.StockSearchData = {
                    item: item._id,
                    location: (await context.read(xtremMasterData.nodes.Location, { id: 'LOC3' }))._id,
                    lot: (await context.read(xtremStockData.nodes.Lot, { id: 'MISCRECEIPT1', item }))._id,
                    status: (await context.read(xtremStockData.nodes.StockStatus, { id: 'A' }))._id,
                    stockUnit: (await item.stockUnit)._id,
                    site: site._id,
                    owner: await site.id,
                };

                const newStockReceipt = await context.create(xtremStock.nodes.StockReceipt, {
                    effectiveDate: date.make(2020, 7, 12),
                    stockSite: site,
                    lines: [
                        {
                            item: stockSearchData.item,
                            stockStatus: stockSearchData.status,
                            quantityInStockUnit: 15,
                            stockDetails: [
                                testLib.functions.TestHelpers.stockSearchDataToDetailCreateData(stockSearchData, {
                                    quantityInStockUnit: 15,
                                }),
                            ],
                        },
                    ],
                });

                await newStockReceipt.$.save({ flushDeferredActions: true });
                assert.deepEqual(newStockReceipt.$.context.diagnoses, []);

                const stockReceiptError = await context.create(xtremStock.nodes.StockReceipt, {
                    effectiveDate: date.make(2020, 7, 11),
                    stockSite: site,
                    lines: [
                        {
                            item: stockSearchData.item,
                            stockStatus: stockSearchData.status,
                            quantityInStockUnit: 15,
                            stockDetails: [
                                testLib.functions.TestHelpers.stockSearchDataToDetailCreateData(stockSearchData, {
                                    quantityInStockUnit: 15,
                                }),
                            ],
                        },
                    ],
                });
                await assert.isRejected(
                    stockReceiptError.$.save(),
                    'The document date 2020-07-11 is earlier than the previous document date 2020-07-12.',
                );
            },
            { today: '2020-05-12', testActiveServiceOptions: [xtremMasterData.serviceOptions.serialNumberOption] },
        ));

    it('Serial numbers uniqueness check', () =>
        Test.withContext(
            async context => {
                const item = await context.read(xtremMasterData.nodes.Item, {
                    id: 'Juice',
                });
                let serialNumbers = 'SSN0002 DEF SSN0003 JKL LKU ZHU TFD ';
                let serialNumberArray =
                    xtremStockData.sharedFunctions.serialNumberHelper.createSerialNumberArray(serialNumbers);
                let checkResult = await xtremStockData.nodes.SerialNumber.uniquenessCheck(
                    context,
                    serialNumberArray,
                    item,
                    true,
                    true,
                );
                assert.equal(checkResult, 'SSN0002');

                serialNumbers = 'A00 A01 A02 A03 A04 A05 A06 A07 A08 A09 SSN0002 ';
                serialNumbers = `${serialNumbers} A10 A11 A12 A13 A14 A15 A16 A17 A18 A19`;
                serialNumbers = `${serialNumbers} A20 A21 A22 A23 A24 A25 A26 A27 A28 A29`;
                serialNumbers = `${serialNumbers} A30 A31 A32 A33 A34 A35 A36 A37 A38 A39`;
                serialNumbers = `${serialNumbers} A40 A41 A42 A43 A44 A45 A46 A47 A48 A49`;
                serialNumbers = `${serialNumbers} A50 A51 A52 A53 A54 A55 A56 A57 A58 A59`;
                serialNumbers = `${serialNumbers} A60 A61 A62 A63 A64 A65 A66 A67 A68 A69`;
                serialNumbers = `${serialNumbers} A70 A71 A72 A73 A74 A75 A76 A77 A78 A79`;
                serialNumbers = `${serialNumbers} A80 A81 A82 A83 A84 A85 A86 A87 A88 A89`;
                serialNumbers = `${serialNumbers} A90 A91 A92 A93 A94 A95 A96 A97 A98 A99`;
                serialNumbers = `${serialNumbers} B00 B01 B02 B03 B04 B05 B06 B07 B08 B09`;
                serialNumbers = `${serialNumbers} B10 B11 B12 B13 B14 B15 B16 B17 B18 B19`;
                serialNumbers = `${serialNumbers} B20 B21 B22 B23 B24 B25 B26 B27 B28 B29`;
                serialNumbers = `${serialNumbers} B30 B31 B32 B33 B34 B35 B36 B37 B38 B39`;
                serialNumbers = `${serialNumbers} B40 B41 B42 B43 B44 B45 B46 B47 B48 B49`;
                serialNumbers = `${serialNumbers} B50 B51 B52 B53 B54 B55 B56 B57 B58 B59`;
                serialNumbers = `${serialNumbers} B60 B61 B62 B63 B64 B65 B66 B67 B68 B69`;
                serialNumbers = `${serialNumbers} B70 B71 B72 B73 B74 B75 B76 B77 B78 B79`;
                serialNumbers = `${serialNumbers} B80 B81 B82 B83 B84 B85 B86 B87 B88 B89`;
                serialNumbers = `${serialNumbers} B90 B91 B92 B93 B94 B95 B96 B97 B98 B99`;
                serialNumbers = `${serialNumbers} C00 C01 C02 C03 C04 C05 C06 C07 C08 C09`;
                serialNumbers = `${serialNumbers} C10 C11 C12 C13 C14 C15 C16 C17 C18 C19`;
                serialNumbers = `${serialNumbers} C20 C21 C22 C23 C24 C25 C26 C27 C28 C29`;
                serialNumbers = `${serialNumbers} C30 C31 C32 C33 C34 C35 C36 C37 C38 C39`;
                serialNumbers = `${serialNumbers} C40 C41 C42 C43 C44 C45 C46 C47 C48 C49`;
                serialNumbers = `${serialNumbers} C50 C51 C52 C53 C54 C55 C56 C57 C58 C59`;
                serialNumbers = `${serialNumbers} C60 C61 C62 C63 C64 C65 C66 C67 C68 C69`;
                serialNumbers = `${serialNumbers} C70 C71 C72 C73 C74 C75 C76 C77 C78 C79`;
                serialNumbers = `${serialNumbers} C80 C81 C82 C83 C84 C85 C86 C87 C88 C89`;
                serialNumbers = `${serialNumbers} C90 C91 C92 C93 C94 C95 C96 C97 C98 C99`;
                serialNumbers = `${serialNumbers} D00 D01 D02 D03 D04 D05 D06 D07 D08 D09`;
                serialNumbers = `${serialNumbers} D10 D11 D12 D13 D14 D15 D16 D17 D18 D19`;
                serialNumbers = `${serialNumbers} D20 D21 D22 D23 D24 D25 D26 D27 D28 D29`;
                serialNumbers = `${serialNumbers} D30 D31 D32 D33 D34 D35 D36 D37 D38 D39`;
                serialNumbers = `${serialNumbers} D40 D41 D42 D43 D44 D45 D46 D47 D48 D49`;
                serialNumbers = `${serialNumbers} D50 D51 D52 D53 D54 D55 D56 D57 D58 D59`;
                serialNumbers = `${serialNumbers} D60 D61 D62 D63 D64 D65 D66 D67 D68 D69`;
                serialNumbers = `${serialNumbers} D70 D71 D72 D73 D74 D75 D76 D77 D78 D79`;
                serialNumbers = `${serialNumbers} D80 D81 D82 D83 D84 D85 D86 D87 D88 D89`;
                serialNumbers = `${serialNumbers} D90 D91 D92 D93 D94 D95 D96 D97 D98 D99`;
                serialNumbers = `${serialNumbers} E00 E01 E02 E03 E04 E05 E06 E07 E08 E09`;
                serialNumbers = `${serialNumbers} E10 E11 E12 E13 E14 E15 E16 E17 E18 E19`;
                serialNumbers = `${serialNumbers} E20 E21 E22 E23 E24 E25 E26 E27 E28 E29`;
                serialNumbers = `${serialNumbers} E30 E31 E32 E33 E34 E35 E36 E37 E38 E39`;
                serialNumbers = `${serialNumbers} E40 E41 E42 E43 E44 E45 E46 E47 E48 E49`;
                serialNumbers = `${serialNumbers} E50 E51 E52 E53 E54 E55 E56 E57 E58 E59`;
                serialNumbers = `${serialNumbers} E60 E61 E62 E63 E64 E65 E66 E67 E68 E69`;
                serialNumbers = `${serialNumbers} E70 E71 E72 E73 E74 E75 E76 E77 E78 E79`;
                serialNumbers = `${serialNumbers} E80 E81 E82 E83 E84 E85 E86 E87 E88 E89`;
                serialNumbers = `${serialNumbers} E90 E91 E92 E93 E94 E95 E96 E97 E98 E99`;
                serialNumbers = `${serialNumbers} F00 F01 F02 F03 F04 F05 F06 F07 F08 F09`;
                serialNumbers = `${serialNumbers} F10 F11 F12 F13 F14 F15 F16 F17 F18 F19`;
                serialNumbers = `${serialNumbers} F20 F21 F22 F23 F24 F25 F26 F27 F28 F29`;
                serialNumbers = `${serialNumbers} F30 F31 F32 F33 F34 F35 F36 F37 F38 F39`;
                serialNumbers = `${serialNumbers} F40 F41 F42 F43 F44 F45 F46 F47 F48 F49`;
                serialNumbers = `${serialNumbers} F50 F51 F52 F53 F54 F55 F56 F57 F58 F59`;
                serialNumbers = `${serialNumbers} F60 F61 F62 F63 F64 F65 F66 F67 F68 F69`;
                serialNumbers = `${serialNumbers} F70 F71 F72 F73 F74 F75 F76 F77 F78 F79`;
                serialNumbers = `${serialNumbers} F80 F81 F82 F83 F84 F85 F86 F87 F88 F89`;
                serialNumbers = `${serialNumbers} F90 F91 F92 F93 F94 F95 F96 F97 F98 F99`;
                serialNumbers = `${serialNumbers} G00 G01 G02 G03 G04 G05 G06 G07 G08 G09`;
                serialNumbers = `${serialNumbers} G10 G11 G12 G13 G14 G15 G16 G17 G18 G19`;
                serialNumbers = `${serialNumbers} G20 G21 G22 G23 G24 G25 G26 G27 G28 G29`;
                serialNumbers = `${serialNumbers} G30 G31 G32 G33 G34 G35 G36 G37 G38 G39`;
                serialNumbers = `${serialNumbers} G40 G41 G42 G43 G44 G45 G46 G47 G48 G49`;
                serialNumbers = `${serialNumbers} G50 G51 G52 G53 G54 G55 G56 G57 G58 G59`;
                serialNumbers = `${serialNumbers} G60 G61 G62 G63 G64 G65 G66 G67 G68 G69`;
                serialNumbers = `${serialNumbers} G70 G71 G72 G73 G74 G75 G76 G77 G78 G79`;
                serialNumbers = `${serialNumbers} G80 G81 G82 G83 G84 G85 G86 G87 G88 G89`;
                serialNumbers = `${serialNumbers} G90 G91 G92 G93 G94 G95 G96 G97 G98 G99`;
                serialNumbers = `${serialNumbers} H00 H01 H02 H03 H04 H05 H06 H07 H08 H09`;
                serialNumbers = `${serialNumbers} H10 H11 H12 H13 H14 H15 H16 H17 H18 H19`;
                serialNumbers = `${serialNumbers} H20 H21 H22 H23 H24 H25 H26 H27 H28 H29`;
                serialNumbers = `${serialNumbers} H30 H31 H32 H33 H34 H35 H36 H37 H38 H39`;
                serialNumbers = `${serialNumbers} H40 H41 H42 H43 H44 H45 H46 H47 H48 H49`;
                serialNumbers = `${serialNumbers} H50 H51 H52 H53 H54 H55 H56 H57 H58 H59`;
                serialNumbers = `${serialNumbers} H60 H61 H62 H63 H64 H65 H66 H67 H68 H69`;
                serialNumbers = `${serialNumbers} H70 H71 H72 H73 H74 H75 H76 H77 H78 H79`;
                serialNumbers = `${serialNumbers} H80 H81 H82 H83 H84 H85 H86 H87 H88 H89`;
                serialNumbers = `${serialNumbers} H90 H91 H92 H93 H94 H95 H96 H97 H98 H99`;
                serialNumbers = `${serialNumbers} I00 I01 I02 I03 I04 I05 I06 I07 I08 I09`;
                serialNumbers = `${serialNumbers} I10 I11 I12 I13 I14 I15 I16 I17 I18 I19`;
                serialNumbers = `${serialNumbers} I20 I21 I22 I23 I24 I25 I26 I27 I28 I29`;
                serialNumbers = `${serialNumbers} I30 I31 I32 I33 I34 I35 I36 I37 I38 I39`;
                serialNumbers = `${serialNumbers} I40 I41 I42 I43 I44 I45 I46 I47 I48 I49`;
                serialNumbers = `${serialNumbers} I50 I51 I52 I53 I54 I55 I56 I57 I58 I59`;
                serialNumbers = `${serialNumbers} I60 I61 I62 I63 I64 I65 I66 I67 I68 I69`;
                serialNumbers = `${serialNumbers} I70 I71 I72 I73 I74 I75 I76 I77 I78 I79`;
                serialNumbers = `${serialNumbers} I80 I81 I82 I83 I84 I85 I86 I87 I88 I89`;
                serialNumbers = `${serialNumbers} I90 I91 I92 I93 I94 I95 I96 I97 I98 I99`;
                serialNumbers = `${serialNumbers} J00 J01 J02 J03 J04 J05 J06 J07 J08 J09`;
                serialNumbers = `${serialNumbers} J10 J11 J12 J13 J14 J15 J16 J17 J18 J19`;
                serialNumbers = `${serialNumbers} J20 J21 J22 J23 J24 J25 J26 J27 J28 J29`;
                serialNumbers = `${serialNumbers} J30 J31 J32 J33 J34 J35 J36 J37 J38 J39`;
                serialNumbers = `${serialNumbers} J40 J41 J42 J43 J44 J45 J46 J47 J48 J49`;
                serialNumbers = `${serialNumbers} J50 J51 J52 J53 J54 J55 J56 J57 J58 J59`;
                serialNumbers = `${serialNumbers} J60 J61 J62 J63 J64 J65 J66 J67 J68 J69`;
                serialNumbers = `${serialNumbers} J70 J71 J72 J73 J74 J75 J76 J77 J78 J79`;
                serialNumbers = `${serialNumbers} J80 J81 J82 J83 J84 J85 J86 J87 J88 J89`;
                serialNumbers = `${serialNumbers} J90 J91 J92 J93 J94 J95 J96 J97 J98 J99`;
                serialNumbers = `${serialNumbers} K00 K01 K02 K03 K04 K05 K06 K07 K08 K09`;
                serialNumbers = `${serialNumbers} K10 K11 K12 K13 K14 K15 K16 K17 K18 K19`;
                serialNumbers = `${serialNumbers} K20 K21 K22 K23 K24 K25 K26 K27 K28 K29`;
                serialNumbers = `${serialNumbers} K30 K31 K32 K33 K34 K35 K36 K37 K38 K39`;
                serialNumbers = `${serialNumbers} K40 K41 K42 K43 K44 K45 K46 K47 K48 K49`;
                serialNumbers = `${serialNumbers} K50 K51 K52 K53 K54 K55 K56 K57 K58 K59`;
                serialNumbers = `${serialNumbers} K60 K61 K62 K63 K64 K65 K66 K67 K68 K69`;
                serialNumbers = `${serialNumbers} K70 K71 K72 K73 K74 K75 K76 K77 K78 K79`;
                serialNumbers = `${serialNumbers} K80 K81 K82 K83 K84 K85 K86 K87 K88 K89`;
                serialNumbers = `${serialNumbers} K90 K91 K92 K93 K94 K95 K96 K97 K98 K99`;
                serialNumbers = `${serialNumbers} L00 L01 L02 L03 L04 L05 L06 L07 L08 L09`;
                serialNumbers = `${serialNumbers} L10 L11 L12 L13 L14 L15 L16 L17 L18 L19`;
                serialNumbers = `${serialNumbers} L20 L21 L22 L23 L24 L25 L26 L27 L28 L29`;
                serialNumbers = `${serialNumbers} L30 L31 L32 L33 L34 L35 L36 L37 L38 L39`;
                serialNumbers = `${serialNumbers} L40 L41 L42 L43 L44 L45 L46 L47 L48 L49`;
                serialNumbers = `${serialNumbers} L50 L51 L52 L53 L54 L55 L56 L57 L58 L59`;
                serialNumbers = `${serialNumbers} L60 L61 L62 L63 L64 L65 L66 L67 L68 L69`;
                serialNumbers = `${serialNumbers} L70 L71 L72 L73 L74 L75 L76 L77 L78 L79`;
                serialNumbers = `${serialNumbers} L80 L81 L82 L83 L84 L85 L86 L87 L88 L89`;
                serialNumbers = `${serialNumbers} L90 L91 L92 L93 L94 L95 L96 L97 L98 L99`;
                serialNumbers = `${serialNumbers} M00 M01 M02 M03 M04 M05 M06 M07 M08 M09`;
                serialNumbers = `${serialNumbers} M10 M11 M12 M13 M14 M15 M16 M17 M18 M19`;
                serialNumbers = `${serialNumbers} M20 M21 M22 M23 M24 M25 M26 M27 M28 M29`;
                serialNumbers = `${serialNumbers} M30 M31 M32 M33 M34 M35 M36 M37 M38 M39`;
                serialNumbers = `${serialNumbers} M40 M41 M42 M43 M44 M45 M46 M47 M48 M49`;
                serialNumbers = `${serialNumbers} M50 M51 M52 M53 M54 M55 M56 M57 M58 M59`;
                serialNumbers = `${serialNumbers} M60 M61 M62 M63 M64 M65 M66 M67 M68 M69`;
                serialNumbers = `${serialNumbers} M70 M71 M72 M73 M74 M75 M76 M77 M78 M79`;
                serialNumbers = `${serialNumbers} M80 M81 M82 M83 M84 M85 M86 M87 M88 M89`;
                serialNumbers = `${serialNumbers} M90 M91 M92 M93 M94 M95 M96 M97 M98 M99`;
                serialNumbers = `${serialNumbers} N00 N01 N02 N03 N04 N05 N06 N07 N08 N09`;
                serialNumbers = `${serialNumbers} N10 N11 N12 N13 N14 N15 N16 N17 N18 N19`;
                serialNumbers = `${serialNumbers} N20 N21 N22 N23 N24 N25 N26 N27 N28 N29`;
                serialNumbers = `${serialNumbers} N30 N31 N32 N33 N34 N35 N36 N37 N38 N39`;
                serialNumbers = `${serialNumbers} N40 N41 N42 N43 N44 N45 N46 N47 N48 N49`;
                serialNumbers = `${serialNumbers} N50 N51 N52 N53 N54 N55 N56 N57 N58 N59`;
                serialNumbers = `${serialNumbers} N60 N61 N62 N63 N64 N65 N66 N67 N68 N69`;
                serialNumbers = `${serialNumbers} N70 N71 N72 N73 N74 N75 N76 N77 N78 N79`;
                serialNumbers = `${serialNumbers} N80 N81 N82 N83 N84 N85 N86 N87 N88 N89`;
                serialNumbers = `${serialNumbers} N90 N91 N92 N93 N94 N95 N96 N97 N98 N99`;
                serialNumbers = `${serialNumbers} O00 O01 O02 O03 O04 O05 O06 O07 O08 O09`;
                serialNumbers = `${serialNumbers} O10 O11 O12 O13 O14 O15 O16 O17 O18 O19`;
                serialNumbers = `${serialNumbers} O20 O21 O22 O23 O24 O25 O26 O27 O28 O29`;
                serialNumbers = `${serialNumbers} O30 O31 O32 O33 O34 O35 O36 O37 O38 O39`;
                serialNumbers = `${serialNumbers} O40 O41 O42 O43 O44 O45 O46 O47 O48 O49`;
                serialNumbers = `${serialNumbers} O50 O51 O52 O53 O54 O55 O56 O57 O58 O59`;
                serialNumbers = `${serialNumbers} O60 O61 O62 O63 O64 O65 O66 O67 O68 O69`;
                serialNumbers = `${serialNumbers} O70 O71 O72 O73 O74 O75 O76 O77 O78 O79`;
                serialNumbers = `${serialNumbers} O80 O81 O82 O83 O84 O85 O86 O87 O88 O89`;
                serialNumbers = `${serialNumbers} O90 O91 O92 O93 O94 O95 O96 O97 O98 O99`;
                serialNumberArray =
                    xtremStockData.sharedFunctions.serialNumberHelper.createSerialNumberArray(serialNumbers);
                checkResult = await xtremStockData.nodes.SerialNumber.uniquenessCheck(
                    context,
                    serialNumberArray,
                    item,
                    true,
                    true,
                );
                assert.equal(checkResult, 'SSN0002');

                let index;
                for (index = 1; index <= 100; index += 1) {
                    const serialNumber = await context.create(xtremStockData.nodes.SerialNumber, {
                        id: String(index).padStart(5, '0'),
                        item: item._id,
                        site: 1,
                        isUsable: true,
                    });
                    await serialNumber.$.save();
                }

                serialNumbers = 'SSN0002;00001;00010;00099;00100';
                serialNumberArray =
                    xtremStockData.sharedFunctions.serialNumberHelper.createSerialNumberArray(serialNumbers);
                checkResult = await xtremStockData.nodes.SerialNumber.uniquenessCheck(
                    context,
                    serialNumberArray,
                    item,
                    true,
                    false,
                );
                assert.equal(checkResult, '00001, 00010, 00099, 00100');
            },
            { testActiveServiceOptions: [xtremMasterData.serviceOptions.serialNumberOption] },
        ));

    it('Allow receipt with 2 lines and correct serial numbers', () =>
        Test.withContext(
            async context => {
                const site = await context.read(xtremSystem.nodes.Site, { id: 'US001' });
                const item = await context.read(xtremMasterData.nodes.Item, { id: 'ISSUESERIAL' });
                const stockSearchData: xtremStockData.interfaces.StockSearchData = {
                    item: item._id,
                    location: (await context.read(xtremMasterData.nodes.Location, { id: 'LOC3' }))._id,
                    lot: null,
                    status: (await context.read(xtremStockData.nodes.StockStatus, { id: 'A' }))._id,
                    stockUnit: (await item.stockUnit)._id,
                    site: site._id,
                    owner: await site.id,
                };

                const receipt = await context.create(xtremStock.nodes.StockReceipt, {
                    number: 'TESTSERIAL',
                    effectiveDate: date.today(),
                    stockSite: site,
                    lines: [
                        {
                            item: stockSearchData.item,
                            stockStatus: stockSearchData.status,
                            quantityInStockUnit: 1,
                            stockDetails: [
                                testLib.functions.TestHelpers.stockSearchDataToDetailCreateData(stockSearchData, {
                                    quantityInStockUnit: 1,
                                    movementType: 'receipt',
                                    stockDetailSerialNumbers: [{ stockDetail: -1, newSerialNumberId: 'SN01' }],
                                }),
                            ],
                        },
                        {
                            item: stockSearchData.item,
                            stockStatus: stockSearchData.status,
                            quantityInStockUnit: 1,
                            stockDetails: [
                                testLib.functions.TestHelpers.stockSearchDataToDetailCreateData(stockSearchData, {
                                    quantityInStockUnit: 1,
                                    movementType: 'receipt',
                                    stockDetailSerialNumbers: [{ stockDetail: -1, newSerialNumberId: 'SN02' }],
                                }),
                            ],
                        },
                    ],
                });

                await receipt.$.save();
                assert.deepEqual(receipt!.$.context.diagnoses, []);
            },
            { today: '2020-07-12', testActiveServiceOptions: [xtremMasterData.serviceOptions.serialNumberOption] },
        ));

    it('Refuse stock receipt with 1 line but 2 serial numbers', () =>
        Test.withContext(
            async context => {
                const site = await context.read(xtremSystem.nodes.Site, { id: 'US001' });
                const item = await context.read(xtremMasterData.nodes.Item, { id: 'ISSUESERIAL' });
                const stockSearchData: xtremStockData.interfaces.StockSearchData = {
                    item: item._id,
                    location: (await context.read(xtremMasterData.nodes.Location, { id: 'LOC3' }))._id,
                    lot: null,
                    status: (await context.read(xtremStockData.nodes.StockStatus, { id: 'A' }))._id,
                    stockUnit: (await item.stockUnit)._id,
                    site: site._id,
                    owner: await site.id,
                };

                const receipt = await context.create(xtremStock.nodes.StockReceipt, {
                    number: 'TESTSERIAL',
                    effectiveDate: date.today(),
                    stockSite: site,
                    lines: [
                        {
                            item: stockSearchData.item,
                            stockStatus: stockSearchData.status,
                            quantityInStockUnit: 1,
                            stockDetails: [
                                {
                                    ...testLib.functions.TestHelpers.stockSearchDataToDetailCreateData(
                                        stockSearchData,
                                        {
                                            quantityInStockUnit: 1,
                                            movementType: 'receipt',
                                            stockDetailSerialNumbers: [
                                                { stockDetail: -1, newSerialNumberId: 'SN02' },
                                                { stockDetail: -1, newSerialNumberId: 'SN03' },
                                            ],
                                        },
                                    ),
                                },
                            ],
                        },
                    ],
                });

                await assert.isRejected(
                    receipt.$.save(),
                    'The number of serial numbers assigned needs to match the quantity for the line. Remove serial numbers to match the quantity.',
                );
            },
            { today: '2020-07-12', testActiveServiceOptions: [xtremMasterData.serviceOptions.serialNumberOption] },
        ));

    it('Refuse receipt with 2 lines and duplicate serial numbers', () =>
        Test.withContext(
            async context => {
                const site = await context.read(xtremSystem.nodes.Site, { id: 'US001' });
                const item = await context.read(xtremMasterData.nodes.Item, { id: 'ISSUESERIAL' });
                const stockSearchData: xtremStockData.interfaces.StockSearchData = {
                    item: item._id,
                    location: (await context.read(xtremMasterData.nodes.Location, { id: 'LOC3' }))._id,
                    lot: null,
                    status: (await context.read(xtremStockData.nodes.StockStatus, { id: 'A' }))._id,
                    stockUnit: (await item.stockUnit)._id,
                    site: site._id,
                    owner: await site.id,
                };

                const receipt = await context.create(xtremStock.nodes.StockReceipt, {
                    number: 'TESTSERIAL',
                    effectiveDate: date.today(),
                    stockSite: site,
                    lines: [
                        {
                            item: stockSearchData.item,
                            stockStatus: stockSearchData.status,
                            quantityInStockUnit: 1,
                            stockDetails: [
                                testLib.functions.TestHelpers.stockSearchDataToDetailCreateData(stockSearchData, {
                                    quantityInStockUnit: 1,
                                    movementType: 'receipt',
                                    stockDetailSerialNumbers: [{ stockDetail: -1, newSerialNumberId: 'SN02' }],
                                }),
                            ],
                        },
                        {
                            item: stockSearchData.item,
                            stockStatus: stockSearchData.status,
                            quantityInStockUnit: 1,
                            stockDetails: [
                                testLib.functions.TestHelpers.stockSearchDataToDetailCreateData(stockSearchData, {
                                    quantityInStockUnit: 1,
                                    movementType: 'receipt',
                                    stockDetailSerialNumbers: [{ stockDetail: -1, newSerialNumberId: 'SN02' }],
                                }),
                            ],
                        },
                    ],
                });

                await assert.isRejected(receipt.$.save(), 'Serial numbers need to be unique. Remove duplicates: SN02');
            },
            { today: '2020-07-12', testActiveServiceOptions: [xtremMasterData.serviceOptions.serialNumberOption] },
        ));

    it('Refuse stock receipt with serial number already in DB', () =>
        Test.withContext(
            async context => {
                const site = await context.read(xtremSystem.nodes.Site, { id: 'US001' });
                const item = await context.read(xtremMasterData.nodes.Item, { id: 'Juice' });
                const stockSearchData: xtremStockData.interfaces.StockSearchData = {
                    item: item._id,
                    location: (await context.read(xtremMasterData.nodes.Location, { id: 'LOC3' }))._id,
                    lot: null,
                    status: (await context.read(xtremStockData.nodes.StockStatus, { id: 'A' }))._id,
                    stockUnit: (await item.stockUnit)._id,
                    site: site._id,
                    owner: await site.id,
                };

                const receipt = await context.create(xtremStock.nodes.StockReceipt, {
                    number: 'TESTSERIAL',
                    effectiveDate: date.today(),
                    stockSite: site,
                    lines: [
                        {
                            item: stockSearchData.item,
                            stockStatus: stockSearchData.status,
                            quantityInStockUnit: 1,
                            stockDetails: [
                                testLib.functions.TestHelpers.stockSearchDataToDetailCreateData(stockSearchData, {
                                    quantityInStockUnit: 1,
                                    movementType: 'receipt',
                                    stockDetailSerialNumbers: [{ stockDetail: -1, newSerialNumberId: 'SSN0002' }],
                                }),
                            ],
                        },
                    ],
                });

                await assert.isRejected(
                    receipt.$.save(),
                    'Serial numbers need to be unique. Remove duplicates: SSN0002',
                );
            },
            { today: '2020-07-12', testActiveServiceOptions: [xtremMasterData.serviceOptions.serialNumberOption] },
        ));

    it('Refuse stock receipt with serial number already in DB and isUsable false', () =>
        Test.withContext(
            async context => {
                const site = await context.read(xtremSystem.nodes.Site, { id: 'US001' });
                const item = await context.read(xtremMasterData.nodes.Item, { id: 'Juice' });
                const stockSearchData: xtremStockData.interfaces.StockSearchData = {
                    item: item._id,
                    location: (await context.read(xtremMasterData.nodes.Location, { id: 'LOC3' }))._id,
                    lot: null,
                    status: (await context.read(xtremStockData.nodes.StockStatus, { id: 'A' }))._id,
                    stockUnit: (await item.stockUnit)._id,
                    site: site._id,
                    owner: await site.id,
                };

                const receipt = await context.create(xtremStock.nodes.StockReceipt, {
                    number: 'TESTSERIAL',
                    effectiveDate: date.today(),
                    stockSite: site,
                    lines: [
                        {
                            item: stockSearchData.item,
                            stockStatus: stockSearchData.status,
                            quantityInStockUnit: 1,
                            stockDetails: [
                                testLib.functions.TestHelpers.stockSearchDataToDetailCreateData(stockSearchData, {
                                    quantityInStockUnit: 1,
                                    movementType: 'receipt',
                                    stockDetailSerialNumbers: [{ stockDetail: -1, newSerialNumberId: 'SSN0007' }],
                                }),
                            ],
                        },
                    ],
                });

                await assert.isRejected(
                    receipt.$.save(),
                    'The following serial numbers are already referenced on another document: SSN0007',
                );
            },
            { today: '2020-07-12', testActiveServiceOptions: [xtremMasterData.serviceOptions.serialNumberOption] },
        ));

    it('Allow receipt with 1 line but only 2 out of 5 serial numbers', () =>
        Test.withContext(
            async context => {
                const site = await context.read(xtremSystem.nodes.Site, { id: 'US001' });
                const item = await context.read(xtremMasterData.nodes.Item, { id: 'ISSUESERIAL' });
                const stockSearchData: xtremStockData.interfaces.StockSearchData = {
                    item: item._id,
                    location: (await context.read(xtremMasterData.nodes.Location, { id: 'LOC3' }))._id,
                    lot: null,
                    status: (await context.read(xtremStockData.nodes.StockStatus, { id: 'A' }))._id,
                    stockUnit: (await item.stockUnit)._id,
                    site: site._id,
                    owner: await site.id,
                };

                const receipt = await context.create(xtremStock.nodes.StockReceipt, {
                    number: 'TESTSERIAL',
                    effectiveDate: date.today(),
                    stockSite: site,
                    lines: [
                        {
                            item: stockSearchData.item,
                            stockStatus: stockSearchData.status,
                            quantityInStockUnit: 5,
                            stockDetails: [
                                testLib.functions.TestHelpers.stockSearchDataToDetailCreateData(stockSearchData, {
                                    quantityInStockUnit: 5,
                                    movementType: 'receipt',
                                    stockDetailSerialNumbers: [
                                        { stockDetail: -1, newSerialNumberId: 'SN01' },
                                        { stockDetail: -1, newSerialNumberId: 'SN02' },
                                    ],
                                }),
                            ],
                        },
                    ],
                });

                await receipt.$.save();
                assert.deepEqual(receipt!.$.context.diagnoses, []);

                const receipt1 = await context.read(xtremStock.nodes.StockReceipt, {
                    number: 'TESTSERIAL',
                });
                const receiptLines = await receipt1.lines.toArray();
                assert.deepEqual(
                    await (
                        await receiptLines[0].stockDetails.elementAt(0)
                    ).stockDetailSerialNumbers.length,
                    5,
                );
                assert.deepEqual(
                    await (await receiptLines[0].stockDetails.elementAt(0)).stockDetailSerialNumbers
                        .map(async sn => (await sn.serialNumber)?.id)
                        .toArray(),
                    ['SN01', 'SN02', 'SSN0008', 'SSN0009', 'SSN0010'],
                );
            },
            { today: '2020-07-12', testActiveServiceOptions: [xtremMasterData.serviceOptions.serialNumberOption] },
        ));

    it('Create and repost a receipt', () =>
        Test.withContext(async context => {
            const site = await context.read(xtremSystem.nodes.Site, { id: 'US001' });
            const item = await context.read(xtremMasterData.nodes.Item, { id: 'LOT_TEST1' });
            const stockSearchData: xtremStockData.interfaces.StockSearchData = {
                item: item._id,
                location: (await context.read(xtremMasterData.nodes.Location, { id: 'LOC3' }))._id,
                lot: (await context.read(xtremStockData.nodes.Lot, { id: 'MISCRECEIPT1', item }))._id,
                status: (await context.read(xtremStockData.nodes.StockStatus, { id: 'A' }))._id,
                stockUnit: (await item.stockUnit)._id,
                site: site._id,
                owner: await site.id,
            };

            const receipt = await context.create(xtremStock.nodes.StockReceipt, {
                number: 'TESTREPOST',
                effectiveDate: date.today(),
                stockSite: site,
                lines: [
                    {
                        item: stockSearchData.item,
                        stockStatus: stockSearchData.status,
                        quantityInStockUnit: 15,
                        stockDetails: [
                            testLib.functions.TestHelpers.stockSearchDataToDetailCreateData(stockSearchData, {
                                quantityInStockUnit: 15,
                            }),
                        ],
                    },
                ],
            });

            await receipt.$.save();

            const receiptLines = await asyncArray(await receipt.lines.toArray())
                .map(async line => {
                    return {
                        baseDocumentLineSysId: line._id,
                        storedAttributes: (await line.storedAttributes) as xtremMasterData.interfaces.StoredAttributes,
                        storedDimensions: (await line.storedDimensions) || {},
                    };
                })
                .toArray();

            await assert.isRejected(
                xtremStock.nodes.StockReceipt.repost(context, receipt, receiptLines),
                "You can only repost a stock receipt if the status is 'Failed' or 'Not recorded.'",
            );
        }));

    it('Create receipt with line with negative order cost', () =>
        Test.withContext(async context => {
            const site = await context.read(xtremSystem.nodes.Site, { id: 'US001' });
            const item = await context.read(xtremMasterData.nodes.Item, { id: 'LOT_TEST1' });
            const stockSearchData: xtremStockData.interfaces.StockSearchData = {
                item: item._id,
                location: (await context.read(xtremMasterData.nodes.Location, { id: 'LOC3' }))._id,
                lot: (await context.read(xtremStockData.nodes.Lot, { id: 'MISCRECEIPT1', item }))._id,
                status: (await context.read(xtremStockData.nodes.StockStatus, { id: 'A' }))._id,
                stockUnit: (await item.stockUnit)._id,
                site: site._id,
                owner: await site.id,
            };

            const receipt = await context.create(xtremStock.nodes.StockReceipt, {
                number: 'TEST',
                effectiveDate: date.today(),
                stockSite: site,
                lines: [
                    {
                        item: stockSearchData.item,
                        stockStatus: stockSearchData.status,
                        quantityInStockUnit: 15,
                        orderCost: -10,
                    },
                ],
            });

            await assert.isRejected(receipt.$.save());

            assert.deepEqual(receipt.$.context.diagnoses, [
                {
                    severity: ValidationSeverity.error,
                    path: ['lines', '-1000000002', 'orderCost'],
                    message: 'value must not be negative',
                },
            ]);
        }));

    it('Create receipt with line with negative quantity', () =>
        Test.withContext(async context => {
            const site = await context.read(xtremSystem.nodes.Site, { id: 'US001' });
            const item = await context.read(xtremMasterData.nodes.Item, { id: 'LOT_TEST1' });
            const stockSearchData: xtremStockData.interfaces.StockSearchData = {
                item: item._id,
                location: (await context.read(xtremMasterData.nodes.Location, { id: 'LOC3' }))._id,
                lot: (await context.read(xtremStockData.nodes.Lot, { id: 'MISCRECEIPT1', item }))._id,
                status: (await context.read(xtremStockData.nodes.StockStatus, { id: 'A' }))._id,
                stockUnit: (await item.stockUnit)._id,
                site: site._id,
                owner: await site.id,
            };

            const receipt = await context.create(xtremStock.nodes.StockReceipt, {
                number: 'TEST',
                effectiveDate: date.today(),
                stockSite: site,
                lines: [
                    {
                        item: stockSearchData.item,
                        stockStatus: stockSearchData.status,
                        quantityInStockUnit: -15,
                    },
                ],
            });

            await assert.isRejected(receipt.$.save());

            assert.deepEqual(receipt.$.context.diagnoses, [
                {
                    severity: ValidationSeverity.error,
                    path: ['lines', '-1000000002', 'quantityInStockUnit'],
                    message: 'value must be positive',
                },
            ]);
        }));

    it('Create receipt with line with quantity 0', () =>
        Test.withContext(async context => {
            const site = await context.read(xtremSystem.nodes.Site, { id: 'US001' });
            const item = await context.read(xtremMasterData.nodes.Item, { id: 'LOT_TEST1' });
            const stockSearchData: xtremStockData.interfaces.StockSearchData = {
                item: item._id,
                location: (await context.read(xtremMasterData.nodes.Location, { id: 'LOC3' }))._id,
                lot: (await context.read(xtremStockData.nodes.Lot, { id: 'MISCRECEIPT1', item }))._id,
                status: (await context.read(xtremStockData.nodes.StockStatus, { id: 'A' }))._id,
                stockUnit: (await item.stockUnit)._id,
                site: site._id,
                owner: await site.id,
            };

            const receipt = await context.create(xtremStock.nodes.StockReceipt, {
                number: 'TEST',
                effectiveDate: date.today(),
                stockSite: site,
                lines: [
                    {
                        item: stockSearchData.item,
                        stockStatus: stockSearchData.status,
                        quantityInStockUnit: 0,
                    },
                ],
            });

            await assert.isRejected(receipt.$.save());

            assert.deepEqual(receipt.$.context.diagnoses, [
                {
                    severity: ValidationSeverity.error,
                    path: ['lines', '-1000000002', 'quantityInStockUnit'],
                    message: 'value must be positive',
                },
            ]);
        }));

    it('Check onceStockCompleted is called only when the document is completed', () =>
        Test.withContext(async context => {
            const result = await xtremStockData.functions.testLib.testOnceStockCompleted(context, {
                clas: xtremStock.nodes.StockReceipt,
                movementType: 'receipt',
                documents: [
                    { key: { _id: 1 }, isCompleted: false },
                    { key: { _id: 2 }, isCompleted: true },
                    { key: { _id: 3 }, isCompleted: false },
                    { key: { _id: 4 }, isCompleted: true },
                ],
                returnedProperty: '_id',
            });

            assert.deepEqual(result, [2, 4]);
        }));

    it('Check writeable context to lock stock transaction to progress updates', () =>
        Test.withContext(async context => {
            const number = 'MISC_RECEIPT11';
            await xtremStockData.testHelpers.testPostToStockSetInProgress(context, {
                documentNode: xtremStock.nodes.StockReceipt,
                number,
                assert,
                postToStock: xtremStock.nodes.StockReceipt.postToStock,
            });

            await Test.rollbackCache(context);

            const stockReceipt = await context.read(xtremStock.nodes.StockReceipt, { number });
            // after the call to postToStock, the document must be 'inProgress' status'
            assert.equal(await stockReceipt.status, 'inProgress');
        }));
});

describe('StockReceipt valuation', () => {
    // This unit test concerns the valuation function of the stock receipt. They are triggered by stock engine when posting
    it('Test getOrderCost and getValuedCost for stock receipt', () =>
        Test.withContext(async context => {
            const site = await context.read(xtremSystem.nodes.Site, { id: 'US001' });
            const item = await context.read(xtremMasterData.nodes.Item, { id: 'STOFIFO' });

            const stockReceipt = await context.create(xtremStock.nodes.StockReceipt, {
                number: 'TEST_VALUATION',
                effectiveDate: date.make(2023, 3, 17),
                stockSite: site,
                lines: [
                    {
                        item,
                        quantityInStockUnit: 3,
                        stockStatus: (await context.read(xtremStockData.nodes.StockStatus, { id: 'A' }))._id,
                    },
                ],
            });
            const orderCost = await (await stockReceipt.lines.elementAt(0)).getOrderCost();
            const valuedCost = await (await stockReceipt.lines.elementAt(0)).getValuedCost();

            // The item is FIFO managed and as 2 FIFO tiers exist at 2023-03-17,
            // the most recent is taken to give the default unit price
            assert.equal(Number(orderCost), 90);
            assert.equal(Number(valuedCost), 90);
        }));

    it('Repost an existing stock receipt notRecorded in finance', () =>
        Test.withContext(
            async context => {
                const stockReceiptNumber = 'MISC_RECEIPT31';

                const stockReceipt = await context.read(
                    xtremStock.nodes.StockReceipt,
                    { number: stockReceiptNumber },
                    { forUpdate: true },
                );
                assert.equal(await stockReceipt.financeIntegrationStatus, 'notRecorded');

                const stockReceiptLines = await asyncArray(await stockReceipt.lines.toArray())
                    .map(async line => {
                        return {
                            baseDocumentLineSysId: line._id,
                            storedAttributes:
                                (await line.storedAttributes) as xtremMasterData.interfaces.StoredAttributes,
                            storedDimensions: (await line.storedDimensions) || {},
                        };
                    })
                    .toArray();

                const repostResult = await xtremStock.nodes.StockReceipt.repost(
                    context,
                    stockReceipt,
                    stockReceiptLines,
                );
                assert.equal(repostResult.wasSuccessful, true);
            },
            { today: '2024-01-11' },
        ));

    it('Create receipt attribute type restricted to', () =>
        Test.withContext(async context => {
            const site = await context.read(xtremSystem.nodes.Site, { id: 'US001' });
            const item = await context.read(xtremMasterData.nodes.Item, { id: 'LOT_TEST1' });
            const stockSearchData: xtremStockData.interfaces.StockSearchData = {
                item: item._id,
                location: (await context.read(xtremMasterData.nodes.Location, { id: 'LOC3' }))._id,
                lot: (await context.read(xtremStockData.nodes.Lot, { id: 'MISCRECEIPT1', item }))._id,
                status: (await context.read(xtremStockData.nodes.StockStatus, { id: 'A' }))._id,
                stockUnit: (await item.stockUnit)._id,
                site: site._id,
                owner: await site.id,
            };

            const receipt = await context.create(xtremStock.nodes.StockReceipt, {
                number: 'TEST',
                effectiveDate: date.today(),
                stockSite: site,
                lines: [
                    {
                        item: stockSearchData.item,
                        stockStatus: stockSearchData.status,
                        quantityInStockUnit: 15,
                        stockDetails: [
                            testLib.functions.TestHelpers.stockSearchDataToDetailCreateData(stockSearchData, {
                                quantityInStockUnit: 15,
                                movementType: 'receipt',
                                stockDetailLot: {
                                    lot: await context.read(xtremStockData.nodes.Lot, {
                                        id: 'MISCRECEIPT1',
                                        sublot: '',
                                        item,
                                    }),
                                },
                            }),
                        ],
                        storedAttributes: { project: '', task: 'TASK1', employee: '' },
                    },
                ],
            });

            await assert.isRejected(receipt.$.save());
            assert.deepEqual(receipt.$.context.diagnoses, [
                {
                    severity: 3,
                    path: ['lines', '-1000000002'],
                    message: 'The Project attribute needs to be filled in.',
                },
            ]);
        }));
    it('Read receipt', () =>
        Test.withContext(async context => {
            const receipt = await context.read(xtremStock.nodes.StockReceipt, {
                number: 'MISC_RECEIPT13',
            });

            assert.instanceOf(receipt, xtremStock.nodes.StockReceipt);
            assert.equal(await receipt.number, 'MISC_RECEIPT13');
            assert.equal((await receipt.effectiveDate).toString(), '2021-08-02');

            const receiptLines = await receipt.lines.toArray();
            assert.equal(await (await receiptLines[0].item).id, 'Chair');
            assert.equal(await receiptLines[0].quantityInStockUnit, 4);
            assert.equal(await (await receiptLines[0].document).number, 'MISC_RECEIPT13');
            assert.equal((await (await receiptLines[0].document).effectiveDate).toString(), '2021-08-02');
            assert.equal(await receiptLines[0].stockTransactionStatus, 'completed');
            assert.equal(await receipt.isSetDimensionsMainListHidden, true);
        }));
});

describe('Posting details', () => {
    it('Gets posting details successfully', () =>
        Test.withContext(async context => {
            const srDocumentNumber = 'SR250003';
            const srDocument = await context.read(xtremStock.nodes.StockReceipt, { number: srDocumentNumber });
            assert.equal(await srDocument.postingDetails.length, 1);

            const journalEntry = await srDocument.postingDetails.at(0);
            assert.equal(await journalEntry?.documentType, 'miscellaneousStockReceipt');
            assert.equal(await journalEntry?.documentNumber, srDocumentNumber);
            assert.equal(await journalEntry?.documentSysId, srDocument._id);
            assert.equal(await journalEntry?.targetDocumentType, 'journalEntry');
        }));
});
