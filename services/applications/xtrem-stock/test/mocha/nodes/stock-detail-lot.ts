import type { Context } from '@sage/xtrem-core';
import { date, Test } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremStockData from '@sage/xtrem-stock-data';
import * as xtremSystem from '@sage/xtrem-system';
import { assert } from 'chai';
import * as xtremStock from '../../../lib';
import * as testLib from '../../fixtures/lib';

async function prepareReceipt(
    context: Context,
    site: xtremSystem.nodes.Site,
): Promise<xtremStockData.interfaces.StockSearchData> {
    const item = await context.read(xtremMasterData.nodes.Item, { id: 'Muesli' });
    return {
        item: item._id,
        location: (await context.read(xtremMasterData.nodes.Location, { id: 'LOC3' }))._id,
        lot: null,
        status: (await context.read(xtremStockData.nodes.StockStatus, { id: 'A' }))._id,
        stockUnit: (await item.stockUnit)._id,
        site: site._id,
        owner: await site.id,
    };
}

describe('StockDetailLot node', () => {
    it('should refuse creation of stock detail lot with both lot and stockDetailLot', () =>
        Test.withContext(async context => {
            const site = await context.read(xtremSystem.nodes.Site, { id: 'US001' });
            const stockSearchData = prepareReceipt(context, site);
            const receipt = await context.create(xtremStock.nodes.StockReceipt, {
                number: 'TESTLOT',
                effectiveDate: date.today(),
                stockSite: site,
                lines: [
                    {
                        _action: 'create',
                        item: (await stockSearchData).item,
                        stockStatus: (await stockSearchData).status,
                        quantityInStockUnit: 1,
                        stockDetails: [
                            testLib.functions.TestHelpers.stockSearchDataToDetailCreateData(await stockSearchData, {
                                quantityInStockUnit: 1,
                                movementType: 'receipt',
                                stockDetailLot: {
                                    lotNumber: '',
                                    lot: 1,
                                    stockDetailLot: 1,
                                },
                            }),
                        ],
                    },
                ],
            });

            await assert.isRejected(receipt.$.save());
            assert.sameDeepMembers(receipt.$.context.diagnoses, [
                {
                    severity: 3,
                    path: ['lines', '-1000000002', 'stockDetails', '-1000000003', 'stockDetailLot', 'lot'],
                    message: "The lot needs to be empty if the 'stockDetailLot' property has a value.",
                },
                {
                    severity: 3,
                    path: ['lines', '-1000000002', 'stockDetails', '-1000000003', 'stockDetailLot', 'stockDetailLot'],
                    message: "The stock detail lot needs to be empty if the 'lot' property has a value.",
                },
            ]);
        }));

    it('should refuse creation of stock detail lot with both lot and lotNumber', () =>
        Test.withContext(async context => {
            const site = await context.read(xtremSystem.nodes.Site, { id: 'US001' });
            const stockSearchData = prepareReceipt(context, site);
            const receipt = await context.create(xtremStock.nodes.StockReceipt, {
                number: 'TESTLOT',
                effectiveDate: date.today(),
                stockSite: site,
                lines: [
                    {
                        _action: 'create',
                        item: (await stockSearchData).item,
                        stockStatus: (await stockSearchData).status,
                        quantityInStockUnit: 1,
                        stockDetails: [
                            testLib.functions.TestHelpers.stockSearchDataToDetailCreateData(await stockSearchData, {
                                quantityInStockUnit: 1,
                                movementType: 'receipt',
                                stockDetailLot: {
                                    lotNumber: 'LOT001',
                                    lot: 1,
                                },
                            }),
                        ],
                    },
                ],
            });

            await assert.isRejected(receipt.$.save());
            assert.deepEqual(receipt.$.context.diagnoses, [
                {
                    severity: 3,
                    path: ['lines', '-1000000002', 'stockDetails', '-1000000003', 'stockDetailLot', 'lotNumber'],
                    message: "The lot number needs to be empty if the 'lot' property has a value.",
                },
            ]);
        }));

    it('should refuse creation of stock detail lot without expiration date and sublot', () =>
        Test.withContext(async context => {
            const stockDetailLot = await context.create(xtremStockData.nodes.StockDetailLot, {
                stockDetail: 1044,
                lotNumber: 'LOT001',
            });

            await assert.isRejected(stockDetailLot.$.save());

            assert.deepEqual(stockDetailLot.$.context.diagnoses, [
                {
                    severity: 3,
                    path: ['expirationDate'],
                    message: 'You need to enter an expiration date.',
                },
                {
                    severity: 3,
                    path: ['sublot'],
                    message: 'You need to enter a sublot.',
                },
            ]);
        }));

    it('should refuse creation of stock detail lot when item does not have lot sequence number', () =>
        Test.withContext(async context => {
            const stockDetailLot = await context.create(xtremStockData.nodes.StockDetailLot, {
                stockDetail: 1016,
                sublot: 'SUBLOT1',
                expirationDate: date.today(),
            });

            await assert.isRejected(stockDetailLot.$.save());

            assert.deepEqual(stockDetailLot.$.context.diagnoses, [
                {
                    severity: 3,
                    path: [],
                    message: "You need to assign a lot number to the item 'Chemical description D'.",
                },
            ]);
        }));

    it('should refuse creation of stock detail lot when different supplierLot or expirationDate for the same lotNumber', () =>
        Test.withContext(
            async context => {
                const site = await context.read(xtremSystem.nodes.Site, { id: 'US001' });
                const stockSearchData = prepareReceipt(context, site);
                const receipt = await context.create(xtremStock.nodes.StockReceipt, {
                    number: 'TESTLOT',
                    effectiveDate: date.today(),
                    stockSite: site,
                    lines: [
                        {
                            _action: 'create',
                            item: (await stockSearchData).item,
                            stockStatus: (await stockSearchData).status,
                            quantityInStockUnit: 3,
                            stockDetails: [
                                testLib.functions.TestHelpers.stockSearchDataToDetailCreateData(await stockSearchData, {
                                    quantityInStockUnit: 2,
                                    movementType: 'receipt',
                                    stockDetailLot: {
                                        lotNumber: 'LOT001',
                                        supplierLot: 'SLOT001',
                                        sublot: 'SUBLOT1',
                                        expirationDate: date.today(),
                                    },
                                }),
                                testLib.functions.TestHelpers.stockSearchDataToDetailCreateData(await stockSearchData, {
                                    quantityInStockUnit: 1,
                                    movementType: 'receipt',
                                    stockDetailLot: {
                                        lotNumber: 'LOT001',
                                        supplierLot: 'SLOT002',
                                        sublot: 'SUBLOT1',
                                        expirationDate: date.today(),
                                    },
                                }),
                            ],
                        },
                        {
                            _action: 'create',
                            item: (await stockSearchData).item,
                            stockStatus: (await stockSearchData).status,
                            quantityInStockUnit: 1,
                            stockDetails: [
                                testLib.functions.TestHelpers.stockSearchDataToDetailCreateData(await stockSearchData, {
                                    quantityInStockUnit: 1,
                                    movementType: 'receipt',
                                    stockDetailLot: {
                                        lotNumber: 'LOT001',
                                        supplierLot: 'SLOT002',
                                        sublot: 'SUBLOT1',
                                        expirationDate: date.make(2021, 9, 30),
                                    },
                                }),
                            ],
                        },
                    ],
                });

                await assert.isRejected(receipt.$.save());
                assert.deepEqual(receipt.$.context.diagnoses, [
                    {
                        severity: 3,
                        path: ['lines', '-1000000002', 'stockDetails', '-1000000003', 'stockDetailLot'],
                        message:
                            'All stock details with the same lot and sublot numbers need to have the same supplier lot and expiration date.',
                    },
                    {
                        severity: 3,
                        path: ['lines', '-1000000002', 'stockDetails', '-1000000005', 'stockDetailLot'],
                        message:
                            'All stock details with the same lot and sublot numbers need to have the same supplier lot and expiration date.',
                    },
                    {
                        severity: 3,
                        path: ['lines', '-1000000008', 'stockDetails', '-1000000009', 'stockDetailLot'],
                        message:
                            'All stock details with the same lot and sublot numbers need to have the same supplier lot and expiration date.',
                    },
                ]);
            },
            { today: '2020-07-12' },
        ));
});
