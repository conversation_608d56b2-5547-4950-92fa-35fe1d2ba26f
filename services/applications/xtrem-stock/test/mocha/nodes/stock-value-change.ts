import { Test, date } from '@sage/xtrem-core';
import * as xtremFinanceData from '@sage/xtrem-finance-data';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremStockData from '@sage/xtrem-stock-data';
import * as xtremSystem from '@sage/xtrem-system';
import { assert } from 'chai';
import * as xtremStock from '../../../lib';

describe('Stock Value controls', () => {
    it('Test rounding of stock value change amounts when creating standard cost', () =>
        Test.withContext(
            async context => {
                const item = await context.read(xtremMasterData.nodes.Item, { _id: '#STOSTD' });
                const site = await context.read(xtremSystem.nodes.Site, { _id: '#US001' });
                // 1- test that amount rounding int the stock value change is 2 decimals because next test is based on this
                const testAmount = 10.008;
                const testNewAmount = 10.009;
                const testVarianceAmount = 0.006;
                const valueChange = await context.create(
                    xtremStock.nodes.StockValueChange,
                    {
                        description: 'Test',
                        site,
                        item,
                        lines: [
                            {
                                quantity: 1,
                                unitCost: testAmount,
                                amount: testAmount,
                                newUnitCost: testNewAmount,
                                newAmount: testNewAmount,
                                stockDetails: [
                                    {
                                        _action: 'create',
                                        site,
                                        item,
                                        amount: testVarianceAmount,
                                        valuationMethod: 'standardCost',
                                        quantityInStockUnit: 1,
                                    },
                                ],
                            },
                        ],
                    },
                    { isTransient: false },
                );

                const line = await valueChange.lines.elementAt(0);
                assert.deepEqual(await line.amount, 10.01);
                assert.deepEqual(await line.newAmount, 10.01);
                assert.deepEqual(await (await line.stockDetails.elementAt(0)).amount, 0.01);

                // check the previous standard cost value to be sure the variance is the one expected
                const previousCost = await xtremMasterData.nodes.ItemSiteCost.getItemSiteCost(
                    context,
                    date.today().addDays(-1),
                    item,
                    site,
                );
                assert.deepEqual(previousCost, 114.444);

                // 2- XT-75988: test that the rounding doesn't trigger a rounding issue between the stock value change line and its detail
                const itemSiteCost: xtremMasterData.nodes.ItemSiteCost = await context.create(
                    xtremMasterData.nodes.ItemSiteCost,
                    {
                        itemSite: '#STOSTD|US001',
                        costCategory: '#Standard',
                        fromDate: date.today(),
                        forQuantity: 1,
                        materialCost: 114.449,
                    },
                );

                /* does not throw */ await itemSiteCost.$.save();
            },
            { today: '2024-05-05' },
        ));

    it('Save controls- check for fifo duplicates', () =>
        Test.withContext(async context => {
            const item = await context.read(xtremMasterData.nodes.Item, { _id: '#STOFIFO' });
            const site = await context.read(xtremSystem.nodes.Site, { _id: '#DEP1-S01' });
            const fifoCost = await context.read(xtremStockData.nodes.FifoValuationTier, { _id: '115400' });

            const stockValueChange = await context.create(xtremStock.nodes.StockValueChange, {
                item,
                site,
                description: 'Stock value change',
                lines: [
                    {
                        _action: 'create',
                        _id: -1,
                        _sortValue: 10,
                        amount: 1500,
                        fifoCost,
                        newAmount: 1500,
                        newUnitCost: 30,
                        quantity: 50,
                        stockTransactionStatus: 'draft',
                        storedAttributes: null,
                        storedDimensions: null,
                        unitCost: 30,
                    },
                    {
                        _action: 'create',
                        _id: -2,
                        _sortValue: 20,
                        amount: 1500,
                        fifoCost,
                        newAmount: 1500,
                        newUnitCost: 30,
                        quantity: 50,
                        stockTransactionStatus: 'draft',
                        storedAttributes: null,
                        storedDimensions: null,
                        unitCost: 30,
                    },
                ],
            });

            await assert.isRejected(stockValueChange.$.save(), 'The record was not created.');
            const errorMessages = context.diagnoses.map(diagnose => diagnose.message);
            assert.equal(errorMessages[0], 'Change or remove duplicate lines.');
        }));
});

describe('StockValueChange posting', () => {
    it('Post success', () =>
        Test.withContext(async context => {
            let stockValueChangeNumber = 'SVC23';

            let stockValueChange = await context.read(
                xtremStock.nodes.StockValueChange,
                { number: stockValueChangeNumber },
                { forUpdate: true },
            );

            await assert.isRejected(
                xtremStock.nodes.StockValueChange.postToStock(context, [stockValueChange._id]),
                'The FIFO stack record corresponding to line with effective date: 2023-03-16   and sequence 1  has changed. Recreate the stock value change line.',
            );

            stockValueChangeNumber = 'SVC23003';

            stockValueChange = await context.read(
                xtremStock.nodes.StockValueChange,
                { number: stockValueChangeNumber },
                { forUpdate: true },
            );

            await assert.isRejected(
                xtremStock.nodes.StockValueChange.postToStock(context, [stockValueChange._id]),
                'Line 1: the current and new amounts cannot be the same. Enter a different value in the new amount.',
            );

            stockValueChangeNumber = 'SVC23004';

            stockValueChange = await context.read(
                xtremStock.nodes.StockValueChange,
                { number: stockValueChangeNumber },
                { forUpdate: true },
            );

            await assert.isRejected(
                xtremStock.nodes.StockValueChange.postToStock(context, [stockValueChange._id]),
                'The average cost corresponding to line 1 has changed. Recreate the stock value change line.',
            );

            stockValueChangeNumber = 'SVC23005';

            stockValueChange = await context.read(
                xtremStock.nodes.StockValueChange,
                { number: stockValueChangeNumber },
                { forUpdate: true },
            );

            await assert.isRejected(
                xtremStock.nodes.StockValueChange.postToStock(context, [stockValueChange._id]),
                'Line 1: the current and new amounts cannot be the same. Enter a different value in the new amount.',
            );
        }));

    it('Check onceStockCompleted is called only when the document is completed', () =>
        Test.withContext(async context => {
            const result = await xtremStockData.functions.testLib.testOnceStockCompleted(context, {
                clas: xtremStock.nodes.StockValueChange,
                movementType: 'valueChange',
                documents: [
                    { key: { _id: 1 }, isCompleted: false },
                    { key: { _id: 2 }, isCompleted: true },
                    { key: { _id: 3 }, isCompleted: false },
                    { key: { _id: 4 }, isCompleted: true },
                ],
                returnedProperty: '_id',
            });

            assert.deepEqual(result, [2, 4]);
        }));

    it('Stock value change attribute type restricted to', () =>
        Test.withContext(async context => {
            const item = await context.read(xtremMasterData.nodes.Item, { id: 'STOFIFO' });
            const site = await context.read(xtremSystem.nodes.Site, { id: 'DEP1-S01' });
            const fifoCost = await context.read(xtremStockData.nodes.FifoValuationTier, { _id: '115400' });

            const stockValueChange = await context.create(xtremStock.nodes.StockValueChange, {
                item,
                site,
                description: 'Stock value change',
                lines: [
                    {
                        _action: 'create',
                        _id: -1,
                        _sortValue: 10,
                        amount: 1500,
                        fifoCost,
                        newAmount: 1500,
                        newUnitCost: 30,
                        quantity: 50,
                        stockTransactionStatus: 'draft',
                        storedAttributes: { employee: '', project: '', task: 'TASK1' },
                        storedDimensions: null,
                        unitCost: 30,
                    },
                ],
            });

            await assert.isRejected(stockValueChange.$.save());
            assert.deepEqual(stockValueChange.$.context.diagnoses, [
                {
                    severity: 3,
                    path: ['lines', '-1'],
                    message: 'The Project attribute needs to be filled in.',
                },
            ]);
        }));

    it('Stock value change default attributes', () =>
        Test.withContext(async context => {
            const item = await context.read(xtremMasterData.nodes.Item, { id: 'HC001' }, { forUpdate: true });
            const site = await context.read(xtremSystem.nodes.Site, { id: 'RX001' });
            const fifoCost = await context.read(xtremStockData.nodes.FifoValuationTier, { _id: '115400' });

            await item.$.set({
                storedAttributes: { project: 'AttPROJ', employee: '', task: 'Task1' },
            });
            await item.$.save();

            const companyDefaultAttributeProject = await context.create(
                xtremFinanceData.nodes.CompanyDefaultAttribute,
                {
                    company: '#RX001',
                    attributeType: '#project',
                    dimensionDefinitionLevel: 'stockDirect',
                    masterDataDefault: 'item',
                },
            );
            await companyDefaultAttributeProject.$.save({ flushDeferredActions: true });

            const companyDefaultAttributeTask = await context.create(xtremFinanceData.nodes.CompanyDefaultAttribute, {
                company: '#RX001',
                attributeType: '#task',
                dimensionDefinitionLevel: 'stockDirect',
                masterDataDefault: 'item',
            });
            await companyDefaultAttributeTask.$.save({ flushDeferredActions: true });

            const stockValueChange = await context.create(xtremStock.nodes.StockValueChange, {
                item,
                site,
                description: 'Stock value change',
                lines: [
                    {
                        _action: 'create',
                        _id: -1,
                        _sortValue: 10,
                        amount: 1500,
                        fifoCost,
                        newAmount: 1500,
                        newUnitCost: 30,
                        quantity: 50,
                        stockTransactionStatus: 'draft',
                        unitCost: 30,
                    },
                ],
            });

            await stockValueChange.$.save();
            assert.deepEqual(
                JSON.stringify(await (await stockValueChange.lines.elementAt(0)).storedAttributes),
                JSON.stringify({
                    project: 'AttPROJ',
                    task: 'Task1',
                }),
            );
        }));
});

describe('Posting details', () => {
    it('Gets posting details successfully', () =>
        Test.withContext(async context => {
            const svcDocumentNumber = 'SVC250001';
            const svcDocument = await context.read(xtremStock.nodes.StockValueChange, { number: svcDocumentNumber });
            assert.equal(await svcDocument.postingDetails.length, 1);

            const journalEntry = await svcDocument.postingDetails.at(0);
            assert.equal(await journalEntry?.documentType, 'stockValueChange');
            assert.equal(await journalEntry?.documentNumber, svcDocumentNumber);
            assert.equal(await journalEntry?.documentSysId, svcDocument._id);
            assert.equal(await journalEntry?.targetDocumentType, 'journalEntry');
        }));
});
