import { Test } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import { assert } from 'chai';
import * as xtremStock from '../../../lib';

describe('CostRollUpInputSet node', () => {
    it('Update attributes and dimensions for all lines', () =>
        Test.withContext(
            async context => {
                const inputSet = await context.read(
                    xtremStock.nodes.CostRollUpInputSet,
                    {
                        _id: 2,
                    },
                    { forUpdate: true },
                );

                assert.instanceOf(inputSet, xtremStock.nodes.CostRollUpInputSet);

                const line0 = await inputSet.resultLines.elementAt(0);
                const line1 = await inputSet.resultLines.elementAt(1);

                assert.deepEqual(await line0.storedAttributes, {} as any);
                assert.deepEqual(await line0.storedDimensions, {
                    dimensionType01: '300',
                    dimensionType03: 'DIMTYPE1VALUE1',
                });
                assert.deepEqual(await line1.storedAttributes, { project: 'AttPROJ2' } as any);
                assert.deepEqual(await line1.storedDimensions, { dimensionType03: '100' });

                await xtremStock.nodes.CostRollUpInputSet.updateAttributesAndDimensions(
                    context,
                    inputSet,
                    '{"dimensionType04":"DIMTYPE2VALUE2","dimensionType05":"RETAIL"}',
                    '{"project":"AttPROJ4"}',
                );

                assert.deepEqual(await line0.storedAttributes, { project: 'AttPROJ4' } as any);
                assert.deepEqual(await line0.storedDimensions, {
                    dimensionType04: 'DIMTYPE2VALUE2',
                    dimensionType05: 'RETAIL',
                });
                assert.deepEqual(await line1.storedAttributes, { project: 'AttPROJ4' } as any);
                assert.deepEqual(await line1.storedDimensions, {
                    dimensionType04: 'DIMTYPE2VALUE2',
                    dimensionType05: 'RETAIL',
                });
            },
            { today: '2023-11-01' },
        ));

    it('Create costs', () =>
        Test.withContext(
            async context => {
                const inputSet = await context.read(
                    xtremStock.nodes.CostRollUpInputSet,
                    {
                        _id: 2,
                    },
                    { forUpdate: true },
                );

                assert.instanceOf(inputSet, xtremStock.nodes.CostRollUpInputSet);

                const resultLine = await inputSet.resultLines.elementAt(0);

                let result = await xtremStock.functions.itemSiteCostLib.createOrUpdateItemSiteCost(context, resultLine);
                assert.deepEqual(result.logStatus, 'info');
                assert.deepEqual(result.creationStatus, 'created');

                const itemSiteCosts = context.query(xtremMasterData.nodes.ItemSiteCost, {
                    filter: { fromDate: { _eq: await inputSet.fromDate } },
                });

                assert.deepEqual(await itemSiteCosts.length, 1);

                const itemSiteCost = await itemSiteCosts.elementAt(0);
                assert.deepEqual(await itemSiteCost.materialCost, await resultLine.materialCost);
                assert.deepEqual(await itemSiteCost.laborCost, await resultLine.laborCost);
                assert.deepEqual(await itemSiteCost.machineCost, await resultLine.machineCost);
                assert.deepEqual(await itemSiteCost.storedAttributes, await resultLine.storedAttributes);
                assert.deepEqual(await itemSiteCost.storedDimensions, await resultLine.storedDimensions);

                result = await xtremStock.functions.itemSiteCostLib.createOrUpdateItemSiteCost(context, resultLine);
                assert.deepEqual(result.logStatus, 'info');
                assert.deepEqual(result.creationStatus, 'updated');
            },
            { today: '2023-11-01' },
        ));
});
