import { date, Test } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremStockData from '@sage/xtrem-stock-data';
import * as xtremSystem from '@sage/xtrem-system';
import { assert } from 'chai';
import * as xtremStock from '../../../lib';
import * as testLib from '../../fixtures/lib';

const { withSequenceNumberContext } = xtremMasterData.functions.testLib;

describe('StockChange node', () => {
    it('Verify chronological control between two stock changes on creation', () =>
        withSequenceNumberContext(
            'StockChange',
            { isChronological: true },
            async context => {
                const newStockChange = await context.create(xtremStock.nodes.StockChange, {
                    effectiveDate: date.make(2020, 7, 12),
                    description: 'Transfer to location2 with status change',
                    stockSite: '#US001',
                    item: '#Muesli',
                    stockStatus: '#A',
                    location: 1,
                    lot: 28,
                    lines: [
                        { quantityInStockUnit: 100, location: 1, stockStatus: '#Q' },
                        { quantityInStockUnit: 200, location: 1, stockStatus: '#R' },
                    ],
                });
                await newStockChange.$.save({ flushDeferredActions: true });
                assert.deepEqual(newStockChange.$.context.diagnoses, []);

                const stockChangeError = await context.create(xtremStock.nodes.StockChange, {
                    effectiveDate: date.make(2020, 7, 11),
                    description: 'Transfer to location2 with status change',
                    stockSite: '#US001',
                    item: '#Muesli',
                    stockStatus: '#A',
                    location: 1,
                    lot: 28,
                    lines: [
                        { quantityInStockUnit: 100, location: 1, stockStatus: '#Q' },
                        { quantityInStockUnit: 200, location: 1, stockStatus: '#R' },
                    ],
                });
                await assert.isRejected(
                    stockChangeError.$.save(),
                    'The document date 2020-07-11 is earlier than the previous document date 2020-07-12.',
                );
            },
            { testActiveServiceOptions: [xtremMasterData.serviceOptions.serialNumberOption] },
        ));

    it('Read test', () =>
        Test.withContext(async context => {
            const change = await context.read(xtremStock.nodes.StockChange, { number: 'CHANGE_STOCK1' });

            assert.instanceOf(change, xtremStock.nodes.StockChange);
            assert.equal((await change.effectiveDate).toString(), '2021-08-02');
            assert.equal(await change.number, 'CHANGE_STOCK1');
            assert.equal(await (await change.item).id, 'Chair');
            assert.equal((await change.location)?._id, 1);
            assert.equal(await (await change.stockStatus).id, 'A');
            assert.isNull(await change.lot);

            const changeLines = await change.lines.toArray();
            assert.equal(await (await changeLines[0].item).id, 'Chair');
            assert.equal((await changeLines[0].quantityInStockUnit).valueOf(), 1);
            assert.equal((await changeLines[0].location)?._id, 3);
            assert.equal(await (await changeLines[0].stockStatus).id, 'A');
        }));

    it('Delete stock change with status draft', () =>
        Test.withContext(async context => {
            const change = await context.read(
                xtremStock.nodes.StockChange,
                {
                    number: 'CHANGE_STOCK1',
                },
                { forUpdate: true },
            );

            assert.instanceOf(change, xtremStock.nodes.StockChange);
            assert.equal(await change.status, 'draft');

            await change.$.delete();
            assert.isFalse(await context.exists(xtremStock.nodes.StockChange, { number: 'CHANGE_STOCK1' }));
        }));

    it('Partial stock change', () =>
        Test.withContext(
            async context => {
                const site = await context.read(xtremSystem.nodes.Site, { id: 'US001' });
                const item = await context.read(xtremMasterData.nodes.Item, { id: 'Chair' });
                const unit = await (await item.stockUnit).id;
                const stockSearchData1: xtremStockData.interfaces.StockSearchData = {
                    item: item._id,
                    location: 1,
                    lot: null,
                    status: (await context.read(xtremStockData.nodes.StockStatus, { id: 'A' }))._id,
                    stockUnit: (await item.stockUnit)._id,
                    site: site._id,
                    owner: await site.id,
                };
                const changedQuantity = 1;

                let stockRecord = await xtremStockData.functions.stockLib.getStockRecord(context, stockSearchData1);
                assert.isNotNull(stockRecord);
                const previousStockQuantity = (
                    (await stockRecord.quantityInStockUnit) - (await stockRecord.totalAllocated)
                ).valueOf();
                assert.isTrue(previousStockQuantity > changedQuantity); // 3

                const stockSearchData2 = { ...stockSearchData1, location: 3 };
                let stockRecord2 = await xtremStockData.functions.stockLib.getStockRecord(context, stockSearchData2);
                let previousStockQuantity2 = 0;
                if (stockRecord2)
                    previousStockQuantity2 = (
                        (await stockRecord2.quantityInStockUnit) - (await stockRecord2.totalAllocated)
                    ).valueOf(); // 1

                const details = [
                    {
                        stockRecord: stockRecord?._id,
                        location: stockSearchData2.location,
                        status: stockSearchData1.status,
                        quantityInStockUnit: changedQuantity,
                    },
                ];
                let change = await context.create(xtremStock.nodes.StockChange, {
                    number: 'TEST',
                    stockSite: stockSearchData1.site,
                    item: stockSearchData1.item,
                    location: stockSearchData1.location,
                    stockStatus: stockSearchData1.status,
                    lot: stockSearchData1.lot,
                    owner: stockSearchData1.owner,
                    lines: [
                        {
                            quantityInStockUnit: changedQuantity,
                            location: stockSearchData2.location,
                            stockStatus: stockSearchData1.status,
                            stockDetails: details,
                        },
                    ],
                });

                const stockJournalSearchData = {
                    item: item._id,
                    site: site._id,
                    effectiveDate: await change.effectiveDate,
                };

                await change.$.save();

                const documentID = change._id;
                const stockRecordID = stockRecord._id;

                // rollbackCache function will flush deferred actions and saves
                await Test.rollbackCache(context);

                change = await context.read(xtremStock.nodes.StockChange, { _id: documentID });

                await testLib.functions.TestHelpers.processStockChange(
                    context,
                    change,
                    { movementType: 'change' },
                    xtremStock.nodes.StockChange.onStockReply,
                    [{ stockUpdateResultStatus: 'changed', stockTransactionStatus: 'succeeded' }],
                );

                // check initial stock record is decremented
                stockRecord = await context.read(xtremStockData.nodes.Stock, { _id: stockRecordID });
                const newStockQuantity = (await stockRecord.quantityInStockUnit) - (await stockRecord.totalAllocated); // 2
                assert.equal(newStockQuantity - previousStockQuantity, -changedQuantity);

                // check final stock record is incremented
                stockRecord2 = await xtremStockData.functions.stockLib.getStockRecord(context, stockSearchData2);
                assert.isNotNull(stockRecord2);
                const newStockQuantity2 =
                    (await stockRecord2.quantityInStockUnit) - (await stockRecord2.totalAllocated); // 2
                assert.equal(newStockQuantity2 - previousStockQuantity2, changedQuantity);

                // check stock movements
                const lastProducedStockMovement = await context
                    .query(xtremStockData.nodes.StockJournal, {
                        filter: { ...stockJournalSearchData },
                        orderBy: { _id: -1 },
                        first: 2,
                    })
                    .toArray();
                assert.equal(lastProducedStockMovement.length, 2);

                const changeLine = await change.lines.elementAt(0);

                const receiptMovement =
                    (await lastProducedStockMovement[0].quantityInStockUnit) > 0
                        ? lastProducedStockMovement[0]
                        : lastProducedStockMovement[1];
                const issueMovement =
                    receiptMovement === lastProducedStockMovement[0]
                        ? lastProducedStockMovement[1]
                        : lastProducedStockMovement[0];

                assert.equal((await receiptMovement?.documentLine)?._id, changeLine._id);
                assert.equal(await receiptMovement?.quantityInStockUnit, changedQuantity);
                assert.equal(await (await receiptMovement?.stockUnit)?.id, unit);
                assert.equal((await receiptMovement?.location)?._id, 3);
                assert.equal(await (await receiptMovement?.status)?.id, 'A');

                assert.equal((await issueMovement?.documentLine)?._id, changeLine._id);
                assert.equal(await issueMovement?.quantityInStockUnit, -changedQuantity);
                assert.equal(await (await issueMovement?.stockUnit)?.id, unit);
                assert.equal((await issueMovement?.location)?._id, 1);
                assert.equal(await (await issueMovement?.status)?.id, 'A');
            },
            { today: '2022-07-21', testActiveServiceOptions: [xtremMasterData.serviceOptions.serialNumberOption] },
        ));

    it('Total stock change', () =>
        Test.withContext(
            async context => {
                const site = await context.read(xtremSystem.nodes.Site, { id: 'US001' });
                const item = await context.read(xtremMasterData.nodes.Item, { id: 'Chair' });
                const unit = await (await item.stockUnit).id;
                const stockSearchData1: xtremStockData.interfaces.StockSearchData = {
                    item: item._id,
                    location: 1,
                    lot: null,
                    status: (await context.read(xtremStockData.nodes.StockStatus, { id: 'A' }))._id,
                    stockUnit: (await item.stockUnit)._id,
                    site: site._id,
                    owner: await site.id,
                };

                let stockRecord = await xtremStockData.functions.stockLib.getStockRecord(context, stockSearchData1);
                assert.isNotNull(stockRecord);
                const previousStockQuantity = (
                    (await stockRecord.quantityInStockUnit) - (await stockRecord.totalAllocated)
                ).valueOf();
                assert.isTrue(previousStockQuantity > 0); // 3
                const changedQuantity = previousStockQuantity;

                const stockSearchData2 = { ...stockSearchData1, location: 8 };
                let stockRecord2 = await xtremStockData.functions.stockLib.getStockRecord(context, stockSearchData2);
                let previousStockQuantity2 = 0;
                if (stockRecord2)
                    previousStockQuantity2 = (
                        (await stockRecord2.quantityInStockUnit) - (await stockRecord2.totalAllocated)
                    ).valueOf(); // 1

                const details = [
                    {
                        stockRecord: stockRecord?._id,
                        location: stockSearchData2.location,
                        status: stockSearchData1.status,
                        quantityInStockUnit: changedQuantity,
                    },
                ];
                let change = await context.create(xtremStock.nodes.StockChange, {
                    number: 'TEST',
                    stockSite: stockSearchData1.site,
                    item: stockSearchData1.item,
                    location: stockSearchData1.location,
                    stockStatus: stockSearchData1.status,
                    lot: stockSearchData1.lot,
                    owner: stockSearchData1.owner,
                    lines: [
                        {
                            quantityInStockUnit: changedQuantity,
                            location: stockSearchData2.location,
                            stockStatus: stockSearchData1.status,
                            stockDetails: details,
                        },
                    ],
                });

                const stockJournalSearchData = {
                    item: item._id,
                    site: site._id,
                    effectiveDate: await change.effectiveDate,
                };

                await change.$.save();

                const documentID = change._id;
                const stockRecordID = stockRecord._id;

                // rollbackCache function will flush deferred actions and saves
                await Test.rollbackCache(context);

                change = await context.read(xtremStock.nodes.StockChange, { _id: documentID });

                await testLib.functions.TestHelpers.processStockChange(
                    context,
                    change,
                    // 'onStockChangeRequest',
                    { movementType: 'change' },
                    xtremStock.nodes.StockChange.onStockReply,
                    [{ stockUpdateResultStatus: 'changed', stockTransactionStatus: 'succeeded' }],
                );

                // check initial stock record is deleted
                stockRecord = await context.tryRead(xtremStockData.nodes.Stock, { _id: stockRecordID });
                assert.isNull(stockRecord);

                // check final stock record is created
                stockRecord2 = await xtremStockData.functions.stockLib.getStockRecord(context, stockSearchData2);
                assert.isNotNull(stockRecord2);
                const newStockQuantity2 =
                    (await stockRecord2.quantityInStockUnit) - (await stockRecord2.totalAllocated); // 2
                assert.equal(newStockQuantity2 - previousStockQuantity2, changedQuantity);

                // check stock movements
                const lastProducedStockMovement = await context
                    .query(xtremStockData.nodes.StockJournal, {
                        filter: { ...stockJournalSearchData },
                        orderBy: { _id: -1 },
                        first: 2,
                    })
                    .toArray();
                assert.equal(lastProducedStockMovement.length, 2);

                const changeLine = await change.lines.elementAt(0);

                const receiptMovement =
                    (await lastProducedStockMovement[0].quantityInStockUnit) > 0
                        ? lastProducedStockMovement[0]
                        : lastProducedStockMovement[1];
                const issueMovement =
                    receiptMovement === lastProducedStockMovement[0]
                        ? lastProducedStockMovement[1]
                        : lastProducedStockMovement[0];

                assert.equal((await receiptMovement?.documentLine)?._id, changeLine._id);
                assert.equal((await receiptMovement?.quantityInStockUnit)?.valueOf(), changedQuantity);
                assert.equal(await (await receiptMovement?.stockUnit)?.id, unit);
                assert.equal((await receiptMovement?.location)?._id, 8);
                assert.equal(await (await receiptMovement?.status)?.id, 'A');

                assert.equal((await issueMovement?.documentLine)?._id, changeLine._id);
                assert.equal((await issueMovement?.quantityInStockUnit)?.valueOf(), -changedQuantity);
                assert.equal(await (await issueMovement?.stockUnit)?.id, unit);
                assert.equal((await issueMovement?.location)?._id, 1);
                assert.equal(await (await issueMovement?.status)?.id, 'A');
            },
            { today: '2022-07-21', testActiveServiceOptions: [xtremMasterData.serviceOptions.serialNumberOption] },
        ));

    it('Stock change exceeds stock record available quantity', () =>
        Test.withContext(
            async context => {
                const site = await context.read(xtremSystem.nodes.Site, { id: 'US001' });
                const item = await context.read(xtremMasterData.nodes.Item, { id: 'Chair' });
                const stockSearchData: xtremStockData.interfaces.StockSearchData = {
                    item: item._id,
                    location: 1,
                    lot: null,
                    status: (await context.read(xtremStockData.nodes.StockStatus, { id: 'A' }))._id,
                    stockUnit: (await item.stockUnit)._id,
                    site: site._id,
                    owner: await site.id,
                };

                const stockRecord = await xtremStockData.functions.stockLib.getStockRecord(context, stockSearchData);
                assert.isNotNull(stockRecord);
                const previousStockQuantity = (
                    (await stockRecord.quantityInStockUnit) - (await stockRecord.totalAllocated)
                ).valueOf();
                assert.isTrue(previousStockQuantity > 0); // 3

                let change = await context.create(xtremStock.nodes.StockChange, {
                    number: 'TEST',
                    stockSite: stockSearchData.site,
                    item: stockSearchData.item,
                    location: stockSearchData.location,
                    stockStatus: stockSearchData.status,
                    lot: stockSearchData.lot,
                    owner: stockSearchData.owner,
                    lines: [
                        {
                            quantityInStockUnit: +previousStockQuantity + 1,
                            location: 8,
                            stockStatus: stockSearchData.status,
                        },
                    ],
                });

                await change.$.save();

                const documentID = change._id;

                await assert.isRejected(
                    xtremStock.nodes.StockChange.postToStock(context, [documentID]),
                    'The sum of the line quantities (4) exceeds the available quantity (3) on the stock record.',
                );

                change = await context.read(xtremStock.nodes.StockChange, { _id: documentID }, { forUpdate: true });

                assert.equal(await change.stockTransactionStatus, 'draft');
                assert.equal(await change.status, 'draft');

                await change.lines.elementAt(0);
                await change.$.set({
                    lines: [
                        {
                            _id: (await change.lines.elementAt(0))._id,
                            _action: 'update',
                            quantityInStockUnit: previousStockQuantity,
                        },
                    ],
                });
                await change.$.save();

                await xtremStockData.testHelpers.testPostToStockSetInProgress(context, {
                    documentNode: xtremStock.nodes.StockChange,
                    number: 'TEST',
                    postToStock: xtremStock.nodes.StockChange.postToStock,
                });

                await Test.rollbackCache(context);
                change = await context.read(xtremStock.nodes.StockChange, { _id: documentID });

                assert.equal(await change.status, 'inProgress');
            },
            { today: '2022-07-21', testActiveServiceOptions: [xtremMasterData.serviceOptions.serialNumberOption] },
        ));

    it('Stock change with no more initial stock record', () =>
        Test.withContext(
            async context => {
                const site = await context.read(xtremSystem.nodes.Site, { id: 'US001' });
                const item = await context.read(xtremMasterData.nodes.Item, { id: 'Chair' });
                const stockSearchData: xtremStockData.interfaces.StockSearchData = {
                    item: item._id,
                    location: 1,
                    lot: null,
                    status: (await context.read(xtremStockData.nodes.StockStatus, { id: 'A' }))._id,
                    stockUnit: (await item.stockUnit)._id,
                    site: site._id,
                    owner: await site.id,
                };

                const stockRecord = await xtremStockData.functions.stockLib.getStockRecord(context, stockSearchData);
                assert.isNotNull(stockRecord);
                const previousStockQuantity = (
                    (await stockRecord.quantityInStockUnit) - (await stockRecord.totalAllocated)
                ).valueOf();
                assert.isTrue(previousStockQuantity > 1); // 3

                let change = await context.create(xtremStock.nodes.StockChange, {
                    number: 'TEST',
                    stockSite: stockSearchData.site,
                    item: stockSearchData.item,
                    location: stockSearchData.location,
                    stockStatus: stockSearchData.status,
                    lot: stockSearchData.lot,
                    owner: stockSearchData.owner,
                    lines: [
                        {
                            quantityInStockUnit: 1,
                            location: 8,
                            stockStatus: stockSearchData.status,
                        },
                    ],
                });

                await change.$.save();
                const documentID = change._id;

                await stockRecord.$.delete();

                await assert.isRejected(
                    xtremStock.nodes.StockChange.postToStock(context, [documentID]),
                    'The stock record to be changed could not be found.',
                );

                change = await context.read(xtremStock.nodes.StockChange, { _id: documentID });

                assert.equal(await change.stockTransactionStatus, 'draft');
                assert.equal(await change.status, 'draft');
            },
            { today: '2022-07-21', testActiveServiceOptions: [xtremMasterData.serviceOptions.serialNumberOption] },
        ));

    it('Refuse stock change with 2 lines and duplicate serial numbers', () =>
        Test.withContext(
            async context => {
                const site = await context.read(xtremSystem.nodes.Site, { id: 'US001' });
                const item = await context.read(xtremMasterData.nodes.Item, { id: 'ISSUESERIAL' });
                const change = await context.create(xtremStock.nodes.StockChange, {
                    number: 'TESTSERIAL',
                    effectiveDate: date.today(),
                    stockSite: site,
                    item,
                    stockStatus: '#A',
                    lines: [
                        {
                            item,
                            quantityInStockUnit: 1,
                            stockStatus: (await context.read(xtremStockData.nodes.StockStatus, { id: 'A1' }))._id,
                            stockDetails: [
                                {
                                    stockUnit: (await item.stockUnit)._id,
                                    quantityInStockUnit: 1,
                                    movementType: 'change',
                                    stockRecord: 77,
                                    item,
                                    stockDetailSerialNumbers: [
                                        {
                                            stockDetail: -1,
                                            supplierSerialNumber: 'SN2',
                                            serialNumber: '#ISSUESERIAL|SSNR0006',
                                        },
                                    ],
                                },
                            ],
                        },
                        {
                            item,
                            quantityInStockUnit: 1,
                            stockStatus: (await context.read(xtremStockData.nodes.StockStatus, { id: 'Q' }))._id,
                            stockDetails: [
                                {
                                    stockUnit: (await item.stockUnit)._id,
                                    quantityInStockUnit: 1,
                                    movementType: 'change',
                                    stockRecord: 77,
                                    item,
                                    stockDetailSerialNumbers: [
                                        {
                                            stockDetail: -1,
                                            supplierSerialNumber: 'SN2',
                                            serialNumber: '#ISSUESERIAL|SSNR0006',
                                        },
                                    ],
                                },
                            ],
                        },
                    ],
                });

                await assert.isRejected(
                    change.$.save(),
                    'There are duplicate serial numbers. Make sure that each line has a unique set of serial numbers.',
                );
            },
            { today: '2020-07-12', testActiveServiceOptions: [xtremMasterData.serviceOptions.serialNumberOption] },
        ));

    it('Refuse stock change with 1 line but no serial numbers', () =>
        Test.withContext(
            async context => {
                const site = await context.read(xtremSystem.nodes.Site, { id: 'US001' });
                const item = await context.read(xtremMasterData.nodes.Item, { id: 'ISSUESERIAL' });
                const change = await context.create(xtremStock.nodes.StockChange, {
                    number: 'TESTSERIAL',
                    effectiveDate: date.today(),
                    stockSite: site,
                    item,
                    stockStatus: '#A',
                    lines: [
                        {
                            item,
                            quantityInStockUnit: 1,
                            stockStatus: (await context.read(xtremStockData.nodes.StockStatus, { id: 'A1' }))._id,
                            stockDetails: [
                                {
                                    stockUnit: (await item.stockUnit)._id,
                                    movementType: 'change',
                                    quantityInStockUnit: 1,
                                    item,
                                    stockRecord: 77,
                                },
                            ],
                        },
                    ],
                });

                await assert.isRejected(
                    change.$.save(),
                    'The number of assigned serial numbers needs to match the quantity for the line. Add or remove serial numbers to match the quantity.',
                );
            },
            { today: '2020-07-12', testActiveServiceOptions: [xtremMasterData.serviceOptions.serialNumberOption] },
        ));
});
