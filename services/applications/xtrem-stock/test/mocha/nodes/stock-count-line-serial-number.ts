import type { Context } from '@sage/xtrem-core';
import { Test } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremStockData from '@sage/xtrem-stock-data';
import { assert } from 'chai';
import * as xtremStock from '../../../lib';

describe('StockCountLineSerialNumber node', () => {
    it('Do not allow creation if no stock record linked to the serialNumber', () =>
        Test.withContext(
            async context => {
                const stockCountLine = await context.read(
                    xtremStock.nodes.StockCountLine,
                    { _id: 'STOCK_COUNT02|10' },
                    { forUpdate: true },
                );
                await stockCountLine.stockCountLineSerialNumbers.append({
                    serialNumber: await context.read(xtremStockData.nodes.SerialNumber, {
                        item: '#STK-SN-TO-COUNT',
                        id: 'STK-SN-0012',
                    }),
                });

                await assert.isRejected(stockCountLine.$.save());
                assert.deepEqual(stockCountLine.$.context.diagnoses, [
                    {
                        message: 'The serial number STK-SN-0012 is not assigned to a stock line.',
                        path: ['stockCountLineSerialNumbers', '-1000000001', 'serialNumber'],
                        severity: 3,
                    },
                ]);
                await assert.isRejected(stockCountLine.$.save());
            },
            {
                testActiveServiceOptions: [xtremMasterData.serviceOptions.serialNumberOption],
            },
        ));

    it('Do not allow creation if stock record does not correspond to the serialNumber', () =>
        Test.withContext(
            async context => {
                const stockCountLine = await context.read(
                    xtremStock.nodes.StockCountLine,
                    { _id: 'STOCK_COUNT02|10' },
                    { forUpdate: true },
                );
                await stockCountLine.stockCountLineSerialNumbers.append({
                    // Try to assign a serial number without the correct item
                    serialNumber: await context.read(xtremStockData.nodes.SerialNumber, {
                        item: '#STK-SN-TO-COUNT',
                        id: 'STK-SN-0001',
                    }),
                });

                await assert.isRejected(stockCountLine.$.save());
                assert.deepEqual(stockCountLine.$.context.diagnoses, [
                    {
                        message:
                            'The stock count line and the serial number STK-SN-0001 do not correspond to the same stock line.',
                        path: ['stockCountLineSerialNumbers', '-1000000001', 'serialNumber'],
                        severity: 3,
                    },
                ]);
                await assert.isRejected(stockCountLine.$.save());
            },
            {
                testActiveServiceOptions: [xtremMasterData.serviceOptions.serialNumberOption],
            },
        ));

    it('Do not allow deletion of stock count line serial number if stock count started', () =>
        Test.withContext(
            async context => {
                const stockCountLine = await context.read(
                    xtremStock.nodes.StockCountLine,
                    { _id: 'STOCK_COUNT07|10' },
                    { forUpdate: true },
                );
                assert.deepEqual(await stockCountLine.stockCountLineSerialNumbers.length, 3);

                await assert.isRejected(
                    stockCountLine.$.set({
                        stockCountLineSerialNumbers: [{ _action: 'delete', _sortValue: 10 }],
                    }),
                );

                assert.deepEqual(stockCountLine.$.context.diagnoses, [
                    {
                        message:
                            'A stock count line serial number cannot be deleted after the count has started. Item STK-SN-COUNT1, serial number STK-SN-0001.',
                        path: ['stockCountLineSerialNumbers', '1'],
                        severity: 3,
                    },
                ]);

                const stockCountLineSerialNumber = await context.read(
                    xtremStock.nodes.StockCountLineSerialNumber,
                    { _id: 'STOCK_COUNT07|10|10' },
                    { forUpdate: true },
                );
                await assert.isRejected(stockCountLineSerialNumber.$.delete());
                assert.deepEqual(stockCountLine.$.context.diagnoses, [
                    {
                        message:
                            'A stock count line serial number cannot be deleted after the count has started. Item STK-SN-COUNT1, serial number STK-SN-0001.',
                        path: [],
                        severity: 3,
                    },
                ]);
            },
            {
                testActiveServiceOptions: [xtremMasterData.serviceOptions.serialNumberOption],
            },
        ));

    it('Do not allow deletion of stock count line serial number if stock exists', () =>
        Test.withContext(
            async context => {
                const stockCountLine = await context.read(
                    xtremStock.nodes.StockCountLine,
                    { _id: 'STOCK_COUNT13|10' },
                    { forUpdate: true },
                );
                assert.deepEqual(await stockCountLine.stockCountLineSerialNumbers.length, 2);

                await assert.isRejected(
                    stockCountLine.$.set({
                        stockCountLineSerialNumbers: [{ _action: 'delete', _sortValue: 10 }],
                    }),
                );

                assert.deepEqual(stockCountLine.$.context.diagnoses, [
                    {
                        message:
                            'A stock count line serial number cannot be deleted when it is associated to the counted stock record. Item STK-SN-COUNT2, serial number STK-SN-0001.',
                        path: ['stockCountLineSerialNumbers', '16'],
                        severity: 3,
                    },
                ]);

                const stockCountLineSerialNumber = await context.read(
                    xtremStock.nodes.StockCountLineSerialNumber,
                    { _id: 'STOCK_COUNT13|10|10' },
                    { forUpdate: true },
                );
                await assert.isRejected(stockCountLineSerialNumber.$.delete());
                assert.deepEqual(stockCountLine.$.context.diagnoses, [
                    {
                        message:
                            'A stock count line serial number cannot be deleted when it is associated to the counted stock record. Item STK-SN-COUNT2, serial number STK-SN-0001.',
                        path: [],
                        severity: 3,
                    },
                ]);
            },
            {
                testActiveServiceOptions: [xtremMasterData.serviceOptions.serialNumberOption],
            },
        ));

    it('Allow deletion of stock count line serial number if deletion of the stock count', () =>
        Test.withContext(
            async context => {
                let stockCountLineSerialNumber = await context.tryRead(xtremStock.nodes.StockCountLineSerialNumber, {
                    _id: 'STOCK_COUNT07|10|10',
                });
                assert.isNotNull(stockCountLineSerialNumber);

                const stockCount = await context.read(
                    xtremStock.nodes.StockCount,
                    { _id: 'STOCK_COUNT07' },
                    { forUpdate: true },
                );
                await stockCount.$.set({ status: 'toBeCounted' });
                await stockCount.$.save();
                await stockCount.$.delete();

                stockCountLineSerialNumber = await context.tryRead(xtremStock.nodes.StockCountLineSerialNumber, {
                    _id: 'STOCK_COUNT07|10|10',
                });

                assert.isNull(stockCountLineSerialNumber);
            },
            {
                testActiveServiceOptions: [xtremMasterData.serviceOptions.serialNumberOption],
            },
        ));

    async function moveSerialNumber(
        context: Context,
        args: { serialNumberId: number; newStockRecordId: number | null },
    ): Promise<xtremStock.nodes.StockCount> {
        await Test.rollbackCache(context);

        const movedSerialNumber = await context.read(
            xtremStockData.nodes.SerialNumber,
            {
                _id: args.serialNumberId,
            },
            { forUpdate: true },
        );

        // simulate an issue of the 2nd serial number in the first line
        await movedSerialNumber.$.set({ stockRecord: args.newStockRecordId });
        await movedSerialNumber.$.save();
        return context.read(xtremStock.nodes.StockCount, { _id: 'STOCK_COUNT13' });
    }

    it('move serial number while stock count draft', () =>
        Test.withContext(
            async context => {
                let stockCount = await context.read(xtremStock.nodes.StockCount, { _id: 'STOCK_COUNT13' });
                assert.deepEqual(await stockCount.status, 'toBeCounted');

                assert.deepEqual(await (await stockCount.lines.elementAt(0)).stockCountLineSerialNumbers.length, 2);
                assert.deepEqual(await (await stockCount.lines.elementAt(1)).stockCountLineSerialNumbers.length, 3);

                const serialNumber = await (
                    await (await stockCount.lines.elementAt(0)).stockCountLineSerialNumbers.elementAt(1)
                ).serialNumber;
                const serialNumberId = serialNumber._id;
                const originStockRecordId = (await serialNumber.stockRecord)?._id ?? null;
                assert.isNotNull(originStockRecordId);

                // simulate an issue of the 2nd serial number in the first line
                stockCount = await moveSerialNumber(context, { serialNumberId, newStockRecordId: null });
                assert.deepEqual(await (await stockCount.lines.elementAt(0)).stockCountLineSerialNumbers.length, 1);
                assert.deepEqual(await (await stockCount.lines.elementAt(1)).stockCountLineSerialNumbers.length, 3);

                // simulate an entry of the serial number in stock record linked to the 2nd stock count line
                stockCount = await moveSerialNumber(context, {
                    serialNumberId,
                    newStockRecordId: (await (await stockCount.lines.elementAt(1)).stockRecord)?._id ?? 0,
                });
                assert.deepEqual(await (await stockCount.lines.elementAt(0)).stockCountLineSerialNumbers.length, 1);
                assert.deepEqual(await (await stockCount.lines.elementAt(1)).stockCountLineSerialNumbers.length, 4);

                // simulate an entry of the serial number in stock record linked to the 2nd stock count line
                stockCount = await moveSerialNumber(context, { serialNumberId, newStockRecordId: originStockRecordId });
                assert.deepEqual(await (await stockCount.lines.elementAt(0)).stockCountLineSerialNumbers.length, 2);
                assert.deepEqual(await (await stockCount.lines.elementAt(1)).stockCountLineSerialNumbers.length, 3);
            },
            {
                testActiveServiceOptions: [xtremMasterData.serviceOptions.serialNumberOption],
            },
        ));
});

describe('StockCountLineSerialNumber mutations', () => {
    describe('Test getEndingSerialNumber', () => {
        it('success', () =>
            Test.withContext(
                async context => {
                    const stockCountLine = await context.read(xtremStock.nodes.StockCountLine, {
                        _id: 'STOCK_COUNT13|30',
                    });
                    assert.deepEqual(await stockCountLine.stockCountLineSerialNumbers.length, 5);
                    const endingSerialNumber = await xtremStock.nodes.StockCountLineSerialNumber.getEndingSerialNumber(
                        context,
                        stockCountLine._id,
                        'STK-SN-0006',
                        3,
                    );

                    assert.deepEqual(endingSerialNumber, 'STK-SN-0008');
                },
                {
                    testActiveServiceOptions: [xtremMasterData.serviceOptions.serialNumberOption],
                },
            ));
        it('quantity greater than range', () =>
            Test.withContext(
                async context => {
                    const stockCountLine = await context.read(xtremStock.nodes.StockCountLine, {
                        _id: 'STOCK_COUNT13|30',
                    });
                    assert.deepEqual(await stockCountLine.stockCountLineSerialNumbers.length, 5);
                    await assert.isRejected(
                        xtremStock.nodes.StockCountLineSerialNumber.getEndingSerialNumber(
                            context,
                            stockCountLine._id,
                            'STK-SN-0006',
                            10, // quantity greater than the range
                        ),
                        'Serial numbers available in this range: 5.',
                    );
                },
                {
                    testActiveServiceOptions: [xtremMasterData.serviceOptions.serialNumberOption],
                },
            ));
        it('incorrect quantity', () =>
            Test.withContext(
                async context => {
                    const stockCountLine = await context.read(xtremStock.nodes.StockCountLine, {
                        _id: 'STOCK_COUNT13|30',
                    });
                    assert.deepEqual(await stockCountLine.stockCountLineSerialNumbers.length, 5);
                    await assert.isRejected(
                        xtremStock.nodes.StockCountLineSerialNumber.getEndingSerialNumber(
                            context,
                            stockCountLine._id,
                            'STK-SN-0006',
                            0,
                        ),
                        'Enter a quantity greater than 0.',
                    );
                },
                {
                    testActiveServiceOptions: [xtremMasterData.serviceOptions.serialNumberOption],
                },
            ));
        it('incorrect serial number', () =>
            Test.withContext(
                async context => {
                    const stockCountLine = await context.read(xtremStock.nodes.StockCountLine, {
                        _id: 'STOCK_COUNT13|30',
                    });
                    assert.deepEqual(await stockCountLine.stockCountLineSerialNumbers.length, 5);
                    await assert.isRejected(
                        xtremStock.nodes.StockCountLineSerialNumber.getEndingSerialNumber(
                            context,
                            stockCountLine._id,
                            'xxxxx',
                            5,
                        ),
                        'The ending serial number cannot be calculated. Check the starting serial number and quantity.',
                    );
                },
                {
                    testActiveServiceOptions: [xtremMasterData.serviceOptions.serialNumberOption],
                },
            ));
        it('serial number not in the line', () =>
            Test.withContext(
                async context => {
                    const stockCountLine = await context.read(xtremStock.nodes.StockCountLine, {
                        _id: 'STOCK_COUNT13|30',
                    });
                    assert.deepEqual(await stockCountLine.stockCountLineSerialNumbers.length, 5);
                    await assert.isRejected(
                        xtremStock.nodes.StockCountLineSerialNumber.getEndingSerialNumber(
                            context,
                            stockCountLine._id,
                            'STK-SN-0002',
                            2,
                        ),
                        'The serial number is not assigned to the stock count line. Serial number STK-SN-0002.',
                    );
                },
                {
                    testActiveServiceOptions: [xtremMasterData.serviceOptions.serialNumberOption],
                },
            ));
    });
});
