import { Test } from '@sage/xtrem-core';
import * as xtremFinanceData from '@sage/xtrem-finance-data';
import { assert } from 'chai';
import * as sinon from 'sinon';
import * as xtremStock from '../../../lib';

describe('Finance posting for stock documents', () => {
    it('Finance posting for a stock value change', () =>
        Test.withContext(
            async context => {
                const documentNumber = 'SVC250002';
                const stockValueChange = await context.read(
                    xtremStock.nodes.StockValueChange,
                    {
                        number: documentNumber,
                    },
                    { forUpdate: true },
                );

                await xtremStock.nodes.StockValueChange.onceStockCompleted(context, stockValueChange);

                const documentFilter = {
                    documentNumber,
                    documentType: 'stockValueChange',
                    targetDocumentType: 'journalEntry',
                };

                const financeTransactions = await context
                    .query(xtremFinanceData.nodes.FinanceTransaction, { filter: { ...documentFilter } })
                    .toArray();

                assert.equal(financeTransactions.length, 1);
                assert.equal(await financeTransactions.at(0)?.message, '');
                assert.equal(await financeTransactions.at(0)?.status, 'pending');

                const accountingStagingRecords = await context
                    .query(xtremFinanceData.nodes.AccountingStaging, { filter: { ...documentFilter } })
                    .toArray();

                assert.equal(accountingStagingRecords.length, 1);
            },
            {
                today: '2021-08-23',
            },
        ));

    it('Finance posting for a stock issue', () =>
        Test.withContext(
            async context => {
                const documentNumber = 'SS250002';
                const stockIssue = await context.read(xtremStock.nodes.StockIssue, {
                    number: documentNumber,
                });

                await xtremStock.nodes.StockIssue.onceStockCompleted(context, stockIssue);

                const documentFilter = {
                    documentNumber,
                    documentType: 'miscellaneousStockIssue',
                    targetDocumentType: 'journalEntry',
                };

                const financeTransactions = await context
                    .query(xtremFinanceData.nodes.FinanceTransaction, { filter: { ...documentFilter } })
                    .toArray();

                assert.equal(financeTransactions.length, 1);
                assert.equal(await financeTransactions.at(0)?.message, '');
                assert.equal(await financeTransactions.at(0)?.status, 'pending');

                const accountingStagingRecords = await context
                    .query(xtremFinanceData.nodes.AccountingStaging, { filter: { ...documentFilter } })
                    .toArray();

                assert.equal(accountingStagingRecords.length, 2);
            },
            {
                today: '2021-08-23',
            },
        ));

    it('Create notifications from a stock receipt', () =>
        Test.withContext(
            async context => {
                const documentNumber = 'SR250007';
                const stockReceipt = await context.read(xtremStock.nodes.StockReceipt, {
                    number: documentNumber,
                });

                await xtremStock.nodes.StockReceipt.onceStockCompleted(context, stockReceipt);

                const documentFilter = {
                    documentNumber,
                    documentType: 'miscellaneousStockReceipt',
                    targetDocumentType: 'journalEntry',
                };

                const financeTransactions = await context
                    .query(xtremFinanceData.nodes.FinanceTransaction, { filter: { ...documentFilter } })
                    .toArray();

                assert.equal(financeTransactions.length, 1);
                assert.equal(await financeTransactions.at(0)?.message, '');
                assert.equal(await financeTransactions.at(0)?.status, 'pending');

                const accountingStagingRecords = await context
                    .query(xtremFinanceData.nodes.AccountingStaging, { filter: { ...documentFilter } })
                    .toArray();

                assert.equal(accountingStagingRecords.length, 2);
            },
            {
                today: '2025-03-31',
            },
        ));
});

describe('Finance posting resend for stock documents', () => {
    it('Stock Value Change - Successfully resend notification for finance', () =>
        Test.withContext(
            async context => {
                const documentNumber = 'SVC250001';

                const stockValueChange: xtremStock.nodes.StockValueChange = await context.read(
                    xtremStock.nodes.StockValueChange,
                    {
                        number: documentNumber,
                    },
                );

                const filter = { documentNumber, documentType: 'stockValueChange' };
                let amountType = await (
                    await (
                        await context
                            .query(xtremFinanceData.nodes.AccountingStaging, {
                                filter,
                            })
                            .at(0)
                    )?.amounts.at(0)
                )?.amountType;

                let financeTransactionRecord = await context
                    .query(xtremFinanceData.nodes.FinanceTransaction, { filter })
                    .at(0);
                let financeTransactionStatus = await financeTransactionRecord?.status;
                let financeTransactionMessage = await financeTransactionRecord?.message;

                assert.equal(amountType, 'landedCostAdjustmentAmount');
                assert.equal(financeTransactionStatus, 'notRecorded');
                assert.notEqual(financeTransactionMessage, '');

                const notifySpy = sinon.spy(context, 'notify');
                await xtremStock.nodes.StockValueChange.resendNotificationForFinance(context, stockValueChange);

                assert.equal(notifySpy.getCalls().length, 0);

                amountType = await (
                    await (
                        await context
                            .query(xtremFinanceData.nodes.AccountingStaging, {
                                filter,
                            })
                            .at(0)
                    )?.amounts.at(0)
                )?.amountType;

                financeTransactionRecord = await context
                    .query(xtremFinanceData.nodes.FinanceTransaction, { filter })
                    .at(0);
                financeTransactionStatus = await financeTransactionRecord?.status;
                financeTransactionMessage = await financeTransactionRecord?.message;

                assert.equal(amountType, 'amount');
                assert.equal(financeTransactionStatus, 'pending');
                assert.equal(financeTransactionMessage, '');
            },
            { today: '2025-01-31' },
        ));

    it('Stock Issue - Successfully resend notification for finance', () =>
        Test.withContext(
            async context => {
                const documentNumber = 'SS250001';

                const stockIssue: xtremStock.nodes.StockIssue = await context.read(xtremStock.nodes.StockIssue, {
                    number: documentNumber,
                });

                const filter = { documentNumber, documentType: 'miscellaneousStockIssue' };
                let amountType = await (
                    await (
                        await context
                            .query(xtremFinanceData.nodes.AccountingStaging, {
                                filter,
                            })
                            .at(0)
                    )?.amounts.at(0)
                )?.amountType;

                let financeTransactionRecord = await context
                    .query(xtremFinanceData.nodes.FinanceTransaction, { filter })
                    .at(0);
                let financeTransactionStatus = await financeTransactionRecord?.status;
                let financeTransactionMessage = await financeTransactionRecord?.message;

                assert.equal(amountType, 'landedCostAdjustmentAmount');
                assert.equal(financeTransactionStatus, 'notRecorded');
                assert.notEqual(financeTransactionMessage, '');

                const notifySpy = sinon.spy(context, 'notify');
                await xtremStock.nodes.StockIssue.resendNotificationForFinance(context, stockIssue);

                assert.equal(notifySpy.getCalls().length, 0);

                amountType = await (
                    await (
                        await context
                            .query(xtremFinanceData.nodes.AccountingStaging, {
                                filter,
                            })
                            .at(0)
                    )?.amounts.at(0)
                )?.amountType;

                financeTransactionRecord = await context
                    .query(xtremFinanceData.nodes.FinanceTransaction, { filter })
                    .at(0);
                financeTransactionStatus = await financeTransactionRecord?.status;
                financeTransactionMessage = await financeTransactionRecord?.message;

                assert.equal(amountType, 'amount');
                assert.equal(financeTransactionStatus, 'pending');
                assert.equal(financeTransactionMessage, '');
            },
            { today: '2025-02-05' },
        ));

    it('Stock Receipt - Successfully resend notification for finance', () =>
        Test.withContext(
            async context => {
                const documentNumber = 'SR250003';

                const stockReceipt: xtremStock.nodes.StockReceipt = await context.read(xtremStock.nodes.StockReceipt, {
                    number: documentNumber,
                });

                const filter = { documentNumber, documentType: 'miscellaneousStockReceipt' };
                let amountType = await (
                    await (
                        await context
                            .query(xtremFinanceData.nodes.AccountingStaging, {
                                filter,
                            })
                            .at(0)
                    )?.amounts.at(0)
                )?.amountType;

                let financeTransactionRecord = await context
                    .query(xtremFinanceData.nodes.FinanceTransaction, { filter })
                    .at(0);
                let financeTransactionStatus = await financeTransactionRecord?.status;
                let financeTransactionMessage = await financeTransactionRecord?.message;

                assert.equal(amountType, 'landedCostAdjustmentAmount');
                assert.equal(financeTransactionStatus, 'notRecorded');
                assert.notEqual(financeTransactionMessage, '');

                const notifySpy = sinon.spy(context, 'notify');
                await xtremStock.nodes.StockReceipt.resendNotificationForFinance(context, stockReceipt);

                assert.equal(notifySpy.getCalls().length, 0);

                amountType = await (
                    await (
                        await context
                            .query(xtremFinanceData.nodes.AccountingStaging, {
                                filter,
                            })
                            .at(0)
                    )?.amounts.at(0)
                )?.amountType;

                financeTransactionRecord = await context
                    .query(xtremFinanceData.nodes.FinanceTransaction, { filter })
                    .at(0);
                financeTransactionStatus = await financeTransactionRecord?.status;
                financeTransactionMessage = await financeTransactionRecord?.message;

                assert.equal(amountType, 'amount');
                assert.equal(financeTransactionStatus, 'pending');
                assert.equal(financeTransactionMessage, '');
            },
            {
                today: '2025-02-13',
            },
        ));

    it('Stock count - Successfully resend notification for finance', () =>
        Test.withContext(
            async context => {
                const documentNumber = 'CS250001';

                const stockCount: xtremStock.nodes.StockCount = await context.read(xtremStock.nodes.StockCount, {
                    number: documentNumber,
                });

                const filter = { documentNumber, documentType: 'stockCount' };
                let amountType = await (
                    await (
                        await context
                            .query(xtremFinanceData.nodes.AccountingStaging, {
                                filter,
                            })
                            .at(0)
                    )?.amounts.at(0)
                )?.amountType;

                let financeTransactionRecord = await context
                    .query(xtremFinanceData.nodes.FinanceTransaction, { filter })
                    .at(0);
                let financeTransactionStatus = await financeTransactionRecord?.status;
                let financeTransactionMessage = await financeTransactionRecord?.message;

                assert.equal(amountType, 'landedCostAdjustmentAmount');
                assert.equal(financeTransactionStatus, 'notRecorded');
                assert.notEqual(financeTransactionMessage, '');

                const notifySpy = sinon.spy(context, 'notify');
                await xtremStock.nodes.StockCount.resendNotificationForFinance(context, stockCount);

                assert.equal(notifySpy.getCalls().length, 0);

                amountType = await (
                    await (
                        await context
                            .query(xtremFinanceData.nodes.AccountingStaging, {
                                filter,
                            })
                            .at(0)
                    )?.amounts.at(0)
                )?.amountType;

                financeTransactionRecord = await context
                    .query(xtremFinanceData.nodes.FinanceTransaction, { filter })
                    .at(0);
                financeTransactionStatus = await financeTransactionRecord?.status;
                financeTransactionMessage = await financeTransactionRecord?.message;

                assert.equal(amountType, 'amount');
                assert.equal(financeTransactionStatus, 'pending');
                assert.equal(financeTransactionMessage, '');
            },
            {
                today: '2025-02-13',
            },
        ));
});
