import { asyncArray, date, Test } from '@sage/xtrem-core';
import * as xtremFinanceData from '@sage/xtrem-finance-data';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremStockData from '@sage/xtrem-stock-data';
import * as xtremSystem from '@sage/xtrem-system';
import { assert } from 'chai';
import * as sinon from 'sinon';
import * as xtremStock from '../../../lib';
import { getLastStockJournal } from '../../../lib/functions/stock-journal-lib';
import * as testLib from '../../fixtures/lib';

const { withSequenceNumberContext } = xtremMasterData.functions.testLib;

describe('StockAdjustment node', () => {
    afterEach(() => {
        sinon.restore();
    });

    it('Create an increase-quantity adjustment with default values', () =>
        Test.withContext(
            async context => {
                const site = await context.read(xtremSystem.nodes.Site, { id: 'US001' });
                const reason = await context.read(xtremMasterData.nodes.ReasonCode, { id: 'R1' });
                const item = await context.read(xtremMasterData.nodes.Item, { id: 'Chair' });
                const stockSearchData: xtremStockData.interfaces.StockSearchData = {
                    item: item._id,
                    location: (await context.read(xtremMasterData.nodes.Location, { _id: 1 }))._id,
                    lot: null,
                    status: (await context.read(xtremStockData.nodes.StockStatus, { id: 'A' }))._id,
                    stockUnit: (await item.stockUnit)._id,
                    site: site._id,
                    owner: await site.id,
                };
                const increasedQuantity = 1;

                const stockRecord = await xtremStockData.functions.stockLib.getStockRecord(context, stockSearchData);
                const previousStockQuantity = (await stockRecord?.quantityInStockUnit)?.valueOf();

                let adjust = await context.create(xtremStock.nodes.StockAdjustment, {
                    stockSite: site,
                    reasonCode: reason,
                    lines: [
                        {
                            item,
                            quantityInStockUnit: 10,
                            adjustmentQuantityInStockUnit: increasedQuantity,
                            stockDetails: [
                                testLib.functions.TestHelpers.stockSearchDataToDetailCreateData<xtremStockData.nodes.StockAdjustmentDetail>(
                                    stockSearchData,
                                    {
                                        quantityInStockUnit: increasedQuantity,
                                        reasonCode: await context.read(xtremMasterData.nodes.ReasonCode, { id: 'R1' }),
                                        stockRecord,
                                    },
                                ),
                            ],
                        },
                    ],
                });

                await adjust.$.save({ flushDeferredActions: true });

                assert.equal(await adjust.displayStatus, 'detailsEntered');

                assert.equal(await (await adjust.lines.elementAt(0)).stockDetailStatus, 'entered');
                assert.equal(await (await adjust.lines.elementAt(0)).displayStatus, 'detailsEntered');

                const stockJournalSearchData = {
                    item: item._id,
                    site: site._id,
                    effectiveDate: await adjust.effectiveDate,
                };

                assert.instanceOf(adjust, xtremStock.nodes.StockAdjustment);
                assert.equal(await adjust.number, `SA200001`);
                assert.deepEqual(await adjust.effectiveDate, date.today());
                assert.equal(await (await adjust.stockSite).id, await site.id);
                assert.equal(await (await adjust.reasonCode).id, await reason.id);
                assert.equal(await adjust.stockTransactionStatus, 'draft');
                assert.equal(await adjust.status, 'draft');
                assert.equal(await (await adjust.financialSite).id, await site.id);
                assert.equal(
                    await (
                        await adjust.transactionCurrency
                    ).id,
                    await (
                        await (
                            await (
                                await adjust.financialSite
                            ).businessEntity
                        ).currency
                    ).id,
                );
                assert.equal(await adjust.documentDate, await adjust.effectiveDate);

                let adjustLine = await adjust.lines.elementAt(0);
                assert.equal(await (await adjustLine.site).id, await site.id);
                assert.equal(await (await adjustLine.item).id, await item.id);
                assert.equal(await (await adjustLine.stockUnit).id, 'EACH');
                assert.equal((await adjustLine.adjustmentQuantityInStockUnit).valueOf(), 1);
                assert.equal((await adjustLine.orderCost).valueOf(), 250);
                assert.equal((await adjustLine.valuedCost).valueOf(), 250);
                assert.equal(await adjustLine.stockTransactionStatus, 'draft');
                assert.equal((await adjustLine.newStockQuantity).valueOf(), 11);

                assert.equal(await adjustLine.stockDetails.length, 1);
                const adjustDetail = await adjustLine.stockDetails.elementAt(0);
                assert.equal((await adjustDetail.site)._id, site._id);
                assert.equal((await adjustDetail.item)._id, item._id);
                assert.equal(await (await adjustDetail.status).id, 'A');
                assert.equal((await adjustDetail.location)!._id, 1);

                assert.equal(JSON.stringify(await adjustLine.storedAttributes), '{"project":"AttPROJ","task":"Task1"}');
                assert.equal(JSON.stringify(await adjustLine.storedDimensions), '{"dimensionType02":"CHANNELVALUE1"}');

                const documentID = adjust._id;

                // rollbackCache function will flush deferred actions and saves
                await Test.rollbackCache(context);

                adjust = await context.read(xtremStock.nodes.StockAdjustment, { _id: documentID });
                adjustLine = await adjust.lines.elementAt(0);

                await testLib.functions.TestHelpers.processStockUpdate(
                    context,
                    adjust,
                    'adjust',
                    { movementType: 'adjustment' },
                    xtremStock.nodes.StockAdjustment.onStockReply,
                    [{ stockUpdateResultStatus: 'adjusted', stockTransactionStatus: 'succeeded' }],
                );

                await Test.rollbackCache(context);

                adjust = await context.read(xtremStock.nodes.StockAdjustment, { _id: documentID });
                adjustLine = await adjust.lines.elementAt(0);

                // FIXME:
                // assert.equal(adjustLine.stockTransactionStatus, 'completed');
                // assert.equal(adjust.stockTransactionStatus, 'completed');
                // assert.equal(adjust.status, 'closed');

                const newStockQuantity = (
                    await (
                        await xtremStockData.functions.stockLib.getStockRecord(context, stockSearchData)
                    )?.quantityInStockUnit
                )?.valueOf();

                const producedStockMovement = await getLastStockJournal(context, stockJournalSearchData);

                assert.equal(
                    (newStockQuantity ? +newStockQuantity : 0).valueOf(),
                    ((previousStockQuantity ? +previousStockQuantity : 0) + increasedQuantity).valueOf(),
                );
                assert.equal((await producedStockMovement?.documentLine)?._id, adjustLine._id);
                assert.isNotEmpty(await adjustLine.stockDetails.toArray());
            },
            {
                today: '2020-07-12',
            },
        ));

    it('Create an increase-quantity adjustment that creates a new stock', () =>
        Test.withContext(
            async context => {
                const site = await context.read(xtremSystem.nodes.Site, { id: 'US001' });
                const reason = await context.read(xtremMasterData.nodes.ReasonCode, { id: 'R1' });
                const item = await context.read(xtremMasterData.nodes.Item, { id: 'Chair' });
                const stockSearchData: xtremStockData.interfaces.StockSearchData = {
                    item: item._id,
                    location: (await context.read(xtremMasterData.nodes.Location, { _id: 1 }))._id,
                    lot: null,
                    status: (await context.read(xtremStockData.nodes.StockStatus, { id: 'A1' }))._id,
                    stockUnit: (await item.stockUnit)._id,
                    site: site._id,
                    owner: await site.id,
                };
                const increasedQuantity = 1;

                const stockRecord = await xtremStockData.functions.stockLib.getStockRecord(context, stockSearchData);
                const previousStockQuantity = (await stockRecord?.quantityInStockUnit)?.valueOf();

                let adjust = await context.create(xtremStock.nodes.StockAdjustment, {
                    stockSite: site,
                    reasonCode: reason,
                    lines: [
                        {
                            item,
                            quantityInStockUnit: 10,
                            adjustmentQuantityInStockUnit: increasedQuantity,
                            stockDetails: [
                                testLib.functions.TestHelpers.stockSearchDataToDetailCreateData<xtremStockData.nodes.StockAdjustmentDetail>(
                                    stockSearchData,
                                    {
                                        quantityInStockUnit: increasedQuantity,
                                        reasonCode: await context.read(xtremMasterData.nodes.ReasonCode, { id: 'R1' }),
                                        stockRecord,
                                    },
                                ),
                            ],
                            storedAttributes: {
                                project: 'AttPROJ',
                                task: '',
                                employee: '',
                            },
                            storedDimensions: {
                                dimensionType02: 'CHANNELVALUE1',
                                dimensionType03: 'DIMTYPE1VALUE1',
                                dimensionType04: 'DIMTYPE2VALUE2',
                            },
                        },
                    ],
                });

                const stockJournalSearchData = {
                    item: item._id,
                    site: site._id,
                    effectiveDate: await adjust.effectiveDate,
                };

                await adjust.$.save();

                const documentID = adjust._id;

                // rollbackCache function will flush deferred actions and saves
                await Test.rollbackCache(context);

                adjust = await context.read(xtremStock.nodes.StockAdjustment, { _id: documentID });

                await testLib.functions.TestHelpers.processStockUpdate(
                    context,
                    adjust,
                    'adjust',
                    { movementType: 'adjustment' },
                    xtremStock.nodes.StockAdjustment.onStockReply,
                    [{ stockUpdateResultStatus: 'adjusted', stockTransactionStatus: 'succeeded' }],
                );

                adjust = await context.read(xtremStock.nodes.StockAdjustment, { _id: adjust._id });
                const adjustLine = await adjust.lines.elementAt(0);

                assert.equal(
                    JSON.stringify(await adjustLine.storedAttributes),
                    '{"task":"","project":"AttPROJ","employee":""}',
                );
                assert.equal(
                    JSON.stringify(await adjustLine.storedDimensions),
                    '{"dimensionType02":"CHANNELVALUE1","dimensionType03":"DIMTYPE1VALUE1","dimensionType04":"DIMTYPE2VALUE2"}',
                );

                // FIXME:
                // assert.equal(adjustLine.stockTransactionStatus, 'completed');
                // assert.equal(adjust.stockTransactionStatus, 'completed');
                // assert.equal(adjust.status, 'closed');

                const newStockQuantity = (
                    await (
                        await xtremStockData.functions.stockLib.getStockRecord(context, stockSearchData)
                    )?.quantityInStockUnit
                )?.valueOf();

                const producedStockMovement = await getLastStockJournal(context, stockJournalSearchData);

                assert.equal(
                    (newStockQuantity ? +newStockQuantity : 0).valueOf(),
                    ((previousStockQuantity ? +previousStockQuantity : 0) + increasedQuantity).valueOf(),
                );
                assert.equal((await producedStockMovement?.documentLine)?._id, adjustLine._id);
                assert.isNotEmpty(await adjustLine.stockDetails.toArray());
            },
            {
                today: '2020-07-12',
            },
        ));

    it('Create an decrease-quantity adjustment, stock still exists after it', () =>
        Test.withContext(
            async context => {
                const site = await context.read(xtremSystem.nodes.Site, { id: 'US001' });
                const reason = await context.read(xtremMasterData.nodes.ReasonCode, { id: 'R2' });
                const item = await context.read(xtremMasterData.nodes.Item, { id: 'Chair' });
                const stockSearchData: xtremStockData.interfaces.StockSearchData = {
                    item: item._id,
                    location: (await context.read(xtremMasterData.nodes.Location, { _id: 1 }))._id,
                    lot: null,
                    status: (await context.read(xtremStockData.nodes.StockStatus, { id: 'A' }))._id,
                    stockUnit: (await item.stockUnit)._id,
                    site: site._id,
                    owner: await site.id,
                };
                const increasedQuantity = -1; // or decreasedQuantity = 1

                const stockRecord = await xtremStockData.functions.stockLib.getStockRecord(context, stockSearchData);
                const previousStockQuantity = (await stockRecord?.quantityInStockUnit)?.valueOf();

                let adjust = await context.create(xtremStock.nodes.StockAdjustment, {
                    stockSite: site,
                    reasonCode: reason,
                    lines: [
                        {
                            item,
                            quantityInStockUnit: 10,
                            adjustmentQuantityInStockUnit: -increasedQuantity,
                            stockDetails: [
                                testLib.functions.TestHelpers.stockSearchDataToDetailCreateData<xtremStockData.nodes.StockAdjustmentDetail>(
                                    stockSearchData,
                                    {
                                        quantityInStockUnit: increasedQuantity,
                                        reasonCode: await context.read(xtremMasterData.nodes.ReasonCode, { id: 'R1' }),
                                        stockRecord,
                                    },
                                ),
                            ],
                        },
                    ],
                });

                const stockJournalSearchData = {
                    item: item._id,
                    site: site._id,
                    effectiveDate: await adjust.effectiveDate,
                };

                await adjust.$.save();

                assert.instanceOf(adjust, xtremStock.nodes.StockAdjustment);
                assert.equal(await adjust.stockTransactionStatus, 'draft');
                assert.equal(await adjust.status, 'draft');
                assert.equal(await adjust.displayStatus, 'detailsEntered');

                assert.equal(await (await adjust.financialSite).id, await site.id);
                assert.equal(
                    await (
                        await adjust.transactionCurrency
                    ).id,
                    await (
                        await (
                            await (
                                await adjust.financialSite
                            ).businessEntity
                        ).currency
                    ).id,
                );
                assert.equal(await adjust.documentDate, await adjust.effectiveDate);

                let adjustLine = await adjust.lines.elementAt(0);
                assert.equal(await adjustLine.stockTransactionStatus, 'draft');
                assert.equal(await adjustLine.stockDetails.length, 1);
                assert.equal(await (await adjust.lines.elementAt(0)).stockDetailStatus, 'entered');
                assert.equal(await (await adjust.lines.elementAt(0)).displayStatus, 'detailsEntered');

                const documentID = adjust._id;

                // rollbackCache function will flush deferred actions and saves
                await Test.rollbackCache(context);

                adjust = await context.read(xtremStock.nodes.StockAdjustment, { _id: documentID });

                await testLib.functions.TestHelpers.processStockUpdate(
                    context,
                    adjust,
                    'adjust',
                    { movementType: 'adjustment' },
                    xtremStock.nodes.StockAdjustment.onStockReply,
                    [{ stockUpdateResultStatus: 'adjusted', stockTransactionStatus: 'succeeded' }],
                );

                adjust = await context.read(xtremStock.nodes.StockAdjustment, { _id: adjust._id });
                adjustLine = await adjust.lines.elementAt(0);

                // FIXME:
                // assert.equal(adjustLine.stockTransactionStatus, 'completed');
                // assert.equal(adjust.stockTransactionStatus, 'completed');
                // assert.equal(adjust.status, 'closed');

                const newStockQuantity = (
                    await (
                        await xtremStockData.functions.stockLib.getStockRecord(context, stockSearchData)
                    )?.quantityInStockUnit
                )?.valueOf();

                const producedStockMovement = await getLastStockJournal(context, stockJournalSearchData);

                assert.equal(
                    (newStockQuantity ? +newStockQuantity : 0).valueOf(),
                    ((previousStockQuantity ? +previousStockQuantity : 0) + increasedQuantity).valueOf(),
                );
                assert.equal((await producedStockMovement?.documentLine)?._id, adjustLine._id);
                assert.isNotEmpty(await adjustLine.stockDetails.toArray());
            },
            {
                today: '2020-07-12',
            },
        ));

    it('Create an decrease-quantity adjustment, no more stock exists after it', () =>
        Test.withContext(
            async context => {
                const site = await context.read(xtremSystem.nodes.Site, { id: 'US001' });
                const reason = await context.read(xtremMasterData.nodes.ReasonCode, { id: 'R2' });
                const item = await context.read(xtremMasterData.nodes.Item, { id: 'Chair' });
                const stockSearchData: xtremStockData.interfaces.StockSearchData = {
                    item: item._id,
                    location: (await context.read(xtremMasterData.nodes.Location, { _id: 1 }))._id,
                    lot: null,
                    status: (await context.read(xtremStockData.nodes.StockStatus, { id: 'A' }))._id,
                    stockUnit: (await item.stockUnit)._id,
                    site: site._id,
                    owner: await site.id,
                };

                let stockRecord = await xtremStockData.functions.stockLib.getStockRecord(context, stockSearchData);
                assert.isNotNull(stockRecord);
                const increasedQuantity = -(await stockRecord!.quantityInStockUnit).valueOf();
                const stockId = stockRecord?._id;

                let adjust = await context.create(xtremStock.nodes.StockAdjustment, {
                    stockSite: site,
                    reasonCode: reason,
                    lines: [
                        {
                            item,
                            quantityInStockUnit: 10,
                            adjustmentQuantityInStockUnit: -increasedQuantity,
                            stockDetails: [
                                testLib.functions.TestHelpers.stockSearchDataToDetailCreateData<xtremStockData.nodes.StockAdjustmentDetail>(
                                    stockSearchData,
                                    {
                                        quantityInStockUnit: increasedQuantity,
                                        reasonCode: await context.read(xtremMasterData.nodes.ReasonCode, { id: 'R1' }),
                                        stockRecord,
                                    },
                                ),
                            ],
                        },
                    ],
                });

                await adjust.$.save();

                assert.instanceOf(adjust, xtremStock.nodes.StockAdjustment);
                assert.equal(await adjust.stockTransactionStatus, 'draft');
                assert.equal(await adjust.status, 'draft');
                assert.equal(await adjust.displayStatus, 'detailsEntered');

                assert.equal(await (await adjust.financialSite).id, await site.id);
                assert.equal(
                    await (
                        await adjust.transactionCurrency
                    ).id,
                    await (
                        await (
                            await (
                                await adjust.financialSite
                            ).businessEntity
                        ).currency
                    ).id,
                );
                assert.equal(await adjust.documentDate, await adjust.effectiveDate);

                let adjustLine = await adjust.lines.elementAt(0);
                assert.equal(await adjustLine.stockTransactionStatus, 'draft');
                assert.equal(await adjustLine.stockDetails.length, 1);
                assert.equal(await adjustLine.displayStatus, 'detailsEntered');

                const documentID = adjust._id;

                // rollbackCache function will flush deferred actions and saves
                await Test.rollbackCache(context);

                adjust = await context.read(xtremStock.nodes.StockAdjustment, { _id: documentID });

                await testLib.functions.TestHelpers.processStockUpdate(
                    context,
                    adjust,
                    'adjust',
                    { movementType: 'adjustment' },
                    xtremStock.nodes.StockAdjustment.onStockReply,
                    [{ stockUpdateResultStatus: 'adjusted', stockTransactionStatus: 'succeeded' }],
                );

                adjust = await context.read(xtremStock.nodes.StockAdjustment, { _id: adjust._id });
                adjustLine = await adjust.lines.elementAt(0);

                // FIXME:
                // assert.equal(adjustLine.stockTransactionStatus, 'completed');
                // assert.equal(adjust.stockTransactionStatus, 'completed');
                // assert.equal(adjust.status, 'closed');

                stockRecord = await context.tryRead(xtremStockData.nodes.Stock, { _id: stockId });
                assert.isNull(stockRecord);
            },
            {
                today: '2020-07-12',
            },
        ));

    it('Create a cost valued adjustment fails for an item-site with standard cost', () =>
        Test.withContext(async context => {
            const site = await context.read(xtremSystem.nodes.Site, { id: 'US001' });
            const reason = await context.read(xtremMasterData.nodes.ReasonCode, { id: 'R3' });
            const item = await context.read(xtremMasterData.nodes.Item, { id: 'Chemical D' });
            const adjust = await context.create(xtremStock.nodes.StockValueCorrection, {
                stockSite: site,
                reasonCode: reason,
                lines: [
                    {
                        item,
                        newUnitCost: 20,
                        correctedDocumentLine: 1136,
                    },
                ],
            });

            await assert.isRejected(adjust.$.save());

            assert.deepEqual(adjust.$.context.diagnoses, [
                {
                    severity: 3,
                    path: ['lines', '-1000000002', 'item'],
                    message: 'The Chemical description D item must be linked to an average cost valuation method.',
                },
            ]);
        }));
    it('Verify chronological control between two stock adjustments on creation', () =>
        withSequenceNumberContext(
            'StockAdjustment',
            { isChronological: true },
            async context => {
                const site = await context.read(xtremSystem.nodes.Site, { id: 'US001' });
                const item = await context.read(xtremMasterData.nodes.Item, { id: 'Chair' });
                const stockSearchData: xtremStockData.interfaces.StockSearchData = {
                    item: item._id,
                    location: (await context.read(xtremMasterData.nodes.Location, { _id: '#LOC1|US001|Loading dock' }))
                        ._id,
                    lot: null,
                    status: (await context.read(xtremStockData.nodes.StockStatus, { id: 'A' }))._id,
                    stockUnit: (await item.stockUnit)._id,
                    site: site._id,
                    owner: await site.id,
                };
                const increasedQuantity = 1;

                const stockRecord = await xtremStockData.functions.stockLib.getStockRecord(context, stockSearchData);

                const newStockAdjustment = await context.create(xtremStock.nodes.StockAdjustment, {
                    stockSite: '#US001',
                    reasonCode: '#R1',
                    effectiveDate: date.make(2020, 7, 12),
                    lines: [
                        {
                            item,
                            quantityInStockUnit: 10,
                            adjustmentQuantityInStockUnit: increasedQuantity,
                            stockDetails: [
                                testLib.functions.TestHelpers.stockSearchDataToDetailCreateData<xtremStockData.nodes.StockAdjustmentDetail>(
                                    stockSearchData,
                                    {
                                        quantityInStockUnit: increasedQuantity,
                                        reasonCode: '#R1',
                                        stockRecord,
                                    },
                                ),
                            ],
                        },
                    ],
                });

                await newStockAdjustment.$.save({ flushDeferredActions: true });
                assert.deepEqual(newStockAdjustment.$.context.diagnoses, []);

                const newStockAdjustmentError = await context.create(xtremStock.nodes.StockAdjustment, {
                    stockSite: '#US001',
                    reasonCode: '#R1',
                    effectiveDate: date.make(2020, 7, 11),
                    lines: [
                        {
                            item,
                            quantityInStockUnit: 10,
                            adjustmentQuantityInStockUnit: increasedQuantity,
                            stockDetails: [
                                testLib.functions.TestHelpers.stockSearchDataToDetailCreateData<xtremStockData.nodes.StockAdjustmentDetail>(
                                    stockSearchData,
                                    {
                                        quantityInStockUnit: increasedQuantity,
                                        reasonCode: '#R1',
                                        stockRecord,
                                    },
                                ),
                            ],
                        },
                    ],
                });
                await assert.isRejected(
                    newStockAdjustmentError.$.save(),
                    'The document date 2020-07-11 is earlier than the previous document date 2020-07-12.',
                );
            },
            { today: '2020-07-12' },
        ));
    it('Create an adjustment and repost', () =>
        Test.withContext(
            async context => {
                const site = await context.read(xtremSystem.nodes.Site, { id: 'US001' });
                const reason = await context.read(xtremMasterData.nodes.ReasonCode, { id: 'R1' });
                const item = await context.read(xtremMasterData.nodes.Item, { id: 'Chair' });
                const stockSearchData: xtremStockData.interfaces.StockSearchData = {
                    item: item._id,
                    location: (await context.read(xtremMasterData.nodes.Location, { _id: 1 }))._id,
                    lot: null,
                    status: (await context.read(xtremStockData.nodes.StockStatus, { id: 'A' }))._id,
                    stockUnit: (await item.stockUnit)._id,
                    site: site._id,
                    owner: await site.id,
                };
                const increasedQuantity = 1;

                const stockRecord = await xtremStockData.functions.stockLib.getStockRecord(context, stockSearchData);

                const adjust = await context.create(xtremStock.nodes.StockAdjustment, {
                    stockSite: site,
                    reasonCode: reason,
                    lines: [
                        {
                            item,
                            quantityInStockUnit: 10,
                            adjustmentQuantityInStockUnit: increasedQuantity,
                            stockDetails: [
                                testLib.functions.TestHelpers.stockSearchDataToDetailCreateData<xtremStockData.nodes.StockAdjustmentDetail>(
                                    stockSearchData,
                                    {
                                        quantityInStockUnit: increasedQuantity,
                                        reasonCode: await context.read(xtremMasterData.nodes.ReasonCode, { id: 'R1' }),
                                        stockRecord,
                                    },
                                ),
                            ],
                        },
                    ],
                });

                await adjust.$.save({ flushDeferredActions: true });

                const adjustLines = await asyncArray(await adjust.lines.toArray())
                    .map(async line => {
                        return {
                            baseDocumentLineSysId: line._id,
                            storedAttributes:
                                (await line.storedAttributes) as xtremMasterData.interfaces.StoredAttributes,
                            storedDimensions: (await line.storedDimensions) || {},
                        };
                    })
                    .toArray();

                await assert.isRejected(
                    xtremStock.nodes.StockAdjustment.repost(context, adjust, adjustLines),
                    "You can only repost a stock adjustment if the status is 'Failed' or 'Not recorded'.",
                );
            },
            {
                today: '2020-07-12',
            },
        ));

    it('Repost an existing stock adjustment notRecorded in finance', () =>
        Test.withContext(
            async context => {
                const stockAdjustmentNumber = 'STOCKADJUSTMENT3';

                const stockAdjustment = await context.read(
                    xtremStock.nodes.StockAdjustment,
                    { number: stockAdjustmentNumber },
                    { forUpdate: true },
                );
                assert.equal(await stockAdjustment.financeIntegrationStatus, 'notRecorded');

                const stockAdjustmentLines = await asyncArray(await stockAdjustment.lines.toArray())
                    .map(async line => {
                        return {
                            baseDocumentLineSysId: line._id,
                            storedAttributes:
                                (await line.storedAttributes) as xtremMasterData.interfaces.StoredAttributes,
                            storedDimensions: (await line.storedDimensions) || {},
                        };
                    })
                    .toArray();

                const repostResult = await xtremStock.nodes.StockAdjustment.repost(
                    context,
                    stockAdjustment,
                    stockAdjustmentLines,
                );
                assert.equal(repostResult.wasSuccessful, true);
            },
            { today: '2024-01-11' },
        ));

    it('Delete line in error and document completed => finance notification', () =>
        Test.withContext(
            async context => {
                const stockAdjustmentNumber = 'TEST_STOCKADJUSTMENT1';

                const stockAdjustment = await context.read(
                    xtremStock.nodes.StockAdjustment,
                    { number: stockAdjustmentNumber },
                    { forUpdate: true },
                );
                assert.equal(await stockAdjustment.financeIntegrationStatus, 'toBeRecorded');
                assert.includeDeepMembers(
                    await stockAdjustment.lines
                        .map(async line => ({
                            _sortValue: await line._sortValue,
                            stockTransactionStatus: await line.stockTransactionStatus,
                        }))
                        .toArray(),
                    [
                        { _sortValue: 10, stockTransactionStatus: 'completed' },
                        { _sortValue: 20, stockTransactionStatus: 'error' },
                    ],
                );

                await stockAdjustment.$.set({ lines: [{ _action: 'delete', _sortValue: 20 }] });
                await stockAdjustment.$.save();

                const financeTransactions = await context
                    .query(xtremFinanceData.nodes.FinanceTransaction, {
                        filter: {
                            documentNumber: 'TEST_STOCKADJUSTMENT1',
                            documentType: 'stockAdjustment',
                            targetDocumentType: 'journalEntry',
                        },
                    })
                    .toArray();

                const accountingStagingRecords = await context
                    .query(xtremFinanceData.nodes.AccountingStaging, {
                        filter: {
                            documentNumber: 'TEST_STOCKADJUSTMENT1',
                            documentType: 'stockAdjustment',
                            targetDocumentType: 'journalEntry',
                        },
                    })
                    .toArray();

                assert.equal(financeTransactions.length, 1);
                assert.equal(accountingStagingRecords.length, 1);
                assert.equal(await stockAdjustment.financeIntegrationStatus, 'pending');
            },
            { today: '2025-02-27' },
        ));

    it('Create an increase-quantity adjustment that creates a new stock attribute type restricted to', () =>
        Test.withContext(
            async context => {
                const site = await context.read(xtremSystem.nodes.Site, { _id: '#US001' });
                const reason = await context.read(xtremMasterData.nodes.ReasonCode, { _id: '#R1' });
                const item = await context.read(xtremMasterData.nodes.Item, { _id: '#Chair' });
                const stockSearchData: xtremStockData.interfaces.StockSearchData = {
                    item: item._id,
                    location: (await context.read(xtremMasterData.nodes.Location, { _id: 1 }))._id,
                    lot: null,
                    status: (await context.read(xtremStockData.nodes.StockStatus, { _id: '#A1' }))._id,
                    stockUnit: (await item.stockUnit)._id,
                    site: site._id,
                    owner: await site.id,
                };
                const increasedQuantity = 1;
                const stockRecord = await xtremStockData.functions.stockLib.getStockRecord(context, stockSearchData);

                const adjust = await context.create(xtremStock.nodes.StockAdjustment, {
                    stockSite: site,
                    reasonCode: reason,
                    lines: [
                        {
                            item,
                            quantityInStockUnit: 10,
                            adjustmentQuantityInStockUnit: increasedQuantity,
                            stockDetails: [
                                testLib.functions.TestHelpers.stockSearchDataToDetailCreateData<xtremStockData.nodes.StockAdjustmentDetail>(
                                    stockSearchData,
                                    {
                                        quantityInStockUnit: increasedQuantity,
                                        reasonCode: await context.read(xtremMasterData.nodes.ReasonCode, { id: 'R1' }),
                                        stockRecord,
                                    },
                                ),
                            ],
                            storedAttributes: {
                                project: '',
                                task: 'TASK1',
                                employee: '',
                            },
                            storedDimensions: {
                                dimensionType02: 'CHANNELVALUE1',
                                dimensionType03: 'DIMTYPE1VALUE1',
                                dimensionType04: 'DIMTYPE2VALUE2',
                            },
                        },
                    ],
                });

                await assert.isRejected(adjust.$.save());
                assert.deepEqual(adjust.$.context.diagnoses, [
                    {
                        severity: 3,
                        path: ['lines', '-1000000002'],
                        message: 'The Project attribute needs to be filled in.',
                    },
                ]);
            },
            {
                today: '2020-07-12',
            },
        ));

    it('Create an increase-quantity adjustment that creates a new stock with default attributes', () =>
        Test.withContext(
            async context => {
                const site = await context.read(xtremSystem.nodes.Site, { id: 'RX001' });
                const reason = await context.read(xtremMasterData.nodes.ReasonCode, { id: 'R1' });
                const item = await context.read(xtremMasterData.nodes.Item, { id: 'HC001' }, { forUpdate: true });

                await item.$.set({
                    storedAttributes: { project: 'AttPROJ', employee: '', task: 'Task1' },
                });
                await item.$.save();

                const companyDefaultAttributeProject = await context.create(
                    xtremFinanceData.nodes.CompanyDefaultAttribute,
                    {
                        company: '#RX001',
                        attributeType: '#project',
                        dimensionDefinitionLevel: 'stockDirect',
                        masterDataDefault: 'item',
                    },
                );
                await companyDefaultAttributeProject.$.save({ flushDeferredActions: true });

                const companyDefaultAttributeTask = await context.create(
                    xtremFinanceData.nodes.CompanyDefaultAttribute,
                    {
                        company: '#RX001',
                        attributeType: '#task',
                        dimensionDefinitionLevel: 'stockDirect',
                        masterDataDefault: 'item',
                    },
                );
                await companyDefaultAttributeTask.$.save({ flushDeferredActions: true });

                const stockSearchData: xtremStockData.interfaces.StockSearchData = {
                    item: item._id,
                    location: null,
                    lot: null,
                    status: (await context.read(xtremStockData.nodes.StockStatus, { id: 'A1' }))._id,
                    stockUnit: (await item.stockUnit)._id,
                    site: site._id,
                    owner: await site.id,
                };
                const increasedQuantity = 1;

                const stockRecord = await xtremStockData.functions.stockLib.getStockRecord(context, stockSearchData);

                let adjust = await context.create(xtremStock.nodes.StockAdjustment, {
                    stockSite: site,
                    reasonCode: reason,
                    lines: [
                        {
                            item,
                            quantityInStockUnit: 10,
                            adjustmentQuantityInStockUnit: increasedQuantity,
                            stockDetails: [
                                testLib.functions.TestHelpers.stockSearchDataToDetailCreateData<xtremStockData.nodes.StockAdjustmentDetail>(
                                    stockSearchData,
                                    {
                                        quantityInStockUnit: increasedQuantity,
                                        reasonCode: await context.read(xtremMasterData.nodes.ReasonCode, { id: 'R1' }),
                                        stockRecord,
                                    },
                                ),
                            ],
                        },
                    ],
                });

                await adjust.$.save();
                assert.equal(await adjust.displayStatus, 'detailsEntered');
                assert.equal(await (await adjust.lines.elementAt(0)).stockDetailStatus, 'entered');
                assert.equal(await (await adjust.lines.elementAt(0)).displayStatus, 'detailsEntered');

                const documentID = adjust._id;

                // rollbackCache function will flush deferred actions and saves
                await Test.rollbackCache(context);

                adjust = await context.read(xtremStock.nodes.StockAdjustment, { _id: documentID });

                await testLib.functions.TestHelpers.processStockUpdate(
                    context,
                    adjust,
                    'adjust',
                    { movementType: 'adjustment' },
                    xtremStock.nodes.StockAdjustment.onStockReply,
                    [{ stockUpdateResultStatus: 'adjusted', stockTransactionStatus: 'succeeded' }],
                );

                adjust = await context.read(xtremStock.nodes.StockAdjustment, { _id: adjust._id });
                const adjustLine = await adjust.lines.elementAt(0);

                assert.deepEqual(
                    JSON.stringify(await adjustLine.storedAttributes),
                    JSON.stringify({
                        task: 'Task1',
                        project: 'AttPROJ',
                    }),
                );
            },
            {
                today: '2020-07-12',
            },
        ));

    it('Check onceStockCompleted is called only when the document is completed', () =>
        Test.withContext(async context => {
            const result = await xtremStockData.functions.testLib.testOnceStockCompleted(context, {
                clas: xtremStock.nodes.StockAdjustment,
                movementType: 'adjustment',
                documents: [
                    { key: { number: 'STOCKADJUSTMENT1' }, isCompleted: false },
                    { key: { number: 'STOCKADJUSTMENT2' }, isCompleted: true },
                    { key: { number: 'STOCKADJUSTMENT3' }, isCompleted: false },
                    { key: { number: 'STOCKADJUSTMENT4' }, isCompleted: true },
                ],
                returnedProperty: 'number',
            });

            assert.deepEqual(result, ['STOCKADJUSTMENT2', 'STOCKADJUSTMENT4']);
        }));
    it('Stock adjustment completed created stockTransactionStatus in progress', () =>
        Test.withContext(async context => {
            let stockAdjustment = await context.read(
                xtremStock.nodes.StockAdjustment,
                { number: 'STOCKADJUSTMENT3' },
                { forUpdate: true },
            );

            await stockAdjustment.lines.forEach(async line => {
                await line.$.set({ stockTransactionStatus: 'inProgress' });
            });

            await stockAdjustment.$.save();

            stockAdjustment = await context.read(
                xtremStock.nodes.StockAdjustment,
                { number: 'STOCKADJUSTMENT3' },
                { forUpdate: true },
            );

            assert.equal(await stockAdjustment.stockTransactionStatus, 'inProgress');

            // Fix statuses
            assert.equal(
                await xtremStock.nodes.StockAdjustment.resynchronizeStockTransactionStatus(context, stockAdjustment),
                true,
            );

            const stockAdjustmentFixed = await context.read(xtremStock.nodes.StockAdjustment, {
                number: 'STOCKADJUSTMENT3',
            });

            assert.equal(await stockAdjustmentFixed.stockTransactionStatus, 'completed'); // stock adjustment stock transaction status all statuses on lines must be complete
        }));

    it('Stock adjustment inProgress created stockTransactionStatus in progress', () =>
        Test.withContext(async context => {
            let stockAdjustment = await context.read(
                xtremStock.nodes.StockAdjustment,
                { number: 'STOCKADJUSTMENT3' },
                { forUpdate: true },
            );

            await stockAdjustment.lines.forEach(async line => {
                await line.$.set({ stockTransactionStatus: 'inProgress' });
                await line.stockTransactions.forEach(async stockTransaction => {
                    await stockTransaction.$.set({ status: 'inProgress' });
                });
            });
            await stockAdjustment.$.save();

            stockAdjustment = await context.read(
                xtremStock.nodes.StockAdjustment,
                { number: 'STOCKADJUSTMENT3' },
                { forUpdate: true },
            );

            assert.equal(await stockAdjustment.stockTransactionStatus, 'inProgress');

            // Check statuses
            await xtremStock.nodes.StockAdjustment.resynchronizeStockTransactionStatus(context, stockAdjustment);

            stockAdjustment = await context.read(xtremStock.nodes.StockAdjustment, { number: 'STOCKADJUSTMENT3' });
            assert.equal(await stockAdjustment.stockTransactionStatus, 'inProgress');
        }));
    it('Create an increase-quantity adjustment with no stock details', () =>
        Test.withContext(async context => {
            const site = await context.read(xtremSystem.nodes.Site, { id: 'US001' });
            const reason = await context.read(xtremMasterData.nodes.ReasonCode, { id: 'R1' });
            const item = await context.read(xtremMasterData.nodes.Item, { id: 'Chair' });
            const increasedQuantity = 1;

            const adjust = await context.create(xtremStock.nodes.StockAdjustment, {
                stockSite: site,
                reasonCode: reason,
                lines: [
                    {
                        item,
                        quantityInStockUnit: 10,
                        adjustmentQuantityInStockUnit: increasedQuantity,
                    },
                ],
            });

            await adjust.$.save({ flushDeferredActions: true });

            assert.equal(await adjust.displayStatus, 'detailsRequired');

            assert.equal(await (await adjust.lines.elementAt(0)).stockDetailStatus, 'required');
            assert.equal(await (await adjust.lines.elementAt(0)).displayStatus, 'detailsRequired');
        }));
    it('Stock adjustment completed created stockTransactionStatus in progress and create financial journals', () =>
        Test.withContext(async context => {
            let stockAdjustment = await context.read(
                xtremStock.nodes.StockAdjustment,
                { number: 'STOCKADJUSTMENT3' },
                { forUpdate: true },
            );

            await stockAdjustment.lines.forEach(async line => {
                await line.$.set({ stockTransactionStatus: 'inProgress' });
            });

            await stockAdjustment.$.save();

            stockAdjustment = await context.read(
                xtremStock.nodes.StockAdjustment,
                { number: 'STOCKADJUSTMENT3' },
                { forUpdate: true },
            );

            assert.equal(await stockAdjustment.stockTransactionStatus, 'inProgress');

            assert.equal(
                await xtremStock.nodes.StockAdjustment.resynchronizeStockTransactionStatus(context, stockAdjustment),
                true,
            );

            const stockAdjustmentFixed = await context.read(xtremStock.nodes.StockAdjustment, {
                number: 'STOCKADJUSTMENT3',
            });

            assert.equal(await stockAdjustmentFixed.stockTransactionStatus, 'completed');

            // Test to be done when this ticket is completed: [XT-76750] Move Accounting staging to Finance data package - Jira (sage.com)
            // assert.equal(
            //     await xtremStock.nodes.StockAdjustment.resendNotificationForFinance(context, stockAdjustment),
            //     true,
            // );
        }));
    it('Stock adjustment update fail - cannot update an adjusted stock adjustment line', () =>
        Test.withContext(async context => {
            const stockAdjustment = await context.read(
                xtremStock.nodes.StockAdjustment,
                { number: 'STOCKADJUSTMENT5' },
                { forUpdate: true },
            );

            const storedAttributes = {
                project: 'AttPROJ',
                task: '',
                employee: '',
            };
            const storedDimensions = {
                dimensionType02: 'CHANNELVALUE1',
                dimensionType03: 'DIMTYPE1VALUE1',
                dimensionType04: 'DIMTYPE2VALUE2',
            };

            await stockAdjustment.lines.forEach(async line => {
                // Check the dimensions where not already set with same values of the test
                assert.deepEqual(await line.storedAttributes, null);
                assert.deepEqual(await line.storedDimensions, { dimensionType01: '300' });
                await line.$.set({
                    storedAttributes,
                    storedDimensions,
                });
            });

            await assert.isRejected(stockAdjustment.$.save(), 'The record was not updated.');
            assert.equal(context.diagnoses.at(0)?.message, 'An Adjusted line cannot be updated.');

            await stockAdjustment.$.set({
                forceUpdateForFinance: true,
            });
            await stockAdjustment.$.save();
            await stockAdjustment.lines.forEach(async line => {
                assert.deepEqual(await line.storedAttributes, storedAttributes);
                assert.deepEqual(await line.storedDimensions, storedDimensions);
            });
        }));

    it('Stock adjustment update fail - cannot update the frozen property status on the stock adjustment', () =>
        Test.withContext(async context => {
            const stockAdjustment = await context.read(
                xtremStock.nodes.StockAdjustment,
                { number: 'STOCKADJUSTMENT5' },
                { forUpdate: true },
            );

            await assert.isRejected(
                stockAdjustment.$.set({ status: 'inProgress' }),
                'StockAdjustment.status: cannot set value on frozen property',
            );
        }));
});
