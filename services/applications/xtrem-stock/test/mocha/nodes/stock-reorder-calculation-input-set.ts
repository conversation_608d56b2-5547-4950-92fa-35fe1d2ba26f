import type { Context, NodeCreateData } from '@sage/xtrem-core';
import { Test, date } from '@sage/xtrem-core';
import { assert } from 'chai';
import * as xtremStock from '../../../lib';

describe('Stock reorder calculation', () => {
    type ExpectedResultType = {
        item: { id: string };
        site: { id: string };
        company: { id: string };
        quantity: number;
        orderDate: string;
        supplier: { id: string } | null;
        stockUnit: { id: string };
        purchaseUnit: { id: string };
        reorderType: 'production' | 'purchasing' | null;
        currency: { id: string; symbol: string };
    };

    type StockReorderCalculationInputSet = xtremStock.nodes.StockReorderCalculationInputSet;

    async function getStockReorderCalculation(
        context: Context,
        parameters: NodeCreateData<StockReorderCalculationInputSet>,
    ): Promise<xtremStock.nodes.StockReorderCalculationInputSet> {
        const inputSet: xtremStock.nodes.StockReorderCalculationInputSet = await context.create(
            xtremStock.nodes.StockReorderCalculationInputSet,
            parameters,
        );
        await inputSet.$.save();

        await xtremStock.nodes.StockReorderCalculationInputSet.reorderCalculation(context, context.userId.toString());

        return context.read(xtremStock.nodes.StockReorderCalculationInputSet, {
            user: context.userId.toString(),
        });
    }

    it('Calculation with company and end date', () =>
        Test.withContext(
            async context => {
                const expectedLine: ExpectedResultType = {
                    item: { id: '1324' },
                    site: { id: 'US001' },
                    company: { id: 'US001' },
                    quantity: 340,
                    orderDate: '2024-09-27',
                    supplier: null,
                    stockUnit: { id: 'LITER' },
                    purchaseUnit: { id: 'LITER' },
                    reorderType: 'production',
                    currency: { id: 'USD', symbol: '$' },
                };
                const calculationResults = await getStockReorderCalculation(context, {
                    company: 'US001',
                    endDate: date.today(),
                });

                assert.equal('US001', await (await calculationResults.company)?.id);
                assert.deepEqual(date.today(), await calculationResults.endDate);
                assert.isUndefined(await (await calculationResults.fromItem)?.id);
                assert.isUndefined(await (await calculationResults.toItem)?.id);

                const { lines } = calculationResults;
                const line1 = await lines.at(0);

                assert.equal(await lines.length, 46);

                // Testing 1st line
                const quantity = await line1?.quantity;
                const itemSite = await line1?.itemSite;
                assert.isNotEmpty(await line1?.orderDate);
                assert.equal(expectedLine.quantity, quantity);
                assert.equal(expectedLine.currency.id, await (await line1?.currency)?.id);
                assert.equal(expectedLine.orderDate, (await line1?.orderDate)?.toString());
                assert.equal(expectedLine.supplier?.id, await (await line1?.supplier)?.id);
                assert.equal(expectedLine.reorderType, await line1?.reorderType);
                assert.equal('byReorderPoint', await itemSite?.replenishmentMethod);
                assert.equal(expectedLine.stockUnit.id, await (await line1?.stockUnit)?.id);
                assert.equal(expectedLine.purchaseUnit.id, await (await line1?.purchaseUnit)?.id);
                assert.equal(expectedLine.site.id, await (await itemSite?.site)?.id);
                assert.equal(expectedLine.item.id, await (await itemSite?.item)?.id);
                assert.equal(expectedLine.company.id, await (await line1?.company)?.id);
                assert.deepEqual('completed', await calculationResults.status);
            },
            { today: '2024-09-27' },
        ));
});
