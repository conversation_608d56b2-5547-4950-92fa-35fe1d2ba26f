import type { Context, integer } from '@sage/xtrem-core';
import { asyncArray, date, Test } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremStockData from '@sage/xtrem-stock-data';
import * as xtremSystem from '@sage/xtrem-system';
import { assert } from 'chai';
import * as xtremStock from '../../../lib';
import { getLastStockJournal } from '../../../lib/functions/stock-journal-lib';
import * as testLib from '../../fixtures/lib';

const { withSequenceNumberContext } = xtremMasterData.functions.testLib;

/**
 * Check the serial numbers after a stock issue
 */
async function assertionsIssueOfSerialNumber(
    context: Context,
    issueLineId: integer,
    expectedSerialNumberIds: string[][],
) {
    // rollbackCache function will flush deferred actions and saves
    await Test.rollbackCache(context);

    const stockJournals = await context
        .query(xtremStockData.nodes.StockJournal, {
            filter: { documentLine: issueLineId },
            orderBy: { _id: +1 },
        })
        .toArray();

    assert.isNotEmpty(stockJournals);

    await asyncArray(stockJournals).forEach(async (stockJournal, index) => {
        const stockDetailSerialNumbers = ((await stockJournal.stockDetail) as xtremStockData.nodes.StockMovementDetail)
            ?.stockDetailSerialNumbers;
        assert.notDeepEqual(stockDetailSerialNumbers, undefined);
        if (!stockDetailSerialNumbers) return;

        assert.deepEqual(await stockDetailSerialNumbers.length, expectedSerialNumberIds[index].length);

        await stockDetailSerialNumbers.forEach(async stockDetailSerialNumber => {
            // check that all expected serial numbers are linked to the StockDetail of the StockJournal in StockDetailSerialNumber
            const serialNumber = await stockDetailSerialNumber.serialNumber;
            assert.isNotNull(serialNumber);
            if (!serialNumber) return;
            assert.include(expectedSerialNumberIds[index], await serialNumber.id);
            // Check properties of SerialNumber: no more in stock but still usable
            assert.isNull(await serialNumber.stockRecord);
            assert.isNull(await serialNumber.allocation);
            assert.deepEqual(await serialNumber.isInStock, false);
            assert.deepEqual(await serialNumber.isUsable, true);
            assert.deepEqual(
                (await (await serialNumber.stockMovements.elementAt(0)).stockMovement)._id,
                stockJournal._id,
            );
        });

        await stockJournal.serialNumbers.forEach(async stockJournalSerialNumber => {
            // check that all expected serial numbers are linked to the StockDetail of the StockJournal in StockDetailSerialNumber
            const serialNumber = await stockJournalSerialNumber.serialNumber;
            assert.isNotNull(serialNumber);
            if (!serialNumber) return;
            assert.include(expectedSerialNumberIds[index], await serialNumber.id);
        });
    });
}

/**
 * create a stock issue with 1 line by using the node FakeStockIssue
 * The line is allocated with serial numbers
 */
async function createAndAllocateStockIssue(
    context: Context,
    item: string,
    lineDetails: {
        stockRecord: integer; // stock line to allocate
        quantityInStockUnit: integer; // quantity to allocate
        serialNumberIds: Array<string> | undefined; // serial numbers to allocate
    }[],
) {
    // Create issue and all its vital children for the test
    const issue = await context.create(testLib.nodes.FakeStockIssue, {
        number: 'TEST',
        lines: [
            {
                lineNumber: 1,
                quantityInStockUnit: lineDetails.reduce((sum, lineDetail) => sum + lineDetail.quantityInStockUnit, 0),
                item,
            },
        ],
    });
    await issue.$.save();

    const issueLine = await issue.lines.elementAt(0);

    // generate serial numbers and allocate them
    await asyncArray(lineDetails).forEach(async lineDetail => {
        let serialNumbers: Array<xtremStockData.nodes.SerialNumber> | undefined;
        if (lineDetail.serialNumberIds && lineDetail.serialNumberIds.length > 0) {
            serialNumbers = await context
                .query(xtremStockData.nodes.SerialNumber, {
                    filter: { id: { _in: lineDetail.serialNumberIds } },
                })
                .toArray();

            assert.deepEqual(serialNumbers.length, lineDetail.serialNumberIds.length);
        }
        await xtremStockData.functions.allocationLib.createAllocation(context, {
            documentLine: issueLine as any,
            stockRecord: (await context.read(xtremStockData.nodes.Stock, {
                _id: lineDetail.stockRecord,
            })) as any,
            quantity: lineDetail.quantityInStockUnit,
            serialNumbers,
        });
    });
    return { issueId: issue._id, issueLineId: issueLine._id };
}

describe('StockIssue node', () => {
    it('Read test', () =>
        Test.withContext(
            async context => {
                const issue = await context.read(xtremStock.nodes.StockIssue, { number: 'MISC_ISSUE1' });

                assert.instanceOf(issue, xtremStock.nodes.StockIssue);
                assert.equal(await issue.number, 'MISC_ISSUE1');
                assert.equal((await issue.effectiveDate).toString(), '2020-05-10');
                assert.equal(await issue.displayStatus, 'detailsRequired');

                const issueLines = await issue.lines.toArray();
                assert.equal(await (await issueLines[0].item).id, 'SIMPLE_TEST');
                assert.equal(await issueLines[0].quantityInStockUnit, 10);
                assert.isNull(await issueLines[0].lot);
                assert.equal(await issueLines[0].displayStatus, 'detailsRequired');

                assert.equal(await (await issueLines[1].item).id, 'LOT_TEST');
                assert.equal(await issueLines[1].quantityInStockUnit, 15);
                assert.equal(await issueLines[1].displayStatus, 'detailsRequired');
            },
            { today: '2020-07-12' },
        ));

    it('Create issue', () =>
        Test.withContext(
            async context => {
                const site = await context.read(xtremSystem.nodes.Site, { id: 'US001' });
                const item = await context.read(xtremMasterData.nodes.Item, { id: 'ITME_LOT' });
                const stockSearchData: xtremStockData.interfaces.StockSearchData = {
                    item: item._id,
                    location: (
                        await context.read(xtremMasterData.nodes.Location, {
                            id: 'LOC1',
                            locationZone: '#US001|Loading dock',
                        })
                    )._id,
                    lot: (await context.read(xtremStockData.nodes.Lot, { id: 'LOT00001', item }))._id,
                    status: (await context.read(xtremStockData.nodes.StockStatus, { id: 'A' }))._id,
                    stockUnit: (await item.stockUnit)._id,
                    site: site._id,
                    owner: await site.id,
                };

                const stockRecord = await xtremStockData.functions.stockLib.getStockRecord(context, stockSearchData);

                const previousStockQuantity = (await stockRecord!.quantityInStockUnit).valueOf();

                let issue = await context.create(xtremStock.nodes.StockIssue, {
                    number: 'TEST',
                    effectiveDate: date.today(),
                    stockSite: site,
                    lines: [
                        {
                            item,
                            quantityInStockUnit: 15,
                            stockStatus: stockSearchData.status,
                            // try to set a wrong stockDetailStatus status
                            stockDetailStatus: 'notRequired',
                            stockDetails: [
                                {
                                    stockUnit: stockSearchData.stockUnit,
                                    quantityInStockUnit: -15,
                                    stockRecord,
                                },
                            ],
                            storedAttributes: {
                                project: 'AttPROJ',
                                task: '',
                                employee: '',
                            },
                            storedDimensions: {
                                dimensionType02: 'CHANNELVALUE1',
                                dimensionType03: 'DIMTYPE1VALUE1',
                                dimensionType04: 'DIMTYPE2VALUE2',
                            },
                        },
                    ],
                });

                const stockJournalSearchData = {
                    item: item._id,
                    site: site._id,
                    effectiveDate: await issue.effectiveDate,
                };

                await issue.$.save();

                assert.equal(await issue.displayStatus, 'detailsEntered');

                assert.equal(await (await issue.financialSite).id, await site.id);
                assert.equal(
                    await (
                        await issue.transactionCurrency
                    ).id,
                    await (
                        await (
                            await (
                                await issue.financialSite
                            ).businessEntity
                        ).currency
                    ).id,
                );
                assert.equal(await issue.documentDate, await issue.effectiveDate);
                assert.equal(await (await issue.lines.elementAt(0)).stockDetailStatus, 'entered');
                assert.equal(await (await issue.lines.elementAt(0)).displayStatus, 'detailsEntered');

                assert.equal(
                    JSON.stringify(await (await issue.lines.elementAt(0)).storedAttributes),
                    JSON.stringify({
                        project: 'AttPROJ',
                        task: '',
                        employee: '',
                    }),
                );
                assert.equal(
                    JSON.stringify(await (await issue.lines.elementAt(0)).storedDimensions),
                    JSON.stringify({
                        dimensionType02: 'CHANNELVALUE1',
                        dimensionType03: 'DIMTYPE1VALUE1',
                        dimensionType04: 'DIMTYPE2VALUE2',
                    }),
                );

                const documentID = issue._id;

                // rollbackCache function will flush deferred actions and saves
                await Test.rollbackCache(context);

                issue = await context.read(xtremStock.nodes.StockIssue, { _id: documentID });
                const firstIssueLine = await issue.lines.elementAt(0);
                const firstIssueDetail = await firstIssueLine.stockDetails.elementAt(0);

                await testLib.functions.TestHelpers.processStockUpdate(
                    context,
                    issue,
                    'issue',
                    { movementType: 'issue' },
                    xtremStock.nodes.StockIssue.onStockReply,
                    [{ stockUpdateResultStatus: 'decreased', stockTransactionStatus: 'succeeded' }],
                );

                const newStockQuantity = (
                    await (await xtremStockData.functions.stockLib.getStockRecord(context, stockSearchData))!
                        .quantityInStockUnit
                ).valueOf();

                const producedStockMovement = await getLastStockJournal(context, stockJournalSearchData);

                assert.instanceOf(issue, xtremStock.nodes.StockIssue);
                assert.equal(await (await firstIssueLine.item).id, 'ITME_LOT');
                assert.equal(await firstIssueLine.quantityInStockUnit, 15);

                assert.equal(
                    await firstIssueLine.item,
                    await firstIssueDetail.item,
                    'Item from detail does not equal item from line',
                );
                assert.equal((await firstIssueDetail.quantityInStockUnit).valueOf(), -15);

                assert.equal(
                    (newStockQuantity ? +newStockQuantity : 0).valueOf(),
                    (
                        (previousStockQuantity ? +previousStockQuantity : 0) -
                        (await firstIssueLine.quantityInStockUnit)
                    ).valueOf(),
                );
                assert.equal(await producedStockMovement?.sequence, 99999);
                assert.equal(await (await firstIssueLine.site).id, await (await issue.stockSite).id);
            },
            {
                today: '2020-07-12',
                testActiveServiceOptions: [xtremMasterData.serviceOptions.serialNumberOption],
            },
        ));

    it('Create issue with defaulted and given order cost', () =>
        Test.withContext(
            async context => {
                const site = await context.read(xtremSystem.nodes.Site, { id: 'US001' });
                const item = await context.read(xtremMasterData.nodes.Item, { id: 'Muesli' });
                const itemSite = await context.read(xtremMasterData.nodes.ItemSite, { site: site._id, item: item._id });
                const itemSiteCurrentCost = 14.4;
                const issueLineOrderCost = 14.5;

                const stockSearchData: xtremStockData.interfaces.StockSearchData = {
                    item,
                    location: await context.read(xtremMasterData.nodes.Location, {
                        id: 'LOC3',
                        locationZone: '#US001|Loading dock',
                    }),
                    lot: await context.read(xtremStockData.nodes.Lot, { id: 'LOTGE0000000001', item }),
                    status: await context.read(xtremStockData.nodes.StockStatus, { id: 'A1' }),
                    stockUnit: await item.stockUnit,
                    site,
                    owner: await site.id,
                };

                assert.equal(await itemSite.valuationMethod, 'standardCost');
                assert.equal(await itemSite.stdCostValue, itemSiteCurrentCost.valueOf());
                const stockRecord = await xtremStockData.functions.stockLib.getStockRecord(context, stockSearchData);

                let issue = await context.create(xtremStock.nodes.StockIssue, {
                    number: 'TEST',
                    effectiveDate: date.today(),
                    stockSite: site,
                    lines: [
                        {
                            item,
                            quantityInStockUnit: 10,
                            stockStatus: stockSearchData.status,
                            stockDetails: [
                                {
                                    stockUnit: stockSearchData.stockUnit,
                                    quantityInStockUnit: -10,
                                    stockRecord,
                                },
                            ],
                        },
                        {
                            item,
                            quantityInStockUnit: 20,
                            orderCost: issueLineOrderCost,
                            stockStatus: stockSearchData.status,
                            stockDetails: [
                                {
                                    stockUnit: stockSearchData.stockUnit,
                                    quantityInStockUnit: -20,
                                    stockRecord,
                                },
                            ],
                        },
                    ],
                });

                await issue.$.save();

                const documentID = issue._id;

                // rollbackCache function will flush deferred actions and saves
                await Test.rollbackCache(context);

                issue = await context.read(xtremStock.nodes.StockIssue, { _id: documentID });

                await testLib.functions.TestHelpers.processStockUpdate(
                    context,
                    issue,
                    'issue',
                    { movementType: 'issue' },
                    xtremStock.nodes.StockIssue.onStockReply,
                    [
                        { stockUpdateResultStatus: 'decreased', stockTransactionStatus: 'succeeded' },
                        { stockUpdateResultStatus: 'decreased', stockTransactionStatus: 'succeeded' },
                    ],
                );

                assert.instanceOf(issue, xtremStock.nodes.StockIssue);

                await Test.rollbackCache(context);

                issue = await context.read(xtremStock.nodes.StockIssue, { _id: documentID });

                // 1st line with defaulted order cost
                let issueLine = await issue.lines.elementAt(0);
                const expectedOrderCost = itemSiteCurrentCost.valueOf();

                let producedStockMovement =
                    (await context
                        .query(xtremStockData.nodes.StockJournal, {
                            filter: {
                                stockDetail: { documentLine: issueLine._id },
                            },
                        })
                        .at(0)) || null;

                assert.equal(await issueLine.orderCost, expectedOrderCost);
                assert.equal(await producedStockMovement?.orderCost, expectedOrderCost);

                assert.equal(JSON.stringify(await issueLine.storedAttributes), '{"task":"Task1","project":"AttPROJ"}');
                assert.equal(JSON.stringify(await issueLine.storedDimensions), '{"dimensionType02":"CHANNELVALUE1"}');

                // 2nd line with given order cost -> not taken into account, it's overloaded by the valuation method
                issueLine = await issue.lines.elementAt(1);

                producedStockMovement =
                    (await context
                        .query(xtremStockData.nodes.StockJournal, {
                            filter: {
                                stockDetail: { documentLine: issueLine._id },
                            },
                        })
                        .at(0)) || null;

                assert.equal(await issueLine.orderCost, expectedOrderCost);
                assert.equal(Number(await producedStockMovement?.orderCost), Number(expectedOrderCost));
            },
            {
                today: '2022-02-18',
                testActiveServiceOptions: [xtremMasterData.serviceOptions.serialNumberOption],
            },
        ));

    it('Create issue with sequence number for issue number', () =>
        Test.withContext(
            async context => {
                const site = await context.read(xtremSystem.nodes.Site, { id: 'US001' });
                const item = await context.read(xtremMasterData.nodes.Item, { id: 'ITME_LOT' });
                const status = await context.read(xtremStockData.nodes.StockStatus, { id: 'A' });

                const issue = await context.create(xtremStock.nodes.StockIssue, {
                    effectiveDate: date.today(),
                    stockSite: site,
                    lines: [
                        {
                            item,
                            quantityInStockUnit: 1,
                            stockStatus: status,
                        },
                    ],
                });

                await issue.$.save({ flushDeferredActions: true });
                assert.equal(await issue.displayStatus, 'detailsRequired');

                assert.equal(await (await issue.financialSite).id, await site.id);
                assert.equal(
                    await (
                        await issue.transactionCurrency
                    ).id,
                    await (
                        await (
                            await (
                                await issue.financialSite
                            ).businessEntity
                        ).currency
                    ).id,
                );
                assert.equal(await issue.documentDate, await issue.effectiveDate);

                assert.instanceOf(issue, xtremStock.nodes.StockIssue);
                assert.equal(
                    // Seq number component : MSI year sequenceNumber
                    await issue.number,
                    `SS200001`,
                );
            },
            {
                today: '2020-07-12',
                testActiveServiceOptions: [xtremMasterData.serviceOptions.serialNumberOption],
            },
        ));

    it('Create issue with multiple (2) lines', () =>
        Test.withContext(
            async context => {
                const site = await context.read(xtremSystem.nodes.Site, { id: 'US001' });
                const item = await context.read(xtremMasterData.nodes.Item, { id: 'ITME_LOT' });
                const stockSearchDataForLine1: xtremStockData.interfaces.StockSearchData = {
                    item: item._id,
                    location: (
                        await context.read(xtremMasterData.nodes.Location, {
                            id: 'LOC1',
                            locationZone: '#US001|Loading dock',
                        })
                    )._id,
                    lot: (await context.read(xtremStockData.nodes.Lot, { id: 'LOT00001', item }))._id,
                    status: (await context.read(xtremStockData.nodes.StockStatus, { id: 'A' }))._id,
                    stockUnit: (await item.stockUnit)._id,
                    site: site._id,
                    owner: await site.id,
                };

                const stockSearchDataForLine2 = {
                    ...stockSearchDataForLine1,
                    status: (await context.read(xtremStockData.nodes.StockStatus, { id: 'A1' }))._id,
                };

                const stockRecordForLine1 = await xtremStockData.functions.stockLib.getStockRecord(
                    context,
                    stockSearchDataForLine1,
                );
                const stockRecordForLine2 = await xtremStockData.functions.stockLib.getStockRecord(
                    context,
                    stockSearchDataForLine2,
                );

                const previousStockQuantityForLine1 = (
                    await (await xtremStockData.functions.stockLib.getStockRecord(context, stockSearchDataForLine1))!
                        .quantityInStockUnit
                ).valueOf();

                const previousStockQuantityForLine2 = (
                    await (await xtremStockData.functions.stockLib.getStockRecord(context, stockSearchDataForLine2))!
                        .quantityInStockUnit
                ).valueOf();

                let issue = await context.create(xtremStock.nodes.StockIssue, {
                    number: 'TEST',
                    effectiveDate: date.today(),
                    stockSite: site,
                    lines: [
                        {
                            item,
                            quantityInStockUnit: 15,
                            stockStatus: stockSearchDataForLine1.status,
                            stockDetails: [
                                {
                                    stockUnit: stockSearchDataForLine1.stockUnit,
                                    quantityInStockUnit: -15,
                                    stockRecord: stockRecordForLine1,
                                },
                            ],
                        },
                        {
                            item,
                            quantityInStockUnit: 15,
                            stockStatus: stockSearchDataForLine2.status,
                            stockDetails: [
                                {
                                    stockUnit: stockSearchDataForLine2.stockUnit,
                                    quantityInStockUnit: -15,
                                    stockRecord: stockRecordForLine2,
                                },
                            ],
                        },
                    ],
                });

                const stockJournalSearchData = {
                    item: item._id,
                    site: site._id,
                    effectiveDate: await issue.effectiveDate,
                };

                let firstIssueLine = await issue.lines.elementAt(0);

                let firstIssueDetail = await firstIssueLine.stockDetails.elementAt(0);

                await issue.$.save();

                assert.equal(await (await issue.financialSite).id, await site.id);
                assert.equal(
                    await (
                        await issue.transactionCurrency
                    ).id,
                    await (
                        await (
                            await (
                                await issue.financialSite
                            ).businessEntity
                        ).currency
                    ).id,
                );
                assert.equal(await issue.documentDate, await issue.effectiveDate);
                assert.equal(await (await firstIssueLine.site).id, await (await issue.stockSite).id);

                const documentID = issue._id;

                // rollbackCache function will flush deferred actions and saves
                await Test.rollbackCache(context);

                issue = await context.read(xtremStock.nodes.StockIssue, { _id: documentID });
                firstIssueLine = await issue.lines.elementAt(0);
                const secondIssueLine = await issue.lines.elementAt(1);
                firstIssueDetail = await firstIssueLine.stockDetails.elementAt(0);

                await testLib.functions.TestHelpers.processStockUpdate(
                    context,
                    issue,
                    'issue',
                    { movementType: 'issue' },
                    xtremStock.nodes.StockIssue.onStockReply,
                    [
                        { stockUpdateResultStatus: 'decreased', stockTransactionStatus: 'succeeded' },
                        { stockUpdateResultStatus: 'decreased', stockTransactionStatus: 'succeeded' },
                    ],
                );

                const newStockQuantityForLine1 = (
                    await (await xtremStockData.functions.stockLib.getStockRecord(context, stockSearchDataForLine1))!
                        .quantityInStockUnit
                ).valueOf();

                const newStockQuantityForLine2 = (
                    await (await xtremStockData.functions.stockLib.getStockRecord(context, stockSearchDataForLine2))!
                        .quantityInStockUnit
                ).valueOf();

                const lastProducedStockMovement = await getLastStockJournal(context, stockJournalSearchData);

                assert.instanceOf(issue, xtremStock.nodes.StockIssue);
                assert.equal(await (await firstIssueLine.item).id, 'ITME_LOT');
                assert.equal(await firstIssueLine.quantityInStockUnit, 15);

                assert.equal(
                    await firstIssueLine.item,
                    await firstIssueDetail.item,
                    'Item from detail does not equal item from line',
                );
                assert.equal((await firstIssueDetail.quantityInStockUnit).valueOf(), -15);

                assert.equal(
                    (newStockQuantityForLine1 ? +newStockQuantityForLine1 : 0).valueOf(),
                    (
                        (previousStockQuantityForLine1 ? +previousStockQuantityForLine1 : 0) -
                        (await firstIssueLine.quantityInStockUnit)
                    ).valueOf(),
                );

                assert.equal(
                    (newStockQuantityForLine2 ? +newStockQuantityForLine2 : 0).valueOf(),
                    (
                        (previousStockQuantityForLine2 ? +previousStockQuantityForLine2 : 0) -
                        (await secondIssueLine.quantityInStockUnit)
                    ).valueOf(),
                );
                assert.equal(await lastProducedStockMovement?.sequence, 99998);
            },
            { today: '2020-07-12', testActiveServiceOptions: [xtremMasterData.serviceOptions.serialNumberOption] },
        ));

    it('Verify chronological control between two stock issues on creation', () =>
        withSequenceNumberContext(
            'MiscStockIssue',
            { isChronological: true },
            async context => {
                const site = await context.read(xtremSystem.nodes.Site, { id: 'US001' });
                const item = await context.read(xtremMasterData.nodes.Item, { id: 'ITME_LOT' });
                const stockSearchData: xtremStockData.interfaces.StockSearchData = {
                    item: item._id,
                    location: (
                        await context.read(xtremMasterData.nodes.Location, {
                            id: 'LOC1',
                            locationZone: '#US001|Loading dock',
                        })
                    )._id,
                    lot: (await context.read(xtremStockData.nodes.Lot, { id: 'LOT00001', item }))._id,
                    status: (await context.read(xtremStockData.nodes.StockStatus, { id: 'A' }))._id,
                    stockUnit: (await item.stockUnit)._id,
                    site: site._id,
                    owner: await site.id,
                };

                const stockRecord = await xtremStockData.functions.stockLib.getStockRecord(context, stockSearchData);

                const newStockIssue = await context.create(xtremStock.nodes.StockIssue, {
                    effectiveDate: date.make(2020, 7, 12),
                    stockSite: site,
                    lines: [
                        {
                            item,
                            quantityInStockUnit: 15,
                            stockStatus: stockSearchData.status,
                            stockDetails: [
                                {
                                    stockUnit: stockSearchData.stockUnit,
                                    quantityInStockUnit: -15,
                                    stockRecord,
                                },
                            ],
                        },
                    ],
                });

                await newStockIssue.$.save({ flushDeferredActions: true });
                assert.deepEqual(newStockIssue.$.context.diagnoses, []);

                const newStockIssueError = await context.create(xtremStock.nodes.StockIssue, {
                    effectiveDate: date.make(2020, 7, 11),
                    stockSite: site,
                    lines: [
                        {
                            item,
                            quantityInStockUnit: 15,
                            stockStatus: stockSearchData.status,
                            stockDetails: [
                                {
                                    stockUnit: stockSearchData.stockUnit,
                                    quantityInStockUnit: -15,
                                    stockRecord,
                                },
                            ],
                        },
                    ],
                });
                await assert.isRejected(
                    newStockIssueError.$.save(),
                    'The document date 2020-07-11 is earlier than the previous document date 2020-07-12.',
                );
            },
            {
                today: '2020-07-12',
                testActiveServiceOptions: [xtremMasterData.serviceOptions.serialNumberOption],
            },
        ));

    it('Refuse issue with 2 lines and duplicate serial numbers', () =>
        Test.withContext(
            async context => {
                const site = await context.read(xtremSystem.nodes.Site, { id: 'US001' });
                const item = await context.read(xtremMasterData.nodes.Item, { id: 'ISSUESERIAL' });
                const issue = await context.create(xtremStock.nodes.StockIssue, {
                    number: 'TESTSERIAL',
                    effectiveDate: date.today(),
                    stockSite: site,
                    lines: [
                        {
                            item,
                            quantityInStockUnit: 1,
                            stockStatus: (await context.read(xtremStockData.nodes.StockStatus, { id: 'A' }))._id,
                            stockDetails: [
                                {
                                    stockUnit: (await item.stockUnit)._id,
                                    quantityInStockUnit: -1,
                                    stockRecord: 77,
                                    stockDetailSerialNumbers: [
                                        {
                                            stockDetail: -1,
                                            supplierSerialNumber: 'SN2',
                                            serialNumber: '#ISSUESERIAL|SSNR0006',
                                        },
                                    ],
                                },
                            ],
                        },
                        {
                            item,
                            quantityInStockUnit: 1,
                            stockStatus: (await context.read(xtremStockData.nodes.StockStatus, { id: 'A' }))._id,
                            stockDetails: [
                                {
                                    stockUnit: (await item.stockUnit)._id,
                                    quantityInStockUnit: -1,
                                    stockRecord: 77,
                                    stockDetailSerialNumbers: [
                                        {
                                            stockDetail: -1,
                                            supplierSerialNumber: 'SN2',
                                            serialNumber: '#ISSUESERIAL|SSNR0006',
                                        },
                                    ],
                                },
                            ],
                        },
                    ],
                });

                await assert.isRejected(
                    issue.$.save(),
                    'There are duplicate serial numbers. Make sure that each line has a unique set of serial numbers.',
                );
            },
            { today: '2020-07-12', testActiveServiceOptions: [xtremMasterData.serviceOptions.serialNumberOption] },
        ));

    it('Check issue of serial numbers allocated - issue & receipt', () =>
        Test.withContext(
            async context => {
                const lineDetails = [
                    {
                        stockRecord: 77,
                        quantityInStockUnit: 4,
                        serialNumberIds: ['SSNR0006', 'SSNR0007', 'SSNR0009', 'SSNR0010'],
                        expectedSerialNumberIds: ['SSNR0006', 'SSNR0007', 'SSNR0009', 'SSNR0010'],
                    },
                    {
                        stockRecord: 80,
                        quantityInStockUnit: 1,
                        serialNumberIds: ['SSNR0011'],
                        expectedSerialNumberIds: ['SSNR0011'],
                    },
                ];

                const { issueId, issueLineId } = await createAndAllocateStockIssue(
                    context,
                    '#ISSUESERIAL',
                    lineDetails,
                );

                // rollbackCache function will flush deferred actions and saves
                await Test.rollbackCache(context);

                const issue = await context.read(testLib.nodes.FakeStockIssue, { _id: issueId });

                /* does not throw */
                await testLib.functions.TestHelpers.processStockUpdate(
                    context,
                    issue,
                    'issue', // 'onStockIssueRequest',
                    {
                        movementType: 'issue',
                        stockDetailData: {
                            isDocumentWithoutStockDetails: true,
                        },
                        allocationData: {
                            isUsingAllocations: true,
                        },
                    },
                    testLib.nodes.FakeStockIssue.onStockReply,
                    [{ stockUpdateResultStatus: 'decreased', stockTransactionStatus: 'succeeded' }],
                );

                await assertionsIssueOfSerialNumber(
                    context,
                    issueLineId,
                    lineDetails.map(d => d.expectedSerialNumberIds),
                );
            },
            {
                testActiveServiceOptions: [xtremMasterData.serviceOptions.serialNumberOption],
            },
        ));

    it('Refuse stock issue with 1 line but no serial numbers', () =>
        Test.withContext(
            async context => {
                const site = await context.read(xtremSystem.nodes.Site, { id: 'US001' });
                const item = await context.read(xtremMasterData.nodes.Item, { id: 'ISSUESERIAL' });
                const issue = await context.create(xtremStock.nodes.StockIssue, {
                    number: 'TESTSERIAL',
                    effectiveDate: date.today(),
                    stockSite: site,
                    lines: [
                        {
                            item,
                            quantityInStockUnit: 1,
                            stockStatus: (await context.read(xtremStockData.nodes.StockStatus, { id: 'A' }))._id,
                            stockDetails: [
                                {
                                    stockUnit: (await item.stockUnit)._id,
                                    movementType: 'issue',
                                    quantityInStockUnit: -1,
                                    item,
                                    stockRecord: 77,
                                },
                            ],
                        },
                    ],
                });

                await assert.isRejected(
                    issue.$.save(),
                    'The number of assigned serial numbers needs to match the quantity for the line. Add or remove serial numbers to match the quantity.',
                );
            },
            { today: '2020-07-12', testActiveServiceOptions: [xtremMasterData.serviceOptions.serialNumberOption] },
        ));

    it('Allow issue with 2 lines and correct serial numbers', () =>
        Test.withContext(
            async context => {
                const site = await context.read(xtremSystem.nodes.Site, { id: 'US001' });
                const item = await context.read(xtremMasterData.nodes.Item, { id: 'ISSUESERIAL' });
                const issue = await context.create(xtremStock.nodes.StockIssue, {
                    number: 'TESTSERIAL',
                    effectiveDate: date.today(),
                    stockSite: site,
                    lines: [
                        {
                            item,
                            quantityInStockUnit: 1,
                            stockStatus: (await context.read(xtremStockData.nodes.StockStatus, { id: 'A' }))._id,
                            stockDetails: [
                                {
                                    stockUnit: (await item.stockUnit)._id,
                                    quantityInStockUnit: -1,
                                    stockRecord: 77,
                                    stockDetailSerialNumbers: [
                                        {
                                            stockDetail: -1,
                                            supplierSerialNumber: 'SN007',
                                            serialNumber: '#ISSUESERIAL|SSNR0015',
                                        },
                                    ],
                                },
                            ],
                        },
                        {
                            item,
                            quantityInStockUnit: 1,
                            stockStatus: (await context.read(xtremStockData.nodes.StockStatus, { id: 'A' }))._id,
                            stockDetails: [
                                {
                                    stockUnit: (await item.stockUnit)._id,
                                    quantityInStockUnit: -1,
                                    stockRecord: 77,
                                    stockDetailSerialNumbers: [
                                        {
                                            stockDetail: -1,
                                            supplierSerialNumber: 'SN008',
                                            serialNumber: '#ISSUESERIAL|SSNR0016',
                                        },
                                    ],
                                },
                            ],
                        },
                    ],
                });

                await issue.$.save();
                assert.deepEqual(issue!.$.context.diagnoses, []);
            },
            { today: '2020-07-12', testActiveServiceOptions: [xtremMasterData.serviceOptions.serialNumberOption] },
        ));

    it('create issue and repost', () =>
        Test.withContext(async context => {
            const site = await context.read(xtremSystem.nodes.Site, { id: 'US001' });
            const item = await context.read(xtremMasterData.nodes.Item, { id: 'ITME_LOT' });
            const stockSearchData: xtremStockData.interfaces.StockSearchData = {
                item: item._id,
                location: (
                    await context.read(xtremMasterData.nodes.Location, {
                        id: 'LOC1',
                        locationZone: '#US001|Loading dock',
                    })
                )._id,
                lot: (await context.read(xtremStockData.nodes.Lot, { id: 'LOT00001', item }))._id,
                status: (await context.read(xtremStockData.nodes.StockStatus, { id: 'A' }))._id,
                stockUnit: (await item.stockUnit)._id,
                site: site._id,
                owner: await site.id,
            };

            const stockRecord = await xtremStockData.functions.stockLib.getStockRecord(context, stockSearchData);

            const issue = await context.create(xtremStock.nodes.StockIssue, {
                number: 'TEST',
                effectiveDate: date.today(),
                stockSite: site,
                lines: [
                    {
                        item,
                        quantityInStockUnit: 15,
                        stockStatus: stockSearchData.status,
                        stockDetails: [
                            {
                                stockUnit: stockSearchData.stockUnit,
                                quantityInStockUnit: -15,
                                stockRecord,
                            },
                        ],
                    },
                ],
            });

            await issue.$.save();
            const issueLines = await asyncArray(await issue.lines.toArray())
                .map(async line => {
                    return {
                        baseDocumentLineSysId: line._id,
                        storedAttributes: (await line.storedAttributes) as xtremMasterData.interfaces.StoredAttributes,
                        storedDimensions: (await line.storedDimensions) || {},
                    };
                })
                .toArray();

            await assert.isRejected(
                xtremStock.nodes.StockIssue.repost(context, issue, issueLines),
                "You can only repost a stock issue if the status is 'Failed' or 'Not recorded'.",
            );
        }));

    it('Repost an existing stock issue notRecorded in finance', () =>
        Test.withContext(
            async context => {
                const stockIssueNumber = 'MISC_ISSUE9';

                const stockIssue = await context.read(
                    xtremStock.nodes.StockIssue,
                    { number: stockIssueNumber },
                    { forUpdate: true },
                );
                assert.equal(await stockIssue.financeIntegrationStatus, 'notRecorded');

                const stockIssueLines = await asyncArray(await stockIssue.lines.toArray())
                    .map(async line => {
                        return {
                            baseDocumentLineSysId: line._id,
                            storedAttributes:
                                (await line.storedAttributes) as xtremMasterData.interfaces.StoredAttributes,
                            storedDimensions: (await line.storedDimensions) || {},
                        };
                    })
                    .toArray();

                const repostResult = await xtremStock.nodes.StockIssue.repost(context, stockIssue, stockIssueLines);
                assert.equal(repostResult.wasSuccessful, true);
            },
            { today: '2024-01-11' },
        ));

    it('Create issue attribute type restricted to', () =>
        Test.withContext(
            async context => {
                const site = await context.read(xtremSystem.nodes.Site, { id: 'US001' });
                const item = await context.read(xtremMasterData.nodes.Item, { id: 'ITME_LOT' });
                const stockSearchData: xtremStockData.interfaces.StockSearchData = {
                    item: item._id,
                    location: (
                        await context.read(xtremMasterData.nodes.Location, {
                            id: 'LOC1',
                            locationZone: '#US001|Loading dock',
                        })
                    )._id,
                    lot: (await context.read(xtremStockData.nodes.Lot, { id: 'LOT00001', item }))._id,
                    status: (await context.read(xtremStockData.nodes.StockStatus, { id: 'A' }))._id,
                    stockUnit: (await item.stockUnit)._id,
                    site: site._id,
                    owner: await site.id,
                };
                const stockRecord = await xtremStockData.functions.stockLib.getStockRecord(context, stockSearchData);

                const issue = await context.create(xtremStock.nodes.StockIssue, {
                    number: 'TEST',
                    effectiveDate: date.today(),
                    stockSite: site,
                    lines: [
                        {
                            item,
                            quantityInStockUnit: 15,
                            stockStatus: stockSearchData.status,
                            // try to set a wrong stockDetailStatus status
                            stockDetailStatus: 'notRequired',
                            stockDetails: [
                                {
                                    stockUnit: stockSearchData.stockUnit,
                                    quantityInStockUnit: -15,
                                    stockRecord,
                                },
                            ],
                            storedAttributes: {
                                project: '',
                                task: 'TASK1',
                                employee: '',
                            },
                            storedDimensions: {
                                dimensionType02: 'CHANNELVALUE1',
                                dimensionType03: 'DIMTYPE1VALUE1',
                                dimensionType04: 'DIMTYPE2VALUE2',
                            },
                        },
                    ],
                });

                await assert.isRejected(issue.$.save());
                assert.deepEqual(issue.$.context.diagnoses, [
                    {
                        severity: 3,
                        path: ['lines', '-1000000002'],
                        message: 'The Project attribute needs to be filled in.',
                    },
                ]);
            },
            {
                today: '2020-07-12',
                testActiveServiceOptions: [xtremMasterData.serviceOptions.serialNumberOption],
            },
        ));

    it('Check onceStockCompleted is called only when the document is completed', () =>
        Test.withContext(async context => {
            const result = await xtremStockData.functions.testLib.testOnceStockCompleted(context, {
                clas: xtremStock.nodes.StockIssue,
                movementType: 'issue',
                documents: [
                    { key: { number: 'MISC_ISSUE6' }, isCompleted: false },
                    { key: { number: 'MISC_ISSUE9' }, isCompleted: true },
                    { key: { number: 'MISC_ISSUE7' }, isCompleted: false },
                ],
                returnedProperty: 'number',
            });

            assert.deepEqual(result, ['MISC_ISSUE9']);
        }));
});

describe('Posting details', () => {
    it('Gets posting details successfully', () =>
        Test.withContext(async context => {
            const siDocumentNumber = 'SS250001';
            const siDocument = await context.read(xtremStock.nodes.StockIssue, { number: siDocumentNumber });
            assert.equal(await siDocument.postingDetails.length, 1);

            const journalEntry = await siDocument.postingDetails.at(0);
            assert.equal(await journalEntry?.documentType, 'miscellaneousStockIssue');
            assert.equal(await journalEntry?.documentNumber, siDocumentNumber);
            assert.equal(await journalEntry?.documentSysId, siDocument._id);
            assert.equal(await journalEntry?.targetDocumentType, 'journalEntry');
        }));
});
