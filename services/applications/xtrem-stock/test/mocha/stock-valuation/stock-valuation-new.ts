import type { Context, integer, NodeCreateData } from '@sage/xtrem-core';
import { asyncArray, date, Test } from '@sage/xtrem-core';
import type * as xtremMasterData from '@sage/xtrem-master-data';
import { assert } from 'chai';
import * as xtremStock from '../../../lib';

interface ValuedItemSitesSearch extends xtremMasterData.interfaces.ValuedItemSitesSearch {
    postingClass?: integer;
}

// This is a copy from valued-item-site.ts
async function assertions(
    itemSiteList: xtremMasterData.nodes.ItemSite[],
    nbRecords: number,
    searchCriteria: xtremMasterData.interfaces.ValuedItemSitesSearch,
): Promise<void> {
    assert.deepEqual(itemSiteList.length, nbRecords, 'The number of records is different than expected');
    await asyncArray(itemSiteList).forEach(async itemSite => {
        assert.deepEqual(await (await itemSite.item).isStockManaged, true);

        // If company criteria is set, we don't care about sites
        if (searchCriteria.company)
            assert.deepEqual((await (await itemSite.site).legalCompany)._id, searchCriteria.company);
        else if (searchCriteria.stockSiteList)
            assert.include(
                searchCriteria.stockSiteList,
                await (
                    await itemSite.site
                ).id,
                `the site ${await (await itemSite.site).id} is not included in the list of criteria's sites ${
                    searchCriteria.stockSiteList
                }`,
            );

        if (searchCriteria.itemRange?.end)
            assert.deepEqual(
                (await (await itemSite.item).id) <= searchCriteria.itemRange.end,
                true,
                `${await (await itemSite.item).id} is greater than the upper bound ${searchCriteria.itemRange.end}`,
            );

        if (searchCriteria.itemRange?.start)
            assert.deepEqual(
                (await (await itemSite.item).id) >= searchCriteria.itemRange.start,
                true,
                `${await (await itemSite.item).id} is less than the lower bound ${searchCriteria.itemRange.start}`,
            );

        if (searchCriteria.statusList) assert.include(searchCriteria.statusList, await (await itemSite.item).status);
    });
}

// This is a copy from valued-item-site.ts + test case posting class.
describe('get list of valued item-sites', () => {
    const companyCriteria = 1;
    const itemRangeCriteria = { start: 'BPEC100ML', end: 'Chemical B' };
    const statusCriteria: xtremMasterData.enums.ItemStatus[] = ['active', 'notRenewed'];
    const siteListCriteria = ['US001', 'US004'];
    const valuationMethodCriteria: xtremMasterData.enums.CostValuationMethod[] = ['averageCost'];
    const itemCategoryCriteria = 4;
    const commodityCodeCriteria = 'xxx';
    const postingClassCriteria = 6;

    const testList: {
        title: string;
        nbRecords: number;
        searchCriteria: ValuedItemSitesSearch;
    }[] = [
        {
            title: 'get items-sites for a company',
            nbRecords: 140,
            searchCriteria: {
                company: companyCriteria,
            },
        },
        {
            title: 'get items-sites for 2 sites',
            nbRecords: 118,
            searchCriteria: {
                stockSiteList: siteListCriteria,
            },
        },
        {
            title: 'get items-sites for a range of products',
            nbRecords: 24,
            searchCriteria: {
                itemRange: itemRangeCriteria,
            },
        },
        {
            title: 'get items-sites for 2 product statuses',
            nbRecords: 294,
            searchCriteria: {
                statusList: statusCriteria,
            },
        },
        {
            title: 'get items-sites for 1 company + range of products + 2 product statuses',
            nbRecords: 11,
            searchCriteria: {
                company: companyCriteria,
                stockSiteList: siteListCriteria,
                itemRange: itemRangeCriteria,
                statusList: statusCriteria,
            },
        },
        {
            title: 'get items-sites for 1 company + valuation method',
            nbRecords: 28,
            searchCriteria: {
                company: companyCriteria,
                valuationMethods: valuationMethodCriteria,
            },
        },
        {
            title: 'get items-sites for a item category',
            nbRecords: 13,
            searchCriteria: {
                itemCategory: itemCategoryCriteria,
            },
        },
        {
            title: 'get items-sites for a commodity code',
            nbRecords: 0,
            searchCriteria: {
                commodityCode: commodityCodeCriteria,
            },
        },
        {
            title: 'get items-sites for an item category and a posting class',
            nbRecords: 5,
            searchCriteria: {
                itemCategory: itemCategoryCriteria,
                postingClass: postingClassCriteria,
            },
        },
    ];

    testList.forEach(element => {
        it(element.title, () =>
            Test.withContext(async context => {
                const itemSiteList = await xtremStock.nodes.StockValuationInputSet.getValuedItemSite(
                    context,
                    element.searchCriteria,
                );
                await assertions(itemSiteList, element.nbRecords, element.searchCriteria);
            }),
        );
    });
});

describe('Stock valuation', () => {
    type ExpectedResultType = {
        status: Awaited<xtremStock.nodes.StockValuationInputSet['status']>;
        lines: {
            item: { id: string };
            site: { id: string };
            stockValue: number;
            quantity: number;
            unitCost: number;
            valuationDate: date;
        }[];
    };

    async function testStockValuation(
        context: Context,
        parameters: NodeCreateData<xtremStock.nodes.StockValuationInputSet>,
        expectedResult: ExpectedResultType,
    ) {
        const inputSet: xtremStock.nodes.StockValuationInputSet = await context.create(
            xtremStock.nodes.StockValuationInputSet,
            parameters,
        );
        await inputSet.$.save();
        const userId = (await inputSet.user)._id;

        await xtremStock.nodes.StockValuationInputSet.stockValuation(context, userId.toString());

        await Test.rollbackCache(context);

        const result = (
            await context.select(
                xtremStock.nodes.StockValuationInputSet,
                {
                    status: true,
                    lines: {
                        item: { id: true },
                        site: { id: true },
                        stockValue: true,
                        quantity: true,
                        unitCost: true,
                        valuationDate: true,
                    },
                },
                { filter: { user: { _id: userId } } },
            )
        )[0];

        assert.deepEqual(result, expectedResult);
    }

    it('Selection with item category and valuation method', () =>
        Test.withContext(
            async context => {
                const valuationDate = date.today();
                const expectedResult: ExpectedResultType = {
                    status: 'completed',
                    lines: [
                        {
                            item: { id: 'Juice' },
                            site: { id: 'US001' },
                            stockValue: 0.0,
                            quantity: 2.0,
                            unitCost: 0.0,
                            valuationDate,
                        },
                        {
                            item: { id: 'Milk' },
                            site: { id: 'US001' },
                            stockValue: 0.0,
                            quantity: 200.0,
                            unitCost: 0.0,
                            valuationDate,
                        },
                        {
                            item: { id: 'Milk' },
                            site: { id: 'US002' },
                            stockValue: 10.08,
                            quantity: 9.0,
                            unitCost: 1.12,
                            valuationDate,
                        },
                    ],
                };

                await testStockValuation(
                    context,
                    {
                        company: '#US001',
                        date: valuationDate,
                        itemCategory: '#FOOD',
                        postingClass: '#TEST_ITEM',
                        valuationMethod: 'standardCost',
                    },
                    expectedResult,
                );
            },
            {
                today: '2022-08-09',
            },
        ));

    it('Stock valuation in the past with Stock journal to remove', () =>
        Test.withContext(async context => {
            // StockJournal records with effective date after 2024-05-20 must be removed from the stock valuation
            // Total
            const valuationDate = date.make(2024, 5, 20);
            const expectedResult: ExpectedResultType = {
                status: 'completed',
                lines: [
                    {
                        item: { id: 'STOAVC' },
                        site: { id: 'US001' },
                        stockValue: 8872.495,
                        quantity: 79.0,
                        unitCost: 112.3101,
                        valuationDate,
                    },
                ],
            };

            await testStockValuation(
                context,
                {
                    company: '#US001',
                    date: valuationDate,
                    fromItem: '#STOAVC',
                    toItem: '#STOAVC',
                },
                expectedResult,
            );
        }));

    describe('Get the same result for a same date (today or considered in the past)', () => {
        async function playTestOnUniqDate(context: Context) {
            const valuationDate = date.make(2024, 5, 21);
            const expectedResult: ExpectedResultType = {
                status: 'completed',
                lines: [
                    {
                        item: { id: 'STOAVC' },
                        site: { id: 'US001' },
                        stockValue: 12022.495,
                        quantity: 100.0,
                        unitCost: 120.225,
                        valuationDate,
                    },
                ],
            };

            await testStockValuation(
                context,
                {
                    company: '#US001',
                    date: valuationDate,
                    fromItem: '#STOAVC',
                    toItem: '#STOAVC',
                },
                expectedResult,
            );
        }
        it('Test value for 2024-05-21 considered in the past', () =>
            Test.withContext(
                async context => {
                    await playTestOnUniqDate(context);
                },
                { today: '2024-05-22' },
            ));

        it('Test value for 2024-05-21 considered as today', () =>
            Test.withContext(
                async context => {
                    await playTestOnUniqDate(context);
                },
                { today: '2024-05-21' },
            ));
    });
});
