import type { Context, NodeCreateData } from '@sage/xtrem-core';
import { asyncArray, date, Test } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremStockData from '@sage/xtrem-stock-data';
import * as xtremSystem from '@sage/xtrem-system';
import { assert } from 'chai';
import * as xtremStock from '../../../lib';
import * as testLib from '../../fixtures/lib';

// The goal is to test the function xtremStock.nodeExtensions.ItemSiteExtension.getStockValuation
// In testCaseXXX.input are defined the stock transactions to create (date, quantity and order price)
// In testCaseXXX.output are defined the desired results for different dates

const testCaseAverageCost = {
    input: [
        { type: 'receipt', effectiveDate: date.make(2021, 1, 1), quantityInStockUnit: 10, orderCost: 10 },
        { type: 'issue', effectiveDate: date.make(2021, 2, 2), quantityInStockUnit: -2 },
        { type: 'receipt', effectiveDate: date.make(2021, 3, 3), quantityInStockUnit: 12, orderCost: 12 },
        { type: 'receipt', effectiveDate: date.make(2021, 3, 3), quantityInStockUnit: 20, orderCost: 10 },
        { type: 'issue', effectiveDate: date.make(2021, 4, 4), quantityInStockUnit: -3 },
        { type: 'issue', effectiveDate: date.make(2021, 4, 4), quantityInStockUnit: -5 },
    ],
    output: [
        { stockValuationDate: date.make(2020, 12, 31), stockValue: 0, quantityInStockUnit: 0, unitCost: 0 },
        { stockValuationDate: date.make(2021, 1, 15), stockValue: 100, quantityInStockUnit: 10, unitCost: 10 },
        { stockValuationDate: date.make(2021, 2, 15), stockValue: 80, quantityInStockUnit: 8, unitCost: 10 },
        { stockValuationDate: date.make(2021, 3, 15), stockValue: 424, quantityInStockUnit: 40, unitCost: 10.6 },
        { stockValuationDate: date.make(2021, 4, 4), stockValue: 339.2, quantityInStockUnit: 32, unitCost: 10.6 },
        { stockValuationDate: undefined, stockValue: 339.2, quantityInStockUnit: 32, unitCost: 10.6 },
    ],
};

const testCaseStandardCost = {
    input: [
        { type: 'receipt', effectiveDate: date.make(2021, 7, 7), quantityInStockUnit: 10, orderCost: 10 },
        { type: 'issue', effectiveDate: date.make(2021, 8, 8), quantityInStockUnit: -2 },
        { type: 'receipt', effectiveDate: date.make(2022, 1, 10), quantityInStockUnit: 12, orderCost: 12 },
        { type: 'receipt', effectiveDate: date.make(2022, 1, 10), quantityInStockUnit: 20, orderCost: 10 },
        { type: 'issue', effectiveDate: date.make(2022, 1, 15), quantityInStockUnit: -3 },
        { type: 'issue', effectiveDate: date.make(2022, 1, 15), quantityInStockUnit: -5 },
    ],
    output: [
        { stockValuationDate: date.make(2021, 7, 1), stockValue: 0, quantityInStockUnit: 0, unitCost: 111.111 },
        { stockValuationDate: date.make(2021, 7, 15), stockValue: 1111.11, quantityInStockUnit: 10, unitCost: 111.111 },
        { stockValuationDate: date.make(2021, 8, 15), stockValue: 888.888, quantityInStockUnit: 8, unitCost: 111.111 },
        { stockValuationDate: date.make(2022, 1, 1), stockValue: 897.776, quantityInStockUnit: 8, unitCost: 112.222 },
        { stockValuationDate: date.make(2022, 1, 12), stockValue: 4488.88, quantityInStockUnit: 40, unitCost: 112.222 },
        {
            stockValuationDate: date.make(2022, 1, 20),
            stockValue: 3591.104,
            quantityInStockUnit: 32,
            unitCost: 112.222,
        },
        { stockValuationDate: undefined, stockValue: 3591.104, quantityInStockUnit: 32, unitCost: 112.222 },
    ],
};

const testCaseFifCost = {
    output: [
        { stockValuationDate: date.make(2023, 3, 15), stockValue: 0, quantityInStockUnit: 16, unitCost: 0 },
        { stockValuationDate: date.make(2023, 3, 18), stockValue: 3340, quantityInStockUnit: 48, unitCost: 69.58 },
        { stockValuationDate: date.make(2023, 3, 20), stockValue: 5360, quantityInStockUnit: 68, unitCost: 78.82 },
    ],
};

/**
 * Create a stock receipt with 1 stock line and 1 stock detail
 * @param context
 * @param stockData stock characteristics
 * @param additionalOrOverrideProperties : quantityInStockUnit, effectiveDate, orderCost, valuedCost...
 */
async function createSimpleReceipt(
    context: Context,
    stockData: xtremStockData.interfaces.StockSearchData,
    additionalOrOverrideProperties: NodeCreateData<xtremStockData.nodes.StockReceiptDetail>,
): Promise<void> {
    let receipt = await context.create(xtremStock.nodes.StockReceipt, {
        number: undefined,
        effectiveDate: additionalOrOverrideProperties.effectiveDate,
        stockSite: stockData.site,
        lines: [
            {
                item: stockData.item,
                stockStatus: stockData.status,
                quantityInStockUnit: additionalOrOverrideProperties.quantityInStockUnit,
                stockDetails: [
                    {
                        ...testLib.functions.TestHelpers.stockSearchDataToDetailCreateData(
                            stockData,
                            additionalOrOverrideProperties,
                        ),
                        effectiveDate: additionalOrOverrideProperties.effectiveDate,
                    },
                ],
            },
        ],
    });

    await receipt.$.save();

    const documentID = receipt._id;

    // rollbackCache function will flush deferred actions and saves
    await Test.rollbackCache(context);

    receipt = await context.read(xtremStock.nodes.StockReceipt, { _id: documentID });

    await testLib.functions.TestHelpers.processStockUpdate(
        context,
        receipt,
        'receive',
        { movementType: 'receipt' },
        xtremStock.nodes.StockReceipt.onStockReply,
        [{ stockUpdateResultStatus: 'increased', stockTransactionStatus: 'succeeded' }],
    );
}

/**
 * Create a stock issue with 1 stock line and 1 stock detail
 * @param context
 * @param stockData stock characteristics
 * @param additionalOrOverrideProperties : quantityInStockUnit (negative), effectiveDate, orderCost, valuedCost...
 */
async function createSimpleIssue(
    context: Context,
    stockSearchData: xtremStockData.interfaces.StockSearchData,
    additionalOrOverrideProperties: NodeCreateData<xtremStockData.nodes.StockIssueDetail>,
): Promise<void> {
    const stockRecord = await xtremStockData.functions.stockLib.getStockRecord(context, stockSearchData);

    let issue = await context.create(xtremStock.nodes.StockIssue, {
        number: undefined,
        effectiveDate: additionalOrOverrideProperties.effectiveDate,
        stockSite: stockSearchData.site,
        lines: [
            {
                item: stockSearchData.item,
                quantityInStockUnit: additionalOrOverrideProperties.quantityInStockUnit
                    ? additionalOrOverrideProperties.quantityInStockUnit * -1
                    : 0,
                stockStatus: stockSearchData.status,
                stockDetails: [
                    {
                        stockUnit: stockSearchData.stockUnit,
                        ...additionalOrOverrideProperties,
                        stockRecord,
                    },
                ],
            },
        ],
    });

    await issue.$.save();

    const documentID = issue._id;

    // rollbackCache function will flush deferred actions and saves
    await Test.rollbackCache(context);

    issue = await context.read(xtremStock.nodes.StockIssue, { _id: documentID });

    await testLib.functions.TestHelpers.processStockUpdate(
        context,
        issue,
        'receive',
        { movementType: 'issue' },
        xtremStock.nodes.StockIssue.onStockReply,
        [{ stockUpdateResultStatus: 'decreased', stockTransactionStatus: 'succeeded' }],
    );
}

describe('ItemSiteExtension', () => {
    // TODO: un-skip when XT-24936 / XT-25368 are fixed
    it.skip('Stock valuation at average cost', () =>
        Test.withContext(async context => {
            const site = await context.read(xtremSystem.nodes.Site, { id: 'US001' });
            const siteID = site._id;
            const item = await context.read(xtremMasterData.nodes.Item, { id: 'STOAVC' });
            const itemID = item._id;

            const stockSearchData: xtremStockData.interfaces.StockSearchData = {
                item: itemID,
                location: (await context.read(xtremMasterData.nodes.Location, { id: 'LOC3' }))._id,
                lot: null,
                status: (await context.read(xtremStockData.nodes.StockStatus, { id: 'A' }))._id,
                stockUnit: (await item.stockUnit)._id,
                site: siteID,
                owner: await site.id,
            };
            let itemSite: xtremMasterData.nodes.ItemSite;

            // Creation of stock transactions
            await asyncArray(testCaseAverageCost.input).forEach(async element => {
                const currentCost = (
                    await xtremStockData.nodeExtensions.ItemSiteExtension.getValuationCost(context, item, site, {
                        quantity: element.quantityInStockUnit,
                        dateOfValuation: element.effectiveDate,
                        valuationType: element.type === 'receipt' ? 'receipt' : 'issue',
                    })
                ).unitCost;
                itemSite = await context.read(xtremMasterData.nodes.ItemSite, { item: itemID, site: siteID });
                if (element.type === 'receipt')
                    await createSimpleReceipt(context, stockSearchData, {
                        ...element,
                        valuedCost: currentCost,
                    });
                else if (element.type === 'issue')
                    await createSimpleIssue(context, stockSearchData, {
                        ...element,
                        orderCost: currentCost,
                        valuedCost: currentCost,
                    });
            });

            // Test of stock valuation
            await asyncArray(testCaseAverageCost.output).forEach(async element => {
                itemSite = await context.read(xtremMasterData.nodes.ItemSite, { item: itemID, site: siteID });
                const stockValuationResult = await xtremStock.nodeExtensions.ItemSiteExtension.getStockValuation(
                    context,
                    {
                        itemSite: itemSite._id,
                        dateOfValuation: element.stockValuationDate,
                    },
                );
                assert.equal(stockValuationResult.stockValue.valueOf(), element.stockValue);
                assert.equal(stockValuationResult.quantityInStock.valueOf(), element.quantityInStockUnit);
                assert.equal(stockValuationResult.unitCost.valueOf(), element.unitCost);
            });
        }));

    // TODO: un-skip when XT-24936 / XT-25368 are fixed
    it.skip('Stock valuation at standard cost', () =>
        Test.withContext(async context => {
            const site = await context.read(xtremSystem.nodes.Site, { id: 'US001' });
            const siteID = site._id;
            const item = await context.read(xtremMasterData.nodes.Item, { id: 'STOSTD' });
            const itemID = item._id;

            const stockSearchData: xtremStockData.interfaces.StockSearchData = {
                item: itemID,
                location: (await context.read(xtremMasterData.nodes.Location, { id: 'LOC3' }))._id,
                lot: null,
                status: (await context.read(xtremStockData.nodes.StockStatus, { id: 'A' }))._id,
                stockUnit: (await item.stockUnit)._id,
                site: siteID,
                owner: await site.id,
            };

            let itemSite: xtremMasterData.nodes.ItemSite;

            // Creation of stock transactions
            await asyncArray(testCaseStandardCost.input).forEach(async element => {
                const currentCost = (
                    await xtremStockData.nodeExtensions.ItemSiteExtension.getValuationCost(context, item, site, {
                        quantity: element.quantityInStockUnit,
                        dateOfValuation: element.effectiveDate,
                        valuationType: element.type === 'receipt' ? 'receipt' : 'issue',
                    })
                ).unitCost;
                itemSite = await context.read(xtremMasterData.nodes.ItemSite, { item: itemID, site: siteID });
                if (element.type === 'receipt')
                    await createSimpleReceipt(context, stockSearchData, {
                        ...element,
                        valuedCost: currentCost,
                    });
                else if (element.type === 'issue')
                    await createSimpleIssue(context, stockSearchData, {
                        ...element,
                        orderCost: currentCost,
                        valuedCost: currentCost,
                    });
            });

            // Test of stock valuation
            await asyncArray(testCaseStandardCost.output).forEach(async element => {
                itemSite = await context.read(xtremMasterData.nodes.ItemSite, { item: itemID, site: siteID });
                const stockValuationResult = await xtremStock.nodeExtensions.ItemSiteExtension.getStockValuation(
                    context,
                    {
                        itemSite: itemSite._id,
                        dateOfValuation: element.stockValuationDate,
                    },
                );
                assert.equal(stockValuationResult.stockValue.valueOf(), element.stockValue);
                assert.equal(stockValuationResult.quantityInStock.valueOf(), element.quantityInStockUnit);
                assert.equal(stockValuationResult.unitCost.valueOf(), element.unitCost);
            });
        }));

    // TODO: As long as XT-24936, XT-25368 are not done we cannot test partly involved order amount on
    //       the last record for we sort _createDate backwards but the data input from CSV imports all
    //       stock journals with the same time stamp.
    it('Stock valuation at FIFO cost', () =>
        Test.withContext(async context => {
            const site = await context.read(xtremSystem.nodes.Site, { id: 'US001' });
            const siteID = site._id;
            const item = await context.read(xtremMasterData.nodes.Item, { id: 'STOFIFO' });
            const itemID = item._id;
            const itemSite = await context.read(xtremMasterData.nodes.ItemSite, { item: itemID, site: siteID });

            // Test of stock valuation
            await asyncArray(testCaseFifCost.output).forEach(async element => {
                const stockValuationResult = await xtremStock.nodeExtensions.ItemSiteExtension.getStockValuation(
                    context,
                    {
                        itemSite: itemSite._id,
                        dateOfValuation: element.stockValuationDate,
                    },
                );
                assert.equal(stockValuationResult.stockValue.valueOf(), element.stockValue);
                assert.equal(stockValuationResult.quantityInStock.valueOf(), element.quantityInStockUnit);
                assert.equal(Math.round(stockValuationResult.unitCost.valueOf() * 100) / 100, element.unitCost);
            });
        }));
});
