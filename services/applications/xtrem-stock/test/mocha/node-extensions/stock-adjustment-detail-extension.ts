import { Test } from '@sage/xtrem-core';
import { assert } from 'chai';
import * as xtremStock from '../../../lib';

describe('StockAdjustmentDetailExtension', () => {
    it('Check consistency between the stockDetail and the stockCountLine', () =>
        Test.withContext(async context => {
            const stockCount = await context.read(
                xtremStock.nodes.StockCount,
                { number: 'STOCK_COUNT03' },
                { forUpdate: true },
            );
            const stockCountLine = await stockCount.lines.elementAt(0);
            const stockDetail = await stockCountLine.stockDetail;

            assert.isNotNull(stockDetail);

            await stockDetail.$.set({
                item: '#LBHYDR100',
                site: '#US003',
                location: '#LOC5|US003|Production line',
                status: '#R1',
                stockUnit: '#PAIR',
                owner: 'wrong owner',
            });

            await assert.isRejected(stockDetail.$.save(), 'The record was not updated.');

            assert.deepEqual(context.diagnoses, [
                {
                    severity: 3,
                    path: ['site'],
                    message:
                        'The stock detail site and stock count line site need to be the same. Stock count STOCK_COUNT03, line 10.',
                },
                {
                    severity: 3,
                    path: ['item'],
                    message:
                        'The stock detail item and stock count line item need to be the same. Stock count STOCK_COUNT03, line 10.',
                },
                {
                    message:
                        'The stock unit for the stock detail and the stock count line need to be the same. Stock count STOCK_COUNT03, line 10.',
                    path: ['stockUnit'],
                    severity: 3,
                },
                {
                    message:
                        'The stock detail location and stock count line location need to be the same. Stock count STOCK_COUNT03, line 10.',
                    path: ['location'],
                    severity: 3,
                },
                {
                    message:
                        'The stock detail status and stock count line status need to be the same. Stock count STOCK_COUNT03, line 10.',
                    path: ['status'],
                    severity: 3,
                },
                {
                    message:
                        'The owner of the stock detail and the stock count line need to be the same. Stock count STOCK_COUNT03, line 10.',
                    path: ['owner'],
                    severity: 3,
                },
            ]);
        }));
});
