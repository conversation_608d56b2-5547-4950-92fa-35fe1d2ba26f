import { Test } from '@sage/xtrem-core';
import type * as xtremStockData from '@sage/xtrem-stock-data';
import { assert } from 'chai';
import * as fakePackage from '../../fixtures/lib';

describe('StockExtension', () => {
    it('should manage the posting of a stock transfer shipment', () =>
        Test.withContext(async context => {
            const shipmentNaturalKey = '#TEST_TRANSFER_SHIPMENT_4';

            // Read the transfer shipment
            let transferShipment = await context.read(fakePackage.nodes.FakeTransferShipment, {
                _id: shipmentNaturalKey,
            });

            // fake the post notification as it's impossible in mocha tests
            await fakePackage.functions.TestHelpers.processStockUpdate<xtremStockData.enums.StockMovementTypeEnum.transfer>(
                context,
                transferShipment,
                'transfer',
                {
                    movementType: 'transfer',
                    intersiteTransferData: {
                        type: 'internalShipment',
                    },
                    stockDetailData: { isDocumentWithoutStockDetails: true },
                    allocationData: { isUsingAllocations: true },
                },
                fakePackage.nodes.FakeTransferShipment.onStockReply,
                [{ stockUpdateResultStatus: 'changed', stockTransactionStatus: 'succeeded' }],
            );

            await Test.rollbackCache(context);

            transferShipment = await context.read(fakePackage.nodes.FakeTransferShipment, {
                _id: shipmentNaturalKey,
            });

            const firstLine = await transferShipment.lines.elementAt(0);

            assert.equal(await firstLine.stockDetails.length, 1);
            assert.equal(await firstLine.stockAllocations.length, 0);
            assert.equal(await firstLine.stockTransactions.length, 1);

            const firstStockDetailStockMovements = (
                await (await transferShipment.lines.elementAt(0)).stockDetails.elementAt(0)
            ).stockMovements;

            assert.equal(await firstStockDetailStockMovements.length, 1);

            const firstStockDetailStockMovement = await firstStockDetailStockMovements.elementAt(0);
            assert.equal(await firstStockDetailStockMovement.quantityInStockUnit, -1);
            assert.equal(await (await firstStockDetailStockMovement.site).id, 'CAS01');
            assert.equal(await (await firstStockDetailStockMovement.item).id, 'STOTRF');
            assert.equal(await (await firstStockDetailStockMovement.stockUnit).id, 'EACH');
            assert.equal(await (await firstStockDetailStockMovement.status)?.id, 'A1');

            assert.equal(await firstStockDetailStockMovement.movementType, 'transfer');
        }));

    it('should manage the posting of a stock transfer receipt', () =>
        Test.withContext(async context => {
            const receiptNaturalKey = '#TEST_TRANSFER_RECEIPT_1';

            // Read the transfer receipt
            let transferReceipt = await context.read(fakePackage.nodes.FakeTransferReceipt, {
                _id: receiptNaturalKey,
            });

            // fake the post notification as it's impossible in mocha tests
            await fakePackage.functions.TestHelpers.processStockUpdate<xtremStockData.enums.StockMovementTypeEnum.transfer>(
                context,
                transferReceipt,
                'transfer',
                {
                    movementType: 'transfer',
                    intersiteTransferData: {
                        type: 'internalReceipt',
                    },
                },
                fakePackage.nodes.FakeTransferReceipt.onStockReply,
                [{ stockUpdateResultStatus: 'changed', stockTransactionStatus: 'succeeded' }],
            );

            await Test.rollbackCache(context);

            transferReceipt = await context.read(fakePackage.nodes.FakeTransferReceipt, {
                _id: receiptNaturalKey,
            });

            const firstLine = await transferReceipt.lines.elementAt(0);

            assert.equal(await firstLine.stockDetails.length, 1);
            assert.equal(await firstLine.stockTransactions.length, 1);

            const firstStockDetailStockMovements = (
                await (await transferReceipt.lines.elementAt(0)).stockDetails.elementAt(0)
            ).stockMovements;

            assert.equal(await firstStockDetailStockMovements.length, 1);

            const firstStockDetailStockMovement = await firstStockDetailStockMovements.elementAt(0);
            assert.equal(await firstStockDetailStockMovement.quantityInStockUnit, 1);
            assert.equal(await (await firstStockDetailStockMovement.site).id, 'CAS02');
            assert.equal(await (await firstStockDetailStockMovement.item).id, 'STOTRF');
            assert.equal(await (await firstStockDetailStockMovement.stockUnit).id, 'EACH');
            assert.equal(await (await firstStockDetailStockMovement.status)?.id, 'A2');

            assert.equal(await firstStockDetailStockMovement.movementType, 'transfer');
        }));
});
