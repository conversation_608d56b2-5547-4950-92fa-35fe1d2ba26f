import type { Context } from '@sage/xtrem-core';
import { date, Test } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremSystem from '@sage/xtrem-system';
import * as xtremTechnicalData from '@sage/xtrem-technical-data';
import { assert } from 'chai';
import * as xtremStock from '../../../lib';

describe('Item site costs extension query ', () => {
    it('Create Item site extension cost', () =>
        Test.withContext(async context => {
            const itemSiteCost = await context.create(xtremMasterData.nodes.ItemSiteCost, {
                itemSite: '#Milk|US002',
                costCategory: '#Standard',
                fromDate: date.today(),
                materialCost: 12,
            });
            await itemSiteCost.$.save();
            assert.deepEqual(itemSiteCost.$.context.diagnoses, []);
            assert.equal((await itemSiteCost.toDate).value, 99991231);

            const itemSiteCost2 = await context.read(xtremMasterData.nodes.ItemSiteCost, { _id: itemSiteCost._id });
            assert.isNotNull(itemSiteCost2);
            assert.isNotNull(await itemSiteCost2.stockValueChange);
            if (await itemSiteCost2.stockValueChange) {
                const stockValueChange = await context.read(xtremStock.nodes.StockValueChange, {
                    _id: String((await itemSiteCost2.stockValueChange)!._id),
                });
                assert.isNotNull(stockValueChange);
                if (stockValueChange) {
                    assert.equal((await stockValueChange.item)._id, (await (await itemSiteCost2.itemSite).item)._id);
                    const line = (await stockValueChange.lines.toArray())[0];
                    assert.equal(await line.quantity, 9);
                    assert.equal(await line.newUnitCost, 3);
                    assert.equal(await line.newAmount, 27);
                }
            }
        }));

    it('Create Item site extension cost with missing dimension', () =>
        Test.withContext(
            async context => {
                const company = await context.read(xtremSystem.nodes.Company, { id: 'US001' }, { forUpdate: true });

                await company.$.set({ dimensionTypes: [{ dimensionType: 1, isRequired: true }] });
                await company.$.save();

                const itemSiteCost = await context.create(xtremMasterData.nodes.ItemSiteCost, {
                    itemSite: '#Milk|US001',
                    costCategory: '#Standard',
                    fromDate: date.today(),
                    materialCost: 12,
                });
                await assert.isRejected(
                    itemSiteCost.$.save(),
                    '* You need to select the Company dimension [Department] for this item: A bottle of milk on document: SVC230001 .',
                );

                const itemSiteCost1 = await context.create(xtremMasterData.nodes.ItemSiteCost, {
                    itemSite: '#Milk|US001',
                    costCategory: '#Standard',
                    fromDate: date.today().addDays(10),
                    materialCost: 12,
                });
                await assert.isRejected(
                    itemSiteCost1.$.save(),
                    'You need to select the Company dimension [Department] for this item: A bottle of milk on document:  .',
                );
            },
            { today: '2023-11-13' },
        ));
});

describe('Item site costs calculation', () => {
    type SubAssemblyLine = {
        componentNumber: Awaited<xtremStock.nodes.CostRollUpSubAssembly['componentNumber']>;
        item: { id: Awaited<Awaited<xtremStock.nodes.CostRollUpSubAssembly['item']>['id']> };
        calculationQuantity: Awaited<xtremStock.nodes.CostRollUpSubAssembly['calculationQuantity']>;
        materialCost: Awaited<xtremStock.nodes.CostRollUpSubAssembly['materialCost']>;
        machineCost: Awaited<xtremStock.nodes.CostRollUpSubAssembly['materialCost']>;
        laborCost: Awaited<xtremStock.nodes.CostRollUpSubAssembly['materialCost']>;
        toolCost: Awaited<xtremStock.nodes.CostRollUpSubAssembly['materialCost']>;
        totalCost: Awaited<xtremStock.nodes.CostRollUpSubAssembly['materialCost']>;
    };

    type ResultLine = {
        item: { id: Awaited<Awaited<xtremStock.nodes.CostRollUpResultLine['item']>['id']> };
        materialCost: Awaited<xtremStock.nodes.CostRollUpResultLine['materialCost']>;
        machineCost: Awaited<xtremStock.nodes.CostRollUpResultLine['machineCost']>;
        laborCost: Awaited<xtremStock.nodes.CostRollUpResultLine['laborCost']>;
        toolCost: Awaited<xtremStock.nodes.CostRollUpResultLine['toolCost']>;
        totalCost: Awaited<xtremStock.nodes.CostRollUpResultLine['totalCost']>;
        subAssemblyLines: SubAssemblyLine[];
    };

    function assertCost(actualResults: ResultLine[], expectedResults: ResultLine[]) {
        assert.deepEqual(actualResults.length, expectedResults.length);

        expectedResults.forEach(expectedResultLine => {
            const message = `item=${expectedResults}`;
            const actualResultLine = actualResults.find(line => line.item.id === expectedResultLine.item.id);
            assert.isDefined(actualResultLine, message);
            if (!actualResultLine) return;

            assert.deepEqual(Number(actualResultLine.materialCost), Number(expectedResultLine.materialCost), message);
            assert.deepEqual(Number(actualResultLine.machineCost), Number(expectedResultLine.machineCost), message);
            assert.deepEqual(Number(actualResultLine.laborCost), Number(expectedResultLine.laborCost), message);
            assert.deepEqual(Number(actualResultLine.toolCost), Number(expectedResultLine.toolCost), message);
            assert.deepEqual(Number(actualResultLine.totalCost), Number(expectedResultLine.totalCost), message);

            assert.deepEqual(
                actualResultLine.subAssemblyLines.length,
                expectedResultLine.subAssemblyLines.length,
                message,
            );
            expectedResultLine.subAssemblyLines.forEach(expectedSubAssemblyLine => {
                const actualSubAssemblyLine = actualResultLine.subAssemblyLines.find(
                    line => line.componentNumber === expectedSubAssemblyLine.componentNumber,
                );
                if (!actualSubAssemblyLine) return;
                const messageSub = `subassembly=${expectedSubAssemblyLine.item.id}`;

                assert.deepEqual(
                    Number(actualSubAssemblyLine.item.id),
                    Number(expectedSubAssemblyLine.item.id),
                    `${message} ${messageSub}`,
                );
                assert.deepEqual(
                    Number(actualSubAssemblyLine.calculationQuantity),
                    Number(expectedSubAssemblyLine.calculationQuantity),
                    `${message} ${messageSub}`,
                );
                assert.deepEqual(
                    Number(actualSubAssemblyLine.materialCost),
                    Number(expectedSubAssemblyLine.materialCost),
                    `${message} ${messageSub}`,
                );
                assert.deepEqual(
                    Number(actualSubAssemblyLine.machineCost),
                    Number(expectedSubAssemblyLine.machineCost),
                    `${message} ${messageSub}`,
                );
                assert.deepEqual(
                    Number(actualSubAssemblyLine.laborCost),
                    Number(expectedSubAssemblyLine.laborCost),
                    `${message} ${messageSub}`,
                );
                assert.deepEqual(
                    Number(actualSubAssemblyLine.toolCost),
                    Number(expectedSubAssemblyLine.toolCost),
                    `${message} ${messageSub}`,
                );
                assert.deepEqual(
                    Number(actualSubAssemblyLine.totalCost),
                    Number(expectedSubAssemblyLine.totalCost),
                    `${message} ${messageSub}`,
                );
            });
        });
    }

    function getBomByName(context: Context, name: string) {
        return context
            .query(xtremTechnicalData.nodes.BillOfMaterial, {
                filter: {
                    name,
                },
                first: 1,
            })
            .elementAt(0);
    }

    it('prepareItemSiteFilter', () =>
        Test.withContext(async context => {
            const item = await context.read(xtremMasterData.nodes.Item, { id: 'COST_C' });
            const itemId = item._id;

            const site = await context.read(xtremSystem.nodes.Site, { id: 'US001' });

            // use as any here to allow to call directly the private function
            const result = (xtremStock.nodeExtensions.ItemSiteCostExtension as any).prepareItemSiteFilter({
                site,
                itemIds: [itemId],
                itemCategory: null,
            });

            assert.deepEqual(result, {
                site: {
                    _id: site._id,
                },
                item: {
                    _id: {
                        _in: [itemId],
                    },
                    isActive: true,
                    isManufactured: true,
                },
            });
        }));

    it('getCostRollUpResultLineCreateData', () =>
        Test.withContext(
            async context => {
                const fromDate = date.make(2023, 12, 1);

                const item = await context.read(xtremMasterData.nodes.Item, { id: 'STOSTD' });
                const itemId = item._id;

                const site = '#US001';

                const inputSet = await context.create(xtremStock.nodes.CostRollUpInputSet, {
                    user: '<EMAIL>',
                    site,
                    // '#STOSTD' doesn't work in reference array => use of _id
                    items: [itemId],
                    fromDate,
                    includesRouting: true,
                    usesComponentStandardCost: true,
                    quantity: 5,
                });

                await inputSet.$.save();

                const inputSetId = inputSet._id;
                const itemSiteId = (
                    await context
                        .query(xtremMasterData.nodes.ItemSite, {
                            filter: {
                                item,
                                site,
                            },
                            first: 1,
                        })
                        .elementAt(0)
                )._id;

                // use as any here to allow to call directly the private function
                const result = await (
                    xtremStock.nodeExtensions.ItemSiteCostExtension as any
                ).getCostRollUpResultLineCreateData(context, {
                    fromDate,
                    inputSetId,
                    itemId,
                    itemSiteId,
                });

                assert.deepEqual(result, {
                    inputSet: inputSetId,
                    item: itemId,
                    currentMaterialCost: 33.0,
                    currentLaborCost: 1000.0,
                    currentMachineCost: 100.0,
                    currentToolCost: 0.3,
                    currentTotalCost: 1133.3,
                });
            },
            { today: '2023-11-13' },
        ));

    it('computeItemRoutingCostsAtRequiredQuantity', () =>
        Test.withContext(async context => {
            const item = await context.read(xtremMasterData.nodes.Item, { id: 'COST_C' });

            const site = await context.read(xtremSystem.nodes.Site, { id: 'US001' });

            const itemSite = await context
                .query(xtremMasterData.nodes.ItemSite, {
                    filter: {
                        item,
                        site,
                    },
                    first: 1,
                })
                .elementAt(0);

            // use as any here to allow to call directly the private function
            const result = await (
                xtremStock.nodeExtensions.ItemSiteCostExtension as any
            ).computeItemRoutingCostsAtRequiredQuantity({
                itemSite,
                requiredQuantity: 10.0,
                currentItemCostSums: {
                    unitCost: 0.0,
                    materialCost: 0.0,
                    laborCost: 0.0,
                    machineCost: 0.0,
                    toolCost: 0.0,
                    totalCost: 0.0,
                    hasResultLine: false,
                } as xtremStock.interfaces.CostSumObject,
            });

            assert.deepEqual(result, {
                unitCost: 0.0,
                materialCost: 0.0,
                laborCost: 960.0,
                machineCost: 57.5,
                toolCost: 10.62,
                totalCost: 0.0,
                hasResultLine: false,
            });
        }));

    it('getCostsFromItemSite', () =>
        Test.withContext(async context => {
            const fromDate = date.make(2023, 12, 1);

            const item = await context.read(xtremMasterData.nodes.Item, { id: 'STOSTD' });

            const site = await context.read(xtremSystem.nodes.Site, { id: 'US001' });

            const itemSite = await context
                .query(xtremMasterData.nodes.ItemSite, {
                    filter: {
                        item,
                        site,
                    },
                    first: 1,
                })
                .elementAt(0);

            // use as any here to allow to call directly the private function
            const result = await (xtremStock.nodeExtensions.ItemSiteCostExtension as any).getCostsFromItemSite(
                context,
                {
                    site,
                    item,
                    itemSite,
                    requiredQuantity: 10.0,
                    fromDate,
                    currentItemCostSums: {
                        unitCost: 0.0,
                        materialCost: 0.0,
                        laborCost: 0.0,
                        machineCost: 0.0,
                        toolCost: 0.0,
                        totalCost: 0.0,
                        hasResultLine: false,
                    } as xtremStock.interfaces.CostSumObject,
                },
            );

            assert.deepEqual(result, {
                unitCost: 113.333,
                materialCost: 1133.33,
                laborCost: 0.0,
                machineCost: 0.0,
                toolCost: 0.0,
                totalCost: 0.0,
                hasResultLine: false,
            });
        }));

    it('computeItemBomCostsAtRequiredQuantity without canComputeSublevels', () =>
        Test.withContext(async context => {
            const fromDate = date.make(2023, 12, 1);

            const site = await context.read(xtremSystem.nodes.Site, { id: 'US001' });

            const bomC = await getBomByName(context, 'Cost C');

            // use as any here to allow to call directly the private function
            const result = await (
                xtremStock.nodeExtensions.ItemSiteCostExtension as any
            ).computeItemBomCostsAtRequiredQuantity(context, {
                site,
                bom: bomC,
                currentItemCostSums: {
                    unitCost: 0.0,
                    materialCost: 0.0,
                    laborCost: 0.0,
                    machineCost: 0.0,
                    toolCost: 0.0,
                    totalCost: 0.0,
                    hasResultLine: false,
                } as xtremStock.interfaces.CostSumObject,
                calculationQuantity: 10.0,
                fromDate,
                computedItemCosts: {},
            });

            assert.deepEqual(result, {
                unitCost: 0.0,
                materialCost: 600602.0,
                laborCost: 0.0,
                machineCost: 0.0,
                toolCost: 0.0,
                totalCost: 0.0,
                hasResultLine: false,
            });
        }));

    it('computeItemBomCostsAtRequiredQuantity with canComputeSublevels', () =>
        Test.withContext(async context => {
            const fromDate = date.make(2023, 12, 1);

            const site = await context.read(xtremSystem.nodes.Site, { id: 'US001' });

            const bomA = await getBomByName(context, 'Cost A');

            // use as any here to allow to call directly the private function
            const result = await (
                xtremStock.nodeExtensions.ItemSiteCostExtension as any
            ).computeItemBomCostsAtRequiredQuantity(context, {
                site,
                bom: bomA,
                currentItemCostSums: {
                    unitCost: 0.0,
                    materialCost: 0.0,
                    laborCost: 0.0,
                    machineCost: 0.0,
                    toolCost: 0.0,
                    totalCost: 0.0,
                    hasResultLine: false,
                } as xtremStock.interfaces.CostSumObject,
                calculationQuantity: 10.0,
                fromDate,
                computedItemCosts: {},
                canComputeSublevels: true,
            });

            assert.deepEqual(result, {
                unitCost: 0.0,
                materialCost: 843668.8,
                laborCost: 0.0,
                machineCost: 0.0,
                toolCost: 0.0,
                totalCost: 0.0,
                hasResultLine: false,
            });
        }));

    it('computeItemCostAtRequiredQuantity (simple component)', () =>
        Test.withContext(async context => {
            const fromDate = date.make(2023, 12, 1);

            const item = await context.read(xtremMasterData.nodes.Item, { id: 'COST_H' });

            const site = await context.read(xtremSystem.nodes.Site, { id: 'US001' });

            // use as any here to allow to call directly the private function
            const result = await (
                xtremStock.nodeExtensions.ItemSiteCostExtension as any
            ).computeItemCostAtRequiredQuantity(context, {
                site,
                item,
                calculationQuantity: 10.0,
                fromDate,
                computedItemCosts: {},
                includesRouting: true,
            });

            assert.deepEqual(result, {
                unitCost: 123.0,
                materialCost: 1230.0,
                laborCost: 0,
                machineCost: 0,
                toolCost: 0,
                totalCost: 1230.0,
                hasResultLine: false,
            });
        }));

    it('computeItemCostAtRequiredQuantity (single level BOM)', () =>
        Test.withContext(async context => {
            const fromDate = date.make(2023, 12, 1);

            const item = await context.read(xtremMasterData.nodes.Item, { id: 'COST_C' });

            const site = await context.read(xtremSystem.nodes.Site, { id: 'US001' });

            // use as any here to allow to call directly the private function
            const result = await (
                xtremStock.nodeExtensions.ItemSiteCostExtension as any
            ).computeItemCostAtRequiredQuantity(context, {
                site,
                item,
                calculationQuantity: 10.0,
                fromDate,
                computedItemCosts: {},
                includesRouting: true,
            });

            assert.deepEqual(result, {
                unitCost: 60163.012,
                materialCost: 600602.0,
                laborCost: 960.0,
                machineCost: 57.5,
                toolCost: 10.62,
                totalCost: 601630.12,
                hasResultLine: false,
            });
        }));

    it('computeItemCostAtRequiredQuantity (multi level BOM)', () =>
        Test.withContext(async context => {
            const fromDate = date.make(2023, 12, 1);

            const item = await context.read(xtremMasterData.nodes.Item, { id: 'COST_D' });

            const site = await context.read(xtremSystem.nodes.Site, { id: 'US001' });

            // use as any here to allow to call directly the private function
            const result = await (
                xtremStock.nodeExtensions.ItemSiteCostExtension as any
            ).computeItemCostAtRequiredQuantity(context, {
                site,
                item,
                calculationQuantity: 10.0,
                fromDate,
                computedItemCosts: {},
                includesRouting: true,
            });

            assert.deepEqual(result, {
                unitCost: 61564.162,
                materialCost: 613902.0,
                laborCost: 1671.5,
                machineCost: 57.5,
                toolCost: 10.62,
                totalCost: 615641.62,
                hasResultLine: false,
            });
        }));

    it('Calculate simple BOM standard cost', () =>
        Test.withContext(
            async context => {
                const inputSet = context.create(xtremStock.nodes.CostRollUpInputSet, {
                    user: '<EMAIL>',
                    site: '#US001',
                    // '#COST_C' doesn't work in reference array => use of _id
                    items: [(await context.read(xtremMasterData.nodes.Item, { id: 'COST_C' }))._id],
                    fromDate: date.make(2023, 12, 1),
                    includesRouting: true,
                    usesComponentStandardCost: true,
                    quantity: 5,
                });

                await (await inputSet).$.save();

                await xtremStock.nodeExtensions.ItemSiteCostExtension.standardCostRollUpCalculation(context, inputSet);

                const expectedResults: ResultLine[] = [
                    {
                        item: { id: 'COST_C' },
                        materialCost: 320110,
                        machineCost: 37.5,
                        laborCost: 600,
                        toolCost: 5.37,
                        totalCost: 320752.87,
                        subAssemblyLines: [],
                    },
                ];
                const results = await context.select(
                    xtremStock.nodes.CostRollUpResultLine,
                    {
                        item: { id: true },
                        materialCost: true,
                        machineCost: true,
                        laborCost: true,
                        toolCost: true,
                        totalCost: true,
                        subAssemblyLines: {
                            componentNumber: true,
                            item: { id: true },
                            calculationQuantity: true,
                            materialCost: true,
                            machineCost: true,
                            laborCost: true,
                            toolCost: true,
                            totalCost: true,
                        },
                    },
                    {
                        filter: { inputSet: await inputSet },
                    },
                );
                assertCost(results, expectedResults);
            },
            { today: '2023-11-13' },
        ));

    it('Calculate Multi-level BOM standard cost', () =>
        Test.withContext(
            async context => {
                const inputSet = context.create(xtremStock.nodes.CostRollUpInputSet, {
                    user: '<EMAIL>',
                    site: '#US001',
                    // '#COST_C' doesn't work in reference array => use of _id
                    items: [(await context.read(xtremMasterData.nodes.Item, { id: 'COST_A' }))._id],
                    fromDate: date.make(2023, 12, 1),
                    includesRouting: true,
                    usesComponentStandardCost: true,
                    quantity: 15,
                });

                await (await inputSet).$.save();

                await xtremStock.nodeExtensions.ItemSiteCostExtension.standardCostRollUpCalculation(context, inputSet);

                const expectedResults: ResultLine[] = [
                    {
                        item: { id: 'COST_A' },
                        materialCost: 1028310,
                        machineCost: 139.5,
                        laborCost: 1998.6,
                        toolCost: 61.44,
                        totalCost: 1030509.54,
                        subAssemblyLines: [
                            {
                                componentNumber: 20,
                                item: { id: 'COST_C' },
                                calculationQuantity: 6,
                                materialCost: 384110,
                                machineCost: 41.5,
                                laborCost: 672,
                                toolCost: 6.42,
                                totalCost: 384829.92,
                            },
                            {
                                componentNumber: 30,
                                item: { id: 'COST_D' },
                                calculationQuantity: 3,
                                materialCost: 644170,
                                machineCost: 57.5,
                                laborCost: 1271.1,
                                toolCost: 10.62,
                                totalCost: 645509.22,
                            },
                        ],
                    },
                    {
                        item: { id: 'COST_C' },
                        materialCost: 960110,
                        machineCost: 77.5,
                        laborCost: 1320,
                        toolCost: 15.87,
                        totalCost: 961523.37,
                        subAssemblyLines: [],
                    },
                    {
                        item: { id: 'COST_D' },
                        materialCost: 660010,
                        machineCost: 57.5,
                        laborCost: 1957.5,
                        toolCost: 10.62,
                        totalCost: 662035.62,
                        subAssemblyLines: [
                            {
                                componentNumber: 10,
                                item: { id: 'COST_C' },
                                calculationQuantity: 10,
                                materialCost: 640110,
                                machineCost: 57.5,
                                laborCost: 960,
                                toolCost: 10.62,
                                totalCost: 641138.12,
                            },
                            {
                                componentNumber: 20,
                                item: { id: 'COST_E' },
                                calculationQuantity: 8.25,
                                materialCost: 19900,
                                machineCost: 0,
                                laborCost: 0,
                                toolCost: 0,
                                totalCost: 19900,
                            },
                        ],
                    },
                    {
                        item: { id: 'COST_E' },
                        materialCost: 36100,
                        machineCost: 0,
                        laborCost: 0,
                        toolCost: 0,
                        totalCost: 36100,
                        subAssemblyLines: [],
                    },
                ];
                const results = await context.select(
                    xtremStock.nodes.CostRollUpResultLine,
                    {
                        item: { id: true },
                        materialCost: true,
                        machineCost: true,
                        laborCost: true,
                        toolCost: true,
                        totalCost: true,
                        subAssemblyLines: {
                            componentNumber: true,
                            item: { id: true },
                            calculationQuantity: true,
                            materialCost: true,
                            machineCost: true,
                            laborCost: true,
                            toolCost: true,
                            totalCost: true,
                        },
                    },
                    {
                        filter: { inputSet: await inputSet },
                    },
                );
                assertCost(results, expectedResults);
            },
            { today: '2023-11-13' },
        ));

    it('Calculate Multi-level BOM standard cost without routing and component valuation method', () =>
        Test.withContext(
            async context => {
                const inputSet = context.create(xtremStock.nodes.CostRollUpInputSet, {
                    user: '<EMAIL>',
                    site: '#US001',
                    // '#COST_C' doesn't work in reference array => use of _id
                    items: [(await context.read(xtremMasterData.nodes.Item, { id: 'COST_A' }))._id],
                    fromDate: date.make(2023, 12, 1),
                    includesRouting: false,
                    usesComponentStandardCost: false,
                    quantity: 15,
                });

                await (await inputSet).$.save();

                await xtremStock.nodeExtensions.ItemSiteCostExtension.standardCostRollUpCalculation(context, inputSet);

                const expectedResults: ResultLine[] = [
                    {
                        item: { id: 'COST_A' },
                        materialCost: 965097.2,
                        machineCost: 0,
                        laborCost: 0,
                        toolCost: 0,
                        totalCost: 965097.2,
                        subAssemblyLines: [
                            {
                                componentNumber: 20,
                                item: { id: 'COST_C' },
                                calculationQuantity: 6,
                                materialCost: 360405.2,
                                machineCost: 0,
                                laborCost: 0,
                                toolCost: 0,
                                totalCost: 360405.2,
                            },
                            {
                                componentNumber: 30,
                                item: { id: 'COST_D' },
                                calculationQuantity: 3,
                                materialCost: 604662,
                                machineCost: 0,
                                laborCost: 0,
                                toolCost: 0,
                                totalCost: 604662,
                            },
                        ],
                    },
                    {
                        item: { id: 'COST_C' },
                        materialCost: 900848,
                        machineCost: 0,
                        laborCost: 0,
                        toolCost: 0,
                        totalCost: 900848,
                        subAssemblyLines: [],
                    },
                    {
                        item: { id: 'COST_D' },
                        materialCost: 620502,
                        machineCost: 0,
                        laborCost: 0,
                        toolCost: 0,
                        totalCost: 620502,
                        subAssemblyLines: [
                            {
                                componentNumber: 10,
                                item: { id: 'COST_C' },
                                calculationQuantity: 10,
                                materialCost: 600602,
                                machineCost: 0,
                                laborCost: 0,
                                toolCost: 0,
                                totalCost: 600602,
                            },
                            {
                                componentNumber: 20,
                                item: { id: 'COST_E' },
                                calculationQuantity: 8.25,
                                materialCost: 19900,
                                machineCost: 0,
                                laborCost: 0,
                                toolCost: 0,
                                totalCost: 19900,
                            },
                        ],
                    },
                    {
                        item: { id: 'COST_E' },
                        materialCost: 36100,
                        machineCost: 0,
                        laborCost: 0,
                        toolCost: 0,
                        totalCost: 36100,
                        subAssemblyLines: [],
                    },
                ];
                const results = await context.select(
                    xtremStock.nodes.CostRollUpResultLine,
                    {
                        item: { id: true },
                        materialCost: true,
                        machineCost: true,
                        laborCost: true,
                        toolCost: true,
                        totalCost: true,
                        subAssemblyLines: {
                            componentNumber: true,
                            item: { id: true },
                            calculationQuantity: true,
                            materialCost: true,
                            machineCost: true,
                            laborCost: true,
                            toolCost: true,
                            totalCost: true,
                        },
                    },
                    {
                        filter: { inputSet: await inputSet },
                    },
                );
                assertCost(results, expectedResults);
            },
            { today: '2023-11-13' },
        ));

    it('Calculate Multi-level BOM standard cost with phantom', () =>
        Test.withContext(
            async context => {
                const inputSet = context.create(xtremStock.nodes.CostRollUpInputSet, {
                    user: '<EMAIL>',
                    site: '#US001',
                    items: [(await context.read(xtremMasterData.nodes.Item, { id: 'CARROT-BOX-STD' }))._id],
                    fromDate: date.make(2024, 7, 23),
                    includesRouting: false,
                    usesComponentStandardCost: true,
                    quantity: 15,
                });

                await (await inputSet).$.save();

                await xtremStock.nodeExtensions.ItemSiteCostExtension.standardCostRollUpCalculation(context, inputSet);

                const expectedResults: ResultLine[] = [
                    {
                        item: {
                            id: 'CARROT-BOX-STD',
                        },
                        materialCost: 14.5715,
                        machineCost: 0,
                        laborCost: 0,
                        toolCost: 0,
                        totalCost: 14.5715,
                        subAssemblyLines: [
                            {
                                componentNumber: 10,
                                item: {
                                    id: 'CARROT-BOX',
                                },
                                calculationQuantity: 15,
                                materialCost: 14.5715,
                                machineCost: 0,
                                laborCost: 0,
                                toolCost: 0,
                                totalCost: 14.5715,
                            },
                        ],
                    },
                    {
                        item: {
                            id: 'DRESSING',
                        },
                        materialCost: 145.65,
                        machineCost: 0,
                        laborCost: 0,
                        toolCost: 0,
                        totalCost: 145.65,
                        subAssemblyLines: [],
                    },
                    {
                        item: {
                            id: 'CARROT-BOX',
                        },
                        materialCost: 14.5715,
                        machineCost: 0,
                        laborCost: 0,
                        toolCost: 0,
                        totalCost: 14.5715,
                        subAssemblyLines: [
                            {
                                componentNumber: 20,
                                item: {
                                    id: 'DRESSING',
                                },
                                calculationQuantity: 0.15,
                                // BOM definition:  1L of DRESSING = 0.2Kg Mustard + 0.6L Oil + 0.4L Vinegar
                                // -> 0.15L of DRESSING = (because of unit rounding) 0.03 Kg Mustard + 0.09 L Oil + 0.06 L Vinegar
                                // -> 0.03*3.5 + 0.09*12.35 + 0.06*4 = 0.105 + 1.1115 + 0.24 = 1.4565
                                materialCost: 1.4565,
                                machineCost: 0,
                                laborCost: 0,
                                toolCost: 0,
                                totalCost: 1.4565,
                            },
                        ],
                    },
                ];
                const results = await context.select(
                    xtremStock.nodes.CostRollUpResultLine,
                    {
                        item: { id: true },
                        materialCost: true,
                        machineCost: true,
                        laborCost: true,
                        toolCost: true,
                        totalCost: true,
                        subAssemblyLines: {
                            componentNumber: true,
                            item: { id: true },
                            calculationQuantity: true,
                            materialCost: true,
                            machineCost: true,
                            laborCost: true,
                            toolCost: true,
                            totalCost: true,
                        },
                    },
                    {
                        filter: { inputSet: await inputSet },
                    },
                );

                assertCost(results, expectedResults);
            },
            { today: '2023-11-13', testActiveServiceOptions: [xtremMasterData.serviceOptions.phantomItemOption] },
        ));
});

describe('Item site cost delete ', () => {
    it('Try to delete an item site cost for standard cost with start date today with stock transactions - should fail', () =>
        Test.withContext(
            async context => {
                const itemSiteCost = await context.read(
                    xtremMasterData.nodes.ItemSiteCost,
                    { _id: 12 },
                    { forUpdate: true },
                );

                await assert.isRejected(itemSiteCost.$.delete());
                assert.deepEqual(itemSiteCost.$.context.diagnoses, [
                    {
                        severity: 3,
                        path: [],
                        message:
                            'Delete not allowed. The item-site cost start date is equal to the current date and stock transactions exist for this item-site.',
                    },
                ]);
            },
            { today: '2021-01-01' },
        ));
});
