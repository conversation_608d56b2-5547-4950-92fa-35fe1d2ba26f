import { Test } from '@sage/xtrem-core';
import * as xtremTechnicalData from '@sage/xtrem-technical-data';
import { assert } from 'chai';

describe('Component extension', () => {
    it('Create billOfMaterial node - stock on hand calculation', () =>
        Test.withContext(
            async context => {
                const idValue = await context.queryCount(xtremTechnicalData.nodes.BillOfMaterial);
                const billOfMaterial = await context.create(xtremTechnicalData.nodes.BillOfMaterial, {
                    item: '#Chemical D',
                    site: '#US001',
                    name: 'Hand cream',
                    baseQuantity: 500,
                    components: [
                        {
                            item: '#5467',
                            componentNumber: 10,
                            unit: { id: 'LITER' },
                            linkQuantity: 0.4,
                            lineType: 'normal',
                        },
                        {
                            item: '#6545',
                            componentNumber: 20,
                            unit: { id: 'LITER' },
                            linkQuantity: 0.1,
                            scrapFactor: 20,
                            lineType: 'normal',
                        },
                    ],
                });
                await billOfMaterial.$.save();
                assert.deepEqual(billOfMaterial.$.context.diagnoses, []);
                assert.equal(billOfMaterial._id, idValue + 1);
                assert.equal(await billOfMaterial.components.length, 2);
                assert.equal((await (await billOfMaterial.components.elementAt(0)).stockOnHand).valueOf(), 10);
                assert.equal((await (await billOfMaterial.components.elementAt(1)).stockOnHand).valueOf(), 0);
                assert.equal(
                    (await (await billOfMaterial.components.elementAt(0)).availableQuantityInStockUnit).valueOf(),
                    10,
                );
                assert.equal(
                    (await (await billOfMaterial.components.elementAt(1)).availableQuantityInStockUnit).valueOf(),
                    0,
                );
                assert.equal(await (await billOfMaterial.components.elementAt(0)).hasStockShortage, false);
                assert.equal(await (await billOfMaterial.components.elementAt(1)).hasStockShortage, false);
            },
            {
                today: '2021-08-23',
            },
        ));
});
