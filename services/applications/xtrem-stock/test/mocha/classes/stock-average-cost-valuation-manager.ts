import type { Context, decimal } from '@sage/xtrem-core';
import { Test, asyncArray } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremStockData from '@sage/xtrem-stock-data';
import { assert } from 'chai';
import * as xtremStock from '../../../lib';
import * as testLib from '../../fixtures/lib';

describe('Stock Average Cost Valuation Manager', () => {
    describe('get amounts', () => {
        describe('correction', () => {
            async function assertStockReceiptCorrection(
                context: Context,
                variance: decimal,
                expectedValues: Parameters<typeof testLib.functions.TestHelpers.checkStockValuationManagerValues>[1][],
            ) {
                const { valuationManager, documentLine, stockCorrectionDetails } =
                    await testLib.functions.TestHelpers.simulateStockReceiptCorrection(context, {
                        variance,
                        correctedDocumentNumber: 'MISC_RECEIPT16',
                        itemLineToCorrect: 'STOAVC',
                        correctorDocumentNumber: 'MISC_RECEIPT18',
                    });

                assert.isTrue(valuationManager instanceof xtremStock.classes.StockAverageCostValuationManager);

                await asyncArray(expectedValues).forEach(async (expectedValue, index) => {
                    testLib.functions.TestHelpers.checkStockValuationManagerValues(
                        {
                            valuationManager,
                            documentLine,
                            quantity: await stockCorrectionDetails[index].impactedQuantity,
                            stockDetailId: stockCorrectionDetails[index]._id,
                        },
                        expectedValue,
                    );
                });
            }

            it('test get methods on price correction', () =>
                Test.withContext(async context => {
                    // make sure, stock is high enough to absorb the whole amount
                    const stock = await context.read(
                        xtremStockData.nodes.Stock,
                        { item: '#STOAVC', site: '#US001' },
                        { forUpdate: true },
                    );
                    await stock.$.set({ quantityInStockUnit: 10000 });
                    await stock.$.save();

                    const variance = 100;
                    // Quantity in stock is higher than impacted quantity => the whole amount is absorbed
                    // The line is made of 2 StockReceiptDetail: Qty = 4 & 6
                    // => the absorbed amounts are respectively 40 and 60
                    await assertStockReceiptCorrection(context, variance, [
                        { orderedAmount: 40.0, valuedAmount: 40.0, nonAbsorbedAmount: 0.0 },
                        { orderedAmount: 60.0, valuedAmount: 60.0, nonAbsorbedAmount: 0.0 },
                    ]);
                }));
            it('test get methods on price correction with partial stock available', () =>
                Test.withContext(async context => {
                    // make the stock (3) lower than the impacted quantity (10)
                    const stockUpdated = await context.read(
                        xtremStockData.nodes.Stock,
                        { item: '#STOAVC', site: '#US001' },
                        { forUpdate: true },
                    );
                    await stockUpdated.$.set({ quantityInStockUnit: 3 });
                    await stockUpdated.$.save({ flushDeferredActions: true });
                    const stocks = context.query(xtremStockData.nodes.Stock, {
                        filter: { item: '#STOAVC', site: '#US001' },
                    });
                    await stocks
                        .filter(stock => stock._id !== stockUpdated._id)
                        .forEach(async stock => {
                            await context.delete(xtremStockData.nodes.Stock, {
                                _id: stock._id,
                            });
                        });
                    const variance = 100;
                    // 100 (variance)
                    // * 4/10 (quantity of the stockDetail over the quantity of the line)
                    // * 3/10 (available stock over the document line quantity)
                    // = 12
                    await assertStockReceiptCorrection(context, variance, [
                        { orderedAmount: 12.0, valuedAmount: 12.0, nonAbsorbedAmount: 28.0 },
                        { orderedAmount: 18.0, valuedAmount: 18.0, nonAbsorbedAmount: 42.0 },
                    ]);
                }));
            it('test get methods on price correction with no stock available', () =>
                Test.withContext(async context => {
                    // remove all stocks
                    const stocks = context.query(xtremStockData.nodes.Stock, {
                        filter: { item: '#STOAVC', site: '#US001' },
                    });
                    await stocks.forEach(async stock => {
                        await context.delete(xtremStockData.nodes.Stock, {
                            _id: stock._id,
                        });
                    });
                    const variance = 100;
                    // As there is no stock, the whole amount must be in non-absorbed
                    await assertStockReceiptCorrection(context, variance, [
                        { orderedAmount: 0.0, valuedAmount: 0.0, nonAbsorbedAmount: 40.0 },
                        { orderedAmount: 0.0, valuedAmount: 0.0, nonAbsorbedAmount: 60.0 },
                    ]);
                }));
        });
        describe('value change', () => {
            it('test get methods on value change', () =>
                Test.withContext(async context => {
                    const variance = 414.68;
                    const document = await testLib.functions.TestHelpers.createStockValueChangeDocument(context, {
                        item: '#STOAVC',
                        site: '#US001',
                        lineAmounts: [
                            {
                                oldAmount: 12300,
                                newAmount: 12300 + variance,
                                quantity: 100,
                            },
                        ],
                    });

                    const documentLine = await document.lines.elementAt(0);
                    const valuationManager = await xtremStock.classes.StockValuationManager.getInstance(context, {
                        action: 'changeValue',
                        stockDetails: await documentLine.stockDetails.toArray(),
                    });

                    assert.isTrue(valuationManager instanceof xtremStock.classes.StockAverageCostValuationManager);

                    testLib.functions.TestHelpers.checkStockValuationManagerValues(
                        {
                            valuationManager,
                            documentLine,
                        },
                        { orderedAmount: variance, valuedAmount: variance, nonAbsorbedAmount: 0 },
                    );
                }));
        });
    });

    describe('finish', () => {
        it('on correction', () =>
            Test.withContext(async context => {
                let itemSite = await context.read(xtremMasterData.nodes.ItemSite, {
                    item: '#STOAVC',
                    site: '#US001',
                });
                const initialAmount = await itemSite.stockValuationAtAverageCost;
                const variance = 100;
                const { valuationManager } = await testLib.functions.TestHelpers.simulateStockReceiptCorrection(
                    context,
                    {
                        variance,
                        correctedDocumentNumber: 'MISC_RECEIPT16',
                        itemLineToCorrect: 'STOAVC',
                        correctorDocumentNumber: 'MISC_RECEIPT18',
                    },
                );
                await valuationManager.finish(context);
                await Test.rollbackCache(context);
                itemSite = await context.read(xtremMasterData.nodes.ItemSite, {
                    item: '#STOAVC',
                    site: '#US001',
                });
                assert.deepEqual(await itemSite.stockValuationAtAverageCost, initialAmount + variance);
            }));

        it('on value change', () =>
            Test.withContext(async context => {
                const itemId = '#STOAVC';
                const siteId = '#US001';
                let itemSite = await context.read(xtremMasterData.nodes.ItemSite, {
                    item: itemId,
                    site: siteId,
                });
                const newAmount = (await itemSite.stockValuationAtAverageCost) + 123.45;
                const document = await testLib.functions.TestHelpers.createStockValueChangeDocument(context, {
                    item: itemId,
                    site: siteId,
                    lineAmounts: [
                        {
                            oldAmount: await itemSite.stockValuationAtAverageCost,
                            newAmount,
                            quantity: await itemSite.inStockQuantity,
                        },
                    ],
                });

                const documentLine = await document.lines.elementAt(0);
                const valuationManager = await xtremStock.classes.StockValuationManager.getInstance(context, {
                    action: 'changeValue',
                    stockDetails: await documentLine.stockDetails.toArray(),
                });
                assert.isTrue(valuationManager instanceof xtremStock.classes.StockAverageCostValuationManager);

                await valuationManager.finish(context);
                await Test.rollbackCache(context);
                itemSite = await context.read(xtremMasterData.nodes.ItemSite, {
                    item: itemId,
                    site: siteId,
                });

                assert.deepEqual(await itemSite.stockValuationAtAverageCost, newAmount);
            }));
    });
});
