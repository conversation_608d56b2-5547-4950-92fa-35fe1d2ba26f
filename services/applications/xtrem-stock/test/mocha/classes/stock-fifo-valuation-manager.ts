import _ = require('lodash');
import type { Context, NodeCreateData, NodePayloadData, NodeQueryFilter } from '@sage/xtrem-core';
import { asyncArray, date, Logger, Test } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremStockData from '@sage/xtrem-stock-data';
import * as xtremSystem from '@sage/xtrem-system';
import { assert, expect } from 'chai';
import * as sinon from 'sinon';
import * as xtremStock from '../../../lib';
import * as testLib from '../../fixtures/lib';
import { FakeStockIssue, FakeStockNegativeReceipt } from '../../fixtures/lib/nodes';

/**
 * asserts that an array of FifoValuationTier or FifoValuationIssue has the same values than in the expectedTierValues
 * @param actualTiers an array of FifoValuationTier or FifoValuationIssue records
 * @param expectedTierValues array of objects with some properties that are found in the records to test
 */
async function assertTiers<
    NodeFifo extends xtremStockData.nodes.FifoValuationTier | xtremStockData.nodes.FifoValuationIssue,
>(actualTiers: Array<NodeFifo>, expectedTierValues: Array<NodePayloadData<NodeFifo>>) {
    assert.deepEqual(actualTiers.length, expectedTierValues.length);

    await asyncArray(expectedTierValues).forEach(async (expectedTier, index) => {
        const actualTier = await actualTiers[index].$.payload({ withIds: true });
        const message = `tier.effectiveDate=${actualTier.effectiveDate}  tier.sequence=${actualTier.sequence}`;

        _.keys(expectedTierValues).forEach(key => {
            const actualValue = (actualTier as any)[key] as unknown as string | number | date;
            const expectedValue = (expectedTier as any)[key] as unknown as string | number | date;
            assert.deepEqual(actualValue, expectedValue, message);
        });
    });
}

async function controlTiers(
    context: Context,
    fifoTierFilter?: NodeQueryFilter<xtremStockData.nodes.FifoValuationTier>,
    expectedFifoTiers?: Array<NodePayloadData<xtremStockData.nodes.FifoValuationTier>>,
    fifoIssueTierFilter?: NodeQueryFilter<xtremStockData.nodes.FifoValuationIssue>,
    expectedFifoIssueTiers?: Array<NodePayloadData<xtremStockData.nodes.FifoValuationIssue>>,
) {
    if (expectedFifoTiers) {
        const actualFifoTiers = await context
            .query(xtremStockData.nodes.FifoValuationTier, {
                filter: fifoTierFilter,
                orderBy: { sequence: 1 },
            })
            .toArray();

        await assertTiers(actualFifoTiers, expectedFifoTiers);
    }
    if (expectedFifoIssueTiers) {
        const actualFifoIssueTiers = await context
            .query(xtremStockData.nodes.FifoValuationIssue, {
                filter: fifoIssueTierFilter,
                orderBy: { effectiveDate: 1, sequence: 1 },
            })
            .toArray();

        await assertTiers(actualFifoIssueTiers, expectedFifoIssueTiers);
    }
}

describe('Stock FIFO Valuation Manager success', () => {
    describe('get amounts', () => {
        describe('correction', () => {
            it('test get methods on correction', () =>
                Test.withContext(async context => {
                    const variance = 120;
                    const { valuationManager, documentLine, stockCorrectionDetails } =
                        await testLib.functions.TestHelpers.simulateStockReceiptCorrection(context, {
                            variance,
                            correctedDocumentNumber: 'MISC_RECEIPT24',
                            itemLineToCorrect: 'STOFIFO',
                            correctorDocumentNumber: 'MISC_RECEIPT18',
                        });

                    assert.isTrue(valuationManager instanceof xtremStock.classes.StockFifoValuationManager);

                    const expectedValues = [
                        { orderedAmount: 50.0, valuedAmount: 50.0, nonAbsorbedAmount: 0.0 },
                        { orderedAmount: 70.0, valuedAmount: 70.0, nonAbsorbedAmount: 0.0 },
                    ];
                    await asyncArray(expectedValues).forEach(async (expectedValue, index) => {
                        testLib.functions.TestHelpers.checkStockValuationManagerValues(
                            {
                                valuationManager,
                                documentLine,
                                quantity: await stockCorrectionDetails[index].impactedQuantity,
                                stockDetailId: stockCorrectionDetails[index]._id,
                            },
                            expectedValue,
                        );
                    });
                }));

            it('test get methods on correction with no remaining stock in FIFO tier', () =>
                Test.withContext(async context => {
                    const variance = 120;
                    const { valuationManager, documentLine, stockCorrectionDetails } =
                        await testLib.functions.TestHelpers.simulateStockReceiptCorrection(context, {
                            variance,
                            correctedDocumentNumber: 'MISC_RECEIPT27',
                            itemLineToCorrect: 'STOFIFO-MAT1',
                            correctorDocumentNumber: 'MISC_RECEIPT18',
                        });

                    assert.isTrue(valuationManager instanceof xtremStock.classes.StockFifoValuationManager);

                    const expectedValues = [{ orderedAmount: 0.0, valuedAmount: 0.0, nonAbsorbedAmount: 120.0 }];
                    await asyncArray(expectedValues).forEach(async (expectedValue, index) => {
                        testLib.functions.TestHelpers.checkStockValuationManagerValues(
                            {
                                valuationManager,
                                documentLine,
                                quantity: await stockCorrectionDetails[index].impactedQuantity,
                                stockDetailId: stockCorrectionDetails[index]._id,
                            },
                            expectedValue,
                        );
                    });
                }));

            it('test get methods on correction with partial remaining stock in FIFO tier', () =>
                Test.withContext(async context => {
                    const variance = 120;
                    const { valuationManager, documentLine, stockCorrectionDetails } =
                        await testLib.functions.TestHelpers.simulateStockReceiptCorrection(context, {
                            variance,
                            correctedDocumentNumber: 'MISC_RECEIPT28',
                            itemLineToCorrect: 'STOFIFO-MAT1',
                            correctorDocumentNumber: 'MISC_RECEIPT18',
                        });

                    assert.isTrue(valuationManager instanceof xtremStock.classes.StockFifoValuationManager);

                    const expectedValues = [{ orderedAmount: 96.0, valuedAmount: 96.0, nonAbsorbedAmount: 24.0 }];
                    await asyncArray(expectedValues).forEach(async (expectedValue, index) => {
                        testLib.functions.TestHelpers.checkStockValuationManagerValues(
                            {
                                valuationManager,
                                documentLine,
                                quantity: await stockCorrectionDetails[index].impactedQuantity,
                                stockDetailId: stockCorrectionDetails[index]._id,
                            },
                            expectedValue,
                        );
                    });
                }));
        });

        it('test get methods on correction with missing FIFO tier (XT-71706)', () =>
            Test.withContext(async context => {
                const variance = 120;
                const receipt = await context.read(xtremStock.nodes.StockReceipt, { number: 'MISC_RECEIPT34' });
                const receiptLine = await receipt.lines.elementAt(0);
                assert.deepEqual(await (await receiptLine.item).id, 'STOFIFO-MAT1');
                assert.deepEqual(await receiptLine.quantityInStockUnit, 10.0);

                // check that the FIFO tier corresponding to the receipt line doesn't exist yet
                const fifoTiers = await context
                    .query(xtremStockData.nodes.FifoValuationTier, { filter: { receiptDocumentLine: receiptLine } })
                    .toArray();
                assert.deepEqual(fifoTiers.length, 0);

                const { valuationManager, documentLine, stockCorrectionDetails } =
                    await testLib.functions.TestHelpers.simulateStockReceiptCorrection(context, {
                        variance,
                        correctedDocumentNumber: 'MISC_RECEIPT34',
                        itemLineToCorrect: 'STOFIFO-MAT1',
                        correctorDocumentNumber: 'MISC_RECEIPT18',
                    });

                assert.isTrue(valuationManager instanceof xtremStock.classes.StockFifoValuationManager);

                const expectedValues = [{ orderedAmount: 0.0, valuedAmount: 0.0, nonAbsorbedAmount: 120.0 }];
                await asyncArray(expectedValues).forEach(async (expectedValue, index) => {
                    testLib.functions.TestHelpers.checkStockValuationManagerValues(
                        {
                            valuationManager,
                            documentLine,
                            quantity: await stockCorrectionDetails[index].impactedQuantity,
                            stockDetailId: stockCorrectionDetails[index]._id,
                        },
                        expectedValue,
                    );
                });

                // check that the FIFO tier corresponding to the receipt line has been created
                let fifoTiersAfter = await context
                    .query(xtremStockData.nodes.FifoValuationTier, { filter: { receiptDocumentLine: receiptLine } })
                    .toArray();
                assert.deepEqual(fifoTiersAfter.length, 1);
                assert.deepEqual(await fifoTiersAfter[0].receiptQuantity, 10.0);
                assert.deepEqual(await fifoTiersAfter[0].remainingQuantity, 0.0);
                assert.deepEqual(await fifoTiersAfter[0].amount, 0.0);
                assert.deepEqual(await fifoTiersAfter[0].nonAbsorbedAmount, 0.0);

                await valuationManager.finish(context);

                // check that the non-absorbed amount of the new FIFO tier corresponding to the receipt line has been updated
                await Test.rollbackCache(context);
                fifoTiersAfter = await context
                    .query(xtremStockData.nodes.FifoValuationTier, { filter: { receiptDocumentLine: receiptLine } })
                    .toArray();
                assert.deepEqual(fifoTiersAfter.length, 1);
                assert.deepEqual(await fifoTiersAfter[0].receiptQuantity, 10.0);
                assert.deepEqual(await fifoTiersAfter[0].remainingQuantity, 0.0);
                assert.deepEqual(await fifoTiersAfter[0].amount, 0.0);
                assert.deepEqual(await fifoTiersAfter[0].nonAbsorbedAmount, 120.0);
            }));

        it('test get methods on correction with missing FIFO tier but stock desynchronized with FIFO stack (XT-71706)', () =>
            Test.withContext(async context => {
                const variance = 120;
                const receipt = await context.read(xtremStock.nodes.StockReceipt, { number: 'MISC_RECEIPT34' });
                const receiptLine = await receipt.lines.elementAt(0);
                assert.deepEqual(await (await receiptLine.item).id, 'STOFIFO-MAT1');
                assert.deepEqual(await receiptLine.quantityInStockUnit, 10.0);

                // check that the FIFO tier corresponding to the receipt line doesn't exist yet
                const fifoTiers = await context
                    .query(xtremStockData.nodes.FifoValuationTier, { filter: { receiptDocumentLine: receiptLine } })
                    .toArray();
                assert.deepEqual(fifoTiers.length, 0);

                // desynchronize the FIFO stack with stock
                const fifoTier = await context
                    .query(xtremStockData.nodes.FifoValuationTier, {
                        filter: {
                            item: await receiptLine.item,
                            site: await receiptLine.site,
                            remainingQuantity: { _gt: 0 },
                        },
                        first: 1,
                        forUpdate: true,
                    })
                    .elementAt(0);

                await fifoTier.$.set({ remainingQuantity: 0 });
                await fifoTier.$.save();

                await assert.isRejected(
                    testLib.functions.TestHelpers.simulateStockReceiptCorrection(context, {
                        variance,
                        correctedDocumentNumber: 'MISC_RECEIPT34',
                        itemLineToCorrect: 'STOFIFO-MAT1',
                        correctorDocumentNumber: 'MISC_RECEIPT18',
                    }),
                    'The FIFO stack quantity (0) for the item-site STOFIFO-MAT1-US001 is different from the quantity in stock (8)',
                );
            }));

        it('test get methods on receipt', () =>
            Test.withReadonlyContext(async context => {
                const documentLine = await context.read(xtremStock.nodes.StockReceiptLine, { _id: 1144 });
                const quantity = await documentLine.quantityInStockUnit;
                const valuationManager = await xtremStock.classes.StockValuationManager.getInstance(context, {
                    action: 'createValue',
                    stockDetails: await documentLine.stockDetails.toArray(),
                });
                assert.isTrue(valuationManager instanceof xtremStock.classes.StockFifoValuationManager);

                assert.deepEqual(Number(valuationManager.getOrderAmount(documentLine, quantity)), 1845);
                assert.deepEqual(Number(valuationManager.getValuedAmount(documentLine, quantity)), 1845);
                assert.deepEqual(Number(valuationManager.getNonAbsorbedAmount(documentLine, quantity)), 0);
            }));
        it('test get methods on issue', () =>
            Test.withContext(async context => {
                const documentLine = await context.read(xtremStock.nodes.StockIssueLine, { _id: 1914 });
                const quantity = -(await documentLine.quantityInStockUnit);

                const valuationManager = await xtremStock.classes.StockValuationManager.getInstance(context, {
                    action: 'createValue',
                    stockDetails: await documentLine.stockDetails.toArray(),
                });
                assert.isTrue(valuationManager instanceof xtremStock.classes.StockFifoValuationManager);
                assert.deepEqual(Number(valuationManager.getOrderAmount(documentLine, quantity)), -2890);
                assert.deepEqual(Number(valuationManager.getValuedAmount(documentLine, quantity)), -2890);
                assert.deepEqual(Number(valuationManager.getNonAbsorbedAmount(documentLine, quantity)), 0);
            }));

        it('test get methods on negative issue', () =>
            Test.withContext(async context => {
                const effectiveDate = date.make(2023, 5, 1);
                const returnedQuantity = 8;
                // the FifoValuationIssue records used in the test are linked to the StockIssueLine 1913
                // but the document used to test the negative issue is a FakeStockNegativeIssue
                const originIssueLine = await context.read(xtremStock.nodes.StockIssueLine, { _id: 1913 });
                const item = await originIssueLine.item;
                const site = await (await originIssueLine.document).getStockSite();

                const negativeIssue = await context.create(testLib.nodes.FakeStockNegativeIssue, {
                    number: 'TEST',
                    site,
                    date: effectiveDate,
                    lines: [
                        {
                            lineNumber: 1,
                            item,
                            quantity: returnedQuantity,
                            originIssueLine,
                            stockDetails: [
                                {
                                    _action: 'create',
                                    effectiveDate,
                                    item,
                                    site,
                                    location: await context.read(xtremMasterData.nodes.Location, {
                                        locationZone: '#US001|Loading dock',
                                        id: 'LOC1',
                                    }),
                                    status: await context.read(xtremStockData.nodes.StockStatus, { id: 'R' }),
                                    quantityInStockUnit: returnedQuantity,
                                    // documentLine: lineID,
                                },
                            ],
                        },
                    ],
                });
                await negativeIssue.$.save();

                const negativeIssueLine = await negativeIssue.lines.elementAt(0);

                const valuationManager = await xtremStock.classes.StockValuationManager.getInstance(context, {
                    action: 'createValue',
                    stockDetails: await negativeIssueLine.stockDetails.toArray(),
                });
                assert.isTrue(valuationManager instanceof xtremStock.classes.StockFifoValuationManager);
                assert.deepEqual(Number(valuationManager.getOrderAmount(negativeIssueLine, returnedQuantity)), 1050); // 2*135+6*130
                assert.deepEqual(Number(valuationManager.getValuedAmount(negativeIssueLine, returnedQuantity)), 1050);
                assert.deepEqual(Number(valuationManager.getNonAbsorbedAmount(negativeIssueLine, returnedQuantity)), 0);
            }));

        // XT-86662: allow negative issue in FIFO even if the issue was not created by using FIFO
        it('test get methods on FIFO negative issue with missing FifoValuationIssue origin', () =>
            Test.withContext(
                async context => {
                    const effectiveDate = date.make(2025, 1, 6);
                    const returnedQuantity = 5;
                    // the StockIssueLine used as origin for the negative is done with another valuation method than FIFO
                    // => there is no FifoValuationIssue record for this line
                    // but the negative issue is done with the FIFO valuation method
                    // => the valuation manager must be able to compute the amounts and create a new FifoValuationIssue record
                    const originIssueLine = await context.read(xtremStock.nodes.StockIssueLine, { _id: 1917 });

                    let checkFifoValuationIssue = await context.select(
                        xtremStockData.nodes.FifoValuationIssue,
                        { remainingQuantity: true, issuedQuantity: true, amount: true, effectiveDate: true },
                        { filter: { issueDocumentLine: originIssueLine }, first: 1 },
                    );
                    assert.equal(checkFifoValuationIssue.length, 0);

                    const item = await originIssueLine.item;
                    const site = await (await originIssueLine.document).getStockSite();

                    const negativeIssue = await context.create(testLib.nodes.FakeStockNegativeIssue, {
                        number: 'TEST',
                        site,
                        date: effectiveDate,
                        lines: [
                            {
                                lineNumber: 1,
                                item,
                                quantity: returnedQuantity,
                                originIssueLine,
                                stockDetails: [
                                    {
                                        _action: 'create',
                                        effectiveDate,
                                        item,
                                        site,
                                        location: await context.read(xtremMasterData.nodes.Location, {
                                            locationZone: '#US001|Loading dock',
                                            id: 'LOC1',
                                        }),
                                        status: await context.read(xtremStockData.nodes.StockStatus, { id: 'R' }),
                                        quantityInStockUnit: returnedQuantity,
                                    },
                                ],
                            },
                        ],
                    });
                    await negativeIssue.$.save();

                    const negativeIssueLine = await negativeIssue.lines.elementAt(0);

                    const valuationManager = await xtremStock.classes.StockValuationManager.getInstance(context, {
                        action: 'createValue',
                        stockDetails: await negativeIssueLine.stockDetails.toArray(),
                    });
                    assert.isTrue(valuationManager instanceof xtremStock.classes.StockFifoValuationManager);

                    // Check that the FifoValuationIssue record has been created with values coming from
                    // the stockJournal values of the issue line
                    checkFifoValuationIssue = await context.select(
                        xtremStockData.nodes.FifoValuationIssue,
                        { remainingQuantity: true, issuedQuantity: true, amount: true, effectiveDate: true },
                        { filter: { issueDocumentLine: originIssueLine } },
                    );
                    assert.equal(checkFifoValuationIssue.length, 1);
                    assert.deepEqual(checkFifoValuationIssue, [
                        {
                            remainingQuantity: 7.0,
                            issuedQuantity: 7.0,
                            amount: 1113.0,
                            effectiveDate: date.make(2025, 1, 4),
                        },
                    ]);

                    // 795 = 5 * 1113/7  = returned quantity * unit cost of the origin issue line
                    assert.deepEqual(Number(valuationManager.getOrderAmount(negativeIssueLine, returnedQuantity)), 795);
                    assert.deepEqual(
                        Number(valuationManager.getValuedAmount(negativeIssueLine, returnedQuantity)),
                        795,
                    );
                    assert.deepEqual(
                        Number(valuationManager.getNonAbsorbedAmount(negativeIssueLine, returnedQuantity)),
                        0,
                    );
                },
                {
                    testActiveServiceOptions: [xtremMasterData.serviceOptions.fifoValuationMethodOption],
                    today: '2025-01-06',
                },
            ));

        // XT-86662: allow negative issue in FIFO even if the issue was not created by using FIFO
        it('test get methods on FIFO negative issue with missing FifoValuationIssue origin and missing StockJournal - fails', () =>
            Test.withContext(
                async context => {
                    const effectiveDate = date.make(2025, 1, 6);
                    const returnedQuantity = 5;
                    // the StockIssueLine used as origin for the negative has no StockJournal record
                    // =>
                    const originIssueLine = await context.read(xtremStock.nodes.StockIssueLine, { _id: 1914 });

                    assert.equal(await originIssueLine.stockMovements.length, 0);

                    const item = await originIssueLine.item;
                    const site = await (await originIssueLine.document).getStockSite();

                    const negativeIssue = await context.create(testLib.nodes.FakeStockNegativeIssue, {
                        number: 'TEST',
                        site,
                        date: effectiveDate,
                        lines: [
                            {
                                lineNumber: 1,
                                item,
                                quantity: returnedQuantity,
                                originIssueLine,
                                stockDetails: [
                                    {
                                        _action: 'create',
                                        effectiveDate,
                                        item,
                                        site,
                                        location: await context.read(xtremMasterData.nodes.Location, {
                                            locationZone: '#US001|Loading dock',
                                            id: 'LOC1',
                                        }),
                                        status: await context.read(xtremStockData.nodes.StockStatus, { id: 'R' }),
                                        quantityInStockUnit: returnedQuantity,
                                    },
                                ],
                            },
                        ],
                    });
                    await negativeIssue.$.save();

                    const negativeIssueLine = await negativeIssue.lines.elementAt(0);

                    await assert.isRejected(
                        xtremStock.classes.StockValuationManager.getInstance(context, {
                            action: 'createValue',
                            stockDetails: await negativeIssueLine.stockDetails.toArray(),
                        }),
                        `The issue document line quantity is 0 or its movements do not exist.\n\nIssue document line ID: ${originIssueLine._id}`,
                    );
                },
                {
                    testActiveServiceOptions: [xtremMasterData.serviceOptions.fifoValuationMethodOption],
                    today: '2025-01-06',
                },
            ));

        it('test get methods on negative receipt', () =>
            Test.withContext(async context => {
                const effectiveDate = date.make(2023, 5, 1);
                const returnedQuantities = [1, 2];
                const returnedQuantity = 3;
                // use of a receipt template: q=10; 2 stock details; remaining qty in FIFO stack=3
                // As 5 are returned => the value of 2 must be set into the non-absorbed amount
                const originReceiptLine = await context.read(xtremStock.nodes.StockReceiptLine, { _id: 1150 });
                const site = await originReceiptLine.site;
                const item = await originReceiptLine.item;
                const quantityInStockUnit = await originReceiptLine.quantityInStockUnit;

                const negativeIssue = await context.create(FakeStockNegativeReceipt, {
                    number: 'TEST',
                    stockSite: site,
                    effectiveDate,
                    lines: [
                        {
                            lineNumber: 1,
                            item,
                            quantityInStockUnit,
                            originReceiptLine,
                            stockDetails: returnedQuantities.map(quantity => {
                                return {
                                    _action: 'create',
                                    effectiveDate,
                                    item,
                                    site,
                                    stockRecord: 96,
                                    quantityInStockUnit: -quantity,
                                } as NodeCreateData<
                                    xtremStockData.nodes.StockIssueDetail & {
                                        _action: 'create';
                                    }
                                >;
                            }),
                        },
                    ],
                });

                await negativeIssue.$.save();

                const negativeIssueLine = await negativeIssue.lines.elementAt(0);

                const valuationManager = await xtremStock.classes.StockValuationManager.getInstance(context, {
                    action: 'createValue',
                    stockDetails: await negativeIssueLine.stockDetails.toArray(),
                });
                assert.isTrue(valuationManager instanceof xtremStock.classes.StockFifoValuationManager);

                // here, the case is a negativeReceipt => the quantity to pass must be negative to get a negative amount

                // order amount corresponds to the receipt price * quantity returned = 20 * 3
                assert.deepEqual(
                    Number(valuationManager.getOrderAmount(negativeIssueLine, -returnedQuantity)),
                    Number(-60),
                );
                // movement amount corresponds to the amount consumed in the FIFO stack = 3 * 20.5 = 61.5
                assert.deepEqual(
                    Number(valuationManager.getValuedAmount(negativeIssueLine, -returnedQuantity)),
                    Number(-61.5),
                );
                // non absorbed amount = order amount - movement amount = -60 - (-61.5)
                assert.deepEqual(
                    Number(valuationManager.getNonAbsorbedAmount(negativeIssueLine, -returnedQuantity)),
                    Number(1.5),
                );
            }));

        it('test get methods on negative receipt fails', () =>
            Test.withContext(async context => {
                const effectiveDate = date.make(2023, 5, 1);
                // use of a receipt template: q=10; 2 stock details; remaining qty in FIFO stack=0
                // As 5 are returned => an exception is triggered
                const originReceiptLine = await context.read(xtremStock.nodes.StockReceiptLine, { _id: 1150 });
                const site = await originReceiptLine.site;
                const item = await originReceiptLine.item;
                const quantityInStockUnit = await originReceiptLine.quantityInStockUnit;

                const negativeIssue = await context.create(FakeStockNegativeReceipt, {
                    number: 'TEST',
                    stockSite: site,
                    effectiveDate,
                    lines: [
                        {
                            lineNumber: 1,
                            item,
                            quantityInStockUnit,
                            originReceiptLine,
                            stockDetails: [2, 3].map(quantity => {
                                return {
                                    _action: 'create',
                                    effectiveDate,
                                    item,
                                    site,
                                    stockRecord: 96,
                                    quantityInStockUnit: -quantity,
                                } as NodeCreateData<
                                    xtremStockData.nodes.StockIssueDetail & {
                                        _action: 'create';
                                    }
                                >;
                            }),
                        },
                    ],
                });
                await negativeIssue.$.save();

                const negativeIssueLine = await negativeIssue.lines.elementAt(0);

                await assert.isRejected(
                    xtremStock.classes.StockValuationManager.getInstance(context, {
                        action: 'createValue',
                        stockDetails: await negativeIssueLine.stockDetails.toArray(),
                    }),
                    'The quantity in FIFO stack is not enough: quantityToConsume=5 totalConsumedQuantity=3',
                );
            }));

        it('test get methods on value change', () =>
            Test.withContext(async context => {
                const document = await testLib.functions.TestHelpers.createStockValueChangeDocument(context, {
                    item: '#STOFIFO-MAT1',
                    site: '#US001',
                    lineAmounts: [
                        {
                            oldAmount: 840,
                            newAmount: 832.98,
                            quantity: 8,
                            fifoTier: await context.read(xtremStockData.nodes.FifoValuationTier, { _id: 115100 }),
                        },
                    ],
                });

                const documentLine = await document.lines.elementAt(0);
                const valuationManager = await xtremStock.classes.StockValuationManager.getInstance(context, {
                    action: 'changeValue',
                    stockDetails: await documentLine.stockDetails.toArray(),
                });

                assert.isTrue(valuationManager instanceof xtremStock.classes.StockFifoValuationManager);
                assert.deepEqual(valuationManager.getOrderAmount(documentLine), -7.02);
                assert.deepEqual(valuationManager.getValuedAmount(documentLine), -7.02);
                assert.deepEqual(valuationManager.getNonAbsorbedAmount(documentLine), 0);
            }));
    });

    describe('finish', () => {
        it('Create new FIFO tier - success', () =>
            Test.withContext(async context => {
                const successTests = [
                    {
                        case: {
                            item: { id: 'STOFIFO' },
                            site: { id: 'US001' },
                            receiptDocumentLine: 1141,
                            effectiveDate: date.make(2023, 3, 1),
                        },
                        result: { sequence: 1, unitCost: 123 },
                    },
                    {
                        case: {
                            item: { id: 'STOFIFO' },
                            site: { id: 'US001' },
                            receiptDocumentLine: 1142,
                            effectiveDate: date.make(2023, 3, 1),
                        },
                        result: { sequence: 2, unitCost: 125 },
                    },
                    {
                        case: {
                            item: { id: 'STOFIFO' },
                            site: { id: 'US001' },
                            receiptDocumentLine: 1143,
                            effectiveDate: date.make(2023, 3, 4),
                        },
                        result: { sequence: 1, unitCost: 125 },
                    },
                    {
                        case: {
                            item: { id: 'STOFIFO' },
                            site: { id: 'US001' },
                            receiptDocumentLine: 1141,
                            effectiveDate: date.make(2023, 3, 1),
                        },
                        result: { sequence: 3, unitCost: 123 },
                    },
                ];

                const item = await context.read(xtremMasterData.nodes.Item, { id: 'STOFIFO' });
                const site = await context.read(xtremSystem.nodes.Site, { id: 'US001' });
                const location = await context.read(xtremMasterData.nodes.Location, {
                    _id: '#LOC1|US001|Loading dock',
                });
                await asyncArray(successTests).forEach(async (successTest, index) => {
                    const documentLine = await context.read(xtremStock.nodes.StockReceiptLine, {
                        _id: successTest.case.receiptDocumentLine,
                    });
                    const valuationManager = await xtremStock.classes.StockValuationManager.getInstance(context, {
                        action: 'createValue',
                        stockDetails: [
                            await xtremStockData.functions.stockDetailLib.createStockDetail(
                                context,
                                xtremStockData.nodes.StockReceiptDetail,
                                {
                                    effectiveDate: await (await documentLine.document).getEffectiveDate(),
                                    site,
                                    item,
                                    location,
                                    quantityInStockUnit: await documentLine.quantityInStockUnit,
                                    stockUnit: await item.stockUnit,
                                    status: '#A',
                                    orderCost: await documentLine.orderCost,
                                    valuedCost: await documentLine.orderCost,
                                    documentLine,
                                },
                                {
                                    doNotSave: true,
                                },
                            ),
                        ],
                    });
                    assert.isTrue(valuationManager instanceof xtremStock.classes.StockFifoValuationManager);

                    await Promise.resolve(valuationManager.finish(context));

                    const fifoTier = await context
                        .query(xtremStockData.nodes.FifoValuationTier, {
                            filter: {
                                item: successTest.case.item,
                                site: successTest.case.site,
                                effectiveDate: successTest.case.effectiveDate,
                                sequence: successTest.result.sequence,
                            },
                        })
                        .toArray();

                    const message = `test case index=${index}`;

                    assert.deepEqual(fifoTier.length, 1, message);

                    assert.deepEqual(Number(await fifoTier[0].unitCost), Number(successTest.result.unitCost), message);
                });
            }));

        it('test finish method on receipt', () =>
            Test.withContext(async context => {
                const documentLine = await context.read(xtremStock.nodes.StockReceiptLine, { _id: 1144 });
                const valuationManager = await xtremStock.classes.StockValuationManager.getInstance(context, {
                    action: 'createValue',
                    stockDetails: await documentLine.stockDetails.toArray(),
                });
                assert.isTrue(valuationManager instanceof xtremStock.classes.StockFifoValuationManager);

                const fifoStackOrigin = await context
                    .query(xtremStockData.nodes.FifoValuationTier, {
                        filter: { item: documentLine.item, site: documentLine.site },
                    })
                    .toArray();

                await valuationManager.finish(context);

                const fifoStack = await context
                    .query(xtremStockData.nodes.FifoValuationTier, {
                        filter: { item: documentLine.item, site: documentLine.site },
                    })
                    .toArray();

                assert.deepEqual(fifoStack.length, fifoStackOrigin.length + 1);

                const addedFifoTier = await asyncArray(fifoStack).find(
                    async tier => !(await tier.effectiveDate).compare(date.make(2023, 3, 13)),
                );
                assert.isTrue(addedFifoTier instanceof xtremStockData.nodes.FifoValuationTier);
                if (!addedFifoTier) return;

                assert.deepEqual(Number(await addedFifoTier.remainingQuantity), 15);
                assert.deepEqual(Number(await addedFifoTier.receiptQuantity), 15);
                assert.deepEqual(Number(await addedFifoTier.amount), 1845);
                assert.deepEqual(Number(await addedFifoTier.nonAbsorbedAmount), 0);
            }));

        it('test finish method on issue', async () => {
            const sandbox = sinon.createSandbox();

            const loggerInternalsOld = {} as {
                _logAsJson?: boolean;
                isDisabled?: boolean;
            };
            function getLogTemplateSpy() {
                (Logger as any)._logAsJson = true;
                (Logger as any).isDisabled = false;
                return sandbox.spy((Logger as any).consoleFormatter, 'template');
            }

            loggerInternalsOld._logAsJson = (Logger as any)._logAsJson;
            loggerInternalsOld.isDisabled = (Logger as any).isDisabled;

            await Test.withContext(async context => {
                const spy = getLogTemplateSpy();
                const documentLine = await context.read(xtremStock.nodes.StockIssueLine, { _id: 1914 }); // 2 StockIssueDetail
                const valuationManager = await xtremStock.classes.StockValuationManager.getInstance(context, {
                    action: 'createValue',
                    stockDetails: await documentLine.stockDetails.toArray(),
                });
                assert.isTrue(valuationManager instanceof xtremStock.classes.StockFifoValuationManager);

                const fifoStackOrigin = await context
                    .query(xtremStockData.nodes.FifoValuationTier, {
                        filter: { item: documentLine.item, site: documentLine.site },
                    })
                    .toArray();

                await valuationManager.finish(context);

                const fifoStack = await context
                    .query(xtremStockData.nodes.FifoValuationTier, {
                        filter: { item: documentLine.item, site: documentLine.site },
                    })
                    .toArray();

                assert.deepEqual(fifoStack.length, fifoStackOrigin.length);

                // 2 tiers have been consumed totally
                // + a tier has been partially consumed
                const testTiers = [
                    {
                        effectiveDate: date.make(2023, 3, 16),
                        sequence: 1,
                        remainingQuantity: 0,
                        amount: 0,
                        nonAbsorbedAmount: 0,
                    },
                    {
                        effectiveDate: date.make(2023, 3, 17),
                        sequence: 1,
                        remainingQuantity: 0,
                        amount: 0,
                        nonAbsorbedAmount: 0,
                    },
                    {
                        effectiveDate: date.make(2023, 3, 17),
                        sequence: 2,
                        remainingQuantity: 5,
                        amount: 450,
                        nonAbsorbedAmount: 0,
                    },
                ];

                let testedTierCount = 0;
                await asyncArray(testTiers).forEach(async testTier => {
                    const actualTier = await asyncArray(fifoStack).find(
                        async tier =>
                            (await tier.sequence) === testTier.sequence &&
                            !(await tier.effectiveDate).compare(testTier.effectiveDate),
                    );
                    if (actualTier) {
                        const message = `tier.effectiveDate=${await actualTier.effectiveDate}  tier.sequence=${await actualTier.sequence}`;
                        assert.deepEqual(
                            Number(await actualTier.remainingQuantity),
                            testTier.remainingQuantity,
                            message,
                        );
                        assert.deepEqual(Number(await actualTier.amount), testTier.amount, message);
                        assert.deepEqual(
                            Number(await actualTier.nonAbsorbedAmount),
                            testTier.nonAbsorbedAmount,
                            message,
                        );
                        testedTierCount += 1;
                    }
                });
                assert.deepEqual(testedTierCount, testTiers.length);
                const jsonError = spy.returnValues.map(r => JSON.parse(r)).find(r => r.domain === 'xtrem-core/runtime');
                expect(jsonError.message).to.be.equal('cannot use queryWithReader with writable context');
            });

            (Logger as any)._logAsJson = loggerInternalsOld._logAsJson;
            (Logger as any).isDisabled = loggerInternalsOld.isDisabled;
            sandbox.restore();
        });

        it('test finish method on issue that can be returned', () =>
            Test.withContext(async context => {
                const templateDocumentLine = await context.read(xtremStock.nodes.StockIssueLine, { _id: 1914 }); // 2 StockIssueDetail
                const site = await templateDocumentLine.site;
                const item = await templateDocumentLine.item;
                const effectiveDate = await (await templateDocumentLine.document).getEffectiveDate();
                const quantityInStockUnit = await templateDocumentLine.quantityInStockUnit;

                const templateStockDetails = [
                    await templateDocumentLine.stockDetails.elementAt(0),
                    await templateDocumentLine.stockDetails.elementAt(1),
                ];
                const document = await context.create(FakeStockIssue, {
                    number: 'TEST',
                    stockSite: site,
                    effectiveDate,
                    lines: [
                        {
                            lineNumber: 1,
                            item,
                            quantityInStockUnit,
                            canBeReturned: true, // Simulate a sales shipment that can be returned
                            stockDetails: await asyncArray(templateStockDetails)
                                .map(async stockDetail => {
                                    return {
                                        _action: 'create',
                                        effectiveDate,
                                        item,
                                        site,
                                        stockRecord: await stockDetail.stockRecord,
                                        quantityInStockUnit: await stockDetail.quantityInStockUnit,
                                    } as NodeCreateData<
                                        xtremStockData.nodes.StockIssueDetail & {
                                            _action: 'create';
                                        }
                                    >;
                                })
                                .toArray(),
                        },
                    ],
                });
                await document.$.save();
                const documentLine = await document.lines.elementAt(0);

                const valuationManager = await xtremStock.classes.StockValuationManager.getInstance(context, {
                    action: 'createValue',
                    stockDetails: await documentLine.stockDetails.toArray(),
                });
                assert.isTrue(valuationManager instanceof xtremStock.classes.StockFifoValuationManager);

                const fifoStackOrigin = await context
                    .query(xtremStockData.nodes.FifoValuationTier, {
                        filter: { item, site },
                    })
                    .toArray();

                await valuationManager.finish(context);

                const fifoStack = await context
                    .query(xtremStockData.nodes.FifoValuationTier, {
                        filter: { item, site },
                    })
                    .toArray();

                assert.deepEqual(fifoStack.length, fifoStackOrigin.length);

                // 2 tiers have been consumed totally
                // + a tier has been partially consumed
                const testTiers = [
                    {
                        effectiveDate: date.make(2023, 3, 16),
                        sequence: 1,
                        remainingQuantity: 0,
                        amount: 0,
                        nonAbsorbedAmount: 0,
                    },
                    {
                        effectiveDate: date.make(2023, 3, 17),
                        sequence: 1,
                        remainingQuantity: 0,
                        amount: 0,
                        nonAbsorbedAmount: 0,
                    },
                    {
                        effectiveDate: date.make(2023, 3, 17),
                        sequence: 2,
                        remainingQuantity: 5,
                        amount: 450,
                        nonAbsorbedAmount: 0,
                    },
                ];

                let testedTierCount = 0;
                await asyncArray(testTiers).forEach(async testTier => {
                    const actualTier = await asyncArray(fifoStack).find(
                        async tier =>
                            (await tier.sequence) === testTier.sequence &&
                            !(await tier.effectiveDate).compare(testTier.effectiveDate),
                    );
                    if (actualTier) {
                        const message = `tier.effectiveDate=${await actualTier.effectiveDate}  tier.sequence=${await actualTier.sequence}`;
                        assert.deepEqual(
                            Number(await actualTier.remainingQuantity),
                            testTier.remainingQuantity,
                            message,
                        );
                        assert.deepEqual(Number(await actualTier.amount), testTier.amount, message);
                        assert.deepEqual(
                            Number(await actualTier.nonAbsorbedAmount),
                            testTier.nonAbsorbedAmount,
                            message,
                        );
                        testedTierCount += 1;
                    }
                });
                assert.deepEqual(testedTierCount, testTiers.length);
            }));

        it('test finish method on negative issue', () =>
            Test.withContext(async context => {
                const effectiveDate = date.make(2023, 5, 1);
                const returnedQuantity = 8;
                // the FifoValuationIssue records used in the test are linked to the StockIssueLine 1913
                // but the document used to test the negative issue is a FakeStockNegativeIssue
                const originIssueLine = await context.read(xtremStock.nodes.StockIssueLine, { _id: 1913 });
                const item = await originIssueLine.item;
                const site = await (await originIssueLine.document).getStockSite();

                const oldFifoTiers: Array<NodePayloadData<xtremStockData.nodes.FifoValuationTier>> = [
                    {
                        effectiveDate,
                        sequence: 1,
                        receiptQuantity: 100,
                        remainingQuantity: 5,
                        amount: 650,
                        nonAbsorbedAmount: 0,
                    },
                    {
                        effectiveDate,
                        sequence: 2,
                        receiptQuantity: 100,
                        remainingQuantity: 0,
                        amount: 0,
                        nonAbsorbedAmount: 0,
                    },
                ];
                const newFifoTiers: Array<NodePayloadData<xtremStockData.nodes.FifoValuationTier>> = _.clone(
                    oldFifoTiers,
                ).concat([
                    {
                        effectiveDate,
                        sequence: 3,
                        receiptQuantity: 2,
                        remainingQuantity: 2,
                        amount: 2 * 135,
                        nonAbsorbedAmount: 0,
                    },
                    {
                        effectiveDate,
                        sequence: 4,
                        receiptQuantity: 6,
                        remainingQuantity: 6,
                        amount: 6 * 130,
                        nonAbsorbedAmount: 0,
                    },
                ]);

                const oldFifoIssueTiers = [
                    {
                        effectiveDate,
                        sequence: 1,
                        issuedQuantity: 7,
                        remainingQuantity: 7,
                        amount: 650,
                        nonAbsorbedAmount: 0,
                    },
                    {
                        effectiveDate,
                        sequence: 2,
                        issuedQuantity: 3,
                        remainingQuantity: 2,
                        amount: 2295,
                        nonAbsorbedAmount: 0,
                    },
                ];
                const newFifoIssueTiers = _.clone(oldFifoIssueTiers);
                newFifoIssueTiers[0].remainingQuantity -= 6;
                newFifoIssueTiers[0].amount -= 6 * 130;
                newFifoIssueTiers[1].remainingQuantity -= 2;
                newFifoIssueTiers[1].amount -= 2 * 135;

                // check starting data
                await controlTiers(
                    context,
                    { item, site, effectiveDate },
                    oldFifoTiers,
                    { issueDocumentLine: originIssueLine._id },
                    oldFifoIssueTiers,
                );

                const negativeIssue = await context.create(testLib.nodes.FakeStockNegativeIssue, {
                    number: 'TEST',
                    site,
                    date: effectiveDate,
                    lines: [
                        {
                            lineNumber: 1,
                            item,
                            quantity: returnedQuantity,
                            originIssueLine,
                            stockDetails: [
                                {
                                    _action: 'create',
                                    effectiveDate,
                                    item,
                                    site,
                                    location: await context.read(xtremMasterData.nodes.Location, {
                                        locationZone: '#US001|Loading dock',
                                        id: 'LOC1',
                                    }),
                                    status: await context.read(xtremStockData.nodes.StockStatus, { id: 'R' }),
                                    quantityInStockUnit: returnedQuantity,
                                },
                            ],
                        },
                    ],
                });
                await negativeIssue.$.save();

                const negativeIssueLine = await negativeIssue.lines.elementAt(0);

                const valuationManager = await xtremStock.classes.StockValuationManager.getInstance(context, {
                    action: 'createValue',
                    stockDetails: await negativeIssueLine.stockDetails.toArray(),
                });

                await valuationManager.finish(context);

                // check new data
                await controlTiers(
                    context,
                    { item, site, effectiveDate },
                    newFifoTiers,
                    { issueDocumentLine: originIssueLine._id },
                    newFifoIssueTiers,
                );
            }));

        // XT-86662: allow negative issue in FIFO even if the issue was not created by using FIFO
        it('test finish methods on FIFO negative issue with missing FifoValuationIssue origin', () =>
            Test.withContext(async context => {
                const effectiveDate = date.make(2025, 1, 6);
                const returnedQuantity = 5;
                // the FifoValuationIssue records used in the test are linked to the StockIssueLine 1913
                // but the document used to test the negative issue is a FakeStockNegativeIssue
                const originIssueLine = await context.read(xtremStock.nodes.StockIssueLine, { _id: 1917 });
                const item = await originIssueLine.item;
                const site = await (await originIssueLine.document).getStockSite();

                const oldFifoTiers: Array<NodePayloadData<xtremStockData.nodes.FifoValuationTier>> = [];
                const newFifoTiers: Array<NodePayloadData<xtremStockData.nodes.FifoValuationTier>> = [
                    {
                        effectiveDate,
                        sequence: 1,
                        receiptQuantity: 5,
                        remainingQuantity: 5,
                        amount: 795,
                        nonAbsorbedAmount: 0,
                    },
                ];

                const oldFifoIssueTiers: Array<NodePayloadData<xtremStockData.nodes.FifoValuationIssue>> = [];
                const newFifoIssueTiers: Array<NodePayloadData<xtremStockData.nodes.FifoValuationIssue>> = [
                    {
                        effectiveDate,
                        sequence: 1,
                        issuedQuantity: 7,
                        remainingQuantity: 7,
                        amount: 1113,
                    },
                ];

                // check starting data
                await controlTiers(
                    context,
                    { item, site, effectiveDate },
                    oldFifoTiers,
                    { issueDocumentLine: originIssueLine._id },
                    oldFifoIssueTiers,
                );

                const negativeIssue = await context.create(testLib.nodes.FakeStockNegativeIssue, {
                    number: 'TEST',
                    site,
                    date: effectiveDate,
                    lines: [
                        {
                            lineNumber: 1,
                            item,
                            quantity: returnedQuantity,
                            originIssueLine,
                            stockDetails: [
                                {
                                    _action: 'create',
                                    effectiveDate,
                                    item,
                                    site,
                                    location: await context.read(xtremMasterData.nodes.Location, {
                                        locationZone: '#US001|Loading dock',
                                        id: 'LOC1',
                                    }),
                                    status: await context.read(xtremStockData.nodes.StockStatus, { id: 'R' }),
                                    quantityInStockUnit: returnedQuantity,
                                },
                            ],
                        },
                    ],
                });
                await negativeIssue.$.save();

                const negativeIssueLine = await negativeIssue.lines.elementAt(0);

                const valuationManager = await xtremStock.classes.StockValuationManager.getInstance(context, {
                    action: 'createValue',
                    stockDetails: await negativeIssueLine.stockDetails.toArray(),
                });

                await valuationManager.finish(context);

                // check new data
                await controlTiers(
                    context,
                    { item, site, effectiveDate },
                    newFifoTiers,
                    { issueDocumentLine: originIssueLine._id },
                    newFifoIssueTiers,
                );
            }));

        it('test finish method on negative receipt', () =>
            Test.withContext(async context => {
                const effectiveDate = date.make(2023, 5, 1);
                // usage of a stock receipt as 'receipt to return' but the corresponding tier has been consumed
                // As 3 are returned the next tier must be consumed
                const originReceiptLine = await context.read(xtremStock.nodes.StockReceiptLine, { _id: 1150 });
                const consumedTierReceiptLine = await context.read(xtremStock.nodes.StockReceiptLine, { _id: 1152 });
                const site = await originReceiptLine.site;
                const item = await originReceiptLine.item;
                const quantityInStockUnit = await originReceiptLine.quantityInStockUnit;

                // The tier of the receipt is already consumed
                await controlTiers(context, { item, site, receiptDocumentLine: originReceiptLine }, [
                    { remainingQuantity: 0, amount: 0, nonAbsorbedAmount: 0 },
                ]);
                // The next tier still have a quantity
                await controlTiers(context, { item, site, receiptDocumentLine: consumedTierReceiptLine }, [
                    { remainingQuantity: 3, amount: 61.5, nonAbsorbedAmount: 0 },
                ]);

                const negativeReceipt = await context.create(FakeStockNegativeReceipt, {
                    number: 'TEST',
                    stockSite: site,
                    effectiveDate,
                    lines: [
                        {
                            lineNumber: 1,
                            item,
                            quantityInStockUnit,
                            originReceiptLine,
                            stockDetails: [1, 2].map(quantity => {
                                return {
                                    _action: 'create',
                                    effectiveDate,
                                    item,
                                    site,
                                    stockRecord: 96,
                                    quantityInStockUnit: -quantity,
                                } as NodeCreateData<
                                    xtremStockData.nodes.StockIssueDetail & {
                                        _action: 'create';
                                    }
                                >;
                            }),
                        },
                    ],
                });
                await negativeReceipt.$.save();

                const negativeIssueLine = await negativeReceipt.lines.elementAt(0);

                const valuationManager = await xtremStock.classes.StockValuationManager.getInstance(context, {
                    action: 'createValue',
                    stockDetails: await negativeIssueLine.stockDetails.toArray(),
                });
                assert.isTrue(valuationManager instanceof xtremStock.classes.StockFifoValuationManager);

                await valuationManager.finish(context);

                // The tier still have 0
                await controlTiers(context, { item, site, receiptDocumentLine: originReceiptLine }, [
                    { remainingQuantity: 0, amount: 0, nonAbsorbedAmount: 0 },
                ]);
                // The next tier is consumed
                await controlTiers(context, { item, site, receiptDocumentLine: consumedTierReceiptLine }, [
                    { remainingQuantity: 0, amount: 0, nonAbsorbedAmount: 0 },
                ]);
            }));

        it('test finish method on value change', () =>
            Test.withContext(async context => {
                const document = await testLib.functions.TestHelpers.createStockValueChangeDocument(context, {
                    item: '#STOFIFO-MAT1',
                    site: '#US001',
                    lineAmounts: [
                        {
                            oldAmount: 840,
                            newAmount: 832.98,
                            quantity: 8,
                            fifoTier: await context.read(xtremStockData.nodes.FifoValuationTier, { _id: 115100 }),
                        },
                    ],
                });

                let documentLine = await document.lines.elementAt(0);
                const valuationManager = await xtremStock.classes.StockValuationManager.getInstance(context, {
                    action: 'changeValue',
                    stockDetails: await documentLine.stockDetails.toArray(),
                });
                assert.isTrue(valuationManager instanceof xtremStock.classes.StockFifoValuationManager);

                const fifoValuationTierBefore = await (await documentLine.stockDetails.elementAt(0)).fifoValuationTier;
                assert.exists(fifoValuationTierBefore);

                await valuationManager.finish(context);

                documentLine = await context.read(xtremStock.nodes.StockValueChangeLine, { _id: documentLine._id });
                const fifoValuationTierAfter = await (await documentLine.stockDetails.elementAt(0)).fifoValuationTier;
                assert.exists(fifoValuationTierAfter);

                assert.deepEqual(fifoValuationTierAfter?._id, fifoValuationTierBefore?._id);
                assert.deepEqual(await fifoValuationTierAfter?.amount, 832.98);
                assert.equal(await fifoValuationTierAfter?.nonAbsorbedAmount, 0);
            }));
    });
});
