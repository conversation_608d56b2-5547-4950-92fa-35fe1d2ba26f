import { Test, asyncArray } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import { assert } from 'chai';
import * as xtremStock from '../../../lib';
import * as testLib from '../../fixtures/lib';

describe('Stock Standard Cost Valuation Manager', () => {
    describe('get amounts', () => {
        it('test get methods on correction', () =>
            Test.withContext(async context => {
                const variance = 100;
                const { valuationManager, documentLine, stockCorrectionDetails } =
                    await testLib.functions.TestHelpers.simulateStockReceiptCorrection(context, {
                        variance,
                        correctedDocumentNumber: 'MISC_RECEIPT10',
                        itemLineToCorrect: 'StockItem02',
                        correctorDocumentNumber: 'MISC_RECEIPT18',
                    });

                assert.isTrue(valuationManager instanceof xtremStock.classes.StockStandardCostValuationManager);

                const expectedValues = [
                    { orderedAmount: 75.0, valuedAmount: 0.0, nonAbsorbedAmount: 75.0 },
                    { orderedAmount: 25.0, valuedAmount: 0.0, nonAbsorbedAmount: 25.0 },
                ];
                await asyncArray(expectedValues).forEach(async (expectedValue, index) => {
                    testLib.functions.TestHelpers.checkStockValuationManagerValues(
                        {
                            valuationManager,
                            documentLine,
                            quantity: await stockCorrectionDetails[index].impactedQuantity,
                            stockDetailId: stockCorrectionDetails[index]._id,
                        },
                        expectedValue,
                    );
                });
            }));
        it('test get methods on value change', () =>
            Test.withContext(async context => {
                const itemSite = await context.read(xtremMasterData.nodes.ItemSite, {
                    item: '#STOSTD',
                    site: '#US001',
                });
                const oldCost = await itemSite.stdCostValue;
                const quantity = 1.47;
                const newCost = oldCost + 10.12;
                const document = await testLib.functions.TestHelpers.createStockValueChangeDocument(context, {
                    itemSite,
                    lineAmounts: [
                        {
                            oldAmount: oldCost * quantity,
                            newAmount: newCost * quantity,
                            quantity: newCost,
                        },
                    ],
                });

                const documentLine = await document.lines.elementAt(0);
                const valuationManager = await xtremStock.classes.StockValuationManager.getInstance(context, {
                    action: 'changeValue',
                    stockDetails: await documentLine.stockDetails.toArray(),
                });

                assert.isTrue(valuationManager instanceof xtremStock.classes.StockStandardCostValuationManager);
                assert.deepEqual(valuationManager.getOrderAmount(documentLine), 0);
                assert.deepEqual(valuationManager.getValuedAmount(documentLine), 14.88);
                assert.deepEqual(valuationManager.getNonAbsorbedAmount(documentLine), 0);
            }));
    });
});
