import { date, Test } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremStockData from '@sage/xtrem-stock-data';
import * as xtremSystem from '@sage/xtrem-system';
import { assert } from 'chai';
import * as xtremStock from '../../../lib';

describe('Stock Valuation Manager failing', () => {
    it('should refuse an empty stockDetails', () =>
        Test.withContext(async context => {
            const documentLine = await context.read(xtremStock.nodes.StockReceiptLine, { _id: 1101 }); // Stock details do not exist
            await assert.isRejected(
                xtremStock.classes.StockValuationManager.getInstance(context, {
                    action: 'createValue',
                    stockDetails: await documentLine.stockDetails.toArray(),
                }),
                'StockDetails is empty',
            );
        }));

    it('should refuse stockDetails with different item-sites', () =>
        Test.withContext(async context => {
            const documentLine = await context.read(xtremStock.nodes.StockReceiptLine, { _id: 1101 });
            const item = await context.read(xtremMasterData.nodes.Item, { id: 'STOAVC' });
            const item2 = await context.read(xtremMasterData.nodes.Item, { id: 'STOFIFO' });
            const site = await context.read(xtremSystem.nodes.Site, { id: 'US001' });
            const location = await context.read(xtremMasterData.nodes.Location, { _id: '#LOC1|US001|Loading dock' });
            // Try to combine 2 stock details with different item-sites
            const stockDetails = [];
            stockDetails.push(
                await xtremStockData.functions.stockDetailLib.createStockDetail(
                    context,
                    xtremStockData.nodes.StockReceiptDetail,
                    {
                        effectiveDate: date.today(),
                        site,
                        item,
                        location,
                        quantityInStockUnit: 50,
                        stockUnit: await item.stockUnit,
                        status: '#A',
                        orderCost: 0.5,
                        valuedCost: 0.44,
                        documentLine,
                    },
                    {
                        doNotSave: true,
                    },
                ),
            );
            stockDetails.push(
                await xtremStockData.functions.stockDetailLib.createStockDetail(
                    context,
                    xtremStockData.nodes.StockReceiptDetail,
                    {
                        effectiveDate: date.today(),
                        site,
                        item: item2,
                        location,
                        quantityInStockUnit: 50,
                        stockUnit: await item.stockUnit,
                        status: '#A',
                        orderCost: 0.5,
                        valuedCost: 0.44,
                        documentLine,
                    },
                    {
                        doNotSave: true,
                    },
                ),
            );
            await assert.isRejected(
                xtremStock.classes.StockValuationManager.getInstance(context, {
                    action: 'createValue',
                    stockDetails,
                }),
                'StockDetails contains several item-sites: STOAVC|US001 and STOFIFO|US001',
            );
        }));

    it('should refuse stockDetails with different document lines', () =>
        Test.withContext(async context => {
            const documentLine1 = await context.read(xtremStock.nodes.StockReceiptLine, { _id: 1144 });
            const documentLine2 = await context.read(xtremStock.nodes.StockReceiptLine, { _id: 1145 });
            const item = await context.read(xtremMasterData.nodes.Item, { id: 'STOFIFO' });
            const site = await context.read(xtremSystem.nodes.Site, { id: 'US001' });
            const location = await context.read(xtremMasterData.nodes.Location, { _id: '#LOC1|US001|Loading dock' });
            // Try to combine 2 stock details with different document lines
            const stockDetails = [];
            stockDetails.push(
                await xtremStockData.functions.stockDetailLib.createStockDetail(
                    context,
                    xtremStockData.nodes.StockReceiptDetail,
                    {
                        effectiveDate: date.today(),
                        site,
                        item,
                        location,
                        quantityInStockUnit: 50,
                        stockUnit: await item.stockUnit,
                        status: '#A',
                        orderCost: 0.5,
                        valuedCost: 0.44,
                        documentLine: documentLine1,
                    },
                    {
                        doNotSave: true,
                    },
                ),
            );
            stockDetails.push(
                await xtremStockData.functions.stockDetailLib.createStockDetail(
                    context,
                    xtremStockData.nodes.StockReceiptDetail,
                    {
                        effectiveDate: date.today(),
                        site,
                        item,
                        location,
                        quantityInStockUnit: 50,
                        stockUnit: await item.stockUnit,
                        status: '#A',
                        orderCost: 0.5,
                        valuedCost: 0.44,
                        documentLine: documentLine2,
                    },
                    {
                        doNotSave: true,
                    },
                ),
            );
            await assert.isRejected(
                xtremStock.classes.StockValuationManager.getInstance(context, {
                    action: 'createValue',
                    stockDetails,
                }),
                'StockDetails contains several document lines: 1144 and 1145',
            );
        }));
});

describe('Stock Valuation Manager success', () => {
    it('should create a StockFifoValuationManager instance', () =>
        Test.withContext(async context => {
            const documentLine = await context.read(xtremStock.nodes.StockReceiptLine, { _id: 1144 });
            const valuationManager = await xtremStock.classes.StockValuationManager.getInstance(context, {
                action: 'createValue',
                stockDetails: await documentLine.stockDetails.toArray(),
            });
            assert.isTrue(valuationManager instanceof xtremStock.classes.StockFifoValuationManager);
        }));
});
