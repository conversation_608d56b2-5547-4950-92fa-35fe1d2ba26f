import type { Context } from '@sage/xtrem-core';
import { Test } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremSystem from '@sage/xtrem-system';
import { assert } from 'chai';
import * as sinon from 'sinon';
import * as xtremStock from '../../../lib';

describe('getVirtualLocation Function', () => {
    let contextCreateSpy: sinon.SinonSpy;

    const getSite = (context: Context) => context.read(xtremSystem.nodes.Site, { id: 'CAS01' });

    afterEach(() => {
        if (contextCreateSpy) {
            contextCreateSpy.restore();
        }
    });

    it('should return a virtual Location', async () => {
        await Test.withContext(async context => {
            const site = await getSite(context);

            const location = await xtremStock.functions.location.getVirtualLocation(context, 'transfer', site);

            assert.equal(await location.id, 'TRANSFER_LOCATION');
            assert.equal(await location.name, 'Transfer location');
        });
    });

    it('should create a virtual Location when none exist', async () => {
        await Test.withContext(async context => {
            const site = await getSite(context);
            contextCreateSpy = sinon.spy(context, 'create');

            await xtremStock.functions.location.getVirtualLocation(context, 'transfer', site);

            // if no virtual location, locationType nor locationZone is in the data layer getVirtualLocation
            // should create 3 new nodes but only the third one must be an instance of Location
            assert.equal(contextCreateSpy.callCount, 3);
            assert.equal(contextCreateSpy.getCall(2)?.args?.at(0), xtremMasterData.nodes.Location);
        });
    });

    it('should create a virtual LocationZone when none exist', async () => {
        await Test.withContext(async context => {
            const site = await getSite(context);
            contextCreateSpy = sinon.spy(context, 'create');

            await xtremStock.functions.location.getVirtualLocation(context, 'transfer', site);

            // if no virtual location, locationType nor locationZone is in the data layer getVirtualLocation
            // should create 3 new nodes but only the second one must be an instance of LocationZone
            assert.equal(contextCreateSpy.callCount, 3);
            assert.equal(contextCreateSpy.getCall(1)?.args?.at(0), xtremMasterData.nodes.LocationZone);
        });
    });

    it('should create a virtual LocationType when none exist', async () => {
        await Test.withContext(async context => {
            const site = await getSite(context);
            contextCreateSpy = sinon.spy(context, 'create');

            await xtremStock.functions.location.getVirtualLocation(context, 'transfer', site);

            // if no virtual location, locationType nor locationZone is in the data layer getVirtualLocation
            // should create 3 new nodes but only the first one must be an instance of LocationType
            assert.equal(contextCreateSpy.callCount, 3);
            assert.equal(contextCreateSpy.getCall(0)?.args?.at(0), xtremMasterData.nodes.LocationType);
        });
    });

    it('should not create a virtual Location when one exist', async () => {
        await Test.withContext(async context => {
            const site = await getSite(context);

            // Create a LocationType and LocationZone for the Location with valid values
            const locationType = await context.create(xtremMasterData.nodes.LocationType, {
                id: 'TRANSFER_TYPE', // must be this
                name: 'Transfer Type Test',
                description: 'Description for Transfer Type Test',
                locationCategory: 'virtual',
                isVirtualAllowed: true,
            });
            await locationType.$.save();
            const locationZone = await context.create(xtremMasterData.nodes.LocationZone, {
                id: 'TRANSFER_ZONE', // must be this
                site,
                name: 'Transfer Zone Test',
                zoneType: 'virtual',
                isVirtualAllowed: true,
            });
            await locationZone.$.save();

            // Create a Location with the TRANSFER_LOCATION value before setting up the spy
            const location = await context.create(xtremMasterData.nodes.Location, {
                id: 'TRANSFER_LOCATION', // must be this
                site,
                name: 'Transfer Location Test',
                locationType,
                locationZone,
                isVirtualAllowed: true,
            });
            await location.$.save();

            contextCreateSpy = sinon.spy(context, 'create');

            const returnedLocation = await xtremStock.functions.location.getVirtualLocation(context, 'transfer', site);

            // if a virtual location already exist for that need no new node will be created.
            assert.isTrue(contextCreateSpy.notCalled);

            assert.equal(await returnedLocation.id, 'TRANSFER_LOCATION');
            assert.equal(await returnedLocation.name, 'Transfer Location Test');

            const returnedLocationType = await location.locationType;
            assert.equal(await returnedLocationType.id, 'TRANSFER_TYPE');
            assert.equal(await returnedLocationType.name, 'Transfer Type Test');
            assert.equal(await returnedLocationType.description, 'Description for Transfer Type Test');
            assert.equal(await returnedLocationType.locationCategory, 'virtual');

            const returnedLocationZone = await location.locationZone;
            assert.equal(await returnedLocationZone.id, 'TRANSFER_ZONE');
            assert.equal(await returnedLocationZone.name, 'Transfer Zone Test');
            assert.equal(await returnedLocationZone.zoneType, 'virtual');
        });
    });

    it('should not create a virtual Location using virtual type and zone', async () => {
        await Test.withContext(async context => {
            const site = await getSite(context);

            // Create a LocationType and LocationZone for the Location with valid values
            const locationType = await context.create(xtremMasterData.nodes.LocationType, {
                id: 'TRANSFER_TYPE', // must be this
                name: 'Transfer Type Test',
                description: 'Description for Transfer Type Test',
                locationCategory: 'virtual',
                isVirtualAllowed: true,
            });
            await locationType.$.save();
            const locationZone = await context.create(xtremMasterData.nodes.LocationZone, {
                id: 'TRANSFER_ZONE', // must be this
                site,
                name: 'Transfer Zone Test',
                zoneType: 'virtual',
                isVirtualAllowed: true,
            });
            await locationZone.$.save();

            // Create a Location with the TRANSFER_LOCATION value before setting up the spy
            const location = await context.create(xtremMasterData.nodes.Location, {
                id: 'TRANSFER_LOCATION', // must be this
                site,
                name: 'Transfer Location Test',
                locationType,
                locationZone,
                isVirtualAllowed: false,
            });
            await assert.isRejected(location.$.save());
            assert.includeDeepMembers(location.$.context.diagnoses, [
                {
                    severity: 3,
                    path: ['locationZone'],
                    message: 'The virtual location zone is not valid. You need to select a different location zone.',
                },
                {
                    severity: 3,
                    path: ['locationType'],
                    message:
                        'The virtual location category is not valid. You need to select a different location category.',
                },
            ]);
        });
    });

    it('should not create a virtual Location when it is not allowed', async () => {
        await Test.withContext(async context => {
            const site = await getSite(context);

            const locationZone = await context.create(xtremMasterData.nodes.LocationZone, {
                id: 'TRANSFER_ZONE',
                site,
                name: 'Transfer Zone Test',
                zoneType: 'virtual',
                isVirtualAllowed: false,
            });
            await assert.isRejected(locationZone.$.save());
            assert.includeDeepMembers(locationZone.$.context.diagnoses, [
                {
                    severity: 3,
                    path: ['zoneType'],
                    message: 'Virtual location zone type is not allowed.',
                },
            ]);
        });
    });

    it('should not create a virtual LocationZone when one exist', async () => {
        await Test.withContext(async context => {
            const site = await getSite(context);

            // Create a LocationZone with a valid zoneType value before setting up the spy
            const locationZone = await context.create(xtremMasterData.nodes.LocationZone, {
                id: 'TRANSFER_ZONE', // must be this
                site,
                name: 'Transfer Zone',
                zoneType: 'virtual',
                isVirtualAllowed: true,
            });
            await locationZone.$.save();

            contextCreateSpy = sinon.spy(context, 'create');

            await xtremStock.functions.location.getVirtualLocation(context, 'transfer', site);

            // if no virtual location nor locationZone is in the data layer getVirtualLocation
            // should create 2 new nodes of which the first one must be an instance of LocationType
            // and the second one must be an instance of Location
            assert.equal(contextCreateSpy.callCount, 2);
            assert.equal(contextCreateSpy.getCall(0)?.args?.at(0), xtremMasterData.nodes.LocationType);
            assert.equal(contextCreateSpy.getCall(1)?.args?.at(0), xtremMasterData.nodes.Location);
        });
    });

    it('should not create a virtual LocationType when one exist', async () => {
        await Test.withContext(async context => {
            const site = await getSite(context);

            // Create a LocationType with a valid locationCategory value before setting up the spy
            const locationType = await context.create(xtremMasterData.nodes.LocationType, {
                id: 'TRANSFER_TYPE', // must be this
                name: 'Transfer Type',
                description: 'Description for Transfer Type',
                locationCategory: 'virtual',
                isVirtualAllowed: true,
            });
            await locationType.$.save();

            contextCreateSpy = sinon.spy(context, 'create');

            await xtremStock.functions.location.getVirtualLocation(context, 'transfer', site);

            // if no virtual location nor locationType is in the data layer getVirtualLocation
            // should create 2 new nodes of which the first one must be an instance of LocationZone
            // and the second one must be an instance of Location
            assert.equal(contextCreateSpy.callCount, 2);
            assert.equal(contextCreateSpy.getCall(0)?.args?.at(0), xtremMasterData.nodes.LocationZone);
            assert.equal(contextCreateSpy.getCall(1)?.args?.at(0), xtremMasterData.nodes.Location);
        });
    });

    it('should throw an error for invalid need value "forced"', async () => {
        await Test.withContext(async context => {
            const site = await getSite(context);

            await assert.isRejected(
                xtremStock.functions.location.getVirtualLocation(context, 'forced' as any, site),
                'Unknown virtual location need: forced',
            );
        });
    });

    it('should throw an error for empty need value', async () => {
        await Test.withContext(async context => {
            const site = await getSite(context);

            await assert.isRejected(
                xtremStock.functions.location.getVirtualLocation(context, '' as any, site),
                'Unknown virtual location need: ',
            );
        });
    });

    it('should throw an error for null need value', async () => {
        await Test.withContext(async context => {
            const site = await getSite(context);

            await assert.isRejected(
                xtremStock.functions.location.getVirtualLocation(context, null as any, site),
                'Unknown virtual location need: null',
            );
        });
    });

    it('should throw an error for undefined need value', async () => {
        await Test.withContext(async context => {
            const site = await getSite(context);

            await assert.isRejected(
                xtremStock.functions.location.getVirtualLocation(context, undefined as any, site),
                'Unknown virtual location need: undefined',
            );
        });
    });
});
