import type { decimal, integer } from '@sage/xtrem-core';
import { asyncArray, date, Test } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremStockData from '@sage/xtrem-stock-data';
import * as xtremSystem from '@sage/xtrem-system';
import { assert } from 'chai';
import * as xtremStock from '../../../lib';
import * as stockEngine from '../../../lib/functions/stock-engine';

describe('FifoValuationTier', () => {
    it('Create new FIFO tier - fails', () =>
        Test.withContext(
            async context => {
                const failingTests = [
                    {
                        case: {
                            item: { id: 'STOFIFO' },
                            site: { id: 'US001' },
                            effectiveDate: date.make(2023, 3, 1),
                            receiptDocumentLine: 1140,
                            sequence: 1,
                        },
                        diagnoses: [
                            {
                                severity: 3,
                                path: [],
                                message:
                                    'The item-site (3542LOT - US003) of the receipt line does not match the item-site (STOFIFO - US001) of the FIFO tier.',
                            },
                            {
                                severity: 4,
                                path: ['receiptQuantity'],
                                message: 'FifoValuationTier.receiptQuantity: property is required',
                            },
                        ],
                    },
                    {
                        case: {
                            item: { id: 'STOFIFO' },
                            site: { id: 'US001' },
                            effectiveDate: date.make(2023, 3, 1),
                            receiptDocumentLine: 1141,
                            receiptQuantity: -12,
                            sequence: 1,
                        },
                        diagnoses: [
                            {
                                severity: 3,
                                path: ['receiptQuantity'],
                                message: 'value must not be negative',
                            },
                            {
                                severity: 3,
                                path: ['remainingQuantity'],
                                message: 'value must not be greater than -12',
                            },
                        ],
                    },
                    {
                        case: {
                            item: { id: 'STOFIFO' },
                            site: { id: 'US001' },
                            effectiveDate: date.make(2023, 3, 1),
                            receiptDocumentLine: 1141,
                            receiptQuantity: 30,
                            remainingQuantity: -5,
                            sequence: 1,
                        },
                        diagnoses: [
                            {
                                severity: 3,
                                path: ['remainingQuantity'],
                                message: 'value must not be negative',
                            },
                        ],
                    },
                    {
                        case: {
                            item: { id: 'STOFIFO' },
                            site: { id: 'US001' },
                            effectiveDate: date.make(2023, 5, 1),
                            receiptDocumentLine: 1141,
                            receiptQuantity: 30,
                            remainingQuantity: 5,
                            sequence: 1,
                        },
                        diagnoses: [
                            {
                                severity: 3,
                                path: ['effectiveDate'],
                                message: 'value must not be after 2023-03-10',
                            },
                        ],
                    },
                ];

                await asyncArray(failingTests).forEach(async (failingTest, index) => {
                    const fifoValuationRecord = await context.create(xtremStockData.nodes.FifoValuationTier, {
                        item: failingTest.case.item,
                        site: failingTest.case.site,
                        effectiveDate: failingTest.case.effectiveDate,
                        receiptDocumentLine: failingTest.case.receiptDocumentLine,
                        receiptQuantity: failingTest.case.receiptQuantity,
                        remainingQuantity: failingTest.case.remainingQuantity,
                        amount: 0,
                        sequence: failingTest.case.sequence,
                    });
                    await assert.isRejected(
                        fifoValuationRecord.$.save(),
                        'The record was not created.',
                        `test case index=${index}`,
                    );
                    assert.deepEqual(context.diagnoses, failingTest.diagnoses, `test case index=${index}`);
                });
            },
            {
                testActiveServiceOptions: [xtremMasterData.serviceOptions.fifoValuationMethodOption],
                today: '2023-03-10',
            },
        ));

    it('should create a unique fifo valuation tier for a stock receipt line', () =>
        Test.withContext(
            async context => {
                const stockReceiptDetails: xtremStockData.nodes.StockReceiptDetail[] = await context
                    .query(xtremStockData.nodes.StockReceiptDetail, { filter: { documentLine: 1144 } })
                    .toArray();
                const stockValuationManager = await xtremStock.classes.StockValuationManager.getInstance(context, {
                    action: 'createValue',
                    stockDetails: stockReceiptDetails,
                });
                const result = await stockEngine.stockReceipt(context, stockReceiptDetails, {
                    movementType: 'receipt',
                    stockValuationManager,
                });
                await Promise.resolve(stockValuationManager.finish(context));

                const fifo = await context
                    .query(xtremStockData.nodes.FifoValuationTier, {
                        filter: { receiptDocumentLine: 1144 },
                    })
                    .toArray();

                assert.deepEqual(result[0].resultAction, 'increased');
                assert.deepEqual(fifo.length, 1);
                assert.deepEqual(await (await fifo[0].item).id, 'STOFIFO');
                assert.deepEqual(await (await fifo[0].site).id, 'US001');
                assert.deepEqual(Number(await fifo[0].receiptQuantity), 15);
                assert.deepEqual(Number(await fifo[0].remainingQuantity), 15);
                assert.deepEqual(Number(await fifo[0].amount), 1845);
                assert.deepEqual(Number(await fifo[0].unitCost), 123);
                assert.deepEqual(Number(await fifo[0].nonAbsorbedAmount), 0);
                assert.deepEqual((await fifo[0].effectiveDate).toString(), '2023-03-13');
            },
            {
                testActiveServiceOptions: [xtremMasterData.serviceOptions.fifoValuationMethodOption],
                today: '2023-03-15',
            },
        ));
    it('consume FIFO tier', () =>
        Test.withContext(
            async context => {
                const item = await context.read(xtremMasterData.nodes.Item, { id: 'STOFIFO' });
                const site = await context.read(xtremSystem.nodes.Site, { id: 'US001' });

                type FifoIssueResultType = {
                    issueDocumentLine: number;
                    effectiveDate: date;
                    sequence: integer;
                    remainingQuantity: decimal;
                    amount: decimal;
                };
                const expectedResult: Array<FifoIssueResultType> = [
                    {
                        issueDocumentLine: 1914,
                        effectiveDate: date.make(2023, 3, 16),
                        sequence: 1,
                        remainingQuantity: 12,
                        amount: 1440,
                    },
                    {
                        issueDocumentLine: 1914,
                        effectiveDate: date.make(2023, 3, 17),
                        sequence: 1,
                        remainingQuantity: 10,
                        amount: 1000,
                    },
                    {
                        issueDocumentLine: 1914,
                        effectiveDate: date.make(2023, 3, 17),
                        sequence: 2,
                        remainingQuantity: 5,
                        amount: 450,
                    },
                ];

                await xtremStockData.functions.fifoValuationLib.consumeItemSiteFifoValuationTier(
                    context,
                    item,
                    site,
                    27,
                    {
                        withUpdate: true,
                        afterUpdateCallback: async (
                            tier: xtremStockData.nodes.FifoValuationTier,
                            consumedQuantity: decimal,
                            consumedValue: decimal,
                        ) => {
                            // Record the issue if the issueDocumentLine has been passed in parameters
                            const fifoIssue = await context.create(xtremStockData.nodes.FifoValuationIssue, {
                                issueDocumentLine: 1914,
                                effectiveDate: await tier.effectiveDate,
                                sequence: await tier.sequence,
                                issuedQuantity: consumedQuantity,
                                amount: consumedValue,
                                createDate: date.today(),
                                remainingQuantity: consumedQuantity,
                            });
                            await fifoIssue.$.save();
                        },
                    },
                );

                const fifoIssues = await context
                    .query(xtremStockData.nodes.FifoValuationIssue, {
                        filter: { issueDocumentLine: 1914 },
                        orderBy: { effectiveDate: 1, sequence: 1, remainingQuantity: 1 },
                    })
                    .map(
                        async (fifoIssue): Promise<FifoIssueResultType> => ({
                            issueDocumentLine: (await fifoIssue.issueDocumentLine)._id,
                            effectiveDate: await fifoIssue.effectiveDate,
                            sequence: await fifoIssue.sequence,
                            remainingQuantity: Number(await fifoIssue.remainingQuantity),
                            amount: Number(await fifoIssue.amount),
                        }),
                    )
                    .toArray();

                assert.deepEqual(fifoIssues.length, 3);

                assert.deepEqual(fifoIssues, expectedResult);
            },
            {
                testActiveServiceOptions: [xtremMasterData.serviceOptions.fifoValuationMethodOption],
            },
        ));
});
describe('FifoValuationIssue', () => {
    it('Create new FIFO Issue - fails', () =>
        Test.withContext(
            async context => {
                const failingTests = [
                    {
                        case: {
                            issueDocumentLine: 1913, // effectiveDate = 2023-03-20
                            effectiveDate: date.make(2023, 3, 21),
                            sequence: 1,
                        },
                        diagnoses: [
                            {
                                severity: 3,
                                path: ['effectiveDate'],
                                message: 'value must not be after 2023-03-20',
                            },
                            {
                                severity: 4,
                                path: ['issuedQuantity'],
                                message: 'FifoValuationIssue.issuedQuantity: property is required',
                            },
                        ],
                    },
                    {
                        case: {
                            issueDocumentLine: 1913,
                            effectiveDate: date.make(2023, 3, 16),
                            sequence: 1,
                            issuedQuantity: -12,
                        },
                        diagnoses: [
                            {
                                severity: 3,
                                path: ['issuedQuantity'],
                                message: 'value must not be negative',
                            },
                            {
                                severity: 3,
                                path: ['remainingQuantity'],
                                message: 'value must not be greater than -12',
                            },
                        ],
                    },
                    {
                        case: {
                            issueDocumentLine: 1913,
                            effectiveDate: date.make(2023, 3, 16),
                            issuedQuantity: 30,
                            remainingQuantity: -5,
                            sequence: 1,
                        },
                        diagnoses: [
                            {
                                severity: 3,
                                path: ['remainingQuantity'],
                                message: 'value must not be negative',
                            },
                        ],
                    },
                ];

                await asyncArray(failingTests).forEach(async (failingTest, index) => {
                    const fifoValuationRecord = await context.create(xtremStockData.nodes.FifoValuationIssue, {
                        issueDocumentLine: failingTest.case.issueDocumentLine,
                        effectiveDate: failingTest.case.effectiveDate,
                        issuedQuantity: failingTest.case.issuedQuantity,
                        remainingQuantity: failingTest.case.remainingQuantity,
                        amount: 0,
                        sequence: failingTest.case.sequence,
                    });
                    await assert.isRejected(
                        fifoValuationRecord.$.save(),
                        'The record was not created.',
                        `test case index=${index}`,
                    );
                    assert.deepEqual(context.diagnoses, failingTest.diagnoses, `test case index=${index}`);
                });
            },
            {
                testActiveServiceOptions: [xtremMasterData.serviceOptions.fifoValuationMethodOption],
                today: '2023-05-01',
            },
        ));
});

describe('Fifo valuation functions', () => {
    it('getFifoValue - fails', () =>
        Test.withContext(
            async context => {
                const item = await context.read(xtremMasterData.nodes.Item, { id: 'STOFIFO' });
                const site = await context.read(xtremSystem.nodes.Site, { id: 'US001' });

                // Try to consume more than the quantity in stock
                await assert.isRejected(
                    xtremStockData.functions.fifoValuationLib.getFifoValue(context, item, site, 10000),
                    'The FIFO stack does not contain the required quantity.\n\nItem: STOFIFO\n\nSite: US001\n\nQuantity required: 10000',
                );
            },
            {
                testActiveServiceOptions: [xtremMasterData.serviceOptions.fifoValuationMethodOption],
            },
        ));

    it('getFifoValue', () =>
        Test.withContext(
            async context => {
                const item = await context.read(xtremMasterData.nodes.Item, { id: 'STOFIFO' });
                const site = await context.read(xtremSystem.nodes.Site, { id: 'US001' });

                const fifoValue = await xtremStockData.functions.fifoValuationLib.getFifoValue(context, item, site, 13);

                assert.deepEqual(Number(fifoValue), 120 * 12 + 100);
            },
            {
                testActiveServiceOptions: [xtremMasterData.serviceOptions.fifoValuationMethodOption],
            },
        ));

    it('getFifoUnitCostAtDate', () =>
        Test.withContext(
            async context => {
                const item = await context.read(xtremMasterData.nodes.Item, { id: 'STOFIFO' });
                const site = await context.read(xtremSystem.nodes.Site, { id: 'US001' });

                const testCases = [
                    { dateOfValuation: date.make(2023, 3, 16), expectedValue: 120 },
                    { dateOfValuation: date.make(2023, 3, 17), expectedValue: 90 },
                    { dateOfValuation: date.make(2023, 3, 18), expectedValue: 101 },
                    { dateOfValuation: date.make(2003, 11, 14), expectedValue: 120 }, // should take the 1st FIFO tier
                    { dateOfValuation: date.make(2100, 1, 1), expectedValue: 130 }, // should take the last FIFO tier
                ];

                await asyncArray(testCases).forEach(async (testCase, index) => {
                    const fifoValue = await xtremStockData.functions.fifoValuationLib.getFifoUnitCostAtDate(
                        context,
                        item,
                        site,
                        testCase.dateOfValuation,
                    );
                    assert.deepEqual(Number(fifoValue), testCase.expectedValue, `test case index=${index}`);
                });
            },
            {
                testActiveServiceOptions: [xtremMasterData.serviceOptions.fifoValuationMethodOption],
            },
        ));
});
