import { Test } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremStockData from '@sage/xtrem-stock-data';
import * as xtremSystem from '@sage/xtrem-system';
import { assert } from 'chai';
import * as xtremStock from '../../../lib';
import * as testLib from '../../fixtures/lib';

describe('Stock Adjustment', () => {
    it('Stock Detail Status - entered', () =>
        Test.withContext(async context => {
            const site = await context.read(xtremSystem.nodes.Site, { id: 'US001' });
            const item = await context.read(xtremMasterData.nodes.Item, { id: 'Chair' });
            const stockSearchData: xtremStockData.interfaces.StockSearchData = {
                item: item._id,
                location: (await context.read(xtremMasterData.nodes.Location, { id: 'LOC3' }))._id,
                lot: null,
                status: (await context.read(xtremStockData.nodes.StockStatus, { id: 'A' }))._id,
                stockUnit: (await item.stockUnit)._id,
                site: site._id,
                owner: await site.id,
            };
            const increasedQuantity = 1;

            const stockRecord = await xtremStockData.functions.stockLib.getStockRecord(context, stockSearchData);

            const adjust = await context.create(xtremStock.nodes.StockAdjustment, {
                stockSite: site,
                reasonCode: 'R1',
                lines: [
                    {
                        item,
                        quantityInStockUnit: increasedQuantity,
                        adjustmentQuantityInStockUnit: increasedQuantity,
                        stockDetails: [
                            testLib.functions.TestHelpers.stockSearchDataToDetailCreateData<xtremStockData.nodes.StockAdjustmentDetail>(
                                stockSearchData,
                                {
                                    quantityInStockUnit: -1,
                                    reasonCode: await context.read(xtremMasterData.nodes.ReasonCode, { id: 'R1' }),
                                    stockRecord,
                                },
                            ),
                        ],
                    },
                ],
            });

            await adjust.$.save();

            const adjustLine = await adjust.lines.elementAt(0);

            const stockDetailStatus = xtremStockData.functions.stockDetailLib.getStockDetailStatus({
                line: adjustLine,
                quantityExpected: await adjustLine.adjustmentQuantityInStockUnit,
                checkSerialNumbers: false,
            });
            assert.equal(await stockDetailStatus, 'entered');
        }));

    it('Stock Detail Status - required', () =>
        Test.withContext(async context => {
            const site = await context.read(xtremSystem.nodes.Site, { id: 'US001' });
            const item = await context.read(xtremMasterData.nodes.Item, { id: 'Chair' });
            const stockSearchData: xtremStockData.interfaces.StockSearchData = {
                item: item._id,
                location: (await context.read(xtremMasterData.nodes.Location, { id: 'LOC3' }))._id,
                lot: null,
                status: (await context.read(xtremStockData.nodes.StockStatus, { id: 'A' }))._id,
                stockUnit: (await item.stockUnit)._id,
                site: site._id,
                owner: await site.id,
            };
            const increasedQuantity = 2;

            const stockRecord = await xtremStockData.functions.stockLib.getStockRecord(context, stockSearchData);

            const adjust = await context.create(xtremStock.nodes.StockAdjustment, {
                stockSite: site,
                reasonCode: 'R1',
                lines: [
                    {
                        item,
                        quantityInStockUnit: 10,
                        adjustmentQuantityInStockUnit: 3,
                        stockDetails: [
                            testLib.functions.TestHelpers.stockSearchDataToDetailCreateData<xtremStockData.nodes.StockAdjustmentDetail>(
                                stockSearchData,
                                {
                                    quantityInStockUnit: increasedQuantity,
                                    reasonCode: await context.read(xtremMasterData.nodes.ReasonCode, { id: 'R1' }),
                                    stockRecord,
                                },
                            ),
                        ],
                    },
                ],
            });

            await adjust.$.save();

            const adjustLine = await adjust.lines.elementAt(0);

            const stockDetailStatus = xtremStockData.functions.stockDetailLib.getStockDetailStatus({
                line: adjustLine,
                quantityExpected: await adjustLine.adjustmentQuantityInStockUnit,
                checkSerialNumbers: false,
            });

            assert.equal(await stockDetailStatus, 'required');
        }));
});
