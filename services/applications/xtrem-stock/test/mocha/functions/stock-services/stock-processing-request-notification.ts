import { date, Logger, Test } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremStockData from '@sage/xtrem-stock-data';
import * as xtremSystem from '@sage/xtrem-system';
import { assert } from 'chai';
import * as xtremStock from '../../../../lib';
import * as testLib from '../../../fixtures/lib';

const logger = new Logger(__filename, 'StockEngine');

describe('Stock movement notifications', () => {
    describe('request to Stock/<MovementType>/start', () => {
        it('should process the stock receipt requested', () =>
            Test.withContext(async context => {
                // Create receipt and all its vital children for the test
                let receipt = await context.create(testLib.nodes.FakeStockReceipt, {
                    number: 'TEST',
                    lines: [
                        {
                            lineNumber: 1,
                            quantityInStockUnit: 20,
                        },
                    ],
                });
                await receipt.$.save();

                let firstReceiptLine = await receipt.lines.elementAt(0);

                const item = await context.read(xtremMasterData.nodes.Item, { id: 'ITME_LOT' });
                const site = await context.read(xtremSystem.nodes.Site, { id: 'US001' });
                const searchData = {
                    item: item._id,
                    location: (
                        await context.read(xtremMasterData.nodes.Location, {
                            locationZone: '#US001|Loading dock',
                            id: 'LOC1',
                        })
                    )._id,
                    lot: null,
                    site: site._id,
                    status: (await context.read(xtremStockData.nodes.StockStatus, { id: 'A' }))._id,
                    stockUnit: (await item.stockUnit)._id,
                    owner: await site.id,
                };

                // store the ids so that we can reference these nodes later in the test
                const receiptID = receipt._id;
                const lineID = firstReceiptLine._id;

                // rollbackCache function will flush deferred actions and saves
                await Test.rollbackCache(context);

                const detail = await context.create(
                    xtremStockData.nodes.StockReceiptDetail,
                    testLib.functions.TestHelpers.stockSearchDataToDetailCreateData(searchData, {
                        effectiveDate: date.today(),
                        quantityInStockUnit: 20,
                        documentLine: lineID,
                    }),
                );
                await detail.$.save();

                const requestMutationOutput = await testLib.nodes.FakeStockReceipt.postToStock(context, [receiptID]);
                assert.isString(requestMutationOutput);
                const notificationID = JSON.parse(requestMutationOutput).notificationId;

                logger.info(`notificationID: ${notificationID}`);
                assert.isNotNull(notificationID);

                receipt = await context.read(testLib.nodes.FakeStockReceipt, { _id: receiptID });

                await receipt.lines.forEach(async line => {
                    const lineId = line._id;
                    const lineSortValue = await line._sortValue;

                    (context as any)._contextValues.notificationId = notificationID;
                    await xtremStock.nodeExtensions.StockExtension.receive(context, {
                        replyTopic: 'intentionallyWrongTopic',
                        stockUpdateParameters: { movementType: 'receipt' },
                        documents: [
                            {
                                id: receipt._id,
                                lines: [
                                    {
                                        id: lineId,
                                        sortValue: lineSortValue,
                                    },
                                ],
                            },
                        ],
                    });

                    await Test.rollbackCache(context);

                    const documentLine = await context.read(testLib.nodes.FakeStockReceiptLine, {
                        _id: lineId,
                    });

                    const transactions = await documentLine.stockTransactions.toArray();

                    assert.isNotEmpty(transactions);
                    assert.equal(
                        await (
                            await documentLine.stockTransactions.elementAt(0)
                        ).notificationId,
                        notificationID,
                    );

                    await testLib.nodes.FakeStockReceipt.onStockReply(context, {
                        requestNotificationId: notificationID,
                        updateResults: {
                            receipt: {
                                documents: [
                                    {
                                        id: (await documentLine.document)._id,
                                        lines: [
                                            {
                                                id: lineId,
                                                sortValue: lineSortValue,
                                                stockUpdateResultStatus: 'created',
                                                originLineIds: [],
                                                stockJournalRecords: [],
                                            },
                                        ],
                                    },
                                ],
                            },
                        },
                    });
                });

                receipt = await context.read(testLib.nodes.FakeStockReceipt, { _id: receiptID });
                firstReceiptLine = await receipt.lines.elementAt(0);

                assert.isNotEmpty(await firstReceiptLine.stockTransactions.toArray());
                assert.equal(await firstReceiptLine.stockTransactionStatus, 'completed');
                assert.equal(
                    await (
                        await firstReceiptLine.stockTransactions.elementAt(0)
                    ).notificationId,
                    notificationID,
                );
            }));
    });
    describe('replies to <Node>/stock/<movementType>/reply', () => {
        it('should update the document line according to the stock update result', () =>
            Test.withContext(async context => {
                let receipt = await context.create(testLib.nodes.FakeStockReceipt, {
                    number: 'TEST',
                    lines: [
                        {
                            lineNumber: 1,
                        },
                    ],
                });

                let firstReceiptLine = await receipt.lines.elementAt(0);

                await receipt.$.save();

                const receiptID = receipt._id;
                const lineID = firstReceiptLine._id;
                const lineSortValue = await firstReceiptLine._sortValue;

                // rollbackCache function will flush deferred actions and saves
                await Test.rollbackCache(context);

                const detail = await context.create(xtremStockData.nodes.StockReceiptDetail, {
                    effectiveDate: date.today(),
                    item: await context.read(xtremMasterData.nodes.Item, { id: 'ITME_LOT' }),
                    location: await context.read(xtremMasterData.nodes.Location, {
                        locationZone: '#US001|Loading dock',
                        id: 'LOC1',
                    }),
                    site: await context.read(xtremSystem.nodes.Site, { id: 'US001' }),
                    status: await context.read(xtremStockData.nodes.StockStatus, { id: 'A' }),
                    quantityInStockUnit: 20,
                    documentLine: lineID,
                });
                await detail.$.save();

                receipt = await context.read(testLib.nodes.FakeStockReceipt, { _id: receiptID });
                firstReceiptLine = await receipt.lines.elementAt(0);

                // hack to make the state of the context mimic the one it should be when triggered by a notification
                const notificationID = `Fake${receiptID}:${date.today()}`;
                (context as any)._contextValues.notificationId = notificationID;

                // Rollback context node cache to be able to transact on children directly, as these actions will be transacted on
                // different contexts in production.
                // WARNING: This method is to only be used in unit tests!
                await Test.rollbackCache(context);

                await xtremStock.nodeExtensions.StockExtension.receive(context, {
                    replyTopic: 'intentionallyWrongTopic',
                    stockUpdateParameters: { movementType: 'receipt' },
                    documents: [
                        {
                            id: receiptID,
                            lines: [
                                {
                                    id: lineID,
                                    sortValue: lineSortValue,
                                },
                            ],
                        },
                    ],
                });

                // simulate the reply notification
                await testLib.nodes.FakeStockReceipt.onStockReply(context, {
                    requestNotificationId: notificationID,
                    updateResults: {
                        receipt: {
                            documents: [
                                {
                                    id: receiptID,
                                    lines: [
                                        {
                                            id: lineID,
                                            sortValue: lineSortValue,
                                            stockUpdateResultStatus: 'increased',
                                            originLineIds: [],
                                            stockJournalRecords: [],
                                        },
                                    ],
                                },
                            ],
                        },
                    },
                });

                const stockTransaction = await context.read(xtremStockData.nodes.StockTransaction, {
                    notificationId: (context as any)._contextValues.notificationId,
                    documentLine: lineID,
                });
                assert.isFalse(await stockTransaction.isInProgress);
                assert.equal(await stockTransaction.status, 'succeeded');
                assert.equal(await stockTransaction.resultAction, 'increased');

                const stockMovements = await context
                    .query(xtremStockData.nodes.StockJournal, {
                        filter: {
                            stockDetail: {
                                documentLine: lineID,
                            },
                        },
                    })
                    .toArray();
                assert.isNotNull(stockMovements);
                assert.lengthOf(stockMovements, 1);
                assert.equal((await stockMovements[0].activeQuantityInStockUnit).valueOf(), 20);
            }));
    });
});
