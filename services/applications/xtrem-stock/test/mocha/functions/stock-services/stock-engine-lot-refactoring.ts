import type { Context, integer } from '@sage/xtrem-core';
import { asyncArray, date, Logger, Test } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremStockData from '@sage/xtrem-stock-data';
import * as xtremSystem from '@sage/xtrem-system';
import { assert } from 'chai';
import * as xtremStock from '../../../../lib';
import * as stockEngine from '../../../../lib/functions/stock-engine';
import * as testLib from '../../../fixtures/lib';

const logger = Logger.getLogger(__filename, 'stock-engine-lot-refactoring-test');

interface ExpectedResults {
    status: xtremStockData.enums.StockUpdateResultAction;
    lotCreated: boolean;
    lotCode: string;
    stockRecord: xtremStockData.nodes.Stock | null;
    stockJournalSearchData: Omit<xtremStock.interfaces.StockJournalKey, '_id'> & { _id?: number };
}

async function assertions(
    context: Context,
    stockDetails: xtremStockData.nodes.StockMovementDetail[],
    stockEngineReturn: xtremStockData.interfaces.StockEngineReturn[],
    expectedResults: ExpectedResults[],
): Promise<void> {
    assert.equal(stockEngineReturn.length, stockDetails.length);

    await asyncArray(expectedResults).forEach(async (expected, index) => {
        logger.info(() => `reading detail ${index}`);

        if (expected.stockRecord && expected.status !== 'deleted') {
            if (expected.lotCode && expected.lotCreated) {
                const lotRecord = await context.tryRead(xtremStockData.nodes.Lot, {
                    id: await (await expected.stockRecord.lot)?.id,
                    item: await expected.stockRecord.item,
                    sublot: await (await expected.stockRecord.lot)?.sublot,
                });

                assert.equal(await (await (await stockDetails[index].stockDetailLot)?.lot)?.id, await lotRecord?.id);
                assert.equal(
                    await (
                        await (
                            await (
                                await stockDetails[index].stockDetailLot
                            )?.lot
                        )?.item
                    )?.id,
                    await (
                        await lotRecord?.item
                    )?.id,
                );
                assert.equal(
                    await (
                        await (
                            await stockDetails[index].stockDetailLot
                        )?.lot
                    )?.sublot,
                    await lotRecord?.sublot,
                );

                await expected.stockRecord.$.set({ lot: lotRecord });
            }

            assert.isNotNull(
                await context.read(xtremStockData.nodes.Stock, {
                    item: (await expected.stockRecord.item)._id,
                    lot: (await expected.stockRecord.lot)?._id || null,
                    stockUnit: (await expected.stockRecord.stockUnit)._id,
                    location: (await expected.stockRecord.location)?._id || null,
                    site: (await expected.stockRecord.site)._id,
                    status: await expected.stockRecord.status,
                    owner: (await expected.stockRecord.owner) || undefined,
                }),
            );

            assert.equal(
                (await expected.stockRecord.quantityInStockUnit)?.valueOf(),
                (await stockEngineReturn[index].stockRecord?.quantityInStockUnit)?.valueOf(),
            );
        } else {
            assert.isNull(expected.stockRecord);
        }

        assert.equal(stockEngineReturn[index].resultAction, expected.status);
        assert.equal(stockEngineReturn[index].lotCreated, expected.lotCreated);
        const stockJournal = await context.read(xtremStockData.nodes.StockJournal, expected.stockJournalSearchData);
        assert.equal(await stockEngineReturn[index].stockJournalRecord?.sequence, await stockJournal?.sequence);
        assert.equal(
            (await stockDetails[index].quantityInStockUnit).valueOf(),
            (await stockEngineReturn[index].stockJournalRecord?.quantityInStockUnit)?.valueOf(),
        );
    });
}

async function prepareReceipt(
    context: Context,
    site: xtremSystem.nodes.Site,
    item: xtremMasterData.nodes.Item,
    locationId: string = 'LOC3',
): Promise<xtremStockData.interfaces.StockSearchData> {
    return {
        item: item._id,
        location: (
            await context.read(xtremMasterData.nodes.Location, { id: locationId, locationZone: '#US001|Loading dock' })
        )._id,
        lot: null,
        status: (await context.read(xtremStockData.nodes.StockStatus, { id: 'A' }))._id,
        stockUnit: (await item.stockUnit)._id,
        site: site._id,
        owner: await site.id,
    };
}

async function prepareResult(
    context: Context,
    stockReceiptDetails: xtremStockData.nodes.StockReceiptDetail[],
    site: xtremSystem.nodes.Site,
    item: xtremMasterData.nodes.Item,
    index: integer,
    status: xtremStockData.enums.StockUpdateResultAction,
    lotCreated: boolean,
    lotId: string,
    sublot: string,
    quantity: number,
    stockUnit: number | xtremMasterData.nodes.UnitOfMeasure,
    sequence: number,
): Promise<ExpectedResults> {
    return {
        status,
        lotCreated,
        lotCode: (await (await (await stockReceiptDetails[index].stockDetailLot)?.lot)?.id) || '',
        stockRecord: await context.create(
            xtremStockData.nodes.Stock,
            {
                location: (await stockReceiptDetails[index].location) || null,
                lot: await context.read(xtremStockData.nodes.Lot, {
                    id: lotId,
                    item,
                    sublot,
                }),
                status: (await stockReceiptDetails[index].status)!,
                quantityInStockUnit: quantity,
                activeQuantityInStockUnit: await stockReceiptDetails[index].quantityInStockUnit,
                stockUnit,
                owner: await stockReceiptDetails[index].owner,
                site,
                item,
            },
            { isTransient: true },
        ),
        stockJournalSearchData: {
            effectiveDate: await stockReceiptDetails[index].effectiveDate,
            item,
            site,
            sequence,
        },
    };
}

describe('Stock engine lot refactoring', () => {
    it('should create 1 stock and 1 lot record for a stock receipt with 1 line and 1 detail', () =>
        Test.withContext(
            async context => {
                const site = await context.read(xtremSystem.nodes.Site, { id: 'US001' });
                const item = await context.read(xtremMasterData.nodes.Item, { id: 'Muesli' });
                const stockSearchData = prepareReceipt(context, site, item);
                const receipt = await context.create(xtremStock.nodes.StockReceipt, {
                    number: 'TESTLOT',
                    effectiveDate: date.today(),
                    stockSite: site,
                    lines: [
                        {
                            _action: 'create',
                            item: (await stockSearchData).item,
                            stockStatus: (await stockSearchData).status,
                            quantityInStockUnit: 2,
                            stockDetails: [
                                testLib.functions.TestHelpers.stockSearchDataToDetailCreateData(await stockSearchData, {
                                    quantityInStockUnit: 2,
                                    movementType: 'receipt',
                                    stockDetailLot: {
                                        lotNumber: 'LOT001',
                                        supplierLot: 'SLOT001',
                                        expirationDate: date.today(),
                                        sublot: '001',
                                    },
                                }),
                            ],
                        },
                    ],
                });

                await receipt.$.save();
                const stockReceiptDetails = await (await receipt.lines.elementAt(0)).stockDetails.toArray();
                await assertions(
                    context,
                    stockReceiptDetails,
                    await stockEngine.stockReceipt(context, stockReceiptDetails, { movementType: 'receipt' }),
                    [
                        await prepareResult(
                            context,
                            stockReceiptDetails,
                            site,
                            item,
                            0,
                            'created',
                            true,
                            'LOT001',
                            '001',
                            await stockReceiptDetails[0].quantityInStockUnit,
                            (await stockSearchData).stockUnit,
                            99999,
                        ),
                    ],
                );
            },
            { today: '2020-07-12' },
        ));

    it('should create 1 stock and 1 lot record for a stock receipt with 1 line and 2 details (same lot)', () =>
        Test.withContext(async context => {
            const site = await context.read(xtremSystem.nodes.Site, { id: 'US001' });
            const item = await context.read(xtremMasterData.nodes.Item, { id: 'Muesli' });
            const lastStockJournalId = await xtremStock.functions.stockJournalLib.getLastSequenceNumberStockJournal(
                context,
                {
                    site,
                    item,
                    effectiveDate: date.today(),
                },
            );
            const stockSearchData = prepareReceipt(context, site, item);
            const receipt = await context.create(xtremStock.nodes.StockReceipt, {
                number: 'TESTLOT',
                effectiveDate: date.today(),
                stockSite: site,
                lines: [
                    {
                        _action: 'create',
                        item: (await stockSearchData).item,
                        stockStatus: (await stockSearchData).status,
                        quantityInStockUnit: 2,
                        stockDetails: [
                            testLib.functions.TestHelpers.stockSearchDataToDetailCreateData(await stockSearchData, {
                                quantityInStockUnit: 2,
                                movementType: 'receipt',
                                stockDetailLot: {
                                    _id: -1,
                                    lotNumber: 'LOT001',
                                    supplierLot: 'SLOT001',
                                    sublot: '001',
                                    expirationDate: date.make(2023, 2, 28),
                                },
                            }),
                            testLib.functions.TestHelpers.stockSearchDataToDetailCreateData(await stockSearchData, {
                                quantityInStockUnit: 2,
                                movementType: 'receipt',
                                stockDetailLot: {
                                    _id: -2,
                                    lotNumber: 'LOT001',
                                    supplierLot: 'SLOT001',
                                    sublot: '001',
                                    expirationDate: date.make(2023, 2, 28),
                                    stockDetailLot: -1,
                                },
                            }),
                        ],
                    },
                ],
            });

            await receipt.$.save();
            const stockReceiptDetails = await (await receipt.lines.elementAt(0)).stockDetails.toArray();

            await assertions(
                context,
                stockReceiptDetails,
                await stockEngine.stockReceipt(context, stockReceiptDetails, { movementType: 'receipt' }),
                [
                    await prepareResult(
                        context,
                        stockReceiptDetails,
                        site,
                        item,
                        0,
                        'created',
                        true,
                        'LOT001',
                        '001',
                        await stockReceiptDetails[0].quantityInStockUnit,
                        (await stockSearchData).stockUnit,
                        lastStockJournalId - 1,
                    ),
                    await prepareResult(
                        context,
                        stockReceiptDetails,
                        site,
                        item,
                        1,
                        'increased',
                        false,
                        'LOT001',
                        '001',
                        (await stockReceiptDetails[0].quantityInStockUnit) +
                            (await stockReceiptDetails[1].quantityInStockUnit),
                        (await stockSearchData).stockUnit,
                        lastStockJournalId - 2,
                    ),
                ],
            );
        }));

    it('should create 2 stock and 2 lot records for a stock receipt with 1 line and 2 details', () =>
        Test.withContext(async context => {
            const site = await context.read(xtremSystem.nodes.Site, { id: 'US001' });
            const item = await context.read(xtremMasterData.nodes.Item, { id: 'Muesli' });
            const lastStockJournalId = await xtremStock.functions.stockJournalLib.getLastSequenceNumberStockJournal(
                context,
                {
                    site,
                    item,
                    effectiveDate: date.today(),
                },
            );
            const stockSearchData = prepareReceipt(context, site, item);
            const receipt = await context.create(xtremStock.nodes.StockReceipt, {
                number: 'TESTLOT',
                effectiveDate: date.today(),
                stockSite: site,
                lines: [
                    {
                        _action: 'create',
                        item: (await stockSearchData).item,
                        stockStatus: (await stockSearchData).status,
                        quantityInStockUnit: 2,
                        stockDetails: [
                            testLib.functions.TestHelpers.stockSearchDataToDetailCreateData(await stockSearchData, {
                                quantityInStockUnit: 2,
                                movementType: 'receipt',
                                stockDetailLot: {
                                    _id: -1,
                                    lotNumber: 'LOT001',
                                    supplierLot: 'SLOT001',
                                    sublot: '001',
                                    expirationDate: date.make(2023, 2, 28),
                                },
                            }),
                            testLib.functions.TestHelpers.stockSearchDataToDetailCreateData(await stockSearchData, {
                                quantityInStockUnit: 2,
                                movementType: 'receipt',
                                stockDetailLot: {
                                    _id: -2,
                                    lotNumber: 'LOT002',
                                    supplierLot: 'SLOT002',
                                    sublot: '002',
                                    expirationDate: date.make(2023, 2, 28),
                                },
                            }),
                        ],
                    },
                ],
            });

            await receipt.$.save();
            const stockReceiptDetails = await (await receipt.lines.elementAt(0)).stockDetails.toArray();

            await assertions(
                context,
                stockReceiptDetails,
                await stockEngine.stockReceipt(context, stockReceiptDetails, { movementType: 'receipt' }),
                [
                    await prepareResult(
                        context,
                        stockReceiptDetails,
                        site,
                        item,
                        0,
                        'created',
                        true,
                        'LOT001',
                        '001',
                        await stockReceiptDetails[0].quantityInStockUnit,
                        (await stockSearchData).stockUnit,
                        lastStockJournalId - 1,
                    ),
                    await prepareResult(
                        context,
                        stockReceiptDetails,
                        site,
                        item,
                        1,
                        'created',
                        true,
                        'LOT002',
                        '002',
                        await stockReceiptDetails[1].quantityInStockUnit,
                        (await stockSearchData).stockUnit,
                        lastStockJournalId - 2,
                    ),
                ],
            );
        }));

    it('should create 2 stock and 2 lot records for a stock receipt with 1 line and 3 details (2 with same lot)', () =>
        Test.withContext(async context => {
            const site = await context.read(xtremSystem.nodes.Site, { id: 'US001' });
            const item = await context.read(xtremMasterData.nodes.Item, { id: 'Muesli' });
            const lastStockJournalId = await xtremStock.functions.stockJournalLib.getLastSequenceNumberStockJournal(
                context,
                {
                    site,
                    item,
                    effectiveDate: date.today(),
                },
            );

            const stockSearchData = prepareReceipt(context, site, item);
            const receipt = await context.create(xtremStock.nodes.StockReceipt, {
                number: 'TESTLOT',
                effectiveDate: date.today(),
                stockSite: site,
                lines: [
                    {
                        _action: 'create',
                        item,
                        stockStatus: (await stockSearchData).status,
                        quantityInStockUnit: 2,
                        stockDetails: [
                            testLib.functions.TestHelpers.stockSearchDataToDetailCreateData(await stockSearchData, {
                                quantityInStockUnit: 2,
                                movementType: 'receipt',
                                stockDetailLot: {
                                    _id: -1,
                                    lotNumber: 'LOT001',
                                    supplierLot: 'SLOT001',
                                    sublot: '001',
                                    expirationDate: date.make(2023, 2, 28),
                                },
                            }),
                            testLib.functions.TestHelpers.stockSearchDataToDetailCreateData(await stockSearchData, {
                                quantityInStockUnit: 2,
                                movementType: 'receipt',
                                stockDetailLot: {
                                    _id: -2,
                                    lotNumber: 'LOT002',
                                    supplierLot: 'SLOT002',
                                    sublot: '002',
                                    expirationDate: date.make(2023, 2, 28),
                                },
                            }),
                            testLib.functions.TestHelpers.stockSearchDataToDetailCreateData(await stockSearchData, {
                                quantityInStockUnit: 2,
                                movementType: 'receipt',
                                stockDetailLot: {
                                    _id: -3,
                                    lotNumber: 'LOT002',
                                    supplierLot: 'SLOT002',
                                    sublot: '002',
                                    expirationDate: date.make(2023, 2, 28),
                                    stockDetailLot: -2,
                                },
                            }),
                        ],
                    },
                ],
            });

            await receipt.$.save();
            const stockReceiptDetails = await (await receipt.lines.elementAt(0)).stockDetails.toArray();

            await assertions(
                context,
                stockReceiptDetails,
                await stockEngine.stockReceipt(context, stockReceiptDetails, { movementType: 'receipt' }),
                [
                    await prepareResult(
                        context,
                        stockReceiptDetails,
                        site,
                        item,
                        0,
                        'created',
                        true,
                        'LOT001',
                        '001',
                        await stockReceiptDetails[0].quantityInStockUnit,
                        (await stockSearchData).stockUnit,
                        lastStockJournalId - 1,
                    ),
                    await prepareResult(
                        context,
                        stockReceiptDetails,
                        site,
                        item,
                        1,
                        'created',
                        true,
                        'LOT002',
                        '002',
                        await stockReceiptDetails[1].quantityInStockUnit,
                        (await stockSearchData).stockUnit,
                        lastStockJournalId - 2,
                    ),
                    await prepareResult(
                        context,
                        stockReceiptDetails,
                        site,
                        item,
                        2,
                        'increased',
                        false,
                        'LOT002',
                        '002',
                        (await stockReceiptDetails[1].quantityInStockUnit) +
                            (await stockReceiptDetails[2].quantityInStockUnit),
                        (await stockSearchData).stockUnit,
                        lastStockJournalId - 3,
                    ),
                ],
            );
        }));

    it('should increase the stock for a stock receipt with 1 line and one detail linked to an existing lot', () =>
        Test.withContext(
            async context => {
                const site = await context.read(xtremSystem.nodes.Site, { id: 'US001' });
                const item = await context.read(xtremMasterData.nodes.Item, { id: 'ITEM_LOT' });
                const stockSearchData = prepareReceipt(context, site, item, 'LOC1');
                const receipt = await context.create(xtremStock.nodes.StockReceipt, {
                    number: 'TESTLOT',
                    effectiveDate: date.today(),
                    stockSite: site,
                    lines: [
                        {
                            _action: 'create',
                            item: (await stockSearchData).item,
                            stockStatus: (await stockSearchData).status,
                            quantityInStockUnit: 2,
                            stockDetails: [
                                testLib.functions.TestHelpers.stockSearchDataToDetailCreateData(await stockSearchData, {
                                    quantityInStockUnit: 2,
                                    movementType: 'receipt',
                                    stockDetailLot: {
                                        lot: await context.read(xtremStockData.nodes.Lot, {
                                            id: 'LOT00001',
                                            sublot: '',
                                            item,
                                        }),
                                    },
                                }),
                            ],
                        },
                    ],
                });

                await receipt.$.save();
                const stockReceiptDetails = await (await receipt.lines.elementAt(0)).stockDetails.toArray();
                await assertions(
                    context,
                    stockReceiptDetails,
                    await stockEngine.stockReceipt(context, stockReceiptDetails, { movementType: 'receipt' }),
                    [
                        await prepareResult(
                            context,
                            stockReceiptDetails,
                            site,
                            item,
                            0,
                            'increased',
                            false,
                            'LOT00001',
                            '',
                            1000 + (await stockReceiptDetails[0].quantityInStockUnit),
                            (await stockSearchData).stockUnit,
                            99999,
                        ),
                    ],
                );
            },
            { today: '2020-07-12' },
        ));

    it('should refuse the posting of a stock receipt if lot information is passed for an item not managed by lot', () =>
        Test.withContext(
            async context => {
                const site = await context.read(xtremSystem.nodes.Site, { id: 'US001' });
                const item = await context.read(xtremMasterData.nodes.Item, { id: 'Chair' });
                const stockSearchData = prepareReceipt(context, site, item);
                const receipt = await context.create(xtremStock.nodes.StockReceipt, {
                    number: 'TESTLOT',
                    effectiveDate: date.today(),
                    stockSite: site,
                    lines: [
                        {
                            _action: 'create',
                            item: (await stockSearchData).item,
                            stockStatus: (await stockSearchData).status,
                            quantityInStockUnit: 2,
                            stockDetails: [
                                testLib.functions.TestHelpers.stockSearchDataToDetailCreateData(await stockSearchData, {
                                    quantityInStockUnit: 2,
                                    movementType: 'receipt',
                                    stockDetailLot: {
                                        _id: -1,
                                        lotNumber: 'LOT001',
                                        supplierLot: 'SLOT001',
                                        sublot: '001',
                                        expirationDate: date.make(2023, 2, 28),
                                    },
                                }),
                            ],
                        },
                    ],
                });

                await receipt.$.save();

                const stockReceiptDetails = await (await receipt.lines.elementAt(0)).stockDetails.toArray();
                await assert.isRejected(
                    stockEngine.stockReceipt(context, stockReceiptDetails, { movementType: 'receipt' }),

                    'The item Chair is not lot-managed. You can only post a stock detail with lot information for an item that is lot-managed.',
                );
            },
            { today: '2020-07-12' },
        ));

    it('should create 1 stock and 1 lot record for a stock receipt with 2 lines and 2 stock details (same item, same lot, same sub-lot)', () =>
        Test.withContext(async context => {
            const site = await context.read(xtremSystem.nodes.Site, { id: 'US001' });
            const item = await context.read(xtremMasterData.nodes.Item, { id: 'Muesli' });
            const lastStockJournalId = await xtremStock.functions.stockJournalLib.getLastSequenceNumberStockJournal(
                context,
                {
                    site,
                    item,
                    effectiveDate: date.today(),
                },
            );
            const stockSearchData = prepareReceipt(context, site, item);
            const receipt = await context.create(xtremStock.nodes.StockReceipt, {
                number: 'TESTLOT',
                effectiveDate: date.today(),
                stockSite: site,
                lines: [
                    {
                        _action: 'create',
                        item: (await stockSearchData).item,
                        stockStatus: (await stockSearchData).status,
                        quantityInStockUnit: 2,
                        stockDetails: [
                            testLib.functions.TestHelpers.stockSearchDataToDetailCreateData(await stockSearchData, {
                                quantityInStockUnit: 2,
                                movementType: 'receipt',
                                stockDetailLot: {
                                    _id: -1,
                                    lotNumber: 'LOT001',
                                    supplierLot: 'SLOT001',
                                    sublot: '001',
                                },
                            }),
                        ],
                    },
                    {
                        _action: 'create',
                        item: (await stockSearchData).item,
                        stockStatus: (await stockSearchData).status,
                        quantityInStockUnit: 2,
                        stockDetails: [
                            testLib.functions.TestHelpers.stockSearchDataToDetailCreateData(await stockSearchData, {
                                quantityInStockUnit: 2,
                                movementType: 'receipt',
                                stockDetailLot: {
                                    _id: -2,
                                    lotNumber: 'LOT001',
                                    supplierLot: 'SLOT001',
                                    sublot: '001',
                                },
                            }),
                        ],
                    },
                ],
            });

            await receipt.$.save();
            const stockReceiptDetails0 = await (await receipt.lines.elementAt(0)).stockDetails.toArray();
            const stockReceiptDetails1 = await (await receipt.lines.elementAt(1)).stockDetails.toArray();

            const stockReceiptDetailsMerged = [...stockReceiptDetails0, ...stockReceiptDetails1];

            await assertions(
                context,
                stockReceiptDetailsMerged,
                await stockEngine.stockReceipt(context, stockReceiptDetailsMerged, { movementType: 'receipt' }),
                [
                    await prepareResult(
                        context,
                        stockReceiptDetailsMerged,
                        site,
                        item,
                        0,
                        'created',
                        true,
                        'LOT001',
                        '001',
                        await stockReceiptDetailsMerged[0].quantityInStockUnit,
                        (await stockSearchData).stockUnit,
                        lastStockJournalId - 1,
                    ),
                    await prepareResult(
                        context,
                        stockReceiptDetailsMerged,
                        site,
                        item,
                        1,
                        'increased',
                        true,
                        'LOT001',
                        '001',
                        (await stockReceiptDetailsMerged[0].quantityInStockUnit) +
                            (await stockReceiptDetailsMerged[1].quantityInStockUnit),
                        (await stockSearchData).stockUnit,
                        lastStockJournalId - 2,
                    ),
                ],
            );
        }));

    it('should refuse the posting of a stock receipt with 2 lines and one without stock detail lot info', () =>
        Test.withContext(async context => {
            const site = await context.read(xtremSystem.nodes.Site, { id: 'US001' });
            const item = await context.read(xtremMasterData.nodes.Item, { id: 'Muesli' });
            const stockSearchData = prepareReceipt(context, site, item);
            const receipt = await context.create(xtremStock.nodes.StockReceipt, {
                number: 'TESTLOT',
                effectiveDate: date.today(),
                stockSite: site,
                lines: [
                    {
                        _action: 'create',
                        item: (await stockSearchData).item,
                        stockStatus: (await stockSearchData).status,
                        quantityInStockUnit: 2,
                        stockDetails: [
                            testLib.functions.TestHelpers.stockSearchDataToDetailCreateData(await stockSearchData, {
                                quantityInStockUnit: 2,
                                movementType: 'receipt',
                                stockDetailLot: {
                                    _id: -1,
                                    lotNumber: 'LOT001',
                                    supplierLot: 'SLOT001',
                                    sublot: '001',
                                },
                            }),
                        ],
                    },
                    {
                        _action: 'create',
                        item: (await stockSearchData).item,
                        stockStatus: (await stockSearchData).status,
                        quantityInStockUnit: 2,
                        stockDetails: [
                            testLib.functions.TestHelpers.stockSearchDataToDetailCreateData(await stockSearchData, {
                                quantityInStockUnit: 2,
                                movementType: 'receipt',
                            }),
                        ],
                    },
                ],
            });

            await receipt.$.save();
            const stockReceiptDetails0 = await (await receipt.lines.elementAt(0)).stockDetails.toArray();
            const stockReceiptDetails1 = await (await receipt.lines.elementAt(1)).stockDetails.toArray();

            const stockReceiptDetailsMerged = [...stockReceiptDetails0, ...stockReceiptDetails1];

            await assert.isRejected(
                stockEngine.stockReceipt(context, stockReceiptDetailsMerged, { movementType: 'receipt' }),
                'No lot information received.',
            );
        }));
});
