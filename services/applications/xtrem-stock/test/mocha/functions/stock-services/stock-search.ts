import { asyncArray, Test } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremStockData from '@sage/xtrem-stock-data';
import * as xtremSystem from '@sage/xtrem-system';
import { assert } from 'chai';
import * as xtremStock from '../../../../lib';
import * as fakePackage from '../../../fixtures/lib';

async function assertions(
    results: xtremStockData.interfaces.StockSearchResult[],
    expectations: {
        quantity: number;
        item: xtremMasterData.nodes.Item;
        site: xtremSystem.nodes.Site;
        lot?: {
            id: string;
            sublot: string;
            supplierLot: string;
            creationDate: string;
            expirationDate: string;
        };
        status?: xtremStockData.nodes.StockStatus;
    }[],
    searchedQuantity: number,
): Promise<void> {
    assert.equal(
        results.length,
        expectations.length,
        'the amount of stock details produced by the stock engine is not as expected',
    );

    if (searchedQuantity !== 0) {
        assert.equal(
            results
                .map(element => element.quantityInStockUnit)
                .reduce((stockDetail, accumulator) => {
                    return accumulator + stockDetail;
                })
                .valueOf(),
            -searchedQuantity,
            "the search engine hasn't found the searched quantity",
        );
    }

    await asyncArray(expectations).forEach(async (expected, index) => {
        const stockDetailQuantity = results[index].quantityInStockUnit;
        assert.equal(stockDetailQuantity, expected.quantity);
        // not working thanks to great work done in the platform preventing us to use number as we should be able to...
        // assert.isAtMost(+stockDetailQuantity, +searchedQuantity);
        if (searchedQuantity !== 0) {
            assert.isFalse(
                -stockDetailQuantity > searchedQuantity,
                'Stock detail quantity computed is greater than the requested quantity',
            );
        }
        assert.equal(await (await results[index].stockRecord?.item)?.id, await expectations[index].item.id);
        assert.equal(await (await results[index].stockRecord?.site)?.id, await expectations[index].site.id);

        if (expected.lot) {
            assert.equal(await (await results[index].stockRecord?.lot)?.id, expected.lot.id);
            assert.equal(await (await results[index].stockRecord?.lot)?.sublot, expected.lot.sublot);
            assert.equal(await (await (await results[index].stockRecord?.lot)?.item)?.id, await expected.item.id);
            assert.equal(await (await results[index].stockRecord?.lot)?.supplierLot, expected.lot.sublot);
            assert.equal(
                (await (await results[index].stockRecord?.lot)?.creationDate)?.valueOf(),
                expected.lot.creationDate,
            );
            assert.equal(
                (await (await results[index].stockRecord?.lot)?.expirationDate)?.valueOf(),
                expected.lot.expirationDate,
            );
        }

        if (expected.status) {
            assert.equal(await (await results[index].stockRecord?.status)?.name, await expected.status.name);
        }
    });
}

describe('StockSearch', () => {
    it('should find the searched active quantity spread between severals stock records', () =>
        Test.withContext(
            async context => {
                const item = await context.read(xtremMasterData.nodes.Item, { id: 'ITME_LOT' });
                const site = await context.read(xtremSystem.nodes.Site, { id: 'US001' });
                const quantity = 2500;

                await assertions(
                    await xtremStock.functions.stockEngine.searchStock(context, {
                        activeQuantityInStockUnit: quantity,
                        item,
                        site,
                    }),
                    [
                        {
                            item,
                            site,
                            quantity: -1000,
                        },
                        {
                            item,
                            site,
                            quantity: -1000,
                        },
                        {
                            item,
                            site,
                            quantity: -500,
                        },
                    ],
                    quantity,
                );
            },
            { today: '2020-07-12' },
        ));

    it('should find the searched active quantity on one stock record', () =>
        Test.withContext(
            async context => {
                const item = await context.read(xtremMasterData.nodes.Item, { id: 'LOT_TEST1' });
                const site = await context.read(xtremSystem.nodes.Site, { id: 'US001' });
                const quantity = 80;

                await assertions(
                    await xtremStock.functions.stockEngine.searchStock(context, {
                        activeQuantityInStockUnit: quantity,
                        item,
                        site,
                    }),
                    [
                        {
                            item,
                            site,
                            quantity: -80,
                        },
                    ],
                    quantity,
                );
            },
            { today: '2020-07-12' },
        ));

    it("shouldn't find the searched active quantity when it's not existing", () =>
        Test.withContext(
            async context => {
                const item = await context.read(xtremMasterData.nodes.Item, { id: 'LOT_TEST1' });
                const site = await context.read(xtremSystem.nodes.Site, { id: 'US001' });
                const quantity = 2000;
                const errorMatch =
                    /^Not enough stock on Site: US001 Item: LOT_TEST1.\nThe stock should not be in transit.$/;

                await xtremStock.functions.stockEngine.searchStock(context, {
                    activeQuantityInStockUnit: quantity,
                    item,
                    site,
                });

                assert.match(context.diagnoses[0].message, errorMatch);
            },
            { today: '2020-07-12' },
        ));

    it("shouldn't find the searched active quantity when there's not any stock record for the specified item and site", () =>
        Test.withContext(
            async context => {
                const item = await context.read(xtremMasterData.nodes.Item, { id: 'SIMPLE_TEST2' });
                const site = await context.read(xtremSystem.nodes.Site, { id: 'US001' });
                const quantity = 50;
                const errorMatch =
                    /^No stock available on Site: US001 Item: SIMPLE_TEST2.\nThe stock should not be in transit.$/;

                await xtremStock.functions.stockEngine.searchStock(context, {
                    activeQuantityInStockUnit: quantity,
                    item,
                    site,
                });

                assert.match(context.diagnoses[0].message, errorMatch);
            },
            { today: '2020-07-12' },
        ));

    it('should find the searched skip expired lot', () =>
        Test.withContext(
            async context => {
                const item = await context.read(xtremMasterData.nodes.Item, { id: '17890' });
                const site = await context.read(xtremSystem.nodes.Site, { id: 'US001' });
                const quantity = 80;

                await assertions(
                    await xtremStock.functions.stockEngine.searchStock(context, {
                        activeQuantityInStockUnit: quantity,
                        item,
                        site,
                    }),
                    [
                        {
                            item,
                            site,
                            quantity: -80,
                            lot: {
                                id: 'MISCRECEIPT3',
                                sublot: '',
                                supplierLot: '',
                                creationDate: '2020-06-16',
                                expirationDate: '2020-07-16',
                            },
                        },
                    ],
                    quantity,
                );
            },
            { today: '2020-07-12' },
        ));

    it('should find the needed quantity according to the lot creation date order', () =>
        Test.withContext(
            async context => {
                const item = await context.read(xtremMasterData.nodes.Item, { id: '17890' });
                const site = await context.read(xtremSystem.nodes.Site, { id: 'US001' });
                const quantity = 250;

                await assertions(
                    await xtremStock.functions.stockEngine.searchStock(context, {
                        activeQuantityInStockUnit: quantity,
                        item,
                        site,
                    }),
                    [
                        {
                            item,
                            site,
                            quantity: -100,
                            lot: {
                                id: 'MISCRECEIPT3',
                                sublot: '',
                                supplierLot: '',
                                creationDate: '2020-06-16',
                                expirationDate: '2020-07-16',
                            },
                        },
                        {
                            item,
                            site,
                            quantity: -100,
                            lot: {
                                id: 'MISCRECEIPT4',
                                sublot: '',
                                supplierLot: '',
                                creationDate: '2020-06-17',
                                expirationDate: '2020-07-17',
                            },
                        },
                        {
                            item,
                            site,
                            quantity: -50,
                            lot: {
                                id: 'MISCRECEIPT4',
                                sublot: '',
                                supplierLot: '',
                                creationDate: '2020-06-17',
                                expirationDate: '2020-07-17',
                            },
                        },
                    ],
                    quantity,
                );
            },
            { today: '2020-07-12' },
        ));

    it('should find the needed quantity according to the allocated quantity', () =>
        Test.withContext(
            async context => {
                const item = await context.read(xtremMasterData.nodes.Item, { id: 'SamA20' });
                const site = await context.read(xtremSystem.nodes.Site, { id: 'US001' });
                const quantity = 4;

                await assertions(
                    await xtremStock.functions.stockEngine.searchStock(context, {
                        activeQuantityInStockUnit: quantity,
                        item,
                        site,
                    }),
                    [
                        {
                            item,
                            site,
                            quantity: -4,
                        },
                    ],
                    quantity,
                );
            },
            { today: '2020-07-12' },
        ));

    it('should find the some stock from the A stockStatus', () =>
        Test.withContext(
            async context => {
                const item = await context.read(xtremMasterData.nodes.Item, { id: 'Chemical D' });
                const site = await context.read(xtremSystem.nodes.Site, { id: 'US001' });
                const status = await context.read(xtremStockData.nodes.StockStatus, { id: 'A' });

                await assertions(
                    await xtremStock.functions.stockEngine.searchStock(context, {
                        activeQuantityInStockUnit: 0,
                        item,
                        site,
                        stockCharacteristics: [
                            {
                                statusList: [
                                    { status: await context.read(xtremStockData.nodes.StockStatus, { id: 'A' }) },
                                ],
                            },
                        ],
                    }),
                    [
                        {
                            item,
                            site,
                            quantity: 4989,
                            status,
                        },
                    ],
                    0,
                );
            },
            { today: '2021-03-31' },
        ));

    it('should find the some stock from the accepted stockStatusType', () =>
        Test.withContext(
            async context => {
                const item = await context.read(xtremMasterData.nodes.Item, { id: 'Chemical D' });
                const site = await context.read(xtremSystem.nodes.Site, { id: 'US001' });
                const status1 = await context.read(xtremStockData.nodes.StockStatus, { id: 'A' });
                const status2 = await context.read(xtremStockData.nodes.StockStatus, { id: 'A1' });

                await assertions(
                    await xtremStock.functions.stockEngine.searchStock(context, {
                        activeQuantityInStockUnit: 0,
                        item,
                        site,
                        stockCharacteristics: [{ statusList: [{ statusType: 'accepted' }] }],
                    }),
                    [
                        {
                            item,
                            site,
                            quantity: 4989,
                            status: status1,
                        },
                        {
                            item,
                            site,
                            quantity: 5000,
                            status: status2,
                        },
                    ],
                    0,
                );
            },
            { today: '2021-03-31' },
        ));

    it('should find the some stock from the A and A1 stockStatus', () =>
        Test.withContext(
            async context => {
                const item = await context.read(xtremMasterData.nodes.Item, { id: 'Chemical D' });
                const site = await context.read(xtremSystem.nodes.Site, { id: 'US001' });
                const status1 = await context.read(xtremStockData.nodes.StockStatus, { id: 'A' });
                const status2 = await context.read(xtremStockData.nodes.StockStatus, { id: 'A1' });

                await assertions(
                    await xtremStock.functions.stockEngine.searchStock(context, {
                        activeQuantityInStockUnit: 0,
                        item,
                        site,
                        stockCharacteristics: [
                            {
                                statusList: [
                                    { status: await context.read(xtremStockData.nodes.StockStatus, { id: 'A' }) },
                                    { status: await context.read(xtremStockData.nodes.StockStatus, { id: 'A1' }) },
                                ],
                            },
                        ],
                    }),
                    [
                        {
                            item,
                            site,
                            quantity: 4989,
                            status: status1,
                        },
                        {
                            item,
                            site,
                            quantity: 5000,
                            status: status2,
                        },
                    ],
                    0,
                );
            },
            { today: '2021-03-31' },
        ));

    it('should not find any stock from the stockStatus Q', () =>
        Test.withContext(
            async context => {
                const item = await context.read(xtremMasterData.nodes.Item, { id: 'Chemical D' });
                const site = await context.read(xtremSystem.nodes.Site, { id: 'US001' });
                const errorMatch =
                    /^No stock available on Site: US001 Item: Chemical D.\nStatuses searched in stock: Q.\nThe expiration date should be greater than or equal to 2021-03-31.\nThe stock should not be in transit.$/;
                await xtremStock.functions.stockEngine.searchStock(context, {
                    activeQuantityInStockUnit: 0,
                    item,
                    site,
                    stockCharacteristics: [
                        { statusList: [{ status: await context.read(xtremStockData.nodes.StockStatus, { id: 'Q' }) }] },
                    ],
                });

                assert.match(context.diagnoses[0].message, errorMatch);
            },
            { today: '2021-03-31' },
        ));

    it('should not find any stock from the stockStatusType rejected', () =>
        Test.withContext(
            async context => {
                const item = await context.read(xtremMasterData.nodes.Item, { id: 'Chemical D' });
                const site = await context.read(xtremSystem.nodes.Site, { id: 'US001' });
                const errorMatch =
                    /^No stock available on Site: US001 Item: Chemical D.\nStatus types searched in stock: Rejected.\nThe expiration date should be greater than or equal to 2021-03-31.\nThe stock should not be in transit.$/;

                await xtremStock.functions.stockEngine.searchStock(context, {
                    activeQuantityInStockUnit: 0,
                    item,
                    site,
                    stockCharacteristics: [{ statusList: [{ statusType: 'rejected' }] }],
                });

                assert.match(context.diagnoses[0].message, errorMatch);
            },
            { today: '2021-03-31' },
        ));

    it('should not find any stock from a list of mixed statuses', () =>
        Test.withContext(
            async context => {
                const item = await context.read(xtremMasterData.nodes.Item, { id: 'ChairSeat' });
                const site = await context.read(xtremSystem.nodes.Site, { id: 'US005' });
                const errorMatch =
                    /^No stock available on Site: US005 Item: ChairSeat.\nStatus types searched in stock: Rejected, Quality control.\nStatuses searched in stock: R, Q.\nThe stock should not be in transit.$/;

                await xtremStock.functions.stockEngine.searchStock(context, {
                    activeQuantityInStockUnit: 0,
                    item,
                    site,
                    stockCharacteristics: [
                        {
                            statusList: [
                                { statusType: 'rejected' },
                                { status: await context.read(xtremStockData.nodes.StockStatus, { id: 'R' }) },
                                { statusType: 'qualityControl' },
                                { status: await context.read(xtremStockData.nodes.StockStatus, { id: 'Q' }) },
                            ],
                        },
                    ],
                });

                assert.match(context.diagnoses[0].message, errorMatch);
            },
            { today: '2021-03-31' },
        ));

    it('should not find any stock if no quantity available', () =>
        Test.withContext(
            async context => {
                const item = await context.read(xtremMasterData.nodes.Item, { id: 'STOCKSEARCH' });
                const site = await context.read(xtremSystem.nodes.Site, { id: 'ETS1-S01' });

                // Create a document with 3 lines of 30 STOCKSEARCH item each
                const document = await fakePackage.functions.TestHelpers.createFakeIssueDocument(
                    context,
                    'FakeIssueDoc01',
                    3,
                    item,
                    30,
                );

                // Fully allocate the 1st line
                const stockRecord1 = await context.read(xtremStockData.nodes.Stock, { _id: 101 });
                const allocation1 = await context.create(xtremStockData.nodes.StockAllocation, {
                    stockRecord: stockRecord1,
                    documentLine: await document.lines.elementAt(0),
                    quantityInStockUnit: 30,
                });
                await allocation1.$.save();

                // Fully allocate the 2nd line
                const stockRecord2 = await context.read(xtremStockData.nodes.Stock, { _id: 102 });
                const allocation2 = await context.create(xtremStockData.nodes.StockAllocation, {
                    stockRecord: stockRecord2,
                    documentLine: await document.lines.elementAt(1),
                    quantityInStockUnit: 30,
                });
                await allocation2.$.save();

                // Partially allocate the 3rd line
                const stockRecord3 = await context.read(xtremStockData.nodes.Stock, { _id: 103 });
                const allocation3 = await context.create(xtremStockData.nodes.StockAllocation, {
                    stockRecord: stockRecord3,
                    documentLine: await document.lines.elementAt(2),
                    quantityInStockUnit: 18,
                });
                await allocation3.$.save();

                const stockSearchResult = await xtremStock.functions.stockEngine.searchStock(context, {
                    activeQuantityInStockUnit: 60,
                    item,
                    site,
                    stockCharacteristics: [{ statusList: [{ statusType: 'accepted' }] }],
                });

                await assertions(
                    stockSearchResult,
                    [
                        {
                            item,
                            site,
                            quantity: -12,
                        },
                    ],
                    0,
                );
            },
            { today: '2023-07-25' },
        ));
});
