import type { Context, NodeKey, NodePayloadData, decimal } from '@sage/xtrem-core';
import { Logger, Test, asyncArray, date, toDecimal } from '@sage/xtrem-core';
import { DateValue } from '@sage/xtrem-date-time';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremStockData from '@sage/xtrem-stock-data';
import * as xtremSystem from '@sage/xtrem-system';
import { assert } from 'chai';
import * as xtremStock from '../../../../lib';
import * as stockEngine from '../../../../lib/functions/stock-engine';
import * as stockJournalLib from '../../../../lib/functions/stock-journal-lib';
import { StockExtension } from '../../../../lib/node-extensions';
import * as testLib from '../../../fixtures/lib';

const logger = Logger.getLogger(__filename, 'stock-engine-test');

type ExpectedResult<ResultStatus extends xtremStockData.enums.StockUpdateResultAction> = {
    deletedOriginalStockRecord: boolean;
    status: ResultStatus;
    lotCreated: boolean;
    lotCode: string;
    stockJournalSearchData: Omit<xtremStock.interfaces.StockJournalKey, '_id'> & { _id?: number };
} & (ResultStatus extends 'changed' | 'transferred'
    ? {
          deletedOriginalStockRecord: true;
          status: 'changed' | 'transferred';
          stockData: NodePayloadData<xtremStockData.nodes.Stock>;
      }
    : {});

async function assertions(
    context: Context,
    stockDetails: xtremStockData.nodes.StockMovementDetail[],
    stockEngineReturn: xtremStockData.interfaces.StockEngineReturn[],
    expectedResults: ExpectedResult<xtremStockData.enums.StockUpdateResultAction>[],
): Promise<void> {
    assert.equal(stockEngineReturn.length, stockDetails.length);

    await asyncArray(expectedResults).forEach(async (expected, index) => {
        const stockDetail = stockDetails[index] as
            | xtremStockData.nodes.StockReceiptDetail
            | xtremStockData.nodes.StockIssueDetail
            | xtremStockData.nodes.StockChangeDetail
            | xtremStockData.nodes.StockAdjustmentDetail
            | xtremStockData.nodes.StockCorrectionDetail;
        logger.info(() => `reading detail ${index}`);

        if (!expected.deletedOriginalStockRecord || ['changed', 'transferred'].includes(expected.status)) {
            let stockData: NodePayloadData<xtremStockData.nodes.Stock>;

            if (expected.deletedOriginalStockRecord && ['changed', 'transferred'].includes(expected.status)) {
                stockData = (expected as ExpectedResult<'changed'>).stockData;
            } else {
                stockData = await stockDetail.$.payload({
                    withIds: true,
                    withNaturalKeyWhenThunk: true,
                });
            }

            const stockDetailLotData = await (await stockDetail.stockDetailLot)?.lot;
            stockData.lot = stockDetailLotData
                ? {
                      _id: stockDetailLotData._id,
                      sublot: await stockDetailLotData.sublot,
                  }
                : null;

            if (!(stockDetail instanceof xtremStockData.nodes.StockIssueDetail)) {
                const location = await stockDetail.location;
                const owner = await stockDetail.owner;

                stockData.location = location ? { _id: location._id } : undefined;

                stockData.owner = owner || undefined;
            } else {
                stockData =
                    (await (
                        await stockDetail.stockRecord
                    )?.$.payload({ withIds: true, withNaturalKeyWhenThunk: true })) ?? {};
            }

            if (stockDetail instanceof xtremStockData.nodes.StockChangeDetail) {
                assert.equal(await stockDetail.stockMovements.length, 1);

                const stockMovement = await stockDetail.stockMovements.elementAt(0);

                assert.equal(
                    toDecimal(await stockMovement.quantityInStockUnit),
                    toDecimal(-(await stockDetail.quantityInStockUnit)),
                );

                if (expected.deletedOriginalStockRecord) {
                    assert.notDeepEqual(
                        await (
                            await context.read(xtremStockData.nodes.StockChangeDetail, { _id: stockDetail._id })
                        ).deletedStockRecordData,
                        {},
                    );
                } else {
                    assert.equal((await stockMovement.site)._id, (await (await stockDetail.stockRecord)?.site)?._id);
                }
            }

            if (expected.lotCode && expected.lotCreated) {
                const lotRecord = await context.tryRead(xtremStockData.nodes.Lot, {
                    id: stockData.lot?.id,
                    item: stockData.item?._id,
                    sublot: stockData.lot?.sublot,
                });

                assert.equal(await lotRecord?.id, stockData.lot?.id);
                assert.equal(await (await lotRecord?.item)?.id, stockData?.item?.id);
                assert.equal(await lotRecord?.sublot, stockData.lot?.sublot);
            }

            const readParameter: NodeKey<xtremStockData.nodes.Stock> = {
                item: stockData.item?._id,
                lot: stockData.lot?._id,
                stockUnit: stockData.stockUnit?._id,
                location: stockData.location?._id ?? null,
                site: stockData.site?._id,
                status: stockData?.status?._id,
                owner: stockData.owner,
            };

            const readStockRecord = await context.read(xtremStockData.nodes.Stock, readParameter);

            assert.isNotNull(readStockRecord);

            const expectedQuantity = await readStockRecord.quantityInStockUnit;

            assert.equal(
                (await stockEngineReturn[index].stockRecord?.quantityInStockUnit)?.valueOf() ?? 0,
                expectedQuantity?.valueOf(),
            );
        } else {
            assert.isNull(stockEngineReturn[index].stockRecord);
        }

        assert.equal(stockEngineReturn[index].resultAction, expected.status);
        assert.equal(stockEngineReturn[index].lotCreated, expected.lotCreated);

        const stockJournal = await context.read(xtremStockData.nodes.StockJournal, expected.stockJournalSearchData);
        assert.equal(await stockEngineReturn[index].stockJournalRecord?.sequence, await stockJournal?.sequence);

        if (stockEngineReturn[index].resultAction === 'transferred') {
            assert.equal(
                -Number((await stockEngineReturn[index].stockJournalRecord?.quantityInStockUnit)?.valueOf()),
                (await stockDetails[index].quantityInStockUnit).valueOf(),
            );
        } else {
            assert.equal(
                (await stockEngineReturn[index].stockJournalRecord?.quantityInStockUnit)?.valueOf(),
                (await stockDetails[index].quantityInStockUnit).valueOf(),
            );
        }
    });
}

interface StockTestParameters {
    stockDetails: xtremStockData.nodes.StockMovementDetail[];
    item: xtremMasterData.nodes.Item;
    site: xtremSystem.nodes.Site;
    stockUnit: xtremMasterData.nodes.UnitOfMeasure;
}

describe('Stock engine', () => {
    afterEach(() => {
        xtremSystem.TestHelpers.Sinon.removeMocks();
    });

    const getTestParameters = async (context: Context, lineId: string = '1114'): Promise<StockTestParameters> => {
        const site = await context.read(xtremSystem.nodes.Site, { id: 'US003' });
        const item = await context.read(xtremMasterData.nodes.Item, { id: '3542LOT' });
        const stockUnit = await context.read(xtremMasterData.nodes.UnitOfMeasure, { id: 'LITER' });

        const stockDetails: xtremStockData.nodes.StockMovementDetail[] = [
            await xtremStockData.functions.stockDetailLib.createStockDetail(
                context,
                xtremStockData.nodes.StockReceiptDetail,
                {
                    effectiveDate: date.today(),
                    site,
                    item,
                    lotCreateData: {
                        id: 'TEST_FAKE_LOT_CODE_1',
                        expirationDate: date.today().addMonths(2),
                        potency: 60,
                        sublot: '001',
                        item,
                    },
                    location: await context.read(xtremMasterData.nodes.Location, { id: 'LOC11' }),
                    status: await context.read(xtremStockData.nodes.StockStatus, { id: 'A' }),
                    quantityInStockUnit: 50,
                    stockUnit,
                    documentLine: await context.read(xtremStock.nodes.StockReceiptLine, { _id: lineId }),
                    stockDetailLot: {
                        _id: -1,
                        lotNumber: 'LOT001',
                        supplierLot: 'SLOT001',
                        expirationDate: date.make(2023, 2, 28),
                    },
                },
            ),
        ];

        return { site, item, stockUnit, stockDetails };
    };

    const getTestSerialParameters = async (context: Context, lineId: string = '1155'): Promise<StockTestParameters> => {
        const site = await context.read(xtremSystem.nodes.Site, { id: 'US001' });
        const item = await context.read(xtremMasterData.nodes.Item, { id: 'ISSUESERIAL' });
        const stockUnit = await context.read(xtremMasterData.nodes.UnitOfMeasure, { id: 'EACH' });

        const stockDetails: xtremStockData.nodes.StockMovementDetail[] = [
            await xtremStockData.functions.stockDetailLib.createStockDetail(
                context,
                xtremStockData.nodes.StockReceiptDetail,
                {
                    effectiveDate: date.today(),
                    site,
                    item,
                    location: await context.read(xtremMasterData.nodes.Location, {
                        id: 'LOC3',
                        locationZone: '#US001|Loading dock',
                    }),
                    status: await context.read(xtremStockData.nodes.StockStatus, { id: 'A' }),
                    quantityInStockUnit: 1,
                    stockUnit,
                    documentLine: await context.read(xtremStock.nodes.StockReceiptLine, { _id: lineId }),
                },
                {
                    doNotSave: true,
                },
            ),
        ];

        return { site, item, stockUnit, stockDetails };
    };

    it('should update the stock record for a stock issue', () =>
        Test.withContext(async context => {
            const { stockDetails } = await getTestParameters(context);
            const site = await context.read(xtremSystem.nodes.Site, {
                id: 'US001',
            });
            const lot = await context.read(xtremStockData.nodes.Lot, {
                _id: 1,
            });
            const location = await context.read(xtremMasterData.nodes.Location, {
                _id: 1,
            });
            const item = await context.read(xtremMasterData.nodes.Item, { id: 'ITME_LOT' });
            const stockRecord = await xtremStockData.functions.stockLib.getStockRecord(context, {
                item,
                location,
                site,
                stockUnit: await item.stockUnit,
                lot,
                owner: await site.id,
                status: await (stockDetails[0] as xtremStockData.nodes.StockReceiptDetail).status,
            });

            (stockDetails as any).stockReceipt = stockRecord;

            const stockIssueDetails = [
                (
                    await xtremStockData.functions.stockDetailLib.cloneStockDetail(
                        context,
                        stockDetails[0] as xtremStockData.nodes.StockIssueDetail,
                        xtremStockData.nodes.StockIssueDetail,
                        {
                            overrides: {
                                site,
                                item,
                                stockUnit: await item.stockUnit,
                                quantityInStockUnit: -20,
                                stockRecord,
                            },
                        },
                    )
                ).newStockDetail,
            ];

            await assertions(
                context,
                stockIssueDetails,
                await stockEngine.stockIssue(context, stockIssueDetails, { movementType: 'issue' }),
                [
                    {
                        deletedOriginalStockRecord: false,
                        status: 'decreased',
                        lotCreated: false,
                        lotCode: '',
                        stockJournalSearchData: {
                            effectiveDate: await stockIssueDetails[0].effectiveDate,
                            item,
                            site,
                            sequence: 99994,
                        },
                    },
                ],
            );
        }));

    it('should remove the stock record for a stock issue', () =>
        Test.withContext(async context => {
            const { stockDetails } = await getTestParameters(context);
            const site = await context.read(xtremSystem.nodes.Site, {
                id: 'US001',
            });
            const lot = await context.read(xtremStockData.nodes.Lot, {
                _id: 1,
            });
            const location = await context.read(xtremMasterData.nodes.Location, {
                _id: 1,
            });
            const item = await context.read(xtremMasterData.nodes.Item, { id: 'ITME_LOT' });

            const stockRecord = await xtremStockData.functions.stockLib.getStockRecord(context, {
                item,
                location,
                site,
                stockUnit: await item.stockUnit,
                lot,
                owner: await site.id,
                status: await (stockDetails[0] as xtremStockData.nodes.StockReceiptDetail).status,
            });

            (stockDetails as any).stockReceipt = stockRecord;

            const stockIssueDetails = [
                (
                    await xtremStockData.functions.stockDetailLib.cloneStockDetail(
                        context,
                        stockDetails[0] as xtremStockData.nodes.StockIssueDetail,
                        xtremStockData.nodes.StockIssueDetail,
                        {
                            overrides: {
                                site,
                                item,
                                stockUnit: await item.stockUnit,
                                quantityInStockUnit: -1000,
                                stockRecord,
                            },
                        },
                    )
                ).newStockDetail,
            ];

            await assertions(
                context,
                stockIssueDetails,
                await stockEngine.stockIssue(context, stockIssueDetails, { movementType: 'issue' }),
                [
                    {
                        deletedOriginalStockRecord: true,
                        status: 'deleted',
                        lotCreated: false,
                        lotCode: '',
                        stockJournalSearchData: {
                            effectiveDate: await stockDetails[0].effectiveDate,
                            item,
                            site,
                            sequence: 99994,
                        },
                    },
                ],
            );
        }));

    // control on stock issue-detail prevents this to append
    it('Should throw an error because there is not enough quantity to issue', () =>
        Test.withContext(async context => {
            const { stockDetails } = await getTestParameters(context);
            const site = await context.read(xtremSystem.nodes.Site, {
                id: 'US001',
            });
            const lot = await context.read(xtremStockData.nodes.Lot, {
                _id: 1,
            });
            const location = await context.read(xtremMasterData.nodes.Location, {
                _id: 1,
            });
            const item = await context.read(xtremMasterData.nodes.Item, { id: 'ITME_LOT' });

            const stockRecord = await xtremStockData.functions.stockLib.getStockRecord(context, {
                item,
                location,
                site,
                stockUnit: await item.stockUnit,
                lot,
                owner: await site.id,
                status: await (stockDetails[0] as xtremStockData.nodes.StockReceiptDetail).status,
            });

            (stockDetails as any).stockReceipt = stockRecord;

            const stockIssueDetails = [
                (
                    await xtremStockData.functions.stockDetailLib.cloneStockDetail(
                        context,
                        stockDetails[0] as xtremStockData.nodes.StockIssueDetail,
                        xtremStockData.nodes.StockIssueDetail,
                        {
                            overrides: {
                                site,
                                item,
                                stockUnit: await item.stockUnit,
                                quantityInStockUnit: -900,
                                stockRecord,
                            },
                        },
                    )
                ).newStockDetail,
                (
                    await xtremStockData.functions.stockDetailLib.cloneStockDetail(
                        context,
                        stockDetails[0] as xtremStockData.nodes.StockIssueDetail,
                        xtremStockData.nodes.StockIssueDetail,
                        {
                            overrides: {
                                site,
                                item,
                                stockUnit: await item.stockUnit,
                                quantityInStockUnit: -900,
                                stockRecord,
                            },
                        },
                    )
                ).newStockDetail,
            ];

            await stockEngine.stockIssue(context, [stockIssueDetails[0]], { movementType: 'issue' });

            await assert.isRejected(
                stockEngine.stockIssue(context, [stockIssueDetails[1]], { movementType: 'issue' }),
                /The quantity \(900\) of stock-detail nb \(\d+\) exceeds the stock record quantity \(100\)\./,
            );
        }));

    it('try to issue stock that does not exist', () =>
        Test.withContext(async context => {
            const site = await context.read(xtremSystem.nodes.Site, { id: 'US001' });
            const item = await context.read(xtremMasterData.nodes.Item, { id: 'ITEM_LOT' });
            const status = await context.read(xtremStockData.nodes.StockStatus, { id: 'A1' });
            const documentLine = await context.read(xtremStock.nodes.StockReceiptLine, { _id: '1135' });
            const location = await context.read(xtremMasterData.nodes.Location, { _id: 1 });
            const stockRecord = await xtremStockData.functions.stockLib.getStockRecord(context, {
                item,
                location,
                site,
                stockUnit: await item.stockUnit,
                owner: await site.id,
                status,
                lot: null,
            });

            await assert.isRejected(
                xtremStockData.functions.stockDetailLib.createStockDetail(
                    context,
                    xtremStockData.nodes.StockIssueDetail,
                    {
                        effectiveDate: date.today(),
                        site,
                        item,
                        status,
                        quantityInStockUnit: -50,
                        stockUnit: await item.stockUnit,
                        orderCost: 0,
                        valuedCost: 0,
                        documentLine,
                        stockRecord,
                    },
                ),
                'The record was not created.',
            );
            assert.deepEqual(context.diagnoses, [
                { message: 'The stock record is mandatory.', severity: 3, path: ['stockRecord'] },
                { message: 'The location is mandatory.', severity: 3, path: ['location'] },
            ]);
        }));

    it('try to issue stock that has been removed since the detail exists', () =>
        Test.withContext(async context => {
            const { stockDetails } = await getTestParameters(context);

            const itemId = '#ITME_LOT';
            const lotNaturalKey = `#${itemId.substring(1)}|LOT00001|`;
            const stockUnitId = `#BOX`;
            const siteId = '#US001';
            const locationNaturalKey = `#LOC1|${siteId.substring(1)}|Loading dock`;
            const stockStatusId = `#A`;

            const documentLineNaturalKey = `#MSRUS0012022010001|113500`;

            const stockSearchData = {
                item: itemId,
                lot: lotNaturalKey,
                stockUnit: stockUnitId,
                site: siteId,
                location: locationNaturalKey,
                status: stockStatusId,
                owner: siteId.substring(1),
            };

            const stockRecord = await context.read(xtremStockData.nodes.Stock, stockSearchData);

            const quantityInStockUnit = -((await stockRecord?.quantityInStockUnit) || 0);

            (stockDetails as any).stockReceipt = stockRecord;

            const stockIssueDetails = [
                await xtremStockData.functions.stockDetailLib.createStockDetail(
                    context,
                    xtremStockData.nodes.StockIssueDetail,
                    {
                        site: siteId,
                        item: itemId,
                        stockUnit: stockUnitId,
                        quantityInStockUnit,
                        stockRecord,
                        documentLine: await context.read(xtremStock.nodes.StockReceiptLine, {
                            _id: documentLineNaturalKey,
                        }),
                    },
                ),
                await xtremStockData.functions.stockDetailLib.createStockDetail(
                    context,
                    xtremStockData.nodes.StockIssueDetail,
                    {
                        site: siteId,
                        item: itemId,
                        stockUnit: stockUnitId,
                        quantityInStockUnit,
                        stockRecord,
                        documentLine: await context.read(xtremStock.nodes.StockReceiptLine, {
                            _id: documentLineNaturalKey,
                        }),
                    },
                ),
            ];

            const secondStockDetailId = stockIssueDetails[1]._id;

            let stockEnginePromise = stockEngine.stockIssue(context, stockIssueDetails.slice(0, 1), {
                movementType: 'issue',
            });
            await assert.isFulfilled(stockEnginePromise);
            const stockEngineResult = await stockEnginePromise;
            assert.isNull(stockEngineResult[0].stockRecord);

            // mandatory for the bulk-update in xtremStockData.nodes.Stock > deleteBegin to be taken into account
            await Test.rollbackCache(context);

            stockEnginePromise = stockEngine.stockIssue(
                context,
                [
                    await context.read(
                        xtremStockData.nodes.StockIssueDetail,
                        { _id: secondStockDetailId },
                        { forUpdate: true },
                    ),
                ],
                {
                    movementType: 'issue',
                },
            );

            await assert.isRejected(
                stockEnginePromise,
                'Stock issue is not possible because the stock-record has been deleted by another transaction.',
            );
        }));

    it.skip('should create a stock record and decrease the other for a stock transfer shipment', () =>
        Test.withContext(async context => {
            const shipmentTransfer = await context.read(testLib.nodes.FakeTransferShipment, {
                number: 'TEST_TRANSFER_SHIPMENT_1',
            });

            const shipmentFirstLine = await shipmentTransfer.lines.elementAt(0);
            const stockDetails = await shipmentFirstLine.stockDetails.toArray();

            const site = await shipmentTransfer.getStockSite();
            const item = await shipmentFirstLine.getItem();

            await assertions(
                context,
                stockDetails,
                await stockEngine.stockChange(context, stockDetails, {
                    movementType: 'transfer',
                    intersiteTransferData: {
                        type: 'internalShipment',
                    },
                    stockDetailData: { isDocumentWithoutStockDetails: true },
                    allocationData: { isUsingAllocations: true },
                }),
                [
                    {
                        deletedOriginalStockRecord: false,
                        status: 'transferred',
                        lotCreated: false,
                        lotCode: '',
                        stockJournalSearchData: {
                            effectiveDate: await shipmentTransfer.getEffectiveDate(),
                            item,
                            site,
                            sequence: 99999,
                        },
                    },
                ],
            );
        }));

    it.skip('should increase a stock record and decrease the other for a stock transfer shipment', () =>
        Test.withContext(async context => {
            const shipmentTransfer = await context.read(testLib.nodes.FakeTransferShipment, {
                number: 'TEST_TRANSFER_SHIPMENT_2',
            });

            const shipmentFirstLine = await shipmentTransfer.lines.elementAt(0);
            const stockDetails = await shipmentFirstLine.stockDetails.toArray();
            const firstStockDetail = await shipmentFirstLine.stockDetails.elementAt(0);

            const site = await shipmentTransfer.getStockSite();
            const item = await firstStockDetail.item;

            const stockEngineReturn = await stockEngine.stockChange(context, stockDetails, {
                movementType: 'transfer',
                intersiteTransferData: {
                    type: 'internalShipment',
                },
                stockDetailData: { isDocumentWithoutStockDetails: true },
                allocationData: { isUsingAllocations: true },
            });

            await assertions(context, stockDetails, stockEngineReturn, [
                {
                    deletedOriginalStockRecord: false,
                    status: 'transferred',
                    lotCreated: false,
                    lotCode: '',
                    stockJournalSearchData: {
                        effectiveDate: await shipmentTransfer.getEffectiveDate(),
                        item,
                        site,
                        sequence: 99999,
                    },
                },
            ]);
        }));

    it.skip('should increase a stock record and delete the other for a stock transfer shipment', () =>
        Test.withContext(async context => {
            const shipmentTransfer = await context.read(testLib.nodes.FakeTransferShipment, {
                number: 'TEST_TRANSFER_SHIPMENT_3',
            });

            const shipmentFirstLine = await shipmentTransfer.lines.elementAt(0);
            const stockDetails = await shipmentFirstLine.stockDetails.toArray();
            const firstStockDetail = await shipmentFirstLine.stockDetails.elementAt(0);

            const site = await shipmentTransfer.getStockSite();
            const item = await firstStockDetail.item;

            const stockData = await firstStockDetail.$.payload({
                withIds: true,
                withNaturalKeyWhenThunk: true,
            });

            const stockEngineReturn = await stockEngine.stockChange(context, stockDetails, {
                movementType: 'transfer',
                intersiteTransferData: {
                    type: 'internalShipment',
                },
                stockDetailData: { isDocumentWithoutStockDetails: true },
                allocationData: { isUsingAllocations: true },
            });

            await assertions(context, stockDetails, stockEngineReturn, [
                {
                    deletedOriginalStockRecord: true,
                    status: 'transferred',
                    lotCreated: false,
                    lotCode: '',
                    stockJournalSearchData: {
                        effectiveDate: await shipmentTransfer.getEffectiveDate(),
                        item,
                        site,
                        sequence: 99999,
                    },
                    stockData,
                } as ExpectedResult<'transferred'>,
            ]);

            // finally verify that the original stock-record has been deleted
            await assert.isRejected(
                context.read(xtremStockData.nodes.Stock, {
                    site: '#CAS01',
                    item: '#STOFIFO',
                    stockUnit: '#EACH',
                    status: '#A2',
                    location: null,
                    lot: null,
                    owner: '#CAS01',
                    isInTransit: false,
                }),
                'Stock: record not found: {"site":"#CAS01","item":"#STOFIFO","stockUnit":"#EACH","status":"#A2","location":null,"lot":null,"owner":"#CAS01","isInTransit":false}',
            );
        }));

    it('Should compute fifo value on stock transfer shipment and receipt', () =>
        Test.withContext(async context => {
            const shipmentTransfer = await context.read(
                testLib.nodes.FakeTransferShipment,
                {
                    number: 'TEST_TRANSFER_SHIPMENT_5',
                },
                { forUpdate: true },
            );
            const fakeNotificationId = '99999';
            (context as any)._contextValues.notificationId = fakeNotificationId;
            await StockExtension.transfer(context, {
                replyTopic: 'fake/reply',
                stockUpdateParameters: {
                    movementType: 'transfer',
                    intersiteTransferData: { type: 'internalShipment' },
                    stockDetailData: { isDocumentWithoutStockDetails: true },
                    allocationData: { isUsingAllocations: true },
                },
                documents: [
                    {
                        id: shipmentTransfer._id,
                        lines: await shipmentTransfer.lines
                            .map(async line => ({
                                id: line._id,
                                sortValue: await line._sortValue,
                            }))
                            .toArray(),
                    },
                ],
            });
            const shipmentFirstLine = await shipmentTransfer.lines.elementAt(0);

            const firstStockDetail = await shipmentFirstLine.stockDetails.elementAt(0);
            const firstStockDetailMovements = await firstStockDetail.stockMovements.elementAt(0);
            assert.equal(await firstStockDetail.stockMovements.length, 1);
            assert.deepEqual(await firstStockDetailMovements.movementAmount, -125.0);
            assert.deepEqual(await firstStockDetailMovements.orderAmount, -125.0);

            const site = await shipmentTransfer.getStockSite();
            const item = await firstStockDetail.item;

            const fifoValuationTier = await context.select(
                xtremStockData.nodes.FifoValuationTier,
                { remainingQuantity: true, amount: true },
                {
                    filter: {
                        site: site._id,
                        item: item._id,
                    },
                },
            );
            assert.equal(fifoValuationTier.length, 3);
            assert.equal(fifoValuationTier.at(0)?.remainingQuantity, 0);
            assert.equal(fifoValuationTier.at(1)?.remainingQuantity, 15);
            assert.equal(fifoValuationTier.at(0)?.amount, 0);
            assert.equal(fifoValuationTier.at(1)?.amount, 75);

            await shipmentFirstLine.$.set({
                orderCost: 8.3333,
            });
            await shipmentFirstLine.$.save();

            const receiptTransfer = await context.read(
                testLib.nodes.FakeTransferReceipt,
                {
                    number: 'TEST_TRANSFER_RECEIPT_2',
                },
                { forUpdate: true },
            );

            const stockTransferReceiptLine = await receiptTransfer.lines.elementAt(0);
            await stockTransferReceiptLine.$.set({
                stockDetails: [
                    {
                        _sortValue: 10,
                        _action: 'update',
                        intersiteShipmentDetail: await shipmentFirstLine.stockDetails.elementAt(0),
                    },
                ],
                shipmentLine: shipmentFirstLine,
            });
            await stockTransferReceiptLine.$.save();

            const stockReceiptDetail = await stockTransferReceiptLine.stockDetails.elementAt(0);

            await StockExtension.transfer(context, {
                replyTopic: 'fake/reply',
                stockUpdateParameters: {
                    movementType: 'transfer',
                    intersiteTransferData: { type: 'internalReceipt' },
                },
                documents: [
                    {
                        id: receiptTransfer._id,
                        lines: await receiptTransfer.lines
                            .map(async line => ({
                                id: line._id,
                                sortValue: await line._sortValue,
                            }))
                            .toArray(),
                    },
                ],
            });

            const stockTransferReceiptMovements = await stockReceiptDetail.stockMovements.elementAt(0);
            assert.equal(await stockReceiptDetail.stockMovements.length, 1);
            assert.deepEqual(await stockTransferReceiptMovements.orderAmount, 125.0);
            assert.deepEqual(await stockTransferReceiptMovements.movementAmount, 125.0);

            const receiptStockSite = await receiptTransfer.getStockSite();
            const fifoValuationTierReceipt = await context.select(
                xtremStockData.nodes.FifoValuationTier,
                { remainingQuantity: true, amount: true },
                {
                    filter: {
                        site: receiptStockSite._id,
                        item: item._id,
                    },
                },
            );
            assert.equal(fifoValuationTierReceipt.length, 1);
            assert.equal(fifoValuationTierReceipt.at(0)?.remainingQuantity, 15);
            assert.equal(fifoValuationTierReceipt.at(0)?.amount, 125);
        }));
    it('create a stock receipt to update stock value', () =>
        Test.withContext(async context => {
            const site = await context.read(xtremSystem.nodes.Site, { id: 'US003' });
            const item = await context.read(xtremMasterData.nodes.Item, { id: 'SC100' });
            const status = await context.read(xtremStockData.nodes.StockStatus, { id: 'A' });
            const location = await context.read(xtremMasterData.nodes.Location, {
                locationZone: '#US003|Production line',
                id: 'LOC5',
            });
            const documentLine = await context.read(xtremStock.nodes.StockReceiptLine, { _id: '1135' });
            const stockReceiptDetails = [
                await xtremStockData.functions.stockDetailLib.createStockDetail(
                    context,
                    xtremStockData.nodes.StockReceiptDetail,
                    {
                        effectiveDate: date.today(),
                        site,
                        item,
                        location,
                        quantityInStockUnit: 50,
                        stockUnit: await item.stockUnit,
                        status,
                        orderCost: 0.5,
                        valuedCost: 0.44,
                        documentLine,
                    },
                ),
            ];

            await stockEngine.stockReceipt(context, stockReceiptDetails, { movementType: 'receipt' });

            const itemSite = await context.read(xtremMasterData.nodes.ItemSite, { item: item._id, site: site._id });

            // FIXME: Replace this hack when activating new stock engine by averageCostValue from itemSite
            // this is a hack cause overrides (in stock-data package) aren't in use
            const stocks = await context
                .query(xtremStockData.nodes.Stock, {
                    filter: { site: (await itemSite.site)._id, item: (await itemSite.item)._id },
                })
                .toArray();
            const qty = await asyncArray(stocks).reduce(
                async (total, line) => total + (await line.quantityInStockUnit),
                0,
            );
            const averageCostValue = qty !== 0 ? (await itemSite.stockValuationAtAverageCost) / qty : 0;

            assert.equal(averageCostValue.valueOf(), parseFloat('0.47'));
        }));

    it('adjust stock value', () =>
        Test.withContext(async context => {
            const site = await context.read(xtremSystem.nodes.Site, { id: 'US003' });
            const item = await context.read(xtremMasterData.nodes.Item, { id: 'SC100' });
            const status = await context.read(xtremStockData.nodes.StockStatus, { id: 'A' });
            const location = await context.read(xtremMasterData.nodes.Location, {
                locationZone: '#US003|Production line',
                id: 'LOC5',
            });
            const documentLine = await context.read(xtremStock.nodes.StockReceiptLine, { _id: '1135' });

            const stockDetails = [
                await xtremStockData.functions.stockDetailLib.createStockDetail(
                    context,
                    xtremStockData.nodes.StockAdjustmentDetail,
                    {
                        effectiveDate: date.today(),
                        site,
                        item,
                        status,
                        quantityInStockUnit: 0,
                        stockUnit: await item.stockUnit,
                        orderCost: 0.5,
                        valuedCost: 0.44,
                        documentLine,
                        reasonCode: await context.read(xtremMasterData.nodes.ReasonCode, { id: 'R1' }),
                        stockRecord: await context
                            .query(xtremStockData.nodes.Stock, {
                                filter: {
                                    site,
                                    item,
                                    location,
                                    stockUnit: await item.stockUnit,
                                    status,
                                },
                            })
                            .elementAt(0),
                    },
                ),
            ];

            await stockEngine.stockValueAdjustment(context, stockDetails[0], { movementType: 'adjustment' });
            const itemSite = await context.read(xtremMasterData.nodes.ItemSite, { item: item._id, site: site._id });
            assert.equal((await itemSite.averageCostValue).valueOf(), parseFloat('0.44'));
        }));

    async function checkStockValueChange(
        context: Context,
        args: {
            itemId: string;
            siteId: string;
            variance: decimal;
            fifoCost?: xtremStockData.nodes.FifoValuationTier;
        },
    ) {
        const mocks = xtremSystem.TestHelpers.Sinon.registerMocks([
            {
                type: 'stub',
                reference: xtremStock.functions.notificationLib,
                name: 'reply',
                returns: Promise.resolve(/* do nothing */),
            },
        ]);
        const replyStub = mocks[0].mock as sinon.SinonStub;

        let itemSite = await context.read(xtremMasterData.nodes.ItemSite, { item: args.itemId, site: args.siteId });

        const itemId = await (await itemSite.item).id;
        const siteId = await (await itemSite.site).id;
        const valuationMethod = await itemSite.valuationMethod;

        let quantity = await itemSite.inStockQuantity;
        let unitCost = await itemSite.averageCostValue;
        let amount = await itemSite.stockValuationAtAverageCost;
        const fifoValuationTierId = args.fifoCost?._id;

        if (valuationMethod === 'fifoCost') {
            assert.exists(args.fifoCost);
            if (!args.fifoCost) return;
            quantity = await args.fifoCost.remainingQuantity;
            unitCost = await args.fifoCost.unitCost;
            amount = await args.fifoCost.amount;
        } else {
            assert.notExists(args.fifoCost);
            if (args.fifoCost) return;
        }

        const newAmount = amount + args.variance;
        // newUnitCost is rounded to 4 decimals in the node (dataType = costDataType)
        const newUnitCost = Math.round((newAmount / quantity) * 10000) / 10000;

        const document = await context.create(xtremStock.nodes.StockValueChange, {
            number: 'TEST',
            item: itemId,
            site: siteId,
            description: 'SVCTEST',
            valuationMethod,
            lines: [
                {
                    fifoCost: args.fifoCost,
                    quantity,
                    newUnitCost,
                    newAmount,
                    unitCost,
                    amount,
                    stockDetails: [
                        {
                            _action: 'create',
                            item: itemId,
                            site: siteId,
                            valuationMethod,
                            amount: args.variance,
                            quantityInStockUnit: quantity,
                            fifoValuationTier: args.fifoCost,
                        },
                    ],
                },
            ],
        });
        await document.$.save();

        const documentId = document._id;
        const documentLineId = (await document.lines.elementAt(0))._id;
        const documentLineSortValue = await (await document.lines.elementAt(0))._sortValue;
        const stockDetails = await (await document.lines.elementAt(0)).stockDetails.toArray();
        const stockDetailId = stockDetails[0]._id;
        (context as any)._contextValues.notificationId = 'notification-id';

        await xtremStock.nodeExtensions.StockExtension.changeValue(context, {
            replyTopic: 'fake/reply',
            stockUpdateParameters: { movementType: 'valueChange' },
            documents: [
                {
                    id: documentId,
                    lines: [
                        {
                            id: documentLineId,
                            sortValue: documentLineSortValue,
                        },
                    ],
                },
            ],
        });

        await Test.rollbackCache(context);

        const stockDetail = await context.read(xtremStockData.nodes.StockValueDetail, { _id: stockDetailId });

        const stockJournalSearchData = {
            item: await stockDetail.item,
            site: await stockDetail.site,
            effectiveDate: date.today(),
        };
        const newStockJournal = await stockJournalLib.getLastStockJournal(context, stockJournalSearchData);

        assert.isNotNull(newStockJournal);
        if (!newStockJournal) return;

        assert.deepEqual(replyStub.getCall(0).args[1], {
            replyTopic: 'fake/reply',
            stockUpdateParameters: { movementType: 'valueChange' },
            documents: [
                {
                    id: documentId,
                    lines: [
                        {
                            id: documentLineId,
                            sortValue: documentLineSortValue,
                        },
                    ],
                },
            ],
        });
        assert.deepEqual(replyStub.getCall(0).args[2], {
            batchTrackingId: undefined,
            requestNotificationId: 'notification-id',
            updateResults: {
                valueChange: {
                    documents: [
                        {
                            id: documentId,
                            lines: [
                                {
                                    errorMessage: undefined,
                                    id: documentLineId,
                                    sortValue: documentLineSortValue,
                                    stockUpdateResultStatus: 'valueChange',
                                    originLineIds: [],
                                    stockJournalRecords: [newStockJournal._id],
                                },
                            ],
                        },
                    ],
                },
            },
        });

        assert.equal(await newStockJournal.stockDetail, stockDetail);
        assert.equal(await newStockJournal.quantityInStockUnit, 0);
        assert.equal(Number(await newStockJournal.orderAmount), args.variance);
        assert.equal(Number(await newStockJournal.movementAmount), args.variance);
        assert.equal(Number(await newStockJournal.nonAbsorbedAmount), 0);

        if (valuationMethod === 'fifoCost') {
            const fifoCost = await context.read(xtremStockData.nodes.FifoValuationTier, { _id: fifoValuationTierId });
            assert.equal(Number(await fifoCost.amount), newAmount);
        }

        if (valuationMethod === 'averageCost') {
            itemSite = await context.read(xtremMasterData.nodes.ItemSite, { item: args.itemId, site: args.siteId });

            assert.equal(Number(await itemSite.stockValuationAtAverageCost), newAmount);
        }
    }

    it('change stock value at average cost', () =>
        Test.withContext(async context => {
            await checkStockValueChange(context, { itemId: '#STOAVC', siteId: '#US001', variance: 123.45 });
        }));

    it('change stock value at FIFO cost', () =>
        Test.withContext(async context => {
            const fifoCost = await context.read(xtremStockData.nodes.FifoValuationTier, { _id: 115100 });
            await checkStockValueChange(context, { itemId: '#STOFIFO', siteId: '#US001', variance: 123.45, fifoCost });
        }));

    // FIXME: fix after switch to new stock journal
    it('check stock-journal/stock update when adjusting stock value', () =>
        Test.withContext(async context => {
            const site = await context.read(xtremSystem.nodes.Site, { id: 'US003' });
            const item = await context.read(xtremMasterData.nodes.Item, { id: 'SC100' });
            const status = await context.read(xtremStockData.nodes.StockStatus, { id: 'A' });
            const location = await context.read(xtremMasterData.nodes.Location, {
                locationZone: '#US003|Production line',
                id: 'LOC5',
            });
            const documentLine = await context.read(xtremStock.nodes.StockReceiptLine, { _id: '1135' });

            const stockDetails = [
                await xtremStockData.functions.stockDetailLib.createStockDetail(
                    context,
                    xtremStockData.nodes.StockAdjustmentDetail,
                    {
                        effectiveDate: date.today(),
                        site,
                        item,
                        quantityInStockUnit: 0,
                        stockUnit: await item.stockUnit,
                        orderCost: 0.5,
                        valuedCost: 0.44,
                        documentLine,
                        reasonCode: await context.read(xtremMasterData.nodes.ReasonCode, { id: 'R1' }),
                        stockRecord: await context
                            .query(xtremStockData.nodes.Stock, {
                                filter: {
                                    site,
                                    item,
                                    location,
                                    stockUnit: await item.stockUnit,
                                    status,
                                },
                            })
                            .elementAt(0),
                    },
                ),
            ];

            const stockJournalSearchData = {
                site: await stockDetails[0].site,
                effectiveDate: await stockDetails[0].effectiveDate,
                item: await stockDetails[0].item,
            };
            const stockSearchData = {
                site,
                item,
                location,
                stockUnit: await item.stockUnit,
                status,
                owner: await site.id,
                lot: null,
            };

            const previousStateOfStockRecord = await xtremStockData.functions.stockLib.getStockRecord(
                context,
                stockSearchData,
            );
            const previousStockJournal = await stockJournalLib.getLastStockJournal(context, stockJournalSearchData);

            await stockEngine.stockValueAdjustment(context, stockDetails[0], { movementType: 'adjustment' });
            const newStateOfStockRecord = await xtremStockData.functions.stockLib.getStockRecord(
                context,
                stockSearchData,
            );
            const newStockJournal = await stockJournalLib.getLastStockJournal(context, stockJournalSearchData);

            assert.deepEqual(await previousStateOfStockRecord?.$.payload(), await newStateOfStockRecord?.$.payload());
            assert.notDeepEqual(await previousStockJournal?.$.payload(), await newStockJournal?.$.payload());
            assert.equal(await newStockJournal?.quantityInStockUnit, 0);
            assert.equal((await newStockJournal?.orderCost)?.valueOf(), (await stockDetails[0].orderCost).valueOf());
        }));

    it('create a stock quantity adjustment to decrease stock', () =>
        Test.withContext(async context => {
            const site = await context.read(xtremSystem.nodes.Site, { id: 'US001' });
            const item = await context.read(xtremMasterData.nodes.Item, { id: 'ITEM_LOT' });
            const lot = await context.read(xtremStockData.nodes.Lot, { item, id: 'LOT00001', sublot: '' });
            const status = await context.read(xtremStockData.nodes.StockStatus, { id: 'A' });
            const location = await context.read(xtremMasterData.nodes.Location, {
                locationZone: '#US001|Loading dock',
                id: 'LOC1',
            });
            const documentLine = await context.read(xtremStock.nodes.StockReceiptLine, { _id: '1135' });
            const stockDetails = [
                await xtremStockData.functions.stockDetailLib.createStockDetail(
                    context,
                    xtremStockData.nodes.StockAdjustmentDetail,
                    {
                        effectiveDate: await (await documentLine.document).getEffectiveDate(),
                        item,
                        site,
                        quantityInStockUnit: -50,
                        documentLine,
                        reasonCode: await context.read(xtremMasterData.nodes.ReasonCode, { id: 'R1' }),
                        stockRecord: await context
                            .query(xtremStockData.nodes.Stock, {
                                filter: {
                                    site,
                                    item,
                                    location,
                                    stockUnit: await item.stockUnit,
                                    status,
                                    lot,
                                },
                            })
                            .elementAt(0),
                    },
                ),
            ];

            await assertions(
                context,
                stockDetails,
                [(await stockEngine.stockAdjustment(context, stockDetails, { movementType: 'adjustment' }))[0]],
                [
                    {
                        deletedOriginalStockRecord: false,
                        status: 'decreased',
                        lotCreated: false,
                        lotCode: '',
                        stockJournalSearchData: {
                            effectiveDate: await (await documentLine.document).getEffectiveDate(),
                            item,
                            site,
                            sequence: 99999,
                        },
                    },
                ],
            );
        }));

    it('create a stock quantity adjustment to increase stock', () =>
        Test.withContext(
            async context => {
                const site = await context.read(xtremSystem.nodes.Site, { id: 'US001' });
                const item = await context.read(xtremMasterData.nodes.Item, { id: 'ITEM_LOT' });
                const lot = await context.read(xtremStockData.nodes.Lot, { item, id: 'LOT00001', sublot: '' });
                const status = await context.read(xtremStockData.nodes.StockStatus, { id: 'A' });
                const location = await context.read(xtremMasterData.nodes.Location, {
                    locationZone: '#US001|Loading dock',
                    id: 'LOC1',
                });
                const documentLine = await context.read(xtremStock.nodes.StockReceiptLine, { _id: '1135' });
                const stockRecord = await context
                    .query(xtremStockData.nodes.Stock, {
                        filter: {
                            site,
                            item,
                            location,
                            stockUnit: await item.stockUnit,
                            status,
                            lot,
                        },
                    })
                    .elementAt(0);
                const stockDetails = [
                    await xtremStockData.functions.stockDetailLib.createStockDetail(
                        context,
                        xtremStockData.nodes.StockAdjustmentDetail,
                        {
                            effectiveDate: date.today(),
                            site,
                            item,
                            quantityInStockUnit: 50,
                            stockUnit: await item.stockUnit,
                            orderCost: 0,
                            valuedCost: 0,
                            documentLine,
                            reasonCode: await context.read(xtremMasterData.nodes.ReasonCode, { id: 'R1' }),
                            stockRecord,
                            stockDetailLot: { lot: await stockRecord.lot },
                        },
                    ),
                ];

                await assertions(
                    context,
                    stockDetails,
                    [
                        (
                            await stockEngine.stockAdjustment(context, stockDetails, {
                                movementType: 'adjustment',
                            })
                        )[0],
                    ],
                    [
                        {
                            deletedOriginalStockRecord: false,
                            status: 'increased',
                            lotCreated: false,
                            lotCode: '',
                            stockJournalSearchData: {
                                effectiveDate: date.today(),
                                item,
                                site,
                                sequence: 99999,
                            },
                        },
                    ],
                );
            },
            { now: '2022-01-07T10:00:00' },
        ));

    async function testStockValueCorrection(
        context: Context,
        testCase: {
            documentLine: number; // document line to correct
            amountToAbsorb: decimal; // amount to absorb
            impactedQuantity: decimal; // quantity of corrector document line

            expectedValues: {
                stockValueVariance: decimal; // the expected variance on global stock value
                // the list of values to find in StockJournal records
                // created by the correction (quantityInStockUnit=0) and ordered by orderAmount
                stockJournals: Array<{
                    orderAmount: decimal;
                    movementAmount: decimal;
                    nonAbsorbedAmount: decimal;
                    orderCost: decimal;
                    valuedCost: decimal;
                }>;
            };
        },
    ) {
        const originDocumentLineID = 1101; // must be an existing BaseDocumentLine

        let documentLine = await context.read(xtremStock.nodes.StockReceiptLine, {
            _id: testCase.documentLine,
        });

        let itemSite = await documentLine.itemSite;
        assert.isNotNull(itemSite);
        if (!itemSite) return;

        // Check with Cyril
        // const initialStockAmount = await itemSite.stockValue;

        const stockReceiptDetails = await context
            .query(xtremStockData.nodes.StockReceiptDetail, {
                filter: { documentLine: documentLine._id },
            })
            .toArray();

        const correctorDocumentLine = await context.read(xtremMasterData.nodes.BaseDocumentLine, {
            _id: originDocumentLineID,
        });

        const valueCorrections: Array<
            xtremStockData.interfaces.ValuationCorrectionParameter & {
                correctorDocumentLine: xtremMasterData.nodes.BaseDocumentLine;
            }
        > = [
            {
                correctedDocumentLineID: documentLine._id,
                reasonCodeID: 'R1',
                correctorDocumentLine,
                impactedQuantity: testCase.impactedQuantity,
                amountToAbsorb: testCase.amountToAbsorb,
                // TODO: remove these properties
                absorbedAmount: testCase.amountToAbsorb,
                nonAbsorbedAmount: 0,
            },
        ];

        const stockDetails = await xtremStockData.functions.stockDetailLib.createStockCorrectionDetailFromStockDetail(
            context,
            asyncArray(stockReceiptDetails),
            valueCorrections[0],
        );

        const stockUpdateParameters: xtremStock.interfaces.StockUpdateParametersWithValuationManager<xtremStockData.enums.StockMovementTypeEnum> =
            {
                movementType: 'correction',
                stockValuationManager:
                    (await xtremStock.functions.notificationLib.getStockValuationManager(context, stockDetails)) ??
                    undefined,
            };

        await stockEngine.stockCorrection(context, stockDetails, stockUpdateParameters);

        if (stockUpdateParameters.stockValuationManager) {
            await stockUpdateParameters.stockValuationManager.finish(context);
        }

        // rollbackCache function will flush deferred actions and saves
        await Test.rollbackCache(context);

        documentLine = await context.read(xtremStock.nodes.StockReceiptLine, {
            _id: testCase.documentLine,
        });
        itemSite = await documentLine.itemSite;
        assert.isNotNull(itemSite);
        if (!itemSite) return;

        const stockJournals = await context
            .query(xtremStockData.nodes.StockJournal, {
                filter: { documentLine: testCase.documentLine, quantityInStockUnit: 0 },
                orderBy: { orderAmount: 1 },
            })
            .toArray();

        assert.equal(stockJournals.length, testCase.expectedValues.stockJournals.length);

        // await asyncArray(stockJournals).forEach(async (stockJournal, index) => {
        // assert.equal((await stockJournal.documentLine)?._id, testCase.documentLine);
        // assert.equal((await stockJournal.sourceDocumentLine)?._id, originDocumentLineID);
        // assert.equal(
        //     (await stockJournal.effectiveDate).valueOf(),
        //     (await stockReceiptDetails[0].effectiveDate).valueOf(),
        // );
        // Check with Cyril
        // assert.equal(
        //     (await stockJournal.orderAmount).valueOf(),
        //     testCase.expectedValues.stockJournals[index].orderAmount,
        // );
        // assert.equal(
        //     (await stockJournal.movementAmount).valueOf(),
        //     testCase.expectedValues.stockJournals[index].movementAmount,
        // );
        // Check with Cyril
        // assert.equal(
        //     (await stockJournal.nonAbsorbedAmount).valueOf(),
        //     testCase.expectedValues.stockJournals[index].nonAbsorbedAmount,
        // );
        // assert.equal(
        //     (await stockJournal.orderCost).valueOf(),
        //     testCase.expectedValues.stockJournals[index].orderCost,
        // );
        // assert.equal(
        //     (await stockJournal.valuedCost).valueOf(),
        //     testCase.expectedValues.stockJournals[index].valuedCost,
        // );
        // });
        // assert.equal(
        //     await itemSite.stockValue,
        //     Number(initialStockAmount + testCase.expectedValues.stockValueVariance),
        // );
    }

    async function testReceiptPriceCorrection(
        context: Context,
        params: {
            receiptLine: xtremStock.nodes.StockReceiptLine;
            correctorDocumentLine: xtremMasterData.nodes.BaseDocumentLine;
            impactedQuantity: decimal;
            amountToAbsorb: decimal;
            expectedValues: {
                stockUpdateResultStatus: xtremStockData.enums.StockUpdateResultAction;
                originLineIds: xtremMasterData.nodes.BaseDocumentLine['_id'][];
                stockJournalRecords: xtremStockData.nodes.StockJournal['_id'][];
            };
        },
    ) {
        const message = `receipt number: ${params.receiptLine.documentNumber} item: ${await (
            await params.receiptLine.item
        ).id}`;

        const mocks = xtremSystem.TestHelpers.Sinon.registerMocks([
            {
                type: 'stub',
                reference: xtremStock.functions.notificationLib,
                name: 'reply',
                returns: Promise.resolve(/* do nothing */),
            },
        ]);
        const replyStub = mocks[0].mock as sinon.SinonStub;

        const documentId = await params.receiptLine.documentId;
        const documentLineId = params.receiptLine._id;
        const documentLineSortValue = params.receiptLine._sortValue;

        (context as any)._contextValues.notificationId = 'notification-id';

        const valueCorrections: Array<
            xtremStockData.interfaces.ValuationCorrectionParameter & {
                correctorDocumentLine: xtremMasterData.nodes.BaseDocumentLine;
            }
        > = [
            {
                correctedDocumentLineID: documentLineId,
                reasonCodeID: 'R1',
                correctorDocumentLine: params.correctorDocumentLine,
                impactedQuantity: params.impactedQuantity,
                amountToAbsorb: params.amountToAbsorb,
                absorbedAmount: 0,
                nonAbsorbedAmount: 0,
            },
        ];

        const stockReceiptDetails = context.query(xtremStockData.nodes.StockReceiptDetail, {
            filter: { documentLine: documentLineId },
        });
        const stockDetails = await xtremStockData.functions.stockDetailLib.createStockCorrectionDetailFromStockDetail(
            context,
            stockReceiptDetails,
            valueCorrections[0],
        );

        logger.verbose(() => `stock details : ${JSON.stringify(stockDetails)}`);

        assert.equal(stockDetails.length, await stockReceiptDetails.length);

        await xtremStock.nodeExtensions.StockExtension.correct(context, {
            replyTopic: 'fake/reply',
            stockUpdateParameters: { movementType: 'correction' },
            documents: [
                {
                    id: documentId,
                    lines: [
                        {
                            id: documentLineId,
                            sortValue: await documentLineSortValue,
                        },
                    ],
                },
            ],
        });

        // The useless StockCorrectionDetail records are deleted by the stock-engine
        const newStockCorrectionDetails = context.query(xtremStockData.nodes.StockCorrectionDetail, {
            filter: { documentLine: documentLineId },
        });
        assert.equal(await newStockCorrectionDetails.length, 0);

        assert.deepEqual(
            replyStub.getCall(0).args[1],
            {
                replyTopic: 'fake/reply',
                stockUpdateParameters: { movementType: 'correction' },
                documents: [
                    {
                        id: documentId,
                        lines: [
                            {
                                id: documentLineId,
                                sortValue: await documentLineSortValue,
                            },
                        ],
                    },
                ],
            },
            message,
        );
        assert.deepEqual(
            replyStub.getCall(0).args[2],
            {
                batchTrackingId: undefined,
                requestNotificationId: 'notification-id',
                updateResults: {
                    correction: {
                        documents: [
                            {
                                id: documentId,
                                lines: [
                                    {
                                        errorMessage: undefined,
                                        id: documentLineId,
                                        sortValue: await documentLineSortValue,
                                        stockUpdateResultStatus: params.expectedValues.stockUpdateResultStatus,
                                        originLineIds: params.expectedValues.originLineIds,
                                        stockJournalRecords: params.expectedValues.stockJournalRecords,
                                    },
                                ],
                            },
                        ],
                    },
                },
            },
            message,
        );
        xtremSystem.TestHelpers.Sinon.removeMocks();
    }

    async function testReceiptPriceCorrectionWithoutAmountToAbsorb(
        context: Context,
        params: { receiptNumber: string; valuationMethod: xtremMasterData.enums.CostValuationMethod },
    ) {
        const receipt = await context.read(xtremStock.nodes.StockReceipt, { number: params.receiptNumber });
        const correctorReceipt = await context.read(xtremStock.nodes.StockReceipt, {
            number: 'MISC_RECEIPT30',
        });

        const receiptLine = await receipt.lines.elementAt(0);
        const itemSite = context.read(xtremMasterData.nodes.ItemSite, {
            item: await receiptLine.item,
            site: await receipt.stockSite,
        });
        assert.equal(await (await itemSite).valuationMethod, params.valuationMethod);
        const correctorDocumentLine = await correctorReceipt.lines.elementAt(0);

        await testReceiptPriceCorrection(context, {
            receiptLine,
            correctorDocumentLine,
            impactedQuantity: await receiptLine.quantityInStockUnit,
            amountToAbsorb: 0,
            expectedValues: {
                stockUpdateResultStatus: 'noChange',
                originLineIds: [correctorDocumentLine._id],
                stockJournalRecords: [],
            },
        });
    }

    it('create a stock value correction no change standard cost', () =>
        Test.withContext(async context => {
            await testReceiptPriceCorrectionWithoutAmountToAbsorb(context, {
                receiptNumber: 'MISC_RECEIPT14',
                valuationMethod: 'standardCost',
            });
        }));

    it('create a stock value correction no change AVC', () =>
        Test.withContext(async context => {
            await testReceiptPriceCorrectionWithoutAmountToAbsorb(context, {
                receiptNumber: 'MISC_RECEIPT16',
                valuationMethod: 'averageCost',
            });
        }));

    it('create a stock value correction no change FIFO', () =>
        Test.withContext(
            async context => {
                await testReceiptPriceCorrectionWithoutAmountToAbsorb(context, {
                    receiptNumber: 'MISC_RECEIPT26',
                    valuationMethod: 'fifoCost',
                });
            },
            { testActiveServiceOptions: [xtremMasterData.serviceOptions.fifoValuationMethodOption] },
        ));

    it('create a stock value correction', () =>
        Test.withContext(async context => {
            const receipt = await context.read(xtremStock.nodes.StockReceipt, { number: 'MISC_RECEIPT32' });
            const correctorReceipt = await context.read(xtremStock.nodes.StockReceipt, { number: 'MISC_RECEIPT23' });

            const mocks = xtremSystem.TestHelpers.Sinon.registerMocks([
                {
                    type: 'stub',
                    reference: xtremStock.functions.notificationLib,
                    name: 'reply',
                    returns: Promise.resolve(/* do nothing */),
                },
            ]);

            const receiptLine = await receipt.lines.elementAt(0);
            const replyStub = mocks[0].mock as sinon.SinonStub;

            const documentId = receipt._id;
            const documentLineId = receiptLine._id;
            const documentLineSortValue = receiptLine._sortValue;

            (context as any)._contextValues.notificationId = 'notification-id';

            const correctorDocumentLine = await correctorReceipt.lines.elementAt(0);

            const correctorDocumentLineId = correctorDocumentLine._id;

            const valueCorrections: Array<
                xtremStockData.interfaces.ValuationCorrectionParameter & {
                    correctorDocumentLine: xtremMasterData.nodes.BaseDocumentLine;
                }
            > = [
                {
                    correctedDocumentLineID: documentLineId,
                    reasonCodeID: 'R1',
                    correctorDocumentLine,
                    impactedQuantity: await receiptLine.quantityInStockUnit,
                    amountToAbsorb: 20,
                    absorbedAmount: 10,
                    nonAbsorbedAmount: 0,
                },
            ];

            const stockReceiptDetails = context.query(xtremStockData.nodes.StockReceiptDetail, {
                filter: { documentLine: documentLineId },
            });
            const stockDetails =
                await xtremStockData.functions.stockDetailLib.createStockCorrectionDetailFromStockDetail(
                    context,
                    stockReceiptDetails,
                    valueCorrections[0],
                );

            logger.verbose(() => `stock details : ${JSON.stringify(stockDetails)}`);

            const lastStockJournalId = (
                await context.query(xtremStockData.nodes.StockJournal, { orderBy: { _id: 1 }, last: 1 }).elementAt(0)
            )._id;

            await xtremStock.nodeExtensions.StockExtension.correct(context, {
                replyTopic: 'fake/reply',
                stockUpdateParameters: { movementType: 'correction' },
                documents: [
                    {
                        id: documentId,
                        lines: [
                            {
                                id: documentLineId,
                                sortValue: await documentLineSortValue,
                            },
                        ],
                    },
                ],
            });

            assert.deepEqual(replyStub.getCall(0).args[1], {
                replyTopic: 'fake/reply',
                stockUpdateParameters: { movementType: 'correction' },
                documents: [
                    {
                        id: documentId,
                        lines: [
                            {
                                id: documentLineId,
                                sortValue: await documentLineSortValue,
                            },
                        ],
                    },
                ],
            });
            assert.deepEqual(replyStub.getCall(0).args[2], {
                batchTrackingId: undefined,
                requestNotificationId: 'notification-id',
                updateResults: {
                    correction: {
                        documents: [
                            {
                                id: documentId,
                                lines: [
                                    {
                                        errorMessage: undefined,
                                        id: documentLineId,
                                        sortValue: await documentLineSortValue,
                                        originLineIds: [correctorDocumentLineId],
                                        stockJournalRecords: [lastStockJournalId + 1, lastStockJournalId + 2],
                                        stockUpdateResultStatus: 'corrected',
                                    },
                                ],
                            },
                        ],
                    },
                },
            });
        }));

    it('should create new stock journal for a price correction - AVG item', () =>
        Test.withContext(
            async context => {
                await testStockValueCorrection(context, {
                    documentLine: 1137,
                    amountToAbsorb: 80,
                    impactedQuantity: 160, // this quantity does not match the quantity of the document line but it allows to test the calculation with non absorbed amount
                    expectedValues: {
                        stockValueVariance: 50,
                        stockJournals: [
                            {
                                orderAmount: 20.6,
                                movementAmount: 20.6,
                                nonAbsorbedAmount: 12,
                                orderCost: 5,
                                valuedCost: 5,
                            },
                            {
                                orderAmount: 30,
                                movementAmount: 30,
                                nonAbsorbedAmount: 18,
                                orderCost: 5,
                                valuedCost: 5,
                            },
                        ],
                    },
                });
            },
            { now: '2022-08-01T10:00:00' },
        ));

    it('should create new stock journal for a price correction - Standard cost item without stock', () =>
        Test.withContext(
            async context => {
                await testStockValueCorrection(context, {
                    documentLine: 1138,
                    impactedQuantity: 100,
                    amountToAbsorb: 1000,
                    expectedValues: {
                        stockValueVariance: 0,
                        stockJournals: [
                            {
                                orderAmount: 1000,
                                movementAmount: 0,
                                nonAbsorbedAmount: 1000,
                                orderCost: 10,
                                valuedCost: 0,
                            },
                        ],
                    },
                });
            },
            { now: '2022-08-01T10:00:00' },
        ));

    it('should create new stock journal for a price correction - Standard cost item with stock', () =>
        Test.withContext(
            async context => {
                await testStockValueCorrection(context, {
                    documentLine: 1139,
                    impactedQuantity: 100,
                    amountToAbsorb: 1235,
                    expectedValues: {
                        stockValueVariance: 0,
                        stockJournals: [
                            {
                                orderAmount: 1235,
                                movementAmount: 0,
                                nonAbsorbedAmount: 1235,
                                orderCost: 12.35,
                                valuedCost: 0,
                            },
                        ],
                    },
                });
            },
            { now: '2022-08-01T10:00:00' },
        ));

    it('should create 1 new stock journal record for a price correction - FIFO cost item', () =>
        Test.withContext(
            async context => {
                await testStockValueCorrection(context, {
                    documentLine: 1148,
                    amountToAbsorb: 50,
                    impactedQuantity: 10,
                    expectedValues: {
                        stockValueVariance: 50,
                        stockJournals: [
                            {
                                orderAmount: 50,
                                movementAmount: 50,
                                nonAbsorbedAmount: 0,
                                orderCost: 5,
                                valuedCost: 5,
                            },
                        ],
                    },
                });
            },
            { today: '2023-04-17' },
        ));

    it('should create several new stock journal records for a price correction - FIFO cost item', () =>
        Test.withContext(
            async context => {
                // The stock receipt has 2 stock details (qty=3 and 7)
                // Only 3 remains in the FIFO stack
                // If the value to absorb is 20, only 3 * 20/10 = 6 can be absorbed and then the non absorbed amount=20-6=14
                // stockjournal.qty=3 -> absorbed amount = 3*6/10 = 1.8 ; non absorbed = 3*14/10 = 4.2
                // stockjournal.qty=7 -> absorbed amount = 7*6/10 = 4.2 ; non absorbed = 7*14/10 = 9.8
                await testStockValueCorrection(context, {
                    documentLine: 1152,
                    impactedQuantity: 10,
                    amountToAbsorb: 20,
                    expectedValues: {
                        stockValueVariance: 6,
                        stockJournals: [
                            {
                                orderAmount: 1.8,
                                movementAmount: 1.8,
                                nonAbsorbedAmount: 4.2,
                                orderCost: 0.6,
                                valuedCost: 0.6,
                            },
                            {
                                orderAmount: 4.2,
                                movementAmount: 4.2,
                                nonAbsorbedAmount: 9.8,
                                orderCost: 0.6,
                                valuedCost: 0.6,
                            },
                        ],
                    },
                });

                // check FIFO tier
                const fifoTier = await context.read(xtremStockData.nodes.FifoValuationTier, {
                    item: '#STOFIFO-MAT2',
                    site: '#US001',
                    effectiveDate: '2023-04-05',
                    sequence: 1,
                });

                assert.deepEqual(+(await fifoTier.amount), 67.5);
                assert.deepEqual(+(await fifoTier.nonAbsorbedAmount), 14.0);
            },
            { today: '2023-04-17' },
        ));

    it('should create several new stock journal records for a limited price correction - FIFO cost item', () =>
        Test.withContext(
            async context => {
                // The stock receipt has 2 stock details (qty=3 and 7)
                // Only 3 remains in the FIFO stack and only 4 are impacted instead of 10
                // If the value to absorb is 20, only 20 * 3/4 = 15 can be absorbed and then the non absorbed amount=20-15=5
                // stockjournal.qty=3 -> absorbed amount = 3/10 * 15 = 4.5 ; non absorbed = 3/10 * 5 = 1.5
                // stockjournal.qty=7 -> absorbed amount = 7/10 * 15 = 10.5 ; non absorbed = 7/10 * 5 = 3.5
                await testStockValueCorrection(context, {
                    documentLine: 1152,
                    amountToAbsorb: 20,
                    impactedQuantity: 4,
                    expectedValues: {
                        stockValueVariance: 15,
                        stockJournals: [
                            {
                                orderAmount: 4.5,
                                movementAmount: 4.5,
                                nonAbsorbedAmount: 1.5,
                                orderCost: 1.5,
                                valuedCost: 1.5,
                            },
                            {
                                orderAmount: 10.5,
                                movementAmount: 10.5,
                                nonAbsorbedAmount: 3.5,
                                orderCost: 1.5,
                                valuedCost: 1.5,
                            },
                        ],
                    },
                });

                // check FIFO tier
                const fifoTier = await context.read(xtremStockData.nodes.FifoValuationTier, {
                    item: '#STOFIFO-MAT2',
                    site: '#US001',
                    effectiveDate: '2023-04-05',
                    sequence: 1,
                });

                assert.deepEqual(+(await fifoTier.amount), 76.5);
                assert.deepEqual(+(await fifoTier.nonAbsorbedAmount), 5.0);
            },
            { today: '2023-04-17' },
        ));

    // XT-32492 Create test data for serial number assignment(s) for stock receipt
    const prepareSerialNumberAssignment = async (
        context: Context,
    ): Promise<xtremStockData.interfaces.StockEngineReturn> => {
        const serialNumber = await context.read(xtremStockData.nodes.SerialNumber, { _id: '#ISSUESERIAL|SSNR0005' });
        const { stockDetails }: StockTestParameters = await getTestSerialParameters(context, '1155');
        // Add a serial number
        await stockDetails[0].$.set({
            stockDetailSerialNumbers: [{ serialNumber, _action: 'create' }],
        });

        // Try to create a stock receipt using the stock engine.
        return (
            await stockEngine.stockReceipt(context, stockDetails as xtremStockData.nodes.StockReceiptDetail[], {
                movementType: 'receipt',
            })
        )[0];
    };

    // XT-32492 Check correct serial number assignment for stock receipt.
    it('Check serial number assignment for stock receipt', () =>
        Test.withContext(
            async context => {
                const stockEngineReturn = await prepareSerialNumberAssignment(context);

                // Check the serial number in the stock journal record.
                const serialNumber = await context.read(xtremStockData.nodes.SerialNumber, {
                    _id: '#ISSUESERIAL|SSNR0005',
                });
                assert.deepEqual(stockEngineReturn.stockRecord, await serialNumber.stockRecord);
                assert.equal(
                    (await (await stockEngineReturn.stockJournalRecord?.serialNumbers.at(0))?.serialNumber)?._id,
                    serialNumber._id,
                );

                // Check flags.
                assert.isTrue(await serialNumber.isUsable);
                assert.isTrue(await serialNumber.isInStock);
            },
            { testActiveServiceOptions: [xtremMasterData.serviceOptions.serialNumberOption] },
        ));

    // XT-32492 Check correct serial number assignment.
    it('Check serial number unassignment on stock record deletion', () =>
        Test.withContext(
            async context => {
                const stockEngineReturn = await prepareSerialNumberAssignment(context);

                // Delete the stock record.
                await stockEngineReturn.stockRecord?.$.delete();
                const serialNumber = await context.read(xtremStockData.nodes.SerialNumber, {
                    _id: '#ISSUESERIAL|SSNR0005',
                });
                assert.isTrue(await serialNumber.isUsable);
                assert.isFalse(await serialNumber.isInStock);
                assert.isNull(await serialNumber.stockRecord);
            },
            { testActiveServiceOptions: [xtremMasterData.serviceOptions.serialNumberOption] },
        ));

    // XT-33346 Check correct serial number for stock issue.
    it('Check serial number assignment for stock issue - issue & receipt', () =>
        Test.withContext(
            async context => {
                const { stockDetails } = await getTestSerialParameters(context);
                const site = await context.read(xtremSystem.nodes.Site, { id: 'US001' });
                const item = await context.read(xtremMasterData.nodes.Item, { id: 'ISSUESERIAL' });
                const status = await context.read(xtremStockData.nodes.StockStatus, { id: 'A1' });
                const location = await context.read(xtremMasterData.nodes.Location, {
                    _id: '#LOC3|US001|Loading dock',
                });

                const stockRecord = await xtremStockData.functions.stockLib.getStockRecord(context, {
                    item,
                    location,
                    site,
                    stockUnit: await item.stockUnit,
                    lot: null,
                    owner: await site.id,
                    status,
                });

                (stockDetails as any).stockReceipt = stockRecord;

                const stockIssueDetail = (
                    await xtremStockData.functions.stockDetailLib.cloneStockDetail(
                        context,
                        stockDetails[0] as xtremStockData.nodes.StockIssueDetail,
                        xtremStockData.nodes.StockIssueDetail,
                        {
                            overrides: {
                                site,
                                item,
                                stockUnit: await item.stockUnit,
                                quantityInStockUnit: -1,
                                stockRecord,
                                movementType: 'issue',
                                status,
                                stockDetailSerialNumbers: [
                                    { serialNumber: '#ISSUESERIAL|SSNR0006', _action: 'create' },
                                ],
                            },
                        },
                    )
                ).newStockDetail;

                // Try to create a stock issue using the stock engine.
                await stockEngine.stockIssue(context, [stockIssueDetail] as xtremStockData.nodes.StockIssueDetail[], {
                    movementType: 'issue',
                });

                // Check the serial number for a null in the stockRecord
                const serialNumber = await context.read(xtremStockData.nodes.SerialNumber, {
                    _id: '#ISSUESERIAL|SSNR0006',
                });
                assert.deepEqual(null, await serialNumber.stockRecord);

                // Check flags.
                assert.isTrue(await serialNumber.isUsable);
                assert.isFalse(await serialNumber.isInStock);
            },
            { testActiveServiceOptions: [xtremMasterData.serviceOptions.serialNumberOption] },
        ));

    // XT-35203 Check correct serial number for stock issue.
    it('Check serial number assignment for stock issue - issue only', () =>
        Test.withContext(
            async context => {
                const { stockDetails } = await getTestSerialParameters(context);
                const site = await context.read(xtremSystem.nodes.Site, { id: 'US001' });
                const item = await context.read(xtremMasterData.nodes.Item, { id: 'ISSUESERIAL' });
                const status = await context.read(xtremStockData.nodes.StockStatus, { id: 'A1' });
                const location = await context.read(xtremMasterData.nodes.Location, {
                    _id: '#LOC3|US001|Loading dock',
                });

                const stockRecord = await xtremStockData.functions.stockLib.getStockRecord(context, {
                    item,
                    location,
                    site,
                    stockUnit: await item.stockUnit,
                    lot: null,
                    owner: await site.id,
                    status,
                });

                const stockIssueDetail = (
                    await xtremStockData.functions.stockDetailLib.cloneStockDetail(
                        context,
                        stockDetails[0] as xtremStockData.nodes.StockIssueDetail,
                        xtremStockData.nodes.StockIssueDetail,
                        {
                            overrides: {
                                site,
                                item,
                                stockUnit: await item.stockUnit,
                                quantityInStockUnit: -1,
                                stockRecord,
                                movementType: 'issue',
                                stockDetailSerialNumbers: [
                                    { serialNumber: '#ISSUESERIAL|SSNR0017', _action: 'create' },
                                ],
                            },
                        },
                    )
                ).newStockDetail;
                await stockIssueDetail.$.save();

                // Check the serial numbers before posting
                let serialNumber = await (await stockIssueDetail.stockDetailSerialNumbers.elementAt(0)).serialNumber;
                assert.isNotNull(serialNumber);
                assert.isTrue(await serialNumber?.isUsable);
                assert.isNotNull(await serialNumber?.stockRecord);
                assert.isTrue(await serialNumber?.isInStock);

                // Posting
                await stockEngine.stockIssue(context, [stockIssueDetail] as xtremStockData.nodes.StockIssueDetail[], {
                    movementType: 'issue',
                });

                // Check serial numbers after posting
                serialNumber = await context.read(xtremStockData.nodes.SerialNumber, { _id: serialNumber?._id });
                assert.isNotNull(serialNumber);
                assert.isTrue(await serialNumber?.isUsable);
                assert.isNull(await serialNumber?.stockRecord);
                assert.isFalse(await serialNumber?.isInStock);
            },
            { testActiveServiceOptions: [xtremMasterData.serviceOptions.serialNumberOption] },
        ));
});

describe('Stock engine - FIFO valuation', () => {
    it('should update the stock FIFO valuation stack for a stock issue', () =>
        Test.withContext(async context => {
            const stockIssue = await context.read(xtremStock.nodes.StockIssue, { _id: '#MISC_ISSUE7' });

            await testLib.functions.TestHelpers.processStockUpdate(
                context,
                stockIssue,
                'issue',
                { movementType: 'issue' },
                xtremStock.nodes.StockIssue.onStockReply,
                [
                    { stockUpdateResultStatus: 'decreased', stockTransactionStatus: 'succeeded' },
                    { stockUpdateResultStatus: 'decreased', stockTransactionStatus: 'succeeded' },
                ],
            );

            let stockJournals = await context
                .query(xtremStockData.nodes.StockJournal, { filter: { documentLine: 1913 } })
                .toArray();
            assert.equal(Number(await stockJournals[0].quantityInStockUnit), -10);
            assert.equal(Number(await stockJournals[0].orderAmount), -1200);

            stockJournals = await context
                .query(xtremStockData.nodes.StockJournal, {
                    filter: { documentLine: 1914 },
                    orderBy: { quantityInStockUnit: -1 },
                })

                .toArray();

            assert.equal(Number(await stockJournals[0].quantityInStockUnit), -2);
            assert.equal(Number(await stockJournals[0].orderAmount), -195.93);
            assert.equal(Number(await stockJournals[0].nonAbsorbedAmount), 0);
            assert.equal(Number(await stockJournals[1].quantityInStockUnit), -25);
            assert.equal(Number(await stockJournals[1].orderAmount), -2449.07);
            assert.equal(Number(await stockJournals[1].nonAbsorbedAmount), 0);

            assert.equal(Number(await stockJournals[0].orderCost), 97.962963);
            assert.equal(Number(await stockJournals[1].orderCost), 97.962963);

            const fifoTiers = await context
                .query(xtremStockData.nodes.FifoValuationTier, {
                    filter: { item: '#STOFIFO', site: '#US001', effectiveDate: { _lte: '2023-03-20' } },
                    orderBy: { effectiveDate: 1, sequence: 1 },
                })
                .toArray();

            const expectedFifoTiers = [
                {
                    effectiveDate: DateValue.make(2023, 3, 16),
                    sequence: 1,
                    receiptQuantity: 12,
                    remainingQuantity: 0,
                    amount: 0,
                },
                {
                    effectiveDate: DateValue.make(2023, 3, 17),
                    sequence: 1,
                    receiptQuantity: 10,
                    remainingQuantity: 0,
                    amount: 0,
                },
                {
                    effectiveDate: DateValue.make(2023, 3, 17),
                    sequence: 2,
                    receiptQuantity: 10,
                    remainingQuantity: 0,
                    amount: 0,
                },
                {
                    effectiveDate: DateValue.make(2023, 3, 20),
                    sequence: 1,
                    receiptQuantity: 20,
                    remainingQuantity: 15,
                    amount: 1515,
                },
            ];

            assert.deepEqual(fifoTiers.length, expectedFifoTiers.length);
            await asyncArray(fifoTiers).forEach(async (fifoTier, index) => {
                assert.deepEqual(await fifoTier.effectiveDate, expectedFifoTiers[index].effectiveDate);
                assert.deepEqual(await fifoTier.sequence, expectedFifoTiers[index].sequence);
                assert.deepEqual(Number(await fifoTier.receiptQuantity), expectedFifoTiers[index].receiptQuantity);
                assert.deepEqual(Number(await fifoTier.remainingQuantity), expectedFifoTiers[index].remainingQuantity);
                assert.deepEqual(Number(await fifoTier.amount), expectedFifoTiers[index].amount);
            });
        }));
});

describe('Stock engine - Stock value change', () => {
    async function testStockValueChange(context: Context, document: xtremStock.nodes.StockValueChange) {
        const documentLine = await document.lines.elementAt(0);
        const documentLineID = documentLine._id;
        const stockDetails = await documentLine.stockDetails.toArray();
        const stockUpdateParameters: xtremStock.interfaces.StockUpdateParametersWithValuationManager<xtremStockData.enums.StockMovementTypeEnum.valueChange> =
            {
                movementType: 'valueChange',
                stockValuationManager:
                    (await xtremStock.functions.notificationLib.getStockValuationManager(
                        context,
                        stockDetails,
                        //      {
                        //     notificationId: 'azerty',
                        //     replyTopic: 'reply',
                        //     id: -1,
                        //     sortValue: 1,
                        //     stockUpdateParameters: {
                        //         movementType: 'valueChange',
                        //     },
                        // }
                    )) ?? undefined,
            };
        await stockEngine.stockValueChange(context, stockDetails, stockUpdateParameters);

        if (stockUpdateParameters.stockValuationManager) {
            await stockUpdateParameters.stockValuationManager.finish(context);
        }

        if ((await document.valuationMethod) === 'averageCost') {
            const itemSite = await context.read(xtremMasterData.nodes.ItemSite, { item: '#STOAVC', site: '#US001' });
            assert.equal((await itemSite.stockValuationAtAverageCost).valueOf(), await documentLine.newAmount);
        }
        if ((await document.valuationMethod) === 'fifoCost') {
            const fifoValuationTier = await stockDetails[0].fifoValuationTier;
            assert.exists(fifoValuationTier);
            if (!fifoValuationTier) return;

            assert.equal((await fifoValuationTier.amount).valueOf(), (await documentLine.newAmount).valueOf());
            assert.equal((await fifoValuationTier.nonAbsorbedAmount).valueOf(), 0);
        }

        const stockJournals = await context
            .query(xtremStockData.nodes.StockJournal, {
                filter: { documentLine: documentLineID },
            })
            .toArray();

        assert.equal(stockJournals.length, 1);
        const variance = (await (await documentLine.stockDetails.elementAt(0)).amount).valueOf();
        if ((await document.valuationMethod) === 'standardCost') {
            assert.equal((await stockJournals[0].movementAmount).valueOf(), variance);
            assert.equal((await stockJournals[0].orderAmount).valueOf(), 0);
            assert.equal((await stockJournals[0].nonAbsorbedAmount).valueOf(), 0);
        } else {
            assert.equal((await stockJournals[0].movementAmount).valueOf(), variance);
            assert.equal((await stockJournals[0].orderAmount).valueOf(), variance);
            assert.equal((await stockJournals[0].nonAbsorbedAmount).valueOf(), 0);
        }
        assert.equal((await stockJournals[0].quantityInStockUnit).valueOf(), 0);
        assert.isNull(await stockJournals[0].status);
        assert.isNull(await stockJournals[0].lot);
        assert.isNull(await stockJournals[0].location);
    }

    it('change stock value at standard cost', () =>
        Test.withContext(async context => {
            const variance = 45.67;
            const itemSite = await context.read(xtremMasterData.nodes.ItemSite, { item: '#STOSTD', site: '#US001' });
            const oldAmount = 1000;
            const quantity = 10;

            const document = await testLib.functions.TestHelpers.createStockValueChangeDocument(context, {
                itemSite,
                lineAmounts: [
                    {
                        oldAmount,
                        newAmount: oldAmount + variance,
                        quantity,
                    },
                ],
            });
            await testStockValueChange(context, document);
        }));

    it('change stock value at average cost', () =>
        Test.withContext(async context => {
            const variance = 45.67;
            const itemSite = await context.read(xtremMasterData.nodes.ItemSite, { item: '#STOAVC', site: '#US001' });
            const oldAmount = await itemSite.stockValuationAtAverageCost;
            const quantity = await itemSite.inStockQuantity;

            const document = await testLib.functions.TestHelpers.createStockValueChangeDocument(context, {
                itemSite,
                lineAmounts: [
                    {
                        oldAmount,
                        newAmount: oldAmount + variance,
                        quantity,
                    },
                ],
            });
            await testStockValueChange(context, document);
        }));

    it('change stock value at FIFO cost', () =>
        Test.withContext(async context => {
            const document = await testLib.functions.TestHelpers.createStockValueChangeDocument(context, {
                item: '#STOFIFO-MAT1',
                site: '#US001',
                lineAmounts: [
                    {
                        oldAmount: 840,
                        newAmount: 832.98,
                        quantity: 8,
                        fifoTier: await context.read(xtremStockData.nodes.FifoValuationTier, { _id: 115100 }),
                    },
                ],
            });

            await testStockValueChange(context, document);
        }));
});
