import { Context, Test } from '@sage/xtrem-core';
import { date } from '@sage/xtrem-date-time';
import * as xtremStockData from '@sage/xtrem-stock-data';
import * as xtremSystem from '@sage/xtrem-system';
import { assert } from 'chai';
import { startCase } from 'lodash';
import { afterEach, beforeEach } from 'mocha';
import * as Sinon from 'sinon';
import * as xtremStock from '../../../lib';
import * as fakePackage from '../../fixtures/lib';

describe('allocation engine', () => {
    beforeEach(() => {
        (xtremStock.classes.AllocationEngine as any)._instance = null;
    });
    describe('getInstance()', () => {
        it('should construct the singleton when called the first time', () => {
            const allocationGetInstanceSpy = Sinon.spy(xtremStock.classes.AllocationEngine, 'getInstance');

            const instanceBefore = (xtremStock.classes.AllocationEngine as any)._instance;
            const instanceAfter = xtremStock.classes.AllocationEngine.getInstance();

            Sinon.assert.calledOnce(allocationGetInstanceSpy);
            assert.isNull(instanceBefore);
            assert.isNotNull(instanceAfter);

            allocationGetInstanceSpy.restore();
        });

        it('should re-use the singleton instance when called the second time', () => {
            xtremStock.classes.AllocationEngine.getInstance();

            const allocationGetInstanceSpy = Sinon.spy(xtremStock.classes.AllocationEngine, 'getInstance');

            const instanceBefore = (xtremStock.classes.AllocationEngine as any)._instance;
            const instanceAfter = xtremStock.classes.AllocationEngine.getInstance();

            Sinon.assert.calledOnce(allocationGetInstanceSpy);
            assert.isNotNull(instanceBefore);
            assert.isNotNull(instanceAfter);
            assert.equal(instanceBefore, instanceAfter);

            allocationGetInstanceSpy.restore();
        });
    });

    describe('allocations', () => {
        let engine: xtremStock.classes.AllocationEngine;
        let fakeNotificationPayload: xtremStockData.interfaces.AutomaticAllocationPayload<xtremStockData.enums.AllocationProcessTypeEnum>;
        const fakeContext = {} as unknown as Context;

        beforeEach(() => {
            engine = xtremStock.classes.AllocationEngine.getInstance();
            fakeNotificationPayload = {
                documentType: 'salesOrder',
                originRequest: 'TEST',
                processId: 'TEST',
                requestType: 'allocation',
                processType: 'document',
                replyTopic: 'SalesOrder/allocation/reply',
            };
        });

        describe('allocate()', () => {
            it('should call the processAllocationQueue with the payload', async () => {
                const mocks = xtremSystem.TestHelpers.Sinon.registerMocks([
                    {
                        type: 'stub',
                        isMethod: true,
                        class: xtremStock.classes.AllocationEngine,
                        name: '_processAllocationQueue',
                        returns: 'test',
                    },
                ]);
                const queueProcessorStub = mocks[0].mock as Sinon.SinonStub;

                await engine.allocate(fakeContext, fakeNotificationPayload);

                Sinon.assert.calledOnceWithExactly(queueProcessorStub, fakeContext, fakeNotificationPayload);

                xtremSystem.TestHelpers.Sinon.removeMocks();
            });
        });

        describe('_processAllocationQueue()', () => {
            it('should query the AllocationQueue records and allocate in case of an allocation request', () => {
                return Test.withContext(async context => {
                    // Create a document with 5 lines
                    const document = await fakePackage.functions.TestHelpers.createFakeIssueDocument(
                        context,
                        'FakeIssueDoc01',
                        5,
                    );

                    // Create the 5 corresponding allocationQueue records
                    const queueLines = document.lines.map(line => {
                        return fakePackage.functions.TestHelpers.createAllocationQueueRecord(
                            context,
                            fakeNotificationPayload.processId,
                            'allocation',
                            line,
                        );
                    });

                    // Create the allocationResult and 5 corresponding allocationResultLine records
                    await fakePackage.functions.TestHelpers.createAllocationResultRecordFromQueue(
                        context,
                        'document',
                        queueLines,
                    );

                    const expectedQuantityAllocated = 3;

                    const fakeAllocationProcessorResponseObject = {
                        resultAction: 'allocated',
                        allocationRecord: {
                            quantityInStockUnit: Promise.resolve(expectedQuantityAllocated),
                        },
                    };

                    const mocks = xtremSystem.TestHelpers.Sinon.registerMocks([
                        {
                            type: 'stub',
                            isMethod: true,
                            class: xtremStock.classes.AllocationEngine,
                            name: '_allocationQueueProcessor',
                            returns: Promise.resolve({
                                updates: [fakeAllocationProcessorResponseObject],
                                quantityAffected: expectedQuantityAllocated,
                            }),
                        },
                        {
                            type: 'spy',
                            isMethod: true,
                            class: Context,
                            name: 'query',
                        },
                        {
                            type: 'stub',
                            isMethod: true,
                            class: xtremStock.classes.AllocationEngine,
                            name: 'reply',
                        },
                    ]);
                    const allocationProcessorStub = mocks[0].mock as Sinon.SinonStub;
                    const querySpy = mocks[1].mock as Sinon.SinonStub;
                    const replyStub = mocks[2].mock as Sinon.SinonStub;

                    // Using as any here to allow call of the private method.
                    await (engine as any)._processAllocationQueue(context, fakeNotificationPayload);

                    Sinon.assert.called(querySpy);
                    Sinon.assert.calledWith(querySpy.firstCall, xtremStockData.nodes.AllocationQueue, {
                        filter: {
                            processId: { _eq: fakeNotificationPayload.processId },
                            requestType: { _eq: 'allocation' },
                        },
                    });
                    Sinon.assert.calledWith(querySpy.secondCall, xtremStockData.nodes.AllocationResultLine, {
                        first: 1,
                        filter: {
                            result: {
                                processId: fakeNotificationPayload.processId,
                            },
                            documentLine: {
                                _id: (await document.lines.elementAt(0))._id,
                            },
                        },
                        forUpdate: true,
                    });

                    Sinon.assert.called(allocationProcessorStub);

                    const allocationProcessorStubFirstCallsArgs: Parameters<
                        xtremStock.classes.AllocationEngine['_allocationQueueProcessor']
                    >[1] = allocationProcessorStub.firstCall.args[1];
                    const firstQueueLine = await queueLines.elementAt(0);

                    assert.equal(
                        (await allocationProcessorStubFirstCallsArgs.queueLine.documentLine)._id,
                        (await firstQueueLine.documentLine)._id,
                    );
                    assert.equal(
                        await allocationProcessorStubFirstCallsArgs.queueLine.processId,
                        await firstQueueLine.processId,
                    );

                    Sinon.assert.calledOnce(replyStub);
                    assert.equal(
                        replyStub.firstCall.args[1].replyTopic,
                        `${startCase(fakeNotificationPayload.documentType).replaceAll(' ', '')}/allocation/reply`,
                    );

                    xtremSystem.TestHelpers.Sinon.removeMocks();
                });
            });
        });

        describe('_allocationQueueProcessor', () => {
            it('Should automatically allocate', () => {
                return Test.withContext(
                    async context => {
                        // Create a document with 1 line
                        const document = await fakePackage.functions.TestHelpers.createFakeIssueDocument(
                            context,
                            'FakeIssueDoc01',
                            1,
                        );

                        // Create the corresponding allocationQueue records
                        const queueLines = document.lines.map(line => {
                            return fakePackage.functions.TestHelpers.createAllocationQueueRecord(
                                context,
                                fakeNotificationPayload.processId,
                                'allocation',
                                line,
                            );
                        });

                        // Create the allocationResult and corresponding allocationResultLine records
                        await fakePackage.functions.TestHelpers.createAllocationResultRecordFromQueue(
                            context,
                            'document',
                            queueLines,
                        );

                        const mocks = xtremSystem.TestHelpers.Sinon.registerMocks([
                            {
                                type: 'spy',
                                reference: xtremStockData.functions.stockLib,
                                name: 'searchStock',
                            },
                            {
                                type: 'stub',
                                reference: xtremStockData.functions.allocationLib,
                                name: 'updateAllocations',
                            },
                        ]);
                        const searchStockSpy = mocks[0].mock as Sinon.SinonStub;
                        const updateAllocationsStub = mocks[1].mock as Sinon.SinonStub;

                        const queueLine = await queueLines.elementAt(0);

                        const stockSearchCriteria = {
                            item: await queueLine.item,
                            site: await queueLine.stockSite,
                            status: { type: 'accepted' },
                        };

                        // Using as any here to allow call of the private method.
                        await (
                            (engine as any)
                                ._allocationQueueProcessor as xtremStock.classes.AllocationEngine['_allocationQueueProcessor']
                        )(context, {
                            processType: fakeNotificationPayload.processType,
                            queueLine,
                            originRequest: fakeNotificationPayload.originRequest,
                        });

                        Sinon.assert.calledOnceWithExactly(searchStockSpy, context, {
                            item: await queueLine.item,
                            site: await queueLine.stockSite,
                            activeQuantityInStockUnit: await queueLine.quantityToProcess,
                            stockCharacteristics: [{ statusList: [{ statusType: 'accepted' }] }],
                        });

                        Sinon.assert.calledOnce(updateAllocationsStub);
                        assert.equal(
                            (await updateAllocationsStub.firstCall.args[1].documentLine)._id,
                            (await queueLine.documentLine)._id,
                        );
                        assert.equal(
                            (await updateAllocationsStub.firstCall.args[1].allocationUpdates[0].stockRecord)._id,
                            (
                                await context
                                    .query(xtremStockData.nodes.Stock, { filter: stockSearchCriteria, first: 1 })
                                    .elementAt(0)
                            )._id,
                        );

                        xtremSystem.TestHelpers.Sinon.removeMocks();
                    },
                    { today: '2021-01-31' },
                );
            });
        });

        describe('_deallocationQueueProcessor', () => {
            it('Should automatically deallocate', () => {
                return Test.withContext(
                    async context => {
                        // Create a document with 1 line
                        const document = await fakePackage.functions.TestHelpers.createFakeIssueDocument(
                            context,
                            'FakeIssueDoc01',
                            1,
                        );

                        // Create the allocation for this document
                        await (
                            await context.create(xtremStockData.nodes.StockAllocation, {
                                stockRecord: 46,
                                quantityInStockUnit: 3,
                                documentLine: await document.lines.elementAt(0),
                            })
                        ).$.save();

                        // Create the corresponding allocationQueue records
                        const queueLines = document.lines.map(line => {
                            return fakePackage.functions.TestHelpers.createAllocationQueueRecord(
                                context,
                                fakeNotificationPayload.processId,
                                'deallocation',
                                line,
                            );
                        });

                        // Create the allocationResult and corresponding allocationResultLine records
                        await fakePackage.functions.TestHelpers.createAllocationResultRecordFromQueue(
                            context,
                            'document',
                            queueLines,
                        );

                        const mocks = xtremSystem.TestHelpers.Sinon.registerMocks([
                            {
                                type: 'stub',
                                reference: xtremStockData.functions.allocationLib,
                                name: 'updateAllocations',
                            },
                        ]);
                        const updateAllocationsStub = mocks[0].mock as Sinon.SinonStub;

                        const queueLine = await queueLines.elementAt(0);

                        // Using as any here to allow call of the private method.
                        await (
                            (engine as any)
                                ._deallocationQueueProcessor as xtremStock.classes.AllocationEngine['_deallocationQueueProcessor']
                        )(context, {
                            processType: fakeNotificationPayload.processType,
                            queueLine,
                            originRequest: fakeNotificationPayload.originRequest,
                        });

                        Sinon.assert.calledOnce(updateAllocationsStub);
                        assert.equal(
                            (await updateAllocationsStub.firstCall.args[1].documentLine)._id,
                            (await queueLine.documentLine)._id,
                        );
                        assert.equal(
                            await (
                                await (
                                    await (
                                        await updateAllocationsStub.firstCall.args[1].allocationUpdates[0]
                                    ).allocationRecord
                                ).stockRecord
                            )._id,
                            46,
                        );

                        xtremSystem.TestHelpers.Sinon.removeMocks();
                    },
                    { today: '2021-01-31' },
                );
            });
            it('Should automatically FEFO deallocate', () => {
                return Test.withContext(
                    async context => {
                        // Create a document with 1 line
                        const document = await fakePackage.functions.TestHelpers.createFakeIssueDocument(
                            context,
                            'FakeIssueDoc01',
                            1,
                            undefined,
                            66,
                        );

                        /* Create the allocations in this order:
                         stockRecord    | lot           | creation date | expiration date   | quantity
                          38            | LOT202102002  | 2021-01-11    | 2021-03-31        | 11
                          46            | LOT202103002  | 2021-01-22    | 2022-03-31        | 22
                          37            | LOT202102001  | 2021-01-22    | 2021-03-31        | 33

                          The deallocation must use this order: 46 (because of expiration date) -> 37 (because of creation date) -> 38
                          By reducing the allocation by 27, the allocation corresponding to stockRecord 46 must be deleted
                          and the allocation corresponding to stockRecord 37 must be reduced by 5
                        */
                        let stockRecord = await context.read(xtremStockData.nodes.Stock, { _id: 38 });
                        let lot = await stockRecord.lot;
                        assert.isNotNull(lot);
                        if (!lot) return;
                        assert.deepEqual(await lot.id, 'LOT202102002');
                        assert.deepEqual(await lot.creationDate, date.make(2021, 1, 11));
                        assert.deepEqual(await lot.expirationDate, date.make(2021, 3, 31));

                        stockRecord = await context.read(xtremStockData.nodes.Stock, { _id: 46 });
                        lot = await stockRecord.lot;
                        assert.isNotNull(lot);
                        if (!lot) return;
                        assert.deepEqual(await lot.id, 'LOT202103002');
                        assert.deepEqual(await lot.creationDate, date.make(2021, 1, 22));
                        assert.deepEqual(await lot.expirationDate, date.make(2022, 3, 31));

                        stockRecord = await context.read(xtremStockData.nodes.Stock, { _id: 37 });
                        lot = await stockRecord.lot;
                        assert.isNotNull(lot);
                        if (!lot) return;
                        assert.deepEqual(await lot.id, 'LOT202102001');
                        assert.deepEqual(await lot.creationDate, date.make(2021, 1, 22));
                        assert.deepEqual(await lot.expirationDate, date.make(2021, 3, 31));

                        await (
                            await context.create(xtremStockData.nodes.StockAllocation, {
                                stockRecord: 38,
                                quantityInStockUnit: 11,
                                documentLine: await document.lines.elementAt(0),
                            })
                        ).$.save();
                        await (
                            await context.create(xtremStockData.nodes.StockAllocation, {
                                stockRecord: 46,
                                quantityInStockUnit: 22,
                                documentLine: await document.lines.elementAt(0),
                            })
                        ).$.save();
                        await (
                            await context.create(xtremStockData.nodes.StockAllocation, {
                                stockRecord: 37,
                                quantityInStockUnit: 33,
                                documentLine: await document.lines.elementAt(0),
                            })
                        ).$.save();

                        // Create the corresponding allocationQueue records
                        const queueLines = document.lines.map(line => {
                            return fakePackage.functions.TestHelpers.createAllocationQueueRecord(
                                context,
                                fakeNotificationPayload.processId,
                                'deallocation',
                                line,
                                27,
                            );
                        });

                        // Create the allocationResult and corresponding allocationResultLine records
                        await fakePackage.functions.TestHelpers.createAllocationResultRecordFromQueue(
                            context,
                            'document',
                            queueLines,
                        );

                        const mocks = xtremSystem.TestHelpers.Sinon.registerMocks([
                            {
                                type: 'stub',
                                isStatic: true,
                                reference: xtremStockData.functions.allocationLib,
                                name: 'updateAllocations',
                            },
                        ]);
                        const updateAllocationsStub = mocks[0].mock as Sinon.SinonStub;

                        const queueLine = await queueLines.elementAt(0);

                        // Using as any here to allow call of the private method.
                        await (
                            (engine as any)
                                ._deallocationQueueProcessor as xtremStock.classes.AllocationEngine['_deallocationQueueProcessor']
                        )(context, {
                            processType: fakeNotificationPayload.processType,
                            queueLine,
                            originRequest: fakeNotificationPayload.originRequest,
                        });

                        Sinon.assert.calledOnce(updateAllocationsStub);
                        assert.equal(
                            (await updateAllocationsStub.firstCall.args[1].documentLine)._id,
                            (await queueLine.documentLine)._id,
                        );
                        const allocationData = await updateAllocationsStub.firstCall.args[1];
                        let allocationUpdate = await allocationData.allocationUpdates[0];
                        let allocationRecord = await allocationUpdate.allocationRecord;
                        assert.equal(await allocationUpdate.action, 'delete');
                        assert.equal(await (await allocationRecord.stockRecord)._id, 46);

                        allocationUpdate = await allocationData.allocationUpdates[1];
                        allocationRecord = await allocationUpdate.allocationRecord;
                        assert.equal(await allocationUpdate.action, 'decrease');
                        assert.equal(await allocationUpdate.quantity, 5);
                        assert.equal(await (await allocationRecord.stockRecord)._id, 37);

                        xtremSystem.TestHelpers.Sinon.removeMocks();
                    },
                    { today: '2021-01-31' },
                );
            });
        });
    });
    afterEach(() => {
        xtremSystem.TestHelpers.Sinon.removeMocks();
    });
});
