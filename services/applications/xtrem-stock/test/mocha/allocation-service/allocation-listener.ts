import { Test } from '@sage/xtrem-core';
import { functions as xtremMasterDataFunctions } from '@sage/xtrem-master-data';
import type * as xtremStockData from '@sage/xtrem-stock-data';
import * as xtremSystem from '@sage/xtrem-system';
import { afterEach, beforeEach } from 'mocha';
import * as Sinon from 'sinon';
import * as xtremStock from '../../../lib';

describe('allocation listener', () => {
    const fakeNotificationPayload: xtremStockData.interfaces.AutomaticAllocationPayload<xtremStockData.enums.AllocationProcessTypeEnum> =
        {
            documentType: 'salesOrder',
            originRequest: 'TEST',
            processId: 'TEST',
            requestType: 'allocation',
            processType: 'document',
            replyTopic: 'FakeReplyTopic',
            stockAllocationParameters: undefined,
        };

    let allocationEngineAllocateStub: Sinon.SinonStub;

    beforeEach(() => {
        const mocks = xtremSystem.TestHelpers.Sinon.registerMocks([
            {
                type: 'stub',
                isMethod: true,
                isStatic: true,
                class: xtremStock.classes.AllocationEngine,
                name: 'getInstance',
            },
            {
                type: 'stub',
                isMethod: true,
                class: xtremStock.classes.AllocationEngine,
                name: 'allocate',
            },
        ]);
        allocationEngineAllocateStub = mocks[1].mock as Sinon.SinonStub;
    });

    describe('onAllocationRequest()', () => {
        it('should call the allocationHandler with the param received and the logger', () => {
            return Test.withContext(async context => {
                await xtremMasterDataFunctions.testLib.simulateBatchContext(context);

                await xtremStock.nodes.AllocationListener.allocate(context, fakeNotificationPayload);

                Sinon.assert.calledOnce(allocationEngineAllocateStub);
                Sinon.assert.calledWith(allocationEngineAllocateStub, context, fakeNotificationPayload);
            });
        });
    });

    afterEach(() => {
        xtremSystem.TestHelpers.Sinon.removeMocks();
    });
});
