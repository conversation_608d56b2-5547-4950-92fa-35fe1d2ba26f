@xtrem_stock
Feature: smoke-test-pr-cd-stock-adjustment

    Scenario: Create Stock adjustment
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-stock/StockAdjustment"
        Then the "Stock adjustments" titled page is displayed
        #Click Create business action
        When the user clicks the "Create" labelled business action button on the navigation panel
        #Select site and reason on reference field
        And the user selects the "site" labelled reference field on the main page
        And the user writes "Atlanta" in the reference field
        And the user selects "Chem. Atlanta" in the reference field
        And the user selects the "adjustment reason" labelled reference field on the main page
        And the user writes "Decrease quantity" in the reference field
        And the user selects "Decrease quantity" in the reference field
        #Adding a line to grid
        Then the user selects the "lines" bound table field on the main page
        And the user adds a new table row to the table field using the sidebar
        #Select item on sidebar
        And the user selects the "Item" labelled reference field on the sidebar
        And the user writes "Chair" in the reference field
        And the user selects "Chair" in the reference field
        And the user selects the "adjustment quantity" labelled numeric field on the sidebar
        And the user writes "10" in the numeric field
        And the user clicks the "Apply" button of the dialog on the sidebar
        And the user waits 4 seconds
        #Click Save Crud Button
        And the user clicks the "Save" labelled business action button on the main page
        And the user selects the "Number" labelled text field on the main page
        And the user stores the value of the text field with the key "[ENV_SINUM]"
        #Verify Creation
        Then a toast containing text "Record created" is displayed

    Scenario: Stock adjustment delete
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-stock/StockAdjustment"
        Then the "Stock adjustments" titled page is displayed
        When the user selects the "stockAdjustments" labelled table field on the main page
        And the user selects the row with text "[ENV_SINUM]" in the "Number" labelled column header of the table field
        And the user clicks the "Number" labelled nested field of the selected row in the table field
        #Then the "Stock receipt SR-Test" titled page is displayed
        #Click Delete Crud Button
        When the user clicks the "Delete" labelled more actions button in the header
        And the user clicks the "Delete" button of the Confirm dialog
        #Verify Deletion
        Then a toast containing text "Record deleted" is displayed
