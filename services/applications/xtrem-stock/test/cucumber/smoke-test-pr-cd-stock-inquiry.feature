@xtrem_stock
Feature: smoke-test-pr-cd-stock-inquiry

    Scenario: Search Stock inquiry
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-stock/StockInquiry"
        #No navigation panel full width
        Then the "Stock inquiry" titled page is displayed
        #Fill in the criteria
        When the user selects the "site" labelled reference field on the main page
        And the user writes "Chem. Atlanta" in the reference field
        And the user selects "Chem. Atlanta" in the reference field
        And the user selects the "From item" labelled reference field on the main page
        And the user writes "Chair" in the reference field
        And the user selects "Chair" in the reference field
        And the user selects the "To item" labelled reference field on the main page
        And the user writes "<PERSON>" in the reference field
        And the user selects "<PERSON>" in the reference field
        # Click the search button
        And the user clicks in the "searchButton" bound button field on the main page
        And the user selects the "stock" bound table field on the main page
        And the user selects the row 1 of the table field
        Then the value of the "quantity" labelled nested numeric field of the selected row in the table field is "4.00"
