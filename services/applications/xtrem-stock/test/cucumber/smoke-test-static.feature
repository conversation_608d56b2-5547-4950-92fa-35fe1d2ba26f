@xtrem_inventory
Feature: smoke-test-static

    #Case without navigation panel full width
    Scenario Outline: sts \ <Page> \ desktop \ check each page loads without any problems
        Given the user opens the application on a desktop using the following link: "<Page>"
        Then the "<Title>" titled page is displayed

        Examples:
            | Page                                           | Title                           |
            | @sage/xtrem-stock/StockValuation               | Stock valuation                 |
            | @sage/xtrem-stock/StockAvcJustificationInquiry | Average unit cost justification |
            | @sage/xtrem-stock/MrpSynchronization           | MRP synchronization             |
            | @sage/xtrem-stock/StockDetailedInquiry         | Stock detailed inquiry          |
            | @sage/xtrem-stock/MrpCalculation               | MRP calculation results         |
            | @sage/xtrem-stock/StockJournalInquiry          | Stock journal inquiry           |
            | @sage/xtrem-stock/StockFifoValuationInquiry    | FIFO cost tier                  |
            | @sage/xtrem-stock/StockInquiryBound            | Stock inquiry                   |
            | @sage/xtrem-stock/StockReorderCalculation      | Stock reordering                |
            | @sage/xtrem-stock/StockCount                   | Stock counts                    |
            | @sage/xtrem-stock/StockCountCreation           | Stock count creation            |
            | @sage/xtrem-stock/StandardCostCalculation      | Standard cost calculations      |
            | @sage/xtrem-stock/StockAvcJustificationInquiry | Average unit cost justification |
            | @sage/xtrem-stock/StockValueChange             | Stock value changes             |
            | @sage/xtrem-stock/StockIssue                   | Stock issues                    |
            | @sage/xtrem-stock/StockAdjustment              | Stock adjustments               |
            | @sage/xtrem-stock/StockReceipt                 | Stock receipts                  |
            | @sage/xtrem-stock/SerialNumberInquiry          | Serial number stock inquiry     |

    #Case with navigation panel full width
    Scenario Outline: sts \ <Page> \ desktop \ check each page loads without any problems
        Given the user opens the application on a desktop using the following link: "<Page>"
        Then the "<NavigationPanelTitle>" titled page is displayed

        #Click Create business action
        When the user clicks the "Create" labelled business action button on the navigation panel
        Then the "<Title>" titled page is displayed

        Examples:
            | Page                             | NavigationPanelTitle | Title        |
            | @sage/xtrem-stock/StockChange    | Stock changes        | Stock change |
            # Opening pages having extensions in the current package
            | @sage/xtrem-master-data/ItemSite | Item-sites           | Item-site    |


    # Positive test to ensure that the error dialog is definitely exist and open
    Scenario: sts \ xtrem-stock \ desktop \ check an error dialog appears when the page does not load
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/Wazup"
        Then an error dialog appears on the main page
