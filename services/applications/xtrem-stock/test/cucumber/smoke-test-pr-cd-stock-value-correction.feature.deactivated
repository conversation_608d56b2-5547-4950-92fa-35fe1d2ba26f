@xtrem_stock
Feature: smoke-test-pr-cd-stock-value-correction

    Scenario: Create Stock value correction
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-stock/StockValueCorrection"

        #Click Create business action
        When the user selects the "Create" labelled business action button on the navigation panel

        Then the "STOCK VALUE CORRECTION" subtitled page is displayed
        #Click Add Crud <PERSON>ton
        When the user clicks on the create CRUD button on the main page
        #Fill in a text field
        And the user selects the "number" labelled text field on the main page
        And the user writes "SMOKETEST1" in the text field
        And the user selects the "site" labelled reference field on the main page
        And the user writes "Chem. Atlanta" in the reference field
        And the user selects "Chem. Atlanta" in the reference field
        And the user selects the "correction reason" labelled reference field on the main page
        And the user writes "Stock value correction" in the reference field
        And the user selects "Stock value correction" in the reference field
        When the user selects the "document type" labelled dropdown-list field on the main page
        And the user clicks in the dropdown-list field
        And the user selects "Stock receipt" in the dropdown-list field
        # Add a record to the table
        And the user selects the "lines" bound table field on the main page
        And the user clicks the "addAdjustLine" bound action of the table field
        And the user selects the "lines" bound table field on the main page
        And the user writes "Chair" in the "item" labelled nested reference field of row 1 in the table field
        And the user selects "Chair" in the "item" labelled nested field of row 1 in the table field
        And the user writes "MISC_RECEIPT13" in the "document line" labelled nested reference field of row 1 in the table field
        And the user selects "MISC_RECEIPT13" in the "document line" labelled nested field of row 1 in the table field
        And the user writes "255" in the "new order cost" labelled nested numeric field of row 1 in the table field
        #Click Save Crud Button
        And the user clicks on the save CRUD button on the main page
        #Verify Creation
        And a toast containing text "Record created" is displayed

    Scenario: Stock value correction delete
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-stock/StockValueCorrection"
        Then the "Stock value corrections" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        When the user filters the "number" bound column in the table field with value "SMOKETEST1"
        And the user clicks the "Number" labelled nested field of row 1 in the table field
        Then the "STOCK VALUE CORRECTION" subtitled page is displayed
        Then the "SMOKETEST1" titled page is displayed
        #Click Delete Crud Button
        And the user clicks on the delete CRUD button on the main page
        And the user clicks the "Delete" button of the Confirm dialog
        #Verify Deletion
        And a toast containing text "Record deleted" is displayed
