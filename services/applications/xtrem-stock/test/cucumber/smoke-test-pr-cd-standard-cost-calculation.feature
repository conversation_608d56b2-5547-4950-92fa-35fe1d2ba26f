@xtrem_stock
@testPurpose
# This feature is using testUser static parameter defined in parameters-atp file
# Make sure parameters-atp file exist and add required value for testUser parameter depending of the environment
Feature: smoke-test-pr-cd-standard-cost-calculation

    Scenario: Open StandardCostCalculation
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-stock/StandardCostCalculation"
        Then the "Standard cost calculations" titled page is displayed

        #Check header status
        #Change value of testUser parameter according to the environment in parameters-atp file
        # with localhost: testUser=Test,Unit
        When the user selects the "Calculation user" labelled reference field on the main page
        Then the value of the reference field is "param:testUser"

        #Select site on reference field
        When the user selects the "Site" labelled reference field on the main page
        And the user writes "Chem. Atlanta" in the reference field
        And the user selects "Chem. Atlanta" in the reference field
        #Fill in items field
        And the user selects the "Items" labelled multi reference field on the main page
        And the user writes "Cost C" in the multi reference field
        And the user selects "Cost C" in the multi reference field

        #Fill in date field
        And the user selects the "Start date" labelled date field on the main page
        #Check if a date is already present in the date field

        #Fill in quantity
        And the user selects the "Quantity" labelled numeric field on the main page
        And the user writes "1" in the numeric field
