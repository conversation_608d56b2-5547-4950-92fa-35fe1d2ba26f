@xtrem_stock
Feature: smoke-test-data

    #Case without navigation panel full width
    Scenario Outline: std \ <Page> \ desktop \ check each page loads without any problems
        Given the user opens the application on a desktop using the following link: "<Page>"
        Then the "<Title>" titled page is displayed
        Examples:
            | Page                                                  | Title                               |
            | @sage/xtrem-stock/StockChange/eyJfaWQiOiIxIn0=        | Stock change CHANGE_STOCK1          |
            # Opening pages having extensions in the current package
            | @sage/xtrem-master-data/ItemSite/eyJfaWQiOiIxMDAifQ== | Item-site Item LOT_TEST2-Site US003 |
