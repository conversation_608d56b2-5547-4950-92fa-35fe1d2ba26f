import type { Context } from '@sage/xtrem-core';
import { asyncArray } from '@sage/xtrem-core';
import { LogicError } from '@sage/xtrem-shared';
import * as xtremStockData from '@sage/xtrem-stock-data';
import * as _ from 'lodash';
import type * as xtremStock from '..';
import { StockValuationManager } from './stock-valuation-manager';

export class StockStandardCostValuationManager extends StockValuationManager {
    static async getStandardCostInstance(
        context: Context,
        createInstanceParameter: xtremStock.interfaces.StockValuationManagerInstanceParameter,
        valuationParameter: xtremStockData.interfaces.ValuationParameter,
    ): Promise<StockStandardCostValuationManager> {
        const instance = new StockStandardCostValuationManager(context, createInstanceParameter, valuationParameter);

        await instance.initialize();
        return instance;
    }

    protected constructor(
        context: Context,
        createInstanceParameter: xtremStock.interfaces.StockValuationManagerInstanceParameter,
        valuationParameter: xtremStockData.interfaces.ValuationParameter,
    ) {
        super(context, createInstanceParameter, valuationParameter);
    }

    protected override async initialize() {
        await super.initialize();

        if (this.action === 'correctValue') {
            await this.initializeCorrection();
            return;
        }

        // For now we only accept 'valueChange'.
        if (this.valuationParameter.valuationType === 'valueChange') {
            await this.initializeForValueChange();
        }
    }

    finish(writableContext: Context): Promise<void> {
        this.context = writableContext;
        // For now nothing must be done.
        return Promise.resolve(_.noop(this));
    }

    private async initializeForValueChange() {
        if (!(this.stockDetails[0] instanceof xtremStockData.nodes.StockValueDetail)) {
            throw new LogicError(
                `stockDetails is not an array of instances of StockValueDetail ${JSON.stringify(
                    this.stockDetails,
                    null,
                    4,
                )}`,
            );
        }

        // Note: There is only one StockValueDetail per stock value change line.
        const stockDetail: xtremStockData.nodes.StockValueDetail = this.stockDetails[0];

        this.setStockValuationGroupAmounts((await stockDetail.documentLine)._id, stockDetail._id, {
            // For standard cost, the ordered amount is 0
            orderAmount: 0,

            // The valued amount in this context is the difference between the new stock value (qty in stock * new standard cost)
            // and the old stock value (qty in stock * old standard cost)
            valuedAmount: await stockDetail.amount,

            // No non-absorbed amount.
            nonAbsorbedAmount: 0,
        });
    }

    protected async initializeCorrection() {
        if (this.valuationParameter.valuationType !== 'receipt') {
            throw new LogicError('Price correction is only managed for receipts');
        }

        if (!(this.stockDetails[0] instanceof xtremStockData.nodes.StockCorrectionDetail)) {
            throw new LogicError(
                `stockDetails is not an instance of StockCorrectionDetail ${JSON.stringify(
                    this.stockDetails,
                    null,
                    4,
                )}`,
            );
        }

        // Initialize amounts to absorb by group
        await asyncArray(this.stockDetails).forEach(
            async (stockCorrectionDetail: xtremStockData.nodes.StockCorrectionDetail) => {
                const documentLine = await stockCorrectionDetail.documentLine;
                const index = this.getGroupIndex(documentLine._id, stockCorrectionDetail._id);

                this.stockValuationGroups[index].orderAmount += await stockCorrectionDetail.amountToAbsorb;
                this.stockValuationGroups[index].valuedAmount = 0;
                this.stockValuationGroups[index].nonAbsorbedAmount += await stockCorrectionDetail.amountToAbsorb;
            },
        );
    }
}
