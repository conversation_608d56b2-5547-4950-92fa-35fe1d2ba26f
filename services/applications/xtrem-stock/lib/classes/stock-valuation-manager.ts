import type { AsyncResponse, Context, decimal, Dict, integer } from '@sage/xtrem-core';
import { asyncArray, Logger } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import { LogicError } from '@sage/xtrem-shared';
import * as xtremStockData from '@sage/xtrem-stock-data';
import type * as xtremSystem from '@sage/xtrem-system';
import * as xtremStock from '..';

export abstract class StockValuationManager {
    static async getInstance(
        context: Context,
        createInstanceParameter: xtremStock.interfaces.StockValuationManagerInstanceParameter,
    ): Promise<StockValuationManager> {
        const asyncStockDetails = asyncArray(createInstanceParameter.stockDetails);
        if (createInstanceParameter.stockDetails.length === 0) {
            throw new LogicError(`StockDetails is empty`);
        }

        const firstStockDetail = createInstanceParameter.stockDetails[0];
        const documentLine = await firstStockDetail.documentLine;
        const valuationParameters = await (
            documentLine as xtremStockData.interfaces.AnyStockDocumentLineWithoutStockDetail
        ).getValuationParameters();

        const item = await firstStockDetail.item;
        const site = await StockValuationManager.getStockSite(firstStockDetail);

        // the item-site must be unique in all stockDetails
        const firstItemSite = [await item.id, await site.id].join('|');

        const stockDetailWithOtherItemSite = await asyncStockDetails.find(
            async stockDetail =>
                firstItemSite.localeCompare(
                    [
                        await (await stockDetail.item).id,
                        await (await StockValuationManager.getStockSite(stockDetail)).id,
                    ].join('|'),
                ) !== 0,
        );
        if (stockDetailWithOtherItemSite) {
            const secondItemSite = [
                await (await stockDetailWithOtherItemSite.item).id,
                await (await StockValuationManager.getStockSite(stockDetailWithOtherItemSite)).id,
            ].join('|');
            throw new LogicError(`StockDetails contains several item-sites: ${firstItemSite} and ${secondItemSite}`);
        }

        if (createInstanceParameter.action !== 'correctValue') {
            // the documentLine must be unique in all stockDetails, except for corrections
            // (case of several production trackings corrected by the WO)
            const stockDetailWithOtherDocumentLine = await asyncStockDetails.find(
                async stockDetail => (await stockDetail.documentLine)._id !== documentLine._id,
            );
            if (stockDetailWithOtherDocumentLine) {
                throw new LogicError(
                    `StockDetails contains several document lines: ${documentLine._id} and ${
                        (await stockDetailWithOtherDocumentLine.documentLine)._id
                    }`,
                );
            }
        } else {
            // all corrected lines must have the same valuationType
            const stockDetailWithOtherValuationType = await asyncStockDetails.find(
                async stockDetail =>
                    (
                        await (
                            (await stockDetail.documentLine) as xtremStockData.interfaces.AnyStockDocumentLineWithoutStockDetail
                        ).getValuationParameters()
                    ).valuationType !== valuationParameters.valuationType,
            );
            if (stockDetailWithOtherValuationType) {
                throw new LogicError(
                    `StockDetails contains several valuation types: ${valuationParameters.valuationType} and ${
                        (
                            await (
                                (await stockDetailWithOtherValuationType.documentLine) as xtremStockData.interfaces.AnyStockDocumentLineWithoutStockDetail
                            ).getValuationParameters()
                        ).valuationType
                    }`,
                );
            }
        }

        const itemSite = await context.read(xtremMasterData.nodes.ItemSite, { item, site });

        let instance: StockValuationManager;

        switch (await itemSite.valuationMethod) {
            case 'averageCost':
                if (createInstanceParameter.action === 'createValue') {
                    throw new LogicError('Average cost is not managed with the StockValuationManager class, yet.');
                }
                instance = await xtremStock.classes.StockAverageCostValuationManager.getAverageCostInstance(
                    context,
                    createInstanceParameter,
                    valuationParameters,
                );
                break;

            case 'fifoCost':
                instance = await xtremStock.classes.StockFifoValuationManager.getFifoInstance(
                    context,
                    createInstanceParameter,
                    valuationParameters,
                );
                break;

            case 'standardCost':
                if (createInstanceParameter.action === 'createValue') {
                    throw new LogicError('Standard cost is not managed with the StockValuationManager class, yet.');
                }
                instance = await xtremStock.classes.StockStandardCostValuationManager.getStandardCostInstance(
                    context,
                    createInstanceParameter,
                    valuationParameters,
                );
                break;

            default:
                throw new LogicError(
                    `Valuation method "${await itemSite.valuationMethod}" is not managed with the StockValuationManager class, yet.`,
                );
        }

        await StockValuationManager.logger.verboseAsync(
            async () => `StockValuationManager instance = ${await instance.toString()}`,
        );

        return instance;
    }

    // ============ Instance declarations
    static readonly logger = Logger.getLogger(__filename, 'stock-valuation-manager');

    protected context: Context; // get a writableContext on "finish"

    readonly action: xtremStock.interfaces.StockValuationAction;

    readonly stockDetails: Array<xtremStockData.nodes.BaseStockDetail>;

    // When there is only 1 document line in the stockDetails
    protected documentLine: xtremMasterData.nodes.BaseDocumentLine | null;

    protected readonly correctionParameters?: Array<xtremStockData.interfaces.ValuationCorrectionParameter>;

    readonly valuationParameter: xtremStockData.interfaces.ValuationParameter;

    protected itemSite: xtremMasterData.nodes.ItemSite;

    protected item: xtremMasterData.nodes.Item;

    protected site: xtremSystem.nodes.Site;

    /**
     * total quantity in the groups.
     * !!!! The amount is negative in case of stock issue !!!!!
     */
    protected totalGroupsQuantity: decimal = 0;

    /**
     * the list of values (amount absorbed, non absorbed) grouped by:
     * - documentLine if action is not correctValue
     * - documentLine and stockDetailId if action is correctValue
     *
     * see {@link StockValuationManager.getGroupIndex} method for the key
     */
    protected stockValuationGroups: Record<string, xtremStock.interfaces.StockValuationGroup> = {};

    protected constructor(
        context: Context,
        createInstanceParameter: xtremStock.interfaces.StockValuationManagerInstanceParameter,
        valuationParameter: xtremStockData.interfaces.ValuationParameter,
    ) {
        this.context = context;
        this.stockDetails = createInstanceParameter.stockDetails;
        this.valuationParameter = valuationParameter;
        this.action = createInstanceParameter.action;
        // plus besoin car tout est dans les stockCorrectionDetails
        // if (createInstanceParameter.action === 'correctValue') {
        //     this.correctionParameters = createInstanceParameter.correctionParameters;
        // }
    }

    static async getStockSite(stockDetail: xtremStockData.nodes.BaseStockDetail): Promise<xtremSystem.nodes.Site> {
        const document = (await (
            await stockDetail.documentLine
        ).document) as xtremStockData.interfaces.StockDocumentHeader;
        const site = await document.getStockSite();

        if (!site) {
            const documentNumber = await document.number;
            throw new LogicError(`The stock site is missing in the ${documentNumber} document.`);
        }

        return site;
    }

    // ============ Instance methods

    protected async initialize() {
        const firstStockDetail = this.stockDetails[0];

        this.item = await firstStockDetail.item;
        this.site = await StockValuationManager.getStockSite(firstStockDetail);

        this.itemSite = await this.context.read(xtremMasterData.nodes.ItemSite, {
            item: this.item,
            site: this.site,
        });

        this.documentLine = await this.stockDetails[0].documentLine;
        await asyncArray(this.stockDetails).forEach(async stockDetail => {
            const detailToUse =
                stockDetail instanceof xtremStockData.nodes.StockCorrectionDetail
                    ? await stockDetail.correctedStockDetail
                    : stockDetail;
            const documentLine =
                (await stockDetail.documentLine) as xtremStockData.interfaces.AnyStockDocumentLineWithoutStockDetail;

            if (this.documentLine && this.documentLine._id !== documentLine._id) {
                this.documentLine = null;
            }

            const quantity = await detailToUse.quantityInStockUnit;
            if (this.stockValuationGroups[this.getGroupIndex(documentLine._id, stockDetail._id)]) {
                this.stockValuationGroups[this.getGroupIndex(documentLine._id, stockDetail._id)].quantity += quantity;
            } else {
                this.stockValuationGroups[this.getGroupIndex(documentLine._id, stockDetail._id)] = {
                    documentLine,
                    stockDetailId: stockDetail._id,
                    quantity,
                    orderAmount: 0,
                    valuedAmount: 0,
                    nonAbsorbedAmount: 0,
                };
            }
            this.totalGroupsQuantity += quantity;
        });

        await this.forEachStockValuationGroup(e => {
            if (e.quantity === 0) {
                throw new LogicError(
                    `The quantity in stockDetails for the line ${JSON.stringify(e.documentLine)} is 0. stockDetails = ${JSON.stringify(
                        this.stockDetails,
                        null,
                        4,
                    )}`,
                );
            }
        });
    }

    /**
     * Get the total quantity and the total of amount to absorb of the stock details
     * @param groupCallback  callback to give the key of groups
     * @example
     *
     * ```typescript
     * this.stockDetails = [
     *   { documentLine: { _id: 1000 }, originDocumentLine: {_id: 2000}, quantityImpacted: 10, amountToAbsorb: 100 },
     *   { documentLine: { _id: 1000 }, originDocumentLine: {_id: 2000}, quantityImpacted: 15, amountToAbsorb: 150 },
     *   { documentLine: { _id: 1000 }, originDocumentLine: {_id: 2001}, quantityImpacted: 100, amountToAbsorb: 100 },
     *   { documentLine: { _id: 1001 }, originDocumentLine: {_id: 2000}, quantityImpacted: 1000, amountToAbsorb: 100 },
     *   { documentLine: { _id: 1001 }, originDocumentLine: {_id: 2002}, quantityImpacted: 10000, amountToAbsorb: 100 },
     * ]
     *
     * const totalQuantity1 = await stockValuationManager.getTotalQuantityByGroup(
     *    stockDetail => `${stockDetail.documentLine._id}|${stockDetail.originDocumentLine._id}`,
     * );
     *  totalQuantity1 = {
     *  '1000|2000': {totalQuantityImpacted: 25, totalAmountToAbsorb:250},
     *  '1000|2001': {totalQuantityImpacted: 100, totalAmountToAbsorb:100},
     *  '1001|2000': {totalQuantityImpacted: 1000, totalAmountToAbsorb:100},
     *  '1001|2002': {totalQuantityImpacted: 10000, totalAmountToAbsorb:100} }
     *
     * const totalQuantity2 = await stockValuationManager.getTotalQuantityByGroup(
     *    stockDetail => `${stockDetail.originDocumentLine._id}`,
     * );
     * totalQuantity2 = {
     * '2000': {totalQuantityImpacted: 1025, totalAmountToAbsorb:350},
     * '2001': {totalQuantityImpacted: 100, totalAmountToAbsorb:100},
     * '2002': {totalQuantityImpacted: 10000, totalAmountToAbsorb:100} }
     * ```
     */
    async getCorrectionTotalsByGroup(
        groupCallback: (stockCorrectionDetail: xtremStockData.nodes.StockCorrectionDetail) => Promise<string>,
    ): Promise<
        Dict<{
            totalQuantityImpacted: decimal;
            totalAmountToAbsorb: decimal;
            totalAbsorbedAmount: decimal;
        }>
    > {
        if (this.action !== 'correctValue') {
            throw new LogicError('getCorrectionTotalsByGroup is only available for correctValue action');
        }

        const totalByOrigin: Awaited<ReturnType<StockValuationManager['getCorrectionTotalsByGroup']>> = {};

        await asyncArray(this.stockDetails).forEach(
            async (stockCorrectionDetail: xtremStockData.nodes.StockCorrectionDetail) => {
                const groupId = await groupCallback(stockCorrectionDetail);
                if (!totalByOrigin[groupId]) {
                    totalByOrigin[groupId] = {
                        totalQuantityImpacted: 0,
                        totalAmountToAbsorb: 0,
                        totalAbsorbedAmount: 0,
                    };
                }
                totalByOrigin[groupId].totalQuantityImpacted += await stockCorrectionDetail.impactedQuantity;
                totalByOrigin[groupId].totalAmountToAbsorb += await stockCorrectionDetail.amountToAbsorb;
            },
        );

        return totalByOrigin;
    }

    async forEachStockValuationGroup(callback: (e: xtremStock.interfaces.StockValuationGroup) => AsyncResponse<void>) {
        await asyncArray(Object.keys(this.stockValuationGroups)).forEach(async key => {
            await callback(this.stockValuationGroups[key]);
        });
    }

    protected setStockValuationGroupAmounts(
        documentLineId: integer,
        stockDetailId: integer,
        value: Omit<xtremStock.interfaces.StockValuationGroup, 'documentLine' | 'stockDetailId' | 'quantity'>,
    ) {
        const stockValuationGroup = this.getStockValuationGroup(documentLineId, stockDetailId);

        stockValuationGroup.orderAmount = value.orderAmount;
        stockValuationGroup.valuedAmount = value.valuedAmount;
        stockValuationGroup.nonAbsorbedAmount = value.nonAbsorbedAmount;
    }

    protected getGroupIndex(documentLineId: integer, stockDetailId?: integer): string {
        if (this.action !== 'correctValue') {
            return documentLineId.toString();
        }
        if (!stockDetailId) {
            throw new LogicError('stockDetailId is required when action is correctValue');
        }
        return `${documentLineId}|${stockDetailId}`;
    }

    private getStockValuationGroup(documentLineId: integer, stockDetailId?: integer) {
        const index = this.getGroupIndex(documentLineId, stockDetailId);
        const documentLineAggregation = this.stockValuationGroups[index];
        if (!documentLineAggregation)
            throw new LogicError(
                `The group ${index} is not present in stockValuationGroups. stockDetails= ${JSON.stringify(
                    this.stockDetails,
                    null,
                    4,
                )}`,
            );
        return documentLineAggregation;
    }

    public getTotalOrderAmount() {
        return Object.keys(this.stockValuationGroups).reduce((sum, key) => {
            return sum + this.stockValuationGroups[key].orderAmount;
        }, 0);
    }

    public getOrderAmount(
        documentLine: xtremMasterData.nodes.BaseDocumentLine,
        quantity?: decimal,
        stockDetailId?: integer,
    ): decimal {
        const documentLineAggregation = this.getStockValuationGroup(documentLine._id, stockDetailId);

        return quantity
            ? documentLineAggregation.orderAmount * Math.abs(quantity / documentLineAggregation.quantity)
            : documentLineAggregation.orderAmount;
    }

    public getValuedAmount(
        documentLine: xtremMasterData.nodes.BaseDocumentLine,
        quantity?: decimal,
        stockDetailId?: integer,
    ): decimal {
        const documentLineAggregation = this.getStockValuationGroup(documentLine._id, stockDetailId);
        return quantity
            ? documentLineAggregation.valuedAmount * Math.abs(quantity / documentLineAggregation.quantity)
            : documentLineAggregation.valuedAmount;
    }

    public getNonAbsorbedAmount(
        documentLine: xtremMasterData.nodes.BaseDocumentLine,
        quantity?: decimal,
        stockDetailId?: integer,
    ): decimal {
        const documentLineAggregation = this.getStockValuationGroup(documentLine._id, stockDetailId);
        return quantity
            ? documentLineAggregation.nonAbsorbedAmount * Math.abs(quantity / documentLineAggregation.quantity)
            : documentLineAggregation.nonAbsorbedAmount;
    }

    public isValuationToPerform(
        documentLine: xtremMasterData.nodes.BaseDocumentLine,
        stockDetailId?: integer,
    ): boolean {
        const documentLineAggregation = this.getStockValuationGroup(documentLine._id, stockDetailId);
        return (
            documentLineAggregation.orderAmount !== 0 ||
            documentLineAggregation.valuedAmount !== 0 ||
            documentLineAggregation.nonAbsorbedAmount !== 0
        );
    }

    static calculateAbsorbedAmount(inStockQuantity: decimal, quantity: decimal, amount: decimal): decimal {
        StockValuationManager.logger.verbose(
            () => `calculateAbsorbedAmount inStockQuantity=${inStockQuantity} quantity=${quantity} amount=${amount} `,
        );

        const res = inStockQuantity >= quantity ? amount : (amount * inStockQuantity) / quantity;

        StockValuationManager.logger.verbose(() => ` -> calculateAbsorbedAmount returns=${res}`);

        return res;
    }

    /**
     * what to do when the valuation is finished
     */
    abstract finish(writableContext: Context): Promise<void>;

    async toString() {
        return JSON.stringify(
            {
                stockDetails: this.stockDetails,
                action: this.action,
                item: await this.item.id,
                site: await this.site.id,
                stockValuationGroups: this.stockValuationGroups,
            },
            null,
            4,
        );
    }
}
