import type * as xtremCommunication from '@sage/xtrem-communication';
import type { Context, NodeUpdateData, Reference } from '@sage/xtrem-core';
import { asyncArray, BusinessRuleError, Logger, ValidationSeverity } from '@sage/xtrem-core';
import * as xtremStockData from '@sage/xtrem-stock-data';
import { noop } from 'lodash';

export class AllocationEngine {
    // ============ Static declarations (private)

    private static _instance: AllocationEngine | null = null;

    // ============ Static declarations (public)

    static getInstance() {
        if (!AllocationEngine._instance) {
            AllocationEngine._instance = new AllocationEngine();
        }

        return AllocationEngine._instance;
    }

    // ============ Instance declarations

    logger: Logger;

    private constructor() {
        this.logger = Logger.getLogger(__filename, 'allocation');
    }

    // ============ Instance methods (private)

    private async reply(
        context: Context,
        args: {
            replyTopic: string;
            replyPayload: xtremStockData.interfaces.AllocationReplyPayload;
        },
    ) {
        noop(this);
        await context.runInWritableContext(async childContext => {
            await childContext.reply(args.replyTopic, args.replyPayload);
        });
    }

    private async _allocationQueueProcessor(
        context: Context,
        args: {
            processType: xtremStockData.enums.AllocationProcessType;
            queueLine: xtremStockData.nodes.AllocationQueue;
            originRequest: string;
            stockAllocationParameters?: xtremStockData.interfaces.StockAllocationParameters;
        },
    ): Promise<xtremStockData.interfaces.AllocationReplyPayload> {
        noop(this);

        const documentLine = (await args.queueLine
            .documentLine) as xtremStockData.interfaces.DocumentLineWithStockAllocation;

        const previousAllocations = documentLine.stockAllocations;
        const previousSerialNumbers = await documentLine.stockAllocations.reduce(async (serialNumbers, allocation) => {
            serialNumbers.push(...(await allocation.serialNumbers.toArray()));
            return serialNumbers;
        }, [] as xtremStockData.nodes.SerialNumber[]);

        const availableStock = await xtremStockData.functions.stockLib.searchStock(context, {
            item: await args.queueLine.item,
            site: await args.queueLine.stockSite,
            activeQuantityInStockUnit: await args.queueLine.quantityToProcess,
            stockCharacteristics: [{ statusList: [{ statusType: 'accepted' }] }],
        });

        if (availableStock.length === 0) {
            return Promise.resolve({
                processId: await args.queueLine.processId,
                processType: args.processType,
                requestType: 'allocation',
                originRequest: args.originRequest,
                updates: [],
                quantityAffected: 0,
            });
        }

        let quantityAffected = 0;

        // Allocate the stock returned by the stock search
        return {
            processId: await args.queueLine.processId,
            processType: args.processType,
            requestType: 'allocation',
            originRequest: args.originRequest,
            updates: await xtremStockData.functions.allocationLib.updateAllocations(
                context,
                {
                    documentLine: args.queueLine
                        .documentLine as Reference<xtremStockData.interfaces.DocumentLineWithStockAllocation>,
                    allocationUpdates: await asyncArray(availableStock)
                        .map(async searchResult => {
                            const allocationRecord = await previousAllocations.find(
                                async allocation => (await allocation.stockRecord)._id === searchResult.stockRecord._id,
                            );

                            const quantity = Math.abs(searchResult.quantityInStockUnit);
                            quantityAffected += quantity;

                            return {
                                action: 'increase',
                                quantity,
                                ...(allocationRecord
                                    ? { allocationRecord }
                                    : { stockRecord: Promise.resolve(searchResult.stockRecord) }),
                                ...(searchResult.serialNumbers?.length
                                    ? {
                                          serialNumbers: searchResult.serialNumbers.filter(
                                              sn =>
                                                  !previousSerialNumbers.find(previousSn => previousSn._id === sn._id),
                                          ),
                                      }
                                    : {}),
                            } as xtremStockData.interfaces.DataForUpdateAllocationActions;
                        })
                        .toArray(),
                },
                args.stockAllocationParameters,
            ),
            quantityAffected,
        };
    }

    private async _deallocationQueueProcessor(
        context: Context,
        args: {
            processType: xtremStockData.enums.AllocationProcessType;
            queueLine: xtremStockData.nodes.AllocationQueue;
            originRequest: string;
        },
    ): Promise<xtremStockData.interfaces.AllocationReplyPayload> {
        noop(this);

        const quantityToProcess = await args.queueLine.quantityToProcess;
        let quantityAffected = 0;

        // Fetch the allocation of the document line to delete or decrease existing allocations
        const documentLine = (await args.queueLine
            .documentLine) as xtremStockData.interfaces.DocumentLineWithStockAllocation & Node;
        const allocationUpdates: xtremStockData.interfaces.DataForUpdateAllocationActions[] = [];

        const item = await (await (await documentLine.stockAllocations.elementAt(0)).stockRecord).item;
        const isLotManaged = (await item.lotManagement) !== 'notManaged';
        const isExpiryManaged = await item.isExpiryManaged;

        await documentLine.stockAllocations
            // We need to deallocate first the last created allocation
            .sort(async (a, b) => {
                const defaultOrder = b._id - a._id;
                let order = 0;
                if (!isLotManaged) {
                    return defaultOrder;
                }

                const lotA = await (await a.stockRecord).lot;
                const lotB = await (await b.stockRecord).lot;
                if (!lotA || !lotB) {
                    return defaultOrder;
                }
                if (isExpiryManaged) {
                    const expirationA = await lotA.expirationDate;
                    const expirationB = await lotB.expirationDate;
                    if (expirationA && expirationB) {
                        order = expirationB.daysDiff(expirationA) || order;
                    }
                }

                return order || (await lotB.creationDate).daysDiff(await lotA.creationDate) || defaultOrder;
            })
            .forEach(async allocation => {
                if (quantityToProcess === quantityAffected) {
                    return;
                }
                const allocationReference = context.read(
                    xtremStockData.nodes.StockAllocation,
                    { _id: allocation._id },
                    { forUpdate: true },
                );

                const allocationToProcess = await allocationReference;
                const allocatedQuantity = await allocationToProcess.quantityInStockUnit;

                if (allocatedQuantity <= quantityToProcess - quantityAffected) {
                    // delete the allocation
                    quantityAffected += allocatedQuantity;
                    allocationUpdates.push({
                        action: 'delete',
                        allocationRecord: allocationReference,
                    });
                } else {
                    // decrease the allocation
                    const quantityToDecrease = quantityToProcess - quantityAffected;
                    quantityAffected += quantityToDecrease;
                    allocationUpdates.push({
                        action: 'decrease',
                        allocationRecord: allocationReference,
                        quantity: quantityToDecrease,
                        serialNumbers: await allocationToProcess.serialNumbers.toArray(),
                    });
                }
            });

        return {
            processId: await args.queueLine.processId,
            processType: args.processType,
            requestType: 'deallocation',
            originRequest: args.originRequest,
            updates: await xtremStockData.functions.allocationLib.updateAllocations(context, {
                documentLine: args.queueLine.documentLine as Reference<
                    xtremStockData.interfaces.DocumentLineWithStockAllocation & Node
                >,
                allocationUpdates,
            }),
            quantityAffected,
        };
    }

    private _processAllocationQueueCore(
        context: Context,
        args: {
            payload: xtremStockData.interfaces.AutomaticAllocationPayload<xtremStockData.enums.AllocationProcessTypeEnum>;
            queueLineProcessor:
                | AllocationEngine['_allocationQueueProcessor']
                | AllocationEngine['_deallocationQueueProcessor'];
            replyTopic: string;
        },
    ): Promise<xtremStockData.interfaces.AllocationReplyPayload> {
        return context
            .query(xtremStockData.nodes.AllocationQueue, {
                filter: {
                    processId: { _eq: args.payload.processId },
                    requestType: { _eq: args.payload.requestType },
                },
            })
            .reduce(
                async (allocationsResult, queueLine) => {
                    const queueLineId = queueLine._id;
                    const documentLineId = (await queueLine.documentLine)._id;
                    const queueLineQuantityToAllocate = await queueLine.quantityToProcess;

                    let systemError: any = null;

                    const updatedAllocationsResult = await context.runInWritableContext(async childContext => {
                        const resultLineUpdateData: NodeUpdateData<xtremStockData.nodes.AllocationResultLine> = {
                            quantityProcessed: 0,
                        };

                        if (await context.batch.isStopRequested()) {
                            await context.batch.logMessage('warning', ` Stop requested at ${Date.now().toString()}`);
                            await childContext.delete(xtremStockData.nodes.AllocationQueue, { _id: queueLineId });

                            return allocationsResult;
                        }

                        let allocationResultLine: xtremStockData.nodes.AllocationResultLine | null = null;

                        try {
                            allocationResultLine = await childContext
                                .query(xtremStockData.nodes.AllocationResultLine, {
                                    first: 1,
                                    filter: {
                                        result: {
                                            processId: args.payload.processId,
                                        },
                                        documentLine: {
                                            _id: documentLineId,
                                        },
                                    },
                                    forUpdate: true,
                                })
                                .elementAt(0);

                            const currentAllocationResult = await args.queueLineProcessor(childContext, {
                                processType: args.payload.processType,
                                queueLine,
                                originRequest: args.payload.originRequest,
                                ...(args.payload.stockAllocationParameters ?? {
                                    stockAllocationParameters: args.payload.stockAllocationParameters,
                                }),
                            });
                            resultLineUpdateData.quantityProcessed = currentAllocationResult.quantityAffected;

                            if (childContext.diagnoses.length) {
                                resultLineUpdateData.message = (
                                    await asyncArray(childContext.diagnoses).elementAt(0)
                                ).message;

                                await asyncArray(childContext.diagnoses).forEach(async diag => {
                                    let logLevel: xtremCommunication.enums.LogLevel;

                                    switch (diag.severity) {
                                        case ValidationSeverity.test:
                                            logLevel = 'test';
                                            break;
                                        case ValidationSeverity.info:
                                            logLevel = 'info';
                                            break;
                                        case ValidationSeverity.warn:
                                            logLevel = 'warning';
                                            break;
                                        case ValidationSeverity.error:
                                            logLevel = 'error';
                                            break;
                                        case ValidationSeverity.exception:
                                            logLevel = 'exception';
                                            break;
                                        default:
                                            logLevel = 'result';
                                            break;
                                    }

                                    await context.batch.logMessage(logLevel, diag.message);
                                    resultLineUpdateData.message = diag.message;
                                });
                            }

                            allocationsResult.updates.push(...currentAllocationResult.updates);
                            allocationsResult.quantityAffected += currentAllocationResult.quantityAffected;
                            allocationsResult.batchTrackingId = args.payload.batchTrackingId;
                        } catch (error) {
                            await context.batch.logMessage('error', error.message);

                            if (!(error instanceof BusinessRuleError)) {
                                allocationsResult.message = context.localize(
                                    '@sage/xtrem-stock/class__allocation-engine__system_error_during_allocation_process',
                                    'The allocation process was interrupted: {{errorMessage}}',
                                    {
                                        errorMessage: error.message,
                                    },
                                );

                                await childContext.bulkDeleteSql(xtremStockData.nodes.AllocationQueue, {
                                    where: {
                                        processId: args.payload.processId,
                                    },
                                });

                                await childContext.bulkUpdate(xtremStockData.nodes.AllocationResultLine, {
                                    set: {
                                        status: 'error',
                                        message: allocationsResult.message,
                                    },
                                    where: {
                                        status: {
                                            _ne: 'completed',
                                        },
                                        result: {
                                            processId: args.payload.processId,
                                        },
                                    },
                                });

                                await this.reply(context, {
                                    replyTopic: args.replyTopic,
                                    replyPayload: allocationsResult,
                                });

                                systemError = error;

                                return allocationsResult;
                            }

                            resultLineUpdateData.status = 'error';
                            resultLineUpdateData.message = error.message;
                        }

                        if (resultLineUpdateData.status !== 'error') {
                            resultLineUpdateData.status = 'completed';
                        }

                        if (!systemError) {
                            await childContext.delete(xtremStockData.nodes.AllocationQueue, { _id: queueLineId });
                        }

                        resultLineUpdateData.shortageQuantity =
                            (resultLineUpdateData.quantityProcessed || 0) > queueLineQuantityToAllocate
                                ? 0
                                : queueLineQuantityToAllocate - (resultLineUpdateData.quantityProcessed || 0);

                        if (allocationResultLine) {
                            await allocationResultLine.$.set(resultLineUpdateData);
                            await allocationResultLine.$.save();
                        }
                        allocationsResult.batchTrackingId = args.payload.batchTrackingId;
                        return allocationsResult;
                    });

                    if (systemError instanceof Error) {
                        throw systemError;
                    }

                    return updatedAllocationsResult;
                },
                {
                    processId: args.payload.processId,
                    processType: args.payload.processType,
                    requestType: args.payload.requestType,
                    originRequest: args.payload.originRequest,
                    updates: [],
                    quantityAffected: 0,
                    batchTrackingId: args.payload.batchTrackingId,
                } as xtremStockData.interfaces.AllocationReplyPayload,
            );
    }

    private async _processAllocationQueue(
        context: Context,
        payload: xtremStockData.interfaces.AutomaticAllocationPayload<xtremStockData.enums.AllocationProcessTypeEnum>,
    ): Promise<xtremStockData.enums.AllocationRequestStatus> {
        let processQueueLine: typeof this._allocationQueueProcessor | typeof this._deallocationQueueProcessor;

        if (payload.requestType === 'allocation') {
            processQueueLine = this._allocationQueueProcessor;
        } else {
            processQueueLine = this._deallocationQueueProcessor;
        }

        const allocationUpdateResults = await this._processAllocationQueueCore(context, {
            payload,
            queueLineProcessor: processQueueLine,
            replyTopic: payload.replyTopic,
        });

        if (await context.batch.isStopRequested()) {
            await context.batch.confirmStop();
        }

        await this.reply(context, {
            replyTopic: payload.replyTopic,
            replyPayload: allocationUpdateResults,
        });

        return 'completed';
    }

    // ============ Instance methods (public)

    allocate(
        context: Context,
        notificationPayload: xtremStockData.interfaces.AutomaticAllocationPayload<xtremStockData.enums.AllocationProcessTypeEnum>,
    ) {
        this.logger.info(
            () =>
                `Automatically trying to allocate processId ${notificationPayload.processId}, request type: ${notificationPayload.requestType}`,
        );

        return this._processAllocationQueue(context, notificationPayload);
    }
}
