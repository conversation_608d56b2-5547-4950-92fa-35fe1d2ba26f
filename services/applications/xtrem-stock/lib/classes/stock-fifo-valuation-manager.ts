import type { Context, decimal, integer, NodeCreateData } from '@sage/xtrem-core';
import { asyncArray, date } from '@sage/xtrem-core';
import type * as xtremMasterData from '@sage/xtrem-master-data';
import { LogicError } from '@sage/xtrem-shared';
import * as xtremStockData from '@sage/xtrem-stock-data';
import type * as xtremSystem from '@sage/xtrem-system';
import type * as xtremStock from '..';
import { StockValuationManager } from './stock-valuation-manager';

export class StockFifoValuationManager extends StockValuationManager {
    static async getFifoInstance(
        context: Context,
        createInstanceParameter: xtremStock.interfaces.StockValuationManagerInstanceParameter,
        valuationParameter: xtremStockData.interfaces.ValuationParameter,
    ): Promise<StockFifoValuationManager> {
        const instance = new StockFifoValuationManager(context, createInstanceParameter, valuationParameter);

        await instance.initialize();
        return instance;
    }

    protected constructor(
        context: Context,
        createInstanceParameter: xtremStock.interfaces.StockValuationManagerInstanceParameter,
        valuationParameter: xtremStockData.interfaces.ValuationParameter,
    ) {
        super(context, createInstanceParameter, valuationParameter);
    }

    private async initializeForReceipt() {
        await this.forEachStockValuationGroup(async stockValuationGroup => {
            stockValuationGroup.orderAmount =
                ((await stockValuationGroup.documentLine.getOrderCost()) || 0) * stockValuationGroup.quantity;
            stockValuationGroup.valuedAmount = stockValuationGroup.orderAmount;

            stockValuationGroup.nonAbsorbedAmount = 0;
        });
    }

    private async initializeForValueChange() {
        // Check for correct type in stockDetails. Note, that we can handle StockValueDetails only.
        if (!(this.stockDetails[0] instanceof xtremStockData.nodes.StockValueDetail)) {
            throw new LogicError(
                `stockDetails is not an array of instances of StockValueDetail ${JSON.stringify(
                    this.stockDetails,
                    null,
                    4,
                )}`,
            );
        }

        // Note: There is only one StockValueDetail per stock value change line.
        const stockDetail: xtremStockData.nodes.StockValueDetail = this.stockDetails[0];

        const amount = await stockDetail.amount;
        this.setStockValuationGroupAmounts((await stockDetail.documentLine)._id, stockDetail._id, {
            // The order amount in this context is the difference between the new value in the stock value change
            // document line and the current value.
            orderAmount: amount,

            // For FIFO cost, the valued amount is the same as the order amount:
            valuedAmount: amount,

            // No non-absorbed amount.
            nonAbsorbedAmount: 0,
        });
    }

    private async initializeForIssue() {
        await this.forEachStockValuationGroup(async stockValuationGroup => {
            stockValuationGroup.orderAmount = -(
                await xtremStockData.nodeExtensions.ItemSiteExtension.getItemSiteValuationCost(
                    this.context,
                    this.itemSite,
                    {
                        quantity: Math.abs(stockValuationGroup.quantity), // quantity is negative for issues
                        fifoCriteria: {
                            dateOfValuation: await (await stockValuationGroup.documentLine.document).getEffectiveDate(),
                            valuationType: this.valuationParameter.valuationType,
                        },
                    },
                )
            ).amount;
            stockValuationGroup.valuedAmount = stockValuationGroup.orderAmount;

            stockValuationGroup.nonAbsorbedAmount = 0;
        });
    }

    private async initializeForNegativeIssue() {
        StockFifoValuationManager.logger.verbose(
            () => `getNegativeIssueValue valuationType=${this.valuationParameter.valuationType}`,
        );
        if (this.valuationParameter.valuationType !== 'negativeIssue') {
            throw new LogicError(
                'getNegativeIssueValue must be called only if valuationParameter.valuationType === negativeIssue ',
            );
        }

        const origin = this.valuationParameter.originDocumentLine;
        StockFifoValuationManager.logger.verbose(() => `getNegativeIssueValue  originDocumentLine=${origin}`);
        if (!origin) {
            await this.initializeForReceipt();
        } else {
            const { totalConsumedValue } = await xtremStockData.functions.fifoValuationLib.consumeFifoIssueTier(
                this.context,
                origin,
                this.totalGroupsQuantity,
            );
            await this.forEachStockValuationGroup(stockValuationGroup => {
                stockValuationGroup.orderAmount =
                    (totalConsumedValue * stockValuationGroup.quantity) / this.totalGroupsQuantity;
                StockFifoValuationManager.logger.verbose(
                    () => `getNegativeIssueValue stockValuationGroup.orderAmount=${stockValuationGroup.orderAmount}`,
                );

                stockValuationGroup.valuedAmount = stockValuationGroup.orderAmount;

                stockValuationGroup.nonAbsorbedAmount = 0;
            });
        }
    }

    private async initializeForNegativeReceiptValue() {
        StockFifoValuationManager.logger.verbose(
            () => `getNegativeReceiptValue valuationType=${this.valuationParameter.valuationType}`,
        );
        if (this.valuationParameter.valuationType !== 'negativeReceipt') {
            throw new LogicError(
                'getNegativeReceiptValue must be called only if valuationParameter.valuationType === negativeReceipt ',
            );
        }

        const origin = this.valuationParameter.originDocumentLine;
        StockFifoValuationManager.logger.verbose(() => `getNegativeReceiptValue  originDocumentLine=${origin}`);
        if (!origin) {
            await this.initializeForIssue();
        } else {
            // here, we suppose that a receipt line must have 1 and only 1 tier (until today, only sales returns can create several records)
            const receiptLineTier = (
                await this.context.select(
                    xtremStockData.nodes.FifoValuationTier,
                    { effectiveDate: true, sequence: true, unitCost: true },
                    {
                        filter: { item: this.item, site: this.site, receiptDocumentLine: origin },
                    },
                )
            )[0];

            const totalsConsumption = await xtremStockData.functions.fifoValuationLib.consumeFifoValuationTierFrom(
                this.context,
                {
                    item: this.item,
                    site: this.site,
                    effectiveDate: receiptLineTier.effectiveDate,
                    sequence: receiptLineTier.sequence,
                },

                Math.abs(this.totalGroupsQuantity),
                false,
                { withUpdate: false },
            );

            // Into 'totalsConsumption', the values are positive (coming from FIFO tier)
            // But as we are in the case of negative receipt => the values of orderAmount and nonAbsorbedAmount must be negative
            // The non-absorbed amount is the difference between what we should have consumed from the FIFO stack and what was actually consumed
            const globalOrderAmount = receiptLineTier.unitCost * Math.abs(this.totalGroupsQuantity);
            const globalMovementAmount = totalsConsumption.totalConsumedValue;
            const nonAbsorbedAmount = globalOrderAmount - globalMovementAmount;

            StockFifoValuationManager.logger.verbose(
                () =>
                    `getNegativeReceiptValue nonAbsorbedAmount=${nonAbsorbedAmount} = (${
                        receiptLineTier.unitCost
                    } * ${Math.abs(this.totalGroupsQuantity)}) - ${totalsConsumption.totalConsumedValue})`,
            );

            await this.forEachStockValuationGroup(stockValuationGroup => {
                stockValuationGroup.orderAmount =
                    (globalOrderAmount * stockValuationGroup.quantity) / Math.abs(this.totalGroupsQuantity);

                stockValuationGroup.valuedAmount =
                    (globalMovementAmount * stockValuationGroup.quantity) / Math.abs(this.totalGroupsQuantity);

                stockValuationGroup.nonAbsorbedAmount =
                    (nonAbsorbedAmount * stockValuationGroup.quantity) / Math.abs(this.totalGroupsQuantity);

                StockFifoValuationManager.logger.verbose(
                    () => `getNegativeReceiptValue stockValuationGroup.orderAmount=${stockValuationGroup.orderAmount}`,
                );
            });
        }
    }

    protected override async initialize() {
        await super.initialize();

        if (this.action === 'correctValue') {
            await this.initializeCorrection();
            return;
        }

        // get the global amount to spread on all stock details
        switch (this.valuationParameter.valuationType) {
            case 'receipt':
                await this.initializeForReceipt();
                break;

            case 'valueChange':
                await this.initializeForValueChange();
                break;

            // change valuation is done like issues for the issue and the receipt
            case 'change':
            case 'issue':
                await this.initializeForIssue();
                break;

            case 'negativeIssue':
                // If there is an origin => take the price of origin document line's FIFO tiers
                // Otherwise, value like a normal receipt
                await this.initializeForNegativeIssue();
                break;

            case 'negativeReceipt':
                // If there is an origin => take the FIFO tiers corresponding to the origin
                // Otherwise, value like a normal issue
                await this.initializeForNegativeReceiptValue();
                break;

            default:
                break;
        }
    }

    protected async initializeCorrection() {
        if (this.valuationParameter.valuationType !== 'receipt') {
            throw new LogicError('Price correction is only managed for receipts');
        }

        if (!(this.stockDetails[0] instanceof xtremStockData.nodes.StockCorrectionDetail)) {
            throw new LogicError(
                `stockDetails is not an instance of StockCorrectionDetail ${JSON.stringify(
                    this.stockDetails,
                    null,
                    4,
                )}`,
            );
        }

        const buildGroupKey = async (
            stockCorrectionDetail: xtremStockData.nodes.StockCorrectionDetail,
        ): Promise<string> =>
            `${(await stockCorrectionDetail.documentLine)._id}|${(await stockCorrectionDetail.originDocumentLine)?._id ?? 0}`;

        const totalByLineAndOrigin = await this.getCorrectionTotalsByGroup(stockCorrectionDetail =>
            buildGroupKey(stockCorrectionDetail),
        );

        await asyncArray(Object.keys(totalByLineAndOrigin)).forEach(async lineAndOrigin => {
            const documentLine = Number(lineAndOrigin.split('|')[0]);
            const fifoTierToCorrect = await this.getFifoTierToCorrect(documentLine);

            totalByLineAndOrigin[lineAndOrigin].totalAbsorbedAmount = fifoTierToCorrect
                ? StockValuationManager.calculateAbsorbedAmount(
                      await fifoTierToCorrect.remainingQuantity,
                      totalByLineAndOrigin[lineAndOrigin].totalQuantityImpacted,
                      totalByLineAndOrigin[lineAndOrigin].totalAmountToAbsorb,
                  )
                : 0;
        });

        await asyncArray(this.stockDetails).forEach(
            async (stockCorrectionDetail: xtremStockData.nodes.StockCorrectionDetail) => {
                const documentLine = await stockCorrectionDetail.documentLine;
                const index = this.getGroupIndex(documentLine._id, stockCorrectionDetail._id);

                const groupKey = await buildGroupKey(stockCorrectionDetail);
                this.stockValuationGroups[index].orderAmount +=
                    totalByLineAndOrigin[groupKey].totalAmountToAbsorb === 0
                        ? 0
                        : ((await stockCorrectionDetail.amountToAbsorb) *
                              totalByLineAndOrigin[groupKey].totalAbsorbedAmount) /
                          totalByLineAndOrigin[groupKey].totalAmountToAbsorb;

                this.stockValuationGroups[index].valuedAmount = this.stockValuationGroups[index].orderAmount;
                this.stockValuationGroups[index].nonAbsorbedAmount =
                    (await stockCorrectionDetail.amountToAbsorb) - this.stockValuationGroups[index].orderAmount;
            },
        );
    }

    private async finishReceipt() {
        await this.forEachStockValuationGroup(async stockValuationGroup => {
            if (this.action === 'createValue') {
                await this.createFifoValuationTier({
                    item: this.item,
                    site: this.site,
                    effectiveDate: await (await stockValuationGroup.documentLine.document).getEffectiveDate(),
                    receiptDocumentLine: stockValuationGroup.documentLine._id,
                    receiptQuantity: stockValuationGroup.quantity,
                    amount: stockValuationGroup.orderAmount,
                });
            } else {
                await this.updateFifoTierValue(
                    {
                        item: this.item,
                        site: this.site,
                        receiptDocumentLine: stockValuationGroup.documentLine._id,
                    },
                    this.getOrderAmount(
                        stockValuationGroup.documentLine,
                        stockValuationGroup.quantity,
                        stockValuationGroup.stockDetailId,
                    ),
                    this.getNonAbsorbedAmount(
                        stockValuationGroup.documentLine,
                        stockValuationGroup.quantity,
                        stockValuationGroup.stockDetailId,
                    ),
                );
            }
        });
    }

    private async finishValueChange() {
        // Note: There is only one StockValueDetail per stock value change line.
        const stockDetail = this.stockDetails[0] as xtremStockData.nodes.StockValueDetail;

        const fifoValuationTier = await stockDetail.fifoValuationTier;

        if (!fifoValuationTier) {
            throw new LogicError('A FIFO valuation tier must exist while FIFO processing.');
        }

        // Update FIFO
        await this.updateFifoTierValue(
            {
                item: await fifoValuationTier.item,
                site: await fifoValuationTier.site,
                receiptDocumentLine: (await fifoValuationTier.receiptDocumentLine)._id,
            },
            this.getOrderAmount(await stockDetail.documentLine),
            this.getNonAbsorbedAmount(await stockDetail.documentLine),
        );
    }

    private async finishIssue() {
        if (this.valuationParameter.valuationType !== 'issue') return;

        const param = this.valuationParameter;
        await this.forEachStockValuationGroup(async stockValuationGroup => {
            await xtremStockData.functions.fifoValuationLib.consumeItemSiteFifoValuationTier(
                this.context,
                this.item,
                this.site,
                Math.abs(stockValuationGroup.quantity),
                {
                    withUpdate: true,
                    afterUpdateCallback: async (
                        tier: xtremStockData.nodes.FifoValuationTier,
                        consumedQuantity: decimal,
                        consumedValue: decimal,
                    ) => {
                        const issueDocumentLine = param.canBeReturned
                            ? stockValuationGroup.documentLine._id
                            : undefined;
                        // Record the issue if the issueDocumentLine has been passed in parameters
                        if (issueDocumentLine) {
                            const fifoIssue = await this.context.create(xtremStockData.nodes.FifoValuationIssue, {
                                issueDocumentLine,
                                effectiveDate: await tier.effectiveDate,
                                sequence: await tier.sequence,
                                issuedQuantity: consumedQuantity,
                                amount: consumedValue,
                                createDate: date.today(),
                                remainingQuantity: consumedQuantity,
                            });
                            await fifoIssue.$.save();
                        }
                    },
                },
            );
        });
    }

    private async finishNegativeReceipt() {
        const { documentLine } = this;
        if (!documentLine) {
            throw new LogicError('The document line must be unique when the valuation type is negativeReceipt');
        }
        StockFifoValuationManager.logger.verbose(
            () => `finishNegativeReceipt valuationType=${this.valuationParameter.valuationType}`,
        );
        if (this.valuationParameter.valuationType !== 'negativeReceipt') {
            throw new LogicError(
                'finishNegativeReceipt must be called only if valuationParameter.valuationType === negativeReceipt ',
            );
        }
        const origin = this.valuationParameter.originDocumentLine;
        StockFifoValuationManager.logger.verbose(() => `finishNegativeReceipt  originDocumentLine=${origin}`);
        if (!origin) {
            // a negative issue without origin is considered as a standard entry
            await this.finishIssue();
            return;
        }

        // here, we suppose that a receipt line must have 1 and only 1 tier (until today, only sales returns can create several records)
        const receiptLineTier = (
            await this.context.select(
                xtremStockData.nodes.FifoValuationTier,
                { effectiveDate: true, sequence: true, unitCost: true },
                {
                    filter: { item: this.item, site: this.site, receiptDocumentLine: origin },
                },
            )
        )[0];

        await xtremStockData.functions.fifoValuationLib.consumeFifoValuationTierFrom(
            this.context,
            {
                item: this.item,
                site: this.site,
                effectiveDate: receiptLineTier.effectiveDate,
                sequence: receiptLineTier.sequence,
            },

            Math.abs(this.totalGroupsQuantity),
            true,
            { withUpdate: true },
        );
    }

    private async finishNegativeIssue() {
        const { documentLine } = this;
        if (!documentLine) {
            throw new LogicError('The document line must be unique when the valuation type is negativeIssue');
        }
        StockFifoValuationManager.logger.verbose(
            () => `getNegativeIssueValue valuationType=${this.valuationParameter.valuationType}`,
        );
        if (this.valuationParameter.valuationType !== 'negativeIssue') {
            throw new LogicError(
                'getNegativeIssueValue must be called only if valuationParameter.valuationType === negativeIssue ',
            );
        }
        const origin = this.valuationParameter.originDocumentLine;
        StockFifoValuationManager.logger.verbose(() => `getNegativeIssueValue  originDocumentLine=${origin}`);
        if (!origin) {
            // a negative issue without origin is considered as a standard entry
            await this.finishReceipt();
            return;
        }

        // Consume the FifoValuationIssue tiers and create new FifoValuationTier
        // corresponding to the current document (negative issue)
        await xtremStockData.functions.fifoValuationLib.consumeFifoIssueTier(
            this.context,
            origin,
            this.totalGroupsQuantity,
            {
                withUpdate: true,
                afterUpdateCallback: async (
                    _tier: xtremStockData.nodes.FifoValuationIssue,
                    consumedQuantity: decimal,
                    consumedValue: decimal,
                ) => {
                    await this.createFifoValuationTier({
                        item: this.item,
                        site: this.site,
                        effectiveDate: await (
                            (await documentLine.document) as xtremStockData.interfaces.StockDocumentHeader
                        ).getEffectiveDate(),
                        receiptDocumentLine: documentLine._id,
                        receiptQuantity: consumedQuantity,
                        amount: consumedValue,
                    });
                },
            },
        );
    }

    /**
     * updates FifoValuationTier node
     */
    async finish(writableContext: Context) {
        this.context = writableContext;
        StockValuationManager.logger.verbose(
            () => `finish this.valuationParameter.valuationType=${this.valuationParameter.valuationType}`,
        );
        switch (this.valuationParameter.valuationType) {
            case 'receipt':
                await this.finishReceipt();
                break;

            case 'valueChange':
                await this.finishValueChange();
                break;

            case 'issue': {
                await this.finishIssue();

                break;
            }

            case 'negativeReceipt': {
                await this.finishNegativeReceipt();
                break;
            }

            case 'negativeIssue': {
                await this.finishNegativeIssue();
                break;
            }
            case 'change': // FIFO stack must not be modified
            default:
                break;
        }
    }

    /**
     * Create a stock FIFO valuation tier in the FIFO stack
     * @param context
     * @param fifoValuationData Data to create the FIFO tier
     */
    private async createFifoValuationTier(
        fifoValuationData: NodeCreateData<xtremStockData.nodes.FifoValuationTier>,
    ): Promise<xtremStockData.nodes.FifoValuationTier> {
        const fifoValuationSearch = {
            effectiveDate: fifoValuationData.effectiveDate,
            item: fifoValuationData.item,
            site: fifoValuationData.site,
        };

        const sequence =
            (await xtremStockData.functions.fifoValuationLib.getLastSequenceNumberFifoValuationTier(
                this.context,
                fifoValuationSearch as xtremStockData.interfaces.FifoValuationSearchData,
            )) + 1;

        StockFifoValuationManager.logger.verbose(
            () =>
                `createFifoValuationTier item=${fifoValuationData.item} site=${fifoValuationData.site}
                 effectiveDate=${fifoValuationData.effectiveDate}  -> sequence = ${sequence}`,
        );

        const fifoValuationRecord = await this.context.create(xtremStockData.nodes.FifoValuationTier, {
            ...fifoValuationData,
            sequence,
            remainingQuantity: fifoValuationData.remainingQuantity ?? fifoValuationData.receiptQuantity,
        });

        await fifoValuationRecord.$.save();

        return fifoValuationRecord;
    }

    /**
     * returns the FIFO tier to correct. If it doesn't exist, it creates a new one with remainingQuantity=0
     * except if the FIFO stack is not synchronized with stock in term of quantity. In this case, an exception is thrown
     * @param correctionParameter
     * @returns the FIFO tier to correct
     */
    async getFifoTierToCorrect(receiptDocumentLine: integer): Promise<xtremStockData.nodes.FifoValuationTier> {
        const fifoTiersToCorrect = await this.context
            .query(xtremStockData.nodes.FifoValuationTier, {
                filter: {
                    item: this.item,
                    site: this.site,
                    receiptDocumentLine,
                },
            })
            .toArray();

        if (fifoTiersToCorrect.length) {
            return fifoTiersToCorrect[0];
        }

        // If the FIFO tier doesn't exist, it means there is a desync in FIFO stack or that the valuation method has changed
        // If the FIFO stack is synchronized with stock in term of quantity, we can suppose that the valuation method has changed
        // => first, check the quantity of the FIFO stack
        const totalQuantityInFifoStack = (
            await xtremStockData.nodes.FifoValuationTier.getTotals(this.context, { item: this.item, site: this.site })
        ).sumRemainingQuantity;

        const inStockQuantity = await this.itemSite.inStockQuantity;
        if (totalQuantityInFifoStack !== inStockQuantity) {
            throw new LogicError(
                `The FIFO stack quantity (${totalQuantityInFifoStack}) for the item-site ${await this.item
                    .id}-${await this.site.id} is different from the quantity in stock (${inStockQuantity})`,
            );
        }

        // the FIFO tier doesn't exist because the valuation method has changed
        // we will create a new one
        const documentLine = (await this.stockDetails[0].documentLine) as xtremStockData.interfaces.StockDocumentLine;
        return this.createFifoValuationTier({
            item: this.item,
            site: this.site,
            effectiveDate: await (await documentLine.document).getEffectiveDate(),
            receiptDocumentLine,
            receiptQuantity: await documentLine.quantityInStockUnit,
            amount: 0,
            remainingQuantity: 0,
        });
    }

    protected async updateFifoTierValue(
        fifoTierSearchCriteria: {
            item: xtremMasterData.nodes.Item;
            site: xtremSystem.nodes.Site;
            receiptDocumentLine: xtremMasterData.nodes.BaseDocumentLine['_id'];
        },
        amountVariance: decimal,
        nonAbsorbedVariance: decimal,
    ) {
        StockFifoValuationManager.logger.verbose(
            () => `fifoTierSearchCriteria = ${JSON.stringify(fifoTierSearchCriteria, null, 4)}`,
        );
        StockFifoValuationManager.logger.verbose(() => `amountVariance = ${JSON.stringify(amountVariance, null, 4)}`);

        const fifoTiers = await this.context
            .query(xtremStockData.nodes.FifoValuationTier, { filter: fifoTierSearchCriteria, forUpdate: true })
            .toArray();
        if (fifoTiers.length > 1) {
            throw new LogicError(
                `Too many records retrieved in updateFifoTierValue. fifoTierSearchCriteria=${JSON.stringify(
                    fifoTierSearchCriteria,
                    null,
                    4,
                )}\nFIFO tiers found:${JSON.stringify(fifoTiers, null, 4)}`,
            );
        }
        const fifoTier = fifoTiers[0];
        await fifoTier.$.set({
            amount: (await fifoTier.amount) + amountVariance,
            nonAbsorbedAmount: (await fifoTier.nonAbsorbedAmount) + nonAbsorbedVariance,
        });
        await fifoTier.$.save();

        await StockValuationManager.logger.verboseAsync(
            async () =>
                `FIFO tier updated:
                 ${await (await fifoTier.item).id} ${await (await fifoTier.site).id}
                 ${await fifoTier.effectiveDate} ${await fifoTier.sequence}
                 ${await fifoTier.amount}`,
        );
        await StockValuationManager.logger.verboseAsync(async () => {
            const fifoStack = await this.context
                .query(xtremStockData.nodes.FifoValuationTier, {
                    filter: {
                        item: await fifoTier.item,
                        site: await fifoTier.site,
                    },
                    orderBy: { item: 1, site: 1, effectiveDate: 1, sequence: 1 },
                })
                .map(async (tier: xtremStockData.nodes.FifoValuationTier) => ({
                    itemSite: `${await (await tier.item).id}|${await (await tier.site).id}`,
                    effectiveDate: await tier.effectiveDate,
                    sequence: await tier.sequence,
                    remainingQuantity: await tier.remainingQuantity,
                    amount: await tier.amount,
                    nonAbsorbedAmount: await tier.nonAbsorbedAmount,
                }))
                .toArray();
            return `FIFO stack for ${await (await fifoTier.item).id} ${await (
                await fifoTier.site
            ).id}:\n${JSON.stringify(fifoStack, null, 4)}`;
        });
    }
}
