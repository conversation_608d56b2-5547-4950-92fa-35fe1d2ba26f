import type { Context } from '@sage/xtrem-core';
import { asyncArray } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import { LogicError } from '@sage/xtrem-shared';
import * as xtremStockData from '@sage/xtrem-stock-data';
import type * as xtremStock from '..';
import { StockValuationManager } from './stock-valuation-manager';

export class StockAverageCostValuationManager extends StockValuationManager {
    static async getAverageCostInstance(
        context: Context,
        createInstanceParameter: xtremStock.interfaces.StockValuationManagerInstanceParameter,
        valuationParameter: xtremStockData.interfaces.ValuationParameter,
    ): Promise<StockAverageCostValuationManager> {
        const instance = new StockAverageCostValuationManager(context, createInstanceParameter, valuationParameter);

        await instance.initialize();
        return instance;
    }

    protected constructor(
        context: Context,
        createInstanceParameter: xtremStock.interfaces.StockValuationManagerInstanceParameter,
        valuationParameter: xtremStockData.interfaces.ValuationParameter,
    ) {
        super(context, createInstanceParameter, valuationParameter);
    }

    protected override async initialize() {
        await super.initialize();

        // For now we only accept 'valueChange' and correction of receipt cost
        if (this.valuationParameter.valuationType === 'receipt' && this.action === 'correctValue') {
            await this.initializeCorrection();
            return;
        }

        if (this.valuationParameter.valuationType === 'valueChange') {
            await this.initializeForValueChange();
        }
    }

    private async initializeForValueChange() {
        if (!(this.stockDetails[0] instanceof xtremStockData.nodes.StockValueDetail)) {
            throw new LogicError(
                `stockDetails is not an array of instances of StockValueDetail ${JSON.stringify(
                    this.stockDetails,
                    null,
                    4,
                )}`,
            );
        }

        // Note: There is only one StockValueDetail per stock value change line.
        const stockDetail: xtremStockData.nodes.StockValueDetail = this.stockDetails[0];

        const amount = await stockDetail.amount;
        this.setStockValuationGroupAmounts((await stockDetail.documentLine)._id, stockDetail._id, {
            // The order amount in this context is the difference between the new value in the stock value change
            // document line and the current value.
            orderAmount: amount,

            // For average cost, the valued amount is the same as the order amount
            valuedAmount: amount,

            // No non-absorbed amount.
            nonAbsorbedAmount: 0,
        });
    }

    protected async initializeCorrection() {
        if (this.valuationParameter.valuationType !== 'receipt') {
            throw new LogicError('Price correction is only managed for receipts');
        }

        if (!(this.stockDetails[0] instanceof xtremStockData.nodes.StockCorrectionDetail)) {
            throw new LogicError(
                `stockDetails is not an instance of StockCorrectionDetail ${JSON.stringify(
                    this.stockDetails,
                    null,
                    4,
                )}`,
            );
        }

        // As we can have multiple stock correction details for the same document line with different origins (this is the case with Landed costs on PO),
        // and as the correction of each document line is function the remaining quantity in the corresponding FIFO tier
        // the absorption must be calculated by group of document line / origin document lines.
        const buildGroupKey = async (
            stockCorrectionDetail: xtremStockData.nodes.StockCorrectionDetail,
        ): Promise<string> => `${(await stockCorrectionDetail.originDocumentLine)?._id ?? 0}`;

        const totalByOrigin = await this.getCorrectionTotalsByGroup(stockCorrectionDetail =>
            buildGroupKey(stockCorrectionDetail),
        );

        await asyncArray(Object.keys(totalByOrigin)).forEach(async originId => {
            totalByOrigin[originId].totalAbsorbedAmount = StockValuationManager.calculateAbsorbedAmount(
                await this.itemSite.inStockQuantity,
                totalByOrigin[originId].totalQuantityImpacted,
                totalByOrigin[originId].totalAmountToAbsorb,
            );
        });
        // Initialize amounts to absorb by origin document lines
        await asyncArray(this.stockDetails).forEach(
            async (stockCorrectionDetail: xtremStockData.nodes.StockCorrectionDetail) => {
                const documentLine = await stockCorrectionDetail.documentLine;
                const originId = await buildGroupKey(stockCorrectionDetail);
                const index = this.getGroupIndex(documentLine._id, stockCorrectionDetail._id);

                this.stockValuationGroups[index].orderAmount +=
                    totalByOrigin[originId].totalAmountToAbsorb !== 0
                        ? (totalByOrigin[originId].totalAbsorbedAmount * (await stockCorrectionDetail.amountToAbsorb)) /
                          totalByOrigin[originId].totalAmountToAbsorb
                        : 0;
                this.stockValuationGroups[index].valuedAmount += this.stockValuationGroups[index].orderAmount;
                this.stockValuationGroups[index].nonAbsorbedAmount +=
                    totalByOrigin[originId].totalAmountToAbsorb !== 0
                        ? (await stockCorrectionDetail.amountToAbsorb) - this.stockValuationGroups[index].orderAmount
                        : 0;
            },
        );
    }

    async finish(writableContext: Context): Promise<void> {
        this.context = writableContext;

        if (this.valuationParameter.valuationType === 'valueChange') {
            await this.finishValueChange();
        }

        if ((this.valuationParameter.valuationType === 'receipt' && this.action) === 'correctValue') {
            await this.finishCorrection();
        }
    }

    private async finishValueChange() {
        await this.updateStockValuationAtAverageCost();
    }

    private async finishCorrection() {
        await this.updateStockValuationAtAverageCost();
    }

    private async updateStockValuationAtAverageCost() {
        // Get the corresponding item-site...
        const itemSite = await this.context.read(
            xtremMasterData.nodes.ItemSite,
            {
                item: this.item,
                site: this.site,
            },
            { forUpdate: true },
        );
        // ... and update the average cost value.
        await itemSite.$.set({
            stockValuationAtAverageCost: (await itemSite.stockValuationAtAverageCost) + this.getTotalOrderAmount(),
        });
        await itemSite.$.save();
    }
}
