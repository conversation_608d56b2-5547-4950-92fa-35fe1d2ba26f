import * as xtremCommunication from '@sage/xtrem-communication';
import { Activity } from '@sage/xtrem-core';
import * as xtremFinanceData from '@sage/xtrem-finance-data';
import * as xtremMasterData from '@sage/xtrem-master-data';
import { commonStockActivities } from '../functions/authorization';
import { StockIssue } from '../nodes/stock-issue';

export const stockIssue = new Activity({
    description: 'Stock issue',
    node: () => StockIssue,
    __filename,
    permissions: ['read', 'manage', 'post'],
    operationGrants: {
        read: [
            { operations: ['lookup'], on: [() => xtremMasterData.nodes.LocationZone] },
            ...xtremFinanceData.functions.dimensionsAndAttributesOperations,
            ...commonStockActivities,
        ],
        manage: [
            {
                operations: ['lookup', 'create', 'update', 'delete'],
                on: [() => StockIssue],
            },
            {
                operations: ['getValuationCost', 'lookup'],
                on: [() => xtremMasterData.nodes.ItemSite],
            },
            { operations: ['lookup'], on: [() => xtremMasterData.nodes.LocationZone] },
            ...xtremFinanceData.functions.dimensionsAndAttributesOperations,
            ...commonStockActivities,
        ],
        post: [
            { operations: ['read', 'postToStock', 'repost', 'resendNotificationForFinance'], on: [() => StockIssue] },
            {
                operations: ['read'],
                on: [() => xtremCommunication.nodes.SysNotificationHistory],
            },
            ...commonStockActivities,
        ],
    },
});
