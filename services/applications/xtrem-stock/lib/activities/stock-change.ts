import type { OperationGrant } from '@sage/xtrem-core';
import { Activity } from '@sage/xtrem-core';
import * as xtremStockData from '@sage/xtrem-stock-data';
import { commonStockActivities } from '../functions/authorization';
import { StockChange } from '../nodes/stock-change';

const { stockReceiptOperations, stockIssueOperations } = xtremStockData.functions.stockDetailLib;
const commonStockChangeOperations: OperationGrant[] = [
    ...commonStockActivities,
    ...stockReceiptOperations,
    ...stockIssueOperations,
];

export const stockChange = new Activity({
    description: 'Stock change',
    node: () => StockChange,
    __filename,
    permissions: ['read', 'manage', 'post'],
    operationGrants: {
        read: [...commonStockChangeOperations],
        manage: [
            {
                operations: ['lookup', 'create', 'update', 'delete'],
                on: [() => StockChange],
            },
            {
                operations: ['queryRange'],
                on: [() => xtremStockData.nodes.SerialNumber],
            },
            ...commonStockChangeOperations,
        ],
        post: [
            {
                operations: ['read', 'postToStock'],
                on: [() => StockChange],
            },
            ...commonStockChangeOperations,
        ],
    },
});
