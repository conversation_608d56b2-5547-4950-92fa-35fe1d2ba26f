import * as xtremCommunication from '@sage/xtrem-communication';
import { Activity } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import { commonStockActivities } from '../functions/authorization';
import { StockAdjustment } from '../nodes/stock-adjustment';

export const stockAdjustment = new Activity({
    description: 'Stock adjustment',
    node: () => StockAdjustment,
    __filename,
    permissions: ['read', 'manage', 'post'],
    operationGrants: {
        read: [...commonStockActivities],
        manage: [
            ...commonStockActivities,
            {
                operations: ['create', 'update', 'delete', 'resynchronizeStockTransactionStatus'],
                on: [() => StockAdjustment],
            },
            {
                operations: ['getValuationCost'],
                on: [() => xtremMasterData.nodes.ItemSite],
            },
        ],
        post: [
            ...commonStockActivities,
            {
                operations: ['read'],
                on: [() => StockAdjustment],
            },
            {
                operations: ['postToStock', 'repost', 'resendNotificationForFinance'],
                on: [() => StockAdjustment],
            },
            {
                operations: ['read'],
                on: [() => xtremCommunication.nodes.SysNotificationHistory],
            },
        ],
    },
});
