import { Activity } from '@sage/xtrem-core';
import { StockReorderCalculationInputSet } from '../nodes/stock-reorder-calculation-input-set';

export const stockReorder = new Activity({
    description: 'Stock reorder',
    node: () => StockReorderCalculationInputSet,
    __filename,
    permissions: ['manage'],
    operationGrants: {
        manage: [
            {
                operations: ['read', 'create', 'update', 'reorderCalculation'],
                on: [() => StockReorderCalculationInputSet],
            },
        ],
    },
});
