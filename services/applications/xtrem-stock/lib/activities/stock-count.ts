import * as xtremCommunication from '@sage/xtrem-communication';
import type { OperationGrant } from '@sage/xtrem-core';
import { Activity } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremStockData from '@sage/xtrem-stock-data';
import { commonStockActivities } from '../functions/authorization';
import { StockCount } from '../nodes/stock-count';
import { StockCountLine } from '../nodes/stock-count-line';

const { stockReceiptOperations, stockIssueOperations } = xtremStockData.functions.stockDetailLib;
const commonStockCountOperations: OperationGrant[] = [
    ...commonStockActivities,
    ...stockReceiptOperations,
    ...stockIssueOperations,
];

export const stockCount = new Activity({
    description: 'Stock count',
    node: () => StockCount,
    __filename,
    permissions: ['read', 'manage', 'start', 'post'],
    operationGrants: {
        read: [
            {
                operations: ['read'],
                on: [() => xtremMasterData.nodes.ReasonCode],
            },
            ...commonStockCountOperations,
        ],
        manage: [
            {
                operations: [
                    'create',
                    'update',
                    'delete',
                    'existingStockCountLine',
                    'confirmStockCount',
                    'confirmZeroQuantity',
                ],
                on: [() => StockCount],
            },
            {
                operations: ['getStockQuantity'],
                on: [() => StockCountLine],
            },
            ...commonStockCountOperations,
        ],
        start: [...commonStockCountOperations],
        post: [
            {
                operations: ['read', 'postToStock', 'repost', 'resendNotificationForFinance'],
                on: [() => StockCount],
            },
            {
                operations: ['read'],
                on: [() => xtremCommunication.nodes.SysNotificationHistory],
            },
            ...commonStockCountOperations,
        ],
    },
});
