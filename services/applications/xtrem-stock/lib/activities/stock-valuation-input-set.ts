import { Activity } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremStockData from '@sage/xtrem-stock-data';
import * as xtremSystem from '@sage/xtrem-system';
import { commonStockActivities } from '../functions/authorization';
import { StockValuationInputSet } from '../nodes/stock-valuation-input-set';

export const stockValuationInputSet = new Activity({
    description: 'Stock valuation',
    node: () => StockValuationInputSet,
    __filename,
    permissions: ['read', 'manageCost'],

    operationGrants: {
        read: [
            ...commonStockActivities,
            {
                operations: ['lookup'],
                on: [() => StockValuationInputSet],
            },
            {
                operations: ['lookup'],
                on: [() => xtremSystem.nodes.User],
            },
        ],
        manageCost: [
            ...commonStockActivities,
            {
                operations: ['lookup', 'create', 'update', 'delete', 'stockValuation'],
                on: [() => StockValuationInputSet],
            },
            {
                operations: ['lookup'],
                on: [() => xtremSystem.nodes.User, () => xtremStockData.nodes.StockJournal],
            },
            { operations: ['getValuedItemSite', 'getStockValuation'], on: [() => xtremMasterData.nodes.ItemSite] },
        ],
    },
});
