import { Activity } from '@sage/xtrem-core';
import * as xtremStockData from '@sage/xtrem-stock-data';
import { commonStockActivities } from '../functions/authorization';
import { StockValueChange } from '../nodes/stock-value-change';

export const stockValueChange = new Activity({
    description: 'Stock value change',
    node: () => StockValueChange,
    __filename,
    permissions: ['read', 'manage', 'post'],
    operationGrants: {
        read: [
            {
                operations: ['lookup'],
                on: [() => xtremStockData.nodes.FifoValuationTier],
            },
            ...commonStockActivities,
        ],
        manage: [
            {
                operations: ['create', 'update', 'delete', 'financeIntegrationCheck'],
                on: [() => StockValueChange],
            },
            {
                operations: ['lookup'],
                on: [() => xtremStockData.nodes.FifoValuationTier],
            },
            ...commonStockActivities,
        ],
        post: [
            {
                operations: ['read', 'repost', 'postToStock', 'resendNotificationForFinance'],
                on: [() => StockValueChange],
            },
            ...commonStockActivities,
            {
                operations: ['lookup'],
                on: [() => xtremStockData.nodes.FifoValuationTier],
            },
        ],
    },
});
