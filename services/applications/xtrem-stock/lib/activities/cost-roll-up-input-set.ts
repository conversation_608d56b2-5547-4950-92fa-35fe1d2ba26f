import { Activity } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremStock from '..';
import { commonStockActivities } from '../functions/authorization';

export const costRollUpInputSet = new Activity({
    description: 'Standard cost roll-up',
    node: () => xtremStock.nodes.CostRollUpInputSet,
    __filename,
    permissions: ['manage'],
    operationGrants: {
        manage: [
            {
                operations: ['read', 'create', 'update', 'updateAttributesAndDimensions'],
                on: [() => xtremStock.nodes.CostRollUpInputSet],
            },
            { operations: ['standardCostRollUpCalculation'], on: [() => xtremMasterData.nodes.ItemSiteCost] },
            {
                operations: ['createItemSiteCostsFromCostRollUpResults'],
                on: [() => xtremStock.nodes.CostRollUpResultLine],
            },
            ...commonStockActivities,
        ],
    },
});
