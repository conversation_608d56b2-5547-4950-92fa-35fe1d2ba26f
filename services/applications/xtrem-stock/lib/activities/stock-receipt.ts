import * as xtremCommunication from '@sage/xtrem-communication';
import type { OperationGrant } from '@sage/xtrem-core';
import { Activity } from '@sage/xtrem-core';
import * as xtremStockData from '@sage/xtrem-stock-data';
import { commonStockActivities } from '../functions/authorization';
import { StockReceipt } from '../nodes/stock-receipt';

const { stockReceiptOperations } = xtremStockData.functions.stockDetailLib;
const commonStockReceiptOperations: OperationGrant[] = [...commonStockActivities, ...stockReceiptOperations];

export const stockReceipt = new Activity({
    description: 'Stock receipt',
    node: () => StockReceipt,
    __filename,
    permissions: ['read', 'manage', 'post'],
    operationGrants: {
        read: [...commonStockReceiptOperations],
        manage: [
            {
                operations: ['create', 'update', 'delete'],
                on: [() => StockReceipt],
            },
            ...commonStockReceiptOperations,
        ],
        post: [
            {
                operations: ['read', 'postToStock', 'repost', 'resendNotificationForFinance'],
                on: [() => StockReceipt],
            },
            {
                operations: ['read'],
                on: [() => xtremCommunication.nodes.SysNotificationHistory],
            },
            ...commonStockReceiptOperations,
        ],
    },
});
