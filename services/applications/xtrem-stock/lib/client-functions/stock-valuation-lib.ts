import type { Filter } from '@sage/xtrem-client';
import { extractEdges } from '@sage/xtrem-client';
import type { ItemSite } from '@sage/xtrem-master-data-api';
import type { StockDetailSerialNumberBinding, StockJournal } from '@sage/xtrem-stock-data-api';
import * as StockDetailHelper from '@sage/xtrem-stock-data/build/lib/client-functions/stock-details-helper';
import type { StockReceiptDetailPageInput } from '@sage/xtrem-stock-data/lib/client-functions/interfaces';
import * as ui from '@sage/xtrem-ui';
import type { StockAdjustment } from '../pages/stock-adjustment';
import type { StockAvcJustificationInquiry } from '../pages/stock-avc-justification-inquiry';
import type { StockIssue } from '../pages/stock-issue';
import type { StockReceipt } from '../pages/stock-receipt';
import type { StockReorderCalculation } from '../pages/stock-reorder-calculation';
import type {
    ItemSelectionLine,
    StockMovement,
    StockReceiptLinePageBinding,
    ValuedItemSiteResult,
    ValuedItemSitesSearch,
} from './interfaces';

export function currencyScale(currency: any) {
    return currency?.decimalDigits || 2;
}

export function currencySymbol(currency: any) {
    return currency?.symbol || '';
}

export function stockUnitScale(stockUnit: any) {
    return stockUnit?.decimalDigits || 2;
}

export function stockUnitSymbol(stockUnit: any) {
    return stockUnit?.symbol || '';
}

/** Condition to hide the site column */
export function isSiteHidden(page: StockAvcJustificationInquiry) {
    return page.sites.value.length === 1;
}

/** Display the Search table instead of Result table and clear both tables
 * @param {boolean} reset : true -> clear both tables
 */
export function displaySearchTable(page: StockAvcJustificationInquiry, reset: boolean) {
    page.itemSelection.isHidden = false;
    page.itemValues.isHidden = true;
    if (reset) {
        page.itemSelection.value = [];
        page.itemValues.value = [];
    }
}

/** What to do when a search criteria is modified */
export function changeSearchCriteria(page: StockAvcJustificationInquiry) {
    displaySearchTable(page, true);
}

/** Display the Result table instead of Selection table
 * @param {boolean} reset : true --> clear the result table
 */
export function displayRunTable(page: StockAvcJustificationInquiry, reset: boolean) {
    page.itemSelection.isHidden = true;
    page.itemValues.isHidden = false;
    if (reset) {
        page.itemValues.value = [];
    }
}

async function getSearchedItemSiteValues(
    page: StockAvcJustificationInquiry | StockReorderCalculation,
    searchCriteria: ValuedItemSitesSearch,
) {
    return (await page.$.graph
        .node('@sage/xtrem-master-data/ItemSite')
        .queries.getValuedItemSite(
            {
                _id: true,
                item: {
                    _id: true,
                    id: true,
                    name: true,
                    description: true,
                    stockUnit: { name: true, symbol: true },
                },
                site: { _id: true, name: true },
            },
            { searchCriteria },
        )
        .execute()) as Array<ItemSite>;
}

/**
 * Fill in the Selection table from criteria
 */
export async function search(
    page: StockAvcJustificationInquiry | StockReorderCalculation,
    searchCriteria: ValuedItemSitesSearch,
): Promise<ItemSelectionLine[]> {
    if (!searchCriteria.preferredProcesses) {
        delete searchCriteria.preferredProcesses;
    }
    if (!searchCriteria.replenishmentMethods) {
        delete searchCriteria.replenishmentMethods;
    }
    const results = await getSearchedItemSiteValues(page, searchCriteria);

    return results.map(itemSite => ({
        _id: itemSite._id,
        item: itemSite.item,
        site: itemSite.site,
        stockUnit: itemSite.item.stockUnit,
    }));
}

function getItemSiteStockValuation(page: StockAvcJustificationInquiry, _id: string, valuationDate: string) {
    return page.$.graph
        .node('@sage/xtrem-master-data/ItemSite')
        .queries.getStockValuation(
            { quantityInStock: true, stockValue: true, unitCost: true },
            {
                searchCriteria: { itemSite: _id, dateOfValuation: valuationDate },
            },
        )
        .execute();
}

function getItemSite(page: StockAvcJustificationInquiry, _id: string) {
    return page.$.graph
        .node('@sage/xtrem-master-data/ItemSite')
        .read(
            {
                _id: true,
                item: {
                    _id: true,
                    id: true,
                    name: true,
                    postingClass: { name: true },
                    stockUnit: { id: true, name: true, decimalDigits: true, symbol: true },
                },
                site: {
                    _id: true,
                    name: true,
                    legalCompany: { name: true, currency: { symbol: true, decimalDigits: true } },
                },
                valuationMethod: true,
            },
            _id,
        )
        .execute();
}

interface StockJournalWithCreateStamp {
    _id: string;
    site: {
        _id: string;
        id: string;
        financialCurrency: { _id: string; id: string; symbol: string; decimalDigits: number };
    };
    item: { _id: string; id: string };
    stockDetail: { documentLine: { documentNumber: string } };
    documentType: string;
    _createStamp: string | undefined;
    sequence: number;
    effectiveDate: string;
    orderCost: string;
    orderAmount: string;
    valuedCost: string;
    movementAmount: string;
    costVariance: string;
    amountVariance: string;
    quantityInStockUnit: string;
    stockUnit: { _id: string; id: string; decimalDigits: number; symbol: string };
}

async function getStockJournals(
    page: StockAvcJustificationInquiry,
    filter: Filter<StockJournal>,
): Promise<StockJournalWithCreateStamp[]> {
    return extractEdges(
        await page.$.graph
            .node('@sage/xtrem-stock-data/StockJournal')
            .query(
                ui.queryUtils.edgesSelector(
                    {
                        _id: true,
                        site: {
                            _id: true,
                            id: true,
                            financialCurrency: { _id: true, id: true, symbol: true, decimalDigits: true },
                        },
                        item: { _id: true, id: true },
                        stockDetail: { documentLine: { documentNumber: true } },
                        documentType: true,
                        _createStamp: true,
                        sequence: true,
                        effectiveDate: true,
                        orderCost: true,
                        orderAmount: true,
                        valuedCost: true,
                        movementAmount: true,
                        costVariance: true,
                        amountVariance: true,
                        quantityInStockUnit: true,
                        stockUnit: { _id: true, id: true, decimalDigits: true, symbol: true },
                    },
                    {
                        // Keep the order of creation
                        orderBy: { _id: 1 },
                        filter,
                        first: 500,
                    },
                ),
            )
            .execute(),
    );
}

function getStockMovementsFromStockJournals(stockJournals: StockJournalWithCreateStamp[]): StockMovement[] {
    // stockMovementRaw gets the result of the query where _createStamp exists but not creationDate
    const stockMovements: StockMovement[] = [];
    stockJournals.forEach(stockJournal => {
        // creationDate is the first 10 characters of _createStamp
        const creationDate = stockJournal._createStamp?.substring(0, 10) ?? '';
        // delete _createStamp to respect StockMovement interface
        delete stockJournal._createStamp;
        // add the stockJournal to the stockMovements array
        stockMovements.push({
            ...stockJournal,
            creationDate,
            stockQuantity: 0,
            stockValue: 0,
            cost: 0,
            type: '',
        });
    });
    return stockMovements;
}

/**
 * Return the valuation report information for a list of items-sites
 * @param {string[]} itemSiteIds array of _id of ItemSite
 * @param {string} valuationDate date of valuation
 * @returns array of valuation information: contains stock valuation for the date requested  + the last valuation
 */
export async function getValuationOfItemsSites(
    page: StockAvcJustificationInquiry,
    itemSiteIds: string[],
    valuationDate: string,
    options?: {
        withMovements?: boolean;
    },
): Promise<ValuedItemSiteResult[]> {
    // getStockValuation doesn't return the input parameters
    // but, here, we need it to fill in the table
    // 1- 'results' gets a list of Promise where the result will be the output of the query + _id + site...
    const results = itemSiteIds.map(_id =>
        (async () => {
            const result = await getItemSiteStockValuation(page, _id, valuationDate);

            const itemSite = await getItemSite(page, _id);

            let stockMovements: StockMovement[] = [];
            if (options?.withMovements) {
                const filter: Filter<StockJournal> = {
                    item: { _id: { _eq: itemSite.item._id } },
                    site: { _id: { _eq: itemSite.site._id } },
                    ...(valuationDate ? { effectiveDate: { _lte: valuationDate } } : {}),
                };
                const stockJournals = await getStockJournals(page, filter);
                if (stockJournals.length) {
                    stockMovements = getStockMovementsFromStockJournals(stockJournals);
                }
            }
            return {
                ...result,
                _id,
                item: itemSite.item,
                site: itemSite.site,
                company: itemSite.site.legalCompany,
                postingClass: itemSite.item.postingClass,
                stockUnit: itemSite.item.stockUnit,
                currency: itemSite.site.legalCompany.currency,
                dateOfValuation: valuationDate,
                valuationMethod: itemSite.valuationMethod,
                stockMovements,
            } as ValuedItemSiteResult;
        })(),
    );
    // 2- when all the Promise are successful, the results are passed in the table
    return (await Promise.all(results)).filter(val => val !== null) as ValuedItemSiteResult[];
}

/**
 * Called on stock pages StockReceipt, StockIssue and StockAdjustment before saving:
 * Check if the serial numbers on all new (or updated) lines are unique
 * @param {StockReceipt | StockAdjustment | StockIssue} page page from which the validateSerialNumbers is called
 * @returns array of valuation messages in case of errors, else empty array
 */
export function validateSerialNumbers(page: StockReceipt | StockAdjustment | StockIssue): string[] {
    // check if the serial numbers on all new (or updated) lines are unique
    const serialNumbersOnNewOrUpdatedLines: Array<{
        receiptLineId: string;
        stockDetailId: string | undefined;
        itemId: string | undefined;
        serialNumberId: string;
    }> = (page.lines as ui.fields.TableControlObject<StockReceiptLinePageBinding>).value
        .filter(
            stockReceiptLine =>
                stockReceiptLine.item?.serialNumberManagement &&
                stockReceiptLine.item?.serialNumberManagement === 'managed' &&
                stockReceiptLine.jsonStockDetails,
        )
        .flatMap(stockReceiptLine =>
            (
                (StockDetailHelper.parseCreationDetails(stockReceiptLine.jsonStockDetails) ||
                    []) as Array<StockReceiptDetailPageInput>
            )
                .filter((stockDetail: StockReceiptDetailPageInput) => stockDetail.jsonStockDetailSerialNumbers)
                .flatMap(stockDetailWithSerialNumbers =>
                    (
                        JSON.parse(
                            stockDetailWithSerialNumbers.jsonStockDetailSerialNumbers || '[]',
                        ) as Array<StockDetailSerialNumberBinding>
                    ).map(stockDetailSerialNumber => ({
                        serialNumberId:
                            stockDetailSerialNumber.serialNumber?.id || stockDetailSerialNumber.newSerialNumberId,
                        stockDetailId: stockDetailWithSerialNumbers._id,
                    })),
                )
                .map(serialNumber => ({
                    ...serialNumber,
                    receiptLineId: stockReceiptLine._id,
                    itemId: stockReceiptLine.item?.id,
                })),
        );

    let validationMessages: string[] = serialNumbersOnNewOrUpdatedLines
        .filter(serialNumber =>
            serialNumbersOnNewOrUpdatedLines.some(
                controlSerialNumber =>
                    controlSerialNumber.serialNumberId === serialNumber.serialNumberId &&
                    controlSerialNumber.itemId === serialNumber.itemId &&
                    controlSerialNumber.receiptLineId !== serialNumber.receiptLineId,
            ),
        )
        .map(repeatedSerialNumber =>
            ui.localize(
                '@sage/xtrem-stock/pages__stock_receipt__serial number_exists_on_another_line',
                'Serial number {{serialNumberId}} for item {{itemId}} already exists on another document line.',
                {
                    serialNumberId: repeatedSerialNumber.serialNumberId,
                    itemId: repeatedSerialNumber.itemId,
                },
            ),
        );
    validationMessages = validationMessages.filter((item, index) => validationMessages.indexOf(item) === index);

    if (validationMessages && !!validationMessages.length) {
        return validationMessages;
    }

    return [];
}
