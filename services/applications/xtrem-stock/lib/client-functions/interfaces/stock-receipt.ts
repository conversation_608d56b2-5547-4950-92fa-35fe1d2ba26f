import type { ExtractEdgesPartial } from '@sage/xtrem-client';
import type { GraphApi, StockReceiptLineBinding } from '@sage/xtrem-stock-api';
import type { LotBinding } from '@sage/xtrem-stock-data-api';
import type { Site } from '@sage/xtrem-system-api';
import type * as ui from '@sage/xtrem-ui';

export interface StockReceiptLinePageBinding extends StockReceiptLineBinding {
    lotNumber: LotBinding['id'];
    expirationDate: LotBinding['expirationDate'];
}

export interface StockReceiptPageParameter {
    stockReceiptPage: ui.Page<GraphApi>;
    recordNumber: string;
}
export interface SetDimensionActionParameter extends StockReceiptPageParameter {
    status: string | null;
    site?: ExtractEdgesPartial<Site> | null;
}
export interface ActionParameters {
    stockReceiptPage: ui.Page<GraphApi>;
    recordId: string;
}

export interface StockReceiptStepSequenceStatus {
    create: ui.StepSequenceStatus;
    detailStock: ui.StepSequenceStatus;
    postStock: ui.StepSequenceStatus;
}
