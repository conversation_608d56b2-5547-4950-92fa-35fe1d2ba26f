import type { ExtractEdgesPartial } from '@sage/xtrem-client';
import type { GraphApi, StockIssueLineBinding } from '@sage/xtrem-stock-api';
import type { Site } from '@sage/xtrem-system-api';
import type * as ui from '@sage/xtrem-ui';

export interface StockIssueLinePageBinding extends StockIssueLineBinding {}

export type HidableStockIssuePanelColumns = {
    quantityToIssue?: true;
    availableQuantity?: true;
    stockUnit?: true;
    lot?: true;
    expirationDate?: true;
    status?: true;
    location?: true;
    owner?: true;
};

export interface StockIssuePageParameter {
    stockIssuePage: ui.Page<GraphApi>;
    recordNumber: string;
}
export interface IssueSetDimensionActionParameter extends StockIssuePageParameter {
    status: string | null;
    site?: ExtractEdgesPartial<Site> | null;
}
export interface IssueActionParameters {
    stockIssuePage: ui.Page<GraphApi>;
    recordId: string;
}

export interface StockIssueStepSequenceStatus {
    create: ui.StepSequenceStatus;
    detailStock: ui.StepSequenceStatus;
    postStock: ui.StepSequenceStatus;
}
