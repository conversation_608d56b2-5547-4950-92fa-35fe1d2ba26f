import type { ExtractEdgesPartial } from '@sage/xtrem-client';
import type { GraphApi, Site } from '@sage/xtrem-system-api';
import type * as ui from '@sage/xtrem-ui';

export interface StockAdjustmentStatusParameter {
    stockAdjustmentPage: ui.Page<GraphApi>;
    stockAdjustmentId: string;
}

export interface StockAdjustmentPageParameter {
    stockAdjustmentPage: ui.Page<GraphApi>;
    recordNumber: string;
}
export interface AdjustmentSetDimensionActionParameter extends StockAdjustmentPageParameter {
    status: string | null;
    site?: ExtractEdgesPartial<Site> | null;
}
export interface AdjustmentActionParameters {
    stockAdjustmentPage: ui.Page<GraphApi>;
    recordId: string;
}

export interface StockAdjustmentStepSequenceStatus {
    create: ui.StepSequenceStatus;
    detailStock: ui.StepSequenceStatus;
    postStock: ui.StepSequenceStatus;
}
