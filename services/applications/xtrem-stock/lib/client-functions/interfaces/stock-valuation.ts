import type { decimal, integer } from '@sage/xtrem-client';
import type {
    CostValuationMethod,
    Item,
    ItemSite,
    ItemStatus,
    PreferredProcess,
    ReplenishmentMethod,
    UnitOfMeasure,
} from '@sage/xtrem-master-data-api';
import type { Site } from '@sage/xtrem-system-api';

export interface ItemSelectionLine {
    _id: string;
    item: Item;
    site: Site;
    stockUnit: UnitOfMeasure;
}

// based on the same name interface in master-data
export interface ValuedItemSitesSearch {
    company?: integer;
    stockSiteList?: string[];
    itemRange?: { start?: string; end?: string };
    statusList?: ItemStatus[];
    valuationMethods?: CostValuationMethod[];
    preferredProcesses?: PreferredProcess[];
    replenishmentMethods?: ReplenishmentMethod[];
}

export interface StockReorderRecommendationRecord {
    itemSite: ItemSite;
    day: Date;
    demand: decimal;
    supply: decimal;
    stockLevel: decimal;
    reorderLevel: decimal;
    safetyLevel: decimal;
    recommendationQuantity: decimal;
}
export interface StockMovement {
    _id: string;
    site: {
        _id: string;
        id: string;
        financialCurrency: { _id: string; id: string; symbol: string; decimalDigits: integer };
    };
    item: { _id: string; id: string };
    stockDetail: { documentLine: { documentNumber: string } };
    documentType: string;
    creationDate: string;
    sequence: integer;
    effectiveDate: string;
    orderCost: string;
    orderAmount: string;
    valuedCost: string;
    movementAmount: string;
    costVariance: string;
    amountVariance: string;
    quantityInStockUnit: string;
    stockQuantity: decimal;
    stockValue: decimal;
    cost: decimal;
    type: string;
    stockUnit: { _id: string; id: string; decimalDigits: integer; symbol: string };
}

export interface ValuedItemSiteResult {
    stockValue: string;
    quantityInStock: string;
    unitCost: string;
    _id: string;
    item: { id: string; name: string };
    site: { name: string };
    company: { name: string };
    postingClass: { name: string };
    stockUnit: {
        id: string;
        name: string;
        decimalDigits: integer;
        symbol: string;
    };
    currency: { symbol: string; decimalDigits: integer };
    dateOfValuation: string;
    valuationMethod: string;
    stockMovements?: StockMovement[];
}
