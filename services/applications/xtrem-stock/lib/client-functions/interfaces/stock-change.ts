import type { decimal, integer } from '@sage/xtrem-shared';
import type { StockChangeLineBinding } from '@sage/xtrem-stock-api';
import type { SerialNumber } from '@sage/xtrem-stock-data-api';

export interface StockChangeLinePageBinding extends StockChangeLineBinding {
    stockDetailId: string;
    jsonStockDetailSerialNumbers: string;
    jsonStockDetailSerialNumberRange: string;
    startingSerialNumber: SerialNumber | null;
    endingSerialNumber: SerialNumber | null;
    numericStart: integer;
    numericEnd: integer;
    originalStartId: string;
}

export interface StockChangeDetailSerialNumberPageBinding {
    startingSerialNumber: SerialNumber | null;
    quantityInStockUnit: decimal | string;
    endingSerialNumber: SerialNumber | null;
    numericStart: integer;
    numericEnd: integer;
    originalStartId: string;
}
