import type { decimal, ExtractEdgesPartial, integer, VitalClientNode } from '@sage/xtrem-client';
import type { ItemBinding, UnitOfMeasureBinding } from '@sage/xtrem-master-data-api';
import type { StockCountLine, StockCountLineBinding, StockCountLineSerialNumber } from '@sage/xtrem-stock-api';
import type { SerialNumber } from '@sage/xtrem-stock-data-api';
import type { SiteBinding } from '@sage/xtrem-system-api';

export interface StockCountPageLine extends StockCountLineBinding {
    jsonStockDetailLot?: string;
    lotSelection?: string;
    sublot?: string;
    expirationDate?: string;
    supplierLot?: string;
    isModified?: true;
}

export interface SerialNumberNumericRange {
    startingSerialNumberId: SerialNumber['id'];
    quantity: integer;
    endingSerialNumberId: SerialNumber['id'];
    numericStart: number;
    numericEnd: number;
    originalStartId: string;
}
export interface StockCountLineSerialNumberNumericRange
    extends Omit<SerialNumberNumericRange, 'startingSerialNumber' | 'endingSerialNumber'> {
    startingSerialNumber: StockCountLineSerialNumber;
    endingSerialNumber: StockCountLineSerialNumber;
}
export type StockCountLineSerialNumberRangePageBinding = VitalClientNode &
    ExtractEdgesPartial<StockCountLineSerialNumberNumericRange>;

export interface StockCountSerialNumberPanelParameters {
    isEditable: boolean;
    jsonSerialNumbers?: string;
    stockCountLineId: StockCountLine['_id'];
    item?: ItemBinding['_id'];
    stockSite?: SiteBinding['_id'];
    quantityInStockUnit?: decimal;
    countedQuantity?: decimal;
    unit?: UnitOfMeasureBinding['_id'];
}
