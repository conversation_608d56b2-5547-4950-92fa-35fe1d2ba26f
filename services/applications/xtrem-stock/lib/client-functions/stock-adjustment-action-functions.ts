import type { Dict } from '@sage/xtrem-client';
import { extractEdges } from '@sage/xtrem-client';
import type { StockAdjustmentDisplayStatus } from '@sage/xtrem-stock-api';
import * as ui from '@sage/xtrem-ui';
import { getDimensionsForPurchaseLines, getValuesForSetDimensionsFromMainList } from './common';
import type {
    AdjustmentActionParameters,
    AdjustmentSetDimensionActionParameter,
    StockAdjustmentPageParameter,
    StockAdjustmentStepSequenceStatus,
} from './interfaces/stock-adjustments';

export async function getStockAdjustmentLines(parameters: StockAdjustmentPageParameter) {
    return extractEdges(
        await parameters.stockAdjustmentPage.$.graph
            .node('@sage/xtrem-stock/StockAdjustmentLine')
            .query(
                ui.queryUtils.edgesSelector(
                    {
                        _id: true,
                        storedDimensions: true,
                        storedAttributes: true,
                        stockDetailStatus: true,
                        document: {
                            number: true,
                        },
                        stockTransactionStatus: true,
                    },

                    {
                        filter: {
                            document: {
                                number: parameters.recordNumber,
                            },
                        },
                    },
                ),
            )
            .execute(),
    );
}

async function getDisplayStatus(parameters: AdjustmentActionParameters) {
    return (
        await parameters.stockAdjustmentPage.$.graph
            .node('@sage/xtrem-stock/StockAdjustment')
            .read(
                {
                    displayStatus: true,
                },
                parameters.recordId,
            )
            .execute()
    ).displayStatus;
}

export async function checkForUpdate(parameters: AdjustmentActionParameters) {
    // TODO: This is a hack. It can be removed when XT-23948 is ready.
    let refreshCounter = 0;
    const checkForDisplayStatus = async () => {
        const displayStatus = await getDisplayStatus({
            stockAdjustmentPage: parameters.stockAdjustmentPage,
            recordId: parameters.recordId,
        });
        if (['adjusted', 'stockPostingError'].includes(displayStatus)) {
            await parameters.stockAdjustmentPage.$.router.refresh(true);
            await parameters.stockAdjustmentPage.$.refreshNavigationPanel();
            parameters.stockAdjustmentPage.$.loader.isHidden = true;
            return;
        }
        refreshCounter += 1;
        if (refreshCounter <= 5) {
            await new Promise(resolve => {
                setTimeout(() => resolve(checkForDisplayStatus()), 1000);
            });
        }
    };
    await checkForDisplayStatus();
}

const adjustmentStepSequenceCreate = ui.localize(
    '@sage/xtrem-stock/pages__stock_adjustment_step_sequence_creation',
    'Create',
);

const adjustmentStepSequenceDetailStock = ui.localize(
    '@sage/xtrem-stock/pages__stock__adjustment_step_sequence_detail_stock',
    'Detail stock',
);

const adjustmentStepSequencePostStock = ui.localize(
    '@sage/xtrem-stock/pages__stock_adjustment_step_sequence_post_stock',
    'Post stock',
);

export const adjustmentStepSequenceOptions = [
    adjustmentStepSequenceCreate,
    adjustmentStepSequenceDetailStock,
    adjustmentStepSequencePostStock,
];

function setStepSequenceStatusObject(
    stepSequenceValues: StockAdjustmentStepSequenceStatus,
): Dict<ui.StepSequenceStatus> {
    return {
        [adjustmentStepSequenceCreate]: stepSequenceValues.create,
        [adjustmentStepSequenceDetailStock]: stepSequenceValues.detailStock,
        [adjustmentStepSequencePostStock]: stepSequenceValues.postStock,
    };
}

export function getDisplayStatusStepSequence(
    recordId: string,
    displayStatus: StockAdjustmentDisplayStatus,
): Dict<ui.StepSequenceStatus> {
    if (!recordId) {
        return setStepSequenceStatusObject({
            create: 'current',
            detailStock: 'incomplete',
            postStock: 'incomplete',
        });
    }
    if (displayStatus === 'adjusted') {
        return setStepSequenceStatusObject({
            create: 'complete',
            detailStock: 'complete',
            postStock: 'complete',
        });
    }
    if (displayStatus === 'stockPostingInProgress' || displayStatus === 'stockPostingError') {
        return setStepSequenceStatusObject({
            create: 'complete',
            detailStock: 'complete',
            postStock: 'current',
        });
    }
    if (displayStatus === 'detailsEntered') {
        return setStepSequenceStatusObject({
            create: 'complete',
            detailStock: 'complete',
            postStock: 'incomplete',
        });
    }
    if (displayStatus === 'detailsRequired') {
        return setStepSequenceStatusObject({
            create: 'complete',
            detailStock: 'current',
            postStock: 'incomplete',
        });
    }
    return setStepSequenceStatusObject({
        create: 'current',
        detailStock: 'incomplete',
        postStock: 'incomplete',
    });
}

export async function setDimensions(parameters: AdjustmentSetDimensionActionParameter) {
    let dataDetermined = false;
    let dimensionsToSet = '';
    let attributesToSet = '';
    const adjustmentLines = await getStockAdjustmentLines({
        stockAdjustmentPage: parameters.stockAdjustmentPage,
        recordNumber: parameters.recordNumber,
    });
    const linesToSetDimensions = adjustmentLines.filter(
        line => !['inProgress', 'completed'].includes(line.stockTransactionStatus),
    );
    // eslint-disable-next-line no-restricted-syntax
    for (const lineToSetDimensions of linesToSetDimensions) {
        if (!dataDetermined) {
            const { defaultedFromItem, defaultDimensionsAttributes } = await getValuesForSetDimensionsFromMainList({
                stockPage: parameters.stockAdjustmentPage,
                site: parameters.site || null,
                supplier: null,
            });
            const { resultDimensionsToSet, resultAttributesToSet, resultDataDetermined } =
                await getDimensionsForPurchaseLines({
                    line: lineToSetDimensions,
                    stockPage: parameters.stockAdjustmentPage,
                    defaultDimensionsAttributes,
                    defaultedFromItem,
                });
            if (!resultDataDetermined) {
                break;
            }
            dimensionsToSet = resultDimensionsToSet;
            attributesToSet = resultAttributesToSet;
            dataDetermined = resultDataDetermined;
        }
        if (dataDetermined) {
            await parameters.stockAdjustmentPage.$.graph
                .node('@sage/xtrem-stock/StockAdjustmentLine')
                .mutations.setDimension(true, {
                    stockAdjustmentLine: lineToSetDimensions._id,
                    storedDimensions: dimensionsToSet,
                    storedAttributes: attributesToSet,
                })
                .execute();
        }
    }
    parameters.stockAdjustmentPage.$.showToast(
        ui.localize('@sage/xtrem-stock/pages__stock_adjustment__apply_dimensions_success', 'Dimensions applied'),
        { type: 'success' },
    );
}
