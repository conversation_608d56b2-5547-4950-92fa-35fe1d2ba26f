import type * as isDisabledButtonsCommon from '@sage/xtrem-system/build/lib/client-functions/interfaces/is-disabled-buttons';
import type * as isHiddenButtonsCommon from '@sage/xtrem-system/build/lib/client-functions/interfaces/is-hidden-buttons';
import type * as isDisabledButtons from './interfaces/is-disabled-buttons-stock-adjustment';
import type * as isHiddenButtons from './interfaces/is-hidden-buttons-stock-adjustment';

export function isHiddenButtonDeleteAction(
    data: isHiddenButtonsCommon.DeleteParameters<isHiddenButtons.StatusParameters>,
) {
    if (data.recordId && data.parameters.stockTransactionStatus) {
        return data.parameters.stockTransactionStatus !== 'draft';
    }

    return true;
}

export function isHiddenButtonGoToSysNotificationPageAction(
    data: isHiddenButtonsCommon.HeaderQuickActionButtonParameters<isHiddenButtons.GoToSysNotificationPageParameters>,
) {
    return (
        !data.recordId ||
        data.parameters.financeIntegrationStatus.every(financeIntegrationStatus =>
            ['toBeGenerated', 'posted'].includes(financeIntegrationStatus),
        )
    );
}

export function isDisabledLinePhantomRow(
    data: isDisabledButtonsCommon.PhantomRowButtonParameters<isDisabledButtons.StatusParameters>,
) {
    if (
        data.parameters &&
        data.parameters.displayStatus &&
        data.parameters.stockSite &&
        data.parameters.reasonCode &&
        !['adjusted', 'stockPostingError', 'stockPostingInProgress'].includes(data.parameters.displayStatus)
    ) {
        return false;
    }
    return true;
}
