import type * as isDisabledButtonsCommon from '@sage/xtrem-system/build/lib/client-functions/interfaces/is-disabled-buttons';
import type * as isHiddenButtonsCommon from '@sage/xtrem-system/build/lib/client-functions/interfaces/is-hidden-buttons';
import type * as isDisabledButtons from './interfaces/is-disabled-buttons-stock-receipt';
import type * as isHiddenButtons from './interfaces/is-hidden-buttons-stock-receipt';

export function isHiddenButtonDeleteAction(
    data: isHiddenButtonsCommon.DeleteParameters<isHiddenButtons.StatusParameters>,
) {
    if (data.recordId && data.parameters.stockTransactionStatus) {
        return data.parameters.stockTransactionStatus !== 'draft';
    }

    return true;
}

export function isDisabledLinePhantomRow(
    data: isDisabledButtonsCommon.PhantomRowButtonParameters<isDisabledButtons.StatusParameters>,
) {
    if (
        data.parameters &&
        data.parameters.displayStatus &&
        data.parameters.stockSite &&
        !['received', 'stockPostingError', 'stockPostingInProgress'].includes(data.parameters.displayStatus)
    ) {
        return false;
    }
    return true;
}
