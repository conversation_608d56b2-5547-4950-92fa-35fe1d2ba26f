import type { ExtractEdgesPartial } from '@sage/xtrem-client';
import * as attributesAndDimensions from '@sage/xtrem-finance-data/build/lib/client-functions/attributes-and-dimensions';
import * as dimensionPanelHelpers from '@sage/xtrem-finance-data/build/lib/client-functions/dimension-panel-helpers';
import type * as dimensionPanelInterfaces from '@sage/xtrem-finance-data/build/lib/client-functions/interfaces';
import type { Supplier } from '@sage/xtrem-master-data-api';
import type { GraphApi, Site } from '@sage/xtrem-system-api';
import * as ui from '@sage/xtrem-ui';
/**
 * Get a string with all attributes defaulted from item and default dimensions attributes to show on 'Set dimensions'
 * dialog called from main list
 * @param page
 * @param site
 * @param supplier
 * @returns Promise<{ defaultedFromItem: string; defaultDimensionsAttributes: dimensionPanelInterfaces.DefaultDimensions }>
 */
export async function getValuesForSetDimensionsFromMainList(options: {
    stockPage: ui.Page<GraphApi>;
    site: ExtractEdgesPartial<Site> | null;
    supplier: ExtractEdgesPartial<Supplier> | null;
}): Promise<{ defaultedFromItem: string; defaultDimensionsAttributes: dimensionPanelInterfaces.DefaultDimensions }> {
    const defaultedFromItem = await attributesAndDimensions.getAttributesAndDimensionsFromItem({
        page: options.stockPage,
        dimensionDefinitionLevel: 'stockDirect',
        companyId: Number(options.site?.legalCompany?._id),
    });
    const defaultDimensionsAttributes = await attributesAndDimensions.initDefaultDimensions({
        page: options.stockPage,
        dimensionDefinitionLevel: 'stockDirect',
        site: options.site || null,
        supplier: options.supplier,
    });

    return { defaultedFromItem, defaultDimensionsAttributes };
}

/**
 * Get dimensions and attributes to set them in purchase lines from main grid
 * @param line
 * @param purchasePage
 */
export async function getDimensionsForPurchaseLines(options: {
    line: ui.PartialNodeWithId<Node>;
    stockPage: ui.Page<GraphApi>;
    defaultDimensionsAttributes: dimensionPanelInterfaces.DefaultDimensions;
    defaultedFromItem?: string;
}) {
    const purchaseInvoiceBase = dimensionPanelHelpers.fromPartialToBaseDocumentLineWithAnalytical(options.line);
    const rowWithDimensions = dimensionPanelHelpers.editDisplayDimensions(
        options.stockPage,
        { documentLine: purchaseInvoiceBase },
        { editable: true, calledFromMainGrid: true },
        options.defaultDimensionsAttributes,
        options.defaultedFromItem,
    );
    if ((await rowWithDimensions).storedAttributes !== '' || (await rowWithDimensions).storedDimensions !== '') {
        return {
            resultDimensionsToSet: (await rowWithDimensions).storedDimensions,
            resultAttributesToSet: (await rowWithDimensions).storedAttributes,
            resultDataDetermined: true,
        };
    }
    return { resultDimensionsToSet: '', resultAttributesToSet: '', resultDataDetermined: false };
}

/**
 * Get site and supplier as partial nodes
 * @param stockPage
 * @param siteId _id of the site to read from DB
 * @returns Promise<{ site: ExtractEdgesPartial<Site> }>
 */
export function getSite(options: { stockPage: ui.Page<GraphApi>; siteId: string }): Promise<ExtractEdgesPartial<Site>> {
    return options.stockPage.$.graph
        .node('@sage/xtrem-system/Site')
        .read(
            {
                _id: true,
                id: true,
                legalCompany: {
                    _id: true,
                },
                storedAttributes: true,
                storedDimensions: true,
            },
            options.siteId,
        )
        .execute();
}

export function deleteDialogWithAcceptButtonText(
    page: ui.Page,
    title: string,
    message: string,
    acceptButtonText: string,
    cancelButtonText?: string,
) {
    const options = {
        acceptButton: { text: acceptButtonText, isDestructive: true },
        cancelButton: {
            text: cancelButtonText || ui.localize('@sage/xtrem-stock/pages-confirm-cancel', 'Cancel'),
        },
    };
    return page.$.dialog
        .confirmation('warn', title, message, options)
        .then(() => true)
        .catch(() => false);
}
