import * as MasterDataUtils from '@sage/xtrem-master-data/build/lib/client-functions/utils';
import type * as StockInterfaces from './interfaces';

export function getQueryParameters(panelArgs: string | number | boolean) {
    if (panelArgs && typeof panelArgs === 'string') {
        const queryParams =
            MasterDataUtils.tryParseJSON<StockInterfaces.CostRollUpSubAssemblyDialogParameters>(panelArgs);

        if (
            queryParams &&
            queryParams.resultLineId &&
            queryParams.resultLineItemName &&
            queryParams.currencySymbol &&
            queryParams.costScale
        ) {
            return queryParams;
        }
    }
    return null;
}
