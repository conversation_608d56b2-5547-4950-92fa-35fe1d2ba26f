import { confirmDialogWithAcceptButtonText } from '@sage/xtrem-master-data/lib/client-functions/page-functions';
import * as ui from '@sage/xtrem-ui';
import type { StockAdjustmentStatusParameter } from './interfaces/stock-adjustments';

export async function correctStockTransactionStatusInProgress(parameters: StockAdjustmentStatusParameter) {
    if (
        await confirmDialogWithAcceptButtonText(
            parameters.stockAdjustmentPage,
            ui.localize(
                '@sage/xtrem-stock/pages__stock_adjustment__resync_display_status_title',
                'Stock Transaction Status',
            ),
            ui.localize(
                '@sage/xtrem-stock/pages__stock_adjustment__resync_display_status_message',
                'You about to correct this stock adjustment stock status to Completed.',
            ),
            ui.localize('@sage/xtrem-stock/pages__stock_adjustment__resync_display_status_continue', 'Continue'),
        )
    ) {
        const statusRefreshed = await parameters.stockAdjustmentPage.$.graph
            .node('@sage/xtrem-stock/StockAdjustment')
            .mutations.resynchronizeStockTransactionStatus(true, { stockAdjustment: parameters.stockAdjustmentId })
            .execute();
        if (statusRefreshed) {
            await parameters.stockAdjustmentPage.$.router.refresh();
        }
    }
}
