import type { CalculationStatus, MrpSynchronizationStatus, SuggestionStatus } from '@sage/xtrem-mrp-data-api';
import type {
    StandardCostRollUpResultLineStatus,
    StandardCostRollUpStatus,
    StockCountLineStatus,
    StockCountStatus,
    StockValuationStatus,
} from '@sage/xtrem-stock-api';
import type { ColoredElement } from '@sage/xtrem-system-api';
import {
    colorfulPillPattern,
    colorfulPillPatternDefaulted,
} from '@sage/xtrem-system/build/lib/client-functions/color-pattern';

function calculationStatusColor(status: CalculationStatus, coloredElement: ColoredElement) {
    switch (status) {
        case 'inProgress':
            return colorfulPillPattern.filledInformation[coloredElement];
        case 'completed':
            return colorfulPillPattern.filledClosing[coloredElement];
        case 'error':
            return colorfulPillPattern.filledNegative[coloredElement];
        default:
            return colorfulPillPattern.default[coloredElement];
    }
}

function standardCostRollUpStatusColor(status: StandardCostRollUpStatus, coloredElement: ColoredElement) {
    switch (status) {
        case 'inProgress':
            return colorfulPillPattern.filledInformation[coloredElement];
        case 'completed':
            return colorfulPillPattern.filledPositive[coloredElement];
        case 'error':
            return colorfulPillPattern.filledNegative[coloredElement];
        default:
            return colorfulPillPattern.default[coloredElement];
    }
}

function standardCostRollUpResultLineStatusColor(
    status: StandardCostRollUpResultLineStatus,
    coloredElement: ColoredElement,
) {
    switch (status) {
        case 'inProgress':
            return colorfulPillPattern.filledInformation[coloredElement];
        case 'created':
            return colorfulPillPattern.filledPositive[coloredElement];
        case 'updated':
            return colorfulPillPattern.filledPositive[coloredElement];
        case 'error':
            return colorfulPillPattern.filledNegative[coloredElement];
        default:
            return colorfulPillPattern.default[coloredElement];
    }
}

function suggestionStatusColor(status: SuggestionStatus, coloredElement: ColoredElement) {
    switch (status) {
        case 'suggestion':
            return colorfulPillPattern.filledCaution[coloredElement];
        case 'ordered':
            return colorfulPillPattern.filledPositive[coloredElement];
        default:
            return colorfulPillPattern.default[coloredElement];
    }
}

function stockCountStatusColor(status: StockCountStatus, coloredElement: ColoredElement) {
    switch (status) {
        case 'toBeCounted':
            return colorfulPillPattern.filledNeutral[coloredElement];
        case 'countInProgress':
            return colorfulPillPattern.filledCaution[coloredElement];
        case 'counted':
            return colorfulPillPattern.filledPositive[coloredElement];
        case 'closed':
            return colorfulPillPattern.filledClosing[coloredElement];
        default:
            return colorfulPillPattern.default[coloredElement];
    }
}

function stockCountLineStatusColor(status: StockCountLineStatus, coloredElement: ColoredElement) {
    switch (status) {
        case 'toBeCounted':
            return colorfulPillPattern.filledNeutral[coloredElement];
        case 'countInProgress':
            return colorfulPillPattern.filledCaution[coloredElement];
        case 'counted':
            return colorfulPillPattern.filledPositive[coloredElement];
        case 'excluded':
            return colorfulPillPattern.filledPositive[coloredElement];
        default:
            return colorfulPillPattern.default[coloredElement];
    }
}

function synchronizationStatusColor(status: MrpSynchronizationStatus, coloredElement: ColoredElement) {
    switch (status) {
        case 'error':
            return colorfulPillPattern.filledNegative[coloredElement];
        case 'success':
            return colorfulPillPattern.filledPositive[coloredElement];
        case 'inProgress':
            return colorfulPillPattern.filledInformation[coloredElement];
        default:
            return colorfulPillPattern.default[coloredElement];
    }
}

function stockValuationStatusColor(status: StockValuationStatus, coloredElement: ColoredElement) {
    switch (status) {
        case 'completed':
            return colorfulPillPattern.filledPositive[coloredElement];
        case 'inProgress':
            return colorfulPillPattern.filledInformation[coloredElement];
        default:
            return colorfulPillPattern.default[coloredElement];
    }
}

export function getLabelColorByStatus(
    enumEntry: string,
    status?:
        | MrpSynchronizationStatus
        | CalculationStatus
        | SuggestionStatus
        | StockCountStatus
        | StandardCostRollUpStatus
        | StandardCostRollUpResultLineStatus
        | StockCountLineStatus
        | null,
): {
    backgroundColor: string;
    borderColor: string;
    textColor: string;
} {
    const getColor = (coloredElement: ColoredElement) => {
        switch (enumEntry) {
            case 'CalculationStatus':
                return calculationStatusColor(status as CalculationStatus, coloredElement);
            case 'SuggestionStatus':
                return suggestionStatusColor(status as SuggestionStatus, coloredElement);
            case 'StockCountStatus':
                return stockCountStatusColor(status as StockCountStatus, coloredElement);
            case 'StockCountLineStatus':
                return stockCountLineStatusColor(status as StockCountLineStatus, coloredElement);
            case 'SynchronizationStatus':
                return synchronizationStatusColor(status as MrpSynchronizationStatus, coloredElement);
            case 'StockValuationStatus':
                return stockValuationStatusColor(status as StockValuationStatus, coloredElement);
            case 'StandardCostRollUpStatus':
                return standardCostRollUpStatusColor(status as StandardCostRollUpStatus, coloredElement);
            case 'StandardCostRollUpResultLineStatus':
                return standardCostRollUpResultLineStatusColor(
                    status as StandardCostRollUpResultLineStatus,
                    coloredElement,
                );
            default:
                return colorfulPillPatternDefaulted(coloredElement);
        }
    };

    return {
        backgroundColor: getColor('backgroundColor'),
        borderColor: getColor('borderColor'),
        textColor: getColor('textColor'),
    };
}
