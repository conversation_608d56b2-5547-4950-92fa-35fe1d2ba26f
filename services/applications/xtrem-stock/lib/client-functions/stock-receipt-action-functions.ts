import { asyncArray } from '@sage/xtrem-async-helper';
import type { Dict } from '@sage/xtrem-client';
import { extractEdges } from '@sage/xtrem-client';
import type { StockReceiptDisplayStatus, StockReceipt as StockReceiptNode } from '@sage/xtrem-stock-api';
import type { GraphApi } from '@sage/xtrem-system-api';
import * as ui from '@sage/xtrem-ui';
import { getDimensionsForPurchaseLines, getValuesForSetDimensionsFromMainList } from './common';
import type {
    ActionParameters,
    SetDimensionActionParameter,
    StockReceiptPageParameter,
    StockReceiptStepSequenceStatus,
} from './interfaces/stock-receipt';

export async function getStockReceiptLines(parameters: StockReceiptPageParameter) {
    return extractEdges(
        await parameters.stockReceiptPage.$.graph
            .node('@sage/xtrem-stock/StockReceiptLine')
            .query(
                ui.queryUtils.edgesSelector(
                    {
                        _id: true,
                        storedDimensions: true,
                        storedAttributes: true,
                        stockDetailStatus: true,
                        document: {
                            number: true,
                        },
                        stockTransactionStatus: true,
                    },

                    {
                        filter: {
                            document: {
                                number: parameters.recordNumber,
                            },
                        },
                    },
                ),
            )
            .execute(),
    );
}

export async function setDimensions(parameters: SetDimensionActionParameter) {
    let dataDetermined = false;
    let dimensionsToSet = '';
    let attributesToSet = '';
    const purchaseReceiptLines = await getStockReceiptLines({
        stockReceiptPage: parameters.stockReceiptPage,
        recordNumber: parameters.recordNumber,
    });
    const linesToSetDimensions = purchaseReceiptLines.filter(
        line => !['inProgress', 'completed'].includes(line.stockTransactionStatus),
    );
    await asyncArray(linesToSetDimensions).some(async lineToSetDimensions => {
        const loopResponse = false;
        if (!dataDetermined) {
            const { defaultedFromItem, defaultDimensionsAttributes } = await getValuesForSetDimensionsFromMainList({
                stockPage: parameters.stockReceiptPage as ui.Page<GraphApi>,
                site: parameters.site || null,
                supplier: null,
            });
            const { resultDimensionsToSet, resultAttributesToSet, resultDataDetermined } =
                await getDimensionsForPurchaseLines({
                    line: lineToSetDimensions,
                    stockPage: parameters.stockReceiptPage as ui.Page<GraphApi>,
                    defaultDimensionsAttributes,
                    defaultedFromItem,
                });
            if (!resultDataDetermined) {
                return true;
            }
            dimensionsToSet = resultDimensionsToSet;
            attributesToSet = resultAttributesToSet;
            dataDetermined = resultDataDetermined;
        }
        if (dataDetermined) {
            await parameters.stockReceiptPage.$.graph
                .node('@sage/xtrem-stock/StockReceiptLine')
                .mutations.setDimension(true, {
                    stockReceiptLine: lineToSetDimensions._id,
                    storedDimensions: dimensionsToSet,
                    storedAttributes: attributesToSet,
                })
                .execute();
        }
        return loopResponse;
    });
    parameters.stockReceiptPage.$.showToast(
        ui.localize('@sage/xtrem-stock/pages__stock_receipt__apply_dimensions_success', 'Dimensions applied.'),
        { type: 'success' },
    );
}

async function getDisplayStatus(parameters: ActionParameters) {
    const stockReceipt = extractEdges(
        await parameters.stockReceiptPage.$.graph
            .node('@sage/xtrem-stock/StockReceipt')
            .query(
                ui.queryUtils.edgesSelector(
                    {
                        displayStatus: true,
                    },

                    {
                        filter: {
                            _id: parameters.recordId,
                        },
                    },
                ),
            )
            .execute(),
    ) as StockReceiptNode[];
    return stockReceipt[0].displayStatus;
}

export async function checkForUpdate(parameters: ActionParameters) {
    // TODO: This is a hack. It can be removed when XT-23948 is ready.
    let refreshCounter = 0;
    const checkForDisplayStatus = async () => {
        const displayStatus = await getDisplayStatus({
            stockReceiptPage: parameters.stockReceiptPage,
            recordId: parameters.recordId,
        });
        if (['received', 'stockPostingError'].includes(displayStatus)) {
            await parameters.stockReceiptPage.$.router.refresh(true);
            await parameters.stockReceiptPage.$.refreshNavigationPanel();
            parameters.stockReceiptPage.$.loader.isHidden = true;
            return;
        }
        refreshCounter += 1;
        if (refreshCounter <= 5) {
            await new Promise(resolve => {
                setTimeout(() => resolve(checkForDisplayStatus()), 1000);
            });
        }
    };
    await checkForDisplayStatus();
}

const receiptStepSequenceCreate = ui.localize(
    '@sage/xtrem-stock/pages__stock_receipt__receipt_step_sequence_creation',
    'Create',
);

const receiptStepSequenceDetailStock = ui.localize(
    '@sage/xtrem-stock/pages__stock_receipt__receipt_step_sequence_detail_stock',
    'Detail stock',
);

const receiptStepSequencePostStock = ui.localize(
    '@sage/xtrem-stock/pages__stock_receipt__receipt_step_sequence_post_stock',
    'Post stock',
);

export const receiptStepSequenceOptions = [
    receiptStepSequenceCreate,
    receiptStepSequenceDetailStock,
    receiptStepSequencePostStock,
];

function setStepSequenceStatusObject(stepSequenceValues: StockReceiptStepSequenceStatus): Dict<ui.StepSequenceStatus> {
    return {
        [receiptStepSequenceCreate]: stepSequenceValues.create,
        [receiptStepSequenceDetailStock]: stepSequenceValues.detailStock,
        [receiptStepSequencePostStock]: stepSequenceValues.postStock,
    };
}

export function getDisplayStatusStepSequence(
    recordId: string,
    displayStatus: StockReceiptDisplayStatus,
): Dict<ui.StepSequenceStatus> {
    if (!recordId) {
        return setStepSequenceStatusObject({
            create: 'current',
            detailStock: 'incomplete',
            postStock: 'incomplete',
        });
    }
    if (displayStatus === 'received') {
        return setStepSequenceStatusObject({
            create: 'complete',
            detailStock: 'complete',
            postStock: 'complete',
        });
    }
    if (displayStatus === 'stockPostingInProgress' || displayStatus === 'stockPostingError') {
        return setStepSequenceStatusObject({
            create: 'complete',
            detailStock: 'complete',
            postStock: 'current',
        });
    }
    if (displayStatus === 'detailsEntered') {
        return setStepSequenceStatusObject({
            create: 'complete',
            detailStock: 'complete',
            postStock: 'incomplete',
        });
    }
    if (displayStatus === 'detailsRequired') {
        return setStepSequenceStatusObject({
            create: 'complete',
            detailStock: 'current',
            postStock: 'incomplete',
        });
    }
    return setStepSequenceStatusObject({
        create: 'current',
        detailStock: 'incomplete',
        postStock: 'incomplete',
    });
}
