import { asyncArray } from '@sage/xtrem-async-helper';
import type { Dict } from '@sage/xtrem-client';
import { extractEdges } from '@sage/xtrem-client';
import type { StockIssueDisplayStatus } from '@sage/xtrem-stock-api';
import type { GraphApi } from '@sage/xtrem-system-api';
import * as ui from '@sage/xtrem-ui';
import { getDimensionsForPurchaseLines, getValuesForSetDimensionsFromMainList } from './common';
import type {
    IssueActionParameters,
    IssueSetDimensionActionParameter,
    StockIssuePageParameter,
    StockIssueStepSequenceStatus,
} from './interfaces/stock-issue';

export async function getStockIssueLines(parameters: StockIssuePageParameter) {
    return extractEdges(
        await parameters.stockIssuePage.$.graph
            .node('@sage/xtrem-stock/StockIssueLine')
            .query(
                ui.queryUtils.edgesSelector(
                    {
                        _id: true,
                        storedDimensions: true,
                        storedAttributes: true,
                        stockDetailStatus: true,
                        document: {
                            number: true,
                        },
                        stockTransactionStatus: true,
                    },

                    {
                        filter: {
                            document: {
                                number: parameters.recordNumber,
                            },
                        },
                    },
                ),
            )
            .execute(),
    );
}

async function getDisplayStatus(parameters: IssueActionParameters) {
    const stockIssue = extractEdges(
        await parameters.stockIssuePage.$.graph
            .node('@sage/xtrem-stock/StockIssue')
            .query(
                ui.queryUtils.edgesSelector(
                    {
                        displayStatus: true,
                    },

                    {
                        filter: {
                            _id: parameters.recordId,
                        },
                    },
                ),
            )
            .execute(),
    );
    return stockIssue[0].displayStatus;
}

export async function checkForUpdate(parameters: IssueActionParameters) {
    // TODO: This is a hack. It can be removed when XT-23948 is ready.
    let refreshCounter = 0;
    const checkForDisplayStatus = async () => {
        const displayStatus = await getDisplayStatus({
            stockIssuePage: parameters.stockIssuePage,
            recordId: parameters.recordId,
        });
        if (['issued', 'stockPostingError'].includes(displayStatus)) {
            await parameters.stockIssuePage.$.router.refresh(true);
            await parameters.stockIssuePage.$.refreshNavigationPanel();
            parameters.stockIssuePage.$.loader.isHidden = true;
            return;
        }
        refreshCounter += 1;
        if (refreshCounter <= 5) {
            await new Promise(resolve => {
                setTimeout(() => resolve(checkForDisplayStatus()), 1000);
            });
        }
    };
    await checkForDisplayStatus();
}

const issueStepSequenceCreate = ui.localize(
    '@sage/xtrem-stock/pages__stock_issue__issue_step_sequence_creation',
    'Create',
);

const issueStepSequenceDetailStock = ui.localize(
    '@sage/xtrem-stock/pages__stock_issue__issue_step_sequence_detail_stock',
    'Detail stock',
);

const issueStepSequencePostStock = ui.localize(
    '@sage/xtrem-stock/pages__stock_issue__issue_step_sequence_post_stock',
    'Post stock',
);

export const issueStepSequenceOptions = [
    issueStepSequenceCreate,
    issueStepSequenceDetailStock,
    issueStepSequencePostStock,
];

function setStepSequenceStatusObject(stepSequenceValues: StockIssueStepSequenceStatus): Dict<ui.StepSequenceStatus> {
    return {
        [issueStepSequenceCreate]: stepSequenceValues.create,
        [issueStepSequenceDetailStock]: stepSequenceValues.detailStock,
        [issueStepSequencePostStock]: stepSequenceValues.postStock,
    };
}

export function getDisplayStatusStepSequence(
    recordId: string,
    displayStatus: StockIssueDisplayStatus,
): Dict<ui.StepSequenceStatus> {
    if (!recordId) {
        return setStepSequenceStatusObject({
            create: 'current',
            detailStock: 'incomplete',
            postStock: 'incomplete',
        });
    }
    if (displayStatus === 'issued') {
        return setStepSequenceStatusObject({
            create: 'complete',
            detailStock: 'complete',
            postStock: 'complete',
        });
    }
    if (displayStatus === 'stockPostingInProgress' || displayStatus === 'stockPostingError') {
        return setStepSequenceStatusObject({
            create: 'complete',
            detailStock: 'complete',
            postStock: 'current',
        });
    }
    if (displayStatus === 'detailsEntered') {
        return setStepSequenceStatusObject({
            create: 'complete',
            detailStock: 'complete',
            postStock: 'incomplete',
        });
    }
    if (displayStatus === 'detailsRequired') {
        return setStepSequenceStatusObject({
            create: 'complete',
            detailStock: 'current',
            postStock: 'incomplete',
        });
    }
    return setStepSequenceStatusObject({
        create: 'current',
        detailStock: 'incomplete',
        postStock: 'incomplete',
    });
}

export async function setDimensions(parameters: IssueSetDimensionActionParameter) {
    let dataDetermined = false;
    let dimensionsToSet = '';
    let attributesToSet = '';
    const purchaseReceiptLines = await getStockIssueLines({
        stockIssuePage: parameters.stockIssuePage,
        recordNumber: parameters.recordNumber,
    });
    const linesToSetDimensions = purchaseReceiptLines.filter(
        line => !['inProgress', 'completed'].includes(line.stockTransactionStatus),
    );
    await asyncArray(linesToSetDimensions).some(async lineToSetDimensions => {
        const loopResponse = false;
        if (!dataDetermined) {
            const { defaultedFromItem, defaultDimensionsAttributes } = await getValuesForSetDimensionsFromMainList({
                stockPage: parameters.stockIssuePage as ui.Page<GraphApi>,
                site: parameters.site || null,
                supplier: null,
            });
            const { resultDimensionsToSet, resultAttributesToSet, resultDataDetermined } =
                await getDimensionsForPurchaseLines({
                    line: lineToSetDimensions,
                    stockPage: parameters.stockIssuePage as ui.Page<GraphApi>,
                    defaultDimensionsAttributes,
                    defaultedFromItem,
                });
            if (!resultDataDetermined) {
                return true;
            }
            dimensionsToSet = resultDimensionsToSet;
            attributesToSet = resultAttributesToSet;
            dataDetermined = resultDataDetermined;
        }
        if (dataDetermined) {
            await parameters.stockIssuePage.$.graph
                .node('@sage/xtrem-stock/StockIssueLine')
                .mutations.setDimension(true, {
                    stockIssueLine: lineToSetDimensions._id,
                    storedDimensions: dimensionsToSet,
                    storedAttributes: attributesToSet,
                })
                .execute();
        }
        return loopResponse;
    });
    parameters.stockIssuePage.$.showToast(
        ui.localize('@sage/xtrem-stock/pages__stock_issue__apply_dimensions_success', 'Dimensions applied.'),
        { type: 'success' },
    );
}
