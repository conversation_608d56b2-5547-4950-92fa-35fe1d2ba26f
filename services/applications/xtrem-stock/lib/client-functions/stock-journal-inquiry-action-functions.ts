import type { FinanceDocumentType, SourceDocumentType } from '@sage/xtrem-finance-data-api';

export function getFinanceDocumentTypeFromDocumentLineType(documentLineType: string): FinanceDocumentType {
    let documentType = documentLineType.split('Line')[0];
    documentType = documentType.charAt(0).toLowerCase() + documentType.slice(1);
    switch (documentType) {
        case 'stockReceipt':
            return 'miscellaneousStockReceipt';
        case 'stockIssue':
            return 'miscellaneousStockIssue';
        case 'productionTracking':
        case 'materialTracking':
        case 'operationTracking':
            return 'workInProgress';
        default:
            break;
    }
    return documentType as FinanceDocumentType;
}

export function getSourceDocumentTypeFromDocumentLineType(documentLineType: string): SourceDocumentType {
    if (documentLineType.startsWith('Stock')) {
        return 'materialTracking';
    }

    const documentType = documentLineType.split('Line')[0];
    return (documentType.charAt(0).toLowerCase() + documentType.slice(1)) as SourceDocumentType;
}
