import type { date, decimal, Reference } from '@sage/xtrem-core';
import { decorators, Node } from '@sage/xtrem-core';
import * as xtremFinanceData from '@sage/xtrem-finance-data';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremSystem from '@sage/xtrem-system';
import * as xtremStock from '..';

@decorators.node<StockValuationResultLine>({
    storage: 'sql',
    isPublished: true,
    canRead: true,
    canSearch: true,
    isVitalCollectionChild: true,
})
export class StockValuationResultLine extends Node {
    @decorators.referenceProperty<StockValuationResultLine, 'inputSet'>({
        isStored: true,
        isPublished: true,
        isVitalParent: true,
        node: () => xtremStock.nodes.StockValuationInputSet,
        lookupAccess: true,
    })
    readonly inputSet: Reference<xtremStock.nodes.StockValuationInputSet>;

    @decorators.referenceProperty<StockValuationResultLine, 'item'>({
        isStored: true,
        isPublished: true,
        isRequired: true,
        ignoreIsActive: true,
        node: () => xtremMasterData.nodes.Item,
        lookupAccess: true,
    })
    readonly item: Reference<xtremMasterData.nodes.Item>;

    @decorators.referenceProperty<StockValuationResultLine, 'site'>({
        isPublished: true,
        isStored: true,
        isRequired: true,
        node: () => xtremSystem.nodes.Site,
        ignoreIsActive: true,
        provides: ['site'],
        lookupAccess: true,
    })
    readonly site: Reference<xtremSystem.nodes.Site>;

    @decorators.referenceProperty<StockValuationResultLine, 'company'>({
        isPublished: true,
        node: () => xtremSystem.nodes.Company,
        ignoreIsActive: true,
        provides: ['company'],
        async getValue() {
            return (await this.site).legalCompany;
        },
        lookupAccess: true,
    })
    readonly company: Reference<xtremSystem.nodes.Company>;

    @decorators.referenceProperty<StockValuationResultLine, 'postingClass'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
        node: () => xtremFinanceData.nodes.PostingClass,
        lookupAccess: true,
    })
    readonly postingClass: Reference<xtremFinanceData.nodes.PostingClass | null>;

    @decorators.decimalProperty<StockValuationResultLine, 'quantity'>({
        isPublished: true,
        isStored: true,
        isRequired: true,
        dependsOn: ['stockUnit'],
        dataType: () => xtremMasterData.dataTypes.quantityInStockUnit,
        lookupAccess: true,
    })
    readonly quantity: Promise<decimal>;

    @decorators.decimalProperty<StockValuationResultLine, 'stockValue'>({
        isPublished: true,
        isStored: true,
        isRequired: true,
        dataType: () => xtremMasterData.dataTypes.amountInCompanyCurrency,
        lookupAccess: true,
    })
    readonly stockValue: Promise<decimal>;

    @decorators.decimalProperty<StockValuationResultLine, 'unitCost'>({
        isStored: true,
        isPublished: true,
        isRequired: true,
        dataType: () => xtremMasterData.dataTypes.costDataType,
        lookupAccess: true,
    })
    readonly unitCost: Promise<decimal>;

    @decorators.dateProperty<StockValuationResultLine, 'valuationDate'>({
        isPublished: true,
        isStored: true,
        isRequired: true,
        lookupAccess: true,
    })
    readonly valuationDate: Promise<date>;

    @decorators.enumProperty<StockValuationResultLine, 'costType'>({
        isStored: true,
        isPublished: true,
        isRequired: true,
        dataType: () => xtremMasterData.enums.costValuationMethodDataType,
        lookupAccess: true,
    })
    readonly costType: Promise<xtremMasterData.enums.CostValuationMethod>;

    @decorators.referenceProperty<StockValuationResultLine, 'stockUnit'>({
        isStored: true,
        isPublished: true,
        isRequired: true,
        node: () => xtremMasterData.nodes.UnitOfMeasure,
        lookupAccess: true,
    })
    readonly stockUnit: Reference<xtremMasterData.nodes.UnitOfMeasure>;

    @decorators.referenceProperty<StockValuationResultLine, 'currency'>({
        isStored: true,
        isPublished: true,
        isRequired: true,
        node: () => xtremMasterData.nodes.Currency,
        lookupAccess: true,
    })
    readonly currency: Reference<xtremMasterData.nodes.Currency>;

    @decorators.referenceProperty<StockValuationResultLine, 'itemCategory'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
        node: () => xtremMasterData.nodes.ItemCategory,
        lookupAccess: true,
    })
    readonly itemCategory: Reference<xtremMasterData.nodes.ItemCategory> | null;

    @decorators.stringProperty<StockValuationResultLine, 'commodityCode'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremSystem.dataTypes.code,
        lookupAccess: true,
    })
    readonly commodityCode: Promise<string>;
}
