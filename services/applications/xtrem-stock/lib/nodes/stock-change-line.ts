import type { Collection, decimal, integer, JsonType, NodeCreateData, Reference } from '@sage/xtrem-core';
import { decorators } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremStockData from '@sage/xtrem-stock-data';
import * as xtremStock from '..';

@decorators.subNode<StockChangeLine>({
    isPublished: true,
    canRead: true,
    canSearch: true,
    canCreate: true,
    canUpdate: false,
    isVitalCollectionChild: true,
    extends: () => xtremMasterData.nodes.BaseDocumentLine,
    async controlEnd(cx) {
        const document = await this.document;
        if (
            (await this.location) === (await document.location) &&
            (await this.stockStatus) === (await document.stockStatus)
        ) {
            cx.error.addLocalized(
                '@sage/xtrem-stock/nodes__stock_change_line__no_change',
                'No change to process on one line. Use a different stock status or location for this line.',
            );
        }
    },
})
@xtremStockData.functions.staticImplements<xtremStockData.interfaces.StaticDocumentLineWithStockPosting>()
export class StockChangeLine extends xtremMasterData.nodes.BaseDocumentLine {
    getOrderCost(): Promise<decimal> {
        return this.orderCost;
    }

    getValuedCost(): Promise<decimal> {
        return this.valuedCost;
    }

    // eslint-disable-next-line class-methods-use-this
    getValuationParameters(): xtremStockData.interfaces.ValuationParameter {
        return { valuationType: 'change' };
    }

    getItem(): Promise<xtremMasterData.nodes.Item> {
        return this.item;
    }

    @decorators.referenceProperty<StockChangeLine, 'document'>({
        isStored: true,
        isPublished: true,
        node: () => xtremStock.nodes.StockChange,
        isVitalParent: true,
        lookupAccess: true,
    })
    override readonly document: Reference<xtremStock.nodes.StockChange>;

    @decorators.stringPropertyOverride<StockChangeLine, 'documentNumber'>({
        async getValue() {
            return (await this.document).number;
        },
    })
    override readonly documentNumber: Promise<string>;

    @decorators.integerPropertyOverride<StockChangeLine, 'documentId'>({
        async getValue() {
            return (await this.document)._id;
        },
    })
    override readonly documentId: Promise<integer>;

    @decorators.enumProperty<StockChangeLine, 'stockTransactionStatus'>({
        isStored: true,
        isPublished: true,
        defaultValue() {
            return 'draft';
        },
        dataType: () => xtremStockData.enums.StockDocumentTransactionStatusDataType,
        lookupAccess: true,
    })
    readonly stockTransactionStatus: Promise<xtremStockData.enums.StockDocumentTransactionStatus>;

    @decorators.referenceProperty<StockChangeLine, 'item'>({
        isPublished: false,
        dependsOn: [{ document: ['item'] }],
        node: () => xtremMasterData.nodes.Item,
        ignoreIsActive: true,
        async getValue() {
            return (await this.document).item;
        },
    })
    readonly item: Reference<xtremMasterData.nodes.Item>;

    @decorators.decimalProperty<StockChangeLine, 'quantityInStockUnit'>({
        isStored: true,
        isPublished: true,
        isNotZero: true,
        dataType: () => xtremMasterData.dataTypes.quantityInStockUnit,
        async control(cx, val) {
            await cx.error.if(val).is.negative();
        },
        lookupAccess: true,
    })
    readonly quantityInStockUnit: Promise<decimal>;

    @decorators.decimalProperty<StockChangeLine, 'orderCost'>({
        isStored: true,
        isPublished: true,
        dependsOn: [{ document: ['itemSite'] }],
        dataType: () => xtremMasterData.dataTypes.costDataType,
        async defaultValue() {
            return (await (await (await this.document)?.itemSite)?.currentCost) ?? 0;
        },
        lookupAccess: true,
    })
    readonly orderCost: Promise<decimal>;

    @decorators.decimalProperty<StockChangeLine, 'valuedCost'>({
        isStored: true,
        isPublished: true,
        dependsOn: [{ document: ['itemSite'] }],
        dataType: () => xtremMasterData.dataTypes.costDataType,
        async defaultValue() {
            return (await (await (await this.document)?.itemSite)?.currentCost) ?? 0;
        },
        lookupAccess: true,
    })
    readonly valuedCost: Promise<decimal>;

    @decorators.referenceProperty<StockChangeLine, 'location'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
        node: () => xtremMasterData.nodes.Location,
        dependsOn: [{ document: ['stockSite'] }],
        // TODO: to replace by a filter{control} when is XT-12798 implemented
        async control(cx) {
            if (await this.location) {
                if ((await (await this.location)?.site) !== (await (await this.document).stockSite)) {
                    cx.error.addLocalized(
                        '@sage/xtrem-stock/nodes__stock_change_line__location_site_must_match',
                        'The location site and the stock site must be the same.',
                    );
                }
            }
        },
        lookupAccess: true,
    })
    readonly location: Reference<xtremMasterData.nodes.Location | null>;

    @decorators.referenceProperty<StockChangeLine, 'stockStatus'>({
        isStored: true,
        isPublished: true,
        node: () => xtremStockData.nodes.StockStatus,
        lookupAccess: true,
    })
    readonly stockStatus: Reference<xtremStockData.nodes.StockStatus>;

    @decorators.collectionProperty<StockChangeLine, 'stockTransactions'>({
        isPublished: true,
        reverseReference: 'documentLine',
        isVital: true,
        node: () => xtremStockData.nodes.StockTransaction,
        lookupAccess: true,
    })
    readonly stockTransactions: Collection<xtremStockData.nodes.StockTransaction>;

    @decorators.collectionProperty<StockChangeLine, 'stockDetails'>({
        isPublished: true,
        isVital: true,
        reverseReference: 'documentLine',
        node: () => xtremStockData.nodes.StockChangeDetail,
        lookupAccess: true,
    })
    readonly stockDetails: Collection<xtremStockData.nodes.StockChangeDetail>;

    @decorators.jsonProperty<StockChangeLine, 'jsonStockDetails'>({
        isPublished: true,
        dependsOn: ['stockDetails'],
        async setValue(details) {
            if (details && details.length > 0) {
                await this.$.set({
                    stockDetails: xtremStockData.functions.stockDetailLib.parseDetails(details) as Array<
                        NodeCreateData<xtremStockData.nodes.StockChangeDetail>
                    >,
                });
            }
        },
        async computeValue() {
            if ((await this.stockDetails.length) > 0) {
                return this.stockDetails.map(detail =>
                    xtremStockData.functions.stockDetailLib.filterStockDetailProperties(
                        detail,
                        xtremStockData.nodes.StockChangeDetail,
                        {
                            onlyIds: true,
                        },
                    ),
                );
            }
            // forcing typing to accept an empty object
            return [] as any;
        },
        lookupAccess: true,
    })
    readonly jsonStockDetails: Promise<Array<JsonType<NodeCreateData<xtremStockData.nodes.StockChangeDetail>>>>;
}
