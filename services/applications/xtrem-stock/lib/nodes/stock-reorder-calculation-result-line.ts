import type { date, decimal, Reference } from '@sage/xtrem-core';
import { decorators, Node } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremSystem from '@sage/xtrem-system';
import * as xtremStock from '..';

@decorators.node<StockReorderCalculationResultLine>({
    storage: 'sql',
    isPublished: true,
    canRead: true,
    canSearch: true,
    isVitalCollectionChild: true,
})
export class StockReorderCalculationResultLine extends Node {
    @decorators.referenceProperty<StockReorderCalculationResultLine, 'inputSet'>({
        isStored: true,
        isPublished: true,
        isVitalParent: true,
        node: () => xtremStock.nodes.StockReorderCalculationInputSet,
        lookupAccess: true,
    })
    readonly inputSet: Reference<xtremStock.nodes.StockReorderCalculationInputSet>;

    @decorators.referenceProperty<StockReorderCalculationResultLine, 'itemSite'>({
        isStored: true,
        isPublished: true,
        isRequired: true,
        node: () => xtremMasterData.nodes.ItemSite,
        lookupAccess: true,
    })
    readonly itemSite: Reference<xtremMasterData.nodes.ItemSite>;

    @decorators.referenceProperty<StockReorderCalculationResultLine, 'company'>({
        isPublished: true,
        node: () => xtremSystem.nodes.Company,
        provides: ['company'],
        async getValue() {
            return (await (await this.itemSite).site).legalCompany;
        },
        lookupAccess: true,
    })
    readonly company: Reference<xtremSystem.nodes.Company>;

    @decorators.referenceProperty<StockReorderCalculationResultLine, 'supplier'>({
        isStored: true,
        isPublished: true,
        node: () => xtremMasterData.nodes.Supplier,
        lookupAccess: true,
        isNullable: true,
    })
    readonly supplier: Reference<xtremMasterData.nodes.Supplier | null>;

    @decorators.decimalProperty<StockReorderCalculationResultLine, 'quantity'>({
        isPublished: true,
        isStored: true,
        isRequired: true,
        dependsOn: ['stockUnit'],
        dataType: () => xtremMasterData.dataTypes.quantityInStockUnit,
        lookupAccess: true,
    })
    readonly quantity: Promise<decimal>;

    @decorators.dateProperty<StockReorderCalculationResultLine, 'orderDate'>({
        isStored: true,
        isPublished: true,
        isRequired: true,
    })
    readonly orderDate: Promise<date>;

    @decorators.enumProperty<StockReorderCalculationResultLine, 'reorderType'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
        dataType: () => xtremMasterData.enums.preferredProcessDataType,
    })
    readonly reorderType: Promise<xtremMasterData.enums.PreferredProcess | null>;

    @decorators.referenceProperty<StockReorderCalculationResultLine, 'stockUnit'>({
        isStored: true,
        isPublished: true,
        isRequired: true,
        node: () => xtremMasterData.nodes.UnitOfMeasure,
        lookupAccess: true,
    })
    readonly stockUnit: Reference<xtremMasterData.nodes.UnitOfMeasure>;

    @decorators.referenceProperty<StockReorderCalculationResultLine, 'purchaseUnit'>({
        isStored: true,
        isPublished: true,
        node: () => xtremMasterData.nodes.UnitOfMeasure,
        lookupAccess: true,
        isNullable: true,
    })
    readonly purchaseUnit: Reference<xtremMasterData.nodes.UnitOfMeasure | null>;

    @decorators.referenceProperty<StockReorderCalculationResultLine, 'currency'>({
        isStored: true,
        isPublished: true,
        isRequired: true,
        node: () => xtremMasterData.nodes.Currency,
        lookupAccess: true,
    })
    readonly currency: Reference<xtremMasterData.nodes.Currency>;
}
