import type { NotificationEnvelope } from '@sage/xtrem-communication';
import type { Collection, Context, Reference, integer } from '@sage/xtrem-core';
import { BusinessRuleError, Node, NodeStatus, date, decorators, useDefaultValue } from '@sage/xtrem-core';
import * as xtremFinanceData from '@sage/xtrem-finance-data';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremStockData from '@sage/xtrem-stock-data';
import * as xtremSystem from '@sage/xtrem-system';
import * as xtremStock from '..';
import { stockAdjustmentLib } from '../functions';

@decorators.node<StockAdjustment>({
    isClearedByReset: true,
    package: 'xtrem-stock',
    storage: 'sql',
    isPublished: true,
    canRead: true,
    canSearch: true,
    canCreate: true,
    canUpdate: true,
    canDelete: true,
    isCustomizable: true,
    hasAttachments: true,
    indexes: [{ orderBy: { number: +1 }, isUnique: true }],
    notifies: ['created', 'updated', 'deleted'],
    async saveBegin() {
        await xtremMasterData.functions.controlDocumentNumber(this);
    },
    async saveEnd() {
        // When the stock transaction status changes to completed because lines have been deleted, we need to send the finance notification
        if (
            this.$.status === NodeStatus.modified &&
            (await (await this.$.old).stockTransactionStatus) !== 'completed' &&
            (await this.stockTransactionStatus) === 'completed' &&
            (await (await this.$.old).lines.length) > (await this.lines.length)
        ) {
            await StockAdjustment.onceStockCompleted(this.$.context, this);
        }
    },
})
@xtremStockData.functions.staticImplements<xtremStockData.interfaces.StaticDocumentHeaderWithStockPosting>()
export class StockAdjustment extends Node implements xtremFinanceData.interfaces.FinanceOriginDocumentHeader {
    getEffectiveDate(): Promise<date> {
        return this.effectiveDate;
    }

    getStockSite(): Promise<xtremSystem.nodes.Site> {
        return this.stockSite;
    }

    @decorators.stringProperty<StockAdjustment, 'number'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.documentNumber,
        defaultValue() {
            return xtremMasterData.functions.getDocumentNumberPlaceholder();
        },
        deferredDefaultValue() {
            return xtremMasterData.functions.allocateDocumentNumber(this);
        },
        provides: ['sequenceNumber'],
    })
    readonly number: Promise<string>;

    @decorators.dateProperty<StockAdjustment, 'effectiveDate'>({
        isStored: true,
        isPublished: true,
        defaultValue() {
            return date.today();
        },
        provides: ['documentDate'],
    })
    readonly effectiveDate: Promise<date>;

    @decorators.referenceProperty<StockAdjustment, 'stockSite'>({
        isStored: true,
        isPublished: true,
        node: () => xtremSystem.nodes.Site,
        async control(cx, val) {
            await xtremSystem.events.control.isActive(
                this.$.status,
                cx,
                this.$.status === NodeStatus.modified ? (await this.$.old)._id : null,
                val,
            );
        },
        provides: ['site'],
    })
    readonly stockSite: Reference<xtremSystem.nodes.Site>;

    @decorators.referenceProperty<StockAdjustment, 'reasonCode'>({
        isStored: true,
        isPublished: true,
        node: () => xtremMasterData.nodes.ReasonCode,
        filters: {
            control: { _or: [{ quantityIncreaseAdjustment: true }, { quantityDecreaseAdjustment: true }] },
        },
    })
    readonly reasonCode: Reference<xtremMasterData.nodes.ReasonCode>;

    @decorators.collectionProperty<StockAdjustment, 'lines'>({
        isPublished: true,
        isVital: true,
        isRequired: true,
        node: () => xtremStock.nodes.StockAdjustmentLine,
        reverseReference: 'document',
    })
    readonly lines: Collection<xtremStock.nodes.StockAdjustmentLine>;

    @decorators.booleanProperty<StockAdjustment, 'isSetDimensionsMainListHidden'>({
        isPublished: true,
        async getValue() {
            const displayStatus = await this.displayStatus;
            return displayStatus === 'adjusted' || displayStatus === 'stockPostingInProgress';
        },
    })
    readonly isSetDimensionsMainListHidden: Promise<boolean>;

    @decorators.collectionProperty<StockAdjustment, 'postingDetails'>({
        isPublished: true,
        join: {
            documentSysId() {
                return this._id;
            },
            documentType() {
                return 'stockAdjustment';
            },
        },
        node: () => xtremFinanceData.nodes.FinanceTransaction,
        lookupAccess: true,
    })
    readonly postingDetails: Collection<xtremFinanceData.nodes.FinanceTransaction>;

    readonly linesWithStockTransactionStatusCollectionName = 'lines';

    @decorators.stringProperty<StockAdjustment, 'description'>({
        isStored: true,
        isPublished: true,
        dependsOn: ['reasonCode'],
        dataType: () => xtremSystem.dataTypes.description,
        async defaultValue() {
            return (await this.reasonCode).name;
        },
    })
    readonly description: Promise<string>;

    @decorators.enumProperty<StockAdjustment, 'financeIntegrationStatus'>({
        isPublished: true,
        dataType: () => xtremFinanceData.enums.financeIntegrationStatusDataType,
        async computeValue() {
            return xtremFinanceData.functions.getDocumentIntegrationStatus(
                this.$.context,
                'stockAdjustment',
                await this.number,
            );
        },
    })
    readonly financeIntegrationStatus: Promise<xtremFinanceData.enums.FinanceIntegrationStatus>;

    @decorators.booleanProperty<StockAdjustment, 'forceUpdateForStock'>({
        defaultValue: false,
    })
    readonly forceUpdateForStock: Promise<boolean>;

    @decorators.enumProperty<StockAdjustment, 'status'>({
        isPublished: true,
        isStoredOutput: true,
        dataType: () => xtremStockData.enums.StockDocumentStatusDataType,
        dependsOn: ['stockTransactionStatus', { lines: ['stockTransactionStatus'] }],
        async defaultValue() {
            return xtremStockData.functions.stockDocumentLib.getStockDocumentStatus(await this.stockTransactionStatus);
        },
        updatedValue: useDefaultValue,
        // it is really updated by stockIssueLine.notificationListener
        lookupAccess: true,
        async isFrozen() {
            return (await (await this.$.old).status) === 'closed';
        },
    })
    readonly status: Promise<xtremStockData.enums.StockDocumentStatus>;

    @decorators.enumProperty<StockAdjustment, 'stockTransactionStatus'>({
        isPublished: true,
        dependsOn: [{ lines: ['stockTransactionStatus'] }],
        dataType: () => xtremStockData.enums.StockDocumentTransactionStatusDataType,
        computeValue() {
            return xtremStockData.functions.stockTransactionLib.computeHeaderStatus(this);
        },
        lookupAccess: true,
    })
    readonly stockTransactionStatus: Promise<xtremStockData.enums.StockDocumentTransactionStatus>;

    @decorators.enumProperty<StockAdjustment, 'displayStatus'>({
        isStoredOutput: true,
        isPublished: true,
        lookupAccess: true,
        dataType: () => xtremStock.enums.StockAdjustmentDisplayStatusDataType,
        dependsOn: [{ lines: ['stockTransactionStatus', 'stockDetailStatus'] }, 'status', 'stockTransactionStatus'],
        defaultValue() {
            return xtremStock.functions.stockAdjustmentLib.computeHeaderDisplayStatus(this);
        },
        updatedValue: useDefaultValue,
    })
    readonly displayStatus: Promise<xtremStock.enums.StockAdjustmentDisplayStatus>;

    // property needed for the accounting interface
    @decorators.referenceProperty<StockAdjustment, 'financialSite'>({
        isPublished: true,
        node: () => xtremSystem.nodes.Site,
        dependsOn: ['stockSite'],
        async getValue() {
            return xtremFinanceData.functions.getFinancialSite(await this.stockSite);
        },
        lookupAccess: true,
    })
    readonly financialSite: Reference<xtremSystem.nodes.Site>;

    // property needed for the accounting interface
    @decorators.referenceProperty<StockAdjustment, 'transactionCurrency'>({
        isPublished: true,
        node: () => xtremMasterData.nodes.Currency,
        dependsOn: ['financialSite'],
        async getValue() {
            return (await (await this.financialSite).legalCompany).currency;
        },
        lookupAccess: true,
    })
    readonly transactionCurrency: Reference<xtremMasterData.nodes.Currency>;

    // property needed for the accounting interface
    @decorators.dateProperty<StockAdjustment, 'documentDate'>({
        isPublished: true,
        dependsOn: ['effectiveDate'],
        getValue() {
            return this.effectiveDate;
        },
        lookupAccess: true,
    })
    readonly documentDate: Promise<date>;

    @decorators.booleanProperty<StockAdjustment, 'forceUpdateForFinance'>({
        defaultValue: false,
    })
    readonly forceUpdateForFinance: Promise<boolean>;

    @decorators.mutation<typeof StockAdjustment, 'postToStock'>({
        isPublished: true,
        parameters: [
            {
                name: 'documentIds',
                type: 'array',
                item: {
                    type: 'integer',
                },
            },
        ],
        return: { type: 'string' },
    })
    static async postToStock(context: Context, documentIds: integer[]): Promise<string> {
        await stockAdjustmentLib.validatePost(context, documentIds);
        return xtremStockData.functions.notificationLib.stockAdjustmentRequestNotification(context, {
            documentClass: StockAdjustment,
            documentIds,
        });
    }

    // Repost creates an update notification to the acc engine ir order to update attributes and dimenions.
    // In fact, the existing finance document headers are preserved while the lines are all rebuilt (old ones deleted and new ones calculated)
    @decorators.mutation<typeof StockAdjustment, 'repost'>({
        isPublished: true,
        parameters: [
            {
                name: 'stockAdjustment',
                type: 'reference',
                isMandatory: true,
                isWritable: true,
                node: () => StockAdjustment,
            },
            {
                name: 'documentLines',
                type: 'array',
                item: {
                    type: 'object',
                    properties: {
                        baseDocumentLineSysId: 'integer',
                        storedAttributes: 'json',
                        storedDimensions: 'json',
                    },
                },
            },
        ],
        return: { type: 'object', properties: { wasSuccessful: 'boolean', message: 'string' } },
    })
    static async repost(
        context: Context,
        stockAdjustment: StockAdjustment,
        documentLines: xtremFinanceData.interfaces.FinanceIntegrationDocumentLineUpdate[],
    ): Promise<xtremFinanceData.interfaces.MutationResult> {
        if (!xtremFinanceData.functions.canRepost(await stockAdjustment.financeIntegrationStatus)) {
            throw new BusinessRuleError(
                context.localize(
                    '@sage/xtrem-stock/nodes__stock-adjustment__cant_repost_stock_adjustment_when_status_is_not_failed',
                    "You can only repost a stock adjustment if the status is 'Failed' or 'Not recorded'.",
                ),
            );
        }

        const updateLines = documentLines.map(documentLine => {
            return {
                _id: documentLine.baseDocumentLineSysId,
                storedAttributes: documentLine.storedAttributes,
                storedDimensions: documentLine.storedDimensions,
            };
        });
        await stockAdjustment.$.set({ forceUpdateForFinance: true, lines: updateLines });
        await stockAdjustment.$.save();

        // send notification in order to update a staging table entry for the accounting engine
        if (await (await (await stockAdjustment.stockSite).legalCompany).doStockPosting) {
            await xtremFinanceData.functions.financeDocumentUpdateNotification(context, {
                document: stockAdjustment,
                lines: await stockAdjustment.lines.toArray(),
                documentType: 'stockAdjustment',
                replyTopic: 'StockAdjustmentUpdate/accountingInterface',
                doNotPostOnUpdate: false,
            });
            return {
                wasSuccessful: true,
                message: context.localize(
                    '@sage/xtrem-stock/nodes__stock-adjustment__document_was_posted',
                    'The stock adjustment was posted.',
                ),
            };
        }
        return {
            wasSuccessful: true,
            message: context.localize(
                '@sage/xtrem-stock/nodes__stock-adjustment__company_has_no_stock_posting',
                'No stock has been posted to this company.',
            ),
        };
    }

    @decorators.mutation<typeof StockAdjustment, 'resynchronizeStockTransactionStatus'>({
        isPublished: true,
        parameters: [
            {
                name: 'stockAdjustment',
                type: 'reference',
                isMandatory: true,
                isWritable: true,
                node: () => StockAdjustment,
            },
        ],
        return: 'boolean',
    })
    static async resynchronizeStockTransactionStatus(
        _context: Context,
        stockAdjustment: StockAdjustment,
    ): Promise<boolean> {
        if ((await stockAdjustment.stockTransactionStatus) === 'inProgress') {
            await stockAdjustmentLib.updateStockTransactionStatus(stockAdjustment);
            return true;
        }
        return false;
    }

    @decorators.notificationListener<typeof StockAdjustment>({
        startsReadOnly: true,
        topic: 'StockAdjustment/stock/adjustment/reply',
        onError(
            context: Context,
            envelope: NotificationEnvelope<
                xtremStockData.interfaces.StockReplyPayload<xtremStockData.enums.StockMovementTypeEnum.adjustment>
            >,
            error: Error,
        ) {
            return xtremStockData.functions.notificationLib.errorHandler(context, envelope, error, StockAdjustment);
        },
    })
    static async onStockReply(
        readOnlyContext: Context,
        payload: xtremStockData.interfaces.StockReplyPayload<xtremStockData.enums.StockMovementTypeEnum.adjustment>,
    ): Promise<void> {
        const adjustmentUpdateResult = await readOnlyContext.runInWritableContext(
            async writableContext =>
                (
                    await xtremStockData.functions.notificationLib.reactToStockMovementReply(
                        writableContext,
                        payload,
                        StockAdjustment,
                    )
                ).adjustment,
        );
        if (!adjustmentUpdateResult) return;

        if (!adjustmentUpdateResult.transactionHadAnError) {
            await adjustmentUpdateResult.documents.forEach(async document => {
                if (document.isStockDocumentCompleted) {
                    await readOnlyContext.runInWritableContext(async writableContext => {
                        const stockAdjustment = await writableContext.read(
                            StockAdjustment,
                            { _id: document.id },
                            { forUpdate: true },
                        );
                        await StockAdjustment.onceStockCompleted(writableContext, stockAdjustment);
                    });
                }
            });
        }
    }

    static async onceStockCompleted(writableContext: Context, stockAdjustment: StockAdjustment): Promise<void> {
        // Send notification in order to create staging table entries for the accounting engine
        if (await (await (await stockAdjustment.stockSite).legalCompany).doStockPosting) {
            await xtremFinanceData.functions.stockAdjustmentNotification(
                writableContext,
                stockAdjustment as xtremFinanceData.interfaces.FinanceOriginDocument,
                (await stockAdjustment.lines.toArray()) as xtremFinanceData.interfaces.FinanceOriginDocumentLine[],
            );
        }
    }

    @decorators.notificationListener<typeof StockAdjustment>({
        topic: 'StockAdjustment/accountingInterface',
    })
    static async onFinanceIntegrationReply(
        context: Context,
        payload: xtremFinanceData.interfaces.FinanceTransactionData,
    ): Promise<void> {
        await xtremFinanceData.functions.reactToFinanceIntegrationReply(context, payload);
    }

    @decorators.mutation<typeof StockAdjustment, 'resendNotificationForFinance'>({
        isPublished: true,
        parameters: [
            {
                name: 'stockAdjustment',
                type: 'reference',
                isMandatory: true,
                isWritable: true,
                node: () => StockAdjustment,
            },
        ],
        return: {
            type: 'boolean',
        },
    })
    static async resendNotificationForFinance(context: Context, stockAdjustment: StockAdjustment): Promise<boolean> {
        if (
            await xtremFinanceData.functions.AccountingEngineCommon.validateResendNotificationForFinance(context, {
                documentNumber: await stockAdjustment.number,
                documentType: 'stockAdjustment',
            })
        ) {
            // send notification in order to create a staging table entry for the accounting engine
            if (await (await (await stockAdjustment.stockSite).legalCompany).doStockPosting) {
                await xtremFinanceData.functions.stockAdjustmentNotification(
                    context,
                    stockAdjustment as xtremFinanceData.interfaces.FinanceOriginDocument,
                    (await stockAdjustment.lines.toArray()) as xtremFinanceData.interfaces.FinanceOriginDocumentLine[],
                );
            }
        }

        return true;
    }

    @decorators.notificationListener<typeof StockAdjustment>({
        topic: 'StockAdjustment/resendNotificationForFinance',
    })
    static async onResendNotificationForFinance(context: Context, document: { number: number }) {
        const stockAdjustment = await context.read(StockAdjustment, { number: document.number });

        await StockAdjustment.resendNotificationForFinance(context, stockAdjustment);
    }
}
