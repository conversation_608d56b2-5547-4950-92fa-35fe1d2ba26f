import * as xtremCommunication from '@sage/xtrem-communication';
import type { Context } from '@sage/xtrem-core';
import { decorators, Node } from '@sage/xtrem-core';
import * as xtremStockData from '@sage/xtrem-stock-data';
import * as xtremStock from '..';

@decorators.node({
    isPublished: true,
})
export class AllocationListener extends Node {
    private static readonly allocationEngine = xtremStock.classes.AllocationEngine.getInstance();

    @decorators.asyncMutation<typeof AllocationListener, 'allocate'>({
        startsReadOnly: true,
        parameters: [
            {
                isMandatory: true,
                name: 'payload',
                type: 'object',
                properties: {
                    processId: { isMandatory: true, type: 'string' },
                    processType: {
                        isMandatory: true,
                        type: 'enum',
                        dataType: () => xtremStockData.enums.allocationProcessTypeDataType,
                    },
                    requestType: {
                        isMandatory: true,
                        type: 'enum',
                        dataType: () => xtremStockData.enums.allocationRequestTypeDataType,
                    },
                    documentType: {
                        isMandatory: true,
                        type: 'enum',
                        dataType: () => xtremStockData.enums.allocationDocumentTypeDataType,
                    },
                    originRequest: { type: 'string' },
                    replyTopic: { isMandatory: true, type: 'string' },
                    batchTrackingId: { isMandatory: false, type: 'string' },
                },
            },
            // make sure that the stockAllocationParameters is matched with the output of getStockAllocationParameterGraphQLDescriptor function
            {
                name: 'stockAllocationParameters',
                type: 'object',
                properties: { cannotOverAllocate: 'boolean', shouldControlAllocationInProgress: 'boolean' },
            },
        ],
        return: {
            type: 'enum',
            dataType: () => xtremStockData.enums.allocationRequestStatusDataType,
        },
    })
    static async allocate(
        context: Context,
        payload: Omit<
            xtremStockData.interfaces.AutomaticAllocationPayload<xtremStockData.enums.AllocationProcessTypeEnum>,
            'stockAllocationParameters'
        >,
        stockAllocationParameters?: xtremStockData.interfaces.StockAllocationParameters,
    ): Promise<xtremStockData.enums.AllocationRequestStatus> {
        await context.runInWritableContext(async childContext => {
            const notificationState = await childContext
                .query(xtremCommunication.nodes.SysNotificationState, {
                    filter: { notificationId: context.batch.trackingId },
                    first: 1,
                    forUpdate: true,
                })
                .elementAt(0);

            await notificationState.$.set({
                operationName: 'AllocationListener.allocate',

                parameterValues: {
                    processType: payload.processType,
                    requestType: payload.requestType,
                    documentType: payload.documentType,
                    originRequest: payload.originRequest,
                    processId: payload.processId,
                    replyTopic: payload.replyTopic,
                    batchTrackingId: payload.batchTrackingId,
                    ...(xtremStockData.functions.typingLib.isThisPayloadOfMassProcessAutoAllocation(payload)
                        ? {
                              userEntries: payload.userEntries,
                              filter: payload.filter,
                          }
                        : {}),

                    stockAllocationParameters,
                },
            });

            await notificationState.$.save();
        });

        return AllocationListener.allocationEngine.allocate(context, { ...payload, stockAllocationParameters });
    }
}
