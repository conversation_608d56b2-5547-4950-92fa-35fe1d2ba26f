import type { Collection, Context, decimal, integer, JsonType, NodeCreateData, Reference } from '@sage/xtrem-core';
import { decorators, useDefaultValue } from '@sage/xtrem-core';
import * as xtremFinanceData from '@sage/xtrem-finance-data';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremStockData from '@sage/xtrem-stock-data';
import * as xtremSystem from '@sage/xtrem-system';
import * as xtremStock from '..';

@decorators.subNode<StockReceiptLine>({
    isPublished: true,
    canRead: true,
    canSearch: true,
    canCreate: true,
    canUpdate: true,
    isVitalCollectionChild: true,
    extends: () => xtremMasterData.nodes.BaseDocumentLine,
    async saveBegin() {
        const site = await (await this.document).stockSite;
        const companyId = (await site.legalCompany)._id;
        const item = await this.item;

        const storedAttributes = await this.storedAttributes;
        const stockDetailStatus = await xtremStockData.functions.stockDetailLib.getStockDetailStatus({
            line: this,
            quantityExpected: await this.quantityInStockUnit,
            checkSerialNumbers: false,
        });
        const stockTransactionStatus = await this.stockTransactionStatus;

        if (!storedAttributes) {
            const defaultAttributes = await xtremFinanceData.functions.getDefaultAttributes(this.$.context, {
                dimensionDefinitionLevel: 'stockDirect',
                companyId,
                site,
                item,
            });
            await this.$.set({ storedAttributes: defaultAttributes });
        }

        await this.$.set({
            stockDetailStatus,
            displayStatus: xtremStock.functions.stockReceiptLib.calculateStockReceiptLineDisplayStatus(
                stockDetailStatus,
                stockTransactionStatus,
            ),
        });
    },
    async controlEnd(cx) {
        await xtremFinanceData.functions.attributTypeRestrictedToCheck(cx, this.$.context, await this.storedAttributes);
    },
})
@xtremStockData.functions.staticImplements<xtremStockData.interfaces.StaticDocumentLineWithStockPosting>()
export class StockReceiptLine
    extends xtremMasterData.nodes.BaseDocumentLine
    implements xtremFinanceData.interfaces.FinanceOriginDocumentLine
{
    getOrderCost(): Promise<decimal> {
        return this.orderCost;
    }

    getValuedCost(): Promise<decimal> {
        return xtremStockData.functions.stockDocumentLib.defaultDocumentLineCost(this.$.context, this, {
            valuationType: 'receipt',
        });
    }

    // eslint-disable-next-line class-methods-use-this
    getValuationParameters(): xtremStockData.interfaces.ValuationParameter {
        return { valuationType: 'receipt' };
    }

    getItem(): Promise<xtremMasterData.nodes.Item> {
        return this.item;
    }

    @decorators.referenceProperty<StockReceiptLine, 'document'>({
        isPublished: true,
        isStored: true,
        isVitalParent: true,
        lookupAccess: true,
        node: () => xtremStock.nodes.StockReceipt,
    })
    override readonly document: Reference<xtremStock.nodes.StockReceipt>;

    @decorators.stringPropertyOverride<StockReceiptLine, 'documentNumber'>({
        async getValue() {
            return (await this.document).number;
        },
    })
    override readonly documentNumber: Promise<string>;

    @decorators.integerPropertyOverride<StockReceiptLine, 'documentId'>({
        async getValue() {
            return (await this.document)._id;
        },
    })
    override readonly documentId: Promise<integer>;

    @decorators.enumProperty<StockReceiptLine, 'stockTransactionStatus'>({
        isStored: true,
        isPublished: true,
        defaultValue() {
            return 'draft';
        },
        dataType: () => xtremStockData.enums.StockDocumentTransactionStatusDataType,
    })
    readonly stockTransactionStatus: Promise<xtremStockData.enums.StockDocumentTransactionStatus>;

    @decorators.referenceProperty<StockReceiptLine, 'itemSite'>({
        isPublished: true,
        isNullable: true,
        node: () => xtremMasterData.nodes.ItemSite,
        join: {
            item() {
                return this.item;
            },
            async site() {
                return (await this.document).stockSite;
            },
        },
    })
    readonly itemSite: Reference<xtremMasterData.nodes.ItemSite | null>;

    @decorators.referenceProperty<StockReceiptLine, 'item'>({
        isPublished: true,
        isStored: true,
        lookupAccess: true,
        filters: {
            control: {
                isStockManaged: true,
                isPhantom: false,
                // TODO: uncomment when is XT-12798 implemented
                // itemSites: {
                //     _atLeast: 1,
                //     site() {
                //         return this.document.stockSite;
                //     },
                // },
            },
        },
        node: () => xtremMasterData.nodes.Item,
    })
    readonly item: Reference<xtremMasterData.nodes.Item>;

    @decorators.referenceProperty<StockReceiptLine, 'stockUnit'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
        dependsOn: ['item'],
        lookupAccess: true,
        filters: {
            control: {
                async type() {
                    return (await (await this.item).stockUnit).type;
                },
            },
        },
        node: () => xtremMasterData.nodes.UnitOfMeasure,
        async defaultValue() {
            // item may still be null when getting default values
            return (await this.item)?.stockUnit;
        },
    })
    readonly stockUnit: Reference<xtremMasterData.nodes.UnitOfMeasure | null>;

    @decorators.decimalProperty<StockReceiptLine, 'quantityInStockUnit'>({
        isPublished: true,
        isStored: true,
        dataType: () => xtremMasterData.dataTypes.quantityInStockUnit,
        async control(ctx, val) {
            await ctx.error.if(val).is.not.positive();
        },
    })
    readonly quantityInStockUnit: Promise<decimal>;

    @decorators.referenceProperty<StockReceiptLine, 'location'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
        // TODO: uncomment when is XT-12798 implemented
        // filters: {
        //     control: {
        //         locationZone: {
        //             site() {
        //                 return this.document.stockSite;
        //             },
        //         },
        //     },
        // },
        node: () => xtremMasterData.nodes.Location,
    })
    readonly location: Reference<xtremMasterData.nodes.Location | null>;

    @decorators.referenceProperty<StockReceiptLine, 'stockStatus'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
        node: () => xtremStockData.nodes.StockStatus,
    })
    readonly stockStatus: Reference<xtremStockData.nodes.StockStatus | null>;

    @decorators.referenceProperty<StockReceiptLine, 'existingLot'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
        filters: {
            control: {
                item() {
                    return this.item;
                },
            },
        },
        node: () => xtremStockData.nodes.Lot,
    })
    readonly existingLot: Reference<xtremStockData.nodes.Lot | null>;

    // Lot to be created in database
    @decorators.jsonProperty<StockReceiptLine, 'lotCreateData'>({
        isPublished: true,
        isTransientInput: true,
        isNullable: true,
    })
    readonly lotCreateData: Promise<xtremStockData.interfaces.LotCreateData | null>;

    @decorators.decimalProperty<StockReceiptLine, 'orderCost'>({
        isPublished: true,
        isStored: true,
        dependsOn: ['itemSite', 'quantityInStockUnit'],
        dataType: () => xtremMasterData.dataTypes.costDataType,
        defaultValue() {
            return xtremStockData.functions.stockDocumentLib.defaultDocumentLineCost(this.$.context, this, {
                valuationType: 'receipt',
            });
        },
        async control(ctx, val) {
            await ctx.error.if(val).is.negative();
        },
    })
    readonly orderCost: Promise<decimal>;

    @decorators.decimalProperty<StockReceiptLine, 'totalCost'>({
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.amountInCompanyCurrency,
        dependsOn: ['quantityInStockUnit', 'orderCost'],
        async getValue() {
            return (await this.quantityInStockUnit) * (await this.orderCost);
        },
    })
    readonly totalCost: Promise<decimal>;

    @decorators.decimalProperty<StockReceiptLine, 'valuedCost'>({
        isPublished: true,
        isStored: true,
        dependsOn: ['itemSite'],
        dataType: () => xtremMasterData.dataTypes.costDataType,
        async defaultValue() {
            return (await (await this.itemSite)?.currentCost) || 0;
        },
    })
    readonly valuedCost: Promise<decimal>;

    @decorators.jsonProperty<StockReceiptLine, 'storedDimensions'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
        dataType: () => xtremFinanceData.dataTypes.storedDimensionsDataType,
        dependsOn: ['item', { document: ['stockSite'] }],
        async defaultValue() {
            // document may still be null when getting default values
            const site = await (await this.document)?.stockSite;
            if (!site) return null;
            return xtremFinanceData.functions.getDefaultDimensions(this.$.context, {
                dimensionDefinitionLevel: 'stockDirect',
                companyId: (await site.legalCompany)._id,
                site,
                item: await this.item,
            });
        },
    })
    readonly storedDimensions: Promise<object | null>;

    @decorators.referenceProperty<StockReceiptLine, 'analyticalData'>({
        isStored: true,
        isMutable: true,
        isPublished: true,
        lookupAccess: true,
        ignoreIsActive: true,
        isFrozen() {
            return xtremFinanceData.functions.isAnalyticalDataFrozen(this);
        },
        node: () => xtremFinanceData.nodes.AnalyticalData,
        dependsOn: ['storedAttributes', 'storedDimensions'],
        async defaultValue() {
            return (await xtremFinanceData.functions.getAnalyticalDataFromStoredAttributesAndDimensions(
                this.$.context,
                await this.storedAttributes,
                await this.storedDimensions,
            )) as any;
        },
        updatedValue: useDefaultValue,
    })
    readonly analyticalData: Reference<xtremFinanceData.nodes.AnalyticalData>;

    @decorators.jsonProperty<StockReceiptLine, 'storedAttributes'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
        dataType: () => xtremFinanceData.dataTypes.storedAttributesDataType,
        dependsOn: ['item', { document: ['stockSite'] }],
        async defaultValue() {
            // document may still be null when getting default values
            const site = await (await this.document)?.stockSite;
            if (!site) return null;
            return xtremFinanceData.functions.getDefaultAttributes(this.$.context, {
                dimensionDefinitionLevel: 'stockDirect',
                companyId: (await site.legalCompany)._id,
                site,
                item: await this.item,
            });
        },
    })
    readonly storedAttributes: Promise<xtremMasterData.interfaces.StoredAttributes | null>;

    @decorators.jsonProperty<StockReceiptLine, 'computedAttributes'>({
        isPublished: true,
        async computeValue() {
            const stockSite = await (await this.document).stockSite;
            return xtremStock.functions.analytical.computeAttributes(this.$.context, {
                item: await this.item,
                stockSite,
                financialSite: await xtremFinanceData.functions.getFinancialSite(stockSite),
            });
        },
    })
    readonly computedAttributes: Promise<object>;

    @decorators.collectionProperty<StockReceiptLine, 'stockDetails'>({
        isPublished: true,
        isVital: true,
        reverseReference: 'documentLine',
        node: () => xtremStockData.nodes.StockReceiptDetail,
    })
    readonly stockDetails: Collection<xtremStockData.nodes.StockReceiptDetail>;

    @decorators.jsonProperty<StockReceiptLine, 'jsonStockDetails'>({
        isPublished: true,
        async setValue(details) {
            if (details && details.length > 0) {
                await this.$.set({
                    stockDetails: xtremStockData.functions.stockDetailLib.parseDetails(details) as Array<
                        NodeCreateData<xtremStockData.nodes.StockReceiptDetail>
                    >,
                });
            }
        },
        async computeValue() {
            if ((await this.stockDetails.length) > 0) {
                return this.stockDetails
                    .map(detail =>
                        xtremStockData.functions.stockDetailLib.filterStockDetailProperties(
                            detail,
                            xtremStockData.nodes.StockReceiptDetail,
                            {
                                onlyIds: true,
                            },
                        ),
                    )
                    .toArray();
            }
            // forcing typing to accept an empty object
            return {} as any;
        },
    })
    readonly jsonStockDetails: Promise<Array<JsonType<NodeCreateData<xtremStockData.nodes.StockReceiptDetail>>>>;

    @decorators.collectionProperty<StockReceiptLine, 'stockMovements'>({
        isPublished: true,
        reverseReference: 'documentLine',
        node: () => xtremStockData.nodes.StockJournal,
    })
    readonly stockMovements: Collection<xtremStockData.nodes.StockJournal>;

    @decorators.collectionProperty<StockReceiptLine, 'stockTransactions'>({
        isPublished: true,
        reverseReference: 'documentLine',
        isVital: true,
        node: () => xtremStockData.nodes.StockTransaction,
    })
    readonly stockTransactions: Collection<xtremStockData.nodes.StockTransaction>;

    // the site property on line level is needed for the accounting interface
    @decorators.referenceProperty<StockReceiptLine, 'site'>({
        isPublished: true,
        node: () => xtremSystem.nodes.Site,
        async getValue() {
            return (await this.document).stockSite;
        },
    })
    readonly site: Reference<xtremSystem.nodes.Site>;

    @decorators.enumProperty<StockReceiptLine, 'stockDetailStatus'>({
        isPublished: true,
        isStored: true,
        dataType: () => xtremStockData.enums.stockDetailStatusDataType,
        dependsOn: ['item', 'stockDetails'],
        async defaultValue() {
            return xtremStockData.functions.stockDetailLib.getStockDetailStatus({
                line: this,
                quantityExpected: await this.quantityInStockUnit,
                checkSerialNumbers: false,
            });
        },
        updatedValue: useDefaultValue,
    })
    readonly stockDetailStatus: Promise<xtremStockData.enums.StockDetailStatus>;

    @decorators.enumProperty<StockReceiptLine, 'displayStatus'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremStock.enums.stockReceiptLineDisplayStatusDataType,
        dependsOn: ['stockDetailStatus', 'stockTransactionStatus'],
        async defaultValue() {
            return xtremStock.functions.stockReceiptLib.calculateStockReceiptLineDisplayStatus(
                await this.stockDetailStatus,
                await this.stockTransactionStatus,
            );
        },
        updatedValue: useDefaultValue,
    })
    readonly displayStatus: Promise<xtremStock.enums.StockReceiptLineDisplayStatus>;

    /**
     * Method that applies dimensions and attributes to the lines of a stock receipt line
     * @param context
     * @param StockReceiptLine
     * @param storedDimensions
     * @param storedAttributes
     * @returns true or throws error
     */
    @decorators.mutation<typeof StockReceiptLine, 'setDimension'>({
        isPublished: true,
        parameters: [
            {
                name: 'stockReceiptLine',
                type: 'reference',
                node: () => xtremStock.nodes.StockReceiptLine,
                isMandatory: true,
                isWritable: true,
            },
            { name: 'storedDimensions', type: 'string' },
            { name: 'storedAttributes', type: 'string' },
        ],
        return: 'boolean',
    })
    static async setDimension(
        _context: Context,
        stockReceiptLine: xtremStock.nodes.StockReceiptLine,
        storedDimensions?: string,
        storedAttributes?: string,
    ): Promise<boolean> {
        await stockReceiptLine.$.set({
            storedDimensions: storedDimensions ? JSON.parse(storedDimensions) : null,
            storedAttributes: storedAttributes ? JSON.parse(storedAttributes) : null,
        });
        await stockReceiptLine.$.save();
        return true;
    }
}
