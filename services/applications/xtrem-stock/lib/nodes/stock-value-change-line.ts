import type { Collection, JsonType, NodeCreateData, Reference, date, decimal, integer } from '@sage/xtrem-core';
import { NodeStatus, decorators, useDefaultValue } from '@sage/xtrem-core';
import * as xtremFinanceData from '@sage/xtrem-finance-data';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremStockData from '@sage/xtrem-stock-data';
import * as xtremSystem from '@sage/xtrem-system';
import * as xtremStock from '..';

@decorators.subNode<StockValueChangeLine>({
    isPublished: true,
    canRead: true,
    canSearch: true,
    canCreate: true,
    canUpdate: true,
    isVitalCollectionChild: true,
    extends: () => xtremMasterData.nodes.BaseDocumentLine,
    async controlBegin(cx) {
        // Currently we only have 1 record in stockDetails
        // and the variance recorded in StockValueDetail must be equal to the variance in the document line
        const totalStockDetails = await this.stockDetails.sum(stockDetail => stockDetail.amount);
        const varianceLine = (await this.newAmount) - (await this.amount);
        if (varianceLine !== totalStockDetails) {
            cx.error.addLocalized(
                '@sage/xtrem-stock/nodes__stock_value_change_line__check_amount_stock_detail',
                'The amount in stockDetails {{totalStockDetails}} is not equal to the value change variance on the document line {{varianceLine}}',
                { totalStockDetails, varianceLine },
            );
        }
    },
    async controlEnd(cx) {
        await xtremFinanceData.functions.attributTypeRestrictedToCheck(cx, this.$.context, await this.storedAttributes);
    },
})
@xtremStockData.functions.staticImplements<xtremStockData.interfaces.StaticDocumentLineWithStockPosting>()
export class StockValueChangeLine
    extends xtremMasterData.nodes.BaseDocumentLine
    implements xtremFinanceData.interfaces.FinanceOriginDocumentLine
{
    getOrderCost(): Promise<decimal> {
        return this.newUnitCost;
    }

    getValuedCost(): Promise<decimal> {
        return this.newAmount;
    }

    async getItem(): Promise<xtremMasterData.nodes.Item> {
        return (await this.document).item;
    }

    @decorators.referenceProperty<StockValueChangeLine, 'document'>({
        isStored: true,
        isPublished: true,
        isVitalParent: true,
        node: () => xtremStock.nodes.StockValueChange,
        lookupAccess: true,
    })
    override readonly document: Reference<xtremStock.nodes.StockValueChange>;

    @decorators.stringPropertyOverride<StockValueChangeLine, 'documentNumber'>({
        async getValue() {
            return (await this.document).number;
        },
    })
    override readonly documentNumber: Promise<string>;

    @decorators.integerPropertyOverride<StockValueChangeLine, 'documentId'>({
        async getValue() {
            return (await this.document)._id;
        },
    })
    override readonly documentId: Promise<integer>;

    @decorators.referenceProperty<StockValueChangeLine, 'currency'>({
        isPublished: true,
        lookupAccess: true,
        dependsOn: [{ document: ['site'] }],
        node: () => xtremMasterData.nodes.Currency,
        // eslint-disable-next-line @sage/xtrem/property-decorators-errors
        async computeValue() {
            return (await (await this.document).site).financialCurrency;
        },
    })
    readonly currency: Reference<xtremMasterData.nodes.Currency>;

    @decorators.referenceProperty<StockValueChangeLine, 'stockUnit'>({
        isPublished: true,
        ignoreIsActive: true,
        lookupAccess: true,
        dependsOn: [{ document: ['item'] }],
        node: () => xtremMasterData.nodes.UnitOfMeasure,
        async getValue() {
            return (await (await this.document).item).stockUnit;
        },
    })
    readonly stockUnit: Reference<xtremMasterData.nodes.UnitOfMeasure>;

    @decorators.referenceProperty<StockValueChangeLine, 'fifoCost'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
        node: () => xtremStockData.nodes.FifoValuationTier,
        async control(cx, val) {
            if (
                this.$.status === NodeStatus.added &&
                (await (
                    await this.document
                ).lines.some(async line => (await line.fifoCost) === val && line._id !== this._id))
            ) {
                cx.error.addLocalized(
                    '@sage/xtrem-stock/pages__stock_value_change__duplication_among_added_lines',
                    'Change or remove duplicate lines.',
                );
            }
        },
        lookupAccess: true,
    })
    readonly fifoCost: Reference<xtremStockData.nodes.FifoValuationTier | null>;

    @decorators.decimalProperty<StockValueChangeLine, 'quantity'>({
        isStored: true,
        isPublished: true,
        isRequired: true,
        async control(cx, val) {
            await cx.error.if(val).is.negative();
        },
        dataType: () => xtremMasterData.dataTypes.quantityInStockUnit,
        lookupAccess: true,
    })
    readonly quantity: Promise<decimal>;

    @decorators.decimalProperty<StockValueChangeLine, 'quantityInStockUnit'>({
        isPublished: true,
        getValue() {
            return this.quantity;
        },
        dataType: () => xtremMasterData.dataTypes.quantityInStockUnit,
        lookupAccess: true,
    })
    readonly quantityInStockUnit: Promise<decimal>;

    @decorators.decimalProperty<StockValueChangeLine, 'unitCost'>({
        isStored: true,
        isPublished: true,
        isRequired: true,
        dataType: () => xtremMasterData.dataTypes.costDataType,
        async control(cx, val) {
            await cx.error.if(val).is.negative();
        },
        lookupAccess: true,
    })
    readonly unitCost: Promise<decimal>;

    @decorators.decimalProperty<StockValueChangeLine, 'newUnitCost'>({
        isStored: true,
        isPublished: true,
        isRequired: true,
        dataType: () => xtremMasterData.dataTypes.costDataType,
        async control(cx, val) {
            await cx.error.if(val).is.negative();
        },
        lookupAccess: true,
    })
    readonly newUnitCost: Promise<decimal>;

    @decorators.decimalProperty<StockValueChangeLine, 'newAmount'>({
        isPublished: true,
        isStored: true,
        isRequired: true,
        dataType: () => xtremMasterData.dataTypes.amountDataType,
        async control(cx, val) {
            await cx.error.if(val).is.negative();
        },
        lookupAccess: true,
    })
    readonly newAmount: Promise<decimal>;

    @decorators.decimalProperty<StockValueChangeLine, 'amount'>({
        isPublished: true,
        isStored: true,
        isRequired: true,
        dataType: () => xtremMasterData.dataTypes.amountDataType,
        async control(cx, val) {
            await cx.error.if(val).is.negative();
        },
        lookupAccess: true,
    })
    readonly amount: Promise<decimal>;

    @decorators.jsonProperty<StockValueChangeLine, 'storedDimensions'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
        dataType: () => xtremFinanceData.dataTypes.storedDimensionsDataType,
        dependsOn: ['item', { document: ['site'] }],
        async defaultValue() {
            const site = await (await this.document).site;
            return xtremFinanceData.functions.getDefaultDimensions(this.$.context, {
                dimensionDefinitionLevel: 'stockDirect',
                companyId: (await (await (await this.document).site).legalCompany)._id,
                site,
                item: await this.item,
            });
        },
        lookupAccess: true,
    })
    readonly storedDimensions: Promise<object | null>;

    @decorators.referenceProperty<StockValueChangeLine, 'analyticalData'>({
        isStored: true,
        isMutable: true,
        isPublished: true,
        lookupAccess: true,
        ignoreIsActive: true,
        isFrozen() {
            return xtremFinanceData.functions.isAnalyticalDataFrozen(this);
        },
        node: () => xtremFinanceData.nodes.AnalyticalData,
        dependsOn: ['storedAttributes', 'storedDimensions'],
        async defaultValue() {
            return (await xtremFinanceData.functions.getAnalyticalDataFromStoredAttributesAndDimensions(
                this.$.context,
                await this.storedAttributes,
                await this.storedDimensions,
            )) as any;
        },
        updatedValue: useDefaultValue,
    })
    readonly analyticalData: Reference<xtremFinanceData.nodes.AnalyticalData>;

    @decorators.jsonProperty<StockValueChangeLine, 'storedAttributes'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
        dataType: () => xtremFinanceData.dataTypes.storedAttributesDataType,
        dependsOn: ['item', { document: ['site'] }],
        async defaultValue() {
            const site = await (await this.document).site;
            return xtremFinanceData.functions.getDefaultAttributes(this.$.context, {
                dimensionDefinitionLevel: 'stockDirect',
                companyId: (await (await (await this.document).site).legalCompany)._id,
                site,
                item: await this.item,
            });
        },
        lookupAccess: true,
    })
    readonly storedAttributes: Promise<xtremMasterData.interfaces.StoredAttributes | null>;

    @decorators.jsonProperty<StockValueChangeLine, 'computedAttributes'>({
        isPublished: true,
        async computeValue() {
            return xtremStock.functions.analytical.computeAttributes(this.$.context, {
                item: await (await this.document).item,
                stockSite: await (await this.document).getStockSite(),
                financialSite: await xtremFinanceData.functions.getFinancialSite(
                    await (await this.document).getStockSite(),
                ),
            });
        },
        lookupAccess: true,
    })
    readonly computedAttributes: Promise<object>;

    @decorators.enumProperty<StockValueChangeLine, 'stockTransactionStatus'>({
        isStored: true,
        isPublished: true,
        defaultValue() {
            return 'draft';
        },
        dataType: () => xtremStockData.enums.StockDocumentTransactionStatusDataType,
        lookupAccess: true,
    })
    readonly stockTransactionStatus: Promise<xtremStockData.enums.StockDocumentTransactionStatus>;

    @decorators.collectionProperty<StockValueChangeLine, 'stockDetails'>({
        isPublished: true,
        isVital: true,
        reverseReference: 'documentLine',
        node: () => xtremStockData.nodes.StockValueDetail,
        lookupAccess: true,
    })
    readonly stockDetails: Collection<xtremStockData.nodes.StockValueDetail>;

    @decorators.jsonProperty<StockValueChangeLine, 'jsonStockDetails'>({
        isPublished: true,
        dependsOn: ['stockDetails'],
        async setValue(details) {
            if (details && details.length > 0) {
                await this.$.set({
                    stockDetails: xtremStockData.functions.stockDetailLib.parseDetails(details) as Array<
                        NodeCreateData<xtremStockData.nodes.StockValueDetail>
                    >,
                });
            }
        },
        async computeValue() {
            if ((await this.stockDetails.length) > 0) {
                return this.stockDetails.map(detail =>
                    xtremStockData.functions.stockDetailLib.filterStockDetailStockValueChangeProperties(detail),
                );
            }
            // forcing typing to accept an empty object
            return [] as any;
        },
        lookupAccess: true,
    })
    readonly jsonStockDetails: Promise<Array<JsonType<NodeCreateData<xtremStockData.nodes.StockValueDetail>>>>;

    @decorators.collectionProperty<StockValueChangeLine, 'stockTransactions'>({
        isPublished: true,
        reverseReference: 'documentLine',
        isVital: true,
        node: () => xtremStockData.nodes.StockTransaction,
        async isFrozen() {
            return (await (await this.document).stockTransactionStatus) === 'completed';
        },
        lookupAccess: true,
    })
    readonly stockTransactions: Collection<xtremStockData.nodes.StockTransaction>;

    // the site property on line level is needed for the accounting interface and stock valuation
    @decorators.referenceProperty<StockValueChangeLine, 'site'>({
        isPublished: true,
        node: () => xtremSystem.nodes.Site,
        async getValue() {
            return (await this.document).site;
        },
        lookupAccess: true,
    })
    readonly site: Reference<xtremSystem.nodes.Site>;

    // the item property on line level is needed for the accounting interface and stock valuation
    @decorators.referenceProperty<StockValueChangeLine, 'item'>({
        isPublished: true,
        node: () => xtremMasterData.nodes.Item,
        async getValue() {
            return (await this.document).item;
        },
        lookupAccess: true,
    })
    readonly item: Reference<xtremMasterData.nodes.Item>;

    // We need the effectiveDate on the line level for stock valuation.
    @decorators.dateProperty<StockValueChangeLine, 'effectiveDate'>({
        isPublished: true,
        async getValue() {
            return (await this.document).effectiveDate;
        },
        lookupAccess: true,
    })
    readonly effectiveDate: Promise<date>;

    // the stockMovements property on line level is needed for the accounting interface
    @decorators.collectionProperty<StockValueChangeLine, 'stockMovements'>({
        isPublished: true,
        reverseReference: 'documentLine',
        node: () => xtremStockData.nodes.StockJournal,
        lookupAccess: true,
    })
    readonly stockMovements: Collection<xtremStockData.nodes.StockJournal>;

    // eslint-disable-next-line class-methods-use-this
    getValuationParameters(): xtremStockData.interfaces.ValuationParameter {
        return { valuationType: 'valueChange' };
    }
}
