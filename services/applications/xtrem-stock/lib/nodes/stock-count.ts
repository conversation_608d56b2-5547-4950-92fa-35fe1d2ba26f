import type { NotificationEnvelope } from '@sage/xtrem-communication';
import type { Collection, Context, Reference, integer } from '@sage/xtrem-core';
import {
    BusinessRuleError,
    LocalizedError,
    Logger,
    Node,
    NodeStatus,
    SystemError,
    asyncArray,
    date,
    decorators,
    useDefaultValue,
} from '@sage/xtrem-core';
import * as xtremFinanceData from '@sage/xtrem-finance-data';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremStockData from '@sage/xtrem-stock-data';
import * as xtremSystem from '@sage/xtrem-system';
import * as xtremStock from '..';

const logger = Logger.getLogger(__filename, 'stock-count');

@decorators.node<StockCount>({
    isClearedByReset: true,
    storage: 'sql',
    isPublished: true,
    canCreate: true,
    canRead: true,
    canSearch: true,
    canUpdate: true,
    canDelete: true,
    isCustomizable: true,
    hasAttachments: true,
    indexes: [{ orderBy: { number: +1 }, isUnique: true, isNaturalKey: true }],
    async controlEnd(cx) {
        // check for duplicate lines
        const addedLines = this.lines.filter(line => line.$.status === NodeStatus.added);
        await addedLines.forEach(async (line1, index1) => {
            await addedLines.forEach(async (line2, index2) => {
                if (index2 <= index1 || (await line1.item)._id !== (await line2.item)._id) {
                    return;
                }
                const stockDetailLot1 = await (await line1.stockDetails.elementAt(0)).stockDetailLot;
                const stockDetailLot2 = await (await line2.stockDetails.elementAt(0)).stockDetailLot;
                const hasDuplicatesIndependentFromLotReference =
                    (await line1.stockStatus)?._id === (await line2.stockStatus)?._id &&
                    (await line1.owner) === (await line2.owner) &&
                    (await line1.location)?._id === (await line2.location)?._id &&
                    (await stockDetailLot1?.sublot) === (await stockDetailLot2?.sublot);
                const hasDuplicatesAndLotReference =
                    (await stockDetailLot1?.lot) &&
                    (await stockDetailLot1?.lot)?._id === (await stockDetailLot2?.lot)?._id;
                const hasDuplicatesAndNoLotReference =
                    !(await stockDetailLot1?.lot) &&
                    (await stockDetailLot1?.lotNumber) &&
                    (await stockDetailLot1?.lotNumber) === (await stockDetailLot2?.lotNumber);
                if (
                    hasDuplicatesIndependentFromLotReference &&
                    (hasDuplicatesAndLotReference || hasDuplicatesAndNoLotReference)
                ) {
                    cx.at('lines', `${line1._id}`).error.addLocalized(
                        '@sage/xtrem-stock/pages__stock_count__duplication_among_added_lines',
                        'Change or remove duplicate lines.',
                    );
                }
            });
        });
    },
    async saveBegin() {
        await xtremMasterData.functions.controlDocumentNumber(this);

        // Update status if necessary.
        // Done in saveBegin because lines' status is may be set correctly in prepare
        if (
            !['draft', 'closed'].includes(await this.status) &&
            (await this.lines.every(async line => ['excluded', 'counted'].includes(await line.status)))
        ) {
            await this.$.set({ status: 'counted' });
        } else if ((await this.status) === 'counted') await this.$.set({ status: 'countInProgress' });
    },
    async controlDelete(cx) {
        if ((await this.status) !== 'toBeCounted' && (await this.lines.length) > 0) {
            cx.error.addLocalized(
                '@sage/xtrem-stock/nodes__stock_count__deletion_forbidden',
                'You cannot delete the stock count.',
            );
        }
    },
})
@xtremStockData.functions.staticImplements<xtremStockData.interfaces.StaticDocumentHeaderWithStockPosting>()
export class StockCount extends Node {
    // this flag allows to know if the confirmCreateStockCount mutation is in progress
    isConfirmingStockCountCreation: boolean;

    getEffectiveDate(): Promise<date> {
        return this.effectiveDate;
    }

    getStockSite(): Promise<xtremSystem.nodes.Site> {
        return this.stockSite;
    }

    @decorators.stringProperty<StockCount, 'number'>({
        isStored: true,
        isPublished: true,
        provides: ['sequenceNumber'],
        lookupAccess: true,
        dataType: () => xtremMasterData.dataTypes.documentNumber,
        defaultValue() {
            return xtremMasterData.functions.getDocumentNumberPlaceholder();
        },
        deferredDefaultValue() {
            return xtremMasterData.functions.allocateDocumentNumber(this);
        },
        async isFrozen() {
            return (await this.status) === 'closed';
        },
    })
    readonly number: Promise<string>;

    @decorators.stringProperty<StockCount, 'description'>({
        isStored: true,
        isPublished: true,
        isRequired: true,
        lookupAccess: true,
        dataType: () => xtremSystem.dataTypes.description,
        async isFrozen() {
            return (await this.status) === 'closed';
        },
    })
    readonly description: Promise<string>;

    @decorators.referenceProperty<StockCount, 'stockSite'>({
        isStored: true,
        isPublished: true,
        isRequired: true,
        ignoreIsActive: true,
        provides: ['site'],
        lookupAccess: true,
        filters: {
            control: { isInventory: true },
        },
        node: () => xtremSystem.nodes.Site,
        async isFrozen() {
            return (await this.status) === 'closed';
        },
    })
    readonly stockSite: Reference<xtremSystem.nodes.Site>;

    @decorators.enumProperty<StockCount, 'status'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremStock.enums.StockCountStatusDataType,
        lookupAccess: true,
        defaultValue() {
            return 'draft';
        },
        async isFrozen() {
            return (await this.status) === 'closed';
        },
    })
    readonly status: Promise<xtremStock.enums.StockCountStatus>;

    @decorators.booleanProperty<StockCount, 'forceUpdateForStock'>({
        defaultValue: false,
    })
    readonly forceUpdateForStock: Promise<boolean>;

    @decorators.enumProperty<StockCount, 'stockTransactionStatus'>({
        isPublished: true,
        isStored: true,
        dependsOn: [{ lines: ['stockTransactionStatus'] }],
        dataType: () => xtremStockData.enums.StockDocumentTransactionStatusDataType,
        defaultValue() {
            return xtremStockData.functions.stockTransactionLib.computeHeaderStatus(this);
        },
        updatedValue: useDefaultValue,
        lookupAccess: true,
    })
    readonly stockTransactionStatus: Promise<xtremStockData.enums.StockDocumentTransactionStatus>;

    @decorators.dateProperty<StockCount, 'effectiveDate'>({
        isStored: true,
        isPublished: true,
        isRequired: true,
        provides: ['documentDate'],
        lookupAccess: true,
        defaultValue() {
            return date.today();
        },
        async isFrozen() {
            return (await this.status) === 'closed';
        },
    })
    readonly effectiveDate: Promise<date>;

    @decorators.enumProperty<StockCount, 'financeIntegrationStatus'>({
        isPublished: true,
        dataType: () => xtremFinanceData.enums.financeIntegrationStatusDataType,
        lookupAccess: true,
        async computeValue() {
            return xtremFinanceData.functions.getDocumentIntegrationStatus(
                this.$.context,
                'stockCount',
                await this.number,
            );
        },
        async isFrozen() {
            return (await this.status) === 'closed';
        },
    })
    readonly financeIntegrationStatus: Promise<xtremFinanceData.enums.FinanceIntegrationStatus>;

    @decorators.stringProperty<StockCount, 'counter'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        dataType: () => xtremSystem.dataTypes.name,
        async isFrozen() {
            return (await this.status) === 'closed';
        },
    })
    readonly counter: Promise<string>;

    // criteria are not changeable once stock count is created
    @decorators.dateProperty<StockCount, 'lastCountDate'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
        async isFrozen() {
            return (await this.status) !== 'draft';
        },
        lookupAccess: true,
        async control(cx, val) {
            if (val) {
                await cx.error.if(val).is.after(await this.effectiveDate);
            }
        },
    })
    readonly lastCountDate: Promise<date | null>;

    @decorators.referenceProperty<StockCount, 'fromItem'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
        ignoreIsActive: true,
        dependsOn: ['stockSite'],
        lookupAccess: true,
        async isFrozen() {
            return (await this.status) !== 'draft';
        },
        filters: {
            control: {
                isStockManaged: true,
                async itemSites() {
                    return { _atLeast: 1, site: await this.stockSite };
                },
                isPhantom: false,
            },
        },
        node: () => xtremMasterData.nodes.Item,
    })
    readonly fromItem: Reference<xtremMasterData.nodes.Item | null>;

    @decorators.referenceProperty<StockCount, 'toItem'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
        ignoreIsActive: true,
        dependsOn: ['stockSite'],
        lookupAccess: true,
        async isFrozen() {
            return (await this.status) !== 'draft';
        },
        filters: {
            control: {
                isStockManaged: true,
                async itemSites() {
                    return { _atLeast: 1, site: await this.stockSite };
                },
                isPhantom: false,
            },
        },
        node: () => xtremMasterData.nodes.Item,
    })
    readonly toItem: Reference<xtremMasterData.nodes.Item | null>;

    @decorators.referenceArrayProperty<StockCount, 'locations'>({
        isPublished: true,
        isStored: true,
        dependsOn: ['stockSite'],
        lookupAccess: true,
        defaultValue: [],
        async isFrozen() {
            return (await this.status) !== 'draft';
        },
        filters: {
            control: {
                locationZone: {
                    site() {
                        return this.stockSite;
                    },
                },
            },
        },
        ignoreIsActive: true,
        onDelete: 'restrict',
        node: () => xtremMasterData.nodes.Location,
    })
    readonly locations: Promise<xtremMasterData.nodes.Location[]>;

    @decorators.referenceArrayProperty<StockCount, 'zones'>({
        isPublished: true,
        isStored: true,
        dependsOn: ['stockSite'],
        lookupAccess: true,
        defaultValue: [],
        async isFrozen() {
            return (await this.status) !== 'draft';
        },
        filters: {
            control: {
                site() {
                    return this.stockSite;
                },
            },
        },
        onDelete: 'restrict',
        node: () => xtremMasterData.nodes.LocationZone,
    })
    readonly zones: Promise<xtremMasterData.nodes.LocationZone[]>;

    @decorators.referenceArrayProperty<StockCount, 'categories'>({
        isPublished: true,
        isStored: true,
        lookupAccess: true,
        defaultValue: [],
        async isFrozen() {
            return (await this.status) !== 'draft';
        },
        ignoreIsActive: true,
        onDelete: 'restrict',
        node: () => xtremMasterData.nodes.ItemCategory,
    })
    readonly categories: Promise<xtremMasterData.nodes.ItemCategory[]>;

    @decorators.collectionProperty<StockCount, 'lines'>({
        isPublished: true,
        isVital: true,
        reverseReference: 'document',
        lookupAccess: true,
        node: () => xtremStock.nodes.StockCountLine,
    })
    readonly lines: Collection<xtremStock.nodes.StockCountLine>;

    readonly linesWithStockTransactionStatusCollectionName = 'lines';

    // property needed for the accounting interface
    @decorators.referenceProperty<StockCount, 'financialSite'>({
        isPublished: true,
        dependsOn: ['stockSite'],
        lookupAccess: true,
        node: () => xtremSystem.nodes.Site,
        async getValue() {
            return xtremFinanceData.functions.getFinancialSite(await this.stockSite);
        },
        async isFrozen() {
            return (await this.status) === 'closed';
        },
    })
    readonly financialSite: Reference<xtremSystem.nodes.Site>;

    // property needed for the accounting interface
    @decorators.referenceProperty<StockCount, 'transactionCurrency'>({
        isPublished: true,
        dependsOn: ['financialSite'],
        lookupAccess: true,
        node: () => xtremMasterData.nodes.Currency,
        async getValue() {
            return (await (await this.financialSite).legalCompany).currency;
        },
        async isFrozen() {
            return (await this.status) === 'closed';
        },
    })
    readonly transactionCurrency: Reference<xtremMasterData.nodes.Currency>;

    // property needed for the accounting interface
    @decorators.dateProperty<StockCount, 'documentDate'>({
        isPublished: true,
        lookupAccess: true,
        dependsOn: ['effectiveDate'],
        getValue() {
            return this.effectiveDate;
        },
        async isFrozen() {
            return (await this.status) === 'closed';
        },
    })
    readonly documentDate: Promise<date>;

    @decorators.booleanProperty<StockCount, 'hasStockRecords'>({
        isStored: true,
        isPublished: true,
        defaultValue: true,
    })
    readonly hasStockRecords: Promise<boolean>;

    @decorators.collectionProperty<StockCount, 'itemSites'>({
        isPublished: true,
        lookupAccess: true,
        node: () => xtremMasterData.nodes.ItemSite,
        dependsOn: ['stockSite', 'fromItem', 'toItem', 'lastCountDate', 'hasStockRecords', 'concurrentStockCountLines'],
        async getFilter() {
            return xtremStock.functions.stockCountLib.filterItemSite(this.$.context, {
                stockSite: await this.stockSite,
                fromItem: await this.fromItem,
                toItem: await this.toItem,
                lastCountDate: (await this.lastCountDate) ? String(await this.lastCountDate) : null,
                hasStockRecords: await this.hasStockRecords,
                locations: (await this.locations) ?? [],
                zones: (await this.zones) ?? [],
                categories: (await this.categories) ?? [],
                concurrentStockCountLines: await this.concurrentStockCountLines.toArray(),
            });
        },
    })
    readonly itemSites: Collection<xtremMasterData.nodes.ItemSite>;

    @decorators.collectionProperty<StockCount, 'concurrentStockCountLines'>({
        node: () => xtremStock.nodes.StockCountLine,
        async getFilter() {
            return {
                document: {
                    _id: { _ne: this._id },
                    stockSite: (await this.stockSite)._id,
                    status: { _in: ['toBeCounted', 'counted', 'countInProgress'] },
                },
            };
        },
    })
    readonly concurrentStockCountLines: Collection<xtremStock.nodes.StockCountLine>;

    @decorators.booleanProperty<StockCount, 'hasLotInLines'>({
        isPublished: true,
        getValue() {
            return this.lines.some(async line => (await (await line.item).lotManagement) !== 'notManaged');
        },
    })
    readonly hasLotInLines: Promise<boolean>;

    @decorators.booleanProperty<StockCount, 'hasSublotInLines'>({
        isPublished: true,
        getValue() {
            return this.lines.some(async line => (await (await line.item).lotManagement) === 'lotSublotManagement');
        },
    })
    readonly hasSublotInLines: Promise<boolean>;

    @decorators.booleanProperty<StockCount, 'hasExpiryManagementInLines'>({
        isPublished: true,
        getValue() {
            return this.lines.some(async line => (await line.item).isExpiryManaged);
        },
    })
    readonly hasExpiryManagementInLines: Promise<boolean>;

    @decorators.collectionProperty<StockCount, 'postingDetails'>({
        isPublished: true,
        lookupAccess: true,
        node: () => xtremFinanceData.nodes.FinanceTransaction,
        join: {
            documentSysId() {
                return this._id;
            },
            documentType() {
                return 'stockCount';
            },
        },
    })
    readonly postingDetails: Collection<xtremFinanceData.nodes.FinanceTransaction>;

    @decorators.mutation<typeof StockCount, 'repost'>({
        isPublished: true,
        parameters: [
            {
                name: 'stockCount',
                type: 'reference',
                isMandatory: true,
                isWritable: true,
                node: () => StockCount,
            },
            {
                name: 'documentLines',
                type: 'array',
                item: {
                    type: 'object',
                    properties: {
                        baseDocumentLineSysId: 'integer',
                        storedAttributes: 'json',
                        storedDimensions: 'json',
                    },
                },
            },
        ],
        return: { type: 'object', properties: { wasSuccessful: 'boolean', message: 'string' } },
    })
    static async repost(
        context: Context,
        stockCount: StockCount,
        documentLines: xtremFinanceData.interfaces.FinanceIntegrationDocumentLineUpdate[],
    ): Promise<xtremFinanceData.interfaces.MutationResult> {
        if (!xtremFinanceData.functions.canRepost(await stockCount.financeIntegrationStatus)) {
            throw new BusinessRuleError(
                context.localize(
                    '@sage/xtrem-stock/nodes__stock_count__cant_repost_stock_count_when_status_is_not_failed',
                    "You can only repost a stock count if the status is 'Failed' or 'Not recorded'.",
                ),
            );
        }

        const updateLines = documentLines.map(documentLine => {
            return {
                _id: documentLine.baseDocumentLineSysId,
                storedAttributes: documentLine.storedAttributes,
                storedDimensions: documentLine.storedDimensions,
                forceUpdateForFinance: true,
            };
        });
        await stockCount.$.set({ lines: updateLines });
        await stockCount.$.save();

        // send notification in order to update a staging table entry for the accounting engine
        if (await (await (await stockCount.stockSite).legalCompany).doStockPosting) {
            await xtremFinanceData.functions.financeDocumentUpdateNotification(context, {
                document: stockCount,
                lines: await stockCount.lines.toArray(),
                documentType: 'stockCount',
                replyTopic: 'StockCountUpdate/accountingInterface',
                doNotPostOnUpdate: false,
            });
            return {
                wasSuccessful: true,
                message: context.localize(
                    '@sage/xtrem-stock/nodes__stock_count__document_was_posted',
                    'The stock count was posted.',
                ),
            };
        }
        return {
            wasSuccessful: true,
            message: context.localize(
                '@sage/xtrem-stock/nodes__stock_count__company_has_no_stock_posting',
                'Stock has not been posted to this company',
            ),
        };
    }

    @decorators.mutation<typeof StockCount, 'start'>({
        isPublished: true,
        parameters: [
            {
                isMandatory: true,
                isWritable: true,
                name: 'document',
                type: 'reference',
                node: () => StockCount,
            },
        ],
        return: 'boolean',
    })
    static async start(context: Context, document: StockCount): Promise<boolean> {
        let linesToBeCounted = 0;
        await document.lines.forEach(async line => {
            if ((await line.status) === 'toBeCounted') {
                line._canUpdateStockQuantity = true;
                if (await line.isCreatedFromStockRecord) {
                    if (!(await line.stockRecord)) {
                        await line.$.set({ status: 'excluded', quantityInStockUnit: 0 });
                    } else {
                        const quantityInStockUnit = await (await line.stockRecord)?.quantityInStockUnit;
                        if (quantityInStockUnit && quantityInStockUnit > 0) {
                            await line.$.set({ quantityInStockUnit });
                        } else {
                            await line.$.set({ status: 'excluded', quantityInStockUnit: 0 });
                        }
                    }
                } else if (
                    (await xtremStock.functions.stockCountLib.nonStockItemExists(
                        context,
                        await line.item,
                        await line.site,
                    )) > 0
                ) {
                    await line.$.set({ status: 'excluded', quantityInStockUnit: 0 });
                }

                line._canUpdateStockQuantity = false;
                linesToBeCounted += 1;
            }
        });

        if (linesToBeCounted > 0) {
            await document.$.set({ status: 'countInProgress' });
            await document.$.save();
        }

        return linesToBeCounted !== 0;
    }

    @decorators.mutation<typeof StockCount, 'postToStock'>({
        isPublished: true,
        startsReadOnly: true,
        parameters: [
            {
                name: 'documentIds',
                type: 'array',
                item: {
                    type: 'integer',
                },
            },
        ],
        return: { type: 'string' },
    })
    static async postToStock(context: Context, documentIds: integer[]): Promise<string> {
        try {
            await xtremStock.functions.stockCountLib.controlBeforePosting(context, documentIds);
        } catch (error) {
            if (!(error instanceof LocalizedError)) throw error;

            return JSON.stringify({
                error: error.message,
                result: 'error',
            } as xtremStockData.interfaces.PostToStockReturnJsonType<xtremStockData.enums.StockMovementTypeEnum>);
        }

        const notPostedDocumentIds = await context.runInWritableContext(writableContext => {
            return xtremStockData.functions.stockLib.setStockTransactionToProgress(writableContext, {
                documentClass: StockCount,
                documentIds,
            });
        });
        if (!notPostedDocumentIds.length) {
            return JSON.stringify({
                notificationId: 'none',
                result: 'requested',
                documents: [],
            } as xtremStockData.interfaces.PostToStockReturnJsonType<xtremStockData.enums.StockMovementTypeEnum>);
        }

        return xtremStockData.functions.notificationLib.stockCountRequestNotification(context, {
            documentClass: StockCount,
            documentIds,
            lineFilterCallback: xtremStock.nodes.StockCountLine.isLineUpdatingStock,
            onStockReplyCallback: StockCount.onStockReply,
        });
    }

    @decorators.notificationListener<typeof StockCount>({
        startsReadOnly: true,
        topic: 'StockCount/stock/adjustment/reply',
        onError(
            context: Context,
            envelope: NotificationEnvelope<
                xtremStockData.interfaces.StockReplyPayload<xtremStockData.enums.StockMovementTypeEnum.adjustment>
            >,
            error: Error,
        ) {
            return xtremStockData.functions.notificationLib.errorHandler(context, envelope, error, StockCount);
        },
    })
    static async onStockReply(
        readOnlyContext: Context,
        payload: xtremStockData.interfaces.StockReplyPayload<xtremStockData.enums.StockMovementTypeEnum.adjustment>,
    ): Promise<void> {
        const adjustmentUpdateResult = await readOnlyContext.runInWritableContext(
            async writableContext =>
                (
                    await xtremStockData.functions.notificationLib.reactToStockMovementReply(
                        writableContext,
                        payload,
                        StockCount,
                    )
                ).adjustment,
        );
        if (!adjustmentUpdateResult) return;

        if (!adjustmentUpdateResult.transactionHadAnError) {
            await adjustmentUpdateResult.documents.forEach(async document => {
                if (document.isStockDocumentCompleted) {
                    await readOnlyContext.runInWritableContext(async writableContext => {
                        const stockCount = await writableContext.read(
                            StockCount,
                            { _id: document.id },
                            { forUpdate: true },
                        );
                        await StockCount.onceStockCompleted(writableContext, stockCount);
                    });
                }
            });
        }
    }

    static async onceStockCompleted(writableContext: Context, stockCount: StockCount): Promise<void> {
        await stockCount.$.set({
            status: 'closed',
        });
        await stockCount.$.save();

        if (await (await (await stockCount.stockSite).legalCompany).doStockPosting) {
            await xtremFinanceData.functions.stockCountAdjustmentNotification(
                writableContext,
                stockCount as xtremFinanceData.interfaces.FinanceOriginDocument,
                await stockCount.lines
                    .filter(async (line: xtremStock.nodes.StockCountLine) => (await line.quantityVariance) !== 0)
                    .toArray(),
            );
        }
    }

    @decorators.notificationListener<typeof StockCount>({ topic: 'StockCount/accountingInterface' })
    static async onFinanceIntegrationReply(
        context: Context,
        payload: xtremFinanceData.interfaces.FinanceTransactionData,
    ): Promise<void> {
        await xtremFinanceData.functions.reactToFinanceIntegrationReply(context, payload);
    }

    @decorators.query<typeof StockCount, 'existingStockCountLine'>({
        isPublished: true,
        parameters: [
            { name: 'throwErrorIfExist', type: 'boolean', isMandatory: true },
            {
                isMandatory: true,
                name: 'searchCriteria',
                type: 'object',
                properties: {
                    item: { type: 'reference', node: () => xtremMasterData.nodes.Item, isMandatory: true },
                    stockStatus: { type: 'reference', node: () => xtremStockData.nodes.StockStatus, isMandatory: true },
                    stockUnit: {
                        type: 'reference',
                        node: () => xtremMasterData.nodes.UnitOfMeasure,
                        isMandatory: true,
                    },
                    owner: { type: 'string', isMandatory: true },
                    document: {
                        type: 'object',
                        isMandatory: true,
                        properties: {
                            stockSite: { type: 'reference', node: () => xtremSystem.nodes.Site, isMandatory: true },
                        },
                    },
                    location: { type: 'reference', node: () => xtremMasterData.nodes.Location },
                    lot: { type: 'reference', node: () => xtremStockData.nodes.Lot },
                },
            },
            {
                name: 'lotCreateData',
                type: 'object',
                properties: {
                    id: { type: 'string', isMandatory: true },
                    sublot: { type: 'string', isNullable: true },
                },
            },
        ],
        return: { type: 'boolean' },
    })
    static existingStockCountLine(
        context: Context,
        throwErrorIfExist: boolean,
        searchCriteria: {
            item: xtremMasterData.nodes.Item;
            stockStatus: xtremStockData.nodes.StockStatus;
            location?: xtremMasterData.nodes.Location;
            lot?: xtremStockData.nodes.Lot;
            stockUnit: xtremMasterData.nodes.UnitOfMeasure;
            owner: string;
            document: { stockSite: xtremSystem.nodes.Site };
        },
        lotCreateData: {
            id: string;
            sublot: string | null;
        },
    ): Promise<boolean> {
        return xtremStock.functions.stockCountLib.existingStockCountLine(
            context,
            throwErrorIfExist,
            {
                item: searchCriteria.item._id,
                stockStatus: searchCriteria.stockStatus._id,
                location: searchCriteria.location?._id,
                lot: searchCriteria.lot?._id,
                stockUnit: searchCriteria.stockUnit._id,
                owner: searchCriteria.owner,
                document: { stockSite: searchCriteria.document.stockSite._id },
            },
            lotCreateData,
        );
    }

    @decorators.asyncMutation<typeof StockCount, 'confirmZeroQuantity'>({
        isPublished: true,
        parameters: [{ name: 'number', type: 'string' }],
        return: 'integer',
    })
    static async confirmZeroQuantity(context: Context, number: string): Promise<integer> {
        await context.batch.logMessage(
            'info',
            context.localize(
                '@sage/xtrem-stock/nodes__stock_count__start_confirm_zero',
                'The confirm zero quantities process started for the stock count: {{number}}.',
                { number },
            ),
        );
        logger.verbose(() => `${number} confirmZeroQuantity start`);

        const stockCount = await context.read(StockCount, { number }, { forUpdate: true });

        if ((await stockCount.status) === 'toBeCounted') {
            throw new BusinessRuleError(
                context.localize(
                    '@sage/xtrem-stock/nodes__stock_count__to_be_counted_confirm_zero_cannot_be_performed',
                    'The stock count has not started yet: {{number}}. Start the count to update the quantities.',
                    { number },
                ),
            );
        }
        if ((await stockCount.status) === 'closed') {
            throw new BusinessRuleError(
                context.localize(
                    '@sage/xtrem-stock/nodes__stock_count__closed_confirm_zero_cannot_be_performed',
                    'You cannot update the quantities on a stock count that was closed: stock count {{number}}.',
                    { number },
                ),
            );
        }

        let recordsUpdated = 0;
        const stopRequest = await stockCount.lines
            .filter(
                async line => (await line.countedQuantityInStockUnit) === 0 && (await line.status) === 'toBeCounted',
            )
            .some(async line => {
                if (await context.batch.isStopRequested()) {
                    await context.batch.confirmStop();
                    return true;
                }
                await line.$.set({ status: 'counted' });
                recordsUpdated += 1;
                return false;
            });
        if (stopRequest) throw new SystemError('Confirm zero quantities process: Stop requested');

        await stockCount.$.save();

        await context.batch.logMessage(
            'info',
            context.localize(
                '@sage/xtrem-stock/nodes__stock_count__end_confirm_zero',
                'The confirm zero quantities process finished for the stock count: {{number}}. Lines updated: {{recordsUpdated}}.',
                { number, recordsUpdated },
            ),
        );
        logger.verbose(() => `${number} confirmZeroQuantity end. ${recordsUpdated} lines updated`);

        return recordsUpdated;
    }

    @decorators.asyncMutation<typeof StockCount, 'confirmStockCount'>({
        isPublished: true,
        parameters: [
            {
                name: 'stockCount',
                type: 'reference',
                node: () => xtremStock.nodes.StockCount,
                isMandatory: true,
                isWritable: true,
            },
            {
                isMandatory: true,
                name: 'selectedRecords',
                type: 'object',
                properties: {
                    stockRecords: {
                        type: 'array',
                        item: {
                            type: 'integer',
                        },
                    },
                    itemSites: {
                        type: 'array',
                        item: {
                            type: 'integer',
                        },
                    },
                    allSelected: {
                        type: 'boolean',
                        isMandatory: true,
                    },
                },
            },
        ],

        return: 'boolean',
    })
    static async confirmStockCount(
        context: Context,
        stockCount: xtremStock.nodes.StockCount,
        selectedRecords: { allSelected: boolean; stockRecords: Array<number>; itemSites: Array<number> },
    ): Promise<boolean> {
        let stockCounter = 0;
        let itemSiteCounter = 0;
        stockCount.isConfirmingStockCountCreation = true;

        if (
            !selectedRecords.allSelected &&
            selectedRecords.stockRecords.length === 0 &&
            selectedRecords.itemSites.length === 0
        ) {
            throw new BusinessRuleError(
                context.localize(
                    '@sage/xtrem-stock/nodes__stock_count__no_records_selected',
                    'No records selected for stock count {{number}}.',
                    { number: await stockCount.number },
                ),
            );
        }

        await context.batch.logMessage(
            'info',
            context.localize(
                '@sage/xtrem-stock/nodes__stock_count__start_confirm_stock_count',
                'The create stock count lines process has started: {{number}}.',
                { number: await stockCount.number },
            ),
        );
        const stockRecordsToStockCountLine = await xtremStock.functions.stockCountLib.getStockRecordsToStockCountLine(
            context,
            {
                stockCount,
                stockRecords: selectedRecords.stockRecords,
                itemSites: selectedRecords.itemSites,
                allSelected: selectedRecords.allSelected,
            },
        );

        await logger.verboseAsync(
            async () =>
                `_Stock count number=${await stockCount.number} stockRecordsToStockCountLine=${stockRecordsToStockCountLine.length}`,
        );

        await asyncArray(stockRecordsToStockCountLine).forEach(async stockRecord => {
            if (!(await xtremStock.functions.stockCountLib.checkStockCountLineExists(context, stockRecord))) {
                await stockCount.lines.append(
                    await xtremStock.functions.stockCountLib.addLineToStockCount(stockCount, stockRecord),
                );
                stockCounter += 1;
            }
        });

        await context.batch.logMessage(
            'info',
            context.localize(
                '@sage/xtrem-stock/nodes__stock_count__confirm_stock_count_stock_records_to_stock_count_line',
                'Stock count lines created from stock records: {{stockCounter}}.',
                { stockCounter },
            ),
        );

        if (!(await stockCount.hasStockRecords)) {
            const itemSiteToStockCountLine = await xtremStock.functions.stockCountLib.getItemSiteToStockCountLine({
                stockCount,
                stockRecords: selectedRecords.stockRecords,
                itemSites: selectedRecords.itemSites,
                allSelected: selectedRecords.allSelected,
            });

            await asyncArray(itemSiteToStockCountLine).forEach(async itemSite => {
                await stockCount.lines.append(
                    await xtremStock.functions.stockCountLib.addLineWithoutStockToStockCount(await itemSite.item),
                );
                itemSiteCounter += 1;
            });
        }

        await context.batch.logMessage(
            'info',
            context.localize(
                '@sage/xtrem-stock/nodes__stock_count__confirm_stock_count_none_stock_records_to_stock_count_line',
                'Stock count lines created from non-stock records: {{itemSiteCounter}}.',
                { itemSiteCounter },
            ),
        );
        await stockCount.$.set({ status: 'toBeCounted' });
        await stockCount.$.save();

        await context.batch.logMessage(
            'info',
            context.localize(
                '@sage/xtrem-stock/nodes__stock_count__confirm_stock_completed',
                'Stock count lines created: {{number}}.',
                { number: await stockCount.number },
            ),
        );

        return true;
    }

    @decorators.mutation<typeof StockCount, 'resendNotificationForFinance'>({
        isPublished: true,
        parameters: [
            {
                name: 'stockCount',
                type: 'reference',
                isMandatory: true,
                isWritable: true,
                node: () => StockCount,
            },
        ],
        return: {
            type: 'boolean',
        },
    })
    static async resendNotificationForFinance(context: Context, stockCount: StockCount): Promise<boolean> {
        logger.info(
            context.localize(
                '@sage/xtrem-stock/node__stock_count__resend_notification_for_finance',
                'Resending finance notification for stock count: {{stockCount}}',
                { stockCount: await stockCount.number },
            ),
        );

        if (
            await xtremFinanceData.functions.AccountingEngineCommon.validateResendNotificationForFinance(context, {
                documentNumber: await stockCount.number,
                documentType: 'stockCount',
            })
        ) {
            // send notification in order to create a staging table entry for the accounting engine
            if (await (await (await stockCount.stockSite).legalCompany).doStockPosting) {
                await xtremFinanceData.functions.stockCountAdjustmentNotification(
                    context,
                    stockCount as xtremFinanceData.interfaces.FinanceOriginDocument,
                    await stockCount.lines
                        .filter(async (line: xtremStock.nodes.StockCountLine) => (await line.quantityVariance) !== 0)
                        .toArray(),
                );
            }
        }

        return true;
    }

    @decorators.notificationListener<typeof StockCount>({
        topic: 'StockCount/resendNotificationForFinance',
    })
    static async onResendNotificationForFinance(context: Context, document: { number: number }) {
        const stockCount = await context.read(StockCount, { number: document.number });

        await StockCount.resendNotificationForFinance(context, stockCount);
    }
}
