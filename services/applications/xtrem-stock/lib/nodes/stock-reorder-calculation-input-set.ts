import type { Collection, Context, date, NodeCreateData, Reference } from '@sage/xtrem-core';
import { asyncArray, DateValue, decorators, Node, SystemError } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremStockData from '@sage/xtrem-stock-data';
import * as xtremSystem from '@sage/xtrem-system';
import * as xtremStock from '..';

@decorators.node<StockReorderCalculationInputSet>({
    isClearedByReset: true,
    storage: 'sql',
    isPublished: true,
    canRead: true,
    canCreate: true,
    canSearch: true,
    canUpdate: true,
    canDelete: true,
    indexes: [
        {
            orderBy: { user: +1 },
            isUnique: true,
        },
    ],
})
export class StockReorderCalculationInputSet extends Node {
    @decorators.referenceProperty<StockReorderCalculationInputSet, 'user'>({
        isStored: true,
        isPublished: true,
        isFrozen: true,
        node: () => xtremSystem.nodes.User,
        async defaultValue() {
            const userInfo = await this.$.context.user;
            if (!userInfo) {
                throw new SystemError('current context does not have a valid user');
            }
            return this.$.context.read(xtremSystem.nodes.User, { email: userInfo.email });
        },
    })
    readonly user: Reference<xtremSystem.nodes.User>;

    @decorators.referenceProperty<StockReorderCalculationInputSet, 'company'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
        provides: ['company'],
        node: () => xtremSystem.nodes.Company,
    })
    readonly company: Reference<xtremSystem.nodes.Company | null>;

    @decorators.referenceArrayProperty<StockReorderCalculationInputSet, 'sites'>({
        isPublished: true,
        isStored: true,
        lookupAccess: true,
        onDelete: 'restrict',
        node: () => xtremSystem.nodes.Site,
        defaultValue: [],
    })
    readonly sites: Promise<xtremSystem.nodes.Site[]>;

    @decorators.referenceProperty<StockReorderCalculationInputSet, 'fromItem'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
        node: () => xtremMasterData.nodes.Item,
        filters: {
            control: {
                isStockManaged: true,
            },
        },
    })
    readonly fromItem: Reference<xtremMasterData.nodes.Item | null>;

    @decorators.referenceProperty<StockReorderCalculationInputSet, 'toItem'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
        node: () => xtremMasterData.nodes.Item,
        filters: {
            control: {
                isStockManaged: true,
            },
        },
    })
    readonly toItem: Reference<xtremMasterData.nodes.Item | null>;

    @decorators.enumProperty<StockReorderCalculationInputSet, 'reorderType'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
        dataType: () => xtremMasterData.enums.preferredProcessDataType,
    })
    readonly reorderType: Promise<xtremMasterData.enums.PreferredProcess | null>;

    @decorators.dateProperty<StockReorderCalculationInputSet, 'endDate'>({
        isStored: true,
        isPublished: true,
        isRequired: true,
    })
    readonly endDate: Promise<date>;

    @decorators.collectionProperty<StockReorderCalculationInputSet, 'lines'>({
        isPublished: true,
        isVital: true,
        reverseReference: 'inputSet',
        node: () => xtremStock.nodes.StockReorderCalculationResultLine,
    })
    readonly lines: Collection<xtremStock.nodes.StockReorderCalculationResultLine>;

    @decorators.enumProperty<StockReorderCalculationInputSet, 'status'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremStock.enums.stockValuationStatusDataType,
        defaultValue: 'draft',
    })
    readonly status: Promise<xtremStock.enums.StockValuationStatus>;

    static async setReorderCalculationInputSetStatus(
        writableContext: Context,
        args: { userId: string; status: Awaited<StockReorderCalculationInputSet['status']> },
    ) {
        const inputSet = await writableContext.read(
            StockReorderCalculationInputSet,
            { user: args.userId },
            { forUpdate: true },
        );

        // Remove all lines in case of error or in progress.
        if (['inProgress', 'error'].includes(args.status)) {
            await inputSet.$.set({ lines: [] });
        }

        await inputSet.$.set({ status: args.status });
        await inputSet.$.save();
    }

    @decorators.asyncMutation<typeof StockReorderCalculationInputSet, 'reorderCalculation'>({
        isPublished: true,
        startsReadOnly: true,
        parameters: [{ name: 'userId', type: 'string' }],
        return: 'boolean',
    })
    static async reorderCalculation(readonlyContext: Context, userId: string): Promise<boolean> {
        let status: Awaited<StockReorderCalculationInputSet['status']> = 'inProgress';

        await readonlyContext.runInWritableContext(async (writableContext: Context) => {
            await StockReorderCalculationInputSet.setReorderCalculationInputSetStatus(writableContext, {
                userId,
                status,
            });
        });

        try {
            await readonlyContext.runInWritableContext(async (calculationContext: Context) => {
                const inputSet = await calculationContext.read(
                    StockReorderCalculationInputSet,
                    { user: userId },
                    { forUpdate: true },
                );

                // Do calculation.
                const lines = await StockReorderCalculationInputSet.doCalculation(calculationContext, inputSet);
                if (lines.length === 0) {
                    // add error message
                } else {
                    await inputSet.$.set({ lines });
                }

                // Signal completion.
                await inputSet.$.set({ status: 'completed' });
                await inputSet.$.save();
                await readonlyContext.batch.logMessage(
                    'result',
                    readonlyContext.localize(
                        '@sage/xtrem-stock/nodes__stock_reorder_calculation_input_set__notification_calculation_finished',
                        'Stock reorder calculation finished.',
                    ),
                    { data: inputSet.$.context.diagnoses },
                );

                status = 'completed';
            });
        } finally {
            // If an error occurred, the status has not been set to completed
            // Then the error in the calculationContext is logged and the status is set to error
            if (status !== ('completed' as Awaited<StockReorderCalculationInputSet['status']>)) {
                status = 'error';
                await readonlyContext.batch.logMessage(
                    'error',
                    readonlyContext.localize(
                        '@sage/xtrem-stock/nodes__stock_reorder_calculation_input_set__notification_calculation_failed',
                        'Stock reorder calculation failed.',
                    ),
                );
            }

            await readonlyContext.runInWritableContext(async writableContext => {
                await StockReorderCalculationInputSet.setReorderCalculationInputSetStatus(writableContext, {
                    userId,
                    status,
                });
            });
        }

        return true;
    }

    static async search(
        context: Context,
        searchCriteria: xtremMasterData.interfaces.ValuedItemSitesSearch,
    ): Promise<xtremStockData.interfaces.StockReorderRecord[]> {
        if (!searchCriteria.preferredProcesses) {
            delete searchCriteria.preferredProcesses;
        }
        if (!searchCriteria.replenishmentMethods) {
            delete searchCriteria.replenishmentMethods;
        }

        const itemSites = await xtremMasterData.nodes.ItemSite.getValuedItemSite(context, searchCriteria);

        return asyncArray(itemSites)
            .map(async (itemSite: xtremMasterData.nodes.ItemSite) => {
                return {
                    item: await itemSite.item,
                    site: await itemSite.site,
                };
            })
            .toArray();
    }

    static async doCalculation(
        context: Context,
        inputSet: StockReorderCalculationInputSet,
    ): Promise<NodeCreateData<xtremStock.nodes.StockReorderCalculationResultLine>[]> {
        const endDate = await inputSet.endDate;
        const company = await inputSet.company;
        const sites = await asyncArray(await inputSet.sites)
            .map((site: xtremSystem.nodes.Site) => site.id)
            .toArray();
        const orderType = await inputSet.reorderType;

        // Get item-sites to be processed.
        const searchCriteria: xtremMasterData.interfaces.ValuedItemSitesSearch = {
            company: company ? company._id : undefined,
            stockSiteList: sites,
            itemRange: {
                start: (await (await inputSet.fromItem)?.id) ?? undefined,
                end: (await (await inputSet.toItem)?.id) ?? undefined,
            },
            preferredProcesses: orderType ? [orderType] : undefined,
            replenishmentMethods: ['byReorderPoint'],
        };

        const itemDetails = await StockReorderCalculationInputSet.search(context, searchCriteria);
        const numberOfDays = endDate ? endDate.daysDiff(DateValue.today()) : 90;

        // Get reorder recommendations.
        const reorderRecommendations = await xtremStockData.nodeExtensions.ItemSiteExtension.getReorderRecommendations(
            context,
            itemDetails,
            numberOfDays,
        );

        return reorderRecommendations
            ? asyncArray(reorderRecommendations)
                  .map(async recommendation => {
                      const orderDate = new Date(recommendation.day.toString());
                      const item = await recommendation.itemSite.item;
                      const site = await recommendation.itemSite.site;
                      return {
                          ...recommendation,
                          itemSite: recommendation.itemSite,
                          supplier: (await recommendation.itemSite.defaultSupplier)?._id,
                          company: (await site.legalCompany)._id,
                          stockUnit: (await item.stockUnit)._id,
                          purchaseUnit: (await item.purchaseUnit)?._id,
                          reorderType: await recommendation.itemSite.preferredProcess,
                          orderDate: DateValue.parse(orderDate.toISOString().substring(0, 10)),
                          currency: (await site.currency)._id,
                          quantity: recommendation.recommendationQuantity ?? 0,
                          _action: 'create',
                      } as NodeCreateData<xtremStock.nodes.StockReorderCalculationResultLine>;
                  })
                  .toArray()
            : [];
    }

    // This function extends the same named function in item-sites.ts by a filter for
    // a posting class for the original function from  xtrem-master-data cannot access the
    // posting class definition defined in xtrem-finance-data.
    static getValuedItemSite(
        context: Context,
        searchCriteria: xtremMasterData.interfaces.ValuedItemSitesSearch,
    ): Promise<Array<xtremMasterData.nodes.ItemSite>> {
        // Create the query filter from search criteria.
        const options = xtremMasterData.nodes.ItemSite.getValuedItemSiteFilter(searchCriteria);

        // Search for the item-site records according to the previously built options
        return context.query(xtremMasterData.nodes.ItemSite, options).toArray();
    }
}
