import type { Collection, Context, date, NodeCreateData, Reference } from '@sage/xtrem-core';
import { asyncArray, decorators, Node, SystemError } from '@sage/xtrem-core';
import * as xtremFinanceData from '@sage/xtrem-finance-data';
import * as xtremMasterData from '@sage/xtrem-master-data';
import type { integer } from '@sage/xtrem-shared';
import * as xtremSystem from '@sage/xtrem-system';
import * as xtremStock from '..';

interface ValuedItemSitesSearch extends xtremMasterData.interfaces.ValuedItemSitesSearch {
    postingClass?: integer;
}

@decorators.node<StockValuationInputSet>({
    isClearedByReset: true,
    storage: 'sql',
    isPublished: true,
    canRead: true,
    canCreate: true,
    canSearch: true,
    canUpdate: true,
    canDelete: true,
    indexes: [
        {
            orderBy: { user: +1 },
            isUnique: true,
        },
    ],
})
export class StockValuationInputSet extends Node {
    @decorators.referenceProperty<StockValuationInputSet, 'user'>({
        isStored: true,
        isPublished: true,
        isFrozen: true,
        node: () => xtremSystem.nodes.User,
        async defaultValue() {
            const userInfo = await this.$.context.user;
            if (!userInfo) {
                throw new SystemError('current context does not have a valid user');
            }
            return this.$.context.read(xtremSystem.nodes.User, { email: userInfo.email });
        },
    })
    readonly user: Reference<xtremSystem.nodes.User>;

    @decorators.referenceProperty<StockValuationInputSet, 'company'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
        provides: ['company'],
        node: () => xtremSystem.nodes.Company,
    })
    readonly company: Reference<xtremSystem.nodes.Company | null>;

    @decorators.collectionProperty<StockValuationInputSet, 'sites'>({
        isPublished: true,
        isVital: true,
        reverseReference: 'inputSet',
        node: () => xtremStock.nodes.StockValuationInputSetToSite,
    })
    readonly sites: Collection<xtremStock.nodes.StockValuationInputSetToSite>;

    @decorators.referenceProperty<StockValuationInputSet, 'fromItem'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
        ignoreIsActive: true,
        node: () => xtremMasterData.nodes.Item,
    })
    readonly fromItem: Reference<xtremMasterData.nodes.Item | null>;

    @decorators.referenceProperty<StockValuationInputSet, 'toItem'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
        ignoreIsActive: true,
        node: () => xtremMasterData.nodes.Item,
    })
    readonly toItem: Reference<xtremMasterData.nodes.Item | null>;

    @decorators.referenceProperty<StockValuationInputSet, 'itemCategory'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
        node: () => xtremMasterData.nodes.ItemCategory,
    })
    readonly itemCategory: Reference<xtremMasterData.nodes.ItemCategory> | null;

    @decorators.stringProperty<StockValuationInputSet, 'commodityCode'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremSystem.dataTypes.code,
    })
    readonly commodityCode: Promise<string>;

    @decorators.enumProperty<StockValuationInputSet, 'valuationMethod'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
        dataType: () => xtremMasterData.enums.costValuationMethodDataType,
    })
    readonly valuationMethod: Promise<xtremMasterData.enums.CostValuationMethod | null>;

    @decorators.referenceProperty<StockValuationInputSet, 'postingClass'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
        node: () => xtremFinanceData.nodes.PostingClass,
        filters: {
            control: {
                type: 'item',
                isDetailed: true,
            },
        },
    })
    readonly postingClass: Reference<xtremFinanceData.nodes.PostingClass | null>;

    @decorators.booleanProperty<StockValuationInputSet, 'displayZeroValues'>({
        isPublished: true,
        isStored: true,
        defaultValue: true,
    })
    readonly displayZeroValues: Promise<boolean>;

    @decorators.dateProperty<StockValuationInputSet, 'date'>({
        isStored: true,
        isPublished: true,
        isRequired: true,
    })
    readonly date: Promise<date>;

    @decorators.enumProperty<StockValuationInputSet, 'status'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremStock.enums.stockValuationStatusDataType,
        defaultValue: 'draft',
    })
    readonly status: Promise<xtremStock.enums.StockValuationStatus>;

    @decorators.collectionProperty<StockValuationInputSet, 'lines'>({
        isPublished: true,
        isVital: true,
        reverseReference: 'inputSet',
        node: () => xtremStock.nodes.StockValuationResultLine,
    })
    readonly lines: Collection<xtremStock.nodes.StockValuationResultLine>;

    static async setStockValuationInputSetStatus(
        writableContext: Context,
        args: { userId: string; status: Awaited<StockValuationInputSet['status']> },
    ) {
        const inputSet = await writableContext.read(StockValuationInputSet, { user: args.userId }, { forUpdate: true });

        // Remove all lines in case of error or in progress.
        await inputSet.$.set({
            ...(['inProgress', 'error'].includes(args.status) ? { lines: [] } : {}),
            status: args.status,
        });
        await inputSet.$.save();
    }

    @decorators.asyncMutation<typeof StockValuationInputSet, 'stockValuation'>({
        isPublished: true,
        startsReadOnly: true,
        parameters: [{ name: 'userId', type: 'string' }],
        return: 'boolean',
    })
    static async stockValuation(readonlyContext: Context, userId: string): Promise<boolean> {
        let status: Awaited<StockValuationInputSet['status']> = 'inProgress';

        await readonlyContext.runInWritableContext(async writableContext => {
            await StockValuationInputSet.setStockValuationInputSetStatus(writableContext, { userId, status });
        });

        try {
            await readonlyContext.runInWritableContext(async calculationContext => {
                const inputSet = await calculationContext.read(
                    StockValuationInputSet,
                    { user: userId },
                    { forUpdate: true },
                );

                // Do calculation.
                await StockValuationInputSet.doValuation(calculationContext, inputSet);

                // Signal completion.
                await inputSet.$.set({ status: 'completed' });
                await inputSet.$.save();
                await readonlyContext.batch.logMessage(
                    'result',
                    readonlyContext.localize(
                        '@sage/xtrem-stock/nodes__stock_valuation_input_set__notification_valuation_finished',
                        'Stock valuation finished.',
                    ),
                    { data: inputSet.$.context.diagnoses },
                );

                status = 'completed';
            });
        } finally {
            // If an error occurred, the status has not been set to completed
            // Then the error in the calculationContext is logged and the status is set to error
            if (status !== ('completed' as Awaited<StockValuationInputSet['status']>)) {
                status = 'error';
                await readonlyContext.batch.logMessage(
                    'error',
                    readonlyContext.localize(
                        '@sage/xtrem-stock/nodes__stock_valuation_input_set__notification_valuation_failed',
                        'Stock valuation failed.',
                    ),
                );
            }

            await readonlyContext.runInWritableContext(async writableContext => {
                await StockValuationInputSet.setStockValuationInputSetStatus(writableContext, {
                    userId,
                    status,
                });
            });
        }

        return true;
    }

    static async doValuation(context: Context, inputSet: StockValuationInputSet) {
        const valuationDate = await inputSet.date;
        const company = await inputSet.company;
        const sites = await inputSet.sites.map(async inputSetToSite => (await inputSetToSite.site).id).toArray();
        const valuationMethod = await inputSet.valuationMethod;
        const itemCategory = await inputSet.itemCategory;
        const postingClass = await inputSet.postingClass;
        const displayZeroValues = await inputSet.displayZeroValues;

        // Get item-sites to be processed.
        const searchCriteria: ValuedItemSitesSearch = {
            company: company ? company._id : undefined,
            stockSiteList: sites.length ? sites : undefined,
            itemRange: {
                start: (await (await inputSet.fromItem)?.id) ?? undefined,
                end: (await (await inputSet.toItem)?.id) ?? undefined,
            },
            valuationMethods: valuationMethod ? [valuationMethod] : undefined,
            itemCategory: itemCategory ? itemCategory._id : undefined,
            commodityCode: await inputSet.commodityCode,
            postingClass: postingClass ? postingClass._id : undefined,
        };
        const itemSites = asyncArray(await StockValuationInputSet.getValuedItemSite(context, searchCriteria));

        // Stock valuation.
        const results = itemSites.map(async itemSite => {
            const result: xtremStock.interfaces.StockValuationResult =
                await xtremStock.nodeExtensions.ItemSiteExtension.getStockValuation(context, {
                    itemSite: itemSite._id,
                    dateOfValuation: valuationDate,
                });

            const item = await itemSite.item;
            const site = await itemSite.site;

            return {
                ...result,
                quantity: result.quantityInStock,
                item,
                site,
                company: await site.legalCompany,
                postingClass: item.postingClass,
                stockUnit: item.stockUnit,
                currency: (await site.legalCompany).currency,
                valuationDate,
                costType: await itemSite.valuationMethod,
                itemCategory: await item.category,
                commodityCode: await item.commodityCode,
            } as NodeCreateData<xtremStock.nodes.StockValuationResultLine>;
        });

        await results
            .filter(line => line !== null && (displayZeroValues || line.quantity !== 0 || line.stockValue !== 0))
            .forEach(async line => {
                await inputSet.lines.append(line);
            });
    }

    // This function extends the same named function in item-sites.ts by a filter for
    // a posting class for the original function from  xtrem-master-data cannot access the
    // posting class definition defined in xtrem-finance-data.
    static getValuedItemSite(
        context: Context,
        searchCriteria: ValuedItemSitesSearch,
    ): Promise<Array<xtremMasterData.nodes.ItemSite>> {
        // Create the query filter from search criteria.
        const options = xtremMasterData.nodes.ItemSite.getValuedItemSiteFilter(searchCriteria);

        // Add a possible posting class filter.
        if (searchCriteria.postingClass)
            options.filter._and.push({
                item: { postingClass: searchCriteria.postingClass },
            });

        // Search for the item-site records according to the previously built options
        return context.query(xtremMasterData.nodes.ItemSite, { ...options }).toArray();
    }
}
