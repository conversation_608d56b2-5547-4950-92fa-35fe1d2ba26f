import type { Collection, decimal, integer, NodeUpdateData, Reference, ValidationContext } from '@sage/xtrem-core';
import { BusinessRuleError, decorators, useDefaultValue } from '@sage/xtrem-core';
import * as xtremFinanceData from '@sage/xtrem-finance-data';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremStockData from '@sage/xtrem-stock-data';
import * as xtremSystem from '@sage/xtrem-system';
import * as xtremStock from '..';

async function getStockValue(correctedDocumentLine: xtremStockData.interfaces.StockDocumentLine): Promise<decimal> {
    return (await correctedDocumentLine.getOrderCost()) || 0;
}

@decorators.subNode<StockValueCorrectionLine>({
    isPublished: true,
    canRead: true,
    canSearch: true,
    canCreate: true,
    canUpdate: true,
    isVitalCollectionChild: true,
    extends: () => xtremMasterData.nodes.BaseDocumentLine,
})
@xtremStockData.functions.staticImplements<xtremStockData.interfaces.StaticDocumentLineWithStockPosting>()
export class StockValueCorrectionLine
    extends xtremMasterData.nodes.BaseDocumentLine
    implements
        xtremFinanceData.interfaces.FinanceOriginDocumentLine,
        xtremStockData.interfaces.AnyStockDocumentLineWithoutStockDetail
{
    getOrderCost(): Promise<decimal> {
        return this.orderCost;
    }

    getValuedCost(): Promise<decimal> {
        return this.valuedCost;
    }

    // eslint-disable-next-line class-methods-use-this
    getValuationParameters(): xtremStockData.interfaces.ValuationParameter {
        // only receipts can be corrected
        return { valuationType: 'receipt' };
    }

    getItem(): Promise<xtremMasterData.nodes.Item> {
        return this.item;
    }

    quantityInStockUnit: decimal = 0;

    @decorators.referenceProperty<StockValueCorrectionLine, 'document'>({
        isStored: true,
        isPublished: true,
        isVitalParent: true,
        lookupAccess: true,
        node: () => xtremStock.nodes.StockValueCorrection,
    })
    override readonly document: Reference<xtremStock.nodes.StockValueCorrection>;

    @decorators.stringPropertyOverride<StockValueCorrectionLine, 'documentNumber'>({
        async getValue() {
            return (await this.document).number;
        },
    })
    override readonly documentNumber: Promise<string>;

    @decorators.integerPropertyOverride<StockValueCorrectionLine, 'documentId'>({
        async getValue() {
            return (await this.document)._id;
        },
    })
    override readonly documentId: Promise<integer>;

    @decorators.enumProperty<StockValueCorrectionLine, 'stockTransactionStatus'>({
        isStored: true,
        isPublished: true,
        defaultValue() {
            return 'draft';
        },
        dataType: () => xtremStockData.enums.StockDocumentTransactionStatusDataType,
        lookupAccess: true,
    })
    readonly stockTransactionStatus: Promise<xtremStockData.enums.StockDocumentTransactionStatus>;

    @decorators.referenceProperty<StockValueCorrectionLine, 'item'>({
        isStored: true,
        isPublished: true,
        filters: {
            control: {
                type: 'good',
                isStockManaged: true,
                status: 'active',
            },
        },
        node: () => xtremMasterData.nodes.Item,
        async control(cx: ValidationContext) {
            const reasonCode = await (await this.document).reasonCode;
            if (
                (await this.itemSite) &&
                (await (await this.itemSite).valuationMethod) !== 'averageCost' &&
                reasonCode &&
                (await reasonCode.stockValueCorrection)
            ) {
                cx.error.addLocalized(
                    '@sage/xtrem-stock/nodes__stock_adjustment_line__incorrect_valuation_method',
                    'The {{item}} item must be linked to an average cost valuation method.',
                    { item: await (await this.item).name },
                );
            }
        },
        lookupAccess: true,
    })
    readonly item: Reference<xtremMasterData.nodes.Item>;

    @decorators.decimalProperty<StockValueCorrectionLine, 'unitCost'>({
        isStored: true,
        isPublished: true,
        dependsOn: ['itemSite'],
        dataType: () => xtremMasterData.dataTypes.costDataType,
        async defaultValue() {
            return (await this.itemSite).currentCost;
        },
        lookupAccess: true,
    })
    readonly unitCost: Promise<decimal>;

    // [RM] For the finance interface
    @decorators.decimalProperty<StockValueCorrectionLine, 'orderCost'>({
        isPublished: true,
        dependsOn: ['correctedDocumentLine'],
        dataType: () => xtremMasterData.dataTypes.costDataType,
        async computeValue() {
            return getStockValue((await this.correctedDocumentLine) as xtremStockData.interfaces.StockDocumentLine);
        },
        lookupAccess: true,
    })
    readonly orderCost: Promise<decimal>;

    //  [RM] For the finance interface
    @decorators.decimalProperty<StockValueCorrectionLine, 'valuedCost'>({
        dependsOn: ['correctedDocumentLine'],
        dataType: () => xtremMasterData.dataTypes.costDataType,
        async computeValue() {
            return getStockValue((await this.correctedDocumentLine) as xtremStockData.interfaces.StockDocumentLine);
        },
    })
    readonly valuedCost: Promise<decimal>;

    /**
     * Value expressed as a delta
     */
    @decorators.decimalProperty<StockValueCorrectionLine, 'newUnitCost'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.costDataType,
        lookupAccess: true,
    })
    readonly newUnitCost: Promise<decimal>;

    @decorators.collectionProperty<StockValueCorrectionLine, 'stockDetails'>({
        isPublished: true,
        isVital: true,
        reverseReference: 'documentLine',
        node: () => xtremStockData.nodes.StockCorrectionDetail,
        lookupAccess: true,
    })
    readonly stockDetails: Collection<xtremStockData.nodes.StockCorrectionDetail>;

    @decorators.collectionProperty<StockValueCorrectionLine, 'stockTransactions'>({
        isPublished: true,
        reverseReference: 'documentLine',
        isVital: true,
        node: () => xtremStockData.nodes.StockTransaction,
        lookupAccess: true,
    })
    readonly stockTransactions: Collection<xtremStockData.nodes.StockTransaction>;

    @decorators.collectionProperty<StockValueCorrectionLine, 'stockMovements'>({
        isPublished: true,
        reverseReference: 'documentLine',
        node: () => xtremStockData.nodes.StockJournal,
        lookupAccess: true,
    })
    readonly stockMovements: Collection<xtremStockData.nodes.StockJournal>;

    @decorators.referenceProperty<StockValueCorrectionLine, 'itemSite'>({
        isPublished: true,
        node: () => xtremMasterData.nodes.ItemSite,
        join: {
            item() {
                return this.item;
            },
            async site() {
                return (await this.document).stockSite;
            },
        },
        lookupAccess: true,
    })
    readonly itemSite: Reference<xtremMasterData.nodes.ItemSite>;

    // the site property on line level is needed for the accounting interface
    @decorators.referenceProperty<StockValueCorrectionLine, 'site'>({
        isPublished: true,
        node: () => xtremSystem.nodes.Site,
        async getValue() {
            return (await this.document).stockSite;
        },
        lookupAccess: true,
    })
    readonly site: Reference<xtremSystem.nodes.Site>;

    @decorators.enumProperty<StockValueCorrectionLine, 'correctedDocumentType'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremStock.enums.AdjustableDocumentTypeDataType,
        lookupAccess: true,
    })
    readonly correctedDocumentType: Promise<xtremStock.enums.AdjustableDocumentType>;

    @decorators.referenceProperty<StockValueCorrectionLine, 'correctedDocumentLine'>({
        isStored: true,
        isPublished: true,
        dependsOn: ['correctedDocumentType', 'stockDetails'],
        isNullable: true,
        node: () => xtremMasterData.nodes.BaseDocumentLine,
        async prepare() {
            if (xtremMasterData.functions.nodeIsBeingModified(this) || (await this.stockDetails.length)) {
                return;
            }

            if (await this.correctedDocumentLine) {
                if (
                    (await ((await this.correctedDocumentLine) as xtremStockData.interfaces.StockDocumentLine)
                        .stockTransactionStatus) !== 'completed'
                ) {
                    throw new BusinessRuleError(
                        this.$.context.localize(
                            '@sage/xtrem-stock/nodes__stock_value_correction_line__invalid_document_line',
                            'The corrected document line stock transaction status must be completed.',
                        ),
                    );
                }
                const costDelta = (await this.newUnitCost) - (await this.orderCost);

                const stockDetails = await (
                    (await this.correctedDocumentLine) as xtremStockData.interfaces.StockDocumentLine
                ).stockDetails
                    .map(async (detail: xtremStockData.nodes.BaseStockDetail) => {
                        const _sourceId = await detail._sourceId;
                        const _sortValue = await detail._sortValue;

                        const stockCorrectionDetailData: NodeUpdateData<xtremStockData.nodes.StockCorrectionDetail> = {
                            // TODO: review this - we are casting a node to a
                            ...(await detail.$.payload({ withIds: true, inputValuesOnly: true })),
                            _sortValue,
                            _sourceId,
                            // this is to ensure we don't reuse the _id of the oldDetail
                            _id: undefined,
                            reasonCode: await (await this.document).reasonCode,
                            correctedStockDetail: detail as xtremStockData.nodes.StockMovementDetail,
                            originDocumentLine: await this.correctedDocumentLine,
                            orderCost: costDelta,
                            valuedCost: costDelta,
                            _action: 'create',
                            quantityInStockUnit: 0,
                            // typing is not happy without this
                            // stockMovements: undefined,
                            // stockDetailSerialNumbers: [],
                        };

                        return stockCorrectionDetailData;
                    })
                    .toArray();

                await this.$.set({
                    stockDetails,
                });
            } else {
                throw new BusinessRuleError(
                    this.$.context.localize(
                        '@sage/xtrem-stock/nodes__stock_value_correction_line__no_corrected_line',
                        'You must provide the document line that is being corrected.',
                    ),
                );
            }
        },
        lookupAccess: true,
    })
    readonly correctedDocumentLine: Reference<xtremMasterData.nodes.BaseDocumentLine | null>;

    @decorators.jsonProperty<StockValueCorrectionLine, 'storedDimensions'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
        dataType: () => xtremFinanceData.dataTypes.storedDimensionsDataType,
        lookupAccess: true,
        dependsOn: ['item', { document: ['stockSite'] }],
        async defaultValue() {
            const site = await (await this.document).stockSite;
            return xtremFinanceData.functions.getDefaultDimensions(this.$.context, {
                dimensionDefinitionLevel: 'stockDirect',
                companyId: (await (await (await this.document).stockSite).legalCompany)._id,
                site,
                item: await this.item,
            });
        },
    })
    readonly storedDimensions: Promise<object | null>;

    @decorators.referenceProperty<StockValueCorrectionLine, 'analyticalData'>({
        isStored: true,
        isMutable: true,
        isPublished: true,
        lookupAccess: true,
        ignoreIsActive: true,
        isFrozen() {
            return xtremFinanceData.functions.isAnalyticalDataFrozen(this);
        },
        node: () => xtremFinanceData.nodes.AnalyticalData,
        dependsOn: ['storedAttributes', 'storedDimensions'],
        async defaultValue() {
            return (await xtremFinanceData.functions.getAnalyticalDataFromStoredAttributesAndDimensions(
                this.$.context,
                await this.storedAttributes,
                await this.storedDimensions,
            )) as any;
        },
        updatedValue: useDefaultValue,
    })
    readonly analyticalData: Reference<xtremFinanceData.nodes.AnalyticalData>;

    @decorators.jsonProperty<StockValueCorrectionLine, 'storedAttributes'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
        dataType: () => xtremFinanceData.dataTypes.storedAttributesDataType,
        lookupAccess: true,
        dependsOn: ['item', { document: ['stockSite'] }],
        async defaultValue() {
            const site = await (await this.document).stockSite;
            return xtremFinanceData.functions.getDefaultAttributes(this.$.context, {
                dimensionDefinitionLevel: 'stockDirect',
                companyId: (await (await (await this.document).stockSite).legalCompany)._id,
                site,
                item: await this.item,
            });
        },
    })
    readonly storedAttributes: Promise<xtremMasterData.interfaces.StoredAttributes | null>;

    @decorators.jsonProperty<StockValueCorrectionLine, 'computedAttributes'>({
        isPublished: true,
        dependsOn: ['item', { document: ['stockSite'] }],
        async computeValue() {
            return xtremStock.functions.analytical.computeAttributes(this.$.context, {
                item: await this.item,
                stockSite: await (await this.document).stockSite,
            });
        },
        lookupAccess: true,
    })
    readonly computedAttributes: Promise<object>;
}
