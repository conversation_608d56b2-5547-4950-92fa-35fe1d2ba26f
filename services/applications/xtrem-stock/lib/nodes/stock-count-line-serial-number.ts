import type { Context, decimal, integer, Reference } from '@sage/xtrem-core';
import { BusinessRuleError, decorators, Node } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremStockData from '@sage/xtrem-stock-data';
import * as xtremStock from '..';

@decorators.node<StockCountLineSerialNumber>({
    serviceOptions: () => [xtremMasterData.serviceOptions.serialNumberOption],
    storage: 'sql',
    isPublished: true,
    isVitalCollectionChild: true,
    canRead: true,
    canSearch: true,
    isCustomizable: true,
    indexes: [
        {
            orderBy: {
                stockCountLine: +1,
                serialNumber: +1,
            },
            isUnique: true,
        },
    ],
    async controlDelete(cx, isCascading) {
        if (!isCascading || (await this.stockCountLine).$.status === 'modified') {
            const serialNumber = await this.serialNumber;
            const stockCountLine = await this.stockCountLine;
            // check the serial number is still in stock record
            if ((await (await stockCountLine.document).status) !== 'toBeCounted') {
                cx.error.addLocalized(
                    '@sage/xtrem-stock/nodes__stock_count_line_serial_number__cannot_delete_count_started',
                    'A stock count line serial number cannot be deleted after the count has started. Item {{itemId}}, serial number {{serialNumber}}.',
                    {
                        itemId: await (await stockCountLine.item).id,
                        serialNumber: await serialNumber.id,
                    },
                );
            } else if ((await serialNumber.stockRecord)?._id === (await stockCountLine.stockRecord)?._id) {
                cx.error.addLocalized(
                    '@sage/xtrem-stock/nodes__stock_count_line_serial_number__cannot_delete_serial_still_in_stock',
                    'A stock count line serial number cannot be deleted when it is associated to the counted stock record. Item {{itemId}}, serial number {{serialNumber}}.',
                    {
                        itemId: await (await stockCountLine.item).id,
                        serialNumber: await serialNumber.id,
                    },
                );
            }
        }
    },
})
export class StockCountLineSerialNumber extends Node {
    @decorators.referenceProperty<StockCountLineSerialNumber, 'stockCountLine'>({
        isStored: true,
        isPublished: true,
        isVitalParent: true,
        isRequired: true,
        lookupAccess: true,
        node: () => xtremStock.nodes.StockCountLine,
    })
    readonly stockCountLine: Reference<xtremStock.nodes.StockCountLine>;

    @decorators.referenceProperty<StockCountLineSerialNumber, 'serialNumber'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        node: () => xtremStockData.nodes.SerialNumber,
        async control(cx, val) {
            const stockCountLine = await this.stockCountLine;
            if ((await stockCountLine.stockTransactionStatus) === 'completed') {
                return;
            }
            const stockRecord = await val.stockRecord;
            if (!stockRecord) {
                cx.error.addLocalized(
                    '@sage/xtrem-stock/nodes__stock_count_line_serial_number__serial_number_not_assigned_to_a_stock_record',
                    'The serial number {{serialNumber}} is not assigned to a stock line.',
                    { serialNumber: await val.id },
                );
                return;
            }

            let lot = await stockCountLine.lot;
            if (!lot && (await stockCountLine.stockDetails.length)) {
                lot = (await (await (await stockCountLine.stockDetails.at(0))?.stockDetailLot)?.lot) || null;
            }
            if (
                (await stockCountLine.site)._id !== (await stockRecord?.site)?._id ||
                (await stockCountLine.item)._id !== (await stockRecord?.item)?._id ||
                (await stockCountLine.stockStatus)?._id !== (await stockRecord?.status)?._id ||
                lot?._id !== (await stockRecord?.lot)?._id ||
                (await stockCountLine.location)?._id !== (await stockRecord?.location)?._id ||
                (await stockCountLine.owner) !== (await stockRecord?.owner)
            ) {
                cx.error.addLocalized(
                    '@sage/xtrem-stock/nodes__stock_count_line_serial_number__stock_count_line_and_serial_number_dont_match',
                    'The stock count line and the serial number {{serialNumber}} do not correspond to the same stock line.',
                    { serialNumber: await (await this.serialNumber).id },
                );
            }
        },
    })
    readonly serialNumber: Reference<xtremStockData.nodes.SerialNumber>;

    @decorators.booleanProperty<StockCountLineSerialNumber, 'isCounted'>({
        isStored: true,
        isPublished: true,
        defaultValue: false,
        lookupAccess: true,
    })
    readonly isCounted: Promise<boolean>;

    @decorators.query<typeof StockCountLineSerialNumber, 'getEndingSerialNumber'>({
        isPublished: true,
        parameters: [
            { name: 'stockCountLineId', type: 'integer' },
            { name: 'startingSerialNumber', type: 'string' },
            { name: 'quantity', type: 'decimal' },
        ],
        return: {
            type: 'string',
        },
    })
    static async getEndingSerialNumber(
        context: Context,
        stockCountLineId: integer,
        startingSerialNumber: string,
        quantity: decimal,
    ): Promise<string> {
        // Prevent a quantity of zero
        if (quantity <= 0)
            throw new BusinessRuleError(
                context.localize(
                    '@sage/xtrem-stock/nodes__stock_count_line_serial_number__zero_quantity_error',
                    'Enter a quantity greater than 0.',
                ),
            );
        const endingSerialNumber = xtremStockData.sharedFunctions.serialNumberHelper.getEndingSerialNumberId(
            startingSerialNumber,
            quantity,
        );
        if (!endingSerialNumber || (endingSerialNumber === startingSerialNumber && quantity > 1))
            throw new BusinessRuleError(
                context.localize(
                    '@sage/xtrem-stock/nodes__stock_count_line_serial_number__invalid_quantity_error',
                    'The ending serial number cannot be calculated. Check the starting serial number and quantity.',
                ),
            );

        const serialNumberRanges = await xtremStock.functions.stockCountLineSerialNumberLib.getRanges(context, {
            stockCountLineId,
            startingSerialNumber,
            endingSerialNumber,
        });

        // if not all serial numbers of range are available -> error
        if (serialNumberRanges[0].quantity === quantity) {
            return endingSerialNumber;
        }

        throw new BusinessRuleError(
            context.localize(
                '@sage/xtrem-stock/nodes__stock_count_line_serial_number__available_error',
                'Serial numbers available in this range: {{available}}.',
                { available: serialNumberRanges[0].quantity },
            ),
        );
    }
}
