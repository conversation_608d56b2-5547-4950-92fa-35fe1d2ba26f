import type { Collection, Context, Reference, decimal } from '@sage/xtrem-core';
import { Node, SystemError, date, decorators } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremSystem from '@sage/xtrem-system';
import * as xtremStock from '..';

@decorators.node<CostRollUpInputSet>({
    storage: 'sql',
    isPublished: true,
    canCreate: true,
    canRead: true,
    canUpdate: true,
    canDelete: true,
    canSearch: true,
    indexes: [
        {
            orderBy: { user: 1 },
            isUnique: true,
        },
    ],
})
export class CostRollUpInputSet extends Node {
    @decorators.referenceProperty<CostRollUpInputSet, 'user'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        node: () => xtremSystem.nodes.User,
        async defaultValue() {
            const userInfo = await this.$.context.user;
            if (!userInfo) {
                throw new SystemError('current context does not have a valid user');
            }
            return this.$.context.read(xtremSystem.nodes.User, { email: userInfo.email });
        },
    })
    readonly user: Reference<xtremSystem.nodes.User>;

    @decorators.referenceProperty<CostRollUpInputSet, 'site'>({
        isPublished: true,
        isStored: true,
        isRequired: true,
        lookupAccess: true,
        node: () => xtremSystem.nodes.Site,
        provides: ['site'],
    })
    readonly site: Reference<xtremSystem.nodes.Site>;

    @decorators.referenceArrayProperty<CostRollUpInputSet, 'items'>({
        isPublished: true,
        isStored: true,
        dependsOn: ['site'],
        filters: {
            control: {
                isManufactured: true,
                itemSites: {
                    _atLeast: 1,
                    valuationMethod: 'standardCost',
                    // we need a non computed value to filter of BOM
                    // billOfMaterial: {
                    //     _ne: null,
                    // },
                    site() {
                        return this.site;
                    },
                },
            },
        },
        lookupAccess: true,
        onDelete: 'restrict',
        node: () => xtremMasterData.nodes.Item,
        defaultValue: [],
    })
    readonly items: Promise<xtremMasterData.nodes.Item[]>;

    @decorators.referenceProperty<CostRollUpInputSet, 'itemCategory'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
        lookupAccess: true,
        node: () => xtremMasterData.nodes.ItemCategory,
    })
    readonly itemCategory: Reference<xtremMasterData.nodes.ItemCategory | null>;

    @decorators.stringProperty<CostRollUpInputSet, 'commodityCode'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremSystem.dataTypes.code,
        lookupAccess: true,
    })
    readonly commodityCode: Promise<string>;

    @decorators.referenceProperty<CostRollUpInputSet, 'costCategory'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        node: () => xtremMasterData.nodes.CostCategory,
        defaultValue() {
            return this.$.context.read(xtremMasterData.nodes.CostCategory, { id: 'Standard' });
        },
    })
    readonly costCategory: Reference<xtremMasterData.nodes.CostCategory>;

    @decorators.dateProperty<CostRollUpInputSet, 'fromDate'>({
        isStored: true,
        isPublished: true,
        async control(cx, val) {
            await cx.error.if(val).is.before(date.today());
        },
        defaultValue() {
            return date.today();
        },
        lookupAccess: true,
    })
    readonly fromDate: Promise<date>;

    @decorators.decimalProperty<CostRollUpInputSet, 'quantity'>({
        isStored: true,
        isPublished: true,
        isNotZero: true,
        dataType: () => xtremMasterData.dataTypes.quantityInStockUnit,
        async control(cx, val) {
            await cx.error.if(val).is.negative();
        },
        defaultValue: 1,
        lookupAccess: true,
    })
    readonly quantity: Promise<decimal>;

    @decorators.booleanProperty<CostRollUpInputSet, 'includesRouting'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
    })
    readonly includesRouting: Promise<boolean>;

    @decorators.booleanProperty<CostRollUpInputSet, 'usesComponentStandardCost'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
    })
    readonly usesComponentStandardCost: Promise<boolean>;

    @decorators.enumProperty<CostRollUpInputSet, 'status'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremStock.enums.standardCostRollUpStatusDataType,
        defaultValue: 'draft',
        lookupAccess: true,
    })
    readonly status: Promise<xtremStock.enums.StandardCostRollUpStatus>;

    @decorators.collectionProperty<CostRollUpInputSet, 'resultLines'>({
        isPublished: true,
        isVital: true,
        reverseReference: 'inputSet',
        node: () => xtremStock.nodes.CostRollUpResultLine,
        lookupAccess: true,
    })
    readonly resultLines: Collection<xtremStock.nodes.CostRollUpResultLine>;

    @decorators.mutation<typeof CostRollUpInputSet, 'updateAttributesAndDimensions'>({
        isPublished: true,
        parameters: [
            {
                name: 'costRollUpInputSet',
                type: 'reference',
                node: () => xtremStock.nodes.CostRollUpInputSet,
                isMandatory: true,
                isWritable: true,
            },
            { name: 'storedDimensions', type: 'string' },
            { name: 'storedAttributes', type: 'string' },
        ],
        return: {
            type: 'boolean',
            name: 'updateResult',
        },
    })
    static async updateAttributesAndDimensions(
        _context: Context,
        costRollUpInputSet: xtremStock.nodes.CostRollUpInputSet,
        storedDimensions?: string,
        storedAttributes?: string,
    ): Promise<boolean> {
        if ((storedDimensions && storedDimensions !== '{}') || (storedAttributes && storedAttributes !== '{}')) {
            const attributesAndDimensions = {
                ...(storedDimensions && storedDimensions !== '{}'
                    ? { storedDimensions: JSON.parse(storedDimensions) }
                    : undefined),
                ...(storedAttributes && storedAttributes !== '{}'
                    ? { storedAttributes: JSON.parse(storedAttributes) }
                    : undefined),
            };

            // TODO: add filter on status
            // await costRollUpInputSet.resultLines.filter(async resultLine => resultLine.status==='completed').forEach(async resultLine => {
            await costRollUpInputSet.resultLines.forEach(async resultLine => {
                await resultLine.$.set({ ...attributesAndDimensions });
            });
            await costRollUpInputSet.$.save();
        }
        return true;
    }
}
