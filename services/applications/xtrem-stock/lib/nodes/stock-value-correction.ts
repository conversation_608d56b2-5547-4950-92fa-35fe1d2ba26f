import type { NotificationEnvelope } from '@sage/xtrem-communication';
import type { Collection, Context, Reference, integer } from '@sage/xtrem-core';
import { Node, NodeStatus, date, decorators, useDefaultValue } from '@sage/xtrem-core';
import * as xtremFinanceData from '@sage/xtrem-finance-data';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremStockData from '@sage/xtrem-stock-data';
import * as xtremSystem from '@sage/xtrem-system';
import * as xtremStock from '..';

@decorators.node<StockValueCorrection>({
    isClearedByReset: true,
    package: 'xtrem-stock',
    storage: 'sql',
    isPublished: true,
    canRead: true,
    canSearch: true,
    canCreate: true,
    canUpdate: true,
    canDelete: true,
    indexes: [{ orderBy: { number: +1 }, isUnique: true }],
    async saveBegin() {
        await xtremMasterData.functions.controlDocumentNumber(this);
    },
})
@xtremStockData.functions.staticImplements<xtremStockData.interfaces.StaticDocumentHeaderWithStockPosting>()
export class StockValueCorrection
    extends Node
    implements
        xtremFinanceData.interfaces.FinanceOriginDocumentHeader,
        xtremStockData.interfaces.StockDocumentHeaderWithoutStockDetail
{
    getEffectiveDate(): Promise<date> {
        return this.effectiveDate;
    }

    getStockSite(): Promise<xtremSystem.nodes.Site> {
        return this.stockSite;
    }

    @decorators.stringProperty<StockValueCorrection, 'number'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.documentNumber,
        defaultValue() {
            return xtremMasterData.functions.getDocumentNumberPlaceholder();
        },
        deferredDefaultValue() {
            return xtremMasterData.functions.allocateDocumentNumber(this);
        },
        provides: ['sequenceNumber'],
        lookupAccess: true,
    })
    readonly number: Promise<string>;

    @decorators.dateProperty<StockValueCorrection, 'effectiveDate'>({
        isStored: true,
        isPublished: true,
        defaultValue() {
            return date.today();
        },
        provides: ['documentDate'],
        lookupAccess: true,
    })
    readonly effectiveDate: Promise<date>;

    @decorators.referenceProperty<StockValueCorrection, 'stockSite'>({
        isStored: true,
        isPublished: true,
        node: () => xtremSystem.nodes.Site,
        async control(cx, val) {
            await xtremSystem.events.control.isActive(
                this.$.status,
                cx,
                this.$.status === NodeStatus.modified ? (await this.$.old)._id : null,
                val,
            );
        },
        provides: ['site'],
        lookupAccess: true,
    })
    readonly stockSite: Reference<xtremSystem.nodes.Site>;

    @decorators.referenceProperty<StockValueCorrection, 'reasonCode'>({
        isStored: true,
        isPublished: true,
        filters: {
            control: {
                quantityDecreaseAdjustment: false,
                quantityIncreaseAdjustment: false,
                stockValueCorrection: true,
            },
        },
        node: () => xtremMasterData.nodes.ReasonCode,
        lookupAccess: true,
    })
    readonly reasonCode: Reference<xtremMasterData.nodes.ReasonCode>;

    @decorators.stringProperty<StockValueCorrection, 'description'>({
        isStored: true,
        isPublished: true,
        dependsOn: ['reasonCode'],
        dataType: () => xtremSystem.dataTypes.description,
        async defaultValue() {
            return (await this.reasonCode).name;
        },
        lookupAccess: true,
    })
    readonly description: Promise<string>;

    @decorators.collectionProperty<StockValueCorrection, 'lines'>({
        isPublished: true,
        isVital: true,
        isRequired: true,
        reverseReference: 'document',
        node: () => xtremStock.nodes.StockValueCorrectionLine,
        lookupAccess: true,
    })
    readonly lines: Collection<xtremStock.nodes.StockValueCorrectionLine>;

    readonly linesWithStockTransactionStatusCollectionName = 'lines';

    @decorators.enumProperty<StockValueCorrection, 'status'>({
        isPublished: true,
        isStoredOutput: true,
        dependsOn: ['stockTransactionStatus', { lines: ['stockTransactionStatus'] }],
        dataType: () => xtremStockData.enums.StockDocumentStatusDataType,
        async defaultValue() {
            return xtremStockData.functions.stockDocumentLib.getStockDocumentStatus(await this.stockTransactionStatus);
        },
        updatedValue: useDefaultValue,
        lookupAccess: true,
    })
    readonly status: Promise<xtremStockData.enums.StockDocumentStatus>;

    @decorators.enumProperty<StockValueCorrection, 'stockTransactionStatus'>({
        isPublished: true,
        dependsOn: [{ lines: ['stockTransactionStatus'] }],
        dataType: () => xtremStockData.enums.StockDocumentTransactionStatusDataType,
        computeValue() {
            return xtremStockData.functions.stockTransactionLib.computeHeaderStatus(this);
        },
        lookupAccess: true,
    })
    readonly stockTransactionStatus: Promise<xtremStockData.enums.StockDocumentTransactionStatus>;

    // property needed for the accounting interface
    @decorators.referenceProperty<StockValueCorrection, 'financialSite'>({
        isPublished: true,
        node: () => xtremSystem.nodes.Site,
        dependsOn: ['stockSite'],
        async getValue() {
            return xtremFinanceData.functions.getFinancialSite(await this.stockSite);
        },
        lookupAccess: true,
    })
    readonly financialSite: Reference<xtremSystem.nodes.Site>;

    // property needed for the accounting interface
    @decorators.referenceProperty<StockValueCorrection, 'transactionCurrency'>({
        isPublished: true,
        node: () => xtremMasterData.nodes.Currency,
        dependsOn: ['financialSite'],
        async getValue() {
            return (await (await this.financialSite).legalCompany).currency;
        },
        lookupAccess: true,
    })
    readonly transactionCurrency: Reference<xtremMasterData.nodes.Currency>;

    // property needed for the accounting interface
    @decorators.dateProperty<StockValueCorrection, 'documentDate'>({
        isPublished: true,
        dependsOn: ['effectiveDate'],
        getValue() {
            return this.effectiveDate;
        },
        lookupAccess: true,
    })
    readonly documentDate: Promise<date>;

    @decorators.mutation<typeof StockValueCorrection, 'postToStock'>({
        isPublished: true,
        parameters: [
            {
                name: 'documentIds',
                type: 'array',
                item: {
                    type: 'integer',
                },
            },
        ],
        return: { type: 'string' },
    })
    static postToStock(context: Context, documentIds: integer[]): Promise<string> {
        return xtremStockData.functions.notificationLib.stockCorrectionRequestNotification(context, {
            documentClass: StockValueCorrection,
            documentIds,
        });
    }

    @decorators.notificationListener<typeof StockValueCorrection>({
        startsReadOnly: true,
        topic: 'StockValueCorrection/stock/correction/reply',
        onError(
            context: Context,
            envelope: NotificationEnvelope<
                xtremStockData.interfaces.StockReplyPayload<xtremStockData.enums.StockMovementTypeEnum.correction>
            >,
            error: Error,
        ) {
            return xtremStockData.functions.notificationLib.errorHandler(
                context,
                envelope,
                error,
                StockValueCorrection,
            );
        },
    })
    static async onStockReply(
        readOnlyContext: Context,
        payload: xtremStockData.interfaces.StockReplyPayload<xtremStockData.enums.StockMovementTypeEnum.correction>,
    ): Promise<void> {
        const correctionUpdateResult = await readOnlyContext.runInWritableContext(
            async writableContext =>
                (
                    await xtremStockData.functions.notificationLib.reactToStockMovementReply(
                        writableContext,
                        payload,
                        StockValueCorrection,
                    )
                ).correction,
        );
        if (!correctionUpdateResult) return;

        if (!correctionUpdateResult.transactionHadAnError) {
            await correctionUpdateResult.documents.forEach(async document => {
                if (document.isStockDocumentCompleted) {
                    await readOnlyContext.runInWritableContext(async writableContext => {
                        const stockValueCorrection = await writableContext.read(
                            StockValueCorrection,
                            { _id: document.id },
                            { forUpdate: true },
                        );
                        await StockValueCorrection.onceStockCompleted(writableContext, stockValueCorrection);
                    });
                }
            });
        }
    }

    static async onceStockCompleted(
        writableContext: Context,
        stockValueCorrection: StockValueCorrection,
    ): Promise<void> {
        // Send notification in order to create staging table entries for the accounting engine
        if (await (await (await stockValueCorrection.stockSite).legalCompany).doStockPosting) {
            await xtremFinanceData.functions.stockAdjustmentNotification(
                writableContext,
                stockValueCorrection as xtremFinanceData.interfaces.FinanceOriginDocument,
                (await stockValueCorrection.lines.toArray()) as xtremFinanceData.interfaces.FinanceOriginDocumentLine[],
            );
        }
    }

    @decorators.notificationListener<typeof StockValueCorrection>({ topic: 'StockValueCorrection/accountingInterface' })
    static async onFinanceIntegrationReply(
        context: Context,
        payload: xtremFinanceData.interfaces.FinanceTransactionData,
    ): Promise<void> {
        await xtremFinanceData.functions.reactToFinanceIntegrationReply(context, payload);
    }
}
