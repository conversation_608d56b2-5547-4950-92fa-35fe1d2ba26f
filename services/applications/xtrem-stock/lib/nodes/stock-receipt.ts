import type { NotificationEnvelope } from '@sage/xtrem-communication';
import type { AsyncArray, Collection, Context, integer, NodeCreateData, Reference } from '@sage/xtrem-core';
import { asyncArray, BusinessRuleError, date, decorators, Logger, Node, useDefaultValue } from '@sage/xtrem-core';
import * as xtremFinanceData from '@sage/xtrem-finance-data';
import * as xtremMasterData from '@sage/xtrem-master-data';
import { DataInputError } from '@sage/xtrem-shared';
import * as xtremStockData from '@sage/xtrem-stock-data';
import * as xtremSystem from '@sage/xtrem-system';
import * as crypto from 'crypto';
import * as _ from 'lodash';
import * as xtremStock from '..';

const logger = Logger.getLogger(__filename, 'miscellaneous-stock-receipt');

@decorators.node<StockReceipt>({
    isClearedByReset: true,
    storage: 'sql',
    isPublished: true,
    canRead: true,
    canSearch: true,
    canCreate: true,
    canUpdate: true,
    canDelete: true,
    isCustomizable: true,
    hasAttachments: true,
    indexes: [{ orderBy: { number: +1 }, isUnique: true, isNaturalKey: true }],
    notifies: ['created', 'updated', 'deleted'],
    async saveBegin() {
        await xtremMasterData.functions.controlDocumentNumber(this);
        const displayStatus = await xtremStock.functions.stockReceiptLib.computeHeaderDisplayStatus(this);
        await this.$.set({ displayStatus });
    },
})
@xtremStockData.functions.staticImplements<xtremStockData.interfaces.StaticDocumentHeaderWithStockPosting>()
export class StockReceipt extends Node implements xtremFinanceData.interfaces.FinanceOriginDocumentHeader {
    getEffectiveDate(): Promise<date> {
        return this.effectiveDate;
    }

    getStockSite(): Promise<xtremSystem.nodes.Site> {
        return this.stockSite;
    }

    @decorators.stringProperty<StockReceipt, 'number'>({
        isPublished: true,
        isStored: true,
        dataType: () => xtremMasterData.dataTypes.documentNumber,
        defaultValue() {
            return xtremMasterData.functions.getDocumentNumberPlaceholder();
        },
        deferredDefaultValue() {
            return xtremMasterData.functions.allocateDocumentNumber(this);
        },
        provides: ['sequenceNumber'],
        lookupAccess: true,
    })
    readonly number: Promise<string>;

    @decorators.stringProperty<StockReceipt, 'description'>({
        isPublished: true,
        isStored: true,
        dataType: () => xtremSystem.dataTypes.description,
        lookupAccess: true,
    })
    readonly description: Promise<string>;

    @decorators.enumProperty<StockReceipt, 'status'>({
        isPublished: true,
        dependsOn: ['stockTransactionStatus', { lines: ['stockTransactionStatus'] }],
        dataType: () => xtremStockData.enums.StockDocumentStatusDataType,
        async computeValue() {
            return xtremStockData.functions.stockDocumentLib.getStockDocumentStatus(await this.stockTransactionStatus);
        },
        lookupAccess: true,
    })
    readonly status: Promise<xtremStockData.enums.StockDocumentStatus>;

    @decorators.enumProperty<StockReceipt, 'stockTransactionStatus'>({
        isPublished: true,
        isStored: true,
        dependsOn: [{ lines: ['stockTransactionStatus'] }],
        dataType: () => xtremStockData.enums.StockDocumentTransactionStatusDataType,
        defaultValue() {
            return xtremStockData.functions.stockTransactionLib.computeHeaderStatus(this);
        },
        updatedValue: useDefaultValue,
        lookupAccess: true,
    })
    readonly stockTransactionStatus: Promise<xtremStockData.enums.StockDocumentTransactionStatus>;

    @decorators.enumProperty<StockReceipt, 'displayStatus'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        dataType: () => xtremStock.enums.stockReceiptDisplayStatusDataType,
        dependsOn: [{ lines: ['stockTransactionStatus', 'stockDetailStatus'] }, 'status', 'stockTransactionStatus'],
        defaultValue() {
            return xtremStock.functions.stockReceiptLib.computeHeaderDisplayStatus(this);
        },
        updatedValue: useDefaultValue,
    })
    readonly displayStatus: Promise<xtremStock.enums.StockReceiptDisplayStatus>;

    @decorators.dateProperty<StockReceipt, 'effectiveDate'>({
        isPublished: true,
        isStored: true,
        defaultValue() {
            return date.today();
        },
        provides: ['documentDate'],
        lookupAccess: true,
    })
    readonly effectiveDate: Promise<date>;

    @decorators.booleanProperty<StockReceipt, 'forceUpdateForStock'>({
        defaultValue: false,
    })
    readonly forceUpdateForStock: Promise<boolean>;

    @decorators.referenceProperty<StockReceipt, 'stockSite'>({
        isPublished: true,
        isStored: true,
        filters: { control: { isInventory: true } },
        node: () => xtremSystem.nodes.Site,
        provides: ['site'],
        lookupAccess: true,
    })
    readonly stockSite: Reference<xtremSystem.nodes.Site>;

    @decorators.collectionProperty<StockReceipt, 'lines'>({
        isVital: true,
        isRequired: true,
        isPublished: true,
        reverseReference: 'document',
        node: () => xtremStock.nodes.StockReceiptLine,
        lookupAccess: true,
    })
    readonly lines: Collection<xtremStock.nodes.StockReceiptLine>;

    @decorators.collectionProperty<StockReceipt, 'postingDetails'>({
        isPublished: true,
        join: {
            documentSysId() {
                return this._id;
            },
            documentType() {
                return 'miscellaneousStockReceipt';
            },
        },
        node: () => xtremFinanceData.nodes.FinanceTransaction,
        lookupAccess: true,
    })
    readonly postingDetails: Collection<xtremFinanceData.nodes.FinanceTransaction>;

    @decorators.booleanProperty<StockReceipt, 'isSetDimensionsMainListHidden'>({
        isPublished: true,
        async getValue() {
            const displayStatus = await this.displayStatus;
            return displayStatus === 'received' || displayStatus === 'stockPostingInProgress';
        },
    })
    readonly isSetDimensionsMainListHidden: Promise<boolean>;

    readonly linesWithStockTransactionStatusCollectionName = 'lines';

    // property needed for the accounting interface
    @decorators.referenceProperty<StockReceipt, 'financialSite'>({
        isPublished: true,
        node: () => xtremSystem.nodes.Site,
        dependsOn: ['stockSite'],
        async getValue() {
            return xtremFinanceData.functions.getFinancialSite(await this.stockSite);
        },
        lookupAccess: true,
    })
    readonly financialSite: Reference<xtremSystem.nodes.Site>;

    // property needed for the accounting interface
    @decorators.referenceProperty<StockReceipt, 'transactionCurrency'>({
        isPublished: true,
        node: () => xtremMasterData.nodes.Currency,
        dependsOn: ['financialSite'],
        async getValue() {
            return (await (await this.financialSite).legalCompany).currency;
        },
        lookupAccess: true,
    })
    readonly transactionCurrency: Reference<xtremMasterData.nodes.Currency>;

    // property needed for the accounting interface
    @decorators.dateProperty<StockReceipt, 'documentDate'>({
        isPublished: true,
        dependsOn: ['effectiveDate'],
        getValue() {
            return this.effectiveDate;
        },
        lookupAccess: true,
    })
    readonly documentDate: Promise<date>;

    @decorators.enumProperty<StockReceipt, 'financeIntegrationStatus'>({
        isPublished: true,
        dataType: () => xtremFinanceData.enums.financeIntegrationStatusDataType,
        async computeValue() {
            return xtremFinanceData.functions.getDocumentIntegrationStatus(
                this.$.context,
                'miscellaneousStockReceipt',
                await this.number,
            );
        },
        lookupAccess: true,
    })
    readonly financeIntegrationStatus: Promise<xtremFinanceData.enums.FinanceIntegrationStatus>;

    @decorators.mutation<typeof StockReceipt, 'postToStock'>({
        isPublished: true,
        startsReadOnly: true,
        parameters: [
            {
                name: 'documentIds',
                type: 'array',
                item: {
                    type: 'integer',
                },
            },
        ],
        return: { type: 'string' },
    })
    static async postToStock(context: Context, documentIds: integer[]): Promise<string> {
        const numberOfStockReceiptLines = context.queryCount(xtremStock.nodes.StockReceiptLine, {
            filter: {
                document: { _in: documentIds },
                stockDetailStatus: 'required',
            },
        });

        if ((await numberOfStockReceiptLines) > 0) {
            throw new BusinessRuleError(
                context.localize(
                    '@sage/xtrem-stock/nodes__stock_receipt_node_post__stock_details_required',
                    'You need to enter stock details for all lines before you can post.',
                ),
            );
        }

        const notPostedDocumentIds = await context.runInWritableContext(writableContext => {
            return xtremStockData.functions.stockLib.setStockTransactionToProgress(writableContext, {
                documentClass: StockReceipt,
                documentIds,
            });
        });
        if (!notPostedDocumentIds.length) {
            return JSON.stringify({
                notificationId: 'none',
                result: 'requested',
                documents: [],
            } as xtremStockData.interfaces.PostToStockReturnJsonType<xtremStockData.enums.StockMovementTypeEnum>);
        }

        return context.runInWritableContext(writableContext => {
            return xtremStockData.functions.notificationLib.stockReceiptRequestNotification(writableContext, {
                documentClass: StockReceipt,
                documentIds,
            });
        });
    }

    @decorators.notificationListener<typeof StockReceipt>({
        startsReadOnly: true,
        topic: 'StockReceipt/stock/receipt/reply',
        onError(
            context: Context,
            envelope: NotificationEnvelope<
                xtremStockData.interfaces.StockReplyPayload<xtremStockData.enums.StockMovementTypeEnum.receipt>
            >,
            error: Error,
        ) {
            return xtremStockData.functions.notificationLib.errorHandler(context, envelope, error, StockReceipt);
        },
    })
    static async onStockReply(
        readOnlyContext: Context,
        payload: xtremStockData.interfaces.StockReplyPayload<xtremStockData.enums.StockMovementTypeEnum.receipt>,
    ): Promise<void> {
        const receiptUpdateResult = await readOnlyContext.runInWritableContext(
            async writableContext =>
                (
                    await xtremStockData.functions.notificationLib.reactToStockMovementReply(
                        writableContext,
                        payload,
                        StockReceipt,
                    )
                ).receipt,
        );
        if (!receiptUpdateResult) return;

        if (!receiptUpdateResult.transactionHadAnError) {
            await receiptUpdateResult.documents.forEach(async document => {
                if (document.isStockDocumentCompleted) {
                    await readOnlyContext.runInWritableContext(async writableContext => {
                        const stockReceipt = await writableContext.read(
                            StockReceipt,
                            { _id: document.id },
                            { forUpdate: true },
                        );
                        await StockReceipt.onceStockCompleted(writableContext, stockReceipt);
                    });
                }
            });
        }
    }

    static async onceStockCompleted(writableContext: Context, stockReceipt: StockReceipt): Promise<void> {
        // Send notification in order to create staging table entries for the accounting engine
        if (await (await (await stockReceipt.stockSite).legalCompany).doStockPosting) {
            await xtremFinanceData.functions.stockReceiptNotification(
                writableContext,
                stockReceipt as xtremFinanceData.interfaces.FinanceOriginDocument,
                (await stockReceipt.lines.toArray()) as xtremFinanceData.interfaces.FinanceOriginDocumentLine[],
            );
        }
    }

    @decorators.notificationListener<typeof StockReceipt>({ topic: 'StockReceipt/accountingInterface' })
    static async onFinanceIntegrationReply(
        context: Context,
        payload: xtremFinanceData.interfaces.FinanceTransactionData,
    ): Promise<void> {
        await xtremFinanceData.functions.reactToFinanceIntegrationReply(context, payload);
    }

    @decorators.notificationListener<typeof StockReceipt>({ topic: 'MiscellaneousStockReceipt/accountingInterface' })
    static async onFinanceIntegrationReplyV31(
        context: Context,
        payload: xtremFinanceData.interfaces.FinanceTransactionData,
    ): Promise<void> {
        await xtremStock.nodes.StockReceipt.onFinanceIntegrationReply(context, payload);
    }

    @decorators.referenceProperty<StockReceipt, 'reasonCode'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
        lookupAccess: true,
        node: () => xtremMasterData.nodes.ReasonCode,
        filters: {
            control: { isStockReceipt: true },
        },
    })
    readonly reasonCode: Reference<xtremMasterData.nodes.ReasonCode | null>;

    @decorators.mutation<typeof StockReceipt, 'repost'>({
        isPublished: true,
        parameters: [
            {
                name: 'stockReceipt',
                type: 'reference',
                isMandatory: true,
                isWritable: true,
                node: () => StockReceipt,
            },
            {
                name: 'documentLines',
                type: 'array',
                item: {
                    type: 'object',
                    properties: {
                        baseDocumentLineSysId: 'integer',
                        storedAttributes: 'json',
                        storedDimensions: 'json',
                    },
                },
            },
        ],
        return: { type: 'object', properties: { wasSuccessful: 'boolean', message: 'string' } },
    })
    static async repost(
        context: Context,
        stockReceipt: StockReceipt,
        documentLines: xtremFinanceData.interfaces.FinanceIntegrationDocumentLineUpdate[],
    ): Promise<xtremFinanceData.interfaces.MutationResult> {
        if (!xtremFinanceData.functions.canRepost(await stockReceipt.financeIntegrationStatus)) {
            throw new BusinessRuleError(
                context.localize(
                    '@sage/xtrem-stock/nodes__stock-receipt__cant_repost_stock_receipt_when_status_is_not_failed',
                    "You can only repost a stock receipt if the status is 'Failed' or 'Not recorded.'",
                ),
            );
        }

        const updateLines = documentLines.map(documentLine => {
            return {
                _id: documentLine.baseDocumentLineSysId,
                storedAttributes: documentLine.storedAttributes,
                storedDimensions: documentLine.storedDimensions,
            };
        });
        await stockReceipt.$.set({ lines: updateLines });
        await stockReceipt.$.save();

        // send notification in order to update a staging table entry for the accounting engine
        if (await (await (await stockReceipt.stockSite).legalCompany).doStockPosting) {
            await xtremFinanceData.functions.financeDocumentUpdateNotification(context, {
                document: stockReceipt,
                lines: await stockReceipt.lines.toArray(),
                documentType: 'miscellaneousStockReceipt',
                replyTopic: 'StockReceiptUpdate/accountingInterface',
                doNotPostOnUpdate: false,
            });
            return {
                wasSuccessful: true,
                message: context.localize(
                    '@sage/xtrem-stock/nodes__stock-receipt__document_was_posted',
                    'The stock receipt was posted.',
                ),
            };
        }
        return {
            wasSuccessful: true,
            message: context.localize(
                '@sage/xtrem-stock/nodes__stock-receipt__company_has_no_stock_posting',
                'The current company has no stock posting.',
            ),
        };
    }

    @decorators.asyncMutation<typeof StockReceipt, 'createTestStockReceipt'>({
        isPublished: true,
        parameters: [
            {
                name: 'data',
                type: 'object',
                properties: {
                    site: {
                        type: 'reference',
                        isMandatory: true,
                        node: () => xtremSystem.nodes.Site,
                    },
                    nbOfLines: { type: 'integer' },
                    maxNbOfLines: { type: 'integer' },
                    items: {
                        type: 'array',
                        item: { type: 'reference', isMandatory: true, node: () => xtremMasterData.nodes.Item },
                    },
                    itemRange: {
                        type: 'object',
                        properties: {
                            from: { type: 'integer', isMandatory: true },
                            to: { type: 'integer', isMandatory: true },
                        },
                    },
                    useRandomItemFromSite: 'boolean',
                    useRandomQuantities: 'boolean',
                },
            },
        ],
        return: { type: 'reference', node: () => StockReceipt },
        serviceOptions: () => [xtremSystem.serviceOptions.DevTools],
    })
    static async createTestStockReceipt(
        context: Context,
        data: {
            site: Reference<xtremSystem.nodes.Site>;
            nbOfLines: integer;
            maxNbOfLines: integer;
            items?: Reference<xtremMasterData.nodes.Item>[];
            itemRange?: {
                from: integer;
                to: integer;
            };
            useRandomItemFromSite?: true;
            useRandomQuantities?: true;
        },
    ) {
        const site = await data.site;
        let items: AsyncArray<xtremMasterData.nodes.Item> = asyncArray([]);

        // if still no items => error
        if (!data.maxNbOfLines && !data.nbOfLines) {
            throw new DataInputError(
                `The parameter received do not allow for any line to be created.
                Please review the values used for maxNbOfLine & nbOfLine.
                ${JSON.stringify({ maxNbOfLines: data.maxNbOfLines, nbOfLine: data.nbOfLines })}`,
            );
        }

        if (!data.useRandomItemFromSite) {
            if (data.items && data.items.length > 0) {
                items = asyncArray(data.items).map(async item => {
                    const awaitedRef = await item;
                    _.noop(awaitedRef);
                    return awaitedRef;
                });
            } else if (data.itemRange) {
                items = context.query(xtremMasterData.nodes.Item, {
                    filter: {
                        _id: {
                            _or: [{ _lte: data.itemRange.from }, { _gte: data.itemRange.to }],
                        },
                    },
                });
            }
        }

        // if no Items will try to use the ones from the site
        if ((await items.length) === 0) {
            logger.info(
                '[createTestStockReceipt] - No item found using the given the range nor with the detailed list',
            );

            if (data.useRandomItemFromSite) {
                items = context
                    .query(xtremMasterData.nodes.Item, {
                        filter: {
                            itemSites: {
                                _atLeast: 1,
                                site: { _id: site._id },
                                item: {
                                    isStockManaged: true,
                                    lotManagement: { _in: ['notManaged', 'lotManagement'] },
                                    serialNumberManagement: 'notManaged',
                                    isPhantom: false,
                                },
                            },
                        },
                    })
                    .filter(async item => {
                        return (
                            (await context.queryCount(xtremStock.nodes.StockCountLine, {
                                filter: {
                                    document: {
                                        stockSite: site._id,
                                        status: { _in: ['countInProgress', 'counted'] },
                                    },
                                    item: item._id,
                                    status: { _ne: 'excluded' },
                                },
                            })) === 0
                        );
                    });
            }
        }

        if ((await items.length) === 0) {
            throw new DataInputError(
                `The parameter received do not allow for using any item to create the receipt line with.
                Please review the values used for items, itemRange, useRandomItemFromSite & site.
                ${JSON.stringify({
                    items: data.items
                        ? await asyncArray(data.items)
                              .map(async item => {
                                  const awaitedRef = await item;
                                  return awaitedRef._id;
                              })
                              .toArray()
                        : undefined,
                    itemRange: data.itemRange,
                    useRandomItemFromSite: data.useRandomItemFromSite,
                    site: site._id,
                })}`,
            );
        }

        let currentItemIndex = 0;

        const { hours, minutes, seconds } = xtremMasterData.sharedFunctions.getTimeNow();

        const receiptDocument = await context.create(StockReceipt, {
            stockSite: site,
            lines: await asyncArray(
                Array.from(
                    {
                        length:
                            data.maxNbOfLines !== undefined
                                ? crypto.randomInt(1, data.maxNbOfLines + 1)
                                : data.nbOfLines,
                    },
                    () => 1,
                ),
            )
                .map(async (_line, index) => {
                    const item = await items.elementAt(currentItemIndex);

                    currentItemIndex += 1;

                    if (currentItemIndex === (await items.length)) {
                        currentItemIndex = 0;
                    }
                    const quantityInStockUnit = data.useRandomQuantities ? crypto.randomInt(1, 1001) : 100;
                    const stockStatus = await context
                        .query(xtremStockData.nodes.StockStatus, { filter: { type: 'accepted' }, first: 1 })
                        .elementAt(0);

                    const createData = {
                        item,
                        stockStatus,
                        quantityInStockUnit,
                        stockDetails: [
                            {
                                item,
                                site,
                                status: stockStatus,
                                quantityInStockUnit,
                            },
                        ],
                    } as NodeCreateData<xtremStock.nodes.StockReceiptLine>;

                    if (await site.isLocationManaged) {
                        createData.location = await context
                            .query(xtremMasterData.nodes.Location, {
                                filter: { locationZone: { site: { _id: site._id } } },
                                first: 1,
                            })
                            .elementAt(0);

                        if (createData.stockDetails) {
                            createData.stockDetails[0].location = createData.location;
                        }
                    }

                    if ((await item.lotManagement) === 'lotManagement') {
                        if (createData.stockDetails) {
                            createData.stockDetails[0].stockDetailLot = {
                                lotNumber: `LOT-${hours}${minutes}${seconds}-${(index + 1).toString().padStart(Math.ceil(Math.log10(data.maxNbOfLines ?? data.nbOfLines + 1)), '0')}`,
                            };
                        }
                    }

                    return createData;
                })
                .toArray(),
        });

        await receiptDocument.$.save();

        return receiptDocument;
    }

    @decorators.mutation<typeof StockReceipt, 'resendNotificationForFinance'>({
        isPublished: true,
        parameters: [{ name: 'stockReceipt', type: 'reference', isMandatory: true, node: () => StockReceipt }],
        return: { type: 'boolean' },
    })
    static async resendNotificationForFinance(context: Context, stockReceipt: StockReceipt): Promise<boolean> {
        logger.info(
            context.localize(
                '@sage/xtrem-stock/node__stock_receipt__resend_notification_for_finance',
                'Resending finance notification for stock receipt: {{stockNumber}}',
                { stockNumber: await stockReceipt.number },
            ),
        );

        if (
            await xtremFinanceData.functions.AccountingEngineCommon.validateResendNotificationForFinance(context, {
                documentNumber: await stockReceipt.number,
                documentType: 'miscellaneousStockReceipt',
            })
        ) {
            // send notification in order to create a staging table entry for the accounting engine
            if (await (await (await stockReceipt.stockSite).legalCompany).doStockPosting) {
                await xtremFinanceData.functions.stockReceiptNotification(
                    context,
                    stockReceipt as xtremFinanceData.interfaces.FinanceOriginDocument,
                    (await stockReceipt.lines.toArray()) as xtremFinanceData.interfaces.FinanceOriginDocumentLine[],
                );
            }
        }

        return true;
    }

    @decorators.notificationListener<typeof StockReceipt>({
        topic: 'StockReceipt/resendNotificationForFinance',
    })
    static async onResendNotificationForFinance(context: Context, document: { number: number }) {
        const stockReceipt = await context.read(StockReceipt, { number: document.number });

        await StockReceipt.resendNotificationForFinance(context, stockReceipt);
    }
}
