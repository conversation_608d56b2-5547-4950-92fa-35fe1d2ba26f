import type { Collection, Context, decimal, integer, JsonType, NodeCreateData, Reference } from '@sage/xtrem-core';
import { asyncArray, decorators, NodeStatus, useDefaultValue } from '@sage/xtrem-core';
import * as xtremFinanceData from '@sage/xtrem-finance-data';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremStockData from '@sage/xtrem-stock-data';
import * as xtremSystem from '@sage/xtrem-system';
import * as xtremStock from '..';

@decorators.subNode<StockAdjustmentLine>({
    isPublished: true,
    canRead: true,
    canSearch: true,
    canCreate: true,
    canUpdate: true,
    isVitalCollectionChild: true,
    extends: () => xtremMasterData.nodes.BaseDocumentLine,
    async controlBegin(cx) {
        await cx.error
            .withMessage(
                '@sage/xtrem-stock/nodes__stock_adjustment_line__cannot_update_completed_line',
                'An Adjusted line cannot be updated.',
            )
            .if(
                this.$.status === NodeStatus.modified &&
                    !(await (
                        await this.document
                    ).forceUpdateForFinance) &&
                    (await (await this.$.old).stockTransactionStatus) === 'completed',
            )
            .is.true();
    },
    async controlEnd(cx) {
        await xtremFinanceData.functions.attributTypeRestrictedToCheck(cx, this.$.context, await this.storedAttributes);
    },
})
@xtremStockData.functions.staticImplements<xtremStockData.interfaces.StaticDocumentLineWithStockPosting>()
// TODO: implements xtremFinanceData.interfaces.FinanceOriginDocumentLine
//       when incompatibility on stockMovements will be resolved
export class StockAdjustmentLine
    extends xtremMasterData.nodes.BaseDocumentLine
    implements xtremFinanceData.interfaces.FinanceOriginDocumentLine
{
    getOrderCost(): Promise<decimal> {
        return this.orderCost;
    }

    getValuedCost(): Promise<decimal> {
        return this.valuedCost;
    }

    async getValuationParameters(): Promise<xtremStockData.interfaces.ValuationParameter> {
        // Only quantityDecreaseAdjustment and quantityIncreaseAdjustment are authorized
        return (await (
            await (
                await this.document
            ).reasonCode
        ).quantityDecreaseAdjustment)
            ? { valuationType: 'issue' }
            : { valuationType: 'receipt' };
    }

    getItem(): Promise<xtremMasterData.nodes.Item> {
        return this.item;
    }

    @decorators.referenceProperty<StockAdjustmentLine, 'document'>({
        isStored: true,
        isPublished: true,
        node: () => xtremStock.nodes.StockAdjustment,
        isVitalParent: true,
    })
    override readonly document: Reference<xtremStock.nodes.StockAdjustment>;

    @decorators.stringPropertyOverride<StockAdjustmentLine, 'documentNumber'>({
        async getValue() {
            return (await this.document).number;
        },
    })
    override readonly documentNumber: Promise<string>;

    @decorators.integerPropertyOverride<StockAdjustmentLine, 'documentId'>({
        async getValue() {
            return (await this.document)._id;
        },
    })
    override readonly documentId: Promise<integer>;

    @decorators.enumProperty<StockAdjustmentLine, 'stockTransactionStatus'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        defaultValue() {
            return 'draft';
        },
        dataType: () => xtremStockData.enums.StockDocumentTransactionStatusDataType,
    })
    readonly stockTransactionStatus: Promise<xtremStockData.enums.StockDocumentTransactionStatus>;

    @decorators.referenceProperty<StockAdjustmentLine, 'item'>({
        isStored: true,
        isPublished: true,
        node: () => xtremMasterData.nodes.Item,
        lookupAccess: true,
        filters: {
            control: {
                type: 'good',
                isStockManaged: true,
                status: 'active',
            },
        },
        async control(cx) {
            if (
                (await this.itemSite) &&
                (await (await this.itemSite).valuationMethod) !== 'averageCost' &&
                (await (
                    await this.document
                ).reasonCode) &&
                (await (
                    await (
                        await this.document
                    ).reasonCode
                ).stockValueCorrection)
            ) {
                cx.error.addLocalized(
                    '@sage/xtrem-stock/nodes__stock_adjustment_line__incorrect_valuation_method',
                    'The {{item}} item must be linked to an average cost valuation method.',
                    { item: await (await this.item).name },
                );
            }
        },
    })
    readonly item: Reference<xtremMasterData.nodes.Item>;

    @decorators.decimalProperty<StockAdjustmentLine, 'quantityInStockUnit'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.quantityInStockUnit,
        lookupAccess: true,
    })
    readonly quantityInStockUnit: Promise<decimal>;

    @decorators.referenceProperty<StockAdjustmentLine, 'stockUnit'>({
        isPublished: true,
        dependsOn: ['item'],
        node: () => xtremMasterData.nodes.UnitOfMeasure,
        lookupAccess: true,
        async getValue() {
            return (await this.item).stockUnit;
        },
    })
    readonly stockUnit: Reference<xtremMasterData.nodes.UnitOfMeasure>;

    @decorators.decimalProperty<StockAdjustmentLine, 'adjustmentQuantityInStockUnit'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        dataType: () => xtremMasterData.dataTypes.quantityInStockUnit,
    })
    readonly adjustmentQuantityInStockUnit: Promise<decimal>;

    @decorators.decimalProperty<StockAdjustmentLine, 'unitCost'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        dataType: () => xtremMasterData.dataTypes.costDataType,
        dependsOn: ['itemSite'],
        defaultValue() {
            // unitCost is used for entry adjustment
            return xtremStockData.functions.stockDocumentLib.defaultDocumentLineCost(this.$.context, this, {
                valuationType: 'receipt',
            });
        },
    })
    readonly unitCost: Promise<decimal>;

    // [RM] For the finance interface
    @decorators.decimalProperty<StockAdjustmentLine, 'orderCost'>({
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.costDataType,
        dependsOn: ['unitCost'],
        lookupAccess: true,
        getValue() {
            return this.unitCost;
        },
    })
    readonly orderCost: Promise<decimal>;

    //  [RM] For the finance interface
    @decorators.decimalProperty<StockAdjustmentLine, 'valuedCost'>({
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.costDataType,
        dependsOn: ['unitCost'],
        lookupAccess: true,
        getValue() {
            return this.unitCost;
        },
    })
    readonly valuedCost: Promise<decimal>;

    @decorators.decimalProperty<StockAdjustmentLine, 'newUnitCost'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        dataType: () => xtremMasterData.dataTypes.costDataType,
    })
    readonly newUnitCost: Promise<decimal>;

    @decorators.decimalProperty<StockAdjustmentLine, 'stockValue'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.stockVariationValue,
        lookupAccess: true,
    })
    readonly stockValue: Promise<decimal>;

    @decorators.decimalProperty<StockAdjustmentLine, 'newStockValue'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.stockVariationValue,
        lookupAccess: true,
    })
    readonly newStockValue: Promise<decimal>;

    @decorators.referenceProperty<StockAdjustmentLine, 'location'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
        lookupAccess: true,
        dependsOn: [{ document: ['stockSite'] }],
        filters: {
            control: {
                // TODO: uncomment when is XT-12798 implemented
                // locationZone: {
                //     site() {
                //         return this.document.stockSite;
                //     },
                // },
            },
        },
        node: () => xtremMasterData.nodes.Location,
    })
    readonly location: Reference<xtremMasterData.nodes.Location | null>;

    @decorators.referenceProperty<StockAdjustmentLine, 'stockStatus'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
        node: () => xtremStockData.nodes.StockStatus,
        lookupAccess: true,
    })
    readonly stockStatus: Reference<xtremStockData.nodes.StockStatus | null>;

    @decorators.referenceProperty<StockAdjustmentLine, 'lot'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
        dependsOn: ['item'],
        filters: {
            control: {
                item() {
                    return this.item;
                },
            },
        },
        node: () => xtremStockData.nodes.Lot,
        lookupAccess: true,
    })
    readonly lot: Reference<xtremStockData.nodes.Lot | null>;

    @decorators.collectionProperty<StockAdjustmentLine, 'stockDetails'>({
        isPublished: true,
        isVital: true,
        reverseReference: 'documentLine',
        dependsOn: ['unitCost'],
        node: () => xtremStockData.nodes.StockAdjustmentDetail,
        lookupAccess: true,
    })
    readonly stockDetails: Collection<xtremStockData.nodes.StockAdjustmentDetail>;

    @decorators.jsonProperty<StockAdjustmentLine, 'jsonStockDetails'>({
        isPublished: true,
        dependsOn: ['stockDetails'],
        lookupAccess: true,
        async setValue(details) {
            if (details && details.length > 0) {
                const newDetails = xtremStockData.functions.stockDetailLib.parseDetails(
                    details,
                ) as NodeCreateData<xtremStockData.nodes.StockAdjustmentDetail>[];
                const stockDetails = await asyncArray(newDetails)
                    .map(async detail => ({
                        ...detail,
                        reasonCode: await (await this.document).reasonCode,
                    }))
                    .toArray();
                await this.$.set({
                    stockDetails,
                });
            }
        },
        async computeValue() {
            if ((await this.stockDetails.length) > 0) {
                return this.stockDetails.map(detail =>
                    xtremStockData.functions.stockDetailLib.filterStockDetailProperties(
                        detail,
                        xtremStockData.nodes.StockAdjustmentDetail,
                        {
                            onlyIds: true,
                        },
                    ),
                );
            }
            // forcing typing to accept an empty object
            return {} as any;
        },
    })
    readonly jsonStockDetails: Promise<Array<JsonType<NodeCreateData<xtremStockData.nodes.StockAdjustmentDetail>>>>;

    @decorators.collectionProperty<StockAdjustmentLine, 'stockMovements'>({
        isPublished: true,
        reverseReference: 'documentLine',
        node: () => xtremStockData.nodes.StockJournal,
        lookupAccess: true,
    })
    readonly stockMovements: Collection<xtremStockData.nodes.StockJournal>;

    @decorators.collectionProperty<StockAdjustmentLine, 'stockTransactions'>({
        isPublished: true,
        reverseReference: 'documentLine',
        isVital: true,
        node: () => xtremStockData.nodes.StockTransaction,
        lookupAccess: true,
    })
    readonly stockTransactions: Collection<xtremStockData.nodes.StockTransaction>;

    @decorators.referenceProperty<StockAdjustmentLine, 'itemSite'>({
        isPublished: true,
        node: () => xtremMasterData.nodes.ItemSite,
        lookupAccess: true,
        join: {
            item() {
                return this.item;
            },
            async site() {
                return (await this.document).stockSite;
            },
        },
    })
    readonly itemSite: Reference<xtremMasterData.nodes.ItemSite>;

    // the site property on line level is needed for the accounting interface
    @decorators.referenceProperty<StockAdjustmentLine, 'site'>({
        isPublished: true,
        node: () => xtremSystem.nodes.Site,
        async getValue() {
            return (await this.document).stockSite;
        },
        lookupAccess: true,
    })
    readonly site: Reference<xtremSystem.nodes.Site>;

    @decorators.decimalProperty<StockAdjustmentLine, 'newStockQuantity'>({
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.stockVariationValue,
        lookupAccess: true,
        async getValue() {
            const reasonCode = await (await this.document).reasonCode;
            if (await reasonCode.quantityIncreaseAdjustment) {
                return (await this.quantityInStockUnit) + (await this.adjustmentQuantityInStockUnit);
            }

            if (await reasonCode.quantityDecreaseAdjustment) {
                return (await this.quantityInStockUnit) - (await this.adjustmentQuantityInStockUnit);
            }

            return 0;
        },
    })
    readonly newStockQuantity: Promise<decimal>;

    @decorators.jsonProperty<StockAdjustmentLine, 'storedDimensions'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
        lookupAccess: true,
        dataType: () => xtremFinanceData.dataTypes.storedDimensionsDataType,
        dependsOn: ['item', { document: ['stockSite'] }],
        async defaultValue() {
            const site = await (await this.document).stockSite;
            return xtremFinanceData.functions.getDefaultDimensions(this.$.context, {
                dimensionDefinitionLevel: 'stockDirect',
                companyId: (await site.legalCompany)._id,
                site,
                item: await this.item,
            });
        },
    })
    readonly storedDimensions: Promise<object | null>;

    @decorators.referenceProperty<StockAdjustmentLine, 'analyticalData'>({
        isStored: true,
        isMutable: true,
        isPublished: true,
        lookupAccess: true,
        ignoreIsActive: true,
        isFrozen() {
            return xtremFinanceData.functions.isAnalyticalDataFrozen(this);
        },
        node: () => xtremFinanceData.nodes.AnalyticalData,
        dependsOn: ['storedAttributes', 'storedDimensions'],
        async defaultValue() {
            return (await xtremFinanceData.functions.getAnalyticalDataFromStoredAttributesAndDimensions(
                this.$.context,
                await this.storedAttributes,
                await this.storedDimensions,
            )) as any;
        },
        updatedValue: useDefaultValue,
    })
    readonly analyticalData: Reference<xtremFinanceData.nodes.AnalyticalData>;

    @decorators.jsonProperty<StockAdjustmentLine, 'storedAttributes'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
        lookupAccess: true,
        dataType: () => xtremFinanceData.dataTypes.storedAttributesDataType,
        dependsOn: ['item', { document: ['stockSite'] }],
        async defaultValue() {
            const site = await (await this.document).stockSite;
            return xtremFinanceData.functions.getDefaultAttributes(this.$.context, {
                dimensionDefinitionLevel: 'stockDirect',
                companyId: (await site.legalCompany)._id,
                site,
                item: await this.item,
            });
        },
    })
    readonly storedAttributes: Promise<xtremMasterData.interfaces.StoredAttributes | null>;

    @decorators.jsonProperty<StockAdjustmentLine, 'computedAttributes'>({
        isPublished: true,
        dependsOn: ['item', { document: ['stockSite'] }],
        lookupAccess: true,
        async computeValue() {
            return xtremStock.functions.analytical.computeAttributes(this.$.context, {
                item: await this.item,
                stockSite: await (await this.document).stockSite,
            });
        },
    })
    readonly computedAttributes: Promise<object>;

    @decorators.enumProperty<StockAdjustmentLine, 'stockDetailStatus'>({
        isPublished: true,
        isStoredOutput: true,
        dataType: () => xtremStockData.enums.stockDetailStatusDataType,
        dependsOn: ['item', 'stockDetails'],
        async defaultValue() {
            return xtremStockData.functions.stockDetailLib.getStockDetailStatus({
                line: this,
                quantityExpected: await this.adjustmentQuantityInStockUnit,
                checkSerialNumbers: false,
            });
        },
        updatedValue: useDefaultValue,
        lookupAccess: true,
    })
    readonly stockDetailStatus: Promise<xtremStockData.enums.StockDetailStatus>;

    @decorators.enumProperty<StockAdjustmentLine, 'displayStatus'>({
        isStoredOutput: true,
        isPublished: true,
        dataType: () => xtremStock.enums.StockAdjustmentDisplayStatusDataType,
        dependsOn: ['stockDetailStatus', 'stockTransactionStatus'],
        async defaultValue() {
            return xtremStock.functions.stockAdjustmentLib.calculateStockAdjustmentLineDisplayStatus(
                await this.stockDetailStatus,
                await this.stockTransactionStatus,
            );
        },
        updatedValue: useDefaultValue,
    })
    readonly displayStatus: Promise<xtremStock.enums.StockAdjustmentDisplayStatus>;

    /**
     * Method that applies dimensions and attributes to the lines of a stock adjustment line
     * @param context
     * @param StockAdjustmentLine
     * @param storedDimensions
     * @param storedAttributes
     * @returns true or throws error
     */
    @decorators.mutation<typeof StockAdjustmentLine, 'setDimension'>({
        isPublished: true,
        parameters: [
            {
                name: 'stockAdjustmentLine',
                type: 'reference',
                node: () => xtremStock.nodes.StockAdjustmentLine,
                isMandatory: true,
                isWritable: true,
            },
            { name: 'storedDimensions', type: 'string' },
            { name: 'storedAttributes', type: 'string' },
        ],
        return: 'boolean',
    })
    static async setDimension(
        _context: Context,
        stockAdjustmentLine: xtremStock.nodes.StockAdjustmentLine,
        storedDimensions?: string,
        storedAttributes?: string,
    ): Promise<boolean> {
        await stockAdjustmentLine.$.set({
            storedDimensions: storedDimensions ? JSON.parse(storedDimensions) : null,
            storedAttributes: storedAttributes ? JSON.parse(storedAttributes) : null,
        });
        await stockAdjustmentLine.$.save();
        return true;
    }
}
