import type { Collection, Context, JsonType, NodeCreateData, Reference, decimal, integer } from '@sage/xtrem-core';
import { BusinessRuleError, NodeStatus, asyncArray, decorators, useDefaultValue } from '@sage/xtrem-core';
import * as xtremFinanceData from '@sage/xtrem-finance-data';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremStockData from '@sage/xtrem-stock-data';
import * as xtremSystem from '@sage/xtrem-system';
import * as xtremStock from '..';

@decorators.subNode<StockCountLine>({
    extends: () => xtremMasterData.nodes.BaseDocumentLine,
    isPublished: true,
    canRead: true,
    canSearch: true,
    isVitalCollectionChild: true,
    async prepare() {
        if ((await this.status) === 'excluded') {
            await this.$.set({ countedQuantityInStockUnit: 0 });
            return;
        }

        const isSerialNumberManaged =
            (await this.$.context.isServiceOptionEnabled(xtremMasterData.serviceOptions.serialNumberOption)) &&
            (await (await this.item).serialNumberManagement) === 'managed';

        if (isSerialNumberManaged && (await this.countedQuantityInStockUnit) !== (await this.countedSerialNumber)) {
            await this.$.set({ status: (await this.countedSerialNumber) === 0 ? 'toBeCounted' : 'countInProgress' });
        }
        if (
            (await this.countedQuantityInStockUnit) > 0 &&
            (!isSerialNumberManaged || (await this.countedQuantityInStockUnit) === (await this.countedSerialNumber))
        ) {
            await this.$.set({ status: 'counted' });
        }

        await xtremStock.events.prepare.stockCountLinePrepare.prepareStockRecord(this);

        await xtremStock.events.prepare.stockCountLinePrepare.prepareStockDetail(this);
    },
    async controlBegin(cx) {
        await xtremStock.events.controls.stockCountLineControls.checkExistingStockLine(cx, this);

        await xtremStock.events.controls.stockCountLineControls.controlLineCanBeAdded(cx, this);

        await xtremStock.events.controls.stockCountLineControls.controlLineCanBeModified(cx, this);
    },
    async saveBegin() {
        await this.createStockCountLineSerialNumbers();
    },
    async saveEnd() {
        let itemSite = await this.itemSite;
        const newLastCountDate = await (await this.document).effectiveDate;
        const currentLastCountDate = await itemSite.lastCountDate;
        if (
            (await this.stockTransactionStatus) === 'completed' &&
            (await this.status) !== 'excluded' &&
            (!currentLastCountDate || currentLastCountDate.compare(newLastCountDate) < 0)
        ) {
            itemSite = await this.$.context.read(
                xtremMasterData.nodes.ItemSite,
                { _id: itemSite._id },
                { forUpdate: true },
            );
            await itemSite.$.set({ lastCountDate: newLastCountDate });
            await itemSite.$.save();
        }
        this._canUpdateStockQuantity = false;
    },
    async controlEnd(cx) {
        await xtremFinanceData.functions.attributTypeRestrictedToCheck(cx, this.$.context, await this.storedAttributes);
    },
    async controlDelete(cx, isCascading) {
        const message = await xtremStock.functions.stockCountLib.checkStockCountLineDeletion(this.$.context, {
            stockCountLine: this,
            isCascading,
        });

        if (message) {
            cx.error.add(message);
        }
    },
})
@xtremStockData.functions.staticImplements<xtremStockData.interfaces.StaticDocumentLineWithStockPosting>()
export class StockCountLine extends xtremMasterData.nodes.BaseDocumentLine {
    async getOrderCost(): Promise<decimal> {
        if ((await this.status) === 'excluded') return 0;

        // If a cost has been entered, it is the order cost
        const cost = await this.newLineOrderCost;
        if ((await this.quantityInStockUnit) === 0 && cost !== null) return cost;

        // otherwise, we use the valuation method to get the order cost
        return this.getValuedCost();
    }

    async getValuedCost(): Promise<decimal> {
        return xtremStockData.functions.stockDocumentLib.defaultDocumentLineCost(this.$.context, this, {
            valuationType: (await this.quantityVariance) > 0 ? 'receipt' : 'issue',
        });
    }

    async getValuationParameters(): Promise<xtremStockData.interfaces.ValuationParameter> {
        return (await this.quantityVariance) < 0 ? { valuationType: 'issue' } : { valuationType: 'receipt' };
    }

    getItem(): Promise<xtremMasterData.nodes.Item> {
        return this.item;
    }

    @decorators.booleanProperty<StockCountLine, 'forceUpdateForStock'>({
        defaultValue: false,
    })
    readonly forceUpdateForStock: Promise<boolean>;

    @decorators.booleanProperty<StockCountLine, 'forceUpdateForFinance'>({
        defaultValue: false,
    })
    readonly forceUpdateForFinance: Promise<boolean>;

    /**
     * indicates if the line was added during the count (true)
     * or the line was added during initialization of the count (false)
     */
    @decorators.booleanProperty<StockCountLine, 'isAddedDuringCount'>({
        isPublished: true,
        isStoredOutput: true,
        dependsOn: [{ stockDetail: ['stockRecord'] }, { document: ['status'] }],
        async defaultValue() {
            // if the line is linked to a stock record, it's definitely a line added at stock count creation time
            if ((await (await this.stockDetail)?.stockRecord) != null) return false;
            // if the line is added when modifying the stock count from 'draft' to 'toBeCounted', it's a line added at stock count creation time
            const document = await this.document;
            const oldDocument = await document.$.old;
            if (oldDocument && (await oldDocument.status) === 'draft') return false;
            // if the line is added when modifying the stock count that is already 'toBeCounted', it's a line added during the count
            return (await document.status) !== 'toBeCounted';
        },
        // The value is never modified after the creation of the line
        updatedValue() {
            return this.isAddedDuringCount;
        },
    })
    readonly isAddedDuringCount: Promise<boolean>;

    @decorators.referenceProperty<StockCountLine, 'document'>({
        isStored: true,
        isPublished: true,
        isVitalParent: true,
        lookupAccess: true,
        node: () => xtremStock.nodes.StockCount,
    })
    override readonly document: Reference<xtremStock.nodes.StockCount>;

    @decorators.stringPropertyOverride<StockCountLine, 'documentNumber'>({
        async getValue() {
            return (await this.document).number;
        },
    })
    override readonly documentNumber: Promise<string>;

    @decorators.integerPropertyOverride<StockCountLine, 'documentId'>({
        async getValue() {
            return (await this.document)._id;
        },
    })
    override readonly documentId: Promise<integer>;

    @decorators.enumProperty<StockCountLine, 'stockTransactionStatus'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        defaultValue() {
            return 'draft';
        },
        dataType: () => xtremStockData.enums.StockDocumentTransactionStatusDataType,
        async isFrozen() {
            return (await (await this.document).status) === 'closed';
        },
    })
    readonly stockTransactionStatus: Promise<xtremStockData.enums.StockDocumentTransactionStatus>;

    @decorators.enumProperty<StockCountLine, 'status'>({
        isStored: true,
        isPublished: true,
        isRequired: true,
        lookupAccess: true,
        dataType: () => xtremStock.enums.StockCountLineStatusDataType,
        defaultValue() {
            return 'toBeCounted';
        },
        async isFrozen() {
            return (await (await this.document).status) === 'closed';
        },
    })
    readonly status: Promise<xtremStock.enums.StockCountLineStatus>;

    @decorators.referenceProperty<StockCountLine, 'item'>({
        isPublished: true,
        isStored: true,
        dependsOn: ['document'],
        lookupAccess: true,
        filters: {
            control: {
                isStockManaged: true,
                isPhantom: false,
                // TODO: uncomment when is XT-12798 implemented
                // itemSites() {
                //     return { _atLeast: 1, site: this.document.stockSite };
                // },
            },
        },
        ignoreIsActive: true,
        node: () => xtremMasterData.nodes.Item,
        isFrozen: true,
    })
    readonly item: Reference<xtremMasterData.nodes.Item>;

    @decorators.referenceProperty<StockCountLine, 'location'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
        ignoreIsActive: true,
        lookupAccess: true,
        dependsOn: [{ document: ['stockSite'] }],
        // filters commented out because location lookup crashes with it when adding a stock count line
        // filters: {
        //     control: {
        //         async site() {
        //             return (await this.document).stockSite;
        //         },
        //     },
        // },
        node: () => xtremMasterData.nodes.Location,
        async isFrozen() {
            return (await this.location) !== null;
        },
    })
    readonly location: Reference<xtremMasterData.nodes.Location | null>;

    @decorators.referenceProperty<StockCountLine, 'zone'>({
        isStoredOutput: true,
        isPublished: true,
        isNullable: true,
        dependsOn: ['location'],
        lookupAccess: true,
        node: () => xtremMasterData.nodes.LocationZone,
        async defaultValue() {
            const location = await this.location;
            return location ? location.locationZone : null;
        },
        updatedValue: useDefaultValue,
    })
    readonly zone: Reference<xtremMasterData.nodes.LocationZone | null>;

    /**
     * lot that was existing before the stock count
     * If the lot is created by adding a line in the stock count, this property is null
     */
    @decorators.referenceProperty<StockCountLine, 'lot'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
        lookupAccess: true,
        filters: {
            lookup: {
                item() {
                    return this.item;
                },
            },
        },
        node: () => xtremStockData.nodes.Lot,
        async isFrozen() {
            return (await this.lot) !== null;
        },
    })
    readonly lot: Reference<xtremStockData.nodes.Lot | null>;

    @decorators.referenceProperty<StockCountLine, 'stockStatus'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isNullable: true,
        node: () => xtremStockData.nodes.StockStatus,
        async isFrozen() {
            return (await this.stockStatus) !== null;
        },
    })
    readonly stockStatus: Reference<xtremStockData.nodes.StockStatus | null>;

    @decorators.stringProperty<StockCountLine, 'owner'>({
        isStored: true,
        isPublished: true,
        dependsOn: ['document'],
        lookupAccess: true,
        dataType: () => xtremSystem.dataTypes.code,
        async defaultValue() {
            return (await (await this.document).stockSite).id;
        },
        async isFrozen() {
            // here we test stockStatus because this is the only stock property that is mandatory in all cases
            // when stock characteristics are entered on the line
            return (await this.stockStatus) !== null;
        },
    })
    readonly owner: Promise<string>;

    @decorators.referenceProperty<StockCountLine, 'stockUnit'>({
        isStoredOutput: true,
        isPublished: true,
        dependsOn: ['item'],
        lookupAccess: true,
        node: () => xtremMasterData.nodes.UnitOfMeasure,
        ignoreIsActive: true,
        async defaultValue() {
            const item = await this.item;
            if (!item) return null;

            return item.stockUnit;
        },
        updatedValue: useDefaultValue,
    })
    readonly stockUnit: Reference<xtremMasterData.nodes.UnitOfMeasure>;

    _canUpdateStockQuantity = false;

    @decorators.decimalProperty<StockCountLine, 'quantityInStockUnit'>({
        isPublished: true,
        isStored: true,
        dependsOn: [
            'status',
            'item',
            'site',
            'location',
            'lot',
            'stockStatus',
            'stockUnit',
            'owner',
            { document: ['status'] },
        ],
        lookupAccess: true,
        dataType: () => xtremMasterData.dataTypes.quantityInStockUnit,
        async isFrozen() {
            return !this._canUpdateStockQuantity || (await (await this.document).status) === 'closed';
        },
        async control(cx, val) {
            await cx.error.if(val).is.negative();
        },
        async updatedValue() {
            this._canUpdateStockQuantity = true;
            if (
                this._id > 0 &&
                (await (await this.$.old).status) === 'excluded' &&
                (await this.status) !== 'excluded'
            ) {
                return xtremStock.functions.stockCountLib.getStockQuantity(this.$.context, {
                    item: this.item,
                    site: this.site,
                    location: this.location,
                    lot: this.lot,
                    stockStatus: this.stockStatus,
                    stockUnit: this.stockUnit,
                    owner: this.owner,
                });
            }
            return this.quantityInStockUnit;
        },
    })
    readonly quantityInStockUnit: Promise<decimal>;

    @decorators.decimalProperty<StockCountLine, 'countedQuantityInStockUnit'>({
        isStored: true,
        isPublished: true,
        dependsOn: ['status'],
        lookupAccess: true,
        dataType: () => xtremMasterData.dataTypes.quantityInStockUnit,
        async control(cx, val) {
            await cx.error.if(val).is.negative();
            if (
                (await this.$.context.isServiceOptionEnabled(xtremMasterData.serviceOptions.serialNumberOption)) &&
                (await (await this.item).serialNumberManagement) === 'managed' &&
                (await (await this.item).serialNumberUsage) === 'issueAndReceipt'
            ) {
                await cx.error.if(val).is.greater.than(await this.quantityInStockUnit);
            }
        },
        async isFrozen() {
            return (await (await this.document).status) === 'closed';
        },
    })
    readonly countedQuantityInStockUnit: Promise<decimal>;

    @decorators.decimalProperty<StockCountLine, 'quantityVariance'>({
        isPublished: true,
        dependsOn: ['quantityInStockUnit', 'countedQuantityInStockUnit', 'status'],
        lookupAccess: true,
        dataType: () => xtremMasterData.dataTypes.quantityInStockUnit,
        async getValue() {
            return (await this.countedQuantityInStockUnit) > 0 || (await this.status) === 'counted'
                ? (await this.countedQuantityInStockUnit) - (await this.quantityInStockUnit)
                : 0;
        },
    })
    readonly quantityVariance: Promise<decimal>;

    @decorators.decimalProperty<StockCountLine, 'adjustmentQuantityInStockUnit'>({
        isPublished: true,
        dependsOn: ['quantityVariance'],
        lookupAccess: true,
        dataType: () => xtremMasterData.dataTypes.quantityInStockUnit,
        // eslint-disable-next-line @sage/xtrem/property-decorators-errors
        async computeValue() {
            return Math.abs(await this.quantityVariance);
        },
    })
    readonly adjustmentQuantityInStockUnit: Promise<decimal>;

    @decorators.decimalProperty<StockCountLine, 'quantityVariancePercentage'>({
        isPublished: true,
        lookupAccess: true,
        dependsOn: ['quantityInStockUnit', 'countedQuantityInStockUnit'],
        dataType: () => xtremMasterData.dataTypes.stockQuantityVariancePercentage,
        async getValue() {
            return (await this.quantityInStockUnit) !== 0
                ? ((await this.quantityVariance) * 100) / (await this.quantityInStockUnit)
                : 0;
        },
    })
    readonly quantityVariancePercentage: Promise<decimal>;

    @decorators.collectionProperty<StockCountLine, 'stockTransactions'>({
        isPublished: true,
        reverseReference: 'documentLine',
        isVital: true,
        lookupAccess: true,
        node: () => xtremStockData.nodes.StockTransaction,
        async isFrozen() {
            return (await (await this.document).status) === 'closed';
        },
    })
    readonly stockTransactions: Collection<xtremStockData.nodes.StockTransaction>;

    @decorators.collectionProperty<StockCountLine, 'stockMovements'>({
        isPublished: true,
        reverseReference: 'documentLine',
        node: () => xtremStockData.nodes.StockJournal,
        async isFrozen() {
            return (await (await this.document).status) === 'closed';
        },
    })
    readonly stockMovements: Collection<xtremStockData.nodes.StockJournal>;

    // the site property on line level is needed for the accounting interface
    @decorators.referenceProperty<StockCountLine, 'site'>({
        isPublished: true,
        node: () => xtremSystem.nodes.Site,
        async computeValue() {
            return (await this.document).getStockSite();
        },
    })
    readonly site: Reference<xtremSystem.nodes.Site>;

    // the site property on line level is needed for the accounting interface
    @decorators.referenceProperty<StockCountLine, 'itemSite'>({
        isPublished: true,
        node: () => xtremMasterData.nodes.ItemSite,

        join: {
            item() {
                return this.item;
            },
            site() {
                return this.site;
            },
        },
    })
    readonly itemSite: Reference<xtremMasterData.nodes.ItemSite>;

    @decorators.decimalProperty<StockCountLine, 'newLineOrderCost'>({
        isPublished: true,
        isStored: true,
        dependsOn: ['itemSite', 'quantityInStockUnit', 'status', { document: ['status'] }],
        isNullable: true,
        lookupAccess: true,
        dataType: () => xtremMasterData.dataTypes.costDataType,
        async control(cx, val) {
            await cx.error.if(val).is.negative();
        },
        async defaultValue() {
            let cost: decimal | null = null;
            if ((await this.quantityInStockUnit) === 0) {
                cost = await xtremStockData.functions.stockDocumentLib.defaultDocumentLineCost(this.$.context, this, {
                    valuationType: 'receipt',
                });
            }
            return cost;
        },
        async adaptValue(val) {
            if ((await this.status) === 'excluded') return null;
            return (await this.quantityInStockUnit) > 0 ? null : val;
        },
        async isFrozen() {
            return (await (await this.document).status) === 'closed';
        },
    })
    readonly newLineOrderCost: Promise<decimal | null>;

    /**
     * Indicates if the order cost can be entered for the line
     * It's only possible in the case where the line corresponds to a stock line that was not existing
     * and, of course, if the stock count has not been posted yet
     */
    @decorators.booleanProperty<StockCountLine, 'canEnterOrderCost'>({
        isPublished: true,
        lookupAccess: true,
        dependsOn: ['quantityInStockUnit', 'stockTransactionStatus', 'status', { document: ['status'] }],
        async getValue() {
            if ((await this.quantityInStockUnit) !== 0) return false;
            if ((await this.stockTransactionStatus) !== 'draft') return false;
            if ((await this.status) === 'excluded') return false;
            if (!['countInProgress', 'counted'].includes(await (await this.document).status)) return false;

            return true;
        },
    })
    readonly canEnterOrderCost: Promise<boolean>;

    @decorators.booleanProperty<StockCountLine, 'shouldEnterOrderCost'>({
        isPublished: true,
        lookupAccess: true,
        dependsOn: ['newLineOrderCost', 'canEnterOrderCost'],
        async getValue() {
            if (((await this.newLineOrderCost) ?? 0) > 0) return false;

            return this.canEnterOrderCost;
        },
    })
    readonly shouldEnterOrderCost: Promise<boolean>;

    @decorators.jsonProperty<StockCountLine, 'storedDimensions'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
        lookupAccess: true,
        dataType: () => xtremFinanceData.dataTypes.storedDimensionsDataType,
        dependsOn: ['item', { document: ['stockSite'] }],
        async defaultValue() {
            const site = await (await this.document).stockSite;
            return xtremFinanceData.functions.getDefaultDimensions(this.$.context, {
                dimensionDefinitionLevel: 'stockDirect',
                companyId: (await (await (await this.document).stockSite).legalCompany)._id,
                site,
                item: await this.item,
            });
        },
    })
    readonly storedDimensions: Promise<object | null>;

    @decorators.referenceProperty<StockCountLine, 'analyticalData'>({
        isStored: true,
        isMutable: true,
        isPublished: true,
        lookupAccess: true,
        ignoreIsActive: true,
        isFrozen() {
            return xtremFinanceData.functions.isAnalyticalDataFrozen(this);
        },
        node: () => xtremFinanceData.nodes.AnalyticalData,
        dependsOn: ['storedAttributes', 'storedDimensions'],
        async defaultValue() {
            return (await xtremFinanceData.functions.getAnalyticalDataFromStoredAttributesAndDimensions(
                this.$.context,
                await this.storedAttributes,
                await this.storedDimensions,
            )) as any;
        },
        updatedValue: useDefaultValue,
    })
    readonly analyticalData: Reference<xtremFinanceData.nodes.AnalyticalData>;

    @decorators.jsonProperty<StockCountLine, 'storedAttributes'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
        lookupAccess: true,
        dataType: () => xtremFinanceData.dataTypes.storedAttributesDataType,
        dependsOn: ['item', { document: ['stockSite'] }],
        async defaultValue() {
            const site = await (await this.document).stockSite;
            return xtremFinanceData.functions.getDefaultAttributes(this.$.context, {
                dimensionDefinitionLevel: 'stockDirect',
                companyId: (await (await (await this.document).stockSite).legalCompany)._id,
                site,
                item: await this.item,
            });
        },
    })
    readonly storedAttributes: Promise<xtremMasterData.interfaces.StoredAttributes | null>;

    @decorators.jsonProperty<StockCountLine, 'computedAttributes'>({
        isPublished: true,
        lookupAccess: true,
        dependsOn: ['item', { document: ['stockSite'] }],
        async computeValue() {
            return xtremStock.functions.analytical.computeAttributes(this.$.context, {
                item: await this.item,
                stockSite: await (await this.document).getStockSite(),
                financialSite: await xtremFinanceData.functions.getFinancialSite(await (await this.document).stockSite),
            });
        },
    })
    readonly computedAttributes: Promise<object>;

    @decorators.collectionProperty<StockCountLine, 'stockCountLineSerialNumbers'>({
        serviceOptions: () => [xtremMasterData.serviceOptions.serialNumberOption],
        isPublished: true,
        isVital: true,
        lookupAccess: true,
        node: () => xtremStock.nodes.StockCountLineSerialNumber,
        reverseReference: 'stockCountLine',
        async isFrozen() {
            return (await (await this.document).status) === 'closed';
        },
    })
    readonly stockCountLineSerialNumbers: Collection<xtremStock.nodes.StockCountLineSerialNumber>;

    /**
     * list of counted serial numbers in the form of ranges
     */
    @decorators.jsonProperty<StockCountLine, 'jsonSerialNumbers'>({
        isPublished: true,
        lookupAccess: true,
        serviceOptions: () => [xtremMasterData.serviceOptions.serialNumberOption],
        dependsOn: ['item', 'stockCountLineSerialNumbers', 'countedQuantityInStockUnit'],
        async setValue(jsonRanges) {
            const item = await this.item;
            if (!item) return; // happens when entering item in the page, the lookup query triggers this setValue
            if (
                !(
                    (await item.serialNumberManagement) === 'managed' &&
                    (await item.serialNumberUsage) === 'issueAndReceipt'
                )
            )
                return;

            const serialNumberRanges = jsonRanges?.ranges ?? [];

            if (serialNumberRanges.length > 1) {
                // Check that there is no overlapping between different ranges
                const rangesToCheck = [...serialNumberRanges].reverse(); // reverse the array to be able to use pop()
                const isRangeOverlapping = serialNumberRanges.some(range1 => {
                    rangesToCheck.pop();
                    const startingSerial1 = xtremStockData.sharedFunctions.serialNumberHelper.splitSerialNumber(
                        range1.startingSerialNumberId,
                    );
                    const endingSerial1 = xtremStockData.sharedFunctions.serialNumberHelper.splitSerialNumber(
                        range1.endingSerialNumberId,
                    );
                    return rangesToCheck.some(range2 => {
                        const startingSerial2 = xtremStockData.sharedFunctions.serialNumberHelper.splitSerialNumber(
                            range2.startingSerialNumberId,
                        );
                        const endingSerial2 = xtremStockData.sharedFunctions.serialNumberHelper.splitSerialNumber(
                            range2.endingSerialNumberId,
                        );
                        return (
                            // check if prefix and postfix are identical
                            startingSerial1.serialNumberPrefixStringPart ===
                                startingSerial2.serialNumberPrefixStringPart &&
                            endingSerial1.serialNumberPostfixStringPart ===
                                endingSerial2.serialNumberPostfixStringPart &&
                            // check for overlapping ranges
                            ((range1.numericStart <= range2.numericStart && range2.numericStart <= range1.numericEnd) ||
                                (range1.numericStart <= range2.numericEnd && range2.numericEnd <= range1.numericEnd) ||
                                (range2.numericStart <= range1.numericStart && range1.numericEnd <= range2.numericEnd))
                        );
                    });
                });
                if (isRangeOverlapping) {
                    throw new BusinessRuleError(
                        this.$.context.localize(
                            '@sage/xtrem-stock/nodes__stock_count_line___duplicated_serial_number',
                            'There are duplicate serial numbers. Make sure that each line has a unique set of serial numbers.',
                        ),
                    );
                }
            }

            // Check that the quantity of selected serial numbers does not exceed the quantity counted
            const selectedQuantity = serialNumberRanges.reduce((sum, range) => sum + range.quantity, 0);
            if (selectedQuantity > (await this.countedQuantityInStockUnit)) {
                throw new BusinessRuleError(
                    this.$.context.localize(
                        '@sage/xtrem-stock/nodes__stock_count_line__selected_quantity_too_high',
                        'The number of selected serial numbers ({{selectedQuantity}}) cannot exceed the counted quantity ({{countedQuantity}}).',
                        { countedQuantity: await this.countedQuantityInStockUnit, selectedQuantity },
                    ),
                );
            }

            // For each serialNumbers, we check that it is included in one of ranges to set it as 'counted'
            await this.stockCountLineSerialNumbers.forEach(async serialNumber => {
                const serialNumberId = await (await serialNumber.serialNumber).id;
                const isCounted = serialNumberRanges.some(range => {
                    return (
                        serialNumberId.localeCompare(range.startingSerialNumberId) >= 0 &&
                        serialNumberId.localeCompare(range.endingSerialNumberId) <= 0
                    );
                });

                await serialNumber.$.set({ isCounted });
            });
        },
        async computeValue() {
            const countedSerialNumbers: Array<string> = await this.stockCountLineSerialNumbers
                .filter(serialNumber => serialNumber.isCounted)
                .map(async serialNumber => (await serialNumber.serialNumber).id)
                .toArray();
            if (countedSerialNumbers.length > 0) {
                const serialNumberRangesFromIds: {
                    originalStartId: string;
                    numericStart: integer;
                    numericEnd: integer;
                    quantity: integer;
                    endId: string;
                }[] =
                    xtremStockData.sharedFunctions.serialNumberHelper.getSerialNumberRangesFromIds(
                        countedSerialNumbers,
                    );
                const res = await asyncArray(serialNumberRangesFromIds)
                    .map(range => ({
                        // don't spread range to return only expected properties
                        startingSerialNumberId: range.originalStartId,
                        quantity: range.quantity,
                        endingSerialNumberId: range.endId,
                        numericStart: range.numericStart,
                        numericEnd: range.numericEnd,
                        originalStartId: range.originalStartId,
                    }))
                    .toArray();
                return { ranges: res };
            }
            // forcing typing to accept an empty object
            return {} as any;
        },
    })
    readonly jsonSerialNumbers: Promise<{
        ranges: Array<xtremStockData.interfaces.SerialNumberIdNumericRange>;
    }>;

    @decorators.decimalProperty<StockCountLine, 'countedSerialNumber'>({
        serviceOptions: () => [xtremMasterData.serviceOptions.serialNumberOption],
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.quantityInSalesUnit,
        lookupAccess: true,
        getValue() {
            return this.stockCountLineSerialNumbers.sum(async serialNumber => ((await serialNumber.isCounted) ? 1 : 0));
        },
    })
    readonly countedSerialNumber: Promise<decimal>;

    @decorators.decimalProperty<StockCountLine, 'countedSerialNumberPercentage'>({
        serviceOptions: () => [xtremMasterData.serviceOptions.serialNumberOption],
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.capacityPercentage,
        lookupAccess: true,
        async getValue() {
            const qty = await this.countedQuantityInStockUnit;

            return qty > 0 ? ((await this.countedSerialNumber) * 100) / qty : 0;
        },
    })
    readonly countedSerialNumberPercentage: Promise<decimal>;

    @decorators.collectionProperty<StockCountLine, 'stockDetails'>({
        isVital: true,
        reverseReference: 'documentLine',
        dependsOn: ['newLineOrderCost', 'quantityInStockUnit', 'countedQuantityInStockUnit'],
        node: () => xtremStockData.nodes.StockAdjustmentDetail,
    })
    readonly stockDetails: Collection<xtremStockData.nodes.StockAdjustmentDetail>;

    @decorators.jsonProperty<StockCountLine, 'jsonStockDetails'>({
        isPublished: true,
        dependsOn: ['stockDetails'],
        async setValue(details) {
            if (details && details.length > 0) {
                const stockDetails = xtremStockData.functions.stockDetailLib.parseDetails(
                    details,
                ) as NodeCreateData<xtremStockData.nodes.StockAdjustmentDetail>[];

                // if the documentLine is set, it can raise an error because this property is frozen
                delete stockDetails[0].documentLine;

                await this.$.set({
                    stockDetails,
                });
            }
        },
        async computeValue() {
            if ((await this.stockDetails.length) > 0) {
                return this.stockDetails
                    .map(async detail => {
                        return {
                            _id: detail._id,
                            ...(await xtremStockData.functions.stockDetailLib.filterStockDetailProperties(
                                detail,
                                xtremStockData.nodes.StockAdjustmentDetail,
                                {
                                    onlyIds: true,
                                },
                            )),
                        };
                    })
                    .toArray();
            }
            // forcing typing to accept an empty object
            return {} as any;
        },
    })
    readonly jsonStockDetails: Promise<Array<JsonType<NodeCreateData<xtremStockData.nodes.StockAdjustmentDetail>>>>;

    // the computed stockDetail property gives the 1st element of the vital stockDetails collection
    // the stockDetails collection always has only 1 element. It was designed as a vital collection for the ability to
    // re-use many functions created for other stock functions
    // this computed property is needed for paging on the stock count function
    @decorators.referenceProperty<StockCountLine, 'stockDetail'>({
        isPublished: true,
        lookupAccess: true,
        node: () => xtremStockData.nodes.StockAdjustmentDetail,
        isNullable: true,
        dependsOn: ['stockDetails'],
        getValue() {
            return this.stockDetails.takeOne(stockDetail => stockDetail._id != null);
        },
    })
    readonly stockDetail: Reference<xtremStockData.nodes.StockAdjustmentDetail | null>;

    @decorators.booleanProperty<StockCountLine, 'hasAllocationError'>({
        isPublished: true,
        lookupAccess: true,
        async getValue() {
            if ((await this.status) === 'excluded') return false;
            return (await this.totalAllocated) > (await this.countedQuantityInStockUnit);
        },
    })
    readonly hasAllocationError: Promise<boolean>;

    @decorators.collectionProperty<StockCountLine, 'allocations'>({
        isPublished: true,
        lookupAccess: true,
        node: () => xtremStockData.nodes.StockAllocation,
        reverseReference: 'stockRecord',
    })
    readonly allocations: Collection<xtremStockData.nodes.StockAllocation>;

    @decorators.booleanProperty<StockCountLine, 'canBeDeleted'>({
        isPublished: true,
        lookupAccess: true,
        dependsOn: ['stockTransactionStatus', 'isAddedDuringCount'],
        async computeValue() {
            return !(await xtremStock.functions.stockCountLib.checkStockCountLineDeletion(this.$.context, {
                stockCountLine: this,
            }));
        },
    })
    readonly canBeDeleted: Promise<boolean>;

    @decorators.referenceProperty<StockCountLine, 'stockRecord'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
        node: () => xtremStockData.nodes.Stock,
        filters: {
            control: {
                site() {
                    return this.site;
                },
                item() {
                    return this.item;
                },
            },
        },
    })
    readonly stockRecord: Reference<xtremStockData.nodes.Stock | null>;

    @decorators.decimalProperty<StockCountLine, 'totalAllocated'>({
        isPublished: true,
        lookupAccess: true,
        dataType: () => xtremMasterData.dataTypes.quantityInStockUnit,
        async getValue() {
            const sr = await this.stockRecord;
            if (!sr) return 0;
            return sr.totalAllocated;
        },
    })
    readonly totalAllocated: Promise<decimal>;

    @decorators.booleanProperty<StockCountLine, 'isStockCountCreation'>({
        isTransientInput: true,
        defaultValue: false,
        isPublished: true,
    })
    readonly isStockCountCreation: Promise<boolean>;

    // Used to manage creation of stock count line of non stock item.
    // Used on page to manage the flow of non stock item vs stock item
    // Value is updated on page once stock details have been entered and record set to counted
    @decorators.booleanProperty<StockCountLine, 'isNonStockItem'>({
        isStoredOutput: true,
        isPublished: true,
        dependsOn: [
            'isAddedDuringCount',
            'stockStatus',
            'location',
            'lot',
            'quantityVariance',
            { stockDetail: ['stockDetailLot'] },
        ],
        async defaultValue() {
            // When a line with isNonStockItem=true gets isNonStockItem=false, the stockDetail must be created
            // if one of the stock properties is set or if the quantityVariance differs from 0
            if (await this.isAddedDuringCount) {
                return false;
            }
            if (await this.stockStatus) {
                return false;
            }
            if (await this.location) {
                return false;
            }
            if ((await this.quantityVariance) !== 0) {
                return false;
            }
            if (await this.lot) {
                return false;
            }
            if (await (await (await this.stockDetail)?.stockDetailLot)?.lotNumber) {
                return false;
            }

            return true;
        },
        updatedValue: useDefaultValue,
    })
    readonly isNonStockItem: Promise<boolean>;

    @decorators.booleanProperty<StockCountLine, 'isCreatedFromStockRecord'>({
        isStored: true,
        defaultValue: false,
        isPublished: true,
    })
    readonly isCreatedFromStockRecord: Promise<boolean>;

    /**
     * Indicates if, from a stock count point of view, the line must update the stock
     * @param line stock count line to check
     * @returns true if this line should be considered in the stock update
     */
    static async isLineUpdatingStock(line: xtremStock.nodes.StockCountLine): Promise<boolean> {
        return (await line.status) !== 'excluded' && (await line.quantityVariance) !== 0;
    }

    @decorators.query<typeof StockCountLine, 'getStockQuantity'>({
        isPublished: true,
        parameters: [
            {
                isMandatory: true,
                name: 'stockCountLine',
                type: 'reference',
                node: () => xtremStock.nodes.StockCountLine,
            },
        ],
        return: 'decimal',
    })
    static getStockQuantity(context: Context, stockCountLine: xtremStock.nodes.StockCountLine): Promise<decimal> {
        return xtremStock.functions.stockCountLib.getStockQuantity(context, {
            item: stockCountLine.item,
            site: stockCountLine.site,
            location: stockCountLine.location,
            lot: stockCountLine.lot,
            stockStatus: stockCountLine.stockStatus,
            stockUnit: stockCountLine.stockUnit,
            owner: stockCountLine.owner,
        });
    }

    async createStockCountLineSerialNumbers() {
        // When a line is created, we have to look for the corresponding serial numbers
        // and create stockCountSerialNumbers links
        if (
            this.$.status !== NodeStatus.added ||
            !(await this.$.context.isServiceOptionEnabled(xtremMasterData.serviceOptions.serialNumberOption))
        )
            return;
        const item = await this.item;
        if ((await item.serialNumberManagement) !== 'managed' || (await item.serialNumberUsage) !== 'issueAndReceipt') {
            return;
        }

        const lot = (await this.stockDetails.length)
            ? await (
                  await (
                      await this.stockDetails.at(0)
                  )?.stockDetailLot
              )?.lot
            : null;

        const stockRecord = await xtremStockData.functions.stockLib.getStockRecord(
            this.$.context,
            {
                item: await this.item,
                site: await this.site,
                location: await this.location,
                lot: lot || null,
                status: await this.stockStatus,
                stockUnit: await this.stockUnit,
                owner: await this.owner,
            },
            false,
        );

        if (!stockRecord) {
            // This case is not possible because a line cannot be added on an existing count
            // if the item is managed with serial number (see controlBegin)
            // => a line for an item managed with serial number can only be added automatically
            // with criteria => the stock line exists
            return;
        }
        const serialNumbers = this.$.context.query(xtremStockData.nodes.SerialNumber, {
            filter: { stockRecord },
            orderBy: { id: +1 },
        });
        // get the serial numbers already set in the stock count line (could have been set by import)
        const stockCountLineSerialNumbers = await this.stockCountLineSerialNumbers
            .map(async stockCountLineSerialNumber => (await stockCountLineSerialNumber.serialNumber).id)
            .toArray();
        // compare the serial numbers in the stock record with the serial numbers in the stock count line
        // and add only the missing serial numbers
        await serialNumbers
            .filter(async serialNumber => !stockCountLineSerialNumbers.includes(await serialNumber.id))
            .forEach(async serialNumber => {
                const detail = {
                    serialNumber,
                    isCounted: false,
                } as NodeCreateData<xtremStock.nodes.StockCountLineSerialNumber>;

                delete detail._id;
                await this.stockCountLineSerialNumbers.append(detail);
            });
    }

    async isLineCharacteristicFrozen(): Promise<boolean> {
        return (await (await this.document).status) === 'closed';
    }
}
