import type { NotificationEnvelope } from '@sage/xtrem-communication';
import type { Collection, Context, Reference, decimal, integer } from '@sage/xtrem-core';
import { BusinessRuleError, Logger, Node, asyncArray, date, decorators } from '@sage/xtrem-core';
import * as xtremFinanceData from '@sage/xtrem-finance-data';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremStockData from '@sage/xtrem-stock-data';
import * as xtremSystem from '@sage/xtrem-system';
import * as xtremStock from '..';
import * as stockValueChangeLib from '../functions/stock-value-change-lib';

const logger = Logger.getLogger(__filename, 'stock-value-change');

@decorators.node<StockValueChange>({
    isClearedByReset: true,
    storage: 'sql',
    isPublished: true,
    canRead: true,
    canCreate: true,
    canSearch: true,
    canUpdate: true,
    canDelete: true,
    hasAttachments: true,
    indexes: [{ orderBy: { number: +1 }, isUnique: true, isNaturalKey: true }],
    notifies: ['created', 'updated', 'deleted'],
    async controlEnd(cx) {
        await cx.error
            .withMessage(
                '@sage/xtrem-stock/nodes__stock_value_change__lines_mandatory',
                'The stock value change must contain at least one line.',
            )
            .if(await this.lines.length)
            .is.zero();
    },
})
@xtremStockData.functions.staticImplements<xtremStockData.interfaces.StaticDocumentHeaderWithStockPosting>()
export class StockValueChange extends Node {
    getEffectiveDate(): Promise<date> {
        return this.effectiveDate;
    }

    getStockSite(): Promise<xtremSystem.nodes.Site> {
        return this.site;
    }

    @decorators.dateProperty<StockValueChange, 'effectiveDate'>({
        isStored: true,
        isPublished: true,
        isRequired: true,
        provides: ['documentDate'],
        defaultValue() {
            return date.today();
        },
        async isFrozen() {
            return (await this.stockTransactionStatus) === 'completed';
        },
    })
    readonly effectiveDate: Promise<date>;

    @decorators.stringProperty<StockValueChange, 'number'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.documentNumber,
        defaultValue() {
            return xtremMasterData.functions.getDocumentNumberPlaceholder();
        },
        deferredDefaultValue() {
            return xtremMasterData.functions.allocateDocumentNumber(this);
        },
        provides: ['sequenceNumber'],
    })
    readonly number: Promise<string>;

    @decorators.referenceProperty<StockValueChange, 'site'>({
        isPublished: true,
        isStored: true,
        isRequired: true,
        node: () => xtremSystem.nodes.Site,
        provides: ['site'],
    })
    readonly site: Reference<xtremSystem.nodes.Site>;

    @decorators.referenceProperty<StockValueChange, 'item'>({
        isStored: true,
        isPublished: true,
        isRequired: true,
        node: () => xtremMasterData.nodes.Item,
    })
    readonly item: Reference<xtremMasterData.nodes.Item>;

    @decorators.referenceProperty<StockValueChange, 'itemSite'>({
        isPublished: false,
        isNullable: true,
        node: () => xtremMasterData.nodes.ItemSite,
        join: {
            item() {
                return this.item;
            },
            site() {
                return this.site;
            },
        },
        filters: { control: { valuationMethod: { _ne: 'standardCost' } } },
    })
    readonly itemSite: Reference<xtremMasterData.nodes.ItemSite | null>;

    @decorators.stringProperty<StockValueChange, 'description'>({
        isPublished: true,
        isStored: true,
        dataType: () => xtremSystem.dataTypes.description,
    })
    readonly description: Promise<string>;

    @decorators.enumProperty<StockValueChange, 'valuationMethod'>({
        isPublished: true,
        isNullable: true,
        dataType: () => xtremMasterData.enums.costValuationMethodDataType,
        async getValue() {
            return (await this.itemSite)?.valuationMethod ?? null;
        },
    })
    readonly valuationMethod: Promise<xtremMasterData.enums.CostValuationMethod | null>;

    @decorators.dateProperty<StockValueChange, 'postedDate'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
    })
    readonly postedDate: Promise<date | null>;

    @decorators.booleanProperty<StockValueChange, 'forceUpdateForStock'>({
        defaultValue: false,
    })
    readonly forceUpdateForStock: Promise<boolean>;

    @decorators.dateProperty<StockValueChange, 'documentDate'>({
        isPublished: true,
        dependsOn: ['effectiveDate'],
        getValue() {
            return this.effectiveDate;
        },
    })
    readonly documentDate: Promise<date>;

    @decorators.enumProperty<StockValueChange, 'stockTransactionStatus'>({
        isPublished: true,
        dataType: () => xtremStockData.enums.StockDocumentTransactionStatusDataType,
        computeValue() {
            return this.computeHeaderStatus();
        },
    })
    readonly stockTransactionStatus: Promise<xtremStockData.enums.StockDocumentTransactionStatus>;

    async computeHeaderStatus(): Promise<xtremStockData.enums.StockDocumentTransactionStatus> {
        // If the document contains no line return 'draft'
        if (!(await this.lines.length)) return 'draft';

        const statusArray = await this.lines.map(line => line.stockTransactionStatus).toArray();

        if (statusArray.includes('error')) return 'error';
        if (statusArray.includes('draft')) return 'draft';
        if (statusArray.includes('inProgress')) return 'inProgress';
        return 'completed';
    }

    @decorators.decimalProperty<StockValueChange, 'totalQuantityInStock'>({
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.quantity,
        getValue() {
            return this.lines.sum(line => line.quantity);
        },
    })
    readonly totalQuantityInStock: Promise<number>;

    @decorators.decimalProperty<StockValueChange, 'totalValueOfStock'>({
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.costDataType,
        async computeValue() {
            switch (await this.valuationMethod) {
                case 'averageCost':
                    return (await this.itemSite)?.stockValuationAtAverageCost || 0;
                case 'fifoCost': {
                    const itemSite = await this.itemSite;

                    return itemSite
                        ? (
                              await xtremStockData.nodeExtensions.ItemSiteExtension.getItemSiteValuationCost(
                                  this.$.context,
                                  itemSite,
                                  {
                                      quantity: await this.totalQuantityInStock,
                                      fifoCriteria: { dateOfValuation: date.today(), valuationType: 'change' },
                                  },
                              )
                          ).amount
                        : 0;
                }
                default: // Should never happen, of course, but Linter and SonarCloud would complain otherwise.
                    return 0;
            }
        },
    })
    readonly totalValueOfStock: Promise<decimal>;

    @decorators.decimalProperty<StockValueChange, 'unitCost'>({
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.costDataType,
        async computeValue() {
            switch (await this.valuationMethod) {
                case 'averageCost': {
                    const totalQuantityInStock = await this.totalQuantityInStock;

                    return totalQuantityInStock ? (await this.totalValueOfStock) / totalQuantityInStock : 0;
                }
                case 'fifoCost': {
                    const itemSite = await this.itemSite;

                    return itemSite
                        ? (
                              await xtremStockData.nodeExtensions.ItemSiteExtension.getItemSiteValuationCost(
                                  this.$.context,
                                  itemSite,
                                  {
                                      quantity: 1,
                                      fifoCriteria: { dateOfValuation: date.today(), valuationType: 'change' },
                                  },
                              )
                          ).unitCost
                        : 0;
                }
                default: // Should never happen, of course, but Linter and SonarCloud would complain otherwise.
                    return 0;
            }
        },
    })
    readonly unitCost: Promise<decimal>;

    @decorators.collectionProperty<StockValueChange, 'lines'>({
        isPublished: true,
        isVital: true,
        isRequired: true,
        reverseReference: 'document',
        node: () => xtremStock.nodes.StockValueChangeLine,
    })
    readonly lines: Collection<xtremStock.nodes.StockValueChangeLine>;

    @decorators.collectionProperty<StockValueChange, 'postingDetails'>({
        isPublished: true,
        join: {
            documentSysId() {
                return this._id;
            },
            documentType() {
                return 'stockValueChange';
            },
        },
        node: () => xtremFinanceData.nodes.FinanceTransaction,
        lookupAccess: true,
    })
    readonly postingDetails: Collection<xtremFinanceData.nodes.FinanceTransaction>;

    readonly linesWithStockTransactionStatusCollectionName = 'lines';

    // property needed for the accounting interface
    @decorators.referenceProperty<StockValueChange, 'financialSite'>({
        isPublished: true,
        lookupAccess: true,
        node: () => xtremSystem.nodes.Site,
        async computeValue() {
            return xtremFinanceData.functions.getFinancialSite(await this.getStockSite());
        },
    })
    readonly financialSite: Reference<xtremSystem.nodes.Site>;

    // property needed for the accounting interface
    @decorators.referenceProperty<StockValueChange, 'transactionCurrency'>({
        isPublished: true,
        lookupAccess: true,
        dependsOn: ['financialSite'],
        node: () => xtremMasterData.nodes.Currency,
        // eslint-disable-next-line @sage/xtrem/property-decorators-errors
        async computeValue() {
            return (await (await this.financialSite).legalCompany).currency;
        },
    })
    readonly transactionCurrency: Reference<xtremMasterData.nodes.Currency>;

    @decorators.enumProperty<StockValueChange, 'financeIntegrationStatus'>({
        isPublished: true,
        lookupAccess: true,
        dataType: () => xtremFinanceData.enums.financeIntegrationStatusDataType,
        async computeValue() {
            return xtremFinanceData.functions.getDocumentIntegrationStatus(
                this.$.context,
                'stockValueChange',
                await this.number,
            );
        },
    })
    readonly financeIntegrationStatus: Promise<xtremFinanceData.enums.FinanceIntegrationStatus>;

    @decorators.mutation<typeof StockValueChange, 'postToStock'>({
        isPublished: true,
        parameters: [
            {
                name: 'documentIds',
                type: 'array',
                item: {
                    type: 'integer',
                },
            },
        ],
        return: { type: 'string' },
    })
    static async postToStock(context: Context, documentIds: integer[]): Promise<string> {
        await asyncArray(documentIds).forEach(async documentID => {
            await stockValueChangeLib.controlPost(context, await context.read(StockValueChange, { _id: documentID }));
        });
        return xtremStockData.functions.notificationLib.stockValueChangeRequestNotification(context, {
            documentClass: StockValueChange,
            documentIds,
        });
    }

    // Repost creates an update notification to the acc engine ir order to update attributes and dimenions.
    // In fact, the existing finance document headers are preserved while the lines are all rebuilt (old ones deleted and new ones calculated)
    @decorators.mutation<typeof StockValueChange, 'repost'>({
        isPublished: true,

        parameters: [
            {
                name: 'stockValueChange',
                type: 'reference',
                isMandatory: true,
                isWritable: true,
                node: () => StockValueChange,
            },
            {
                name: 'documentLines',
                type: 'array',
                item: {
                    type: 'object',
                    properties: {
                        baseDocumentLineSysId: 'integer',
                        storedAttributes: 'json',
                        storedDimensions: 'json',
                    },
                },
            },
        ],
        return: { type: 'object', properties: { wasSuccessful: 'boolean', message: 'string' } },
    })
    static async repost(
        context: Context,
        stockValueChange: StockValueChange,
        documentLines: xtremFinanceData.interfaces.FinanceIntegrationDocumentLineUpdate[],
    ): Promise<xtremFinanceData.interfaces.MutationResult> {
        if (!xtremFinanceData.functions.canRepost((await stockValueChange.financeIntegrationStatus) || 'submitted')) {
            // submitted used in case of undefined to assure that function will return false
            throw new BusinessRuleError(
                context.localize(
                    '@sage/xtrem-stock/nodes__stock_value_change__cant_repost_stock_value_change_when_status_is_not_failed',
                    "You can only repost a stock value change if the status is 'Failed'.",
                ),
            );
        }

        const updateLines = documentLines.map(documentLine => {
            return {
                _id: documentLine.baseDocumentLineSysId,
                storedAttributes: documentLine.storedAttributes,
                storedDimensions: documentLine.storedDimensions,
            };
        });
        await stockValueChange.$.set({ lines: updateLines });
        await stockValueChange.$.save();

        // send notification in order to update a staging table entry for the accounting engine
        if (await (await (await stockValueChange.site).legalCompany).doStockPosting) {
            await xtremFinanceData.functions.financeDocumentUpdateNotification(context, {
                document: stockValueChange,
                lines: await stockValueChange.lines.toArray(),
                documentType: 'stockValueChange',
                replyTopic: 'StockValueChangeUpdate/accountingInterface',
                doNotPostOnUpdate: false,
            });
            return {
                wasSuccessful: true,
                message: context.localize(
                    '@sage/xtrem-stock/nodes__stock_value_change__document_was_posted',
                    'The stock value change was posted.',
                ),
            };
        }
        return {
            wasSuccessful: true,
            message: context.localize(
                '@sage/xtrem-stock/nodes__stock_value_change__company_has_no_stock_posting',
                'No stock has been posted to this company.',
            ),
        };
    }

    @decorators.notificationListener<typeof StockValueChange>({
        startsReadOnly: true,
        topic: 'StockValueChange/stock/valueChange/reply',
        onError(
            context: Context,
            envelope: NotificationEnvelope<
                xtremStockData.interfaces.StockReplyPayload<xtremStockData.enums.StockMovementTypeEnum.valueChange>
            >,
            error: Error,
        ) {
            return xtremStockData.functions.notificationLib.errorHandler(context, envelope, error, StockValueChange);
        },
    })
    static async onStockReply(
        readOnlyContext: Context,
        payload: xtremStockData.interfaces.StockReplyPayload<xtremStockData.enums.StockMovementTypeEnum.valueChange>,
    ): Promise<void> {
        const valueChangeUpdateResult = await readOnlyContext.runInWritableContext(
            async writableContext =>
                (
                    await xtremStockData.functions.notificationLib.reactToStockMovementReply(
                        writableContext,
                        payload,
                        StockValueChange,
                    )
                ).valueChange,
        );

        if (!valueChangeUpdateResult) return;

        if (!valueChangeUpdateResult.transactionHadAnError) {
            await valueChangeUpdateResult.documents.forEach(async document => {
                if (document.isStockDocumentCompleted) {
                    await readOnlyContext.runInWritableContext(async writableContext => {
                        const stockReceipt = await writableContext.read(
                            StockValueChange,
                            { _id: document.id },
                            { forUpdate: true },
                        );
                        await StockValueChange.onceStockCompleted(writableContext, stockReceipt);
                    });
                }
            });
        }
    }

    static async onceStockCompleted(writableContext: Context, stockValueChange: StockValueChange): Promise<void> {
        await stockValueChange.$.set({
            postedDate: date.today(),
        });
        await stockValueChange.$.save();

        if (await (await (await stockValueChange.site).legalCompany).doStockPosting) {
            await xtremFinanceData.functions.stockValueChangeNotification(
                writableContext,
                stockValueChange as xtremFinanceData.interfaces.FinanceOriginDocument,
                (await stockValueChange.lines.toArray()) as xtremFinanceData.interfaces.FinanceOriginDocumentLine[],
            );
        }
    }

    @decorators.notificationListener<typeof StockValueChange>({ topic: 'StockValueChange/accountingInterface' })
    static async onFinanceIntegrationReply(
        context: Context,
        payload: xtremFinanceData.interfaces.FinanceTransactionData,
    ): Promise<void> {
        await xtremFinanceData.functions.reactToFinanceIntegrationReply(context, payload);
    }

    @decorators.mutation<typeof StockValueChange, 'financeIntegrationCheck'>({
        isPublished: true,

        parameters: [
            {
                name: 'valueChange',
                type: 'reference',
                isMandatory: true,
                isWritable: true,
                node: () => StockValueChange,
            },
        ],
        return: { type: 'object', properties: { wasSuccessful: 'boolean', message: 'string' } },
    })
    static financeIntegrationCheck(
        context: Context,
        valueChange: StockValueChange,
    ): Promise<xtremFinanceData.interfaces.MutationResult> {
        return xtremStock.functions.financeIntegration.stockValueChangeControlFromNotificationPayloadErrors(
            context,
            valueChange,
        );
    }

    @decorators.mutation<typeof StockValueChange, 'resendNotificationForFinance'>({
        isPublished: true,
        parameters: [{ name: 'stockValueChange', type: 'reference', isMandatory: true, node: () => StockValueChange }],
        return: {
            type: 'boolean',
        },
    })
    static async resendNotificationForFinance(context: Context, stockValueChange: StockValueChange): Promise<boolean> {
        logger.info(
            context.localize(
                '@sage/xtrem-stock/node__stock_value_change__resend_notification_for_finance',
                'Resending finance notification for stock value change: {{stockValueChange}}',
                { stockValueChange: await stockValueChange.number },
            ),
        );

        if (
            await xtremFinanceData.functions.AccountingEngineCommon.validateResendNotificationForFinance(context, {
                documentNumber: await stockValueChange.number,
                documentType: 'stockValueChange',
            })
        ) {
            // send notification in order to create a staging table entry for the accounting engine
            if (await (await (await stockValueChange.site).legalCompany).doStockPosting) {
                await xtremFinanceData.functions.stockValueChangeNotification(
                    context,
                    stockValueChange as xtremFinanceData.interfaces.FinanceOriginDocument,
                    (await stockValueChange.lines.toArray()) as xtremFinanceData.interfaces.FinanceOriginDocumentLine[],
                );
            }
        }

        return true;
    }

    @decorators.notificationListener<typeof StockValueChange>({
        topic: 'StockValueChange/resendNotificationForFinance',
    })
    static async onResendNotificationForFinance(context: Context, document: { number: number }) {
        const stockValueChange = await context.read(StockValueChange, { number: document.number });

        await StockValueChange.resendNotificationForFinance(context, stockValueChange);
    }
}
