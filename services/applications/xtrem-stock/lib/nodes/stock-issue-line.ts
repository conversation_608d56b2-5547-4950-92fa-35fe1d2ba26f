import type { Collection, Context, decimal, integer, JsonType, NodeCreateData, Reference } from '@sage/xtrem-core';
import { decorators, useDefaultValue } from '@sage/xtrem-core';
import * as xtremFinanceData from '@sage/xtrem-finance-data';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremStockData from '@sage/xtrem-stock-data';
import * as xtremSystem from '@sage/xtrem-system';
import * as xtremStock from '..';

@decorators.subNode<StockIssueLine>({
    isPublished: true,
    canRead: true,
    canSearch: true,
    isVitalCollectionChild: true,
    extends: () => xtremMasterData.nodes.BaseDocumentLine,

    async saveBegin() {
        await this.$.set({
            stockDetailStatus: await xtremStockData.functions.stockDetailLib.getStockDetailStatus({
                line: this,
                quantityExpected: await this.quantityInStockUnit,
                checkSerialNumbers: await this.checkSerialNumbers(),
            }),
            displayStatus: xtremStock.functions.stockIssueLib.calculateStockIssueLineDisplayStatus(
                await this.stockDetailStatus,
                await this.stockTransactionStatus,
            ),
        });
    },
    async controlEnd(cx) {
        await xtremFinanceData.functions.attributTypeRestrictedToCheck(cx, this.$.context, await this.storedAttributes);
    },
})
@xtremStockData.functions.staticImplements<xtremStockData.interfaces.StaticDocumentLineWithStockPosting>()
export class StockIssueLine
    extends xtremMasterData.nodes.BaseDocumentLine
    implements xtremFinanceData.interfaces.FinanceOriginDocumentLine
{
    getOrderCost(): Promise<decimal> {
        return xtremStockData.functions.stockDocumentLib.defaultDocumentLineCost(this.$.context, this, {
            valuationType: 'issue',
        });
    }

    getValuedCost(): Promise<decimal> {
        return this.getOrderCost();
    }

    // eslint-disable-next-line class-methods-use-this
    getValuationParameters(): xtremStockData.interfaces.ValuationParameter {
        return { valuationType: 'issue' };
    }

    getItem(): Promise<xtremMasterData.nodes.Item> {
        return this.item;
    }

    async checkSerialNumbers(): Promise<boolean> {
        if (await this.$.context.isServiceOptionEnabled(xtremMasterData.serviceOptions.serialNumberOption)) {
            return (
                (await (await this.item).serialNumberManagement) === 'managed' &&
                (await (await this.item).serialNumberUsage) === 'issueAndReceipt'
            );
        }
        return false;
    }

    @decorators.referenceProperty<StockIssueLine, 'document'>({
        isPublished: true,
        isStored: true,
        isVitalParent: true,
        lookupAccess: true,
        node: () => xtremStock.nodes.StockIssue,
    })
    override readonly document: Reference<xtremStock.nodes.StockIssue>;

    @decorators.stringPropertyOverride<StockIssueLine, 'documentNumber'>({
        async getValue() {
            return (await this.document).number;
        },
    })
    override readonly documentNumber: Promise<string>;

    @decorators.integerPropertyOverride<StockIssueLine, 'documentId'>({
        async getValue() {
            return (await this.document)._id;
        },
    })
    override readonly documentId: Promise<integer>;

    @decorators.enumProperty<StockIssueLine, 'stockTransactionStatus'>({
        isStored: true,
        isPublished: true,
        defaultValue() {
            return 'draft';
        },
        dataType: () => xtremStockData.enums.StockDocumentTransactionStatusDataType,
        lookupAccess: true,
    })
    readonly stockTransactionStatus: Promise<xtremStockData.enums.StockDocumentTransactionStatus>;

    @decorators.referenceProperty<StockIssueLine, 'itemSite'>({
        isPublished: true,
        isNullable: true,
        dependsOn: ['item', { document: ['stockSite'] }],
        node: () => xtremMasterData.nodes.ItemSite,
        join: {
            item() {
                return this.item;
            },
            async site() {
                return (await this.document).stockSite;
            },
        },
        lookupAccess: true,
    })
    readonly itemSite: Reference<xtremMasterData.nodes.ItemSite | null>;

    @decorators.referenceProperty<StockIssueLine, 'item'>({
        isPublished: true,
        isStored: true,
        dependsOn: [{ document: ['stockSite'] }],
        filters: {
            control: {
                isStockManaged: true,
                // TODO: uncomment when is XT-12798 implemented
                // itemSites: {
                //     _atLeast: 1,
                //     site() {
                //         return this.document.stockSite;
                //     },
                // },
            },
        },
        node: () => xtremMasterData.nodes.Item,
        lookupAccess: true,
    })
    readonly item: Reference<xtremMasterData.nodes.Item>;

    @decorators.referenceProperty<StockIssueLine, 'stockUnit'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
        dependsOn: ['item'],
        filters: {
            control: {
                async type() {
                    return (await (await this.item).stockUnit).type;
                },
            },
        },
        node: () => xtremMasterData.nodes.UnitOfMeasure,
        async defaultValue() {
            return (await this.item).stockUnit;
        },
        lookupAccess: true,
    })
    readonly stockUnit: Reference<xtremMasterData.nodes.UnitOfMeasure | null>;

    @decorators.decimalProperty<StockIssueLine, 'quantityInStockUnit'>({
        isPublished: true,
        isStored: true,
        dataType: () => xtremMasterData.dataTypes.quantityInStockUnit,
        lookupAccess: true,
    })
    readonly quantityInStockUnit: Promise<decimal>;

    @decorators.referenceProperty<StockIssueLine, 'location'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
        filters: {
            control: {
                // TODO: uncomment when is XT-12798 implemented
                // locationZone: {
                //     site() {
                //         return this.document.stockSite;
                //     },
                // },
            },
        },
        node: () => xtremMasterData.nodes.Location,
        lookupAccess: true,
    })
    readonly location: Reference<xtremMasterData.nodes.Location | null>;

    @decorators.referenceProperty<StockIssueLine, 'stockStatus'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
        node: () => xtremStockData.nodes.StockStatus,
        lookupAccess: true,
    })
    readonly stockStatus: Reference<xtremStockData.nodes.StockStatus | null>;

    @decorators.referenceProperty<StockIssueLine, 'lot'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
        filters: {
            control: {
                item() {
                    return this.item;
                },
            },
        },
        node: () => xtremStockData.nodes.Lot,
        lookupAccess: true,
    })
    readonly lot: Reference<xtremStockData.nodes.Lot | null>;

    @decorators.decimalProperty<StockIssueLine, 'orderCost'>({
        isPublished: true,
        isStored: true,
        dependsOn: ['itemSite'],
        dataType: () => xtremMasterData.dataTypes.costDataType,
        async defaultValue() {
            return (await (await this.itemSite)?.currentCost) || 0;
        },
        lookupAccess: true,
    })
    readonly orderCost: Promise<decimal>;

    @decorators.decimalProperty<StockIssueLine, 'totalCost'>({
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.amountInCompanyCurrency,
        dependsOn: ['quantityInStockUnit', 'orderCost'],
        async getValue() {
            return (await this.quantityInStockUnit) * (await this.orderCost);
        },
    })
    readonly totalCost: Promise<decimal>;

    @decorators.decimalProperty<StockIssueLine, 'valuedCost'>({
        isPublished: true,
        isStored: true,
        dependsOn: ['itemSite'],
        dataType: () => xtremMasterData.dataTypes.costDataType,
        async defaultValue() {
            return (await (await this.itemSite)?.currentCost) || 0;
        },
        lookupAccess: true,
    })
    readonly valuedCost: Promise<decimal>;

    @decorators.jsonProperty<StockIssueLine, 'storedDimensions'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
        dataType: () => xtremFinanceData.dataTypes.storedDimensionsDataType,
        dependsOn: ['item', { document: ['stockSite'] }],
        async defaultValue() {
            const site = await (await this.document).stockSite;
            return xtremFinanceData.functions.getDefaultDimensions(this.$.context, {
                dimensionDefinitionLevel: 'stockDirect',
                companyId: (await site.legalCompany)._id,
                site,
                item: await this.item,
            });
        },
    })
    readonly storedDimensions: Promise<object | null>;

    @decorators.referenceProperty<StockIssueLine, 'analyticalData'>({
        isStored: true,
        isMutable: true,
        isPublished: true,
        lookupAccess: true,
        ignoreIsActive: true,
        isFrozen() {
            return xtremFinanceData.functions.isAnalyticalDataFrozen(this);
        },
        node: () => xtremFinanceData.nodes.AnalyticalData,
        dependsOn: ['storedAttributes', 'storedDimensions'],
        async defaultValue() {
            return (await xtremFinanceData.functions.getAnalyticalDataFromStoredAttributesAndDimensions(
                this.$.context,
                await this.storedAttributes,
                await this.storedDimensions,
            )) as any;
        },
        updatedValue: useDefaultValue,
    })
    readonly analyticalData: Reference<xtremFinanceData.nodes.AnalyticalData>;

    @decorators.jsonProperty<StockIssueLine, 'storedAttributes'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
        dataType: () => xtremFinanceData.dataTypes.storedAttributesDataType,
        dependsOn: ['item', { document: ['stockSite'] }],
        async defaultValue() {
            const site = await (await this.document).stockSite;
            return xtremFinanceData.functions.getDefaultAttributes(this.$.context, {
                dimensionDefinitionLevel: 'stockDirect',
                companyId: (await site.legalCompany)._id,
                site,
                item: await this.item,
            });
        },
    })
    readonly storedAttributes: Promise<xtremMasterData.interfaces.StoredAttributes | null>;

    @decorators.jsonProperty<StockIssueLine, 'computedAttributes'>({
        isPublished: true,
        async computeValue() {
            const stockSite = await (await this.document).stockSite;
            return xtremStock.functions.analytical.computeAttributes(this.$.context, {
                item: await this.item,
                stockSite: await (await this.document).stockSite,
                financialSite: await xtremFinanceData.functions.getFinancialSite(stockSite),
            });
        },
        lookupAccess: true,
    })
    readonly computedAttributes: Promise<object>;

    @decorators.collectionProperty<StockIssueLine, 'stockDetails'>({
        isPublished: true,
        isVital: true,
        reverseReference: 'documentLine',
        node: () => xtremStockData.nodes.StockIssueDetail,
        lookupAccess: true,
    })
    readonly stockDetails: Collection<xtremStockData.nodes.StockIssueDetail>;

    @decorators.jsonProperty<StockIssueLine, 'jsonStockDetails'>({
        isPublished: true,
        dependsOn: ['stockDetails'],
        async setValue(details) {
            if (details && details.length > 0) {
                await this.$.set({
                    stockDetails: xtremStockData.functions.stockDetailLib.parseDetails(details) as Array<
                        NodeCreateData<xtremStockData.nodes.StockIssueDetail>
                    >,
                });
            }
        },
        async computeValue() {
            if ((await this.stockDetails.length) > 0) {
                return this.stockDetails.map(detail =>
                    xtremStockData.functions.stockDetailLib.filterStockDetailProperties(
                        detail,
                        xtremStockData.nodes.StockIssueDetail,
                        {
                            onlyIds: true,
                        },
                    ),
                );
            }
            // forcing typing to accept an empty object
            return {} as any;
        },
        lookupAccess: true,
    })
    readonly jsonStockDetails: Promise<Array<JsonType<NodeCreateData<xtremStockData.nodes.StockIssueDetail>>>>;

    @decorators.collectionProperty<StockIssueLine, 'stockMovements'>({
        isPublished: true,
        reverseReference: 'documentLine',
        node: () => xtremStockData.nodes.StockJournal,
        lookupAccess: true,
    })
    readonly stockMovements: Collection<xtremStockData.nodes.StockJournal>;

    @decorators.collectionProperty<StockIssueLine, 'stockTransactions'>({
        isPublished: true,
        reverseReference: 'documentLine',
        isVital: true,
        node: () => xtremStockData.nodes.StockTransaction,
        lookupAccess: true,
    })
    readonly stockTransactions: Collection<xtremStockData.nodes.StockTransaction>;

    // the site property on line level is needed for the accounting interface
    @decorators.referenceProperty<StockIssueLine, 'site'>({
        isPublished: true,
        node: () => xtremSystem.nodes.Site,
        async getValue() {
            return (await this.document).stockSite;
        },
        lookupAccess: true,
    })
    readonly site: Reference<xtremSystem.nodes.Site>;

    @decorators.enumProperty<StockIssueLine, 'stockDetailStatus'>({
        isPublished: true,
        isStored: true,
        dataType: () => xtremStockData.enums.stockDetailStatusDataType,
        dependsOn: ['item', 'stockDetails'],
        async defaultValue() {
            return xtremStockData.functions.stockDetailLib.getStockDetailStatus({
                line: this,
                quantityExpected: await this.quantityInStockUnit,
                checkSerialNumbers: await this.checkSerialNumbers(),
            });
        },
        updatedValue: useDefaultValue,
        lookupAccess: true,
    })
    readonly stockDetailStatus: Promise<xtremStockData.enums.StockDetailStatus>;

    @decorators.enumProperty<StockIssueLine, 'displayStatus'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremStock.enums.StockIssueLineDisplayStatusDataType,
        dependsOn: ['stockDetailStatus', 'stockTransactionStatus'],
        async defaultValue() {
            return xtremStock.functions.stockIssueLib.calculateStockIssueLineDisplayStatus(
                await this.stockDetailStatus,
                await this.stockTransactionStatus,
            );
        },
        updatedValue: useDefaultValue,
    })
    readonly displayStatus: Promise<xtremStock.enums.StockIssueLineDisplayStatus>;

    /**
     * Method that applies dimensions and attributes to the lines of a stock receipt line
     * @param context
     * @param stockIssueLine
     * @param storedDimensions
     * @param storedAttributes
     * @returns true or throws error
     */
    @decorators.mutation<typeof StockIssueLine, 'setDimension'>({
        isPublished: true,
        parameters: [
            {
                name: 'stockIssueLine',
                type: 'reference',
                node: () => xtremStock.nodes.StockIssueLine,
                isMandatory: true,
                isWritable: true,
            },
            { name: 'storedDimensions', type: 'string' },
            { name: 'storedAttributes', type: 'string' },
        ],
        return: 'boolean',
    })
    static async setDimension(
        _context: Context,
        stockIssueLine: xtremStock.nodes.StockIssueLine,
        storedDimensions?: string,
        storedAttributes?: string,
    ): Promise<boolean> {
        await stockIssueLine.$.set({
            storedDimensions: storedDimensions ? JSON.parse(storedDimensions) : null,
            storedAttributes: storedAttributes ? JSON.parse(storedAttributes) : null,
        });
        await stockIssueLine.$.save();
        return true;
    }
}
