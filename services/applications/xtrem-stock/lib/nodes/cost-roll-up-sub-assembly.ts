import type { decimal, integer, Reference } from '@sage/xtrem-core';
import { decorators, Node } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremStock from '..';

@decorators.node<CostRollUpSubAssembly>({
    storage: 'sql',
    isPublished: true,
    canRead: true,
    canSearch: true,
    canDeleteMany: true,
    isVitalCollectionChild: true,
    indexes: [
        {
            orderBy: { resultLine: 1, componentNumber: 1 },
            isUnique: true,
        },
        {
            orderBy: { resultLine: 1, componentNumber: 1, calculationQuantity: 1 },
        },
    ],
})
export class CostRollUpSubAssembly extends Node {
    @decorators.referenceProperty<CostRollUpSubAssembly, 'resultLine'>({
        isPublished: true,
        isStored: true,
        isFrozen: true,
        isVitalParent: true,
        node: () => xtremStock.nodes.CostRollUpResultLine,
        lookupAccess: true,
    })
    readonly resultLine: Reference<xtremStock.nodes.CostRollUpResultLine>;

    @decorators.integerProperty<CostRollUpSubAssembly, 'componentNumber'>({
        isStored: true,
        isPublished: true,
        async control(cx, val) {
            await cx.error.if(val).is.not.greater.than(0);
        },
        lookupAccess: true,
    })
    readonly componentNumber: Promise<integer>;

    @decorators.referenceProperty<CostRollUpSubAssembly, 'item'>({
        isPublished: true,
        isStored: true,
        node: () => xtremMasterData.nodes.Item,
        lookupAccess: true,
    })
    readonly item: Promise<xtremMasterData.nodes.Item>;

    @decorators.decimalProperty<CostRollUpSubAssembly, 'calculationQuantity'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.quantityInStockUnit,
        async control(cx, val) {
            await cx.error.if(val).is.negative();
            await cx.error.if(val).is.zero();
        },
        lookupAccess: true,
    })
    readonly calculationQuantity: Promise<decimal>;

    @decorators.referenceProperty<CostRollUpSubAssembly, 'stockUnit'>({
        isPublished: true,
        node: () => xtremMasterData.nodes.UnitOfMeasure,
        dependsOn: ['item'],
        async getValue() {
            return (await this.item).stockUnit;
        },
    })
    readonly stockUnit: Reference<xtremMasterData.nodes.UnitOfMeasure>;

    @decorators.decimalProperty<CostRollUpSubAssembly, 'currentMaterialCost'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.costDataType,
        async control(cx, val) {
            await cx.error.if(val).is.negative();
        },
        lookupAccess: true,
    })
    readonly currentMaterialCost: Promise<decimal>;

    @decorators.decimalProperty<CostRollUpSubAssembly, 'materialCost'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.costDataType,
        async control(cx, val) {
            await cx.error.if(val).is.negative();
        },
        lookupAccess: true,
    })
    readonly materialCost: Promise<decimal>;

    @decorators.decimalProperty<CostRollUpSubAssembly, 'currentMachineCost'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.costDataType,
        async control(cx, val) {
            await cx.error.if(val).is.negative();
        },
        lookupAccess: true,
    })
    readonly currentMachineCost: Promise<decimal>;

    @decorators.decimalProperty<CostRollUpSubAssembly, 'machineCost'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.costDataType,
        async control(cx, val) {
            await cx.error.if(val).is.negative();
        },
        lookupAccess: true,
    })
    readonly machineCost: Promise<decimal>;

    @decorators.decimalProperty<CostRollUpSubAssembly, 'currentLaborCost'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.costDataType,
        async control(cx, val) {
            await cx.error.if(val).is.negative();
        },
        lookupAccess: true,
    })
    readonly currentLaborCost: Promise<decimal>;

    @decorators.decimalProperty<CostRollUpSubAssembly, 'laborCost'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.costDataType,
        async control(cx, val) {
            await cx.error.if(val).is.negative();
        },
        lookupAccess: true,
    })
    readonly laborCost: Promise<decimal>;

    @decorators.decimalProperty<CostRollUpSubAssembly, 'currentToolCost'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.costDataType,
        async control(cx, val) {
            await cx.error.if(val).is.negative();
        },
        lookupAccess: true,
    })
    readonly currentToolCost: Promise<decimal>;

    @decorators.decimalProperty<CostRollUpSubAssembly, 'toolCost'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.costDataType,
        async control(cx, val) {
            await cx.error.if(val).is.negative();
        },
        lookupAccess: true,
    })
    readonly toolCost: Promise<decimal>;

    @decorators.decimalProperty<CostRollUpSubAssembly, 'currentTotalCost'>({
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.costDataType,
        async getValue() {
            return (
                (await this.currentMaterialCost) +
                (await this.currentMachineCost) +
                (await this.currentLaborCost) +
                (await this.currentToolCost)
            );
        },
        lookupAccess: true,
    })
    readonly currentTotalCost: Promise<decimal>;

    @decorators.decimalProperty<CostRollUpSubAssembly, 'totalCost'>({
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.costDataType,
        async getValue() {
            return (
                (await this.materialCost) + (await this.machineCost) + (await this.laborCost) + (await this.toolCost)
            );
        },
        lookupAccess: true,
    })
    readonly totalCost: Promise<decimal>;
}
