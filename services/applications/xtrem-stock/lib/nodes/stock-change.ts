import type { NotificationEnvelope } from '@sage/xtrem-communication';
import type { Collection, Context, Reference, integer } from '@sage/xtrem-core';
import { BusinessRuleError, Node, date, decorators, useDefaultValue } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremStockData from '@sage/xtrem-stock-data';
import * as xtremSystem from '@sage/xtrem-system';
import * as _ from 'lodash';
import * as xtremStock from '..';

@decorators.node<StockChange>({
    isClearedByReset: true,
    storage: 'sql',
    isPublished: true,
    canRead: true,
    canSearch: true,
    canCreate: true,
    canUpdate: true,
    canDelete: true,
    isCustomizable: true,
    hasAttachments: true,
    indexes: [
        {
            orderBy: { number: +1 },
            isUnique: true,
        },
    ],
    notifies: ['created', 'updated', 'deleted'],
    async saveBegin() {
        await xtremMasterData.functions.controlDocumentNumber(this);

        // check uniqueness of all assigned serial numbers over all lines and check if all lines
        // have serial numbers assigned
        if (await this.$.context.isServiceOptionEnabled(xtremMasterData.serviceOptions.serialNumberOption)) {
            if (
                (await (await this.item).serialNumberManagement) === 'managed' &&
                (await (await this.item).serialNumberUsage) === 'issueAndReceipt'
            ) {
                const serialNumberArray: xtremStockData.nodes.SerialNumber[] = [];
                await this.lines.forEach(async line => {
                    // collect all serial numbers assigned to any stock detail over all lines
                    await line.stockDetails.forEach(async stockDetail => {
                        await stockDetail.stockDetailSerialNumbers.forEach(async detailSerialNumber => {
                            const serialNumId =
                                (await detailSerialNumber.serialNumber) as xtremStockData.nodes.SerialNumber;
                            const serial = await this.$.context.read(xtremStockData.nodes.SerialNumber, {
                                _id: serialNumId._id,
                            });
                            serialNumberArray.push(serial);
                        });
                    });
                });
                // check for duplicated serial numbers
                if (_.uniq(serialNumberArray).length !== serialNumberArray.length) {
                    throw new BusinessRuleError(
                        this.$.context.localize(
                            '@sage/xtrem-stock/nodes__stock_change___duplicated_serial_number',
                            'There are duplicate serial numbers. Make sure that each line has a unique set of serial numbers.',
                        ),
                    );
                }
            }
        }
    },
})
@xtremStockData.functions.staticImplements<xtremStockData.interfaces.StaticDocumentHeaderWithStockPosting>()
export class StockChange extends Node implements xtremStockData.interfaces.StockChangeDocumentHeader {
    getEffectiveDate(): Promise<date> {
        return this.effectiveDate;
    }

    getStockSite(): Promise<xtremSystem.nodes.Site> {
        return this.stockSite;
    }

    @decorators.stringProperty<StockChange, 'number'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.documentNumber,
        defaultValue() {
            return xtremMasterData.functions.getDocumentNumberPlaceholder();
        },
        deferredDefaultValue() {
            return xtremMasterData.functions.allocateDocumentNumber(this);
        },
        provides: ['sequenceNumber'],
        lookupAccess: true,
    })
    readonly number: Promise<string>;

    @decorators.dateProperty<StockChange, 'effectiveDate'>({
        isStored: true,
        isPublished: true,
        defaultValue() {
            return date.today();
        },
        provides: ['documentDate'],
        lookupAccess: true,
    })
    readonly effectiveDate: Promise<date>;

    @decorators.referenceProperty<StockChange, 'stockSite'>({
        isStored: true,
        isPublished: true,
        filters: {
            control: {
                isInventory: true,
            },
        },
        node: () => xtremSystem.nodes.Site,
        provides: ['site'],
        lookupAccess: true,
    })
    readonly stockSite: Reference<xtremSystem.nodes.Site>;

    @decorators.stringProperty<StockChange, 'description'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremSystem.dataTypes.description,
        lookupAccess: true,
    })
    readonly description: Promise<string>;

    @decorators.enumProperty<StockChange, 'status'>({
        isPublished: true,
        dependsOn: ['stockTransactionStatus', { lines: ['stockTransactionStatus'] }],
        dataType: () => xtremStockData.enums.StockDocumentStatusDataType,
        async computeValue() {
            return xtremStockData.functions.stockDocumentLib.getStockDocumentStatus(await this.stockTransactionStatus);
        },
        lookupAccess: true,
    })
    readonly status: Promise<xtremStockData.enums.StockDocumentStatus>;

    @decorators.enumProperty<StockChange, 'stockTransactionStatus'>({
        isPublished: true,
        isStored: true,
        dependsOn: [{ lines: ['stockTransactionStatus'] }],
        dataType: () => xtremStockData.enums.StockDocumentTransactionStatusDataType,
        defaultValue() {
            return xtremStockData.functions.stockTransactionLib.computeHeaderStatus(this);
        },
        updatedValue: useDefaultValue,
        lookupAccess: true,
    })
    readonly stockTransactionStatus: Promise<xtremStockData.enums.StockDocumentTransactionStatus>;

    @decorators.referenceProperty<StockChange, 'item'>({
        isStored: true,
        isPublished: true,
        node: () => xtremMasterData.nodes.Item,
        ignoreIsActive: true,
        filters: {
            control: { isStockManaged: true },
        },
        lookupAccess: true,
    })
    readonly item: Reference<xtremMasterData.nodes.Item>;

    @decorators.referenceProperty<StockChange, 'itemSite'>({
        isPublished: false,
        isNullable: true,
        dependsOn: ['item', 'stockSite'],
        node: () => xtremMasterData.nodes.ItemSite,
        join: {
            item() {
                return this.item;
            },
            site() {
                return this.stockSite;
            },
        },
    })
    readonly itemSite: Reference<xtremMasterData.nodes.ItemSite | null>;

    @decorators.referenceProperty<StockChange, 'stockStatus'>({
        isStored: true,
        isPublished: true,
        node: () => xtremStockData.nodes.StockStatus,
        lookupAccess: true,
    })
    readonly stockStatus: Reference<xtremStockData.nodes.StockStatus>;

    @decorators.booleanProperty<StockChange, 'forceUpdateForStock'>({
        defaultValue: false,
    })
    readonly forceUpdateForStock: Promise<boolean>;

    @decorators.referenceProperty<StockChange, 'location'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
        node: () => xtremMasterData.nodes.Location,
        dependsOn: ['stockSite'],
        filters: {
            control: {
                locationZone: {
                    site() {
                        return this.stockSite;
                    },
                },
            },
        },
        lookupAccess: true,
    })
    readonly location: Reference<xtremMasterData.nodes.Location | null>;

    @decorators.referenceProperty<StockChange, 'lot'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
        node: () => xtremStockData.nodes.Lot,
        dependsOn: ['item'],
        filters: {
            control: {
                item() {
                    return this.item;
                },
            },
        },
        lookupAccess: true,
    })
    readonly lot: Reference<xtremStockData.nodes.Lot | null>;

    @decorators.stringProperty<StockChange, 'owner'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremSystem.dataTypes.code,
        lookupAccess: true,
    })
    readonly owner: Promise<string>;

    @decorators.referenceProperty<StockChange, 'stockUnit'>({
        isPublished: true,
        dependsOn: ['item'],
        node: () => xtremMasterData.nodes.UnitOfMeasure,
        ignoreIsActive: true,
        async getValue() {
            return (await this.item).stockUnit;
        },
        lookupAccess: true,
    })
    readonly stockUnit: Reference<xtremMasterData.nodes.UnitOfMeasure>;

    @decorators.collectionProperty<StockChange, 'lines'>({
        isPublished: true,
        isVital: true,
        dependsOn: ['item', 'itemSite'],
        node: () => xtremStock.nodes.StockChangeLine,
        reverseReference: 'document',
        isRequired: true,
        lookupAccess: true,
    })
    readonly lines: Collection<xtremStock.nodes.StockChangeLine>;

    readonly linesWithStockTransactionStatusCollectionName = 'lines';

    @decorators.mutation<typeof StockChange, 'postToStock'>({
        isPublished: true,
        startsReadOnly: true,
        parameters: [
            {
                name: 'documentIds',
                type: 'array',
                item: {
                    type: 'integer',
                },
            },
        ],
        return: {
            type: 'string',
        },
    })
    static async postToStock(context: Context, documentIds: integer[]): Promise<string> {
        const args = { documentClass: StockChange, documentIds };
        await xtremStockData.functions.notificationLib.validateStockChangeDocuments(context, args);

        const notPostedDocumentIds = await context.runInWritableContext(writableContext => {
            return xtremStockData.functions.stockLib.setStockTransactionToProgress(writableContext, args);
        });
        if (notPostedDocumentIds.length === 0) {
            return JSON.stringify({
                notificationId: 'none',
                result: 'requested',
                documents: [],
            } as xtremStockData.interfaces.PostToStockReturnJsonType<xtremStockData.enums.StockMovementTypeEnum>);
        }

        return context.runInWritableContext(writableContext => {
            return xtremStockData.functions.notificationLib.stockChangeRequestNotification(writableContext, {
                documentClass: StockChange,
                documentIds: notPostedDocumentIds,
            });
        });
    }

    @decorators.notificationListener<typeof StockChange>({
        startsReadOnly: true,
        topic: 'StockChange/stock/change/reply',
        onError(
            context: Context,
            envelope: NotificationEnvelope<
                xtremStockData.interfaces.StockReplyPayload<xtremStockData.enums.StockMovementTypeEnum.change>
            >,
            error: Error,
        ) {
            return xtremStockData.functions.notificationLib.errorHandler(context, envelope, error, StockChange);
        },
    })
    static async onStockReply(
        readOnlyContext: Context,
        payload: xtremStockData.interfaces.StockReplyPayload<xtremStockData.enums.StockMovementTypeEnum.change>,
    ): Promise<void> {
        await readOnlyContext.runInWritableContext(writableContext =>
            xtremStockData.functions.notificationLib.reactToStockMovementReply(writableContext, payload, StockChange),
        );
    }
}
