import type { NotificationEnvelope } from '@sage/xtrem-communication';
import type { Collection, Context, Reference, integer } from '@sage/xtrem-core';
import { BusinessRuleError, Logger, Node, date, decorators } from '@sage/xtrem-core';
import * as xtremFinanceData from '@sage/xtrem-finance-data';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremStockData from '@sage/xtrem-stock-data';
import * as xtremSystem from '@sage/xtrem-system';
import * as _ from 'lodash';
import * as xtremStock from '..';

const logger = Logger.getLogger(__filename, 'miscellaneous-stock-issue');

@decorators.node<StockIssue>({
    isClearedByReset: true,
    storage: 'sql',
    isPublished: true,
    canRead: true,
    canCreate: true,
    canSearch: true,
    canUpdate: true,
    canDelete: true,
    isCustomizable: true,
    hasAttachments: true,
    indexes: [{ orderBy: { number: +1 }, isUnique: true, isNaturalKey: true }],
    notifies: ['created', 'updated', 'deleted'],
    async saveBegin() {
        await xtremMasterData.functions.controlDocumentNumber(this);

        // check uniqueness of all assigned serial numbers over all lines
        if (await this.$.context.isServiceOptionEnabled(xtremMasterData.serviceOptions.serialNumberOption)) {
            const serialNumberArray: xtremStockData.nodes.SerialNumber[] = [];
            await this.lines
                .filter(
                    async line =>
                        (await (await line.item).serialNumberManagement) === 'managed' &&
                        (await (await line.item).serialNumberUsage) === 'issueAndReceipt',
                )
                .forEach(async line => {
                    // collect all serial numbers assigned to any stock detail over all lines
                    await line.stockDetails.forEach(async stockDetail => {
                        await stockDetail.stockDetailSerialNumbers.forEach(async detailSerialNumber => {
                            const serialNumId =
                                (await detailSerialNumber.serialNumber) as xtremStockData.nodes.SerialNumber;
                            const serial = await this.$.context.read(xtremStockData.nodes.SerialNumber, {
                                _id: serialNumId._id,
                            });
                            serialNumberArray.push(serial);
                        });
                    });
                });
            // check for duplicated serial numbers
            if (_.uniq(serialNumberArray).length !== serialNumberArray.length) {
                throw new BusinessRuleError(
                    this.$.context.localize(
                        '@sage/xtrem-stock/nodes__stock_issue___duplicated_serial_number',
                        'There are duplicate serial numbers. Make sure that each line has a unique set of serial numbers.',
                    ),
                );
            }
        }
        const displayStatus = await xtremStock.functions.stockIssueLib.computeHeaderDisplayStatus(this);
        await this.$.set({ displayStatus });
    },
})
@xtremStockData.functions.staticImplements<xtremStockData.interfaces.StaticDocumentHeaderWithStockPosting>()
export class StockIssue extends Node implements xtremFinanceData.interfaces.FinanceOriginDocumentHeader {
    getEffectiveDate(): Promise<date> {
        return this.effectiveDate;
    }

    getStockSite(): Promise<xtremSystem.nodes.Site> {
        return this.stockSite;
    }

    @decorators.stringProperty<StockIssue, 'number'>({
        isPublished: true,
        isStored: true,
        dataType: () => xtremMasterData.dataTypes.documentNumber,
        defaultValue() {
            return xtremMasterData.functions.getDocumentNumberPlaceholder();
        },
        deferredDefaultValue() {
            return xtremMasterData.functions.allocateDocumentNumber(this);
        },
        provides: ['sequenceNumber'],
    })
    readonly number: Promise<string>;

    @decorators.stringProperty<StockIssue, 'description'>({
        isPublished: true,
        isStored: true,
        dataType: () => xtremSystem.dataTypes.description,
    })
    readonly description: Promise<string>;

    @decorators.enumProperty<StockIssue, 'status'>({
        isPublished: true,
        dependsOn: ['stockTransactionStatus', { lines: ['stockTransactionStatus'] }],
        dataType: () => xtremStockData.enums.StockDocumentStatusDataType,
        async computeValue() {
            return xtremStockData.functions.stockDocumentLib.getStockDocumentStatus(await this.stockTransactionStatus);
        },
    })
    readonly status: Promise<xtremStockData.enums.StockDocumentStatus>;

    @decorators.enumProperty<StockIssue, 'stockTransactionStatus'>({
        isPublished: true,
        dependsOn: [{ lines: ['stockTransactionStatus'] }],
        dataType: () => xtremStockData.enums.StockDocumentTransactionStatusDataType,
        computeValue() {
            return xtremStockData.functions.stockTransactionLib.computeHeaderStatus(this);
        },
    })
    readonly stockTransactionStatus: Promise<xtremStockData.enums.StockDocumentTransactionStatus>;

    @decorators.enumProperty<StockIssue, 'displayStatus'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        dataType: () => xtremStock.enums.StockIssueDisplayStatusDataType,
        defaultValue: 'detailsRequired',
        dependsOn: [{ lines: ['stockTransactionStatus'] }, 'status', 'stockTransactionStatus'],
        updatedValue() {
            return xtremStock.functions.stockIssueLib.computeHeaderDisplayStatus(this);
        },
    })
    readonly displayStatus: Promise<xtremStock.enums.StockIssueDisplayStatus>;

    @decorators.dateProperty<StockIssue, 'effectiveDate'>({
        isPublished: true,
        isStored: true,
        defaultValue() {
            return date.today();
        },
        provides: ['documentDate'],
    })
    readonly effectiveDate: Promise<date>;

    @decorators.booleanProperty<StockIssue, 'forceUpdateForStock'>({
        defaultValue: false,
    })
    readonly forceUpdateForStock: Promise<boolean>;

    @decorators.referenceProperty<StockIssue, 'stockSite'>({
        isPublished: true,
        isStored: true,
        filters: { control: { isInventory: true } },
        lookupAccess: true,
        node: () => xtremSystem.nodes.Site,
        provides: ['site'],
    })
    readonly stockSite: Reference<xtremSystem.nodes.Site>;

    @decorators.collectionProperty<StockIssue, 'lines'>({
        isVital: true,
        isRequired: true,
        isPublished: true,
        reverseReference: 'document',
        node: () => xtremStock.nodes.StockIssueLine,
    })
    readonly lines: Collection<xtremStock.nodes.StockIssueLine>;

    @decorators.collectionProperty<StockIssue, 'postingDetails'>({
        isPublished: true,
        join: {
            documentSysId() {
                return this._id;
            },
            documentType() {
                return 'miscellaneousStockIssue';
            },
        },
        node: () => xtremFinanceData.nodes.FinanceTransaction,
        lookupAccess: true,
    })
    readonly postingDetails: Collection<xtremFinanceData.nodes.FinanceTransaction>;

    readonly linesWithStockTransactionStatusCollectionName = 'lines';

    // property needed for the accounting interface
    @decorators.referenceProperty<StockIssue, 'financialSite'>({
        isPublished: true,
        node: () => xtremSystem.nodes.Site,
        dependsOn: ['stockSite'],
        lookupAccess: true,
        async getValue() {
            return xtremFinanceData.functions.getFinancialSite(await this.stockSite);
        },
    })
    readonly financialSite: Reference<xtremSystem.nodes.Site>;

    // property needed for the accounting interface
    @decorators.referenceProperty<StockIssue, 'transactionCurrency'>({
        isPublished: true,
        node: () => xtremMasterData.nodes.Currency,
        dependsOn: ['financialSite'],
        lookupAccess: true,

        async getValue() {
            return (await (await this.financialSite).legalCompany).currency;
        },
    })
    readonly transactionCurrency: Reference<xtremMasterData.nodes.Currency>;

    // property needed for the accounting interface
    @decorators.dateProperty<StockIssue, 'documentDate'>({
        isPublished: true,
        dependsOn: ['effectiveDate'],
        lookupAccess: true,
        getValue() {
            return this.effectiveDate;
        },
    })
    readonly documentDate: Promise<date>;

    @decorators.enumProperty<StockIssue, 'financeIntegrationStatus'>({
        isPublished: true,
        dataType: () => xtremFinanceData.enums.financeIntegrationStatusDataType,
        lookupAccess: true,
        async computeValue() {
            return xtremFinanceData.functions.getDocumentIntegrationStatus(
                this.$.context,
                'miscellaneousStockIssue',
                await this.number,
            );
        },
    })
    readonly financeIntegrationStatus: Promise<xtremFinanceData.enums.FinanceIntegrationStatus>;

    @decorators.referenceProperty<StockIssue, 'reasonCode'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
        lookupAccess: true,
        node: () => xtremMasterData.nodes.ReasonCode,
        filters: {
            control: { isStockIssue: true },
        },
    })
    readonly reasonCode: Reference<xtremMasterData.nodes.ReasonCode | null>;

    @decorators.booleanProperty<StockIssue, 'isSetDimensionsMainListHidden'>({
        isPublished: true,
        async getValue() {
            const displayStatus = await this.displayStatus;
            return displayStatus === 'issued' || displayStatus === 'stockPostingInProgress';
        },
    })
    readonly isSetDimensionsMainListHidden: Promise<boolean>;

    @decorators.mutation<typeof StockIssue, 'postToStock'>({
        isPublished: true,
        parameters: [
            {
                name: 'documentIds',
                type: 'array',
                item: {
                    type: 'integer',
                },
            },
        ],
        return: { type: 'string' },
    })
    static async postToStock(context: Context, documentIds: integer[]): Promise<string> {
        const numberOfStockIssueLines = context.queryCount(xtremStock.nodes.StockIssueLine, {
            filter: {
                document: { _in: documentIds },
                stockDetailStatus: 'required',
            },
        });

        if ((await numberOfStockIssueLines) > 0) {
            throw new BusinessRuleError(
                context.localize(
                    '@sage/xtrem-stock/nodes__stock_issue_post__stock_details_required',
                    'You need to enter stock details for all lines before you can post.',
                ),
            );
        }

        return xtremStockData.functions.notificationLib.stockIssueRequestNotification(context, {
            documentClass: StockIssue,
            documentIds,
        });
    }

    // Repost creates an update notification to the acc engine ir order to update att and dimenions.
    // In fact, the existing finance document headers are preserved while the lines are all rebuilt (old ones deleted and new ones calculated)
    @decorators.mutation<typeof StockIssue, 'repost'>({
        isPublished: true,
        parameters: [
            {
                name: 'stockIssue',
                type: 'reference',
                isMandatory: true,
                isWritable: true,
                node: () => StockIssue,
            },
            {
                name: 'documentLines',
                type: 'array',
                item: {
                    type: 'object',
                    properties: {
                        baseDocumentLineSysId: 'integer',
                        storedAttributes: 'json',
                        storedDimensions: 'json',
                    },
                },
            },
        ],
        return: { type: 'object', properties: { wasSuccessful: 'boolean', message: 'string' } },
    })
    static async repost(
        context: Context,
        stockIssue: StockIssue,
        documentLines: xtremFinanceData.interfaces.FinanceIntegrationDocumentLineUpdate[],
    ): Promise<xtremFinanceData.interfaces.MutationResult> {
        if (!xtremFinanceData.functions.canRepost(await stockIssue.financeIntegrationStatus)) {
            throw new BusinessRuleError(
                context.localize(
                    '@sage/xtrem-stock/nodes__stock-issue__cant_repost_stock_issue_when_status_is_not_failed',
                    "You can only repost a stock issue if the status is 'Failed' or 'Not recorded'.",
                ),
            );
        }

        const updateLines = documentLines.map(documentLine => {
            return {
                _id: documentLine.baseDocumentLineSysId,
                storedAttributes: documentLine.storedAttributes,
                storedDimensions: documentLine.storedDimensions,
            };
        });
        await stockIssue.$.set({ lines: updateLines });
        await stockIssue.$.save();

        // send notification in order to update a staging table entry for the accounting engine
        if (await (await (await stockIssue.stockSite).legalCompany).doStockPosting) {
            await xtremFinanceData.functions.financeDocumentUpdateNotification(context, {
                document: stockIssue,
                lines: await stockIssue.lines.toArray(),
                documentType: 'miscellaneousStockIssue',
                replyTopic: 'StockIssueUpdate/accountingInterface',
                doNotPostOnUpdate: false,
            });
            return {
                wasSuccessful: true,
                message: context.localize(
                    '@sage/xtrem-stock/nodes__stock-issue__document_was_posted',
                    'The stock receipt was posted.',
                ),
            };
        }
        return {
            wasSuccessful: true,
            message: context.localize(
                '@sage/xtrem-stock/nodes__stock-issue__company_has_no_stock_posting',
                'The current company has no stock posting.',
            ),
        };
    }

    @decorators.mutation<typeof StockIssue, 'resendNotificationForFinance'>({
        isPublished: true,
        parameters: [{ name: 'stockIssue', type: 'reference', isMandatory: true, node: () => StockIssue }],
        return: { type: 'boolean' },
    })
    static async resendNotificationForFinance(context: Context, stockIssue: StockIssue): Promise<boolean> {
        logger.info(
            context.localize(
                '@sage/xtrem-stock/node__stock_issue__resend_notification_for_finance',
                'Resending finance notification for stock issue: {{stockIssueNumber}}',
                { stockIssueNumber: await stockIssue.number },
            ),
        );

        if (
            await xtremFinanceData.functions.AccountingEngineCommon.validateResendNotificationForFinance(context, {
                documentNumber: await stockIssue.number,
                documentType: 'miscellaneousStockIssue',
            })
        ) {
            // send notification in order to create a staging table entry for the accounting engine
            if (await (await (await stockIssue.stockSite).legalCompany).doStockPosting) {
                await xtremFinanceData.functions.stockIssueNotification(
                    context,
                    stockIssue as xtremFinanceData.interfaces.FinanceOriginDocument,
                    (await stockIssue.lines.toArray()) as xtremFinanceData.interfaces.FinanceOriginDocumentLine[],
                );
            }
        }

        return true;
    }

    @decorators.notificationListener<typeof StockIssue>({
        startsReadOnly: true,
        topic: 'StockIssue/stock/issue/reply',
        onError(
            context: Context,
            envelope: NotificationEnvelope<
                xtremStockData.interfaces.StockReplyPayload<xtremStockData.enums.StockMovementTypeEnum.issue>
            >,
            error: Error,
        ) {
            return xtremStockData.functions.notificationLib.errorHandler(context, envelope, error, StockIssue);
        },
    })
    static async onStockReply(
        readOnlyContext: Context,
        payload: xtremStockData.interfaces.StockReplyPayload<xtremStockData.enums.StockMovementTypeEnum.issue>,
    ): Promise<void> {
        const issueUpdateResult = await readOnlyContext.runInWritableContext(
            async writableContext =>
                (
                    await xtremStockData.functions.notificationLib.reactToStockMovementReply(
                        writableContext,
                        payload,
                        StockIssue,
                    )
                ).issue,
        );
        if (!issueUpdateResult) return;

        if (!issueUpdateResult.transactionHadAnError) {
            await issueUpdateResult.documents.forEach(async document => {
                await readOnlyContext.runInWritableContext(async writableContext => {
                    const stockIssue = await writableContext.read(
                        StockIssue,
                        { _id: document.id },
                        { forUpdate: true },
                    );
                    await StockIssue.updateLineCostValue(stockIssue);
                    if (document.isStockDocumentCompleted) {
                        await StockIssue.onceStockCompleted(writableContext, stockIssue);
                    }
                });
            });
        }
    }

    static async updateLineCostValue(document: StockIssue): Promise<void> {
        await document.lines
            .filter(async line => (await line.stockTransactionStatus) === 'completed')
            .forEach(async line => {
                // It should be the same value for all movements, so we pick the first
                const firstMovement = await (await line.stockDetails.elementAt(0)).stockMovements.elementAt(0);
                await line.$.set({
                    orderCost: await firstMovement.orderCost,
                    valuedCost: await firstMovement.valuedCost,
                });
            });
        await document.$.save();
    }

    static async onceStockCompleted(writableContext: Context, stockIssue: StockIssue): Promise<void> {
        // Send notification in order to create staging table entries for the accounting engine
        if (await (await (await stockIssue.stockSite).legalCompany).doStockPosting) {
            await xtremFinanceData.functions.stockIssueNotification(
                writableContext,
                stockIssue as xtremFinanceData.interfaces.FinanceOriginDocument,
                (await stockIssue.lines.toArray()) as xtremFinanceData.interfaces.FinanceOriginDocumentLine[],
            );
        }
    }

    @decorators.notificationListener<typeof StockIssue>({ topic: 'StockIssue/accountingInterface' })
    static async onFinanceIntegrationReply(
        context: Context,
        payload: xtremFinanceData.interfaces.FinanceTransactionData,
    ): Promise<void> {
        await xtremFinanceData.functions.reactToFinanceIntegrationReply(context, payload);
    }

    @decorators.notificationListener<typeof StockIssue>({ topic: 'MiscellaneousStockIssue/accountingInterface' })
    static async onFinanceIntegrationReplyV31(
        context: Context,
        payload: xtremFinanceData.interfaces.FinanceTransactionData,
    ): Promise<void> {
        await xtremStock.nodes.StockIssue.onFinanceIntegrationReply(context, payload);
    }

    @decorators.notificationListener<typeof StockIssue>({
        topic: 'StockIssue/resendNotificationForFinance',
    })
    static async onResendNotificationForFinance(context: Context, document: { number: number }) {
        const stockIssue = await context.read(StockIssue, { number: document.number });

        await StockIssue.resendNotificationForFinance(context, stockIssue);
    }
}
