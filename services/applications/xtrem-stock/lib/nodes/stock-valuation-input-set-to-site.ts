import type { Reference } from '@sage/xtrem-core';
import { decorators, Node } from '@sage/xtrem-core';
import * as xtremSystem from '@sage/xtrem-system';
import * as xtremStock from '..';

@decorators.node<StockValuationInputSetToSite>({
    storage: 'sql',
    isPublished: true,
    canRead: true,
    canSearch: true,
    isVitalCollectionChild: true,
    indexes: [
        {
            orderBy: { inputSet: 1, site: 1 },
            isUnique: true,
        },
    ],
})
export class StockValuationInputSetToSite extends Node {
    @decorators.referenceProperty<StockValuationInputSetToSite, 'inputSet'>({
        isStored: true,
        isPublished: true,
        isVitalParent: true,
        node: () => xtremStock.nodes.StockValuationInputSet,
    })
    readonly inputSet: Reference<xtremStock.nodes.StockValuationInputSet>;

    @decorators.referenceProperty<StockValuationInputSetToSite, 'site'>({
        isPublished: true,
        isStored: true,
        isRequired: true,
        node: () => xtremSystem.nodes.Site,
        provides: ['site'],
    })
    readonly site: Reference<xtremSystem.nodes.Site>;
}
