import type { Collection, Context, decimal, Reference } from '@sage/xtrem-core';
import { decorators, Node, useDefaultValue } from '@sage/xtrem-core';
import * as xtremFinanceData from '@sage/xtrem-finance-data';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremSystem from '@sage/xtrem-system';
import * as xtremStock from '..';

@decorators.node<CostRollUpResultLine>({
    storage: 'sql',
    isPublished: true,
    canRead: true,
    canSearch: true,
    canDeleteMany: true,
    isVitalCollectionChild: true,
    indexes: [
        {
            orderBy: { inputSet: 1, item: 1 },
            isUnique: true,
        },
    ],
})
export class CostRollUpResultLine extends Node {
    @decorators.referenceProperty<CostRollUpResultLine, 'inputSet'>({
        isPublished: true,
        isStored: true,
        isFrozen: true,
        isVitalParent: true,
        node: () => xtremStock.nodes.CostRollUpInputSet,
        lookupAccess: true,
    })
    readonly inputSet: Reference<xtremStock.nodes.CostRollUpInputSet>;

    @decorators.enumProperty<CostRollUpResultLine, 'creationStatus'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremStock.enums.standardCostRollUpResultLineStatusDataType,
        defaultValue: 'pending',
        lookupAccess: true,
    })
    readonly creationStatus: Promise<xtremStock.enums.StandardCostRollUpResultLineStatus>;

    @decorators.referenceProperty<CostRollUpResultLine, 'item'>({
        isPublished: true,
        isStored: true,
        node: () => xtremMasterData.nodes.Item,
        lookupAccess: true,
    })
    readonly item: Promise<xtremMasterData.nodes.Item>;

    @decorators.decimalProperty<CostRollUpResultLine, 'currentMaterialCost'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.costDataType,
        async control(cx, val) {
            await cx.error.if(val).is.negative();
        },
        lookupAccess: true,
    })
    readonly currentMaterialCost: Promise<decimal>;

    @decorators.decimalProperty<CostRollUpResultLine, 'materialCost'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.costDataType,
        async control(cx, val) {
            await cx.error.if(val).is.negative();
        },
        lookupAccess: true,
    })
    readonly materialCost: Promise<decimal>;

    @decorators.decimalProperty<CostRollUpResultLine, 'currentMachineCost'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.costDataType,
        async control(cx, val) {
            await cx.error.if(val).is.negative();
        },
        lookupAccess: true,
    })
    readonly currentMachineCost: Promise<decimal>;

    @decorators.decimalProperty<CostRollUpResultLine, 'machineCost'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.costDataType,
        async control(cx, val) {
            await cx.error.if(val).is.negative();
        },
        lookupAccess: true,
    })
    readonly machineCost: Promise<decimal>;

    @decorators.decimalProperty<CostRollUpResultLine, 'currentLaborCost'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.costDataType,
        async control(cx, val) {
            await cx.error.if(val).is.negative();
        },
        lookupAccess: true,
    })
    readonly currentLaborCost: Promise<decimal>;

    @decorators.decimalProperty<CostRollUpResultLine, 'laborCost'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.costDataType,
        async control(cx, val) {
            await cx.error.if(val).is.negative();
        },
        lookupAccess: true,
    })
    readonly laborCost: Promise<decimal>;

    @decorators.decimalProperty<CostRollUpResultLine, 'currentToolCost'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.costDataType,
        async control(cx, val) {
            await cx.error.if(val).is.negative();
        },
        lookupAccess: true,
    })
    readonly currentToolCost: Promise<decimal>;

    @decorators.decimalProperty<CostRollUpResultLine, 'toolCost'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.costDataType,
        async control(cx, val) {
            await cx.error.if(val).is.negative();
        },
        lookupAccess: true,
    })
    readonly toolCost: Promise<decimal>;

    @decorators.decimalProperty<CostRollUpResultLine, 'currentTotalCost'>({
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.costDataType,
        async getValue() {
            return (
                (await this.currentMaterialCost) +
                (await this.currentMachineCost) +
                (await this.currentLaborCost) +
                (await this.currentToolCost)
            );
        },
        lookupAccess: true,
    })
    readonly currentTotalCost: Promise<decimal>;

    @decorators.decimalProperty<CostRollUpResultLine, 'totalCost'>({
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.costDataType,
        async getValue() {
            return (
                (await this.materialCost) + (await this.machineCost) + (await this.laborCost) + (await this.toolCost)
            );
        },
        lookupAccess: true,
    })
    readonly totalCost: Promise<decimal>;

    @decorators.jsonProperty<CostRollUpResultLine, 'storedDimensions'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
        dataType: () => xtremFinanceData.dataTypes.storedDimensionsDataType,
        lookupAccess: true,
    })
    readonly storedDimensions: Promise<object | null>;

    @decorators.referenceProperty<CostRollUpResultLine, 'analyticalData'>({
        isStored: true,
        isMutable: true,
        isPublished: true,
        lookupAccess: true,
        ignoreIsActive: true,
        isFrozen() {
            return xtremFinanceData.functions.isAnalyticalDataFrozen(this);
        },
        node: () => xtremFinanceData.nodes.AnalyticalData,
        dependsOn: ['storedAttributes', 'storedDimensions'],
        async defaultValue() {
            return (await xtremFinanceData.functions.getAnalyticalDataFromStoredAttributesAndDimensions(
                this.$.context,
                await this.storedAttributes,
                await this.storedDimensions,
            )) as any;
        },
        updatedValue: useDefaultValue,
    })
    readonly analyticalData: Reference<xtremFinanceData.nodes.AnalyticalData>;

    @decorators.jsonProperty<CostRollUpResultLine, 'storedAttributes'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
        dataType: () => xtremFinanceData.dataTypes.storedAttributesDataType,
        lookupAccess: true,
    })
    readonly storedAttributes: Promise<xtremMasterData.interfaces.StoredAttributes | null>;

    @decorators.stringProperty<CostRollUpResultLine, 'creationErrorMessage'>({
        isPublished: true,
        isStored: true,
        dataType: () => xtremSystem.dataTypes.description,
        lookupAccess: true,
    })
    readonly creationErrorMessage: Promise<string>;

    @decorators.collectionProperty<CostRollUpResultLine, 'subAssemblyLines'>({
        isPublished: true,
        isVital: true,
        reverseReference: 'resultLine',
        node: () => xtremStock.nodes.CostRollUpSubAssembly,
        lookupAccess: true,
    })
    readonly subAssemblyLines: Collection<xtremStock.nodes.CostRollUpSubAssembly>;

    @decorators.bulkMutation<typeof CostRollUpResultLine, 'createItemSiteCostsFromCostRollUpResults'>({
        isPublished: true,
        async onComplete(context, results) {
            if (results.length > 0) {
                await context.batch.logMessage(
                    'info',
                    context.localize(
                        '@sage/xtrem-stock/nodes__cost_roll_up_result_line__item_site_cost_creation_finished',
                        'Item-site cost creation finished.',
                    ),
                );
            } else {
                await context.batch.logMessage(
                    'error',
                    context.localize(
                        '@sage/xtrem-stock/nodes__cost_roll_up_result_line__no_line_processed_message',
                        'Error while processing lines. Selection is empty.',
                    ),
                );
            }
        },
    })
    static async createItemSiteCostsFromCostRollUpResults(
        context: Context,
        costRollUpResultLine: xtremStock.nodes.CostRollUpResultLine,
    ): Promise<xtremStock.interfaces.CreatedOrUpdatedItemSiteCost> {
        const result = await xtremStock.functions.itemSiteCostLib.createOrUpdateItemSiteCost(
            context,
            costRollUpResultLine,
        );
        await context.batch.logMessage(result.logStatus, result.message);
        return result;
    }
}
