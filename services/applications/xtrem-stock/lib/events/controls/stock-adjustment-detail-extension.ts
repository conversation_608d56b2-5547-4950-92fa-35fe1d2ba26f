import type { ValidationContext } from '@sage/xtrem-core';
import type * as xtremMasterData from '@sage/xtrem-master-data';
import type * as xtremStockData from '@sage/xtrem-stock-data';
import * as xtremStock from '../..';

export async function controlStockDetailItem(
    cx: ValidationContext,
    args: {
        documentLine: xtremMasterData.nodes.BaseDocumentLine;
        stockDetailValue: Awaited<xtremStockData.nodes.StockAdjustmentDetail['item']>;
    },
) {
    if (!(args.documentLine instanceof xtremStock.nodes.StockCountLine)) return;

    const lineValue = await args.documentLine.item;
    if (args.stockDetailValue._id !== lineValue._id) {
        cx.error.addLocalized(
            '@sage/xtrem-stock/nodes__stock_count_line__item_of_stock_detail_must_be_the_same_as_item_of_stock_count_line',
            'The stock detail item and stock count line item need to be the same. Stock count {{number}}, line {{lineNumber}}.',
            {
                number: await args.documentLine.documentNumber,
                lineNumber: await args.documentLine._sortValue,
            },
        );
    }
}

export async function controlStockDetailSite(
    cx: ValidationContext,
    args: {
        documentLine: xtremMasterData.nodes.BaseDocumentLine;
        stockDetailValue: Awaited<xtremStockData.nodes.StockAdjustmentDetail['site']>;
    },
) {
    if (!(args.documentLine instanceof xtremStock.nodes.StockCountLine)) return;

    const lineValue = await args.documentLine.site;
    if (args.stockDetailValue._id !== lineValue._id) {
        cx.error.addLocalized(
            '@sage/xtrem-stock/nodes__stock_count_line__site_of_stock_detail_must_be_the_same_as_site_of_stock_count_line',
            'The stock detail site and stock count line site need to be the same. Stock count {{number}}, line {{lineNumber}}.',
            {
                number: await args.documentLine.documentNumber,
                lineNumber: await args.documentLine._sortValue,
            },
        );
    }
}

export async function controlStockDetailLocation(
    cx: ValidationContext,
    args: {
        documentLine: xtremMasterData.nodes.BaseDocumentLine;
        stockDetailValue: Awaited<xtremStockData.nodes.StockAdjustmentDetail['location']>;
    },
) {
    if (!(args.documentLine instanceof xtremStock.nodes.StockCountLine)) return;

    const lineValue = await args.documentLine.location;
    if (args.stockDetailValue?._id !== lineValue?._id) {
        cx.error.addLocalized(
            '@sage/xtrem-stock/nodes__stock_count_line__location_of_stock_detail_must_be_the_same_as_location_of_stock_count_line',
            'The stock detail location and stock count line location need to be the same. Stock count {{number}}, line {{lineNumber}}.',
            {
                number: await args.documentLine.documentNumber,
                lineNumber: await args.documentLine._sortValue,
            },
        );
    }
}

export async function controlStockDetailStockUnit(
    cx: ValidationContext,
    args: {
        documentLine: xtremMasterData.nodes.BaseDocumentLine;
        stockDetailValue: Awaited<xtremStockData.nodes.StockAdjustmentDetail['stockUnit']>;
    },
) {
    if (!(args.documentLine instanceof xtremStock.nodes.StockCountLine)) return;

    const lineValue = await args.documentLine.stockUnit;
    if (args.stockDetailValue._id !== lineValue._id) {
        cx.error.addLocalized(
            '@sage/xtrem-stock/nodes__stock_count_line__stock_unit_of_stock_detail_must_be_the_same_as_stock_unit_of_stock_count_line',
            'The stock unit for the stock detail and the stock count line need to be the same. Stock count {{number}}, line {{lineNumber}}.',
            {
                number: await args.documentLine.documentNumber,
                lineNumber: await args.documentLine._sortValue,
            },
        );
    }
}

export async function controlStockDetailStatus(
    cx: ValidationContext,
    args: {
        documentLine: xtremMasterData.nodes.BaseDocumentLine;
        stockDetailValue: Awaited<xtremStockData.nodes.StockAdjustmentDetail['status']>;
    },
) {
    if (!(args.documentLine instanceof xtremStock.nodes.StockCountLine)) return;

    const lineValue = await args.documentLine.stockStatus;
    if (args.stockDetailValue._id !== lineValue?._id) {
        cx.error.addLocalized(
            '@sage/xtrem-stock/nodes__stock_count_line__status_of_stock_detail_must_be_the_same_as_status_of_stock_count_line',
            'The stock detail status and stock count line status need to be the same. Stock count {{number}}, line {{lineNumber}}.',
            {
                number: await args.documentLine.documentNumber,
                lineNumber: await args.documentLine._sortValue,
            },
        );
    }
}

export async function controlStockDetailOwner(
    cx: ValidationContext,
    args: {
        documentLine: xtremMasterData.nodes.BaseDocumentLine;
        stockDetailValue: Awaited<xtremStockData.nodes.StockAdjustmentDetail['owner']>;
    },
) {
    if (!(args.documentLine instanceof xtremStock.nodes.StockCountLine)) return;

    const lineValue = await args.documentLine.owner;
    if (args.stockDetailValue !== lineValue) {
        cx.error.addLocalized(
            '@sage/xtrem-stock/nodes__stock_count_line__owner_of_stock_detail_must_be_the_same_as_owner_of_stock_count_line',
            'The owner of the stock detail and the stock count line need to be the same. Stock count {{number}}, line {{lineNumber}}.',
            {
                number: await args.documentLine.documentNumber,
                lineNumber: await args.documentLine._sortValue,
            },
        );
    }
}

export async function controlStockDetailLot(
    cx: ValidationContext,
    stockAdjustmentDetail: xtremStockData.nodes.StockAdjustmentDetail,
    stockDetailLot: xtremStockData.nodes.StockDetailLot | null,
) {
    if (stockDetailLot) return;
    const documentLine = await stockAdjustmentDetail.documentLine;

    // in the case of StockCountLine, if the item is lot managed then the stockDetail is mandatory
    if (!(documentLine instanceof xtremStock.nodes.StockCountLine)) return;

    const item = await documentLine.item;
    if ((await item.lotManagement) === 'notManaged') return;

    if (!stockDetailLot) {
        cx.error.addLocalized(
            '@sage/xtrem-stock/nodes__stock_adjustment_detail_extension__lot_mandatory',
            'You need to select a lot for the item: {{itemId}}. Stock count {{number}}, line {{lineNumber}}.',
            {
                itemId: await item.id,
                number: await documentLine.documentNumber,
                lineNumber: await documentLine._sortValue,
            },
        );
    }
}
