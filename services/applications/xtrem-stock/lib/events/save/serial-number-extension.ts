import { Logger } from '@sage/xtrem-core';
import type * as xtremStockData from '@sage/xtrem-stock-data';
import * as xtremStock from '../..';

const logger = Logger.getLogger(__filename, 'serial-number');

/**
 * When a serial number is issued from a stock record,
 * the stock count line in status 'toBeCounted' linked to this stock record should be updated accordingly.
 * @param serialNumber the serial number issued from stock
 */
async function removeSerialNumberFromStockCount(serialNumber: xtremStockData.nodes.SerialNumber) {
    const stockCountLineSerialNumber = await serialNumber.$.context
        .query(xtremStock.nodes.StockCountLineSerialNumber, {
            filter: {
                serialNumber: { _id: serialNumber._id },
                stockCountLine: { document: { status: 'toBeCounted' } },
            },
        })
        .at(0); // at most, 1 stock count line not closed can be linked to a stock record
    if (stockCountLineSerialNumber) {
        const stockCount = await serialNumber.$.context.read(
            xtremStock.nodes.StockCount,
            {
                _id: (await (await stockCountLineSerialNumber.stockCountLine).document)._id,
            },
            { forUpdate: true },
        );
        await logger.verboseAsync(
            async () =>
                `Removing serial number ${serialNumber._id} from stock count ${await stockCount.number} line ${await (
                    await stockCountLineSerialNumber.stockCountLine
                )._sortValue}`,
        );
        await stockCount.$.set({
            lines: [
                {
                    _action: 'update',
                    _id: (await stockCountLineSerialNumber.stockCountLine)._id,
                    stockCountLineSerialNumbers: [{ _id: stockCountLineSerialNumber._id, _action: 'delete' }],
                },
            ],
        });
        await stockCount.$.save();
    }
}

/**
 * When a serial number is added to a stock record,
 * the stock count line in status 'toBeCounted' linked to this stock record should be updated accordingly.
 * @param serialNumber the serial number added to stock
 */
async function addSerialNumberToStockCount(serialNumber: xtremStockData.nodes.SerialNumber) {
    const stockRecord = await serialNumber.stockRecord;
    const stockCountLine = await serialNumber.$.context
        .query(xtremStock.nodes.StockCountLine, {
            filter: { document: { status: 'toBeCounted' }, stockRecord },
        })
        .at(0); // at most, 1 stock count line not closed can be linked to a stock record
    if (stockCountLine) {
        const stockCount = await serialNumber.$.context.read(
            xtremStock.nodes.StockCount,
            {
                _id: (await stockCountLine.document)._id,
            },
            { forUpdate: true },
        );
        await logger.verboseAsync(
            async () =>
                `Adding serial number ${serialNumber._id} to stock count ${await stockCount.number} line ${await stockCountLine._sortValue}`,
        );
        await stockCount.$.set({
            lines: [
                {
                    _action: 'update',
                    _id: stockCountLine._id,
                    stockCountLineSerialNumbers: [{ serialNumber, _action: 'create', isCounted: false }],
                },
            ],
        });
        await stockCount.$.save();
    }
}

export async function updateStockCount(serialNumber: xtremStockData.nodes.SerialNumber) {
    if (serialNumber.$.status !== 'modified') return;

    // if the serial number is no longer in stock,
    // check in to be counted stock count if a StockCountLineSerialNumber is linked to the serial number and remove it
    const oldStockRecord = await (await serialNumber.$.old).stockRecord;
    const newStockRecord = await serialNumber.stockRecord;

    if (oldStockRecord && !newStockRecord) {
        logger.verbose(() => `Serial number ${serialNumber._id} is no longer in stock ${oldStockRecord._id}`);
        await removeSerialNumberFromStockCount(serialNumber);
    }

    // if the serial number is now in stock,
    // check in to be counted stock count if a StockCountLineSerialNumber should be linked to the serial number and add it
    if (!oldStockRecord && newStockRecord) {
        logger.verbose(() => `Serial number ${serialNumber._id} is added to stock ${newStockRecord._id}`);
        await addSerialNumberToStockCount(serialNumber);
    }

    // if the serial number is moved into the stock, update StockCountLineSerialNumber of 'toBeCounted' stock count accordingly
    if (oldStockRecord && newStockRecord && oldStockRecord !== newStockRecord) {
        logger.verbose(
            () =>
                `Serial number ${serialNumber._id} is moved from stock ${oldStockRecord._id} to stock ${newStockRecord._id}`,
        );
        await removeSerialNumberFromStockCount(serialNumber);
        await addSerialNumberToStockCount(serialNumber);
    }
}
