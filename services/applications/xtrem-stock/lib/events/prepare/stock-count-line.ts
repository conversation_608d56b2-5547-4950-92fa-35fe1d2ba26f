import type { UpdateAction } from '@sage/xtrem-core';
import { BusinessRuleError } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremStockData from '@sage/xtrem-stock-data';
import type * as xtremStock from '../..';

/**
 * The goal of this function is to find the stock record for the stock count line.
 * It's needed when isCreatedFromStockRecord is true and stockRecord is null.
 *
 * @param stockCountLine
 * @returns
 */
export async function prepareStockRecord(stockCountLine: xtremStock.nodes.StockCountLine) {
    if (stockCountLine.$.status !== 'added') return;
    if (!(await stockCountLine.isCreatedFromStockRecord)) return;
    if (await stockCountLine.stockRecord) return;
    if (!['toBeCounted', 'countInProgress'].includes(await (await stockCountLine.document).status)) return;

    const stockRecord = await xtremStockData.functions.stockLib.getStockRecord(stockCountLine.$.context, {
        item: await stockCountLine.item,
        site: await (await stockCountLine.document).stockSite,
        location: await stockCountLine.location,
        lot: await stockCountLine.lot,
        status: await stockCountLine.stockStatus,
        stockUnit: await stockCountLine.stockUnit,
        owner: await stockCountLine.owner,
    });

    // if document status = countInProgress and the stock record is not found => error
    // otherwise (if document status=toBeCounted) we count on start to find the stockRecord and exclude if necessary
    // => leave stockRecord=null
    if (!stockRecord && (await (await stockCountLine.document).status) === 'countInProgress') {
        throw new BusinessRuleError(
            stockCountLine.$.context.localize(
                '@sage/xtrem-stock/nodes__stock_count_line__stock_record_not_found',
                'Stock record not found. Stock count {{number}}, line {{lineNumber}}.',
                { number: await stockCountLine.documentNumber, lineNumber: await stockCountLine._sortValue },
            ),
        );
    }

    await stockCountLine.$.set({ stockRecord });
}

export async function prepareStockDetail(stockCountLine: xtremStock.nodes.StockCountLine) {
    if (await stockCountLine.isNonStockItem) return;

    const stockDetail = await stockCountLine.stockDetail;

    // Check the consistency of the reasonCode
    let reasonCode = await stockDetail?.reasonCode;
    if (
        !reasonCode ||
        ((await reasonCode.quantityDecreaseAdjustment) && (await stockCountLine.quantityVariance) > 0) ||
        ((await reasonCode.quantityIncreaseAdjustment) && (await stockCountLine.quantityVariance) < 0)
    ) {
        const isQuantityIncreasing = (await stockCountLine.quantityInStockUnit) > 0;
        reasonCode = await stockCountLine.$.context
            .query(xtremMasterData.nodes.ReasonCode, {
                filter: {
                    quantityDecreaseAdjustment: !isQuantityIncreasing,
                    quantityIncreaseAdjustment: isQuantityIncreasing,
                    isDefault: true,
                },
            })
            .elementAt(0);
    }

    // ensure consistency between the line and the stockAdjustmentDetail
    // The quantity in the stock detail corresponds to the variance of quantity
    // in case the countedQuantityInStockUnit is set without changing the stockDetail via jsonStockDetails
    const stockDetailLot = await stockDetail?.stockDetailLot;
    let defaultStockDetailLot = {};
    if (!stockDetailLot && (await (await stockCountLine.item).lotManagement) !== 'notManaged') {
        defaultStockDetailLot = { stockDetailLot: { lot: await stockCountLine.lot } };
    }
    await stockCountLine.$.set({
        stockDetails: [
            {
                ...(stockDetail
                    ? { _action: 'update' as UpdateAction, _id: stockDetail._id }
                    : { ...defaultStockDetailLot }),
                site: await stockCountLine.site,
                item: await stockCountLine.item,
                status: await stockCountLine.stockStatus,
                location: await stockCountLine.location,
                stockUnit: await stockCountLine.stockUnit,
                owner: await stockCountLine.owner,
                reasonCode,
                quantityInStockUnit: ['excluded', 'toBeCounted'].includes(await stockCountLine.status)
                    ? 0 // by setting 0 we avoid controls on the quantity that are not relevant in this case (excluded or toBeCounted)
                    : (await stockCountLine.countedQuantityInStockUnit) - (await stockCountLine.quantityInStockUnit),
            },
        ],
    });
}
