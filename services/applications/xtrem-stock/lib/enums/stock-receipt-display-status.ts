import { EnumDataType } from '@sage/xtrem-core';

export enum StockReceiptDisplayStatusEnum {
    detailsRequired,
    detailsEntered,
    stockPostingInProgress,
    stockPostingError,
    received,
}

export type StockReceiptDisplayStatus = keyof typeof StockReceiptDisplayStatusEnum;

export const stockReceiptDisplayStatusDataType = new EnumDataType<StockReceiptDisplayStatus>({
    enum: StockReceiptDisplayStatusEnum,
    filename: __filename,
});
