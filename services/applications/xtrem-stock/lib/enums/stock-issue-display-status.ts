import { EnumDataType } from '@sage/xtrem-core';

export enum StockIssueDisplayStatusEnum {
    detailsRequired,
    detailsEntered,
    stockPostingInProgress,
    stockPostingError,
    issued,
}

export type StockIssueDisplayStatus = keyof typeof StockIssueDisplayStatusEnum;

export const StockIssueDisplayStatusDataType = new EnumDataType<StockIssueDisplayStatus>({
    enum: StockIssueDisplayStatusEnum,
    filename: __filename,
});
