import { EnumDataType } from '@sage/xtrem-core';

export enum StockReceiptLineDisplayStatusEnum {
    detailsRequired,
    detailsEntered,
    stockPostingInProgress,
    stockPostingError,
    received,
}

export type StockReceiptLineDisplayStatus = keyof typeof StockReceiptLineDisplayStatusEnum;

export const stockReceiptLineDisplayStatusDataType = new EnumDataType<StockReceiptLineDisplayStatus>({
    enum: StockReceiptLineDisplayStatusEnum,
    filename: __filename,
});
