import { EnumDataType } from '@sage/xtrem-core';

export enum StockAdjustmentDisplayStatusEnum {
    detailsRequired,
    detailsEntered,
    stockPostingInProgress,
    stockPostingError,
    adjusted,
}

export type StockAdjustmentDisplayStatus = keyof typeof StockAdjustmentDisplayStatusEnum;

export const StockAdjustmentDisplayStatusDataType = new EnumDataType<StockAdjustmentDisplayStatus>({
    enum: StockAdjustmentDisplayStatusEnum,
    filename: __filename,
});
