import { EnumDataType } from '@sage/xtrem-core';

export enum StandardCostRollUpResultLineStatusEnum {
    pending,
    inProgress,
    created,
    updated,
    error,
}

export type StandardCostRollUpResultLineStatus = keyof typeof StandardCostRollUpResultLineStatusEnum;

export const standardCostRollUpResultLineStatusDataType = new EnumDataType<StandardCostRollUpResultLineStatus>({
    enum: StandardCostRollUpResultLineStatusEnum,
    filename: __filename,
});
