import { EnumDataType } from '@sage/xtrem-core';

export enum StockIssueLineDisplayStatusEnum {
    detailsRequired,
    detailsEntered,
    stockPostingInProgress,
    stockPostingError,
    issued,
}

export type StockIssueLineDisplayStatus = keyof typeof StockIssueLineDisplayStatusEnum;

export const StockIssueLineDisplayStatusDataType = new EnumDataType<StockIssueLineDisplayStatus>({
    enum: StockIssueLineDisplayStatusEnum,
    filename: __filename,
});
