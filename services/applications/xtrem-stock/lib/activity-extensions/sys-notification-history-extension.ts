import * as xtremCommunication from '@sage/xtrem-communication';
import { ActivityExtension } from '@sage/xtrem-core';
import * as xtremStock from '..';

export const sysNotificationHistoryExtension = new ActivityExtension({
    extends: xtremCommunication.activities.sysNotificationHistory,
    __filename,
    permissions: [],
    operationGrants: {
        read: [
            {
                operations: ['lookup'],
                on: [
                    () => xtremStock.nodes.StockReceipt,
                    () => xtremStock.nodes.StockAdjustment,
                    () => xtremStock.nodes.StockIssue,
                    () => xtremStock.nodes.StockValueChange,
                    () => xtremStock.nodes.StockCount,
                ],
            },
        ],
    },
});
