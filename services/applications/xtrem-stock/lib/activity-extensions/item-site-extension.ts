import { ActivityExtension } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremStock from '..';

export const itemSiteExtension = new ActivityExtension({
    extends: xtremMasterData.activities.itemSite,
    __filename,
    permissions: [],
    operationGrants: {
        read: [
            {
                operations: ['read', 'updateAttributesAndDimensions'],
                on: [() => xtremStock.nodes.CostRollUpInputSet],
            },
            {
                operations: ['syncStockValueChange'],
                on: [() => xtremMasterData.nodes.ItemSiteCost],
            },
        ],
        create: [
            {
                operations: ['create', 'updateAttributesAndDimensions'],
                on: [() => xtremStock.nodes.CostRollUpInputSet],
            },
        ],
        update: [
            {
                operations: ['update', 'updateAttributesAndDimensions'],
                on: [() => xtremStock.nodes.CostRollUpInputSet],
            },
        ],
        delete: [
            {
                operations: ['delete', 'updateAttributesAndDimensions'],
                on: [() => xtremStock.nodes.CostRollUpInputSet],
            },
        ],
    },
});
