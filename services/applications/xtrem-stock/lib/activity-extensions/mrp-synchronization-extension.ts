import { ActivityExtension } from '@sage/xtrem-core';
import * as xtremMrpData from '@sage/xtrem-mrp-data';

export const mrpSynchronizationExtension = new ActivityExtension({
    extends: xtremMrpData.activities.mrpSynchronization,
    __filename,
    permissions: [],
    operationGrants: {
        manage: [
            {
                operations: ['mrpSynchronizeBom'],
                on: [() => xtremMrpData.nodes.MrpSynchronization],
            },
        ],
    },
});
