import { CustomSqlAction } from '@sage/xtrem-system';

export const setIssueDisplayStatus = new CustomSqlAction({
    description: 'Set the value of StockIssue.displayStatus',
    fixes: {
        notNullableColumns: [{ table: 'stock_issue_line', column: 'display_status' }],
    },
    body: async helper => {
        await helper.executeSql(`
            UPDATE ${helper.schemaName}.stock_issue_line as t0
            SET display_status =
            ( CASE
            WHEN (t0.stock_transaction_status = 'completed') THEN 'issued'::${helper.schemaName}.stock_issue_line_display_status_enum
            WHEN (t0.stock_transaction_status = 'error') THEN 'stockPostingError'::${helper.schemaName}.stock_issue_line_display_status_enum
            WHEN (t0.stock_transaction_status = 'inProgress') THEN 'stockPostingInProgress'::${helper.schemaName}.stock_issue_line_display_status_enum
            WHEN (t0.stock_detail_status = 'required') THEN 'detailsRequired'::${helper.schemaName}.stock_issue_line_display_status_enum
            ELSE 'detailsEntered'::${helper.schemaName}.stock_issue_line_display_status_enum
            END );

            Do $$ DECLARE
            srd RECORD;
            begin
            FOR srd IN (
                SELECT sr._id, sr._tenant_id
                FROM
                    ${helper.schemaName}.stock_issue sr)
            loop
                UPDATE ${helper.schemaName}.stock_issue t0
                SET display_status=
                ( CASE
                    WHEN ((select count (display_status) from ${helper.schemaName}.stock_issue_line srl
                            where srd._id =srl.document and srd._tenant_id = srl._tenant_id and srl.display_status = 'stockPostingError') > 0)
                        THEN 'stockPostingError'::${helper.schemaName}.stock_issue_display_status_enum
                    WHEN ((select count (display_status) from ${helper.schemaName}.stock_issue_line srl
                            where srd._id =srl.document and srd._tenant_id = srl._tenant_id and srl.display_status = 'stockPostingInProgress') > 0)
                        THEN 'stockPostingInProgress'::${helper.schemaName}.stock_issue_display_status_enum
                    WHEN ((select count (display_status) from ${helper.schemaName}.stock_issue_line srl
                            where srd._id =srl.document and srd._tenant_id = srl._tenant_id and srl.display_status = 'detailsRequired') > 0)
                        THEN 'detailsRequired'::${helper.schemaName}.stock_issue_display_status_enum
                    WHEN ((select count (display_status) from ${helper.schemaName}.stock_issue_line srl
                            where srd._id =srl.document and srd._tenant_id = srl._tenant_id and srl.display_status = 'detailsEntered') > 0)
                        THEN 'detailsEntered'::${helper.schemaName}.stock_issue_display_status_enum
                    else 'issued'::${helper.schemaName}.stock_issue_display_status_enum
                END)
                WHERE t0._id = srd._id and t0._tenant_id = srd._tenant_id;
            end loop;
        end $$`);
    },
});
