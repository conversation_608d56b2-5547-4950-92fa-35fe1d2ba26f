import { CustomSqlAction } from '@sage/xtrem-system';

export const setStockTransactionStatus = new CustomSqlAction({
    description: 'Set the value of StockReceipt.stockTransactionStatus',
    fixes: {
        notNullableColumns: [{ table: 'stock_receipt', column: 'stock_transaction_status' }],
    },
    body: async helper => {
        await helper.executeSql(`        
            Do $$ DECLARE
            srd RECORD;
            begin
            FOR srd IN (
                SELECT sr._id, sr._tenant_id
                FROM
                    ${helper.schemaName}.stock_receipt sr)
            loop
                UPDATE ${helper.schemaName}.stock_receipt t0
                SET stock_transaction_status=
                ( CASE
                    /* need to find out if any stock receipt lines have a stock transaction status of 'error' */
                    WHEN ((select count (stock_transaction_status) from ${helper.schemaName}.stock_receipt_line srl
                            where srd._id =srl.document and srd._tenant_id = srl._tenant_id and srl.stock_transaction_status = 'error') > 0)
                        THEN 'error'::${helper.schemaName}.stock_document_transaction_status_enum
                    /* need to get find out if any stock receipt lines have a stock transaction status of 'inProgress' */
                    WHEN ((select count (stock_transaction_status) from ${helper.schemaName}.stock_receipt_line srl
                            where srd._id =srl.document and srd._tenant_id = srl._tenant_id and srl.stock_transaction_status = 'inProgress') > 0)
                        THEN 'inProgress'::${helper.schemaName}.stock_document_transaction_status_enum
                    /* need to get find out if any stock receipt lines have a stock transaction status of 'draft' */
                    WHEN ((select count (stock_transaction_status) from ${helper.schemaName}.stock_receipt_line srl
                            where srd._id =srl.document and srd._tenant_id = srl._tenant_id and srl.stock_transaction_status = 'draft') > 0)
                        THEN 'draft'::${helper.schemaName}.stock_document_transaction_status_enum
                    /* need to get find out if any stock receipt lines have a stock transaction status of 'completed' */
                    WHEN ((select count (stock_transaction_status) from ${helper.schemaName}.stock_receipt_line srl
                            where srd._id =srl.document and srd._tenant_id = srl._tenant_id and srl.stock_transaction_status = 'completed') > 0)
                        THEN 'completed'::${helper.schemaName}.stock_document_transaction_status_enum
                    else 'draft'::${helper.schemaName}.stock_document_transaction_status_enum
                END)
                WHERE t0._id = srd._id and t0._tenant_id = srd._tenant_id;
            end loop;
        end $$`);
    },
});
