import { CustomSqlAction } from '@sage/xtrem-system';

export const setupStockDetailStatusOnStockAdjustmentLine = new CustomSqlAction({
    description: 'Set stock detail status on Stock adjustment line',
    fixes: { notNullableColumns: [{ table: 'stock_adjustment_line', column: 'stock_detail_status' }] },
    body: async helper => {
        await helper.executeSql(`DO $$
        BEGIN
            UPDATE ${helper.schemaName}.stock_adjustment_line adjustment_line
            SET stock_detail_status = (
            SELECT
            cast( CASE
                WHEN item.is_stock_managed = TRUE THEN
                    CASE
                        WHEN line.adjustment_quantity_in_stock_unit <> 0
                        AND line.adjustment_quantity_in_stock_unit = SUM(ABS(base_stock_line.quantity_in_stock_unit)) THEN 'entered'
                        ELSE 'required'
                    END
                ELSE 'notRequired'
            END as ${helper.schemaName}.stock_detail_status_enum)
        FROM
            ${helper.schemaName}.stock_adjustment_line line
        LEFT JOIN
            ${helper.schemaName}.stock_adjustment base_doc ON line.document = base_doc._id AND line._tenant_id = base_doc._tenant_id
        LEFT JOIN
            ${helper.schemaName}.base_stock_detail base_stock_line ON base_stock_line.document_line = line._id AND line._tenant_id = base_stock_line._tenant_id
        LEFT JOIN
            ${helper.schemaName}.item item ON line.item = item._id AND line._tenant_id = item._tenant_id
        WHERE
            line._id = adjustment_line._id AND line._tenant_id = adjustment_line._tenant_id
        GROUP BY
            line._tenant_id,
            base_doc._id,
            line.document,
            line._id,
            item.is_stock_managed,
            line.adjustment_quantity_in_stock_unit
            );
        END $$;`);
    },
});
