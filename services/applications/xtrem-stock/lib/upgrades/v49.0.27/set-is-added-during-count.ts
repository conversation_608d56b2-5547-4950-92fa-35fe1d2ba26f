import { CustomSqlAction } from '@sage/xtrem-system';

export const setIsAddedDuringCount = new CustomSqlAction({
    description: 'Set the value of StockCountLine',
    fixes: {
        notNullableColumns: [{ table: 'stock_count_line', column: 'is_added_during_count' }],
    },
    body: async helper => {
        await helper.executeSql(`
            UPDATE
                ${helper.schemaName}.stock_count_line AS t00
            SET
                is_added_during_count = (
                    CASE WHEN (t4.stock_record IS NOT NULL) THEN
                        FALSE
                    ELSE
                        (t5.status::text != 'toBeCounted')
                    END)
            FROM
                ${helper.schemaName}.stock_count_line AS t0
                INNER JOIN ${helper.schemaName}.stock_count AS t5 ON t0.document = t5."_id"
                    AND t0._tenant_id = t5._tenant_id
                LEFT JOIN ${helper.schemaName}.base_stock_detail AS t3 ON t0._id = t3.document_line
                    AND t0._tenant_id = t3._tenant_id
                LEFT JOIN ${helper.schemaName}.stock_adjustment_detail AS t4 ON t3._id = t4._id
                    AND t0._tenant_id = t4._tenant_id
            WHERE (t0._id = t00._id)
            AND (t00._tenant_id = t0._tenant_id)
            AND (t0.is_added_during_count IS NULL);`);
    },
});
