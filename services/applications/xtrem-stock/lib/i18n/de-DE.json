{"@sage/xtrem-stock/activity__cost_roll_up_input_set__name": "Eingabemenge Kostenkalkulation", "@sage/xtrem-stock/activity__stock_adjustment__name": "Bestandskorrektur", "@sage/xtrem-stock/activity__stock_change__name": "Bestandsänderung", "@sage/xtrem-stock/activity__stock_count__name": "Inventur", "@sage/xtrem-stock/activity__stock_issue__name": "Bestandsabgang", "@sage/xtrem-stock/activity__stock_receipt__name": "Bestandseingang", "@sage/xtrem-stock/activity__stock_reorder__name": "Wiederbeschaffung Bestand", "@sage/xtrem-stock/activity__stock_valuation_input_set__name": "Eingabemenge Bestandsbewertung", "@sage/xtrem-stock/activity__stock_value_change__name": "Änderung Bestandswert", "@sage/xtrem-stock/class__allocation-engine__system_error_during_allocation_process": "Der Reservierungsprozess wurde unterbrochen: {{errorMessage}}", "@sage/xtrem-stock/create_items_create": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-stock/data_types__adjustable_document_type_enum__name": "Enum an<PERSON><PERSON>", "@sage/xtrem-stock/data_types__standard_cost_roll_up_result_line_status_enum__name": "Enum Status Ergebniszeile Standardkostenkalkulation", "@sage/xtrem-stock/data_types__standard_cost_roll_up_status_enum__name": "Enum Status Standardkostenkalkulation", "@sage/xtrem-stock/data_types__stock_adjustment_display_status_enum__name": "Enum Status Anzeige Bestandskorrektur", "@sage/xtrem-stock/data_types__stock_count_line_status_enum__name": "Enum Status Inventurzeile", "@sage/xtrem-stock/data_types__stock_count_status_enum__name": "Enum Status Inventur", "@sage/xtrem-stock/data_types__stock_issue_display_status_enum__name": "Enum Status Anzeige Bestandsabgang", "@sage/xtrem-stock/data_types__stock_issue_line_display_status_enum__name": "Enum Status Anzeige Bestandsabgangszeile", "@sage/xtrem-stock/data_types__stock_receipt_display_status_enum__name": "Enum Status Anzeige Bestandseingang", "@sage/xtrem-stock/data_types__stock_receipt_line_display_status_enum__name": "Enum Status Anzeige Bestandseingangszeile", "@sage/xtrem-stock/data_types__stock_valuation_status_enum__name": "Enum Status Bestandsbewertung", "@sage/xtrem-stock/data_types__virtual_location_need_enum__name": "Enum Bedarf <PERSON>ller Lagerplatz", "@sage/xtrem-stock/edit-create-line": "Neue Zeile hinzufügen", "@sage/xtrem-stock/enums__adjustable_document_type__purchaseReceipt": "Wareneingang", "@sage/xtrem-stock/enums__adjustable_document_type__stockReceipt": "Bestandseingang", "@sage/xtrem-stock/enums__standard_cost_roll_up_result_line_status__created": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-stock/enums__standard_cost_roll_up_result_line_status__error": "<PERSON><PERSON>", "@sage/xtrem-stock/enums__standard_cost_roll_up_result_line_status__inProgress": "In Bearbeitung", "@sage/xtrem-stock/enums__standard_cost_roll_up_result_line_status__pending": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-stock/enums__standard_cost_roll_up_result_line_status__updated": "<PERSON>ktual<PERSON><PERSON>", "@sage/xtrem-stock/enums__standard_cost_roll_up_status__completed": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-stock/enums__standard_cost_roll_up_status__draft": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-stock/enums__standard_cost_roll_up_status__error": "<PERSON><PERSON>", "@sage/xtrem-stock/enums__standard_cost_roll_up_status__inProgress": "In Bearbeitung", "@sage/xtrem-stock/enums__stock_adjustment_display_status__adjusted": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-stock/enums__stock_adjustment_display_status__detailsEntered": "Details erfasst", "@sage/xtrem-stock/enums__stock_adjustment_display_status__detailsRequired": "Details erforderlich", "@sage/xtrem-stock/enums__stock_adjustment_display_status__stockPostingError": "Bestandsbuchungsfehler", "@sage/xtrem-stock/enums__stock_adjustment_display_status__stockPostingInProgress": "Bestandsbuchung in Bearbeitung", "@sage/xtrem-stock/enums__stock_count_line_status__counted": "Gezählt", "@sage/xtrem-stock/enums__stock_count_line_status__countInProgress": "Invent<PERSON> l<PERSON>", "@sage/xtrem-stock/enums__stock_count_line_status__excluded": "Ausgeschlossen", "@sage/xtrem-stock/enums__stock_count_line_status__toBeCounted": "<PERSON><PERSON>", "@sage/xtrem-stock/enums__stock_count_status__closed": "Abgeschlossen", "@sage/xtrem-stock/enums__stock_count_status__counted": "Gezählt", "@sage/xtrem-stock/enums__stock_count_status__countInProgress": "Invent<PERSON> l<PERSON>", "@sage/xtrem-stock/enums__stock_count_status__draft": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-stock/enums__stock_count_status__toBeCounted": "<PERSON><PERSON>", "@sage/xtrem-stock/enums__stock_document_status__completed": "Abgeschlossen", "@sage/xtrem-stock/enums__stock_document_status__error": "<PERSON><PERSON>", "@sage/xtrem-stock/enums__stock_issue_display_status__detailsEntered": "Details erfasst", "@sage/xtrem-stock/enums__stock_issue_display_status__detailsRequired": "Details erforderlich", "@sage/xtrem-stock/enums__stock_issue_display_status__issued": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-stock/enums__stock_issue_display_status__stockPostingError": "Bestandsbuchungsfehler", "@sage/xtrem-stock/enums__stock_issue_display_status__stockPostingInProgress": "Bestandsbuchung in Bearbeitung", "@sage/xtrem-stock/enums__stock_issue_line_display_status__detailsEntered": "Details erfasst", "@sage/xtrem-stock/enums__stock_issue_line_display_status__detailsRequired": "Details erforderlich", "@sage/xtrem-stock/enums__stock_issue_line_display_status__issued": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-stock/enums__stock_issue_line_display_status__stockPostingError": "Bestandsbuchungsfehler", "@sage/xtrem-stock/enums__stock_issue_line_display_status__stockPostingInProgress": "Bestandsbuchung in Bearbeitung", "@sage/xtrem-stock/enums__stock_receipt_display_status__detailsEntered": "Details erfasst", "@sage/xtrem-stock/enums__stock_receipt_display_status__detailsRequired": "Details erforderlich", "@sage/xtrem-stock/enums__stock_receipt_display_status__received": "Eingegangen", "@sage/xtrem-stock/enums__stock_receipt_display_status__stockPostingError": "Bestandsbuchungsfehler", "@sage/xtrem-stock/enums__stock_receipt_display_status__stockPostingInProgress": "Bestandsbuchung in Bearbeitung", "@sage/xtrem-stock/enums__stock_receipt_line_display_status__detailsEntered": "Details erfasst", "@sage/xtrem-stock/enums__stock_receipt_line_display_status__detailsRequired": "Details erforderlich", "@sage/xtrem-stock/enums__stock_receipt_line_display_status__received": "Eingegangen", "@sage/xtrem-stock/enums__stock_receipt_line_display_status__stockPostingError": "Bestandsbuchungsfehler", "@sage/xtrem-stock/enums__stock_receipt_line_display_status__stockPostingInProgress": "Bestandsbuchung in Bearbeitung", "@sage/xtrem-stock/enums__stock_valuation_status__completed": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-stock/enums__stock_valuation_status__draft": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-stock/enums__stock_valuation_status__error": "<PERSON><PERSON>", "@sage/xtrem-stock/enums__stock_valuation_status__inProgress": "In Bearbeitung", "@sage/xtrem-stock/enums__virtual_location_need__transfer": "Transfer", "@sage/xtrem-stock/events/control__location_for_wrong_site": "Der Lagerplatz sollte am Standort {{site}} sein.", "@sage/xtrem-stock/events/control__location_missing": "Der Lagerplatz ist erforderlich.", "@sage/xtrem-stock/events/control__location_not_managed": "Lagerplätze werden für den Standort {{site}} nicht verwaltet.", "@sage/xtrem-stock/function__stock_count_lib__line_already_exists_in_a_not_closed_stock-count": "Die Zeile ist bereits in der Inventur {{number}} vorhanden.", "@sage/xtrem-stock/functions__lot-lib__no_lot_information_created": "<PERSON><PERSON> erstellt.", "@sage/xtrem-stock/functions__lot-lib__no_lot_information_received": "<PERSON>ine Chargeninformatinen erhalten.", "@sage/xtrem-stock/functions__lot-lib__no_lot_sequence_number_definied": "<PERSON><PERSON><PERSON> den Artikel {{item}} ist kein Chargennummernkreis definiert.", "@sage/xtrem-stock/functions__stock__already_allocated": "Der Bestand ist bereits vollständig reserviert.", "@sage/xtrem-stock/functions__stock__location_mandatory_no_default": "Der Lagerplatz ist erforderlich und es ist kein Standardlagerplatz für diesen Lagerstandort definiert.", "@sage/xtrem-stock/functions__stock__lot_expired": "Ihr Artikel wird chargenverwaltet. Das Ablaufdatum  muss am oder nach dem {{date}} liegen.", "@sage/xtrem-stock/functions__stock__not_enough_stock": "Bestand am Standort {{site}} nicht ausreichend.", "@sage/xtrem-stock/functions__stock__potency_greater_than_100": "Die Konzentration kann nicht größer als 100 sein.", "@sage/xtrem-stock/functions__stock__status_filter_part_1": "{{#if is<PERSON>irst}} <PERSON><PERSON><PERSON><PERSON> für{{/if}}", "@sage/xtrem-stock/functions__stock__status_filter_part_2": "{{#if notFirst}} ODER {{/if}} Statustyp {{stockStatusType}}{{#if isLast}}.{{/if}}", "@sage/xtrem-stock/functions__stock__status_filter_part_3": "{{#if notFirst}} ODER{{/if}} Status {{stockStatus}}{{#if isLast}}.{{/if}}", "@sage/xtrem-stock/functions__stock__stock_change_quantity_must_be_greater_thant_0": "<PERSON>e können keine Bestandsänderung mit einer Menge von 0 erstellen.", "@sage/xtrem-stock/functions__stock__stock_record_not_found": "Bestandsdatensatz nicht gefunden. Die Menge kann nicht entnommen werden.", "@sage/xtrem-stock/functions__stock__stock_record_not_found_during_stock_change": "Die erfassten Bestandsinformationen sind nicht vorhanden.", "@sage/xtrem-stock/functions__stock__stock_status_mandatory_no_default": "DerBestandsstatus ist erforderlich und es ist kein Standardbestandsstatus für diesen Lagerstandort definiert.", "@sage/xtrem-stock/functions__stock__stock_update_failed": "Bestandsaktualisierung fehlgeschlagen. Überprüfen Sie Ihre Bestandsaktualisierungsparameter.", "@sage/xtrem-stock/functions__stock_engine__no_lot_information_received": "<PERSON>ine Chargeninformatinen erhalten.", "@sage/xtrem-stock/functions__stock-detail-lib__item_not_managed_on_site": "Der Artikel wird am Eingangsstandort nicht verwaltet.", "@sage/xtrem-stock/functions__stock-engine__lot_passed_for_an_item_not_lot_manage": "Der Artikel {{item}} wird nicht chargenverwaltet. Sie können nur Bestandsdetails mit Chargeninformationen für einen Artikel buchen, der chargenverwaltet wird.", "@sage/xtrem-stock/functions__stock-engine__not_enough_stock": "Der Bestand ist nicht ausreichend. Überprüfen Sie Ihre Parameter:\nArtikel {{item}}\nStandort {{site}}\nLagereinheit {{stockUnit}}\nCharge {{lot}}\nLagerplatz {{location}}\nBestandsstatus {{stockStatus}}\nBesitzer {{owner}}", "@sage/xtrem-stock/functions__stock-engine__not_enough_stock_allocation": "Um dieses Dokument zu buchen, muss <PERSON>and reserviert sein.", "@sage/xtrem-stock/functions__stock-engine__not_enough_stock_details": "Standardsektoren setzen", "@sage/xtrem-stock/functions__stock-engine__serial_number_already_in_stock": "Eine der in Artikel {{item}} verwendeten Seriennummern ist bereits im Bestand vorhanden. Prüfen Sie Ihre Seriennummern.", "@sage/xtrem-stock/functions__stock-engine__stock_update_failed_with_parameters": "Bestandsaktualisierung fehlgeschlagen. Überprüfen Sie Ihre Parameter:\nArtikel {{item}}\nStandort {{site}}\nLagereinheit {{stockUnit}}\nCharge {{lot}}\nLagerplatz {{location}}\nBestandsstatus {{stockStatus}}\nBesitzer {{owner}}", "@sage/xtrem-stock/functions__stock-engine__stock_value_update_failed_with_parameters": "Aktualisierung Bestandswert fehlgeschlagen. Überprüfen Sie Ihre Parameter:\nArtikel {{item}}\nStandort {{site}}\nMenge {{quantity}}", "@sage/xtrem-stock/functions--allocation-lib--allocate--not-enough-stock": "Die angeforderte Bestandsreservierung kann nicht verarbeitet werden, da diese die verfügbare Menge in Lagereinheit überschreitet.", "@sage/xtrem-stock/functions--allocation-lib--allocate--stock-not-in-status-accepted": "Die angeforderte Bestandsreservierung kann nicht verarbeitet werden, da der ausgewählte Bestand nicht den Status '{{acceptedStatus}}' hat.", "@sage/xtrem-stock/functions-stock-lib-no-item-site-supplier": "De<PERSON> Standort {{currentSite}} und dem Artikel {{currentItem}} ist kein Lieferant zugewiesen.", "@sage/xtrem-stock/functions-stock-lib-stock-journal-no-item-site-record": "Der Artikel {{currentItem}} wird nicht für den Standort {{currentSite}} verwaltet.", "@sage/xtrem-stock/functions-stock-no-stock-available": "<PERSON><PERSON><PERSON> den Standort {{site}} und den Artikel {{item}} ist kein Bestand verfügbar.", "@sage/xtrem-stock/node__stock__stock_status_incorrect_type": "Der Bestandsstatus muss eine StockStatus-Knoteninstanz oder ein StockStatusType-Enum sein.", "@sage/xtrem-stock/node__stock_count__resend_notification_for_finance": "Finanzbenachrichtigung für Inventur {{stockCount}} erneut senden", "@sage/xtrem-stock/node__stock_issue__resend_notification_for_finance": "Finanzbenachrichtigung für Bestandsabgang {{stockIssueNumber}} erneut senden", "@sage/xtrem-stock/node__stock_receipt__resend_notification_for_finance": "Finanzbenachrichtigung für Bestandseingangsnummer {{stockNumber}} erneut senden", "@sage/xtrem-stock/node__stock_value_change__resend_notification_for_finance": "Finanzbenachrichtigung für Bestandswertänderung {{stockValueChange}} erneut senden", "@sage/xtrem-stock/node-extensions__component_extension__property__availableQuantityInStockUnit": "Verfügbare Menge in Lagereinheit", "@sage/xtrem-stock/node-extensions__component_extension__property__hasStockShortage": "Hat Fehlbestand", "@sage/xtrem-stock/node-extensions__component_extension__property__stockOnHand": "<PERSON>orr<PERSON><PERSON> Bestand", "@sage/xtrem-stock/node-extensions__component_extension__property__stockShortageInStockUnit": "Fehlbestand in Lagereinheit", "@sage/xtrem-stock/node-extensions__item_extension__asyncMutation__createTestItems": "Testartikel er<PERSON>", "@sage/xtrem-stock/node-extensions__item_extension__asyncMutation__createTestItems__failed": "Testartikel erstellen fehlgeschlagen.", "@sage/xtrem-stock/node-extensions__item_extension__asyncMutation__createTestItems__parameter__itemId": "Artikel-ID", "@sage/xtrem-stock/node-extensions__item_extension__asyncMutation__createTestItems__parameter__quantity": "<PERSON><PERSON>", "@sage/xtrem-stock/node-extensions__item_extension__asyncMutation__createTestItems__parameter__stockCreation": "Bestandserstellung", "@sage/xtrem-stock/node-extensions__item_site_cost_extension__asyncMutation__standardCostRollUpCalculation": "Standardkostenkalkulationsberechnung", "@sage/xtrem-stock/node-extensions__item_site_cost_extension__asyncMutation__standardCostRollUpCalculation__failed": "Standardkostenkalkulationsberechnung fehlgeschlagen.", "@sage/xtrem-stock/node-extensions__item_site_cost_extension__asyncMutation__standardCostRollUpCalculation__parameter__inputSet": "Eingabemenge", "@sage/xtrem-stock/node-extensions__item_site_cost_extension__asyncMutation__syncStockValueChange": "Synchronisierung Änderung Bestandswert", "@sage/xtrem-stock/node-extensions__item_site_cost_extension__asyncMutation__syncStockValueChange__failed": "Synchronisierung Änderung Bestandswert fehlgeschlagen.", "@sage/xtrem-stock/node-extensions__item_site_cost_extension__error_message": "{{errorMessage}}: [Artikel: {{itemId}} - Standort: {{siteId}}]", "@sage/xtrem-stock/node-extensions__item_site_cost_extension__item_site_cost_creation_finished": "Erstellung Artikel-Standort-Kosten beendet.", "@sage/xtrem-stock/node-extensions__item_site_cost_extension__no_line_processed_message": "Fehler bei der Verarbeitung der Zeilen. Die Auswahl ist leer", "@sage/xtrem-stock/node-extensions__item_site_cost_extension__property__analyticalData": "Analytische Daten", "@sage/xtrem-stock/node-extensions__item_site_cost_extension__property__stockValueChange": "Änderung Bestandswert", "@sage/xtrem-stock/node-extensions__item_site_cost_extension__property__storedAttributes": "Gespeicherte Attribute", "@sage/xtrem-stock/node-extensions__item_site_cost_extension__property__storedDimensions": "Gespeicherte Sektoren", "@sage/xtrem-stock/node-extensions__item_site_cost_extension__success_cost_creation_status_message": "Artikel-Standort-<PERSON><PERSON> erstellt: [Artikel: {{itemId}} - Standort: {{siteId}}]", "@sage/xtrem-stock/node-extensions__item_site_cost_extension__success_cost_update_status_error_message": "Artikel-Standort-Kosten sind vorhanden und können nicht aktualisiert werden: [Artikel: {{itemId}} - Standort: {{siteId}}]", "@sage/xtrem-stock/node-extensions__item_site_cost_extension__success_cost_update_status_message": "Artikel-Standort-Kosten aktualisiert: [Artikel: {{itemId}} - Standort: {{siteId}}]", "@sage/xtrem-stock/node-extensions__item_site_extension__property__countStockRecords": "Datensätze Zählung Bestand", "@sage/xtrem-stock/node-extensions__item_site_extension__property__hasStockRecords": "Hat Bestandsdatensätze", "@sage/xtrem-stock/node-extensions__item_site_extension__property__lastCountDate": "Datum letzte Inventur", "@sage/xtrem-stock/node-extensions__item_site_extension__query__getStockValuation": "Bestandsbewertung abrufen", "@sage/xtrem-stock/node-extensions__item_site_extension__query__getStockValuation__failed": "Bestandsbewertung abrufen fehlgeschlagen.", "@sage/xtrem-stock/node-extensions__item_site_extension__query__getStockValuation__parameter__searchCriteria": "Suchkriterien", "@sage/xtrem-stock/node-extensions__mrp_calculation_extension__asyncMutation__deleteOldMrpCalculations": "Alte MRP-Berechnungen löschen", "@sage/xtrem-stock/node-extensions__mrp_calculation_extension__asyncMutation__deleteOldMrpCalculations__failed": "Alte MRP-Berechnungen löschen fehlgeschlagen.", "@sage/xtrem-stock/node-extensions__mrp_calculation_extension__asyncMutation__deleteOldMrpCalculations__parameter__numberOfDaysToKeep": "<PERSON><PERSON><PERSON> der zu behaltenden Tage", "@sage/xtrem-stock/node-extensions__mrp_calculation_extension__asyncMutation__deleteOldMrpCalculations__parameter__numberOfRecordsToKeep": "<PERSON><PERSON><PERSON> der zu behaltenden Datensätze", "@sage/xtrem-stock/node-extensions__mrp_synchronization_extension__asyncMutation__mrpSynchronizeBom": "Synchronisierung Stückliste MRP", "@sage/xtrem-stock/node-extensions__mrp_synchronization_extension__asyncMutation__mrpSynchronizeBom__parameter__resetSynchronization": "Synchronisierung zurücksetzen", "@sage/xtrem-stock/node-extensions__mrp_synchronization_extension__asyncMutation__mrpSynchronizeBom__parameter__synchronizeDeletions": "Löschvorgänge synchronisieren", "@sage/xtrem-stock/node-extensions__mrp_synchronization_extension__asyncMutation__mrpSynchronizeBom__parameter__type": "<PERSON><PERSON>", "@sage/xtrem-stock/node-extensions__stock_adjustment_detail_extension__property__item": "Artikel", "@sage/xtrem-stock/node-extensions__stock_adjustment_detail_extension__property__location": "Lagerplatz", "@sage/xtrem-stock/node-extensions__stock_adjustment_detail_extension__property__owner": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-stock/node-extensions__stock_adjustment_detail_extension__property__site": "<PERSON><PERSON>", "@sage/xtrem-stock/node-extensions__stock_adjustment_detail_extension__property__status": "Status", "@sage/xtrem-stock/node-extensions__stock_adjustment_detail_extension__property__stockDetailLot": "Charge Bestandsdetails", "@sage/xtrem-stock/node-extensions__stock_adjustment_detail_extension__property__stockUnit": "Lagereinheit", "@sage/xtrem-stock/node-extensions__stock_change_detail_extension__property__startingSerialNumber": "<PERSON><PERSON><PERSON><PERSON> von", "@sage/xtrem-stock/node-extensions__stock_detail_lot_extension__property__lot": "Charge", "@sage/xtrem-stock/node-extensions__stock_detail_lot_extension__property__lotNumber": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-stock/node-extensions__stock_extension__asyncMutation__adjust": "Korrigieren", "@sage/xtrem-stock/node-extensions__stock_extension__asyncMutation__adjust__failed": "Korrigieren fehlgeschlagen.", "@sage/xtrem-stock/node-extensions__stock_extension__asyncMutation__adjust__parameter__payload": "Payload", "@sage/xtrem-stock/node-extensions__stock_extension__asyncMutation__change": "Änderung", "@sage/xtrem-stock/node-extensions__stock_extension__asyncMutation__change__failed": "Änderung fehlgeschlagen.", "@sage/xtrem-stock/node-extensions__stock_extension__asyncMutation__change__parameter__payload": "Payload", "@sage/xtrem-stock/node-extensions__stock_extension__asyncMutation__changeValue": "<PERSON><PERSON>", "@sage/xtrem-stock/node-extensions__stock_extension__asyncMutation__changeValue__failed": "Wert ändern fehlgeschlagen.", "@sage/xtrem-stock/node-extensions__stock_extension__asyncMutation__changeValue__parameter__payload": "Payload", "@sage/xtrem-stock/node-extensions__stock_extension__asyncMutation__correct": "Korrigieren", "@sage/xtrem-stock/node-extensions__stock_extension__asyncMutation__correct__failed": "Korrigieren fehlgeschlagen.", "@sage/xtrem-stock/node-extensions__stock_extension__asyncMutation__correct__parameter__payload": "Payload", "@sage/xtrem-stock/node-extensions__stock_extension__asyncMutation__issue": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-stock/node-extensions__stock_extension__asyncMutation__issue__failed": "Abgang fehlgeschlagen.", "@sage/xtrem-stock/node-extensions__stock_extension__asyncMutation__issue__parameter__payload": "Payload", "@sage/xtrem-stock/node-extensions__stock_extension__asyncMutation__receive": "Empfangen", "@sage/xtrem-stock/node-extensions__stock_extension__asyncMutation__receive__failed": "Empfangen fehlgeschlagen.", "@sage/xtrem-stock/node-extensions__stock_extension__asyncMutation__receive__parameter__payload": "Payload", "@sage/xtrem-stock/node-extensions__stock_extension__asyncMutation__transfer": "Transfer", "@sage/xtrem-stock/node-extensions__stock_extension__asyncMutation__transfer__failed": "Übertragen fehlgeschlagen.", "@sage/xtrem-stock/node-extensions__stock_extension__asyncMutation__transfer__parameter__payload": "Payload", "@sage/xtrem-stock/node-extensions__stock_extension__property__isStockCountingInProgress": "Ist Inventur wird durchgeführt", "@sage/xtrem-stock/node-extensions__stock_extension__property__stockCountingInProgress": "Inventur wird durchgeführt", "@sage/xtrem-stock/node-extensions__stock_issue_detail_extension__property__startingSerialNumber": "<PERSON><PERSON><PERSON><PERSON> von", "@sage/xtrem-stock/nodes__allocation_listener__asyncMutation__allocate": "Reservieren", "@sage/xtrem-stock/nodes__allocation_listener__asyncMutation__allocate__failed": "Reservieren fehlgeschlagen.", "@sage/xtrem-stock/nodes__allocation_listener__asyncMutation__allocate__parameter__payload": "Payload", "@sage/xtrem-stock/nodes__allocation_listener__asyncMutation__allocate__parameter__stockAllocationParameters": "Parameter Bestandsreservierung", "@sage/xtrem-stock/nodes__allocation_listener__node_name": "Listener <PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-stock/nodes__cost_roll_up_input_set__asyncMutation__asyncExport": "Export", "@sage/xtrem-stock/nodes__cost_roll_up_input_set__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-stock/nodes__cost_roll_up_input_set__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-stock/nodes__cost_roll_up_input_set__mutation__updateAttributesAndDimensions": "Attribute und Sektoren aktualisieren", "@sage/xtrem-stock/nodes__cost_roll_up_input_set__mutation__updateAttributesAndDimensions__failed": "Attribute und Sektoren aktualisieren fehlgeschlagen.", "@sage/xtrem-stock/nodes__cost_roll_up_input_set__mutation__updateAttributesAndDimensions__parameter__costRollUpInputSet": "Eingabemenge Kostenkalkulation", "@sage/xtrem-stock/nodes__cost_roll_up_input_set__mutation__updateAttributesAndDimensions__parameter__storedAttributes": "Gespeicherte Attribute", "@sage/xtrem-stock/nodes__cost_roll_up_input_set__mutation__updateAttributesAndDimensions__parameter__storedDimensions": "Gespeicherte Sektoren", "@sage/xtrem-stock/nodes__cost_roll_up_input_set__node_name": "Eingabemenge Kostenkalkulation", "@sage/xtrem-stock/nodes__cost_roll_up_input_set__property__commodityCode": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-stock/nodes__cost_roll_up_input_set__property__costCategory": "Kostenkategorie", "@sage/xtrem-stock/nodes__cost_roll_up_input_set__property__fromDate": "<PERSON><PERSON> von", "@sage/xtrem-stock/nodes__cost_roll_up_input_set__property__includesRouting": "Arbeitsplan eingeschlossen", "@sage/xtrem-stock/nodes__cost_roll_up_input_set__property__itemCategory": "Artikelkategorie", "@sage/xtrem-stock/nodes__cost_roll_up_input_set__property__items": "Artikel", "@sage/xtrem-stock/nodes__cost_roll_up_input_set__property__quantity": "<PERSON><PERSON>", "@sage/xtrem-stock/nodes__cost_roll_up_input_set__property__resultLines": "Ergebniszeilen", "@sage/xtrem-stock/nodes__cost_roll_up_input_set__property__site": "<PERSON><PERSON>", "@sage/xtrem-stock/nodes__cost_roll_up_input_set__property__status": "Status", "@sage/xtrem-stock/nodes__cost_roll_up_input_set__property__user": "<PERSON><PERSON><PERSON>", "@sage/xtrem-stock/nodes__cost_roll_up_input_set__property__usesComponentStandardCost": "Verwendet Komponentenstandardkosten", "@sage/xtrem-stock/nodes__cost_roll_up_result_line__asyncMutation__asyncExport": "Export", "@sage/xtrem-stock/nodes__cost_roll_up_result_line__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-stock/nodes__cost_roll_up_result_line__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-stock/nodes__cost_roll_up_result_line__bulkMutation__createItemSiteCostsFromCostRollUpResults": "Artikel-Standort-Kosten aus Kostenkalkulationsergebnissen erstellen", "@sage/xtrem-stock/nodes__cost_roll_up_result_line__bulkMutation__createItemSiteCostsFromCostRollUpResults__failed": "Artikel-Standort-Kosten aus Kostenkalkulationsergebnissen erstellen fehlgeschlagen.", "@sage/xtrem-stock/nodes__cost_roll_up_result_line__item_site_cost_creation_finished": "Erstellung Artikel-Standort-Kosten beendet.", "@sage/xtrem-stock/nodes__cost_roll_up_result_line__no_line_processed_message": "Fehler bei der Verarbeitung der Zeilen. Die Auswahl ist leer.", "@sage/xtrem-stock/nodes__cost_roll_up_result_line__node_name": "Ergebniszeile Kostenkalkulation", "@sage/xtrem-stock/nodes__cost_roll_up_result_line__property__analyticalData": "Analytische Daten", "@sage/xtrem-stock/nodes__cost_roll_up_result_line__property__creationErrorMessage": "Fehlermeldung Erstellung", "@sage/xtrem-stock/nodes__cost_roll_up_result_line__property__creationStatus": "Status Erstellung", "@sage/xtrem-stock/nodes__cost_roll_up_result_line__property__currentLaborCost": "Aktuelle Arbeitskraftkosten", "@sage/xtrem-stock/nodes__cost_roll_up_result_line__property__currentMachineCost": "Aktuelle Maschinenkosten", "@sage/xtrem-stock/nodes__cost_roll_up_result_line__property__currentMaterialCost": "Aktuelle Materialkosten", "@sage/xtrem-stock/nodes__cost_roll_up_result_line__property__currentToolCost": "Aktuelle Werkzeugkosten", "@sage/xtrem-stock/nodes__cost_roll_up_result_line__property__currentTotalCost": "Aktuelle Gesamtkosten", "@sage/xtrem-stock/nodes__cost_roll_up_result_line__property__inputSet": "Eingabemenge", "@sage/xtrem-stock/nodes__cost_roll_up_result_line__property__item": "Artikel", "@sage/xtrem-stock/nodes__cost_roll_up_result_line__property__laborCost": "Arbeitskraftkosten", "@sage/xtrem-stock/nodes__cost_roll_up_result_line__property__machineCost": "Maschinenkosten", "@sage/xtrem-stock/nodes__cost_roll_up_result_line__property__materialCost": "Materialkosten", "@sage/xtrem-stock/nodes__cost_roll_up_result_line__property__storedAttributes": "Gespeicherte Attribute", "@sage/xtrem-stock/nodes__cost_roll_up_result_line__property__storedDimensions": "Gespeicherte Sektoren", "@sage/xtrem-stock/nodes__cost_roll_up_result_line__property__subAssemblyLines": "Zeilen Unterbaugruppen", "@sage/xtrem-stock/nodes__cost_roll_up_result_line__property__toolCost": "Werkzeugkosten", "@sage/xtrem-stock/nodes__cost_roll_up_result_line__property__totalCost": "<PERSON><PERSON> gesamt", "@sage/xtrem-stock/nodes__cost_roll_up_sub_assembly__asyncMutation__asyncExport": "Export", "@sage/xtrem-stock/nodes__cost_roll_up_sub_assembly__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-stock/nodes__cost_roll_up_sub_assembly__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-stock/nodes__cost_roll_up_sub_assembly__node_name": "Untergruppe Kostenkalkulation", "@sage/xtrem-stock/nodes__cost_roll_up_sub_assembly__property__calculationQuantity": "Berechnungsmenge", "@sage/xtrem-stock/nodes__cost_roll_up_sub_assembly__property__componentNumber": "Komponentennummer", "@sage/xtrem-stock/nodes__cost_roll_up_sub_assembly__property__currentLaborCost": "Aktuelle Arbeitskraftkosten", "@sage/xtrem-stock/nodes__cost_roll_up_sub_assembly__property__currentMachineCost": "Aktuelle Maschinenkosten", "@sage/xtrem-stock/nodes__cost_roll_up_sub_assembly__property__currentMaterialCost": "Aktuelle Materialkosten", "@sage/xtrem-stock/nodes__cost_roll_up_sub_assembly__property__currentToolCost": "Aktuelle Werkzeugkosten", "@sage/xtrem-stock/nodes__cost_roll_up_sub_assembly__property__currentTotalCost": "Aktuelle Gesamtkosten", "@sage/xtrem-stock/nodes__cost_roll_up_sub_assembly__property__item": "Artikel", "@sage/xtrem-stock/nodes__cost_roll_up_sub_assembly__property__laborCost": "Arbeitskraftkosten", "@sage/xtrem-stock/nodes__cost_roll_up_sub_assembly__property__machineCost": "Maschinenkosten", "@sage/xtrem-stock/nodes__cost_roll_up_sub_assembly__property__materialCost": "Materialkosten", "@sage/xtrem-stock/nodes__cost_roll_up_sub_assembly__property__resultLine": "Ergebniszeile", "@sage/xtrem-stock/nodes__cost_roll_up_sub_assembly__property__stockUnit": "", "@sage/xtrem-stock/nodes__cost_roll_up_sub_assembly__property__toolCost": "Werkzeugkosten", "@sage/xtrem-stock/nodes__cost_roll_up_sub_assembly__property__totalCost": "<PERSON><PERSON> gesamt", "@sage/xtrem-stock/nodes__item_site_cost__success_notification_title": "Standardkostenkalkulationsberechnung abgeschlossen.", "@sage/xtrem-stock/nodes__item-site-cost__failed_deletion_impossible_if_transactions": "Löschen nicht zulässig. Das Startdatum der Artikel-Standortkosten ist gleich dem aktuellen Datum und für diese Artikel-Standort-Kosten besteht eine Bestandswertänderung.", "@sage/xtrem-stock/nodes__item-site-extension__item_site_not_stock_managed": "Dieser Artikel-Standort wird nicht bestandsgeführt.", "@sage/xtrem-stock/nodes__mrp_calculation-extension__delete_old_mrp_calculations": "Alte MRP-Berechnungen gelöscht: {{number}}.", "@sage/xtrem-stock/nodes__mrp_calculation-extension__deleting_old_mrp_calculations": "Löschen von alten MRP-Berechnungen mit einem früheren Datum als: {{deletionDate}}.", "@sage/xtrem-stock/nodes__mrp_calculation-extension__no_old_mrp_calculations": "<PERSON>ine alten MRP-Berechnungen zu löschen.", "@sage/xtrem-stock/nodes__mrp_calculation-extension__start_delete_old_mrp_calculations": "Löschen alter MRP-Berechnungen wird gestartet.", "@sage/xtrem-stock/nodes__stock_adjustment__asyncMutation__asyncExport": "Export", "@sage/xtrem-stock/nodes__stock_adjustment__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-stock/nodes__stock_adjustment__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-stock/nodes__stock_adjustment__mutation__postToStock": "Buchung in Bestand", "@sage/xtrem-stock/nodes__stock_adjustment__mutation__postToStock__failed": "Buchung in Bestand fehlgeschlagen.", "@sage/xtrem-stock/nodes__stock_adjustment__mutation__postToStock__parameter__documentIds": "Dokument-IDs", "@sage/xtrem-stock/nodes__stock_adjustment__mutation__repost": "<PERSON><PERSON><PERSON> buchen", "@sage/xtrem-stock/nodes__stock_adjustment__mutation__repost__failed": "Erneut buchen fehlgeschlagen.", "@sage/xtrem-stock/nodes__stock_adjustment__mutation__repost__parameter__documentLines": "Dokumentzeilen", "@sage/xtrem-stock/nodes__stock_adjustment__mutation__repost__parameter__stockAdjustment": "Bestandskorrektur", "@sage/xtrem-stock/nodes__stock_adjustment__mutation__resendNotificationForFinance": "Benachrichtigung für Finanzen erneut senden", "@sage/xtrem-stock/nodes__stock_adjustment__mutation__resendNotificationForFinance__failed": "Benachrichtigung für Finanzen erneut senden fehlgeschlagen.", "@sage/xtrem-stock/nodes__stock_adjustment__mutation__resendNotificationForFinance__parameter__stockAdjustment": "Bestandskorrektur", "@sage/xtrem-stock/nodes__stock_adjustment__mutation__resynchronizeStockTransactionStatus": "Status Bestandstransaktion erneut synchronisieren", "@sage/xtrem-stock/nodes__stock_adjustment__mutation__resynchronizeStockTransactionStatus__failed": "Status Bestandstransaktion erneut synchronisieren fehlgeschlagen.", "@sage/xtrem-stock/nodes__stock_adjustment__mutation__resynchronizeStockTransactionStatus__parameter__stockAdjustment": "Bestandskorrektur", "@sage/xtrem-stock/nodes__stock_adjustment__node_name": "Bestandskorrektur", "@sage/xtrem-stock/nodes__stock_adjustment__property__description": "Bezeichnung", "@sage/xtrem-stock/nodes__stock_adjustment__property__displayStatus": "Status anzeigen", "@sage/xtrem-stock/nodes__stock_adjustment__property__documentDate": "Dokumentdatum", "@sage/xtrem-stock/nodes__stock_adjustment__property__effectiveDate": "Aktivierungsdatum", "@sage/xtrem-stock/nodes__stock_adjustment__property__financeIntegrationStatus": "Status Integration Finanzen", "@sage/xtrem-stock/nodes__stock_adjustment__property__financialSite": "Buchhaltungsstandort", "@sage/xtrem-stock/nodes__stock_adjustment__property__forceUpdateForFinance": "Aktualisierung für Finanzen erzwingen", "@sage/xtrem-stock/nodes__stock_adjustment__property__forceUpdateForStock": "Aktualisierung für Bestand erzwingen", "@sage/xtrem-stock/nodes__stock_adjustment__property__isSetDimensionsMainListHidden": "Ist Hauptliste Sektoren setzen verborgen", "@sage/xtrem-stock/nodes__stock_adjustment__property__lines": "<PERSON><PERSON><PERSON>", "@sage/xtrem-stock/nodes__stock_adjustment__property__number": "<PERSON><PERSON><PERSON>", "@sage/xtrem-stock/nodes__stock_adjustment__property__postingDetails": "Buchungsdetails", "@sage/xtrem-stock/nodes__stock_adjustment__property__reasonCode": "Grundcode", "@sage/xtrem-stock/nodes__stock_adjustment__property__status": "Status", "@sage/xtrem-stock/nodes__stock_adjustment__property__stockSite": "Lagerstandort", "@sage/xtrem-stock/nodes__stock_adjustment__property__stockTransactionStatus": "Status Bestandstransaktion", "@sage/xtrem-stock/nodes__stock_adjustment__property__transactionCurrency": "Transaktionswährung", "@sage/xtrem-stock/nodes__stock_adjustment_detail_extension__lot_mandatory": "<PERSON>e müssen eine Charge für den Artikel {{itemId}} auswählen. Inventur {{number}}, <PERSON><PERSON><PERSON> {{lineNumber}}.", "@sage/xtrem-stock/nodes__stock_adjustment_line__asyncMutation__asyncExport": "Export", "@sage/xtrem-stock/nodes__stock_adjustment_line__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-stock/nodes__stock_adjustment_line__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-stock/nodes__stock_adjustment_line__cannot_update_completed_line": "Eine Zeile mit Status Korrigiert kann nicht aktualisiert werden.", "@sage/xtrem-stock/nodes__stock_adjustment_line__incorrect_valuation_method": "Der Artikel {{item}} muss mit einer Bewertungsmethode für den gewichteten Durschnittspreis verknüpft sein.", "@sage/xtrem-stock/nodes__stock_adjustment_line__mutation__setDimension": "<PERSON><PERSON><PERSON> <PERSON>", "@sage/xtrem-stock/nodes__stock_adjustment_line__mutation__setDimension__failed": "<PERSON><PERSON><PERSON> setzen fehlgeschlagen.", "@sage/xtrem-stock/nodes__stock_adjustment_line__mutation__setDimension__parameter__stockAdjustmentLine": "Bestandskorrekturzeile", "@sage/xtrem-stock/nodes__stock_adjustment_line__mutation__setDimension__parameter__storedAttributes": "Gespeicherte Attribute", "@sage/xtrem-stock/nodes__stock_adjustment_line__mutation__setDimension__parameter__storedDimensions": "Gespeicherte Sektoren", "@sage/xtrem-stock/nodes__stock_adjustment_line__node_name": "Bestandskorrekturzeile", "@sage/xtrem-stock/nodes__stock_adjustment_line__property__adjustmentQuantityInStockUnit": "Korrekturmenge in Lagereinheit", "@sage/xtrem-stock/nodes__stock_adjustment_line__property__analyticalData": "Analytische Daten", "@sage/xtrem-stock/nodes__stock_adjustment_line__property__computedAttributes": "Berechnete Attribute", "@sage/xtrem-stock/nodes__stock_adjustment_line__property__displayStatus": "Status anzeigen", "@sage/xtrem-stock/nodes__stock_adjustment_line__property__document": "Dokument", "@sage/xtrem-stock/nodes__stock_adjustment_line__property__documentId": "Dokument-ID", "@sage/xtrem-stock/nodes__stock_adjustment_line__property__documentNumber": "Dokumentnummer", "@sage/xtrem-stock/nodes__stock_adjustment_line__property__item": "Artikel", "@sage/xtrem-stock/nodes__stock_adjustment_line__property__itemSite": "Artikel-Standort", "@sage/xtrem-stock/nodes__stock_adjustment_line__property__jsonStockDetails": "Bestandsdetails JSON", "@sage/xtrem-stock/nodes__stock_adjustment_line__property__location": "Lagerplatz", "@sage/xtrem-stock/nodes__stock_adjustment_line__property__lot": "Charge", "@sage/xtrem-stock/nodes__stock_adjustment_line__property__newStockQuantity": "Neue Bestandsmenge", "@sage/xtrem-stock/nodes__stock_adjustment_line__property__newStockValue": "Neuer Bestandswert", "@sage/xtrem-stock/nodes__stock_adjustment_line__property__newUnitCost": "Neue Stückkosten", "@sage/xtrem-stock/nodes__stock_adjustment_line__property__orderCost": "Auftragskosten", "@sage/xtrem-stock/nodes__stock_adjustment_line__property__quantityInStockUnit": "Menge in Lagereinheit", "@sage/xtrem-stock/nodes__stock_adjustment_line__property__site": "<PERSON><PERSON>", "@sage/xtrem-stock/nodes__stock_adjustment_line__property__stockDetails": "Bestandsdetails", "@sage/xtrem-stock/nodes__stock_adjustment_line__property__stockDetailStatus": "Status Bestandsdetails", "@sage/xtrem-stock/nodes__stock_adjustment_line__property__stockMovements": "Bestandsbewegungen", "@sage/xtrem-stock/nodes__stock_adjustment_line__property__stockStatus": "Bestandsstatus", "@sage/xtrem-stock/nodes__stock_adjustment_line__property__stockTransactions": "Bestandstransaktion", "@sage/xtrem-stock/nodes__stock_adjustment_line__property__stockTransactionStatus": "Status Bestandstransaktion", "@sage/xtrem-stock/nodes__stock_adjustment_line__property__stockUnit": "Lagereinheit", "@sage/xtrem-stock/nodes__stock_adjustment_line__property__stockValue": "Bestandswert", "@sage/xtrem-stock/nodes__stock_adjustment_line__property__storedAttributes": "Gespeicherte Attribute", "@sage/xtrem-stock/nodes__stock_adjustment_line__property__storedDimensions": "Gespeicherte Sektoren", "@sage/xtrem-stock/nodes__stock_adjustment_line__property__unitCost": "Stückkosten", "@sage/xtrem-stock/nodes__stock_adjustment_line__property__valuedCost": "Bewertete Kosten", "@sage/xtrem-stock/nodes__stock_change___duplicated_serial_number": "Es sind doppelte Seriennummern vorhanden. <PERSON><PERSON><PERSON>, dass jede Zeile eindeutige Seriennummern umfasst.", "@sage/xtrem-stock/nodes__stock_change___wrong_number_of_serial_numbers": "Die Anzahl zugewiesener Seriennummern muss der Menge der Zeile entsprechen. Fügen Sie Seriennummern hinzu, um die Menge anzupassen.", "@sage/xtrem-stock/nodes__stock_change__asyncMutation__asyncExport": "Export", "@sage/xtrem-stock/nodes__stock_change__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-stock/nodes__stock_change__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-stock/nodes__stock_change__mutation__postToStock": "Buchung in Bestand", "@sage/xtrem-stock/nodes__stock_change__mutation__postToStock__failed": "Buchung in Bestand fehlgeschlagen.", "@sage/xtrem-stock/nodes__stock_change__mutation__postToStock__parameter__documentIds": "Dokument-IDs", "@sage/xtrem-stock/nodes__stock_change__node_name": "Bestandsänderung", "@sage/xtrem-stock/nodes__stock_change__property__description": "Bezeichnung", "@sage/xtrem-stock/nodes__stock_change__property__effectiveDate": "Aktivierungsdatum", "@sage/xtrem-stock/nodes__stock_change__property__forceUpdateForStock": "Aktualisierung für Bestand erzwingen", "@sage/xtrem-stock/nodes__stock_change__property__item": "Artikel", "@sage/xtrem-stock/nodes__stock_change__property__itemSite": "Artikel-Standort", "@sage/xtrem-stock/nodes__stock_change__property__lines": "<PERSON><PERSON><PERSON>", "@sage/xtrem-stock/nodes__stock_change__property__location": "Lagerplatz", "@sage/xtrem-stock/nodes__stock_change__property__lot": "Charge", "@sage/xtrem-stock/nodes__stock_change__property__number": "<PERSON><PERSON><PERSON>", "@sage/xtrem-stock/nodes__stock_change__property__owner": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-stock/nodes__stock_change__property__postingError": "Buchungsfehler", "@sage/xtrem-stock/nodes__stock_change__property__status": "Status", "@sage/xtrem-stock/nodes__stock_change__property__stockSite": "Lagerstandort", "@sage/xtrem-stock/nodes__stock_change__property__stockStatus": "Bestandsstatus", "@sage/xtrem-stock/nodes__stock_change__property__stockTransactionStatus": "Status Bestandstransaktion", "@sage/xtrem-stock/nodes__stock_change__property__stockUnit": "Lagereinheit", "@sage/xtrem-stock/nodes__stock_change_line__asyncMutation__asyncExport": "Export", "@sage/xtrem-stock/nodes__stock_change_line__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-stock/nodes__stock_change_line__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-stock/nodes__stock_change_line__location_site_must_match": "Der Lagerstandort und der Bestandsstandort müssen gleich sein.", "@sage/xtrem-stock/nodes__stock_change_line__no_change": "<PERSON>ine Änderungen in einer Zeile zu verarbeiten. Verwenden Sie einen anderen Bestandsstatus oder einen anderen Lagerplatz für diese Zeile.", "@sage/xtrem-stock/nodes__stock_change_line__node_name": "Bestandsänderungszeile", "@sage/xtrem-stock/nodes__stock_change_line__property__document": "Dokument", "@sage/xtrem-stock/nodes__stock_change_line__property__documentId": "Dokument-ID", "@sage/xtrem-stock/nodes__stock_change_line__property__documentNumber": "Dokumentnummer", "@sage/xtrem-stock/nodes__stock_change_line__property__item": "Artikel", "@sage/xtrem-stock/nodes__stock_change_line__property__jsonStockDetails": "Bestandsdetails JSON", "@sage/xtrem-stock/nodes__stock_change_line__property__location": "Lagerplatz", "@sage/xtrem-stock/nodes__stock_change_line__property__orderCost": "Auftragskosten", "@sage/xtrem-stock/nodes__stock_change_line__property__quantityInStockUnit": "Menge in Lagereinheit", "@sage/xtrem-stock/nodes__stock_change_line__property__stockDetails": "Bestandsdetails", "@sage/xtrem-stock/nodes__stock_change_line__property__stockStatus": "Bestandsstatus", "@sage/xtrem-stock/nodes__stock_change_line__property__stockTransactions": "Bestandstransaktion", "@sage/xtrem-stock/nodes__stock_change_line__property__stockTransactionStatus": "Status Bestandstransaktion", "@sage/xtrem-stock/nodes__stock_change_line__property__valuedCost": "Bewertete Kosten", "@sage/xtrem-stock/nodes__stock_count__asyncMutation__asyncExport": "Export", "@sage/xtrem-stock/nodes__stock_count__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-stock/nodes__stock_count__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-stock/nodes__stock_count__asyncMutation__confirmStockCount": "Inventur bestätigen", "@sage/xtrem-stock/nodes__stock_count__asyncMutation__confirmStockCount__failed": "Inventur bestätigen fehlgeschlagen.", "@sage/xtrem-stock/nodes__stock_count__asyncMutation__confirmStockCount__parameter__selectedRecords": "Ausgewählte Datensätze", "@sage/xtrem-stock/nodes__stock_count__asyncMutation__confirmStockCount__parameter__stockCount": "Inventur", "@sage/xtrem-stock/nodes__stock_count__asyncMutation__confirmZeroQuantity": "Nullmenge bestätigen", "@sage/xtrem-stock/nodes__stock_count__asyncMutation__confirmZeroQuantity__failed": "Nullmenge bestätigen fehlgeschlagen.", "@sage/xtrem-stock/nodes__stock_count__asyncMutation__confirmZeroQuantity__parameter__number": "<PERSON><PERSON><PERSON>", "@sage/xtrem-stock/nodes__stock_count__cant_repost_stock_count_when_status_is_not_failed": "<PERSON>e können eine Inventur nur erneut buchen, wenn der Status 'Fehlgeschlagen' oder 'Nicht erfasst' ist.", "@sage/xtrem-stock/nodes__stock_count__closed_confirm_zero_cannot_be_performed": "Sie können die Mengen in einer abgeschlossenen Inventur nicht aktualisieren: Inventur {{number}}.", "@sage/xtrem-stock/nodes__stock_count__company_has_no_stock_posting": "Der Bestand wurde nicht auf dieses Unternehmen gebucht", "@sage/xtrem-stock/nodes__stock_count__confirm_stock_completed": "Inventurzeilen erstellt: {{number}}.", "@sage/xtrem-stock/nodes__stock_count__confirm_stock_count_none_stock_records_to_stock_count_line": "Aus Nicht-Bestandsdatensätzen erstellte Inventurzeilen: {{itemSiteCounter}}.", "@sage/xtrem-stock/nodes__stock_count__confirm_stock_count_stock_record_excluded": "Der Bestandsdatensatz für den Artikel {{item}} ist ausgeschlossen. Er befindet sich bereits in einer anderen Inventur: {{existingRecord}}.", "@sage/xtrem-stock/nodes__stock_count__confirm_stock_count_stock_records_to_stock_count_line": "Aus Bestandsdatensätzen erstellte Inventurzeilen: {{stockCounter}}.", "@sage/xtrem-stock/nodes__stock_count__counted_quantity_less_than_allocated_quantity": "Für die Inventur {{number}} muss die gezählte Menge {{countedQuantity}} für {{item}} kleiner als die reservierte Menge {{totalAllocated}} sein.", "@sage/xtrem-stock/nodes__stock_count__counted_stock_record_not_found": "Der Inventurdatensatz für den Artikel {{item}} wurde seit Beginn der Inventur geändert. Si<PERSON> müssen diese Zeile von der Inventur ausschließen.", "@sage/xtrem-stock/nodes__stock_count__counting_is_not_finished_yet": "Eine Inventur kann nicht gebucht werden, wenn die Zählung noch nicht beendet wurde: Inventur {{number}}.", "@sage/xtrem-stock/nodes__stock_count__deletion_forbidden": "Die Inventur kann nicht gelöscht werden.", "@sage/xtrem-stock/nodes__stock_count__document_was_posted": "Die Inventur wurde gebucht.", "@sage/xtrem-stock/nodes__stock_count__end_confirm_zero": "Die Bestätigung der Nullmengen wurde für die Inventur {{number}} abgeschlossen. Zeilen aktualisiert: {{recordsUpdated}}.", "@sage/xtrem-stock/nodes__stock_count__mutation__postToStock": "Buchung in Bestand", "@sage/xtrem-stock/nodes__stock_count__mutation__postToStock__failed": "Buchung in Bestand fehlgeschlagen.", "@sage/xtrem-stock/nodes__stock_count__mutation__postToStock__parameter__documentIds": "Dokument-IDs", "@sage/xtrem-stock/nodes__stock_count__mutation__repost": "<PERSON><PERSON><PERSON> buchen", "@sage/xtrem-stock/nodes__stock_count__mutation__repost__failed": "Erneut buchen fehlgeschlagen.", "@sage/xtrem-stock/nodes__stock_count__mutation__repost__parameter__documentLines": "Dokumentzeilen", "@sage/xtrem-stock/nodes__stock_count__mutation__repost__parameter__stockCount": "Inventur", "@sage/xtrem-stock/nodes__stock_count__mutation__resendNotificationForFinance": "Benachrichtigung für Finanzen erneut senden", "@sage/xtrem-stock/nodes__stock_count__mutation__resendNotificationForFinance__failed": "Benachrichtigung für Finanzen erneut senden fehlgeschlagen.", "@sage/xtrem-stock/nodes__stock_count__mutation__resendNotificationForFinance__parameter__stockCount": "Inventur", "@sage/xtrem-stock/nodes__stock_count__mutation__start": "Start", "@sage/xtrem-stock/nodes__stock_count__mutation__start__failed": "Start fehlgeschlagen.", "@sage/xtrem-stock/nodes__stock_count__mutation__start__parameter__document": "Dokument", "@sage/xtrem-stock/nodes__stock_count__no_records_selected": "<PERSON><PERSON> Datensätze für Inventur {{number}} ausgewählt.", "@sage/xtrem-stock/nodes__stock_count__node_name": "Inventur", "@sage/xtrem-stock/nodes__stock_count__property__categories": "<PERSON><PERSON><PERSON>", "@sage/xtrem-stock/nodes__stock_count__property__concurrentStockCountLines": "<PERSON><PERSON><PERSON>", "@sage/xtrem-stock/nodes__stock_count__property__counter": "<PERSON><PERSON><PERSON>", "@sage/xtrem-stock/nodes__stock_count__property__description": "Bezeichnung", "@sage/xtrem-stock/nodes__stock_count__property__documentDate": "Dokumentdatum", "@sage/xtrem-stock/nodes__stock_count__property__effectiveDate": "Aktivierungsdatum", "@sage/xtrem-stock/nodes__stock_count__property__financeIntegrationStatus": "Status Integration Finanzen", "@sage/xtrem-stock/nodes__stock_count__property__financialSite": "Buchhaltungsstandort", "@sage/xtrem-stock/nodes__stock_count__property__forceUpdateForStock": "Aktualisierung für Bestand erzwingen", "@sage/xtrem-stock/nodes__stock_count__property__fromItem": "<PERSON><PERSON><PERSON> von", "@sage/xtrem-stock/nodes__stock_count__property__hasExpiryManagementInLines": "Hat Ablaufverwaltung in Zeilen", "@sage/xtrem-stock/nodes__stock_count__property__hasLotInLines": "Hat Charge in Zeilen", "@sage/xtrem-stock/nodes__stock_count__property__hasStockRecords": "Hat Bestandsdatensätze", "@sage/xtrem-stock/nodes__stock_count__property__hasSublotInLines": "Hat Untercharge in Zeilen", "@sage/xtrem-stock/nodes__stock_count__property__itemSites": "Artikel-<PERSON><PERSON><PERSON>", "@sage/xtrem-stock/nodes__stock_count__property__lastCountDate": "Datum letzte Inventur", "@sage/xtrem-stock/nodes__stock_count__property__lines": "<PERSON><PERSON><PERSON>", "@sage/xtrem-stock/nodes__stock_count__property__locations": "Lagerplätze", "@sage/xtrem-stock/nodes__stock_count__property__number": "<PERSON><PERSON><PERSON>", "@sage/xtrem-stock/nodes__stock_count__property__postingDetails": "Buchungsdetails", "@sage/xtrem-stock/nodes__stock_count__property__status": "Status", "@sage/xtrem-stock/nodes__stock_count__property__stockSite": "Lagerstandort", "@sage/xtrem-stock/nodes__stock_count__property__stockTransactionStatus": "Status Bestandstransaktion", "@sage/xtrem-stock/nodes__stock_count__property__toItem": "Artikel bis", "@sage/xtrem-stock/nodes__stock_count__property__transactionCurrency": "Transaktionswährung", "@sage/xtrem-stock/nodes__stock_count__property__zones": "Bereiche", "@sage/xtrem-stock/nodes__stock_count__query__existingStockCountLine": "Bestehende Inventurzeile", "@sage/xtrem-stock/nodes__stock_count__query__existingStockCountLine__failed": "Bestehende Inventurzeile fehlgeschlagen.", "@sage/xtrem-stock/nodes__stock_count__query__existingStockCountLine__parameter__lotCreateData": "Daten Chargenerstellung", "@sage/xtrem-stock/nodes__stock_count__query__existingStockCountLine__parameter__searchCriteria": "Suchkriterien", "@sage/xtrem-stock/nodes__stock_count__query__existingStockCountLine__parameter__throwErrorIfExist": "Fehler ausgeben, falls vorhanden", "@sage/xtrem-stock/nodes__stock_count__start_confirm_stock_count": "Der Vorgang für die Erstellung von Inventurzeilen wurde gestartet: {{number}}.", "@sage/xtrem-stock/nodes__stock_count__start_confirm_zero": "Die Bestätigung der Nullmengen wurde für die Inventur {{number}} gestartet.", "@sage/xtrem-stock/nodes__stock_count__to_be_counted_confirm_zero_cannot_be_performed": "Die Inventur wurde noch nicht gestartet: {{number}}. Starten Sie mit der Zählung, um die Mengen zu aktualisieren.", "@sage/xtrem-stock/nodes__stock_count_line___duplicated_serial_number": "Es sind doppelte Seriennummern vorhanden. <PERSON><PERSON><PERSON>, dass jede Zeile eindeutige Seriennummern umfasst.", "@sage/xtrem-stock/nodes__stock_count_line__asyncMutation__asyncExport": "Export", "@sage/xtrem-stock/nodes__stock_count_line__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-stock/nodes__stock_count_line__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-stock/nodes__stock_count_line__cannot_add_line_if_counting_closed": "<PERSON>e können keine Zeile hinzufügen, wenn die Inventur Abgeschlossen ist.", "@sage/xtrem-stock/nodes__stock_count_line__delete_not_allowed": "<PERSON><PERSON>, die während dem Inventurstart hinzugefügt wurde, kann nicht gelöscht werden. Inventur {{number}}, <PERSON><PERSON><PERSON> {{lineNumber}}.", "@sage/xtrem-stock/nodes__stock_count_line__expiration_date_mandatory": "Sie müssen ein Ablaufdatum für den Artikel {{itemId}} und für die Charge {{lotId}} auswählen.", "@sage/xtrem-stock/nodes__stock_count_line__forbid_addition_serialized_managed_item": "Eine Bestandszeile für einen Artikel mit Seriennummer kann nicht zu einer bestehenden Inventur hinzugefügt werden.", "@sage/xtrem-stock/nodes__stock_count_line__item_cannot_be_modified": "Der Artikel kann nicht geändert werden. Inventur {{number}}, <PERSON><PERSON><PERSON> {{lineNumber}}.", "@sage/xtrem-stock/nodes__stock_count_line__item_of_stock_detail_must_be_the_same_as_item_of_stock_count_line": "Der Artikel der Bestandsdetails und der Artikel der Inventurzeile müssen gleich sein. Inventur {{number}}, <PERSON><PERSON><PERSON> {{lineNumber}}.", "@sage/xtrem-stock/nodes__stock_count_line__line_posted_delete_not_allowed": "Die Inventurzeile kann nicht gelöscht werden. Sie wurde bereits gebucht. Inventur {{number}}, <PERSON><PERSON>e {{lineNumber}}.", "@sage/xtrem-stock/nodes__stock_count_line__location_cannot_be_modified": "Der Lagerplatz kann nicht geändert werden. Inventur {{number}}, <PERSON><PERSON>e {{lineNumber}}.", "@sage/xtrem-stock/nodes__stock_count_line__location_of_stock_detail_must_be_the_same_as_location_of_stock_count_line": "Der Lagerplatz der Bestandsdetails und der Lagerplatz der Inventurzeile müssen gleich sein. Inventur {{number}}, <PERSON><PERSON>e {{lineNumber}}.", "@sage/xtrem-stock/nodes__stock_count_line__lot_cannot_be_modified": "Die Charge kann nicht geändert werden. Inventur {{number}}, <PERSON><PERSON><PERSON> {{lineNumber}}.", "@sage/xtrem-stock/nodes__stock_count_line__lot_mandatory": "Sie müssen eine Charge für den Artikel {{itemId}} auswählen.", "@sage/xtrem-stock/nodes__stock_count_line__node_name": "Inventurzeile", "@sage/xtrem-stock/nodes__stock_count_line__owner_cannot_be_modified": "Der Besitzer kann nicht geändert werden. Inventur {{number}}, <PERSON><PERSON><PERSON> {{lineNumber}}.", "@sage/xtrem-stock/nodes__stock_count_line__owner_of_stock_detail_must_be_the_same_as_owner_of_stock_count_line": "Der Besitzer der Bestandsdetails und der Inventurzeile müssen gleich sein. Inventur {{number}}, <PERSON><PERSON><PERSON> {{lineNumber}}.", "@sage/xtrem-stock/nodes__stock_count_line__property__adjustmentQuantityInStockUnit": "Korrekturmenge in Lagereinheit", "@sage/xtrem-stock/nodes__stock_count_line__property__allocations": "Reservierungen", "@sage/xtrem-stock/nodes__stock_count_line__property__analyticalData": "Analytische Daten", "@sage/xtrem-stock/nodes__stock_count_line__property__canBeDeleted": "<PERSON>nn gel<PERSON> werden", "@sage/xtrem-stock/nodes__stock_count_line__property__canEnterOrderCost": "Kann Auftragskosten erfassen", "@sage/xtrem-stock/nodes__stock_count_line__property__computedAttributes": "Berechnete Attribute", "@sage/xtrem-stock/nodes__stock_count_line__property__countedQuantityInStockUnit": "Gezählte Menge in Lagereinheit", "@sage/xtrem-stock/nodes__stock_count_line__property__countedSerialNumber": "Seriennummer gezählt", "@sage/xtrem-stock/nodes__stock_count_line__property__countedSerialNumberPercentage": "Prozentsatz Seriennummer gezählt", "@sage/xtrem-stock/nodes__stock_count_line__property__document": "Dokument", "@sage/xtrem-stock/nodes__stock_count_line__property__documentId": "Dokument-ID", "@sage/xtrem-stock/nodes__stock_count_line__property__documentNumber": "Dokumentnummer", "@sage/xtrem-stock/nodes__stock_count_line__property__forceUpdateForFinance": "Aktualisierung für Finanzen erzwingen", "@sage/xtrem-stock/nodes__stock_count_line__property__forceUpdateForStock": "Aktualisierung für Bestand erzwingen", "@sage/xtrem-stock/nodes__stock_count_line__property__hasAllocationError": "Hat Reservierungsfehler", "@sage/xtrem-stock/nodes__stock_count_line__property__isAddedDuringCount": "Ist während der Zählung hinzugefügt", "@sage/xtrem-stock/nodes__stock_count_line__property__isCreatedFromStockRecord": "Ist aus Bestandsdatensatz erstellt", "@sage/xtrem-stock/nodes__stock_count_line__property__isNonStockItem": "Ist nicht bestandsgeführter Artikel", "@sage/xtrem-stock/nodes__stock_count_line__property__isStockCountCreation": "Ist Erstellung Inventur", "@sage/xtrem-stock/nodes__stock_count_line__property__item": "Artikel", "@sage/xtrem-stock/nodes__stock_count_line__property__itemSite": "Artikel-Standort", "@sage/xtrem-stock/nodes__stock_count_line__property__jsonSerialNumbers": "Seriennummern JSON", "@sage/xtrem-stock/nodes__stock_count_line__property__jsonStockDetails": "Bestandsdetails JSON", "@sage/xtrem-stock/nodes__stock_count_line__property__location": "Lagerplatz", "@sage/xtrem-stock/nodes__stock_count_line__property__lot": "Charge", "@sage/xtrem-stock/nodes__stock_count_line__property__newLineOrderCost": "Neue Zeile Auftragskosten", "@sage/xtrem-stock/nodes__stock_count_line__property__owner": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-stock/nodes__stock_count_line__property__quantityInStockUnit": "Menge in Lagereinheit", "@sage/xtrem-stock/nodes__stock_count_line__property__quantityVariance": "Abweichung Menge", "@sage/xtrem-stock/nodes__stock_count_line__property__quantityVariancePercentage": "Prozentsatz Mengenabweichung", "@sage/xtrem-stock/nodes__stock_count_line__property__shouldEnterOrderCost": "Muss Auftragskosten erfassen", "@sage/xtrem-stock/nodes__stock_count_line__property__site": "<PERSON><PERSON>", "@sage/xtrem-stock/nodes__stock_count_line__property__status": "Status", "@sage/xtrem-stock/nodes__stock_count_line__property__stockCountLineSerialNumbers": "Seriennummern Inventurzeile", "@sage/xtrem-stock/nodes__stock_count_line__property__stockDetail": "Bestandsdetails", "@sage/xtrem-stock/nodes__stock_count_line__property__stockDetails": "Bestandsdetails", "@sage/xtrem-stock/nodes__stock_count_line__property__stockMovements": "Bestandsbewegungen", "@sage/xtrem-stock/nodes__stock_count_line__property__stockRecord": "Bestandsdatensatz", "@sage/xtrem-stock/nodes__stock_count_line__property__stockStatus": "Bestandsstatus", "@sage/xtrem-stock/nodes__stock_count_line__property__stockTransactions": "Bestandstransaktion", "@sage/xtrem-stock/nodes__stock_count_line__property__stockTransactionStatus": "Status Bestandstransaktion", "@sage/xtrem-stock/nodes__stock_count_line__property__stockUnit": "Lagereinheit", "@sage/xtrem-stock/nodes__stock_count_line__property__storedAttributes": "Gespeicherte Attribute", "@sage/xtrem-stock/nodes__stock_count_line__property__storedDimensions": "Gespeicherte Sektoren", "@sage/xtrem-stock/nodes__stock_count_line__property__totalAllocated": "Gesamt reserviert", "@sage/xtrem-stock/nodes__stock_count_line__property__zone": "<PERSON><PERSON><PERSON>", "@sage/xtrem-stock/nodes__stock_count_line__query__getStockQuantity": "Bestandsmenge abrufen", "@sage/xtrem-stock/nodes__stock_count_line__query__getStockQuantity__failed": "Bestandsmenge abrufen fehlgeschlagen.", "@sage/xtrem-stock/nodes__stock_count_line__query__getStockQuantity__parameter__stockCountLine": "Inventurzeile", "@sage/xtrem-stock/nodes__stock_count_line__selected_quantity_too_high": "<PERSON> Anzahl der ausgewählten Seriennummern ({{selectedQuantity}}) kann nicht größer als die gezählte Menge ({{countedQuantity}}) sein.", "@sage/xtrem-stock/nodes__stock_count_line__site_of_stock_detail_must_be_the_same_as_site_of_stock_count_line": "Der Standort der Bestandsdetails und der Standort der Inventurzeile müssen gleich sein. Inventur {{number}}, <PERSON><PERSON><PERSON> {{lineNumber}}.", "@sage/xtrem-stock/nodes__stock_count_line__status_of_stock_detail_must_be_the_same_as_status_of_stock_count_line": "Der Status der Bestandsdetails und der Status der Inventurzeile müssen gleich sein. Inventur {{number}}, <PERSON><PERSON><PERSON> {{lineNumber}}.", "@sage/xtrem-stock/nodes__stock_count_line__stock_detail_inconsistent_with_line": "Die Bestandsdetails stimmen nicht mit der Zeile überein.", "@sage/xtrem-stock/nodes__stock_count_line__stock_record_not_found": "Bestandsdatensatz nicht gefunden. Inventur {{number}}, <PERSON><PERSON><PERSON> {{lineNumber}}.", "@sage/xtrem-stock/nodes__stock_count_line__stock_status_cannot_be_modified": "Der Bestandsstatus kann nicht geändert werden. Inventur {{number}}, <PERSON><PERSON><PERSON> {{lineNumber}}.", "@sage/xtrem-stock/nodes__stock_count_line__stock_unit_of_stock_detail_must_be_the_same_as_stock_unit_of_stock_count_line": "Die Lagereinheit der Bestandsdetails und der Inventurzeile müssen gleich sein. Inventur {{number}}, <PERSON>eile {{lineNumber}}.", "@sage/xtrem-stock/nodes__stock_count_line__sublot_mandatory": "Sie müssen eine Untercharge für den Artikel {{itemId}} auswählen.", "@sage/xtrem-stock/nodes__stock_count_line__update_forbidden_count_line_posted": "Die Zeile stockCountLine {{id}} kann nicht aktualisiert werden. Sie wurde bereits gebucht.", "@sage/xtrem-stock/nodes__stock_count_line_serial_number__asyncMutation__asyncExport": "Export", "@sage/xtrem-stock/nodes__stock_count_line_serial_number__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-stock/nodes__stock_count_line_serial_number__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-stock/nodes__stock_count_line_serial_number__available_error": "In diesem Bereich verfügbare Seriennummern: {{available}}.", "@sage/xtrem-stock/nodes__stock_count_line_serial_number__cannot_delete_count_started": "Die Seriennummer einer Inventurzeile kann nach Start der Inventur nicht mehr gelöscht werden. Artikel {{itemId}}, Seriennummer {{serialNumber}}.", "@sage/xtrem-stock/nodes__stock_count_line_serial_number__cannot_delete_serial_still_in_stock": "Die Seriennummer einer Inventurzeile kann nicht gelöscht werden, wenn diese mit mit dem gezählten Bestandsdatensatz verbunden ist. Artikel {{itemId}}, Seriennummer {{serialNumber}}.", "@sage/xtrem-stock/nodes__stock_count_line_serial_number__invalid_quantity_error": "Die Seriennummer bis kann nicht berechnet werden. Prüfuen Sie die Seriennummer von und die Menge.", "@sage/xtrem-stock/nodes__stock_count_line_serial_number__invalid_starting_serial_number_error": "Die Seriennummer ist der Inventurzeile nicht zugewiesen. Seriennummer {{serialNumber}}.", "@sage/xtrem-stock/nodes__stock_count_line_serial_number__node_name": "Seriennummer Inventurzeile", "@sage/xtrem-stock/nodes__stock_count_line_serial_number__property__isCounted": "Gezählt", "@sage/xtrem-stock/nodes__stock_count_line_serial_number__property__serialNumber": "Seriennummer", "@sage/xtrem-stock/nodes__stock_count_line_serial_number__property__stockCountLine": "Inventurzeile", "@sage/xtrem-stock/nodes__stock_count_line_serial_number__query__getEndingSerialNumber": "Seriennummer bis abrufen", "@sage/xtrem-stock/nodes__stock_count_line_serial_number__query__getEndingSerialNumber__failed": "Seriennummer bis abrufen fehlgeschlagen.", "@sage/xtrem-stock/nodes__stock_count_line_serial_number__query__getEndingSerialNumber__parameter__quantity": "<PERSON><PERSON>", "@sage/xtrem-stock/nodes__stock_count_line_serial_number__query__getEndingSerialNumber__parameter__startingSerialNumber": "<PERSON><PERSON><PERSON><PERSON> von", "@sage/xtrem-stock/nodes__stock_count_line_serial_number__query__getEndingSerialNumber__parameter__stockCountLineId": "ID Inventurzeile", "@sage/xtrem-stock/nodes__stock_count_line_serial_number__serial_number_not_assigned_to_a_stock_record": "Die Seriennummer {{serialNumber}} ist keiner Bestandszeile zugewiesen.", "@sage/xtrem-stock/nodes__stock_count_line_serial_number__stock_count_line_and_serial_number_dont_match": "Die Inventurzeile und die Seriennummer {{serialNumber}} entsprechen nicht der gleichen Bestandszeile.", "@sage/xtrem-stock/nodes__stock_count_line_serial_number__zero_quantity_error": "Erfassen Sie eine Menge größer als 0.", "@sage/xtrem-stock/nodes__stock_detail__exceeds_stock_record_quantity": "Die Menge {{qty1}} für die Bestandsdetailnummer {{detailNb}} überschreitet die Bestandsdatensatzmenge {{qty2}}.", "@sage/xtrem-stock/nodes__stock_detail__stock_record_has_been_deleted": "Der Bestandsabgang kann nicht durchgeführt werden, da der Bestandsdatensatz durch eine andere Transaktion gelöscht wurde.", "@sage/xtrem-stock/nodes__stock_issue___duplicated_serial_number": "Es sind doppelte Seriennummern vorhanden. <PERSON><PERSON><PERSON>, dass jede Zeile eindeutige Seriennummern umfasst.", "@sage/xtrem-stock/nodes__stock_issue__asyncMutation__asyncExport": "Export", "@sage/xtrem-stock/nodes__stock_issue__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-stock/nodes__stock_issue__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-stock/nodes__stock_issue__mutation__postToStock": "Buchung in Bestand", "@sage/xtrem-stock/nodes__stock_issue__mutation__postToStock__failed": "Buchung in Bestand fehlgeschlagen.", "@sage/xtrem-stock/nodes__stock_issue__mutation__postToStock__parameter__documentIds": "Dokument-IDs", "@sage/xtrem-stock/nodes__stock_issue__mutation__repost": "<PERSON><PERSON><PERSON> buchen", "@sage/xtrem-stock/nodes__stock_issue__mutation__repost__failed": "Erneut buchen fehlgeschlagen.", "@sage/xtrem-stock/nodes__stock_issue__mutation__repost__parameter__documentLines": "Dokumentzeilen", "@sage/xtrem-stock/nodes__stock_issue__mutation__repost__parameter__stockIssue": "Bestandsabgang", "@sage/xtrem-stock/nodes__stock_issue__mutation__resendNotificationForFinance": "Benachrichtigung für Finanzen erneut senden", "@sage/xtrem-stock/nodes__stock_issue__mutation__resendNotificationForFinance__failed": "Benachrichtigung für Finanzen erneut senden fehlgeschlagen.", "@sage/xtrem-stock/nodes__stock_issue__mutation__resendNotificationForFinance__parameter__stockIssue": "Bestandsabgang", "@sage/xtrem-stock/nodes__stock_issue__node_name": "Bestandsabgang", "@sage/xtrem-stock/nodes__stock_issue__property__description": "Bezeichnung", "@sage/xtrem-stock/nodes__stock_issue__property__displayStatus": "Status anzeigen", "@sage/xtrem-stock/nodes__stock_issue__property__documentDate": "Dokumentdatum", "@sage/xtrem-stock/nodes__stock_issue__property__effectiveDate": "Aktivierungsdatum", "@sage/xtrem-stock/nodes__stock_issue__property__financeIntegrationStatus": "Status Integration Finanzen", "@sage/xtrem-stock/nodes__stock_issue__property__financialSite": "Buchhaltungsstandort", "@sage/xtrem-stock/nodes__stock_issue__property__forceUpdateForStock": "Aktualisierung für Bestand erzwingen", "@sage/xtrem-stock/nodes__stock_issue__property__isSetDimensionsMainListHidden": "Ist Hauptliste Sektoren setzen verborgen", "@sage/xtrem-stock/nodes__stock_issue__property__lines": "<PERSON><PERSON><PERSON>", "@sage/xtrem-stock/nodes__stock_issue__property__number": "<PERSON><PERSON><PERSON>", "@sage/xtrem-stock/nodes__stock_issue__property__postingDetails": "Buchungsdetails", "@sage/xtrem-stock/nodes__stock_issue__property__reasonCode": "Grundcode", "@sage/xtrem-stock/nodes__stock_issue__property__status": "Status", "@sage/xtrem-stock/nodes__stock_issue__property__stockSite": "Lagerstandort", "@sage/xtrem-stock/nodes__stock_issue__property__stockTransactionStatus": "Status Bestandstransaktion", "@sage/xtrem-stock/nodes__stock_issue__property__transactionCurrency": "Transaktionswährung", "@sage/xtrem-stock/nodes__stock_issue_line__asyncMutation__asyncExport": "Export", "@sage/xtrem-stock/nodes__stock_issue_line__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-stock/nodes__stock_issue_line__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-stock/nodes__stock_issue_line__mutation__setDimension": "<PERSON><PERSON><PERSON> <PERSON>", "@sage/xtrem-stock/nodes__stock_issue_line__mutation__setDimension__failed": "<PERSON><PERSON><PERSON> setzen fehlgeschlagen.", "@sage/xtrem-stock/nodes__stock_issue_line__mutation__setDimension__parameter__stockIssueLine": "Bestandsabgangszeile", "@sage/xtrem-stock/nodes__stock_issue_line__mutation__setDimension__parameter__storedAttributes": "Gespeicherte Attribute", "@sage/xtrem-stock/nodes__stock_issue_line__mutation__setDimension__parameter__storedDimensions": "Gespeicherte Sektoren", "@sage/xtrem-stock/nodes__stock_issue_line__node_name": "Bestandsabgangszeile", "@sage/xtrem-stock/nodes__stock_issue_line__property__analyticalData": "Analytische Daten", "@sage/xtrem-stock/nodes__stock_issue_line__property__computedAttributes": "Berechnete Attribute", "@sage/xtrem-stock/nodes__stock_issue_line__property__displayStatus": "Status anzeigen", "@sage/xtrem-stock/nodes__stock_issue_line__property__document": "Dokument", "@sage/xtrem-stock/nodes__stock_issue_line__property__documentId": "Dokument-ID", "@sage/xtrem-stock/nodes__stock_issue_line__property__documentNumber": "Dokumentnummer", "@sage/xtrem-stock/nodes__stock_issue_line__property__item": "Artikel", "@sage/xtrem-stock/nodes__stock_issue_line__property__itemSite": "Artikel-Standort", "@sage/xtrem-stock/nodes__stock_issue_line__property__jsonStockDetails": "Bestandsdetails JSON", "@sage/xtrem-stock/nodes__stock_issue_line__property__location": "Lagerplatz", "@sage/xtrem-stock/nodes__stock_issue_line__property__lot": "Charge", "@sage/xtrem-stock/nodes__stock_issue_line__property__orderCost": "Auftragskosten", "@sage/xtrem-stock/nodes__stock_issue_line__property__quantityInStockUnit": "Menge in Lagereinheit", "@sage/xtrem-stock/nodes__stock_issue_line__property__site": "<PERSON><PERSON>", "@sage/xtrem-stock/nodes__stock_issue_line__property__stockDetails": "Bestandsdetails", "@sage/xtrem-stock/nodes__stock_issue_line__property__stockDetailStatus": "Status Bestandsdetail", "@sage/xtrem-stock/nodes__stock_issue_line__property__stockMovements": "Bestandsbewegungen", "@sage/xtrem-stock/nodes__stock_issue_line__property__stockStatus": "Bestandsstatus", "@sage/xtrem-stock/nodes__stock_issue_line__property__stockTransactions": "Bestandstransaktion", "@sage/xtrem-stock/nodes__stock_issue_line__property__stockTransactionStatus": "Status Bestandstransaktion", "@sage/xtrem-stock/nodes__stock_issue_line__property__stockUnit": "Lagereinheit", "@sage/xtrem-stock/nodes__stock_issue_line__property__storedAttributes": "Gespeicherte Attribute", "@sage/xtrem-stock/nodes__stock_issue_line__property__storedDimensions": "Gespeicherte Sektoren", "@sage/xtrem-stock/nodes__stock_issue_line__property__totalCost": "<PERSON><PERSON> gesamt", "@sage/xtrem-stock/nodes__stock_issue_line__property__valuedCost": "Bewertete Kosten", "@sage/xtrem-stock/nodes__stock_issue_post__stock_details_required": "Sie müssen Bestandsdetails für alle Zeilen vor der Buchung erfassen.", "@sage/xtrem-stock/nodes__stock_receipt__asyncMutation__asyncExport": "Export", "@sage/xtrem-stock/nodes__stock_receipt__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-stock/nodes__stock_receipt__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-stock/nodes__stock_receipt__asyncMutation__createTestStockReceipt": "Testbestandseingang erstellen", "@sage/xtrem-stock/nodes__stock_receipt__asyncMutation__createTestStockReceipt__failed": "Testbestandseingang erstellen fehlgeschlagen.", "@sage/xtrem-stock/nodes__stock_receipt__asyncMutation__createTestStockReceipt__parameter__data": "Daten", "@sage/xtrem-stock/nodes__stock_receipt__deletion_forbidden": "Der aktuelle Bestandseingang kann nicht gelöscht werden.", "@sage/xtrem-stock/nodes__stock_receipt__mutation__postToStock": "Buchung in Bestand", "@sage/xtrem-stock/nodes__stock_receipt__mutation__postToStock__failed": "Buchung in Bestand fehlgeschlagen.", "@sage/xtrem-stock/nodes__stock_receipt__mutation__postToStock__parameter__documentIds": "Dokument-IDs", "@sage/xtrem-stock/nodes__stock_receipt__mutation__repost": "<PERSON><PERSON><PERSON> buchen", "@sage/xtrem-stock/nodes__stock_receipt__mutation__repost__failed": "Erneut buchen fehlgeschlagen.", "@sage/xtrem-stock/nodes__stock_receipt__mutation__repost__parameter__documentLines": "Dokumentzeilen", "@sage/xtrem-stock/nodes__stock_receipt__mutation__repost__parameter__stockReceipt": "Bestandseingang", "@sage/xtrem-stock/nodes__stock_receipt__mutation__resendNotificationForFinance": "Benachrichtigung für Finanzen erneut senden", "@sage/xtrem-stock/nodes__stock_receipt__mutation__resendNotificationForFinance__failed": "Benachrichtigung für Finanzen erneut senden fehlgeschlagen.", "@sage/xtrem-stock/nodes__stock_receipt__mutation__resendNotificationForFinance__parameter__stockReceipt": "Bestandseingang", "@sage/xtrem-stock/nodes__stock_receipt__node_name": "Bestandseingang", "@sage/xtrem-stock/nodes__stock_receipt__property__description": "Bezeichnung", "@sage/xtrem-stock/nodes__stock_receipt__property__displayStatus": "Status anzeigen", "@sage/xtrem-stock/nodes__stock_receipt__property__documentDate": "Dokumentdatum", "@sage/xtrem-stock/nodes__stock_receipt__property__effectiveDate": "Aktivierungsdatum", "@sage/xtrem-stock/nodes__stock_receipt__property__financeIntegrationStatus": "Status Integration Finanzen", "@sage/xtrem-stock/nodes__stock_receipt__property__financialSite": "Buchhaltungsstandort", "@sage/xtrem-stock/nodes__stock_receipt__property__forceUpdateForStock": "Aktualisierung für Bestand erzwingen", "@sage/xtrem-stock/nodes__stock_receipt__property__isSetDimensionsMainListHidden": "Ist Hauptliste Sektoren setzen verborgen", "@sage/xtrem-stock/nodes__stock_receipt__property__lines": "<PERSON><PERSON><PERSON>", "@sage/xtrem-stock/nodes__stock_receipt__property__number": "<PERSON><PERSON><PERSON>", "@sage/xtrem-stock/nodes__stock_receipt__property__postingDetails": "Buchungsdetails", "@sage/xtrem-stock/nodes__stock_receipt__property__reasonCode": "Grundcode", "@sage/xtrem-stock/nodes__stock_receipt__property__status": "Status", "@sage/xtrem-stock/nodes__stock_receipt__property__stockSite": "Lagerstandort", "@sage/xtrem-stock/nodes__stock_receipt__property__stockTransactionStatus": "Status Bestandstransaktion", "@sage/xtrem-stock/nodes__stock_receipt__property__transactionCurrency": "Transaktionswährung", "@sage/xtrem-stock/nodes__stock_receipt_line__asyncMutation__asyncExport": "Export", "@sage/xtrem-stock/nodes__stock_receipt_line__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-stock/nodes__stock_receipt_line__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-stock/nodes__stock_receipt_line__mutation__setDimension": "<PERSON><PERSON><PERSON> <PERSON>", "@sage/xtrem-stock/nodes__stock_receipt_line__mutation__setDimension__failed": "<PERSON><PERSON><PERSON> setzen fehlgeschlagen.", "@sage/xtrem-stock/nodes__stock_receipt_line__mutation__setDimension__parameter__stockReceiptLine": "Bestandseingangszeile", "@sage/xtrem-stock/nodes__stock_receipt_line__mutation__setDimension__parameter__storedAttributes": "Gespeicherte Attribute", "@sage/xtrem-stock/nodes__stock_receipt_line__mutation__setDimension__parameter__storedDimensions": "Gespeicherte Sektoren", "@sage/xtrem-stock/nodes__stock_receipt_line__node_name": "Bestandseingangszeile", "@sage/xtrem-stock/nodes__stock_receipt_line__property__analyticalData": "Analytische Daten", "@sage/xtrem-stock/nodes__stock_receipt_line__property__computedAttributes": "Berechnete Attribute", "@sage/xtrem-stock/nodes__stock_receipt_line__property__displayStatus": "Status anzeigen", "@sage/xtrem-stock/nodes__stock_receipt_line__property__document": "Dokument", "@sage/xtrem-stock/nodes__stock_receipt_line__property__documentId": "Dokument-ID", "@sage/xtrem-stock/nodes__stock_receipt_line__property__documentNumber": "Dokumentnummer", "@sage/xtrem-stock/nodes__stock_receipt_line__property__existingLot": "Bestehende Charge", "@sage/xtrem-stock/nodes__stock_receipt_line__property__item": "Artikel", "@sage/xtrem-stock/nodes__stock_receipt_line__property__itemSite": "Artikel-Standort", "@sage/xtrem-stock/nodes__stock_receipt_line__property__jsonStockDetails": "Bestandsdetails JSON", "@sage/xtrem-stock/nodes__stock_receipt_line__property__location": "Lagerplatz", "@sage/xtrem-stock/nodes__stock_receipt_line__property__lotCreateData": "Daten Chargenerstellung", "@sage/xtrem-stock/nodes__stock_receipt_line__property__orderCost": "Auftragskosten", "@sage/xtrem-stock/nodes__stock_receipt_line__property__quantityInStockUnit": "Menge in Lagereinheit", "@sage/xtrem-stock/nodes__stock_receipt_line__property__site": "<PERSON><PERSON>", "@sage/xtrem-stock/nodes__stock_receipt_line__property__stockDetails": "Bestandsdetails", "@sage/xtrem-stock/nodes__stock_receipt_line__property__stockDetailStatus": "Status Bestandsdetail", "@sage/xtrem-stock/nodes__stock_receipt_line__property__stockMovements": "Bestandsbewegungen", "@sage/xtrem-stock/nodes__stock_receipt_line__property__stockStatus": "Bestandsstatus", "@sage/xtrem-stock/nodes__stock_receipt_line__property__stockTransactions": "Bestandstransaktion", "@sage/xtrem-stock/nodes__stock_receipt_line__property__stockTransactionStatus": "Status Bestandstransaktion", "@sage/xtrem-stock/nodes__stock_receipt_line__property__stockUnit": "Lagereinheit", "@sage/xtrem-stock/nodes__stock_receipt_line__property__storedAttributes": "Gespeicherte Attribute", "@sage/xtrem-stock/nodes__stock_receipt_line__property__storedDimensions": "Gespeicherte Sektoren", "@sage/xtrem-stock/nodes__stock_receipt_line__property__totalCost": "<PERSON><PERSON> gesamt", "@sage/xtrem-stock/nodes__stock_receipt_line__property__valuedCost": "Bewertete Kosten", "@sage/xtrem-stock/nodes__stock_receipt_node_post__stock_details_required": "Sie müssen Bestandsdetails für alle Zeilen vor der Buchung erfassen.", "@sage/xtrem-stock/nodes__stock_reorder_calculation_input_set__asyncMutation__asyncExport": "Export", "@sage/xtrem-stock/nodes__stock_reorder_calculation_input_set__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-stock/nodes__stock_reorder_calculation_input_set__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-stock/nodes__stock_reorder_calculation_input_set__asyncMutation__reorderCalculation": "Berechnung Wiederbeschaffung", "@sage/xtrem-stock/nodes__stock_reorder_calculation_input_set__asyncMutation__reorderCalculation__failed": "Berechnung Wiederbeschaffung fehlgeschlagen.", "@sage/xtrem-stock/nodes__stock_reorder_calculation_input_set__asyncMutation__reorderCalculation__parameter__userId": "Benutzer-ID", "@sage/xtrem-stock/nodes__stock_reorder_calculation_input_set__node_name": "Eingabemenge Berechnung Wiederbeschaffung Lager", "@sage/xtrem-stock/nodes__stock_reorder_calculation_input_set__notification_calculation_failed": "Berechnung Wiederbeschaffung Lager fehlgeschlagen.", "@sage/xtrem-stock/nodes__stock_reorder_calculation_input_set__notification_calculation_finished": "Berechnung Wiederbeschaffung Lager durchgeführt.", "@sage/xtrem-stock/nodes__stock_reorder_calculation_input_set__property__company": "Unternehmen", "@sage/xtrem-stock/nodes__stock_reorder_calculation_input_set__property__endDate": "Enddatum", "@sage/xtrem-stock/nodes__stock_reorder_calculation_input_set__property__fromItem": "<PERSON><PERSON><PERSON> von", "@sage/xtrem-stock/nodes__stock_reorder_calculation_input_set__property__lines": "<PERSON><PERSON><PERSON>", "@sage/xtrem-stock/nodes__stock_reorder_calculation_input_set__property__reorderType": "Wiederbeschaffungsart", "@sage/xtrem-stock/nodes__stock_reorder_calculation_input_set__property__sites": "<PERSON><PERSON><PERSON>", "@sage/xtrem-stock/nodes__stock_reorder_calculation_input_set__property__status": "Status", "@sage/xtrem-stock/nodes__stock_reorder_calculation_input_set__property__toItem": "Artikel bis", "@sage/xtrem-stock/nodes__stock_reorder_calculation_input_set__property__user": "<PERSON><PERSON><PERSON>", "@sage/xtrem-stock/nodes__stock_reorder_calculation_result_line__asyncMutation__asyncExport": "Export", "@sage/xtrem-stock/nodes__stock_reorder_calculation_result_line__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-stock/nodes__stock_reorder_calculation_result_line__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-stock/nodes__stock_reorder_calculation_result_line__node_name": "Ergebniszeile Berechnung Wiederbeschaffung Lager", "@sage/xtrem-stock/nodes__stock_reorder_calculation_result_line__property__company": "Unternehmen", "@sage/xtrem-stock/nodes__stock_reorder_calculation_result_line__property__currency": "Währung", "@sage/xtrem-stock/nodes__stock_reorder_calculation_result_line__property__inputSet": "Eingabemenge", "@sage/xtrem-stock/nodes__stock_reorder_calculation_result_line__property__itemSite": "Artikelstandort", "@sage/xtrem-stock/nodes__stock_reorder_calculation_result_line__property__orderDate": "Auftragsdatum", "@sage/xtrem-stock/nodes__stock_reorder_calculation_result_line__property__purchaseUnit": "Einkaufseinheit", "@sage/xtrem-stock/nodes__stock_reorder_calculation_result_line__property__quantity": "<PERSON><PERSON>", "@sage/xtrem-stock/nodes__stock_reorder_calculation_result_line__property__reorderType": "Wiederbeschaffungsart", "@sage/xtrem-stock/nodes__stock_reorder_calculation_result_line__property__stockUnit": "Lagereinheit", "@sage/xtrem-stock/nodes__stock_reorder_calculation_result_line__property__supplier": "Lieferant", "@sage/xtrem-stock/nodes__stock_update_listener__quantity_of_stock_record_to_be_counted_has_changed": "Die Menge des zu zählenden Bestandsdatensatzes wurde seit dem Start der Inventur geändert.\n\n{{stock_identifiers}}", "@sage/xtrem-stock/nodes__stock_update_listener__stock_record_identification": "Artikel: {{item}}\n\nStatus: {{status}}\n\nCharge: {{lot}}\n\nLagerplatz: {{location}}\n\nAktuelle Menge im Bestand: {{quantity}}", "@sage/xtrem-stock/nodes__stock_valuation_input_set__asyncMutation__asyncExport": "Export", "@sage/xtrem-stock/nodes__stock_valuation_input_set__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-stock/nodes__stock_valuation_input_set__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-stock/nodes__stock_valuation_input_set__asyncMutation__stockValuation": "Bestandsbewertung", "@sage/xtrem-stock/nodes__stock_valuation_input_set__asyncMutation__stockValuation__failed": "Bestandsbewertung fehlgeschlagen.", "@sage/xtrem-stock/nodes__stock_valuation_input_set__asyncMutation__stockValuation__parameter__userId": "Benutzer-ID", "@sage/xtrem-stock/nodes__stock_valuation_input_set__node_name": "Eingabemenge Bestandsbewertung", "@sage/xtrem-stock/nodes__stock_valuation_input_set__notification_valuation_failed": "Bestandsbewertung fehlgeschlagen.", "@sage/xtrem-stock/nodes__stock_valuation_input_set__notification_valuation_finished": "Bestandsbewertung fertig.", "@sage/xtrem-stock/nodes__stock_valuation_input_set__property__commodityCode": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-stock/nodes__stock_valuation_input_set__property__company": "Unternehmen", "@sage/xtrem-stock/nodes__stock_valuation_input_set__property__date": "Datum", "@sage/xtrem-stock/nodes__stock_valuation_input_set__property__displayZeroValues": "Nullwerte anzeigen", "@sage/xtrem-stock/nodes__stock_valuation_input_set__property__fromItem": "<PERSON><PERSON><PERSON> von", "@sage/xtrem-stock/nodes__stock_valuation_input_set__property__itemCategory": "Artikelkategorie", "@sage/xtrem-stock/nodes__stock_valuation_input_set__property__lines": "<PERSON><PERSON><PERSON>", "@sage/xtrem-stock/nodes__stock_valuation_input_set__property__postingClass": "Buchungsklasse", "@sage/xtrem-stock/nodes__stock_valuation_input_set__property__sites": "<PERSON><PERSON><PERSON>", "@sage/xtrem-stock/nodes__stock_valuation_input_set__property__status": "Status", "@sage/xtrem-stock/nodes__stock_valuation_input_set__property__toItem": "Artikel bis", "@sage/xtrem-stock/nodes__stock_valuation_input_set__property__user": "<PERSON><PERSON><PERSON>", "@sage/xtrem-stock/nodes__stock_valuation_input_set__property__valuationMethod": "Bewertungsmethode", "@sage/xtrem-stock/nodes__stock_valuation_input_set_to_site__asyncMutation__asyncExport": "Export", "@sage/xtrem-stock/nodes__stock_valuation_input_set_to_site__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-stock/nodes__stock_valuation_input_set_to_site__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-stock/nodes__stock_valuation_input_set_to_site__node_name": "Eingabemenge Bestandsbewertung zu Standort", "@sage/xtrem-stock/nodes__stock_valuation_input_set_to_site__property__inputSet": "Eingabemenge", "@sage/xtrem-stock/nodes__stock_valuation_input_set_to_site__property__site": "<PERSON><PERSON>", "@sage/xtrem-stock/nodes__stock_valuation_result_line__asyncMutation__asyncExport": "Export", "@sage/xtrem-stock/nodes__stock_valuation_result_line__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-stock/nodes__stock_valuation_result_line__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-stock/nodes__stock_valuation_result_line__node_name": "Ergebniszeile Bestandsbewertung", "@sage/xtrem-stock/nodes__stock_valuation_result_line__property__commodityCode": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-stock/nodes__stock_valuation_result_line__property__company": "Unternehmen", "@sage/xtrem-stock/nodes__stock_valuation_result_line__property__costType": "<PERSON><PERSON><PERSON>", "@sage/xtrem-stock/nodes__stock_valuation_result_line__property__currency": "Währung", "@sage/xtrem-stock/nodes__stock_valuation_result_line__property__inputSet": "Eingabemenge", "@sage/xtrem-stock/nodes__stock_valuation_result_line__property__item": "Artikel", "@sage/xtrem-stock/nodes__stock_valuation_result_line__property__itemCategory": "Artikelkategorie", "@sage/xtrem-stock/nodes__stock_valuation_result_line__property__postingClass": "Buchungsklasse", "@sage/xtrem-stock/nodes__stock_valuation_result_line__property__quantity": "<PERSON><PERSON>", "@sage/xtrem-stock/nodes__stock_valuation_result_line__property__site": "<PERSON><PERSON>", "@sage/xtrem-stock/nodes__stock_valuation_result_line__property__stockUnit": "Lagereinheit", "@sage/xtrem-stock/nodes__stock_valuation_result_line__property__stockValue": "Bestandswert", "@sage/xtrem-stock/nodes__stock_valuation_result_line__property__unitCost": "Stückkosten", "@sage/xtrem-stock/nodes__stock_valuation_result_line__property__valuationDate": "Bewertungsdatum", "@sage/xtrem-stock/nodes__stock_value_change__amount_newAmount_average_cost": "Zeile 1: Die aktuellen und neuen Beträge können nicht gleich sein. Erfassen Sie im neuen Betrag einen anderen Wert.", "@sage/xtrem-stock/nodes__stock_value_change__amount_newAmount_fifo_cost": "Zeile {{lineNumber}}: Die aktuellen und neuen Beträge können nicht gleich sein. Erfassen Sie im neuen Betrag einen anderen Wert.", "@sage/xtrem-stock/nodes__stock_value_change__asyncMutation__asyncExport": "Export", "@sage/xtrem-stock/nodes__stock_value_change__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-stock/nodes__stock_value_change__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-stock/nodes__stock_value_change__average_cost_quantity_cost_amount_has_changed": "Die Durchschnittskosten, die Zeile 1 entsprechen, wurden geändert. Erstellen Sie die Zeile für die Bestandswertänderung erneut.", "@sage/xtrem-stock/nodes__stock_value_change__cant_repost_stock_value_change_when_status_is_not_failed": "Sie können eine Bestandswertänderung nur erneut buchen, wenn der Status 'Fehlgeschlagen' ist.", "@sage/xtrem-stock/nodes__stock_value_change__company_has_no_stock_posting": "<PERSON>s wurde kein <PERSON>and auf dieses Unternehmen gebucht.", "@sage/xtrem-stock/nodes__stock_value_change__document_was_posted": "Die Bestandswertänderung wurde gebucht.", "@sage/xtrem-stock/nodes__stock_value_change__fifo_cost_quantity_cost_amount_has_changed": "Der FIFO-Stapeldatensatz, der der Zeile mit dem Aktivierungsdatum {{date}} und der Sequenz {{sequence}} entsp<PERSON>t, wurde geändert. Erstellen Sie die Zeile für die Bestandswertänderung erneut.", "@sage/xtrem-stock/nodes__stock_value_change__lines_mandatory": "Die Bestandswertänderung muss mindestens eine Zeile enthalten.", "@sage/xtrem-stock/nodes__stock_value_change__mutation__financeIntegrationCheck": "Prüfung Integration Finanzen", "@sage/xtrem-stock/nodes__stock_value_change__mutation__financeIntegrationCheck__failed": "Prüfung Integration Finanzen fehlgeschlagen.", "@sage/xtrem-stock/nodes__stock_value_change__mutation__financeIntegrationCheck__parameter__valueChange": "Wertänderung", "@sage/xtrem-stock/nodes__stock_value_change__mutation__postToStock": "Buchung in Bestand", "@sage/xtrem-stock/nodes__stock_value_change__mutation__postToStock__failed": "Buchung in Bestand fehlgeschlagen.", "@sage/xtrem-stock/nodes__stock_value_change__mutation__postToStock__parameter__documentIds": "Dokument-IDs", "@sage/xtrem-stock/nodes__stock_value_change__mutation__repost": "<PERSON><PERSON><PERSON> buchen", "@sage/xtrem-stock/nodes__stock_value_change__mutation__repost__failed": "Erneut buchen fehlgeschlagen.", "@sage/xtrem-stock/nodes__stock_value_change__mutation__repost__parameter__documentLines": "Dokumentzeilen", "@sage/xtrem-stock/nodes__stock_value_change__mutation__repost__parameter__stockValueChange": "Änderung Bestandswert", "@sage/xtrem-stock/nodes__stock_value_change__mutation__resendNotificationForFinance": "Benachrichtigung für Finanzen erneut senden", "@sage/xtrem-stock/nodes__stock_value_change__mutation__resendNotificationForFinance__failed": "Benachrichtigung für Finanzen erneut senden fehlgeschlagen.", "@sage/xtrem-stock/nodes__stock_value_change__mutation__resendNotificationForFinance__parameter__stockValueChange": "Änderung Bestandswert", "@sage/xtrem-stock/nodes__stock_value_change__node_name": "Änderung Bestandswert", "@sage/xtrem-stock/nodes__stock_value_change__property__description": "Bezeichnung", "@sage/xtrem-stock/nodes__stock_value_change__property__documentDate": "Dokumentdatum", "@sage/xtrem-stock/nodes__stock_value_change__property__effectiveDate": "Aktivierungsdatum", "@sage/xtrem-stock/nodes__stock_value_change__property__financeIntegrationStatus": "Status Integration Finanzen", "@sage/xtrem-stock/nodes__stock_value_change__property__financialSite": "Buchhaltungsstandort", "@sage/xtrem-stock/nodes__stock_value_change__property__forceUpdateForStock": "Aktualisierung für Bestand erzwingen", "@sage/xtrem-stock/nodes__stock_value_change__property__item": "Artikel", "@sage/xtrem-stock/nodes__stock_value_change__property__itemSite": "Artikelstandort", "@sage/xtrem-stock/nodes__stock_value_change__property__lines": "<PERSON><PERSON><PERSON>", "@sage/xtrem-stock/nodes__stock_value_change__property__number": "<PERSON><PERSON><PERSON>", "@sage/xtrem-stock/nodes__stock_value_change__property__postedDate": "<PERSON><PERSON> g<PERSON>t", "@sage/xtrem-stock/nodes__stock_value_change__property__postingDetails": "Buchungsdetails", "@sage/xtrem-stock/nodes__stock_value_change__property__site": "<PERSON><PERSON>", "@sage/xtrem-stock/nodes__stock_value_change__property__stockTransactionStatus": "Status Bestandstransaktion", "@sage/xtrem-stock/nodes__stock_value_change__property__totalQuantityInStock": "Gesamtmenge in Bestand", "@sage/xtrem-stock/nodes__stock_value_change__property__totalValueOfStock": "Gesamtwert Bestand", "@sage/xtrem-stock/nodes__stock_value_change__property__transactionCurrency": "Transaktionswährung", "@sage/xtrem-stock/nodes__stock_value_change__property__unitCost": "Stückkosten", "@sage/xtrem-stock/nodes__stock_value_change__property__valuationMethod": "Bewertungsmethode", "@sage/xtrem-stock/nodes__stock_value_change_line__asyncMutation__asyncExport": "Export", "@sage/xtrem-stock/nodes__stock_value_change_line__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-stock/nodes__stock_value_change_line__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-stock/nodes__stock_value_change_line__check_amount_stock_detail": "Der Betrag in stockDetails {{totalStockDetails}} entspricht nicht der Wertänderungsabweichung in der Dokumentzeile {{varianceLine}}", "@sage/xtrem-stock/nodes__stock_value_change_line__node_name": "Zeile Änderung Bestandswert", "@sage/xtrem-stock/nodes__stock_value_change_line__property__amount": "Betrag", "@sage/xtrem-stock/nodes__stock_value_change_line__property__analyticalData": "Analytische Daten", "@sage/xtrem-stock/nodes__stock_value_change_line__property__computedAttributes": "Berechnete Attribute", "@sage/xtrem-stock/nodes__stock_value_change_line__property__currency": "Währung", "@sage/xtrem-stock/nodes__stock_value_change_line__property__document": "Dokument", "@sage/xtrem-stock/nodes__stock_value_change_line__property__documentId": "Dokument-ID", "@sage/xtrem-stock/nodes__stock_value_change_line__property__documentNumber": "Dokumentnummer", "@sage/xtrem-stock/nodes__stock_value_change_line__property__effectiveDate": "Aktivierungsdatum", "@sage/xtrem-stock/nodes__stock_value_change_line__property__fifoCost": "FIFO-Preis", "@sage/xtrem-stock/nodes__stock_value_change_line__property__item": "Artikel", "@sage/xtrem-stock/nodes__stock_value_change_line__property__jsonStockDetails": "Bestandsdetails JSON", "@sage/xtrem-stock/nodes__stock_value_change_line__property__newAmount": "<PERSON>euer Betrag", "@sage/xtrem-stock/nodes__stock_value_change_line__property__newUnitCost": "Neue Stückkosten", "@sage/xtrem-stock/nodes__stock_value_change_line__property__quantity": "<PERSON><PERSON>", "@sage/xtrem-stock/nodes__stock_value_change_line__property__quantityInStockUnit": "Menge in Lagereinheit", "@sage/xtrem-stock/nodes__stock_value_change_line__property__site": "<PERSON><PERSON>", "@sage/xtrem-stock/nodes__stock_value_change_line__property__stockDetails": "Bestandsdetails", "@sage/xtrem-stock/nodes__stock_value_change_line__property__stockMovements": "Bestandsbewegungen", "@sage/xtrem-stock/nodes__stock_value_change_line__property__stockTransactions": "Bestandstransaktionen", "@sage/xtrem-stock/nodes__stock_value_change_line__property__stockTransactionStatus": "Status Bestandstransaktion", "@sage/xtrem-stock/nodes__stock_value_change_line__property__stockUnit": "Lagereinheit", "@sage/xtrem-stock/nodes__stock_value_change_line__property__storedAttributes": "Gespeicherte Attribute", "@sage/xtrem-stock/nodes__stock_value_change_line__property__storedDimensions": "Gespeicherte Sektoren", "@sage/xtrem-stock/nodes__stock_value_change_line__property__unitCost": "Stückkosten", "@sage/xtrem-stock/nodes__stock_value_correction__asyncMutation__asyncExport": "Export", "@sage/xtrem-stock/nodes__stock_value_correction__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-stock/nodes__stock_value_correction__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-stock/nodes__stock_value_correction__mutation__postToStock": "Buchung in Bestand", "@sage/xtrem-stock/nodes__stock_value_correction__mutation__postToStock__failed": "Buchung in Bestand fehlgeschlagen.", "@sage/xtrem-stock/nodes__stock_value_correction__mutation__postToStock__parameter__documentIds": "Dokument-IDs", "@sage/xtrem-stock/nodes__stock_value_correction__node_name": "Korrektur Bestandswert", "@sage/xtrem-stock/nodes__stock_value_correction__property__description": "Bezeichnung", "@sage/xtrem-stock/nodes__stock_value_correction__property__documentDate": "Dokumentdatum", "@sage/xtrem-stock/nodes__stock_value_correction__property__effectiveDate": "Aktivierungsdatum", "@sage/xtrem-stock/nodes__stock_value_correction__property__financialSite": "Buchhaltungsstandort", "@sage/xtrem-stock/nodes__stock_value_correction__property__lines": "<PERSON><PERSON><PERSON>", "@sage/xtrem-stock/nodes__stock_value_correction__property__number": "<PERSON><PERSON><PERSON>", "@sage/xtrem-stock/nodes__stock_value_correction__property__reasonCode": "Grundcode", "@sage/xtrem-stock/nodes__stock_value_correction__property__status": "Status", "@sage/xtrem-stock/nodes__stock_value_correction__property__stockSite": "Lagerstandort", "@sage/xtrem-stock/nodes__stock_value_correction__property__stockTransactionStatus": "Status Bestandstransaktion", "@sage/xtrem-stock/nodes__stock_value_correction__property__transactionCurrency": "Transaktionswährung", "@sage/xtrem-stock/nodes__stock_value_correction_line__asyncMutation__asyncExport": "Export", "@sage/xtrem-stock/nodes__stock_value_correction_line__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-stock/nodes__stock_value_correction_line__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-stock/nodes__stock_value_correction_line__invalid_document_line": "Der Bestandstransaktionssatus der korrigierten Dokumentzeile muss erledigt sein.", "@sage/xtrem-stock/nodes__stock_value_correction_line__no_corrected_line": "Sie müssen die Dokumentzeile angeben, die korrigiert werden soll.", "@sage/xtrem-stock/nodes__stock_value_correction_line__node_name": "Zeile Korrektur Bestandswert", "@sage/xtrem-stock/nodes__stock_value_correction_line__property__analyticalData": "Analytische Daten", "@sage/xtrem-stock/nodes__stock_value_correction_line__property__computedAttributes": "Berechnete Attribute", "@sage/xtrem-stock/nodes__stock_value_correction_line__property__correctedDocumentLine": "Korrigierte Dokumentzeile", "@sage/xtrem-stock/nodes__stock_value_correction_line__property__correctedDocumentType": "Korrigierter Dokumenttyp", "@sage/xtrem-stock/nodes__stock_value_correction_line__property__document": "Dokument", "@sage/xtrem-stock/nodes__stock_value_correction_line__property__documentId": "Dokument-ID", "@sage/xtrem-stock/nodes__stock_value_correction_line__property__documentNumber": "Dokumentnummer", "@sage/xtrem-stock/nodes__stock_value_correction_line__property__item": "Artikel", "@sage/xtrem-stock/nodes__stock_value_correction_line__property__itemSite": "Artikel-Standort", "@sage/xtrem-stock/nodes__stock_value_correction_line__property__newUnitCost": "Neue Stückkosten", "@sage/xtrem-stock/nodes__stock_value_correction_line__property__orderCost": "Auftragskosten", "@sage/xtrem-stock/nodes__stock_value_correction_line__property__site": "<PERSON><PERSON>", "@sage/xtrem-stock/nodes__stock_value_correction_line__property__stockDetails": "Bestandsdetails", "@sage/xtrem-stock/nodes__stock_value_correction_line__property__stockMovements": "Bestandsbewegungen", "@sage/xtrem-stock/nodes__stock_value_correction_line__property__stockTransactions": "Bestandstransaktion", "@sage/xtrem-stock/nodes__stock_value_correction_line__property__stockTransactionStatus": "Status Bestandstransaktion", "@sage/xtrem-stock/nodes__stock_value_correction_line__property__storedAttributes": "Gespeicherte Attribute", "@sage/xtrem-stock/nodes__stock_value_correction_line__property__storedDimensions": "Gespeicherte Sektoren", "@sage/xtrem-stock/nodes__stock_value_correction_line__property__unitCost": "Stückkosten", "@sage/xtrem-stock/nodes__stock_value_correction_line__property__valuedCost": "Bewertete Kosten", "@sage/xtrem-stock/nodes__stock-adjustment__cant_post_stock_adjustment_when_stock_detail_status_is_required": "Sie müssen Bestandsdetails für alle Zeilen vor der Buchung erfassen.", "@sage/xtrem-stock/nodes__stock-adjustment__cant_repost_stock_adjustment_when_status_is_not_failed": "Sie können eine Bestandskorrektur nur erneut buchen, wenn der Status 'Fehlgeschlagen' oder 'Nicht erfasst' ist.", "@sage/xtrem-stock/nodes__stock-adjustment__company_has_no_stock_posting": "<PERSON>s wurde kein <PERSON>and auf dieses Unternehmen gebucht.", "@sage/xtrem-stock/nodes__stock-adjustment__document_was_posted": "Die Bestandskorrektur wurde gebucht.", "@sage/xtrem-stock/nodes__stock-issue__cant_repost_stock_issue_when_status_is_not_failed": "<PERSON>e können einen Bestandsabgang nur erneut buchen, wenn der Status 'Fehlgeschlagen' oder 'Nicht erfasst' ist.", "@sage/xtrem-stock/nodes__stock-issue__company_has_no_stock_posting": "Das aktuelle Unternehmen hat keine Bestandsbuchung.", "@sage/xtrem-stock/nodes__stock-issue__document_was_posted": "Der Bestandseingang wurde gebucht.", "@sage/xtrem-stock/nodes__stock-receipt__cant_repost_stock_receipt_when_status_is_not_failed": "<PERSON>e können einen Bestandseingang nur erneut buchen, wenn der Status 'Fehlgeschlagen' oder 'Nicht erfasst' ist.", "@sage/xtrem-stock/nodes__stock-receipt__company_has_no_stock_posting": "Das aktuelle Unternehmen hat keine Bestandsbuchung.", "@sage/xtrem-stock/nodes__stock-receipt__document_was_posted": "Der Bestandseingang wurde gebucht.", "@sage/xtrem-stock/package__name": "Bestand", "@sage/xtrem-stock/page__stock_count__add_new_line": "Neue Zeile hinzufügen", "@sage/xtrem-stock/page__stock_count__forbid_addition_serialized_managed_item": "Eine Bestandszeile für einen Artikel mit Seriennummer kann nicht zu einer bestehenden Inventur hinzugefügt werden.", "@sage/xtrem-stock/page-extensions__bill_of_material_component_panel_extension__availableQuantityInStockUnit____title": "Verfügbare Bestandsmenge", "@sage/xtrem-stock/page-extensions__bill_of_material_component_panel_extension__stockOnHand____title": "<PERSON>orr<PERSON><PERSON> Bestand", "@sage/xtrem-stock/page-extensions__bill_of_material_component_panel_extension__stockShortageInStockUnit____title": "Fehlbestand", "@sage/xtrem-stock/page-extensions__bill_of_material_component_panel_extension__stockShortageStatus____title": "Status Fehlbestand", "@sage/xtrem-stock/page-extensions__bill_of_material_extension__components____columns__title__availableQuantityInStockUnit": "Verfügbare Bestandsmenge", "@sage/xtrem-stock/page-extensions__bill_of_material_extension__components____columns__title__stockShortageInStockUnit": "Fehlbestand", "@sage/xtrem-stock/page-extensions__create_test_data_extension__isStockQuantityFixed____title": "Feste Bestandsmenge für alle Artikel", "@sage/xtrem-stock/page-extensions__create_test_data_extension__item____columns__title__description": "Bezeichnung", "@sage/xtrem-stock/page-extensions__create_test_data_extension__item____columns__title__id": "Artikel-ID", "@sage/xtrem-stock/page-extensions__create_test_data_extension__item____columns__title__name": "Name", "@sage/xtrem-stock/page-extensions__create_test_data_extension__item____lookupDialogTitle": "Artikel auswählen", "@sage/xtrem-stock/page-extensions__create_test_data_extension__item____title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-stock/page-extensions__create_test_data_extension__itemBlock____title": "Artikel", "@sage/xtrem-stock/page-extensions__create_test_data_extension__itemQuantity____title": "<PERSON><PERSON> an <PERSON>ikeln", "@sage/xtrem-stock/page-extensions__create_test_data_extension__maxStockQuantity____title": "Höchstbestandsmenge", "@sage/xtrem-stock/page-extensions__item_site_extension__countingInProgress____title": "Invent<PERSON> l<PERSON>", "@sage/xtrem-stock/page-extensions__item_site_extension__countingInProgressMention____title": "Invent<PERSON> l<PERSON>", "@sage/xtrem-stock/page-extensions__item_site_extension__lastCountDate____title": "Datum letzte Inventur", "@sage/xtrem-stock/pages__bill_of_material_component_panel_extension__display_shortage_status_false": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__bill_of_material_component_panel_extension__display_shortage_status_true": "Fehlbestand", "@sage/xtrem-stock/pages__cost_roll_up_sub_assembly_dialog____title": "Unterbaugruppen", "@sage/xtrem-stock/pages__cost_roll_up_sub_assembly_dialog____update_title": "Artikel: {{itemName}}", "@sage/xtrem-stock/pages__cost_roll_up_sub_assembly_dialog__mainSection____title": "Unterbaugruppen", "@sage/xtrem-stock/pages__cost_roll_up_sub_assembly_dialog__ok____title": "OK", "@sage/xtrem-stock/pages__cost_roll_up_sub_assembly_dialog__subAssemblies____columns__title__calculationQuantity": "Berechnungsmenge", "@sage/xtrem-stock/pages__cost_roll_up_sub_assembly_dialog__subAssemblies____columns__title__componentNumber": "Komponentennummer", "@sage/xtrem-stock/pages__cost_roll_up_sub_assembly_dialog__subAssemblies____columns__title__currentLaborCost": "Aktuelle Arbeitskraftkosten", "@sage/xtrem-stock/pages__cost_roll_up_sub_assembly_dialog__subAssemblies____columns__title__currentMachineCost": "Aktuelle Maschinenkosten", "@sage/xtrem-stock/pages__cost_roll_up_sub_assembly_dialog__subAssemblies____columns__title__currentMaterialCost": "Aktuelle Materialkosten", "@sage/xtrem-stock/pages__cost_roll_up_sub_assembly_dialog__subAssemblies____columns__title__currentToolCost": "Aktuelle Werkzeugkosten", "@sage/xtrem-stock/pages__cost_roll_up_sub_assembly_dialog__subAssemblies____columns__title__currentTotalCost": "Aktuelle Gesamtkosten", "@sage/xtrem-stock/pages__cost_roll_up_sub_assembly_dialog__subAssemblies____columns__title__item__id": "Artikel-ID", "@sage/xtrem-stock/pages__cost_roll_up_sub_assembly_dialog__subAssemblies____columns__title__item__name": "Artikelname", "@sage/xtrem-stock/pages__cost_roll_up_sub_assembly_dialog__subAssemblies____columns__title__laborCost": "Arbeitskraftkosten", "@sage/xtrem-stock/pages__cost_roll_up_sub_assembly_dialog__subAssemblies____columns__title__machineCost": "Maschinenkosten", "@sage/xtrem-stock/pages__cost_roll_up_sub_assembly_dialog__subAssemblies____columns__title__materialCost": "Materialkosten", "@sage/xtrem-stock/pages__cost_roll_up_sub_assembly_dialog__subAssemblies____columns__title__toolCost": "Werkzeugkosten", "@sage/xtrem-stock/pages__cost_roll_up_sub_assembly_dialog__subAssemblies____columns__title__totalCost": "<PERSON><PERSON> gesamt", "@sage/xtrem-stock/pages__cost_roll_up_sub_assembly_dialog__subAssemblies____title": "Unterbaugruppen", "@sage/xtrem-stock/pages__create-test-data__create_items_validation_item": "Wählen Sie einen Artikel aus.", "@sage/xtrem-stock/pages__create-test-data__create_items_validation_max_stock_quantity": "Erfassen Sie eine Menge größer als 0.", "@sage/xtrem-stock/pages__create-test-data__create_items_validation_quantity": "Erfassen Sie eine Menge größer als 0.", "@sage/xtrem-stock/pages__fifo_valuation_tier_table_panel____title": "Aus FIFO-Stapel hinzufügen", "@sage/xtrem-stock/pages__fifo_valuation_tier_table_panel__cancel____title": "Abbrechen", "@sage/xtrem-stock/pages__fifo_valuation_tier_table_panel__confirm____title": "Hinzufügen", "@sage/xtrem-stock/pages__fifo_valuation_tier_table_panel__fifoValuationTierLines____columns__title__amount": "Betrag", "@sage/xtrem-stock/pages__fifo_valuation_tier_table_panel__fifoValuationTierLines____columns__title__effectiveDate": "Aktivierungsdatum", "@sage/xtrem-stock/pages__fifo_valuation_tier_table_panel__fifoValuationTierLines____columns__title__nonAbsorbedAmount": "Nicht absorbierter Betrag", "@sage/xtrem-stock/pages__fifo_valuation_tier_table_panel__fifoValuationTierLines____columns__title__receiptDocumentLine__documentNumber": "Dokumentnummer", "@sage/xtrem-stock/pages__fifo_valuation_tier_table_panel__fifoValuationTierLines____columns__title__receiptQuantity": "Eingangsmenge", "@sage/xtrem-stock/pages__fifo_valuation_tier_table_panel__fifoValuationTierLines____columns__title__remainingQuantity": "Verbleibende Menge", "@sage/xtrem-stock/pages__fifo_valuation_tier_table_panel__fifoValuationTierLines____columns__title__sequence": "Nummernkreis", "@sage/xtrem-stock/pages__fifo_valuation_tier_table_panel__fifoValuationTierLines____columns__title__unitCost": "Stückkosten", "@sage/xtrem-stock/pages__item_site_cost__dimensions_button_text": "Sektoren", "@sage/xtrem-stock/pages__mrp_calculation____navigationPanel__bulkActions__title": "Löschen", "@sage/xtrem-stock/pages__mrp_calculation____navigationPanel__listItem__line2Right__title": "Status", "@sage/xtrem-stock/pages__mrp_calculation____navigationPanel__listItem__numberOfWeeks__title": "Zeitraum in Wochen", "@sage/xtrem-stock/pages__mrp_calculation____navigationPanel__listItem__titleRight__title": "Berechnungsdatum", "@sage/xtrem-stock/pages__mrp_calculation____navigationPanel__listItem__user__title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__mrp_calculation____objectTypePlural": "Berechnungsergebnisse MRP", "@sage/xtrem-stock/pages__mrp_calculation____objectTypeSingular": "Berechnungsergebnis MRP", "@sage/xtrem-stock/pages__mrp_calculation____title": "MRP-Berechnung", "@sage/xtrem-stock/pages__mrp_calculation__calculationDate____title": "Berechnungsdatum", "@sage/xtrem-stock/pages__mrp_calculation__calculationStatus____title": "Status", "@sage/xtrem-stock/pages__mrp_calculation__company____columns__title__id": "ID", "@sage/xtrem-stock/pages__mrp_calculation__company____columns__title__name": "Name", "@sage/xtrem-stock/pages__mrp_calculation__company____placeholder": "Auswählen...", "@sage/xtrem-stock/pages__mrp_calculation__company____title": "Unternehmen", "@sage/xtrem-stock/pages__mrp_calculation__createMrpCalculationRequest____title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__mrp_calculation__criteriaBlock____title": "Kriterien", "@sage/xtrem-stock/pages__mrp_calculation__description____title": "Bezeichnung", "@sage/xtrem-stock/pages__mrp_calculation__errorMessage____title": "Fehlermeldung", "@sage/xtrem-stock/pages__mrp_calculation__explodeBillOfMaterial____title": "Stückliste auflösen", "@sage/xtrem-stock/pages__mrp_calculation__fromItem____title": "<PERSON><PERSON><PERSON> von", "@sage/xtrem-stock/pages__mrp_calculation__fromItemText____title": "<PERSON><PERSON><PERSON> von", "@sage/xtrem-stock/pages__mrp_calculation__isSalesQuoteIncluded____title": "Angebote einschließen", "@sage/xtrem-stock/pages__mrp_calculation__itemFrom____columns__title__description": "Bezeichnung", "@sage/xtrem-stock/pages__mrp_calculation__itemFrom____columns__title__id": "ID", "@sage/xtrem-stock/pages__mrp_calculation__itemFrom____columns__title__name": "Name", "@sage/xtrem-stock/pages__mrp_calculation__itemFrom____placeholder": "Auswählen...", "@sage/xtrem-stock/pages__mrp_calculation__itemFrom____title": "<PERSON><PERSON><PERSON> von", "@sage/xtrem-stock/pages__mrp_calculation__itemTo____columns__title__description": "Bezeichnung", "@sage/xtrem-stock/pages__mrp_calculation__itemTo____columns__title__id": "Artikel", "@sage/xtrem-stock/pages__mrp_calculation__itemTo____columns__title__name": "Name", "@sage/xtrem-stock/pages__mrp_calculation__itemTo____placeholder": "Auswählen...", "@sage/xtrem-stock/pages__mrp_calculation__itemTo____title": "Artikel bis", "@sage/xtrem-stock/pages__mrp_calculation__linesSection____title": "Ergebnisse", "@sage/xtrem-stock/pages__mrp_calculation__lowLevelCode____title": "Niedrigste Stücklistenebene", "@sage/xtrem-stock/pages__mrp_calculation__mainSection____title": "Allgemein", "@sage/xtrem-stock/pages__mrp_calculation__multiRefCompanies____columns__title__id": "ID", "@sage/xtrem-stock/pages__mrp_calculation__multiRefCompanies____columns__title__name": "Name", "@sage/xtrem-stock/pages__mrp_calculation__multiRefCompanies____lookupDialogTitle": "Unternehmen auswählen", "@sage/xtrem-stock/pages__mrp_calculation__multiRefCompanies____title": "Unternehmen", "@sage/xtrem-stock/pages__mrp_calculation__multiRefSites____columns__title__id": "ID", "@sage/xtrem-stock/pages__mrp_calculation__multiRefSites____columns__title__name": "Name", "@sage/xtrem-stock/pages__mrp_calculation__multiRefSites____lookupDialogTitle": "Standort auswählen", "@sage/xtrem-stock/pages__mrp_calculation__multiRefSites____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__mrp_calculation__numberOfWeeks____title": "Zeitraum in Wochen", "@sage/xtrem-stock/pages__mrp_calculation__purchase_order_creation__success": "Bestellung {{num}} erstellt.", "@sage/xtrem-stock/pages__mrp_calculation__resultLines____columns__columns__uCompany__name__title": "Name", "@sage/xtrem-stock/pages__mrp_calculation__resultLines____columns__columns__uCompany__name__title__2": "ID", "@sage/xtrem-stock/pages__mrp_calculation__resultLines____columns__columns__uSite__name__title": "Name", "@sage/xtrem-stock/pages__mrp_calculation__resultLines____columns__columns__uSite__name__title__2": "ID", "@sage/xtrem-stock/pages__mrp_calculation__resultLines____columns__title___sortValue": "Sortierwert", "@sage/xtrem-stock/pages__mrp_calculation__resultLines____columns__title__company": "Unternehmen", "@sage/xtrem-stock/pages__mrp_calculation__resultLines____columns__title__company__id": "Unternehmens-ID", "@sage/xtrem-stock/pages__mrp_calculation__resultLines____columns__title__company__name": "Unternehmensname", "@sage/xtrem-stock/pages__mrp_calculation__resultLines____columns__title__endDate": "Enddatum", "@sage/xtrem-stock/pages__mrp_calculation__resultLines____columns__title__item": "Artikel", "@sage/xtrem-stock/pages__mrp_calculation__resultLines____columns__title__item__id": "Artikel-ID", "@sage/xtrem-stock/pages__mrp_calculation__resultLines____columns__title__item__name": "Artikel", "@sage/xtrem-stock/pages__mrp_calculation__resultLines____columns__title__itemName": "Artikel", "@sage/xtrem-stock/pages__mrp_calculation__resultLines____columns__title__mrpCalculation__startDate": "<PERSON><PERSON><PERSON>chlagsda<PERSON>", "@sage/xtrem-stock/pages__mrp_calculation__resultLines____columns__title__preferredProcess": "Wiederbeschaffungsart", "@sage/xtrem-stock/pages__mrp_calculation__resultLines____columns__title__preferredProcessString": "Wiederbeschaffungsart", "@sage/xtrem-stock/pages__mrp_calculation__resultLines____columns__title__quantity": "<PERSON><PERSON>", "@sage/xtrem-stock/pages__mrp_calculation__resultLines____columns__title__referenceItem__description": "Artikelbezeichnung", "@sage/xtrem-stock/pages__mrp_calculation__resultLines____columns__title__referenceItem__id": "Artikel-ID", "@sage/xtrem-stock/pages__mrp_calculation__resultLines____columns__title__site": "<PERSON><PERSON>", "@sage/xtrem-stock/pages__mrp_calculation__resultLines____columns__title__site__id": "Standort-ID", "@sage/xtrem-stock/pages__mrp_calculation__resultLines____columns__title__site__name": "Standortname", "@sage/xtrem-stock/pages__mrp_calculation__resultLines____columns__title__startDate": "Startdatum", "@sage/xtrem-stock/pages__mrp_calculation__resultLines____columns__title__suggestionStatus": "Status Vorschlag", "@sage/xtrem-stock/pages__mrp_calculation__resultLines____columns__title__uCompany__name": "Unternehmen", "@sage/xtrem-stock/pages__mrp_calculation__resultLines____columns__title__uSite__name": "<PERSON><PERSON>", "@sage/xtrem-stock/pages__mrp_calculation__resultLines____dropdownActions__title": "Bestellung erstellen", "@sage/xtrem-stock/pages__mrp_calculation__resultLines____dropdownActions__title__2": "Fertigungsauftrag erstellen", "@sage/xtrem-stock/pages__mrp_calculation__resultLines____title": "Details", "@sage/xtrem-stock/pages__mrp_calculation__sites____columns__title__id": "ID", "@sage/xtrem-stock/pages__mrp_calculation__sites____columns__title__name": "Name", "@sage/xtrem-stock/pages__mrp_calculation__sites____placeholder": "Auswählen...", "@sage/xtrem-stock/pages__mrp_calculation__sites____title": "<PERSON><PERSON>", "@sage/xtrem-stock/pages__mrp_calculation__startDate____title": "Startdatum", "@sage/xtrem-stock/pages__mrp_calculation__toItem____title": "Artikel bis", "@sage/xtrem-stock/pages__mrp_calculation__toItemText____title": "Artikel bis", "@sage/xtrem-stock/pages__mrp_calculation__user____columns__title__email": "E-Mail", "@sage/xtrem-stock/pages__mrp_calculation__user____columns__title__firstName": "<PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__mrp_calculation__user____columns__title__lastName": "Nachname", "@sage/xtrem-stock/pages__mrp_calculation__user____lookupDialogTitle": "Benutzer auswählen", "@sage/xtrem-stock/pages__mrp_calculation__user____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__mrp_calculation__valuationDate____title": "Startdatum", "@sage/xtrem-stock/pages__mrp_calculation__work_order_creation__success": "Fertigungsauftrag {{num}} erstellt.", "@sage/xtrem-stock/pages__mrp_calculation_justification____navigationPanel__listItem__calculationStatus__title": "Status", "@sage/xtrem-stock/pages__mrp_calculation_justification____navigationPanel__listItem__numberOfWeeks__title": "Zeitraum in Wochen", "@sage/xtrem-stock/pages__mrp_calculation_justification____navigationPanel__listItem__titleRight__title": "Berechnungsdatum", "@sage/xtrem-stock/pages__mrp_calculation_justification____navigationPanel__listItem__user__title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__mrp_calculation_justification____objectTypePlural": "Begründungen MRP-Berechnung", "@sage/xtrem-stock/pages__mrp_calculation_justification____objectTypeSingular": "Begründung MRP-Berechnung", "@sage/xtrem-stock/pages__mrp_calculation_justification____title": "Begründung MRP-Berechnung", "@sage/xtrem-stock/pages__mrp_calculation_justification__calculationDate____title": "Berechnungsdatum", "@sage/xtrem-stock/pages__mrp_calculation_justification__calculationStatus____title": "Status", "@sage/xtrem-stock/pages__mrp_calculation_justification__criteriaBlock____title": "Kriterien", "@sage/xtrem-stock/pages__mrp_calculation_justification__description____title": "Bezeichnung", "@sage/xtrem-stock/pages__mrp_calculation_justification__fromItem____title": "<PERSON><PERSON><PERSON> von", "@sage/xtrem-stock/pages__mrp_calculation_justification__fromItemText____title": "<PERSON><PERSON><PERSON> von", "@sage/xtrem-stock/pages__mrp_calculation_justification__inputSet____columns__title___id": "ID", "@sage/xtrem-stock/pages__mrp_calculation_justification__inputSet____title": "Eingabemenge", "@sage/xtrem-stock/pages__mrp_calculation_justification__mainSection____title": "Allgemein", "@sage/xtrem-stock/pages__mrp_calculation_justification__multiRefCompanies____columns__title__id": "ID", "@sage/xtrem-stock/pages__mrp_calculation_justification__multiRefCompanies____columns__title__name": "Name", "@sage/xtrem-stock/pages__mrp_calculation_justification__multiRefCompanies____lookupDialogTitle": "Unternehmen auswählen", "@sage/xtrem-stock/pages__mrp_calculation_justification__multiRefCompanies____title": "Unternehmen", "@sage/xtrem-stock/pages__mrp_calculation_justification__multiRefSites____columns__columns__legalCompany__name__title": "ID", "@sage/xtrem-stock/pages__mrp_calculation_justification__multiRefSites____columns__title__id": "ID", "@sage/xtrem-stock/pages__mrp_calculation_justification__multiRefSites____columns__title__legalCompany__name": "Unternehmen", "@sage/xtrem-stock/pages__mrp_calculation_justification__multiRefSites____columns__title__name": "Name", "@sage/xtrem-stock/pages__mrp_calculation_justification__multiRefSites____lookupDialogTitle": "Standort auswählen", "@sage/xtrem-stock/pages__mrp_calculation_justification__multiRefSites____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__mrp_calculation_justification__numberOfWeeks____title": "Zeitraum in Wochen", "@sage/xtrem-stock/pages__mrp_calculation_justification__results____columns__title__company__id": "Unternehmens-ID", "@sage/xtrem-stock/pages__mrp_calculation_justification__results____columns__title__company__name": "Unternehmensname", "@sage/xtrem-stock/pages__mrp_calculation_justification__results____columns__title__firstSuggestionDate": "<PERSON><PERSON><PERSON>chlagsda<PERSON>", "@sage/xtrem-stock/pages__mrp_calculation_justification__results____columns__title__item__id": "Artikel-ID", "@sage/xtrem-stock/pages__mrp_calculation_justification__results____columns__title__itemName": "Artikelname", "@sage/xtrem-stock/pages__mrp_calculation_justification__results____columns__title__site__id": "Standort-ID", "@sage/xtrem-stock/pages__mrp_calculation_justification__results____columns__title__site__name": "Standortname", "@sage/xtrem-stock/pages__mrp_calculation_justification__results____title": "Details", "@sage/xtrem-stock/pages__mrp_calculation_justification__startDate____title": "Startdatum", "@sage/xtrem-stock/pages__mrp_calculation_justification__toItem____title": "Artikel bis", "@sage/xtrem-stock/pages__mrp_calculation_justification__toItemText____title": "Artikel bis", "@sage/xtrem-stock/pages__mrp_calculation_justification__user____columns__title__email": "E-Mail", "@sage/xtrem-stock/pages__mrp_calculation_justification__user____columns__title__firstName": "<PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__mrp_calculation_justification__user____columns__title__lastName": "Nachname", "@sage/xtrem-stock/pages__mrp_calculation_justification__user____lookupDialogTitle": "Benutzer auswählen", "@sage/xtrem-stock/pages__mrp_calculation_justification__user____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__mrp_calculation_request____title": "Anforderung MRP-Berechnung", "@sage/xtrem-stock/pages__mrp_calculation_request__cancelRequest____title": "Abbrechen", "@sage/xtrem-stock/pages__mrp_calculation_request__companies____columns__title__id": "ID", "@sage/xtrem-stock/pages__mrp_calculation_request__companies____columns__title__name": "Name", "@sage/xtrem-stock/pages__mrp_calculation_request__companies____lookupDialogTitle": "Unternehmen auswählen", "@sage/xtrem-stock/pages__mrp_calculation_request__companies____placeholder": "Auswählen ...", "@sage/xtrem-stock/pages__mrp_calculation_request__companies____title": "Unternehmen", "@sage/xtrem-stock/pages__mrp_calculation_request__company____columns__title__id": "ID", "@sage/xtrem-stock/pages__mrp_calculation_request__company____columns__title__name": "Name", "@sage/xtrem-stock/pages__mrp_calculation_request__company____placeholder": "Auswählen...", "@sage/xtrem-stock/pages__mrp_calculation_request__company____title": "Unternehmen", "@sage/xtrem-stock/pages__mrp_calculation_request__criteriaBlock____title": "Kriterien", "@sage/xtrem-stock/pages__mrp_calculation_request__description____title": "Bezeichnung", "@sage/xtrem-stock/pages__mrp_calculation_request__explodeBillOfMaterial____title": "Stückliste auflösen", "@sage/xtrem-stock/pages__mrp_calculation_request__invalid_criteria": "Für die erfassten Kriterien wurden keine Artikel-Standort-Datensätze durch die MRP wiederbeschafft.", "@sage/xtrem-stock/pages__mrp_calculation_request__invalid_item_from_range": "Der Wert 'Artikel von' kann nicht größer als der Wert 'Artikel bis' sein.", "@sage/xtrem-stock/pages__mrp_calculation_request__invalid_item_to_range": "Der Wert 'Artikel bis' kann nicht kleiner als der Wert 'Artikel von' sein.", "@sage/xtrem-stock/pages__mrp_calculation_request__isSalesQuoteIncluded____title": "Angebote einschließen", "@sage/xtrem-stock/pages__mrp_calculation_request__itemFrom____columns__title__description": "Bezeichnung", "@sage/xtrem-stock/pages__mrp_calculation_request__itemFrom____columns__title__id": "ID", "@sage/xtrem-stock/pages__mrp_calculation_request__itemFrom____columns__title__name": "Name", "@sage/xtrem-stock/pages__mrp_calculation_request__itemFrom____lookupDialogTitle": "Artikel auswählen", "@sage/xtrem-stock/pages__mrp_calculation_request__itemFrom____placeholder": "Auswählen ...", "@sage/xtrem-stock/pages__mrp_calculation_request__itemFrom____title": "<PERSON><PERSON><PERSON> von", "@sage/xtrem-stock/pages__mrp_calculation_request__itemTo____columns__title__category__name": "<PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__mrp_calculation_request__itemTo____lookupDialogTitle": "Artikel auswählen", "@sage/xtrem-stock/pages__mrp_calculation_request__itemTo____placeholder": "Auswählen ...", "@sage/xtrem-stock/pages__mrp_calculation_request__itemTo____title": "Artikel bis", "@sage/xtrem-stock/pages__mrp_calculation_request__mainSection____title": "Allgemein", "@sage/xtrem-stock/pages__mrp_calculation_request__notification_success": "Anforderung MRP-Berechnung gesendet", "@sage/xtrem-stock/pages__mrp_calculation_request__numberOfWeeks____title": "Zeitraum in Wochen", "@sage/xtrem-stock/pages__mrp_calculation_request__scheduleMrpCalculationRequest____title": "Planen", "@sage/xtrem-stock/pages__mrp_calculation_request__sites____columns__title__id": "ID", "@sage/xtrem-stock/pages__mrp_calculation_request__sites____columns__title__name": "Name", "@sage/xtrem-stock/pages__mrp_calculation_request__sites____lookupDialogTitle": "Standort auswählen", "@sage/xtrem-stock/pages__mrp_calculation_request__sites____placeholder": "Auswählen ...", "@sage/xtrem-stock/pages__mrp_calculation_request__sites____title": "<PERSON><PERSON>", "@sage/xtrem-stock/pages__mrp_calculation_request__startDate____title": "Startdatum", "@sage/xtrem-stock/pages__mrp_calculation_request__supply_chain_error": "Es werden gerade Bestellungen aus früheren MRP-Berechnungen erstellt. Versuchen Sie es später noch einmal.", "@sage/xtrem-stock/pages__mrp_projected_stock_dialog____title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__mrp_projected_stock_dialog__batchQuantity____title": "Losmenge", "@sage/xtrem-stock/pages__mrp_projected_stock_dialog__chartBlock____title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__mrp_projected_stock_dialog__criteriaBlock____title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__mrp_projected_stock_dialog__day": "Tag", "@sage/xtrem-stock/pages__mrp_projected_stock_dialog__economicOrderQuantity____title": "Optimale Bestellmenge", "@sage/xtrem-stock/pages__mrp_projected_stock_dialog__frequency____title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__mrp_projected_stock_dialog__item____title": "Artikel", "@sage/xtrem-stock/pages__mrp_projected_stock_dialog__lines____columns__title__bucket": "Bucket", "@sage/xtrem-stock/pages__mrp_projected_stock_dialog__lines____columns__title__documentNumber": "Dokumentnummer", "@sage/xtrem-stock/pages__mrp_projected_stock_dialog__lines____columns__title__documentType": "Dokumenttyp", "@sage/xtrem-stock/pages__mrp_projected_stock_dialog__lines____columns__title__dueDate": "Fälligkeitsdatum", "@sage/xtrem-stock/pages__mrp_projected_stock_dialog__lines____columns__title__quantity": "<PERSON><PERSON>", "@sage/xtrem-stock/pages__mrp_projected_stock_dialog__lines____columns__title__suggestion": "Vorschlag", "@sage/xtrem-stock/pages__mrp_projected_stock_dialog__lines____title": "Details", "@sage/xtrem-stock/pages__mrp_projected_stock_dialog__mainSection____title": "Allgemeine Informationen", "@sage/xtrem-stock/pages__mrp_projected_stock_dialog__numberOfWeeks____title": "Zeitraum in Wochen", "@sage/xtrem-stock/pages__mrp_projected_stock_dialog__ok____title": "OK", "@sage/xtrem-stock/pages__mrp_projected_stock_dialog__preferredProcess____title": "Bevorzugte Verarbeitung", "@sage/xtrem-stock/pages__mrp_projected_stock_dialog__productionLeadTime____title": "Fertigungszeit", "@sage/xtrem-stock/pages__mrp_projected_stock_dialog__projectedStockChart____chart__series__title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__mrp_projected_stock_dialog__projectedStockChart____chart__series__title__2": "Sicherheitsbestand", "@sage/xtrem-stock/pages__mrp_projected_stock_dialog__projectedStockChart____chart__xAxis__title": "Datum", "@sage/xtrem-stock/pages__mrp_projected_stock_dialog__projectedStockChart____title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__mrp_projected_stock_dialog__purchaseLeadTime____title": "Einkaufszeit", "@sage/xtrem-stock/pages__mrp_projected_stock_dialog__safetyStock____title": "Sicherheitsbestand", "@sage/xtrem-stock/pages__mrp_projected_stock_dialog__site____title": "<PERSON><PERSON>", "@sage/xtrem-stock/pages__mrp_projected_stock_dialog__startDate____title": "Startdatum", "@sage/xtrem-stock/pages__mrp_projected_stock_dialog__starting_stock": "Ausgangsbestand", "@sage/xtrem-stock/pages__mrp_projected_stock_dialog__stockUnit____columns__title__decimalDigits": "Dezimalstellen", "@sage/xtrem-stock/pages__mrp_projected_stock_dialog__stockUnit____columns__title__symbol": "Symbol", "@sage/xtrem-stock/pages__mrp_projected_stock_dialog__week": "<PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__mrp_synchronization____navigationPanel__listItem__lastSyncDateTime__title": "Letzte Synchronisierung", "@sage/xtrem-stock/pages__mrp_synchronization____navigationPanel__listItem__titleRight__title": "Status letzte Synchronisierung", "@sage/xtrem-stock/pages__mrp_synchronization____title": "Synchronisierung MRP", "@sage/xtrem-stock/pages__mrp_synchronization___id____title": "ID", "@sage/xtrem-stock/pages__mrp_synchronization__criteriaBlock____title": "Kriterien", "@sage/xtrem-stock/pages__mrp_synchronization__lastSyncDateTime____title": "Letzte Synchronisierung", "@sage/xtrem-stock/pages__mrp_synchronization__mainSection____title": "Allgemein", "@sage/xtrem-stock/pages__mrp_synchronization__resetSynchronization____title": "Alle Datensätze synchronisieren", "@sage/xtrem-stock/pages__mrp_synchronization__synchronizeDeletions____title": "Löschvorgänge synchronisieren", "@sage/xtrem-stock/pages__mrp_synchronization__synchronizeMrp____title": "Synchroni<PERSON><PERSON>", "@sage/xtrem-stock/pages__mrp_synchronization__syncStatus____title": "Status letzte Synchronisierung", "@sage/xtrem-stock/pages__mrp_synchronization__type____title": "<PERSON><PERSON>", "@sage/xtrem-stock/pages__mrp_synchronization_request__notification_success": "Anforderung MRP-Synchronisierung gesendet.", "@sage/xtrem-stock/pages__serial_number_inquiry____navigationPanel__listItem__allocatedQuantity__title": "Reservierte Menge", "@sage/xtrem-stock/pages__serial_number_inquiry____navigationPanel__listItem__availableQuantity__title": "Verfügbar<PERSON>", "@sage/xtrem-stock/pages__serial_number_inquiry____navigationPanel__listItem__creationDate__title": "Erstellungsdatum Seriennummer", "@sage/xtrem-stock/pages__serial_number_inquiry____navigationPanel__listItem__documentLink__title": "Reserviert in", "@sage/xtrem-stock/pages__serial_number_inquiry____navigationPanel__listItem__id__title": "Seriennummer", "@sage/xtrem-stock/pages__serial_number_inquiry____navigationPanel__listItem__isAllocated__title": "Ist reserviert", "@sage/xtrem-stock/pages__serial_number_inquiry____navigationPanel__listItem__itemId__title": "Artikel-ID", "@sage/xtrem-stock/pages__serial_number_inquiry____navigationPanel__listItem__itemName__title": "Artikel", "@sage/xtrem-stock/pages__serial_number_inquiry____navigationPanel__listItem__locationId__title": "Lagerplatz-ID", "@sage/xtrem-stock/pages__serial_number_inquiry____navigationPanel__listItem__locationZone__title": "Lagerplatzbereich", "@sage/xtrem-stock/pages__serial_number_inquiry____navigationPanel__listItem__lot__title": "Charge", "@sage/xtrem-stock/pages__serial_number_inquiry____navigationPanel__listItem__lotCreationDate__title": "Datum Chargenerstellung", "@sage/xtrem-stock/pages__serial_number_inquiry____navigationPanel__listItem__lotExpirationDate__title": "Ablaufdatum Charge", "@sage/xtrem-stock/pages__serial_number_inquiry____navigationPanel__listItem__onHandQuantity__title": "Vorrätige Menge", "@sage/xtrem-stock/pages__serial_number_inquiry____navigationPanel__listItem__owner__title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__serial_number_inquiry____navigationPanel__listItem__siteId__title": "Standort-ID", "@sage/xtrem-stock/pages__serial_number_inquiry____navigationPanel__listItem__siteName__title": "<PERSON><PERSON>", "@sage/xtrem-stock/pages__serial_number_inquiry____navigationPanel__listItem__stockLocation__title": "Lagerplatz", "@sage/xtrem-stock/pages__serial_number_inquiry____navigationPanel__listItem__stockStatus__title": "Qualitätskontrolle", "@sage/xtrem-stock/pages__serial_number_inquiry____navigationPanel__listItem__stockUnit__title": "Einheit", "@sage/xtrem-stock/pages__serial_number_inquiry____navigationPanel__listItem__subLot__title": "Untercharge", "@sage/xtrem-stock/pages__serial_number_inquiry____navigationPanel__listItem__supplierId__title": "ID Lieferant", "@sage/xtrem-stock/pages__serial_number_inquiry____navigationPanel__listItem__supplierLot__title": "Lieferantenchargennummer", "@sage/xtrem-stock/pages__serial_number_inquiry____navigationPanel__listItem__supplierName__columns__title__businessEntity__id": "ID", "@sage/xtrem-stock/pages__serial_number_inquiry____navigationPanel__listItem__supplierName__columns__title__businessEntity__name": "Name", "@sage/xtrem-stock/pages__serial_number_inquiry____navigationPanel__listItem__supplierName__title": "Lieferant", "@sage/xtrem-stock/pages__serial_number_inquiry____navigationPanel__listItem__title__title": "Seriennummer", "@sage/xtrem-stock/pages__serial_number_inquiry____navigationPanel__optionsMenu__title": "Alle", "@sage/xtrem-stock/pages__serial_number_inquiry____title": "Bestandsabfrage Seriennummer", "@sage/xtrem-stock/pages__standard_cost_calculation____objectTypePlural": "Standardkostenberechnungen", "@sage/xtrem-stock/pages__standard_cost_calculation____objectTypeSingular": "Standardkostenberechnung", "@sage/xtrem-stock/pages__standard_cost_calculation____title": "Standardkostenberechnung", "@sage/xtrem-stock/pages__standard_cost_calculation__button_be_notified": "Benachrichtigen", "@sage/xtrem-stock/pages__standard_cost_calculation__button_wait": "<PERSON><PERSON>", "@sage/xtrem-stock/pages__standard_cost_calculation__calculation_dialog_content": "Diese Be<PERSON>chnung kann sehr lange dauern. <PERSON><PERSON><PERSON><PERSON> Si<PERSON> warten, bis die Berechnung abgeschlossen ist, oder diese Seite verlassen und benachrichtigt werden, wenn sie abgeschlossen ist?", "@sage/xtrem-stock/pages__standard_cost_calculation__commodityCode____title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__standard_cost_calculation__cost_creation_content": "Die Erstellung der Artikel-Standort-Kostendatensätze kann sehr lange dauern. <PERSON><PERSON><PERSON><PERSON> Si<PERSON> warten, während die Datensätze erstellt werden, oder diese Seite verlassen und benachrichtigt werden, wenn sie fertig sind?", "@sage/xtrem-stock/pages__standard_cost_calculation__costCategory____columns__title__costCategoryType": "<PERSON><PERSON>", "@sage/xtrem-stock/pages__standard_cost_calculation__costCategory____columns__title__isMandatory": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__standard_cost_calculation__costCategory____lookupDialogTitle": "Kostenkategorie auswählen", "@sage/xtrem-stock/pages__standard_cost_calculation__costCategory____title": "Kostenkategorie", "@sage/xtrem-stock/pages__standard_cost_calculation__criteriaBlock____title": "Kriterien", "@sage/xtrem-stock/pages__standard_cost_calculation__defaultDimension____title": "Standardsektoren setzen", "@sage/xtrem-stock/pages__standard_cost_calculation__dimensions_updated": "Aktualisierte Sektoren", "@sage/xtrem-stock/pages__standard_cost_calculation__fromDate____title": "Startdatum", "@sage/xtrem-stock/pages__standard_cost_calculation__includesRouting____title": "Arbeitsplan einschließen", "@sage/xtrem-stock/pages__standard_cost_calculation__isAllSelected____title": "Alle Ergebniszeilen auswählen", "@sage/xtrem-stock/pages__standard_cost_calculation__itemCategory____columns__title__type": "<PERSON><PERSON>", "@sage/xtrem-stock/pages__standard_cost_calculation__itemCategory____lookupDialogTitle": "Artikelkategorie auswählen", "@sage/xtrem-stock/pages__standard_cost_calculation__itemCategory____title": "Artikelkategorie", "@sage/xtrem-stock/pages__standard_cost_calculation__items____columns__title__id": "ID", "@sage/xtrem-stock/pages__standard_cost_calculation__items____columns__title__name": "Name", "@sage/xtrem-stock/pages__standard_cost_calculation__items____lookupDialogTitle": "Artikel auswählen", "@sage/xtrem-stock/pages__standard_cost_calculation__items____title": "Artikel", "@sage/xtrem-stock/pages__standard_cost_calculation__linesSection____title": "Ergebniszeilen", "@sage/xtrem-stock/pages__standard_cost_calculation__mainSection____title": "Allgemein", "@sage/xtrem-stock/pages__standard_cost_calculation__quantity____title": "<PERSON><PERSON>", "@sage/xtrem-stock/pages__standard_cost_calculation__request_notification_item_site_cost_creation_finished": "Erstellung Artikel-Standort-Kosten beendet.", "@sage/xtrem-stock/pages__standard_cost_calculation__request_notification_item_site_cost_creation_impossible": "<PERSON><PERSON> müssen zu<PERSON>t die Berechnung durchführen.", "@sage/xtrem-stock/pages__standard_cost_calculation__request_notification_item_site_cost_creation_request_sent": "Anforderung Erstellung Artikel-Standort-Kosten gesendet.", "@sage/xtrem-stock/pages__standard_cost_calculation__resultLines____columns__title__creationErrorMessage": "Fehlermeldung", "@sage/xtrem-stock/pages__standard_cost_calculation__resultLines____columns__title__creationStatus": "Status Erstellung Kosten", "@sage/xtrem-stock/pages__standard_cost_calculation__resultLines____columns__title__currentLaborCost": "Aktuelle Arbeitskraftkosten", "@sage/xtrem-stock/pages__standard_cost_calculation__resultLines____columns__title__currentMachineCost": "Aktuelle Maschinenkosten", "@sage/xtrem-stock/pages__standard_cost_calculation__resultLines____columns__title__currentMaterialCost": "Aktuelle Materialkosten", "@sage/xtrem-stock/pages__standard_cost_calculation__resultLines____columns__title__currentToolCost": "Aktuelle Werkzeugkosten", "@sage/xtrem-stock/pages__standard_cost_calculation__resultLines____columns__title__currentTotalCost": "Aktuelle Gesamtkosten", "@sage/xtrem-stock/pages__standard_cost_calculation__resultLines____columns__title__item__description": "Artikelbezeichnung", "@sage/xtrem-stock/pages__standard_cost_calculation__resultLines____columns__title__item__id": "Artikel-ID", "@sage/xtrem-stock/pages__standard_cost_calculation__resultLines____columns__title__item__name": "Artikel", "@sage/xtrem-stock/pages__standard_cost_calculation__resultLines____columns__title__laborCost": "Arbeitskraftkosten", "@sage/xtrem-stock/pages__standard_cost_calculation__resultLines____columns__title__machineCost": "Maschinenkosten", "@sage/xtrem-stock/pages__standard_cost_calculation__resultLines____columns__title__materialCost": "Materialkosten", "@sage/xtrem-stock/pages__standard_cost_calculation__resultLines____columns__title__toolCost": "Werkzeugkosten", "@sage/xtrem-stock/pages__standard_cost_calculation__resultLines____columns__title__totalCost": "<PERSON><PERSON> gesamt", "@sage/xtrem-stock/pages__standard_cost_calculation__resultLines____dropdownActions__title": "Unterbaugruppen", "@sage/xtrem-stock/pages__standard_cost_calculation__resultLines____dropdownActions__title__2": "Sektoren", "@sage/xtrem-stock/pages__standard_cost_calculation__runCreateItemSiteCostsFromRollUpResults____title": "Artikel-Standort-<PERSON>sten <PERSON>llen", "@sage/xtrem-stock/pages__standard_cost_calculation__runStandardCostCalculation____title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__standard_cost_calculation__site____columns__columns__legalCompany__name__title": "Währung", "@sage/xtrem-stock/pages__standard_cost_calculation__site____columns__title__id": "ID ", "@sage/xtrem-stock/pages__standard_cost_calculation__site____columns__title__legalCompany__name": "Unternehmen", "@sage/xtrem-stock/pages__standard_cost_calculation__site____columns__title__name": "Name", "@sage/xtrem-stock/pages__standard_cost_calculation__site____lookupDialogTitle": "Standort auswählen", "@sage/xtrem-stock/pages__standard_cost_calculation__site____title": "<PERSON><PERSON>", "@sage/xtrem-stock/pages__standard_cost_calculation__status____title": "Status", "@sage/xtrem-stock/pages__standard_cost_calculation__title_wait_for_finish": "<PERSON><PERSON>, bis die Berechnung abgeschlossen ist", "@sage/xtrem-stock/pages__standard_cost_calculation__user____columns__title__email": "E-Mail", "@sage/xtrem-stock/pages__standard_cost_calculation__user____columns__title__firstName": "<PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__standard_cost_calculation__user____columns__title__lastName": "Nachname", "@sage/xtrem-stock/pages__standard_cost_calculation__user____lookupDialogTitle": "Benutzer auswählen", "@sage/xtrem-stock/pages__standard_cost_calculation__user____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__standard_cost_calculation__usesComponentStandardCost____title": "Komponentenstandardkosten", "@sage/xtrem-stock/pages__standard_cost_calculation__wait_for_creation": "<PERSON><PERSON>, bis die Artikel-Standort-Kostendatensätze erstellt wurden", "@sage/xtrem-stock/pages__standard_cost_calculation_request__notification_request_sent": "Standardkostenberechnungsanforderung gesendet.", "@sage/xtrem-stock/pages__standard_cost_calculation_request__notification_valuation_failed": "Standardkostenberechnung fehlgeschlagen.", "@sage/xtrem-stock/pages__standard_cost_calculation_request__notification_valuation_finished": "Standardkostenberechnung durchgeführt.", "@sage/xtrem-stock/pages__stock__adjustment_step_sequence_detail_stock": "Details Bestand", "@sage/xtrem-stock/pages__stock_adjustment____navigationPanel__dropdownActions__title": "<PERSON><PERSON><PERSON><PERSON> setzen", "@sage/xtrem-stock/pages__stock_adjustment____navigationPanel__dropdownActions__title__2": "Löschen", "@sage/xtrem-stock/pages__stock_adjustment____navigationPanel__listItem__description__title": "Bezeichnung", "@sage/xtrem-stock/pages__stock_adjustment____navigationPanel__listItem__line2__title": "<PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_adjustment____navigationPanel__listItem__line2Right__title": "Datum", "@sage/xtrem-stock/pages__stock_adjustment____navigationPanel__listItem__reasonCode__title": "Korrekturgrund", "@sage/xtrem-stock/pages__stock_adjustment____navigationPanel__listItem__title__title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_adjustment____navigationPanel__listItem__titleRight__title": "Status", "@sage/xtrem-stock/pages__stock_adjustment____navigationPanel__optionsMenu__title": "Alle offenen Statuswerte", "@sage/xtrem-stock/pages__stock_adjustment____navigationPanel__optionsMenu__title__2": "Alle Statuswerte", "@sage/xtrem-stock/pages__stock_adjustment____navigationPanel__optionsMenu__title__3": "Details erforderlich", "@sage/xtrem-stock/pages__stock_adjustment____navigationPanel__optionsMenu__title__4": "Details erfasst", "@sage/xtrem-stock/pages__stock_adjustment____navigationPanel__optionsMenu__title__5": "Bestandsbuchung in Bearbeitung", "@sage/xtrem-stock/pages__stock_adjustment____navigationPanel__optionsMenu__title__6": "Bestandsbuchungsfehler", "@sage/xtrem-stock/pages__stock_adjustment____navigationPanel__optionsMenu__title__7": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_adjustment____objectTypePlural": "Bestandskorrekturen", "@sage/xtrem-stock/pages__stock_adjustment____objectTypeSingular": "Bestandskorrektur", "@sage/xtrem-stock/pages__stock_adjustment____title": "Bestandskorrektur", "@sage/xtrem-stock/pages__stock_adjustment__addAdjustLine____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_adjustment__apply_dimensions_success": "Sektoren angewendet.", "@sage/xtrem-stock/pages__stock_adjustment__defaultDimension____title": "<PERSON><PERSON><PERSON><PERSON> setzen", "@sage/xtrem-stock/pages__stock_adjustment__description____title": "Bezeichnung", "@sage/xtrem-stock/pages__stock_adjustment__displayStatus____title": "Status anzeigen", "@sage/xtrem-stock/pages__stock_adjustment__effectiveDate____title": "Datum", "@sage/xtrem-stock/pages__stock_adjustment__goToSysNotificationPage____title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_adjustment__line_delete_action_dialog_content": "<PERSON>e sind dabei, diese Bestandskorrekturzeile zu löschen.", "@sage/xtrem-stock/pages__stock_adjustment__line_delete_action_dialog_title": "Löschen bestätigen", "@sage/xtrem-stock/pages__stock_adjustment__lines____columns__columns__item__name__title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_adjustment__lines____columns__columns__item__name__title__2": "Einheit", "@sage/xtrem-stock/pages__stock_adjustment__lines____columns__columns__item__name__title__3": "Chargenverwaltung", "@sage/xtrem-stock/pages__stock_adjustment__lines____columns__columns__location__name__title": "Name", "@sage/xtrem-stock/pages__stock_adjustment__lines____columns__columns__location__name__title__2": "ID", "@sage/xtrem-stock/pages__stock_adjustment__lines____columns__columns__lot__id__columns__title__id": "ID", "@sage/xtrem-stock/pages__stock_adjustment__lines____columns__columns__lot__id__columns__title__name": "Name", "@sage/xtrem-stock/pages__stock_adjustment__lines____columns__columns__lot__id__title": "ID", "@sage/xtrem-stock/pages__stock_adjustment__lines____columns__columns__lot__id__title__2": "Artikel", "@sage/xtrem-stock/pages__stock_adjustment__lines____columns__columns__lot__id__title__3": "Ablaufdatum", "@sage/xtrem-stock/pages__stock_adjustment__lines____columns__columns__stockStatus__name__title": "Name", "@sage/xtrem-stock/pages__stock_adjustment__lines____columns__columns__stockStatus__name__title__2": "ID", "@sage/xtrem-stock/pages__stock_adjustment__lines____columns__columns__stockStatus__title": "Name", "@sage/xtrem-stock/pages__stock_adjustment__lines____columns__columns__stockStatus__title__2": "ID", "@sage/xtrem-stock/pages__stock_adjustment__lines____columns__lookupDialogTitle__item__name": "Artikel auswählen", "@sage/xtrem-stock/pages__stock_adjustment__lines____columns__lookupDialogTitle__location__name": "Lagerplatz auswählen", "@sage/xtrem-stock/pages__stock_adjustment__lines____columns__lookupDialogTitle__lot__id": "Charge auswählen", "@sage/xtrem-stock/pages__stock_adjustment__lines____columns__lookupDialogTitle__stockStatus": "Wert Qualitätskontrolle auswählen", "@sage/xtrem-stock/pages__stock_adjustment__lines____columns__lookupDialogTitle__stockStatus__name": "Bestandsstatus auswählen", "@sage/xtrem-stock/pages__stock_adjustment__lines____columns__placeholder__item__name": "Auswählen ...", "@sage/xtrem-stock/pages__stock_adjustment__lines____columns__placeholder__lot__id": "Auswählen ...", "@sage/xtrem-stock/pages__stock_adjustment__lines____columns__title__2": "Details", "@sage/xtrem-stock/pages__stock_adjustment__lines____columns__title__adjustmentQuantityInStockUnit": "Korrekturmenge", "@sage/xtrem-stock/pages__stock_adjustment__lines____columns__title__displayStatus": "Status", "@sage/xtrem-stock/pages__stock_adjustment__lines____columns__title__item__description": "Artikelbezeichnung", "@sage/xtrem-stock/pages__stock_adjustment__lines____columns__title__item__id": "Artikel-ID", "@sage/xtrem-stock/pages__stock_adjustment__lines____columns__title__item__name": "Artikel", "@sage/xtrem-stock/pages__stock_adjustment__lines____columns__title__location__name": "Lagerplatz", "@sage/xtrem-stock/pages__stock_adjustment__lines____columns__title__lot__id": "Charge", "@sage/xtrem-stock/pages__stock_adjustment__lines____columns__title__newStockQuantity": "Neue Bestandsmenge", "@sage/xtrem-stock/pages__stock_adjustment__lines____columns__title__quantityInStockUnit": "Bestandsm<PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_adjustment__lines____columns__title__stockStatus": "Qualitätskontrolle", "@sage/xtrem-stock/pages__stock_adjustment__lines____columns__title__stockStatus__name": "Status", "@sage/xtrem-stock/pages__stock_adjustment__lines____columns__title__stockValue": "Bestandswert", "@sage/xtrem-stock/pages__stock_adjustment__lines____columns__title__unitCost": "Stückkosten", "@sage/xtrem-stock/pages__stock_adjustment__lines____dropdownActions__title": "Bestandsdetails", "@sage/xtrem-stock/pages__stock_adjustment__lines____dropdownActions__title__2": "Sektoren", "@sage/xtrem-stock/pages__stock_adjustment__lines____dropdownActions__title__3": "Löschen", "@sage/xtrem-stock/pages__stock_adjustment__lines____inlineActions__title": "Zeilenbereich ö<PERSON>", "@sage/xtrem-stock/pages__stock_adjustment__lines____mobileCard__line2__title": "Bezeichnung", "@sage/xtrem-stock/pages__stock_adjustment__lines____mobileCard__title__title": "Produkt", "@sage/xtrem-stock/pages__stock_adjustment__lines____mobileCard__titleRight__title": "Status", "@sage/xtrem-stock/pages__stock_adjustment__lines____optionsMenu__title": "Alle Statuswerte", "@sage/xtrem-stock/pages__stock_adjustment__lines____optionsMenu__title__2": "Alle offenen Statuswerte", "@sage/xtrem-stock/pages__stock_adjustment__lines____optionsMenu__title__3": "Details erforderlich", "@sage/xtrem-stock/pages__stock_adjustment__lines____optionsMenu__title__4": "Details erfasst", "@sage/xtrem-stock/pages__stock_adjustment__lines____optionsMenu__title__5": "Bestandsbuchung in Bearbeitung", "@sage/xtrem-stock/pages__stock_adjustment__lines____optionsMenu__title__6": "Bestandsbuchungsfehler", "@sage/xtrem-stock/pages__stock_adjustment__lines____optionsMenu__title__7": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_adjustment__lines____sidebar__headerDropdownActions__title": "Bestandsdetails", "@sage/xtrem-stock/pages__stock_adjustment__lines____sidebar__headerDropdownActions__title__2": "Sektoren", "@sage/xtrem-stock/pages__stock_adjustment__lines____sidebar__headerDropdownActions__title__3": "Löschen", "@sage/xtrem-stock/pages__stock_adjustment__lines____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_adjustment__linesSection____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_adjustment__mainSection____title": "Allgemein", "@sage/xtrem-stock/pages__stock_adjustment__number____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_adjustment__post____title": "<PERSON>and buchen", "@sage/xtrem-stock/pages__stock_adjustment__postingDetails____columns__title__financeIntegrationAppRecordId": "Referenz Integration Buchhaltung", "@sage/xtrem-stock/pages__stock_adjustment__postingDetails____columns__title__postingStatus": "Status", "@sage/xtrem-stock/pages__stock_adjustment__postingDetails____columns__title__targetDocumentNumber": "Dokumentnummer", "@sage/xtrem-stock/pages__stock_adjustment__postingDetails____columns__title__targetDocumentType": "Dokumenttyp", "@sage/xtrem-stock/pages__stock_adjustment__postingDetails____title": "Ergebnisse", "@sage/xtrem-stock/pages__stock_adjustment__postingMessageBlock____title": "Fehlerdetails", "@sage/xtrem-stock/pages__stock_adjustment__postingSection____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_adjustment__reasonCode____columns__title__quantityDecreaseAdjustment": "Verringerung Menge", "@sage/xtrem-stock/pages__stock_adjustment__reasonCode____columns__title__quantityIncreaseAdjustment": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_adjustment__reasonCode____lookupDialogTitle": "Grundcode auswählen", "@sage/xtrem-stock/pages__stock_adjustment__reasonCode____title": "Korrekturgrund", "@sage/xtrem-stock/pages__stock_adjustment__repost____title": "<PERSON><PERSON><PERSON> buchen", "@sage/xtrem-stock/pages__stock_adjustment__repost_errors": "Fehler bei der erneuten Buchung:", "@sage/xtrem-stock/pages__stock_adjustment__resync_display_status_continue": "Fortfahren", "@sage/xtrem-stock/pages__stock_adjustment__resync_display_status_message": "<PERSON><PERSON> sind dabei, diesen Bestandskorrekturstatus auf Erledigt zu setzen.", "@sage/xtrem-stock/pages__stock_adjustment__resync_display_status_title": "Status Bestandstransaktion", "@sage/xtrem-stock/pages__stock_adjustment__saveStockAdjustment____title": "Speichern", "@sage/xtrem-stock/pages__stock_adjustment__stock_journal_inquiry": "Abfrage Bestandsjournal", "@sage/xtrem-stock/pages__stock_adjustment__stock_posting_error": "Bestandsbuchungsfehler", "@sage/xtrem-stock/pages__stock_adjustment__stockSite____columns__columns__legalCompany__name__columns__title__symbol": "Symbol", "@sage/xtrem-stock/pages__stock_adjustment__stockSite____lookupDialogTitle": "Standort auswählen", "@sage/xtrem-stock/pages__stock_adjustment__stockSite____placeholder": "Auswählen ...", "@sage/xtrem-stock/pages__stock_adjustment__stockSite____title": "<PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_adjustment__stockTransactionStatus____title": "Bestandsstatus", "@sage/xtrem-stock/pages__stock_adjustment__The_lot_is_mandatory": "Erfassen Sie die Charge.", "@sage/xtrem-stock/pages__stock_adjustment_post__stock_details_required": "Sie müssen Bestandsdetails für alle Zeilen vor der Buchung erfassen.", "@sage/xtrem-stock/pages__stock_adjustment_step_sequence_creation": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_adjustment_step_sequence_post_stock": "<PERSON>and buchen", "@sage/xtrem-stock/pages__stock_allocation_inquiry____navigationPanel__listItem__documentLink__title": "Dokumentnummer", "@sage/xtrem-stock/pages__stock_allocation_inquiry____navigationPanel__listItem__quantityInStockUnit__title": "Reservierte Menge", "@sage/xtrem-stock/pages__stock_allocation_inquiry____navigationPanel__listItem__title__title": "Dokumentnummer", "@sage/xtrem-stock/pages__stock_allocation_inquiry____title": "Abfrage Bestandsreservierung", "@sage/xtrem-stock/pages__stock_avc_justification_inquiry____title": "Begründung gewichteter Durchschnittspreis", "@sage/xtrem-stock/pages__stock_avc_justification_inquiry__company____lookupDialogTitle": "Unternehmen auswählen", "@sage/xtrem-stock/pages__stock_avc_justification_inquiry__company____placeholder": "Auswählen ...", "@sage/xtrem-stock/pages__stock_avc_justification_inquiry__company____title": "Unternehmen", "@sage/xtrem-stock/pages__stock_avc_justification_inquiry__criteriaBlock____title": "Kriterien", "@sage/xtrem-stock/pages__stock_avc_justification_inquiry__itemFrom____columns__title__category__name": "<PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_avc_justification_inquiry__itemFrom____lookupDialogTitle": "Artikel auswählen", "@sage/xtrem-stock/pages__stock_avc_justification_inquiry__itemFrom____placeholder": "Auswählen ...", "@sage/xtrem-stock/pages__stock_avc_justification_inquiry__itemFrom____title": "<PERSON><PERSON><PERSON> von", "@sage/xtrem-stock/pages__stock_avc_justification_inquiry__itemSelection____columns__title__item__description": "Artikelbezeichnung", "@sage/xtrem-stock/pages__stock_avc_justification_inquiry__itemSelection____columns__title__item__id": "Artikel-ID", "@sage/xtrem-stock/pages__stock_avc_justification_inquiry__itemSelection____columns__title__item__name": "Artikel", "@sage/xtrem-stock/pages__stock_avc_justification_inquiry__itemSelection____columns__title__site__name": "<PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_avc_justification_inquiry__itemSelection____columns__title__stockUnit__name": "Maßeinheit", "@sage/xtrem-stock/pages__stock_avc_justification_inquiry__itemSelection____title": "Ergebnisse", "@sage/xtrem-stock/pages__stock_avc_justification_inquiry__itemTo____columns__title__category__name": "<PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_avc_justification_inquiry__itemTo____columns__title__description": "Bezeichnung", "@sage/xtrem-stock/pages__stock_avc_justification_inquiry__itemTo____columns__title__id": "Artikel", "@sage/xtrem-stock/pages__stock_avc_justification_inquiry__itemTo____columns__title__name": "Name", "@sage/xtrem-stock/pages__stock_avc_justification_inquiry__itemTo____lookupDialogTitle": "Artikel auswählen", "@sage/xtrem-stock/pages__stock_avc_justification_inquiry__itemTo____placeholder": "Auswählen ...", "@sage/xtrem-stock/pages__stock_avc_justification_inquiry__itemTo____title": "Artikel bis", "@sage/xtrem-stock/pages__stock_avc_justification_inquiry__itemValues____levels__columns__columns__stockUnit__symbol__title": "Symbol", "@sage/xtrem-stock/pages__stock_avc_justification_inquiry__itemValues____levels__columns__columns__stockUnit__symbol__title__2": "Symbol", "@sage/xtrem-stock/pages__stock_avc_justification_inquiry__itemValues____levels__columns__title___createStamp": "Bewegungsdatum", "@sage/xtrem-stock/pages__stock_avc_justification_inquiry__itemValues____levels__columns__title__amountVariance": "Abweichung Betrag", "@sage/xtrem-stock/pages__stock_avc_justification_inquiry__itemValues____levels__columns__title__company__name": "Unternehmen", "@sage/xtrem-stock/pages__stock_avc_justification_inquiry__itemValues____levels__columns__title__cost": "Stückkosten", "@sage/xtrem-stock/pages__stock_avc_justification_inquiry__itemValues____levels__columns__title__costVariance": "Abweichung Kosten", "@sage/xtrem-stock/pages__stock_avc_justification_inquiry__itemValues____levels__columns__title__creationDate": "Erstellungsdatum", "@sage/xtrem-stock/pages__stock_avc_justification_inquiry__itemValues____levels__columns__title__currency__symbol": "Währung", "@sage/xtrem-stock/pages__stock_avc_justification_inquiry__itemValues____levels__columns__title__dateOfValuation": "Bewertungsdatum", "@sage/xtrem-stock/pages__stock_avc_justification_inquiry__itemValues____levels__columns__title__documentLine___constructor": "<PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_avc_justification_inquiry__itemValues____levels__columns__title__documentLine__documentNumber": "Dokument", "@sage/xtrem-stock/pages__stock_avc_justification_inquiry__itemValues____levels__columns__title__effectiveDate": "Datum", "@sage/xtrem-stock/pages__stock_avc_justification_inquiry__itemValues____levels__columns__title__item__id": "Artikel", "@sage/xtrem-stock/pages__stock_avc_justification_inquiry__itemValues____levels__columns__title__item__name": "Name", "@sage/xtrem-stock/pages__stock_avc_justification_inquiry__itemValues____levels__columns__title__movementAmount": "Bewegungsbetrag", "@sage/xtrem-stock/pages__stock_avc_justification_inquiry__itemValues____levels__columns__title__orderAmount": "Auftragswert", "@sage/xtrem-stock/pages__stock_avc_justification_inquiry__itemValues____levels__columns__title__orderCost": "Auftragskosten", "@sage/xtrem-stock/pages__stock_avc_justification_inquiry__itemValues____levels__columns__title__postingClass__name": "Buchungsklasse", "@sage/xtrem-stock/pages__stock_avc_justification_inquiry__itemValues____levels__columns__title__quantityInStock": "<PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_avc_justification_inquiry__itemValues____levels__columns__title__quantityInStockUnit": "<PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_avc_justification_inquiry__itemValues____levels__columns__title__sequence": "Nummernkreis", "@sage/xtrem-stock/pages__stock_avc_justification_inquiry__itemValues____levels__columns__title__site__name": "<PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_avc_justification_inquiry__itemValues____levels__columns__title__stockDetail__documentLine__documentNumber": "Dokument", "@sage/xtrem-stock/pages__stock_avc_justification_inquiry__itemValues____levels__columns__title__stockQuantity": "Bestandsm<PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_avc_justification_inquiry__itemValues____levels__columns__title__stockUnit__name": "Maßeinheit", "@sage/xtrem-stock/pages__stock_avc_justification_inquiry__itemValues____levels__columns__title__stockUnit__symbol": "Lagereinheit", "@sage/xtrem-stock/pages__stock_avc_justification_inquiry__itemValues____levels__columns__title__stockValue": "Bestandswert", "@sage/xtrem-stock/pages__stock_avc_justification_inquiry__itemValues____levels__columns__title__stockValue__2": "Bestandswert", "@sage/xtrem-stock/pages__stock_avc_justification_inquiry__itemValues____levels__columns__title__type": "Dokumenttyp", "@sage/xtrem-stock/pages__stock_avc_justification_inquiry__itemValues____levels__columns__title__unitCost": "Stückkosten", "@sage/xtrem-stock/pages__stock_avc_justification_inquiry__itemValues____levels__columns__title__valuationMethod": "<PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_avc_justification_inquiry__itemValues____levels__columns__title__valuedCost": "Bewertete Kosten", "@sage/xtrem-stock/pages__stock_avc_justification_inquiry__itemValues____levels__columns__title__variance": "<PERSON>bwei<PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_avc_justification_inquiry__mainSection____title": "Allgemein", "@sage/xtrem-stock/pages__stock_avc_justification_inquiry__runStockValuation____title": "Ausführen", "@sage/xtrem-stock/pages__stock_avc_justification_inquiry__sites____lookupDialogTitle": "Standort auswählen", "@sage/xtrem-stock/pages__stock_avc_justification_inquiry__sites____placeholder": "Auswählen ...", "@sage/xtrem-stock/pages__stock_avc_justification_inquiry__sites____title": "<PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_avc_justification_inquiry__valuationDate____title": "Datum", "@sage/xtrem-stock/pages__stock_change____navigationPanel__listItem__description__title": "Bezeichnung", "@sage/xtrem-stock/pages__stock_change____navigationPanel__listItem__effectiveDate__title": "Datum", "@sage/xtrem-stock/pages__stock_change____navigationPanel__listItem__itemDescription__title": "Artikelbezeichnung", "@sage/xtrem-stock/pages__stock_change____navigationPanel__listItem__itemId__title": "Artikel-ID", "@sage/xtrem-stock/pages__stock_change____navigationPanel__listItem__line2__title": "Standortname", "@sage/xtrem-stock/pages__stock_change____navigationPanel__listItem__line2Right__title": "Datum", "@sage/xtrem-stock/pages__stock_change____navigationPanel__listItem__line3__title": "Artikel", "@sage/xtrem-stock/pages__stock_change____navigationPanel__listItem__siteID__title": "Standort-ID", "@sage/xtrem-stock/pages__stock_change____navigationPanel__listItem__stockSite__title": "<PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_change____navigationPanel__listItem__title__title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_change____objectTypePlural": "Bestandsänderungen", "@sage/xtrem-stock/pages__stock_change____objectTypeSingular": "Bestandsänderung", "@sage/xtrem-stock/pages__stock_change____subtitle": "Lagerumbuchung", "@sage/xtrem-stock/pages__stock_change____title": "Bestandsänderung", "@sage/xtrem-stock/pages__stock_change__addLine____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_change__availableQuantity____title": "Verfügbar<PERSON>", "@sage/xtrem-stock/pages__stock_change__description____title": "Bezeichnung", "@sage/xtrem-stock/pages__stock_change__effectiveDate____title": "Datum", "@sage/xtrem-stock/pages__stock_change__initialStockBlock____title": "Anfangsbestand", "@sage/xtrem-stock/pages__stock_change__item____columns__title__category__name": "<PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_change__item____lookupDialogTitle": "Artikel auswählen", "@sage/xtrem-stock/pages__stock_change__item____title": "Artikel", "@sage/xtrem-stock/pages__stock_change__lines____columns__columns__endingSerialNumber__id__title": "Lieferantenseriennummer", "@sage/xtrem-stock/pages__stock_change__lines____columns__columns__endingSerialNumber__id__title__2": "Lieferant", "@sage/xtrem-stock/pages__stock_change__lines____columns__columns__location__name__title": "Name", "@sage/xtrem-stock/pages__stock_change__lines____columns__columns__location__name__title__2": "ID", "@sage/xtrem-stock/pages__stock_change__lines____columns__columns__startingSerialNumber__id__title": "Lieferantenseriennummer", "@sage/xtrem-stock/pages__stock_change__lines____columns__columns__startingSerialNumber__id__title__2": "Lieferant", "@sage/xtrem-stock/pages__stock_change__lines____columns__columns__stockStatus__name__title": "Name", "@sage/xtrem-stock/pages__stock_change__lines____columns__columns__stockStatus__name__title__2": "ID", "@sage/xtrem-stock/pages__stock_change__lines____columns__lookupDialogTitle__startingSerialNumber__id": "<PERSON><PERSON><PERSON><PERSON> von au<PERSON>w<PERSON>hl<PERSON>", "@sage/xtrem-stock/pages__stock_change__lines____columns__lookupDialogTitle__stockStatus": "Wert Qualitätskontrolle auswählen", "@sage/xtrem-stock/pages__stock_change__lines____columns__title__endingSerialNumber__id": "Seriennummer bis", "@sage/xtrem-stock/pages__stock_change__lines____columns__title__location__name": "Lagerplatz", "@sage/xtrem-stock/pages__stock_change__lines____columns__title__quantityInStockUnit": "<PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_change__lines____columns__title__startingSerialNumber__id": "<PERSON><PERSON><PERSON><PERSON> von", "@sage/xtrem-stock/pages__stock_change__lines____columns__title__stockStatus": "Qualitätskontrolle", "@sage/xtrem-stock/pages__stock_change__lines____columns__title__stockStatus__name": "Status", "@sage/xtrem-stock/pages__stock_change__lines____columns__title__stockTransactionStatus": "Bestandsstatus", "@sage/xtrem-stock/pages__stock_change__lines____dropdownActions__title": "Löschen", "@sage/xtrem-stock/pages__stock_change__lines____title": "Änderungen", "@sage/xtrem-stock/pages__stock_change__location____columns__title__id": "ID", "@sage/xtrem-stock/pages__stock_change__location____columns__title__name": "Name", "@sage/xtrem-stock/pages__stock_change__location____title": "Lagerplatz", "@sage/xtrem-stock/pages__stock_change__lot____columns__title__expirationDate": "Ablaufdatum", "@sage/xtrem-stock/pages__stock_change__lot____title": "Charge", "@sage/xtrem-stock/pages__stock_change__mainSection____title": "Allgemein", "@sage/xtrem-stock/pages__stock_change__number____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_change__owner____title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_change__post____title": "Buchen", "@sage/xtrem-stock/pages__stock_change__postingError____title": "Buchungsfehler", "@sage/xtrem-stock/pages__stock_change__saveStockChange____title": "Speichern", "@sage/xtrem-stock/pages__stock_change__status____title": "Status", "@sage/xtrem-stock/pages__stock_change__stockRecord____columns__columns__location__name__title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_change__stockRecord____columns__columns__site__name__title": "Lagerplatzverwaltung", "@sage/xtrem-stock/pages__stock_change__stockRecord____columns__title__item__name": "Artikel", "@sage/xtrem-stock/pages__stock_change__stockRecord____columns__title__location__name": "Lagerplatz", "@sage/xtrem-stock/pages__stock_change__stockRecord____columns__title__lot__id": "Charge", "@sage/xtrem-stock/pages__stock_change__stockRecord____columns__title__owner": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_change__stockRecord____columns__title__quantityInStockUnit": "<PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_change__stockRecord____columns__title__site__name": "<PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_change__stockRecord____columns__title__status__name": "Status", "@sage/xtrem-stock/pages__stock_change__stockRecord____columns__title__totalAllocated": "Reserviert", "@sage/xtrem-stock/pages__stock_change__stockRecord____title": "Bestand", "@sage/xtrem-stock/pages__stock_change__stockSite____columns__title__isLocationManaged": "Lagerplatzverwaltung", "@sage/xtrem-stock/pages__stock_change__stockSite____lookupDialogTitle": "Standort auswählen", "@sage/xtrem-stock/pages__stock_change__stockSite____title": "<PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_change__stockStatus____columns__title__id": "ID", "@sage/xtrem-stock/pages__stock_change__stockStatus____columns__title__name": "Name", "@sage/xtrem-stock/pages__stock_change__stockStatus____lookupDialogTitle": "Wert Qualitätskontrolle auswählen", "@sage/xtrem-stock/pages__stock_change__stockStatus____title": "Qualitätskontrolle", "@sage/xtrem-stock/pages__stock_change__stockTransactionStatus____title": "Bestandsstatus", "@sage/xtrem-stock/pages__stock_change__totalQuantity____title": "<PERSON>ge gesamt", "@sage/xtrem-stock/pages__stock_change_available_error": "In diesem Bereich verfügbare Seriennummern: {{available}}", "@sage/xtrem-stock/pages__stock_change_duplicate_error": "Die Seriennummer ist bereits in einem anderen Bereich enthalten.", "@sage/xtrem-stock/pages__stock_change_duplicate_range_error": "Eine der Seriennummern ist bereits in einem anderen Bereich enthalten.", "@sage/xtrem-stock/pages__stock_change_missing_from_serial_number": "<PERSON><PERSON> müssen eine Seriennummer von er<PERSON>ssen.", "@sage/xtrem-stock/pages__stock_change_zero_quantity_error": "Erfassen Sie eine Menge größer als 0.", "@sage/xtrem-stock/pages__stock_count____navigationPanel__listItem__counter__title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_count____navigationPanel__listItem__line2__title": "Bezeichnung", "@sage/xtrem-stock/pages__stock_count____navigationPanel__listItem__line2Right__title": "Datum", "@sage/xtrem-stock/pages__stock_count____navigationPanel__listItem__stockSite__title": "<PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_count____navigationPanel__listItem__stockTransactionStatus__title": "Bestandsstatus", "@sage/xtrem-stock/pages__stock_count____navigationPanel__listItem__title__title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_count____navigationPanel__listItem__titleRight__title": "Status", "@sage/xtrem-stock/pages__stock_count____navigationPanel__optionsMenu__title": "", "@sage/xtrem-stock/pages__stock_count____objectTypePlural": "Inventuren", "@sage/xtrem-stock/pages__stock_count____objectTypeSingular": "Inventur", "@sage/xtrem-stock/pages__stock_count____title": "Inventur", "@sage/xtrem-stock/pages__stock_count__categories____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_count__confirm_zero_quantity_result": "Zeilen aktualisiert: {{recordsUpdated}}.", "@sage/xtrem-stock/pages__stock_count__confirmZeroQuantity____title": "Nullmengen bestätigen", "@sage/xtrem-stock/pages__stock_count__counted_quantity_can_not_be_greater_than_quantity_in_stock": "Die gezählte Menge kann nicht größer als die Bestandsmenge für einen Artikel mit Seriennummer sein.", "@sage/xtrem-stock/pages__stock_count__counter____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_count__creation": "Die Inventur wurde erfolgreich erstellt.", "@sage/xtrem-stock/pages__stock_count__criteriaBlock____title": "Auswahlkriterien", "@sage/xtrem-stock/pages__stock_count__defaultDimension____title": "Standardsektoren setzen", "@sage/xtrem-stock/pages__stock_count__description____title": "Bezeichnung", "@sage/xtrem-stock/pages__stock_count__duplication_among_added_lines": "Doppelte Zeilen ändern oder entfernen.", "@sage/xtrem-stock/pages__stock_count__effectiveDate____title": "Datum", "@sage/xtrem-stock/pages__stock_count__fromItem____title": "<PERSON><PERSON><PERSON> von", "@sage/xtrem-stock/pages__stock_count__goToSysNotificationPage____title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_count__hasStockRecords____title": "Nur in Bestand", "@sage/xtrem-stock/pages__stock_count__lastCountDate____title": "Artikel nicht gezählt ab", "@sage/xtrem-stock/pages__stock_count__lines____columns__columns__item__name__columns__title__symbol": "Symbol", "@sage/xtrem-stock/pages__stock_count__lines____columns__columns__location__name__title": "Name", "@sage/xtrem-stock/pages__stock_count__lines____columns__columns__location__name__title__2": "ID", "@sage/xtrem-stock/pages__stock_count__lines____columns__columns__location__name__title__3": "<PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_count__lines____columns__columns__lotSelection__id__columns__title__id": "ID", "@sage/xtrem-stock/pages__stock_count__lines____columns__columns__lotSelection__id__columns__title__name": "Name", "@sage/xtrem-stock/pages__stock_count__lines____columns__columns__stockDetail__stockDetailLot__lotId__id__title": "ID", "@sage/xtrem-stock/pages__stock_count__lines____columns__columns__stockDetail__stockDetailLot__lotId__id__title__2": "Untercharge", "@sage/xtrem-stock/pages__stock_count__lines____columns__columns__stockDetail__stockDetailLot__lotId__id__title__3": "Ablaufdatum", "@sage/xtrem-stock/pages__stock_count__lines____columns__columns__stockDetail__stockDetailLot__lotId__id__title__4": "Lieferantenchargennummer", "@sage/xtrem-stock/pages__stock_count__lines____columns__columns__stockDetail__stockDetailLot__lotId__id__title__5": "Artikel", "@sage/xtrem-stock/pages__stock_count__lines____columns__columns__stockStatus__name__title": "Name", "@sage/xtrem-stock/pages__stock_count__lines____columns__columns__stockStatus__name__title__2": "ID", "@sage/xtrem-stock/pages__stock_count__lines____columns__columns__stockStatus__name__title__3": "Bezeichnung", "@sage/xtrem-stock/pages__stock_count__lines____columns__lookupDialogTitle__stockDetail__stockDetailLot__lotId__id": "Charge auswählen", "@sage/xtrem-stock/pages__stock_count__lines____columns__postfix__quantityVariancePercentage": "%", "@sage/xtrem-stock/pages__stock_count__lines____columns__title___id": "", "@sage/xtrem-stock/pages__stock_count__lines____columns__title__countedQuantityInStockUnit": "Gezählte Menge", "@sage/xtrem-stock/pages__stock_count__lines____columns__title__countedSerialNumberPercentage": "Seriennummer", "@sage/xtrem-stock/pages__stock_count__lines____columns__title__expirationDate": "Ablaufdatum", "@sage/xtrem-stock/pages__stock_count__lines____columns__title__item__description": "Artikelbezeichnung", "@sage/xtrem-stock/pages__stock_count__lines____columns__title__item__id": "Artikel-ID", "@sage/xtrem-stock/pages__stock_count__lines____columns__title__item__name": "Artikel", "@sage/xtrem-stock/pages__stock_count__lines____columns__title__location__locationZone__name": "<PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_count__lines____columns__title__location__name": "Lagerplatz", "@sage/xtrem-stock/pages__stock_count__lines____columns__title__newLineOrderCost": "Stückkosten", "@sage/xtrem-stock/pages__stock_count__lines____columns__title__owner": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_count__lines____columns__title__quantityInStockUnit": "Bestandsm<PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_count__lines____columns__title__quantityVariance": "Abweichung Menge", "@sage/xtrem-stock/pages__stock_count__lines____columns__title__quantityVariancePercentage": "Abweichung Menge %", "@sage/xtrem-stock/pages__stock_count__lines____columns__title__status": "Status Zählung", "@sage/xtrem-stock/pages__stock_count__lines____columns__title__stockDetail__stockDetailLot__lotId__id": "Charge", "@sage/xtrem-stock/pages__stock_count__lines____columns__title__stockStatus__name": "Qualitätskontrolle", "@sage/xtrem-stock/pages__stock_count__lines____columns__title__stockTransactionStatus": "Bestandsstatus", "@sage/xtrem-stock/pages__stock_count__lines____columns__title__sublot": "Unterchargennummer", "@sage/xtrem-stock/pages__stock_count__lines____columns__title__supplierLot": "Lieferantenchargennummer", "@sage/xtrem-stock/pages__stock_count__lines____dropdownActions__title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_count__lines____dropdownActions__title__2": "Ausschließen", "@sage/xtrem-stock/pages__stock_count__lines____dropdownActions__title__3": "Nullmenge bestätigen", "@sage/xtrem-stock/pages__stock_count__lines____dropdownActions__title__4": "Sektoren", "@sage/xtrem-stock/pages__stock_count__lines____dropdownActions__title__5": "Seriennummern", "@sage/xtrem-stock/pages__stock_count__lines____dropdownActions__title__6": "Löschen", "@sage/xtrem-stock/pages__stock_count__lines____inlineActions__title": "Zeilenbereich ö<PERSON>", "@sage/xtrem-stock/pages__stock_count__lines____title": "Dokumentzeilen", "@sage/xtrem-stock/pages__stock_count__linesSection____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_count__locations____title": "Lagerplatz", "@sage/xtrem-stock/pages__stock_count__mainSection____title": "Allgemein", "@sage/xtrem-stock/pages__stock_count__number____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_count__order_cost_should_be_entered": "Überprüfen Sie Ihre Stückkosten. Ein Wert 0 kann Auswirkungen auf den Bestandswert haben.", "@sage/xtrem-stock/pages__stock_count__post____title": "<PERSON>and buchen", "@sage/xtrem-stock/pages__stock_count__postingDetails____columns__title__financeIntegrationAppRecordId": "Referenz Integration Buchhaltung", "@sage/xtrem-stock/pages__stock_count__postingDetails____columns__title__postingStatus": "Status", "@sage/xtrem-stock/pages__stock_count__postingDetails____columns__title__targetDocumentNumber": "Dokumentnummer", "@sage/xtrem-stock/pages__stock_count__postingDetails____columns__title__targetDocumentType": "Dokumenttyp", "@sage/xtrem-stock/pages__stock_count__postingDetails____title": "Ergebnisse", "@sage/xtrem-stock/pages__stock_count__postingMessageBlock____title": "Fehlerdetails", "@sage/xtrem-stock/pages__stock_count__postingSection____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_count__print____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_count__repost____title": "<PERSON><PERSON><PERSON> buchen", "@sage/xtrem-stock/pages__stock_count__repost_errors": "Fehler bei der erneuten Buchung:", "@sage/xtrem-stock/pages__stock_count__saveStockCount____title": "Speichern", "@sage/xtrem-stock/pages__stock_count__selected_serial_number_quantity_too_high": "<PERSON> Anzahl der ausgewählten Seriennummern ({{selectedQuantity}}) kann nicht größer als die gezählte Menge ({{countedQuantity}}) sein.", "@sage/xtrem-stock/pages__stock_count__showQuantityInStock____title": "Menge in Bestand anzeigen", "@sage/xtrem-stock/pages__stock_count__sidebar_tab_title_information": "Informationen", "@sage/xtrem-stock/pages__stock_count__start____title": "Start", "@sage/xtrem-stock/pages__stock_count__status____title": "Status", "@sage/xtrem-stock/pages__stock_count__stockSite____title": "<PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_count__stockTransactionStatus____title": "Bestandsstatus", "@sage/xtrem-stock/pages__stock_count__toItem____title": "Artikel bis", "@sage/xtrem-stock/pages__stock_count__zones____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_count_creation____navigationPanel__listItem__line2__title": "<PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_count_creation____navigationPanel__optionsMenu__title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_count_creation____title": "Erstellung Inventur", "@sage/xtrem-stock/pages__stock_count_creation__cancelDetailedButton____title": "Erweiterte Auswahl ausblenden", "@sage/xtrem-stock/pages__stock_count_creation__categories____columns__title__id": "ID", "@sage/xtrem-stock/pages__stock_count_creation__categories____columns__title__name": "Name", "@sage/xtrem-stock/pages__stock_count_creation__categories____lookupDialogTitle": "Kategorie auswählen", "@sage/xtrem-stock/pages__stock_count_creation__categories____placeholder": "Auswählen...", "@sage/xtrem-stock/pages__stock_count_creation__categories____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_count_creation__createStockCount____title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_count_creation__criteriaBlock____title": "Kriterien", "@sage/xtrem-stock/pages__stock_count_creation__description____title": "Bezeichnung", "@sage/xtrem-stock/pages__stock_count_creation__effectiveDate____title": "Datum", "@sage/xtrem-stock/pages__stock_count_creation__fromItem____columns__title__description": "Bezeichnung", "@sage/xtrem-stock/pages__stock_count_creation__fromItem____columns__title__id": "ID", "@sage/xtrem-stock/pages__stock_count_creation__fromItem____columns__title__name": "Name", "@sage/xtrem-stock/pages__stock_count_creation__fromItem____lookupDialogTitle": "Artikel auswählen", "@sage/xtrem-stock/pages__stock_count_creation__fromItem____placeholder": "Auswählen ...", "@sage/xtrem-stock/pages__stock_count_creation__fromItem____title": "<PERSON><PERSON><PERSON> von", "@sage/xtrem-stock/pages__stock_count_creation__hasStockRecords____title": "Nur in Bestand", "@sage/xtrem-stock/pages__stock_count_creation__invalid_item_range": "Erfassen Sie einen Wert für den 'Artikel bis', der größer ist als der 'Artikel von'.", "@sage/xtrem-stock/pages__stock_count_creation__itemSites____levels__columns__title__hasStockRecords": "Bestandsdatensatz", "@sage/xtrem-stock/pages__stock_count_creation__itemSites____levels__columns__title__item__description": "Artikelbezeichnung", "@sage/xtrem-stock/pages__stock_count_creation__itemSites____levels__columns__title__item__id": "Artikel-ID", "@sage/xtrem-stock/pages__stock_count_creation__itemSites____levels__columns__title__item__name": "Artikel", "@sage/xtrem-stock/pages__stock_count_creation__itemSites____levels__columns__title__location__locationZone__name": "<PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_count_creation__itemSites____levels__columns__title__location__name": "Lagerplatz", "@sage/xtrem-stock/pages__stock_count_creation__itemSites____levels__columns__title__lot__id": "Charge", "@sage/xtrem-stock/pages__stock_count_creation__itemSites____levels__columns__title__lot__sublot": "Untercharge", "@sage/xtrem-stock/pages__stock_count_creation__itemSites____levels__columns__title__owner": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_count_creation__itemSites____levels__columns__title__quantityInStockUnit": "<PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_count_creation__itemSites____levels__columns__title__status__name": "Status", "@sage/xtrem-stock/pages__stock_count_creation__lastCountDate____title": "Artikel nicht gezählt ab", "@sage/xtrem-stock/pages__stock_count_creation__locations____columns__title__id": "ID", "@sage/xtrem-stock/pages__stock_count_creation__locations____columns__title__name": "Name", "@sage/xtrem-stock/pages__stock_count_creation__locations____lookupDialogTitle": "Lagerplatz auswählen", "@sage/xtrem-stock/pages__stock_count_creation__locations____placeholder": "Auswählen ...", "@sage/xtrem-stock/pages__stock_count_creation__locations____title": "Lagerplatz", "@sage/xtrem-stock/pages__stock_count_creation__mainSection____title": "Allgemein", "@sage/xtrem-stock/pages__stock_count_creation__selectAllCheckbox____title": "Alle auswählen", "@sage/xtrem-stock/pages__stock_count_creation__stockCountBlock____title": "Inventur", "@sage/xtrem-stock/pages__stock_count_creation__stockSite____lookupDialogTitle": "Standort auswählen", "@sage/xtrem-stock/pages__stock_count_creation__stockSite____placeholder": "Auswählen ...", "@sage/xtrem-stock/pages__stock_count_creation__stockSite____title": "<PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_count_creation__toItem____columns__title__description": "Bezeichnung", "@sage/xtrem-stock/pages__stock_count_creation__toItem____columns__title__id": "ID", "@sage/xtrem-stock/pages__stock_count_creation__toItem____columns__title__name": "Name", "@sage/xtrem-stock/pages__stock_count_creation__toItem____lookupDialogTitle": "Artikel auswählen", "@sage/xtrem-stock/pages__stock_count_creation__toItem____placeholder": "Auswählen ...", "@sage/xtrem-stock/pages__stock_count_creation__toItem____title": "Artikel bis", "@sage/xtrem-stock/pages__stock_count_creation__zones____columns__title__id": "ID", "@sage/xtrem-stock/pages__stock_count_creation__zones____columns__title__name": "Name", "@sage/xtrem-stock/pages__stock_count_creation__zones____columns__title__zoneType": "<PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_count_creation__zones____lookupDialogTitle": "Lagerplatzbereich auswählen", "@sage/xtrem-stock/pages__stock_count_creation__zones____placeholder": "Auswählen ...", "@sage/xtrem-stock/pages__stock_count_creation__zones____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_count_line__not_add_a_line_matching_with_a_stock_record": "Sie müssen die Details in dieser Inventurzeile ändern, da sie die bestehenden Bestandsinformationen dupliziert. Artikel: {{item}}, Standort: {{site}}, Lagerplatz: {{location}}, Charge: {{lot}}, Status: {{status}}, Lagereinheit: {{stockUnit}}, <PERSON><PERSON>tz<PERSON>: {{owner}}.", "@sage/xtrem-stock/pages__stock_count_serial_numbers_panel____title": "Seriennummern", "@sage/xtrem-stock/pages__stock_count_serial_numbers_panel__addSerialNumberRange____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_count_serial_numbers_panel__cancel____title": "Abbrechen", "@sage/xtrem-stock/pages__stock_count_serial_numbers_panel__duplicate_error": "Die Seriennummer ist bereits in einem anderen Bereich enthalten.", "@sage/xtrem-stock/pages__stock_count_serial_numbers_panel__duplicate_range_error": "Eine der Seriennummern ist bereits in einem anderen Bereich enthalten.", "@sage/xtrem-stock/pages__stock_count_serial_numbers_panel__mainBlock____title": "Allgemein", "@sage/xtrem-stock/pages__stock_count_serial_numbers_panel__mainSection____title": "Allgemein", "@sage/xtrem-stock/pages__stock_count_serial_numbers_panel__ok____title": "OK", "@sage/xtrem-stock/pages__stock_count_serial_numbers_panel__quantityToIssue____title": "<PERSON><PERSON> entne<PERSON><PERSON>e Seriennummern", "@sage/xtrem-stock/pages__stock_count_serial_numbers_panel__remainingQuantity____title": "Verbleibende Menge", "@sage/xtrem-stock/pages__stock_count_serial_numbers_panel__requiredQuantity____title": "Erforderliche Menge", "@sage/xtrem-stock/pages__stock_count_serial_numbers_panel__selected_quantity_too_high": "<PERSON> Anzahl der ausgewählten Seriennummern ({{selectedQuantity}}) kann nicht größer als die gezählte Menge ({{countedQuantity}}) sein.", "@sage/xtrem-stock/pages__stock_count_serial_numbers_panel__selectedQuantity____title": "Ausgewählte Menge", "@sage/xtrem-stock/pages__stock_count_serial_numbers_panel__serialNumberRanges____columns__columns__endingSerialNumber__serialNumber.id__title": "ID", "@sage/xtrem-stock/pages__stock_count_serial_numbers_panel__serialNumberRanges____columns__columns__startingSerialNumber__serialNumber__id__title": "ID", "@sage/xtrem-stock/pages__stock_count_serial_numbers_panel__serialNumberRanges____columns__lookupDialogTitle__startingSerialNumber__serialNumber__id": "<PERSON><PERSON><PERSON><PERSON> von au<PERSON>w<PERSON>hl<PERSON>", "@sage/xtrem-stock/pages__stock_count_serial_numbers_panel__serialNumberRanges____columns__title__endingSerialNumber__serialNumber.id": "Seriennummer bis", "@sage/xtrem-stock/pages__stock_count_serial_numbers_panel__serialNumberRanges____columns__title__numericEnd": "Seriennummer bis", "@sage/xtrem-stock/pages__stock_count_serial_numbers_panel__serialNumberRanges____columns__title__numericStart": "<PERSON><PERSON><PERSON><PERSON> von", "@sage/xtrem-stock/pages__stock_count_serial_numbers_panel__serialNumberRanges____columns__title__originalStartId": "Ursprüngliche Seriennummer von", "@sage/xtrem-stock/pages__stock_count_serial_numbers_panel__serialNumberRanges____columns__title__quantity": "<PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_count_serial_numbers_panel__serialNumberRanges____columns__title__startingSerialNumber__serialNumber__id": "<PERSON><PERSON><PERSON><PERSON> von", "@sage/xtrem-stock/pages__stock_count_serial_numbers_panel__serialNumberRanges____dropdownActions__title": "Löschen", "@sage/xtrem-stock/pages__stock_count_serial_numbers_panel__serialNumberRanges____title": "Seriennummern", "@sage/xtrem-stock/pages__stock_detailed_inquiry____navigationPanel__dropdownActions__title": "Reservierungen", "@sage/xtrem-stock/pages__stock_detailed_inquiry____navigationPanel__dropdownActions__title__2": "<PERSON><PERSON>mer", "@sage/xtrem-stock/pages__stock_detailed_inquiry____navigationPanel__listItem__activeQuantityInStockUnit__title": "Aktive Menge", "@sage/xtrem-stock/pages__stock_detailed_inquiry____navigationPanel__listItem__allocatedQuantity__title": "Reservierte Menge", "@sage/xtrem-stock/pages__stock_detailed_inquiry____navigationPanel__listItem__availableQuantity__title": "Verfügbar<PERSON>", "@sage/xtrem-stock/pages__stock_detailed_inquiry____navigationPanel__listItem__creationDate__title": "Erstellungsdatum", "@sage/xtrem-stock/pages__stock_detailed_inquiry____navigationPanel__listItem__expirationDate__title": "Ablaufdatum", "@sage/xtrem-stock/pages__stock_detailed_inquiry____navigationPanel__listItem__inTransitQuantityInStockUnit__title": "Menge in Transit", "@sage/xtrem-stock/pages__stock_detailed_inquiry____navigationPanel__listItem__item__title": "Artikel", "@sage/xtrem-stock/pages__stock_detailed_inquiry____navigationPanel__listItem__itemDescription__title": "Artikelbezeichnung", "@sage/xtrem-stock/pages__stock_detailed_inquiry____navigationPanel__listItem__itemId__title": "Artikel-ID", "@sage/xtrem-stock/pages__stock_detailed_inquiry____navigationPanel__listItem__line2__title": "Charge", "@sage/xtrem-stock/pages__stock_detailed_inquiry____navigationPanel__listItem__location__title": "Lagerplatz", "@sage/xtrem-stock/pages__stock_detailed_inquiry____navigationPanel__listItem__locationId__title": "Lagerplatz-ID", "@sage/xtrem-stock/pages__stock_detailed_inquiry____navigationPanel__listItem__locationName__title": "Lagerplatz", "@sage/xtrem-stock/pages__stock_detailed_inquiry____navigationPanel__listItem__locationZone__title": "Lagerplatzbereich", "@sage/xtrem-stock/pages__stock_detailed_inquiry____navigationPanel__listItem__lot__title": "Charge", "@sage/xtrem-stock/pages__stock_detailed_inquiry____navigationPanel__listItem__onHandQuantityInStockUnit__title": "Vorrätige Menge", "@sage/xtrem-stock/pages__stock_detailed_inquiry____navigationPanel__listItem__owner__title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_detailed_inquiry____navigationPanel__listItem__siteId__title": "Standort-ID", "@sage/xtrem-stock/pages__stock_detailed_inquiry____navigationPanel__listItem__siteName__title": "<PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_detailed_inquiry____navigationPanel__listItem__status__title": "Qualitätskontrolle", "@sage/xtrem-stock/pages__stock_detailed_inquiry____navigationPanel__listItem__stockUnit__title": "Maßeinheit", "@sage/xtrem-stock/pages__stock_detailed_inquiry____navigationPanel__listItem__sublot__title": "Untercharge", "@sage/xtrem-stock/pages__stock_detailed_inquiry____navigationPanel__listItem__supplierLot__title": "Lieferantenchargennummer", "@sage/xtrem-stock/pages__stock_detailed_inquiry____navigationPanel__listItem__title__columns__title": "Name", "@sage/xtrem-stock/pages__stock_detailed_inquiry____navigationPanel__listItem__title__columns__title__2": "ID", "@sage/xtrem-stock/pages__stock_detailed_inquiry____navigationPanel__listItem__title__columns__title__3": "Bezeichnung", "@sage/xtrem-stock/pages__stock_detailed_inquiry____navigationPanel__listItem__title__title": "Artikel", "@sage/xtrem-stock/pages__stock_detailed_inquiry____navigationPanel__listItem__titleRight__title": "<PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_detailed_inquiry____navigationPanel__optionsMenu__title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_detailed_inquiry____navigationPanel__optionsMenu__title__2": "In Transit einschließen", "@sage/xtrem-stock/pages__stock_detailed_inquiry____title": "Bestandsabfrage detailliert", "@sage/xtrem-stock/pages__stock_detailed_inquiry___id____title": "ID", "@sage/xtrem-stock/pages__stock_detailed_inquiry__activeQuantityInStockUnit____title": "Aktive Menge", "@sage/xtrem-stock/pages__stock_detailed_inquiry__location____columns__title__id": "ID", "@sage/xtrem-stock/pages__stock_detailed_inquiry__location____columns__title__locationType__name": "<PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_detailed_inquiry__location____lookupDialogTitle": "Lagerplatz auswählen", "@sage/xtrem-stock/pages__stock_detailed_inquiry__location____title": "Lagerplatz", "@sage/xtrem-stock/pages__stock_detailed_inquiry__lot____columns__title__item__description": "Artikel", "@sage/xtrem-stock/pages__stock_detailed_inquiry__mainBlock____title": "<PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_detailed_inquiry__owner____title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_detailed_inquiry__serialNumberLines____columns__title__supplierSerialNumber": "Lieferantenseriennummer", "@sage/xtrem-stock/pages__stock_detailed_inquiry__stockUnit____columns__title__id": "ID", "@sage/xtrem-stock/pages__stock_detailed_inquiry__stockUnit____columns__title__name": "Name", "@sage/xtrem-stock/pages__stock_detailed_inquiry__stockUnit____lookupDialogTitle": "Maßeinheit auswählen", "@sage/xtrem-stock/pages__stock_detailed_inquiry__stockUnit____placeholder": "Auswählen ...", "@sage/xtrem-stock/pages__stock_detailed_inquiry__stockUnit____title": "Maßeinheit", "@sage/xtrem-stock/pages__stock_fifo_valuation_inquiry____title": "FIFO-Kostenstufe", "@sage/xtrem-stock/pages__stock_fifo_valuation_inquiry__criteriaBlock____title": "Kriterien", "@sage/xtrem-stock/pages__stock_fifo_valuation_inquiry__fifoValuationTiers____columns__title__amount": "Betrag", "@sage/xtrem-stock/pages__stock_fifo_valuation_inquiry__fifoValuationTiers____columns__title__createDate": "Erstellungsdatum", "@sage/xtrem-stock/pages__stock_fifo_valuation_inquiry__fifoValuationTiers____columns__title__effectiveDate": "Eingangsdatum", "@sage/xtrem-stock/pages__stock_fifo_valuation_inquiry__fifoValuationTiers____columns__title__nonAbsorbedAmount": "Nicht absorbierter Betrag", "@sage/xtrem-stock/pages__stock_fifo_valuation_inquiry__fifoValuationTiers____columns__title__receiptDocumentLine__documentNumber": "Dokument Eingang", "@sage/xtrem-stock/pages__stock_fifo_valuation_inquiry__fifoValuationTiers____columns__title__receiptQuantity": "Ursprüngliche Menge", "@sage/xtrem-stock/pages__stock_fifo_valuation_inquiry__fifoValuationTiers____columns__title__remainingQuantity": "<PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_fifo_valuation_inquiry__fifoValuationTiers____columns__title__sequence": "Nummernkreis", "@sage/xtrem-stock/pages__stock_fifo_valuation_inquiry__fifoValuationTiers____columns__title__unitCost": "Stückkosten", "@sage/xtrem-stock/pages__stock_fifo_valuation_inquiry__fifoValuationTiers____title": "Ergebnisse", "@sage/xtrem-stock/pages__stock_fifo_valuation_inquiry__fromDate____title": "<PERSON><PERSON> von", "@sage/xtrem-stock/pages__stock_fifo_valuation_inquiry__item____columns__title__category__name": "<PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_fifo_valuation_inquiry__item____lookupDialogTitle": "Artikel auswählen", "@sage/xtrem-stock/pages__stock_fifo_valuation_inquiry__item____placeholder": "Artikel auswählen", "@sage/xtrem-stock/pages__stock_fifo_valuation_inquiry__item____title": "Artikel", "@sage/xtrem-stock/pages__stock_fifo_valuation_inquiry__mainSection____title": "Allgemein", "@sage/xtrem-stock/pages__stock_fifo_valuation_inquiry__site____columns__title__id": "ID", "@sage/xtrem-stock/pages__stock_fifo_valuation_inquiry__site____columns__title__name": "Name", "@sage/xtrem-stock/pages__stock_fifo_valuation_inquiry__site____lookupDialogTitle": "Standort auswählen", "@sage/xtrem-stock/pages__stock_fifo_valuation_inquiry__site____placeholder": "Standort auswählen", "@sage/xtrem-stock/pages__stock_fifo_valuation_inquiry__site____title": "<PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_fifo_valuation_inquiry__toDate____title": "Datum bis", "@sage/xtrem-stock/pages__Stock_has__allocated_quantity_the_counted_cannot_be_less_than_allocated_quantity": "Der Bestand hat Reservierungen. Die gezählte Menge kann nicht kleiner als die reservierte Menge sein.", "@sage/xtrem-stock/pages__stock_inquiry____title": "Bestandsabfrage", "@sage/xtrem-stock/pages__stock_inquiry__criteriaBlock____title": "Kriterien", "@sage/xtrem-stock/pages__stock_inquiry__item____columns__title__category__name": "<PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_inquiry__item____lookupDialogTitle": "Artikel auswählen", "@sage/xtrem-stock/pages__stock_inquiry__item____title": "Artikel", "@sage/xtrem-stock/pages__stock_inquiry__itemFrom____columns__title__category__name": "<PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_inquiry__itemFrom____columns__title__description": "Bezeichnung", "@sage/xtrem-stock/pages__stock_inquiry__itemFrom____columns__title__id": "Artikel", "@sage/xtrem-stock/pages__stock_inquiry__itemFrom____columns__title__name": "Name", "@sage/xtrem-stock/pages__stock_inquiry__itemFrom____lookupDialogTitle": "Artikel auswählen", "@sage/xtrem-stock/pages__stock_inquiry__itemFrom____placeholder": "<PERSON><PERSON><PERSON> von", "@sage/xtrem-stock/pages__stock_inquiry__itemFrom____title": "<PERSON><PERSON><PERSON> von", "@sage/xtrem-stock/pages__stock_inquiry__itemTo____columns__title__category__name": "<PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_inquiry__itemTo____columns__title__description": "Bezeichnung", "@sage/xtrem-stock/pages__stock_inquiry__itemTo____columns__title__id": "Artikel", "@sage/xtrem-stock/pages__stock_inquiry__itemTo____columns__title__name": "Name", "@sage/xtrem-stock/pages__stock_inquiry__itemTo____lookupDialogTitle": "Artikel auswählen", "@sage/xtrem-stock/pages__stock_inquiry__itemTo____placeholder": "Artikel bis", "@sage/xtrem-stock/pages__stock_inquiry__itemTo____title": "Artikel bis", "@sage/xtrem-stock/pages__stock_inquiry__mainSection____title": "Allgemein", "@sage/xtrem-stock/pages__stock_inquiry__resultsSection____title": "Ergebnisse", "@sage/xtrem-stock/pages__stock_inquiry__search": "<PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_inquiry__site____columns__title__id": "ID", "@sage/xtrem-stock/pages__stock_inquiry__site____columns__title__name": "Name", "@sage/xtrem-stock/pages__stock_inquiry__site____lookupDialogTitle": "Standort auswählen", "@sage/xtrem-stock/pages__stock_inquiry__site____placeholder": "Auswählen ...", "@sage/xtrem-stock/pages__stock_inquiry__site____title": "<PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_inquiry__stock____columns__title__activeQuantityInStockUnit": "<PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_inquiry__stock____columns__title__item__id": "Artikel", "@sage/xtrem-stock/pages__stock_inquiry__stock____columns__title__item__name": "Name", "@sage/xtrem-stock/pages__stock_inquiry__stock____columns__title__site__name": "<PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_inquiry__stock____columns__title__stockUnit__name": "Maßeinheit", "@sage/xtrem-stock/pages__stock_inquiry__stock____mobileCard__line2__title": "Name", "@sage/xtrem-stock/pages__stock_inquiry__stock____mobileCard__line2Right__title": "Maßeinheit", "@sage/xtrem-stock/pages__stock_inquiry__stock____mobileCard__title__title": "Name", "@sage/xtrem-stock/pages__stock_inquiry__stock____mobileCard__titleRight__title": "<PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_inquiry__stock____title": "Ergebnisse", "@sage/xtrem-stock/pages__stock_inquiry__stockStatus____columns__title__id": "ID", "@sage/xtrem-stock/pages__stock_inquiry__stockStatus____columns__title__name": "Name", "@sage/xtrem-stock/pages__stock_inquiry__stockStatus____lookupDialogTitle": "Status auswählen", "@sage/xtrem-stock/pages__stock_inquiry__stockStatus____placeholder": "Auswählen ...", "@sage/xtrem-stock/pages__stock_inquiry__stockStatus____title": "Bestandsstatus", "@sage/xtrem-stock/pages__stock_inquiry_bound____navigationPanel__listItem__accepted__title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_inquiry_bound____navigationPanel__listItem__allocated__title": "Reservierte Menge", "@sage/xtrem-stock/pages__stock_inquiry_bound____navigationPanel__listItem__image__title": "Bild", "@sage/xtrem-stock/pages__stock_inquiry_bound____navigationPanel__listItem__inStock__title": "Vorrätige Menge", "@sage/xtrem-stock/pages__stock_inquiry_bound____navigationPanel__listItem__itemCategory__title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_inquiry_bound____navigationPanel__listItem__itemDescription__title": "Artikelbezeichnung", "@sage/xtrem-stock/pages__stock_inquiry_bound____navigationPanel__listItem__itemIdReference__title": "Artikel-ID", "@sage/xtrem-stock/pages__stock_inquiry_bound____navigationPanel__listItem__itemName__title": "Artikel", "@sage/xtrem-stock/pages__stock_inquiry_bound____navigationPanel__listItem__qualityQuntity__title": "Menge in Qualitätskontrolle", "@sage/xtrem-stock/pages__stock_inquiry_bound____navigationPanel__listItem__rejected__title": "<PERSON><PERSON>gel<PERSON><PERSON> Menge", "@sage/xtrem-stock/pages__stock_inquiry_bound____navigationPanel__listItem__site__title": "<PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_inquiry_bound____navigationPanel__listItem__stockValue__title": "Bestandswert", "@sage/xtrem-stock/pages__stock_inquiry_bound____navigationPanel__listItem__title__title": "Name", "@sage/xtrem-stock/pages__stock_inquiry_bound____title": "Bestandsabfrage", "@sage/xtrem-stock/pages__stock_issue____navigationPanel__dropdownActions__title": "<PERSON><PERSON><PERSON><PERSON> setzen", "@sage/xtrem-stock/pages__stock_issue____navigationPanel__dropdownActions__title__2": "Löschen", "@sage/xtrem-stock/pages__stock_issue____navigationPanel__listItem__description__title": "Bezeichnung", "@sage/xtrem-stock/pages__stock_issue____navigationPanel__listItem__effectiveDate__title": "Datum", "@sage/xtrem-stock/pages__stock_issue____navigationPanel__listItem__line2__title": "<PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_issue____navigationPanel__listItem__line2Right__title": "Datum", "@sage/xtrem-stock/pages__stock_issue____navigationPanel__listItem__reasonCode__title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_issue____navigationPanel__listItem__status__title": "Status", "@sage/xtrem-stock/pages__stock_issue____navigationPanel__listItem__stockSite__title": "<PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_issue____navigationPanel__listItem__stockTransactionStatus__title": "Bestandsstatus", "@sage/xtrem-stock/pages__stock_issue____navigationPanel__listItem__title__title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_issue____navigationPanel__listItem__titleRight__title": "Status", "@sage/xtrem-stock/pages__stock_issue____navigationPanel__optionsMenu__title": "Alle offenen Statuswerte", "@sage/xtrem-stock/pages__stock_issue____navigationPanel__optionsMenu__title__2": "Alle Statuswerte", "@sage/xtrem-stock/pages__stock_issue____navigationPanel__optionsMenu__title__3": "Details erforderlich", "@sage/xtrem-stock/pages__stock_issue____navigationPanel__optionsMenu__title__4": "Details erfasst", "@sage/xtrem-stock/pages__stock_issue____navigationPanel__optionsMenu__title__5": "Bestandsbuchung in Bearbeitung", "@sage/xtrem-stock/pages__stock_issue____navigationPanel__optionsMenu__title__6": "Bestandsbuchungsfehler", "@sage/xtrem-stock/pages__stock_issue____navigationPanel__optionsMenu__title__7": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_issue____objectTypePlural": "Bestandsabgänge", "@sage/xtrem-stock/pages__stock_issue____objectTypeSingular": "Bestandsabgang", "@sage/xtrem-stock/pages__stock_issue____title": "Bestandsabgang", "@sage/xtrem-stock/pages__stock_issue__addIssueLine____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_issue__apply_dimensions_success": "Sektoren angewendet.", "@sage/xtrem-stock/pages__stock_issue__defaultDimension____title": "<PERSON><PERSON><PERSON><PERSON> setzen", "@sage/xtrem-stock/pages__stock_issue__defaultDimensionAction____title": "Standardsektoren setzen", "@sage/xtrem-stock/pages__stock_issue__description____title": "Bezeichnung", "@sage/xtrem-stock/pages__stock_issue__displayStatus____title": "Status anzeigen", "@sage/xtrem-stock/pages__stock_issue__effectiveDate____title": "Datum", "@sage/xtrem-stock/pages__stock_issue__goToSysNotificationPage____title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_issue__issue_step_sequence_creation": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_issue__issue_step_sequence_detail_stock": "Details Bestand", "@sage/xtrem-stock/pages__stock_issue__issue_step_sequence_post_stock": "<PERSON>and buchen", "@sage/xtrem-stock/pages__stock_issue__line_delete_action_dialog_content": "<PERSON>e sind dabei, diese Bestandsabgangszeile zu löschen.", "@sage/xtrem-stock/pages__stock_issue__line_delete_action_dialog_title": "Löschen bestätigen", "@sage/xtrem-stock/pages__stock_issue__lines____columns__columns__item__name__columns__title__decimalDigits": "Dezimalstellen", "@sage/xtrem-stock/pages__stock_issue__lines____columns__columns__item__name__columns__title__id": "ID", "@sage/xtrem-stock/pages__stock_issue__lines____columns__columns__item__name__columns__title__name": "Name", "@sage/xtrem-stock/pages__stock_issue__lines____columns__columns__item__name__columns__title__symbol": "Symbol", "@sage/xtrem-stock/pages__stock_issue__lines____columns__columns__item__name__title": "Name", "@sage/xtrem-stock/pages__stock_issue__lines____columns__columns__item__name__title__2": "ID", "@sage/xtrem-stock/pages__stock_issue__lines____columns__columns__item__name__title__3": "<PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_issue__lines____columns__columns__stockStatus__name__title": "Name", "@sage/xtrem-stock/pages__stock_issue__lines____columns__columns__stockStatus__name__title__2": "ID", "@sage/xtrem-stock/pages__stock_issue__lines____columns__lookupDialogTitle__item__name": "Artikel auswählen", "@sage/xtrem-stock/pages__stock_issue__lines____columns__lookupDialogTitle__stockStatus__name": "Bestandsstatus auswählen", "@sage/xtrem-stock/pages__stock_issue__lines____columns__placeholder__item__name": "Auswählen ...", "@sage/xtrem-stock/pages__stock_issue__lines____columns__title__displayStatus": "Status", "@sage/xtrem-stock/pages__stock_issue__lines____columns__title__item__description": "Artikelbezeichnung", "@sage/xtrem-stock/pages__stock_issue__lines____columns__title__item__id": "Artikel-ID", "@sage/xtrem-stock/pages__stock_issue__lines____columns__title__item__name": "Artikel", "@sage/xtrem-stock/pages__stock_issue__lines____columns__title__orderCost": "Ist-<PERSON>sten", "@sage/xtrem-stock/pages__stock_issue__lines____columns__title__quantityInStockUnit": "<PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_issue__lines____columns__title__stockDetailStatus": "Status Bestandsdetail", "@sage/xtrem-stock/pages__stock_issue__lines____columns__title__stockStatus__name": "Status", "@sage/xtrem-stock/pages__stock_issue__lines____columns__title__valuedCost": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_issue__lines____dropdownActions__title": "Bestandsdetails", "@sage/xtrem-stock/pages__stock_issue__lines____dropdownActions__title__2": "Sektoren", "@sage/xtrem-stock/pages__stock_issue__lines____dropdownActions__title__3": "Löschen", "@sage/xtrem-stock/pages__stock_issue__lines____inlineActions__title": "Zeilenbereich ö<PERSON>", "@sage/xtrem-stock/pages__stock_issue__lines____mobileCard__line2__title": "Bezeichnung", "@sage/xtrem-stock/pages__stock_issue__lines____mobileCard__line2Right__title": "Pre<PERSON>", "@sage/xtrem-stock/pages__stock_issue__lines____mobileCard__line3Right__title": "<PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_issue__lines____mobileCard__title__title": "Produkt", "@sage/xtrem-stock/pages__stock_issue__lines____mobileCard__titleRight__title": "Status", "@sage/xtrem-stock/pages__stock_issue__lines____optionsMenu__title": "Alle Statuswerte", "@sage/xtrem-stock/pages__stock_issue__lines____optionsMenu__title__2": "Alle offenen Statuswerte", "@sage/xtrem-stock/pages__stock_issue__lines____optionsMenu__title__3": "Details erforderlich", "@sage/xtrem-stock/pages__stock_issue__lines____optionsMenu__title__4": "Details erfasst", "@sage/xtrem-stock/pages__stock_issue__lines____optionsMenu__title__5": "Bestandsbuchung in Bearbeitung", "@sage/xtrem-stock/pages__stock_issue__lines____optionsMenu__title__6": "Bestandsbuchungsfehler", "@sage/xtrem-stock/pages__stock_issue__lines____optionsMenu__title__7": "Abgegangen", "@sage/xtrem-stock/pages__stock_issue__lines____sidebar__headerDropdownActions__title": "Bestandsdetails", "@sage/xtrem-stock/pages__stock_issue__lines____sidebar__headerDropdownActions__title__2": "Sektoren", "@sage/xtrem-stock/pages__stock_issue__lines____sidebar__headerDropdownActions__title__3": "Löschen", "@sage/xtrem-stock/pages__stock_issue__lines____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_issue__linesSection____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_issue__mainSection____title": "Allgemein", "@sage/xtrem-stock/pages__stock_issue__notification__The_quantity_is_mandatory": "Erfassen Sie die Menge.", "@sage/xtrem-stock/pages__stock_issue__number____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_issue__post____title": "<PERSON>and buchen", "@sage/xtrem-stock/pages__stock_issue__postingDetails____columns__title__financeIntegrationAppRecordId": "Referenz Integration Buchhaltung", "@sage/xtrem-stock/pages__stock_issue__postingDetails____columns__title__postingStatus": "Status", "@sage/xtrem-stock/pages__stock_issue__postingDetails____columns__title__targetDocumentNumber": "Dokumentnummer", "@sage/xtrem-stock/pages__stock_issue__postingDetails____columns__title__targetDocumentType": "Dokumenttyp", "@sage/xtrem-stock/pages__stock_issue__postingDetails____title": "Ergebnisse", "@sage/xtrem-stock/pages__stock_issue__postingMessageBlock____title": "Fehlerdetails", "@sage/xtrem-stock/pages__stock_issue__postingSection____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_issue__reasonCode____lookupDialogTitle": "<PERSON><PERSON>d auswählen", "@sage/xtrem-stock/pages__stock_issue__reasonCode____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_issue__repost____title": "<PERSON><PERSON><PERSON> buchen", "@sage/xtrem-stock/pages__stock_issue__repost_errors": "Fehler bei der erneuten Buchung:", "@sage/xtrem-stock/pages__stock_issue__saveStockIssue____title": "Speichern", "@sage/xtrem-stock/pages__stock_issue__stock_journal_inquiry": "Abfrage Bestandsjournal", "@sage/xtrem-stock/pages__stock_issue__stock_posting_error": "Bestandsbuchungsfehler", "@sage/xtrem-stock/pages__stock_issue__stockSite____columns__title__id": "ID", "@sage/xtrem-stock/pages__stock_issue__stockSite____columns__title__name": "Name", "@sage/xtrem-stock/pages__stock_issue__stockSite____lookupDialogTitle": "Standort auswählen", "@sage/xtrem-stock/pages__stock_issue__stockSite____placeholder": "Auswählen ...", "@sage/xtrem-stock/pages__stock_issue__stockSite____title": "<PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_issue__stockTransactionStatus____title": "Bestandsstatus", "@sage/xtrem-stock/pages__stock_issue_post__stock_details_required": "Sie müssen Bestandsdetails für alle Zeilen vor der Buchung erfassen.", "@sage/xtrem-stock/pages__stock_journal_inquiry____navigationPanel__dropdownActions__title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_journal_inquiry____navigationPanel__inlineActions__title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_journal_inquiry____navigationPanel__listItem__amountVariance__title": "Abweichung Betrag", "@sage/xtrem-stock/pages__stock_journal_inquiry____navigationPanel__listItem__costVariance__title": "Abweichung Kosten", "@sage/xtrem-stock/pages__stock_journal_inquiry____navigationPanel__listItem__currency__title": "Währung", "@sage/xtrem-stock/pages__stock_journal_inquiry____navigationPanel__listItem__currencyId__title": "Währungs-ID", "@sage/xtrem-stock/pages__stock_journal_inquiry____navigationPanel__listItem__documentLineType__title": "Dokumentzeilentyp", "@sage/xtrem-stock/pages__stock_journal_inquiry____navigationPanel__listItem__documentLink__title": "Dokument", "@sage/xtrem-stock/pages__stock_journal_inquiry____navigationPanel__listItem__effectiveDate__title": "Datum", "@sage/xtrem-stock/pages__stock_journal_inquiry____navigationPanel__listItem__expirationDate__title": "Ablaufdatum", "@sage/xtrem-stock/pages__stock_journal_inquiry____navigationPanel__listItem__itemDescription__title": "Artikelbezeichnung", "@sage/xtrem-stock/pages__stock_journal_inquiry____navigationPanel__listItem__itemId__title": "Artikel-ID", "@sage/xtrem-stock/pages__stock_journal_inquiry____navigationPanel__listItem__itemName__title": "Artikel", "@sage/xtrem-stock/pages__stock_journal_inquiry____navigationPanel__listItem__locationId__title": "Lagerplatz-ID", "@sage/xtrem-stock/pages__stock_journal_inquiry____navigationPanel__listItem__locationName__title": "Lagerplatz", "@sage/xtrem-stock/pages__stock_journal_inquiry____navigationPanel__listItem__locationZone__title": "Lagerplatzbereich", "@sage/xtrem-stock/pages__stock_journal_inquiry____navigationPanel__listItem__lot__title": "Charge", "@sage/xtrem-stock/pages__stock_journal_inquiry____navigationPanel__listItem__movementAmount__title": "Bewegungsbetrag", "@sage/xtrem-stock/pages__stock_journal_inquiry____navigationPanel__listItem__movementType__title": "Bewegungstyp", "@sage/xtrem-stock/pages__stock_journal_inquiry____navigationPanel__listItem__nonAbsorbedAmount__title": "Nicht absorbierter Betrag", "@sage/xtrem-stock/pages__stock_journal_inquiry____navigationPanel__listItem__orderAmount__title": "Auftragswert", "@sage/xtrem-stock/pages__stock_journal_inquiry____navigationPanel__listItem__orderCost__title": "Auftragskosten", "@sage/xtrem-stock/pages__stock_journal_inquiry____navigationPanel__listItem__quantityInStockUnit__title": "<PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_journal_inquiry____navigationPanel__listItem__reasonCode__title": "Grundcode", "@sage/xtrem-stock/pages__stock_journal_inquiry____navigationPanel__listItem__siteId__title": "Standort-ID", "@sage/xtrem-stock/pages__stock_journal_inquiry____navigationPanel__listItem__stockStatus__title": "Qualitätskontrolle", "@sage/xtrem-stock/pages__stock_journal_inquiry____navigationPanel__listItem__stockUnit__title": "Maßeinheit", "@sage/xtrem-stock/pages__stock_journal_inquiry____navigationPanel__listItem__sublot__title": "Untercharge", "@sage/xtrem-stock/pages__stock_journal_inquiry____navigationPanel__listItem__supplierLot__title": "Lieferantenchargennummer", "@sage/xtrem-stock/pages__stock_journal_inquiry____navigationPanel__listItem__valuedCost__title": "Bewertete Kosten", "@sage/xtrem-stock/pages__stock_journal_inquiry____title": "Abfrage Bestandsjournal", "@sage/xtrem-stock/pages__stock_journal_inquiry__no_journal_entry_found": "Keine Buchung für das ausgewählte Dokument gefunden.", "@sage/xtrem-stock/pages__stock_journal_query____title": "Abfrage Bestandsjournal", "@sage/xtrem-stock/pages__stock_journal_query__criteriaBlock____title": "Kriterien", "@sage/xtrem-stock/pages__stock_journal_query__itemFrom____columns__title__id": "ID", "@sage/xtrem-stock/pages__stock_journal_query__itemFrom____columns__title__name": "Name", "@sage/xtrem-stock/pages__stock_journal_query__itemFrom____placeholder": "<PERSON><PERSON><PERSON> von", "@sage/xtrem-stock/pages__stock_journal_query__itemFrom____title": "<PERSON>", "@sage/xtrem-stock/pages__stock_journal_query__itemTo____columns__title__id": "ID", "@sage/xtrem-stock/pages__stock_journal_query__itemTo____columns__title__name": "Name", "@sage/xtrem-stock/pages__stock_journal_query__itemTo____placeholder": "Artikel bis", "@sage/xtrem-stock/pages__stock_journal_query__itemTo____title": "Bis", "@sage/xtrem-stock/pages__stock_journal_query__mainBlock____title": "Ergebnisse", "@sage/xtrem-stock/pages__stock_journal_query__site____columns__title__id": "ID", "@sage/xtrem-stock/pages__stock_journal_query__site____columns__title__name": "Name", "@sage/xtrem-stock/pages__stock_journal_query__site____placeholder": "Auswählen...", "@sage/xtrem-stock/pages__stock_journal_query__site____title": "<PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_journal_query__stockJournal____columns__title__activeQuantityInStockUnit": "Wirkstoffmenge", "@sage/xtrem-stock/pages__stock_journal_query__stockJournal____columns__title__amountVariance": "Abweichung Betrag", "@sage/xtrem-stock/pages__stock_journal_query__stockJournal____columns__title__costVariance": "Abweichung Kosten", "@sage/xtrem-stock/pages__stock_journal_query__stockJournal____columns__title__effectiveDate": "Datum", "@sage/xtrem-stock/pages__stock_journal_query__stockJournal____columns__title__item__name": "Artikel", "@sage/xtrem-stock/pages__stock_journal_query__stockJournal____columns__title__location__id": "Lagerplatz", "@sage/xtrem-stock/pages__stock_journal_query__stockJournal____columns__title__lot__id": "Charge", "@sage/xtrem-stock/pages__stock_journal_query__stockJournal____columns__title__lot__sublot": "Untercharge", "@sage/xtrem-stock/pages__stock_journal_query__stockJournal____columns__title__lot__supplierLot": "Lieferantenchargennummer", "@sage/xtrem-stock/pages__stock_journal_query__stockJournal____columns__title__movementAmount": "Bewegungsbetrag", "@sage/xtrem-stock/pages__stock_journal_query__stockJournal____columns__title__orderAmount": "Auftragswert", "@sage/xtrem-stock/pages__stock_journal_query__stockJournal____columns__title__orderCost": "Auftragskosten", "@sage/xtrem-stock/pages__stock_journal_query__stockJournal____columns__title__quantityInStockUnit": "<PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_journal_query__stockJournal____columns__title__site__name": "<PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_journal_query__stockJournal____columns__title__stockUnit__name": "Maßeinheit", "@sage/xtrem-stock/pages__stock_journal_query__stockJournal____columns__title__valuedCost": "Bewertete Kosten", "@sage/xtrem-stock/pages__stock_mrp_calculation__calculate": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_mrp_calculation_company_sites_error": "Erfassen Sie mindestens ein Unternehmen oder einen Standort.", "@sage/xtrem-stock/pages__stock_receipt____navigationPanel__dropdownActions__title": "<PERSON><PERSON><PERSON><PERSON> setzen", "@sage/xtrem-stock/pages__stock_receipt____navigationPanel__dropdownActions__title__2": "Löschen", "@sage/xtrem-stock/pages__stock_receipt____navigationPanel__listItem__description__title": "Bezeichnung", "@sage/xtrem-stock/pages__stock_receipt____navigationPanel__listItem__line2__title": "<PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_receipt____navigationPanel__listItem__line2Right__title": "Datum", "@sage/xtrem-stock/pages__stock_receipt____navigationPanel__listItem__reasonCode__title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_receipt____navigationPanel__listItem__status__title": "Status", "@sage/xtrem-stock/pages__stock_receipt____navigationPanel__listItem__title__title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_receipt____navigationPanel__listItem__titleRight__title": "Status", "@sage/xtrem-stock/pages__stock_receipt____navigationPanel__optionsMenu__title": "Alle offenen Statuswerte", "@sage/xtrem-stock/pages__stock_receipt____navigationPanel__optionsMenu__title__2": "Alle Statuswerte", "@sage/xtrem-stock/pages__stock_receipt____navigationPanel__optionsMenu__title__3": "Details erforderlich", "@sage/xtrem-stock/pages__stock_receipt____navigationPanel__optionsMenu__title__4": "Details erfasst", "@sage/xtrem-stock/pages__stock_receipt____navigationPanel__optionsMenu__title__5": "Bestandsbuchung in Bearbeitung", "@sage/xtrem-stock/pages__stock_receipt____navigationPanel__optionsMenu__title__6": "Bestandsbuchungsfehler", "@sage/xtrem-stock/pages__stock_receipt____navigationPanel__optionsMenu__title__7": "Eingegangen", "@sage/xtrem-stock/pages__stock_receipt____objectTypePlural": "Bestandseingänge", "@sage/xtrem-stock/pages__stock_receipt____objectTypeSingular": "Bestandseingang", "@sage/xtrem-stock/pages__stock_receipt____title": "Bestandseingang", "@sage/xtrem-stock/pages__stock_receipt__actual_cost_cannot_be_negative": "Sie müssen einen Wert größer oder gleich 0 erfassen.", "@sage/xtrem-stock/pages__stock_receipt__apply_dimensions_success": "Sektoren angewendet.", "@sage/xtrem-stock/pages__stock_receipt__defaultDimension____title": "<PERSON><PERSON><PERSON><PERSON> setzen", "@sage/xtrem-stock/pages__stock_receipt__defaultDimensionAction____title": "Standardsektoren setzen", "@sage/xtrem-stock/pages__stock_receipt__description____title": "Bezeichnung", "@sage/xtrem-stock/pages__stock_receipt__displayStatus____title": "Status anzeigen", "@sage/xtrem-stock/pages__stock_receipt__effectiveDate____title": "Datum", "@sage/xtrem-stock/pages__stock_receipt__goToSysNotificationPage____title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_receipt__line_delete_action_dialog_content": "<PERSON><PERSON> sind dabei, diese Bestandseingangszeile zu löschen.", "@sage/xtrem-stock/pages__stock_receipt__line_delete_action_dialog_title": "Löschen bestätigen", "@sage/xtrem-stock/pages__stock_receipt__lines____columns__columns__existingLot__id__columns__title__id": "ID", "@sage/xtrem-stock/pages__stock_receipt__lines____columns__columns__existingLot__id__columns__title__name": "Name", "@sage/xtrem-stock/pages__stock_receipt__lines____columns__columns__existingLot__id__title": "ID", "@sage/xtrem-stock/pages__stock_receipt__lines____columns__columns__existingLot__id__title__2": "Ablaufdatum", "@sage/xtrem-stock/pages__stock_receipt__lines____columns__columns__existingLot__id__title__3": "Artikel", "@sage/xtrem-stock/pages__stock_receipt__lines____columns__columns__item__name__columns__title__decimalDigits": "Dezimalstellen", "@sage/xtrem-stock/pages__stock_receipt__lines____columns__columns__item__name__columns__title__id__2": "ID", "@sage/xtrem-stock/pages__stock_receipt__lines____columns__columns__item__name__columns__title__name__2": "Name", "@sage/xtrem-stock/pages__stock_receipt__lines____columns__columns__item__name__columns__title__symbol": "Symbol", "@sage/xtrem-stock/pages__stock_receipt__lines____columns__columns__item__name__title": "Name", "@sage/xtrem-stock/pages__stock_receipt__lines____columns__columns__item__name__title__2": "ID", "@sage/xtrem-stock/pages__stock_receipt__lines____columns__columns__item__name__title__3": "<PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_receipt__lines____columns__columns__location__name__title": "Name", "@sage/xtrem-stock/pages__stock_receipt__lines____columns__columns__location__name__title__2": "ID", "@sage/xtrem-stock/pages__stock_receipt__lines____columns__columns__stockStatus__name__title": "Name", "@sage/xtrem-stock/pages__stock_receipt__lines____columns__columns__stockStatus__name__title__2": "ID", "@sage/xtrem-stock/pages__stock_receipt__lines____columns__lookupDialogTitle__existingLot__id": "Charge auswählen", "@sage/xtrem-stock/pages__stock_receipt__lines____columns__lookupDialogTitle__item__name": "Artikel auswählen", "@sage/xtrem-stock/pages__stock_receipt__lines____columns__lookupDialogTitle__location__name": "Lagerplatz auswählen", "@sage/xtrem-stock/pages__stock_receipt__lines____columns__lookupDialogTitle__stockStatus": "Wert Qualitätskontrolle auswählen", "@sage/xtrem-stock/pages__stock_receipt__lines____columns__lookupDialogTitle__stockStatus__name": "Bestandsstatus auswählen", "@sage/xtrem-stock/pages__stock_receipt__lines____columns__placeholder__existingLot__id": "Auswählen ...", "@sage/xtrem-stock/pages__stock_receipt__lines____columns__placeholder__item__name": "Artikel auswählen", "@sage/xtrem-stock/pages__stock_receipt__lines____columns__title__displayStatus": "Status", "@sage/xtrem-stock/pages__stock_receipt__lines____columns__title__existingLot__id": "Charge", "@sage/xtrem-stock/pages__stock_receipt__lines____columns__title__expirationDate": "Ablaufdatum", "@sage/xtrem-stock/pages__stock_receipt__lines____columns__title__item__description": "Artikelbezeichnung", "@sage/xtrem-stock/pages__stock_receipt__lines____columns__title__item__id": "Artikel-ID", "@sage/xtrem-stock/pages__stock_receipt__lines____columns__title__item__name": "Artikel", "@sage/xtrem-stock/pages__stock_receipt__lines____columns__title__itemSite": "Artikelstandort", "@sage/xtrem-stock/pages__stock_receipt__lines____columns__title__location__name": "Lagerplatz", "@sage/xtrem-stock/pages__stock_receipt__lines____columns__title__lotNumber": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_receipt__lines____columns__title__orderCost": "Ist-<PERSON>sten", "@sage/xtrem-stock/pages__stock_receipt__lines____columns__title__quantityInStockUnit": "<PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_receipt__lines____columns__title__stockStatus": "Qualitätskontrolle", "@sage/xtrem-stock/pages__stock_receipt__lines____columns__title__stockStatus__name": "Status", "@sage/xtrem-stock/pages__stock_receipt__lines____columns__title__valuedCost": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_receipt__lines____dropdownActions__title": "Bestandsdetails", "@sage/xtrem-stock/pages__stock_receipt__lines____dropdownActions__title__2": "Sektoren", "@sage/xtrem-stock/pages__stock_receipt__lines____dropdownActions__title__3": "Löschen", "@sage/xtrem-stock/pages__stock_receipt__lines____inlineActions__title": "Zeilenbereich ö<PERSON>", "@sage/xtrem-stock/pages__stock_receipt__lines____mobileCard__line2__title": "Bezeichnung", "@sage/xtrem-stock/pages__stock_receipt__lines____mobileCard__line2Right__title": "Pre<PERSON>", "@sage/xtrem-stock/pages__stock_receipt__lines____mobileCard__line3Right__title": "<PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_receipt__lines____mobileCard__title__title": "Produkt", "@sage/xtrem-stock/pages__stock_receipt__lines____mobileCard__titleRight__title": "Status", "@sage/xtrem-stock/pages__stock_receipt__lines____optionsMenu__title": "Alle Statuswerte", "@sage/xtrem-stock/pages__stock_receipt__lines____optionsMenu__title__2": "Alle offenen Statuswerte", "@sage/xtrem-stock/pages__stock_receipt__lines____optionsMenu__title__3": "Details erforderlich", "@sage/xtrem-stock/pages__stock_receipt__lines____optionsMenu__title__4": "Details erfasst", "@sage/xtrem-stock/pages__stock_receipt__lines____optionsMenu__title__5": "Bestandsbuchung in Bearbeitung", "@sage/xtrem-stock/pages__stock_receipt__lines____optionsMenu__title__6": "Bestandsbuchungsfehler", "@sage/xtrem-stock/pages__stock_receipt__lines____optionsMenu__title__7": "Eingegangen", "@sage/xtrem-stock/pages__stock_receipt__lines____sidebar__headerDropdownActions__title": "Bestandsdetails", "@sage/xtrem-stock/pages__stock_receipt__lines____sidebar__headerDropdownActions__title__2": "Sektoren", "@sage/xtrem-stock/pages__stock_receipt__lines____sidebar__headerDropdownActions__title__3": "Löschen", "@sage/xtrem-stock/pages__stock_receipt__lines____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_receipt__linesSection____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_receipt__notification__expiration_date_cannot_be_past": "Das Ablaufdatum kann nicht vor dem heutigen Datum liegen.", "@sage/xtrem-stock/pages__stock_receipt__notification__expiration_date_is_mandatory": "Erfassen Sie das Ablaufdatum.", "@sage/xtrem-stock/pages__stock_receipt__notification__validation_error": "Freigabefehler:\n{{#each errors}}\t- {{this}}{{#unless @last}}\n{{/unless}}{{/each}}", "@sage/xtrem-stock/pages__stock_receipt__number____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_receipt__post____title": "<PERSON>and buchen", "@sage/xtrem-stock/pages__stock_receipt__postingDetails____columns__title__financeIntegrationAppRecordId": "Referenz Integration Buchhaltung", "@sage/xtrem-stock/pages__stock_receipt__postingDetails____columns__title__postingStatus": "Status", "@sage/xtrem-stock/pages__stock_receipt__postingDetails____columns__title__targetDocumentNumber": "Dokumentnummer", "@sage/xtrem-stock/pages__stock_receipt__postingDetails____columns__title__targetDocumentType": "Dokumenttyp", "@sage/xtrem-stock/pages__stock_receipt__postingDetails____title": "Ergebnisse", "@sage/xtrem-stock/pages__stock_receipt__postingMessageBlock____title": "Fehlerdetails", "@sage/xtrem-stock/pages__stock_receipt__postingSection____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_receipt__quantity_must_be_positive": "Sie müssen einen Wert größer als 0 erfassen.", "@sage/xtrem-stock/pages__stock_receipt__reasonCode____lookupDialogTitle": "<PERSON><PERSON>d auswählen", "@sage/xtrem-stock/pages__stock_receipt__reasonCode____placeholder": "<PERSON><PERSON>d auswählen", "@sage/xtrem-stock/pages__stock_receipt__reasonCode____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_receipt__receipt_step_sequence_creation": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_receipt__receipt_step_sequence_detail_stock": "Details Bestand", "@sage/xtrem-stock/pages__stock_receipt__receipt_step_sequence_post_stock": "<PERSON>and buchen", "@sage/xtrem-stock/pages__stock_receipt__repost____title": "<PERSON><PERSON><PERSON> buchen", "@sage/xtrem-stock/pages__stock_receipt__repost_errors": "Fehler bei der erneuten Buchung:", "@sage/xtrem-stock/pages__stock_receipt__saveStockReceipt____title": "Speichern", "@sage/xtrem-stock/pages__stock_receipt__serial number_exists_on_another_line": "Die Seriennummer {{serialNumberId}} für Artikel {{itemId}} ist bereits in einer anderen Dokumentzeile vorhanden.", "@sage/xtrem-stock/pages__stock_receipt__stock_journal_inquiry": "Abfrage Bestandsjournal", "@sage/xtrem-stock/pages__stock_receipt__stock_posting_error": "Bestandsbuchungsfehler", "@sage/xtrem-stock/pages__stock_receipt__stockReceiptStatus____title": "Status Bestandseingang", "@sage/xtrem-stock/pages__stock_receipt__stockSite____columns__columns__legalCompany__name__columns__title__symbol": "Symbol", "@sage/xtrem-stock/pages__stock_receipt__stockSite____columns__title__id": "ID", "@sage/xtrem-stock/pages__stock_receipt__stockSite____columns__title__isLocationManaged": "Lagerplatzverwaltung", "@sage/xtrem-stock/pages__stock_receipt__stockSite____columns__title__name": "Name", "@sage/xtrem-stock/pages__stock_receipt__stockSite____lookupDialogTitle": "Standort auswählen", "@sage/xtrem-stock/pages__stock_receipt__stockSite____placeholder": "Auswählen ...", "@sage/xtrem-stock/pages__stock_receipt__stockSite____title": "<PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_receipt__stockTransactionStatus____title": "Bestandsstatus", "@sage/xtrem-stock/pages__stock_receipt_detail_panel__notification__The_lot_is_mandatory": "Die Charge ist erforderlich.", "@sage/xtrem-stock/pages__stock_receipt_detail_panel__notification__The_status_is_mandatory": "Der Status ist erforderlich.", "@sage/xtrem-stock/pages__stock_receipt_detail_panel__notification__The_sublot_is_mandatory": "Die Untercharge ist erforderlich.", "@sage/xtrem-stock/pages__stock_receipt_details_panel__notification__expiration_date_is_mandatory": "Das Ablaufdatum ist erforderlich.", "@sage/xtrem-stock/pages__stock_receipt_post__stock_details_required": "Sie müssen Bestandsdetails für alle Zeilen vor der Buchung erfassen.", "@sage/xtrem-stock/pages__stock_reorder_calculation____title": "Wiederbeschaffung Lager", "@sage/xtrem-stock/pages__stock_reorder_calculation__company____columns__title__id": "ID", "@sage/xtrem-stock/pages__stock_reorder_calculation__company____columns__title__name": "Name", "@sage/xtrem-stock/pages__stock_reorder_calculation__company____lookupDialogTitle": "Unternehmen auswählen", "@sage/xtrem-stock/pages__stock_reorder_calculation__company____placeholder": "Auswählen ...", "@sage/xtrem-stock/pages__stock_reorder_calculation__company____title": "Unternehmen", "@sage/xtrem-stock/pages__stock_reorder_calculation__criteriaBlock____title": "Kriterien", "@sage/xtrem-stock/pages__stock_reorder_calculation__endDate____title": "Enddatum", "@sage/xtrem-stock/pages__stock_reorder_calculation__fromItem____columns__title__category__name": "<PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_reorder_calculation__fromItem____lookupDialogTitle": "Artikel auswählen", "@sage/xtrem-stock/pages__stock_reorder_calculation__fromItem____placeholder": "Auswählen...", "@sage/xtrem-stock/pages__stock_reorder_calculation__fromItem____title": "<PERSON><PERSON><PERSON> von", "@sage/xtrem-stock/pages__stock_reorder_calculation__itemValues____columns__columns__stockUnit__symbol__title__2": "Symbol", "@sage/xtrem-stock/pages__stock_reorder_calculation__lines____columns__title__company__name": "Unternehmen", "@sage/xtrem-stock/pages__stock_reorder_calculation__lines____columns__title__itemSite__item__description": "Artikelbezeichnung", "@sage/xtrem-stock/pages__stock_reorder_calculation__lines____columns__title__itemSite__item__id": "Artikel-ID", "@sage/xtrem-stock/pages__stock_reorder_calculation__lines____columns__title__itemSite__item__name": "Artikel", "@sage/xtrem-stock/pages__stock_reorder_calculation__lines____columns__title__itemSite__site__id": "<PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_reorder_calculation__lines____columns__title__orderDate": "Auftragsdatum", "@sage/xtrem-stock/pages__stock_reorder_calculation__lines____columns__title__quantity": "Vorgeschlagene Menge", "@sage/xtrem-stock/pages__stock_reorder_calculation__lines____columns__title__reorderType": "Verarbeitungsmethode", "@sage/xtrem-stock/pages__stock_reorder_calculation__lines____columns__title__supplier__businessEntity__id": "ID Lieferant", "@sage/xtrem-stock/pages__stock_reorder_calculation__lines____columns__title__supplier__businessEntity__name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_reorder_calculation__lines____dropdownActions__title": "Bestellung erstellen", "@sage/xtrem-stock/pages__stock_reorder_calculation__lines____dropdownActions__title__2": "Fertigungsauftrag erstellen", "@sage/xtrem-stock/pages__stock_reorder_calculation__mainSection____title": "Allgemein", "@sage/xtrem-stock/pages__stock_reorder_calculation__purchase_order_creation__success": "Bestellung {{num}} erstellt", "@sage/xtrem-stock/pages__stock_reorder_calculation__reorderType____title": "Wiederbeschaffungsart", "@sage/xtrem-stock/pages__stock_reorder_calculation__runStockReorderCalculation____title": "Ausführen", "@sage/xtrem-stock/pages__stock_reorder_calculation__sites____columns__title__id": "ID", "@sage/xtrem-stock/pages__stock_reorder_calculation__sites____columns__title__name": "Name", "@sage/xtrem-stock/pages__stock_reorder_calculation__sites____lookupDialogTitle": "Standort auswählen", "@sage/xtrem-stock/pages__stock_reorder_calculation__sites____placeholder": "Auswählen ...", "@sage/xtrem-stock/pages__stock_reorder_calculation__sites____title": "<PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_reorder_calculation__status____title": "Status", "@sage/xtrem-stock/pages__stock_reorder_calculation__toItem____columns__title__category__name": "<PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_reorder_calculation__toItem____columns__title__description": "Bezeichnung", "@sage/xtrem-stock/pages__stock_reorder_calculation__toItem____columns__title__id": "ID", "@sage/xtrem-stock/pages__stock_reorder_calculation__toItem____columns__title__name": "Name", "@sage/xtrem-stock/pages__stock_reorder_calculation__toItem____lookupDialogTitle": "Artikel auswählen", "@sage/xtrem-stock/pages__stock_reorder_calculation__toItem____placeholder": "Auswählen...", "@sage/xtrem-stock/pages__stock_reorder_calculation__toItem____title": "Artikel bis", "@sage/xtrem-stock/pages__stock_reorder_calculation__user____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_reorder_calculation__work_order_creation__success": "Fertigungsauftrag {{num}} erstellt", "@sage/xtrem-stock/pages__stock_reorder_calculation_new__fromItem____columns__title__description": "Bezeichnung", "@sage/xtrem-stock/pages__stock_reorder_calculation_new__fromItem____columns__title__id": "ID", "@sage/xtrem-stock/pages__stock_reorder_calculation_new__fromItem____columns__title__name": "Name", "@sage/xtrem-stock/pages__stock_reorder_calculation_new__user____columns__title__email": "E-Mail", "@sage/xtrem-stock/pages__stock_reorder_calculation_new__user____columns__title__firstName": "<PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_reorder_calculation_new__user____columns__title__lastName": "Nachname", "@sage/xtrem-stock/pages__stock_reorder_calculation_new__user____lookupDialogTitle": "Benutzer auswählen", "@sage/xtrem-stock/pages__stock_reorder_calculation_request__notification_calculation_failed": "Berechnung Wiederbeschaffung Lager fehlgeschlagen.", "@sage/xtrem-stock/pages__stock_reorder_calculation_request__notification_calculation_finished": "Berechnung Wiederbeschaffung Lager durchgeführt.", "@sage/xtrem-stock/pages__stock_reorder_calculation_request__notification_request_sent": "Berechnungsanforderung Wiederbeschaffung Lager gesendet.", "@sage/xtrem-stock/pages__stock_trace_inquiry____title": "Rückverfolgung Artikel", "@sage/xtrem-stock/pages__stock_trace_inquiry__criteriaBlock____title": "Kriterien", "@sage/xtrem-stock/pages__stock_trace_inquiry__item____title": "Artikel", "@sage/xtrem-stock/pages__stock_trace_inquiry__lot____columns__title__id": "ID", "@sage/xtrem-stock/pages__stock_trace_inquiry__lot____columns__title__item__name": "Artikel", "@sage/xtrem-stock/pages__stock_trace_inquiry__lot____columns__title__sublot": "Untercharge", "@sage/xtrem-stock/pages__stock_trace_inquiry__lot____lookupDialogTitle": "Charge auswählen", "@sage/xtrem-stock/pages__stock_trace_inquiry__lot____placeholder": "Charge auswählen", "@sage/xtrem-stock/pages__stock_trace_inquiry__lot____title": "Charge", "@sage/xtrem-stock/pages__stock_trace_inquiry__serialNumber____columns__title__id": "ID", "@sage/xtrem-stock/pages__stock_trace_inquiry__serialNumber____columns__title__item__name": "Artikel", "@sage/xtrem-stock/pages__stock_trace_inquiry__serialNumber____columns__title__site__name": "<PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_trace_inquiry__serialNumber____lookupDialogTitle": "Seriennummer auswählen", "@sage/xtrem-stock/pages__stock_trace_inquiry__serialNumber____placeholder": "Seriennummer auswählen", "@sage/xtrem-stock/pages__stock_trace_inquiry__serialNumber____title": "Seriennummer", "@sage/xtrem-stock/pages__stock_trace_inquiry__site____title": "<PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_valuation____objectTypeSingular": "Bestandsbewertung", "@sage/xtrem-stock/pages__stock_valuation____title": "Bestandsbewertung", "@sage/xtrem-stock/pages__stock_valuation__commodityCode____title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_valuation__company____columns__title__id": "ID", "@sage/xtrem-stock/pages__stock_valuation__company____columns__title__name": "Name", "@sage/xtrem-stock/pages__stock_valuation__company____lookupDialogTitle": "Unternehmen auswählen", "@sage/xtrem-stock/pages__stock_valuation__company____placeholder": "Auswählen ...", "@sage/xtrem-stock/pages__stock_valuation__company____title": "Unternehmen", "@sage/xtrem-stock/pages__stock_valuation__criteriaBlock____title": "Kriterien", "@sage/xtrem-stock/pages__stock_valuation__date____title": "Datum", "@sage/xtrem-stock/pages__stock_valuation__displayZeroValues____title": "Nullwerte anzeigen", "@sage/xtrem-stock/pages__stock_valuation__fromItem____columns__title__category__name": "<PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_valuation__fromItem____lookupDialogTitle": "Artikel auswählen", "@sage/xtrem-stock/pages__stock_valuation__fromItem____placeholder": "Auswählen...", "@sage/xtrem-stock/pages__stock_valuation__fromItem____title": "<PERSON><PERSON><PERSON> von", "@sage/xtrem-stock/pages__stock_valuation__itemCategory____columns__title__type": "<PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_valuation__itemCategory____lookupDialogTitle": "Artikelkategorie auswählen", "@sage/xtrem-stock/pages__stock_valuation__itemCategory____title": "Artikelkategorie", "@sage/xtrem-stock/pages__stock_valuation__itemSites____columns__columns__item__id__title": "Status", "@sage/xtrem-stock/pages__stock_valuation__itemSites____columns__columns__stockUnit__symbol__title": "Symbol", "@sage/xtrem-stock/pages__stock_valuation__itemSites____columns__columns__stockUnit__symbol__title__2": "Symbol", "@sage/xtrem-stock/pages__stock_valuation__itemSites____columns__title__commodityCode": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_valuation__itemSites____columns__title__company__name": "Unternehmen", "@sage/xtrem-stock/pages__stock_valuation__itemSites____columns__title__costType": "<PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_valuation__itemSites____columns__title__currency__symbol": "Währung", "@sage/xtrem-stock/pages__stock_valuation__itemSites____columns__title__item__category__id": "Artikelkategorie", "@sage/xtrem-stock/pages__stock_valuation__itemSites____columns__title__item__description": "Artikelbezeichnung", "@sage/xtrem-stock/pages__stock_valuation__itemSites____columns__title__item__id": "Artikel-ID", "@sage/xtrem-stock/pages__stock_valuation__itemSites____columns__title__item__name": "Artikel", "@sage/xtrem-stock/pages__stock_valuation__itemSites____columns__title__item__status": "Status", "@sage/xtrem-stock/pages__stock_valuation__itemSites____columns__title__postingClass__name": "Buchungsklasse", "@sage/xtrem-stock/pages__stock_valuation__itemSites____columns__title__quantity": "<PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_valuation__itemSites____columns__title__site__name": "<PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_valuation__itemSites____columns__title__stockUnit__symbol": "Lagereinheit", "@sage/xtrem-stock/pages__stock_valuation__itemSites____columns__title__stockValue": "Bestandswert", "@sage/xtrem-stock/pages__stock_valuation__itemSites____columns__title__unitCost": "Stückkosten", "@sage/xtrem-stock/pages__stock_valuation__itemSites____columns__title__valuationDate": "Bewertungsdatum", "@sage/xtrem-stock/pages__stock_valuation__mainSection____title": "Allgemein", "@sage/xtrem-stock/pages__stock_valuation__postingClass____lookupDialogTitle": "Buchungsklasse auswählen", "@sage/xtrem-stock/pages__stock_valuation__postingClass____title": "Buchungsklasse", "@sage/xtrem-stock/pages__stock_valuation__runStockValuation____title": "Ausführen", "@sage/xtrem-stock/pages__stock_valuation__selectedSites____columns__columns__site__name__columns__title__id": "ID", "@sage/xtrem-stock/pages__stock_valuation__selectedSites____columns__columns__site__name__title": "Name", "@sage/xtrem-stock/pages__stock_valuation__selectedSites____columns__columns__site__name__title__2": "ID", "@sage/xtrem-stock/pages__stock_valuation__selectedSites____columns__title__site__name": "<PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_valuation__selectedSites____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_valuation__sites____columns__title__id": "ID", "@sage/xtrem-stock/pages__stock_valuation__sites____columns__title__name": "Name", "@sage/xtrem-stock/pages__stock_valuation__sites____lookupDialogTitle": "Standort auswählen", "@sage/xtrem-stock/pages__stock_valuation__sites____placeholder": "Auswählen ...", "@sage/xtrem-stock/pages__stock_valuation__sites____title": "<PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_valuation__status____title": "Status", "@sage/xtrem-stock/pages__stock_valuation__toItem____columns__title__category__name": "<PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_valuation__toItem____columns__title__description": "Bezeichnung", "@sage/xtrem-stock/pages__stock_valuation__toItem____columns__title__id": "ID", "@sage/xtrem-stock/pages__stock_valuation__toItem____columns__title__name": "Name", "@sage/xtrem-stock/pages__stock_valuation__toItem____lookupDialogTitle": "Artikel auswählen", "@sage/xtrem-stock/pages__stock_valuation__toItem____placeholder": "Auswählen...", "@sage/xtrem-stock/pages__stock_valuation__toItem____title": "Artikel bis", "@sage/xtrem-stock/pages__stock_valuation__totalStockValue____title": "Bestandswert gesamt", "@sage/xtrem-stock/pages__stock_valuation__totalStockValueCurrency____title": "Währung Bestandswert gesamt", "@sage/xtrem-stock/pages__stock_valuation__user____columns__title__email": "E-Mail", "@sage/xtrem-stock/pages__stock_valuation__user____columns__title__firstName": "<PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_valuation__user____columns__title__lastName": "Nachname", "@sage/xtrem-stock/pages__stock_valuation__user____lookupDialogTitle": "Benutzer auswählen", "@sage/xtrem-stock/pages__stock_valuation__user____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_valuation__valuationMethod____title": "Bewertungsmethode", "@sage/xtrem-stock/pages__stock_valuation_inquiry____title": "Abfrage Bestandsbewertung", "@sage/xtrem-stock/pages__stock_valuation_inquiry__company____columns__title__id": "ID", "@sage/xtrem-stock/pages__stock_valuation_inquiry__company____columns__title__name": "Name", "@sage/xtrem-stock/pages__stock_valuation_inquiry__company____placeholder": "Auswählen...", "@sage/xtrem-stock/pages__stock_valuation_inquiry__company____title": "Unternehmen", "@sage/xtrem-stock/pages__stock_valuation_inquiry__confirmation____title": "Bestätigung", "@sage/xtrem-stock/pages__stock_valuation_inquiry__criteriaBlock____title": "Kriterien", "@sage/xtrem-stock/pages__stock_valuation_inquiry__itemFrom____columns__title__id": "ID", "@sage/xtrem-stock/pages__stock_valuation_inquiry__itemFrom____columns__title__name": "Name", "@sage/xtrem-stock/pages__stock_valuation_inquiry__itemFrom____placeholder": "Auswählen...", "@sage/xtrem-stock/pages__stock_valuation_inquiry__itemFrom____title": "<PERSON><PERSON><PERSON> von", "@sage/xtrem-stock/pages__stock_valuation_inquiry__itemSelection____columns__title__item__id": "Artikel", "@sage/xtrem-stock/pages__stock_valuation_inquiry__itemSelection____columns__title__item__name": "Name", "@sage/xtrem-stock/pages__stock_valuation_inquiry__itemSelection____columns__title__site__name": "<PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_valuation_inquiry__itemSelection____columns__title__stockUnit__name": "Maßeinheit", "@sage/xtrem-stock/pages__stock_valuation_inquiry__itemTo____columns__title__id": "ID", "@sage/xtrem-stock/pages__stock_valuation_inquiry__itemTo____columns__title__name": "Name", "@sage/xtrem-stock/pages__stock_valuation_inquiry__itemTo____placeholder": "Auswählen...", "@sage/xtrem-stock/pages__stock_valuation_inquiry__itemTo____title": "Artikel bis", "@sage/xtrem-stock/pages__stock_valuation_inquiry__itemValues____columns__title__company__name": "Unternehmen", "@sage/xtrem-stock/pages__stock_valuation_inquiry__itemValues____columns__title__currency__symbol": "Währung", "@sage/xtrem-stock/pages__stock_valuation_inquiry__itemValues____columns__title__dateOfValuation": "Bewertungsdatum", "@sage/xtrem-stock/pages__stock_valuation_inquiry__itemValues____columns__title__item__id": "Artikel", "@sage/xtrem-stock/pages__stock_valuation_inquiry__itemValues____columns__title__item__name": "Name", "@sage/xtrem-stock/pages__stock_valuation_inquiry__itemValues____columns__title__lastDateOfValuation": "Letztes Bewertungsdatum", "@sage/xtrem-stock/pages__stock_valuation_inquiry__itemValues____columns__title__lastQuantityInStock": "Letzte Menge", "@sage/xtrem-stock/pages__stock_valuation_inquiry__itemValues____columns__title__lastStockValue": "Letzter Bestandswert", "@sage/xtrem-stock/pages__stock_valuation_inquiry__itemValues____columns__title__lastUnitCost": "Letzte Stückkosten", "@sage/xtrem-stock/pages__stock_valuation_inquiry__itemValues____columns__title__lastValuationMethod": "<PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_valuation_inquiry__itemValues____columns__title__postingClass__name": "Buchungsklasse", "@sage/xtrem-stock/pages__stock_valuation_inquiry__itemValues____columns__title__quantityInStock": "<PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_valuation_inquiry__itemValues____columns__title__site__name": "<PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_valuation_inquiry__itemValues____columns__title__stockUnit__id": "Lagereinheit", "@sage/xtrem-stock/pages__stock_valuation_inquiry__itemValues____columns__title__stockValue": "Bestandswert", "@sage/xtrem-stock/pages__stock_valuation_inquiry__itemValues____columns__title__unitCost": "Stückkosten", "@sage/xtrem-stock/pages__stock_valuation_inquiry__itemValues____columns__title__valuationMethod": "<PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_valuation_inquiry__itemValues____columns__title__variance": "<PERSON>bwei<PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_valuation_inquiry__mainBlock____title": "Ergebnisse", "@sage/xtrem-stock/pages__stock_valuation_inquiry__runStockValuation____title": "Ausführen", "@sage/xtrem-stock/pages__stock_valuation_inquiry__search": "<PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_valuation_inquiry__sites____columns__title__id": "ID", "@sage/xtrem-stock/pages__stock_valuation_inquiry__sites____columns__title__name": "Name", "@sage/xtrem-stock/pages__stock_valuation_inquiry__sites____placeholder": "Auswählen...", "@sage/xtrem-stock/pages__stock_valuation_inquiry__sites____title": "<PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_valuation_inquiry__valuationDate____title": "Datum", "@sage/xtrem-stock/pages__stock_valuation_request__notification_request_sent": "Bestandsbewertungsanforderung gesendet.", "@sage/xtrem-stock/pages__stock_valuation_request__notification_valuation_failed": "Bestandsbewertung fehlgeschlagen.", "@sage/xtrem-stock/pages__stock_valuation_request__notification_valuation_finished": "Bestandsbewertung fertig.", "@sage/xtrem-stock/pages__stock_value_change____navigationPanel__listItem__itemDescription__title": "Artikelbezeichnung", "@sage/xtrem-stock/pages__stock_value_change____navigationPanel__listItem__itemId__title": "Artikel-ID", "@sage/xtrem-stock/pages__stock_value_change____navigationPanel__listItem__line2__title": "Standortname", "@sage/xtrem-stock/pages__stock_value_change____navigationPanel__listItem__line2Right__title": "Datum", "@sage/xtrem-stock/pages__stock_value_change____navigationPanel__listItem__line3__title": "Artikel", "@sage/xtrem-stock/pages__stock_value_change____navigationPanel__listItem__siteID__title": "Standort-ID", "@sage/xtrem-stock/pages__stock_value_change____navigationPanel__listItem__title__title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_value_change____navigationPanel__listItem__titleRight__title": "Bestandsstatus", "@sage/xtrem-stock/pages__stock_value_change____objectTypePlural": "Änderungen Bestandswert", "@sage/xtrem-stock/pages__stock_value_change____objectTypeSingular": "Änderung Bestandswert", "@sage/xtrem-stock/pages__stock_value_change____title": "Änderung Bestandswert", "@sage/xtrem-stock/pages__stock_value_change__addAverageCost____title": "Hinzufügen", "@sage/xtrem-stock/pages__stock_value_change__defaultDimension____title": "Standardsektoren setzen", "@sage/xtrem-stock/pages__stock_value_change__description____title": "Bezeichnung", "@sage/xtrem-stock/pages__stock_value_change__duplication_among_added_lines": "Doppelte Zeilen ändern oder entfernen.", "@sage/xtrem-stock/pages__stock_value_change__goToSysNotificationPage____title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_value_change__item____columns__columns__stockUnit__description__title": "Name", "@sage/xtrem-stock/pages__stock_value_change__item____columns__title__category__name": "<PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_value_change__item____columns__title__stockUnit__description": "Lagereinheit", "@sage/xtrem-stock/pages__stock_value_change__item____lookupDialogTitle": "Artikel auswählen", "@sage/xtrem-stock/pages__stock_value_change__item____title": "Artikel", "@sage/xtrem-stock/pages__stock_value_change__lines____columns__title__amount": "Betrag", "@sage/xtrem-stock/pages__stock_value_change__lines____columns__title__fifoCost__amount": "Betrag", "@sage/xtrem-stock/pages__stock_value_change__lines____columns__title__fifoCost__effectiveDate": "Aktivierungsdatum", "@sage/xtrem-stock/pages__stock_value_change__lines____columns__title__fifoCost__nonAbsorbedAmount": "Nicht absorbierter Betrag", "@sage/xtrem-stock/pages__stock_value_change__lines____columns__title__fifoCost__receiptDocumentLine__documentNumber": "Eingang", "@sage/xtrem-stock/pages__stock_value_change__lines____columns__title__fifoCost__receiptQuantity": "Eingangsmenge", "@sage/xtrem-stock/pages__stock_value_change__lines____columns__title__fifoCost__remainingQuantity": "Verbleibende Menge", "@sage/xtrem-stock/pages__stock_value_change__lines____columns__title__fifoCost__sequence": "Nummernkreis", "@sage/xtrem-stock/pages__stock_value_change__lines____columns__title__newAmount": "<PERSON>euer Betrag", "@sage/xtrem-stock/pages__stock_value_change__lines____columns__title__newUnitCost": "Neue Stückkosten", "@sage/xtrem-stock/pages__stock_value_change__lines____columns__title__quantity": "<PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_value_change__lines____columns__title__stockTransactionStatus": "Bestandsstatus", "@sage/xtrem-stock/pages__stock_value_change__lines____columns__title__unitCost": "Stückkosten", "@sage/xtrem-stock/pages__stock_value_change__lines____dropdownActions__title": "Löschen", "@sage/xtrem-stock/pages__stock_value_change__lines____dropdownActions__title__2": "Sektoren", "@sage/xtrem-stock/pages__stock_value_change__lines____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_value_change__linesSection____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_value_change__mainSection____title": "Allgemein", "@sage/xtrem-stock/pages__stock_value_change__number____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_value_change__post____title": "<PERSON>and buchen", "@sage/xtrem-stock/pages__stock_value_change__postedDate____title": "Datum", "@sage/xtrem-stock/pages__stock_value_change__postingDetails____columns__title__financeIntegrationAppRecordId": "Referenz Integration Buchhaltung", "@sage/xtrem-stock/pages__stock_value_change__postingDetails____columns__title__postingStatus": "Status", "@sage/xtrem-stock/pages__stock_value_change__postingDetails____columns__title__targetDocumentNumber": "Dokumentnummer", "@sage/xtrem-stock/pages__stock_value_change__postingDetails____columns__title__targetDocumentType": "Dokumenttyp", "@sage/xtrem-stock/pages__stock_value_change__postingDetails____title": "Ergebnisse", "@sage/xtrem-stock/pages__stock_value_change__postingMessageBlock____title": "Fehlerdetails", "@sage/xtrem-stock/pages__stock_value_change__postingSection____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_value_change__repost____title": "<PERSON><PERSON><PERSON> buchen", "@sage/xtrem-stock/pages__stock_value_change__repost_errors": "Fehler bei der erneuten Buchung: {{errors}}", "@sage/xtrem-stock/pages__stock_value_change__save_warnings": "Warnungen beim S<PERSON>ichern:", "@sage/xtrem-stock/pages__stock_value_change__saveStockValueChange____title": "Speichern", "@sage/xtrem-stock/pages__stock_value_change__selectFromFifo____title": "Aus FIFO-Stapel hinzufügen", "@sage/xtrem-stock/pages__stock_value_change__site____lookupDialogTitle": "Standort auswählen", "@sage/xtrem-stock/pages__stock_value_change__site____placeholder": "Auswählen...", "@sage/xtrem-stock/pages__stock_value_change__site____title": "<PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_value_change__stockTransactionStatus____title": "Bestandsstatus", "@sage/xtrem-stock/pages__stock_value_change__totalQuantityInStock____title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_value_change__totalValueOfStock____title": "Wert", "@sage/xtrem-stock/pages__stock_value_change__unitCost____title": "Stückkosten", "@sage/xtrem-stock/pages__stock_value_change__valuationMethod____title": "Bewertungsmethode", "@sage/xtrem-stock/pages__stock_value_correction____navigationPanel__listItem__description__title": "Bezeichnung", "@sage/xtrem-stock/pages__stock_value_correction____navigationPanel__listItem__effectiveDate__title": "Datum", "@sage/xtrem-stock/pages__stock_value_correction____navigationPanel__listItem__reasonCode__title": "Korrekturgrund", "@sage/xtrem-stock/pages__stock_value_correction____navigationPanel__listItem__stockSite__title": "<PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_value_correction____navigationPanel__listItem__stockTransactionStatus__title": "Bestandsstatus", "@sage/xtrem-stock/pages__stock_value_correction____navigationPanel__listItem__title__title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_value_correction____objectTypePlural": "Korrekturen Bestandswert", "@sage/xtrem-stock/pages__stock_value_correction____objectTypeSingular": "Korrektur Bestandswert", "@sage/xtrem-stock/pages__stock_value_correction____title": "Korrektur Bestandswert", "@sage/xtrem-stock/pages__stock_value_correction___correctedDocumentType____title": "Dokumenttyp", "@sage/xtrem-stock/pages__stock_value_correction__addAdjustLine____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_value_correction__defaultDimension____title": "Standardsektoren setzen", "@sage/xtrem-stock/pages__stock_value_correction__description____title": "Bezeichnung", "@sage/xtrem-stock/pages__stock_value_correction__effectiveDate____title": "Datum", "@sage/xtrem-stock/pages__stock_value_correction__lines____columns__columns__item__name__columns__title__decimalDigits": "Dezimalstellen", "@sage/xtrem-stock/pages__stock_value_correction__lines____columns__columns__item__name__columns__title__id": "ID", "@sage/xtrem-stock/pages__stock_value_correction__lines____columns__columns__item__name__columns__title__name": "Name", "@sage/xtrem-stock/pages__stock_value_correction__lines____columns__columns__item__name__columns__title__symbol": "Symbol", "@sage/xtrem-stock/pages__stock_value_correction__lines____columns__columns__item__name__title": "Name", "@sage/xtrem-stock/pages__stock_value_correction__lines____columns__columns__item__name__title__2": "ID", "@sage/xtrem-stock/pages__stock_value_correction__lines____columns__columns__item__name__title__3": "<PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_value_correction__lines____columns__columns__title___sortValue": "Zeilennummer", "@sage/xtrem-stock/pages__stock_value_correction__lines____columns__columns__title__document__number": "Dokumentnummer", "@sage/xtrem-stock/pages__stock_value_correction__lines____columns__columns__title__orderCost": "Auftragskosten", "@sage/xtrem-stock/pages__stock_value_correction__lines____columns__lookupDialogTitle__item__name": "Artikel auswählen", "@sage/xtrem-stock/pages__stock_value_correction__lines____columns__placeholder__item__name": "Auswählen ...", "@sage/xtrem-stock/pages__stock_value_correction__lines____columns__title": "Dokumentzeile", "@sage/xtrem-stock/pages__stock_value_correction__lines____columns__title__item__name": "Artikel", "@sage/xtrem-stock/pages__stock_value_correction__lines____columns__title__newUnitCost": "Neue Auftragskosten", "@sage/xtrem-stock/pages__stock_value_correction__lines____columns__title__orderCost": "Auftragskosten", "@sage/xtrem-stock/pages__stock_value_correction__lines____columns__title__stockTransactionStatus": "Bestandsstatus", "@sage/xtrem-stock/pages__stock_value_correction__lines____columns__title__unitCost": "Aktuelle Kosten", "@sage/xtrem-stock/pages__stock_value_correction__lines____dropdownActions__title": "Sektoren", "@sage/xtrem-stock/pages__stock_value_correction__lines____dropdownActions__title__2": "Löschen", "@sage/xtrem-stock/pages__stock_value_correction__lines____title": "Dokumentzeilen", "@sage/xtrem-stock/pages__stock_value_correction__mainSection____title": "Allgemein", "@sage/xtrem-stock/pages__stock_value_correction__number____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_value_correction__post____title": "Buchen", "@sage/xtrem-stock/pages__stock_value_correction__purchaseReceiptLines____columns__columns__item__name__columns__title__decimalDigits": "Dezimalstellen", "@sage/xtrem-stock/pages__stock_value_correction__purchaseReceiptLines____columns__columns__item__name__columns__title__id": "ID", "@sage/xtrem-stock/pages__stock_value_correction__purchaseReceiptLines____columns__columns__item__name__columns__title__name": "Name", "@sage/xtrem-stock/pages__stock_value_correction__purchaseReceiptLines____columns__columns__item__name__title": "Name", "@sage/xtrem-stock/pages__stock_value_correction__purchaseReceiptLines____columns__columns__item__name__title__2": "ID", "@sage/xtrem-stock/pages__stock_value_correction__purchaseReceiptLines____columns__columns__item__name__title__3": "Einheit", "@sage/xtrem-stock/pages__stock_value_correction__purchaseReceiptLines____columns__columns__item__name__title__4": "Chargenverwaltung", "@sage/xtrem-stock/pages__stock_value_correction__purchaseReceiptLines____columns__columns__title___sortValue": "Zeilennummer", "@sage/xtrem-stock/pages__stock_value_correction__purchaseReceiptLines____columns__columns__title__document__number": "Dokumentnummer", "@sage/xtrem-stock/pages__stock_value_correction__purchaseReceiptLines____columns__placeholder__item__name": "Auswählen...", "@sage/xtrem-stock/pages__stock_value_correction__purchaseReceiptLines____columns__title": "Dokumentzeile", "@sage/xtrem-stock/pages__stock_value_correction__purchaseReceiptLines____columns__title__item__name": "Artikel", "@sage/xtrem-stock/pages__stock_value_correction__purchaseReceiptLines____columns__title__newUnitCost": "Neue Stückkosten", "@sage/xtrem-stock/pages__stock_value_correction__purchaseReceiptLines____columns__title__stockTransactionStatus": "Bestandsstatus", "@sage/xtrem-stock/pages__stock_value_correction__purchaseReceiptLines____columns__title__unitCost": "Stückkosten", "@sage/xtrem-stock/pages__stock_value_correction__purchaseReceiptLines____dropdownActions__title": "Sektoren", "@sage/xtrem-stock/pages__stock_value_correction__purchaseReceiptLines____dropdownActions__title__2": "Löschen", "@sage/xtrem-stock/pages__stock_value_correction__purchaseReceiptLines____title": "Dokumentzeilen", "@sage/xtrem-stock/pages__stock_value_correction__reasonCode____columns__title__id": "ID", "@sage/xtrem-stock/pages__stock_value_correction__reasonCode____columns__title__name": "Name", "@sage/xtrem-stock/pages__stock_value_correction__reasonCode____columns__title__stockValueCorrection": "Wertkorrektur", "@sage/xtrem-stock/pages__stock_value_correction__reasonCode____lookupDialogTitle": "Grundcode auswählen", "@sage/xtrem-stock/pages__stock_value_correction__reasonCode____title": "Korrekturgrund", "@sage/xtrem-stock/pages__stock_value_correction__saveStockValueCorrection____title": "Speichern", "@sage/xtrem-stock/pages__stock_value_correction__stockReceiptLines____columns__columns__columns__document__number__title": "<PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_value_correction__stockReceiptLines____columns__columns__item__name__columns__title__decimalDigits": "Dezimalstellen", "@sage/xtrem-stock/pages__stock_value_correction__stockReceiptLines____columns__columns__item__name__columns__title__id": "ID", "@sage/xtrem-stock/pages__stock_value_correction__stockReceiptLines____columns__columns__item__name__columns__title__name": "Name", "@sage/xtrem-stock/pages__stock_value_correction__stockReceiptLines____columns__columns__item__name__title": "Name", "@sage/xtrem-stock/pages__stock_value_correction__stockReceiptLines____columns__columns__item__name__title__2": "ID", "@sage/xtrem-stock/pages__stock_value_correction__stockReceiptLines____columns__columns__item__name__title__3": "Einheit", "@sage/xtrem-stock/pages__stock_value_correction__stockReceiptLines____columns__columns__item__name__title__4": "Chargenverwaltung", "@sage/xtrem-stock/pages__stock_value_correction__stockReceiptLines____columns__columns__title___sortValue": "Zeilennummer", "@sage/xtrem-stock/pages__stock_value_correction__stockReceiptLines____columns__columns__title__document__number": "Dokumentnummer", "@sage/xtrem-stock/pages__stock_value_correction__stockReceiptLines____columns__placeholder__item__name": "Auswählen...", "@sage/xtrem-stock/pages__stock_value_correction__stockReceiptLines____columns__title": "Dokumentzeile", "@sage/xtrem-stock/pages__stock_value_correction__stockReceiptLines____columns__title__item__name": "Artikel", "@sage/xtrem-stock/pages__stock_value_correction__stockReceiptLines____columns__title__newUnitCost": "Neue Stückkosten", "@sage/xtrem-stock/pages__stock_value_correction__stockReceiptLines____columns__title__stockTransactionStatus": "Bestandsstatus", "@sage/xtrem-stock/pages__stock_value_correction__stockReceiptLines____columns__title__unitCost": "Stückkosten", "@sage/xtrem-stock/pages__stock_value_correction__stockReceiptLines____dropdownActions__title": "Sektoren", "@sage/xtrem-stock/pages__stock_value_correction__stockReceiptLines____dropdownActions__title__2": "Löschen", "@sage/xtrem-stock/pages__stock_value_correction__stockReceiptLines____title": "Dokumentzeilen", "@sage/xtrem-stock/pages__stock_value_correction__stockSite____columns__title__id": "ID", "@sage/xtrem-stock/pages__stock_value_correction__stockSite____columns__title__name": "Name", "@sage/xtrem-stock/pages__stock_value_correction__stockSite____lookupDialogTitle": "Standort auswählen", "@sage/xtrem-stock/pages__stock_value_correction__stockSite____placeholder": "Auswählen ...", "@sage/xtrem-stock/pages__stock_value_correction__stockSite____title": "<PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_value_correction__stockTransactionStatus____title": "Bestandsstatus", "@sage/xtrem-stock/pages__stock-adjustment-detail-panel__notification__The_adjustment_quantity_is_mandatory": "Erfassen Sie die Korrekturmenge.", "@sage/xtrem-stock/pages__stock-entry-detail-panel__notification__The_location_is_mandatory": "Erfassen Sie den Lagerplatz.", "@sage/xtrem-stock/pages__stock-entry-detail-panel__notification__The_stock_status_is_mandatory": "Erfassen Sie den Bestandsstatus.", "@sage/xtrem-stock/pages__stock-receipt__notification__The_lot_is_mandatory": "Erfassen Sie die Charge.", "@sage/xtrem-stock/pages_sidebar_tab_title_information": "Informationen", "@sage/xtrem-stock/pages-confirm-cancel": "Abbrechen", "@sage/xtrem-stock/pages-confirm-delete": "Löschen", "@sage/xtrem-stock/permission__create__name": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-stock/permission__create_item_site_costs_from_cost_roll_up_results__name": "Artikel-Standort-Kosten aus Kostenkalkulationsergebnissen erstellen", "@sage/xtrem-stock/permission__delete__name": "Löschen", "@sage/xtrem-stock/permission__finance_integration_check__name": "Prüfung Integration Finanzen", "@sage/xtrem-stock/permission__manage__name": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-stock/permission__manage_cost__name": "<PERSON><PERSON> ver<PERSON>", "@sage/xtrem-stock/permission__post__name": "Buchen", "@sage/xtrem-stock/permission__post_to_stock__name": "Buchung in Bestand", "@sage/xtrem-stock/permission__read__name": "<PERSON><PERSON>", "@sage/xtrem-stock/permission__repost__name": "<PERSON><PERSON><PERSON> buchen", "@sage/xtrem-stock/permission__start__name": "Start", "@sage/xtrem-stock/permission__sync_stock_value_change__name": "Synchronisierung Änderung Bestandswert", "@sage/xtrem-stock/permission__update__name": "Aktualisieren", "@sage/xtrem-stock/permission__update_attributes_and_dimensions__name": "Attribute und Sektoren aktualisieren", "@sage/xtrem-stock/search": "<PERSON><PERSON>", "@sage/xtrem-stock/search-count": "Suche ({{#if plus}}+{{/if}}{{nb}})", "@sage/xtrem-stock/standard_cost_roll_up_calculation__stop_calculation": "Standardkostenkalkulationsberechnung angehalten um {{stopTime}}.", "@sage/xtrem-stock/standard_cost_roll_up_calculation_complete": "Standardkostenkalkulationsberechnung abgeschlossen.", "@sage/xtrem-stock/standard_cost_roll_up_calculation_completed": "Standardkostenkalkulationsberechnung erfolgreich abgeschlossen.", "@sage/xtrem-stock/standard_cost_roll_up_calculation_failed": "Standardkostenkalkulationsberechnung fehlgeschlagen: {{{errorMessage}}}.", "@sage/xtrem-stock/standard_cost_roll_up_calculation_processing_items": "Die Standardkostenkalkulationsberechnung verarbeitet derzeit Artikel.", "@sage/xtrem-stock/standard_cost_roll_up_calculation_start": "Start der Standardkostenkalkulationsberechnung.", "@sage/xtrem-stock/standard_cost_roll_up_calculation_updating_sub_assemblies": "Die Standardkostenkalkulationsberechnung aktualisiert derzeit Unterbaugruppen.", "@sage/xtrem-stock/stock_count_pages__stock_count_print": "<PERSON><PERSON><PERSON>", "@sage/xtrem-stock/stock_value_change_from_item_site_cost": "Änderung Bestandswert aus Artikel-Standort-Kosten.", "@sage/xtrem-stock/stock_value_change_from_item_site_cost_complete": "Änderung Bestandswert aus Artikel-Standort-Kosten erledigt.", "@sage/xtrem-stock/stock_value_change_from_item_site_cost_saved": "Änderung Bestandswert aus Artikel-Standort-Kosten gespeichert.", "@sage/xtrem-stock/stock_value_change_from_item_site_cost_start": "Änderung Bestandswert aus Artikel-Standort-Kosten gestartet.", "@sage/xtrem-stock/stock_value_change_from_item_site_cost_stop": "Bestandswertänderungen enden am {{stopDate}} "}