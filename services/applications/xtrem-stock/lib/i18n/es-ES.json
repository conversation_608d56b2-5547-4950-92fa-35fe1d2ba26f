{"@sage/xtrem-stock/activity__cost_roll_up_input_set__name": "Conjunto de entrada de cálculo de costes", "@sage/xtrem-stock/activity__stock_adjustment__name": "Regularización de stock", "@sage/xtrem-stock/activity__stock_change__name": "Cambio de stock", "@sage/xtrem-stock/activity__stock_count__name": "Inventario", "@sage/xtrem-stock/activity__stock_issue__name": "Salida de stock", "@sage/xtrem-stock/activity__stock_receipt__name": "Entrada de stock", "@sage/xtrem-stock/activity__stock_reorder__name": "Reaprovisionamiento de stock", "@sage/xtrem-stock/activity__stock_valuation_input_set__name": "Conjunto de entrada de valoración de stock", "@sage/xtrem-stock/activity__stock_value_change__name": "Cambio de valor de stock", "@sage/xtrem-stock/class__allocation-engine__system_error_during_allocation_process": "El proceso de asignación se ha interrumpido. {{errorMessage}}", "@sage/xtrem-stock/create_items_create": "<PERSON><PERSON><PERSON>", "@sage/xtrem-stock/data_types__adjustable_document_type_enum__name": "Adjustable document type enum", "@sage/xtrem-stock/data_types__standard_cost_roll_up_result_line_status_enum__name": "Standard cost roll up result line status enum", "@sage/xtrem-stock/data_types__standard_cost_roll_up_status_enum__name": "Standard cost roll up status enum", "@sage/xtrem-stock/data_types__stock_adjustment_display_status_enum__name": "Stock adjustment display status enum", "@sage/xtrem-stock/data_types__stock_count_line_status_enum__name": "Stock count line status enum", "@sage/xtrem-stock/data_types__stock_count_status_enum__name": "Stock count status enum", "@sage/xtrem-stock/data_types__stock_issue_display_status_enum__name": "Stock issue display status enum", "@sage/xtrem-stock/data_types__stock_issue_line_display_status_enum__name": "Stock issue line display status enum", "@sage/xtrem-stock/data_types__stock_receipt_display_status_enum__name": "Stock receipt display status enum", "@sage/xtrem-stock/data_types__stock_receipt_line_display_status_enum__name": "Stock receipt line display status enum", "@sage/xtrem-stock/data_types__stock_valuation_status_enum__name": "Stock valuation status enum", "@sage/xtrem-stock/data_types__virtual_location_need_enum__name": "Virtual location need enum", "@sage/xtrem-stock/edit-create-line": "<PERSON><PERSON><PERSON>", "@sage/xtrem-stock/enums__adjustable_document_type__purchaseReceipt": "Recepción de compra", "@sage/xtrem-stock/enums__adjustable_document_type__stockReceipt": "Entrada de stock", "@sage/xtrem-stock/enums__standard_cost_roll_up_result_line_status__created": "<PERSON><PERSON><PERSON>", "@sage/xtrem-stock/enums__standard_cost_roll_up_result_line_status__error": "Error", "@sage/xtrem-stock/enums__standard_cost_roll_up_result_line_status__inProgress": "En curso", "@sage/xtrem-stock/enums__standard_cost_roll_up_result_line_status__pending": "Pendiente", "@sage/xtrem-stock/enums__standard_cost_roll_up_result_line_status__updated": "Actualizado", "@sage/xtrem-stock/enums__standard_cost_roll_up_status__completed": "Actualizado", "@sage/xtrem-stock/enums__standard_cost_roll_up_status__draft": "<PERSON><PERSON><PERSON>", "@sage/xtrem-stock/enums__standard_cost_roll_up_status__error": "Error", "@sage/xtrem-stock/enums__standard_cost_roll_up_status__inProgress": "En curso", "@sage/xtrem-stock/enums__stock_adjustment_display_status__adjusted": "Regularizado", "@sage/xtrem-stock/enums__stock_adjustment_display_status__detailsEntered": "Detalles introducidos", "@sage/xtrem-stock/enums__stock_adjustment_display_status__detailsRequired": "Detalles pendientes", "@sage/xtrem-stock/enums__stock_adjustment_display_status__stockPostingError": "Error de contabilización de stock", "@sage/xtrem-stock/enums__stock_adjustment_display_status__stockPostingInProgress": "Contabilización de stock en curso", "@sage/xtrem-stock/enums__stock_count_line_status__counted": "<PERSON><PERSON><PERSON>", "@sage/xtrem-stock/enums__stock_count_line_status__countInProgress": "Inventario en curso", "@sage/xtrem-stock/enums__stock_count_line_status__excluded": "Excluida", "@sage/xtrem-stock/enums__stock_count_line_status__toBeCounted": "<PERSON>r contar", "@sage/xtrem-stock/enums__stock_count_status__closed": "<PERSON><PERSON><PERSON>", "@sage/xtrem-stock/enums__stock_count_status__counted": "<PERSON><PERSON><PERSON>", "@sage/xtrem-stock/enums__stock_count_status__countInProgress": "Inventario en curso", "@sage/xtrem-stock/enums__stock_count_status__draft": "<PERSON><PERSON><PERSON>", "@sage/xtrem-stock/enums__stock_count_status__toBeCounted": "<PERSON>r contar", "@sage/xtrem-stock/enums__stock_document_status__completed": "Finalizado", "@sage/xtrem-stock/enums__stock_document_status__error": "Error", "@sage/xtrem-stock/enums__stock_issue_display_status__detailsEntered": "Detalles introducidos", "@sage/xtrem-stock/enums__stock_issue_display_status__detailsRequired": "Detalles pendientes", "@sage/xtrem-stock/enums__stock_issue_display_status__issued": "Emitida", "@sage/xtrem-stock/enums__stock_issue_display_status__stockPostingError": "Error de transacción de stock", "@sage/xtrem-stock/enums__stock_issue_display_status__stockPostingInProgress": "Contabilización de stock en curso", "@sage/xtrem-stock/enums__stock_issue_line_display_status__detailsEntered": "Detalles introducidos", "@sage/xtrem-stock/enums__stock_issue_line_display_status__detailsRequired": "Detalles pendientes", "@sage/xtrem-stock/enums__stock_issue_line_display_status__issued": "Emitida", "@sage/xtrem-stock/enums__stock_issue_line_display_status__stockPostingError": "Error de contabilización de stock", "@sage/xtrem-stock/enums__stock_issue_line_display_status__stockPostingInProgress": "Contabilización de stock en curso", "@sage/xtrem-stock/enums__stock_receipt_display_status__detailsEntered": "Detalles introducidos", "@sage/xtrem-stock/enums__stock_receipt_display_status__detailsRequired": "Detalles pendientes", "@sage/xtrem-stock/enums__stock_receipt_display_status__received": "Recibida", "@sage/xtrem-stock/enums__stock_receipt_display_status__stockPostingError": "Error de contabilización de stock", "@sage/xtrem-stock/enums__stock_receipt_display_status__stockPostingInProgress": "Contabilización de stock en curso", "@sage/xtrem-stock/enums__stock_receipt_line_display_status__detailsEntered": "Detalles introducidos", "@sage/xtrem-stock/enums__stock_receipt_line_display_status__detailsRequired": "Detalles pendientes", "@sage/xtrem-stock/enums__stock_receipt_line_display_status__received": "Recibida", "@sage/xtrem-stock/enums__stock_receipt_line_display_status__stockPostingError": "Error de contabilización de stock", "@sage/xtrem-stock/enums__stock_receipt_line_display_status__stockPostingInProgress": "Contabilización de stock en curso", "@sage/xtrem-stock/enums__stock_valuation_status__completed": "Finalizada", "@sage/xtrem-stock/enums__stock_valuation_status__draft": "<PERSON><PERSON><PERSON>", "@sage/xtrem-stock/enums__stock_valuation_status__error": "Error", "@sage/xtrem-stock/enums__stock_valuation_status__inProgress": "En curso", "@sage/xtrem-stock/enums__virtual_location_need__transfer": "Transferencia", "@sage/xtrem-stock/events/control__location_for_wrong_site": "La ubicación debe pertenecer a la planta {{site}}.", "@sage/xtrem-stock/events/control__location_missing": "Introduce la ubicación.", "@sage/xtrem-stock/events/control__location_not_managed": "En la planta {{site}} no se gestionan ubicaciones.", "@sage/xtrem-stock/function__stock_count_lib__line_already_exists_in_a_not_closed_stock-count": "La línea ya existe en el inventario {{number}}.", "@sage/xtrem-stock/functions__lot-lib__no_lot_information_created": "No se ha creado ninguna información de lote.", "@sage/xtrem-stock/functions__lot-lib__no_lot_information_received": "No se ha recibido ninguna información de lote.", "@sage/xtrem-stock/functions__lot-lib__no_lot_sequence_number_definied": "No se ha definido ningún número de secuencia de lote para el artículo {{item}}.", "@sage/xtrem-stock/functions__stock__already_allocated": "El stock ya está asignado por completo.", "@sage/xtrem-stock/functions__stock__location_mandatory_no_default": "La ubicación es obligatoria y no se ha definido ninguna ubicación por defecto para esta planta de stock.", "@sage/xtrem-stock/functions__stock__lot_expired": "El artículo se gestiona por lotes. La fecha de caducidad debe ser posterior o igual a {{date}}.", "@sage/xtrem-stock/functions__stock__not_enough_stock": "No hay stock suficiente en la planta {{site}}.", "@sage/xtrem-stock/functions__stock__potency_greater_than_100": "La concentración no puede ser superior a 100.", "@sage/xtrem-stock/functions__stock__status_filter_part_1": "{{#if is<PERSON>irst}} o para {{/if}}", "@sage/xtrem-stock/functions__stock__status_filter_part_2": "{{#if notFirst}} o {{/if}} tipo de estado {{stockStatusType}}{{#if isLast}}.{{/if}}", "@sage/xtrem-stock/functions__stock__status_filter_part_3": "{{#if notFirst}} o {{/if}} estado {{stockStatus}}{{#if isLast}}.{{/if}}", "@sage/xtrem-stock/functions__stock__stock_change_quantity_must_be_greater_thant_0": "No puedes crear un cambio de stock cuya cantidad sea 0.", "@sage/xtrem-stock/functions__stock__stock_record_not_found": "No se ha encontrado el registro de stock. La cantidad no se puede utilizar.", "@sage/xtrem-stock/functions__stock__stock_record_not_found_during_stock_change": "Los detalles de stock introducidos no existen.", "@sage/xtrem-stock/functions__stock__stock_status_mandatory_no_default": "El estado de stock es obligatorio y no se ha definido ningún estado de stock por defecto para esta planta de stock.", "@sage/xtrem-stock/functions__stock__stock_update_failed": "Ha habido un error al actualizar el stock. Revisa los parámetros de actualización de stock.", "@sage/xtrem-stock/functions__stock_engine__no_lot_information_received": "No se ha recibido ninguna información de lote.", "@sage/xtrem-stock/functions__stock-detail-lib__item_not_managed_on_site": "Este artículo no se gestiona en la planta de recepción.", "@sage/xtrem-stock/functions__stock-engine__lot_passed_for_an_item_not_lot_manage": "El artículo {{item}} no se gestiona por lotes. Solo puedes contabilizar unos detalles de stock con información de lote si el artículo se gestiona por lotes.", "@sage/xtrem-stock/functions__stock-engine__not_enough_stock": "No hay stock suficiente. Revisa los parámetros:\nArtículo {{item}}\nPlanta {{site}}\nUnidad de stock {{stockUnit}}\nLote {{lot}}\nUbicación {{location}}\nEstado de stock {{stockStatus}}\nPropietario {{owner}}", "@sage/xtrem-stock/functions__stock-engine__not_enough_stock_allocation": "Asigna stock para contabilizar el documento.", "@sage/xtrem-stock/functions__stock-engine__not_enough_stock_details": "Definir secciones por defecto", "@sage/xtrem-stock/functions__stock-engine__serial_number_already_in_stock": "Uno de los números de serie del artículo {{item}} ya está en stock. Revísalos.", "@sage/xtrem-stock/functions__stock-engine__stock_update_failed_with_parameters": "Ha habido un error al actualizar el stock. Revisa los parámetros:\nArtículo {{item}}\nPlanta {{site}}\nUnidad de stock {{stockUnit}}\nLote {{lot}}\nUbicación {{location}}\nEstado de stock {{stockStatus}}\nPropietario {{owner}}", "@sage/xtrem-stock/functions__stock-engine__stock_value_update_failed_with_parameters": "Ha habido un error al actualizar el valor del stock. Revisa los parámetros:\nArtículo {{item}}\nPlanta {{site}}\nCantidad {{quantity}}", "@sage/xtrem-stock/functions--allocation-lib--allocate--not-enough-stock": "La asignación de stock solicitada no se ha procesado porque es superior a la cantidad disponible en unidad de stock.", "@sage/xtrem-stock/functions--allocation-lib--allocate--stock-not-in-status-accepted": "La asignación de stock solicitada no se ha procesado porque el estado del stock seleccionado no es '{{acceptedStatus}}'.", "@sage/xtrem-stock/functions-stock-lib-no-item-site-supplier": "No se ha asignado ningún proveedor a la planta {{currentSite}} ni al artículo {{currentItem}}.", "@sage/xtrem-stock/functions-stock-lib-stock-journal-no-item-site-record": "El artículo {{currentItem}} no se gestiona para la planta {{currentSite}}.", "@sage/xtrem-stock/functions-stock-no-stock-available": "No hay stock del artículo {{item}} disponible para la planta {{site}}.", "@sage/xtrem-stock/node__stock__stock_status_incorrect_type": "El estado de stock debe ser una instancia del nodo StockStatus o una enumeración StockStatusType.", "@sage/xtrem-stock/node__stock_count__resend_notification_for_finance": "Reenvío de la notificación de contabilidad para el inventario {{stockCount}}", "@sage/xtrem-stock/node__stock_issue__resend_notification_for_finance": "Reenvío de la notificación de contabilidad para la salida de stock {{stockIssueNumber}}", "@sage/xtrem-stock/node__stock_receipt__resend_notification_for_finance": "Reenvío de la notificación de contabilidad para la entrada de stock {{stockNumber}}", "@sage/xtrem-stock/node__stock_value_change__resend_notification_for_finance": "Reenvío de la notificación de contabilidad para el cambio de valor de stock {{stockValueChange}}", "@sage/xtrem-stock/node-extensions__component_extension__property__availableQuantityInStockUnit": "Cantidad disponible en unidad de stock", "@sage/xtrem-stock/node-extensions__component_extension__property__hasStockShortage": "Ruptura de stock", "@sage/xtrem-stock/node-extensions__component_extension__property__stockOnHand": "Stock físico", "@sage/xtrem-stock/node-extensions__component_extension__property__stockShortageInStockUnit": "Ruptura de stock en unidad de stock", "@sage/xtrem-stock/node-extensions__item_extension__asyncMutation__createTestItems": "<PERSON><PERSON><PERSON>í<PERSON><PERSON>", "@sage/xtrem-stock/node-extensions__item_extension__asyncMutation__createTestItems__failed": "Error al crear los artículos de prueba", "@sage/xtrem-stock/node-extensions__item_extension__asyncMutation__createTestItems__parameter__itemId": "Id<PERSON> <PERSON> artí<PERSON>lo", "@sage/xtrem-stock/node-extensions__item_extension__asyncMutation__createTestItems__parameter__quantity": "Cantidad", "@sage/xtrem-stock/node-extensions__item_extension__asyncMutation__createTestItems__parameter__stockCreation": "Creación de stock", "@sage/xtrem-stock/node-extensions__item_site_cost_extension__asyncMutation__standardCostRollUpCalculation": "Cálculo de coste estándar", "@sage/xtrem-stock/node-extensions__item_site_cost_extension__asyncMutation__standardCostRollUpCalculation__failed": "Error al calcular el coste estándar", "@sage/xtrem-stock/node-extensions__item_site_cost_extension__asyncMutation__standardCostRollUpCalculation__parameter__inputSet": "Conjunto de entrada", "@sage/xtrem-stock/node-extensions__item_site_cost_extension__asyncMutation__syncStockValueChange": "Sincronizar cambio de valor de stock", "@sage/xtrem-stock/node-extensions__item_site_cost_extension__asyncMutation__syncStockValueChange__failed": "Error al sincronizar el cambio de valor de stock", "@sage/xtrem-stock/node-extensions__item_site_cost_extension__error_message": "Artí<PERSON>lo {{itemId}} y planta {{siteId}}: {{errorMessage}}", "@sage/xtrem-stock/node-extensions__item_site_cost_extension__item_site_cost_creation_finished": "La creación del coste de artículo-planta ha finalizado.", "@sage/xtrem-stock/node-extensions__item_site_cost_extension__no_line_processed_message": "Error al procesar las líneas. No se ha realizado ninguna selección.", "@sage/xtrem-stock/node-extensions__item_site_cost_extension__property__analyticalData": "Datos analític<PERSON>", "@sage/xtrem-stock/node-extensions__item_site_cost_extension__property__stockValueChange": "Cambio de valor de stock", "@sage/xtrem-stock/node-extensions__item_site_cost_extension__property__storedAttributes": "Atributos almacenados", "@sage/xtrem-stock/node-extensions__item_site_cost_extension__property__storedDimensions": "Secciones almacenadas", "@sage/xtrem-stock/node-extensions__item_site_cost_extension__success_cost_creation_status_message": "El coste del artículo {{itemId}} y la planta {{siteId}} se ha creado.", "@sage/xtrem-stock/node-extensions__item_site_cost_extension__success_cost_update_status_error_message": "El coste del artículo {{itemId}} y la planta {{siteId}} ya existe y no se puede actualizar.", "@sage/xtrem-stock/node-extensions__item_site_cost_extension__success_cost_update_status_message": "El coste del artículo {{itemId}} y la planta {{siteId}} se ha actualizado.", "@sage/xtrem-stock/node-extensions__item_site_extension__property__countStockRecords": "Registros de stock de inventario", "@sage/xtrem-stock/node-extensions__item_site_extension__property__hasStockRecords": "Registros de stock", "@sage/xtrem-stock/node-extensions__item_site_extension__property__lastCountDate": "<PERSON><PERSON> de último inventario", "@sage/xtrem-stock/node-extensions__item_site_extension__query__getStockValuation": "Obtener valoración de stock", "@sage/xtrem-stock/node-extensions__item_site_extension__query__getStockValuation__failed": "Error al obtener la valoración de stock", "@sage/xtrem-stock/node-extensions__item_site_extension__query__getStockValuation__parameter__searchCriteria": "Buscar criterios", "@sage/xtrem-stock/node-extensions__mrp_calculation_extension__asyncMutation__deleteOldMrpCalculations": "Eliminar cálculos MRP anteriores", "@sage/xtrem-stock/node-extensions__mrp_calculation_extension__asyncMutation__deleteOldMrpCalculations__failed": "Error al eliminar los cálculos MRP anteriores", "@sage/xtrem-stock/node-extensions__mrp_calculation_extension__asyncMutation__deleteOldMrpCalculations__parameter__numberOfDaysToKeep": "Días de conservación", "@sage/xtrem-stock/node-extensions__mrp_calculation_extension__asyncMutation__deleteOldMrpCalculations__parameter__numberOfRecordsToKeep": "Registros conservados", "@sage/xtrem-stock/node-extensions__mrp_synchronization_extension__asyncMutation__mrpSynchronizeBom": "Sincronizar estructura con MRP", "@sage/xtrem-stock/node-extensions__mrp_synchronization_extension__asyncMutation__mrpSynchronizeBom__parameter__resetSynchronization": "Restablecer sincronización", "@sage/xtrem-stock/node-extensions__mrp_synchronization_extension__asyncMutation__mrpSynchronizeBom__parameter__synchronizeDeletions": "Eliminaciones", "@sage/xtrem-stock/node-extensions__mrp_synchronization_extension__asyncMutation__mrpSynchronizeBom__parameter__type": "Tipo", "@sage/xtrem-stock/node-extensions__stock_adjustment_detail_extension__property__item": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-stock/node-extensions__stock_adjustment_detail_extension__property__location": "Ubicación", "@sage/xtrem-stock/node-extensions__stock_adjustment_detail_extension__property__owner": "Propietario", "@sage/xtrem-stock/node-extensions__stock_adjustment_detail_extension__property__site": "Planta", "@sage/xtrem-stock/node-extensions__stock_adjustment_detail_extension__property__status": "Estado", "@sage/xtrem-stock/node-extensions__stock_adjustment_detail_extension__property__stockDetailLot": "Lote de detalles de stock", "@sage/xtrem-stock/node-extensions__stock_adjustment_detail_extension__property__stockUnit": "Unidad de stock", "@sage/xtrem-stock/node-extensions__stock_change_detail_extension__property__startingSerialNumber": "Número de serie de inicio", "@sage/xtrem-stock/node-extensions__stock_detail_lot_extension__property__lot": "Lote", "@sage/xtrem-stock/node-extensions__stock_detail_lot_extension__property__lotNumber": "<PERSON><PERSON><PERSON><PERSON>e", "@sage/xtrem-stock/node-extensions__stock_extension__asyncMutation__adjust": "Regularizar", "@sage/xtrem-stock/node-extensions__stock_extension__asyncMutation__adjust__failed": "Error de regularización", "@sage/xtrem-stock/node-extensions__stock_extension__asyncMutation__adjust__parameter__payload": "Payload", "@sage/xtrem-stock/node-extensions__stock_extension__asyncMutation__change": "Cambiar", "@sage/xtrem-stock/node-extensions__stock_extension__asyncMutation__change__failed": "<PERSON><PERSON>r de cambio", "@sage/xtrem-stock/node-extensions__stock_extension__asyncMutation__change__parameter__payload": "Payload", "@sage/xtrem-stock/node-extensions__stock_extension__asyncMutation__changeValue": "Cambiar valor", "@sage/xtrem-stock/node-extensions__stock_extension__asyncMutation__changeValue__failed": "Error al cambiar el valor", "@sage/xtrem-stock/node-extensions__stock_extension__asyncMutation__changeValue__parameter__payload": "Payload", "@sage/xtrem-stock/node-extensions__stock_extension__asyncMutation__correct": "Rectificar", "@sage/xtrem-stock/node-extensions__stock_extension__asyncMutation__correct__failed": "Error de rectificación", "@sage/xtrem-stock/node-extensions__stock_extension__asyncMutation__correct__parameter__payload": "Payload", "@sage/xtrem-stock/node-extensions__stock_extension__asyncMutation__issue": "Salir", "@sage/xtrem-stock/node-extensions__stock_extension__asyncMutation__issue__failed": "<PERSON><PERSON><PERSON> de <PERSON>", "@sage/xtrem-stock/node-extensions__stock_extension__asyncMutation__issue__parameter__payload": "Payload", "@sage/xtrem-stock/node-extensions__stock_extension__asyncMutation__receive": "Recibir", "@sage/xtrem-stock/node-extensions__stock_extension__asyncMutation__receive__failed": "Error de entrada", "@sage/xtrem-stock/node-extensions__stock_extension__asyncMutation__receive__parameter__payload": "Payload", "@sage/xtrem-stock/node-extensions__stock_extension__asyncMutation__transfer": "Transferir", "@sage/xtrem-stock/node-extensions__stock_extension__asyncMutation__transfer__failed": "Error de transferencia", "@sage/xtrem-stock/node-extensions__stock_extension__asyncMutation__transfer__parameter__payload": "Payload", "@sage/xtrem-stock/node-extensions__stock_extension__property__isStockCountingInProgress": "Inventario en curso", "@sage/xtrem-stock/node-extensions__stock_extension__property__stockCountingInProgress": "Inventario en curso", "@sage/xtrem-stock/node-extensions__stock_issue_detail_extension__property__startingSerialNumber": "Número de serie de inicio", "@sage/xtrem-stock/nodes__allocation_listener__asyncMutation__allocate": "<PERSON><PERSON><PERSON>", "@sage/xtrem-stock/nodes__allocation_listener__asyncMutation__allocate__failed": "Error de asignación", "@sage/xtrem-stock/nodes__allocation_listener__asyncMutation__allocate__parameter__payload": "Payload", "@sage/xtrem-stock/nodes__allocation_listener__asyncMutation__allocate__parameter__stockAllocationParameters": "Configuración de asignación de stock", "@sage/xtrem-stock/nodes__allocation_listener__asyncMutation__deallocate": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-stock/nodes__allocation_listener__asyncMutation__deallocate__parameter__payload": "Payload", "@sage/xtrem-stock/nodes__allocation_listener__asyncMutation__deallocate__parameter__stockAllocationParameters": "Configuración de asignación de stock", "@sage/xtrem-stock/nodes__allocation_listener__node_name": "Proceso de escucha de asignación", "@sage/xtrem-stock/nodes__cost_roll_up_input_set__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-stock/nodes__cost_roll_up_input_set__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-stock/nodes__cost_roll_up_input_set__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-stock/nodes__cost_roll_up_input_set__mutation__updateAttributesAndDimensions": "Actualizar atributos y secciones", "@sage/xtrem-stock/nodes__cost_roll_up_input_set__mutation__updateAttributesAndDimensions__failed": "Error al actualizar los atributos y las secciones", "@sage/xtrem-stock/nodes__cost_roll_up_input_set__mutation__updateAttributesAndDimensions__parameter__costRollUpInputSet": "Conjunto de entrada de cálculo de costes", "@sage/xtrem-stock/nodes__cost_roll_up_input_set__mutation__updateAttributesAndDimensions__parameter__storedAttributes": "Atributos almacenados", "@sage/xtrem-stock/nodes__cost_roll_up_input_set__mutation__updateAttributesAndDimensions__parameter__storedDimensions": "Secciones almacenadas", "@sage/xtrem-stock/nodes__cost_roll_up_input_set__node_name": "Conjunto de entrada de cálculo de costes", "@sage/xtrem-stock/nodes__cost_roll_up_input_set__property__commodityCode": "Código de mercancías", "@sage/xtrem-stock/nodes__cost_roll_up_input_set__property__costCategory": "Categoría de coste", "@sage/xtrem-stock/nodes__cost_roll_up_input_set__property__fromDate": "Fecha de inicio", "@sage/xtrem-stock/nodes__cost_roll_up_input_set__property__includesRouting": "Con ruta", "@sage/xtrem-stock/nodes__cost_roll_up_input_set__property__itemCategory": "Categoría de artículo", "@sage/xtrem-stock/nodes__cost_roll_up_input_set__property__items": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-stock/nodes__cost_roll_up_input_set__property__quantity": "Cantidad", "@sage/xtrem-stock/nodes__cost_roll_up_input_set__property__resultLines": "Líneas de resultados", "@sage/xtrem-stock/nodes__cost_roll_up_input_set__property__site": "Planta", "@sage/xtrem-stock/nodes__cost_roll_up_input_set__property__status": "Estado", "@sage/xtrem-stock/nodes__cost_roll_up_input_set__property__user": "Usuario", "@sage/xtrem-stock/nodes__cost_roll_up_input_set__property__usesComponentStandardCost": "Coste estándar de componente", "@sage/xtrem-stock/nodes__cost_roll_up_result_line__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-stock/nodes__cost_roll_up_result_line__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-stock/nodes__cost_roll_up_result_line__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-stock/nodes__cost_roll_up_result_line__bulkMutation__createItemSiteCostsFromCostRollUpResults": "Crear costes de artículo-planta a partir del cálculo de costes", "@sage/xtrem-stock/nodes__cost_roll_up_result_line__bulkMutation__createItemSiteCostsFromCostRollUpResults__failed": "Error al crear los costes del artículo-planta a partir del cálculo de costes", "@sage/xtrem-stock/nodes__cost_roll_up_result_line__item_site_cost_creation_finished": "La creación del coste de artículo-planta ha finalizado.", "@sage/xtrem-stock/nodes__cost_roll_up_result_line__no_line_processed_message": "Error al procesar las líneas. No se ha realizado ninguna selección.", "@sage/xtrem-stock/nodes__cost_roll_up_result_line__node_name": "Línea de resultado de cálculo de costes", "@sage/xtrem-stock/nodes__cost_roll_up_result_line__property__analyticalData": "Datos analític<PERSON>", "@sage/xtrem-stock/nodes__cost_roll_up_result_line__property__creationErrorMessage": "Mensaje de error de creación", "@sage/xtrem-stock/nodes__cost_roll_up_result_line__property__creationStatus": "Estado de creación", "@sage/xtrem-stock/nodes__cost_roll_up_result_line__property__currentLaborCost": "Coste de mano de obra actual", "@sage/xtrem-stock/nodes__cost_roll_up_result_line__property__currentMachineCost": "Coste de máquina actual", "@sage/xtrem-stock/nodes__cost_roll_up_result_line__property__currentMaterialCost": "Coste de material actual", "@sage/xtrem-stock/nodes__cost_roll_up_result_line__property__currentToolCost": "Coste de herramienta actual", "@sage/xtrem-stock/nodes__cost_roll_up_result_line__property__currentTotalCost": "Coste total actual", "@sage/xtrem-stock/nodes__cost_roll_up_result_line__property__inputSet": "Conjunto de entrada", "@sage/xtrem-stock/nodes__cost_roll_up_result_line__property__item": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-stock/nodes__cost_roll_up_result_line__property__laborCost": "Coste de mano de obra", "@sage/xtrem-stock/nodes__cost_roll_up_result_line__property__machineCost": "Coste de m<PERSON>", "@sage/xtrem-stock/nodes__cost_roll_up_result_line__property__materialCost": "Coste de material", "@sage/xtrem-stock/nodes__cost_roll_up_result_line__property__storedAttributes": "Atributos almacenados", "@sage/xtrem-stock/nodes__cost_roll_up_result_line__property__storedDimensions": "Secciones almacenadas", "@sage/xtrem-stock/nodes__cost_roll_up_result_line__property__subAssemblyLines": "Líneas de subconjunto", "@sage/xtrem-stock/nodes__cost_roll_up_result_line__property__toolCost": "Coste de <PERSON>", "@sage/xtrem-stock/nodes__cost_roll_up_result_line__property__totalCost": "Coste total", "@sage/xtrem-stock/nodes__cost_roll_up_sub_assembly__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-stock/nodes__cost_roll_up_sub_assembly__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-stock/nodes__cost_roll_up_sub_assembly__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-stock/nodes__cost_roll_up_sub_assembly__node_name": "Subconjunto de cálculo de costes", "@sage/xtrem-stock/nodes__cost_roll_up_sub_assembly__property__calculationQuantity": "Cantidad de cálculo", "@sage/xtrem-stock/nodes__cost_roll_up_sub_assembly__property__componentNumber": "Número de componente", "@sage/xtrem-stock/nodes__cost_roll_up_sub_assembly__property__currentLaborCost": "Coste de mano de obra actual", "@sage/xtrem-stock/nodes__cost_roll_up_sub_assembly__property__currentMachineCost": "Coste de máquina actual", "@sage/xtrem-stock/nodes__cost_roll_up_sub_assembly__property__currentMaterialCost": "Coste de material actual", "@sage/xtrem-stock/nodes__cost_roll_up_sub_assembly__property__currentToolCost": "Coste de herramienta actual", "@sage/xtrem-stock/nodes__cost_roll_up_sub_assembly__property__currentTotalCost": "Coste total actual", "@sage/xtrem-stock/nodes__cost_roll_up_sub_assembly__property__item": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-stock/nodes__cost_roll_up_sub_assembly__property__laborCost": "Coste de mano de obra", "@sage/xtrem-stock/nodes__cost_roll_up_sub_assembly__property__machineCost": "Coste de m<PERSON>", "@sage/xtrem-stock/nodes__cost_roll_up_sub_assembly__property__materialCost": "Coste de material", "@sage/xtrem-stock/nodes__cost_roll_up_sub_assembly__property__resultLine": "Línea de resultados", "@sage/xtrem-stock/nodes__cost_roll_up_sub_assembly__property__stockUnit": "", "@sage/xtrem-stock/nodes__cost_roll_up_sub_assembly__property__toolCost": "Coste de <PERSON>", "@sage/xtrem-stock/nodes__cost_roll_up_sub_assembly__property__totalCost": "Coste total", "@sage/xtrem-stock/nodes__item_site_cost__success_notification_title": "Cálculo de coste estándar realizado", "@sage/xtrem-stock/nodes__item-site-cost__failed_deletion_impossible_if_transactions": "No se puede eliminar. La fecha de inicio del coste de artículo-planta es igual a la fecha actual y este coste tiene un cambio de valor de stock.", "@sage/xtrem-stock/nodes__item-site-extension__item_site_not_stock_managed": "El artículo-planta no se gestiona en stock.", "@sage/xtrem-stock/nodes__mrp_calculation-extension__delete_old_mrp_calculations": "<PERSON><PERSON><PERSON><PERSON>los MRP anteriores eliminados: {{number}}", "@sage/xtrem-stock/nodes__mrp_calculation-extension__deleting_old_mrp_calculations": "Se van a eliminar los cálculos MRP con una fecha anterior a {{deletionDate}}.", "@sage/xtrem-stock/nodes__mrp_calculation-extension__no_old_mrp_calculations": "No hay cálculos MRP anteriores que eliminar.", "@sage/xtrem-stock/nodes__mrp_calculation-extension__start_delete_old_mrp_calculations": "La eliminación de los cálculos MRP anteriores se ha iniciado.", "@sage/xtrem-stock/nodes__stock_adjustment__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-stock/nodes__stock_adjustment__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-stock/nodes__stock_adjustment__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-stock/nodes__stock_adjustment__mutation__postToStock": "Contabilizar en stock", "@sage/xtrem-stock/nodes__stock_adjustment__mutation__postToStock__failed": "Error al contabilizar en stock", "@sage/xtrem-stock/nodes__stock_adjustment__mutation__postToStock__parameter__documentIds": "Ids. de documentos", "@sage/xtrem-stock/nodes__stock_adjustment__mutation__repost": "Volver a contabilizar", "@sage/xtrem-stock/nodes__stock_adjustment__mutation__repost__failed": "Error al volver a contabilizar", "@sage/xtrem-stock/nodes__stock_adjustment__mutation__repost__parameter__documentLines": "Líneas de documento", "@sage/xtrem-stock/nodes__stock_adjustment__mutation__repost__parameter__stockAdjustment": "Regularización de stock", "@sage/xtrem-stock/nodes__stock_adjustment__mutation__resendNotificationForFinance": "Reenviar notificación para contabilidad", "@sage/xtrem-stock/nodes__stock_adjustment__mutation__resendNotificationForFinance__failed": "Error al reenviar la notificación para la contabilidad", "@sage/xtrem-stock/nodes__stock_adjustment__mutation__resendNotificationForFinance__parameter__stockAdjustment": "Regularización de stock", "@sage/xtrem-stock/nodes__stock_adjustment__mutation__resynchronizeStockTransactionStatus": "Resincronizar estado de transacción de stock", "@sage/xtrem-stock/nodes__stock_adjustment__mutation__resynchronizeStockTransactionStatus__failed": "Error al resincronizar el estado de la transacción de stock", "@sage/xtrem-stock/nodes__stock_adjustment__mutation__resynchronizeStockTransactionStatus__parameter__stockAdjustment": "Regularización de stock", "@sage/xtrem-stock/nodes__stock_adjustment__node_name": "Regularización de stock", "@sage/xtrem-stock/nodes__stock_adjustment__property__description": "Descripción", "@sage/xtrem-stock/nodes__stock_adjustment__property__displayStatus": "Estado", "@sage/xtrem-stock/nodes__stock_adjustment__property__documentDate": "Fecha de documento", "@sage/xtrem-stock/nodes__stock_adjustment__property__effectiveDate": "Fecha efectiva", "@sage/xtrem-stock/nodes__stock_adjustment__property__financeIntegrationStatus": "Estado de integración contable", "@sage/xtrem-stock/nodes__stock_adjustment__property__financialSite": "Planta financiera", "@sage/xtrem-stock/nodes__stock_adjustment__property__forceUpdateForFinance": "Forzar actualización de contabilidad", "@sage/xtrem-stock/nodes__stock_adjustment__property__forceUpdateForStock": "Forzar actualización de Stock", "@sage/xtrem-stock/nodes__stock_adjustment__property__isSetDimensionsMainListHidden": "Lista principal de definición de secciones oculta", "@sage/xtrem-stock/nodes__stock_adjustment__property__lines": "Líneas", "@sage/xtrem-stock/nodes__stock_adjustment__property__number": "Número", "@sage/xtrem-stock/nodes__stock_adjustment__property__postingDetails": "Detalles de contabilización", "@sage/xtrem-stock/nodes__stock_adjustment__property__reasonCode": "Código de motivo", "@sage/xtrem-stock/nodes__stock_adjustment__property__status": "Estado", "@sage/xtrem-stock/nodes__stock_adjustment__property__stockSite": "Planta de stock", "@sage/xtrem-stock/nodes__stock_adjustment__property__stockTransactionStatus": "Estado de transacción de stock", "@sage/xtrem-stock/nodes__stock_adjustment__property__transactionCurrency": "Divisa de transacción", "@sage/xtrem-stock/nodes__stock_adjustment_detail_extension__lot_mandatory": "Selecciona un lote del artículo {{itemId}} en la línea {{lineNumber}} del inventario {{number}}.", "@sage/xtrem-stock/nodes__stock_adjustment_line__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-stock/nodes__stock_adjustment_line__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-stock/nodes__stock_adjustment_line__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-stock/nodes__stock_adjustment_line__cannot_update_completed_line": "No puedes actualizar una línea regularizada.", "@sage/xtrem-stock/nodes__stock_adjustment_line__incorrect_valuation_method": "Vincula el artículo {{item}} al método de valoración de precio medio ponderado.", "@sage/xtrem-stock/nodes__stock_adjustment_line__mutation__setDimension": "Definir secci<PERSON>", "@sage/xtrem-stock/nodes__stock_adjustment_line__mutation__setDimension__failed": "Error al definir la sección", "@sage/xtrem-stock/nodes__stock_adjustment_line__mutation__setDimension__parameter__stockAdjustmentLine": "Línea de regularización de stock", "@sage/xtrem-stock/nodes__stock_adjustment_line__mutation__setDimension__parameter__storedAttributes": "Atributos almacenados", "@sage/xtrem-stock/nodes__stock_adjustment_line__mutation__setDimension__parameter__storedDimensions": "Secciones almacenadas", "@sage/xtrem-stock/nodes__stock_adjustment_line__node_name": "Línea de regularización de stock", "@sage/xtrem-stock/nodes__stock_adjustment_line__property__adjustmentQuantityInStockUnit": "Cantidad de regularización en unidad de stock", "@sage/xtrem-stock/nodes__stock_adjustment_line__property__analyticalData": "Datos analític<PERSON>", "@sage/xtrem-stock/nodes__stock_adjustment_line__property__computedAttributes": "Atributos calculados", "@sage/xtrem-stock/nodes__stock_adjustment_line__property__displayStatus": "Estado", "@sage/xtrem-stock/nodes__stock_adjustment_line__property__document": "Documento", "@sage/xtrem-stock/nodes__stock_adjustment_line__property__documentId": "Id. de documento", "@sage/xtrem-stock/nodes__stock_adjustment_line__property__documentNumber": "Número de documento", "@sage/xtrem-stock/nodes__stock_adjustment_line__property__item": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-stock/nodes__stock_adjustment_line__property__itemSite": "Artículo-planta", "@sage/xtrem-stock/nodes__stock_adjustment_line__property__jsonStockDetails": "Detalles de stock en formato JSON", "@sage/xtrem-stock/nodes__stock_adjustment_line__property__location": "Ubicación", "@sage/xtrem-stock/nodes__stock_adjustment_line__property__lot": "Lote", "@sage/xtrem-stock/nodes__stock_adjustment_line__property__newStockQuantity": "Nueva cantidad de stock", "@sage/xtrem-stock/nodes__stock_adjustment_line__property__newStockValue": "Nuevo valor de stock", "@sage/xtrem-stock/nodes__stock_adjustment_line__property__newUnitCost": "Nuevo coste unitario", "@sage/xtrem-stock/nodes__stock_adjustment_line__property__orderCost": "Coste de pedido", "@sage/xtrem-stock/nodes__stock_adjustment_line__property__quantityInStockUnit": "Cantidad en unidad de stock", "@sage/xtrem-stock/nodes__stock_adjustment_line__property__site": "Planta", "@sage/xtrem-stock/nodes__stock_adjustment_line__property__stockDetails": "Detalles de stock", "@sage/xtrem-stock/nodes__stock_adjustment_line__property__stockDetailStatus": "Estado de detalles de stock", "@sage/xtrem-stock/nodes__stock_adjustment_line__property__stockMovements": "Movimientos de stock", "@sage/xtrem-stock/nodes__stock_adjustment_line__property__stockStatus": "Estado de stock", "@sage/xtrem-stock/nodes__stock_adjustment_line__property__stockTransactions": "Transacción de stock", "@sage/xtrem-stock/nodes__stock_adjustment_line__property__stockTransactionStatus": "Estado de transacción de stock", "@sage/xtrem-stock/nodes__stock_adjustment_line__property__stockUnit": "Unidad de stock", "@sage/xtrem-stock/nodes__stock_adjustment_line__property__stockValue": "Valor de stock", "@sage/xtrem-stock/nodes__stock_adjustment_line__property__storedAttributes": "Atributos almacenados", "@sage/xtrem-stock/nodes__stock_adjustment_line__property__storedDimensions": "Secciones almacenadas", "@sage/xtrem-stock/nodes__stock_adjustment_line__property__unitCost": "Coste unitario", "@sage/xtrem-stock/nodes__stock_adjustment_line__property__valuedCost": "Coste valorado", "@sage/xtrem-stock/nodes__stock_change___duplicated_serial_number": "Hay números de serie duplicados. Asigna a cada línea números de serie únicos.", "@sage/xtrem-stock/nodes__stock_change__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-stock/nodes__stock_change__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-stock/nodes__stock_change__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-stock/nodes__stock_change__mutation__postToStock": "Contabilizar en stock", "@sage/xtrem-stock/nodes__stock_change__mutation__postToStock__failed": "Error al contabilizar en stock", "@sage/xtrem-stock/nodes__stock_change__mutation__postToStock__parameter__documentIds": "Ids. de documentos", "@sage/xtrem-stock/nodes__stock_change__node_name": "Cambio de stock", "@sage/xtrem-stock/nodes__stock_change__property__description": "Descripción", "@sage/xtrem-stock/nodes__stock_change__property__effectiveDate": "Fecha efectiva", "@sage/xtrem-stock/nodes__stock_change__property__forceUpdateForStock": "Forzar actualización de Stock", "@sage/xtrem-stock/nodes__stock_change__property__item": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-stock/nodes__stock_change__property__itemSite": "Artículo-planta", "@sage/xtrem-stock/nodes__stock_change__property__lines": "Líneas", "@sage/xtrem-stock/nodes__stock_change__property__location": "Ubicación", "@sage/xtrem-stock/nodes__stock_change__property__lot": "Lote", "@sage/xtrem-stock/nodes__stock_change__property__number": "Número", "@sage/xtrem-stock/nodes__stock_change__property__owner": "Propietario", "@sage/xtrem-stock/nodes__stock_change__property__postingError": "Error de contabilización", "@sage/xtrem-stock/nodes__stock_change__property__status": "Estado", "@sage/xtrem-stock/nodes__stock_change__property__stockSite": "Planta de stock", "@sage/xtrem-stock/nodes__stock_change__property__stockStatus": "Estado de stock", "@sage/xtrem-stock/nodes__stock_change__property__stockTransactionStatus": "Estado de transacción de stock", "@sage/xtrem-stock/nodes__stock_change__property__stockUnit": "Unidad de stock", "@sage/xtrem-stock/nodes__stock_change_line__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-stock/nodes__stock_change_line__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-stock/nodes__stock_change_line__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-stock/nodes__stock_change_line__location_site_must_match": "La planta de la ubicación y la planta de stock deben ser iguales.", "@sage/xtrem-stock/nodes__stock_change_line__no_change": "Hay una línea sin modificar. Cámbiale la ubicación o el estado de stock.", "@sage/xtrem-stock/nodes__stock_change_line__node_name": "Línea de cambio de stock", "@sage/xtrem-stock/nodes__stock_change_line__property__document": "Documento", "@sage/xtrem-stock/nodes__stock_change_line__property__documentId": "Id. de documento", "@sage/xtrem-stock/nodes__stock_change_line__property__documentNumber": "Número de documento", "@sage/xtrem-stock/nodes__stock_change_line__property__item": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-stock/nodes__stock_change_line__property__jsonStockDetails": "Detalles de stock en formato JSON", "@sage/xtrem-stock/nodes__stock_change_line__property__location": "Ubicación", "@sage/xtrem-stock/nodes__stock_change_line__property__orderCost": "Coste de pedido", "@sage/xtrem-stock/nodes__stock_change_line__property__quantityInStockUnit": "Cantidad en unidad de stock", "@sage/xtrem-stock/nodes__stock_change_line__property__stockDetails": "Detalles de stock", "@sage/xtrem-stock/nodes__stock_change_line__property__stockStatus": "Estado de stock", "@sage/xtrem-stock/nodes__stock_change_line__property__stockTransactions": "Transacción de stock", "@sage/xtrem-stock/nodes__stock_change_line__property__stockTransactionStatus": "Estado de transacción de stock", "@sage/xtrem-stock/nodes__stock_change_line__property__valuedCost": "Coste valorado", "@sage/xtrem-stock/nodes__stock_count__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-stock/nodes__stock_count__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-stock/nodes__stock_count__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-stock/nodes__stock_count__asyncMutation__confirmStockCount": "Confirmar inventario", "@sage/xtrem-stock/nodes__stock_count__asyncMutation__confirmStockCount__failed": "Error al confirmar el inventario", "@sage/xtrem-stock/nodes__stock_count__asyncMutation__confirmStockCount__parameter__selectedRecords": "Registros seleccionados", "@sage/xtrem-stock/nodes__stock_count__asyncMutation__confirmStockCount__parameter__stockCount": "Inventario", "@sage/xtrem-stock/nodes__stock_count__asyncMutation__confirmZeroQuantity": "Confirmar cantidad cero", "@sage/xtrem-stock/nodes__stock_count__asyncMutation__confirmZeroQuantity__failed": "Error al confirmar la cantidad cero", "@sage/xtrem-stock/nodes__stock_count__asyncMutation__confirmZeroQuantity__parameter__number": "Número", "@sage/xtrem-stock/nodes__stock_count__cant_repost_stock_count_when_status_is_not_failed": "Solo puedes volver a contabilizar un inventario con errores o sin registrar.", "@sage/xtrem-stock/nodes__stock_count__closed_confirm_zero_cannot_be_performed": "El inventario {{number}} está cerrado. No puedes actualizar las cantidades.", "@sage/xtrem-stock/nodes__stock_count__company_has_no_stock_posting": "No se ha contabilizado ningún registro de stock en esta sociedad.", "@sage/xtrem-stock/nodes__stock_count__confirm_stock_completed": "Líneas de inventario creadas: {{number}}", "@sage/xtrem-stock/nodes__stock_count__confirm_stock_count_none_stock_records_to_stock_count_line": "Líneas de inventario creadas desde registros sin stock: {{itemSiteCounter}}", "@sage/xtrem-stock/nodes__stock_count__confirm_stock_count_stock_record_excluded": "El registro de stock del artículo {{item}} se ha excluido porque este ya está incluido en el inventario {{existingRecord}}.", "@sage/xtrem-stock/nodes__stock_count__confirm_stock_count_stock_records_to_stock_count_line": "Líneas de inventario creadas desde registros de stock: {{stockCounter}}", "@sage/xtrem-stock/nodes__stock_count__counted_quantity_less_than_allocated_quantity": "En el inventario {{number}}, la cantidad contada ({{countedQuantity}}) del artículo {{item}} no puede ser inferior a la cantidad asignada ({{totalAllocated}}).", "@sage/xtrem-stock/nodes__stock_count__counted_stock_record_not_found": "El registro de inventario del artículo {{item}} ha cambiado desde que se inició el proceso. Excluye esta línea del inventario.", "@sage/xtrem-stock/nodes__stock_count__counting_is_not_finished_yet": "No puedes contabilizar el inventario {{number}} porque todavía no ha finalizado.", "@sage/xtrem-stock/nodes__stock_count__deletion_forbidden": "No puedes eliminar este inventario.", "@sage/xtrem-stock/nodes__stock_count__document_was_posted": "El inventario se ha contabilizado.", "@sage/xtrem-stock/nodes__stock_count__end_confirm_zero": "La confirmación de cantidad cero en el inventario {{number}} ha finalizado. Líneas actualizadas: {{recordsUpdated}}.", "@sage/xtrem-stock/nodes__stock_count__mutation__postToStock": "Contabilizar en stock", "@sage/xtrem-stock/nodes__stock_count__mutation__postToStock__failed": "Error al contabilizar en stock", "@sage/xtrem-stock/nodes__stock_count__mutation__postToStock__parameter__documentIds": "Ids. de documentos", "@sage/xtrem-stock/nodes__stock_count__mutation__repost": "Volver a contabilizar", "@sage/xtrem-stock/nodes__stock_count__mutation__repost__failed": "Error al volver a contabilizar", "@sage/xtrem-stock/nodes__stock_count__mutation__repost__parameter__documentLines": "Líneas de documento", "@sage/xtrem-stock/nodes__stock_count__mutation__repost__parameter__stockCount": "Inventario", "@sage/xtrem-stock/nodes__stock_count__mutation__resendNotificationForFinance": "Reenviar notificación para contabilidad", "@sage/xtrem-stock/nodes__stock_count__mutation__resendNotificationForFinance__failed": "Error al reenviar la notificación para la contabilidad", "@sage/xtrem-stock/nodes__stock_count__mutation__resendNotificationForFinance__parameter__stockCount": "Inventario", "@sage/xtrem-stock/nodes__stock_count__mutation__start": "Iniciar", "@sage/xtrem-stock/nodes__stock_count__mutation__start__failed": "Error de inicio", "@sage/xtrem-stock/nodes__stock_count__mutation__start__parameter__document": "Documento", "@sage/xtrem-stock/nodes__stock_count__no_records_selected": "No se ha seleccionado ningún registro para el inventario {{number}}.", "@sage/xtrem-stock/nodes__stock_count__node_name": "Inventario", "@sage/xtrem-stock/nodes__stock_count__property__categories": "Categorías", "@sage/xtrem-stock/nodes__stock_count__property__concurrentStockCountLines": "Líneas de inventario en curso", "@sage/xtrem-stock/nodes__stock_count__property__counter": "<PERSON><PERSON><PERSON>", "@sage/xtrem-stock/nodes__stock_count__property__description": "Descripción", "@sage/xtrem-stock/nodes__stock_count__property__documentDate": "Fecha de documento", "@sage/xtrem-stock/nodes__stock_count__property__effectiveDate": "Fecha efectiva", "@sage/xtrem-stock/nodes__stock_count__property__financeIntegrationStatus": "Estado de integración contable", "@sage/xtrem-stock/nodes__stock_count__property__financialSite": "Planta financiera", "@sage/xtrem-stock/nodes__stock_count__property__forceUpdateForStock": "Forzar actualización de Stock", "@sage/xtrem-stock/nodes__stock_count__property__fromItem": "<PERSON><PERSON>", "@sage/xtrem-stock/nodes__stock_count__property__hasExpiryManagementInLines": "Gestión de caducidad en líneas", "@sage/xtrem-stock/nodes__stock_count__property__hasLotInLines": "Lote en líneas", "@sage/xtrem-stock/nodes__stock_count__property__hasStockRecords": "Registros de stock", "@sage/xtrem-stock/nodes__stock_count__property__hasSublotInLines": "Sublote en líneas", "@sage/xtrem-stock/nodes__stock_count__property__itemSites": "Artículos-plantas", "@sage/xtrem-stock/nodes__stock_count__property__lastCountDate": "<PERSON><PERSON> de último inventario", "@sage/xtrem-stock/nodes__stock_count__property__lines": "Líneas", "@sage/xtrem-stock/nodes__stock_count__property__locations": "Ubicaciones", "@sage/xtrem-stock/nodes__stock_count__property__number": "Número", "@sage/xtrem-stock/nodes__stock_count__property__postingDetails": "Detalles de contabilización", "@sage/xtrem-stock/nodes__stock_count__property__status": "Estado", "@sage/xtrem-stock/nodes__stock_count__property__stockSite": "Planta de stock", "@sage/xtrem-stock/nodes__stock_count__property__stockTransactionStatus": "Estado de transacción de stock", "@sage/xtrem-stock/nodes__stock_count__property__toItem": "<PERSON><PERSON>", "@sage/xtrem-stock/nodes__stock_count__property__transactionCurrency": "Divisa de transacción", "@sage/xtrem-stock/nodes__stock_count__property__zones": "<PERSON><PERSON><PERSON>", "@sage/xtrem-stock/nodes__stock_count__query__existingStockCountLine": "Línea de inventario existente", "@sage/xtrem-stock/nodes__stock_count__query__existingStockCountLine__failed": "Error de línea de inventario existente", "@sage/xtrem-stock/nodes__stock_count__query__existingStockCountLine__parameter__lotCreateData": "Datos de creación de lote", "@sage/xtrem-stock/nodes__stock_count__query__existingStockCountLine__parameter__searchCriteria": "Buscar criterios", "@sage/xtrem-stock/nodes__stock_count__query__existingStockCountLine__parameter__throwErrorIfExist": "Generar error si se produce", "@sage/xtrem-stock/nodes__stock_count__start_confirm_stock_count": "La creación de líneas en el inventario {{number}} se ha iniciado.", "@sage/xtrem-stock/nodes__stock_count__start_confirm_zero": "La confirmación de cantidad cero en el inventario {{number}} se ha iniciado.", "@sage/xtrem-stock/nodes__stock_count__to_be_counted_confirm_zero_cannot_be_performed": "El inventario {{number}} todavía no se ha iniciado. Inícialo para poder actualizar las cantidades.", "@sage/xtrem-stock/nodes__stock_count_line___duplicated_serial_number": "Hay números de serie duplicados. Asigna a cada línea números de serie únicos.", "@sage/xtrem-stock/nodes__stock_count_line__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-stock/nodes__stock_count_line__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-stock/nodes__stock_count_line__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-stock/nodes__stock_count_line__cannot_add_line_if_counting_closed": "No puedes añadir una línea a un inventario cerrado.", "@sage/xtrem-stock/nodes__stock_count_line__delete_not_allowed": "No puedes eliminar la línea {{lineNumber}}. Se ha añadido al iniciar el inventario {{number}}.", "@sage/xtrem-stock/nodes__stock_count_line__expiration_date_mandatory": "Selecciona la fecha de caducidad del artículo {{itemId}} y el lote {{lotId}}.", "@sage/xtrem-stock/nodes__stock_count_line__forbid_addition_serialized_managed_item": "No se puede añadir una línea de stock de un artículo serializado a un inventario existente.", "@sage/xtrem-stock/nodes__stock_count_line__item_cannot_be_modified": "No puedes cambiar el artículo en la línea {{lineNumber}} del inventario {{number}}.", "@sage/xtrem-stock/nodes__stock_count_line__item_of_stock_detail_must_be_the_same_as_item_of_stock_count_line": "El artículo de los detalles de stock debe ser el mismo que el de la línea {{lineNumber}} del inventario {{number}}.", "@sage/xtrem-stock/nodes__stock_count_line__line_posted_delete_not_allowed": "La línea {{lineNumber}} del inventario {{number}} no se puede eliminar porque ya se ha contabilizado.", "@sage/xtrem-stock/nodes__stock_count_line__location_cannot_be_modified": "No puedes cambiar la ubicación en la línea {{lineNumber}} del inventario {{number}}.", "@sage/xtrem-stock/nodes__stock_count_line__location_of_stock_detail_must_be_the_same_as_location_of_stock_count_line": "La ubicación de los detalles de stock debe ser la misma que la de la línea {{lineNumber}} del inventario {{number}}.", "@sage/xtrem-stock/nodes__stock_count_line__lot_cannot_be_modified": "No puedes cambiar el lote en la línea {{lineNumber}} del inventario {{number}}.", "@sage/xtrem-stock/nodes__stock_count_line__lot_mandatory": "Selecciona un lote del artículo {{itemId}}.", "@sage/xtrem-stock/nodes__stock_count_line__node_name": "Línea de inventario", "@sage/xtrem-stock/nodes__stock_count_line__owner_cannot_be_modified": "No puedes cambiar el propietario en la línea {{lineNumber}} del inventario {{number}}.", "@sage/xtrem-stock/nodes__stock_count_line__owner_of_stock_detail_must_be_the_same_as_owner_of_stock_count_line": "El propietario de los detalles de stock debe ser el mismo que el de la línea {{lineNumber}} del inventario {{number}}.", "@sage/xtrem-stock/nodes__stock_count_line__property__adjustmentQuantityInStockUnit": "Cantidad de regularización en unidad de stock", "@sage/xtrem-stock/nodes__stock_count_line__property__allocations": "Asignaciones", "@sage/xtrem-stock/nodes__stock_count_line__property__analyticalData": "Datos analític<PERSON>", "@sage/xtrem-stock/nodes__stock_count_line__property__canBeDeleted": "Se puede eliminar", "@sage/xtrem-stock/nodes__stock_count_line__property__canEnterOrderCost": "Se puede introducir coste de pedido", "@sage/xtrem-stock/nodes__stock_count_line__property__computedAttributes": "Atributos calculados", "@sage/xtrem-stock/nodes__stock_count_line__property__countedQuantityInStockUnit": "Cantidad contada en unidad de stock", "@sage/xtrem-stock/nodes__stock_count_line__property__countedSerialNumber": "Número de serie contado", "@sage/xtrem-stock/nodes__stock_count_line__property__countedSerialNumberPercentage": "Porcentaje de número de serie contado", "@sage/xtrem-stock/nodes__stock_count_line__property__document": "Documento", "@sage/xtrem-stock/nodes__stock_count_line__property__documentId": "Id. de documento", "@sage/xtrem-stock/nodes__stock_count_line__property__documentNumber": "Número de documento", "@sage/xtrem-stock/nodes__stock_count_line__property__forceUpdateForFinance": "Forzar actualización de Contabilidad", "@sage/xtrem-stock/nodes__stock_count_line__property__forceUpdateForStock": "Forzar actualización de Stock", "@sage/xtrem-stock/nodes__stock_count_line__property__hasAllocationError": "Error de asignación", "@sage/xtrem-stock/nodes__stock_count_line__property__isAddedDuringCount": "Añadida durante inventario", "@sage/xtrem-stock/nodes__stock_count_line__property__isCreatedFromStockRecord": "Creada a partir de registro de stock", "@sage/xtrem-stock/nodes__stock_count_line__property__isNonStockItem": "Artículo sin stock", "@sage/xtrem-stock/nodes__stock_count_line__property__isStockCountCreation": "Creación de inventario", "@sage/xtrem-stock/nodes__stock_count_line__property__item": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-stock/nodes__stock_count_line__property__itemSite": "Artículo-planta", "@sage/xtrem-stock/nodes__stock_count_line__property__jsonSerialNumbers": "Números de serie JSON", "@sage/xtrem-stock/nodes__stock_count_line__property__jsonStockDetails": "Detalles de stock en formato JSON", "@sage/xtrem-stock/nodes__stock_count_line__property__location": "Ubicación", "@sage/xtrem-stock/nodes__stock_count_line__property__lot": "Lote", "@sage/xtrem-stock/nodes__stock_count_line__property__newLineOrderCost": "Nuevo coste de línea pedido", "@sage/xtrem-stock/nodes__stock_count_line__property__owner": "Propietario", "@sage/xtrem-stock/nodes__stock_count_line__property__quantityInStockUnit": "Cantidad en unidad de stock", "@sage/xtrem-stock/nodes__stock_count_line__property__quantityVariance": "Desviación de cantidad", "@sage/xtrem-stock/nodes__stock_count_line__property__quantityVariancePercentage": "Porcentaje de desviación de cantidad", "@sage/xtrem-stock/nodes__stock_count_line__property__serialNumbers": "", "@sage/xtrem-stock/nodes__stock_count_line__property__shouldEnterOrderCost": "Se debe introducir coste de pedido", "@sage/xtrem-stock/nodes__stock_count_line__property__site": "Planta", "@sage/xtrem-stock/nodes__stock_count_line__property__status": "Estado", "@sage/xtrem-stock/nodes__stock_count_line__property__stockCountLineSerialNumbers": "Números de serie de línea de inventario", "@sage/xtrem-stock/nodes__stock_count_line__property__stockDetail": "Detalles de stock", "@sage/xtrem-stock/nodes__stock_count_line__property__stockDetails": "Detalles de stock", "@sage/xtrem-stock/nodes__stock_count_line__property__stockMovements": "Movimientos de stock", "@sage/xtrem-stock/nodes__stock_count_line__property__stockRecord": "Registro de stock", "@sage/xtrem-stock/nodes__stock_count_line__property__stockStatus": "Estado de stock", "@sage/xtrem-stock/nodes__stock_count_line__property__stockTransactions": "Transacción de stock", "@sage/xtrem-stock/nodes__stock_count_line__property__stockTransactionStatus": "Estado de transacción de stock", "@sage/xtrem-stock/nodes__stock_count_line__property__stockUnit": "Unidad de stock", "@sage/xtrem-stock/nodes__stock_count_line__property__storedAttributes": "Atributos almacenados", "@sage/xtrem-stock/nodes__stock_count_line__property__storedDimensions": "Secciones almacenadas", "@sage/xtrem-stock/nodes__stock_count_line__property__totalAllocated": "Total asignado", "@sage/xtrem-stock/nodes__stock_count_line__property__zone": "Á<PERSON>", "@sage/xtrem-stock/nodes__stock_count_line__query__getStockQuantity": "Obtener cantidad de stock", "@sage/xtrem-stock/nodes__stock_count_line__query__getStockQuantity__failed": "Error al obtener la cantidad de stock", "@sage/xtrem-stock/nodes__stock_count_line__query__getStockQuantity__parameter__stockCountLine": "Línea de inventario", "@sage/xtrem-stock/nodes__stock_count_line__selected_quantity_too_high": "La cantidad de números de serie seleccionada ({{selectedQuantity}}) no puede ser superior a la cantidad contada ({{countedQuantity}}).", "@sage/xtrem-stock/nodes__stock_count_line__site_of_stock_detail_must_be_the_same_as_site_of_stock_count_line": "La planta de los detalles de stock debe ser la misma que la de la línea {{lineNumber}} del inventario {{number}}.", "@sage/xtrem-stock/nodes__stock_count_line__status_of_stock_detail_must_be_the_same_as_status_of_stock_count_line": "El estado de los detalles de stock debe ser el mismo que el de la línea {{lineNumber}} del inventario {{number}}.", "@sage/xtrem-stock/nodes__stock_count_line__stock_detail_inconsistent_with_line": "Los detalles de stock no coinciden con la línea.", "@sage/xtrem-stock/nodes__stock_count_line__stock_record_not_found": "No se ha encontrado el registro de stock para la línea {{lineNumber}} del inventario {{number}}.", "@sage/xtrem-stock/nodes__stock_count_line__stock_status_cannot_be_modified": "No puedes cambiar el estado en la línea {{lineNumber}} del inventario {{number}}.", "@sage/xtrem-stock/nodes__stock_count_line__stock_unit_of_stock_detail_must_be_the_same_as_stock_unit_of_stock_count_line": "La unidad de stock de los detalles de stock debe ser la misma que la de la línea {{lineNumber}} del inventario {{number}}.", "@sage/xtrem-stock/nodes__stock_count_line__sublot_mandatory": "Selecciona un sublote del artículo {{itemId}}.", "@sage/xtrem-stock/nodes__stock_count_line__update_forbidden_count_line_posted": "No puedes actualizar la línea de inventario {{id}} porque ya se ha contabilizado.", "@sage/xtrem-stock/nodes__stock_count_line_serial_number__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-stock/nodes__stock_count_line_serial_number__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-stock/nodes__stock_count_line_serial_number__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-stock/nodes__stock_count_line_serial_number__available_error": "Números de serie disponibles en este rango: {{available}}", "@sage/xtrem-stock/nodes__stock_count_line_serial_number__cannot_delete_count_started": "No puedes eliminar el número de serie {{serialNumber}} de la línea de inventario para el artículo {{itemId}}. El inventario ya se ha iniciado.", "@sage/xtrem-stock/nodes__stock_count_line_serial_number__cannot_delete_serial_still_in_stock": "No puedes eliminar el número de serie {{serialNumber}} de la línea de inventario para el artículo {{itemId}}. Está asociado al registro de stock contado.", "@sage/xtrem-stock/nodes__stock_count_line_serial_number__invalid_quantity_error": "El número de serie de fin no se puede calcular. Revisa el número de serie de inicio y la cantidad.", "@sage/xtrem-stock/nodes__stock_count_line_serial_number__invalid_starting_serial_number_error": "El número de serie {{serialNumber}} no está asignado a la línea de inventario.", "@sage/xtrem-stock/nodes__stock_count_line_serial_number__node_name": "Número de serie de línea de inventario", "@sage/xtrem-stock/nodes__stock_count_line_serial_number__property__isCounted": "<PERSON><PERSON><PERSON>", "@sage/xtrem-stock/nodes__stock_count_line_serial_number__property__serialNumber": "Número de serie", "@sage/xtrem-stock/nodes__stock_count_line_serial_number__property__stockCountLine": "Línea de inventario", "@sage/xtrem-stock/nodes__stock_count_line_serial_number__query__getEndingSerialNumber": "Obtener número de serie de fin", "@sage/xtrem-stock/nodes__stock_count_line_serial_number__query__getEndingSerialNumber__failed": "Error al obtener el número de serie de fin", "@sage/xtrem-stock/nodes__stock_count_line_serial_number__query__getEndingSerialNumber__parameter__quantity": "Cantidad", "@sage/xtrem-stock/nodes__stock_count_line_serial_number__query__getEndingSerialNumber__parameter__startingSerialNumber": "Número de serie de inicio", "@sage/xtrem-stock/nodes__stock_count_line_serial_number__query__getEndingSerialNumber__parameter__stockCountLineId": "Id. de línea de inventario", "@sage/xtrem-stock/nodes__stock_count_line_serial_number__serial_number_not_assigned_to_a_stock_record": "El número de serie {{serialNumber}} no está asignado a ninguna línea de stock.", "@sage/xtrem-stock/nodes__stock_count_line_serial_number__stock_count_line_and_serial_number_dont_match": "La línea de inventario y el número de serie {{serialNumber}} no tienen asignada la misma línea de stock.", "@sage/xtrem-stock/nodes__stock_count_line_serial_number__zero_quantity_error": "Introduce una cantidad superior a 0.", "@sage/xtrem-stock/nodes__stock_detail__exceeds_stock_record_quantity": "La cantidad {{qty1}} de los detalles de stock {{detailNb}} supera la cantidad del registro de stock {{qty2}}.", "@sage/xtrem-stock/nodes__stock_detail__stock_record_has_been_deleted": "No se puede realizar la salida de stock porque el registro de stock se ha eliminado con otra transacción.", "@sage/xtrem-stock/nodes__stock_issue___duplicated_serial_number": "Hay números de serie duplicados. Asigna a cada línea números de serie únicos.", "@sage/xtrem-stock/nodes__stock_issue__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-stock/nodes__stock_issue__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-stock/nodes__stock_issue__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-stock/nodes__stock_issue__mutation__postToStock": "Contabilizar en stock", "@sage/xtrem-stock/nodes__stock_issue__mutation__postToStock__failed": "Error al contabilizar en stock", "@sage/xtrem-stock/nodes__stock_issue__mutation__postToStock__parameter__documentIds": "Ids. de documentos", "@sage/xtrem-stock/nodes__stock_issue__mutation__repost": "Volver a contabilizar", "@sage/xtrem-stock/nodes__stock_issue__mutation__repost__failed": "Error al volver a contabilizar", "@sage/xtrem-stock/nodes__stock_issue__mutation__repost__parameter__documentLines": "Líneas de documento", "@sage/xtrem-stock/nodes__stock_issue__mutation__repost__parameter__stockIssue": "Salida de stock", "@sage/xtrem-stock/nodes__stock_issue__mutation__resendNotificationForFinance": "Reenviar notificación para contabilidad", "@sage/xtrem-stock/nodes__stock_issue__mutation__resendNotificationForFinance__failed": "Error al reenviar la notificación para la contabilidad", "@sage/xtrem-stock/nodes__stock_issue__mutation__resendNotificationForFinance__parameter__stockIssue": "Salida de stock", "@sage/xtrem-stock/nodes__stock_issue__node_name": "Salida de stock", "@sage/xtrem-stock/nodes__stock_issue__property__description": "Descripción", "@sage/xtrem-stock/nodes__stock_issue__property__displayStatus": "Estado", "@sage/xtrem-stock/nodes__stock_issue__property__documentDate": "Fecha de documento", "@sage/xtrem-stock/nodes__stock_issue__property__effectiveDate": "Fecha efectiva", "@sage/xtrem-stock/nodes__stock_issue__property__financeIntegrationStatus": "Estado de integración contable", "@sage/xtrem-stock/nodes__stock_issue__property__financialSite": "Planta financiera", "@sage/xtrem-stock/nodes__stock_issue__property__forceUpdateForStock": "Forzar actualización de Stock", "@sage/xtrem-stock/nodes__stock_issue__property__isSetDimensionsMainListHidden": "Lista principal de definición de secciones oculta", "@sage/xtrem-stock/nodes__stock_issue__property__lines": "Líneas", "@sage/xtrem-stock/nodes__stock_issue__property__number": "Número", "@sage/xtrem-stock/nodes__stock_issue__property__postingDetails": "Detalles de contabilización", "@sage/xtrem-stock/nodes__stock_issue__property__reasonCode": "Código de motivo", "@sage/xtrem-stock/nodes__stock_issue__property__status": "Estado", "@sage/xtrem-stock/nodes__stock_issue__property__stockSite": "Planta de stock", "@sage/xtrem-stock/nodes__stock_issue__property__stockTransactionStatus": "Estado de transacción de stock", "@sage/xtrem-stock/nodes__stock_issue__property__transactionCurrency": "Divisa de transacción", "@sage/xtrem-stock/nodes__stock_issue_line__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-stock/nodes__stock_issue_line__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-stock/nodes__stock_issue_line__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-stock/nodes__stock_issue_line__mutation__setDimension": "Definir secci<PERSON>", "@sage/xtrem-stock/nodes__stock_issue_line__mutation__setDimension__failed": "Error al definir la sección", "@sage/xtrem-stock/nodes__stock_issue_line__mutation__setDimension__parameter__stockIssueLine": "Línea de salida de stock", "@sage/xtrem-stock/nodes__stock_issue_line__mutation__setDimension__parameter__storedAttributes": "Atributos almacenados", "@sage/xtrem-stock/nodes__stock_issue_line__mutation__setDimension__parameter__storedDimensions": "Secciones almacenadas", "@sage/xtrem-stock/nodes__stock_issue_line__node_name": "Línea de salida de stock", "@sage/xtrem-stock/nodes__stock_issue_line__property__analyticalData": "Datos analític<PERSON>", "@sage/xtrem-stock/nodes__stock_issue_line__property__computedAttributes": "Atributos calculados", "@sage/xtrem-stock/nodes__stock_issue_line__property__displayStatus": "Estado", "@sage/xtrem-stock/nodes__stock_issue_line__property__document": "Documento", "@sage/xtrem-stock/nodes__stock_issue_line__property__documentId": "Id. de documento", "@sage/xtrem-stock/nodes__stock_issue_line__property__documentNumber": "Número de documento", "@sage/xtrem-stock/nodes__stock_issue_line__property__item": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-stock/nodes__stock_issue_line__property__itemSite": "Artículo-planta", "@sage/xtrem-stock/nodes__stock_issue_line__property__jsonStockDetails": "Detalles de stock en formato JSON", "@sage/xtrem-stock/nodes__stock_issue_line__property__location": "Ubicación", "@sage/xtrem-stock/nodes__stock_issue_line__property__lot": "Lote", "@sage/xtrem-stock/nodes__stock_issue_line__property__orderCost": "Coste de pedido", "@sage/xtrem-stock/nodes__stock_issue_line__property__quantityInStockUnit": "Cantidad en unidad de stock", "@sage/xtrem-stock/nodes__stock_issue_line__property__site": "Planta", "@sage/xtrem-stock/nodes__stock_issue_line__property__stockDetails": "Detalles de stock", "@sage/xtrem-stock/nodes__stock_issue_line__property__stockDetailStatus": "Estado de detalles de stock", "@sage/xtrem-stock/nodes__stock_issue_line__property__stockMovements": "Movimientos de stock", "@sage/xtrem-stock/nodes__stock_issue_line__property__stockStatus": "Estado de stock", "@sage/xtrem-stock/nodes__stock_issue_line__property__stockTransactions": "Transacción de stock", "@sage/xtrem-stock/nodes__stock_issue_line__property__stockTransactionStatus": "Estado de transacción de stock", "@sage/xtrem-stock/nodes__stock_issue_line__property__stockUnit": "Unidad de stock", "@sage/xtrem-stock/nodes__stock_issue_line__property__storedAttributes": "Atributos almacenados", "@sage/xtrem-stock/nodes__stock_issue_line__property__storedDimensions": "Secciones almacenadas", "@sage/xtrem-stock/nodes__stock_issue_line__property__totalCost": "Coste total", "@sage/xtrem-stock/nodes__stock_issue_line__property__valuedCost": "Coste valorado", "@sage/xtrem-stock/nodes__stock_issue_post__stock_details_required": "Introduce los detalles de stock en todas las líneas para poder contabilizar.", "@sage/xtrem-stock/nodes__stock_receipt__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-stock/nodes__stock_receipt__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-stock/nodes__stock_receipt__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-stock/nodes__stock_receipt__asyncMutation__createTestStockReceipt": "Crear entrada de stock de prueba", "@sage/xtrem-stock/nodes__stock_receipt__asyncMutation__createTestStockReceipt__failed": "Error al crear la entrada de stock de prueba", "@sage/xtrem-stock/nodes__stock_receipt__asyncMutation__createTestStockReceipt__parameter__data": "Datos", "@sage/xtrem-stock/nodes__stock_receipt__deletion_forbidden": "La entrada de stock actual no se puede eliminar.", "@sage/xtrem-stock/nodes__stock_receipt__mutation__postToStock": "Contabilizar en stock", "@sage/xtrem-stock/nodes__stock_receipt__mutation__postToStock__failed": "Error al contabilizar en stock", "@sage/xtrem-stock/nodes__stock_receipt__mutation__postToStock__parameter__documentIds": "Ids. de documentos", "@sage/xtrem-stock/nodes__stock_receipt__mutation__repost": "Volver a contabilizar", "@sage/xtrem-stock/nodes__stock_receipt__mutation__repost__failed": "Error al volver a contabilizar", "@sage/xtrem-stock/nodes__stock_receipt__mutation__repost__parameter__documentLines": "Líneas de documento", "@sage/xtrem-stock/nodes__stock_receipt__mutation__repost__parameter__stockReceipt": "Entrada de stock", "@sage/xtrem-stock/nodes__stock_receipt__mutation__resendNotificationForFinance": "Reenviar notificación para contabilidad", "@sage/xtrem-stock/nodes__stock_receipt__mutation__resendNotificationForFinance__failed": "Error al reenviar la notificación para la contabilidad", "@sage/xtrem-stock/nodes__stock_receipt__mutation__resendNotificationForFinance__parameter__stockReceipt": "Entrada de stock", "@sage/xtrem-stock/nodes__stock_receipt__node_name": "Entrada de stock", "@sage/xtrem-stock/nodes__stock_receipt__property__description": "Descripción", "@sage/xtrem-stock/nodes__stock_receipt__property__displayStatus": "Estado", "@sage/xtrem-stock/nodes__stock_receipt__property__documentDate": "Fecha de documento", "@sage/xtrem-stock/nodes__stock_receipt__property__effectiveDate": "Fecha efectiva", "@sage/xtrem-stock/nodes__stock_receipt__property__financeIntegrationStatus": "Estado de integración contable", "@sage/xtrem-stock/nodes__stock_receipt__property__financialSite": "Planta financiera", "@sage/xtrem-stock/nodes__stock_receipt__property__forceUpdateForStock": "Forzar actualización de Stock", "@sage/xtrem-stock/nodes__stock_receipt__property__isSetDimensionsMainListHidden": "Lista principal de definición de secciones oculta", "@sage/xtrem-stock/nodes__stock_receipt__property__lines": "Líneas", "@sage/xtrem-stock/nodes__stock_receipt__property__number": "Número", "@sage/xtrem-stock/nodes__stock_receipt__property__postingDetails": "Detalles de contabilización", "@sage/xtrem-stock/nodes__stock_receipt__property__reasonCode": "Código de motivo", "@sage/xtrem-stock/nodes__stock_receipt__property__status": "Estado", "@sage/xtrem-stock/nodes__stock_receipt__property__stockSite": "Planta de stock", "@sage/xtrem-stock/nodes__stock_receipt__property__stockTransactionStatus": "Estado de transacción de stock", "@sage/xtrem-stock/nodes__stock_receipt__property__transactionCurrency": "Divisa de transacción", "@sage/xtrem-stock/nodes__stock_receipt_line__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-stock/nodes__stock_receipt_line__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-stock/nodes__stock_receipt_line__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-stock/nodes__stock_receipt_line__mutation__setDimension": "Definir secci<PERSON>", "@sage/xtrem-stock/nodes__stock_receipt_line__mutation__setDimension__failed": "Error al definir la sección", "@sage/xtrem-stock/nodes__stock_receipt_line__mutation__setDimension__parameter__stockReceiptLine": "Línea de entrada de stock", "@sage/xtrem-stock/nodes__stock_receipt_line__mutation__setDimension__parameter__storedAttributes": "Atributos almacenados", "@sage/xtrem-stock/nodes__stock_receipt_line__mutation__setDimension__parameter__storedDimensions": "Secciones almacenadas", "@sage/xtrem-stock/nodes__stock_receipt_line__node_name": "Línea de entrada de stock", "@sage/xtrem-stock/nodes__stock_receipt_line__property__analyticalData": "Datos analític<PERSON>", "@sage/xtrem-stock/nodes__stock_receipt_line__property__computedAttributes": "Atributos calculados", "@sage/xtrem-stock/nodes__stock_receipt_line__property__displayStatus": "Estado", "@sage/xtrem-stock/nodes__stock_receipt_line__property__document": "Documento", "@sage/xtrem-stock/nodes__stock_receipt_line__property__documentId": "Id. de documento", "@sage/xtrem-stock/nodes__stock_receipt_line__property__documentNumber": "Número de documento", "@sage/xtrem-stock/nodes__stock_receipt_line__property__existingLot": "Lote existente", "@sage/xtrem-stock/nodes__stock_receipt_line__property__item": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-stock/nodes__stock_receipt_line__property__itemSite": "Artículo-planta", "@sage/xtrem-stock/nodes__stock_receipt_line__property__jsonStockDetails": "Detalles de stock en formato JSON", "@sage/xtrem-stock/nodes__stock_receipt_line__property__location": "Ubicación", "@sage/xtrem-stock/nodes__stock_receipt_line__property__lotCreateData": "Datos de creación de lote", "@sage/xtrem-stock/nodes__stock_receipt_line__property__orderCost": "Coste de pedido", "@sage/xtrem-stock/nodes__stock_receipt_line__property__quantityInStockUnit": "Cantidad en unidad de stock", "@sage/xtrem-stock/nodes__stock_receipt_line__property__site": "Planta", "@sage/xtrem-stock/nodes__stock_receipt_line__property__stockDetails": "Detalles de stock", "@sage/xtrem-stock/nodes__stock_receipt_line__property__stockDetailStatus": "Estado de detalles de stock", "@sage/xtrem-stock/nodes__stock_receipt_line__property__stockMovements": "Movimientos de stock", "@sage/xtrem-stock/nodes__stock_receipt_line__property__stockStatus": "Estado de stock", "@sage/xtrem-stock/nodes__stock_receipt_line__property__stockTransactions": "Transacción de stock", "@sage/xtrem-stock/nodes__stock_receipt_line__property__stockTransactionStatus": "Estado de transacción de stock", "@sage/xtrem-stock/nodes__stock_receipt_line__property__stockUnit": "Unidad de stock", "@sage/xtrem-stock/nodes__stock_receipt_line__property__storedAttributes": "Atributos almacenados", "@sage/xtrem-stock/nodes__stock_receipt_line__property__storedDimensions": "Secciones almacenadas", "@sage/xtrem-stock/nodes__stock_receipt_line__property__totalCost": "Coste total", "@sage/xtrem-stock/nodes__stock_receipt_line__property__valuedCost": "Coste valorado", "@sage/xtrem-stock/nodes__stock_receipt_node_post__stock_details_required": "Introduce los detalles de stock en todas las líneas para poder contabilizar.", "@sage/xtrem-stock/nodes__stock_reorder_calculation_input_set__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-stock/nodes__stock_reorder_calculation_input_set__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-stock/nodes__stock_reorder_calculation_input_set__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-stock/nodes__stock_reorder_calculation_input_set__asyncMutation__reorderCalculation": "Calcular reaprovisionamiento", "@sage/xtrem-stock/nodes__stock_reorder_calculation_input_set__asyncMutation__reorderCalculation__failed": "Error al calcular el reaprovisionamiento", "@sage/xtrem-stock/nodes__stock_reorder_calculation_input_set__asyncMutation__reorderCalculation__parameter__userId": "<PERSON>d. de usuario", "@sage/xtrem-stock/nodes__stock_reorder_calculation_input_set__node_name": "Conjunto de entrada de cálculo de reaprovisionamiento de stock", "@sage/xtrem-stock/nodes__stock_reorder_calculation_input_set__notification_calculation_failed": "Ha habido un error al calcular el reaprovisionamiento de stock.", "@sage/xtrem-stock/nodes__stock_reorder_calculation_input_set__notification_calculation_finished": "El cálculo de reaprovisionamiento de stock ha finalizado.", "@sage/xtrem-stock/nodes__stock_reorder_calculation_input_set__property__company": "Sociedad", "@sage/xtrem-stock/nodes__stock_reorder_calculation_input_set__property__endDate": "<PERSON><PERSON> de fin", "@sage/xtrem-stock/nodes__stock_reorder_calculation_input_set__property__fromItem": "<PERSON><PERSON>", "@sage/xtrem-stock/nodes__stock_reorder_calculation_input_set__property__lines": "Líneas", "@sage/xtrem-stock/nodes__stock_reorder_calculation_input_set__property__reorderType": "Tipo de reaprovisionamiento", "@sage/xtrem-stock/nodes__stock_reorder_calculation_input_set__property__sites": "Plantas", "@sage/xtrem-stock/nodes__stock_reorder_calculation_input_set__property__status": "Estado", "@sage/xtrem-stock/nodes__stock_reorder_calculation_input_set__property__toItem": "<PERSON><PERSON>", "@sage/xtrem-stock/nodes__stock_reorder_calculation_input_set__property__user": "Usuario", "@sage/xtrem-stock/nodes__stock_reorder_calculation_result_line__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-stock/nodes__stock_reorder_calculation_result_line__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-stock/nodes__stock_reorder_calculation_result_line__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-stock/nodes__stock_reorder_calculation_result_line__node_name": "Línea de resultados de cálculo de reaprovisionamiento de stock", "@sage/xtrem-stock/nodes__stock_reorder_calculation_result_line__property__company": "Sociedad", "@sage/xtrem-stock/nodes__stock_reorder_calculation_result_line__property__currency": "Divisa", "@sage/xtrem-stock/nodes__stock_reorder_calculation_result_line__property__inputSet": "Conjunto de entrada", "@sage/xtrem-stock/nodes__stock_reorder_calculation_result_line__property__itemSite": "Artículo-planta", "@sage/xtrem-stock/nodes__stock_reorder_calculation_result_line__property__orderDate": "<PERSON><PERSON>edido", "@sage/xtrem-stock/nodes__stock_reorder_calculation_result_line__property__purchaseUnit": "Unidad de compra", "@sage/xtrem-stock/nodes__stock_reorder_calculation_result_line__property__quantity": "Cantidad", "@sage/xtrem-stock/nodes__stock_reorder_calculation_result_line__property__reorderType": "Tipo de reaprovisionamiento", "@sage/xtrem-stock/nodes__stock_reorder_calculation_result_line__property__stockUnit": "Unidad de stock", "@sage/xtrem-stock/nodes__stock_reorder_calculation_result_line__property__supplier": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-stock/nodes__stock_update_listener__quantity_of_stock_record_to_be_counted_has_changed": "La cantidad del registro de stock por contar se ha modificado desde que ha comenzado el inventario.\n\n{{stock_identifiers}}", "@sage/xtrem-stock/nodes__stock_update_listener__stock_record_identification": "Artículo: {{item}}\n\nEstado: {{status}}\n\nLote: {{lot}}\n\nUbicación: {{location}}\n\nCantidad en stock: {{quantity}}", "@sage/xtrem-stock/nodes__stock_valuation_input_set__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-stock/nodes__stock_valuation_input_set__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-stock/nodes__stock_valuation_input_set__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-stock/nodes__stock_valuation_input_set__asyncMutation__stockValuation": "Valorar stock", "@sage/xtrem-stock/nodes__stock_valuation_input_set__asyncMutation__stockValuation__failed": "Error al valorar el stock", "@sage/xtrem-stock/nodes__stock_valuation_input_set__asyncMutation__stockValuation__parameter__userId": "<PERSON>d. de usuario", "@sage/xtrem-stock/nodes__stock_valuation_input_set__node_name": "Conjunto de entrada de valoración de stock", "@sage/xtrem-stock/nodes__stock_valuation_input_set__notification_valuation_failed": "Ha habido un error al valorar el stock.", "@sage/xtrem-stock/nodes__stock_valuation_input_set__notification_valuation_finished": "La valoración de stock ha finalizado.", "@sage/xtrem-stock/nodes__stock_valuation_input_set__property__commodityCode": "Código de mercancías", "@sage/xtrem-stock/nodes__stock_valuation_input_set__property__company": "Sociedad", "@sage/xtrem-stock/nodes__stock_valuation_input_set__property__date": "<PERSON><PERSON>", "@sage/xtrem-stock/nodes__stock_valuation_input_set__property__displayZeroValues": "Mostrar valores cero", "@sage/xtrem-stock/nodes__stock_valuation_input_set__property__fromItem": "<PERSON><PERSON>", "@sage/xtrem-stock/nodes__stock_valuation_input_set__property__itemCategory": "Categoría de artículo", "@sage/xtrem-stock/nodes__stock_valuation_input_set__property__lines": "Líneas", "@sage/xtrem-stock/nodes__stock_valuation_input_set__property__postingClass": "Clase contable", "@sage/xtrem-stock/nodes__stock_valuation_input_set__property__sites": "Plantas", "@sage/xtrem-stock/nodes__stock_valuation_input_set__property__status": "Estado", "@sage/xtrem-stock/nodes__stock_valuation_input_set__property__toItem": "<PERSON><PERSON>", "@sage/xtrem-stock/nodes__stock_valuation_input_set__property__user": "Usuario", "@sage/xtrem-stock/nodes__stock_valuation_input_set__property__valuationMethod": "Método de valoración", "@sage/xtrem-stock/nodes__stock_valuation_input_set_to_site__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-stock/nodes__stock_valuation_input_set_to_site__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-stock/nodes__stock_valuation_input_set_to_site__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-stock/nodes__stock_valuation_input_set_to_site__node_name": "Conjunto de entrada de valoración de stock para planta", "@sage/xtrem-stock/nodes__stock_valuation_input_set_to_site__property__inputSet": "Conjunto de entrada", "@sage/xtrem-stock/nodes__stock_valuation_input_set_to_site__property__site": "Planta", "@sage/xtrem-stock/nodes__stock_valuation_result_line__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-stock/nodes__stock_valuation_result_line__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-stock/nodes__stock_valuation_result_line__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-stock/nodes__stock_valuation_result_line__node_name": "Línea de resultado de valoración de stock", "@sage/xtrem-stock/nodes__stock_valuation_result_line__property__commodityCode": "Código de mercancías", "@sage/xtrem-stock/nodes__stock_valuation_result_line__property__company": "Sociedad", "@sage/xtrem-stock/nodes__stock_valuation_result_line__property__costType": "Tipo de coste", "@sage/xtrem-stock/nodes__stock_valuation_result_line__property__currency": "Divisa", "@sage/xtrem-stock/nodes__stock_valuation_result_line__property__inputSet": "Conjunto de entrada", "@sage/xtrem-stock/nodes__stock_valuation_result_line__property__item": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-stock/nodes__stock_valuation_result_line__property__itemCategory": "Categoría de artículo", "@sage/xtrem-stock/nodes__stock_valuation_result_line__property__postingClass": "Clase contable", "@sage/xtrem-stock/nodes__stock_valuation_result_line__property__quantity": "Cantidad", "@sage/xtrem-stock/nodes__stock_valuation_result_line__property__site": "Planta", "@sage/xtrem-stock/nodes__stock_valuation_result_line__property__stockUnit": "Unidad de stock", "@sage/xtrem-stock/nodes__stock_valuation_result_line__property__stockValue": "Valor de stock", "@sage/xtrem-stock/nodes__stock_valuation_result_line__property__unitCost": "Coste unitario", "@sage/xtrem-stock/nodes__stock_valuation_result_line__property__valuationDate": "Fecha de valoración", "@sage/xtrem-stock/nodes__stock_value_change__amount_newAmount_average_cost": "Línea 1: el nuevo importe no puede coincidir con el actual. Introduce uno diferente.", "@sage/xtrem-stock/nodes__stock_value_change__amount_newAmount_fifo_cost": "Línea {{lineNumber}}: el nuevo importe no puede coincidir con el actual. Introduce uno diferente.", "@sage/xtrem-stock/nodes__stock_value_change__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-stock/nodes__stock_value_change__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-stock/nodes__stock_value_change__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-stock/nodes__stock_value_change__average_cost_quantity_cost_amount_has_changed": "El precio medio de la línea 1 ha cambiado. Crea de nuevo la línea de cambio de valor de stock.", "@sage/xtrem-stock/nodes__stock_value_change__cant_repost_stock_value_change_when_status_is_not_failed": "Solo puedes volver a contabilizar un cambio de valor de stock si su estado es \"Error\".", "@sage/xtrem-stock/nodes__stock_value_change__company_has_no_stock_posting": "No se ha contabilizado ningún registro de stock en esta sociedad.", "@sage/xtrem-stock/nodes__stock_value_change__document_was_posted": "El cambio de valor de stock se ha contabilizado.", "@sage/xtrem-stock/nodes__stock_value_change__fifo_cost_quantity_cost_amount_has_changed": "El registro de nivel FIFO de la línea con fecha efectiva {{date}} y número de secuencia {{sequence}} ha cambiado. Crea de nuevo la línea de cambio de valor de stock.", "@sage/xtrem-stock/nodes__stock_value_change__lines_mandatory": "El cambio de valor de stock debe tener al menos una línea.", "@sage/xtrem-stock/nodes__stock_value_change__mutation__financeIntegrationCheck": "Revisar integración contable", "@sage/xtrem-stock/nodes__stock_value_change__mutation__financeIntegrationCheck__failed": "Error al revisar la integración contable", "@sage/xtrem-stock/nodes__stock_value_change__mutation__financeIntegrationCheck__parameter__valueChange": "Cambio de valor", "@sage/xtrem-stock/nodes__stock_value_change__mutation__postToStock": "Contabilizar en stock", "@sage/xtrem-stock/nodes__stock_value_change__mutation__postToStock__failed": "Error al contabilizar en stock", "@sage/xtrem-stock/nodes__stock_value_change__mutation__postToStock__parameter__documentIds": "Ids. de documentos", "@sage/xtrem-stock/nodes__stock_value_change__mutation__repost": "Volver a contabilizar", "@sage/xtrem-stock/nodes__stock_value_change__mutation__repost__failed": "Error al volver a contabilizar", "@sage/xtrem-stock/nodes__stock_value_change__mutation__repost__parameter__documentLines": "Líneas de documento", "@sage/xtrem-stock/nodes__stock_value_change__mutation__repost__parameter__stockValueChange": "Cambio de valor de stock", "@sage/xtrem-stock/nodes__stock_value_change__mutation__resendNotificationForFinance": "Reenviar notificación para contabilidad", "@sage/xtrem-stock/nodes__stock_value_change__mutation__resendNotificationForFinance__failed": "Error al reenviar la notificación para la contabilidad", "@sage/xtrem-stock/nodes__stock_value_change__mutation__resendNotificationForFinance__parameter__stockValueChange": "Cambio de valor de stock", "@sage/xtrem-stock/nodes__stock_value_change__node_name": "Cambio de valor de stock", "@sage/xtrem-stock/nodes__stock_value_change__property__description": "Descripción", "@sage/xtrem-stock/nodes__stock_value_change__property__documentDate": "Fecha de documento", "@sage/xtrem-stock/nodes__stock_value_change__property__effectiveDate": "Fecha efectiva", "@sage/xtrem-stock/nodes__stock_value_change__property__financeIntegrationStatus": "Estado de integración contable", "@sage/xtrem-stock/nodes__stock_value_change__property__financialSite": "Planta financiera", "@sage/xtrem-stock/nodes__stock_value_change__property__forceUpdateForStock": "Forzar actualización de Stock", "@sage/xtrem-stock/nodes__stock_value_change__property__item": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-stock/nodes__stock_value_change__property__itemSite": "Artículo-planta", "@sage/xtrem-stock/nodes__stock_value_change__property__lines": "Líneas", "@sage/xtrem-stock/nodes__stock_value_change__property__number": "Número", "@sage/xtrem-stock/nodes__stock_value_change__property__postedDate": "Fecha de contabilización", "@sage/xtrem-stock/nodes__stock_value_change__property__postingDetails": "Detalles de contabilización", "@sage/xtrem-stock/nodes__stock_value_change__property__site": "Planta", "@sage/xtrem-stock/nodes__stock_value_change__property__stockTransactionStatus": "Estado de transacción de stock", "@sage/xtrem-stock/nodes__stock_value_change__property__totalQuantityInStock": "Cantidad total en stock", "@sage/xtrem-stock/nodes__stock_value_change__property__totalValueOfStock": "Valor total de stock", "@sage/xtrem-stock/nodes__stock_value_change__property__transactionCurrency": "Divisa de transacción", "@sage/xtrem-stock/nodes__stock_value_change__property__unitCost": "Coste unitario", "@sage/xtrem-stock/nodes__stock_value_change__property__valuationMethod": "Método de valoración", "@sage/xtrem-stock/nodes__stock_value_change_line__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-stock/nodes__stock_value_change_line__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-stock/nodes__stock_value_change_line__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-stock/nodes__stock_value_change_line__check_amount_stock_detail": "El importe en los detalles de stock ({{totalStockDetails}}) no coincide con la desviación de cambio de valor en la línea de documento {{varianceLine}}.", "@sage/xtrem-stock/nodes__stock_value_change_line__node_name": "Línea de cambio de valor de stock", "@sage/xtrem-stock/nodes__stock_value_change_line__property__amount": "Importe", "@sage/xtrem-stock/nodes__stock_value_change_line__property__analyticalData": "Datos analític<PERSON>", "@sage/xtrem-stock/nodes__stock_value_change_line__property__computedAttributes": "Atributos calculados", "@sage/xtrem-stock/nodes__stock_value_change_line__property__currency": "Divisa", "@sage/xtrem-stock/nodes__stock_value_change_line__property__document": "Documento", "@sage/xtrem-stock/nodes__stock_value_change_line__property__documentId": "Id. de documento", "@sage/xtrem-stock/nodes__stock_value_change_line__property__documentNumber": "Número de documento", "@sage/xtrem-stock/nodes__stock_value_change_line__property__effectiveDate": "Fecha efectiva", "@sage/xtrem-stock/nodes__stock_value_change_line__property__fifoCost": "FIFO", "@sage/xtrem-stock/nodes__stock_value_change_line__property__item": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-stock/nodes__stock_value_change_line__property__jsonStockDetails": "Detalles de stock en formato JSON", "@sage/xtrem-stock/nodes__stock_value_change_line__property__newAmount": "Nuevo importe", "@sage/xtrem-stock/nodes__stock_value_change_line__property__newUnitCost": "Nuevo coste unitario", "@sage/xtrem-stock/nodes__stock_value_change_line__property__quantity": "Cantidad", "@sage/xtrem-stock/nodes__stock_value_change_line__property__quantityInStockUnit": "Cantidad en unidad de stock", "@sage/xtrem-stock/nodes__stock_value_change_line__property__site": "Planta", "@sage/xtrem-stock/nodes__stock_value_change_line__property__stockDetails": "Detalles de stock", "@sage/xtrem-stock/nodes__stock_value_change_line__property__stockMovements": "Movimientos de stock", "@sage/xtrem-stock/nodes__stock_value_change_line__property__stockTransactions": "Transacciones de stock", "@sage/xtrem-stock/nodes__stock_value_change_line__property__stockTransactionStatus": "Estado de transacción de stock", "@sage/xtrem-stock/nodes__stock_value_change_line__property__stockUnit": "Unidad de stock", "@sage/xtrem-stock/nodes__stock_value_change_line__property__storedAttributes": "Atributos almacenados", "@sage/xtrem-stock/nodes__stock_value_change_line__property__storedDimensions": "Secciones almacenadas", "@sage/xtrem-stock/nodes__stock_value_change_line__property__unitCost": "Coste unitario", "@sage/xtrem-stock/nodes__stock_value_correction__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-stock/nodes__stock_value_correction__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-stock/nodes__stock_value_correction__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-stock/nodes__stock_value_correction__mutation__postToStock": "Contabilizar en stock", "@sage/xtrem-stock/nodes__stock_value_correction__mutation__postToStock__failed": "Error al contabilizar en stock", "@sage/xtrem-stock/nodes__stock_value_correction__mutation__postToStock__parameter__documentIds": "Ids. de documentos", "@sage/xtrem-stock/nodes__stock_value_correction__node_name": "Rectificación de valor de stock", "@sage/xtrem-stock/nodes__stock_value_correction__property__description": "Descripción", "@sage/xtrem-stock/nodes__stock_value_correction__property__documentDate": "Fecha de documento", "@sage/xtrem-stock/nodes__stock_value_correction__property__effectiveDate": "Fecha efectiva", "@sage/xtrem-stock/nodes__stock_value_correction__property__financialSite": "Planta financiera", "@sage/xtrem-stock/nodes__stock_value_correction__property__lines": "Líneas", "@sage/xtrem-stock/nodes__stock_value_correction__property__number": "Número", "@sage/xtrem-stock/nodes__stock_value_correction__property__reasonCode": "Código de motivo", "@sage/xtrem-stock/nodes__stock_value_correction__property__status": "Estado", "@sage/xtrem-stock/nodes__stock_value_correction__property__stockSite": "Planta de stock", "@sage/xtrem-stock/nodes__stock_value_correction__property__stockTransactionStatus": "Estado de transacción de stock", "@sage/xtrem-stock/nodes__stock_value_correction__property__transactionCurrency": "Divisa de transacción", "@sage/xtrem-stock/nodes__stock_value_correction_line__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-stock/nodes__stock_value_correction_line__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-stock/nodes__stock_value_correction_line__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-stock/nodes__stock_value_correction_line__invalid_document_line": "La transacción de stock de la línea corregida debe estar finalizada.", "@sage/xtrem-stock/nodes__stock_value_correction_line__no_corrected_line": "Indica la línea de documento que se está corrigiendo.", "@sage/xtrem-stock/nodes__stock_value_correction_line__node_name": "Línea de rectificación de valor de stock", "@sage/xtrem-stock/nodes__stock_value_correction_line__property__analyticalData": "Datos analític<PERSON>", "@sage/xtrem-stock/nodes__stock_value_correction_line__property__computedAttributes": "Atributos calculados", "@sage/xtrem-stock/nodes__stock_value_correction_line__property__correctedDocumentLine": "Línea de documento corregido", "@sage/xtrem-stock/nodes__stock_value_correction_line__property__correctedDocumentType": "Tipo de documento corregido", "@sage/xtrem-stock/nodes__stock_value_correction_line__property__document": "Documento", "@sage/xtrem-stock/nodes__stock_value_correction_line__property__documentId": "Id. de documento", "@sage/xtrem-stock/nodes__stock_value_correction_line__property__documentNumber": "Número de documento", "@sage/xtrem-stock/nodes__stock_value_correction_line__property__item": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-stock/nodes__stock_value_correction_line__property__itemSite": "Artículo-planta", "@sage/xtrem-stock/nodes__stock_value_correction_line__property__newUnitCost": "Nuevo coste unitario", "@sage/xtrem-stock/nodes__stock_value_correction_line__property__orderCost": "Coste de pedido", "@sage/xtrem-stock/nodes__stock_value_correction_line__property__site": "Planta", "@sage/xtrem-stock/nodes__stock_value_correction_line__property__stockDetails": "Detalles de stock", "@sage/xtrem-stock/nodes__stock_value_correction_line__property__stockMovements": "Movimientos de stock", "@sage/xtrem-stock/nodes__stock_value_correction_line__property__stockTransactions": "Transacción de stock", "@sage/xtrem-stock/nodes__stock_value_correction_line__property__stockTransactionStatus": "Estado de transacción de stock", "@sage/xtrem-stock/nodes__stock_value_correction_line__property__storedAttributes": "Atributos almacenados", "@sage/xtrem-stock/nodes__stock_value_correction_line__property__storedDimensions": "Secciones almacenadas", "@sage/xtrem-stock/nodes__stock_value_correction_line__property__unitCost": "Coste unitario", "@sage/xtrem-stock/nodes__stock_value_correction_line__property__valuedCost": "Coste valorado", "@sage/xtrem-stock/nodes__stock-adjustment__cant_post_stock_adjustment_when_stock_detail_status_is_required": "Introduce los detalles de stock en todas las líneas para poder contabilizar.", "@sage/xtrem-stock/nodes__stock-adjustment__cant_repost_stock_adjustment_when_status_is_not_failed": "Solo puedes volver a contabilizar una regularización de stock con errores o sin registrar.", "@sage/xtrem-stock/nodes__stock-adjustment__company_has_no_stock_posting": "No se ha contabilizado ningún registro de stock en esta sociedad.", "@sage/xtrem-stock/nodes__stock-adjustment__document_was_posted": "La regularización de stock se ha contabilizado.", "@sage/xtrem-stock/nodes__stock-issue__cant_repost_stock_issue_when_status_is_not_failed": "Solo puedes volver a contabilizar una salida de stock con errores o sin registrar.", "@sage/xtrem-stock/nodes__stock-issue__company_has_no_stock_posting": "Esta sociedad no tiene ninguna contabilización de stock.", "@sage/xtrem-stock/nodes__stock-issue__document_was_posted": "La entrada de stock se ha contabilizado.", "@sage/xtrem-stock/nodes__stock-receipt__cant_repost_stock_receipt_when_status_is_not_failed": "Solo puedes volver a contabilizar una entrada de stock si su estado es \"Error\" o \"Sin registrar\".", "@sage/xtrem-stock/nodes__stock-receipt__company_has_no_stock_posting": "Esta sociedad no tiene ninguna contabilización de stock.", "@sage/xtrem-stock/nodes__stock-receipt__document_was_posted": "La entrada de stock se ha contabilizado.", "@sage/xtrem-stock/package__name": "Stock", "@sage/xtrem-stock/page__stock_count__add_new_line": "<PERSON><PERSON><PERSON>", "@sage/xtrem-stock/page__stock_count__forbid_addition_serialized_managed_item": "No se puede añadir una línea de stock de un artículo serializado a un inventario existente.", "@sage/xtrem-stock/page-extensions__bill_of_material_component_panel_extension__availableQuantityInStockUnit____title": "Cantidad de stock disponible", "@sage/xtrem-stock/page-extensions__bill_of_material_component_panel_extension__stockOnHand____title": "Stock físico", "@sage/xtrem-stock/page-extensions__bill_of_material_component_panel_extension__stockShortageInStockUnit____title": "Ruptura de stock", "@sage/xtrem-stock/page-extensions__bill_of_material_component_panel_extension__stockShortageStatus____title": "Estado de ruptura de stock", "@sage/xtrem-stock/page-extensions__bill_of_material_extension__components____columns__title__availableQuantityInStockUnit": "Cantidad de stock disponible", "@sage/xtrem-stock/page-extensions__bill_of_material_extension__components____columns__title__stockShortageInStockUnit": "Ruptura de stock", "@sage/xtrem-stock/page-extensions__create_test_data_extension__isStockQuantityFixed____title": "Stock quantity fixed for all items", "@sage/xtrem-stock/page-extensions__create_test_data_extension__item____columns__title__description": "Descripción", "@sage/xtrem-stock/page-extensions__create_test_data_extension__item____columns__title__id": "Id<PERSON> <PERSON> artí<PERSON>lo", "@sage/xtrem-stock/page-extensions__create_test_data_extension__item____columns__title__name": "Nombre", "@sage/xtrem-stock/page-extensions__create_test_data_extension__item____lookupDialogTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-stock/page-extensions__create_test_data_extension__item____title": "Artículo base", "@sage/xtrem-stock/page-extensions__create_test_data_extension__itemBlock____title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-stock/page-extensions__create_test_data_extension__itemQuantity____title": "Cantidad de artículos", "@sage/xtrem-stock/page-extensions__create_test_data_extension__maxStockQuantity____title": "Cantidad máxima de stock", "@sage/xtrem-stock/page-extensions__item_site_extension__countingInProgress____title": "Inventario en curso", "@sage/xtrem-stock/page-extensions__item_site_extension__countingInProgressMention____title": "Inventario en curso", "@sage/xtrem-stock/page-extensions__item_site_extension__lastCountDate____title": "<PERSON><PERSON> de último inventario", "@sage/xtrem-stock/pages__bill_of_material_component_panel_extension__display_shortage_status_false": "Stock disponible", "@sage/xtrem-stock/pages__bill_of_material_component_panel_extension__display_shortage_status_true": "Ruptura de stock", "@sage/xtrem-stock/pages__cost_roll_up_sub_assembly_dialog____title": "Subconjuntos", "@sage/xtrem-stock/pages__cost_roll_up_sub_assembly_dialog____update_title": "Artí<PERSON>lo {{itemName}}", "@sage/xtrem-stock/pages__cost_roll_up_sub_assembly_dialog__mainSection____title": "Subconjuntos", "@sage/xtrem-stock/pages__cost_roll_up_sub_assembly_dialog__ok____title": "Aceptar", "@sage/xtrem-stock/pages__cost_roll_up_sub_assembly_dialog__subAssemblies____columns__title__calculationQuantity": "Cantidad de cálculo", "@sage/xtrem-stock/pages__cost_roll_up_sub_assembly_dialog__subAssemblies____columns__title__componentNumber": "Número de componente", "@sage/xtrem-stock/pages__cost_roll_up_sub_assembly_dialog__subAssemblies____columns__title__currentLaborCost": "Coste de mano de obra actual", "@sage/xtrem-stock/pages__cost_roll_up_sub_assembly_dialog__subAssemblies____columns__title__currentMachineCost": "Coste de máquina actual", "@sage/xtrem-stock/pages__cost_roll_up_sub_assembly_dialog__subAssemblies____columns__title__currentMaterialCost": "Coste de material actual", "@sage/xtrem-stock/pages__cost_roll_up_sub_assembly_dialog__subAssemblies____columns__title__currentToolCost": "Coste de herramienta actual", "@sage/xtrem-stock/pages__cost_roll_up_sub_assembly_dialog__subAssemblies____columns__title__currentTotalCost": "Coste total actual", "@sage/xtrem-stock/pages__cost_roll_up_sub_assembly_dialog__subAssemblies____columns__title__item__id": "Id<PERSON> <PERSON> artí<PERSON>lo", "@sage/xtrem-stock/pages__cost_roll_up_sub_assembly_dialog__subAssemblies____columns__title__item__name": "Nombre de artículo", "@sage/xtrem-stock/pages__cost_roll_up_sub_assembly_dialog__subAssemblies____columns__title__laborCost": "Coste de mano de obra", "@sage/xtrem-stock/pages__cost_roll_up_sub_assembly_dialog__subAssemblies____columns__title__machineCost": "Coste de m<PERSON>", "@sage/xtrem-stock/pages__cost_roll_up_sub_assembly_dialog__subAssemblies____columns__title__materialCost": "Coste de material", "@sage/xtrem-stock/pages__cost_roll_up_sub_assembly_dialog__subAssemblies____columns__title__toolCost": "Coste de <PERSON>", "@sage/xtrem-stock/pages__cost_roll_up_sub_assembly_dialog__subAssemblies____columns__title__totalCost": "Coste total", "@sage/xtrem-stock/pages__cost_roll_up_sub_assembly_dialog__subAssemblies____title": "Subconjuntos", "@sage/xtrem-stock/pages__create-test-data__create_items_validation_item": "Selecciona un artículo.", "@sage/xtrem-stock/pages__create-test-data__create_items_validation_max_stock_quantity": "Introduce una cantidad superior a 0.", "@sage/xtrem-stock/pages__create-test-data__create_items_validation_quantity": "Introduce una cantidad superior a 0.", "@sage/xtrem-stock/pages__fifo_valuation_tier_table_panel____title": "<PERSON><PERSON><PERSON> de nivel FIFO", "@sage/xtrem-stock/pages__fifo_valuation_tier_table_panel__cancel____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__fifo_valuation_tier_table_panel__confirm____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__fifo_valuation_tier_table_panel__fifoValuationTierLines____columns__title__amount": "Importe", "@sage/xtrem-stock/pages__fifo_valuation_tier_table_panel__fifoValuationTierLines____columns__title__effectiveDate": "Fecha efectiva", "@sage/xtrem-stock/pages__fifo_valuation_tier_table_panel__fifoValuationTierLines____columns__title__nonAbsorbedAmount": "Importe sin absorber", "@sage/xtrem-stock/pages__fifo_valuation_tier_table_panel__fifoValuationTierLines____columns__title__receiptDocumentLine__documentNumber": "Número de documento", "@sage/xtrem-stock/pages__fifo_valuation_tier_table_panel__fifoValuationTierLines____columns__title__receiptQuantity": "Cantidad de entrada", "@sage/xtrem-stock/pages__fifo_valuation_tier_table_panel__fifoValuationTierLines____columns__title__remainingQuantity": "Cantidad restante", "@sage/xtrem-stock/pages__fifo_valuation_tier_table_panel__fifoValuationTierLines____columns__title__sequence": "Secuencia", "@sage/xtrem-stock/pages__fifo_valuation_tier_table_panel__fifoValuationTierLines____columns__title__unitCost": "Coste unitario", "@sage/xtrem-stock/pages__item_site_cost__dimensions_button_text": "Secciones", "@sage/xtrem-stock/pages__mrp_calculation____navigationPanel__bulkActions__title": "Eliminar", "@sage/xtrem-stock/pages__mrp_calculation____navigationPanel__listItem__line2Right__title": "Estado", "@sage/xtrem-stock/pages__mrp_calculation____navigationPanel__listItem__numberOfWeeks__title": "Periodo en semanas", "@sage/xtrem-stock/pages__mrp_calculation____navigationPanel__listItem__titleRight__title": "<PERSON><PERSON>", "@sage/xtrem-stock/pages__mrp_calculation____navigationPanel__listItem__user__title": "Usuario", "@sage/xtrem-stock/pages__mrp_calculation____objectTypePlural": "Resultados de cálculo MRP", "@sage/xtrem-stock/pages__mrp_calculation____objectTypeSingular": "Resultado de cálculo MRP", "@sage/xtrem-stock/pages__mrp_calculation____title": "Cálculo MRP", "@sage/xtrem-stock/pages__mrp_calculation__calculationDate____title": "<PERSON><PERSON>", "@sage/xtrem-stock/pages__mrp_calculation__calculationStatus____title": "Estado", "@sage/xtrem-stock/pages__mrp_calculation__company____columns__title__id": "Id.", "@sage/xtrem-stock/pages__mrp_calculation__company____columns__title__name": "Nombre", "@sage/xtrem-stock/pages__mrp_calculation__company____placeholder": "Seleccionar...", "@sage/xtrem-stock/pages__mrp_calculation__company____title": "Sociedad", "@sage/xtrem-stock/pages__mrp_calculation__createMrpCalculationRequest____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__mrp_calculation__criteriaBlock____title": "Criterios", "@sage/xtrem-stock/pages__mrp_calculation__description____title": "Descripción", "@sage/xtrem-stock/pages__mrp_calculation__errorMessage____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__mrp_calculation__explodeBillOfMaterial____title": "Expandir estructura de materiales", "@sage/xtrem-stock/pages__mrp_calculation__fromItem____title": "<PERSON><PERSON>", "@sage/xtrem-stock/pages__mrp_calculation__fromItemText____title": "<PERSON><PERSON>", "@sage/xtrem-stock/pages__mrp_calculation__isSalesQuoteIncluded____title": "Incluir presupuestos", "@sage/xtrem-stock/pages__mrp_calculation__itemFrom____columns__title__description": "Descripción", "@sage/xtrem-stock/pages__mrp_calculation__itemFrom____columns__title__id": "Id.", "@sage/xtrem-stock/pages__mrp_calculation__itemFrom____columns__title__name": "Nombre", "@sage/xtrem-stock/pages__mrp_calculation__itemFrom____placeholder": "Seleccionar...", "@sage/xtrem-stock/pages__mrp_calculation__itemFrom____title": "<PERSON><PERSON>", "@sage/xtrem-stock/pages__mrp_calculation__itemTo____columns__title__description": "Descripción", "@sage/xtrem-stock/pages__mrp_calculation__itemTo____columns__title__id": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__mrp_calculation__itemTo____columns__title__name": "Nombre", "@sage/xtrem-stock/pages__mrp_calculation__itemTo____placeholder": "Seleccionar...", "@sage/xtrem-stock/pages__mrp_calculation__itemTo____title": "<PERSON><PERSON>", "@sage/xtrem-stock/pages__mrp_calculation__linesSection____title": "Resul<PERSON><PERSON>", "@sage/xtrem-stock/pages__mrp_calculation__lowLevelCode____title": "Nivel inferior de estructura de materiales", "@sage/xtrem-stock/pages__mrp_calculation__mainSection____title": "General", "@sage/xtrem-stock/pages__mrp_calculation__multiRefCompanies____columns__title__id": "Id.", "@sage/xtrem-stock/pages__mrp_calculation__multiRefCompanies____columns__title__name": "Nombre", "@sage/xtrem-stock/pages__mrp_calculation__multiRefCompanies____lookupDialogTitle": "Seleccionar sociedad", "@sage/xtrem-stock/pages__mrp_calculation__multiRefCompanies____title": "Sociedades", "@sage/xtrem-stock/pages__mrp_calculation__multiRefSites____columns__title__id": "Id.", "@sage/xtrem-stock/pages__mrp_calculation__multiRefSites____columns__title__name": "Nombre", "@sage/xtrem-stock/pages__mrp_calculation__multiRefSites____lookupDialogTitle": "Seleccionar planta", "@sage/xtrem-stock/pages__mrp_calculation__multiRefSites____title": "Plantas", "@sage/xtrem-stock/pages__mrp_calculation__numberOfWeeks____title": "Periodo en semanas", "@sage/xtrem-stock/pages__mrp_calculation__purchase_order_creation__success": "El pedido de compra {{num}} se ha creado.", "@sage/xtrem-stock/pages__mrp_calculation__resultLines____columns__columns__uCompany__name__title": "Nombre", "@sage/xtrem-stock/pages__mrp_calculation__resultLines____columns__columns__uCompany__name__title__2": "Id.", "@sage/xtrem-stock/pages__mrp_calculation__resultLines____columns__columns__uSite__name__title": "Nombre", "@sage/xtrem-stock/pages__mrp_calculation__resultLines____columns__columns__uSite__name__title__2": "Id.", "@sage/xtrem-stock/pages__mrp_calculation__resultLines____columns__title___sortValue": "Número de línea", "@sage/xtrem-stock/pages__mrp_calculation__resultLines____columns__title__company": "Sociedad", "@sage/xtrem-stock/pages__mrp_calculation__resultLines____columns__title__company__id": "Id. de sociedad", "@sage/xtrem-stock/pages__mrp_calculation__resultLines____columns__title__company__name": "Razón social", "@sage/xtrem-stock/pages__mrp_calculation__resultLines____columns__title__endDate": "<PERSON><PERSON> de fin", "@sage/xtrem-stock/pages__mrp_calculation__resultLines____columns__title__item": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__mrp_calculation__resultLines____columns__title__item__id": "Id<PERSON> <PERSON> artí<PERSON>lo", "@sage/xtrem-stock/pages__mrp_calculation__resultLines____columns__title__item__name": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__mrp_calculation__resultLines____columns__title__itemName": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__mrp_calculation__resultLines____columns__title__mrpCalculation__startDate": "Primera fecha de propuesta", "@sage/xtrem-stock/pages__mrp_calculation__resultLines____columns__title__preferredProcess": "Método de reaprovisionamiento", "@sage/xtrem-stock/pages__mrp_calculation__resultLines____columns__title__preferredProcessString": "Método de reaprovisionamiento", "@sage/xtrem-stock/pages__mrp_calculation__resultLines____columns__title__quantity": "Cantidad", "@sage/xtrem-stock/pages__mrp_calculation__resultLines____columns__title__referenceItem__description": "Descripción de artículo", "@sage/xtrem-stock/pages__mrp_calculation__resultLines____columns__title__referenceItem__id": "Id<PERSON> <PERSON> artí<PERSON>lo", "@sage/xtrem-stock/pages__mrp_calculation__resultLines____columns__title__site": "Planta", "@sage/xtrem-stock/pages__mrp_calculation__resultLines____columns__title__site__id": "<PERSON><PERSON><PERSON> de planta", "@sage/xtrem-stock/pages__mrp_calculation__resultLines____columns__title__site__name": "Nombre de planta", "@sage/xtrem-stock/pages__mrp_calculation__resultLines____columns__title__startDate": "Fecha de inicio", "@sage/xtrem-stock/pages__mrp_calculation__resultLines____columns__title__suggestionStatus": "Estado de propuesta", "@sage/xtrem-stock/pages__mrp_calculation__resultLines____columns__title__uCompany__name": "Sociedad", "@sage/xtrem-stock/pages__mrp_calculation__resultLines____columns__title__uSite__name": "Planta", "@sage/xtrem-stock/pages__mrp_calculation__resultLines____dropdownActions__title": "<PERSON><PERSON><PERSON> pedido de compra", "@sage/xtrem-stock/pages__mrp_calculation__resultLines____dropdownActions__title__2": "Crear orden de fabricación", "@sage/xtrem-stock/pages__mrp_calculation__resultLines____title": "Detalles", "@sage/xtrem-stock/pages__mrp_calculation__sites____columns__title__id": "Id.", "@sage/xtrem-stock/pages__mrp_calculation__sites____columns__title__name": "Nombre", "@sage/xtrem-stock/pages__mrp_calculation__sites____placeholder": "Seleccionar...", "@sage/xtrem-stock/pages__mrp_calculation__sites____title": "Planta", "@sage/xtrem-stock/pages__mrp_calculation__startDate____title": "Fecha de inicio", "@sage/xtrem-stock/pages__mrp_calculation__toItem____title": "<PERSON><PERSON>", "@sage/xtrem-stock/pages__mrp_calculation__toItemText____title": "<PERSON><PERSON>", "@sage/xtrem-stock/pages__mrp_calculation__user____columns__title__email": "E-mail", "@sage/xtrem-stock/pages__mrp_calculation__user____columns__title__firstName": "Nombre", "@sage/xtrem-stock/pages__mrp_calculation__user____columns__title__lastName": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__mrp_calculation__user____lookupDialogTitle": "Se<PERSON><PERSON><PERSON>r usuario", "@sage/xtrem-stock/pages__mrp_calculation__user____title": "Usuario", "@sage/xtrem-stock/pages__mrp_calculation__valuationDate____title": "Fecha de inicio", "@sage/xtrem-stock/pages__mrp_calculation__work_order_creation__success": "La orden de fabricación {{num}} se ha creado.", "@sage/xtrem-stock/pages__mrp_calculation_justification____navigationPanel__listItem__calculationStatus__title": "Estado", "@sage/xtrem-stock/pages__mrp_calculation_justification____navigationPanel__listItem__numberOfWeeks__title": "Periodo en semanas", "@sage/xtrem-stock/pages__mrp_calculation_justification____navigationPanel__listItem__titleRight__title": "<PERSON><PERSON>", "@sage/xtrem-stock/pages__mrp_calculation_justification____navigationPanel__listItem__user__title": "Usuario", "@sage/xtrem-stock/pages__mrp_calculation_justification____objectTypePlural": "Justificaciones de cálculo MRP", "@sage/xtrem-stock/pages__mrp_calculation_justification____objectTypeSingular": "Justificación de cálculo MRP", "@sage/xtrem-stock/pages__mrp_calculation_justification____title": "Justificación de cálculo MRP", "@sage/xtrem-stock/pages__mrp_calculation_justification__calculationDate____title": "<PERSON><PERSON>", "@sage/xtrem-stock/pages__mrp_calculation_justification__calculationStatus____title": "Estado", "@sage/xtrem-stock/pages__mrp_calculation_justification__criteriaBlock____title": "Criterios", "@sage/xtrem-stock/pages__mrp_calculation_justification__description____title": "Descripción", "@sage/xtrem-stock/pages__mrp_calculation_justification__fromItem____title": "<PERSON><PERSON>", "@sage/xtrem-stock/pages__mrp_calculation_justification__fromItemText____title": "<PERSON><PERSON>", "@sage/xtrem-stock/pages__mrp_calculation_justification__inputSet____columns__title___id": "Id.", "@sage/xtrem-stock/pages__mrp_calculation_justification__inputSet____title": "Conjunto de entrada", "@sage/xtrem-stock/pages__mrp_calculation_justification__mainSection____title": "General", "@sage/xtrem-stock/pages__mrp_calculation_justification__multiRefCompanies____columns__title__id": "Id.", "@sage/xtrem-stock/pages__mrp_calculation_justification__multiRefCompanies____columns__title__name": "Nombre", "@sage/xtrem-stock/pages__mrp_calculation_justification__multiRefCompanies____lookupDialogTitle": "Seleccionar sociedad", "@sage/xtrem-stock/pages__mrp_calculation_justification__multiRefCompanies____title": "Sociedades", "@sage/xtrem-stock/pages__mrp_calculation_justification__multiRefSites____columns__columns__legalCompany__name__title": "Id.", "@sage/xtrem-stock/pages__mrp_calculation_justification__multiRefSites____columns__title__id": "Id.", "@sage/xtrem-stock/pages__mrp_calculation_justification__multiRefSites____columns__title__legalCompany__name": "Sociedad", "@sage/xtrem-stock/pages__mrp_calculation_justification__multiRefSites____columns__title__name": "Nombre", "@sage/xtrem-stock/pages__mrp_calculation_justification__multiRefSites____lookupDialogTitle": "Seleccionar planta", "@sage/xtrem-stock/pages__mrp_calculation_justification__multiRefSites____title": "Plantas", "@sage/xtrem-stock/pages__mrp_calculation_justification__numberOfWeeks____title": "Periodo en semanas", "@sage/xtrem-stock/pages__mrp_calculation_justification__results____columns__title__company__id": "Id. de sociedad", "@sage/xtrem-stock/pages__mrp_calculation_justification__results____columns__title__company__name": "Razón social", "@sage/xtrem-stock/pages__mrp_calculation_justification__results____columns__title__firstSuggestionDate": "Primera fecha de propuesta", "@sage/xtrem-stock/pages__mrp_calculation_justification__results____columns__title__item__id": "Id<PERSON> <PERSON> artí<PERSON>lo", "@sage/xtrem-stock/pages__mrp_calculation_justification__results____columns__title__itemName": "Nombre de artículo", "@sage/xtrem-stock/pages__mrp_calculation_justification__results____columns__title__site__id": "<PERSON><PERSON><PERSON> de planta", "@sage/xtrem-stock/pages__mrp_calculation_justification__results____columns__title__site__name": "Nombre de planta", "@sage/xtrem-stock/pages__mrp_calculation_justification__results____title": "Detalles", "@sage/xtrem-stock/pages__mrp_calculation_justification__startDate____title": "Fecha de inicio", "@sage/xtrem-stock/pages__mrp_calculation_justification__toItem____title": "<PERSON><PERSON>", "@sage/xtrem-stock/pages__mrp_calculation_justification__toItemText____title": "<PERSON><PERSON>", "@sage/xtrem-stock/pages__mrp_calculation_justification__user____columns__title__email": "E-mail", "@sage/xtrem-stock/pages__mrp_calculation_justification__user____columns__title__firstName": "Nombre", "@sage/xtrem-stock/pages__mrp_calculation_justification__user____columns__title__lastName": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__mrp_calculation_justification__user____lookupDialogTitle": "Se<PERSON><PERSON><PERSON>r usuario", "@sage/xtrem-stock/pages__mrp_calculation_justification__user____title": "Usuario", "@sage/xtrem-stock/pages__mrp_calculation_request____title": "Solicitud de cálculo MRP", "@sage/xtrem-stock/pages__mrp_calculation_request__cancelRequest____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__mrp_calculation_request__companies____columns__title__id": "Id.", "@sage/xtrem-stock/pages__mrp_calculation_request__companies____columns__title__name": "Nombre", "@sage/xtrem-stock/pages__mrp_calculation_request__companies____lookupDialogTitle": "Seleccionar sociedad", "@sage/xtrem-stock/pages__mrp_calculation_request__companies____placeholder": "Seleccionar...", "@sage/xtrem-stock/pages__mrp_calculation_request__companies____title": "Sociedad", "@sage/xtrem-stock/pages__mrp_calculation_request__company____columns__title__id": "Id.", "@sage/xtrem-stock/pages__mrp_calculation_request__company____columns__title__name": "Nombre", "@sage/xtrem-stock/pages__mrp_calculation_request__company____placeholder": "Seleccionar...", "@sage/xtrem-stock/pages__mrp_calculation_request__company____title": "Sociedad", "@sage/xtrem-stock/pages__mrp_calculation_request__criteriaBlock____title": "Criterios", "@sage/xtrem-stock/pages__mrp_calculation_request__description____title": "Descripción", "@sage/xtrem-stock/pages__mrp_calculation_request__explodeBillOfMaterial____title": "Expandir estructura de materiales", "@sage/xtrem-stock/pages__mrp_calculation_request__invalid_criteria": "No hay ningún registro de artículo-planta reaprovisionado mediante MRP que coincida con los criterios introducidos.", "@sage/xtrem-stock/pages__mrp_calculation_request__invalid_item_from_range": "El artículo inicial no puede ser posterior al artículo final.", "@sage/xtrem-stock/pages__mrp_calculation_request__invalid_item_to_range": "El artículo final no puede ser anterior al artículo inicial.", "@sage/xtrem-stock/pages__mrp_calculation_request__isSalesQuoteIncluded____title": "Incluir presupuestos", "@sage/xtrem-stock/pages__mrp_calculation_request__itemFrom____columns__title__description": "Descripción", "@sage/xtrem-stock/pages__mrp_calculation_request__itemFrom____columns__title__id": "Id.", "@sage/xtrem-stock/pages__mrp_calculation_request__itemFrom____columns__title__name": "Nombre", "@sage/xtrem-stock/pages__mrp_calculation_request__itemFrom____lookupDialogTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__mrp_calculation_request__itemFrom____placeholder": "Seleccionar...", "@sage/xtrem-stock/pages__mrp_calculation_request__itemFrom____title": "<PERSON><PERSON>", "@sage/xtrem-stock/pages__mrp_calculation_request__itemTo____columns__title__category__name": "Categoría", "@sage/xtrem-stock/pages__mrp_calculation_request__itemTo____lookupDialogTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__mrp_calculation_request__itemTo____placeholder": "Seleccionar...", "@sage/xtrem-stock/pages__mrp_calculation_request__itemTo____title": "<PERSON><PERSON>", "@sage/xtrem-stock/pages__mrp_calculation_request__mainSection____title": "General", "@sage/xtrem-stock/pages__mrp_calculation_request__notification_success": "La solicitud de cálculo MRP se ha enviado.", "@sage/xtrem-stock/pages__mrp_calculation_request__numberOfWeeks____title": "Periodo en semanas", "@sage/xtrem-stock/pages__mrp_calculation_request__scheduleMrpCalculationRequest____title": "Programar", "@sage/xtrem-stock/pages__mrp_calculation_request__sites____columns__title__id": "Id.", "@sage/xtrem-stock/pages__mrp_calculation_request__sites____columns__title__name": "Nombre", "@sage/xtrem-stock/pages__mrp_calculation_request__sites____lookupDialogTitle": "Seleccionar planta", "@sage/xtrem-stock/pages__mrp_calculation_request__sites____placeholder": "Seleccionar...", "@sage/xtrem-stock/pages__mrp_calculation_request__sites____title": "Planta", "@sage/xtrem-stock/pages__mrp_calculation_request__startDate____title": "Fecha de inicio", "@sage/xtrem-stock/pages__mrp_calculation_request__supply_chain_error": "Ya se están creando pedidos de compra desde cálculos MRP anteriores. Inténtalo de nuevo más tarde.", "@sage/xtrem-stock/pages__mrp_projected_stock_dialog____title": "Previsión de stock", "@sage/xtrem-stock/pages__mrp_projected_stock_dialog__batchQuantity____title": "Cantidad de tanda", "@sage/xtrem-stock/pages__mrp_projected_stock_dialog__chartBlock____title": "Previsión de stock", "@sage/xtrem-stock/pages__mrp_projected_stock_dialog__criteriaBlock____title": "Previsión de stock", "@sage/xtrem-stock/pages__mrp_projected_stock_dialog__day": "Día", "@sage/xtrem-stock/pages__mrp_projected_stock_dialog__economicOrderQuantity____title": "Cantidad económica de pedido", "@sage/xtrem-stock/pages__mrp_projected_stock_dialog__frequency____title": "Frecuencia", "@sage/xtrem-stock/pages__mrp_projected_stock_dialog__item____title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__mrp_projected_stock_dialog__lines____columns__title__bucket": "Periodo de tiempo", "@sage/xtrem-stock/pages__mrp_projected_stock_dialog__lines____columns__title__documentNumber": "Número de documento", "@sage/xtrem-stock/pages__mrp_projected_stock_dialog__lines____columns__title__documentType": "Tipo de documento", "@sage/xtrem-stock/pages__mrp_projected_stock_dialog__lines____columns__title__dueDate": "<PERSON><PERSON>nc<PERSON>o", "@sage/xtrem-stock/pages__mrp_projected_stock_dialog__lines____columns__title__quantity": "Cantidad", "@sage/xtrem-stock/pages__mrp_projected_stock_dialog__lines____columns__title__suggestion": "Propuesta", "@sage/xtrem-stock/pages__mrp_projected_stock_dialog__lines____title": "Detalles", "@sage/xtrem-stock/pages__mrp_projected_stock_dialog__mainSection____title": "Información general", "@sage/xtrem-stock/pages__mrp_projected_stock_dialog__numberOfWeeks____title": "Periodo en semanas", "@sage/xtrem-stock/pages__mrp_projected_stock_dialog__ok____title": "Aceptar", "@sage/xtrem-stock/pages__mrp_projected_stock_dialog__preferredProcess____title": "Proceso preferente", "@sage/xtrem-stock/pages__mrp_projected_stock_dialog__productionLeadTime____title": "Plazo de producción", "@sage/xtrem-stock/pages__mrp_projected_stock_dialog__projectedStockChart____chart__series__title": "Previsión de stock", "@sage/xtrem-stock/pages__mrp_projected_stock_dialog__projectedStockChart____chart__series__title__2": "<PERSON><PERSON> de seguridad", "@sage/xtrem-stock/pages__mrp_projected_stock_dialog__projectedStockChart____chart__xAxis__title": "<PERSON><PERSON>", "@sage/xtrem-stock/pages__mrp_projected_stock_dialog__projectedStockChart____title": "Previsión de stock", "@sage/xtrem-stock/pages__mrp_projected_stock_dialog__purchaseLeadTime____title": "<PERSON><PERSON><PERSON> de compra", "@sage/xtrem-stock/pages__mrp_projected_stock_dialog__safetyStock____title": "Stock de seguridad", "@sage/xtrem-stock/pages__mrp_projected_stock_dialog__site____title": "Planta", "@sage/xtrem-stock/pages__mrp_projected_stock_dialog__startDate____title": "Fecha de inicio", "@sage/xtrem-stock/pages__mrp_projected_stock_dialog__starting_stock": "Stock inicial", "@sage/xtrem-stock/pages__mrp_projected_stock_dialog__stockUnit____columns__title__decimalDigits": "", "@sage/xtrem-stock/pages__mrp_projected_stock_dialog__stockUnit____columns__title__symbol": "", "@sage/xtrem-stock/pages__mrp_projected_stock_dialog__week": "Se<PERSON>", "@sage/xtrem-stock/pages__mrp_synchronization____navigationPanel__listItem__lastSyncDateTime__title": "Última sincronización", "@sage/xtrem-stock/pages__mrp_synchronization____navigationPanel__listItem__titleRight__title": "Estado de última sincronización", "@sage/xtrem-stock/pages__mrp_synchronization____title": "Sincronización con MRP", "@sage/xtrem-stock/pages__mrp_synchronization___id____title": "Id.", "@sage/xtrem-stock/pages__mrp_synchronization__criteriaBlock____title": "Criterios", "@sage/xtrem-stock/pages__mrp_synchronization__lastSyncDateTime____title": "Última sincronización", "@sage/xtrem-stock/pages__mrp_synchronization__mainSection____title": "General", "@sage/xtrem-stock/pages__mrp_synchronization__resetSynchronization____title": "Todos los registros", "@sage/xtrem-stock/pages__mrp_synchronization__synchronizeDeletions____title": "Eliminaciones", "@sage/xtrem-stock/pages__mrp_synchronization__synchronizeMrp____title": "Sincronizar", "@sage/xtrem-stock/pages__mrp_synchronization__syncStatus____title": "Estado de última sincronización", "@sage/xtrem-stock/pages__mrp_synchronization__type____title": "Tipo", "@sage/xtrem-stock/pages__mrp_synchronization_request__notification_success": "La solicitud de sincronización con MRP se ha enviado.", "@sage/xtrem-stock/pages__serial_number_inquiry____navigationPanel__listItem__allocatedQuantity__title": "Cantidad asignada", "@sage/xtrem-stock/pages__serial_number_inquiry____navigationPanel__listItem__availableQuantity__title": "Cantidad disponible", "@sage/xtrem-stock/pages__serial_number_inquiry____navigationPanel__listItem__creationDate__title": "Fecha de creación de número de serie", "@sage/xtrem-stock/pages__serial_number_inquiry____navigationPanel__listItem__documentLink__title": "Asignación", "@sage/xtrem-stock/pages__serial_number_inquiry____navigationPanel__listItem__id__title": "Número de serie", "@sage/xtrem-stock/pages__serial_number_inquiry____navigationPanel__listItem__isAllocated__title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__serial_number_inquiry____navigationPanel__listItem__itemId__title": "Id<PERSON> <PERSON> artí<PERSON>lo", "@sage/xtrem-stock/pages__serial_number_inquiry____navigationPanel__listItem__itemName__title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__serial_number_inquiry____navigationPanel__listItem__locationId__title": "Id. de ubicación", "@sage/xtrem-stock/pages__serial_number_inquiry____navigationPanel__listItem__locationZone__title": "Área de almacenamiento", "@sage/xtrem-stock/pages__serial_number_inquiry____navigationPanel__listItem__lot__title": "Lote", "@sage/xtrem-stock/pages__serial_number_inquiry____navigationPanel__listItem__lotCreationDate__title": "Fecha de creación de lote", "@sage/xtrem-stock/pages__serial_number_inquiry____navigationPanel__listItem__lotExpirationDate__title": "<PERSON><PERSON> de caducidad de lote", "@sage/xtrem-stock/pages__serial_number_inquiry____navigationPanel__listItem__onHandQuantity__title": "Cantidad física", "@sage/xtrem-stock/pages__serial_number_inquiry____navigationPanel__listItem__owner__title": "Propietario", "@sage/xtrem-stock/pages__serial_number_inquiry____navigationPanel__listItem__siteId__title": "<PERSON><PERSON><PERSON> de planta", "@sage/xtrem-stock/pages__serial_number_inquiry____navigationPanel__listItem__siteName__title": "Planta", "@sage/xtrem-stock/pages__serial_number_inquiry____navigationPanel__listItem__stockLocation__title": "Ubicación", "@sage/xtrem-stock/pages__serial_number_inquiry____navigationPanel__listItem__stockStatus__title": "Control de calidad", "@sage/xtrem-stock/pages__serial_number_inquiry____navigationPanel__listItem__stockUnit__title": "Unidad", "@sage/xtrem-stock/pages__serial_number_inquiry____navigationPanel__listItem__subLot__title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__serial_number_inquiry____navigationPanel__listItem__supplierId__title": "<PERSON><PERSON>. de <PERSON>edor", "@sage/xtrem-stock/pages__serial_number_inquiry____navigationPanel__listItem__supplierLot__title": "<PERSON><PERSON><PERSON><PERSON> de lote de <PERSON>", "@sage/xtrem-stock/pages__serial_number_inquiry____navigationPanel__listItem__supplierName__columns__title__businessEntity__id": "Id.", "@sage/xtrem-stock/pages__serial_number_inquiry____navigationPanel__listItem__supplierName__columns__title__businessEntity__name": "Nombre", "@sage/xtrem-stock/pages__serial_number_inquiry____navigationPanel__listItem__supplierName__title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__serial_number_inquiry____navigationPanel__listItem__title__title": "Número de serie", "@sage/xtrem-stock/pages__serial_number_inquiry____navigationPanel__optionsMenu__title": "Todo", "@sage/xtrem-stock/pages__serial_number_inquiry____title": "Consulta de stock de números de serie", "@sage/xtrem-stock/pages__standard_cost_calculation____objectTypePlural": "Cálculos de coste estándar", "@sage/xtrem-stock/pages__standard_cost_calculation____objectTypeSingular": "Cálculo de coste estándar", "@sage/xtrem-stock/pages__standard_cost_calculation____title": "Cálculo de coste estándar", "@sage/xtrem-stock/pages__standard_cost_calculation__button_be_notified": "Notificar", "@sage/xtrem-stock/pages__standard_cost_calculation__button_wait": "<PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__standard_cost_calculation__calculation_dialog_content": "El cálculo puede llevar un tiempo. ¿Quieres esperar o prefieres salir de la página y recibir una notificación cuando esté listo?", "@sage/xtrem-stock/pages__standard_cost_calculation__commodityCode____title": "Código de mercancías", "@sage/xtrem-stock/pages__standard_cost_calculation__cost_creation_content": "Crear los registros de los costes puede llevar un tiempo. ¿Quieres esperar o prefieres salir de la página y recibir una notificación cuando estén listos?", "@sage/xtrem-stock/pages__standard_cost_calculation__costCategory____columns__title__costCategoryType": "Tipo", "@sage/xtrem-stock/pages__standard_cost_calculation__costCategory____columns__title__isMandatory": "Obligatoria", "@sage/xtrem-stock/pages__standard_cost_calculation__costCategory____lookupDialogTitle": "Seleccionar categoría de coste", "@sage/xtrem-stock/pages__standard_cost_calculation__costCategory____title": "Categoría de coste", "@sage/xtrem-stock/pages__standard_cost_calculation__criteriaBlock____title": "Criterios", "@sage/xtrem-stock/pages__standard_cost_calculation__defaultDimension____title": "Definir secciones por defecto", "@sage/xtrem-stock/pages__standard_cost_calculation__dimensions_updated": "Las secciones se han actualizado.", "@sage/xtrem-stock/pages__standard_cost_calculation__fromDate____title": "Fecha de inicio", "@sage/xtrem-stock/pages__standard_cost_calculation__includesRouting____title": "Incluir ruta", "@sage/xtrem-stock/pages__standard_cost_calculation__isAllSelected____title": "Seleccionar todas las líneas", "@sage/xtrem-stock/pages__standard_cost_calculation__itemCategory____columns__title__type": "Tipo", "@sage/xtrem-stock/pages__standard_cost_calculation__itemCategory____lookupDialogTitle": "Seleccionar categoría de artículo", "@sage/xtrem-stock/pages__standard_cost_calculation__itemCategory____title": "Categoría de artículo", "@sage/xtrem-stock/pages__standard_cost_calculation__items____columns__title__id": "Id.", "@sage/xtrem-stock/pages__standard_cost_calculation__items____columns__title__name": "Nombre", "@sage/xtrem-stock/pages__standard_cost_calculation__items____lookupDialogTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__standard_cost_calculation__items____title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__standard_cost_calculation__linesSection____title": "Líneas de resultados", "@sage/xtrem-stock/pages__standard_cost_calculation__mainSection____title": "General", "@sage/xtrem-stock/pages__standard_cost_calculation__quantity____title": "Cantidad", "@sage/xtrem-stock/pages__standard_cost_calculation__request_notification_item_site_cost_creation_finished": "La creación del coste de artículo-planta ha finalizado.", "@sage/xtrem-stock/pages__standard_cost_calculation__request_notification_item_site_cost_creation_impossible": "Primero ejecuta el cálculo.", "@sage/xtrem-stock/pages__standard_cost_calculation__request_notification_item_site_cost_creation_request_sent": "La solicitud de creación del coste de artículo-planta se ha enviado.", "@sage/xtrem-stock/pages__standard_cost_calculation__resultLines____columns__title__creationErrorMessage": "<PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__standard_cost_calculation__resultLines____columns__title__creationStatus": "Estado de creación de coste", "@sage/xtrem-stock/pages__standard_cost_calculation__resultLines____columns__title__currentLaborCost": "Coste de mano de obra actual", "@sage/xtrem-stock/pages__standard_cost_calculation__resultLines____columns__title__currentMachineCost": "Coste de máquina actual", "@sage/xtrem-stock/pages__standard_cost_calculation__resultLines____columns__title__currentMaterialCost": "Coste de material actual", "@sage/xtrem-stock/pages__standard_cost_calculation__resultLines____columns__title__currentToolCost": "Coste de herramienta actual", "@sage/xtrem-stock/pages__standard_cost_calculation__resultLines____columns__title__currentTotalCost": "Coste total actual", "@sage/xtrem-stock/pages__standard_cost_calculation__resultLines____columns__title__item__description": "Descripción de artículo", "@sage/xtrem-stock/pages__standard_cost_calculation__resultLines____columns__title__item__id": "Id<PERSON> <PERSON> artí<PERSON>lo", "@sage/xtrem-stock/pages__standard_cost_calculation__resultLines____columns__title__item__name": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__standard_cost_calculation__resultLines____columns__title__laborCost": "Coste de mano de obra", "@sage/xtrem-stock/pages__standard_cost_calculation__resultLines____columns__title__machineCost": "Coste de m<PERSON>", "@sage/xtrem-stock/pages__standard_cost_calculation__resultLines____columns__title__materialCost": "Coste de material", "@sage/xtrem-stock/pages__standard_cost_calculation__resultLines____columns__title__toolCost": "Coste de <PERSON>", "@sage/xtrem-stock/pages__standard_cost_calculation__resultLines____columns__title__totalCost": "Coste total", "@sage/xtrem-stock/pages__standard_cost_calculation__resultLines____dropdownActions__title": "Subconjuntos", "@sage/xtrem-stock/pages__standard_cost_calculation__resultLines____dropdownActions__title__2": "Secciones", "@sage/xtrem-stock/pages__standard_cost_calculation__runCreateItemSiteCostsFromRollUpResults____title": "<PERSON><PERSON><PERSON> coste de artículo-planta", "@sage/xtrem-stock/pages__standard_cost_calculation__runStandardCostCalculation____title": "Calcular", "@sage/xtrem-stock/pages__standard_cost_calculation__site____columns__columns__legalCompany__name__title": "Divisa", "@sage/xtrem-stock/pages__standard_cost_calculation__site____columns__title__id": "Id. ", "@sage/xtrem-stock/pages__standard_cost_calculation__site____columns__title__legalCompany__name": "Sociedad", "@sage/xtrem-stock/pages__standard_cost_calculation__site____columns__title__name": "Nombre", "@sage/xtrem-stock/pages__standard_cost_calculation__site____lookupDialogTitle": "Seleccionar planta", "@sage/xtrem-stock/pages__standard_cost_calculation__site____title": "Planta", "@sage/xtrem-stock/pages__standard_cost_calculation__status____title": "Estado", "@sage/xtrem-stock/pages__standard_cost_calculation__title_wait_for_finish": "Calcular", "@sage/xtrem-stock/pages__standard_cost_calculation__user____columns__title__email": "E-mail", "@sage/xtrem-stock/pages__standard_cost_calculation__user____columns__title__firstName": "Nombre", "@sage/xtrem-stock/pages__standard_cost_calculation__user____columns__title__lastName": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__standard_cost_calculation__user____lookupDialogTitle": "Se<PERSON><PERSON><PERSON>r usuario", "@sage/xtrem-stock/pages__standard_cost_calculation__user____title": "Usuario", "@sage/xtrem-stock/pages__standard_cost_calculation__usesComponentStandardCost____title": "Coste estándar de componente", "@sage/xtrem-stock/pages__standard_cost_calculation__wait_for_creation": "<PERSON><PERSON><PERSON> coste de artículo-planta", "@sage/xtrem-stock/pages__standard_cost_calculation_request__notification_request_sent": "La solicitud de cálculo de coste estándar se ha enviado.", "@sage/xtrem-stock/pages__standard_cost_calculation_request__notification_valuation_failed": "Ha habido un error al calcular el coste estándar.", "@sage/xtrem-stock/pages__standard_cost_calculation_request__notification_valuation_finished": "El cálculo de coste estándar ha finalizado.", "@sage/xtrem-stock/pages__stock__adjustment_step_sequence_detail_stock": "Detallar stock", "@sage/xtrem-stock/pages__stock_adjustment____navigationPanel__dropdownActions__title": "Definir secci<PERSON>", "@sage/xtrem-stock/pages__stock_adjustment____navigationPanel__dropdownActions__title__2": "Eliminar", "@sage/xtrem-stock/pages__stock_adjustment____navigationPanel__listItem__description__title": "Descripción", "@sage/xtrem-stock/pages__stock_adjustment____navigationPanel__listItem__line2__title": "Planta", "@sage/xtrem-stock/pages__stock_adjustment____navigationPanel__listItem__line2Right__title": "<PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_adjustment____navigationPanel__listItem__reasonCode__title": "Motivo de regularización", "@sage/xtrem-stock/pages__stock_adjustment____navigationPanel__listItem__title__title": "Número", "@sage/xtrem-stock/pages__stock_adjustment____navigationPanel__listItem__titleRight__title": "Estado", "@sage/xtrem-stock/pages__stock_adjustment____navigationPanel__optionsMenu__title": "Abiertas", "@sage/xtrem-stock/pages__stock_adjustment____navigationPanel__optionsMenu__title__2": "<PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_adjustment____navigationPanel__optionsMenu__title__3": "Detalles pendientes", "@sage/xtrem-stock/pages__stock_adjustment____navigationPanel__optionsMenu__title__4": "Detalles introducidos", "@sage/xtrem-stock/pages__stock_adjustment____navigationPanel__optionsMenu__title__5": "Contabilización de stock en curso", "@sage/xtrem-stock/pages__stock_adjustment____navigationPanel__optionsMenu__title__6": "Error de contabilización de stock", "@sage/xtrem-stock/pages__stock_adjustment____navigationPanel__optionsMenu__title__7": "Regularizado", "@sage/xtrem-stock/pages__stock_adjustment____objectTypePlural": "Regularizaciones de stock", "@sage/xtrem-stock/pages__stock_adjustment____objectTypeSingular": "Regularización de stock", "@sage/xtrem-stock/pages__stock_adjustment____title": "Regularización de stock", "@sage/xtrem-stock/pages__stock_adjustment__apply_dimensions_success": "Las secciones se han aplicado.", "@sage/xtrem-stock/pages__stock_adjustment__defaultDimension____title": "Definir secci<PERSON>", "@sage/xtrem-stock/pages__stock_adjustment__description____title": "Descripción", "@sage/xtrem-stock/pages__stock_adjustment__displayStatus____title": "Estado", "@sage/xtrem-stock/pages__stock_adjustment__effectiveDate____title": "<PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_adjustment__goToSysNotificationPage____title": "Reintentar", "@sage/xtrem-stock/pages__stock_adjustment__line_delete_action_dialog_content": "¿Quieres eliminar esta línea de regularización de stock?", "@sage/xtrem-stock/pages__stock_adjustment__line_delete_action_dialog_title": "Confirmar eliminación", "@sage/xtrem-stock/pages__stock_adjustment__lines____columns__columns__item__name__title": "Categoría", "@sage/xtrem-stock/pages__stock_adjustment__lines____columns__columns__item__name__title__2": "Unidad", "@sage/xtrem-stock/pages__stock_adjustment__lines____columns__columns__item__name__title__3": "Gestión de lotes", "@sage/xtrem-stock/pages__stock_adjustment__lines____columns__columns__location__name__title": "Nombre", "@sage/xtrem-stock/pages__stock_adjustment__lines____columns__columns__location__name__title__2": "Id.", "@sage/xtrem-stock/pages__stock_adjustment__lines____columns__columns__lot__id__columns__title__id": "Id.", "@sage/xtrem-stock/pages__stock_adjustment__lines____columns__columns__lot__id__columns__title__name": "Nombre", "@sage/xtrem-stock/pages__stock_adjustment__lines____columns__columns__lot__id__title": "Id.", "@sage/xtrem-stock/pages__stock_adjustment__lines____columns__columns__lot__id__title__2": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_adjustment__lines____columns__columns__lot__id__title__3": "<PERSON><PERSON> de caducidad", "@sage/xtrem-stock/pages__stock_adjustment__lines____columns__columns__stockStatus__name__title": "Nombre", "@sage/xtrem-stock/pages__stock_adjustment__lines____columns__columns__stockStatus__name__title__2": "Id.", "@sage/xtrem-stock/pages__stock_adjustment__lines____columns__columns__stockStatus__title": "Nombre", "@sage/xtrem-stock/pages__stock_adjustment__lines____columns__columns__stockStatus__title__2": "Id.", "@sage/xtrem-stock/pages__stock_adjustment__lines____columns__lookupDialogTitle__item__name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_adjustment__lines____columns__lookupDialogTitle__location__name": "Seleccionar ubicación", "@sage/xtrem-stock/pages__stock_adjustment__lines____columns__lookupDialogTitle__lot__id": "<PERSON><PERSON><PERSON><PERSON><PERSON> lote", "@sage/xtrem-stock/pages__stock_adjustment__lines____columns__lookupDialogTitle__stockStatus": "Se<PERSON><PERSON><PERSON>r valor de calidad", "@sage/xtrem-stock/pages__stock_adjustment__lines____columns__lookupDialogTitle__stockStatus__name": "Seleccionar estado de stock", "@sage/xtrem-stock/pages__stock_adjustment__lines____columns__placeholder__item__name": "Seleccionar...", "@sage/xtrem-stock/pages__stock_adjustment__lines____columns__placeholder__lot__id": "Seleccionar...", "@sage/xtrem-stock/pages__stock_adjustment__lines____columns__title__2": "Detalles", "@sage/xtrem-stock/pages__stock_adjustment__lines____columns__title__adjustmentQuantityInStockUnit": "Cantidad de regularización", "@sage/xtrem-stock/pages__stock_adjustment__lines____columns__title__displayStatus": "Estado", "@sage/xtrem-stock/pages__stock_adjustment__lines____columns__title__item__description": "Descripción de artículo", "@sage/xtrem-stock/pages__stock_adjustment__lines____columns__title__item__id": "Id<PERSON> <PERSON> artí<PERSON>lo", "@sage/xtrem-stock/pages__stock_adjustment__lines____columns__title__item__name": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_adjustment__lines____columns__title__location__name": "Ubicación", "@sage/xtrem-stock/pages__stock_adjustment__lines____columns__title__lot__id": "Lote", "@sage/xtrem-stock/pages__stock_adjustment__lines____columns__title__newStockQuantity": "Nueva cantidad de stock", "@sage/xtrem-stock/pages__stock_adjustment__lines____columns__title__quantityInStockUnit": "Cantidad de stock", "@sage/xtrem-stock/pages__stock_adjustment__lines____columns__title__stockDetailStatus": "", "@sage/xtrem-stock/pages__stock_adjustment__lines____columns__title__stockStatus": "Control de calidad", "@sage/xtrem-stock/pages__stock_adjustment__lines____columns__title__stockStatus__name": "Estado", "@sage/xtrem-stock/pages__stock_adjustment__lines____columns__title__stockValue": "Valor de stock", "@sage/xtrem-stock/pages__stock_adjustment__lines____columns__title__unitCost": "Coste unitario", "@sage/xtrem-stock/pages__stock_adjustment__lines____dropdownActions__title": "Detalles de stock", "@sage/xtrem-stock/pages__stock_adjustment__lines____dropdownActions__title__2": "Secciones", "@sage/xtrem-stock/pages__stock_adjustment__lines____dropdownActions__title__3": "Eliminar", "@sage/xtrem-stock/pages__stock_adjustment__lines____inlineActions__title": "Abrir panel de línea", "@sage/xtrem-stock/pages__stock_adjustment__lines____mobileCard__line2__title": "Descripción", "@sage/xtrem-stock/pages__stock_adjustment__lines____mobileCard__title__title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_adjustment__lines____mobileCard__titleRight__title": "Estado", "@sage/xtrem-stock/pages__stock_adjustment__lines____optionsMenu__title": "<PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_adjustment__lines____optionsMenu__title__2": "Abiertas", "@sage/xtrem-stock/pages__stock_adjustment__lines____optionsMenu__title__3": "Detalles pendientes", "@sage/xtrem-stock/pages__stock_adjustment__lines____optionsMenu__title__4": "Detalles introducidos", "@sage/xtrem-stock/pages__stock_adjustment__lines____optionsMenu__title__5": "Contabilización de stock en curso", "@sage/xtrem-stock/pages__stock_adjustment__lines____optionsMenu__title__6": "Error de contabilización de stock", "@sage/xtrem-stock/pages__stock_adjustment__lines____optionsMenu__title__7": "Regularizadas", "@sage/xtrem-stock/pages__stock_adjustment__lines____sidebar__headerDropdownActions__title": "Detalles de stock", "@sage/xtrem-stock/pages__stock_adjustment__lines____sidebar__headerDropdownActions__title__2": "Secciones", "@sage/xtrem-stock/pages__stock_adjustment__lines____sidebar__headerDropdownActions__title__3": "Eliminar", "@sage/xtrem-stock/pages__stock_adjustment__lines____title": "Líneas", "@sage/xtrem-stock/pages__stock_adjustment__linesSection____title": "Líneas", "@sage/xtrem-stock/pages__stock_adjustment__mainSection____title": "General", "@sage/xtrem-stock/pages__stock_adjustment__number____title": "Número", "@sage/xtrem-stock/pages__stock_adjustment__post____title": "Contabilizar stock", "@sage/xtrem-stock/pages__stock_adjustment__postingDetails____columns__title__financeIntegrationAppRecordId": "Referencia de integración contable", "@sage/xtrem-stock/pages__stock_adjustment__postingDetails____columns__title__postingStatus": "Estado", "@sage/xtrem-stock/pages__stock_adjustment__postingDetails____columns__title__targetDocumentNumber": "Número de documento", "@sage/xtrem-stock/pages__stock_adjustment__postingDetails____columns__title__targetDocumentType": "Tipo de documento", "@sage/xtrem-stock/pages__stock_adjustment__postingDetails____title": "Resul<PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_adjustment__postingMessageBlock____title": "Detalles de error", "@sage/xtrem-stock/pages__stock_adjustment__postingSection____title": "Contabilización", "@sage/xtrem-stock/pages__stock_adjustment__reasonCode____columns__title__quantityDecreaseAdjustment": "Disminución de cantidad", "@sage/xtrem-stock/pages__stock_adjustment__reasonCode____columns__title__quantityIncreaseAdjustment": "Aumento de cantidad", "@sage/xtrem-stock/pages__stock_adjustment__reasonCode____lookupDialogTitle": "Seleccionar código de motivo", "@sage/xtrem-stock/pages__stock_adjustment__reasonCode____title": "Motivo de regularización", "@sage/xtrem-stock/pages__stock_adjustment__repost____title": "Volver a contabilizar", "@sage/xtrem-stock/pages__stock_adjustment__repost_errors": "Errores al volver a contabilizar:", "@sage/xtrem-stock/pages__stock_adjustment__resync_display_status_continue": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_adjustment__resync_display_status_message": "¿Quieres actualizar el estado de stock de esta regularización a \"Finalizado\"?", "@sage/xtrem-stock/pages__stock_adjustment__resync_display_status_title": "Estado de transacción de stock", "@sage/xtrem-stock/pages__stock_adjustment__saveStockAdjustment____title": "Guardar", "@sage/xtrem-stock/pages__stock_adjustment__stock_journal_inquiry": "Consulta de diario de stock", "@sage/xtrem-stock/pages__stock_adjustment__stock_posting_error": "Error de contabilización de stock", "@sage/xtrem-stock/pages__stock_adjustment__stockSite____columns__columns__legalCompany__name__columns__title__symbol": "Símbolo", "@sage/xtrem-stock/pages__stock_adjustment__stockSite____lookupDialogTitle": "Seleccionar planta", "@sage/xtrem-stock/pages__stock_adjustment__stockSite____placeholder": "Seleccionar...", "@sage/xtrem-stock/pages__stock_adjustment__stockSite____title": "Planta", "@sage/xtrem-stock/pages__stock_adjustment__stockTransactionStatus____title": "Estado de stock", "@sage/xtrem-stock/pages__stock_adjustment__The_lot_is_mandatory": "Introduce el lote.", "@sage/xtrem-stock/pages__stock_adjustment_post__stock_details_required": "Introduce los detalles de stock en todas las líneas para poder contabilizar.", "@sage/xtrem-stock/pages__stock_adjustment_step_sequence_creation": "<PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_adjustment_step_sequence_post_stock": "Contabilizar stock", "@sage/xtrem-stock/pages__stock_allocation_inquiry____navigationPanel__listItem__documentLink__title": "Número de documento", "@sage/xtrem-stock/pages__stock_allocation_inquiry____navigationPanel__listItem__quantityInStockUnit__title": "Cantidad asignada", "@sage/xtrem-stock/pages__stock_allocation_inquiry____navigationPanel__listItem__title__title": "Número de documento", "@sage/xtrem-stock/pages__stock_allocation_inquiry____title": "Consulta de asignación de stock", "@sage/xtrem-stock/pages__stock_avc_justification_inquiry____title": "Justificación de precio medio ponderado", "@sage/xtrem-stock/pages__stock_avc_justification_inquiry__company____lookupDialogTitle": "Seleccionar sociedad", "@sage/xtrem-stock/pages__stock_avc_justification_inquiry__company____placeholder": "Seleccionar...", "@sage/xtrem-stock/pages__stock_avc_justification_inquiry__company____title": "Sociedad", "@sage/xtrem-stock/pages__stock_avc_justification_inquiry__criteriaBlock____title": "Criterios", "@sage/xtrem-stock/pages__stock_avc_justification_inquiry__itemFrom____columns__title__category__name": "Categoría", "@sage/xtrem-stock/pages__stock_avc_justification_inquiry__itemFrom____lookupDialogTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_avc_justification_inquiry__itemFrom____placeholder": "Seleccionar...", "@sage/xtrem-stock/pages__stock_avc_justification_inquiry__itemFrom____title": "<PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_avc_justification_inquiry__itemSelection____columns__title__item__description": "Descripción de artículo", "@sage/xtrem-stock/pages__stock_avc_justification_inquiry__itemSelection____columns__title__item__id": "Id<PERSON> <PERSON> artí<PERSON>lo", "@sage/xtrem-stock/pages__stock_avc_justification_inquiry__itemSelection____columns__title__item__name": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_avc_justification_inquiry__itemSelection____columns__title__site__name": "Planta", "@sage/xtrem-stock/pages__stock_avc_justification_inquiry__itemSelection____columns__title__stockUnit__name": "Unidad de medida", "@sage/xtrem-stock/pages__stock_avc_justification_inquiry__itemSelection____title": "Resul<PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_avc_justification_inquiry__itemTo____columns__title__category__name": "Categoría", "@sage/xtrem-stock/pages__stock_avc_justification_inquiry__itemTo____columns__title__description": "Descripción", "@sage/xtrem-stock/pages__stock_avc_justification_inquiry__itemTo____columns__title__id": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_avc_justification_inquiry__itemTo____columns__title__name": "Nombre", "@sage/xtrem-stock/pages__stock_avc_justification_inquiry__itemTo____lookupDialogTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_avc_justification_inquiry__itemTo____placeholder": "Seleccionar...", "@sage/xtrem-stock/pages__stock_avc_justification_inquiry__itemTo____title": "<PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_avc_justification_inquiry__itemValues____levels__columns__columns__stockUnit__symbol__title": "Símbolo", "@sage/xtrem-stock/pages__stock_avc_justification_inquiry__itemValues____levels__columns__columns__stockUnit__symbol__title__2": "Símbolo", "@sage/xtrem-stock/pages__stock_avc_justification_inquiry__itemValues____levels__columns__title___createStamp": "<PERSON><PERSON> de movimiento", "@sage/xtrem-stock/pages__stock_avc_justification_inquiry__itemValues____levels__columns__title__amountVariance": "Desviación de importe", "@sage/xtrem-stock/pages__stock_avc_justification_inquiry__itemValues____levels__columns__title__company__name": "Sociedad", "@sage/xtrem-stock/pages__stock_avc_justification_inquiry__itemValues____levels__columns__title__cost": "Coste unitario", "@sage/xtrem-stock/pages__stock_avc_justification_inquiry__itemValues____levels__columns__title__costVariance": "Desviación de coste", "@sage/xtrem-stock/pages__stock_avc_justification_inquiry__itemValues____levels__columns__title__creationDate": "Fecha de creación", "@sage/xtrem-stock/pages__stock_avc_justification_inquiry__itemValues____levels__columns__title__currency__symbol": "Divisa", "@sage/xtrem-stock/pages__stock_avc_justification_inquiry__itemValues____levels__columns__title__dateOfValuation": "Fecha de valoración", "@sage/xtrem-stock/pages__stock_avc_justification_inquiry__itemValues____levels__columns__title__documentLine___constructor": "Tipo", "@sage/xtrem-stock/pages__stock_avc_justification_inquiry__itemValues____levels__columns__title__documentLine__documentNumber": "Documento", "@sage/xtrem-stock/pages__stock_avc_justification_inquiry__itemValues____levels__columns__title__effectiveDate": "<PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_avc_justification_inquiry__itemValues____levels__columns__title__item__id": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_avc_justification_inquiry__itemValues____levels__columns__title__item__name": "Nombre", "@sage/xtrem-stock/pages__stock_avc_justification_inquiry__itemValues____levels__columns__title__movementAmount": "Importe de movimiento", "@sage/xtrem-stock/pages__stock_avc_justification_inquiry__itemValues____levels__columns__title__orderAmount": "Importe de pedido", "@sage/xtrem-stock/pages__stock_avc_justification_inquiry__itemValues____levels__columns__title__orderCost": "Coste de pedido", "@sage/xtrem-stock/pages__stock_avc_justification_inquiry__itemValues____levels__columns__title__postingClass__name": "Clase contable", "@sage/xtrem-stock/pages__stock_avc_justification_inquiry__itemValues____levels__columns__title__quantityInStock": "Cantidad", "@sage/xtrem-stock/pages__stock_avc_justification_inquiry__itemValues____levels__columns__title__quantityInStockUnit": "Cantidad", "@sage/xtrem-stock/pages__stock_avc_justification_inquiry__itemValues____levels__columns__title__sequence": "Secuencia", "@sage/xtrem-stock/pages__stock_avc_justification_inquiry__itemValues____levels__columns__title__site__name": "Planta", "@sage/xtrem-stock/pages__stock_avc_justification_inquiry__itemValues____levels__columns__title__stockDetail__documentLine__documentNumber": "Documento", "@sage/xtrem-stock/pages__stock_avc_justification_inquiry__itemValues____levels__columns__title__stockQuantity": "Cantidad de stock", "@sage/xtrem-stock/pages__stock_avc_justification_inquiry__itemValues____levels__columns__title__stockUnit__name": "Unidad de medida", "@sage/xtrem-stock/pages__stock_avc_justification_inquiry__itemValues____levels__columns__title__stockUnit__symbol": "Unidad de stock", "@sage/xtrem-stock/pages__stock_avc_justification_inquiry__itemValues____levels__columns__title__stockValue": "Valor de stock", "@sage/xtrem-stock/pages__stock_avc_justification_inquiry__itemValues____levels__columns__title__stockValue__2": "Valor de stock", "@sage/xtrem-stock/pages__stock_avc_justification_inquiry__itemValues____levels__columns__title__type": "Tipo de documento", "@sage/xtrem-stock/pages__stock_avc_justification_inquiry__itemValues____levels__columns__title__unitCost": "Coste unitario", "@sage/xtrem-stock/pages__stock_avc_justification_inquiry__itemValues____levels__columns__title__valuationMethod": "Tipo de coste", "@sage/xtrem-stock/pages__stock_avc_justification_inquiry__itemValues____levels__columns__title__valuedCost": "Coste valorado", "@sage/xtrem-stock/pages__stock_avc_justification_inquiry__itemValues____levels__columns__title__variance": "Desviación", "@sage/xtrem-stock/pages__stock_avc_justification_inquiry__mainSection____title": "General", "@sage/xtrem-stock/pages__stock_avc_justification_inquiry__runStockValuation____title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_avc_justification_inquiry__sites____lookupDialogTitle": "Seleccionar planta", "@sage/xtrem-stock/pages__stock_avc_justification_inquiry__sites____placeholder": "Seleccionar...", "@sage/xtrem-stock/pages__stock_avc_justification_inquiry__sites____title": "Planta", "@sage/xtrem-stock/pages__stock_avc_justification_inquiry__valuationDate____title": "<PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_change____navigationPanel__listItem__description__title": "Descripción", "@sage/xtrem-stock/pages__stock_change____navigationPanel__listItem__effectiveDate__title": "<PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_change____navigationPanel__listItem__itemDescription__title": "Descripción de artículo", "@sage/xtrem-stock/pages__stock_change____navigationPanel__listItem__itemId__title": "Id<PERSON> <PERSON> artí<PERSON>lo", "@sage/xtrem-stock/pages__stock_change____navigationPanel__listItem__line2__title": "Nombre de planta", "@sage/xtrem-stock/pages__stock_change____navigationPanel__listItem__line2Right__title": "<PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_change____navigationPanel__listItem__line3__title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_change____navigationPanel__listItem__siteID__title": "<PERSON><PERSON><PERSON> de planta", "@sage/xtrem-stock/pages__stock_change____navigationPanel__listItem__stockSite__title": "Planta", "@sage/xtrem-stock/pages__stock_change____navigationPanel__listItem__title__title": "Número", "@sage/xtrem-stock/pages__stock_change____objectTypePlural": "Cambios de stock", "@sage/xtrem-stock/pages__stock_change____objectTypeSingular": "Cambio de stock", "@sage/xtrem-stock/pages__stock_change____subtitle": "Cambio de stock", "@sage/xtrem-stock/pages__stock_change____title": "Cambio de stock", "@sage/xtrem-stock/pages__stock_change__addLine____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_change__availableQuantity____title": "Cantidad disponible", "@sage/xtrem-stock/pages__stock_change__description____title": "Descripción", "@sage/xtrem-stock/pages__stock_change__effectiveDate____title": "<PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_change__initialStockBlock____title": "Stock inicial", "@sage/xtrem-stock/pages__stock_change__item____columns__title__category__name": "Categoría", "@sage/xtrem-stock/pages__stock_change__item____lookupDialogTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_change__item____title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_change__lines____columns__columns__endingSerialNumber__id__title": "Número de serie de proveedor", "@sage/xtrem-stock/pages__stock_change__lines____columns__columns__endingSerialNumber__id__title__2": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_change__lines____columns__columns__location__name__title": "Nombre", "@sage/xtrem-stock/pages__stock_change__lines____columns__columns__location__name__title__2": "Id.", "@sage/xtrem-stock/pages__stock_change__lines____columns__columns__startingSerialNumber__id__title": "Número de serie de proveedor", "@sage/xtrem-stock/pages__stock_change__lines____columns__columns__startingSerialNumber__id__title__2": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_change__lines____columns__columns__stockStatus__name__title": "Nombre", "@sage/xtrem-stock/pages__stock_change__lines____columns__columns__stockStatus__name__title__2": "Id.", "@sage/xtrem-stock/pages__stock_change__lines____columns__lookupDialogTitle__startingSerialNumber__id": "Desde número de serie", "@sage/xtrem-stock/pages__stock_change__lines____columns__lookupDialogTitle__stockStatus": "Se<PERSON><PERSON><PERSON>r valor de calidad", "@sage/xtrem-stock/pages__stock_change__lines____columns__title__endingSerialNumber__id": "Hasta número de serie", "@sage/xtrem-stock/pages__stock_change__lines____columns__title__location__name": "Ubicación", "@sage/xtrem-stock/pages__stock_change__lines____columns__title__quantityInStockUnit": "Cantidad", "@sage/xtrem-stock/pages__stock_change__lines____columns__title__startingSerialNumber__id": "Desde número de serie", "@sage/xtrem-stock/pages__stock_change__lines____columns__title__stockStatus": "Control de calidad", "@sage/xtrem-stock/pages__stock_change__lines____columns__title__stockStatus__name": "Estado", "@sage/xtrem-stock/pages__stock_change__lines____columns__title__stockTransactionStatus": "Estado de stock", "@sage/xtrem-stock/pages__stock_change__lines____dropdownActions__title": "Eliminar", "@sage/xtrem-stock/pages__stock_change__lines____title": "Cambios", "@sage/xtrem-stock/pages__stock_change__location____columns__title__id": "Id.", "@sage/xtrem-stock/pages__stock_change__location____columns__title__name": "Nombre", "@sage/xtrem-stock/pages__stock_change__location____title": "Ubicación", "@sage/xtrem-stock/pages__stock_change__lot____columns__title__expirationDate": "<PERSON><PERSON> de caducidad", "@sage/xtrem-stock/pages__stock_change__lot____title": "Lote", "@sage/xtrem-stock/pages__stock_change__mainSection____title": "General", "@sage/xtrem-stock/pages__stock_change__number____title": "Número", "@sage/xtrem-stock/pages__stock_change__owner____title": "Propietario", "@sage/xtrem-stock/pages__stock_change__post____title": "Contabilizar", "@sage/xtrem-stock/pages__stock_change__postingError____title": "Error de contabilización", "@sage/xtrem-stock/pages__stock_change__saveStockChange____title": "Guardar", "@sage/xtrem-stock/pages__stock_change__status____title": "Estado", "@sage/xtrem-stock/pages__stock_change__stockRecord____columns__columns__location__name__title": "Á<PERSON>", "@sage/xtrem-stock/pages__stock_change__stockRecord____columns__columns__site__name__title": "Gestión de ubicación", "@sage/xtrem-stock/pages__stock_change__stockRecord____columns__title__item__name": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_change__stockRecord____columns__title__location__name": "Ubicación", "@sage/xtrem-stock/pages__stock_change__stockRecord____columns__title__lot__id": "Lote", "@sage/xtrem-stock/pages__stock_change__stockRecord____columns__title__owner": "Propietario", "@sage/xtrem-stock/pages__stock_change__stockRecord____columns__title__quantityInStockUnit": "Cantidad", "@sage/xtrem-stock/pages__stock_change__stockRecord____columns__title__site__name": "Planta", "@sage/xtrem-stock/pages__stock_change__stockRecord____columns__title__status__name": "Estado", "@sage/xtrem-stock/pages__stock_change__stockRecord____columns__title__totalAllocated": "<PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_change__stockRecord____title": "Stock", "@sage/xtrem-stock/pages__stock_change__stockSite____columns__title__isLocationManaged": "Gestión de ubicación", "@sage/xtrem-stock/pages__stock_change__stockSite____lookupDialogTitle": "Seleccionar planta", "@sage/xtrem-stock/pages__stock_change__stockSite____title": "Planta", "@sage/xtrem-stock/pages__stock_change__stockStatus____columns__title__id": "Id.", "@sage/xtrem-stock/pages__stock_change__stockStatus____columns__title__name": "Nombre", "@sage/xtrem-stock/pages__stock_change__stockStatus____lookupDialogTitle": "Se<PERSON><PERSON><PERSON>r valor de calidad", "@sage/xtrem-stock/pages__stock_change__stockStatus____title": "Control de calidad", "@sage/xtrem-stock/pages__stock_change__stockTransactionStatus____title": "Estado de stock", "@sage/xtrem-stock/pages__stock_change__totalQuantity____title": "Cantidad total", "@sage/xtrem-stock/pages__stock_change_available_error": "Números de serie disponibles en este rango: {{available}}", "@sage/xtrem-stock/pages__stock_change_duplicate_error": "El número de serie ya está incluido en otro rango.", "@sage/xtrem-stock/pages__stock_change_duplicate_range_error": "Uno de los números de serie ya está incluido en otro rango.", "@sage/xtrem-stock/pages__stock_change_missing_from_serial_number": "Introduce el número de inicio del rango de números de serie.", "@sage/xtrem-stock/pages__stock_change_zero_quantity_error": "Introduce una cantidad superior a 0.", "@sage/xtrem-stock/pages__stock_count____navigationPanel__listItem__counter__title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_count____navigationPanel__listItem__line2__title": "Descripción", "@sage/xtrem-stock/pages__stock_count____navigationPanel__listItem__line2Right__title": "<PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_count____navigationPanel__listItem__stockSite__title": "Planta", "@sage/xtrem-stock/pages__stock_count____navigationPanel__listItem__stockTransactionStatus__title": "Estado de stock", "@sage/xtrem-stock/pages__stock_count____navigationPanel__listItem__title__title": "Número", "@sage/xtrem-stock/pages__stock_count____navigationPanel__listItem__titleRight__title": "Estado", "@sage/xtrem-stock/pages__stock_count____navigationPanel__optionsMenu__title": "", "@sage/xtrem-stock/pages__stock_count____objectTypePlural": "Inventarios", "@sage/xtrem-stock/pages__stock_count____objectTypeSingular": "Inventario", "@sage/xtrem-stock/pages__stock_count____title": "Inventario", "@sage/xtrem-stock/pages__stock_count__categories____title": "Categoría", "@sage/xtrem-stock/pages__stock_count__confirm_zero_quantity_result": "Líneas actualizadas: {{recordsUpdated}}.", "@sage/xtrem-stock/pages__stock_count__confirmZeroQuantity____title": "Confirmar cantidad cero", "@sage/xtrem-stock/pages__stock_count__counted_quantity_can_not_be_greater_than_quantity_in_stock": "La cantidad contada no puede ser superior a la cantidad de stock de un artículo serializado.", "@sage/xtrem-stock/pages__stock_count__counter____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_count__creation": "El inventario se ha creado.", "@sage/xtrem-stock/pages__stock_count__criteriaBlock____title": "Criterios", "@sage/xtrem-stock/pages__stock_count__defaultDimension____title": "Definir secciones por defecto", "@sage/xtrem-stock/pages__stock_count__description____title": "Descripción", "@sage/xtrem-stock/pages__stock_count__duplication_among_added_lines": "Cambia o quita las líneas duplicadas.", "@sage/xtrem-stock/pages__stock_count__effectiveDate____title": "<PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_count__fromItem____title": "<PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_count__goToSysNotificationPage____title": "Reintentar", "@sage/xtrem-stock/pages__stock_count__hasStockRecords____title": "Solo en stock", "@sage/xtrem-stock/pages__stock_count__lastCountDate____title": "Artículos sin inventario desde", "@sage/xtrem-stock/pages__stock_count__lines____columns__columns__item__name__columns__title__symbol": "Símbolo", "@sage/xtrem-stock/pages__stock_count__lines____columns__columns__location__name__title": "Nombre", "@sage/xtrem-stock/pages__stock_count__lines____columns__columns__location__name__title__2": "Id.", "@sage/xtrem-stock/pages__stock_count__lines____columns__columns__location__name__title__3": "Tipo", "@sage/xtrem-stock/pages__stock_count__lines____columns__columns__lotSelection__id__columns__title__id": "Id.", "@sage/xtrem-stock/pages__stock_count__lines____columns__columns__lotSelection__id__columns__title__name": "Nombre", "@sage/xtrem-stock/pages__stock_count__lines____columns__columns__stockDetail__stockDetailLot__lotId__id__title": "Id.", "@sage/xtrem-stock/pages__stock_count__lines____columns__columns__stockDetail__stockDetailLot__lotId__id__title__2": "<PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_count__lines____columns__columns__stockDetail__stockDetailLot__lotId__id__title__3": "<PERSON><PERSON> de caducidad", "@sage/xtrem-stock/pages__stock_count__lines____columns__columns__stockDetail__stockDetailLot__lotId__id__title__4": "<PERSON><PERSON><PERSON><PERSON> de lote de <PERSON>", "@sage/xtrem-stock/pages__stock_count__lines____columns__columns__stockDetail__stockDetailLot__lotId__id__title__5": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_count__lines____columns__columns__stockStatus__name__title": "Nombre", "@sage/xtrem-stock/pages__stock_count__lines____columns__columns__stockStatus__name__title__2": "Id.", "@sage/xtrem-stock/pages__stock_count__lines____columns__columns__stockStatus__name__title__3": "Descripción", "@sage/xtrem-stock/pages__stock_count__lines____columns__lookupDialogTitle__stockDetail__stockDetailLot__lotId__id": "<PERSON><PERSON><PERSON><PERSON><PERSON> lote", "@sage/xtrem-stock/pages__stock_count__lines____columns__postfix__quantityVariancePercentage": "%", "@sage/xtrem-stock/pages__stock_count__lines____columns__title___id": "", "@sage/xtrem-stock/pages__stock_count__lines____columns__title__countedQuantityInStockUnit": "Cantidad contada", "@sage/xtrem-stock/pages__stock_count__lines____columns__title__countedSerialNumberPercentage": "Número de serie", "@sage/xtrem-stock/pages__stock_count__lines____columns__title__expirationDate": "<PERSON><PERSON> de caducidad", "@sage/xtrem-stock/pages__stock_count__lines____columns__title__item__description": "Descripción de artículo", "@sage/xtrem-stock/pages__stock_count__lines____columns__title__item__id": "Id<PERSON> <PERSON> artí<PERSON>lo", "@sage/xtrem-stock/pages__stock_count__lines____columns__title__item__name": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_count__lines____columns__title__location__locationZone__name": "Á<PERSON>", "@sage/xtrem-stock/pages__stock_count__lines____columns__title__location__name": "Ubicación", "@sage/xtrem-stock/pages__stock_count__lines____columns__title__lot__sublot": "<PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_count__lines____columns__title__newLineOrderCost": "Coste unitario", "@sage/xtrem-stock/pages__stock_count__lines____columns__title__owner": "Propietario", "@sage/xtrem-stock/pages__stock_count__lines____columns__title__quantityInStockUnit": "Cantidad de stock", "@sage/xtrem-stock/pages__stock_count__lines____columns__title__quantityVariance": "Desviación de cantidad", "@sage/xtrem-stock/pages__stock_count__lines____columns__title__quantityVariancePercentage": "Porcentaje de desviación de cantidad", "@sage/xtrem-stock/pages__stock_count__lines____columns__title__status": "Estado de inventario", "@sage/xtrem-stock/pages__stock_count__lines____columns__title__stockDetail__stockDetailLot__expirationDate": "<PERSON><PERSON> de caducidad", "@sage/xtrem-stock/pages__stock_count__lines____columns__title__stockDetail__stockDetailLot__lot__id": "Lote", "@sage/xtrem-stock/pages__stock_count__lines____columns__title__stockDetail__stockDetailLot__lotId__id": "Lote", "@sage/xtrem-stock/pages__stock_count__lines____columns__title__stockDetail__stockDetailLot__lotNumber": "<PERSON><PERSON><PERSON><PERSON>e", "@sage/xtrem-stock/pages__stock_count__lines____columns__title__stockDetail__stockDetailLot__sublot": "Número de sublote", "@sage/xtrem-stock/pages__stock_count__lines____columns__title__stockDetail__stockDetailLot__supplierLot": "<PERSON><PERSON><PERSON><PERSON> de lote de <PERSON>", "@sage/xtrem-stock/pages__stock_count__lines____columns__title__stockStatus__name": "Control de calidad", "@sage/xtrem-stock/pages__stock_count__lines____columns__title__stockTransactionStatus": "Estado de stock", "@sage/xtrem-stock/pages__stock_count__lines____columns__title__sublot": "Número de sublote", "@sage/xtrem-stock/pages__stock_count__lines____columns__title__supplierLot": "<PERSON><PERSON><PERSON><PERSON> de lote de <PERSON>", "@sage/xtrem-stock/pages__stock_count__lines____dropdownActions__title": "Contar", "@sage/xtrem-stock/pages__stock_count__lines____dropdownActions__title__2": "Excluir", "@sage/xtrem-stock/pages__stock_count__lines____dropdownActions__title__3": "Confirmar cantidad cero", "@sage/xtrem-stock/pages__stock_count__lines____dropdownActions__title__4": "Secciones", "@sage/xtrem-stock/pages__stock_count__lines____dropdownActions__title__5": "Números de serie", "@sage/xtrem-stock/pages__stock_count__lines____dropdownActions__title__6": "Eliminar", "@sage/xtrem-stock/pages__stock_count__lines____inlineActions__title": "Abrir panel de línea", "@sage/xtrem-stock/pages__stock_count__lines____title": "Líneas de documento", "@sage/xtrem-stock/pages__stock_count__linesSection____title": "Líneas", "@sage/xtrem-stock/pages__stock_count__locations____title": "Ubicación", "@sage/xtrem-stock/pages__stock_count__mainSection____title": "General", "@sage/xtrem-stock/pages__stock_count__number____title": "Número", "@sage/xtrem-stock/pages__stock_count__order_cost_should_be_entered": "Revisa el coste unitario. Un coste unitario 0 puede afectar al valor de stock.", "@sage/xtrem-stock/pages__stock_count__post____title": "Contabilizar stock", "@sage/xtrem-stock/pages__stock_count__postingDetails____columns__title__financeIntegrationAppRecordId": "Referencia de integración contable", "@sage/xtrem-stock/pages__stock_count__postingDetails____columns__title__postingStatus": "Estado", "@sage/xtrem-stock/pages__stock_count__postingDetails____columns__title__targetDocumentNumber": "Número de documento", "@sage/xtrem-stock/pages__stock_count__postingDetails____columns__title__targetDocumentType": "Tipo de documento", "@sage/xtrem-stock/pages__stock_count__postingDetails____title": "Resul<PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_count__postingMessageBlock____title": "Detalles de error", "@sage/xtrem-stock/pages__stock_count__postingSection____title": "Contabilización", "@sage/xtrem-stock/pages__stock_count__print____title": "Imprimir", "@sage/xtrem-stock/pages__stock_count__repost____title": "Volver a contabilizar", "@sage/xtrem-stock/pages__stock_count__repost_errors": "Errores al volver a contabilizar:", "@sage/xtrem-stock/pages__stock_count__saveStockCount____title": "Guardar", "@sage/xtrem-stock/pages__stock_count__selected_serial_number_quantity_too_high": "La cantidad de números de serie seleccionada ({{selectedQuantity}}) no puede ser superior a la cantidad contada ({{countedQuantity}}).", "@sage/xtrem-stock/pages__stock_count__showQuantityInStock____title": "Mostrar cantidad en stock", "@sage/xtrem-stock/pages__stock_count__sidebar_tab_title_information": "Información", "@sage/xtrem-stock/pages__stock_count__start____title": "Iniciar", "@sage/xtrem-stock/pages__stock_count__status____title": "Estado", "@sage/xtrem-stock/pages__stock_count__stockSite____title": "Planta", "@sage/xtrem-stock/pages__stock_count__stockTransactionStatus____title": "Estado de stock", "@sage/xtrem-stock/pages__stock_count__toItem____title": "<PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_count__zones____title": "Á<PERSON>", "@sage/xtrem-stock/pages__stock_count_creation____navigationPanel__listItem__line2__title": "Planta", "@sage/xtrem-stock/pages__stock_count_creation____navigationPanel__optionsMenu__title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_count_creation____title": "Creación de inventario", "@sage/xtrem-stock/pages__stock_count_creation__cancelDetailedButton____title": "Ocultar selección avanzada", "@sage/xtrem-stock/pages__stock_count_creation__categories____columns__title__id": "Id.", "@sage/xtrem-stock/pages__stock_count_creation__categories____columns__title__name": "Nombre", "@sage/xtrem-stock/pages__stock_count_creation__categories____lookupDialogTitle": "Seleccionar categoría", "@sage/xtrem-stock/pages__stock_count_creation__categories____placeholder": "Seleccionar...", "@sage/xtrem-stock/pages__stock_count_creation__categories____title": "Categoría", "@sage/xtrem-stock/pages__stock_count_creation__createStockCount____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_count_creation__criteriaBlock____title": "Criterios", "@sage/xtrem-stock/pages__stock_count_creation__description____title": "Descripción", "@sage/xtrem-stock/pages__stock_count_creation__effectiveDate____title": "<PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_count_creation__fromItem____columns__title__description": "Descripción", "@sage/xtrem-stock/pages__stock_count_creation__fromItem____columns__title__id": "Id.", "@sage/xtrem-stock/pages__stock_count_creation__fromItem____columns__title__name": "Nombre", "@sage/xtrem-stock/pages__stock_count_creation__fromItem____lookupDialogTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_count_creation__fromItem____placeholder": "Seleccionar...", "@sage/xtrem-stock/pages__stock_count_creation__fromItem____title": "<PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_count_creation__hasStockRecords____title": "Solo en stock", "@sage/xtrem-stock/pages__stock_count_creation__invalid_item_range": "Introduce un valor en \"Hasta artículo\" que sea superior al definido en \"Desde artículo\".", "@sage/xtrem-stock/pages__stock_count_creation__itemSites____levels__columns__title__hasStockRecords": "Registro de stock", "@sage/xtrem-stock/pages__stock_count_creation__itemSites____levels__columns__title__item__description": "Descripción de artículo", "@sage/xtrem-stock/pages__stock_count_creation__itemSites____levels__columns__title__item__id": "Id<PERSON> <PERSON> artí<PERSON>lo", "@sage/xtrem-stock/pages__stock_count_creation__itemSites____levels__columns__title__item__name": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_count_creation__itemSites____levels__columns__title__location__locationZone__name": "Á<PERSON>", "@sage/xtrem-stock/pages__stock_count_creation__itemSites____levels__columns__title__location__name": "Ubicación", "@sage/xtrem-stock/pages__stock_count_creation__itemSites____levels__columns__title__lot__id": "Lote", "@sage/xtrem-stock/pages__stock_count_creation__itemSites____levels__columns__title__lot__sublot": "<PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_count_creation__itemSites____levels__columns__title__owner": "Propietario", "@sage/xtrem-stock/pages__stock_count_creation__itemSites____levels__columns__title__quantityInStockUnit": "Cantidad", "@sage/xtrem-stock/pages__stock_count_creation__itemSites____levels__columns__title__status__name": "Estado", "@sage/xtrem-stock/pages__stock_count_creation__lastCountDate____title": "Artículos sin inventario desde", "@sage/xtrem-stock/pages__stock_count_creation__locations____columns__title__id": "Id.", "@sage/xtrem-stock/pages__stock_count_creation__locations____columns__title__name": "Nombre", "@sage/xtrem-stock/pages__stock_count_creation__locations____lookupDialogTitle": "Seleccionar ubicación", "@sage/xtrem-stock/pages__stock_count_creation__locations____placeholder": "Seleccionar...", "@sage/xtrem-stock/pages__stock_count_creation__locations____title": "Ubicación", "@sage/xtrem-stock/pages__stock_count_creation__mainSection____title": "General", "@sage/xtrem-stock/pages__stock_count_creation__selectAllCheckbox____title": "<PERSON><PERSON><PERSON><PERSON><PERSON> todo", "@sage/xtrem-stock/pages__stock_count_creation__stockCountBlock____title": "Inventario", "@sage/xtrem-stock/pages__stock_count_creation__stockSite____lookupDialogTitle": "Seleccionar planta", "@sage/xtrem-stock/pages__stock_count_creation__stockSite____placeholder": "Seleccionar...", "@sage/xtrem-stock/pages__stock_count_creation__stockSite____title": "Planta", "@sage/xtrem-stock/pages__stock_count_creation__toItem____columns__title__description": "Descripción", "@sage/xtrem-stock/pages__stock_count_creation__toItem____columns__title__id": "Id.", "@sage/xtrem-stock/pages__stock_count_creation__toItem____columns__title__name": "Nombre", "@sage/xtrem-stock/pages__stock_count_creation__toItem____lookupDialogTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_count_creation__toItem____placeholder": "Seleccionar...", "@sage/xtrem-stock/pages__stock_count_creation__toItem____title": "<PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_count_creation__zones____columns__title__id": "Id.", "@sage/xtrem-stock/pages__stock_count_creation__zones____columns__title__name": "Nombre", "@sage/xtrem-stock/pages__stock_count_creation__zones____columns__title__zoneType": "Tipo", "@sage/xtrem-stock/pages__stock_count_creation__zones____lookupDialogTitle": "Seleccionar área de almacenamiento", "@sage/xtrem-stock/pages__stock_count_creation__zones____placeholder": "Seleccionar...", "@sage/xtrem-stock/pages__stock_count_creation__zones____title": "Á<PERSON>", "@sage/xtrem-stock/pages__stock_count_line__not_add_a_line_matching_with_a_stock_record": "Cambia los datos de la siguiente línea de inventario para que no duplique información de stock existente:artí<PERSON><PERSON> \"{{item}}\", planta \"{{site}}\", ubicación \"{{location}}\", lote \"{{lot}}\", estado \"{{status}}\", unidad de stock \"{{stockUnit}}\" y propietario \"{{owner}}\".", "@sage/xtrem-stock/pages__stock_count_serial_numbers_panel____title": "Números de serie", "@sage/xtrem-stock/pages__stock_count_serial_numbers_panel__addSerialNumberRange____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_count_serial_numbers_panel__cancel____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_count_serial_numbers_panel__duplicate_error": "El número de serie ya está incluido en otro rango.", "@sage/xtrem-stock/pages__stock_count_serial_numbers_panel__duplicate_range_error": "Uno de los números de serie ya está incluido en otro rango.", "@sage/xtrem-stock/pages__stock_count_serial_numbers_panel__mainBlock____title": "General", "@sage/xtrem-stock/pages__stock_count_serial_numbers_panel__mainSection____title": "General", "@sage/xtrem-stock/pages__stock_count_serial_numbers_panel__ok____title": "Aceptar", "@sage/xtrem-stock/pages__stock_count_serial_numbers_panel__quantityToIssue____title": "Números de serie por emitir", "@sage/xtrem-stock/pages__stock_count_serial_numbers_panel__remainingQuantity____title": "Cantidad restante", "@sage/xtrem-stock/pages__stock_count_serial_numbers_panel__requiredQuantity____title": "Cantidad necesaria", "@sage/xtrem-stock/pages__stock_count_serial_numbers_panel__selected_quantity_too_high": "La cantidad de números de serie seleccionada ({{selectedQuantity}}) no puede ser superior a la cantidad contada ({{countedQuantity}}).", "@sage/xtrem-stock/pages__stock_count_serial_numbers_panel__selectedQuantity____title": "Cantidad seleccionada", "@sage/xtrem-stock/pages__stock_count_serial_numbers_panel__serialNumberRanges____columns__columns__endingSerialNumber__serialNumber.id__title": "Id.", "@sage/xtrem-stock/pages__stock_count_serial_numbers_panel__serialNumberRanges____columns__columns__startingSerialNumber__serialNumber__id__title": "Id.", "@sage/xtrem-stock/pages__stock_count_serial_numbers_panel__serialNumberRanges____columns__lookupDialogTitle__startingSerialNumber__serialNumber__id": "Desde número de serie", "@sage/xtrem-stock/pages__stock_count_serial_numbers_panel__serialNumberRanges____columns__title__endingSerialNumber__serialNumber.id": "Hasta número de serie", "@sage/xtrem-stock/pages__stock_count_serial_numbers_panel__serialNumberRanges____columns__title__numericEnd": "Hasta número de serie", "@sage/xtrem-stock/pages__stock_count_serial_numbers_panel__serialNumberRanges____columns__title__numericStart": "Desde número de serie", "@sage/xtrem-stock/pages__stock_count_serial_numbers_panel__serialNumberRanges____columns__title__originalStartId": "Desde número de serie original", "@sage/xtrem-stock/pages__stock_count_serial_numbers_panel__serialNumberRanges____columns__title__quantity": "Cantidad", "@sage/xtrem-stock/pages__stock_count_serial_numbers_panel__serialNumberRanges____columns__title__startingSerialNumber__serialNumber__id": "Desde número de serie", "@sage/xtrem-stock/pages__stock_count_serial_numbers_panel__serialNumberRanges____dropdownActions__title": "Eliminar", "@sage/xtrem-stock/pages__stock_count_serial_numbers_panel__serialNumberRanges____title": "Números de serie", "@sage/xtrem-stock/pages__stock_detailed_inquiry____navigationPanel__dropdownActions__title": "Asignaciones", "@sage/xtrem-stock/pages__stock_detailed_inquiry____navigationPanel__dropdownActions__title__2": "Información de número de serie", "@sage/xtrem-stock/pages__stock_detailed_inquiry____navigationPanel__listItem__activeQuantityInStockUnit__title": "Cantidad activa", "@sage/xtrem-stock/pages__stock_detailed_inquiry____navigationPanel__listItem__allocatedQuantity__title": "Cantidad asignada", "@sage/xtrem-stock/pages__stock_detailed_inquiry____navigationPanel__listItem__availableQuantity__title": "Cantidad disponible", "@sage/xtrem-stock/pages__stock_detailed_inquiry____navigationPanel__listItem__creationDate__title": "Fecha de creación", "@sage/xtrem-stock/pages__stock_detailed_inquiry____navigationPanel__listItem__expirationDate__title": "<PERSON><PERSON> de caducidad", "@sage/xtrem-stock/pages__stock_detailed_inquiry____navigationPanel__listItem__inTransitQuantityInStockUnit__title": "Cantidad en tránsito", "@sage/xtrem-stock/pages__stock_detailed_inquiry____navigationPanel__listItem__item__title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_detailed_inquiry____navigationPanel__listItem__itemDescription__title": "Descripción de artículo", "@sage/xtrem-stock/pages__stock_detailed_inquiry____navigationPanel__listItem__itemId__title": "Id<PERSON> <PERSON> artí<PERSON>lo", "@sage/xtrem-stock/pages__stock_detailed_inquiry____navigationPanel__listItem__line2__title": "Lote", "@sage/xtrem-stock/pages__stock_detailed_inquiry____navigationPanel__listItem__location__title": "Ubicación", "@sage/xtrem-stock/pages__stock_detailed_inquiry____navigationPanel__listItem__locationId__title": "Id. de ubicación", "@sage/xtrem-stock/pages__stock_detailed_inquiry____navigationPanel__listItem__locationName__title": "Ubicación", "@sage/xtrem-stock/pages__stock_detailed_inquiry____navigationPanel__listItem__locationZone__title": "Área de almacenamiento", "@sage/xtrem-stock/pages__stock_detailed_inquiry____navigationPanel__listItem__lot__title": "Lote", "@sage/xtrem-stock/pages__stock_detailed_inquiry____navigationPanel__listItem__onHandQuantityInStockUnit__title": "Cantidad física", "@sage/xtrem-stock/pages__stock_detailed_inquiry____navigationPanel__listItem__owner__title": "Propietario", "@sage/xtrem-stock/pages__stock_detailed_inquiry____navigationPanel__listItem__siteId__title": "<PERSON><PERSON><PERSON> de planta", "@sage/xtrem-stock/pages__stock_detailed_inquiry____navigationPanel__listItem__siteName__title": "Planta", "@sage/xtrem-stock/pages__stock_detailed_inquiry____navigationPanel__listItem__status__title": "Control de calidad", "@sage/xtrem-stock/pages__stock_detailed_inquiry____navigationPanel__listItem__stockUnit__title": "Unidad de medida", "@sage/xtrem-stock/pages__stock_detailed_inquiry____navigationPanel__listItem__sublot__title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_detailed_inquiry____navigationPanel__listItem__supplierLot__title": "<PERSON><PERSON><PERSON><PERSON> de lote de <PERSON>", "@sage/xtrem-stock/pages__stock_detailed_inquiry____navigationPanel__listItem__title__columns__title": "Nombre", "@sage/xtrem-stock/pages__stock_detailed_inquiry____navigationPanel__listItem__title__columns__title__2": "Id.", "@sage/xtrem-stock/pages__stock_detailed_inquiry____navigationPanel__listItem__title__columns__title__3": "Descripción", "@sage/xtrem-stock/pages__stock_detailed_inquiry____navigationPanel__listItem__title__title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_detailed_inquiry____navigationPanel__listItem__titleRight__title": "Planta", "@sage/xtrem-stock/pages__stock_detailed_inquiry____navigationPanel__optionsMenu__title": "Principal", "@sage/xtrem-stock/pages__stock_detailed_inquiry____navigationPanel__optionsMenu__title__2": "Incluyendo en tránsito", "@sage/xtrem-stock/pages__stock_detailed_inquiry____title": "Consulta detallada de stock", "@sage/xtrem-stock/pages__stock_detailed_inquiry___id____title": "Id.", "@sage/xtrem-stock/pages__stock_detailed_inquiry__activeQuantityInStockUnit____title": "Cantidad activa", "@sage/xtrem-stock/pages__stock_detailed_inquiry__location____columns__title__id": "Id.", "@sage/xtrem-stock/pages__stock_detailed_inquiry__location____columns__title__locationType__name": "Tipo", "@sage/xtrem-stock/pages__stock_detailed_inquiry__location____lookupDialogTitle": "Seleccionar ubicación", "@sage/xtrem-stock/pages__stock_detailed_inquiry__location____title": "Ubicación", "@sage/xtrem-stock/pages__stock_detailed_inquiry__lot____columns__title__item__description": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_detailed_inquiry__mainBlock____title": "Información de stock", "@sage/xtrem-stock/pages__stock_detailed_inquiry__owner____title": "Propietario", "@sage/xtrem-stock/pages__stock_detailed_inquiry__serialNumberLines____columns__title__supplierSerialNumber": "Número de serie de proveedor", "@sage/xtrem-stock/pages__stock_detailed_inquiry__stockUnit____columns__title__id": "Id.", "@sage/xtrem-stock/pages__stock_detailed_inquiry__stockUnit____columns__title__name": "Nombre", "@sage/xtrem-stock/pages__stock_detailed_inquiry__stockUnit____lookupDialogTitle": "Seleccionar unidad de medida", "@sage/xtrem-stock/pages__stock_detailed_inquiry__stockUnit____placeholder": "Seleccionar...", "@sage/xtrem-stock/pages__stock_detailed_inquiry__stockUnit____title": "Unidad de medida", "@sage/xtrem-stock/pages__stock_fifo_valuation_inquiry____title": "Nivel de coste FIFO", "@sage/xtrem-stock/pages__stock_fifo_valuation_inquiry__criteriaBlock____title": "Criterios", "@sage/xtrem-stock/pages__stock_fifo_valuation_inquiry__fifoValuationTiers____columns__title__amount": "Importe", "@sage/xtrem-stock/pages__stock_fifo_valuation_inquiry__fifoValuationTiers____columns__title__createDate": "Fecha de creación", "@sage/xtrem-stock/pages__stock_fifo_valuation_inquiry__fifoValuationTiers____columns__title__effectiveDate": "<PERSON>cha de entrada", "@sage/xtrem-stock/pages__stock_fifo_valuation_inquiry__fifoValuationTiers____columns__title__nonAbsorbedAmount": "Importe sin absorber", "@sage/xtrem-stock/pages__stock_fifo_valuation_inquiry__fifoValuationTiers____columns__title__receiptDocumentLine__documentNumber": "Documento de entrada", "@sage/xtrem-stock/pages__stock_fifo_valuation_inquiry__fifoValuationTiers____columns__title__receiptQuantity": "Cantidad original", "@sage/xtrem-stock/pages__stock_fifo_valuation_inquiry__fifoValuationTiers____columns__title__remainingQuantity": "Cantidad", "@sage/xtrem-stock/pages__stock_fifo_valuation_inquiry__fifoValuationTiers____columns__title__sequence": "Secuencia", "@sage/xtrem-stock/pages__stock_fifo_valuation_inquiry__fifoValuationTiers____columns__title__unitCost": "Coste unitario", "@sage/xtrem-stock/pages__stock_fifo_valuation_inquiry__fifoValuationTiers____title": "Resul<PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_fifo_valuation_inquiry__fromDate____title": "Fecha de inicio", "@sage/xtrem-stock/pages__stock_fifo_valuation_inquiry__item____columns__title__category__name": "Categoría", "@sage/xtrem-stock/pages__stock_fifo_valuation_inquiry__item____lookupDialogTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_fifo_valuation_inquiry__item____placeholder": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_fifo_valuation_inquiry__item____title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_fifo_valuation_inquiry__mainSection____title": "General", "@sage/xtrem-stock/pages__stock_fifo_valuation_inquiry__site____columns__title__id": "Id.", "@sage/xtrem-stock/pages__stock_fifo_valuation_inquiry__site____columns__title__name": "Nombre", "@sage/xtrem-stock/pages__stock_fifo_valuation_inquiry__site____lookupDialogTitle": "Seleccionar planta", "@sage/xtrem-stock/pages__stock_fifo_valuation_inquiry__site____placeholder": "Seleccionar planta", "@sage/xtrem-stock/pages__stock_fifo_valuation_inquiry__site____title": "Planta", "@sage/xtrem-stock/pages__stock_fifo_valuation_inquiry__toDate____title": "<PERSON><PERSON> de fin", "@sage/xtrem-stock/pages__Stock_has__allocated_quantity_the_counted_cannot_be_less_than_allocated_quantity": "El stock está asignado. La cantidad contada no puede ser inferior a la cantidad asignada.", "@sage/xtrem-stock/pages__stock_inquiry____title": "Consulta de stock", "@sage/xtrem-stock/pages__stock_inquiry__criteriaBlock____title": "Criterios", "@sage/xtrem-stock/pages__stock_inquiry__item____columns__title__category__name": "Categoría", "@sage/xtrem-stock/pages__stock_inquiry__item____lookupDialogTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_inquiry__item____title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_inquiry__itemFrom____columns__title__category__name": "Categoría", "@sage/xtrem-stock/pages__stock_inquiry__itemFrom____columns__title__description": "Descripción", "@sage/xtrem-stock/pages__stock_inquiry__itemFrom____columns__title__id": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_inquiry__itemFrom____columns__title__name": "Nombre", "@sage/xtrem-stock/pages__stock_inquiry__itemFrom____lookupDialogTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_inquiry__itemFrom____placeholder": "<PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_inquiry__itemFrom____title": "<PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_inquiry__itemTo____columns__title__category__name": "Categoría", "@sage/xtrem-stock/pages__stock_inquiry__itemTo____columns__title__description": "Descripción", "@sage/xtrem-stock/pages__stock_inquiry__itemTo____columns__title__id": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_inquiry__itemTo____columns__title__name": "Nombre", "@sage/xtrem-stock/pages__stock_inquiry__itemTo____lookupDialogTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_inquiry__itemTo____placeholder": "<PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_inquiry__itemTo____title": "<PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_inquiry__mainSection____title": "General", "@sage/xtrem-stock/pages__stock_inquiry__resultsSection____title": "Resul<PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_inquiry__search": "Buscar", "@sage/xtrem-stock/pages__stock_inquiry__site____columns__title__id": "Id.", "@sage/xtrem-stock/pages__stock_inquiry__site____columns__title__name": "Nombre", "@sage/xtrem-stock/pages__stock_inquiry__site____lookupDialogTitle": "Seleccionar planta", "@sage/xtrem-stock/pages__stock_inquiry__site____placeholder": "Seleccionar...", "@sage/xtrem-stock/pages__stock_inquiry__site____title": "Planta", "@sage/xtrem-stock/pages__stock_inquiry__stock____columns__title__activeQuantityInStockUnit": "Cantidad", "@sage/xtrem-stock/pages__stock_inquiry__stock____columns__title__item__id": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_inquiry__stock____columns__title__item__name": "Nombre", "@sage/xtrem-stock/pages__stock_inquiry__stock____columns__title__site__name": "Planta", "@sage/xtrem-stock/pages__stock_inquiry__stock____columns__title__stockUnit__name": "Unidad de medida", "@sage/xtrem-stock/pages__stock_inquiry__stock____mobileCard__line2__title": "Nombre", "@sage/xtrem-stock/pages__stock_inquiry__stock____mobileCard__line2Right__title": "Unidad de medida", "@sage/xtrem-stock/pages__stock_inquiry__stock____mobileCard__title__title": "Nombre", "@sage/xtrem-stock/pages__stock_inquiry__stock____mobileCard__titleRight__title": "Cantidad", "@sage/xtrem-stock/pages__stock_inquiry__stock____title": "Resul<PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_inquiry__stockStatus____columns__title__id": "Id.", "@sage/xtrem-stock/pages__stock_inquiry__stockStatus____columns__title__name": "Nombre", "@sage/xtrem-stock/pages__stock_inquiry__stockStatus____lookupDialogTitle": "Seleccionar estado", "@sage/xtrem-stock/pages__stock_inquiry__stockStatus____placeholder": "Seleccionar...", "@sage/xtrem-stock/pages__stock_inquiry__stockStatus____title": "Estado de stock", "@sage/xtrem-stock/pages__stock_inquiry_bound____navigationPanel__listItem__accepted__title": "Cantidad aceptada", "@sage/xtrem-stock/pages__stock_inquiry_bound____navigationPanel__listItem__allocated__title": "Cantidad asignada", "@sage/xtrem-stock/pages__stock_inquiry_bound____navigationPanel__listItem__image__title": "Imagen", "@sage/xtrem-stock/pages__stock_inquiry_bound____navigationPanel__listItem__inStock__title": "Cantidad física", "@sage/xtrem-stock/pages__stock_inquiry_bound____navigationPanel__listItem__itemCategory__title": "Categoría", "@sage/xtrem-stock/pages__stock_inquiry_bound____navigationPanel__listItem__itemDescription__title": "Descripción de artículo", "@sage/xtrem-stock/pages__stock_inquiry_bound____navigationPanel__listItem__itemIdReference__title": "Id<PERSON> <PERSON> artí<PERSON>lo", "@sage/xtrem-stock/pages__stock_inquiry_bound____navigationPanel__listItem__itemName__title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_inquiry_bound____navigationPanel__listItem__qualityQuntity__title": "Cantidad en control de calidad", "@sage/xtrem-stock/pages__stock_inquiry_bound____navigationPanel__listItem__rejected__title": "Cantidad rechazada", "@sage/xtrem-stock/pages__stock_inquiry_bound____navigationPanel__listItem__site__title": "Planta", "@sage/xtrem-stock/pages__stock_inquiry_bound____navigationPanel__listItem__stockValue__title": "Valor de stock", "@sage/xtrem-stock/pages__stock_inquiry_bound____navigationPanel__listItem__title__title": "Nombre", "@sage/xtrem-stock/pages__stock_inquiry_bound____title": "Consulta de stock", "@sage/xtrem-stock/pages__stock_issue____navigationPanel__dropdownActions__title": "Definir secci<PERSON>", "@sage/xtrem-stock/pages__stock_issue____navigationPanel__dropdownActions__title__2": "Eliminar", "@sage/xtrem-stock/pages__stock_issue____navigationPanel__listItem__description__title": "Descripción", "@sage/xtrem-stock/pages__stock_issue____navigationPanel__listItem__line2__title": "Planta", "@sage/xtrem-stock/pages__stock_issue____navigationPanel__listItem__line2Right__title": "<PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_issue____navigationPanel__listItem__reasonCode__title": "Motivo", "@sage/xtrem-stock/pages__stock_issue____navigationPanel__listItem__title__title": "Número", "@sage/xtrem-stock/pages__stock_issue____navigationPanel__listItem__titleRight__title": "Estado", "@sage/xtrem-stock/pages__stock_issue____navigationPanel__optionsMenu__title": "Abiertas", "@sage/xtrem-stock/pages__stock_issue____navigationPanel__optionsMenu__title__2": "<PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_issue____navigationPanel__optionsMenu__title__3": "Detalles pendientes", "@sage/xtrem-stock/pages__stock_issue____navigationPanel__optionsMenu__title__4": "Detalles introducidos", "@sage/xtrem-stock/pages__stock_issue____navigationPanel__optionsMenu__title__5": "Contabilización de stock en curso", "@sage/xtrem-stock/pages__stock_issue____navigationPanel__optionsMenu__title__6": "Error de contabilización de stock", "@sage/xtrem-stock/pages__stock_issue____navigationPanel__optionsMenu__title__7": "<PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_issue____objectTypePlural": "Salidas de stock", "@sage/xtrem-stock/pages__stock_issue____objectTypeSingular": "Salida de stock", "@sage/xtrem-stock/pages__stock_issue____title": "Salida de stock", "@sage/xtrem-stock/pages__stock_issue__addIssueLine____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_issue__apply_dimensions_success": "Las secciones se han aplicado.", "@sage/xtrem-stock/pages__stock_issue__defaultDimension____title": "Definir secci<PERSON>", "@sage/xtrem-stock/pages__stock_issue__defaultDimensionAction____title": "Definir secciones por defecto", "@sage/xtrem-stock/pages__stock_issue__description____title": "Descripción", "@sage/xtrem-stock/pages__stock_issue__displayStatus____title": "Estado", "@sage/xtrem-stock/pages__stock_issue__effectiveDate____title": "<PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_issue__goToSysNotificationPage____title": "Reintentar", "@sage/xtrem-stock/pages__stock_issue__issue_step_sequence_creation": "<PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_issue__issue_step_sequence_detail_stock": "Detallar stock", "@sage/xtrem-stock/pages__stock_issue__issue_step_sequence_post_stock": "Contabilizar stock", "@sage/xtrem-stock/pages__stock_issue__line_delete_action_dialog_content": "¿Quieres eliminar esta línea de salida de stock?", "@sage/xtrem-stock/pages__stock_issue__line_delete_action_dialog_title": "Confirmar eliminación", "@sage/xtrem-stock/pages__stock_issue__lines____columns__columns__item__name__columns__title__decimalDigits": "<PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_issue__lines____columns__columns__item__name__columns__title__id": "Id.", "@sage/xtrem-stock/pages__stock_issue__lines____columns__columns__item__name__columns__title__name": "Nombre", "@sage/xtrem-stock/pages__stock_issue__lines____columns__columns__item__name__columns__title__symbol": "Símbolo", "@sage/xtrem-stock/pages__stock_issue__lines____columns__columns__item__name__title": "Nombre", "@sage/xtrem-stock/pages__stock_issue__lines____columns__columns__item__name__title__2": "Id.", "@sage/xtrem-stock/pages__stock_issue__lines____columns__columns__item__name__title__3": "Categoría", "@sage/xtrem-stock/pages__stock_issue__lines____columns__columns__stockStatus__name__title": "Nombre", "@sage/xtrem-stock/pages__stock_issue__lines____columns__columns__stockStatus__name__title__2": "Id.", "@sage/xtrem-stock/pages__stock_issue__lines____columns__lookupDialogTitle__item__name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_issue__lines____columns__lookupDialogTitle__stockStatus__name": "Seleccionar estado de stock", "@sage/xtrem-stock/pages__stock_issue__lines____columns__placeholder__item__name": "Seleccionar...", "@sage/xtrem-stock/pages__stock_issue__lines____columns__title__displayStatus": "Estado", "@sage/xtrem-stock/pages__stock_issue__lines____columns__title__item__description": "Descripción de artículo", "@sage/xtrem-stock/pages__stock_issue__lines____columns__title__item__id": "Id<PERSON> <PERSON> artí<PERSON>lo", "@sage/xtrem-stock/pages__stock_issue__lines____columns__title__item__name": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_issue__lines____columns__title__orderCost": "Coste real", "@sage/xtrem-stock/pages__stock_issue__lines____columns__title__quantityInStockUnit": "Cantidad", "@sage/xtrem-stock/pages__stock_issue__lines____columns__title__stockStatus__name": "Estado", "@sage/xtrem-stock/pages__stock_issue__lines____columns__title__valuedCost": "<PERSON><PERSON> pre<PERSON>o", "@sage/xtrem-stock/pages__stock_issue__lines____dropdownActions__title": "Detalles de stock", "@sage/xtrem-stock/pages__stock_issue__lines____dropdownActions__title__2": "Secciones", "@sage/xtrem-stock/pages__stock_issue__lines____dropdownActions__title__3": "Eliminar", "@sage/xtrem-stock/pages__stock_issue__lines____inlineActions__title": "Abrir panel de línea", "@sage/xtrem-stock/pages__stock_issue__lines____mobileCard__line2__title": "Descripción", "@sage/xtrem-stock/pages__stock_issue__lines____mobileCard__line2Right__title": "Precio", "@sage/xtrem-stock/pages__stock_issue__lines____mobileCard__line3Right__title": "Cantidad", "@sage/xtrem-stock/pages__stock_issue__lines____mobileCard__title__title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_issue__lines____mobileCard__titleRight__title": "Estado", "@sage/xtrem-stock/pages__stock_issue__lines____optionsMenu__title": "<PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_issue__lines____optionsMenu__title__2": "Abiertas", "@sage/xtrem-stock/pages__stock_issue__lines____optionsMenu__title__3": "Detalles pendientes", "@sage/xtrem-stock/pages__stock_issue__lines____optionsMenu__title__4": "Detalles introducidos", "@sage/xtrem-stock/pages__stock_issue__lines____optionsMenu__title__5": "Contabilización de stock en curso", "@sage/xtrem-stock/pages__stock_issue__lines____optionsMenu__title__6": "Error de contabilización de stock", "@sage/xtrem-stock/pages__stock_issue__lines____optionsMenu__title__7": "<PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_issue__lines____sidebar__headerDropdownActions__title": "Detalles de stock", "@sage/xtrem-stock/pages__stock_issue__lines____sidebar__headerDropdownActions__title__2": "Secciones", "@sage/xtrem-stock/pages__stock_issue__lines____sidebar__headerDropdownActions__title__3": "Eliminar", "@sage/xtrem-stock/pages__stock_issue__lines____title": "Líneas", "@sage/xtrem-stock/pages__stock_issue__linesSection____title": "Líneas", "@sage/xtrem-stock/pages__stock_issue__mainSection____title": "General", "@sage/xtrem-stock/pages__stock_issue__notification__The_quantity_is_mandatory": "Introduce la cantidad.", "@sage/xtrem-stock/pages__stock_issue__number____title": "Número", "@sage/xtrem-stock/pages__stock_issue__post____title": "Contabilizar stock", "@sage/xtrem-stock/pages__stock_issue__postingDetails____columns__title__financeIntegrationAppRecordId": "Referencia de integración contable", "@sage/xtrem-stock/pages__stock_issue__postingDetails____columns__title__postingStatus": "Estado", "@sage/xtrem-stock/pages__stock_issue__postingDetails____columns__title__targetDocumentNumber": "Número de documento", "@sage/xtrem-stock/pages__stock_issue__postingDetails____columns__title__targetDocumentType": "Tipo de documento", "@sage/xtrem-stock/pages__stock_issue__postingDetails____title": "Resul<PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_issue__postingMessageBlock____title": "Detalles de error", "@sage/xtrem-stock/pages__stock_issue__postingSection____title": "Contabilización", "@sage/xtrem-stock/pages__stock_issue__reasonCode____lookupDialogTitle": "Seleccionar motivo", "@sage/xtrem-stock/pages__stock_issue__reasonCode____title": "Motivo", "@sage/xtrem-stock/pages__stock_issue__repost____title": "Volver a contabilizar", "@sage/xtrem-stock/pages__stock_issue__repost_errors": "Errores al volver a contabilizar:", "@sage/xtrem-stock/pages__stock_issue__saveStockIssue____title": "Guardar", "@sage/xtrem-stock/pages__stock_issue__stock_journal_inquiry": "Consulta de diario de stock", "@sage/xtrem-stock/pages__stock_issue__stock_posting_error": "Error de contabilización de stock", "@sage/xtrem-stock/pages__stock_issue__stockSite____columns__title__id": "Id.", "@sage/xtrem-stock/pages__stock_issue__stockSite____columns__title__name": "Nombre", "@sage/xtrem-stock/pages__stock_issue__stockSite____lookupDialogTitle": "Seleccionar planta", "@sage/xtrem-stock/pages__stock_issue__stockSite____placeholder": "Seleccionar...", "@sage/xtrem-stock/pages__stock_issue__stockSite____title": "Planta", "@sage/xtrem-stock/pages__stock_issue__stockTransactionStatus____title": "Estado de stock", "@sage/xtrem-stock/pages__stock_issue_post__stock_details_required": "Introduce los detalles de stock en todas las líneas para poder contabilizar.", "@sage/xtrem-stock/pages__stock_journal_inquiry____navigationPanel__dropdownActions__title": "Asiento", "@sage/xtrem-stock/pages__stock_journal_inquiry____navigationPanel__inlineActions__title": "Asiento", "@sage/xtrem-stock/pages__stock_journal_inquiry____navigationPanel__listItem__amountVariance__title": "Desviación de importe", "@sage/xtrem-stock/pages__stock_journal_inquiry____navigationPanel__listItem__costVariance__title": "Desviación de coste", "@sage/xtrem-stock/pages__stock_journal_inquiry____navigationPanel__listItem__currency__title": "Divisa", "@sage/xtrem-stock/pages__stock_journal_inquiry____navigationPanel__listItem__currencyId__title": "Id. de divisa", "@sage/xtrem-stock/pages__stock_journal_inquiry____navigationPanel__listItem__documentLineType__title": "Tipo de línea de documento", "@sage/xtrem-stock/pages__stock_journal_inquiry____navigationPanel__listItem__documentLink__title": "Documento", "@sage/xtrem-stock/pages__stock_journal_inquiry____navigationPanel__listItem__effectiveDate__title": "<PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_journal_inquiry____navigationPanel__listItem__expirationDate__title": "<PERSON><PERSON> de caducidad", "@sage/xtrem-stock/pages__stock_journal_inquiry____navigationPanel__listItem__itemDescription__title": "Descripción de artículo", "@sage/xtrem-stock/pages__stock_journal_inquiry____navigationPanel__listItem__itemId__title": "Id<PERSON> <PERSON> artí<PERSON>lo", "@sage/xtrem-stock/pages__stock_journal_inquiry____navigationPanel__listItem__itemName__title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_journal_inquiry____navigationPanel__listItem__locationId__title": "Id. de ubicación", "@sage/xtrem-stock/pages__stock_journal_inquiry____navigationPanel__listItem__locationName__title": "Ubicación", "@sage/xtrem-stock/pages__stock_journal_inquiry____navigationPanel__listItem__locationZone__title": "Área de almacenamiento", "@sage/xtrem-stock/pages__stock_journal_inquiry____navigationPanel__listItem__lot__title": "Lote", "@sage/xtrem-stock/pages__stock_journal_inquiry____navigationPanel__listItem__movementAmount__title": "Importe de movimiento", "@sage/xtrem-stock/pages__stock_journal_inquiry____navigationPanel__listItem__movementType__title": "Tipo de movimiento", "@sage/xtrem-stock/pages__stock_journal_inquiry____navigationPanel__listItem__nonAbsorbedAmount__title": "Importe sin absorber", "@sage/xtrem-stock/pages__stock_journal_inquiry____navigationPanel__listItem__orderAmount__title": "Importe de documento", "@sage/xtrem-stock/pages__stock_journal_inquiry____navigationPanel__listItem__orderCost__title": "Coste de documento", "@sage/xtrem-stock/pages__stock_journal_inquiry____navigationPanel__listItem__quantityInStockUnit__title": "Cantidad", "@sage/xtrem-stock/pages__stock_journal_inquiry____navigationPanel__listItem__reasonCode__title": "Código de motivo", "@sage/xtrem-stock/pages__stock_journal_inquiry____navigationPanel__listItem__siteId__title": "<PERSON><PERSON><PERSON> de planta", "@sage/xtrem-stock/pages__stock_journal_inquiry____navigationPanel__listItem__stockStatus__title": "Control de calidad", "@sage/xtrem-stock/pages__stock_journal_inquiry____navigationPanel__listItem__stockUnit__title": "Unidad de medida", "@sage/xtrem-stock/pages__stock_journal_inquiry____navigationPanel__listItem__sublot__title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_journal_inquiry____navigationPanel__listItem__supplierLot__title": "<PERSON><PERSON><PERSON><PERSON> de lote de <PERSON>", "@sage/xtrem-stock/pages__stock_journal_inquiry____navigationPanel__listItem__valuedCost__title": "Coste valorado", "@sage/xtrem-stock/pages__stock_journal_inquiry____title": "Consulta de diario de stock", "@sage/xtrem-stock/pages__stock_journal_inquiry__no_journal_entry_found": "No se ha encontrado ningún asiento para el documento seleccionado.", "@sage/xtrem-stock/pages__stock_journal_query____title": "Consulta de diario de stock", "@sage/xtrem-stock/pages__stock_journal_query__criteriaBlock____title": "Criterios", "@sage/xtrem-stock/pages__stock_journal_query__itemFrom____columns__title__id": "Id.", "@sage/xtrem-stock/pages__stock_journal_query__itemFrom____columns__title__name": "Nombre", "@sage/xtrem-stock/pages__stock_journal_query__itemFrom____placeholder": "<PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_journal_query__itemFrom____title": "<PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_journal_query__itemTo____columns__title__id": "Id.", "@sage/xtrem-stock/pages__stock_journal_query__itemTo____columns__title__name": "Nombre", "@sage/xtrem-stock/pages__stock_journal_query__itemTo____placeholder": "<PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_journal_query__itemTo____title": "<PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_journal_query__mainBlock____title": "Resul<PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_journal_query__site____columns__title__id": "Id.", "@sage/xtrem-stock/pages__stock_journal_query__site____columns__title__name": "Nombre", "@sage/xtrem-stock/pages__stock_journal_query__site____placeholder": "Seleccionar...", "@sage/xtrem-stock/pages__stock_journal_query__site____title": "Planta", "@sage/xtrem-stock/pages__stock_journal_query__stockJournal____columns__title__activeQuantityInStockUnit": "Cantidad activa", "@sage/xtrem-stock/pages__stock_journal_query__stockJournal____columns__title__amountVariance": "Desviación de importe", "@sage/xtrem-stock/pages__stock_journal_query__stockJournal____columns__title__costVariance": "Desviación de coste", "@sage/xtrem-stock/pages__stock_journal_query__stockJournal____columns__title__effectiveDate": "<PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_journal_query__stockJournal____columns__title__item__name": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_journal_query__stockJournal____columns__title__location__id": "Ubicación", "@sage/xtrem-stock/pages__stock_journal_query__stockJournal____columns__title__lot__id": "Lote", "@sage/xtrem-stock/pages__stock_journal_query__stockJournal____columns__title__lot__sublot": "<PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_journal_query__stockJournal____columns__title__lot__supplierLot": "<PERSON><PERSON><PERSON><PERSON> de lote de <PERSON>", "@sage/xtrem-stock/pages__stock_journal_query__stockJournal____columns__title__movementAmount": "Importe de movimiento", "@sage/xtrem-stock/pages__stock_journal_query__stockJournal____columns__title__orderAmount": "Importe de pedido", "@sage/xtrem-stock/pages__stock_journal_query__stockJournal____columns__title__orderCost": "Coste de pedido", "@sage/xtrem-stock/pages__stock_journal_query__stockJournal____columns__title__quantityInStockUnit": "Cantidad", "@sage/xtrem-stock/pages__stock_journal_query__stockJournal____columns__title__site__name": "Planta", "@sage/xtrem-stock/pages__stock_journal_query__stockJournal____columns__title__stockUnit__name": "Unidad de medida", "@sage/xtrem-stock/pages__stock_journal_query__stockJournal____columns__title__valuedCost": "Coste valorado", "@sage/xtrem-stock/pages__stock_mrp_calculation__calculate": "Calcular", "@sage/xtrem-stock/pages__stock_mrp_calculation_company_sites_error": "Introduce al menos una sociedad o una planta.", "@sage/xtrem-stock/pages__stock_receipt____navigationPanel__dropdownActions__title": "Definir secci<PERSON>", "@sage/xtrem-stock/pages__stock_receipt____navigationPanel__dropdownActions__title__2": "Eliminar", "@sage/xtrem-stock/pages__stock_receipt____navigationPanel__listItem__description__title": "Descripción", "@sage/xtrem-stock/pages__stock_receipt____navigationPanel__listItem__line2__title": "Planta", "@sage/xtrem-stock/pages__stock_receipt____navigationPanel__listItem__line2Right__title": "<PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_receipt____navigationPanel__listItem__reasonCode__title": "Motivo", "@sage/xtrem-stock/pages__stock_receipt____navigationPanel__listItem__status__title": "Estado", "@sage/xtrem-stock/pages__stock_receipt____navigationPanel__listItem__title__title": "Número", "@sage/xtrem-stock/pages__stock_receipt____navigationPanel__listItem__titleRight__title": "Estado", "@sage/xtrem-stock/pages__stock_receipt____navigationPanel__optionsMenu__title": "Abiertas", "@sage/xtrem-stock/pages__stock_receipt____navigationPanel__optionsMenu__title__2": "<PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_receipt____navigationPanel__optionsMenu__title__3": "Detalles pendientes", "@sage/xtrem-stock/pages__stock_receipt____navigationPanel__optionsMenu__title__4": "Detalles introducidos", "@sage/xtrem-stock/pages__stock_receipt____navigationPanel__optionsMenu__title__5": "Contabilización de stock en curso", "@sage/xtrem-stock/pages__stock_receipt____navigationPanel__optionsMenu__title__6": "Error de contabilización de stock", "@sage/xtrem-stock/pages__stock_receipt____navigationPanel__optionsMenu__title__7": "Recibidas", "@sage/xtrem-stock/pages__stock_receipt____objectTypePlural": "Entradas de stock", "@sage/xtrem-stock/pages__stock_receipt____objectTypeSingular": "Entrada de stock", "@sage/xtrem-stock/pages__stock_receipt____title": "Entrada de stock", "@sage/xtrem-stock/pages__stock_receipt__actual_cost_cannot_be_negative": "Introduce un valor superior o igual a 0.", "@sage/xtrem-stock/pages__stock_receipt__apply_dimensions_success": "Las secciones se han aplicado.", "@sage/xtrem-stock/pages__stock_receipt__defaultDimension____title": "Definir secci<PERSON>", "@sage/xtrem-stock/pages__stock_receipt__defaultDimensionAction____title": "Definir secciones por defecto", "@sage/xtrem-stock/pages__stock_receipt__description____title": "Descripción", "@sage/xtrem-stock/pages__stock_receipt__displayStatus____title": "Estado", "@sage/xtrem-stock/pages__stock_receipt__effectiveDate____title": "<PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_receipt__goToSysNotificationPage____title": "Reintentar", "@sage/xtrem-stock/pages__stock_receipt__line_delete_action_dialog_content": "¿Quieres eliminar esta línea de entrada de stock?", "@sage/xtrem-stock/pages__stock_receipt__line_delete_action_dialog_title": "Confirmar eliminación", "@sage/xtrem-stock/pages__stock_receipt__lines____columns__columns__existingLot__id__columns__title__id": "Id.", "@sage/xtrem-stock/pages__stock_receipt__lines____columns__columns__existingLot__id__columns__title__name": "Nombre", "@sage/xtrem-stock/pages__stock_receipt__lines____columns__columns__existingLot__id__title": "Id.", "@sage/xtrem-stock/pages__stock_receipt__lines____columns__columns__existingLot__id__title__2": "<PERSON><PERSON> de caducidad", "@sage/xtrem-stock/pages__stock_receipt__lines____columns__columns__existingLot__id__title__3": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_receipt__lines____columns__columns__item__name__columns__title__decimalDigits": "<PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_receipt__lines____columns__columns__item__name__columns__title__id__2": "Id.", "@sage/xtrem-stock/pages__stock_receipt__lines____columns__columns__item__name__columns__title__name__2": "Nombre", "@sage/xtrem-stock/pages__stock_receipt__lines____columns__columns__item__name__columns__title__symbol": "Símbolo", "@sage/xtrem-stock/pages__stock_receipt__lines____columns__columns__item__name__title": "Nombre", "@sage/xtrem-stock/pages__stock_receipt__lines____columns__columns__item__name__title__2": "Id.", "@sage/xtrem-stock/pages__stock_receipt__lines____columns__columns__item__name__title__3": "Categoría", "@sage/xtrem-stock/pages__stock_receipt__lines____columns__columns__location__name__title": "Nombre", "@sage/xtrem-stock/pages__stock_receipt__lines____columns__columns__location__name__title__2": "Id.", "@sage/xtrem-stock/pages__stock_receipt__lines____columns__columns__stockStatus__name__title": "Nombre", "@sage/xtrem-stock/pages__stock_receipt__lines____columns__columns__stockStatus__name__title__2": "Id.", "@sage/xtrem-stock/pages__stock_receipt__lines____columns__lookupDialogTitle__existingLot__id": "<PERSON><PERSON><PERSON><PERSON><PERSON> lote", "@sage/xtrem-stock/pages__stock_receipt__lines____columns__lookupDialogTitle__item__name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_receipt__lines____columns__lookupDialogTitle__location__name": "Seleccionar ubicación", "@sage/xtrem-stock/pages__stock_receipt__lines____columns__lookupDialogTitle__stockStatus": "Se<PERSON><PERSON><PERSON>r valor de calidad", "@sage/xtrem-stock/pages__stock_receipt__lines____columns__lookupDialogTitle__stockStatus__name": "Seleccionar estado de stock", "@sage/xtrem-stock/pages__stock_receipt__lines____columns__placeholder__existingLot__id": "Seleccionar...", "@sage/xtrem-stock/pages__stock_receipt__lines____columns__placeholder__item__name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_receipt__lines____columns__title__displayStatus": "Estado", "@sage/xtrem-stock/pages__stock_receipt__lines____columns__title__existingLot__id": "Lote", "@sage/xtrem-stock/pages__stock_receipt__lines____columns__title__expirationDate": "<PERSON><PERSON> de caducidad", "@sage/xtrem-stock/pages__stock_receipt__lines____columns__title__item__description": "Descripción de artículo", "@sage/xtrem-stock/pages__stock_receipt__lines____columns__title__item__id": "Id<PERSON> <PERSON> artí<PERSON>lo", "@sage/xtrem-stock/pages__stock_receipt__lines____columns__title__item__name": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_receipt__lines____columns__title__location__name": "Ubicación", "@sage/xtrem-stock/pages__stock_receipt__lines____columns__title__lotNumber": "<PERSON><PERSON><PERSON><PERSON>e", "@sage/xtrem-stock/pages__stock_receipt__lines____columns__title__orderCost": "Coste real", "@sage/xtrem-stock/pages__stock_receipt__lines____columns__title__quantityInStockUnit": "Cantidad", "@sage/xtrem-stock/pages__stock_receipt__lines____columns__title__stockStatus": "Control de calidad", "@sage/xtrem-stock/pages__stock_receipt__lines____columns__title__stockStatus__name": "Estado", "@sage/xtrem-stock/pages__stock_receipt__lines____columns__title__valuedCost": "<PERSON><PERSON> pre<PERSON>o", "@sage/xtrem-stock/pages__stock_receipt__lines____dropdownActions__title": "Detalles de stock", "@sage/xtrem-stock/pages__stock_receipt__lines____dropdownActions__title__2": "Secciones", "@sage/xtrem-stock/pages__stock_receipt__lines____dropdownActions__title__3": "Eliminar", "@sage/xtrem-stock/pages__stock_receipt__lines____inlineActions__title": "Abrir panel de línea", "@sage/xtrem-stock/pages__stock_receipt__lines____mobileCard__line2__title": "Descripción", "@sage/xtrem-stock/pages__stock_receipt__lines____mobileCard__line2Right__title": "Precio", "@sage/xtrem-stock/pages__stock_receipt__lines____mobileCard__line3Right__title": "Cantidad", "@sage/xtrem-stock/pages__stock_receipt__lines____mobileCard__title__title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_receipt__lines____mobileCard__titleRight__title": "Estado", "@sage/xtrem-stock/pages__stock_receipt__lines____optionsMenu__title": "<PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_receipt__lines____optionsMenu__title__2": "Abiertas", "@sage/xtrem-stock/pages__stock_receipt__lines____optionsMenu__title__3": "Detalles pendientes", "@sage/xtrem-stock/pages__stock_receipt__lines____optionsMenu__title__4": "Detalles introducidos", "@sage/xtrem-stock/pages__stock_receipt__lines____optionsMenu__title__5": "Contabilización de stock en curso", "@sage/xtrem-stock/pages__stock_receipt__lines____optionsMenu__title__6": "Error de contabilización de stock", "@sage/xtrem-stock/pages__stock_receipt__lines____optionsMenu__title__7": "Recibidas", "@sage/xtrem-stock/pages__stock_receipt__lines____sidebar__headerDropdownActions__title": "Detalles de stock", "@sage/xtrem-stock/pages__stock_receipt__lines____sidebar__headerDropdownActions__title__2": "Secciones", "@sage/xtrem-stock/pages__stock_receipt__lines____sidebar__headerDropdownActions__title__3": "Eliminar", "@sage/xtrem-stock/pages__stock_receipt__lines____title": "Líneas", "@sage/xtrem-stock/pages__stock_receipt__linesSection____title": "Líneas", "@sage/xtrem-stock/pages__stock_receipt__notification__expiration_date_cannot_be_past": "La fecha de caducidad no puede ser anterior a la fecha actual.", "@sage/xtrem-stock/pages__stock_receipt__notification__expiration_date_is_mandatory": "Introduce la fecha de caducidad.", "@sage/xtrem-stock/pages__stock_receipt__notification__validation_error": "Ha habido errores de validación:\n{{#each errors}}\t- {{this}}{{#unless @last}}\n{{/unless}}{{/each}}", "@sage/xtrem-stock/pages__stock_receipt__number____title": "Número", "@sage/xtrem-stock/pages__stock_receipt__post____title": "Contabilizar stock", "@sage/xtrem-stock/pages__stock_receipt__postingDetails____columns__title__financeIntegrationAppRecordId": "Referencia de integración contable", "@sage/xtrem-stock/pages__stock_receipt__postingDetails____columns__title__postingStatus": "Estado", "@sage/xtrem-stock/pages__stock_receipt__postingDetails____columns__title__targetDocumentNumber": "Número de documento", "@sage/xtrem-stock/pages__stock_receipt__postingDetails____columns__title__targetDocumentType": "Tipo de documento", "@sage/xtrem-stock/pages__stock_receipt__postingDetails____title": "Resul<PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_receipt__postingMessageBlock____title": "Detalles de error", "@sage/xtrem-stock/pages__stock_receipt__postingSection____title": "Contabilización", "@sage/xtrem-stock/pages__stock_receipt__quantity_must_be_positive": "Introduce un valor superior a 0.", "@sage/xtrem-stock/pages__stock_receipt__reasonCode____lookupDialogTitle": "Seleccionar motivo", "@sage/xtrem-stock/pages__stock_receipt__reasonCode____placeholder": "Seleccionar motivo", "@sage/xtrem-stock/pages__stock_receipt__reasonCode____title": "Motivo", "@sage/xtrem-stock/pages__stock_receipt__receipt_step_sequence_creation": "<PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_receipt__receipt_step_sequence_detail_stock": "Detallar stock", "@sage/xtrem-stock/pages__stock_receipt__receipt_step_sequence_post_stock": "Contabilizar stock", "@sage/xtrem-stock/pages__stock_receipt__repost____title": "Volver a contabilizar", "@sage/xtrem-stock/pages__stock_receipt__repost_errors": "Errores al volver a contabilizar:", "@sage/xtrem-stock/pages__stock_receipt__saveStockReceipt____title": "Guardar", "@sage/xtrem-stock/pages__stock_receipt__serial number_exists_on_another_line": "El número de serie {{serialNumberId}} del artículo {{itemId}} ya existe en otra línea de documento.", "@sage/xtrem-stock/pages__stock_receipt__stock_journal_inquiry": "Consulta de diario de stock", "@sage/xtrem-stock/pages__stock_receipt__stock_posting_error": "Error de contabilización de stock", "@sage/xtrem-stock/pages__stock_receipt__stockReceiptStatus____title": "Estado de entrada de stock", "@sage/xtrem-stock/pages__stock_receipt__stockSite____columns__columns__legalCompany__name__columns__title__symbol": "Símbolo", "@sage/xtrem-stock/pages__stock_receipt__stockSite____columns__title__id": "Id.", "@sage/xtrem-stock/pages__stock_receipt__stockSite____columns__title__isLocationManaged": "Gestión de ubicación", "@sage/xtrem-stock/pages__stock_receipt__stockSite____columns__title__name": "Nombre", "@sage/xtrem-stock/pages__stock_receipt__stockSite____lookupDialogTitle": "Seleccionar planta", "@sage/xtrem-stock/pages__stock_receipt__stockSite____placeholder": "Seleccionar...", "@sage/xtrem-stock/pages__stock_receipt__stockSite____title": "Planta", "@sage/xtrem-stock/pages__stock_receipt__stockTransactionStatus____title": "Estado de stock", "@sage/xtrem-stock/pages__stock_receipt_detail_panel__notification__The_lot_is_mandatory": "Introduce el lote.", "@sage/xtrem-stock/pages__stock_receipt_detail_panel__notification__The_status_is_mandatory": "Introduce el estado.", "@sage/xtrem-stock/pages__stock_receipt_detail_panel__notification__The_sublot_is_mandatory": "Introduce el sublote.", "@sage/xtrem-stock/pages__stock_receipt_details_panel__notification__expiration_date_is_mandatory": "Introduce la fecha de caducidad.", "@sage/xtrem-stock/pages__stock_receipt_post__stock_details_required": "Introduce los detalles de stock en todas las líneas para poder contabilizar.", "@sage/xtrem-stock/pages__stock_reorder_calculation____title": "Reaprovisionamiento de stock", "@sage/xtrem-stock/pages__stock_reorder_calculation__company____columns__title__id": "Id.", "@sage/xtrem-stock/pages__stock_reorder_calculation__company____columns__title__name": "Nombre", "@sage/xtrem-stock/pages__stock_reorder_calculation__company____lookupDialogTitle": "Seleccionar sociedad", "@sage/xtrem-stock/pages__stock_reorder_calculation__company____placeholder": "Seleccionar...", "@sage/xtrem-stock/pages__stock_reorder_calculation__company____title": "Sociedad", "@sage/xtrem-stock/pages__stock_reorder_calculation__criteriaBlock____title": "Criterios", "@sage/xtrem-stock/pages__stock_reorder_calculation__endDate____title": "<PERSON><PERSON> de fin", "@sage/xtrem-stock/pages__stock_reorder_calculation__fromItem____columns__title__category__name": "Categoría", "@sage/xtrem-stock/pages__stock_reorder_calculation__fromItem____lookupDialogTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_reorder_calculation__fromItem____placeholder": "Seleccionar...", "@sage/xtrem-stock/pages__stock_reorder_calculation__fromItem____title": "<PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_reorder_calculation__itemValues____columns__columns__stockUnit__symbol__title__2": "Símbolo", "@sage/xtrem-stock/pages__stock_reorder_calculation__lines____columns__title__company__name": "Sociedad", "@sage/xtrem-stock/pages__stock_reorder_calculation__lines____columns__title__itemSite__item__description": "Descripción de artículo", "@sage/xtrem-stock/pages__stock_reorder_calculation__lines____columns__title__itemSite__item__id": "Id<PERSON> <PERSON> artí<PERSON>lo", "@sage/xtrem-stock/pages__stock_reorder_calculation__lines____columns__title__itemSite__item__name": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_reorder_calculation__lines____columns__title__itemSite__site__id": "Planta", "@sage/xtrem-stock/pages__stock_reorder_calculation__lines____columns__title__orderDate": "<PERSON><PERSON>edido", "@sage/xtrem-stock/pages__stock_reorder_calculation__lines____columns__title__quantity": "Cantidad propuesta", "@sage/xtrem-stock/pages__stock_reorder_calculation__lines____columns__title__reorderType": "Método de proceso", "@sage/xtrem-stock/pages__stock_reorder_calculation__lines____columns__title__supplier__businessEntity__id": "<PERSON><PERSON>. de <PERSON>edor", "@sage/xtrem-stock/pages__stock_reorder_calculation__lines____columns__title__supplier__businessEntity__name": "Nombre de proveedor", "@sage/xtrem-stock/pages__stock_reorder_calculation__lines____dropdownActions__title": "<PERSON><PERSON><PERSON> pedido de compra", "@sage/xtrem-stock/pages__stock_reorder_calculation__lines____dropdownActions__title__2": "Crear orden de fabricación", "@sage/xtrem-stock/pages__stock_reorder_calculation__mainSection____title": "General", "@sage/xtrem-stock/pages__stock_reorder_calculation__purchase_order_creation__success": "El pedido de compra {{num}} se ha creado.", "@sage/xtrem-stock/pages__stock_reorder_calculation__reorderType____title": "Tipo de reaprovisionamiento", "@sage/xtrem-stock/pages__stock_reorder_calculation__runStockReorderCalculation____title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_reorder_calculation__sites____columns__title__id": "Id.", "@sage/xtrem-stock/pages__stock_reorder_calculation__sites____columns__title__name": "Nombre", "@sage/xtrem-stock/pages__stock_reorder_calculation__sites____lookupDialogTitle": "Seleccionar planta", "@sage/xtrem-stock/pages__stock_reorder_calculation__sites____placeholder": "Seleccionar...", "@sage/xtrem-stock/pages__stock_reorder_calculation__sites____title": "Planta", "@sage/xtrem-stock/pages__stock_reorder_calculation__status____title": "Estado", "@sage/xtrem-stock/pages__stock_reorder_calculation__toItem____columns__title__category__name": "Categoría", "@sage/xtrem-stock/pages__stock_reorder_calculation__toItem____columns__title__description": "Descripción", "@sage/xtrem-stock/pages__stock_reorder_calculation__toItem____columns__title__id": "Id.", "@sage/xtrem-stock/pages__stock_reorder_calculation__toItem____columns__title__name": "Nombre", "@sage/xtrem-stock/pages__stock_reorder_calculation__toItem____lookupDialogTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_reorder_calculation__toItem____placeholder": "Seleccionar...", "@sage/xtrem-stock/pages__stock_reorder_calculation__toItem____title": "<PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_reorder_calculation__user____title": "Usuario", "@sage/xtrem-stock/pages__stock_reorder_calculation__work_order_creation__success": "La orden de fabricación {{num}} se ha creado.", "@sage/xtrem-stock/pages__stock_reorder_calculation_new__fromItem____columns__title__description": "Descripción", "@sage/xtrem-stock/pages__stock_reorder_calculation_new__fromItem____columns__title__id": "Id.", "@sage/xtrem-stock/pages__stock_reorder_calculation_new__fromItem____columns__title__name": "Nombre", "@sage/xtrem-stock/pages__stock_reorder_calculation_new__user____columns__title__email": "E-mail", "@sage/xtrem-stock/pages__stock_reorder_calculation_new__user____columns__title__firstName": "Nombre", "@sage/xtrem-stock/pages__stock_reorder_calculation_new__user____columns__title__lastName": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_reorder_calculation_new__user____lookupDialogTitle": "Se<PERSON><PERSON><PERSON>r usuario", "@sage/xtrem-stock/pages__stock_reorder_calculation_request__notification_calculation_failed": "Ha habido un error al calcular el reaprovisionamiento de stock.", "@sage/xtrem-stock/pages__stock_reorder_calculation_request__notification_calculation_finished": "El cálculo de reaprovisionamiento de stock ha finalizado.", "@sage/xtrem-stock/pages__stock_reorder_calculation_request__notification_request_sent": "La solicitud de cálculo de reaprovisionamiento de stock se ha enviado.", "@sage/xtrem-stock/pages__stock_trace_inquiry____title": "Trazabilidad de artículo", "@sage/xtrem-stock/pages__stock_trace_inquiry__criteriaBlock____title": "Criterios", "@sage/xtrem-stock/pages__stock_trace_inquiry__item____title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_trace_inquiry__lot____columns__title__id": "Id.", "@sage/xtrem-stock/pages__stock_trace_inquiry__lot____columns__title__item__name": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_trace_inquiry__lot____columns__title__sublot": "<PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_trace_inquiry__lot____lookupDialogTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON> lote", "@sage/xtrem-stock/pages__stock_trace_inquiry__lot____placeholder": "<PERSON><PERSON><PERSON><PERSON><PERSON> lote", "@sage/xtrem-stock/pages__stock_trace_inquiry__lot____title": "Lote", "@sage/xtrem-stock/pages__stock_trace_inquiry__serialNumber____columns__title__id": "Id.", "@sage/xtrem-stock/pages__stock_trace_inquiry__serialNumber____columns__title__item__name": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_trace_inquiry__serialNumber____columns__title__site__name": "Planta", "@sage/xtrem-stock/pages__stock_trace_inquiry__serialNumber____lookupDialogTitle": "Seleccionar número de serie", "@sage/xtrem-stock/pages__stock_trace_inquiry__serialNumber____placeholder": "Seleccionar número de serie", "@sage/xtrem-stock/pages__stock_trace_inquiry__serialNumber____title": "Número de serie", "@sage/xtrem-stock/pages__stock_trace_inquiry__site____title": "Planta", "@sage/xtrem-stock/pages__stock_valuation____objectTypeSingular": "Valoración de stock", "@sage/xtrem-stock/pages__stock_valuation____title": "Valoración de stock", "@sage/xtrem-stock/pages__stock_valuation__commodityCode____title": "Código de mercancías", "@sage/xtrem-stock/pages__stock_valuation__company____columns__title__id": "Id.", "@sage/xtrem-stock/pages__stock_valuation__company____columns__title__name": "Nombre", "@sage/xtrem-stock/pages__stock_valuation__company____lookupDialogTitle": "Seleccionar sociedad", "@sage/xtrem-stock/pages__stock_valuation__company____placeholder": "Seleccionar...", "@sage/xtrem-stock/pages__stock_valuation__company____title": "Sociedad", "@sage/xtrem-stock/pages__stock_valuation__criteriaBlock____title": "Criterios", "@sage/xtrem-stock/pages__stock_valuation__date____title": "<PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_valuation__displayZeroValues____title": "Mostrar valores cero", "@sage/xtrem-stock/pages__stock_valuation__fromItem____columns__title__category__name": "Categoría", "@sage/xtrem-stock/pages__stock_valuation__fromItem____lookupDialogTitle": "<PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_valuation__fromItem____placeholder": "Seleccionar...", "@sage/xtrem-stock/pages__stock_valuation__fromItem____title": "<PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_valuation__itemCategory____columns__title__type": "Tipo", "@sage/xtrem-stock/pages__stock_valuation__itemCategory____lookupDialogTitle": "Seleccionar categoría de artículo", "@sage/xtrem-stock/pages__stock_valuation__itemCategory____title": "Categoría de artículo", "@sage/xtrem-stock/pages__stock_valuation__itemSites____columns__columns__item__id__title": "Estado", "@sage/xtrem-stock/pages__stock_valuation__itemSites____columns__columns__stockUnit__symbol__title": "Símbolo", "@sage/xtrem-stock/pages__stock_valuation__itemSites____columns__columns__stockUnit__symbol__title__2": "Símbolo", "@sage/xtrem-stock/pages__stock_valuation__itemSites____columns__title__commodityCode": "Código de mercancías", "@sage/xtrem-stock/pages__stock_valuation__itemSites____columns__title__company__name": "Sociedad", "@sage/xtrem-stock/pages__stock_valuation__itemSites____columns__title__costType": "Tipo de coste", "@sage/xtrem-stock/pages__stock_valuation__itemSites____columns__title__currency__symbol": "Divisa", "@sage/xtrem-stock/pages__stock_valuation__itemSites____columns__title__item__category__id": "Categoría de artículo", "@sage/xtrem-stock/pages__stock_valuation__itemSites____columns__title__item__description": "Descripción de artículo", "@sage/xtrem-stock/pages__stock_valuation__itemSites____columns__title__item__description__2": "Descripción de artículo", "@sage/xtrem-stock/pages__stock_valuation__itemSites____columns__title__item__id": "Id<PERSON> <PERSON> artí<PERSON>lo", "@sage/xtrem-stock/pages__stock_valuation__itemSites____columns__title__item__name": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_valuation__itemSites____columns__title__item__status": "Estado", "@sage/xtrem-stock/pages__stock_valuation__itemSites____columns__title__postingClass__name": "Clase contable", "@sage/xtrem-stock/pages__stock_valuation__itemSites____columns__title__quantity": "Cantidad", "@sage/xtrem-stock/pages__stock_valuation__itemSites____columns__title__site__name": "Planta", "@sage/xtrem-stock/pages__stock_valuation__itemSites____columns__title__stockUnit__symbol": "Unidad de stock", "@sage/xtrem-stock/pages__stock_valuation__itemSites____columns__title__stockValue": "Valor de stock", "@sage/xtrem-stock/pages__stock_valuation__itemSites____columns__title__unitCost": "Coste unitario", "@sage/xtrem-stock/pages__stock_valuation__itemSites____columns__title__valuationDate": "Fecha de valoración", "@sage/xtrem-stock/pages__stock_valuation__mainSection____title": "General", "@sage/xtrem-stock/pages__stock_valuation__postingClass____lookupDialogTitle": "Seleccionar clase contable", "@sage/xtrem-stock/pages__stock_valuation__postingClass____title": "Clase contable", "@sage/xtrem-stock/pages__stock_valuation__runStockValuation____title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_valuation__selectedSites____columns__columns__site__name__columns__title__id": "Id.", "@sage/xtrem-stock/pages__stock_valuation__selectedSites____columns__columns__site__name__title": "Nombre", "@sage/xtrem-stock/pages__stock_valuation__selectedSites____columns__columns__site__name__title__2": "Id.", "@sage/xtrem-stock/pages__stock_valuation__selectedSites____columns__title__site__name": "Plantas", "@sage/xtrem-stock/pages__stock_valuation__selectedSites____title": "Plantas", "@sage/xtrem-stock/pages__stock_valuation__sites____columns__title__id": "Id.", "@sage/xtrem-stock/pages__stock_valuation__sites____columns__title__name": "Nombre", "@sage/xtrem-stock/pages__stock_valuation__sites____lookupDialogTitle": "Seleccionar planta", "@sage/xtrem-stock/pages__stock_valuation__sites____placeholder": "Seleccionar...", "@sage/xtrem-stock/pages__stock_valuation__sites____title": "Planta", "@sage/xtrem-stock/pages__stock_valuation__status____title": "Estado", "@sage/xtrem-stock/pages__stock_valuation__toItem____columns__title__category__name": "Categoría", "@sage/xtrem-stock/pages__stock_valuation__toItem____columns__title__description": "Descripción", "@sage/xtrem-stock/pages__stock_valuation__toItem____columns__title__id": "Id.", "@sage/xtrem-stock/pages__stock_valuation__toItem____columns__title__name": "Nombre", "@sage/xtrem-stock/pages__stock_valuation__toItem____lookupDialogTitle": "<PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_valuation__toItem____placeholder": "Seleccionar...", "@sage/xtrem-stock/pages__stock_valuation__toItem____title": "<PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_valuation__totalStockValue____title": "Valor de stock total", "@sage/xtrem-stock/pages__stock_valuation__totalStockValueCurrency____title": "Divisa de valor de stock total", "@sage/xtrem-stock/pages__stock_valuation__user____columns__title__email": "E-mail", "@sage/xtrem-stock/pages__stock_valuation__user____columns__title__firstName": "Nombre", "@sage/xtrem-stock/pages__stock_valuation__user____columns__title__lastName": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_valuation__user____lookupDialogTitle": "Se<PERSON><PERSON><PERSON>r usuario", "@sage/xtrem-stock/pages__stock_valuation__user____title": "Usuario", "@sage/xtrem-stock/pages__stock_valuation__valuationMethod____title": "Método de valoración", "@sage/xtrem-stock/pages__stock_valuation_inquiry____title": "Consulta de valoración de stock", "@sage/xtrem-stock/pages__stock_valuation_inquiry__company____columns__title__id": "Id.", "@sage/xtrem-stock/pages__stock_valuation_inquiry__company____columns__title__name": "Nombre", "@sage/xtrem-stock/pages__stock_valuation_inquiry__company____placeholder": "Seleccionar...", "@sage/xtrem-stock/pages__stock_valuation_inquiry__company____title": "Sociedad", "@sage/xtrem-stock/pages__stock_valuation_inquiry__confirmation____title": "Confirmar", "@sage/xtrem-stock/pages__stock_valuation_inquiry__criteriaBlock____title": "Criterios", "@sage/xtrem-stock/pages__stock_valuation_inquiry__itemFrom____columns__title__id": "Id.", "@sage/xtrem-stock/pages__stock_valuation_inquiry__itemFrom____columns__title__name": "Nombre", "@sage/xtrem-stock/pages__stock_valuation_inquiry__itemFrom____placeholder": "Seleccionar...", "@sage/xtrem-stock/pages__stock_valuation_inquiry__itemFrom____title": "<PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_valuation_inquiry__itemSelection____columns__title__item__id": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_valuation_inquiry__itemSelection____columns__title__item__name": "Nombre", "@sage/xtrem-stock/pages__stock_valuation_inquiry__itemSelection____columns__title__site__name": "Planta", "@sage/xtrem-stock/pages__stock_valuation_inquiry__itemSelection____columns__title__stockUnit__name": "Unidad de medida", "@sage/xtrem-stock/pages__stock_valuation_inquiry__itemTo____columns__title__id": "Id.", "@sage/xtrem-stock/pages__stock_valuation_inquiry__itemTo____columns__title__name": "Nombre", "@sage/xtrem-stock/pages__stock_valuation_inquiry__itemTo____placeholder": "Seleccionar...", "@sage/xtrem-stock/pages__stock_valuation_inquiry__itemTo____title": "<PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_valuation_inquiry__itemValues____columns__title__company__name": "Sociedad", "@sage/xtrem-stock/pages__stock_valuation_inquiry__itemValues____columns__title__currency__symbol": "Divisa", "@sage/xtrem-stock/pages__stock_valuation_inquiry__itemValues____columns__title__dateOfValuation": "Fecha de valoración", "@sage/xtrem-stock/pages__stock_valuation_inquiry__itemValues____columns__title__item__id": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_valuation_inquiry__itemValues____columns__title__item__name": "Nombre", "@sage/xtrem-stock/pages__stock_valuation_inquiry__itemValues____columns__title__lastDateOfValuation": "Última fecha de valoración", "@sage/xtrem-stock/pages__stock_valuation_inquiry__itemValues____columns__title__lastQuantityInStock": "Última cantidad", "@sage/xtrem-stock/pages__stock_valuation_inquiry__itemValues____columns__title__lastStockValue": "Último valor de stock", "@sage/xtrem-stock/pages__stock_valuation_inquiry__itemValues____columns__title__lastUnitCost": "Último coste unitario", "@sage/xtrem-stock/pages__stock_valuation_inquiry__itemValues____columns__title__lastValuationMethod": "Último tipo de coste", "@sage/xtrem-stock/pages__stock_valuation_inquiry__itemValues____columns__title__postingClass__name": "Clase contable", "@sage/xtrem-stock/pages__stock_valuation_inquiry__itemValues____columns__title__quantityInStock": "Cantidad", "@sage/xtrem-stock/pages__stock_valuation_inquiry__itemValues____columns__title__site__name": "Planta", "@sage/xtrem-stock/pages__stock_valuation_inquiry__itemValues____columns__title__stockUnit__id": "Unidad de stock", "@sage/xtrem-stock/pages__stock_valuation_inquiry__itemValues____columns__title__stockValue": "Valor de stock", "@sage/xtrem-stock/pages__stock_valuation_inquiry__itemValues____columns__title__unitCost": "Coste unitario", "@sage/xtrem-stock/pages__stock_valuation_inquiry__itemValues____columns__title__valuationMethod": "Tipo de coste", "@sage/xtrem-stock/pages__stock_valuation_inquiry__itemValues____columns__title__variance": "Desviación", "@sage/xtrem-stock/pages__stock_valuation_inquiry__mainBlock____title": "Resul<PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_valuation_inquiry__runStockValuation____title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_valuation_inquiry__search": "Buscar", "@sage/xtrem-stock/pages__stock_valuation_inquiry__sites____columns__title__id": "Id.", "@sage/xtrem-stock/pages__stock_valuation_inquiry__sites____columns__title__name": "Nombre", "@sage/xtrem-stock/pages__stock_valuation_inquiry__sites____placeholder": "Seleccionar...", "@sage/xtrem-stock/pages__stock_valuation_inquiry__sites____title": "Planta", "@sage/xtrem-stock/pages__stock_valuation_inquiry__valuationDate____title": "<PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_valuation_request__notification_request_sent": "La solicitud de valoración de stock se ha enviado.", "@sage/xtrem-stock/pages__stock_valuation_request__notification_valuation_failed": "Ha habido un error al valorar el stock.", "@sage/xtrem-stock/pages__stock_valuation_request__notification_valuation_finished": "La valoración de stock ha finalizado.", "@sage/xtrem-stock/pages__stock_value_change____navigationPanel__listItem__itemDescription__title": "Descripción de artículo", "@sage/xtrem-stock/pages__stock_value_change____navigationPanel__listItem__itemId__title": "Id<PERSON> <PERSON> artí<PERSON>lo", "@sage/xtrem-stock/pages__stock_value_change____navigationPanel__listItem__line2__title": "Nombre de planta", "@sage/xtrem-stock/pages__stock_value_change____navigationPanel__listItem__line2Right__title": "<PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_value_change____navigationPanel__listItem__line3__title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_value_change____navigationPanel__listItem__siteID__title": "<PERSON><PERSON><PERSON> de planta", "@sage/xtrem-stock/pages__stock_value_change____navigationPanel__listItem__title__title": "Número", "@sage/xtrem-stock/pages__stock_value_change____navigationPanel__listItem__titleRight__title": "Estado de stock", "@sage/xtrem-stock/pages__stock_value_change____objectTypePlural": "Cambios de valor de stock", "@sage/xtrem-stock/pages__stock_value_change____objectTypeSingular": "Cambio de valor de stock", "@sage/xtrem-stock/pages__stock_value_change____title": "Cambio de valor de stock", "@sage/xtrem-stock/pages__stock_value_change__addAverageCost____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_value_change__defaultDimension____title": "Definir secciones por defecto", "@sage/xtrem-stock/pages__stock_value_change__description____title": "Descripción", "@sage/xtrem-stock/pages__stock_value_change__duplication_among_added_lines": "Cambia o quita las líneas duplicadas.", "@sage/xtrem-stock/pages__stock_value_change__goToSysNotificationPage____title": "Reintentar", "@sage/xtrem-stock/pages__stock_value_change__item____columns__columns__stockUnit__description__title": "Nombre", "@sage/xtrem-stock/pages__stock_value_change__item____columns__title__category__name": "Categoría", "@sage/xtrem-stock/pages__stock_value_change__item____columns__title__stockUnit__description": "Unidad de stock", "@sage/xtrem-stock/pages__stock_value_change__item____lookupDialogTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_value_change__item____title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_value_change__lines____columns__title__amount": "Importe", "@sage/xtrem-stock/pages__stock_value_change__lines____columns__title__fifoCost__amount": "Importe", "@sage/xtrem-stock/pages__stock_value_change__lines____columns__title__fifoCost__effectiveDate": "Fecha efectiva", "@sage/xtrem-stock/pages__stock_value_change__lines____columns__title__fifoCost__nonAbsorbedAmount": "Importe sin absorber", "@sage/xtrem-stock/pages__stock_value_change__lines____columns__title__fifoCost__receiptDocumentLine__documentNumber": "Documento de entrada", "@sage/xtrem-stock/pages__stock_value_change__lines____columns__title__fifoCost__receiptQuantity": "Cantidad de entrada", "@sage/xtrem-stock/pages__stock_value_change__lines____columns__title__fifoCost__remainingQuantity": "Cantidad restante", "@sage/xtrem-stock/pages__stock_value_change__lines____columns__title__fifoCost__sequence": "Secuencia", "@sage/xtrem-stock/pages__stock_value_change__lines____columns__title__newAmount": "Nuevo importe", "@sage/xtrem-stock/pages__stock_value_change__lines____columns__title__newUnitCost": "Nuevo coste unitario", "@sage/xtrem-stock/pages__stock_value_change__lines____columns__title__quantity": "Cantidad", "@sage/xtrem-stock/pages__stock_value_change__lines____columns__title__stockTransactionStatus": "Estado de stock", "@sage/xtrem-stock/pages__stock_value_change__lines____columns__title__unitCost": "Coste unitario", "@sage/xtrem-stock/pages__stock_value_change__lines____dropdownActions__title": "Eliminar", "@sage/xtrem-stock/pages__stock_value_change__lines____dropdownActions__title__2": "Secciones", "@sage/xtrem-stock/pages__stock_value_change__lines____title": "Líneas", "@sage/xtrem-stock/pages__stock_value_change__linesSection____title": "Líneas", "@sage/xtrem-stock/pages__stock_value_change__mainSection____title": "General", "@sage/xtrem-stock/pages__stock_value_change__number____title": "Número", "@sage/xtrem-stock/pages__stock_value_change__post____title": "Contabilizar stock", "@sage/xtrem-stock/pages__stock_value_change__postedDate____title": "<PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_value_change__postingDetails____columns__title__financeIntegrationAppRecordId": "Referencia de integración contable", "@sage/xtrem-stock/pages__stock_value_change__postingDetails____columns__title__postingStatus": "Estado", "@sage/xtrem-stock/pages__stock_value_change__postingDetails____columns__title__targetDocumentNumber": "Número de documento", "@sage/xtrem-stock/pages__stock_value_change__postingDetails____columns__title__targetDocumentType": "Tipo de documento", "@sage/xtrem-stock/pages__stock_value_change__postingDetails____title": "Resul<PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_value_change__postingMessageBlock____title": "Detalles de error", "@sage/xtrem-stock/pages__stock_value_change__postingSection____title": "Contabilización", "@sage/xtrem-stock/pages__stock_value_change__repost____title": "Volver a contabilizar", "@sage/xtrem-stock/pages__stock_value_change__repost_errors": "Errores al volver a contabilizar: {{errors}}", "@sage/xtrem-stock/pages__stock_value_change__save_warnings": "Avisos al guardar:", "@sage/xtrem-stock/pages__stock_value_change__saveStockValueChange____title": "Guardar", "@sage/xtrem-stock/pages__stock_value_change__selectFromFifo____title": "<PERSON><PERSON><PERSON> de nivel FIFO", "@sage/xtrem-stock/pages__stock_value_change__site____lookupDialogTitle": "Seleccionar planta", "@sage/xtrem-stock/pages__stock_value_change__site____placeholder": "Seleccionar...", "@sage/xtrem-stock/pages__stock_value_change__site____title": "Planta", "@sage/xtrem-stock/pages__stock_value_change__stockTransactionStatus____title": "Estado de stock", "@sage/xtrem-stock/pages__stock_value_change__totalQuantityInStock____title": "Física", "@sage/xtrem-stock/pages__stock_value_change__totalValueOfStock____title": "Valor", "@sage/xtrem-stock/pages__stock_value_change__unitCost____title": "Coste unitario", "@sage/xtrem-stock/pages__stock_value_change__valuationMethod____title": "Método de valoración", "@sage/xtrem-stock/pages__stock_value_correction____navigationPanel__listItem__description__title": "Descripción", "@sage/xtrem-stock/pages__stock_value_correction____navigationPanel__listItem__effectiveDate__title": "<PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_value_correction____navigationPanel__listItem__reasonCode__title": "Motivo de regularización", "@sage/xtrem-stock/pages__stock_value_correction____navigationPanel__listItem__stockSite__title": "Planta", "@sage/xtrem-stock/pages__stock_value_correction____navigationPanel__listItem__stockTransactionStatus__title": "Estado de stock", "@sage/xtrem-stock/pages__stock_value_correction____navigationPanel__listItem__title__title": "Número", "@sage/xtrem-stock/pages__stock_value_correction____objectTypePlural": "Rectificaciones de valor de stock", "@sage/xtrem-stock/pages__stock_value_correction____objectTypeSingular": "Rectificación de valor de stock", "@sage/xtrem-stock/pages__stock_value_correction____title": "Rectificación de valor de stock", "@sage/xtrem-stock/pages__stock_value_correction___correctedDocumentType____title": "Tipo de documento", "@sage/xtrem-stock/pages__stock_value_correction__addAdjustLine____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_value_correction__defaultDimension____title": "Definir secciones por defecto", "@sage/xtrem-stock/pages__stock_value_correction__description____title": "Descripción", "@sage/xtrem-stock/pages__stock_value_correction__effectiveDate____title": "<PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_value_correction__lines____columns__columns__item__name__columns__title__decimalDigits": "<PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_value_correction__lines____columns__columns__item__name__columns__title__id": "Id.", "@sage/xtrem-stock/pages__stock_value_correction__lines____columns__columns__item__name__columns__title__name": "Nombre", "@sage/xtrem-stock/pages__stock_value_correction__lines____columns__columns__item__name__columns__title__symbol": "Símbolo", "@sage/xtrem-stock/pages__stock_value_correction__lines____columns__columns__item__name__title": "Nombre", "@sage/xtrem-stock/pages__stock_value_correction__lines____columns__columns__item__name__title__2": "Id.", "@sage/xtrem-stock/pages__stock_value_correction__lines____columns__columns__item__name__title__3": "Categoría", "@sage/xtrem-stock/pages__stock_value_correction__lines____columns__columns__title___sortValue": "Número de línea", "@sage/xtrem-stock/pages__stock_value_correction__lines____columns__columns__title__document__number": "Número de documento", "@sage/xtrem-stock/pages__stock_value_correction__lines____columns__columns__title__orderCost": "Coste de pedido", "@sage/xtrem-stock/pages__stock_value_correction__lines____columns__lookupDialogTitle__item__name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_value_correction__lines____columns__placeholder__item__name": "Seleccionar...", "@sage/xtrem-stock/pages__stock_value_correction__lines____columns__title": "Línea de documento", "@sage/xtrem-stock/pages__stock_value_correction__lines____columns__title__item__name": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_value_correction__lines____columns__title__newUnitCost": "Nuevo coste de pedido", "@sage/xtrem-stock/pages__stock_value_correction__lines____columns__title__orderCost": "Coste de pedido", "@sage/xtrem-stock/pages__stock_value_correction__lines____columns__title__stockTransactionStatus": "Estado de stock", "@sage/xtrem-stock/pages__stock_value_correction__lines____columns__title__unitCost": "<PERSON><PERSON> actual", "@sage/xtrem-stock/pages__stock_value_correction__lines____dropdownActions__title": "Secciones", "@sage/xtrem-stock/pages__stock_value_correction__lines____dropdownActions__title__2": "Eliminar", "@sage/xtrem-stock/pages__stock_value_correction__lines____title": "Líneas de documento", "@sage/xtrem-stock/pages__stock_value_correction__mainSection____title": "General", "@sage/xtrem-stock/pages__stock_value_correction__number____title": "Número", "@sage/xtrem-stock/pages__stock_value_correction__post____title": "Contabilizar", "@sage/xtrem-stock/pages__stock_value_correction__purchaseReceiptLines____columns__columns__item__name__columns__title__decimalDigits": "<PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_value_correction__purchaseReceiptLines____columns__columns__item__name__columns__title__id": "Id.", "@sage/xtrem-stock/pages__stock_value_correction__purchaseReceiptLines____columns__columns__item__name__columns__title__name": "Nombre", "@sage/xtrem-stock/pages__stock_value_correction__purchaseReceiptLines____columns__columns__item__name__title": "Nombre", "@sage/xtrem-stock/pages__stock_value_correction__purchaseReceiptLines____columns__columns__item__name__title__2": "Id.", "@sage/xtrem-stock/pages__stock_value_correction__purchaseReceiptLines____columns__columns__item__name__title__3": "Unidad", "@sage/xtrem-stock/pages__stock_value_correction__purchaseReceiptLines____columns__columns__item__name__title__4": "Gestión de lotes", "@sage/xtrem-stock/pages__stock_value_correction__purchaseReceiptLines____columns__columns__title___sortValue": "Número de línea", "@sage/xtrem-stock/pages__stock_value_correction__purchaseReceiptLines____columns__columns__title__document__number": "Número de documento", "@sage/xtrem-stock/pages__stock_value_correction__purchaseReceiptLines____columns__placeholder__item__name": "Seleccionar...", "@sage/xtrem-stock/pages__stock_value_correction__purchaseReceiptLines____columns__title": "Línea de documento", "@sage/xtrem-stock/pages__stock_value_correction__purchaseReceiptLines____columns__title__item__name": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_value_correction__purchaseReceiptLines____columns__title__newUnitCost": "Nuevo coste unitario", "@sage/xtrem-stock/pages__stock_value_correction__purchaseReceiptLines____columns__title__stockTransactionStatus": "Estado de stock", "@sage/xtrem-stock/pages__stock_value_correction__purchaseReceiptLines____columns__title__unitCost": "Coste unitario", "@sage/xtrem-stock/pages__stock_value_correction__purchaseReceiptLines____dropdownActions__title": "Secciones", "@sage/xtrem-stock/pages__stock_value_correction__purchaseReceiptLines____dropdownActions__title__2": "Eliminar", "@sage/xtrem-stock/pages__stock_value_correction__purchaseReceiptLines____title": "Líneas de documento", "@sage/xtrem-stock/pages__stock_value_correction__reasonCode____columns__title__id": "Id.", "@sage/xtrem-stock/pages__stock_value_correction__reasonCode____columns__title__name": "Nombre", "@sage/xtrem-stock/pages__stock_value_correction__reasonCode____columns__title__stockValueCorrection": "Rectificación de valor", "@sage/xtrem-stock/pages__stock_value_correction__reasonCode____lookupDialogTitle": "Seleccionar código de motivo", "@sage/xtrem-stock/pages__stock_value_correction__reasonCode____title": "Motivo de rectificación", "@sage/xtrem-stock/pages__stock_value_correction__saveStockValueCorrection____title": "Guardar", "@sage/xtrem-stock/pages__stock_value_correction__stockReceiptLines____columns__columns__columns__document__number__title": "Planta", "@sage/xtrem-stock/pages__stock_value_correction__stockReceiptLines____columns__columns__item__name__columns__title__decimalDigits": "<PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_value_correction__stockReceiptLines____columns__columns__item__name__columns__title__id": "Id.", "@sage/xtrem-stock/pages__stock_value_correction__stockReceiptLines____columns__columns__item__name__columns__title__name": "Nombre", "@sage/xtrem-stock/pages__stock_value_correction__stockReceiptLines____columns__columns__item__name__title": "Nombre", "@sage/xtrem-stock/pages__stock_value_correction__stockReceiptLines____columns__columns__item__name__title__2": "Id.", "@sage/xtrem-stock/pages__stock_value_correction__stockReceiptLines____columns__columns__item__name__title__3": "Unidad", "@sage/xtrem-stock/pages__stock_value_correction__stockReceiptLines____columns__columns__item__name__title__4": "Gestión de lotes", "@sage/xtrem-stock/pages__stock_value_correction__stockReceiptLines____columns__columns__title___sortValue": "Número de línea", "@sage/xtrem-stock/pages__stock_value_correction__stockReceiptLines____columns__columns__title__document__number": "Número de documento", "@sage/xtrem-stock/pages__stock_value_correction__stockReceiptLines____columns__placeholder__item__name": "Seleccionar...", "@sage/xtrem-stock/pages__stock_value_correction__stockReceiptLines____columns__title": "Línea de documento", "@sage/xtrem-stock/pages__stock_value_correction__stockReceiptLines____columns__title__item__name": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_value_correction__stockReceiptLines____columns__title__newUnitCost": "Nuevo coste unitario", "@sage/xtrem-stock/pages__stock_value_correction__stockReceiptLines____columns__title__stockTransactionStatus": "Estado de stock", "@sage/xtrem-stock/pages__stock_value_correction__stockReceiptLines____columns__title__unitCost": "Coste unitario", "@sage/xtrem-stock/pages__stock_value_correction__stockReceiptLines____dropdownActions__title": "Secciones", "@sage/xtrem-stock/pages__stock_value_correction__stockReceiptLines____dropdownActions__title__2": "Eliminar", "@sage/xtrem-stock/pages__stock_value_correction__stockReceiptLines____title": "Líneas de documento", "@sage/xtrem-stock/pages__stock_value_correction__stockSite____columns__title__id": "Id.", "@sage/xtrem-stock/pages__stock_value_correction__stockSite____columns__title__name": "Nombre", "@sage/xtrem-stock/pages__stock_value_correction__stockSite____lookupDialogTitle": "Seleccionar planta", "@sage/xtrem-stock/pages__stock_value_correction__stockSite____placeholder": "Seleccionar...", "@sage/xtrem-stock/pages__stock_value_correction__stockSite____title": "Planta", "@sage/xtrem-stock/pages__stock_value_correction__stockTransactionStatus____title": "Estado de stock", "@sage/xtrem-stock/pages__stock-adjustment-detail-panel__notification__The_adjustment_quantity_is_mandatory": "Introduce la cantidad de regularización.", "@sage/xtrem-stock/pages__stock-entry-detail-panel__notification__The_location_is_mandatory": "Introduce la ubicación.", "@sage/xtrem-stock/pages__stock-entry-detail-panel__notification__The_stock_status_is_mandatory": "Introduce el estado del stock.", "@sage/xtrem-stock/pages__stock-receipt__notification__The_lot_is_mandatory": "Introduce el lote.", "@sage/xtrem-stock/pages_sidebar_tab_title_information": "Información", "@sage/xtrem-stock/pages-confirm-cancel": "<PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages-confirm-delete": "Eliminar", "@sage/xtrem-stock/permission__create__name": "<PERSON><PERSON><PERSON>", "@sage/xtrem-stock/permission__create_item_site_costs_from_cost_roll_up_results__name": "Crear costes de artículo-planta a partir del cálculo de costes", "@sage/xtrem-stock/permission__delete__name": "Eliminar", "@sage/xtrem-stock/permission__finance_integration_check__name": "Revisar integración contable", "@sage/xtrem-stock/permission__manage__name": "Gestionar", "@sage/xtrem-stock/permission__manage_cost__name": "Gestionar coste", "@sage/xtrem-stock/permission__post__name": "Contabilizar", "@sage/xtrem-stock/permission__post_to_stock__name": "Contabilizar en stock", "@sage/xtrem-stock/permission__read__name": "<PERSON><PERSON>", "@sage/xtrem-stock/permission__repost__name": "Volver a contabilizar", "@sage/xtrem-stock/permission__start__name": "Iniciar", "@sage/xtrem-stock/permission__sync_stock_value_change__name": "Cambio de valor de stock de sincronización", "@sage/xtrem-stock/permission__update__name": "Actualizar", "@sage/xtrem-stock/permission__update_attributes_and_dimensions__name": "Actualizar atributos y secciones", "@sage/xtrem-stock/search": "Buscar", "@sage/xtrem-stock/search-count": "Buscar ({{#if plus}}+{{/if}}{{nb}})", "@sage/xtrem-stock/standard_cost_roll_up_calculation__stop_calculation": "Cancelación del cálculo del coste estándar: {{stopTime}}", "@sage/xtrem-stock/standard_cost_roll_up_calculation_complete": "El coste estándar se ha calculado.", "@sage/xtrem-stock/standard_cost_roll_up_calculation_completed": "El coste estándar se ha calculado correctamente.", "@sage/xtrem-stock/standard_cost_roll_up_calculation_failed": "Ha habido un error al calcular el coste estándar. {{{errorMessage}}", "@sage/xtrem-stock/standard_cost_roll_up_calculation_processing_items": "El cálculo de coste estándar está procesando los artículos.", "@sage/xtrem-stock/standard_cost_roll_up_calculation_start": "El cálculo de coste estándar se ha iniciado.", "@sage/xtrem-stock/standard_cost_roll_up_calculation_updating_sub_assemblies": "El cálculo de coste estándar está actualizando los subconjuntos.", "@sage/xtrem-stock/stock_count_pages__stock_count_print": "Imprimir", "@sage/xtrem-stock/stock_value_change_from_item_site_cost": "Cambio de valor de stock a partir del coste de artículo-planta", "@sage/xtrem-stock/stock_value_change_from_item_site_cost_complete": "El cambio de valor de stock a partir del coste de artículo-planta ha finalizado.", "@sage/xtrem-stock/stock_value_change_from_item_site_cost_saved": "El cambio de valor de stock a partir del coste de artículo-planta se ha guardado.", "@sage/xtrem-stock/stock_value_change_from_item_site_cost_start": "El cambio de valor de stock a partir del coste de artículo-planta se ha iniciado.", "@sage/xtrem-stock/stock_value_change_from_item_site_cost_stop": "De<PERSON> de cambiar valores de stock el {{stopDate}} "}