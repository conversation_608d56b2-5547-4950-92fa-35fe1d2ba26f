{"@sage/xtrem-stock/activity__cost_roll_up_input_set__name": "<PERSON>u de donn<PERSON> du calcul de coût", "@sage/xtrem-stock/activity__stock_adjustment__name": "Régularisation de stock", "@sage/xtrem-stock/activity__stock_change__name": "Changement de stock", "@sage/xtrem-stock/activity__stock_count__name": "Inventaire", "@sage/xtrem-stock/activity__stock_issue__name": "Sortie de stock", "@sage/xtrem-stock/activity__stock_receipt__name": "Entrée de stock", "@sage/xtrem-stock/activity__stock_reorder__name": "Réapprovisionnement de stock", "@sage/xtrem-stock/activity__stock_valuation_input_set__name": "Jeu de données de la valorisation de stock", "@sage/xtrem-stock/activity__stock_value_change__name": "Changement de la valeur du stock", "@sage/xtrem-stock/class__allocation-engine__system_error_during_allocation_process": "Le processus d'allocation a été interrompu : {{errorMessage}}", "@sage/xtrem-stock/create_items_create": "<PERSON><PERSON><PERSON>", "@sage/xtrem-stock/data_types__adjustable_document_type_enum__name": "Enum type document adaptable", "@sage/xtrem-stock/data_types__standard_cost_roll_up_result_line_status_enum__name": "Enum statut ligne résultat calcul standard", "@sage/xtrem-stock/data_types__standard_cost_roll_up_status_enum__name": "Enum statut résultat calcul standard", "@sage/xtrem-stock/data_types__stock_adjustment_display_status_enum__name": "Enum statut d'affichage de régularisation de stock", "@sage/xtrem-stock/data_types__stock_count_line_status_enum__name": "Enum statut ligne inventaire", "@sage/xtrem-stock/data_types__stock_count_status_enum__name": "Enum statut inventaire", "@sage/xtrem-stock/data_types__stock_issue_display_status_enum__name": "Enum statut d'affichage sortie de stock", "@sage/xtrem-stock/data_types__stock_issue_line_display_status_enum__name": "Enum statut d'affichage ligne de sortie de stock", "@sage/xtrem-stock/data_types__stock_receipt_display_status_enum__name": "Enum statut affichage réception de stock", "@sage/xtrem-stock/data_types__stock_receipt_line_display_status_enum__name": "Enum statut affichage ligne de réception de stock", "@sage/xtrem-stock/data_types__stock_valuation_status_enum__name": "Enum statut valorisation stock", "@sage/xtrem-stock/data_types__virtual_location_need_enum__name": "Enum besoin emplacement virtuel", "@sage/xtrem-stock/edit-create-line": "<PERSON><PERSON><PERSON> ligne", "@sage/xtrem-stock/enums__adjustable_document_type__purchaseReceipt": "<PERSON><PERSON><PERSON> d'<PERSON>", "@sage/xtrem-stock/enums__adjustable_document_type__stockReceipt": "Entrée de stock", "@sage/xtrem-stock/enums__standard_cost_roll_up_result_line_status__created": "<PERSON><PERSON><PERSON>", "@sage/xtrem-stock/enums__standard_cost_roll_up_result_line_status__error": "<PERSON><PERSON><PERSON>", "@sage/xtrem-stock/enums__standard_cost_roll_up_result_line_status__inProgress": "En cours", "@sage/xtrem-stock/enums__standard_cost_roll_up_result_line_status__pending": "En attente", "@sage/xtrem-stock/enums__standard_cost_roll_up_result_line_status__updated": "Mis à jour", "@sage/xtrem-stock/enums__standard_cost_roll_up_status__completed": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-stock/enums__standard_cost_roll_up_status__draft": "Brouillon", "@sage/xtrem-stock/enums__standard_cost_roll_up_status__error": "<PERSON><PERSON><PERSON>", "@sage/xtrem-stock/enums__standard_cost_roll_up_status__inProgress": "En cours", "@sage/xtrem-stock/enums__stock_adjustment_display_status__adjusted": "Regularisé", "@sage/xtrem-stock/enums__stock_adjustment_display_status__detailsEntered": "<PERSON>é<PERSON> renseign<PERSON>", "@sage/xtrem-stock/enums__stock_adjustment_display_status__detailsRequired": "Détails demandés", "@sage/xtrem-stock/enums__stock_adjustment_display_status__stockPostingError": "Erreur de comptabilisation de stock", "@sage/xtrem-stock/enums__stock_adjustment_display_status__stockPostingInProgress": "Comptabilisation de stock en cours", "@sage/xtrem-stock/enums__stock_count_line_status__counted": "Comptée", "@sage/xtrem-stock/enums__stock_count_line_status__countInProgress": "Inventaire en cours", "@sage/xtrem-stock/enums__stock_count_line_status__excluded": "Exclue", "@sage/xtrem-stock/enums__stock_count_line_status__toBeCounted": "À compter", "@sage/xtrem-stock/enums__stock_count_status__closed": "Soldé", "@sage/xtrem-stock/enums__stock_count_status__counted": "Compté", "@sage/xtrem-stock/enums__stock_count_status__countInProgress": "Comptage en cours", "@sage/xtrem-stock/enums__stock_count_status__draft": "Brouillon", "@sage/xtrem-stock/enums__stock_count_status__toBeCounted": "À compter", "@sage/xtrem-stock/enums__stock_document_status__completed": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-stock/enums__stock_document_status__error": "<PERSON><PERSON><PERSON>", "@sage/xtrem-stock/enums__stock_issue_display_status__detailsEntered": "<PERSON>é<PERSON> renseign<PERSON>", "@sage/xtrem-stock/enums__stock_issue_display_status__detailsRequired": "Détails demandés", "@sage/xtrem-stock/enums__stock_issue_display_status__issued": "En sortie", "@sage/xtrem-stock/enums__stock_issue_display_status__stockPostingError": "Erreur de comptabilisation de stock", "@sage/xtrem-stock/enums__stock_issue_display_status__stockPostingInProgress": "Comptabilisation de stock en cours", "@sage/xtrem-stock/enums__stock_issue_line_display_status__detailsEntered": "<PERSON>é<PERSON> renseign<PERSON>", "@sage/xtrem-stock/enums__stock_issue_line_display_status__detailsRequired": "Détails demandés", "@sage/xtrem-stock/enums__stock_issue_line_display_status__issued": "En sortie", "@sage/xtrem-stock/enums__stock_issue_line_display_status__stockPostingError": "Erreur de comptabilisation de stock", "@sage/xtrem-stock/enums__stock_issue_line_display_status__stockPostingInProgress": "Comptabilisation de stock en cours", "@sage/xtrem-stock/enums__stock_receipt_display_status__detailsEntered": "<PERSON>é<PERSON> renseign<PERSON>", "@sage/xtrem-stock/enums__stock_receipt_display_status__detailsRequired": "Détails demandés", "@sage/xtrem-stock/enums__stock_receipt_display_status__received": "Ré<PERSON><PERSON><PERSON>", "@sage/xtrem-stock/enums__stock_receipt_display_status__stockPostingError": "Erreur de comptabilisation de stock", "@sage/xtrem-stock/enums__stock_receipt_display_status__stockPostingInProgress": "Comptabilisation de stock en cours", "@sage/xtrem-stock/enums__stock_receipt_line_display_status__detailsEntered": "<PERSON>é<PERSON> renseign<PERSON>", "@sage/xtrem-stock/enums__stock_receipt_line_display_status__detailsRequired": "Détails demandés", "@sage/xtrem-stock/enums__stock_receipt_line_display_status__received": "Ré<PERSON><PERSON><PERSON>", "@sage/xtrem-stock/enums__stock_receipt_line_display_status__stockPostingError": "Erreur de comptabilisation de stock", "@sage/xtrem-stock/enums__stock_receipt_line_display_status__stockPostingInProgress": "Comptabilisation de stock en cours", "@sage/xtrem-stock/enums__stock_valuation_status__completed": "Réalisée", "@sage/xtrem-stock/enums__stock_valuation_status__draft": "Brouillon", "@sage/xtrem-stock/enums__stock_valuation_status__error": "<PERSON><PERSON><PERSON>", "@sage/xtrem-stock/enums__stock_valuation_status__inProgress": "En cours", "@sage/xtrem-stock/enums__virtual_location_need__transfer": "Transfert", "@sage/xtrem-stock/events/control__location_for_wrong_site": "L'emplacement devrait être sur le site {{site}}.", "@sage/xtrem-stock/events/control__location_missing": "L'emplacement est obligatoire.", "@sage/xtrem-stock/events/control__location_not_managed": "Les emplacements ne sont pas gérés pour le site {{site}}.", "@sage/xtrem-stock/function__stock_count_lib__line_already_exists_in_a_not_closed_stock-count": "La ligne existe déjà dans l'inventaire {{number}}.", "@sage/xtrem-stock/functions__lot-lib__no_lot_information_created": "Aucune information de lot créée", "@sage/xtrem-stock/functions__lot-lib__no_lot_information_received": "Aucune information de lot reçue.", "@sage/xtrem-stock/functions__lot-lib__no_lot_sequence_number_definied": "Aucun compteur défini pour l'article {item}.", "@sage/xtrem-stock/functions__stock__already_allocated": "Le stock est déjà totalement alloué.", "@sage/xtrem-stock/functions__stock__location_mandatory_no_default": "L'emplacement est obligatoire et aucun emplacement par défaut n'est défini pour ce site de stock.", "@sage/xtrem-stock/functions__stock__lot_expired": "Votre article est géré par lot. La date de péremption ne doit pas être supérieure ou égale à {{date}}.", "@sage/xtrem-stock/functions__stock__not_enough_stock": "Stock insuffisant sur le site {{site}}.", "@sage/xtrem-stock/functions__stock__potency_greater_than_100": "Le titre ne doit pas dépasser 100.", "@sage/xtrem-stock/functions__stock__status_filter_part_1": "{{#if is<PERSON>irst}} soit pour {{/if}}", "@sage/xtrem-stock/functions__stock__status_filter_part_2": "{{#if notFirst}} ou {{/if}} type de statut {{stockStatusType}}{{#if isLast}}.{{/if}}", "@sage/xtrem-stock/functions__stock__status_filter_part_3": "{{#if notFirst}} ou {{/if}} statut {{stockStatus}}{{#if isLast}}.{{/if}}", "@sage/xtrem-stock/functions__stock__stock_change_quantity_must_be_greater_thant_0": "Vous ne pouvez pas créer de changement de stock avec une quantité à 0.", "@sage/xtrem-stock/functions__stock__stock_record_not_found": "Enregistrement de stock introuvable. La quantité ne peut pas être prise.", "@sage/xtrem-stock/functions__stock__stock_record_not_found_during_stock_change": "Les informations de stock renseignées n'existent pas.", "@sage/xtrem-stock/functions__stock__stock_status_mandatory_no_default": "Le statut de stock est obligatoire et aucun statut de stock par défaut n'est défini pour ce site de stock.", "@sage/xtrem-stock/functions__stock__stock_update_failed": "Le stock n'a pas pu être mis à jour. Vérifiez vos paramètres de mise à jour du stock.", "@sage/xtrem-stock/functions__stock_engine__no_lot_information_received": "Aucune information de lot reçue.", "@sage/xtrem-stock/functions__stock-detail-lib__item_not_managed_on_site": "L'article n'est pas géré sur le site de réception.", "@sage/xtrem-stock/functions__stock-engine__lot_passed_for_an_item_not_lot_manage": "L'article {{item}} n'est pas géré par lot. Vous pouvez uniquement comptabiliser un détail de stock avec des informations de lot pour un article géré par lot.", "@sage/xtrem-stock/functions__stock-engine__not_enough_stock": "Le niveau de stock n'est pas suffisant. Vérifiez vos paramètres :\nArticle {{item}}\nSite  {{site}}\nUnité de stock {{stockUnit}}\nLot {{lot}}\nEmplacement {{location}}\nStatut de stock {{stockStatus}}\nPropriétaire {{owner}}", "@sage/xtrem-stock/functions__stock-engine__not_enough_stock_allocation": "Vous auriez du allouer du stock pour comptabiliser ce document.", "@sage/xtrem-stock/functions__stock-engine__not_enough_stock_details": "Paramétrer les sections par défaut", "@sage/xtrem-stock/functions__stock-engine__serial_number_already_in_stock": "L'un des numéros de série utilisé pour l'article {{item}} est déjà en stock. Vérifiez les numéros de série.", "@sage/xtrem-stock/functions__stock-engine__stock_update_failed_with_parameters": "Le stock n'a pas été mis à jour. Vérifiez vos paramètres :\nArticle {{item}}\nSite  {{site}}\nUnité de stock {{stockUnit}}\nLot {{lot}}\nEmplacement {{location}}\nStatut stock {{stockStatus}}\nPropriétaire {{owner}}", "@sage/xtrem-stock/functions__stock-engine__stock_value_update_failed_with_parameters": "La valeur de stock n'a pas été mise à jour. Vérifiez les détails renseignés : \nArticle {{item}}\nSite {{site}}\nQuantité {{quantity}}", "@sage/xtrem-stock/functions--allocation-lib--allocate--not-enough-stock": "L'allocation de stock demandée ne peut pas être traitée parce qu'elle dépasse la quantité disponible en unité de stock.", "@sage/xtrem-stock/functions--allocation-lib--allocate--stock-not-in-status-accepted": "L'allocation de stock demandée ne peut pas être traitée parce que le stock sélectionné n'a pas le statut '{{acceptedStatus}}'.", "@sage/xtrem-stock/functions-stock-lib-no-item-site-supplier": "Aucun fournisseur n'est affecté au site {{currentSite}} et à l'article {{currentItem}}.", "@sage/xtrem-stock/functions-stock-lib-stock-journal-no-item-site-record": "L'article {{currentItem}} n'est pas géré par le site {{currentSite}}.", "@sage/xtrem-stock/functions-stock-no-stock-available": "Aucun stock disponible pour le site {{site}} et l'article {{item}}.", "@sage/xtrem-stock/node__stock__stock_status_incorrect_type": "Le statut de stock doit être une instance du node StockStatus, ou un enum StockStatusType.", "@sage/xtrem-stock/node__stock_count__resend_notification_for_finance": "Renvoi de la notification finance pour l'inventaire : {{stockCount}}", "@sage/xtrem-stock/node__stock_issue__resend_notification_for_finance": "Renvoi de notification finance pour la sortie de stock {{invoiceNumber}}", "@sage/xtrem-stock/node__stock_receipt__resend_notification_for_finance": "Renvoi de notification finance pour le numéro d'entrée de stock {{invoiceNumber}}", "@sage/xtrem-stock/node__stock_value_change__resend_notification_for_finance": "Renvoi de la notification finance pour le changement de valeur de stock : {{stockValueChange}}", "@sage/xtrem-stock/node-extensions__component_extension__property__availableQuantityInStockUnit": "Quantité disponible en unité de stock", "@sage/xtrem-stock/node-extensions__component_extension__property__hasStockShortage": "Contient de la rupture de stock", "@sage/xtrem-stock/node-extensions__component_extension__property__stockOnHand": "Stock physique", "@sage/xtrem-stock/node-extensions__component_extension__property__stockShortageInStockUnit": "Rupture de stock en unité de stock", "@sage/xtrem-stock/node-extensions__item_extension__asyncMutation__createTestItems": "<PERSON><PERSON>er articles de test", "@sage/xtrem-stock/node-extensions__item_extension__asyncMutation__createTestItems__failed": "Échec de création des articles de test.", "@sage/xtrem-stock/node-extensions__item_extension__asyncMutation__createTestItems__parameter__itemId": "Code article", "@sage/xtrem-stock/node-extensions__item_extension__asyncMutation__createTestItems__parameter__quantity": "Quantité", "@sage/xtrem-stock/node-extensions__item_extension__asyncMutation__createTestItems__parameter__stockCreation": "Création de stock", "@sage/xtrem-stock/node-extensions__item_site_cost_extension__asyncMutation__standardCostRollUpCalculation": "Calcul de coût standard", "@sage/xtrem-stock/node-extensions__item_site_cost_extension__asyncMutation__standardCostRollUpCalculation__failed": "Échec du calcul de coût standard.", "@sage/xtrem-stock/node-extensions__item_site_cost_extension__asyncMutation__standardCostRollUpCalculation__parameter__inputSet": "<PERSON><PERSON> <PERSON> don<PERSON>", "@sage/xtrem-stock/node-extensions__item_site_cost_extension__asyncMutation__syncStockValueChange": "Synchronisation du changement de la valeur du stock", "@sage/xtrem-stock/node-extensions__item_site_cost_extension__asyncMutation__syncStockValueChange__failed": "Échec de synchronisation du changement de la valeur du stock", "@sage/xtrem-stock/node-extensions__item_site_cost_extension__error_message": "{{errorMessage}} : [Article : {{itemId}} - Site : {{siteId}}]", "@sage/xtrem-stock/node-extensions__item_site_cost_extension__item_site_cost_creation_finished": "Création du coût article-site terminée.", "@sage/xtrem-stock/node-extensions__item_site_cost_extension__no_line_processed_message": "Erreur lors du traitement des lignes. La sélection est vide.", "@sage/xtrem-stock/node-extensions__item_site_cost_extension__property__analyticalData": "Données analytiques", "@sage/xtrem-stock/node-extensions__item_site_cost_extension__property__stockValueChange": "Changement de la valeur du stock", "@sage/xtrem-stock/node-extensions__item_site_cost_extension__property__storedAttributes": "Attributs stockés", "@sage/xtrem-stock/node-extensions__item_site_cost_extension__property__storedDimensions": "Sections stockées", "@sage/xtrem-stock/node-extensions__item_site_cost_extension__success_cost_creation_status_message": "Coût article-site créé : [Article : {{itemId}} - Site : {{siteId}}]", "@sage/xtrem-stock/node-extensions__item_site_cost_extension__success_cost_update_status_error_message": "Le coût de l'article-site existe et ne peut pas être mis à jour : [Article : {{itemId}} - Site : {{siteId}}]", "@sage/xtrem-stock/node-extensions__item_site_cost_extension__success_cost_update_status_message": "Coût de l'article-site mis à jour : [Article : {{itemId}} - Site : {{siteId}}]", "@sage/xtrem-stock/node-extensions__item_site_extension__property__countStockRecords": "Comptage des enregistrements de stock", "@sage/xtrem-stock/node-extensions__item_site_extension__property__hasStockRecords": "A des enregistrements de stock", "@sage/xtrem-stock/node-extensions__item_site_extension__property__lastCountDate": "Date dernier inventaire", "@sage/xtrem-stock/node-extensions__item_site_extension__query__getStockValuation": "Obtenir valorisation de stock", "@sage/xtrem-stock/node-extensions__item_site_extension__query__getStockValuation__failed": "Échec de la valorisation de stock.", "@sage/xtrem-stock/node-extensions__item_site_extension__query__getStockValuation__parameter__searchCriteria": "Critères de recherche", "@sage/xtrem-stock/node-extensions__mrp_calculation_extension__asyncMutation__deleteOldMrpCalculations": "Supprimer les anciens calculs CBN", "@sage/xtrem-stock/node-extensions__mrp_calculation_extension__asyncMutation__deleteOldMrpCalculations__failed": "Échec de suppression des anciens calculs CBN : {{number}}.", "@sage/xtrem-stock/node-extensions__mrp_calculation_extension__asyncMutation__deleteOldMrpCalculations__parameter__numberOfDaysToKeep": "Nombre de jours à conserver", "@sage/xtrem-stock/node-extensions__mrp_calculation_extension__asyncMutation__deleteOldMrpCalculations__parameter__numberOfRecordsToKeep": "Nombre d'enregistrements à conserver", "@sage/xtrem-stock/node-extensions__mrp_synchronization_extension__asyncMutation__mrpSynchronizeBom": "CBN Synchroniser nomenclature", "@sage/xtrem-stock/node-extensions__mrp_synchronization_extension__asyncMutation__mrpSynchronizeBom__parameter__resetSynchronization": "Réinitialiser la synchronisation", "@sage/xtrem-stock/node-extensions__mrp_synchronization_extension__asyncMutation__mrpSynchronizeBom__parameter__synchronizeDeletions": "Synchroniser les suppressions", "@sage/xtrem-stock/node-extensions__mrp_synchronization_extension__asyncMutation__mrpSynchronizeBom__parameter__type": "Type", "@sage/xtrem-stock/node-extensions__stock_adjustment_detail_extension__property__item": "Article", "@sage/xtrem-stock/node-extensions__stock_adjustment_detail_extension__property__location": "Emplacement", "@sage/xtrem-stock/node-extensions__stock_adjustment_detail_extension__property__owner": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-stock/node-extensions__stock_adjustment_detail_extension__property__site": "Site", "@sage/xtrem-stock/node-extensions__stock_adjustment_detail_extension__property__status": "Statut", "@sage/xtrem-stock/node-extensions__stock_adjustment_detail_extension__property__stockDetailLot": "Lot de détail de stock", "@sage/xtrem-stock/node-extensions__stock_adjustment_detail_extension__property__stockUnit": "Unité de stock", "@sage/xtrem-stock/node-extensions__stock_change_detail_extension__property__startingSerialNumber": "Numéro série début", "@sage/xtrem-stock/node-extensions__stock_detail_lot_extension__property__lot": "Lot", "@sage/xtrem-stock/node-extensions__stock_detail_lot_extension__property__lotNumber": "<PERSON>um<PERSON><PERSON>", "@sage/xtrem-stock/node-extensions__stock_extension__asyncMutation__adjust": "Regulariser", "@sage/xtrem-stock/node-extensions__stock_extension__asyncMutation__adjust__failed": "Échec de régularisation.", "@sage/xtrem-stock/node-extensions__stock_extension__asyncMutation__adjust__parameter__payload": "Charge", "@sage/xtrem-stock/node-extensions__stock_extension__asyncMutation__change": "Changer", "@sage/xtrem-stock/node-extensions__stock_extension__asyncMutation__change__failed": "Échec du changement.", "@sage/xtrem-stock/node-extensions__stock_extension__asyncMutation__change__parameter__payload": "Charge", "@sage/xtrem-stock/node-extensions__stock_extension__asyncMutation__changeValue": "Changer la valeur", "@sage/xtrem-stock/node-extensions__stock_extension__asyncMutation__changeValue__failed": "Échec de changement de valeur.", "@sage/xtrem-stock/node-extensions__stock_extension__asyncMutation__changeValue__parameter__payload": "Charge", "@sage/xtrem-stock/node-extensions__stock_extension__asyncMutation__correct": "<PERSON><PERSON><PERSON>", "@sage/xtrem-stock/node-extensions__stock_extension__asyncMutation__correct__failed": "Échec de correction.", "@sage/xtrem-stock/node-extensions__stock_extension__asyncMutation__correct__parameter__payload": "Charge", "@sage/xtrem-stock/node-extensions__stock_extension__asyncMutation__issue": "<PERSON><PERSON><PERSON>", "@sage/xtrem-stock/node-extensions__stock_extension__asyncMutation__issue__failed": "Échec sortie.", "@sage/xtrem-stock/node-extensions__stock_extension__asyncMutation__issue__parameter__payload": "Charge", "@sage/xtrem-stock/node-extensions__stock_extension__asyncMutation__receive": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-stock/node-extensions__stock_extension__asyncMutation__receive__failed": "Échec de réception.", "@sage/xtrem-stock/node-extensions__stock_extension__asyncMutation__receive__parameter__payload": "Charge", "@sage/xtrem-stock/node-extensions__stock_extension__asyncMutation__transfer": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-stock/node-extensions__stock_extension__asyncMutation__transfer__failed": "Échec de transfert.", "@sage/xtrem-stock/node-extensions__stock_extension__asyncMutation__transfer__parameter__payload": "Charge", "@sage/xtrem-stock/node-extensions__stock_extension__property__isStockCountingInProgress": "Inventaire en cours", "@sage/xtrem-stock/node-extensions__stock_extension__property__stockCountingInProgress": "Inventaire en cours", "@sage/xtrem-stock/node-extensions__stock_issue_detail_extension__property__startingSerialNumber": "Numéro série début", "@sage/xtrem-stock/nodes__allocation_listener__asyncMutation__allocate": "<PERSON><PERSON><PERSON>", "@sage/xtrem-stock/nodes__allocation_listener__asyncMutation__allocate__failed": "Échec d'allocation.", "@sage/xtrem-stock/nodes__allocation_listener__asyncMutation__allocate__parameter__payload": "Charge", "@sage/xtrem-stock/nodes__allocation_listener__asyncMutation__allocate__parameter__stockAllocationParameters": "Paramètres d'allocation de stock", "@sage/xtrem-stock/nodes__allocation_listener__asyncMutation__deallocate": "Désallouée", "@sage/xtrem-stock/nodes__allocation_listener__asyncMutation__deallocate__parameter__payload": "Charge", "@sage/xtrem-stock/nodes__allocation_listener__asyncMutation__deallocate__parameter__stockAllocationParameters": "Paramètres d'allocation de stock", "@sage/xtrem-stock/nodes__allocation_listener__node_name": "Listener d'allocation", "@sage/xtrem-stock/nodes__cost_roll_up_input_set__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-stock/nodes__cost_roll_up_input_set__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-stock/nodes__cost_roll_up_input_set__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-stock/nodes__cost_roll_up_input_set__mutation__updateAttributesAndDimensions": "Mettre à jour les attributs et les sections", "@sage/xtrem-stock/nodes__cost_roll_up_input_set__mutation__updateAttributesAndDimensions__failed": "Échec de mise à jour des attributs et des sections.", "@sage/xtrem-stock/nodes__cost_roll_up_input_set__mutation__updateAttributesAndDimensions__parameter__costRollUpInputSet": "<PERSON>u de donn<PERSON> du calcul de coût", "@sage/xtrem-stock/nodes__cost_roll_up_input_set__mutation__updateAttributesAndDimensions__parameter__storedAttributes": "Attributs stockés", "@sage/xtrem-stock/nodes__cost_roll_up_input_set__mutation__updateAttributesAndDimensions__parameter__storedDimensions": "Sections stockées", "@sage/xtrem-stock/nodes__cost_roll_up_input_set__node_name": "<PERSON>u de donn<PERSON> du calcul de coût", "@sage/xtrem-stock/nodes__cost_roll_up_input_set__property__commodityCode": "Code marchandise", "@sage/xtrem-stock/nodes__cost_roll_up_input_set__property__costCategory": "<PERSON><PERSON><PERSON><PERSON> de <PERSON>", "@sage/xtrem-stock/nodes__cost_roll_up_input_set__property__fromDate": "Date début", "@sage/xtrem-stock/nodes__cost_roll_up_input_set__property__includesRouting": "Inclut la gamme", "@sage/xtrem-stock/nodes__cost_roll_up_input_set__property__itemCategory": "Catégorie d'article", "@sage/xtrem-stock/nodes__cost_roll_up_input_set__property__items": "Articles", "@sage/xtrem-stock/nodes__cost_roll_up_input_set__property__quantity": "Quantité", "@sage/xtrem-stock/nodes__cost_roll_up_input_set__property__resultLines": "Lignes de résultat", "@sage/xtrem-stock/nodes__cost_roll_up_input_set__property__site": "Site", "@sage/xtrem-stock/nodes__cost_roll_up_input_set__property__status": "Statut", "@sage/xtrem-stock/nodes__cost_roll_up_input_set__property__user": "Utilisa<PERSON>ur", "@sage/xtrem-stock/nodes__cost_roll_up_input_set__property__usesComponentStandardCost": "Utilisation du coût composant standard", "@sage/xtrem-stock/nodes__cost_roll_up_result_line__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-stock/nodes__cost_roll_up_result_line__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-stock/nodes__cost_roll_up_result_line__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-stock/nodes__cost_roll_up_result_line__bulkMutation__createItemSiteCostsFromCostRollUpResults": "Créer des coûts article-site à partir des résultats de calcul de coût", "@sage/xtrem-stock/nodes__cost_roll_up_result_line__bulkMutation__createItemSiteCostsFromCostRollUpResults__failed": "Échec de création des coûts article-site à partir des résultats de calculs de coût.", "@sage/xtrem-stock/nodes__cost_roll_up_result_line__item_site_cost_creation_finished": "Création du coût article-site terminée.", "@sage/xtrem-stock/nodes__cost_roll_up_result_line__no_line_processed_message": "Erreur lors du traitement des lignes. La sélection est vide.", "@sage/xtrem-stock/nodes__cost_roll_up_result_line__node_name": "Ligne de résultat du calcul de coût", "@sage/xtrem-stock/nodes__cost_roll_up_result_line__property__analyticalData": "Données analytiques", "@sage/xtrem-stock/nodes__cost_roll_up_result_line__property__creationErrorMessage": "Message d'erreur de création", "@sage/xtrem-stock/nodes__cost_roll_up_result_line__property__creationStatus": "Statut de création", "@sage/xtrem-stock/nodes__cost_roll_up_result_line__property__currentLaborCost": "Coût main d'oeuvre actuel", "@sage/xtrem-stock/nodes__cost_roll_up_result_line__property__currentMachineCost": "Coût machine actuel", "@sage/xtrem-stock/nodes__cost_roll_up_result_line__property__currentMaterialCost": "Coût matière actuel", "@sage/xtrem-stock/nodes__cost_roll_up_result_line__property__currentToolCost": "Coût outil actuel", "@sage/xtrem-stock/nodes__cost_roll_up_result_line__property__currentTotalCost": "Coût total actuel", "@sage/xtrem-stock/nodes__cost_roll_up_result_line__property__inputSet": "<PERSON><PERSON> <PERSON> don<PERSON>", "@sage/xtrem-stock/nodes__cost_roll_up_result_line__property__item": "Article", "@sage/xtrem-stock/nodes__cost_roll_up_result_line__property__laborCost": "Coût de main d'oeuvre", "@sage/xtrem-stock/nodes__cost_roll_up_result_line__property__machineCost": "Coût machine", "@sage/xtrem-stock/nodes__cost_roll_up_result_line__property__materialCost": "Co<PERSON>t matière", "@sage/xtrem-stock/nodes__cost_roll_up_result_line__property__storedAttributes": "Attributs stockés", "@sage/xtrem-stock/nodes__cost_roll_up_result_line__property__storedDimensions": "Sections stockées", "@sage/xtrem-stock/nodes__cost_roll_up_result_line__property__subAssemblyLines": "Lignes de sous-ensembles", "@sage/xtrem-stock/nodes__cost_roll_up_result_line__property__toolCost": "Coût outil", "@sage/xtrem-stock/nodes__cost_roll_up_result_line__property__totalCost": "Coût total", "@sage/xtrem-stock/nodes__cost_roll_up_sub_assembly__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-stock/nodes__cost_roll_up_sub_assembly__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-stock/nodes__cost_roll_up_sub_assembly__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-stock/nodes__cost_roll_up_sub_assembly__node_name": "Sous-ensemble de calcul de coût", "@sage/xtrem-stock/nodes__cost_roll_up_sub_assembly__property__calculationQuantity": "Quantité de calcul", "@sage/xtrem-stock/nodes__cost_roll_up_sub_assembly__property__componentNumber": "Numéro de composant", "@sage/xtrem-stock/nodes__cost_roll_up_sub_assembly__property__currentLaborCost": "Coût main d'oeuvre actuel", "@sage/xtrem-stock/nodes__cost_roll_up_sub_assembly__property__currentMachineCost": "Coût machine actuel", "@sage/xtrem-stock/nodes__cost_roll_up_sub_assembly__property__currentMaterialCost": "Coût matière actuel", "@sage/xtrem-stock/nodes__cost_roll_up_sub_assembly__property__currentToolCost": "Coût outil actuel", "@sage/xtrem-stock/nodes__cost_roll_up_sub_assembly__property__currentTotalCost": "Coût total actuel", "@sage/xtrem-stock/nodes__cost_roll_up_sub_assembly__property__item": "Article", "@sage/xtrem-stock/nodes__cost_roll_up_sub_assembly__property__laborCost": "Coût de main d'oeuvre", "@sage/xtrem-stock/nodes__cost_roll_up_sub_assembly__property__machineCost": "Coût machine", "@sage/xtrem-stock/nodes__cost_roll_up_sub_assembly__property__materialCost": "Co<PERSON>t matière", "@sage/xtrem-stock/nodes__cost_roll_up_sub_assembly__property__resultLine": "Ligne de résultat", "@sage/xtrem-stock/nodes__cost_roll_up_sub_assembly__property__stockUnit": "", "@sage/xtrem-stock/nodes__cost_roll_up_sub_assembly__property__toolCost": "Coût outil", "@sage/xtrem-stock/nodes__cost_roll_up_sub_assembly__property__totalCost": "Coût total", "@sage/xtrem-stock/nodes__item_site_cost__success_notification_title": "Calcul de coût standard achevé", "@sage/xtrem-stock/nodes__item-site-cost__failed_deletion_impossible_if_transactions": "Suppression non autorisée. La date de début du coût d'article-site est égale à la date actuelle et il existe un changement de valeur de stock pour ce coût d'article-site.", "@sage/xtrem-stock/nodes__item-site-extension__item_site_not_stock_managed": "L'article-site n'est pas géré en stock.", "@sage/xtrem-stock/nodes__mrp_calculation-extension__delete_old_mrp_calculations": "Anciens calculs CBN supprimés : {{number}}.", "@sage/xtrem-stock/nodes__mrp_calculation-extension__deleting_old_mrp_calculations": "Suppression des anciens calculs CBN ayant une date antérieure à : {{deletionDate}}.", "@sage/xtrem-stock/nodes__mrp_calculation-extension__no_old_mrp_calculations": "Pas d'anciens calculs CBN à supprimer.", "@sage/xtrem-stock/nodes__mrp_calculation-extension__start_delete_old_mrp_calculations": "Début de la suppression des anciens calculs CBN.", "@sage/xtrem-stock/nodes__stock_adjustment__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-stock/nodes__stock_adjustment__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-stock/nodes__stock_adjustment__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-stock/nodes__stock_adjustment__mutation__postToStock": "Comptabiliser en stock", "@sage/xtrem-stock/nodes__stock_adjustment__mutation__postToStock__failed": "Échec de la comptabilisation en stock.", "@sage/xtrem-stock/nodes__stock_adjustment__mutation__postToStock__parameter__documentIds": "Codes documents", "@sage/xtrem-stock/nodes__stock_adjustment__mutation__repost": "Recomptabiliser", "@sage/xtrem-stock/nodes__stock_adjustment__mutation__repost__failed": "Échec de la comptabilisation.", "@sage/xtrem-stock/nodes__stock_adjustment__mutation__repost__parameter__documentLines": "Lignes de documents", "@sage/xtrem-stock/nodes__stock_adjustment__mutation__repost__parameter__stockAdjustment": "Régularisation de stock", "@sage/xtrem-stock/nodes__stock_adjustment__mutation__resendNotificationForFinance": "Renvoyer la notification pour la finance", "@sage/xtrem-stock/nodes__stock_adjustment__mutation__resendNotificationForFinance__failed": "Échec de renvoi de notification pour la finance.", "@sage/xtrem-stock/nodes__stock_adjustment__mutation__resendNotificationForFinance__parameter__stockAdjustment": "Régularisation de stock", "@sage/xtrem-stock/nodes__stock_adjustment__mutation__resynchronizeStockTransactionStatus": "Resynchroniser le statut de transaction de stock", "@sage/xtrem-stock/nodes__stock_adjustment__mutation__resynchronizeStockTransactionStatus__failed": "Échec de resynchronisation du statut de transaction de stock.", "@sage/xtrem-stock/nodes__stock_adjustment__mutation__resynchronizeStockTransactionStatus__parameter__stockAdjustment": "Régularisation de stock", "@sage/xtrem-stock/nodes__stock_adjustment__no_sequence_number": "Le numéro de compteur de la régularisation de stock ne peut pas être généré. Renseignez d'abord un compteur par défaut.", "@sage/xtrem-stock/nodes__stock_adjustment__node_name": "Régularisation de stock", "@sage/xtrem-stock/nodes__stock_adjustment__property__description": "Description", "@sage/xtrem-stock/nodes__stock_adjustment__property__displayStatus": "Statut d'affichage", "@sage/xtrem-stock/nodes__stock_adjustment__property__documentDate": "Date document", "@sage/xtrem-stock/nodes__stock_adjustment__property__effectiveDate": "Date effective", "@sage/xtrem-stock/nodes__stock_adjustment__property__financeIntegrationStatus": "Statut intégration financière", "@sage/xtrem-stock/nodes__stock_adjustment__property__financialSite": "Site financier", "@sage/xtrem-stock/nodes__stock_adjustment__property__forceUpdateForFinance": "Forcer la mise à jour pour la finance", "@sage/xtrem-stock/nodes__stock_adjustment__property__forceUpdateForStock": "Forcer la mise à jour pour le stock", "@sage/xtrem-stock/nodes__stock_adjustment__property__isSetDimensionsMainListHidden": "Définir sections liste principale Caché", "@sage/xtrem-stock/nodes__stock_adjustment__property__lines": "<PERSON><PERSON><PERSON>", "@sage/xtrem-stock/nodes__stock_adjustment__property__number": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-stock/nodes__stock_adjustment__property__postingDetails": "Détails de comptabilisation", "@sage/xtrem-stock/nodes__stock_adjustment__property__reasonCode": "Code motif", "@sage/xtrem-stock/nodes__stock_adjustment__property__status": "Statut", "@sage/xtrem-stock/nodes__stock_adjustment__property__stockSite": "Site de stock", "@sage/xtrem-stock/nodes__stock_adjustment__property__stockTransactionStatus": "Statut de transaction de stock", "@sage/xtrem-stock/nodes__stock_adjustment__property__transactionCurrency": "Devise de transaction", "@sage/xtrem-stock/nodes__stock_adjustment_detail_extension__lot_mandatory": "<PERSON><PERSON> de<PERSON> sélectionner un lot pour l'article : {{itemId}}. Inventaire {{number}}, ligne {{lineNumber}}.", "@sage/xtrem-stock/nodes__stock_adjustment_line__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-stock/nodes__stock_adjustment_line__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-stock/nodes__stock_adjustment_line__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-stock/nodes__stock_adjustment_line__cannot_update_completed_line": "Une ligne Régularisée ne peut pas être mise à jour.", "@sage/xtrem-stock/nodes__stock_adjustment_line__incorrect_valuation_method": "L'article {{item}} doit être associé à une méthode de valorisation par prix moyen pondéré.", "@sage/xtrem-stock/nodes__stock_adjustment_line__mutation__setDimension": "Définir la section", "@sage/xtrem-stock/nodes__stock_adjustment_line__mutation__setDimension__failed": "Échec de paramétrage de section.", "@sage/xtrem-stock/nodes__stock_adjustment_line__mutation__setDimension__parameter__stockAdjustmentLine": "Ligne de régularisation de stock", "@sage/xtrem-stock/nodes__stock_adjustment_line__mutation__setDimension__parameter__storedAttributes": "Attributs stockés", "@sage/xtrem-stock/nodes__stock_adjustment_line__mutation__setDimension__parameter__storedDimensions": "Sections stockées", "@sage/xtrem-stock/nodes__stock_adjustment_line__node_name": "Ligne de régularisation de stock", "@sage/xtrem-stock/nodes__stock_adjustment_line__property__adjustmentQuantityInStockUnit": "Quantité de régularisation en unité de stock", "@sage/xtrem-stock/nodes__stock_adjustment_line__property__analyticalData": "Données analytiques", "@sage/xtrem-stock/nodes__stock_adjustment_line__property__computedAttributes": "Attributs calculés", "@sage/xtrem-stock/nodes__stock_adjustment_line__property__displayStatus": "Statut d'affichage", "@sage/xtrem-stock/nodes__stock_adjustment_line__property__document": "Document", "@sage/xtrem-stock/nodes__stock_adjustment_line__property__documentId": "Code de document", "@sage/xtrem-stock/nodes__stock_adjustment_line__property__documentNumber": "Numéro de document", "@sage/xtrem-stock/nodes__stock_adjustment_line__property__item": "Article", "@sage/xtrem-stock/nodes__stock_adjustment_line__property__itemSite": "Article-site", "@sage/xtrem-stock/nodes__stock_adjustment_line__property__jsonStockDetails": "Détails de stock JSON", "@sage/xtrem-stock/nodes__stock_adjustment_line__property__location": "Emplacement", "@sage/xtrem-stock/nodes__stock_adjustment_line__property__lot": "Lot", "@sage/xtrem-stock/nodes__stock_adjustment_line__property__newStockQuantity": "Nouvelle quantité stock", "@sage/xtrem-stock/nodes__stock_adjustment_line__property__newStockValue": "Nouvelle valeur stock", "@sage/xtrem-stock/nodes__stock_adjustment_line__property__newUnitCost": "Nouveau coût unitaire", "@sage/xtrem-stock/nodes__stock_adjustment_line__property__orderCost": "Coût de l'ordre", "@sage/xtrem-stock/nodes__stock_adjustment_line__property__quantityInStockUnit": "Quantité en unité de stock", "@sage/xtrem-stock/nodes__stock_adjustment_line__property__site": "Site", "@sage/xtrem-stock/nodes__stock_adjustment_line__property__stockDetails": "Détails de stock", "@sage/xtrem-stock/nodes__stock_adjustment_line__property__stockDetailStatus": "Statut détail stock", "@sage/xtrem-stock/nodes__stock_adjustment_line__property__stockMovements": "Mouvements de stock", "@sage/xtrem-stock/nodes__stock_adjustment_line__property__stockStatus": "Statut de stock", "@sage/xtrem-stock/nodes__stock_adjustment_line__property__stockTransactions": "Transaction de stock", "@sage/xtrem-stock/nodes__stock_adjustment_line__property__stockTransactionStatus": "Statut de transaction de stock", "@sage/xtrem-stock/nodes__stock_adjustment_line__property__stockUnit": "Unité de stock", "@sage/xtrem-stock/nodes__stock_adjustment_line__property__stockValue": "Valeur du stock", "@sage/xtrem-stock/nodes__stock_adjustment_line__property__storedAttributes": "Attributs stockés", "@sage/xtrem-stock/nodes__stock_adjustment_line__property__storedDimensions": "Sections stockées", "@sage/xtrem-stock/nodes__stock_adjustment_line__property__unitCost": "Coût unitaire", "@sage/xtrem-stock/nodes__stock_adjustment_line__property__valuedCost": "Coût valorisé", "@sage/xtrem-stock/nodes__stock_change___duplicated_serial_number": "Des numéros de série sont dupliqués. Assurez-vous que chaque ligne contient des numéros de série uniques.", "@sage/xtrem-stock/nodes__stock_change___wrong_number_of_serial_numbers": "Le nombre de numéros de série alloués doit correspondre à la quantité pour la ligne. Ajouter des numéros de série pour respecter la quantité.", "@sage/xtrem-stock/nodes__stock_change__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-stock/nodes__stock_change__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-stock/nodes__stock_change__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-stock/nodes__stock_change__mutation__postToStock": "Comptabiliser en stock", "@sage/xtrem-stock/nodes__stock_change__mutation__postToStock__failed": "Échec de la comptabilisation en stock.", "@sage/xtrem-stock/nodes__stock_change__mutation__postToStock__parameter__documentIds": "Codes documents", "@sage/xtrem-stock/nodes__stock_change__node_name": "Changement de stock", "@sage/xtrem-stock/nodes__stock_change__property__description": "Description", "@sage/xtrem-stock/nodes__stock_change__property__effectiveDate": "Date effective", "@sage/xtrem-stock/nodes__stock_change__property__forceUpdateForStock": "Forcer la mise à jour pour le stock", "@sage/xtrem-stock/nodes__stock_change__property__item": "Article", "@sage/xtrem-stock/nodes__stock_change__property__itemSite": "Article-site", "@sage/xtrem-stock/nodes__stock_change__property__lines": "<PERSON><PERSON><PERSON>", "@sage/xtrem-stock/nodes__stock_change__property__location": "Emplacement", "@sage/xtrem-stock/nodes__stock_change__property__lot": "Lot", "@sage/xtrem-stock/nodes__stock_change__property__number": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-stock/nodes__stock_change__property__owner": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-stock/nodes__stock_change__property__postingError": "Erreur de comptabilisation", "@sage/xtrem-stock/nodes__stock_change__property__status": "Statut", "@sage/xtrem-stock/nodes__stock_change__property__stockSite": "Site de stock", "@sage/xtrem-stock/nodes__stock_change__property__stockStatus": "Statut de stock", "@sage/xtrem-stock/nodes__stock_change__property__stockTransactionStatus": "Statut de transaction de stock", "@sage/xtrem-stock/nodes__stock_change__property__stockUnit": "Unité de stock", "@sage/xtrem-stock/nodes__stock_change_line__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-stock/nodes__stock_change_line__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-stock/nodes__stock_change_line__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-stock/nodes__stock_change_line__location_site_must_match": "Le site d'emplacement et le site de stock doivent être identiques.", "@sage/xtrem-stock/nodes__stock_change_line__no_change": "Aucun changement à traiter sur une ligne. Utilisez un statut de stock ou un emplacement différents pour cette ligne.", "@sage/xtrem-stock/nodes__stock_change_line__node_name": "Ligne de changement de stock", "@sage/xtrem-stock/nodes__stock_change_line__property__document": "Document", "@sage/xtrem-stock/nodes__stock_change_line__property__documentId": "Code de document", "@sage/xtrem-stock/nodes__stock_change_line__property__documentNumber": "Numéro de document", "@sage/xtrem-stock/nodes__stock_change_line__property__item": "Article", "@sage/xtrem-stock/nodes__stock_change_line__property__jsonStockDetails": "Détails de stock JSON", "@sage/xtrem-stock/nodes__stock_change_line__property__location": "Emplacement", "@sage/xtrem-stock/nodes__stock_change_line__property__orderCost": "Coût de l'ordre", "@sage/xtrem-stock/nodes__stock_change_line__property__quantityInStockUnit": "Quantité en unité de stock", "@sage/xtrem-stock/nodes__stock_change_line__property__stockDetails": "Détails de stock", "@sage/xtrem-stock/nodes__stock_change_line__property__stockStatus": "Statut de stock", "@sage/xtrem-stock/nodes__stock_change_line__property__stockTransactions": "Transaction de stock", "@sage/xtrem-stock/nodes__stock_change_line__property__stockTransactionStatus": "Statut de transaction de stock", "@sage/xtrem-stock/nodes__stock_change_line__property__valuedCost": "Coût valorisé", "@sage/xtrem-stock/nodes__stock_count__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-stock/nodes__stock_count__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-stock/nodes__stock_count__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-stock/nodes__stock_count__asyncMutation__confirmStockCount": "Confirmer l'inventaire", "@sage/xtrem-stock/nodes__stock_count__asyncMutation__confirmStockCount__failed": "Échec de confirmation d''inventaire.", "@sage/xtrem-stock/nodes__stock_count__asyncMutation__confirmStockCount__parameter__selectedRecords": "Enregistrements sélectionnés", "@sage/xtrem-stock/nodes__stock_count__asyncMutation__confirmStockCount__parameter__stockCount": "Inventaire", "@sage/xtrem-stock/nodes__stock_count__asyncMutation__confirmZeroQuantity": "Confirmer la quantité nulle", "@sage/xtrem-stock/nodes__stock_count__asyncMutation__confirmZeroQuantity__failed": "Échec de confirmation de quantité nulle.", "@sage/xtrem-stock/nodes__stock_count__asyncMutation__confirmZeroQuantity__parameter__number": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-stock/nodes__stock_count__cant_repost_stock_count_when_status_is_not_failed": "Vous pouvez uniquement recomptabiliser un inventaire si son statut est 'Échec' ou 'Non enregistrée'.", "@sage/xtrem-stock/nodes__stock_count__closed_confirm_zero_cannot_be_performed": "Vous ne pouvez pas mettre à jour les quantités d'un inventaire soldé : inventaire {{number}}.", "@sage/xtrem-stock/nodes__stock_count__company_has_no_stock_posting": "Le stock n'a pas été comptabilisé pour cette société.", "@sage/xtrem-stock/nodes__stock_count__confirm_stock_completed": "Lignes d'inventaire créées : {{number}}", "@sage/xtrem-stock/nodes__stock_count__confirm_stock_count_none_stock_records_to_stock_count_line": "Lignes d'inventaire créées à partir d'enregistrements hors stock : {{itemSiteCounter}}.", "@sage/xtrem-stock/nodes__stock_count__confirm_stock_count_stock_record_excluded": "L'enregistrement de stock pour l'article {{item}} est exclu. Il fait déjà partie d'un autre inventaire : {{existingRecord}}..", "@sage/xtrem-stock/nodes__stock_count__confirm_stock_count_stock_records_to_stock_count_line": "Lignes d'inventaire créées à partir d'enregistrements de stock : {{itemSiteCounter}}.", "@sage/xtrem-stock/nodes__stock_count__counted_quantity_less_than_allocated_quantity": "Pour l'inventaire {{number}}, la quantité comptée {{countedQuantity}} pour {{item}} ne peut pas être inférieure à la quantité allouée {{totalAllocated}}.", "@sage/xtrem-stock/nodes__stock_count__counted_stock_record_not_found": "L'enregistrement de stock pour l'article {{item}} a changé depuis que l'inventaire a démarré. Exclure cette ligne de l'inventaire.", "@sage/xtrem-stock/nodes__stock_count__counting_is_not_finished_yet": "Vous ne pouvez pas comptabiliser un inventaire alors que le comptage est toujours en cours : inventaire {{number}}.", "@sage/xtrem-stock/nodes__stock_count__deletion_forbidden": "Vous ne pouvez pas supprimer l'inventaire.", "@sage/xtrem-stock/nodes__stock_count__document_was_posted": "L'inventaire a été comptabilisé.", "@sage/xtrem-stock/nodes__stock_count__end_confirm_zero": "Le processus de confirmation de quantités nulles est terminé pour l'inventaire : {{number}}. Lignes mises à jour : {{recordsUpdated}}.", "@sage/xtrem-stock/nodes__stock_count__mutation__postToStock": "Comptabiliser en stock", "@sage/xtrem-stock/nodes__stock_count__mutation__postToStock__failed": "Échec de la comptabilisation en stock.", "@sage/xtrem-stock/nodes__stock_count__mutation__postToStock__parameter__documentIds": "Codes documents", "@sage/xtrem-stock/nodes__stock_count__mutation__repost": "Recomptabiliser", "@sage/xtrem-stock/nodes__stock_count__mutation__repost__failed": "Échec de la comptabilisation.", "@sage/xtrem-stock/nodes__stock_count__mutation__repost__parameter__documentLines": "Lignes de documents", "@sage/xtrem-stock/nodes__stock_count__mutation__repost__parameter__stockCount": "Inventaire", "@sage/xtrem-stock/nodes__stock_count__mutation__resendNotificationForFinance": "Renvoyer la notification pour la finance", "@sage/xtrem-stock/nodes__stock_count__mutation__resendNotificationForFinance__failed": "Échec du renvoi de la notification pour la finance.", "@sage/xtrem-stock/nodes__stock_count__mutation__resendNotificationForFinance__parameter__stockCount": "Inventaire", "@sage/xtrem-stock/nodes__stock_count__mutation__start": "D<PERSON>but", "@sage/xtrem-stock/nodes__stock_count__mutation__start__failed": "Échec du démarrage.", "@sage/xtrem-stock/nodes__stock_count__mutation__start__parameter__document": "Document", "@sage/xtrem-stock/nodes__stock_count__no_records_selected": "Aucun enregistrement sélectionné pour l'inventaire {{number}}.", "@sage/xtrem-stock/nodes__stock_count__node_name": "Inventaire", "@sage/xtrem-stock/nodes__stock_count__property__categories": "Catégories", "@sage/xtrem-stock/nodes__stock_count__property__concurrentStockCountLines": "Ligne d'inventaire concurrente", "@sage/xtrem-stock/nodes__stock_count__property__counter": "Compteur", "@sage/xtrem-stock/nodes__stock_count__property__description": "Description", "@sage/xtrem-stock/nodes__stock_count__property__documentDate": "Date document", "@sage/xtrem-stock/nodes__stock_count__property__effectiveDate": "Date effective", "@sage/xtrem-stock/nodes__stock_count__property__financeIntegrationStatus": "Statut d'intégration financière", "@sage/xtrem-stock/nodes__stock_count__property__financialSite": "Site financier", "@sage/xtrem-stock/nodes__stock_count__property__forceUpdateForStock": "Forcer la mise à jour pour le stock", "@sage/xtrem-stock/nodes__stock_count__property__fromItem": "Article début", "@sage/xtrem-stock/nodes__stock_count__property__hasExpiryManagementInLines": "A la gestion de l'expiration dans les lignes", "@sage/xtrem-stock/nodes__stock_count__property__hasLotInLines": "A le lot dans les lignes", "@sage/xtrem-stock/nodes__stock_count__property__hasStockRecords": "Existence d'enregistrements de stock", "@sage/xtrem-stock/nodes__stock_count__property__hasSublotInLines": "A le sous-lot dans les lignes", "@sage/xtrem-stock/nodes__stock_count__property__itemSites": "Articles-site", "@sage/xtrem-stock/nodes__stock_count__property__lastCountDate": "Date dernier inventaire", "@sage/xtrem-stock/nodes__stock_count__property__lines": "<PERSON><PERSON><PERSON>", "@sage/xtrem-stock/nodes__stock_count__property__locations": "Emplacements", "@sage/xtrem-stock/nodes__stock_count__property__number": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-stock/nodes__stock_count__property__postingDetails": "Détails de comptabilisation", "@sage/xtrem-stock/nodes__stock_count__property__status": "Statut", "@sage/xtrem-stock/nodes__stock_count__property__stockSite": "Site de stock", "@sage/xtrem-stock/nodes__stock_count__property__stockTransactionStatus": "Statut de transaction de stock", "@sage/xtrem-stock/nodes__stock_count__property__toItem": "Article fin", "@sage/xtrem-stock/nodes__stock_count__property__transactionCurrency": "Devise de transaction", "@sage/xtrem-stock/nodes__stock_count__property__zones": "Zones", "@sage/xtrem-stock/nodes__stock_count__query__existingStockCountLine": "Ligne d'inventaire existant", "@sage/xtrem-stock/nodes__stock_count__query__existingStockCountLine__failed": "Échec de la ligne d'inventaire existant.", "@sage/xtrem-stock/nodes__stock_count__query__existingStockCountLine__parameter__lotCreateData": "Données de création de lots", "@sage/xtrem-stock/nodes__stock_count__query__existingStockCountLine__parameter__searchCriteria": "Critères de recherche", "@sage/xtrem-stock/nodes__stock_count__query__existingStockCountLine__parameter__throwErrorIfExist": "<PERSON><PERSON>er une erreur si elle existe", "@sage/xtrem-stock/nodes__stock_count__start_confirm_stock_count": "Le processus de création des lignes d'inventaire a démarré : {{number}}.", "@sage/xtrem-stock/nodes__stock_count__start_confirm_zero": "Le processus de confirmation de quantités nulles a débuté pour l'inventaire : {{number}}.", "@sage/xtrem-stock/nodes__stock_count__to_be_counted_confirm_zero_cannot_be_performed": "L'inventaire n'a pas encore débuté : {{number}}. <PERSON><PERSON> le comptage pour mettre à jours les quantités.", "@sage/xtrem-stock/nodes__stock_count_line___duplicated_serial_number": "Des numéros de série sont dupliqués. Assurez-vous que chaque ligne contient des numéros de série uniques.", "@sage/xtrem-stock/nodes__stock_count_line__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-stock/nodes__stock_count_line__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-stock/nodes__stock_count_line__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-stock/nodes__stock_count_line__cannot_add_line_if_counting_closed": "Vous ne pouvez pas ajouter de ligne si l'inventaire est Soldé.", "@sage/xtrem-stock/nodes__stock_count_line__delete_not_allowed": "Une ligne ajoutée au début de l'inventaire ne peut pas être supprimée. Inventaire {{number}}, ligne {{lineNumber}}.", "@sage/xtrem-stock/nodes__stock_count_line__expiration_date_mandatory": "<PERSON><PERSON> <PERSON> sélectionner une date d'expiration pour l'article : {{itemId}}, et pour le lot : {{lotId}}.", "@sage/xtrem-stock/nodes__stock_count_line__forbid_addition_serialized_managed_item": "Une ligne de stock pour un article sérialisé ne peut pas être ajouté à un inventaire existant.", "@sage/xtrem-stock/nodes__stock_count_line__item_cannot_be_modified": "L'article ne peut pas être modifié. Inventaire {{number}}, ligne {{lineNumber}}.", "@sage/xtrem-stock/nodes__stock_count_line__item_of_stock_detail_must_be_the_same_as_item_of_stock_count_line": "L'article du détail de stock et l'article de la ligne d'inventaire doivent être identiques. Inventaire {{number}}, ligne {{lineNumber}}.", "@sage/xtrem-stock/nodes__stock_count_line__line_posted_delete_not_allowed": "La ligne d'inventaire ne peut pas être supprimée. Elle a déjà été comptabilisée. Inventaire {{number}}, ligne {{lineNumber}}.", "@sage/xtrem-stock/nodes__stock_count_line__location_cannot_be_modified": "L'emplacement ne peut pas être modifié. Inventaire {{number}}, ligne {{lineNumber}}.", "@sage/xtrem-stock/nodes__stock_count_line__location_of_stock_detail_must_be_the_same_as_location_of_stock_count_line": "L'emplacement du détail de stock et l'emplacement de la ligne d'inventaire doivent être identiques. Inventaire {{number}}, ligne {{lineNumber}}.", "@sage/xtrem-stock/nodes__stock_count_line__lot_cannot_be_modified": "Le lot ne peut pas être modifié. Inventaire {{number}}, ligne {{lineNumber}}.", "@sage/xtrem-stock/nodes__stock_count_line__lot_mandatory": "<PERSON><PERSON> de<PERSON> sélectionner un lot pour l'article : {{itemId}}.", "@sage/xtrem-stock/nodes__stock_count_line__node_name": "Ligne d'inventaire", "@sage/xtrem-stock/nodes__stock_count_line__owner_cannot_be_modified": "Le propriétaire ne peut pas être modifié. Inventaire {{number}}, ligne {{lineNumber}}.", "@sage/xtrem-stock/nodes__stock_count_line__owner_of_stock_detail_must_be_the_same_as_owner_of_stock_count_line": "Le propriétaire du détail de stock et de la ligne d'inventaire doit être identique. Inventaire {{number}}, ligne {{lineNumber}}.", "@sage/xtrem-stock/nodes__stock_count_line__property__adjustmentQuantityInStockUnit": "Quantité de régularisation en unité de stock", "@sage/xtrem-stock/nodes__stock_count_line__property__allocations": "Allocations", "@sage/xtrem-stock/nodes__stock_count_line__property__analyticalData": "Données analytiques", "@sage/xtrem-stock/nodes__stock_count_line__property__canBeDeleted": "Peut être supprimée", "@sage/xtrem-stock/nodes__stock_count_line__property__canEnterOrderCost": "Peut renseigner coût de l'ordre/commande", "@sage/xtrem-stock/nodes__stock_count_line__property__computedAttributes": "Attributs calculés", "@sage/xtrem-stock/nodes__stock_count_line__property__countedQuantityInStockUnit": "Quantité comptée en unité de stock", "@sage/xtrem-stock/nodes__stock_count_line__property__countedSerialNumber": "Numéro de série compté", "@sage/xtrem-stock/nodes__stock_count_line__property__countedSerialNumberPercentage": "Pourcentage de numéros de série comptés", "@sage/xtrem-stock/nodes__stock_count_line__property__document": "Document", "@sage/xtrem-stock/nodes__stock_count_line__property__documentId": "Code de document", "@sage/xtrem-stock/nodes__stock_count_line__property__documentNumber": "Numéro de document", "@sage/xtrem-stock/nodes__stock_count_line__property__forceUpdateForFinance": "Forcer la mise à jour pour la Finance", "@sage/xtrem-stock/nodes__stock_count_line__property__forceUpdateForStock": "Mise à jour forcée pour le Stock", "@sage/xtrem-stock/nodes__stock_count_line__property__hasAllocationError": "Erreur d'allocation", "@sage/xtrem-stock/nodes__stock_count_line__property__isAddedDuringCount": "Ajoutée en cours d'inventaire", "@sage/xtrem-stock/nodes__stock_count_line__property__isCreatedFromStockRecord": "Est créée à partir de l'enregistrement de stock", "@sage/xtrem-stock/nodes__stock_count_line__property__isNonStockItem": "Est un article hors stock", "@sage/xtrem-stock/nodes__stock_count_line__property__isStockCountCreation": "Est une création d'inventaire", "@sage/xtrem-stock/nodes__stock_count_line__property__item": "Article", "@sage/xtrem-stock/nodes__stock_count_line__property__itemSite": "Article-site", "@sage/xtrem-stock/nodes__stock_count_line__property__jsonSerialNumbers": "Numéros de série JSON", "@sage/xtrem-stock/nodes__stock_count_line__property__jsonStockDetails": "Détails de stock JSON", "@sage/xtrem-stock/nodes__stock_count_line__property__location": "Emplacement", "@sage/xtrem-stock/nodes__stock_count_line__property__lot": "Lot", "@sage/xtrem-stock/nodes__stock_count_line__property__newLineOrderCost": "Nouveau coût de commande de ligne", "@sage/xtrem-stock/nodes__stock_count_line__property__owner": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-stock/nodes__stock_count_line__property__quantityInStockUnit": "Quantité en unité de stock", "@sage/xtrem-stock/nodes__stock_count_line__property__quantityVariance": "<PERSON>cart de quantité", "@sage/xtrem-stock/nodes__stock_count_line__property__quantityVariancePercentage": "Pourcentage d'écart de quantité", "@sage/xtrem-stock/nodes__stock_count_line__property__serialNumbers": "", "@sage/xtrem-stock/nodes__stock_count_line__property__shouldEnterOrderCost": "Doit renseigner coût de l'ordre/commande", "@sage/xtrem-stock/nodes__stock_count_line__property__site": "Site", "@sage/xtrem-stock/nodes__stock_count_line__property__status": "Statut", "@sage/xtrem-stock/nodes__stock_count_line__property__stockCountLineSerialNumbers": "Numéros de série des lignes d'inventaire", "@sage/xtrem-stock/nodes__stock_count_line__property__stockDetail": "Détail de stock", "@sage/xtrem-stock/nodes__stock_count_line__property__stockDetails": "Détails de stock", "@sage/xtrem-stock/nodes__stock_count_line__property__stockMovements": "Mouvements de stock", "@sage/xtrem-stock/nodes__stock_count_line__property__stockRecord": "Fiche de stock", "@sage/xtrem-stock/nodes__stock_count_line__property__stockStatus": "Statut stock", "@sage/xtrem-stock/nodes__stock_count_line__property__stockTransactions": "Transaction de stock", "@sage/xtrem-stock/nodes__stock_count_line__property__stockTransactionStatus": "Statut de transaction de stock", "@sage/xtrem-stock/nodes__stock_count_line__property__stockUnit": "Unité de stock", "@sage/xtrem-stock/nodes__stock_count_line__property__storedAttributes": "Attributs stockés", "@sage/xtrem-stock/nodes__stock_count_line__property__storedDimensions": "Sections stockées", "@sage/xtrem-stock/nodes__stock_count_line__property__totalAllocated": "Total alloué", "@sage/xtrem-stock/nodes__stock_count_line__property__zone": "Zone", "@sage/xtrem-stock/nodes__stock_count_line__query__getStockQuantity": "Obtenir quantité stock", "@sage/xtrem-stock/nodes__stock_count_line__query__getStockQuantity__failed": "Échec de la valorisation de stock.", "@sage/xtrem-stock/nodes__stock_count_line__query__getStockQuantity__parameter__stockCountLine": "Ligne d'inventaire", "@sage/xtrem-stock/nodes__stock_count_line__selected_quantity_too_high": "La quantité de numéros de série sélectionnés ({{selectedQuantity}}) ne doit pas dépasser la quantité comptée ({{countedQuantity}}).", "@sage/xtrem-stock/nodes__stock_count_line__site_of_stock_detail_must_be_the_same_as_site_of_stock_count_line": "Le site du détail de stock et de la ligne d'inventaire doit être identique. Inventaire {{number}}, ligne {{lineNumber}}.", "@sage/xtrem-stock/nodes__stock_count_line__status_of_stock_detail_must_be_the_same_as_status_of_stock_count_line": "Le statut du détail de stock et de la ligne d'inventaire doit être identique. Inventaire {{number}}, ligne {{lineNumber}}.", "@sage/xtrem-stock/nodes__stock_count_line__stock_detail_inconsistent_with_line": "Le détail de stock ne correspond pas à la ligne.", "@sage/xtrem-stock/nodes__stock_count_line__stock_record_not_found": "Enregistrement de stock introuvable. Inventaire {{number}}, ligne {{lineNumber}}.", "@sage/xtrem-stock/nodes__stock_count_line__stock_status_cannot_be_modified": "Le statut de stock ne peut pas être modifié. Inventaire {{number}}, ligne {{lineNumber}}.", "@sage/xtrem-stock/nodes__stock_count_line__stock_unit_of_stock_detail_must_be_the_same_as_stock_unit_of_stock_count_line": "L'unité de stock pour le détail de stock et la ligne d'inventaire doit être identique. Inventaire {{number}}, ligne {{lineNumber}}.", "@sage/xtrem-stock/nodes__stock_count_line__sublot_mandatory": "<PERSON><PERSON> de<PERSON> sélectionner un sous-lot pour l'article : {{itemId}}.", "@sage/xtrem-stock/nodes__stock_count_line__update_forbidden_count_line_posted": "La ligne stockCountLine {{id}} ne peut pas être mise à jour. Elle a déjà été comptabilisée.", "@sage/xtrem-stock/nodes__stock_count_line_serial_number__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-stock/nodes__stock_count_line_serial_number__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-stock/nodes__stock_count_line_serial_number__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-stock/nodes__stock_count_line_serial_number__available_error": "Numéros de série disponibles dans cette borne : {{available}}.", "@sage/xtrem-stock/nodes__stock_count_line_serial_number__cannot_delete_count_started": "Le numéro de série d'une ligne d'inventaire ne peut pas être supprimé après le début de l'inventaire. Article {{itemId}}, numéro de série {{serialNumber}}.", "@sage/xtrem-stock/nodes__stock_count_line_serial_number__cannot_delete_serial_still_in_stock": "Le numéro de série d'une ligne d'inventaire ne peut pas être supprimé lorsqu'il est associé à du stock compté. Article {{itemId}}, numéro de série {{serialNumber}}.", "@sage/xtrem-stock/nodes__stock_count_line_serial_number__invalid_quantity_error": "Le numéro de série de fin ne peut pas être calculé. Vérifier le numéro de série de début et la quantité.", "@sage/xtrem-stock/nodes__stock_count_line_serial_number__invalid_starting_serial_number_error": "Le numéro de série n'est pas affecté à une ligne d'inventaire. Numéro {{serialNumber}}.", "@sage/xtrem-stock/nodes__stock_count_line_serial_number__node_name": "Numéro de série de la ligne d'inventaire", "@sage/xtrem-stock/nodes__stock_count_line_serial_number__property__isCounted": "Compté", "@sage/xtrem-stock/nodes__stock_count_line_serial_number__property__serialNumber": "Numéro de série", "@sage/xtrem-stock/nodes__stock_count_line_serial_number__property__stockCountLine": "Ligne d'inventaire", "@sage/xtrem-stock/nodes__stock_count_line_serial_number__query__getEndingSerialNumber": "Obtenir n° de série fin", "@sage/xtrem-stock/nodes__stock_count_line_serial_number__query__getEndingSerialNumber__failed": "Échec d'obtention de n° de série fin.", "@sage/xtrem-stock/nodes__stock_count_line_serial_number__query__getEndingSerialNumber__parameter__quantity": "Quantité", "@sage/xtrem-stock/nodes__stock_count_line_serial_number__query__getEndingSerialNumber__parameter__startingSerialNumber": "Numéro de série début", "@sage/xtrem-stock/nodes__stock_count_line_serial_number__query__getEndingSerialNumber__parameter__stockCountLineId": "ID de ligne d'inventaire", "@sage/xtrem-stock/nodes__stock_count_line_serial_number__serial_number_not_assigned_to_a_stock_record": "Le numéro de série {{serialNumber}} n'est pas attribué à une ligne de stock.", "@sage/xtrem-stock/nodes__stock_count_line_serial_number__stock_count_line_and_serial_number_dont_match": "La ligne d'inventaire et le numéro de série {{serialNumber}} ne correspondent pas à la même ligne de stock.", "@sage/xtrem-stock/nodes__stock_count_line_serial_number__zero_quantity_error": "Renseignez une quantité supérieure à 0.", "@sage/xtrem-stock/nodes__stock_detail__exceeds_stock_record_quantity": "La quantité de {{qty1}} allouée au détail de stock n° {{detailNb}} dépasse la quantité de la fiche de stock {{qty2}}.", "@sage/xtrem-stock/nodes__stock_detail__stock_record_has_been_deleted": "La sortie de stock ne peut pas être effectuée parce que l'enregistrement de stock a été supprimé par une autre transaction.", "@sage/xtrem-stock/nodes__stock_issue___duplicated_serial_number": "Des numéros de série sont dupliqués. Assurez-vous que chaque ligne contient des numéros de série uniques.", "@sage/xtrem-stock/nodes__stock_issue__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-stock/nodes__stock_issue__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-stock/nodes__stock_issue__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-stock/nodes__stock_issue__mutation__postToStock": "Comptabiliser en stock", "@sage/xtrem-stock/nodes__stock_issue__mutation__postToStock__failed": "Échec de la comptabilisation en stock.", "@sage/xtrem-stock/nodes__stock_issue__mutation__postToStock__parameter__documentIds": "Codes documents", "@sage/xtrem-stock/nodes__stock_issue__mutation__repost": "Recomptabiliser", "@sage/xtrem-stock/nodes__stock_issue__mutation__repost__failed": "Échec de la comptabilisation.", "@sage/xtrem-stock/nodes__stock_issue__mutation__repost__parameter__documentLines": "Lignes de documents", "@sage/xtrem-stock/nodes__stock_issue__mutation__repost__parameter__stockIssue": "Sortie de stock", "@sage/xtrem-stock/nodes__stock_issue__mutation__resendNotificationForFinance": "Renvoyer notification pour la finance", "@sage/xtrem-stock/nodes__stock_issue__mutation__resendNotificationForFinance__failed": "Échec du renvoi de la notification pour la finance.", "@sage/xtrem-stock/nodes__stock_issue__mutation__resendNotificationForFinance__parameter__stockIssue": "Sortie de stock", "@sage/xtrem-stock/nodes__stock_issue__no_sequence_number": "Le numéro de compteur de la sortie de stock ne peut pas être généré. Renseignez d'abord un compteur par défaut.", "@sage/xtrem-stock/nodes__stock_issue__node_name": "Sortie de stock", "@sage/xtrem-stock/nodes__stock_issue__property__description": "Description", "@sage/xtrem-stock/nodes__stock_issue__property__displayStatus": "Statut d'affichage", "@sage/xtrem-stock/nodes__stock_issue__property__documentDate": "Date document", "@sage/xtrem-stock/nodes__stock_issue__property__effectiveDate": "Date effective", "@sage/xtrem-stock/nodes__stock_issue__property__financeIntegrationStatus": "Statut intégration financière", "@sage/xtrem-stock/nodes__stock_issue__property__financialSite": "Site financier", "@sage/xtrem-stock/nodes__stock_issue__property__forceUpdateForStock": "Forcer la mise à jour pour le stock", "@sage/xtrem-stock/nodes__stock_issue__property__isSetDimensionsMainListHidden": "Définir sections liste principale caché", "@sage/xtrem-stock/nodes__stock_issue__property__lines": "<PERSON><PERSON><PERSON>", "@sage/xtrem-stock/nodes__stock_issue__property__number": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-stock/nodes__stock_issue__property__postingDetails": "Détails de comptabilisation", "@sage/xtrem-stock/nodes__stock_issue__property__reasonCode": "Code motif", "@sage/xtrem-stock/nodes__stock_issue__property__status": "Statut", "@sage/xtrem-stock/nodes__stock_issue__property__stockSite": "Site de stock", "@sage/xtrem-stock/nodes__stock_issue__property__stockTransactionStatus": "Statut de transaction de stock", "@sage/xtrem-stock/nodes__stock_issue__property__transactionCurrency": "Devise de transaction", "@sage/xtrem-stock/nodes__stock_issue_line__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-stock/nodes__stock_issue_line__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-stock/nodes__stock_issue_line__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-stock/nodes__stock_issue_line__mutation__setDimension": "Définir la section", "@sage/xtrem-stock/nodes__stock_issue_line__mutation__setDimension__failed": "Échec de paramétrage de section.", "@sage/xtrem-stock/nodes__stock_issue_line__mutation__setDimension__parameter__stockIssueLine": "Ligne de sortie de stock", "@sage/xtrem-stock/nodes__stock_issue_line__mutation__setDimension__parameter__storedAttributes": "Attributs stockés", "@sage/xtrem-stock/nodes__stock_issue_line__mutation__setDimension__parameter__storedDimensions": "Sections stockées", "@sage/xtrem-stock/nodes__stock_issue_line__node_name": "Ligne de sortie de stock", "@sage/xtrem-stock/nodes__stock_issue_line__property__analyticalData": "Données analytiques", "@sage/xtrem-stock/nodes__stock_issue_line__property__computedAttributes": "Attributs calculés", "@sage/xtrem-stock/nodes__stock_issue_line__property__displayStatus": "Statut d'affichage", "@sage/xtrem-stock/nodes__stock_issue_line__property__document": "Document", "@sage/xtrem-stock/nodes__stock_issue_line__property__documentId": "Code de document", "@sage/xtrem-stock/nodes__stock_issue_line__property__documentNumber": "Numéro de document", "@sage/xtrem-stock/nodes__stock_issue_line__property__item": "Article", "@sage/xtrem-stock/nodes__stock_issue_line__property__itemSite": "Article-site", "@sage/xtrem-stock/nodes__stock_issue_line__property__jsonStockDetails": "Détails de stock JSON", "@sage/xtrem-stock/nodes__stock_issue_line__property__location": "Emplacement", "@sage/xtrem-stock/nodes__stock_issue_line__property__lot": "Lot", "@sage/xtrem-stock/nodes__stock_issue_line__property__orderCost": "Coût de la commande", "@sage/xtrem-stock/nodes__stock_issue_line__property__quantityInStockUnit": "Quantité en unité de stock", "@sage/xtrem-stock/nodes__stock_issue_line__property__site": "Site", "@sage/xtrem-stock/nodes__stock_issue_line__property__stockDetails": "Détails de stock", "@sage/xtrem-stock/nodes__stock_issue_line__property__stockDetailStatus": "Statut détail stock", "@sage/xtrem-stock/nodes__stock_issue_line__property__stockMovements": "Mouvements de stock", "@sage/xtrem-stock/nodes__stock_issue_line__property__stockStatus": "Statut de stock", "@sage/xtrem-stock/nodes__stock_issue_line__property__stockTransactions": "Transaction de stock", "@sage/xtrem-stock/nodes__stock_issue_line__property__stockTransactionStatus": "Statut de transaction de stock", "@sage/xtrem-stock/nodes__stock_issue_line__property__stockUnit": "Unité de stock", "@sage/xtrem-stock/nodes__stock_issue_line__property__storedAttributes": "Attributs stockés", "@sage/xtrem-stock/nodes__stock_issue_line__property__storedDimensions": "Sections stockées", "@sage/xtrem-stock/nodes__stock_issue_line__property__totalCost": "Coût total", "@sage/xtrem-stock/nodes__stock_issue_line__property__valuedCost": "Coût valorisé", "@sage/xtrem-stock/nodes__stock_issue_post__stock_details_required": "<PERSON><PERSON> de<PERSON> renseigner les détails de stock pour toutes les lignes avant d'effectuer la comptabilisation.", "@sage/xtrem-stock/nodes__stock_receipt__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-stock/nodes__stock_receipt__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-stock/nodes__stock_receipt__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-stock/nodes__stock_receipt__asyncMutation__createTestStockReceipt": "Créer l'entrée de stock de test", "@sage/xtrem-stock/nodes__stock_receipt__asyncMutation__createTestStockReceipt__failed": "Échec de création de la réception de stock de test.", "@sage/xtrem-stock/nodes__stock_receipt__asyncMutation__createTestStockReceipt__parameter__data": "<PERSON><PERSON><PERSON>", "@sage/xtrem-stock/nodes__stock_receipt__deletion_forbidden": "Cette entrée de stock ne peut pas être supprimée.", "@sage/xtrem-stock/nodes__stock_receipt__mutation__postToStock": "Comptabiliser en stock", "@sage/xtrem-stock/nodes__stock_receipt__mutation__postToStock__failed": "Échec de la comptabilisation en stock.", "@sage/xtrem-stock/nodes__stock_receipt__mutation__postToStock__parameter__documentIds": "Codes documents", "@sage/xtrem-stock/nodes__stock_receipt__mutation__repost": "Recomptabiliser", "@sage/xtrem-stock/nodes__stock_receipt__mutation__repost__failed": "Échec de la comptabilisation.", "@sage/xtrem-stock/nodes__stock_receipt__mutation__repost__parameter__documentLines": "Lignes de documents", "@sage/xtrem-stock/nodes__stock_receipt__mutation__repost__parameter__stockReceipt": "Entrée de stock", "@sage/xtrem-stock/nodes__stock_receipt__mutation__resendNotificationForFinance": "Renvoyer notification pour la finance", "@sage/xtrem-stock/nodes__stock_receipt__mutation__resendNotificationForFinance__failed": "Échec du renvoi de la notification pour la finance.", "@sage/xtrem-stock/nodes__stock_receipt__mutation__resendNotificationForFinance__parameter__stockReceipt": "Entrée de stock", "@sage/xtrem-stock/nodes__stock_receipt__no_sequence_number": "Le numéro de compteur de l'entrée de stock ne peut pas être généré. Renseignez d'abord un compteur par défaut.", "@sage/xtrem-stock/nodes__stock_receipt__node_name": "Entrée de stock", "@sage/xtrem-stock/nodes__stock_receipt__property__description": "Description", "@sage/xtrem-stock/nodes__stock_receipt__property__displayStatus": "Statut d'affichage", "@sage/xtrem-stock/nodes__stock_receipt__property__documentDate": "Date document", "@sage/xtrem-stock/nodes__stock_receipt__property__effectiveDate": "Date effective", "@sage/xtrem-stock/nodes__stock_receipt__property__financeIntegrationStatus": "Statut intégration financière", "@sage/xtrem-stock/nodes__stock_receipt__property__financialSite": "Site financier", "@sage/xtrem-stock/nodes__stock_receipt__property__forceUpdateForStock": "Forcer la mise à jour pour le stock", "@sage/xtrem-stock/nodes__stock_receipt__property__isSetDimensionsMainListHidden": "Définir sections liste principale caché", "@sage/xtrem-stock/nodes__stock_receipt__property__lines": "<PERSON><PERSON><PERSON>", "@sage/xtrem-stock/nodes__stock_receipt__property__number": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-stock/nodes__stock_receipt__property__postingDetails": "Détails de comptabilisation", "@sage/xtrem-stock/nodes__stock_receipt__property__reasonCode": "Code du motif", "@sage/xtrem-stock/nodes__stock_receipt__property__status": "Statut", "@sage/xtrem-stock/nodes__stock_receipt__property__stockSite": "Site de stock", "@sage/xtrem-stock/nodes__stock_receipt__property__stockTransactionStatus": "Statut de transaction de stock", "@sage/xtrem-stock/nodes__stock_receipt__property__transactionCurrency": "Devise de transaction", "@sage/xtrem-stock/nodes__stock_receipt_line__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-stock/nodes__stock_receipt_line__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-stock/nodes__stock_receipt_line__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-stock/nodes__stock_receipt_line__mutation__setDimension": "Définir section", "@sage/xtrem-stock/nodes__stock_receipt_line__mutation__setDimension__failed": "Échec de paramétrage de section.", "@sage/xtrem-stock/nodes__stock_receipt_line__mutation__setDimension__parameter__stockReceiptLine": "Ligne d'entrée de stock", "@sage/xtrem-stock/nodes__stock_receipt_line__mutation__setDimension__parameter__storedAttributes": "Attributs stockés", "@sage/xtrem-stock/nodes__stock_receipt_line__mutation__setDimension__parameter__storedDimensions": "Sections stockées", "@sage/xtrem-stock/nodes__stock_receipt_line__node_name": "Ligne d'entrée de stock", "@sage/xtrem-stock/nodes__stock_receipt_line__property__analyticalData": "Données analytiques", "@sage/xtrem-stock/nodes__stock_receipt_line__property__computedAttributes": "Attributs calculés", "@sage/xtrem-stock/nodes__stock_receipt_line__property__displayStatus": "Statut d'affichage", "@sage/xtrem-stock/nodes__stock_receipt_line__property__document": "Document", "@sage/xtrem-stock/nodes__stock_receipt_line__property__documentId": "Code de document", "@sage/xtrem-stock/nodes__stock_receipt_line__property__documentNumber": "Numéro de document", "@sage/xtrem-stock/nodes__stock_receipt_line__property__existingLot": "Lot existant", "@sage/xtrem-stock/nodes__stock_receipt_line__property__item": "Article", "@sage/xtrem-stock/nodes__stock_receipt_line__property__itemSite": "Article-site", "@sage/xtrem-stock/nodes__stock_receipt_line__property__jsonStockDetails": "Détails de stock JSON", "@sage/xtrem-stock/nodes__stock_receipt_line__property__location": "Emplacement", "@sage/xtrem-stock/nodes__stock_receipt_line__property__lotCreateData": "Données de création de lots", "@sage/xtrem-stock/nodes__stock_receipt_line__property__orderCost": "Coût de la commande", "@sage/xtrem-stock/nodes__stock_receipt_line__property__quantityInStockUnit": "Quantité en unité de stock", "@sage/xtrem-stock/nodes__stock_receipt_line__property__site": "Site", "@sage/xtrem-stock/nodes__stock_receipt_line__property__stockDetails": "Détails de stock", "@sage/xtrem-stock/nodes__stock_receipt_line__property__stockDetailStatus": "Statut détail stock", "@sage/xtrem-stock/nodes__stock_receipt_line__property__stockMovements": "Mouvements de stock", "@sage/xtrem-stock/nodes__stock_receipt_line__property__stockStatus": "Statut de stock", "@sage/xtrem-stock/nodes__stock_receipt_line__property__stockTransactions": "Transaction de stock", "@sage/xtrem-stock/nodes__stock_receipt_line__property__stockTransactionStatus": "Statut de transaction de stock", "@sage/xtrem-stock/nodes__stock_receipt_line__property__stockUnit": "Unité de stock", "@sage/xtrem-stock/nodes__stock_receipt_line__property__storedAttributes": "Attributs stockés", "@sage/xtrem-stock/nodes__stock_receipt_line__property__storedDimensions": "Sections stockées", "@sage/xtrem-stock/nodes__stock_receipt_line__property__totalCost": "Coût total", "@sage/xtrem-stock/nodes__stock_receipt_line__property__valuedCost": "Coût valorisé", "@sage/xtrem-stock/nodes__stock_receipt_node_post__stock_details_required": "<PERSON><PERSON> de<PERSON> renseigner les détails de stock pour toutes les lignes avant d'effectuer la comptabilisation.", "@sage/xtrem-stock/nodes__stock_reorder_calculation_input_set__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-stock/nodes__stock_reorder_calculation_input_set__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-stock/nodes__stock_reorder_calculation_input_set__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-stock/nodes__stock_reorder_calculation_input_set__asyncMutation__reorderCalculation": "Calcul de réapprovisionnement", "@sage/xtrem-stock/nodes__stock_reorder_calculation_input_set__asyncMutation__reorderCalculation__failed": "Échec du calcul de réapprovisionnement.", "@sage/xtrem-stock/nodes__stock_reorder_calculation_input_set__asyncMutation__reorderCalculation__parameter__userId": "Code utilisateur", "@sage/xtrem-stock/nodes__stock_reorder_calculation_input_set__node_name": "Jeu de données de calcul de réapprovisionnement de stock", "@sage/xtrem-stock/nodes__stock_reorder_calculation_input_set__notification_calculation_failed": "Échec du calcul de réapprovisionnement de stock.", "@sage/xtrem-stock/nodes__stock_reorder_calculation_input_set__notification_calculation_finished": "Calcul de réapprovisionnement de stock terminé.", "@sage/xtrem-stock/nodes__stock_reorder_calculation_input_set__property__company": "Société", "@sage/xtrem-stock/nodes__stock_reorder_calculation_input_set__property__endDate": "Date de fin", "@sage/xtrem-stock/nodes__stock_reorder_calculation_input_set__property__fromItem": "Article début", "@sage/xtrem-stock/nodes__stock_reorder_calculation_input_set__property__lines": "<PERSON><PERSON><PERSON>", "@sage/xtrem-stock/nodes__stock_reorder_calculation_input_set__property__reorderType": "Type de réapprovisionnement", "@sage/xtrem-stock/nodes__stock_reorder_calculation_input_set__property__sites": "Sites", "@sage/xtrem-stock/nodes__stock_reorder_calculation_input_set__property__status": "Statut", "@sage/xtrem-stock/nodes__stock_reorder_calculation_input_set__property__toItem": "Article fin", "@sage/xtrem-stock/nodes__stock_reorder_calculation_input_set__property__user": "Utilisa<PERSON>ur", "@sage/xtrem-stock/nodes__stock_reorder_calculation_result_line__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-stock/nodes__stock_reorder_calculation_result_line__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-stock/nodes__stock_reorder_calculation_result_line__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-stock/nodes__stock_reorder_calculation_result_line__node_name": "Ligne de résultat de calcul de réapprovisionnement de stock", "@sage/xtrem-stock/nodes__stock_reorder_calculation_result_line__property__company": "Société", "@sage/xtrem-stock/nodes__stock_reorder_calculation_result_line__property__currency": "<PERSON><PERSON>", "@sage/xtrem-stock/nodes__stock_reorder_calculation_result_line__property__inputSet": "<PERSON><PERSON> <PERSON> don<PERSON>", "@sage/xtrem-stock/nodes__stock_reorder_calculation_result_line__property__itemSite": "Article-site", "@sage/xtrem-stock/nodes__stock_reorder_calculation_result_line__property__orderDate": "Date d'ordre", "@sage/xtrem-stock/nodes__stock_reorder_calculation_result_line__property__purchaseUnit": "<PERSON><PERSON>", "@sage/xtrem-stock/nodes__stock_reorder_calculation_result_line__property__quantity": "Quantité", "@sage/xtrem-stock/nodes__stock_reorder_calculation_result_line__property__reorderType": "Type de réapprovisionnement", "@sage/xtrem-stock/nodes__stock_reorder_calculation_result_line__property__stockUnit": "Unité de stock", "@sage/xtrem-stock/nodes__stock_reorder_calculation_result_line__property__supplier": "Fournisseur", "@sage/xtrem-stock/nodes__stock_update_listener__quantity_of_stock_record_to_be_counted_has_changed": "La quantité de l'enregistrement de stock à compter a été modifiée depuis le début de l'inventaire.\n\n{{stock_identifiers}}", "@sage/xtrem-stock/nodes__stock_update_listener__stock_record_identification": "Article : {{item}}\n\nStatut : {{status}}\n\nLot : {{lot}}\n\nEmplacement : {{location}}\n\nQuantité actuelle en stock : {{quantity}}", "@sage/xtrem-stock/nodes__stock_valuation_input_set__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-stock/nodes__stock_valuation_input_set__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-stock/nodes__stock_valuation_input_set__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-stock/nodes__stock_valuation_input_set__asyncMutation__stockValuation": "Valoriser stock", "@sage/xtrem-stock/nodes__stock_valuation_input_set__asyncMutation__stockValuation__failed": "Échec de la valorisation de stock.", "@sage/xtrem-stock/nodes__stock_valuation_input_set__asyncMutation__stockValuation__parameter__userId": "Code utilisateur", "@sage/xtrem-stock/nodes__stock_valuation_input_set__node_name": "Jeu de données de la valorisation de stock", "@sage/xtrem-stock/nodes__stock_valuation_input_set__notification_valuation_failed": "Échec de la valorisation de stock.", "@sage/xtrem-stock/nodes__stock_valuation_input_set__notification_valuation_finished": "Valorisation de stock finalisée.", "@sage/xtrem-stock/nodes__stock_valuation_input_set__property__commodityCode": "Code marchandise", "@sage/xtrem-stock/nodes__stock_valuation_input_set__property__company": "Société", "@sage/xtrem-stock/nodes__stock_valuation_input_set__property__date": "Date", "@sage/xtrem-stock/nodes__stock_valuation_input_set__property__displayZeroValues": "Afficher valeurs nulles", "@sage/xtrem-stock/nodes__stock_valuation_input_set__property__fromItem": "Article début", "@sage/xtrem-stock/nodes__stock_valuation_input_set__property__itemCategory": "Catégorie d'article", "@sage/xtrem-stock/nodes__stock_valuation_input_set__property__lines": "<PERSON><PERSON><PERSON>", "@sage/xtrem-stock/nodes__stock_valuation_input_set__property__postingClass": "Classe de comptabilisation", "@sage/xtrem-stock/nodes__stock_valuation_input_set__property__sites": "Sites", "@sage/xtrem-stock/nodes__stock_valuation_input_set__property__status": "Statut", "@sage/xtrem-stock/nodes__stock_valuation_input_set__property__toItem": "Article fin", "@sage/xtrem-stock/nodes__stock_valuation_input_set__property__user": "Utilisa<PERSON>ur", "@sage/xtrem-stock/nodes__stock_valuation_input_set__property__valuationMethod": "Méthode de valorisation", "@sage/xtrem-stock/nodes__stock_valuation_input_set_to_site__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-stock/nodes__stock_valuation_input_set_to_site__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-stock/nodes__stock_valuation_input_set_to_site__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-stock/nodes__stock_valuation_input_set_to_site__node_name": "Données de valorisation de stock définies sur site", "@sage/xtrem-stock/nodes__stock_valuation_input_set_to_site__property__inputSet": "<PERSON><PERSON> <PERSON> don<PERSON>", "@sage/xtrem-stock/nodes__stock_valuation_input_set_to_site__property__site": "Site", "@sage/xtrem-stock/nodes__stock_valuation_result_line__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-stock/nodes__stock_valuation_result_line__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-stock/nodes__stock_valuation_result_line__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-stock/nodes__stock_valuation_result_line__node_name": "Ligne de résultat de la valorisation de stock", "@sage/xtrem-stock/nodes__stock_valuation_result_line__property__commodityCode": "Code marchandise", "@sage/xtrem-stock/nodes__stock_valuation_result_line__property__company": "Société", "@sage/xtrem-stock/nodes__stock_valuation_result_line__property__costType": "Nature de dépense", "@sage/xtrem-stock/nodes__stock_valuation_result_line__property__currency": "<PERSON><PERSON>", "@sage/xtrem-stock/nodes__stock_valuation_result_line__property__inputSet": "<PERSON><PERSON> <PERSON> don<PERSON>", "@sage/xtrem-stock/nodes__stock_valuation_result_line__property__item": "Article", "@sage/xtrem-stock/nodes__stock_valuation_result_line__property__itemCategory": "Catégorie d'article", "@sage/xtrem-stock/nodes__stock_valuation_result_line__property__postingClass": "Classe de comptabilisation", "@sage/xtrem-stock/nodes__stock_valuation_result_line__property__quantity": "Quantité", "@sage/xtrem-stock/nodes__stock_valuation_result_line__property__site": "Site", "@sage/xtrem-stock/nodes__stock_valuation_result_line__property__stockUnit": "Unité de stock", "@sage/xtrem-stock/nodes__stock_valuation_result_line__property__stockValue": "Valeur du stock", "@sage/xtrem-stock/nodes__stock_valuation_result_line__property__unitCost": "Coût unitaire", "@sage/xtrem-stock/nodes__stock_valuation_result_line__property__valuationDate": "Date de valorisation", "@sage/xtrem-stock/nodes__stock_value_change__amount_newAmount_average_cost": "Ligne 1 : le montant actuel et le nouveau montant ne peuvent pas être identiques. Renseignez une valeur différente pour le nouveau montant.", "@sage/xtrem-stock/nodes__stock_value_change__amount_newAmount_fifo_cost": "Ligne {{lineNumber}} : le montant actuel et le nouveau montant ne peuvent pas être identiques. Renseignez une valeur différente pour le nouveau montant.", "@sage/xtrem-stock/nodes__stock_value_change__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-stock/nodes__stock_value_change__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-stock/nodes__stock_value_change__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-stock/nodes__stock_value_change__average_cost_quantity_cost_amount_has_changed": "Le coût moyen correspondant à la ligne 1 a changé. Re<PERSON><PERSON>ez la ligne de changement de la valeur de stock.", "@sage/xtrem-stock/nodes__stock_value_change__cant_repost_stock_value_change_when_status_is_not_failed": "Vous pouvez uniquement recomptabiliser un changement de valeur de stock si son statut est 'Échec'.", "@sage/xtrem-stock/nodes__stock_value_change__company_has_no_stock_posting": "Aucun stock n'a été comptabilisé pour cette société.", "@sage/xtrem-stock/nodes__stock_value_change__document_was_posted": "Le changement de valeur de stock a été comptabilisé.", "@sage/xtrem-stock/nodes__stock_value_change__fifo_cost_quantity_cost_amount_has_changed": "L'enregistrement de la pile FIFO correspondant à la ligne avec la date effective {{date}} et la séquence {{sequence}} a changé. Recréez la ligne de changement de la valeur de stock.", "@sage/xtrem-stock/nodes__stock_value_change__lines_mandatory": "Le changement de valeur de stock doit contenir au moins une ligne.", "@sage/xtrem-stock/nodes__stock_value_change__mutation__financeIntegrationCheck": "Contrôle intégration finance", "@sage/xtrem-stock/nodes__stock_value_change__mutation__financeIntegrationCheck__failed": "Échec de contrôle d'intégration finance.", "@sage/xtrem-stock/nodes__stock_value_change__mutation__financeIntegrationCheck__parameter__valueChange": "Changement de valeur", "@sage/xtrem-stock/nodes__stock_value_change__mutation__postToStock": "Comptabiliser en stock", "@sage/xtrem-stock/nodes__stock_value_change__mutation__postToStock__failed": "Échec de la comptabilisation en stock.", "@sage/xtrem-stock/nodes__stock_value_change__mutation__postToStock__parameter__documentIds": "Codes documents", "@sage/xtrem-stock/nodes__stock_value_change__mutation__repost": "Recomptabiliser", "@sage/xtrem-stock/nodes__stock_value_change__mutation__repost__failed": "Échec de la comptabilisation.", "@sage/xtrem-stock/nodes__stock_value_change__mutation__repost__parameter__documentLines": "Lignes de documents", "@sage/xtrem-stock/nodes__stock_value_change__mutation__repost__parameter__stockValueChange": "Changement de la valeur du stock", "@sage/xtrem-stock/nodes__stock_value_change__mutation__resendNotificationForFinance": "Renvoyer notification pour la finance", "@sage/xtrem-stock/nodes__stock_value_change__mutation__resendNotificationForFinance__failed": "Échec du renvoi de la notification pour la finance.", "@sage/xtrem-stock/nodes__stock_value_change__mutation__resendNotificationForFinance__parameter__stockValueChange": "Changement de la valeur du stock", "@sage/xtrem-stock/nodes__stock_value_change__node_name": "Changement de la valeur du stock", "@sage/xtrem-stock/nodes__stock_value_change__property__description": "Description", "@sage/xtrem-stock/nodes__stock_value_change__property__documentDate": "Date de document", "@sage/xtrem-stock/nodes__stock_value_change__property__effectiveDate": "Date effective", "@sage/xtrem-stock/nodes__stock_value_change__property__financeIntegrationStatus": "Statut d'intégration financière", "@sage/xtrem-stock/nodes__stock_value_change__property__financialSite": "Site financier", "@sage/xtrem-stock/nodes__stock_value_change__property__forceUpdateForStock": "Forcer la mise à jour pour le stock", "@sage/xtrem-stock/nodes__stock_value_change__property__item": "Article", "@sage/xtrem-stock/nodes__stock_value_change__property__itemSite": "Article-site", "@sage/xtrem-stock/nodes__stock_value_change__property__lines": "<PERSON><PERSON><PERSON>", "@sage/xtrem-stock/nodes__stock_value_change__property__number": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-stock/nodes__stock_value_change__property__postedDate": "Date de comptabilisation", "@sage/xtrem-stock/nodes__stock_value_change__property__postingDetails": "Détails de comptabilisation", "@sage/xtrem-stock/nodes__stock_value_change__property__site": "Site", "@sage/xtrem-stock/nodes__stock_value_change__property__stockTransactionStatus": "Statut de transaction de stock", "@sage/xtrem-stock/nodes__stock_value_change__property__totalQuantityInStock": "Quantité totale en stock", "@sage/xtrem-stock/nodes__stock_value_change__property__totalValueOfStock": "Valeur totale du stock", "@sage/xtrem-stock/nodes__stock_value_change__property__transactionCurrency": "Devise de transaction", "@sage/xtrem-stock/nodes__stock_value_change__property__unitCost": "Coût unitaire", "@sage/xtrem-stock/nodes__stock_value_change__property__valuationMethod": "Méthode de valorisation", "@sage/xtrem-stock/nodes__stock_value_change_line__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-stock/nodes__stock_value_change_line__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-stock/nodes__stock_value_change_line__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-stock/nodes__stock_value_change_line__check_amount_stock_detail": "Le montant des détails de stock {{totalStockDetails}} n'est pas égal à l'écart du changement de valeur sur la ligne de document {{varianceLine}}", "@sage/xtrem-stock/nodes__stock_value_change_line__node_name": "Ligne de changement de la valeur du stock", "@sage/xtrem-stock/nodes__stock_value_change_line__property__amount": "<PERSON><PERSON>", "@sage/xtrem-stock/nodes__stock_value_change_line__property__analyticalData": "Données analytiques", "@sage/xtrem-stock/nodes__stock_value_change_line__property__computedAttributes": "Attributs calculés", "@sage/xtrem-stock/nodes__stock_value_change_line__property__currency": "<PERSON><PERSON>", "@sage/xtrem-stock/nodes__stock_value_change_line__property__document": "Document", "@sage/xtrem-stock/nodes__stock_value_change_line__property__documentId": "Code de document", "@sage/xtrem-stock/nodes__stock_value_change_line__property__documentNumber": "Numéro de document", "@sage/xtrem-stock/nodes__stock_value_change_line__property__effectiveDate": "Date effective", "@sage/xtrem-stock/nodes__stock_value_change_line__property__fifoCost": "Prix FIFO", "@sage/xtrem-stock/nodes__stock_value_change_line__property__item": "Article", "@sage/xtrem-stock/nodes__stock_value_change_line__property__jsonStockDetails": "Détails de stock JSON", "@sage/xtrem-stock/nodes__stock_value_change_line__property__newAmount": "Nouveau montant", "@sage/xtrem-stock/nodes__stock_value_change_line__property__newUnitCost": "Nouveau coût unitaire", "@sage/xtrem-stock/nodes__stock_value_change_line__property__quantity": "Quantité", "@sage/xtrem-stock/nodes__stock_value_change_line__property__quantityInStockUnit": "Quantité en unité de stock", "@sage/xtrem-stock/nodes__stock_value_change_line__property__site": "Site", "@sage/xtrem-stock/nodes__stock_value_change_line__property__stockDetails": "Détails de stock", "@sage/xtrem-stock/nodes__stock_value_change_line__property__stockMovements": "Mouvements de stock", "@sage/xtrem-stock/nodes__stock_value_change_line__property__stockTransactions": "Transactions de stock", "@sage/xtrem-stock/nodes__stock_value_change_line__property__stockTransactionStatus": "Statut de transaction de stock", "@sage/xtrem-stock/nodes__stock_value_change_line__property__stockUnit": "Unité de stock", "@sage/xtrem-stock/nodes__stock_value_change_line__property__storedAttributes": "Attributs stockés", "@sage/xtrem-stock/nodes__stock_value_change_line__property__storedDimensions": "Sections stockées", "@sage/xtrem-stock/nodes__stock_value_change_line__property__unitCost": "Coût unitaire", "@sage/xtrem-stock/nodes__stock_value_correction__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-stock/nodes__stock_value_correction__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-stock/nodes__stock_value_correction__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-stock/nodes__stock_value_correction__mutation__postToStock": "Comptabiliser en stock", "@sage/xtrem-stock/nodes__stock_value_correction__mutation__postToStock__failed": "Échec de la comptabilisation en stock.", "@sage/xtrem-stock/nodes__stock_value_correction__mutation__postToStock__parameter__documentIds": "Codes documents", "@sage/xtrem-stock/nodes__stock_value_correction__node_name": "Correction de la valeur de stock", "@sage/xtrem-stock/nodes__stock_value_correction__property__description": "Description", "@sage/xtrem-stock/nodes__stock_value_correction__property__documentDate": "Date document", "@sage/xtrem-stock/nodes__stock_value_correction__property__effectiveDate": "Date effective", "@sage/xtrem-stock/nodes__stock_value_correction__property__financialSite": "Site financier", "@sage/xtrem-stock/nodes__stock_value_correction__property__lines": "<PERSON><PERSON><PERSON>", "@sage/xtrem-stock/nodes__stock_value_correction__property__number": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-stock/nodes__stock_value_correction__property__reasonCode": "Code motif", "@sage/xtrem-stock/nodes__stock_value_correction__property__status": "Statut", "@sage/xtrem-stock/nodes__stock_value_correction__property__stockSite": "Site de stock", "@sage/xtrem-stock/nodes__stock_value_correction__property__stockTransactionStatus": "Statut de transaction de stock", "@sage/xtrem-stock/nodes__stock_value_correction__property__transactionCurrency": "Devise de transaction", "@sage/xtrem-stock/nodes__stock_value_correction_line__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-stock/nodes__stock_value_correction_line__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-stock/nodes__stock_value_correction_line__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-stock/nodes__stock_value_correction_line__invalid_document_line": "Le statut de transaction de stock de la ligne de document corrigée doit être Réalisé.", "@sage/xtrem-stock/nodes__stock_value_correction_line__no_corrected_line": "<PERSON><PERSON> <PERSON><PERSON> spécifier la ligne de document en correction.", "@sage/xtrem-stock/nodes__stock_value_correction_line__node_name": "Ligne de correction de la valeur de stock", "@sage/xtrem-stock/nodes__stock_value_correction_line__property__analyticalData": "Données analytiques", "@sage/xtrem-stock/nodes__stock_value_correction_line__property__computedAttributes": "Attributs calculés", "@sage/xtrem-stock/nodes__stock_value_correction_line__property__correctedDocumentLine": "Ligne de document corrigé", "@sage/xtrem-stock/nodes__stock_value_correction_line__property__correctedDocumentType": "Type de document corrigé", "@sage/xtrem-stock/nodes__stock_value_correction_line__property__document": "Document", "@sage/xtrem-stock/nodes__stock_value_correction_line__property__documentId": "Code de document", "@sage/xtrem-stock/nodes__stock_value_correction_line__property__documentNumber": "Numéro de document", "@sage/xtrem-stock/nodes__stock_value_correction_line__property__item": "Article", "@sage/xtrem-stock/nodes__stock_value_correction_line__property__itemSite": "Article-site", "@sage/xtrem-stock/nodes__stock_value_correction_line__property__newUnitCost": "Nouveau coût unitaire", "@sage/xtrem-stock/nodes__stock_value_correction_line__property__orderCost": "Coût de l'ordre", "@sage/xtrem-stock/nodes__stock_value_correction_line__property__site": "Site", "@sage/xtrem-stock/nodes__stock_value_correction_line__property__stockDetails": "Détails de stock", "@sage/xtrem-stock/nodes__stock_value_correction_line__property__stockMovements": "Mouvements de stock", "@sage/xtrem-stock/nodes__stock_value_correction_line__property__stockTransactions": "Transaction de stock", "@sage/xtrem-stock/nodes__stock_value_correction_line__property__stockTransactionStatus": "Statut de transaction de stock", "@sage/xtrem-stock/nodes__stock_value_correction_line__property__storedAttributes": "Attributs stockés", "@sage/xtrem-stock/nodes__stock_value_correction_line__property__storedDimensions": "Sections stockées", "@sage/xtrem-stock/nodes__stock_value_correction_line__property__unitCost": "Coût unitaire", "@sage/xtrem-stock/nodes__stock_value_correction_line__property__valuedCost": "Coût valorisé", "@sage/xtrem-stock/nodes__stock-adjustment__cant_post_stock_adjustment_when_stock_detail_status_is_required": "<PERSON><PERSON> de<PERSON> renseigner les détails de stock pour toutes les lignes avant d'effectuer la comptabilisation.", "@sage/xtrem-stock/nodes__stock-adjustment__cant_repost_stock_adjustment_when_status_is_not_failed": "Vous pouvez uniquement recomptabiliser une régularisation de stock si son statut est 'Échec' ou 'Non enregistrée'.", "@sage/xtrem-stock/nodes__stock-adjustment__company_has_no_stock_posting": "Aucun stock n'a pas été comptabilisé pour cette société.", "@sage/xtrem-stock/nodes__stock-adjustment__document_was_posted": "La régularisation de stock a été comptabilisée.", "@sage/xtrem-stock/nodes__stock-issue__cant_repost_stock_issue_when_status_is_not_failed": "Vous pouvez uniquement recomptabiliser une sortie de stock si son statut est 'Échec' ou 'Non enregistrée'.", "@sage/xtrem-stock/nodes__stock-issue__company_has_no_stock_posting": "La société courante n'a pas de comptabilisation de stock.", "@sage/xtrem-stock/nodes__stock-issue__document_was_posted": "L'entrée de stock a été comptabilisée.", "@sage/xtrem-stock/nodes__stock-receipt__cant_repost_stock_receipt_when_status_is_not_failed": "Vous pouvez uniquement recomptabiliser une entrée de stock si son statut est 'Échec' ou 'Non enregistrée'", "@sage/xtrem-stock/nodes__stock-receipt__company_has_no_stock_posting": "La société courante n'a pas de comptabilisation de stock.", "@sage/xtrem-stock/nodes__stock-receipt__document_was_posted": "L'entrée de stock a été comptabilisée.", "@sage/xtrem-stock/package__name": "Stock", "@sage/xtrem-stock/page__stock_count__add_new_line": "<PERSON><PERSON><PERSON> ligne", "@sage/xtrem-stock/page__stock_count__forbid_addition_serialized_managed_item": "Une ligne de stock pour un article sérialisé ne peut pas être ajouté à un inventaire existant.", "@sage/xtrem-stock/page-extensions__bill_of_material_component_panel_extension__availableQuantityInStockUnit____title": "Quantité de stock disponible", "@sage/xtrem-stock/page-extensions__bill_of_material_component_panel_extension__stockOnHand____title": "Stock physique", "@sage/xtrem-stock/page-extensions__bill_of_material_component_panel_extension__stockShortageInStockUnit____title": "Rupture de stock", "@sage/xtrem-stock/page-extensions__bill_of_material_component_panel_extension__stockShortageStatus____title": "Statut de rupture de stock", "@sage/xtrem-stock/page-extensions__bill_of_material_extension__components____columns__title__availableQuantityInStockUnit": "Quantité de stock disponible", "@sage/xtrem-stock/page-extensions__bill_of_material_extension__components____columns__title__stockShortageInStockUnit": "Rupture de stock", "@sage/xtrem-stock/page-extensions__create_test_data_extension__isStockQuantityFixed____title": "Quantité de stock fixe pour tous les articles", "@sage/xtrem-stock/page-extensions__create_test_data_extension__item____columns__title__description": "Description", "@sage/xtrem-stock/page-extensions__create_test_data_extension__item____columns__title__id": "Code article", "@sage/xtrem-stock/page-extensions__create_test_data_extension__item____columns__title__name": "Nom", "@sage/xtrem-stock/page-extensions__create_test_data_extension__item____lookupDialogTitle": "Sélectionner l'article", "@sage/xtrem-stock/page-extensions__create_test_data_extension__item____title": "Article de base", "@sage/xtrem-stock/page-extensions__create_test_data_extension__itemBlock____title": "Article", "@sage/xtrem-stock/page-extensions__create_test_data_extension__itemQuantity____title": "Quantité d'articles", "@sage/xtrem-stock/page-extensions__create_test_data_extension__maxStockQuantity____title": "Quantité de stock maximum", "@sage/xtrem-stock/page-extensions__item_site_extension__countingInProgress____title": "Comptage en cours", "@sage/xtrem-stock/page-extensions__item_site_extension__countingInProgressMention____title": "Comptage en cours", "@sage/xtrem-stock/page-extensions__item_site_extension__lastCountDate____title": "Date dernier inventaire", "@sage/xtrem-stock/pages__allocation_result____navigationPanel__listItem__status__title": "Statut", "@sage/xtrem-stock/pages__allocation_result____navigationPanel__listItem__stockSiteName__title": "Site de stock", "@sage/xtrem-stock/pages__allocation_result____navigationPanel__listItem__title__title": "Description", "@sage/xtrem-stock/pages__allocation_result____navigationPanel__listItem__titleRight__title": "Date / Heure", "@sage/xtrem-stock/pages__allocation_result____objectTypePlural": "Résultat d'allocation", "@sage/xtrem-stock/pages__allocation_result____objectTypeSingular": "Résultat d'allocation", "@sage/xtrem-stock/pages__allocation_result____title": "Résultat d'allocation", "@sage/xtrem-stock/pages__allocation_result__allocationResultLineCount____title": "Nombre de lignes", "@sage/xtrem-stock/pages__allocation_result__creationDatetime____title": "Date / Heure", "@sage/xtrem-stock/pages__allocation_result__description____title": "Description", "@sage/xtrem-stock/pages__allocation_result__documentType____title": "Type de document", "@sage/xtrem-stock/pages__allocation_result__fromItemText____title": "Article début", "@sage/xtrem-stock/pages__allocation_result__fromOrderNumber____title": "Numéro ordre de fabrication début", "@sage/xtrem-stock/pages__allocation_result__lines____columns__title": "Code article", "@sage/xtrem-stock/pages__allocation_result__lines____columns__title__2": "N° doc", "@sage/xtrem-stock/pages__allocation_result__lines____columns__title__documentType": "Type doc", "@sage/xtrem-stock/pages__allocation_result__lines____columns__title__item__stockUnit__symbol": "Unité stock", "@sage/xtrem-stock/pages__allocation_result__lines____columns__title__quantityProcessed": "<PERSON><PERSON>", "@sage/xtrem-stock/pages__allocation_result__lines____columns__title__quantityToProcess": "<PERSON><PERSON> à traiter", "@sage/xtrem-stock/pages__allocation_result__lines____columns__title__shortageQuantity": "Q<PERSON> rupture", "@sage/xtrem-stock/pages__allocation_result__lines____columns__title__status": "Statut", "@sage/xtrem-stock/pages__allocation_result__lines____columns__title__stockSite__id": "Site stock", "@sage/xtrem-stock/pages__allocation_result__lines____title": "Détails", "@sage/xtrem-stock/pages__allocation_result__processId____title": "Code traitement", "@sage/xtrem-stock/pages__allocation_result__processType____title": "Type de traitement", "@sage/xtrem-stock/pages__allocation_result__requestType____title": "Type de demande", "@sage/xtrem-stock/pages__allocation_result__status____title": "Statut", "@sage/xtrem-stock/pages__allocation_result__stockSiteName____title": "Site de stock", "@sage/xtrem-stock/pages__allocation_result__toItemText____title": "Article fin", "@sage/xtrem-stock/pages__allocation_result__toOrderNumber____title": "Numéro ordre de fabrication fin", "@sage/xtrem-stock/pages__bill_of_material_component_panel_extension__display_shortage_status_false": "Stock disponible", "@sage/xtrem-stock/pages__bill_of_material_component_panel_extension__display_shortage_status_true": "Rupture de stock", "@sage/xtrem-stock/pages__cost_roll_up_sub_assembly_dialog____title": "Sous-ensembles", "@sage/xtrem-stock/pages__cost_roll_up_sub_assembly_dialog____update_title": "Article : {{itemName}}", "@sage/xtrem-stock/pages__cost_roll_up_sub_assembly_dialog__mainSection____title": "Sous-ensembles", "@sage/xtrem-stock/pages__cost_roll_up_sub_assembly_dialog__ok____title": "OK", "@sage/xtrem-stock/pages__cost_roll_up_sub_assembly_dialog__subAssemblies____columns__title__calculationQuantity": "Quantité de calcul", "@sage/xtrem-stock/pages__cost_roll_up_sub_assembly_dialog__subAssemblies____columns__title__componentNumber": "N° composant", "@sage/xtrem-stock/pages__cost_roll_up_sub_assembly_dialog__subAssemblies____columns__title__currentLaborCost": "Coût main d'oeuvre actuel", "@sage/xtrem-stock/pages__cost_roll_up_sub_assembly_dialog__subAssemblies____columns__title__currentMachineCost": "Coût machine actuel", "@sage/xtrem-stock/pages__cost_roll_up_sub_assembly_dialog__subAssemblies____columns__title__currentMaterialCost": "Coût matière actuel", "@sage/xtrem-stock/pages__cost_roll_up_sub_assembly_dialog__subAssemblies____columns__title__currentToolCost": "Coût outil actuel", "@sage/xtrem-stock/pages__cost_roll_up_sub_assembly_dialog__subAssemblies____columns__title__currentTotalCost": "Coût total actuel", "@sage/xtrem-stock/pages__cost_roll_up_sub_assembly_dialog__subAssemblies____columns__title__item__id": "Code article", "@sage/xtrem-stock/pages__cost_roll_up_sub_assembly_dialog__subAssemblies____columns__title__item__name": "Nom article", "@sage/xtrem-stock/pages__cost_roll_up_sub_assembly_dialog__subAssemblies____columns__title__laborCost": "Coût main d'oeuvre", "@sage/xtrem-stock/pages__cost_roll_up_sub_assembly_dialog__subAssemblies____columns__title__machineCost": "Coût machine", "@sage/xtrem-stock/pages__cost_roll_up_sub_assembly_dialog__subAssemblies____columns__title__materialCost": "Co<PERSON>t matière", "@sage/xtrem-stock/pages__cost_roll_up_sub_assembly_dialog__subAssemblies____columns__title__toolCost": "Coût outil", "@sage/xtrem-stock/pages__cost_roll_up_sub_assembly_dialog__subAssemblies____columns__title__totalCost": "Coût total", "@sage/xtrem-stock/pages__cost_roll_up_sub_assembly_dialog__subAssemblies____title": "Sous-ensembles", "@sage/xtrem-stock/pages__create-test-data__create_items_validation_item": "Sélectionner un article", "@sage/xtrem-stock/pages__create-test-data__create_items_validation_max_stock_quantity": "Renseignez une quantité supérieure à 0.", "@sage/xtrem-stock/pages__create-test-data__create_items_validation_quantity": "Renseignez une quantité supérieure à 0.", "@sage/xtrem-stock/pages__fifo_valuation_tier_table_panel____title": "Ajouter à partir de la pile FIFO", "@sage/xtrem-stock/pages__fifo_valuation_tier_table_panel__cancel____title": "Annuler", "@sage/xtrem-stock/pages__fifo_valuation_tier_table_panel__confirm____title": "Ajouter", "@sage/xtrem-stock/pages__fifo_valuation_tier_table_panel__fifoValuationTierLines____columns__title__amount": "<PERSON><PERSON>", "@sage/xtrem-stock/pages__fifo_valuation_tier_table_panel__fifoValuationTierLines____columns__title__effectiveDate": "Date effective", "@sage/xtrem-stock/pages__fifo_valuation_tier_table_panel__fifoValuationTierLines____columns__title__nonAbsorbedAmount": "Montant non-absorbé", "@sage/xtrem-stock/pages__fifo_valuation_tier_table_panel__fifoValuationTierLines____columns__title__receiptDocumentLine__documentNumber": "N° doc", "@sage/xtrem-stock/pages__fifo_valuation_tier_table_panel__fifoValuationTierLines____columns__title__receiptQuantity": "<PERSON><PERSON> r<PERSON>", "@sage/xtrem-stock/pages__fifo_valuation_tier_table_panel__fifoValuationTierLines____columns__title__remainingQuantity": "<PERSON><PERSON> restante", "@sage/xtrem-stock/pages__fifo_valuation_tier_table_panel__fifoValuationTierLines____columns__title__sequence": "Compteur", "@sage/xtrem-stock/pages__fifo_valuation_tier_table_panel__fifoValuationTierLines____columns__title__unitCost": "Coût unitaire", "@sage/xtrem-stock/pages__item_site_cost__dimensions_button_text": "Sections", "@sage/xtrem-stock/pages__mrp_calculation____navigationPanel__bulkActions__title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__mrp_calculation____navigationPanel__listItem__line2Right__title": "Statut", "@sage/xtrem-stock/pages__mrp_calculation____navigationPanel__listItem__numberOfWeeks__title": "Période en semaines", "@sage/xtrem-stock/pages__mrp_calculation____navigationPanel__listItem__titleRight__title": "Date de calcul", "@sage/xtrem-stock/pages__mrp_calculation____navigationPanel__listItem__user__title": "Calcul demandé par", "@sage/xtrem-stock/pages__mrp_calculation____objectTypePlural": "Résultats du calcul de CBN", "@sage/xtrem-stock/pages__mrp_calculation____objectTypeSingular": "Résultat du calcul de CBN", "@sage/xtrem-stock/pages__mrp_calculation____title": "Calcul du CBN", "@sage/xtrem-stock/pages__mrp_calculation__calculationDate____title": "Date de calcul", "@sage/xtrem-stock/pages__mrp_calculation__calculationStatus____title": "Statut", "@sage/xtrem-stock/pages__mrp_calculation__company____columns__title__id": "Code", "@sage/xtrem-stock/pages__mrp_calculation__company____columns__title__name": "Nom", "@sage/xtrem-stock/pages__mrp_calculation__company____placeholder": "Sélectionner ...", "@sage/xtrem-stock/pages__mrp_calculation__company____title": "Société", "@sage/xtrem-stock/pages__mrp_calculation__createMrpCalculationRequest____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__mrp_calculation__criteriaBlock____title": "Critères", "@sage/xtrem-stock/pages__mrp_calculation__description____title": "Description", "@sage/xtrem-stock/pages__mrp_calculation__errorMessage____title": "Message d'erreur", "@sage/xtrem-stock/pages__mrp_calculation__explodeBillOfMaterial____title": "Éclater une nomenclature", "@sage/xtrem-stock/pages__mrp_calculation__fromItem____title": "Article début", "@sage/xtrem-stock/pages__mrp_calculation__fromItemText____title": "Article début", "@sage/xtrem-stock/pages__mrp_calculation__isSalesQuoteIncluded____title": "<PERSON><PERSON>re les devis de vente", "@sage/xtrem-stock/pages__mrp_calculation__itemFrom____columns__title__description": "Description", "@sage/xtrem-stock/pages__mrp_calculation__itemFrom____columns__title__id": "Code", "@sage/xtrem-stock/pages__mrp_calculation__itemFrom____columns__title__name": "Nom", "@sage/xtrem-stock/pages__mrp_calculation__itemFrom____placeholder": "Sélectionner ...", "@sage/xtrem-stock/pages__mrp_calculation__itemFrom____title": "Article début", "@sage/xtrem-stock/pages__mrp_calculation__itemTo____columns__title__description": "Description", "@sage/xtrem-stock/pages__mrp_calculation__itemTo____columns__title__id": "Article", "@sage/xtrem-stock/pages__mrp_calculation__itemTo____columns__title__name": "Nom", "@sage/xtrem-stock/pages__mrp_calculation__itemTo____placeholder": "Sélectionner ...", "@sage/xtrem-stock/pages__mrp_calculation__itemTo____title": "Article fin", "@sage/xtrem-stock/pages__mrp_calculation__linesSection____title": "Résultats", "@sage/xtrem-stock/pages__mrp_calculation__lowLevelCode____title": "Niveau de nomenclature le plus bas", "@sage/xtrem-stock/pages__mrp_calculation__mainSection____title": "Général", "@sage/xtrem-stock/pages__mrp_calculation__multiRefCompanies____columns__title__id": "Code", "@sage/xtrem-stock/pages__mrp_calculation__multiRefCompanies____columns__title__name": "Nom", "@sage/xtrem-stock/pages__mrp_calculation__multiRefCompanies____lookupDialogTitle": "Sélectionner la société", "@sage/xtrem-stock/pages__mrp_calculation__multiRefCompanies____title": "Sociétés", "@sage/xtrem-stock/pages__mrp_calculation__multiRefSites____columns__title__id": "Code", "@sage/xtrem-stock/pages__mrp_calculation__multiRefSites____columns__title__name": "Nom", "@sage/xtrem-stock/pages__mrp_calculation__multiRefSites____lookupDialogTitle": "Sélectionner le site", "@sage/xtrem-stock/pages__mrp_calculation__multiRefSites____title": "Sites", "@sage/xtrem-stock/pages__mrp_calculation__numberOfWeeks____title": "Période en semaines", "@sage/xtrem-stock/pages__mrp_calculation__purchase_order_creation__success": "Commande d'achat {{num}} c<PERSON><PERSON>.", "@sage/xtrem-stock/pages__mrp_calculation__resultLines____columns__columns__uCompany__name__title": "Nom", "@sage/xtrem-stock/pages__mrp_calculation__resultLines____columns__columns__uCompany__name__title__2": "Code", "@sage/xtrem-stock/pages__mrp_calculation__resultLines____columns__columns__uSite__name__title": "Nom", "@sage/xtrem-stock/pages__mrp_calculation__resultLines____columns__columns__uSite__name__title__2": "Code", "@sage/xtrem-stock/pages__mrp_calculation__resultLines____columns__title___sortValue": "Valeur tri", "@sage/xtrem-stock/pages__mrp_calculation__resultLines____columns__title__company": "Société", "@sage/xtrem-stock/pages__mrp_calculation__resultLines____columns__title__company__id": "Code société", "@sage/xtrem-stock/pages__mrp_calculation__resultLines____columns__title__company__name": "Nom société", "@sage/xtrem-stock/pages__mrp_calculation__resultLines____columns__title__endDate": "Date fin", "@sage/xtrem-stock/pages__mrp_calculation__resultLines____columns__title__item": "Article", "@sage/xtrem-stock/pages__mrp_calculation__resultLines____columns__title__item__id": "Code article", "@sage/xtrem-stock/pages__mrp_calculation__resultLines____columns__title__item__name": "Article", "@sage/xtrem-stock/pages__mrp_calculation__resultLines____columns__title__itemName": "Article", "@sage/xtrem-stock/pages__mrp_calculation__resultLines____columns__title__mrpCalculation__startDate": "Date de première suggestion", "@sage/xtrem-stock/pages__mrp_calculation__resultLines____columns__title__preferredProcess": "Méthode réappro.", "@sage/xtrem-stock/pages__mrp_calculation__resultLines____columns__title__preferredProcessString": "Mode réappro.", "@sage/xtrem-stock/pages__mrp_calculation__resultLines____columns__title__quantity": "Quantité", "@sage/xtrem-stock/pages__mrp_calculation__resultLines____columns__title__referenceItem__description": "Descr. article", "@sage/xtrem-stock/pages__mrp_calculation__resultLines____columns__title__referenceItem__id": "Code article", "@sage/xtrem-stock/pages__mrp_calculation__resultLines____columns__title__site": "Site", "@sage/xtrem-stock/pages__mrp_calculation__resultLines____columns__title__site__id": "Code site", "@sage/xtrem-stock/pages__mrp_calculation__resultLines____columns__title__site__name": "Nom site", "@sage/xtrem-stock/pages__mrp_calculation__resultLines____columns__title__startDate": "Date début", "@sage/xtrem-stock/pages__mrp_calculation__resultLines____columns__title__suggestionStatus": "Statut suggestion", "@sage/xtrem-stock/pages__mrp_calculation__resultLines____columns__title__uCompany__name": "Société", "@sage/xtrem-stock/pages__mrp_calculation__resultLines____columns__title__uSite__name": "Site", "@sage/xtrem-stock/pages__mrp_calculation__resultLines____dropdownActions__title": "<PERSON><PERSON><PERSON> commande d'achat", "@sage/xtrem-stock/pages__mrp_calculation__resultLines____dropdownActions__title__2": "Créer ordre de fabrication", "@sage/xtrem-stock/pages__mrp_calculation__resultLines____title": "Détails", "@sage/xtrem-stock/pages__mrp_calculation__sites____columns__title__id": "Code", "@sage/xtrem-stock/pages__mrp_calculation__sites____columns__title__name": "Nom", "@sage/xtrem-stock/pages__mrp_calculation__sites____placeholder": "Sélectionner ...", "@sage/xtrem-stock/pages__mrp_calculation__sites____title": "Site", "@sage/xtrem-stock/pages__mrp_calculation__startDate____title": "Date de début", "@sage/xtrem-stock/pages__mrp_calculation__toItem____title": "Article fin", "@sage/xtrem-stock/pages__mrp_calculation__toItemText____title": "Article fin", "@sage/xtrem-stock/pages__mrp_calculation__user____columns__title__email": "E-mail", "@sage/xtrem-stock/pages__mrp_calculation__user____columns__title__firstName": "Prénom", "@sage/xtrem-stock/pages__mrp_calculation__user____columns__title__lastName": "Nom", "@sage/xtrem-stock/pages__mrp_calculation__user____lookupDialogTitle": "Sélectionner l'utilisateur", "@sage/xtrem-stock/pages__mrp_calculation__user____title": "Calcul demandé par", "@sage/xtrem-stock/pages__mrp_calculation__valuationDate____title": "Date de début", "@sage/xtrem-stock/pages__mrp_calculation__work_order_creation__success": "Ordre de fabrication {{num}} c<PERSON><PERSON>.", "@sage/xtrem-stock/pages__mrp_calculation_justification____navigationPanel__listItem__calculationStatus__title": "Statut", "@sage/xtrem-stock/pages__mrp_calculation_justification____navigationPanel__listItem__numberOfWeeks__title": "Période en semaines", "@sage/xtrem-stock/pages__mrp_calculation_justification____navigationPanel__listItem__titleRight__title": "Date de calcul", "@sage/xtrem-stock/pages__mrp_calculation_justification____navigationPanel__listItem__user__title": "Calcul demandé par", "@sage/xtrem-stock/pages__mrp_calculation_justification____objectTypePlural": "Justifications de calcul CBN", "@sage/xtrem-stock/pages__mrp_calculation_justification____objectTypeSingular": "Justification de calcul CBN", "@sage/xtrem-stock/pages__mrp_calculation_justification____title": "Justification de calcul CBN", "@sage/xtrem-stock/pages__mrp_calculation_justification__calculationDate____title": "Date de calcul", "@sage/xtrem-stock/pages__mrp_calculation_justification__calculationStatus____title": "Statut", "@sage/xtrem-stock/pages__mrp_calculation_justification__criteriaBlock____title": "Critères", "@sage/xtrem-stock/pages__mrp_calculation_justification__description____title": "Description", "@sage/xtrem-stock/pages__mrp_calculation_justification__fromItem____title": "Article début", "@sage/xtrem-stock/pages__mrp_calculation_justification__fromItemText____title": "Article début", "@sage/xtrem-stock/pages__mrp_calculation_justification__inputSet____columns__title___id": "Code", "@sage/xtrem-stock/pages__mrp_calculation_justification__inputSet____title": "<PERSON><PERSON> <PERSON> don<PERSON>", "@sage/xtrem-stock/pages__mrp_calculation_justification__mainSection____title": "Général", "@sage/xtrem-stock/pages__mrp_calculation_justification__multiRefCompanies____columns__title__id": "Code", "@sage/xtrem-stock/pages__mrp_calculation_justification__multiRefCompanies____columns__title__name": "Nom", "@sage/xtrem-stock/pages__mrp_calculation_justification__multiRefCompanies____lookupDialogTitle": "Sélectionner la société", "@sage/xtrem-stock/pages__mrp_calculation_justification__multiRefCompanies____title": "Sociétés", "@sage/xtrem-stock/pages__mrp_calculation_justification__multiRefSites____columns__columns__legalCompany__name__title": "Code", "@sage/xtrem-stock/pages__mrp_calculation_justification__multiRefSites____columns__title__id": "Code", "@sage/xtrem-stock/pages__mrp_calculation_justification__multiRefSites____columns__title__legalCompany__name": "Société", "@sage/xtrem-stock/pages__mrp_calculation_justification__multiRefSites____columns__title__name": "Nom", "@sage/xtrem-stock/pages__mrp_calculation_justification__multiRefSites____lookupDialogTitle": "Sélectionner le site", "@sage/xtrem-stock/pages__mrp_calculation_justification__multiRefSites____title": "Sites", "@sage/xtrem-stock/pages__mrp_calculation_justification__numberOfWeeks____title": "Période en semaines", "@sage/xtrem-stock/pages__mrp_calculation_justification__results____columns__title__company__id": "Code société", "@sage/xtrem-stock/pages__mrp_calculation_justification__results____columns__title__company__name": "Nom société", "@sage/xtrem-stock/pages__mrp_calculation_justification__results____columns__title__firstSuggestionDate": "Date de première suggestion", "@sage/xtrem-stock/pages__mrp_calculation_justification__results____columns__title__item__id": "Code article", "@sage/xtrem-stock/pages__mrp_calculation_justification__results____columns__title__itemName": "Nom article", "@sage/xtrem-stock/pages__mrp_calculation_justification__results____columns__title__site__id": "Code site", "@sage/xtrem-stock/pages__mrp_calculation_justification__results____columns__title__site__name": "Nom site", "@sage/xtrem-stock/pages__mrp_calculation_justification__results____title": "Détails", "@sage/xtrem-stock/pages__mrp_calculation_justification__startDate____title": "Date de début", "@sage/xtrem-stock/pages__mrp_calculation_justification__toItem____title": "Article fin", "@sage/xtrem-stock/pages__mrp_calculation_justification__toItemText____title": "Article fin", "@sage/xtrem-stock/pages__mrp_calculation_justification__user____columns__title__email": "E-mail", "@sage/xtrem-stock/pages__mrp_calculation_justification__user____columns__title__firstName": "Prénom", "@sage/xtrem-stock/pages__mrp_calculation_justification__user____columns__title__lastName": "Nom", "@sage/xtrem-stock/pages__mrp_calculation_justification__user____lookupDialogTitle": "Sélectionner l'utilisateur", "@sage/xtrem-stock/pages__mrp_calculation_justification__user____title": "Calcul demandé par", "@sage/xtrem-stock/pages__mrp_calculation_request____title": "Demande de calcul de CBN", "@sage/xtrem-stock/pages__mrp_calculation_request__cancelRequest____title": "Annuler", "@sage/xtrem-stock/pages__mrp_calculation_request__companies____columns__title__id": "Code", "@sage/xtrem-stock/pages__mrp_calculation_request__companies____columns__title__name": "Nom", "@sage/xtrem-stock/pages__mrp_calculation_request__companies____lookupDialogTitle": "Sélectionner la société", "@sage/xtrem-stock/pages__mrp_calculation_request__companies____placeholder": "Sélectionner...", "@sage/xtrem-stock/pages__mrp_calculation_request__companies____title": "Société", "@sage/xtrem-stock/pages__mrp_calculation_request__company____columns__title__id": "Code", "@sage/xtrem-stock/pages__mrp_calculation_request__company____columns__title__name": "Nom", "@sage/xtrem-stock/pages__mrp_calculation_request__company____placeholder": "Sélectionner ...", "@sage/xtrem-stock/pages__mrp_calculation_request__company____title": "Société", "@sage/xtrem-stock/pages__mrp_calculation_request__criteriaBlock____title": "Critères", "@sage/xtrem-stock/pages__mrp_calculation_request__description____title": "Description", "@sage/xtrem-stock/pages__mrp_calculation_request__explodeBillOfMaterial____title": "Éclater une nomenclature", "@sage/xtrem-stock/pages__mrp_calculation_request__invalid_criteria": "Aucun article-site réapprovisionné par CBN pour les critères renseignés.", "@sage/xtrem-stock/pages__mrp_calculation_request__invalid_item_from_range": "L''article fin' ne peut pas être postérieur à l''article début'.", "@sage/xtrem-stock/pages__mrp_calculation_request__invalid_item_to_range": "L''article début' ne peut pas être inférieur à l''article fin'.", "@sage/xtrem-stock/pages__mrp_calculation_request__isSalesQuoteIncluded____title": "<PERSON><PERSON>re les devis de vente", "@sage/xtrem-stock/pages__mrp_calculation_request__itemFrom____columns__title__description": "Description", "@sage/xtrem-stock/pages__mrp_calculation_request__itemFrom____columns__title__id": "Code", "@sage/xtrem-stock/pages__mrp_calculation_request__itemFrom____columns__title__name": "Nom", "@sage/xtrem-stock/pages__mrp_calculation_request__itemFrom____lookupDialogTitle": "Sélectionner l'article", "@sage/xtrem-stock/pages__mrp_calculation_request__itemFrom____placeholder": "Sélectionner...", "@sage/xtrem-stock/pages__mrp_calculation_request__itemFrom____title": "Article début", "@sage/xtrem-stock/pages__mrp_calculation_request__itemTo____columns__title__category__name": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__mrp_calculation_request__itemTo____lookupDialogTitle": "Sélectionner l'article", "@sage/xtrem-stock/pages__mrp_calculation_request__itemTo____placeholder": "Sélectionner...", "@sage/xtrem-stock/pages__mrp_calculation_request__itemTo____title": "Article fin", "@sage/xtrem-stock/pages__mrp_calculation_request__mainSection____title": "Général", "@sage/xtrem-stock/pages__mrp_calculation_request__notification_success": "<PERSON><PERSON><PERSON> de calcul de CBN envoyée", "@sage/xtrem-stock/pages__mrp_calculation_request__numberOfWeeks____title": "Période en semaines", "@sage/xtrem-stock/pages__mrp_calculation_request__scheduleMrpCalculationRequest____title": "Planifier", "@sage/xtrem-stock/pages__mrp_calculation_request__sites____columns__title__id": "Code", "@sage/xtrem-stock/pages__mrp_calculation_request__sites____columns__title__name": "Nom", "@sage/xtrem-stock/pages__mrp_calculation_request__sites____lookupDialogTitle": "Sélectionner le site", "@sage/xtrem-stock/pages__mrp_calculation_request__sites____placeholder": "Sélectionner...", "@sage/xtrem-stock/pages__mrp_calculation_request__sites____title": "Site", "@sage/xtrem-stock/pages__mrp_calculation_request__startDate____title": "Date de début", "@sage/xtrem-stock/pages__mrp_calculation_request__supply_chain_error": "Les commandes d'achat sont actuellement créées à partir de calculs CBN précédents. Réessayez.", "@sage/xtrem-stock/pages__mrp_projected_stock_dialog____title": "Stock projeté", "@sage/xtrem-stock/pages__mrp_projected_stock_dialog__batchQuantity____title": "Quantité batch", "@sage/xtrem-stock/pages__mrp_projected_stock_dialog__chartBlock____title": "Stock projeté", "@sage/xtrem-stock/pages__mrp_projected_stock_dialog__criteriaBlock____title": "Stock projeté", "@sage/xtrem-stock/pages__mrp_projected_stock_dialog__day": "Jour", "@sage/xtrem-stock/pages__mrp_projected_stock_dialog__economicOrderQuantity____title": "Lot économique", "@sage/xtrem-stock/pages__mrp_projected_stock_dialog__frequency____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__mrp_projected_stock_dialog__item____title": "Article", "@sage/xtrem-stock/pages__mrp_projected_stock_dialog__lines____columns__title__bucket": "Période", "@sage/xtrem-stock/pages__mrp_projected_stock_dialog__lines____columns__title__documentNumber": "N° doc", "@sage/xtrem-stock/pages__mrp_projected_stock_dialog__lines____columns__title__documentType": "Type doc.", "@sage/xtrem-stock/pages__mrp_projected_stock_dialog__lines____columns__title__dueDate": "Date d'échéance", "@sage/xtrem-stock/pages__mrp_projected_stock_dialog__lines____columns__title__quantity": "Quantité", "@sage/xtrem-stock/pages__mrp_projected_stock_dialog__lines____columns__title__suggestion": "Suggestion", "@sage/xtrem-stock/pages__mrp_projected_stock_dialog__lines____title": "Détails", "@sage/xtrem-stock/pages__mrp_projected_stock_dialog__mainSection____title": "Informations générales", "@sage/xtrem-stock/pages__mrp_projected_stock_dialog__numberOfWeeks____title": "Période en semaines", "@sage/xtrem-stock/pages__mrp_projected_stock_dialog__ok____title": "OK", "@sage/xtrem-stock/pages__mrp_projected_stock_dialog__preferredProcess____title": "<PERSON><PERSON>", "@sage/xtrem-stock/pages__mrp_projected_stock_dialog__productionLeadTime____title": "Délai de production", "@sage/xtrem-stock/pages__mrp_projected_stock_dialog__projectedStockChart____chart__series__title": "Stock projeté", "@sage/xtrem-stock/pages__mrp_projected_stock_dialog__projectedStockChart____chart__series__title__2": "Niveau de sécurité", "@sage/xtrem-stock/pages__mrp_projected_stock_dialog__projectedStockChart____chart__xAxis__title": "Date", "@sage/xtrem-stock/pages__mrp_projected_stock_dialog__projectedStockChart____title": "Stock projeté", "@sage/xtrem-stock/pages__mrp_projected_stock_dialog__purchaseLeadTime____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__mrp_projected_stock_dialog__safetyStock____title": "Stock de sécurité", "@sage/xtrem-stock/pages__mrp_projected_stock_dialog__site____title": "Site", "@sage/xtrem-stock/pages__mrp_projected_stock_dialog__startDate____title": "Date de début", "@sage/xtrem-stock/pages__mrp_projected_stock_dialog__starting_stock": "Stock de départ", "@sage/xtrem-stock/pages__mrp_projected_stock_dialog__stockUnit____columns__title__decimalDigits": "Décimales", "@sage/xtrem-stock/pages__mrp_projected_stock_dialog__stockUnit____columns__title__symbol": "Symbole", "@sage/xtrem-stock/pages__mrp_projected_stock_dialog__week": "<PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__mrp_synchronization____navigationPanel__listItem__lastSyncDateTime__title": "Dernière synchronisation", "@sage/xtrem-stock/pages__mrp_synchronization____navigationPanel__listItem__titleRight__title": "Status de la dernière synchronisation", "@sage/xtrem-stock/pages__mrp_synchronization____title": "Synchronisation CBN", "@sage/xtrem-stock/pages__mrp_synchronization___id____title": "Code", "@sage/xtrem-stock/pages__mrp_synchronization__criteriaBlock____title": "Critères", "@sage/xtrem-stock/pages__mrp_synchronization__lastSyncDateTime____title": "Dernière synchronisation", "@sage/xtrem-stock/pages__mrp_synchronization__mainSection____title": "Général", "@sage/xtrem-stock/pages__mrp_synchronization__resetSynchronization____title": "Synchroniser tous les enregistrements", "@sage/xtrem-stock/pages__mrp_synchronization__synchronizeDeletions____title": "Synchroniser les suppressions", "@sage/xtrem-stock/pages__mrp_synchronization__synchronizeMrp____title": "Synchroniser", "@sage/xtrem-stock/pages__mrp_synchronization__syncStatus____title": "Status de la dernière synchronisation", "@sage/xtrem-stock/pages__mrp_synchronization__type____title": "Type", "@sage/xtrem-stock/pages__mrp_synchronization_request__notification_success": "Demande de synchronisation de CBN envoyée", "@sage/xtrem-stock/pages__serial_number_inquiry____navigationPanel__listItem__allocatedQuantity__title": "Quantité allouée", "@sage/xtrem-stock/pages__serial_number_inquiry____navigationPanel__listItem__availableQuantity__title": "Quantité disponible", "@sage/xtrem-stock/pages__serial_number_inquiry____navigationPanel__listItem__creationDate__title": "Date de création numéro de série", "@sage/xtrem-stock/pages__serial_number_inquiry____navigationPanel__listItem__documentLink__title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__serial_number_inquiry____navigationPanel__listItem__id__title": "Numéro de série", "@sage/xtrem-stock/pages__serial_number_inquiry____navigationPanel__listItem__isAllocated__title": "Allou<PERSON>", "@sage/xtrem-stock/pages__serial_number_inquiry____navigationPanel__listItem__itemId__title": "Code article", "@sage/xtrem-stock/pages__serial_number_inquiry____navigationPanel__listItem__itemName__title": "Article", "@sage/xtrem-stock/pages__serial_number_inquiry____navigationPanel__listItem__locationId__title": "Code emplacement", "@sage/xtrem-stock/pages__serial_number_inquiry____navigationPanel__listItem__locationZone__title": "Zone emplacement", "@sage/xtrem-stock/pages__serial_number_inquiry____navigationPanel__listItem__lot__title": "Lot", "@sage/xtrem-stock/pages__serial_number_inquiry____navigationPanel__listItem__lotCreationDate__title": "Date de création de lot", "@sage/xtrem-stock/pages__serial_number_inquiry____navigationPanel__listItem__lotExpirationDate__title": "Date de péremption lot", "@sage/xtrem-stock/pages__serial_number_inquiry____navigationPanel__listItem__onHandQuantity__title": "Quantité physique", "@sage/xtrem-stock/pages__serial_number_inquiry____navigationPanel__listItem__owner__title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__serial_number_inquiry____navigationPanel__listItem__siteId__title": "Code site", "@sage/xtrem-stock/pages__serial_number_inquiry____navigationPanel__listItem__siteName__title": "Site", "@sage/xtrem-stock/pages__serial_number_inquiry____navigationPanel__listItem__stockLocation__title": "Emplacement", "@sage/xtrem-stock/pages__serial_number_inquiry____navigationPanel__listItem__stockStatus__title": "Contrôle qualité", "@sage/xtrem-stock/pages__serial_number_inquiry____navigationPanel__listItem__stockUnit__title": "Unité", "@sage/xtrem-stock/pages__serial_number_inquiry____navigationPanel__listItem__subLot__title": "Sous-lot", "@sage/xtrem-stock/pages__serial_number_inquiry____navigationPanel__listItem__supplierId__title": "Code fournisseur", "@sage/xtrem-stock/pages__serial_number_inquiry____navigationPanel__listItem__supplierLot__title": "Numéro lot fournisseur", "@sage/xtrem-stock/pages__serial_number_inquiry____navigationPanel__listItem__supplierName__columns__title__businessEntity__id": "Code", "@sage/xtrem-stock/pages__serial_number_inquiry____navigationPanel__listItem__supplierName__columns__title__businessEntity__name": "Nom", "@sage/xtrem-stock/pages__serial_number_inquiry____navigationPanel__listItem__supplierName__title": "Fournisseur", "@sage/xtrem-stock/pages__serial_number_inquiry____navigationPanel__listItem__title__title": "Numéro de série", "@sage/xtrem-stock/pages__serial_number_inquiry____navigationPanel__optionsMenu__title": "Tous", "@sage/xtrem-stock/pages__serial_number_inquiry____title": "Consultation des numéros de série en stock", "@sage/xtrem-stock/pages__standard_cost_calculation____objectTypePlural": "Calculs de coûts standards", "@sage/xtrem-stock/pages__standard_cost_calculation____objectTypeSingular": "Calcul de coût standard", "@sage/xtrem-stock/pages__standard_cost_calculation____title": "Calcul de coût standard", "@sage/xtrem-stock/pages__standard_cost_calculation__button_be_notified": "M'informer", "@sage/xtrem-stock/pages__standard_cost_calculation__button_wait": "<PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__standard_cost_calculation__calculation_dialog_content": "Le calcul peut prendre un certain temps. Voulez-vous attendre la fin du calcul ou quitter cette page et être informé lorsque le calcul est terminé ?", "@sage/xtrem-stock/pages__standard_cost_calculation__commodityCode____title": "Code marchandise", "@sage/xtrem-stock/pages__standard_cost_calculation__cost_creation_content": "La création des enregistrements de coût d'article-site peut prendre un certain temps. Voulez-vous attendre que la création des enregistrements soit effectuée ou quitter cette page et être informé lorsque la création est terminée ?\"", "@sage/xtrem-stock/pages__standard_cost_calculation__costCategory____columns__title__costCategoryType": "Type", "@sage/xtrem-stock/pages__standard_cost_calculation__costCategory____columns__title__isMandatory": "Obligatoire", "@sage/xtrem-stock/pages__standard_cost_calculation__costCategory____lookupDialogTitle": "Sélectionner la catégorie de coût", "@sage/xtrem-stock/pages__standard_cost_calculation__costCategory____title": "<PERSON><PERSON><PERSON><PERSON> de <PERSON>", "@sage/xtrem-stock/pages__standard_cost_calculation__criteriaBlock____title": "Critères", "@sage/xtrem-stock/pages__standard_cost_calculation__defaultDimension____title": "Paramétrer les sections par défaut", "@sage/xtrem-stock/pages__standard_cost_calculation__dimensions_updated": "Sections mises à jour", "@sage/xtrem-stock/pages__standard_cost_calculation__fromDate____title": "Date de début", "@sage/xtrem-stock/pages__standard_cost_calculation__includesRouting____title": "Inclure la gamme", "@sage/xtrem-stock/pages__standard_cost_calculation__isAllSelected____title": "sélectionner toutes les lignes de résultats", "@sage/xtrem-stock/pages__standard_cost_calculation__itemCategory____columns__title__type": "Type", "@sage/xtrem-stock/pages__standard_cost_calculation__itemCategory____lookupDialogTitle": "Sélectionner la catégorie d'article", "@sage/xtrem-stock/pages__standard_cost_calculation__itemCategory____title": "Catégorie d'article", "@sage/xtrem-stock/pages__standard_cost_calculation__items____columns__title__id": "Code", "@sage/xtrem-stock/pages__standard_cost_calculation__items____columns__title__name": "Nom", "@sage/xtrem-stock/pages__standard_cost_calculation__items____lookupDialogTitle": "Sélectionner l'article", "@sage/xtrem-stock/pages__standard_cost_calculation__items____title": "Articles", "@sage/xtrem-stock/pages__standard_cost_calculation__linesSection____title": "Lignes de résultat", "@sage/xtrem-stock/pages__standard_cost_calculation__mainSection____title": "Général", "@sage/xtrem-stock/pages__standard_cost_calculation__quantity____title": "Quantité", "@sage/xtrem-stock/pages__standard_cost_calculation__request_notification_item_site_cost_creation_finished": "Création du coût article-site terminée", "@sage/xtrem-stock/pages__standard_cost_calculation__request_notification_item_site_cost_creation_impossible": "<PERSON><PERSON> de<PERSON> d'abord exécuter le calcul.", "@sage/xtrem-stock/pages__standard_cost_calculation__request_notification_item_site_cost_creation_request_sent": "De<PERSON>e de création du coût de l'article-site envoyée", "@sage/xtrem-stock/pages__standard_cost_calculation__resultLines____columns__title__creationErrorMessage": "Message erreur", "@sage/xtrem-stock/pages__standard_cost_calculation__resultLines____columns__title__creationStatus": "Statut de création de coût", "@sage/xtrem-stock/pages__standard_cost_calculation__resultLines____columns__title__currentLaborCost": "Coût main d'oeuvre actuel", "@sage/xtrem-stock/pages__standard_cost_calculation__resultLines____columns__title__currentMachineCost": "Coût machine actuel", "@sage/xtrem-stock/pages__standard_cost_calculation__resultLines____columns__title__currentMaterialCost": "Coût matière actuel", "@sage/xtrem-stock/pages__standard_cost_calculation__resultLines____columns__title__currentToolCost": "Coût outil actuel", "@sage/xtrem-stock/pages__standard_cost_calculation__resultLines____columns__title__currentTotalCost": "Coût total actuel", "@sage/xtrem-stock/pages__standard_cost_calculation__resultLines____columns__title__item__description": "Descr. article", "@sage/xtrem-stock/pages__standard_cost_calculation__resultLines____columns__title__item__id": "Code article", "@sage/xtrem-stock/pages__standard_cost_calculation__resultLines____columns__title__item__name": "Article", "@sage/xtrem-stock/pages__standard_cost_calculation__resultLines____columns__title__laborCost": "Coût main d'oeuvre", "@sage/xtrem-stock/pages__standard_cost_calculation__resultLines____columns__title__machineCost": "Coût machine", "@sage/xtrem-stock/pages__standard_cost_calculation__resultLines____columns__title__materialCost": "Co<PERSON>t matière", "@sage/xtrem-stock/pages__standard_cost_calculation__resultLines____columns__title__toolCost": "Coût outil", "@sage/xtrem-stock/pages__standard_cost_calculation__resultLines____columns__title__totalCost": "Coût total", "@sage/xtrem-stock/pages__standard_cost_calculation__resultLines____dropdownActions__title": "Sous-ensembles", "@sage/xtrem-stock/pages__standard_cost_calculation__resultLines____dropdownActions__title__2": "Sections", "@sage/xtrem-stock/pages__standard_cost_calculation__runCreateItemSiteCostsFromRollUpResults____title": "<PERSON><PERSON><PERSON> co<PERSON>t article-site", "@sage/xtrem-stock/pages__standard_cost_calculation__runStandardCostCalculation____title": "Calculer", "@sage/xtrem-stock/pages__standard_cost_calculation__site____columns__columns__legalCompany__name__title": "<PERSON><PERSON>", "@sage/xtrem-stock/pages__standard_cost_calculation__site____columns__title__id": "Code ", "@sage/xtrem-stock/pages__standard_cost_calculation__site____columns__title__legalCompany__name": "Société", "@sage/xtrem-stock/pages__standard_cost_calculation__site____columns__title__name": "Nom", "@sage/xtrem-stock/pages__standard_cost_calculation__site____lookupDialogTitle": "Sélectionner le site", "@sage/xtrem-stock/pages__standard_cost_calculation__site____title": "Site", "@sage/xtrem-stock/pages__standard_cost_calculation__status____title": "Statut", "@sage/xtrem-stock/pages__standard_cost_calculation__title_wait_for_finish": "Attendre que le calcul soit effectué", "@sage/xtrem-stock/pages__standard_cost_calculation__user____columns__title__email": "E-mail", "@sage/xtrem-stock/pages__standard_cost_calculation__user____columns__title__firstName": "Prénom", "@sage/xtrem-stock/pages__standard_cost_calculation__user____columns__title__lastName": "Nom de famille", "@sage/xtrem-stock/pages__standard_cost_calculation__user____lookupDialogTitle": "Sélectionner l'utilisateur", "@sage/xtrem-stock/pages__standard_cost_calculation__user____title": "Calcul demandé par", "@sage/xtrem-stock/pages__standard_cost_calculation__usesComponentStandardCost____title": "Coût standard composant", "@sage/xtrem-stock/pages__standard_cost_calculation__wait_for_creation": "Attendre que les enregistrements de coût d'article-site soient créés", "@sage/xtrem-stock/pages__standard_cost_calculation_request__notification_request_sent": "De<PERSON><PERSON> de calcul de coût standard envoyée", "@sage/xtrem-stock/pages__standard_cost_calculation_request__notification_valuation_failed": "Échec du calcul de coût standard", "@sage/xtrem-stock/pages__standard_cost_calculation_request__notification_valuation_finished": "Fin du calcul de coût standard", "@sage/xtrem-stock/pages__stock__adjustment_step_sequence_detail_stock": "Détail stock", "@sage/xtrem-stock/pages__stock_adjustment____navigationPanel__dropdownActions__title": "Définir sections", "@sage/xtrem-stock/pages__stock_adjustment____navigationPanel__dropdownActions__title__2": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_adjustment____navigationPanel__listItem__description__title": "Description", "@sage/xtrem-stock/pages__stock_adjustment____navigationPanel__listItem__line2__title": "Site", "@sage/xtrem-stock/pages__stock_adjustment____navigationPanel__listItem__line2Right__title": "Date", "@sage/xtrem-stock/pages__stock_adjustment____navigationPanel__listItem__reasonCode__title": "<PERSON><PERSON><PERSON> de régu<PERSON>", "@sage/xtrem-stock/pages__stock_adjustment____navigationPanel__listItem__title__title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_adjustment____navigationPanel__listItem__titleRight__title": "Statut", "@sage/xtrem-stock/pages__stock_adjustment____navigationPanel__optionsMenu__title": "Tous les statuts ouverts", "@sage/xtrem-stock/pages__stock_adjustment____navigationPanel__optionsMenu__title__2": "Tous les statuts", "@sage/xtrem-stock/pages__stock_adjustment____navigationPanel__optionsMenu__title__3": "Détails demandés", "@sage/xtrem-stock/pages__stock_adjustment____navigationPanel__optionsMenu__title__4": "<PERSON>é<PERSON> renseign<PERSON>", "@sage/xtrem-stock/pages__stock_adjustment____navigationPanel__optionsMenu__title__5": "Comptabilisation de stock en cours", "@sage/xtrem-stock/pages__stock_adjustment____navigationPanel__optionsMenu__title__6": "Erreur de comptabilisation de stock", "@sage/xtrem-stock/pages__stock_adjustment____navigationPanel__optionsMenu__title__7": "Regularisé", "@sage/xtrem-stock/pages__stock_adjustment____objectTypePlural": "Régularisations de stock", "@sage/xtrem-stock/pages__stock_adjustment____objectTypeSingular": "Régularisation de stock", "@sage/xtrem-stock/pages__stock_adjustment____title": "Régularisation de stock", "@sage/xtrem-stock/pages__stock_adjustment__addAdjustLine____title": "<PERSON><PERSON><PERSON> ligne", "@sage/xtrem-stock/pages__stock_adjustment__apply_dimensions_success": "Sections appliquées", "@sage/xtrem-stock/pages__stock_adjustment__defaultDimension____title": "Définir sections", "@sage/xtrem-stock/pages__stock_adjustment__description____title": "Description", "@sage/xtrem-stock/pages__stock_adjustment__displayStatus____title": "Statut d'affichage", "@sage/xtrem-stock/pages__stock_adjustment__effectiveDate____title": "Date", "@sage/xtrem-stock/pages__stock_adjustment__goToSysNotificationPage____title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_adjustment__line_delete_action_dialog_content": "Vous êtes sur le point de supprimer cette ligne de régularisation de stock.", "@sage/xtrem-stock/pages__stock_adjustment__line_delete_action_dialog_title": "Confirmer la <PERSON>", "@sage/xtrem-stock/pages__stock_adjustment__lines____columns__columns__item__name__title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_adjustment__lines____columns__columns__item__name__title__2": "Unité", "@sage/xtrem-stock/pages__stock_adjustment__lines____columns__columns__item__name__title__3": "Gestion lots", "@sage/xtrem-stock/pages__stock_adjustment__lines____columns__columns__location__name__title": "Nom", "@sage/xtrem-stock/pages__stock_adjustment__lines____columns__columns__location__name__title__2": "Code", "@sage/xtrem-stock/pages__stock_adjustment__lines____columns__columns__lot__id__columns__title__id": "Code", "@sage/xtrem-stock/pages__stock_adjustment__lines____columns__columns__lot__id__columns__title__name": "Nom", "@sage/xtrem-stock/pages__stock_adjustment__lines____columns__columns__lot__id__title": "Code", "@sage/xtrem-stock/pages__stock_adjustment__lines____columns__columns__lot__id__title__2": "Article", "@sage/xtrem-stock/pages__stock_adjustment__lines____columns__columns__lot__id__title__3": "Date péremption", "@sage/xtrem-stock/pages__stock_adjustment__lines____columns__columns__stockStatus__name__title": "Nom", "@sage/xtrem-stock/pages__stock_adjustment__lines____columns__columns__stockStatus__name__title__2": "Code", "@sage/xtrem-stock/pages__stock_adjustment__lines____columns__columns__stockStatus__title": "Nom", "@sage/xtrem-stock/pages__stock_adjustment__lines____columns__columns__stockStatus__title__2": "Code", "@sage/xtrem-stock/pages__stock_adjustment__lines____columns__lookupDialogTitle__item__name": "Sélectionner l'article", "@sage/xtrem-stock/pages__stock_adjustment__lines____columns__lookupDialogTitle__location__name": "Sélectionner l'emplacement", "@sage/xtrem-stock/pages__stock_adjustment__lines____columns__lookupDialogTitle__lot__id": "Sélectionner le lot", "@sage/xtrem-stock/pages__stock_adjustment__lines____columns__lookupDialogTitle__stockStatus": "Sé<PERSON><PERSON>ner une valeur qualité", "@sage/xtrem-stock/pages__stock_adjustment__lines____columns__lookupDialogTitle__stockStatus__name": "Sélectionner le statut de stock", "@sage/xtrem-stock/pages__stock_adjustment__lines____columns__placeholder__item__name": "Sélectionner...", "@sage/xtrem-stock/pages__stock_adjustment__lines____columns__placeholder__lot__id": "Sélectionner...", "@sage/xtrem-stock/pages__stock_adjustment__lines____columns__title__2": "Détails", "@sage/xtrem-stock/pages__stock_adjustment__lines____columns__title__adjustmentQuantityInStockUnit": "Qté régularisation", "@sage/xtrem-stock/pages__stock_adjustment__lines____columns__title__displayStatus": "Statut", "@sage/xtrem-stock/pages__stock_adjustment__lines____columns__title__item__description": "Descr. article", "@sage/xtrem-stock/pages__stock_adjustment__lines____columns__title__item__id": "Code article", "@sage/xtrem-stock/pages__stock_adjustment__lines____columns__title__item__name": "Article", "@sage/xtrem-stock/pages__stock_adjustment__lines____columns__title__location__name": "Emplacement", "@sage/xtrem-stock/pages__stock_adjustment__lines____columns__title__lot__id": "Lot", "@sage/xtrem-stock/pages__stock_adjustment__lines____columns__title__newStockQuantity": "Nouvelle quantité stock", "@sage/xtrem-stock/pages__stock_adjustment__lines____columns__title__quantityInStockUnit": "Quantité stock", "@sage/xtrem-stock/pages__stock_adjustment__lines____columns__title__stockStatus": "Contrôle qualité", "@sage/xtrem-stock/pages__stock_adjustment__lines____columns__title__stockStatus__name": "Statut", "@sage/xtrem-stock/pages__stock_adjustment__lines____columns__title__stockValue": "Valeur stock", "@sage/xtrem-stock/pages__stock_adjustment__lines____columns__title__unitCost": "Coût unitaire", "@sage/xtrem-stock/pages__stock_adjustment__lines____dropdownActions__title": "Détails de stock", "@sage/xtrem-stock/pages__stock_adjustment__lines____dropdownActions__title__2": "Sections", "@sage/xtrem-stock/pages__stock_adjustment__lines____dropdownActions__title__3": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_adjustment__lines____inlineActions__title": "Ouv<PERSON>r volet des lignes", "@sage/xtrem-stock/pages__stock_adjustment__lines____mobileCard__line2__title": "Description", "@sage/xtrem-stock/pages__stock_adjustment__lines____mobileCard__title__title": "Article", "@sage/xtrem-stock/pages__stock_adjustment__lines____mobileCard__titleRight__title": "Statut", "@sage/xtrem-stock/pages__stock_adjustment__lines____optionsMenu__title": "Tous les statuts", "@sage/xtrem-stock/pages__stock_adjustment__lines____optionsMenu__title__2": "Tous les statuts ouverts", "@sage/xtrem-stock/pages__stock_adjustment__lines____optionsMenu__title__3": "Détails demandés", "@sage/xtrem-stock/pages__stock_adjustment__lines____optionsMenu__title__4": "<PERSON>é<PERSON> renseign<PERSON>", "@sage/xtrem-stock/pages__stock_adjustment__lines____optionsMenu__title__5": "Comptabilisation de stock en cours", "@sage/xtrem-stock/pages__stock_adjustment__lines____optionsMenu__title__6": "Erreur de comptabilisation de stock", "@sage/xtrem-stock/pages__stock_adjustment__lines____optionsMenu__title__7": "Regularisé", "@sage/xtrem-stock/pages__stock_adjustment__lines____sidebar__headerDropdownActions__title": "Détails de stock", "@sage/xtrem-stock/pages__stock_adjustment__lines____sidebar__headerDropdownActions__title__2": "Sections", "@sage/xtrem-stock/pages__stock_adjustment__lines____sidebar__headerDropdownActions__title__3": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_adjustment__lines____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_adjustment__linesSection____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_adjustment__mainSection____title": "Général", "@sage/xtrem-stock/pages__stock_adjustment__number____title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_adjustment__post____title": "Comptabiliser le stock", "@sage/xtrem-stock/pages__stock_adjustment__postingDetails____columns__title__financeIntegrationAppRecordId": "Référence intégration comptable", "@sage/xtrem-stock/pages__stock_adjustment__postingDetails____columns__title__postingStatus": "Statut", "@sage/xtrem-stock/pages__stock_adjustment__postingDetails____columns__title__targetDocumentNumber": "N° doc", "@sage/xtrem-stock/pages__stock_adjustment__postingDetails____columns__title__targetDocumentType": "Type doc", "@sage/xtrem-stock/pages__stock_adjustment__postingDetails____title": "Résultats", "@sage/xtrem-stock/pages__stock_adjustment__postingMessageBlock____title": "<PERSON><PERSON><PERSON> de l'erreur", "@sage/xtrem-stock/pages__stock_adjustment__postingSection____title": "Comptabilisation", "@sage/xtrem-stock/pages__stock_adjustment__reasonCode____columns__title__quantityDecreaseAdjustment": "Diminution quantité", "@sage/xtrem-stock/pages__stock_adjustment__reasonCode____columns__title__quantityIncreaseAdjustment": "Augmentation quantité", "@sage/xtrem-stock/pages__stock_adjustment__reasonCode____lookupDialogTitle": "Sélectionner le code motif", "@sage/xtrem-stock/pages__stock_adjustment__reasonCode____title": "<PERSON><PERSON><PERSON> de régu<PERSON>", "@sage/xtrem-stock/pages__stock_adjustment__repost____title": "Recomptabiliser", "@sage/xtrem-stock/pages__stock_adjustment__repost_errors": "Erreurs lors de la recomptabilisation:", "@sage/xtrem-stock/pages__stock_adjustment__resync_display_status_continue": "<PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_adjustment__resync_display_status_message": "Vous êtes sur le point de corriger le statut de stock de cette régularisation de stock en Brouillon.", "@sage/xtrem-stock/pages__stock_adjustment__resync_display_status_title": "Statut de transaction de stock", "@sage/xtrem-stock/pages__stock_adjustment__saveStockAdjustment____title": "Enregistrer", "@sage/xtrem-stock/pages__stock_adjustment__stock_journal_inquiry": "Consultation des journaux de stock", "@sage/xtrem-stock/pages__stock_adjustment__stock_posting_error": "Erreur de comptabilisation de stock", "@sage/xtrem-stock/pages__stock_adjustment__stockSite____columns__columns__legalCompany__name__columns__title__symbol": "Symbole", "@sage/xtrem-stock/pages__stock_adjustment__stockSite____lookupDialogTitle": "Sélectionner le site", "@sage/xtrem-stock/pages__stock_adjustment__stockSite____placeholder": "Sélectionner...", "@sage/xtrem-stock/pages__stock_adjustment__stockSite____title": "Site", "@sage/xtrem-stock/pages__stock_adjustment__stockTransactionStatus____title": "Statut de stock", "@sage/xtrem-stock/pages__stock_adjustment__The_lot_is_mandatory": "Renseignez le lot.", "@sage/xtrem-stock/pages__stock_adjustment_post__stock_details_required": "<PERSON><PERSON> de<PERSON> renseigner les détails de stock pour toutes les lignes avant d'effectuer la comptabilisation.", "@sage/xtrem-stock/pages__stock_adjustment_step_sequence_creation": "<PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_adjustment_step_sequence_post_stock": "Comptabiliser le stock", "@sage/xtrem-stock/pages__stock_allocation_inquiry____navigationPanel__listItem__documentLink__title": "Numéro de document", "@sage/xtrem-stock/pages__stock_allocation_inquiry____navigationPanel__listItem__quantityInStockUnit__title": "Quantité allouée", "@sage/xtrem-stock/pages__stock_allocation_inquiry____navigationPanel__listItem__title__title": "Numéro de document", "@sage/xtrem-stock/pages__stock_allocation_inquiry____title": "Consultation d'allocation de stock", "@sage/xtrem-stock/pages__stock_avc_justification_inquiry____title": "Justification de prix moyen pond<PERSON>", "@sage/xtrem-stock/pages__stock_avc_justification_inquiry__company____lookupDialogTitle": "Sélectionner la société", "@sage/xtrem-stock/pages__stock_avc_justification_inquiry__company____placeholder": "Sélectionner...", "@sage/xtrem-stock/pages__stock_avc_justification_inquiry__company____title": "Société", "@sage/xtrem-stock/pages__stock_avc_justification_inquiry__criteriaBlock____title": "Critères", "@sage/xtrem-stock/pages__stock_avc_justification_inquiry__itemFrom____columns__title__category__name": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_avc_justification_inquiry__itemFrom____lookupDialogTitle": "Sélectionner l'article", "@sage/xtrem-stock/pages__stock_avc_justification_inquiry__itemFrom____placeholder": "Sélectionner...", "@sage/xtrem-stock/pages__stock_avc_justification_inquiry__itemFrom____title": "Article début", "@sage/xtrem-stock/pages__stock_avc_justification_inquiry__itemSelection____columns__title__item__description": "Descr. article", "@sage/xtrem-stock/pages__stock_avc_justification_inquiry__itemSelection____columns__title__item__id": "Code article", "@sage/xtrem-stock/pages__stock_avc_justification_inquiry__itemSelection____columns__title__item__name": "Article", "@sage/xtrem-stock/pages__stock_avc_justification_inquiry__itemSelection____columns__title__site__name": "Site", "@sage/xtrem-stock/pages__stock_avc_justification_inquiry__itemSelection____columns__title__stockUnit__name": "Unité mesure", "@sage/xtrem-stock/pages__stock_avc_justification_inquiry__itemSelection____title": "Résultats", "@sage/xtrem-stock/pages__stock_avc_justification_inquiry__itemTo____columns__title__category__name": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_avc_justification_inquiry__itemTo____columns__title__description": "Description", "@sage/xtrem-stock/pages__stock_avc_justification_inquiry__itemTo____columns__title__id": "Article", "@sage/xtrem-stock/pages__stock_avc_justification_inquiry__itemTo____columns__title__name": "Nom", "@sage/xtrem-stock/pages__stock_avc_justification_inquiry__itemTo____lookupDialogTitle": "Sélectionner l'article", "@sage/xtrem-stock/pages__stock_avc_justification_inquiry__itemTo____placeholder": "Sélectionner...", "@sage/xtrem-stock/pages__stock_avc_justification_inquiry__itemTo____title": "Article fin", "@sage/xtrem-stock/pages__stock_avc_justification_inquiry__itemValues____levels__columns__columns__stockUnit__symbol__title": "Symbole", "@sage/xtrem-stock/pages__stock_avc_justification_inquiry__itemValues____levels__columns__columns__stockUnit__symbol__title__2": "Symbole", "@sage/xtrem-stock/pages__stock_avc_justification_inquiry__itemValues____levels__columns__title___createStamp": "Date mouvement", "@sage/xtrem-stock/pages__stock_avc_justification_inquiry__itemValues____levels__columns__title__amountVariance": "<PERSON><PERSON><PERSON> mnt", "@sage/xtrem-stock/pages__stock_avc_justification_inquiry__itemValues____levels__columns__title__company__name": "Société", "@sage/xtrem-stock/pages__stock_avc_justification_inquiry__itemValues____levels__columns__title__cost": "Coût unitaire", "@sage/xtrem-stock/pages__stock_avc_justification_inquiry__itemValues____levels__columns__title__costVariance": "<PERSON><PERSON><PERSON> co<PERSON>t", "@sage/xtrem-stock/pages__stock_avc_justification_inquiry__itemValues____levels__columns__title__creationDate": "Date créée", "@sage/xtrem-stock/pages__stock_avc_justification_inquiry__itemValues____levels__columns__title__currency__symbol": "<PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_avc_justification_inquiry__itemValues____levels__columns__title__dateOfValuation": "Date de valorisation", "@sage/xtrem-stock/pages__stock_avc_justification_inquiry__itemValues____levels__columns__title__documentLine___constructor": "Type", "@sage/xtrem-stock/pages__stock_avc_justification_inquiry__itemValues____levels__columns__title__documentLine__documentNumber": "Document", "@sage/xtrem-stock/pages__stock_avc_justification_inquiry__itemValues____levels__columns__title__effectiveDate": "Date", "@sage/xtrem-stock/pages__stock_avc_justification_inquiry__itemValues____levels__columns__title__item__id": "Article", "@sage/xtrem-stock/pages__stock_avc_justification_inquiry__itemValues____levels__columns__title__item__name": "Nom", "@sage/xtrem-stock/pages__stock_avc_justification_inquiry__itemValues____levels__columns__title__movementAmount": "Mnt mouvement", "@sage/xtrem-stock/pages__stock_avc_justification_inquiry__itemValues____levels__columns__title__orderAmount": "Mnt ordre", "@sage/xtrem-stock/pages__stock_avc_justification_inquiry__itemValues____levels__columns__title__orderCost": "Coût ordre", "@sage/xtrem-stock/pages__stock_avc_justification_inquiry__itemValues____levels__columns__title__postingClass__name": "Classe comptabilisation", "@sage/xtrem-stock/pages__stock_avc_justification_inquiry__itemValues____levels__columns__title__quantityInStock": "Quantité", "@sage/xtrem-stock/pages__stock_avc_justification_inquiry__itemValues____levels__columns__title__quantityInStockUnit": "Quantité", "@sage/xtrem-stock/pages__stock_avc_justification_inquiry__itemValues____levels__columns__title__sequence": "Compteur", "@sage/xtrem-stock/pages__stock_avc_justification_inquiry__itemValues____levels__columns__title__site__name": "Site", "@sage/xtrem-stock/pages__stock_avc_justification_inquiry__itemValues____levels__columns__title__stockDetail__documentLine__documentNumber": "Document", "@sage/xtrem-stock/pages__stock_avc_justification_inquiry__itemValues____levels__columns__title__stockQuantity": "Quantité stock", "@sage/xtrem-stock/pages__stock_avc_justification_inquiry__itemValues____levels__columns__title__stockUnit__name": "Unité mesure", "@sage/xtrem-stock/pages__stock_avc_justification_inquiry__itemValues____levels__columns__title__stockUnit__symbol": "Unité stock", "@sage/xtrem-stock/pages__stock_avc_justification_inquiry__itemValues____levels__columns__title__stockValue": "Valeur stock", "@sage/xtrem-stock/pages__stock_avc_justification_inquiry__itemValues____levels__columns__title__stockValue__2": "Valeur stock", "@sage/xtrem-stock/pages__stock_avc_justification_inquiry__itemValues____levels__columns__title__type": "Type document", "@sage/xtrem-stock/pages__stock_avc_justification_inquiry__itemValues____levels__columns__title__unitCost": "Coût unitaire", "@sage/xtrem-stock/pages__stock_avc_justification_inquiry__itemValues____levels__columns__title__valuationMethod": "Nature dépense", "@sage/xtrem-stock/pages__stock_avc_justification_inquiry__itemValues____levels__columns__title__valuedCost": "Coût valorisé", "@sage/xtrem-stock/pages__stock_avc_justification_inquiry__itemValues____levels__columns__title__variance": "<PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_avc_justification_inquiry__mainSection____title": "Général", "@sage/xtrem-stock/pages__stock_avc_justification_inquiry__runStockValuation____title": "Exécuter", "@sage/xtrem-stock/pages__stock_avc_justification_inquiry__sites____lookupDialogTitle": "Sélectionner le site", "@sage/xtrem-stock/pages__stock_avc_justification_inquiry__sites____placeholder": "Sélectionner...", "@sage/xtrem-stock/pages__stock_avc_justification_inquiry__sites____title": "Site", "@sage/xtrem-stock/pages__stock_avc_justification_inquiry__valuationDate____title": "Date", "@sage/xtrem-stock/pages__stock_change____navigationPanel__listItem__description__title": "Description", "@sage/xtrem-stock/pages__stock_change____navigationPanel__listItem__effectiveDate__title": "Date", "@sage/xtrem-stock/pages__stock_change____navigationPanel__listItem__itemDescription__title": "Description de l'article", "@sage/xtrem-stock/pages__stock_change____navigationPanel__listItem__itemId__title": "Code article", "@sage/xtrem-stock/pages__stock_change____navigationPanel__listItem__line2__title": "Nom site", "@sage/xtrem-stock/pages__stock_change____navigationPanel__listItem__line2Right__title": "Date", "@sage/xtrem-stock/pages__stock_change____navigationPanel__listItem__line3__title": "Article", "@sage/xtrem-stock/pages__stock_change____navigationPanel__listItem__siteID__title": "Code site", "@sage/xtrem-stock/pages__stock_change____navigationPanel__listItem__stockSite__title": "Site", "@sage/xtrem-stock/pages__stock_change____navigationPanel__listItem__title__title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_change____objectTypePlural": "Changements de stock", "@sage/xtrem-stock/pages__stock_change____objectTypeSingular": "Changement de stock", "@sage/xtrem-stock/pages__stock_change____subtitle": "Changement de stock", "@sage/xtrem-stock/pages__stock_change____title": "Changement de stock", "@sage/xtrem-stock/pages__stock_change__addLine____title": "<PERSON><PERSON><PERSON> ligne", "@sage/xtrem-stock/pages__stock_change__availableQuantity____title": "Quantité disponible", "@sage/xtrem-stock/pages__stock_change__description____title": "Description", "@sage/xtrem-stock/pages__stock_change__effectiveDate____title": "Date", "@sage/xtrem-stock/pages__stock_change__initialStockBlock____title": "Stock initial", "@sage/xtrem-stock/pages__stock_change__item____columns__title__category__name": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_change__item____lookupDialogTitle": "Sélectionner l'article", "@sage/xtrem-stock/pages__stock_change__item____title": "Article", "@sage/xtrem-stock/pages__stock_change__lines____columns__columns__endingSerialNumber__id__title": "N° série fournisseur", "@sage/xtrem-stock/pages__stock_change__lines____columns__columns__endingSerialNumber__id__title__2": "Fournisseur", "@sage/xtrem-stock/pages__stock_change__lines____columns__columns__location__name__title": "Nom", "@sage/xtrem-stock/pages__stock_change__lines____columns__columns__location__name__title__2": "Code", "@sage/xtrem-stock/pages__stock_change__lines____columns__columns__startingSerialNumber__id__title": "N° série fournisseur", "@sage/xtrem-stock/pages__stock_change__lines____columns__columns__startingSerialNumber__id__title__2": "Fournisseur", "@sage/xtrem-stock/pages__stock_change__lines____columns__columns__stockStatus__name__title": "Nom", "@sage/xtrem-stock/pages__stock_change__lines____columns__columns__stockStatus__name__title__2": "Code", "@sage/xtrem-stock/pages__stock_change__lines____columns__lookupDialogTitle__startingSerialNumber__id": "Sélectionner le numéro de série début", "@sage/xtrem-stock/pages__stock_change__lines____columns__lookupDialogTitle__stockStatus": "Sé<PERSON><PERSON>ner une valeur qualité", "@sage/xtrem-stock/pages__stock_change__lines____columns__title__endingSerialNumber__id": "N° série fin", "@sage/xtrem-stock/pages__stock_change__lines____columns__title__location__name": "Emplacement", "@sage/xtrem-stock/pages__stock_change__lines____columns__title__quantityInStockUnit": "Quantité", "@sage/xtrem-stock/pages__stock_change__lines____columns__title__startingSerialNumber__id": "N° série début", "@sage/xtrem-stock/pages__stock_change__lines____columns__title__stockStatus": "Contrôle qualité", "@sage/xtrem-stock/pages__stock_change__lines____columns__title__stockStatus__name": "Statut", "@sage/xtrem-stock/pages__stock_change__lines____columns__title__stockTransactionStatus": "Statut stock", "@sage/xtrem-stock/pages__stock_change__lines____dropdownActions__title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_change__lines____title": "Changements", "@sage/xtrem-stock/pages__stock_change__location____columns__title__id": "Code", "@sage/xtrem-stock/pages__stock_change__location____columns__title__name": "Nom", "@sage/xtrem-stock/pages__stock_change__location____title": "Emplacement", "@sage/xtrem-stock/pages__stock_change__lot____columns__title__expirationDate": "Date péremption", "@sage/xtrem-stock/pages__stock_change__lot____title": "Lot", "@sage/xtrem-stock/pages__stock_change__mainSection____title": "Général", "@sage/xtrem-stock/pages__stock_change__number____title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_change__owner____title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_change__post____title": "Comptabi<PERSON>er", "@sage/xtrem-stock/pages__stock_change__postingError____title": "Erreur de comptabilisation", "@sage/xtrem-stock/pages__stock_change__saveStockChange____title": "Enregistrer", "@sage/xtrem-stock/pages__stock_change__status____title": "Statut", "@sage/xtrem-stock/pages__stock_change__stockRecord____columns__columns__location__name__title": "Zone", "@sage/xtrem-stock/pages__stock_change__stockRecord____columns__columns__site__name__title": "Gestion emplacements", "@sage/xtrem-stock/pages__stock_change__stockRecord____columns__title__item__name": "Article", "@sage/xtrem-stock/pages__stock_change__stockRecord____columns__title__location__name": "Emplacement", "@sage/xtrem-stock/pages__stock_change__stockRecord____columns__title__lot__id": "Lot", "@sage/xtrem-stock/pages__stock_change__stockRecord____columns__title__owner": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_change__stockRecord____columns__title__quantityInStockUnit": "Quantité", "@sage/xtrem-stock/pages__stock_change__stockRecord____columns__title__site__name": "Site", "@sage/xtrem-stock/pages__stock_change__stockRecord____columns__title__status__name": "Statut", "@sage/xtrem-stock/pages__stock_change__stockRecord____columns__title__totalAllocated": "Allou<PERSON>", "@sage/xtrem-stock/pages__stock_change__stockRecord____title": "Stock", "@sage/xtrem-stock/pages__stock_change__stockSite____columns__title__isLocationManaged": "Gestion emplacements", "@sage/xtrem-stock/pages__stock_change__stockSite____lookupDialogTitle": "Sélectionner le site", "@sage/xtrem-stock/pages__stock_change__stockSite____title": "Site", "@sage/xtrem-stock/pages__stock_change__stockStatus____columns__title__id": "Code", "@sage/xtrem-stock/pages__stock_change__stockStatus____columns__title__name": "Nom", "@sage/xtrem-stock/pages__stock_change__stockStatus____lookupDialogTitle": "Sé<PERSON><PERSON>ner une valeur qualité", "@sage/xtrem-stock/pages__stock_change__stockStatus____title": "Contrôle qualité", "@sage/xtrem-stock/pages__stock_change__stockTransactionStatus____title": "Statut de stock", "@sage/xtrem-stock/pages__stock_change__totalQuantity____title": "Quantité totale", "@sage/xtrem-stock/pages__stock_change_available_error": "Numéros de série disponibles dans cette borne : {{available}}", "@sage/xtrem-stock/pages__stock_change_duplicate_error": "Le numéro de série est déjà compris dans une autre borne.", "@sage/xtrem-stock/pages__stock_change_duplicate_range_error": "Un des numéros de série est déjà compris dans une autre borne.", "@sage/xtrem-stock/pages__stock_change_missing_from_serial_number": "<PERSON><PERSON> de<PERSON> renseigner un 'N° série début'.", "@sage/xtrem-stock/pages__stock_change_zero_quantity_error": "Renseignez une quantité supérieure à 0.", "@sage/xtrem-stock/pages__stock_count____navigationPanel__listItem__counter__title": "Compteur", "@sage/xtrem-stock/pages__stock_count____navigationPanel__listItem__line2__title": "Description", "@sage/xtrem-stock/pages__stock_count____navigationPanel__listItem__line2Right__title": "Date", "@sage/xtrem-stock/pages__stock_count____navigationPanel__listItem__stockSite__title": "Site", "@sage/xtrem-stock/pages__stock_count____navigationPanel__listItem__stockTransactionStatus__title": "Statut du stock", "@sage/xtrem-stock/pages__stock_count____navigationPanel__listItem__title__title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_count____navigationPanel__listItem__titleRight__title": "Statut", "@sage/xtrem-stock/pages__stock_count____navigationPanel__optionsMenu__title": "", "@sage/xtrem-stock/pages__stock_count____objectTypePlural": "Inventaires", "@sage/xtrem-stock/pages__stock_count____objectTypeSingular": "Inventaire", "@sage/xtrem-stock/pages__stock_count____title": "Inventaire", "@sage/xtrem-stock/pages__stock_count__categories____title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_count__confirm_zero_quantity_result": "<PERSON><PERSON><PERSON> mises à jour : {{recordsUpdated}}.", "@sage/xtrem-stock/pages__stock_count__confirmZeroQuantity____title": "Confirmer quantités nulles", "@sage/xtrem-stock/pages__stock_count__counted_quantity_can_not_be_greater_than_quantity_in_stock": "La quantité comptée ne peut pas excéder la quantité de stock pour un article sérialisé.", "@sage/xtrem-stock/pages__stock_count__counter____title": "Compteur", "@sage/xtrem-stock/pages__stock_count__creation": "L'inventaire a été créé.", "@sage/xtrem-stock/pages__stock_count__criteriaBlock____title": "Critères de sélection", "@sage/xtrem-stock/pages__stock_count__defaultDimension____title": "Paramétrer les sections par défaut", "@sage/xtrem-stock/pages__stock_count__description____title": "Description", "@sage/xtrem-stock/pages__stock_count__duplication_among_added_lines": "Modifier ou supprimer les lignes en double.", "@sage/xtrem-stock/pages__stock_count__effectiveDate____title": "Date", "@sage/xtrem-stock/pages__stock_count__fromItem____title": "Article début", "@sage/xtrem-stock/pages__stock_count__goToSysNotificationPage____title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_count__hasStockRecords____title": "En stock uniquement", "@sage/xtrem-stock/pages__stock_count__lastCountDate____title": "Articles non comptés depuis", "@sage/xtrem-stock/pages__stock_count__lines____columns__columns__item__name__columns__title__symbol": "Symbole", "@sage/xtrem-stock/pages__stock_count__lines____columns__columns__location__name__title": "Nom", "@sage/xtrem-stock/pages__stock_count__lines____columns__columns__location__name__title__2": "Code", "@sage/xtrem-stock/pages__stock_count__lines____columns__columns__location__name__title__3": "Type", "@sage/xtrem-stock/pages__stock_count__lines____columns__columns__lotSelection__id__columns__title__id": "Code", "@sage/xtrem-stock/pages__stock_count__lines____columns__columns__lotSelection__id__columns__title__name": "Nom", "@sage/xtrem-stock/pages__stock_count__lines____columns__columns__stockDetail__stockDetailLot__lotId__id__title": "Code", "@sage/xtrem-stock/pages__stock_count__lines____columns__columns__stockDetail__stockDetailLot__lotId__id__title__2": "Sous-lot", "@sage/xtrem-stock/pages__stock_count__lines____columns__columns__stockDetail__stockDetailLot__lotId__id__title__3": "Date péremption", "@sage/xtrem-stock/pages__stock_count__lines____columns__columns__stockDetail__stockDetailLot__lotId__id__title__4": "N° lot fournisseur", "@sage/xtrem-stock/pages__stock_count__lines____columns__columns__stockDetail__stockDetailLot__lotId__id__title__5": "Article", "@sage/xtrem-stock/pages__stock_count__lines____columns__columns__stockStatus__name__title": "Nom", "@sage/xtrem-stock/pages__stock_count__lines____columns__columns__stockStatus__name__title__2": "Code", "@sage/xtrem-stock/pages__stock_count__lines____columns__columns__stockStatus__name__title__3": "Description", "@sage/xtrem-stock/pages__stock_count__lines____columns__lookupDialogTitle__stockDetail__stockDetailLot__lotId__id": "Sélectionner le lot", "@sage/xtrem-stock/pages__stock_count__lines____columns__postfix__quantityVariancePercentage": "%", "@sage/xtrem-stock/pages__stock_count__lines____columns__title___id": "", "@sage/xtrem-stock/pages__stock_count__lines____columns__title__countedQuantityInStockUnit": "Quantité comptée", "@sage/xtrem-stock/pages__stock_count__lines____columns__title__countedSerialNumberPercentage": "N° série", "@sage/xtrem-stock/pages__stock_count__lines____columns__title__expirationDate": "Date de péremption", "@sage/xtrem-stock/pages__stock_count__lines____columns__title__item__description": "Descr. article", "@sage/xtrem-stock/pages__stock_count__lines____columns__title__item__id": "Code article", "@sage/xtrem-stock/pages__stock_count__lines____columns__title__item__name": "Article", "@sage/xtrem-stock/pages__stock_count__lines____columns__title__location__locationZone__name": "Zone", "@sage/xtrem-stock/pages__stock_count__lines____columns__title__location__name": "Emplacement", "@sage/xtrem-stock/pages__stock_count__lines____columns__title__lot__sublot": "Sous-lot", "@sage/xtrem-stock/pages__stock_count__lines____columns__title__newLineOrderCost": "Coût unitaire", "@sage/xtrem-stock/pages__stock_count__lines____columns__title__owner": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_count__lines____columns__title__quantityInStockUnit": "Quantité stock", "@sage/xtrem-stock/pages__stock_count__lines____columns__title__quantityVariance": "<PERSON>cart de quantité", "@sage/xtrem-stock/pages__stock_count__lines____columns__title__quantityVariancePercentage": "% d'écart de quantité", "@sage/xtrem-stock/pages__stock_count__lines____columns__title__status": "Statut d'inventaire", "@sage/xtrem-stock/pages__stock_count__lines____columns__title__stockDetail__stockDetailLot__lotId__id": "Lot", "@sage/xtrem-stock/pages__stock_count__lines____columns__title__stockStatus__name": "Contrôle qualité", "@sage/xtrem-stock/pages__stock_count__lines____columns__title__stockTransactionStatus": "Statut stock", "@sage/xtrem-stock/pages__stock_count__lines____columns__title__sublot": "N° sous-lot", "@sage/xtrem-stock/pages__stock_count__lines____columns__title__supplierLot": "N° lot fournisseur", "@sage/xtrem-stock/pages__stock_count__lines____dropdownActions__title": "<PERSON><PERSON>er", "@sage/xtrem-stock/pages__stock_count__lines____dropdownActions__title__2": "Exclure", "@sage/xtrem-stock/pages__stock_count__lines____dropdownActions__title__3": "Confirmer la quantité nulle", "@sage/xtrem-stock/pages__stock_count__lines____dropdownActions__title__4": "Sections", "@sage/xtrem-stock/pages__stock_count__lines____dropdownActions__title__5": "Numéros de série", "@sage/xtrem-stock/pages__stock_count__lines____dropdownActions__title__6": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_count__lines____inlineActions__title": "Ouv<PERSON>r volet des lignes", "@sage/xtrem-stock/pages__stock_count__lines____title": "Lignes de documents", "@sage/xtrem-stock/pages__stock_count__linesSection____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_count__locations____title": "Emplacement", "@sage/xtrem-stock/pages__stock_count__mainSection____title": "Général", "@sage/xtrem-stock/pages__stock_count__number____title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_count__order_cost_should_be_entered": "Vérifiez votre coût unitaire. Une valeur nulle peut avoir un impact sur la valeur du stock.", "@sage/xtrem-stock/pages__stock_count__post____title": "Comptabiliser le stock", "@sage/xtrem-stock/pages__stock_count__postingDetails____columns__title__financeIntegrationAppRecordId": "Référence intégration comptable", "@sage/xtrem-stock/pages__stock_count__postingDetails____columns__title__postingStatus": "Statut", "@sage/xtrem-stock/pages__stock_count__postingDetails____columns__title__targetDocumentNumber": "N° doc", "@sage/xtrem-stock/pages__stock_count__postingDetails____columns__title__targetDocumentType": "Type doc", "@sage/xtrem-stock/pages__stock_count__postingDetails____title": "Résultats", "@sage/xtrem-stock/pages__stock_count__postingMessageBlock____title": "<PERSON><PERSON><PERSON> de l'erreur", "@sage/xtrem-stock/pages__stock_count__postingSection____title": "Comptabilisation", "@sage/xtrem-stock/pages__stock_count__print____title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_count__repost____title": "Recomptabiliser", "@sage/xtrem-stock/pages__stock_count__repost_errors": "Erreurs lors de la recomptabilisation :", "@sage/xtrem-stock/pages__stock_count__saveStockCount____title": "Enregistrer", "@sage/xtrem-stock/pages__stock_count__selected_serial_number_quantity_too_high": "La quantité de numéros de série sélectionnés ({{selectedQuantity}}) ne doit pas dépasser la quantité comptée ({{countedQuantity}}).", "@sage/xtrem-stock/pages__stock_count__showQuantityInStock____title": "Afficher quantité en stock", "@sage/xtrem-stock/pages__stock_count__sidebar_tab_title_information": "Informations", "@sage/xtrem-stock/pages__stock_count__start____title": "D<PERSON>but", "@sage/xtrem-stock/pages__stock_count__status____title": "Statut", "@sage/xtrem-stock/pages__stock_count__stockSite____title": "Site", "@sage/xtrem-stock/pages__stock_count__stockTransactionStatus____title": "Statut du stock", "@sage/xtrem-stock/pages__stock_count__toItem____title": "Article fin", "@sage/xtrem-stock/pages__stock_count__zones____title": "Zone", "@sage/xtrem-stock/pages__stock_count_creation____navigationPanel__listItem__line2__title": "Site", "@sage/xtrem-stock/pages__stock_count_creation____navigationPanel__optionsMenu__title": "Brouillon", "@sage/xtrem-stock/pages__stock_count_creation____title": "Création d'inventaire", "@sage/xtrem-stock/pages__stock_count_creation__categories____columns__title__id": "Code", "@sage/xtrem-stock/pages__stock_count_creation__categories____columns__title__name": "Nom", "@sage/xtrem-stock/pages__stock_count_creation__categories____lookupDialogTitle": "Sélectionner la catégorie", "@sage/xtrem-stock/pages__stock_count_creation__categories____placeholder": "Sélectionner...", "@sage/xtrem-stock/pages__stock_count_creation__categories____title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_count_creation__createStockCount____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_count_creation__criteriaBlock____title": "Critères", "@sage/xtrem-stock/pages__stock_count_creation__description____title": "Description", "@sage/xtrem-stock/pages__stock_count_creation__effectiveDate____title": "Date", "@sage/xtrem-stock/pages__stock_count_creation__fromItem____columns__title__description": "Description", "@sage/xtrem-stock/pages__stock_count_creation__fromItem____columns__title__id": "Code", "@sage/xtrem-stock/pages__stock_count_creation__fromItem____columns__title__name": "Nom", "@sage/xtrem-stock/pages__stock_count_creation__fromItem____lookupDialogTitle": "Sélectionner l'article", "@sage/xtrem-stock/pages__stock_count_creation__fromItem____placeholder": "Sélectionner...", "@sage/xtrem-stock/pages__stock_count_creation__fromItem____title": "Article début", "@sage/xtrem-stock/pages__stock_count_creation__hasStockRecords____title": "En stock uniquement", "@sage/xtrem-stock/pages__stock_count_creation__invalid_item_range": "Renseigner un 'Article fin' supérieur à l''Article début'.", "@sage/xtrem-stock/pages__stock_count_creation__itemSites____levels__columns__title__hasStockRecords": "Fiche de stock", "@sage/xtrem-stock/pages__stock_count_creation__itemSites____levels__columns__title__item__description": "Descr. article", "@sage/xtrem-stock/pages__stock_count_creation__itemSites____levels__columns__title__item__id": "Code article", "@sage/xtrem-stock/pages__stock_count_creation__itemSites____levels__columns__title__item__name": "Article", "@sage/xtrem-stock/pages__stock_count_creation__itemSites____levels__columns__title__location__locationZone__name": "Zone", "@sage/xtrem-stock/pages__stock_count_creation__itemSites____levels__columns__title__location__name": "Emplacement", "@sage/xtrem-stock/pages__stock_count_creation__itemSites____levels__columns__title__lot__id": "Lot", "@sage/xtrem-stock/pages__stock_count_creation__itemSites____levels__columns__title__lot__sublot": "Sous-lot", "@sage/xtrem-stock/pages__stock_count_creation__itemSites____levels__columns__title__owner": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_count_creation__itemSites____levels__columns__title__quantityInStockUnit": "Quantité", "@sage/xtrem-stock/pages__stock_count_creation__itemSites____levels__columns__title__status__name": "Statut", "@sage/xtrem-stock/pages__stock_count_creation__lastCountDate____title": "Articles non comptés depuis", "@sage/xtrem-stock/pages__stock_count_creation__locations____columns__title__id": "Code", "@sage/xtrem-stock/pages__stock_count_creation__locations____columns__title__name": "Nom", "@sage/xtrem-stock/pages__stock_count_creation__locations____lookupDialogTitle": "Sélectionner l'emplacement", "@sage/xtrem-stock/pages__stock_count_creation__locations____placeholder": "Sélectionner...", "@sage/xtrem-stock/pages__stock_count_creation__locations____title": "Emplacement", "@sage/xtrem-stock/pages__stock_count_creation__mainSection____title": "Général", "@sage/xtrem-stock/pages__stock_count_creation__selectAllCheckbox____title": "<PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_count_creation__stockCountBlock____title": "Inventaire", "@sage/xtrem-stock/pages__stock_count_creation__stockSite____lookupDialogTitle": "Sélectionner le site", "@sage/xtrem-stock/pages__stock_count_creation__stockSite____placeholder": "Sélectionner...", "@sage/xtrem-stock/pages__stock_count_creation__stockSite____title": "Site", "@sage/xtrem-stock/pages__stock_count_creation__toItem____columns__title__description": "Description", "@sage/xtrem-stock/pages__stock_count_creation__toItem____columns__title__id": "Code", "@sage/xtrem-stock/pages__stock_count_creation__toItem____columns__title__name": "Nom", "@sage/xtrem-stock/pages__stock_count_creation__toItem____lookupDialogTitle": "Sélectionner l'article", "@sage/xtrem-stock/pages__stock_count_creation__toItem____placeholder": "Sélectionner...", "@sage/xtrem-stock/pages__stock_count_creation__toItem____title": "Article fin", "@sage/xtrem-stock/pages__stock_count_creation__zones____columns__title__id": "Code", "@sage/xtrem-stock/pages__stock_count_creation__zones____columns__title__name": "Nom", "@sage/xtrem-stock/pages__stock_count_creation__zones____columns__title__zoneType": "Type", "@sage/xtrem-stock/pages__stock_count_creation__zones____lookupDialogTitle": "Sélectionner la zone de stockage", "@sage/xtrem-stock/pages__stock_count_creation__zones____placeholder": "Sélectionner...", "@sage/xtrem-stock/pages__stock_count_creation__zones____title": "Zone", "@sage/xtrem-stock/pages__stock_count_line__not_add_a_line_matching_with_a_stock_record": "<PERSON><PERSON> <PERSON><PERSON> modifier les détails de cette ligne d'inventaire car elle duplique des informations de stock existantes. Article : {{item}}, site : {{site}}, emplacement : {{location}}, lot : {{lot}}, statut : {{status}}, unité de stock : {{stockUnit}}, propriétaire : {{owner}}.", "@sage/xtrem-stock/pages__stock_count_serial_numbers_panel____title": "Numéros de série", "@sage/xtrem-stock/pages__stock_count_serial_numbers_panel__addSerialNumberRange____title": "<PERSON><PERSON><PERSON> ligne", "@sage/xtrem-stock/pages__stock_count_serial_numbers_panel__cancel____title": "Annuler", "@sage/xtrem-stock/pages__stock_count_serial_numbers_panel__duplicate_error": "Le numéro de série est déjà compris dans une autre borne.", "@sage/xtrem-stock/pages__stock_count_serial_numbers_panel__duplicate_range_error": "Un des numéros de série est déjà compris dans une autre borne.", "@sage/xtrem-stock/pages__stock_count_serial_numbers_panel__mainBlock____title": "Général", "@sage/xtrem-stock/pages__stock_count_serial_numbers_panel__mainSection____title": "Général", "@sage/xtrem-stock/pages__stock_count_serial_numbers_panel__ok____title": "OK", "@sage/xtrem-stock/pages__stock_count_serial_numbers_panel__quantityToIssue____title": "Numéros de série à émettre", "@sage/xtrem-stock/pages__stock_count_serial_numbers_panel__remainingQuantity____title": "Quantité restante", "@sage/xtrem-stock/pages__stock_count_serial_numbers_panel__requiredQuantity____title": "Quantité demandée", "@sage/xtrem-stock/pages__stock_count_serial_numbers_panel__selected_quantity_too_high": "La quantité de numéros de série sélectionnés ({{selectedQuantity}}) ne doit pas dépasser la quantité comptée ({{countedQuantity}}).", "@sage/xtrem-stock/pages__stock_count_serial_numbers_panel__selectedQuantity____title": "Quantité sélectionnée", "@sage/xtrem-stock/pages__stock_count_serial_numbers_panel__serialNumberRanges____columns__columns__endingSerialNumber__serialNumber.id__title": "Code", "@sage/xtrem-stock/pages__stock_count_serial_numbers_panel__serialNumberRanges____columns__columns__startingSerialNumber__serialNumber__id__title": "Code", "@sage/xtrem-stock/pages__stock_count_serial_numbers_panel__serialNumberRanges____columns__lookupDialogTitle__startingSerialNumber__serialNumber__id": "Sélectionner numéro de série début", "@sage/xtrem-stock/pages__stock_count_serial_numbers_panel__serialNumberRanges____columns__title__endingSerialNumber__serialNumber.id": "N° série fin", "@sage/xtrem-stock/pages__stock_count_serial_numbers_panel__serialNumberRanges____columns__title__numericEnd": "N° série fin", "@sage/xtrem-stock/pages__stock_count_serial_numbers_panel__serialNumberRanges____columns__title__numericStart": "N° série début", "@sage/xtrem-stock/pages__stock_count_serial_numbers_panel__serialNumberRanges____columns__title__originalStartId": "N° série début origine", "@sage/xtrem-stock/pages__stock_count_serial_numbers_panel__serialNumberRanges____columns__title__quantity": "Quantité", "@sage/xtrem-stock/pages__stock_count_serial_numbers_panel__serialNumberRanges____columns__title__startingSerialNumber__serialNumber__id": "N° série début", "@sage/xtrem-stock/pages__stock_count_serial_numbers_panel__serialNumberRanges____dropdownActions__title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_count_serial_numbers_panel__serialNumberRanges____title": "Numéros de série", "@sage/xtrem-stock/pages__stock_detailed_inquiry____navigationPanel__dropdownActions__title": "Allocations", "@sage/xtrem-stock/pages__stock_detailed_inquiry____navigationPanel__dropdownActions__title__2": "Informations de numéro de série", "@sage/xtrem-stock/pages__stock_detailed_inquiry____navigationPanel__listItem__activeQuantityInStockUnit__title": "Quantité active", "@sage/xtrem-stock/pages__stock_detailed_inquiry____navigationPanel__listItem__allocatedQuantity__title": "Quantité allouée", "@sage/xtrem-stock/pages__stock_detailed_inquiry____navigationPanel__listItem__availableQuantity__title": "Quantité disponible", "@sage/xtrem-stock/pages__stock_detailed_inquiry____navigationPanel__listItem__creationDate__title": "Date de création", "@sage/xtrem-stock/pages__stock_detailed_inquiry____navigationPanel__listItem__expirationDate__title": "Date de péremption", "@sage/xtrem-stock/pages__stock_detailed_inquiry____navigationPanel__listItem__inTransitQuantityInStockUnit__title": "Quantité en transit", "@sage/xtrem-stock/pages__stock_detailed_inquiry____navigationPanel__listItem__item__title": "Article", "@sage/xtrem-stock/pages__stock_detailed_inquiry____navigationPanel__listItem__itemDescription__title": "Description de l'article", "@sage/xtrem-stock/pages__stock_detailed_inquiry____navigationPanel__listItem__itemId__title": "Code article", "@sage/xtrem-stock/pages__stock_detailed_inquiry____navigationPanel__listItem__line2__title": "Lot", "@sage/xtrem-stock/pages__stock_detailed_inquiry____navigationPanel__listItem__location__title": "Emplacement", "@sage/xtrem-stock/pages__stock_detailed_inquiry____navigationPanel__listItem__locationId__title": "Code emplacement", "@sage/xtrem-stock/pages__stock_detailed_inquiry____navigationPanel__listItem__locationName__title": "Emplacement", "@sage/xtrem-stock/pages__stock_detailed_inquiry____navigationPanel__listItem__locationZone__title": "Zone emplacement", "@sage/xtrem-stock/pages__stock_detailed_inquiry____navigationPanel__listItem__lot__title": "Lot", "@sage/xtrem-stock/pages__stock_detailed_inquiry____navigationPanel__listItem__onHandQuantityInStockUnit__title": "Quantité physique", "@sage/xtrem-stock/pages__stock_detailed_inquiry____navigationPanel__listItem__owner__title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_detailed_inquiry____navigationPanel__listItem__siteId__title": "Code site", "@sage/xtrem-stock/pages__stock_detailed_inquiry____navigationPanel__listItem__siteName__title": "Site", "@sage/xtrem-stock/pages__stock_detailed_inquiry____navigationPanel__listItem__status__title": "Contrôle qualité", "@sage/xtrem-stock/pages__stock_detailed_inquiry____navigationPanel__listItem__stockUnit__title": "Unité de mesure", "@sage/xtrem-stock/pages__stock_detailed_inquiry____navigationPanel__listItem__sublot__title": "Sous-lot", "@sage/xtrem-stock/pages__stock_detailed_inquiry____navigationPanel__listItem__supplierLot__title": "Numéro lot fournisseur", "@sage/xtrem-stock/pages__stock_detailed_inquiry____navigationPanel__listItem__title__columns__title": "Nom", "@sage/xtrem-stock/pages__stock_detailed_inquiry____navigationPanel__listItem__title__columns__title__2": "Code", "@sage/xtrem-stock/pages__stock_detailed_inquiry____navigationPanel__listItem__title__columns__title__3": "Description", "@sage/xtrem-stock/pages__stock_detailed_inquiry____navigationPanel__listItem__title__title": "Article", "@sage/xtrem-stock/pages__stock_detailed_inquiry____navigationPanel__listItem__titleRight__title": "Site", "@sage/xtrem-stock/pages__stock_detailed_inquiry____navigationPanel__optionsMenu__title": "Principal", "@sage/xtrem-stock/pages__stock_detailed_inquiry____navigationPanel__optionsMenu__title__2": "Y compris en transit", "@sage/xtrem-stock/pages__stock_detailed_inquiry____title": "Consultation détaillée de stock", "@sage/xtrem-stock/pages__stock_detailed_inquiry___id____title": "Code", "@sage/xtrem-stock/pages__stock_detailed_inquiry__activeQuantityInStockUnit____title": "Quantité active", "@sage/xtrem-stock/pages__stock_detailed_inquiry__location____columns__title__id": "Code", "@sage/xtrem-stock/pages__stock_detailed_inquiry__location____columns__title__locationType__name": "Type", "@sage/xtrem-stock/pages__stock_detailed_inquiry__location____lookupDialogTitle": "Sélectionner un emplacement.", "@sage/xtrem-stock/pages__stock_detailed_inquiry__location____title": "Emplacement", "@sage/xtrem-stock/pages__stock_detailed_inquiry__lot____columns__title__item__description": "Article", "@sage/xtrem-stock/pages__stock_detailed_inquiry__mainBlock____title": "Informations de stock", "@sage/xtrem-stock/pages__stock_detailed_inquiry__owner____title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_detailed_inquiry__serialNumberLines____columns__title__supplierSerialNumber": "Numéro de série fournisseur", "@sage/xtrem-stock/pages__stock_detailed_inquiry__stockUnit____columns__title__id": "Code", "@sage/xtrem-stock/pages__stock_detailed_inquiry__stockUnit____columns__title__name": "Nom", "@sage/xtrem-stock/pages__stock_detailed_inquiry__stockUnit____lookupDialogTitle": "Sélectionner une unité de mesure.", "@sage/xtrem-stock/pages__stock_detailed_inquiry__stockUnit____placeholder": "Sélectionner...", "@sage/xtrem-stock/pages__stock_detailed_inquiry__stockUnit____title": "Unité de mesure", "@sage/xtrem-stock/pages__stock_fifo_valuation_inquiry____title": "Couche de coût FIFO", "@sage/xtrem-stock/pages__stock_fifo_valuation_inquiry__criteriaBlock____title": "Critères", "@sage/xtrem-stock/pages__stock_fifo_valuation_inquiry__fifoValuationTiers____columns__title__amount": "<PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_fifo_valuation_inquiry__fifoValuationTiers____columns__title__createDate": "Date créée", "@sage/xtrem-stock/pages__stock_fifo_valuation_inquiry__fifoValuationTiers____columns__title__effectiveDate": "Date réception", "@sage/xtrem-stock/pages__stock_fifo_valuation_inquiry__fifoValuationTiers____columns__title__nonAbsorbedAmount": "Montant non-absorbé", "@sage/xtrem-stock/pages__stock_fifo_valuation_inquiry__fifoValuationTiers____columns__title__receiptDocumentLine__documentNumber": "Document de réception", "@sage/xtrem-stock/pages__stock_fifo_valuation_inquiry__fifoValuationTiers____columns__title__receiptQuantity": "Qté origine", "@sage/xtrem-stock/pages__stock_fifo_valuation_inquiry__fifoValuationTiers____columns__title__remainingQuantity": "Quantité", "@sage/xtrem-stock/pages__stock_fifo_valuation_inquiry__fifoValuationTiers____columns__title__sequence": "Compteur", "@sage/xtrem-stock/pages__stock_fifo_valuation_inquiry__fifoValuationTiers____columns__title__unitCost": "Coût unitaire", "@sage/xtrem-stock/pages__stock_fifo_valuation_inquiry__fifoValuationTiers____title": "Résultats", "@sage/xtrem-stock/pages__stock_fifo_valuation_inquiry__fromDate____title": "Date début", "@sage/xtrem-stock/pages__stock_fifo_valuation_inquiry__item____columns__title__category__name": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_fifo_valuation_inquiry__item____lookupDialogTitle": "Sélectionner l'article", "@sage/xtrem-stock/pages__stock_fifo_valuation_inquiry__item____placeholder": "Sélectionner l'article", "@sage/xtrem-stock/pages__stock_fifo_valuation_inquiry__item____title": "Article", "@sage/xtrem-stock/pages__stock_fifo_valuation_inquiry__mainSection____title": "Général", "@sage/xtrem-stock/pages__stock_fifo_valuation_inquiry__site____columns__title__id": "Code", "@sage/xtrem-stock/pages__stock_fifo_valuation_inquiry__site____columns__title__name": "Nom", "@sage/xtrem-stock/pages__stock_fifo_valuation_inquiry__site____lookupDialogTitle": "Sélectionner le site", "@sage/xtrem-stock/pages__stock_fifo_valuation_inquiry__site____placeholder": "Sélectionner le site", "@sage/xtrem-stock/pages__stock_fifo_valuation_inquiry__site____title": "Site", "@sage/xtrem-stock/pages__stock_fifo_valuation_inquiry__toDate____title": "Taxe début", "@sage/xtrem-stock/pages__Stock_has__allocated_quantity_the_counted_cannot_be_less_than_allocated_quantity": "Des allocations sont associées au stock. La quantité comptée ne peut pas être inférieure à la quantité allouée.", "@sage/xtrem-stock/pages__stock_inquiry____title": "Consultation de stock", "@sage/xtrem-stock/pages__stock_inquiry__criteriaBlock____title": "Critères", "@sage/xtrem-stock/pages__stock_inquiry__item____columns__title__category__name": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_inquiry__item____lookupDialogTitle": "Sélectionner l'article", "@sage/xtrem-stock/pages__stock_inquiry__item____title": "Article", "@sage/xtrem-stock/pages__stock_inquiry__itemFrom____columns__title__category__name": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_inquiry__itemFrom____columns__title__description": "Description", "@sage/xtrem-stock/pages__stock_inquiry__itemFrom____columns__title__id": "Article", "@sage/xtrem-stock/pages__stock_inquiry__itemFrom____columns__title__name": "Nom", "@sage/xtrem-stock/pages__stock_inquiry__itemFrom____lookupDialogTitle": "Sélectionner l'article", "@sage/xtrem-stock/pages__stock_inquiry__itemFrom____placeholder": "Articles début", "@sage/xtrem-stock/pages__stock_inquiry__itemFrom____title": "Article début", "@sage/xtrem-stock/pages__stock_inquiry__itemTo____columns__title__category__name": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_inquiry__itemTo____columns__title__description": "Description", "@sage/xtrem-stock/pages__stock_inquiry__itemTo____columns__title__id": "Article", "@sage/xtrem-stock/pages__stock_inquiry__itemTo____columns__title__name": "Nom", "@sage/xtrem-stock/pages__stock_inquiry__itemTo____lookupDialogTitle": "Sélectionner l'article", "@sage/xtrem-stock/pages__stock_inquiry__itemTo____placeholder": "Articles fin", "@sage/xtrem-stock/pages__stock_inquiry__itemTo____title": "Article fin", "@sage/xtrem-stock/pages__stock_inquiry__mainSection____title": "Général", "@sage/xtrem-stock/pages__stock_inquiry__resultsSection____title": "Résultats", "@sage/xtrem-stock/pages__stock_inquiry__search": "<PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_inquiry__site____columns__title__id": "Code", "@sage/xtrem-stock/pages__stock_inquiry__site____columns__title__name": "Nom", "@sage/xtrem-stock/pages__stock_inquiry__site____lookupDialogTitle": "Sélectionner le site", "@sage/xtrem-stock/pages__stock_inquiry__site____placeholder": "Sélectionner...", "@sage/xtrem-stock/pages__stock_inquiry__site____title": "Site", "@sage/xtrem-stock/pages__stock_inquiry__stock____columns__title__activeQuantityInStockUnit": "Quantité", "@sage/xtrem-stock/pages__stock_inquiry__stock____columns__title__item__id": "Article", "@sage/xtrem-stock/pages__stock_inquiry__stock____columns__title__item__name": "Nom", "@sage/xtrem-stock/pages__stock_inquiry__stock____columns__title__site__name": "Site", "@sage/xtrem-stock/pages__stock_inquiry__stock____columns__title__stockUnit__name": "Unité mesure", "@sage/xtrem-stock/pages__stock_inquiry__stock____mobileCard__line2__title": "Nom", "@sage/xtrem-stock/pages__stock_inquiry__stock____mobileCard__line2Right__title": "Unité de mesure", "@sage/xtrem-stock/pages__stock_inquiry__stock____mobileCard__title__title": "Nom", "@sage/xtrem-stock/pages__stock_inquiry__stock____mobileCard__titleRight__title": "Quantité", "@sage/xtrem-stock/pages__stock_inquiry__stock____title": "Résultats", "@sage/xtrem-stock/pages__stock_inquiry__stockStatus____columns__title__id": "Code", "@sage/xtrem-stock/pages__stock_inquiry__stockStatus____columns__title__name": "Nom", "@sage/xtrem-stock/pages__stock_inquiry__stockStatus____lookupDialogTitle": "Sélectionner le statut", "@sage/xtrem-stock/pages__stock_inquiry__stockStatus____placeholder": "Sélectionner...", "@sage/xtrem-stock/pages__stock_inquiry__stockStatus____title": "Statut de stock", "@sage/xtrem-stock/pages__stock_inquiry_bound____navigationPanel__listItem__accepted__title": "Quantité acceptée", "@sage/xtrem-stock/pages__stock_inquiry_bound____navigationPanel__listItem__allocated__title": "Quantité allouée", "@sage/xtrem-stock/pages__stock_inquiry_bound____navigationPanel__listItem__image__title": "Image", "@sage/xtrem-stock/pages__stock_inquiry_bound____navigationPanel__listItem__inStock__title": "Quantité physique", "@sage/xtrem-stock/pages__stock_inquiry_bound____navigationPanel__listItem__itemCategory__title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_inquiry_bound____navigationPanel__listItem__itemDescription__title": "Description article", "@sage/xtrem-stock/pages__stock_inquiry_bound____navigationPanel__listItem__itemIdReference__title": "Code article", "@sage/xtrem-stock/pages__stock_inquiry_bound____navigationPanel__listItem__itemName__title": "Article", "@sage/xtrem-stock/pages__stock_inquiry_bound____navigationPanel__listItem__qualityQuntity__title": "Quantité en contrôle qualité", "@sage/xtrem-stock/pages__stock_inquiry_bound____navigationPanel__listItem__rejected__title": "Quantité rejetée", "@sage/xtrem-stock/pages__stock_inquiry_bound____navigationPanel__listItem__site__title": "Site", "@sage/xtrem-stock/pages__stock_inquiry_bound____navigationPanel__listItem__stockValue__title": "Valeur du stock", "@sage/xtrem-stock/pages__stock_inquiry_bound____navigationPanel__listItem__title__title": "Nom", "@sage/xtrem-stock/pages__stock_inquiry_bound____title": "Consultation de stock", "@sage/xtrem-stock/pages__stock_issue____navigationPanel__dropdownActions__title": "Définir sections", "@sage/xtrem-stock/pages__stock_issue____navigationPanel__dropdownActions__title__2": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_issue____navigationPanel__listItem__description__title": "Description", "@sage/xtrem-stock/pages__stock_issue____navigationPanel__listItem__effectiveDate__title": "Date", "@sage/xtrem-stock/pages__stock_issue____navigationPanel__listItem__line2__title": "Site", "@sage/xtrem-stock/pages__stock_issue____navigationPanel__listItem__line2Right__title": "Date", "@sage/xtrem-stock/pages__stock_issue____navigationPanel__listItem__reasonCode__title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_issue____navigationPanel__listItem__status__title": "Statut", "@sage/xtrem-stock/pages__stock_issue____navigationPanel__listItem__stockSite__title": "Site", "@sage/xtrem-stock/pages__stock_issue____navigationPanel__listItem__stockTransactionStatus__title": "Statut de stock", "@sage/xtrem-stock/pages__stock_issue____navigationPanel__listItem__title__title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_issue____navigationPanel__listItem__titleRight__title": "Statut", "@sage/xtrem-stock/pages__stock_issue____navigationPanel__optionsMenu__title": "Tous les statuts ouverts", "@sage/xtrem-stock/pages__stock_issue____navigationPanel__optionsMenu__title__2": "Tous les statuts", "@sage/xtrem-stock/pages__stock_issue____navigationPanel__optionsMenu__title__3": "Détails demandés", "@sage/xtrem-stock/pages__stock_issue____navigationPanel__optionsMenu__title__4": "<PERSON>é<PERSON> renseign<PERSON>", "@sage/xtrem-stock/pages__stock_issue____navigationPanel__optionsMenu__title__5": "Comptabilisation de stock en cours", "@sage/xtrem-stock/pages__stock_issue____navigationPanel__optionsMenu__title__6": "Erreur de comptabilisation de stock", "@sage/xtrem-stock/pages__stock_issue____navigationPanel__optionsMenu__title__7": "En sortie", "@sage/xtrem-stock/pages__stock_issue____objectTypePlural": "Sorties de stock", "@sage/xtrem-stock/pages__stock_issue____objectTypeSingular": "Sortie de stock", "@sage/xtrem-stock/pages__stock_issue____subtitle": "Sortie de stock", "@sage/xtrem-stock/pages__stock_issue____title": "Sortie de stock", "@sage/xtrem-stock/pages__stock_issue__addIssueLine____title": "<PERSON><PERSON><PERSON> ligne", "@sage/xtrem-stock/pages__stock_issue__apply_dimensions_success": "Sections appliquées", "@sage/xtrem-stock/pages__stock_issue__defaultDimension____title": "Définir sections", "@sage/xtrem-stock/pages__stock_issue__defaultDimensionAction____title": "Paramétrer les sections par défaut", "@sage/xtrem-stock/pages__stock_issue__description____title": "Description", "@sage/xtrem-stock/pages__stock_issue__displayStatus____title": "Statut d'affichage", "@sage/xtrem-stock/pages__stock_issue__effectiveDate____title": "Date", "@sage/xtrem-stock/pages__stock_issue__goToSysNotificationPage____title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_issue__issue_step_sequence_creation": "<PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_issue__issue_step_sequence_detail_stock": "Stock détail", "@sage/xtrem-stock/pages__stock_issue__issue_step_sequence_post_stock": "Comptabiliser le stock", "@sage/xtrem-stock/pages__stock_issue__line_delete_action_dialog_content": "Vous êtes sur le point de supprimer cette ligne de sortie de stock.", "@sage/xtrem-stock/pages__stock_issue__line_delete_action_dialog_title": "Confirmer la <PERSON>", "@sage/xtrem-stock/pages__stock_issue__lines____columns__columns__item__name__columns__title__decimalDigits": "Décimales", "@sage/xtrem-stock/pages__stock_issue__lines____columns__columns__item__name__columns__title__id": "Code", "@sage/xtrem-stock/pages__stock_issue__lines____columns__columns__item__name__columns__title__name": "Nom", "@sage/xtrem-stock/pages__stock_issue__lines____columns__columns__item__name__columns__title__symbol": "Symbole", "@sage/xtrem-stock/pages__stock_issue__lines____columns__columns__item__name__title": "Nom", "@sage/xtrem-stock/pages__stock_issue__lines____columns__columns__item__name__title__2": "Code", "@sage/xtrem-stock/pages__stock_issue__lines____columns__columns__item__name__title__3": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_issue__lines____columns__columns__stockStatus__name__title": "Nom", "@sage/xtrem-stock/pages__stock_issue__lines____columns__columns__stockStatus__name__title__2": "Code", "@sage/xtrem-stock/pages__stock_issue__lines____columns__lookupDialogTitle__item__name": "Sélectionner l'article", "@sage/xtrem-stock/pages__stock_issue__lines____columns__lookupDialogTitle__stockStatus__name": "Sélectionner le statut de stock", "@sage/xtrem-stock/pages__stock_issue__lines____columns__placeholder__item__name": "Sélectionner...", "@sage/xtrem-stock/pages__stock_issue__lines____columns__title__displayStatus": "Statut", "@sage/xtrem-stock/pages__stock_issue__lines____columns__title__item__description": "Descr. article", "@sage/xtrem-stock/pages__stock_issue__lines____columns__title__item__id": "Code article", "@sage/xtrem-stock/pages__stock_issue__lines____columns__title__item__name": "Article", "@sage/xtrem-stock/pages__stock_issue__lines____columns__title__orderCost": "<PERSON><PERSON><PERSON> réel", "@sage/xtrem-stock/pages__stock_issue__lines____columns__title__quantityInStockUnit": "Quantité", "@sage/xtrem-stock/pages__stock_issue__lines____columns__title__stockDetailStatus": "Statut détail stock", "@sage/xtrem-stock/pages__stock_issue__lines____columns__title__stockStatus__name": "Statut", "@sage/xtrem-stock/pages__stock_issue__lines____columns__title__valuedCost": "Co<PERSON>t attendu", "@sage/xtrem-stock/pages__stock_issue__lines____dropdownActions__title": "Détails de stock", "@sage/xtrem-stock/pages__stock_issue__lines____dropdownActions__title__2": "Sections", "@sage/xtrem-stock/pages__stock_issue__lines____dropdownActions__title__3": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_issue__lines____inlineActions__title": "Ouv<PERSON>r volet des lignes", "@sage/xtrem-stock/pages__stock_issue__lines____mobileCard__line2__title": "Description", "@sage/xtrem-stock/pages__stock_issue__lines____mobileCard__line2Right__title": "Prix", "@sage/xtrem-stock/pages__stock_issue__lines____mobileCard__line3Right__title": "Quantité", "@sage/xtrem-stock/pages__stock_issue__lines____mobileCard__title__title": "Article", "@sage/xtrem-stock/pages__stock_issue__lines____mobileCard__titleRight__title": "Statut", "@sage/xtrem-stock/pages__stock_issue__lines____optionsMenu__title": "Tous les statuts", "@sage/xtrem-stock/pages__stock_issue__lines____optionsMenu__title__2": "Tous les statuts ouverts", "@sage/xtrem-stock/pages__stock_issue__lines____optionsMenu__title__3": "Détails demandés", "@sage/xtrem-stock/pages__stock_issue__lines____optionsMenu__title__4": "<PERSON>é<PERSON> renseign<PERSON>", "@sage/xtrem-stock/pages__stock_issue__lines____optionsMenu__title__5": "Comptabilisation de stock en cours", "@sage/xtrem-stock/pages__stock_issue__lines____optionsMenu__title__6": "Erreur de comptabilisation de stock", "@sage/xtrem-stock/pages__stock_issue__lines____optionsMenu__title__7": "En sortie", "@sage/xtrem-stock/pages__stock_issue__lines____sidebar__headerDropdownActions__title": "Détails de stock", "@sage/xtrem-stock/pages__stock_issue__lines____sidebar__headerDropdownActions__title__2": "Sections", "@sage/xtrem-stock/pages__stock_issue__lines____sidebar__headerDropdownActions__title__3": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_issue__lines____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_issue__linesSection____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_issue__mainSection____title": "Général", "@sage/xtrem-stock/pages__stock_issue__notification__The_quantity_is_mandatory": "Renseigner la quantité.", "@sage/xtrem-stock/pages__stock_issue__number____title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_issue__post____title": "Comptabiliser le stock", "@sage/xtrem-stock/pages__stock_issue__postingDetails____columns__title__financeIntegrationAppRecordId": "Référence intégration comptable", "@sage/xtrem-stock/pages__stock_issue__postingDetails____columns__title__postingStatus": "Statut", "@sage/xtrem-stock/pages__stock_issue__postingDetails____columns__title__targetDocumentNumber": "N° doc", "@sage/xtrem-stock/pages__stock_issue__postingDetails____columns__title__targetDocumentType": "Type doc", "@sage/xtrem-stock/pages__stock_issue__postingDetails____title": "Résultats", "@sage/xtrem-stock/pages__stock_issue__postingMessageBlock____title": "<PERSON><PERSON><PERSON> de l'erreur", "@sage/xtrem-stock/pages__stock_issue__postingSection____title": "Comptabilisation", "@sage/xtrem-stock/pages__stock_issue__reasonCode____lookupDialogTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON> le motif", "@sage/xtrem-stock/pages__stock_issue__reasonCode____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_issue__repost____title": "Recomptabiliser", "@sage/xtrem-stock/pages__stock_issue__repost_errors": "Erreurs lors de la recomptabilisation :", "@sage/xtrem-stock/pages__stock_issue__saveStockIssue____title": "Enregistrer", "@sage/xtrem-stock/pages__stock_issue__stock_journal_inquiry": "Consultation des journaux de stock", "@sage/xtrem-stock/pages__stock_issue__stock_posting_error": "Erreur de comptabilisation de stock", "@sage/xtrem-stock/pages__stock_issue__stockSite____columns__title__id": "Code", "@sage/xtrem-stock/pages__stock_issue__stockSite____columns__title__name": "Nom", "@sage/xtrem-stock/pages__stock_issue__stockSite____lookupDialogTitle": "Sélectionner le site", "@sage/xtrem-stock/pages__stock_issue__stockSite____placeholder": "Sélectionner...", "@sage/xtrem-stock/pages__stock_issue__stockSite____title": "Site", "@sage/xtrem-stock/pages__stock_issue__stockTransactionStatus____title": "Statut de stock", "@sage/xtrem-stock/pages__stock_issue_post__stock_details_required": "<PERSON><PERSON> de<PERSON> renseigner les détails de stock pour toutes les lignes avant d'effectuer la comptabilisation.", "@sage/xtrem-stock/pages__stock_journal_inquiry____navigationPanel__dropdownActions__title": "Écriture", "@sage/xtrem-stock/pages__stock_journal_inquiry____navigationPanel__inlineActions__title": "Écriture", "@sage/xtrem-stock/pages__stock_journal_inquiry____navigationPanel__listItem__amountVariance__title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_journal_inquiry____navigationPanel__listItem__costVariance__title": "<PERSON><PERSON><PERSON> de <PERSON>", "@sage/xtrem-stock/pages__stock_journal_inquiry____navigationPanel__listItem__currency__title": "<PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_journal_inquiry____navigationPanel__listItem__currencyId__title": "Code de la devise", "@sage/xtrem-stock/pages__stock_journal_inquiry____navigationPanel__listItem__documentLineType__title": "Type de ligne de document", "@sage/xtrem-stock/pages__stock_journal_inquiry____navigationPanel__listItem__documentLink__title": "Document", "@sage/xtrem-stock/pages__stock_journal_inquiry____navigationPanel__listItem__effectiveDate__title": "Date", "@sage/xtrem-stock/pages__stock_journal_inquiry____navigationPanel__listItem__expirationDate__title": "Date de péremption", "@sage/xtrem-stock/pages__stock_journal_inquiry____navigationPanel__listItem__itemDescription__title": "Description de l'article", "@sage/xtrem-stock/pages__stock_journal_inquiry____navigationPanel__listItem__itemId__title": "Code de l'article", "@sage/xtrem-stock/pages__stock_journal_inquiry____navigationPanel__listItem__itemName__title": "Article", "@sage/xtrem-stock/pages__stock_journal_inquiry____navigationPanel__listItem__locationId__title": "Code emplacement", "@sage/xtrem-stock/pages__stock_journal_inquiry____navigationPanel__listItem__locationName__title": "Emplacement", "@sage/xtrem-stock/pages__stock_journal_inquiry____navigationPanel__listItem__locationZone__title": "Zone d'emplacement", "@sage/xtrem-stock/pages__stock_journal_inquiry____navigationPanel__listItem__lot__title": "Lot", "@sage/xtrem-stock/pages__stock_journal_inquiry____navigationPanel__listItem__movementAmount__title": "Montant de mouvement", "@sage/xtrem-stock/pages__stock_journal_inquiry____navigationPanel__listItem__movementType__title": "Type de mouvement", "@sage/xtrem-stock/pages__stock_journal_inquiry____navigationPanel__listItem__nonAbsorbedAmount__title": "Montant non-absorbé", "@sage/xtrem-stock/pages__stock_journal_inquiry____navigationPanel__listItem__orderAmount__title": "Montant de l'ordre", "@sage/xtrem-stock/pages__stock_journal_inquiry____navigationPanel__listItem__orderCost__title": "Coût de l'ordre", "@sage/xtrem-stock/pages__stock_journal_inquiry____navigationPanel__listItem__quantityInStockUnit__title": "Quantité", "@sage/xtrem-stock/pages__stock_journal_inquiry____navigationPanel__listItem__reasonCode__title": "Code motif", "@sage/xtrem-stock/pages__stock_journal_inquiry____navigationPanel__listItem__siteId__title": "Code site", "@sage/xtrem-stock/pages__stock_journal_inquiry____navigationPanel__listItem__stockStatus__title": "Contrôle qualité", "@sage/xtrem-stock/pages__stock_journal_inquiry____navigationPanel__listItem__stockUnit__title": "Unité de mesure", "@sage/xtrem-stock/pages__stock_journal_inquiry____navigationPanel__listItem__sublot__title": "Sous-lot", "@sage/xtrem-stock/pages__stock_journal_inquiry____navigationPanel__listItem__supplierLot__title": "Numéro lot fournisseur", "@sage/xtrem-stock/pages__stock_journal_inquiry____navigationPanel__listItem__valuedCost__title": "Coût valorisé", "@sage/xtrem-stock/pages__stock_journal_inquiry____title": "Consultation des journaux de stock", "@sage/xtrem-stock/pages__stock_journal_inquiry__no_journal_entry_found": "Aucune écriture trouvée pour le document sélectionné.", "@sage/xtrem-stock/pages__stock_journal_query____title": "Consultation journal des stocks", "@sage/xtrem-stock/pages__stock_journal_query__criteriaBlock____title": "Critères", "@sage/xtrem-stock/pages__stock_journal_query__itemFrom____columns__title__id": "ID", "@sage/xtrem-stock/pages__stock_journal_query__itemFrom____columns__title__name": "Nom", "@sage/xtrem-stock/pages__stock_journal_query__itemFrom____placeholder": "Articles début", "@sage/xtrem-stock/pages__stock_journal_query__itemFrom____title": "De", "@sage/xtrem-stock/pages__stock_journal_query__itemTo____columns__title__id": "ID", "@sage/xtrem-stock/pages__stock_journal_query__itemTo____columns__title__name": "Nom", "@sage/xtrem-stock/pages__stock_journal_query__itemTo____placeholder": "Articles fin", "@sage/xtrem-stock/pages__stock_journal_query__itemTo____title": "À", "@sage/xtrem-stock/pages__stock_journal_query__mainBlock____title": "Résultats", "@sage/xtrem-stock/pages__stock_journal_query__site____columns__title__id": "ID", "@sage/xtrem-stock/pages__stock_journal_query__site____columns__title__name": "Nom", "@sage/xtrem-stock/pages__stock_journal_query__site____placeholder": "Sélectionner...", "@sage/xtrem-stock/pages__stock_journal_query__site____title": "Site", "@sage/xtrem-stock/pages__stock_journal_query__stockJournal____columns__title__activeQuantityInStockUnit": "Quantité active", "@sage/xtrem-stock/pages__stock_journal_query__stockJournal____columns__title__amountVariance": "<PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_journal_query__stockJournal____columns__title__costVariance": "<PERSON><PERSON><PERSON> co<PERSON>t", "@sage/xtrem-stock/pages__stock_journal_query__stockJournal____columns__title__effectiveDate": "Date", "@sage/xtrem-stock/pages__stock_journal_query__stockJournal____columns__title__item__name": "Article", "@sage/xtrem-stock/pages__stock_journal_query__stockJournal____columns__title__location__id": "Emplacement", "@sage/xtrem-stock/pages__stock_journal_query__stockJournal____columns__title__lot__id": "Lot", "@sage/xtrem-stock/pages__stock_journal_query__stockJournal____columns__title__lot__sublot": "Sous-lot", "@sage/xtrem-stock/pages__stock_journal_query__stockJournal____columns__title__lot__supplierLot": "Numéro lot fournisseur", "@sage/xtrem-stock/pages__stock_journal_query__stockJournal____columns__title__movementAmount": "Montant mouvement", "@sage/xtrem-stock/pages__stock_journal_query__stockJournal____columns__title__orderAmount": "Montant ordre", "@sage/xtrem-stock/pages__stock_journal_query__stockJournal____columns__title__orderCost": "Coût ordre", "@sage/xtrem-stock/pages__stock_journal_query__stockJournal____columns__title__quantityInStockUnit": "Quantité", "@sage/xtrem-stock/pages__stock_journal_query__stockJournal____columns__title__site__name": "Site", "@sage/xtrem-stock/pages__stock_journal_query__stockJournal____columns__title__stockUnit__name": "Unité mesure", "@sage/xtrem-stock/pages__stock_journal_query__stockJournal____columns__title__valuedCost": "Coût valorisé", "@sage/xtrem-stock/pages__stock_mrp_calculation__calculate": "Calculer", "@sage/xtrem-stock/pages__stock_mrp_calculation_company_sites_error": "Renseignez au moins une société ou un site.", "@sage/xtrem-stock/pages__stock_receipt____navigationPanel__dropdownActions__title": "Définir sections", "@sage/xtrem-stock/pages__stock_receipt____navigationPanel__dropdownActions__title__2": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_receipt____navigationPanel__listItem__description__title": "Description", "@sage/xtrem-stock/pages__stock_receipt____navigationPanel__listItem__line2__title": "Site", "@sage/xtrem-stock/pages__stock_receipt____navigationPanel__listItem__line2Right__title": "Date", "@sage/xtrem-stock/pages__stock_receipt____navigationPanel__listItem__reasonCode__title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_receipt____navigationPanel__listItem__status__title": "Statut", "@sage/xtrem-stock/pages__stock_receipt____navigationPanel__listItem__title__title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_receipt____navigationPanel__listItem__titleRight__title": "Statut", "@sage/xtrem-stock/pages__stock_receipt____navigationPanel__optionsMenu__title": "Tous les statuts ouverts", "@sage/xtrem-stock/pages__stock_receipt____navigationPanel__optionsMenu__title__2": "Tous les statuts", "@sage/xtrem-stock/pages__stock_receipt____navigationPanel__optionsMenu__title__3": "Détails demandés", "@sage/xtrem-stock/pages__stock_receipt____navigationPanel__optionsMenu__title__4": "<PERSON>é<PERSON> renseign<PERSON>", "@sage/xtrem-stock/pages__stock_receipt____navigationPanel__optionsMenu__title__5": "Comptabilisation de stock en cours", "@sage/xtrem-stock/pages__stock_receipt____navigationPanel__optionsMenu__title__6": "Erreur de comptabilisation de stock", "@sage/xtrem-stock/pages__stock_receipt____navigationPanel__optionsMenu__title__7": "Ré<PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_receipt____objectTypePlural": "Entrées de stock", "@sage/xtrem-stock/pages__stock_receipt____objectTypeSingular": "Entrée de stock", "@sage/xtrem-stock/pages__stock_receipt____title": "Entrée de stock", "@sage/xtrem-stock/pages__stock_receipt__actual_cost_cannot_be_negative": "<PERSON><PERSON> de<PERSON> renseigner une valeur supérieure ou égale à zéro.", "@sage/xtrem-stock/pages__stock_receipt__apply_dimensions_success": "Sections appliquées", "@sage/xtrem-stock/pages__stock_receipt__defaultDimension____title": "Définir sections", "@sage/xtrem-stock/pages__stock_receipt__defaultDimensionAction____title": "Paramétrer les sections par défaut", "@sage/xtrem-stock/pages__stock_receipt__description____title": "Description", "@sage/xtrem-stock/pages__stock_receipt__displayStatus____title": "Statut d'affichage", "@sage/xtrem-stock/pages__stock_receipt__effectiveDate____title": "Date", "@sage/xtrem-stock/pages__stock_receipt__goToSysNotificationPage____title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_receipt__line_delete_action_dialog_content": "Vous êtes sur le point de supprimer cette ligne de réception de stock.", "@sage/xtrem-stock/pages__stock_receipt__line_delete_action_dialog_title": "Confirmer la <PERSON>", "@sage/xtrem-stock/pages__stock_receipt__lines____columns__columns__existingLot__id__columns__title__id": "Code", "@sage/xtrem-stock/pages__stock_receipt__lines____columns__columns__existingLot__id__columns__title__name": "Nom", "@sage/xtrem-stock/pages__stock_receipt__lines____columns__columns__existingLot__id__title": "Code", "@sage/xtrem-stock/pages__stock_receipt__lines____columns__columns__existingLot__id__title__2": "Date péremption", "@sage/xtrem-stock/pages__stock_receipt__lines____columns__columns__existingLot__id__title__3": "Article", "@sage/xtrem-stock/pages__stock_receipt__lines____columns__columns__item__name__columns__title__decimalDigits": "Décimales", "@sage/xtrem-stock/pages__stock_receipt__lines____columns__columns__item__name__columns__title__id__2": "Code", "@sage/xtrem-stock/pages__stock_receipt__lines____columns__columns__item__name__columns__title__name__2": "Nom", "@sage/xtrem-stock/pages__stock_receipt__lines____columns__columns__item__name__columns__title__symbol": "Symbole", "@sage/xtrem-stock/pages__stock_receipt__lines____columns__columns__item__name__title": "Nom", "@sage/xtrem-stock/pages__stock_receipt__lines____columns__columns__item__name__title__2": "Code", "@sage/xtrem-stock/pages__stock_receipt__lines____columns__columns__item__name__title__3": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_receipt__lines____columns__columns__location__name__title": "Nom", "@sage/xtrem-stock/pages__stock_receipt__lines____columns__columns__location__name__title__2": "Code", "@sage/xtrem-stock/pages__stock_receipt__lines____columns__columns__stockStatus__name__title": "Nom", "@sage/xtrem-stock/pages__stock_receipt__lines____columns__columns__stockStatus__name__title__2": "Code", "@sage/xtrem-stock/pages__stock_receipt__lines____columns__lookupDialogTitle__existingLot__id": "Sélectionner le lot", "@sage/xtrem-stock/pages__stock_receipt__lines____columns__lookupDialogTitle__item__name": "Sélectionner l'article", "@sage/xtrem-stock/pages__stock_receipt__lines____columns__lookupDialogTitle__location__name": "Sélectionner l'emplacement", "@sage/xtrem-stock/pages__stock_receipt__lines____columns__lookupDialogTitle__stockStatus": "Sé<PERSON><PERSON>ner une valeur qualité", "@sage/xtrem-stock/pages__stock_receipt__lines____columns__lookupDialogTitle__stockStatus__name": "Sélectionner le statut de stock", "@sage/xtrem-stock/pages__stock_receipt__lines____columns__placeholder__existingLot__id": "Sélectionner...", "@sage/xtrem-stock/pages__stock_receipt__lines____columns__placeholder__item__name": "Sélectionner l'article", "@sage/xtrem-stock/pages__stock_receipt__lines____columns__title__displayStatus": "Statut", "@sage/xtrem-stock/pages__stock_receipt__lines____columns__title__existingLot__id": "Lot", "@sage/xtrem-stock/pages__stock_receipt__lines____columns__title__expirationDate": "Date péremption", "@sage/xtrem-stock/pages__stock_receipt__lines____columns__title__item__description": "Descr. article", "@sage/xtrem-stock/pages__stock_receipt__lines____columns__title__item__id": "Code article", "@sage/xtrem-stock/pages__stock_receipt__lines____columns__title__item__name": "Article", "@sage/xtrem-stock/pages__stock_receipt__lines____columns__title__itemSite": "Article-site", "@sage/xtrem-stock/pages__stock_receipt__lines____columns__title__location__name": "Emplacement", "@sage/xtrem-stock/pages__stock_receipt__lines____columns__title__lotNumber": "Numéro lot", "@sage/xtrem-stock/pages__stock_receipt__lines____columns__title__orderCost": "<PERSON><PERSON><PERSON> réel", "@sage/xtrem-stock/pages__stock_receipt__lines____columns__title__quantityInStockUnit": "Quantité", "@sage/xtrem-stock/pages__stock_receipt__lines____columns__title__stockStatus": "Contrôle qualité", "@sage/xtrem-stock/pages__stock_receipt__lines____columns__title__stockStatus__name": "Statut", "@sage/xtrem-stock/pages__stock_receipt__lines____columns__title__valuedCost": "Co<PERSON>t attendu", "@sage/xtrem-stock/pages__stock_receipt__lines____dropdownActions__title": "Détails de stock", "@sage/xtrem-stock/pages__stock_receipt__lines____dropdownActions__title__2": "Sections", "@sage/xtrem-stock/pages__stock_receipt__lines____dropdownActions__title__3": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_receipt__lines____inlineActions__title": "Ouv<PERSON>r volet des lignes", "@sage/xtrem-stock/pages__stock_receipt__lines____mobileCard__line2__title": "Description", "@sage/xtrem-stock/pages__stock_receipt__lines____mobileCard__line2Right__title": "Prix", "@sage/xtrem-stock/pages__stock_receipt__lines____mobileCard__line3Right__title": "Quantité", "@sage/xtrem-stock/pages__stock_receipt__lines____mobileCard__title__title": "Article", "@sage/xtrem-stock/pages__stock_receipt__lines____mobileCard__titleRight__title": "Statut", "@sage/xtrem-stock/pages__stock_receipt__lines____optionsMenu__title": "Tous les statuts", "@sage/xtrem-stock/pages__stock_receipt__lines____optionsMenu__title__2": "Tous les statuts ouverts", "@sage/xtrem-stock/pages__stock_receipt__lines____optionsMenu__title__3": "Détails demandés", "@sage/xtrem-stock/pages__stock_receipt__lines____optionsMenu__title__4": "<PERSON>é<PERSON> renseign<PERSON>", "@sage/xtrem-stock/pages__stock_receipt__lines____optionsMenu__title__5": "Comptabilisation de stock en cours", "@sage/xtrem-stock/pages__stock_receipt__lines____optionsMenu__title__6": "Erreur de comptabilisation de stock", "@sage/xtrem-stock/pages__stock_receipt__lines____optionsMenu__title__7": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_receipt__lines____sidebar__headerDropdownActions__title": "Détails de stock", "@sage/xtrem-stock/pages__stock_receipt__lines____sidebar__headerDropdownActions__title__2": "Sections", "@sage/xtrem-stock/pages__stock_receipt__lines____sidebar__headerDropdownActions__title__3": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_receipt__lines____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_receipt__linesSection____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_receipt__notification__expiration_date_cannot_be_past": "La date de péremption ne peut pas être antérieure à la date du jour.", "@sage/xtrem-stock/pages__stock_receipt__notification__expiration_date_is_mandatory": "Renseignez la date de péremption.", "@sage/xtrem-stock/pages__stock_receipt__notification__validation_error": "Des erreurs de validation se sont produites :\n{{#each errors}}\t- {{this}}{{#unless @last}}\n{{/unless}}{{/each}}.", "@sage/xtrem-stock/pages__stock_receipt__number____title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_receipt__post____title": "Comptabiliser le stock", "@sage/xtrem-stock/pages__stock_receipt__postingDetails____columns__title__financeIntegrationAppRecordId": "Réf. intégration comptable", "@sage/xtrem-stock/pages__stock_receipt__postingDetails____columns__title__postingStatus": "Statut", "@sage/xtrem-stock/pages__stock_receipt__postingDetails____columns__title__targetDocumentNumber": "N° doc", "@sage/xtrem-stock/pages__stock_receipt__postingDetails____columns__title__targetDocumentType": "Type doc", "@sage/xtrem-stock/pages__stock_receipt__postingDetails____title": "Résultats", "@sage/xtrem-stock/pages__stock_receipt__postingMessageBlock____title": "<PERSON><PERSON><PERSON> de l'erreur", "@sage/xtrem-stock/pages__stock_receipt__postingSection____title": "Comptabilisation", "@sage/xtrem-stock/pages__stock_receipt__quantity_must_be_positive": "<PERSON><PERSON> de<PERSON> renseigner une valeur supérieure à zéro.", "@sage/xtrem-stock/pages__stock_receipt__reasonCode____lookupDialogTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON> le motif", "@sage/xtrem-stock/pages__stock_receipt__reasonCode____placeholder": "<PERSON><PERSON><PERSON><PERSON><PERSON> le motif", "@sage/xtrem-stock/pages__stock_receipt__reasonCode____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_receipt__receipt_step_sequence_creation": "<PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_receipt__receipt_step_sequence_detail_stock": "Stock détail", "@sage/xtrem-stock/pages__stock_receipt__receipt_step_sequence_post_stock": "Comptabiliser le stock", "@sage/xtrem-stock/pages__stock_receipt__repost____title": "Recomptabiliser", "@sage/xtrem-stock/pages__stock_receipt__repost_errors": "Erreurs lors de la recomptabilisation :", "@sage/xtrem-stock/pages__stock_receipt__saveStockReceipt____title": "Enregistrer", "@sage/xtrem-stock/pages__stock_receipt__serial number_exists_on_another_line": "Le numéro de série {{serialNumberId}} pour l'article {{itemId}} existe déjà sur une autre ligne de document.", "@sage/xtrem-stock/pages__stock_receipt__stock_journal_inquiry": "Consultation des journaux de stock", "@sage/xtrem-stock/pages__stock_receipt__stock_posting_error": "Erreur de comptabilisation de stock", "@sage/xtrem-stock/pages__stock_receipt__stockReceiptStatus____title": "Statut réception de stock", "@sage/xtrem-stock/pages__stock_receipt__stockSite____columns__columns__legalCompany__name__columns__title__symbol": "Symbole", "@sage/xtrem-stock/pages__stock_receipt__stockSite____columns__title__id": "Code", "@sage/xtrem-stock/pages__stock_receipt__stockSite____columns__title__isLocationManaged": "Gestion emplacements", "@sage/xtrem-stock/pages__stock_receipt__stockSite____columns__title__name": "Nom", "@sage/xtrem-stock/pages__stock_receipt__stockSite____lookupDialogTitle": "Sélectionner le site", "@sage/xtrem-stock/pages__stock_receipt__stockSite____placeholder": "Sélectionner...", "@sage/xtrem-stock/pages__stock_receipt__stockSite____title": "Site", "@sage/xtrem-stock/pages__stock_receipt__stockTransactionStatus____title": "Statut stock", "@sage/xtrem-stock/pages__stock_receipt_detail_panel__notification__The_lot_is_mandatory": "Le lot est obligatoire.", "@sage/xtrem-stock/pages__stock_receipt_detail_panel__notification__The_status_is_mandatory": "Le statut est obligatoire.", "@sage/xtrem-stock/pages__stock_receipt_detail_panel__notification__The_sublot_is_mandatory": "Le sous-lot est obligatoire.", "@sage/xtrem-stock/pages__stock_receipt_details_panel__notification__expiration_date_is_mandatory": "La date de péremption est obligatoire.", "@sage/xtrem-stock/pages__stock_receipt_post__stock_details_required": "<PERSON><PERSON> de<PERSON> renseigner les détails de stock pour toutes les lignes avant d'effectuer la comptabilisation.", "@sage/xtrem-stock/pages__stock_reorder_calculation____title": "Réapprovisionnement de stock", "@sage/xtrem-stock/pages__stock_reorder_calculation__company____columns__title__id": "Code", "@sage/xtrem-stock/pages__stock_reorder_calculation__company____columns__title__name": "Nom", "@sage/xtrem-stock/pages__stock_reorder_calculation__company____lookupDialogTitle": "Sélectionner la société", "@sage/xtrem-stock/pages__stock_reorder_calculation__company____placeholder": "Sélectionner...", "@sage/xtrem-stock/pages__stock_reorder_calculation__company____title": "Société", "@sage/xtrem-stock/pages__stock_reorder_calculation__criteriaBlock____title": "Critères", "@sage/xtrem-stock/pages__stock_reorder_calculation__endDate____title": "Date de fin", "@sage/xtrem-stock/pages__stock_reorder_calculation__fromItem____columns__title__category__name": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_reorder_calculation__fromItem____lookupDialogTitle": "Sélectionner l'article", "@sage/xtrem-stock/pages__stock_reorder_calculation__fromItem____placeholder": "Sélectionner...", "@sage/xtrem-stock/pages__stock_reorder_calculation__fromItem____title": "Article début", "@sage/xtrem-stock/pages__stock_reorder_calculation__itemValues____columns__columns__stockUnit__symbol__title__2": "Symbole", "@sage/xtrem-stock/pages__stock_reorder_calculation__lines____columns__title__company__name": "Société", "@sage/xtrem-stock/pages__stock_reorder_calculation__lines____columns__title__itemSite__item__description": "Descr. article", "@sage/xtrem-stock/pages__stock_reorder_calculation__lines____columns__title__itemSite__item__id": "Code article", "@sage/xtrem-stock/pages__stock_reorder_calculation__lines____columns__title__itemSite__item__name": "Article", "@sage/xtrem-stock/pages__stock_reorder_calculation__lines____columns__title__itemSite__site__id": "Site", "@sage/xtrem-stock/pages__stock_reorder_calculation__lines____columns__title__orderDate": "Date cde", "@sage/xtrem-stock/pages__stock_reorder_calculation__lines____columns__title__quantity": "Qté suggérée", "@sage/xtrem-stock/pages__stock_reorder_calculation__lines____columns__title__reorderType": "Méthode processus", "@sage/xtrem-stock/pages__stock_reorder_calculation__lines____columns__title__supplier__businessEntity__id": "Code fournisseur", "@sage/xtrem-stock/pages__stock_reorder_calculation__lines____columns__title__supplier__businessEntity__name": "Nom fournisseur", "@sage/xtrem-stock/pages__stock_reorder_calculation__lines____dropdownActions__title": "<PERSON><PERSON><PERSON> commande d'achat", "@sage/xtrem-stock/pages__stock_reorder_calculation__lines____dropdownActions__title__2": "Créer ordre de fabrication", "@sage/xtrem-stock/pages__stock_reorder_calculation__mainSection____title": "Général", "@sage/xtrem-stock/pages__stock_reorder_calculation__purchase_order_creation__success": "Commande d'achat {{num}} c<PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_reorder_calculation__reorderType____title": "Type de réapprovisionnement", "@sage/xtrem-stock/pages__stock_reorder_calculation__runStockReorderCalculation____title": "Exécuter", "@sage/xtrem-stock/pages__stock_reorder_calculation__sites____columns__title__id": "Code", "@sage/xtrem-stock/pages__stock_reorder_calculation__sites____columns__title__name": "Nom", "@sage/xtrem-stock/pages__stock_reorder_calculation__sites____lookupDialogTitle": "Sélectionner le site", "@sage/xtrem-stock/pages__stock_reorder_calculation__sites____placeholder": "Sélectionner...", "@sage/xtrem-stock/pages__stock_reorder_calculation__sites____title": "Site", "@sage/xtrem-stock/pages__stock_reorder_calculation__status____title": "Statut", "@sage/xtrem-stock/pages__stock_reorder_calculation__toItem____columns__title__category__name": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_reorder_calculation__toItem____columns__title__description": "Description", "@sage/xtrem-stock/pages__stock_reorder_calculation__toItem____columns__title__id": "Code", "@sage/xtrem-stock/pages__stock_reorder_calculation__toItem____columns__title__name": "Nom", "@sage/xtrem-stock/pages__stock_reorder_calculation__toItem____lookupDialogTitle": "Sélectionner l'article", "@sage/xtrem-stock/pages__stock_reorder_calculation__toItem____placeholder": "Sélectionner...", "@sage/xtrem-stock/pages__stock_reorder_calculation__toItem____title": "Article fin", "@sage/xtrem-stock/pages__stock_reorder_calculation__user____title": "Calcul demandé par", "@sage/xtrem-stock/pages__stock_reorder_calculation__work_order_creation__success": "Ordre de fabrication {{num}} c<PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_reorder_calculation_new__fromItem____columns__title__description": "Description", "@sage/xtrem-stock/pages__stock_reorder_calculation_new__fromItem____columns__title__id": "Code", "@sage/xtrem-stock/pages__stock_reorder_calculation_new__fromItem____columns__title__name": "Nom", "@sage/xtrem-stock/pages__stock_reorder_calculation_new__user____columns__title__email": "E-mail", "@sage/xtrem-stock/pages__stock_reorder_calculation_new__user____columns__title__firstName": "Prénom", "@sage/xtrem-stock/pages__stock_reorder_calculation_new__user____columns__title__lastName": "Nom", "@sage/xtrem-stock/pages__stock_reorder_calculation_new__user____lookupDialogTitle": "Sélectionner l'utilisateur", "@sage/xtrem-stock/pages__stock_reorder_calculation_request__notification_calculation_failed": "Échec du calcul de réapprovisionnement de stock.", "@sage/xtrem-stock/pages__stock_reorder_calculation_request__notification_calculation_finished": "Calcul de réapprovisionnement de stock terminé.", "@sage/xtrem-stock/pages__stock_reorder_calculation_request__notification_request_sent": "Demande de calcul de réapprovisionnement de stock envoyée.", "@sage/xtrem-stock/pages__stock_trace_inquiry____title": "Traçabilité article", "@sage/xtrem-stock/pages__stock_trace_inquiry__criteriaBlock____title": "Critères", "@sage/xtrem-stock/pages__stock_trace_inquiry__item____title": "Article", "@sage/xtrem-stock/pages__stock_trace_inquiry__lot____columns__title__id": "Code", "@sage/xtrem-stock/pages__stock_trace_inquiry__lot____columns__title__item__name": "Article", "@sage/xtrem-stock/pages__stock_trace_inquiry__lot____columns__title__sublot": "Sous-lot", "@sage/xtrem-stock/pages__stock_trace_inquiry__lot____lookupDialogTitle": "Sélectionner le lot", "@sage/xtrem-stock/pages__stock_trace_inquiry__lot____placeholder": "Sélectionner le lot", "@sage/xtrem-stock/pages__stock_trace_inquiry__lot____title": "Lot", "@sage/xtrem-stock/pages__stock_trace_inquiry__serialNumber____columns__title__id": "Code", "@sage/xtrem-stock/pages__stock_trace_inquiry__serialNumber____columns__title__item__name": "Article", "@sage/xtrem-stock/pages__stock_trace_inquiry__serialNumber____columns__title__site__name": "Site", "@sage/xtrem-stock/pages__stock_trace_inquiry__serialNumber____lookupDialogTitle": "Sélectionner le numéro de série", "@sage/xtrem-stock/pages__stock_trace_inquiry__serialNumber____placeholder": "Sélectionner le numéro de série", "@sage/xtrem-stock/pages__stock_trace_inquiry__serialNumber____title": "Numéro de série", "@sage/xtrem-stock/pages__stock_trace_inquiry__site____title": "Site", "@sage/xtrem-stock/pages__stock_valuation____objectTypeSingular": "Valorisation de stock", "@sage/xtrem-stock/pages__stock_valuation____title": "Valorisation de stock", "@sage/xtrem-stock/pages__stock_valuation__commodityCode____title": "Code marchandise", "@sage/xtrem-stock/pages__stock_valuation__company____columns__title__id": "Code", "@sage/xtrem-stock/pages__stock_valuation__company____columns__title__name": "Nom", "@sage/xtrem-stock/pages__stock_valuation__company____lookupDialogTitle": "Sélectionner la société", "@sage/xtrem-stock/pages__stock_valuation__company____placeholder": "Sélectionner...", "@sage/xtrem-stock/pages__stock_valuation__company____title": "Société", "@sage/xtrem-stock/pages__stock_valuation__criteriaBlock____title": "Critères", "@sage/xtrem-stock/pages__stock_valuation__date____title": "Date", "@sage/xtrem-stock/pages__stock_valuation__displayZeroValues____title": "Afficher valeurs nulles", "@sage/xtrem-stock/pages__stock_valuation__fromItem____columns__title__category__name": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_valuation__fromItem____lookupDialogTitle": "Sélectionner l'article", "@sage/xtrem-stock/pages__stock_valuation__fromItem____placeholder": "Sélectionner...", "@sage/xtrem-stock/pages__stock_valuation__fromItem____title": "Article début", "@sage/xtrem-stock/pages__stock_valuation__itemCategory____columns__title__type": "Type", "@sage/xtrem-stock/pages__stock_valuation__itemCategory____lookupDialogTitle": "Sélectionner la catégorie d'article", "@sage/xtrem-stock/pages__stock_valuation__itemCategory____title": "Catégorie d'article", "@sage/xtrem-stock/pages__stock_valuation__itemSites____columns__columns__item__id__title": "Statut", "@sage/xtrem-stock/pages__stock_valuation__itemSites____columns__columns__stockUnit__symbol__title": "Symbole", "@sage/xtrem-stock/pages__stock_valuation__itemSites____columns__columns__stockUnit__symbol__title__2": "Symbole", "@sage/xtrem-stock/pages__stock_valuation__itemSites____columns__title__commodityCode": "Code marchandise", "@sage/xtrem-stock/pages__stock_valuation__itemSites____columns__title__company__name": "Société", "@sage/xtrem-stock/pages__stock_valuation__itemSites____columns__title__costType": "Nature dépense", "@sage/xtrem-stock/pages__stock_valuation__itemSites____columns__title__currency__symbol": "<PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_valuation__itemSites____columns__title__item__category__id": "Catégorie d'article", "@sage/xtrem-stock/pages__stock_valuation__itemSites____columns__title__item__description": "Descr. article", "@sage/xtrem-stock/pages__stock_valuation__itemSites____columns__title__item__description__2": "Descr. article", "@sage/xtrem-stock/pages__stock_valuation__itemSites____columns__title__item__id": "Code article", "@sage/xtrem-stock/pages__stock_valuation__itemSites____columns__title__item__name": "Article", "@sage/xtrem-stock/pages__stock_valuation__itemSites____columns__title__item__status": "Statut", "@sage/xtrem-stock/pages__stock_valuation__itemSites____columns__title__postingClass__name": "Classe comptabilisation", "@sage/xtrem-stock/pages__stock_valuation__itemSites____columns__title__quantity": "Quantité", "@sage/xtrem-stock/pages__stock_valuation__itemSites____columns__title__site__name": "Site", "@sage/xtrem-stock/pages__stock_valuation__itemSites____columns__title__stockUnit__symbol": "Unité stock", "@sage/xtrem-stock/pages__stock_valuation__itemSites____columns__title__stockValue": "Valeur stock", "@sage/xtrem-stock/pages__stock_valuation__itemSites____columns__title__unitCost": "Coût unitaire", "@sage/xtrem-stock/pages__stock_valuation__itemSites____columns__title__valuationDate": "Date de valorisation", "@sage/xtrem-stock/pages__stock_valuation__mainSection____title": "Général", "@sage/xtrem-stock/pages__stock_valuation__postingClass____lookupDialogTitle": "Sélectionner la classe de comptabilisation", "@sage/xtrem-stock/pages__stock_valuation__postingClass____title": "Classe de comptabilisation", "@sage/xtrem-stock/pages__stock_valuation__runStockValuation____title": "Exécuter", "@sage/xtrem-stock/pages__stock_valuation__selectedSites____columns__columns__site__name__columns__title__id": "Code", "@sage/xtrem-stock/pages__stock_valuation__selectedSites____columns__columns__site__name__title": "Nom", "@sage/xtrem-stock/pages__stock_valuation__selectedSites____columns__columns__site__name__title__2": "Code", "@sage/xtrem-stock/pages__stock_valuation__selectedSites____columns__title__site__name": "Site", "@sage/xtrem-stock/pages__stock_valuation__selectedSites____title": "Sites", "@sage/xtrem-stock/pages__stock_valuation__sites____columns__title__id": "Code", "@sage/xtrem-stock/pages__stock_valuation__sites____columns__title__name": "Nom", "@sage/xtrem-stock/pages__stock_valuation__sites____lookupDialogTitle": "Sélectionner le site", "@sage/xtrem-stock/pages__stock_valuation__sites____placeholder": "Sélectionner...", "@sage/xtrem-stock/pages__stock_valuation__sites____title": "Site", "@sage/xtrem-stock/pages__stock_valuation__status____title": "Statut", "@sage/xtrem-stock/pages__stock_valuation__toItem____columns__title__category__name": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_valuation__toItem____columns__title__description": "Description", "@sage/xtrem-stock/pages__stock_valuation__toItem____columns__title__id": "Code", "@sage/xtrem-stock/pages__stock_valuation__toItem____columns__title__name": "Nom", "@sage/xtrem-stock/pages__stock_valuation__toItem____lookupDialogTitle": "Sélectionner l'article", "@sage/xtrem-stock/pages__stock_valuation__toItem____placeholder": "Sélectionner...", "@sage/xtrem-stock/pages__stock_valuation__toItem____title": "Article fin", "@sage/xtrem-stock/pages__stock_valuation__totalStockValue____title": "Valeur de stock total", "@sage/xtrem-stock/pages__stock_valuation__totalStockValueCurrency____title": "Devise de la valeur de stock total", "@sage/xtrem-stock/pages__stock_valuation__user____columns__title__email": "E-mail", "@sage/xtrem-stock/pages__stock_valuation__user____columns__title__firstName": "Prénom", "@sage/xtrem-stock/pages__stock_valuation__user____columns__title__lastName": "Nom de famille", "@sage/xtrem-stock/pages__stock_valuation__user____lookupDialogTitle": "Sélectionner l'utilisateur", "@sage/xtrem-stock/pages__stock_valuation__user____title": "Calcul demandé par", "@sage/xtrem-stock/pages__stock_valuation__valuationMethod____title": "Méthode de valorisation", "@sage/xtrem-stock/pages__stock_valuation_inquiry____title": "Consultation de valorisation de stock", "@sage/xtrem-stock/pages__stock_valuation_inquiry__company____columns__title__id": "Code", "@sage/xtrem-stock/pages__stock_valuation_inquiry__company____columns__title__name": "Nom", "@sage/xtrem-stock/pages__stock_valuation_inquiry__company____placeholder": "Sélectionner ...", "@sage/xtrem-stock/pages__stock_valuation_inquiry__company____title": "Société", "@sage/xtrem-stock/pages__stock_valuation_inquiry__confirmation____title": "Confirmation", "@sage/xtrem-stock/pages__stock_valuation_inquiry__criteriaBlock____title": "Critères", "@sage/xtrem-stock/pages__stock_valuation_inquiry__itemFrom____columns__title__id": "Code", "@sage/xtrem-stock/pages__stock_valuation_inquiry__itemFrom____columns__title__name": "Nom", "@sage/xtrem-stock/pages__stock_valuation_inquiry__itemFrom____placeholder": "Sélectionner ...", "@sage/xtrem-stock/pages__stock_valuation_inquiry__itemFrom____title": "Article début", "@sage/xtrem-stock/pages__stock_valuation_inquiry__itemSelection____columns__title__item__id": "Article", "@sage/xtrem-stock/pages__stock_valuation_inquiry__itemSelection____columns__title__item__name": "Nom", "@sage/xtrem-stock/pages__stock_valuation_inquiry__itemSelection____columns__title__site__name": "Site", "@sage/xtrem-stock/pages__stock_valuation_inquiry__itemSelection____columns__title__stockUnit__name": "Unité mesure", "@sage/xtrem-stock/pages__stock_valuation_inquiry__itemTo____columns__title__id": "Code", "@sage/xtrem-stock/pages__stock_valuation_inquiry__itemTo____columns__title__name": "Nom", "@sage/xtrem-stock/pages__stock_valuation_inquiry__itemTo____placeholder": "Sélectionner ...", "@sage/xtrem-stock/pages__stock_valuation_inquiry__itemTo____title": "Article fin", "@sage/xtrem-stock/pages__stock_valuation_inquiry__itemValues____columns__title__company__name": "Société", "@sage/xtrem-stock/pages__stock_valuation_inquiry__itemValues____columns__title__currency__symbol": "<PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_valuation_inquiry__itemValues____columns__title__dateOfValuation": "Date valorisation", "@sage/xtrem-stock/pages__stock_valuation_inquiry__itemValues____columns__title__item__id": "Article", "@sage/xtrem-stock/pages__stock_valuation_inquiry__itemValues____columns__title__item__name": "Nom", "@sage/xtrem-stock/pages__stock_valuation_inquiry__itemValues____columns__title__lastDateOfValuation": "Dernière date valorisation", "@sage/xtrem-stock/pages__stock_valuation_inquiry__itemValues____columns__title__lastQuantityInStock": "Dernière quantité", "@sage/xtrem-stock/pages__stock_valuation_inquiry__itemValues____columns__title__lastStockValue": "Dernière valeur stock", "@sage/xtrem-stock/pages__stock_valuation_inquiry__itemValues____columns__title__lastUnitCost": "Dernier coût unitaire", "@sage/xtrem-stock/pages__stock_valuation_inquiry__itemValues____columns__title__lastValuationMethod": "Dernier type de coût", "@sage/xtrem-stock/pages__stock_valuation_inquiry__itemValues____columns__title__postingClass__name": "Classe de comptabilisation", "@sage/xtrem-stock/pages__stock_valuation_inquiry__itemValues____columns__title__quantityInStock": "Quantité", "@sage/xtrem-stock/pages__stock_valuation_inquiry__itemValues____columns__title__site__name": "Site", "@sage/xtrem-stock/pages__stock_valuation_inquiry__itemValues____columns__title__stockUnit__id": "Unité stock", "@sage/xtrem-stock/pages__stock_valuation_inquiry__itemValues____columns__title__stockValue": "Valeur stock", "@sage/xtrem-stock/pages__stock_valuation_inquiry__itemValues____columns__title__unitCost": "Coût unitaire", "@sage/xtrem-stock/pages__stock_valuation_inquiry__itemValues____columns__title__valuationMethod": "Nature dépense", "@sage/xtrem-stock/pages__stock_valuation_inquiry__itemValues____columns__title__variance": "<PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_valuation_inquiry__mainBlock____title": "Résultats", "@sage/xtrem-stock/pages__stock_valuation_inquiry__runStockValuation____title": "Opération", "@sage/xtrem-stock/pages__stock_valuation_inquiry__search": "<PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_valuation_inquiry__sites____columns__title__id": "Code", "@sage/xtrem-stock/pages__stock_valuation_inquiry__sites____columns__title__name": "Nom", "@sage/xtrem-stock/pages__stock_valuation_inquiry__sites____placeholder": "Sélectionner ...", "@sage/xtrem-stock/pages__stock_valuation_inquiry__sites____title": "Site", "@sage/xtrem-stock/pages__stock_valuation_inquiry__valuationDate____title": "Date", "@sage/xtrem-stock/pages__stock_valuation_request__notification_request_sent": "Demande de valorisation de stock envoyée", "@sage/xtrem-stock/pages__stock_valuation_request__notification_valuation_failed": "Échec de la valorisation de stock", "@sage/xtrem-stock/pages__stock_valuation_request__notification_valuation_finished": "Valorisation de stock finalisée", "@sage/xtrem-stock/pages__stock_value_change____navigationPanel__listItem__itemDescription__title": "Description de l'article", "@sage/xtrem-stock/pages__stock_value_change____navigationPanel__listItem__itemId__title": "Code article", "@sage/xtrem-stock/pages__stock_value_change____navigationPanel__listItem__line2__title": "Nom site", "@sage/xtrem-stock/pages__stock_value_change____navigationPanel__listItem__line2Right__title": "Date", "@sage/xtrem-stock/pages__stock_value_change____navigationPanel__listItem__line3__title": "Article", "@sage/xtrem-stock/pages__stock_value_change____navigationPanel__listItem__siteID__title": "Code site", "@sage/xtrem-stock/pages__stock_value_change____navigationPanel__listItem__title__title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_value_change____navigationPanel__listItem__titleRight__title": "Statut de stock", "@sage/xtrem-stock/pages__stock_value_change____objectTypePlural": "Changements de valeur du stock", "@sage/xtrem-stock/pages__stock_value_change____objectTypeSingular": "Changement de la valeur du stock", "@sage/xtrem-stock/pages__stock_value_change____title": "Changement de la valeur du stock", "@sage/xtrem-stock/pages__stock_value_change__addAverageCost____title": "Ajouter", "@sage/xtrem-stock/pages__stock_value_change__defaultDimension____title": "Paramétrer les sections par défaut", "@sage/xtrem-stock/pages__stock_value_change__description____title": "Description", "@sage/xtrem-stock/pages__stock_value_change__duplication_among_added_lines": "Modifier ou supprimer les lignes en double.", "@sage/xtrem-stock/pages__stock_value_change__goToSysNotificationPage____title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_value_change__item____columns__columns__stockUnit__description__title": "Nom", "@sage/xtrem-stock/pages__stock_value_change__item____columns__title__category__name": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_value_change__item____columns__title__stockUnit__description": "Unité stock", "@sage/xtrem-stock/pages__stock_value_change__item____lookupDialogTitle": "Sélectionner l'article", "@sage/xtrem-stock/pages__stock_value_change__item____title": "Article", "@sage/xtrem-stock/pages__stock_value_change__lines____columns__title__amount": "<PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_value_change__lines____columns__title__fifoCost__amount": "<PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_value_change__lines____columns__title__fifoCost__effectiveDate": "Date effective", "@sage/xtrem-stock/pages__stock_value_change__lines____columns__title__fifoCost__nonAbsorbedAmount": "Montant non-absorbé", "@sage/xtrem-stock/pages__stock_value_change__lines____columns__title__fifoCost__receiptDocumentLine__documentNumber": "Ré<PERSON>", "@sage/xtrem-stock/pages__stock_value_change__lines____columns__title__fifoCost__receiptQuantity": "<PERSON><PERSON> r<PERSON>", "@sage/xtrem-stock/pages__stock_value_change__lines____columns__title__fifoCost__remainingQuantity": "<PERSON><PERSON> restante", "@sage/xtrem-stock/pages__stock_value_change__lines____columns__title__fifoCost__sequence": "Compteur", "@sage/xtrem-stock/pages__stock_value_change__lines____columns__title__newAmount": "Nouveau montant", "@sage/xtrem-stock/pages__stock_value_change__lines____columns__title__newUnitCost": "Nouveau coût unitaire", "@sage/xtrem-stock/pages__stock_value_change__lines____columns__title__quantity": "Quantité", "@sage/xtrem-stock/pages__stock_value_change__lines____columns__title__stockTransactionStatus": "Statut stock", "@sage/xtrem-stock/pages__stock_value_change__lines____columns__title__unitCost": "Coût unitaire", "@sage/xtrem-stock/pages__stock_value_change__lines____dropdownActions__title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_value_change__lines____dropdownActions__title__2": "Sections", "@sage/xtrem-stock/pages__stock_value_change__lines____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_value_change__linesSection____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_value_change__mainSection____title": "Général", "@sage/xtrem-stock/pages__stock_value_change__number____title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_value_change__post____title": "Comptabiliser le stock", "@sage/xtrem-stock/pages__stock_value_change__postedDate____title": "Date", "@sage/xtrem-stock/pages__stock_value_change__postingDetails____columns__title__financeIntegrationAppRecordId": "Réf. intégration comptable", "@sage/xtrem-stock/pages__stock_value_change__postingDetails____columns__title__postingStatus": "Statut", "@sage/xtrem-stock/pages__stock_value_change__postingDetails____columns__title__targetDocumentNumber": "N° doc", "@sage/xtrem-stock/pages__stock_value_change__postingDetails____columns__title__targetDocumentType": "Type doc", "@sage/xtrem-stock/pages__stock_value_change__postingDetails____title": "Résultats", "@sage/xtrem-stock/pages__stock_value_change__postingMessageBlock____title": "<PERSON><PERSON><PERSON> de l'erreur", "@sage/xtrem-stock/pages__stock_value_change__postingSection____title": "Comptabilisation", "@sage/xtrem-stock/pages__stock_value_change__repost____title": "Recomptabiliser", "@sage/xtrem-stock/pages__stock_value_change__repost_errors": "Erreurs lors de la recomptabilisation : {{errors}}", "@sage/xtrem-stock/pages__stock_value_change__save_warnings": "Avertissements lors de l'enregistrement:", "@sage/xtrem-stock/pages__stock_value_change__saveStockValueChange____title": "Enregistrer", "@sage/xtrem-stock/pages__stock_value_change__selectFromFifo____title": "Ajouter à partir de la pile FIFO", "@sage/xtrem-stock/pages__stock_value_change__site____lookupDialogTitle": "Sélectionner le site", "@sage/xtrem-stock/pages__stock_value_change__site____placeholder": "Sélectionner...", "@sage/xtrem-stock/pages__stock_value_change__site____title": "Site", "@sage/xtrem-stock/pages__stock_value_change__stockTransactionStatus____title": "Statut de stock", "@sage/xtrem-stock/pages__stock_value_change__totalQuantityInStock____title": "Physique", "@sage/xtrem-stock/pages__stock_value_change__totalValueOfStock____title": "<PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_value_change__unitCost____title": "Coût unitaire", "@sage/xtrem-stock/pages__stock_value_change__valuationMethod____title": "Méthode de valorisation", "@sage/xtrem-stock/pages__stock_value_correction____navigationPanel__listItem__description__title": "Description", "@sage/xtrem-stock/pages__stock_value_correction____navigationPanel__listItem__effectiveDate__title": "Date", "@sage/xtrem-stock/pages__stock_value_correction____navigationPanel__listItem__reasonCode__title": "<PERSON><PERSON><PERSON> de régu<PERSON>", "@sage/xtrem-stock/pages__stock_value_correction____navigationPanel__listItem__stockSite__title": "Site", "@sage/xtrem-stock/pages__stock_value_correction____navigationPanel__listItem__stockTransactionStatus__title": "Statut de stock", "@sage/xtrem-stock/pages__stock_value_correction____navigationPanel__listItem__title__title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_value_correction____objectTypePlural": "Corrections de la valeur de stock", "@sage/xtrem-stock/pages__stock_value_correction____objectTypeSingular": "Correction de la valeur de stock", "@sage/xtrem-stock/pages__stock_value_correction____title": "Correction de la valeur de stock", "@sage/xtrem-stock/pages__stock_value_correction___correctedDocumentType____title": "Type de document", "@sage/xtrem-stock/pages__stock_value_correction__addAdjustLine____title": "<PERSON><PERSON><PERSON> ligne", "@sage/xtrem-stock/pages__stock_value_correction__defaultDimension____title": "Paramétrer les sections par défaut", "@sage/xtrem-stock/pages__stock_value_correction__description____title": "Description", "@sage/xtrem-stock/pages__stock_value_correction__effectiveDate____title": "Date", "@sage/xtrem-stock/pages__stock_value_correction__lines____columns__columns__item__name__columns__title__decimalDigits": "Décimales", "@sage/xtrem-stock/pages__stock_value_correction__lines____columns__columns__item__name__columns__title__id": "Code", "@sage/xtrem-stock/pages__stock_value_correction__lines____columns__columns__item__name__columns__title__name": "Nom", "@sage/xtrem-stock/pages__stock_value_correction__lines____columns__columns__item__name__columns__title__symbol": "Symbole", "@sage/xtrem-stock/pages__stock_value_correction__lines____columns__columns__item__name__title": "Nom", "@sage/xtrem-stock/pages__stock_value_correction__lines____columns__columns__item__name__title__2": "Code", "@sage/xtrem-stock/pages__stock_value_correction__lines____columns__columns__item__name__title__3": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_value_correction__lines____columns__columns__title___sortValue": "N° ligne", "@sage/xtrem-stock/pages__stock_value_correction__lines____columns__columns__title__document__number": "N° document", "@sage/xtrem-stock/pages__stock_value_correction__lines____columns__columns__title__orderCost": "Coût ordre", "@sage/xtrem-stock/pages__stock_value_correction__lines____columns__lookupDialogTitle__item__name": "Sélectionner l'article", "@sage/xtrem-stock/pages__stock_value_correction__lines____columns__placeholder__item__name": "Sélectionner...", "@sage/xtrem-stock/pages__stock_value_correction__lines____columns__title": "Ligne document", "@sage/xtrem-stock/pages__stock_value_correction__lines____columns__title__item__name": "Article", "@sage/xtrem-stock/pages__stock_value_correction__lines____columns__title__newUnitCost": "Nouveau coût ordre", "@sage/xtrem-stock/pages__stock_value_correction__lines____columns__title__orderCost": "Coût ordre", "@sage/xtrem-stock/pages__stock_value_correction__lines____columns__title__stockTransactionStatus": "Statut stock", "@sage/xtrem-stock/pages__stock_value_correction__lines____columns__title__unitCost": "Coût actuel", "@sage/xtrem-stock/pages__stock_value_correction__lines____dropdownActions__title": "Sections", "@sage/xtrem-stock/pages__stock_value_correction__lines____dropdownActions__title__2": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_value_correction__lines____title": "Lignes de documents", "@sage/xtrem-stock/pages__stock_value_correction__mainSection____title": "Général", "@sage/xtrem-stock/pages__stock_value_correction__number____title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_value_correction__post____title": "Comptabi<PERSON>er", "@sage/xtrem-stock/pages__stock_value_correction__purchaseReceiptLines____columns__columns__item__name__columns__title__decimalDigits": "Décimales", "@sage/xtrem-stock/pages__stock_value_correction__purchaseReceiptLines____columns__columns__item__name__columns__title__id": "Code", "@sage/xtrem-stock/pages__stock_value_correction__purchaseReceiptLines____columns__columns__item__name__columns__title__name": "Nom", "@sage/xtrem-stock/pages__stock_value_correction__purchaseReceiptLines____columns__columns__item__name__title": "Nom", "@sage/xtrem-stock/pages__stock_value_correction__purchaseReceiptLines____columns__columns__item__name__title__2": "Code", "@sage/xtrem-stock/pages__stock_value_correction__purchaseReceiptLines____columns__columns__item__name__title__3": "Unité", "@sage/xtrem-stock/pages__stock_value_correction__purchaseReceiptLines____columns__columns__item__name__title__4": "Gestion lots", "@sage/xtrem-stock/pages__stock_value_correction__purchaseReceiptLines____columns__columns__title___sortValue": "N° ligne", "@sage/xtrem-stock/pages__stock_value_correction__purchaseReceiptLines____columns__columns__title__document__number": "N° doc.", "@sage/xtrem-stock/pages__stock_value_correction__purchaseReceiptLines____columns__placeholder__item__name": "Sélectionner ...", "@sage/xtrem-stock/pages__stock_value_correction__purchaseReceiptLines____columns__title": "Ligne document", "@sage/xtrem-stock/pages__stock_value_correction__purchaseReceiptLines____columns__title__item__name": "Article", "@sage/xtrem-stock/pages__stock_value_correction__purchaseReceiptLines____columns__title__newUnitCost": "Nouveau coût unitaire", "@sage/xtrem-stock/pages__stock_value_correction__purchaseReceiptLines____columns__title__stockTransactionStatus": "Statut stock", "@sage/xtrem-stock/pages__stock_value_correction__purchaseReceiptLines____columns__title__unitCost": "Coût unitaire", "@sage/xtrem-stock/pages__stock_value_correction__purchaseReceiptLines____dropdownActions__title": "Sections", "@sage/xtrem-stock/pages__stock_value_correction__purchaseReceiptLines____dropdownActions__title__2": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_value_correction__purchaseReceiptLines____title": "Lignes de documents", "@sage/xtrem-stock/pages__stock_value_correction__reasonCode____columns__title__id": "Code", "@sage/xtrem-stock/pages__stock_value_correction__reasonCode____columns__title__name": "Nom", "@sage/xtrem-stock/pages__stock_value_correction__reasonCode____columns__title__stockValueCorrection": "Correction de valeur", "@sage/xtrem-stock/pages__stock_value_correction__reasonCode____lookupDialogTitle": "Sélectionner le code motif", "@sage/xtrem-stock/pages__stock_value_correction__reasonCode____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_value_correction__saveStockValueCorrection____title": "Enregistrer", "@sage/xtrem-stock/pages__stock_value_correction__stockReceiptLines____columns__columns__columns__document__number__title": "Site", "@sage/xtrem-stock/pages__stock_value_correction__stockReceiptLines____columns__columns__item__name__columns__title__decimalDigits": "Décimales", "@sage/xtrem-stock/pages__stock_value_correction__stockReceiptLines____columns__columns__item__name__columns__title__id": "Code", "@sage/xtrem-stock/pages__stock_value_correction__stockReceiptLines____columns__columns__item__name__columns__title__name": "Nom", "@sage/xtrem-stock/pages__stock_value_correction__stockReceiptLines____columns__columns__item__name__title": "Nom", "@sage/xtrem-stock/pages__stock_value_correction__stockReceiptLines____columns__columns__item__name__title__2": "Code", "@sage/xtrem-stock/pages__stock_value_correction__stockReceiptLines____columns__columns__item__name__title__3": "Unité", "@sage/xtrem-stock/pages__stock_value_correction__stockReceiptLines____columns__columns__item__name__title__4": "Gestion lots", "@sage/xtrem-stock/pages__stock_value_correction__stockReceiptLines____columns__columns__title___sortValue": "N° ligne", "@sage/xtrem-stock/pages__stock_value_correction__stockReceiptLines____columns__columns__title__document__number": "N° document", "@sage/xtrem-stock/pages__stock_value_correction__stockReceiptLines____columns__placeholder__item__name": "Sélectionner ...", "@sage/xtrem-stock/pages__stock_value_correction__stockReceiptLines____columns__title": "Ligne document", "@sage/xtrem-stock/pages__stock_value_correction__stockReceiptLines____columns__title__item__name": "Article", "@sage/xtrem-stock/pages__stock_value_correction__stockReceiptLines____columns__title__newUnitCost": "Nouveau coût unitaire", "@sage/xtrem-stock/pages__stock_value_correction__stockReceiptLines____columns__title__stockTransactionStatus": "Statut stock", "@sage/xtrem-stock/pages__stock_value_correction__stockReceiptLines____columns__title__unitCost": "Coût unitaire", "@sage/xtrem-stock/pages__stock_value_correction__stockReceiptLines____dropdownActions__title": "Sections", "@sage/xtrem-stock/pages__stock_value_correction__stockReceiptLines____dropdownActions__title__2": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_value_correction__stockReceiptLines____title": "Lignes de documents", "@sage/xtrem-stock/pages__stock_value_correction__stockSite____columns__title__id": "Code", "@sage/xtrem-stock/pages__stock_value_correction__stockSite____columns__title__name": "Nom", "@sage/xtrem-stock/pages__stock_value_correction__stockSite____lookupDialogTitle": "Sélectionner le site", "@sage/xtrem-stock/pages__stock_value_correction__stockSite____placeholder": "Sélectionner...", "@sage/xtrem-stock/pages__stock_value_correction__stockSite____title": "Site", "@sage/xtrem-stock/pages__stock_value_correction__stockTransactionStatus____title": "Statut de stock", "@sage/xtrem-stock/pages__stock-adjustment-detail-panel__notification__The_adjustment_quantity_is_mandatory": "Renseignez la quantité de régularisation.", "@sage/xtrem-stock/pages__stock-entry-detail-panel__notification__The_location_is_mandatory": "Renseignez l'emplacement.", "@sage/xtrem-stock/pages__stock-entry-detail-panel__notification__The_stock_status_is_mandatory": "Renseigner le statut de stock.", "@sage/xtrem-stock/pages__stock-receipt__notification__The_lot_is_mandatory": "Renseignez le lot.", "@sage/xtrem-stock/pages_sidebar_tab_title_information": "Informations", "@sage/xtrem-stock/pages-confirm-cancel": "Annuler", "@sage/xtrem-stock/pages-confirm-delete": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-stock/permission__create__name": "<PERSON><PERSON><PERSON>", "@sage/xtrem-stock/permission__create_item_site_costs_from_cost_roll_up_results__name": "Créer des coûts article-site à partir des résultats de calculs de coût", "@sage/xtrem-stock/permission__delete__name": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-stock/permission__finance_integration_check__name": "Contrôle intégration finance", "@sage/xtrem-stock/permission__manage__name": "<PERSON><PERSON><PERSON>", "@sage/xtrem-stock/permission__manage_cost__name": "<PERSON><PERSON><PERSON> les coûts", "@sage/xtrem-stock/permission__post__name": "Comptabi<PERSON>er", "@sage/xtrem-stock/permission__post_to_stock__name": "Comptabiliser en stock", "@sage/xtrem-stock/permission__read__name": "<PERSON><PERSON>", "@sage/xtrem-stock/permission__repost__name": "Recomptabiliser", "@sage/xtrem-stock/permission__start__name": "D<PERSON>but", "@sage/xtrem-stock/permission__sync_stock_value_change__name": "Synchronisation du changement de la valeur du stock", "@sage/xtrem-stock/permission__update__name": "Mettre à jour", "@sage/xtrem-stock/permission__update_attributes_and_dimensions__name": "Mettre à jour les attributs et les sections", "@sage/xtrem-stock/search": "Recherche", "@sage/xtrem-stock/search-count": "Rechercher ({{#if plus}}+{{/if}}{{nb}})", "@sage/xtrem-stock/standard_cost_roll_up_calculation__stop_calculation": "Calcul de coût standard arrêté à {{stopTime}}.", "@sage/xtrem-stock/standard_cost_roll_up_calculation_complete": "Calcul de coût standard achevé", "@sage/xtrem-stock/standard_cost_roll_up_calculation_completed": "Calcul de coût standard accompli", "@sage/xtrem-stock/standard_cost_roll_up_calculation_failed": "Échec du calcul de coût standard.", "@sage/xtrem-stock/standard_cost_roll_up_calculation_processing_items": "Le calcul de coût standard est en cours d'exécution.", "@sage/xtrem-stock/standard_cost_roll_up_calculation_start": "Début de calcul de coût standard", "@sage/xtrem-stock/standard_cost_roll_up_calculation_updating_sub_assemblies": "Le calcul de coût standard est en train de mettre à jour les sous-ensembles.", "@sage/xtrem-stock/stock_count_pages__stock_count_print": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-stock/stock_value_change_from_item_site_cost": "Changement de la valeur de stock issu de l'article-site", "@sage/xtrem-stock/stock_value_change_from_item_site_cost_complete": "Le changement de la valeur de stock issu de l'article-site est terminé.", "@sage/xtrem-stock/stock_value_change_from_item_site_cost_saved": "Le changement de la valeur de stock issu de l'article-site est enregistré.", "@sage/xtrem-stock/stock_value_change_from_item_site_cost_start": "Le changement de la valeur de stock issu de l'article-site a débuté.", "@sage/xtrem-stock/stock_value_change_from_item_site_cost_stop": "Arr<PERSON><PERSON> du changement des valeurs de stock le {{stopDate}} "}