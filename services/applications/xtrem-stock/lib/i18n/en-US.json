{"@sage/xtrem-stock/activity__cost_roll_up_input_set__name": "Cost roll up input set", "@sage/xtrem-stock/activity__stock_adjustment__name": "Stock adjustment", "@sage/xtrem-stock/activity__stock_change__name": "Stock change", "@sage/xtrem-stock/activity__stock_count__name": "Stock count", "@sage/xtrem-stock/activity__stock_issue__name": "Stock issue", "@sage/xtrem-stock/activity__stock_receipt__name": "Stock receipt", "@sage/xtrem-stock/activity__stock_reorder__name": "Stock reorder", "@sage/xtrem-stock/activity__stock_valuation_input_set__name": "Stock valuation input set", "@sage/xtrem-stock/activity__stock_value_change__name": "Stock value change", "@sage/xtrem-stock/class__allocation-engine__system_error_during_allocation_process": "The allocation process was interrupted: {{errorMessage}}", "@sage/xtrem-stock/create_items_create": "Create", "@sage/xtrem-stock/data_types__adjustable_document_type_enum__name": "Adjustable document type enum", "@sage/xtrem-stock/data_types__standard_cost_roll_up_result_line_status_enum__name": "Standard cost roll up result line status enum", "@sage/xtrem-stock/data_types__standard_cost_roll_up_status_enum__name": "Standard cost roll up status enum", "@sage/xtrem-stock/data_types__stock_adjustment_display_status_enum__name": "Stock adjustment display status enum", "@sage/xtrem-stock/data_types__stock_count_line_status_enum__name": "Stock count line status enum", "@sage/xtrem-stock/data_types__stock_count_status_enum__name": "Stock count status enum", "@sage/xtrem-stock/data_types__stock_issue_display_status_enum__name": "Stock issue display status enum", "@sage/xtrem-stock/data_types__stock_issue_line_display_status_enum__name": "Stock issue line display status enum", "@sage/xtrem-stock/data_types__stock_receipt_display_status_enum__name": "Stock receipt display status enum", "@sage/xtrem-stock/data_types__stock_receipt_line_display_status_enum__name": "Stock receipt line display status enum", "@sage/xtrem-stock/data_types__stock_valuation_status_enum__name": "Stock valuation status enum", "@sage/xtrem-stock/data_types__virtual_location_need_enum__name": "Virtual location need enum", "@sage/xtrem-stock/edit-create-line": "Add new line", "@sage/xtrem-stock/enums__adjustable_document_type__purchaseReceipt": "Purchase receipt", "@sage/xtrem-stock/enums__adjustable_document_type__stockReceipt": "Stock receipt", "@sage/xtrem-stock/enums__standard_cost_roll_up_result_line_status__created": "Created", "@sage/xtrem-stock/enums__standard_cost_roll_up_result_line_status__error": "Error", "@sage/xtrem-stock/enums__standard_cost_roll_up_result_line_status__inProgress": "In progress", "@sage/xtrem-stock/enums__standard_cost_roll_up_result_line_status__pending": "Pending", "@sage/xtrem-stock/enums__standard_cost_roll_up_result_line_status__updated": "Updated", "@sage/xtrem-stock/enums__standard_cost_roll_up_status__completed": "Completed", "@sage/xtrem-stock/enums__standard_cost_roll_up_status__draft": "Draft", "@sage/xtrem-stock/enums__standard_cost_roll_up_status__error": "Error", "@sage/xtrem-stock/enums__standard_cost_roll_up_status__inProgress": "In progress", "@sage/xtrem-stock/enums__stock_adjustment_display_status__adjusted": "Adjusted", "@sage/xtrem-stock/enums__stock_adjustment_display_status__detailsEntered": "Details entered", "@sage/xtrem-stock/enums__stock_adjustment_display_status__detailsRequired": "Details required", "@sage/xtrem-stock/enums__stock_adjustment_display_status__stockPostingError": "Stock posting error", "@sage/xtrem-stock/enums__stock_adjustment_display_status__stockPostingInProgress": "Stock posting in progress", "@sage/xtrem-stock/enums__stock_count_line_status__counted": "Counted", "@sage/xtrem-stock/enums__stock_count_line_status__countInProgress": "Stock count in progress", "@sage/xtrem-stock/enums__stock_count_line_status__excluded": "Excluded", "@sage/xtrem-stock/enums__stock_count_line_status__toBeCounted": "To be counted", "@sage/xtrem-stock/enums__stock_count_status__closed": "Closed", "@sage/xtrem-stock/enums__stock_count_status__counted": "Counted", "@sage/xtrem-stock/enums__stock_count_status__countInProgress": "Count in progress", "@sage/xtrem-stock/enums__stock_count_status__draft": "Draft", "@sage/xtrem-stock/enums__stock_count_status__toBeCounted": "To be counted", "@sage/xtrem-stock/enums__stock_issue_display_status__detailsEntered": "Details entered", "@sage/xtrem-stock/enums__stock_issue_display_status__detailsRequired": "Details required", "@sage/xtrem-stock/enums__stock_issue_display_status__issued": "Issued", "@sage/xtrem-stock/enums__stock_issue_display_status__stockPostingError": "Stock posting error", "@sage/xtrem-stock/enums__stock_issue_display_status__stockPostingInProgress": "Stock posting in progress", "@sage/xtrem-stock/enums__stock_issue_line_display_status__detailsEntered": "Details entered", "@sage/xtrem-stock/enums__stock_issue_line_display_status__detailsRequired": "Details required", "@sage/xtrem-stock/enums__stock_issue_line_display_status__issued": "Issued", "@sage/xtrem-stock/enums__stock_issue_line_display_status__stockPostingError": "Stock posting error", "@sage/xtrem-stock/enums__stock_issue_line_display_status__stockPostingInProgress": "Stock posting in progress", "@sage/xtrem-stock/enums__stock_receipt_display_status__detailsEntered": "Details entered", "@sage/xtrem-stock/enums__stock_receipt_display_status__detailsRequired": "Details required", "@sage/xtrem-stock/enums__stock_receipt_display_status__received": "Received", "@sage/xtrem-stock/enums__stock_receipt_display_status__stockPostingError": "Stock posting error", "@sage/xtrem-stock/enums__stock_receipt_display_status__stockPostingInProgress": "Stock posting in progress", "@sage/xtrem-stock/enums__stock_receipt_line_display_status__detailsEntered": "Details entered", "@sage/xtrem-stock/enums__stock_receipt_line_display_status__detailsRequired": "Details required", "@sage/xtrem-stock/enums__stock_receipt_line_display_status__received": "Received", "@sage/xtrem-stock/enums__stock_receipt_line_display_status__stockPostingError": "Stock posting error", "@sage/xtrem-stock/enums__stock_receipt_line_display_status__stockPostingInProgress": "Stock posting in progress", "@sage/xtrem-stock/enums__stock_valuation_status__completed": "Completed", "@sage/xtrem-stock/enums__stock_valuation_status__draft": "Draft", "@sage/xtrem-stock/enums__stock_valuation_status__error": "Error", "@sage/xtrem-stock/enums__stock_valuation_status__inProgress": "In progress", "@sage/xtrem-stock/enums__virtual_location_need__transfer": "Transfer", "@sage/xtrem-stock/events/control__location_for_wrong_site": "The location should be at the {{site}} site.", "@sage/xtrem-stock/events/control__location_missing": "The location is mandatory.", "@sage/xtrem-stock/events/control__location_not_managed": "Locations are not managed for the {{site}} site.", "@sage/xtrem-stock/function__stock_count_lib__line_already_exists_in_a_not_closed_stock-count": "The line already exists in the stock count {{number}}.", "@sage/xtrem-stock/functions__lot-lib__no_lot_information_created": "No lot information created.", "@sage/xtrem-stock/functions__lot-lib__no_lot_information_received": "No lot information received.", "@sage/xtrem-stock/functions__lot-lib__no_lot_sequence_number_definied": "There is no lot sequence number defined for the {{item}} item.", "@sage/xtrem-stock/functions__stock__location_mandatory_no_default": "The location is mandatory and there is no default location defined for this stock site.", "@sage/xtrem-stock/functions__stock__stock_change_quantity_must_be_greater_thant_0": "You cannot create a stock change with a quantity of 0.", "@sage/xtrem-stock/functions__stock__stock_record_not_found": "Stock record not found. The quantity cannot be taken.", "@sage/xtrem-stock/functions__stock__stock_record_not_found_during_stock_change": "The entered stock information does not exist.", "@sage/xtrem-stock/functions__stock__stock_status_mandatory_no_default": "The stock status is mandatory and there is no default stock status defined for this stock site.", "@sage/xtrem-stock/functions__stock__stock_update_failed": "Stock update failed. Check your stock update parameters.", "@sage/xtrem-stock/functions__stock_engine__no_lot_information_received": "No lot information received.", "@sage/xtrem-stock/functions__stock-detail-lib__item_not_managed_on_site": "The item is not managed on the receiving site.", "@sage/xtrem-stock/functions__stock-engine__lot_passed_for_an_item_not_lot_manage": "The item {{item}} is not lot-managed. You can only post a stock detail with lot information for an item that is lot-managed.", "@sage/xtrem-stock/functions__stock-engine__not_enough_stock": "There is not enough stock. Check your parameters:\nItem {{item}}\nSite {{site}}\nStock unit {{stockUnit}}\nLot {{lot}}\nLocation {{location}}\nStock status {{stockStatus}}\nOwner {{owner}}", "@sage/xtrem-stock/functions__stock-engine__not_enough_stock_allocation": "You should have allocated stock to post this document.", "@sage/xtrem-stock/functions__stock-engine__not_enough_stock_details": "Set default dimensions", "@sage/xtrem-stock/functions__stock-engine__serial_number_already_in_stock": "One of the serial numbers used on item {{item}} is already in stock. Check your serial numbers.", "@sage/xtrem-stock/functions__stock-engine__stock_update_failed_with_parameters": "Stock update failed. Check your parameters:\nItem {{item}}\nSite {{site}}\nStock unit {{stockUnit}}\nLot {{lot}}\nLocation {{location}}\nStock status {{stockStatus}}\nOwner {{owner}}", "@sage/xtrem-stock/functions__stock-engine__stock_value_update_failed_with_parameters": "Stock value update failed. Check your parameters:\nItem {{item}}\nSite {{site}}\nQuantity {{quantity}}", "@sage/xtrem-stock/node__stock_count__resend_notification_for_finance": "Resending finance notification for stock count: {{stockCount}}", "@sage/xtrem-stock/node__stock_issue__resend_notification_for_finance": "Resending finance notification for stock issue: {{stockIssueNumber}}", "@sage/xtrem-stock/node__stock_receipt__resend_notification_for_finance": "Resending finance notification for stock receipt number {{stockNumber}}", "@sage/xtrem-stock/node__stock_value_change__resend_notification_for_finance": "Resending finance notification for stock value change: {{stockValueChange}}", "@sage/xtrem-stock/node-extensions__component_extension__property__availableQuantityInStockUnit": "Available quantity in stock unit", "@sage/xtrem-stock/node-extensions__component_extension__property__hasStockShortage": "Has stock shortage", "@sage/xtrem-stock/node-extensions__component_extension__property__stockOnHand": "Stock on hand", "@sage/xtrem-stock/node-extensions__component_extension__property__stockShortageInStockUnit": "Stock shortage in stock unit", "@sage/xtrem-stock/node-extensions__item_extension__asyncMutation__createTestItems": "Create test items", "@sage/xtrem-stock/node-extensions__item_extension__asyncMutation__createTestItems__failed": "Create test items failed.", "@sage/xtrem-stock/node-extensions__item_extension__asyncMutation__createTestItems__parameter__itemId": "Item ID", "@sage/xtrem-stock/node-extensions__item_extension__asyncMutation__createTestItems__parameter__quantity": "Quantity", "@sage/xtrem-stock/node-extensions__item_extension__asyncMutation__createTestItems__parameter__stockCreation": "Stock creation", "@sage/xtrem-stock/node-extensions__item_site_cost_extension__asyncMutation__standardCostRollUpCalculation": "Standard cost roll up calculation", "@sage/xtrem-stock/node-extensions__item_site_cost_extension__asyncMutation__standardCostRollUpCalculation__failed": "Standard cost roll up calculation failed.", "@sage/xtrem-stock/node-extensions__item_site_cost_extension__asyncMutation__standardCostRollUpCalculation__parameter__inputSet": "Input set", "@sage/xtrem-stock/node-extensions__item_site_cost_extension__asyncMutation__syncStockValueChange": "Synchronization stock value change", "@sage/xtrem-stock/node-extensions__item_site_cost_extension__asyncMutation__syncStockValueChange__failed": "Sync stock value change failed.", "@sage/xtrem-stock/node-extensions__item_site_cost_extension__error_message": "{{errorMessage}}: [Item: {{itemId}} - Site: {{siteId}}]", "@sage/xtrem-stock/node-extensions__item_site_cost_extension__property__analyticalData": "Analytical data", "@sage/xtrem-stock/node-extensions__item_site_cost_extension__property__stockValueChange": "Stock value change", "@sage/xtrem-stock/node-extensions__item_site_cost_extension__property__storedAttributes": "Stored attributes", "@sage/xtrem-stock/node-extensions__item_site_cost_extension__property__storedDimensions": "Stored dimensions", "@sage/xtrem-stock/node-extensions__item_site_cost_extension__success_cost_creation_status_message": "Item-site cost created: [Item: {{itemId}} - Site: {{siteId}}]", "@sage/xtrem-stock/node-extensions__item_site_cost_extension__success_cost_update_status_error_message": "Item-site cost exists and cannot be updated: [Item: {{itemId}} - Site: {{siteId}}]", "@sage/xtrem-stock/node-extensions__item_site_cost_extension__success_cost_update_status_message": "Item-site cost updated: [Item: {{itemId}} - Site: {{siteId}}]", "@sage/xtrem-stock/node-extensions__item_site_extension__property__countStockRecords": "Count stock records", "@sage/xtrem-stock/node-extensions__item_site_extension__property__hasStockRecords": "Has stock records", "@sage/xtrem-stock/node-extensions__item_site_extension__property__lastCountDate": "Last count date", "@sage/xtrem-stock/node-extensions__item_site_extension__query__getStockValuation": "Get stock valuation", "@sage/xtrem-stock/node-extensions__item_site_extension__query__getStockValuation__failed": "Get stock valuation failed.", "@sage/xtrem-stock/node-extensions__item_site_extension__query__getStockValuation__parameter__searchCriteria": "Search criteria", "@sage/xtrem-stock/node-extensions__mrp_calculation_extension__asyncMutation__deleteOldMrpCalculations": "Delete old MRP calculations", "@sage/xtrem-stock/node-extensions__mrp_calculation_extension__asyncMutation__deleteOldMrpCalculations__failed": "Delete old mrp calculations failed.", "@sage/xtrem-stock/node-extensions__mrp_calculation_extension__asyncMutation__deleteOldMrpCalculations__parameter__numberOfDaysToKeep": "Number of days to keep", "@sage/xtrem-stock/node-extensions__mrp_calculation_extension__asyncMutation__deleteOldMrpCalculations__parameter__numberOfRecordsToKeep": "Number of records to keep", "@sage/xtrem-stock/node-extensions__stock_adjustment_detail_extension__property__item": "<PERSON><PERSON>", "@sage/xtrem-stock/node-extensions__stock_adjustment_detail_extension__property__location": "Location", "@sage/xtrem-stock/node-extensions__stock_adjustment_detail_extension__property__owner": "Owner", "@sage/xtrem-stock/node-extensions__stock_adjustment_detail_extension__property__site": "Site", "@sage/xtrem-stock/node-extensions__stock_adjustment_detail_extension__property__status": "Status", "@sage/xtrem-stock/node-extensions__stock_adjustment_detail_extension__property__stockDetailLot": "Stock detail lot", "@sage/xtrem-stock/node-extensions__stock_adjustment_detail_extension__property__stockUnit": "Stock unit", "@sage/xtrem-stock/node-extensions__stock_change_detail_extension__property__startingSerialNumber": "Starting serial number", "@sage/xtrem-stock/node-extensions__stock_detail_lot_extension__property__lot": "Lot", "@sage/xtrem-stock/node-extensions__stock_detail_lot_extension__property__lotNumber": "Lot number", "@sage/xtrem-stock/node-extensions__stock_extension__asyncMutation__adjust": "Adjust", "@sage/xtrem-stock/node-extensions__stock_extension__asyncMutation__adjust__failed": "Adjust failed.", "@sage/xtrem-stock/node-extensions__stock_extension__asyncMutation__adjust__parameter__payload": "Payload", "@sage/xtrem-stock/node-extensions__stock_extension__asyncMutation__change": "Change", "@sage/xtrem-stock/node-extensions__stock_extension__asyncMutation__change__failed": "Change failed.", "@sage/xtrem-stock/node-extensions__stock_extension__asyncMutation__change__parameter__payload": "Payload", "@sage/xtrem-stock/node-extensions__stock_extension__asyncMutation__changeValue": "Change value", "@sage/xtrem-stock/node-extensions__stock_extension__asyncMutation__changeValue__failed": "Change value failed.", "@sage/xtrem-stock/node-extensions__stock_extension__asyncMutation__changeValue__parameter__payload": "Payload", "@sage/xtrem-stock/node-extensions__stock_extension__asyncMutation__correct": "Correct", "@sage/xtrem-stock/node-extensions__stock_extension__asyncMutation__correct__failed": "Correct failed.", "@sage/xtrem-stock/node-extensions__stock_extension__asyncMutation__correct__parameter__payload": "Payload", "@sage/xtrem-stock/node-extensions__stock_extension__asyncMutation__issue": "Issue", "@sage/xtrem-stock/node-extensions__stock_extension__asyncMutation__issue__failed": "Issue failed.", "@sage/xtrem-stock/node-extensions__stock_extension__asyncMutation__issue__parameter__payload": "Payload", "@sage/xtrem-stock/node-extensions__stock_extension__asyncMutation__receive": "Receive", "@sage/xtrem-stock/node-extensions__stock_extension__asyncMutation__receive__failed": "Receive failed.", "@sage/xtrem-stock/node-extensions__stock_extension__asyncMutation__receive__parameter__payload": "Payload", "@sage/xtrem-stock/node-extensions__stock_extension__asyncMutation__transfer": "Transfer", "@sage/xtrem-stock/node-extensions__stock_extension__asyncMutation__transfer__failed": "Transfer failed.", "@sage/xtrem-stock/node-extensions__stock_extension__asyncMutation__transfer__parameter__payload": "Payload", "@sage/xtrem-stock/node-extensions__stock_extension__property__isStockCountingInProgress": "Is stock counting in progress", "@sage/xtrem-stock/node-extensions__stock_extension__property__stockCountingInProgress": "Stock counting in progress", "@sage/xtrem-stock/node-extensions__stock_issue_detail_extension__property__startingSerialNumber": "Starting serial number", "@sage/xtrem-stock/nodes__allocation_listener__asyncMutation__allocate": "Allocate", "@sage/xtrem-stock/nodes__allocation_listener__asyncMutation__allocate__failed": "Allocate failed.", "@sage/xtrem-stock/nodes__allocation_listener__asyncMutation__allocate__parameter__payload": "Payload", "@sage/xtrem-stock/nodes__allocation_listener__asyncMutation__allocate__parameter__stockAllocationParameters": "Stock allocation parameters", "@sage/xtrem-stock/nodes__allocation_listener__asyncMutation__deallocate": "Deallocate", "@sage/xtrem-stock/nodes__allocation_listener__asyncMutation__deallocate__parameter__payload": "Payload", "@sage/xtrem-stock/nodes__allocation_listener__asyncMutation__deallocate__parameter__stockAllocationParameters": "Stock allocation parameters", "@sage/xtrem-stock/nodes__allocation_listener__node_name": "Allocation listener", "@sage/xtrem-stock/nodes__cost_roll_up_input_set__asyncMutation__asyncExport": "Export", "@sage/xtrem-stock/nodes__cost_roll_up_input_set__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-stock/nodes__cost_roll_up_input_set__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-stock/nodes__cost_roll_up_input_set__mutation__updateAttributesAndDimensions": "Update attributes and dimensions", "@sage/xtrem-stock/nodes__cost_roll_up_input_set__mutation__updateAttributesAndDimensions__failed": "Update attributes and dimensions failed.", "@sage/xtrem-stock/nodes__cost_roll_up_input_set__mutation__updateAttributesAndDimensions__parameter__costRollUpInputSet": "Cost roll up input set", "@sage/xtrem-stock/nodes__cost_roll_up_input_set__mutation__updateAttributesAndDimensions__parameter__storedAttributes": "Stored attributes", "@sage/xtrem-stock/nodes__cost_roll_up_input_set__mutation__updateAttributesAndDimensions__parameter__storedDimensions": "Stored dimensions", "@sage/xtrem-stock/nodes__cost_roll_up_input_set__node_name": "Cost roll up input set", "@sage/xtrem-stock/nodes__cost_roll_up_input_set__property__commodityCode": "Commodity code", "@sage/xtrem-stock/nodes__cost_roll_up_input_set__property__costCategory": "Cost category", "@sage/xtrem-stock/nodes__cost_roll_up_input_set__property__fromDate": "From date", "@sage/xtrem-stock/nodes__cost_roll_up_input_set__property__includesRouting": "Includes routing", "@sage/xtrem-stock/nodes__cost_roll_up_input_set__property__itemCategory": "Item category", "@sage/xtrem-stock/nodes__cost_roll_up_input_set__property__items": "Items", "@sage/xtrem-stock/nodes__cost_roll_up_input_set__property__quantity": "Quantity", "@sage/xtrem-stock/nodes__cost_roll_up_input_set__property__resultLines": "Result lines", "@sage/xtrem-stock/nodes__cost_roll_up_input_set__property__site": "Site", "@sage/xtrem-stock/nodes__cost_roll_up_input_set__property__status": "Status", "@sage/xtrem-stock/nodes__cost_roll_up_input_set__property__user": "User", "@sage/xtrem-stock/nodes__cost_roll_up_input_set__property__usesComponentStandardCost": "Uses component standard cost", "@sage/xtrem-stock/nodes__cost_roll_up_result_line__asyncMutation__asyncExport": "Export", "@sage/xtrem-stock/nodes__cost_roll_up_result_line__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-stock/nodes__cost_roll_up_result_line__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-stock/nodes__cost_roll_up_result_line__bulkMutation__createItemSiteCostsFromCostRollUpResults": "Create item site costs from cost roll up results", "@sage/xtrem-stock/nodes__cost_roll_up_result_line__bulkMutation__createItemSiteCostsFromCostRollUpResults__failed": "Create item site costs from cost roll up results failed.", "@sage/xtrem-stock/nodes__cost_roll_up_result_line__item_site_cost_creation_finished": "Item-site cost creation finished.", "@sage/xtrem-stock/nodes__cost_roll_up_result_line__no_line_processed_message": "Error while processing lines. Selection is empty.", "@sage/xtrem-stock/nodes__cost_roll_up_result_line__node_name": "Cost roll up result line", "@sage/xtrem-stock/nodes__cost_roll_up_result_line__property__analyticalData": "Analytical data", "@sage/xtrem-stock/nodes__cost_roll_up_result_line__property__creationErrorMessage": "Creation error message", "@sage/xtrem-stock/nodes__cost_roll_up_result_line__property__creationStatus": "Creation status", "@sage/xtrem-stock/nodes__cost_roll_up_result_line__property__currentLaborCost": "Current labor cost", "@sage/xtrem-stock/nodes__cost_roll_up_result_line__property__currentMachineCost": "Current machine cost", "@sage/xtrem-stock/nodes__cost_roll_up_result_line__property__currentMaterialCost": "Current material cost", "@sage/xtrem-stock/nodes__cost_roll_up_result_line__property__currentToolCost": "Current tool cost", "@sage/xtrem-stock/nodes__cost_roll_up_result_line__property__currentTotalCost": "Current total cost", "@sage/xtrem-stock/nodes__cost_roll_up_result_line__property__inputSet": "Input set", "@sage/xtrem-stock/nodes__cost_roll_up_result_line__property__item": "<PERSON><PERSON>", "@sage/xtrem-stock/nodes__cost_roll_up_result_line__property__laborCost": "Labor cost", "@sage/xtrem-stock/nodes__cost_roll_up_result_line__property__machineCost": "Machine cost", "@sage/xtrem-stock/nodes__cost_roll_up_result_line__property__materialCost": "Material cost", "@sage/xtrem-stock/nodes__cost_roll_up_result_line__property__storedAttributes": "Stored attributes", "@sage/xtrem-stock/nodes__cost_roll_up_result_line__property__storedDimensions": "Stored dimensions", "@sage/xtrem-stock/nodes__cost_roll_up_result_line__property__subAssemblyLines": "Subassembly lines", "@sage/xtrem-stock/nodes__cost_roll_up_result_line__property__toolCost": "Tool cost", "@sage/xtrem-stock/nodes__cost_roll_up_result_line__property__totalCost": "Total cost", "@sage/xtrem-stock/nodes__cost_roll_up_sub_assembly__asyncMutation__asyncExport": "Export", "@sage/xtrem-stock/nodes__cost_roll_up_sub_assembly__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-stock/nodes__cost_roll_up_sub_assembly__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-stock/nodes__cost_roll_up_sub_assembly__node_name": "Cost roll up sub-assembly", "@sage/xtrem-stock/nodes__cost_roll_up_sub_assembly__property__calculationQuantity": "Calculation quantity", "@sage/xtrem-stock/nodes__cost_roll_up_sub_assembly__property__componentNumber": "Component number", "@sage/xtrem-stock/nodes__cost_roll_up_sub_assembly__property__currentLaborCost": "Current labor cost", "@sage/xtrem-stock/nodes__cost_roll_up_sub_assembly__property__currentMachineCost": "Current machine cost", "@sage/xtrem-stock/nodes__cost_roll_up_sub_assembly__property__currentMaterialCost": "Current material cost", "@sage/xtrem-stock/nodes__cost_roll_up_sub_assembly__property__currentToolCost": "Current tool cost", "@sage/xtrem-stock/nodes__cost_roll_up_sub_assembly__property__currentTotalCost": "Current total cost", "@sage/xtrem-stock/nodes__cost_roll_up_sub_assembly__property__item": "<PERSON><PERSON>", "@sage/xtrem-stock/nodes__cost_roll_up_sub_assembly__property__laborCost": "Labor cost", "@sage/xtrem-stock/nodes__cost_roll_up_sub_assembly__property__machineCost": "Machine cost", "@sage/xtrem-stock/nodes__cost_roll_up_sub_assembly__property__materialCost": "Material cost", "@sage/xtrem-stock/nodes__cost_roll_up_sub_assembly__property__resultLine": "Result line", "@sage/xtrem-stock/nodes__cost_roll_up_sub_assembly__property__stockUnit": "", "@sage/xtrem-stock/nodes__cost_roll_up_sub_assembly__property__toolCost": "Tool cost", "@sage/xtrem-stock/nodes__cost_roll_up_sub_assembly__property__totalCost": "Total cost", "@sage/xtrem-stock/nodes__fake_stock_issue__node_name": "Fake stock issue", "@sage/xtrem-stock/nodes__fake_stock_issue__property__lines": "Lines", "@sage/xtrem-stock/nodes__fake_stock_issue__property__number": "Number", "@sage/xtrem-stock/nodes__fake_stock_issue__property__stockTransactionStatus": "Stock transaction status", "@sage/xtrem-stock/nodes__fake_stock_issue_line__node_name": "Fake stock issue line", "@sage/xtrem-stock/nodes__fake_stock_issue_line__property__allocationStatus": "Allocation status", "@sage/xtrem-stock/nodes__fake_stock_issue_line__property__document": "Document", "@sage/xtrem-stock/nodes__fake_stock_issue_line__property__item": "<PERSON><PERSON>", "@sage/xtrem-stock/nodes__fake_stock_issue_line__property__lineNumber": "Line number", "@sage/xtrem-stock/nodes__fake_stock_issue_line__property__quantityAllocated": "Quantity allocated", "@sage/xtrem-stock/nodes__fake_stock_issue_line__property__quantityInStockUnit": "Quantity in stock unit", "@sage/xtrem-stock/nodes__fake_stock_issue_line__property__remainingQuantityToAllocate": "Remaining quantity to allocate", "@sage/xtrem-stock/nodes__fake_stock_issue_line__property__stockAllocations": "Stock allocations", "@sage/xtrem-stock/nodes__fake_stock_issue_line__property__stockDetails": "Stock details", "@sage/xtrem-stock/nodes__fake_stock_issue_line__property__stockTransactions": "Stock transactions", "@sage/xtrem-stock/nodes__fake_stock_issue_line__property__stockTransactionStatus": "Stock transaction status", "@sage/xtrem-stock/nodes__fake_stock_receipt__node_name": "Fake stock receipt", "@sage/xtrem-stock/nodes__fake_stock_receipt__property__lines": "Lines", "@sage/xtrem-stock/nodes__fake_stock_receipt__property__number": "Number", "@sage/xtrem-stock/nodes__fake_stock_receipt__property__stockTransactionStatus": "Stock transaction status", "@sage/xtrem-stock/nodes__fake_stock_receipt_line__node_name": "Fake stock receipt line", "@sage/xtrem-stock/nodes__fake_stock_receipt_line__property__document": "Document", "@sage/xtrem-stock/nodes__fake_stock_receipt_line__property__lineNumber": "Line number", "@sage/xtrem-stock/nodes__fake_stock_receipt_line__property__quantityInStockUnit": "Quantity in stock unit", "@sage/xtrem-stock/nodes__fake_stock_receipt_line__property__stockDetails": "Stock details", "@sage/xtrem-stock/nodes__fake_stock_receipt_line__property__stockTransactions": "Stock transaction", "@sage/xtrem-stock/nodes__fake_stock_receipt_line__property__stockTransactionStatus": "Stock transaction status", "@sage/xtrem-stock/nodes__item_site_cost__success_notification_title": "Standard cost roll up calculation complete.", "@sage/xtrem-stock/nodes__item-site-cost__failed_deletion_impossible_if_transactions": "Delete not allowed. The item-site cost start date is equal to the current date and a stock value change exists for this item-site cost.", "@sage/xtrem-stock/nodes__item-site-extension__item_site_not_stock_managed": "This item-site is not stock managed", "@sage/xtrem-stock/nodes__mrp_calculation-extension__delete_old_mrp_calculations": "Deleted old MRP calculations: {{number}}.", "@sage/xtrem-stock/nodes__mrp_calculation-extension__deleting_old_mrp_calculations": "Deleting old MRP calculations with a date earlier than: {{deletionDate}}.", "@sage/xtrem-stock/nodes__mrp_calculation-extension__no_old_mrp_calculations": "No old MRP calculations to delete.", "@sage/xtrem-stock/nodes__mrp_calculation-extension__start_delete_old_mrp_calculations": "Starting to delete old MRP calculations.", "@sage/xtrem-stock/nodes__stock_adjustment__asyncMutation__asyncExport": "Export", "@sage/xtrem-stock/nodes__stock_adjustment__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-stock/nodes__stock_adjustment__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-stock/nodes__stock_adjustment__mutation__postToStock": "Post to stock", "@sage/xtrem-stock/nodes__stock_adjustment__mutation__postToStock__failed": "Post to stock failed.", "@sage/xtrem-stock/nodes__stock_adjustment__mutation__postToStock__parameter__documentIds": "Document IDs", "@sage/xtrem-stock/nodes__stock_adjustment__mutation__repost": "Repost", "@sage/xtrem-stock/nodes__stock_adjustment__mutation__repost__failed": "Repost failed.", "@sage/xtrem-stock/nodes__stock_adjustment__mutation__repost__parameter__documentLines": "Document lines", "@sage/xtrem-stock/nodes__stock_adjustment__mutation__repost__parameter__stockAdjustment": "Stock adjustment", "@sage/xtrem-stock/nodes__stock_adjustment__mutation__resendNotificationForFinance": "Resend notification for finance", "@sage/xtrem-stock/nodes__stock_adjustment__mutation__resendNotificationForFinance__failed": "Resend notification for finance failed.", "@sage/xtrem-stock/nodes__stock_adjustment__mutation__resendNotificationForFinance__parameter__stockAdjustment": "Stock adjustment", "@sage/xtrem-stock/nodes__stock_adjustment__mutation__resynchronizeStockTransactionStatus": "Resynchronize stock transaction status", "@sage/xtrem-stock/nodes__stock_adjustment__mutation__resynchronizeStockTransactionStatus__failed": "Resynchronize stock transaction status failed.", "@sage/xtrem-stock/nodes__stock_adjustment__mutation__resynchronizeStockTransactionStatus__parameter__stockAdjustment": "Stock adjustment", "@sage/xtrem-stock/nodes__stock_adjustment__node_name": "Stock adjustment", "@sage/xtrem-stock/nodes__stock_adjustment__property__description": "Description", "@sage/xtrem-stock/nodes__stock_adjustment__property__displayStatus": "Display status", "@sage/xtrem-stock/nodes__stock_adjustment__property__documentDate": "Document date", "@sage/xtrem-stock/nodes__stock_adjustment__property__effectiveDate": "Effective date", "@sage/xtrem-stock/nodes__stock_adjustment__property__financeIntegrationStatus": "Finance integration status", "@sage/xtrem-stock/nodes__stock_adjustment__property__financialSite": "Financial site", "@sage/xtrem-stock/nodes__stock_adjustment__property__forceUpdateForFinance": "Force update for finance", "@sage/xtrem-stock/nodes__stock_adjustment__property__forceUpdateForStock": "Force update for stock", "@sage/xtrem-stock/nodes__stock_adjustment__property__isSetDimensionsMainListHidden": "Is set dimensions main list hidden", "@sage/xtrem-stock/nodes__stock_adjustment__property__lines": "Lines", "@sage/xtrem-stock/nodes__stock_adjustment__property__number": "Number", "@sage/xtrem-stock/nodes__stock_adjustment__property__postingDetails": "Posting details", "@sage/xtrem-stock/nodes__stock_adjustment__property__reasonCode": "Reason code", "@sage/xtrem-stock/nodes__stock_adjustment__property__status": "Status", "@sage/xtrem-stock/nodes__stock_adjustment__property__stockSite": "Stock site", "@sage/xtrem-stock/nodes__stock_adjustment__property__stockTransactionStatus": "Stock transaction status", "@sage/xtrem-stock/nodes__stock_adjustment__property__transactionCurrency": "Transaction currency", "@sage/xtrem-stock/nodes__stock_adjustment_detail_extension__lot_mandatory": "You need to select a lot for the item: {{itemId}}. Stock count {{number}}, line {{lineNumber}}.", "@sage/xtrem-stock/nodes__stock_adjustment_line__asyncMutation__asyncExport": "Export", "@sage/xtrem-stock/nodes__stock_adjustment_line__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-stock/nodes__stock_adjustment_line__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-stock/nodes__stock_adjustment_line__cannot_update_completed_line": "An Adjusted line cannot be updated.", "@sage/xtrem-stock/nodes__stock_adjustment_line__incorrect_valuation_method": "The {{item}} item must be linked to an average unit cost valuation method.", "@sage/xtrem-stock/nodes__stock_adjustment_line__mutation__setDimension": "Set dimension", "@sage/xtrem-stock/nodes__stock_adjustment_line__mutation__setDimension__failed": "Set dimension failed.", "@sage/xtrem-stock/nodes__stock_adjustment_line__mutation__setDimension__parameter__stockAdjustmentLine": "Stock adjustment line", "@sage/xtrem-stock/nodes__stock_adjustment_line__mutation__setDimension__parameter__storedAttributes": "Stored attributes", "@sage/xtrem-stock/nodes__stock_adjustment_line__mutation__setDimension__parameter__storedDimensions": "Stored dimensions", "@sage/xtrem-stock/nodes__stock_adjustment_line__node_name": "Stock adjustment line", "@sage/xtrem-stock/nodes__stock_adjustment_line__property__adjustmentQuantityInStockUnit": "Adjustment quantity in stock unit", "@sage/xtrem-stock/nodes__stock_adjustment_line__property__analyticalData": "Analytical data", "@sage/xtrem-stock/nodes__stock_adjustment_line__property__computedAttributes": "Computed attributes", "@sage/xtrem-stock/nodes__stock_adjustment_line__property__displayStatus": "Display status", "@sage/xtrem-stock/nodes__stock_adjustment_line__property__document": "Document", "@sage/xtrem-stock/nodes__stock_adjustment_line__property__documentId": "Document ID", "@sage/xtrem-stock/nodes__stock_adjustment_line__property__documentNumber": "Document number", "@sage/xtrem-stock/nodes__stock_adjustment_line__property__item": "<PERSON><PERSON>", "@sage/xtrem-stock/nodes__stock_adjustment_line__property__itemSite": "Item-site", "@sage/xtrem-stock/nodes__stock_adjustment_line__property__jsonStockDetails": "JSON stock details", "@sage/xtrem-stock/nodes__stock_adjustment_line__property__location": "Location", "@sage/xtrem-stock/nodes__stock_adjustment_line__property__lot": "Lot", "@sage/xtrem-stock/nodes__stock_adjustment_line__property__newStockQuantity": "New stock quantity", "@sage/xtrem-stock/nodes__stock_adjustment_line__property__newStockValue": "New stock value", "@sage/xtrem-stock/nodes__stock_adjustment_line__property__newUnitCost": "New unit cost", "@sage/xtrem-stock/nodes__stock_adjustment_line__property__orderCost": "Order cost", "@sage/xtrem-stock/nodes__stock_adjustment_line__property__quantityInStockUnit": "Quantity in stock unit", "@sage/xtrem-stock/nodes__stock_adjustment_line__property__site": "Site", "@sage/xtrem-stock/nodes__stock_adjustment_line__property__stockDetails": "Stock details", "@sage/xtrem-stock/nodes__stock_adjustment_line__property__stockDetailStatus": "Stock detail status", "@sage/xtrem-stock/nodes__stock_adjustment_line__property__stockMovements": "Stock movements", "@sage/xtrem-stock/nodes__stock_adjustment_line__property__stockStatus": "Stock status", "@sage/xtrem-stock/nodes__stock_adjustment_line__property__stockTransactions": "Stock transaction", "@sage/xtrem-stock/nodes__stock_adjustment_line__property__stockTransactionStatus": "Stock transaction status", "@sage/xtrem-stock/nodes__stock_adjustment_line__property__stockUnit": "Stock unit", "@sage/xtrem-stock/nodes__stock_adjustment_line__property__stockValue": "Stock value", "@sage/xtrem-stock/nodes__stock_adjustment_line__property__storedAttributes": "Stored attributes", "@sage/xtrem-stock/nodes__stock_adjustment_line__property__storedDimensions": "Stored dimensions", "@sage/xtrem-stock/nodes__stock_adjustment_line__property__unitCost": "Unit cost", "@sage/xtrem-stock/nodes__stock_adjustment_line__property__valuedCost": "Valued cost", "@sage/xtrem-stock/nodes__stock_change___duplicated_serial_number": "There are duplicate serial numbers. Make sure that each line has a unique set of serial numbers.", "@sage/xtrem-stock/nodes__stock_change___wrong_number_of_serial_numbers": "The number of serial numbers assigned needs to match the quantity for the line. Add serial numbers to match the quantity.", "@sage/xtrem-stock/nodes__stock_change__asyncMutation__asyncExport": "Export", "@sage/xtrem-stock/nodes__stock_change__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-stock/nodes__stock_change__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-stock/nodes__stock_change__mutation__postToStock": "Post to stock", "@sage/xtrem-stock/nodes__stock_change__mutation__postToStock__failed": "Post to stock failed.", "@sage/xtrem-stock/nodes__stock_change__mutation__postToStock__parameter__documentIds": "Document IDs", "@sage/xtrem-stock/nodes__stock_change__node_name": "Stock change", "@sage/xtrem-stock/nodes__stock_change__property__description": "Description", "@sage/xtrem-stock/nodes__stock_change__property__effectiveDate": "Effective date", "@sage/xtrem-stock/nodes__stock_change__property__forceUpdateForStock": "Force update for stock", "@sage/xtrem-stock/nodes__stock_change__property__item": "<PERSON><PERSON>", "@sage/xtrem-stock/nodes__stock_change__property__itemSite": "Item-site", "@sage/xtrem-stock/nodes__stock_change__property__lines": "Lines", "@sage/xtrem-stock/nodes__stock_change__property__location": "Location", "@sage/xtrem-stock/nodes__stock_change__property__lot": "Lot", "@sage/xtrem-stock/nodes__stock_change__property__number": "Number", "@sage/xtrem-stock/nodes__stock_change__property__owner": "Owner", "@sage/xtrem-stock/nodes__stock_change__property__postingError": "Posting error", "@sage/xtrem-stock/nodes__stock_change__property__status": "Status", "@sage/xtrem-stock/nodes__stock_change__property__stockSite": "Stock site", "@sage/xtrem-stock/nodes__stock_change__property__stockStatus": "Stock status", "@sage/xtrem-stock/nodes__stock_change__property__stockTransactionStatus": "Stock transaction status", "@sage/xtrem-stock/nodes__stock_change__property__stockUnit": "Stock unit", "@sage/xtrem-stock/nodes__stock_change_line__asyncMutation__asyncExport": "Export", "@sage/xtrem-stock/nodes__stock_change_line__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-stock/nodes__stock_change_line__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-stock/nodes__stock_change_line__location_site_must_match": "The location site and the stock site must be the same.", "@sage/xtrem-stock/nodes__stock_change_line__no_change": "No change to process on one line. Use a different stock status or location for this line.", "@sage/xtrem-stock/nodes__stock_change_line__node_name": "Stock change line", "@sage/xtrem-stock/nodes__stock_change_line__property__document": "Document", "@sage/xtrem-stock/nodes__stock_change_line__property__documentId": "Document ID", "@sage/xtrem-stock/nodes__stock_change_line__property__documentNumber": "Document number", "@sage/xtrem-stock/nodes__stock_change_line__property__item": "<PERSON><PERSON>", "@sage/xtrem-stock/nodes__stock_change_line__property__jsonStockDetails": "JSON stock details", "@sage/xtrem-stock/nodes__stock_change_line__property__location": "Location", "@sage/xtrem-stock/nodes__stock_change_line__property__orderCost": "Order cost", "@sage/xtrem-stock/nodes__stock_change_line__property__quantityInStockUnit": "Quantity in stock unit", "@sage/xtrem-stock/nodes__stock_change_line__property__stockDetails": "Stock details", "@sage/xtrem-stock/nodes__stock_change_line__property__stockStatus": "Stock status", "@sage/xtrem-stock/nodes__stock_change_line__property__stockTransactions": "Stock transaction", "@sage/xtrem-stock/nodes__stock_change_line__property__stockTransactionStatus": "Stock transaction status", "@sage/xtrem-stock/nodes__stock_change_line__property__valuedCost": "Valued cost", "@sage/xtrem-stock/nodes__stock_count__asyncMutation__asyncExport": "Export", "@sage/xtrem-stock/nodes__stock_count__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-stock/nodes__stock_count__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-stock/nodes__stock_count__asyncMutation__confirmStockCount": "Confirm stock count", "@sage/xtrem-stock/nodes__stock_count__asyncMutation__confirmStockCount__failed": "Confirm stock count failed.", "@sage/xtrem-stock/nodes__stock_count__asyncMutation__confirmStockCount__parameter__selectedRecords": "Selected records", "@sage/xtrem-stock/nodes__stock_count__asyncMutation__confirmStockCount__parameter__stockCount": "Stock count", "@sage/xtrem-stock/nodes__stock_count__asyncMutation__confirmZeroQuantity": "Confirm zero quantity", "@sage/xtrem-stock/nodes__stock_count__asyncMutation__confirmZeroQuantity__failed": "Confirm zero quantity failed.", "@sage/xtrem-stock/nodes__stock_count__asyncMutation__confirmZeroQuantity__parameter__number": "Number", "@sage/xtrem-stock/nodes__stock_count__cant_repost_stock_count_when_status_is_not_failed": "You can only repost a stock count if the status is 'Failed' or 'Not recorded'.", "@sage/xtrem-stock/nodes__stock_count__closed_confirm_zero_cannot_be_performed": "You cannot update the quantities on a stock count that was closed: stock count {{number}}.", "@sage/xtrem-stock/nodes__stock_count__company_has_no_stock_posting": "Stock has not been posted to this company", "@sage/xtrem-stock/nodes__stock_count__confirm_stock_completed": "Stock count lines created: {{number}}.", "@sage/xtrem-stock/nodes__stock_count__confirm_stock_count_none_stock_records_to_stock_count_line": "Stock count lines created from non-stock records: {{itemSiteCounter}}.", "@sage/xtrem-stock/nodes__stock_count__confirm_stock_count_stock_record_excluded": "The stock record for item {{item}} is excluded. It is already in another stock count: {{existingRecord}}..", "@sage/xtrem-stock/nodes__stock_count__confirm_stock_count_stock_records_to_stock_count_line": "Stock count lines created from stock records: {{stockCounter}}.", "@sage/xtrem-stock/nodes__stock_count__counted_quantity_less_than_allocated_quantity": "For stock count {{number}}, the counted quantity {{countedQuantity}} for {{item}} cannot be less than the allocated quantity {{totalAllocated}}.", "@sage/xtrem-stock/nodes__stock_count__counted_stock_record_not_found": "The stock count record for item {{item}} has changed since the stock count started. Please exclude this line from the stock count.", "@sage/xtrem-stock/nodes__stock_count__counting_is_not_finished_yet": "You cannot post a stock count when the count is not finished: stock count {{number}}.", "@sage/xtrem-stock/nodes__stock_count__deletion_forbidden": "You cannot delete the stock count.", "@sage/xtrem-stock/nodes__stock_count__document_was_posted": "The stock count was posted.", "@sage/xtrem-stock/nodes__stock_count__end_confirm_zero": "The confirm zero quantities process finished for the stock count: {{number}}. Lines updated: {{recordsUpdated}}.", "@sage/xtrem-stock/nodes__stock_count__mutation__postToStock": "Post to stock", "@sage/xtrem-stock/nodes__stock_count__mutation__postToStock__failed": "Post to stock failed.", "@sage/xtrem-stock/nodes__stock_count__mutation__postToStock__parameter__documentIds": "Document IDs", "@sage/xtrem-stock/nodes__stock_count__mutation__repost": "Repost", "@sage/xtrem-stock/nodes__stock_count__mutation__repost__failed": "Repost failed.", "@sage/xtrem-stock/nodes__stock_count__mutation__repost__parameter__documentLines": "Document lines", "@sage/xtrem-stock/nodes__stock_count__mutation__repost__parameter__stockCount": "Stock count", "@sage/xtrem-stock/nodes__stock_count__mutation__resendNotificationForFinance": "Resend notification for finance", "@sage/xtrem-stock/nodes__stock_count__mutation__resendNotificationForFinance__failed": "Resend notification for finance failed.", "@sage/xtrem-stock/nodes__stock_count__mutation__resendNotificationForFinance__parameter__stockCount": "Stock count", "@sage/xtrem-stock/nodes__stock_count__mutation__start": "Start", "@sage/xtrem-stock/nodes__stock_count__mutation__start__failed": "Start failed.", "@sage/xtrem-stock/nodes__stock_count__mutation__start__parameter__document": "Document", "@sage/xtrem-stock/nodes__stock_count__no_records_selected": "No records selected for stock count {{number}}.", "@sage/xtrem-stock/nodes__stock_count__node_name": "Stock count", "@sage/xtrem-stock/nodes__stock_count__property__categories": "Categories", "@sage/xtrem-stock/nodes__stock_count__property__concurrentStockCountLines": "Concurrent stock count lines", "@sage/xtrem-stock/nodes__stock_count__property__counter": "Counter", "@sage/xtrem-stock/nodes__stock_count__property__description": "Description", "@sage/xtrem-stock/nodes__stock_count__property__documentDate": "Document date", "@sage/xtrem-stock/nodes__stock_count__property__effectiveDate": "Effective date", "@sage/xtrem-stock/nodes__stock_count__property__financeIntegrationStatus": "Finance integration status", "@sage/xtrem-stock/nodes__stock_count__property__financialSite": "Financial site", "@sage/xtrem-stock/nodes__stock_count__property__forceUpdateForStock": "Force update for stock", "@sage/xtrem-stock/nodes__stock_count__property__fromItem": "From item", "@sage/xtrem-stock/nodes__stock_count__property__hasExpiryManagementInLines": "Has expiry management in lines", "@sage/xtrem-stock/nodes__stock_count__property__hasLotInLines": "Has lot in lines", "@sage/xtrem-stock/nodes__stock_count__property__hasStockRecords": "Has stock records", "@sage/xtrem-stock/nodes__stock_count__property__hasSublotInLines": "Has sublot in lines", "@sage/xtrem-stock/nodes__stock_count__property__itemSites": "Item-sites", "@sage/xtrem-stock/nodes__stock_count__property__lastCountDate": "Last count date", "@sage/xtrem-stock/nodes__stock_count__property__lines": "Lines", "@sage/xtrem-stock/nodes__stock_count__property__locations": "Locations", "@sage/xtrem-stock/nodes__stock_count__property__number": "Number", "@sage/xtrem-stock/nodes__stock_count__property__postingDetails": "Posting details", "@sage/xtrem-stock/nodes__stock_count__property__status": "Status", "@sage/xtrem-stock/nodes__stock_count__property__stockSite": "Stock site", "@sage/xtrem-stock/nodes__stock_count__property__stockTransactionStatus": "Stock transaction status", "@sage/xtrem-stock/nodes__stock_count__property__toItem": "To item", "@sage/xtrem-stock/nodes__stock_count__property__transactionCurrency": "Transaction currency", "@sage/xtrem-stock/nodes__stock_count__property__zones": "Zones", "@sage/xtrem-stock/nodes__stock_count__query__existingStockCountLine": "Existing stock count line", "@sage/xtrem-stock/nodes__stock_count__query__existingStockCountLine__failed": "Existing stock count line failed.", "@sage/xtrem-stock/nodes__stock_count__query__existingStockCountLine__parameter__lotCreateData": "Lot creation data", "@sage/xtrem-stock/nodes__stock_count__query__existingStockCountLine__parameter__searchCriteria": "Search criteria", "@sage/xtrem-stock/nodes__stock_count__query__existingStockCountLine__parameter__throwErrorIfExist": "Throw error if exists", "@sage/xtrem-stock/nodes__stock_count__start_confirm_stock_count": "The create stock count lines process has started: {{number}}.", "@sage/xtrem-stock/nodes__stock_count__start_confirm_zero": "The confirm zero quantities process started for the stock count: {{number}}.", "@sage/xtrem-stock/nodes__stock_count__to_be_counted_confirm_zero_cannot_be_performed": "The stock count has not started yet: {{number}}. Start the count to update the quantities.", "@sage/xtrem-stock/nodes__stock_count_line___duplicated_serial_number": "There are duplicate serial numbers. Make sure that each line has a unique set of serial numbers.", "@sage/xtrem-stock/nodes__stock_count_line__asyncMutation__asyncExport": "Export", "@sage/xtrem-stock/nodes__stock_count_line__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-stock/nodes__stock_count_line__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-stock/nodes__stock_count_line__cannot_add_line_if_counting_closed": "You cannot add a line if the stock count is Closed.", "@sage/xtrem-stock/nodes__stock_count_line__delete_not_allowed": "A line that was added during the start of the stock count cannot be deleted. Stock count {{number}}, line {{lineNumber}}.", "@sage/xtrem-stock/nodes__stock_count_line__expiration_date_mandatory": "You need to select an expiration date for the item: {{itemId}}, and for the lot: {{lotId}}.", "@sage/xtrem-stock/nodes__stock_count_line__forbid_addition_serialized_managed_item": "A stock line for a serialized item cannot be added to an existing stock count.", "@sage/xtrem-stock/nodes__stock_count_line__item_cannot_be_modified": "The item cannot be modified. Stock count {{number}}, line {{lineNumber}}.", "@sage/xtrem-stock/nodes__stock_count_line__item_of_stock_detail_must_be_the_same_as_item_of_stock_count_line": "The stock detail item and stock count line item need to be the same. Stock count {{number}}, line {{lineNumber}}.", "@sage/xtrem-stock/nodes__stock_count_line__line_posted_delete_not_allowed": "The stock count line cannot be deleted. It was already posted. Stock count {{number}}, line {{lineNumber}}.", "@sage/xtrem-stock/nodes__stock_count_line__location_cannot_be_modified": "The location cannot be modified. Stock count {{number}}, line {{lineNumber}}.", "@sage/xtrem-stock/nodes__stock_count_line__location_of_stock_detail_must_be_the_same_as_location_of_stock_count_line": "The stock detail location and stock count line location need to be the same. Stock count {{number}}, line {{lineNumber}}.", "@sage/xtrem-stock/nodes__stock_count_line__lot_cannot_be_modified": "The lot cannot be modified. Stock count {{number}}, line {{lineNumber}}.", "@sage/xtrem-stock/nodes__stock_count_line__lot_mandatory": "You need to select a lot for the item: {{itemId}}.", "@sage/xtrem-stock/nodes__stock_count_line__node_name": "Stock count line", "@sage/xtrem-stock/nodes__stock_count_line__owner_cannot_be_modified": "The owner cannot be modified. Stock count {{number}}, line {{lineNumber}}.", "@sage/xtrem-stock/nodes__stock_count_line__owner_of_stock_detail_must_be_the_same_as_owner_of_stock_count_line": "The owner of the stock detail and the stock count line need to be the same. Stock count {{number}}, line {{lineNumber}}.", "@sage/xtrem-stock/nodes__stock_count_line__property__adjustmentQuantityInStockUnit": "Adjustment quantity in stock unit", "@sage/xtrem-stock/nodes__stock_count_line__property__allocations": "Allocations", "@sage/xtrem-stock/nodes__stock_count_line__property__analyticalData": "Analytical data", "@sage/xtrem-stock/nodes__stock_count_line__property__canBeDeleted": "Can be deleted", "@sage/xtrem-stock/nodes__stock_count_line__property__canEnterOrderCost": "Can enter order cost", "@sage/xtrem-stock/nodes__stock_count_line__property__computedAttributes": "Computed attributes", "@sage/xtrem-stock/nodes__stock_count_line__property__countedQuantityInStockUnit": "Counted quantity in stock unit", "@sage/xtrem-stock/nodes__stock_count_line__property__countedSerialNumber": "Counted serial number", "@sage/xtrem-stock/nodes__stock_count_line__property__countedSerialNumberPercentage": "Counted serial number percentage", "@sage/xtrem-stock/nodes__stock_count_line__property__document": "Document", "@sage/xtrem-stock/nodes__stock_count_line__property__documentId": "Document ID", "@sage/xtrem-stock/nodes__stock_count_line__property__documentNumber": "Document number", "@sage/xtrem-stock/nodes__stock_count_line__property__forceUpdateForFinance": "Force update for finance", "@sage/xtrem-stock/nodes__stock_count_line__property__forceUpdateForStock": "Force update for Stock", "@sage/xtrem-stock/nodes__stock_count_line__property__hasAllocationError": "Has allocation error", "@sage/xtrem-stock/nodes__stock_count_line__property__isAddedDuringCount": "Is added during count", "@sage/xtrem-stock/nodes__stock_count_line__property__isCreatedFromStockRecord": "Is created from stock record", "@sage/xtrem-stock/nodes__stock_count_line__property__isNonStockItem": "Is non stock item", "@sage/xtrem-stock/nodes__stock_count_line__property__isStockCountCreation": "Is stock count creation", "@sage/xtrem-stock/nodes__stock_count_line__property__item": "<PERSON><PERSON>", "@sage/xtrem-stock/nodes__stock_count_line__property__itemSite": "Item-site", "@sage/xtrem-stock/nodes__stock_count_line__property__jsonSerialNumbers": "JSON serial numbers", "@sage/xtrem-stock/nodes__stock_count_line__property__jsonStockDetails": "JSON stock details", "@sage/xtrem-stock/nodes__stock_count_line__property__location": "Location", "@sage/xtrem-stock/nodes__stock_count_line__property__lot": "Lot", "@sage/xtrem-stock/nodes__stock_count_line__property__newLineOrderCost": "New line order cost", "@sage/xtrem-stock/nodes__stock_count_line__property__owner": "Owner", "@sage/xtrem-stock/nodes__stock_count_line__property__quantityInStockUnit": "Quantity in stock unit", "@sage/xtrem-stock/nodes__stock_count_line__property__quantityVariance": "Quantity variance", "@sage/xtrem-stock/nodes__stock_count_line__property__quantityVariancePercentage": "Quantity variance percentage", "@sage/xtrem-stock/nodes__stock_count_line__property__serialNumbers": "", "@sage/xtrem-stock/nodes__stock_count_line__property__shouldEnterOrderCost": "Should enter order cost", "@sage/xtrem-stock/nodes__stock_count_line__property__site": "Site", "@sage/xtrem-stock/nodes__stock_count_line__property__status": "Status", "@sage/xtrem-stock/nodes__stock_count_line__property__stockCountLineSerialNumbers": "Stock count line serial numbers", "@sage/xtrem-stock/nodes__stock_count_line__property__stockDetail": "Stock detail", "@sage/xtrem-stock/nodes__stock_count_line__property__stockDetails": "Stock details", "@sage/xtrem-stock/nodes__stock_count_line__property__stockMovements": "Stock movements", "@sage/xtrem-stock/nodes__stock_count_line__property__stockRecord": "Stock record", "@sage/xtrem-stock/nodes__stock_count_line__property__stockStatus": "Stock status", "@sage/xtrem-stock/nodes__stock_count_line__property__stockTransactions": "Stock transaction", "@sage/xtrem-stock/nodes__stock_count_line__property__stockTransactionStatus": "Stock transaction status", "@sage/xtrem-stock/nodes__stock_count_line__property__stockUnit": "Stock unit", "@sage/xtrem-stock/nodes__stock_count_line__property__storedAttributes": "Stored attributes", "@sage/xtrem-stock/nodes__stock_count_line__property__storedDimensions": "Stored dimensions", "@sage/xtrem-stock/nodes__stock_count_line__property__totalAllocated": "Total allocated", "@sage/xtrem-stock/nodes__stock_count_line__property__zone": "Zone", "@sage/xtrem-stock/nodes__stock_count_line__query__getStockQuantity": "Get stock quantity", "@sage/xtrem-stock/nodes__stock_count_line__query__getStockQuantity__failed": "Get stock quantity failed.", "@sage/xtrem-stock/nodes__stock_count_line__query__getStockQuantity__parameter__stockCountLine": "Stock count line", "@sage/xtrem-stock/nodes__stock_count_line__selected_quantity_too_high": "The number of selected serial numbers ({{selectedQuantity}}) cannot exceed the counted quantity ({{countedQuantity}}).", "@sage/xtrem-stock/nodes__stock_count_line__site_of_stock_detail_must_be_the_same_as_site_of_stock_count_line": "The stock detail site and stock count line site need to be the same. Stock count {{number}}, line {{lineNumber}}.", "@sage/xtrem-stock/nodes__stock_count_line__status_of_stock_detail_must_be_the_same_as_status_of_stock_count_line": "The stock detail status and stock count line status need to be the same. Stock count {{number}}, line {{lineNumber}}.", "@sage/xtrem-stock/nodes__stock_count_line__stock_detail_inconsistent_with_line": "The stock detail does not match the line.", "@sage/xtrem-stock/nodes__stock_count_line__stock_record_not_found": "Stock record not found. Stock count {{number}}, line {{lineNumber}}.", "@sage/xtrem-stock/nodes__stock_count_line__stock_status_cannot_be_modified": "The stock status cannot be modified. Stock count {{number}}, line {{lineNumber}}.", "@sage/xtrem-stock/nodes__stock_count_line__stock_unit_of_stock_detail_must_be_the_same_as_stock_unit_of_stock_count_line": "The stock unit for the stock detail and the stock count line need to be the same. Stock count {{number}}, line {{lineNumber}}.", "@sage/xtrem-stock/nodes__stock_count_line__sublot_mandatory": "You need to select a sublot for the item: {{itemId}}.", "@sage/xtrem-stock/nodes__stock_count_line__update_forbidden_count_line_posted": "The stockCountLine line {{id}} cannot be updated. It was already posted.", "@sage/xtrem-stock/nodes__stock_count_line_serial_number__asyncMutation__asyncExport": "Export", "@sage/xtrem-stock/nodes__stock_count_line_serial_number__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-stock/nodes__stock_count_line_serial_number__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-stock/nodes__stock_count_line_serial_number__available_error": "Serial numbers available in this range: {{available}}.", "@sage/xtrem-stock/nodes__stock_count_line_serial_number__cannot_delete_count_started": "A stock count line serial number cannot be deleted after the count has started. Item {{itemId}}, serial number {{serialNumber}}.", "@sage/xtrem-stock/nodes__stock_count_line_serial_number__cannot_delete_serial_still_in_stock": "A stock count line serial number cannot be deleted when it is associated to the counted stock record. Item {{itemId}}, serial number {{serialNumber}}.", "@sage/xtrem-stock/nodes__stock_count_line_serial_number__invalid_quantity_error": "The ending serial number cannot be calculated. Check the starting serial number and quantity.", "@sage/xtrem-stock/nodes__stock_count_line_serial_number__invalid_starting_serial_number_error": "The serial number is not assigned to the stock count line. Serial number {{serialNumber}}.", "@sage/xtrem-stock/nodes__stock_count_line_serial_number__node_name": "Stock count line serial number", "@sage/xtrem-stock/nodes__stock_count_line_serial_number__property__isCounted": "Counted", "@sage/xtrem-stock/nodes__stock_count_line_serial_number__property__serialNumber": "Serial number", "@sage/xtrem-stock/nodes__stock_count_line_serial_number__property__stockCountLine": "Stock count line", "@sage/xtrem-stock/nodes__stock_count_line_serial_number__query__getEndingSerialNumber": "Get ending serial number", "@sage/xtrem-stock/nodes__stock_count_line_serial_number__query__getEndingSerialNumber__failed": "Get ending serial number failed.", "@sage/xtrem-stock/nodes__stock_count_line_serial_number__query__getEndingSerialNumber__parameter__quantity": "Quantity", "@sage/xtrem-stock/nodes__stock_count_line_serial_number__query__getEndingSerialNumber__parameter__startingSerialNumber": "Starting serial number", "@sage/xtrem-stock/nodes__stock_count_line_serial_number__query__getEndingSerialNumber__parameter__stockCountLineId": "Stock count line ID", "@sage/xtrem-stock/nodes__stock_count_line_serial_number__serial_number_not_assigned_to_a_stock_record": "The serial number {{serialNumber}} is not assigned to a stock line.", "@sage/xtrem-stock/nodes__stock_count_line_serial_number__stock_count_line_and_serial_number_dont_match": "The stock count line and the serial number {{serialNumber}} do not correspond to the same stock line.", "@sage/xtrem-stock/nodes__stock_count_line_serial_number__zero_quantity_error": "Enter a quantity greater than 0.", "@sage/xtrem-stock/nodes__stock_detail__exceeds_stock_record_quantity": "The quantity of {{qty1}} allocated to stock detail no. {{detailNb}} exceeds the stock record quantity {{qty2}}.", "@sage/xtrem-stock/nodes__stock_detail__stock_record_has_been_deleted": "The stock issue cannot be performed because the stock record has been deleted by another transaction.", "@sage/xtrem-stock/nodes__stock_issue___duplicated_serial_number": "There are duplicate serial numbers. Make sure that each line has a unique set of serial numbers.", "@sage/xtrem-stock/nodes__stock_issue__asyncMutation__asyncExport": "Export", "@sage/xtrem-stock/nodes__stock_issue__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-stock/nodes__stock_issue__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-stock/nodes__stock_issue__mutation__postToStock": "Post to stock", "@sage/xtrem-stock/nodes__stock_issue__mutation__postToStock__failed": "Post to stock failed.", "@sage/xtrem-stock/nodes__stock_issue__mutation__postToStock__parameter__documentIds": "Document IDs", "@sage/xtrem-stock/nodes__stock_issue__mutation__repost": "Repost", "@sage/xtrem-stock/nodes__stock_issue__mutation__repost__failed": "Repost failed.", "@sage/xtrem-stock/nodes__stock_issue__mutation__repost__parameter__documentLines": "Document lines", "@sage/xtrem-stock/nodes__stock_issue__mutation__repost__parameter__stockIssue": "Stock issue", "@sage/xtrem-stock/nodes__stock_issue__mutation__resendNotificationForFinance": "Resend notification for finance", "@sage/xtrem-stock/nodes__stock_issue__mutation__resendNotificationForFinance__failed": "Resend notification for finance failed.", "@sage/xtrem-stock/nodes__stock_issue__mutation__resendNotificationForFinance__parameter__stockIssue": "Stock issue", "@sage/xtrem-stock/nodes__stock_issue__node_name": "Stock issue", "@sage/xtrem-stock/nodes__stock_issue__property__description": "Description", "@sage/xtrem-stock/nodes__stock_issue__property__displayStatus": "Display status", "@sage/xtrem-stock/nodes__stock_issue__property__documentDate": "Document date", "@sage/xtrem-stock/nodes__stock_issue__property__effectiveDate": "Effective date", "@sage/xtrem-stock/nodes__stock_issue__property__financeIntegrationStatus": "Finance integration status", "@sage/xtrem-stock/nodes__stock_issue__property__financialSite": "Financial site", "@sage/xtrem-stock/nodes__stock_issue__property__forceUpdateForStock": "Force update for stock", "@sage/xtrem-stock/nodes__stock_issue__property__isSetDimensionsMainListHidden": "Is set dimensions main list hidden", "@sage/xtrem-stock/nodes__stock_issue__property__lines": "Lines", "@sage/xtrem-stock/nodes__stock_issue__property__number": "Number", "@sage/xtrem-stock/nodes__stock_issue__property__postingDetails": "Posting details", "@sage/xtrem-stock/nodes__stock_issue__property__reasonCode": "Reason code", "@sage/xtrem-stock/nodes__stock_issue__property__status": "Status", "@sage/xtrem-stock/nodes__stock_issue__property__stockSite": "Stock site", "@sage/xtrem-stock/nodes__stock_issue__property__stockTransactionStatus": "Stock transaction status", "@sage/xtrem-stock/nodes__stock_issue__property__transactionCurrency": "Transaction currency", "@sage/xtrem-stock/nodes__stock_issue_line__asyncMutation__asyncExport": "Export", "@sage/xtrem-stock/nodes__stock_issue_line__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-stock/nodes__stock_issue_line__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-stock/nodes__stock_issue_line__mutation__setDimension": "Set dimension", "@sage/xtrem-stock/nodes__stock_issue_line__mutation__setDimension__failed": "Set dimension failed.", "@sage/xtrem-stock/nodes__stock_issue_line__mutation__setDimension__parameter__stockIssueLine": "Stock issue line", "@sage/xtrem-stock/nodes__stock_issue_line__mutation__setDimension__parameter__storedAttributes": "Stored attributes", "@sage/xtrem-stock/nodes__stock_issue_line__mutation__setDimension__parameter__storedDimensions": "Stored dimensions", "@sage/xtrem-stock/nodes__stock_issue_line__node_name": "Stock issue line", "@sage/xtrem-stock/nodes__stock_issue_line__property__analyticalData": "Analytical data", "@sage/xtrem-stock/nodes__stock_issue_line__property__computedAttributes": "Computed attributes", "@sage/xtrem-stock/nodes__stock_issue_line__property__displayStatus": "Display status", "@sage/xtrem-stock/nodes__stock_issue_line__property__document": "Document", "@sage/xtrem-stock/nodes__stock_issue_line__property__documentId": "Document ID", "@sage/xtrem-stock/nodes__stock_issue_line__property__documentNumber": "Document number", "@sage/xtrem-stock/nodes__stock_issue_line__property__item": "<PERSON><PERSON>", "@sage/xtrem-stock/nodes__stock_issue_line__property__itemSite": "Item-site", "@sage/xtrem-stock/nodes__stock_issue_line__property__jsonStockDetails": "JSON stock details", "@sage/xtrem-stock/nodes__stock_issue_line__property__location": "Location", "@sage/xtrem-stock/nodes__stock_issue_line__property__lot": "Lot", "@sage/xtrem-stock/nodes__stock_issue_line__property__orderCost": "Order cost", "@sage/xtrem-stock/nodes__stock_issue_line__property__quantityInStockUnit": "Quantity in stock unit", "@sage/xtrem-stock/nodes__stock_issue_line__property__site": "Site", "@sage/xtrem-stock/nodes__stock_issue_line__property__stockDetails": "Stock details", "@sage/xtrem-stock/nodes__stock_issue_line__property__stockDetailStatus": "Stock detail status", "@sage/xtrem-stock/nodes__stock_issue_line__property__stockMovements": "Stock movements", "@sage/xtrem-stock/nodes__stock_issue_line__property__stockStatus": "Stock status", "@sage/xtrem-stock/nodes__stock_issue_line__property__stockTransactions": "Stock transaction", "@sage/xtrem-stock/nodes__stock_issue_line__property__stockTransactionStatus": "Stock transaction status", "@sage/xtrem-stock/nodes__stock_issue_line__property__stockUnit": "Stock unit", "@sage/xtrem-stock/nodes__stock_issue_line__property__storedAttributes": "Stored attributes", "@sage/xtrem-stock/nodes__stock_issue_line__property__storedDimensions": "Stored dimensions", "@sage/xtrem-stock/nodes__stock_issue_line__property__totalCost": "Total cost", "@sage/xtrem-stock/nodes__stock_issue_line__property__valuedCost": "Valued cost", "@sage/xtrem-stock/nodes__stock_issue_post__stock_details_required": "You need to enter stock details for all lines before you can post.", "@sage/xtrem-stock/nodes__stock_receipt__asyncMutation__asyncExport": "Export", "@sage/xtrem-stock/nodes__stock_receipt__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-stock/nodes__stock_receipt__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-stock/nodes__stock_receipt__asyncMutation__createTestStockReceipt": "Create test stock receipt", "@sage/xtrem-stock/nodes__stock_receipt__asyncMutation__createTestStockReceipt__failed": "Create test stock receipt failed.", "@sage/xtrem-stock/nodes__stock_receipt__asyncMutation__createTestStockReceipt__parameter__data": "Data", "@sage/xtrem-stock/nodes__stock_receipt__mutation__postToStock": "Post to stock", "@sage/xtrem-stock/nodes__stock_receipt__mutation__postToStock__failed": "Post to stock failed.", "@sage/xtrem-stock/nodes__stock_receipt__mutation__postToStock__parameter__documentIds": "Document IDs", "@sage/xtrem-stock/nodes__stock_receipt__mutation__repost": "Repost", "@sage/xtrem-stock/nodes__stock_receipt__mutation__repost__failed": "Repost failed.", "@sage/xtrem-stock/nodes__stock_receipt__mutation__repost__parameter__documentLines": "Document lines", "@sage/xtrem-stock/nodes__stock_receipt__mutation__repost__parameter__stockReceipt": "Stock receipt", "@sage/xtrem-stock/nodes__stock_receipt__mutation__resendNotificationForFinance": "Resend notification for finance", "@sage/xtrem-stock/nodes__stock_receipt__mutation__resendNotificationForFinance__failed": "Resend notification for finance failed.", "@sage/xtrem-stock/nodes__stock_receipt__mutation__resendNotificationForFinance__parameter__stockReceipt": "Stock receipt", "@sage/xtrem-stock/nodes__stock_receipt__node_name": "Stock receipt", "@sage/xtrem-stock/nodes__stock_receipt__property__description": "Description", "@sage/xtrem-stock/nodes__stock_receipt__property__displayStatus": "Display status", "@sage/xtrem-stock/nodes__stock_receipt__property__documentDate": "Document date", "@sage/xtrem-stock/nodes__stock_receipt__property__effectiveDate": "Effective date", "@sage/xtrem-stock/nodes__stock_receipt__property__financeIntegrationStatus": "Finance integration status", "@sage/xtrem-stock/nodes__stock_receipt__property__financialSite": "Financial site", "@sage/xtrem-stock/nodes__stock_receipt__property__forceUpdateForStock": "Force update for stock", "@sage/xtrem-stock/nodes__stock_receipt__property__isSetDimensionsMainListHidden": "Is set dimensions main list hidden", "@sage/xtrem-stock/nodes__stock_receipt__property__lines": "Lines", "@sage/xtrem-stock/nodes__stock_receipt__property__number": "Number", "@sage/xtrem-stock/nodes__stock_receipt__property__postingDetails": "Posting details", "@sage/xtrem-stock/nodes__stock_receipt__property__reasonCode": "Reason code", "@sage/xtrem-stock/nodes__stock_receipt__property__status": "Status", "@sage/xtrem-stock/nodes__stock_receipt__property__stockSite": "Stock site", "@sage/xtrem-stock/nodes__stock_receipt__property__stockTransactionStatus": "Stock transaction status", "@sage/xtrem-stock/nodes__stock_receipt__property__transactionCurrency": "Transaction currency", "@sage/xtrem-stock/nodes__stock_receipt_line__asyncMutation__asyncExport": "Export", "@sage/xtrem-stock/nodes__stock_receipt_line__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-stock/nodes__stock_receipt_line__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-stock/nodes__stock_receipt_line__mutation__setDimension": "Set dimension", "@sage/xtrem-stock/nodes__stock_receipt_line__mutation__setDimension__failed": "Set dimension failed.", "@sage/xtrem-stock/nodes__stock_receipt_line__mutation__setDimension__parameter__stockReceiptLine": "Stock receipt line", "@sage/xtrem-stock/nodes__stock_receipt_line__mutation__setDimension__parameter__storedAttributes": "Stored attributes", "@sage/xtrem-stock/nodes__stock_receipt_line__mutation__setDimension__parameter__storedDimensions": "Stored dimensions", "@sage/xtrem-stock/nodes__stock_receipt_line__node_name": "Stock receipt line", "@sage/xtrem-stock/nodes__stock_receipt_line__property__analyticalData": "Analytical data", "@sage/xtrem-stock/nodes__stock_receipt_line__property__computedAttributes": "Computed attributes", "@sage/xtrem-stock/nodes__stock_receipt_line__property__displayStatus": "Display status", "@sage/xtrem-stock/nodes__stock_receipt_line__property__document": "Document", "@sage/xtrem-stock/nodes__stock_receipt_line__property__documentId": "Document ID", "@sage/xtrem-stock/nodes__stock_receipt_line__property__documentNumber": "Document number", "@sage/xtrem-stock/nodes__stock_receipt_line__property__existingLot": "Existing lot", "@sage/xtrem-stock/nodes__stock_receipt_line__property__item": "<PERSON><PERSON>", "@sage/xtrem-stock/nodes__stock_receipt_line__property__itemSite": "Item-site", "@sage/xtrem-stock/nodes__stock_receipt_line__property__jsonStockDetails": "JSON stock details", "@sage/xtrem-stock/nodes__stock_receipt_line__property__location": "Location", "@sage/xtrem-stock/nodes__stock_receipt_line__property__lotCreateData": "Lot creation data", "@sage/xtrem-stock/nodes__stock_receipt_line__property__orderCost": "Order cost", "@sage/xtrem-stock/nodes__stock_receipt_line__property__quantityInStockUnit": "Quantity in stock unit", "@sage/xtrem-stock/nodes__stock_receipt_line__property__site": "Site", "@sage/xtrem-stock/nodes__stock_receipt_line__property__stockDetails": "Stock details", "@sage/xtrem-stock/nodes__stock_receipt_line__property__stockDetailStatus": "Stock detail status", "@sage/xtrem-stock/nodes__stock_receipt_line__property__stockMovements": "Stock movements", "@sage/xtrem-stock/nodes__stock_receipt_line__property__stockStatus": "Stock status", "@sage/xtrem-stock/nodes__stock_receipt_line__property__stockTransactions": "Stock transaction", "@sage/xtrem-stock/nodes__stock_receipt_line__property__stockTransactionStatus": "Stock transaction status", "@sage/xtrem-stock/nodes__stock_receipt_line__property__stockUnit": "Stock unit", "@sage/xtrem-stock/nodes__stock_receipt_line__property__storedAttributes": "Stored attributes", "@sage/xtrem-stock/nodes__stock_receipt_line__property__storedDimensions": "Stored dimensions", "@sage/xtrem-stock/nodes__stock_receipt_line__property__totalCost": "Total cost", "@sage/xtrem-stock/nodes__stock_receipt_line__property__valuedCost": "Valued cost", "@sage/xtrem-stock/nodes__stock_receipt_node_post__stock_details_required": "You need to enter stock details for all lines before you can post.", "@sage/xtrem-stock/nodes__stock_reorder_calculation_input_set__asyncMutation__asyncExport": "Export", "@sage/xtrem-stock/nodes__stock_reorder_calculation_input_set__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-stock/nodes__stock_reorder_calculation_input_set__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-stock/nodes__stock_reorder_calculation_input_set__asyncMutation__reorderCalculation": "Reorder calculation", "@sage/xtrem-stock/nodes__stock_reorder_calculation_input_set__asyncMutation__reorderCalculation__failed": "Reorder calculation failed.", "@sage/xtrem-stock/nodes__stock_reorder_calculation_input_set__asyncMutation__reorderCalculation__parameter__userId": "User ID", "@sage/xtrem-stock/nodes__stock_reorder_calculation_input_set__node_name": "Stock reorder calculation input set", "@sage/xtrem-stock/nodes__stock_reorder_calculation_input_set__notification_calculation_failed": "Stock reorder calculation failed.", "@sage/xtrem-stock/nodes__stock_reorder_calculation_input_set__notification_calculation_finished": "Stock reorder calculation finished.", "@sage/xtrem-stock/nodes__stock_reorder_calculation_input_set__property__company": "Company", "@sage/xtrem-stock/nodes__stock_reorder_calculation_input_set__property__endDate": "End date", "@sage/xtrem-stock/nodes__stock_reorder_calculation_input_set__property__fromItem": "From item", "@sage/xtrem-stock/nodes__stock_reorder_calculation_input_set__property__lines": "Lines", "@sage/xtrem-stock/nodes__stock_reorder_calculation_input_set__property__reorderType": "Reorder type", "@sage/xtrem-stock/nodes__stock_reorder_calculation_input_set__property__sites": "Sites", "@sage/xtrem-stock/nodes__stock_reorder_calculation_input_set__property__status": "Status", "@sage/xtrem-stock/nodes__stock_reorder_calculation_input_set__property__toItem": "To item", "@sage/xtrem-stock/nodes__stock_reorder_calculation_input_set__property__user": "User", "@sage/xtrem-stock/nodes__stock_reorder_calculation_result_line__asyncMutation__asyncExport": "Export", "@sage/xtrem-stock/nodes__stock_reorder_calculation_result_line__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-stock/nodes__stock_reorder_calculation_result_line__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-stock/nodes__stock_reorder_calculation_result_line__node_name": "Stock reorder calculation result line", "@sage/xtrem-stock/nodes__stock_reorder_calculation_result_line__property__company": "Company", "@sage/xtrem-stock/nodes__stock_reorder_calculation_result_line__property__currency": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-stock/nodes__stock_reorder_calculation_result_line__property__inputSet": "Input set", "@sage/xtrem-stock/nodes__stock_reorder_calculation_result_line__property__itemSite": "Item site", "@sage/xtrem-stock/nodes__stock_reorder_calculation_result_line__property__orderDate": "Order date", "@sage/xtrem-stock/nodes__stock_reorder_calculation_result_line__property__purchaseUnit": "Purchase unit", "@sage/xtrem-stock/nodes__stock_reorder_calculation_result_line__property__quantity": "Quantity", "@sage/xtrem-stock/nodes__stock_reorder_calculation_result_line__property__reorderType": "Reorder type", "@sage/xtrem-stock/nodes__stock_reorder_calculation_result_line__property__stockUnit": "Stock unit", "@sage/xtrem-stock/nodes__stock_reorder_calculation_result_line__property__supplier": "Supplier", "@sage/xtrem-stock/nodes__stock_update_listener__quantity_of_stock_record_to_be_counted_has_changed": "The quantity of the stock record to count has been modified since the stock count has started.\n\n{{stock_identifiers}}", "@sage/xtrem-stock/nodes__stock_update_listener__stock_record_identification": "Item: {{item}}\n\nStatus: {{status}}\n\nLot: {{lot}}\n\nLocation: {{location}}\n\nCurrent quantity in stock: {{quantity}}", "@sage/xtrem-stock/nodes__stock_valuation_input_set__asyncMutation__asyncExport": "Export", "@sage/xtrem-stock/nodes__stock_valuation_input_set__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-stock/nodes__stock_valuation_input_set__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-stock/nodes__stock_valuation_input_set__asyncMutation__stockValuation": "Stock valuation", "@sage/xtrem-stock/nodes__stock_valuation_input_set__asyncMutation__stockValuation__failed": "Stock valuation failed.", "@sage/xtrem-stock/nodes__stock_valuation_input_set__asyncMutation__stockValuation__parameter__userId": "User ID", "@sage/xtrem-stock/nodes__stock_valuation_input_set__node_name": "Stock valuation input set", "@sage/xtrem-stock/nodes__stock_valuation_input_set__notification_valuation_failed": "Stock valuation failed.", "@sage/xtrem-stock/nodes__stock_valuation_input_set__notification_valuation_finished": "Stock valuation finished.", "@sage/xtrem-stock/nodes__stock_valuation_input_set__property__commodityCode": "Commodity code", "@sage/xtrem-stock/nodes__stock_valuation_input_set__property__company": "Company", "@sage/xtrem-stock/nodes__stock_valuation_input_set__property__date": "Date", "@sage/xtrem-stock/nodes__stock_valuation_input_set__property__displayZeroValues": "Display zero values", "@sage/xtrem-stock/nodes__stock_valuation_input_set__property__fromItem": "From item", "@sage/xtrem-stock/nodes__stock_valuation_input_set__property__itemCategory": "Item category", "@sage/xtrem-stock/nodes__stock_valuation_input_set__property__lines": "Lines", "@sage/xtrem-stock/nodes__stock_valuation_input_set__property__postingClass": "Posting class", "@sage/xtrem-stock/nodes__stock_valuation_input_set__property__sites": "Sites", "@sage/xtrem-stock/nodes__stock_valuation_input_set__property__status": "Status", "@sage/xtrem-stock/nodes__stock_valuation_input_set__property__toItem": "To item", "@sage/xtrem-stock/nodes__stock_valuation_input_set__property__user": "User", "@sage/xtrem-stock/nodes__stock_valuation_input_set__property__valuationMethod": "Valuation method", "@sage/xtrem-stock/nodes__stock_valuation_input_set_to_site__asyncMutation__asyncExport": "Export", "@sage/xtrem-stock/nodes__stock_valuation_input_set_to_site__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-stock/nodes__stock_valuation_input_set_to_site__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-stock/nodes__stock_valuation_input_set_to_site__node_name": "Stock valuation input set to site", "@sage/xtrem-stock/nodes__stock_valuation_input_set_to_site__property__inputSet": "Input set", "@sage/xtrem-stock/nodes__stock_valuation_input_set_to_site__property__site": "Site", "@sage/xtrem-stock/nodes__stock_valuation_result_line__asyncMutation__asyncExport": "Export", "@sage/xtrem-stock/nodes__stock_valuation_result_line__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-stock/nodes__stock_valuation_result_line__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-stock/nodes__stock_valuation_result_line__node_name": "Stock valuation result line", "@sage/xtrem-stock/nodes__stock_valuation_result_line__property__commodityCode": "Commodity code", "@sage/xtrem-stock/nodes__stock_valuation_result_line__property__company": "Company", "@sage/xtrem-stock/nodes__stock_valuation_result_line__property__costType": "Cost type", "@sage/xtrem-stock/nodes__stock_valuation_result_line__property__currency": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-stock/nodes__stock_valuation_result_line__property__inputSet": "Input set", "@sage/xtrem-stock/nodes__stock_valuation_result_line__property__item": "<PERSON><PERSON>", "@sage/xtrem-stock/nodes__stock_valuation_result_line__property__itemCategory": "Item category", "@sage/xtrem-stock/nodes__stock_valuation_result_line__property__postingClass": "Posting class", "@sage/xtrem-stock/nodes__stock_valuation_result_line__property__quantity": "Quantity", "@sage/xtrem-stock/nodes__stock_valuation_result_line__property__site": "Site", "@sage/xtrem-stock/nodes__stock_valuation_result_line__property__stockUnit": "Stock unit", "@sage/xtrem-stock/nodes__stock_valuation_result_line__property__stockValue": "Stock value", "@sage/xtrem-stock/nodes__stock_valuation_result_line__property__unitCost": "Unit cost", "@sage/xtrem-stock/nodes__stock_valuation_result_line__property__valuationDate": "Valuation date", "@sage/xtrem-stock/nodes__stock_value_change__amount_newAmount_average_cost": "Line 1: the current and new amounts cannot be the same. Enter a different value in the new amount.", "@sage/xtrem-stock/nodes__stock_value_change__amount_newAmount_fifo_cost": "Line {{lineNumber}}: the current and new amounts cannot be the same. Enter a different value in the new amount.", "@sage/xtrem-stock/nodes__stock_value_change__asyncMutation__asyncExport": "Export", "@sage/xtrem-stock/nodes__stock_value_change__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-stock/nodes__stock_value_change__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-stock/nodes__stock_value_change__average_cost_quantity_cost_amount_has_changed": "The average cost corresponding to line 1 has changed. Recreate the stock value change line.", "@sage/xtrem-stock/nodes__stock_value_change__cant_repost_stock_value_change_when_status_is_not_failed": "You can only repost a stock value change if the status is 'Failed'.", "@sage/xtrem-stock/nodes__stock_value_change__company_has_no_stock_posting": "No stock has been posted to this company.", "@sage/xtrem-stock/nodes__stock_value_change__document_was_posted": "The stock value change was posted.", "@sage/xtrem-stock/nodes__stock_value_change__fifo_cost_quantity_cost_amount_has_changed": "The FIFO stack record corresponding to line with effective date {{date}} and sequence {{sequence}} has changed. Recreate the stock value change line.", "@sage/xtrem-stock/nodes__stock_value_change__lines_mandatory": "The stock value change must contain at least one line.", "@sage/xtrem-stock/nodes__stock_value_change__mutation__financeIntegrationCheck": "Finance integration check", "@sage/xtrem-stock/nodes__stock_value_change__mutation__financeIntegrationCheck__failed": "Finance integration check failed.", "@sage/xtrem-stock/nodes__stock_value_change__mutation__financeIntegrationCheck__parameter__valueChange": "Value change", "@sage/xtrem-stock/nodes__stock_value_change__mutation__postToStock": "Post to stock", "@sage/xtrem-stock/nodes__stock_value_change__mutation__postToStock__failed": "Post to stock failed.", "@sage/xtrem-stock/nodes__stock_value_change__mutation__postToStock__parameter__documentIds": "Document IDs", "@sage/xtrem-stock/nodes__stock_value_change__mutation__repost": "Repost", "@sage/xtrem-stock/nodes__stock_value_change__mutation__repost__failed": "Repost failed.", "@sage/xtrem-stock/nodes__stock_value_change__mutation__repost__parameter__documentLines": "Document lines", "@sage/xtrem-stock/nodes__stock_value_change__mutation__repost__parameter__stockValueChange": "Stock value change", "@sage/xtrem-stock/nodes__stock_value_change__mutation__resendNotificationForFinance": "Resend notification for finance", "@sage/xtrem-stock/nodes__stock_value_change__mutation__resendNotificationForFinance__failed": "Resend notification for finance failed.", "@sage/xtrem-stock/nodes__stock_value_change__mutation__resendNotificationForFinance__parameter__stockValueChange": "Stock value change", "@sage/xtrem-stock/nodes__stock_value_change__node_name": "Stock value change", "@sage/xtrem-stock/nodes__stock_value_change__property__description": "Description", "@sage/xtrem-stock/nodes__stock_value_change__property__documentDate": "Document date", "@sage/xtrem-stock/nodes__stock_value_change__property__effectiveDate": "Effective date", "@sage/xtrem-stock/nodes__stock_value_change__property__financeIntegrationStatus": "Finance integration status", "@sage/xtrem-stock/nodes__stock_value_change__property__financialSite": "Financial site", "@sage/xtrem-stock/nodes__stock_value_change__property__forceUpdateForStock": "Force update for stock", "@sage/xtrem-stock/nodes__stock_value_change__property__item": "<PERSON><PERSON>", "@sage/xtrem-stock/nodes__stock_value_change__property__itemSite": "Item site", "@sage/xtrem-stock/nodes__stock_value_change__property__lines": "Lines", "@sage/xtrem-stock/nodes__stock_value_change__property__number": "Number", "@sage/xtrem-stock/nodes__stock_value_change__property__postedDate": "Posted date", "@sage/xtrem-stock/nodes__stock_value_change__property__postingDetails": "Posting details", "@sage/xtrem-stock/nodes__stock_value_change__property__site": "Site", "@sage/xtrem-stock/nodes__stock_value_change__property__stockTransactionStatus": "Stock transaction status", "@sage/xtrem-stock/nodes__stock_value_change__property__totalQuantityInStock": "Total quantity in stock", "@sage/xtrem-stock/nodes__stock_value_change__property__totalValueOfStock": "Total value of stock", "@sage/xtrem-stock/nodes__stock_value_change__property__transactionCurrency": "Transaction currency", "@sage/xtrem-stock/nodes__stock_value_change__property__unitCost": "Unit cost", "@sage/xtrem-stock/nodes__stock_value_change__property__valuationMethod": "Valuation method", "@sage/xtrem-stock/nodes__stock_value_change_line__asyncMutation__asyncExport": "Export", "@sage/xtrem-stock/nodes__stock_value_change_line__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-stock/nodes__stock_value_change_line__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-stock/nodes__stock_value_change_line__check_amount_stock_detail": "The amount in stockDetails {{totalStockDetails}} is not equal to the value change variance on the document line {{varianceLine}}", "@sage/xtrem-stock/nodes__stock_value_change_line__node_name": "Stock value change line", "@sage/xtrem-stock/nodes__stock_value_change_line__property__amount": "Amount", "@sage/xtrem-stock/nodes__stock_value_change_line__property__analyticalData": "Analytical data", "@sage/xtrem-stock/nodes__stock_value_change_line__property__computedAttributes": "Computed attributes", "@sage/xtrem-stock/nodes__stock_value_change_line__property__currency": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-stock/nodes__stock_value_change_line__property__document": "Document", "@sage/xtrem-stock/nodes__stock_value_change_line__property__documentId": "Document ID", "@sage/xtrem-stock/nodes__stock_value_change_line__property__documentNumber": "Document number", "@sage/xtrem-stock/nodes__stock_value_change_line__property__effectiveDate": "Effective date", "@sage/xtrem-stock/nodes__stock_value_change_line__property__fifoCost": "FIFO cost", "@sage/xtrem-stock/nodes__stock_value_change_line__property__item": "<PERSON><PERSON>", "@sage/xtrem-stock/nodes__stock_value_change_line__property__jsonStockDetails": "JSON stock details", "@sage/xtrem-stock/nodes__stock_value_change_line__property__newAmount": "New amount", "@sage/xtrem-stock/nodes__stock_value_change_line__property__newUnitCost": "New unit cost", "@sage/xtrem-stock/nodes__stock_value_change_line__property__quantity": "Quantity", "@sage/xtrem-stock/nodes__stock_value_change_line__property__quantityInStockUnit": "Quantity in stock unit", "@sage/xtrem-stock/nodes__stock_value_change_line__property__site": "Site", "@sage/xtrem-stock/nodes__stock_value_change_line__property__stockDetails": "Stock details", "@sage/xtrem-stock/nodes__stock_value_change_line__property__stockMovements": "Stock movements", "@sage/xtrem-stock/nodes__stock_value_change_line__property__stockTransactions": "Stock transactions", "@sage/xtrem-stock/nodes__stock_value_change_line__property__stockTransactionStatus": "Stock transaction status", "@sage/xtrem-stock/nodes__stock_value_change_line__property__stockUnit": "Stock unit", "@sage/xtrem-stock/nodes__stock_value_change_line__property__storedAttributes": "Stored attributes", "@sage/xtrem-stock/nodes__stock_value_change_line__property__storedDimensions": "Stored dimensions", "@sage/xtrem-stock/nodes__stock_value_change_line__property__unitCost": "Unit cost", "@sage/xtrem-stock/nodes__stock_value_correction__asyncMutation__asyncExport": "Export", "@sage/xtrem-stock/nodes__stock_value_correction__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-stock/nodes__stock_value_correction__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-stock/nodes__stock_value_correction__mutation__postToStock": "Post to stock", "@sage/xtrem-stock/nodes__stock_value_correction__mutation__postToStock__failed": "Post to stock failed.", "@sage/xtrem-stock/nodes__stock_value_correction__mutation__postToStock__parameter__documentIds": "Document IDs", "@sage/xtrem-stock/nodes__stock_value_correction__node_name": "Stock value correction", "@sage/xtrem-stock/nodes__stock_value_correction__property__description": "Description", "@sage/xtrem-stock/nodes__stock_value_correction__property__documentDate": "Document date", "@sage/xtrem-stock/nodes__stock_value_correction__property__effectiveDate": "Effective date", "@sage/xtrem-stock/nodes__stock_value_correction__property__financialSite": "Financial site", "@sage/xtrem-stock/nodes__stock_value_correction__property__lines": "Lines", "@sage/xtrem-stock/nodes__stock_value_correction__property__number": "Number", "@sage/xtrem-stock/nodes__stock_value_correction__property__reasonCode": "Reason code", "@sage/xtrem-stock/nodes__stock_value_correction__property__status": "Status", "@sage/xtrem-stock/nodes__stock_value_correction__property__stockSite": "Stock site", "@sage/xtrem-stock/nodes__stock_value_correction__property__stockTransactionStatus": "Stock transaction status", "@sage/xtrem-stock/nodes__stock_value_correction__property__transactionCurrency": "Transaction currency", "@sage/xtrem-stock/nodes__stock_value_correction_line__asyncMutation__asyncExport": "Export", "@sage/xtrem-stock/nodes__stock_value_correction_line__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-stock/nodes__stock_value_correction_line__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-stock/nodes__stock_value_correction_line__invalid_document_line": "The corrected document line stock transaction status must be completed.", "@sage/xtrem-stock/nodes__stock_value_correction_line__no_corrected_line": "You must provide the document line that is being corrected.", "@sage/xtrem-stock/nodes__stock_value_correction_line__node_name": "Stock value correction line", "@sage/xtrem-stock/nodes__stock_value_correction_line__property__analyticalData": "Analytical data", "@sage/xtrem-stock/nodes__stock_value_correction_line__property__computedAttributes": "Computed attributes", "@sage/xtrem-stock/nodes__stock_value_correction_line__property__correctedDocumentLine": "Corrected document line", "@sage/xtrem-stock/nodes__stock_value_correction_line__property__correctedDocumentType": "Corrected document type", "@sage/xtrem-stock/nodes__stock_value_correction_line__property__document": "Document", "@sage/xtrem-stock/nodes__stock_value_correction_line__property__documentId": "Document ID", "@sage/xtrem-stock/nodes__stock_value_correction_line__property__documentNumber": "Document number", "@sage/xtrem-stock/nodes__stock_value_correction_line__property__item": "<PERSON><PERSON>", "@sage/xtrem-stock/nodes__stock_value_correction_line__property__itemSite": "Item-site", "@sage/xtrem-stock/nodes__stock_value_correction_line__property__newUnitCost": "New unit cost", "@sage/xtrem-stock/nodes__stock_value_correction_line__property__orderCost": "Order cost", "@sage/xtrem-stock/nodes__stock_value_correction_line__property__site": "Site", "@sage/xtrem-stock/nodes__stock_value_correction_line__property__stockDetails": "Stock details", "@sage/xtrem-stock/nodes__stock_value_correction_line__property__stockMovements": "Stock movements", "@sage/xtrem-stock/nodes__stock_value_correction_line__property__stockTransactions": "Stock transaction", "@sage/xtrem-stock/nodes__stock_value_correction_line__property__stockTransactionStatus": "Stock transaction status", "@sage/xtrem-stock/nodes__stock_value_correction_line__property__storedAttributes": "Stored attributes", "@sage/xtrem-stock/nodes__stock_value_correction_line__property__storedDimensions": "Stored dimensions", "@sage/xtrem-stock/nodes__stock_value_correction_line__property__unitCost": "Unit cost", "@sage/xtrem-stock/nodes__stock_value_correction_line__property__valuedCost": "Valued cost", "@sage/xtrem-stock/nodes__stock-adjustment__cant_post_stock_adjustment_when_stock_detail_status_is_required": "You need to enter stock details for all lines before you can post.", "@sage/xtrem-stock/nodes__stock-adjustment__cant_repost_stock_adjustment_when_status_is_not_failed": "You can only repost a stock adjustment if the status is 'Failed' or 'Not recorded'.", "@sage/xtrem-stock/nodes__stock-adjustment__company_has_no_stock_posting": "No stock has been posted to this company.", "@sage/xtrem-stock/nodes__stock-adjustment__document_was_posted": "The stock adjustment was posted.", "@sage/xtrem-stock/nodes__stock-issue__cant_repost_stock_issue_when_status_is_not_failed": "You can only repost a stock issue if the status is 'Failed' or 'Not recorded'.", "@sage/xtrem-stock/nodes__stock-issue__company_has_no_stock_posting": "The current company has no stock posting.", "@sage/xtrem-stock/nodes__stock-issue__document_was_posted": "The stock receipt was posted.", "@sage/xtrem-stock/nodes__stock-receipt__cant_repost_stock_receipt_when_status_is_not_failed": "You can only repost a stock receipt if the status is 'Failed' or 'Not recorded.'", "@sage/xtrem-stock/nodes__stock-receipt__company_has_no_stock_posting": "The current company has no stock posting.", "@sage/xtrem-stock/nodes__stock-receipt__document_was_posted": "The stock receipt was posted.", "@sage/xtrem-stock/package__name": "Stock", "@sage/xtrem-stock/page__stock_count__add_new_line": "Add new line", "@sage/xtrem-stock/page__stock_count__forbid_addition_serialized_managed_item": "A stock line for a serialized item cannot be added to an existing stock count.", "@sage/xtrem-stock/page-extensions__bill_of_material_component_panel_extension__availableQuantityInStockUnit____title": "Available stock quantity", "@sage/xtrem-stock/page-extensions__bill_of_material_component_panel_extension__stockOnHand____title": "Stock on hand", "@sage/xtrem-stock/page-extensions__bill_of_material_component_panel_extension__stockShortageInStockUnit____title": "Stock shortage", "@sage/xtrem-stock/page-extensions__bill_of_material_component_panel_extension__stockShortageStatus____title": "Stock shortage status", "@sage/xtrem-stock/page-extensions__bill_of_material_extension__components____columns__title__availableQuantityInStockUnit": "Available stock quantity", "@sage/xtrem-stock/page-extensions__bill_of_material_extension__components____columns__title__stockShortageInStockUnit": "Stock shortage", "@sage/xtrem-stock/page-extensions__create_test_data_extension__isStockQuantityFixed____title": "Stock quantity fixed for all items", "@sage/xtrem-stock/page-extensions__create_test_data_extension__item____columns__title__description": "Description", "@sage/xtrem-stock/page-extensions__create_test_data_extension__item____columns__title__id": "Item ID", "@sage/xtrem-stock/page-extensions__create_test_data_extension__item____columns__title__name": "Name", "@sage/xtrem-stock/page-extensions__create_test_data_extension__item____lookupDialogTitle": "Select item", "@sage/xtrem-stock/page-extensions__create_test_data_extension__item____title": "Base item", "@sage/xtrem-stock/page-extensions__create_test_data_extension__itemBlock____title": "<PERSON><PERSON>", "@sage/xtrem-stock/page-extensions__create_test_data_extension__itemQuantity____title": "Quantity of items", "@sage/xtrem-stock/page-extensions__create_test_data_extension__maxStockQuantity____title": "Maximum stock quantity", "@sage/xtrem-stock/page-extensions__item_site_extension__countingInProgress____title": "Counting in progress", "@sage/xtrem-stock/page-extensions__item_site_extension__countingInProgressMention____title": "Counting in progress", "@sage/xtrem-stock/page-extensions__item_site_extension__lastCountDate____title": "Last count date", "@sage/xtrem-stock/pages__bill_of_material_component_panel_extension__display_shortage_status_false": "Available stock", "@sage/xtrem-stock/pages__bill_of_material_component_panel_extension__display_shortage_status_true": "Stock shortage", "@sage/xtrem-stock/pages__cost_roll_up_sub_assembly_dialog____title": "Subassemblies", "@sage/xtrem-stock/pages__cost_roll_up_sub_assembly_dialog____update_title": "Item: {{itemName}}", "@sage/xtrem-stock/pages__cost_roll_up_sub_assembly_dialog__mainSection____title": "Subassemblies", "@sage/xtrem-stock/pages__cost_roll_up_sub_assembly_dialog__ok____title": "OK", "@sage/xtrem-stock/pages__cost_roll_up_sub_assembly_dialog__subAssemblies____columns__title__calculationQuantity": "Calculation quantity", "@sage/xtrem-stock/pages__cost_roll_up_sub_assembly_dialog__subAssemblies____columns__title__componentNumber": "Component number", "@sage/xtrem-stock/pages__cost_roll_up_sub_assembly_dialog__subAssemblies____columns__title__currentLaborCost": "Current labor cost", "@sage/xtrem-stock/pages__cost_roll_up_sub_assembly_dialog__subAssemblies____columns__title__currentMachineCost": "Current machine cost", "@sage/xtrem-stock/pages__cost_roll_up_sub_assembly_dialog__subAssemblies____columns__title__currentMaterialCost": "Current material cost", "@sage/xtrem-stock/pages__cost_roll_up_sub_assembly_dialog__subAssemblies____columns__title__currentToolCost": "Current tool cost", "@sage/xtrem-stock/pages__cost_roll_up_sub_assembly_dialog__subAssemblies____columns__title__currentTotalCost": "Current total cost", "@sage/xtrem-stock/pages__cost_roll_up_sub_assembly_dialog__subAssemblies____columns__title__item__id": "Item ID", "@sage/xtrem-stock/pages__cost_roll_up_sub_assembly_dialog__subAssemblies____columns__title__item__name": "Item name", "@sage/xtrem-stock/pages__cost_roll_up_sub_assembly_dialog__subAssemblies____columns__title__laborCost": "Labor cost", "@sage/xtrem-stock/pages__cost_roll_up_sub_assembly_dialog__subAssemblies____columns__title__machineCost": "Machine cost", "@sage/xtrem-stock/pages__cost_roll_up_sub_assembly_dialog__subAssemblies____columns__title__materialCost": "Material cost", "@sage/xtrem-stock/pages__cost_roll_up_sub_assembly_dialog__subAssemblies____columns__title__toolCost": "Tool cost", "@sage/xtrem-stock/pages__cost_roll_up_sub_assembly_dialog__subAssemblies____columns__title__totalCost": "Total cost", "@sage/xtrem-stock/pages__cost_roll_up_sub_assembly_dialog__subAssemblies____title": "Subassemblies", "@sage/xtrem-stock/pages__fifo_valuation_tier_table_panel____title": "Add from FIFO stack", "@sage/xtrem-stock/pages__fifo_valuation_tier_table_panel__cancel____title": "Cancel", "@sage/xtrem-stock/pages__fifo_valuation_tier_table_panel__confirm____title": "Add", "@sage/xtrem-stock/pages__fifo_valuation_tier_table_panel__fifoValuationTierLines____columns__title__amount": "Amount", "@sage/xtrem-stock/pages__fifo_valuation_tier_table_panel__fifoValuationTierLines____columns__title__effectiveDate": "Effective date", "@sage/xtrem-stock/pages__fifo_valuation_tier_table_panel__fifoValuationTierLines____columns__title__nonAbsorbedAmount": "Non-absorbed amount", "@sage/xtrem-stock/pages__fifo_valuation_tier_table_panel__fifoValuationTierLines____columns__title__receiptDocumentLine__documentNumber": "Document number", "@sage/xtrem-stock/pages__fifo_valuation_tier_table_panel__fifoValuationTierLines____columns__title__receiptQuantity": "Receipt quantity", "@sage/xtrem-stock/pages__fifo_valuation_tier_table_panel__fifoValuationTierLines____columns__title__remainingQuantity": "Remaining quantity", "@sage/xtrem-stock/pages__fifo_valuation_tier_table_panel__fifoValuationTierLines____columns__title__sequence": "Sequence", "@sage/xtrem-stock/pages__fifo_valuation_tier_table_panel__fifoValuationTierLines____columns__title__unitCost": "Unit cost", "@sage/xtrem-stock/pages__item_site_cost__dimensions_button_text": "Dimensions", "@sage/xtrem-stock/pages__mrp_calculation____navigationPanel__bulkActions__title": "Delete", "@sage/xtrem-stock/pages__mrp_calculation____navigationPanel__listItem__line2Right__title": "Status", "@sage/xtrem-stock/pages__mrp_calculation____navigationPanel__listItem__numberOfWeeks__title": "Period in weeks", "@sage/xtrem-stock/pages__mrp_calculation____navigationPanel__listItem__titleRight__title": "Calculation date", "@sage/xtrem-stock/pages__mrp_calculation____navigationPanel__listItem__user__title": "Calculation user", "@sage/xtrem-stock/pages__mrp_calculation____objectTypePlural": "MRP calculation results", "@sage/xtrem-stock/pages__mrp_calculation____objectTypeSingular": "MRP calculation result", "@sage/xtrem-stock/pages__mrp_calculation____title": "MRP calculation", "@sage/xtrem-stock/pages__mrp_calculation__calculationDate____title": "Calculation date", "@sage/xtrem-stock/pages__mrp_calculation__calculationStatus____title": "Status", "@sage/xtrem-stock/pages__mrp_calculation__createMrpCalculationRequest____title": "Create", "@sage/xtrem-stock/pages__mrp_calculation__criteriaBlock____title": "Criteria", "@sage/xtrem-stock/pages__mrp_calculation__description____title": "Description", "@sage/xtrem-stock/pages__mrp_calculation__errorMessage____title": "Error message", "@sage/xtrem-stock/pages__mrp_calculation__explodeBillOfMaterial____title": "Explode bill of material", "@sage/xtrem-stock/pages__mrp_calculation__fromItem____title": "From item", "@sage/xtrem-stock/pages__mrp_calculation__fromItemText____title": "From item", "@sage/xtrem-stock/pages__mrp_calculation__isSalesQuoteIncluded____title": "Include sales quotes", "@sage/xtrem-stock/pages__mrp_calculation__linesSection____title": "Results", "@sage/xtrem-stock/pages__mrp_calculation__lowLevelCode____title": "Lowest BOM level", "@sage/xtrem-stock/pages__mrp_calculation__mainSection____title": "General", "@sage/xtrem-stock/pages__mrp_calculation__multiRefCompanies____columns__title__id": "ID", "@sage/xtrem-stock/pages__mrp_calculation__multiRefCompanies____columns__title__name": "Name", "@sage/xtrem-stock/pages__mrp_calculation__multiRefCompanies____lookupDialogTitle": "Select company", "@sage/xtrem-stock/pages__mrp_calculation__multiRefCompanies____title": "Companies", "@sage/xtrem-stock/pages__mrp_calculation__multiRefSites____columns__title__id": "ID", "@sage/xtrem-stock/pages__mrp_calculation__multiRefSites____columns__title__name": "Name", "@sage/xtrem-stock/pages__mrp_calculation__multiRefSites____lookupDialogTitle": "Select site", "@sage/xtrem-stock/pages__mrp_calculation__multiRefSites____title": "Sites", "@sage/xtrem-stock/pages__mrp_calculation__numberOfWeeks____title": "Period in weeks", "@sage/xtrem-stock/pages__mrp_calculation__purchase_order_creation__success": "Purchase order {{num}} created.", "@sage/xtrem-stock/pages__mrp_calculation__resultLines____columns__title___sortValue": "Sort value", "@sage/xtrem-stock/pages__mrp_calculation__resultLines____columns__title__company": "Company", "@sage/xtrem-stock/pages__mrp_calculation__resultLines____columns__title__company__id": "Company ID", "@sage/xtrem-stock/pages__mrp_calculation__resultLines____columns__title__company__name": "Company name", "@sage/xtrem-stock/pages__mrp_calculation__resultLines____columns__title__endDate": "End date", "@sage/xtrem-stock/pages__mrp_calculation__resultLines____columns__title__item": "<PERSON><PERSON>", "@sage/xtrem-stock/pages__mrp_calculation__resultLines____columns__title__item__id": "Item ID", "@sage/xtrem-stock/pages__mrp_calculation__resultLines____columns__title__item__name": "<PERSON><PERSON>", "@sage/xtrem-stock/pages__mrp_calculation__resultLines____columns__title__mrpCalculation__startDate": "First suggestion date", "@sage/xtrem-stock/pages__mrp_calculation__resultLines____columns__title__preferredProcess": "Replenishment method", "@sage/xtrem-stock/pages__mrp_calculation__resultLines____columns__title__quantity": "Quantity", "@sage/xtrem-stock/pages__mrp_calculation__resultLines____columns__title__referenceItem__description": "Item description", "@sage/xtrem-stock/pages__mrp_calculation__resultLines____columns__title__referenceItem__id": "Item ID", "@sage/xtrem-stock/pages__mrp_calculation__resultLines____columns__title__site": "Site", "@sage/xtrem-stock/pages__mrp_calculation__resultLines____columns__title__site__id": "Site ID", "@sage/xtrem-stock/pages__mrp_calculation__resultLines____columns__title__site__name": "Site name", "@sage/xtrem-stock/pages__mrp_calculation__resultLines____columns__title__startDate": "Start date", "@sage/xtrem-stock/pages__mrp_calculation__resultLines____columns__title__suggestionStatus": "Suggestion status", "@sage/xtrem-stock/pages__mrp_calculation__resultLines____dropdownActions__title": "Create purchase order", "@sage/xtrem-stock/pages__mrp_calculation__resultLines____dropdownActions__title__2": "Create work order", "@sage/xtrem-stock/pages__mrp_calculation__resultLines____title": "Details", "@sage/xtrem-stock/pages__mrp_calculation__startDate____title": "Start date", "@sage/xtrem-stock/pages__mrp_calculation__toItem____title": "To item", "@sage/xtrem-stock/pages__mrp_calculation__toItemText____title": "To item", "@sage/xtrem-stock/pages__mrp_calculation__user____columns__title__email": "Email", "@sage/xtrem-stock/pages__mrp_calculation__user____columns__title__firstName": "First name", "@sage/xtrem-stock/pages__mrp_calculation__user____columns__title__lastName": "Last name", "@sage/xtrem-stock/pages__mrp_calculation__user____lookupDialogTitle": "Select user", "@sage/xtrem-stock/pages__mrp_calculation__user____title": "Calculation user", "@sage/xtrem-stock/pages__mrp_calculation__work_order_creation__success": "Work order {{num}} created.", "@sage/xtrem-stock/pages__mrp_calculation_justification__results____columns__columns__company__name__title": "Name", "@sage/xtrem-stock/pages__mrp_calculation_justification__results____columns__columns__company__name__title__2": "ID", "@sage/xtrem-stock/pages__mrp_calculation_justification__results____columns__columns__item__id__title": "Name", "@sage/xtrem-stock/pages__mrp_calculation_justification__results____columns__columns__item__id__title__2": "ID", "@sage/xtrem-stock/pages__mrp_calculation_justification__results____columns__columns__site__name__title": "Name", "@sage/xtrem-stock/pages__mrp_calculation_justification__results____columns__columns__site__name__title__2": "ID", "@sage/xtrem-stock/pages__mrp_calculation_request____title": "MRP calculation request", "@sage/xtrem-stock/pages__mrp_calculation_request__cancelRequest____title": "Cancel", "@sage/xtrem-stock/pages__mrp_calculation_request__companies____columns__title__id": "ID", "@sage/xtrem-stock/pages__mrp_calculation_request__companies____columns__title__name": "Name", "@sage/xtrem-stock/pages__mrp_calculation_request__companies____lookupDialogTitle": "Select company", "@sage/xtrem-stock/pages__mrp_calculation_request__companies____placeholder": "Select ...", "@sage/xtrem-stock/pages__mrp_calculation_request__companies____title": "Company", "@sage/xtrem-stock/pages__mrp_calculation_request__criteriaBlock____title": "Criteria", "@sage/xtrem-stock/pages__mrp_calculation_request__description____title": "Description", "@sage/xtrem-stock/pages__mrp_calculation_request__explodeBillOfMaterial____title": "Explode bill of material", "@sage/xtrem-stock/pages__mrp_calculation_request__invalid_criteria": "No item-site records replenished by MRP for the entered criteria.", "@sage/xtrem-stock/pages__mrp_calculation_request__invalid_item_from_range": "The 'From item' cannot be after the 'To item.'", "@sage/xtrem-stock/pages__mrp_calculation_request__invalid_item_to_range": "The 'To item' cannot be before the 'From item'.", "@sage/xtrem-stock/pages__mrp_calculation_request__isSalesQuoteIncluded____title": "Include sales quotes", "@sage/xtrem-stock/pages__mrp_calculation_request__itemFrom____columns__title__description": "Description", "@sage/xtrem-stock/pages__mrp_calculation_request__itemFrom____columns__title__id": "ID", "@sage/xtrem-stock/pages__mrp_calculation_request__itemFrom____columns__title__name": "Name", "@sage/xtrem-stock/pages__mrp_calculation_request__itemFrom____lookupDialogTitle": "Select item", "@sage/xtrem-stock/pages__mrp_calculation_request__itemFrom____placeholder": "Select ...", "@sage/xtrem-stock/pages__mrp_calculation_request__itemFrom____title": "From item", "@sage/xtrem-stock/pages__mrp_calculation_request__itemTo____columns__title__category__name": "Category", "@sage/xtrem-stock/pages__mrp_calculation_request__itemTo____lookupDialogTitle": "Select item", "@sage/xtrem-stock/pages__mrp_calculation_request__itemTo____placeholder": "Select ...", "@sage/xtrem-stock/pages__mrp_calculation_request__itemTo____title": "To item", "@sage/xtrem-stock/pages__mrp_calculation_request__mainSection____title": "General", "@sage/xtrem-stock/pages__mrp_calculation_request__notification_success": "MRP calculation request sent", "@sage/xtrem-stock/pages__mrp_calculation_request__numberOfWeeks____title": "Period in weeks", "@sage/xtrem-stock/pages__mrp_calculation_request__scheduleMrpCalculationRequest____title": "Schedule", "@sage/xtrem-stock/pages__mrp_calculation_request__sites____columns__title__id": "ID", "@sage/xtrem-stock/pages__mrp_calculation_request__sites____columns__title__name": "Name", "@sage/xtrem-stock/pages__mrp_calculation_request__sites____lookupDialogTitle": "Select site", "@sage/xtrem-stock/pages__mrp_calculation_request__sites____placeholder": "Select ...", "@sage/xtrem-stock/pages__mrp_calculation_request__sites____title": "Site", "@sage/xtrem-stock/pages__mrp_calculation_request__startDate____title": "Start date", "@sage/xtrem-stock/pages__mrp_calculation_request__supply_chain_error": "Purchase orders are currently being created from previous MRP calculations. Try again later.", "@sage/xtrem-stock/pages__mrp_projected_stock_dialog____title": "Projected stock", "@sage/xtrem-stock/pages__mrp_projected_stock_dialog__batchQuantity____title": "Batch quantity", "@sage/xtrem-stock/pages__mrp_projected_stock_dialog__chartBlock____title": "Projected stock", "@sage/xtrem-stock/pages__mrp_projected_stock_dialog__criteriaBlock____title": "Projected stock", "@sage/xtrem-stock/pages__mrp_projected_stock_dialog__day": "Day", "@sage/xtrem-stock/pages__mrp_projected_stock_dialog__economicOrderQuantity____title": "Economic order quantity", "@sage/xtrem-stock/pages__mrp_projected_stock_dialog__frequency____title": "Frequency", "@sage/xtrem-stock/pages__mrp_projected_stock_dialog__item____title": "<PERSON><PERSON>", "@sage/xtrem-stock/pages__mrp_projected_stock_dialog__lines____columns__title__bucket": "Bucket", "@sage/xtrem-stock/pages__mrp_projected_stock_dialog__lines____columns__title__documentNumber": "Document number", "@sage/xtrem-stock/pages__mrp_projected_stock_dialog__lines____columns__title__documentType": "Document type", "@sage/xtrem-stock/pages__mrp_projected_stock_dialog__lines____columns__title__dueDate": "Due date", "@sage/xtrem-stock/pages__mrp_projected_stock_dialog__lines____columns__title__quantity": "Quantity", "@sage/xtrem-stock/pages__mrp_projected_stock_dialog__lines____columns__title__suggestion": "Suggestion", "@sage/xtrem-stock/pages__mrp_projected_stock_dialog__lines____title": "Details", "@sage/xtrem-stock/pages__mrp_projected_stock_dialog__mainSection____title": "General information", "@sage/xtrem-stock/pages__mrp_projected_stock_dialog__numberOfWeeks____title": "Period in weeks", "@sage/xtrem-stock/pages__mrp_projected_stock_dialog__ok____title": "OK", "@sage/xtrem-stock/pages__mrp_projected_stock_dialog__preferredProcess____title": "Preferred process", "@sage/xtrem-stock/pages__mrp_projected_stock_dialog__productionLeadTime____title": "Production lead time", "@sage/xtrem-stock/pages__mrp_projected_stock_dialog__projectedStockChart____chart__series__title": "Projected stock", "@sage/xtrem-stock/pages__mrp_projected_stock_dialog__projectedStockChart____chart__series__title__2": "Safety level", "@sage/xtrem-stock/pages__mrp_projected_stock_dialog__projectedStockChart____chart__xAxis__title": "Date", "@sage/xtrem-stock/pages__mrp_projected_stock_dialog__projectedStockChart____title": "Projected stock", "@sage/xtrem-stock/pages__mrp_projected_stock_dialog__purchaseLeadTime____title": "Purchase lead time", "@sage/xtrem-stock/pages__mrp_projected_stock_dialog__safetyStock____title": "Safety stock", "@sage/xtrem-stock/pages__mrp_projected_stock_dialog__site____title": "Site", "@sage/xtrem-stock/pages__mrp_projected_stock_dialog__startDate____title": "Start date", "@sage/xtrem-stock/pages__mrp_projected_stock_dialog__starting_stock": "Starting stock", "@sage/xtrem-stock/pages__mrp_projected_stock_dialog__stockUnit____columns__title__decimalDigits": "Decimal places", "@sage/xtrem-stock/pages__mrp_projected_stock_dialog__stockUnit____columns__title__symbol": "Symbol", "@sage/xtrem-stock/pages__mrp_projected_stock_dialog__week": "Week", "@sage/xtrem-stock/pages__mrp_synchronization____navigationPanel__listItem__lastSyncDateTime__title": "Last synchronization", "@sage/xtrem-stock/pages__mrp_synchronization____navigationPanel__listItem__titleRight__title": "Last synchronization status", "@sage/xtrem-stock/pages__mrp_synchronization____title": "MRP synchronization", "@sage/xtrem-stock/pages__mrp_synchronization___id____title": "ID", "@sage/xtrem-stock/pages__mrp_synchronization__criteriaBlock____title": "Criteria", "@sage/xtrem-stock/pages__mrp_synchronization__lastSyncDateTime____title": "Last synchronization", "@sage/xtrem-stock/pages__mrp_synchronization__mainSection____title": "General", "@sage/xtrem-stock/pages__mrp_synchronization__resetSynchronization____title": "Synchronize all records", "@sage/xtrem-stock/pages__mrp_synchronization__synchronizeDeletions____title": "Synchronize deletions", "@sage/xtrem-stock/pages__mrp_synchronization__synchronizeMrp____title": "Synchronize", "@sage/xtrem-stock/pages__mrp_synchronization__syncStatus____title": "Last synchronization status", "@sage/xtrem-stock/pages__mrp_synchronization__type____title": "Type", "@sage/xtrem-stock/pages__mrp_synchronization_request__notification_success": "MRP synchronization request sent.", "@sage/xtrem-stock/pages__serial_number_inquiry____navigationPanel__listItem__allocatedQuantity__title": "Allocated quantity", "@sage/xtrem-stock/pages__serial_number_inquiry____navigationPanel__listItem__availableQuantity__title": "Available quantity", "@sage/xtrem-stock/pages__serial_number_inquiry____navigationPanel__listItem__creationDate__title": "Serial number creation date", "@sage/xtrem-stock/pages__serial_number_inquiry____navigationPanel__listItem__documentLink__title": "Allocated on", "@sage/xtrem-stock/pages__serial_number_inquiry____navigationPanel__listItem__id__title": "Serial number", "@sage/xtrem-stock/pages__serial_number_inquiry____navigationPanel__listItem__isAllocated__title": "Is allocated", "@sage/xtrem-stock/pages__serial_number_inquiry____navigationPanel__listItem__itemId__title": "Item ID", "@sage/xtrem-stock/pages__serial_number_inquiry____navigationPanel__listItem__itemName__title": "<PERSON><PERSON>", "@sage/xtrem-stock/pages__serial_number_inquiry____navigationPanel__listItem__locationId__title": "Location ID", "@sage/xtrem-stock/pages__serial_number_inquiry____navigationPanel__listItem__locationZone__title": "Location zone", "@sage/xtrem-stock/pages__serial_number_inquiry____navigationPanel__listItem__lot__title": "Lot", "@sage/xtrem-stock/pages__serial_number_inquiry____navigationPanel__listItem__lotCreationDate__title": "Lot creation date", "@sage/xtrem-stock/pages__serial_number_inquiry____navigationPanel__listItem__lotExpirationDate__title": "Lot expiration date", "@sage/xtrem-stock/pages__serial_number_inquiry____navigationPanel__listItem__onHandQuantity__title": "On-hand quantity", "@sage/xtrem-stock/pages__serial_number_inquiry____navigationPanel__listItem__owner__title": "Owner", "@sage/xtrem-stock/pages__serial_number_inquiry____navigationPanel__listItem__siteId__title": "Site ID", "@sage/xtrem-stock/pages__serial_number_inquiry____navigationPanel__listItem__siteName__title": "Site", "@sage/xtrem-stock/pages__serial_number_inquiry____navigationPanel__listItem__stockLocation__title": "Location", "@sage/xtrem-stock/pages__serial_number_inquiry____navigationPanel__listItem__stockStatus__title": "Quality control", "@sage/xtrem-stock/pages__serial_number_inquiry____navigationPanel__listItem__stockUnit__title": "Unit", "@sage/xtrem-stock/pages__serial_number_inquiry____navigationPanel__listItem__subLot__title": "Sublot", "@sage/xtrem-stock/pages__serial_number_inquiry____navigationPanel__listItem__supplierId__title": "Supplier ID", "@sage/xtrem-stock/pages__serial_number_inquiry____navigationPanel__listItem__supplierLot__title": "Supplier lot number", "@sage/xtrem-stock/pages__serial_number_inquiry____navigationPanel__listItem__supplierName__columns__title__businessEntity__id": "ID", "@sage/xtrem-stock/pages__serial_number_inquiry____navigationPanel__listItem__supplierName__columns__title__businessEntity__name": "Name", "@sage/xtrem-stock/pages__serial_number_inquiry____navigationPanel__listItem__supplierName__title": "Supplier", "@sage/xtrem-stock/pages__serial_number_inquiry____navigationPanel__listItem__title__title": "Serial number", "@sage/xtrem-stock/pages__serial_number_inquiry____navigationPanel__optionsMenu__title": "All", "@sage/xtrem-stock/pages__serial_number_inquiry____title": "Serial number stock inquiry", "@sage/xtrem-stock/pages__standard_cost_calculation____objectTypePlural": "Standard cost calculations", "@sage/xtrem-stock/pages__standard_cost_calculation____objectTypeSingular": "Standard cost calculation", "@sage/xtrem-stock/pages__standard_cost_calculation____title": "Standard cost calculation", "@sage/xtrem-stock/pages__standard_cost_calculation__button_be_notified": "Notify", "@sage/xtrem-stock/pages__standard_cost_calculation__button_wait": "Wait", "@sage/xtrem-stock/pages__standard_cost_calculation__calculation_dialog_content": "This calculation could take a long time to complete. Do you want to wait for the calculation to finish or navigate away from this page and be notified when it's complete?", "@sage/xtrem-stock/pages__standard_cost_calculation__commodityCode____title": "Commodity code", "@sage/xtrem-stock/pages__standard_cost_calculation__cost_creation_content": "Creating the item-site cost records could take a long time to complete. Do you want to wait while the records are created or navigate away from this page and be notified when they're done?", "@sage/xtrem-stock/pages__standard_cost_calculation__costCategory____columns__title__costCategoryType": "Type", "@sage/xtrem-stock/pages__standard_cost_calculation__costCategory____columns__title__isMandatory": "Mandatory", "@sage/xtrem-stock/pages__standard_cost_calculation__costCategory____lookupDialogTitle": "Select cost category", "@sage/xtrem-stock/pages__standard_cost_calculation__costCategory____title": "Cost category", "@sage/xtrem-stock/pages__standard_cost_calculation__criteriaBlock____title": "Criteria", "@sage/xtrem-stock/pages__standard_cost_calculation__defaultDimension____title": "Set default dimensions", "@sage/xtrem-stock/pages__standard_cost_calculation__dimensions_updated": "Dimensions updated", "@sage/xtrem-stock/pages__standard_cost_calculation__fromDate____title": "Start date", "@sage/xtrem-stock/pages__standard_cost_calculation__includesRouting____title": "Include routing", "@sage/xtrem-stock/pages__standard_cost_calculation__isAllSelected____title": "Select all result lines", "@sage/xtrem-stock/pages__standard_cost_calculation__itemCategory____columns__title__type": "Type", "@sage/xtrem-stock/pages__standard_cost_calculation__itemCategory____lookupDialogTitle": "Select item category", "@sage/xtrem-stock/pages__standard_cost_calculation__itemCategory____title": "Item category", "@sage/xtrem-stock/pages__standard_cost_calculation__items____columns__title__id": "ID", "@sage/xtrem-stock/pages__standard_cost_calculation__items____columns__title__name": "Name", "@sage/xtrem-stock/pages__standard_cost_calculation__items____lookupDialogTitle": "Select item", "@sage/xtrem-stock/pages__standard_cost_calculation__items____title": "Items", "@sage/xtrem-stock/pages__standard_cost_calculation__linesSection____title": "Result lines", "@sage/xtrem-stock/pages__standard_cost_calculation__mainSection____title": "General", "@sage/xtrem-stock/pages__standard_cost_calculation__quantity____title": "Quantity", "@sage/xtrem-stock/pages__standard_cost_calculation__request_notification_item_site_cost_creation_finished": "Item-site cost creation finished.", "@sage/xtrem-stock/pages__standard_cost_calculation__request_notification_item_site_cost_creation_impossible": "You need to run the calculation first.", "@sage/xtrem-stock/pages__standard_cost_calculation__request_notification_item_site_cost_creation_request_sent": "Item-site cost creation request sent.", "@sage/xtrem-stock/pages__standard_cost_calculation__resultLines____columns__title__creationErrorMessage": "Error message", "@sage/xtrem-stock/pages__standard_cost_calculation__resultLines____columns__title__creationStatus": "Cost creation status", "@sage/xtrem-stock/pages__standard_cost_calculation__resultLines____columns__title__currentLaborCost": "Current labor cost", "@sage/xtrem-stock/pages__standard_cost_calculation__resultLines____columns__title__currentMachineCost": "Current machine cost", "@sage/xtrem-stock/pages__standard_cost_calculation__resultLines____columns__title__currentMaterialCost": "Current material cost", "@sage/xtrem-stock/pages__standard_cost_calculation__resultLines____columns__title__currentToolCost": "Current tool cost", "@sage/xtrem-stock/pages__standard_cost_calculation__resultLines____columns__title__currentTotalCost": "Current total cost", "@sage/xtrem-stock/pages__standard_cost_calculation__resultLines____columns__title__item__description": "Item description", "@sage/xtrem-stock/pages__standard_cost_calculation__resultLines____columns__title__item__id": "Item ID", "@sage/xtrem-stock/pages__standard_cost_calculation__resultLines____columns__title__item__name": "<PERSON><PERSON>", "@sage/xtrem-stock/pages__standard_cost_calculation__resultLines____columns__title__laborCost": "Labor cost", "@sage/xtrem-stock/pages__standard_cost_calculation__resultLines____columns__title__machineCost": "Machine cost", "@sage/xtrem-stock/pages__standard_cost_calculation__resultLines____columns__title__materialCost": "Material cost", "@sage/xtrem-stock/pages__standard_cost_calculation__resultLines____columns__title__toolCost": "Tool cost", "@sage/xtrem-stock/pages__standard_cost_calculation__resultLines____columns__title__totalCost": "Total cost", "@sage/xtrem-stock/pages__standard_cost_calculation__resultLines____dropdownActions__title": "Subassemblies", "@sage/xtrem-stock/pages__standard_cost_calculation__resultLines____dropdownActions__title__2": "Dimensions", "@sage/xtrem-stock/pages__standard_cost_calculation__runCreateItemSiteCostsFromRollUpResults____title": "Create item-site cost", "@sage/xtrem-stock/pages__standard_cost_calculation__runStandardCostCalculation____title": "Calculate", "@sage/xtrem-stock/pages__standard_cost_calculation__site____columns__columns__legalCompany__name__title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__standard_cost_calculation__site____columns__title__id": "ID ", "@sage/xtrem-stock/pages__standard_cost_calculation__site____columns__title__legalCompany__name": "Company", "@sage/xtrem-stock/pages__standard_cost_calculation__site____columns__title__name": "Name", "@sage/xtrem-stock/pages__standard_cost_calculation__site____lookupDialogTitle": "Select site", "@sage/xtrem-stock/pages__standard_cost_calculation__site____title": "Site", "@sage/xtrem-stock/pages__standard_cost_calculation__status____title": "Status", "@sage/xtrem-stock/pages__standard_cost_calculation__title_wait_for_finish": "Wait for the calculation to complete", "@sage/xtrem-stock/pages__standard_cost_calculation__user____columns__title__email": "Email", "@sage/xtrem-stock/pages__standard_cost_calculation__user____columns__title__firstName": "First name", "@sage/xtrem-stock/pages__standard_cost_calculation__user____columns__title__lastName": "Last name", "@sage/xtrem-stock/pages__standard_cost_calculation__user____lookupDialogTitle": "Select user", "@sage/xtrem-stock/pages__standard_cost_calculation__user____title": "Calculation user", "@sage/xtrem-stock/pages__standard_cost_calculation__usesComponentStandardCost____title": "Component standard cost", "@sage/xtrem-stock/pages__standard_cost_calculation__wait_for_creation": "Wait for the item-site cost records to be created", "@sage/xtrem-stock/pages__standard_cost_calculation_request__notification_request_sent": "Standard cost calculation request sent.", "@sage/xtrem-stock/pages__standard_cost_calculation_request__notification_valuation_failed": "Standard cost calculation failed.", "@sage/xtrem-stock/pages__standard_cost_calculation_request__notification_valuation_finished": "Standard cost calculation finished.", "@sage/xtrem-stock/pages__stock__adjustment_step_sequence_detail_stock": "Detail stock", "@sage/xtrem-stock/pages__stock_adjustment____navigationPanel__dropdownActions__title": "Set dimensions", "@sage/xtrem-stock/pages__stock_adjustment____navigationPanel__dropdownActions__title__2": "Delete", "@sage/xtrem-stock/pages__stock_adjustment____navigationPanel__listItem__description__title": "Description", "@sage/xtrem-stock/pages__stock_adjustment____navigationPanel__listItem__line2__title": "Site", "@sage/xtrem-stock/pages__stock_adjustment____navigationPanel__listItem__line2Right__title": "Date", "@sage/xtrem-stock/pages__stock_adjustment____navigationPanel__listItem__reasonCode__title": "Adjustment reason", "@sage/xtrem-stock/pages__stock_adjustment____navigationPanel__listItem__title__title": "Number", "@sage/xtrem-stock/pages__stock_adjustment____navigationPanel__listItem__titleRight__title": "Status", "@sage/xtrem-stock/pages__stock_adjustment____navigationPanel__optionsMenu__title": "All open statuses", "@sage/xtrem-stock/pages__stock_adjustment____navigationPanel__optionsMenu__title__2": "All statuses", "@sage/xtrem-stock/pages__stock_adjustment____navigationPanel__optionsMenu__title__3": "Details required", "@sage/xtrem-stock/pages__stock_adjustment____navigationPanel__optionsMenu__title__4": "Details entered", "@sage/xtrem-stock/pages__stock_adjustment____navigationPanel__optionsMenu__title__5": "Stock posting in progress", "@sage/xtrem-stock/pages__stock_adjustment____navigationPanel__optionsMenu__title__6": "Stock posting error", "@sage/xtrem-stock/pages__stock_adjustment____navigationPanel__optionsMenu__title__7": "Adjusted", "@sage/xtrem-stock/pages__stock_adjustment____objectTypePlural": "Stock adjustments", "@sage/xtrem-stock/pages__stock_adjustment____objectTypeSingular": "Stock adjustment", "@sage/xtrem-stock/pages__stock_adjustment____title": "Stock adjustment", "@sage/xtrem-stock/pages__stock_adjustment__apply_dimensions_success": "Dimensions applied", "@sage/xtrem-stock/pages__stock_adjustment__defaultDimension____title": "Set dimensions", "@sage/xtrem-stock/pages__stock_adjustment__description____title": "Description", "@sage/xtrem-stock/pages__stock_adjustment__displayStatus____title": "Display status", "@sage/xtrem-stock/pages__stock_adjustment__effectiveDate____title": "Date", "@sage/xtrem-stock/pages__stock_adjustment__goToSysNotificationPage____title": "Retry", "@sage/xtrem-stock/pages__stock_adjustment__line_delete_action_dialog_content": "You are about to delete this stock adjustment line.", "@sage/xtrem-stock/pages__stock_adjustment__line_delete_action_dialog_title": "Confirm delete", "@sage/xtrem-stock/pages__stock_adjustment__lines____columns__columns__item__name__title": "Category", "@sage/xtrem-stock/pages__stock_adjustment__lines____columns__columns__item__name__title__2": "Unit", "@sage/xtrem-stock/pages__stock_adjustment__lines____columns__columns__item__name__title__3": "Lot management", "@sage/xtrem-stock/pages__stock_adjustment__lines____columns__columns__location__name__title": "Name", "@sage/xtrem-stock/pages__stock_adjustment__lines____columns__columns__location__name__title__2": "ID", "@sage/xtrem-stock/pages__stock_adjustment__lines____columns__columns__lot__id__columns__title__id": "ID", "@sage/xtrem-stock/pages__stock_adjustment__lines____columns__columns__lot__id__columns__title__name": "Name", "@sage/xtrem-stock/pages__stock_adjustment__lines____columns__columns__lot__id__title": "ID", "@sage/xtrem-stock/pages__stock_adjustment__lines____columns__columns__lot__id__title__2": "<PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_adjustment__lines____columns__columns__lot__id__title__3": "Expiration date", "@sage/xtrem-stock/pages__stock_adjustment__lines____columns__columns__stockStatus__title": "Name", "@sage/xtrem-stock/pages__stock_adjustment__lines____columns__columns__stockStatus__title__2": "ID", "@sage/xtrem-stock/pages__stock_adjustment__lines____columns__lookupDialogTitle__item__name": "Select item", "@sage/xtrem-stock/pages__stock_adjustment__lines____columns__lookupDialogTitle__location__name": "Select location", "@sage/xtrem-stock/pages__stock_adjustment__lines____columns__lookupDialogTitle__lot__id": "Select lot", "@sage/xtrem-stock/pages__stock_adjustment__lines____columns__lookupDialogTitle__stockStatus": "Select quality value", "@sage/xtrem-stock/pages__stock_adjustment__lines____columns__placeholder__item__name": "Select ...", "@sage/xtrem-stock/pages__stock_adjustment__lines____columns__placeholder__lot__id": "Select ...", "@sage/xtrem-stock/pages__stock_adjustment__lines____columns__title__2": "Details", "@sage/xtrem-stock/pages__stock_adjustment__lines____columns__title__adjustmentQuantityInStockUnit": "Adjustment quantity", "@sage/xtrem-stock/pages__stock_adjustment__lines____columns__title__displayStatus": "Status", "@sage/xtrem-stock/pages__stock_adjustment__lines____columns__title__item__description": "Item description", "@sage/xtrem-stock/pages__stock_adjustment__lines____columns__title__item__id": "Item ID", "@sage/xtrem-stock/pages__stock_adjustment__lines____columns__title__item__name": "<PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_adjustment__lines____columns__title__location__name": "Location", "@sage/xtrem-stock/pages__stock_adjustment__lines____columns__title__lot__id": "Lot", "@sage/xtrem-stock/pages__stock_adjustment__lines____columns__title__newStockQuantity": "New stock quantity", "@sage/xtrem-stock/pages__stock_adjustment__lines____columns__title__quantityInStockUnit": "Stock quantity", "@sage/xtrem-stock/pages__stock_adjustment__lines____columns__title__stockStatus": "Quality control", "@sage/xtrem-stock/pages__stock_adjustment__lines____columns__title__stockValue": "Stock value", "@sage/xtrem-stock/pages__stock_adjustment__lines____columns__title__unitCost": "Unit cost", "@sage/xtrem-stock/pages__stock_adjustment__lines____dropdownActions__title": "Stock details", "@sage/xtrem-stock/pages__stock_adjustment__lines____dropdownActions__title__2": "Dimensions", "@sage/xtrem-stock/pages__stock_adjustment__lines____dropdownActions__title__3": "Delete", "@sage/xtrem-stock/pages__stock_adjustment__lines____inlineActions__title": "Open line panel", "@sage/xtrem-stock/pages__stock_adjustment__lines____mobileCard__line2__title": "Description", "@sage/xtrem-stock/pages__stock_adjustment__lines____mobileCard__title__title": "Product", "@sage/xtrem-stock/pages__stock_adjustment__lines____mobileCard__titleRight__title": "Status", "@sage/xtrem-stock/pages__stock_adjustment__lines____optionsMenu__title": "All statuses", "@sage/xtrem-stock/pages__stock_adjustment__lines____optionsMenu__title__2": "All open statuses", "@sage/xtrem-stock/pages__stock_adjustment__lines____optionsMenu__title__3": "Details required", "@sage/xtrem-stock/pages__stock_adjustment__lines____optionsMenu__title__4": "Details entered", "@sage/xtrem-stock/pages__stock_adjustment__lines____optionsMenu__title__5": "Stock posting in progress", "@sage/xtrem-stock/pages__stock_adjustment__lines____optionsMenu__title__6": "Stock posting error", "@sage/xtrem-stock/pages__stock_adjustment__lines____optionsMenu__title__7": "Adjusted", "@sage/xtrem-stock/pages__stock_adjustment__lines____sidebar__headerDropdownActions__title": "Stock details", "@sage/xtrem-stock/pages__stock_adjustment__lines____sidebar__headerDropdownActions__title__2": "Dimensions", "@sage/xtrem-stock/pages__stock_adjustment__lines____sidebar__headerDropdownActions__title__3": "Delete", "@sage/xtrem-stock/pages__stock_adjustment__lines____title": "Lines", "@sage/xtrem-stock/pages__stock_adjustment__linesSection____title": "Lines", "@sage/xtrem-stock/pages__stock_adjustment__mainSection____title": "General", "@sage/xtrem-stock/pages__stock_adjustment__number____title": "Number", "@sage/xtrem-stock/pages__stock_adjustment__post____title": "Post stock", "@sage/xtrem-stock/pages__stock_adjustment__postingDetails____columns__title__financeIntegrationAppRecordId": "Accounting integration reference", "@sage/xtrem-stock/pages__stock_adjustment__postingDetails____columns__title__postingStatus": "Status", "@sage/xtrem-stock/pages__stock_adjustment__postingDetails____columns__title__targetDocumentNumber": "Document number", "@sage/xtrem-stock/pages__stock_adjustment__postingDetails____columns__title__targetDocumentType": "Document type", "@sage/xtrem-stock/pages__stock_adjustment__postingDetails____title": "Results", "@sage/xtrem-stock/pages__stock_adjustment__postingMessageBlock____title": "Error details", "@sage/xtrem-stock/pages__stock_adjustment__postingSection____title": "Posting", "@sage/xtrem-stock/pages__stock_adjustment__reasonCode____columns__title__quantityDecreaseAdjustment": "Quantity decrease", "@sage/xtrem-stock/pages__stock_adjustment__reasonCode____columns__title__quantityIncreaseAdjustment": "Quantity increase", "@sage/xtrem-stock/pages__stock_adjustment__reasonCode____lookupDialogTitle": "Select reason code", "@sage/xtrem-stock/pages__stock_adjustment__reasonCode____title": "Adjustment reason", "@sage/xtrem-stock/pages__stock_adjustment__repost____title": "Repost", "@sage/xtrem-stock/pages__stock_adjustment__repost_errors": "Errors while reposting:", "@sage/xtrem-stock/pages__stock_adjustment__resync_display_status_continue": "Continue", "@sage/xtrem-stock/pages__stock_adjustment__resync_display_status_message": "You are about to correct this stock adjustment stock status to Completed.", "@sage/xtrem-stock/pages__stock_adjustment__resync_display_status_title": "Stock transaction status", "@sage/xtrem-stock/pages__stock_adjustment__saveStockAdjustment____title": "Save", "@sage/xtrem-stock/pages__stock_adjustment__stock_journal_inquiry": "Stock journal inquiry", "@sage/xtrem-stock/pages__stock_adjustment__stock_posting_error": "Stock posting error", "@sage/xtrem-stock/pages__stock_adjustment__stockSite____columns__columns__legalCompany__name__columns__title__symbol": "Symbol", "@sage/xtrem-stock/pages__stock_adjustment__stockSite____lookupDialogTitle": "Select site", "@sage/xtrem-stock/pages__stock_adjustment__stockSite____placeholder": "Select ...", "@sage/xtrem-stock/pages__stock_adjustment__stockSite____title": "Site", "@sage/xtrem-stock/pages__stock_adjustment__stockTransactionStatus____title": "Stock status", "@sage/xtrem-stock/pages__stock_adjustment__The_lot_is_mandatory": "Enter the lot.", "@sage/xtrem-stock/pages__stock_adjustment_step_sequence_creation": "Create", "@sage/xtrem-stock/pages__stock_adjustment_step_sequence_post_stock": "Post stock", "@sage/xtrem-stock/pages__stock_allocation_inquiry____navigationPanel__listItem__documentLink__title": "Document number", "@sage/xtrem-stock/pages__stock_allocation_inquiry____navigationPanel__listItem__quantityInStockUnit__title": "Allocated quantity", "@sage/xtrem-stock/pages__stock_allocation_inquiry____navigationPanel__listItem__title__title": "Document number", "@sage/xtrem-stock/pages__stock_allocation_inquiry____title": "Stock allocation inquiry", "@sage/xtrem-stock/pages__stock_avc_justification_inquiry____title": "Average unit cost justification", "@sage/xtrem-stock/pages__stock_avc_justification_inquiry__company____lookupDialogTitle": "Select company", "@sage/xtrem-stock/pages__stock_avc_justification_inquiry__company____placeholder": "Select ...", "@sage/xtrem-stock/pages__stock_avc_justification_inquiry__company____title": "Company", "@sage/xtrem-stock/pages__stock_avc_justification_inquiry__criteriaBlock____title": "Criteria", "@sage/xtrem-stock/pages__stock_avc_justification_inquiry__itemFrom____columns__title__category__name": "Category", "@sage/xtrem-stock/pages__stock_avc_justification_inquiry__itemFrom____lookupDialogTitle": "Select item", "@sage/xtrem-stock/pages__stock_avc_justification_inquiry__itemFrom____placeholder": "Select ...", "@sage/xtrem-stock/pages__stock_avc_justification_inquiry__itemFrom____title": "From item", "@sage/xtrem-stock/pages__stock_avc_justification_inquiry__itemSelection____columns__title__item__description": "Item description", "@sage/xtrem-stock/pages__stock_avc_justification_inquiry__itemSelection____columns__title__item__id": "Item ID", "@sage/xtrem-stock/pages__stock_avc_justification_inquiry__itemSelection____columns__title__item__name": "<PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_avc_justification_inquiry__itemSelection____columns__title__site__name": "Site", "@sage/xtrem-stock/pages__stock_avc_justification_inquiry__itemSelection____columns__title__stockUnit__name": "Unit of measure", "@sage/xtrem-stock/pages__stock_avc_justification_inquiry__itemSelection____title": "Results", "@sage/xtrem-stock/pages__stock_avc_justification_inquiry__itemTo____columns__title__category__name": "Category", "@sage/xtrem-stock/pages__stock_avc_justification_inquiry__itemTo____columns__title__description": "Description", "@sage/xtrem-stock/pages__stock_avc_justification_inquiry__itemTo____columns__title__id": "<PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_avc_justification_inquiry__itemTo____columns__title__name": "Name", "@sage/xtrem-stock/pages__stock_avc_justification_inquiry__itemTo____lookupDialogTitle": "Select item", "@sage/xtrem-stock/pages__stock_avc_justification_inquiry__itemTo____placeholder": "Select ...", "@sage/xtrem-stock/pages__stock_avc_justification_inquiry__itemTo____title": "To item", "@sage/xtrem-stock/pages__stock_avc_justification_inquiry__itemValues____levels__columns__columns__stockUnit__symbol__title": "Symbol", "@sage/xtrem-stock/pages__stock_avc_justification_inquiry__itemValues____levels__columns__title__amountVariance": "Amount variance", "@sage/xtrem-stock/pages__stock_avc_justification_inquiry__itemValues____levels__columns__title__company__name": "Company", "@sage/xtrem-stock/pages__stock_avc_justification_inquiry__itemValues____levels__columns__title__cost": "Unit cost", "@sage/xtrem-stock/pages__stock_avc_justification_inquiry__itemValues____levels__columns__title__costVariance": "Cost variance", "@sage/xtrem-stock/pages__stock_avc_justification_inquiry__itemValues____levels__columns__title__creationDate": "Date created", "@sage/xtrem-stock/pages__stock_avc_justification_inquiry__itemValues____levels__columns__title__currency__symbol": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_avc_justification_inquiry__itemValues____levels__columns__title__dateOfValuation": "Valuation date", "@sage/xtrem-stock/pages__stock_avc_justification_inquiry__itemValues____levels__columns__title__effectiveDate": "Date", "@sage/xtrem-stock/pages__stock_avc_justification_inquiry__itemValues____levels__columns__title__item__id": "<PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_avc_justification_inquiry__itemValues____levels__columns__title__item__name": "Name", "@sage/xtrem-stock/pages__stock_avc_justification_inquiry__itemValues____levels__columns__title__movementAmount": "Movement amount", "@sage/xtrem-stock/pages__stock_avc_justification_inquiry__itemValues____levels__columns__title__orderAmount": "Order amount", "@sage/xtrem-stock/pages__stock_avc_justification_inquiry__itemValues____levels__columns__title__orderCost": "Order cost", "@sage/xtrem-stock/pages__stock_avc_justification_inquiry__itemValues____levels__columns__title__postingClass__name": "Posting class", "@sage/xtrem-stock/pages__stock_avc_justification_inquiry__itemValues____levels__columns__title__quantityInStock": "Quantity", "@sage/xtrem-stock/pages__stock_avc_justification_inquiry__itemValues____levels__columns__title__quantityInStockUnit": "Quantity", "@sage/xtrem-stock/pages__stock_avc_justification_inquiry__itemValues____levels__columns__title__sequence": "Sequence", "@sage/xtrem-stock/pages__stock_avc_justification_inquiry__itemValues____levels__columns__title__site__name": "Site", "@sage/xtrem-stock/pages__stock_avc_justification_inquiry__itemValues____levels__columns__title__stockDetail__documentLine__documentNumber": "Document", "@sage/xtrem-stock/pages__stock_avc_justification_inquiry__itemValues____levels__columns__title__stockQuantity": "Stock quantity", "@sage/xtrem-stock/pages__stock_avc_justification_inquiry__itemValues____levels__columns__title__stockUnit__name": "Unit of measure", "@sage/xtrem-stock/pages__stock_avc_justification_inquiry__itemValues____levels__columns__title__stockUnit__symbol": "Stock unit", "@sage/xtrem-stock/pages__stock_avc_justification_inquiry__itemValues____levels__columns__title__stockValue": "Stock value", "@sage/xtrem-stock/pages__stock_avc_justification_inquiry__itemValues____levels__columns__title__stockValue__2": "Stock value", "@sage/xtrem-stock/pages__stock_avc_justification_inquiry__itemValues____levels__columns__title__type": "Document type", "@sage/xtrem-stock/pages__stock_avc_justification_inquiry__itemValues____levels__columns__title__unitCost": "Unit cost", "@sage/xtrem-stock/pages__stock_avc_justification_inquiry__itemValues____levels__columns__title__valuationMethod": "Cost type", "@sage/xtrem-stock/pages__stock_avc_justification_inquiry__itemValues____levels__columns__title__valuedCost": "Valued cost", "@sage/xtrem-stock/pages__stock_avc_justification_inquiry__mainSection____title": "General", "@sage/xtrem-stock/pages__stock_avc_justification_inquiry__runStockValuation____title": "Run", "@sage/xtrem-stock/pages__stock_avc_justification_inquiry__sites____lookupDialogTitle": "Select site", "@sage/xtrem-stock/pages__stock_avc_justification_inquiry__sites____placeholder": "Select ...", "@sage/xtrem-stock/pages__stock_avc_justification_inquiry__sites____title": "Site", "@sage/xtrem-stock/pages__stock_avc_justification_inquiry__valuationDate____title": "Date", "@sage/xtrem-stock/pages__stock_change____navigationPanel__listItem__description__title": "Description", "@sage/xtrem-stock/pages__stock_change____navigationPanel__listItem__itemDescription__title": "Item description", "@sage/xtrem-stock/pages__stock_change____navigationPanel__listItem__itemId__title": "Item ID", "@sage/xtrem-stock/pages__stock_change____navigationPanel__listItem__line2__title": "Site name", "@sage/xtrem-stock/pages__stock_change____navigationPanel__listItem__line2Right__title": "Date", "@sage/xtrem-stock/pages__stock_change____navigationPanel__listItem__line3__title": "<PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_change____navigationPanel__listItem__siteID__title": "Site ID", "@sage/xtrem-stock/pages__stock_change____navigationPanel__listItem__title__title": "Number", "@sage/xtrem-stock/pages__stock_change____objectTypePlural": "Stock changes", "@sage/xtrem-stock/pages__stock_change____objectTypeSingular": "Stock change", "@sage/xtrem-stock/pages__stock_change____title": "Stock change", "@sage/xtrem-stock/pages__stock_change__addLine____title": "Add line", "@sage/xtrem-stock/pages__stock_change__availableQuantity____title": "Available quantity", "@sage/xtrem-stock/pages__stock_change__description____title": "Description", "@sage/xtrem-stock/pages__stock_change__effectiveDate____title": "Date", "@sage/xtrem-stock/pages__stock_change__initialStockBlock____title": "Initial stock", "@sage/xtrem-stock/pages__stock_change__item____columns__title__category__name": "Category", "@sage/xtrem-stock/pages__stock_change__item____lookupDialogTitle": "Select item", "@sage/xtrem-stock/pages__stock_change__item____title": "<PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_change__lines____columns__columns__endingSerialNumber__id__title": "Supplier serial number", "@sage/xtrem-stock/pages__stock_change__lines____columns__columns__endingSerialNumber__id__title__2": "Supplier", "@sage/xtrem-stock/pages__stock_change__lines____columns__columns__location__name__title": "Name", "@sage/xtrem-stock/pages__stock_change__lines____columns__columns__location__name__title__2": "ID", "@sage/xtrem-stock/pages__stock_change__lines____columns__columns__startingSerialNumber__id__title": "Supplier serial number", "@sage/xtrem-stock/pages__stock_change__lines____columns__columns__startingSerialNumber__id__title__2": "Supplier", "@sage/xtrem-stock/pages__stock_change__lines____columns__lookupDialogTitle__startingSerialNumber__id": "Select from serial number", "@sage/xtrem-stock/pages__stock_change__lines____columns__lookupDialogTitle__stockStatus": "Select quality value", "@sage/xtrem-stock/pages__stock_change__lines____columns__title__endingSerialNumber__id": "To serial number", "@sage/xtrem-stock/pages__stock_change__lines____columns__title__location__name": "Location", "@sage/xtrem-stock/pages__stock_change__lines____columns__title__quantityInStockUnit": "Quantity", "@sage/xtrem-stock/pages__stock_change__lines____columns__title__startingSerialNumber__id": "From serial number", "@sage/xtrem-stock/pages__stock_change__lines____columns__title__stockStatus": "Quality control", "@sage/xtrem-stock/pages__stock_change__lines____columns__title__stockTransactionStatus": "Stock status", "@sage/xtrem-stock/pages__stock_change__lines____dropdownActions__title": "Delete", "@sage/xtrem-stock/pages__stock_change__lines____title": "Changes", "@sage/xtrem-stock/pages__stock_change__location____columns__title__id": "ID", "@sage/xtrem-stock/pages__stock_change__location____columns__title__name": "Name", "@sage/xtrem-stock/pages__stock_change__location____title": "Location", "@sage/xtrem-stock/pages__stock_change__lot____columns__title__expirationDate": "Expiration date", "@sage/xtrem-stock/pages__stock_change__lot____title": "Lot", "@sage/xtrem-stock/pages__stock_change__mainSection____title": "General", "@sage/xtrem-stock/pages__stock_change__number____title": "Number", "@sage/xtrem-stock/pages__stock_change__owner____title": "Owner", "@sage/xtrem-stock/pages__stock_change__post____title": "Post", "@sage/xtrem-stock/pages__stock_change__postingError____title": "Posting error", "@sage/xtrem-stock/pages__stock_change__saveStockChange____title": "Save", "@sage/xtrem-stock/pages__stock_change__status____title": "Status", "@sage/xtrem-stock/pages__stock_change__stockRecord____columns__columns__location__name__title": "Zone", "@sage/xtrem-stock/pages__stock_change__stockRecord____columns__columns__site__name__title": "Location management", "@sage/xtrem-stock/pages__stock_change__stockRecord____columns__title__item__name": "<PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_change__stockRecord____columns__title__location__name": "Location", "@sage/xtrem-stock/pages__stock_change__stockRecord____columns__title__lot__id": "Lot", "@sage/xtrem-stock/pages__stock_change__stockRecord____columns__title__owner": "Owner", "@sage/xtrem-stock/pages__stock_change__stockRecord____columns__title__quantityInStockUnit": "Quantity", "@sage/xtrem-stock/pages__stock_change__stockRecord____columns__title__site__name": "Site", "@sage/xtrem-stock/pages__stock_change__stockRecord____columns__title__totalAllocated": "Allocated", "@sage/xtrem-stock/pages__stock_change__stockRecord____title": "Stock", "@sage/xtrem-stock/pages__stock_change__stockSite____lookupDialogTitle": "Select site", "@sage/xtrem-stock/pages__stock_change__stockSite____title": "Site", "@sage/xtrem-stock/pages__stock_change__stockStatus____lookupDialogTitle": "Select quality value", "@sage/xtrem-stock/pages__stock_change__stockStatus____title": "Quality control", "@sage/xtrem-stock/pages__stock_change__stockTransactionStatus____title": "Stock status", "@sage/xtrem-stock/pages__stock_change__totalQuantity____title": "Total quantity", "@sage/xtrem-stock/pages__stock_change_available_error": "Serial numbers available in this range: {{available}}", "@sage/xtrem-stock/pages__stock_change_duplicate_error": "The serial number is already included in another range.", "@sage/xtrem-stock/pages__stock_change_duplicate_range_error": "One of the serial numbers is already included in another range.", "@sage/xtrem-stock/pages__stock_change_missing_from_serial_number": "You need to enter a From serial number.", "@sage/xtrem-stock/pages__stock_change_zero_quantity_error": "Enter a quantity greater than 0.", "@sage/xtrem-stock/pages__stock_count____navigationPanel__listItem__counter__title": "Counter", "@sage/xtrem-stock/pages__stock_count____navigationPanel__listItem__line2__title": "Description", "@sage/xtrem-stock/pages__stock_count____navigationPanel__listItem__line2Right__title": "Date", "@sage/xtrem-stock/pages__stock_count____navigationPanel__listItem__stockSite__title": "Site", "@sage/xtrem-stock/pages__stock_count____navigationPanel__listItem__stockTransactionStatus__title": "Stock status", "@sage/xtrem-stock/pages__stock_count____navigationPanel__listItem__title__title": "Number", "@sage/xtrem-stock/pages__stock_count____navigationPanel__listItem__titleRight__title": "Status", "@sage/xtrem-stock/pages__stock_count____navigationPanel__optionsMenu__title": "", "@sage/xtrem-stock/pages__stock_count____objectTypePlural": "Stock counts", "@sage/xtrem-stock/pages__stock_count____objectTypeSingular": "Stock count", "@sage/xtrem-stock/pages__stock_count____title": "Stock count", "@sage/xtrem-stock/pages__stock_count__categories____title": "Category", "@sage/xtrem-stock/pages__stock_count__confirm_zero_quantity_result": "Lines updated: {{recordsUpdated}}.", "@sage/xtrem-stock/pages__stock_count__confirmZeroQuantity____title": "Confirm zero quantities", "@sage/xtrem-stock/pages__stock_count__counted_quantity_can_not_be_greater_than_quantity_in_stock": "The counted quantity cannot exceed the stock quantity for a serialized item.", "@sage/xtrem-stock/pages__stock_count__counter____title": "Counter", "@sage/xtrem-stock/pages__stock_count__creation": "The stock count was created successfully.", "@sage/xtrem-stock/pages__stock_count__criteriaBlock____title": "Selection criteria", "@sage/xtrem-stock/pages__stock_count__defaultDimension____title": "Set default dimensions", "@sage/xtrem-stock/pages__stock_count__description____title": "Description", "@sage/xtrem-stock/pages__stock_count__duplication_among_added_lines": "Change or remove duplicate lines.", "@sage/xtrem-stock/pages__stock_count__effectiveDate____title": "Date", "@sage/xtrem-stock/pages__stock_count__fromItem____title": "From item", "@sage/xtrem-stock/pages__stock_count__goToSysNotificationPage____title": "Retry", "@sage/xtrem-stock/pages__stock_count__hasStockRecords____title": "In stock only", "@sage/xtrem-stock/pages__stock_count__lastCountDate____title": "Items not counted as of", "@sage/xtrem-stock/pages__stock_count__lines____columns__columns__item__id__columns__title__symbol": "Symbol", "@sage/xtrem-stock/pages__stock_count__lines____columns__columns__item__name__columns__title__symbol": "Symbol", "@sage/xtrem-stock/pages__stock_count__lines____columns__columns__location__name__title": "Name", "@sage/xtrem-stock/pages__stock_count__lines____columns__columns__location__name__title__2": "ID", "@sage/xtrem-stock/pages__stock_count__lines____columns__columns__location__name__title__3": "Type", "@sage/xtrem-stock/pages__stock_count__lines____columns__columns__stockDetail__stockDetailLot__lotId__id__title": "ID", "@sage/xtrem-stock/pages__stock_count__lines____columns__columns__stockDetail__stockDetailLot__lotId__id__title__2": "Sublot", "@sage/xtrem-stock/pages__stock_count__lines____columns__columns__stockDetail__stockDetailLot__lotId__id__title__3": "Expiration date", "@sage/xtrem-stock/pages__stock_count__lines____columns__columns__stockDetail__stockDetailLot__lotId__id__title__4": "Supplier lot number", "@sage/xtrem-stock/pages__stock_count__lines____columns__columns__stockDetail__stockDetailLot__lotId__id__title__5": "<PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_count__lines____columns__columns__stockStatus__name__title": "Name", "@sage/xtrem-stock/pages__stock_count__lines____columns__columns__stockStatus__name__title__2": "ID", "@sage/xtrem-stock/pages__stock_count__lines____columns__columns__stockStatus__name__title__3": "Description", "@sage/xtrem-stock/pages__stock_count__lines____columns__lookupDialogTitle__stockDetail__stockDetailLot__lotId__id": "Select lot", "@sage/xtrem-stock/pages__stock_count__lines____columns__postfix__quantityVariancePercentage": "%", "@sage/xtrem-stock/pages__stock_count__lines____columns__title___id": "", "@sage/xtrem-stock/pages__stock_count__lines____columns__title__countedQuantityInStockUnit": "Counted quantity", "@sage/xtrem-stock/pages__stock_count__lines____columns__title__countedSerialNumberPercentage": "Serial number", "@sage/xtrem-stock/pages__stock_count__lines____columns__title__expirationDate": "Expiration date", "@sage/xtrem-stock/pages__stock_count__lines____columns__title__item__description": "Item description", "@sage/xtrem-stock/pages__stock_count__lines____columns__title__item__id": "Item ID", "@sage/xtrem-stock/pages__stock_count__lines____columns__title__item__name": "<PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_count__lines____columns__title__location__locationZone__name": "Zone", "@sage/xtrem-stock/pages__stock_count__lines____columns__title__location__name": "Location", "@sage/xtrem-stock/pages__stock_count__lines____columns__title__lot__sublot": "Sublot", "@sage/xtrem-stock/pages__stock_count__lines____columns__title__lotNumber": "Lot number", "@sage/xtrem-stock/pages__stock_count__lines____columns__title__newLineOrderCost": "Unit cost", "@sage/xtrem-stock/pages__stock_count__lines____columns__title__owner": "Owner", "@sage/xtrem-stock/pages__stock_count__lines____columns__title__potency": "Potency", "@sage/xtrem-stock/pages__stock_count__lines____columns__title__quantityInStockUnit": "Stock quantity", "@sage/xtrem-stock/pages__stock_count__lines____columns__title__quantityVariance": "Quantity variance", "@sage/xtrem-stock/pages__stock_count__lines____columns__title__quantityVariancePercentage": "Quantity variance %", "@sage/xtrem-stock/pages__stock_count__lines____columns__title__status": "Count status", "@sage/xtrem-stock/pages__stock_count__lines____columns__title__stockDetail__stockDetailLot__lotId__id": "Lot", "@sage/xtrem-stock/pages__stock_count__lines____columns__title__stockStatus__name": "Quality control", "@sage/xtrem-stock/pages__stock_count__lines____columns__title__stockTransactionStatus": "Stock status", "@sage/xtrem-stock/pages__stock_count__lines____columns__title__sublot": "Sublot number", "@sage/xtrem-stock/pages__stock_count__lines____columns__title__sublotNumber": "Sublot number", "@sage/xtrem-stock/pages__stock_count__lines____columns__title__supplierLot": "Supplier lot number", "@sage/xtrem-stock/pages__stock_count__lines____columns__title__supplierLotNumber": "Supplier lot number", "@sage/xtrem-stock/pages__stock_count__lines____dropdownActions__title": "Count", "@sage/xtrem-stock/pages__stock_count__lines____dropdownActions__title__2": "Exclude", "@sage/xtrem-stock/pages__stock_count__lines____dropdownActions__title__3": "Confirm zero quantity", "@sage/xtrem-stock/pages__stock_count__lines____dropdownActions__title__4": "Dimensions", "@sage/xtrem-stock/pages__stock_count__lines____dropdownActions__title__5": "Serial numbers", "@sage/xtrem-stock/pages__stock_count__lines____dropdownActions__title__6": "Delete", "@sage/xtrem-stock/pages__stock_count__lines____inlineActions__title": "Open line panel", "@sage/xtrem-stock/pages__stock_count__lines____title": "Document lines", "@sage/xtrem-stock/pages__stock_count__linesSection____title": "Lines", "@sage/xtrem-stock/pages__stock_count__locations____title": "Location", "@sage/xtrem-stock/pages__stock_count__mainSection____title": "General", "@sage/xtrem-stock/pages__stock_count__number____title": "Number", "@sage/xtrem-stock/pages__stock_count__order_cost_should_be_entered": "Check your unit cost. A value of 0 can affect the stock value.", "@sage/xtrem-stock/pages__stock_count__post____title": "Post stock", "@sage/xtrem-stock/pages__stock_count__postingDetails____columns__title__financeIntegrationAppRecordId": "Accounting integration reference", "@sage/xtrem-stock/pages__stock_count__postingDetails____columns__title__postingStatus": "Status", "@sage/xtrem-stock/pages__stock_count__postingDetails____columns__title__targetDocumentNumber": "Document number", "@sage/xtrem-stock/pages__stock_count__postingDetails____columns__title__targetDocumentType": "Document type", "@sage/xtrem-stock/pages__stock_count__postingDetails____title": "Results", "@sage/xtrem-stock/pages__stock_count__postingMessageBlock____title": "Error details", "@sage/xtrem-stock/pages__stock_count__postingSection____title": "Posting", "@sage/xtrem-stock/pages__stock_count__print____title": "Print", "@sage/xtrem-stock/pages__stock_count__repost____title": "Repost", "@sage/xtrem-stock/pages__stock_count__repost_errors": "Errors while reposting:", "@sage/xtrem-stock/pages__stock_count__saveStockCount____title": "Save", "@sage/xtrem-stock/pages__stock_count__selected_serial_number_quantity_too_high": "The number of selected serial numbers ({{selectedQuantity}}) cannot exceed the counted quantity ({{countedQuantity}}).", "@sage/xtrem-stock/pages__stock_count__showQuantityInStock____title": "Show quantity in stock", "@sage/xtrem-stock/pages__stock_count__sidebar_tab_title_information": "Information", "@sage/xtrem-stock/pages__stock_count__start____title": "Start", "@sage/xtrem-stock/pages__stock_count__status____title": "Status", "@sage/xtrem-stock/pages__stock_count__stockSite____title": "Site", "@sage/xtrem-stock/pages__stock_count__stockTransactionStatus____title": "Stock status", "@sage/xtrem-stock/pages__stock_count__toItem____title": "To item", "@sage/xtrem-stock/pages__stock_count__zones____title": "Zone", "@sage/xtrem-stock/pages__stock_count_creation____navigationPanel__listItem__line2__title": "Site", "@sage/xtrem-stock/pages__stock_count_creation____navigationPanel__optionsMenu__title": "Draft", "@sage/xtrem-stock/pages__stock_count_creation____title": "Stock count creation", "@sage/xtrem-stock/pages__stock_count_creation__categories____columns__title__id": "ID", "@sage/xtrem-stock/pages__stock_count_creation__categories____columns__title__name": "Name", "@sage/xtrem-stock/pages__stock_count_creation__categories____lookupDialogTitle": "Select category", "@sage/xtrem-stock/pages__stock_count_creation__categories____placeholder": "Select...", "@sage/xtrem-stock/pages__stock_count_creation__categories____title": "Category", "@sage/xtrem-stock/pages__stock_count_creation__createStockCount____title": "Create", "@sage/xtrem-stock/pages__stock_count_creation__criteriaBlock____title": "Criteria", "@sage/xtrem-stock/pages__stock_count_creation__description____title": "Description", "@sage/xtrem-stock/pages__stock_count_creation__effectiveDate____title": "Date", "@sage/xtrem-stock/pages__stock_count_creation__fromItem____columns__title__description": "Description", "@sage/xtrem-stock/pages__stock_count_creation__fromItem____columns__title__id": "ID", "@sage/xtrem-stock/pages__stock_count_creation__fromItem____columns__title__name": "Name", "@sage/xtrem-stock/pages__stock_count_creation__fromItem____lookupDialogTitle": "Select item", "@sage/xtrem-stock/pages__stock_count_creation__fromItem____placeholder": "Select ...", "@sage/xtrem-stock/pages__stock_count_creation__fromItem____title": "From item", "@sage/xtrem-stock/pages__stock_count_creation__hasStockRecords____title": "In stock only", "@sage/xtrem-stock/pages__stock_count_creation__invalid_item_range": "Enter a 'To item' higher than the 'From item'.", "@sage/xtrem-stock/pages__stock_count_creation__itemSites____levels__columns__title__hasStockRecords": "Stock record", "@sage/xtrem-stock/pages__stock_count_creation__itemSites____levels__columns__title__item__description": "Item description", "@sage/xtrem-stock/pages__stock_count_creation__itemSites____levels__columns__title__item__id": "Item ID", "@sage/xtrem-stock/pages__stock_count_creation__itemSites____levels__columns__title__item__name": "<PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_count_creation__itemSites____levels__columns__title__location__locationZone__name": "Zone", "@sage/xtrem-stock/pages__stock_count_creation__itemSites____levels__columns__title__location__name": "Location", "@sage/xtrem-stock/pages__stock_count_creation__itemSites____levels__columns__title__lot__id": "Lot", "@sage/xtrem-stock/pages__stock_count_creation__itemSites____levels__columns__title__lot__sublot": "Sublot", "@sage/xtrem-stock/pages__stock_count_creation__itemSites____levels__columns__title__owner": "Owner", "@sage/xtrem-stock/pages__stock_count_creation__itemSites____levels__columns__title__quantityInStockUnit": "Quantity", "@sage/xtrem-stock/pages__stock_count_creation__itemSites____levels__columns__title__status__name": "Status", "@sage/xtrem-stock/pages__stock_count_creation__lastCountDate____title": "Items not counted as of", "@sage/xtrem-stock/pages__stock_count_creation__locations____columns__title__id": "ID", "@sage/xtrem-stock/pages__stock_count_creation__locations____columns__title__name": "Name", "@sage/xtrem-stock/pages__stock_count_creation__locations____lookupDialogTitle": "Select location", "@sage/xtrem-stock/pages__stock_count_creation__locations____placeholder": "Select ...", "@sage/xtrem-stock/pages__stock_count_creation__locations____title": "Location", "@sage/xtrem-stock/pages__stock_count_creation__mainSection____title": "General", "@sage/xtrem-stock/pages__stock_count_creation__selectAllCheckbox____title": "Select all", "@sage/xtrem-stock/pages__stock_count_creation__stockCountBlock____title": "Stock count", "@sage/xtrem-stock/pages__stock_count_creation__stockSite____lookupDialogTitle": "Select site", "@sage/xtrem-stock/pages__stock_count_creation__stockSite____placeholder": "Select ...", "@sage/xtrem-stock/pages__stock_count_creation__stockSite____title": "Site", "@sage/xtrem-stock/pages__stock_count_creation__toItem____columns__title__description": "Description", "@sage/xtrem-stock/pages__stock_count_creation__toItem____columns__title__id": "ID", "@sage/xtrem-stock/pages__stock_count_creation__toItem____columns__title__name": "Name", "@sage/xtrem-stock/pages__stock_count_creation__toItem____lookupDialogTitle": "Select item", "@sage/xtrem-stock/pages__stock_count_creation__toItem____placeholder": "Select ...", "@sage/xtrem-stock/pages__stock_count_creation__toItem____title": "To item", "@sage/xtrem-stock/pages__stock_count_creation__zones____columns__title__id": "ID", "@sage/xtrem-stock/pages__stock_count_creation__zones____columns__title__name": "Name", "@sage/xtrem-stock/pages__stock_count_creation__zones____columns__title__zoneType": "Type", "@sage/xtrem-stock/pages__stock_count_creation__zones____lookupDialogTitle": "Select storage zone", "@sage/xtrem-stock/pages__stock_count_creation__zones____placeholder": "Select ...", "@sage/xtrem-stock/pages__stock_count_creation__zones____title": "Zone", "@sage/xtrem-stock/pages__stock_count_line__not_add_a_line_matching_with_a_stock_record": "You need to change the details on this stock count line because it duplicates existing stock information. Item: {{item}}, site: {{site}}, location: {{location}}, lot: {{lot}}, status: {{status}}, stock unit: {{stockUnit}}, owner: {{owner}}.", "@sage/xtrem-stock/pages__stock_count_serial_numbers_panel____title": "Serial numbers", "@sage/xtrem-stock/pages__stock_count_serial_numbers_panel__addSerialNumberRange____title": "Add line", "@sage/xtrem-stock/pages__stock_count_serial_numbers_panel__cancel____title": "Cancel", "@sage/xtrem-stock/pages__stock_count_serial_numbers_panel__duplicate_error": "The serial number is already included in another range.", "@sage/xtrem-stock/pages__stock_count_serial_numbers_panel__duplicate_range_error": "One of the serial numbers is already included in another range.", "@sage/xtrem-stock/pages__stock_count_serial_numbers_panel__item____columns__title__description": "Description", "@sage/xtrem-stock/pages__stock_count_serial_numbers_panel__item____columns__title__id": "<PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_count_serial_numbers_panel__item____columns__title__name": "Name", "@sage/xtrem-stock/pages__stock_count_serial_numbers_panel__item____lookupDialogTitle": "Select item", "@sage/xtrem-stock/pages__stock_count_serial_numbers_panel__item____title": "<PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_count_serial_numbers_panel__mainBlock____title": "General", "@sage/xtrem-stock/pages__stock_count_serial_numbers_panel__mainSection____title": "General", "@sage/xtrem-stock/pages__stock_count_serial_numbers_panel__ok____title": "OK", "@sage/xtrem-stock/pages__stock_count_serial_numbers_panel__quantityToIssue____title": "Serial numbers to be issued", "@sage/xtrem-stock/pages__stock_count_serial_numbers_panel__remainingQuantity____title": "Remaining quantity", "@sage/xtrem-stock/pages__stock_count_serial_numbers_panel__requiredQuantity____title": "Required quantity", "@sage/xtrem-stock/pages__stock_count_serial_numbers_panel__selected_quantity_too_high": "The number of selected serial numbers ({{selectedQuantity}}) cannot exceed the counted quantity ({{countedQuantity}}).", "@sage/xtrem-stock/pages__stock_count_serial_numbers_panel__selectedQuantity____title": "Selected quantity", "@sage/xtrem-stock/pages__stock_count_serial_numbers_panel__serialNumberRanges____columns__columns__endingSerialNumber__serialNumber.id__title": "ID", "@sage/xtrem-stock/pages__stock_count_serial_numbers_panel__serialNumberRanges____columns__columns__startingSerialNumber__serialNumber__id__title": "ID", "@sage/xtrem-stock/pages__stock_count_serial_numbers_panel__serialNumberRanges____columns__lookupDialogTitle__startingSerialNumber__serialNumber__id": "Select from serial number", "@sage/xtrem-stock/pages__stock_count_serial_numbers_panel__serialNumberRanges____columns__title__endingSerialNumber__serialNumber.id": "To serial number", "@sage/xtrem-stock/pages__stock_count_serial_numbers_panel__serialNumberRanges____columns__title__numericEnd": "To serial number", "@sage/xtrem-stock/pages__stock_count_serial_numbers_panel__serialNumberRanges____columns__title__numericStart": "From serial number", "@sage/xtrem-stock/pages__stock_count_serial_numbers_panel__serialNumberRanges____columns__title__originalStartId": "Original from serial number", "@sage/xtrem-stock/pages__stock_count_serial_numbers_panel__serialNumberRanges____columns__title__quantity": "Quantity", "@sage/xtrem-stock/pages__stock_count_serial_numbers_panel__serialNumberRanges____columns__title__startingSerialNumber__serialNumber__id": "From serial number", "@sage/xtrem-stock/pages__stock_count_serial_numbers_panel__serialNumberRanges____dropdownActions__title": "Delete", "@sage/xtrem-stock/pages__stock_count_serial_numbers_panel__serialNumberRanges____title": "Serial numbers", "@sage/xtrem-stock/pages__stock_count_serial_numbers_panel__unit____columns__title__description": "Description", "@sage/xtrem-stock/pages__stock_count_serial_numbers_panel__unit____columns__title__id": "ID", "@sage/xtrem-stock/pages__stock_count_serial_numbers_panel__unit____columns__title__name": "Name", "@sage/xtrem-stock/pages__stock_count_serial_numbers_panel__unit____columns__title__symbol": "Symbol", "@sage/xtrem-stock/pages__stock_count_serial_numbers_panel__unit____title": "Stock unit", "@sage/xtrem-stock/pages__stock_detailed_inquiry____navigationPanel__dropdownActions__title": "Allocations", "@sage/xtrem-stock/pages__stock_detailed_inquiry____navigationPanel__dropdownActions__title__2": "Serial number information", "@sage/xtrem-stock/pages__stock_detailed_inquiry____navigationPanel__listItem__allocatedQuantity__title": "Allocated quantity", "@sage/xtrem-stock/pages__stock_detailed_inquiry____navigationPanel__listItem__availableQuantity__title": "Available quantity", "@sage/xtrem-stock/pages__stock_detailed_inquiry____navigationPanel__listItem__creationDate__title": "Creation date", "@sage/xtrem-stock/pages__stock_detailed_inquiry____navigationPanel__listItem__expirationDate__title": "Expiration date", "@sage/xtrem-stock/pages__stock_detailed_inquiry____navigationPanel__listItem__inTransitQuantityInStockUnit__title": "In-transit quantity", "@sage/xtrem-stock/pages__stock_detailed_inquiry____navigationPanel__listItem__item__title": "<PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_detailed_inquiry____navigationPanel__listItem__itemDescription__title": "Item description", "@sage/xtrem-stock/pages__stock_detailed_inquiry____navigationPanel__listItem__itemId__title": "Item ID", "@sage/xtrem-stock/pages__stock_detailed_inquiry____navigationPanel__listItem__locationId__title": "Location ID", "@sage/xtrem-stock/pages__stock_detailed_inquiry____navigationPanel__listItem__locationName__title": "Location", "@sage/xtrem-stock/pages__stock_detailed_inquiry____navigationPanel__listItem__locationZone__title": "Location zone", "@sage/xtrem-stock/pages__stock_detailed_inquiry____navigationPanel__listItem__lot__title": "Lot", "@sage/xtrem-stock/pages__stock_detailed_inquiry____navigationPanel__listItem__onHandQuantityInStockUnit__title": "On-hand quantity", "@sage/xtrem-stock/pages__stock_detailed_inquiry____navigationPanel__listItem__owner__title": "Owner", "@sage/xtrem-stock/pages__stock_detailed_inquiry____navigationPanel__listItem__siteId__title": "Site ID", "@sage/xtrem-stock/pages__stock_detailed_inquiry____navigationPanel__listItem__siteName__title": "Site", "@sage/xtrem-stock/pages__stock_detailed_inquiry____navigationPanel__listItem__status__title": "Quality control", "@sage/xtrem-stock/pages__stock_detailed_inquiry____navigationPanel__listItem__stockUnit__title": "Unit of measure", "@sage/xtrem-stock/pages__stock_detailed_inquiry____navigationPanel__listItem__sublot__title": "Sublot", "@sage/xtrem-stock/pages__stock_detailed_inquiry____navigationPanel__listItem__supplierLot__title": "Supplier lot number", "@sage/xtrem-stock/pages__stock_detailed_inquiry____navigationPanel__optionsMenu__title": "Main", "@sage/xtrem-stock/pages__stock_detailed_inquiry____navigationPanel__optionsMenu__title__2": "Including in-transit", "@sage/xtrem-stock/pages__stock_detailed_inquiry____title": "Stock detailed inquiry", "@sage/xtrem-stock/pages__stock_detailed_inquiry__allocatedQuantity____title": "Allocated quantity", "@sage/xtrem-stock/pages__stock_detailed_inquiry__allocationsLines____columns__title__documentLine__documentNumber": "Document number", "@sage/xtrem-stock/pages__stock_detailed_inquiry__allocationsLines____columns__title__quantityInStockUnit": "Allocated quantity", "@sage/xtrem-stock/pages__stock_detailed_inquiry__allocationsPanelSection____title": "Allocations", "@sage/xtrem-stock/pages__stock_detailed_inquiry__criteriaBlock____title": "Criteria", "@sage/xtrem-stock/pages__stock_detailed_inquiry__detailedStockSection____title": "Detailed stock", "@sage/xtrem-stock/pages__stock_detailed_inquiry__item____columns__title__category__name": "Category", "@sage/xtrem-stock/pages__stock_detailed_inquiry__item____lookupDialogTitle": "Select item", "@sage/xtrem-stock/pages__stock_detailed_inquiry__item____placeholder": "Select ...", "@sage/xtrem-stock/pages__stock_detailed_inquiry__item____title": "<PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_detailed_inquiry__lines____columns__columns__site__name__title": "Name", "@sage/xtrem-stock/pages__stock_detailed_inquiry__lines____columns__columns__site__name__title__2": "ID", "@sage/xtrem-stock/pages__stock_detailed_inquiry__lines____columns__columns__status__name__title": "Name", "@sage/xtrem-stock/pages__stock_detailed_inquiry__lines____columns__columns__status__name__title__2": "ID", "@sage/xtrem-stock/pages__stock_detailed_inquiry__lines____columns__columns__status__name__title__3": "Description", "@sage/xtrem-stock/pages__stock_detailed_inquiry__lines____columns__lookupDialogTitle__location__name": "Select location", "@sage/xtrem-stock/pages__stock_detailed_inquiry__lines____columns__placeholder__lot__id": "Select lot", "@sage/xtrem-stock/pages__stock_detailed_inquiry__lines____columns__title__allocatedQuantity": "Allocated quantity", "@sage/xtrem-stock/pages__stock_detailed_inquiry__lines____columns__title__availableQuantity": "Available quantity", "@sage/xtrem-stock/pages__stock_detailed_inquiry__lines____columns__title__endingSerialNumber": "Ending serial number", "@sage/xtrem-stock/pages__stock_detailed_inquiry__lines____columns__title__expirationDate": "Expiration date", "@sage/xtrem-stock/pages__stock_detailed_inquiry__lines____columns__title__location__name": "Location", "@sage/xtrem-stock/pages__stock_detailed_inquiry__lines____columns__title__lot__id": "Lot", "@sage/xtrem-stock/pages__stock_detailed_inquiry__lines____columns__title__onHandQuantity": "On-hand quantity", "@sage/xtrem-stock/pages__stock_detailed_inquiry__lines____columns__title__owner": "Owner", "@sage/xtrem-stock/pages__stock_detailed_inquiry__lines____columns__title__site__name": "Site", "@sage/xtrem-stock/pages__stock_detailed_inquiry__lines____columns__title__startingSerialNumber": "Starting serial number", "@sage/xtrem-stock/pages__stock_detailed_inquiry__lines____columns__title__status__name": "Quality control", "@sage/xtrem-stock/pages__stock_detailed_inquiry__lines____dropdownActions__title": "Lot information", "@sage/xtrem-stock/pages__stock_detailed_inquiry__lines____dropdownActions__title__2": "Serial number information", "@sage/xtrem-stock/pages__stock_detailed_inquiry__lines____dropdownActions__title__3": "Allocations", "@sage/xtrem-stock/pages__stock_detailed_inquiry__lines____title": "Detailed stock", "@sage/xtrem-stock/pages__stock_detailed_inquiry__lot____columns__title__id": "ID", "@sage/xtrem-stock/pages__stock_detailed_inquiry__lot____columns__title__item__name": "<PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_detailed_inquiry__lot____lookupDialogTitle": "Select lot", "@sage/xtrem-stock/pages__stock_detailed_inquiry__lot____placeholder": "Select ...", "@sage/xtrem-stock/pages__stock_detailed_inquiry__lot____title": "Lot", "@sage/xtrem-stock/pages__stock_detailed_inquiry__mainSection____title": "General", "@sage/xtrem-stock/pages__stock_detailed_inquiry__serialNumber____title": "Serial number", "@sage/xtrem-stock/pages__stock_detailed_inquiry__serialNumberLines____columns__columns__baseDocumentLine__documentNumber__title": "Document", "@sage/xtrem-stock/pages__stock_detailed_inquiry__serialNumberLines____columns__columns__baseDocumentLine__documentNumber__title__2": "Document", "@sage/xtrem-stock/pages__stock_detailed_inquiry__serialNumberLines____columns__columns__supplier__businessEntity__name__title": "Name", "@sage/xtrem-stock/pages__stock_detailed_inquiry__serialNumberLines____columns__columns__supplier__businessEntity__name__title__2": "ID", "@sage/xtrem-stock/pages__stock_detailed_inquiry__serialNumberLines____columns__title__baseDocumentLine__documentNumber": "Last document", "@sage/xtrem-stock/pages__stock_detailed_inquiry__serialNumberLines____columns__title__id": "Serial number", "@sage/xtrem-stock/pages__stock_detailed_inquiry__serialNumberLines____columns__title__isAllocated": "Allocated", "@sage/xtrem-stock/pages__stock_detailed_inquiry__serialNumberLines____columns__title__isInStock": "In stock", "@sage/xtrem-stock/pages__stock_detailed_inquiry__serialNumberLines____columns__title__supplier__businessEntity__name": "Supplier", "@sage/xtrem-stock/pages__stock_detailed_inquiry__serialNumberLines____title": "Serial numbers", "@sage/xtrem-stock/pages__stock_detailed_inquiry__sideLotPanelCreationDate____title": "Creation date", "@sage/xtrem-stock/pages__stock_detailed_inquiry__sideLotPanelExpirationDate____title": "Expiration date", "@sage/xtrem-stock/pages__stock_detailed_inquiry__sideLotPanelGeneralSection____title": "Lot information", "@sage/xtrem-stock/pages__stock_detailed_inquiry__sideLotPanelLotId____title": "Lot", "@sage/xtrem-stock/pages__stock_detailed_inquiry__sideLotPanelPotency____title": "Potency", "@sage/xtrem-stock/pages__stock_detailed_inquiry__sideLotPanelQuantity____title": "Quantity", "@sage/xtrem-stock/pages__stock_detailed_inquiry__sideLotPanelSublot____title": "Sublot", "@sage/xtrem-stock/pages__stock_detailed_inquiry__sideLotPanelSupplierLot____title": "Supplier lot", "@sage/xtrem-stock/pages__stock_detailed_inquiry__sideSerialNumberPanelBlock____title": "Serial number information ", "@sage/xtrem-stock/pages__stock_detailed_inquiry__site____columns__title__id": "ID", "@sage/xtrem-stock/pages__stock_detailed_inquiry__site____columns__title__isLocationManaged": "Location management", "@sage/xtrem-stock/pages__stock_detailed_inquiry__site____columns__title__name": "Name", "@sage/xtrem-stock/pages__stock_detailed_inquiry__site____lookupDialogTitle": "Select site", "@sage/xtrem-stock/pages__stock_detailed_inquiry__site____placeholder": "Select ...", "@sage/xtrem-stock/pages__stock_detailed_inquiry__site____title": "Site", "@sage/xtrem-stock/pages__stock_detailed_inquiry__status____columns__title__id": "ID", "@sage/xtrem-stock/pages__stock_detailed_inquiry__status____columns__title__name": "Name", "@sage/xtrem-stock/pages__stock_detailed_inquiry__status____lookupDialogTitle": "Select quality value", "@sage/xtrem-stock/pages__stock_detailed_inquiry__status____placeholder": "Select ...", "@sage/xtrem-stock/pages__stock_detailed_inquiry__status____title": "Quality control", "@sage/xtrem-stock/pages__stock_detailed_inquiry__totalAllocatedQuantity____title": "Allocated", "@sage/xtrem-stock/pages__stock_detailed_inquiry__totalAvailableQuantity____title": "Available", "@sage/xtrem-stock/pages__stock_detailed_inquiry__totalOnHandQuantity____title": "On hand", "@sage/xtrem-stock/pages__stock_fifo_valuation_inquiry____title": "FIFO cost tier", "@sage/xtrem-stock/pages__stock_fifo_valuation_inquiry__criteriaBlock____title": "Criteria", "@sage/xtrem-stock/pages__stock_fifo_valuation_inquiry__fifoValuationTiers____columns__title__amount": "Amount", "@sage/xtrem-stock/pages__stock_fifo_valuation_inquiry__fifoValuationTiers____columns__title__createDate": "Date created", "@sage/xtrem-stock/pages__stock_fifo_valuation_inquiry__fifoValuationTiers____columns__title__effectiveDate": "Receipt date", "@sage/xtrem-stock/pages__stock_fifo_valuation_inquiry__fifoValuationTiers____columns__title__nonAbsorbedAmount": "Non-absorbed amount", "@sage/xtrem-stock/pages__stock_fifo_valuation_inquiry__fifoValuationTiers____columns__title__receiptDocumentLine__documentNumber": "Receipt document", "@sage/xtrem-stock/pages__stock_fifo_valuation_inquiry__fifoValuationTiers____columns__title__receiptQuantity": "Original quantity", "@sage/xtrem-stock/pages__stock_fifo_valuation_inquiry__fifoValuationTiers____columns__title__remainingQuantity": "Quantity", "@sage/xtrem-stock/pages__stock_fifo_valuation_inquiry__fifoValuationTiers____columns__title__sequence": "Sequence", "@sage/xtrem-stock/pages__stock_fifo_valuation_inquiry__fifoValuationTiers____columns__title__unitCost": "Unit cost", "@sage/xtrem-stock/pages__stock_fifo_valuation_inquiry__fifoValuationTiers____title": "Results", "@sage/xtrem-stock/pages__stock_fifo_valuation_inquiry__fromDate____title": "From date", "@sage/xtrem-stock/pages__stock_fifo_valuation_inquiry__item____columns__title__category__name": "Category", "@sage/xtrem-stock/pages__stock_fifo_valuation_inquiry__item____lookupDialogTitle": "Select item", "@sage/xtrem-stock/pages__stock_fifo_valuation_inquiry__item____placeholder": "Select item", "@sage/xtrem-stock/pages__stock_fifo_valuation_inquiry__item____title": "<PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_fifo_valuation_inquiry__mainSection____title": "General", "@sage/xtrem-stock/pages__stock_fifo_valuation_inquiry__site____columns__title__id": "ID", "@sage/xtrem-stock/pages__stock_fifo_valuation_inquiry__site____columns__title__name": "Name", "@sage/xtrem-stock/pages__stock_fifo_valuation_inquiry__site____lookupDialogTitle": "Select site", "@sage/xtrem-stock/pages__stock_fifo_valuation_inquiry__site____placeholder": "Select site", "@sage/xtrem-stock/pages__stock_fifo_valuation_inquiry__site____title": "Site", "@sage/xtrem-stock/pages__stock_fifo_valuation_inquiry__toDate____title": "To date", "@sage/xtrem-stock/pages__Stock_has__allocated_quantity_the_counted_cannot_be_less_than_allocated_quantity": "Stock has allocations. The counted quantity cannot be less than the allocated quantity.", "@sage/xtrem-stock/pages__stock_inquiry____title": "Stock inquiry", "@sage/xtrem-stock/pages__stock_inquiry__criteriaBlock____title": "Criteria", "@sage/xtrem-stock/pages__stock_inquiry__item____columns__title__category__name": "Category", "@sage/xtrem-stock/pages__stock_inquiry__item____lookupDialogTitle": "Select item", "@sage/xtrem-stock/pages__stock_inquiry__item____title": "<PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_inquiry__itemFrom____columns__title__category__name": "Category", "@sage/xtrem-stock/pages__stock_inquiry__itemFrom____columns__title__description": "Description", "@sage/xtrem-stock/pages__stock_inquiry__itemFrom____columns__title__id": "<PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_inquiry__itemFrom____columns__title__name": "Name", "@sage/xtrem-stock/pages__stock_inquiry__itemFrom____lookupDialogTitle": "Select item", "@sage/xtrem-stock/pages__stock_inquiry__itemFrom____placeholder": "From items", "@sage/xtrem-stock/pages__stock_inquiry__itemFrom____title": "From item", "@sage/xtrem-stock/pages__stock_inquiry__itemTo____columns__title__category__name": "Category", "@sage/xtrem-stock/pages__stock_inquiry__itemTo____columns__title__description": "Description", "@sage/xtrem-stock/pages__stock_inquiry__itemTo____columns__title__id": "<PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_inquiry__itemTo____columns__title__name": "Name", "@sage/xtrem-stock/pages__stock_inquiry__itemTo____lookupDialogTitle": "Select item", "@sage/xtrem-stock/pages__stock_inquiry__itemTo____placeholder": "To items", "@sage/xtrem-stock/pages__stock_inquiry__itemTo____title": "To item", "@sage/xtrem-stock/pages__stock_inquiry__mainSection____title": "General", "@sage/xtrem-stock/pages__stock_inquiry__resultsSection____title": "Results", "@sage/xtrem-stock/pages__stock_inquiry__search": "Search", "@sage/xtrem-stock/pages__stock_inquiry__site____columns__title__id": "ID", "@sage/xtrem-stock/pages__stock_inquiry__site____columns__title__name": "Name", "@sage/xtrem-stock/pages__stock_inquiry__site____lookupDialogTitle": "Select site", "@sage/xtrem-stock/pages__stock_inquiry__site____placeholder": "Select ...", "@sage/xtrem-stock/pages__stock_inquiry__site____title": "Site", "@sage/xtrem-stock/pages__stock_inquiry__stock____columns__title__activeQuantityInStockUnit": "Quantity", "@sage/xtrem-stock/pages__stock_inquiry__stock____columns__title__item__id": "<PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_inquiry__stock____columns__title__item__name": "Name", "@sage/xtrem-stock/pages__stock_inquiry__stock____columns__title__site__name": "Site", "@sage/xtrem-stock/pages__stock_inquiry__stock____columns__title__stockUnit__name": "Unit of measure", "@sage/xtrem-stock/pages__stock_inquiry__stock____mobileCard__line2__title": "Name", "@sage/xtrem-stock/pages__stock_inquiry__stock____mobileCard__line2Right__title": "Unit of measure", "@sage/xtrem-stock/pages__stock_inquiry__stock____mobileCard__title__title": "Name", "@sage/xtrem-stock/pages__stock_inquiry__stock____mobileCard__titleRight__title": "Quantity", "@sage/xtrem-stock/pages__stock_inquiry__stock____title": "Results", "@sage/xtrem-stock/pages__stock_inquiry__stockStatus____columns__title__id": "ID", "@sage/xtrem-stock/pages__stock_inquiry__stockStatus____columns__title__name": "Name", "@sage/xtrem-stock/pages__stock_inquiry__stockStatus____lookupDialogTitle": "Select status", "@sage/xtrem-stock/pages__stock_inquiry__stockStatus____placeholder": "Select ...", "@sage/xtrem-stock/pages__stock_inquiry__stockStatus____title": "Stock status", "@sage/xtrem-stock/pages__stock_inquiry_bound____navigationPanel__listItem__accepted__title": "Accepted quantity", "@sage/xtrem-stock/pages__stock_inquiry_bound____navigationPanel__listItem__allocated__title": "Allocated quantity", "@sage/xtrem-stock/pages__stock_inquiry_bound____navigationPanel__listItem__image__title": "Image", "@sage/xtrem-stock/pages__stock_inquiry_bound____navigationPanel__listItem__inStock__title": "Quantity on hand", "@sage/xtrem-stock/pages__stock_inquiry_bound____navigationPanel__listItem__itemCategory__title": "Category", "@sage/xtrem-stock/pages__stock_inquiry_bound____navigationPanel__listItem__itemDescription__title": "Item description", "@sage/xtrem-stock/pages__stock_inquiry_bound____navigationPanel__listItem__itemIdReference__title": "Item ID", "@sage/xtrem-stock/pages__stock_inquiry_bound____navigationPanel__listItem__itemName__title": "<PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_inquiry_bound____navigationPanel__listItem__qualityQuntity__title": "Quantity in quality control", "@sage/xtrem-stock/pages__stock_inquiry_bound____navigationPanel__listItem__rejected__title": "Rejected quantity", "@sage/xtrem-stock/pages__stock_inquiry_bound____navigationPanel__listItem__site__title": "Site", "@sage/xtrem-stock/pages__stock_inquiry_bound____navigationPanel__listItem__stockValue__title": "Stock value", "@sage/xtrem-stock/pages__stock_inquiry_bound____navigationPanel__listItem__title__title": "Name", "@sage/xtrem-stock/pages__stock_inquiry_bound____title": "Stock inquiry", "@sage/xtrem-stock/pages__stock_issue____navigationPanel__dropdownActions__title": "Set dimensions", "@sage/xtrem-stock/pages__stock_issue____navigationPanel__dropdownActions__title__2": "Delete", "@sage/xtrem-stock/pages__stock_issue____navigationPanel__listItem__description__title": "Description", "@sage/xtrem-stock/pages__stock_issue____navigationPanel__listItem__effectiveDate__title": "Date", "@sage/xtrem-stock/pages__stock_issue____navigationPanel__listItem__line2__title": "Site", "@sage/xtrem-stock/pages__stock_issue____navigationPanel__listItem__line2Right__title": "Date", "@sage/xtrem-stock/pages__stock_issue____navigationPanel__listItem__reasonCode__title": "Reason", "@sage/xtrem-stock/pages__stock_issue____navigationPanel__listItem__status__title": "Status", "@sage/xtrem-stock/pages__stock_issue____navigationPanel__listItem__stockSite__title": "Site", "@sage/xtrem-stock/pages__stock_issue____navigationPanel__listItem__stockTransactionStatus__title": "Stock status", "@sage/xtrem-stock/pages__stock_issue____navigationPanel__listItem__title__title": "Number", "@sage/xtrem-stock/pages__stock_issue____navigationPanel__listItem__titleRight__title": "Status", "@sage/xtrem-stock/pages__stock_issue____navigationPanel__optionsMenu__title": "All open statuses", "@sage/xtrem-stock/pages__stock_issue____navigationPanel__optionsMenu__title__2": "All statuses", "@sage/xtrem-stock/pages__stock_issue____navigationPanel__optionsMenu__title__3": "Details required", "@sage/xtrem-stock/pages__stock_issue____navigationPanel__optionsMenu__title__4": "Details entered", "@sage/xtrem-stock/pages__stock_issue____navigationPanel__optionsMenu__title__5": "Stock posting in progress", "@sage/xtrem-stock/pages__stock_issue____navigationPanel__optionsMenu__title__6": "Stock posting error", "@sage/xtrem-stock/pages__stock_issue____navigationPanel__optionsMenu__title__7": "Issued", "@sage/xtrem-stock/pages__stock_issue____objectTypePlural": "Stock issues", "@sage/xtrem-stock/pages__stock_issue____objectTypeSingular": "Stock issue", "@sage/xtrem-stock/pages__stock_issue____title": "Stock issue", "@sage/xtrem-stock/pages__stock_issue__addIssueLine____title": "Add line", "@sage/xtrem-stock/pages__stock_issue__apply_dimensions_success": "Dimensions applied.", "@sage/xtrem-stock/pages__stock_issue__defaultDimension____title": "Set dimensions", "@sage/xtrem-stock/pages__stock_issue__description____title": "Description", "@sage/xtrem-stock/pages__stock_issue__displayStatus____title": "Display status", "@sage/xtrem-stock/pages__stock_issue__effectiveDate____title": "Date", "@sage/xtrem-stock/pages__stock_issue__goToSysNotificationPage____title": "Retry", "@sage/xtrem-stock/pages__stock_issue__issue_step_sequence_creation": "Create", "@sage/xtrem-stock/pages__stock_issue__issue_step_sequence_detail_stock": "Detail stock", "@sage/xtrem-stock/pages__stock_issue__issue_step_sequence_post_stock": "Post stock", "@sage/xtrem-stock/pages__stock_issue__line_delete_action_dialog_content": "You are about to delete this stock issue line.", "@sage/xtrem-stock/pages__stock_issue__line_delete_action_dialog_title": "Confirm delete", "@sage/xtrem-stock/pages__stock_issue__lines____columns__columns__item__name__title": "Name", "@sage/xtrem-stock/pages__stock_issue__lines____columns__columns__item__name__title__2": "ID", "@sage/xtrem-stock/pages__stock_issue__lines____columns__columns__item__name__title__3": "Category", "@sage/xtrem-stock/pages__stock_issue__lines____columns__lookupDialogTitle__item__name": "Select item", "@sage/xtrem-stock/pages__stock_issue__lines____columns__title__displayStatus": "Status", "@sage/xtrem-stock/pages__stock_issue__lines____columns__title__item__description": "Item description", "@sage/xtrem-stock/pages__stock_issue__lines____columns__title__item__id": "Item ID", "@sage/xtrem-stock/pages__stock_issue__lines____columns__title__item__name": "<PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_issue__lines____columns__title__orderCost": "Actual cost", "@sage/xtrem-stock/pages__stock_issue__lines____columns__title__quantityInStockUnit": "Quantity", "@sage/xtrem-stock/pages__stock_issue__lines____columns__title__stockDetailStatus": "Stock detail status", "@sage/xtrem-stock/pages__stock_issue__lines____columns__title__stockTransactionStatus": "Stock status", "@sage/xtrem-stock/pages__stock_issue__lines____columns__title__valuedCost": "Expected cost", "@sage/xtrem-stock/pages__stock_issue__lines____dropdownActions__title": "Stock details", "@sage/xtrem-stock/pages__stock_issue__lines____dropdownActions__title__2": "Dimensions", "@sage/xtrem-stock/pages__stock_issue__lines____dropdownActions__title__3": "Delete", "@sage/xtrem-stock/pages__stock_issue__lines____inlineActions__title": "Open line panel", "@sage/xtrem-stock/pages__stock_issue__lines____mobileCard__line2__title": "Description", "@sage/xtrem-stock/pages__stock_issue__lines____mobileCard__line2Right__title": "Price", "@sage/xtrem-stock/pages__stock_issue__lines____mobileCard__line3Right__title": "Quantity", "@sage/xtrem-stock/pages__stock_issue__lines____mobileCard__title__title": "Product", "@sage/xtrem-stock/pages__stock_issue__lines____mobileCard__titleRight__title": "Status", "@sage/xtrem-stock/pages__stock_issue__lines____optionsMenu__title": "All statuses", "@sage/xtrem-stock/pages__stock_issue__lines____optionsMenu__title__2": "All open statuses", "@sage/xtrem-stock/pages__stock_issue__lines____optionsMenu__title__3": "Details required", "@sage/xtrem-stock/pages__stock_issue__lines____optionsMenu__title__4": "Details entered", "@sage/xtrem-stock/pages__stock_issue__lines____optionsMenu__title__5": "Stock posting in progress", "@sage/xtrem-stock/pages__stock_issue__lines____optionsMenu__title__6": "Stock posting error", "@sage/xtrem-stock/pages__stock_issue__lines____optionsMenu__title__7": "Issued", "@sage/xtrem-stock/pages__stock_issue__lines____sidebar__headerDropdownActions__title": "Stock details", "@sage/xtrem-stock/pages__stock_issue__lines____sidebar__headerDropdownActions__title__2": "Dimensions", "@sage/xtrem-stock/pages__stock_issue__lines____sidebar__headerDropdownActions__title__3": "Delete", "@sage/xtrem-stock/pages__stock_issue__lines____title": "Lines", "@sage/xtrem-stock/pages__stock_issue__linesSection____title": "Lines", "@sage/xtrem-stock/pages__stock_issue__mainSection____title": "General", "@sage/xtrem-stock/pages__stock_issue__notification__The_quantity_is_mandatory": "Enter the quantity.", "@sage/xtrem-stock/pages__stock_issue__number____title": "Number", "@sage/xtrem-stock/pages__stock_issue__post____title": "Post stock", "@sage/xtrem-stock/pages__stock_issue__postingDetails____columns__title__financeIntegrationAppRecordId": "Accounting integration reference", "@sage/xtrem-stock/pages__stock_issue__postingDetails____columns__title__postingStatus": "Status", "@sage/xtrem-stock/pages__stock_issue__postingDetails____columns__title__targetDocumentNumber": "Document number", "@sage/xtrem-stock/pages__stock_issue__postingDetails____columns__title__targetDocumentType": "Document type", "@sage/xtrem-stock/pages__stock_issue__postingDetails____title": "Results", "@sage/xtrem-stock/pages__stock_issue__postingMessageBlock____title": "Error details", "@sage/xtrem-stock/pages__stock_issue__postingSection____title": "Posting", "@sage/xtrem-stock/pages__stock_issue__reasonCode____lookupDialogTitle": "Select reason", "@sage/xtrem-stock/pages__stock_issue__reasonCode____title": "Reason", "@sage/xtrem-stock/pages__stock_issue__repost____title": "Repost", "@sage/xtrem-stock/pages__stock_issue__repost_errors": "Errors while reposting:", "@sage/xtrem-stock/pages__stock_issue__saveStockIssue____title": "Save", "@sage/xtrem-stock/pages__stock_issue__stock_journal_inquiry": "Stock journal inquiry", "@sage/xtrem-stock/pages__stock_issue__stock_posting_error": "Stock posting error", "@sage/xtrem-stock/pages__stock_issue__stockSite____columns__title__id": "ID", "@sage/xtrem-stock/pages__stock_issue__stockSite____columns__title__name": "Name", "@sage/xtrem-stock/pages__stock_issue__stockSite____lookupDialogTitle": "Select site", "@sage/xtrem-stock/pages__stock_issue__stockSite____placeholder": "Select ...", "@sage/xtrem-stock/pages__stock_issue__stockSite____title": "Site", "@sage/xtrem-stock/pages__stock_issue__stockTransactionStatus____title": "Stock status", "@sage/xtrem-stock/pages__stock_issue_post__stock_details_required": "You need to enter stock details for all lines before you can post.", "@sage/xtrem-stock/pages__stock_journal_inquiry____navigationPanel__dropdownActions__title": "Journal entry", "@sage/xtrem-stock/pages__stock_journal_inquiry____navigationPanel__inlineActions__title": "Journal entry", "@sage/xtrem-stock/pages__stock_journal_inquiry____navigationPanel__listItem__amountVariance__title": "Amount variance", "@sage/xtrem-stock/pages__stock_journal_inquiry____navigationPanel__listItem__costVariance__title": "Cost variance", "@sage/xtrem-stock/pages__stock_journal_inquiry____navigationPanel__listItem__currency__title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_journal_inquiry____navigationPanel__listItem__currencyId__title": "Currency ID", "@sage/xtrem-stock/pages__stock_journal_inquiry____navigationPanel__listItem__documentLineType__title": "Document line type", "@sage/xtrem-stock/pages__stock_journal_inquiry____navigationPanel__listItem__documentLink__title": "Document", "@sage/xtrem-stock/pages__stock_journal_inquiry____navigationPanel__listItem__effectiveDate__title": "Date", "@sage/xtrem-stock/pages__stock_journal_inquiry____navigationPanel__listItem__expirationDate__title": "Expiration date", "@sage/xtrem-stock/pages__stock_journal_inquiry____navigationPanel__listItem__itemDescription__title": "Item description", "@sage/xtrem-stock/pages__stock_journal_inquiry____navigationPanel__listItem__itemId__title": "Item ID", "@sage/xtrem-stock/pages__stock_journal_inquiry____navigationPanel__listItem__itemName__title": "<PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_journal_inquiry____navigationPanel__listItem__locationId__title": "Location ID", "@sage/xtrem-stock/pages__stock_journal_inquiry____navigationPanel__listItem__locationName__title": "Location", "@sage/xtrem-stock/pages__stock_journal_inquiry____navigationPanel__listItem__locationZone__title": "Location zone", "@sage/xtrem-stock/pages__stock_journal_inquiry____navigationPanel__listItem__lot__title": "Lot", "@sage/xtrem-stock/pages__stock_journal_inquiry____navigationPanel__listItem__movementAmount__title": "Movement amount", "@sage/xtrem-stock/pages__stock_journal_inquiry____navigationPanel__listItem__movementType__title": "Movement type", "@sage/xtrem-stock/pages__stock_journal_inquiry____navigationPanel__listItem__nonAbsorbedAmount__title": "Non-absorbed amount", "@sage/xtrem-stock/pages__stock_journal_inquiry____navigationPanel__listItem__orderAmount__title": "Order amount", "@sage/xtrem-stock/pages__stock_journal_inquiry____navigationPanel__listItem__orderCost__title": "Order cost", "@sage/xtrem-stock/pages__stock_journal_inquiry____navigationPanel__listItem__quantityInStockUnit__title": "Quantity", "@sage/xtrem-stock/pages__stock_journal_inquiry____navigationPanel__listItem__reasonCode__title": "Reason code", "@sage/xtrem-stock/pages__stock_journal_inquiry____navigationPanel__listItem__siteId__title": "Site ID", "@sage/xtrem-stock/pages__stock_journal_inquiry____navigationPanel__listItem__stockStatus__title": "Quality control", "@sage/xtrem-stock/pages__stock_journal_inquiry____navigationPanel__listItem__stockUnit__title": "Unit of measure", "@sage/xtrem-stock/pages__stock_journal_inquiry____navigationPanel__listItem__sublot__title": "Sublot", "@sage/xtrem-stock/pages__stock_journal_inquiry____navigationPanel__listItem__supplierLot__title": "Supplier lot number", "@sage/xtrem-stock/pages__stock_journal_inquiry____navigationPanel__listItem__valuedCost__title": "Valued cost", "@sage/xtrem-stock/pages__stock_journal_inquiry____title": "Stock journal inquiry", "@sage/xtrem-stock/pages__stock_journal_inquiry__no_journal_entry_found": "No journal entry found for the selected document.", "@sage/xtrem-stock/pages__stock_mrp_calculation__calculate": "Calculate", "@sage/xtrem-stock/pages__stock_mrp_calculation_company_sites_error": "Enter at least one company or site.", "@sage/xtrem-stock/pages__stock_receipt____navigationPanel__dropdownActions__title": "Set dimensions", "@sage/xtrem-stock/pages__stock_receipt____navigationPanel__dropdownActions__title__2": "Delete", "@sage/xtrem-stock/pages__stock_receipt____navigationPanel__listItem__description__title": "Description", "@sage/xtrem-stock/pages__stock_receipt____navigationPanel__listItem__line2__title": "Site", "@sage/xtrem-stock/pages__stock_receipt____navigationPanel__listItem__line2Right__title": "Date", "@sage/xtrem-stock/pages__stock_receipt____navigationPanel__listItem__reasonCode__title": "Reason", "@sage/xtrem-stock/pages__stock_receipt____navigationPanel__listItem__status__title": "Status", "@sage/xtrem-stock/pages__stock_receipt____navigationPanel__listItem__title__title": "Number", "@sage/xtrem-stock/pages__stock_receipt____navigationPanel__listItem__titleRight__title": "Status", "@sage/xtrem-stock/pages__stock_receipt____navigationPanel__optionsMenu__title": "All open statuses", "@sage/xtrem-stock/pages__stock_receipt____navigationPanel__optionsMenu__title__2": "All statuses", "@sage/xtrem-stock/pages__stock_receipt____navigationPanel__optionsMenu__title__3": "Details required", "@sage/xtrem-stock/pages__stock_receipt____navigationPanel__optionsMenu__title__4": "Details entered", "@sage/xtrem-stock/pages__stock_receipt____navigationPanel__optionsMenu__title__5": "Stock posting in progress", "@sage/xtrem-stock/pages__stock_receipt____navigationPanel__optionsMenu__title__6": "Stock posting error", "@sage/xtrem-stock/pages__stock_receipt____navigationPanel__optionsMenu__title__7": "Received", "@sage/xtrem-stock/pages__stock_receipt____objectTypePlural": "Stock receipts", "@sage/xtrem-stock/pages__stock_receipt____objectTypeSingular": "Stock receipt", "@sage/xtrem-stock/pages__stock_receipt____title": "Stock receipt", "@sage/xtrem-stock/pages__stock_receipt__actual_cost_cannot_be_negative": "You need to enter a value greater than or equal to 0.", "@sage/xtrem-stock/pages__stock_receipt__apply_dimensions_success": "Dimensions applied.", "@sage/xtrem-stock/pages__stock_receipt__defaultDimension____title": "Set dimensions", "@sage/xtrem-stock/pages__stock_receipt__description____title": "Description", "@sage/xtrem-stock/pages__stock_receipt__displayStatus____title": "Display status", "@sage/xtrem-stock/pages__stock_receipt__effectiveDate____title": "Date", "@sage/xtrem-stock/pages__stock_receipt__goToSysNotificationPage____title": "Retry", "@sage/xtrem-stock/pages__stock_receipt__line_delete_action_dialog_content": "You are about to delete this stock receipt line.", "@sage/xtrem-stock/pages__stock_receipt__line_delete_action_dialog_title": "Confirm delete", "@sage/xtrem-stock/pages__stock_receipt__lines____columns__columns__existingLot__id__columns__title__id": "ID", "@sage/xtrem-stock/pages__stock_receipt__lines____columns__columns__existingLot__id__columns__title__name": "Name", "@sage/xtrem-stock/pages__stock_receipt__lines____columns__columns__existingLot__id__title": "ID", "@sage/xtrem-stock/pages__stock_receipt__lines____columns__columns__existingLot__id__title__2": "Expiration date", "@sage/xtrem-stock/pages__stock_receipt__lines____columns__columns__existingLot__id__title__3": "<PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_receipt__lines____columns__columns__item__name__title": "Name", "@sage/xtrem-stock/pages__stock_receipt__lines____columns__columns__item__name__title__2": "ID", "@sage/xtrem-stock/pages__stock_receipt__lines____columns__columns__item__name__title__3": "Category", "@sage/xtrem-stock/pages__stock_receipt__lines____columns__columns__location__name__title": "Name", "@sage/xtrem-stock/pages__stock_receipt__lines____columns__columns__location__name__title__2": "ID", "@sage/xtrem-stock/pages__stock_receipt__lines____columns__lookupDialogTitle__existingLot__id": "Select lot", "@sage/xtrem-stock/pages__stock_receipt__lines____columns__lookupDialogTitle__item__name": "Select item", "@sage/xtrem-stock/pages__stock_receipt__lines____columns__lookupDialogTitle__location__name": "Select location", "@sage/xtrem-stock/pages__stock_receipt__lines____columns__lookupDialogTitle__stockStatus": "Select quality value", "@sage/xtrem-stock/pages__stock_receipt__lines____columns__placeholder__existingLot__id": "Select ...", "@sage/xtrem-stock/pages__stock_receipt__lines____columns__placeholder__item__name": "Select item", "@sage/xtrem-stock/pages__stock_receipt__lines____columns__title__displayStatus": "Status", "@sage/xtrem-stock/pages__stock_receipt__lines____columns__title__existingLot__id": "Lot", "@sage/xtrem-stock/pages__stock_receipt__lines____columns__title__expirationDate": "Expiration date", "@sage/xtrem-stock/pages__stock_receipt__lines____columns__title__item__description": "Item description", "@sage/xtrem-stock/pages__stock_receipt__lines____columns__title__item__id": "Item ID", "@sage/xtrem-stock/pages__stock_receipt__lines____columns__title__item__name": "<PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_receipt__lines____columns__title__itemSite": "Item site", "@sage/xtrem-stock/pages__stock_receipt__lines____columns__title__location__name": "Location", "@sage/xtrem-stock/pages__stock_receipt__lines____columns__title__lotNumber": "Lot number", "@sage/xtrem-stock/pages__stock_receipt__lines____columns__title__orderCost": "Actual cost", "@sage/xtrem-stock/pages__stock_receipt__lines____columns__title__quantityInStockUnit": "Quantity", "@sage/xtrem-stock/pages__stock_receipt__lines____columns__title__stockStatus": "Quality control", "@sage/xtrem-stock/pages__stock_receipt__lines____columns__title__valuedCost": "Expected cost", "@sage/xtrem-stock/pages__stock_receipt__lines____dropdownActions__title": "Stock details", "@sage/xtrem-stock/pages__stock_receipt__lines____dropdownActions__title__2": "Dimensions", "@sage/xtrem-stock/pages__stock_receipt__lines____dropdownActions__title__3": "Delete", "@sage/xtrem-stock/pages__stock_receipt__lines____inlineActions__title": "Open line panel", "@sage/xtrem-stock/pages__stock_receipt__lines____mobileCard__line2__title": "Description", "@sage/xtrem-stock/pages__stock_receipt__lines____mobileCard__line2Right__title": "Price", "@sage/xtrem-stock/pages__stock_receipt__lines____mobileCard__line3Right__title": "Quantity", "@sage/xtrem-stock/pages__stock_receipt__lines____mobileCard__title__title": "Product", "@sage/xtrem-stock/pages__stock_receipt__lines____mobileCard__titleRight__title": "Status", "@sage/xtrem-stock/pages__stock_receipt__lines____optionsMenu__title": "All statuses", "@sage/xtrem-stock/pages__stock_receipt__lines____optionsMenu__title__2": "All open statuses", "@sage/xtrem-stock/pages__stock_receipt__lines____optionsMenu__title__3": "Details required", "@sage/xtrem-stock/pages__stock_receipt__lines____optionsMenu__title__4": "Details entered", "@sage/xtrem-stock/pages__stock_receipt__lines____optionsMenu__title__5": "Stock posting in progress", "@sage/xtrem-stock/pages__stock_receipt__lines____optionsMenu__title__6": "Stock posting error", "@sage/xtrem-stock/pages__stock_receipt__lines____optionsMenu__title__7": "Received", "@sage/xtrem-stock/pages__stock_receipt__lines____sidebar__headerDropdownActions__title": "Stock details", "@sage/xtrem-stock/pages__stock_receipt__lines____sidebar__headerDropdownActions__title__2": "Dimensions", "@sage/xtrem-stock/pages__stock_receipt__lines____sidebar__headerDropdownActions__title__3": "Delete", "@sage/xtrem-stock/pages__stock_receipt__lines____title": "Lines", "@sage/xtrem-stock/pages__stock_receipt__linesSection____title": "Lines", "@sage/xtrem-stock/pages__stock_receipt__notification__expiration_date_is_mandatory": "Enter the expiration date.", "@sage/xtrem-stock/pages__stock_receipt__number____title": "Number", "@sage/xtrem-stock/pages__stock_receipt__post____title": "Post stock", "@sage/xtrem-stock/pages__stock_receipt__postingDetails____columns__title__financeIntegrationAppRecordId": "Accounting integration reference", "@sage/xtrem-stock/pages__stock_receipt__postingDetails____columns__title__postingStatus": "Status", "@sage/xtrem-stock/pages__stock_receipt__postingDetails____columns__title__targetDocumentNumber": "Document number", "@sage/xtrem-stock/pages__stock_receipt__postingDetails____columns__title__targetDocumentType": "Document type", "@sage/xtrem-stock/pages__stock_receipt__postingDetails____title": "Results", "@sage/xtrem-stock/pages__stock_receipt__postingMessageBlock____title": "Error details", "@sage/xtrem-stock/pages__stock_receipt__postingSection____title": "Posting", "@sage/xtrem-stock/pages__stock_receipt__quantity_must_be_positive": "You need to enter a value greater than 0.", "@sage/xtrem-stock/pages__stock_receipt__reasonCode____lookupDialogTitle": "Select reason", "@sage/xtrem-stock/pages__stock_receipt__reasonCode____placeholder": "Select reason", "@sage/xtrem-stock/pages__stock_receipt__reasonCode____title": "Reason", "@sage/xtrem-stock/pages__stock_receipt__receipt_step_sequence_creation": "Create", "@sage/xtrem-stock/pages__stock_receipt__receipt_step_sequence_detail_stock": "Detail stock", "@sage/xtrem-stock/pages__stock_receipt__receipt_step_sequence_post_stock": "Post stock", "@sage/xtrem-stock/pages__stock_receipt__repost____title": "Repost", "@sage/xtrem-stock/pages__stock_receipt__repost_errors": "Errors while reposting:", "@sage/xtrem-stock/pages__stock_receipt__saveStockReceipt____title": "Save", "@sage/xtrem-stock/pages__stock_receipt__serial number_exists_on_another_line": "Serial number {{serialNumberId}} for item {{itemId}} already exists on another document line.", "@sage/xtrem-stock/pages__stock_receipt__stock_journal_inquiry": "Stock journal inquiry", "@sage/xtrem-stock/pages__stock_receipt__stock_posting_error": "Stock posting error", "@sage/xtrem-stock/pages__stock_receipt__stockSite____columns__columns__legalCompany__name__columns__title__symbol": "Symbol", "@sage/xtrem-stock/pages__stock_receipt__stockSite____columns__title__id": "ID", "@sage/xtrem-stock/pages__stock_receipt__stockSite____columns__title__name": "Name", "@sage/xtrem-stock/pages__stock_receipt__stockSite____lookupDialogTitle": "Select site", "@sage/xtrem-stock/pages__stock_receipt__stockSite____placeholder": "Select ...", "@sage/xtrem-stock/pages__stock_receipt__stockSite____title": "Site", "@sage/xtrem-stock/pages__stock_receipt__stockTransactionStatus____title": "Stock status", "@sage/xtrem-stock/pages__stock_receipt_detail_panel__notification__The_lot_is_mandatory": "The lot is mandatory.", "@sage/xtrem-stock/pages__stock_receipt_detail_panel__notification__The_status_is_mandatory": "The status is mandatory.", "@sage/xtrem-stock/pages__stock_receipt_detail_panel__notification__The_sublot_is_mandatory": "The sublot is mandatory.", "@sage/xtrem-stock/pages__stock_receipt_details_panel__notification__expiration_date_is_mandatory": "The expiration date is mandatory.", "@sage/xtrem-stock/pages__stock_receipt_post__stock_details_required": "You need to enter stock details for all lines before you can post.", "@sage/xtrem-stock/pages__stock_reorder_calculation____title": "Stock reordering", "@sage/xtrem-stock/pages__stock_reorder_calculation__company____columns__title__id": "ID", "@sage/xtrem-stock/pages__stock_reorder_calculation__company____columns__title__name": "Name", "@sage/xtrem-stock/pages__stock_reorder_calculation__company____lookupDialogTitle": "Select company", "@sage/xtrem-stock/pages__stock_reorder_calculation__company____placeholder": "Select ...", "@sage/xtrem-stock/pages__stock_reorder_calculation__company____title": "Company", "@sage/xtrem-stock/pages__stock_reorder_calculation__criteriaBlock____title": "Criteria", "@sage/xtrem-stock/pages__stock_reorder_calculation__endDate____title": "End date", "@sage/xtrem-stock/pages__stock_reorder_calculation__fromItem____columns__title__category__name": "Category", "@sage/xtrem-stock/pages__stock_reorder_calculation__fromItem____lookupDialogTitle": "Select item", "@sage/xtrem-stock/pages__stock_reorder_calculation__fromItem____placeholder": "Select...", "@sage/xtrem-stock/pages__stock_reorder_calculation__fromItem____title": "From item", "@sage/xtrem-stock/pages__stock_reorder_calculation__lines____columns__title__company__name": "Company", "@sage/xtrem-stock/pages__stock_reorder_calculation__lines____columns__title__itemSite__item__description": "Item description", "@sage/xtrem-stock/pages__stock_reorder_calculation__lines____columns__title__itemSite__item__id": "Item ID", "@sage/xtrem-stock/pages__stock_reorder_calculation__lines____columns__title__itemSite__item__name": "<PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_reorder_calculation__lines____columns__title__itemSite__site__id": "Site", "@sage/xtrem-stock/pages__stock_reorder_calculation__lines____columns__title__orderDate": "Order date", "@sage/xtrem-stock/pages__stock_reorder_calculation__lines____columns__title__quantity": "Suggested quantity", "@sage/xtrem-stock/pages__stock_reorder_calculation__lines____columns__title__reorderType": "Process method", "@sage/xtrem-stock/pages__stock_reorder_calculation__lines____columns__title__supplier__businessEntity__id": "Supplier ID", "@sage/xtrem-stock/pages__stock_reorder_calculation__lines____columns__title__supplier__businessEntity__name": "Supplier name", "@sage/xtrem-stock/pages__stock_reorder_calculation__lines____dropdownActions__title": "Create purchase order", "@sage/xtrem-stock/pages__stock_reorder_calculation__lines____dropdownActions__title__2": "Create work order", "@sage/xtrem-stock/pages__stock_reorder_calculation__mainSection____title": "General", "@sage/xtrem-stock/pages__stock_reorder_calculation__purchase_order_creation__success": "Purchase order {{num}} created", "@sage/xtrem-stock/pages__stock_reorder_calculation__reorderType____title": "Reorder type", "@sage/xtrem-stock/pages__stock_reorder_calculation__runStockReorderCalculation____title": "Run", "@sage/xtrem-stock/pages__stock_reorder_calculation__sites____columns__title__id": "ID", "@sage/xtrem-stock/pages__stock_reorder_calculation__sites____columns__title__name": "Name", "@sage/xtrem-stock/pages__stock_reorder_calculation__sites____lookupDialogTitle": "Select site", "@sage/xtrem-stock/pages__stock_reorder_calculation__sites____placeholder": "Select ...", "@sage/xtrem-stock/pages__stock_reorder_calculation__sites____title": "Site", "@sage/xtrem-stock/pages__stock_reorder_calculation__status____title": "Status", "@sage/xtrem-stock/pages__stock_reorder_calculation__toItem____columns__title__category__name": "Category", "@sage/xtrem-stock/pages__stock_reorder_calculation__toItem____columns__title__description": "Description", "@sage/xtrem-stock/pages__stock_reorder_calculation__toItem____columns__title__id": "ID", "@sage/xtrem-stock/pages__stock_reorder_calculation__toItem____columns__title__name": "Name", "@sage/xtrem-stock/pages__stock_reorder_calculation__toItem____lookupDialogTitle": "Select item", "@sage/xtrem-stock/pages__stock_reorder_calculation__toItem____placeholder": "Select...", "@sage/xtrem-stock/pages__stock_reorder_calculation__toItem____title": "To item", "@sage/xtrem-stock/pages__stock_reorder_calculation__user____title": "Calculation user", "@sage/xtrem-stock/pages__stock_reorder_calculation__work_order_creation__success": "Work order {{num}} created", "@sage/xtrem-stock/pages__stock_reorder_calculation_new__fromItem____columns__title__description": "Description", "@sage/xtrem-stock/pages__stock_reorder_calculation_new__fromItem____columns__title__id": "ID", "@sage/xtrem-stock/pages__stock_reorder_calculation_new__fromItem____columns__title__name": "Name", "@sage/xtrem-stock/pages__stock_reorder_calculation_new__user____columns__title__email": "Email", "@sage/xtrem-stock/pages__stock_reorder_calculation_new__user____columns__title__firstName": "First name", "@sage/xtrem-stock/pages__stock_reorder_calculation_new__user____columns__title__lastName": "Last name", "@sage/xtrem-stock/pages__stock_reorder_calculation_new__user____lookupDialogTitle": "Select user", "@sage/xtrem-stock/pages__stock_reorder_calculation_request__notification_calculation_failed": "Stock reorder calculation failed.", "@sage/xtrem-stock/pages__stock_reorder_calculation_request__notification_calculation_finished": "Stock reorder calculation finished.", "@sage/xtrem-stock/pages__stock_reorder_calculation_request__notification_request_sent": "Stock reorder calculation request sent.", "@sage/xtrem-stock/pages__stock_valuation____objectTypeSingular": "Stock valuation", "@sage/xtrem-stock/pages__stock_valuation____title": "Stock valuation", "@sage/xtrem-stock/pages__stock_valuation__commodityCode____title": "Commodity code", "@sage/xtrem-stock/pages__stock_valuation__company____columns__title__id": "ID", "@sage/xtrem-stock/pages__stock_valuation__company____columns__title__name": "Name", "@sage/xtrem-stock/pages__stock_valuation__company____lookupDialogTitle": "Select company", "@sage/xtrem-stock/pages__stock_valuation__company____placeholder": "Select ...", "@sage/xtrem-stock/pages__stock_valuation__company____title": "Company", "@sage/xtrem-stock/pages__stock_valuation__confirm_today_date": "You are about to set the last valuation report date to today.", "@sage/xtrem-stock/pages__stock_valuation__confirm_today_date_title": "Confirm recording for today", "@sage/xtrem-stock/pages__stock_valuation__confirmation____title": "Record valuation", "@sage/xtrem-stock/pages__stock_valuation__criteriaBlock____title": "Criteria", "@sage/xtrem-stock/pages__stock_valuation__date____title": "Date", "@sage/xtrem-stock/pages__stock_valuation__displayZeroValues____title": "Display zero values", "@sage/xtrem-stock/pages__stock_valuation__fromItem____columns__title__category__name": "Category", "@sage/xtrem-stock/pages__stock_valuation__fromItem____lookupDialogTitle": "Select item", "@sage/xtrem-stock/pages__stock_valuation__fromItem____placeholder": "Select...", "@sage/xtrem-stock/pages__stock_valuation__fromItem____title": "From item", "@sage/xtrem-stock/pages__stock_valuation__itemCategory____columns__title__type": "Type", "@sage/xtrem-stock/pages__stock_valuation__itemCategory____lookupDialogTitle": "Select item category", "@sage/xtrem-stock/pages__stock_valuation__itemCategory____title": "Item category", "@sage/xtrem-stock/pages__stock_valuation__itemFrom____columns__title__description": "Description", "@sage/xtrem-stock/pages__stock_valuation__itemFrom____columns__title__id": "ID", "@sage/xtrem-stock/pages__stock_valuation__itemFrom____columns__title__name": "Name", "@sage/xtrem-stock/pages__stock_valuation__itemFrom____lookupDialogTitle": "Select item", "@sage/xtrem-stock/pages__stock_valuation__itemFrom____placeholder": "Select ...", "@sage/xtrem-stock/pages__stock_valuation__itemFrom____title": "From item", "@sage/xtrem-stock/pages__stock_valuation__itemSelection____columns__title__item__id": "<PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_valuation__itemSelection____columns__title__item__name": "Name", "@sage/xtrem-stock/pages__stock_valuation__itemSelection____columns__title__site__name": "Site", "@sage/xtrem-stock/pages__stock_valuation__itemSelection____columns__title__stockUnit__name": "Unit of measure", "@sage/xtrem-stock/pages__stock_valuation__itemSelection____title": "Results", "@sage/xtrem-stock/pages__stock_valuation__itemSites____columns__columns__item__id__title": "Status", "@sage/xtrem-stock/pages__stock_valuation__itemSites____columns__columns__stockUnit__symbol__title": "Symbol", "@sage/xtrem-stock/pages__stock_valuation__itemSites____columns__title__commodityCode": "Commodity code", "@sage/xtrem-stock/pages__stock_valuation__itemSites____columns__title__company__name": "Company", "@sage/xtrem-stock/pages__stock_valuation__itemSites____columns__title__costType": "Cost type", "@sage/xtrem-stock/pages__stock_valuation__itemSites____columns__title__currency__symbol": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_valuation__itemSites____columns__title__item__category__id": "Item category", "@sage/xtrem-stock/pages__stock_valuation__itemSites____columns__title__item__description": "Item description", "@sage/xtrem-stock/pages__stock_valuation__itemSites____columns__title__item__id": "Item ID", "@sage/xtrem-stock/pages__stock_valuation__itemSites____columns__title__item__name": "<PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_valuation__itemSites____columns__title__item__status": "Status", "@sage/xtrem-stock/pages__stock_valuation__itemSites____columns__title__postingClass__name": "Posting class", "@sage/xtrem-stock/pages__stock_valuation__itemSites____columns__title__quantity": "Quantity", "@sage/xtrem-stock/pages__stock_valuation__itemSites____columns__title__site__name": "Site", "@sage/xtrem-stock/pages__stock_valuation__itemSites____columns__title__stockUnit__symbol": "Stock unit", "@sage/xtrem-stock/pages__stock_valuation__itemSites____columns__title__stockValue": "Stock value", "@sage/xtrem-stock/pages__stock_valuation__itemSites____columns__title__unitCost": "Unit cost", "@sage/xtrem-stock/pages__stock_valuation__itemSites____columns__title__valuationDate": "Valuation date", "@sage/xtrem-stock/pages__stock_valuation__itemTo____columns__title__description": "Description", "@sage/xtrem-stock/pages__stock_valuation__itemTo____columns__title__id": "<PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_valuation__itemTo____columns__title__name": "Name", "@sage/xtrem-stock/pages__stock_valuation__itemTo____lookupDialogTitle": "Select item", "@sage/xtrem-stock/pages__stock_valuation__itemTo____placeholder": "Select ...", "@sage/xtrem-stock/pages__stock_valuation__itemTo____title": "To item", "@sage/xtrem-stock/pages__stock_valuation__itemValues____columns__columns__stockUnit__symbol__title": "ID", "@sage/xtrem-stock/pages__stock_valuation__itemValues____columns__columns__stockUnit__symbol__title__2": "Symbol", "@sage/xtrem-stock/pages__stock_valuation__itemValues____columns__title__company__name": "Company", "@sage/xtrem-stock/pages__stock_valuation__itemValues____columns__title__currency__symbol": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_valuation__itemValues____columns__title__dateOfValuation": "Valuation date", "@sage/xtrem-stock/pages__stock_valuation__itemValues____columns__title__item__id": "<PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_valuation__itemValues____columns__title__item__name": "Name", "@sage/xtrem-stock/pages__stock_valuation__itemValues____columns__title__lastDateOfValuation": "Last valuation date", "@sage/xtrem-stock/pages__stock_valuation__itemValues____columns__title__lastQuantityInStock": "Last quantity", "@sage/xtrem-stock/pages__stock_valuation__itemValues____columns__title__lastStockValue": "Last stock value", "@sage/xtrem-stock/pages__stock_valuation__itemValues____columns__title__lastUnitCost": "Last unit cost", "@sage/xtrem-stock/pages__stock_valuation__itemValues____columns__title__lastValuationMethod": "Last cost type", "@sage/xtrem-stock/pages__stock_valuation__itemValues____columns__title__postingClass__name": "Posting class", "@sage/xtrem-stock/pages__stock_valuation__itemValues____columns__title__quantityInStock": "Quantity", "@sage/xtrem-stock/pages__stock_valuation__itemValues____columns__title__site__name": "Site", "@sage/xtrem-stock/pages__stock_valuation__itemValues____columns__title__stockUnit__symbol": "Stock unit", "@sage/xtrem-stock/pages__stock_valuation__itemValues____columns__title__stockValue": "Stock value", "@sage/xtrem-stock/pages__stock_valuation__itemValues____columns__title__unitCost": "Unit cost", "@sage/xtrem-stock/pages__stock_valuation__itemValues____columns__title__valuationMethod": "Cost type", "@sage/xtrem-stock/pages__stock_valuation__itemValues____columns__title__variance": "<PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_valuation__mainSection____title": "General", "@sage/xtrem-stock/pages__stock_valuation__postingClass____lookupDialogTitle": "Select posting class", "@sage/xtrem-stock/pages__stock_valuation__postingClass____title": "Posting class", "@sage/xtrem-stock/pages__stock_valuation__runStockValuation____title": "Run", "@sage/xtrem-stock/pages__stock_valuation__search": "Search", "@sage/xtrem-stock/pages__stock_valuation__selectedSites____columns__columns__site__name__columns__title__id": "ID", "@sage/xtrem-stock/pages__stock_valuation__selectedSites____columns__columns__site__name__title": "Name", "@sage/xtrem-stock/pages__stock_valuation__selectedSites____columns__columns__site__name__title__2": "ID", "@sage/xtrem-stock/pages__stock_valuation__selectedSites____columns__title__site__name": "Site", "@sage/xtrem-stock/pages__stock_valuation__selectedSites____title": "Sites", "@sage/xtrem-stock/pages__stock_valuation__sites____columns__title__id": "ID", "@sage/xtrem-stock/pages__stock_valuation__sites____columns__title__name": "Name", "@sage/xtrem-stock/pages__stock_valuation__sites____lookupDialogTitle": "Select site", "@sage/xtrem-stock/pages__stock_valuation__sites____placeholder": "Select ...", "@sage/xtrem-stock/pages__stock_valuation__sites____title": "Site", "@sage/xtrem-stock/pages__stock_valuation__status____title": "Status", "@sage/xtrem-stock/pages__stock_valuation__toItem____columns__title__category__name": "Category", "@sage/xtrem-stock/pages__stock_valuation__toItem____columns__title__description": "Description", "@sage/xtrem-stock/pages__stock_valuation__toItem____columns__title__id": "ID", "@sage/xtrem-stock/pages__stock_valuation__toItem____columns__title__name": "Name", "@sage/xtrem-stock/pages__stock_valuation__toItem____lookupDialogTitle": "Select item", "@sage/xtrem-stock/pages__stock_valuation__toItem____placeholder": "Select...", "@sage/xtrem-stock/pages__stock_valuation__toItem____title": "To item", "@sage/xtrem-stock/pages__stock_valuation__totalStockValue____title": "Total stock value", "@sage/xtrem-stock/pages__stock_valuation__totalStockValueCurrency____title": "Total stock value currency", "@sage/xtrem-stock/pages__stock_valuation__user____columns__title__email": "Email", "@sage/xtrem-stock/pages__stock_valuation__user____columns__title__firstName": "First name", "@sage/xtrem-stock/pages__stock_valuation__user____columns__title__lastName": "Last name", "@sage/xtrem-stock/pages__stock_valuation__user____lookupDialogTitle": "Select user", "@sage/xtrem-stock/pages__stock_valuation__user____title": "Calculation user", "@sage/xtrem-stock/pages__stock_valuation__valuationDate____title": "Date", "@sage/xtrem-stock/pages__stock_valuation__valuationMethod____title": "Valuation method", "@sage/xtrem-stock/pages__stock_valuation_inquiry__search": "Search", "@sage/xtrem-stock/pages__stock_valuation_new____objectTypePlural": "Stock valuations", "@sage/xtrem-stock/pages__stock_valuation_new____objectTypeSingular": "Stock valuation", "@sage/xtrem-stock/pages__stock_valuation_new____title": "Stock valuation", "@sage/xtrem-stock/pages__stock_valuation_new__commodityCode____title": "Commodity code", "@sage/xtrem-stock/pages__stock_valuation_new__company____columns__title__id": "ID", "@sage/xtrem-stock/pages__stock_valuation_new__company____columns__title__name": "Name", "@sage/xtrem-stock/pages__stock_valuation_new__company____lookupDialogTitle": "Select company", "@sage/xtrem-stock/pages__stock_valuation_new__company____placeholder": "Select...", "@sage/xtrem-stock/pages__stock_valuation_new__company____title": "Company", "@sage/xtrem-stock/pages__stock_valuation_new__criteriaBlock____title": "Criteria", "@sage/xtrem-stock/pages__stock_valuation_new__date____title": "Date", "@sage/xtrem-stock/pages__stock_valuation_new__displayZeroValues____title": "Display zero values", "@sage/xtrem-stock/pages__stock_valuation_new__fromItem____columns__title__description": "Description", "@sage/xtrem-stock/pages__stock_valuation_new__fromItem____columns__title__id": "ID", "@sage/xtrem-stock/pages__stock_valuation_new__fromItem____columns__title__name": "Name", "@sage/xtrem-stock/pages__stock_valuation_new__fromItem____lookupDialogTitle": "Select item", "@sage/xtrem-stock/pages__stock_valuation_new__fromItem____placeholder": "Select...", "@sage/xtrem-stock/pages__stock_valuation_new__fromItem____title": "From item", "@sage/xtrem-stock/pages__stock_valuation_new__itemCategory____columns__title__id": "ID", "@sage/xtrem-stock/pages__stock_valuation_new__itemCategory____columns__title__name": "Name", "@sage/xtrem-stock/pages__stock_valuation_new__itemCategory____columns__title__type": "Type", "@sage/xtrem-stock/pages__stock_valuation_new__itemCategory____lookupDialogTitle": "Select item category", "@sage/xtrem-stock/pages__stock_valuation_new__itemCategory____title": "Item category", "@sage/xtrem-stock/pages__stock_valuation_new__itemSites____columns__columns__item__id__title": "Status", "@sage/xtrem-stock/pages__stock_valuation_new__itemSites____columns__columns__stockUnit__symbol__title": "ID", "@sage/xtrem-stock/pages__stock_valuation_new__itemSites____columns__columns__stockUnit__symbol__title__2": "Symbol", "@sage/xtrem-stock/pages__stock_valuation_new__itemSites____columns__title__commodityCode": "Commodity code", "@sage/xtrem-stock/pages__stock_valuation_new__itemSites____columns__title__company__name": "Company", "@sage/xtrem-stock/pages__stock_valuation_new__itemSites____columns__title__costType": "Cost type", "@sage/xtrem-stock/pages__stock_valuation_new__itemSites____columns__title__currency__symbol": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_valuation_new__itemSites____columns__title__item__category__id": "Item category", "@sage/xtrem-stock/pages__stock_valuation_new__itemSites____columns__title__item__id": "Item ID", "@sage/xtrem-stock/pages__stock_valuation_new__itemSites____columns__title__item__name": "Item name", "@sage/xtrem-stock/pages__stock_valuation_new__itemSites____columns__title__item__status": "Status", "@sage/xtrem-stock/pages__stock_valuation_new__itemSites____columns__title__postingClass__name": "Posting class", "@sage/xtrem-stock/pages__stock_valuation_new__itemSites____columns__title__quantity": "Quantity", "@sage/xtrem-stock/pages__stock_valuation_new__itemSites____columns__title__site__name": "Site", "@sage/xtrem-stock/pages__stock_valuation_new__itemSites____columns__title__stockUnit__symbol": "Stock unit", "@sage/xtrem-stock/pages__stock_valuation_new__itemSites____columns__title__stockValue": "Stock value", "@sage/xtrem-stock/pages__stock_valuation_new__itemSites____columns__title__unitCost": "Unit cost", "@sage/xtrem-stock/pages__stock_valuation_new__itemSites____columns__title__valuationDate": "Valuation date", "@sage/xtrem-stock/pages__stock_valuation_new__postingClass____columns__title__name": "Name", "@sage/xtrem-stock/pages__stock_valuation_new__postingClass____lookupDialogTitle": "Select posting class", "@sage/xtrem-stock/pages__stock_valuation_new__postingClass____title": "Posting class", "@sage/xtrem-stock/pages__stock_valuation_new__runStockValuation____title": "Run", "@sage/xtrem-stock/pages__stock_valuation_new__selectedSites____columns__columns__site__name__columns__title__id": "ID", "@sage/xtrem-stock/pages__stock_valuation_new__selectedSites____columns__columns__site__name__title": "Name", "@sage/xtrem-stock/pages__stock_valuation_new__selectedSites____columns__columns__site__name__title__2": "ID", "@sage/xtrem-stock/pages__stock_valuation_new__selectedSites____columns__title__site__name": "Site", "@sage/xtrem-stock/pages__stock_valuation_new__selectedSites____title": "Sites", "@sage/xtrem-stock/pages__stock_valuation_new__sites____columns__columns__legalCompany__currency__symbol__title": "ID", "@sage/xtrem-stock/pages__stock_valuation_new__sites____columns__title__id": "ID", "@sage/xtrem-stock/pages__stock_valuation_new__sites____columns__title__name": "Name", "@sage/xtrem-stock/pages__stock_valuation_new__sites____lookupDialogTitle": "Select site", "@sage/xtrem-stock/pages__stock_valuation_new__sites____placeholder": "Select...", "@sage/xtrem-stock/pages__stock_valuation_new__sites____title": "Site", "@sage/xtrem-stock/pages__stock_valuation_new__status____title": "Status", "@sage/xtrem-stock/pages__stock_valuation_new__toItem____columns__title__description": "Description", "@sage/xtrem-stock/pages__stock_valuation_new__toItem____columns__title__id": "ID", "@sage/xtrem-stock/pages__stock_valuation_new__toItem____columns__title__name": "Name", "@sage/xtrem-stock/pages__stock_valuation_new__toItem____lookupDialogTitle": "Select item", "@sage/xtrem-stock/pages__stock_valuation_new__toItem____placeholder": "Select...", "@sage/xtrem-stock/pages__stock_valuation_new__toItem____title": "To item", "@sage/xtrem-stock/pages__stock_valuation_new__totalStockValue____title": "Total stock value", "@sage/xtrem-stock/pages__stock_valuation_new__user____columns__title__email": "Email", "@sage/xtrem-stock/pages__stock_valuation_new__user____columns__title__firstName": "First name", "@sage/xtrem-stock/pages__stock_valuation_new__user____columns__title__lastName": "Last name", "@sage/xtrem-stock/pages__stock_valuation_new__user____lookupDialogTitle": "Select user", "@sage/xtrem-stock/pages__stock_valuation_new__user____title": "Calculation user", "@sage/xtrem-stock/pages__stock_valuation_new__valuationMethod____title": "Valuation method", "@sage/xtrem-stock/pages__stock_valuation_request__notification_request_sent": "Stock valuation request sent.", "@sage/xtrem-stock/pages__stock_valuation_request__notification_valuation_failed": "Stock valuation failed.", "@sage/xtrem-stock/pages__stock_valuation_request__notification_valuation_finished": "Stock valuation finished.", "@sage/xtrem-stock/pages__stock_value_change____navigationPanel__listItem__itemDescription__title": "Item description", "@sage/xtrem-stock/pages__stock_value_change____navigationPanel__listItem__itemId__title": "Item ID", "@sage/xtrem-stock/pages__stock_value_change____navigationPanel__listItem__line2__title": "Site name", "@sage/xtrem-stock/pages__stock_value_change____navigationPanel__listItem__line2Right__title": "Date", "@sage/xtrem-stock/pages__stock_value_change____navigationPanel__listItem__line3__title": "<PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_value_change____navigationPanel__listItem__siteID__title": "Site ID", "@sage/xtrem-stock/pages__stock_value_change____navigationPanel__listItem__title__title": "Number", "@sage/xtrem-stock/pages__stock_value_change____navigationPanel__listItem__titleRight__title": "Stock status", "@sage/xtrem-stock/pages__stock_value_change____objectTypePlural": "Stock value changes", "@sage/xtrem-stock/pages__stock_value_change____objectTypeSingular": "Stock value change", "@sage/xtrem-stock/pages__stock_value_change____title": "Stock value change", "@sage/xtrem-stock/pages__stock_value_change__addAverageCost____title": "Add", "@sage/xtrem-stock/pages__stock_value_change__defaultDimension____title": "Set default dimensions", "@sage/xtrem-stock/pages__stock_value_change__description____title": "Description", "@sage/xtrem-stock/pages__stock_value_change__duplication_among_added_lines": "Change or remove duplicate lines.", "@sage/xtrem-stock/pages__stock_value_change__goToSysNotificationPage____title": "Retry", "@sage/xtrem-stock/pages__stock_value_change__item____columns__title__category__name": "Category", "@sage/xtrem-stock/pages__stock_value_change__item____lookupDialogTitle": "Select item", "@sage/xtrem-stock/pages__stock_value_change__item____title": "<PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_value_change__lines____columns__title__amount": "Amount", "@sage/xtrem-stock/pages__stock_value_change__lines____columns__title__fifoCost__effectiveDate": "Effective date", "@sage/xtrem-stock/pages__stock_value_change__lines____columns__title__fifoCost__nonAbsorbedAmount": "Non-absorbed amount", "@sage/xtrem-stock/pages__stock_value_change__lines____columns__title__fifoCost__receiptDocumentLine__documentNumber": "Receipt", "@sage/xtrem-stock/pages__stock_value_change__lines____columns__title__fifoCost__receiptQuantity": "Receipt quantity", "@sage/xtrem-stock/pages__stock_value_change__lines____columns__title__fifoCost__remainingQuantity": "Remaining quantity", "@sage/xtrem-stock/pages__stock_value_change__lines____columns__title__fifoCost__sequence": "Sequence", "@sage/xtrem-stock/pages__stock_value_change__lines____columns__title__newAmount": "New amount", "@sage/xtrem-stock/pages__stock_value_change__lines____columns__title__newUnitCost": "New unit cost", "@sage/xtrem-stock/pages__stock_value_change__lines____columns__title__quantity": "Quantity", "@sage/xtrem-stock/pages__stock_value_change__lines____columns__title__stockTransactionStatus": "Stock status", "@sage/xtrem-stock/pages__stock_value_change__lines____columns__title__unitCost": "Unit cost", "@sage/xtrem-stock/pages__stock_value_change__lines____dropdownActions__title": "Delete", "@sage/xtrem-stock/pages__stock_value_change__lines____dropdownActions__title__2": "Dimensions", "@sage/xtrem-stock/pages__stock_value_change__lines____title": "Lines", "@sage/xtrem-stock/pages__stock_value_change__linesSection____title": "Lines", "@sage/xtrem-stock/pages__stock_value_change__mainSection____title": "General", "@sage/xtrem-stock/pages__stock_value_change__number____title": "Number", "@sage/xtrem-stock/pages__stock_value_change__post____title": "Post stock", "@sage/xtrem-stock/pages__stock_value_change__postedDate____title": "Date", "@sage/xtrem-stock/pages__stock_value_change__postingDetails____columns__title__financeIntegrationAppRecordId": "Accounting integration reference", "@sage/xtrem-stock/pages__stock_value_change__postingDetails____columns__title__postingStatus": "Status", "@sage/xtrem-stock/pages__stock_value_change__postingDetails____columns__title__targetDocumentNumber": "Document number", "@sage/xtrem-stock/pages__stock_value_change__postingDetails____columns__title__targetDocumentType": "Document type", "@sage/xtrem-stock/pages__stock_value_change__postingDetails____title": "Results", "@sage/xtrem-stock/pages__stock_value_change__postingMessageBlock____title": "Error details", "@sage/xtrem-stock/pages__stock_value_change__postingSection____title": "Posting", "@sage/xtrem-stock/pages__stock_value_change__repost____title": "Repost", "@sage/xtrem-stock/pages__stock_value_change__repost_errors": "Errors while reposting: {{errors}}", "@sage/xtrem-stock/pages__stock_value_change__save_warnings": "Warnings while saving:", "@sage/xtrem-stock/pages__stock_value_change__saveStockValueChange____title": "Save", "@sage/xtrem-stock/pages__stock_value_change__selectFromFifo____title": "Add from FIFO stack", "@sage/xtrem-stock/pages__stock_value_change__site____lookupDialogTitle": "Select site", "@sage/xtrem-stock/pages__stock_value_change__site____placeholder": "Select...", "@sage/xtrem-stock/pages__stock_value_change__site____title": "Site", "@sage/xtrem-stock/pages__stock_value_change__stockTransactionStatus____title": "Stock status", "@sage/xtrem-stock/pages__stock_value_change__totalQuantityInStock____title": "On hand", "@sage/xtrem-stock/pages__stock_value_change__totalValueOfStock____title": "Value", "@sage/xtrem-stock/pages__stock_value_change__unitCost____title": "Unit cost", "@sage/xtrem-stock/pages__stock_value_change__valuationMethod____title": "Valuation method", "@sage/xtrem-stock/pages__stock_value_correction____navigationPanel__listItem__description__title": "Description", "@sage/xtrem-stock/pages__stock_value_correction____navigationPanel__listItem__effectiveDate__title": "Date", "@sage/xtrem-stock/pages__stock_value_correction____navigationPanel__listItem__reasonCode__title": "Adjustment reason", "@sage/xtrem-stock/pages__stock_value_correction____navigationPanel__listItem__stockSite__title": "Site", "@sage/xtrem-stock/pages__stock_value_correction____navigationPanel__listItem__stockTransactionStatus__title": "Stock status", "@sage/xtrem-stock/pages__stock_value_correction____navigationPanel__listItem__title__title": "Number", "@sage/xtrem-stock/pages__stock_value_correction____objectTypePlural": "Stock value corrections", "@sage/xtrem-stock/pages__stock_value_correction____objectTypeSingular": "Stock value correction", "@sage/xtrem-stock/pages__stock_value_correction____title": "Stock value correction", "@sage/xtrem-stock/pages__stock_value_correction___correctedDocumentType____title": "Document type", "@sage/xtrem-stock/pages__stock_value_correction__addAdjustLine____title": "Add line", "@sage/xtrem-stock/pages__stock_value_correction__defaultDimension____title": "Set default dimensions", "@sage/xtrem-stock/pages__stock_value_correction__description____title": "Description", "@sage/xtrem-stock/pages__stock_value_correction__effectiveDate____title": "Date", "@sage/xtrem-stock/pages__stock_value_correction__lines____columns__columns__item__name__title": "Name", "@sage/xtrem-stock/pages__stock_value_correction__lines____columns__columns__item__name__title__2": "ID", "@sage/xtrem-stock/pages__stock_value_correction__lines____columns__columns__item__name__title__3": "Category", "@sage/xtrem-stock/pages__stock_value_correction__lines____columns__columns__title___sortValue": "Line number", "@sage/xtrem-stock/pages__stock_value_correction__lines____columns__columns__title__document__number": "Document number", "@sage/xtrem-stock/pages__stock_value_correction__lines____columns__columns__title__orderCost": "Order cost", "@sage/xtrem-stock/pages__stock_value_correction__lines____columns__lookupDialogTitle__item__name": "Select item", "@sage/xtrem-stock/pages__stock_value_correction__lines____columns__placeholder__item__name": "Select ...", "@sage/xtrem-stock/pages__stock_value_correction__lines____columns__title": "Document line", "@sage/xtrem-stock/pages__stock_value_correction__lines____columns__title__item__name": "<PERSON><PERSON>", "@sage/xtrem-stock/pages__stock_value_correction__lines____columns__title__newUnitCost": "New order cost", "@sage/xtrem-stock/pages__stock_value_correction__lines____columns__title__orderCost": "Order cost", "@sage/xtrem-stock/pages__stock_value_correction__lines____columns__title__stockTransactionStatus": "Stock status", "@sage/xtrem-stock/pages__stock_value_correction__lines____columns__title__unitCost": "Current cost", "@sage/xtrem-stock/pages__stock_value_correction__lines____dropdownActions__title": "Dimensions", "@sage/xtrem-stock/pages__stock_value_correction__lines____dropdownActions__title__2": "Delete", "@sage/xtrem-stock/pages__stock_value_correction__lines____title": "Document lines", "@sage/xtrem-stock/pages__stock_value_correction__mainSection____title": "General", "@sage/xtrem-stock/pages__stock_value_correction__number____title": "Number", "@sage/xtrem-stock/pages__stock_value_correction__post____title": "Post", "@sage/xtrem-stock/pages__stock_value_correction__reasonCode____columns__title__id": "ID", "@sage/xtrem-stock/pages__stock_value_correction__reasonCode____columns__title__name": "Name", "@sage/xtrem-stock/pages__stock_value_correction__reasonCode____columns__title__stockValueCorrection": "Value correction", "@sage/xtrem-stock/pages__stock_value_correction__reasonCode____lookupDialogTitle": "Select reason code", "@sage/xtrem-stock/pages__stock_value_correction__reasonCode____title": "Correction reason", "@sage/xtrem-stock/pages__stock_value_correction__saveStockValueCorrection____title": "Save", "@sage/xtrem-stock/pages__stock_value_correction__stockSite____columns__title__id": "ID", "@sage/xtrem-stock/pages__stock_value_correction__stockSite____columns__title__name": "Name", "@sage/xtrem-stock/pages__stock_value_correction__stockSite____lookupDialogTitle": "Select site", "@sage/xtrem-stock/pages__stock_value_correction__stockSite____placeholder": "Select ...", "@sage/xtrem-stock/pages__stock_value_correction__stockSite____title": "Site", "@sage/xtrem-stock/pages__stock_value_correction__stockTransactionStatus____title": "Stock status", "@sage/xtrem-stock/pages__stock-adjustment-detail-panel__notification__The_adjustment_quantity_is_mandatory": "Enter the adjustment quantity.", "@sage/xtrem-stock/pages__stock-receipt__notification__The_lot_is_mandatory": "Enter the lot.", "@sage/xtrem-stock/pages_sidebar_tab_title_information": "Information", "@sage/xtrem-stock/pages-confirm-cancel": "Cancel", "@sage/xtrem-stock/pages-confirm-delete": "Delete", "@sage/xtrem-stock/permission__create_item_site_costs_from_cost_roll_up_results__name": "Create item-site costs from cost roll up results", "@sage/xtrem-stock/permission__manage__name": "Manage", "@sage/xtrem-stock/permission__manage_cost__name": "Manage cost", "@sage/xtrem-stock/permission__post__name": "Post", "@sage/xtrem-stock/permission__read__name": "Read", "@sage/xtrem-stock/permission__start__name": "Start", "@sage/xtrem-stock/permission__sync_stock_value_change__name": "Synchronization stock value change", "@sage/xtrem-stock/permission__update_attributes_and_dimensions__name": "Update attributes and dimensions", "@sage/xtrem-stock/search": "Search", "@sage/xtrem-stock/search-count": "Search ({{#if plus}}+{{/if}}{{nb}})", "@sage/xtrem-stock/standard_cost_roll_up_calculation__stop_calculation": "Standard cost roll up calculation stopped at {{stopTime}}.", "@sage/xtrem-stock/standard_cost_roll_up_calculation_complete": "Standard cost roll up calculation complete.", "@sage/xtrem-stock/standard_cost_roll_up_calculation_completed": "Standard cost roll up calculation completed successfully.", "@sage/xtrem-stock/standard_cost_roll_up_calculation_failed": "Standard cost roll up calculation has failed: {{{errorMessage}}}.", "@sage/xtrem-stock/standard_cost_roll_up_calculation_processing_items": "The standard cost roll up calculation is processing items.", "@sage/xtrem-stock/standard_cost_roll_up_calculation_start": "Standard cost roll up calculation start.", "@sage/xtrem-stock/standard_cost_roll_up_calculation_updating_sub_assemblies": "The standard cost roll up calculation is updating the subassemblies.", "@sage/xtrem-stock/stock_count_pages__stock_count_print": "Print", "@sage/xtrem-stock/stock_value_change_from_item_site_cost": "Stock value change from item-site cost.", "@sage/xtrem-stock/stock_value_change_from_item_site_cost_complete": "Stock value change from item-site cost is complete.", "@sage/xtrem-stock/stock_value_change_from_item_site_cost_start": "Stock value change from item-site cost has started.", "@sage/xtrem-stock/stock_value_change_from_item_site_cost_stop": "Stop changing stock values on {{stopDate}} ", "@sage/xtrem-stock/stock-detailed-inquiry": "Enter the item."}