import * as xtremStockData from '@sage/xtrem-stock-data';
import type { StockEngineParameters } from '../interfaces';

export function isThisUpdateForStockReceipt(
    stockUpdateParameter: xtremStockData.interfaces.StockUpdateParameters<xtremStockData.enums.StockMovementTypeEnum>,
): stockUpdateParameter is xtremStockData.interfaces.StockUpdateParameters<xtremStockData.enums.StockMovementTypeEnum.receipt> {
    return stockUpdateParameter.movementType === 'receipt';
}

export function isThisUpdateForStockIssue(
    stockUpdateParameter: xtremStockData.interfaces.StockUpdateParameters<xtremStockData.enums.StockMovementTypeEnum>,
): stockUpdateParameter is xtremStockData.interfaces.StockUpdateParameters<xtremStockData.enums.StockMovementTypeEnum.issue> {
    return stockUpdateParameter.movementType === 'issue';
}

export function isThisUpdateForStockChange(
    stockUpdateParameter: xtremStockData.interfaces.StockUpdateParameters<xtremStockData.enums.StockMovementTypeEnum>,
): stockUpdateParameter is xtremStockData.interfaces.StockUpdateParameters<xtremStockData.enums.StockMovementTypeEnum.change> {
    return stockUpdateParameter.movementType === 'change';
}

export function isThisUpdateForStockTransfer(
    stockUpdateParameter: xtremStockData.interfaces.StockUpdateParameters<xtremStockData.enums.StockMovementTypeEnum>,
): stockUpdateParameter is xtremStockData.interfaces.StockUpdateParameters<xtremStockData.enums.StockMovementTypeEnum.transfer> {
    return stockUpdateParameter.movementType === 'transfer';
}

export function isThisUpdateForStockAdjustment(
    stockUpdateParameter: xtremStockData.interfaces.StockUpdateParameters<xtremStockData.enums.StockMovementTypeEnum>,
): stockUpdateParameter is xtremStockData.interfaces.StockUpdateParameters<xtremStockData.enums.StockMovementTypeEnum.adjustment> {
    return stockUpdateParameter.movementType === 'adjustment';
}

export function isThisUpdateForStockCorrection(
    stockUpdateParameter: xtremStockData.interfaces.StockUpdateParameters<xtremStockData.enums.StockMovementTypeEnum>,
): stockUpdateParameter is xtremStockData.interfaces.StockUpdateParameters<xtremStockData.enums.StockMovementTypeEnum.correction> {
    return stockUpdateParameter.movementType === 'correction';
}

export function isThisUpdateForStockValueChange(
    stockUpdateParameter: xtremStockData.interfaces.StockUpdateParameters<xtremStockData.enums.StockMovementTypeEnum>,
): stockUpdateParameter is xtremStockData.interfaces.StockUpdateParameters<xtremStockData.enums.StockMovementTypeEnum.valueChange> {
    return stockUpdateParameter.movementType === 'valueChange';
}

export function isThisStockUpdateDataOfStockReceipt(
    stockUpdateData: StockEngineParameters<xtremStockData.enums.StockMovementTypeEnum>,
): stockUpdateData is StockEngineParameters<xtremStockData.enums.StockMovementTypeEnum.receipt> {
    return stockUpdateData.stockUpdateParameters.movementType === 'receipt';
}

export function isThisStockUpdateDataOfStockIssue(
    stockUpdateData: StockEngineParameters<xtremStockData.enums.StockMovementTypeEnum>,
): stockUpdateData is StockEngineParameters<xtremStockData.enums.StockMovementTypeEnum.issue> {
    return stockUpdateData.stockUpdateParameters.movementType === 'issue';
}

export function isThisStockUpdateDataOfStockChange(
    stockUpdateData: StockEngineParameters<xtremStockData.enums.StockMovementTypeEnum>,
): stockUpdateData is StockEngineParameters<xtremStockData.enums.StockMovementTypeEnum.change> {
    return stockUpdateData.stockUpdateParameters.movementType === 'change';
}

export function isThisStockUpdateDataOfStockTransfer(
    stockUpdateData: StockEngineParameters<xtremStockData.enums.StockMovementTypeEnum>,
): stockUpdateData is StockEngineParameters<xtremStockData.enums.StockMovementTypeEnum.transfer> {
    return stockUpdateData.stockUpdateParameters.movementType === 'transfer';
}

export function isThisStockUpdateDataOfStockAdjustment(
    stockUpdateData: StockEngineParameters<xtremStockData.enums.StockMovementTypeEnum>,
): stockUpdateData is StockEngineParameters<xtremStockData.enums.StockMovementTypeEnum.adjustment> {
    return stockUpdateData.stockUpdateParameters.movementType === 'adjustment';
}

export function isThisStockUpdateDataOfStockCorrection(
    stockUpdateData: StockEngineParameters<xtremStockData.enums.StockMovementTypeEnum>,
): stockUpdateData is StockEngineParameters<xtremStockData.enums.StockMovementTypeEnum.correction> {
    return stockUpdateData.stockUpdateParameters.movementType === 'correction';
}

export function isThisStockUpdateDataOfStockValueChange(
    stockUpdateData: StockEngineParameters<xtremStockData.enums.StockMovementTypeEnum>,
): stockUpdateData is StockEngineParameters<xtremStockData.enums.StockMovementTypeEnum.valueChange> {
    return stockUpdateData.stockUpdateParameters.movementType === 'valueChange';
}

export function isArrayOfStockCorrectionDetail(
    details: Array<xtremStockData.nodes.BaseStockDetail>,
): details is xtremStockData.nodes.StockCorrectionDetail[] {
    return Array.isArray(details) && details.every(item => item instanceof xtremStockData.nodes.StockCorrectionDetail);
}

export function isArrayOfStockValueDetail(
    details: Array<xtremStockData.nodes.BaseStockDetail>,
): details is xtremStockData.nodes.StockValueDetail[] {
    return Array.isArray(details) && details.every(item => item instanceof xtremStockData.nodes.StockValueDetail);
}
