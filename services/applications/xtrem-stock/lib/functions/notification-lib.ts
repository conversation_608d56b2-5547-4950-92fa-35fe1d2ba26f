import type { Context, Dict, integer, StaticThis } from '@sage/xtrem-core';
import { asyncArray, LocalizedError, Logger, LogicError, SystemError } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremStockData from '@sage/xtrem-stock-data';
import * as xtremStock from '..';
import { StockValuationManager } from '../classes';

const logger = Logger.getLogger(__filename, 'stock-update-listener');

function getDetailResultForError(errorMessage: string): Array<xtremStockData.interfaces.StockEngineReturn> {
    return [
        {
            lotCreated: false,
            resultAction: 'none',
            stockJournalRecord: null,
            stockRecord: null,
            errorMessage,
        },
    ];
}

export async function reply(
    readOnlyContext: Context,
    requestPayload: xtremStockData.interfaces.StockPostingPayload<xtremStockData.enums.StockMovementTypeEnum>,
    replyPayload: xtremStockData.interfaces.StockReplyPayload<xtremStockData.enums.StockMovementTypeEnum>,
): Promise<void> {
    await readOnlyContext.runInWritableContext(async childContext => {
        await xtremStockData.functions.notificationLib.updateStockTransactionRecordAfterStockUpdate(
            childContext,
            replyPayload,
        );
        await childContext.reply(requestPayload.replyTopic, replyPayload);
    });
}

async function errorHandler(
    readOnlyContext: Context,
    payload: xtremStockData.interfaces.StockPostingPayload<xtremStockData.enums.StockMovementTypeEnum>,
    error: Error,
): Promise<void> {
    logger.error(() => `Handling an error in notification transaction:\n${error}`);
    logger.error(() => `Additional information:\n${JSON.stringify(error)}`);
    const notificationId = xtremMasterData.functions.tryToGetNotificationIdFromContext(readOnlyContext);

    const documentsInError: xtremStockData.interfaces.StockUpdateResultCommonType['documents'] = [];
    const genericErrorLine: xtremStockData.interfaces.StockUpdateResultCommonType['documents'][0]['lines'][0] = {
        id: -1,
        sortValue: -1,
        stockUpdateResultStatus: 'none',
        errorMessage: error.message,
        originLineIds: [],
        stockJournalRecords: [],
    };

    documentsInError.push(
        ...payload.documents.map(document => ({
            id: document.id,
            lines: [{ ...genericErrorLine }],
        })),
    );

    await reply(readOnlyContext, payload, {
        requestNotificationId: notificationId,
        updateResults: {
            [payload.stockUpdateParameters.movementType]: { documents: documentsInError },
        } as xtremStockData.interfaces.StockUpdateResults<xtremStockData.enums.StockMovementTypeEnum>,
    });
}

export async function createStockTransaction(
    readOnlyContext: Context,
    payload: xtremStockData.interfaces.StockPostingPayload<xtremStockData.enums.StockMovementTypeEnum>,
    notificationId: string,
): Promise<void> {
    await asyncArray(payload.documents).forEach(async document => {
        await asyncArray(document.lines).forEach(async line => {
            const stockTransactions = readOnlyContext.query(xtremStockData.nodes.StockTransaction, {
                filter: { documentLine: line.id },
            });

            if ((await stockTransactions.filter(transaction => transaction.isInProgress).length) >= 1) {
                // developer only error
                throw new SystemError('Another stock transaction for the same document is already in progress.');
            }

            try {
                await readOnlyContext.runInWritableContext(async writableContext => {
                    const stockTransaction = await writableContext.create(xtremStockData.nodes.StockTransaction, {
                        documentLine: line.id,
                        notificationId,
                    });
                    await stockTransaction.$.save();
                });
            } catch (err) {
                await errorHandler(readOnlyContext, payload, err);
            }
        });
    });
}

export async function getStockValuationManager<Detail extends xtremStockData.nodes.BaseStockDetail>(
    context: Context,
    details: Array<Detail>,
): Promise<xtremStock.classes.StockValuationManager | null> {
    // For the moment, use the StockValuationManager only for FIFO except for stock value change and price correction
    const itemSite = await context.read(xtremMasterData.nodes.ItemSite, {
        item: await details[0].item,
        site: await StockValuationManager.getStockSite(details[0]),
    });

    if (xtremStock.functions.typingLib.isArrayOfStockCorrectionDetail(details)) {
        return xtremStock.classes.StockValuationManager.getInstance(context, {
            action: 'correctValue',
            stockDetails: details,
        });
    }

    if (
        (await itemSite.valuationMethod) === 'fifoCost' ||
        (['averageCost', 'standardCost'].includes(await itemSite.valuationMethod) &&
            xtremStock.functions.typingLib.isArrayOfStockValueDetail(details))
    ) {
        if (xtremStock.functions.typingLib.isArrayOfStockValueDetail(details)) {
            return xtremStock.classes.StockValuationManager.getInstance(context, {
                action: 'changeValue',
                stockDetails: details,
            });
        }
        if (!xtremStock.functions.typingLib.isArrayOfStockCorrectionDetail(details)) {
            return xtremStock.classes.StockValuationManager.getInstance(context, {
                action: 'createValue',
                stockDetails: details,
            });
        }
    }
    return null;
}

export type DetailGroup = {
    lineIds: integer[];
    originLineMap: Record<integer, integer[]>;
    details: xtremStockData.nodes.BaseStockDetail[];
};
export type DetailGroups = Dict<DetailGroup>;

async function groupDetails(
    stockDetails: xtremStockData.nodes.BaseStockDetail[],
    getGroupIndexCallback: (detail: xtremStockData.nodes.BaseStockDetail) => Promise<string>,
): Promise<DetailGroups> {
    const detailGroups = {} as DetailGroups;
    await asyncArray(stockDetails).forEach(async detail => {
        const groupIndex = await getGroupIndexCallback(detail);
        if (!detailGroups[groupIndex]) {
            detailGroups[groupIndex] = { lineIds: [], originLineMap: {}, details: [] };
        }
        detailGroups[groupIndex].details.push(detail);
        const lineId = (await detail.documentLine)._id;
        if (detailGroups[groupIndex].lineIds.findIndex(id => id === lineId) === -1)
            detailGroups[groupIndex].lineIds.push(lineId);
        if (!detailGroups[groupIndex].originLineMap[lineId]) {
            detailGroups[groupIndex].originLineMap[lineId] = [];
        }
        // Test only on StockCorrectionDetail because today it's the only one that can trigger a reply on its origin
        if (detail instanceof xtremStockData.nodes.StockCorrectionDetail) {
            const originDocumentLine = await detail.originDocumentLine;
            if (
                originDocumentLine &&
                detailGroups[groupIndex].originLineMap[lineId].findIndex(id => id === originDocumentLine._id) === -1
            ) {
                detailGroups[groupIndex].originLineMap[lineId].push(originDocumentLine._id);
            }
        }
    });
    return detailGroups;
}

export async function getGroupedDetails(
    readOnlyContext: Context,
    args: {
        documentLineIds: integer[];
        stockUpdateParameters: xtremStockData.interfaces.StockUpdateParameters<xtremStockData.enums.StockMovementTypeEnum>;
        detailType: StaticThis<xtremStockData.nodes.BaseStockDetail>;
    },
): Promise<DetailGroups> {
    // In case we have no stock detail, the grouping is done by document line and the stock details are empty
    // they will be prepared later in the committed transaction
    if (
        (xtremStock.functions.typingLib.isThisUpdateForStockIssue(args.stockUpdateParameters) ||
            xtremStock.functions.typingLib.isThisUpdateForStockAdjustment(args.stockUpdateParameters) ||
            xtremStock.functions.typingLib.isThisUpdateForStockTransfer(args.stockUpdateParameters)) &&
        args.stockUpdateParameters.stockDetailData?.isDocumentWithoutStockDetails
    ) {
        return args.documentLineIds.reduce((acc, documentLineId) => {
            acc[`${documentLineId}`] = {
                lineIds: [documentLineId],
                originLineMap: { [documentLineId]: [] },
                details: [],
            };
            return acc;
        }, {} as DetailGroups);
    }

    let details: Array<xtremStockData.nodes.BaseStockDetail> = [];

    if (xtremStock.functions.typingLib.isThisUpdateForStockCorrection(args.stockUpdateParameters)) {
        details = await readOnlyContext
            .query(xtremStockData.nodes.StockCorrectionDetail, {
                filter: {
                    documentLine: { _in: args.documentLineIds },
                },
            })
            .filter(async stockCorrectionDetail => !(await stockCorrectionDetail.isTransacted))
            .toArray();
        return groupDetails(details, async detail => `${await (await detail.item).id}|${await (await detail.site).id}`);
    }

    if (xtremStock.functions.typingLib.isThisUpdateForStockValueChange(args.stockUpdateParameters)) {
        details = await readOnlyContext
            .query(xtremStockData.nodes.StockValueDetail, {
                filter: {
                    documentLine: { _in: args.documentLineIds },
                },
            })
            .toArray();
    } else {
        details = await xtremStock.functions.stockDetailLib.filterStockDetailsForMovement(
            readOnlyContext.query(xtremStockData.nodes.StockMovementDetail, {
                filter: {
                    documentLine: { _in: args.documentLineIds },
                },
            }),
            args.detailType as StaticThis<xtremStockData.nodes.StockMovementDetail>,
        );
    }

    return groupDetails(details, async detail => `${(await detail.documentLine)._id}`);
}

async function createMissingStockDetails(
    writableContext: Context,
    args: Parameters<typeof splitContextAndHandleError>[1],
) {
    // It's necessary to read the details with "forUpdate: true"
    // otherwise the update of StockDetailLot will not update the cache
    // and the linked correction details (managed in the same notification) will not consider the new lot
    let details = await writableContext
        .query(xtremStockData.nodes.BaseStockDetail, {
            filter: { _id: { _in: args.group.details.map(detail => detail._id) } },
            forUpdate: true,
        })
        .toArray();

    if (
        (xtremStock.functions.typingLib.isThisUpdateForStockIssue(args.stockUpdateParameters) ||
            xtremStock.functions.typingLib.isThisUpdateForStockTransfer(args.stockUpdateParameters)) &&
        args.stockUpdateParameters.allocationData?.isUsingAllocations
    ) {
        details = await asyncArray(args.group.lineIds).reduce(
            async (acc, lineId) =>
                acc.concat(
                    await xtremStock.functions.stockDetailLib.prepareDetailsUsingAllocations(
                        writableContext,
                        lineId,
                        args.stockUpdateParameters,
                    ),
                ),
            [] as (xtremStockData.nodes.StockIssueDetail | xtremStockData.nodes.StockChangeDetail)[],
        );
    }
    if (
        xtremStock.functions.typingLib.isThisUpdateForStockAdjustment(args.stockUpdateParameters) &&
        args.stockUpdateParameters.stockDetailData?.isDocumentWithoutStockDetails
    ) {
        details = await asyncArray(args.group.lineIds).reduce(
            async (acc, lineId) =>
                acc.concat(
                    await xtremStock.functions.stockDetailLib.prepareDetailsForStockCount(writableContext, lineId),
                ),
            [] as xtremStockData.nodes.StockAdjustmentDetail[],
        );
    }

    return details;
}

function splitContextAndHandleError(
    readOnlyContext: Context,
    args: {
        stockUpdateFunction: xtremStock.interfaces.StockUpdateFunction;
        group: DetailGroup;
        stockUpdateParameters: xtremStockData.interfaces.StockUpdateParameters<xtremStockData.enums.StockMovementTypeEnum>;
    },
): Promise<xtremStockData.interfaces.StockEngineReturn[]> {
    return readOnlyContext.runInWritableContext(async writableContext => {
        try {
            const details = await createMissingStockDetails(writableContext, args);

            let updateResult: xtremStockData.interfaces.StockEngineReturn[] = [];
            if (details.length > 0) {
                const stockValuationManager =
                    (await xtremStock.functions.notificationLib.getStockValuationManager(writableContext, details)) ??
                    undefined;

                updateResult = await args.stockUpdateFunction(writableContext, details, {
                    ...args.stockUpdateParameters,
                    stockValuationManager,
                });

                if (stockValuationManager) {
                    // finish is async in child class but can't be in parent abstract class => need to resolve
                    await Promise.resolve(stockValuationManager.finish(writableContext));
                }
            }
            return updateResult;
        } catch (err) {
            if (!(err instanceof LocalizedError)) throw err;

            logger.error(() => `Handling an error in notification transaction:\n${err}`);
            logger.error(() => `Additional information:\n${JSON.stringify(err)}`);
            return getDetailResultForError(err.getMessageAndDiagnosesText(writableContext.diagnoses));
        }
    });
}

function getDefaultMovementResultStatus(
    movementType: xtremStockData.enums.StockMovementType,
): xtremStockData.enums.StockUpdateResultAction {
    switch (movementType) {
        case 'issue':
            return 'decreased';
        case 'change':
            return 'changed';
        case 'correction':
            return 'corrected';
        case 'adjustment':
            return 'adjusted';
        case 'valueChange':
            return 'valueChange';
        case 'receipt':
        default:
            return 'increased';
    }
}

function prepareLineResultPayload(
    line: xtremStockData.interfaces.StockPostingPayload<xtremStockData.enums.StockMovementTypeEnum>['documents'][0]['lines'][0],
    detailResults: xtremStockData.interfaces.StockEngineReturn[],
    movementType: xtremStockData.enums.StockMovementType,
    originLineIds: integer[],
) {
    let stockUpdateResultStatus = getDefaultMovementResultStatus(movementType);
    if (detailResults.some(result => result.resultAction === 'none')) {
        stockUpdateResultStatus = 'none';
    } else if (detailResults.some(result => result.resultAction === 'noChange')) {
        stockUpdateResultStatus = 'noChange';
    }
    return {
        ...line,
        stockUpdateResultStatus,
        errorMessage:
            detailResults.length === 1 && detailResults[0].errorMessage ? detailResults[0].errorMessage : undefined,
        originLineIds,
        stockJournalRecords: detailResults.flatMap(result =>
            result.stockJournalRecord ? [result.stockJournalRecord._id] : [],
        ),
    };
}

function simulateError(
    readOnlyContext: Context,
    args: { movementType: xtremStockData.enums.StockMovementType; groupIndex: integer; numberOfGroups: integer },
): xtremStockData.interfaces.StockEngineReturn[] {
    const stockConfigErrorGenerator = xtremStock.functions.config.getStockConfig(readOnlyContext)?.errorGenerator;
    let generateErrorOnLine;
    switch (args.movementType) {
        case 'issue':
            generateErrorOnLine = stockConfigErrorGenerator?.stockIssue;
            break;
        case 'change':
            generateErrorOnLine = stockConfigErrorGenerator?.stockChange;
            break;
        case 'correction':
            generateErrorOnLine = stockConfigErrorGenerator?.stockCorrection;
            break;
        case 'adjustment':
            generateErrorOnLine = stockConfigErrorGenerator?.stockAdjustment;
            break;
        case 'valueChange':
            generateErrorOnLine = stockConfigErrorGenerator?.stockValueChange;
            break;
        case 'receipt':
        default:
            generateErrorOnLine = stockConfigErrorGenerator?.stockReceipt;
            break;
    }
    if (
        generateErrorOnLine === args.groupIndex ||
        (generateErrorOnLine === -1 && args.groupIndex === args.numberOfGroups - 1)
    ) {
        const errorMessage = `Generated error in stock ${args.movementType}`;
        logger.error(() => errorMessage);
        return getDetailResultForError(errorMessage);
    }
    return [];
}

export async function processGroups<MovementType extends xtremStockData.enums.StockMovementTypeEnum>(
    readOnlyContext: Context,
    processGroupsParameters: {
        payload: xtremStockData.interfaces.StockPostingPayload<MovementType>;
        stockUpdateFunction: xtremStock.interfaces.StockUpdateFunction;
        groupDetails: DetailGroups;
    },
): Promise<xtremStockData.interfaces.StockUpdateResultCommonType['documents'][0]['lines']> {
    let detailResults: xtremStockData.interfaces.StockEngineReturn[];
    const linesResult: xtremStockData.interfaces.StockUpdateResultCommonType['documents'][0]['lines'] = [];

    const flatMapLines = processGroupsParameters.payload.documents.flatMap(document => document.lines);

    const groupIds = Object.keys(processGroupsParameters.groupDetails);
    const numberOfGroups = groupIds.length;
    await asyncArray(groupIds).forEach(async (groupId, groupIndex) => {
        detailResults = simulateError(readOnlyContext, {
            movementType: processGroupsParameters.payload.stockUpdateParameters.movementType,
            groupIndex,
            numberOfGroups,
        });

        if (detailResults.length === 0) {
            detailResults = await splitContextAndHandleError(readOnlyContext, {
                stockUpdateFunction: processGroupsParameters.stockUpdateFunction,
                group: processGroupsParameters.groupDetails[groupId],
                stockUpdateParameters: processGroupsParameters.payload.stockUpdateParameters,
            });
        }

        if (detailResults?.length) {
            processGroupsParameters.groupDetails[groupId].lineIds.forEach(lineId => {
                const documentLine = flatMapLines.find(line => Number(line.id) === Number(lineId));
                if (!documentLine) {
                    throw new LogicError(`Document line with id ${lineId} not found in payload`);
                }
                linesResult.push(
                    prepareLineResultPayload(
                        documentLine,
                        detailResults,
                        processGroupsParameters.payload.stockUpdateParameters.movementType,
                        processGroupsParameters.groupDetails[groupId].originLineMap[lineId],
                    ),
                );
            });
        }
    });

    return linesResult;
}

export async function processDocuments(
    readOnlyContext: Context,
    args: {
        payload: xtremStockData.interfaces.StockPostingPayload<xtremStockData.enums.StockMovementTypeEnum>;
        updateSteps: {
            detailType: StaticThis<xtremStockData.nodes.BaseStockDetail>;
            stockUpdateFunction: xtremStock.interfaces.StockUpdateFunction;
            stockUpdateParameters: xtremStockData.interfaces.StockUpdateParameters<xtremStockData.enums.StockMovementTypeEnum>;
        }[];
    },
) {
    const stepsResults = {} as xtremStockData.interfaces.StockUpdateResults<xtremStockData.enums.StockMovementTypeEnum>;
    const lineToDocumentMap = args.payload.documents.reduce(
        (acc, document) => {
            document.lines.forEach(line => {
                acc[line.id] = document.id;
            });
            return acc;
        },
        {} as Record<
            xtremStockData.interfaces.StockPostingPayload<xtremStockData.enums.StockMovementTypeEnum>['documents'][0]['lines'][0]['id'],
            xtremStockData.interfaces.StockPostingPayload<xtremStockData.enums.StockMovementTypeEnum>['documents'][0]['id']
        >,
    );

    const notificationId = xtremMasterData.functions.tryToGetNotificationIdFromContext(readOnlyContext);

    await xtremStock.functions.notificationLib.createStockTransaction(readOnlyContext, args.payload, notificationId);

    await asyncArray(args.updateSteps).forEach(async updateStep => {
        const lineDetails = await xtremStock.functions.notificationLib.getGroupedDetails(readOnlyContext, {
            documentLineIds: args.payload.documents.flatMap(document => document.lines.map(line => line.id)),
            stockUpdateParameters: updateStep.stockUpdateParameters,
            detailType: updateStep.detailType,
        });

        logger.verbose(() => `Processing ${Object.keys(lineDetails).length} groups of details`);
        logger.verbose(() => `    lineDetails=${JSON.stringify(lineDetails)}`);

        const groupResults = await xtremStock.functions.notificationLib.processGroups(readOnlyContext, {
            payload: { ...args.payload, stockUpdateParameters: updateStep.stockUpdateParameters },
            stockUpdateFunction: updateStep.stockUpdateFunction,
            groupDetails: lineDetails,
        });
        logger.verbose(() => `Groups Results : ${Object.keys(groupResults).length} items`);
        logger.verbose(() => `    groupResults=${JSON.stringify(groupResults)}`);

        const documentsResults: Record<
            xtremStockData.interfaces.StockUpdateResultCommonType['documents'][0]['id'],
            { lines: xtremStockData.interfaces.StockUpdateResultCommonType['documents'][0]['lines'] }
        > = {};

        groupResults.forEach(groupResult => {
            if (!documentsResults[lineToDocumentMap[groupResult.id]]) {
                documentsResults[lineToDocumentMap[groupResult.id]] = { lines: [] };
            }
            documentsResults[lineToDocumentMap[groupResult.id]].lines.push(groupResult);
        });

        stepsResults[updateStep.stockUpdateParameters.movementType] = {
            documents: Object.entries(documentsResults).map(([id, { lines }]) => ({
                id: Number(id),
                lines,
            })),
        };
    });

    logger.verbose(() => `Documents Results : ${JSON.stringify(stepsResults)}`);

    await xtremStock.functions.notificationLib.reply(readOnlyContext, args.payload, {
        requestNotificationId: notificationId,
        updateResults: stepsResults,
        batchTrackingId: args.payload.batchTrackingId,
    });
}
