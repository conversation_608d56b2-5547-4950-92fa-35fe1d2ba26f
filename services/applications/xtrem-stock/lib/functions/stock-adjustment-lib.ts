import type { Context, integer } from '@sage/xtrem-core';
import { BusinessRuleError, asyncArray } from '@sage/xtrem-core';
import type * as xtremStockData from '@sage/xtrem-stock-data';
import * as xtremStock from '../../index';

export async function updateStockTransactionStatus(stockAdjustment: xtremStock.nodes.StockAdjustment): Promise<void> {
    await stockAdjustment.lines
        .filter(
            async stockAdjustmentLine =>
                (await stockAdjustmentLine.stockTransactions.some(
                    async stockTransaction => (await stockTransaction.status) === 'succeeded',
                )) && (await stockAdjustmentLine.stockTransactionStatus) === 'inProgress',
        )
        .forEach(async stockAdjustmentLine => {
            await stockAdjustmentLine.$.set({ stockTransactionStatus: 'completed' });
        });

    await stockAdjustment.$.save();
}

export function calculateStockAdjustmentLineDisplayStatus(
    stockDetailStatus: xtremStockData.enums.StockDetailStatus,
    stockTransactionStatus: xtremStockData.enums.StockDocumentTransactionStatus,
): xtremStock.enums.StockAdjustmentDisplayStatus {
    switch (stockTransactionStatus) {
        case 'completed':
            return 'adjusted';
        case 'error':
            return 'stockPostingError';
        case 'inProgress':
            return 'stockPostingInProgress';
        default:
            return stockDetailStatus === 'required' ? 'detailsRequired' : 'detailsEntered';
    }
}

export function calculateStockAdjustmentDisplayStatus(
    status: xtremStockData.enums.StockDocumentStatus,
    stockTransactionStatus: xtremStockData.enums.StockDocumentTransactionStatus,
    stockDetailStatus: string,
): xtremStock.enums.StockAdjustmentDisplayStatus {
    if (status === 'closed') return 'adjusted';
    if (stockTransactionStatus === 'inProgress') return 'stockPostingInProgress';
    if (stockTransactionStatus === 'error') return 'stockPostingError';
    if (stockTransactionStatus === 'draft' && stockDetailStatus === 'required') return 'detailsRequired';
    return 'detailsEntered';
}

/**
 * Calculate the display status of the header in function of the stock status and the status header:
 * - status "closed" => issued
 * - stockTransactionStatus "inProgress" => stock posting in progress
 * - stockTransactionStatus "error" => stock posting error
 * - one line "required" => detail required
 * - all lines "entered" or "notRequired" => Detail completed
 * @param document
 * @returns {xtremStock.enums.stockAdjustmentDisplayStatus} "detailsRequired" | "detailsEntered" | "stockPostingInProgress" | "stockPostingError" | "issued"
 */
export async function computeHeaderDisplayStatus(
    stockAdjustment: xtremStock.nodes.StockAdjustment,
): Promise<xtremStock.enums.StockAdjustmentDisplayStatus> {
    const areLinesRequired =
        (await stockAdjustment.lines.length) === 0 ||
        (await stockAdjustment.lines.some(async line => (await line.stockDetailStatus) === 'required'));
    return xtremStock.functions.stockAdjustmentLib.calculateStockAdjustmentDisplayStatus(
        await stockAdjustment.status,
        await stockAdjustment.stockTransactionStatus,
        areLinesRequired ? 'required' : 'entered',
    );
}

export async function validatePost(context: Context, documentIds: integer[]) {
    await asyncArray(documentIds).forEach(async documentId => {
        if (
            await context.queryCount(xtremStock.nodes.StockAdjustmentLine, {
                filter: {
                    document: documentId,
                    stockDetailStatus: 'required',
                },
            })
        ) {
            throw new BusinessRuleError(
                context.localize(
                    '@sage/xtrem-stock/nodes__stock-adjustment__cant_post_stock_adjustment_when_stock_detail_status_is_required',
                    'You need to enter stock details for all lines before you can post.',
                ),
            );
        }
    });
}
