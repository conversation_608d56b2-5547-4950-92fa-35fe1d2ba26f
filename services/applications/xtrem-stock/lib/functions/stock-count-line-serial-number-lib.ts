import type { Context, integer } from '@sage/xtrem-core';
import { BusinessRuleError } from '@sage/xtrem-core';
import * as xtremStockData from '@sage/xtrem-stock-data';
import * as xtremStock from '..';

class StockCountLineSerialNumberLib {
    // eslint-disable-next-line @typescript-eslint/no-empty-function
    private constructor() {}

    public static readonly instance: StockCountLineSerialNumberLib = new StockCountLineSerialNumberLib();

    // eslint-disable-next-line class-methods-use-this
    async getRanges(
        context: Context,
        args: { stockCountLineId: integer; startingSerialNumber: string; endingSerialNumber: string },
    ) {
        const startingSerialNumber = await context.select(
            xtremStock.nodes.StockCountLineSerialNumber,
            { _id: true },
            {
                filter: {
                    stockCountLine: { _id: args.stockCountLineId },
                    serialNumber: { id: args.startingSerialNumber },
                },
            },
        );

        if (startingSerialNumber.length === 0) {
            throw new BusinessRuleError(
                context.localize(
                    '@sage/xtrem-stock/nodes__stock_count_line_serial_number__invalid_starting_serial_number_error',
                    'The serial number is not assigned to the stock count line. Serial number {{serialNumber}}.',
                    { serialNumber: args.startingSerialNumber, line: args.stockCountLineId },
                ),
            );
        }

        const serialNumbers = (
            await context.select(
                xtremStock.nodes.StockCountLineSerialNumber,
                { serialNumber: { id: true } },
                {
                    filter: {
                        stockCountLine: { _id: args.stockCountLineId },
                        serialNumber: { id: { _gte: args.startingSerialNumber, _lte: args.endingSerialNumber } },
                    },
                    orderBy: { serialNumber: { id: +1 } },
                },
            )
        ).map(stockCountLineSerialNumber => stockCountLineSerialNumber.serialNumber.id);

        return xtremStockData.sharedFunctions.serialNumberHelper.getSerialNumberRangesFromIds(serialNumbers);
    }
}

export const stockCountLineSerialNumberLib = StockCountLineSerialNumberLib.instance;
