import type { Context, NodeCreateData } from '@sage/xtrem-core';
import { BusinessRuleError } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import type * as xtremSystem from '@sage/xtrem-system';
import type * as xtremStock from '..';

function getVirtualLocationDetail(need: xtremStock.enums.VirtualLocationNeed): {
    type: NodeCreateData<xtremMasterData.nodes.LocationType>;
    zone: NodeCreateData<xtremMasterData.nodes.LocationZone>;
    location: NodeCreateData<xtremMasterData.nodes.Location>;
} {
    switch (need) {
        case 'transfer':
            return {
                type: {
                    id: 'TRANSFER_TYPE',
                    name: 'Transfer type',
                    description: 'Transfer virtual location type',
                    locationCategory: 'virtual',
                },
                zone: {
                    id: 'TRANSFER_ZONE',
                    name: 'Transfer zone',
                    zoneType: 'virtual',
                },
                location: {
                    id: 'TRANSFER_LOCATION',
                    name: 'Transfer location',
                },
            };
        default:
            throw new BusinessRuleError(`Unknown virtual location need: ${need}`);
    }
}

async function generateVirtualLocationType(context: Context, need: xtremStock.enums.VirtualLocationNeed) {
    const virtualLocationType = await context.create(xtremMasterData.nodes.LocationType, {
        ...getVirtualLocationDetail(need).type,
        isVirtualAllowed: true,
    });
    await virtualLocationType.$.save();
    return virtualLocationType;
}

async function getVirtualLocationType(context: Context, need: xtremStock.enums.VirtualLocationNeed) {
    let virtualLocationType = await context.tryRead(xtremMasterData.nodes.LocationType, {
        id: getVirtualLocationDetail(need).type.id,
    });

    if (!virtualLocationType) {
        virtualLocationType = await generateVirtualLocationType(context, need);
    }

    return virtualLocationType;
}

async function generateVirtualLocationZone(
    context: Context,
    need: xtremStock.enums.VirtualLocationNeed,
    site: xtremSystem.nodes.Site,
) {
    const virtualLocationZone = await context.create(xtremMasterData.nodes.LocationZone, {
        ...getVirtualLocationDetail(need).zone,
        site,
        isVirtualAllowed: true,
    });
    await virtualLocationZone.$.save();
    return virtualLocationZone;
}

async function getVirtualLocationZone(
    context: Context,
    need: xtremStock.enums.VirtualLocationNeed,
    site: xtremSystem.nodes.Site,
) {
    let virtualLocationZone = await context.tryRead(xtremMasterData.nodes.LocationZone, {
        id: getVirtualLocationDetail(need).zone.id,
    });

    if (!virtualLocationZone) {
        virtualLocationZone = await generateVirtualLocationZone(context, need, site);
    }

    return virtualLocationZone;
}

async function generateVirtualLocation(
    context: Context,
    need: xtremStock.enums.VirtualLocationNeed,
    site: xtremSystem.nodes.Site,
) {
    const locationType = await getVirtualLocationType(context, need);
    const locationZone = await getVirtualLocationZone(context, need, site);

    const virtualLocation = await context.create(xtremMasterData.nodes.Location, {
        ...getVirtualLocationDetail(need).location,
        locationType,
        locationZone,
        isVirtualAllowed: true,
    });
    await virtualLocation.$.save();
    return virtualLocation;
}

/**
 * This function retrieves a virtual location dedicated to a particular need (e.g. stock transfer).
 * If there is no existing one, it will generate one (same for locationType and locationZone).
 * It must be run in a writable context!
 * The IDs are static as they are managed by the application, and we can only have one virtual location per site for a purpose.
 *
 * @param {Context} context - The context in which the function is executed. Must be writable.
 * @param {xtremStock.enums.VirtualLocationNeed} need - The specific need for which the virtual location is required.
 * @param {xtremSystem.nodes.Site} site - The site for which the virtual location is being retrieved or generated.
 * @returns {Promise<xtremMasterData.nodes.Location>} - The virtual location (loaded or generated).
 */
export async function getVirtualLocation(
    context: Context,
    need: xtremStock.enums.VirtualLocationNeed,
    site: xtremSystem.nodes.Site,
) {
    let virtualLocation = await context.tryRead(xtremMasterData.nodes.Location, {
        id: getVirtualLocationDetail(need).location.id,
    });

    if (!virtualLocation) {
        virtualLocation = await generateVirtualLocation(context, need, site);
    }

    return virtualLocation;
}
