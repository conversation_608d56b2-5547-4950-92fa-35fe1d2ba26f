import type { AsyncArray, Collection, Context, integer, NodeCreateData, StaticThis } from '@sage/xtrem-core';
import { asyncArray, BusinessRuleError, SystemError } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import { DataInputError } from '@sage/xtrem-shared';
import * as xtremStockData from '@sage/xtrem-stock-data';
import * as xtremStock from '..';

export type MovementStockDetail<T extends xtremStockData.enums.StockMovementTypeEnum> =
    (T extends xtremStockData.enums.StockMovementTypeEnum.receipt
        ? xtremStockData.nodes.StockReceiptDetail
        : xtremStockData.nodes.BaseStockDetail) &
        (T extends xtremStockData.enums.StockMovementTypeEnum.issue
            ? xtremStockData.nodes.StockIssueDetail
            : xtremStockData.nodes.BaseStockDetail) &
        (T extends xtremStockData.enums.StockMovementTypeEnum.change
            ? xtremStockData.nodes.StockChangeDetail
            : xtremStockData.nodes.BaseStockDetail) &
        (T extends xtremStockData.enums.StockMovementTypeEnum.correction
            ? xtremStockData.nodes.StockCorrectionDetail
            : xtremStockData.nodes.BaseStockDetail) &
        (T extends xtremStockData.enums.StockMovementTypeEnum.adjustment
            ? xtremStockData.nodes.StockAdjustmentDetail
            : xtremStockData.nodes.BaseStockDetail);

function ensureWritableStockNode(
    context: Context,
    stockRecord: xtremStockData.nodes.Stock | null,
): Promise<xtremStockData.nodes.Stock> {
    if (!stockRecord) throw new SystemError('stockRecord null'); // shouldn't happen

    return xtremMasterData.functions.getWritableNode(context, xtremStockData.nodes.Stock, stockRecord._id);
}

export async function ensureWritableStockDetail<Detail extends xtremStockData.nodes.BaseStockDetail>(
    context: Context,
    stockDetail: Detail,
): Promise<Detail> {
    if (xtremMasterData.functions.doesNodeExistsInDatabase(stockDetail)) {
        return (await xtremMasterData.functions.getWritableNode(
            context,
            xtremStockData.nodes.BaseStockDetail,
            stockDetail._id,
        )) as unknown as Detail;
    }

    if (!stockDetail.$.isWritable) {
        throw new DataInputError(
            'The stock-detail received could not be loaded from the database and is not writable.',
        );
    }

    return stockDetail;
}

export async function readLotSequenceNumberID(
    lotCreateData: xtremStockData.interfaces.LotCreateData | null,
): Promise<integer | undefined> {
    if (lotCreateData) {
        if (Number.isInteger(await lotCreateData.item.lotSequenceNumber)) {
            return (await lotCreateData.item.lotSequenceNumber) as any;
        }
        return (await lotCreateData.item.lotSequenceNumber)?._id;
    }
    return undefined;
}

export async function prepareStockSearchDataForReceipt(
    stockDetail: xtremStockData.nodes.StockReceiptDetail,
): Promise<
    xtremStock.interfaces.StockEngineParameters<xtremStockData.enums.StockMovementTypeEnum.receipt>['searchData']
> {
    return {
        item: await stockDetail.item,
        location: await stockDetail.location,
        lot: (await (await stockDetail.stockDetailLot)?.lot) || null,
        site: await stockDetail.site,
        status: await stockDetail.status,
        stockUnit: await stockDetail.stockUnit,
        owner: await stockDetail.owner,
    };
}

export async function prepareStockSearchDataForValueChange(
    stockDetail: xtremStockData.nodes.StockValueDetail,
): Promise<
    xtremStock.interfaces.StockEngineParameters<xtremStockData.enums.StockMovementTypeEnum.valueChange>['searchData']
> {
    const site = await stockDetail.site; // For better performance
    return {
        item: await stockDetail.item,
        location: null,
        lot: null,
        site,
        stockUnit: await stockDetail.stockUnit,
        owner: await site.id,
    };
}

export async function prepareStockSearchDataForIssue(
    stockDetail: xtremStockData.nodes.StockIssueDetail,
): Promise<
    xtremStock.interfaces.StockEngineParameters<xtremStockData.enums.StockMovementTypeEnum.issue>['searchData']
> {
    return {
        item: await stockDetail.item,
        location: await (await stockDetail.stockRecord)!.location,
        lot: await (await stockDetail.stockRecord)!.lot,
        site: await stockDetail.site,
        status: await (await stockDetail.stockRecord)!.status,
        stockUnit: await stockDetail.stockUnit,
        owner: await (await stockDetail.stockRecord)!.owner,
    };
}

export async function prepareStockSearchDataForChange(
    stockDetail: xtremStockData.nodes.StockChangeDetail,
): Promise<
    xtremStock.interfaces.StockEngineParameters<xtremStockData.enums.StockMovementTypeEnum.change>['searchData']
> {
    return {
        item: await stockDetail.item,
        location: await stockDetail.location,
        lot: await (await stockDetail.stockRecord)!.lot,
        site: await stockDetail.site,
        status: await stockDetail.status,
        stockUnit: await stockDetail.stockUnit,
        owner: await stockDetail.owner,
    };
}

export async function prepareStockSearchDataForAdjustment(
    stockDetail: xtremStockData.nodes.StockAdjustmentDetail,
): Promise<
    xtremStock.interfaces.StockEngineParameters<xtremStockData.enums.StockMovementTypeEnum.adjustment>['searchData']
> {
    return {
        item: await stockDetail.item,
        location: (await (await stockDetail.stockRecord)?.location) || (await stockDetail.location),
        lot: (await (await stockDetail.stockRecord)?.lot) || (await (await stockDetail.stockDetailLot)?.lot) || null,
        site: await stockDetail.site,
        status: (await (await stockDetail.stockRecord)?.status) || (await stockDetail.status),
        stockUnit: await stockDetail.stockUnit,
        owner: (await (await stockDetail.stockRecord)?.owner) || (await stockDetail.owner),
    };
}

export async function prepareStockUpdateData<Detail extends xtremStockData.nodes.BaseStockDetail>(
    context: Context,
    stockDetail: Detail,
    stockUpdateParameters: xtremStockData.interfaces.StockUpdateParameters<xtremStockData.enums.StockMovementTypeEnum>,
): Promise<xtremStock.interfaces.StockEngineParameters<xtremStockData.enums.StockMovementTypeEnum>> {
    if (stockDetail instanceof xtremStockData.nodes.StockValueDetail) {
        const searchData = await prepareStockSearchDataForValueChange(stockDetail);
        return {
            stockDetail,
            searchData,
            stockRecord: null,
            stockUpdateParameters,
        };
    }

    if (stockDetail instanceof xtremStockData.nodes.StockReceiptDetail) {
        const searchData = await prepareStockSearchDataForReceipt(stockDetail);
        return {
            stockDetail,
            searchData,
            stockRecord: await xtremStockData.functions.stockLib.getStockRecord(context, searchData),
            stockUpdateParameters,
        };
    }

    if (stockDetail instanceof xtremStockData.nodes.StockChangeDetail) {
        return {
            stockDetail,
            stockRecord: await ensureWritableStockNode(context, await stockDetail.stockRecord),
            searchData: await prepareStockSearchDataForChange(stockDetail),
            stockUpdateParameters,
        };
    }

    if (stockDetail instanceof xtremStockData.nodes.StockIssueDetail) {
        return {
            stockDetail,
            stockRecord: await ensureWritableStockNode(context, await stockDetail.stockRecord),
            searchData: await prepareStockSearchDataForIssue(stockDetail),
            stockUpdateParameters,
        };
    }

    if (stockDetail instanceof xtremStockData.nodes.StockAdjustmentDetail) {
        const searchData = await prepareStockSearchDataForAdjustment(stockDetail);
        let stockRecord = await stockDetail.stockRecord;
        stockRecord = stockRecord
            ? await ensureWritableStockNode(context, stockRecord)
            : await xtremStockData.functions.stockLib.getStockRecord(context, searchData);
        return {
            stockDetail,
            stockRecord,
            searchData,
            stockUpdateParameters,
        };
    }

    // Note: stockDetail must be of type Correction as other types have been eliminated
    //       by previous branches.
    const stockCorrectionDetail = stockDetail as unknown as xtremStockData.nodes.StockCorrectionDetail;
    const dataFromOriginStockDetail = await prepareStockUpdateData(
        context,
        await xtremStock.functions.stockEngine.getCorrectionOriginalDetails(stockCorrectionDetail),
        stockUpdateParameters,
    );

    dataFromOriginStockDetail.stockDetail = stockCorrectionDetail;
    return {
        ...dataFromOriginStockDetail,
        stockUpdateParameters,
    };
}

export async function stockAllocationToDetailCreateData(
    context: Context,
    args: {
        allocation: xtremStockData.nodes.StockAllocation;
        documentLine: xtremStockData.interfaces.DocumentLineWithStockAllocation;
        stockUpdateParameters: xtremStockData.interfaces.StockUpdateParameters<xtremStockData.enums.StockMovementTypeEnum>;
    },
): Promise<NodeCreateData<xtremStockData.nodes.StockIssueDetail | xtremStockData.nodes.StockChangeDetail>> {
    const additionalData: NodeCreateData<
        xtremStockData.nodes.StockIssueDetail | xtremStockData.nodes.StockChangeDetail
    > = {
        quantityInStockUnit: -(await args.allocation.quantityInStockUnit),
    };

    const item = await (await args.allocation.stockRecord).item;

    if (
        xtremStock.functions.typingLib.isThisUpdateForStockTransfer(args.stockUpdateParameters) &&
        xtremStockData.functions.transfer.isInternalTransfer(args.stockUpdateParameters.intersiteTransferData?.type)
    ) {
        (additionalData as NodeCreateData<xtremStockData.nodes.StockChangeDetail>).canUpdateSite = true;
        (additionalData as NodeCreateData<xtremStockData.nodes.StockChangeDetail>).isIntersite = true;

        const receivingSite = await (
            await (args.documentLine as xtremStockData.interfaces.TransferDocumentLine).document
        ).receivingSite;

        (additionalData as NodeCreateData<xtremStockData.nodes.StockChangeDetail>).site = receivingSite;

        const itemSite = await context.tryRead(xtremMasterData.nodes.ItemSite, {
            site: receivingSite,
            item,
        });

        if (!itemSite) {
            throw new BusinessRuleError(
                context.localize(
                    '@sage/xtrem-stock/functions__stock-detail-lib__item_not_managed_on_site',
                    'The item is not managed on the receiving site.',
                ),
            );
        }

        if (await receivingSite.isLocationManaged) {
            (additionalData as NodeCreateData<xtremStockData.nodes.StockChangeDetail>).location =
                await xtremStock.functions.location.getVirtualLocation(context, 'transfer', receivingSite);
        }

        // this will override the negative quantity necessary for the issues to ensure its positive.
        additionalData.quantityInStockUnit = Math.abs(additionalData.quantityInStockUnit ?? 0);
    }

    return {
        item,
        site: await (await args.allocation.stockRecord).site,
        stockRecord: await args.allocation.stockRecord,
        stockUnit: await args.allocation.stockUnit,
        ...additionalData,
    };
}

export function serialNumbersAllocationToDetailSerialNumbersCreateData(
    stockAllocation: xtremStockData.nodes.StockAllocation,
): Promise<Array<NodeCreateData<xtremStockData.nodes.StockDetailSerialNumber>>> {
    return stockAllocation.serialNumbers
        .map(async serialNumber => ({
            serialNumber,
            supplierSerialNumber: await serialNumber.supplierSerialNumber,
        }))
        .toArray();
}

export function serialNumbersCountLineToDetailSerialNumbersCreateData(
    stockCountLine: xtremStock.nodes.StockCountLine,
): Promise<Array<NodeCreateData<xtremStockData.nodes.StockDetailSerialNumber>>> {
    return stockCountLine.stockCountLineSerialNumbers
        .filter(async serial => !(await serial.isCounted))
        .map(async line => ({
            serialNumber: await line.serialNumber,
            supplierSerialNumber: await (await line.serialNumber).supplierSerialNumber,
            _action: 'create',
        }))
        .toArray();
}

export function filterStockDetailsForMovement(
    stockDetails:
        | AsyncArray<xtremStockData.nodes.StockMovementDetail>
        | Collection<xtremStockData.nodes.StockMovementDetail>,
    detailType?: StaticThis<xtremStockData.nodes.StockMovementDetail>,
): Promise<Array<xtremStockData.nodes.StockMovementDetail>> {
    return stockDetails
        .filter(async detail => {
            let check = true;
            if (detailType) {
                check = detail instanceof detailType;
            }

            if (check) {
                check = check && !(await detail.isTransacted);
            }

            return check;
        })
        .toArray();
}

export async function prepareDetailsUsingAllocations(
    context: Context,
    documentLineId: number,
    stockUpdateParameters: xtremStockData.interfaces.StockUpdateParameters<xtremStockData.enums.StockMovementTypeEnum>,
): Promise<Array<xtremStockData.nodes.StockIssueDetail>> {
    const documentLine = (await context.read(
        xtremMasterData.nodes.BaseDocumentLine,
        { _id: documentLineId },
        { forUpdate: true },
    )) as xtremStockData.interfaces.DocumentLineWithStockAllocation & xtremStockData.interfaces.StockIssueDocumentLine;

    const conversionParameters: Parameters<
        typeof xtremStock.functions.allocationLib.transformDocumentLineAllocationsInStockDetails
    >[1] = {
        quantityToIssue:
            (await documentLine.quantityInStockUnit) -
            (await asyncArray(
                await filterStockDetailsForMovement(
                    documentLine.stockDetails as Collection<xtremStockData.nodes.StockIssueDetail>,
                ),
            ).sum(detail => detail.quantityInStockUnit)),
        documentLine,
    };

    const throwNotEnoughStockAllocations = () => {
        throw new BusinessRuleError(
            context.localize(
                '@sage/xtrem-stock/functions__stock-engine__not_enough_stock_allocation',
                'You should have allocated stock to post this document.',
            ),
        );
    };

    const detailsFromDocumentLineAllocations = () => {
        delete conversionParameters.quantityToIssue;
        return xtremStock.functions.allocationLib.transformDocumentLineAllocationsInStockDetails(
            context,
            conversionParameters,
            stockUpdateParameters,
        );
    };

    const stockDetailCreateData: Array<
        NodeCreateData<xtremStockData.nodes.StockIssueDetail | xtremStockData.nodes.StockChangeDetail>
    > = [];

    switch (await documentLine.allocationStatus) {
        case 'notAllocated':
        case 'partiallyAllocated':
            if (
                (conversionParameters.quantityToIssue || 0) >
                (await xtremStockData.functions.allocationLib.getDocumentLineAllocatedQuantity(
                    conversionParameters.documentLine,
                ))
            ) {
                throwNotEnoughStockAllocations();
            }

            stockDetailCreateData.push(...(await detailsFromDocumentLineAllocations()));
            break;
        case 'allocated':
            stockDetailCreateData.push(...(await detailsFromDocumentLineAllocations()));
            break;
        case 'notManaged':
        default:
            return [];
    }

    await documentLine.$.set({
        stockDetails: stockDetailCreateData,
    });

    await documentLine.$.save();

    return filterStockDetailsForMovement(documentLine.stockDetails) as Promise<
        (xtremStockData.nodes.StockIssueDetail | xtremStockData.nodes.StockChangeDetail)[]
    >;
}

/**
 * Prepare a StockAdjustmentDetail from a StockCountLine
 * @param context
 * @param documentLineID
 * @returns a node StockAdjustmentDetail to update the stock
 */
export async function prepareDetailsForStockCount(
    context: Context,
    documentLineID: number,
): Promise<xtremStockData.nodes.StockAdjustmentDetail[]> {
    const documentLine = (await context.read(xtremMasterData.nodes.BaseDocumentLine, {
        _id: documentLineID,
    })) as xtremStock.nodes.StockCountLine;

    const stockRecord = await (await documentLine.stockDetails?.elementAt(0))?.stockRecord;
    const stockDetail = (await context.read(
        xtremStockData.nodes.StockMovementDetail,
        {
            _id: (await documentLine.stockDetails?.elementAt(0))?._id,
        },
        { forUpdate: true },
    )) as xtremStockData.nodes.StockAdjustmentDetail;

    // If the stock record exists, the quantity must be the same as the one expected when starting the count
    if (!(!stockRecord || (await stockRecord.quantityInStockUnit) === (await documentLine.quantityInStockUnit))) {
        throw new BusinessRuleError(
            context.localize(
                '@sage/xtrem-stock/nodes__stock_update_listener__quantity_of_stock_record_to_be_counted_has_changed',
                'The quantity of the stock record to count has been modified since the stock count started.\n\n{{stock_identifiers}}',
                {
                    stock_identifiers: context.localize(
                        '@sage/xtrem-stock/nodes__stock_update_listener__stock_record_identification',
                        'Item: {{item}}\n\nStatus: {{status}}\n\nLot: {{lot}}\n\nLocation: {{location}}\n\nCurrent quantity in stock: {{quantity}}',
                        {
                            item: await ((await documentLine.item) as xtremMasterData.nodes.Item).id,
                            status: await (await documentLine.stockStatus)?.id,
                            lot:
                                (await (await stockDetail?.stockDetailLot)?.lot) &&
                                (await (
                                    await (
                                        await stockDetail?.stockDetailLot
                                    )?.lot
                                )?.id),
                            location:
                                (await documentLine.location) &&
                                (await ((await documentLine.location) as xtremMasterData.nodes.Location).id),
                            quantity: await stockRecord.quantityInStockUnit,
                        },
                    ),
                },
            ),
        );
    }

    let createDataStockDetailSerialNumbers: Array<NodeCreateData<xtremStockData.nodes.StockDetailSerialNumber>> | null =
        null;
    if (await context.isServiceOptionEnabled(xtremMasterData.serviceOptions.serialNumberOption)) {
        createDataStockDetailSerialNumbers = await serialNumbersCountLineToDetailSerialNumbersCreateData(documentLine);
    }

    await stockDetail?.$.set({
        stockDetailSerialNumbers: createDataStockDetailSerialNumbers || undefined,
    });
    await stockDetail.$.save();

    xtremStock.functions.stockEngine.logger.debug(
        () => `stock adjustment to create: ${JSON.stringify(stockDetail, null, 4)}`,
    );
    return [stockDetail];
}
