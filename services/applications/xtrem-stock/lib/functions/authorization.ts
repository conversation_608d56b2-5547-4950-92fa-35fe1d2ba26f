import type { OperationGrant } from '@sage/xtrem-core';
import * as xtremFinanceData from '@sage/xtrem-finance-data';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremStockData from '@sage/xtrem-stock-data';
import * as xtremStructure from '@sage/xtrem-structure';
import * as xtremSystem from '@sage/xtrem-system';
import * as xtremUpload from '@sage/xtrem-upload';

export const commonStockActivities: OperationGrant[] = [
    { operations: ['lookup'], on: [() => xtremSystem.nodes.Company] },
    { operations: ['lookup'], on: [() => xtremSystem.nodes.Site] },
    { operations: ['lookup'], on: [() => xtremMasterData.nodes.Currency] },
    { operations: ['lookup'], on: [() => xtremMasterData.nodes.Item] },
    { operations: ['lookup'], on: [() => xtremMasterData.nodes.ItemSite] },
    { operations: ['lookup'], on: [() => xtremMasterData.nodes.ItemCategory] },
    { operations: ['lookup'], on: [() => xtremFinanceData.nodes.PostingClass] },
    { operations: ['lookup'], on: [() => xtremStockData.nodes.StockJournal] },
    { operations: ['lookup'], on: [() => xtremMasterData.nodes.BusinessEntity] },
    { operations: ['lookup'], on: [() => xtremStructure.nodes.Legislation] },
    { operations: ['searchStock'], on: [() => xtremStockData.nodes.Stock] },
    { operations: ['lookup'], on: [() => xtremMasterData.nodes.LocationZone] },
    { operations: ['lookup'], on: [() => xtremMasterData.nodes.SequenceNumber] },
    ...xtremFinanceData.functions.dimensionsAndAttributesOperations,
    { operations: ['lookup'], on: [() => xtremUpload.nodes.AttachmentAssociation] },
    { operations: ['lookup'], on: [() => xtremStockData.nodes.StockMovementDetail] },
];
