import type * as xtremStockData from '@sage/xtrem-stock-data';
import * as xtremStock from '..';

export function calculateStockIssueLineDisplayStatus(
    stockDetailStatus: xtremStockData.enums.StockDetailStatus,
    stockTransactionStatus: xtremStockData.enums.StockDocumentTransactionStatus,
): xtremStock.enums.StockIssueLineDisplayStatus {
    if (stockTransactionStatus === 'completed') return 'issued';
    if (stockTransactionStatus === 'error') return 'stockPostingError';
    if (stockTransactionStatus === 'inProgress') return 'stockPostingInProgress';
    if (stockDetailStatus === 'required') return 'detailsRequired';
    return 'detailsEntered';
}

export function calculateStockIssueDisplayStatus(
    status: xtremStockData.enums.StockDocumentStatus,
    stockTransactionStatus: xtremStockData.enums.StockDocumentTransactionStatus,
    stockDetailStatus: string,
): xtremStock.enums.StockIssueDisplayStatus {
    if (status === 'closed') return 'issued';
    if (stockTransactionStatus === 'inProgress') return 'stockPostingInProgress';
    if (stockTransactionStatus === 'error') return 'stockPostingError';
    if (stockTransactionStatus === 'draft' && stockDetailStatus === 'required') return 'detailsRequired';
    return 'detailsEntered';
}

/**
 * Calculate the display status of the header in function of the stock status and the status header:
 * - status "closed" => issued
 * - stockTransactionStatus "inProgress" => stock posting in progress
 * - stockTransactionStatus "error" => stock posting error
 * - one line "required" => detail required
 * - all lines "entered" or "notRequired" => Detail completed
 * @param document
 * @returns {xtremStock.enums.stockIssueDisplayStatus} "detailsRequired" | "detailsEntered" | "stockPostingInProgress" | "stockPostingError" | "issued"
 */
export async function computeHeaderDisplayStatus(
    stockIssue: xtremStock.nodes.StockIssue,
): Promise<xtremStock.enums.StockIssueDisplayStatus> {
    const areLinesRequired = await stockIssue.lines.some(async line => (await line.stockDetailStatus) === 'required');
    return xtremStock.functions.stockIssueLib.calculateStockIssueDisplayStatus(
        await stockIssue.status,
        await stockIssue.stockTransactionStatus,
        areLinesRequired ? 'required' : 'entered',
    );
}
