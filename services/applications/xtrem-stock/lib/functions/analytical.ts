import type { Context } from '@sage/xtrem-core';
import * as xtremFinanceData from '@sage/xtrem-finance-data';
import type * as xtremMasterData from '@sage/xtrem-master-data';
import type * as xtremSystem from '@sage/xtrem-system';

export async function computeAttributes(
    context: Context,
    data: {
        stockSite: xtremSystem.nodes.Site;
        financialSite?: xtremSystem.nodes.Site;
        item: xtremMasterData.nodes.Item;
    },
): Promise<xtremFinanceData.interfaces.ComputedAttributes> {
    // For stock documents we must not provide a business site, so we have to get rid of it.
    // (In fact if we don't remove it journal entry creation will fail.)
    const { businessSite, ...attributes } = await xtremFinanceData.functions.computeGenericAttributes(context, {
        item: data.item,
        stockSite: data.stockSite,
        financialSite: data.financialSite,
    });

    return attributes;
}
