import type { Context } from '@sage/xtrem-core';
import { ValidationSeverity } from '@sage/xtrem-core';
import * as xtremFinanceData from '@sage/xtrem-finance-data';
import * as xtremStock from '../index';

/**
 * For a given stock value change, we run the controls for finance integration.
 * @param context: A context
 * @param valueChange The stock value change
 * @return A CreateFinanceDocumentsReturn object
 */
export async function stockValueChangeControlFromNotificationPayload(
    context: Context,
    valueChange: xtremStock.nodes.StockValueChange,
): Promise<xtremFinanceData.interfaces.CreateFinanceDocumentsReturn> {
    return xtremFinanceData.functions.FinanceDocumentGeneration.financeDownstreamDocumentControlFromNotificationPayload(
        context,
        await xtremFinanceData.functions.getStockNotificationPayload(
            valueChange as xtremFinanceData.interfaces.FinanceOriginDocument,
            await valueChange.lines.toArray(),
            'stockValueChange',
            'StockValueChangeLine',
        ),
    );
}

export async function stockValueChangeControlFromNotificationPayloadErrors(
    context: Context,
    valueChange: xtremStock.nodes.StockValueChange,
): Promise<xtremFinanceData.interfaces.MutationResult> {
    await context.flushDeferredActions();
    const valueChangeControlFromNotificationPayloadErrors = (
        await xtremStock.functions.financeIntegration.stockValueChangeControlFromNotificationPayload(
            context,
            valueChange,
        )
    ).validationMessages.filter(validationMessage => validationMessage.type === ValidationSeverity.error);

    const message = valueChangeControlFromNotificationPayloadErrors
        .map((errorMessage: xtremFinanceData.interfaces.ValidationMessage) => '* '.concat(errorMessage.message))
        .join('\n');

    return { wasSuccessful: message === '', message };
}
