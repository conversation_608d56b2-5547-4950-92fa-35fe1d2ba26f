import type * as xtremStockData from '@sage/xtrem-stock-data';
import * as xtremStock from '..';

export function calculateStockReceiptLineDisplayStatus(
    stockDetailStatus: xtremStockData.enums.StockDetailStatus,
    stockTransactionStatus: xtremStockData.enums.StockDocumentTransactionStatus,
): xtremStock.enums.StockReceiptLineDisplayStatus {
    if (stockTransactionStatus === 'completed') return 'received';
    if (stockTransactionStatus === 'error') return 'stockPostingError';
    if (stockTransactionStatus === 'inProgress') return 'stockPostingInProgress';
    if (stockDetailStatus === 'required') return 'detailsRequired';
    return 'detailsEntered';
}

export function calculateStockReceiptDisplayStatus(
    status: xtremStockData.enums.StockDocumentStatus,
    stockTransactionStatus: xtremStockData.enums.StockDocumentTransactionStatus,
    stockDetailStatus: string,
): xtremStock.enums.StockReceiptDisplayStatus {
    if (status === 'closed') return 'received';
    if (stockTransactionStatus === 'inProgress') return 'stockPostingInProgress';
    if (stockTransactionStatus === 'error') return 'stockPostingError';
    if (stockTransactionStatus === 'draft' && stockDetailStatus === 'required') return 'detailsRequired';
    return 'detailsEntered';
}

/**
 * Calculate the display status of the header in function of the stock status and the status header:
 * - status "closed" => received
 * - stockTransactionStatus "inProgress" => stock posting in progress
 * - stockTransactionStatus "error" => stock posting error
 * - one line "required" => detail required
 * - all lines "entered" => Detail completed
 * @param document
 * @returns {xtremStock.enums.stockReceiptDisplayStatus} "detailsRequired" | "detailsEntered" | "stockPostingInProgress" | "stockPostingError" | "received"
 */
export async function computeHeaderDisplayStatus(
    stockReceipt: xtremStock.nodes.StockReceipt,
): Promise<xtremStock.enums.StockReceiptDisplayStatus> {
    let lineStatusComputed = '';
    const requiredLine =
        (await stockReceipt.lines.length) === 0 ||
        (await stockReceipt.lines.some(async line => (await line.stockDetailStatus) === 'required'));
    if (requiredLine) {
        lineStatusComputed = 'required';
    } else {
        lineStatusComputed = 'entered';
    }
    return xtremStock.functions.stockReceiptLib.calculateStockReceiptDisplayStatus(
        await stockReceipt.status,
        await stockReceipt.stockTransactionStatus,
        lineStatusComputed,
    );
}
