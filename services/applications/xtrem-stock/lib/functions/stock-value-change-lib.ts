import type { Context } from '@sage/xtrem-core';
import { BusinessRuleError } from '@sage/xtrem-core';
import type * as xtremFinanceData from '@sage/xtrem-finance-data';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremStock from '..';

export async function controlPost(context: Context, document: xtremStock.nodes.StockValueChange) {
    const postResult: xtremFinanceData.interfaces.MutationResult =
        await xtremStock.functions.financeIntegration.stockValueChangeControlFromNotificationPayloadErrors(
            context,
            document,
        );
    if (postResult.message.length) {
        throw new BusinessRuleError(postResult.message);
    }
    if ((await document.valuationMethod) === 'fifoCost') {
        await document.lines.forEach(async (line, index1) => {
            const fifoDocument = await line.fifoCost;
            if ((await fifoDocument?.remainingQuantity) !== (await line.quantity)) {
                throw new BusinessRuleError(
                    context.localize(
                        '@sage/xtrem-stock/nodes__stock_value_change__fifo_cost_quantity_cost_amount_has_changed',
                        `The FIFO stack record corresponding to line with effective date: {{date}}   and sequence {{sequence}}  has changed. Recreate the stock value change line.`,
                        { date: await fifoDocument?.effectiveDate, sequence: await fifoDocument?.sequence },
                    ),
                );
            }
            if ((await line.amount) === (await line.newAmount)) {
                throw new BusinessRuleError(
                    context.localize(
                        '@sage/xtrem-stock/nodes__stock_value_change__amount_newAmount_fifo_cost',
                        `Line {{lineNumber}}: the current and new amounts cannot be the same. Enter a different value in the new amount.`,
                        { lineNumber: index1 + 1 },
                    ),
                );
            }
        });
    } else if ((await document.valuationMethod) === 'averageCost') {
        const itemSite = await context.read(xtremMasterData.nodes.ItemSite, {
            item: await document.item,
            site: await document.site,
        });
        const line = await document.lines.elementAt(0);
        const lineAmount = await line.amount;
        if (
            (await itemSite.inStockQuantity) !== (await line.quantity) ||
            (await itemSite.stockValuationAtAverageCost) !== lineAmount
        ) {
            throw new BusinessRuleError(
                context.localize(
                    '@sage/xtrem-stock/nodes__stock_value_change__average_cost_quantity_cost_amount_has_changed',
                    'The average cost corresponding to line 1 has changed. Recreate the stock value change line.',
                ),
            );
        }

        if (lineAmount === (await line.newAmount)) {
            throw new BusinessRuleError(
                context.localize(
                    '@sage/xtrem-stock/nodes__stock_value_change__amount_newAmount_average_cost',
                    'Line 1: the current and new amounts cannot be the same. Enter a different value in the new amount.',
                ),
            );
        }
    }
}
