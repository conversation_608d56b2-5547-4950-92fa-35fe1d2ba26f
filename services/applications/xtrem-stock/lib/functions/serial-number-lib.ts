import type { Context } from '@sage/xtrem-core';
import { asyncArray } from '@sage/xtrem-core';
import type { decimal, integer } from '@sage/xtrem-shared';
import * as xtremStockData from '@sage/xtrem-stock-data';

export async function getSerialNumberFromStartingSerialNumber(
    context: Context,
    startingSerialNumber: xtremStockData.nodes.SerialNumber,
    after: integer,
): Promise<xtremStockData.nodes.SerialNumber> {
    // eslint-disable-next-line @sage/redos/no-vulnerable
    const serialNumberID = Number((await startingSerialNumber.id).match(/[0-9]*$/)![0]) + after;
    // Should use the following regex without redos
    // const serialNumberID = Number((await startingSerialNumber.id).match(/^[0-9]*$/)![0]) + after;
    return context.read(xtremStockData.nodes.SerialNumber, {
        item: await startingSerialNumber.item,
        id: serialNumberID,
    });
}

export function getSerialNumbersForStockDetail(
    context: Context,
    startingSerialNumber: xtremStockData.nodes.SerialNumber,
    quantityInStockUnit: decimal,
): Promise<xtremStockData.nodes.SerialNumber[]> {
    return asyncArray(new Array(quantityInStockUnit))
        .map((value, i) => {
            if (i === 0) {
                return startingSerialNumber;
            }
            return getSerialNumberFromStartingSerialNumber(context, startingSerialNumber, i);
        })
        .toArray();
}
