import type { AnyValue, Context, integer, NodeCreateData, NodePayloadData, NodeUpdateData } from '@sage/xtrem-core';
import { asyncArray, BusinessRuleError, Logger, SystemError } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import type { decimal } from '@sage/xtrem-shared';
import { DataInputError, LogicError } from '@sage/xtrem-shared';
import * as xtremStockData from '@sage/xtrem-stock-data';
import { isNumber } from 'lodash';
import * as xtremStock from '..';

export const logger = Logger.getLogger(__filename, 'stock-engine');

export type OriginStockDetails =
    | xtremStockData.nodes.StockChangeDetail
    | xtremStockData.nodes.StockIssueDetail
    | xtremStockData.nodes.StockReceiptDetail
    | xtremStockData.nodes.StockAdjustmentDetail;
export type AllStockDetails = OriginStockDetails | xtremStockData.nodes.StockCorrectionDetail;

/**
 *
 */
export async function getCorrectionOriginalDetails(
    stockDetail: xtremStockData.nodes.StockMovementDetail,
): Promise<xtremStockData.nodes.StockMovementDetail> {
    if (stockDetail instanceof xtremStockData.nodes.StockCorrectionDetail) {
        return getCorrectionOriginalDetails((await stockDetail.correctedStockDetail) as unknown as AllStockDetails);
    }
    return stockDetail;
}

export async function sumCorrectionOriginalDetailsQuantity(
    stockDetail: xtremStockData.nodes.StockMovementDetail,
    accumulator: decimal = 0,
): Promise<decimal> {
    if (stockDetail instanceof xtremStockData.nodes.StockCorrectionDetail) {
        const correctedDetail = await stockDetail.correctedStockDetail;
        return sumCorrectionOriginalDetailsQuantity(
            correctedDetail,
            accumulator + (await stockDetail.quantityInStockUnit),
        );
    }
    return accumulator + (await stockDetail.quantityInStockUnit);
}

export async function get(
    stockDetail: xtremStockData.nodes.StockMovementDetail,
): Promise<xtremStockData.nodes.StockMovementDetail> {
    if (stockDetail instanceof xtremStockData.nodes.StockCorrectionDetail)
        return getCorrectionOriginalDetails((await stockDetail.correctedStockDetail) as unknown as AllStockDetails);
    return stockDetail;
}

export function ensureAtLeastOneStockDetail(context: Context, stockDetails: xtremStockData.nodes.BaseStockDetail[]) {
    if (stockDetails.length === 0) {
        throw new BusinessRuleError(
            context.localize(
                '@sage/xtrem-stock/functions__stock-engine__not_enough_stock_details',
                'You must pass at least one stock detail',
            ),
        );
    }
}

function isDoubleMovementUpdate(
    stockUpdateData: xtremStock.interfaces.StockEngineParameters<xtremStockData.enums.StockMovementTypeEnum>,
): stockUpdateData is
    | xtremStock.interfaces.StockEngineParameters<xtremStockData.enums.StockMovementTypeEnum.change>
    | xtremStock.interfaces.StockEngineParameters<xtremStockData.enums.StockMovementTypeEnum.transfer> {
    return (
        xtremStock.functions.typingLib.isThisStockUpdateDataOfStockChange(stockUpdateData) ||
        xtremStock.functions.typingLib.isThisStockUpdateDataOfStockTransfer(stockUpdateData)
    );
}

// Stock lot refactoring: if stockDetail has a StockDetailLot reference:
// create a new lot from the stockReceiptLot linked to the stock detail if no lot is linked yet
async function _handleLot(
    context: Context,
    stockDetail: xtremStockData.nodes.StockReceiptDetail | xtremStockData.nodes.StockAdjustmentDetail,
): Promise<xtremStockData.interfaces.LotUpdateReturn | null> {
    const stockDetailLot = await stockDetail.stockDetailLot;
    const isNotLotManaged = (await (await stockDetail.item).lotManagement) === 'notManaged';

    // If the item is lot managed and we don't have a reference to a lot, we'll need to get that reference or create a new lot
    if (isNotLotManaged && stockDetailLot) {
        throw new BusinessRuleError(
            context.localize(
                '@sage/xtrem-stock/functions__stock-engine__lot_passed_for_an_item_not_lot_manage',
                'The item {{item}} is not lot-managed. You can only post a stock detail with lot information for an item that is lot-managed.',
                { item: await (await stockDetail.item).id },
            ),
        );
    }

    let lotUpdateReturn: xtremStockData.interfaces.LotUpdateReturn = {
        lotCreated: false,
        lotRecord: (await stockDetailLot?.lot) ?? null,
    };

    if (isNotLotManaged || (await stockDetailLot?.lot)) return lotUpdateReturn;

    if (!stockDetailLot)
        throw new BusinessRuleError(
            context.localize(
                '@sage/xtrem-stock/functions__stock_engine__no_lot_information_received',
                'No lot information received.',
            ),
        );

    const existingLot = await context.tryRead(xtremStockData.nodes.Lot, {
        item: (await stockDetail.item)._id,
        id: await stockDetailLot.lotNumber,
        sublot: await stockDetailLot.sublot,
    });

    if (existingLot) {
        lotUpdateReturn.lotCreated = true;
        lotUpdateReturn.lotRecord = existingLot;
    } else {
        lotUpdateReturn = await xtremStock.functions.lotLib.createLot(context, stockDetail);
    }

    await stockDetail?.$.set({
        stockDetailLot: { canUpdateLot: true, lot: lotUpdateReturn.lotRecord, lotNumber: '' },
    });

    await stockDetail.$.save();

    lotUpdateReturn.updatedStockDetail = stockDetail;

    return lotUpdateReturn;
}

async function _prepareStockJournalCreateData(
    stockUpdateResult: xtremStockData.interfaces.StockUpdateReturn,
    stockUpdateData: xtremStock.interfaces.StockEngineParameters<xtremStockData.enums.StockMovementTypeEnum>,
) {
    // Prepare data to create the record on the stock journal
    const stockJournalCreateData: NodeCreateData<xtremStockData.nodes.StockJournal> = stockUpdateResult.stockRecord
        ? {
              item: await stockUpdateResult.stockRecord.item,
              status: await stockUpdateResult.stockRecord.status,
              location: (await stockUpdateResult.stockRecord.location) || undefined,
              stockUnit: await stockUpdateResult.stockRecord.stockUnit,
              owner: await stockUpdateResult.stockRecord.owner,
              site: await stockUpdateResult.stockRecord.site,
              lot: (await stockUpdateResult.stockRecord.lot) || null,
          }
        : {
              item: stockUpdateData.searchData.item,
              status: stockUpdateData.searchData.status,
              location: stockUpdateData.searchData.location || undefined,
              stockUnit: stockUpdateData.searchData.stockUnit,
              owner: stockUpdateData.searchData.owner,
              site: stockUpdateData.searchData.site,
              lot: stockUpdateData.searchData.lot || null,
          };

    // Alert if stock journal status is null/undefined for stock quantity movements.
    if (
        !stockJournalCreateData.status &&
        stockUpdateData.stockDetail instanceof xtremStockData.nodes.StockMovementDetail
    ) {
        throw new LogicError(
            `A stock journal must be provided for quantity movement ${JSON.stringify(
                stockUpdateData.stockDetail,
                null,
                4,
            )}`,
        );
    }

    stockJournalCreateData.movementType = stockUpdateData.stockUpdateParameters.movementType;

    return stockJournalCreateData;
}

async function _getCostWithoutValuationManager(
    stockUpdateData: xtremStock.interfaces.StockEngineParameters<xtremStockData.enums.StockMovementTypeEnum>,
    itemSite: xtremMasterData.nodes.ItemSite,
    qty: number,
) {
    let nonAbsorbedAmount = 0;
    let cost = 0;
    let valuedCost = 0;
    let orderCost = 0;
    let orderAmount: number | undefined;
    let movementAmount: number | undefined;
    let sourceDocumentLine: xtremMasterData.nodes.BaseDocumentLine | undefined;

    logger.verbose(() => 'do not use stockValuationManager');
    if (stockUpdateData.stockDetail instanceof xtremStockData.nodes.StockMovementDetail) {
        valuedCost = await stockUpdateData.stockDetail.valuedCost;
        orderCost = await stockUpdateData.stockDetail.orderCost;
        cost = qty < 0 ? await stockUpdateData.stockDetail.valuedCost : await stockUpdateData.stockDetail.orderCost;
    }

    // Compute the order amount only for value correction otherwise the default value will be applied
    if (xtremStock.functions.typingLib.isThisStockUpdateDataOfStockCorrection(stockUpdateData)) {
        const detailsQty = await sumCorrectionOriginalDetailsQuantity(stockUpdateData.stockDetail);
        if ((await itemSite.valuationMethod) === 'averageCost') {
            orderAmount = detailsQty * cost;
            movementAmount = orderAmount;
        } else {
            orderAmount = detailsQty * orderCost;
            movementAmount = 0;
            valuedCost = 0;
        }

        // For a purchase invoice posting, stockDetail.originDocumentLine = invoice line
        sourceDocumentLine = (await stockUpdateData.stockDetail.originDocumentLine) || undefined;

        nonAbsorbedAmount = await stockUpdateData.stockDetail.nonAbsorbedAmount;
        if ((await itemSite.valuationMethod) === 'standardCost') {
            orderCost =
                nonAbsorbedAmount /
                (await (
                    await stockUpdateData.stockDetail.correctedStockDetail
                ).quantityInStockUnit);
        }
    }

    const documentLine = (await stockUpdateData.stockDetail
        .documentLine) as xtremStockData.interfaces.StockDocumentLine;
    const valuationParameters = await documentLine.getValuationParameters();

    if (
        xtremStock.functions.typingLib.isThisStockUpdateDataOfStockReceipt(stockUpdateData) ||
        (qty > 0 && xtremStock.functions.typingLib.isThisStockUpdateDataOfStockAdjustment(stockUpdateData)) ||
        (xtremStock.functions.typingLib.isThisStockUpdateDataOfStockTransfer(stockUpdateData) &&
            valuationParameters.valuationType === 'receipt')
    ) {
        if ((await itemSite.valuationMethod) === 'standardCost') {
            nonAbsorbedAmount = (orderCost - valuedCost) * qty;
        } else {
            valuedCost = await stockUpdateData.stockDetail.orderCost;
        }
    }

    logger.verbose(() => `orderCost=${orderCost} valuedCost=${valuedCost} nonAbsorbedAmount=${nonAbsorbedAmount}`);

    return { valuedCost, orderCost, nonAbsorbedAmount, orderAmount, sourceDocumentLine, movementAmount };
}

async function _setStockJournalCreateData(
    context: Context,
    stockUpdateResult: xtremStockData.interfaces.StockUpdateReturn,
    stockUpdateData: xtremStock.interfaces.StockEngineParameters<xtremStockData.enums.StockMovementTypeEnum>,
): Promise<NodeCreateData<xtremStockData.nodes.StockJournal>> {
    // Prepare data to create the record on the stock journal
    const stockJournalCreateData = await _prepareStockJournalCreateData(stockUpdateResult, stockUpdateData);

    const qty = await stockUpdateData.stockDetail.quantityInStockUnit;
    const itemSite = await context.read(xtremMasterData.nodes.ItemSite, {
        item: stockUpdateData.searchData.item,
        site: stockUpdateData.searchData.site,
    });

    let nonAbsorbedAmount = 0;
    let valuedCost = 0;
    let orderCost = 0;

    if (
        xtremStock.functions.typingLib.isThisStockUpdateDataOfStockAdjustment(stockUpdateData) ||
        xtremStock.functions.typingLib.isThisStockUpdateDataOfStockCorrection(stockUpdateData)
    ) {
        stockJournalCreateData.reasonCode = await stockUpdateData.stockDetail.reasonCode;
    }

    if (stockUpdateData.stockUpdateParameters.stockValuationManager) {
        logger.verbose(() => 'use stockValuationManager');
        const detailToUse = xtremStock.functions.typingLib.isThisStockUpdateDataOfStockCorrection(stockUpdateData)
            ? await (stockUpdateData.stockDetail as xtremStockData.nodes.StockCorrectionDetail).correctedStockDetail
            : stockUpdateData.stockDetail;
        const quantity = await detailToUse.quantityInStockUnit;
        const documentLine = await detailToUse.documentLine;
        stockJournalCreateData.orderAmount = stockUpdateData.stockUpdateParameters.stockValuationManager.getOrderAmount(
            documentLine,
            quantity,
            stockUpdateData.stockDetail._id,
        );
        stockJournalCreateData.movementAmount =
            stockUpdateData.stockUpdateParameters.stockValuationManager.getValuedAmount(
                documentLine,
                quantity,
                stockUpdateData.stockDetail._id,
            );
        nonAbsorbedAmount = stockUpdateData.stockUpdateParameters.stockValuationManager.getNonAbsorbedAmount(
            documentLine,
            quantity,
            stockUpdateData.stockDetail._id,
        );

        if (xtremStock.functions.typingLib.isThisStockUpdateDataOfStockCorrection(stockUpdateData)) {
            // For a purchase invoice posting, stockDetail.originDocumentLine = invoice line
            stockJournalCreateData.sourceDocumentLine =
                (await stockUpdateData.stockDetail.originDocumentLine) || undefined;
        }

        orderCost = stockJournalCreateData.orderAmount / quantity;
        valuedCost = stockJournalCreateData.movementAmount / quantity;
    } else {
        const cost = await _getCostWithoutValuationManager(stockUpdateData, itemSite, qty);
        orderCost = cost.orderCost;
        valuedCost = cost.valuedCost;
        nonAbsorbedAmount = cost.nonAbsorbedAmount;

        stockJournalCreateData.orderAmount = cost.orderAmount ?? stockJournalCreateData.orderAmount;
        stockJournalCreateData.sourceDocumentLine =
            cost.sourceDocumentLine ?? stockJournalCreateData.sourceDocumentLine;
        stockJournalCreateData.movementAmount = cost.movementAmount ?? stockJournalCreateData.movementAmount;
    }

    logger.verbose(() => `orderCost=${orderCost} valuedCost=${valuedCost} nonAbsorbedAmount=${nonAbsorbedAmount}`);
    return {
        ...stockJournalCreateData,
        quantityInStockUnit: !xtremStock.functions.typingLib.isThisStockUpdateDataOfStockValueChange(stockUpdateData)
            ? await stockUpdateData.stockDetail.quantityInStockUnit
            : 0,
        effectiveDate:
            stockUpdateData.stockDetail instanceof xtremStockData.nodes.StockMovementDetail
                ? await stockUpdateData.stockDetail.effectiveDate
                : undefined,
        stockDetail: stockUpdateData.stockDetail,
        orderCost,
        valuedCost,
        nonAbsorbedAmount,
    };
}

/**
 * [Internal] Take the stock information and the stock record and update the assigned serial numbers
 * @ignore
 * @param context
 * @param stockUpdateData (has to fit the StockUpdateData interface)
 * @param  stockRecord: stock record if it still exists after the update
 * For a stock receipt, update serial numbers with the stock record to be created/updated
 *       and baseDocumentLine to indicate the last document line that entered serial numbers into the stock
 * For a stock issue, update serial numbers to indicate they are no longer on stock
 *       and baseDocumentLine to indicate the last document line that issued serial numbers from the stock
 */
async function _serialNumberUpdate(
    context: Context,
    stockUpdateData: xtremStock.interfaces.StockEngineParameters<xtremStockData.enums.StockMovementTypeEnum>,
    stockRecord: xtremStockData.nodes.Stock | null,
    resultAction: xtremStockData.enums.StockUpdateResultAction,
): Promise<boolean> {
    // Make sure only stockUpdateData capable of using serial numbers are processed.
    // Note: Return value is true if not because all serial numbers are processed (if there isn't any), of course.
    if (!(stockUpdateData.stockDetail instanceof xtremStockData.nodes.StockMovementDetail)) {
        throw new LogicError(
            `stockUpdateData.stockDetail is not an instance of StockMovementDetail ${JSON.stringify(
                stockUpdateData.stockDetail,
                null,
                4,
            )}`,
        );
    }

    let serialNumberUpdated = true;
    let updateType: string = stockUpdateData.stockUpdateParameters.movementType;
    if (
        updateType === 'issue' &&
        (await (stockUpdateData.stockDetail as xtremStockData.nodes.StockMovementDetail).stockDetailSerialNumbers
            .length)
    ) {
        const item = await context.read(xtremMasterData.nodes.Item, {
            _id: isNumber(stockUpdateData.searchData.item)
                ? stockUpdateData.searchData.item
                : `#${await stockUpdateData.searchData.item.id}`,
        });
        updateType = await item.serialNumberUsage;
    }

    let documentLine = await stockUpdateData.stockDetail.documentLine;
    // The user needs in the case of production tracking or material tracking the serialNumbers.baseDocumentLine to be
    // populated with the workOrderReleasedItem instead of the production/material tracking line
    // there is a non mandatory function "getWorkOrderDocumentLine" in the interface "DocumentLineWithStockPosting"
    // which is implemented only in production and material tracking line nodes.
    // If this function exists on documentLine, we get the baseDocumentLine for the serial numbers by calling
    // this function (will return the productionItem of the work order for production and material tracking)
    // otherwise we return the documentLine (all other nodes except production / material tracking)
    const line = (await context.read(xtremMasterData.nodes.BaseDocumentLine, {
        _id: documentLine._id,
    })) as xtremStockData.interfaces.DocumentLineWithStockPosting & Node;
    if (line?.getWorkOrderDocumentLine && line?.getWorkOrderDocumentLine()) {
        documentLine = (await line?.getWorkOrderDocumentLine()) as xtremMasterData.nodes.BaseDocumentLine;
    }

    await (stockUpdateData.stockDetail as xtremStockData.nodes.StockMovementDetail).stockDetailSerialNumbers.forEach(
        async stockDetailSerialNumber => {
            if (
                serialNumberUpdated &&
                (xtremStock.functions.typingLib.isThisStockUpdateDataOfStockReceipt(stockUpdateData) ||
                    xtremStock.functions.typingLib.isThisStockUpdateDataOfStockIssue(stockUpdateData) ||
                    xtremStock.functions.typingLib.isThisStockUpdateDataOfStockChange(stockUpdateData) ||
                    xtremStock.functions.typingLib.isThisStockUpdateDataOfStockAdjustment(stockUpdateData) ||
                    xtremStock.functions.typingLib.isThisStockUpdateDataOfStockTransfer(stockUpdateData))
            ) {
                const serialNumber = await context.read(
                    xtremStockData.nodes.SerialNumber,
                    {
                        _id: (await stockDetailSerialNumber.serialNumber)?._id,
                    },
                    { forUpdate: true },
                );

                const quantity = await stockUpdateData.stockDetail.quantityInStockUnit;

                switch (updateType) {
                    case 'change':
                        await serialNumber.$.set({ stockRecord, isUsable: true });
                        break;
                    case 'receipt':
                        if (await serialNumber.isInStock) {
                            serialNumberUpdated = false;
                        } else {
                            await serialNumber.$.set({
                                stockRecord,
                                isUsable: true,
                                baseDocumentLine: documentLine,
                                createDate: await stockUpdateData.stockDetail.effectiveDate,
                            });
                        }
                        break;
                    case 'transfer':
                        if (stockRecord && resultAction !== 'deleted') {
                            if (
                                quantity > 0 &&
                                xtremStock.functions.typingLib.isThisStockUpdateDataOfStockTransfer(stockUpdateData) &&
                                stockUpdateData.stockUpdateParameters.intersiteTransferData.type === 'internalShipment'
                            ) {
                                await serialNumber.$.set({
                                    stockRecord: null,
                                    baseDocumentLine: documentLine,
                                });
                            }
                            if (
                                quantity > 0 &&
                                xtremStock.functions.typingLib.isThisStockUpdateDataOfStockTransfer(stockUpdateData) &&
                                stockUpdateData.stockUpdateParameters.intersiteTransferData.type === 'internalReceipt'
                            ) {
                                await serialNumber.$.set({
                                    stockRecord: stockRecord._id,
                                    baseDocumentLine: documentLine,
                                    site: (await stockRecord?.site)?._id,
                                });
                            }
                        }

                        break;
                    case 'issueAndReceipt':
                        await serialNumber.$.set({ stockRecord: null, baseDocumentLine: documentLine });
                        break;
                    case 'issueOnly':
                        await serialNumber.$.set({
                            stockRecord: null,
                            baseDocumentLine: documentLine,
                            isUsable: true,
                            createDate: await stockUpdateData.stockDetail.effectiveDate,
                        });
                        break;
                    case 'adjustment':
                        // an adjustment can either be an increase like a receipt or a decrease like an issue and must be
                        // treated accordingly
                        if (['increased', 'created'].includes(resultAction)) {
                            // same properties as receipt
                            if (await serialNumber.isInStock) {
                                serialNumberUpdated = false;
                            } else {
                                await serialNumber.$.set({
                                    stockRecord,
                                    isUsable: true,
                                    baseDocumentLine: documentLine,
                                    createDate: await stockUpdateData.stockDetail.effectiveDate,
                                });
                            }
                        } else {
                            // same properties as issue
                            await serialNumber.$.set({ stockRecord: null, baseDocumentLine: documentLine });
                        }
                        break;
                    default:
                        await serialNumber.$.set({ stockRecord, baseDocumentLine: documentLine });
                }
                if (serialNumberUpdated) await serialNumber.$.save();
            }
        },
    );
    return serialNumberUpdated;
}

async function deleteStockRecord(
    stockUpdateData: xtremStock.interfaces.StockEngineParameters<xtremStockData.enums.StockMovementTypeEnum>,
    stockRecord: xtremStockData.nodes.Stock,
) {
    // This will delete the stock record
    await stockRecord.$.delete();

    return {
        stockRecord: null,
        resultAction: 'deleted' as xtremStockData.enums.StockUpdateResultAction,
    };
}

/**
 * [Internal] Take the stock information and update/delete/create the Stock record according to the quantity passed in parameter.
 * @ignore
 * @param context
 * @param args (have to fit the StockUpdateData interface)
 * @returns an object having:
 *  stockUpdateReturn: the action done,
 *  stockRecord: stock record if it still exists after the update
 */
async function _stockUpdate(
    context: Context,
    stockUpdateData: xtremStock.interfaces.StockEngineParameters<xtremStockData.enums.StockMovementTypeEnum>,
): Promise<xtremStockData.interfaces.StockUpdateReturn> {
    let resultAction: xtremStockData.enums.StockUpdateResultAction = 'none';
    let updateMessage: string = '';
    let { stockRecord } = stockUpdateData;

    const quantity = await stockUpdateData.stockDetail.quantityInStockUnit;

    if (stockRecord) {
        if ((await stockRecord.quantityInStockUnit) === -(await stockUpdateData.stockDetail.quantityInStockUnit)) {
            if (
                xtremStock.functions.typingLib.isThisStockUpdateDataOfStockTransfer(stockUpdateData) &&
                stockUpdateData.stockUpdateParameters.intersiteTransferData.type === 'internalShipment'
            ) {
                await stockRecord.$.set({ isBeingDeletedByStockReceiptTransfer: true });
            }

            const deletionResult = await deleteStockRecord(stockUpdateData, stockRecord);
            stockRecord = deletionResult.stockRecord;
            resultAction = deletionResult.resultAction;
        } else if ((await stockRecord.quantityInStockUnit) < -(await stockUpdateData.stockDetail.quantityInStockUnit)) {
            // We don't have enough stock to issue
            updateMessage = context.localize(
                '@sage/xtrem-stock/functions__stock-engine__not_enough_stock',
                'There is not enough stock. Please check your parameters:\nItem {{item}}\nSite {{site}}\nStock unit {{stockUnit}}\nLot {{lot}}\nLocation {{location}}\nStock status {{stockStatus}}\nOwner {{owner}}',
                {
                    item: isNumber(stockUpdateData.searchData.item)
                        ? stockUpdateData.searchData.item
                        : await stockUpdateData.searchData.item.id,
                    site: isNumber(stockUpdateData.searchData.site)
                        ? stockUpdateData.searchData.site
                        : await stockUpdateData.searchData.site.id,
                    stockUnit: isNumber(stockUpdateData.searchData.stockUnit)
                        ? stockUpdateData.searchData.stockUnit
                        : await stockUpdateData.searchData.stockUnit.id,
                    lot: isNumber(stockUpdateData.searchData.lot)
                        ? stockUpdateData.searchData.lot
                        : await stockUpdateData.searchData.lot?.id,
                    location: isNumber(stockUpdateData.searchData.location)
                        ? stockUpdateData.searchData.location
                        : await stockUpdateData.searchData.location?.id,
                    stockStatus: isNumber(stockUpdateData.searchData.status)
                        ? stockUpdateData.searchData.status
                        : await stockUpdateData.searchData.status?.id,
                    owner: stockUpdateData.searchData.owner,
                },
            );
        } else {
            // This will update the stock record's quantity
            await stockRecord.$.set({
                quantityInStockUnit:
                    +(await stockRecord.quantityInStockUnit) + +(await stockUpdateData.stockDetail.quantityInStockUnit),
            });
            resultAction = (await stockUpdateData.stockDetail.quantityInStockUnit) > 0 ? 'increased' : 'decreased';
        }
    } else if (quantity >= 0) {
        // This will create a new stock record
        const stockRecordToCreate = {
            ...stockUpdateData.searchData,
            quantityInStockUnit: await stockUpdateData.stockDetail.quantityInStockUnit,
        };
        logger.verbose(() => `Stock record to create: ${JSON.stringify(stockRecordToCreate, null, 4)}`);
        stockRecord = await context.create(xtremStockData.nodes.Stock, stockRecordToCreate);
        resultAction = 'created';
    } else {
        logger.error(
            context.localize(
                '@sage/xtrem-stock/functions__stock__stock_record_not_found',
                'Stock record was not found, cannot take quantity.',
            ),
        );
    }

    if (stockRecord && resultAction !== 'deleted') {
        if (
            quantity > 0 &&
            xtremStock.functions.typingLib.isThisStockUpdateDataOfStockTransfer(stockUpdateData) &&
            stockUpdateData.stockUpdateParameters.intersiteTransferData.type === 'internalShipment'
        ) {
            // Update the stock record's status
            await stockRecord.$.set({
                isInTransit: true,
            });
        }

        await stockRecord.$.save();
    }

    // XT-32492 | XT-33346
    // Update serial numbers with the stock record to be created/updated.
    // Note: Serial numbers are automatically unassigned on stock record deletion.
    // Note: Reset baseDocumentLine even when stock issue has just deleted stock record.
    if (await context.isServiceOptionEnabled(xtremMasterData.serviceOptions.serialNumberOption)) {
        if (!(await _serialNumberUpdate(context, stockUpdateData, stockRecord, resultAction))) {
            resultAction = 'none';
            updateMessage = context.localize(
                '@sage/xtrem-stock/functions__stock-engine__serial_number_already_in_stock',
                'One of the serial numbers used on item {{item}} is already in stock. Check your serial numbers.',
                {
                    item: isNumber(stockUpdateData.searchData.item)
                        ? stockUpdateData.searchData.item
                        : await stockUpdateData.searchData.item.id,
                },
            );
        }
    }
    return { resultAction, errorMessage: updateMessage, stockRecord };
}

async function stockChangeFlipFlop<T extends AnyValue>(
    context: Context,
    stockUpdateData: xtremStock.interfaces.StockEngineParameters<xtremStockData.enums.StockMovementTypeEnum>,
    stockDetail: xtremStockData.nodes.StockChangeDetail,
    stockRecordIncreaseCallback: (stockDetail: xtremStockData.nodes.StockChangeDetail) => Promise<T>,
    stockRecordDecreaseCallback: (stockDetail: xtremStockData.nodes.StockChangeDetail) => Promise<T>,
): Promise<T[]> {
    await logger.verboseAsync(
        async () =>
            `stockChangeFlipFlop stockDetail before : ${JSON.stringify({ stockDetailPayload: await stockDetail.$.payload({ withIds: true }), stockDetailNodeStatus: stockDetail.$.status }, undefined, 4)}`,
    );

    const returns = new Array<T>();

    const writableDetail = await context.read(
        xtremStockData.nodes.StockChangeDetail,
        { _id: stockDetail._id },
        { forUpdate: true },
    );

    await logger.verboseAsync(
        async () =>
            `stockChangeFlipFlop stockDetail after writeable read : ${JSON.stringify({ stockDetailPayload: await writableDetail.$.payload({ withIds: true }), stockDetailNodeStatus: writableDetail.$.status }, undefined, 4)}`,
    );

    const originalStockUnit = await writableDetail.stockUnit;
    const originalStatus = await writableDetail.status;
    const originalLocation = await writableDetail.location;
    const originalSite = await writableDetail.site;
    const originalOwner = await writableDetail.owner;

    let stockData: NodePayloadData<xtremStockData.nodes.Stock>;

    const stockRecordFromStockDetail = await writableDetail.stockRecord;
    const additionalUpdates: NodeUpdateData<xtremStockData.nodes.StockChangeDetail> = {};

    if (xtremStock.functions.typingLib.isThisStockUpdateDataOfStockTransfer(stockUpdateData)) {
        additionalUpdates.canUpdateSite = true;
    }

    if (!stockRecordFromStockDetail || xtremMasterData.functions.nodeIsBeingDeleted(stockRecordFromStockDetail)) {
        const deletedStockRecordData = await writableDetail.deletedStockRecordData;

        logger.verbose(
            () =>
                `stockChangeFlipFlop StockRecord deleted since entry, deletedStockRecordData: ${JSON.stringify(deletedStockRecordData, undefined, 4)}`,
        );

        if (!deletedStockRecordData) {
            throw new SystemError("Stock engine wasn't expecting deletedStockRecordData to be null");
        }

        stockData = deletedStockRecordData;
    } else {
        await logger.verboseAsync(
            async () =>
                `stockChangeFlipFlop StockRecord didn't change since entry, stockRecordFromStockDetail: ${JSON.stringify(
                    await stockRecordFromStockDetail?.$.payload({ withIds: true }),
                    undefined,
                    4,
                )}`,
        );
        stockData = await stockRecordFromStockDetail.$.payload({ withIds: true });
    }

    const initialStockRecordWillBeDeleted = (await stockDetail.quantityInStockUnit) === stockData.quantityInStockUnit;

    await logger.verboseAsync(
        async () =>
            `stockChangeFlipFlop stockDetail after stock record deletion test : ${JSON.stringify({ stockDetailPayload: await writableDetail.$.payload({ withIds: true }), stockDetailNodeStatus: writableDetail.$.status }, undefined, 4)}`,
    );

    // flip the sign of the quantity
    await writableDetail.$.set({
        stockUnit: stockData.stockUnit,
        quantityInStockUnit: -(await writableDetail.quantityInStockUnit),
        status: stockData.status,
        location: stockData.location,
        site: stockData.site,
        owner: stockData.owner,
        ...additionalUpdates,
    });

    await logger.verboseAsync(
        async () =>
            `stockChangeFlipFlop stockDetail after flip $set : ${JSON.stringify({ stockDetailPayload: await writableDetail.$.payload({ withIds: true }), stockDetailNodeStatus: writableDetail.$.status }, undefined, 4)}`,
    );

    // call the first callback
    returns.push(await stockRecordIncreaseCallback(writableDetail));

    logger.verbose(
        () =>
            `stockChangeFlipFlop stockDetail after increase callback : ${JSON.stringify({ stockDetailNodeStatus: writableDetail.$.status }, undefined, 4)}`,
    );

    if (initialStockRecordWillBeDeleted) {
        additionalUpdates.stockRecord = null;
        additionalUpdates.deletedStockRecordData = stockData;
    }

    // flop the sign of the quantity
    await writableDetail.$.set({
        stockUnit: originalStockUnit,
        quantityInStockUnit: -(await writableDetail.quantityInStockUnit),
        status: originalStatus,
        location: originalLocation,
        site: originalSite,
        owner: originalOwner,
        isBeingUpdatedByStockEngine: true,
        ...additionalUpdates,
    });

    await logger.verboseAsync(
        async () =>
            `stockChangeFlipFlop stockDetail after flop $set : ${JSON.stringify({ stockDetailPayload: await writableDetail.$.payload({ withIds: true }), stockDetailNodeStatus: writableDetail.$.status }, undefined, 4)}`,
    );

    // call the second callback
    returns.push(await stockRecordDecreaseCallback(writableDetail));

    await logger.verboseAsync(
        async () =>
            `stockChangeFlipFlop stockDetail after decrease callback : ${JSON.stringify({ stockDetailPayload: await writableDetail.$.payload({ withIds: true }), stockDetailNodeStatus: writableDetail.$.status }, undefined, 4)}`,
    );

    await writableDetail.$.save();

    await logger.verboseAsync(
        async () =>
            `stockChangeFlipFlop stockDetail after $save : ${JSON.stringify({ stockDetailPayload: await writableDetail.$.payload({ withIds: true }), stockDetailNodeStatus: writableDetail.$.status }, undefined, 4)}`,
    );

    // return both results
    return returns;
}

async function _stockChange(
    context: Context,
    stockUpdateData: xtremStock.interfaces.StockEngineParameters<xtremStockData.enums.StockMovementTypeEnum>,
): Promise<xtremStockData.interfaces.StockUpdateReturn> {
    let resultAction: xtremStockData.enums.StockUpdateResultAction = 'none';
    let updateMessage: string = '';
    let { stockRecord } = stockUpdateData;

    const stockDetail = stockUpdateData.stockDetail as xtremStockData.nodes.StockChangeDetail;

    if (!stockRecord) {
        updateMessage = context.localize(
            '@sage/xtrem-stock/functions__stock__stock_record_not_found_during_stock_change',
            'Missing stock record found during stock change',
        );
    } else if ((await stockDetail.quantityInStockUnit) === 0) {
        updateMessage = context.localize(
            '@sage/xtrem-stock/functions__stock__stock_change_quantity_must_be_greater_thant_0',
            'You cannot create a stock change with a quantity of 0',
        );
    } else {
        if (xtremStock.functions.typingLib.isThisStockUpdateDataOfStockTransfer(stockUpdateData)) {
            resultAction = 'transferred';
        } else {
            resultAction = 'changed';
        }

        // TODO: try to avoid two updates on the stock table
        // by updating the existing stock record qty
        // or by updating the current stock record's attributes

        const quantityEqualAvailableStock =
            Math.abs(await stockDetail.quantityInStockUnit) ===
            (await (
                await stockDetail.stockRecord
            )?.quantityInStockUnit);
        const stockRecordPayload = await (await stockDetail.stockRecord)?.$.payload({ withIds: true });

        const flipFlopReturn = await stockChangeFlipFlop<xtremStockData.interfaces.StockUpdateReturn>(
            context,
            stockUpdateData,
            stockUpdateData.stockDetail as unknown as xtremStockData.nodes.StockChangeDetail,
            async (detail: xtremStockData.nodes.StockChangeDetail) => {
                if (
                    xtremStock.functions.typingLib.isThisStockUpdateDataOfStockTransfer(stockUpdateData) &&
                    stockUpdateData.stockUpdateParameters.intersiteTransferData.type === 'internalReceipt'
                ) {
                    const stockChangeDetail = detail as xtremStockData.nodes.StockChangeDetail;
                    const intersiteShipmentStockDetail = await stockChangeDetail.intersiteShipmentDetail;
                    if (intersiteShipmentStockDetail) {
                        const writableIntersiteShipmentStockDetail =
                            await xtremStock.functions.stockDetailLib.ensureWritableStockDetail(
                                context,
                                intersiteShipmentStockDetail,
                            );
                        await writableIntersiteShipmentStockDetail.$.set({
                            intersiteReceivedStockRecord: null,
                            isBeingUpdatedByStockEngine: true,
                        });
                        await writableIntersiteShipmentStockDetail.$.save();
                    }
                }
                const stockUpdateReturn = await _stockUpdate(context, { ...stockUpdateData, stockDetail: detail });
                logger.verbose(
                    () =>
                        `_stockChange StockRecord first movement (decreasing), engine return: ${JSON.stringify(stockUpdateReturn, undefined, 4)}`,
                );
                return stockUpdateReturn;
            },
            async detail => {
                const stockUpdateReturn = await _stockUpdate(context, {
                    ...stockUpdateData,
                    stockDetail: detail,
                    stockRecord: await xtremStockData.functions.stockLib.getStockRecord(
                        context,
                        stockUpdateData.searchData,
                    ),
                });
                logger.verbose(
                    () =>
                        `_stockChange second movement (increasing), engine return: ${JSON.stringify(stockUpdateReturn, undefined, 4)}`,
                );

                if (quantityEqualAvailableStock) {
                    await detail.$.set({
                        deletedStockRecordData: stockRecordPayload,
                        stockRecord: null,
                    });
                    await logger.verboseAsync(
                        async () =>
                            `_stockChange initial StockRecord deleted by first movement: ${JSON.stringify({ stockDetailPayload: await detail.$.payload({ withIds: true }), stockDetailNodeStatus: detail.$.status }, undefined, 4)}`,
                    );
                }

                if (
                    xtremStock.functions.typingLib.isThisStockUpdateDataOfStockTransfer(stockUpdateData) &&
                    stockUpdateData.stockUpdateParameters.intersiteTransferData.type === 'internalShipment'
                ) {
                    await detail.$.set({
                        intersiteReceivedStockRecord: stockUpdateReturn.stockRecord,
                    });
                }

                return stockUpdateReturn;
            },
        );
        stockRecord = flipFlopReturn[1].stockRecord;
    }
    return { resultAction, errorMessage: updateMessage, stockRecord };
}

/**
 * [Internal] Update the stock value on the ItemSite record according to the quantity and cost passed in the parameter.
 * @ignore
 * @param context
 * @param stockLineDetail (have to fit the StockLineDetail interface)
 * @returns a boolean if successful or not
 */
async function _stockValueUpdate(context: Context, stockMovement: xtremStockData.nodes.StockJournal): Promise<boolean> {
    logger.verbose(() => `_stockValueUpdate stockMovement=${JSON.stringify(stockMovement)}`);
    const itemSite = await context.tryRead(
        xtremMasterData.nodes.ItemSite,
        { item: (await stockMovement.item)._id, site: (await stockMovement.site)._id },
        { forUpdate: true },
    );
    if (itemSite) {
        if ((await itemSite.valuationMethod) === 'averageCost') {
            await logger.verboseAsync(
                async () => `_stockValueUpdate currentValue=${await itemSite.stockValuationAtAverageCost}`,
            );

            await itemSite.$.set({
                stockValuationAtAverageCost:
                    (await itemSite.stockValuationAtAverageCost) + (await stockMovement.orderAmount),
            });

            await itemSite.$.save();
            await logger.verboseAsync(
                async () => `_stockValueUpdate newValue=${await itemSite.stockValuationAtAverageCost}`,
            );
        }
    } else {
        return false;
    }
    return true;
}

/**
 * update the costs in the stock detail at posting time
 */
async function _updateStockDetailCost(
    context: Context,
    stockDetails: xtremStockData.nodes.StockMovementDetail[],
): Promise<void> {
    logger.verbose(() => `_manageCost stockDetail=${JSON.stringify(stockDetails)}`);
    await logger.verboseAsync(async () => `_manageCost currentValue=${await stockDetails[0].orderCost}`);

    // at this stage, all stockDetail are linked to the same documentLine
    const documentLine = (await stockDetails[0].documentLine) as xtremStockData.interfaces.StockDocumentLine;
    // Get cost to apply to all stockDetails of the same documentLine
    const orderCost = (await documentLine.getOrderCost()) || 0;
    const valuedCost = (await documentLine.getValuedCost()) || 0;

    await asyncArray(stockDetails).forEach(async stockDetail => {
        const detail = await xtremStock.functions.stockDetailLib.ensureWritableStockDetail(context, stockDetail);
        await detail.$.set({ orderCost, valuedCost });
        await detail.$.save();
    });
}

async function _updateStockJournal(
    context: Context,
    stockUpdateData: xtremStock.interfaces.StockEngineParameters<xtremStockData.enums.StockMovementTypeEnum>,
    stockUpdateResult: xtremStockData.interfaces.StockUpdateReturn,
    stockDetail: xtremStockData.nodes.BaseStockDetail,
): Promise<xtremStockData.nodes.StockJournal> {
    let stockJournalRecord: xtremStockData.nodes.StockJournal;
    let stockValueResult = false;

    // Update the stock-journal and value if average costing
    if (isDoubleMovementUpdate(stockUpdateData)) {
        let stockMovement: typeof stockJournalRecord;
        const flipFlopReturn = await stockChangeFlipFlop<boolean>(
            context,
            stockUpdateData,
            stockDetail as unknown as xtremStockData.nodes.StockChangeDetail,
            async (detail: xtremStockData.nodes.StockChangeDetail) => {
                if (
                    xtremStock.functions.typingLib.isThisStockUpdateDataOfStockTransfer(stockUpdateData) &&
                    stockUpdateData.stockUpdateParameters.intersiteTransferData.type === 'internalReceipt'
                ) {
                    // if the stock movement is an internal inter-site transfer receipt, we don't need to create a stock journal
                    // this movement would only be internal info to show the inTransit quantities
                    return true;
                }
                // Process to the stock journal record creation
                const stockRecord =
                    !(await detail.stockRecord) ||
                    xtremMasterData.functions.nodeIsBeingDeleted((await detail.stockRecord)!)
                        ? ((await detail.deletedStockRecordData) as any)
                        : await detail.stockRecord;
                stockMovement = await xtremStock.functions.stockJournalLib._createStockJournal(
                    context,
                    await _setStockJournalCreateData(
                        context,
                        {
                            ...stockUpdateResult,
                            stockRecord,
                        },
                        {
                            ...stockUpdateData,
                            stockDetail: detail,
                        },
                    ),
                );
                return (
                    stockUpdateData.stockUpdateParameters.stockValuationManager instanceof
                        xtremStock.classes.StockAverageCostValuationManager || _stockValueUpdate(context, stockMovement)
                );
            },
            async (detail: xtremStockData.nodes.StockChangeDetail) => {
                if (
                    xtremStock.functions.typingLib.isThisStockUpdateDataOfStockTransfer(stockUpdateData) &&
                    stockUpdateData.stockUpdateParameters.intersiteTransferData.type === 'internalShipment'
                ) {
                    // if the stock movement is an internal inter-site transfer shipment, we don't need to create a stock journal
                    // this movement would only be internal info to show the inTransit quantities
                    return true;
                }
                // Process to the stock journal record creation
                stockMovement = await xtremStock.functions.stockJournalLib._createStockJournal(
                    context,
                    await _setStockJournalCreateData(context, stockUpdateResult, {
                        ...stockUpdateData,
                        stockDetail: detail,
                    }),
                );
                return (
                    stockUpdateData.stockUpdateParameters.stockValuationManager instanceof
                        xtremStock.classes.StockAverageCostValuationManager || _stockValueUpdate(context, stockMovement)
                );
            },
        );
        [stockValueResult] = flipFlopReturn;

        // ensure typescript detect assignation on both branches here
        stockJournalRecord = stockMovement!;
    } else {
        // Prepare data to create the record on the stock journal
        const stockJournalCreateData = await _setStockJournalCreateData(context, stockUpdateResult, stockUpdateData);

        // Process to the stock journal record creation
        stockJournalRecord = await xtremStock.functions.stockJournalLib._createStockJournal(
            context,
            stockJournalCreateData,
        );
        stockValueResult =
            stockUpdateData.stockUpdateParameters.stockValuationManager instanceof
                xtremStock.classes.StockAverageCostValuationManager ||
            (await _stockValueUpdate(context, stockJournalRecord));
    }

    const config = xtremStock.functions.config.getStockConfig(context);

    if (!stockValueResult || config?.errorGenerator?.stopAfterStockUpdate) {
        throw new BusinessRuleError(
            (config?.errorGenerator?.stopAfterStockUpdate ? '(Generated error)' : '') +
                context.localize(
                    '@sage/xtrem-stock/functions__stock-engine__stock_value_update_failed_with_parameters',
                    'The stock value update has failed. Please check your parameters:\nItem {{item}}\nSite {{site}}\nQuantity {{quantity}}',
                    {
                        item: await (await stockDetail.item).id,
                        site: await (await stockDetail.site).id,
                        quantity: await stockDetail.quantityInStockUnit,
                        cost:
                            stockDetail instanceof xtremStockData.nodes.StockMovementDetail
                                ? await stockDetail.orderCost
                                : 0,
                    },
                ),
        );
    }
    return stockJournalRecord;
}

async function _commonStockUpdates(
    context: Context,
    stockDetail: xtremStockData.nodes.StockMovementDetail,
    stockUpdateParameters: xtremStock.interfaces.StockUpdateParametersWithValuationManager<xtremStockData.enums.StockMovementTypeEnum>,
): Promise<xtremStockData.interfaces.StockEngineReturn> {
    let stockUpdateResult: xtremStockData.interfaces.StockUpdateReturn;
    let stockUpdateData: xtremStock.interfaces.StockEngineParameters<xtremStockData.enums.StockMovementTypeEnum>;

    if (stockDetail instanceof xtremStockData.nodes.StockIssueDetail) {
        let stockHaBeenDeleted = JSON.stringify(await stockDetail.deletedStockRecordData) !== '{}';

        if (!stockHaBeenDeleted) {
            const stockRecord = await stockDetail.stockRecord;
            stockHaBeenDeleted = !stockRecord || xtremMasterData.functions.nodeIsBeingDeleted(stockRecord);
        }

        if (stockHaBeenDeleted) {
            throw new BusinessRuleError(
                context.localize(
                    '@sage/xtrem-stock/nodes__stock_detail__stock_record_has_been_deleted',
                    'Stock issue is not possible because the stock-record has been deleted by another transaction.',
                ),
            );
        }
    }

    try {
        // Prepare data to update stock node
        stockUpdateData = await xtremStock.functions.stockDetailLib.prepareStockUpdateData(
            context,
            stockDetail,
            stockUpdateParameters,
        );
    } catch (error) {
        if (error.message !== "Cannot read property 'location' of null") throw error;
        else {
            const lotNumber = (await (await (await stockDetail.stockDetailLot)?.lot)?.id) || '∅';
            throw new BusinessRuleError(
                stockUpdateResult!?.errorMessage
                    ? stockUpdateResult!.errorMessage!
                    : context.localize(
                          '@sage/xtrem-stock/functions__stock-engine__stock_update_failed_with_parameters',
                          'The stock update has failed. Please check your parameters:\nItem {{item}}\nSite {{site}}\nStock unit {{stockUnit}}\nLot {{lot}}\nLocation {{location}}\nStock status {{stockStatus}}\nOwner {{owner}}',
                          {
                              item: await (await stockDetail.item).id,
                              site: await (await stockDetail.site).id,
                              stockUnit: await (await stockDetail.stockUnit).id,
                              lot: lotNumber,
                              location: (stockDetail as any).location?.id || '∅',
                              stockStatus: (stockDetail as any).status?.id || '∅',
                              owner: (stockDetail as any).owner || (await (await stockDetail.site).id) || '∅',
                          },
                      ),
            );
        }
    }

    // add this so the issue doesn't go through if there is not enough stock available
    if (
        stockUpdateData &&
        !xtremStock.functions.typingLib.isThisStockUpdateDataOfStockReceipt(stockUpdateData) &&
        !xtremStock.functions.typingLib.isThisStockUpdateDataOfStockAdjustment(stockUpdateData)
    ) {
        const quantityAvailable = xtremStockData.functions.stockLib.getAvailableActiveQuantity(
            (await stockUpdateData.stockRecord?.quantityInStockUnit) || 0,
            (await stockUpdateData.stockRecord?.totalAllocated) || 0,
        );
        const quantityToIssue = await stockUpdateData.stockDetail.quantityInStockUnit;

        if (
            await xtremStockData.events.control.stock.doesStockHasEnoughAvailableQuantity(
                stockDetail as xtremStockData.interfaces.StockDetailLinkedToStock,
                quantityToIssue,
                quantityAvailable,
                true,
            )
        ) {
            throw new BusinessRuleError(
                context.localize(
                    '@sage/xtrem-stock/nodes__stock_detail__exceeds_stock_record_quantity',
                    'The quantity {{qty1}} of stock-detail nb {{detailNb}} exceeds the stock record quantity {{qty2}}.',
                    {
                        detailNb: `(${await stockDetail._sortValue})`,
                        qty1: `(${-quantityToIssue})`,
                        qty2: `(${quantityAvailable})`,
                    },
                ),
            );
        }
    }

    if (
        !(
            (xtremStock.functions.typingLib.isThisStockUpdateDataOfStockAdjustment(stockUpdateData) ||
                xtremStock.functions.typingLib.isThisStockUpdateDataOfStockCorrection(stockUpdateData)) &&
            (await stockDetail.quantityInStockUnit) === 0
        )
    ) {
        // Process to the stock update
        if (isDoubleMovementUpdate(stockUpdateData)) {
            stockUpdateResult = await _stockChange(context, stockUpdateData);
        } else {
            stockUpdateResult = await _stockUpdate(context, stockUpdateData);
        }

        // Check if stock update was successful
        if (stockUpdateResult === null || stockUpdateResult.resultAction === 'none') {
            throw new BusinessRuleError(
                stockUpdateResult?.errorMessage
                    ? stockUpdateResult.errorMessage
                    : context.localize(
                          '@sage/xtrem-stock/functions__stock-engine__stock_update_failed_with_parameters',
                          'The stock update has failed. Please check your parameters:\nItem {{item}}\nSite {{site}}\nStock unit {{stockUnit}}\nLot {{lot}}\nLocation {{location}}\nStock status {{stockStatus}}\nOwner {{owner}}',
                          {
                              item: isNumber(stockUpdateData.searchData.item)
                                  ? stockUpdateData.searchData.item
                                  : await stockUpdateData.searchData.item.id,
                              site: isNumber(stockUpdateData.searchData.site)
                                  ? stockUpdateData.searchData.site
                                  : await stockUpdateData.searchData.site.id,
                              stockUnit: isNumber(stockUpdateData.searchData.stockUnit)
                                  ? stockUpdateData.searchData.stockUnit
                                  : await stockUpdateData.searchData.stockUnit.id,
                              lot: isNumber(stockUpdateData.searchData.lot)
                                  ? stockUpdateData.searchData.lot
                                  : await stockUpdateData.searchData.lot?.id,
                              location: isNumber(stockUpdateData.searchData.location)
                                  ? stockUpdateData.searchData.location
                                  : await stockUpdateData.searchData.location?.id,
                              stockStatus: isNumber(stockUpdateData.searchData.status)
                                  ? stockUpdateData.searchData.status
                                  : await stockUpdateData.searchData.status?.id,
                              owner: stockUpdateData.searchData.owner,
                          },
                      ),
            );
        }
    } else {
        stockUpdateResult = {
            stockRecord: null,
            resultAction: xtremStock.functions.typingLib.isThisStockUpdateDataOfStockAdjustment(stockUpdateData)
                ? 'adjusted'
                : 'corrected',
        };
    }

    const stockJournalRecord = await _updateStockJournal(context, stockUpdateData, stockUpdateResult, stockDetail);

    // TODO: [MVP] call stock traceability update
    // use stockJournalRecord;

    return {
        ...stockUpdateResult,
        stockJournalRecord,
        // stockTraceabilityRecord: null,
        lotCreated: false,
    };
}

export async function stockIssue(
    context: Context,
    stockDetails: xtremStockData.nodes.StockIssueDetail[],
    stockUpdateParameters: xtremStock.interfaces.StockUpdateParametersWithValuationManager<xtremStockData.enums.StockMovementTypeEnum>,
): Promise<xtremStockData.interfaces.StockEngineReturn[]> {
    ensureAtLeastOneStockDetail(context, stockDetails);

    await _updateStockDetailCost(context, stockDetails);

    // run all updates needed for each document-line stock detail
    return asyncArray(stockDetails)
        .map(async stockDetail => {
            const stockUpdatesResult = await _commonStockUpdates(context, stockDetail, stockUpdateParameters);

            return {
                ...stockUpdatesResult,
                lotCreated: false,
            };
        })
        .toArray();
}

export async function stockReceipt(
    context: Context,
    stockDetails: xtremStockData.nodes.StockReceiptDetail[],
    stockUpdateParameters: xtremStock.interfaces.StockUpdateParametersWithValuationManager<xtremStockData.enums.StockMovementTypeEnum>,
): Promise<xtremStockData.interfaces.StockEngineReturn[]> {
    ensureAtLeastOneStockDetail(context, stockDetails);

    await _updateStockDetailCost(context, stockDetails);

    let results: Array<xtremStockData.interfaces.StockEngineReturn> = [];

    if (stockDetails.length > 0) {
        results = await asyncArray(stockDetails)
            .map(async stockDetail => {
                if (!(await (await stockDetail.stockDetailLot)?.stockDetailLot)) {
                    // first case: handle detail without temporary link from the linked stockDetailLot to another stockDetailLot
                    // (these are the details for which we maybe need to create a new lot)

                    // Handle the lot. We may have a lot record or data to create a new lot
                    const lotUpdateResult = await _handleLot(context, stockDetail);

                    const stockNewLotUpdatesResult = await _commonStockUpdates(
                        context,
                        lotUpdateResult?.updatedStockDetail || stockDetail,
                        stockUpdateParameters,
                    );

                    return {
                        ...stockNewLotUpdatesResult,
                        lotCreated: lotUpdateResult?.lotCreated || false,
                    };
                }
                // second case: handle detail with a temporary link from the linked stockDetailLot to another stockDetailLot
                // (these are the details for which we don't need to create a new lot, the lot is taken from the referenced stockDetailLot)

                const stockDetailLot = await xtremMasterData.functions.getWritableNode(
                    context,
                    xtremStockData.nodes.StockDetailLot,
                    (await stockDetail.stockDetailLot)?._id || 0,
                );
                const detailId: integer = (await (await stockDetail.stockDetailLot)?.stockDetailLot)?._id || 0;
                let baseDetail = await asyncArray(stockDetails)
                    .filter(async detail => (await detail.stockDetailLot)?._id === detailId)
                    .elementAt(0);
                baseDetail = await context.read(xtremStockData.nodes.StockReceiptDetail, { _id: baseDetail._id });
                await stockDetailLot?.$.set({
                    lot: await xtremStock.functions.lotLib.getLotRecord(context, {
                        item: await stockDetail.item,
                        id: (await (await (await baseDetail.stockDetailLot)?.lot)?.id) || '',
                        sublot: (await (await (await baseDetail.stockDetailLot)?.lot)?.sublot) || '',
                    }),
                    lotNumber: '',
                    stockDetailLot: null,
                });
                await stockDetailLot.$.save();

                const stockUpdatesResult = await _commonStockUpdates(context, stockDetail, stockUpdateParameters);

                return {
                    ...stockUpdatesResult,
                    lotCreated: false,
                };
            })
            .toArray();
    }

    return results;
}

function stockQuantityAdjustmentOrCorrection(
    context: Context,
    stockDetail: xtremStockData.nodes.StockAdjustmentDetail | xtremStockData.nodes.StockCorrectionDetail,
    stockUpdateParameters: xtremStock.interfaces.StockUpdateParametersWithValuationManager<xtremStockData.enums.StockMovementTypeEnum>,
): Promise<xtremStockData.interfaces.StockEngineReturn> {
    return _commonStockUpdates(context, stockDetail, stockUpdateParameters);
}

export async function stockValueAdjustment(
    context: Context,
    stockDetail: xtremStockData.nodes.StockAdjustmentDetail,
    stockUpdateParameters: xtremStock.interfaces.StockUpdateParametersWithValuationManager<xtremStockData.enums.StockMovementTypeEnum>,
): Promise<xtremStockData.interfaces.StockEngineReturn> {
    if ((await stockDetail.quantityInStockUnit) !== 0) {
        throw new DataInputError('A stock value adjustment cannot contain quantity changes');
    }

    return _commonStockUpdates(context, stockDetail, stockUpdateParameters);
}

async function _commonStockValueUpdates(
    context: Context,
    stockDetail: xtremStockData.nodes.StockValueDetail,
    stockUpdateParameters: xtremStock.interfaces.StockUpdateParametersWithValuationManager<xtremStockData.enums.StockMovementTypeEnum.valueChange>,
): Promise<xtremStockData.interfaces.StockEngineReturn> {
    const stockUpdateData: xtremStock.interfaces.StockEngineParameters<xtremStockData.enums.StockMovementTypeEnum> =
        await xtremStock.functions.stockDetailLib.prepareStockUpdateData(context, stockDetail, stockUpdateParameters);

    const stockUpdateResult: xtremStockData.interfaces.StockUpdateReturn = {
        stockRecord: null,
        resultAction: 'valueChange',
    };

    const stockJournalRecord = await _updateStockJournal(context, stockUpdateData, stockUpdateResult, stockDetail);

    return {
        ...stockUpdateResult,
        stockJournalRecord,
        lotCreated: false,
    };
}

export function stockValueChange(
    context: Context,
    stockDetails: xtremStockData.nodes.StockValueDetail[],
    stockUpdateParameters: xtremStock.interfaces.StockUpdateParametersWithValuationManager<xtremStockData.enums.StockMovementTypeEnum.valueChange>,
): Promise<xtremStockData.interfaces.StockEngineReturn[]> {
    ensureAtLeastOneStockDetail(context, stockDetails);

    return asyncArray(stockDetails)
        .map(async stockDetail => ({
            ...(await _commonStockValueUpdates(context, stockDetail, stockUpdateParameters)),
        }))
        .toArray();
}

export async function stockAdjustment(
    context: Context,
    stockDetails: Array<xtremStockData.nodes.StockAdjustmentDetail>,
    stockUpdateParameters: xtremStock.interfaces.StockUpdateParametersWithValuationManager<xtremStockData.enums.StockMovementTypeEnum>,
): Promise<Array<xtremStockData.interfaces.StockEngineReturn>> {
    await _updateStockDetailCost(context, stockDetails);

    return asyncArray(stockDetails)
        .map(async stockDetail => {
            if ((await stockDetail.quantityInStockUnit) !== 0) {
                let lotUpdateResult: xtremStockData.interfaces.LotUpdateReturn | null = null;

                // Only manage lot for increase adjustments
                if ((await stockDetail.quantityInStockUnit) > 0) {
                    lotUpdateResult = await _handleLot(context, stockDetail);
                }

                const stockUpdatesResult = await stockQuantityAdjustmentOrCorrection(
                    context,
                    (lotUpdateResult?.updatedStockDetail as xtremStockData.nodes.StockAdjustmentDetail) || stockDetail,
                    stockUpdateParameters,
                );

                return {
                    ...stockUpdatesResult,
                    lotCreated: lotUpdateResult?.lotCreated || false,
                } as xtremStockData.interfaces.StockEngineReturn;
            }
            return {
                ...(await stockValueAdjustment(context, stockDetail, stockUpdateParameters)),
                lotCreated: false,
            } as xtremStockData.interfaces.StockEngineReturn;
        })
        .toArray();
}
export function searchStock(
    context: Context,
    stockIssueCriteria: xtremStockData.interfaces.StockIssueSearchData,
): Promise<xtremStockData.interfaces.StockSearchResult[]> {
    return xtremStockData.functions.stockLib.searchStock(context, stockIssueCriteria);
}

export function getSerialNumbersForStockDetail(
    context: Context,
    startingSerialNumber: xtremStockData.nodes.SerialNumber,
    quantityInStockUnit: decimal,
): Promise<xtremStockData.nodes.SerialNumber[]> {
    return xtremStock.functions.serialNumberLib.getSerialNumbersForStockDetail(
        context,
        startingSerialNumber,
        quantityInStockUnit,
    );
}

export async function stockChange(
    context: Context,
    stockDetails: xtremStockData.nodes.StockChangeDetail[],
    stockUpdateParameters: xtremStock.interfaces.StockUpdateParametersWithValuationManager<xtremStockData.enums.StockMovementTypeEnum>,
): Promise<xtremStockData.interfaces.StockEngineReturn[]> {
    if (
        !xtremStockData.functions.typingLib.areTheseParametersOfStockTransfer(stockUpdateParameters) ||
        !stockUpdateParameters.stockDetailData?.isDocumentWithoutStockDetails
    ) {
        ensureAtLeastOneStockDetail(context, stockDetails);
    }

    await _updateStockDetailCost(context, stockDetails);

    return asyncArray(stockDetails)
        .map(async stockDetail => ({
            ...(await _commonStockUpdates(context, stockDetail, stockUpdateParameters)),
            lotCreated: false,
        }))
        .toArray();
}

export function stockCorrection(
    context: Context,
    stockDetails: Array<xtremStockData.nodes.StockCorrectionDetail>,
    stockUpdateParameters: xtremStock.interfaces.StockUpdateParametersWithValuationManager<xtremStockData.enums.StockMovementTypeEnum>,
): Promise<xtremStockData.interfaces.StockEngineReturn[]> {
    ensureAtLeastOneStockDetail(context, stockDetails);

    return asyncArray(stockDetails)
        .map(async stockDetail => {
            // TODO: review manageServiceItems to include the following test
            if (
                (await stockDetail.quantityInStockUnit) === 0 &&
                !stockUpdateParameters.stockValuationManager?.isValuationToPerform(
                    await stockDetail.documentLine,
                    stockDetail._id,
                )
            ) {
                // as the StockCorrectionDetail is not used to correct the stock value, this record will keep 'isTransacted' to false.
                // => It's not possible to know if it has been processed afterwards
                // => delete it
                await stockDetail.$.delete();
                return {
                    resultAction: 'noChange',
                    stockRecord: null,
                    stockJournalRecord: null,
                    // stockTraceabilityRecord: null,
                    lotCreated: false,
                } as xtremStockData.interfaces.StockEngineReturn;
            }
            return {
                ...(await stockQuantityAdjustmentOrCorrection(context, stockDetail, stockUpdateParameters)),
                lotCreated: false,
            };
        })
        .toArray();
}
