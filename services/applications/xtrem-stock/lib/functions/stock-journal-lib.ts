import type { Context, NodeCreateData } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremStockData from '@sage/xtrem-stock-data';
import type * as xtremStock from '..';

export async function getLastStockJournal(
    context: Context,
    stockJournalSearch: xtremStock.interfaces.StockJournalSearchData,
): Promise<xtremStockData.nodes.StockJournal | null> {
    return (
        (
            await context
                .query(xtremStockData.nodes.StockJournal, {
                    filter: { ...stockJournalSearch },
                    orderBy: { sequence: +1 },
                    first: 1,
                })
                .toArray()
        )[0] || null
    );
}

/**
 * Get the last sequence Number Created
 */
export async function getLastSequenceNumberStockJournal(
    context: Context,
    stockJournalSearch: xtremStock.interfaces.StockJournalSearchData,
): Promise<number> {
    return (await (await getLastStockJournal(context, stockJournalSearch))?.sequence) || 100000;
}

// TODO: rename into _stockJournalUpdate
/**
 * Create a stock Journal entry Simple function for now
 * @param context
 * @param stockJournalData Data to create the Stock Journal Entry
 */
export async function _createStockJournal(
    context: Context,
    stockJournalData: NodeCreateData<xtremStockData.nodes.StockJournal>,
): Promise<xtremStockData.nodes.StockJournal> {
    const stockJournalSearch = {
        effectiveDate: stockJournalData.effectiveDate,
        item: stockJournalData.item,
        site: stockJournalData.site,
    };
    /**
     * This one need to be refactor, but createFunction
     */
    const sequence =
        (await getLastSequenceNumberStockJournal(
            context,
            stockJournalSearch as xtremStock.interfaces.StockJournalSearchData,
        )) - 1;

    const stockJournalRecord = await context.create(xtremStockData.nodes.StockJournal, {
        ...stockJournalData,
        sequence,
    });

    // XT-32492 Fill stock journal serial number collection with associated serial numbers.
    if (await context.isServiceOptionEnabled(xtremMasterData.serviceOptions.serialNumberOption)) {
        if (stockJournalData.stockDetail instanceof xtremStockData.nodes.StockMovementDetail) {
            await stockJournalData.stockDetail.stockDetailSerialNumbers.forEach(async stockDetailSerialNumber => {
                const serialNumber = await context.read(xtremStockData.nodes.SerialNumber, {
                    _id: (await stockDetailSerialNumber.serialNumber)?._id,
                });
                await stockJournalRecord.serialNumbers.append({
                    serialNumber,
                });
            });
        }
    }

    await stockJournalRecord.$.save();

    return stockJournalRecord;
}
