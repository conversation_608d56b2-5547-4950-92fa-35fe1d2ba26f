import type { Context } from '@sage/xtrem-core';
import { date, LocalizedError } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremStock from '..';

export async function setLineStatus(
    context: Context,
    lineStatus: {
        _id: number;
        creationStatus: xtremStock.enums.StandardCostRollUpResultLineStatus;
        creationErrorMessage?: string;
    },
) {
    const costRollUpResultLine = await context.read(
        xtremStock.nodes.CostRollUpResultLine,
        {
            _id: lineStatus._id,
        },
        { forUpdate: true },
    );
    await costRollUpResultLine.$.set({
        creationStatus: lineStatus.creationStatus,
        creationErrorMessage: lineStatus.creationErrorMessage ?? '',
    });
    await costRollUpResultLine.$.save();
}

export async function createItemSiteCost(
    context: Context,
    parameters: {
        costRollUpInputSet: xtremStock.nodes.CostRollUpInputSet;
        costRollUpResultLine: xtremStock.nodes.CostRollUpResultLine;
    },
): Promise<xtremStock.interfaces.CreatedOrUpdatedItemSiteCost> {
    const newItemSiteCost = await context.create(xtremMasterData.nodes.ItemSiteCost, {
        itemSite: `#${await (await parameters.costRollUpResultLine.item).id}|${await (
            await parameters.costRollUpInputSet.site
        ).id}`,
        costCategory: (await parameters.costRollUpInputSet.costCategory)._id,
        forQuantity: await parameters.costRollUpInputSet.quantity,
        fromDate: await parameters.costRollUpInputSet.fromDate,
        materialCost: await parameters.costRollUpResultLine.materialCost,
        machineCost: await parameters.costRollUpResultLine.machineCost,
        laborCost: await parameters.costRollUpResultLine.laborCost,
        toolCost: await parameters.costRollUpResultLine.toolCost,
        storedDimensions: await parameters.costRollUpResultLine.storedDimensions,
        storedAttributes: await parameters.costRollUpResultLine.storedAttributes,
        isCalculated: true,
    });
    await newItemSiteCost.$.save();

    const message = context.localize(
        '@sage/xtrem-stock/node-extensions__item_site_cost_extension__success_cost_creation_status_message',
        'Item-site cost created: [Item: {{itemId}} - Site: {{siteId}}]',
        {
            itemId: await (await parameters.costRollUpResultLine.item).id,
            siteId: await (await parameters.costRollUpInputSet.site).id,
        },
    );

    return {
        logStatus: 'info',
        creationStatus: 'created',
        message,
        costRollUpResultLine: parameters.costRollUpResultLine,
    };
}

export async function updateItemSiteCost(
    context: Context,
    parameters: {
        itemSiteCost: xtremMasterData.nodes.ItemSiteCost;
        costRollUpInputSet: xtremStock.nodes.CostRollUpInputSet;
        costRollUpResultLine: xtremStock.nodes.CostRollUpResultLine;
    },
): Promise<xtremStock.interfaces.CreatedOrUpdatedItemSiteCost> {
    let message = '';
    if ((await parameters.costRollUpInputSet.fromDate).compare(date.today()) === 0) {
        message = context.localize(
            '@sage/xtrem-stock/node-extensions__item_site_cost_extension__success_cost_update_status_error_message',
            'Item-site cost exists and cannot be updated: [Item: {{itemId}} - Site: {{siteId}}]',
            {
                itemId: await (await parameters.costRollUpResultLine.item).id,
                siteId: await (await parameters.costRollUpInputSet.site).id,
            },
        );

        return {
            logStatus: 'error',
            creationStatus: 'error',
            message,
            costRollUpResultLine: parameters.costRollUpResultLine,
        };
    }

    const existingItemSiteCost = await context.read(
        xtremMasterData.nodes.ItemSiteCost,
        {
            _id: parameters.itemSiteCost._id,
        },
        { forUpdate: true },
    );

    await existingItemSiteCost.$.set({
        forQuantity: await parameters.costRollUpInputSet.quantity,
        materialCost: await parameters.costRollUpResultLine.materialCost,
        machineCost: await parameters.costRollUpResultLine.machineCost,
        laborCost: await parameters.costRollUpResultLine.laborCost,
        toolCost: await parameters.costRollUpResultLine.toolCost,
        storedDimensions: await parameters.costRollUpResultLine.storedDimensions,
        storedAttributes: await parameters.costRollUpResultLine.storedAttributes,
        isCalculated: true,
    });
    await existingItemSiteCost.$.save();

    message = context.localize(
        '@sage/xtrem-stock/node-extensions__item_site_cost_extension__success_cost_update_status_message',
        'Item-site cost updated: [Item: {{itemId}} - Site: {{siteId}}]',
        {
            itemId: await (await parameters.costRollUpResultLine.item).id,
            siteId: await (await parameters.costRollUpInputSet.site).id,
        },
    );

    return {
        logStatus: 'info',
        creationStatus: 'updated',
        message,
        costRollUpResultLine: parameters.costRollUpResultLine,
    };
}

export async function itemSiteCostOnError(
    context: Context,
    parameters: {
        errorMessage: string;
        costRollUpInputSet: xtremStock.nodes.CostRollUpInputSet;
        costRollUpResultLine: xtremStock.nodes.CostRollUpResultLine;
    },
): Promise<xtremStock.interfaces.CreatedOrUpdatedItemSiteCost> {
    const message = context.localize(
        '@sage/xtrem-stock/node-extensions__item_site_cost_extension__error_message',
        '{{errorMessage}}: [Item: {{itemId}} - Site: {{siteId}}]',
        {
            errorMessage: parameters.errorMessage,
            itemId: await (await parameters.costRollUpResultLine.item).id,
            siteId: await (await parameters.costRollUpInputSet.site).id,
        },
    );

    return {
        logStatus: 'error',
        creationStatus: 'error',
        message,
        costRollUpResultLine: parameters.costRollUpResultLine,
    };
}

export async function createOrUpdateItemSiteCost(
    context: Context,
    costRollUpResultLine: xtremStock.nodes.CostRollUpResultLine,
): Promise<xtremStock.interfaces.CreatedOrUpdatedItemSiteCost> {
    const costRollUpInputSet = await costRollUpResultLine.inputSet;

    const creationStatus: xtremStock.enums.StandardCostRollUpResultLineStatus = 'inProgress';

    await setLineStatus(context, {
        _id: costRollUpResultLine._id,
        creationStatus,
    });

    try {
        const existingItemSiteCost = await context
            .query(xtremMasterData.nodes.ItemSiteCost, {
                filter: {
                    itemSite: `#${await (await costRollUpResultLine.item).id}|${await (
                        await costRollUpInputSet.site
                    ).id}`,
                    costCategory: (await costRollUpInputSet.costCategory)._id,
                    fromDate: await costRollUpInputSet.fromDate,
                    version: 1,
                },
                orderBy: { fromDate: 1 },
                last: 1,
            })
            .at(0);
        if (existingItemSiteCost) {
            const updatedItemSiteCost = await updateItemSiteCost(context, {
                itemSiteCost: existingItemSiteCost,
                costRollUpInputSet,
                costRollUpResultLine,
            });
            await setLineStatus(context, {
                _id: costRollUpResultLine._id,
                creationStatus: updatedItemSiteCost.creationStatus,
            });
            return updatedItemSiteCost;
        }
        const createdItemSiteCost = await createItemSiteCost(context, {
            costRollUpInputSet,
            costRollUpResultLine,
        });
        await setLineStatus(context, {
            _id: costRollUpResultLine._id,
            creationStatus: createdItemSiteCost.creationStatus,
        });
        return createdItemSiteCost;
    } catch (error) {
        context.logger.error(error);
        if (!(error instanceof LocalizedError)) throw error;

        const diagnoses = error.extensions.diagnoses ?? [];
        const itemSiteCostInError = await itemSiteCostOnError(context, {
            errorMessage: diagnoses.length > 0 && diagnoses[0].message ? diagnoses[0].message : error.message,
            costRollUpInputSet,
            costRollUpResultLine,
        });
        await setLineStatus(context, {
            _id: costRollUpResultLine._id,
            creationStatus: itemSiteCostInError.creationStatus,
            creationErrorMessage: itemSiteCostInError.message,
        });
        return itemSiteCostInError;
    }
}
