import type { Context, decimal, NodeCreateData } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremStockData from '@sage/xtrem-stock-data';
import * as xtremStock from '..';

export function transformDocumentLineAllocationsInStockDetails<
    DetailCreateData extends NodeCreateData<
        xtremStockData.nodes.StockIssueDetail | xtremStockData.nodes.StockChangeDetail
    > & { _action: 'create' },
>(
    context: Context,
    conversionParameters: {
        documentLine: xtremStockData.interfaces.DocumentLineWithStockAllocation;
        quantityToIssue?: decimal;
    },
    stockUpdateParameters: xtremStockData.interfaces.StockUpdateParameters<xtremStockData.enums.StockMovementTypeEnum>,
): Promise<Array<DetailCreateData>> {
    return conversionParameters.documentLine.stockAllocations
        .map(async allocation => {
            // this has to be done before the allocation gets deleted
            const detailCreateData = await xtremStock.functions.stockDetailLib.stockAllocationToDetailCreateData(
                context,
                {
                    allocation,
                    documentLine: conversionParameters.documentLine,
                    stockUpdateParameters,
                },
            );

            if (await context.isServiceOptionEnabled(xtremMasterData.serviceOptions.serialNumberOption)) {
                const createDataStockDetailSerialNumbers = (
                    await xtremStock.functions.stockDetailLib.serialNumbersAllocationToDetailSerialNumbersCreateData(
                        allocation,
                    )
                ).map(serialNumber => {
                    return { ...serialNumber, _action: 'create' };
                }) as Array<
                    NodeCreateData<
                        xtremStockData.nodes.StockDetailSerialNumber & {
                            _action: 'create';
                        }
                    >
                >;
                detailCreateData.stockDetailSerialNumbers = createDataStockDetailSerialNumbers;
            }
            // remove the allocation which we don't want to have after the posting
            await xtremStockData.functions.allocationLib.deleteAllocation(context, {
                allocationRecord: (await xtremMasterData.functions.getWritableNode(
                    context,
                    xtremStockData.nodes.StockAllocation,
                    allocation._id,
                )) as unknown as Promise<xtremStockData.nodes.StockAllocation>,
            });

            // generate the create data object
            return {
                ...detailCreateData,
                _action: 'create',
            } as DetailCreateData;
        })
        .toArray();
}
