import type { Context } from '@sage/xtrem-core';
import { BusinessRuleError, date } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremStockData from '@sage/xtrem-stock-data';
import type * as xtremSystem from '@sage/xtrem-system';
import * as _ from 'lodash';
import * as stockDetailLib from './stock-detail-lib';

// [Hack] This is done to make the linter happy...
const Date = date;

/**
 * [Internal] This function cleanup unneeded property from LotCreateData to be used for read/tryRead.
 * <PERSON>leas pay attention to clone the object before the call if you need LotCreateData properties after
 * @ignore
 * @param lotData (should fit StockSearchData interface)
 */
function _cleanupCriteria(lotData: xtremStockData.interfaces.LotSearchData): void {
    if (_.hasIn(lotData, 'supplierLot')) {
        delete (lotData as xtremStockData.interfaces.LotCreateData).supplierLot;
    }
    if (_.hasIn(lotData, 'expirationDate')) {
        delete (lotData as xtremStockData.interfaces.LotCreateData).expirationDate;
    }
    if (_.hasIn(lotData, 'sublot') && !lotData.sublot) {
        delete (lotData as xtremStockData.interfaces.LotCreateData).sublot;
    }
    if (_.hasIn(lotData, 'documentLine')) {
        delete (lotData as xtremStockData.interfaces.LotCreateData).documentLine;
    }
}

/**
 * Return a lot record or null (if not found) corresponding to the given lot data.
 * @param context
 * @param lotData lot information needed to find your record.
 * @returns a lot record or null (if not found).
 */
export function getLotRecord(
    context: Context,
    lotData: xtremStockData.interfaces.LotSearchData,
): Promise<xtremStockData.nodes.Lot | null> {
    const lotCriteria = { ...lotData };
    _cleanupCriteria(lotCriteria);
    return context.tryRead(xtremStockData.nodes.Lot, lotCriteria);
}

/**
 * Returns the data needed to create a new lot from a stockLineDetail.
 * To be used only when a lot id is not provided on the current lotCreationData.
 * It will generate a lot id based on the lot sequence number defined for the item.
 * @param context
 * @param stockLineDetail stock line detail record
 * @returns data needed to create a new lot
 */
async function _generateLotId(
    context: Context,
    lotCreationData: xtremStockData.interfaces.LotCreateData,
    site?: xtremSystem.nodes.Site,
): Promise<xtremStockData.interfaces.LotCreateData> {
    if (await lotCreationData.item.lotSequenceNumber) {
        // then we generate the new lot id
        lotCreationData.id = await (
            await xtremMasterData.classes.DocumentNumberGenerator.create(context, {
                sequenceNumber: await context.read(xtremMasterData.nodes.SequenceNumber, {
                    _id: await stockDetailLib.readLotSequenceNumberID(lotCreationData),
                }),
                currentDate: date.today(),
                site,
                company: await site?.legalCompany,
            })
        ).allocate();
    } else {
        throw new BusinessRuleError(
            context.localize(
                '@sage/xtrem-stock/functions__lot-lib__no_lot_sequence_number_definied',
                'There is no lot sequence number defined for item {{item}}',
                { item: await lotCreationData.item.id },
            ),
        );
    }
    return lotCreationData;
}

/**
 * Creates a new lot from a stockLineDetail.lotCreationData
 * This function is used for the 'old' lot management and for the 'new' one which has a stockDetailLot reference in
 * the stockDetail. In this case we create the lotCreationData from the stockDetailLot reference on the stock detail
 * @param context
 * @param stockLineDetail stock line detail
 * @returns a new Lot
 */
async function _createNewLot(
    context: Context,
    stockDetail: xtremStockData.nodes.StockReceiptDetail,
): Promise<xtremStockData.nodes.Lot> {
    let lotCreationData = await stockDetail.lotCreateData;

    // if stockDetail has a reference to a stockDetailLot > do it the new way
    // create lotCreationData from stockDetailLot reference of the stock detail
    if (await stockDetail.stockDetailLot) {
        lotCreationData = {
            id: await (await stockDetail.stockDetailLot)?.lotNumber,
            item: await stockDetail.item,
            supplierLot: (await (await stockDetail.stockDetailLot)?.supplierLot) ?? '',
            sublot: await (await stockDetail.stockDetailLot)?.sublot,
            expirationDate: (await (
                await stockDetail.item
            ).isExpiryManaged)
                ? (await (await stockDetail.stockDetailLot)?.expirationDate) || undefined
                : undefined,
        };
    }

    if (lotCreationData) {
        // If we don't have lot id for the new lot to create, we generate one from the lot sequence counter assigned to the item
        if (!lotCreationData?.id) {
            lotCreationData = await _generateLotId(context, lotCreationData, await stockDetail.site);
        }

        if (typeof lotCreationData.expirationDate === 'string') {
            lotCreationData.expirationDate = new Date(
                parseInt((lotCreationData.expirationDate as string).replace(/\D/g, ''), 10),
            );
        }

        if (typeof lotCreationData.expirationReferenceDate === 'string') {
            lotCreationData.expirationReferenceDate = new Date(
                parseInt((lotCreationData.expirationReferenceDate as string).replace(/\D/g, ''), 10),
            );
        }

        lotCreationData.creationDate = await stockDetail.effectiveDate;

        const newLot = await context.create(xtremStockData.nodes.Lot, lotCreationData);
        await newLot.$.save();

        return newLot;
    }
    throw new BusinessRuleError(
        context.localize(
            '@sage/xtrem-stock/functions__lot-lib__no_lot_information_created',
            'No lot information created.',
        ),
    );
}

/**
 * Creates and returns a new lot. This function is only used for the 'new' way of lot management which has a stockDetailLot
 * the stockDetailLot (called from _handleLot in stock-engine where stockDetailLot reference is checked before)
 * @param context
 * @param stockDetail
 * @returns a lot
 */
export async function createLot(
    context: Context,
    stockDetail: xtremStockData.nodes.StockReceiptDetail,
): Promise<xtremStockData.interfaces.LotUpdateReturn> {
    if (await stockDetail.stockDetailLot) {
        return {
            lotCreated: true,
            lotRecord: await _createNewLot(context, stockDetail),
        };
    }

    throw new BusinessRuleError(
        context.localize(
            '@sage/xtrem-stock/functions__lot-lib__no_lot_information_received',
            'No lot information received.',
        ),
    );
}
