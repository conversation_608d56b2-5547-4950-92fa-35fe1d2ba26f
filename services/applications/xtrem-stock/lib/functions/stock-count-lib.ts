import type { Context, NodeCreateData, NodeQueryFilter, decimal, integer } from '@sage/xtrem-core';
import { BusinessRuleError, Logger, asyncArray } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremStockData from '@sage/xtrem-stock-data';
import type * as xtremSystem from '@sage/xtrem-system';
import * as _ from 'lodash';
import * as xtremStock from '..';

const logger = Logger.getLogger(__filename, 'stock-count');
class StockCountLib {
    // eslint-disable-next-line @typescript-eslint/no-empty-function
    private constructor() {}

    public static readonly instance: StockCountLib = new StockCountLib();

    // eslint-disable-next-line class-methods-use-this
    filterCriteria(
        stockSite: xtremSystem.nodes.Site,
        fromItemId?: string,
        toItemId?: string,
        lastCountDate?: string | null,
        locations?: Array<xtremMasterData.nodes.Location>,
        zones?: Array<xtremMasterData.nodes.LocationZone>,
        includeInTransit?: boolean,
    ) {
        return xtremStock.sharedFunctions.stockCountFilterCriteria(
            `${stockSite._id}`,
            fromItemId,
            toItemId,
            lastCountDate,
            locations?.map(location => location._id.toString()),
            zones?.map(zone => zone._id.toString()),
            includeInTransit,
        );
    }

    // eslint-disable-next-line class-methods-use-this
    async filterItemSite(
        context: Context,
        filterData: xtremStock.interfaces.StockCountItemSiteFilter,
    ): Promise<NodeQueryFilter<xtremMasterData.nodes.ItemSite>> {
        const itemRange = {
            ...(filterData.fromItem ? { _gte: await filterData.fromItem.id } : {}),
            ...(filterData.toItem ? { _lte: await filterData.toItem.id } : {}),
        };

        const categories =
            filterData.categories.length > 0
                ? { _id: { _in: filterData.categories.map(category => category._id) } }
                : {};

        const itemFilter = {
            ...{ isStockManaged: true, isPhantom: false },
            ...(!_.isEmpty(itemRange) ? { id: itemRange } : {}),
            ...(!_.isEmpty(categories) ? { category: categories } : {}),
        };

        const locationFilter = {
            ...(filterData.locations.length > 0
                ? { _id: { _in: filterData.locations.map(location => location._id) } }
                : {}),
            ...(filterData.zones.length > 0
                ? { locationZone: { _id: { _in: filterData.zones.map(zone => zone._id) } } }
                : {}),
        };

        const stockFilter = {
            ...(filterData.hasStockRecords
                ? {
                      countStockRecords: { _atLeast: 1 },
                  }
                : {}),
            ...(!_.isEmpty(locationFilter) ? { countStockRecords: { _atLeast: 1, location: locationFilter } } : {}),
        };

        // An SN item can only be counted if it has stock.
        const serialNumberFilter = (await context.isServiceOptionEnabled(
            xtremMasterData.serviceOptions.serialNumberOption,
        ))
            ? {
                  _or: [
                      { item: { serialNumberManagement: { _eq: 'notManaged' } } },
                      { item: { serialNumberManagement: { _eq: 'managed' } }, countStockRecords: { _atLeast: 1 } },
                  ],
              }
            : {};

        const itemSitesInProgressFilter =
            filterData.concurrentStockCountLines.length > 0
                ? {
                      _or: [
                          {
                              _id: {
                                  _nin: await asyncArray(filterData.concurrentStockCountLines)
                                      .map(async stockCountLine => (await stockCountLine.itemSite)._id)
                                      .toArray(),
                              },
                          },
                          {
                              countStockRecords: { _atLeast: 1 },
                          },
                      ],
                  }
                : {};

        const lastCountDateFilter = filterData.lastCountDate
            ? {
                  _or: [{ lastCountDate: undefined }, { lastCountDate: { _lt: filterData.lastCountDate } }],
              }
            : {};

        const combineOrFilter = { _and: [serialNumberFilter, itemSitesInProgressFilter, lastCountDateFilter] };

        return {
            site: { _id: { _eq: filterData.stockSite._id } },
            ...combineOrFilter,
            ...(!_.isEmpty(itemFilter) ? { item: itemFilter } : {}),
            ...stockFilter,
        };
    }

    // control that a stock count line is uniq for all not closed stock counts (including the current one)
    // eslint-disable-next-line class-methods-use-this
    async existingStockCountLine(
        context: Context,
        throwErrorIfExist: boolean,
        searchCriteria: {
            item: integer;
            stockStatus: integer | null;
            location?: integer;
            lot?: integer;
            stockUnit: integer;
            owner: string;
            document: { stockSite: integer };
        },
        lotCreateData?: {
            id: string;
            sublot: string | null;
        },
    ): Promise<boolean> {
        const filter: typeof searchCriteria & {
            document: { status?: { _in: Array<xtremStock.enums.StockCountStatus> } };
        } = searchCriteria;
        filter.document.status = { _in: ['toBeCounted', 'counted', 'countInProgress'] };
        const { lot } = searchCriteria;
        if (lot) {
            delete filter.lot;
        }
        let searchResult = context.query(xtremStock.nodes.StockCountLine, {
            filter,
        });
        let result = (await searchResult.length) > 0;
        if (result && lot) {
            searchResult = searchResult.filter(
                async stockCountLine =>
                    (await (await (await stockCountLine.stockDetails.at(0))?.stockDetailLot)?.lot)?._id === lot,
            );
        }
        result = (await searchResult.length) > 0;
        let stockCountNumber = result ? await (await searchResult.at(0))?.documentNumber : '';

        if (result && lotCreateData) {
            result = await searchResult.some(async stockCountLine => {
                const stockDetailLot = await (await stockCountLine.stockDetails.at(0))?.stockDetailLot;
                if (stockDetailLot == null) {
                    return false;
                }
                stockCountNumber = await stockCountLine.documentNumber;
                return (
                    (await stockDetailLot.lotNumber) === lotCreateData.id &&
                    ((await stockDetailLot.sublot) || null) === lotCreateData.sublot
                );
            });
        }

        if (result && throwErrorIfExist) {
            throw new BusinessRuleError(
                context.localize(
                    '@sage/xtrem-stock/function__stock_count_lib__line_already_exists_in_a_not_closed_stock-count',
                    'The line already exists in the stock count {{number}}.',
                    {
                        number: stockCountNumber,
                    },
                ),
            );
        }
        return result;
    }

    // eslint-disable-next-line class-methods-use-this
    async checkStockCountLineExists(context: Context, stockRecord: xtremStockData.nodes.Stock): Promise<boolean> {
        const existingStockCount =
            (
                await context.select(
                    xtremStock.nodes.StockCountLine,
                    { document: { number: true } },
                    {
                        filter: {
                            document: { status: { _in: ['toBeCounted', 'counted', 'countInProgress'] } },
                            stockRecord: stockRecord._id,
                        },
                    },
                )
            ).at(0)?.document.number ?? '';
        if (existingStockCount) {
            logger.verbose(
                () => `_Record excluded as exists on another stock count line stockCountLineId=${existingStockCount}`,
            );
            await context.batch.logMessage(
                'info',
                context.localize(
                    '@sage/xtrem-stock/nodes__stock_count__confirm_stock_count_stock_record_excluded',
                    'The stock record for item {{item}} is excluded. It is already in another stock count: {{existingRecord}}..',
                    { item: (await stockRecord.item).id, existingRecord: existingStockCount },
                ),
            );
        }
        return !!existingStockCount;
    }

    // As this function is called from the 'quantityInStockUnit' property,
    // the list of properties must be specified to be consistent with the 'dependsOn' array
    // eslint-disable-next-line class-methods-use-this
    async getStockQuantity(
        context: Context,
        stockCountLine: Pick<
            xtremStock.nodes.StockCountLine,
            'item' | 'site' | 'location' | 'lot' | 'stockStatus' | 'stockUnit' | 'owner'
        >,
    ): Promise<decimal> {
        const stockRecord = await xtremStockData.functions.stockLib.getStockRecord(
            context,
            {
                item: await stockCountLine.item,
                site: await stockCountLine.site,
                location: await stockCountLine.location,
                lot: await stockCountLine.lot,
                status: await stockCountLine.stockStatus,
                stockUnit: await stockCountLine.stockUnit,
                owner: await stockCountLine.owner,
            },
            false,
        );

        return (await stockRecord?.quantityInStockUnit) ?? 0;
    }

    // eslint-disable-next-line class-methods-use-this
    private async controlStatusBeforePosting(context: Context, documentIds: integer[]) {
        const wrongDocument = await context
            .query(xtremStock.nodes.StockCount, {
                filter: { _id: { _in: documentIds }, status: { _ne: 'counted' } },
                first: 1,
            })
            .at(0);
        if (wrongDocument) {
            throw new BusinessRuleError(
                context.localize(
                    '@sage/xtrem-stock/nodes__stock_count__counting_is_not_finished_yet',
                    'You cannot post a stock count when the count is not finished: stock count {{number}}.',
                    { number: await wrongDocument.number },
                ),
            );
        }
    }

    // eslint-disable-next-line class-methods-use-this
    private async controlAllocationsBeforePosting(context: Context, documentIds: integer[]) {
        const wrongDocumentLine = (
            await context.select(
                xtremStock.nodes.StockCountLine,
                { documentNumber: true, item: { id: true }, countedQuantityInStockUnit: true, totalAllocated: true },
                {
                    filter: {
                        documentId: { _in: documentIds },
                        hasAllocationError: true,
                    },
                    first: 1,
                },
            )
        ).at(0);
        if (wrongDocumentLine) {
            throw new BusinessRuleError(
                context.localize(
                    '@sage/xtrem-stock/nodes__stock_count__counted_quantity_less_than_allocated_quantity',
                    'For stock count {{number}}, the counted quantity {{countedQuantity}} for {{item}} cannot be less than the allocated quantity {{totalAllocated}}.',
                    {
                        number: wrongDocumentLine.documentNumber,
                        item: wrongDocumentLine.item.id,
                        countedQuantity: wrongDocumentLine.countedQuantityInStockUnit,
                        totalAllocated: wrongDocumentLine.totalAllocated,
                    },
                ),
            );
        }
    }

    // eslint-disable-next-line class-methods-use-this
    async controlStockRecordBeforePosting(context: Context, documentIds: integer[]) {
        const wrongItem =
            (
                await context.select(
                    xtremStock.nodes.StockCountLine,
                    { item: { id: true } },
                    {
                        filter: {
                            documentId: { _in: documentIds },
                            status: 'counted',
                            stockRecord: null,
                            isCreatedFromStockRecord: true,
                        },
                    },
                )
            ).at(0)?.item.id ?? '';
        if (wrongItem) {
            throw new BusinessRuleError(
                context.localize(
                    '@sage/xtrem-stock/nodes__stock_count__counted_stock_record_not_found',
                    'The stock count record for item {{item}} has changed since the stock count started. Please exclude this line from the stock count.',
                    {
                        item: wrongItem,
                    },
                ),
            );
        }
    }

    async controlBeforePosting(context: Context, documentIds: integer[]) {
        await this.controlStatusBeforePosting(context, documentIds);
        await this.controlAllocationsBeforePosting(context, documentIds);
        await this.controlStockRecordBeforePosting(context, documentIds);
    }

    /**
     * A line added during the initialization of the stock count cannot be deleted
     * except if the document itself is deleted
     * => it  can be deleted if:
     *   - the line has been added after the stock count started and is not posted yet
     *   - or the deletion comes from the document (isCascading)
     *        and the document is not modified (because it is deleting -> status='unchanged')
     * @param context
     * @param args
     * @param args.stockCountLine the stock count line to delete
     * @param args.isCascading if the deletion is cascading from the document. If not provided, there is no control on the document status
     * @returns a message explaining why the line cannot be deleted. Empty string if it can be deleted
     */
    // eslint-disable-next-line class-methods-use-this
    async checkStockCountLineDeletion(
        context: Context,
        args: { stockCountLine: xtremStock.nodes.StockCountLine; isCascading?: boolean },
    ): Promise<string> {
        if (args.stockCountLine._id < 0) return '';

        if (['completed', 'inProgress'].includes(await args.stockCountLine.stockTransactionStatus))
            return context.localize(
                '@sage/xtrem-stock/nodes__stock_count_line__line_posted_delete_not_allowed',
                'The stock count line cannot be deleted. It was already posted. Stock count {{number}}, line {{lineNumber}}.',
                { number: await args.stockCountLine.documentNumber, lineNumber: await args.stockCountLine._sortValue },
            );

        if (
            !(await args.stockCountLine.isAddedDuringCount) &&
            (!args.isCascading || (await args.stockCountLine.document).$.status === 'modified')
        )
            return context.localize(
                '@sage/xtrem-stock/nodes__stock_count_line__delete_not_allowed',
                'A line that was added during the start of the stock count cannot be deleted. Stock count {{number}}, line {{lineNumber}}.',
                { number: await args.stockCountLine.documentNumber, lineNumber: await args.stockCountLine._sortValue },
            );

        return '';
    }

    // eslint-disable-next-line class-methods-use-this
    async getAllStockRecordsID(stockCount: xtremStock.nodes.StockCount): Promise<xtremStockData.nodes.Stock[]> {
        const locations = (await stockCount.locations) ?? [];
        const zones = (await stockCount.zones) ?? [];
        return (
            await stockCount.itemSites
                .map(itemSite => {
                    if (!locations.length && !zones.length) {
                        return itemSite.countStockRecords.toArray();
                    }
                    return itemSite.countStockRecords
                        .filter(async stockRecord => {
                            const location = await stockRecord.location;
                            if (!location) return false;
                            if (locations.includes(location)) return true;
                            if (zones.includes(await location.locationZone)) return true;
                            return false;
                        })
                        .toArray();
                })
                .toArray()
        ).flat(1);
    }

    // eslint-disable-next-line class-methods-use-this
    async getAllSelectedStockRecords(
        context: Context,
        selectedRecords: xtremStock.interfaces.StockCountFilterSelection,
    ): Promise<xtremStockData.nodes.Stock[]> {
        const selectedItemSites = selectedRecords.stockCount.itemSites.filter(itemSite => {
            return selectedRecords.itemSites?.includes(itemSite._id) && itemSite.hasStockRecords;
        });
        const locations = (await selectedRecords.stockCount.locations) ?? [];
        const zones = (await selectedRecords.stockCount.zones) ?? [];

        const stockRecordsFromItemSite = (
            await selectedItemSites
                .map(itemSite => {
                    if (!locations.length && !zones.length) {
                        return itemSite.countStockRecords
                            .map(stockRecord => {
                                return stockRecord._id;
                            })
                            .toArray();
                    }
                    return itemSite.countStockRecords
                        .filter(async stockRecord => {
                            const location = await stockRecord.location;
                            if (!location) return false;
                            if (locations.includes(location)) return true;
                            if (zones.includes(await location.locationZone)) return true;
                            return false;
                        })
                        .map(stockRecord => {
                            return stockRecord._id;
                        })
                        .toArray();
                })
                .toArray()
        ).flat(1);

        const allStockRecords = [...stockRecordsFromItemSite, ...selectedRecords.stockRecords];

        return context
            .query(xtremStockData.nodes.Stock, {
                filter: {
                    _id: { _in: allStockRecords },
                },
            })
            .toArray();
    }

    // eslint-disable-next-line class-methods-use-this
    getStockRecordsToStockCountLine(
        context: Context,
        selectedRecords: xtremStock.interfaces.StockCountFilterSelection,
    ): Promise<xtremStockData.nodes.Stock[]> {
        return selectedRecords.allSelected
            ? xtremStock.functions.stockCountLib.getAllStockRecordsID(selectedRecords.stockCount)
            : xtremStock.functions.stockCountLib.getAllSelectedStockRecords(context, {
                  stockCount: selectedRecords.stockCount,
                  stockRecords: selectedRecords.stockRecords,
                  itemSites: selectedRecords.itemSites,
                  allSelected: selectedRecords.allSelected,
              });
    }

    // eslint-disable-next-line class-methods-use-this
    getItemSiteToStockCountLine(
        selectedRecords: xtremStock.interfaces.StockCountFilterSelection,
    ): Promise<xtremMasterData.nodes.ItemSite[]> {
        return selectedRecords.allSelected
            ? selectedRecords.stockCount.itemSites
                  ?.filter(async itemSite => {
                      return !(await itemSite.hasStockRecords);
                  })
                  .toArray()
            : selectedRecords.stockCount.itemSites
                  ?.filter(async itemSite => {
                      return selectedRecords.itemSites?.includes(itemSite._id) && !(await itemSite.hasStockRecords);
                  })
                  .toArray();
    }

    // eslint-disable-next-line class-methods-use-this
    async addLineWithoutStockToStockCount(
        item: xtremMasterData.nodes.Item,
    ): Promise<NodeCreateData<xtremStock.nodes.StockCountLine>> {
        const stockCountLineToCreate: NodeCreateData<xtremStock.nodes.StockCountLine> = {
            item,
            stockStatus: null,
            location: null,
            zone: null,
            lot: null,
            stockUnit: await item.stockUnit,
            quantityInStockUnit: 0,
            stockRecord: null,
            isNonStockItem: true,
        };

        return {
            ...stockCountLineToCreate,
        };
    }

    // eslint-disable-next-line class-methods-use-this
    async addLineToStockCount(
        stockCount: xtremStock.nodes.StockCount,
        stockRecord: xtremStockData.nodes.Stock,
    ): Promise<NodeCreateData<xtremStock.nodes.StockCountLine>> {
        const lot = await stockRecord.lot;

        const stockCountLineToCreate: NodeCreateData<xtremStock.nodes.StockCountLine> = {
            item: await stockRecord.item,
            stockStatus: await stockRecord.status,
            location: await stockRecord.location,
            zone: await (await stockRecord.location)?.locationZone,
            lot: await stockRecord.lot,
            owner: await stockRecord.owner,
            stockUnit: await stockRecord.stockUnit,
            quantityInStockUnit: await stockRecord.quantityInStockUnit,
            stockRecord,
            isCreatedFromStockRecord: true,
        };

        const stockAdjustmentDetailToCreate: NodeCreateData<xtremStockData.nodes.StockAdjustmentDetail> = {
            effectiveDate: await stockCount.effectiveDate,
            site: await stockCount.stockSite,
            owner: await (await stockCount.stockSite).id,
            item: await stockRecord.item,
            stockUnit: await stockRecord.stockUnit,
            existingLot: null,
            lotCreateData: null,
            location: await stockRecord.location,
            status: await stockRecord.status,
            quantityInStockUnit: await stockRecord.quantityInStockUnit,
            stockRecord,
            reasonCode: '#Increase quantity',
        };

        const stockDetailLotToCreate: NodeCreateData<xtremStockData.nodes.StockDetailLot> | null = lot
            ? {
                  lot,
                  lotNumber: '',
                  sublot: await lot.sublot,
                  supplierLot: await lot.supplierLot,
                  expirationDate: await lot.expirationDate,
              }
            : null;

        return {
            ...stockCountLineToCreate,
            stockDetails: [{ ...stockAdjustmentDetailToCreate, stockDetailLot: stockDetailLotToCreate }],
        };
    }

    // eslint-disable-next-line class-methods-use-this
    nonStockItemExists(
        context: Context,
        item: xtremMasterData.nodes.Item,
        site: xtremSystem.nodes.Site,
    ): Promise<number> {
        return context.queryCount(xtremStockData.nodes.Stock, {
            filter: { site, item },
        });
    }
}

export const stockCountLib = StockCountLib.instance;
