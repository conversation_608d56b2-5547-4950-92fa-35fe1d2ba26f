import type { Filter } from '@sage/xtrem-client';
import type { Stock } from '@sage/xtrem-stock-data-api';

type FilterWithAndArray<T> = Filter<T> & { _and: Filter<T>[] };

function ensureAndArray(filter: Filter<Stock>): FilterWithAndArray<Stock> {
    const updatedFilter = { ...filter };

    if (!updatedFilter._and) {
        updatedFilter._and = [];
    }

    return updatedFilter as FilterWithAndArray<Stock>;
}

function filterLastCountDate(filter: FilterWithAndArray<Stock>, stockSite: string, lastCountDate?: string | null) {
    const updateFilter = { ...filter };

    if (lastCountDate) {
        updateFilter._and.push({
            item: {
                itemSites: {
                    _atLeast: 1,
                    site: stockSite,
                    // TODO: TS 5.2 added any cast
                    _or: [{ lastCountDate: undefined }, { lastCountDate: { _lt: lastCountDate } }] as any,
                },
            },
        });
    }

    return updateFilter;
}

function filterItem(filter: FilterWithAndArray<Stock>, fromItemId?: string, toItemId?: string) {
    const updatedFilter = { ...filter };

    updatedFilter._and.push({
        item: {
            _and: [{ isStockManaged: true }],
        },
    });

    if (fromItemId) {
        updatedFilter._and.push({ item: { id: { _gte: fromItemId } } });
    }

    if (toItemId) {
        updatedFilter._and.push({ item: { id: { _lte: toItemId } } });
    }

    return updatedFilter;
}

function filterLocation(filter: FilterWithAndArray<Stock>, locations?: Array<string>) {
    const updatedFilter = { ...filter };

    if (locations?.length) {
        updatedFilter._and.push({ location: { _id: { _in: locations } } });
    }

    return updatedFilter;
}

function filterZones(filter: FilterWithAndArray<Stock>, zones?: Array<string>) {
    const updatedFilter = { ...filter };

    if (zones?.length) {
        updatedFilter._and.push({ location: { locationZone: { _id: { _in: zones } } } });
    }

    return updatedFilter;
}

function filterInTransit(filter: FilterWithAndArray<Stock>, includeInTransit?: boolean) {
    const updatedFilter = { ...filter };

    if (!includeInTransit) {
        updatedFilter._and.push({ isInTransit: false });
    }

    return updatedFilter;
}

export function stockCountFilterCriteria(
    stockSite: string,
    fromItemId?: string,
    toItemId?: string,
    lastCountDate?: string | null,
    locations?: Array<string>,
    zones?: Array<string>,
    includeInTransit?: boolean,
) {
    // Base filter
    let filter: FilterWithAndArray<Stock> = ensureAndArray({ _and: [{ site: { _id: stockSite } }] });

    filter = filterLastCountDate(filter, stockSite, lastCountDate);
    filter = filterItem(filter, fromItemId, toItemId);
    filter = filterLocation(filter, locations);
    filter = filterZones(filter, zones);
    filter = filterInTransit(filter, includeInTransit);

    return filter;
}
