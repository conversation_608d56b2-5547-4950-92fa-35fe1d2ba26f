import type { Reference } from '@sage/xtrem-core';
import { decorators, SubNodeExtension2 } from '@sage/xtrem-core';
import * as xtremStockData from '@sage/xtrem-stock-data';
import * as xtremStock from '..';

@decorators.subNodeExtension2<StockIssueDetailExtension>({
    extends: () => xtremStockData.nodes.StockIssueDetail,
})
export class StockIssueDetailExtension extends SubNodeExtension2<xtremStockData.nodes.StockIssueDetail> {
    @decorators.referencePropertyOverride<StockIssueDetailExtension, 'startingSerialNumber'>({
        async control(cx, val) {
            if (val && (await this.stockRecord)) {
                await xtremStockData.events.control.serialNumber.serialNumberControl(
                    cx,
                    await xtremStock.functions.stockEngine.getSerialNumbersForStockDetail(
                        this.$.context,
                        val,
                        await this.quantityInStockUnit,
                    ),
                    (await this.stockRecord)!,
                );
            }
        },
    })
    readonly startingSerialNumber: Reference<xtremStockData.nodes.SerialNumber | null>;
}

declare module '@sage/xtrem-stock-data/lib/nodes/stock-issue-detail' {
    interface StockIssueDetail extends StockIssueDetailExtension {}
}
