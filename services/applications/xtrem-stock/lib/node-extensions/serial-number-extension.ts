import { decorators, NodeExtension } from '@sage/xtrem-core';
import * as xtremStockData from '@sage/xtrem-stock-data';
import * as xtremStock from '..';

@decorators.nodeExtension<SerialNumberExtension>({
    extends: () => xtremStockData.nodes.SerialNumber,
    async saveEnd() {
        await xtremStock.events.save.serialNumberExtension.updateStockCount(this);
    },
})
export class SerialNumberExtension extends NodeExtension<xtremStockData.nodes.SerialNumber> {}

declare module '@sage/xtrem-stock-data/lib/nodes/serial-number' {
    interface SerialNumber extends SerialNumberExtension {}
}
