import type { Collection, Context, NodeQueryFilter, date } from '@sage/xtrem-core';
import { BusinessRuleError, NodeExtension, decorators } from '@sage/xtrem-core';
import { DateValue } from '@sage/xtrem-date-time';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremStockData from '@sage/xtrem-stock-data';
import * as xtremStock from '..';

xtremStockData.nodeExtensions.ItemSiteExtension.computeCountingInProgress.push(async instance => {
    return (
        (await instance.$.context.queryCount(xtremStock.nodes.StockCountLine, {
            filter: {
                document: {
                    stockSite: (await instance.site)._id,
                    status: { _in: ['countInProgress', 'counted'] },
                },
                item: (await instance.item)._id,
                status: { _ne: 'excluded' },
            },
        })) > 0
    );
});

@decorators.nodeExtension<ItemSiteExtension>({
    extends: () => xtremMasterData.nodes.ItemSite,
})
export class ItemSiteExtension extends NodeExtension<xtremMasterData.nodes.ItemSite> {
    @decorators.collectionProperty<ItemSiteExtension, 'countStockRecords'>({
        isPublished: true,
        lookupAccess: true,
        node: () => xtremStockData.nodes.Stock,
        join: {
            item() {
                return this.item;
            },
            site() {
                return this.site;
            },
            isInTransit() {
                return false;
            },
            isStockCountingInProgress() {
                return false;
            },
        },
    })
    readonly countStockRecords: Collection<xtremStockData.nodes.Stock>;

    @decorators.booleanProperty<ItemSiteExtension, 'hasStockRecords'>({
        isPublished: true,
        async getValue() {
            return (await this.countStockRecords.length) > 0;
        },
    })
    readonly hasStockRecords: Promise<boolean>;

    @decorators.dateProperty<ItemSiteExtension, 'lastCountDate'>({
        isClearedByReset: true,
        isStored: true,
        isPublished: true,
        isNullable: true,
        duplicatedValue: null,
    })
    readonly lastCountDate: Promise<date | null>;

    /**
     * Returns the quantity in stock + unit cost + total value (=quantity*unit cost) of an item-site
     * @param {Context} context
     * @param {xtremStock.interfaces.StockValuationSearch} searchCriteria
     * @returns {StockValuationResult}
     */
    @decorators.query<typeof ItemSiteExtension, 'getStockValuation'>({
        isPublished: true,
        parameters: [
            {
                name: 'searchCriteria',
                type: 'object',
                isMandatory: true,
                properties: {
                    itemSite: 'integer',
                    dateOfValuation: 'date',
                },
            },
        ],
        return: {
            type: 'object',
            properties: {
                quantityInStock: 'decimal',
                unitCost: 'decimal',
                stockValue: 'decimal',
            },
        },
    })
    static async getStockValuation(
        context: Context,
        searchCriteria: xtremStock.interfaces.StockValuationSearch,
    ): Promise<xtremStock.interfaces.StockValuationResult> {
        const itemSite: xtremMasterData.nodes.ItemSite = await context.read(xtremMasterData.nodes.ItemSite, {
            _id: searchCriteria.itemSite,
        });

        if (!itemSite) {
            throw new BusinessRuleError(
                context.localize(
                    '@sage/xtrem-stock/nodes__item-site-extension__no_item_site',
                    `The item-site corresponding to following criteria doesn't exist ${searchCriteria}`,
                ),
            );
        }
        if (!(await (await itemSite.item).isStockManaged))
            throw new BusinessRuleError(
                context.localize(
                    '@sage/xtrem-stock/nodes__item-site-extension__item_site_not_stock_managed',
                    `This item-site is not stock managed`,
                ),
            );

        const today = DateValue.today();
        let calculationDate = searchCriteria.dateOfValuation ?? today;
        if (calculationDate.compare(today) > 0) calculationDate = today;

        // Get aggregation of last transactions up to calculationDate
        // if calculationDate='2023-05-31T00:00:00.000Z', we want to select the transactions after '2023-06-01T00:00:00.000Z'
        // we don't want to select transactions between '2023-05-31T00:00:00.000Z' and '2023-06-01T00:00:00.000Z'
        // => addDays(1) (see XT-34515)
        const itemId = (await itemSite.item)._id;
        const siteId = (await itemSite.site)._id;
        const referenceDate = calculationDate.addDays(1);

        const transactionsAggregation = (await context
            .queryAggregate(xtremStockData.nodes.StockJournal, {
                filter: {
                    item: itemId,
                    site: siteId,
                    effectiveDate: { _gte: referenceDate },
                },
                group: {},
                values: { quantityInStockUnit: { sum: true }, orderAmount: { sum: true } },
            })
            .map(total => {
                return {
                    quantityInStockUnit: total.values.quantityInStockUnit.sum ?? 0,
                    orderAmount: total.values.orderAmount.sum ?? 0,
                };
            })
            .at(0)) ?? { quantityInStockUnit: 0, orderAmount: 0 };

        // Remove quantity of last transactions to current quantity
        const quantityInStock = (await itemSite.inStockQuantity) - transactionsAggregation.quantityInStockUnit;

        // Get stock valuation
        switch (await itemSite.valuationMethod) {
            case 'standardCost': {
                // For standard cost, get the standard cost for calculationDate
                const unitCost = await xtremMasterData.nodes.ItemSiteCost.getItemSiteCost(
                    context,
                    calculationDate,
                    await itemSite.item,
                    await itemSite.site,
                );
                return {
                    quantityInStock,
                    unitCost,
                    stockValue: quantityInStock * unitCost,
                };
            }
            case 'averageCost': {
                // For average cost, remove last transactions' value to the current value
                const stockValue = (await itemSite.stockValuationAtAverageCost) - transactionsAggregation.orderAmount;
                return {
                    quantityInStock,
                    stockValue: (await itemSite.stockValuationAtAverageCost) - transactionsAggregation.orderAmount,
                    unitCost: quantityInStock !== 0 ? stockValue / quantityInStock : 0,
                };
            }
            case 'fifoCost':
                // For FIFO cost, ...
                return ItemSiteExtension.getStockValuationFifo({
                    context,
                    filter: {
                        item: itemId,
                        site: siteId,
                        effectiveDate: { _lt: referenceDate },
                    },
                    quantityInStock,
                });
            default:
                // Note: This case should never occur. But as usual if you create a new valuation method you
                //       will get this message. (This will prevent the chaos happening when a new method is started
                //       but valuation is not yet supported -- like for 'fifoCost'.)
                throw new BusinessRuleError(
                    context.localize(
                        '@sage/xtrem-stock/nodes__item-site-extension__unsupported_valuation_method',
                        `Valuation method ${await itemSite.valuationMethod} is not yet supported.`,
                    ),
                );
        }
    }

    static async getStockValuationFifo(param: {
        context: Context;
        filter: NodeQueryFilter<xtremStockData.nodes.StockJournal>;
        quantityInStock: number;
    }): Promise<xtremStock.interfaces.StockValuationResult> {
        const { context, filter, quantityInStock } = param; // (For convenience reason)

        const stockJournalRecords = context.query(xtremStockData.nodes.StockJournal, {
            filter,
        });

        // Loop over stock journals and calculate stock value by summing up the movement amounts.
        const stockValue = await stockJournalRecords.sum(stockJournal => stockJournal.movementAmount);

        return {
            quantityInStock,
            unitCost: quantityInStock !== 0 ? stockValue / quantityInStock : 0,
            stockValue,
        };
    }
}

declare module '@sage/xtrem-master-data/lib/nodes/item-site' {
    interface ItemSite extends ItemSiteExtension {}
}
