import type { Reference } from '@sage/xtrem-core';
import { decorators, SubNodeExtension2 } from '@sage/xtrem-core';
import type * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremStockData from '@sage/xtrem-stock-data';
import type * as xtremSystem from '@sage/xtrem-system';
import * as xtremStock from '..';

@decorators.subNodeExtension2<StockAdjustmentDetailExtension>({
    extends: () => xtremStockData.nodes.StockAdjustmentDetail,
})
export class StockAdjustmentDetailExtension extends SubNodeExtension2<xtremStockData.nodes.StockAdjustmentDetail> {
    @decorators.referencePropertyOverride<StockAdjustmentDetailExtension, 'item'>({
        async control(cx, item) {
            await xtremStock.events.controls.stockAdjustmentDetail.controlStockDetailItem(cx, {
                documentLine: await this.documentLine,
                stockDetailValue: item,
            });
        },
    })
    readonly item: Reference<xtremMasterData.nodes.Item>;

    @decorators.referencePropertyOverride<StockAdjustmentDetailExtension, 'site'>({
        async control(cx, site) {
            await xtremStock.events.controls.stockAdjustmentDetail.controlStockDetailSite(cx, {
                documentLine: await this.documentLine,
                stockDetailValue: site,
            });
        },
    })
    readonly site: Reference<xtremSystem.nodes.Site>;

    @decorators.referencePropertyOverride<StockAdjustmentDetailExtension, 'location'>({
        async control(cx, location) {
            await xtremStock.events.controls.stockAdjustmentDetail.controlStockDetailLocation(cx, {
                documentLine: await this.documentLine,
                stockDetailValue: location,
            });
        },
    })
    readonly location: Reference<xtremMasterData.nodes.Location | null>;

    @decorators.referencePropertyOverride<StockAdjustmentDetailExtension, 'stockUnit'>({
        async control(cx, stockUnit) {
            await xtremStock.events.controls.stockAdjustmentDetail.controlStockDetailStockUnit(cx, {
                documentLine: await this.documentLine,
                stockDetailValue: stockUnit,
            });
        },
    })
    readonly stockUnit: Reference<xtremMasterData.nodes.UnitOfMeasure>;

    @decorators.referencePropertyOverride<StockAdjustmentDetailExtension, 'status'>({
        async control(cx, status) {
            await xtremStock.events.controls.stockAdjustmentDetail.controlStockDetailStatus(cx, {
                documentLine: await this.documentLine,
                stockDetailValue: status,
            });
        },
    })
    readonly status: Reference<xtremStockData.nodes.StockStatus>;

    @decorators.stringPropertyOverride<StockAdjustmentDetailExtension, 'owner'>({
        async control(cx, owner) {
            await xtremStock.events.controls.stockAdjustmentDetail.controlStockDetailOwner(cx, {
                documentLine: await this.documentLine,
                stockDetailValue: owner,
            });
        },
    })
    readonly owner: Promise<string>;

    @decorators.referencePropertyOverride<StockAdjustmentDetailExtension, 'stockDetailLot'>({
        async control(cx, stockDetailLot) {
            await xtremStock.events.controls.stockAdjustmentDetail.controlStockDetailLot(cx, this, stockDetailLot);
        },
    })
    readonly stockDetailLot: Reference<xtremStockData.nodes.StockDetailLot | null>;
}

declare module '@sage/xtrem-stock-data/lib/nodes/stock-adjustment-detail' {
    interface StockAdjustmentDetail extends StockAdjustmentDetailExtension {}
}
