import type {
    AsyncArray,
    Context,
    NodeCreateData,
    NodeUpdateData,
    Reference,
    decimal,
    integer,
} from '@sage/xtrem-core';
import {
    BusinessRuleError,
    Logger,
    NodeExtension,
    ValidationSeverity,
    date,
    datetime,
    decorators,
    useDefaultValue,
} from '@sage/xtrem-core';
import * as xtremFinanceData from '@sage/xtrem-finance-data';
import * as xtremMasterData from '@sage/xtrem-master-data';
import type { InitialNotificationAction } from '@sage/xtrem-shared';
import * as xtremStockData from '@sage/xtrem-stock-data';
import type * as xtremSystem from '@sage/xtrem-system';
import * as xtremTechnicalData from '@sage/xtrem-technical-data';
import * as xtremStock from '..';

const logger = Logger.getLogger(__filename, 'ItemSiteCost');

@decorators.nodeExtension<ItemSiteCostExtension>({
    extends: () => xtremMasterData.nodes.ItemSiteCost,

    async controlDelete(cx) {
        if (!this.allowDeletion) return; // Item-site-cost cannot be deleted because an error has already been detected.
        if ((await this.itemSite).deletionInProgress) return; // Item-site can be deleted. So can item-site-cost.

        if (
            (await (await this.itemSite).valuationMethod) === 'standardCost' &&
            (await (
                await (
                    await this.itemSite
                ).item
            ).isStockManaged) &&
            (await this.fromDate).compare(date.today()) === 0 &&
            (await this.stockValueChange)
        ) {
            this.allowDeletion = false; // Signal that we refuse to delete.
            cx.addDiagnose(
                ValidationSeverity.error,
                cx.localize(
                    '@sage/xtrem-stock/nodes__item-site-cost__failed_deletion_impossible_if_transactions',
                    'Delete not allowed. The item-site cost start date is equal to the current date and a stock value change exists for this item-site cost.',
                ),
            );
        }
    },

    async saveBegin() {
        // Control if all dimensions for later finance posting are entered correctly
        // unfortunately this control can't be done in the controlBegin because we need to create a transient temporary
        // stock count for the checks
        const stockValueChange = await ItemSiteCostExtension.instantiateStockValueChangeFromStandardItemSiteCost(
            this.$.context,
            this as xtremMasterData.nodes.ItemSiteCost,
            true,
        );
        if (stockValueChange !== null) {
            const postResult: xtremFinanceData.interfaces.MutationResult =
                await xtremStock.functions.financeIntegration.stockValueChangeControlFromNotificationPayloadErrors(
                    this.$.context,
                    stockValueChange,
                );

            if (postResult.message.length) throw new BusinessRuleError(postResult.message);
        }

        const site = await (await this.itemSite).site;
        const companyId = (await (await (await this.itemSite).site).legalCompany)._id;
        const item = await (await this.itemSite).item;

        if ((await this.storedAttributes) === null) {
            const defaultAttributes = await xtremFinanceData.functions.getDefaultAttributes(this.$.context, {
                dimensionDefinitionLevel: 'stockDirect',
                companyId,
                site,
                item,
            });
            await this.$.set({ storedAttributes: defaultAttributes });
        }

        if ((await this.storedDimensions) === null) {
            const defaultDimensions = xtremFinanceData.functions.getDefaultDimensions(this.$.context, {
                dimensionDefinitionLevel: 'stockDirect',
                companyId: (await (await (await this.itemSite).site).legalCompany)._id,
                site,
                item,
            });
            await this.$.set({ storedDimensions: await defaultDimensions });
        }
    },

    // Create and post a stock value change if the fromDate of the new ItemSiteCost === today
    async saveEnd() {
        await ItemSiteCostExtension.createStockValueChangeFromStandardItemSiteCost(
            this.$.context,
            this as xtremMasterData.nodes.ItemSiteCost,
        );
    },
})
export class ItemSiteCostExtension extends NodeExtension<xtremMasterData.nodes.ItemSiteCost> {
    @decorators.jsonProperty<ItemSiteCostExtension, 'storedDimensions'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
        dataType: () => xtremFinanceData.dataTypes.storedDimensionsDataType,
        dependsOn: ['itemSite'],
        // TODO: determine why defaultValue is not executed

        async defaultValue() {
            const site = await (await this.itemSite).site;
            const companyId = (await (await (await this.itemSite).site).legalCompany)._id;
            return xtremFinanceData.functions.getDefaultDimensions(this.$.context, {
                dimensionDefinitionLevel: 'stockDirect',
                companyId,
                site,
                item: await (await this.itemSite).item,
            });
        },
    })
    readonly storedDimensions: Promise<object | null>;

    @decorators.referenceProperty<ItemSiteCostExtension, 'analyticalData'>({
        isStored: true,
        isMutable: true,
        isPublished: true,
        lookupAccess: true,
        ignoreIsActive: true,
        isFrozen() {
            return xtremFinanceData.functions.isAnalyticalDataFrozen(this);
        },
        node: () => xtremFinanceData.nodes.AnalyticalData,
        dependsOn: ['storedAttributes', 'storedDimensions'],
        async defaultValue() {
            return (await xtremFinanceData.functions.getAnalyticalDataFromStoredAttributesAndDimensions(
                this.$.context,
                await this.storedAttributes,
                await this.storedDimensions,
            )) as any;
        },
        updatedValue: useDefaultValue,
    })
    readonly analyticalData: Reference<xtremFinanceData.nodes.AnalyticalData>;

    @decorators.jsonProperty<ItemSiteCostExtension, 'storedAttributes'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
        dataType: () => xtremFinanceData.dataTypes.storedAttributesDataType,
        dependsOn: ['itemSite'],
        // TODO: determine why defaultValue is not executed

        async defaultValue() {
            const site = await (await this.itemSite).site;
            const companyId = (await (await (await this.itemSite).site).legalCompany)._id;
            return xtremFinanceData.functions.getDefaultAttributes(this.$.context, {
                dimensionDefinitionLevel: 'stockDirect',
                companyId,
                site,
                item: await (await this.itemSite).item,
            });
        },
    })
    readonly storedAttributes: Promise<xtremMasterData.interfaces.StoredAttributes | null>;

    @decorators.referenceProperty<ItemSiteCostExtension, 'stockValueChange'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
        node: () => xtremStock.nodes.StockValueChange,
    })
    readonly stockValueChange: Reference<xtremStock.nodes.StockValueChange | null>;

    @decorators.asyncMutation<typeof ItemSiteCostExtension, 'syncStockValueChange'>({
        isPublished: true,
        startsReadOnly: false,
        isSchedulable: true,
        parameters: [],
        return: {
            type: 'boolean',
        },
    })
    static async syncStockValueChange(context: Context): Promise<boolean> {
        const itemSiteCosts = context.query(xtremMasterData.nodes.ItemSiteCost, {
            filter: { fromDate: { _eq: date.today() } },
        });

        const totalCount = await itemSiteCosts.length;

        await context.batch.updateProgress({
            detail: 'synchronized',
            errorCount: 0,
            successCount: 0,
            totalCount,
            phase: 'start',
        });

        await context.batch.logMessage(
            'info',
            context.localize(
                '@sage/xtrem-stock/stock_value_change_from_item_site_cost_start',
                'Stock value change from item-site cost has started.',
            ),
        );

        await itemSiteCosts.forEach(async (itemSiteCost, i) => {
            if (await context.batch.isStopRequested()) {
                await context.batch.logMessage(
                    'info',
                    context.localize(
                        '@sage/xtrem-stock/stock_value_change_from_item_site_cost_stop',
                        'Stop changing stock values on {{stopDate}} ',
                        {
                            stopDate: Date.now().toString(),
                        },
                    ),
                );

                await context.batch.confirmStop();
                return;
            }

            await context.batch.updateProgress({
                detail: 'processing',
                errorCount: 0,
                successCount: i,
                totalCount,
                phase: 'processing',
            });
            await ItemSiteCostExtension.createStockValueChangeFromStandardItemSiteCost(context, itemSiteCost);
        });

        await context.batch.updateProgress({
            detail: 'complete',
            errorCount: 0,
            successCount: totalCount,
            totalCount,
            phase: 'done',
        });

        await context.batch.logMessage(
            'result',
            context.localize(
                '@sage/xtrem-stock/stock_value_change_from_item_site_cost_complete',
                'Stock value change from item-site cost is complete.',
            ),
        );

        return true;
    }

    /**
     * For a given item site cost, we instantiate and return a StockValueChange node (for some controls or saving and posting it).
     * @param context: A context
     * @param itemSiteCost The item site cost record
     * @param forCheckOnly if true the created instance is transient, else not
     * @return A StockValueChange object
     */
    private static async instantiateStockValueChangeFromStandardItemSiteCost(
        context: Context,
        itemSiteCost: xtremMasterData.nodes.ItemSiteCost,
        forCheckOnly = false,
    ): Promise<xtremStock.nodes.StockValueChange | null> {
        if (!forCheckOnly && (await itemSiteCost.fromDate).value !== date.today().value) {
            return null;
        }

        if (forCheckOnly && (await itemSiteCost.fromDate).value <= date.today().value) {
            return null;
        }

        const itemSite = await itemSiteCost.itemSite;

        if ((await itemSite.valuationMethod) !== 'standardCost') {
            return null;
        }

        if (await itemSiteCost.stockValueChange) {
            return null;
        }

        const inStockQuantity = await itemSite.inStockQuantity;
        if (inStockQuantity === 0) {
            return null;
        }

        const site = await itemSite.site;
        const item = await itemSite.item;
        const previousCost = await xtremMasterData.nodes.ItemSiteCost.getItemSiteCost(
            context,
            date.today().addDays(-1),
            item,
            site,
        );

        const amount = previousCost * inStockQuantity;
        const newAmount = (await itemSiteCost.unitCost) * inStockQuantity;
        const stockValueChange = context.create(
            xtremStock.nodes.StockValueChange,
            {
                description: context.localize(
                    '@sage/xtrem-stock/stock_value_change_from_item_site_cost',
                    'Stock value change from item-site-cost.',
                ),
                site,
                item,
                lines: [
                    {
                        quantity: inStockQuantity,
                        unitCost: previousCost,
                        amount,
                        newUnitCost: await itemSiteCost.unitCost,
                        newAmount,
                        storedAttributes: await itemSiteCost.storedAttributes,
                        storedDimensions: await itemSiteCost.storedDimensions,
                    },
                ],
            },
            { isTransient: forCheckOnly },
        );
        const line = await (await stockValueChange).lines.elementAt(0);
        // XT-75988: set the stockDetails after the line is created so that the amount of the stock detail
        // is calculated with the potential rouding made by the line
        await line.$.set({
            stockDetails: [
                {
                    _action: 'create',
                    site,
                    item,
                    amount: (await line.newAmount) - (await line.amount),
                    valuationMethod: 'standardCost',
                    quantityInStockUnit: inStockQuantity,
                },
            ],
        });
        return stockValueChange;
    }

    private static async createStockValueChangeFromStandardItemSiteCost(
        context: Context,
        itemSiteCost: xtremMasterData.nodes.ItemSiteCost,
    ) {
        const stockValueChange = await this.instantiateStockValueChangeFromStandardItemSiteCost(context, itemSiteCost);
        if (stockValueChange !== null) {
            await stockValueChange.$.save();

            const updatedItemSiteCost = await context.read(
                xtremMasterData.nodes.ItemSiteCost,
                { _id: itemSiteCost._id },
                { forUpdate: true },
            );
            await updatedItemSiteCost.$.set({ stockValueChange, isUpdatingPreviousCost: true });
            await updatedItemSiteCost.$.save();
            await xtremStock.nodes.StockValueChange.postToStock(context, [stockValueChange._id]);
        }
    }

    private static async prepareFunctionalData(
        data: {
            requiredQuantity: decimal;
        } & xtremTechnicalData.interfaces.SimpleCallbackExplorationData<
            undefined,
            xtremStock.interfaces.BomExplorationFunctionalData
        >,
    ) {
        const { component } = data;
        const bomBaseQuantity = await data.bom.baseQuantity;

        const linkQuantity = await component.linkQuantity;
        const isFixedLinkQuantity = await component.isFixedLinkQuantity;
        const scrapFactor = await component.scrapFactor;
        const decimalPlaces = await (await component.unit)?.decimalDigits;

        const releasedQuantityForUserRequirement =
            data.parentFunctionalData?.componentRequiredQuantityForUserRequirement ?? data.requiredQuantity;

        const functionalData: xtremStock.interfaces.BomExplorationFunctionalData = {
            bomBaseQuantity,
            componentBomBaseQuantity: await (await component.bom)?.baseQuantity,
            componentRequiredQuantityForUserRequirement:
                xtremTechnicalData.functions.componentFunctions.setRequiredQuantity(
                    releasedQuantityForUserRequirement,
                    bomBaseQuantity,
                    linkQuantity,
                    isFixedLinkQuantity,
                    scrapFactor,
                    decimalPlaces,
                ),
        };

        return functionalData;
    }

    private static async reuseAlreadyProcessedData(
        data: {
            computedItemCosts: xtremStock.interfaces.ComputedItemCosts;
        } & Parameters<
            Required<
                Parameters<
                    typeof xtremTechnicalData.functions.bomLib.exploreBomSubAssemblies<
                        any,
                        xtremStock.interfaces.BomExplorationFunctionalData
                    >
                >[1]
            >['reuseAlreadyProcessedData']
        >[0],
    ) {
        let alreadyProcessedData: xtremStock.interfaces.CostSumObject | null = null;

        const item = await data.component.item;
        if (item) {
            const itemCheckedComputedCosts = data.computedItemCosts[item._id];
            if (itemCheckedComputedCosts) {
                alreadyProcessedData =
                    itemCheckedComputedCosts[data.functionalData.componentRequiredQuantityForUserRequirement] ?? null;
            }
        }
        return alreadyProcessedData;
    }

    private static async getCostRollUpResultLineCreateData(
        context: Context,
        data: {
            fromDate: date;
            inputSetId: integer;
            itemId: integer;
            itemSiteId: integer;
        },
    ) {
        // TODO: create a shared function for this and xtremMasterData.nodes.ItemSiteCost.getItemSiteCost
        const currentItemSiteStandardCost = await context
            .query(xtremMasterData.nodes.ItemSiteCost, {
                filter: {
                    itemSite: {
                        _id: data.itemSiteId,
                    },
                    fromDate: {
                        _lte: data.fromDate,
                    },
                    toDate: {
                        _gte: data.fromDate,
                    },
                },
                orderBy: {
                    fromDate: -1,
                },
                first: 1,
            })
            .at(0);

        const currentItemCosts: {
            currentMaterialCost: decimal;
            currentLaborCost: decimal;
            currentMachineCost: decimal;
            currentToolCost: decimal;
        } = {
            currentMaterialCost: (await currentItemSiteStandardCost?.materialCost) ?? 0,
            currentLaborCost: (await currentItemSiteStandardCost?.laborCost) ?? 0,
            currentMachineCost: (await currentItemSiteStandardCost?.machineCost) ?? 0,
            currentToolCost: (await currentItemSiteStandardCost?.toolCost) ?? 0,
        };

        return {
            inputSet: data.inputSetId,
            item: data.itemId,
            ...currentItemCosts,
            currentTotalCost:
                currentItemCosts.currentMaterialCost +
                currentItemCosts.currentLaborCost +
                currentItemCosts.currentMachineCost +
                currentItemCosts.currentToolCost,
        } as NodeCreateData<xtremStock.nodes.CostRollUpResultLine>;
    }

    private static async computeSubAssemblyLines(
        context: Context,
        data: {
            resultLine: xtremStock.nodes.CostRollUpResultLine;
            itemIds: number[];
            requiredQuantity: number;
            computedItemCosts: xtremStock.interfaces.ComputedItemCosts;
        },
    ): Promise<(NodeUpdateData<xtremStock.nodes.CostRollUpSubAssembly> | undefined)[]> {
        const inputSet = await data.resultLine.inputSet;
        const resultLineItem = await data.resultLine.item;
        return context
            .query(xtremTechnicalData.nodes.Component, {
                filter: {
                    lineType: 'normal',
                    billOfMaterial: {
                        item: resultLineItem,
                        site: await inputSet.site,
                    },
                    item: {
                        isManufactured: true,
                    },
                },
            })
            .map(async component => {
                if (!(await component.bom)) {
                    return undefined;
                }
                const bomBaseQuantity = await (await component.billOfMaterial).baseQuantity;

                const calculationQuantity = xtremTechnicalData.functions.componentFunctions.setRequiredQuantity(
                    data.requiredQuantity,
                    bomBaseQuantity,
                    await component.linkQuantity,
                    await component.isFixedLinkQuantity,
                    await component.scrapFactor,
                    await (
                        await component.unit
                    )?.decimalDigits,
                );

                const itemId = (await component.item)?._id ?? 0;

                const componentCosts = data.computedItemCosts[itemId][calculationQuantity];

                if (calculationQuantity === 0) {
                    return undefined;
                }
                return {
                    _action: 'create',
                    ...(await ItemSiteCostExtension.getCostRollUpResultLineCreateData(context, {
                        inputSetId: inputSet._id,
                        fromDate: await inputSet.fromDate,
                        itemId,
                        itemSiteId: (await component.itemSite)?._id ?? 0,
                    })),
                    componentNumber: await component.componentNumber,
                    calculationQuantity,
                    materialCost: componentCosts?.materialCost,
                    laborCost: componentCosts?.laborCost,
                    machineCost: componentCosts?.machineCost,
                    toolCost: componentCosts?.toolCost,
                } as NodeUpdateData<xtremStock.nodes.CostRollUpSubAssembly>;
            })
            .filter(component => !!component)
            .toArray();
    }

    private static async updateCostRollUpInputSetWithSubAssemblies(
        context: Context,
        data: {
            costRollUpInputSet: xtremStock.nodes.CostRollUpInputSet;
            itemIds: number[];
            requiredQuantity: number;
            computedItemCosts: xtremStock.interfaces.ComputedItemCosts;
        },
    ) {
        await context.runInWritableContext(async childContext => {
            const writableCostRollUpInputSet = await childContext.read(
                xtremStock.nodes.CostRollUpInputSet,
                {
                    _id: data.costRollUpInputSet._id,
                },
                {
                    forUpdate: true,
                },
            );

            await writableCostRollUpInputSet.$.set({
                status: 'completed',
                resultLines: await writableCostRollUpInputSet.resultLines
                    .map(async resultLine => {
                        return {
                            _sortValue: await resultLine._sortValue,
                            _action: 'update',
                            subAssemblyLines: await ItemSiteCostExtension.computeSubAssemblyLines(context, {
                                resultLine,
                                itemIds: data.itemIds,
                                requiredQuantity: data.requiredQuantity,
                                computedItemCosts: data.computedItemCosts,
                            }),
                        } as NodeUpdateData<xtremStock.nodes.CostRollUpResultLine>;
                    })
                    .toArray(),
            });

            await writableCostRollUpInputSet.$.save();
        });
    }

    /**
     * Computes the costs of an itemSite's routing according to the required quantity
     * @param computationData.itemSite the itemSite we want to compute the routing costs for.
     * @param computationData.currentItemCostSums the sum object we will update with the results.
     * @param computationData.requiredQuantity the quantity we want to compute the routing costs for.
     */
    private static async computeItemRoutingCostsAtRequiredQuantity(computationData: {
        itemSite: xtremMasterData.nodes.ItemSite;
        requiredQuantity: decimal;
        currentItemCostSums: xtremStock.interfaces.CostSumObject;
    }) {
        const currentItemCostSums = { ...computationData.currentItemCostSums };

        const routing = await computationData.itemSite.routing;

        if (routing) {
            const runQuantity = computationData.requiredQuantity / (await routing.batchQuantity);

            currentItemCostSums.laborCost = (await routing.laborSetupCost) + (await routing.laborCost) * runQuantity;

            currentItemCostSums.machineCost =
                (await routing.machineSetupCost) + (await routing.machineCost) * runQuantity;

            currentItemCostSums.toolCost = (await routing.toolSetupCost) + (await routing.toolCost) * runQuantity;
        }
        return currentItemCostSums;
    }

    /**
     * Computes the costs of a bom according to the required quantity
     * @param computationData.bom the bom we want to compute the costs for.
     * @param computationData.currentItemCostSums the sum object we will update with the results.
     * @param computationData.requiredQuantity the quantity we want to compute the bom costs for.
     * @param computationData.computedItemCosts the memory object we store all the already computed sum objects.
     */
    private static async computeItemBomCostsAtRequiredQuantity(
        context: Context,
        computationData: {
            site: xtremSystem.nodes.Site;
            bom: xtremTechnicalData.nodes.BillOfMaterial;
            currentItemCostSums: xtremStock.interfaces.CostSumObject;
            calculationQuantity: decimal;
            fromDate: date;
            computedItemCosts: xtremStock.interfaces.ComputedItemCosts;
            usesComponentStandardCost?: boolean;
            includesRouting?: boolean;
            canComputeSublevels?: boolean;
        },
    ) {
        const currentItemCostSums = { ...computationData.currentItemCostSums };
        const { bom } = computationData;

        const bomItemCosts = (
            await xtremTechnicalData.functions.bomLib.exploreBomSubAssemblies(context, {
                bom,
                isSingleLevel: !computationData.canComputeSublevels,
                prepareFunctionalData: (
                    explorationData: xtremTechnicalData.interfaces.SimpleCallbackExplorationData<
                        undefined,
                        xtremStock.interfaces.BomExplorationFunctionalData
                    >,
                ) =>
                    ItemSiteCostExtension.prepareFunctionalData({
                        ...explorationData,
                        requiredQuantity: computationData.calculationQuantity,
                    }),
                reuseAlreadyProcessedData: explorationData =>
                    explorationData.counters.currentLevel === 0
                        ? ItemSiteCostExtension.reuseAlreadyProcessedData({
                              ...explorationData,
                              computedItemCosts: computationData.computedItemCosts,
                          })
                        : null,
                ascendingExplorationCallback: async explorationData => {
                    const componentItem = await explorationData.component.item;
                    const requiredQuantity = explorationData.functionalData.componentRequiredQuantityForUserRequirement;

                    let componentCostSumsAtRequiredQuantity: xtremStock.interfaces.CostSumObject | null | undefined =
                        null;

                    if (componentItem) {
                        const componentCostSums = computationData.computedItemCosts[componentItem._id] ?? {};
                        componentCostSumsAtRequiredQuantity = componentCostSums[requiredQuantity];

                        if (!componentCostSumsAtRequiredQuantity) {
                            componentCostSumsAtRequiredQuantity =
                                await ItemSiteCostExtension.computeItemCostAtRequiredQuantity(context, {
                                    site: computationData.site,
                                    item: componentItem,
                                    calculationQuantity: requiredQuantity,
                                    fromDate: computationData.fromDate,
                                    computedItemCosts: computationData.computedItemCosts,
                                    usesComponentStandardCost: computationData.usesComponentStandardCost,
                                    includesRouting: computationData.includesRouting,
                                });

                            computationData.computedItemCosts[componentItem._id] = {
                                ...computationData.computedItemCosts[componentItem._id],
                                [requiredQuantity]: componentCostSumsAtRequiredQuantity,
                            };
                        }
                    }

                    return explorationData.counters.currentLevel === 0 ? componentCostSumsAtRequiredQuantity : null;
                },
            })
        ).reduce(
            (sums: xtremStock.interfaces.CostSumObject, componentCostSums) => ({
                ...sums,
                materialCost: sums.materialCost + (componentCostSums?.materialCost ?? 0),
                laborCost: sums.laborCost + (componentCostSums?.laborCost ?? 0),
                machineCost: sums.machineCost + (componentCostSums?.machineCost ?? 0),
                toolCost: sums.toolCost + (componentCostSums?.toolCost ?? 0),
            }),
            currentItemCostSums,
        );

        if (bomItemCosts) {
            currentItemCostSums.materialCost = bomItemCosts.materialCost;
            currentItemCostSums.laborCost = bomItemCosts.laborCost;
            currentItemCostSums.machineCost = bomItemCosts.machineCost;
            currentItemCostSums.toolCost = bomItemCosts.toolCost;
        }

        return currentItemCostSums;
    }

    /**
     * Retrieve the costs of an itemSite
     * @param computationData.currentItemCostSums the sum object we will update with the results.
     * @param computationData.requiredQuantity the quantity we want to compute the bom costs for.
     * @param computationData.computedItemCosts the memory object we store all the already computed sum objects.
     */
    private static async getCostsFromItemSite(
        context: Context,
        computationData: {
            site: xtremSystem.nodes.Site;
            item: xtremMasterData.nodes.Item;
            itemSite: xtremMasterData.nodes.ItemSite;
            requiredQuantity: decimal;
            fromDate: date;
            currentItemCostSums: xtremStock.interfaces.CostSumObject;
            usesComponentStandardCost?: boolean;
        },
    ) {
        const currentItemCostSums = { ...computationData.currentItemCostSums };
        if (computationData.usesComponentStandardCost) {
            currentItemCostSums.unitCost = await xtremMasterData.nodes.ItemSiteCost.getItemSiteCost(
                context,
                computationData.fromDate,
                computationData.item,
                computationData.site,
            );
        } else {
            switch (await computationData.itemSite.valuationMethod) {
                case 'averageCost':
                    currentItemCostSums.unitCost = await computationData.itemSite.averageCostValue;
                    break;
                case 'fifoCost':
                    currentItemCostSums.unitCost =
                        (await (
                            await xtremStockData.functions.fifoValuationLib.getLastFifoValuationTier(
                                context,
                                {
                                    item: computationData.item,
                                    site: computationData.site,
                                },
                                true,
                            )
                        )?.unitCost) ?? 0;
                    break;
                case 'standardCost':
                default:
                    currentItemCostSums.unitCost = await xtremMasterData.nodes.ItemSiteCost.getItemSiteCost(
                        context,
                        computationData.fromDate,
                        computationData.item,
                        computationData.site,
                    );
                    break;
            }
        }

        currentItemCostSums.materialCost = currentItemCostSums.unitCost * computationData.requiredQuantity;

        return currentItemCostSums;
    }

    /**
     * Computes the costs of a bom according to the required quantity
     * @param computationData.bom the bom we want to compute the costs for.
     * @param computationData.currentItemCostSums the sum object we well update with the results.
     * @param computationData.requiredQuantity the quantity we want to compute the bom costs for.
     * @param computationData.computedItemCosts the memory object we store all the already computed sum objects.
     */
    private static async computeItemCostAtRequiredQuantity(
        context: Context,
        computationData: {
            site: xtremSystem.nodes.Site;
            item: xtremMasterData.nodes.Item;
            calculationQuantity: decimal;
            fromDate: date;
            computedItemCosts: xtremStock.interfaces.ComputedItemCosts;
            usesComponentStandardCost?: boolean;
            includesRouting?: boolean;
            canComputeSublevels?: boolean;
        },
    ) {
        let currentItemCostSums: xtremStock.interfaces.CostSumObject = {
            totalCost: 0,
            unitCost: 0,
            materialCost: 0,
            laborCost: 0,
            machineCost: 0,
            toolCost: 0,
            hasResultLine: false,
        };

        const itemSite = await context.tryRead(xtremMasterData.nodes.ItemSite, {
            site: computationData.site,
            item: computationData.item,
        });

        if (itemSite && computationData.calculationQuantity > 0) {
            const itemBom = await itemSite.billOfMaterial;

            if (computationData.includesRouting) {
                currentItemCostSums = await ItemSiteCostExtension.computeItemRoutingCostsAtRequiredQuantity({
                    itemSite,
                    requiredQuantity: computationData.calculationQuantity,
                    currentItemCostSums,
                });
            }

            if (itemBom) {
                currentItemCostSums = await ItemSiteCostExtension.computeItemBomCostsAtRequiredQuantity(context, {
                    site: computationData.site,
                    bom: itemBom,
                    calculationQuantity: computationData.calculationQuantity,
                    currentItemCostSums,
                    fromDate: computationData.fromDate,
                    computedItemCosts: computationData.computedItemCosts,
                    usesComponentStandardCost: computationData.usesComponentStandardCost,
                    includesRouting: computationData.includesRouting,
                    canComputeSublevels: computationData.canComputeSublevels,
                });
            } else {
                currentItemCostSums = await ItemSiteCostExtension.getCostsFromItemSite(context, {
                    site: computationData.site,
                    item: computationData.item,
                    itemSite,
                    requiredQuantity: computationData.calculationQuantity,
                    fromDate: computationData.fromDate,
                    currentItemCostSums,
                    usesComponentStandardCost: computationData.usesComponentStandardCost,
                });
            }

            currentItemCostSums.totalCost =
                currentItemCostSums.materialCost +
                currentItemCostSums.laborCost +
                currentItemCostSums.machineCost +
                currentItemCostSums.toolCost;

            if (itemBom) {
                currentItemCostSums.unitCost = currentItemCostSums.totalCost / computationData.calculationQuantity;
            }
        }

        await logger.debugAsync(async () => {
            const json = JSON.stringify({
                usesComponentStandardCost: !!computationData.usesComponentStandardCost,
                includesRouting: !!computationData.includesRouting,
                costSums: currentItemCostSums,
            });
            return `computed cost of item ${await computationData.item.name}, for qty=${
                computationData.calculationQuantity
            }

            ${json}`;
        });

        return currentItemCostSums;
    }

    private static async computeComponentCosts(
        context: Context,
        data: Parameters<
            Required<
                Parameters<
                    typeof xtremTechnicalData.functions.bomLib.exploreBomSubAssemblies<
                        any,
                        xtremStock.interfaces.BomExplorationFunctionalData
                    >
                >[1]
            >['ascendingExplorationCallback']
        >[0] & {
            site: xtremSystem.nodes.Site;
            requiredQuantity: decimal;
            fromDate: date;
            computedItemCosts: xtremStock.interfaces.ComputedItemCosts;
            usesComponentStandardCost: boolean;
            includesRouting: boolean;
            userFilteredItemSites: AsyncArray<xtremMasterData.nodes.ItemSite>;
            inputSetId: integer;
        },
    ) {
        const { component, computedItemCosts, site, fromDate } = data;
        const componentItem = await component.item;
        const componentItemId = componentItem?._id ?? 0;
        const componentItemSiteId = (await component.itemSite)?._id ?? 0;
        const componentRequiredQuantity = data.functionalData.componentRequiredQuantityForUserRequirement ?? 0;

        let currentlyComputedItemCostSums = computedItemCosts[componentItemId];

        if (!componentItem) {
            return null;
        }

        const componentCostsAtRequiredQuantity = await ItemSiteCostExtension.computeItemCostAtRequiredQuantity(
            context,
            {
                site,
                item: componentItem,
                calculationQuantity: componentRequiredQuantity,
                fromDate,
                computedItemCosts,
                usesComponentStandardCost: data.usesComponentStandardCost || undefined,
                includesRouting: data.includesRouting || undefined,
            },
        );

        computedItemCosts[componentItemId] = {
            ...currentlyComputedItemCostSums,
            [componentRequiredQuantity]: componentCostsAtRequiredQuantity,
        };
        currentlyComputedItemCostSums = computedItemCosts[componentItemId];

        if (data.functionalData.componentBomBaseQuantity) {
            let resultLineCostsAtRequiredQuantity: xtremStock.interfaces.CostSumObject | undefined =
                currentlyComputedItemCostSums[data.requiredQuantity];

            // we need to check here if the second quantity we want has already been saved to avoid computing and/or saving it more than once
            if (resultLineCostsAtRequiredQuantity === undefined) {
                resultLineCostsAtRequiredQuantity = await ItemSiteCostExtension.computeItemCostAtRequiredQuantity(
                    context,
                    {
                        site,
                        item: componentItem,
                        calculationQuantity: data.requiredQuantity,
                        fromDate,
                        computedItemCosts,
                        usesComponentStandardCost: data.usesComponentStandardCost || undefined,
                        includesRouting: data.includesRouting || undefined,
                        canComputeSublevels: true,
                    },
                );

                currentlyComputedItemCostSums[data.requiredQuantity] = { ...resultLineCostsAtRequiredQuantity };
            }

            // if the result line already exist set the local sum obj to undefined so we won't try to save it twice or more
            if (resultLineCostsAtRequiredQuantity.hasResultLine) {
                resultLineCostsAtRequiredQuantity = undefined;
            }

            await context.runInWritableContext(async childContext => {
                const componentCostRollUpResultLineOrSubAssemblyCreateData =
                    await ItemSiteCostExtension.getCostRollUpResultLineCreateData(context, {
                        fromDate,
                        inputSetId: data.inputSetId,
                        itemId: componentItemId,
                        itemSiteId: componentItemSiteId,
                    });

                if (resultLineCostsAtRequiredQuantity) {
                    let resultLine = await childContext
                        .query(xtremStock.nodes.CostRollUpResultLine, {
                            forUpdate: true,
                            filter: {
                                inputSet: data.inputSetId,
                                item: {
                                    _id: componentItemId,
                                },
                            },
                            first: 1,
                        })
                        .at(0);

                    const costRollUpResultLineUpdateData: NodeUpdateData<xtremStock.nodes.CostRollUpResultLine> = {
                        materialCost: resultLineCostsAtRequiredQuantity.materialCost,
                        laborCost: resultLineCostsAtRequiredQuantity.laborCost,
                        machineCost: resultLineCostsAtRequiredQuantity.machineCost,
                        toolCost: resultLineCostsAtRequiredQuantity.toolCost,
                    };

                    if (resultLine) {
                        await logger.debugAsync(
                            async () =>
                                `updating result line record: ${JSON.stringify(
                                    costRollUpResultLineUpdateData,
                                )} ${await componentItem?.name}`,
                        );
                        await resultLine.$.set(costRollUpResultLineUpdateData);
                    } else {
                        await logger.debugAsync(
                            async () =>
                                `creating result line record bis: ${JSON.stringify({
                                    ...componentCostRollUpResultLineOrSubAssemblyCreateData,
                                    ...costRollUpResultLineUpdateData,
                                })} ${await componentItem?.name}`,
                        );

                        resultLine = await childContext.create(xtremStock.nodes.CostRollUpResultLine, {
                            ...componentCostRollUpResultLineOrSubAssemblyCreateData,
                            ...costRollUpResultLineUpdateData,
                        });
                    }

                    await resultLine.$.save();

                    currentlyComputedItemCostSums[data.requiredQuantity] = {
                        ...currentlyComputedItemCostSums[data.requiredQuantity],
                        hasResultLine: true,
                    };
                }
            });
        }

        return componentCostsAtRequiredQuantity;
    }

    private static prepareItemSiteFilter(data: {
        site: xtremSystem.nodes.Site;
        itemIds?: xtremMasterData.nodes.Item['_id'][];
        itemCategory: xtremMasterData.nodes.ItemCategory | null;
    }) {
        const itemSiteFilter = {
            site: {
                _id: data.site._id,
            },
            item: {
                isActive: true,
                isManufactured: true,
            },
        };
        const additionalItemFilter: {
            _id?: { _in: integer[] };
            category?: { _id: integer };
        } = {};

        if (data.itemIds?.length) {
            additionalItemFilter._id = {
                _in: data.itemIds,
            };
        }

        if (data.itemCategory) {
            additionalItemFilter.category = {
                _id: data.itemCategory._id,
            };
        }

        return {
            ...itemSiteFilter,
            item: {
                ...itemSiteFilter.item,
                ...additionalItemFilter,
            },
        };
    }

    private static async processItemSite(
        context: Context,
        data: {
            itemSites: AsyncArray<xtremMasterData.nodes.ItemSite>;
            item: xtremMasterData.nodes.Item;
            filteredItemSite: xtremMasterData.nodes.ItemSite;
            fromDate: date;
            costRollUpInputSetId: integer;
            requiredQuantity: decimal;
            site: xtremSystem.nodes.Site;
            computedItemCosts: xtremStock.interfaces.ComputedItemCosts;
            usesComponentStandardCost: boolean;
            includesRouting: boolean;
            costRollUpResultLineCreateData: NodeCreateData<xtremStock.nodes.CostRollUpResultLine>;
        },
    ) {
        await context.runInWritableContext(async childContext => {
            await logger.debugAsync(
                async () =>
                    `creating result line record: ${JSON.stringify(data.costRollUpResultLineCreateData)} ${await data.item.name}`,
            );
            const costRollUpResultLine = await childContext.create(
                xtremStock.nodes.CostRollUpResultLine,
                data.costRollUpResultLineCreateData,
            );
            await costRollUpResultLine.$.save();

            return costRollUpResultLine._id;
        });
        logger.debug(() => 'standardCostRollUpCalculation: After create cost roll up result line');

        let currentItemSiteCosts: xtremStock.interfaces.CostSumObject = {
            totalCost: 0,
            unitCost: 0,
            materialCost: 0,
            laborCost: 0,
            machineCost: 0,
            toolCost: 0,
            hasResultLine: false,
        };

        const bom = await data.filteredItemSite.billOfMaterial;

        if ((await data.item.isManufactured) && bom) {
            // this will loop through all the sub-assemblies of the current item and compute their costs according to the userRequiredQuantity
            logger.debug(() => 'standardCostRollUpCalculation: Before explore sub assemblies');
            await xtremTechnicalData.functions.bomLib.exploreBomSubAssemblies(context, {
                bom,
                componentQueryOptions: {
                    filter: {
                        lineType: 'normal',
                    },
                },
                prepareFunctionalData: (
                    explorationData: xtremTechnicalData.interfaces.SimpleCallbackExplorationData<
                        undefined,
                        xtremStock.interfaces.BomExplorationFunctionalData
                    >,
                ) =>
                    ItemSiteCostExtension.prepareFunctionalData({
                        ...explorationData,
                        requiredQuantity: data.requiredQuantity,
                    }),
                reuseAlreadyProcessedData: explorationData =>
                    ItemSiteCostExtension.reuseAlreadyProcessedData({
                        ...explorationData,
                        computedItemCosts: data.computedItemCosts,
                    }),
                ascendingExplorationCallback: explorationData => {
                    return ItemSiteCostExtension.computeComponentCosts(context, {
                        ...explorationData,
                        site: data.site,
                        requiredQuantity: data.requiredQuantity,
                        fromDate: data.fromDate,
                        computedItemCosts: data.computedItemCosts,
                        usesComponentStandardCost: data.usesComponentStandardCost,
                        includesRouting: data.includesRouting,
                        userFilteredItemSites: data.itemSites,
                        inputSetId: data.costRollUpInputSetId,
                    });
                },
            });
            logger.debug(() => 'standardCostRollUpCalculation: After explore sub assemblies');

            // once all the sub-assemblies of an item have been computed we need to sum their costs.
            currentItemSiteCosts = await ItemSiteCostExtension.computeItemCostAtRequiredQuantity(context, {
                site: data.site,
                item: data.item,
                calculationQuantity: data.requiredQuantity,
                fromDate: data.fromDate,
                computedItemCosts: data.computedItemCosts,
                usesComponentStandardCost: data.usesComponentStandardCost || undefined,
                includesRouting: data.includesRouting || undefined,
            });
            logger.debug(() => 'standardCostRollUpCalculation: After compute item cost');
        }

        if (data.computedItemCosts[data.item._id]) {
            data.computedItemCosts[data.item._id][data.requiredQuantity] = currentItemSiteCosts;
        } else {
            data.computedItemCosts[data.item._id] = {
                ...data.computedItemCosts[data.item._id],
                [data.requiredQuantity]: currentItemSiteCosts,
            };
        }

        logger.debug(() => 'standardCostRollUpCalculation: Before save result line');
        await context.runInWritableContext(async childContext => {
            const resultLine = await childContext
                .query(xtremStock.nodes.CostRollUpResultLine, {
                    forUpdate: true,
                    filter: {
                        inputSet: data.costRollUpInputSetId,
                        item: {
                            _id: data.item._id,
                        },
                    },
                    first: 1,
                })
                .elementAt(0);

            const costRollUpResultLineUpdateData: NodeUpdateData<xtremStock.nodes.CostRollUpResultLine> = {
                materialCost: currentItemSiteCosts.materialCost,
                laborCost: currentItemSiteCosts.laborCost,
                machineCost: currentItemSiteCosts.machineCost,
                toolCost: currentItemSiteCosts.toolCost,
            };

            await resultLine.$.set(costRollUpResultLineUpdateData);

            await resultLine.$.save();
        });
        logger.debug(() => 'standardCostRollUpCalculation: After save result line');
    }

    private static async processItemSitesCostRollup(
        context: Context,
        data: {
            itemSites: AsyncArray<xtremMasterData.nodes.ItemSite>;
            fromDate: date;
            costRollUpInputSetId: integer;
            requiredQuantity: decimal;
            site: xtremSystem.nodes.Site;
            computedItemCosts: xtremStock.interfaces.ComputedItemCosts;
            usesComponentStandardCost: boolean;
            includesRouting: boolean;
        },
    ) {
        const numberOfItemSites = await data.itemSites.length;

        await context.batch.updateProgress({
            phase: 'start',
            totalCount: numberOfItemSites,
            errorCount: 0,
            detail: 'started',
            successCount: 0,
        });

        await data.itemSites.forEach(async (filteredItemSite, index) => {
            const item = await filteredItemSite.item;

            if (await context.batch.isStopRequested()) {
                return;
            }

            // Only update every 100 items
            if (index % 100 === 0) {
                await context.batch.updateProgress({
                    detail: 'processing',
                    successCount: index,
                    phase: 'processing',
                });
            }

            const costRollUpResultLineCreateData = await ItemSiteCostExtension.getCostRollUpResultLineCreateData(
                context,
                {
                    fromDate: data.fromDate,
                    inputSetId: data.costRollUpInputSetId,
                    itemId: item._id,
                    itemSiteId: filteredItemSite._id,
                },
            );
            logger.debug(() => 'standardCostRollUpCalculation: After getCostRollUpResultLineCreateData');

            const currentlyComputedItemCostSums = data.computedItemCosts[item._id];

            if (!currentlyComputedItemCostSums || currentlyComputedItemCostSums[data.requiredQuantity] === undefined) {
                await ItemSiteCostExtension.processItemSite(context, {
                    itemSites: data.itemSites,
                    item,
                    filteredItemSite,
                    fromDate: data.fromDate,
                    costRollUpInputSetId: data.costRollUpInputSetId,
                    requiredQuantity: data.requiredQuantity,
                    site: data.site,
                    computedItemCosts: data.computedItemCosts,
                    usesComponentStandardCost: data.usesComponentStandardCost,
                    includesRouting: data.includesRouting,
                    costRollUpResultLineCreateData,
                });
            }
        });
        await context.batch.updateProgress({
            successCount: numberOfItemSites,
            phase: 'finish',
        });
    }

    private static async sendCalculationUserNotification(context: Context, success: boolean, msgDescription: string) {
        const actions: InitialNotificationAction[] = [
            {
                link: context.batch.notificationStateLink,
                title: 'History',
                icon: 'link',
                style: 'tertiary',
            },
        ];
        if (success) {
            actions.push({
                link: `@sage/xtrem-stock/StandardCostCalculation`,
                title: 'Results',
                icon: 'link',
                style: 'tertiary',
            });
        }
        await context.notifyUser({
            title: context.localize(
                '@sage/xtrem-stock/nodes__item_site_cost__success_notification_title',
                'Standard cost roll up calculation complete.',
            ),
            description: msgDescription,
            icon: success ? 'tick' : 'error',
            level: success ? 'success' : 'error',
            shouldDisplayToast: true,
            actions,
        });
    }

    private static async updateStatusAndDeleteLinesIfNotError(
        context: Context,
        costRollUpInputSetId: integer,
        status: xtremStock.enums.StandardCostRollUpStatus,
    ) {
        await context.runInWritableContext(async childContext => {
            const updatableInputSet = await childContext.read(
                xtremStock.nodes.CostRollUpInputSet,
                {
                    _id: costRollUpInputSetId,
                },
                {
                    forUpdate: true,
                },
            );
            if (status !== 'error') {
                await updatableInputSet.$.set({ status, resultLines: [] });
            } else {
                await updatableInputSet.$.set({ status });
            }
            await updatableInputSet.$.save();
        });
    }

    @decorators.asyncMutation<typeof ItemSiteCostExtension, 'standardCostRollUpCalculation'>({
        isPublished: true,
        startsReadOnly: true,
        parameters: [
            {
                isMandatory: true,
                name: 'inputSet',
                type: 'reference',
                node: () => xtremStock.nodes.CostRollUpInputSet,
            },
        ],
        return: 'boolean',
    })
    static async standardCostRollUpCalculation(
        context: Context,
        inputSet: Reference<xtremStock.nodes.CostRollUpInputSet>,
    ) {
        logger.debug(() => 'standardCostRollUpCalculation: Start');
        const costRollUpInputSet = await inputSet;
        const costRollUpInputSetId = costRollUpInputSet._id;

        // TODO: Refactor to onError when XT-51158 is fixed
        try {
            await context.batch.logMessage(
                'info',
                context.localize(
                    '@sage/xtrem-stock/standard_cost_roll_up_calculation_start',
                    'Standard cost roll up calculation start.',
                ),
            );

            // Delete the existing result lines.
            await ItemSiteCostExtension.updateStatusAndDeleteLinesIfNotError(
                context,
                costRollUpInputSetId,
                'inProgress',
            );

            logger.debug(() => 'standardCostRollUpCalculation: After possible deletion');
            const site = await costRollUpInputSet.site;
            const itemCategory = await costRollUpInputSet.itemCategory;
            const requiredQuantity = await costRollUpInputSet.quantity;
            const fromDate = await costRollUpInputSet.fromDate;

            const usesComponentStandardCost = await costRollUpInputSet.usesComponentStandardCost;
            const includesRouting = await costRollUpInputSet.includesRouting;

            const itemIds = (await costRollUpInputSet.items).map(item => item._id);

            const computedItemCosts: xtremStock.interfaces.ComputedItemCosts = {};

            const itemSites = context.query(xtremMasterData.nodes.ItemSite, {
                filter: ItemSiteCostExtension.prepareItemSiteFilter({
                    site,
                    itemIds,
                    itemCategory,
                }),
            });
            logger.debug(() => 'standardCostRollUpCalculation: After query of itemSites');

            await context.batch.logMessage(
                'info',
                context.localize(
                    '@sage/xtrem-stock/standard_cost_roll_up_calculation_processing_items',
                    'The standard cost roll up calculation is processing items.',
                ),
            );

            await ItemSiteCostExtension.processItemSitesCostRollup(context, {
                itemSites,
                fromDate,
                costRollUpInputSetId,
                requiredQuantity,
                site,
                computedItemCosts,
                usesComponentStandardCost,
                includesRouting,
            });

            if (await context.batch.isStopRequested()) {
                await context.batch.logMessage(
                    'info',
                    context.localize(
                        '@sage/xtrem-stock/standard_cost_roll_up_calculation__stop_calculation',
                        'Standard cost roll up calculation stopped at {{stopTime}}.',
                        {
                            stopTime: datetime.now().toJsDate(),
                        },
                    ),
                );
                await context.batch.confirmStop();
                await ItemSiteCostExtension.updateStatusAndDeleteLinesIfNotError(
                    context,
                    costRollUpInputSetId,
                    'error',
                );

                return false;
            }
            logger.debug(() => 'standardCostRollUpCalculation: Before updateCostRollUpInputSetWithSubAssemblies');

            await context.batch.logMessage(
                'info',
                context.localize(
                    '@sage/xtrem-stock/standard_cost_roll_up_calculation_updating_sub_assemblies',
                    'The standard cost roll up calculation is updating the subassemblies.',
                ),
            );

            await ItemSiteCostExtension.updateCostRollUpInputSetWithSubAssemblies(context, {
                costRollUpInputSet,
                itemIds,
                computedItemCosts,
                requiredQuantity,
            });
            logger.debug(() => 'standardCostRollUpCalculation: After updateCostRollUpInputSetWithSubAssemblies');
        } catch (error) {
            const errorMessage = context.localize(
                '@sage/xtrem-stock/standard_cost_roll_up_calculation_failed',
                'Standard cost roll up calculation has failed: {{{errorMessage}}}.',
                { errorMessage: error.message },
            );

            await context.batch.logMessage('error', errorMessage);

            // Set status to 'error'.
            await ItemSiteCostExtension.updateStatusAndDeleteLinesIfNotError(context, costRollUpInputSetId, 'error');

            await ItemSiteCostExtension.sendCalculationUserNotification(context, false, errorMessage);
            await context.batch.end();

            throw new BusinessRuleError(errorMessage, error);
        }

        await context.batch.logMessage(
            'result',
            context.localize(
                '@sage/xtrem-stock/standard_cost_roll_up_calculation_complete',
                'Standard cost roll up calculation complete.',
            ),
        );

        await ItemSiteCostExtension.sendCalculationUserNotification(
            context,
            true,
            context.localize(
                '@sage/xtrem-stock/standard_cost_roll_up_calculation_completed',
                'Standard cost roll up calculation completed successfully.',
            ),
        );

        return true;
    }
}

declare module '@sage/xtrem-master-data/lib/nodes/item-site-cost' {
    interface ItemSiteCost extends ItemSiteCostExtension {}
}
