import type { Collection, Context } from '@sage/xtrem-core';
import { decorators, NodeExtension } from '@sage/xtrem-core';
import * as xtremStockData from '@sage/xtrem-stock-data';
import * as xtremStock from '..';

@decorators.nodeExtension<StockExtension>({
    extends: () => xtremStockData.nodes.Stock,
    async deleteBegin() {
        await this.$.context.bulkUpdate(xtremStock.nodes.StockCountLine, {
            set: { stockRecord: null },
            where: {
                stockRecord: this._id,
            },
        });
    },
})
export class StockExtension extends NodeExtension<xtremStockData.nodes.Stock> {
    @decorators.collectionProperty<StockExtension, 'stockCountingInProgress'>({
        node: () => xtremStock.nodes.StockCountLine,
        join: {
            stockRecord() {
                return this._id;
            },
        },
    })
    readonly stockCountingInProgress: Collection<xtremStock.nodes.StockCountLine>;

    @decorators.booleanProperty<StockExtension, 'isStockCountingInProgress'>({
        dependsOn: ['stockCountingInProgress'],
        getValue() {
            return this.stockCountingInProgress.some(async stockCountLine =>
                ['toBeCounted', 'counted', 'countInProgress'].includes(await (await stockCountLine.document).status),
            );
        },
    })
    readonly isStockCountingInProgress: Promise<boolean>;

    static readonly stepDefinitions: Record<
        xtremStockData.enums.StockMovementType,
        { detailType: typeof xtremStockData.nodes.BaseStockDetail; stockUpdateFunction: any }
    > = {
        receipt: {
            detailType: xtremStockData.nodes.StockReceiptDetail,
            stockUpdateFunction: xtremStock.functions.stockEngine.stockReceipt,
        },
        issue: {
            detailType: xtremStockData.nodes.StockIssueDetail,
            stockUpdateFunction: xtremStock.functions.stockEngine.stockIssue,
        },

        correction: {
            detailType: xtremStockData.nodes.StockCorrectionDetail,
            stockUpdateFunction: xtremStock.functions.stockEngine.stockCorrection,
        },

        valueChange: {
            detailType: xtremStockData.nodes.StockValueDetail,
            stockUpdateFunction: xtremStock.functions.stockEngine.stockValueChange,
        },

        change: {
            detailType: xtremStockData.nodes.StockChangeDetail,
            stockUpdateFunction: xtremStock.functions.stockEngine.stockChange,
        },

        transfer: {
            detailType: xtremStockData.nodes.StockChangeDetail,
            stockUpdateFunction: xtremStock.functions.stockEngine.stockChange,
        },

        adjustment: {
            detailType: xtremStockData.nodes.StockAdjustmentDetail,
            stockUpdateFunction: xtremStock.functions.stockEngine.stockAdjustment,
        },
    };

    static async processSteps(
        readOnlyContext: Context,
        payload: xtremStockData.interfaces.StockPostingPayload<xtremStockData.enums.StockMovementTypeEnum>,
    ) {
        await xtremStock.functions.notificationLib.processDocuments(readOnlyContext, {
            payload,
            updateSteps: [
                {
                    ...StockExtension.stepDefinitions[payload.stockUpdateParameters.movementType],
                    stockUpdateParameters: payload.stockUpdateParameters,
                },
                ...(payload.additionalSteps?.map(step => ({
                    ...StockExtension.stepDefinitions[step.movementType],
                    stockUpdateParameters: { ...step },
                })) ?? []),
            ],
        });
    }

    @decorators.asyncMutation<typeof StockExtension, 'receive'>({
        startsReadOnly: true,
        parameters: [
            {
                name: 'payload',
                isMandatory: true,
                type: 'object',
                properties: {
                    replyTopic: { type: 'string', isMandatory: true },
                    stockUpdateParameters: {
                        type: 'object',
                        properties: {
                            movementType: {
                                type: 'enum',
                                dataType: () => xtremStockData.enums.StockMovementTypeDataType,
                                isMandatory: true,
                            },
                        },
                    },
                    documents: {
                        type: 'array',
                        item: {
                            type: 'object',
                            properties: {
                                id: { type: 'string', isMandatory: true },
                                lines: {
                                    type: 'array',
                                    item: {
                                        type: 'object',
                                        properties: {
                                            id: 'string',
                                            sortValue: 'string',
                                        },
                                    },
                                },
                            },
                        },
                    },
                    additionalSteps: {
                        type: 'array',
                        isMandatory: false,
                        item: {
                            type: 'object',
                            properties: {
                                movementType: {
                                    type: 'enum',
                                    dataType: () => xtremStockData.enums.StockMovementTypeDataType,
                                    isMandatory: true,
                                },
                            },
                        },
                    },
                },
            },
        ],
        return: {
            type: 'object',
            properties: {
                result: {
                    type: 'boolean',
                    isMandatory: true,
                },
            },
        },
    })
    static async receive(
        readOnlyContext: Context,
        payload: xtremStockData.interfaces.StockPostingPayload<xtremStockData.enums.StockMovementTypeEnum.receipt>,
    ) {
        await StockExtension.processSteps(readOnlyContext, payload);
    }

    @decorators.asyncMutation<typeof StockExtension, 'issue'>({
        startsReadOnly: true,
        parameters: [
            {
                name: 'payload',
                isMandatory: true,
                type: 'object',
                properties: {
                    replyTopic: { type: 'string', isMandatory: true },
                    stockUpdateParameters: {
                        type: 'object',
                        properties: {
                            movementType: {
                                type: 'enum',
                                dataType: () => xtremStockData.enums.StockMovementTypeDataType,
                                isMandatory: true,
                            },
                            stockDetailData: {
                                type: 'object',
                                properties: {
                                    isDocumentWithoutStockDetails: { type: 'boolean', isMandatory: true },
                                },
                            },
                            allocationData: {
                                type: 'object',
                                properties: {
                                    isUsingAllocations: { type: 'boolean', isMandatory: true },
                                    isUsingOrderDocumentAllocations: 'boolean',
                                },
                            },
                        },
                    },
                    documents: {
                        type: 'array',
                        item: {
                            type: 'object',
                            properties: {
                                id: { type: 'string', isMandatory: true },
                                lines: {
                                    type: 'array',
                                    item: {
                                        type: 'object',
                                        properties: {
                                            id: 'string',
                                            sortValue: 'string',
                                        },
                                    },
                                },
                            },
                        },
                    },
                    batchTrackingId: { type: 'string', isMandatory: false },
                },
            },
        ],
        return: {
            type: 'object',
            properties: {
                result: {
                    type: 'boolean',
                    isMandatory: true,
                },
            },
        },
    })
    static async issue(
        readOnlyContext: Context,
        payload: xtremStockData.interfaces.StockPostingPayload<xtremStockData.enums.StockMovementTypeEnum.issue>,
    ) {
        await StockExtension.processSteps(readOnlyContext, payload);
    }

    @decorators.asyncMutation<typeof StockExtension, 'change'>({
        startsReadOnly: true,
        parameters: [
            {
                name: 'payload',
                isMandatory: true,
                type: 'object',
                properties: {
                    replyTopic: { type: 'string', isMandatory: true },
                    stockUpdateParameters: {
                        type: 'object',
                        properties: {
                            movementType: {
                                type: 'enum',
                                dataType: () => xtremStockData.enums.StockMovementTypeDataType,
                                isMandatory: true,
                            },
                        },
                    },
                    documents: {
                        type: 'array',
                        item: {
                            type: 'object',
                            properties: {
                                id: { type: 'string', isMandatory: true },
                                lines: {
                                    type: 'array',
                                    item: {
                                        type: 'object',
                                        properties: {
                                            id: 'string',
                                            sortValue: 'string',
                                        },
                                    },
                                },
                            },
                        },
                    },
                },
            },
        ],
        return: {
            type: 'object',
            properties: {
                result: {
                    type: 'boolean',
                    isMandatory: true,
                },
            },
        },
    })
    static async change(
        readOnlyContext: Context,
        payload: xtremStockData.interfaces.StockPostingPayload<xtremStockData.enums.StockMovementTypeEnum.change>,
    ) {
        await StockExtension.processSteps(readOnlyContext, payload);
    }

    @decorators.asyncMutation<typeof StockExtension, 'transfer'>({
        startsReadOnly: true,
        parameters: [
            {
                name: 'payload',
                isMandatory: true,
                type: 'object',
                properties: {
                    replyTopic: { type: 'string', isMandatory: true },
                    stockUpdateParameters: {
                        type: 'object',
                        properties: {
                            movementType: {
                                type: 'enum',
                                dataType: () => xtremStockData.enums.StockMovementTypeDataType,
                                isMandatory: true,
                            },
                            intersiteTransferData: {
                                type: 'object',
                                isMandatory: true,
                                properties: {
                                    type: {
                                        type: 'enum',
                                        isMandatory: true,
                                        dataType: () => xtremStockData.enums.IntersiteTransferTypeDataType,
                                    },
                                },
                            },
                            stockDetailData: {
                                type: 'object',
                                properties: {
                                    isDocumentWithoutStockDetails: { type: 'boolean', isMandatory: true },
                                },
                            },
                            allocationData: {
                                type: 'object',
                                properties: {
                                    isUsingAllocations: { type: 'boolean', isMandatory: true },
                                    isUsingOrderDocumentAllocations: 'boolean',
                                },
                            },
                        },
                    },
                    documents: {
                        type: 'array',
                        item: {
                            type: 'object',
                            properties: {
                                id: { type: 'string', isMandatory: true },
                                lines: {
                                    type: 'array',
                                    item: {
                                        type: 'object',
                                        properties: {
                                            id: 'string',
                                            sortValue: 'string',
                                        },
                                    },
                                },
                            },
                        },
                    },
                },
            },
        ],
        return: {
            type: 'object',
            properties: {
                result: {
                    type: 'boolean',
                    isMandatory: true,
                },
            },
        },
    })
    static async transfer(
        readOnlyContext: Context,
        payload: xtremStockData.interfaces.StockPostingPayload<xtremStockData.enums.StockMovementTypeEnum.transfer>,
    ) {
        await StockExtension.processSteps(readOnlyContext, payload);
    }

    @decorators.asyncMutation<typeof StockExtension, 'correct'>({
        startsReadOnly: true,
        parameters: [
            {
                name: 'payload',
                isMandatory: true,
                type: 'object',
                properties: {
                    replyTopic: { type: 'string', isMandatory: true },
                    stockUpdateParameters: {
                        type: 'object',
                        properties: {
                            movementType: {
                                type: 'enum',
                                dataType: () => xtremStockData.enums.StockMovementTypeDataType,
                                isMandatory: true,
                            },
                            stockDetailData: {
                                type: 'object',
                                properties: {
                                    isDocumentWithoutStockDetails: { type: 'boolean', isMandatory: true },
                                },
                            },
                        },
                    },
                    documents: {
                        type: 'array',
                        item: {
                            type: 'object',
                            properties: {
                                id: { type: 'string', isMandatory: true },
                                lines: {
                                    type: 'array',
                                    item: {
                                        type: 'object',
                                        properties: {
                                            id: 'string',
                                            sortValue: 'string',
                                        },
                                    },
                                },
                            },
                        },
                    },
                },
            },
        ],
        return: {
            type: 'object',
            properties: {
                result: {
                    type: 'boolean',
                    isMandatory: true,
                },
            },
        },
    })
    static async correct(
        readOnlyContext: Context,
        payload: xtremStockData.interfaces.StockPostingPayload<xtremStockData.enums.StockMovementTypeEnum.correction>,
    ) {
        await StockExtension.processSteps(readOnlyContext, payload);
    }

    @decorators.asyncMutation<typeof StockExtension, 'adjust'>({
        startsReadOnly: true,
        parameters: [
            {
                name: 'payload',
                isMandatory: true,
                type: 'object',
                properties: {
                    replyTopic: { type: 'string', isMandatory: true },
                    stockUpdateParameters: {
                        type: 'object',
                        properties: {
                            movementType: {
                                type: 'enum',
                                dataType: () => xtremStockData.enums.StockMovementTypeDataType,
                                isMandatory: true,
                            },
                            bypassCountingInProgressControl: { type: 'boolean', isMandatory: false },
                            stockDetailData: {
                                type: 'object',
                                properties: {
                                    isDocumentWithoutStockDetails: { type: 'boolean', isMandatory: true },
                                },
                            },
                        },
                    },
                    documents: {
                        type: 'array',
                        item: {
                            type: 'object',
                            properties: {
                                id: { type: 'string', isMandatory: true },
                                lines: {
                                    type: 'array',
                                    item: {
                                        type: 'object',
                                        properties: {
                                            id: 'string',
                                            sortValue: 'string',
                                        },
                                    },
                                },
                            },
                        },
                    },
                },
            },
        ],
        return: {
            type: 'object',
            properties: {
                result: {
                    type: 'boolean',
                    isMandatory: true,
                },
            },
        },
    })
    static async adjust(
        readOnlyContext: Context,
        payload: xtremStockData.interfaces.StockPostingPayload<xtremStockData.enums.StockMovementTypeEnum.adjustment>,
    ) {
        await StockExtension.processSteps(readOnlyContext, payload);
    }

    @decorators.asyncMutation<typeof StockExtension, 'changeValue'>({
        startsReadOnly: true,
        parameters: [
            {
                name: 'payload',
                isMandatory: true,
                type: 'object',
                properties: {
                    replyTopic: { type: 'string', isMandatory: true },
                    stockUpdateParameters: {
                        type: 'object',
                        properties: {
                            movementType: {
                                type: 'enum',
                                dataType: () => xtremStockData.enums.StockMovementTypeDataType,
                                isMandatory: true,
                            },
                        },
                    },
                    documents: {
                        type: 'array',
                        item: {
                            type: 'object',
                            properties: {
                                id: { type: 'string', isMandatory: true },
                                lines: {
                                    type: 'array',
                                    item: {
                                        type: 'object',
                                        properties: {
                                            id: 'string',
                                            sortValue: 'string',
                                        },
                                    },
                                },
                            },
                        },
                    },
                },
            },
        ],
        return: {
            type: 'object',
            properties: {
                result: {
                    type: 'boolean',
                    isMandatory: true,
                },
            },
        },
    })
    static async changeValue(
        readOnlyContext: Context,
        payload: xtremStockData.interfaces.StockPostingPayload<xtremStockData.enums.StockMovementTypeEnum.valueChange>,
    ) {
        await StockExtension.processSteps(readOnlyContext, payload);
    }
}

declare module '@sage/xtrem-stock-data/lib/nodes/stock' {
    interface Stock extends StockExtension {}
}
