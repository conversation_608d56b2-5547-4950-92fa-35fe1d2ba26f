import type { Context } from '@sage/xtrem-core';
import { date, datetime, decorators, NodeExtension } from '@sage/xtrem-core';
import * as xtremMrpData from '@sage/xtrem-mrp-data';
import { loggers } from '../functions/loggers';

@decorators.nodeExtension<MrpCalculationExtension>({
    extends: () => xtremMrpData.nodes.MrpCalculation,
})
export class MrpCalculationExtension extends NodeExtension<xtremMrpData.nodes.MrpCalculation> {
    @decorators.asyncMutation<typeof MrpCalculationExtension, 'deleteOldMrpCalculations'>({
        isPublished: true,
        isSchedulable: true,
        startsReadOnly: true,
        parameters: [
            {
                name: 'numberOfDaysToKeep',
                type: 'integer',
            },
            {
                name: 'numberOfRecordsToKeep',
                type: 'integer',
            },
        ],
        return: 'boolean',
    })
    static async deleteOldMrpCalculations(
        context: Context,
        numberOfDaysToKeep = 14,
        numberOfRecordsToKeep = 30,
    ): Promise<boolean> {
        loggers.mrpCalculation.debug(() => `deleteOldMrpCalculations: start`);
        loggers.mrpCalculation.debug(() => `deleteOldMrpCalculations: numberOfDaysToKeep ${numberOfDaysToKeep}`);
        loggers.mrpCalculation.debug(() => `deleteOldMrpCalculations: numberOfRecordsToKeep ${numberOfRecordsToKeep}`);
        const dateToKeep = datetime.fromJsDate(date.today().addDays(-numberOfDaysToKeep).toJsDate('UTC'));
        let idToDeleteFrom = 0;
        let deletionDate = dateToKeep;

        await context.batch.logMessage(
            'info',
            context.localize(
                '@sage/xtrem-stock/nodes__mrp_calculation-extension__start_delete_old_mrp_calculations',
                'Starting to delete old MRP calculations.',
            ),
        );

        loggers.mrpCalculation.debug(
            () => `deleteOldMrpCalculations: numberOfRecordsToKeep ${dateToKeep.withoutTimezoneOffset().toString()}`,
        );
        const calculationsByDateToKeep = await context.select(
            xtremMrpData.nodes.MrpCalculation,
            {
                _id: true,
                calculationDate: true,
            },
            {
                filter: {
                    calculationDate: { _gte: dateToKeep.withoutTimezoneOffset() },
                },
                orderBy: { calculationDate: 1 },
            },
        );
        loggers.mrpCalculation.debug(
            () => `deleteOldMrpCalculations: numberOfRecordsToKeep 2 ${JSON.stringify(calculationsByDateToKeep)}`,
        );
        idToDeleteFrom = calculationsByDateToKeep.length ? calculationsByDateToKeep[0]._id : 0;
        loggers.mrpCalculation.debug(() => `deleteOldMrpCalculations: numberOfRecordsToKeep date ID ${idToDeleteFrom}`);

        if (calculationsByDateToKeep.length < numberOfRecordsToKeep) {
            loggers.mrpCalculation.debug(
                () => `deleteOldMrpCalculations: numberOfRecordsToKeep 4 ${numberOfRecordsToKeep}`,
            );

            const calculationsByNumberToKeep = await context.select(
                xtremMrpData.nodes.MrpCalculation,
                {
                    _id: true,
                    calculationDate: true,
                },
                {
                    first: numberOfRecordsToKeep,
                    orderBy: { calculationDate: -1 },
                    filter: {},
                },
            );
            loggers.mrpCalculation.debug(
                () => `deleteOldMrpCalculations: numberOfRecordsToKeep 5 ${numberOfRecordsToKeep}`,
            );

            idToDeleteFrom =
                calculationsByNumberToKeep.length && calculationsByNumberToKeep.length === numberOfRecordsToKeep
                    ? calculationsByNumberToKeep[calculationsByNumberToKeep.length - 1]._id
                    : 0;
            loggers.mrpCalculation.debug(() => `deleteOldMrpCalculations: numberofRecordsID ${idToDeleteFrom}`);

            deletionDate = calculationsByNumberToKeep.length
                ? calculationsByNumberToKeep[0].calculationDate
                : dateToKeep;
        }

        loggers.mrpCalculation.debug(
            () => `deleteOldMrpCalculations: deletionDate ${deletionDate.withoutTimezoneOffset().toString()}`,
        );

        if (idToDeleteFrom) {
            await context.batch.logMessage(
                'info',
                context.localize(
                    '@sage/xtrem-stock/nodes__mrp_calculation-extension__deleting_old_mrp_calculations',
                    'Deleting old MRP calculations with a date earlier than: {{deletionDate}}.',
                    { deletionDate: deletionDate.date },
                ),
            );

            const numberDeleted = await context.runInWritableContext(writableContext => {
                return writableContext.deleteMany(xtremMrpData.nodes.MrpCalculation, {
                    _id: { _lt: idToDeleteFrom },
                });
            });

            await context.batch.logMessage(
                'info',
                context.localize(
                    '@sage/xtrem-stock/nodes__mrp_calculation-extension__delete_old_mrp_calculations',
                    'Deleted old MRP calculations: {{number}}.',
                    { number: numberDeleted },
                ),
            );

            await context.batch.updateProgress({
                phase: 'result',
                totalCount: numberDeleted,
                errorCount: 0,
                detail: 'completed',
                successCount: numberDeleted,
            });

            loggers.mrpCalculation.debug(() => `deleteOldMrpCalculations: end deleted ${numberDeleted}`);
            return true;
        }

        await context.batch.logMessage(
            'info',
            context.localize(
                '@sage/xtrem-stock/nodes__mrp_calculation-extension__no_old_mrp_calculations',
                'No old MRP calculations to delete.',
            ),
        );
        loggers.mrpCalculation.debug(() => `deleteOldMrpCalculations: end deleted 0`);

        return false;
    }
}
declare module '@sage/xtrem-mrp-data/lib/nodes/mrp-calculation' {
    interface MrpCalculation extends MrpCalculationExtension {}
}
