import type { Reference } from '@sage/xtrem-core';
import { decorators, NodeExtension } from '@sage/xtrem-core';
import * as xtremStockData from '@sage/xtrem-stock-data';
import * as xtremStock from '..';

@decorators.nodeExtension<StockDetailLotExtension>({
    extends: () => xtremStockData.nodes.StockDetailLot,
})
export class StockDetailLotExtension extends NodeExtension<xtremStockData.nodes.StockDetailLot> {
    @decorators.referencePropertyOverride<StockDetailLotExtension, 'lot'>({
        dependsOn: ['canUpdateLot'],
        async isFrozen() {
            // In the case where the lot is created, the 'lotNumber' is erased and the lot gets the new reference
            // => it mustn't be frozen
            if (await this.canUpdateLot) return false;

            // In the case of stock count line, the lot is frozen as soon as it's set
            const documentLine = await (await this.stockDetail).documentLine;
            if (documentLine instanceof xtremStock.nodes.StockCountLine) {
                return !!(await this.lot);
            }
            return false;
        },
    })
    readonly lot: Reference<xtremStockData.nodes.Lot | null>;

    @decorators.stringPropertyOverride<StockDetailLotExtension, 'lotNumber'>({
        dependsOn: ['canUpdateLot'],
        async isFrozen() {
            // In the case where the lot is created, the 'lotNumber' is erased and the lot gets the new reference
            // => it mustn't be frozen
            if (await this.canUpdateLot) return false;

            // In the case of stock count line, the lot is frozen as soon as it's set
            const documentLine = await (await this.stockDetail).documentLine;
            if (documentLine instanceof xtremStock.nodes.StockCountLine) {
                return !!(await this.lotNumber);
            }
            return false;
        },
    })
    readonly lotNumber: Promise<string>;
}

declare module '@sage/xtrem-stock-data/lib/nodes/stock-detail-lot' {
    interface StockDetailLot extends StockDetailLotExtension {}
}
