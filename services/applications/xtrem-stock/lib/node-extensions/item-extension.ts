import type { Context, decimal, integer } from '@sage/xtrem-core';
import { NodeExtension, decorators } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremStockData from '@sage/xtrem-stock-data';
import * as xtremSystem from '@sage/xtrem-system';

interface TestStockCreationParameters {
    maxStockQuantity: decimal;
    isStockQuantityFixed: boolean;
}

@decorators.nodeExtension<ItemExtension>({
    extends: () => xtremMasterData.nodes.Item,
})
export class ItemExtension extends NodeExtension<xtremMasterData.nodes.Item> {
    @decorators.asyncMutation<typeof ItemExtension, 'createTestItems'>({
        isPublished: true,
        startsReadOnly: false,
        serviceOptions: () => [xtremSystem.serviceOptions.DevTools],
        parameters: [
            {
                name: 'itemId',
                type: 'string',
                isMandatory: true,
            },
            {
                name: 'quantity',
                type: 'integer',
                isMandatory: true,
            },
            {
                isMandatory: false,
                name: 'stockCreation',
                type: 'object',
                properties: {
                    maxStockQuantity: {
                        isMandatory: true,
                        type: 'decimal',
                    },
                    isStockQuantityFixed: {
                        isMandatory: true,
                        type: 'boolean',
                    },
                },
            },
        ],
        return: {
            type: 'string',
        },
    })
    static async createTestItems(
        context: Context,
        itemId: string,
        quantity: integer,
        stockCreation?: TestStockCreationParameters,
    ): Promise<string> {
        const item = await context.read(xtremMasterData.nodes.Item, { id: itemId });
        const eachUnit = '#EACH';

        const id = await item.id;
        const name = await item.name;
        const site = await (await item.itemSites.at(0))?.site;
        let stockStatusId: integer = 0;
        let locationId: integer = 0;

        if (stockCreation) {
            const stockStatusRecords = await context.select(
                xtremStockData.nodes.StockStatus,
                {
                    _id: true,
                },
                { filter: { type: 'accepted' } },
            );
            stockStatusId = stockStatusRecords[0]._id;
            const locationRecords = await context.select(
                xtremMasterData.nodes.Location,
                {
                    _id: true,
                },
                { filter: { site: site?._id } },
            );
            locationId = locationRecords[0]._id;
        }

        const lastItem = (
            await context.select(
                xtremMasterData.nodes.Item,
                { id: true },
                { filter: { id: { _regex: `^${id}-\\\\d+$`, _options: 'i' } }, orderBy: { id: -1 }, first: 1 },
            )
        )[0];
        let start = 1;
        let finalQuantity = quantity;
        if (lastItem) {
            const lastItemId = lastItem.id;
            const lastItemNumber = parseInt(lastItemId.split('-')[1], 10);
            finalQuantity += lastItemNumber;
            start = lastItemNumber + 1;
        }

        for (let i = start; i <= finalQuantity; i += 1) {
            const newItem = await context.create(xtremMasterData.nodes.Item, {
                id: `${id}-${i}`,
                name: `${name}-${i}`,
                description: `${name}-${i}`,
                isManufactured: true,
                isBought: true,
                isSold: true,
                isStockManaged: true,
                stockUnit: eachUnit,
                purchaseUnit: eachUnit,
                salesUnit: eachUnit,
                postingClass: await item.postingClass,
                itemSites: [
                    {
                        site,
                        replenishmentMethod: 'byMRP',
                        preferredProcess: i % 2 === 0 ? 'purchasing' : 'production',
                        safetyStock: xtremMasterData.functions.randomInteger(0, 500),
                        prodLeadTime: xtremMasterData.functions.randomInteger(1, 14),
                        purchaseLeadTime: xtremMasterData.functions.randomInteger(1, 14),
                    },
                ],
            });
            await newItem.$.save();

            // Create stock records if required
            if (stockCreation) {
                const stockQuantity = stockCreation.isStockQuantityFixed
                    ? stockCreation.maxStockQuantity
                    : xtremMasterData.functions.randomInteger(1, stockCreation.maxStockQuantity);
                const stockRecord = await context.create(xtremStockData.nodes.Stock, {
                    site,
                    item: newItem,
                    stockUnit: eachUnit,
                    owner: await site?.id,
                    quantityInStockUnit: stockQuantity,
                    status: stockStatusId,
                    location: locationId,
                });
                await stockRecord.$.save();
            }
        }
        return `Number of items created - ${quantity}`;
    }
}

declare module '@sage/xtrem-master-data/lib/nodes/item' {
    interface Item extends ItemExtension {}
}
