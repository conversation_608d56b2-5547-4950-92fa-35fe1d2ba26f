import type { Reference } from '@sage/xtrem-core';
import { decorators, SubNodeExtension2 } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremStockData from '@sage/xtrem-stock-data';
import * as xtremStock from '..';

@decorators.subNodeExtension2<StockChangeDetailExtension>({
    extends: () => xtremStockData.nodes.StockChangeDetail,
})
export class StockChangeDetailExtension extends SubNodeExtension2<xtremStockData.nodes.StockChangeDetail> {
    @decorators.referencePropertyOverride<StockChangeDetailExtension, 'startingSerialNumber'>({
        async control(cx, val) {
            if (await this.$.context.isServiceOptionEnabled(xtremMasterData.serviceOptions.serialNumberOption)) {
                if (val && (await this.stockRecord)) {
                    await xtremStockData.events.control.serialNumber.serialNumberControl(
                        cx,
                        await xtremStock.functions.stockEngine.getSerialNumbersForStockDetail(
                            this.$.context,
                            val,
                            await this.quantityInStockUnit,
                        ),
                        (await this.stockRecord)!,
                    );
                }
            }
        },
    })
    readonly startingSerialNumber: Reference<xtremStockData.nodes.SerialNumber | null>;
}

declare module '@sage/xtrem-stock-data/lib/nodes/stock-change-detail' {
    interface StockChangeDetail extends StockChangeDetailExtension {}
}
