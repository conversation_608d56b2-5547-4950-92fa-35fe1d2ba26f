import type { decimal } from '@sage/xtrem-core';
import { NodeExtension, decorators } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremTechnicalData from '@sage/xtrem-technical-data';

@decorators.nodeExtension<ComponentExtension>({
    extends: () => xtremTechnicalData.nodes.Component,
})
export class ComponentExtension extends NodeExtension<xtremTechnicalData.nodes.Component> {
    @decorators.decimalProperty<ComponentExtension, 'stockOnHand'>({
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.quantityInStockUnit,
        dependsOn: ['item', { billOfMaterial: ['site'] }],
        async getValue() {
            const itemSite = await this.itemSite;
            if (itemSite) {
                return itemSite.inStockQuantity;
            }
            return 0;
        },
    })
    readonly stockOnHand: Promise<decimal>;

    @decorators.decimalProperty<ComponentExtension, 'availableQuantityInStockUnit'>({
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.quantityInStockUnit,
        dependsOn: ['unit', 'item', { billOfMaterial: ['site'] }],
        lookupAccess: true,
        async getValue() {
            const itemSite = await this.itemSite;
            if (itemSite) {
                return (await itemSite.acceptedStockQuantity) - (await itemSite.allocatedQuantity);
            }
            return 0;
        },
    })
    readonly availableQuantityInStockUnit: Promise<decimal>;

    @decorators.decimalProperty<ComponentExtension, 'stockShortageInStockUnit'>({
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.quantityInStockUnit,
        dependsOn: ['unit', 'item', { billOfMaterial: ['site'] }],
        async getValue() {
            const itemSite = await this.itemSite;
            if (!itemSite) {
                return 0;
            }

            const availableQuantityInStockUnit = await this.availableQuantityInStockUnit;
            const quantityInStockUnit = await itemSite.inStockQuantity;
            const quantityAllocated = await itemSite.allocatedQuantity;

            const shortage = quantityInStockUnit - (availableQuantityInStockUnit + quantityAllocated);
            if (shortage > 0) {
                return shortage;
            }
            return 0;
        },
    })
    readonly stockShortageInStockUnit: Promise<decimal>;

    @decorators.booleanProperty<ComponentExtension, 'hasStockShortage'>({
        isPublished: true,
        dependsOn: ['stockShortageInStockUnit'],
        async getValue() {
            return (await this.stockShortageInStockUnit) > 0;
        },
    })
    readonly hasStockShortage: Promise<boolean>;
}

declare module '@sage/xtrem-technical-data/lib/nodes/component' {
    export interface Component extends ComponentExtension {}
}
