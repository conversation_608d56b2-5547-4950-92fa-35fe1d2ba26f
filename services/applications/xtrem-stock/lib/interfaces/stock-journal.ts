import type { date } from '@sage/xtrem-core';
import type * as xtremMasterData from '@sage/xtrem-master-data';
import type * as xtremSystem from '@sage/xtrem-system';

/**
 * Used to fill the sequence key
 */
export type StockJournalSearchData = {
    site: xtremSystem.nodes.Site | xtremSystem.nodes.Site['_id'];
    item: xtremMasterData.nodes.Item | xtremMasterData.nodes.Item['_id'];
    effectiveDate: date;
};

/**
 * Key of StockJournal
 */
export interface StockJournalKey extends StockJournalSearchData {
    sequence: number;
    _id: number;
}
