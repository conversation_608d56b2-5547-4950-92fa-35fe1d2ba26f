import type { integer } from '@sage/xtrem-core';

/**
 * Example of parameters in xtrem-config.yml:
 * ```
packages:
    '@sage/xtrem-stock':
        errorGenerator:
            stopAfterStockUpdate: true # triggers an exception at the end of all stock modifications/update
            stockCorrection: 0 # simulates that an error occurred at the beginning of a stock correction process
            stockReceipt: 1 # simulates that an error occurred for the 2nd line of the stock receipt lines to create
 *  ```
 */

/**
 * To reference when the error must be thrown
 * For the moment it's only the group index
 */
type GroupReference = integer;

export interface StockConfig {
    sleepTime: integer;
    errorGenerator: {
        /**
         * To generate an exception after _stockValueUpdate function (concerns all type of transaction)
         */
        stopAfterStockUpdate?: boolean;
        /**
         * To generate an error for a stock issue
         */
        stockIssue?: GroupReference;
        /**
         * To generate an error for a stock issue
         */
        stockReceipt?: GroupReference;
        /**
         * To generate an error for a stock adjustment
         */
        stockAdjustment?: GroupReference;
        /**
         * To generate an error for a stock change
         */
        stockChange?: GroupReference;
        /**
         * To generate an error for a stock correction
         */
        stockCorrection?: GroupReference;
        /**
         * To generate an error for a stock value change
         */
        stockValueChange?: GroupReference;
    };
}
