import type * as xtremMasterData from '@sage/xtrem-master-data';
import type * as xtremSystem from '@sage/xtrem-system';
import type * as xtremStock from '..';

export interface StockCountItemSiteFilter {
    stockSite: xtremSystem.nodes.Site;
    fromItem?: xtremMasterData.nodes.Item | null;
    toItem?: xtremMasterData.nodes.Item | null;
    lastCountDate?: string | null;
    hasStockRecords: boolean;
    locations: Array<xtremMasterData.nodes.Location>;
    zones: Array<xtremMasterData.nodes.LocationZone>;
    categories: Array<xtremMasterData.nodes.ItemCategory>;
    concurrentStockCountLines: Array<xtremStock.nodes.StockCountLine>;
}

export interface StockCountFilterSelection {
    stockCount: xtremStock.nodes.StockCount;
    itemSites: number[];
    stockRecords: number[];
    allSelected: boolean;
}
