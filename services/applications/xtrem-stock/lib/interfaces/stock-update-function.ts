import type * as xtremCommunication from '@sage/xtrem-communication';
import type { Context } from '@sage/xtrem-core';
import type * as xtremStockData from '@sage/xtrem-stock-data';
import type * as xtremStock from '..';

export type StockUpdateParametersWithValuationManager<T extends xtremStockData.enums.StockMovementTypeEnum> = {
    // optional while all valuation methods are not managed
    stockValuationManager?: xtremStock.classes.StockValuationManager;
} & xtremStockData.interfaces.StockUpdateParameters<T>;

export type StockUpdateFunction = (
    context: Context,
    stockDetails: xtremStockData.nodes.BaseStockDetail[],
    stockUpdateParameters: StockUpdateParametersWithValuationManager<xtremStockData.enums.StockMovementTypeEnum>,
) => Promise<xtremStockData.interfaces.StockEngineReturn[]>;

type NotificationSharedData<MovementType extends xtremStockData.enums.StockMovementTypeEnum> = Omit<
    xtremStockData.interfaces.StockPostingPayload<MovementType>,
    'documents'
> & {
    notificationId: Awaited<xtremCommunication.nodes.SysNotification['notificationId']>;
};

export type NotificationDocumentData<MovementType extends xtremStockData.enums.StockMovementTypeEnum> =
    xtremStockData.interfaces.StockPostingPayload<MovementType>['documents'][0] & NotificationSharedData<MovementType>;

export type NotificationLineData<MovementType extends xtremStockData.enums.StockMovementTypeEnum> =
    xtremStockData.interfaces.StockPostingPayload<MovementType>['documents'][0]['lines'][0] &
        NotificationSharedData<MovementType>;

export type StockEngineParameters<T extends xtremStockData.enums.StockMovementTypeEnum> = {
    stockDetail: xtremStockData.nodes.BaseStockDetail;
    searchData: xtremStockData.interfaces.StockSearchData;
    stockRecord: xtremStockData.nodes.Stock | null;
    stockUpdateParameters: StockUpdateParametersWithValuationManager<T>;
} & (T extends xtremStockData.enums.StockMovementTypeEnum.receipt
    ? {
          stockDetail: xtremStockData.nodes.StockReceiptDetail | xtremStockData.nodes.StockCorrectionDetail;
      }
    : {}) &
    (T extends xtremStockData.enums.StockMovementTypeEnum.issue
        ? {
              stockDetail: xtremStockData.nodes.StockIssueDetail | xtremStockData.nodes.StockCorrectionDetail;
          }
        : {}) &
    (T extends xtremStockData.enums.StockMovementTypeEnum.change
        ? {
              stockDetail: xtremStockData.nodes.StockChangeDetail | xtremStockData.nodes.StockCorrectionDetail;
          }
        : {}) &
    (T extends xtremStockData.enums.StockMovementTypeEnum.adjustment
        ? {
              stockDetail: xtremStockData.nodes.StockAdjustmentDetail | xtremStockData.nodes.StockCorrectionDetail;
          }
        : {}) &
    (T extends xtremStockData.enums.StockMovementTypeEnum.correction
        ? {
              stockDetail: xtremStockData.nodes.StockCorrectionDetail;
          }
        : {}) &
    (T extends xtremStockData.enums.StockMovementTypeEnum.valueChange
        ? {
              stockDetail: xtremStockData.nodes.StockValueDetail;
          }
        : {}) &
    (T extends xtremStockData.enums.StockMovementTypeEnum.transfer
        ? {
              stockDetail: xtremStockData.nodes.StockChangeDetail;
          }
        : {});
