import type { decimal } from '@sage/xtrem-core';
import type * as xtremStockData from '@sage/xtrem-stock-data';

export type StockValuationAction = 'createValue' | 'correctValue' | 'changeValue';

export type StockValuationManagerInstanceParameter =
    | { action: 'createValue'; stockDetails: Array<xtremStockData.nodes.BaseStockDetail> }
    | {
          action: 'correctValue';
          stockDetails: Array<xtremStockData.nodes.StockCorrectionDetail>;
      }
    | { action: 'changeValue'; stockDetails: Array<xtremStockData.nodes.StockValueDetail> };

/**
 * When creating a new instance of StockValuationManager, some data are spread/grouped by documentLine
 * These data are:
 * - quantity: the quantity of the group
 * - orderAmount: the order amount of the group
 * - valuedAmount: the movement amount of the group
 * - nonAbsorbedAmount: the non absorbed amount of the group
 * When a StockJournal will be created, getOrderAmount will retrieve the order amount corresponding to the group (document line)
 * pro-rated with quantity of the StockJournal
 *
 * @example
 *  manager.stockValuationGroups = {
  "1148": {
    "documentLine": 1148,
    "quantity": 10,
    "orderAmount": 123
  },
  "1159": {
    "documentLine": 1159,
    "quantity": 20,
    "orderAmount": 45
  }

  // If 2 StockJournal are created with quantity = 3 and 7:
manager.getOrderAmount(1148, 3) -> 123*3/10 = 36.9
manager.getOrderAmount(1148, 7) -> 123*7/10 = 86.1

 */
export interface StockValuationGroup {
    /**
     * documentLine on which is applied the valuation or the correction
     */
    documentLine: xtremStockData.interfaces.AnyStockDocumentLineWithoutStockDetail;

    stockDetailId: xtremStockData.nodes.BaseStockDetail['_id'];

    /**
     * sum of stockDetails.quantityInStockUnit => is >0 for receipts and is <0 for issues
     */
    quantity: decimal;
    /**
     * total order amount that will be applied to all StockJournal records of a group
     */
    orderAmount: decimal;
    /**
     * total valued amount that will be applied to all StockJournal records of a group
     */
    valuedAmount: decimal;
    /**
     * total non absorbed amount that will be applied to all StockJournal records of a group
     */
    nonAbsorbedAmount: decimal;
}
