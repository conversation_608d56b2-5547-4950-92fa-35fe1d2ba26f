import type { decimal, integer } from '@sage/xtrem-core';
import type * as xtremStock from '..';

export interface CreatedOrUpdatedItemSiteCost {
    logStatus: 'info' | 'error';
    creationStatus: xtremStock.enums.StandardCostRollUpResultLineStatus;
    message: string;
    costRollUpResultLine: xtremStock.nodes.CostRollUpResultLine;
}

export type CostSumObject = {
    totalCost: decimal;
    unitCost: decimal;
    materialCost: decimal;
    laborCost: decimal;
    machineCost: decimal;
    toolCost: decimal;
    hasResultLine: boolean;
};

export type ComputedItemCosts = Record<integer, Record<decimal, CostSumObject>>;

export type BomExplorationFunctionalData = {
    bomBaseQuantity: decimal;
    componentBomBaseQuantity?: decimal;
    componentRequiredQuantityForUserRequirement: decimal;
};
