import type { date, decimal, integer } from '@sage/xtrem-core';
import type * as xtremMasterData from '@sage/xtrem-master-data';
import type * as xtremSystem from '@sage/xtrem-system';

/**
 * Parameters to get the stock valuation of an item-site
 * @member {integer} itemSite = _id of an ItemSite
 * @member {date} dateOfValuation = requested date for the stock valuation. If null, is replaced by today
 */
export interface StockValuationSearch {
    itemSite: integer;
    dateOfValuation?: date;
}

/**
 * Content of the stock valuation result for an item-site
 * @member {decimal} quantityInStock  = quantity in stock unit
 * @member {decimal} stockValue = total stock valuation
 * @member {decimal} unitCost = unit cost of the stock valuation = stockValue / quantityInStock
 */
export interface StockValuationResult {
    quantityInStock: decimal;
    unitCost: decimal;
    stockValue: decimal;
}

export interface CommonStockCountSearchData {
    site: xtremSystem.nodes.Site;
    locations?: Array<xtremMasterData.nodes.Location>;
    zones?: Array<xtremMasterData.nodes.LocationZone>;
}
