import { withoutEdges } from '@sage/xtrem-client';
import type { FifoValuationTier, GraphApi } from '@sage/xtrem-stock-data-api';
import * as DocumentLink from '@sage/xtrem-stock-data/build/lib/client-functions/document-link';
import * as ui from '@sage/xtrem-ui';

@ui.decorators.page<FifoValuationTierTablePanel, FifoValuationTier>({
    title: 'Add from FIFO stack',
    mode: 'default',
    node: '@sage/xtrem-stock-data/FifoValuationTier',
    isTransient: true,
    module: 'xtrem-stock-data',
    access: { node: '@sage/xtrem-stock/StockValueChange', bind: '$read' },
    businessActions() {
        return [this.cancel, this.confirm];
    },
    async onLoad() {
        this.$.loader.isHidden = false;
        this.fifoValuationTierLines.value = await this.loadFifoValuationTierLines(
            this.$.queryParameters.siteId.toString(),
            this.$.queryParameters.itemId.toString(),
            this.$.queryParameters.existingFifo.toString(),
        );
        this.$.setPageClean();
        this.$.loader.isHidden = true;
    },
})
export class FifoValuationTierTablePanel extends ui.Page<GraphApi> {
    @ui.decorators.pageAction<FifoValuationTierTablePanel>({
        title: 'Add',
        async onClick() {
            const validation = await this.$.page.validate();
            if (validation.length === 0) {
                const fifoValuationTierLines: ui.PartialCollectionValue<FifoValuationTier>[] =
                    this.fifoValuationTierLines.selectedRecords.map(rowId => {
                        const line = this.fifoValuationTierLines.getRecordValue(rowId);
                        return {
                            ...line,
                            _id: line?._id,
                            effectiveDate: line?.effectiveDate,
                            sequence: line?.sequence,
                            receiptQuantity: line?.receiptQuantity,
                            remainingQuantity: line?.remainingQuantity,
                            unitCost: line?.unitCost,
                            amount: line?.amount,
                            receiptDocumentLine: {
                                documentNumber: line?.receiptDocumentLine?.documentNumber,
                            },
                        };
                    });
                this.$.finish({
                    lines: fifoValuationTierLines,
                });
            } else {
                this.$.showToast(validation.join('\n'), { type: 'error' });
            }
        },
        isDisabled() {
            return this.fifoValuationTierLines.selectedRecords.length <= 0;
        },
    })
    confirm: ui.PageAction;

    @ui.decorators.pageAction<FifoValuationTierTablePanel>({
        title: 'Cancel',
        buttonType: 'tertiary',
        onClick() {
            this.$.finish();
        },
    })
    cancel: ui.PageAction;

    @ui.decorators.section<FifoValuationTierTablePanel>({
        isTitleHidden: true,
    })
    lineSection: ui.containers.Section;

    @ui.decorators.block<FifoValuationTierTablePanel>({
        parent() {
            return this.lineSection;
        },
    })
    lineBlock: ui.containers.Block;

    @ui.decorators.separatorField<FifoValuationTierTablePanel>({
        parent() {
            return this.lineBlock;
        },
        isFullWidth: true,
        isHidden: true,
    })
    lineStatusSeparator: ui.fields.Separator;

    @ui.decorators.tableField<FifoValuationTierTablePanel, FifoValuationTier>({
        parent() {
            return this.lineBlock;
        },
        canUserHideColumns: false,
        isReadOnly: true,
        node: '@sage/xtrem-stock-data/FifoValuationTier',
        columns: [
            ui.nestedFields.technical({
                bind: '_id',
            }),
            ui.nestedFields.text({
                bind: 'effectiveDate',
                title: 'Effective Date',
            }),
            ui.nestedFields.text({
                bind: 'sequence',
                title: 'Sequence',
            }),
            ui.nestedFields.link({
                title: 'Document number',
                bind: { receiptDocumentLine: { documentNumber: true } },
                // TODO: DNE REFAC As soon as the page decorator can handle showing the page in a new tab
                //                 uncomment and adapt + remove the work around (onClick).
                // page(_value, rowData) {
                //     return rowData?.receiptDocumentLine?._constructor
                //         ? DocumentLink.getDocumentPageName(rowData.receiptDocumentLine._constructor)
                //         : DocumentLink.getDocumentPageName(rowData?.receiptDocumentLine?._constructor || '');
                // },
                // queryParameters(_value, rowData) {
                //     return {
                //         _id: rowData?.receiptDocumentLine?.documentId
                //             ? rowData?.receiptDocumentLine?.documentId
                //             : rowData?.receiptDocumentLine?.documentId || '',
                //     };
                // },
                onClick(_id: string, rowData: any) {
                    const pageName = rowData?.receiptDocumentLine?._constructor
                        ? (DocumentLink.getDocumentPageName(rowData.receiptDocumentLine._constructor) as string)
                        : '';
                    const docId = rowData?.receiptDocumentLine?.documentId
                        ? rowData?.receiptDocumentLine?.documentId
                        : 0;
                    if (pageName.length && docId) {
                        this.$.router.goToExternal(`${pageName}/${btoa(JSON.stringify({ _id: docId }))}`);
                    }
                },
            }),
            ui.nestedFields.text({
                bind: 'receiptQuantity',
                title: 'Receipt quantity',
            }),
            ui.nestedFields.text({
                bind: 'remainingQuantity',
                title: 'Remaining quantity',
            }),
            ui.nestedFields.text({
                bind: 'unitCost',
                title: 'Unit cost',
            }),
            ui.nestedFields.text({
                bind: 'amount',
                title: 'Amount',
            }),
            ui.nestedFields.text({
                bind: 'nonAbsorbedAmount',
                title: 'Non-absorbed amount',
            }),
        ],
    })
    fifoValuationTierLines: ui.fields.Table<FifoValuationTier>;

    async loadFifoValuationTierLines(siteId: string, itemId: string, existingFifo: string) {
        return withoutEdges(
            await this.$.graph
                .node('@sage/xtrem-stock-data/FifoValuationTier')
                .query(
                    ui.queryUtils.edgesSelector(
                        {
                            _id: true,
                            effectiveDate: true,
                            sequence: true,
                            receiptQuantity: true,
                            remainingQuantity: true,
                            amount: true,
                            unitCost: true,
                            nonAbsorbedAmount: true,
                            receiptDocumentLine: {
                                _constructor: true,
                                documentNumber: true,
                                documentId: true,
                            },
                        },
                        {
                            filter: {
                                item: { id: itemId },
                                site: { id: siteId },
                                remainingQuantity: { _gt: 0 },
                                _id: {
                                    _nin: JSON.parse(existingFifo as string),
                                },
                            },
                        },
                    ),
                )
                .execute(),
        );
    }
}
