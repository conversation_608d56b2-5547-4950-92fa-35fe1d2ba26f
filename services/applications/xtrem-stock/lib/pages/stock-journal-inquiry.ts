import { withoutEdges } from '@sage/xtrem-client';
import type { FinanceTransaction } from '@sage/xtrem-finance-data-api';
import type { Currency, Item, Location, ReasonCode, UnitOfMeasure } from '@sage/xtrem-master-data-api';
import type { GraphApi } from '@sage/xtrem-stock-api';
import type { StockJournal, StockStatus } from '@sage/xtrem-stock-data-api';
import * as DocumentLink from '@sage/xtrem-stock-data/build/lib/client-functions/document-link';
import { StockInquiries } from '@sage/xtrem-stock-data/build/lib/menu-items/stock-inquiries';
import type { Site } from '@sage/xtrem-system-api';
import * as ui from '@sage/xtrem-ui';
import * as StockJournalInquiryFunctions from '../client-functions/stock-journal-inquiry-action-functions';

@ui.decorators.page<StockJournalInquiry, StockJournal>({
    menuItem: StockInquiries,
    priority: 155,
    title: 'Stock journal inquiry',
    module: 'stock',
    mode: 'default',
    node: '@sage/xtrem-stock-data/StockJournal',
    access: { node: '@sage/xtrem-stock-data/StockJournal', bind: '$read' },
    navigationPanel: {
        onSelect() {
            // We have to prevent selecting a line to avoid opening the "normal" page
            // (which is not even defined).
            return true;
        },
        listItem: {
            title: ui.nestedFields.text({
                bind: '_id',
                isHidden: true,
            }),
            siteName: ui.nestedFields.reference<StockJournalInquiry, StockJournal, Site>({
                bind: 'site',
                node: '@sage/xtrem-system/Site',
                tunnelPage: '@sage/xtrem-master-data/Site',
            }),
            siteId: ui.nestedFields.text({
                bind: { site: { id: true } },
                title: 'Site ID',
                isHiddenOnMainField: true,
            }),
            effectiveDate: ui.nestedFields.date({
                bind: 'effectiveDate',
                title: 'Date',
            }),
            documentLineDocumentNumber: ui.nestedFields.technical({
                bind: { documentLine: { documentNumber: true } },
            }), // Needed for the document link
            documentLineDocumentId: ui.nestedFields.technical({
                bind: { documentLine: { documentId: true } },
            }), // Needed for the document link
            documentLineFactoryName: ui.nestedFields.technical({
                bind: { documentLine: { _factory: { name: true } } },
            }), // Needed for the link to the journal entry
            sourceDocumentLineDocumentId: ui.nestedFields.technical({
                bind: { sourceDocumentLine: { documentId: true } },
            }), // Needed for the link to the journal entry
            documentLineType: ui.nestedFields.text({
                bind: { documentLine: { _factory: { title: true } } },
                title: 'Document line type',
            }),
            documentLink: ui.nestedFields.link({
                isFullWidth: true,
                bind: { documentLine: { documentNumber: true } },
                title: 'Document',
                onClick(_value, rowData) {
                    return this.$.dialog.page(
                        DocumentLink.getDocumentPageName(rowData?.documentLine?._factory?.name ?? ''),
                        { _id: rowData?.documentLine?.documentId || '' },
                        { fullScreen: true, resolveOnCancel: true },
                    );
                },
            }),
            itemName: ui.nestedFields.reference<StockJournalInquiry, StockJournal, Item>({
                bind: 'item',
                title: 'Item',
                node: '@sage/xtrem-master-data/Item',
                tunnelPage: '@sage/xtrem-master-data/Item',
                valueField: 'name',
            }),
            itemId: ui.nestedFields.text({
                bind: { item: { id: true } },
                title: 'Item ID',
            }),
            itemDescription: ui.nestedFields.text({
                bind: { item: { description: true } },
                title: 'Item description',
                isHiddenOnMainField: true,
            }),
            isItemSerialNumberManaged: ui.nestedFields.technical({ bind: { item: { serialNumberManagement: true } } }),
            quantityInStockUnit: ui.nestedFields.numeric({
                bind: 'quantityInStockUnit',
                title: 'Quantity',
                scale(_rowId, rowData) {
                    return rowData?.stockUnit?.decimalDigits ?? 0;
                },
                postfix(_rowId, rowData) {
                    return rowData?.stockUnit?.symbol ?? '';
                },
                groupAggregationMethod: 'sum',
            }),
            locationName: ui.nestedFields.reference<StockJournalInquiry, StockJournal, Location>({
                bind: 'location',
                title: 'Location',
                node: '@sage/xtrem-master-data/Location',
                tunnelPage: '@sage/xtrem-master-data/Location',
                valueField: 'name',
            }),
            locationId: ui.nestedFields.text({
                bind: { location: { id: true } },
                title: 'Location ID',
                isHiddenOnMainField: true,
            }),
            locationZone: ui.nestedFields.text({
                bind: { location: { locationZone: { name: true } } },
                title: 'Location zone',
                isHiddenOnMainField: true,
            }),
            stockStatus: ui.nestedFields.reference<StockJournalInquiry, StockJournal, StockStatus>({
                bind: 'status',
                title: 'Quality control',
                node: '@sage/xtrem-stock-data/StockStatus',
                tunnelPage: '@sage/xtrem-stock-data/StockStatus',
                valueField: 'name',
            }),
            lot: ui.nestedFields.text({
                bind: { lot: { id: true } },
                title: 'Lot',
            }),
            sublot: ui.nestedFields.text({
                bind: { lot: { sublot: true } },
                title: 'Sublot',
                isHiddenOnMainField: true,
            }),
            supplierLot: ui.nestedFields.text({
                bind: { lot: { supplierLot: true } },
                title: 'Supplier lot number',
            }),
            expirationDate: ui.nestedFields.date({
                bind: { lot: { expirationDate: true } },
                title: 'Expiration date',
                isHiddenOnMainField: true,
            }),
            orderCost: ui.nestedFields.numeric({
                bind: 'orderCost',
                title: 'Order cost',
                scale: 4,
                prefix(_rowId, rowData) {
                    return rowData?.currency?.symbol ?? '';
                },
            }),
            orderAmount: ui.nestedFields.numeric({
                bind: 'orderAmount',
                title: 'Order amount',
                unit: (_id, rowData) => rowData?.currency,
                groupAggregationMethod: 'sum',
            }),
            valuedCost: ui.nestedFields.numeric({
                bind: 'valuedCost',
                title: 'Valued cost',
                scale: 4,
                prefix(_rowId, rowData) {
                    return rowData?.currency?.symbol ?? '';
                },
            }),
            movementAmount: ui.nestedFields.numeric({
                bind: 'movementAmount',
                title: 'Movement amount',
                unit: (_id, rowData) => rowData?.currency,
                groupAggregationMethod: 'sum',
            }),
            costVariance: ui.nestedFields.numeric({
                bind: 'costVariance',
                title: 'Cost variance',
                scale: 4,
                prefix(_rowId, rowData) {
                    return rowData?.currency?.symbol ?? '';
                },
            }),
            amountVariance: ui.nestedFields.numeric({
                bind: 'amountVariance',
                title: 'Amount variance',
                unit: (_id, rowData) => rowData?.currency,
                groupAggregationMethod: 'sum',
            }),
            nonAbsorbedAmount: ui.nestedFields.numeric({
                bind: 'nonAbsorbedAmount',
                title: 'Non-absorbed amount',
                unit: (_id, rowData) => rowData?.currency,
                groupAggregationMethod: 'sum',
            }),
            stockUnit: ui.nestedFields.reference<StockJournalInquiry, StockJournal, UnitOfMeasure>({
                bind: 'stockUnit',
                title: 'Unit of measure',
                node: '@sage/xtrem-master-data/UnitOfMeasure',
                tunnelPage: '@sage/xtrem-master-data/UnitOfMeasure',
                columns: [
                    ui.nestedFields.technical({ bind: 'symbol' }),
                    ui.nestedFields.technical({ bind: 'decimalDigits' }),
                ],
                isHiddenOnMainField: true,
            }),
            currency: ui.nestedFields.reference<StockJournalInquiry, StockJournal, Currency>({
                bind: 'currency',
                title: 'Currency',
                node: '@sage/xtrem-master-data/Currency',
                tunnelPage: '@sage/xtrem-master-data/Currency',
                isHiddenOnMainField: true,
            }),
            currencyId: ui.nestedFields.text({
                bind: { currency: { id: true } },
                title: 'Currency ID',
                isHiddenOnMainField: true,
            }),
            reasonCode: ui.nestedFields.reference<StockJournalInquiry, StockJournal, ReasonCode>({
                bind: 'reasonCode',
                title: 'Reason code',
                node: '@sage/xtrem-master-data/ReasonCode',
                tunnelPage: '@sage/xtrem-master-data/ReasonCode',
            }),
            movementType: ui.nestedFields.select({
                bind: 'movementType',
                title: 'Movement type',
                optionType: '@sage/xtrem-stock-data/StockMovementType',
            }),
        },
        dropdownActions: [
            // TODO: We have to hide this action as we noticed that the current serial number inquiry´page is not
            //       sufficient for the needs of the users and even might confuse. We will create a new page for
            //       the serial number information. (XT-78861)
            // {
            //     title: 'Serial number information',
            //     icon: 'draft',
            //     isHidden(_recordId: string, rowData: ui.PartialNodeWithId<StockJournal>) {
            //         return rowData.item?.serialNumberManagement !== 'managed';
            //     },
            //     onClick(_recordId: string, rowData: ui.PartialNodeWithId<StockJournal>) {
            //         const filter = {
            //             _filter: JSON.stringify({
            //                 stockMovements: {
            //                     _atLeast: 1,
            //                     stockMovement: rowData._id,
            //                 },
            //             }),
            //         };
            //         this.$.router.goTo(`@sage/xtrem-stock/SerialNumberInquiry`, filter);
            //     },
            // },
            {
                title: 'Journal entry',
                icon: 'document_right_align',
                async onClick(_recordId: string, rowItem: ui.PartialNodeWithId<StockJournal>) {
                    const journalEntries = await this.getJournalEntries({
                        documentLineDocumentId: rowItem.documentLine?.documentId ?? -1,
                        documentLineDocumentNumber: rowItem.documentLine?.documentNumber ?? '',
                        documentLineFactoryName: rowItem.documentLine?._factory?.name ?? '',
                        sourceDocumentLineDocumentId: rowItem.sourceDocumentLine?.documentId ?? -1,
                        movementType: rowItem.movementType ?? '',
                    });

                    if (!journalEntries.length) {
                        this.$.showToast(
                            ui.localize(
                                '@sage/xtrem-stock/pages__stock_journal_inquiry__no_journal_entry_found',
                                'No journal entry found for the selected document.',
                            ),
                            { timeout: 0, type: 'warning' },
                        );
                        return;
                    }

                    // If there is only one record, we can directly open it.
                    if (journalEntries.length === 1) {
                        await this.$.dialog.page(
                            '@sage/xtrem-finance/JournalEntry',
                            { _id: journalEntries[0].documentSysId },
                            { fullScreen: true, resolveOnCancel: true },
                        );
                        return;
                    }

                    // Otherwise let's open a filtered main list instead.
                    await this.$.dialog.page(
                        '@sage/xtrem-finance/JournalEntry',
                        {
                            [ui.QUERY_PARAM_CUSTOM_MAIN_LIST_FILTER]: JSON.stringify({
                                number: { _in: journalEntries.map(entry => entry.documentNumber) },
                            }),
                        },
                        {
                            isMainListDisplayedInDialog: true,
                            fullScreen: true,
                            resolveOnCancel: true,
                        },
                    );
                },
            },
        ],
        orderBy: { effectiveDate: -1, _id: -1 },
    },
})
export class StockJournalInquiry extends ui.Page<GraphApi, StockJournal> {
    async getJournalEntries(param: {
        documentLineDocumentId: number;
        documentLineDocumentNumber: string;
        documentLineFactoryName: string;
        sourceDocumentLineDocumentId: number;
        movementType: string;
    }): Promise<{ documentSysId: number; documentNumber: string }[]> {
        const documentType = StockJournalInquiryFunctions.getFinanceDocumentTypeFromDocumentLineType(
            param.documentLineFactoryName,
        );

        // For purchase receipts, we look for receipt entries in the finance transaction lines, too.
        if (param.movementType === 'receipt' && documentType === 'purchaseReceipt') {
            return (
                await this.$.graph
                    .node('@sage/xtrem-finance-data/FinanceTransaction')
                    .queries.getPostingStatusDataByDocumentId(
                        {
                            documentSysId: true,
                            documentNumber: true,
                        },
                        {
                            documentSysId: param.documentLineDocumentId.toString(),
                            documentType,
                        },
                    )
                    .execute()
            )
                .filter(transaction => transaction.documentSysId > 0 && transaction.documentNumber.length > 0)
                .map(transaction => ({
                    documentSysId: transaction.documentSysId,
                    documentNumber: transaction.documentNumber,
                }));
        }

        // If we have a source document we look for the finance transactions of the source document.
        if (param.sourceDocumentLineDocumentId > 0) {
            return withoutEdges(
                await this.$.graph
                    .node('@sage/xtrem-finance-data/FinanceTransaction')
                    .query(
                        ui.queryUtils.edgesSelector<FinanceTransaction>(
                            {
                                targetDocumentSysId: true,
                                targetDocumentNumber: true,
                            },
                            {
                                filter: {
                                    targetDocumentType: 'journalEntry',
                                    targetDocumentSysId: { _gt: 0 },
                                    documentSysId: param.sourceDocumentLineDocumentId,
                                },
                            },
                        ),
                    )
                    .execute(),
            ).map(transaction => ({
                documentSysId: transaction.targetDocumentSysId,
                documentNumber: transaction.targetDocumentNumber,
            }));
        }

        // Otherwise, we take the finance transactions into account.
        const sourceDocumentType = StockJournalInquiryFunctions.getSourceDocumentTypeFromDocumentLineType(
            param.documentLineFactoryName,
        );

        const documentFilter =
            param.documentLineFactoryName === 'SalesReturnReceiptLine'
                ? { documentNumber: param.documentLineDocumentNumber }
                : {
                      _or: [
                          { sourceDocumentNumber: param.documentLineDocumentNumber, sourceDocumentType },
                          { documentNumber: param.documentLineDocumentNumber },
                      ],
                  };
        return withoutEdges(
            await this.$.graph
                .node('@sage/xtrem-finance-data/FinanceTransaction')
                .query(
                    ui.queryUtils.edgesSelector<FinanceTransaction>(
                        {
                            targetDocumentSysId: true,
                            targetDocumentNumber: true,
                        },
                        {
                            filter: {
                                targetDocumentType: 'journalEntry',
                                targetDocumentSysId: { _gt: 0 },
                                ...documentFilter,
                            },
                        },
                    ),
                )
                .execute(),
        ).map(transaction => ({
            documentSysId: transaction.targetDocumentSysId,
            documentNumber: transaction.targetDocumentNumber,
        }));
    }
}
