import type { ExtractEdgesPartial } from '@sage/xtrem-client';
import { withoutEdges } from '@sage/xtrem-client';
import { DateValue } from '@sage/xtrem-date-time';
import type { PostingClass } from '@sage/xtrem-finance-data-api';
import type { Currency, Item, ItemCategory } from '@sage/xtrem-master-data-api';
import * as masterDataUtils from '@sage/xtrem-master-data/build/lib/client-functions/utils';
import { setMultiReferenceIfSingleValue } from '@sage/xtrem-master-data/lib/client-functions/get-default';
import type {
    GraphApi,
    StockValuationInputSetToSite,
    StockValuationResultLine,
    StockValuationStatus,
} from '@sage/xtrem-stock-api';
import { StockValuationMenu } from '@sage/xtrem-stock-data/build/lib/menu-items/stock-valuation-menu';
import type { Company, Site, User } from '@sage/xtrem-system-api';
import * as ui from '@sage/xtrem-ui';
import * as PillColor from '../client-functions/pill-color';
import * as StockValuationLib from '../client-functions/stock-valuation-lib';

@ui.decorators.page<StockValuation>({
    menuItem: StockValuationMenu,
    priority: 11,
    title: 'Stock valuation',
    objectTypeSingular: 'Stock valuation',
    node: '@sage/xtrem-stock/StockValuationInputSet',
    module: 'stock',
    mode: 'default',
    access: { node: '@sage/xtrem-stock/StockValuationInputSet', bind: '$read' },
    createAction() {
        return this.$standardNewAction;
    },
    businessActions() {
        return [this.runStockValuation];
    },
    async defaultEntry() {
        // As there is only one record per user try to load this record.
        //
        // NOTE (for local Cucumber script test on local development instances): Please make sure that the user
        // e-mail address in the top field is set to the e-mail address of the user configured in xtrem-config.yml!
        const stockValuationId = withoutEdges(
            await this.$.graph
                .node('@sage/xtrem-stock/StockValuationInputSet')
                .query(
                    ui.queryUtils.edgesSelector(
                        { _id: true },
                        { filter: { user: { email: this.$.username } }, first: 1 },
                    ),
                )
                .execute(),
        );

        // If there is such record open it. Otherwise open work with an empty page in creation mode.
        return stockValuationId.at(0)?._id || '$new';
    },
    async onLoad() {
        if (!this.$.recordId) {
            // In case we have no record for this user pre-set the calculation user field.
            await this.$.fetchDefaults(['user']);
            await setMultiReferenceIfSingleValue([this.sites]);
        }

        // We only want to set the sites list if we have more than one site.
        if (this.sites.value.length > 1) {
            // Update the multiReferenceField for sites with the site collection in the hidden grid.
            this.updateSitesFromSiteTable();
        }

        // Hide the total stock value in case there is more than one currency for in this case we cannot add
        // the amounts. Note, that in case a company is selected all amounts are of the same currency.
        if (this.company.value?._id?.length) {
            this.tileContainer.isHidden = false;
            this.totalStockValueCurrency.value = this.company.value?.currency ?? null;
        } else if (this.sites.value.length === 1) {
            this.tileContainer.isHidden = false;
            this.totalStockValueCurrency.value = this.sites.value[0].legalCompany?.currency ?? null;
        } else if (this.sites.value.length === 0) {
            this.tileContainer.isHidden = true;
        } else {
            const firstCurrency = this.sites.value[0].legalCompany?.currency;
            const firstCurrencyId = firstCurrency?._id;
            this.totalStockValueCurrency.value = firstCurrency ?? null;
            this.tileContainer.isHidden = this.sites.value.some(
                site => site.legalCompany?.currency?._id !== firstCurrencyId,
            );
        }
    },
})
export class StockValuation extends ui.Page<GraphApi> {
    @ui.decorators.pageAction<StockValuation>({
        title: 'Run',
        isDisabled() {
            return this.status.value === 'inProgress';
        },
        onError(error: string | (Error & { errors: Array<any> })) {
            return masterDataUtils.formatError(this, error);
        },
        async onClick() {
            // Mark the record as 'in progress'.
            //
            // Note: a) This is done in the async mutation, too. But we have to signal the page that
            //          from now on the RUN button should be disabled to prevent another activation.
            //          while we asynchronously calculate the current request.
            //       b) We have to wait for a fraction of a second to prevent flickering.
            this.status.value = 'inProgress';
            await new Promise(resolve => {
                setTimeout(resolve, 0);
            });

            // Before we can do the calculation asynchronously we must save the current record.
            await this.$standardSaveAction.execute(true);

            // Notify the user that a calculation request is on the way.
            this.$.showToast(
                ui.localize(
                    '@sage/xtrem-stock/pages__stock_valuation_request__notification_request_sent',
                    'Stock valuation request sent.',
                ),
                { type: 'success' },
            );

            // Request calculation in an asynchronous mutation.
            const isSuccess: boolean = await this.$.graph
                .node('@sage/xtrem-stock/StockValuationInputSet')
                .asyncOperations.stockValuation.runToCompletion(true, {
                    userId: this.user.value?._id,
                })
                .execute();

            // After processing signal success or error.
            this.$.showToast(
                isSuccess
                    ? ui.localize(
                          '@sage/xtrem-stock/pages__stock_valuation_request__notification_valuation_finished',
                          'Stock valuation finished.',
                      )
                    : ui.localize(
                          '@sage/xtrem-stock/pages__stock_valuation_request__notification_valuation_failed',
                          'Stock valuation failed.',
                      ),
                isSuccess ? { type: 'success' } : { type: 'error' },
            );
            await this.$.router.refresh(true);
        },
        buttonType: 'primary',
    })
    runStockValuation: ui.PageAction;

    @ui.decorators.section<StockValuation>({
        isTitleHidden: true,
        title: 'General',
    })
    mainSection: ui.containers.Section;

    @ui.decorators.block<StockValuation>({
        parent() {
            return this.mainSection;
        },
        width: 'extra-large',
        title: 'Criteria',
    })
    criteriaBlock: ui.containers.Block;

    @ui.decorators.referenceField<StockValuation, User>({
        parent() {
            return this.criteriaBlock;
        },
        title: 'Calculation user',
        bind: 'user',
        valueField: 'displayName',
        node: '@sage/xtrem-system/User',
        minLookupCharacters: 1,
        lookupDialogTitle: 'Select user',
        columns: [
            ui.nestedFields.text({ bind: 'firstName', title: 'First name' }),
            ui.nestedFields.text({ bind: 'lastName', title: 'Last name' }),
            ui.nestedFields.text({ bind: 'email', title: 'Email' }),
        ],
        isReadOnly: true,
        width: 'small',
        fetchesDefaults: true,
    })
    user: ui.fields.Reference<User>;

    @ui.decorators.referenceField<StockValuation, Company>({
        parent() {
            return this.criteriaBlock;
        },
        title: 'Company',
        node: '@sage/xtrem-system/Company',
        tunnelPage: '@sage/xtrem-master-data/Company',
        lookupDialogTitle: 'Select company',
        minLookupCharacters: 0,
        valueField: 'name',
        helperTextField: 'id',
        columns: [
            ui.nestedFields.text({ title: 'Name', bind: 'name' }),
            ui.nestedFields.text({ title: 'ID', bind: 'id' }),
            ui.nestedFields.reference({
                bind: 'currency',
                node: '@sage/xtrem-master-data/Currency',
                valueField: 'name',
                columns: [ui.nestedFields.technical({ bind: 'symbol' })],
                isHidden: true,
            }),
        ],
        filter() {
            return {
                isActive: { _eq: true },
            };
        },
        placeholder: 'Select...',
        width: 'small',
        onChange() {
            this.sites.value = [];
            this.updateSiteTableFromSites();
        },
    })
    company: ui.fields.Reference<Company>;

    @ui.decorators.multiReferenceField<StockValuation, Site>({
        parent() {
            return this.criteriaBlock;
        },
        isTransient: true,
        title: 'Site',
        node: '@sage/xtrem-system/Site',
        lookupDialogTitle: 'Select site',
        minLookupCharacters: 0,
        valueField: 'name',
        helperTextField: 'id',
        isDisabled() {
            return !!this.company.value;
        },
        columns: [
            ui.nestedFields.text({ title: 'Name', bind: 'name' }),
            ui.nestedFields.text({ title: 'ID', bind: 'id' }),
            ui.nestedFields.reference({
                bind: { legalCompany: { currency: true } },
                node: '@sage/xtrem-master-data/Currency',
                valueField: 'symbol',
                columns: [ui.nestedFields.text({ bind: 'id' }), ui.nestedFields.technical({ bind: 'symbol' })],
                isHidden: true,
            }),
        ],
        filter() {
            return {
                isActive: { _eq: true },
            };
        },
        placeholder: 'Select...',
        width: 'small',
        onChange() {
            this.updateSiteTableFromSites();
        },
    })
    sites: ui.fields.MultiReference<Site>;

    updateSitesFromSiteTable() {
        // Copy sites from hidden table (bound to node) to multi-reference field
        this.sites.value = this.selectedSites.value.map(line => line.site) as ExtractEdgesPartial<
            Site & { $id: string }
        >[];
        if (!this.totalStockValue.value) {
            this.totalStockValue.value = 0;
        }
    }

    updateSiteTableFromSites() {
        // Empty the hidden site table.
        this.selectedSites.value.forEach(line => this.selectedSites.removeRecord(line._id));

        // Copy sites from multi-reference field to hidden table (bound to node)
        this.sites.value.forEach(site =>
            this.selectedSites.addOrUpdateRecordValue({
                site,
            }),
        );
    }

    @ui.decorators.tableField<StockValuation, StockValuationInputSetToSite>({
        parent() {
            return this.criteriaBlock;
        },
        bind: 'sites',
        title: 'Sites',
        node: '@sage/xtrem-stock/StockValuationInputSetToSite',
        canSelect: false,
        isHidden: true,
        columns: [
            ui.nestedFields.reference<StockValuation, StockValuationInputSetToSite, Site>({
                node: '@sage/xtrem-system/Site',
                bind: 'site',
                title: 'Site',
                valueField: 'name',
                columns: [
                    ui.nestedFields.text({ title: 'Name', bind: 'name' }),
                    ui.nestedFields.text({ title: 'ID', bind: 'id' }),
                    ui.nestedFields.reference({
                        bind: { legalCompany: { currency: true } },
                        node: '@sage/xtrem-master-data/Currency',
                        valueField: 'symbol',
                        columns: [
                            ui.nestedFields.text({ title: 'ID', bind: 'id' }),
                            ui.nestedFields.technical({ bind: 'symbol' }),
                        ],
                    }),
                ],
            }),
        ],
        orderBy: { _sortValue: +1 },
    })
    selectedSites: ui.fields.Table<StockValuationInputSetToSite>;

    @ui.decorators.referenceField<StockValuation, Item>({
        parent() {
            return this.criteriaBlock;
        },
        title: 'From item',
        shouldSuggestionsIncludeColumns: true,
        node: '@sage/xtrem-master-data/Item',
        tunnelPage: '@sage/xtrem-master-data/Item',
        lookupDialogTitle: 'Select item',
        valueField: 'name',
        helperTextField: 'id',
        columns: [
            ui.nestedFields.text({ bind: 'name' }),
            ui.nestedFields.text({ bind: 'id' }),
            ui.nestedFields.text({ bind: 'description' }),
            ui.nestedFields.text({ title: 'Category', bind: { category: { name: true } } }),
            ui.nestedFields.checkbox({ bind: 'isStockManaged', isHidden: true }),
        ],
        filter: { isStockManaged: true },
        orderBy: { name: +1, id: +1 },
        placeholder: 'Select...',
        width: 'small',
    })
    fromItem: ui.fields.Reference<Item>;

    @ui.decorators.referenceField<StockValuation, Item>({
        parent() {
            return this.criteriaBlock;
        },
        title: 'To item',
        shouldSuggestionsIncludeColumns: true,
        node: '@sage/xtrem-master-data/Item',
        tunnelPage: '@sage/xtrem-master-data/Item',
        lookupDialogTitle: 'Select item',
        valueField: 'name',
        helperTextField: 'id',
        columns: [
            ui.nestedFields.text({ title: 'Name', bind: 'name' }),
            ui.nestedFields.text({ title: 'ID', bind: 'id' }),
            ui.nestedFields.text({ title: 'Description', bind: 'description' }),
            ui.nestedFields.text({ title: 'Category', bind: { category: { name: true } } }),
            ui.nestedFields.checkbox({ bind: 'isStockManaged', isHidden: true }),
        ],
        filter: { isStockManaged: true },
        orderBy: { name: +1, id: +1 },
        placeholder: 'Select...',
        width: 'small',
    })
    toItem: ui.fields.Reference<Item>;

    @ui.decorators.referenceField<StockValuation, ItemCategory>({
        parent() {
            return this.criteriaBlock;
        },
        title: 'Item category',
        node: '@sage/xtrem-master-data/ItemCategory',
        columns: [
            ui.nestedFields.text({ bind: 'name' }),
            ui.nestedFields.text({ bind: 'id' }),
            ui.nestedFields.dropdownList({ bind: 'type', title: 'Type' }),
        ],
        valueField: 'name',
        helperTextField: 'id',
        minLookupCharacters: 1,
        lookupDialogTitle: 'Select item category',
    })
    itemCategory: ui.fields.Reference<ItemCategory>;

    @ui.decorators.textField<StockValuation>({
        parent() {
            return this.criteriaBlock;
        },
        title: 'Commodity code',
    })
    commodityCode: ui.fields.Text;

    @ui.decorators.dropdownListField<StockValuation>({
        parent() {
            return this.criteriaBlock;
        },
        title: 'Valuation method',
        optionType: '@sage/xtrem-master-data/costValuationMethod',
        hasEmptyValue: true,
    })
    valuationMethod: ui.fields.DropdownList;

    @ui.decorators.referenceField<StockValuation, PostingClass>({
        parent() {
            return this.criteriaBlock;
        },
        title: 'Posting class',
        node: '@sage/xtrem-finance-data/PostingClass',
        valueField: 'name',
        width: 'medium',
        minLookupCharacters: 1,
        lookupDialogTitle: 'Select posting class',
        columns: [ui.nestedFields.text({ bind: 'name' })],
    })
    postingClass: ui.fields.Reference<PostingClass>;

    @ui.decorators.dateField<StockValuation>({
        parent() {
            return this.criteriaBlock;
        },
        title: 'Date',
        width: 'small',
        maxDate: DateValue.today().toString(),
    })
    date: ui.fields.Date;

    @ui.decorators.checkboxField<StockValuation>({
        parent() {
            return this.criteriaBlock;
        },
        title: 'Display zero values',
    })
    displayZeroValues: ui.fields.Checkbox;

    @ui.decorators.labelField<StockValuation>({
        title: 'Status',
        optionType: '@sage/xtrem-stock/StockValuationStatus',
        parent() {
            return this.criteriaBlock;
        },
        style() {
            return PillColor.getLabelColorByStatus('StockValuationStatus', this.status.value);
        },
    })
    status: ui.fields.Label<StockValuationStatus>;

    @ui.decorators.tile<StockValuation>({
        parent() {
            return this.mainSection;
        },
        isHidden: true,
    })
    tileContainer: ui.containers.Tile;

    @ui.decorators.aggregateField<StockValuation>({
        parent() {
            return this.tileContainer;
        },
        aggregateOn: 'stockValue',
        aggregationMethod: 'sum',
        title: 'Total stock value',
        bind: 'lines',
        width: 'medium',
        prefix() {
            return this.totalStockValueCurrency.value?.symbol ?? '';
        },
        scale() {
            return masterDataUtils.getScaleValue(2, this.totalStockValueCurrency.value?.decimalDigits);
        },
    })
    totalStockValue: ui.fields.Aggregate;

    @ui.decorators.referenceField<StockValuation, Currency>({
        parent() {
            return this.tileContainer;
        },
        title: 'Total stock value currency',
        node: '@sage/xtrem-master-data/Currency',
        valueField: 'symbol',
        helperTextField: 'id',
        columns: [ui.nestedFields.technical({ bind: 'symbol' }), ui.nestedFields.technical({ bind: 'decimalDigits' })],
        isTransient: true,
        isHidden: true, // Note: There is no technical field on that level.
    })
    totalStockValueCurrency: ui.fields.Reference<Currency>;

    @ui.decorators.tableField<StockValuation, StockValuationResultLine>({
        node: '@sage/xtrem-master-data/ItemSite',
        canResizeColumns: true,
        canSelect: false,
        isHelperTextHidden: true,
        bind: 'lines',
        isReadOnly: true,
        canExport: true,
        parent() {
            return this.mainSection;
        },
        columns: [
            ui.nestedFields.text({
                bind: { item: { name: true } },
                title: 'Item',
            }),
            ui.nestedFields.reference({
                bind: 'item',
                title: 'Item ID',
                node: '@sage/xtrem-master-data/Item',
                tunnelPage: '@sage/xtrem-master-data/Item',
                valueField: 'id',
                columns: [
                    ui.nestedFields.dropdownList({
                        bind: 'status',
                        title: 'Status',
                        optionType: '@sage/xtrem-master-data/ItemStatus',
                    }),
                ],
            }),
            ui.nestedFields.text({
                bind: { item: { description: true } },
                title: 'Item description',
                isHiddenOnMainField: true,
            }),
            ui.nestedFields.dropdownList({
                bind: { item: { status: true } },
                title: 'Status',
                optionType: '@sage/xtrem-master-data/ItemStatus',
            }),
            ui.nestedFields.text({
                bind: { site: { name: true } },
                title: 'Site',
                isHidden() {
                    return this.sites.value.length === 1;
                },
            }),
            ui.nestedFields.reference({
                bind: 'company',
                title: 'Company',
                node: '@sage/xtrem-system/Company',
                tunnelPage: '@sage/xtrem-master-data/Company',
                valueField: 'name',
            }),
            ui.nestedFields.text({
                bind: { postingClass: { name: true } },
                title: 'Posting class',
                isHidden() {
                    return !!this.postingClass.value;
                },
            }),
            ui.nestedFields.numeric({
                bind: 'quantity',
                title: 'Quantity',

                scale(_val, rowData) {
                    return StockValuationLib.stockUnitScale(rowData?.stockUnit);
                },
                postfix(_rowId, rowData) {
                    return StockValuationLib.stockUnitSymbol(rowData?.stockUnit);
                },
            }),
            ui.nestedFields.numeric({
                bind: 'stockValue',
                title: 'Stock value',
                scale(_val, rowData) {
                    return StockValuationLib.currencyScale(rowData?.currency);
                },
                postfix(_rowId, rowData) {
                    return StockValuationLib.currencySymbol(rowData?.currency);
                },
            }),
            ui.nestedFields.text({
                bind: { currency: { symbol: true } },
                title: 'Currency',
                isHiddenOnMainField: true,
            }),
            ui.nestedFields.numeric({
                bind: 'unitCost',
                title: 'Unit cost',

                scale(_val, rowData) {
                    return StockValuationLib.stockUnitScale(rowData?.stockUnit);
                },
                postfix(_rowId, rowData) {
                    return StockValuationLib.stockUnitSymbol(rowData?.stockUnit);
                },
            }),
            ui.nestedFields.reference({
                bind: 'stockUnit',
                title: 'Stock unit',
                node: '@sage/xtrem-master-data/UnitOfMeasure',
                tunnelPage: '@sage/xtrem-master-data/UnitOfMeasure',
                valueField: 'symbol',
                columns: [
                    ui.nestedFields.text({ bind: 'id', isHidden: true }),
                    ui.nestedFields.text({ title: 'Symbol', bind: 'symbol' }),
                ],
                isHiddenOnMainField: true,
            }),
            ui.nestedFields.date({
                bind: 'valuationDate',
                title: 'Valuation date',
                isHiddenOnMainField: true,
            }),
            ui.nestedFields.dropdownList({
                bind: 'costType',
                title: 'Cost type',
                optionType: '@sage/xtrem-master-data/costValuationMethod',
                isHidden() {
                    return !!this.valuationMethod.value;
                },
            }),
            ui.nestedFields.text({
                bind: { item: { category: { id: true } } },
                title: 'Item category',
                isHiddenOnMainField: true,
                isHidden() {
                    return !!this.itemCategory.value;
                },
            }),
            ui.nestedFields.text({
                bind: 'commodityCode',
                title: 'Commodity code',
                isHiddenOnMainField: true,
                isHidden() {
                    return !!this.commodityCode.value;
                },
            }),
        ],
    })
    itemSites: ui.fields.Table<StockValuationResultLine>;
}
