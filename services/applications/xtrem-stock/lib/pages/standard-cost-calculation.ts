import type { ExtractEdgesPartial } from '@sage/xtrem-client';
import { withoutEdges } from '@sage/xtrem-client';
import { DateValue } from '@sage/xtrem-date-time';
import * as dimensionPanelHelpers from '@sage/xtrem-finance-data/build/lib/client-functions/dimension-panel-helpers';
import type * as financeInterfaces from '@sage/xtrem-finance-data/build/lib/client-functions/interfaces';
import type { BusinessEntity, CostCategory, Currency, Item, ItemCategory } from '@sage/xtrem-master-data-api';
import { confirmDialogWithAcceptButtonText } from '@sage/xtrem-master-data/build/lib/client-functions/page-functions';
import { applyPanelToLineIfChanged, formatError } from '@sage/xtrem-master-data/build/lib/client-functions/utils';
import type {
    CostRollUpInputSet,
    CostRollUpResultLine,
    GraphApi,
    StandardCostRollUpStatus,
} from '@sage/xtrem-stock-api';
import { StockValuationMenu } from '@sage/xtrem-stock-data/build/lib/menu-items/stock-valuation-menu';
import type { Legislation } from '@sage/xtrem-structure-api';
import type { Company, Site, User } from '@sage/xtrem-system-api';
import * as ui from '@sage/xtrem-ui';
import type * as StockInterfaces from '../client-functions/interfaces';
import * as PillColorStock from '../client-functions/pill-color';

@ui.decorators.page<StandardCostCalculation, CostRollUpInputSet>({
    menuItem: StockValuationMenu,
    priority: 60,
    title: 'Standard cost calculation',
    objectTypeSingular: 'Standard cost calculation',
    objectTypePlural: 'Standard cost calculations',
    module: 'stock',
    mode: 'default',
    node: '@sage/xtrem-stock/CostRollUpInputSet',
    navigationPanel: undefined,
    headerLabel() {
        return this.status;
    },
    headerSection() {
        return this.mainSection;
    },
    createAction() {
        return this.$standardNewAction;
    },
    businessActions() {
        return [this.runCreateItemSiteCostsFromRollUpResults, this.runStandardCostCalculation];
    },
    async onError(error: string | (Error & { errors: Array<any> })) {
        this.$.loader.isHidden = true;
        // Refresh to get the latest status from the server.
        await this.status.refresh();
        return formatError(this, error);
    },
    async onLoad() {
        this.inputSetId = this.$.recordId;
        let userEmail = this.$.username;
        // this make sure the user field not is empty in dev mode when the default user is used
        // so cucumber tests run properly without SageId
        if (userEmail === 'admin') {
            userEmail = '<EMAIL>';
        }

        if (!this.inputSetId) {
            const records = withoutEdges(
                await this.$.graph
                    .node('@sage/xtrem-stock/CostRollUpInputSet')
                    .query(ui.queryUtils.edgesSelector({ _id: true }, { filter: { user: `#${userEmail}` } }))
                    .execute(),
            );

            if (records.length > 0) {
                this.$.router.goTo('@sage/xtrem-stock/StandardCostCalculation', { _id: `${records[0]._id}` });
                // we need to stop the function here as we will still be executing while the page is filled with the record selected.
                return;
            }
            this.user.value = withoutEdges(
                await this.$.graph
                    .node('@sage/xtrem-system/User')
                    .query(
                        ui.queryUtils.edgesSelector<User>(
                            { _id: true, displayName: true, firstName: true, lastName: true, email: true },
                            { filter: { email: `${userEmail}` }, first: 1 },
                        ),
                    )
                    .execute(),
            ).at(0) as unknown as ExtractEdgesPartial<User>;
        }
        this._defaultDimensionsAttributes = dimensionPanelHelpers.initDefaultDimensions();
        this.quantity.scale = 2;
        this.quantity.postfix = '';
        this.fromDate.value = this.fromDate.value ?? DateValue.today().toString();
    },
})
export class StandardCostCalculation extends ui.Page<GraphApi> {
    _defaultDimensionsAttributes: financeInterfaces.DefaultDimensions;

    inputSetId: string | undefined;

    @ui.decorators.section<StandardCostCalculation>({
        isTitleHidden: true,
        title: 'General',
    })
    mainSection: ui.containers.Section;

    @ui.decorators.section<StandardCostCalculation>({
        isTitleHidden: true,
        title: 'Result lines',
    })
    linesSection: ui.containers.Section;

    @ui.decorators.block<StandardCostCalculation>({
        parent() {
            return this.mainSection;
        },
        title: 'Criteria',
        isTitleHidden: true,
    })
    criteriaBlock: ui.containers.Block;

    @ui.decorators.referenceField<StandardCostCalculation, User>({
        parent() {
            return this.criteriaBlock;
        },
        title: 'Calculation user',
        bind: 'user',
        valueField: 'displayName',
        node: '@sage/xtrem-system/User',
        minLookupCharacters: 1,
        lookupDialogTitle: 'Select user',
        isReadOnly: true,
        columns: [
            ui.nestedFields.text({ bind: 'firstName', title: 'First Name' }),
            ui.nestedFields.text({ bind: 'lastName', title: 'Last name' }),
            ui.nestedFields.text({ bind: 'email', title: 'Email' }),
        ],
    })
    user: ui.fields.Reference<User>;

    @ui.decorators.referenceField<StandardCostCalculation, Site>({
        parent() {
            return this.criteriaBlock;
        },
        title: 'Site',
        lookupDialogTitle: 'Select site',
        node: '@sage/xtrem-system/Site',
        valueField: 'name',
        helperTextField: 'id',
        isMandatory: true,
        minLookupCharacters: 0,
        fetchesDefaults: true,
        columns: [
            ui.nestedFields.text({ title: 'Name', bind: 'name' }),
            ui.nestedFields.technical<StandardCostCalculation, Site, BusinessEntity>({
                bind: 'businessEntity',
                node: '@sage/xtrem-master-data/BusinessEntity',
                nestedFields: [
                    ui.nestedFields.technical<StandardCostCalculation, BusinessEntity, Currency>({
                        node: '@sage/xtrem-master-data/Currency',
                        bind: 'currency',
                        nestedFields: [
                            ui.nestedFields.technical({ bind: 'name' }),
                            ui.nestedFields.technical({ bind: 'id' }),
                            ui.nestedFields.technical({ bind: 'symbol' }),
                            ui.nestedFields.technical({ bind: 'decimalDigits' }),
                        ],
                    }),
                ],
            }),
            ui.nestedFields.text({ title: 'ID ', bind: 'id' }),
            ui.nestedFields.technical({ bind: 'isPurchase' }),
            ui.nestedFields.technical({ bind: 'isInventory' }),
            ui.nestedFields.reference<StandardCostCalculation, Site, Company>({
                title: 'Company',
                valueField: 'name',
                bind: 'legalCompany',
                node: '@sage/xtrem-system/Company',
                columns: [
                    ui.nestedFields.text({ bind: 'name' }),
                    ui.nestedFields.text({ bind: 'id' }),
                    ui.nestedFields.technical<StandardCostCalculation, Company, Legislation>({
                        node: '@sage/xtrem-structure/Legislation',
                        bind: 'legislation',
                        nestedFields: [
                            ui.nestedFields.technical({ bind: 'name' }),
                            ui.nestedFields.technical({ bind: 'id' }),
                        ],
                    }),
                    ui.nestedFields.technical({ bind: 'taxEngine' }),
                    ui.nestedFields.reference<StandardCostCalculation, Company, Currency>({
                        node: '@sage/xtrem-master-data/Currency',
                        tunnelPage: '@sage/xtrem-master-data/Currency',
                        title: 'Currency',
                        bind: 'currency',
                        valueField: 'name',
                        columns: [
                            ui.nestedFields.technical({ bind: 'name' }),
                            ui.nestedFields.technical({ bind: 'id' }),
                            ui.nestedFields.technical({ bind: 'symbol' }),
                            ui.nestedFields.technical({ bind: 'decimalDigits' }),
                        ],
                    }),
                    ui.nestedFields.technical({ bind: 'priceScale' }),
                ],
            }),
            ui.nestedFields.technical<StandardCostCalculation, Site, BusinessEntity>({
                bind: 'businessEntity',
                node: '@sage/xtrem-master-data/BusinessEntity',
                nestedFields: [ui.nestedFields.technical({ bind: 'id' }), ui.nestedFields.technical({ bind: '_id' })],
            }),
        ],
        async onChange() {
            this.items.value = [];
            await this.resetStatusIfNeeded();
        },
    })
    site: ui.fields.Reference<Site>;

    @ui.decorators.multiReferenceField<StandardCostCalculation, Item>({
        title: 'Items',
        node: '@sage/xtrem-master-data/Item',
        lookupDialogTitle: 'Select item',
        minLookupCharacters: 0,
        width: 'medium',
        valueField: 'name',
        helperTextField: 'id',
        columns: [
            ui.nestedFields.label({ title: 'ID', bind: 'id', isTitleHidden: true }),
            ui.nestedFields.label({ title: 'Name', bind: 'name', isTitleHidden: true }),
        ],
        parent() {
            return this.criteriaBlock;
        },
        filter() {
            const site = this.site.value;
            return {
                ...(site && site._id
                    ? { itemSites: { _atLeast: 1, valuationMethod: 'standardCost', site: { _id: site._id } } }
                    : {}),
            };
        },
        async onChange() {
            await this.resetStatusIfNeeded();
        },
    })
    items: ui.fields.MultiReference<Item>;

    @ui.decorators.referenceField<StandardCostCalculation, ItemCategory>({
        title: 'Item category',
        node: '@sage/xtrem-master-data/ItemCategory',
        columns: [
            ui.nestedFields.text({ bind: 'name' }),
            ui.nestedFields.text({ bind: 'id' }),
            ui.nestedFields.dropdownList({ bind: 'type', title: 'Type' }),
        ],
        valueField: 'name',
        helperTextField: 'id',
        minLookupCharacters: 1,
        lookupDialogTitle: 'Select item category',
        parent() {
            return this.criteriaBlock;
        },
        async onChange() {
            await this.resetStatusIfNeeded();
        },
    })
    itemCategory: ui.fields.Reference<ItemCategory>;

    @ui.decorators.textField<StandardCostCalculation>({
        title: 'Commodity code',
        parent() {
            return this.criteriaBlock;
        },
        async onChange() {
            await this.resetStatusIfNeeded();
        },
    })
    commodityCode: ui.fields.Text;

    @ui.decorators.referenceField<StandardCostCalculation, CostCategory>({
        isReadOnly: true,
        title: 'Cost category',
        node: '@sage/xtrem-master-data/CostCategory',
        valueField: 'name',
        helperTextField: 'id',
        minLookupCharacters: 0,
        lookupDialogTitle: 'Select cost category',
        columns: [
            ui.nestedFields.text({ bind: 'name' }),
            ui.nestedFields.text({ bind: 'id' }),
            ui.nestedFields.dropdownList({
                bind: 'costCategoryType',
                optionType: '@sage/xtrem-master-data/CostCategoryType',
                title: 'Type',
            }),
            ui.nestedFields.checkbox({ title: 'Mandatory', bind: 'isMandatory' }),
        ],
        parent() {
            return this.criteriaBlock;
        },
        filter: { costCategoryType: { _eq: 'standard' } },
        async onChange() {
            await this.resetStatusIfNeeded();
        },
    })
    costCategory: ui.fields.Reference<CostCategory>;

    @ui.decorators.dateField<StandardCostCalculation>({
        parent() {
            return this.criteriaBlock;
        },
        title: 'Start date',
        width: 'small',
        minDate: DateValue.today().toString(),
    })
    fromDate: ui.fields.Date;

    @ui.decorators.numericField<StandardCostCalculation>({
        title: 'Quantity',
        isMandatory: true,
        isNotZero: true,
        parent() {
            return this.criteriaBlock;
        },
        async onChange() {
            await this.resetStatusIfNeeded();
        },
    })
    quantity: ui.fields.Numeric;

    @ui.decorators.checkboxField<StandardCostCalculation>({
        title: 'Include routing',
        parent() {
            return this.criteriaBlock;
        },
        async onChange() {
            await this.resetStatusIfNeeded();
        },
    })
    includesRouting: ui.fields.Checkbox;

    @ui.decorators.checkboxField<StandardCostCalculation>({
        title: 'Component standard cost',
        parent() {
            return this.criteriaBlock;
        },
        async onChange() {
            await this.resetStatusIfNeeded();
        },
    })
    usesComponentStandardCost: ui.fields.Checkbox;

    @ui.decorators.labelField<StandardCostCalculation>({
        title: 'Status',
        bind: 'status',
        optionType: '@sage/xtrem-stock/StandardCostRollUpStatus',
        width: 'small',
        style() {
            return PillColorStock.getLabelColorByStatus('StandardCostRollUpStatus', this.status.value);
        },
    })
    status: ui.fields.Label<StandardCostRollUpStatus>;

    @ui.decorators.checkboxField<StandardCostCalculation>({
        parent() {
            return this.criteriaBlock;
        },
        title: 'Select all result lines',
        isTransient: true,
        isDisabled() {
            return this.status.value !== 'completed';
        },
        onChange() {
            if (this.isAllSelected.value) {
                this.resultLines.value.forEach(value => this.resultLines.selectRecord(value._id));
                this.runCreateItemSiteCostsFromRollUpResults.isHidden = false;
            } else {
                this.resultLines.unselectAllRecords();
                this.runCreateItemSiteCostsFromRollUpResults.isHidden = true;
            }
            this.$.setPageClean();
        },
    })
    isAllSelected: ui.fields.Checkbox;

    @ui.decorators.tableField<StandardCostCalculation, CostRollUpResultLine>({
        node: '@sage/xtrem-stock/CostRollUpResultLine',
        canResizeColumns: true,
        isHelperTextHidden: true,
        bind: 'resultLines',
        isReadOnly: true,
        canExport: true,
        parent() {
            return this.linesSection;
        },
        onRowSelected() {
            this.runCreateItemSiteCostsFromRollUpResults.isHidden = false;
        },
        onRowUnselected() {
            this.runCreateItemSiteCostsFromRollUpResults.isHidden = this.resultLines.selectedRecords.length <= 0;
            this.isAllSelected.value = false;
        },
        columns: [
            ui.nestedFields.label({
                title: 'Cost creation status',
                bind: 'creationStatus',
                optionType: '@sage/xtrem-stock/StandardCostRollUpResultLineStatus',
                style: (_id, rowData) =>
                    PillColorStock.getLabelColorByStatus('StandardCostRollUpResultLineStatus', rowData.creationStatus),
            }),
            ui.nestedFields.reference({
                bind: 'item',
                title: 'Item',
                node: '@sage/xtrem-master-data/Item',
                valueField: 'name',
            }),
            ui.nestedFields.reference({
                bind: 'item',
                title: 'Item ID',
                node: '@sage/xtrem-master-data/Item',
                valueField: 'id',
            }),
            ui.nestedFields.text({
                bind: { item: { description: true } },
                title: 'Item description',
                isHiddenOnMainField: true,
            }),
            ui.nestedFields.numeric({
                bind: 'currentMaterialCost',
                title: 'Current material cost',
                size: 'small',
                prefix() {
                    return this.currencySymbol();
                },
                scale() {
                    return StandardCostCalculation.costScale();
                },
            }),
            ui.nestedFields.numeric({
                bind: 'materialCost',
                title: 'Material cost',
                size: 'small',
                prefix() {
                    return this.currencySymbol();
                },
                scale() {
                    return StandardCostCalculation.costScale();
                },
            }),
            ui.nestedFields.numeric({
                bind: 'currentMachineCost',
                title: 'Current machine cost',
                size: 'small',
                prefix() {
                    return this.currencySymbol();
                },
                scale() {
                    return StandardCostCalculation.costScale();
                },
            }),
            ui.nestedFields.numeric({
                bind: 'machineCost',
                title: 'Machine cost',
                size: 'small',
                prefix() {
                    return this.currencySymbol();
                },
                scale() {
                    return StandardCostCalculation.costScale();
                },
            }),
            ui.nestedFields.numeric({
                bind: 'currentLaborCost',
                title: 'Current labor cost',
                size: 'small',
                prefix() {
                    return this.currencySymbol();
                },
                scale() {
                    return StandardCostCalculation.costScale();
                },
            }),
            ui.nestedFields.numeric({
                bind: 'laborCost',
                title: 'Labor cost',
                size: 'small',
                prefix() {
                    return this.currencySymbol();
                },
                scale() {
                    return StandardCostCalculation.costScale();
                },
            }),
            ui.nestedFields.numeric({
                bind: 'currentToolCost',
                title: 'Current tool cost',
                size: 'small',
                prefix() {
                    return this.currencySymbol();
                },
                scale() {
                    return StandardCostCalculation.costScale();
                },
            }),
            ui.nestedFields.numeric({
                bind: 'toolCost',
                title: 'Tool cost',
                size: 'small',
                prefix() {
                    return this.currencySymbol();
                },
                scale() {
                    return StandardCostCalculation.costScale();
                },
            }),
            ui.nestedFields.numeric({
                bind: 'currentTotalCost',
                title: 'Current total cost',
                size: 'small',
                prefix() {
                    return this.currencySymbol();
                },
                scale() {
                    return StandardCostCalculation.costScale();
                },
            }),
            ui.nestedFields.numeric({
                bind: 'totalCost',
                title: 'Total cost',
                size: 'small',
                prefix() {
                    return this.currencySymbol();
                },
                scale() {
                    return StandardCostCalculation.costScale();
                },
            }),
            ui.nestedFields.text({
                bind: 'creationErrorMessage',
                title: 'Error message',
                size: 'small',
            }),
            ui.nestedFields.technical({ bind: 'storedAttributes' }),
            ui.nestedFields.technical({ bind: 'storedDimensions' }),
        ],
        dropdownActions: [
            {
                title: 'Subassemblies',
                async onClick(_rowId, rowItem: ui.PartialCollectionValue<CostRollUpResultLine>) {
                    const args: StockInterfaces.CostRollUpSubAssemblyDialogParameters = {
                        resultLineId: rowItem._id ?? '',
                        resultLineItemName: rowItem.item?.name ?? '',
                        currencySymbol: this.currencySymbol(),
                        costScale: StandardCostCalculation.costScale(),
                    };
                    await this.$.dialog.page(
                        '@sage/xtrem-stock/CostRollUpSubAssemblyDialog',
                        { args: JSON.stringify(args) },
                        { rightAligned: false, size: 'extra-large', resolveOnCancel: true },
                    );
                },
            },
            {
                icon: 'edit',
                title: 'Dimensions',
                async onClick(_rowId, rowItem: ui.PartialCollectionValue<CostRollUpResultLine>) {
                    const rowData = dimensionPanelHelpers.fromPartialToBaseDocumentLineWithAnalytical(rowItem);
                    const newRowData = await applyPanelToLineIfChanged(
                        this.resultLines,
                        dimensionPanelHelpers.editDisplayDimensions(
                            this,
                            { documentLine: rowData },
                            {
                                // TODO: Check the status of the line too
                                editable: StandardCostCalculation.isLineSelectable(
                                    rowItem as ui.PartialNodeWithId<CostRollUpResultLine>,
                                ),
                            },
                        ),
                    );
                    if (newRowData) {
                        this.$.loader.isHidden = false;
                        await this.$standardSaveAction.execute();
                        this.$.loader.isHidden = true;
                    }
                },
            },
        ],
        fieldActions() {
            return [this.defaultDimension];
        },
    })
    resultLines: ui.fields.Table<CostRollUpResultLine>;

    @ui.decorators.pageAction<StandardCostCalculation>({
        icon: 'arrow_right',
        title: 'Set default dimensions',
        isDisabled() {
            // TODO: Check if this condition is enough
            return this.status.value !== 'completed' || this.$.isDirty || !this.resultLines.value.length;
        },
        async onClick() {
            if (!this.$.recordId) return;
            const newDefaults = (await new Promise((resolve, reject) => {
                this.$.dialog
                    .page(
                        '@sage/xtrem-finance-data/DimensionPanel',
                        {
                            enteredDimensions: JSON.stringify({
                                ...this._defaultDimensionsAttributes,
                            }),
                            disabledAction: JSON.stringify({}),
                            editable: true,
                            isDefaultDimensionPage: JSON.stringify(false),
                            isDimensionPageToInherit: JSON.stringify(false),
                            calledFromMainGrid: JSON.stringify(true),
                            defaultedFromItem: '{}',
                        },
                        { size: 'large' },
                    )
                    .then((resolveData: financeInterfaces.DefaultDimensions) => {
                        resolve(resolveData);
                    })
                    .catch(() => {
                        reject();
                    });
            })) as financeInterfaces.DefaultDimensions;
            if (newDefaults) {
                this.$.loader.isHidden = false;

                this._defaultDimensionsAttributes = { ...newDefaults };
                if (
                    await this.$.graph
                        .node('@sage/xtrem-stock/CostRollUpInputSet')
                        .mutations.updateAttributesAndDimensions(true, {
                            costRollUpInputSet: this.$.recordId,
                            storedDimensions: newDefaults.dimensions,
                            storedAttributes: newDefaults.attributes,
                        })
                        .execute()
                ) {
                    this.$.showToast(
                        ui.localize(
                            '@sage/xtrem-stock/pages__standard_cost_calculation__dimensions_updated',
                            'Dimensions updated',
                        ),
                    );
                    await this.resultLines.refresh();
                }
                this.$.loader.isHidden = true;
            }
        },
    })
    defaultDimension: ui.PageAction;

    getSerializedValues() {
        const pageValuesObj = this.$.values;
        if (this.inputSetId) {
            pageValuesObj._id = this.inputSetId;
        }

        return pageValuesObj;
    }

    @ui.decorators.pageAction<StandardCostCalculation>({
        title: 'Calculate',
        buttonType: 'primary',
        isDisabled() {
            return this.status.value === 'draft' && !this.$.isDirty;
        },
        isHidden() {
            return this.status.value === 'inProgress';
        },
        async onClick() {
            const validation = await this.$.page.validate();

            if (validation.length) {
                this.$.showToast(validation.join('\n'), { type: 'error' });
            } else {
                await this.performRollUpCalculation();
            }
        },
    })
    runStandardCostCalculation: ui.PageAction;

    @ui.decorators.pageAction<StandardCostCalculation>({
        title: 'Create item-site cost',
        isHidden: true,
        async onClick() {
            this.runStandardCostCalculation.isDisabled = true;
            this.runCreateItemSiteCostsFromRollUpResults.isDisabled = true;
            if (
                (this.resultLines.selectedRecords.length > 0 || this.isAllSelected.value) &&
                this.$.recordId &&
                this.status.value === 'completed'
            ) {
                const validation = await this.$.page.validate();
                if (validation.length) {
                    this.$.showToast(validation.join('\n'), { type: 'error' });
                } else if (this.$.isDirty) {
                    this.$.showToast(
                        ui.localize(
                            '@sage/xtrem-stock/pages__standard_cost_calculation__request_notification_item_site_cost_creation_impossible',
                            'You need to run the calculation first.',
                        ),
                        { type: 'error' },
                    );
                    this.resultLines.selectedRecords = [];
                } else {
                    await this.performCreateItemSiteCosts();
                }
                this.runStandardCostCalculation.isDisabled = false;
                this.runCreateItemSiteCostsFromRollUpResults.isDisabled = false;
                await this.resultLines.refresh();
                this.isAllSelected.value = false;
            }
        },
    })
    runCreateItemSiteCostsFromRollUpResults: ui.PageAction;

    async performRollUpCalculation() {
        // Request calculation in an asynchronous mutation.
        const waitrequest = await confirmDialogWithAcceptButtonText(
            this,
            ui.localize(
                '@sage/xtrem-stock/pages__standard_cost_calculation__title_wait_for_finish',
                'Wait for the calculation to complete',
            ),
            ui.localize(
                '@sage/xtrem-stock/pages__standard_cost_calculation__calculation_dialog_content',
                "This calculation could take a long time to complete. Do you want to wait for the calculation to finish or navigate away from this page and be notified when it's complete?",
            ),
            ui.localize('@sage/xtrem-stock/pages__standard_cost_calculation__button_wait', 'Wait'),
            ui.localize('@sage/xtrem-stock/pages__standard_cost_calculation__button_be_notified', 'Notify'),
        );

        this.status.value = 'inProgress';

        this.$.loader.isHidden = false;

        // This should prevent bugs on first creation of the input set when using the standard save action
        // if the input set doesn't already exists there might be an race condition
        // wether the page's record id get updated first or the code carries on first...
        if (!this.inputSetId) {
            const createResult = await this.$.graph
                .node('@sage/xtrem-stock/CostRollUpInputSet')
                .create({ _id: true }, { data: this.getSerializedValues() })
                .execute();

            if (createResult?._id) {
                this.inputSetId = createResult._id;
            } else {
                throw new Error('test');
            }
        } else {
            // Before we can do the calculation asynchronously we must save the current record.
            await this.$.graph
                .node('@sage/xtrem-stock/CostRollUpInputSet')
                .update({ _id: true }, { data: this.getSerializedValues() })
                .execute();
        }
        this.$.loader.isHidden = true;

        if (waitrequest) {
            this.$.loader.isHidden = false;
            const calculationResult = await this.$.graph
                .node('@sage/xtrem-master-data/ItemSiteCost')
                .asyncOperations.standardCostRollUpCalculation.runToCompletion(true, {
                    inputSet: this.inputSetId ?? '',
                })
                .execute();
            this.$.loader.isHidden = true;
            if (calculationResult) {
                // Signal success.
                this.$.showToast(
                    ui.localize(
                        '@sage/xtrem-stock/pages__standard_cost_calculation_request__notification_valuation_finished',
                        'Standard cost calculation finished.',
                    ),
                    { type: 'success' },
                );

                // force re-enable select all button in case the onLoad on refresh doesn't manage it properly
                this.isAllSelected.isDisabled = false;
            } else {
                // Signal error.
                this.$.showToast(
                    ui.localize(
                        '@sage/xtrem-stock/pages__standard_cost_calculation_request__notification_valuation_failed',
                        'Standard cost calculation failed.',
                    ),
                    { type: 'error' },
                );
            }
        } else {
            await this.$.graph
                .node('@sage/xtrem-master-data/ItemSiteCost')
                .asyncOperations.standardCostRollUpCalculation.start(
                    { trackingId: true },
                    {
                        inputSet: this.inputSetId ?? '',
                    },
                )
                .execute();

            // Notify the user that a calculation request is on the way.
            this.$.showToast(
                ui.localize(
                    '@sage/xtrem-stock/pages__standard_cost_calculation_request__notification_request_sent',
                    'Standard cost calculation request sent.',
                ),
                { type: 'success' },
            );

            // Need to wait for the asynchronous update before refreshing the page.
            await new Promise<void>(resolve => {
                setTimeout(resolve, 1000);
            });
        }
        // using this instead of the refresh works for both creation and update of the input set
        this.$.router.goTo('@sage/xtrem-stock/StandardCostCalculation', { _id: `${this.inputSetId}` }, true);
    }

    async performCreateItemSiteCosts() {
        // Request calculation in an asynchronous mutation.
        const waitrequest = await confirmDialogWithAcceptButtonText(
            this,
            ui.localize(
                '@sage/xtrem-stock/pages__standard_cost_calculation__wait_for_creation',
                'Wait for the item-site cost records to be created',
            ),
            ui.localize(
                '@sage/xtrem-stock/pages__standard_cost_calculation__cost_creation_content',
                "Creating the item-site cost records could take a long time to complete. Do you want to wait while the records are created or navigate away from this page and be notified when they're done?",
            ),
            ui.localize('@sage/xtrem-stock/pages__standard_cost_calculation__button_wait', 'Wait'),
            ui.localize('@sage/xtrem-stock/pages__standard_cost_calculation__button_be_notified', 'Notify'),
        );

        const filter = this.isAllSelected.value
            ? '{}'
            : JSON.stringify({ _id: { _in: this.resultLines.selectedRecords } });

        if (waitrequest) {
            this.$.loader.isHidden = false;
            if (
                await this.$.graph
                    .node('@sage/xtrem-stock/CostRollUpResultLine')
                    .asyncOperations.createItemSiteCostsFromCostRollUpResults.runToCompletion(true, {
                        filter,
                    })
                    .execute()
            ) {
                this.$.showToast(
                    ui.localize(
                        '@sage/xtrem-stock/pages__standard_cost_calculation__request_notification_item_site_cost_creation_finished',
                        'Item-site cost creation finished.',
                    ),
                    { type: 'success' },
                );
                this.resultLines.selectedRecords = [];
            }
            this.$.loader.isHidden = true;
        } else {
            await this.$.graph
                .node('@sage/xtrem-stock/CostRollUpResultLine')
                .asyncOperations.createItemSiteCostsFromCostRollUpResults.start(
                    { trackingId: true },
                    {
                        filter,
                    },
                )
                .execute();

            this.$.showToast(
                ui.localize(
                    '@sage/xtrem-stock/pages__standard_cost_calculation__request_notification_item_site_cost_creation_request_sent',
                    'Item-site cost creation request sent.',
                ),
                { type: 'success' },
            );
        }
    }

    static costScale(): number {
        return 4;
    }

    currencySymbol(): string {
        return this.site.value?.legalCompany?.currency?.symbol || '';
    }

    static isLineSelectable(line: ui.PartialNodeWithId<CostRollUpResultLine>) {
        return ['created', 'updated'].includes(line.creationStatus ?? '');
    }

    async resetStatusIfNeeded() {
        if (['completed', 'error'].includes(this.status.value ?? '')) {
            this.status.value = 'draft';
            await this.$.commitValueAndPropertyChanges();
        }
    }
}
