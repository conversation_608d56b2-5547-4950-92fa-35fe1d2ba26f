import type { Graph<PERSON><PERSON> } from '@sage/xtrem-stock-api';
import type { StockAllocation } from '@sage/xtrem-stock-data-api';
import * as DocumentLink from '@sage/xtrem-stock-data/build/lib/client-functions/document-link';
import * as ui from '@sage/xtrem-ui';

@ui.decorators.page<StockAllocationInquiry, StockAllocation>({
    // For now we hide the page in the menu until it is further developed into a full-featured one.
    // menuItem: StockInquiries,
    priority: 157,
    title: 'Stock allocation inquiry',
    module: 'stock',
    mode: 'default',
    node: '@sage/xtrem-stock-data/StockAllocation',
    access: { node: '@sage/xtrem-stock-data/StockAllocation', bind: '$read' },
    navigationPanel: {
        onSelect() {
            return true;
        },
        listItem: {
            documentId: ui.nestedFields.technical({ bind: { documentLine: { documentId: true } } }), // Needed for the documentLink field.
            documentConstructor: ui.nestedFields.technical({ bind: { documentLine: { _constructor: true } } }), // Needed for the documentLink field.
            title: ui.nestedFields.link({
                title: 'Document number',
                bind: { documentLine: { documentNumber: true } },
                onClick(_value, rowData) {
                    return this.$.dialog.page(
                        DocumentLink.getDocumentPageName(rowData?.documentLine?._constructor || ''),
                        { _id: rowData?.documentLine?.documentId || '' },
                        { fullScreen: true, resolveOnCancel: true },
                    );
                },
            }),
            quantityInStockUnit: ui.nestedFields.text({
                title: 'Allocated quantity',
                bind: 'quantityInStockUnit',
                isReadOnly: true,
            }),
        },
        orderBy: { _id: -1 },
    },
})
export class StockAllocationInquiry extends ui.Page<GraphApi> {}
