import type { Filter } from '@sage/xtrem-client';
import { DateValue } from '@sage/xtrem-date-time';
import type { Item, ItemCategory, ItemSite, Location, LocationZone, UnitOfMeasure } from '@sage/xtrem-master-data-api';
import { setReferenceIfSingleValue } from '@sage/xtrem-master-data/build/lib/client-functions/get-default';
import * as MasterDataUtils from '@sage/xtrem-master-data/build/lib/client-functions/utils';
import type { GraphApi } from '@sage/xtrem-stock-api';
import type { Lot, Stock, StockStatus } from '@sage/xtrem-stock-data-api';
import { StockCountMenu } from '@sage/xtrem-stock-data/build/lib/menu-items/stock-count-menu';
import type { Site } from '@sage/xtrem-system-api';
import { setApplicativePageCrudActions } from '@sage/xtrem-system/build/lib/client-functions/applicative-crud-actions';
import * as ui from '@sage/xtrem-ui';

@ui.decorators.page<StockCountCreation>({
    title: 'Stock count creation',
    mode: 'default',
    node: '@sage/xtrem-stock/StockCount',
    menuItem: StockCountMenu,
    priority: 100,
    module: 'stock',
    createAction() {
        return this.$standardNewAction;
    },
    businessActions() {
        return [this.$standardCancelAction, this.$standardSaveAction, this.createStockCount];
    },
    navigationPanel: {
        listItem: {
            title: ui.nestedFields.text({ bind: 'number' }),
            line2: ui.nestedFields.reference<StockCountCreation, Site>({
                title: 'Site',
                bind: 'stockSite',
                node: '@sage/xtrem-system/Site',
                valueField: 'name',
                columns: [],
                tunnelPage: undefined,
            }),
        },
        optionsMenu: [{ title: 'Draft', graphQLFilter: { status: { _eq: 'draft' } } }],
    },
    async onLoad() {
        this.effectiveDate.value = DateValue.today().toString();
        await setReferenceIfSingleValue([this.stockSite]);
        this.changeCriteriaVisibility();
        if (!this.$.recordId) this.hasStockRecords.value = true;

        this.$.setPageClean();

        setApplicativePageCrudActions({
            page: this,
            isDirty: false,
            save: this.$standardSaveAction,
            cancel: this.$standardCancelAction,
        });
    },
    onDirtyStateUpdated(isDirty: boolean) {
        if (isDirty) {
            // TODO: when XT-85743 is fixed, the selectAllCheckbox should benefit from the fix
            // and then the following lines can be activated
            //     // if something is modified, even the description, the user has to save or cancel
            //     // Then it doesn't make sense to be able to select as it will be lost
            //     this.resetItemSitesSelection();
        } else {
            this.activateItemSitesSelection();
        }
        setApplicativePageCrudActions({
            page: this,
            isDirty,
            save: this.$standardSaveAction,
            cancel: this.$standardCancelAction,
        });
    },
})
export class StockCountCreation extends ui.Page<GraphApi> {
    @ui.decorators.section<StockCountCreation>({
        isOpen: true,
        isTitleHidden: true,
        title: 'General',
    })
    mainSection: ui.containers.Section;

    @ui.decorators.section<StockCountCreation>({
        isTitleHidden: true,
    })
    itemSiteSection: ui.containers.Section;

    @ui.decorators.block<StockCountCreation>({
        parent() {
            return this.mainSection;
        },
        width: 'extra-large',
        title: 'Selection criteria',
    })
    criteriaBlock: ui.containers.Block;

    @ui.decorators.block<StockCountCreation>({
        parent() {
            return this.mainSection;
        },
        width: 'extra-large',
        isTitleHidden: true,
    })
    stockCriteriaBlock: ui.containers.Block;

    @ui.decorators.referenceField<StockCountCreation, Site>({
        parent() {
            return this.criteriaBlock;
        },
        title: 'Site',
        lookupDialogTitle: 'Select site',
        node: '@sage/xtrem-system/Site',
        valueField: 'name',
        helperTextField: 'id',
        minLookupCharacters: 0,
        isMandatory: true,
        columns: [
            ui.nestedFields.text({ bind: 'name' }),
            ui.nestedFields.text({ bind: 'id' }),
            ui.nestedFields.technical({ bind: 'isLocationManaged' }),
        ],
        placeholder: 'Select...',
        width: 'small',
        onChange() {
            this.resetItemSitesSelection(); // see onDirtyStateUpdated. When XT-85743 is fixed this line should be removed
            this.fromItem.value = null;
            this.toItem.value = null;
            this.categories.value = [];
            this.locations.value = [];
            this.zones.value = [];

            this.fromItem.isDisabled = !this.stockSite.value;
            this.toItem.isDisabled = !this.stockSite.value;
            this.categories.isDisabled = !this.stockSite.value;
            this.locations.isDisabled = !this.stockSite.value;
            this.zones.isDisabled = !this.stockSite.value;
        },
        filter() {
            return {
                isInventory: true,
            };
        },
    })
    stockSite: ui.fields.Reference<Site>;

    @ui.decorators.referenceField<StockCountCreation, Item>({
        parent() {
            return this.criteriaBlock;
        },
        title: 'From item',
        shouldSuggestionsIncludeColumns: true,
        node: '@sage/xtrem-master-data/Item',
        tunnelPage: '@sage/xtrem-master-data/Item',
        lookupDialogTitle: 'Select item',
        valueField: 'name',
        helperTextField: 'id',
        columns: [
            ui.nestedFields.text({ title: 'Name', bind: 'name' }),
            ui.nestedFields.text({ title: 'ID', bind: 'id' }),
            ui.nestedFields.text({ title: 'Description', bind: 'description' }),
            ui.nestedFields.technical({ bind: 'isStockManaged' }),
            ui.nestedFields.technical({ bind: 'serialNumberManagement' }),
        ],
        orderBy: {
            name: +1,
            id: +1,
        },
        placeholder: 'Select...',
        width: 'small',
        validation() {
            return this.checkItemRange() ?? '';
        },
        onChange() {
            this.resetItemSitesSelection(); // see onDirtyStateUpdated. When XT-85743 is fixed this line should be removed
        },
    })
    fromItem: ui.fields.Reference<Item>;

    @ui.decorators.referenceField<StockCountCreation, Item>({
        parent() {
            return this.criteriaBlock;
        },
        title: 'To item',
        shouldSuggestionsIncludeColumns: true,
        node: '@sage/xtrem-master-data/Item',
        tunnelPage: '@sage/xtrem-master-data/Item',
        lookupDialogTitle: 'Select item',
        valueField: 'name',
        helperTextField: 'id',
        columns: [
            ui.nestedFields.text({ title: 'Name', bind: 'name' }),
            ui.nestedFields.text({ title: 'ID', bind: 'id' }),
            ui.nestedFields.text({ title: 'Description', bind: 'description' }),
            ui.nestedFields.technical({ bind: 'isStockManaged' }),
            ui.nestedFields.technical({ bind: 'serialNumberManagement' }),
        ],
        orderBy: {
            name: +1,
            id: +1,
        },
        placeholder: 'Select...',
        width: 'small',
        validation() {
            return this.checkItemRange() ?? '';
        },
        onChange() {
            this.resetItemSitesSelection(); // see onDirtyStateUpdated. When XT-85743 is fixed this line should be removed
        },
    })
    toItem: ui.fields.Reference<Item>;

    stockFilter(): Filter<Stock> {
        const _and: Filter<Stock>[] = [];

        if (this.locations.value.length > 0) {
            _and.push({ location: { _id: { _in: this.locations.value.map(location => location._id ?? '') } } });
        }

        if (this.zones.value.length > 0) {
            _and.push({ location: { locationZone: { _id: { _in: this.zones.value.map(zone => zone._id ?? '') } } } });
        }

        return _and.length > 0 ? { _and } : {};
    }

    locationFilter(): Filter<Location> {
        const _and: Filter<Location>[] = [];

        _and.push({ site: { _id: { _eq: this.stockSite.value?._id } } });

        if (this.zones.value.length > 0) {
            _and.push({ locationZone: { _id: { _in: this.zones.value.map(zone => zone._id ?? '') } } });
        }

        return _and.length > 0 ? { _and } : {};
    }

    @ui.decorators.multiReferenceField<StockCountCreation, ItemCategory>({
        parent() {
            return this.criteriaBlock;
        },
        title: 'Category',
        node: '@sage/xtrem-master-data/ItemCategory',
        lookupDialogTitle: 'Select category',
        minLookupCharacters: 0,
        valueField: 'name',
        helperTextField: 'id',
        columns: [
            ui.nestedFields.text({ title: 'Name', bind: 'name' }),
            ui.nestedFields.text({ title: 'ID', bind: 'id' }),
        ],
        placeholder: 'Select...',
        onChange() {
            this.resetItemSitesSelection(); // see onDirtyStateUpdated. When XT-85743 is fixed this line should be removed
        },
    })
    categories: ui.fields.MultiReference<ItemCategory>;

    @ui.decorators.dateField<StockCountCreation>({
        parent() {
            return this.criteriaBlock;
        },
        title: 'Items not counted as of',
        width: 'small',
        maxDate() {
            if (this.effectiveDate.value) {
                return this.effectiveDate.value;
            }
            return '';
        },
        onChange() {
            this.resetItemSitesSelection(); // see onDirtyStateUpdated. When XT-85743 is fixed this line should be removed
        },
    })
    lastCountDate: ui.fields.Date;

    @ui.decorators.switchField<StockCountCreation>({
        parent() {
            return this.stockCriteriaBlock;
        },
        title: 'In stock only',
        onChange() {
            this.resetItemSitesSelection(); // see onDirtyStateUpdated. When XT-85743 is fixed this line should be removed
            this.locations.isReadOnly = !this.hasStockRecords.value;
            this.zones.isReadOnly = !this.hasStockRecords.value;
            if (!this.hasStockRecords.value) {
                this.locations.value = [];
                this.zones.value = [];
            }
        },
    })
    hasStockRecords: ui.fields.Switch;

    @ui.decorators.multiReferenceField<StockCountCreation, LocationZone>({
        parent() {
            return this.stockCriteriaBlock;
        },
        title: 'Zone',
        node: '@sage/xtrem-master-data/LocationZone',
        lookupDialogTitle: 'Select storage zone',
        minLookupCharacters: 0,
        valueField: 'name',
        helperTextField: 'id',
        columns: [
            ui.nestedFields.text({ title: 'Name', bind: 'name' }),
            ui.nestedFields.text({ title: 'ID', bind: 'id' }),
            ui.nestedFields.text({ title: 'Type', bind: 'zoneType' }),
            ui.nestedFields.reference<StockCountCreation, LocationZone, LocationZone['site']>({
                bind: 'site',
                node: '@sage/xtrem-system/Site',
                valueField: '_id',
                isHidden: true,
            }),
        ],
        placeholder: 'Select...',
        isHidden() {
            return this.siteNotLocationManaged();
        },
        filter() {
            return {
                site: { _id: { _eq: this.stockSite.value?._id } },
            };
        },
        onChange() {
            this.resetItemSitesSelection(); // see onDirtyStateUpdated. When XT-85743 is fixed this line should be removed
        },
    })
    zones: ui.fields.MultiReference<LocationZone>;

    @ui.decorators.multiReferenceField<StockCountCreation, Location>({
        parent() {
            return this.stockCriteriaBlock;
        },
        title: 'Location',
        node: '@sage/xtrem-master-data/Location',
        lookupDialogTitle: 'Select location',
        minLookupCharacters: 0,
        valueField: 'name',
        helperTextField: 'id',
        columns: [
            ui.nestedFields.technical({ bind: '_id' }),
            ui.nestedFields.text({ title: 'Name', bind: 'name' }),
            ui.nestedFields.text({ title: 'ID', bind: 'id' }),
            ui.nestedFields.reference<StockCountCreation, Location, Location['locationZone']>({
                bind: 'locationZone',
                node: '@sage/xtrem-master-data/LocationZone',
                valueField: '_id',
                isHidden: true,
                columns: [
                    ui.nestedFields.reference<
                        StockCountCreation,
                        Location['locationZone'],
                        Location['locationZone']['site']
                    >({
                        bind: 'site',
                        node: '@sage/xtrem-system/Site',
                        valueField: '_id',
                    }),
                ],
            }),
        ],
        placeholder: 'Select...',
        isHidden() {
            return this.siteNotLocationManaged();
        },
        filter() {
            return this.locationFilter();
        },
        onChange() {
            this.resetItemSitesSelection(); // see onDirtyStateUpdated. When XT-85743 is fixed this line should be removed
        },
    })
    locations: ui.fields.MultiReference<Location>;

    @ui.decorators.block<StockCountCreation>({
        parent() {
            return this.mainSection;
        },
        width: 'extra-large',
        title: 'Stock count',
    })
    stockCountBlock: ui.containers.Block;

    @ui.decorators.textField<StockCountCreation>({
        parent() {
            return this.stockCountBlock;
        },
        title: 'Description',
        isMandatory: true,
        onChange() {
            this.resetItemSitesSelection(); // see onDirtyStateUpdated. When XT-85743 is fixed this line should be removed
        },
    })
    description: ui.fields.Text;

    @ui.decorators.dateField<StockCountCreation>({
        parent() {
            return this.stockCountBlock;
        },
        title: 'Date',
        isMandatory: true,
        onChange() {
            this.resetItemSitesSelection(); // see onDirtyStateUpdated. When XT-85743 is fixed this line should be removed
        },
    })
    effectiveDate: ui.fields.Date;

    @ui.decorators.block<StockCountCreation>({
        parent() {
            return this.mainSection;
        },
        width: 'extra-large',
        isTitleHidden: true,
    })
    mainBlock: ui.containers.Block;

    @ui.decorators.checkboxField<StockCountCreation>({
        parent() {
            return this.mainBlock;
        },
        title: 'Select all',
        isTransient: true,
        isHidden() {
            return !this.$.recordId;
        },
        async onChange() {
            const allSelected = !this.selectAllCheckbox.value;
            this.itemSites.canSelect = allSelected;
            await this.itemSites.refresh();
            this.$.setPageClean();
        },
    })
    selectAllCheckbox: ui.fields.Checkbox;

    @ui.decorators.nestedGridField<StockCountCreation, [ItemSite, Stock]>({
        parent() {
            return this.itemSiteSection;
        },
        bind: 'itemSites',
        node: '@sage/xtrem-stock/StockCount',
        canSelect: true,
        isTitleHidden: true,
        pageSize: 10,
        isHidden() {
            return !this.$.recordId;
        },
        levels: [
            {
                node: '@sage/xtrem-master-data/ItemSite',
                childProperty: 'countStockRecords',
                orderBy: { item: { name: +1 } },
                columns: [
                    ui.nestedFields.reference<StockCountCreation, ItemSite, Item>({
                        bind: 'item',
                        title: 'Item',
                        node: '@sage/xtrem-master-data/Item',
                        tunnelPage: '@sage/xtrem-master-data/Item',
                        isReadOnly: true,
                        valueField: 'name',
                    }),
                    ui.nestedFields.text({
                        bind: { item: { id: true } },
                        title: 'Item ID',
                        isReadOnly: true,
                    }),
                    ui.nestedFields.text({
                        bind: { item: { description: true } },
                        title: 'Item description',
                        isReadOnly: true,
                    }),
                    ui.nestedFields.checkbox({
                        title: 'Stock record',
                        bind: 'hasStockRecords',
                        isReadOnly: true,
                        isHidden() {
                            return !!this.hasStockRecords.value;
                        },
                    }),
                ],
            },
            {
                node: '@sage/xtrem-stock-data/Stock',
                orderBy: { location: +1 },
                filter() {
                    return this.stockFilter();
                },
                columns: [
                    ui.nestedFields.numeric<StockCountCreation, Stock>({
                        title: 'Quantity',
                        bind: 'quantityInStockUnit',
                        scale: (_rowId, rowData) => MasterDataUtils.getScaleValue(2, rowData?.stockUnit?.decimalDigits),
                        postfix: (_rowId, rowData) => rowData?.stockUnit?.symbol ?? '',
                        isReadOnly: true,
                    }),
                    ui.nestedFields.technical<StockCountCreation, Stock, UnitOfMeasure>({
                        bind: 'stockUnit',
                        nestedFields: [
                            ui.nestedFields.technical({ bind: 'id' }),
                            ui.nestedFields.technical({ bind: 'symbol' }),
                            ui.nestedFields.technical({ bind: 'decimalDigits' }),
                        ],
                    }),
                    ui.nestedFields.reference<StockCountCreation, Stock, Location>({
                        title: 'Location',
                        bind: 'location',
                        node: '@sage/xtrem-master-data/Location',
                        valueField: 'name',
                        isReadOnly: true,
                        columns: [ui.nestedFields.technical({ bind: '_id' })],
                        isHidden() {
                            return this.stockSite.value ? !this.stockSite.value.isLocationManaged : false;
                        },
                    }),
                    ui.nestedFields.reference<StockCountCreation, Stock, Location>({
                        title: 'Zone',
                        bind: 'location',
                        node: '@sage/xtrem-master-data/Location',
                        valueField: { locationZone: { name: true } },
                        isReadOnly: true,
                        isHidden() {
                            return this.stockSite.value ? !this.stockSite.value.isLocationManaged : false;
                        },
                    }),
                    ui.nestedFields.reference<StockCountCreation, Stock, StockStatus>({
                        title: 'Status',
                        bind: 'status',
                        node: '@sage/xtrem-stock-data/StockStatus',
                        tunnelPage: '@sage/xtrem-stock-data/StockStatus',
                        valueField: 'name',
                        isReadOnly: true,
                    }),
                    ui.nestedFields.reference<StockCountCreation, Stock, Lot>({
                        title: 'Lot',
                        bind: 'lot',
                        node: '@sage/xtrem-stock-data/Lot',
                        valueField: 'id',
                        isReadOnly: true,
                    }),
                    ui.nestedFields.reference<StockCountCreation, Stock, Lot>({
                        title: 'Sublot',
                        bind: 'lot',
                        node: '@sage/xtrem-stock-data/Lot',
                        valueField: 'sublot',
                        isReadOnly: true,
                    }),
                    ui.nestedFields.text<StockCountCreation, Stock>({
                        title: 'Owner',
                        bind: 'owner',
                        isReadOnly: true,
                        isHiddenOnMainField: true,
                    }),
                ],
            },
        ],
    })
    itemSites: ui.fields.NestedGrid<[ItemSite, Stock], StockCountCreation>;

    @ui.decorators.pageAction<StockCountCreation>({
        title: 'Create',
        isHidden() {
            return !this.$.recordId || this.$.isDirty;
        },
        onError(error) {
            return MasterDataUtils.formatError(this, error);
        },
        isDisabled() {
            return (
                (!this.itemSites.selectedRecords[0].length &&
                    !this.itemSites.selectedRecords[1].length &&
                    !this.selectAllCheckbox.value) ||
                this.itemSites.value.length === 0
            );
        },
        async onClick() {
            this.$.loader.isHidden = false;
            const recordsUpdated = await this.$.graph
                .node('@sage/xtrem-stock/StockCount')
                .asyncOperations.confirmStockCount.runToCompletion(true, {
                    stockCount: this.$.recordId || '',
                    selectedRecords: {
                        allSelected: this.selectAllCheckbox.value || false,
                        stockRecords: this.itemSites.selectedRecords[1],
                        itemSites: this.itemSites.selectedRecords[0],
                    },
                })
                .execute();

            if (recordsUpdated) {
                this.$.showToast(
                    ui.localize(
                        '@sage/xtrem-stock/pages__stock_count__creation',
                        'The stock count was created successfully.',
                    ),
                    { type: 'success', timeout: 5000 },
                );

                if (this.$.recordId) {
                    this.$.router.goTo(`@sage/xtrem-stock/StockCount`, { _id: this.$.recordId });
                }
            }

            this.$.loader.isHidden = true;
        },
    })
    createStockCount: ui.PageAction;

    private checkItemRange() {
        if (this.fromItem.value?.id && this.toItem.value?.id && this.fromItem.value.id > this.toItem.value.id) {
            return ui.localize(
                '@sage/xtrem-stock/pages__stock_count_creation__invalid_item_range',
                "Enter a 'To item' higher than the 'From item'.",
            );
        }

        return null;
    }

    private siteNotLocationManaged(): boolean {
        return this.stockSite.value ? !this.stockSite.value.isLocationManaged : false;
    }

    private changeCriteriaVisibility() {
        const isStockSiteEntered = !!this.stockSite.value;
        this.fromItem.isDisabled = !isStockSiteEntered;
        this.toItem.isDisabled = !isStockSiteEntered;
        this.locations.isDisabled = !isStockSiteEntered;
        this.zones.isDisabled = !isStockSiteEntered;
        this.categories.isDisabled = !isStockSiteEntered;
    }

    resetItemSitesSelection() {
        // it's important to reset this array to be sure previous selections are removed and not count only on the 'refresh' method
        this.itemSites.value = [];
        this.selectAllCheckbox.value = false;
        this.selectAllCheckbox.isDisabled = true;
    }

    activateItemSitesSelection() {
        this.itemSites.canSelect = true;
        this.selectAllCheckbox.isDisabled = this.itemSites.value.length === 0;
    }
}
