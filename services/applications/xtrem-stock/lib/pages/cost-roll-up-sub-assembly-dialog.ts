import { extractEdges } from '@sage/xtrem-client';
import * as utils from '@sage/xtrem-master-data/build/lib/client-functions/utils';
import type { CostRollUpSubAssembly, GraphApi } from '@sage/xtrem-stock-api';
import * as ui from '@sage/xtrem-ui';
import * as CostRollUp from '../client-functions/cost-roll-up-lib';
import type * as StockInterfaces from '../client-functions/interfaces';

@ui.decorators.page<CostRollUpSubAssemblyDialog>({
    title: 'Subassemblies',
    module: 'stock',
    mode: 'default',
    isTransient: true,

    businessActions() {
        return [this.ok];
    },

    async onLoad() {
        const queryParams = CostRollUp.getQueryParameters(this.$.queryParameters.args);
        if (!queryParams) {
            throw new Error('Parameters are missing.');
        }
        this.panelParameters = queryParams;

        this.$.page.title = ui.localize(
            '@sage/xtrem-stock/pages__cost_roll_up_sub_assembly_dialog____update_title',
            'Item: {{itemName}}',
            { itemName: this.panelParameters.resultLineItemName },
        );

        const result = extractEdges(
            await this.$.graph
                .node('@sage/xtrem-stock/CostRollUpSubAssembly')
                .query(
                    ui.queryUtils.edgesSelector<CostRollUpSubAssembly>(
                        {
                            _id: true,
                            _sortValue: true,
                            componentNumber: true,
                            item: {
                                id: true,
                                name: true,
                            },
                            stockUnit: {
                                id: true,
                                decimalDigits: true,
                                symbol: true,
                            },
                            calculationQuantity: true,
                            currentMaterialCost: true,
                            materialCost: true,
                            currentLaborCost: true,
                            laborCost: true,
                            currentMachineCost: true,
                            machineCost: true,
                            currentToolCost: true,
                            toolCost: true,
                            currentTotalCost: true,
                            totalCost: true,
                        },
                        {
                            filter: {
                                resultLine: { _id: this.panelParameters.resultLineId },
                            },
                        },
                    ),
                )
                .execute(),
        );

        if (result.length > 0) {
            this.subAssemblies.value = result as ui.PartialNodeWithId<CostRollUpSubAssembly>[];
        }
    },
    onError(error: string | (Error & { errors: Array<any> })) {
        return utils.formatError(this, error);
    },
})
export class CostRollUpSubAssemblyDialog extends ui.Page<GraphApi> {
    private panelParameters: StockInterfaces.CostRollUpSubAssemblyDialogParameters;

    @ui.decorators.pageAction<CostRollUpSubAssemblyDialog>({
        title: 'OK',
        onClick() {
            this.$.finish({});
        },
    })
    ok: ui.PageAction;

    @ui.decorators.section<CostRollUpSubAssemblyDialog>({
        title: 'Subassemblies',
        isTitleHidden: true,
    })
    mainSection: ui.containers.Section;

    @ui.decorators.tableField<CostRollUpSubAssemblyDialog, CostRollUpSubAssembly>({
        title: 'Subassemblies',
        node: '@sage/xtrem-stock/CostRollUpSubAssembly',
        canResizeColumns: true,
        isHelperTextHidden: true,
        isReadOnly: true,
        canExport: true,
        canSelect: false,
        parent() {
            return this.mainSection;
        },
        columns: [
            ui.nestedFields.technical({ bind: '_sortValue' }),
            ui.nestedFields.numeric({
                bind: 'componentNumber',
                title: 'Component number',
                size: 'small',
            }),
            ui.nestedFields.reference({
                bind: 'item',
                title: 'Item ID',
                node: '@sage/xtrem-master-data/Item',
                valueField: 'id',
            }),
            ui.nestedFields.reference({
                bind: 'item',
                title: 'Item name',
                node: '@sage/xtrem-master-data/Item',
                valueField: 'name',
            }),
            ui.nestedFields.technical({
                bind: 'stockUnit',
                node: '@sage/xtrem-master-data/UnitOfMeasure',
                nestedFields: [
                    ui.nestedFields.technical({ bind: 'decimalDigits' }),
                    ui.nestedFields.technical({ bind: 'symbol' }),
                ],
            }),
            ui.nestedFields.numeric({
                bind: 'calculationQuantity',
                title: 'Calculation quantity',
                size: 'small',
                unitMode: 'unitOfMeasure',
                unit(_val, rowData) {
                    return rowData?.stockUnit;
                },
            }),
            ui.nestedFields.numeric({
                bind: 'currentMaterialCost',
                title: 'Current material cost',
                size: 'small',
                prefix() {
                    return this.panelParameters.currencySymbol;
                },
                scale() {
                    return this.panelParameters.costScale;
                },
            }),
            ui.nestedFields.numeric({
                bind: 'materialCost',
                title: 'Material cost',
                size: 'small',
                prefix() {
                    return this.panelParameters.currencySymbol;
                },
                scale() {
                    return this.panelParameters.costScale;
                },
            }),
            ui.nestedFields.numeric({
                bind: 'currentMachineCost',
                title: 'Current machine cost',
                size: 'small',
                prefix() {
                    return this.panelParameters.currencySymbol;
                },
                scale() {
                    return this.panelParameters.costScale;
                },
            }),
            ui.nestedFields.numeric({
                bind: 'machineCost',
                title: 'Machine cost',
                size: 'small',
                prefix() {
                    return this.panelParameters.currencySymbol;
                },
                scale() {
                    return this.panelParameters.costScale;
                },
            }),
            ui.nestedFields.numeric({
                bind: 'currentLaborCost',
                title: 'Current labor cost',
                size: 'small',
                prefix() {
                    return this.panelParameters.currencySymbol;
                },
                scale() {
                    return this.panelParameters.costScale;
                },
            }),
            ui.nestedFields.numeric({
                bind: 'laborCost',
                title: 'Labor cost',
                size: 'small',
                prefix() {
                    return this.panelParameters.currencySymbol;
                },
                scale() {
                    return this.panelParameters.costScale;
                },
            }),
            ui.nestedFields.numeric({
                bind: 'currentToolCost',
                title: 'Current tool cost',
                size: 'small',
                prefix() {
                    return this.panelParameters.currencySymbol;
                },
                scale() {
                    return this.panelParameters.costScale;
                },
            }),
            ui.nestedFields.numeric({
                bind: 'toolCost',
                title: 'Tool cost',
                size: 'small',
                prefix() {
                    return this.panelParameters.currencySymbol;
                },
                scale() {
                    return this.panelParameters.costScale;
                },
            }),
            ui.nestedFields.numeric({
                bind: 'currentTotalCost',
                title: 'Current total cost',
                size: 'small',
                prefix() {
                    return this.panelParameters.currencySymbol;
                },
                scale() {
                    return this.panelParameters.costScale;
                },
            }),
            ui.nestedFields.numeric({
                bind: 'totalCost',
                title: 'Total cost',
                size: 'small',
                prefix() {
                    return this.panelParameters.currencySymbol;
                },
                scale() {
                    return this.panelParameters.costScale;
                },
            }),
        ],
    })
    subAssemblies: ui.fields.Table<CostRollUpSubAssembly>;
}
