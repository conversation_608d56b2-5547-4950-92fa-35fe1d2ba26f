import { DateValue } from '@sage/xtrem-date-time';
import type { Item } from '@sage/xtrem-master-data-api';
import * as masterDataUtils from '@sage/xtrem-master-data/build/lib/client-functions/utils';
import { setMultiReferenceIfSingleValue } from '@sage/xtrem-master-data/lib/client-functions/get-default';
import type { GraphApi } from '@sage/xtrem-stock-api';
import { StockValuationMenu } from '@sage/xtrem-stock-data/build/lib/menu-items/stock-valuation-menu';
import * as ui from '@sage/xtrem-ui';
import type { StockMovement, ValuedItemSiteResult } from '../client-functions/interfaces';
import * as StockValuationLib from '../client-functions/stock-valuation-lib';

@ui.decorators.page<StockAvcJustificationInquiry>({
    menuItem: StockValuationMenu,
    priority: 50,
    title: 'Average cost justification',
    module: 'stock',
    mode: 'default',
    isTransient: true,
    access: { node: '@sage/xtrem-stock/StockValuationInputSet', bind: '$read' },
    businessActions() {
        return [this.runStockValuation];
    },
    async onLoad() {
        this.valuationDate.value = DateValue.today().toString();
        await setMultiReferenceIfSingleValue([this.sites]);
    },
})
export class StockAvcJustificationInquiry extends ui.Page<GraphApi> {
    @ui.decorators.pageAction<StockAvcJustificationInquiry>({
        title: 'Run',
        isDisabled() {
            return this.itemSelection.value.length === 0;
        },
        onError(error: string | (Error & { errors: Array<any> })) {
            return masterDataUtils.formatError(this, error);
        },
        async onClick() {
            this.$.loader.isHidden = false;
            StockValuationLib.displayRunTable(this, true);
            const details = await StockValuationLib.getValuationOfItemsSites(
                this,
                this.itemSelection.value.map(itemSiteLine => itemSiteLine._id)!,
                this.valuationDate.value!,
                { withMovements: true },
            );
            if (details.length)
                details.forEach((detail: ValuedItemSiteResult) => {
                    this.itemValues.addOrUpdateRecordValue(detail, 0, undefined);
                    let stockQuantity = 0;
                    let stockValue = 0;
                    if (detail.stockMovements) {
                        detail.stockMovements.forEach((movement: StockMovement) => {
                            stockQuantity += +movement.quantityInStockUnit;
                            stockValue += +movement.orderAmount;
                            movement.stockQuantity = stockQuantity;
                            movement.stockValue = stockValue;
                            movement.cost = +stockQuantity ? +stockValue / +stockQuantity : 0;
                            movement.type = movement.documentType;
                            this.itemValues.addOrUpdateRecordValue(movement, 1, detail._id);
                        });
                    }
                });
            this.$.setPageClean();
            this.$.loader.isHidden = true;
        },
        buttonType: 'primary',
    })
    runStockValuation: ui.PageAction;

    @ui.decorators.section<StockAvcJustificationInquiry>({
        isOpen: true,
        isTitleHidden: true,
        title: 'General',
    })
    mainSection: ui.containers.Section;

    @ui.decorators.block<StockAvcJustificationInquiry>({
        parent() {
            return this.mainSection;
        },
        width: 'extra-large',
        title: 'Criteria',
    })
    criteriaBlock: ui.containers.Block;

    @ui.decorators.referenceField<StockAvcJustificationInquiry>({
        parent() {
            return this.criteriaBlock;
        },
        title: 'Company',
        node: '@sage/xtrem-system/Company',
        tunnelPage: '@sage/xtrem-master-data/Company',
        lookupDialogTitle: 'Select company',
        minLookupCharacters: 0,
        valueField: 'name',
        helperTextField: 'id',
        columns: [ui.nestedFields.text({ bind: 'name' }), ui.nestedFields.text({ bind: 'id' })],
        placeholder: 'Select...',
        width: 'small',
        onChange() {
            StockValuationLib.changeSearchCriteria(this);
            this.sites.value = [];
        },
    })
    company: ui.fields.Reference;

    @ui.decorators.multiReferenceField<StockAvcJustificationInquiry>({
        parent() {
            return this.criteriaBlock;
        },
        title: 'Site',
        node: '@sage/xtrem-system/Site',
        lookupDialogTitle: 'Select site',
        minLookupCharacters: 0,
        valueField: 'name',
        helperTextField: 'id',
        isDisabled() {
            return !!this.company.value;
        },
        columns: [ui.nestedFields.text({ bind: 'name' }), ui.nestedFields.text({ bind: 'id' })],
        placeholder: 'Select...',
        width: 'small',
        onChange() {
            StockValuationLib.changeSearchCriteria(this);
        },
    })
    sites: ui.fields.MultiReference;

    @ui.decorators.referenceField<StockAvcJustificationInquiry, Item>({
        parent() {
            return this.criteriaBlock;
        },
        title: 'From item',
        node: '@sage/xtrem-master-data/Item',
        tunnelPage: '@sage/xtrem-master-data/Item',
        lookupDialogTitle: 'Select item',
        valueField: 'name',
        helperTextField: 'id',
        columns: [
            ui.nestedFields.text({ bind: 'name' }),
            ui.nestedFields.text({ bind: 'id' }),
            ui.nestedFields.text({ bind: 'description' }),
            ui.nestedFields.text({ title: 'Category', bind: { category: { name: true } } }),
            ui.nestedFields.checkbox({ bind: 'isStockManaged', isHidden: true }),
        ],
        filter: { isStockManaged: true },
        orderBy: { id: +1 },
        placeholder: 'Select...',
        width: 'small',
        onChange() {
            StockValuationLib.changeSearchCriteria(this);
        },
    })
    itemFrom: ui.fields.Reference;

    @ui.decorators.referenceField<StockAvcJustificationInquiry, Item>({
        parent() {
            return this.criteriaBlock;
        },
        title: 'To item',
        node: '@sage/xtrem-master-data/Item',
        tunnelPage: '@sage/xtrem-master-data/Item',
        lookupDialogTitle: 'Select item',
        valueField: 'name',
        helperTextField: 'id',
        columns: [
            ui.nestedFields.text({ title: 'Name', bind: 'name' }),
            ui.nestedFields.text({ title: 'Item', bind: 'id' }),
            ui.nestedFields.text({ title: 'Description', bind: 'description' }),
            ui.nestedFields.text({ title: 'Category', bind: { category: { name: true } } }),
            ui.nestedFields.checkbox({ bind: 'isStockManaged', isHidden: true }),
        ],
        filter: { isStockManaged: true },
        placeholder: 'Select...',
        width: 'small',
        onChange() {
            StockValuationLib.changeSearchCriteria(this);
        },
    })
    itemTo: ui.fields.Reference;

    @ui.decorators.dateField<StockAvcJustificationInquiry>({
        parent() {
            return this.criteriaBlock;
        },
        title: 'Date',
        width: 'small',
        maxDate: DateValue.today().toString(),
        onChange() {
            StockValuationLib.displaySearchTable(this, false);
            this.itemValues.value = [];
        },
    })
    valuationDate: ui.fields.Date;

    @ui.decorators.buttonField<StockAvcJustificationInquiry>({
        parent() {
            return this.criteriaBlock;
        },
        map() {
            return ui.localize('@sage/xtrem-stock/pages__stock_valuation_inquiry__search', 'Search');
        },
        width: 'small',
        onError(error: string | (Error & { errors: Array<any> })) {
            return masterDataUtils.formatError(this, error);
        },
        async onClick() {
            this.$.loader.isHidden = false;
            StockValuationLib.displaySearchTable(this, true);
            this.itemSelection.value = await StockValuationLib.search(this, {
                company: this.company.value?._id ?? null,
                stockSiteList: this.sites.value?.map(site => site.id as string),
                itemRange: { start: this.itemFrom.value?.id ?? null, end: this.itemTo.value?.id ?? null },
                valuationMethods: ['averageCost'],
            });
            this.$.loader.isHidden = true;
        },
    })
    searchButton: ui.fields.Button;

    @ui.decorators.tableField<StockAvcJustificationInquiry>({
        title: 'Results',
        node: '@sage/xtrem-master-data/ItemSite',
        canSelect: false,
        isHelperTextHidden: true,
        isTransient: true,
        orderBy: { item: { id: 1 }, site: { name: 1 } },
        parent() {
            return this.mainSection;
        },
        columns: [
            ui.nestedFields.reference({
                bind: 'site',
                title: 'Site',
                node: '@sage/xtrem-system/Site',
                isReadOnly: true,
                isHidden() {
                    return StockValuationLib.isSiteHidden(this);
                },
                valueField: 'name',
            }),
            ui.nestedFields.reference({
                bind: 'item',
                title: 'Item',
                node: '@sage/xtrem-master-data/Item',
                valueField: 'name',
                isReadOnly: true,
            }),
            ui.nestedFields.reference({
                bind: 'item',
                title: 'Item ID',
                node: '@sage/xtrem-master-data/Item',
                valueField: 'id',
                isReadOnly: true,
            }),
            ui.nestedFields.reference({
                bind: 'item',
                node: '@sage/xtrem-master-data/Item',
                valueField: 'description',
                title: 'Item description',
                isReadOnly: true,
                isHiddenOnMainField: true,
            }),
            ui.nestedFields.reference({
                bind: 'stockUnit',
                title: 'Unit of measure',
                node: '@sage/xtrem-master-data/UnitOfMeasure',
                tunnelPage: '@sage/xtrem-master-data/UnitOfMeasure',
                valueField: 'name',
                isReadOnly: true,
            }),
        ],
    })
    itemSelection: ui.fields.Table;

    @ui.decorators.nestedGridField<StockAvcJustificationInquiry>({
        canUserHideColumns: false,
        canSelect: false,
        isHelperTextHidden: true,
        isChangeIndicatorDisabled: true,
        isTransient: true,
        isHidden: true,
        parent() {
            return this.mainSection;
        },
        levels: [
            {
                node: '@sage/xtrem-stock-data/StockJournal',
                orderBy: { item: { id: 1 }, site: { name: 1 } },
                childProperty: 'movements',
                columns: [
                    ui.nestedFields.reference({
                        bind: 'item',
                        title: 'Item',
                        node: '@sage/xtrem-master-data/Item',
                        valueField: 'id',
                        isReadOnly: true,
                    }),
                    ui.nestedFields.reference({
                        bind: 'item',
                        title: 'Name',
                        node: '@sage/xtrem-master-data/Item',
                        valueField: 'name',
                        isReadOnly: true,
                    }),
                    ui.nestedFields.reference({
                        bind: 'site',
                        title: 'Site',
                        node: '@sage/xtrem-system/Site',
                        valueField: 'name',
                        isReadOnly: true,
                        isHidden() {
                            return StockValuationLib.isSiteHidden(this);
                        },
                    }),
                    ui.nestedFields.reference({
                        bind: 'company',
                        title: 'Company',
                        node: '@sage/xtrem-system/Site',
                        valueField: 'name',
                        isReadOnly: true,
                    }),
                    ui.nestedFields.reference({
                        bind: 'postingClass',
                        title: 'Posting class',
                        node: '@sage/xtrem-finance-data/PostingClass',
                        valueField: 'name',
                        isReadOnly: true,
                    }),

                    ui.nestedFields.numeric({
                        bind: 'quantityInStock',
                        title: 'Quantity',
                        isReadOnly: true,
                        scale(val, rowData) {
                            return StockValuationLib.stockUnitScale(rowData?.stockUnit);
                        },
                        postfix(rowId: any, rowData: any) {
                            return StockValuationLib.stockUnitSymbol(rowData?.stockUnit);
                        },
                    }),
                    ui.nestedFields.numeric({
                        bind: 'stockValue',
                        title: 'Stock value',
                        isReadOnly: true,
                        scale(val, rowData) {
                            return StockValuationLib.currencyScale(rowData?.currency);
                        },
                        postfix(rowId: any, rowData: any) {
                            return StockValuationLib.currencySymbol(rowData?.currency);
                        },
                    }),
                    ui.nestedFields.numeric({
                        bind: 'unitCost',
                        title: 'Unit cost',
                        isReadOnly: true,
                        scale(val, rowData) {
                            return StockValuationLib.currencyScale(rowData?.currency);
                        },
                        postfix(rowId: any, rowData: any) {
                            return StockValuationLib.currencySymbol(rowData?.currency);
                        },
                    }),
                    ui.nestedFields.date({
                        bind: 'dateOfValuation',
                        title: 'Date of valuation',
                        isReadOnly: true,
                    }),
                    ui.nestedFields.dropdownList({
                        bind: 'valuationMethod',
                        title: 'Cost type',
                        optionType: '@sage/xtrem-master-data/costValuationMethod',
                        isReadOnly: true,
                    }),
                    // Add stock unit and currency to have them in the export
                    ui.nestedFields.reference({
                        bind: 'stockUnit',
                        title: 'Stock unit',
                        node: '@sage/xtrem-master-data/UnitOfMeasure',
                        tunnelPage: '@sage/xtrem-master-data/UnitOfMeasure',
                        valueField: 'symbol',
                        isReadOnly: true,
                        columns: [
                            ui.nestedFields.text({ bind: 'id', isHidden: true }),
                            ui.nestedFields.text({ title: 'Symbol', bind: 'symbol' }),
                        ],
                    }),
                    ui.nestedFields.reference({
                        bind: 'currency',
                        title: 'Currency',
                        node: '@sage/xtrem-master-data/Currency',
                        valueField: 'symbol',
                        isReadOnly: true,
                    }),
                ],
            },
            {
                node: '@sage/xtrem-stock-data/StockJournal',
                orderBy: { _id: 1 },
                columns: [
                    ui.nestedFields.technical({ bind: '_id' }),
                    ui.nestedFields.date({
                        bind: 'creationDate',
                        title: 'Date created',
                        isReadOnly: true,
                    }),
                    ui.nestedFields.numeric({
                        bind: 'sequence',
                        title: 'sequence',
                        isHidden: true,
                    }),
                    ui.nestedFields.text({
                        bind: 'type',
                        title: 'Document type',
                        isReadOnly: true,
                    }),
                    ui.nestedFields.reference({
                        node: '@sage/xtrem-stock-data/StockMovementDetail',
                        bind: 'stockDetail',
                        title: 'Document',
                        valueField: { documentLine: { documentNumber: true } },
                        isReadOnly: true,
                    }),
                    ui.nestedFields.date({
                        bind: 'effectiveDate',
                        title: 'Date',
                        isReadOnly: true,
                    }),
                    ui.nestedFields.numeric({
                        bind: 'quantityInStockUnit',
                        title: 'Quantity',
                        isReadOnly: true,
                        scale(val, rowData) {
                            return StockValuationLib.stockUnitScale(rowData?.stockUnit);
                        },
                        postfix(rowId: any, rowData: any) {
                            return StockValuationLib.stockUnitSymbol(rowData?.stockUnit);
                        },
                    }),
                    ui.nestedFields.reference({
                        bind: 'stockUnit',
                        title: 'Unit of measure',
                        node: '@sage/xtrem-master-data/UnitOfMeasure',
                        tunnelPage: '@sage/xtrem-master-data/UnitOfMeasure',
                        valueField: 'name',
                        isReadOnly: true,
                        isHidden: true,
                    }),
                    ui.nestedFields.numeric({
                        bind: 'orderCost',
                        title: 'Order cost',
                        isReadOnly: true,
                        scale(val, rowData) {
                            return StockValuationLib.currencyScale(rowData?.site.financialCurrency);
                        },
                        postfix(rowId: any, rowData: any) {
                            return StockValuationLib.currencySymbol(rowData?.site.financialCurrency);
                        },
                    }),
                    ui.nestedFields.numeric({
                        bind: 'orderAmount',
                        title: 'Order amount',
                        isReadOnly: true,
                        scale(val, rowData) {
                            return StockValuationLib.currencyScale(rowData?.site.financialCurrency);
                        },
                        postfix(rowId: any, rowData: any) {
                            return StockValuationLib.currencySymbol(rowData?.site.financialCurrency);
                        },
                    }),
                    ui.nestedFields.numeric({
                        bind: 'stockQuantity',
                        title: 'Stock quantity',
                        isReadOnly: true,
                        scale(val, rowData) {
                            return StockValuationLib.stockUnitScale(rowData?.stockUnit);
                        },
                        postfix(rowId: any, rowData: any) {
                            return StockValuationLib.stockUnitSymbol(rowData?.stockUnit);
                        },
                    }),
                    ui.nestedFields.numeric({
                        bind: 'stockValue',
                        title: 'Stock value',
                        isReadOnly: true,
                        scale(val, rowData) {
                            return StockValuationLib.currencyScale(rowData?.site.financialCurrency);
                        },
                        postfix(rowId: any, rowData: any) {
                            return StockValuationLib.currencySymbol(rowData?.site.financialCurrency);
                        },
                    }),
                    ui.nestedFields.numeric({
                        bind: 'cost',
                        title: 'Unit cost',
                        isReadOnly: true,
                        scale(val, rowData) {
                            return StockValuationLib.currencyScale(rowData?.site.financialCurrency);
                        },
                        postfix(rowId: any, rowData: any) {
                            return StockValuationLib.currencySymbol(rowData?.site.financialCurrency);
                        },
                    }),

                    ui.nestedFields.numeric({
                        bind: 'valuedCost',
                        title: 'Valued cost',
                        isReadOnly: true,
                        scale(val, rowData) {
                            return StockValuationLib.currencyScale(rowData?.site.financialCurrency);
                        },
                        postfix(rowId: any, rowData: any) {
                            return StockValuationLib.currencySymbol(rowData?.site.financialCurrency);
                        },
                    }),
                    ui.nestedFields.numeric({
                        bind: 'movementAmount',
                        title: 'Movement amount',
                        isReadOnly: true,
                        scale(val, rowData) {
                            return StockValuationLib.currencyScale(rowData?.site.financialCurrency);
                        },
                        postfix(rowId: any, rowData: any) {
                            return StockValuationLib.currencySymbol(rowData?.site.financialCurrency);
                        },
                    }),
                    ui.nestedFields.numeric({
                        bind: 'costVariance',
                        title: 'Cost variance',
                        isReadOnly: true,
                        scale(val, rowData) {
                            return StockValuationLib.currencyScale(rowData?.site.financialCurrency);
                        },
                        postfix(rowId: any, rowData: any) {
                            return StockValuationLib.currencySymbol(rowData?.site.financialCurrency);
                        },
                    }),
                    ui.nestedFields.numeric({
                        bind: 'amountVariance',
                        title: 'Amount variance',
                        isReadOnly: true,
                        scale(val, rowData) {
                            return StockValuationLib.currencyScale(rowData?.site.financialCurrency);
                        },
                        postfix(rowId: any, rowData: any) {
                            return StockValuationLib.currencySymbol(rowData?.site.financialCurrency);
                        },
                    }),

                    ui.nestedFields.reference({
                        bind: 'site',
                        node: '@sage/xtrem-system/Site',
                        isReadOnly: true,
                        valueField: 'id',
                        isHidden: true,
                    }),
                    ui.nestedFields.reference({
                        bind: 'item',
                        node: '@sage/xtrem-master-data/Item',
                        valueField: 'id',
                        isReadOnly: true,
                        isHidden: true,
                    }),
                ],
            },
        ],
    })
    itemValues: ui.fields.NestedGrid;
}
