import type { Graph<PERSON><PERSON> } from '@sage/xtrem-stock-api';
import type { Stock } from '@sage/xtrem-stock-data-api';
import { StockInquiries } from '@sage/xtrem-stock-data/build/lib/menu-items/stock-inquiries';
import * as ui from '@sage/xtrem-ui';

@ui.decorators.page<StockDetailedInquiry>({
    menuItem: StockInquiries,
    priority: 105,
    title: 'Stock detailed inquiry',
    module: 'inventory',
    mode: 'default',
    node: '@sage/xtrem-stock-data/Stock',
    access: { node: '@sage/xtrem-stock-data/Stock', bind: '$read' },
    navigationPanel: {
        onSelect() {
            // We have to prevent selecting a line to avoid opening the "normal" page
            // (which is not even defined).
            return true;
        },
        listItem: {
            // In a nav. panel/main list the title field is mandatory. So, binding the _id was the easiest way to do it.
            title: ui.nestedFields.text({
                bind: '_id',
                isHidden: true,
            }),
            siteName: ui.nestedFields.reference({
                bind: 'site',
                title: 'Site',
                node: '@sage/xtrem-system/Site',
                tunnelPage: '@sage/xtrem-master-data/Site',
                valueField: 'name',
            }),
            siteId: ui.nestedFields.text({
                bind: { site: { id: true } },
                title: 'Site ID',
                isHiddenOnMainField: true,
            }),
            item: ui.nestedFields.reference({
                bind: 'item',
                title: 'Item',
                node: '@sage/xtrem-master-data/Item',
                tunnelPage: '@sage/xtrem-master-data/Item',
                valueField: 'name',
            }),
            itemId: ui.nestedFields.text({
                bind: { item: { id: true } },
                title: 'Item ID',
            }),
            itemDescription: ui.nestedFields.text({
                bind: { item: { description: true } },
                title: 'Item description',
                isHiddenOnMainField: true,
            }),
            isItemSerialNumberManaged: ui.nestedFields.technical({ bind: { item: { serialNumberManagement: true } } }),
            inTransitQuantityInStockUnit: ui.nestedFields.numeric({
                bind: 'inTransitQuantityInStockUnit',
                title: 'In-transit quantity',
                scale(_rowId, rowData) {
                    return rowData?.stockUnit?.decimalDigits ?? 0;
                },
                postfix(_rowId, rowData) {
                    return rowData?.stockUnit?.symbol ?? '';
                },
                groupAggregationMethod: 'sum',
            }),
            onHandQuantityInStockUnit: ui.nestedFields.numeric({
                bind: 'onHandQuantityInStockUnit',
                title: 'On-hand quantity',
                scale(_rowId, rowData) {
                    return rowData?.stockUnit?.decimalDigits ?? 0;
                },
                postfix(_rowId, rowData) {
                    return rowData?.stockUnit?.symbol ?? '';
                },
                groupAggregationMethod: 'sum',
            }),
            availableQuantity: ui.nestedFields.numeric({
                bind: 'availableQuantityInStockUnit',
                title: 'Available quantity',
                scale(_rowId, rowData) {
                    return rowData?.stockUnit?.decimalDigits ?? 0;
                },
                postfix(_rowId, rowData) {
                    return rowData?.stockUnit?.symbol ?? '';
                },
                groupAggregationMethod: 'sum',
            }),
            allocatedQuantity: ui.nestedFields.numeric({
                bind: 'allocatedQuantityInStockUnit',
                title: 'Allocated quantity',
                scale(_rowId, rowData) {
                    return rowData?.stockUnit?.decimalDigits ?? 0;
                },
                postfix(_rowId, rowData) {
                    return rowData?.stockUnit?.symbol ?? '';
                },
                groupAggregationMethod: 'sum',
            }),
            stockUnit: ui.nestedFields.reference({
                bind: 'stockUnit',
                title: 'Unit of measure',
                node: '@sage/xtrem-master-data/UnitOfMeasure',
                tunnelPage: '@sage/xtrem-master-data/UnitOfMeasure',
                columns: [
                    ui.nestedFields.technical({ bind: 'symbol' }),
                    ui.nestedFields.technical({ bind: 'decimalDigits' }),
                ],
                isHiddenOnMainField: true,
            }),
            status: ui.nestedFields.reference({
                bind: 'status',
                title: 'Quality control',
                node: '@sage/xtrem-stock-data/StockStatus',
                tunnelPage: '@sage/xtrem-stock-data/StockStatus',
                valueField: 'name',
            }),
            locationName: ui.nestedFields.reference({
                bind: 'location',
                title: 'Location',
                node: '@sage/xtrem-master-data/Location',
                tunnelPage: '@sage/xtrem-master-data/Location',
                valueField: 'name',
            }),
            locationId: ui.nestedFields.text({
                bind: { location: { id: true } },
                title: 'Location ID',
                isHiddenOnMainField: true,
            }),
            locationZone: ui.nestedFields.text({
                bind: { location: { locationZone: { name: true } } },
                title: 'Location zone',
                isHiddenOnMainField: true,
            }),
            lot: ui.nestedFields.text({
                bind: { lot: { id: true } },
                title: 'Lot',
            }),
            sublot: ui.nestedFields.text({
                bind: { lot: { sublot: true } },
                title: 'Sublot',
                isHiddenOnMainField: true,
            }),
            supplierLot: ui.nestedFields.text({
                bind: { lot: { supplierLot: true } },
                title: 'Supplier lot number',
            }),
            creationDate: ui.nestedFields.date({
                bind: { lot: { creationDate: true } },
                title: 'Creation date',
                isHiddenOnMainField: true,
            }),
            expirationDate: ui.nestedFields.date({
                bind: { lot: { expirationDate: true } },
                title: 'Expiration date',
                isHiddenOnMainField: true,
            }),
            owner: ui.nestedFields.text({
                bind: 'owner',
                title: 'Owner',
                isHiddenOnMainField: true,
                isHidden: true, // ... for now.
            }),
        },
        dropdownActions: [
            {
                title: 'Allocations',
                icon: 'document_right_align',
                isHidden(_recordId: string, rowData: ui.PartialNodeWithId<Stock>) {
                    return !rowData.allocatedQuantityInStockUnit;
                },
                onClick(_recordId: string, rowData: ui.PartialNodeWithId<Stock>) {
                    const queryParameters = {
                        [ui.QUERY_PARAM_CUSTOM_MAIN_LIST_FILTER]: JSON.stringify({ stockRecord: { _id: rowData._id } }),
                    };
                    return this.$.dialog.page('@sage/xtrem-stock/StockAllocationInquiry', queryParameters, {
                        isMainListDisplayedInDialog: true,
                        fullScreen: true,
                    });
                },
            },
            {
                title: 'Serial number information',
                icon: 'draft',
                isHidden(_recordId: string, rowData: ui.PartialNodeWithId<Stock>) {
                    return rowData.item?.serialNumberManagement !== 'managed';
                },
                onClick(_recordId: string, rowData: ui.PartialNodeWithId<Stock>) {
                    const queryParameters = {
                        [ui.QUERY_PARAM_CUSTOM_MAIN_LIST_FILTER]: JSON.stringify({ stockRecord: { _id: rowData._id } }),
                    };
                    return this.$.dialog.page('@sage/xtrem-stock/SerialNumberInquiry', queryParameters, {
                        isMainListDisplayedInDialog: true,
                        fullScreen: true,
                    });
                },
            },
        ],
        orderBy: { _id: -1 },
        optionsMenu: [
            {
                title: 'Main',
                graphQLFilter: {
                    isInTransit: false,
                },
            },
            {
                title: 'Including in-transit',
                graphQLFilter: {},
            },
        ],
    },
})
export class StockDetailedInquiry extends ui.Page<GraphApi> {}
