import type { Filter } from '@sage/xtrem-client';
import { extractEdges } from '@sage/xtrem-client';
import { DateValue } from '@sage/xtrem-date-time';
import type { CostValuationMethod, Item, UnitOfMeasure } from '@sage/xtrem-master-data-api';
import { setReferenceIfSingleValue } from '@sage/xtrem-master-data/build/lib/client-functions/get-default';
import type { GraphApi } from '@sage/xtrem-stock-api';
import type { FifoValuationTier, FifoValuationTierBinding } from '@sage/xtrem-stock-data-api';
import { StockInquiries } from '@sage/xtrem-stock-data/build/lib/menu-items/stock-inquiries';
import type { Company, Site } from '@sage/xtrem-system-api';
import * as ui from '@sage/xtrem-ui';

@ui.decorators.page<StockFifoValuationInquiry>({
    menuItem: StockInquiries,
    priority: 250,
    title: 'FIFO cost tier',
    module: 'stock',
    mode: 'default',
    isTransient: true,
    access: { node: '@sage/xtrem-stock-data/StockJournal' },
    async onLoad() {
        await setReferenceIfSingleValue([this.site]);
    },
})
export class StockFifoValuationInquiry extends ui.Page<GraphApi> {
    @ui.decorators.section<StockFifoValuationInquiry>({
        isOpen: true,
        isTitleHidden: true,
        title: 'General',
    })
    mainSection: ui.containers.Section;

    @ui.decorators.block<StockFifoValuationInquiry>({
        parent() {
            return this.mainSection;
        },
        width: 'extra-large',
        title: 'Criteria',
    })
    criteriaBlock: ui.containers.Block;

    @ui.decorators.referenceField<StockFifoValuationInquiry, Site>({
        parent() {
            return this.criteriaBlock;
        },
        title: 'Site',
        node: '@sage/xtrem-system/Site',
        lookupDialogTitle: 'Select site',
        minLookupCharacters: 0,
        valueField: 'name',
        helperTextField: 'id',
        isMandatory: true,
        columns: [
            ui.nestedFields.text({ title: 'Name', bind: 'name' }),
            ui.nestedFields.text({ title: 'ID', bind: 'id' }),
            ui.nestedFields.checkbox({ bind: 'isInventory', isHidden: true }),
            ui.nestedFields.reference<StockFifoValuationInquiry, Site, Company>({
                bind: 'legalCompany',
                node: '@sage/xtrem-system/Company',
                tunnelPage: '@sage/xtrem-master-data/Company',
                valueField: 'id',
                columns: [
                    ui.nestedFields.reference({
                        bind: 'currency',
                        node: '@sage/xtrem-master-data/Currency',
                        valueField: 'id',
                        columns: [
                            ui.nestedFields.technical({ bind: 'id' }),
                            ui.nestedFields.technical({ bind: 'name' }),
                            ui.nestedFields.technical({ bind: 'symbol' }),
                            ui.nestedFields.technical({ bind: 'decimalDigits' }),
                        ],
                    }),
                ],
            }),
        ],
        placeholder: 'Select site',
        width: 'small',
        filter() {
            return { isInventory: true };
        },
    })
    site: ui.fields.Reference<Site>;

    @ui.decorators.referenceField<StockFifoValuationInquiry, Item>({
        parent() {
            return this.criteriaBlock;
        },
        title: 'Item',
        shouldSuggestionsIncludeColumns: true,
        node: '@sage/xtrem-master-data/Item',
        tunnelPage: '@sage/xtrem-master-data/Item',
        lookupDialogTitle: 'Select item',
        valueField: 'name',
        helperTextField: 'id',
        isMandatory: true,
        columns: [
            ui.nestedFields.text({ bind: 'name' }),
            ui.nestedFields.text({ bind: 'id' }),
            ui.nestedFields.text({ bind: 'description' }),
            ui.nestedFields.text({ title: 'Category', bind: { category: { name: true } } }),
            ui.nestedFields.technical({ bind: 'isStockManaged' }),
            ui.nestedFields.reference<StockFifoValuationInquiry, Item, UnitOfMeasure>({
                bind: 'stockUnit',
                node: '@sage/xtrem-master-data/UnitOfMeasure',
                valueField: 'symbol',
                isHidden: true,
                columns: [
                    ui.nestedFields.technical({ bind: 'id' }),
                    ui.nestedFields.technical({ bind: 'symbol' }),
                    ui.nestedFields.technical({ bind: 'decimalDigits' }),
                ],
            }),
        ],
        placeholder: 'Select item',
        width: 'small',
        filter() {
            return this.getItemFilter();
        },
        orderBy: {
            name: +1,
            id: +1,
        },
    })
    item: ui.fields.Reference<Item>;

    @ui.decorators.dateField<StockFifoValuationInquiry>({
        parent() {
            return this.criteriaBlock;
        },
        title: 'From date',
        width: 'small',
        maxDate: DateValue.today().toString(),
        onChange() {
            if (this.fromDate.value && this.toDate.value && this.toDate.value < this.fromDate.value) {
                this.toDate.value = this.fromDate.value;
            }
        },
    })
    fromDate: ui.fields.Date;

    @ui.decorators.dateField<StockFifoValuationInquiry>({
        parent() {
            return this.criteriaBlock;
        },
        title: 'To date',
        width: 'small',
        minDate() {
            return this.fromDate.value ?? undefined;
        },
    })
    toDate: ui.fields.Date;

    @ui.decorators.buttonField<StockFifoValuationInquiry>({
        parent() {
            return this.criteriaBlock;
        },
        map() {
            return ui.localize('@sage/xtrem-stock/search', 'Search');
        },
        width: 'small',
        async onClick() {
            const validation = await this.criteriaBlock.validate();
            if (validation.length) return;

            this.$.loader.isHidden = false;
            await this.search();
            this.$.loader.isHidden = true;
        },
    })
    searchButton: ui.fields.Button;

    @ui.decorators.tableField<StockFifoValuationInquiry, FifoValuationTierBinding>({
        isHelperTextHidden: true,
        isReadOnly: true,
        canSelect: false,
        canExport: true,
        title: 'Results',
        parent() {
            return this.mainSection;
        },
        columns: [
            ui.nestedFields.date({
                bind: 'effectiveDate',
                title: 'Receipt date',
            }),
            ui.nestedFields.numeric({
                bind: 'sequence',
                title: 'Sequence',
            }),
            ui.nestedFields.date({
                bind: 'createDate',
                title: 'Date created',
            }),
            ui.nestedFields.text({
                isTransient: true,
                isFullWidth: true,
                bind: { receiptDocumentLine: { documentNumber: true } },
                title: 'Receipt document',
            }),
            ui.nestedFields.numeric({
                bind: 'receiptQuantity',
                title: 'Original quantity',
                unit() {
                    return this.item.value?.stockUnit;
                },
                unitMode: 'unitOfMeasure',
                scale: null,
            }),
            ui.nestedFields.numeric<StockFifoValuationInquiry, FifoValuationTierBinding>({
                bind: 'remainingQuantity',
                title: 'Quantity',
                unit() {
                    return this.item.value?.stockUnit;
                },
                unitMode: 'unitOfMeasure',
                scale: null,
            }),

            ui.nestedFields.numeric({
                bind: 'unitCost',
                title: 'Unit cost',
                scale: 4,
                unit() {
                    return this.site.value?.legalCompany?.currency;
                },
            }),
            ui.nestedFields.numeric({
                bind: 'amount',
                title: 'Amount',
                unit() {
                    return this.site.value?.legalCompany?.currency;
                },
                scale: null,
            }),
            ui.nestedFields.numeric({
                bind: 'nonAbsorbedAmount',
                title: 'Non absorbed amount',
                unit() {
                    return this.site.value?.legalCompany?.currency;
                },
                scale: null,
            }),
        ],
    })
    fifoValuationTiers: ui.fields.Table<FifoValuationTierBinding>;

    async search() {
        const filter: Filter<FifoValuationTier> = {};

        filter._and = new Array<{}>({
            remainingQuantity: {
                _gt: 0,
            },
        });
        if (this.site.value) {
            filter._and.push({ site: this.site.value._id });
        }
        if (this.item.value) {
            filter._and.push({ item: this.item.value._id });
        }
        if (this.fromDate.value) {
            filter._and.push({
                effectiveDate: {
                    _gte: this.fromDate.value,
                },
            });
        }
        if (this.toDate.value) {
            filter._and.push({
                effectiveDate: {
                    _lte: this.toDate.value,
                },
            });
        }

        this.fifoValuationTiers.value = extractEdges(
            await this.$.graph
                .node('@sage/xtrem-stock-data/FifoValuationTier')
                .query(
                    ui.queryUtils.edgesSelector(
                        {
                            effectiveDate: true,
                            sequence: true,
                            createDate: true,
                            receiptDocumentLine: { documentId: true, documentNumber: true },
                            receiptQuantity: true,
                            remainingQuantity: true,
                            unitCost: true,
                            amount: true,
                            nonAbsorbedAmount: true,
                        },
                        {
                            first: 500,
                            filter,
                            orderBy: { effectiveDate: 1, sequence: 1 },
                        },
                    ),
                )
                .execute(),
        ) as ui.PartialNodeWithId<FifoValuationTierBinding>[];

        this.$.setPageClean();
    }

    getItemFilter(): Filter<Item> {
        if (this.site.value) {
            return {
                isStockManaged: true,
                itemSites: {
                    _atLeast: 1,
                    site: this.site.value._id,
                    valuationMethod: 'fifoCost' as CostValuationMethod,
                },
            };
        }
        return { isStockManaged: true };
    }
}
