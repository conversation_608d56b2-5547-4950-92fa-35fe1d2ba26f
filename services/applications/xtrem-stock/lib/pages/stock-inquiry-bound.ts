import * as MasterDataUtils from '@sage/xtrem-master-data/build/lib/client-functions/utils';
import type { GraphApi } from '@sage/xtrem-stock-data-api';
import { StockInquiries } from '@sage/xtrem-stock-data/build/lib/menu-items/stock-inquiries';
import * as ui from '@sage/xtrem-ui';

@ui.decorators.page<StockInquiryBound>({
    menuItem: StockInquiries,
    priority: 50,
    title: 'Stock inquiry',
    module: 'inventory',
    mode: 'default',
    node: '@sage/xtrem-master-data/ItemSite',
    access: { node: '@sage/xtrem-master-data/ItemSite', bind: '$read' },
    navigationPanel: {
        onSelect() {
            return true;
        },
        listItem: {
            image: ui.nestedFields.image({
                bind: { item: { image: true } },
                title: 'Image',
            }),
            itemName: ui.nestedFields.reference({
                bind: 'item',
                title: 'Item',
                node: '@sage/xtrem-master-data/Item',
                valueField: 'name',
            }),
            itemIdReference: ui.nestedFields.reference({
                bind: 'item',
                title: 'Item ID',
                node: '@sage/xtrem-master-data/Item',
                valueField: 'id',
            }),
            itemDescription: ui.nestedFields.text({
                bind: { item: { description: true } },
                title: 'Item description',
                isHiddenOnMainField: true,
            }),
            itemCategory: ui.nestedFields.reference({
                bind: { item: { category: true } },
                title: 'Category',
                node: '@sage/xtrem-master-data/ItemCategory',
                valueField: 'id',
            }),
            site: ui.nestedFields.reference({
                bind: 'site',
                title: 'Site',
                node: '@sage/xtrem-system/Site',
                valueField: 'name',
            }),
            inStock: ui.nestedFields.numeric({
                bind: 'inStockQuantity',
                title: 'Quantity on hand',
                canFilter: false,
                scale(_val, rowData) {
                    return MasterDataUtils.getScaleValue(2, rowData?.item?.stockUnit?.decimalDigits);
                },
                postfix(_val, rowData) {
                    return rowData?.item?.stockUnit?.symbol || '';
                },
            }),
            allocated: ui.nestedFields.numeric({
                bind: 'allocatedQuantity',
                title: 'Allocated quantity',
                canFilter: false,
                scale(_val, rowData) {
                    return MasterDataUtils.getScaleValue(2, rowData?.item?.stockUnit?.decimalDigits);
                },
                postfix(_val, rowData) {
                    return rowData?.item?.stockUnit?.symbol || '';
                },
            }),
            accepted: ui.nestedFields.numeric({
                bind: 'acceptedStockQuantity',
                title: 'Accepted quantity',
                canFilter: false,
                scale(_val, rowData) {
                    return MasterDataUtils.getScaleValue(2, rowData?.item?.stockUnit?.decimalDigits);
                },
                postfix(_val, rowData) {
                    return rowData?.item?.stockUnit?.symbol || '';
                },
            }),
            rejected: ui.nestedFields.numeric({
                bind: 'rejectedStockQuantity',
                title: 'Rejected quantity',
                canFilter: false,
                scale(_val, rowData) {
                    return MasterDataUtils.getScaleValue(2, rowData?.item?.stockUnit?.decimalDigits);
                },
                postfix(_val, rowData) {
                    return rowData?.item?.stockUnit?.symbol || '';
                },
            }),
            qualityQuntity: ui.nestedFields.numeric({
                bind: 'onQualityControlStockQuantity',
                title: 'Quantity in quality control',
                canFilter: false,
                scale(_val, rowData) {
                    return MasterDataUtils.getScaleValue(2, rowData?.item?.stockUnit?.decimalDigits);
                },
                postfix(_val, rowData) {
                    return rowData?.item?.stockUnit?.symbol || '';
                },
            }),
            stockValue: ui.nestedFields.numeric({
                bind: 'stockValue',
                title: 'Stock value',
                canFilter: false,
                scale(_val, rowData) {
                    return MasterDataUtils.getScaleValue(2, rowData?.site?.currency?.decimalDigits);
                },
                prefix(_val, rowData) {
                    return rowData?.site?.currency?.symbol || '';
                },
            }),
            unit: ui.nestedFields.reference({
                bind: { item: { stockUnit: true } },
                node: '@sage/xtrem-master-data/UnitOfMeasure',
                tunnelPage: '@sage/xtrem-master-data/UnitOfMeasure',
                valueField: 'name',
                isHiddenOnMainField: true,
                columns: [
                    ui.nestedFields.technical({ bind: 'name' }),
                    ui.nestedFields.technical({ bind: 'symbol' }),
                    ui.nestedFields.technical({ bind: 'decimalDigits' }),
                ],
            }),
            currency: ui.nestedFields.reference({
                bind: { site: { currency: true } },
                node: '@sage/xtrem-master-data/Currency',
                valueField: 'name',
                isHiddenOnMainField: true,
                columns: [
                    ui.nestedFields.technical({ bind: 'name' }),
                    ui.nestedFields.technical({ bind: 'symbol' }),
                    ui.nestedFields.technical({ bind: 'decimalDigits' }),
                ],
            }),
            title: ui.nestedFields.text({ bind: { item: { id: true } }, title: 'Name', isHidden: true }),
        },
    },
})
export class StockInquiryBound extends ui.Page<GraphApi> {}
