import type { Filter } from '@sage/xtrem-client';
import { extractEdges } from '@sage/xtrem-client';
import type { Item } from '@sage/xtrem-master-data-api';
import type { GraphApi, Stock, StockStatus } from '@sage/xtrem-stock-data-api';
import type { Site } from '@sage/xtrem-system-api';
import * as ui from '@sage/xtrem-ui';

@ui.decorators.page<StockInquiry>({
    // Remove menuItem so not available in production
    // menuItem: StockInquiries,
    priority: 50,
    title: 'Stock inquiry',
    module: 'inventory',
    mode: 'tabs',
    isTransient: true,
    access: { node: '@sage/xtrem-stock-data/Stock' },
    headerSection() {
        return this.mainSection;
    },
})
export class StockInquiry extends ui.Page<GraphApi> {
    @ui.decorators.section<StockInquiry>({
        isOpen: true,
        isTitleHidden: true,
        title: 'General',
    })
    mainSection: ui.containers.Section;

    @ui.decorators.block<StockInquiry>({
        parent() {
            return this.mainSection;
        },
        width: 'extra-large',
        title: 'Criteria',
    })
    criteriaBlock: ui.containers.Block;

    @ui.decorators.referenceField<StockInquiry>({
        parent() {
            return this.criteriaBlock;
        },
        title: 'Site',
        node: '@sage/xtrem-system/Site',
        lookupDialogTitle: 'Select site',
        minLookupCharacters: 0,
        valueField: 'name',
        helperTextField: 'id',
        columns: [
            ui.nestedFields.text({ title: 'Name', bind: 'name' }),
            ui.nestedFields.text({ title: 'ID', bind: 'id' }),
        ],
        placeholder: 'Select site',
        width: 'small',
    })
    site: ui.fields.Reference<Site>;

    /** for mobile only  */
    @ui.decorators.referenceField<StockInquiry>({
        parent() {
            return this.criteriaBlock;
        },
        isHiddenDesktop: true,
        title: 'Item',
        lookupDialogTitle: 'Select item',
        node: '@sage/xtrem-master-data/Item',
        tunnelPage: '@sage/xtrem-master-data/Item',
        valueField: 'name',
        helperTextField: 'id',
        width: 'small',
        columns: [
            ui.nestedFields.text({ bind: 'name' }),
            ui.nestedFields.text({ bind: 'id' }),
            ui.nestedFields.text({ bind: 'description' }),
            ui.nestedFields.text({ title: 'Category', bind: { category: { name: true } } }),
        ],
    })
    item: ui.fields.Reference<Item>;

    @ui.decorators.referenceField<StockInquiry>({
        parent() {
            return this.criteriaBlock;
        },
        isHiddenMobile: true,
        title: 'From',
        lookupDialogTitle: 'Select item',
        node: '@sage/xtrem-master-data/Item',
        tunnelPage: '@sage/xtrem-master-data/Item',
        valueField: 'name',
        helperTextField: 'id',
        placeholder: 'Items from...',
        width: 'small',
        columns: [
            ui.nestedFields.text({ title: 'Name', bind: 'name' }),
            ui.nestedFields.text({ title: 'Item', bind: 'id' }),
            ui.nestedFields.text({ title: 'Description', bind: 'description' }),
            ui.nestedFields.text({ title: 'Category', bind: { category: { name: true } } }),
        ],
    })
    itemFrom: ui.fields.Reference<Item>;

    @ui.decorators.referenceField<StockInquiry>({
        parent() {
            return this.criteriaBlock;
        },
        isHiddenMobile: true,
        title: 'To',
        lookupDialogTitle: 'Select item',
        node: '@sage/xtrem-master-data/Item',
        tunnelPage: '@sage/xtrem-master-data/Item',
        valueField: 'name',
        helperTextField: 'id',
        placeholder: 'Items to...',
        width: 'small',
        columns: [
            ui.nestedFields.text({ title: 'Name', bind: 'name' }),
            ui.nestedFields.text({ title: 'Item', bind: 'id' }),
            ui.nestedFields.text({ title: 'Description', bind: 'description' }),
            ui.nestedFields.text({ title: 'Category', bind: { category: { name: true } } }),
        ],
    })
    itemTo: ui.fields.Reference<Item>;

    @ui.decorators.referenceField<StockInquiry>({
        parent() {
            return this.criteriaBlock;
        },
        title: 'Stock status',
        node: '@sage/xtrem-stock-data/StockStatus',
        tunnelPage: '@sage/xtrem-stock-data/StockStatus',
        lookupDialogTitle: 'Select status',
        valueField: 'name',
        helperTextField: 'id',
        columns: [
            ui.nestedFields.text({ title: 'Name', bind: 'name' }),
            ui.nestedFields.text({ title: 'ID', bind: 'id' }),
            ui.nestedFields.text({ bind: 'description' }),
        ],
        minLookupCharacters: 0,
        placeholder: 'Select stock status',
        width: 'small',
    })
    stockStatus: ui.fields.Reference<StockStatus>;

    @ui.decorators.buttonField<StockInquiry>({
        parent() {
            return this.criteriaBlock;
        },
        map() {
            return ui.localize('@sage/xtrem-stock/pages__stock_inquiry__search', 'Search');
        },
        width: 'small',
        async onClick() {
            await this.search();
        },
    })
    searchButton: ui.fields.Button;

    @ui.decorators.section({
        title: 'Results',
    })
    resultsSection: ui.containers.Section;

    @ui.decorators.tableField<StockInquiry>({
        title: 'Results',
        isTitleHidden: true,
        node: '@sage/xtrem-stock-data/Stock',
        canUserHideColumns: false,
        canSelect: false,
        isHelperTextHidden: true,
        isTransient: true,
        canExport: true,
        parent() {
            return this.resultsSection;
        },
        mobileCard: {
            title: ui.nestedFields.reference({
                bind: 'item',
                title: 'Name',
                node: '@sage/xtrem-master-data/Item',
                valueField: 'id',
                isReadOnly: true,
            }),
            titleRight: ui.nestedFields.numeric({
                bind: 'activeQuantityInStockUnit',
                title: 'Quantity',
                scale: 2,
                isReadOnly: true,
            }),
            line2: ui.nestedFields.reference({
                bind: 'item',
                title: 'Name',
                node: '@sage/xtrem-master-data/Item',
                valueField: 'name',
                isReadOnly: true,
            }),
            line2Right: ui.nestedFields.reference({
                bind: 'stockUnit',
                title: 'Unit of measure',
                node: '@sage/xtrem-master-data/UnitOfMeasure',
                tunnelPage: '@sage/xtrem-master-data/UnitOfMeasure',
                valueField: 'name',
                isReadOnly: true,
            }),
        },
        columns: [
            ui.nestedFields.reference({
                bind: 'item',
                title: 'Item',
                node: '@sage/xtrem-master-data/Item',
                valueField: 'id',
                isReadOnly: true,
            }),
            ui.nestedFields.reference({
                bind: 'item',
                title: 'Name',
                node: '@sage/xtrem-master-data/Item',
                valueField: 'name',
                isReadOnly: true,
            }),
            ui.nestedFields.reference({
                bind: 'site',
                title: 'Site',
                node: '@sage/xtrem-system/Site',
                isReadOnly: true,
                valueField: 'name',
            }),
            ui.nestedFields.numeric({
                bind: 'activeQuantityInStockUnit',
                title: 'Quantity',
                scale: 2,
                isReadOnly: true,
            }),
            ui.nestedFields.reference({
                bind: 'stockUnit',
                title: 'Unit of measure',
                node: '@sage/xtrem-master-data/UnitOfMeasure',
                tunnelPage: '@sage/xtrem-master-data/UnitOfMeasure',
                valueField: 'name',
                isReadOnly: true,
            }),
        ],
    })
    stock: ui.fields.Table;

    getFilter(): Filter<Stock> {
        const filter: Filter<Stock> & { _and: Filter<Stock>[] } = { _and: [] };

        if (this.site.value) {
            filter._and.push({ site: this.site.value._id });
        }
        /** Mobile mode */
        if (this.item.value) {
            filter._and.push({
                item: {
                    id: {
                        _eq: this.item.value.id,
                    },
                },
            });
        }
        /** Desktop mode */
        if (this.itemFrom.value) {
            filter._and.push({
                item: {
                    id: {
                        _gte: this.itemFrom.value.id,
                    },
                },
            });
        }
        if (this.itemTo.value) {
            filter._and.push({
                item: {
                    id: {
                        _lte: this.itemTo.value.id,
                    },
                },
            });
        }
        if (this.stockStatus.value) {
            filter._and.push({ status: this.stockStatus.value._id });
        }
        return filter._and.length ? filter : {};
    }

    async search() {
        this.stock.value = [];
        this.$.loader.isHidden = false;

        const results = extractEdges(
            await this.$.graph
                .node('@sage/xtrem-stock-data/Stock')
                .aggregate.query(
                    ui.queryUtils.edgesSelector(
                        {
                            group: {
                                item: {
                                    id: { _by: 'value' },
                                    _id: { _by: 'value' },
                                    name: { _by: 'value' },
                                },
                                site: {
                                    id: { _by: 'value' },
                                    _id: { _by: 'value' },
                                    name: { _by: 'value' },
                                },
                                stockUnit: {
                                    id: { _by: 'value' },
                                    _id: { _by: 'value' },
                                    name: { _by: 'value' },
                                    symbol: { _by: 'value' },
                                },
                            },
                            values: {
                                quantityInStockUnit: {
                                    sum: true,
                                    avg: false,
                                    min: false,
                                    max: false,
                                    distinctCount: false,
                                },
                            },
                        },
                        { filter: this.getFilter() },
                    ),
                )
                .execute()
                .finally(() => {
                    this.$.loader.isHidden = true;
                }),
        );

        this.stock.value = results.map(result => ({
            _id: this.stock.generateRecordId(),
            item: result.group.item,
            site: result.group.site,
            stockUnit: result.group.stockUnit,
            activeQuantityInStockUnit: result.values?.quantityInStockUnit?.sum || 0,
        }));

        this.$.setPageClean();
    }
}
