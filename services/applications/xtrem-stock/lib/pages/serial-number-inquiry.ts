import type { Supplier } from '@sage/xtrem-master-data-api';
import type { GraphApi } from '@sage/xtrem-stock-api';
import type { SerialNumber } from '@sage/xtrem-stock-data-api';
import * as DocumentLink from '@sage/xtrem-stock-data/build/lib/client-functions/document-link';
import { StockInquiries } from '@sage/xtrem-stock-data/build/lib/menu-items/stock-inquiries';
import * as ui from '@sage/xtrem-ui';

@ui.decorators.page<SerialNumberInquiry, SerialNumber>({
    menuItem: StockInquiries,
    priority: 155,
    title: 'Serial number stock inquiry',
    module: 'stock',
    mode: 'default',
    node: '@sage/xtrem-stock-data/SerialNumber',
    access: { node: '@sage/xtrem-stock-data/Stock', bind: '$read' },
    navigationPanel: {
        onSelect() {
            // We have to prevent selecting a line to avoid opening the "normal" page
            // (which is not even defined).
            return true;
        },
        listItem: {
            siteName: ui.nestedFields.reference({
                bind: 'site',
                title: 'Site',
                node: '@sage/xtrem-system/Site',
                tunnelPage: '@sage/xtrem-master-data/Site',
                valueField: 'name',
            }),
            siteId: ui.nestedFields.text({
                bind: { site: { id: true } },
                title: 'Site ID',
                isHiddenOnMainField: true,
            }),
            itemName: ui.nestedFields.reference({
                bind: 'item',
                title: 'Item',
                node: '@sage/xtrem-master-data/Item',
                tunnelPage: '@sage/xtrem-master-data/Item',
                valueField: 'name',
            }),
            itemId: ui.nestedFields.text({
                bind: { item: { id: true } },
                title: 'Item ID',
            }),
            title: ui.nestedFields.text({
                bind: 'id',
                title: 'Serial number',
            }),
            isAllocated: ui.nestedFields.text({
                bind: 'isAllocated',
                title: 'Is allocated',
                isHidden: true, // For now. But as it will be shown in the future, we keep it as a non-technical field.
            }),
            onHandQuantity: ui.nestedFields.numeric({
                bind: 'onHandQuantity',
                title: 'On-hand quantity',
                unit(_id, rowData) {
                    return rowData?.stockRecord?.stockUnit;
                },
                unitMode: 'unitOfMeasure',
                groupAggregationMethod: 'sum',
            }),
            availableQuantity: ui.nestedFields.numeric({
                bind: 'availableQuantity',
                title: 'Available quantity',
                unit(_id, rowData) {
                    return rowData?.stockRecord?.stockUnit;
                },
                unitMode: 'unitOfMeasure',
                groupAggregationMethod: 'sum',
            }),
            allocatedQuantity: ui.nestedFields.numeric({
                bind: 'allocatedQuantity',
                title: 'Allocated quantity',
                unit(_id, rowData) {
                    return rowData?.stockRecord?.stockUnit;
                },
                unitMode: 'unitOfMeasure',
                groupAggregationMethod: 'sum',
            }),
            documentLineType: ui.nestedFields.technical({
                bind: { allocation: { documentLine: { _constructor: true } } },
            }), // Needed for the document link.
            documentLineId: ui.nestedFields.technical({
                bind: { allocation: { documentLine: { documentId: true } } },
            }), // Needed for the document link.
            documentLink: ui.nestedFields.link({
                title: 'Allocated on',
                bind: { allocation: { documentLine: { documentNumber: true } } },
                onClick(_value, rowData) {
                    return this.$.dialog.page(
                        DocumentLink.getDocumentPageName(rowData?.allocation?.documentLine?._constructor || ''),
                        { _id: rowData?.allocation?.documentLine?.documentId || '' },
                        { fullScreen: true, resolveOnCancel: true },
                    );
                },
            }),
            stockUnit: ui.nestedFields.reference({
                bind: { stockRecord: { stockUnit: true } },
                title: 'Unit',
                node: '@sage/xtrem-master-data/UnitOfMeasure',
                tunnelPage: '@sage/xtrem-master-data/UnitOfMeasure',
                columns: [
                    ui.nestedFields.technical({ bind: 'symbol' }),
                    ui.nestedFields.technical({ bind: 'decimalDigits' }),
                ],
                isHiddenOnMainField: true,
            }),
            stockStatus: ui.nestedFields.reference({
                bind: { stockRecord: { status: true } },
                title: 'Quality control',
                node: '@sage/xtrem-stock-data/StockStatus',
                tunnelPage: '@sage/xtrem-stock-data/StockStatus',
                valueField: 'name',
            }),
            stockLocation: ui.nestedFields.reference({
                bind: { stockRecord: { location: true } },
                title: 'Location',
                node: '@sage/xtrem-master-data/Location',
                tunnelPage: '@sage/xtrem-master-data/Location',
                valueField: 'name',
            }),
            locationId: ui.nestedFields.text({
                bind: { stockRecord: { location: { id: true } } },
                title: 'Location ID',
                isHiddenOnMainField: true,
            }),
            locationZone: ui.nestedFields.text({
                bind: { stockRecord: { location: { locationZone: { name: true } } } },
                title: 'Location zone',
                isHiddenOnMainField: true,
            }),
            lot: ui.nestedFields.text({
                bind: { stockRecord: { lot: { id: true } } },
                title: 'Lot',
            }),
            subLot: ui.nestedFields.text({
                bind: { stockRecord: { lot: { sublot: true } } },
                title: 'Sublot',
                isHiddenOnMainField: true,
            }),
            supplierLot: ui.nestedFields.text({
                bind: { stockRecord: { lot: { supplierLot: true } } },
                title: 'Supplier lot number',
                isHiddenOnMainField: true,
            }),
            lotCreationDate: ui.nestedFields.date({
                bind: { stockRecord: { lot: { creationDate: true } } },
                title: 'Lot creation date',
                isHiddenOnMainField: true,
            }),
            lotExpirationDate: ui.nestedFields.date({
                bind: { stockRecord: { lot: { expirationDate: true } } },
                title: 'Lot expiration date',
                isHiddenOnMainField: true,
            }),
            creationDate: ui.nestedFields.date({
                bind: 'createDate',
                title: 'Serial number creation date',
            }),
            supplierName: ui.nestedFields.reference<SerialNumberInquiry, SerialNumber, Supplier>({
                title: 'Supplier',
                node: '@sage/xtrem-master-data/Supplier',
                bind: 'supplier',
                valueField: { businessEntity: { name: true } },
                columns: [
                    ui.nestedFields.text({ title: 'Name', bind: { businessEntity: { name: true } } }),
                    ui.nestedFields.text({ title: 'ID', bind: { businessEntity: { id: true } } }),
                ],
            }),
            supplierId: ui.nestedFields.text({
                title: 'Supplier ID',
                bind: { supplier: { id: true } },
            }),
            owner: ui.nestedFields.date({
                bind: { stockRecord: { owner: true } },
                title: 'Owner',
                isHiddenOnMainField: true,
            }),
        },
        optionsMenu: [{ title: 'All', graphQLFilter: { isInStock: true } }],
        orderBy: { _id: -1 },
    },
})
export class SerialNumberInquiry extends ui.Page<GraphApi> {}
