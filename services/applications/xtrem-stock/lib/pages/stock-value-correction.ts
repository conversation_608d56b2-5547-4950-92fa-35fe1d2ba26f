import { extractEdges } from '@sage/xtrem-client';
import { DateValue } from '@sage/xtrem-date-time';
import * as attributesAndDimensions from '@sage/xtrem-finance-data/build/lib/client-functions/attributes-and-dimensions';
import * as dimensionPanelHelpers from '@sage/xtrem-finance-data/build/lib/client-functions/dimension-panel-helpers';
import type * as financeInterfaces from '@sage/xtrem-finance-data/build/lib/client-functions/interfaces/dimension';
import type {
    BaseDocumentLineBinding,
    Currency,
    Item,
    ReasonCode,
    SequenceNumber,
    UnitOfMeasure,
} from '@sage/xtrem-master-data-api';
import * as utils from '@sage/xtrem-master-data/build/lib/client-functions/utils';
import type { GraphApi, StockReceipt, StockReceiptLine, StockValueCorrectionLineBinding } from '@sage/xtrem-stock-api';
import type { StockDocumentTransactionStatus } from '@sage/xtrem-stock-data-api';
import * as StockPillColor from '@sage/xtrem-stock-data/build/lib/client-functions/pill-color';
import * as StockDocumentHelper from '@sage/xtrem-stock-data/build/lib/client-functions/stock-document-helper';
import * as StockDataUtils from '@sage/xtrem-stock-data/build/lib/client-functions/utils';
import type { Site } from '@sage/xtrem-system-api';
import {
    setApplicativePageCrudActions,
    setOrderOfPageHeaderDropDownActions,
} from '@sage/xtrem-system/build/lib/client-functions/applicative-crud-actions';
import * as ui from '@sage/xtrem-ui';

@ui.decorators.page<StockValueCorrection>({
    // menuItem: stock,
    // priority: 250,
    title: 'Stock value correction',
    objectTypeSingular: 'Stock value correction',
    objectTypePlural: 'Stock value corrections',
    idField() {
        return this.number;
    },
    module: 'stock-data',
    mode: 'default',
    node: '@sage/xtrem-stock/StockValueCorrection',
    navigationPanel: {
        listItem: {
            title: ui.nestedFields.text({ bind: 'number', title: 'Number' }),
            stockSite: ui.nestedFields.reference({
                bind: 'stockSite',
                valueField: 'id',
                node: '@sage/xtrem-system/Site',
                title: 'Site',
                tunnelPage: undefined,
            }),
            effectiveDate: ui.nestedFields.date({ bind: 'effectiveDate', title: 'Date' }),
            reasonCode: ui.nestedFields.reference({
                bind: 'reasonCode',
                title: 'Correction reason',
                tunnelPage: undefined,
            }),
            stockTransactionStatus: ui.nestedFields.label({
                title: 'Stock status',
                optionType: '@sage/xtrem-stock-data/StockDocumentTransactionStatus',
                bind: 'stockTransactionStatus',
                style: (_id, rowData) =>
                    StockPillColor.getLabelColorByStatus(
                        'StockDocumentTransactionStatus',
                        rowData.stockTransactionStatus,
                    ),
            }),
            description: ui.nestedFields.text({
                bind: 'description',
                title: 'Description',
                isHiddenOnMainField: true,
            }),
        },
    },
    onError() {
        // For some reason the loader is displayed, so hide it.
        this.$.loader.isHidden = true;
    },
    async onLoad() {
        setApplicativePageCrudActions({
            page: this,
            isDirty: false,
            save: this.saveStockValueCorrection,
            cancel: this.$standardCancelAction,
            remove: this.$standardDeleteAction,
        });
        this.setDeleteAction();

        if (!this.$.recordId) {
            this.effectiveDate.value = DateValue.today().toString();
        } else if (this.lines.value.length) {
            this._correctedDocumentType.value = null;
            if (this.lines.value.at(0)?.correctedDocumentType === 'stockReceipt') {
                this.correctedDocumentType = 'stockReceipt';
                this._correctedDocumentType.value = this._correctedDocumentType.options?.at(0) ?? null;
                const lineMissingInfo = await this.$.graph
                    .node('@sage/xtrem-stock/StockReceiptLine')
                    .query(
                        ui.queryUtils.edgesSelector(
                            {
                                _id: true,
                                _sortValue: true,
                                document: {
                                    number: true,
                                    stockSite: {
                                        _id: true,
                                    },
                                },
                                orderCost: true,
                            },
                            {
                                filter: {
                                    _id: {
                                        _in: this.lines.value.reduce<string[]>((lines, line) => {
                                            if (line.correctedDocumentLine?._id) {
                                                return lines.concat(line.correctedDocumentLine._id);
                                            }
                                            return lines;
                                        }, []),
                                    },
                                },
                            },
                        ),
                    )
                    .execute();

                if (lineMissingInfo.edges.length) {
                    this.lines.value = this.lines.value.map(line => {
                        const currentCorrectedLine = line.correctedDocumentLine;
                        const queryCorrectedLine = extractEdges(lineMissingInfo).find(
                            line2 => line2._id === currentCorrectedLine?._id,
                        );
                        return {
                            ...line,
                            _correctedStockDocumentLine: {
                                ...currentCorrectedLine,
                                _sortValue: queryCorrectedLine?._sortValue,
                                orderCost: queryCorrectedLine?.orderCost,
                                document: queryCorrectedLine?.document,
                            },
                        };
                    });
                }
                await this.lines.redraw();
            }
        }
        this._defaultDimensionsAttributes = dimensionPanelHelpers.initDefaultDimensions();
    },
    createAction() {
        return this.$standardNewAction;
    },
    headerDropDownActions() {
        return setOrderOfPageHeaderDropDownActions({
            remove: [this.$standardDeleteAction],
            dropDownBusinessActions: [this.$standardOpenRecordHistoryAction],
        });
    },
    businessActions() {
        return [this.$standardCancelAction, this.post, this.saveStockValueCorrection];
    },
    onDirtyStateUpdated(isDirty: boolean) {
        setApplicativePageCrudActions({
            page: this,
            isDirty,
            save: this.saveStockValueCorrection,
            cancel: this.$standardCancelAction,
            remove: this.$standardDeleteAction,
        });
        this.setDeleteAction();
    },
})
export class StockValueCorrection extends ui.Page<GraphApi> implements financeInterfaces.PageWithDefaultDimensions {
    _defaultDimensionsAttributes: financeInterfaces.DefaultDimensions;

    correctedDocumentType: 'stockReceipt' | 'purchaseReceipt' = 'stockReceipt';

    @ui.decorators.section<StockValueCorrection>({
        isOpen: true,
        isTitleHidden: true,
        title: 'General',
    })
    mainSection: ui.containers.Section;

    @ui.decorators.block<StockValueCorrection>({
        parent() {
            return this.mainSection;
        },
        width: 'extra-large',
        isTitleHidden: true,
    })
    mainBlock: ui.containers.Block;

    @ui.decorators.textField<StockValueCorrection>({
        isHidden: true,
    })
    _id: ui.fields.Text;

    @ui.decorators.textField<StockValueCorrection>({
        title: 'Number',
        parent() {
            return this.mainBlock;
        },
        width: 'medium',
        isReadOnly() {
            return this.readOnlyHeaderFields();
        },
    })
    number: ui.fields.Text;

    @ui.decorators.referenceField<StockValueCorrection, Site>({
        parent() {
            return this.mainBlock;
        },
        title: 'Site',
        node: '@sage/xtrem-system/Site',
        lookupDialogTitle: 'Select site',
        minLookupCharacters: 0,
        valueField: 'name',
        helperTextField: 'id',
        columns: [
            ui.nestedFields.text({ title: 'Name', bind: 'name' }),
            ui.nestedFields.text({ title: 'ID', bind: 'id' }),
            ui.nestedFields.checkbox({ bind: 'isLocationManaged', isHidden: true }),
            ui.nestedFields.reference<StockValueCorrection, Site, Currency>({
                bind: 'financialCurrency',
                valueField: 'name',
                node: '@sage/xtrem-master-data/Currency',
                isHidden: true,
                columns: [
                    ui.nestedFields.text({
                        bind: 'symbol',
                    }),
                ],
            }),
            ui.nestedFields.checkbox({ bind: 'isActive', isHidden: true }),
        ],
        filter() {
            return { isActive: { _eq: true } };
        },
        placeholder: 'Select...',
        width: 'medium',
        isMandatory: true,
        isReadOnly() {
            return this.readOnlyHeaderFields();
        },
    })
    stockSite: ui.fields.Reference;

    @ui.decorators.referenceField<StockValueCorrection, ReasonCode>({
        title: 'Correction reason',
        lookupDialogTitle: 'Select reason code',
        minLookupCharacters: 0,
        columns: [
            ui.nestedFields.text({ title: 'Name', bind: 'name' }),
            ui.nestedFields.text({ title: 'ID', bind: 'id' }),
            ui.nestedFields.checkbox({ bind: 'stockValueCorrection', title: 'Value correction' }),
        ],
        width: 'medium',
        isMandatory: true,
        parent() {
            return this.mainBlock;
        },
        isReadOnly() {
            return this.readOnlyHeaderFields();
        },
        onChange() {
            this.description.value = this.reasonCode.value?.name;
        },
    })
    reasonCode: ui.fields.Reference;

    @ui.decorators.dateField<StockValueCorrection>({
        title: 'Date',
        parent() {
            return this.mainBlock;
        },
        isReadOnly() {
            return this.readOnlyHeaderFields();
        },
        width: 'medium',
        isMandatory: true,
        maxDate: DateValue.today().toString(),
    })
    effectiveDate: ui.fields.Date;

    @ui.decorators.textField<StockValueCorrection>({
        title: 'Description',
        parent() {
            return this.mainBlock;
        },
        isReadOnly() {
            return this.readOnlyHeaderFields();
        },
        width: 'medium',
    })
    description: ui.fields.Text;

    @ui.decorators.dropdownListField<StockValueCorrection>({
        title: 'Document type',
        // impossible to use optionType for this field maybe because it's transient
        // optionType: '@sage/xtrem-stock/AdjustableDocumentType',
        // options: ['stockReceipt', 'purchaseReceipt'],
        options() {
            return [ui.localizeEnumMember('@sage/xtrem-stock/AdjustableDocumentType', 'stockReceipt')];
        },
        width: 'medium',
        isMandatory: true,
        isTransientInput: true,
        parent() {
            return this.mainBlock;
        },
        isReadOnly() {
            return this.readOnlyHeaderFields();
        },
        async onChange() {
            if (this._correctedDocumentType.value === this._correctedDocumentType.options?.at(0)) {
                this.correctedDocumentType = 'stockReceipt';
            }
            await this.lines.redraw();
        },
    })
    _correctedDocumentType: ui.fields.DropdownList;

    @ui.decorators.labelField<StockValueCorrection>({
        title: 'Stock status',
        optionType: '@sage/xtrem-stock-data/StockDocumentTransactionStatus',
        parent() {
            return this.mainBlock;
        },
        style() {
            return StockPillColor.getLabelColorByStatus(
                'StockDocumentTransactionStatus',
                this.stockTransactionStatus.value,
            );
        },
        async onClick() {
            await StockDataUtils.onStockTransactionStatusClick(this, this.stockTransactionStatus.value, {
                origin: 'header',
                tableField: this.lines,
            });
        },
    })
    stockTransactionStatus: ui.fields.Label<StockDocumentTransactionStatus>;

    @ui.decorators.tableField<StockValueCorrection, StockValueCorrectionLineBinding>({
        title: 'Document lines',
        bind: 'lines',
        canSelect: false,
        pageSize: 10,
        node: '@sage/xtrem-stock/StockValueCorrectionLine',
        orderBy: {
            _sortValue: +1,
        },
        parent() {
            return this.mainSection;
        },
        columns: [
            ui.nestedFields.label<StockValueCorrection, StockValueCorrectionLineBinding>({
                title: 'Stock status',
                bind: 'stockTransactionStatus',
                optionType: '@sage/xtrem-stock-data/StockDocumentTransactionStatus',
                style: (_id, rowData) =>
                    StockPillColor.getLabelColorByStatus(
                        'StockDocumentTransactionStatus',
                        rowData.stockTransactionStatus,
                    ),
                isHidden() {
                    if (this.stockTransactionStatus.value) {
                        return ['draft', 'completed'].includes(this.stockTransactionStatus.value);
                    }
                    return false;
                },
                async onClick(_id: string, data) {
                    await StockDataUtils.onStockTransactionStatusClick(this, data.stockTransactionStatus, {
                        origin: 'line',
                        _id,
                    });
                },
            }),
            ui.nestedFields.reference<StockValueCorrection, StockValueCorrectionLineBinding, Item>({
                title: 'Item',
                valueField: 'name',
                bind: 'item',
                node: '@sage/xtrem-master-data/Item',
                tunnelPage: '@sage/xtrem-master-data/Item',
                shouldSuggestionsIncludeColumns: true,
                placeholder: 'Select...',
                lookupDialogTitle: 'Select item',
                helperTextField: 'id',
                width: 'medium',
                isMandatory: true,
                columns: [
                    ui.nestedFields.text({ bind: 'name', title: 'Name' }),
                    ui.nestedFields.text({ bind: 'id', title: 'ID' }),
                    ui.nestedFields.text({ bind: 'description' }),
                    ui.nestedFields.text({ title: 'Category', bind: { category: { name: true } } }),
                    ui.nestedFields.technical<StockValueCorrection, Item, UnitOfMeasure>({
                        bind: { stockUnit: { _id: true } },
                    }),
                    ui.nestedFields.technical<StockValueCorrection, Item, UnitOfMeasure>({
                        bind: { stockUnit: { name: true } },
                    }),
                    ui.nestedFields.technical<StockValueCorrection, Item, UnitOfMeasure>({
                        bind: { stockUnit: { symbol: true } },
                    }),
                    ui.nestedFields.technical<StockValueCorrection, Item, UnitOfMeasure>({
                        bind: { stockUnit: { id: true } },
                    }),
                    ui.nestedFields.technical<StockValueCorrection, Item, UnitOfMeasure>({
                        bind: { stockUnit: { decimalDigits: true } },
                    }),
                    ui.nestedFields.technical({ bind: 'lotManagement' }),
                    ui.nestedFields.reference<StockValueCorrection, Item, SequenceNumber>({
                        bind: 'lotSequenceNumber',
                        node: '@sage/xtrem-master-data/SequenceNumber',
                        valueField: 'id',
                        isHidden: true,
                    }),
                    ui.nestedFields.checkbox({ bind: 'isExpiryManaged', isHidden: true }),
                ],
                filter() {
                    const filter = { _and: [{}] };
                    if (this.stockSite.value) {
                        if (
                            this.reasonCode.value &&
                            (this.reasonCode.value.costValueAdjustment || this.reasonCode.value.stockValueAdjustment)
                        ) {
                            filter._and.push({
                                itemSites: {
                                    _atLeast: 1,
                                    site: this.stockSite.value._id,
                                    valuationMethod: 'averageCost',
                                },
                            });
                        } else {
                            filter._and.push({
                                itemSites: { _atLeast: 1, site: this.stockSite.value._id },
                            });
                        }
                    }
                    return filter;
                },
                orderBy: {
                    name: +1,
                    id: +1,
                },
                async onChange(_id: number, rowData: ui.PartialNodeWithId<StockValueCorrectionLineBinding>) {
                    this.lines.setRecordValue({
                        _id: rowData._id,
                        unitCost: (
                            await StockDataUtils.getLineCost(this, {
                                item: rowData.item?._id,
                                site: this.stockSite.value?._id,
                                dateOfValuation: this.effectiveDate.value,
                            })
                        ).orderCost,
                    });
                },
                isReadOnly(rowId: any, rowData: any) {
                    return StockValueCorrection.lineStatusReadonly(rowData.stockTransactionStatus);
                },
            }),
            ui.nestedFields.reference<StockValueCorrection, StockValueCorrectionLineBinding, any>({
                bind: 'correctedDocumentLine',
                node: '@sage/xtrem-master-data/BaseDocumentLine',
                valueField: 'documentNumber',
                minLookupCharacters: 0,
                orderBy: { _id: 1 },
                columns: [
                    ui.nestedFields.text({ bind: 'documentNumber' }),
                    ui.nestedFields.text({ bind: 'documentId' }),
                ],
                isHidden: true,
            }),
            ui.nestedFieldExtensions.reference<StockValueCorrection, StockValueCorrectionLineBinding, StockReceiptLine>(
                {
                    title: 'Document line',
                    bind: '_correctedStockDocumentLine' as any,
                    node: '@sage/xtrem-stock/StockReceiptLine' as any,
                    valueField: { document: { number: true } },
                    minLookupCharacters: 0,
                    isTransient: true,
                    insertBefore: 'unitCost',
                    columns: [
                        ui.nestedFields.reference<StockValueCorrection, StockReceiptLine, StockReceipt>({
                            bind: 'document',
                            valueField: 'number',
                            node: '@sage/xtrem-stock/StockReceipt' as any,
                            title: 'document number',
                            columns: [
                                ui.nestedFields.reference({
                                    bind: 'stockSite',
                                    valueField: '_id',
                                    node: '@sage/xtrem-system/Site',
                                    isHidden: true,
                                }),
                            ],
                        }),
                        ui.nestedFields.numeric({ bind: 'orderCost', title: 'Order cost' }),
                        ui.nestedFields.text({ bind: '_sortValue', title: 'line number' }),
                    ],
                    filter(this: any, rowValue) {
                        return {
                            item: { _id: rowValue.item._id },
                            document: { stockSite: { _id: this.stockSite.value._id } },
                            stockTransactionStatus: 'completed',
                        };
                    },
                    isReadOnly(_rowId, rowData) {
                        return StockValueCorrection.lineStatusReadonly(rowData?.stockTransactionStatus);
                    },
                    isHidden() {
                        return this._correctedDocumentType.value !== this._correctedDocumentType.options?.at(0);
                    },
                    onChange(_id, value) {
                        const line = this.lines.getRecordValue(value._id);
                        if (line) {
                            line.orderCost = value._correctedStockDocumentLine.orderCost;
                            line.correctedDocumentLine = value._correctedStockDocumentLine._id;
                            this.lines.addOrUpdateRecordValue(line);
                        }
                    },
                },
            ),
            ui.nestedFields.numeric({
                title: 'Current cost',
                bind: 'unitCost',
                isReadOnly: true,
                prefix() {
                    return this.costSymbol();
                },
            }),
            ui.nestedFields.numeric({
                title: 'Order cost',
                bind: 'orderCost',
                isReadOnly: true,
                prefix() {
                    return this.costSymbol();
                },
            }),
            ui.nestedFields.numeric({
                title: 'New order cost',
                bind: 'newUnitCost',
                isReadOnly(rowId: any, rowData: any) {
                    return (
                        this.reasonCode.value?.stockValueAdjustment ||
                        StockValueCorrection.lineStatusReadonly(rowData.stockTransactionStatus)
                    );
                },
                prefix() {
                    return this.costSymbol();
                },
            }),
            ui.nestedFields.text({
                isHidden: true,
                bind: 'computedAttributes',
            }),
            ui.nestedFields.text({
                isHidden: true,
                bind: 'storedAttributes',
            }),
            ui.nestedFields.text({
                isHidden: true,
                bind: 'storedDimensions',
            }),
            ui.nestedFields.technical({
                bind: 'correctedDocumentType' as any,
            }),
        ],
        dropdownActions: [
            {
                icon: 'edit',
                title: 'Dimensions',
                async onClick(rowId: any, rowItem: ui.PartialCollectionValue<StockValueCorrectionLineBinding>) {
                    const rowData = dimensionPanelHelpers.fromPartialToBaseDocumentLineWithAnalytical(rowItem);
                    await utils.applyPanelToLineIfChanged(
                        this.lines,
                        dimensionPanelHelpers.editDisplayDimensions(
                            this,
                            {
                                documentLine: rowData,
                            },
                            {
                                editable: !StockValueCorrection.lineStatusReadonly(rowData.stockTransactionStatus),
                            },
                        ),
                    );
                },
            },
            {
                icon: 'bin',
                title: 'Delete',
                isDestructive: true,
                isHidden(_rowID, rowItem) {
                    return StockValueCorrection.lineStatusReadonly(rowItem.stockTransactionStatus);
                },
                onClick(rowID: any) {
                    this.lines.removeRecord(rowID);
                    this.disableHeaderFields(this.lines.value.length);
                },
            },
        ],
        fieldActions() {
            return [this.addAdjustLine, this.defaultDimension];
        },
    })
    lines: ui.fields.Table<StockValueCorrectionLineBinding>;

    getSerializedValues() {
        const values = {
            ...this.$.values,
            lines: [
                ...this.lines.value.map(
                    (
                        line: ui.PartialCollectionValue<
                            StockValueCorrectionLineBinding & {
                                _correctedDocumentLine: BaseDocumentLineBinding;
                            }
                        >,
                    ) => {
                        const documentLineId = line.correctedDocumentLine?._id
                            ? line.correctedDocumentLine._id
                            : (line.correctedDocumentLine as any);
                        return {
                            _id: line._id,
                            item: line.item?._id,
                            correctedDocumentType:
                                this._correctedDocumentType.value === this._correctedDocumentType.options?.at(0)
                                    ? 'stockReceipt'
                                    : 'purchaseReceipt',
                            correctedDocumentLine: documentLineId,
                            newUnitCost: line.newUnitCost,
                            storedAttributes: line.storedAttributes || null,
                            storedDimensions: line.storedDimensions || null,
                        };
                    },
                ),
            ],
        };
        return values;
    }

    @ui.decorators.pageAction<StockValueCorrection>({
        title: 'Save',
        access: {
            bind: '$update',
        },
        async onClick() {
            await this.$standardSaveAction.execute();
            // refresh to redo onLoad event and reset _correctedDocumentLine
            await this.$.router.refresh();
        },
    })
    saveStockValueCorrection: ui.PageAction;

    @ui.decorators.pageAction<StockValueCorrection>({
        title: 'Post',
        isDisabled() {
            return this.$.isDirty || !this._id.value;
        },
        isHidden() {
            if (this.stockTransactionStatus.value) {
                return !['draft', 'error'].includes(this.stockTransactionStatus.value);
            }
            return false;
        },
        async onClick() {
            StockDocumentHelper.catchPostingError(
                await this.$.graph
                    .node('@sage/xtrem-stock/StockValueCorrection')
                    .mutations.postToStock(true, { documentIds: [this.$.recordId ?? ''] })
                    .execute(),
                this,
            );
            await this.$.router.refresh();
        },
    })
    post: ui.PageAction;

    setDeleteAction() {
        if (
            !this.$standardDeleteAction.isDisabled &&
            this.lines.value.some(line => StockValueCorrection.lineStatusReadonly(line.stockTransactionStatus))
        ) {
            this.$standardDeleteAction.isDisabled = true;
        }
    }

    @ui.decorators.pageAction<StockValueCorrection>({
        icon: 'add',
        title: 'Add line',
        isDisabled() {
            return (
                !this.stockSite.value ||
                !this.reasonCode.value ||
                (this.stockTransactionStatus.value && this.stockTransactionStatus.value !== 'draft') ||
                !this._correctedDocumentType.value
            );
        },
        onClick() {
            const lineData = dimensionPanelHelpers.applyDefaultDimensionsOnNewLineIfNeeded(
                {} as any,
                this._defaultDimensionsAttributes,
            );
            this.lines.addRecord(lineData);
            this.disableHeaderFields(this.lines.value.length);
        },
    })
    addAdjustLine: ui.PageAction;

    @ui.decorators.pageAction<StockValueCorrection>({
        icon: 'arrow_right',
        title: 'Set default dimensions',
        isDisabled() {
            if (this.$.recordId) {
                return (
                    !dimensionPanelHelpers.isDefaultDimensionActionActive(this.$.recordId, this.$.isDirty) ||
                    (this.lines.value.length > 0 &&
                        this.lines.value.every(line =>
                            StockValueCorrection.lineStatusReadonly(line.stockTransactionStatus),
                        ))
                );
            }
            return false;
        },
        async onClick() {
            const filter = (line: ui.PartialNodeWithId<StockValueCorrectionLineBinding>) =>
                !StockValueCorrection.lineStatusReadonly(line.stockTransactionStatus);
            const defaultedFromItem = await attributesAndDimensions.getAttributesAndDimensionsFromItem({
                page: this,
                dimensionDefinitionLevel: 'stockDirect',
                companyId: Number(this.stockSite?.value?.legalCompany?._id),
            });
            this._defaultDimensionsAttributes = await dimensionPanelHelpers.editAndApplyDefaultDimensionsIfNeeded(
                this,
                this.lines,
                this._defaultDimensionsAttributes,
                filter,
                defaultedFromItem,
            );
        },
    })
    defaultDimension: ui.PageAction;

    private static lineStatusReadonly(stockTransactionStatus: StockDocumentTransactionStatus) {
        return ['inProgress', 'completed'].includes(stockTransactionStatus);
    }

    readOnlyHeaderFields() {
        return (this.lines.value?.length || 0) !== 0;
    }

    disableHeaderFields(enteredLines: number) {
        this.stockSite.isDisabled = enteredLines > 0;
        this.reasonCode.isDisabled = enteredLines > 0;
        this._correctedDocumentType.isDisabled = enteredLines > 0;
    }

    costSymbol() {
        return this.stockSite.value?.financialCurrency.symbol || '';
    }
}
