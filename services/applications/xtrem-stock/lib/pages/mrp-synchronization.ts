import * as MasterDataUtils from '@sage/xtrem-master-data/build/lib/client-functions/utils';
import type { MrpSynchronizationStatus } from '@sage/xtrem-mrp-data-api';
import type { GraphApi } from '@sage/xtrem-stock-api';
import { StockReordering } from '@sage/xtrem-stock-data/build/lib/menu-items/stock-reordering';
import * as ui from '@sage/xtrem-ui';
import * as PillColor from '../client-functions/pill-color';

@ui.decorators.page<MrpSynchronization>({
    menuItem: StockReordering,
    priority: 5,
    title: 'MRP synchronization',
    module: 'stock',
    mode: 'default',
    node: '@sage/xtrem-mrp-data/MrpSynchronization',
    access: { node: '@sage/xtrem-mrp-data/MrpSynchronization', bind: 'requestSynchronization' },
    navigationPanel: {
        listItem: {
            title: ui.nestedFields.select({
                bind: 'type',
                optionType: '@sage/xtrem-mrp-data/MrpSynchronizationType',
            }),
            lastSyncDateTime: ui.nestedFields.relativeDate({
                title: 'Last synchronization',
                bind: 'lastSyncDateTime',
            }),
            titleRight: ui.nestedFields.label({
                title: 'Last synchronization status',
                optionType: '@sage/xtrem-mrp-data/MrpSynchronizationStatus',
                bind: 'syncStatus',
                style: (_id, rowData) => PillColor.getLabelColorByStatus('SynchronizationStatus', rowData.syncStatus),
            }),
        },
    },
    async onLoad() {
        // Note: The following line causes a refresh of the navigation panel. Normally, we would not do that, but
        //       a) the navigation panel is not updated while the page is reloaded and leads to showing different
        //          status on the navigation panel and on the page,
        //       b) the refresh is not too costly for at the moment there is only one record in the database. There
        //          might be more in the future but most likely not more than a handful.
        await this.$.refreshNavigationPanel();
    },
    businessActions() {
        return [this.synchronizeMrp];
    },
})
export class MrpSynchronization extends ui.Page<GraphApi> {
    @ui.decorators.section<MrpSynchronization>({
        isTitleHidden: true,
        title: 'General',
    })
    mainSection: ui.containers.Section;

    @ui.decorators.block<MrpSynchronization>({
        parent() {
            return this.mainSection;
        },
        width: 'extra-large',
        title: 'Criteria',
    })
    criteriaBlock: ui.containers.Block;

    @ui.decorators.textField<MrpSynchronization>({
        title: 'ID',
        isHidden: true,
        isTitleHidden: true,
    })
    _id: ui.fields.Text;

    @ui.decorators.dropdownListField<MrpSynchronization>({
        parent() {
            return this.criteriaBlock;
        },
        title: 'Type',
        width: 'medium',
        isReadOnly: true,
        optionType: '@sage/xtrem-mrp-data/MrpSynchronizationType',
    })
    type: ui.fields.DropdownList;

    @ui.decorators.relativeDateField<MrpSynchronization>({
        parent() {
            return this.criteriaBlock;
        },
        title: 'Last synchronization',
        width: 'medium',
    })
    lastSyncDateTime: ui.fields.RelativeDate;

    @ui.decorators.labelField<MrpSynchronization>({
        title: 'Last synchronization status',
        optionType: '@sage/xtrem-mrp-data/MrpSynchronizationStatus',
        parent() {
            return this.criteriaBlock;
        },
        style() {
            return PillColor.getLabelColorByStatus('SynchronizationStatus', this.syncStatus.value);
        },
    })
    syncStatus: ui.fields.Label<MrpSynchronizationStatus>;

    @ui.decorators.checkboxField<MrpSynchronization>({
        parent() {
            return this.criteriaBlock;
        },
        title: 'Synchronize all records',
        width: 'medium',
        isTransient: true,
    })
    resetSynchronization: ui.fields.Checkbox;

    @ui.decorators.checkboxField<MrpSynchronization>({
        parent() {
            return this.criteriaBlock;
        },
        title: 'Synchronize deletions',
        width: 'medium',
        isTransient: true,
    })
    synchronizeDeletions: ui.fields.Checkbox;

    @ui.decorators.pageAction<MrpSynchronization>({
        title: 'Synchronize',
        onError(error: string | (Error & { errors: Array<any> })) {
            return MasterDataUtils.formatError(this, error);
        },
        isDisabled() {
            return this.syncStatus.value === 'inProgress';
        },
        async onClick() {
            if (this._id.value) {
                // Show as busy.
                this.$.loader.isHidden = false;

                await this.$.graph
                    .node('@sage/xtrem-mrp-data/MrpSynchronization')
                    .asyncOperations.mrpSynchronizeBom.start(
                        { trackingId: true },
                        {
                            resetSynchronization: this.resetSynchronization.value ?? false,
                            synchronizeDeletions: this.synchronizeDeletions.value ?? false,
                        },
                    )
                    .execute();

                // Not busy anymore
                this.$.loader.isHidden = true;

                this.$.showToast(
                    ui.localize(
                        '@sage/xtrem-stock/pages__mrp_synchronization_request__notification_success',
                        'MRP synchronization request sent.',
                    ),
                    { type: 'success' },
                );

                // Need to wait for the asynchronous update before refreshing the page.
                await new Promise<void>(resolve => {
                    setTimeout(resolve, 250);
                });
                // Refresh screen to show the updated status.
                await this.$.router.refresh(true);
                await this.$.refreshNavigationPanel();
            }
        },
        buttonType: 'primary',
    })
    synchronizeMrp: ui.PageAction;
}
