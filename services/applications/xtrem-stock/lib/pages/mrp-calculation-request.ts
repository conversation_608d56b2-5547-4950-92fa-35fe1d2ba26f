import { DateValue } from '@sage/xtrem-date-time';
import type { Item } from '@sage/xtrem-master-data-api';
import * as masterDataUtils from '@sage/xtrem-master-data/build/lib/client-functions/utils';
import { setMultiReferenceIfSingleValue } from '@sage/xtrem-master-data/lib/client-functions/get-default';
import type { MrpCalculation$AsyncOperations, MrpInputSet$Operations } from '@sage/xtrem-mrp-data-api';
import type { GraphApi as SchedulerGraphApi } from '@sage/xtrem-scheduler-api';
import { once, recurring } from '@sage/xtrem-scheduler/build/lib/client-functions/job-execution';
import { scheduleWizard } from '@sage/xtrem-scheduler/build/lib/client-functions/job-schedule';
import type { GraphApi } from '@sage/xtrem-stock-api';
import type { Company, Site } from '@sage/xtrem-system-api';
import * as ui from '@sage/xtrem-ui';

@ui.decorators.page<MrpCalculationRequest>({
    title: 'MRP calculation request',
    module: 'stock',
    mode: 'default',
    isTransient: true,
    access: { node: '@sage/xtrem-mrp-data/MrpCalculation', bind: 'mrpCalculationRequest' },
    businessActions() {
        return [this.cancelRequest, this.scheduleMrpCalculationRequest, this.calculationRequest];
    },
    async onLoad() {
        this.numberOfWeeks.value = 5;
        this.explodeBillOfMaterial.value = false;
        this.isSalesQuoteIncluded.value = false;
        this.startDate.value = DateValue.today().toString();
        await setMultiReferenceIfSingleValue([this.sites]);
    },

    onError(error: string | (Error & { errors: Array<any> })) {
        return masterDataUtils.formatError(this, error);
    },
})
export class MrpCalculationRequest extends ui.Page<GraphApi> {
    @ui.decorators.section<MrpCalculationRequest>({
        isTitleHidden: true,
        title: 'General',
    })
    mainSection: ui.containers.Section;

    @ui.decorators.block<MrpCalculationRequest>({
        parent() {
            return this.mainSection;
        },
        width: 'extra-large',
        title: 'Criteria',
    })
    criteriaBlock: ui.containers.Block;

    @ui.decorators.textField<MrpCalculationRequest>({
        parent() {
            return this.criteriaBlock;
        },
        title: 'Description',
        isMandatory: true,
    })
    description: ui.fields.Text;

    @ui.decorators.multiReferenceField<MrpCalculationRequest>({
        parent() {
            return this.criteriaBlock;
        },
        title: 'Company',
        node: '@sage/xtrem-system/Company',
        lookupDialogTitle: 'Select company',
        minLookupCharacters: 0,
        valueField: 'name',
        helperTextField: 'id',
        columns: [
            ui.nestedFields.text({ title: 'Name', bind: 'name' }),
            ui.nestedFields.text({ title: 'ID', bind: 'id' }),
        ],
        placeholder: 'Select...',
        width: 'small',
        onChange() {
            this.sites.value = [];
            this.sites.isDisabled = !(this.companies.value.length === 0);
        },
    })
    companies: ui.fields.MultiReference<Company>;

    @ui.decorators.multiReferenceField<MrpCalculationRequest>({
        parent() {
            return this.criteriaBlock;
        },
        title: 'Site',
        node: '@sage/xtrem-system/Site',
        lookupDialogTitle: 'Select site',
        minLookupCharacters: 0,
        valueField: 'name',
        helperTextField: 'id',
        columns: [
            ui.nestedFields.text({ title: 'Name', bind: 'name' }),
            ui.nestedFields.text({ title: 'ID', bind: 'id' }),
        ],
        placeholder: 'Select...',
        width: 'small',
        onChange() {
            this.companies.value = [];
        },
    })
    sites: ui.fields.MultiReference<Site>;

    @ui.decorators.referenceField<MrpCalculationRequest, Item>({
        parent() {
            return this.criteriaBlock;
        },
        title: 'From item',
        node: '@sage/xtrem-master-data/Item',
        tunnelPage: '@sage/xtrem-master-data/Item',
        lookupDialogTitle: 'Select item',
        valueField: 'name',
        helperTextField: 'id',
        columns: [
            ui.nestedFields.text({ title: 'Name', bind: 'name' }),
            ui.nestedFields.text({ title: 'ID', bind: 'id' }),
            ui.nestedFields.text({ title: 'Description', bind: 'description' }),
            ui.nestedFields.checkbox({ bind: 'isStockManaged', isHidden: true }),
        ],
        filter() {
            return {
                isStockManaged: true,
                ...(this.$.isServiceOptionEnabled('phantomItemOption') ? { isPhantom: false } : {}),
            };
        },
        orderBy: { id: +1 },
        placeholder: 'Select...',
        width: 'small',
        validation() {
            if (this.itemFrom.value && this.itemTo.value) {
                if (this.itemFrom.value.id && this.itemTo.value.id && this.itemFrom.value.id > this.itemTo.value.id) {
                    return ui.localize(
                        '@sage/xtrem-stock/pages__mrp_calculation_request__invalid_item_from_range',
                        'The from item must not be greater than the to item.',
                    );
                }
            }
            return '';
        },
    })
    itemFrom: ui.fields.Reference<Item>;

    @ui.decorators.referenceField<MrpCalculationRequest, Item>({
        parent() {
            return this.criteriaBlock;
        },
        title: 'To item',
        node: '@sage/xtrem-master-data/Item',
        tunnelPage: '@sage/xtrem-master-data/Item',
        lookupDialogTitle: 'Select item',
        valueField: 'name',
        helperTextField: 'id',
        columns: [
            ui.nestedFields.text({ bind: 'name' }),
            ui.nestedFields.text({ bind: 'id' }),
            ui.nestedFields.text({ bind: 'description' }),
            ui.nestedFields.text({ title: 'Category', bind: { category: { name: true } } }),
            ui.nestedFields.checkbox({ bind: 'isStockManaged', isHidden: true }),
        ],
        filter() {
            return {
                isStockManaged: true,
                ...(this.$.isServiceOptionEnabled('phantomItemOption') ? { isPhantom: false } : {}),
            };
        },
        orderBy: { id: +1 },
        placeholder: 'Select...',
        width: 'small',
        validation() {
            if (this.itemFrom.value && this.itemTo.value) {
                if (this.itemFrom.value.id && this.itemTo.value.id && this.itemFrom.value.id > this.itemTo.value.id) {
                    return ui.localize(
                        '@sage/xtrem-stock/pages__mrp_calculation_request__invalid_item_to_range',
                        'The to item must not be less than the from item.',
                    );
                }
            }
            return '';
        },
    })
    itemTo: ui.fields.Reference<Item>;

    @ui.decorators.dateField<MrpCalculationRequest>({
        parent() {
            return this.criteriaBlock;
        },
        title: 'Start date',
        width: 'small',
        minDate: DateValue.today().toString(),
        isMandatory: true,
    })
    startDate: ui.fields.Date;

    @ui.decorators.numericField<MrpCalculationRequest>({
        isMandatory: true,
        title: 'Period in weeks',
        parent() {
            return this.criteriaBlock;
        },
    })
    numberOfWeeks: ui.fields.Numeric;

    @ui.decorators.checkboxField<MrpCalculationRequest>({
        title: 'Explode bill of material',
        parent() {
            return this.criteriaBlock;
        },
    })
    explodeBillOfMaterial: ui.fields.Checkbox;

    @ui.decorators.checkboxField<MrpCalculationRequest>({
        title: 'Include sales quotes',
        parent() {
            return this.criteriaBlock;
        },
    })
    isSalesQuoteIncluded: ui.fields.Checkbox;

    @ui.decorators.pageAction<MrpCalculationRequest>({
        title: 'Cancel',
        buttonType: 'tertiary',
        isHidden() {
            return !this.$.queryParameters.called;
        },
        onClick() {
            this.$.finish(false);
        },
    })
    cancelRequest: ui.PageAction;

    @ui.decorators.pageAction<MrpCalculationRequest>({
        title() {
            return ui.localize('@sage/xtrem-stock/pages__stock_mrp_calculation__calculate', 'Calculate');
        },
        buttonType: 'primary',
        onError(error: string | (Error & { errors: Array<any> })) {
            return masterDataUtils.formatError(this, error);
        },
        async onClick() {
            const validation = await this.$.page.validate();

            if (validation.length > 0) {
                this.$.showToast(validation.join('\n'), { type: 'error' });
                return;
            }

            if (!(await this.checkSupplyPlanningPurchaseOrderCreated())) {
                return;
            }

            const searchCriteria = this.setupMrpRequestSearchCriteriaObject();
            if (await this.mrpRequestCriteriaCheck(searchCriteria)) {
                this.$.loader.isHidden = false;

                const data: Parameters<MrpCalculation$AsyncOperations['mrpCalculationRequest']['start']>[1]['data'] = {
                    ...searchCriteria,
                    description: this.description.value ?? '',
                    startDate: this.startDate.value ?? '',
                    numberOfWeeks: this.numberOfWeeks.value ?? '',
                    explodeBillOfMaterial: this.explodeBillOfMaterial.value ?? '',
                    isSalesQuoteIncluded: this.isSalesQuoteIncluded.value ?? false,
                };
                await this.$.graph
                    .node('@sage/xtrem-mrp-data/MrpCalculation')
                    .asyncOperations.mrpCalculationRequest.start({ trackingId: true }, { data })
                    .execute();

                this.$.loader.isHidden = true;
                this.$.showToast(
                    ui.localize(
                        '@sage/xtrem-stock/pages__mrp_calculation_request__notification_success',
                        'MRP calculation request sent.',
                    ),
                    { type: 'success' },
                );

                this.$.setPageClean();
                if (this.$.queryParameters.called) {
                    this.$.finish(true);
                }
            }
        },
    })
    calculationRequest: ui.PageAction;

    @ui.decorators.pageAction<MrpCalculationRequest>({
        title: 'Schedule',
        icon: 'clock',
        buttonType: 'tertiary',
        async onClick() {
            const searchCriteria = this.setupMrpRequestSearchCriteriaObject();
            if (await this.mrpRequestCriteriaCheck(searchCriteria)) {
                const data: Parameters<MrpCalculation$AsyncOperations['mrpCalculationRequest']['start']>[1]['data'] = {
                    ...searchCriteria,
                    numberOfWeeks: this.numberOfWeeks.value ?? '',
                    explodeBillOfMaterial: this.explodeBillOfMaterial.value ?? '',
                    isSalesQuoteIncluded: this.isSalesQuoteIncluded.value ?? false,
                    isScheduledTask: true,
                };

                this.$.setPageClean();
                await scheduleWizard(this as unknown as ui.Page<SchedulerGraphApi>, {
                    jobSchedule: [once, recurring],
                    operationKey: 'MrpCalculation|mrpCalculationRequest|start',
                    additionalParameters: {
                        data: JSON.stringify(data),
                    },
                    isParametersHidden: true,
                });
            }
        },
    })
    scheduleMrpCalculationRequest: ui.PageAction;

    setupMrpRequestSearchCriteriaObject(): Parameters<
        MrpInputSet$Operations['queries']['getMrpInputItemSites']
    >[1]['searchCriteria'] {
        const searchCriteria: Parameters<
            MrpInputSet$Operations['queries']['getMrpInputItemSites']
        >[1]['searchCriteria'] = {};
        if (this.itemFrom.value) {
            searchCriteria.fromItem = {
                id: this.itemFrom.value.id,
                name: this.itemFrom.value.name,
            };
        }
        if (this.itemTo.value) {
            searchCriteria.toItem = {
                id: this.itemTo.value.id,
                name: this.itemTo.value.name,
            };
        }
        if (this.companies.value.length) {
            searchCriteria.companies = {
                array: this.companies.value.map(company => ({ id: company.id, name: company.name })),
            };
        }

        if (this.sites.value.length) {
            searchCriteria.sites = {
                array: this.sites.value.map(site => ({
                    id: site.id,
                    name: site.name,
                })),
            };
        }

        return searchCriteria;
    }

    async mrpRequestCriteriaCheck(
        searchCriteria: Parameters<MrpInputSet$Operations['queries']['getMrpInputItemSites']>[1]['searchCriteria'],
    ) {
        if (this.sites.value.length === 0 && this.companies.value.length === 0) {
            this.$.showToast(
                ui.localize(
                    '@sage/xtrem-stock/pages__stock_mrp_calculation_company_sites_error',
                    'Enter at least one company or site.',
                ),
                { type: 'error' },
            );
            return false;
        }

        const record = (await this.$.graph
            .node('@sage/xtrem-mrp-data/MrpInputSet')
            .queries.getMrpInputItemSites(
                { _id: true },
                {
                    searchCriteria: { ...searchCriteria, queryCheck: true },
                },
            )
            .execute()) as [];

        if (record.length) {
            return true;
        }
        this.$.showToast(
            ui.localize(
                '@sage/xtrem-stock/pages__mrp_calculation_request__invalid_criteria',
                'No item site records replenished by MRP for the entered criteria.',
            ),
            { type: 'error' },
        );
        return false;
    }

    async checkSupplyPlanningPurchaseOrderCreated() {
        const isSupplyPlanningWithPurchaseOrders = await this.$.graph.raw(
            `query {
                xtremSupplyChain {
                  supplyPlanning {
                    checkSupplyPlanningPurchaseOrderCreated
                  }
                }
              }`,
        );

        if (
            isSupplyPlanningWithPurchaseOrders.xtremSupplyChain?.supplyPlanning?.checkSupplyPlanningPurchaseOrderCreated
        ) {
            this.$.showToast(
                ui.localize(
                    '@sage/xtrem-stock/pages__mrp_calculation_request__supply_chain_error',
                    'Purchase orders are currently being created from previous MRP calculations. Try again later.',
                ),
                { type: 'error' },
            );
            return false;
        }
        return true;
    }
}
