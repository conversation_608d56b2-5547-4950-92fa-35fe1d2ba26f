import { withoutEdges } from '@sage/xtrem-client';
import { date, DateValue } from '@sage/xtrem-date-time';
import type { BusinessEntity, Item, Supplier } from '@sage/xtrem-master-data-api';
import * as masterDataUtils from '@sage/xtrem-master-data/build/lib/client-functions/utils';
import type {
    GraphApi,
    StockReorderCalculationInputSet,
    StockReorderCalculationResultLine,
} from '@sage/xtrem-stock-api';
import { StockReordering } from '@sage/xtrem-stock-data/build/lib/menu-items/stock-reordering';
import type { Company, Site, User } from '@sage/xtrem-system-api';
import * as ui from '@sage/xtrem-ui';
import * as StockValuationLib from '../client-functions/stock-valuation-lib';

@ui.decorators.page<StockReorderCalculation, StockReorderCalculationInputSet>({
    menuItem: StockReordering,
    priority: 200,
    title: 'Stock reordering',
    module: 'stock',
    mode: 'default',
    node: '@sage/xtrem-stock/StockReorderCalculationInputSet',
    createAction() {
        return this.$standardNewAction;
    },
    businessActions() {
        return [this.runStockReorderCalculation];
    },
    async defaultEntry() {
        // As there is only one record per user try to load this record.
        //
        // NOTE (for local Cucumber script test on local development instances): Please make sure that the user
        // e-mail address in the top field is set to the e-mail address of the user configured in xtrem-config.yml!
        const stockReorderCalculationId = withoutEdges(
            await this.$.graph
                .node('@sage/xtrem-stock/StockReorderCalculationInputSet')
                .query(
                    ui.queryUtils.edgesSelector(
                        { _id: true },
                        { filter: { user: { email: this.$.username } }, first: 1 },
                    ),
                )
                .execute(),
        );

        // If there is such record open it. Otherwise open work with an empty page in creation mode.
        return stockReorderCalculationId.at(0)?._id || '$new';
    },
    async onLoad() {
        if (!this.$.recordId) {
            // In case we have no record for this user pre-set the calculation user field.
            await this.$.fetchDefaults(['user']);
        }
    },
})
export class StockReorderCalculation extends ui.Page<GraphApi> {
    @ui.decorators.pageAction<StockReorderCalculation>({
        title: 'Run',
        isDisabled() {
            return this.status.value === 'inProgress' || (!this.company.value && this.sites.value.length === 0);
        },
        onError(error: string | (Error & { errors: Array<any> })) {
            return masterDataUtils.formatError(this, error);
        },
        async onClick() {
            // Mark the record as 'in progress'.
            //
            // Note: a) This is done in the async mutation, too. But we have to signal the page that
            //          from now on the RUN button should be disabled to prevent another activation.
            //          while we asynchronously calculate the current request.
            //       b) We have to wait for a fraction of a second to prevent flickering.
            this.status.value = 'inProgress';
            await new Promise(resolve => {
                setTimeout(resolve, 0);
            });

            // Before we can do the calculation asynchronously we must save the current record.
            await this.$standardSaveAction.execute(true);

            // Notify the user that a calculation request is on the way.
            this.$.showToast(
                ui.localize(
                    '@sage/xtrem-stock/pages__stock_reorder_calculation_request__notification_request_sent',
                    'Stock reorder calculation request sent.',
                ),
                { type: 'success' },
            );

            // Request calculation in an asynchronous mutation.
            const isSuccess = await this.$.graph
                .node('@sage/xtrem-stock/StockReorderCalculationInputSet')
                .asyncOperations.reorderCalculation.runToCompletion(true, {
                    userId: this.user.value?._id,
                })
                .execute();

            // After processing signal success or error.
            this.$.showToast(
                isSuccess
                    ? ui.localize(
                          '@sage/xtrem-stock/pages__stock_reorder_calculation_request__notification_calculation_finished',
                          'Stock reorder calculation finished.',
                      )
                    : ui.localize(
                          '@sage/xtrem-stock/pages__stock_reorder_calculation_request__notification_calculation_failed',
                          'Stock reorder calculation failed.',
                      ),
                isSuccess ? { type: 'success' } : { type: 'error' },
            );

            await this.$.router.refresh(true);
        },
        buttonType: 'primary',
    })
    runStockReorderCalculation: ui.PageAction;

    @ui.decorators.section<StockReorderCalculation>({
        isOpen: true,
        isTitleHidden: true,
        title: 'General',
    })
    mainSection: ui.containers.Section;

    @ui.decorators.block<StockReorderCalculation>({
        parent() {
            return this.mainSection;
        },
        width: 'extra-large',
        title: 'Criteria',
    })
    criteriaBlock: ui.containers.Block;

    @ui.decorators.referenceField<StockReorderCalculation, User>({
        parent() {
            return this.criteriaBlock;
        },
        title: 'Calculation user',
        isHidden: true,
        fetchesDefaults: true,
    })
    user: ui.fields.Reference<User>;

    @ui.decorators.labelField<StockReorderCalculation>({
        title: 'Status',
        optionType: '@sage/xtrem-stock/StockValuationStatus',
        isHidden: true,
        parent() {
            return this.criteriaBlock;
        },
    })
    status: ui.fields.Label;

    @ui.decorators.referenceField<StockReorderCalculation, Company>({
        parent() {
            return this.criteriaBlock;
        },
        title: 'Company',
        node: '@sage/xtrem-system/Company',
        tunnelPage: '@sage/xtrem-master-data/Company',
        lookupDialogTitle: 'Select company',
        minLookupCharacters: 0,
        placeholder: 'Select...',
        width: 'small',
        valueField: 'name',
        helperTextField: 'id',
        columns: [
            ui.nestedFields.text({ title: 'Name', bind: 'name' }),
            ui.nestedFields.text({ title: 'ID', bind: 'id' }),
        ],
        onChange() {
            if (this.company.value) {
                this.sites.value = [];
            } else {
                this.sites.isDisabled = false;
            }
        },
    })
    company: ui.fields.Reference<Company>;

    @ui.decorators.multiReferenceField<StockReorderCalculation, Site>({
        parent() {
            return this.criteriaBlock;
        },
        title: 'Site',
        node: '@sage/xtrem-system/Site',
        bind: 'sites',
        valueField: 'name',
        lookupDialogTitle: 'Select site',
        minLookupCharacters: 0,
        helperTextField: 'id',
        isDisabled() {
            return !!this.company.value;
        },
        columns: [
            ui.nestedFields.text({ title: 'Name', bind: 'name' }),
            ui.nestedFields.text({ title: 'ID', bind: 'id' }),
        ],
        placeholder: 'Select...',
        width: 'small',
    })
    sites: ui.fields.MultiReference<Site>;

    @ui.decorators.referenceField<StockReorderCalculation, Item>({
        parent() {
            return this.criteriaBlock;
        },
        title: 'From item',
        shouldSuggestionsIncludeColumns: true,
        node: '@sage/xtrem-master-data/Item',
        tunnelPage: '@sage/xtrem-master-data/Item',
        lookupDialogTitle: 'Select item',
        valueField: 'name',
        helperTextField: 'id',
        columns: [
            ui.nestedFields.text({ bind: 'name' }),
            ui.nestedFields.text({ bind: 'id' }),
            ui.nestedFields.text({ bind: 'description' }),
            ui.nestedFields.text({ title: 'Category', bind: { category: { name: true } } }),
            ui.nestedFields.checkbox({ bind: 'isStockManaged', isHidden: true }),
        ],
        orderBy: {
            name: +1,
            id: +1,
        },
        placeholder: 'Select...',
        width: 'small',
    })
    fromItem: ui.fields.Reference<Item>;

    @ui.decorators.referenceField<StockReorderCalculation, Item>({
        parent() {
            return this.criteriaBlock;
        },
        title: 'To item',
        shouldSuggestionsIncludeColumns: true,
        node: '@sage/xtrem-master-data/Item',
        tunnelPage: '@sage/xtrem-master-data/Item',
        lookupDialogTitle: 'Select item',
        valueField: 'name',
        helperTextField: 'id',
        columns: [
            ui.nestedFields.text({ title: 'Name', bind: 'name' }),
            ui.nestedFields.text({ title: 'ID', bind: 'id' }),
            ui.nestedFields.text({ title: 'Description', bind: 'description' }),
            ui.nestedFields.text({ title: 'Category', bind: { category: { name: true } } }),
            ui.nestedFields.technical({ bind: 'isStockManaged' }),
        ],
        orderBy: {
            name: +1,
            id: +1,
        },
        placeholder: 'Select...',
        width: 'small',
    })
    toItem: ui.fields.Reference<Item>;

    @ui.decorators.dropdownListField<StockReorderCalculation>({
        parent() {
            return this.criteriaBlock;
        },
        title: 'Reorder type',
        optionType: '@sage/xtrem-master-data/PreferredProcess',
        hasEmptyValue: true,
        width: 'small',
    })
    reorderType: ui.fields.DropdownList;

    @ui.decorators.dateField<StockReorderCalculation>({
        parent() {
            return this.criteriaBlock;
        },
        title: 'End date',
        width: 'small',
        maxDate() {
            return DateValue.today().addDays(90).toString();
        },
        isMandatory: true,
    })
    endDate: ui.fields.Date;

    @ui.decorators.tableField<StockReorderCalculation, StockReorderCalculationResultLine>({
        canSelect: false,
        isHelperTextHidden: true,
        node: '@sage/xtrem-stock/StockReorderCalculationResultLine',
        bind: 'lines',
        isTitleHidden: true,
        canExport: true,
        isReadOnly: true,
        parent() {
            return this.mainSection;
        },
        columns: [
            ui.nestedFields.reference({
                bind: 'supplier',
                title: 'Supplier ID',
                node: '@sage/xtrem-master-data/Supplier',
                valueField: { businessEntity: { id: true } },
                columns: [
                    ui.nestedFields.technical({ bind: { businessEntity: { name: true } } }),
                    ui.nestedFields.technical({ bind: { businessEntity: { id: true } } }),
                    ui.nestedFields.technical<StockReorderCalculation, Supplier, BusinessEntity>({
                        bind: 'businessEntity',
                        node: '@sage/xtrem-master-data/BusinessEntity',
                        nestedFields: [
                            ui.nestedFields.technical({ bind: 'id' }),
                            ui.nestedFields.technical({ bind: 'name' }),
                        ],
                    }),
                    ui.nestedFields.technical({ bind: { currency: { _id: true } } }),
                ],
            }),
            ui.nestedFields.reference({
                bind: 'supplier',
                title: 'Supplier name',
                node: '@sage/xtrem-master-data/Supplier',
                valueField: { businessEntity: { name: true } },
                isReadOnly: true,
            }),
            ui.nestedFields.reference({
                bind: 'itemSite',
                title: 'Item',
                node: '@sage/xtrem-master-data/ItemSite',
                valueField: { item: { name: true } },
            }),
            ui.nestedFields.reference({
                bind: 'itemSite',
                title: 'Item ID',
                node: '@sage/xtrem-master-data/ItemSite',
                valueField: { item: { id: true } },
                columns: [
                    ui.nestedFields.reference({
                        bind: 'item',
                        node: '@sage/xtrem-master-data/Item',
                        columns: [
                            ui.nestedFields.technical({ bind: 'name' }),
                            ui.nestedFields.technical({ bind: 'isBought' }),
                            ui.nestedFields.technical({ bind: 'isManufactured' }),
                        ],
                    }),
                    ui.nestedFields.technical({ bind: 'purchaseLeadTime' }),
                    ui.nestedFields.technical({ bind: 'prodLeadTime' }),
                ],
            }),
            ui.nestedFields.reference({
                bind: 'itemSite',
                title: 'Item description',
                node: '@sage/xtrem-master-data/ItemSite',
                valueField: { item: { description: true } },
                isHiddenOnMainField: true,
            }),
            ui.nestedFields.reference({
                bind: 'itemSite',
                title: 'Site',
                node: '@sage/xtrem-master-data/ItemSite',
                valueField: { site: { id: true } },
                columns: [
                    ui.nestedFields.reference({
                        bind: 'site',
                        node: '@sage/xtrem-system/Site',
                        columns: [
                            ui.nestedFields.technical({ bind: 'id' }),
                            ui.nestedFields.technical({ bind: 'name' }),
                        ],
                    }),
                ],
            }),
            ui.nestedFields.reference({
                bind: 'company',
                title: 'Company',
                node: '@sage/xtrem-system/Company',
                valueField: 'name',
            }),
            ui.nestedFields.numeric({
                bind: 'quantity',
                title: 'Suggested quantity',
                isReadOnly: true,
                scale(_rowId, rowData) {
                    return StockValuationLib.stockUnitScale(rowData?.stockUnit);
                },
                postfix(_rowId, rowData) {
                    return StockValuationLib.stockUnitSymbol(rowData?.stockUnit);
                },
            }),
            ui.nestedFields.date({
                bind: 'orderDate',
                title: 'Order date',
            }),
            ui.nestedFields.dropdownList({
                bind: 'reorderType',
                title: 'Process method',
                optionType: '@sage/xtrem-master-data/preferredProcess',
            }),
            ui.nestedFields.reference({
                bind: 'stockUnit',
                node: '@sage/xtrem-master-data/UnitOfMeasure',
                valueField: 'symbol',
                isHidden: true,
                columns: [ui.nestedFields.technical({ bind: 'id' }), ui.nestedFields.technical({ bind: 'symbol' })],
            }),
        ],
        dropdownActions: [
            {
                icon: 'add',
                title: 'Create purchase order',
                access: { node: '@sage/xtrem-purchasing/PurchaseOrder', bind: '$create' },
                isHidden(_rowId, rowItem) {
                    return !(rowItem.itemSite?.item && rowItem.itemSite?.item.isBought);
                },
                async onClick(_rowId: string, rowData) {
                    const newPurchaseOrder = await this.$.dialog.page(
                        '@sage/xtrem-purchasing/ReorderPurchaseOrderPanel',
                        {
                            recommendation: JSON.stringify({
                                item: rowData.itemSite?.item,
                                site: rowData.itemSite?.site,
                                supplier: rowData.supplier,
                                quantity: rowData.quantity,
                                startDate: date.today(),
                                endDate: rowData.orderDate,
                            }),
                        },
                        {
                            rightAligned: true,
                            size: 'large',
                            resolveOnCancel: true,
                        },
                    );
                    if (newPurchaseOrder) {
                        this.$.showToast(
                            ui.localize(
                                '@sage/xtrem-stock/pages__stock_reorder_calculation__purchase_order_creation__success',
                                'Purchase order {{num}} created.',
                                { num: newPurchaseOrder.number },
                            ),
                            { timeout: 0, type: 'success' },
                        );
                    }
                },
            },
            {
                icon: 'add',
                title: 'Create work order',
                access: { node: '@sage/xtrem-manufacturing/WorkOrder', bind: '$create' },
                isHidden(_rowId, rowItem) {
                    return !(rowItem?.itemSite?.item && rowItem.itemSite.item.isManufactured);
                },
                async onClick(_rowId: string, rowData) {
                    const prodLeadTime = rowData.itemSite?.prodLeadTime ?? 0;
                    let startDate = date.parse(rowData?.orderDate ?? '').addDays(-prodLeadTime);
                    if (startDate.daysDiff(date.today()) < 0) {
                        startDate = date.today();
                    }
                    const newWorkOrder = await this.$.dialog.page(
                        '@sage/xtrem-manufacturing/WorkOrderPanel',
                        {
                            recommendation: JSON.stringify({
                                item: {
                                    name: rowData.itemSite?.item?.name,
                                    id: rowData.itemSite?.item?.id,
                                    item: rowData.itemSite?.item,
                                    site: rowData.itemSite?.site,
                                },
                                site: rowData.itemSite?.site,
                                quantity: rowData.quantity,
                                startDate,
                                endDate: rowData.orderDate,
                            }),
                        },
                        {
                            rightAligned: true,
                            size: 'large',
                            resolveOnCancel: true,
                        },
                    );
                    if (newWorkOrder) {
                        this.$.showToast(
                            ui.localize(
                                '@sage/xtrem-stock/pages__stock_reorder_calculation__work_order_creation__success',
                                'Work order {{num}} created.',
                                { num: newWorkOrder.number },
                            ),
                            { timeout: 0, type: 'success' },
                        );
                    }
                },
            },
        ],
    })
    lines: ui.fields.Table<StockReorderCalculationResultLine>;
}
