import type { ExtractEdgesPartial } from '@sage/xtrem-client';
import type { ItemBinding, UnitOfMeasure } from '@sage/xtrem-master-data-api';
import * as MasterDataUtils from '@sage/xtrem-master-data/build/lib/client-functions/utils';
import { getScaleValue } from '@sage/xtrem-master-data/build/lib/client-functions/utils';
import type { StockCountLineSerialNumber } from '@sage/xtrem-stock-api';
import type { GraphApi } from '@sage/xtrem-stock-data-api';
import * as StockDataUtils from '@sage/xtrem-stock-data/build/lib/client-functions/utils';
import * as serialNumberHelper from '@sage/xtrem-stock-data/build/lib/shared-functions/serial-number-helper';
import * as ui from '@sage/xtrem-ui';
import { isEqual } from 'lodash';
import type * as StockInterfaces from '../client-functions/interfaces';

@ui.decorators.page<StockCountSerialNumbersPanel>({
    title: 'Serial numbers',
    mode: 'default',
    isTransient: true,
    businessActions() {
        return [this.ok, this.cancel];
    },
    async onLoad() {
        await this.init();
    },
    onError(error: string | (Error & { errors: Array<any> })) {
        return MasterDataUtils.formatError(this, error);
    },
})
export class StockCountSerialNumbersPanel extends ui.Page<GraphApi> {
    initialTableValue = new Array<ExtractEdgesPartial<StockInterfaces.StockCountLineSerialNumberRangePageBinding>>();

    queryParams: StockInterfaces.StockCountSerialNumberPanelParameters;

    @ui.decorators.referenceField<StockCountSerialNumbersPanel, ItemBinding>({
        bind: 'item',
        node: '@sage/xtrem-master-data/Item',
        valueField: 'name',
        isHidden: true,
        columns: [
            ui.nestedFields.technical({ bind: 'name' }),
            ui.nestedFields.technical({ bind: 'id' }),
            ui.nestedFields.technical({ bind: 'description' }),
        ],
    })
    item: ui.fields.Reference<ItemBinding>;

    @ui.decorators.referenceField<StockCountSerialNumbersPanel, UnitOfMeasure>({
        node: '@sage/xtrem-master-data/UnitOfMeasure',
        tunnelPage: '@sage/xtrem-master-data/UnitOfMeasure',
        valueField: 'name',
        isHidden: true,
        columns: [
            ui.nestedFields.technical({ bind: 'name' }),
            ui.nestedFields.technical({ bind: 'id' }),
            ui.nestedFields.technical({ bind: 'symbol' }),
            ui.nestedFields.technical({ bind: 'description' }),
            ui.nestedFields.technical({ bind: 'decimalDigits' }),
        ],
    })
    unit: ui.fields.Reference<UnitOfMeasure>;

    @ui.decorators.numericField<StockCountSerialNumbersPanel>({
        parent() {
            return this.mainBlock;
        },
        title: 'Required quantity',
        isDisabled: true,
        isTransient: true,
        size: 'small',
        scale() {
            return this.getScale();
        },
        validation() {
            return this.validateQuantity();
        },
    })
    requiredQuantity: ui.fields.Numeric;

    @ui.decorators.numericField<StockCountSerialNumbersPanel>({
        parent() {
            return this.mainBlock;
        },
        title: 'Selected quantity',
        isDisabled: true,
        isTransient: true,
        size: 'small',
        scale() {
            return this?.unit?.value?.decimalDigits || 0;
        },
        validation() {
            return this.validateQuantity();
        },
    })
    selectedQuantity: ui.fields.Numeric;

    @ui.decorators.numericField<StockCountSerialNumbersPanel>({
        parent() {
            return this.mainBlock;
        },
        title: 'Remaining quantity',
        isDisabled: true,
        isTransient: true,
        size: 'small',
        scale() {
            return this.getScale();
        },
    })
    remainingQuantity: ui.fields.Numeric;

    @ui.decorators.numericField<StockCountSerialNumbersPanel>({
        parent() {
            return this.mainBlock;
        },
        title: 'Serial numbers to be issued',
        isHidden() {
            return this.queryParams.countedQuantity === this.queryParams.quantityInStockUnit;
        },
        isDisabled: true,
        isTransient: true,
        size: 'small',
        scale() {
            return this.getScale();
        },
    })
    quantityToIssue: ui.fields.Numeric;

    @ui.decorators.section<StockCountSerialNumbersPanel>({
        isOpen: true,
        isTitleHidden: true,
        title: 'General',
    })
    mainSection: ui.containers.Section;

    @ui.decorators.block<StockCountSerialNumbersPanel>({
        parent() {
            return this.mainSection;
        },
        width: 'extra-large',
        title: 'General',
        isTitleHidden: true,
    })
    mainBlock: ui.containers.Block;

    @ui.decorators.tableField<StockCountSerialNumbersPanel, StockInterfaces.StockCountLineSerialNumberRangePageBinding>(
        {
            title: 'Serial numbers',
            pageSize: 10,
            displayMode: ui.fields.TableDisplayMode.compact,
            orderBy: {
                startingSerialNumber: +1,
            },
            canSelect: false,
            isDisabled() {
                return !this.queryParams.isEditable;
            },
            columns: [
                ui.nestedFields.reference<
                    StockCountSerialNumbersPanel,
                    StockInterfaces.StockCountLineSerialNumberRangePageBinding,
                    StockCountLineSerialNumber
                >({
                    title: 'From serial number',
                    bind: 'startingSerialNumber',
                    node: '@sage/xtrem-stock/StockCountLineSerialNumber',
                    valueField: { serialNumber: { id: true } },
                    minLookupCharacters: 0,
                    lookupDialogTitle: 'Select from serial number',
                    columns: [ui.nestedFields.text({ bind: { serialNumber: { id: true } }, title: 'ID' })],
                    filter() {
                        return {
                            stockCountLine: { _id: { _eq: `${this.queryParams.stockCountLineId}` } },
                        };
                    },
                    validation(val, rowData) {
                        if (
                            StockCountSerialNumbersPanel.initSerialNumberRowData(rowData) &&
                            !this.checkUniquenessOfSerialNumberRange(rowData)
                        ) {
                            return ui.localize(
                                '@sage/xtrem-stock/pages__stock_count_serial_numbers_panel__duplicate_error',
                                'The serial number is already included in another range.',
                            );
                        }
                        const line = this.serialNumberRanges.getRecordValue(rowData._id);
                        // enforce setRecordValue if startingSerialNumber really changed. Reason:
                        // 'validation' for quantity property is triggered before onChange
                        // of startingSerialNumber property. Therefore the quantity is in this case the old one before
                        // change of startingSerialNumber and we enforce the new quantity here (didn't find another solution).
                        if (line?.originalStartId !== rowData.startingSerialNumber?.serialNumber?.id) {
                            this.serialNumberRanges.setRecordValue(rowData);
                            this.updateHeaderQuantities();
                        }

                        return undefined;
                    },
                    onChange(_id: number, rowData) {
                        if (StockCountSerialNumbersPanel.initSerialNumberRowData(rowData)) {
                            rowData.originalStartId = rowData.startingSerialNumber?.serialNumber.id ?? '';
                            this.serialNumberRanges.setRecordValue(rowData);
                            this.updateHeaderQuantities();
                        }
                    },
                }),
                ui.nestedFields.numeric<
                    StockCountSerialNumbersPanel,
                    StockInterfaces.StockCountLineSerialNumberRangePageBinding
                >({
                    title: 'Quantity',
                    bind: 'quantity',
                    async validation(val: number, rowData) {
                        await this.$.graph
                            .node('@sage/xtrem-stock/StockCountLineSerialNumber')
                            .queries.getEndingSerialNumber(true, {
                                stockCountLineId: this.queryParams.stockCountLineId,
                                startingSerialNumber: rowData.startingSerialNumber?.serialNumber?.id,
                                quantity: val,
                            })
                            .execute();

                        // check the uniqueness of the calculated range for the stock detail
                        if (!this.checkUniquenessOfSerialNumberRange(rowData)) {
                            return ui.localize(
                                '@sage/xtrem-stock/pages__stock_count_serial_numbers_panel__duplicate_range_error',
                                'One of the serial numbers is already included in another range.',
                            );
                        }

                        return undefined;
                    },
                    onChange(_id: number, rowData) {
                        if (StockCountSerialNumbersPanel.prepareSerialNumberRowData(rowData)) {
                            this.serialNumberRanges.setRecordValue(rowData);
                        }
                        this.updateHeaderQuantities();
                    },
                    scale() {
                        return this.getScale();
                    },
                }),
                ui.nestedFields.reference({
                    title: 'To serial number',
                    bind: 'endingSerialNumber',
                    node: '@sage/xtrem-stock/StockCountLineSerialNumber',
                    valueField: 'serialNumber.id',
                    isReadOnly: true,
                    columns: [ui.nestedFields.text({ bind: 'serialNumber.id', title: 'ID' })],
                }),
                ui.nestedFields.numeric({
                    title: 'From serial number',
                    bind: 'numericStart',
                    isHidden: true,
                }),
                ui.nestedFields.numeric({
                    title: 'To serial number',
                    bind: 'numericEnd',
                    isHidden: true,
                }),
                ui.nestedFields.text({
                    title: 'Original from serial number',
                    bind: 'originalStartId',
                    isHidden: true,
                }),
            ],
            dropdownActions: [
                {
                    icon: 'bin',
                    title: 'Delete',
                    isDestructive: true,
                    isDisabled() {
                        return !this.queryParams.isEditable;
                    },
                    onClick(rowId) {
                        this.serialNumberRanges.removeRecord(rowId);
                        this.updateHeaderQuantities();
                    },
                },
            ],
            fieldActions() {
                return [this.addSerialNumberRange];
            },
            async onChange() {
                await this.serialNumberRanges.validate();
            },
            parent() {
                return this.mainSection;
            },
        },
    )
    serialNumberRanges: ui.fields.Table<StockInterfaces.StockCountLineSerialNumberRangePageBinding>;

    @ui.decorators.pageAction<StockCountSerialNumbersPanel>({
        icon: 'add',
        title: 'Add line',
        onClick() {
            this.serialNumberRanges.addRecord({});
        },
    })
    addSerialNumberRange: ui.PageAction;

    @ui.decorators.pageAction<StockCountSerialNumbersPanel>({
        title: 'OK',
        isDisabled() {
            return !this.queryParams.isEditable || !this.checkPageIsDirty();
        },
        async onClick() {
            let output: { jsonSerialNumbers: string } | false = false;

            if ((await this.mainBlock.validate()).length === 0) {
                if (this.checkPageIsDirty()) {
                    output = {
                        jsonSerialNumbers: JSON.stringify({
                            ranges: this.serialNumberRanges.value.map(serialNumberRange => ({
                                ...serialNumberRange,
                                startingSerialNumberId: serialNumberRange.startingSerialNumber?.serialNumber?.id,
                                endingSerialNumberId: serialNumberRange.endingSerialNumber?.serialNumber?.id,
                            })),
                        }),
                    };
                }

                this.$.finish({
                    output,
                });
            }
        },
    })
    ok: ui.PageAction;

    @ui.decorators.pageAction<StockCountSerialNumbersPanel>({
        title: 'Cancel',
        buttonType: 'tertiary',
        onClick() {
            this.$.finish({ output: false });
        },
    })
    cancel: ui.PageAction;

    private async init() {
        this.loadQueryParams(this.$.queryParameters.args);
        await this.setTransientValues(this.queryParams);
        this.$.setPageClean();
        this.initialTableValue = this.serialNumberRanges.value;
    }

    private updateHeaderQuantities() {
        this.selectedQuantity.value = this.serialNumberRanges.value.reduce(
            (sum, rowData) => sum + (rowData.quantity ?? 0),
            0,
        );
        this.remainingQuantity.value = (this.requiredQuantity.value ?? 0) - this.selectedQuantity.value;
    }

    // Init properties of a row after selecting startingSerialNumber in the same row (same endingSerialNumber, quantity 1)
    private static initSerialNumberRowData(
        rowData: StockInterfaces.StockCountLineSerialNumberRangePageBinding,
    ): boolean {
        rowData.quantity = 1;
        rowData.endingSerialNumber = rowData.startingSerialNumber;
        const { serialNumberNumericPart } = serialNumberHelper.splitSerialNumber(
            rowData.startingSerialNumber?.serialNumber?.id || '',
        );
        rowData.numericStart = parseInt(serialNumberNumericPart, 10);
        rowData.numericEnd = rowData.numericStart;
        return true;
    }

    /**
     * Calculate endingSerialNumber, numericStart and numericEnd of a row from the selected startingSerialNumber in the same row
     */
    private static prepareSerialNumberRowData(
        rowData: StockInterfaces.StockCountLineSerialNumberRangePageBinding,
    ): boolean {
        if ((rowData.quantity ?? 0) > 0) {
            // extract last numeric part from startingSerialNumber and make it numeric
            const { serialNumberNumericPart } = serialNumberHelper.splitSerialNumber(
                rowData.startingSerialNumber?.serialNumber?.id || '',
            );
            rowData.numericStart = parseInt(serialNumberNumericPart, 10);
            rowData.numericEnd = (rowData.numericStart ?? 0) + (rowData.quantity ?? 0) - 1;
            const endingSerialNumber = serialNumberHelper.getEndingSerialNumberId(
                rowData.startingSerialNumber?.serialNumber?.id || '',
                rowData.quantity ?? 0,
            );
            rowData.endingSerialNumber = {
                serialNumber: {
                    id: endingSerialNumber,
                },
            };
            return true;
        }
        return false;
    }

    // Checks for the range of a changed row in the serial number grid if there are overlapping with another row of the grid
    private checkUniquenessOfSerialNumberRange(
        rowDataToCheck: StockInterfaces.StockCountLineSerialNumberRangePageBinding,
    ): boolean {
        return !this.serialNumberRanges.value.some(rowData => {
            const numericStartToCheck = rowDataToCheck.numericStart ?? 0;
            const numericEndToCheck = rowDataToCheck.numericEnd ?? 0;
            const numericStart = rowData.numericStart ?? 0;
            const numericEnd = rowData.numericEnd ?? 0;
            const startingSerialToCheck = serialNumberHelper.splitSerialNumber(
                rowDataToCheck.startingSerialNumber?.serialNumber?.id || '',
            );
            const startingSerial = serialNumberHelper.splitSerialNumber(
                rowData.startingSerialNumber?.serialNumber?.id || '',
            );
            return (
                rowDataToCheck._id !== rowData._id && // don't check actual row itself
                // check if prefix and postfix are identical
                startingSerialToCheck.serialNumberPrefixStringPart === startingSerial.serialNumberPrefixStringPart &&
                startingSerialToCheck.serialNumberPostfixStringPart === startingSerial.serialNumberPostfixStringPart &&
                // check ranges
                ((numericStartToCheck >= numericStart && numericStartToCheck <= numericEnd) ||
                    (numericEndToCheck >= numericStart && numericEndToCheck <= numericEnd) ||
                    (numericStart >= numericStartToCheck && numericStart <= numericEndToCheck) ||
                    (numericEnd >= numericStartToCheck && numericEnd <= numericEndToCheck))
            );
        });
    }

    private loadQueryParams(panelArgs: string | number | boolean) {
        if (panelArgs && typeof panelArgs === 'string') {
            const queryParams =
                MasterDataUtils.tryParseJSON<StockInterfaces.StockCountSerialNumberPanelParameters>(panelArgs);

            if (!queryParams) return;

            this.queryParams = queryParams;
        }

        if (this.queryParams.jsonSerialNumbers) {
            const serialNumbers =
                MasterDataUtils.tryParseJSON<{ ranges: Array<StockInterfaces.SerialNumberNumericRange> }>(
                    this.queryParams.jsonSerialNumbers,
                )?.ranges ?? [];
            this.loadSerialNumberRanges(serialNumbers);
        }
        // default the isEditable value to false
        if (this.queryParams.isEditable === null) {
            this.queryParams.isEditable = false;
        }
    }

    private async setTransientValues(queryParams: this['queryParams']) {
        this.requiredQuantity.value = Number(this.queryParams.countedQuantity);
        this.quantityToIssue.value =
            (this.queryParams.quantityInStockUnit ?? 0) - (this.queryParams.countedQuantity ?? 0);
        this.updateHeaderQuantities();
        this.item.value = await StockDataUtils.getItemInfo(this, queryParams.item ?? '');
        await StockDataUtils.setUnitInfo(this, this.unit, this.item, queryParams.unit ?? '', !!queryParams.unit);
    }

    private loadSerialNumberRanges(serialNumberRanges: Array<StockInterfaces.SerialNumberNumericRange>) {
        this.serialNumberRanges.value = serialNumberRanges.map(serialNumberRange => ({
            ...serialNumberRange,
            _id: this.serialNumberRanges.generateRecordId(),
            startingSerialNumber: { serialNumber: { id: serialNumberRange.startingSerialNumberId } },
            endingSerialNumber: { serialNumber: { id: serialNumberRange.endingSerialNumberId } },
        }));
    }

    validateQuantity() {
        if ((this.selectedQuantity.value ?? 0) <= (this.requiredQuantity.value ?? 0)) {
            return ui.localize(
                '@sage/xtrem-stock/pages__stock_count_serial_numbers_panel__selected_quantity_too_high',
                'The number of selected serial numbers ({{selectedQuantity}}) cannot exceed the counted quantity ({{countedQuantity}}).',
                {
                    countedQuantity: this.requiredQuantity.value,
                    selectedQuantity: this.selectedQuantity.value,
                },
            );
        }
        return undefined;
    }

    checkPageIsDirty() {
        return !isEqual(this.initialTableValue, this.serialNumberRanges.value);
    }

    getScale() {
        return getScaleValue(0, this?.unit?.value?.decimalDigits);
    }
}
