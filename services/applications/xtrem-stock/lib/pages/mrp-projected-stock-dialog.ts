import { extractEdges } from '@sage/xtrem-client';
import { date, DateValue } from '@sage/xtrem-date-time';
import * as utils from '@sage/xtrem-master-data/build/lib/client-functions/utils';
import type { GraphApi } from '@sage/xtrem-stock-data-api';
import * as ui from '@sage/xtrem-ui';

@ui.decorators.page<MrpProjectedStockDialog>({
    title: 'Projected stock',
    module: 'stock',
    mode: 'default',
    isTransient: true,
    skipDirtyCheck: true,

    businessActions() {
        return [this.ok];
    },

    async onLoad() {
        // Process parameters given from the calling function.
        if (this.$.queryParameters.item !== undefined) {
            this.item.value = JSON.parse(this.$.queryParameters.item as string).name;
            this.item.helperText = JSON.parse(this.$.queryParameters.item as string).id;
        }
        if (this.$.queryParameters.site !== undefined) {
            this.site.value = JSON.parse(this.$.queryParameters.site as string).name;
            this.site.helperText = JSON.parse(this.$.queryParameters.site as string).id;
        }
        if (this.$.queryParameters.startDate !== undefined) {
            this.startDate.value = JSON.parse(this.$.queryParameters.startDate as string);
        }
        if (this.$.queryParameters.numberOfWeeks !== undefined) {
            this.numberOfWeeks.value = this.$.queryParameters.numberOfWeeks as number;
        }
        if (this.$.queryParameters.preferredProcess !== undefined) {
            this.preferredProcess.value = this.$.queryParameters.preferredProcess as string;
        }
        if (this.$.queryParameters.safetyStock !== undefined) {
            this.safetyStock.value = this.$.queryParameters.safetyStock as number;
        }
        if (this.$.queryParameters.batchQuantity !== undefined) {
            this.batchQuantity.value = this.$.queryParameters.batchQuantity as number;
        }
        if (this.$.queryParameters.economicOrderQuantity !== undefined) {
            this.economicOrderQuantity.value = this.$.queryParameters.economicOrderQuantity as number;
        }
        if (this.$.queryParameters.decimalDigits !== undefined) {
            this.decimalDigits.value = this.$.queryParameters.decimalDigits as number;
        }
        if (this.$.queryParameters.stockUnit !== undefined) {
            this.stockUnit.value = this.$.queryParameters.stockUnit
                ? JSON.parse(this.$.queryParameters.stockUnit as string)
                : null;
        }
        if (this.$.queryParameters.mrpCalculationId !== undefined) {
            this.mrpCalculationId.value = JSON.parse(this.$.queryParameters.mrpCalculationId as string);
        }
        if (this.$.queryParameters.leadTime !== undefined) {
            if (this.preferredProcess.value === 'purchasing') {
                this.purchaseLeadTime.value = this.$.queryParameters.leadTime as number;
                this.productionLeadTime.isHidden = true;
                this.purchaseLeadTime.isHidden = false;
            } else {
                this.productionLeadTime.value = this.$.queryParameters.leadTime as number;
                this.productionLeadTime.isHidden = false;
                this.purchaseLeadTime.isHidden = true;
            }
        }
        this.frequency.value = 'week';
        this.$.page.title = `${this.site.helperText ?? ''} - ${this.item.helperText ?? ''}`;
        await this.updateChart(this.site.helperText ?? '', this.item.helperText ?? '');
    },
    onError(error: string | (Error & { errors: Array<any> })) {
        return utils.formatError(this, error);
    },
})
export class MrpProjectedStockDialog extends ui.Page<GraphApi> {
    @ui.decorators.pageAction<MrpProjectedStockDialog>({
        title: 'Ok',
        onClick() {
            this.$.finish({});
        },
    })
    ok: ui.PageAction;

    @ui.decorators.section<MrpProjectedStockDialog>({
        title: 'General information',
        isTitleHidden: true,
    })
    mainSection: ui.containers.Section;

    @ui.decorators.block<MrpProjectedStockDialog>({
        title: 'Projected stock',
        isTitleHidden: true,
        parent() {
            return this.mainSection;
        },
        width: 'extra-large',
    })
    criteriaBlock: ui.containers.Block;

    @ui.decorators.block<MrpProjectedStockDialog>({
        title: 'Projected stock',
        isTitleHidden: true,
        parent() {
            return this.mainSection;
        },
        width: 'extra-large',
    })
    chartBlock: ui.containers.Block;

    @ui.decorators.textField<MrpProjectedStockDialog>({
        isHidden: true,
    })
    mrpCalculationId: ui.fields.Text;

    @ui.decorators.textField<MrpProjectedStockDialog>({
        parent() {
            return this.criteriaBlock;
        },
        title: 'Site',
        width: 'small',
        isReadOnly: true,
    })
    site: ui.fields.Text;

    @ui.decorators.textField<MrpProjectedStockDialog>({
        parent() {
            return this.criteriaBlock;
        },
        title: 'Item',
        width: 'small',
        isReadOnly: true,
    })
    item: ui.fields.Text;

    @ui.decorators.dateField<MrpProjectedStockDialog>({
        parent() {
            return this.criteriaBlock;
        },
        title: 'Start date',
        width: 'small',
        isReadOnly: true,
    })
    startDate: ui.fields.Date;

    @ui.decorators.numericField<MrpProjectedStockDialog>({
        parent() {
            return this.criteriaBlock;
        },
        title: 'Period in weeks',
        isReadOnly: true,
        width: 'small',
    })
    numberOfWeeks: ui.fields.Numeric;

    @ui.decorators.textField<MrpProjectedStockDialog>({
        parent() {
            return this.criteriaBlock;
        },
        title: 'Preferred process',
        width: 'small',
        isReadOnly: true,
    })
    preferredProcess: ui.fields.Text;

    @ui.decorators.numericField<MrpProjectedStockDialog>({
        parent() {
            return this.criteriaBlock;
        },
        bind: 'safetyStock',
        title: 'Safety stock',
        unit() {
            return this.stockUnit.value;
        },
        unitMode: 'unitOfMeasure',
        width: 'small',
        isReadOnly: true,
    })
    safetyStock: ui.fields.Numeric;

    @ui.decorators.numericField<MrpProjectedStockDialog>({
        parent() {
            return this.criteriaBlock;
        },
        bind: 'batchQuantity',
        title: 'Batch quantity',
        unit() {
            return this.stockUnit.value;
        },
        unitMode: 'unitOfMeasure',
        width: 'small',
        isReadOnly: true,
    })
    batchQuantity: ui.fields.Numeric;

    @ui.decorators.numericField<MrpProjectedStockDialog>({
        parent() {
            return this.criteriaBlock;
        },
        bind: 'economicOrderQuantity',
        title: 'Economic order quantity',
        unit() {
            return this.stockUnit.value;
        },
        unitMode: 'unitOfMeasure',
        width: 'small',
        isReadOnly: true,
    })
    economicOrderQuantity: ui.fields.Numeric;

    @ui.decorators.referenceField<MrpProjectedStockDialog>({
        bind: 'stockUnit',
        isHidden: true,
        columns: [
            ui.nestedFields.technical({ bind: 'id' }),
            ui.nestedFields.text({ bind: 'symbol', title: 'Symbol' }),
            ui.nestedFields.numeric({ bind: 'decimalDigits', title: 'Decimal places' }),
        ],
    })
    stockUnit: ui.fields.Reference;

    @ui.decorators.numericField<MrpProjectedStockDialog>({
        isHidden: true,
    })
    decimalDigits: ui.fields.Numeric;

    @ui.decorators.numericField<MrpProjectedStockDialog>({
        parent() {
            return this.criteriaBlock;
        },
        bind: 'leadTime',
        title: 'Purchase lead time',
        scale() {
            return 0;
        },
        postfix() {
            return 'd';
        },
        width: 'small',
        isReadOnly: true,
    })
    purchaseLeadTime: ui.fields.Numeric;

    @ui.decorators.numericField<MrpProjectedStockDialog>({
        parent() {
            return this.criteriaBlock;
        },
        bind: 'leadTime',
        title: 'Production lead time',
        scale() {
            return 0;
        },
        postfix() {
            return 'd';
        },
        width: 'small',
        isReadOnly: true,
    })
    productionLeadTime: ui.fields.Numeric;

    @ui.decorators.radioField<MrpProjectedStockDialog>({
        parent() {
            return this.criteriaBlock;
        },
        title: 'Frequency',
        width: 'small',
        options: ['day', 'week'],
        map(value: any) {
            switch (value) {
                case 'week':
                    return ui.localize('@sage/xtrem-stock/pages__mrp_projected_stock_dialog__week', 'Week');
                case 'day':
                    return ui.localize('@sage/xtrem-stock/pages__mrp_projected_stock_dialog__day', 'Day');
                default:
                    return value;
            }
        },
        async onChange() {
            if (this.site.helperText && this.item.helperText) {
                await this.updateChart(this.site.helperText, this.item.helperText);
            }
        },
    })
    frequency: ui.fields.Radio;

    @ui.decorators.chartField<MrpProjectedStockDialog>({
        parent() {
            return this.chartBlock;
        },
        title: 'Projected stock',
        isTitleHidden: true,
        isFullWidth: true,
        isTransient: true,
        chart: ui.charts.line({
            series: [
                ui.nestedFields.numeric({
                    bind: 'quantity',
                    title: 'Projected stock',
                    scale: 4,
                }),
                ui.nestedFields.numeric({
                    bind: 'safetyLevel',
                    title: 'Safety level',
                }),
            ],
            xAxis: ui.nestedFields.date({ bind: 'dueDate', title: 'Date' }),
        }),
    })
    projectedStockChart: ui.fields.Chart;

    @ui.decorators.tableField<MrpProjectedStockDialog>({
        parent() {
            return this.mainSection;
        },
        title: 'Details',
        canUserHideColumns: false,
        canSelect: false,
        isHelperTextHidden: true,
        pageSize: 10,
        canExport: true,
        isTitleHidden: true,
        isTransient: true,
        orderBy: {
            dueDate: +1,
        },
        columns: [
            ui.nestedFields.text<MrpProjectedStockDialog>({
                bind: '_sortValue',
                isHidden: true,
            }),
            ui.nestedFields.text<MrpProjectedStockDialog>({
                bind: 'documentId',
                isHidden: true,
            }),
            ui.nestedFields.date<MrpProjectedStockDialog>({
                title: 'Due date',
                bind: 'dueDate',
                isReadOnly: true,
            }),
            ui.nestedFields.dropdownList({
                title: 'Document type',
                bind: 'documentType',
                map(value) {
                    // Note: Unfortunately, we inherit the document type as string from WIP as optionType
                    //       '@sage/xtrem-master-data/WorkInProgressDocumentType' (enum) but with an additional
                    //       value 'startingStock'. So, we handle this extra value separately and return
                    //       otherwise the translated enum text.
                    if (value === 'startingStock') {
                        return ui.localize(
                            '@sage/xtrem-stock/pages__mrp_projected_stock_dialog__starting_stock',
                            'Starting stock',
                        );
                    }
                    return ui.localizeEnumMember('@sage/xtrem-master-data/WorkInProgressDocumentType', value);
                },
                isReadOnly: true,
            }),
            ui.nestedFields.link<MrpProjectedStockDialog>({
                title: 'Document number',
                bind: 'documentNumber',
                width: 'large',
                onClick(_id: string, rowData: any) {
                    const pageName = MrpProjectedStockDialog.getDocumentPageName(rowData.documentType);
                    if (pageName.length) {
                        // TODO: REFACTOR As soon as router.gotoExternal() is enhenced to compute the whole URL please change.
                        this.$.router.goToExternal(`${pageName}/${btoa(JSON.stringify({ _id: rowData.documentId }))}`);
                    }
                },
            }),
            ui.nestedFields.numeric<MrpProjectedStockDialog>({
                title: 'Quantity',
                bind: 'quantity',
                isReadOnly: true,
                unit: (_id, rowData) => rowData?.stockUnit,
                unitMode: 'unitOfMeasure',
            }),
            ui.nestedFields.checkbox<MrpProjectedStockDialog>({
                title: 'Suggestion',
                bind: 'suggestion',
                isReadOnly: true,
            }),
            ui.nestedFields.numeric<MrpProjectedStockDialog>({
                title: 'Bucket',
                bind: 'bucket',
                isReadOnly: true,
            }),
        ],
    })
    lines: ui.fields.Table;

    async updateChart(site: string, item: string) {
        const projectedStockData: any[] = [];
        const periods = this.frequency.value === 'week' && this.numberOfWeeks.value ? this.numberOfWeeks.value : 7;
        const startDate = DateValue.parse(this.startDate.value ?? '');
        const endDate = this.frequency.value === 'week' ? startDate.addWeeks(periods) : startDate.addDays(periods);

        // initialize the data for all periods with a zero quantity but correct safetyLevel and period date
        // eslint-disable-next-line no-plusplus
        for (let step = 0; step < periods; step++) {
            projectedStockData.push({
                dueDate: this.frequency.value === 'week' ? this.getBucketDate(step) : startDate.addDays(step),
                safetyLevel: this.safetyStock.value,
                quantity: 0,
            });
        }

        const start = startDate.format('YYYY-MM-DD');
        const end = endDate.format('YYYY-MM-DD');

        // get all the MrpWorkLine record of the calculation within the date range for the existing orders
        const periodData = extractEdges(
            await this.$.graph
                .node('@sage/xtrem-mrp-data/MrpWorkLine')
                .aggregate.query(
                    ui.queryUtils.edgesSelector(
                        {
                            group: {
                                bucket: { _by: 'value' },
                                dueDate: { _by: 'day' },
                                documentType: { _by: 'value' },
                                documentId: { _by: 'value' },
                                documentNumber: { _by: 'value' },
                            },
                            values: {
                                quantity: {
                                    sum: true,
                                },
                            },
                        },
                        {
                            filter: {
                                _and: [
                                    { mrpCalculation: { _id: this.mrpCalculationId.value } },
                                    { item: { id: item } },
                                    { site: { id: site } },
                                    { dueDate: { _gte: start } },
                                    { dueDate: { _lte: end } },
                                ],
                            },
                        },
                    ),
                )
                .execute(),
        );

        // get all the MrpResultLine record of the calculation within the date range for the suggestions
        const periodSuggestionData = extractEdges(
            await this.$.graph
                .node('@sage/xtrem-mrp-data/MrpResultLine')
                .aggregate.query(
                    ui.queryUtils.edgesSelector(
                        {
                            group: {
                                endDate: { _by: 'day' },
                            },
                            values: {
                                quantity: {
                                    sum: true,
                                },
                            },
                        },
                        {
                            filter: {
                                _and: [
                                    { mrpCalculation: { _id: this.mrpCalculationId.value } },
                                    { item: { id: item } },
                                    { site: { id: site } },
                                    { endDate: { _gte: start } },
                                    { endDate: { _lte: end } },
                                ],
                            },
                        },
                    ),
                )
                .execute(),
        );

        // calculate the quantity (stock level) for all the prepared days or weeks
        let stockLevel = 0;
        projectedStockData.forEach(detail => {
            const periodDataDay = periodData.filter(dataDay =>
                this.frequency.value === 'week'
                    ? detail.dueDate.format('YYYY-MM-DD') <= date.parse(dataDay.group.dueDate).format('YYYY-MM-DD') &&
                      date.parse(dataDay.group.dueDate).format('YYYY-MM-DD') <=
                          detail.dueDate.addDays(7).format('YYYY-MM-DD')
                    : detail.dueDate.format('YYYY-MM-DD') === date.parse(dataDay.group.dueDate).format('YYYY-MM-DD'),
            );

            periodDataDay.forEach(day => {
                if (day.values.quantity) {
                    stockLevel += +day.values.quantity.sum;
                }
            });

            const periodSuggestionDataDay = periodSuggestionData.filter(dataDay =>
                this.frequency.value === 'week'
                    ? detail.dueDate.format('YYYY-MM-DD') <= date.parse(dataDay.group.endDate).format('YYYY-MM-DD') &&
                      date.parse(dataDay.group.endDate).format('YYYY-MM-DD') <=
                          detail.dueDate.addDays(7).format('YYYY-MM-DD')
                    : detail.dueDate.format('YYYY-MM-DD') === date.parse(dataDay.group.endDate).format('YYYY-MM-DD'),
            );

            periodSuggestionDataDay.forEach(day => {
                if (day.values.quantity) {
                    stockLevel += +day.values.quantity.sum;
                }
            });

            detail.quantity = stockLevel;
            detail.dueDate = detail.dueDate.format('YYYY-MM-DD');
        });

        this.projectedStockChart.value = projectedStockData;

        // add grid data
        this.lines.value = [];
        let count = 0;
        periodData.forEach(line => {
            if (line.values.quantity) {
                count += 1;
                this.lines.addRecord({
                    _sortValue: count,
                    documentType: line.group.documentType,
                    documentNumber: line.group.documentNumber,
                    documentId: line.group.documentId,
                    dueDate: line.group.dueDate,
                    quantity: line.values.quantity.sum,
                    stockUnit: this.stockUnit.value,
                    suggestion: false,
                    bucket: line.group.bucket,
                });
            }
        });
        periodSuggestionData.forEach(line => {
            if (line.values.quantity) {
                count += 1;
                this.lines.addRecord({
                    _sortValue: count,
                    dueDate: line.group.endDate,
                    quantity: line.values.quantity.sum,
                    stockUnit: this.stockUnit.value,
                    suggestion: true,
                    bucket: 0,
                });
            }
        });
    }

    private getBucketDate(bucketNumber: number) {
        if (this.startDate.value) {
            const startDateBucket1 = date.parse(this.startDate.value).sameWeek(1, 1);
            return startDateBucket1.addDays(bucketNumber * 7);
        }
        return this.startDate.value;
    }

    private static getDocumentPageName(documentType: string): string {
        switch (documentType) {
            case 'workOrder':
            case 'materialNeed':
                return '@sage/xtrem-manufacturing/WorkOrder';
            case 'salesOrder':
                return '@sage/xtrem-sales/SalesOrder';
            case 'purchaseOrder':
                return '@sage/xtrem-purchasing/PurchaseOrder';
            case 'purchaseReceipt':
                return '@sage/xtrem-purchasing/PurchaseReceipt';
            case 'purchaseReturn':
                return '@sage/xtrem-purchasing/PurchaseReturn';
            default:
                return '';
        }
    }
}
