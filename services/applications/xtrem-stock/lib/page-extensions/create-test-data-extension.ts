import type { Item } from '@sage/xtrem-master-data-api';
import type { CreateTestData as CreateTestDataPage } from '@sage/xtrem-master-data/build/lib/pages/create-test-data';
import type { GraphApi } from '@sage/xtrem-stock-api';
import * as ui from '@sage/xtrem-ui';

@ui.decorators.pageExtension<CreateTestDataExtension>({
    extends: '@sage/xtrem-master-data/CreateTestData',
})
export class CreateTestDataExtension extends ui.PageExtension<CreateTestDataPage, GraphApi> {
    @ui.decorators.block<CreateTestDataExtension>({
        parent() {
            return this.createSection;
        },
        width: 'extra-large',
        title: 'Item',
    })
    itemBlock: ui.containers.Block;

    @ui.decorators.referenceField<CreateTestDataExtension, Item>({
        parent() {
            return this.itemBlock;
        },
        node: '@sage/xtrem-master-data/Item',
        title: 'Base item',
        width: 'small',
        isMandatory: true,
        tunnelPage: '@sage/xtrem-master-data/Item',
        lookupDialogTitle: 'Select item',
        valueField: 'name',
        helperTextField: 'id',
        shouldSuggestionsIncludeColumns: true,
        columns: [
            ui.nestedFields.text({ title: 'Name', bind: 'name' }),
            ui.nestedFields.text({ title: 'Item ID', bind: 'id' }),
            ui.nestedFields.text({ title: 'Description', bind: 'description' }),
            ui.nestedFields.technical({
                bind: 'stockUnit',
                node: '@sage/xtrem-master-data/UnitOfMeasure',
                nestedFields: [
                    ui.nestedFields.technical({
                        bind: 'symbol',
                    }),
                    ui.nestedFields.technical({
                        bind: 'description',
                    }),
                    ui.nestedFields.technical({
                        bind: 'decimalDigits',
                    }),
                ],
            }),
        ],
    })
    item: ui.fields.Reference<Item>;

    @ui.decorators.numericField<CreateTestDataExtension>({
        parent() {
            return this.itemBlock;
        },
        isMandatory: true,
        title: 'Quantity of items',
        width: 'small',
        min: 1,
    })
    itemQuantity: ui.fields.Numeric;

    @ui.decorators.numericField<CreateTestDataExtension>({
        parent() {
            return this.itemBlock;
        },
        title: 'Maximum stock quantity',
        width: 'small',
    })
    maxStockQuantity: ui.fields.Numeric;

    @ui.decorators.switchField<CreateTestDataExtension>({
        parent() {
            return this.itemBlock;
        },
        title: 'Stock quantity fixed for all items',
        width: 'small',
    })
    isStockQuantityFixed: ui.fields.Switch;

    @ui.decorators.buttonField<CreateTestDataExtension>({
        parent() {
            return this.itemBlock;
        },
        map() {
            return ui.localize('@sage/xtrem-stock/create_items_create', 'Create');
        },
        async onClick() {
            const validation = await this.itemBlock.validate();

            if (!validation.length) {
                this.$.loader.isHidden = false;
                this.itemCreate.isDisabled = true;
                await this.$.graph
                    .node('@sage/xtrem-master-data/Item')
                    .asyncOperations.createTestItems.runToCompletion(true, {
                        itemId: this.item.value?.id ?? '',
                        quantity: Number(this.itemQuantity.value),
                        stockCreation: this.maxStockQuantity.value
                            ? {
                                  maxStockQuantity: this.maxStockQuantity.value,
                                  isStockQuantityFixed: this.isStockQuantityFixed.value ?? false,
                              }
                            : undefined,
                    })
                    .execute();
                this.itemCreate.isDisabled = false;
                this.$.loader.isHidden = true;
                this.$.setPageClean();
            }
        },
    })
    itemCreate: ui.fields.Button;
}
declare module '@sage/xtrem-master-data/build/lib/pages/create-test-data' {
    interface CreateTestData extends CreateTestDataExtension {}
}
