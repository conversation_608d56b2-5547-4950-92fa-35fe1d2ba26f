import type { ComponentExtension } from '@sage/xtrem-stock-api';
import * as PillColorStock from '@sage/xtrem-stock-data/build/lib/client-functions/pill-color';
import type { GraphApi } from '@sage/xtrem-system-api';
import type { BillOfMaterialComponentPanel } from '@sage/xtrem-technical-data/lib/pages/bill-of-material-component-panel';
import * as ui from '@sage/xtrem-ui';

@ui.decorators.pageExtension<BillOfMaterialComponentPanelExtension>({
    extends: '@sage/xtrem-technical-data/BillOfMaterialComponentPanel',
    onLoad() {
        if (this.$.queryParameters.component) {
            const component = JSON.parse(
                this.$.queryParameters.component as string,
            ) as ui.PartialNodeWithId<ComponentExtension>;
            this.stockOnHand.value = +(component.stockOnHand || 0);
            this.availableQuantityInStockUnit.value = +(component.availableQuantityInStockUnit || 0);
            this.stockShortageInStockUnit.value = +(component.stockShortageInStockUnit || 0);
            this.stockShortageStatus.value =
                component.hasStockShortage !== undefined ? component.hasStockShortage.toString() : null;
        }
    },
})
export class BillOfMaterialComponentPanelExtension extends ui.PageExtension<BillOfMaterialComponentPanel, GraphApi> {
    extends: '@sage/xtrem-technical-data/BillOfMaterialComponentPanel';

    @ui.decorators.labelField<BillOfMaterialComponentPanelExtension>({
        title: 'Stock shortage status',
        parent() {
            return this.stockBlock;
        },
        backgroundColor: value =>
            PillColorStock.setBooleanStatusColors('stockShortageStatus', value, 'backgroundColor'),
        borderColor: value => PillColorStock.setBooleanStatusColors('stockShortageStatus', value, 'borderColor'),
        color: value => PillColorStock.setBooleanStatusColors('stockShortageStatus', value, 'textColor'),
        map: value =>
            value === 'true'
                ? ui.localize(
                      '@sage/xtrem-stock/pages__bill_of_material_component_panel_extension__display_shortage_status_true',
                      'Stock shortage',
                  )
                : ui.localize(
                      '@sage/xtrem-stock/pages__bill_of_material_component_panel_extension__display_shortage_status_false',
                      'Available stock',
                  ),
    })
    stockShortageStatus: ui.fields.Label;

    @ui.decorators.separatorField<BillOfMaterialComponentPanelExtension>({
        parent() {
            return this.stockBlock;
        },
        isFullWidth: true,
        isInvisible: true,
    })
    separatorShortage: ui.fields.Separator;

    @ui.decorators.numericField<BillOfMaterialComponentPanelExtension>({
        title: 'Stock on hand',
        parent() {
            return this.stockBlock;
        },
        isReadOnly: true,
    })
    stockOnHand: ui.fields.Numeric;

    @ui.decorators.numericField<BillOfMaterialComponentPanelExtension>({
        title: 'Available stock quantity',
        parent() {
            return this.stockBlock;
        },
        isReadOnly: true,
    })
    availableQuantityInStockUnit: ui.fields.Numeric;

    @ui.decorators.numericField<BillOfMaterialComponentPanelExtension>({
        title: 'Stock shortage',
        parent() {
            return this.stockBlock;
        },
        isReadOnly: true,
    })
    stockShortageInStockUnit: ui.fields.Numeric;
}

declare module '@sage/xtrem-technical-data/lib/pages/bill-of-material-component-panel' {
    export interface BillOfMaterialComponentPanel extends BillOfMaterialComponentPanelExtension {}
}
