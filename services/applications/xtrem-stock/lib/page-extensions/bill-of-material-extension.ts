import type { Graph<PERSON><PERSON> } from '@sage/xtrem-stock-data-api';
import type { Component } from '@sage/xtrem-technical-data-api';
import type { BillOfMaterial } from '@sage/xtrem-technical-data/build/lib/pages/bill-of-material';
import * as ui from '@sage/xtrem-ui';

@ui.decorators.pageExtension<BillOfMaterialExtension>({
    extends: '@sage/xtrem-technical-data/BillOfMaterial',
})
export class BillOfMaterialExtension extends ui.PageExtension<BillOfMaterial, GraphApi> {
    extends: '@sage/xtrem-technical-data/BillOfMaterial';

    @ui.decorators.tableFieldOverride<BillOfMaterialExtension, Component>({
        columns: [
            ui.nestedFieldExtensions.numeric({
                title: 'Available stock quantity',
                bind: 'availableQuantityInStockUnit',
                isReadOnly: true,
                isHiddenOnMainField: true,
            }),
            ui.nestedFieldExtensions.numeric({
                title: 'Stock shortage',
                bind: 'stockShortageInStockUnit',
                isReadOnly: true,
                isHiddenOnMainField: true,
            }),
            ui.nestedFieldExtensions.numeric({ bind: 'stockOnHand', isHidden: true }),
            ui.nestedFieldExtensions.checkbox({ isHidden: true, bind: 'hasStockShortage' }),
        ],
    })
    components: ui.fields.Table<Component>;
}

declare module '@sage/xtrem-technical-data/build/lib/pages/bill-of-material' {
    export interface BillOfMaterial extends BillOfMaterialExtension {}
}
