import { initDefaultDimensions } from '@sage/xtrem-finance-data/build/lib/client-functions/attributes-and-dimensions';
import type * as FinanceDataInterfaces from '@sage/xtrem-finance-data/build/lib/client-functions/interfaces';
import type * as financeInterfaces from '@sage/xtrem-finance-data/build/lib/client-functions/interfaces/dimension';
import * as utils from '@sage/xtrem-master-data/build/lib/client-functions/utils';
import type { ItemSiteCostPanel as ItemSiteCostPanelPage } from '@sage/xtrem-master-data/build/lib/pages/item-site-cost-panel';
import type { GraphApi } from '@sage/xtrem-stock-data-api';
import * as ui from '@sage/xtrem-ui';
import { noop } from 'lodash';

@ui.decorators.pageExtension<ItemSiteCostPanelExtension>({
    extends: '@sage/xtrem-master-data/ItemSiteCostPanel',
    async onLoad() {
        this.dimensionsButton.isDisabled = this.isCostFrozen();

        this._defaultDimensionsAttributes = await initDefaultDimensions({
            page: this,
            dimensionDefinitionLevel: 'stockDirect',
            site: this.itemSite.value?.site ?? null,
            item: this.itemSite.value?.item,
        });
    },
})
export class ItemSiteCostPanelExtension extends ui.PageExtension<ItemSiteCostPanelPage, GraphApi> {
    _defaultDimensionsAttributes: financeInterfaces.DefaultDimensions;

    @ui.decorators.textField({
        bind: 'storedAttributes',
        isHidden: true,
    })
    storedAttributes: ui.fields.Text;

    @ui.decorators.textField({
        bind: 'storedDimensions',
        isHidden: true,
    })
    storedDimensions: ui.fields.Text;

    @ui.decorators.buttonField<ItemSiteCostPanelExtension>({
        isTransient: true,
        parent() {
            return this.costBlock;
        },
        map() {
            return ui.localize('@sage/xtrem-stock/pages__item_site_cost__dimensions_button_text', 'Dimensions');
        },
        isHidden() {
            return !this.isStandardCostValuationMethod;
        },
        onError(error) {
            if (!(error instanceof Error) && typeof error !== 'string') {
                return noop();
            }
            return utils.formatError(this, error as any);
        },
        async onClick() {
            const newDefaults = (await new Promise((resolve, reject) => {
                this.$.dialog
                    .page(
                        '@sage/xtrem-finance-data/DimensionPanel',
                        {
                            enteredDimensions: JSON.stringify({
                                storedAttributes:
                                    this.storedAttributes.value || this._defaultDimensionsAttributes.attributes,
                                storedDimensions:
                                    this.storedDimensions.value || this._defaultDimensionsAttributes.dimensions,
                            }),
                            disabledAction: JSON.stringify({}),
                            editable: JSON.stringify(!this._id.value || !this.isCostFrozen()),
                            isDefaultDimensionPage: JSON.stringify(false),
                            isDimensionPageToInherit: JSON.stringify(false),
                            calledFromMainGrid: JSON.stringify(false),
                            defaultedFromItem: JSON.stringify({}),
                        },
                        {
                            size: 'large',
                        },
                    )
                    .then((resolveData: FinanceDataInterfaces.DefaultDimensions) => {
                        resolve(resolveData);
                    })
                    .catch(() => {
                        reject();
                    });
            })) as FinanceDataInterfaces.DefaultDimensions;
            if (newDefaults) {
                this.storedAttributes.value = newDefaults.attributes;
                this.storedDimensions.value = newDefaults.dimensions;
            }
        },
    })
    dimensionsButton: ui.fields.Button;
}

declare module '@sage/xtrem-master-data/build/lib/pages/item-site-cost-panel' {
    interface ItemSiteCostPanel extends ItemSiteCostPanelExtension {}
}
