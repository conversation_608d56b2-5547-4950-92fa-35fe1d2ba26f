import type { ItemSite as ItemSitePage } from '@sage/xtrem-master-data/build/lib/pages/item-site';
import type { GraphApi } from '@sage/xtrem-stock-data-api';
import * as ui from '@sage/xtrem-ui';

@ui.decorators.pageExtension<ItemSiteExtension>({
    extends: '@sage/xtrem-master-data/ItemSite',
    onLoad() {
        if (this.countingInProgress.value) {
            this.countingInProgressMention.value = this.countingInProgress.title || 'Counting in progress';
            this.countingInProgressMention.isHidden = false;
        }
    },
})
export class ItemSiteExtension extends ui.PageExtension<ItemSitePage, GraphApi> {
    @ui.decorators.dateField<ItemSiteExtension>({
        parent() {
            return this.stockBlock;
        },
        title: 'Last count date',
        width: 'medium',
        isReadOnly: true,
        isHidden() {
            return this.isStockNotManaged();
        },
    })
    lastCountDate: ui.fields.Date;

    @ui.decorators.checkboxField<ItemSiteExtension>({
        parent() {
            return this.stockBlock;
        },
        title: 'Counting in progress',
        isHidden: true,
    })
    countingInProgress: ui.fields.Checkbox;

    @ui.decorators.labelField<ItemSiteExtension>({
        isTransient: true,
        isHidden: true,
        title: 'Counting in progress',
        isTitleHidden: true,
        width: 'large',
        parent() {
            return this.stockBlock;
        },
        backgroundColor: ui.tokens.colorsSemanticCaution500,
        borderColor: ui.tokens.colorsSemanticCaution500,
        color: '#FFFFFF',
    })
    countingInProgressMention: ui.fields.Label;
}

declare module '@sage/xtrem-master-data/build/lib/pages/item-site' {
    interface ItemSite extends ItemSiteExtension {}
}
