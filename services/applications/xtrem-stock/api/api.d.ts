declare module '@sage/xtrem-stock-api-partial' {
    import type { Package as SageXtremAuditing$Package } from '@sage/xtrem-auditing-api';
    import type { Package as SageXtremAuthorization$Package } from '@sage/xtrem-authorization-api';
    import type { Package as SageXtremCommunication$Package } from '@sage/xtrem-communication-api';
    import type { Package as SageXtremCustomization$Package } from '@sage/xtrem-customization-api';
    import type { Package as SageXtremDashboard$Package } from '@sage/xtrem-dashboard-api';
    import type {
        AnalyticalData,
        FinanceTransaction,
        Package as SageXtremFinanceData$Package,
        PostingClass,
    } from '@sage/xtrem-finance-data-api';
    import type { Package as SageXtremImportExport$Package } from '@sage/xtrem-import-export-api';
    import type {
        LandedCostItem,
        LandedCostItemBinding,
        LandedCostItemInput,
        Package as SageXtremLandedCost$Package,
    } from '@sage/xtrem-landed-cost-api';
    import type { Package as SageXtremMailer$Package } from '@sage/xtrem-mailer-api';
    import type {
        BaseDocumentLine,
        BomRevisionSequence,
        CostCategory,
        Currency,
        IndirectCostSection,
        Item,
        ItemAllergen,
        ItemAllergenBinding,
        ItemAllergenInput,
        ItemCategory,
        ItemClassifications,
        ItemClassificationsBinding,
        ItemClassificationsInput,
        ItemCustomer,
        ItemCustomerInput,
        ItemCustomerPrice,
        ItemCustomerPriceBinding,
        ItemCustomerPriceInput,
        ItemSite,
        ItemSiteBinding,
        ItemSiteCost,
        ItemSiteCostInput,
        ItemSiteInput,
        ItemSiteSupplier,
        ItemSupplier,
        ItemSupplierInput,
        ItemSupplierPrice,
        ItemSupplierPriceBinding,
        ItemSupplierPriceInput,
        Location,
        LocationZone,
        Package as SageXtremMasterData$Package,
        ReasonCode,
        SequenceNumber,
        Supplier,
        SupplierBinding,
        UnitOfMeasure,
    } from '@sage/xtrem-master-data-api';
    import type { MetaNodeFactory, Package as SageXtremMetadata$Package } from '@sage/xtrem-metadata-api';
    import type {
        MrpInputSet,
        MrpResultLine,
        MrpWorkLine,
        Package as SageXtremMrpData$Package,
    } from '@sage/xtrem-mrp-data-api';
    import type { Package as SageXtremReporting$Package } from '@sage/xtrem-reporting-api';
    import type { Package as SageXtremRouting$Package } from '@sage/xtrem-routing-api';
    import type { Package as SageXtremScheduler$Package } from '@sage/xtrem-scheduler-api';
    import type {
        FifoValuationTier,
        Lot,
        Package as SageXtremStockData$Package,
        SerialNumber,
        Stock,
        StockAdjustmentDetail,
        StockAdjustmentDetailBinding,
        StockAdjustmentDetailInput,
        StockAllocation,
        StockChangeDetail,
        StockChangeDetailBinding,
        StockChangeDetailInput,
        StockCorrectionDetail,
        StockCorrectionDetailBinding,
        StockCorrectionDetailInput,
        StockDetailLot,
        StockDetailLotBinding,
        StockDetailLotInput,
        StockDetailSerialNumber,
        StockDetailSerialNumberBinding,
        StockDetailSerialNumberInput,
        StockIssueDetail,
        StockIssueDetailBinding,
        StockIssueDetailInput,
        StockJournal,
        StockJournalSerialNumber,
        StockMovementDetail,
        StockReceiptDetail,
        StockReceiptDetailBinding,
        StockReceiptDetailInput,
        StockStatus,
        StockTransaction,
        StockTransactionBinding,
        StockTransactionInput,
        StockValueDetail,
        StockValueDetailBinding,
        StockValueDetailInput,
    } from '@sage/xtrem-stock-data-api';
    import type { Package as SageXtremStructure$Package } from '@sage/xtrem-structure-api';
    import type { Package as SageXtremSynchronization$Package } from '@sage/xtrem-synchronization-api';
    import type { Company, Package as SageXtremSystem$Package, Site, User } from '@sage/xtrem-system-api';
    import type { ItemTaxGroup, Package as SageXtremTax$Package } from '@sage/xtrem-tax-api';
    import type {
        BillOfMaterial,
        BillOfMaterialRevision,
        Operation,
        Package as SageXtremTechnicalData$Package,
        Routing,
    } from '@sage/xtrem-technical-data-api';
    import type {
        AttachmentAssociation,
        AttachmentAssociationInput,
        Package as SageXtremUpload$Package,
    } from '@sage/xtrem-upload-api';
    import type { Package as SageXtremWorkflow$Package } from '@sage/xtrem-workflow-api';
    import type {
        AggregateQueryOperation,
        AggregateReadOperation,
        AsyncOperation,
        BinaryStream,
        ClientCollection,
        ClientNode,
        ClientNodeInput,
        CreateOperation,
        DeleteOperation,
        GetDefaultsOperation,
        GetDuplicateOperation,
        Operation as Node$Operation,
        QueryOperation,
        ReadOperation,
        TextStream,
        UpdateByIdOperation,
        UpdateOperation,
        VitalClientNode,
        VitalClientNodeInput,
        decimal,
        integer,
    } from '@sage/xtrem-client';
    export interface AdjustableDocumentType$Enum {
        stockReceipt: 1;
        purchaseReceipt: 2;
    }
    export type AdjustableDocumentType = keyof AdjustableDocumentType$Enum;
    export interface StandardCostRollUpResultLineStatus$Enum {
        pending: 0;
        inProgress: 1;
        created: 2;
        updated: 3;
        error: 4;
    }
    export type StandardCostRollUpResultLineStatus = keyof StandardCostRollUpResultLineStatus$Enum;
    export interface StandardCostRollUpStatus$Enum {
        draft: 0;
        inProgress: 1;
        completed: 2;
        error: 3;
    }
    export type StandardCostRollUpStatus = keyof StandardCostRollUpStatus$Enum;
    export interface StockAdjustmentDisplayStatus$Enum {
        detailsRequired: 0;
        detailsEntered: 1;
        stockPostingInProgress: 2;
        stockPostingError: 3;
        adjusted: 4;
    }
    export type StockAdjustmentDisplayStatus = keyof StockAdjustmentDisplayStatus$Enum;
    export interface StockCountLineStatus$Enum {
        toBeCounted: 0;
        countInProgress: 1;
        counted: 2;
        excluded: 3;
    }
    export type StockCountLineStatus = keyof StockCountLineStatus$Enum;
    export interface StockCountStatus$Enum {
        toBeCounted: 0;
        countInProgress: 1;
        counted: 2;
        closed: 3;
        draft: 4;
    }
    export type StockCountStatus = keyof StockCountStatus$Enum;
    export interface StockIssueDisplayStatus$Enum {
        detailsRequired: 0;
        detailsEntered: 1;
        stockPostingInProgress: 2;
        stockPostingError: 3;
        issued: 4;
    }
    export type StockIssueDisplayStatus = keyof StockIssueDisplayStatus$Enum;
    export interface StockIssueLineDisplayStatus$Enum {
        detailsRequired: 0;
        detailsEntered: 1;
        stockPostingInProgress: 2;
        stockPostingError: 3;
        issued: 4;
    }
    export type StockIssueLineDisplayStatus = keyof StockIssueLineDisplayStatus$Enum;
    export interface StockReceiptDisplayStatus$Enum {
        detailsRequired: 0;
        detailsEntered: 1;
        stockPostingInProgress: 2;
        stockPostingError: 3;
        received: 4;
    }
    export type StockReceiptDisplayStatus = keyof StockReceiptDisplayStatus$Enum;
    export interface StockReceiptLineDisplayStatus$Enum {
        detailsRequired: 0;
        detailsEntered: 1;
        stockPostingInProgress: 2;
        stockPostingError: 3;
        received: 4;
    }
    export type StockReceiptLineDisplayStatus = keyof StockReceiptLineDisplayStatus$Enum;
    export interface StockValuationStatus$Enum {
        inProgress: 1;
        completed: 2;
        draft: 3;
        error: 4;
    }
    export type StockValuationStatus = keyof StockValuationStatus$Enum;
    export interface VirtualLocationNeed$Enum {
        transfer: 0;
    }
    export type VirtualLocationNeed = keyof VirtualLocationNeed$Enum;
    export interface AllocationListener extends ClientNode {}
    export interface AllocationListenerInput extends ClientNodeInput {}
    export interface AllocationListenerBinding extends ClientNode {}
    export interface CostRollUpInputSet extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        user: User;
        site: Site;
        items: Item[];
        itemCategory: ItemCategory;
        commodityCode: string;
        costCategory: CostCategory;
        fromDate: string;
        quantity: string;
        includesRouting: boolean;
        usesComponentStandardCost: boolean;
        status: StandardCostRollUpStatus;
        resultLines: ClientCollection<CostRollUpResultLine>;
    }
    export interface CostRollUpInputSetInput extends ClientNodeInput {
        user?: integer | string;
        site?: integer | string;
        items?: (integer | string)[];
        itemCategory?: integer | string;
        commodityCode?: string;
        costCategory?: integer | string;
        fromDate?: string;
        quantity?: decimal | string;
        includesRouting?: boolean | string;
        usesComponentStandardCost?: boolean | string;
        status?: StandardCostRollUpStatus;
        resultLines?: Partial<CostRollUpResultLineInput>[];
    }
    export interface CostRollUpInputSetBinding extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        user: User;
        site: Site;
        items: Item[];
        itemCategory: ItemCategory;
        commodityCode: string;
        costCategory: CostCategory;
        fromDate: string;
        quantity: string;
        includesRouting: boolean;
        usesComponentStandardCost: boolean;
        status: StandardCostRollUpStatus;
        resultLines: ClientCollection<CostRollUpResultLineBinding>;
    }
    export interface CostRollUpInputSet$Mutations {
        updateAttributesAndDimensions: Node$Operation<
            {
                costRollUpInputSet: string;
                storedDimensions?: string;
                storedAttributes?: string;
            },
            boolean
        >;
    }
    export interface CostRollUpInputSet$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface CostRollUpInputSet$Lookups {
        user: QueryOperation<User>;
        site: QueryOperation<Site>;
        itemCategory: QueryOperation<ItemCategory>;
        costCategory: QueryOperation<CostCategory>;
    }
    export interface CostRollUpInputSet$Operations {
        query: QueryOperation<CostRollUpInputSet>;
        read: ReadOperation<CostRollUpInputSet>;
        aggregate: {
            read: AggregateReadOperation<CostRollUpInputSet>;
            query: AggregateQueryOperation<CostRollUpInputSet>;
        };
        create: CreateOperation<CostRollUpInputSetInput, CostRollUpInputSet>;
        getDuplicate: GetDuplicateOperation<CostRollUpInputSet>;
        update: UpdateOperation<CostRollUpInputSetInput, CostRollUpInputSet>;
        updateById: UpdateByIdOperation<CostRollUpInputSetInput, CostRollUpInputSet>;
        delete: DeleteOperation<{}>;
        deleteById: DeleteOperation<string>;
        mutations: CostRollUpInputSet$Mutations;
        asyncOperations: CostRollUpInputSet$AsyncOperations;
        lookups(dataOrId: string | { data: CostRollUpInputSetInput }): CostRollUpInputSet$Lookups;
        getDefaults: GetDefaultsOperation<CostRollUpInputSet>;
    }
    export interface CostRollUpResultLine extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        inputSet: CostRollUpInputSet;
        creationStatus: StandardCostRollUpResultLineStatus;
        item: Item;
        currentMaterialCost: string;
        materialCost: string;
        currentMachineCost: string;
        machineCost: string;
        currentLaborCost: string;
        laborCost: string;
        currentToolCost: string;
        toolCost: string;
        currentTotalCost: string;
        totalCost: string;
        storedDimensions: string;
        storedAttributes: string;
        creationErrorMessage: string;
        subAssemblyLines: ClientCollection<CostRollUpSubAssembly>;
        analyticalData: AnalyticalData;
    }
    export interface CostRollUpResultLineInput extends VitalClientNodeInput {
        creationStatus?: StandardCostRollUpResultLineStatus;
        item?: integer | string;
        currentMaterialCost?: decimal | string;
        materialCost?: decimal | string;
        currentMachineCost?: decimal | string;
        machineCost?: decimal | string;
        currentLaborCost?: decimal | string;
        laborCost?: decimal | string;
        currentToolCost?: decimal | string;
        toolCost?: decimal | string;
        storedDimensions?: string;
        storedAttributes?: string;
        creationErrorMessage?: string;
        subAssemblyLines?: Partial<CostRollUpSubAssemblyInput>[];
        analyticalData?: integer | string;
    }
    export interface CostRollUpResultLineBinding extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        inputSet: CostRollUpInputSet;
        creationStatus: StandardCostRollUpResultLineStatus;
        item: Item;
        currentMaterialCost: string;
        materialCost: string;
        currentMachineCost: string;
        machineCost: string;
        currentLaborCost: string;
        laborCost: string;
        currentToolCost: string;
        toolCost: string;
        currentTotalCost: string;
        totalCost: string;
        storedDimensions: any;
        storedAttributes: any;
        creationErrorMessage: string;
        subAssemblyLines: ClientCollection<CostRollUpSubAssemblyBinding>;
        analyticalData: AnalyticalData;
    }
    export interface CostRollUpResultLine$AsyncOperations {
        createItemSiteCostsFromCostRollUpResults: AsyncOperation<
            {
                filter?: string;
            },
            boolean
        >;
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface CostRollUpResultLine$Lookups {
        item: QueryOperation<Item>;
        analyticalData: QueryOperation<AnalyticalData>;
    }
    export interface CostRollUpResultLine$Operations {
        query: QueryOperation<CostRollUpResultLine>;
        read: ReadOperation<CostRollUpResultLine>;
        aggregate: {
            read: AggregateReadOperation<CostRollUpResultLine>;
            query: AggregateQueryOperation<CostRollUpResultLine>;
        };
        asyncOperations: CostRollUpResultLine$AsyncOperations;
        lookups(dataOrId: string | { data: CostRollUpResultLineInput }): CostRollUpResultLine$Lookups;
        getDefaults: GetDefaultsOperation<CostRollUpResultLine>;
    }
    export interface CostRollUpSubAssembly extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        resultLine: CostRollUpResultLine;
        componentNumber: integer;
        item: Item;
        calculationQuantity: string;
        stockUnit: UnitOfMeasure;
        currentMaterialCost: string;
        materialCost: string;
        currentMachineCost: string;
        machineCost: string;
        currentLaborCost: string;
        laborCost: string;
        currentToolCost: string;
        toolCost: string;
        currentTotalCost: string;
        totalCost: string;
    }
    export interface CostRollUpSubAssemblyInput extends VitalClientNodeInput {
        componentNumber?: integer | string;
        item?: integer | string;
        calculationQuantity?: decimal | string;
        currentMaterialCost?: decimal | string;
        materialCost?: decimal | string;
        currentMachineCost?: decimal | string;
        machineCost?: decimal | string;
        currentLaborCost?: decimal | string;
        laborCost?: decimal | string;
        currentToolCost?: decimal | string;
        toolCost?: decimal | string;
    }
    export interface CostRollUpSubAssemblyBinding extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        resultLine: CostRollUpResultLine;
        componentNumber: integer;
        item: Item;
        calculationQuantity: string;
        stockUnit: UnitOfMeasure;
        currentMaterialCost: string;
        materialCost: string;
        currentMachineCost: string;
        machineCost: string;
        currentLaborCost: string;
        laborCost: string;
        currentToolCost: string;
        toolCost: string;
        currentTotalCost: string;
        totalCost: string;
    }
    export interface CostRollUpSubAssembly$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface CostRollUpSubAssembly$Lookups {
        item: QueryOperation<Item>;
        stockUnit: QueryOperation<UnitOfMeasure>;
    }
    export interface CostRollUpSubAssembly$Operations {
        query: QueryOperation<CostRollUpSubAssembly>;
        read: ReadOperation<CostRollUpSubAssembly>;
        aggregate: {
            read: AggregateReadOperation<CostRollUpSubAssembly>;
            query: AggregateQueryOperation<CostRollUpSubAssembly>;
        };
        asyncOperations: CostRollUpSubAssembly$AsyncOperations;
        lookups(dataOrId: string | { data: CostRollUpSubAssemblyInput }): CostRollUpSubAssembly$Lookups;
        getDefaults: GetDefaultsOperation<CostRollUpSubAssembly>;
    }
    export interface StockAdjustment extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        number: string;
        effectiveDate: string;
        stockSite: Site;
        reasonCode: ReasonCode;
        lines: ClientCollection<StockAdjustmentLine>;
        isSetDimensionsMainListHidden: boolean;
        postingDetails: ClientCollection<FinanceTransaction>;
        description: string;
        financeIntegrationStatus: FinanceIntegrationStatus;
        stockTransactionStatus: StockDocumentTransactionStatus;
        financialSite: Site;
        transactionCurrency: Currency;
        documentDate: string;
        _attachments: ClientCollection<AttachmentAssociation>;
        status: StockDocumentStatus;
        displayStatus: StockAdjustmentDisplayStatus;
    }
    export interface StockAdjustmentInput extends ClientNodeInput {
        number?: string;
        effectiveDate?: string;
        stockSite?: integer | string;
        reasonCode?: integer | string;
        lines?: Partial<StockAdjustmentLineInput>[];
        description?: string;
        _attachments?: Partial<AttachmentAssociationInput>[];
    }
    export interface StockAdjustmentBinding extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        number: string;
        effectiveDate: string;
        stockSite: Site;
        reasonCode: ReasonCode;
        lines: ClientCollection<StockAdjustmentLineBinding>;
        isSetDimensionsMainListHidden: boolean;
        postingDetails: ClientCollection<FinanceTransaction>;
        description: string;
        financeIntegrationStatus: FinanceIntegrationStatus;
        stockTransactionStatus: StockDocumentTransactionStatus;
        financialSite: Site;
        transactionCurrency: Currency;
        documentDate: string;
        _attachments: ClientCollection<AttachmentAssociation>;
        status: StockDocumentStatus;
        displayStatus: StockAdjustmentDisplayStatus;
    }
    export interface StockAdjustment$Mutations {
        postToStock: Node$Operation<
            {
                documentIds?: (integer | string)[];
            },
            string
        >;
        repost: Node$Operation<
            {
                stockAdjustment: string;
                documentLines?: {
                    baseDocumentLineSysId?: integer | string;
                    storedAttributes?: string;
                    storedDimensions?: string;
                }[];
            },
            {
                wasSuccessful: boolean;
                message: string;
            }
        >;
        resynchronizeStockTransactionStatus: Node$Operation<
            {
                stockAdjustment: string;
            },
            boolean
        >;
        resendNotificationForFinance: Node$Operation<
            {
                stockAdjustment: string;
            },
            boolean
        >;
    }
    export interface StockAdjustment$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface StockAdjustment$Lookups {
        stockSite: QueryOperation<Site>;
        reasonCode: QueryOperation<ReasonCode>;
        financialSite: QueryOperation<Site>;
        transactionCurrency: QueryOperation<Currency>;
    }
    export interface StockAdjustment$Operations {
        query: QueryOperation<StockAdjustment>;
        read: ReadOperation<StockAdjustment>;
        aggregate: {
            read: AggregateReadOperation<StockAdjustment>;
            query: AggregateQueryOperation<StockAdjustment>;
        };
        create: CreateOperation<StockAdjustmentInput, StockAdjustment>;
        getDuplicate: GetDuplicateOperation<StockAdjustment>;
        update: UpdateOperation<StockAdjustmentInput, StockAdjustment>;
        updateById: UpdateByIdOperation<StockAdjustmentInput, StockAdjustment>;
        delete: DeleteOperation<{}>;
        deleteById: DeleteOperation<string>;
        mutations: StockAdjustment$Mutations;
        asyncOperations: StockAdjustment$AsyncOperations;
        lookups(dataOrId: string | { data: StockAdjustmentInput }): StockAdjustment$Lookups;
        getDefaults: GetDefaultsOperation<StockAdjustment>;
    }
    export interface StockChange extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        number: string;
        effectiveDate: string;
        stockSite: Site;
        description: string;
        item: Item;
        stockStatus: StockStatus;
        location: Location;
        lot: Lot;
        owner: string;
        stockUnit: UnitOfMeasure;
        lines: ClientCollection<StockChangeLine>;
        _attachments: ClientCollection<AttachmentAssociation>;
        stockTransactionStatus: StockDocumentTransactionStatus;
        status: StockDocumentStatus;
    }
    export interface StockChangeInput extends ClientNodeInput {
        number?: string;
        effectiveDate?: string;
        stockSite?: integer | string;
        description?: string;
        item?: integer | string;
        stockStatus?: integer | string;
        location?: integer | string;
        lot?: integer | string;
        owner?: string;
        lines?: Partial<StockChangeLineInput>[];
        _attachments?: Partial<AttachmentAssociationInput>[];
        stockTransactionStatus?: StockDocumentTransactionStatus;
    }
    export interface StockChangeBinding extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        number: string;
        effectiveDate: string;
        stockSite: Site;
        description: string;
        item: Item;
        stockStatus: StockStatus;
        location: Location;
        lot: Lot;
        owner: string;
        stockUnit: UnitOfMeasure;
        lines: ClientCollection<StockChangeLineBinding>;
        _attachments: ClientCollection<AttachmentAssociation>;
        stockTransactionStatus: StockDocumentTransactionStatus;
        status: StockDocumentStatus;
    }
    export interface StockChange$Mutations {
        postToStock: Node$Operation<
            {
                documentIds?: (integer | string)[];
            },
            string
        >;
    }
    export interface StockChange$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface StockChange$Lookups {
        stockSite: QueryOperation<Site>;
        item: QueryOperation<Item>;
        stockStatus: QueryOperation<StockStatus>;
        location: QueryOperation<Location>;
        lot: QueryOperation<Lot>;
        stockUnit: QueryOperation<UnitOfMeasure>;
    }
    export interface StockChange$Operations {
        query: QueryOperation<StockChange>;
        read: ReadOperation<StockChange>;
        aggregate: {
            read: AggregateReadOperation<StockChange>;
            query: AggregateQueryOperation<StockChange>;
        };
        create: CreateOperation<StockChangeInput, StockChange>;
        getDuplicate: GetDuplicateOperation<StockChange>;
        update: UpdateOperation<StockChangeInput, StockChange>;
        updateById: UpdateByIdOperation<StockChangeInput, StockChange>;
        delete: DeleteOperation<{}>;
        deleteById: DeleteOperation<string>;
        mutations: StockChange$Mutations;
        asyncOperations: StockChange$AsyncOperations;
        lookups(dataOrId: string | { data: StockChangeInput }): StockChange$Lookups;
        getDefaults: GetDefaultsOperation<StockChange>;
    }
    export interface StockCount extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        number: string;
        description: string;
        stockSite: Site;
        status: StockCountStatus;
        effectiveDate: string;
        financeIntegrationStatus: FinanceIntegrationStatus;
        counter: string;
        lastCountDate: string;
        fromItem: Item;
        toItem: Item;
        locations: Location[];
        zones: LocationZone[];
        categories: ItemCategory[];
        lines: ClientCollection<StockCountLine>;
        financialSite: Site;
        transactionCurrency: Currency;
        documentDate: string;
        hasStockRecords: boolean;
        hasLotInLines: boolean;
        hasSublotInLines: boolean;
        hasExpiryManagementInLines: boolean;
        postingDetails: ClientCollection<FinanceTransaction>;
        _attachments: ClientCollection<AttachmentAssociation>;
        stockTransactionStatus: StockDocumentTransactionStatus;
        itemSites: ClientCollection<ItemSite>;
    }
    export interface StockCountInput extends ClientNodeInput {
        number?: string;
        description?: string;
        stockSite?: integer | string;
        status?: StockCountStatus;
        effectiveDate?: string;
        counter?: string;
        lastCountDate?: string;
        fromItem?: integer | string;
        toItem?: integer | string;
        locations?: (integer | string)[];
        zones?: (integer | string)[];
        categories?: (integer | string)[];
        lines?: Partial<StockCountLineInput>[];
        hasStockRecords?: boolean | string;
        _attachments?: Partial<AttachmentAssociationInput>[];
        stockTransactionStatus?: StockDocumentTransactionStatus;
    }
    export interface StockCountBinding extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        number: string;
        description: string;
        stockSite: Site;
        status: StockCountStatus;
        effectiveDate: string;
        financeIntegrationStatus: FinanceIntegrationStatus;
        counter: string;
        lastCountDate: string;
        fromItem: Item;
        toItem: Item;
        locations: Location[];
        zones: LocationZone[];
        categories: ItemCategory[];
        lines: ClientCollection<StockCountLineBinding>;
        financialSite: Site;
        transactionCurrency: Currency;
        documentDate: string;
        hasStockRecords: boolean;
        hasLotInLines: boolean;
        hasSublotInLines: boolean;
        hasExpiryManagementInLines: boolean;
        postingDetails: ClientCollection<FinanceTransaction>;
        _attachments: ClientCollection<AttachmentAssociation>;
        stockTransactionStatus: StockDocumentTransactionStatus;
        itemSites: ClientCollection<ItemSite>;
    }
    export interface StockCount$Queries {
        existingStockCountLine: Node$Operation<
            {
                throwErrorIfExist: boolean | string;
                searchCriteria: {
                    item: integer | string;
                    stockStatus: integer | string;
                    stockUnit: integer | string;
                    owner: string;
                    document: {
                        stockSite: integer | string;
                    };
                    location?: integer | string;
                    lot?: integer | string;
                };
                lotCreateData?: {
                    id: string;
                    sublot?: string | null;
                };
            },
            boolean
        >;
    }
    export interface StockCount$Mutations {
        repost: Node$Operation<
            {
                stockCount: string;
                documentLines?: {
                    baseDocumentLineSysId?: integer | string;
                    storedAttributes?: string;
                    storedDimensions?: string;
                }[];
            },
            {
                wasSuccessful: boolean;
                message: string;
            }
        >;
        start: Node$Operation<
            {
                document: string;
            },
            boolean
        >;
        postToStock: Node$Operation<
            {
                documentIds?: (integer | string)[];
            },
            string
        >;
        resendNotificationForFinance: Node$Operation<
            {
                stockCount: string;
            },
            boolean
        >;
    }
    export interface StockCount$AsyncOperations {
        confirmZeroQuantity: AsyncOperation<
            {
                number?: string;
            },
            integer
        >;
        confirmStockCount: AsyncOperation<
            {
                stockCount: string;
                selectedRecords: {
                    stockRecords?: (integer | string)[];
                    itemSites?: (integer | string)[];
                    allSelected: boolean | string;
                };
            },
            boolean
        >;
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface StockCount$Lookups {
        stockSite: QueryOperation<Site>;
        fromItem: QueryOperation<Item>;
        toItem: QueryOperation<Item>;
        financialSite: QueryOperation<Site>;
        transactionCurrency: QueryOperation<Currency>;
    }
    export interface StockCount$Operations {
        query: QueryOperation<StockCount>;
        read: ReadOperation<StockCount>;
        aggregate: {
            read: AggregateReadOperation<StockCount>;
            query: AggregateQueryOperation<StockCount>;
        };
        queries: StockCount$Queries;
        create: CreateOperation<StockCountInput, StockCount>;
        getDuplicate: GetDuplicateOperation<StockCount>;
        update: UpdateOperation<StockCountInput, StockCount>;
        updateById: UpdateByIdOperation<StockCountInput, StockCount>;
        delete: DeleteOperation<{}>;
        deleteById: DeleteOperation<string>;
        mutations: StockCount$Mutations;
        asyncOperations: StockCount$AsyncOperations;
        lookups(dataOrId: string | { data: StockCountInput }): StockCount$Lookups;
        getDefaults: GetDefaultsOperation<StockCount>;
    }
    export interface StockCountLineSerialNumber extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        stockCountLine: StockCountLine;
        serialNumber: SerialNumber;
        isCounted: boolean;
    }
    export interface StockCountLineSerialNumberInput extends VitalClientNodeInput {
        serialNumber?: integer | string;
        isCounted?: boolean | string;
    }
    export interface StockCountLineSerialNumberBinding extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        stockCountLine: StockCountLine;
        serialNumber: SerialNumber;
        isCounted: boolean;
    }
    export interface StockCountLineSerialNumber$Queries {
        getEndingSerialNumber: Node$Operation<
            {
                stockCountLineId?: integer | string;
                startingSerialNumber?: string;
                quantity?: decimal | string;
            },
            string
        >;
    }
    export interface StockCountLineSerialNumber$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface StockCountLineSerialNumber$Lookups {
        serialNumber: QueryOperation<SerialNumber>;
    }
    export interface StockCountLineSerialNumber$Operations {
        query: QueryOperation<StockCountLineSerialNumber>;
        read: ReadOperation<StockCountLineSerialNumber>;
        aggregate: {
            read: AggregateReadOperation<StockCountLineSerialNumber>;
            query: AggregateQueryOperation<StockCountLineSerialNumber>;
        };
        queries: StockCountLineSerialNumber$Queries;
        asyncOperations: StockCountLineSerialNumber$AsyncOperations;
        lookups(dataOrId: string | { data: StockCountLineSerialNumberInput }): StockCountLineSerialNumber$Lookups;
        getDefaults: GetDefaultsOperation<StockCountLineSerialNumber>;
    }
    export interface StockIssue extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        number: string;
        description: string;
        effectiveDate: string;
        stockSite: Site;
        lines: ClientCollection<StockIssueLine>;
        postingDetails: ClientCollection<FinanceTransaction>;
        financialSite: Site;
        transactionCurrency: Currency;
        documentDate: string;
        financeIntegrationStatus: FinanceIntegrationStatus;
        reasonCode: ReasonCode;
        isSetDimensionsMainListHidden: boolean;
        _attachments: ClientCollection<AttachmentAssociation>;
        stockTransactionStatus: StockDocumentTransactionStatus;
        status: StockDocumentStatus;
        displayStatus: StockIssueDisplayStatus;
    }
    export interface StockIssueInput extends ClientNodeInput {
        number?: string;
        description?: string;
        effectiveDate?: string;
        stockSite?: integer | string;
        lines?: Partial<StockIssueLineInput>[];
        reasonCode?: integer | string;
        _attachments?: Partial<AttachmentAssociationInput>[];
        displayStatus?: StockIssueDisplayStatus;
    }
    export interface StockIssueBinding extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        number: string;
        description: string;
        effectiveDate: string;
        stockSite: Site;
        lines: ClientCollection<StockIssueLineBinding>;
        postingDetails: ClientCollection<FinanceTransaction>;
        financialSite: Site;
        transactionCurrency: Currency;
        documentDate: string;
        financeIntegrationStatus: FinanceIntegrationStatus;
        reasonCode: ReasonCode;
        isSetDimensionsMainListHidden: boolean;
        _attachments: ClientCollection<AttachmentAssociation>;
        stockTransactionStatus: StockDocumentTransactionStatus;
        status: StockDocumentStatus;
        displayStatus: StockIssueDisplayStatus;
    }
    export interface StockIssue$Mutations {
        postToStock: Node$Operation<
            {
                documentIds?: (integer | string)[];
            },
            string
        >;
        repost: Node$Operation<
            {
                stockIssue: string;
                documentLines?: {
                    baseDocumentLineSysId?: integer | string;
                    storedAttributes?: string;
                    storedDimensions?: string;
                }[];
            },
            {
                wasSuccessful: boolean;
                message: string;
            }
        >;
        resendNotificationForFinance: Node$Operation<
            {
                stockIssue: string;
            },
            boolean
        >;
    }
    export interface StockIssue$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface StockIssue$Lookups {
        stockSite: QueryOperation<Site>;
        financialSite: QueryOperation<Site>;
        transactionCurrency: QueryOperation<Currency>;
        reasonCode: QueryOperation<ReasonCode>;
    }
    export interface StockIssue$Operations {
        query: QueryOperation<StockIssue>;
        read: ReadOperation<StockIssue>;
        aggregate: {
            read: AggregateReadOperation<StockIssue>;
            query: AggregateQueryOperation<StockIssue>;
        };
        create: CreateOperation<StockIssueInput, StockIssue>;
        getDuplicate: GetDuplicateOperation<StockIssue>;
        update: UpdateOperation<StockIssueInput, StockIssue>;
        updateById: UpdateByIdOperation<StockIssueInput, StockIssue>;
        delete: DeleteOperation<{}>;
        deleteById: DeleteOperation<string>;
        mutations: StockIssue$Mutations;
        asyncOperations: StockIssue$AsyncOperations;
        lookups(dataOrId: string | { data: StockIssueInput }): StockIssue$Lookups;
        getDefaults: GetDefaultsOperation<StockIssue>;
    }
    export interface StockReceipt extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        number: string;
        description: string;
        effectiveDate: string;
        stockSite: Site;
        lines: ClientCollection<StockReceiptLine>;
        postingDetails: ClientCollection<FinanceTransaction>;
        isSetDimensionsMainListHidden: boolean;
        financialSite: Site;
        transactionCurrency: Currency;
        documentDate: string;
        financeIntegrationStatus: FinanceIntegrationStatus;
        reasonCode: ReasonCode;
        _attachments: ClientCollection<AttachmentAssociation>;
        stockTransactionStatus: StockDocumentTransactionStatus;
        status: StockDocumentStatus;
        displayStatus: StockReceiptDisplayStatus;
    }
    export interface StockReceiptInput extends ClientNodeInput {
        number?: string;
        description?: string;
        effectiveDate?: string;
        stockSite?: integer | string;
        lines?: Partial<StockReceiptLineInput>[];
        reasonCode?: integer | string;
        _attachments?: Partial<AttachmentAssociationInput>[];
        stockTransactionStatus?: StockDocumentTransactionStatus;
        displayStatus?: StockReceiptDisplayStatus;
    }
    export interface StockReceiptBinding extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        number: string;
        description: string;
        effectiveDate: string;
        stockSite: Site;
        lines: ClientCollection<StockReceiptLineBinding>;
        postingDetails: ClientCollection<FinanceTransaction>;
        isSetDimensionsMainListHidden: boolean;
        financialSite: Site;
        transactionCurrency: Currency;
        documentDate: string;
        financeIntegrationStatus: FinanceIntegrationStatus;
        reasonCode: ReasonCode;
        _attachments: ClientCollection<AttachmentAssociation>;
        stockTransactionStatus: StockDocumentTransactionStatus;
        status: StockDocumentStatus;
        displayStatus: StockReceiptDisplayStatus;
    }
    export interface StockReceipt$Mutations {
        postToStock: Node$Operation<
            {
                documentIds?: (integer | string)[];
            },
            string
        >;
        repost: Node$Operation<
            {
                stockReceipt: string;
                documentLines?: {
                    baseDocumentLineSysId?: integer | string;
                    storedAttributes?: string;
                    storedDimensions?: string;
                }[];
            },
            {
                wasSuccessful: boolean;
                message: string;
            }
        >;
        resendNotificationForFinance: Node$Operation<
            {
                stockReceipt: string;
            },
            boolean
        >;
    }
    export interface StockReceipt$AsyncOperations {
        createTestStockReceipt: AsyncOperation<
            {
                data?: {
                    site: integer | string;
                    nbOfLines?: integer | string;
                    maxNbOfLines?: integer | string;
                    items?: string[];
                    itemRange?: {
                        from: integer | string;
                        to: integer | string;
                    };
                    useRandomItemFromSite?: boolean | string;
                    useRandomQuantities?: boolean | string;
                };
            },
            StockReceipt
        >;
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface StockReceipt$Lookups {
        stockSite: QueryOperation<Site>;
        financialSite: QueryOperation<Site>;
        transactionCurrency: QueryOperation<Currency>;
        reasonCode: QueryOperation<ReasonCode>;
    }
    export interface StockReceipt$Operations {
        query: QueryOperation<StockReceipt>;
        read: ReadOperation<StockReceipt>;
        aggregate: {
            read: AggregateReadOperation<StockReceipt>;
            query: AggregateQueryOperation<StockReceipt>;
        };
        create: CreateOperation<StockReceiptInput, StockReceipt>;
        getDuplicate: GetDuplicateOperation<StockReceipt>;
        update: UpdateOperation<StockReceiptInput, StockReceipt>;
        updateById: UpdateByIdOperation<StockReceiptInput, StockReceipt>;
        delete: DeleteOperation<{}>;
        deleteById: DeleteOperation<string>;
        mutations: StockReceipt$Mutations;
        asyncOperations: StockReceipt$AsyncOperations;
        lookups(dataOrId: string | { data: StockReceiptInput }): StockReceipt$Lookups;
        getDefaults: GetDefaultsOperation<StockReceipt>;
    }
    export interface StockReorderCalculationInputSet extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        user: User;
        company: Company;
        sites: Site[];
        fromItem: Item;
        toItem: Item;
        reorderType: PreferredProcess;
        endDate: string;
        lines: ClientCollection<StockReorderCalculationResultLine>;
        status: StockValuationStatus;
    }
    export interface StockReorderCalculationInputSetInput extends ClientNodeInput {
        user?: integer | string;
        company?: integer | string;
        sites?: (integer | string)[];
        fromItem?: integer | string;
        toItem?: integer | string;
        reorderType?: PreferredProcess;
        endDate?: string;
        lines?: Partial<StockReorderCalculationResultLineInput>[];
        status?: StockValuationStatus;
    }
    export interface StockReorderCalculationInputSetBinding extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        user: User;
        company: Company;
        sites: Site[];
        fromItem: Item;
        toItem: Item;
        reorderType: PreferredProcess;
        endDate: string;
        lines: ClientCollection<StockReorderCalculationResultLineBinding>;
        status: StockValuationStatus;
    }
    export interface StockReorderCalculationInputSet$AsyncOperations {
        reorderCalculation: AsyncOperation<
            {
                userId?: string;
            },
            boolean
        >;
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface StockReorderCalculationInputSet$Lookups {
        user: QueryOperation<User>;
        company: QueryOperation<Company>;
        fromItem: QueryOperation<Item>;
        toItem: QueryOperation<Item>;
    }
    export interface StockReorderCalculationInputSet$Operations {
        query: QueryOperation<StockReorderCalculationInputSet>;
        read: ReadOperation<StockReorderCalculationInputSet>;
        aggregate: {
            read: AggregateReadOperation<StockReorderCalculationInputSet>;
            query: AggregateQueryOperation<StockReorderCalculationInputSet>;
        };
        create: CreateOperation<StockReorderCalculationInputSetInput, StockReorderCalculationInputSet>;
        getDuplicate: GetDuplicateOperation<StockReorderCalculationInputSet>;
        update: UpdateOperation<StockReorderCalculationInputSetInput, StockReorderCalculationInputSet>;
        updateById: UpdateByIdOperation<StockReorderCalculationInputSetInput, StockReorderCalculationInputSet>;
        delete: DeleteOperation<{}>;
        deleteById: DeleteOperation<string>;
        asyncOperations: StockReorderCalculationInputSet$AsyncOperations;
        lookups(
            dataOrId: string | { data: StockReorderCalculationInputSetInput },
        ): StockReorderCalculationInputSet$Lookups;
        getDefaults: GetDefaultsOperation<StockReorderCalculationInputSet>;
    }
    export interface StockReorderCalculationResultLine extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        inputSet: StockReorderCalculationInputSet;
        itemSite: ItemSite;
        company: Company;
        supplier: Supplier;
        orderDate: string;
        reorderType: PreferredProcess;
        stockUnit: UnitOfMeasure;
        purchaseUnit: UnitOfMeasure;
        currency: Currency;
        quantity: string;
    }
    export interface StockReorderCalculationResultLineInput extends VitalClientNodeInput {
        itemSite?: integer | string;
        supplier?: integer | string;
        orderDate?: string;
        reorderType?: PreferredProcess;
        stockUnit?: integer | string;
        purchaseUnit?: integer | string;
        currency?: integer | string;
        quantity?: decimal | string;
    }
    export interface StockReorderCalculationResultLineBinding extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        inputSet: StockReorderCalculationInputSet;
        itemSite: ItemSite;
        company: Company;
        supplier: Supplier;
        orderDate: string;
        reorderType: PreferredProcess;
        stockUnit: UnitOfMeasure;
        purchaseUnit: UnitOfMeasure;
        currency: Currency;
        quantity: string;
    }
    export interface StockReorderCalculationResultLine$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface StockReorderCalculationResultLine$Lookups {
        itemSite: QueryOperation<ItemSite>;
        company: QueryOperation<Company>;
        supplier: QueryOperation<Supplier>;
        stockUnit: QueryOperation<UnitOfMeasure>;
        purchaseUnit: QueryOperation<UnitOfMeasure>;
        currency: QueryOperation<Currency>;
    }
    export interface StockReorderCalculationResultLine$Operations {
        query: QueryOperation<StockReorderCalculationResultLine>;
        read: ReadOperation<StockReorderCalculationResultLine>;
        aggregate: {
            read: AggregateReadOperation<StockReorderCalculationResultLine>;
            query: AggregateQueryOperation<StockReorderCalculationResultLine>;
        };
        asyncOperations: StockReorderCalculationResultLine$AsyncOperations;
        lookups(
            dataOrId: string | { data: StockReorderCalculationResultLineInput },
        ): StockReorderCalculationResultLine$Lookups;
        getDefaults: GetDefaultsOperation<StockReorderCalculationResultLine>;
    }
    export interface StockValuationInputSet extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        user: User;
        company: Company;
        sites: ClientCollection<StockValuationInputSetToSite>;
        fromItem: Item;
        toItem: Item;
        itemCategory: ItemCategory;
        commodityCode: string;
        valuationMethod: CostValuationMethod;
        postingClass: PostingClass;
        displayZeroValues: boolean;
        date: string;
        status: StockValuationStatus;
        lines: ClientCollection<StockValuationResultLine>;
    }
    export interface StockValuationInputSetInput extends ClientNodeInput {
        user?: integer | string;
        company?: integer | string;
        sites?: Partial<StockValuationInputSetToSiteInput>[];
        fromItem?: integer | string;
        toItem?: integer | string;
        itemCategory?: integer | string;
        commodityCode?: string;
        valuationMethod?: CostValuationMethod;
        postingClass?: integer | string;
        displayZeroValues?: boolean | string;
        date?: string;
        status?: StockValuationStatus;
        lines?: Partial<StockValuationResultLineInput>[];
    }
    export interface StockValuationInputSetBinding extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        user: User;
        company: Company;
        sites: ClientCollection<StockValuationInputSetToSiteBinding>;
        fromItem: Item;
        toItem: Item;
        itemCategory: ItemCategory;
        commodityCode: string;
        valuationMethod: CostValuationMethod;
        postingClass: PostingClass;
        displayZeroValues: boolean;
        date: string;
        status: StockValuationStatus;
        lines: ClientCollection<StockValuationResultLineBinding>;
    }
    export interface StockValuationInputSet$AsyncOperations {
        stockValuation: AsyncOperation<
            {
                userId?: string;
            },
            boolean
        >;
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface StockValuationInputSet$Lookups {
        user: QueryOperation<User>;
        company: QueryOperation<Company>;
        fromItem: QueryOperation<Item>;
        toItem: QueryOperation<Item>;
        itemCategory: QueryOperation<ItemCategory>;
        postingClass: QueryOperation<PostingClass>;
    }
    export interface StockValuationInputSet$Operations {
        query: QueryOperation<StockValuationInputSet>;
        read: ReadOperation<StockValuationInputSet>;
        aggregate: {
            read: AggregateReadOperation<StockValuationInputSet>;
            query: AggregateQueryOperation<StockValuationInputSet>;
        };
        create: CreateOperation<StockValuationInputSetInput, StockValuationInputSet>;
        getDuplicate: GetDuplicateOperation<StockValuationInputSet>;
        update: UpdateOperation<StockValuationInputSetInput, StockValuationInputSet>;
        updateById: UpdateByIdOperation<StockValuationInputSetInput, StockValuationInputSet>;
        delete: DeleteOperation<{}>;
        deleteById: DeleteOperation<string>;
        asyncOperations: StockValuationInputSet$AsyncOperations;
        lookups(dataOrId: string | { data: StockValuationInputSetInput }): StockValuationInputSet$Lookups;
        getDefaults: GetDefaultsOperation<StockValuationInputSet>;
    }
    export interface StockValuationInputSetToSite extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        inputSet: StockValuationInputSet;
        site: Site;
    }
    export interface StockValuationInputSetToSiteInput extends VitalClientNodeInput {
        site?: integer | string;
    }
    export interface StockValuationInputSetToSiteBinding extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        inputSet: StockValuationInputSet;
        site: Site;
    }
    export interface StockValuationInputSetToSite$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface StockValuationInputSetToSite$Lookups {
        site: QueryOperation<Site>;
    }
    export interface StockValuationInputSetToSite$Operations {
        query: QueryOperation<StockValuationInputSetToSite>;
        read: ReadOperation<StockValuationInputSetToSite>;
        aggregate: {
            read: AggregateReadOperation<StockValuationInputSetToSite>;
            query: AggregateQueryOperation<StockValuationInputSetToSite>;
        };
        asyncOperations: StockValuationInputSetToSite$AsyncOperations;
        lookups(dataOrId: string | { data: StockValuationInputSetToSiteInput }): StockValuationInputSetToSite$Lookups;
        getDefaults: GetDefaultsOperation<StockValuationInputSetToSite>;
    }
    export interface StockValuationResultLine extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        inputSet: StockValuationInputSet;
        item: Item;
        site: Site;
        company: Company;
        postingClass: PostingClass;
        stockValue: string;
        unitCost: string;
        valuationDate: string;
        costType: CostValuationMethod;
        stockUnit: UnitOfMeasure;
        currency: Currency;
        itemCategory: ItemCategory;
        commodityCode: string;
        quantity: string;
    }
    export interface StockValuationResultLineInput extends VitalClientNodeInput {
        item?: integer | string;
        site?: integer | string;
        postingClass?: integer | string;
        stockValue?: decimal | string;
        unitCost?: decimal | string;
        valuationDate?: string;
        costType?: CostValuationMethod;
        stockUnit?: integer | string;
        currency?: integer | string;
        itemCategory?: integer | string;
        commodityCode?: string;
        quantity?: decimal | string;
    }
    export interface StockValuationResultLineBinding extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        inputSet: StockValuationInputSet;
        item: Item;
        site: Site;
        company: Company;
        postingClass: PostingClass;
        stockValue: string;
        unitCost: string;
        valuationDate: string;
        costType: CostValuationMethod;
        stockUnit: UnitOfMeasure;
        currency: Currency;
        itemCategory: ItemCategory;
        commodityCode: string;
        quantity: string;
    }
    export interface StockValuationResultLine$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface StockValuationResultLine$Lookups {
        item: QueryOperation<Item>;
        site: QueryOperation<Site>;
        company: QueryOperation<Company>;
        postingClass: QueryOperation<PostingClass>;
        stockUnit: QueryOperation<UnitOfMeasure>;
        currency: QueryOperation<Currency>;
        itemCategory: QueryOperation<ItemCategory>;
    }
    export interface StockValuationResultLine$Operations {
        query: QueryOperation<StockValuationResultLine>;
        read: ReadOperation<StockValuationResultLine>;
        aggregate: {
            read: AggregateReadOperation<StockValuationResultLine>;
            query: AggregateQueryOperation<StockValuationResultLine>;
        };
        asyncOperations: StockValuationResultLine$AsyncOperations;
        lookups(dataOrId: string | { data: StockValuationResultLineInput }): StockValuationResultLine$Lookups;
        getDefaults: GetDefaultsOperation<StockValuationResultLine>;
    }
    export interface StockValueChange extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        effectiveDate: string;
        number: string;
        site: Site;
        item: Item;
        description: string;
        valuationMethod: CostValuationMethod;
        postedDate: string;
        documentDate: string;
        stockTransactionStatus: StockDocumentTransactionStatus;
        totalQuantityInStock: string;
        totalValueOfStock: string;
        unitCost: string;
        lines: ClientCollection<StockValueChangeLine>;
        postingDetails: ClientCollection<FinanceTransaction>;
        financialSite: Site;
        transactionCurrency: Currency;
        financeIntegrationStatus: FinanceIntegrationStatus;
        _attachments: ClientCollection<AttachmentAssociation>;
    }
    export interface StockValueChangeInput extends ClientNodeInput {
        effectiveDate?: string;
        number?: string;
        site?: integer | string;
        item?: integer | string;
        description?: string;
        postedDate?: string;
        lines?: Partial<StockValueChangeLineInput>[];
        _attachments?: Partial<AttachmentAssociationInput>[];
    }
    export interface StockValueChangeBinding extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        effectiveDate: string;
        number: string;
        site: Site;
        item: Item;
        description: string;
        valuationMethod: CostValuationMethod;
        postedDate: string;
        documentDate: string;
        stockTransactionStatus: StockDocumentTransactionStatus;
        totalQuantityInStock: string;
        totalValueOfStock: string;
        unitCost: string;
        lines: ClientCollection<StockValueChangeLineBinding>;
        postingDetails: ClientCollection<FinanceTransaction>;
        financialSite: Site;
        transactionCurrency: Currency;
        financeIntegrationStatus: FinanceIntegrationStatus;
        _attachments: ClientCollection<AttachmentAssociation>;
    }
    export interface StockValueChange$Mutations {
        postToStock: Node$Operation<
            {
                documentIds?: (integer | string)[];
            },
            string
        >;
        repost: Node$Operation<
            {
                stockValueChange: string;
                documentLines?: {
                    baseDocumentLineSysId?: integer | string;
                    storedAttributes?: string;
                    storedDimensions?: string;
                }[];
            },
            {
                wasSuccessful: boolean;
                message: string;
            }
        >;
        financeIntegrationCheck: Node$Operation<
            {
                valueChange: string;
            },
            {
                wasSuccessful: boolean;
                message: string;
            }
        >;
        resendNotificationForFinance: Node$Operation<
            {
                stockValueChange: string;
            },
            boolean
        >;
    }
    export interface StockValueChange$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface StockValueChange$Lookups {
        site: QueryOperation<Site>;
        item: QueryOperation<Item>;
        financialSite: QueryOperation<Site>;
        transactionCurrency: QueryOperation<Currency>;
    }
    export interface StockValueChange$Operations {
        query: QueryOperation<StockValueChange>;
        read: ReadOperation<StockValueChange>;
        aggregate: {
            read: AggregateReadOperation<StockValueChange>;
            query: AggregateQueryOperation<StockValueChange>;
        };
        create: CreateOperation<StockValueChangeInput, StockValueChange>;
        getDuplicate: GetDuplicateOperation<StockValueChange>;
        update: UpdateOperation<StockValueChangeInput, StockValueChange>;
        updateById: UpdateByIdOperation<StockValueChangeInput, StockValueChange>;
        delete: DeleteOperation<{}>;
        deleteById: DeleteOperation<string>;
        mutations: StockValueChange$Mutations;
        asyncOperations: StockValueChange$AsyncOperations;
        lookups(dataOrId: string | { data: StockValueChangeInput }): StockValueChange$Lookups;
        getDefaults: GetDefaultsOperation<StockValueChange>;
    }
    export interface StockValueCorrection extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        number: string;
        effectiveDate: string;
        stockSite: Site;
        reasonCode: ReasonCode;
        description: string;
        lines: ClientCollection<StockValueCorrectionLine>;
        stockTransactionStatus: StockDocumentTransactionStatus;
        financialSite: Site;
        transactionCurrency: Currency;
        documentDate: string;
        status: StockDocumentStatus;
    }
    export interface StockValueCorrectionInput extends ClientNodeInput {
        number?: string;
        effectiveDate?: string;
        stockSite?: integer | string;
        reasonCode?: integer | string;
        description?: string;
        lines?: Partial<StockValueCorrectionLineInput>[];
    }
    export interface StockValueCorrectionBinding extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        number: string;
        effectiveDate: string;
        stockSite: Site;
        reasonCode: ReasonCode;
        description: string;
        lines: ClientCollection<StockValueCorrectionLineBinding>;
        stockTransactionStatus: StockDocumentTransactionStatus;
        financialSite: Site;
        transactionCurrency: Currency;
        documentDate: string;
        status: StockDocumentStatus;
    }
    export interface StockValueCorrection$Mutations {
        postToStock: Node$Operation<
            {
                documentIds?: (integer | string)[];
            },
            string
        >;
    }
    export interface StockValueCorrection$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface StockValueCorrection$Lookups {
        stockSite: QueryOperation<Site>;
        reasonCode: QueryOperation<ReasonCode>;
        financialSite: QueryOperation<Site>;
        transactionCurrency: QueryOperation<Currency>;
    }
    export interface StockValueCorrection$Operations {
        query: QueryOperation<StockValueCorrection>;
        read: ReadOperation<StockValueCorrection>;
        aggregate: {
            read: AggregateReadOperation<StockValueCorrection>;
            query: AggregateQueryOperation<StockValueCorrection>;
        };
        create: CreateOperation<StockValueCorrectionInput, StockValueCorrection>;
        getDuplicate: GetDuplicateOperation<StockValueCorrection>;
        update: UpdateOperation<StockValueCorrectionInput, StockValueCorrection>;
        updateById: UpdateByIdOperation<StockValueCorrectionInput, StockValueCorrection>;
        delete: DeleteOperation<{}>;
        deleteById: DeleteOperation<string>;
        mutations: StockValueCorrection$Mutations;
        asyncOperations: StockValueCorrection$AsyncOperations;
        lookups(dataOrId: string | { data: StockValueCorrectionInput }): StockValueCorrection$Lookups;
        getDefaults: GetDefaultsOperation<StockValueCorrection>;
    }
    export interface StockAdjustmentLine extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        documentNumber: string;
        documentId: integer;
        document: StockAdjustment;
        stockTransactionStatus: StockDocumentTransactionStatus;
        item: Item;
        quantityInStockUnit: string;
        stockUnit: UnitOfMeasure;
        adjustmentQuantityInStockUnit: string;
        newUnitCost: string;
        stockValue: string;
        newStockValue: string;
        location: Location;
        stockStatus: StockStatus;
        lot: Lot;
        stockMovements: ClientCollection<StockJournal>;
        stockTransactions: ClientCollection<StockTransaction>;
        itemSite: ItemSite;
        site: Site;
        newStockQuantity: string;
        storedDimensions: string;
        storedAttributes: string;
        computedAttributes: string;
        unitCost: string;
        orderCost: string;
        valuedCost: string;
        stockDetails: ClientCollection<StockAdjustmentDetail>;
        jsonStockDetails: string;
        analyticalData: AnalyticalData;
        stockDetailStatus: StockDetailStatus;
        displayStatus: StockAdjustmentDisplayStatus;
    }
    export interface StockAdjustmentLineInput extends VitalClientNodeInput {
        stockTransactionStatus?: StockDocumentTransactionStatus;
        item?: integer | string;
        quantityInStockUnit?: decimal | string;
        adjustmentQuantityInStockUnit?: decimal | string;
        newUnitCost?: decimal | string;
        stockValue?: decimal | string;
        newStockValue?: decimal | string;
        location?: integer | string;
        stockStatus?: integer | string;
        lot?: integer | string;
        stockTransactions?: Partial<StockTransactionInput>[];
        storedDimensions?: string;
        storedAttributes?: string;
        unitCost?: decimal | string;
        stockDetails?: Partial<StockAdjustmentDetailInput>[];
        jsonStockDetails?: string;
        analyticalData?: integer | string;
    }
    export interface StockAdjustmentLineBinding extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        documentNumber: string;
        documentId: integer;
        document: StockAdjustment;
        stockTransactionStatus: StockDocumentTransactionStatus;
        item: Item;
        quantityInStockUnit: string;
        stockUnit: UnitOfMeasure;
        adjustmentQuantityInStockUnit: string;
        newUnitCost: string;
        stockValue: string;
        newStockValue: string;
        location: Location;
        stockStatus: StockStatus;
        lot: Lot;
        stockMovements: ClientCollection<StockJournal>;
        stockTransactions: ClientCollection<StockTransactionBinding>;
        itemSite: ItemSite;
        site: Site;
        newStockQuantity: string;
        storedDimensions: any;
        storedAttributes: any;
        computedAttributes: any;
        unitCost: string;
        orderCost: string;
        valuedCost: string;
        stockDetails: ClientCollection<StockAdjustmentDetailBinding>;
        jsonStockDetails: any;
        analyticalData: AnalyticalData;
        stockDetailStatus: StockDetailStatus;
        displayStatus: StockAdjustmentDisplayStatus;
    }
    export interface StockAdjustmentLine$Mutations {
        setDimension: Node$Operation<
            {
                stockAdjustmentLine: string;
                storedDimensions?: string;
                storedAttributes?: string;
            },
            boolean
        >;
    }
    export interface StockAdjustmentLine$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface StockAdjustmentLine$Lookups {
        item: QueryOperation<Item>;
        stockUnit: QueryOperation<UnitOfMeasure>;
        location: QueryOperation<Location>;
        stockStatus: QueryOperation<StockStatus>;
        lot: QueryOperation<Lot>;
        itemSite: QueryOperation<ItemSite>;
        site: QueryOperation<Site>;
        analyticalData: QueryOperation<AnalyticalData>;
    }
    export interface StockAdjustmentLine$Operations {
        query: QueryOperation<StockAdjustmentLine>;
        read: ReadOperation<StockAdjustmentLine>;
        aggregate: {
            read: AggregateReadOperation<StockAdjustmentLine>;
            query: AggregateQueryOperation<StockAdjustmentLine>;
        };
        create: CreateOperation<StockAdjustmentLineInput, StockAdjustmentLine>;
        getDuplicate: GetDuplicateOperation<StockAdjustmentLine>;
        update: UpdateOperation<StockAdjustmentLineInput, StockAdjustmentLine>;
        updateById: UpdateByIdOperation<StockAdjustmentLineInput, StockAdjustmentLine>;
        mutations: StockAdjustmentLine$Mutations;
        asyncOperations: StockAdjustmentLine$AsyncOperations;
        lookups(dataOrId: string | { data: StockAdjustmentLineInput }): StockAdjustmentLine$Lookups;
        getDefaults: GetDefaultsOperation<StockAdjustmentLine>;
    }
    export interface StockChangeLine extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        documentNumber: string;
        documentId: integer;
        document: StockChange;
        stockTransactionStatus: StockDocumentTransactionStatus;
        quantityInStockUnit: string;
        orderCost: string;
        valuedCost: string;
        location: Location;
        stockStatus: StockStatus;
        stockTransactions: ClientCollection<StockTransaction>;
        stockDetails: ClientCollection<StockChangeDetail>;
        jsonStockDetails: string;
    }
    export interface StockChangeLineInput extends VitalClientNodeInput {
        stockTransactionStatus?: StockDocumentTransactionStatus;
        quantityInStockUnit?: decimal | string;
        orderCost?: decimal | string;
        valuedCost?: decimal | string;
        location?: integer | string;
        stockStatus?: integer | string;
        stockTransactions?: Partial<StockTransactionInput>[];
        stockDetails?: Partial<StockChangeDetailInput>[];
        jsonStockDetails?: string;
    }
    export interface StockChangeLineBinding extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        documentNumber: string;
        documentId: integer;
        document: StockChange;
        stockTransactionStatus: StockDocumentTransactionStatus;
        quantityInStockUnit: string;
        orderCost: string;
        valuedCost: string;
        location: Location;
        stockStatus: StockStatus;
        stockTransactions: ClientCollection<StockTransactionBinding>;
        stockDetails: ClientCollection<StockChangeDetailBinding>;
        jsonStockDetails: any;
    }
    export interface StockChangeLine$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface StockChangeLine$Lookups {
        location: QueryOperation<Location>;
        stockStatus: QueryOperation<StockStatus>;
    }
    export interface StockChangeLine$Operations {
        query: QueryOperation<StockChangeLine>;
        read: ReadOperation<StockChangeLine>;
        aggregate: {
            read: AggregateReadOperation<StockChangeLine>;
            query: AggregateQueryOperation<StockChangeLine>;
        };
        create: CreateOperation<StockChangeLineInput, StockChangeLine>;
        getDuplicate: GetDuplicateOperation<StockChangeLine>;
        asyncOperations: StockChangeLine$AsyncOperations;
        lookups(dataOrId: string | { data: StockChangeLineInput }): StockChangeLine$Lookups;
        getDefaults: GetDefaultsOperation<StockChangeLine>;
    }
    export interface StockCountLine extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        documentNumber: string;
        documentId: integer;
        document: StockCount;
        stockTransactionStatus: StockDocumentTransactionStatus;
        status: StockCountLineStatus;
        item: Item;
        location: Location;
        zone: LocationZone;
        lot: Lot;
        stockStatus: StockStatus;
        owner: string;
        stockUnit: UnitOfMeasure;
        countedQuantityInStockUnit: string;
        stockTransactions: ClientCollection<StockTransaction>;
        stockMovements: ClientCollection<StockJournal>;
        site: Site;
        itemSite: ItemSite;
        storedDimensions: string;
        storedAttributes: string;
        computedAttributes: string;
        stockCountLineSerialNumbers: ClientCollection<StockCountLineSerialNumber>;
        jsonSerialNumbers: string;
        countedSerialNumber: string;
        countedSerialNumberPercentage: string;
        hasAllocationError: boolean;
        allocations: ClientCollection<StockAllocation>;
        stockRecord: Stock;
        totalAllocated: string;
        isCreatedFromStockRecord: boolean;
        quantityInStockUnit: string;
        quantityVariance: string;
        adjustmentQuantityInStockUnit: string;
        quantityVariancePercentage: string;
        newLineOrderCost: string;
        canEnterOrderCost: boolean;
        shouldEnterOrderCost: boolean;
        analyticalData: AnalyticalData;
        jsonStockDetails: string;
        stockDetail: StockAdjustmentDetail;
        isAddedDuringCount: boolean;
        canBeDeleted: boolean;
        isNonStockItem: boolean;
    }
    export interface StockCountLineInput extends VitalClientNodeInput {
        stockTransactionStatus?: StockDocumentTransactionStatus;
        status?: StockCountLineStatus;
        item?: integer | string;
        location?: integer | string;
        lot?: integer | string;
        stockStatus?: integer | string;
        owner?: string;
        countedQuantityInStockUnit?: decimal | string;
        stockTransactions?: Partial<StockTransactionInput>[];
        storedDimensions?: string;
        storedAttributes?: string;
        stockCountLineSerialNumbers?: Partial<StockCountLineSerialNumberInput>[];
        jsonSerialNumbers?: string;
        stockRecord?: integer | string;
        isStockCountCreation?: boolean | string;
        isCreatedFromStockRecord?: boolean | string;
        quantityInStockUnit?: decimal | string;
        newLineOrderCost?: decimal | string;
        analyticalData?: integer | string;
        jsonStockDetails?: string;
    }
    export interface StockCountLineBinding extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        documentNumber: string;
        documentId: integer;
        document: StockCount;
        stockTransactionStatus: StockDocumentTransactionStatus;
        status: StockCountLineStatus;
        item: Item;
        location: Location;
        zone: LocationZone;
        lot: Lot;
        stockStatus: StockStatus;
        owner: string;
        stockUnit: UnitOfMeasure;
        countedQuantityInStockUnit: string;
        stockTransactions: ClientCollection<StockTransactionBinding>;
        stockMovements: ClientCollection<StockJournal>;
        site: Site;
        itemSite: ItemSite;
        storedDimensions: any;
        storedAttributes: any;
        computedAttributes: any;
        stockCountLineSerialNumbers: ClientCollection<StockCountLineSerialNumberBinding>;
        jsonSerialNumbers: any;
        countedSerialNumber: string;
        countedSerialNumberPercentage: string;
        hasAllocationError: boolean;
        allocations: ClientCollection<StockAllocation>;
        stockRecord: Stock;
        totalAllocated: string;
        isStockCountCreation: boolean;
        isCreatedFromStockRecord: boolean;
        quantityInStockUnit: string;
        quantityVariance: string;
        adjustmentQuantityInStockUnit: string;
        quantityVariancePercentage: string;
        newLineOrderCost: string;
        canEnterOrderCost: boolean;
        shouldEnterOrderCost: boolean;
        analyticalData: AnalyticalData;
        jsonStockDetails: any;
        stockDetail: StockAdjustmentDetail;
        isAddedDuringCount: boolean;
        canBeDeleted: boolean;
        isNonStockItem: boolean;
    }
    export interface StockCountLine$Queries {
        getStockQuantity: Node$Operation<
            {
                stockCountLine: string;
            },
            decimal
        >;
    }
    export interface StockCountLine$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface StockCountLine$Lookups {
        item: QueryOperation<Item>;
        location: QueryOperation<Location>;
        zone: QueryOperation<LocationZone>;
        lot: QueryOperation<Lot>;
        stockStatus: QueryOperation<StockStatus>;
        stockUnit: QueryOperation<UnitOfMeasure>;
        site: QueryOperation<Site>;
        itemSite: QueryOperation<ItemSite>;
        stockRecord: QueryOperation<Stock>;
        analyticalData: QueryOperation<AnalyticalData>;
        stockDetail: QueryOperation<StockAdjustmentDetail>;
    }
    export interface StockCountLine$Operations {
        query: QueryOperation<StockCountLine>;
        read: ReadOperation<StockCountLine>;
        aggregate: {
            read: AggregateReadOperation<StockCountLine>;
            query: AggregateQueryOperation<StockCountLine>;
        };
        queries: StockCountLine$Queries;
        asyncOperations: StockCountLine$AsyncOperations;
        lookups(dataOrId: string | { data: StockCountLineInput }): StockCountLine$Lookups;
        getDefaults: GetDefaultsOperation<StockCountLine>;
    }
    export interface StockIssueLine extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        documentNumber: string;
        documentId: integer;
        document: StockIssue;
        stockTransactionStatus: StockDocumentTransactionStatus;
        item: Item;
        stockUnit: UnitOfMeasure;
        quantityInStockUnit: string;
        location: Location;
        stockStatus: StockStatus;
        lot: Lot;
        storedDimensions: string;
        storedAttributes: string;
        computedAttributes: string;
        stockDetails: ClientCollection<StockIssueDetail>;
        jsonStockDetails: string;
        stockMovements: ClientCollection<StockJournal>;
        stockTransactions: ClientCollection<StockTransaction>;
        site: Site;
        stockDetailStatus: StockDetailStatus;
        displayStatus: StockIssueLineDisplayStatus;
        itemSite: ItemSite;
        orderCost: string;
        totalCost: string;
        valuedCost: string;
        analyticalData: AnalyticalData;
    }
    export interface StockIssueLineInput extends VitalClientNodeInput {
        stockTransactionStatus?: StockDocumentTransactionStatus;
        item?: integer | string;
        stockUnit?: integer | string;
        quantityInStockUnit?: decimal | string;
        location?: integer | string;
        stockStatus?: integer | string;
        lot?: integer | string;
        storedDimensions?: string;
        storedAttributes?: string;
        stockDetails?: Partial<StockIssueDetailInput>[];
        jsonStockDetails?: string;
        stockTransactions?: Partial<StockTransactionInput>[];
        stockDetailStatus?: StockDetailStatus;
        displayStatus?: StockIssueLineDisplayStatus;
        orderCost?: decimal | string;
        valuedCost?: decimal | string;
        analyticalData?: integer | string;
    }
    export interface StockIssueLineBinding extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        documentNumber: string;
        documentId: integer;
        document: StockIssue;
        stockTransactionStatus: StockDocumentTransactionStatus;
        item: Item;
        stockUnit: UnitOfMeasure;
        quantityInStockUnit: string;
        location: Location;
        stockStatus: StockStatus;
        lot: Lot;
        storedDimensions: any;
        storedAttributes: any;
        computedAttributes: any;
        stockDetails: ClientCollection<StockIssueDetailBinding>;
        jsonStockDetails: any;
        stockMovements: ClientCollection<StockJournal>;
        stockTransactions: ClientCollection<StockTransactionBinding>;
        site: Site;
        stockDetailStatus: StockDetailStatus;
        displayStatus: StockIssueLineDisplayStatus;
        itemSite: ItemSite;
        orderCost: string;
        totalCost: string;
        valuedCost: string;
        analyticalData: AnalyticalData;
    }
    export interface StockIssueLine$Mutations {
        setDimension: Node$Operation<
            {
                stockIssueLine: string;
                storedDimensions?: string;
                storedAttributes?: string;
            },
            boolean
        >;
    }
    export interface StockIssueLine$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface StockIssueLine$Lookups {
        item: QueryOperation<Item>;
        stockUnit: QueryOperation<UnitOfMeasure>;
        location: QueryOperation<Location>;
        stockStatus: QueryOperation<StockStatus>;
        lot: QueryOperation<Lot>;
        site: QueryOperation<Site>;
        itemSite: QueryOperation<ItemSite>;
        analyticalData: QueryOperation<AnalyticalData>;
    }
    export interface StockIssueLine$Operations {
        query: QueryOperation<StockIssueLine>;
        read: ReadOperation<StockIssueLine>;
        aggregate: {
            read: AggregateReadOperation<StockIssueLine>;
            query: AggregateQueryOperation<StockIssueLine>;
        };
        mutations: StockIssueLine$Mutations;
        asyncOperations: StockIssueLine$AsyncOperations;
        lookups(dataOrId: string | { data: StockIssueLineInput }): StockIssueLine$Lookups;
        getDefaults: GetDefaultsOperation<StockIssueLine>;
    }
    export interface StockReceiptLine extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        documentNumber: string;
        documentId: integer;
        document: StockReceipt;
        stockTransactionStatus: StockDocumentTransactionStatus;
        itemSite: ItemSite;
        item: Item;
        stockUnit: UnitOfMeasure;
        quantityInStockUnit: string;
        location: Location;
        stockStatus: StockStatus;
        existingLot: Lot;
        orderCost: string;
        totalCost: string;
        valuedCost: string;
        storedDimensions: string;
        storedAttributes: string;
        computedAttributes: string;
        stockDetails: ClientCollection<StockReceiptDetail>;
        jsonStockDetails: string;
        stockMovements: ClientCollection<StockJournal>;
        stockTransactions: ClientCollection<StockTransaction>;
        site: Site;
        stockDetailStatus: StockDetailStatus;
        displayStatus: StockReceiptLineDisplayStatus;
        analyticalData: AnalyticalData;
    }
    export interface StockReceiptLineInput extends VitalClientNodeInput {
        stockTransactionStatus?: StockDocumentTransactionStatus;
        item?: integer | string;
        stockUnit?: integer | string;
        quantityInStockUnit?: decimal | string;
        location?: integer | string;
        stockStatus?: integer | string;
        existingLot?: integer | string;
        lotCreateData?: string;
        orderCost?: decimal | string;
        valuedCost?: decimal | string;
        storedDimensions?: string;
        storedAttributes?: string;
        stockDetails?: Partial<StockReceiptDetailInput>[];
        jsonStockDetails?: string;
        stockTransactions?: Partial<StockTransactionInput>[];
        stockDetailStatus?: StockDetailStatus;
        displayStatus?: StockReceiptLineDisplayStatus;
        analyticalData?: integer | string;
    }
    export interface StockReceiptLineBinding extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        documentNumber: string;
        documentId: integer;
        document: StockReceipt;
        stockTransactionStatus: StockDocumentTransactionStatus;
        itemSite: ItemSite;
        item: Item;
        stockUnit: UnitOfMeasure;
        quantityInStockUnit: string;
        location: Location;
        stockStatus: StockStatus;
        existingLot: Lot;
        lotCreateData: any;
        orderCost: string;
        totalCost: string;
        valuedCost: string;
        storedDimensions: any;
        storedAttributes: any;
        computedAttributes: any;
        stockDetails: ClientCollection<StockReceiptDetailBinding>;
        jsonStockDetails: any;
        stockMovements: ClientCollection<StockJournal>;
        stockTransactions: ClientCollection<StockTransactionBinding>;
        site: Site;
        stockDetailStatus: StockDetailStatus;
        displayStatus: StockReceiptLineDisplayStatus;
        analyticalData: AnalyticalData;
    }
    export interface StockReceiptLine$Mutations {
        setDimension: Node$Operation<
            {
                stockReceiptLine: string;
                storedDimensions?: string;
                storedAttributes?: string;
            },
            boolean
        >;
    }
    export interface StockReceiptLine$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface StockReceiptLine$Lookups {
        itemSite: QueryOperation<ItemSite>;
        item: QueryOperation<Item>;
        stockUnit: QueryOperation<UnitOfMeasure>;
        location: QueryOperation<Location>;
        stockStatus: QueryOperation<StockStatus>;
        existingLot: QueryOperation<Lot>;
        site: QueryOperation<Site>;
        analyticalData: QueryOperation<AnalyticalData>;
    }
    export interface StockReceiptLine$Operations {
        query: QueryOperation<StockReceiptLine>;
        read: ReadOperation<StockReceiptLine>;
        aggregate: {
            read: AggregateReadOperation<StockReceiptLine>;
            query: AggregateQueryOperation<StockReceiptLine>;
        };
        create: CreateOperation<StockReceiptLineInput, StockReceiptLine>;
        getDuplicate: GetDuplicateOperation<StockReceiptLine>;
        update: UpdateOperation<StockReceiptLineInput, StockReceiptLine>;
        updateById: UpdateByIdOperation<StockReceiptLineInput, StockReceiptLine>;
        mutations: StockReceiptLine$Mutations;
        asyncOperations: StockReceiptLine$AsyncOperations;
        lookups(dataOrId: string | { data: StockReceiptLineInput }): StockReceiptLine$Lookups;
        getDefaults: GetDefaultsOperation<StockReceiptLine>;
    }
    export interface StockValueChangeLine extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        documentNumber: string;
        documentId: integer;
        document: StockValueChange;
        currency: Currency;
        stockUnit: UnitOfMeasure;
        fifoCost: FifoValuationTier;
        quantity: string;
        quantityInStockUnit: string;
        unitCost: string;
        newUnitCost: string;
        newAmount: string;
        amount: string;
        computedAttributes: string;
        stockTransactionStatus: StockDocumentTransactionStatus;
        stockDetails: ClientCollection<StockValueDetail>;
        jsonStockDetails: string;
        stockTransactions: ClientCollection<StockTransaction>;
        site: Site;
        item: Item;
        effectiveDate: string;
        stockMovements: ClientCollection<StockJournal>;
        storedDimensions: string;
        storedAttributes: string;
        analyticalData: AnalyticalData;
    }
    export interface StockValueChangeLineInput extends VitalClientNodeInput {
        fifoCost?: integer | string;
        quantity?: decimal | string;
        unitCost?: decimal | string;
        newUnitCost?: decimal | string;
        newAmount?: decimal | string;
        amount?: decimal | string;
        stockTransactionStatus?: StockDocumentTransactionStatus;
        stockDetails?: Partial<StockValueDetailInput>[];
        jsonStockDetails?: string;
        stockTransactions?: Partial<StockTransactionInput>[];
        storedDimensions?: string;
        storedAttributes?: string;
        analyticalData?: integer | string;
    }
    export interface StockValueChangeLineBinding extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        documentNumber: string;
        documentId: integer;
        document: StockValueChange;
        currency: Currency;
        stockUnit: UnitOfMeasure;
        fifoCost: FifoValuationTier;
        quantity: string;
        quantityInStockUnit: string;
        unitCost: string;
        newUnitCost: string;
        newAmount: string;
        amount: string;
        computedAttributes: any;
        stockTransactionStatus: StockDocumentTransactionStatus;
        stockDetails: ClientCollection<StockValueDetailBinding>;
        jsonStockDetails: any;
        stockTransactions: ClientCollection<StockTransactionBinding>;
        site: Site;
        item: Item;
        effectiveDate: string;
        stockMovements: ClientCollection<StockJournal>;
        storedDimensions: any;
        storedAttributes: any;
        analyticalData: AnalyticalData;
    }
    export interface StockValueChangeLine$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface StockValueChangeLine$Lookups {
        currency: QueryOperation<Currency>;
        stockUnit: QueryOperation<UnitOfMeasure>;
        fifoCost: QueryOperation<FifoValuationTier>;
        site: QueryOperation<Site>;
        item: QueryOperation<Item>;
        analyticalData: QueryOperation<AnalyticalData>;
    }
    export interface StockValueChangeLine$Operations {
        query: QueryOperation<StockValueChangeLine>;
        read: ReadOperation<StockValueChangeLine>;
        aggregate: {
            read: AggregateReadOperation<StockValueChangeLine>;
            query: AggregateQueryOperation<StockValueChangeLine>;
        };
        create: CreateOperation<StockValueChangeLineInput, StockValueChangeLine>;
        getDuplicate: GetDuplicateOperation<StockValueChangeLine>;
        update: UpdateOperation<StockValueChangeLineInput, StockValueChangeLine>;
        updateById: UpdateByIdOperation<StockValueChangeLineInput, StockValueChangeLine>;
        asyncOperations: StockValueChangeLine$AsyncOperations;
        lookups(dataOrId: string | { data: StockValueChangeLineInput }): StockValueChangeLine$Lookups;
        getDefaults: GetDefaultsOperation<StockValueChangeLine>;
    }
    export interface StockValueCorrectionLine extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        documentNumber: string;
        documentId: integer;
        document: StockValueCorrection;
        stockTransactionStatus: StockDocumentTransactionStatus;
        item: Item;
        newUnitCost: string;
        stockDetails: ClientCollection<StockCorrectionDetail>;
        stockTransactions: ClientCollection<StockTransaction>;
        stockMovements: ClientCollection<StockJournal>;
        itemSite: ItemSite;
        site: Site;
        correctedDocumentType: AdjustableDocumentType;
        correctedDocumentLine: BaseDocumentLine;
        storedDimensions: string;
        storedAttributes: string;
        computedAttributes: string;
        unitCost: string;
        orderCost: string;
        analyticalData: AnalyticalData;
    }
    export interface StockValueCorrectionLineInput extends VitalClientNodeInput {
        stockTransactionStatus?: StockDocumentTransactionStatus;
        item?: integer | string;
        newUnitCost?: decimal | string;
        stockDetails?: Partial<StockCorrectionDetailInput>[];
        stockTransactions?: Partial<StockTransactionInput>[];
        correctedDocumentType?: AdjustableDocumentType;
        correctedDocumentLine?: integer | string;
        storedDimensions?: string;
        storedAttributes?: string;
        unitCost?: decimal | string;
        analyticalData?: integer | string;
    }
    export interface StockValueCorrectionLineBinding extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        documentNumber: string;
        documentId: integer;
        document: StockValueCorrection;
        stockTransactionStatus: StockDocumentTransactionStatus;
        item: Item;
        newUnitCost: string;
        stockDetails: ClientCollection<StockCorrectionDetailBinding>;
        stockTransactions: ClientCollection<StockTransactionBinding>;
        stockMovements: ClientCollection<StockJournal>;
        itemSite: ItemSite;
        site: Site;
        correctedDocumentType: AdjustableDocumentType;
        correctedDocumentLine: BaseDocumentLine;
        storedDimensions: any;
        storedAttributes: any;
        computedAttributes: any;
        unitCost: string;
        orderCost: string;
        analyticalData: AnalyticalData;
    }
    export interface StockValueCorrectionLine$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface StockValueCorrectionLine$Lookups {
        item: QueryOperation<Item>;
        itemSite: QueryOperation<ItemSite>;
        site: QueryOperation<Site>;
        correctedDocumentLine: QueryOperation<BaseDocumentLine>;
        analyticalData: QueryOperation<AnalyticalData>;
    }
    export interface StockValueCorrectionLine$Operations {
        query: QueryOperation<StockValueCorrectionLine>;
        read: ReadOperation<StockValueCorrectionLine>;
        aggregate: {
            read: AggregateReadOperation<StockValueCorrectionLine>;
            query: AggregateQueryOperation<StockValueCorrectionLine>;
        };
        create: CreateOperation<StockValueCorrectionLineInput, StockValueCorrectionLine>;
        getDuplicate: GetDuplicateOperation<StockValueCorrectionLine>;
        update: UpdateOperation<StockValueCorrectionLineInput, StockValueCorrectionLine>;
        updateById: UpdateByIdOperation<StockValueCorrectionLineInput, StockValueCorrectionLine>;
        asyncOperations: StockValueCorrectionLine$AsyncOperations;
        lookups(dataOrId: string | { data: StockValueCorrectionLineInput }): StockValueCorrectionLine$Lookups;
        getDefaults: GetDefaultsOperation<StockValueCorrectionLine>;
    }
    export interface ComponentExtension {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        billOfMaterial: BillOfMaterial;
        lineType: BomLineType;
        bomItem: BillOfMaterial;
        unit: UnitOfMeasure;
        isFixedLinkQuantity: boolean;
        linkQuantity: string;
        scrapFactor: string;
        operation: Operation;
        currency: Currency;
        instruction: TextStream;
        revision: BillOfMaterialRevision;
        componentNumber: integer;
        item: Item;
        itemSite: ItemSite;
        bom: BillOfMaterial;
        name: string;
        standardCost: string;
        stockOnHand: string;
        availableQuantityInStockUnit: string;
        stockShortageInStockUnit: string;
        hasStockShortage: boolean;
    }
    export interface ComponentInputExtension {
        lineType?: BomLineType;
        bomItem?: integer | string;
        unit?: integer | string;
        isFixedLinkQuantity?: boolean | string;
        linkQuantity?: decimal | string;
        scrapFactor?: decimal | string;
        operation?: integer | string;
        instruction?: TextStream;
        revision?: integer | string;
        componentNumber?: integer | string;
        item?: integer | string;
        name?: string;
    }
    export interface ComponentBindingExtension {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        billOfMaterial: BillOfMaterial;
        lineType: BomLineType;
        bomItem: BillOfMaterial;
        unit: UnitOfMeasure;
        isFixedLinkQuantity: boolean;
        linkQuantity: string;
        scrapFactor: string;
        operation: Operation;
        currency: Currency;
        instruction: TextStream;
        revision: BillOfMaterialRevision;
        componentNumber: integer;
        item: Item;
        itemSite: ItemSite;
        bom: BillOfMaterial;
        name: string;
        standardCost: string;
        stockOnHand: string;
        availableQuantityInStockUnit: string;
        stockShortageInStockUnit: string;
        hasStockShortage: boolean;
    }
    export interface ItemExtension {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        name: string;
        description: string;
        status: ItemStatus;
        isActive: boolean;
        type: ItemType;
        isBought: boolean;
        isManufactured: boolean;
        isSold: boolean;
        eanNumber: string;
        commodityCode: string;
        stockUnit: UnitOfMeasure;
        volumeUnit: UnitOfMeasure;
        volume: string;
        weightUnit: UnitOfMeasure;
        weight: string;
        density: string;
        image: BinaryStream;
        allergens: ClientCollection<ItemAllergen>;
        classifications: ClientCollection<ItemClassifications>;
        category: ItemCategory;
        isStockManaged: boolean;
        isPhantom: boolean;
        isBomRevisionManaged: boolean;
        bomRevisionSequenceNumber: BomRevisionSequence;
        isPotencyManagement: boolean;
        isTraceabilityManagement: boolean;
        supplierPrices: ClientCollection<ItemSupplierPrice>;
        itemSites: ClientCollection<ItemSite>;
        suppliers: ClientCollection<ItemSupplier>;
        customers: ClientCollection<ItemCustomer>;
        purchaseUnit: UnitOfMeasure;
        purchaseUnitToStockUnitConversionDedicated: boolean;
        serialNumberManagement: SerialNumberManagement;
        serialNumberSequenceNumber: SequenceNumber;
        serialNumberUsage: SerialNumberUsage;
        useSupplierSerialNumbers: boolean;
        salesUnit: UnitOfMeasure;
        salesUnitToStockUnitConversionDedicated: boolean;
        minimumSalesQuantity: string;
        maximumSalesQuantity: string;
        currency: Currency;
        basePrice: string;
        minimumPrice: string;
        customerPrices: ClientCollection<ItemCustomerPrice>;
        capacity: integer;
        landedCostItem: LandedCostItem;
        landedCostAllocationRule: AllocationRule;
        landedCostAllocationRuleUnit: UnitOfMeasure;
        inStockQuantity: string;
        inTransitStockQuantity: string;
        itemTaxGroup: ItemTaxGroup;
        postingClass: PostingClass;
        storedDimensions: string;
        storedAttributes: string;
        billOfMaterials: ClientCollection<BillOfMaterial>;
        _syncTick: string;
        _attachments: ClientCollection<AttachmentAssociation>;
        id: string;
        lotManagement: LotManagement;
        lotSequenceNumber: SequenceNumber;
        isExpiryManaged: boolean;
        analyticalData: AnalyticalData;
    }
    export interface ItemInputExtension {
        name?: string;
        description?: string;
        status?: ItemStatus;
        type?: ItemType;
        isBought?: boolean | string;
        isManufactured?: boolean | string;
        isSold?: boolean | string;
        eanNumber?: string;
        commodityCode?: string;
        stockUnit?: integer | string;
        volumeUnit?: integer | string;
        volume?: decimal | string;
        weightUnit?: integer | string;
        weight?: decimal | string;
        density?: decimal | string;
        image?: BinaryStream;
        allergens?: Partial<ItemAllergenInput>[];
        classifications?: Partial<ItemClassificationsInput>[];
        category?: integer | string;
        isStockManaged?: boolean | string;
        isPhantom?: boolean | string;
        isBomRevisionManaged?: boolean | string;
        bomRevisionSequenceNumber?: integer | string;
        isPotencyManagement?: boolean | string;
        isTraceabilityManagement?: boolean | string;
        supplierPrices?: Partial<ItemSupplierPriceInput>[];
        itemSites?: Partial<ItemSiteInput>[];
        suppliers?: Partial<ItemSupplierInput>[];
        customers?: Partial<ItemCustomerInput>[];
        purchaseUnit?: integer | string;
        purchaseUnitToStockUnitConversion?: decimal | string;
        serialNumberManagement?: SerialNumberManagement;
        serialNumberSequenceNumber?: integer | string;
        serialNumberUsage?: SerialNumberUsage;
        salesUnit?: integer | string;
        salesUnitToStockUnitConversion?: decimal | string;
        minimumSalesQuantity?: decimal | string;
        maximumSalesQuantity?: decimal | string;
        currency?: integer | string;
        basePrice?: decimal | string;
        minimumPrice?: decimal | string;
        customerPrices?: Partial<ItemCustomerPriceInput>[];
        capacity?: integer | string;
        landedCostItem?: LandedCostItemInput;
        itemTaxGroup?: integer | string;
        postingClass?: integer | string;
        storedDimensions?: string;
        storedAttributes?: string;
        _syncTick?: decimal | string;
        _attachments?: Partial<AttachmentAssociationInput>[];
        id?: string;
        lotManagement?: LotManagement;
        lotSequenceNumber?: integer | string;
        isExpiryManaged?: boolean | string;
        analyticalData?: integer | string;
    }
    export interface ItemBindingExtension {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        name: string;
        description: string;
        status: ItemStatus;
        isActive: boolean;
        type: ItemType;
        isBought: boolean;
        isManufactured: boolean;
        isSold: boolean;
        eanNumber: string;
        commodityCode: string;
        stockUnit: UnitOfMeasure;
        volumeUnit: UnitOfMeasure;
        volume: string;
        weightUnit: UnitOfMeasure;
        weight: string;
        density: string;
        image: BinaryStream;
        allergens: ClientCollection<ItemAllergenBinding>;
        classifications: ClientCollection<ItemClassificationsBinding>;
        category: ItemCategory;
        isStockManaged: boolean;
        isPhantom: boolean;
        isBomRevisionManaged: boolean;
        bomRevisionSequenceNumber: BomRevisionSequence;
        isPotencyManagement: boolean;
        isTraceabilityManagement: boolean;
        supplierPrices: ClientCollection<ItemSupplierPriceBinding>;
        itemSites: ClientCollection<ItemSiteBinding>;
        suppliers: ClientCollection<ItemSupplier>;
        customers: ClientCollection<ItemCustomer>;
        purchaseUnit: UnitOfMeasure;
        purchaseUnitToStockUnitConversion: string;
        purchaseUnitToStockUnitConversionDedicated: boolean;
        serialNumberManagement: SerialNumberManagement;
        serialNumberSequenceNumber: SequenceNumber;
        serialNumberUsage: SerialNumberUsage;
        useSupplierSerialNumbers: boolean;
        salesUnit: UnitOfMeasure;
        salesUnitToStockUnitConversion: string;
        salesUnitToStockUnitConversionDedicated: boolean;
        minimumSalesQuantity: string;
        maximumSalesQuantity: string;
        currency: Currency;
        basePrice: string;
        minimumPrice: string;
        customerPrices: ClientCollection<ItemCustomerPriceBinding>;
        capacity: integer;
        landedCostItem: LandedCostItemBinding;
        landedCostAllocationRule: AllocationRule;
        landedCostAllocationRuleUnit: UnitOfMeasure;
        inStockQuantity: string;
        inTransitStockQuantity: string;
        itemTaxGroup: ItemTaxGroup;
        postingClass: PostingClass;
        storedDimensions: any;
        storedAttributes: any;
        billOfMaterials: ClientCollection<BillOfMaterial>;
        _syncTick: string;
        _attachments: ClientCollection<AttachmentAssociation>;
        id: string;
        lotManagement: LotManagement;
        lotSequenceNumber: SequenceNumber;
        isExpiryManaged: boolean;
        analyticalData: AnalyticalData;
    }
    export interface ItemExtension$AsyncOperations {
        createTestItems: AsyncOperation<
            {
                itemId: string;
                quantity: integer | string;
                stockCreation?: {
                    maxStockQuantity: decimal | string;
                    isStockQuantityFixed: boolean | string;
                };
            },
            string
        >;
    }
    export interface ItemExtension$Operations {
        asyncOperations: ItemExtension$AsyncOperations;
    }
    export interface ItemSiteCostExtension {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        itemSite: ItemSite;
        costCategory: CostCategory;
        fromDate: string;
        toDate: string;
        version: integer;
        stockUnit: UnitOfMeasure;
        forQuantity: string;
        isCalculated: boolean;
        materialCost: string;
        machineCost: string;
        laborCost: string;
        toolCost: string;
        indirectCost: string;
        totalCost: string;
        unitCost: string;
        areStockTransactionsAvailable: boolean;
        storedDimensions: string;
        storedAttributes: string;
        stockValueChange: StockValueChange;
        analyticalData: AnalyticalData;
    }
    export interface ItemSiteCostInputExtension {
        itemSite?: integer | string;
        costCategory?: integer | string;
        fromDate?: string;
        toDate?: string;
        version?: integer | string;
        forQuantity?: decimal | string;
        isCalculated?: boolean | string;
        materialCost?: decimal | string;
        machineCost?: decimal | string;
        laborCost?: decimal | string;
        toolCost?: decimal | string;
        indirectCost?: decimal | string;
        storedDimensions?: string;
        storedAttributes?: string;
        stockValueChange?: integer | string;
        analyticalData?: integer | string;
    }
    export interface ItemSiteCostBindingExtension {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        itemSite: ItemSite;
        costCategory: CostCategory;
        fromDate: string;
        toDate: string;
        version: integer;
        stockUnit: UnitOfMeasure;
        forQuantity: string;
        isCalculated: boolean;
        materialCost: string;
        machineCost: string;
        laborCost: string;
        toolCost: string;
        indirectCost: string;
        totalCost: string;
        unitCost: string;
        areStockTransactionsAvailable: boolean;
        storedDimensions: any;
        storedAttributes: any;
        stockValueChange: StockValueChange;
        analyticalData: AnalyticalData;
    }
    export interface ItemSiteCostExtension$AsyncOperations {
        syncStockValueChange: AsyncOperation<{}, boolean>;
        standardCostRollUpCalculation: AsyncOperation<
            {
                inputSet: string;
            },
            boolean
        >;
    }
    export interface ItemSiteCostExtension$Lookups {
        stockValueChange: QueryOperation<StockValueChange>;
        analyticalData: QueryOperation<AnalyticalData>;
    }
    export interface ItemSiteCostExtension$Operations {
        asyncOperations: ItemSiteCostExtension$AsyncOperations;
        lookups(dataOrId: string | { data: ItemSiteCostInput }): ItemSiteCostExtension$Lookups;
    }
    export interface ItemSiteExtension {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        item: Item;
        site: Site;
        id: string;
        prodLeadTime: integer;
        stockUnit: UnitOfMeasure;
        safetyStock: string;
        batchQuantity: string;
        itemSiteCost: ClientCollection<ItemSiteCost>;
        stdCostValue: string;
        purchaseLeadTime: integer;
        isOrderToOrder: boolean;
        expectedQuantity: string;
        requiredQuantity: string;
        replenishmentMethod: ReplenishmentMethod;
        reorderPoint: string;
        preferredProcess: PreferredProcess;
        suppliers: ClientCollection<ItemSiteSupplier>;
        defaultSupplier: Supplier;
        indirectCostSection: IndirectCostSection;
        valuationMethod: CostValuationMethod;
        costs: ClientCollection<ItemSiteCost>;
        outboundDefaultLocation: Location;
        inboundDefaultLocation: Location;
        completedProductDefaultLocation: Location;
        stockRecords: ClientCollection<Stock>;
        inStockQuantity: string;
        allocatedQuantity: string;
        acceptedStockQuantity: string;
        rejectedStockQuantity: string;
        onQualityControlStockQuantity: string;
        inTransitStockQuantity: string;
        fifoCosts: ClientCollection<FifoValuationTier>;
        fifoCostValue: string;
        fifoCostSum: string;
        currentCost: string;
        stockValueAtStandardCost: string;
        stockValue: string;
        countingInProgress: boolean;
        areStockTransactionsAvailable: boolean;
        allocations: ClientCollection<StockAllocation>;
        allocationRecords: ClientCollection<StockAllocation>;
        inboundDefaultQualityValue: StockStatus;
        completedProductDefaultQualityValue: StockStatus;
        isSpecificItemTaxGroup: boolean;
        itemTaxGroup: ItemTaxGroup;
        billOfMaterial: BillOfMaterial;
        routing: Routing;
        countStockRecords: ClientCollection<Stock>;
        hasStockRecords: boolean;
        lastCountDate: string;
        economicOrderQuantity: string;
        stockValuationAtAverageCost: string;
        averageCostValue: string;
    }
    export interface ItemSiteInputExtension {
        site?: integer | string;
        prodLeadTime?: integer | string;
        safetyStock?: decimal | string;
        batchQuantity?: decimal | string;
        purchaseLeadTime?: integer | string;
        isOrderToOrder?: boolean | string;
        replenishmentMethod?: ReplenishmentMethod;
        reorderPoint?: decimal | string;
        preferredProcess?: PreferredProcess;
        indirectCostSection?: integer | string;
        valuationMethod?: CostValuationMethod;
        outboundDefaultLocation?: integer | string;
        inboundDefaultLocation?: integer | string;
        completedProductDefaultLocation?: integer | string;
        inboundDefaultQualityValue?: integer | string;
        completedProductDefaultQualityValue?: integer | string;
        isSpecificItemTaxGroup?: boolean | string;
        itemTaxGroup?: integer | string;
        lastCountDate?: string;
        economicOrderQuantity?: decimal | string;
        stockValuationAtAverageCost?: decimal | string;
    }
    export interface ItemSiteBindingExtension {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        item: Item;
        site: Site;
        id: string;
        prodLeadTime: integer;
        stockUnit: UnitOfMeasure;
        safetyStock: string;
        batchQuantity: string;
        itemSiteCost: ClientCollection<ItemSiteCost>;
        stdCostValue: string;
        purchaseLeadTime: integer;
        isOrderToOrder: boolean;
        expectedQuantity: string;
        requiredQuantity: string;
        replenishmentMethod: ReplenishmentMethod;
        reorderPoint: string;
        preferredProcess: PreferredProcess;
        suppliers: ClientCollection<ItemSiteSupplier>;
        defaultSupplier: Supplier;
        indirectCostSection: IndirectCostSection;
        valuationMethod: CostValuationMethod;
        costs: ClientCollection<ItemSiteCost>;
        outboundDefaultLocation: Location;
        inboundDefaultLocation: Location;
        completedProductDefaultLocation: Location;
        stockRecords: ClientCollection<Stock>;
        inStockQuantity: string;
        allocatedQuantity: string;
        acceptedStockQuantity: string;
        rejectedStockQuantity: string;
        onQualityControlStockQuantity: string;
        inTransitStockQuantity: string;
        fifoCosts: ClientCollection<FifoValuationTier>;
        fifoCostValue: string;
        fifoCostSum: string;
        currentCost: string;
        stockValueAtStandardCost: string;
        stockValue: string;
        countingInProgress: boolean;
        areStockTransactionsAvailable: boolean;
        allocations: ClientCollection<StockAllocation>;
        allocationRecords: ClientCollection<StockAllocation>;
        inboundDefaultQualityValue: StockStatus;
        completedProductDefaultQualityValue: StockStatus;
        isSpecificItemTaxGroup: boolean;
        itemTaxGroup: ItemTaxGroup;
        billOfMaterial: BillOfMaterial;
        routing: Routing;
        countStockRecords: ClientCollection<Stock>;
        hasStockRecords: boolean;
        lastCountDate: string;
        economicOrderQuantity: string;
        stockValuationAtAverageCost: string;
        averageCostValue: string;
    }
    export interface ItemSiteExtension$Queries {
        getStockValuation: Node$Operation<
            {
                searchCriteria: {
                    itemSite?: integer | string;
                    dateOfValuation?: string;
                };
            },
            {
                quantityInStock: string;
                unitCost: string;
                stockValue: string;
            }
        >;
    }
    export interface ItemSiteExtension$Operations {
        queries: ItemSiteExtension$Queries;
        getDefaults: GetDefaultsOperation<ItemSite>;
    }
    export interface MrpCalculationExtension {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        description: string;
        calculationDate: string;
        user: User;
        sites: string;
        companies: string;
        fromItem: string;
        toItem: string;
        startDate: string;
        numberOfWeeks: integer;
        explodeBillOfMaterial: boolean;
        isSalesQuoteIncluded: boolean;
        calculationStatus: CalculationStatus;
        errorMessage: string;
        lowLevelCode: integer;
        workLines: ClientCollection<MrpWorkLine>;
        resultLines: ClientCollection<MrpResultLine>;
        inputSet: MrpInputSet;
    }
    export interface MrpCalculationInputExtension {
        description?: string;
        calculationDate?: string;
        user?: integer | string;
        sites?: string;
        companies?: string;
        fromItem?: string;
        toItem?: string;
        startDate?: string;
        numberOfWeeks?: integer | string;
        explodeBillOfMaterial?: boolean | string;
        isSalesQuoteIncluded?: boolean | string;
        calculationStatus?: CalculationStatus;
        errorMessage?: string;
        lowLevelCode?: integer | string;
        inputSet?: integer | string;
    }
    export interface MrpCalculationBindingExtension {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        description: string;
        calculationDate: string;
        user: User;
        sites: any;
        companies: any;
        fromItem: any;
        toItem: any;
        startDate: string;
        numberOfWeeks: integer;
        explodeBillOfMaterial: boolean;
        isSalesQuoteIncluded: boolean;
        calculationStatus: CalculationStatus;
        errorMessage: string;
        lowLevelCode: integer;
        workLines: ClientCollection<MrpWorkLine>;
        resultLines: ClientCollection<MrpResultLine>;
        inputSet: MrpInputSet;
    }
    export interface MrpCalculationExtension$AsyncOperations {
        deleteOldMrpCalculations: AsyncOperation<
            {
                numberOfDaysToKeep?: integer | string;
                numberOfRecordsToKeep?: integer | string;
            },
            boolean
        >;
    }
    export interface MrpCalculationExtension$Operations {
        asyncOperations: MrpCalculationExtension$AsyncOperations;
    }
    export interface SerialNumberExtension {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        id: string;
        supplierSerialNumber: string;
        stockRecord: Stock;
        isUsable: boolean;
        item: Item;
        site: Site;
        supplier: Supplier;
        allocation: StockAllocation;
        isAllocated: boolean;
        isInStock: boolean;
        stockMovements: ClientCollection<StockJournalSerialNumber>;
        baseDocumentLine: BaseDocumentLine;
        createDate: string;
        onHandQuantity: string;
        availableQuantity: string;
        allocatedQuantity: string;
    }
    export interface SerialNumberInputExtension {
        id?: string;
        supplierSerialNumber?: string;
        stockRecord?: integer | string;
        isUsable?: boolean | string;
        item?: integer | string;
        site?: integer | string;
        supplier?: integer | string;
        allocation?: integer | string;
        baseDocumentLine?: integer | string;
        createDate?: string;
    }
    export interface SerialNumberBindingExtension {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        id: string;
        supplierSerialNumber: string;
        stockRecord: Stock;
        isUsable: boolean;
        item: Item;
        site: Site;
        supplier: Supplier;
        allocation: StockAllocation;
        isAllocated: boolean;
        isInStock: boolean;
        stockMovements: ClientCollection<StockJournalSerialNumber>;
        baseDocumentLine: BaseDocumentLine;
        createDate: string;
        onHandQuantity: string;
        availableQuantity: string;
        allocatedQuantity: string;
    }
    export interface StockAdjustmentDetailExtension {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        documentLine: BaseDocumentLine;
        effectiveDate: string;
        originDocumentLine: BaseDocumentLine;
        startingSerialNumber: SerialNumber;
        orderCost: string;
        valuedCost: string;
        isTransacted: boolean;
        stockDetailSerialNumberStatus: StockDetailSerialNumberStatus;
        stockMovements: ClientCollection<StockJournal>;
        stockDetailSerialNumbers: ClientCollection<StockDetailSerialNumber>;
        jsonStockDetailSerialNumbers: string;
        stockDetailLot: StockDetailLot;
        jsonStockDetailLot: string;
        stockRecord: Stock;
        deletedStockRecordData: string;
        reasonCode: ReasonCode;
        existingLot: Lot;
        lotCreateData: string;
        site: Site;
        currency: Currency;
        item: Item;
        stockUnit: UnitOfMeasure;
        quantityInStockUnit: string;
        location: Location;
        status: StockStatus;
        owner: string;
    }
    export interface StockAdjustmentDetailInputExtension {
        movementType?: StockMovementType;
        supplier?: integer | string;
        effectiveDate?: string;
        originDocumentLine?: integer | string;
        startingSerialNumber?: integer | string;
        orderCost?: decimal | string;
        valuedCost?: decimal | string;
        stockDetailSerialNumbers?: Partial<StockDetailSerialNumberInput>[];
        jsonStockDetailSerialNumbers?: string;
        stockDetailLot?: StockDetailLotInput;
        jsonStockDetailLot?: string;
        stockRecord?: integer | string;
        deletedStockRecordData?: string;
        reasonCode?: integer | string;
        existingLot?: integer | string;
        lotCreateData?: string;
        site?: integer | string;
        item?: integer | string;
        stockUnit?: integer | string;
        quantityInStockUnit?: decimal | string;
        location?: integer | string;
        status?: integer | string;
        owner?: string;
    }
    export interface StockAdjustmentDetailBindingExtension {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        documentLine: BaseDocumentLine;
        movementType: StockMovementType;
        supplier: SupplierBinding;
        effectiveDate: string;
        originDocumentLine: BaseDocumentLine;
        startingSerialNumber: SerialNumber;
        orderCost: string;
        valuedCost: string;
        isTransacted: boolean;
        stockDetailSerialNumberStatus: StockDetailSerialNumberStatus;
        stockMovements: ClientCollection<StockJournal>;
        stockDetailSerialNumbers: ClientCollection<StockDetailSerialNumberBinding>;
        jsonStockDetailSerialNumbers: any;
        stockDetailLot: StockDetailLotBinding;
        jsonStockDetailLot: any;
        stockRecord: Stock;
        deletedStockRecordData: any;
        reasonCode: ReasonCode;
        existingLot: Lot;
        lotCreateData: any;
        site: Site;
        currency: Currency;
        item: Item;
        stockUnit: UnitOfMeasure;
        quantityInStockUnit: string;
        location: Location;
        status: StockStatus;
        owner: string;
    }
    export interface StockChangeDetailExtension {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        documentLine: BaseDocumentLine;
        effectiveDate: string;
        originDocumentLine: BaseDocumentLine;
        startingSerialNumber: SerialNumber;
        orderCost: string;
        valuedCost: string;
        isTransacted: boolean;
        stockDetailSerialNumberStatus: StockDetailSerialNumberStatus;
        stockMovements: ClientCollection<StockJournal>;
        stockDetailSerialNumbers: ClientCollection<StockDetailSerialNumber>;
        jsonStockDetailSerialNumbers: string;
        stockDetailLot: StockDetailLot;
        jsonStockDetailLot: string;
        stockRecord: Stock;
        deletedStockRecordData: string;
        site: Site;
        currency: Currency;
        item: Item;
        stockUnit: UnitOfMeasure;
        quantityInStockUnit: string;
        location: Location;
        status: StockStatus;
    }
    export interface StockChangeDetailInputExtension {
        movementType?: StockMovementType;
        supplier?: integer | string;
        effectiveDate?: string;
        originDocumentLine?: integer | string;
        startingSerialNumber?: integer | string;
        orderCost?: decimal | string;
        valuedCost?: decimal | string;
        stockDetailSerialNumbers?: Partial<StockDetailSerialNumberInput>[];
        jsonStockDetailSerialNumbers?: string;
        stockDetailLot?: StockDetailLotInput;
        jsonStockDetailLot?: string;
        stockRecord?: integer | string;
        deletedStockRecordData?: string;
        site?: integer | string;
        item?: integer | string;
        stockUnit?: integer | string;
        quantityInStockUnit?: decimal | string;
        location?: integer | string;
        status?: integer | string;
    }
    export interface StockChangeDetailBindingExtension {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        documentLine: BaseDocumentLine;
        movementType: StockMovementType;
        supplier: SupplierBinding;
        effectiveDate: string;
        originDocumentLine: BaseDocumentLine;
        startingSerialNumber: SerialNumber;
        orderCost: string;
        valuedCost: string;
        isTransacted: boolean;
        stockDetailSerialNumberStatus: StockDetailSerialNumberStatus;
        stockMovements: ClientCollection<StockJournal>;
        stockDetailSerialNumbers: ClientCollection<StockDetailSerialNumberBinding>;
        jsonStockDetailSerialNumbers: any;
        stockDetailLot: StockDetailLotBinding;
        jsonStockDetailLot: any;
        stockRecord: Stock;
        deletedStockRecordData: any;
        site: Site;
        currency: Currency;
        item: Item;
        stockUnit: UnitOfMeasure;
        quantityInStockUnit: string;
        location: Location;
        status: StockStatus;
    }
    export interface StockDetailLotExtension {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        stockDetail: StockMovementDetail;
        stockDetailLot: StockDetailLot;
        lotId: string;
        lot: Lot;
        lotNumber: string;
        supplierLot: string;
        expirationDate: string;
        sublot: string;
    }
    export interface StockDetailLotInputExtension {
        stockDetailLot?: integer | string;
        lot?: integer | string;
        lotNumber?: string;
        supplierLot?: string;
        expirationDate?: string;
        sublot?: string;
    }
    export interface StockDetailLotBindingExtension {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        stockDetail: StockMovementDetail;
        stockDetailLot: StockDetailLot;
        lotId: string;
        lot: Lot;
        lotNumber: string;
        supplierLot: string;
        expirationDate: string;
        sublot: string;
    }
    export interface StockExtension {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        isInTransit: boolean;
        site: Site;
        item: Item;
        stockUnit: UnitOfMeasure;
        quantityInStockUnit: string;
        allocatedQuantityInStockUnit: string;
        availableQuantityInStockUnit: string;
        inTransitQuantityInStockUnit: string;
        onHandQuantityInStockUnit: string;
        owner: string;
        lot: Lot;
        status: StockStatus;
        location: Location;
        serialNumbers: ClientCollection<SerialNumber>;
        allocations: ClientCollection<StockAllocation>;
        totalAllocated: string;
        activeQuantityInStockUnit: string;
    }
    export interface StockInputExtension {
        isInTransit?: boolean | string;
        site?: integer | string;
        item?: integer | string;
        stockUnit?: integer | string;
        quantityInStockUnit?: decimal | string;
        owner?: string;
        lot?: integer | string;
        status?: integer | string;
        location?: integer | string;
    }
    export interface StockBindingExtension {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        isInTransit: boolean;
        site: Site;
        item: Item;
        stockUnit: UnitOfMeasure;
        quantityInStockUnit: string;
        allocatedQuantityInStockUnit: string;
        availableQuantityInStockUnit: string;
        inTransitQuantityInStockUnit: string;
        onHandQuantityInStockUnit: string;
        owner: string;
        lot: Lot;
        status: StockStatus;
        location: Location;
        serialNumbers: ClientCollection<SerialNumber>;
        allocations: ClientCollection<StockAllocation>;
        totalAllocated: string;
        activeQuantityInStockUnit: string;
    }
    export interface StockIssueDetailExtension {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        documentLine: BaseDocumentLine;
        effectiveDate: string;
        originDocumentLine: BaseDocumentLine;
        startingSerialNumber: SerialNumber;
        orderCost: string;
        valuedCost: string;
        isTransacted: boolean;
        stockDetailSerialNumberStatus: StockDetailSerialNumberStatus;
        stockMovements: ClientCollection<StockJournal>;
        stockDetailSerialNumbers: ClientCollection<StockDetailSerialNumber>;
        jsonStockDetailSerialNumbers: string;
        stockDetailLot: StockDetailLot;
        jsonStockDetailLot: string;
        stockRecord: Stock;
        deletedStockRecordData: string;
        site: Site;
        currency: Currency;
        item: Item;
        stockUnit: UnitOfMeasure;
        quantityInStockUnit: string;
        location: Location;
        status: StockStatus;
    }
    export interface StockIssueDetailInputExtension {
        movementType?: StockMovementType;
        supplier?: integer | string;
        effectiveDate?: string;
        originDocumentLine?: integer | string;
        startingSerialNumber?: integer | string;
        orderCost?: decimal | string;
        valuedCost?: decimal | string;
        stockDetailSerialNumbers?: Partial<StockDetailSerialNumberInput>[];
        jsonStockDetailSerialNumbers?: string;
        stockDetailLot?: StockDetailLotInput;
        jsonStockDetailLot?: string;
        stockRecord?: integer | string;
        deletedStockRecordData?: string;
        site?: integer | string;
        item?: integer | string;
        stockUnit?: integer | string;
        quantityInStockUnit?: decimal | string;
        location?: integer | string;
        status?: integer | string;
    }
    export interface StockIssueDetailBindingExtension {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        documentLine: BaseDocumentLine;
        movementType: StockMovementType;
        supplier: SupplierBinding;
        effectiveDate: string;
        originDocumentLine: BaseDocumentLine;
        startingSerialNumber: SerialNumber;
        orderCost: string;
        valuedCost: string;
        isTransacted: boolean;
        stockDetailSerialNumberStatus: StockDetailSerialNumberStatus;
        stockMovements: ClientCollection<StockJournal>;
        stockDetailSerialNumbers: ClientCollection<StockDetailSerialNumberBinding>;
        jsonStockDetailSerialNumbers: any;
        stockDetailLot: StockDetailLotBinding;
        jsonStockDetailLot: any;
        stockRecord: Stock;
        deletedStockRecordData: any;
        site: Site;
        currency: Currency;
        item: Item;
        stockUnit: UnitOfMeasure;
        quantityInStockUnit: string;
        location: Location;
        status: StockStatus;
    }
    export interface Package {
        '@sage/xtrem-stock/CostRollUpInputSet': CostRollUpInputSet$Operations;
        '@sage/xtrem-stock/CostRollUpResultLine': CostRollUpResultLine$Operations;
        '@sage/xtrem-stock/CostRollUpSubAssembly': CostRollUpSubAssembly$Operations;
        '@sage/xtrem-stock/StockAdjustment': StockAdjustment$Operations;
        '@sage/xtrem-stock/StockChange': StockChange$Operations;
        '@sage/xtrem-stock/StockCount': StockCount$Operations;
        '@sage/xtrem-stock/StockCountLineSerialNumber': StockCountLineSerialNumber$Operations;
        '@sage/xtrem-stock/StockIssue': StockIssue$Operations;
        '@sage/xtrem-stock/StockReceipt': StockReceipt$Operations;
        '@sage/xtrem-stock/StockReorderCalculationInputSet': StockReorderCalculationInputSet$Operations;
        '@sage/xtrem-stock/StockReorderCalculationResultLine': StockReorderCalculationResultLine$Operations;
        '@sage/xtrem-stock/StockValuationInputSet': StockValuationInputSet$Operations;
        '@sage/xtrem-stock/StockValuationInputSetToSite': StockValuationInputSetToSite$Operations;
        '@sage/xtrem-stock/StockValuationResultLine': StockValuationResultLine$Operations;
        '@sage/xtrem-stock/StockValueChange': StockValueChange$Operations;
        '@sage/xtrem-stock/StockValueCorrection': StockValueCorrection$Operations;
        '@sage/xtrem-stock/StockAdjustmentLine': StockAdjustmentLine$Operations;
        '@sage/xtrem-stock/StockChangeLine': StockChangeLine$Operations;
        '@sage/xtrem-stock/StockCountLine': StockCountLine$Operations;
        '@sage/xtrem-stock/StockIssueLine': StockIssueLine$Operations;
        '@sage/xtrem-stock/StockReceiptLine': StockReceiptLine$Operations;
        '@sage/xtrem-stock/StockValueChangeLine': StockValueChangeLine$Operations;
        '@sage/xtrem-stock/StockValueCorrectionLine': StockValueCorrectionLine$Operations;
    }
    export interface GraphApi
        extends Package,
            SageXtremAuditing$Package,
            SageXtremAuthorization$Package,
            SageXtremCommunication$Package,
            SageXtremCustomization$Package,
            SageXtremDashboard$Package,
            SageXtremFinanceData$Package,
            SageXtremImportExport$Package,
            SageXtremLandedCost$Package,
            SageXtremMailer$Package,
            SageXtremMasterData$Package,
            SageXtremMetadata$Package,
            SageXtremMrpData$Package,
            SageXtremReporting$Package,
            SageXtremRouting$Package,
            SageXtremScheduler$Package,
            SageXtremStockData$Package,
            SageXtremStructure$Package,
            SageXtremSynchronization$Package,
            SageXtremSystem$Package,
            SageXtremTax$Package,
            SageXtremTechnicalData$Package,
            SageXtremUpload$Package,
            SageXtremWorkflow$Package {}
}
declare module '@sage/xtrem-stock-api' {
    export type * from '@sage/xtrem-stock-api-partial';
}
declare module '@sage/xtrem-auditing-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-stock-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-authorization-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-stock-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-communication-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-stock-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-customization-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-stock-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-dashboard-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-stock-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-finance-data-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-stock-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-import-export-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-stock-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-landed-cost-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-stock-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-mailer-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-stock-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-master-data-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-stock-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-metadata-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-stock-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-mrp-data-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-stock-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-reporting-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-stock-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-routing-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-stock-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-scheduler-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-stock-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-stock-data-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-stock-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-structure-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-stock-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-synchronization-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-stock-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-system-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-stock-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-tax-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-stock-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-technical-data-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-stock-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-upload-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-stock-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-workflow-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-stock-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-technical-data-api-partial' {
    import type { ComponentBindingExtension, ComponentExtension, ComponentInputExtension } from '@sage/xtrem-stock-api';
    export interface Component extends ComponentExtension {}
    export interface ComponentBinding extends ComponentBindingExtension {}
    export interface ComponentInput extends ComponentInputExtension {}
}
declare module '@sage/xtrem-master-data-api-partial' {
    import type {
        ItemBindingExtension,
        ItemExtension,
        ItemExtension$AsyncOperations,
        ItemExtension$Operations,
        ItemInputExtension,
        ItemSiteBindingExtension,
        ItemSiteCostBindingExtension,
        ItemSiteCostExtension,
        ItemSiteCostExtension$AsyncOperations,
        ItemSiteCostExtension$Lookups,
        ItemSiteCostExtension$Operations,
        ItemSiteCostInputExtension,
        ItemSiteExtension,
        ItemSiteExtension$Operations,
        ItemSiteExtension$Queries,
        ItemSiteInputExtension,
    } from '@sage/xtrem-stock-api';
    export interface Item extends ItemExtension {}
    export interface ItemBinding extends ItemBindingExtension {}
    export interface ItemInput extends ItemInputExtension {}
    export interface Item$AsyncOperations extends ItemExtension$AsyncOperations {}
    export interface Item$Operations extends ItemExtension$Operations {}
    export interface ItemSiteCost extends ItemSiteCostExtension {}
    export interface ItemSiteCostBinding extends ItemSiteCostBindingExtension {}
    export interface ItemSiteCostInput extends ItemSiteCostInputExtension {}
    export interface ItemSiteCost$Lookups extends ItemSiteCostExtension$Lookups {}
    export interface ItemSiteCost$AsyncOperations extends ItemSiteCostExtension$AsyncOperations {}
    export interface ItemSiteCost$Operations extends ItemSiteCostExtension$Operations {}
    export interface ItemSite extends ItemSiteExtension {}
    export interface ItemSiteBinding extends ItemSiteBindingExtension {}
    export interface ItemSiteInput extends ItemSiteInputExtension {}
    export interface ItemSite$Queries extends ItemSiteExtension$Queries {}
    export interface ItemSite$Operations extends ItemSiteExtension$Operations {}
}
declare module '@sage/xtrem-mrp-data-api-partial' {
    import type {
        MrpCalculationBindingExtension,
        MrpCalculationExtension,
        MrpCalculationExtension$AsyncOperations,
        MrpCalculationExtension$Operations,
        MrpCalculationInputExtension,
    } from '@sage/xtrem-stock-api';
    export interface MrpCalculation extends MrpCalculationExtension {}
    export interface MrpCalculationBinding extends MrpCalculationBindingExtension {}
    export interface MrpCalculationInput extends MrpCalculationInputExtension {}
    export interface MrpCalculation$AsyncOperations extends MrpCalculationExtension$AsyncOperations {}
    export interface MrpCalculation$Operations extends MrpCalculationExtension$Operations {}
}
declare module '@sage/xtrem-stock-data-api-partial' {
    import type {
        SerialNumberBindingExtension,
        SerialNumberExtension,
        SerialNumberInputExtension,
        StockAdjustmentDetailBindingExtension,
        StockAdjustmentDetailExtension,
        StockAdjustmentDetailInputExtension,
        StockBindingExtension,
        StockChangeDetailBindingExtension,
        StockChangeDetailExtension,
        StockChangeDetailInputExtension,
        StockDetailLotBindingExtension,
        StockDetailLotExtension,
        StockDetailLotInputExtension,
        StockExtension,
        StockInputExtension,
        StockIssueDetailBindingExtension,
        StockIssueDetailExtension,
        StockIssueDetailInputExtension,
    } from '@sage/xtrem-stock-api';
    export interface SerialNumber extends SerialNumberExtension {}
    export interface SerialNumberBinding extends SerialNumberBindingExtension {}
    export interface SerialNumberInput extends SerialNumberInputExtension {}
    export interface StockAdjustmentDetail extends StockAdjustmentDetailExtension {}
    export interface StockAdjustmentDetailBinding extends StockAdjustmentDetailBindingExtension {}
    export interface StockAdjustmentDetailInput extends StockAdjustmentDetailInputExtension {}
    export interface StockChangeDetail extends StockChangeDetailExtension {}
    export interface StockChangeDetailBinding extends StockChangeDetailBindingExtension {}
    export interface StockChangeDetailInput extends StockChangeDetailInputExtension {}
    export interface StockDetailLot extends StockDetailLotExtension {}
    export interface StockDetailLotBinding extends StockDetailLotBindingExtension {}
    export interface StockDetailLotInput extends StockDetailLotInputExtension {}
    export interface Stock extends StockExtension {}
    export interface StockBinding extends StockBindingExtension {}
    export interface StockInput extends StockInputExtension {}
    export interface StockIssueDetail extends StockIssueDetailExtension {}
    export interface StockIssueDetailBinding extends StockIssueDetailBindingExtension {}
    export interface StockIssueDetailInput extends StockIssueDetailInputExtension {}
}
