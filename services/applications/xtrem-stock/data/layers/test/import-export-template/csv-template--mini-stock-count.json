{"data": [{"_id": "0", "path": "!number", "locale": "", "dataType": "string", "isCustom": false, "description": "number"}, {"_id": "50", "path": "counter", "locale": "", "dataType": "string", "isCustom": false, "description": "counter"}, {"_id": "120", "path": "#lines", "locale": "", "dataType": "collection", "isCustom": false, "description": "lines"}, {"_id": "130", "path": "!_sortValue", "locale": "", "dataType": "integer", "isCustom": false, "description": "sort value"}, {"_id": "150", "path": "status#1", "locale": "", "dataType": "enum(to<PERSON><PERSON><PERSON><PERSON><PERSON>,countInProgress,counted,excluded)", "isCustom": false, "description": "status"}, {"_id": "160", "path": "*item", "locale": "", "dataType": "reference", "isCustom": false, "description": "item (#id)"}, {"_id": "170", "path": "location", "locale": "", "dataType": "reference", "isCustom": false, "description": "location (#id|locationZone)"}, {"_id": "210", "path": "*stockStatus", "locale": "", "dataType": "reference", "isCustom": false, "description": "stock status (#id)"}, {"_id": "220", "path": "owner", "locale": "", "dataType": "string", "isCustom": false, "description": "owner"}, {"_id": "250", "path": "countedQuantityInStockUnit", "locale": "", "dataType": "decimal", "isCustom": false, "description": "counted quantity in stock unit"}, {"_id": "260", "path": "newLineOrderCost", "locale": "", "dataType": "decimal", "isCustom": false, "description": "new line order cost"}, {"_id": "350", "path": "##stockDetails", "locale": "", "dataType": "collection", "isCustom": false, "description": "stock details"}, {"_id": "620", "path": "_sortValue", "locale": "", "dataType": "integer", "isCustom": null, "description": "sort value"}, {"_id": "610", "path": "reasonCode", "locale": "", "dataType": "reference", "isCustom": false, "description": "reason code (#id)"}, {"_id": "510", "path": "location#1", "locale": "", "dataType": "reference", "isCustom": false, "description": "location (#id|locationZone)"}, {"_id": "520", "path": "status#3", "locale": "", "dataType": "reference", "isCustom": false, "description": "status (#id)"}, {"_id": "530", "path": "owner#1", "locale": "", "dataType": "string", "isCustom": false, "description": "owner"}, {"_id": "540", "path": "///stockDetailLot", "locale": "", "dataType": "reference", "isCustom": false, "description": "stock detail lot"}, {"_id": "550", "path": "lot#2", "locale": "", "dataType": "reference", "isCustom": false, "description": "lot (#item|id|sublot)"}, {"_id": "560", "path": "lotNumber", "locale": "", "dataType": "string", "isCustom": false, "description": "lot number"}, {"_id": "570", "path": "supplierLot", "locale": "", "dataType": "string", "isCustom": false, "description": "supplier lot"}, {"_id": "580", "path": "expirationDate", "locale": "", "dataType": "date", "isCustom": false, "description": "expiration date (yyyy-MM-dd)"}, {"_id": "590", "path": "sublot", "locale": "", "dataType": "string", "isCustom": false, "description": "sublot"}]}