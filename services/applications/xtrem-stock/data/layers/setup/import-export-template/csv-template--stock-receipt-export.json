{"data": [{"_id": "0", "path": "number", "locale": "", "dataType": "string", "isCustom": false, "description": "number"}, {"_id": "10", "path": "description", "locale": "", "dataType": "string", "isCustom": false, "description": "description"}, {"_id": "20", "path": "effectiveDate", "locale": "", "dataType": "date", "isCustom": false, "description": "effective date (YYYY-MM-DD)"}, {"_id": "30", "path": "*stockSite", "locale": "", "dataType": "reference", "isCustom": false, "description": "stock site (#id)"}, {"_id": "40", "path": "#lines", "locale": "", "dataType": "collection", "isCustom": false, "description": "lines"}, {"_id": "50", "path": "stockTransactionStatus", "locale": "", "dataType": "enum(draft,inProgress,completed,error)", "isCustom": false, "description": "stock transaction status"}, {"_id": "60", "path": "*item", "locale": "", "dataType": "reference", "isCustom": false, "description": "item (#id)"}, {"_id": "70", "path": "stockUnit", "locale": "", "dataType": "reference", "isCustom": false, "description": "stock unit (#id)"}, {"_id": "80", "path": "quantityInStockUnit", "locale": "", "dataType": "decimal", "isCustom": false, "description": "quantity in stock unit"}, {"_id": "90", "path": "location", "locale": "", "dataType": "reference", "isCustom": false, "description": "location (#id|locationZone)"}, {"_id": "100", "path": "stockStatus", "locale": "", "dataType": "reference", "isCustom": false, "description": "stock status (#id)"}, {"_id": "110", "path": "existingLot", "locale": "", "dataType": "reference", "isCustom": false, "description": "existing lot (#item|id|sublot)"}, {"_id": "120", "path": "lotCreateData", "locale": "", "dataType": "json", "isCustom": false, "description": "lot create data"}, {"_id": "130", "path": "orderCost", "locale": "", "dataType": "decimal", "isCustom": false, "description": "order cost"}, {"_id": "140", "path": "valuedCost", "locale": "", "dataType": "decimal", "isCustom": false, "description": "valued cost"}, {"_id": "150", "path": "storedDimensions", "locale": "", "dataType": "json", "isCustom": false, "description": "stored dimensions"}, {"_id": "160", "path": "storedAttributes", "locale": "", "dataType": "json", "isCustom": false, "description": "stored attributes"}, {"_id": "170", "path": "##stockDetails", "locale": "", "dataType": "collection", "isCustom": false, "description": "stock details"}, {"_id": "180", "path": "*site", "locale": "", "dataType": "reference", "isCustom": false, "description": "site (#id)"}, {"_id": "190", "path": "*item#1", "locale": "", "dataType": "reference", "isCustom": false, "description": "item (#id)"}, {"_id": "200", "path": "stockUnit#1", "locale": "", "dataType": "reference", "isCustom": false, "description": "stock unit (#id)"}, {"_id": "210", "path": "quantityInStockUnit#1", "locale": "", "dataType": "decimal", "isCustom": false, "description": "quantity in stock unit"}, {"_id": "220", "path": "movementType", "locale": "", "dataType": "enum(receipt,issue,change,adjustment,correction,valueChange)", "isCustom": false, "description": "movement type"}, {"_id": "230", "path": "supplier", "locale": "", "dataType": "reference", "isCustom": false, "description": "supplier (#businessEntity)"}, {"_id": "240", "path": "*status", "locale": "", "dataType": "reference", "isCustom": false, "description": "status (#id)"}, {"_id": "250", "path": "effectiveDate#1", "locale": "", "dataType": "date", "isCustom": false, "description": "effective date (YYYY-MM-DD)"}, {"_id": "260", "path": "orderCost#1", "locale": "", "dataType": "decimal", "isCustom": false, "description": "order cost"}, {"_id": "270", "path": "valuedCost#1", "locale": "", "dataType": "decimal", "isCustom": false, "description": "valued cost"}, {"_id": "280", "path": "location#1", "locale": "", "dataType": "reference", "isCustom": false, "description": "location (#id|locationZone)"}, {"_id": "290", "path": "existingLot#1", "locale": "", "dataType": "reference", "isCustom": false, "description": "existing lot (#item|id|sublot)"}, {"_id": "300", "path": "lotCreateData#1", "locale": "", "dataType": "json", "isCustom": false, "description": "lot create data"}, {"_id": "310", "path": "owner", "locale": "", "dataType": "string", "isCustom": false, "description": "owner"}, {"_id": "320", "path": "///stockDetailLot", "locale": "", "dataType": "reference", "isCustom": false, "description": "stock detail lot"}, {"_id": "330", "path": "lot", "locale": "", "dataType": "reference", "isCustom": false, "description": "lot (#item|id|sublot)"}, {"_id": "340", "path": "lotNumber", "locale": "", "dataType": "string", "isCustom": false, "description": "lot number"}, {"_id": "350", "path": "supplierLot", "locale": "", "dataType": "string", "isCustom": false, "description": "supplier lot"}, {"_id": "360", "path": "expirationDate", "locale": "", "dataType": "date", "isCustom": false, "description": "expiration date (YYYY-MM-DD)"}, {"_id": "370", "path": "sublot", "locale": "", "dataType": "string", "isCustom": false, "description": "sublot"}, {"_id": "380", "path": "##stockTransactions", "locale": "", "dataType": "collection", "isCustom": false, "description": "stock transactions"}, {"_id": "390", "path": "notificationId", "locale": "", "dataType": "string", "isCustom": false, "description": "notification id"}, {"_id": "400", "path": "status", "locale": "", "dataType": "enum(inProgress,succeeded,error)", "isCustom": false, "description": "status"}, {"_id": "410", "path": "message", "locale": "", "dataType": "string", "isCustom": false, "description": "message"}, {"_id": "420", "path": "resultAction", "locale": "", "dataType": "enum(none,created,increased,decreased,deleted,adjusted,changed,allocated,deallocated,corrected,noChange,valueChange)", "isCustom": false, "description": "result action"}]}