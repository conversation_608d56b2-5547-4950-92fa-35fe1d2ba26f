{"data": [{"_id": "0", "path": "*site", "locale": "", "dataType": "reference", "isCustom": false, "description": "site (#id)"}, {"_id": "80", "path": "site.name", "locale": "", "dataType": "string", "description": "name"}, {"_id": "90", "path": "site.id", "locale": "", "dataType": "string", "description": "id"}, {"_id": "10", "path": "*item", "locale": "", "dataType": "reference", "isCustom": false, "description": "item (#id)"}, {"_id": "100", "path": "item.name", "locale": "en", "dataType": "localized text", "description": "\"default locale: base, other locales can be specified with (locale-name), for example \"\"name (de-DE)\"\". Available locales are (en-GB, fr-FR, ...). You can also duplicate the columns to import several translations\""}, {"_id": "110", "path": "item.id", "locale": "", "dataType": "string", "description": "id"}, {"_id": "20", "path": "*stockUnit", "locale": "", "dataType": "reference", "isCustom": false, "description": "stock unit (#id)"}, {"_id": "250", "path": "stockUnit.name", "locale": "en", "dataType": "localized text", "description": "\"default locale: base, other locales can be specified with (locale-name), for example \"\"name (de-DE)\"\". Available locales are (en-GB, fr-FR, ...). You can also duplicate the columns to import several translations\""}, {"_id": "30", "path": "quantityInStockUnit", "locale": "", "dataType": "decimal", "isCustom": false, "description": "quantity in stock unit"}, {"_id": "140", "path": "*status", "locale": "", "dataType": "reference", "isCustom": false, "description": "status (#id)"}, {"_id": "180", "path": "status.name", "locale": "en", "dataType": "localized text", "description": "\"default locale: base, other locales can be specified with (locale-name), for example \"\"name (de-DE)\"\". Available locales are (en-GB, fr-FR, ...). You can also duplicate the columns to import several translations\""}, {"_id": "190", "path": "status.id", "locale": "", "dataType": "string", "description": "id"}, {"_id": "130", "path": "location", "locale": "", "dataType": "reference", "isCustom": false, "description": "location (#id|locationZone)"}, {"_id": "150", "path": "location.name", "locale": "", "dataType": "string", "description": "name"}, {"_id": "160", "path": "location.id", "locale": "", "dataType": "string", "description": "id"}, {"_id": "120", "path": "lot", "locale": "", "dataType": "reference", "isCustom": false, "description": "lot (#item|id|sublot)"}, {"_id": "170", "path": "lot.id", "locale": "", "dataType": "string", "description": "id"}, {"_id": "210", "path": "lot.sublot", "locale": "", "dataType": "string", "description": "sublot"}, {"_id": "200", "path": "lot.supplierLot", "locale": "", "dataType": "string", "description": "supplier lot"}, {"_id": "220", "path": "lot.expirationDate", "locale": "", "dataType": "date", "description": "expiration date (yyyy-MM-dd)"}, {"_id": "230", "path": "lot.creationDate", "locale": "", "dataType": "date", "description": "creation date (yyyy-MM-dd)"}, {"_id": "240", "path": "*owner", "locale": "", "dataType": "string", "isCustom": null, "description": "owner"}]}