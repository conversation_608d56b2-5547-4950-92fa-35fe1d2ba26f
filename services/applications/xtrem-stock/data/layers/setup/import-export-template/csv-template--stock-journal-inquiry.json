{"data": [{"_id": "10", "path": "*site", "locale": "", "dataType": "reference", "isCustom": false, "description": "site (#id)"}, {"_id": "240", "path": "site.name", "locale": "", "dataType": "string", "description": "name"}, {"_id": "50", "path": "effectiveDate", "locale": "", "dataType": "date", "isCustom": false, "description": "effective date (yyyy-MM-dd)"}, {"_id": "20", "path": "*item", "locale": "", "dataType": "reference", "isCustom": false, "description": "item (#id)"}, {"_id": "190", "path": "item.name", "locale": "en", "dataType": "localized text", "description": "\"default locale: base, other locales can be specified with (locale-name), for example \"\"name (de-DE)\"\". Available locales are (en-GB, fr-FR, ...). You can also duplicate the columns to import several translations\""}, {"_id": "40", "path": "quantityInStockUnit", "locale": "", "dataType": "decimal", "isCustom": false, "description": "quantity in stock unit"}, {"_id": "30", "path": "*stockUnit", "locale": "", "dataType": "reference", "isCustom": false, "description": "stock unit (#id)"}, {"_id": "250", "path": "stockUnit.name", "locale": "en", "dataType": "localized text", "description": "\"default locale: base, other locales can be specified with (locale-name), for example \"\"name (de-DE)\"\". Available locales are (en-GB, fr-FR, ...). You can also duplicate the columns to import several translations\""}, {"_id": "70", "path": "lot", "locale": "", "dataType": "reference", "isCustom": false, "description": "lot (#item|id|sublot)"}, {"_id": "340", "path": "lot.id", "locale": "", "dataType": "string", "description": "id"}, {"_id": "290", "path": "lot.sublot", "locale": "", "dataType": "string", "description": "sublot"}, {"_id": "300", "path": "lot.supplierLot", "locale": "", "dataType": "string", "description": "supplier lot"}, {"_id": "310", "path": "lot.expirationDate", "locale": "", "dataType": "date", "description": "expiration date (yyyy-MM-dd)"}, {"_id": "320", "path": "lot.creationDate", "locale": "", "dataType": "date", "description": "creation date (yyyy-MM-dd)"}, {"_id": "80", "path": "status", "locale": "", "dataType": "reference", "isCustom": false, "description": "status (#id)"}, {"_id": "220", "path": "status.name", "locale": "en", "dataType": "localized text", "description": "\"default locale: base, other locales can be specified with (locale-name), for example \"\"name (de-DE)\"\". Available locales are (en-GB, fr-FR, ...). You can also duplicate the columns to import several translations\""}, {"_id": "90", "path": "location", "locale": "", "dataType": "reference", "isCustom": false, "description": "location (#id|locationZone)"}, {"_id": "210", "path": "location.name", "locale": "", "dataType": "string", "description": "name"}, {"_id": "260", "path": "location.id", "locale": "", "dataType": "string", "description": "id"}, {"_id": "270", "path": "location.locationZone", "locale": "", "dataType": "reference", "description": "location zone (#site|id)"}, {"_id": "280", "path": "location.locationZone.name", "locale": "", "dataType": "string", "description": "name"}, {"_id": "350", "path": "location.locationZone.id", "locale": "", "dataType": "string", "description": "id"}, {"_id": "100", "path": "orderCost", "locale": "", "dataType": "decimal", "isCustom": false, "description": "order cost"}, {"_id": "160", "path": "orderAmount", "locale": "", "dataType": "decimal", "isCustom": false, "description": "order amount"}, {"_id": "110", "path": "valuedCost", "locale": "", "dataType": "decimal", "isCustom": false, "description": "valued cost"}, {"_id": "330", "path": "movementAmount", "locale": "", "dataType": "decimal", "isCustom": false, "description": "movement amount"}, {"_id": "140", "path": "nonAbsorbedAmount", "locale": "", "dataType": "decimal", "isCustom": false, "description": "non absorbed amount"}, {"_id": "130", "path": "reasonCode", "locale": "", "dataType": "reference", "isCustom": false, "description": "reason code (#id)"}, {"_id": "200", "path": "reasonCode.name", "locale": "en", "dataType": "localized text", "description": "\"default locale: base, other locales can be specified with (locale-name), for example \"\"name (de-DE)\"\". Available locales are (en-GB, fr-FR, ...). You can also duplicate the columns to import several translations\""}]}