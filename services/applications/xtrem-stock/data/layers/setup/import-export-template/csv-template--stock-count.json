{"data": [{"_id": 0, "path": "!number", "locale": "", "dataType": "string", "isCustom": false, "description": "number"}, {"_id": 10, "path": "*description", "locale": "", "dataType": "string", "isCustom": false, "description": "description"}, {"_id": 20, "path": "status", "locale": "", "dataType": "enum(toBeCounted,countInProgress,counted,closed,draft)", "isCustom": false, "description": "status"}, {"_id": 30, "path": "*stockSite", "locale": "", "dataType": "reference", "isCustom": false, "description": "stock site (#id)"}, {"_id": 40, "path": "effectiveDate", "locale": "", "dataType": "date", "isCustom": false, "description": "effective date (yyyy-MM-dd)"}, {"_id": 50, "path": "counter", "locale": "", "dataType": "string", "isCustom": false, "description": "counter"}, {"_id": 60, "path": "#lines", "locale": "", "dataType": "collection", "isCustom": false, "description": "lines"}, {"_id": 70, "path": "!_sortValue", "locale": "", "dataType": "integer", "isCustom": false, "description": "sort value"}, {"_id": 80, "path": "stockTransactionStatus", "locale": "", "dataType": "enum(draft,inProgress,completed,error)", "isCustom": false, "description": "stock transaction status"}, {"_id": 90, "path": "status#1", "locale": "", "dataType": "enum(to<PERSON><PERSON><PERSON><PERSON><PERSON>,countInProgress,counted,excluded)", "isCustom": false, "description": "status"}, {"_id": 100, "path": "*item", "locale": "", "dataType": "reference", "isCustom": false, "description": "item (#id)"}, {"_id": 110, "path": "location", "locale": "", "dataType": "reference", "isCustom": false, "description": "location (#id|locationZone)"}, {"_id": 120, "path": "lot", "locale": "", "dataType": "reference", "isCustom": false, "description": "lot (#item|id|sublot)"}, {"_id": 140, "path": "stockStatus", "locale": "", "dataType": "reference", "isCustom": false, "description": "stock status (#id)"}, {"_id": 150, "path": "owner", "locale": "", "dataType": "string", "isCustom": false, "description": "owner"}, {"_id": 160, "path": "countedQuantityInStockUnit", "locale": "", "dataType": "decimal", "isCustom": false, "description": "counted quantity in stock unit"}, {"_id": 170, "path": "storedDimensions", "locale": "", "dataType": "json", "isCustom": false, "description": "stored dimensions"}, {"_id": 180, "path": "storedAttributes", "locale": "", "dataType": "json", "isCustom": false, "description": "stored attributes"}, {"_id": 210, "path": "isCreatedFromStockRecord", "locale": "", "dataType": "boolean", "isCustom": false, "description": "is created from stock record (false/true)"}, {"_id": 220, "path": "quantityInStockUnit", "locale": "", "dataType": "decimal", "isCustom": false, "description": "quantity in stock unit"}, {"_id": 230, "path": "newLineOrderCost", "locale": "", "dataType": "decimal", "isCustom": false, "description": "new line order cost"}, {"_id": 240, "path": "##stockCountLineSerialNumbers", "locale": "", "dataType": "collection", "isCustom": false, "description": "stock count line serial numbers"}, {"_id": 250, "path": "!_sortValue#1", "locale": "", "dataType": "integer", "isCustom": false, "description": "sort value"}, {"_id": 260, "path": "*serialNumber", "locale": "", "dataType": "reference", "isCustom": false, "description": "serial number (#item|id)"}, {"_id": 270, "path": "isCounted", "locale": "", "dataType": "boolean", "isCustom": false, "description": "is counted (false/true)"}, {"_id": 280, "path": "//analyticalData", "locale": "", "dataType": "reference", "isCustom": false, "description": "analytical data"}, {"_id": 290, "path": "stockSite", "locale": "", "dataType": "reference", "isCustom": false, "description": "stock site (#id)"}, {"_id": 300, "path": "##stockDetails", "locale": "", "dataType": "collection", "isCustom": false, "description": "stock details"}, {"_id": 310, "path": "_sortValue#1", "locale": "", "dataType": "integer", "isCustom": false, "description": "sort value"}, {"_id": 320, "path": "movementType", "locale": "", "dataType": "enum(receipt,issue,change,adjustment,correction,valueChange,transfer)", "isCustom": false, "description": "movement type"}, {"_id": 330, "path": "supplier#1", "locale": "", "dataType": "reference", "isCustom": false, "description": "supplier (#businessEntity)"}, {"_id": 340, "path": "effectiveDate#1", "locale": "", "dataType": "date", "isCustom": false, "description": "effective date (yyyy-MM-dd)"}, {"_id": 350, "path": "startingSerialNumber", "locale": "", "dataType": "reference", "isCustom": false, "description": "starting serial number (#item|id)"}, {"_id": 360, "path": "*reasonCode", "locale": "", "dataType": "reference", "isCustom": false, "description": "reason code (#id)"}, {"_id": 370, "path": "existingLot", "locale": "", "dataType": "reference", "isCustom": false, "description": "existing lot (#item|id|sublot)"}, {"_id": 380, "path": "lotCreateData#1", "locale": "", "dataType": "json", "isCustom": false, "description": "lot create data"}, {"_id": 390, "path": "site", "locale": "", "dataType": "reference", "isCustom": false, "description": "site (#id)"}, {"_id": 400, "path": "item#1", "locale": "", "dataType": "reference", "isCustom": false, "description": "item (#id)"}, {"_id": 410, "path": "stockUnit", "locale": "", "dataType": "reference", "isCustom": false, "description": "stock unit (#id)"}, {"_id": 420, "path": "quantityInStockUnit#1", "locale": "", "dataType": "decimal", "isCustom": false, "description": "quantity in stock unit"}, {"_id": 430, "path": "location#1", "locale": "", "dataType": "reference", "isCustom": false, "description": "location (#id|locationZone)"}, {"_id": 440, "path": "status#3", "locale": "", "dataType": "reference", "isCustom": false, "description": "status (#id)"}, {"_id": 450, "path": "owner#1", "locale": "", "dataType": "string", "isCustom": false, "description": "owner"}, {"_id": 460, "path": "###stockDetailSerialNumbers", "locale": "", "dataType": "collection", "isCustom": false, "description": "stock detail serial numbers"}, {"_id": 470, "path": "_sortValue#2", "locale": "", "dataType": "integer", "isCustom": false, "description": "sort value"}, {"_id": 480, "path": "supplierSerialNumber", "locale": "", "dataType": "string", "isCustom": false, "description": "supplier serial number"}, {"_id": 490, "path": "serialNumber", "locale": "", "dataType": "reference", "isCustom": false, "description": "serial number (#item|id)"}, {"_id": 500, "path": "newSerialNumberId", "locale": "", "dataType": "string", "isCustom": false, "description": "new serial number id"}, {"_id": 510, "path": "///stockDetailLot", "locale": "", "dataType": "reference", "isCustom": false, "description": "stock detail lot"}, {"_id": 520, "path": "lot#1", "locale": "", "dataType": "reference", "isCustom": false, "description": "lot (#item|id|sublot)"}, {"_id": 530, "path": "lotNumber", "locale": "", "dataType": "string", "isCustom": false, "description": "lot number"}, {"_id": 540, "path": "sublot", "locale": "", "dataType": "string", "isCustom": false, "description": "sublot"}, {"_id": 550, "path": "supplierLot", "locale": "", "dataType": "string", "isCustom": false, "description": "supplier lot"}, {"_id": 560, "path": "expirationDate", "locale": "", "dataType": "date", "isCustom": false, "description": "expiration date (yyyy-MM-dd)"}]}