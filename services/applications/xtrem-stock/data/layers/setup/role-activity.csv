"role";"activity";"_vendor";"_sort_value";"has_all_permissions";"permissions";"is_active"
"400";"stockReorder";"sage";"5100";"Y";"[]";"Y"
"500";"stockReorder";"sage";"5100";"Y";"[]";"Y"
"600";"stockReorder";"sage";"7400";"Y";"[]";"Y"
"600";"stockValuationInputSet";"sage";"3000";"Y";"[]";"Y"
"600";"costRollUpInputSet";"sage";"3100";"Y";"[]";"Y"
"600";"stockAdjustment";"sage";"3400";"Y";"[]";"Y"
"600";"stockChange";"sage";"4900";"Y";"[]";"Y"
"600";"stockCount";"sage";"5000";"Y";"[]";"Y"
"600";"stockIssue";"sage";"5100";"Y";"[]";"Y"
"600";"stockReceipt";"sage";"5200";"Y";"[]";"Y"
"600";"stockValueChange";"sage";"5300";"Y";"[]";"Y"
"700";"stockIssue";"sage";"1000";"Y";"[]";"Y"
"700";"stockAdjustment";"sage";"3500";"Y";"[]";"Y"
"700";"stockCount";"sage";"5100";"Y";"[]";"Y"
"700";"stockReceipt";"sage";"5200";"Y";"[]";"Y"
"700";"stockValueChange";"sage";"5300";"Y";"[]";"Y"
"800";"stockValuationInputSet";"sage";"3200";"Y";"[]";"Y"
"800";"costRollUpInputSet";"sage";"3300";"Y";"[]";"Y"
"Support User";"stockValuationInputSet";"sage";"4700";"Y";"[]";"Y"
"Support User";"stockAdjustment";"sage";"4800";"Y";"[]";"Y"
"Support User";"stockChange";"sage";"4900";"Y";"[]";"Y"
"Support User";"stockCount";"sage";"5000";"Y";"[]";"Y"
"Support User";"stockIssue";"sage";"5100";"Y";"[]";"Y"
"Support User";"stockReceipt";"sage";"5200";"Y";"[]";"Y"
"Support User";"costRollUpInputSet";"sage";"5250";"Y";"[]";"Y"
"Support User";"stockValueChange";"sage";"5300";"Y";"[]";"Y"
"Support User";"stockReorder";"sage";"22400";"Y";"[]";"Y"
"Support User Read-only";"stockValuationInputSet";"sage";"4700";"N";"[""read""]";"Y"
"Support User Read-only";"stockAdjustment";"sage";"4800";"N";"[""read""]";"Y"
"Support User Read-only";"stockChange";"sage";"4900";"N";"[""read""]";"Y"
"Support User Read-only";"stockCount";"sage";"5000";"N";"[""read""]";"Y"
"Support User Read-only";"stockIssue";"sage";"5100";"N";"[""read""]";"Y"
"Support User Read-only";"stockReceipt";"sage";"5200";"N";"[""read""]";"Y"
"Operational User";"stockReorder";"sage";"5200";"Y";"[]";"Y"
"Operational User";"stockAdjustment";"sage";"190";"Y";"[]";"Y"
"Operational User";"stockCount";"sage";"210";"Y";"[]";"Y"
"Operational User";"stockIssue";"sage";"220";"Y";"[]";"Y"
"Operational User";"stockReceipt";"sage";"230";"Y";"[]";"Y"
"Operational User";"stockValueChange";"sage";"235";"Y";"[]";"Y"
"Admin";"stockValuationInputSet";"sage";"3200";"Y";"[]";"Y"
"Admin";"stockCount";"sage";"3250";"Y";"[]";"Y"
"Admin";"costRollUpInputSet";"sage";"3300";"Y";"[]";"Y"
"Admin";"stockAdjustment";"sage";"3400";"Y";"[]";"Y"
"Admin";"stockChange";"sage";"3500";"Y";"[]";"Y"
"Admin";"stockReceipt";"sage";"3700";"Y";"[]";"Y"
"Admin";"stockValueChange";"sage";"3800";"Y";"[]";"Y"
"Admin";"stockIssue";"sage";"3900";"Y";"[]";"Y"
"Admin";"stockReorder";"sage";"14000";"Y";"[]";"Y"
"Business User";"stockValuationInputSet";"sage";"6300";"Y";"[]";"Y"
"Business User";"stockCount";"sage";"6400";"Y";"[]";"Y"
"Business User";"costRollUpInputSet";"sage";"6500";"Y";"[]";"Y"
"Business User";"stockAdjustment";"sage";"6600";"Y";"[]";"Y"
"Business User";"stockChange";"sage";"6700";"Y";"[]";"Y"
"Business User";"stockReceipt";"sage";"6800";"Y";"[]";"Y"
"Business User";"stockValueChange";"sage";"6900";"Y";"[]";"Y"
"Business User";"stockIssue";"sage";"7000";"Y";"[]";"Y"
"Business User";"stockReorder";"sage";"14300";"Y";"[]";"Y"
"1100";"costRollUpInputSet";"sage";"490";"N";"[""manage""]";"Y"
"1100";"stockValuationInputSet";"sage";"500";"N";"[""read"",""manageCost""]";"Y"
"1100";"stockValueChange";"sage";"510";"N";"[""read"",""manage"",""post""]";"Y"
