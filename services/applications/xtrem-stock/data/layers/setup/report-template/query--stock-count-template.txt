query {
  xtremStock {
    stockCount {
      query(
        filter: "{_id:'{{stockCount}}'}"
        orderBy: "{}"
        first: 1
      ) {
        edges {
          node {
            number
            documentDate
            financialSite {
              stockSite {
                siret
              }
            }
            stockSite {
              taxIdNumber
              name
            }
            status
            stockTransactionStatus
            description
            lines {
              query(
                filter: "{}"
                orderBy: "{\"item\":{\"name\":1},\"location\":{\"name\":1},\"lot\":{\"id\":1},\"stockStatus\":{\"name\":1},\"countedQuantityInStockUnit\":1,\"quantityVariance\":1,\"quantityVariancePercentage\":1}"
              ) {
                edges {
                  node {
                    _id
                    item {
                      name
                      inStockQuantity
                    }
                    location {
                      name
                    }
                    lot {
                      id
                    }
                    stockStatus {
                      name
                    }
                    stockUnit {
                      symbol
                    }
                    status
                    quantityInStockUnit
                    countedQuantityInStockUnit
                    quantityVariance
                    quantityVariancePercentage
                  }
                }
              }
            }
            _id
          }
        }
      }
    }
  }
}
