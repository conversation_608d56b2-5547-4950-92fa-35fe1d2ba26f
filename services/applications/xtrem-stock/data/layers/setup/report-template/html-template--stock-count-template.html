<div>
  {{#each xtremStock.stockCount.query.edges }}
  <table class="report-container">
    <thead class="report-header">
      <tr>
        <th class="report-header-cell normal-black">
          <div class="header-info">
            <table>
              <tbody>
                <tr>
                  <td class="column-right">
                    <div>
                      <h1>{{ translatedContent "99e5f1557826ccf476fb2fa6f241498c" }}</h1>
                      <br />
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
          <div class="frame">
            <table style="border-style: none">
              <tbody>
                <tr>
                  <td class="column-left" colspan="2">
                    <span class="strong-theme">{{ translatedContent "60630f788fa16995828cc21fba5f0ef2" }}</span>
                    {{node.stockSite.name}}
                  </td>
                </tr>
                <tr>
                  <td class="column-left"><span class="strong-theme">{{ translatedContent "2a39f204d4eac05604c2059e5ac2f9f7" }}</span> {{node.number}}</td>
                  <td class="column-left">
                    <span class="strong-theme">{{ translatedContent "0d8d58008ef5dd7afce337373ef73993" }}</span> {{node.documentDate}}
                  </td>
                </tr>
                <tr>
                  <td class="column-left">
                    <span class="strong-theme">{{ translatedContent "24a23d787190f2c4812ff9ab11847a72" }}</span>
                    {{node.status}}
                  </td>
                  <td class="column-left">
                    <span class="strong-theme">{{ translatedContent "f57fc6f4bbe0cf4dfd7ad71954e85715" }}</span>
                    {{node.stockTransactionStatus}}
                  </td>
                </tr>
                <tr>
                  <td class="column-left">
                    <span class="strong-theme">{{ translatedContent "d04e2c1b67f3ef0d475409516b812e8b" }}</span>
                    {{node.financialSite.stockSite.siret}}
                  </td>
                  <td class="column-left">
                    <span class="strong-theme">{{ translatedContent "37a7a58f6fdc66ce34c2bf56389a2aac" }}</span>
                    {{node.stockSite.taxIdNumber}}
                  </td>
                </tr>
                <tr>
                  <td class="column-left" colspan="2">
                    <span class="strong-theme">{{ translatedContent "d0042a700e9bdf79689d63ee6846dc0e" }}</span>
                    {{node.description}}
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </th>
      </tr>
    </thead>
    <tfoot class="report-footer"></tfoot>
    <tbody class="report-content">
      <tr>
        <td class="report-content-cell">
          <div class="main">
            <table class="lines-table">
              <thead>
                <tr>
                  <th class="column-left">{{ translatedContent "7d74f3b92b19da5e606d737d339a9679" }}</th>
                  <th class="column-left">{{ translatedContent "d5189de027922f81005951e6efe0efd5" }}</th>
                  <th class="column-left">{{ translatedContent "eeec6c7a9d2b475c23650b202208b892" }}</th>
                  <th class="column-left">{{ translatedContent "2b68a2bf3641ac89e02bb903f3e4af4d" }}</th>
                  {{#eq @root.showQuantityInStock true}}
                  <th class="column-left">{{ translatedContent "f766176bbc3cf5fdfbe88ea1667a7b14" }}</th>
                  {{/eq}}
                  <th class="column-left">{{ translatedContent "11bc8bb90d33f4497172cbeceb907389" }}</th>
                  <th class="column-left">{{ translatedContent "f6454cda12c9b6538bf39382b2ec5d2d" }}</th>
                  <th class="column-left">{{ translatedContent "68ede44f9fbb02a6444b210084df5681" }}</th>
                </tr>
              </thead>
              <tbody>
                {{#each node.lines.query.edges}}
                <tr>
                  <td class="column-left">{{node.item.name}}</td>
                  <td class="column-left">{{node.location.name}}</td>
                  <td class="column-left">{{node.lot.id}}</td>
                  <td class="column-left">{{node.stockStatus.name}}</td>
                  {{#eq @root.showQuantityInStock true}}
                  <td class="column-right">{{{node.quantityInStockUnit}}} {{{node.stockUnit.symbol}}}</td>
                  {{/eq}}
                  <td class="column-right">
                    {{#eq node.status "counted"}}{{node.countedQuantityInStockUnit}}&nbsp;{{node.stockUnit.symbol}}{{else}}{{#eq node.countedQuantityInStockUnit
                    "0"}} {{node.stockUnit.symbol}}{{else}}{{node.countedQuantityInStockUnit}}&nbsp;{{node.stockUnit.symbol}}{{/eq}}{{/eq}}
                  </td>
                  <td class="column-right">{{node.quantityVariance}} {{node.stockUnit.symbol}}</td>
                  <td class="column-right">{{node.quantityVariancePercentage}} &#37;</td>
                </tr>
                {{/each}}
              </tbody>
            </table>
          </div>
        </td>
      </tr>
    </tbody>
  </table>
</div>
{{/each}}
