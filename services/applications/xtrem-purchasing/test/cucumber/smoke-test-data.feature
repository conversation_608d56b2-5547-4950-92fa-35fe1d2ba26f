@xtrem_purchasing
Feature: smoke-test-data

    #Case without navigation panel full width
    Scenario Outline: std \ <Page> \ desktop \ check each page loads without any problems
        Given the user opens the application on a desktop using the following link: "<Page>"
        Then the "<Title>" titled page is displayed
        Examples:
            | Page                                                            | Title                           |
            | @sage/xtrem-purchasing/PurchaseRequisition/eyJfaWQiOiI0MyJ9     | Purchase requisition REQ9       |
            | @sage/xtrem-purchasing/PurchaseInvoice/eyJfaWQiOiJQSTEifQ==     | Purchase invoice PI1            |
            # Opening pages having extensions in the current package
            | @sage/xtrem-master-data/Supplier/eyJfaWQiOiI0In0=               | Supplier Siège social S01 PARIS |
            | @sage/xtrem-master-data/Site/eyJfaWQiOiIzNiJ9                   | Site Canadian site              |
