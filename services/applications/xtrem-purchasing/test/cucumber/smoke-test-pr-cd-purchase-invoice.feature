@xtrem_purchasing
Feature:  smoke-test-pr-cd-purchase-invoice

    #feature to be activated once step defintion for OK button on sidebar is delivered
    Scenario: Purchase Invoice creation
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-purchasing/PurchaseInvoice"
        Then the "Purchase invoices" titled page is displayed
        #Click Create business action
        When the user clicks the "Create" labelled business action button on the navigation panel
        #Click Add Crud Button
        And the user selects the "Number" labelled text field on the main page
        And the user writes "PI-test" in the text field
        #Fill in site reference field
        And the user selects the "Financial site" labelled reference field on the main page
        And the user writes "Chem. Atlanta" in the reference field
        And the user selects "Chem. Atlanta" in the reference field
        #Fill in Supplier reference field
        And the user selects the "Bill-by supplier" labelled reference field on the main page
        And the user writes "LECLERC supermarket" in the reference field
        And the user selects "LECLERC supermarket" in the reference field
        #Add a line
        And the user selects the "lines" bound table field on the main page
        And the user adds a new table row to the table field using the sidebar
        #Select item on sidebar
        And the user selects the "Item" labelled reference field on the sidebar
        And the user writes "Sales Item 81" in the reference field
        And the user selects "Sales Item 81" in the reference field
        #Fill in Qty on sidebar
        And the user selects the "Quantity in purchase unit" labelled numeric field on the sidebar
        And the user writes "10" in the numeric field
        #select price tab
        And selects the "Price" labelled navigation anchor on the sidebar
        #Fill in Gross Price on sidebar
        And the user selects the "Gross price" labelled numeric field on the sidebar
        And the user writes "10" in the numeric field
        #Fill in Discount on sidebar
        And the user selects the "Discount" labelled numeric field on the sidebar
        And the user writes "10" in the numeric field
        #Fill in Charge on sidebar
        And the user selects the "Charge" labelled numeric field on the sidebar
        And the user writes "10" in the numeric field
        # Validating PO Line
        And the user clicks the "Apply" button of the dialog on the sidebar
        #Click Save Crud Button on main page
        And the user clicks the "Save" labelled business action button on the main page
        #Verify Creation
        Then a toast containing text "Record created" is displayed

    Scenario: Purchase invoice Deletion
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-purchasing/PurchaseInvoice"
        Then the "Purchase invoices" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        When the user filters the "number" bound column in the table field with value "PI-test"
        And the user selects the row 1 of the table field
        And the value of the "number" bound nested text field of the selected row in the table field is "PI-test"
        And the user clicks the "Number" labelled nested field of the selected row in the table field
        Then the "Purchase invoice PI-test" titled page is displayed
        When the user clicks the "Delete" labelled more actions button in the header
        And the user clicks the "Delete" button of the Confirm dialog
        #Verify Deletion
        Then a toast containing text "Record deleted" is displayed
