@xtrem_purchasing
Feature: smoke-test-static

    #Case with navigation panel full width only (no page)
    Scenario Outline: sts \ <Page> \ desktop \ check each page loads without any problems
        Given the user opens the application on a desktop using the following link: "<Page>"
        Then the "<NavigationPanelTitle>" titled page is displayed

        Examples:

            | Page                                                        | NavigationPanelTitle                   |
            | @sage/xtrem-purchasing/PurchaseOrderLineInquiry             | Purchase order line                    |
            | @sage/xtrem-purchasing/PurchaseReceiptLineInquiry           | Purchase receipt line                  |
            | @sage/xtrem-purchasing/UnbilledAccountPayableInquiry        | Unbilled accounts payable              |
            | @sage/xtrem-purchasing/PurchaseOrder                        | Purchase orders                        |
            | @sage/xtrem-purchasing/PurchaseReceipt                      | Purchase receipts                      |
            | @sage/xtrem-purchasing/PurchaseReturn                       | Purchase returns                       |
            | @sage/xtrem-purchasing/PurchaseCreditMemo                   | Purchase credit memos                  |
            | @sage/xtrem-purchasing/PurchaseInvoiceCreditMemoLineInquiry | Purchase invoice and credit memo lines |


    #Case with navigation panel full width a business action button
    Scenario Outline: sts \ <Page> \ desktop \ check each page loads without any problems
        Given the user opens the application on a desktop using the following link: "<Page>"
        Then the "<NavigationPanelTitle>" titled page is displayed
        #Click Create business action
        When the user clicks the "Create" labelled business action button on the navigation panel
        Then the "<Title>" titled page is displayed

        Examples:
            | Page                                       | NavigationPanelTitle  | Title                |
            | @sage/xtrem-purchasing/PurchaseRequisition | Purchase requisitions | Purchase requisition |
            | @sage/xtrem-purchasing/PurchaseInvoice     | Purchase invoices     | Purchase invoice     |

    #Case with navigation panel full width with a multi action button
    Scenario Outline: sts \ <Page> \ desktop \ check each page loads without any problems
        Given the user opens the application on a desktop using the following link: "<Page>"
        Then the "<NavigationPanelTitle>" titled page is displayed
        #Click Create business action
        When the user clicks the "Create" labelled multi action button on the navigation panel
        Then the "<Title>" titled page is displayed

        Examples:
            | Page                             | NavigationPanelTitle | Title    |
            | @sage/xtrem-master-data/Supplier | Suppliers            | Supplier |
            | @sage/xtrem-master-data/Site     | Sites                | Site     |

    # Positive test to ensure that the error dialog definitely exist and opens
    Scenario: sts \ xtrem-purchasing \ desktop \ check an error dialog appears when the page does not load
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/Wazup"
        Then an error dialog appears on the main page
