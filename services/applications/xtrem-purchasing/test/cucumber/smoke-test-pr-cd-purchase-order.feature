@xtrem_purchasing
Feature:  smoke-test-pr-cd-purchase-order

    #feature to be activated once step defintion for OK button on sidebar is delivered

    Scenario: Purchase order creation
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-purchasing/PurchaseOrder"
        Then the "Purchase orders" titled page is displayed
        #Click Create business action
        When the user clicks the "Create" labelled business action button on the navigation panel
        #Fill in a text field
        And the user selects the "Number" labelled text field on the main page
        And the user writes "PO-test" in the text field
        #Fill in site reference field
        And the user selects the "purchasingSite " labelled reference field on the main page
        And the user writes "Chem. Atlanta" in the reference field
        And the user selects "Chem. Atlanta" in the reference field
        #Fill in Supplier reference field
        And the user selects the "supplier" labelled reference field on the main page
        And the user writes "LECLERC supermarket" in the reference field
        And the user selects "LECLERC supermarket" in the reference field
        #We need to unhide the addLine button
        #And scrolls to the "linesBlock" bound block
        #Add a line
        Then the user selects the "lines" bound table field on the main page
        And the user adds a new table row to the table field using the sidebar

        # And the user clicks the "addLine" bound action of the "lines" bound table field on the main page
        #Select item on sidebar
        And the user selects the "Item" labelled reference field on the sidebar
        And the user writes "Glycerin" in the reference field
        And the user selects "Glycerin" in the reference field
        #Fill in Qty on sidebar
        And the user selects the "quantity" bound numeric field on the sidebar
        And the user writes "30" in the numeric field
        #select price tab
        And selects the "Price" labelled navigation anchor on the sidebar
        #Fill in Price on sidebar
        And the user selects the "Gross price" labelled numeric field on the sidebar
        And the user writes "12" in the numeric field
        And the user presses Enter
        # Validating PO Line
        # And the user selects the OK button on the sidebar
        And the user clicks the "Apply" button of the dialog on the sidebar
        And the user waits 4 seconds
        #Click Save Crud Button on main page
        And the user clicks the "Save" labelled business action button on the main page
        #Verify Creation
        Then the value of the toast is "Record created"

    Scenario: Purchase order Deletion
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-purchasing/PurchaseOrder"
        Then the "Purchase orders" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        When the user filters the "number" bound column in the table field with value "PO-test"
        And the user selects the row 1 of the table field
        Then the value of the "number" bound nested text field of the selected row in the table field is "PO-test"
        And the user clicks the "Number" labelled nested field of the selected row in the table field
        Then the "Purchase order PO-test" titled page is displayed
        When the user clicks the "Delete" labelled more actions button in the header
        And the user clicks the "Delete" button of the Confirm dialog
        #Verify Deletion
        Then a toast containing text "Record deleted" is displayed
