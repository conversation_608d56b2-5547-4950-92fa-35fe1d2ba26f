@xtrem_purchasing
Feature:  smoke-test-pr-cd-purchase-requisition

    Scenario: Purchase requisition creation
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-purchasing/PurchaseRequisition"

        #Click Create business action
        When the user clicks the "Create" labelled business action button on the navigation panel

        Then the "Purchase requisition" titled page is displayed
        #Fill in a text field
        And the user selects the "Number" labelled text field on the main page
        And the user writes "PR-test" in the text field
        #Fill in site reference field
        Given the user selects the "Purchasing site *" labelled reference field on the main page
        And the user writes "Chem. Atlanta" in the reference field
        And the user selects "Chem. Atlanta" in the reference field
        #Add a line
        And the user selects the "lines" bound table field on the main page
        When the user adds a new table row to the table field using the sidebar
        #Select item on sidebar
        And the user selects the "Item" labelled reference field on the sidebar
        And the user writes "A box of muesli" in the reference field
        And the user selects "A box of muesli" in the reference field
        #Select currency on sidebar
        And the user selects the "Transaction currency" labelled reference field on the sidebar
        And the user writes "Euro" in the reference field
        And the user selects "Euro" in the reference field
        #Fill in Qty on sidebar
        And the user selects the "Quantity in purchase unit" labelled numeric field on the sidebar
        And the user writes "30" in the numeric field
        #Validating PREQ Line
        #select price tab
        And selects the "Price" labelled navigation anchor on the sidebar
        #Fill in Price on sidebar
        And the user selects the "Gross price" labelled numeric field on the sidebar
        And the user writes "12" in the numeric field
        And the user presses Enter
        Then the user clicks the "Apply" button of the dialog on the sidebar
        And the user waits 4 seconds
        #Click Save Crud Button on main page
        And the user clicks the "Save" labelled business action button on the main page
        # TODO: This fails on CI, but passes locally, it should be investigated why
        #Verify Creation
        Then a toast containing text "Record created" is displayed



    Scenario: Purchase requisition Deletion
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-purchasing/PurchaseRequisition"
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        When the user filters the "number" bound column in the table field with value "PR-test"
        And the user selects the row 1 of the table field
        And the value of the "number" bound nested text field of the selected row in the table field is "PR-test"
        And the user clicks the "Number" labelled nested field of the selected row in the table field
        When the user clicks the "Delete" labelled more actions button in the header
        And the user clicks the "Delete" button of the Confirm dialog
        #Verify Deletion
        And a toast containing text "Record deleted" is displayed
