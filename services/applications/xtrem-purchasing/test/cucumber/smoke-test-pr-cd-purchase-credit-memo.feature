@xtrem_purchasing
Feature:  smoke-test-pr-cd-purchase-credit-memo

    Scenario: Purchase credit memo creation
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-purchasing/PurchaseCreditMemo"
        Then the "Purchase credit memos" titled page is displayed
        #Click Create business action
        When the user clicks the "Create" labelled business action button on the navigation panel
        #Fill in a text field
        And the user selects the "Number" labelled text field on the main page
        And the user writes "PCM-test" in the text field
        #Fill in site reference field
        And the user selects the "Financial site" labelled reference field on the main page
        And the user writes "Chem. Atlanta" in the reference field
        And the user selects "Chem. Atlanta" in the reference field
        #Fill in Bill by supplie reference field
        And the user selects the "Bill-by supplier" labelled reference field on the main page
        And the user writes "LECLERC supermarket" in the reference field
        And the user selects "LECLERC supermarket" in the reference field
        #Fill in Reason reference field
        And the user selects the "reason " labelled reference field on the main page
        And the user writes "Decrease quantity" in the reference field
        And the user selects "Decrease quantity" in the reference field
        #Fill in Total tax excluding amount field
        And the user selects the "Total credit memo amount excl. tax" labelled numeric field on the main page
        And the user writes "100" in the numeric field
        #Add a line
        And the user selects the "lines" bound table field on the main page
        And the user adds a new table row to the table field using the sidebar
        #Select item on sidebar
        And the user selects the "Item" labelled reference field on the sidebar
        And the user writes "Non stock managed item" in the reference field
        And the user selects "Non stock managed item" in the reference field
        #Fill in Quantity on sidebar
        And the user selects the "Quantity in purchase unit" labelled numeric field on the sidebar
        And the user writes "10" in the numeric field
        #Select Quantities tab on sidebar
        And selects the "Price" labelled navigation anchor on the sidebar
        #Fill in Gross Price on sidebar
        And the user selects the "Gross price" labelled numeric field on the sidebar
        And the user writes "10" in the numeric field
        # And the user clicks the "OK" button of the Custom dialog on the sidebar
        And the user presses Enter
        And the user clicks the "Apply" button of the dialog on the sidebar
        #Click Save Crud Button on main page
        And the user clicks the "Save" labelled business action button on the main page
        #Verify Creation
        Then a toast containing text "Record created" is displayed

    Scenario: Purchase credit memo Deletion
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-purchasing/PurchaseCreditMemo"
        Then the "Purchase credit memos" titled page is displayed
        #Search record on navigation panel
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        When the user filters the "number" bound column in the table field with value "PCM-test"
        And the user selects the row 1 of the table field
        And the value of the "number" bound nested text field of the selected row in the table field is "PCM-test"
        And the user clicks the "Number" labelled nested field of the selected row in the table field
        Then the "Purchase credit memo PCM-test" titled page is displayed
        When the user searches for "PCM-test" in the navigation panel
        And the user clicks the "Delete" labelled more actions button in the header
        And the user clicks the "Delete" button of the Confirm dialog
        #Verify Deletion
        Then a toast containing text "Record deleted" is displayed
