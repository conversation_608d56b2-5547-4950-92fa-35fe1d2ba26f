@xtrem_purchasing
Feature:  smoke-test-pr-cd-purchase-return

    Scenario: Purchase return creation
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-purchasing/PurchaseReturn"
        Then the "Purchase returns" titled page is displayed
        #Click Add <PERSON><PERSON>
        When the user clicks the "Create" labelled business action button on the navigation panel
        #Fill in a text field
        And the user selects the "Number" labelled text field on the main page
        And the user writes "PR-TEST" in the text field
        #Fill in site reference field
        And the user selects the "Return site" labelled reference field on the main page
        And the user writes "Chem. Atlanta" in the reference field
        And the user selects "Chem. Atlanta" in the reference field
        #Fill in Supplier reference field
        And the user selects the "Supplier" labelled reference field on the main page
        And the user writes "LECLERC supermarket" in the reference field
        And the user selects "LECLERC supermarket" in the reference field


        #Select the look up table
        And selects the "Lines" labelled navigation anchor on the main page
        And the user clicks the "Add lines from receipts" labelled business action button on the main page
        And the user selects the "$applicationCodeLookup" bound table field on a modal

        #Select the second record in the list returned by the lookup
        And the user selects the row 2 of the table field
        When the user ticks the main checkbox of the selected row in the table field
        And the user waits 1 second
        And the user clicks the "Select" button of the Lookup dialog
        And the user waits 1 second


        # Update the reason for the return
        And the user selects the "lines" bound table field on the main page
        And the user selects the row 1 of the table field
        When the user clicks the "Open line panel" inline action button of the selected row in the table field
        And the user waits 5 second
        And the user selects the "Return reason" labelled reference field on the sidebar
        And the user writes "Reason 1" in the reference field
        And the user selects "Reason 1" in the reference field
        And the user presses Tab
        And the user clicks the "Apply" button of the dialog on the sidebar
        And the user waits 4 seconds
        #Click Save Crud Button on main page
        And the user clicks the "Save" labelled business action button on the main page
        #Verify Creation
        Then a toast containing text "Record created" is displayed




    Scenario: Purchase return Deletion
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-purchasing/PurchaseReturn"
        Then the "Purchase returns" titled page is displayed
        #Search record on navigation panel
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        When the user filters the "number" bound column in the table field with value "PR-TEST"
        And the user selects the row 1 of the table field
        Then the value of the "number" bound nested text field of the selected row in the table field is "PR-TEST"
        And the user clicks the "Number" labelled nested field of the selected row in the table field
        Then the "Purchase return PR-TEST" titled page is displayed
        #Click Deletion
        And the user clicks the "Delete" labelled more actions button in the header
        And the user clicks the "Delete" button of the Confirm dialog
        #Verify Deletion
        Then a toast containing text "Record deleted" is displayed
