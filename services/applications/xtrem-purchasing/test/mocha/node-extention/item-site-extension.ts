import { Test } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import { assert } from 'chai';

describe('Item site cost delete', () => {
    it('ItemSite delete with purchasing documents - should fail.', () =>
        Test.withContext(async context => {
            const item = await context.read(xtremMasterData.nodes.Item, { _id: '#5467' }, { forUpdate: true });
            await item.$.set({ isStockManaged: false });
            const itemSite = await context.read(
                xtremMasterData.nodes.ItemSite,
                { _id: '#5467|US001' },
                { forUpdate: true },
            );
            await assert.isRejected(itemSite.$.delete(), 'The record was not deleted.');
            assert.deepEqual(context.diagnoses, [
                {
                    severity: 3,
                    path: [],
                    message: 'A work in progress record with this item site exists. You cannot delete it.',
                },
                {
                    severity: 3,
                    path: [],
                    message: 'Delete not allowed. Purchasing documents exist for this item-site.',
                },
            ]);
        }));

    it('ItemSite delete without purchasing documents - should succeed.', () =>
        Test.withContext(async context => {
            const item = await context.read(xtremMasterData.nodes.Item, { _id: '#17893' }, { forUpdate: true });
            await item.$.set({ isStockManaged: false });

            const ItemSite = await context.read(
                xtremMasterData.nodes.ItemSite,
                { _id: '#17890|US004' },
                { forUpdate: true },
            );
            await ItemSite.$.delete();
        }));
});
