import { Test } from '@sage/xtrem-core';
import * as xtremSystem from '@sage/xtrem-system';
import { assert } from 'chai';

describe('Update site - is purchase order approval managed', () => {
    before(() => {});
    it('Update a site where with  purchase orders are in pending status, update fail', () =>
        Test.withContext(async context => {
            const site = await context.read(xtremSystem.nodes.Site, { id: 'US001' }, { forUpdate: true });

            await site.$.set({ isPurchaseOrderApprovalManaged: false });
            await assert.isRejected(site.$.save());

            assert.deepEqual(site.$.context.diagnoses, [
                {
                    message: 'You need to approve or reject pending documents before disabling the approval process.',
                    path: ['isPurchaseOrderApprovalManaged'],
                    severity: 3,
                },
            ]);
        }));

    it('Update a site where with no purchase orders and purchase returns are in pending status, update success', () =>
        Test.withContext(async context => {
            const site = await context.read(xtremSystem.nodes.Site, { id: 'US017' }, { forUpdate: true });

            await site.$.set({ isPurchaseOrderApprovalManaged: false, isPurchaseReturnApprovalManaged: false });
            await site.$.save();
            assert.deepEqual(site.$.context.diagnoses, []);
        }));
});

describe('Update site - is purchase return approval managed', () => {
    before(() => {});
    it('Update a site where with  purchase returns are in pending status, update fail', () =>
        Test.withContext(async context => {
            const site = await context.read(xtremSystem.nodes.Site, { id: 'US005' }, { forUpdate: true });

            await site.$.set({ isPurchaseReturnApprovalManaged: false });
            await assert.isRejected(site.$.save());

            assert.deepEqual(site.$.context.diagnoses, [
                {
                    message: 'You need to approve or reject pending documents before disabling the approval process.',
                    path: ['isPurchaseReturnApprovalManaged'],
                    severity: 3,
                },
            ]);
        }));
});
