import { Test } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import { assert } from 'chai';

describe('Item site cost delete', () => {
    before(() => {});

    it('ItemSiteCost deletion with purchasing documents - should fail.', () =>
        Test.withContext(
            async context => {
                const itemId = '#5467';
                const item = await context.read(xtremMasterData.nodes.Item, { _id: itemId }, { forUpdate: true });
                await item.$.set({ isStockManaged: false });

                const itemSiteCost = await context.read(
                    xtremMasterData.nodes.ItemSiteCost,
                    { _id: 22 },
                    { forUpdate: true },
                );
                await assert.isRejected(itemSiteCost.$.delete(), 'The record was not deleted.');
                assert.deepEqual(context.diagnoses, [
                    {
                        severity: 3,
                        path: [],
                        message: 'Delete not allowed. Purchase documents exist for this item-site.',
                    },
                ]);
            },
            { today: '2017-12-31' },
        ));

    it('ItemSiteCost deletion without purchasing documents - should succeed.', () =>
        Test.withContext(
            async context => {
                const itemId = '#3426';
                const item = await context.read(xtremMasterData.nodes.Item, { _id: itemId }, { forUpdate: true });
                await item.$.set({ isStockManaged: false });

                const itemSiteCost = await context.read(
                    xtremMasterData.nodes.ItemSiteCost,
                    { _id: 2 },
                    { forUpdate: true },
                );
                await itemSiteCost.$.delete();
            },
            { today: '2017-12-31' },
        ));
});
