import type { NodeQueryFilter } from '@sage/xtrem-core';
import { date, Test } from '@sage/xtrem-core';
import type * as xtremFinanceData from '@sage/xtrem-finance-data';
import { assert } from 'chai';
import * as xtremPurchasing from '../../../lib/index';

describe('Test getFilterPurchaseReceiptLines()', () => {
    it('Get filter query ', () =>
        Test.withContext(() => {
            const stockSiteIds = [1, 2, 3];
            const searchCriteria: xtremFinanceData.interfaces.FinanceUnbilledAccountPayableSearch = {
                company: 'company',
                stockSites: stockSiteIds,
                fromSupplier: 'fromSupplierId',
                toSupplier: 'toSupplierId',
                asOfDate: date.today(),
            };
            const filter =
                xtremPurchasing.functions.PurchaseReceiptLineLib.getFilterPurchaseReceiptLines(searchCriteria);
            const referenceFilter: NodeQueryFilter<xtremPurchasing.nodes.PurchaseReceiptLine> = {
                document: {
                    billBySupplier: {
                        businessEntity: { id: { _gte: searchCriteria.fromSupplier, _lte: searchCriteria.toSupplier } },
                    },
                    date: { _lte: searchCriteria.asOfDate },
                    stockSite: { legalCompany: { id: { _eq: 'company' } } },
                },
                netPrice: { _gt: 0 },
                site: { _id: { _in: stockSiteIds } },
                status: { _in: ['pending', 'closed', 'inProgress', 'posted'] },
            };

            assert.deepEqual(filter, referenceFilter);
        }));
});
