import { Test } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremSystem from '@sage/xtrem-system';
import { assert } from 'chai';
import * as xtremPurchasing from '../../../index';

describe('Purchase common functions - doPurchaseDocumentsExist', () => {
    it('Check for purchasing documents preventing item-site and item-site-cost from deletion', () =>
        Test.withContext(async context => {
            // Item-site with purchase orders
            let item = await context.read(xtremMasterData.nodes.Item, { _id: '#Aqua' });
            let site = await context.read(xtremSystem.nodes.Site, { id: 'US005' });
            let result = await xtremPurchasing.functions.doPurchaseDocumentsExist(context, item, site);
            assert.deepEqual(result, true);

            // Item-site with purchase invoices without orders
            item = await context.read(xtremMasterData.nodes.Item, { _id: '#SalesItem81' });
            site = await context.read(xtremSystem.nodes.Site, { id: 'US001' });
            result = await xtremPurchasing.functions.doPurchaseDocumentsExist(context, item, site);
            assert.deepEqual(result, true);

            // Item-site with purchase requisitions without orders and invoices
            item = await context.read(xtremMasterData.nodes.Item, { _id: '#Chemical D' });
            site = await context.read(xtremSystem.nodes.Site, { id: 'US001' });
            result = await xtremPurchasing.functions.doPurchaseDocumentsExist(context, item, site);
            assert.deepEqual(result, true);

            // Item-site with purchase receipts without any of the previous
            item = await context.read(xtremMasterData.nodes.Item, { _id: '#Chemical C' });
            site = await context.read(xtremSystem.nodes.Site, { id: 'US002' });
            result = await xtremPurchasing.functions.doPurchaseDocumentsExist(context, item, site);
            assert.deepEqual(result, true);

            // Item-site without any of the previous purchase documents.
            item = await context.read(xtremMasterData.nodes.Item, { _id: '#SalesItem81' });
            site = await context.read(xtremSystem.nodes.Site, { id: 'DEP1-S01' });
            result = await xtremPurchasing.functions.doPurchaseDocumentsExist(context, item, site);
            assert.deepEqual(result, false);
        }));
});
