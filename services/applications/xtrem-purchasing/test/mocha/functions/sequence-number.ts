import { Test } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import { assert } from 'chai';
import * as xtremPurchasing from '../../../lib/index';

describe('Purchase order sequence number setup', () => {
    it('PurchaseOrder / PurchaseOrderSuggestion sequence Number ', () =>
        Test.withContext(async context => {
            const sequenceNumberId = 'PurchaseOrder';
            const sugSequenceNumberId = 'PurchaseOrderSuggestion';
            const purchaseOrder = await context.read(xtremPurchasing.nodes.PurchaseOrder, { number: 'PO2' });
            const { name } = purchaseOrder.$.factory;

            const sequenceNumberIds = await context
                .query(xtremMasterData.nodes.SequenceNumberAssignment, {
                    filter: { sequenceNumberAssignmentDocumentType: { nodeFactory: { name } } },
                })
                .map(async assignement => (await assignement.sequenceNumber).id)
                .toArray();

            assert.equal(sequenceNumberIds.length, 2);
            assert.deepEqual(sequenceNumberIds, [sequenceNumberId, sugSequenceNumberId]);

            const generator = await xtremMasterData.classes.DocumentNumberGenerator.create(context, {
                sequenceNumber: sequenceNumberId,
                nodeInstance: purchaseOrder,
                skipControls: true,
            });

            const seqNumber = await generator.sequenceNumberInstance;
            const constantComponent = await seqNumber.components.find(async comp => (await comp.type) === 'constant');
            assert.equal(await constantComponent?.constant, 'PO');
            assert.equal(await seqNumber.id, sequenceNumberId);

            const purchaseOrderNumber = await generator.allocate();
            // Constant for purchase order must start with PO
            assert.equal(purchaseOrderNumber, 'PO200001');

            const sugGenerator = await xtremMasterData.classes.DocumentNumberGenerator.create(context, {
                sequenceNumber: sugSequenceNumberId,
                nodeInstance: purchaseOrder,
                skipControls: true,
            });

            const sugSeqNumber = await sugGenerator.sequenceNumberInstance;
            const sugConstantComponent = await sugSeqNumber.components.find(
                async comp => (await comp.type) === 'constant',
            );
            assert.equal(await sugConstantComponent?.constant, 'POS');

            assert.equal(await sugSeqNumber.id, sugSequenceNumberId);
            const sugPurchaseOrderNumber = await sugGenerator.allocate();
            // Constant for suggestion must start with POS
            assert.equal(sugPurchaseOrderNumber, 'POS20000001');
        }));
});
