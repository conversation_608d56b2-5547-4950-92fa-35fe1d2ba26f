import { Test } from '@sage/xtrem-core';
import { assert } from 'chai';
import * as xtremPurchasing from '../../../lib/index';

describe('Test display status computing', () => {
    it('Stock transaction in error should supersede invoice status partially invoiced', () =>
        Test.withContext(() => {
            const displayStatus = xtremPurchasing.functions.PurchaseReceiptLib.calculatePurchaseReceiptDisplayStatus(
                'draft',
                'notReturned',
                'partiallyInvoiced',
                'error',
                'notDone',
            );
            assert.deepEqual(displayStatus, 'error');
        }));

    it('Stock transaction in error should supersede invoice status invoiced', () =>
        Test.withContext(() => {
            const displayStatus = xtremPurchasing.functions.PurchaseReceiptLib.calculatePurchaseReceiptDisplayStatus(
                'draft',
                'notReturned',
                'invoiced',
                'error',
                'notDone',
            );
            assert.deepEqual(displayStatus, 'error');
        }));

    it('Stock transaction in error should supersede return status partially returned', () =>
        Test.withContext(() => {
            const displayStatus = xtremPurchasing.functions.PurchaseReceiptLib.calculatePurchaseReceiptDisplayStatus(
                'draft',
                'partiallyReturned',
                'notInvoiced',
                'error',
                'notDone',
            );
            assert.deepEqual(displayStatus, 'error');
        }));

    it('Stock transaction in error should supersede return status returned', () =>
        Test.withContext(() => {
            const displayStatus = xtremPurchasing.functions.PurchaseReceiptLib.calculatePurchaseReceiptDisplayStatus(
                'draft',
                'returned',
                'notInvoiced',
                'error',
                'notDone',
            );
            assert.deepEqual(displayStatus, 'error');
        }));

    it('Closing status should supersede stock transaction error status', () =>
        Test.withContext(() => {
            const displayStatus = xtremPurchasing.functions.PurchaseReceiptLib.calculatePurchaseReceiptDisplayStatus(
                'closed',
                'notReturned',
                'notInvoiced',
                'error',
                'notDone',
            );
            assert.deepEqual(displayStatus, 'error');
        }));
    it('Closing status should only appear if the receipt is manually closed', () =>
        Test.withContext(() => {
            const displayStatus = xtremPurchasing.functions.PurchaseReceiptLib.calculatePurchaseReceiptDisplayStatus(
                'closed',
                'notReturned',
                'notInvoiced',
                'draft',
                'done',
            );
            assert.deepEqual(displayStatus, 'closed');
        }));
    it('Return status returned with invoice status partiallyInvoiced should display status returned', () =>
        Test.withContext(() => {
            const displayStatus = xtremPurchasing.functions.PurchaseReceiptLib.calculatePurchaseReceiptDisplayStatus(
                'closed',
                'returned',
                'partiallyInvoiced',
                'draft',
                'done',
            );
            assert.deepEqual(displayStatus, 'returned');
        }));
    it('Return status partiallyReturned with invoice status notInvoiced should display status returned', () =>
        Test.withContext(() => {
            const displayStatus = xtremPurchasing.functions.PurchaseReceiptLib.calculatePurchaseReceiptDisplayStatus(
                'closed',
                'partiallyReturned',
                'notInvoiced',
                'draft',
                'done',
            );
            assert.deepEqual(displayStatus, 'partiallyReturned');
        }));
    it('Return status partiallyReturned with invoice status invoiced should display status invoiced', () =>
        Test.withContext(() => {
            const displayStatus = xtremPurchasing.functions.PurchaseReceiptLib.calculatePurchaseReceiptDisplayStatus(
                'closed',
                'partiallyReturned',
                'invoiced',
                'draft',
                'done',
            );
            assert.deepEqual(displayStatus, 'invoiced');
        }));
    it('Return status partiallyReturned with invoice status partiallyInvoiced should display status partiallyInvoiced', () =>
        Test.withContext(() => {
            const displayStatus = xtremPurchasing.functions.PurchaseReceiptLib.calculatePurchaseReceiptDisplayStatus(
                'closed',
                'partiallyReturned',
                'partiallyInvoiced',
                'draft',
                'done',
            );
            assert.deepEqual(displayStatus, 'closed');
        }));
    it('Return status notReturned with invoice status partiallyInvoiced should display status partiallyInvoiced', () =>
        Test.withContext(() => {
            const displayStatus = xtremPurchasing.functions.PurchaseReceiptLib.calculatePurchaseReceiptDisplayStatus(
                'closed',
                'notReturned',
                'invoiced',
                'draft',
                'done',
            );
            assert.deepEqual(displayStatus, 'invoiced');
        }));
});
