import { Test } from '@sage/xtrem-core';
import { assert } from 'chai';
import * as xtremMasterData from '@sage/xtrem-master-data';

describe('master data work in progress for item-site expected quantity and required quantity', () => {
    it('Expected quantity and required quantity', () =>
        Test.withContext(async context => {
            let quantity = await xtremMasterData.nodes.WorkInProgress.getWorkInProgressQuantityPerItemSite(
                context,
                44,
                3,
            );
            assert.equal(quantity.supply, 200);

            quantity = await xtremMasterData.nodes.WorkInProgress.getWorkInProgressQuantityPerItemSite(
                context,
                44,
                3,
                null,
                'firm',
            );
            assert.equal(quantity.supply, 0);
        }));
});
