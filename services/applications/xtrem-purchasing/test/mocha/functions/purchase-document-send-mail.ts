import { Test } from '@sage/xtrem-core';
import * as xtremSystem from '@sage/xtrem-system';
import { assert } from 'chai';
import * as sinon from 'sinon';
import * as xtremPurchasing from '../../../lib/index';
import { stubSendMail } from '../../fixtures/lib/functions/send-mail';
import { getUser } from '../fixtures/mailer';

const sandbox = sinon.createSandbox();
const to = { name: '<PERSON>, rachel', address: '<EMAIL>' };

/**
 * purchase-document-send-mail function :
 * SendMail used by :
 * - PurchaseCreditMemo  => sendNotificationToBuyerMail
 * - PurchaseInvoice     => sendNotificationToBuyerMail
 * - PurchaseOrder       => sendApprovalRequestMail
 *                       => sendRequestChangesMail
 * - PurchaseRequisition => sendApprovalRequestMail
 *                       => sendRequestChangesMail
 */
describe('SendMail From purchase documents', () => {
    beforeEach(() => sandbox.restore());

    it('Purchase Order  - request', () =>
        Test.withContext(
            async context => {
                const sendSpy = stubSendMail(sandbox, {
                    recipients: { to: [to] },
                    htmlMatcher: [],
                    subject: 'Changes requested for this purchase order: [Purchase order PO1]',
                });

                const purchaseOrder = await context.read(
                    xtremPurchasing.nodes.PurchaseOrder,
                    { number: 'PO1' },
                    { forUpdate: true },
                );

                const isSend = await xtremPurchasing.nodes.PurchaseOrder.sendRequestChangesMail(
                    context,
                    purchaseOrder,
                    await getUser(context, '<EMAIL>'),
                );
                const urlDocument = 'http://localhost:8240/@sage/xtrem-purchasing/PurchaseOrderApproval';
                const page = 'http://localhost:8240/@sage/xtrem-purchasing/PurchaseOrder';
                const jsonUrl = { _id: purchaseOrder._id.toString() };
                const jsonReject = { ...jsonUrl, action: 'rejected' };
                const jsonApprove = { ...jsonUrl, action: 'approved' };
                const jsonPurchaseDocument = { _id: purchaseOrder._id.toString() };

                assert.deepEqual(sendSpy.args, [
                    [
                        { to: [to] },
                        'Changes requested for this purchase order: [Purchase order PO1]',
                        'purchase_order_request_changes_mail',
                        {
                            number: 'PO1',
                            supplier: 'LECLERC supermarket',
                            receivingSite: 'Chem. Atlanta',
                            orderDate: '2020-08-10',
                            url: urlDocument,
                            requester: 'Unit Test',
                            requestDate: '2020-05-29',
                            urlReject: `${urlDocument}/${btoa(JSON.stringify(jsonReject))}`,
                            urlApprove: `${urlDocument}/${btoa(JSON.stringify(jsonApprove))}`,
                            urlPurchaseDocument: `${page}/${btoa(JSON.stringify(jsonPurchaseDocument))}`,
                        },
                    ],
                ]);

                assert.isTrue(isSend);
            },
            { today: '2020-05-29' },
        ));
    it('Purchase Order  - approval', () =>
        Test.withContext(
            async context => {
                const sendSpy = stubSendMail(sandbox, {
                    recipients: { to: [to] },
                    htmlMatcher: [],
                    subject: 'This purchase order needs approval: [Purchase order PO2]',
                });
                const purchaseOrder = await context.read(
                    xtremPurchasing.nodes.PurchaseOrder,
                    { number: 'PO2' },
                    { forUpdate: true },
                );
                const isSend = await xtremPurchasing.nodes.PurchaseOrder.sendApprovalRequestMail(
                    context,
                    purchaseOrder,
                    await getUser(context, '<EMAIL>'),
                );

                const urlApproval = 'http://localhost:8240/@sage/xtrem-purchasing/PurchaseOrderApproval';
                const urlDocument = 'http://localhost:8240/@sage/xtrem-purchasing/PurchaseOrder';
                const jsonUrl = { _id: purchaseOrder._id.toString() };
                const jsonReject = { ...jsonUrl, action: 'rejected' };
                const jsonApprove = { ...jsonUrl, action: 'approved' };
                const jsonPurchaseDocument = { _id: purchaseOrder._id.toString() };

                assert.deepEqual(sendSpy.args[0], [
                    { to: [to] },
                    'This purchase order needs approval: [Purchase order PO2]',
                    'purchase_order_approval_mail',
                    {
                        number: 'PO2',
                        orderDate: '2020-08-10',
                        receivingSite: 'Chem. Atlanta',
                        requestDate: '2020-05-29',
                        requester: 'Unit Test',
                        supplier: 'LECLERC supermarket',
                        url: urlApproval,
                        urlApprove: `${urlApproval}/${btoa(JSON.stringify(jsonApprove))}`,
                        urlReject: `${urlApproval}/${btoa(JSON.stringify(jsonReject))}`,
                        urlPurchaseDocument: `${urlDocument}/${btoa(JSON.stringify(jsonPurchaseDocument))}`,
                    },
                ]);
                sandbox.reset();
                assert.isTrue(isSend);
            },
            { today: '2020-05-29' },
        ));
    it('Purchase Requision request', () =>
        Test.withContext(
            async context => {
                const sendSpy = stubSendMail(sandbox, {
                    recipients: { to: [to] },
                    htmlMatcher: [],
                    subject: '[Purchase requisition REQ1] changes requested',
                });
                const requisition = await context.read(
                    xtremPurchasing.nodes.PurchaseRequisition,
                    { number: 'REQ1' },
                    { forUpdate: true },
                );

                const isSend = await xtremPurchasing.nodes.PurchaseRequisition.sendRequestChangesMail(
                    context,
                    requisition,
                    await getUser(context, '<EMAIL>'),
                );

                const urlDocument = 'http://localhost:8240/@sage/xtrem-purchasing/PurchaseRequisitionApproval';
                const page = 'http://localhost:8240/@sage/xtrem-purchasing/PurchaseRequisition';
                const jsonUrl = { _id: requisition._id.toString() };
                const jsonReject = { ...jsonUrl, action: 'rejected' };
                const jsonApprove = { ...jsonUrl, action: 'approved' };
                const jsonPurchaseDocument = { _id: requisition._id.toString() };

                assert.deepEqual(sendSpy.args[0], [
                    { to: [to] },
                    '[Purchase requisition REQ1] changes requested',
                    'purchase_requisition_request_changes_mail',
                    {
                        number: 'REQ1',
                        requester: 'da Cruz e Mello Moraes, Marcus Vinicius',
                        receivingSite: 'Chem. Atlanta',
                        requestDate: '2020-08-10',
                        urlReject: `${urlDocument}/${btoa(JSON.stringify(jsonReject))}`,
                        urlApprove: `${urlDocument}/${btoa(JSON.stringify(jsonApprove))}`,
                        urlPurchaseDocument: `${page}/${btoa(JSON.stringify(jsonPurchaseDocument))}`,
                    },
                ]);
                sandbox.reset();
                assert.isTrue(isSend);
            },
            { today: '2020-05-29' },
        ));
    it('Purchase Requisition approval', () =>
        Test.withContext(
            async context => {
                const sendSpy = stubSendMail(sandbox, {
                    recipients: { to: [to] },
                    htmlMatcher: [
                        // 'Purchase requisition <span>REQ1</span> requested by <span>da Cruz e Mello Moraes, Marcus Vinicius</span> from <span>Chem. Atlanta</span> on <span>2020-08-10</span>.',
                    ],
                    subject: '[Purchase requisition REQ1] approval request',
                });
                const requisition = await context.read(
                    xtremPurchasing.nodes.PurchaseRequisition,
                    { number: 'REQ1' },
                    { forUpdate: true },
                );
                const isSend = await xtremPurchasing.nodes.PurchaseRequisition.sendApprovalRequestMail(
                    context,
                    requisition,

                    await getUser(context, '<EMAIL>'),
                );

                const urlDocument = 'http://localhost:8240/@sage/xtrem-purchasing/PurchaseRequisitionApproval';
                const page = 'http://localhost:8240/@sage/xtrem-purchasing/PurchaseRequisition';
                const jsonUrl = { _id: requisition._id.toString() };
                const jsonReject = { ...jsonUrl, action: 'rejected' };
                const jsonApprove = { ...jsonUrl, action: 'approved' };
                const jsonPurchaseDocument = { _id: requisition._id.toString() };

                assert.deepEqual(sendSpy.args[0], [
                    { to: [to] },
                    '[Purchase requisition REQ1] approval request',
                    'purchase_requisition_approval_mail',
                    {
                        number: 'REQ1',
                        requester: 'da Cruz e Mello Moraes, Marcus Vinicius',
                        receivingSite: 'Chem. Atlanta',
                        requestDate: '2020-08-10',
                        urlReject: `${urlDocument}/${btoa(JSON.stringify(jsonReject))}`,
                        urlApprove: `${urlDocument}/${btoa(JSON.stringify(jsonApprove))}`,
                        urlPurchaseDocument: `${page}/${btoa(JSON.stringify(jsonPurchaseDocument))}`,
                    },
                ]);
                sandbox.reset();
                assert.isTrue(isSend);
            },
            { today: '2020-05-29' },
        ));
    it('Purchase Invoice ', () =>
        Test.withContext(
            async context => {
                const sendSpy = stubSendMail(sandbox, {
                    recipients: { to: [to] },
                    htmlMatcher: ['Purchase invoice PINVUS0012021090001 <NAME_EMAIL>'],
                    subject: '[Purchase invoice to be matched - PINVUS0012021090001]',
                });
                const invoice = await context.read(
                    xtremPurchasing.nodes.PurchaseInvoice,
                    {
                        number: 'PINVUS0012021090001',
                    },
                    { forUpdate: true },
                );
                const urlDocument = 'http://localhost:8240/@sage/xtrem-purchasing/PurchaseInvoice';
                const jsonPurchaseDocument = { _id: invoice._id.toString() };

                const isSend = await xtremPurchasing.nodes.PurchaseInvoice.sendNotificationToBuyerMail(
                    context,
                    invoice,
                    await getUser(context, '<EMAIL>'),
                );
                assert.deepEqual(sendSpy.args[0], [
                    { to: [to] },
                    '[Purchase invoice to be matched - PINVUS0012021090001]',
                    'purchase_invoice_buyer_notification_mail',
                    {
                        number: 'PINVUS0012021090001',
                        supplier: 'LECLERC supermarket',
                        site: 'Chem. Atlanta',
                        orderDate: '2021-09-20',
                        url: urlDocument,
                        requester: '<EMAIL>',
                        urlPurchaseDocument: `${urlDocument}/${btoa(JSON.stringify(jsonPurchaseDocument))}`,
                    },
                ]);
                sandbox.reset();
                assert.isTrue(isSend);
            },
            { today: '2021-03-02' },
        ));
    it('Purchase Credit memo', () =>
        Test.withContext(
            async context => {
                const email = '<EMAIL>';
                const buyer = await context.read(xtremSystem.nodes.User, { email });
                const sendSpy = stubSendMail(sandbox, {
                    recipients: { to: [to] },
                    htmlMatcher: [/Purchase credit memo\s+PCM1 requested by unit\.test@acme\.com/],
                    subject: 'Purchase credit memo PCM1: Matching request',
                });
                const creditMemo = await context.read(
                    xtremPurchasing.nodes.PurchaseCreditMemo,
                    { number: 'PCM1' },
                    { forUpdate: true },
                );
                const urlDocument = 'http://localhost:8240/@sage/xtrem-purchasing/PurchaseCreditMemo';
                const jsonPurchaseDocument = { _id: creditMemo._id.toString() };

                const isSend = await xtremPurchasing.nodes.PurchaseCreditMemo.sendNotificationToBuyerMail(
                    context,
                    creditMemo,
                    await getUser(context, '<EMAIL>'),
                );
                assert.deepEqual(sendSpy.args[0], [
                    { to: [to] },
                    'Purchase credit memo PCM1: Matching request',
                    'purchase_credit_memo_buyer_notification_mail',
                    {
                        number: 'PCM1',
                        url: urlDocument,
                        requester: '<EMAIL>',
                        buyerId: buyer._id,
                        urlPurchaseDocument: `${urlDocument}/${btoa(JSON.stringify(jsonPurchaseDocument))}`,
                    },
                ]);
                sandbox.reset();
                assert.isTrue(isSend);
            },
            { today: '2021-11-04' },
        ));
});
