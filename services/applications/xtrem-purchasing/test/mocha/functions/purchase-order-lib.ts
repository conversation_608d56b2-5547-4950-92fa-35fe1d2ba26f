import type { Context, Diagnose } from '@sage/xtrem-core';
import { Test } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremStockData from '@sage/xtrem-stock-data';
import * as xtremSystem from '@sage/xtrem-system';
import { assert } from 'chai';
import * as xtremPurchasing from '../../../lib/index';

let item: xtremMasterData.nodes.Item;
let site: xtremSystem.nodes.Site;
let itemSite: xtremMasterData.nodes.ItemSite;
let itemSupplier: xtremMasterData.nodes.ItemSupplier;
let itemSiteSupplier: xtremMasterData.nodes.ItemSiteSupplier;

async function isClosable(
    context: Context,
    previousLineStatus: xtremPurchasing.enums.PurchaseDocumentStatus,
    previousLineReceiptStatus: xtremPurchasing.enums.PurchaseOrderReceiptStatus,
    previousLineInvoiceStatus: xtremPurchasing.enums.PurchaseOrderInvoiceStatus,
    newApprovalStatus: xtremPurchasing.enums.PurchaseDocumentApprovalStatus,
) {
    const purchaseOrder = await context.read(xtremPurchasing.nodes.PurchaseOrder, { _id: '#PO1' });
    const purchaseOrderLine: xtremPurchasing.nodes.PurchaseOrderLine = await purchaseOrder.lines.elementAt(0);
    const diagnoses: Diagnose[] = await xtremPurchasing.functions.PurchaseOrderLib.controlCloseOrderLine(
        context,
        purchaseOrderLine,
        {
            previousLineStatus,
            previousLineReceiptStatus,
            previousLineInvoiceStatus,
            newApprovalStatus,
        },
    );
    if (
        diagnoses.length &&
        diagnoses[0].message ===
            context.localize(
                '@sage/xtrem-purchasing/functions-purchase-order-lib_purchase_order_line__error_updating_status_order_line_not_closable',
                `Unable to update status on already closed, received, pending approval order line.`,
            )
    ) {
        return false;
    }
    return true;
}

async function setupData(context: Context): Promise<void> {
    item = await context.read(xtremMasterData.nodes.Item, { id: 'Milk' });
    site = await context.read(xtremSystem.nodes.Site, { id: 'US002' });
    itemSite = await context.read(xtremMasterData.nodes.ItemSite, { item, site });
    const supplier = await context.read(xtremMasterData.nodes.Supplier, { _id: '#500' });
    itemSupplier = await context.read(xtremMasterData.nodes.ItemSupplier, {
        item,
        supplier: supplier._id,
    });
    itemSiteSupplier = await context.read(xtremMasterData.nodes.ItemSiteSupplier, {
        itemSite,
        supplier: await itemSupplier?.supplier,
    });
}
describe('Replenishment functions', () => {
    it('Create replenishment purchase order suggestion multiple EOQ', () =>
        Test.withContext(
            async context => {
                await setupData(context);
                const stockIssueDetail = await context.create(xtremStockData.nodes.StockIssueDetail, {
                    stockRecord: 29,
                    quantityInStockUnit: -8,
                    documentLine: '2401',
                });

                await stockIssueDetail.$.save();

                await xtremPurchasing.functions.PurchaseOrderLib.createPurchaseOrderReplenishment(
                    context,
                    item,
                    site,
                    itemSiteSupplier,
                    itemSupplier,
                    itemSite,
                    450,
                );

                assert.deepEqual((await stockIssueDetail.quantityInStockUnit).toString(), '-8');
            },
            { today: '2020-05-29' },
        ));

    it('Create replenishment purchase order suggestion greater EOQ', () =>
        Test.withContext(
            async context => {
                await setupData(context);
                const stockIssueDetail = await context.create(xtremStockData.nodes.StockIssueDetail, {
                    stockRecord: 29,
                    quantityInStockUnit: -8,
                    documentLine: '2401',
                });

                await stockIssueDetail.$.save();

                await xtremPurchasing.functions.PurchaseOrderLib.createPurchaseOrderReplenishment(
                    context,
                    item,
                    site,
                    itemSiteSupplier,
                    itemSupplier,
                    itemSite,
                    230,
                );

                assert.deepEqual((await stockIssueDetail.quantityInStockUnit).toString(), '-8');
            },
            { today: '2020-05-29' },
        ));

    it('Create replenishment purchase order suggestion minimum purchase quantity', () =>
        Test.withContext(
            async context => {
                await setupData(context);
                const stockIssueDetail = await context.create(xtremStockData.nodes.StockIssueDetail, {
                    stockRecord: 29,
                    quantityInStockUnit: -8,
                    documentLine: '2401',
                });

                await stockIssueDetail.$.save();

                await xtremPurchasing.functions.PurchaseOrderLib.createPurchaseOrderReplenishment(
                    context,
                    item,
                    site,
                    itemSiteSupplier,
                    itemSupplier,
                    itemSite,
                    2,
                );

                assert.deepEqual((await stockIssueDetail.quantityInStockUnit).toString(), '-8');
            },
            { today: '2020-05-29' },
        ));
    it('Checking the controlCloseOrderLine function', () =>
        Test.withContext(async context => {
            assert.isFalse(await isClosable(context, 'draft', 'notReceived', 'notInvoiced', 'confirmed'));
            assert.isTrue(await isClosable(context, 'draft', 'notReceived', 'notInvoiced', 'rejected'));
            assert.isFalse(await isClosable(context, 'draft', 'notReceived', 'notInvoiced', 'draft'));
            assert.isFalse(await isClosable(context, 'draft', 'notReceived', 'notInvoiced', 'pendingApproval'));
            assert.isFalse(await isClosable(context, 'draft', 'notReceived', 'notInvoiced', 'approved'));
            assert.isFalse(await isClosable(context, 'draft', 'notReceived', 'notInvoiced', 'changeRequested'));
            assert.isFalse(await isClosable(context, 'draft', 'notReceived', 'partiallyInvoiced', 'confirmed'));
            assert.isTrue(await isClosable(context, 'draft', 'notReceived', 'partiallyInvoiced', 'rejected'));
            assert.isFalse(await isClosable(context, 'draft', 'notReceived', 'partiallyInvoiced', 'draft'));
            assert.isFalse(await isClosable(context, 'draft', 'notReceived', 'partiallyInvoiced', 'pendingApproval'));
            assert.isFalse(await isClosable(context, 'draft', 'notReceived', 'partiallyInvoiced', 'approved'));
            assert.isFalse(await isClosable(context, 'draft', 'notReceived', 'partiallyInvoiced', 'changeRequested'));
            assert.isFalse(await isClosable(context, 'draft', 'notReceived', 'invoiced', 'confirmed'));
            assert.isTrue(await isClosable(context, 'draft', 'notReceived', 'invoiced', 'rejected'));
            assert.isFalse(await isClosable(context, 'draft', 'notReceived', 'invoiced', 'draft'));
            assert.isFalse(await isClosable(context, 'draft', 'notReceived', 'invoiced', 'pendingApproval'));
            assert.isFalse(await isClosable(context, 'draft', 'notReceived', 'invoiced', 'approved'));
            assert.isFalse(await isClosable(context, 'draft', 'notReceived', 'invoiced', 'changeRequested'));
            assert.isFalse(await isClosable(context, 'draft', 'partiallyReceived', 'notInvoiced', 'confirmed'));
            assert.isTrue(await isClosable(context, 'draft', 'partiallyReceived', 'notInvoiced', 'rejected'));
            assert.isFalse(await isClosable(context, 'draft', 'partiallyReceived', 'notInvoiced', 'draft'));
            assert.isFalse(await isClosable(context, 'draft', 'partiallyReceived', 'notInvoiced', 'pendingApproval'));
            assert.isFalse(await isClosable(context, 'draft', 'partiallyReceived', 'notInvoiced', 'approved'));
            assert.isFalse(await isClosable(context, 'draft', 'partiallyReceived', 'notInvoiced', 'changeRequested'));
            assert.isFalse(await isClosable(context, 'draft', 'partiallyReceived', 'partiallyInvoiced', 'confirmed'));
            assert.isTrue(await isClosable(context, 'draft', 'partiallyReceived', 'partiallyInvoiced', 'rejected'));
            assert.isFalse(await isClosable(context, 'draft', 'partiallyReceived', 'partiallyInvoiced', 'draft'));
            assert.isFalse(
                await isClosable(context, 'draft', 'partiallyReceived', 'partiallyInvoiced', 'pendingApproval'),
            );
            assert.isFalse(await isClosable(context, 'draft', 'partiallyReceived', 'partiallyInvoiced', 'approved'));
            assert.isFalse(
                await isClosable(context, 'draft', 'partiallyReceived', 'partiallyInvoiced', 'changeRequested'),
            );
            assert.isFalse(await isClosable(context, 'draft', 'partiallyReceived', 'invoiced', 'confirmed'));
            assert.isTrue(await isClosable(context, 'draft', 'partiallyReceived', 'invoiced', 'rejected'));
            assert.isFalse(await isClosable(context, 'draft', 'partiallyReceived', 'invoiced', 'draft'));
            assert.isFalse(await isClosable(context, 'draft', 'partiallyReceived', 'invoiced', 'pendingApproval'));
            assert.isFalse(await isClosable(context, 'draft', 'partiallyReceived', 'invoiced', 'approved'));
            assert.isFalse(await isClosable(context, 'draft', 'partiallyReceived', 'invoiced', 'changeRequested'));
            assert.isFalse(await isClosable(context, 'draft', 'received', 'notInvoiced', 'confirmed'));
            assert.isTrue(await isClosable(context, 'draft', 'received', 'notInvoiced', 'rejected'));
            assert.isFalse(await isClosable(context, 'draft', 'received', 'notInvoiced', 'draft'));
            assert.isFalse(await isClosable(context, 'draft', 'received', 'notInvoiced', 'pendingApproval'));
            assert.isFalse(await isClosable(context, 'draft', 'received', 'notInvoiced', 'approved'));
            assert.isFalse(await isClosable(context, 'draft', 'received', 'notInvoiced', 'changeRequested'));
            assert.isFalse(await isClosable(context, 'draft', 'received', 'partiallyInvoiced', 'confirmed'));
            assert.isTrue(await isClosable(context, 'draft', 'received', 'partiallyInvoiced', 'rejected'));
            assert.isFalse(await isClosable(context, 'draft', 'received', 'partiallyInvoiced', 'draft'));
            assert.isFalse(await isClosable(context, 'draft', 'received', 'partiallyInvoiced', 'pendingApproval'));
            assert.isFalse(await isClosable(context, 'draft', 'received', 'partiallyInvoiced', 'approved'));
            assert.isFalse(await isClosable(context, 'draft', 'received', 'partiallyInvoiced', 'changeRequested'));
            assert.isFalse(await isClosable(context, 'draft', 'received', 'invoiced', 'confirmed'));
            assert.isFalse(await isClosable(context, 'draft', 'received', 'invoiced', 'rejected'));
            assert.isFalse(await isClosable(context, 'draft', 'received', 'invoiced', 'draft'));
            assert.isFalse(await isClosable(context, 'draft', 'received', 'invoiced', 'pendingApproval'));
            assert.isFalse(await isClosable(context, 'draft', 'received', 'invoiced', 'approved'));
            assert.isFalse(await isClosable(context, 'draft', 'received', 'invoiced', 'changeRequested'));
            assert.isTrue(await isClosable(context, 'pending', 'notReceived', 'notInvoiced', 'confirmed'));
            assert.isTrue(await isClosable(context, 'pending', 'notReceived', 'notInvoiced', 'rejected'));
            assert.isTrue(await isClosable(context, 'pending', 'notReceived', 'notInvoiced', 'draft'));
            assert.isFalse(await isClosable(context, 'pending', 'notReceived', 'notInvoiced', 'pendingApproval'));
            assert.isTrue(await isClosable(context, 'pending', 'notReceived', 'notInvoiced', 'approved'));
            assert.isTrue(await isClosable(context, 'pending', 'notReceived', 'notInvoiced', 'changeRequested'));
            assert.isTrue(await isClosable(context, 'pending', 'notReceived', 'partiallyInvoiced', 'confirmed'));
            assert.isTrue(await isClosable(context, 'pending', 'notReceived', 'partiallyInvoiced', 'rejected'));
            assert.isTrue(await isClosable(context, 'pending', 'notReceived', 'partiallyInvoiced', 'draft'));
            assert.isFalse(await isClosable(context, 'pending', 'notReceived', 'partiallyInvoiced', 'pendingApproval'));
            assert.isTrue(await isClosable(context, 'pending', 'notReceived', 'partiallyInvoiced', 'approved'));
            assert.isTrue(await isClosable(context, 'pending', 'notReceived', 'partiallyInvoiced', 'changeRequested'));
            assert.isTrue(await isClosable(context, 'pending', 'notReceived', 'invoiced', 'confirmed'));
            assert.isTrue(await isClosable(context, 'pending', 'notReceived', 'invoiced', 'rejected'));
            assert.isTrue(await isClosable(context, 'pending', 'notReceived', 'invoiced', 'draft'));
            assert.isFalse(await isClosable(context, 'pending', 'notReceived', 'invoiced', 'pendingApproval'));
            assert.isTrue(await isClosable(context, 'pending', 'notReceived', 'invoiced', 'approved'));
            assert.isTrue(await isClosable(context, 'pending', 'notReceived', 'invoiced', 'changeRequested'));
            assert.isTrue(await isClosable(context, 'pending', 'partiallyReceived', 'notInvoiced', 'confirmed'));
            assert.isTrue(await isClosable(context, 'pending', 'partiallyReceived', 'notInvoiced', 'rejected'));
            assert.isTrue(await isClosable(context, 'pending', 'partiallyReceived', 'notInvoiced', 'draft'));
            assert.isFalse(await isClosable(context, 'pending', 'partiallyReceived', 'notInvoiced', 'pendingApproval'));
            assert.isTrue(await isClosable(context, 'pending', 'partiallyReceived', 'notInvoiced', 'approved'));
            assert.isTrue(await isClosable(context, 'pending', 'partiallyReceived', 'notInvoiced', 'changeRequested'));
            assert.isTrue(await isClosable(context, 'pending', 'partiallyReceived', 'partiallyInvoiced', 'confirmed'));
            assert.isTrue(await isClosable(context, 'pending', 'partiallyReceived', 'partiallyInvoiced', 'rejected'));
            assert.isTrue(await isClosable(context, 'pending', 'partiallyReceived', 'partiallyInvoiced', 'draft'));
            assert.isFalse(
                await isClosable(context, 'pending', 'partiallyReceived', 'partiallyInvoiced', 'pendingApproval'),
            );
            assert.isTrue(await isClosable(context, 'pending', 'partiallyReceived', 'partiallyInvoiced', 'approved'));
            assert.isTrue(
                await isClosable(context, 'pending', 'partiallyReceived', 'partiallyInvoiced', 'changeRequested'),
            );
            assert.isTrue(await isClosable(context, 'pending', 'partiallyReceived', 'invoiced', 'confirmed'));
            assert.isTrue(await isClosable(context, 'pending', 'partiallyReceived', 'invoiced', 'rejected'));
            assert.isTrue(await isClosable(context, 'pending', 'partiallyReceived', 'invoiced', 'draft'));
            assert.isFalse(await isClosable(context, 'pending', 'partiallyReceived', 'invoiced', 'pendingApproval'));
            assert.isTrue(await isClosable(context, 'pending', 'partiallyReceived', 'invoiced', 'approved'));
            assert.isTrue(await isClosable(context, 'pending', 'partiallyReceived', 'invoiced', 'changeRequested'));
            assert.isTrue(await isClosable(context, 'pending', 'received', 'notInvoiced', 'confirmed'));
            assert.isTrue(await isClosable(context, 'pending', 'received', 'notInvoiced', 'rejected'));
            assert.isTrue(await isClosable(context, 'pending', 'received', 'notInvoiced', 'draft'));
            assert.isFalse(await isClosable(context, 'pending', 'received', 'notInvoiced', 'pendingApproval'));
            assert.isTrue(await isClosable(context, 'pending', 'received', 'notInvoiced', 'approved'));
            assert.isTrue(await isClosable(context, 'pending', 'received', 'notInvoiced', 'changeRequested'));
            assert.isTrue(await isClosable(context, 'pending', 'received', 'partiallyInvoiced', 'confirmed'));
            assert.isTrue(await isClosable(context, 'pending', 'received', 'partiallyInvoiced', 'rejected'));
            assert.isTrue(await isClosable(context, 'pending', 'received', 'partiallyInvoiced', 'draft'));
            assert.isFalse(await isClosable(context, 'pending', 'received', 'partiallyInvoiced', 'pendingApproval'));
            assert.isTrue(await isClosable(context, 'pending', 'received', 'partiallyInvoiced', 'approved'));
            assert.isTrue(await isClosable(context, 'pending', 'received', 'partiallyInvoiced', 'changeRequested'));
            assert.isFalse(await isClosable(context, 'pending', 'received', 'invoiced', 'confirmed'));
            assert.isFalse(await isClosable(context, 'pending', 'received', 'invoiced', 'rejected'));
            assert.isFalse(await isClosable(context, 'pending', 'received', 'invoiced', 'draft'));
            assert.isFalse(await isClosable(context, 'pending', 'received', 'invoiced', 'pendingApproval'));
            assert.isFalse(await isClosable(context, 'pending', 'received', 'invoiced', 'approved'));
            assert.isFalse(await isClosable(context, 'pending', 'received', 'invoiced', 'changeRequested'));
            assert.isFalse(await isClosable(context, 'closed', 'notReceived', 'notInvoiced', 'confirmed'));
            assert.isFalse(await isClosable(context, 'closed', 'notReceived', 'notInvoiced', 'rejected'));
            assert.isFalse(await isClosable(context, 'closed', 'notReceived', 'notInvoiced', 'draft'));
            assert.isFalse(await isClosable(context, 'closed', 'notReceived', 'notInvoiced', 'pendingApproval'));
            assert.isFalse(await isClosable(context, 'closed', 'notReceived', 'notInvoiced', 'approved'));
            assert.isFalse(await isClosable(context, 'closed', 'notReceived', 'notInvoiced', 'changeRequested'));
            assert.isFalse(await isClosable(context, 'closed', 'notReceived', 'partiallyInvoiced', 'confirmed'));
            assert.isFalse(await isClosable(context, 'closed', 'notReceived', 'partiallyInvoiced', 'rejected'));
            assert.isFalse(await isClosable(context, 'closed', 'notReceived', 'partiallyInvoiced', 'draft'));
            assert.isFalse(await isClosable(context, 'closed', 'notReceived', 'partiallyInvoiced', 'pendingApproval'));
            assert.isFalse(await isClosable(context, 'closed', 'notReceived', 'partiallyInvoiced', 'approved'));
            assert.isFalse(await isClosable(context, 'closed', 'notReceived', 'partiallyInvoiced', 'changeRequested'));
            assert.isFalse(await isClosable(context, 'closed', 'notReceived', 'invoiced', 'confirmed'));
            assert.isFalse(await isClosable(context, 'closed', 'notReceived', 'invoiced', 'rejected'));
            assert.isFalse(await isClosable(context, 'closed', 'notReceived', 'invoiced', 'draft'));
            assert.isFalse(await isClosable(context, 'closed', 'notReceived', 'invoiced', 'pendingApproval'));
            assert.isFalse(await isClosable(context, 'closed', 'notReceived', 'invoiced', 'approved'));
            assert.isFalse(await isClosable(context, 'closed', 'notReceived', 'invoiced', 'changeRequested'));
            assert.isFalse(await isClosable(context, 'closed', 'partiallyReceived', 'notInvoiced', 'confirmed'));
            assert.isFalse(await isClosable(context, 'closed', 'partiallyReceived', 'notInvoiced', 'rejected'));
            assert.isFalse(await isClosable(context, 'closed', 'partiallyReceived', 'notInvoiced', 'draft'));
            assert.isFalse(await isClosable(context, 'closed', 'partiallyReceived', 'notInvoiced', 'pendingApproval'));
            assert.isFalse(await isClosable(context, 'closed', 'partiallyReceived', 'notInvoiced', 'approved'));
            assert.isFalse(await isClosable(context, 'closed', 'partiallyReceived', 'notInvoiced', 'changeRequested'));
            assert.isFalse(await isClosable(context, 'closed', 'partiallyReceived', 'partiallyInvoiced', 'confirmed'));
            assert.isFalse(await isClosable(context, 'closed', 'partiallyReceived', 'partiallyInvoiced', 'rejected'));
            assert.isFalse(await isClosable(context, 'closed', 'partiallyReceived', 'partiallyInvoiced', 'draft'));
            assert.isFalse(
                await isClosable(context, 'closed', 'partiallyReceived', 'partiallyInvoiced', 'pendingApproval'),
            );
            assert.isFalse(await isClosable(context, 'closed', 'partiallyReceived', 'partiallyInvoiced', 'approved'));
            assert.isFalse(
                await isClosable(context, 'closed', 'partiallyReceived', 'partiallyInvoiced', 'changeRequested'),
            );
            assert.isFalse(await isClosable(context, 'closed', 'partiallyReceived', 'invoiced', 'confirmed'));
            assert.isFalse(await isClosable(context, 'closed', 'partiallyReceived', 'invoiced', 'rejected'));
            assert.isFalse(await isClosable(context, 'closed', 'partiallyReceived', 'invoiced', 'draft'));
            assert.isFalse(await isClosable(context, 'closed', 'partiallyReceived', 'invoiced', 'pendingApproval'));
            assert.isFalse(await isClosable(context, 'closed', 'partiallyReceived', 'invoiced', 'approved'));
            assert.isFalse(await isClosable(context, 'closed', 'partiallyReceived', 'invoiced', 'changeRequested'));
            assert.isFalse(await isClosable(context, 'closed', 'received', 'notInvoiced', 'confirmed'));
            assert.isFalse(await isClosable(context, 'closed', 'received', 'notInvoiced', 'rejected'));
            assert.isFalse(await isClosable(context, 'closed', 'received', 'notInvoiced', 'draft'));
            assert.isFalse(await isClosable(context, 'closed', 'received', 'notInvoiced', 'pendingApproval'));
            assert.isFalse(await isClosable(context, 'closed', 'received', 'notInvoiced', 'approved'));
            assert.isFalse(await isClosable(context, 'closed', 'received', 'notInvoiced', 'changeRequested'));
            assert.isFalse(await isClosable(context, 'closed', 'received', 'partiallyInvoiced', 'confirmed'));
            assert.isFalse(await isClosable(context, 'closed', 'received', 'partiallyInvoiced', 'rejected'));
            assert.isFalse(await isClosable(context, 'closed', 'received', 'partiallyInvoiced', 'draft'));
            assert.isFalse(await isClosable(context, 'closed', 'received', 'partiallyInvoiced', 'pendingApproval'));
            assert.isFalse(await isClosable(context, 'closed', 'received', 'partiallyInvoiced', 'approved'));
            assert.isFalse(await isClosable(context, 'closed', 'received', 'partiallyInvoiced', 'changeRequested'));
            assert.isFalse(await isClosable(context, 'closed', 'received', 'invoiced', 'confirmed'));
            assert.isFalse(await isClosable(context, 'closed', 'received', 'invoiced', 'rejected'));
            assert.isFalse(await isClosable(context, 'closed', 'received', 'invoiced', 'draft'));
            assert.isFalse(await isClosable(context, 'closed', 'received', 'invoiced', 'pendingApproval'));
            assert.isFalse(await isClosable(context, 'closed', 'received', 'invoiced', 'approved'));
            assert.isFalse(await isClosable(context, 'closed', 'received', 'invoiced', 'changeRequested'));
            assert.isTrue(await isClosable(context, 'inProgress', 'notReceived', 'notInvoiced', 'confirmed'));
            assert.isTrue(await isClosable(context, 'inProgress', 'notReceived', 'notInvoiced', 'rejected'));
            assert.isTrue(await isClosable(context, 'inProgress', 'notReceived', 'notInvoiced', 'draft'));
            assert.isFalse(await isClosable(context, 'inProgress', 'notReceived', 'notInvoiced', 'pendingApproval'));
            assert.isTrue(await isClosable(context, 'inProgress', 'notReceived', 'notInvoiced', 'approved'));
            assert.isTrue(await isClosable(context, 'inProgress', 'notReceived', 'notInvoiced', 'changeRequested'));
            assert.isTrue(await isClosable(context, 'inProgress', 'notReceived', 'partiallyInvoiced', 'confirmed'));
            assert.isTrue(await isClosable(context, 'inProgress', 'notReceived', 'partiallyInvoiced', 'rejected'));
            assert.isTrue(await isClosable(context, 'inProgress', 'notReceived', 'partiallyInvoiced', 'draft'));
            assert.isFalse(
                await isClosable(context, 'inProgress', 'notReceived', 'partiallyInvoiced', 'pendingApproval'),
            );
            assert.isTrue(await isClosable(context, 'inProgress', 'notReceived', 'partiallyInvoiced', 'approved'));
            assert.isTrue(
                await isClosable(context, 'inProgress', 'notReceived', 'partiallyInvoiced', 'changeRequested'),
            );
            assert.isTrue(await isClosable(context, 'inProgress', 'notReceived', 'invoiced', 'confirmed'));
            assert.isTrue(await isClosable(context, 'inProgress', 'notReceived', 'invoiced', 'rejected'));
            assert.isTrue(await isClosable(context, 'inProgress', 'notReceived', 'invoiced', 'draft'));
            assert.isFalse(await isClosable(context, 'inProgress', 'notReceived', 'invoiced', 'pendingApproval'));
            assert.isTrue(await isClosable(context, 'inProgress', 'notReceived', 'invoiced', 'approved'));
            assert.isTrue(await isClosable(context, 'inProgress', 'notReceived', 'invoiced', 'changeRequested'));
            assert.isTrue(await isClosable(context, 'inProgress', 'partiallyReceived', 'notInvoiced', 'confirmed'));
            assert.isTrue(await isClosable(context, 'inProgress', 'partiallyReceived', 'notInvoiced', 'rejected'));
            assert.isTrue(await isClosable(context, 'inProgress', 'partiallyReceived', 'notInvoiced', 'draft'));
            assert.isFalse(
                await isClosable(context, 'inProgress', 'partiallyReceived', 'notInvoiced', 'pendingApproval'),
            );
            assert.isTrue(await isClosable(context, 'inProgress', 'partiallyReceived', 'notInvoiced', 'approved'));
            assert.isTrue(
                await isClosable(context, 'inProgress', 'partiallyReceived', 'notInvoiced', 'changeRequested'),
            );
            assert.isTrue(
                await isClosable(context, 'inProgress', 'partiallyReceived', 'partiallyInvoiced', 'confirmed'),
            );
            assert.isTrue(
                await isClosable(context, 'inProgress', 'partiallyReceived', 'partiallyInvoiced', 'rejected'),
            );
            assert.isTrue(await isClosable(context, 'inProgress', 'partiallyReceived', 'partiallyInvoiced', 'draft'));
            assert.isFalse(
                await isClosable(context, 'inProgress', 'partiallyReceived', 'partiallyInvoiced', 'pendingApproval'),
            );
            assert.isTrue(
                await isClosable(context, 'inProgress', 'partiallyReceived', 'partiallyInvoiced', 'approved'),
            );
            assert.isTrue(
                await isClosable(context, 'inProgress', 'partiallyReceived', 'partiallyInvoiced', 'changeRequested'),
            );
            assert.isTrue(await isClosable(context, 'inProgress', 'partiallyReceived', 'invoiced', 'confirmed'));
            assert.isTrue(await isClosable(context, 'inProgress', 'partiallyReceived', 'invoiced', 'rejected'));
            assert.isTrue(await isClosable(context, 'inProgress', 'partiallyReceived', 'invoiced', 'draft'));
            assert.isFalse(await isClosable(context, 'inProgress', 'partiallyReceived', 'invoiced', 'pendingApproval'));
            assert.isTrue(await isClosable(context, 'inProgress', 'partiallyReceived', 'invoiced', 'approved'));
            assert.isTrue(await isClosable(context, 'inProgress', 'partiallyReceived', 'invoiced', 'changeRequested'));
            assert.isTrue(await isClosable(context, 'inProgress', 'received', 'notInvoiced', 'confirmed'));
            assert.isTrue(await isClosable(context, 'inProgress', 'received', 'notInvoiced', 'rejected'));
            assert.isTrue(await isClosable(context, 'inProgress', 'received', 'notInvoiced', 'draft'));
            assert.isFalse(await isClosable(context, 'inProgress', 'received', 'notInvoiced', 'pendingApproval'));
            assert.isTrue(await isClosable(context, 'inProgress', 'received', 'notInvoiced', 'approved'));
            assert.isTrue(await isClosable(context, 'inProgress', 'received', 'notInvoiced', 'changeRequested'));
            assert.isTrue(await isClosable(context, 'inProgress', 'received', 'partiallyInvoiced', 'confirmed'));
            assert.isTrue(await isClosable(context, 'inProgress', 'received', 'partiallyInvoiced', 'rejected'));
            assert.isTrue(await isClosable(context, 'inProgress', 'received', 'partiallyInvoiced', 'draft'));
            assert.isFalse(await isClosable(context, 'inProgress', 'received', 'partiallyInvoiced', 'pendingApproval'));
            assert.isTrue(await isClosable(context, 'inProgress', 'received', 'partiallyInvoiced', 'approved'));
            assert.isTrue(await isClosable(context, 'inProgress', 'received', 'partiallyInvoiced', 'changeRequested'));
            assert.isFalse(await isClosable(context, 'inProgress', 'received', 'invoiced', 'confirmed'));
            assert.isFalse(await isClosable(context, 'inProgress', 'received', 'invoiced', 'rejected'));
            assert.isFalse(await isClosable(context, 'inProgress', 'received', 'invoiced', 'draft'));
            assert.isFalse(await isClosable(context, 'inProgress', 'received', 'invoiced', 'pendingApproval'));
            assert.isFalse(await isClosable(context, 'inProgress', 'received', 'invoiced', 'approved'));
            assert.isFalse(await isClosable(context, 'inProgress', 'received', 'invoiced', 'changeRequested'));
            assert.isTrue(await isClosable(context, 'posted', 'notReceived', 'notInvoiced', 'confirmed'));
            assert.isTrue(await isClosable(context, 'posted', 'notReceived', 'notInvoiced', 'rejected'));
            assert.isTrue(await isClosable(context, 'posted', 'notReceived', 'notInvoiced', 'draft'));
            assert.isFalse(await isClosable(context, 'posted', 'notReceived', 'notInvoiced', 'pendingApproval'));
            assert.isTrue(await isClosable(context, 'posted', 'notReceived', 'notInvoiced', 'approved'));
            assert.isTrue(await isClosable(context, 'posted', 'notReceived', 'notInvoiced', 'changeRequested'));
            assert.isTrue(await isClosable(context, 'posted', 'notReceived', 'partiallyInvoiced', 'confirmed'));
            assert.isTrue(await isClosable(context, 'posted', 'notReceived', 'partiallyInvoiced', 'rejected'));
            assert.isTrue(await isClosable(context, 'posted', 'notReceived', 'partiallyInvoiced', 'draft'));
            assert.isFalse(await isClosable(context, 'posted', 'notReceived', 'partiallyInvoiced', 'pendingApproval'));
            assert.isTrue(await isClosable(context, 'posted', 'notReceived', 'partiallyInvoiced', 'approved'));
            assert.isTrue(await isClosable(context, 'posted', 'notReceived', 'partiallyInvoiced', 'changeRequested'));
            assert.isTrue(await isClosable(context, 'posted', 'notReceived', 'invoiced', 'confirmed'));
            assert.isTrue(await isClosable(context, 'posted', 'notReceived', 'invoiced', 'rejected'));
            assert.isTrue(await isClosable(context, 'posted', 'notReceived', 'invoiced', 'draft'));
            assert.isFalse(await isClosable(context, 'posted', 'notReceived', 'invoiced', 'pendingApproval'));
            assert.isTrue(await isClosable(context, 'posted', 'notReceived', 'invoiced', 'approved'));
            assert.isTrue(await isClosable(context, 'posted', 'notReceived', 'invoiced', 'changeRequested'));
            assert.isTrue(await isClosable(context, 'posted', 'partiallyReceived', 'notInvoiced', 'confirmed'));
            assert.isTrue(await isClosable(context, 'posted', 'partiallyReceived', 'notInvoiced', 'rejected'));
            assert.isTrue(await isClosable(context, 'posted', 'partiallyReceived', 'notInvoiced', 'draft'));
            assert.isFalse(await isClosable(context, 'posted', 'partiallyReceived', 'notInvoiced', 'pendingApproval'));
            assert.isTrue(await isClosable(context, 'posted', 'partiallyReceived', 'notInvoiced', 'approved'));
            assert.isTrue(await isClosable(context, 'posted', 'partiallyReceived', 'notInvoiced', 'changeRequested'));
            assert.isTrue(await isClosable(context, 'posted', 'partiallyReceived', 'partiallyInvoiced', 'confirmed'));
            assert.isTrue(await isClosable(context, 'posted', 'partiallyReceived', 'partiallyInvoiced', 'rejected'));
            assert.isTrue(await isClosable(context, 'posted', 'partiallyReceived', 'partiallyInvoiced', 'draft'));
            assert.isFalse(
                await isClosable(context, 'posted', 'partiallyReceived', 'partiallyInvoiced', 'pendingApproval'),
            );
            assert.isTrue(await isClosable(context, 'posted', 'partiallyReceived', 'partiallyInvoiced', 'approved'));
            assert.isTrue(
                await isClosable(context, 'posted', 'partiallyReceived', 'partiallyInvoiced', 'changeRequested'),
            );
            assert.isTrue(await isClosable(context, 'posted', 'partiallyReceived', 'invoiced', 'confirmed'));
            assert.isTrue(await isClosable(context, 'posted', 'partiallyReceived', 'invoiced', 'rejected'));
            assert.isTrue(await isClosable(context, 'posted', 'partiallyReceived', 'invoiced', 'draft'));
            assert.isFalse(await isClosable(context, 'posted', 'partiallyReceived', 'invoiced', 'pendingApproval'));
            assert.isTrue(await isClosable(context, 'posted', 'partiallyReceived', 'invoiced', 'approved'));
            assert.isTrue(await isClosable(context, 'posted', 'partiallyReceived', 'invoiced', 'changeRequested'));
            assert.isTrue(await isClosable(context, 'posted', 'received', 'notInvoiced', 'confirmed'));
            assert.isTrue(await isClosable(context, 'posted', 'received', 'notInvoiced', 'rejected'));
            assert.isTrue(await isClosable(context, 'posted', 'received', 'notInvoiced', 'draft'));
            assert.isFalse(await isClosable(context, 'posted', 'received', 'notInvoiced', 'pendingApproval'));
            assert.isTrue(await isClosable(context, 'posted', 'received', 'notInvoiced', 'approved'));
            assert.isTrue(await isClosable(context, 'posted', 'received', 'notInvoiced', 'changeRequested'));
            assert.isTrue(await isClosable(context, 'posted', 'received', 'partiallyInvoiced', 'confirmed'));
            assert.isTrue(await isClosable(context, 'posted', 'received', 'partiallyInvoiced', 'rejected'));
            assert.isTrue(await isClosable(context, 'posted', 'received', 'partiallyInvoiced', 'draft'));
            assert.isFalse(await isClosable(context, 'posted', 'received', 'partiallyInvoiced', 'pendingApproval'));
            assert.isTrue(await isClosable(context, 'posted', 'received', 'partiallyInvoiced', 'approved'));
            assert.isTrue(await isClosable(context, 'posted', 'received', 'partiallyInvoiced', 'changeRequested'));
            assert.isFalse(await isClosable(context, 'posted', 'received', 'invoiced', 'confirmed'));
            assert.isFalse(await isClosable(context, 'posted', 'received', 'invoiced', 'rejected'));
            assert.isFalse(await isClosable(context, 'posted', 'received', 'invoiced', 'draft'));
            assert.isFalse(await isClosable(context, 'posted', 'received', 'invoiced', 'pendingApproval'));
            assert.isFalse(await isClosable(context, 'posted', 'received', 'invoiced', 'approved'));
            assert.isFalse(await isClosable(context, 'posted', 'received', 'invoiced', 'changeRequested'));
            assert.isTrue(await isClosable(context, 'error', 'notReceived', 'notInvoiced', 'confirmed'));
            assert.isTrue(await isClosable(context, 'error', 'notReceived', 'notInvoiced', 'rejected'));
            assert.isTrue(await isClosable(context, 'error', 'notReceived', 'notInvoiced', 'draft'));
            assert.isFalse(await isClosable(context, 'error', 'notReceived', 'notInvoiced', 'pendingApproval'));
            assert.isTrue(await isClosable(context, 'error', 'notReceived', 'notInvoiced', 'approved'));
            assert.isTrue(await isClosable(context, 'error', 'notReceived', 'notInvoiced', 'changeRequested'));
            assert.isTrue(await isClosable(context, 'error', 'notReceived', 'partiallyInvoiced', 'confirmed'));
            assert.isTrue(await isClosable(context, 'error', 'notReceived', 'partiallyInvoiced', 'rejected'));
            assert.isTrue(await isClosable(context, 'error', 'notReceived', 'partiallyInvoiced', 'draft'));
            assert.isFalse(await isClosable(context, 'error', 'notReceived', 'partiallyInvoiced', 'pendingApproval'));
            assert.isTrue(await isClosable(context, 'error', 'notReceived', 'partiallyInvoiced', 'approved'));
            assert.isTrue(await isClosable(context, 'error', 'notReceived', 'partiallyInvoiced', 'changeRequested'));
            assert.isTrue(await isClosable(context, 'error', 'notReceived', 'invoiced', 'confirmed'));
            assert.isTrue(await isClosable(context, 'error', 'notReceived', 'invoiced', 'rejected'));
            assert.isTrue(await isClosable(context, 'error', 'notReceived', 'invoiced', 'draft'));
            assert.isFalse(await isClosable(context, 'error', 'notReceived', 'invoiced', 'pendingApproval'));
            assert.isTrue(await isClosable(context, 'error', 'notReceived', 'invoiced', 'approved'));
            assert.isTrue(await isClosable(context, 'error', 'notReceived', 'invoiced', 'changeRequested'));
            assert.isTrue(await isClosable(context, 'error', 'partiallyReceived', 'notInvoiced', 'confirmed'));
            assert.isTrue(await isClosable(context, 'error', 'partiallyReceived', 'notInvoiced', 'rejected'));
            assert.isTrue(await isClosable(context, 'error', 'partiallyReceived', 'notInvoiced', 'draft'));
            assert.isFalse(await isClosable(context, 'error', 'partiallyReceived', 'notInvoiced', 'pendingApproval'));
            assert.isTrue(await isClosable(context, 'error', 'partiallyReceived', 'notInvoiced', 'approved'));
            assert.isTrue(await isClosable(context, 'error', 'partiallyReceived', 'notInvoiced', 'changeRequested'));
            assert.isTrue(await isClosable(context, 'error', 'partiallyReceived', 'partiallyInvoiced', 'confirmed'));
            assert.isTrue(await isClosable(context, 'error', 'partiallyReceived', 'partiallyInvoiced', 'rejected'));
            assert.isTrue(await isClosable(context, 'error', 'partiallyReceived', 'partiallyInvoiced', 'draft'));
            assert.isFalse(
                await isClosable(context, 'error', 'partiallyReceived', 'partiallyInvoiced', 'pendingApproval'),
            );
            assert.isTrue(await isClosable(context, 'error', 'partiallyReceived', 'partiallyInvoiced', 'approved'));
            assert.isTrue(
                await isClosable(context, 'error', 'partiallyReceived', 'partiallyInvoiced', 'changeRequested'),
            );
            assert.isTrue(await isClosable(context, 'error', 'partiallyReceived', 'invoiced', 'confirmed'));
            assert.isTrue(await isClosable(context, 'error', 'partiallyReceived', 'invoiced', 'rejected'));
            assert.isTrue(await isClosable(context, 'error', 'partiallyReceived', 'invoiced', 'draft'));
            assert.isFalse(await isClosable(context, 'error', 'partiallyReceived', 'invoiced', 'pendingApproval'));
            assert.isTrue(await isClosable(context, 'error', 'partiallyReceived', 'invoiced', 'approved'));
            assert.isTrue(await isClosable(context, 'error', 'partiallyReceived', 'invoiced', 'changeRequested'));
            assert.isTrue(await isClosable(context, 'error', 'received', 'notInvoiced', 'confirmed'));
            assert.isTrue(await isClosable(context, 'error', 'received', 'notInvoiced', 'rejected'));
            assert.isTrue(await isClosable(context, 'error', 'received', 'notInvoiced', 'draft'));
            assert.isFalse(await isClosable(context, 'error', 'received', 'notInvoiced', 'pendingApproval'));
            assert.isTrue(await isClosable(context, 'error', 'received', 'notInvoiced', 'approved'));
            assert.isTrue(await isClosable(context, 'error', 'received', 'notInvoiced', 'changeRequested'));
            assert.isTrue(await isClosable(context, 'error', 'received', 'partiallyInvoiced', 'confirmed'));
            assert.isTrue(await isClosable(context, 'error', 'received', 'partiallyInvoiced', 'rejected'));
            assert.isTrue(await isClosable(context, 'error', 'received', 'partiallyInvoiced', 'draft'));
            assert.isFalse(await isClosable(context, 'error', 'received', 'partiallyInvoiced', 'pendingApproval'));
            assert.isTrue(await isClosable(context, 'error', 'received', 'partiallyInvoiced', 'approved'));
            assert.isTrue(await isClosable(context, 'error', 'received', 'partiallyInvoiced', 'changeRequested'));
            assert.isFalse(await isClosable(context, 'error', 'received', 'invoiced', 'confirmed'));
            assert.isFalse(await isClosable(context, 'error', 'received', 'invoiced', 'rejected'));
            assert.isFalse(await isClosable(context, 'error', 'received', 'invoiced', 'draft'));
            assert.isFalse(await isClosable(context, 'error', 'received', 'invoiced', 'pendingApproval'));
            assert.isFalse(await isClosable(context, 'error', 'received', 'invoiced', 'approved'));
            assert.isFalse(await isClosable(context, 'error', 'received', 'invoiced', 'changeRequested'));
        }));
});
