import { date, Test } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import { assert, expect } from 'chai';
import * as sinon from 'sinon';
import * as xtremPurchasing from '../../../lib/index';

describe('Purchase Invoice Lib', () => {
    let sandbox: sinon.SinonSandbox;

    beforeEach(() => {
        sandbox = sinon.createSandbox();
    });

    afterEach(() => {
        sandbox.restore();
    });

    describe('getLineInvoiceStatus', () => {
        it('should return "notInvoiced" when invoicedQuantityInStockUnit is 0', async () => {
            const line = {
                invoicedQuantityInStockUnit: Promise.resolve(0),
                quantityInStockUnit: Promise.resolve(10),
            } as xtremPurchasing.interfaces.InvoicedDocumentLine;

            const status = await xtremPurchasing.functions.PurchaseInvoiceLib.getLineInvoiceStatus(line);
            assert.equal(status, 'notInvoiced');
        });

        it('should return "partiallyInvoiced" when invoicedQuantityInStockUnit is less than quantityInStockUnit', async () => {
            const line = {
                invoicedQuantityInStockUnit: Promise.resolve(5),
                quantityInStockUnit: Promise.resolve(10),
            } as xtremPurchasing.interfaces.InvoicedDocumentLine;

            const status = await xtremPurchasing.functions.PurchaseInvoiceLib.getLineInvoiceStatus(line);
            assert.equal(status, 'partiallyInvoiced');
        });

        it('should return "invoiced" when invoicedQuantityInStockUnit is equal to or greater than quantityInStockUnit', async () => {
            const line = {
                invoicedQuantityInStockUnit: Promise.resolve(10),
                quantityInStockUnit: Promise.resolve(10),
            } as xtremPurchasing.interfaces.InvoicedDocumentLine;

            const status = await xtremPurchasing.functions.PurchaseInvoiceLib.getLineInvoiceStatus(line);
            assert.equal(status, 'invoiced');
        });
    });

    describe('getLineStatuses', () => {
        it('should return correct statuses for PurchaseReceiptLine', async () => {
            const documentLine = {
                invoicedQuantityInStockUnit: Promise.resolve(5),
                quantityInStockUnit: Promise.resolve(10),
                status: Promise.resolve('pending'),
                stockTransactionStatus: Promise.resolve('completed'),
                lineReturnStatus: Promise.resolve('none'),
                remainingQuantityInStockUnit: Promise.resolve(5),
                lineInvoiceStatus: Promise.resolve('notInvoiced'),
                purchaseReceiptLine: true,
            } as unknown as xtremPurchasing.nodes.PurchaseReceiptLine;

            sandbox
                .stub(xtremPurchasing.functions.PurchaseReceiptLib, 'computeReceiptLineStatus')
                .resolves('inProgress');

            const { invoiceStatus, lineStatus } =
                await xtremPurchasing.functions.PurchaseInvoiceLib.getLineStatuses(documentLine);
            assert.equal(invoiceStatus, 'partiallyInvoiced');
            assert.equal(lineStatus, 'inProgress');
        });

        it('should return "pending" status when invoicedQuantityInStockUnit is 0', async () => {
            const documentLine = {
                invoicedQuantityInStockUnit: Promise.resolve(0),
                quantityInStockUnit: Promise.resolve(10),
                status: Promise.resolve('pending'),
                lineInvoiceStatus: Promise.resolve('notInvoiced'),
            } as xtremPurchasing.interfaces.InvoicedDocumentLine;

            const { invoiceStatus, lineStatus } =
                await xtremPurchasing.functions.PurchaseInvoiceLib.getLineStatuses(documentLine);
            assert.equal(invoiceStatus, 'notInvoiced');
            assert.equal(lineStatus, 'pending');
        });

        it('should return "closed" status when invoicedQuantityInStockUnit is equal to or greater than quantityInStockUnit', async () => {
            const documentLine = {
                invoicedQuantityInStockUnit: Promise.resolve(10),
                quantityInStockUnit: Promise.resolve(10),
                status: Promise.resolve('pending'),
                lineInvoiceStatus: Promise.resolve('notInvoiced'),
            } as xtremPurchasing.interfaces.InvoicedDocumentLine;

            const { invoiceStatus, lineStatus } =
                await xtremPurchasing.functions.PurchaseInvoiceLib.getLineStatuses(documentLine);
            assert.equal(invoiceStatus, 'invoiced');
            assert.equal(lineStatus, 'closed');
        });

        it('should return "inProgress" status when invoicedQuantityInStockUnit is less than quantityInStockUnit', async () => {
            const documentLine = {
                invoicedQuantityInStockUnit: Promise.resolve(5),
                quantityInStockUnit: Promise.resolve(10),
                status: Promise.resolve('pending'),
                lineInvoiceStatus: Promise.resolve('notInvoiced'),
            } as xtremPurchasing.interfaces.InvoicedDocumentLine;

            const { invoiceStatus, lineStatus } =
                await xtremPurchasing.functions.PurchaseInvoiceLib.getLineStatuses(documentLine);
            assert.equal(invoiceStatus, 'partiallyInvoiced');
            assert.equal(lineStatus, 'inProgress');
        });
    });
    describe('initPurchaseCreditMemoCreateData', () => {
        it('should initialize memo data', () =>
            Test.withContext(async context => {
                const document = await context.read(xtremPurchasing.nodes.PurchaseInvoice, {
                    number: 'PI240004',
                });
                const reasonCode = await context.read(xtremMasterData.nodes.ReasonCode, { _id: '#Decrease quantity' });
                const totalAmountExcludingTax = 150.0;
                const supplierDocumentDate = date.parse('2025-04-20');

                const memoData = await xtremPurchasing.functions.PurchaseInvoiceLib.initPurchaseCreditMemoCreateData(
                    document,
                    reasonCode,
                    totalAmountExcludingTax,
                    supplierDocumentDate,
                );

                const parsedResponse = JSON.parse(JSON.stringify(memoData));
                expect(parsedResponse).to.have.all.keys(
                    'site',
                    'billBySupplier',
                    'currency',
                    'paymentTerm',
                    'payToSupplier',
                    'paymentTracking',
                    'creditMemoDate',
                    'isOverwriteNote',
                    'lines',
                    'reason',
                    'supplierDocumentDate',
                    'totalAmountExcludingTax',
                );
                expect(parsedResponse).to.deep.include({
                    reason: JSON.parse(JSON.stringify(reasonCode)),
                    totalAmountExcludingTax: `${totalAmountExcludingTax}`,
                    supplierDocumentDate: '2025-04-20',
                    isOverwriteNote: true,
                });
                expect(parsedResponse.lines[0]).to.have.all.keys(
                    'charge',
                    'discount',
                    'grossPrice',
                    'item',
                    'priceOrigin',
                    'purchaseInvoiceLine',
                    'quantity',
                    'recipientSite',
                    'site',
                    'stockSite',
                    'storedAttributes',
                    'storedDimensions',
                    'taxes',
                    'unit',
                    'unitToStockUnitConversionFactor',
                );
                expect(parsedResponse.lines[0].taxes[0]).to.have.all.keys(
                    'taxCategoryReference',
                    'taxReference',
                    'isSubjectToGlTaxExcludedAmount',
                    'isTaxMandatory',
                    'taxRate',
                    '_sortValue',
                );
            }));
    });
});
