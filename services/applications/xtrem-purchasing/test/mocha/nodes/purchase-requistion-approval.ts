import { date, Test } from '@sage/xtrem-core';
import * as xtremSystem from '@sage/xtrem-system';
import { assert } from 'chai';
import * as sinon from 'sinon';
import * as xtremPurchasing from '../../../lib';
import { checkStatus, createRequisition } from '../../fixtures/lib/functions/requisition';
import { getUser } from '../fixtures/mailer';

const sandbox = sinon.createSandbox();

describe('Purchase requisition node - process flow email approval vs confirmation', () => {
    beforeEach(() => sandbox.restore());
    const testDate = '2020-08-10';
    it('Create purchase requisition in approval flow, update site. Create purchase flow in new confirmation workflow', () =>
        Test.withContext(
            async context => {
                const requisition = await createRequisition(context);

                await checkStatus(context, { document: requisition, status: 'draft', approvalStatus: 'draft' });
                const rachel = await getUser(context, '<EMAIL>');
                const { mailerUser, ...dataForApproval } = await requisition.beforeSendApprovalRequestMail(
                    context,
                    rachel,
                );

                const urlDocument = '@sage/xtrem-purchasing/PurchaseRequisitionApproval';
                const page = '@sage/xtrem-purchasing/PurchaseRequisition';
                const jsonUrl = { _id: requisition._id.toString() };
                const jsonReject = { ...jsonUrl, action: 'rejected' };
                const jsonApprove = { ...jsonUrl, action: 'approved' };

                assert.deepEqual(dataForApproval, {
                    data: {
                        number: 'PQ200001',
                        receivingSite: 'Chem. Irvine',
                        requestDate: '2020-08-10',
                        requester: 'Test, Unit',
                        urlReject: `${urlDocument}/${btoa(JSON.stringify(jsonReject))}`,
                        urlApprove: `${urlDocument}/${btoa(JSON.stringify(jsonApprove))}`,
                        urlPurchaseDocument: `${page}/${btoa(JSON.stringify(jsonUrl))}`,
                    },
                    subject: '[Purchase requisition PQ200001] approval request',
                    template: 'purchase_requisition_approval_mail',
                });

                // send approval mail
                const isSend = await xtremPurchasing.nodes.PurchaseRequisition.sendApprovalRequestMail(
                    context,
                    requisition,
                    await getUser(context, '<EMAIL>'),
                );
                assert.isTrue(isSend);

                await checkStatus(context, {
                    document: requisition,
                    status: 'draft',
                    approvalStatus: 'pendingApproval',
                });

                // Approve PR
                assert.isTrue(await xtremPurchasing.nodes.PurchaseRequisition.approve(context, requisition, true));

                await checkStatus(context, {
                    document: requisition,
                    status: 'pending',
                    approvalStatus: 'approved',
                });
            },
            {
                mocks: ['axios'],
                scenario: 'create-purchase-requisition-in-approval-flow',
                directory: __dirname,
                today: testDate,
            },
        ));
    it('approve a requisition with a site that dont have approval workflow', () =>
        Test.withContext(async context => {
            const site = await context.read(xtremSystem.nodes.Site, { id: 'US002' }, { forUpdate: true });
            await site.$.set({ isLocationManaged: false, isPurchaseRequisitionApprovalManaged: false });
            await site.$.save();

            let requisition = await createRequisition(context);
            requisition = await xtremPurchasing.nodes.PurchaseRequisition.confirm(context, requisition);

            assert.equal(await requisition.approvalStatus, 'confirmed');

            await checkStatus(context, {
                document: requisition,
                status: 'pending',
                approvalStatus: 'confirmed',
            });
        }));

    it('Create purchase requisition in confirm workflow, control approvalStatus update to approval workflow', () =>
        Test.withContext(
            async context => {
                const site = await context.read(xtremSystem.nodes.Site, { id: 'US002' }, { forUpdate: true });

                // able to update site and set isPurchaseRequisitionApprovalManaged=false
                await site.$.set({ isLocationManaged: false, isPurchaseRequisitionApprovalManaged: false });
                await site.$.save();

                const requisition = await createRequisition(context);

                // check status to confirm PR is confirmed
                // check that approval status cannot be updated to approved - control
                await requisition.$.set({ approvalStatus: 'pendingApproval' });
                await assert.isRejected(requisition.$.save());
                assert.deepEqual(requisition.$.context.diagnoses, [
                    {
                        message: 'You cannot use this status if the approval workflow is disabled',
                        path: ['approvalStatus'],
                        severity: 3,
                    },
                ]);
            },
            { today: testDate },
        ));

    it('Create purchase requisition in approval workflow, control approvalStatus update to confirm workflow', () =>
        Test.withContext(
            async context => {
                const requisition = await createRequisition(context);

                const requisitionUpdate = await context.read(
                    xtremPurchasing.nodes.PurchaseRequisition,
                    { _id: requisition._id },
                    { forUpdate: true },
                );

                // check status to confirm PR is confirmed
                // check that approval status cannot be updated to confirmed - control
                await requisitionUpdate.$.set({ approvalStatus: 'confirmed' });
                await assert.isRejected(requisitionUpdate.$.save());
                assert.deepEqual(requisitionUpdate.$.context.diagnoses, [
                    {
                        message: 'You cannot use this status if the approval workflow is enabled',
                        path: ['approvalStatus'],
                        severity: 3,
                    },
                ]);
            },
            { today: testDate },
        ));

    it('Throw exception when confirmed status update and document not in draft or isPurchaseRequisitionApprovalManaged=false', () =>
        Test.withContext(async context => {
            const document = await context.read(xtremPurchasing.nodes.PurchaseRequisition, {
                number: 'REQ7',
            });
            await assert.isRejected(
                xtremPurchasing.nodes.PurchaseRequisition.confirm(context, document),
                'Document status needs to be "Draft" and approval workflow disabled.',
            );
        }));

    it('Create line on a purchase requisition with confirm approval status ', () =>
        Test.withContext(
            async context => {
                const site = await context.read(xtremSystem.nodes.Site, { _id: '#US003' }, { forUpdate: true });
                await site.$.set({ isPurchaseRequisitionApprovalManaged: false });
                await site.$.save();

                let newPurchaseRequisition = await context.create(xtremPurchasing.nodes.PurchaseRequisition, {
                    requester: '#<EMAIL>',
                    site: '#US003',
                    requestDate: date.make(2020, 8, 10),
                    lines: [{ item: '#Chemical D', quantity: 20, unit: '#GRAM' }],
                });
                await newPurchaseRequisition.$.trySave();
                assert.deepEqual(newPurchaseRequisition.$.context.diagnoses, []);

                const newPurchaseRequisitionId = newPurchaseRequisition._id;
                // rollbackCache function will flush deferred actions and saves
                await Test.rollbackCache(context);

                newPurchaseRequisition = await context.read(
                    xtremPurchasing.nodes.PurchaseRequisition,
                    {
                        _id: newPurchaseRequisitionId,
                    },
                    { forUpdate: true },
                );

                await xtremPurchasing.nodes.PurchaseRequisition.confirm(context, newPurchaseRequisition);
                const newPurchaseRequisitionLine = await context.create(xtremPurchasing.nodes.PurchaseRequisitionLine, {
                    item: '#Chemical D',
                    quantity: 20,
                    unit: '#GRAM',
                    document: newPurchaseRequisition,
                });
                await newPurchaseRequisitionLine.$.save();

                // Line status must be pending in this case
                assert.equal(await newPurchaseRequisitionLine.status, 'pending');
            },
            { today: testDate },
        ));
});
