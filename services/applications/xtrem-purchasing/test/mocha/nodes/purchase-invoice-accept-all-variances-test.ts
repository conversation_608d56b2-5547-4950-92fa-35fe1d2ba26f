import { Test } from '@sage/xtrem-core';
import { assert } from 'chai';
import * as xtremPurchasing from '../../../index';

describe('PurchaseInvoiceNode', () => {
    it('Accepts all variances, if it has a valid status', () =>
        Test.withContext(async context => {
            const purchaseInvoice = await context.read(
                xtremPurchasing.nodes.PurchaseInvoice,
                { number: 'PI240006' },
                { forUpdate: true },
            );
            assert.equal(
                await xtremPurchasing.nodes.PurchaseInvoice.acceptAllVariances(context, purchaseInvoice, true),
                true,
            );
        }));

    it("Doesn't fullfill acceptAllVariances request, if it doesn't have a valid status", () =>
        Test.withContext(async context => {
            const purchaseInvoice = await context.read(
                xtremPurchasing.nodes.PurchaseInvoice,
                { number: 'PI6' },
                { forUpdate: true },
            );
            await assert.isRejected(
                xtremPurchasing.nodes.PurchaseInvoice.acceptAllVariances(context, purchaseInvoice, true),
                'The record was not updated.',
            );
            assert.deepEqual(context.diagnoses, [
                {
                    message: 'You cannot update a document that was posted: document PI6.',
                    path: [],
                    severity: 3,
                },
                {
                    message: 'You cannot update a document that was posted: document PI6.',
                    path: ['lines', '2608'],
                    severity: 3,
                },
                {
                    message: 'You cannot update a document that was posted: document PI6.',
                    path: ['lines', '2609'],
                    severity: 3,
                },
                {
                    message: 'The item: NonStockManagedItem, is not managed for the supplier: LECLERC.',
                    path: ['lines', '2609'],
                    severity: 2,
                },
            ]);
        }));
});
