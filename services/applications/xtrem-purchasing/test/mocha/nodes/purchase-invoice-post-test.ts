import type { Context, NodeCreateData, NodeUpdateData, decimal } from '@sage/xtrem-core';
import { Test, asyncArray, date } from '@sage/xtrem-core';
import * as xtremFinanceData from '@sage/xtrem-finance-data';
import * as xtremLandedCost from '@sage/xtrem-landed-cost';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremStockData from '@sage/xtrem-stock-data';
import * as xtremSystem from '@sage/xtrem-system';
import { assert } from 'chai';
import * as sinon from 'sinon';
import * as xtremPurchasing from '../../../index';
import type { ValueCorrectionInterfaces } from '../../fixtures/lib/interfaces';
import { assertionStockCorrectionDetail } from '../fixtures/common';

const supplierId = '#US017';
const currencyId = '#EUR';
const siteId = '#US001';
const itemId = '#Aqua';
const unitId = '#LITER';
const locationId = '#LOC1|US001|Loading dock';
const statusId = '#A';
const owner = siteId.slice(1);

async function createAndPostInvoiceFromReceipt(
    context: Context,
    args: {
        purchaseReceiptNumber: string;
        unitPriceVariance: decimal;
        quantityInvoiced?: decimal;
        quantityInvoicedInStockUnit?: decimal;
        landedCostAllocationData?: {
            landedCostId: string;
            linesToBeAllocated: xtremLandedCost.interfaces.LandedCostAllocatedDocumentLine<
                | xtremLandedCost.enums.LandedCostDocumentTypeEnum.transaction
                | xtremLandedCost.enums.LandedCostDocumentTypeEnum.order
            >[];
        }[];
    },
): Promise<ValueCorrectionInterfaces.PostInvoiceResult> {
    const firstInvoiceLineId = await context.runInWritableContext(async childContext => {
        const receipt = await childContext.read(xtremPurchasing.nodes.PurchaseReceipt, {
            number: args.purchaseReceiptNumber,
        });
        // 1-Create the invoice against the receipt
        const createResult = await xtremPurchasing.nodes.PurchaseReceipt.createPurchaseInvoice(childContext, receipt);
        await childContext.flushDeferredActions();
        assert.isArray(createResult);

        const _invoiceLine = await (
            await childContext.read(xtremPurchasing.nodes.PurchaseReceiptLineToPurchaseInvoiceLine, {
                purchaseReceiptLine: (await receipt.lines.elementAt(0))._id,
            })
        ).purchaseInvoiceLine;

        const invoice = await childContext.read(
            xtremPurchasing.nodes.PurchaseInvoice,
            {
                _id: (await _invoiceLine.document)._id,
            },
            { forUpdate: true },
        );

        if (args.landedCostAllocationData?.length) {
            const sourceCurrency = await invoice.currency;
            const destinationCurrency = await (await invoice.site).currency;

            await invoice.$.set({
                lines: await asyncArray(args.landedCostAllocationData)
                    .map(async allocationData => {
                        const roundedValueGenerator = new xtremMasterData.sharedFunctions.RoundedValueGenerator(
                            63,
                            await asyncArray(allocationData.linesToBeAllocated)
                                .map(line => line.quantityInStockUnitForLandedCostAllocation)
                                .toArray(),
                            2,
                        );

                        const costAmounts = roundedValueGenerator.getValues();

                        return {
                            _action: 'create' as NodeUpdateData<xtremPurchasing.nodes.PurchaseInvoiceLine>['_action'],
                            item: allocationData.landedCostId,
                            quantity: 7,
                            grossPrice: 9,
                            landedCost: {
                                allocationMethod: 'manual' as xtremLandedCost.enums.LandedCostAllocationMethod,
                                allocations: await asyncArray(allocationData.linesToBeAllocated)
                                    .map(async (line, index) => ({
                                        costAmount: costAmounts[index],
                                        costAmountInCompanyCurrency: await xtremMasterData.functions.convertCurrency(
                                            childContext,
                                            costAmounts[index],
                                            sourceCurrency,
                                            destinationCurrency,
                                            date.today(),
                                        ),
                                        allocatedDocumentLine: line,
                                        allocatedDocumentType:
                                            line instanceof xtremPurchasing.nodes.PurchaseReceiptLine
                                                ? 'purchaseReceipt'
                                                : 'purchaseOrder',
                                    }))
                                    .toArray(),
                            },
                        };
                    })
                    .toArray(),
            });
            await invoice.$.save();
        }

        let totalAmountExcludingTax: decimal = 0;

        // 2-Change the unit price in the invoice and approve the variance
        await invoice.$.set({
            lines: await invoice.lines
                .map(async line => {
                    const newUnitPrice =
                        (await line.grossPrice) +
                        ((await (await line.item).type) === 'landedCost' ? 0 : args.unitPriceVariance);
                    const quantityInvoiced = args.quantityInvoiced ?? (await line.quantity);
                    totalAmountExcludingTax += quantityInvoiced * newUnitPrice;

                    return {
                        _action: 'update',
                        _id: line._id,
                        grossPrice: newUnitPrice,
                        netPrice: newUnitPrice,
                        quantity: quantityInvoiced,
                        quantityInStockUnit: args.quantityInvoicedInStockUnit ?? (await line.quantityInStockUnit),
                        matchingStatus: 'varianceApproved',
                    } as NodeUpdateData<xtremPurchasing.nodes.PurchaseInvoiceLine>;
                })
                .toArray(),
            totalAmountExcludingTax,
            matchingStatus: 'varianceApproved',
        });

        await invoice.$.save();

        return _invoiceLine._id;
    });

    let invoice = await context
        .query(xtremPurchasing.nodes.PurchaseInvoice, {
            filter: { lines: { _atLeast: 1, _id: firstInvoiceLineId } },
            first: 1,
            forUpdate: true,
        })
        .elementAt(0);

    const invoiceId = invoice._id;

    // 3-Post the invoice
    const postResult = await xtremPurchasing.nodes.PurchaseInvoice.post(context, invoice);

    await Test.rollbackCache(context);
    invoice = await context.read(xtremPurchasing.nodes.PurchaseInvoice, { _id: invoiceId });

    return {
        invoiceLine: await invoice.lines.elementAt(0),
        postResult,
    };
}

function getStockDetailCreateData() {
    return {
        site: siteId,
        item: itemId,
        location: locationId,
        status: statusId,
        stockUnit: unitId,
        quantityInStockUnit: 150,
        owner,
    };
}

async function createOriginDocument<T extends 'order' | 'receipt'>(
    context: Context,
    originDocumentType: T,
): Promise<T extends 'order' ? xtremPurchasing.nodes.PurchaseOrder : xtremPurchasing.nodes.PurchaseReceipt>;
async function createOriginDocument(
    context: Context,
    originDocumentType: 'order' | 'receipt',
): Promise<xtremPurchasing.nodes.PurchaseOrder | xtremPurchasing.nodes.PurchaseReceipt> {
    const originDocumentCreateData:
        | NodeCreateData<xtremPurchasing.nodes.PurchaseOrder>
        | NodeCreateData<xtremPurchasing.nodes.PurchaseReceipt> = {
        site: siteId,
        businessRelation: supplierId,
        currency: currencyId,
        status: 'pending',
        lines: [
            {
                item: itemId,
                status: 'pending',
                quantity: 150,
                unit: unitId,
                grossPrice: 100,
            },
        ],
    };

    let newOriginDocument: xtremPurchasing.nodes.PurchaseOrder | xtremPurchasing.nodes.PurchaseReceipt;

    if (originDocumentType === 'order') {
        const orderCreateData = originDocumentCreateData as NodeCreateData<xtremPurchasing.nodes.PurchaseOrder>;
        orderCreateData.orderDate = date.make(2020, 8, 10);

        newOriginDocument = await context.create(xtremPurchasing.nodes.PurchaseOrder, orderCreateData);
    } else {
        const receiptCreateData = originDocumentCreateData as NodeCreateData<xtremPurchasing.nodes.PurchaseReceipt>;
        receiptCreateData.date = date.make(2020, 8, 10);
        receiptCreateData.lines![0].stockDetails = [getStockDetailCreateData()];
        receiptCreateData.lines![0].stockTransactionStatus = 'completed';

        newOriginDocument = await context.create(xtremPurchasing.nodes.PurchaseReceipt, receiptCreateData);
    }

    await newOriginDocument.$.save({ flushDeferredActions: true });

    return newOriginDocument;
}

describe('PurchaseInvoiceNode', () => {
    afterEach(() => sinon.restore());

    function getStockAndFinanceNotify(notifySpy: any) {
        return notifySpy.getCalls().filter((notifyCall: any) => {
            if (/Stock.*start/.test(notifyCall.args[0].toString())) return true;
            if (notifyCall.args[0].toString() === 'accountingInterface') return true;
            return false;
        });
    }

    it('Post purchase invoice linked to a purchase receipt, without variance', () =>
        Test.withContext(
            async (context: Context) => {
                const unitPriceVariance = 0;
                const { postResult } = await createAndPostInvoiceFromReceipt(context, {
                    purchaseReceiptNumber: 'PR41',
                    unitPriceVariance,
                });

                assert.deepEqual(postResult.wasSuccessful, true);

                // As there is no variance on the single invoice line, there is no stock posting
                assert.deepEqual(postResult.stockPostingResult, '');
            },
            { today: '2021-11-17' },
        ));

    it('Post purchase invoice linked to a purchase receipt, with variance -> creation of StockCorrectionDetail', () =>
        Test.withContext(
            async (context: Context) => {
                const unitPriceVariance = 15.9;
                const { postResult, invoiceLine } = await createAndPostInvoiceFromReceipt(context, {
                    purchaseReceiptNumber: 'PR41',
                    unitPriceVariance,
                    quantityInvoiced: 5,
                    quantityInvoicedInStockUnit: 5,
                });
                const purchaseReceiptLine = await (await invoiceLine.purchaseReceiptLine)?.purchaseReceiptLine;
                assert.isDefined(purchaseReceiptLine);
                if (!purchaseReceiptLine) return; // type checking

                assert.deepEqual(postResult.wasSuccessful, true);

                await assertionStockCorrectionDetail(
                    context,
                    {
                        filter: {
                            documentLine: purchaseReceiptLine._id,
                        },
                        orderBy: { correctedStockDetail: { _id: 1 }, _id: 1 },
                    },
                    [
                        {
                            documentLine: { _id: purchaseReceiptLine._id },
                            _documentLine_constructor: 'PurchaseReceiptLine',
                            reasonCode: { id: 'Stock value correction' },
                            amountToAbsorb: 111.3, // unitPriceVariance * receipt quantity = 15.9 * 7
                            nonAbsorbedAmount: 0.0,
                            // XT-46359: impactedQuantity should be the quantity of the receipt line
                            impactedQuantity: await purchaseReceiptLine.quantityInStockUnit,
                        },
                    ],
                );
            },
            { today: '2021-11-17' },
        ));

    it('Post purchase invoice linked to a purchase receipt, with item not stock managed', () =>
        Test.withContext(
            async context => {
                const unitPriceVariance = 1.5;
                const { invoiceLine, postResult } = await createAndPostInvoiceFromReceipt(context, {
                    purchaseReceiptNumber: 'PR40',
                    unitPriceVariance,
                });

                assert.deepEqual(postResult.wasSuccessful, true);

                assert.equal(postResult.stockPostingResult.length, 0);
                assert.equal(await invoiceLine.stockTransactionStatus, 'completed');
            },
            { today: '2022-08-02' },
        ));

    it('Post purchase invoice linked to a purchase receipt, with landed-cost', () =>
        Test.withContext(
            async (context: Context) => {
                const receipt = await createOriginDocument(context, 'receipt');

                const landedCostId = '#LandedCost002';
                const firstReceiptLine = await receipt.lines.elementAt(0);
                const receiptId = receipt._id;
                const firstReceiptLineId = firstReceiptLine._id;

                const { postResult, invoiceLine } = await createAndPostInvoiceFromReceipt(context, {
                    purchaseReceiptNumber: await receipt.number,
                    unitPriceVariance: 0,
                    landedCostAllocationData: [
                        {
                            landedCostId,
                            linesToBeAllocated: [firstReceiptLine],
                        },
                    ],
                });
                assert.deepEqual(postResult.wasSuccessful, true);

                const landedCostInvoiceLine = await (
                    await invoiceLine.document
                ).lines.find(async line => {
                    return (await (await line.item).id) === landedCostId.slice(1);
                });
                assert.isDefined(landedCostInvoiceLine);
                if (!landedCostInvoiceLine) return; // type checking
                const landedCostInvoiceLineId = landedCostInvoiceLine._id;

                // rollbackCache function will flush deferred actions and saves
                await Test.rollbackCache(context);

                await xtremStockData.functions.testLib.simulateStockTransaction(
                    context,
                    receiptId,
                    xtremPurchasing.nodes.PurchaseReceipt,
                    'correction',
                    xtremPurchasing.nodes.PurchaseInvoice.onStockReply,
                    { stockUpdateResultStatus: 'corrected', originLineIds: [landedCostInvoiceLineId] },
                    () => Promise.resolve(true), // post both lines: 'Aqua' and 'LandedCost002'
                );

                const landedCostLine = await context
                    .query(xtremLandedCost.nodes.LandedCostLine, {
                        filter: {
                            landedCost: landedCostId,
                            documentLine: firstReceiptLineId,
                        },
                        first: 1,
                    })
                    .elementAt(0);

                assert.equal((await landedCostLine.actualCostAmountInCompanyCurrency).valueOf(), 63.13);
                assert.equal(await landedCostLine.actualAllocatedCostAmountInCompanyCurrency, 0);
            },
            { testActiveServiceOptions: [xtremMasterData.serviceOptions.landedCostOption], today: '2021-11-17' },
        ));

    it('Post purchase invoice linked to a purchase receipt, with landed-cost and simulate error', () =>
        Test.withContext(
            async (context: Context) => {
                // 1- Create a receipt with a single line
                // 2- Create and post an invoice for the receipt + another line with landed-cost allocated to the receipt line
                // 3- Simulate an error on the stock service reply
                // 4- Change the stock transaction status of the receipt line to 'completed' in order to simulate that the post has been done correctly on the receipt side
                // 5- Post the invoice again and check that the stock service is not called again but the stock transaction status is updated to 'completed' on the invoice line
                const receipt = await createOriginDocument(context, 'receipt');

                const landedCostId = '#LandedCost002';
                const firstReceiptLine = await receipt.lines.elementAt(0);
                const receiptId = receipt._id;
                const firstReceiptLineId = firstReceiptLine._id;

                const { postResult, invoiceLine } = await createAndPostInvoiceFromReceipt(context, {
                    purchaseReceiptNumber: await receipt.number,
                    unitPriceVariance: 0,
                    landedCostAllocationData: [
                        {
                            landedCostId,
                            linesToBeAllocated: [firstReceiptLine],
                        },
                    ],
                });

                assert.deepEqual(postResult.wasSuccessful, true);
                const stockPostingResult = JSON.parse(postResult.stockPostingResult);
                delete stockPostingResult.notificationId;
                assert.deepEqual(stockPostingResult, {
                    result: 'requested',
                    documents: [{ id: receiptId, lines: [{ id: firstReceiptLineId, sortValue: 10 }] }],
                });

                const invoiceId = (await invoiceLine.document)._id;
                // rollbackCache function will flush deferred actions and saves
                await Test.rollbackCache(context);

                // The landed-cost lines are created before the notification to the stock service
                const landedCosts = await context
                    .query(xtremLandedCost.nodes.LandedCostLine, {
                        filter: {
                            landedCost: landedCostId,
                            documentLine: firstReceiptLineId,
                        },
                        first: 1,
                    })
                    .toArray();

                assert.equal(landedCosts.length, 1);
                assert.equal((await landedCosts[0].actualCostAmountInCompanyCurrency).valueOf(), 63.13);
                assert.equal(await landedCosts[0].actualAllocatedCostAmountInCompanyCurrency, 0);

                // Before the reply of stock service, the not managed items should be considered as completed
                let invoice = await context.read(xtremPurchasing.nodes.PurchaseInvoice, { _id: invoiceId });

                const invoiceLineOk = await invoice.lines.find(
                    async line => (await (await line.item).id) === itemId.slice(1),
                );
                assert.isDefined(invoiceLineOk);
                if (!invoiceLineOk) return; // type checking

                assert.deepEqual(await invoiceLineOk.stockTransactionStatus, 'completed');

                // select the line on the landed cost to simulate an error on stock service side
                let invoiceLineInError = await invoice.lines.find(
                    async line => (await (await line.item).id) === landedCostId.slice(1),
                );
                assert.isDefined(invoiceLineInError);
                if (!invoiceLineInError) return; // type checking
                const invoiceLineInErrorId = invoiceLineInError._id;

                assert.deepEqual(await invoiceLineInError.stockTransactionStatus, 'inProgress');

                // Then we can test the reply in error
                await xtremStockData.functions.testLib.simulateStockTransaction(
                    context,
                    receiptId,
                    xtremPurchasing.nodes.PurchaseReceipt,
                    'correction',
                    xtremPurchasing.nodes.PurchaseInvoice.onStockReply,
                    {
                        stockTransactionStatus: 'error',
                        errorMessage: 'Simulated error',
                        originLineIds: [invoiceLineInErrorId],
                    },
                );

                await Test.rollbackCache(context);

                invoiceLineInError = await context.read(xtremPurchasing.nodes.PurchaseInvoiceLine, {
                    _id: invoiceLineInErrorId,
                });
                assert.deepEqual(await invoiceLineInError.stockTransactionStatus, 'error');

                const receipt1 = await context.read(
                    xtremPurchasing.nodes.PurchaseReceipt,
                    { _id: receiptId },
                    { forUpdate: true },
                );

                const receiptLineInError = await receipt1.lines.takeOne(line => line._id === firstReceiptLineId);
                assert.isNotNull(receiptLineInError);
                assert.deepEqual(await receiptLineInError.stockTransactionStatus, 'error');

                // simulate that the receipt has been posted directly without error
                await receipt1.$.set({
                    forceUpdateForStock: true,
                    lines: [{ _action: 'update', _id: firstReceiptLineId, stockTransactionStatus: 'completed' }],
                });
                await receipt1.$.save();

                const [
                    { mock: stockCorrectionRequestNotificationSpy },
                    { mock: manageStockReplySpy },
                    { mock: manageLinesWithoutStockUpdateSpy },
                ] = xtremSystem.TestHelpers.Sinon.registerMocks([
                    {
                        type: 'spy',
                        reference: xtremStockData.functions.notificationLib,
                        name: 'stockCorrectionRequestNotification',
                    },
                    {
                        type: 'spy',
                        isMethod: true,
                        isStatic: true,
                        class: xtremPurchasing.nodes.PurchaseInvoice,
                        name: 'manageStockReply',
                    },
                    {
                        type: 'spy',
                        isMethod: true,
                        isStatic: true,
                        class: xtremPurchasing.nodes.PurchaseInvoice,
                        name: 'manageLinesWithoutStockUpdate',
                    },
                ]);

                invoice = await context.read(xtremPurchasing.nodes.PurchaseInvoice, { _id: invoiceId });
                const postResult2 = await xtremPurchasing.nodes.PurchaseInvoice.post(context, invoice);
                assert.deepEqual(postResult2, {
                    wasSuccessful: true,
                    message: 'The purchase invoice posted.',
                    stockPostingResult: '',
                    validationMessages: [],
                });
                assert.equal(await (await (await invoice.paymentTracking)?.document)?.number, await invoice.number);

                // The stock service should not be called again
                sinon.assert.notCalled(stockCorrectionRequestNotificationSpy);

                // The stock transaction status should become 'completed' on the invoice line
                // because the linked receipt has been posted correctly
                const invoiceLineNoMoreInError = await context.read(xtremPurchasing.nodes.PurchaseInvoiceLine, {
                    _id: invoiceLineInErrorId,
                });
                assert.deepEqual(await invoiceLineNoMoreInError.stockTransactionStatus, 'completed');

                const lineResults = [
                    {
                        id: invoiceLineNoMoreInError._id,
                        sortValue: await invoiceLineNoMoreInError._sortValue,
                        stockUpdateResultStatus: 'none' as xtremStockData.enums.StockUpdateResultAction,
                        originLineIds: [],
                        stockJournalRecords: [],
                    },
                ];
                // The function manageStockReply should have been called by considering the invoice line doesn't need to be sent to stock service
                sinon.assert.calledOnce(manageStockReplySpy);
                const manageStockReplyParam1 = manageStockReplySpy.getCall(0).args[1] as Parameters<
                    typeof xtremPurchasing.nodes.PurchaseInvoice.manageStockReply
                >[1];
                assert.deepEqual(manageStockReplyParam1, {
                    requestNotificationId: 'none',
                    movementHasBeenSkipped: true,
                    updateResults: { correction: { documents: [{ id: invoice._id, lines: lineResults }] } },
                });

                // The function manageLinesWithoutStockUpdate should return the line _id
                sinon.assert.calledOnce(manageLinesWithoutStockUpdateSpy);
                const manageLinesWithoutStockUpdateParam1 = await (manageLinesWithoutStockUpdateSpy.getCall(0)
                    .returnValue as ReturnType<
                    typeof xtremPurchasing.nodes.PurchaseInvoice.manageLinesWithoutStockUpdate
                >);
                assert.deepEqual(manageLinesWithoutStockUpdateParam1, [invoiceLineNoMoreInError._id]);

                xtremSystem.TestHelpers.Sinon.removeMocks();
            },
            { testActiveServiceOptions: [xtremMasterData.serviceOptions.landedCostOption] },
        ));

    it('Post purchase invoice linked to a purchase receipt linked to a purchase order, with landed-cost', () =>
        Test.withContext(
            async (context: Context) => {
                const notifySpy = sinon.spy(context, 'notify');

                const order = await createOriginDocument(context, 'order');
                const firstOrderLineId = (await order.lines.elementAt(0))._id;

                const orderNumber = await order.number;
                assert.equal(orderNumber, 'PO200001');

                const receipts = await xtremPurchasing.nodes.PurchaseOrder.createPurchaseReceipt(context, order);
                await context.flushDeferredActions();

                assert.isArray(receipts);
                assert.lengthOf(receipts, 1);

                const receipt = await context.read(
                    xtremPurchasing.nodes.PurchaseReceipt,
                    { number: await receipts.at(0)?.number },
                    { forUpdate: true },
                );

                let firstReceiptLine = await receipt.lines.elementAt(0);
                const firstReceiptLineId = firstReceiptLine._id;

                await receipt.$.set({
                    status: 'pending',
                    lines: [
                        {
                            _action: 'update',
                            _id: firstReceiptLineId,
                            status: 'pending',
                            stockDetails: [getStockDetailCreateData()],
                            stockTransactionStatus: 'completed',
                        },
                    ],
                });

                await receipt.$.save();
                const receiptId = receipt._id;

                const landedCostId = '#LandedCost002';

                const { postResult, invoiceLine } = await createAndPostInvoiceFromReceipt(context, {
                    purchaseReceiptNumber: await receipt.number,
                    unitPriceVariance: 0,
                    landedCostAllocationData: [
                        {
                            landedCostId,
                            linesToBeAllocated: [firstReceiptLine],
                        },
                    ],
                });

                assert.deepEqual(postResult.wasSuccessful, true);

                const invoiceId = (await invoiceLine.document)._id;
                const toInvoiceLines = await (await invoiceLine.document).lines.toArray();
                const landedCostInvoiceLine = await (
                    await invoiceLine.document
                ).lines.find(async line => {
                    return (await (await line.item).id) === landedCostId.slice(1);
                });
                assert.isDefined(landedCostInvoiceLine);
                if (!landedCostInvoiceLine) return; // type checking
                const landedCostInvoiceLineId = landedCostInvoiceLine._id;

                // rollbackCache function will flush deferred actions and saves
                await Test.rollbackCache(context);

                const purchaseInvoice = await context.read(xtremPurchasing.nodes.PurchaseInvoice, {
                    _id: invoiceId,
                });
                firstReceiptLine = await context.read(xtremPurchasing.nodes.PurchaseReceiptLine, {
                    _id: firstReceiptLineId,
                });
                const purchaseInvoiceNumber = await purchaseInvoice.number;
                const receiptNumber = await firstReceiptLine.documentNumber;
                const stockJournal = await context.create(xtremStockData.nodes.StockJournal, {
                    item: (await firstReceiptLine.item)._id,
                    site: (await firstReceiptLine.site)._id,
                    activeQuantityInStockUnit: 0,
                    currency: (await purchaseInvoice.currency)._id,
                    effectiveDate: await purchaseInvoice.documentDate,
                    movementAmount: 10,
                    quantityInStockUnit: 0,
                    owner: 'US001',
                    documentLine: firstReceiptLine._id,
                    sourceDocumentLine: (await purchaseInvoice.lines.elementAt(0))._id,
                    stockUnit: (await firstReceiptLine.stockUnit)._id,
                    reasonCode: 4,
                });
                await stockJournal.$.save();

                await xtremStockData.functions.testLib.simulateStockTransaction(
                    context,
                    receiptId,
                    xtremPurchasing.nodes.PurchaseReceipt,
                    'correction',
                    xtremPurchasing.nodes.PurchaseInvoice.onStockReply,
                    { stockUpdateResultStatus: 'corrected', originLineIds: [landedCostInvoiceLineId] },
                    () => Promise.resolve(true), // post both lines: 'Aqua' and 'LandedCost002'
                );

                const receiptLandedCostLine = await context
                    .query(xtremLandedCost.nodes.LandedCostLine, {
                        filter: {
                            landedCost: landedCostId,
                            documentLine: firstReceiptLineId,
                        },
                        first: 1,
                    })
                    .elementAt(0);

                assert.equal((await receiptLandedCostLine.actualCostAmountInCompanyCurrency).valueOf(), 63.13);
                assert.equal(await receiptLandedCostLine.actualAllocatedCostAmountInCompanyCurrency, 0);

                const orderLandedCostLine = await context
                    .query(xtremLandedCost.nodes.LandedCostLine, {
                        filter: {
                            landedCost: landedCostId,
                            documentLine: firstOrderLineId,
                        },
                        first: 1,
                    })
                    .elementAt(0);

                assert.equal((await orderLandedCostLine.actualCostAmountInCompanyCurrency).valueOf(), 63.13);
                assert.equal((await orderLandedCostLine.actualAllocatedCostAmountInCompanyCurrency).valueOf(), 63.13);
                assert.equal(
                    await (
                        await (
                            await purchaseInvoice.paymentTracking
                        )?.document
                    )?.number,
                    purchaseInvoiceNumber,
                );

                // Just check notifications for Stock and Finance
                const notifyCalls = getStockAndFinanceNotify(notifySpy);

                // Check notifications
                assert.equal(notifyCalls.length, 1); // 1 for stock, no finance notification anymore

                // Check stock notification
                assert.deepEqual(notifyCalls[0].args[0], 'Stock/correct/start');
                assert.deepEqual(notifyCalls[0].args[1], {
                    payload: {
                        replyTopic: 'PurchaseInvoice/stock/correction/reply',
                        stockUpdateParameters: {
                            movementType: 'correction',
                        },
                        documents: [
                            {
                                id: await firstReceiptLine.documentId,
                                lines: [
                                    {
                                        id: firstReceiptLine._id,
                                        sortValue: 10,
                                    },
                                ],
                            },
                        ],
                    },
                });

                const documentFilter = {
                    documentNumber: purchaseInvoiceNumber,
                    documentType: 'purchaseInvoice',
                };

                const accountingStagingRecords = await context
                    .query(xtremFinanceData.nodes.AccountingStaging, { filter: { ...documentFilter } })
                    .toArray();

                assert.equal(accountingStagingRecords.length, 3);
                const accountingStagingRecord1 = accountingStagingRecords.at(0);
                const accountingStagingRecord1Amount = await accountingStagingRecord1?.amounts.at(0);
                const accountingStagingRecord2 = accountingStagingRecords.at(1);
                const accountingStagingRecord2Amount = await accountingStagingRecord2?.amounts.at(0);
                const accountingStagingRecord3 = accountingStagingRecords.at(2);
                const accountingStagingRecord3Amount = await accountingStagingRecord3?.amounts.at(0);

                assert.equal(await accountingStagingRecord1?.documentSysId, purchaseInvoice._id);
                assert.equal(
                    (await accountingStagingRecord1?.financialSite)?._id,
                    (await purchaseInvoice.financialSite)._id,
                );
                assert.equal(await accountingStagingRecord1?.documentNumber, purchaseInvoiceNumber);

                assert.equal((await accountingStagingRecord1?.documentDate)?.toString(), '2021-11-17');
                assert.equal(
                    (await accountingStagingRecord1?.transactionCurrency)?._id,
                    (await purchaseInvoice.transactionCurrency)._id,
                );
                assert.equal(await accountingStagingRecord1?.documentType, 'purchaseInvoice');
                assert.equal(await accountingStagingRecord1?.targetDocumentType, 'accountsPayableInvoice');
                assert.equal(
                    (await accountingStagingRecord1?.paymentTerm)?._id,
                    (await purchaseInvoice.paymentTerm)._id,
                );
                assert.equal((await accountingStagingRecord1?.dueDate)?.toString(), '2021-12-17');
                assert.equal((await accountingStagingRecord1?.supplierDocumentDate)?.toString(), '2021-11-17');
                assert.equal(await accountingStagingRecord1?.supplierDocumentNumber, '');
                assert.equal((await accountingStagingRecord1?.baseDocumentLine)?._id, toInvoiceLines[0]._id);
                assert.equal(await accountingStagingRecord1?.movementType, 'document');
                assert.equal(await accountingStagingRecord1?.sourceDocumentNumber, receiptNumber);
                assert.equal((await accountingStagingRecord1?.fxRateDate)?.toString(), '2021-11-17');
                assert.equal((await accountingStagingRecord1?.companyFxRate)?.toString(), '1.002');
                assert.equal(await accountingStagingRecord1?.companyFxRateDivisor, 1);
                assert.equal((await accountingStagingRecord1?.item)?._id, (await firstReceiptLine.item)._id);
                assert.equal((await accountingStagingRecord1?.supplier)?._id, (await purchaseInvoice.supplier)._id);
                assert.equal(
                    (await accountingStagingRecord1?.payToSupplier)?._id,
                    (await purchaseInvoice.supplier)._id,
                );
                assert.equal(
                    (await accountingStagingRecord1?.payToSupplierLinkedAddress)?._id,
                    (await purchaseInvoice.payToLinkedAddress)?._id,
                );
                assert.equal(
                    (await accountingStagingRecord1?.returnLinkedAddress)?._id,
                    (await purchaseInvoice.returnLinkedAddress)?._id,
                );
                assert.equal(
                    (await accountingStagingRecord1?.recipientSite)?._id,
                    (await toInvoiceLines[0].recipientSite)._id,
                );

                assert.equal(
                    JSON.stringify(
                        await accountingStagingRecord1?.taxes
                            .map(async tax => ({ baseTaxSysId: (await tax.baseTax)._id }))
                            .toArray(),
                    ),
                    JSON.stringify(await toInvoiceLines[0].taxes.map(tax => ({ baseTaxSysId: tax._id })).toArray()),
                );
                assert.equal((await accountingStagingRecord1?.taxDate)?.toString(), '2021-11-17');
                assert.equal(
                    JSON.stringify(await accountingStagingRecord1?.storedDimensions),
                    JSON.stringify({
                        dimensionType01: '300',
                        dimensionType02: 'CHANNELVALUE1',
                    }),
                );
                assert.equal(
                    JSON.stringify(await accountingStagingRecord1?.storedAttributes),
                    JSON.stringify({
                        item: 'Aqua',
                        task: 'Task1',
                        project: 'AttPROJ',
                        supplier: 'US017',
                        stockSite: 'US001',
                        businessSite: 'US001',
                        financialSite: 'US001',
                    }),
                );

                assert.equal(await accountingStagingRecord1Amount?.amountType, 'amountExcludingTax');
                assert.equal(await accountingStagingRecord1Amount?.amount, 15000);
                assert.equal(await accountingStagingRecord1Amount?.documentLineType, 'documentLine');

                // Check 2nd accounting staging record, still for the ap invoice

                assert.equal(
                    await accountingStagingRecord2?.documentSysId,
                    await accountingStagingRecord1?.documentSysId,
                );
                assert.equal(
                    (await accountingStagingRecord2?.financialSite)?._id,
                    (await accountingStagingRecord1?.financialSite)?._id,
                );
                assert.equal(
                    await accountingStagingRecord2?.documentNumber,
                    await accountingStagingRecord1?.documentNumber,
                );
                assert.equal(
                    (await accountingStagingRecord2?.documentDate)?.toString(),
                    (await accountingStagingRecord1?.documentDate)?.toString(),
                );
                assert.equal(
                    (await accountingStagingRecord2?.transactionCurrency)?._id,
                    (await accountingStagingRecord1?.transactionCurrency)?._id,
                );
                assert.equal(await accountingStagingRecord2?.batchSize, await accountingStagingRecord1?.batchSize);
                assert.equal(
                    await accountingStagingRecord2?.documentType,
                    await accountingStagingRecord1?.documentType,
                );
                assert.equal(
                    await accountingStagingRecord2?.targetDocumentType,
                    await accountingStagingRecord1?.targetDocumentType,
                );
                assert.equal((await accountingStagingRecord2?.baseDocumentLine)?._id, toInvoiceLines[1]._id);
                assert.equal(
                    await accountingStagingRecord2?.movementType,
                    await accountingStagingRecord1?.movementType,
                );
                assert.equal(
                    await accountingStagingRecord2?.documentNumber,
                    await accountingStagingRecord1?.documentNumber,
                );
                assert.equal(await accountingStagingRecord2?.sourceDocumentNumber, '');
                assert.equal(
                    (await accountingStagingRecord2?.companyFxRate)?.toString(),
                    (await accountingStagingRecord1?.companyFxRate)?.toString(),
                );
                assert.equal(
                    (await accountingStagingRecord2?.companyFxRateDivisor)?.toString(),
                    (await accountingStagingRecord1?.companyFxRateDivisor)?.toString(),
                );
                assert.equal(
                    (await accountingStagingRecord2?.fxRateDate)?.toString(),
                    (await accountingStagingRecord1?.fxRateDate)?.toString(),
                );
                assert.equal((await accountingStagingRecord2?.item)?._id, (await toInvoiceLines[1].item)._id);
                assert.equal(
                    (await accountingStagingRecord2?.supplier)?._id,
                    (await accountingStagingRecord1?.supplier)?._id,
                );
                assert.equal(
                    (await accountingStagingRecord1?.payToSupplier)?._id,
                    (await accountingStagingRecord2?.payToSupplier)?._id,
                );
                assert.equal(
                    (await accountingStagingRecord2?.payToSupplierLinkedAddress)?._id,
                    (await accountingStagingRecord1?.payToSupplierLinkedAddress)?._id,
                );
                assert.equal(
                    (await accountingStagingRecord2?.returnLinkedAddress)?._id,
                    (await accountingStagingRecord1?.returnLinkedAddress)?._id,
                );
                assert.equal(
                    (await accountingStagingRecord2?.recipientSite)?._id,
                    (await accountingStagingRecord1?.recipientSite)?._id,
                );
                assert.equal(await accountingStagingRecord2Amount?.amountType, 'amountExcludingTax');
                assert.equal(await accountingStagingRecord2Amount?.amount, 63);
                assert.equal(await accountingStagingRecord2Amount?.documentLineType, 'documentLine');
                assert.equal(
                    (await accountingStagingRecord1?.taxDate)?.toString(),
                    (await accountingStagingRecord2?.taxDate)?.toString(),
                );
                assert.equal(
                    JSON.stringify(await accountingStagingRecord2?.storedDimensions),
                    JSON.stringify({
                        dimensionType01: '300',
                        dimensionType02: 'CHANNELVALUE1',
                    }),
                );
                assert.equal(
                    JSON.stringify(await accountingStagingRecord2?.storedAttributes),
                    JSON.stringify({
                        item: 'LandedCost002',
                        task: 'Task1',
                        project: 'AttPROJ',
                        supplier: 'US017',
                        businessSite: 'US001',
                        financialSite: 'US001',
                    }),
                );

                assert.equal(
                    JSON.stringify(
                        await accountingStagingRecord2?.taxes
                            .map(async tax => ({ baseTaxSysId: (await tax.baseTax)._id }))
                            .toArray(),
                    ),
                    JSON.stringify(await toInvoiceLines[1].taxes.map(tax => ({ baseTaxSysId: tax._id })).toArray()),
                );

                // Check 3rd accounting staging record, for the journal entry

                assert.equal(
                    await accountingStagingRecord3?.documentSysId,
                    await accountingStagingRecord1?.documentSysId,
                );
                assert.equal(
                    (await accountingStagingRecord3?.financialSite)?._id,
                    (await accountingStagingRecord1?.financialSite)?._id,
                );
                assert.equal(
                    await accountingStagingRecord3?.documentNumber,
                    await accountingStagingRecord1?.documentNumber,
                );
                assert.equal(
                    (await accountingStagingRecord3?.documentDate)?.toString(),
                    (await accountingStagingRecord1?.documentDate)?.toString(),
                );

                assert.equal(
                    (await accountingStagingRecord3?.transactionCurrency)?._id,
                    (await (await purchaseInvoice.financialSite).currency)?._id,
                );
                assert.equal(await accountingStagingRecord3?.batchSize, await accountingStagingRecord1?.batchSize);
                assert.equal(
                    await accountingStagingRecord3?.documentType,
                    await accountingStagingRecord1?.documentType,
                );
                assert.equal(await accountingStagingRecord3?.targetDocumentType, 'journalEntry');

                assert.equal((await accountingStagingRecord3?.baseDocumentLine)?._id, toInvoiceLines[0]._id);
                assert.equal((await accountingStagingRecord3?.sourceBaseDocumentLine)?._id, toInvoiceLines[0]._id);
                assert.equal(await accountingStagingRecord3?.movementType, 'stockJournal');
                assert.equal(await accountingStagingRecord3?.documentNumber, purchaseInvoiceNumber);
                assert.equal(await accountingStagingRecord3?.sourceDocumentNumber, receiptNumber);
                assert.equal(
                    (await accountingStagingRecord3?.transactionCurrency)?._id,
                    (await purchaseInvoice.companyCurrency)._id,
                );
                assert.equal(await accountingStagingRecord3?.companyFxRate, 1);
                assert.equal(await accountingStagingRecord3?.companyFxRateDivisor, 1);
                assert.equal((await accountingStagingRecord3?.fxRateDate)?.toString(), '2021-11-17');
                assert.equal((await accountingStagingRecord3?.item)?._id, (await toInvoiceLines[0].item)._id);
                assert.equal((await accountingStagingRecord3?.supplier)?._id, (await purchaseInvoice.supplier)._id);

                assert.equal(await accountingStagingRecord3Amount?.amountType, 'landedCostAdjustmentAmount');
                assert.equal(await accountingStagingRecord3Amount?.amount, 10);
                assert.equal(await accountingStagingRecord3Amount?.documentLineType, 'documentLine');

                assert.equal(
                    JSON.stringify(await accountingStagingRecord3?.storedDimensions),
                    JSON.stringify({
                        dimensionType01: '300',
                        dimensionType02: 'CHANNELVALUE1',
                    }),
                );
                assert.equal(
                    JSON.stringify(await accountingStagingRecord3?.storedAttributes),
                    JSON.stringify({
                        item: 'Aqua',
                        task: 'Task1',
                        project: 'AttPROJ',
                        supplier: 'US017',
                        stockSite: 'US001',
                        businessSite: 'US001',
                        financialSite: 'US001',
                    }),
                );

                assert.equal(await accountingStagingRecord3?.sourceDocumentNumber, receiptNumber);
                assert.equal(await accountingStagingRecord3?.sourceDocumentType, 'purchaseReceipt');
            },
            { testActiveServiceOptions: [xtremMasterData.serviceOptions.landedCostOption], today: '2021-11-17' },
        ));

    it('Post purchase invoice linked to a purchase order and a purchase receipt of the same PO, with landed-cost', () =>
        Test.withContext(
            async (context: Context) => {
                // XT-77727
                // The goal is to check the finance payload with the following scenario:
                // - Create a purchase order with a single line
                // - Create + simulate posting of a partial purchase receipt for the order (50/150)
                // - Create a purchase invoice for the receipt + another line with a landed-cost line allocated to the receipt line and the order line
                // - Post the invoice
                // - Check the finance payload
                const order = await createOriginDocument(context, 'order');
                const orderNumber = await order.number;
                const orderId = order._id;
                const firstOrderLine = await order.lines.elementAt(0);
                const firstOrderLineId = firstOrderLine._id;

                assert.equal(orderNumber, 'PO200001');

                const receipts = await xtremPurchasing.nodes.PurchaseOrder.createPurchaseReceipt(context, order);
                await context.flushDeferredActions();
                const receiptNumber = await receipts[0].number;

                assert.isArray(receipts);
                assert.lengthOf(receipts, 1);

                let receipt = await context.read(
                    xtremPurchasing.nodes.PurchaseReceipt,
                    { number: await receipts[0].number },
                    { forUpdate: true },
                );

                let firstReceiptLine = await receipt.lines.elementAt(0);
                const firstReceiptLineId = firstReceiptLine._id;

                // modify the quantity of the receipt line to 50 instead of 150 in order to have a partial receipt for the order line
                await receipt.$.set({
                    status: 'pending',
                    lines: [
                        {
                            _action: 'update',
                            _id: firstReceiptLineId,
                            quantity: 50,
                            quantityInStockUnit: 50,
                            purchaseOrderLine: {
                                purchaseOrderLine: firstOrderLine,
                            },
                            stockDetails: [{ ...getStockDetailCreateData(), quantityInStockUnit: 50 }],
                            status: 'pending',
                            stockTransactionStatus: 'completed',
                        },
                    ],
                });

                await receipt.$.save();
                const receiptId = receipt._id;

                const landedCostId = '#LandedCost002';

                // Create and post a PI for the receipt with no variance + a landed-cost line allocated to the receipt line and the order line
                const { postResult, invoiceLine } = await createAndPostInvoiceFromReceipt(context, {
                    purchaseReceiptNumber: await receipt.number,
                    unitPriceVariance: 0,
                    landedCostAllocationData: [
                        {
                            landedCostId,
                            linesToBeAllocated: [firstReceiptLine, firstOrderLine],
                        },
                    ],
                });

                assert.deepEqual(postResult.wasSuccessful, true);

                // Check landed cost lines assigned to the PR and the PO
                const receiptLandedCostLines = context.query(xtremLandedCost.nodes.LandedCostLine, {
                    filter: {
                        landedCost: landedCostId,
                        documentLine: firstReceiptLineId,
                    },
                });
                assert.equal(await receiptLandedCostLines.length, 1);
                const receiptLandedCostLine = await receiptLandedCostLines.elementAt(0);
                assert.deepEqual(await receiptLandedCostLine.actualCostAmountInCompanyCurrency, 21.04);
                assert.deepEqual(await receiptLandedCostLine.actualAllocatedCostAmountInCompanyCurrency, 0.0);

                const orderLandedCostLines = context.query(xtremLandedCost.nodes.LandedCostLine, {
                    filter: {
                        landedCost: landedCostId,
                        documentLine: firstOrderLineId,
                    },
                    orderBy: { _id: 1 },
                });
                assert.deepEqual(
                    await orderLandedCostLines
                        .map(async orderLandedCostLine => ({
                            actualCostAmountInCompanyCurrency:
                                await orderLandedCostLine.actualCostAmountInCompanyCurrency,
                            actualAllocatedCostAmountInCompanyCurrency:
                                await orderLandedCostLine.actualAllocatedCostAmountInCompanyCurrency,
                        }))
                        .toArray(),
                    [
                        { actualCostAmountInCompanyCurrency: 21.04, actualAllocatedCostAmountInCompanyCurrency: 21.04 },
                        { actualCostAmountInCompanyCurrency: 42.09, actualAllocatedCostAmountInCompanyCurrency: 0.0 },
                    ],
                );

                const invoiceId = (await invoiceLine.document)._id;
                let landedCostInvoiceLine = await (
                    await invoiceLine.document
                ).lines.find(async line => {
                    return (await (await line.item).id) === landedCostId.slice(1);
                });
                assert.isDefined(landedCostInvoiceLine);
                if (!landedCostInvoiceLine) return; // type checking
                const landedCostInvoiceLineId = landedCostInvoiceLine._id;

                // rollbackCache function will flush deferred actions and saves
                await Test.rollbackCache(context);

                await xtremStockData.functions.testLib.simulateStockTransaction(
                    context,
                    receiptId,
                    xtremPurchasing.nodes.PurchaseReceipt,
                    'correction',
                    xtremPurchasing.nodes.PurchaseInvoice.onStockReply,
                    { stockUpdateResultStatus: 'corrected', originLineIds: [landedCostInvoiceLineId] },
                    () => Promise.resolve(true), // post both lines: 'Aqua' and 'LandedCost002'
                );

                const purchaseInvoice = await context.read(xtremPurchasing.nodes.PurchaseInvoice, {
                    _id: invoiceId,
                });
                firstReceiptLine = await context.read(xtremPurchasing.nodes.PurchaseReceiptLine, {
                    _id: firstReceiptLineId,
                });

                const stockJournal = await context.create(xtremStockData.nodes.StockJournal, {
                    item: (await firstReceiptLine.item)._id,
                    site: (await firstReceiptLine.site)._id,
                    activeQuantityInStockUnit: 0,
                    currency: (await purchaseInvoice.currency)._id,
                    effectiveDate: await purchaseInvoice.documentDate,
                    movementAmount: await (await orderLandedCostLines.elementAt(0)).actualCostAmountInCompanyCurrency,
                    quantityInStockUnit: 0,
                    owner: 'US001',
                    documentLine: firstReceiptLine._id,
                    sourceDocumentLine: (await purchaseInvoice.lines.elementAt(1))._id,
                    stockUnit: (await firstReceiptLine.stockUnit)._id,
                    reasonCode: 4,
                });
                await stockJournal.$.save();
                type FinancePayload = (Omit<
                    xtremFinanceData.interfaces.FinanceIntegrationDocument,
                    'batchId' | 'batchSize'
                > & { batchId?: string; batchSize?: number })[];
                const financePayload: FinancePayload =
                    await xtremPurchasing.functions.FinanceIntegration.getPurchaseInvoiceCreditMemoNotificationPayload(
                        context,
                        {
                            document: purchaseInvoice as xtremFinanceData.interfaces.PurchaseFinanceDocumentWithTax,
                            lines: (await purchaseInvoice.lines.toArray()) as xtremFinanceData.interfaces.InvoiceDocumentLine[],
                            documentType: 'purchaseInvoice',
                            targetDocumentType: 'accountsPayableInvoice',
                        },
                    );

                assert.equal(financePayload.length, 2);

                const invoice = await context.read(xtremPurchasing.nodes.PurchaseInvoice, { _id: invoiceId });
                receipt = await context.read(xtremPurchasing.nodes.PurchaseReceipt, { _id: receiptId });

                landedCostInvoiceLine = await invoice.lines.elementAt(1);
                const expectedResults: FinancePayload = [
                    {
                        documentSysId: invoiceId,
                        financialSiteSysId: (await invoice.financialSite)._id,
                        documentNumber: await invoice.number,
                        documentDate: '2021-11-17',
                        currencySysId: (await invoice.transactionCurrency)._id,
                        documentType: 'purchaseInvoice',
                        targetDocumentType: 'accountsPayableInvoice',
                        paymentTermSysId: (await invoice.paymentTerm)._id,
                        dueDate: '2021-12-17',
                        supplierDocumentDate: '2021-11-17',
                        supplierDocumentNumber: '',
                        documentLines: await invoice.lines
                            .map(async documentLine => ({
                                baseDocumentLineSysId: documentLine._id,
                                movementType: 'document' as xtremFinanceData.enums.MovementType,
                                sourceDocumentNumber: await documentLine.sourceDocumentNumber,
                                sourceDocumentType: await documentLine.sourceDocumentType,
                                fxRateDate: '2021-11-17',
                                currencySysId: (await invoice.transactionCurrency)._id,
                                companyFxRate: await invoice.companyFxRate,
                                companyFxRateDivisor: await invoice.companyFxRateDivisor,
                                itemSysId: (await documentLine.item)._id,
                                supplierSysId: (await invoice.billBySupplier)?._id,
                                payToSupplierSysId: (await invoice.payToSupplier)?._id,
                                payToSupplierAddressSysId: (await invoice.payToLinkedAddress)?._id,
                                returnAddressSysId: (await invoice.returnLinkedAddress)?._id,
                                recipientSiteSysId: (await documentLine.recipientSite)?._id,
                                amounts: [
                                    {
                                        amountType: 'amountExcludingTax',
                                        amount: await documentLine.amountExcludingTax,
                                        documentLineType: 'documentLine',
                                    },
                                ] as xtremFinanceData.interfaces.FinanceIntegrationDocument['documentLines'][0]['amounts'],
                                taxDate: '2021-11-17',
                                storedDimensions: (await documentLine.storedDimensions) ?? {},
                                storedAttributes: {
                                    ...((await documentLine.storedAttributes) as xtremMasterData.interfaces.StoredAttributes),
                                    ...(await documentLine.computedAttributes),
                                },
                                taxes: await documentLine.taxes.map(tax => ({ baseTaxSysId: tax._id })).toArray(),
                                customerSysId: undefined,
                                providerSiteSysId: undefined,
                            }))
                            .toArray(),
                        taxes: [],
                        sourceLines: [],
                        isPrinted: undefined,
                        taxCalculationStatus: undefined,
                    },
                    {
                        documentSysId: invoiceId,
                        financialSiteSysId: (await invoice.financialSite)._id,
                        documentNumber: await invoice.number,
                        documentDate: '2021-11-17',
                        currencySysId: (await invoice.transactionCurrency)._id,
                        documentType: 'purchaseInvoice',
                        targetDocumentType: 'journalEntry',
                        documentLines:
                            (await (
                                await landedCostInvoiceLine.landedCost
                            )?.allocations
                                .map(async allocation => {
                                    const allocatedDocumentLine = (await allocation.getAllocatedDocumentLine()) as
                                        | xtremPurchasing.nodes.PurchaseOrderLine
                                        | xtremPurchasing.nodes.PurchaseReceiptLine;
                                    return {
                                        uiBaseDocumentLineSysId: landedCostInvoiceLine._id,
                                        uiSourceDocumentNumber:
                                            (await allocation.allocatedDocumentType) === 'purchaseReceipt'
                                                ? await invoice.number
                                                : orderNumber,
                                        sourceBaseDocumentLineSysId:
                                            (await allocation.allocatedDocumentType) === 'purchaseReceipt'
                                                ? landedCostInvoiceLine._id
                                                : allocatedDocumentLine._id,
                                        baseDocumentLineSysId: landedCostInvoiceLine._id,
                                        movementType: ((await allocation.allocatedDocumentType) === 'purchaseReceipt'
                                            ? 'stockJournal'
                                            : 'document') as xtremFinanceData.enums.MovementType,
                                        documentNumber: await invoice.number,
                                        sourceDocumentNumber: await (
                                            await allocation.allocatedDocumentLine
                                        ).documentNumber,
                                        sourceDocumentType:
                                            (await allocation.allocatedDocumentType) as xtremFinanceData.enums.SourceDocumentType,
                                        fxRateDate: '2021-11-17',
                                        currencySysId: (
                                            await (
                                                await (
                                                    await invoice.financialSite
                                                ).legalCompany
                                            ).currency
                                        )._id,
                                        companyFxRate: 1,
                                        companyFxRateDivisor: 1,
                                        itemSysId: (await allocation.item)?._id,
                                        supplierSysId: (await invoice.billBySupplier)?._id,
                                        amounts: [
                                            {
                                                amountType:
                                                    (await allocation.allocatedDocumentType) === 'purchaseReceipt'
                                                        ? 'landedCostAdjustmentAmount'
                                                        : 'landedCostStockInTransitAmount',
                                                amount: await allocation.costAmountInCompanyCurrency,
                                                documentLineType: 'documentLine',
                                            },
                                        ] as xtremFinanceData.interfaces.FinanceIntegrationDocument['documentLines'][0]['amounts'],
                                        storedDimensions: (await allocatedDocumentLine.storedDimensions) ?? {},
                                        storedAttributes: {
                                            ...((await allocatedDocumentLine.storedAttributes) as xtremMasterData.interfaces.StoredAttributes),
                                            ...(await allocatedDocumentLine.computedAttributes),
                                        },
                                    };
                                })
                                .toArray()) ?? [],
                        sourceLines: [
                            {
                                sourceDocumentNumber: receiptNumber,
                                sourceDocumentSysId: receiptId,
                                sourceDocumentType: 'purchaseReceipt',
                                isSourceForDimension: false,
                            },
                            {
                                sourceDocumentNumber: receiptNumber,
                                sourceDocumentSysId: receiptId,
                                sourceDocumentType: 'purchaseReceipt',
                                isSourceForDimension: true,
                            },
                            {
                                sourceDocumentNumber: orderNumber,
                                sourceDocumentSysId: orderId,
                                sourceDocumentType: 'purchaseReceipt',
                                isSourceForDimension: true,
                            },
                        ],
                    },
                ];

                delete financePayload[0].batchId;
                delete financePayload[0].batchSize;
                delete financePayload[1].batchId;
                delete financePayload[1].batchSize;
                // divide the payload into two parts to make it easier to find the difference in the event of failure.
                assert.deepEqual(financePayload[0], expectedResults[0]);
                assert.deepEqual(financePayload[1], expectedResults[1]);
            },
            { testActiveServiceOptions: [xtremMasterData.serviceOptions.landedCostOption], today: '2021-11-17' },
        ));

    it('Post an invoice with stock managed and non-stock managed items and check the finance notification is called once', () =>
        Test.withContext(async (context: Context) => {
            const purchaseInvoiceNumber = 'PI22';
            let purchaseInvoice = await context.read(xtremPurchasing.nodes.PurchaseInvoice, {
                number: purchaseInvoiceNumber,
            });
            let purchaseReceipt = await context.read(xtremPurchasing.nodes.PurchaseReceipt, { number: 'PR57' });
            const stockManagedReceiptLine = await purchaseReceipt.lines.elementAt(1);

            // only the 2nd line of the receipt and the invoice is a stock managed item
            const expectedDocuments = [
                {
                    id: purchaseReceipt._id,
                    lines: [
                        {
                            id: stockManagedReceiptLine._id,
                            sortValue: await stockManagedReceiptLine._sortValue,
                        },
                    ],
                },
            ];
            const expectedStockNotificationPayload = {
                payload: {
                    replyTopic: 'PurchaseInvoice/stock/correction/reply',
                    stockUpdateParameters: { movementType: 'correction' },
                    documents: expectedDocuments,
                },
            };

            const notifySpy = sinon.spy(context, 'notify');
            const postResult = await xtremPurchasing.nodes.PurchaseInvoice.post(context, purchaseInvoice);

            let notifyCalls = getStockAndFinanceNotify(notifySpy);

            assert.deepEqual(
                {
                    ...postResult,
                    stockPostingResult: JSON.stringify({
                        ...JSON.parse(postResult.stockPostingResult),
                        notificationId: undefined,
                    }),
                },
                {
                    wasSuccessful: true,
                    message: 'The purchase invoice posted.',
                    stockPostingResult: `{"result":"requested","documents":${JSON.stringify(expectedDocuments)}}`,
                    validationMessages: [],
                },
            );

            // stock notification only for the stock managed item. Finance notification must not be sent
            assert.equal(notifyCalls.length, 1);
            sinon.assert.calledWith(notifyCalls[0], 'Stock/correct/start', expectedStockNotificationPayload);

            notifySpy.resetHistory();
            await Test.rollbackCache(context);

            purchaseInvoice = await context.read(xtremPurchasing.nodes.PurchaseInvoice, {
                number: purchaseInvoiceNumber,
            });
            purchaseReceipt = await context.read(xtremPurchasing.nodes.PurchaseReceipt, { number: 'PR57' });

            await xtremStockData.functions.testLib.simulateStockTransaction(
                context,
                purchaseReceipt._id,
                xtremPurchasing.nodes.PurchaseReceipt,
                'correction',
                xtremPurchasing.nodes.PurchaseInvoice.onStockReply,
                {
                    stockUpdateResultStatus: 'corrected',
                    originLineIds: [(await purchaseInvoice.lines.elementAt(1))._id],
                },
            );

            notifyCalls = getStockAndFinanceNotify(notifySpy);
            assert.equal(notifyCalls.length, 0);

            const documentFilter = {
                documentNumber: purchaseInvoiceNumber,
                documentType: 'purchaseInvoice',
                targetDocumentType: 'accountsPayableInvoice',
            };

            const financeTransactions = context.query(xtremFinanceData.nodes.FinanceTransaction, {
                filter: { ...documentFilter },
            });
            assert.equal(await financeTransactions.length, 1);
            const financeTransaction = await financeTransactions.at(0);

            assert.equal(await financeTransaction?.message, '');
            assert.equal(await financeTransaction?.status, 'pending');

            const accountingStagingRecords = await context
                .query(xtremFinanceData.nodes.AccountingStaging, { filter: { ...documentFilter } })
                .toArray();

            assert.equal(accountingStagingRecords.length, 3);
            const accountingStagingRecord = accountingStagingRecords.at(0);

            assert.equal(await accountingStagingRecord?.documentSysId, purchaseInvoice._id);
            assert.equal(await accountingStagingRecord?.batchId, await financeTransaction?.batchId);
            assert.equal(
                (await accountingStagingRecord?.financialSite)?._id,
                (await purchaseInvoice.financialSite)._id,
            );
            assert.equal(await accountingStagingRecord?.documentNumber, purchaseInvoiceNumber);
            assert.equal(
                (await accountingStagingRecord?.documentDate)?.toString(),
                (await purchaseInvoice.invoiceDate).toString(),
            );
            assert.equal(
                (await accountingStagingRecord?.transactionCurrency)?._id,
                (await purchaseInvoice.transactionCurrency)._id,
            );
            assert.equal(await accountingStagingRecord?.batchSize, 3);
            assert.equal(await accountingStagingRecord?.documentType, 'purchaseInvoice');
            assert.equal(await accountingStagingRecord?.targetDocumentType, 'accountsPayableInvoice');
            assert.equal((await accountingStagingRecord?.paymentTerm)?._id, (await purchaseInvoice.paymentTerm)._id);
            assert.equal(
                (await accountingStagingRecord?.dueDate)?.toString(),
                (await purchaseInvoice.dueDate).toString(),
            );
            assert.equal(
                (await accountingStagingRecord?.supplierDocumentDate)?.toString(),
                (await purchaseInvoice.documentDate).toString(),
            );
            assert.equal(await accountingStagingRecord?.supplierDocumentNumber, '');

            assert.equal(
                await (
                    await (
                        await purchaseInvoice.paymentTracking
                    )?.document
                )?.number,
                purchaseInvoiceNumber,
            );
            assert.equal(
                await (
                    await (
                        await financeTransaction?.paymentTracking
                    )?.document
                )?.number,
                purchaseInvoiceNumber,
            );
        }));

    it('Post an invoice for a receipt line with stock transaction status not completed - fails', () =>
        Test.withContext(
            async context => {
                let purchaseReceipt = await context.read(
                    xtremPurchasing.nodes.PurchaseReceipt,
                    { number: 'PR240001' },
                    { forUpdate: true },
                );
                const firstPurchaseReceiptLine = await purchaseReceipt.lines.elementAt(0);
                await purchaseReceipt.$.set({
                    lines: [
                        {
                            _action: 'update',
                            _id: firstPurchaseReceiptLine._id,
                            stockTransactionStatus: 'completed',
                        },
                    ],
                });
                await purchaseReceipt.$.save();

                let purchaseInvoice = await context.create(xtremPurchasing.nodes.PurchaseInvoice, {
                    site: '#US001',
                    billBySupplier: '#US017',
                    currency: '#EUR',
                    totalAmountExcludingTax: 33,
                    invoiceDate: date.today(),
                    lines: [
                        {
                            item: '#STOAVC',
                            quantity: 3,
                            unit: '#EACH',
                            grossPrice: 11,
                            purchaseReceiptLine: { purchaseReceiptLine: '#PR240001|10' },
                        },
                    ],
                });
                await purchaseInvoice.$.save({ flushDeferredActions: true });
                const number = await purchaseInvoice.number;

                purchaseReceipt = await context.read(
                    xtremPurchasing.nodes.PurchaseReceipt,
                    { number: 'PR240001' },
                    { forUpdate: true },
                );
                await purchaseReceipt.$.set({
                    lines: [
                        {
                            _action: 'update',
                            _id: firstPurchaseReceiptLine._id,
                            stockTransactionStatus: 'error',
                        },
                    ],
                });
                await purchaseReceipt.$.save();

                purchaseInvoice = await context.read(
                    xtremPurchasing.nodes.PurchaseInvoice,
                    { number },
                    { forUpdate: true },
                );

                await assert.isRejected(
                    xtremPurchasing.nodes.PurchaseInvoice.post(context, purchaseInvoice),
                    'The purchase invoice cannot be posted. The stock status of all related purchase receipt lines needs to be Completed.',
                );
            },
            { today: '2024-11-02', testActiveServiceOptions: [xtremMasterData.serviceOptions.landedCostOption] },
        ));

    it('Post a landed cost invoice allocated to a receipt line with stock transaction status not completed - fails', () =>
        Test.withContext(
            async context => {
                const purchaseInvoice = await context.read(
                    xtremPurchasing.nodes.PurchaseInvoice,
                    { number: 'PI12' },
                    { forUpdate: true },
                );
                const firstPurchaseInvoiceLine = await purchaseInvoice.lines.elementAt(0);
                assert.isNotNull(await firstPurchaseInvoiceLine.landedCost);
                const firstAllocation = await (await firstPurchaseInvoiceLine.landedCost)!.allocations.at(0);
                assert.notEqual(firstAllocation, undefined);
                assert.equal(await firstAllocation!.allocatedDocumentType, 'purchaseReceipt');
                const receiptLineAllocated = await firstAllocation!.allocatedDocumentLine;
                assert.equal(await receiptLineAllocated.documentNumber, 'PR43');

                const purchaseReceipt = await context.read(
                    xtremPurchasing.nodes.PurchaseReceipt,
                    { number: 'PR43' },
                    { forUpdate: true },
                );
                await purchaseReceipt.$.set({
                    lines: [
                        {
                            _action: 'update',
                            _id: receiptLineAllocated._id,
                            stockTransactionStatus: 'error',
                        },
                    ],
                });
                await purchaseReceipt.$.save();

                await assert.isRejected(
                    xtremPurchasing.nodes.PurchaseInvoice.post(context, purchaseInvoice),
                    /The purchase receipt lines allocated to landed cost need to have the stock status Completed./,
                );
            },
            { testActiveServiceOptions: [xtremMasterData.serviceOptions.landedCostOption], today: '2024-11-02' },
        ));
});

describe('Discount before payment date control', () => {
    function readPurchaseInvoice(context: Context, number: string): Promise<xtremPurchasing.nodes.PurchaseInvoice> {
        return context.read(xtremPurchasing.nodes.PurchaseInvoice, { number }, { forUpdate: true });
    }

    it('Failed control due to discountPaymentBeforeDate being after the due date', () =>
        Test.withContext(async (context: Context) => {
            const purchaseInvoice = await readPurchaseInvoice(context, 'PI22');
            const dueDate = await purchaseInvoice.dueDate;
            // Update the discountPaymentBeforeDate to be after the due date
            await purchaseInvoice.$.set({ paymentTracking: { discountPaymentBeforeDate: dueDate.addDays(1) } });
            await assert.isRejected(
                xtremPurchasing.nodes.PurchaseInvoice.post(context, purchaseInvoice),
                'The discount date needs to be on or before the due date.',
            );
        }));
});
