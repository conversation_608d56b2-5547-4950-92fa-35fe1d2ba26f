import type { Context } from '@sage/xtrem-core';
import { Test } from '@sage/xtrem-core';
import { assert } from 'chai';
import * as xtremPurchasing from '../../../lib';

async function checkPurchaseOrder(
    context: Context,
    order: { number: string; line: number; status: string; lineStatus: string; haveLineToLine: boolean },
) {
    const myOrder = await context.read(xtremPurchasing.nodes.PurchaseOrder, { number: order.number });
    const orderLine = await myOrder.lines.elementAt(order.line);

    assert.equal(await myOrder.status, order.status, `Purchase order ${order.number} status is not ${order.status}`);
    assert.equal(
        await orderLine.status,
        order.status,
        `Purchase order line ${order.number} status is not ${order.status}`,
    );
    if (order.haveLineToLine) {
        const orderLineToInvoiceLine = await orderLine.purchaseInvoiceLines.at(0);
        assert.isNotNull(orderLineToInvoiceLine);
    }

    return { orderSysId: myOrder._id, orderLineSysId: orderLine._id };
}

describe('Purchase order line to purchase invoice line', () => {
    it('updatePurchaseOrder() on already closed purchase order', () =>
        Test.withContext(async context => {
            // spy on function  xtremPurchasing.functions.OrderLineToLines.updatePurchaseOrder;

            const { orderLineSysId } = await checkPurchaseOrder(context, {
                number: 'PO4',
                line: 0,
                status: 'pending',
                lineStatus: 'pending',
                haveLineToLine: true,
            });

            let purchaseOrderLine = await context.read(
                xtremPurchasing.nodes.PurchaseOrderLine,
                { _id: orderLineSysId },
                { forUpdate: true },
            );

            await purchaseOrderLine.$.set({ status: 'pending' });
            await purchaseOrderLine.$.save();
            await Test.rollbackCache(context);

            await checkPurchaseOrder(context, {
                number: 'PO4',
                line: 0,
                status: 'pending',
                lineStatus: 'pending',
                haveLineToLine: true,
            });

            purchaseOrderLine = await context.read(
                xtremPurchasing.nodes.PurchaseOrderLine,
                { _id: orderLineSysId },
                { forUpdate: true },
            );

            await purchaseOrderLine.$.set({ status: 'closed' });
            // Todo : Change this to a assert.isRejected
            await assert.isRejected(purchaseOrderLine.$.save());
            // 'It is forbidden to change the status of an order line to close it. Use the dedicated function.'

            await checkPurchaseOrder(context, {
                number: 'PO4',
                line: 0,
                status: 'pending',
                lineStatus: 'pending',
                haveLineToLine: true,
            });

            await Test.rollbackCache(context);

            await checkPurchaseOrder(context, {
                number: 'PO4',
                line: 0,
                status: 'pending',
                lineStatus: 'pending',
                haveLineToLine: true,
            });
        }));

    /**
     * The following 13 lines of code were suggestions provided by GitHub Copilot
     * */
    it('should return lineStatus "closed" if status is "closed"', async () => {
        const mockLine = {
            invoicedQuantityInStockUnit: Promise.resolve(5),
            quantityInStockUnit: Promise.resolve(10),
            status: Promise.resolve('closed'),
            _id: 2,
        };
        Object.setPrototypeOf(mockLine, xtremPurchasing.nodes.PurchaseOrderLine.prototype);

        const result = await xtremPurchasing.functions.PurchaseInvoiceLib.getLineStatuses(mockLine as any);
        assert.equal(result.lineStatus, 'closed');
    });
});
