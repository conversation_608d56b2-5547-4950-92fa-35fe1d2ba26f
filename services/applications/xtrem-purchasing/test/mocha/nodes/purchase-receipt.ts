import { Test, date } from '@sage/xtrem-core';
import * as xtremDistribution from '@sage/xtrem-distribution';
import * as xtremFinanceData from '@sage/xtrem-finance-data';
import { assert } from 'chai';
import * as sinon from 'sinon';
import * as xtremPurchasing from '../../../lib/index';

describe('Purchase receipt node', () => {
    const testDate = '2020-08-10';

    async function getExpectedPR250003JE(purchaseReceipt: xtremPurchasing.nodes.PurchaseReceipt, batchId: string) {
        const financialSite = await purchaseReceipt.financialSite;
        const supplier = await purchaseReceipt.businessRelation;
        const currency = await purchaseReceipt.currency;
        const companyCurrency = await (await financialSite.legalCompany).currency;
        const purchaseReceiptLine1 = await purchaseReceipt.lines.at(0);
        const purchaseReceiptLine2 = await purchaseReceipt.lines.at(1);
        return {
            documentSysId: purchaseReceipt._id,
            batchId,
            financialSiteSysId: financialSite._id,
            documentNumber: 'PR250003',
            documentDate: '2025-01-13',
            currencySysId: currency._id,
            batchSize: 2,
            documentType: 'purchaseReceipt',
            targetDocumentType: 'journalEntry',
            documentLines: [
                {
                    baseDocumentLineSysId: purchaseReceiptLine1?._id,
                    sourceBaseDocumentLineSysId: undefined,
                    movementType: 'stockJournal',
                    sourceDocumentNumber: '',
                    sourceDocumentType: null,
                    currencySysId: companyCurrency._id,
                    companyFxRate: '1',
                    companyFxRateDivisor: '1',
                    fxRateDate: '2025-01-13',
                    itemSysId: (await purchaseReceiptLine1?.item)?._id,
                    supplierSysId: supplier._id,
                    amounts: [
                        {
                            amount: '1002',
                            amountType: 'varianceAmount',
                            documentLineType: 'documentLine',
                        },
                    ],
                    storedDimensions: { dimensionType01: '300', dimensionType02: 'CHANNELVALUE1' },
                    storedAttributes: {
                        task: 'Task1',
                        project: 'AttPROJ',
                        financialSite: 'US001',
                        stockSite: 'US001',
                        item: 'RM_ITEM_001',
                        businessSite: 'US001',
                        supplier: 'US016',
                    },
                },
                {
                    baseDocumentLineSysId: purchaseReceiptLine2?._id,
                    sourceBaseDocumentLineSysId: undefined,
                    movementType: 'stockJournal',
                    sourceDocumentNumber: '',
                    sourceDocumentType: null,
                    currencySysId: companyCurrency._id,
                    companyFxRate: '1',
                    companyFxRateDivisor: '1',
                    fxRateDate: '2025-01-13',
                    itemSysId: (await purchaseReceiptLine2?.item)?._id,
                    supplierSysId: supplier._id,
                    amounts: [
                        {
                            amount: '2104.2',
                            amountType: 'varianceAmount',
                            documentLineType: 'documentLine',
                        },
                    ],
                    storedDimensions: { dimensionType01: '300', dimensionType02: 'CHANNELVALUE1' },
                    storedAttributes: {
                        task: 'Task1',
                        project: 'AttPROJ',
                        financialSite: 'US001',
                        stockSite: 'US001',
                        item: 'RM_ITEM_002',
                        businessSite: 'US001',
                        supplier: 'US016',
                    },
                },
            ],
            sourceLines: [],
        };
    }

    it('Control on purchase receipt with wrong tax type.', () =>
        Test.withContext(
            async context => {
                const purchaseReceipt = await context.create(xtremPurchasing.nodes.PurchaseReceipt, {
                    site: '#US001',
                    businessRelation: '#LECLERC',
                    currency: '#USD',
                    date: date.make(2020, 8, 10),
                    lines: [
                        {
                            item: '#Muesli',
                            quantity: 10,
                            unit: '#GRAM',
                            grossPrice: 30,
                            taxes: [
                                {
                                    taxReference: '#FR_TVA_INTERMEDIATE_COLLECTED_ON_PAYMENT',
                                    taxRate: 0,
                                },
                            ],
                        },
                    ],
                });
                await assert.isRejected(purchaseReceipt.$.save());
                assert.deepEqual(purchaseReceipt.$.context.diagnoses, [
                    {
                        severity: 3,
                        path: [],
                        message: "The tax type for all documents needs to be 'Purchasing' or 'Purchasing and sales'.",
                    },
                ]);
            },
            { today: testDate },
        ));

    it('Control on purchase receipt with correct tax type.', () =>
        Test.withContext(
            async context => {
                const purchaseReceipt = await context.create(xtremPurchasing.nodes.PurchaseReceipt, {
                    site: '#US001',
                    businessRelation: '#LECLERC',
                    currency: '#USD',
                    date: date.make(2020, 8, 10),
                    lines: [
                        {
                            item: '#Muesli',
                            quantity: 10,
                            unit: '#GRAM',
                            grossPrice: 30,
                            taxes: [
                                {
                                    taxReference: '#FR_TVA_NORMAL_DEDUCTIBLE_FA_INTRASTAT',
                                    taxRate: 0,
                                },
                            ],
                        },
                    ],
                });
                assert.isOk(await purchaseReceipt.$.control());
            },
            { today: testDate },
        ));

    it('Resend notification for finance - rejected because there is a landed cost allocated to a PO', () =>
        Test.withContext(async context => {
            const purchaseReceipt: xtremPurchasing.nodes.PurchaseReceipt = await context.read(
                xtremPurchasing.nodes.PurchaseReceipt,
                { number: 'PR250001' },
            );

            await assert.isRejected(
                xtremPurchasing.nodes.PurchaseReceipt.resendNotificationForFinance(context, purchaseReceipt),
                'There are journal entries already created for this document. A notification will not be resent.',
            );
        }));

    it('Resend notification for finance - rejected because there is a landed cost allocated to a PO', () =>
        Test.withContext(async context => {
            const purchaseReceipt: xtremPurchasing.nodes.PurchaseReceipt = await context.read(
                xtremPurchasing.nodes.PurchaseReceipt,
                { number: 'PR250002' },
            );

            await assert.isRejected(
                xtremPurchasing.nodes.PurchaseReceipt.resendNotificationForFinance(context, purchaseReceipt),
                'This purchase receipt has values allocated from a purchase order. A notification will not be resent.',
            );
        }));

    it.skip('Resend notification for finance - success', () =>
        Test.withContext(
            async context => {
                const purchaseReceipt: xtremPurchasing.nodes.PurchaseReceipt = await context.read(
                    xtremPurchasing.nodes.PurchaseReceipt,
                    { number: 'PR250003' },
                );

                const notifySpy = sinon.spy(context, 'notify');
                await xtremPurchasing.nodes.PurchaseReceipt.resendNotificationForFinance(context, purchaseReceipt);
                assert.equal(notifySpy.getCalls().length, 1);
                const firstNotificationTopic = notifySpy.args[0][0];
                const firstNotificationPayload = notifySpy.args[0][1];
                const firstNotificationReplyTopic = notifySpy.args[0][2];

                const batchId =
                    (await (
                        await context
                            .query(xtremFinanceData.nodes.FinanceTransaction, {
                                filter: {
                                    documentNumber: 'PR250003',
                                    documentType: 'purchaseReceipt',
                                    targetDocumentType: 'journalEntry',
                                },
                            })
                            .at(0)
                    )?.batchId) || '';

                assert.deepEqual(
                    [
                        firstNotificationTopic,
                        JSON.parse(JSON.stringify(firstNotificationPayload)),
                        firstNotificationReplyTopic,
                    ],
                    [
                        'accountingInterface',
                        JSON.parse(JSON.stringify(await getExpectedPR250003JE(purchaseReceipt, batchId))),
                        { replyTopic: 'PurchaseReceipt/accountingInterface' },
                    ],
                );
            },
            { today: '2025-01-13' },
        ));

    it('Purchase receipt - beforePrintPurchaseReceipt - taxCalculationStatus', () =>
        Test.withContext(async context => {
            let purchaseReceipt = await context.read(xtremPurchasing.nodes.PurchaseReceipt, {
                _id: '#PR20',
            });
            const baseDistributionDocument = await context.read(
                xtremDistribution.nodes.BaseDistributionDocument,
                { _id: purchaseReceipt._id },
                { forUpdate: true },
            );
            await baseDistributionDocument.$.set({ taxCalculationStatus: 'failed' });
            await baseDistributionDocument.$.save();

            purchaseReceipt = await context.read(xtremPurchasing.nodes.PurchaseReceipt, { _id: '#PR20' });
            assert.equal(await purchaseReceipt.taxCalculationStatus, 'failed');
            await assert.isRejected(
                xtremPurchasing.nodes.PurchaseReceipt.beforePrintPurchaseReceipt(context, purchaseReceipt),
                'You need to resolve tax calculation issues before you can print the receipt.',
            );
        }));

    it('Purchase receipt - beforePrintPurchaseReceipt - status', () =>
        Test.withContext(async context => {
            let purchaseReceipt = await context.read(xtremPurchasing.nodes.PurchaseReceipt, {
                _id: '#PR20',
            });
            const baseDistributionDocument = await context.read(
                xtremDistribution.nodes.BaseDistributionDocument,
                { _id: purchaseReceipt._id },
                { forUpdate: true },
            );
            await baseDistributionDocument.$.set({ status: 'error' });
            await baseDistributionDocument.$.save();

            purchaseReceipt = await context.read(xtremPurchasing.nodes.PurchaseReceipt, { _id: '#PR20' });
            assert.equal(await purchaseReceipt.status, 'error');
            await assert.isRejected(
                xtremPurchasing.nodes.PurchaseReceipt.beforePrintPurchaseReceipt(context, purchaseReceipt),
                'You need to correct the receipt details before you can print the receipt.',
            );
        }));
});
