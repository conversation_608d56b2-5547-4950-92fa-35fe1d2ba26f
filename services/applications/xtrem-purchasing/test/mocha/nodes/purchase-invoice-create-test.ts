// tslint:disable:no-duplicate-string
import type { Context, NodeCreateData } from '@sage/xtrem-core';
import { Test, date } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import { ValidationSeverity } from '@sage/xtrem-shared';
import * as xtremSystem from '@sage/xtrem-system';
import { assert } from 'chai';
import * as xtremPurchasing from '../../../index';

const { withSequenceNumberContext } = xtremMasterData.functions.testLib;

describe('PurchaseInvoiceNode', () => {
    function createPurchaseInvoice(context: Context, invoiceDate: date, site: string = '#ETS1-S01') {
        const baseData: NodeCreateData<xtremPurchasing.nodes.PurchaseInvoice> = {
            site,
            billBySupplier: '#US017',
            currency: '#EUR',
            invoiceDate,
            matchingStatus: 'variance',
            supplierDocumentNumber: '123',
            totalAmountExcludingTax: 28,
            totalTaxAmount: 8,
            lines: [
                {
                    _action: 'create',
                    item: '#NonStockManagedItem',
                    quantity: 1,
                    unit: '#KILOGRAM',
                    grossPrice: 100,
                    recipientSite: site,
                    taxes: [
                        {
                            _action: 'create',
                            taxReference: '#FR002',
                            taxRate: 0,
                        },
                    ],
                },
                {
                    _action: 'create',
                    item: '#NonStockManagedItem',
                    quantity: 4,
                    unit: '#KILOGRAM',
                    grossPrice: 44.55,
                    recipientSite: site,
                    taxes: [
                        {
                            _action: 'create',
                            taxReference: '#FR002',
                            taxRate: 0,
                        },
                    ],
                },
                {
                    _action: 'create',
                    item: '#NonStockManagedItem',
                    quantity: 5,
                    unit: '#KILOGRAM',
                    grossPrice: 44.55,
                    recipientSite: site,
                },
            ],
            matchingUser: '#<EMAIL>',
        };
        return context.create(xtremPurchasing.nodes.PurchaseInvoice, baseData);
    }
    it('Create purchasing invoice with purchasing site not belonging to legal company - create 2 lines with 2 different receiving sites.', () =>
        Test.withContext(
            async context => {
                const newPurchaseInvoice = await context.create(xtremPurchasing.nodes.PurchaseInvoice, {
                    site: 'US007',
                    billBySupplier: '#US017',
                    currency: '#EUR',
                    invoiceDate: date.today(),
                    matchingStatus: 'variance',
                    supplierDocumentNumber: '123',
                    totalAmountExcludingTax: 20,
                    totalTaxAmount: 5,
                    matchingUser: '<EMAIL>',
                    lines: [
                        {
                            item: '#Service001',
                            recipientSite: '#US007',
                            stockTransactionStatus: 'draft',
                            matchingStatus: 'variance',
                            quantity: 10,
                        },
                    ],
                });
                await assert.isRejected(newPurchaseInvoice.$.save());
                assert.deepEqual(newPurchaseInvoice.$.context.diagnoses, [
                    {
                        path: ['lines', '-1000000002', 'recipientSite'],
                        message: 'The record is not valid. You need to select a different record.',
                        severity: 3,
                    },
                    {
                        path: ['lines', '-1000000002'],
                        message: 'The item: Service001, is not managed for the site: US007.',
                        severity: 3,
                    },
                    {
                        path: ['lines', '-1000000002'],
                        message: 'The item: Service001, is not managed for the supplier: US017.',
                        severity: 2,
                    },
                ]);
            },
            { today: '2023-01-17' },
        ));
    it('Verify chronological control between two purchase invoice on creation', () =>
        withSequenceNumberContext(
            'PurchaseInvoice',
            { isChronological: true },
            async context => {
                const purchaseInvoice: xtremPurchasing.nodes.PurchaseInvoice = await createPurchaseInvoice(
                    context,
                    date.make(2022, 2, 2),
                );
                await purchaseInvoice.$.save({ flushDeferredActions: true });

                const purchaseInvoiceError: xtremPurchasing.nodes.PurchaseInvoice = await createPurchaseInvoice(
                    context,
                    date.make(2022, 2, 1),
                );
                await assert.isRejected(purchaseInvoiceError.$.save(), 'The record was not created.');
            },
            { today: '2022-02-02' },
        ));

    it('Create a purchase invoice and repost', () =>
        Test.withContext(
            async context => {
                const purchaseInvoice: xtremPurchasing.nodes.PurchaseInvoice = await createPurchaseInvoice(
                    context,
                    date.make(2022, 2, 2),
                );
                await purchaseInvoice.$.save({ flushDeferredActions: true });

                const toInvoiceLines = await purchaseInvoice.lines
                    .map(async line => {
                        return {
                            baseDocumentLineSysId: line._id,
                            storedAttributes:
                                (await line.storedAttributes) as xtremMasterData.interfaces.StoredAttributes,
                            storedDimensions: (await line.storedDimensions) || {},
                        };
                    })
                    .toArray();

                await assert.isRejected(
                    xtremPurchasing.nodes.PurchaseInvoice.repost(context, purchaseInvoice, {
                        header: { supplierDocumentNumber: 'SUPDOC001' },
                        lines: toInvoiceLines,
                    }),
                    "You can only repost a purchase invoice if the status is 'Failed.'",
                );
            },
            { today: '2022-02-02' },
        ));

    it('Ensure purchase invoices with different supplier document numbers can be saved', () =>
        Test.withContext(async context => {
            // Purchase Invoice A.
            const supplierDocumentNumberA = '123';
            const purchaseInvoiceDataA: NodeCreateData<xtremPurchasing.nodes.PurchaseInvoice> = {
                site: '#US001',
                billBySupplier: '#US017',
                currency: '#EUR',
                invoiceDate: date.make(2023, 4, 28),
                matchingStatus: 'variance',
                supplierDocumentNumber: supplierDocumentNumberA,
                totalAmountExcludingTax: 20,
                totalTaxAmount: 5,
                lines: [],
                matchingUser: '<EMAIL>',
            };
            const purchaseInvoiceA = await context.create(xtremPurchasing.nodes.PurchaseInvoice, purchaseInvoiceDataA);
            await purchaseInvoiceA.$.save({ flushDeferredActions: true });

            // Purchase Invoice B.
            const supplierDocumentNumberB = '456';
            const purchaseInvoiceDataB: NodeCreateData<xtremPurchasing.nodes.PurchaseInvoice> = {
                site: '#US001',
                billBySupplier: '#US017',
                currency: '#EUR',
                invoiceDate: date.make(2023, 4, 28),
                matchingStatus: 'variance',
                supplierDocumentNumber: supplierDocumentNumberB,
                totalAmountExcludingTax: 20,
                totalTaxAmount: 5,
                lines: [],
                matchingUser: '<EMAIL>',
            };
            const purchaseInvoiceB = await context.create(xtremPurchasing.nodes.PurchaseInvoice, purchaseInvoiceDataB);

            await assert.isFulfilled(purchaseInvoiceB.$.save({ flushDeferredActions: true }));
        }));

    it('Ensure purchase invoices with equal supplier document numbers cannot be saved', () =>
        Test.withContext(async context => {
            const supplierDocumentNumber = '123';

            // Purchase Invoice A.
            const purchaseInvoiceDataA: NodeCreateData<xtremPurchasing.nodes.PurchaseInvoice> = {
                site: '#US001',
                billBySupplier: '#US017',
                currency: '#EUR',
                invoiceDate: date.make(2023, 4, 28),
                matchingStatus: 'variance',
                supplierDocumentNumber,
                totalAmountExcludingTax: 20,
                totalTaxAmount: 5,
                lines: [],
                matchingUser: '<EMAIL>',
            };
            const purchaseInvoiceA = await context.create(xtremPurchasing.nodes.PurchaseInvoice, purchaseInvoiceDataA);
            await purchaseInvoiceA.$.save({ flushDeferredActions: true });

            // Purchase Invoice B.
            const purchaseInvoiceDataB: NodeCreateData<xtremPurchasing.nodes.PurchaseInvoice> = {
                site: '#US001',
                billBySupplier: '#US017',
                currency: '#EUR',
                invoiceDate: date.make(2023, 4, 28),
                matchingStatus: 'variance',
                supplierDocumentNumber,
                totalAmountExcludingTax: 20,
                totalTaxAmount: 5,
                lines: [],
                matchingUser: '<EMAIL>',
            };
            const purchaseInvoiceB = await context.create(xtremPurchasing.nodes.PurchaseInvoice, purchaseInvoiceDataB);
            await purchaseInvoiceB.$.save({ flushDeferredActions: true });

            assert.deepStrictEqual(purchaseInvoiceB.$.context.diagnoses, [
                {
                    severity: ValidationSeverity.warn,
                    path: [],
                    message: 'Supplier document number already exists.',
                },
            ]);
        }));
    it('Create a purchase invoice without lines and post', () =>
        Test.withContext(
            async context => {
                const purchaseInvoice: xtremPurchasing.nodes.PurchaseInvoice = await createPurchaseInvoice(
                    context,
                    date.make(2022, 2, 2),
                );
                await purchaseInvoice.$.set({ lines: [] });
                await purchaseInvoice.$.save({ flushDeferredActions: true });

                assert.deepEqual(
                    (await xtremPurchasing.nodes.PurchaseInvoice.post(context, purchaseInvoice)).message,
                    'You need to add lines to the purchase invoice before you can post it.',
                );
            },
            { today: '2022-02-02' },
        ));

    it('Should reject purchase invoice creation when the site is not valid for purchasing', () =>
        Test.withContext(
            async context => {
                const validPurchaseInvoice = await createPurchaseInvoice(context, date.make(2022, 2, 2));
                const originalSite = await context.read(
                    xtremSystem.nodes.Site,
                    { id: 'ETS1-S01' },
                    { forUpdate: true },
                );
                const firstInvoiceLine = await validPurchaseInvoice.lines.elementAt(0);
                assert.deepEqual(
                    (await firstInvoiceLine.stockSite)._id,
                    originalSite._id,
                    'Stock site should match the original site',
                );
                assert.deepEqual(
                    (await firstInvoiceLine.recipientSite)._id,
                    originalSite._id,
                    'Recipient site should match the original site',
                );

                // Modify the site to make it invalid for purchasing
                await originalSite.$.set({ isPurchase: false, isInventory: false });
                await originalSite.$.save();
                const invalidPurchaseInvoice = await createPurchaseInvoice(context, date.make(2022, 2, 2));
                await assert.isRejected(
                    invalidPurchaseInvoice.$.save(),
                    'The record was not created.',
                    'Saving an invoice with an invalid site should be rejected',
                );

                const expectedErrorMessage = 'The receiving site needs to be either stock or purchasing.';
                const hasExpectedError = invalidPurchaseInvoice.$.context.diagnoses.some(
                    diagnosis => diagnosis.message === expectedErrorMessage,
                );
                assert.isTrue(
                    hasExpectedError,
                    `The diagnoses should include the error message: "${expectedErrorMessage}"`,
                );
            },
            { today: '2022-02-02' },
        ));

    it('Ensure purchase invoices created with default dimensions', () =>
        Test.withContext(async context => {
            // Purchase Invoice A.
            const supplierDocumentNumber = 'INV000000123';
            const purchaseInvoiceData: NodeCreateData<xtremPurchasing.nodes.PurchaseInvoice> = {
                site: '#US001',
                billBySupplier: '#US017',
                currency: '#EUR',
                invoiceDate: date.make(2023, 4, 28),
                supplierDocumentNumber,
                totalAmountExcludingTax: 300,
                totalTaxAmount: 0,
                lines: [
                    {
                        item: '#NonStockManagedItem',
                        quantity: 10,
                        unit: '#KILOGRAM',
                        grossPrice: 30,
                    },
                ],
            };
            const purchaseInvoice = await context.create(xtremPurchasing.nodes.PurchaseInvoice, purchaseInvoiceData);
            await purchaseInvoice.$.save({ flushDeferredActions: true });

            // default dimensions and attributes
            const expectedAttributes = { project: 'AttPROJ', task: 'Task1' };
            const expectedDimensions = {
                dimensionType01: '300',
                dimensionType02: 'CHANNELVALUE1',
            };
            const attributesLine1 = await (await purchaseInvoice.lines.elementAt(0)).storedAttributes;
            const dimensionsLine1 = await (await purchaseInvoice.lines.elementAt(0)).storedDimensions;
            assert.equal(JSON.stringify(attributesLine1), JSON.stringify(expectedAttributes));
            assert.deepEqual(dimensionsLine1, expectedDimensions);
        }));

    it('Purchase invoice refused because of missing attribute restricted to', () =>
        Test.withContext(async context => {
            const supplierDocumentNumber = 'INV0000001234';
            const purchaseInvoiceData: NodeCreateData<xtremPurchasing.nodes.PurchaseInvoice> = {
                site: '#US001',
                billBySupplier: '#US017',
                currency: '#EUR',
                invoiceDate: date.make(2023, 4, 28),
                supplierDocumentNumber,
                totalAmountExcludingTax: 300,
                totalTaxAmount: 0,
                lines: [
                    {
                        item: '#NonStockManagedItem',
                        quantity: 10,
                        unit: '#KILOGRAM',
                        grossPrice: 30,
                        storedAttributes: { employee: '', task: 'TASK1', project: '' },
                    },
                ],
            };
            const purchaseInvoice = await context.create(xtremPurchasing.nodes.PurchaseInvoice, purchaseInvoiceData);
            await assert.isRejected(purchaseInvoice.$.save());

            assert.deepEqual(purchaseInvoice.$.context.diagnoses, [
                {
                    severity: 3,
                    path: ['lines', '-1000000002', 'storedAttributes'],
                    message: 'The Project attribute needs to be filled in.',
                },
            ]);
        }));

    it('Purchase invoice default attributes and dimensions', () =>
        Test.withContext(async context => {
            const supplierDocumentNumber = 'INV0000001234';
            const purchaseInvoiceData: NodeCreateData<xtremPurchasing.nodes.PurchaseInvoice> = {
                site: '#US001',
                billBySupplier: '#US017',
                currency: '#EUR',
                invoiceDate: date.make(2023, 4, 28),
                supplierDocumentNumber,
                totalAmountExcludingTax: 300,
                totalTaxAmount: 0,
                lines: [
                    {
                        item: '#NonStockManagedItem',
                        quantity: 10,
                        unit: '#GRAM',
                        grossPrice: 30,
                    },
                ],
            };
            const purchaseInvoice = await context.create(xtremPurchasing.nodes.PurchaseInvoice, purchaseInvoiceData);
            await purchaseInvoice.$.save();
            assert.deepEqual(await (await purchaseInvoice.lines.elementAt(0)).storedDimensions, {
                dimensionType01: '300',
                dimensionType02: 'CHANNELVALUE1',
            });

            assert.equal(
                JSON.stringify(await (await purchaseInvoice.lines.elementAt(0)).storedAttributes),
                JSON.stringify({
                    project: 'AttPROJ',
                    task: 'Task1',
                }),
            );
        }));

    it('Create a purchase invoice for a receipt line with stock transaction status not completed - fails', () =>
        Test.withContext(
            async context => {
                const newPurchaseInvoice = await context.create(xtremPurchasing.nodes.PurchaseInvoice, {
                    site: '#US001',
                    billBySupplier: '#US017',
                    currency: '#EUR',
                    totalAmountExcludingTax: 33,
                    invoiceDate: date.today(),
                    lines: [
                        {
                            item: '#STOAVC',
                            quantity: 3,
                            unit: '#EACH',
                            grossPrice: 11,
                            purchaseReceiptLine: { purchaseReceiptLine: '#PR240001|10' },
                        },
                    ],
                });
                await assert.isRejected(newPurchaseInvoice.$.save());
                assert.deepEqual(newPurchaseInvoice.$.context.diagnoses, [
                    {
                        severity: ValidationSeverity.error,
                        path: ['lines', '-1000000002', 'purchaseReceiptLine'],
                        message: 'The stock status of the purchase receipt line needs to be Completed.',
                    },
                    {
                        severity: ValidationSeverity.warn,
                        path: ['lines', '-1000000002'],
                        message: 'The item: STOAVC, is not managed for the supplier: US017.',
                    },
                ]);
            },
            { today: '2024-11-22' },
        ));
});
