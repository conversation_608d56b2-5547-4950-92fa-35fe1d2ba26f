import { DateValue, Test } from '@sage/xtrem-core';
import * as xtremFinanceData from '@sage/xtrem-finance-data';
import * as xtremMasterData from '@sage/xtrem-master-data';
import { assert } from 'chai';
import * as sinon from 'sinon';
import * as xtremPurchasing from '../../../index';

/* Test cases
On this test script we want to test 3 cases:
- posting documents
- reposting documents
- resend finance notification for document

Note that in fact the records on accounting staging are no longer created via a notification but directly saved on the database since the accounting staging is now on finance data package
 */

describe('Finance posting for purchase documents', () => {
    it('Purchase order line closing', () =>
        Test.withContext(
            async context => {
                const documentNumber = 'PR240001';

                const documentFilter = {
                    documentNumber,
                    documentType: 'purchaseReceipt',
                    targetDocumentType: 'journalEntry',
                };

                const purchaseReceipt = await context.read(xtremPurchasing.nodes.PurchaseReceipt, {
                    number: documentNumber,
                });
                const purchaseInvoice = await context.read(xtremPurchasing.nodes.PurchaseInvoice, {
                    number: 'PI240003',
                });

                let financeTransactions = await context
                    .query(xtremFinanceData.nodes.FinanceTransaction, { filter: { ...documentFilter } })
                    .toArray();

                assert.equal(financeTransactions.length, 1);
                assert.equal(await financeTransactions.at(0)?.message, '');
                assert.equal(await financeTransactions.at(0)?.status, 'pending');

                let accountingStagingRecords = await context
                    .query(xtremFinanceData.nodes.AccountingStaging, { filter: { ...documentFilter } })
                    .toArray();

                assert.equal(accountingStagingRecords.length, 3);

                await xtremPurchasing.nodes.PurchaseReceipt.onStockReply(context, {
                    requestNotificationId: '8mXw5p3c3J6K1Ulqvi9wf',
                    updateResults: {
                        receipt: { documents: [] },
                        correction: {
                            documents: [
                                {
                                    id: purchaseReceipt._id,
                                    lines: [
                                        {
                                            id: (await purchaseReceipt.lines?.at(0))?._id || 0,
                                            sortValue: 10,
                                            stockUpdateResultStatus: 'corrected',
                                            originLineIds: [(await purchaseInvoice.lines?.at(0))?._id || 0],
                                            stockJournalRecords: [149803],
                                        },
                                    ],
                                },
                            ],
                        },
                    },
                });

                financeTransactions = await context
                    .query(xtremFinanceData.nodes.FinanceTransaction, { filter: { ...documentFilter } })
                    .toArray();

                assert.equal(financeTransactions.length, 2);
                assert.notEqual(await financeTransactions.at(0)?.batchId, await financeTransactions.at(1)?.batchId);
                assert.equal(await financeTransactions.at(0)?.message, '');
                assert.equal(await financeTransactions.at(0)?.status, 'pending');

                accountingStagingRecords = await context
                    .query(xtremFinanceData.nodes.AccountingStaging, { filter: { ...documentFilter } })
                    .toArray();

                assert.equal(accountingStagingRecords.length, 5);

                // Not deleted, it can be yusefull if we want to extend our test
                // const expectedFinanceNotificationPayload = {
                //     documentSysId: purchaseReceipt._id,
                //     batchId: (await financeTransaction?.batchId) || '',
                //     financialSiteSysId: (await purchaseReceipt.financialSite)._id,
                //     documentNumber: 'PR240001',
                //     documentDate: '2024-04-23',
                //     currencySysId: (await purchaseReceipt.transactionCurrency)._id,
                //     batchSize: 2,
                //     documentType: 'purchaseReceipt',
                //     targetDocumentType: 'journalEntry',
                //     sourceLines: [
                //         {
                //             sourceDocumentType: 'purchaseOrder',
                //             sourceDocumentSysId: purchaseOrder._id,
                //             sourceDocumentNumber: 'PO240002',
                //             isSourceForDimension: true,
                //         },
                //     ],
                //     documentLines: [
                //         {
                //             movementType: 'stockJournal',
                //             documentNumber: 'PR240001',
                //             currencySysId: lineCurrencySysId,
                //             companyFxRate: 1,
                //             companyFxRateDivisor: 1,
                //             fxRateDate: '2024-04-23',
                //             itemSysId,
                //             baseDocumentLineSysId: purchaseReceiptLine?._id,
                //             sourceBaseDocumentLineSysId: purchaseInvoiceLine?._id,
                //             sourceDocumentNumber: 'PI240003',
                //             sourceDocumentType: 'purchaseInvoice',
                //             supplierSysId,
                //             uiSourceDocumentNumber: undefined,
                //             amounts: [
                //                 {
                //                     amount: 35.07,
                //                     amountType: 'landedCostAdjustmentAmount',
                //                     documentLineType: 'documentLine',
                //                 },
                //             ],
                //             storedDimensions: {},
                //             storedAttributes: {
                //                 financialSite: 'US001',
                //                 stockSite: 'US001',
                //                 item: 'STOAVC',
                //                 businessSite: 'US001',
                //                 supplier: 'US017',
                //             },
                //         },
                //         {
                //             movementType: 'stockJournal',
                //             documentNumber: 'PR240001',
                //             currencySysId: lineCurrencySysId,
                //             companyFxRate: 1,
                //             companyFxRateDivisor: 1,
                //             fxRateDate: '2024-04-23',
                //             itemSysId,
                //             baseDocumentLineSysId: purchaseInvoiceLine?._id,
                //             sourceBaseDocumentLineSysId: (await purchaseOrder.lines.at(1))?._id,
                //             uiSourceDocumentNumber: 'PO240002',
                //             uiBaseDocumentLineSysId: purchaseReceiptLine?._id,
                //             sourceDocumentNumber: 'PO240002',
                //             sourceDocumentType: 'purchaseOrder',
                //             supplierSysId,
                //             amounts: [
                //                 {
                //                     amount: 35.07,
                //                     amountType: 'landedCostStockInTransitAdjustmentAmount',
                //                     documentLineType: 'documentLine',
                //                 },
                //             ],
                //             storedDimensions: {},
                //             storedAttributes: {
                //                 financialSite: 'US001',
                //                 stockSite: 'US001',
                //                 item: 'STOAVC',
                //                 businessSite: 'US001',
                //                 supplier: 'US017',
                //             },
                //         },
                //     ],
                // };
            },
            {
                testActiveServiceOptions: [xtremMasterData.serviceOptions.landedCostOption],
                today: '2024-04-23',
            },
        ));
    it('Create notifications from a purchase receipt', () =>
        Test.withContext(
            async context => {
                const documentNumber = 'TPR250007';
                const purchaseReceipt = await context.read(
                    xtremPurchasing.nodes.PurchaseReceipt,
                    {
                        number: documentNumber,
                    },
                    { forUpdate: true },
                );

                const documentFilter = {
                    documentNumber,
                    documentType: 'purchaseReceipt',
                    targetDocumentType: 'journalEntry',
                };

                let accountingStagingRecords = await context
                    .query(xtremFinanceData.nodes.AccountingStaging, { filter: { ...documentFilter } })
                    .toArray();

                assert.equal(accountingStagingRecords.length, 0);

                let financeTransactions = await context
                    .query(xtremFinanceData.nodes.FinanceTransaction, { filter: { ...documentFilter } })
                    .toArray();

                assert.equal(financeTransactions.length, 0);

                await xtremPurchasing.nodes.PurchaseReceipt.sendFinanceNotification(context, { purchaseReceipt });

                financeTransactions = await context
                    .query(xtremFinanceData.nodes.FinanceTransaction, { filter: { ...documentFilter } })
                    .toArray();

                assert.equal(financeTransactions.length, 1);
                assert.equal(await financeTransactions.at(0)?.message, '');
                assert.equal(await financeTransactions.at(0)?.status, 'pending');

                accountingStagingRecords = await context
                    .query(xtremFinanceData.nodes.AccountingStaging, { filter: { ...documentFilter } })
                    .toArray();

                assert.equal(accountingStagingRecords.length, 2);
            },
            {
                today: '2021-08-23',
            },
        ));

    it('Create notifications from a  purchase ReceiptLineNotification StockManaged', () =>
        Test.withContext(
            async context => {
                const documentNumber = 'PR250008';
                const purchaseReceipt = await context.read(
                    xtremPurchasing.nodes.PurchaseReceipt,
                    {
                        number: documentNumber,
                    },
                    { forUpdate: true },
                );

                const documentFilter = {
                    documentNumber,
                    documentType: 'purchaseReceipt',
                    targetDocumentType: 'journalEntry',
                };

                let accountingStagingRecords = await context
                    .query(xtremFinanceData.nodes.AccountingStaging, { filter: { ...documentFilter } })
                    .toArray();

                assert.equal(accountingStagingRecords.length, 0);

                let financeTransactions = await context
                    .query(xtremFinanceData.nodes.FinanceTransaction, { filter: { ...documentFilter } })
                    .toArray();

                assert.equal(financeTransactions.length, 0);

                await xtremPurchasing.nodes.PurchaseReceipt.sendFinanceNotification(context, { purchaseReceipt });

                financeTransactions = await context
                    .query(xtremFinanceData.nodes.FinanceTransaction, { filter: { ...documentFilter } })
                    .toArray();

                assert.equal(financeTransactions.length, 1);
                assert.equal(await financeTransactions.at(0)?.message, '');
                assert.equal(await financeTransactions.at(0)?.status, 'pending');

                accountingStagingRecords = await context
                    .query(xtremFinanceData.nodes.AccountingStaging, { filter: { ...documentFilter } })
                    .toArray();

                assert.equal(accountingStagingRecords.length, 1);

                const accountingRecordId = accountingStagingRecords.at(0)?._id;
                const accountingStagingAmounts = await context
                    .query(xtremFinanceData.nodes.AccountingStagingAmount, {
                        filter: { accountingStaging: accountingRecordId },
                    })
                    .toArray();

                assert.equal(accountingStagingAmounts.length, 2);
                assert.equal(await accountingStagingAmounts.at(0)?.amountType, 'amount');
                assert.equal(await accountingStagingAmounts.at(1)?.amountType, 'varianceAmount');
                assert.equal(await accountingStagingAmounts.at(0)?.amount, 40);
                assert.equal(await accountingStagingAmounts.at(1)?.amount, 1160);
            },
            {
                testActiveServiceOptions: [xtremMasterData.serviceOptions.landedCostOption],
                today: '2024-04-23',
            },
        ));
    it('Create notifications from a purchase invoice', () =>
        Test.withContext(
            async context => {
                const documentNumber = 'TPI250015';
                const purchaseInvoice = await context.read(
                    xtremPurchasing.nodes.PurchaseInvoice,
                    {
                        number: documentNumber,
                    },
                    { forUpdate: true },
                );

                const documentFilter = {
                    documentNumber,
                    documentType: 'purchaseInvoice',
                    targetDocumentType: 'accountsPayableInvoice',
                };

                let accountingStagingRecords = await context
                    .query(xtremFinanceData.nodes.AccountingStaging, { filter: { ...documentFilter } })
                    .toArray();

                assert.equal(accountingStagingRecords.length, 0);

                let financeTransactions = await context
                    .query(xtremFinanceData.nodes.FinanceTransaction, { filter: { ...documentFilter } })
                    .toArray();

                assert.equal(financeTransactions.length, 0);

                await xtremPurchasing.nodes.PurchaseInvoice.onceStockCompleted(context, purchaseInvoice);

                financeTransactions = await context
                    .query(xtremFinanceData.nodes.FinanceTransaction, { filter: { ...documentFilter } })
                    .toArray();

                assert.equal(financeTransactions.length, 1);
                assert.equal(await financeTransactions.at(0)?.message, '');
                assert.equal(await financeTransactions.at(0)?.status, 'pending');

                accountingStagingRecords = await context
                    .query(xtremFinanceData.nodes.AccountingStaging, { filter: { ...documentFilter } })
                    .toArray();

                assert.equal(accountingStagingRecords.length, 2);
            },
            {
                today: '2025-04-17',
            },
        ));

    it('Create notifications from a purchase credit memo', () =>
        Test.withContext(
            async context => {
                const documentNumber = 'TPC250003';
                const purchaseCreditMemo = await context.read(
                    xtremPurchasing.nodes.PurchaseCreditMemo,
                    {
                        number: documentNumber,
                    },
                    { forUpdate: true },
                );

                const documentFilter = {
                    documentNumber,
                    documentType: 'purchaseCreditMemo',
                    targetDocumentType: 'accountsPayableInvoice',
                };

                let accountingStagingRecords = await context
                    .query(xtremFinanceData.nodes.AccountingStaging, { filter: { ...documentFilter } })
                    .toArray();

                assert.equal(accountingStagingRecords.length, 0);

                let financeTransactions = await context
                    .query(xtremFinanceData.nodes.FinanceTransaction, { filter: { ...documentFilter } })
                    .toArray();

                assert.equal(financeTransactions.length, 0);

                await xtremPurchasing.nodes.PurchaseCreditMemo.onceStockCompleted(context, purchaseCreditMemo);

                financeTransactions = await context
                    .query(xtremFinanceData.nodes.FinanceTransaction, { filter: { ...documentFilter } })
                    .toArray();

                assert.equal(financeTransactions.length, 1);
                assert.equal(await financeTransactions.at(0)?.message, '');
                assert.equal(await financeTransactions.at(0)?.status, 'pending');

                accountingStagingRecords = await context
                    .query(xtremFinanceData.nodes.AccountingStaging, { filter: { ...documentFilter } })
                    .toArray();

                assert.equal(accountingStagingRecords.length, 2);
            },
            {
                today: '2025-04-17',
            },
        ));

    it('Finance posting for a purchase return', () =>
        Test.withContext(
            async context => {
                const documentNumber = 'TPT250002';
                const purchaseReturn = await context.read(
                    xtremPurchasing.nodes.PurchaseReturn,
                    {
                        number: documentNumber,
                    },
                    { forUpdate: true },
                );

                await xtremPurchasing.nodes.PurchaseReturn.onceStockCompleted(context, purchaseReturn);

                const documentFilter = {
                    documentNumber,
                    documentType: 'purchaseReturn',
                    targetDocumentType: 'journalEntry',
                };

                const financeTransactions = await context
                    .query(xtremFinanceData.nodes.FinanceTransaction, { filter: { ...documentFilter } })
                    .toArray();

                assert.equal(financeTransactions.length, 1);
                assert.equal(await financeTransactions.at(0)?.message, '');
                assert.equal(await financeTransactions.at(0)?.status, 'pending');

                const accountingStagingRecords = await context
                    .query(xtremFinanceData.nodes.AccountingStaging, { filter: { ...documentFilter } })
                    .toArray();

                assert.equal(accountingStagingRecords.length, 2);
            },
            {
                today: '2021-08-23',
            },
        ));
});

describe('Finance repost for purchase documents', () => {
    async function getExpectedPC250008RepostPayload(
        purchaseCreditMemo: xtremPurchasing.nodes.PurchaseCreditMemo,
        paymentTerm: xtremMasterData.nodes.PaymentTerm,
        financeTransaction?: xtremFinanceData.nodes.FinanceTransaction,
        unchangedLine?: xtremPurchasing.nodes.PurchaseCreditMemoLine,
    ) {
        return {
            batchId: (await financeTransaction?.batchId) ?? '',
            documentSysId: purchaseCreditMemo._id,
            documentNumber: 'PC250002',
            financialSiteSysId: (await purchaseCreditMemo.financialSite)._id,
            isJustForPost: true,
            batchSize: 2,
            documentType: 'purchaseCreditMemo',
            targetDocumentType: 'accountsPayableInvoice',
            supplierDocumentDate: '2025-02-24',
            supplierDocumentNumber: 'new_number',
            dueDate: '2025-04-25',
            paymentTerm,
            documentLines: [
                {
                    baseDocumentLineSysId: (await purchaseCreditMemo.lines.at(0))?._id ?? 0,
                    storedDimensions: {},
                    storedAttributes: {
                        businessSite: 'US001',
                        financialSite: 'US001',
                        item: 'Service001',
                        stockSite: 'US001',
                        supplier: 'US016',
                        project: 'AttPROJ2',
                        task: 'Task2',
                        employee: '',
                    },
                },
                {
                    baseDocumentLineSysId: unchangedLine?._id ?? 0,
                    storedDimensions: (await unchangedLine?.storedDimensions) ?? {},
                    storedAttributes: {
                        ...((await unchangedLine?.storedAttributes) as xtremMasterData.interfaces.StoredAttributes),
                        ...((await unchangedLine?.computedAttributes) as {}),
                    },
                },
            ],
        };
    }

    async function getExpectedPI250008RepostPayload(
        purchaseInvoice: xtremPurchasing.nodes.PurchaseInvoice,
        paymentTerm: xtremMasterData.nodes.PaymentTerm,
        financeTransaction?: xtremFinanceData.nodes.FinanceTransaction,
        unchangedLine?: xtremPurchasing.nodes.PurchaseInvoiceLine,
    ) {
        return {
            batchId: (await financeTransaction?.batchId) ?? '',
            documentSysId: purchaseInvoice._id,
            documentNumber: 'PI250008',
            financialSiteSysId: (await purchaseInvoice.financialSite)._id,
            batchSize: 2,
            isJustForPost: true,
            documentType: 'purchaseInvoice',
            targetDocumentType: 'accountsPayableInvoice',
            supplierDocumentDate: '2025-02-24',
            supplierDocumentNumber: 'new_number',
            dueDate: '2025-03-26',
            paymentTerm,
            documentLines: [
                {
                    baseDocumentLineSysId: (await purchaseInvoice.lines.at(0))?._id ?? 0,
                    storedDimensions: {},
                    storedAttributes: {
                        businessSite: 'US001',
                        financialSite: 'US001',
                        item: 'Service001',
                        supplier: 'US016',
                        project: 'AttPROJ',
                        task: 'Task1',
                        employee: '',
                    },
                },
                {
                    baseDocumentLineSysId: unchangedLine?._id ?? 0,
                    storedDimensions: (await unchangedLine?.storedDimensions) ?? {},
                    storedAttributes: {
                        ...((await unchangedLine?.storedAttributes) as xtremMasterData.interfaces.StoredAttributes),
                        ...((await unchangedLine?.computedAttributes) as {}),
                    },
                },
            ],
        };
    }

    it('Purchase credit memo - repost', () =>
        Test.withContext(
            async context => {
                const documentNumber = 'PC250002';
                const purchaseCreditMemo = await context.read(
                    xtremPurchasing.nodes.PurchaseCreditMemo,
                    {
                        number: documentNumber,
                    },
                    { forUpdate: true },
                );

                const filter = {
                    documentNumber,
                    documentType: 'purchaseCreditMemo',
                    targetDocumentType: 'accountsPayableInvoice',
                };

                const financeTransaction = await context
                    .query(xtremFinanceData.nodes.FinanceTransaction, {
                        filter,
                        forUpdate: true,
                    })
                    .at(0);

                await financeTransaction?.$.set({ documentSysId: purchaseCreditMemo._id });
                await financeTransaction?.$.save();

                let accountingStagingRecords = context.query(xtremFinanceData.nodes.AccountingStaging, {
                    filter,
                });
                assert.equal(await accountingStagingRecords.length, 2);
                let accountingStagingRecord = await accountingStagingRecords.at(0);
                assert.equal(await (await accountingStagingRecord?.paymentTerm)?.id, 'TEST_NET_30_SUPPLIER');
                assert.deepEqual(await accountingStagingRecord?.storedAttributes, {
                    businessSite: 'US001',
                    financialSite: 'US001',
                    item: 'Service001',
                    project: 'AttPROJ',
                    supplier: 'US016',
                    stockSite: 'US001',
                    task: 'Task1',
                } as any);

                const paymentTerm = await context.read(xtremMasterData.nodes.PaymentTerm, {
                    id: 'TEST_NET_60_ALL',
                });

                const notifySpy = sinon.spy(context, 'notify');

                const firstPurchaseInvoiceLine = await purchaseCreditMemo.lines.at(0);
                const unchangedLine = await purchaseCreditMemo.lines.at(1);

                const repostResult = await xtremPurchasing.nodes.PurchaseCreditMemo.repost(
                    context,
                    purchaseCreditMemo,
                    {
                        header: {
                            supplierDocumentNumber: 'new_number',
                            paymentData: {
                                paymentTerm,
                                supplierDocumentDate: DateValue.make(2025, 2, 24),
                            },
                        },
                        lines: [
                            {
                                _action: 'update',
                                baseDocumentLineSysId: firstPurchaseInvoiceLine?._id ?? 0,
                                storedAttributes: {
                                    project: 'AttPROJ2',
                                    task: 'Task2',
                                    employee: '',
                                } as xtremMasterData.interfaces.StoredAttributes,
                                storedDimensions: {},
                            },
                        ],
                    },
                );

                assert.deepEqual(repostResult, {
                    wasSuccessful: true,
                    message: 'The purchase credit memo was posted.',
                });

                assert.equal(notifySpy.callCount, 1);

                const notificationTopic = notifySpy.args[0][0];
                const notificationPayload = notifySpy.args[0][1];
                const notificationReplyTopic = notifySpy.args[0][2];

                assert.deepEqual(
                    [notificationTopic, JSON.parse(JSON.stringify(notificationPayload)), notificationReplyTopic],
                    [
                        'accountingInterfaceUpdate',
                        JSON.parse(
                            JSON.stringify(
                                await getExpectedPC250008RepostPayload(
                                    purchaseCreditMemo,
                                    paymentTerm,
                                    financeTransaction,
                                    unchangedLine,
                                ),
                            ),
                        ),
                        { replyTopic: 'PurchaseCreditMemo/accountingInterface' },
                    ],
                );

                accountingStagingRecords = context.query(xtremFinanceData.nodes.AccountingStaging, {
                    filter,
                });
                assert.equal(await accountingStagingRecords.length, 2);

                accountingStagingRecord = await accountingStagingRecords.at(0);
                assert.equal(await (await accountingStagingRecord?.paymentTerm)?.id, 'TEST_NET_60_ALL');
                assert.deepEqual(await accountingStagingRecord?.storedAttributes, {
                    businessSite: 'US001',
                    supplier: 'US016',
                    financialSite: 'US001',
                    item: 'Service001',
                    stockSite: 'US001',
                    project: 'AttPROJ2',
                    task: 'Task2',
                    employee: '',
                });
            },
            {
                today: '2025-02-24',
            },
        ));

    it('Purchase order - repost', () =>
        Test.withContext(
            async context => {
                const documentNumber = 'PI240002';

                const purchaseOrder = await context.read(
                    xtremPurchasing.nodes.PurchaseOrder,
                    { number: 'PO240001' },
                    { forUpdate: true },
                );

                const purchaseInvoice = await context.read(xtremPurchasing.nodes.PurchaseInvoice, {
                    number: documentNumber,
                });

                const filter = {
                    documentNumber,
                    documentType: 'purchaseInvoice',
                    targetDocumentType: 'journalEntry',
                };

                const financeTransaction = await context.read(
                    xtremFinanceData.nodes.FinanceTransaction,
                    {
                        batchId: '9a31eebc-f3bb-4d2b-b338-d4dfae33b6ed',
                        documentNumber: 'PI240002',
                        documentType: 'purchaseInvoice',
                        targetDocumentType: 'journalEntry',
                    },
                    { forUpdate: true },
                );
                // as the _id change on purchase documents we have to change the documentSysId linked with this finance transaction
                await financeTransaction.$.set({ documentSysId: purchaseInvoice._id });

                await financeTransaction.$.save();

                let accountingStagingRecords = context.query(xtremFinanceData.nodes.AccountingStaging, {
                    filter,
                });
                assert.equal(await accountingStagingRecords.length, 2);
                let accountingStagingRecord = await accountingStagingRecords.at(0);
                assert.deepEqual(await accountingStagingRecord?.storedAttributes, {
                    businessSite: 'US001',
                    financialSite: 'US001',
                    item: 'STOFIFO',
                    project: 'AttPROJ',
                    supplier: 'US016',
                    stockSite: 'US001',
                } as any);

                const purchaseOrderLines = await purchaseOrder.lines
                    .map(async line => {
                        return {
                            baseDocumentLineSysId: line._id,
                            storedDimensions: (await line.storedDimensions) || {},
                            storedAttributes: {
                                project: 'AttPROJ2',
                            } as xtremMasterData.interfaces.StoredAttributes,
                        };
                    })
                    .toArray();

                const notifySpy = sinon.spy(context, 'notify');
                const repostResult = await xtremPurchasing.nodes.PurchaseOrder.repost(
                    context,
                    purchaseOrder,
                    purchaseOrderLines,
                    false,
                    financeTransaction,
                );

                const purchaseInvoiceLineId = (await purchaseInvoice.lines.at(0))?._id || 0;
                const expectedFinanceNotificationPayload = {
                    batchId: '9a31eebc-f3bb-4d2b-b338-d4dfae33b6ed',
                    documentSysId: purchaseInvoice._id,
                    documentNumber: 'PI240002',
                    financialSiteSysId: (await purchaseInvoice.financialSite)._id,
                    batchSize: 2,
                    documentType: 'purchaseInvoice',
                    targetDocumentType: 'journalEntry',
                    doNotPostOnUpdate: false,
                    isJustForPost: true,
                    documentLines: [
                        {
                            baseDocumentLineSysId: purchaseInvoiceLineId,
                            sourceBaseDocumentLineSysId: (await purchaseOrder.lines.at(0))?._id,
                            storedDimensions: { dimensionType01: '300', dimensionType02: 'CHANNELVALUE1' },
                            storedAttributes: {
                                project: 'AttPROJ2',
                                financialSite: 'US001',
                                stockSite: 'US001',
                                item: 'STOFIFO',
                                businessSite: 'US001',
                                supplier: 'US016',
                            },
                        },
                        {
                            baseDocumentLineSysId: purchaseInvoiceLineId,
                            sourceBaseDocumentLineSysId: (await purchaseOrder.lines.at(1))?._id,
                            storedDimensions: { dimensionType01: '300', dimensionType02: 'CHANNELVALUE1' },
                            storedAttributes: {
                                project: 'AttPROJ2',
                                financialSite: 'US001',
                                stockSite: 'US001',
                                item: 'STOAVC',
                                businessSite: 'US001',
                                supplier: 'US016',
                            },
                        },
                    ],
                    sourceDocumentType: 'materialTracking',
                    sourceDocumentNumber: 'PO240001',
                };

                assert.deepEqual(repostResult, { wasSuccessful: true, message: 'The purchase order was posted.' });

                assert.equal(notifySpy.callCount, 1);

                assert.deepEqual(notifySpy.getCall(0).args[1], expectedFinanceNotificationPayload);

                assert.isTrue(
                    notifySpy.calledWith('accountingInterfaceUpdate', expectedFinanceNotificationPayload, {
                        replyTopic: 'PurchaseOrder/accountingInterface',
                    }),
                    JSON.stringify(notifySpy.args[0]),
                );
                accountingStagingRecords = context.query(xtremFinanceData.nodes.AccountingStaging, {
                    filter,
                });
                assert.equal(await accountingStagingRecords.length, 2);

                accountingStagingRecord = await accountingStagingRecords.at(0);
                assert.deepEqual(await accountingStagingRecord?.storedAttributes, {
                    businessSite: 'US001',
                    supplier: 'US016',
                    financialSite: 'US001',
                    item: 'STOFIFO',
                    stockSite: 'US001',
                    project: 'AttPROJ',
                } as any);
            },
            {
                testActiveServiceOptions: [xtremMasterData.serviceOptions.landedCostOption],
            },
        ));

    it('Purchase invoice - repost', () =>
        Test.withContext(async context => {
            const documentNumber = 'PI250008';

            const purchaseInvoice = await context.read(
                xtremPurchasing.nodes.PurchaseInvoice,
                {
                    number: documentNumber,
                },
                { forUpdate: true },
            );

            const filter = {
                documentNumber,
                documentType: 'purchaseInvoice',
                targetDocumentType: 'accountsPayableInvoice',
            };

            const financeTransaction = await context
                .query(xtremFinanceData.nodes.FinanceTransaction, {
                    filter,
                    forUpdate: true,
                })
                .at(0);

            await financeTransaction?.$.set({ documentSysId: purchaseInvoice._id });
            await financeTransaction?.$.save();

            let accountingStagingRecords = context.query(xtremFinanceData.nodes.AccountingStaging, {
                filter,
            });
            assert.equal(await accountingStagingRecords.length, 2);
            let accountingStagingRecord = await accountingStagingRecords.at(0);
            assert.deepEqual(await accountingStagingRecord?.storedAttributes, {
                businessSite: 'US001',
                financialSite: 'US001',
                item: 'Service001',
                project: 'AttPROJ',
                supplier: 'US016',
                task: 'Task1',
            } as any);

            const paymentTerm = await context.read(xtremMasterData.nodes.PaymentTerm, {
                id: 'TEST_NET_30_SUPPLIER',
            });

            const notifySpy = sinon.spy(context, 'notify');

            const firstPurchaseInvoiceLine = await purchaseInvoice.lines.at(0);
            const unchangedLine = await purchaseInvoice.lines.at(1);

            const repostResult = await xtremPurchasing.nodes.PurchaseInvoice.repost(context, purchaseInvoice, {
                header: {
                    supplierDocumentNumber: 'new_number',
                    paymentData: {
                        paymentTerm,
                        supplierDocumentDate: DateValue.make(2025, 2, 24),
                    },
                },
                lines: [
                    {
                        _action: 'update',
                        baseDocumentLineSysId: firstPurchaseInvoiceLine?._id ?? 0,
                        storedAttributes: {
                            project: 'AttPROJ',
                            task: 'Task1',
                            employee: '',
                        } as xtremMasterData.interfaces.StoredAttributes,
                        storedDimensions: {},
                    },
                ],
            });

            assert.deepEqual(repostResult, { wasSuccessful: true, message: 'The purchase invoice posted.' });

            assert.equal(notifySpy.callCount, 1);

            const notificationTopic = notifySpy.args[0][0];
            const notificationPayload = notifySpy.args[0][1];
            const notificationReplyTopic = notifySpy.args[0][2];

            assert.deepEqual(
                [notificationTopic, JSON.parse(JSON.stringify(notificationPayload)), notificationReplyTopic],
                [
                    'accountingInterfaceUpdate',
                    JSON.parse(
                        JSON.stringify(
                            await getExpectedPI250008RepostPayload(
                                purchaseInvoice,
                                paymentTerm,
                                financeTransaction,
                                unchangedLine,
                            ),
                        ),
                    ),
                    { replyTopic: 'PurchaseInvoice/accountingInterface' },
                ],
            );
            accountingStagingRecords = context.query(xtremFinanceData.nodes.AccountingStaging, {
                filter,
            });
            assert.equal(await accountingStagingRecords.length, 2);

            accountingStagingRecord = await accountingStagingRecords.at(0);
            assert.deepEqual(await accountingStagingRecord?.storedAttributes, {
                businessSite: 'US001',
                supplier: 'US016',
                financialSite: 'US001',
                item: 'Service001',
                project: 'AttPROJ',
                task: 'Task1',
                employee: '',
            } as any);
        }));
});

describe('Finance resend for purchase documents', () => {
    it('Purchase return - Successfully resend notification for finance', () =>
        Test.withContext(
            async context => {
                const documentNumber = 'PT250001';

                const purchaseReturn: xtremPurchasing.nodes.PurchaseReturn = await context.read(
                    xtremPurchasing.nodes.PurchaseReturn,
                    {
                        number: documentNumber,
                    },
                );

                const filter = {
                    documentNumber,
                    documentType: 'purchaseReturn',
                    targetDocumentType: 'journalEntry',
                };

                let amountType = await (
                    await (
                        await context
                            .query(xtremFinanceData.nodes.AccountingStaging, {
                                filter,
                            })
                            .at(0)
                    )?.amounts.at(0)
                )?.amountType;

                let financeTransactionRecord = await context
                    .query(xtremFinanceData.nodes.FinanceTransaction, { filter })
                    .at(0);
                let financeTransactionStatus = await financeTransactionRecord?.status;
                let financeTransactionMessage = await financeTransactionRecord?.message;

                assert.equal(amountType, 'landedCostAdjustmentAmount');
                assert.equal(financeTransactionStatus, 'notRecorded');
                assert.notEqual(financeTransactionMessage, '');

                await xtremPurchasing.nodes.PurchaseReturn.resendNotificationForFinance(context, purchaseReturn);

                amountType = await (
                    await (
                        await context
                            .query(xtremFinanceData.nodes.AccountingStaging, {
                                filter,
                            })
                            .at(0)
                    )?.amounts.at(0)
                )?.amountType;

                financeTransactionRecord = await context
                    .query(xtremFinanceData.nodes.FinanceTransaction, { filter })
                    .at(0);
                financeTransactionStatus = await financeTransactionRecord?.status;
                financeTransactionMessage = await financeTransactionRecord?.message;

                assert.equal(amountType, 'amount');
                assert.equal(financeTransactionStatus, 'pending');
                assert.equal(financeTransactionMessage, '');
            },
            { today: '2025-02-07' },
        ));

    it('Purchase invoice - Successfully resend notification for finance', () =>
        Test.withContext(
            async context => {
                const documentNumber = 'PI250003';

                const purchaseInvoice: xtremPurchasing.nodes.PurchaseInvoice = await context.read(
                    xtremPurchasing.nodes.PurchaseInvoice,
                    { number: 'PI250003' },
                );

                const filter = {
                    documentNumber,
                    documentType: 'purchaseInvoice',
                    targetDocumentType: 'accountsPayableInvoice',
                };

                let accountingStagingRecords = context.query(xtremFinanceData.nodes.AccountingStaging, {
                    filter,
                });

                let financeTransactionRecord = await context
                    .query(xtremFinanceData.nodes.FinanceTransaction, { filter })
                    .at(0);
                let financeTransactionStatus = await financeTransactionRecord?.status;
                let financeTransactionMessage = await financeTransactionRecord?.message;

                assert.equal(await accountingStagingRecords?.length, 0);
                assert.equal(financeTransactionStatus, 'error');
                assert.notEqual(financeTransactionMessage, '');

                const notifySpy = sinon.spy(context, 'notify');

                const result = await xtremPurchasing.nodes.PurchaseInvoice.resendNotificationForFinance(
                    context,
                    purchaseInvoice,
                );
                assert.equal(notifySpy.getCalls().length, 1);
                const notificationTopic = notifySpy.args[0][0];
                const notificationPayload = notifySpy.args[0][1];
                assert.equal(result, true);

                financeTransactionRecord = await context
                    .query(xtremFinanceData.nodes.FinanceTransaction, { filter })
                    .at(0);

                const batchId = (await financeTransactionRecord?.batchId) ?? '';

                const expectedPayload = {
                    filter: `{"batchId":"${batchId}","isProcessed":false}`,
                    journalsCreatedData: false,
                };

                assert.deepEqual(
                    [notificationTopic, JSON.parse(JSON.stringify(notificationPayload))],
                    [
                        'AccountingInterfaceListener/createJournalsFromAccountingStagingJob/start',
                        JSON.parse(JSON.stringify(expectedPayload)),
                    ],
                );

                accountingStagingRecords = context.query(xtremFinanceData.nodes.AccountingStaging, {
                    filter,
                });

                financeTransactionStatus = await financeTransactionRecord?.status;
                financeTransactionMessage = await financeTransactionRecord?.message;

                assert.equal(await accountingStagingRecords.length, 2);
                assert.equal(financeTransactionStatus, 'pending');
                assert.equal(financeTransactionMessage, '');
            },
            { today: '2025-01-13' },
        ));

    it('Purchase credit memo - Successfully resend notification for finance', () =>
        Test.withContext(
            async context => {
                const documentNumber = 'PC250001';

                const purchaseCreditMemo: xtremPurchasing.nodes.PurchaseCreditMemo = await context.read(
                    xtremPurchasing.nodes.PurchaseCreditMemo,
                    {
                        number: documentNumber,
                    },
                );

                const filter = {
                    documentNumber,
                    documentType: 'purchaseCreditMemo',
                    targetDocumentType: 'accountsPayableInvoice',
                };

                let accountingStagingRecords = context.query(xtremFinanceData.nodes.AccountingStaging, {
                    filter,
                });

                let financeTransactionRecord = await context
                    .query(xtremFinanceData.nodes.FinanceTransaction, { filter })
                    .at(0);
                let financeTransactionStatus = await financeTransactionRecord?.status;
                let financeTransactionMessage = await financeTransactionRecord?.message;

                assert.equal(await accountingStagingRecords?.length, 1);
                assert.equal(financeTransactionStatus, 'notRecorded');
                assert.notEqual(financeTransactionMessage, '');

                const notifySpy = sinon.spy(context, 'notify');
                const result = await xtremPurchasing.nodes.PurchaseCreditMemo.resendNotificationForFinance(
                    context,
                    purchaseCreditMemo,
                );
                assert.equal(notifySpy.getCalls().length, 1);
                const notificationTopic = notifySpy.args[0][0];
                const notificationPayload = notifySpy.args[0][1];

                assert.equal(result, true);
                financeTransactionRecord = await context
                    .query(xtremFinanceData.nodes.FinanceTransaction, {
                        filter,
                    })
                    .at(0);

                const batchId = (await financeTransactionRecord?.batchId) ?? '';
                const expectedPayload = {
                    filter: `{"batchId":"${batchId}","isProcessed":false}`,
                    journalsCreatedData: false,
                };

                assert.deepEqual(
                    [notificationTopic, JSON.parse(JSON.stringify(notificationPayload))],
                    [
                        'AccountingInterfaceListener/createJournalsFromAccountingStagingJob/start',
                        JSON.parse(JSON.stringify(expectedPayload)),
                    ],
                );

                accountingStagingRecords = context.query(xtremFinanceData.nodes.AccountingStaging, {
                    filter,
                });

                financeTransactionStatus = await financeTransactionRecord?.status;
                financeTransactionMessage = await financeTransactionRecord?.message;

                assert.equal(await accountingStagingRecords.length, 1);
                assert.equal(financeTransactionStatus, 'pending');
                assert.equal(financeTransactionMessage, '');
            },
            { today: '2025-02-13' },
        ));
});
