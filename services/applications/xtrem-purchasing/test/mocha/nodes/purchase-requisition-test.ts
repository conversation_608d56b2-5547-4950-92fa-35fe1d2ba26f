import { Test, ValidationSeverity, date } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremSystem from '@sage/xtrem-system';
import { assert } from 'chai';
import * as xtremPurchasing from '../../../lib/index';
import { checkRequisitionLInes, requisitionLines1 } from '../../fixtures/lib/functions/purchase-requisition-line';

const { withSequenceNumberContext } = xtremMasterData.functions.testLib;

describe('Purchase requisition node', () => {
    const testDate = '2020-08-10';
    const testDatePlusOneMonth = '2020-09-10';
    it('Create purchase requisition', () =>
        Test.withContext(
            async context => {
                const newPurchaseRequisition = await context.create(xtremPurchasing.nodes.PurchaseRequisition, {
                    requester: '#<EMAIL>',
                    site: '#US001',
                    requestDate: date.make(2020, 8, 10),
                    lines: [{ item: '#Chemical C', quantity: 20, unit: '#GRAM' }],
                });
                await newPurchaseRequisition.$.save();
                assert.deepEqual(newPurchaseRequisition.$.context.diagnoses, []);
            },
            { today: testDate },
        ));
    it('Create order for purchase requisition REQ5', () =>
        Test.withContext(
            async context => {
                const document = await context.read(xtremPurchasing.nodes.PurchaseRequisition, {
                    number: 'REQ5',
                });
                const orders = await xtremPurchasing.nodes.PurchaseRequisition.createPurchaseOrders(context, document);
                assert.deepEqual(orders, []);
                assert.deepEqual(context.diagnoses, []);
            },
            { today: testDate },
        ));
    it('Create order for purchase requisition REQ6', () =>
        Test.withContext(
            async context => {
                const document = await context.read(xtremPurchasing.nodes.PurchaseRequisition, {
                    number: 'REQ6',
                });
                const orders = await xtremPurchasing.nodes.PurchaseRequisition.createPurchaseOrders(context, document);
                assert.deepEqual(orders, []);
                assert.deepEqual(context.diagnoses, []);
            },
            { today: testDate },
        ));
    it('Create order for purchase requisition REQ7 testDate = 2020-08-10', () =>
        Test.withContext(
            async context => {
                const document = await context.read(
                    xtremPurchasing.nodes.PurchaseRequisition,
                    {
                        number: 'REQ7',
                    },
                    { forUpdate: true },
                );
                const [order] = await xtremPurchasing.nodes.PurchaseRequisition.createPurchaseOrders(context, document);
                await context.flushDeferredActions();
                const orderNumber = await order.number;
                assert.equal(orderNumber, 'PO200001');

                const po = await context.read(xtremPurchasing.nodes.PurchaseOrder, {
                    number: orderNumber,
                });
                assert.equal(await po.number, 'PO200001');
                assert.equal(await (await po.lines.elementAt(0)).purchaseRequisitionLines.length, 1);

                assert.isNotNull(await (await document.lines.elementAt(0)).purchaseOrderLines.toArray());
                assert.equal(await (await document.lines.elementAt(0)).lineOrderStatus, 'ordered');
                assert.equal(await (await document.lines.elementAt(0)).orderedPercentage, 100);
                assert.equal(await (await document.lines.elementAt(0)).status, 'closed');
                assert.equal(await document.orderStatus, 'ordered');
                assert.equal(await document.status, 'closed');
                assert.equal(await document.displayStatus, 'ordered');
            },
            { today: testDate },
        ));
    it('Create order for purchase requisition REQ7 testDate = 2020-09-10', () =>
        Test.withContext(
            async context => {
                const document = await context.read(
                    xtremPurchasing.nodes.PurchaseRequisition,
                    {
                        number: 'REQ7',
                    },
                    { forUpdate: true },
                );
                const [order] = await xtremPurchasing.nodes.PurchaseRequisition.createPurchaseOrders(context, document);
                await context.flushDeferredActions();
                const orderNumber = await order.number;
                assert.equal(orderNumber, 'PO200001');

                const po = await context.read(xtremPurchasing.nodes.PurchaseOrder, {
                    number: orderNumber,
                });
                assert.equal(await po.number, 'PO200001');
                assert.equal(await (await po.lines.elementAt(0)).purchaseRequisitionLines.length, 1);

                assert.isNotNull(await (await document.lines.elementAt(0)).purchaseOrderLines.toArray());
                assert.equal(await (await document.lines.elementAt(0)).lineOrderStatus, 'ordered');
                assert.equal(await (await document.lines.elementAt(0)).status, 'closed');
                assert.equal(await document.orderStatus, 'ordered');
                assert.equal(await document.status, 'closed');
            },
            { today: testDatePlusOneMonth },
        ));
    it('Create order for purchase requisition REQ8 testDate = 2020-08-10', () =>
        Test.withContext(
            async context => {
                const document = await context.read(
                    xtremPurchasing.nodes.PurchaseRequisition,
                    {
                        number: 'REQ8',
                    },
                    { forUpdate: true },
                );
                const [order] = await xtremPurchasing.nodes.PurchaseRequisition.createPurchaseOrders(context, document);
                assert.deepEqual(context.diagnoses, [
                    {
                        message: 'The item: Chemical C, is not managed for the supplier: LECLERC.',
                        path: ['lines', '-1000000008'],
                        severity: 2,
                    },
                ]);
                await context.flushDeferredActions();
                const orderNumber = await order.number;
                assert.equal(orderNumber, 'PO200001');

                const po = await context.read(xtremPurchasing.nodes.PurchaseOrder, { number: orderNumber });
                assert.equal(await po.number, 'PO200001');
                assert.equal(await (await po.lines.elementAt(0)).purchaseRequisitionLines.length, 1);

                assert.isNotNull(await (await document.lines.elementAt(1)).purchaseOrderLines.toArray());
                assert.equal(await (await document.lines.elementAt(0)).status, 'pending');
                assert.equal(await (await document.lines.elementAt(0)).lineOrderStatus, 'notOrdered');
                assert.equal(await (await document.lines.elementAt(1)).lineOrderStatus, 'ordered');
                assert.equal(await (await document.lines.elementAt(1)).status, 'closed');
                assert.equal(await document.orderStatus, 'partiallyOrdered');
                assert.equal(await document.status, 'inProgress');
                assert.equal(await document.displayStatus, 'partiallyOrdered');
            },
            { today: testDate },
        ));
    it('Create order for purchase requisition REQ8 testDate = 2020-09-10', () =>
        Test.withContext(
            async context => {
                const document = await context.read(
                    xtremPurchasing.nodes.PurchaseRequisition,
                    {
                        number: 'REQ8',
                    },
                    { forUpdate: true },
                );
                const [order] = await xtremPurchasing.nodes.PurchaseRequisition.createPurchaseOrders(context, document);
                assert.deepEqual(context.diagnoses, [
                    {
                        message: 'The item: Chemical C, is not managed for the supplier: LECLERC.',
                        path: ['lines', '-1000000008'],
                        severity: 2,
                    },
                ]);
                await context.flushDeferredActions();
                const orderNumber = await order.number;
                assert.equal(orderNumber, 'PO200001');

                const po = await context.read(xtremPurchasing.nodes.PurchaseOrder, { number: orderNumber });
                assert.equal(await po.number, 'PO200001');
                assert.equal(await (await po.lines.elementAt(0)).purchaseRequisitionLines.length, 1);

                assert.isNotNull(await (await document.lines.elementAt(1)).purchaseOrderLines.toArray());
                assert.equal(await (await document.lines.elementAt(0)).status, 'pending');
                assert.equal(await (await document.lines.elementAt(0)).lineOrderStatus, 'notOrdered');
                assert.equal(await (await document.lines.elementAt(1)).lineOrderStatus, 'ordered');
                assert.equal(await (await document.lines.elementAt(1)).status, 'closed');
                assert.equal(await document.orderStatus, 'partiallyOrdered');
                assert.equal(await document.status, 'inProgress');
            },
            { today: testDatePlusOneMonth },
        ));
    it('Create order for purchase requisition REQ9 testDate = 2020-08-10', () =>
        Test.withContext(
            async context => {
                const document = await context.read(
                    xtremPurchasing.nodes.PurchaseRequisition,
                    {
                        number: 'REQ9',
                    },
                    { forUpdate: true },
                );
                const orders = await xtremPurchasing.nodes.PurchaseRequisition.createPurchaseOrders(context, document);
                await context.flushDeferredActions();
                const orderNumber = await orders.at(0)?.number;
                assert.equal(orderNumber, 'PO200001');

                const po = await context.read(xtremPurchasing.nodes.PurchaseOrder, { number: orderNumber });
                assert.equal(await po.number, 'PO200001');
                assert.equal(await (await po.lines.elementAt(0)).purchaseRequisitionLines.length, 2);

                assert.isNotNull(await (await document.lines.elementAt(0)).purchaseOrderLines.toArray());
                assert.isNotNull(await (await document.lines.elementAt(1)).purchaseOrderLines.toArray());
                assert.equal(await document.orderStatus, 'partiallyOrdered');
                assert.equal(await document.status, 'inProgress');
                assert.equal(await (await document.lines.elementAt(0)).lineOrderStatus, 'ordered');
                assert.equal(await (await document.lines.elementAt(0)).status, 'closed');
                assert.equal(await (await document.lines.elementAt(1)).lineOrderStatus, 'ordered');
                assert.equal(await (await document.lines.elementAt(1)).status, 'closed');
            },
            { today: testDate },
        ));
    it('Create order for purchase requisition REQ9 testDate = 2020-09-10', () =>
        Test.withContext(
            async context => {
                const document = await context.read(
                    xtremPurchasing.nodes.PurchaseRequisition,
                    {
                        number: 'REQ9',
                    },
                    { forUpdate: true },
                );
                const orders = await xtremPurchasing.nodes.PurchaseRequisition.createPurchaseOrders(context, document);
                await context.flushDeferredActions();
                const orderNumber = await orders.at(0)?.number;
                assert.equal(orderNumber, 'PO200001');

                const po = await context.read(xtremPurchasing.nodes.PurchaseOrder, { number: orderNumber });
                assert.equal(await po.number, 'PO200001');
                assert.equal(await (await po.lines.elementAt(0)).purchaseRequisitionLines.length, 2);

                assert.isNotNull(await (await document.lines.elementAt(0)).purchaseOrderLines.toArray());
                assert.isNotNull(await (await document.lines.elementAt(1)).purchaseOrderLines.toArray());
                assert.equal(await document.orderStatus, 'partiallyOrdered');
                assert.equal(await document.status, 'inProgress');
                assert.equal(await (await document.lines.elementAt(0)).lineOrderStatus, 'ordered');
                assert.equal(await (await document.lines.elementAt(0)).status, 'closed');
                assert.equal(await (await document.lines.elementAt(1)).lineOrderStatus, 'ordered');
                assert.equal(await (await document.lines.elementAt(1)).status, 'closed');
            },
            { today: testDatePlusOneMonth },
        ));
    it('Create order for purchase requisition REQ10 testDate = 2020-08-10', () =>
        Test.withContext(
            async context => {
                const document = await context.read(
                    xtremPurchasing.nodes.PurchaseRequisition,
                    {
                        number: 'REQ10',
                    },
                    { forUpdate: true },
                );
                const [order] = await xtremPurchasing.nodes.PurchaseRequisition.createPurchaseOrders(context, document);
                await context.flushDeferredActions();
                const orderNumber = await order.number;
                assert.equal(orderNumber, 'PO200001');

                const po = await context.read(xtremPurchasing.nodes.PurchaseOrder, {
                    number: orderNumber,
                });
                assert.equal(await po.number, 'PO200001');
                assert.equal(await (await po.lines.elementAt(0)).purchaseRequisitionLines.length, 2);

                assert.isNotNull(await (await document.lines.elementAt(0)).purchaseOrderLines.toArray());
                assert.isNotNull(await (await document.lines.elementAt(1)).purchaseOrderLines.toArray());
                assert.equal(await document.orderStatus, 'partiallyOrdered');
                assert.equal(await document.status, 'inProgress');
                assert.equal(await (await document.lines.elementAt(0)).lineOrderStatus, 'ordered');
                assert.equal(await (await document.lines.elementAt(0)).status, 'closed');
                assert.equal(await (await document.lines.elementAt(1)).lineOrderStatus, 'ordered');
                assert.equal(await (await document.lines.elementAt(1)).status, 'closed');
            },
            { today: testDate },
        ));
    it('Create order for purchase requisition REQ10 testDate = 2020-09-10', () =>
        Test.withContext(
            async context => {
                const document = await context.read(
                    xtremPurchasing.nodes.PurchaseRequisition,
                    {
                        number: 'REQ10',
                    },
                    { forUpdate: true },
                );
                const orders = await xtremPurchasing.nodes.PurchaseRequisition.createPurchaseOrders(context, document);
                await context.flushDeferredActions();
                const orderNumber = await orders.at(0)?.number;
                assert.equal(orderNumber, 'PO200001');

                const po = await context.read(xtremPurchasing.nodes.PurchaseOrder, {
                    number: orderNumber,
                });
                assert.equal(await po.number, 'PO200001');
                assert.equal(await (await po.lines.elementAt(0)).purchaseRequisitionLines.length, 2);

                assert.equal(await (await document.lines.elementAt(0)).purchaseOrderLines.length, 1);
                assert.equal(await (await document.lines.elementAt(1)).purchaseOrderLines.length, 1);
                assert.equal(await document.orderStatus, 'partiallyOrdered');
                assert.equal(await document.status, 'inProgress');
                assert.equal(await (await document.lines.elementAt(0)).lineOrderStatus, 'ordered');
                assert.equal(await (await document.lines.elementAt(0)).status, 'closed');
                assert.equal(await (await document.lines.elementAt(1)).lineOrderStatus, 'ordered');
                assert.equal(await (await document.lines.elementAt(1)).status, 'closed');
            },
            { today: testDatePlusOneMonth },
        ));
    it('Create order for purchase requisition REQ11 testDate = 2020-08-10', () =>
        Test.withContext(
            async context => {
                await checkRequisitionLInes(
                    context,
                    { site: 'US001', supplier: 'LECLERC', currency: '' },
                    requisitionLines1,
                );
                const requisitionNumber = 'REQ11';
                const document = await context.read(
                    xtremPurchasing.nodes.PurchaseRequisition,
                    {
                        number: requisitionNumber,
                    },
                    { forUpdate: true },
                );
                const orders = await xtremPurchasing.nodes.PurchaseRequisition.createPurchaseOrders(context, document);
                assert.deepEqual(context.diagnoses, [
                    {
                        severity: 2,
                        path: ['lines', '-1000000009'],
                        message: 'The item: Aqua, is not managed for the supplier: LECLERC.',
                    },
                ]);
                await context.flushDeferredActions();
                const orderNumber = await orders.at(0)?.number;
                assert.equal(orderNumber, 'PO200001');

                const po = await context.read(xtremPurchasing.nodes.PurchaseOrder, { number: orderNumber });
                assert.equal(await po.number, 'PO200001');
                assert.equal(await (await po.lines.elementAt(0)).purchaseRequisitionLines.length, 2);

                assert.equal(await (await document.lines.elementAt(0)).purchaseOrderLines.length, 1);
                assert.equal(await (await document.lines.elementAt(1)).purchaseOrderLines.length, 1);
                assert.equal(await (await document.lines.elementAt(2)).purchaseOrderLines.length, 1);
                assert.equal(await document.orderStatus, 'partiallyOrdered');
                assert.equal(await document.status, 'inProgress');
                assert.equal(await (await document.lines.elementAt(0)).lineOrderStatus, 'ordered');
                assert.equal(await (await document.lines.elementAt(0)).status, 'closed');
                assert.equal(await (await document.lines.elementAt(1)).lineOrderStatus, 'ordered');
                assert.equal(await (await document.lines.elementAt(1)).status, 'closed');
                assert.equal(await (await document.lines.elementAt(2)).lineOrderStatus, 'ordered');
                assert.equal(await (await document.lines.elementAt(2)).status, 'closed');

                await checkRequisitionLInes(
                    context,
                    { site: 'US001', supplier: 'LECLERC', currency: '' },
                    requisitionLines1.filter(line => !line.startsWith(requisitionNumber)),
                );
            },
            { today: testDate },
        ));
    it('Get filtered ids list for US005 and US017', () =>
        Test.withContext(async context => {
            await checkRequisitionLInes(context, { site: 'US005', supplier: 'US017', currency: '' }, [
                'REQ18|10',
                'REQ18|30',
            ]);
        }));
    it('Create purchase order for purchase requisition - entry parameters testing', () =>
        Test.withContext(async context => {
            const document = await context.read(xtremPurchasing.nodes.PurchaseRequisition, {
                number: 'REQ13',
            });
            const createResult = await xtremPurchasing.nodes.PurchaseRequisition.createPurchaseOrders(
                context,
                document,
            );
            assert.deepEqual(createResult, []);
            assert.deepEqual(context.diagnoses, []);
        }));
    it('Approve purchase requisition - entry parameters testing', () =>
        Test.withContext(async context => {
            const req7 = await context.read(
                xtremPurchasing.nodes.PurchaseRequisition,
                { _id: '#REQ7' },
                { forUpdate: true },
            );
            assert.equal(await req7.approvalStatus, 'approved');
            assert.equal(await req7.status, 'pending');
            // PO5 - ready for processing (only draft accepted)
            await assert.isRejected(xtremPurchasing.nodes.PurchaseRequisition.approve(context, req7, true));
            await assert.isRejected(xtremPurchasing.nodes.PurchaseRequisition.approve(context, req7, false));
        }));
    it('Approve purchase requisition REQ3', () =>
        Test.withContext(async context => {
            const req3 = await context.read(
                xtremPurchasing.nodes.PurchaseRequisition,
                { _id: '#REQ3' },
                { forUpdate: true },
            );

            const approveResult = await xtremPurchasing.nodes.PurchaseRequisition.approve(context, req3, true);
            assert.isTrue(approveResult);

            const document = await context.read(
                xtremPurchasing.nodes.PurchaseRequisition,
                {
                    number: 'REQ7',
                },
                { forUpdate: true },
            );
            // PO5 - ready for processing (only draft accepted)
            await assert.isRejected(xtremPurchasing.nodes.PurchaseRequisition.approve(context, document, true));
            await assert.isRejected(xtremPurchasing.nodes.PurchaseRequisition.approve(context, document, false));
        }));
    it('Approve purchase requisition REQ3', () =>
        Test.withContext(async context => {
            const document = await context.read(
                xtremPurchasing.nodes.PurchaseRequisition,
                { number: 'REQ3' },
                { forUpdate: true },
            );

            const approveResult = await xtremPurchasing.nodes.PurchaseRequisition.approve(context, document, true);
            assert.isTrue(approveResult);

            const requisition = await context.read(xtremPurchasing.nodes.PurchaseRequisition, {
                number: await document.number,
            });

            assert.equal(await requisition.status, 'pending');
            assert.equal(await requisition.approvalStatus, 'approved');
            assert.equal(await requisition.displayStatus, 'approved');

            const line1 = await requisition.lines.at(0);
            const line2 = await requisition.lines.at(1);
            // Line 0
            assert.equal(await line1?.status, 'pending');
            assert.equal(await line1?.approvalStatus, 'approved');
            // Line 1
            assert.equal(await line2?.status, 'pending');
            assert.equal(await line2?.approvalStatus, 'approved');
        }));
    it('Reject purchase requisition REQ3', () =>
        Test.withContext(async context => {
            const requisition = await context.read(
                xtremPurchasing.nodes.PurchaseRequisition,
                { _id: '#REQ3' },
                { forUpdate: true },
            );
            const approveResult = await xtremPurchasing.nodes.PurchaseRequisition.approve(context, requisition, false);
            assert.isTrue(approveResult);

            const purchaseRequisition = await context.read(xtremPurchasing.nodes.PurchaseRequisition, {
                number: 'REQ3',
            });

            assert.equal(await purchaseRequisition.status, 'closed');
            assert.equal(await purchaseRequisition.approvalStatus, 'rejected');
            assert.equal(await purchaseRequisition.displayStatus, 'rejected');

            const lines = await purchaseRequisition.lines.toArray();
            // Line 0
            assert.equal(await lines[0].status, 'closed');
            assert.equal(await lines[0].approvalStatus, 'rejected');
            // Line 1
            assert.equal(await lines[1].status, 'closed');
            assert.equal(await lines[1].approvalStatus, 'rejected');
        }));
    it('Create order for purchase requisition REQ19', () =>
        Test.withContext(
            async context => {
                const document = await context.read(
                    xtremPurchasing.nodes.PurchaseRequisition,
                    {
                        number: 'REQ19',
                    },
                    { forUpdate: true },
                );
                const orders = await xtremPurchasing.nodes.PurchaseRequisition.createPurchaseOrders(context, document);
                await context.flushDeferredActions();
                const orderNumber = await orders.at(0)?.number;
                assert.equal(orderNumber, 'PO200001');

                const po = await context.read(xtremPurchasing.nodes.PurchaseOrder, { number: orderNumber });
                assert.equal(await po.number, 'PO200001');
                assert.equal(await (await po.lines.elementAt(0)).purchaseRequisitionLines.length, 2);

                assert.isNotNull(await (await document.lines.elementAt(0)).purchaseOrderLines.toArray());
                assert.isNotNull(await (await document.lines.elementAt(1)).purchaseOrderLines.toArray());
                assert.equal(await document.orderStatus, 'partiallyOrdered');
                assert.equal(await document.status, 'inProgress');
                assert.equal(await (await document.lines.elementAt(0)).lineOrderStatus, 'ordered');
                assert.equal(await (await document.lines.elementAt(0)).status, 'closed');
                assert.equal(await (await document.lines.elementAt(1)).lineOrderStatus, 'ordered');
                assert.equal(await (await document.lines.elementAt(1)).status, 'closed');
            },
            { today: testDate },
        ));
    it('Create order for purchase requisition REQ20', () =>
        Test.withContext(
            async context => {
                const document = await context.read(
                    xtremPurchasing.nodes.PurchaseRequisition,
                    {
                        number: 'REQ20',
                    },
                    { forUpdate: true },
                );
                const orders = await xtremPurchasing.nodes.PurchaseRequisition.createPurchaseOrders(context, document);
                await context.flushDeferredActions();
                const orderNumber = await orders.at(0)?.number;
                assert.equal(orderNumber, 'PO200001');

                const po = await context.read(xtremPurchasing.nodes.PurchaseOrder, { number: orderNumber });
                assert.equal(await po.number, 'PO200001');
                assert.equal(await (await po.lines.elementAt(0)).purchaseRequisitionLines.length, 1);
                assert.equal(await (await po.lines.elementAt(1)).purchaseRequisitionLines.length, 1);

                assert.isNotNull(await (await document.lines.elementAt(0)).purchaseOrderLines.toArray());
                assert.isNotNull(await (await document.lines.elementAt(1)).purchaseOrderLines.toArray());
                assert.equal(await document.orderStatus, 'partiallyOrdered');
                assert.equal(await document.status, 'inProgress');
                assert.equal(await (await document.lines.elementAt(0)).lineOrderStatus, 'ordered');
                assert.equal(await (await document.lines.elementAt(0)).status, 'closed');
                assert.equal(await (await document.lines.elementAt(1)).lineOrderStatus, 'ordered');
                assert.equal(await (await document.lines.elementAt(1)).status, 'closed');
            },
            { today: testDate },
        ));
    it('Create order for purchase requisition REQ21', () =>
        Test.withContext(
            async context => {
                const document = await context.read(
                    xtremPurchasing.nodes.PurchaseRequisition,
                    {
                        number: 'REQ21',
                    },
                    { forUpdate: true },
                );
                const orders = await xtremPurchasing.nodes.PurchaseRequisition.createPurchaseOrders(context, document);
                await context.flushDeferredActions();
                const orderNumber = await orders.at(0)?.number;
                assert.equal(orderNumber, 'PO200001');

                const po = await context.read(xtremPurchasing.nodes.PurchaseOrder, { number: orderNumber });
                assert.equal(await po.number, 'PO200001');
                assert.equal(await (await po.lines.elementAt(0)).purchaseRequisitionLines.length, 1);
                assert.equal(await (await po.lines.elementAt(1)).purchaseRequisitionLines.length, 1);

                assert.isNotNull(await (await document.lines.elementAt(0)).purchaseOrderLines.toArray());
                assert.isNotNull(await (await document.lines.elementAt(1)).purchaseOrderLines.toArray());
                assert.equal(await document.orderStatus, 'partiallyOrdered');
                assert.equal(await document.status, 'inProgress');
                assert.equal(await (await document.lines.elementAt(0)).lineOrderStatus, 'ordered');
                assert.equal(await (await document.lines.elementAt(0)).status, 'closed');
                assert.equal(await (await document.lines.elementAt(1)).lineOrderStatus, 'ordered');
                assert.equal(await (await document.lines.elementAt(1)).status, 'closed');
            },
            { today: testDate },
        ));
    it('Verify chronological control between two purchase requisition on creation', () =>
        withSequenceNumberContext(
            'PurchaseRequisition',
            { isChronological: true },
            async context => {
                const newPurchaseRequisition = await context.create(xtremPurchasing.nodes.PurchaseRequisition, {
                    requester: '#<EMAIL>',
                    site: '#US001',
                    requestDate: date.make(2020, 8, 9),
                    lines: [{ item: '#Chemical C', quantity: 20, unit: '#GRAM' }],
                });
                await newPurchaseRequisition.$.save({ flushDeferredActions: true });
                assert.deepEqual(newPurchaseRequisition.$.context.diagnoses, []);

                const newPurchaseRequisitionError = await context.create(xtremPurchasing.nodes.PurchaseRequisition, {
                    requester: '#<EMAIL>',
                    site: '#US001',
                    requestDate: date.make(2020, 8, 8),
                    lines: [{ item: '#Chemical C', quantity: 20, unit: '#GRAM' }],
                });
                await assert.isRejected(newPurchaseRequisitionError.$.save(), 'The record was not created.');
                assert.deepEqual(newPurchaseRequisitionError.$.context.diagnoses, [
                    {
                        severity: 3,
                        path: [],
                        message: 'The document date 2020-08-08 is earlier than the previous document date 2020-08-09.',
                    },
                ]);
            },
            { today: '2020-08-09' },
        ));
    it('Verify chronological control between two purchase requisition on modification', () =>
        withSequenceNumberContext(
            'PurchaseRequisition',
            { isChronological: true },
            async context => {
                const newPurchaseRequisition = await context.create(xtremPurchasing.nodes.PurchaseRequisition, {
                    requester: '#<EMAIL>',
                    site: '#US001',
                    requestDate: date.make(2020, 8, 9),
                    lines: [{ item: '#Chemical C', quantity: 20, unit: '#GRAM' }],
                });
                await newPurchaseRequisition.$.save({ flushDeferredActions: true });
                assert.deepEqual(newPurchaseRequisition.$.context.diagnoses, []);

                const newPurchaseRequisitionError = await context.create(xtremPurchasing.nodes.PurchaseRequisition, {
                    requester: '#<EMAIL>',
                    site: '#US001',
                    requestDate: date.make(2020, 8, 8),
                    lines: [{ item: '#Chemical C', quantity: 20, unit: '#GRAM' }],
                });
                await assert.isRejected(newPurchaseRequisitionError.$.save(), 'The record was not created.');
                assert.deepEqual(newPurchaseRequisitionError.$.context.diagnoses, [
                    {
                        severity: 3,
                        path: [],
                        message: 'The document date 2020-08-08 is earlier than the previous document date 2020-08-09.',
                    },
                ]);
            },
            { today: '2020-08-09' },
        ));

    it('Ensure confirmed purchase requisitions with all lines closed go into closed state', () =>
        Test.withContext(async context => {
            // Enable PR confirmation.
            const site = await context.read(xtremSystem.nodes.Site, { id: 'ETS1-S01' }, { forUpdate: true });
            await site.$.set({ isPurchaseRequisitionApprovalManaged: false });
            await site.$.save();

            // Create PR with 2 lines.
            let purchaseRequisition = await context.create(xtremPurchasing.nodes.PurchaseRequisition, {
                site: 'ETS1-S01',
                lines: [
                    {
                        item: 'StockItem01',
                        unit: 'GRAM',
                        quantity: 10,
                    },
                    {
                        item: 'StockItem02',
                        unit: 'GRAM',
                        quantity: 20,
                    },
                ],
            });
            await purchaseRequisition.$.save();
            const purchaseRequisitionId = purchaseRequisition._id;
            // rollbackCache function will flush deferred actions and saves
            await Test.rollbackCache(context);

            purchaseRequisition = await context.read(
                xtremPurchasing.nodes.PurchaseRequisition,
                {
                    _id: purchaseRequisitionId,
                },
                { forUpdate: true },
            );

            // Confirm PR.
            await xtremPurchasing.nodes.PurchaseRequisition.confirm(context, purchaseRequisition);

            // Check status - should be pending.
            purchaseRequisition = await context.read(
                xtremPurchasing.nodes.PurchaseRequisition,
                {
                    number: await purchaseRequisition.number,
                },
                { forUpdate: true },
            );
            assert.deepStrictEqual(await purchaseRequisition.status, 'pending');

            // Close 1st line.
            await xtremPurchasing.nodes.PurchaseRequisitionLine.close(await purchaseRequisition.lines.elementAt(0));

            // Delete 2nd line.
            await purchaseRequisition.lines.delete(1);
            await purchaseRequisition.$.save();

            // Check status - should be closed.
            purchaseRequisition = await context.read(xtremPurchasing.nodes.PurchaseRequisition, {
                number: await purchaseRequisition.number,
            });
            assert.deepStrictEqual(await purchaseRequisition.status, 'closed');
        }));
    it('Create purchase requisition - refuse requisition with landed cost item', () =>
        Test.withContext(
            async context => {
                const newPurchaseRequisition = await context.create(xtremPurchasing.nodes.PurchaseRequisition, {
                    requester: '#<EMAIL>',
                    site: '#US003',
                    requestDate: date.make(2020, 8, 10),
                    lines: [
                        {
                            item: '#LandedCost001',
                            quantity: 10,
                            quantityInStockUnit: 10,
                            grossPrice: 30,
                            currency: '#USD',
                            unitToStockUnitConversionFactor: 1,
                        },
                    ],
                });
                await assert.isRejected(newPurchaseRequisition.$.save());
                assert.deepEqual(newPurchaseRequisition.$.context.diagnoses, [
                    {
                        severity: ValidationSeverity.error,
                        path: ['lines', '-1000000002', 'item'],
                        message: 'The record is not valid. You need to select a different record.',
                    },
                    {
                        message: 'A landed cost item cannot be added to the document: PurchaseRequisition.',
                        path: ['lines', '-1000000002', 'item'],
                        severity: 3,
                    },
                ]);
            },
            { today: '2023-01-17' },
        ));
    it('Duplicates a purchase requisition a sets the status to draft', () =>
        Test.withContext(async context => {
            const requisition = await context.read(
                xtremPurchasing.nodes.PurchaseRequisition,
                { number: 'REQ20' },
                { forUpdate: true },
            );

            assert.equal(await requisition.displayStatus, 'approved');

            const duplicateRequisition = await requisition.$.duplicate();

            assert.equal(await duplicateRequisition.displayStatus, 'draft');
        }));
    it('Add new line on confirmed purchase requisition - XT-89429', () =>
        Test.withContext(async context => {
            // Enable PR confirmation.
            const site = await context.read(xtremSystem.nodes.Site, { id: 'ETS1-S01' }, { forUpdate: true });
            await site.$.set({ isPurchaseRequisitionApprovalManaged: false });
            await site.$.save();

            // Create PR with 2 lines.
            let purchaseRequisition = await context.create(xtremPurchasing.nodes.PurchaseRequisition, {
                site: 'ETS1-S01',
                lines: [
                    { _action: 'create', item: 'StockItem01', unit: 'GRAM', quantity: 10 },
                    { _action: 'create', item: 'StockItem02', unit: 'GRAM', quantity: 20 },
                ],
            });

            await purchaseRequisition.$.save();
            const purchaseRequisitionId = purchaseRequisition._id;

            purchaseRequisition = await context.read(
                xtremPurchasing.nodes.PurchaseRequisition,
                { _id: purchaseRequisitionId },
                { forUpdate: true },
            );

            // Confirm PR.
            await xtremPurchasing.nodes.PurchaseRequisition.confirm(context, purchaseRequisition);

            // Check status - should be pending.
            purchaseRequisition = await context.read(
                xtremPurchasing.nodes.PurchaseRequisition,
                { number: await purchaseRequisition.number },
                { forUpdate: true },
            );
            assert.deepStrictEqual(await purchaseRequisition.status, 'pending');

            // Add new line on confirmed purchase requisition
            await purchaseRequisition.$.set({ lines: [{ item: 'StockItem03', unit: 'GRAM', quantity: 30 }] });
            assert.equal(await purchaseRequisition.lines.length, 1);
        }));
    it('Add line to draft purchase requisition - XT-89430', () =>
        Test.withContext(async context => {
            const req3 = await context.read(
                xtremPurchasing.nodes.PurchaseRequisition,
                { _id: '#REQ3' },
                { forUpdate: true },
            );
            assert.equal(await req3.status, 'draft');

            // Add new lines on confirmed purchase requisition
            await req3.$.set({
                lines: [
                    { _action: 'create', item: 'Chemical D', unit: 'KILOGRAM', quantity: 10 },
                    { _action: 'create', item: 'Chemical D', unit: 'KILOGRAM', quantity: 20 },
                ],
            });
            await req3.$.save();

            assert.equal(await req3.lines.length, 4);
        }));
    it('Reject created purchase requisition without line', () =>
        Test.withContext(
            async context => {
                const newPurchaseRequisition = await context.create(xtremPurchasing.nodes.PurchaseRequisition, {
                    requester: '#<EMAIL>',
                    site: '#US001',
                    requestDate: date.make(2020, 8, 10),
                });
                await assert.isRejected(newPurchaseRequisition.$.save());
                assert.deepEqual(newPurchaseRequisition.$.context.diagnoses, [
                    {
                        severity: ValidationSeverity.error,
                        path: [],
                        message: 'The document needs at least one line.',
                    },
                ]);
            },
            { today: testDate },
        ));

    it('should return 0 when supplier is missing', () =>
        Test.withContext(
            async () => {
                const mockRequisitionLine = {
                    item: '#Chemical C',
                    unit: '#GRAM',
                    currency: '#USD',
                    quantity: 1,
                } as unknown as xtremPurchasing.nodes.PurchaseRequisitionLine;
                const price = await xtremPurchasing.functions.getPrice(mockRequisitionLine);
                assert.equal(price, 0);
            },
            { today: testDate },
        ));
});
