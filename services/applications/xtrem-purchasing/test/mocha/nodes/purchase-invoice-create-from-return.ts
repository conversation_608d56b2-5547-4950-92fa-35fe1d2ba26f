import { date, Test } from '@sage/xtrem-core';
import { assert } from 'chai';
import * as xtremPurchasing from '../../../lib';

describe('Purchase return node - create invoice from a return', () => {
    it('Create purchase invoice for purchase return - entry parameters testing', () =>
        Test.withContext(async context => {
            const ret14 = await context.read(xtremPurchasing.nodes.PurchaseReturn, { number: 'RET014' });
            await assert.isRejected(
                xtremPurchasing.nodes.PurchaseReturn.createPurchaseInvoice(context, ret14),
                'The purchase return is already closed.',
            );
        }));
    it('Create invoice for purchase return RET022 - 2 pr lines on 1 pi line', () =>
        Test.withContext(
            async context => {
                const ret22 = await context.read(xtremPurchasing.nodes.PurchaseReturn, { number: 'RET022' });
                assert.equal(await ret22.status, 'pending');
                assert.equal(await ret22.approvalStatus, 'approved');
                const [invoice] = await xtremPurchasing.nodes.PurchaseReturn.createPurchaseInvoice(context, ret22);
                await context.flushDeferredActions();
                assert.equal(await invoice.number, 'PI200001');
                assert.equal(await invoice.lines.length, 1);
            },
            { today: '2020-11-11' },
        ));
    it('Create invoice for purchase return RET023 - 2 pr lines on 2 pi lines (different purchase unit)', () =>
        Test.withContext(
            async context => {
                const ret23 = await context.read(xtremPurchasing.nodes.PurchaseReturn, { number: 'RET023' });
                const [pi] = await xtremPurchasing.nodes.PurchaseReturn.createPurchaseInvoice(context, ret23);
                await context.flushDeferredActions();
                assert.equal(await pi.number, 'PI200001');
                assert.equal(await pi.lines.length, 2);
            },
            { today: '2020-11-11' },
        ));
    it('Create invoice for purchase return RET024 - 1 pr line on 1 pi line', () =>
        Test.withContext(
            async context => {
                const ret26 = await context.read(xtremPurchasing.nodes.PurchaseReturn, { number: 'RET026' });
                const [pi] = await xtremPurchasing.nodes.PurchaseReturn.createPurchaseInvoice(context, ret26);
                await context.flushDeferredActions();
                assert.equal(await pi.number, 'PI200001');
                assert.equal(await pi.lines.length, 1);
            },
            { today: '2020-11-11' },
        ));
    it('Create purchase return and then fully invoice it in a row', () =>
        Test.withContext(async context => {
            let newPurchaseReturn = await context.create(xtremPurchasing.nodes.PurchaseReturn, {
                returnSite: '#US001',
                businessRelation: '#LECLERC',
                currency: '#EUR',
                returnRequestDate: date.today(),
                lines: [
                    {
                        item: '#Muesli',
                        quantity: 10,
                        unit: '#KILOGRAM',
                        grossPrice: 30,
                        reason: { id: 'R1' },
                        purchaseReceiptLine: {
                            purchaseReceiptLine: 1034,
                            returnedQuantity: 10,
                            returnedQuantityInStockUnit: 10000,
                        },
                    },
                ],
            });
            await newPurchaseReturn.$.save({ flushDeferredActions: true });

            // Submitted for approval
            await newPurchaseReturn.$.set({ approvalStatus: 'pendingApproval', status: 'pending' });
            await newPurchaseReturn.lines.forEach(async line => {
                await line.$.set({ status: 'pending', approvalStatus: 'pendingApproval' });
            });
            await newPurchaseReturn.$.save();

            // Approved
            await newPurchaseReturn.$.set({ approvalStatus: 'approved', status: 'inProgress' });
            await newPurchaseReturn.lines.forEach(async line => {
                await line.$.set({ status: 'inProgress', approvalStatus: 'approved' });
            });
            await newPurchaseReturn.$.save();

            const [purchaseInvoice] = await xtremPurchasing.nodes.PurchaseReturn.createPurchaseInvoice(
                context,
                newPurchaseReturn,
            );

            assert.equal(await purchaseInvoice.lines.length, 1);
            const purchaseInvoiceLine = await purchaseInvoice.lines.elementAt(0);
            assert.equal(await purchaseInvoiceLine.purchaseReturnLines.length, 1);
            assert.equal(
                (await (await purchaseInvoiceLine.purchaseReturnLines.elementAt(0)).invoicedQuantity).toString(),
                (await (await newPurchaseReturn.lines.elementAt(0)).quantity).toString(),
            );
            assert.equal(
                (
                    await (
                        await purchaseInvoiceLine.purchaseReturnLines.elementAt(0)
                    ).invoicedQuantityInStockUnit
                ).toString(),
                (await (await newPurchaseReturn.lines.elementAt(0)).quantityInStockUnit).toString(),
            );
            newPurchaseReturn = await context.read(xtremPurchasing.nodes.PurchaseReturn, {
                _id: newPurchaseReturn._id,
            });
            assert.equal(await (await newPurchaseReturn.lines.elementAt(0)).lineInvoiceStatus, 'invoiced');
            assert.equal(await newPurchaseReturn.status, 'closed');
        }));
    it('Create purchase return and then partially invoice it in a row', () =>
        Test.withContext(async context => {
            let newPurchaseReturn = await context.create(xtremPurchasing.nodes.PurchaseReturn, {
                returnSite: '#US001',
                businessRelation: '#LECLERC',
                currency: '#EUR',
                returnRequestDate: date.today(),
                lines: [
                    {
                        item: '#Muesli',
                        quantity: 10,
                        unit: '#KILOGRAM',
                        grossPrice: 30,
                        reason: { id: 'R1' },
                        purchaseReceiptLine: {
                            purchaseReceiptLine: 1034,
                            returnedQuantity: 10,
                            returnedQuantityInStockUnit: 10000,
                        },
                    },
                ],
            });
            await newPurchaseReturn.$.save({ flushDeferredActions: true });

            // Submitted for approval
            await newPurchaseReturn.$.set({ approvalStatus: 'pendingApproval' });
            await newPurchaseReturn.$.set({ status: 'pending' });
            await newPurchaseReturn.lines.forEach(async line => {
                await line.$.set({ status: 'pending' });
                await line.$.set({ approvalStatus: 'pendingApproval' });
            });
            await newPurchaseReturn.$.save();

            // Approved
            await newPurchaseReturn.$.set({ approvalStatus: 'approved' });
            await newPurchaseReturn.$.set({ status: 'inProgress' });
            await newPurchaseReturn.lines.forEach(async line => {
                await line.$.set({ status: 'inProgress' });
                await line.$.set({ approvalStatus: 'approved' });
            });
            await newPurchaseReturn.$.save();

            const purchaseInvoice = await context.create(xtremPurchasing.nodes.PurchaseInvoice, {
                site: '#US001',
                billBySupplier: '#LECLERC',
                currency: '#EUR',
                invoiceDate: date.today(),
                totalAmountExcludingTax: await newPurchaseReturn.totalAmountExcludingTax,
                lines: [
                    {
                        item: '#Muesli',
                        quantity: 6,
                        unit: '#KILOGRAM',
                        grossPrice: 30,
                        purchaseReturnLines: [
                            {
                                purchaseReturnLine: (await newPurchaseReturn.lines.elementAt(0))._id,
                                invoicedQuantity: 6,
                            },
                        ],
                    },
                ],
            });
            await purchaseInvoice.$.save();

            assert.equal(await purchaseInvoice.lines.length, 1);
            const purchaseInvoiceLine = await purchaseInvoice.lines.elementAt(0);
            assert.equal(await purchaseInvoiceLine.purchaseReturnLines.length, 1);
            assert.equal(
                (await (await purchaseInvoiceLine.purchaseReturnLines.elementAt(0)).invoicedQuantity).toString(),
                '6',
            );
            assert.equal(
                (
                    await (
                        await purchaseInvoiceLine.purchaseReturnLines.elementAt(0)
                    ).invoicedQuantityInStockUnit
                ).toString(),
                '6000',
            );

            newPurchaseReturn = await context.read(xtremPurchasing.nodes.PurchaseReturn, {
                _id: newPurchaseReturn._id,
            });
            assert.equal((await (await newPurchaseReturn.lines.elementAt(0)).invoicedQuantity).toString(), '6');
            assert.equal(await (await newPurchaseReturn.lines.elementAt(0)).lineInvoiceStatus, 'partiallyInvoiced');
            assert.equal('inProgress', await newPurchaseReturn.status);
        }));
    it('Create purchase return and then partially and fully invoice it in a row', () =>
        Test.withContext(
            async context => {
                let newPurchaseReturn = await context.create(xtremPurchasing.nodes.PurchaseReturn, {
                    returnSite: '#US001',
                    businessRelation: '#LECLERC',
                    currency: '#EUR',
                    returnRequestDate: date.make(2020, 8, 10),
                    lines: [
                        {
                            item: '#Muesli',
                            quantity: 10,
                            unit: '#KILOGRAM',
                            grossPrice: 30,
                            reason: { id: 'R1' },
                            purchaseReceiptLine: {
                                purchaseReceiptLine: 1034,
                                returnedQuantity: 10,
                                returnedQuantityInStockUnit: 10000,
                            },
                        },
                    ],
                });
                await newPurchaseReturn.$.save({ flushDeferredActions: true });

                // Submitted for approval
                await newPurchaseReturn.$.set({ approvalStatus: 'pendingApproval' });
                await newPurchaseReturn.$.set({ status: 'pending' });
                await newPurchaseReturn.lines.forEach(async line => {
                    await line.$.set({ status: 'pending' });
                    await line.$.set({ approvalStatus: 'pendingApproval' });
                });
                await newPurchaseReturn.$.save();

                // Approved
                await newPurchaseReturn.$.set({ approvalStatus: 'approved' });
                await newPurchaseReturn.$.set({ status: 'inProgress' });
                await newPurchaseReturn.lines.forEach(async line => {
                    await line.$.set({ status: 'inProgress' });
                    await line.$.set({ approvalStatus: 'approved' });
                });
                await newPurchaseReturn.$.save();
                let purchaseInvoice = await context.create(xtremPurchasing.nodes.PurchaseInvoice, {
                    site: '#US001',
                    billBySupplier: '#LECLERC',
                    currency: '#EUR',
                    invoiceDate: date.today(),
                    totalAmountExcludingTax: await newPurchaseReturn.totalAmountExcludingTax,
                    lines: [
                        {
                            item: '#Muesli',
                            quantity: 7,
                            unit: '#KILOGRAM',
                            grossPrice: 30,
                            purchaseReturnLines: [
                                {
                                    purchaseReturnLine: (await newPurchaseReturn.lines.elementAt(0))._id,
                                    invoicedQuantity: 7,
                                },
                            ],
                        },
                    ],
                });
                await purchaseInvoice.$.save();

                assert.equal(await purchaseInvoice.lines.length, 1);
                const purchaseInvoiceLine = await purchaseInvoice.lines.elementAt(0);
                assert.equal(await purchaseInvoiceLine.purchaseReturnLines.length, 1);
                assert.equal(
                    (await (await purchaseInvoiceLine.purchaseReturnLines.elementAt(0)).invoicedQuantity).toString(),
                    '7',
                );
                assert.equal(
                    (
                        await (
                            await purchaseInvoiceLine.purchaseReturnLines.elementAt(0)
                        ).invoicedQuantityInStockUnit
                    ).toString(),
                    '7000',
                );

                newPurchaseReturn = await context.read(xtremPurchasing.nodes.PurchaseReturn, {
                    _id: newPurchaseReturn._id,
                });
                assert.equal(await (await newPurchaseReturn.lines.elementAt(0)).lineInvoiceStatus, 'partiallyInvoiced');
                assert.equal(await newPurchaseReturn.status, 'inProgress');

                newPurchaseReturn = await context.read(xtremPurchasing.nodes.PurchaseReturn, {
                    _id: newPurchaseReturn._id,
                });
                purchaseInvoice = await context.create(xtremPurchasing.nodes.PurchaseInvoice, {
                    site: '#US001',
                    billBySupplier: '#LECLERC',
                    currency: '#EUR',
                    invoiceDate: date.today(),
                    totalAmountExcludingTax: await newPurchaseReturn.totalAmountExcludingTax,
                    lines: [
                        {
                            item: '#Muesli',
                            quantity: 3,
                            unit: '#KILOGRAM',
                            grossPrice: 30,
                            purchaseReturnLines: [
                                {
                                    purchaseReturnLine: (await newPurchaseReturn.lines.elementAt(0))._id,
                                    invoicedQuantity: 3,
                                },
                            ],
                        },
                    ],
                });
                await purchaseInvoice.$.save();

                assert.equal(await purchaseInvoice.lines.length, 1);
                assert.equal(await (await purchaseInvoice.lines.elementAt(0)).purchaseReturnLines.length, 1);
                assert.equal(
                    (
                        await (
                            await (await purchaseInvoice.lines.elementAt(0)).purchaseReturnLines.elementAt(0)
                        ).invoicedQuantity
                    ).toString(),
                    '3',
                );
                assert.equal(
                    (
                        await (
                            await (await purchaseInvoice.lines.elementAt(0)).purchaseReturnLines.elementAt(0)
                        ).invoicedQuantityInStockUnit
                    ).toString(),
                    '3000',
                );

                newPurchaseReturn = await context.read(xtremPurchasing.nodes.PurchaseReturn, {
                    _id: newPurchaseReturn._id,
                });
                assert.equal(await (await newPurchaseReturn.lines.elementAt(0)).lineInvoiceStatus, 'invoiced');
                assert.equal(await newPurchaseReturn.status, 'closed');
            },
            { today: '2020-08-10' },
        ));
});
