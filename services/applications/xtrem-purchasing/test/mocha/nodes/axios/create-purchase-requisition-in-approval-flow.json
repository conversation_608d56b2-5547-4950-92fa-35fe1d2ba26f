{"request": {"method": "POST", "url": "https://api.sendgrid.com/v3/mail/send", "headers": {"Content-Type": "application/json"}, "data": {"personalizations": [{"to": [{"email": "<EMAIL>", "name": "Green, rachel"}], "subject": "[Purchase requisition PQ200001] approval request"}], "from": {"name": "sender.name", "email": "<EMAIL>"}, "content": [{"type": "text/plain", "value": "\n        \n            :root {\n                --background: #FFFFFF;\n                --black55: rgba(0, 0, 0, 0.55);\n                --black65: rgba(0, 0, 0, 0.65);\n                --black90: rgba(0, 0, 0, 0.90);\n                --error: #C7384F;\n                --gold: #FFB500;\n                --info: #0077C8;\n                --logo: #00DC00;\n                --slate: #003349;\n                --slate20: #335C6D;\n                --slate40: #668592;\n                --slate60: #99ADB6;\n                --slate80: #CCD6DB;\n                --slate90: #E5EAEC;\n                --slate95: #F2F5F6;\n                --success: #00B000;\n                --tableSeparator: #D9E0E4;\n                --textAndLabels: rgba(0, 0, 0, 0.85);\n                --themePrimary: #008A21;\n                --themePrimaryHover: #005C9A;\n                --warning: #E96400;\n            }\n\n            @media print { body { -webkit-print-color-adjust: exact; } }\n            body {\n                font-size: 12pt;\n            }\n\n            h1,h2,h3,h4,th{\n                color: var(--themePrimary);\n            }\n\n            @page {\n                size: auto;\n                margin: 15mm 15mm 15mm 15mm;\n            }\n\n            #footer-header {\n                margin: 15mm 15mm 15mm 15mm;\n                font-size: 8pt;\n            }\n\n            .xtrem-page-break {\n                page-break-after: always;\n            }\n\n            h1{\n  margin-top:20px;\n  margin-left:10px;\n}\n\n\ntable .header {\n  text-align: center;\n}\n\ntable .content {\n  text-align: left;\n  background-color: #f2f5f6;\n  padding: 32px 32px 32px 32px;\n  border-collapse:collapse;\n  font-family: AdelleSansSAGE, Arial, Roboto, Segoe UI, Helvetica Neue;\n  font-weight: 300;\n  color: #191919;\n  font-size: 16px;\n  line-height: 1.5;\n}\n\ntable .approveButton{\n  width: 300px;\n  text-align: center;\n  background-color: #009900;\n}\n\ntable .rejectButton{\n  width: 300px;\n  text-align: center;\n  background-color: #99001c;\n}\n        \n    \n    \n    \n        \n            \n                \n                    \n                        Purchase requisition approval for PQ200001\n                    \n                \n            \n            \n                \n                    Hello,\n                    \n                    \n                    You have a pending approval for: Purchase requisition PQ200001 requested by Test, Unit from Chem. Irvine on 2020-08-10.\n                    \n                    You can find details for this request by following this link: Click here\n                    \n                    \n                    Please use the following buttons to approve or reject the request.\n                    \n                    \n                    \n                        \n                            \n                                \n                                    \n                                        \n                                            \n                                                \n                                                    Approve\n                                                \n                                                \n                                                \n                                                    Reject\n                                                \n                                            \n                                        \n                                    \n                                \n                            \n                        \n                    \n                    \n                    \n                    Sincerely,\n                    Chem. Irvine\n                    \n                    \n                    The information contained in this email transmission may constitute confidential information. If you are not the intended recipient, please take notice that reuse of the information is prohibited.\n                    \n                \n            \n        \n    \n\n\n    "}, {"type": "text/html", "value": "<!DOCTYPE html><html><head><meta charset=\"utf-8\">\n        <style>\n            :root {\n                --background: #FFFFFF;\n                --black55: rgba(0, 0, 0, 0.55);\n                --black65: rgba(0, 0, 0, 0.65);\n                --black90: rgba(0, 0, 0, 0.90);\n                --error: #C7384F;\n                --gold: #FFB500;\n                --info: #0077C8;\n                --logo: #00DC00;\n                --slate: #003349;\n                --slate20: #335C6D;\n                --slate40: #668592;\n                --slate60: #99ADB6;\n                --slate80: #CCD6DB;\n                --slate90: #E5EAEC;\n                --slate95: #F2F5F6;\n                --success: #00B000;\n                --tableSeparator: #D9E0E4;\n                --textAndLabels: rgba(0, 0, 0, 0.85);\n                --themePrimary: #008A21;\n                --themePrimaryHover: #005C9A;\n                --warning: #E96400;\n            }\n\n            @media print { body { -webkit-print-color-adjust: exact; } }\n            body {\n                font-size: 12pt;\n            }\n\n            h1,h2,h3,h4,th{\n                color: var(--themePrimary);\n            }\n\n            @page {\n                size: auto;\n                margin: 15mm 15mm 15mm 15mm;\n            }\n\n            #footer-header {\n                margin: 15mm 15mm 15mm 15mm;\n                font-size: 8pt;\n            }\n\n            .xtrem-page-break {\n                page-break-after: always;\n            }\n\n            h1{\n  margin-top:20px;\n  margin-left:10px;\n}\n\n\ntable .header {\n  text-align: center;\n}\n\ntable .content {\n  text-align: left;\n  background-color: #f2f5f6;\n  padding: 32px 32px 32px 32px;\n  border-collapse:collapse;\n  font-family: AdelleSansSAGE, Arial, Roboto, Segoe UI, Helvetica Neue;\n  font-weight: 300;\n  color: #191919;\n  font-size: 16px;\n  line-height: 1.5;\n}\n\ntable .approveButton{\n  width: 300px;\n  text-align: center;\n  background-color: #009900;\n}\n\ntable .rejectButton{\n  width: 300px;\n  text-align: center;\n  background-color: #99001c;\n}\n        </style>\n    </head>\n    <body class=\"ck ck-content\"><div>\n    <table>\n        <tbody>\n            <tr>\n                <td class=\"header\">\n                    <h1>\n                        Purchase requisition approval for <span>PQ200001</span>\n                    </h1>\n                </td>\n            </tr>\n            <tr>\n                <td class=\"content\">\n                    Hello,\n                    <br>\n                    <br>\n                    You have a pending approval for: <h4>Purchase requisition <span>PQ200001</span> requested by <span>Test, Unit</span> from <span>Chem. Irvine</span> on <span>2020-08-10</span>.</h4>\n                    <br>\n                    You can find details for this request by following this link: <a href=\"http://localhost:8240/@sage/xtrem-purchasing/PurchaseRequisition/eyJfaWQiOiIyNSJ9\" style=\"font-weight: bold; text-decoration: underline; color:#008200;\"><span>Click here</span></a>\n                    <br>\n                    <br>\n                    Please use the following buttons to approve or reject the request.\n                    <br>\n                    <br>\n                    <table cellpadding=\"0\" cellspacing=\"0\" border=\"0\" width=\"260\" style=\"width: 260px; text-align: center\">\n                        <tbody>\n                            <tr>\n                                <td align=\"center\">\n                                    <table width=\"296\" border=\"0\" cellspacing=\"0\" cellpadding=\"0\" style=\"width: 296px; height: 36px; text-align: center; -webkit-text-size-adjust:none\">\n                                        <tbody>\n                                            <tr>\n                                                <td class=\"approveButton\">\n                                                    <a href=\"http://localhost:8240/@sage/xtrem-purchasing/PurchaseRequisitionApproval/eyJwdXJjaGFzZURvY3VtZW50TnVtYmVyIjoiMjUiLCJzdGF0dXMiOiJhcHByb3ZlZCJ9\" style=\"font-weight: bold !important; color: #ffffff; text-decoration:none; display: inline-block\">Approve</a>\n                                                </td>\n                                                <td style=\"width: 232px;\"></td>\n                                                <td class=\"rejectButton\">\n                                                    <a href=\"http://localhost:8240/@sage/xtrem-purchasing/PurchaseRequisitionApproval/eyJwdXJjaGFzZURvY3VtZW50TnVtYmVyIjoiMjUiLCJzdGF0dXMiOiJyZWplY3RlZCJ9\" style=\"font-weight: bold !important; color: #ffffff; text-decoration:none; display: inline-block\">Reject</a>\n                                                </td>\n                                            </tr>\n                                        </tbody>\n                                    </table>\n                                </td>\n                            </tr>\n                        </tbody>\n                    </table>\n                    <br>\n                    <br>\n                    Sincerely,<br>\n                    Chem. Irvine\n                    <br>\n                    <br>\n                    The information contained in this email transmission may constitute confidential information. If you are not the intended recipient, please take notice that reuse of the information is prohibited.\n                    <br><br>\n                </td>\n            </tr>\n        </tbody>\n    </table>\n</div>\n\n    </body></html>"}], "mail_settings": {"bcc": {"enable": false}, "footer": {"enable": false}, "sandbox_mode": {"enable": false}, "check_spam": {"enable": false}}}}, "response": {"headers": {"isMock": true}, "data": {"statusCode": "202", "results": ""}}}