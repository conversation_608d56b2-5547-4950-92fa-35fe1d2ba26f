import { date, Test } from '@sage/xtrem-core';
import { assert } from 'chai';
import * as xtremPurchasing from '../../../lib/index';
import * as TestHelper from '../../fixtures/lib/functions';

describe('Purchase return node update', () => {
    // Skip this test until we determine if returns can be updated.
    it.skip('Update purchase return', () =>
        Test.withContext(async context => {
            let purchaseReturn = await context.read(
                xtremPurchasing.nodes.PurchaseReturn,
                { number: 'RET011' },
                { forUpdate: true },
            );
            const purchaseReturnLine = await purchaseReturn.lines.elementAt(0);
            assert.deepEqual((await purchaseReturnLine.quantity).toString(), '150');
            await purchaseReturnLine.$.set({ quantity: 250 });
            assert.isTrue(await purchaseReturn.$.control());
            /* does not throw */ await (() => purchaseReturn.$.save())();
            purchaseReturn = await context.read(
                xtremPurchasing.nodes.PurchaseReturn,
                { number: 'RET011' },
                { forUpdate: true },
            );
            assert.deepEqual((await (await purchaseReturn.lines.elementAt(0)).quantity).toString(), '250');
            assert.deepEqual((await (await purchaseReturn.lines.elementAt(0)).quantityInStockUnit).toString(), '25000');
        }));
});

describe('Purchase return node  update', () => {
    const testDate = '2020-08-10';

    it('Update purchase return after save stays in draft status', () =>
        Test.withContext(
            async context => {
                const return25Update = await context.read(
                    xtremPurchasing.nodes.PurchaseReturn,
                    { number: 'RET025' },
                    { forUpdate: true },
                );
                assert.equal(await return25Update.status, 'draft');

                const firstLine = await return25Update.lines.takeOne(async line => (await line._sortValue) === 1);

                assert.isNotNull(firstLine);
                assert.isNotNull(firstLine.purchaseInvoiceLines);
                /** Not invoiced */
                assert.lengthOf(await firstLine.purchaseInvoiceLines.toArray(), 0);

                assert.isNotNull(await firstLine.purchaseReceiptLine);

                assert.equal(await (await firstLine.purchaseReceiptLine).returnedQuantity, 15);

                await return25Update.$.set({ lines: [{ _action: 'update', _sortValue: 1, quantity: 3 }] });
                await return25Update.$.save();

                const return25 = await context.read(
                    xtremPurchasing.nodes.PurchaseReturn,
                    { number: 'RET025' },
                    { forUpdate: true },
                );

                assert.equal(await return25.status, 'draft');
                xtremPurchasing.functions.loggers.return.debug(() => `Return saved`);
            },
            { today: testDate },
        ));

    it('Update purchase return RET006 with 2 receipt partial lines PR20 and PR21', () =>
        Test.withContext(
            async context => {
                const receipt20 = await context.read(xtremPurchasing.nodes.PurchaseReceipt, { number: 'PR20' });
                const receipt21 = await context.read(xtremPurchasing.nodes.PurchaseReceipt, { number: 'PR21' });

                const receiptLine1 = await receipt20.lines.elementAt(0);
                const receiptLine2 = await receipt21.lines.elementAt(0);

                let purchaseReturn = await context.read(
                    xtremPurchasing.nodes.PurchaseReturn,
                    { number: 'RET006' },
                    { forUpdate: true },
                );
                await purchaseReturn.$.set({
                    lines: [
                        {
                            item: '#Muesli',
                            grossPrice: 100,
                            unit: '#KILOGRAM',
                            quantity: 7,
                            stockUnit: '#GRAM',
                            quantityInStockUnit: 7000,
                            expectedReturnDate: date.make(2020, 8, 10),
                            purchaseReceiptLine: {
                                purchaseReceiptLine: receiptLine1._id,
                                returnedQuantity: 7,
                                returnedQuantityInStockUnit: 7000,
                            },
                            reason: { id: 'R1' },
                            _action: 'create',
                        },
                        {
                            item: '#Chemical C',
                            grossPrice: 100,
                            unit: '#KILOGRAM',
                            quantity: 8,
                            stockUnit: '#GRAM',
                            quantityInStockUnit: 8000,
                            expectedReturnDate: date.make(2020, 8, 10),
                            purchaseReceiptLine: {
                                purchaseReceiptLine: receiptLine2._id,
                                returnedQuantity: 8,
                                returnedQuantityInStockUnit: 8000,
                            },
                            reason: { id: 'R1' },
                            _action: 'create',
                        },
                    ],
                });
                await purchaseReturn.$.save();
                await context.flushDeferredSaves();

                Test.assertDeepEqualDiagnoses(purchaseReturn.$.context.diagnoses, [
                    {
                        message: 'The item: Chemical C, is not managed for the supplier: LECLERC.',
                        path: ['lines', '<???>'],
                        severity: 2,
                    },
                    {
                        message:
                            'While saving PurchaseCreditMemo(<???>): The item: Chemical C, is not managed for the supplier: LECLERC.',
                        path: [],
                        severity: 2,
                    },
                    {
                        message:
                            'While saving PurchaseCreditMemo(<???>): The item: Chemical C, is not managed for the supplier: LECLERC.',
                        path: [],
                        severity: 2,
                    },
                    {
                        message: `While saving PurchaseReceipt(${receipt21._id}): The item: Chemical C, is not managed for the supplier: LECLERC.`,
                        path: [],
                        severity: 2,
                    },
                ]);

                // rollbackCache function will flush deferred actions and saves
                await Test.rollbackCache(context);

                const pr20 = await context.read(xtremPurchasing.nodes.PurchaseReceipt, { number: 'PR20' });
                assert.isNotNull(await (await pr20.lines.elementAt(0)).purchaseReturnLines.toArray());
                assert.equal(await pr20.returnStatus, 'partiallyReturned');
                assert.equal(await (await pr20.lines.elementAt(0)).lineReturnStatus, 'partiallyReturned');
                assert.equal((await (await pr20.lines.elementAt(0)).returnedQuantity).toString(), '7');
                assert.equal((await (await pr20.lines.elementAt(0)).returnedQuantityInStockUnit).toString(), '7000');
                assert.equal((await (await pr20.lines.elementAt(0)).remainingReturnQuantity).toString(), '3');
                assert.equal((await (await pr20.lines.elementAt(0)).remainingQuantityToInvoice).toString(), '10');
                assert.equal((await (await pr20.lines.elementAt(0)).remainingQuantityInStockUnit).toString(), '3000');
                const pr21 = await context.read(xtremPurchasing.nodes.PurchaseReceipt, { number: 'PR21' });
                assert.isNotNull(await (await pr21.lines.elementAt(0)).purchaseReturnLines.toArray());
                assert.equal((await (await pr21.lines.elementAt(0)).returnedQuantity).toString(), '8');
                assert.equal((await (await pr21.lines.elementAt(0)).returnedQuantityInStockUnit).toString(), '8000');
                assert.equal((await (await pr21.lines.elementAt(0)).remainingReturnQuantity).toString(), '2');
                assert.equal((await (await pr21.lines.elementAt(0)).remainingQuantityToInvoice).toString(), '10');
                assert.equal((await (await pr21.lines.elementAt(0)).remainingQuantityInStockUnit).toString(), '2000');
                assert.equal(await pr21.returnStatus, 'partiallyReturned');
                assert.equal(await (await pr21.lines.elementAt(0)).lineReturnStatus, 'partiallyReturned');
                assert.equal(await (await pr21.lines.elementAt(1)).lineReturnStatus, 'notReturned');
                assert.equal(await (await pr21.lines.elementAt(1)).lineReturnStatus, 'notReturned');

                purchaseReturn = await context.read(
                    xtremPurchasing.nodes.PurchaseReturn,
                    { number: 'RET006' },
                    { forUpdate: true },
                );
                await purchaseReturn.$.set({
                    lines: [
                        {
                            item: '#Muesli',
                            grossPrice: 100,
                            unit: '#KILOGRAM',
                            quantity: 3,
                            stockUnit: '#GRAM',
                            quantityInStockUnit: 3000,
                            expectedReturnDate: date.make(2020, 8, 10),
                            purchaseReceiptLine: { purchaseReceiptLine: receiptLine1._id, returnedQuantity: 3 },
                            reason: { id: 'R1' },
                            _action: 'create',
                        },
                        {
                            item: '#Chemical C',
                            grossPrice: 100,
                            unit: '#KILOGRAM',
                            quantity: 2,
                            stockUnit: '#GRAM',
                            quantityInStockUnit: 2000,
                            expectedReturnDate: date.make(2020, 8, 10),
                            purchaseReceiptLine: { purchaseReceiptLine: receiptLine2._id, returnedQuantity: 2 },
                            reason: { id: 'R1' },
                            _action: 'create',
                        },
                    ],
                });
                await purchaseReturn.$.save();
                await context.flushDeferredSaves();

                Test.assertDeepEqualDiagnoses(purchaseReturn.$.context.diagnoses, [
                    {
                        message: 'The item: Chemical C, is not managed for the supplier: LECLERC.',
                        path: ['lines', '<???>'],
                        severity: 2,
                    },
                    {
                        message:
                            'While saving PurchaseCreditMemo(<???>): The item: Chemical C, is not managed for the supplier: LECLERC.',
                        path: [],
                        severity: 2,
                    },
                    {
                        message:
                            'While saving PurchaseCreditMemo(<???>): The item: Chemical C, is not managed for the supplier: LECLERC.',
                        path: [],
                        severity: 2,
                    },
                    {
                        message:
                            'While saving PurchaseCreditMemo(<???>): The item: Chemical C, is not managed for the supplier: LECLERC.',
                        path: [],
                        severity: 2,
                    },
                    {
                        message: `While saving PurchaseReceipt(${receipt21._id}): The item: Chemical C, is not managed for the supplier: LECLERC.`,
                        path: [],
                        severity: 2,
                    },
                ]);

                await Test.rollbackCache(context);

                const pr15bis = await context.read(xtremPurchasing.nodes.PurchaseReceipt, { number: 'PR20' });
                let lines = await pr15bis.lines.toArray();
                assert.isNotNull(await lines[0].purchaseReturnLines.toArray());
                assert.equal(await pr15bis.returnStatus, 'returned');
                assert.equal(await lines[0].lineReturnStatus, 'returned');
                // assert.equal(lines[0].returnedQuantity.toString(), '10');
                // assert.equal(lines[0].returnedQuantityInStockUnit.toString(), '10000');
                // assert.equal(lines[0].remainingQuantity.toString(), '0');
                // assert.equal(lines[0].remainingQuantityInStockUnit.toString(), '0');
                const pr16bis = await context.read(xtremPurchasing.nodes.PurchaseReceipt, { number: 'PR21' });
                lines = await pr16bis.lines.toArray();
                assert.isNotNull(await lines[0].purchaseReturnLines.toArray());
                assert.equal(await pr16bis.returnStatus, 'partiallyReturned');
                assert.equal(await lines[0].lineReturnStatus, 'returned');
                // assert.equal(lines[0].returnedQuantity.toString(), '10');
                // assert.equal(lines[0].returnedQuantityInStockUnit.toString(), '10000');
                // assert.equal(lines[0].remainingQuantity.toString(), '0');
                // assert.equal(lines[0].remainingQuantityInStockUnit.toString(), '0');

                assert.equal(await lines[1].lineReturnStatus, 'notReturned');
                // assert.equal(lines[1].returnedQuantity.toString(), '0');
                // assert.equal(lines[1].returnedQuantityInStockUnit.toString(), '0');
                // assert.equal(lines[1].remainingQuantity.toString(), '10');
                // assert.equal(lines[1].remainingQuantityInStockUnit.toString(), '10000');
                assert.equal(await lines[1].lineReturnStatus, 'notReturned');
            },
            { today: testDate },
        ));
    it('Update purchase return RET006 with 1 order partial lines PR20 and PR21 then delete related PR20 return line using po.$.set', () =>
        Test.withContext(
            async context => {
                const receiptLineIds = await TestHelper.PurchaseReceipt.simulateReceiptPostToStock(context, [
                    'PR20',
                    'PR21',
                ]);
                assert.equal(receiptLineIds.length, 2);

                const receipt21 = await context.read(xtremPurchasing.nodes.PurchaseReceipt, { number: 'PR21' });

                let purchaseReturn = await context.read(
                    xtremPurchasing.nodes.PurchaseReturn,
                    { number: 'RET006' },
                    { forUpdate: true },
                );
                await purchaseReturn.$.set({
                    lines: [
                        {
                            item: '#Muesli',
                            grossPrice: 100,
                            unit: '#KILOGRAM',
                            quantity: 7,
                            stockUnit: '#GRAM',
                            quantityInStockUnit: 7000,
                            expectedReturnDate: date.make(2020, 8, 10),
                            purchaseReceiptLine: { purchaseReceiptLine: receiptLineIds[0], returnedQuantity: 7 },

                            reason: { id: 'R1' },
                            _action: 'create',
                        },
                        {
                            item: '#Chemical C',
                            grossPrice: 100,
                            unit: '#KILOGRAM',
                            quantity: 8,
                            stockUnit: '#GRAM',
                            quantityInStockUnit: 8000,
                            expectedReturnDate: date.make(2020, 8, 10),
                            purchaseReceiptLine: { purchaseReceiptLine: receiptLineIds[1], returnedQuantity: 8 },
                            reason: { id: 'R1' },
                            _action: 'create',
                        },
                    ],
                });
                assert.equal(await purchaseReturn.lines.length, 3);
                await purchaseReturn.$.save();
                await context.flushDeferredSaves();

                Test.assertDeepEqualDiagnoses(purchaseReturn.$.context.diagnoses, [
                    {
                        message: 'The item: Chemical C, is not managed for the supplier: LECLERC.',
                        path: ['lines', '<???>'],
                        severity: 2,
                    },
                    {
                        message:
                            'While saving PurchaseCreditMemo(<???>): The item: Chemical C, is not managed for the supplier: LECLERC.',
                        path: [],
                        severity: 2,
                    },
                    {
                        message:
                            'While saving PurchaseCreditMemo(<???>): The item: Chemical C, is not managed for the supplier: LECLERC.',
                        path: [],
                        severity: 2,
                    },
                    {
                        message: `While saving PurchaseReceipt(${receipt21._id}): The item: Chemical C, is not managed for the supplier: LECLERC.`,
                        path: [],
                        severity: 2,
                    },
                ]);

                // rollbackCache function will flush deferred actions and saves
                await Test.rollbackCache(context);

                purchaseReturn = await context.read(xtremPurchasing.nodes.PurchaseReturn, { number: 'RET006' });
                const lineToDeleteId = (await purchaseReturn.lines.elementAt(1))._id;
                const pr15 = await context.read(xtremPurchasing.nodes.PurchaseReceipt, { number: 'PR20' });
                assert.isNotNull(await (await pr15.lines.elementAt(0)).purchaseReturnLines.toArray());
                assert.equal(await pr15.returnStatus, 'partiallyReturned');
                assert.equal(await (await pr15.lines.elementAt(0)).lineReturnStatus, 'partiallyReturned');
                assert.equal((await (await pr15.lines.elementAt(0)).returnedQuantity).toString(), '7');
                assert.equal((await (await pr15.lines.elementAt(0)).returnedQuantityInStockUnit).toString(), '7000');
                assert.equal((await (await pr15.lines.elementAt(0)).remainingQuantityToInvoice).toString(), '10');
                assert.equal((await (await pr15.lines.elementAt(0)).remainingQuantityInStockUnit).toString(), '3000');

                purchaseReturn = await context.read(
                    xtremPurchasing.nodes.PurchaseReturn,
                    { number: 'RET006' },
                    { forUpdate: true },
                );
                await purchaseReturn.$.set({
                    lines: [
                        {
                            _id: lineToDeleteId,
                            _action: 'delete',
                        },
                    ],
                });
                assert.equal(await purchaseReturn.lines.length, 2);
                await purchaseReturn.$.save();
                await context.flushDeferredSaves();

                Test.assertDeepEqualDiagnoses(purchaseReturn.$.context.diagnoses, [
                    {
                        message:
                            'While saving PurchaseCreditMemo(<???>): The item: Chemical C, is not managed for the supplier: LECLERC.',
                        path: [],
                        severity: 2,
                    },
                    {
                        message:
                            'While saving PurchaseCreditMemo(<???>): The item: Chemical C, is not managed for the supplier: LECLERC.',
                        path: [],
                        severity: 2,
                    },
                ]);

                await Test.rollbackCache(context);

                const pr15bis = await context.read(xtremPurchasing.nodes.PurchaseReceipt, { number: 'PR20' });
                assert.isNotNull(await (await pr15bis.lines.elementAt(0)).purchaseReturnLines.toArray());
                assert.equal(await pr15bis.returnStatus, 'notReturned');
                assert.equal(await (await pr15bis.lines.elementAt(0)).lineReturnStatus, 'notReturned');
                // assert.equal(pr15bis.lines.elementAt(0).returnedQuantity.toString(), '0');
                // assert.equal(pr15bis.lines.elementAt(0).returnedQuantityInStockUnit.toString(), '10000');
                // assert.equal(pr15bis.lines.elementAt(0).remainingQuantity.toString(), '10');
                // assert.equal(pr15bis.lines.elementAt(0).remainingQuantityInStockUnit.toString(), '10000');

                purchaseReturn = await context.read(
                    xtremPurchasing.nodes.PurchaseReturn,
                    { number: 'RET006' },
                    { forUpdate: true },
                );
                await (await purchaseReturn.lines.elementAt(1)).$.delete();
                await context.flushDeferredSaves();

                assert.deepEqual(purchaseReturn.$.context.diagnoses, [
                    {
                        message: `While saving PurchaseReceipt(82): The item: Chemical C, is not managed for the supplier: LECLERC.`,
                        path: [],
                        severity: 2,
                    },
                ]);

                await Test.rollbackCache(context);

                const pr16bis = await context.read(xtremPurchasing.nodes.PurchaseReceipt, { number: 'PR21' });
                assert.isNotNull(await (await pr16bis.lines.elementAt(0)).purchaseReturnLines.toArray());
                assert.equal(await pr16bis.returnStatus, 'notReturned');
                assert.equal(await (await pr16bis.lines.elementAt(0)).lineReturnStatus, 'notReturned');
            },
            { today: testDate },
        ));
    it('Update purchase return RET006 with 2 order partial lines PR20 and PR21 then fully delete the return using po.$.delete()', () =>
        Test.withContext(
            async context => {
                const receiptLineIds = await TestHelper.PurchaseReceipt.simulateReceiptPostToStock(context, [
                    'PR20',
                    'PR21',
                ]);
                assert.equal(receiptLineIds.length, 2);

                const receipt21 = await context.read(xtremPurchasing.nodes.PurchaseReceipt, { number: 'PR21' });

                let purchaseReturn = await context.read(
                    xtremPurchasing.nodes.PurchaseReturn,
                    { number: 'RET006' },
                    { forUpdate: true },
                );
                await purchaseReturn.$.set({
                    lines: [
                        {
                            item: '#Muesli',
                            grossPrice: 100,
                            unit: '#KILOGRAM',
                            quantity: 7, // in purchase unit
                            stockUnit: '#GRAM',
                            quantityInStockUnit: 7000,
                            expectedReturnDate: date.make(2020, 8, 10),
                            purchaseReceiptLine: { purchaseReceiptLine: receiptLineIds[0], returnedQuantity: 7 },
                            reason: { id: 'R1' },
                            _action: 'create',
                        },
                        {
                            item: '#Chemical C',
                            grossPrice: 100,
                            unit: '#KILOGRAM',
                            quantity: 8,
                            stockUnit: '#GRAM',
                            quantityInStockUnit: 8000,
                            expectedReturnDate: date.make(2020, 8, 10),
                            purchaseReceiptLine: { purchaseReceiptLine: receiptLineIds[1], returnedQuantity: 8 },
                            reason: { id: 'R1' },
                            _action: 'create',
                        },
                    ],
                });
                assert.equal(await purchaseReturn.lines.length, 3);
                await purchaseReturn.$.save();
                await context.flushDeferredSaves();

                Test.assertDeepEqualDiagnoses(purchaseReturn.$.context.diagnoses, [
                    {
                        message: 'The item: Chemical C, is not managed for the supplier: LECLERC.',
                        path: ['lines', '<???>'],
                        severity: 2,
                    },
                    {
                        message:
                            'While saving PurchaseCreditMemo(<???>): The item: Chemical C, is not managed for the supplier: LECLERC.',
                        path: [],
                        severity: 2,
                    },
                    {
                        message:
                            'While saving PurchaseCreditMemo(<???>): The item: Chemical C, is not managed for the supplier: LECLERC.',
                        path: [],
                        severity: 2,
                    },
                    {
                        message: `While saving PurchaseReceipt(${receipt21._id}): The item: Chemical C, is not managed for the supplier: LECLERC.`,
                        path: [],
                        severity: 2,
                    },
                ]);

                // rollbackCache function will flush deferred actions and saves
                await Test.rollbackCache(context);

                purchaseReturn = await context.read(xtremPurchasing.nodes.PurchaseReturn, { number: 'RET006' });
                const pr20 = await context.read(xtremPurchasing.nodes.PurchaseReceipt, { number: 'PR20' });
                assert.isNotNull(await (await pr20.lines.elementAt(0)).purchaseReturnLines.toArray());
                assert.equal(await pr20.returnStatus, 'partiallyReturned');
                assert.equal(await (await pr20.lines.elementAt(0)).lineReturnStatus, 'partiallyReturned');
                // assert.equal(pr15.lines.elementAt(0).returnedQuantity.toString(), '7');
                // assert.equal(pr15.lines.elementAt(0).returnedQuantityInStockUnit.toString(), '7000');
                // assert.equal(pr15.lines.elementAt(0).remainingQuantity.toString(), '3');
                // assert.equal(pr15.lines.elementAt(0).remainingQuantityInStockUnit.toString(), '3000');

                const pr21 = await context.read(xtremPurchasing.nodes.PurchaseReceipt, { number: 'PR21' });
                assert.isNotNull(await (await pr21.lines.elementAt(0)).purchaseReturnLines.toArray());
                // assert.equal(pr16.lines.elementAt(0).returnedQuantity.toString(), '8');
                // assert.equal(pr16.lines.elementAt(0).returnedQuantityInStockUnit.toString(), '8000');
                // assert.equal(pr16.lines.elementAt(0).remainingQuantity.toString(), '2');
                // assert.equal(pr16.lines.elementAt(0).remainingQuantityInStockUnit.toString(), '2000');
                assert.equal(await pr21.returnStatus, 'partiallyReturned');
                assert.equal(await (await pr21.lines.elementAt(0)).lineReturnStatus, 'partiallyReturned');
                assert.equal(await (await pr21.lines.elementAt(1)).lineReturnStatus, 'notReturned');
                assert.equal(await (await pr21.lines.elementAt(1)).lineReturnStatus, 'notReturned');

                purchaseReturn = await context.read(
                    xtremPurchasing.nodes.PurchaseReturn,
                    { number: 'RET006' },
                    { forUpdate: true },
                );
                assert.equal(await purchaseReturn.lines.length, 3);
                await purchaseReturn.$.delete();
                await context.flushDeferredSaves();

                assert.deepEqual(purchaseReturn.$.context.diagnoses, [
                    {
                        message: `While saving PurchaseReceipt(${pr21._id}): The item: Chemical C, is not managed for the supplier: LECLERC.`,
                        path: [],
                        severity: 2,
                    },
                ]);

                await Test.rollbackCache(context);

                const pr15bis = await context.read(xtremPurchasing.nodes.PurchaseReceipt, { number: 'PR20' });
                let lines = await pr15bis.lines.toArray();
                assert.isNotNull(await lines[0].purchaseReturnLines.toArray());
                assert.equal(await pr15bis.returnStatus, 'notReturned');
                assert.equal(await lines[0].lineReturnStatus, 'notReturned');
                // assert.equal(lines[0].returnedQuantity.toString(), '0');
                // assert.equal(lines[0].returnedQuantityInStockUnit.toString(), '10000');
                // assert.equal(lines[0].remainingQuantity.toString(), '10');
                // assert.equal(lines[0].remainingQuantityInStockUnit.toString(), '10000');

                const pr16bis = await context.read(xtremPurchasing.nodes.PurchaseReceipt, { number: 'PR21' });
                lines = await pr16bis.lines.toArray();
                assert.isNotNull(await lines[0].purchaseReturnLines.toArray());
                assert.equal(await pr16bis.returnStatus, 'notReturned');
                assert.equal(await lines[0].lineReturnStatus, 'notReturned');
                // assert.equal(lines[0].returnedQuantity.toString(), '10');
                // assert.equal(lines[0].returnedQuantityInStockUnit.toString(), '10000');
                // assert.equal(lines[0].remainingQuantity.toString(), '0');
                // assert.equal(lines[0].remainingQuantityInStockUnit.toString(), '0');

                assert.equal(await lines[1].lineReturnStatus, 'notReturned');
                // assert.equal(lines[1].returnedQuantity.toString(), '0');
                // assert.equal(lines[1].returnedQuantityInStockUnit.toString(), '0');
                // assert.equal(lines[1].remainingQuantity.toString(), '10');
                // assert.equal(lines[1].remainingQuantityInStockUnit.toString(), '10000');
            },
            { today: testDate },
        ));
});
