import { Test } from '@sage/xtrem-core';
import * as xtremPurchasing from '../../../lib';

describe('Purchase invoice from purchase receipt ', () => {
    it('Purchase Invoice -- add a closed receipt', () =>
        Test.withContext(async context => {
            const invoice = await context.read(
                xtremPurchasing.nodes.PurchaseInvoice,
                { number: 'PI4' },
                { forUpdate: true },
            );

            const receipt = await context.read(
                xtremPurchasing.nodes.PurchaseReceipt,
                { number: 'PR45' },
                { forUpdate: true },
            );

            await receipt.$.set({ status: 'closed' });

            const receiptLine = await receipt.lines.elementAt(0);

            await receipt.$.save();

            await invoice.$.set({
                lines: [
                    {
                        _action: 'create',
                        item: await receiptLine.item,
                        quantity: await receiptLine.quantity,
                        unit: await receiptLine.unit,
                        purchaseReceiptLine: { purchaseReceiptLine: receiptLine },
                    },
                ],
            });

            await invoice.$.save();
        }));
});
