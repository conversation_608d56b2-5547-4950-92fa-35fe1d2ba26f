import { Test } from '@sage/xtrem-core';
import { assert } from 'chai';
import * as xtremPurchasing from '../../../lib';

describe('Purchase return node - approve/reject', () => {
    it('purchase return - approve', () =>
        Test.withContext(async context => {
            const return24 = await context.read(
                xtremPurchasing.nodes.PurchaseReturn,
                { number: 'RET024' },
                { forUpdate: true },
            );

            assert.equal(await return24.status, 'pending');
            assert.equal(await return24.approvalStatus, 'pendingApproval');

            const approveResult = await xtremPurchasing.nodes.PurchaseReturn.approve(context, return24, true);
            assert.isTrue(approveResult);

            assert.equal(await return24.status, 'pending');
            assert.equal(await return24.approvalStatus, 'approved');

            const line1 = await return24.lines.elementAt(0);
            // Line 0
            assert.equal(await line1.status, 'pending');
            assert.equal(await line1.approvalStatus, 'approved');
        }));

    it('purchase return - reject', () =>
        Test.withContext(async context => {
            const return24 = await context.read(
                xtremPurchasing.nodes.PurchaseReturn,
                { number: 'RET024' },
                { forUpdate: true },
            );

            const approveResult = await xtremPurchasing.nodes.PurchaseReturn.approve(context, return24, false);
            assert.isTrue(approveResult);

            assert.equal(await return24.status, 'closed');
            assert.equal(await return24.approvalStatus, 'rejected');

            const line1 = await return24.lines.elementAt(0);
            // Line 0
            assert.equal(await line1.status, 'closed');
            assert.equal(await line1.approvalStatus, 'rejected');
        }));
});
