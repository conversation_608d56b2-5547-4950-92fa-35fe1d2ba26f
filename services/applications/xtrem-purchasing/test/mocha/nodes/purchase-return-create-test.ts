import { asyncArray, date, Test } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import { assert } from 'chai';
import * as xtremPurchasing from '../../../lib/index';

const { withSequenceNumberContext } = xtremMasterData.functions.testLib;

describe('Purchase return node', () => {
    const testDate = '2020-08-10';
    // not anymore possible to unit test this because the notification feature is now needed to trigger stock movement processing...
    // we would need the possibility to send notification in test mode to perform this test
    it.skip('Create purchase return with stock issue', () =>
        Test.withContext(
            async context => {
                const newPurchaseReturn = await context.create(xtremPurchasing.nodes.PurchaseReturn, {
                    returnSite: '#US005',
                    businessRelation: '#US017',
                    currency: '#EUR',
                    returnRequestDate: date.make(2020, 8, 10),
                    lines: [
                        {
                            item: '#Aqua',
                            quantity: 15,
                            unit: '#LITER',
                            grossPrice: 100,
                            reason: { id: 'R1' },
                            purchaseReceiptLine: {
                                purchaseReceiptLine: 1015,
                                returnedQuantity: 15,
                                returnedQuantityInStockUnit: 15,
                            },
                        },
                    ],
                });
                await newPurchaseReturn.$.save();
                assert.deepEqual(newPurchaseReturn.$.context.diagnoses, [
                    {
                        message: 'The Aqua item is not managed for the US005 site and US017 supplier.',
                        path: ['lines', '-4', 'item'],
                        severity: 2,
                    },
                    {
                        message:
                            'While saving PurchaseReceipt(110): The Aqua item is not managed for the US005 site and US017 supplier.',
                        path: [],
                        severity: 2,
                    },
                ]);

                const piLines = await newPurchaseReturn.lines.toArray();
                assert.equal(piLines.length, 1);
                // const stockIssue =
                //     await (
                //         context
                //             .query(xtremStockData.nodes.StockJournal, {
                //                 filter: {
                //                     documentLine: piLines[0]._id,
                //                 },
                //             })
                //             .at(0),
                //     ) || null;
                // assert.exists(stockIssue, 'Should not be null'); // TODO: stock issue call is deactivate in return line; has to be reviewed
            },
            { today: testDate },
        ));

    it('Create purchase return - check evolution of work in progress expectedQuantity', () =>
        Test.withContext(
            async context => {
                const itemSite = await context.read(xtremMasterData.nodes.ItemSite, {
                    item: '#Muesli',
                    site: '#US001',
                });
                const prevOnReturnQty = await itemSite.expectedQuantity;
                const newPurchaseReturn = await context.create(xtremPurchasing.nodes.PurchaseReturn, {
                    returnSite: '#US001',
                    businessRelation: '#LECLERC',
                    currency: '#USD',
                    returnRequestDate: date.make(2020, 8, 10),
                    lines: [
                        {
                            item: '#Muesli',
                            quantity: 10,
                            unit: '#GRAM',
                            grossPrice: 30,
                            reason: { id: 'R1' },
                            purchaseReceiptLine: {
                                purchaseReceiptLine: '#PR8|1',
                                returnedQuantity: 10,
                                returnedQuantityInStockUnit: 10,
                            },
                        },
                    ],
                });
                await newPurchaseReturn.$.save();
                assert.deepEqual(newPurchaseReturn.$.context.diagnoses, []);
                assert.equal(prevOnReturnQty.toString(), (await itemSite.expectedQuantity).toString());
            },
            { today: testDate },
        ));
    it('Verify chronological control between two purchase returns on creation', () =>
        withSequenceNumberContext(
            'PurchaseReturn',
            { isChronological: true },
            async context => {
                const newPurchaseReturn = await context.create(xtremPurchasing.nodes.PurchaseReturn, {
                    returnSite: '#US001',
                    businessRelation: '#LECLERC',
                    currency: '#USD',
                    returnRequestDate: date.make(2022, 5, 5),
                    lines: [
                        {
                            item: '#Muesli',
                            quantity: 5,
                            unit: '#GRAM',
                            grossPrice: 30,
                            reason: { id: 'R1' },
                            purchaseReceiptLine: {
                                purchaseReceiptLine: '#PR8|1',
                                returnedQuantity: 5,
                                returnedQuantityInStockUnit: 5,
                            },
                        },
                    ],
                });
                await newPurchaseReturn.$.save({ flushDeferredActions: true });
                assert.deepEqual(newPurchaseReturn.$.context.diagnoses, []);

                const newPurchaseReturnError = await context.create(xtremPurchasing.nodes.PurchaseReturn, {
                    returnSite: '#US001',
                    businessRelation: '#LECLERC',
                    currency: '#USD',
                    returnRequestDate: date.make(2022, 5, 4),
                    lines: [
                        {
                            item: '#Muesli',
                            quantity: 5,
                            unit: '#GRAM',
                            grossPrice: 30,
                            reason: { id: 'R1' },
                            purchaseReceiptLine: {
                                purchaseReceiptLine: '#PR8|1',
                                returnedQuantity: 5,
                                returnedQuantityInStockUnit: 5,
                            },
                        },
                    ],
                });
                await assert.isRejected(newPurchaseReturnError.$.save(), 'The record was not created.');
                assert.deepEqual(newPurchaseReturnError.$.context.diagnoses, [
                    {
                        severity: 3,
                        path: [],
                        message: 'The document date 2022-05-04 is earlier than the previous document date 2022-05-05.',
                    },
                ]);

                // Because of the update of the linked purchase receipt that is flushed
                await assert.isRejected(context.flushDeferredSaves(), 'The record was not updated.');
                assert.deepEqual(context.diagnoses, [
                    {
                        severity: 3,
                        path: [],
                        message: 'The document date 2022-05-04 is earlier than the previous document date 2022-05-05.',
                    },
                ]);
            },
            { today: '2022-05-05' },
        ));

    it('Purchase return creation error with receipt in draft', () =>
        Test.withContext(
            async context => {
                const newPurchaseReceipt = await context.create(xtremPurchasing.nodes.PurchaseReceipt, {
                    site: '#US001',
                    businessRelation: '#LECLERC',
                    currency: '#USD',
                    date: date.make(2020, 8, 10),
                    lines: [
                        {
                            item: '#Muesli',
                            quantity: 10,
                            unit: '#GRAM',
                            grossPrice: 30,
                        },
                    ],
                });
                await newPurchaseReceipt.$.save({ flushDeferredActions: true });

                const number = await newPurchaseReceipt.number;
                const receipt = await context.read(xtremPurchasing.nodes.PurchaseReceipt, { number });
                const receiptLine = await receipt.lines.elementAt(0);

                const newPurchaseReturnError = await context.create(xtremPurchasing.nodes.PurchaseReturn, {
                    returnSite: '#US001',
                    businessRelation: '#LECLERC',
                    currency: '#USD',
                    returnRequestDate: date.make(2022, 5, 5),
                    lines: [
                        {
                            item: '#Muesli',
                            quantity: 5,
                            unit: '#GRAM',
                            grossPrice: 100,
                            reason: { id: 'R1' },
                            purchaseReceiptLine: {
                                purchaseReceiptLine: receiptLine,
                                returnedQuantity: 5,
                                returnedQuantityInStockUnit: 5,
                            },
                        },
                    ],
                });
                await assert.isRejected(newPurchaseReturnError.$.save(), 'The record was not created.');
                assert.equal(
                    newPurchaseReturnError.$.context.diagnoses[0].message,
                    'You need to select a purchase receipt that is at Pending, In progress or Closed status.',
                );
            },
            { today: '2022-05-05' },
        ));

    it('Purchase return creation error with receipt line in draft', () =>
        Test.withContext(
            async context => {
                const newPurchaseReceipt = await context.create(xtremPurchasing.nodes.PurchaseReceipt, {
                    site: '#US001',
                    businessRelation: '#LECLERC',
                    currency: '#USD',
                    date: date.make(2020, 8, 10),
                    status: 'inProgress',
                    lines: [
                        {
                            item: '#Muesli',
                            quantity: 10,
                            unit: '#GRAM',
                            grossPrice: 30,
                        },
                    ],
                });
                await newPurchaseReceipt.$.save({ flushDeferredActions: true });

                const number = await newPurchaseReceipt.number;
                const receipt = await context.read(xtremPurchasing.nodes.PurchaseReceipt, { number });
                const receiptLine = await receipt.lines.elementAt(0);

                const newPurchaseReturnError = await context.create(xtremPurchasing.nodes.PurchaseReturn, {
                    returnSite: '#US001',
                    businessRelation: '#LECLERC',
                    currency: '#USD',
                    returnRequestDate: date.make(2022, 5, 5),
                    lines: [
                        {
                            item: '#Muesli',
                            quantity: 5,
                            unit: '#GRAM',
                            grossPrice: 100,
                            reason: { id: 'R1' },
                            purchaseReceiptLine: {
                                // purchaseReceiptLine: '#PR22|1',
                                purchaseReceiptLine: receiptLine,
                                returnedQuantity: 5,
                                returnedQuantityInStockUnit: 5,
                            },
                        },
                    ],
                });
                await assert.isRejected(newPurchaseReturnError.$.save(), 'The record was not created.');
                assert.equal(
                    newPurchaseReturnError.$.context.diagnoses[0].message,
                    'You need to select a purchase receipt line that is at Pending, In progress or Closed status.',
                );
            },
            { today: '2022-05-05' },
        ));

    it('Create purchase return and repost it', () =>
        Test.withContext(
            async context => {
                const newPurchaseReturn = await context.create(xtremPurchasing.nodes.PurchaseReturn, {
                    returnSite: '#US001',
                    businessRelation: '#LECLERC',
                    currency: '#USD',
                    returnRequestDate: date.make(2020, 8, 10),
                    lines: [
                        {
                            item: '#Muesli',
                            quantity: 10,
                            unit: '#GRAM',
                            grossPrice: 30,
                            reason: { id: 'R1' },
                            purchaseReceiptLine: {
                                purchaseReceiptLine: '#PR8|1',
                                returnedQuantity: 10,
                                returnedQuantityInStockUnit: 10,
                            },
                        },
                    ],
                });

                await newPurchaseReturn.$.save();

                const purchaseReturn = await context
                    .query(xtremPurchasing.nodes.PurchaseReturn, {
                        last: 1,
                    })
                    .toArray();

                const purchaseReturnLines = await asyncArray(await purchaseReturn[0].lines.toArray())
                    .map(async line => {
                        return {
                            baseDocumentLineSysId: line._id,
                            storedAttributes:
                                (await line.storedAttributes) as xtremMasterData.interfaces.StoredAttributes,
                            storedDimensions: (await line.storedDimensions) || {},
                        };
                    })
                    .toArray();

                await assert.isRejected(
                    xtremPurchasing.nodes.PurchaseReturn.repost(context, purchaseReturn[0], purchaseReturnLines),
                    "You can only repost a purchase return if the status is 'Failed.'",
                );
            },
            { today: testDate },
        ));
    it('Create purchase return and then partially invoice it in a row', () =>
        Test.withContext(async context => {
            let newPurchaseReturn = await context.create(xtremPurchasing.nodes.PurchaseReturn, {
                returnSite: '#US001',
                businessRelation: '#LECLERC',
                currency: '#EUR',
                returnRequestDate: date.today(),
                lines: [
                    {
                        item: '#Muesli',
                        quantity: 10,
                        unit: '#KILOGRAM',
                        grossPrice: 30,
                        reason: { id: 'R1' },
                        purchaseReceiptLine: {
                            purchaseReceiptLine: 1034,
                            returnedQuantity: 10,
                            returnedQuantityInStockUnit: 10000,
                        },
                    },
                ],
            });
            await newPurchaseReturn.$.save({ flushDeferredActions: true });

            // Submitted for approval
            await newPurchaseReturn.$.set({ approvalStatus: 'pendingApproval' });
            await newPurchaseReturn.$.set({ status: 'pending' });
            await newPurchaseReturn.lines.forEach(async line => {
                await line.$.set({ status: 'pending' });
                await line.$.set({ approvalStatus: 'pendingApproval' });
            });
            await newPurchaseReturn.$.save();

            // Approved
            await newPurchaseReturn.$.set({ approvalStatus: 'approved' });
            await newPurchaseReturn.$.set({ status: 'inProgress' });
            await newPurchaseReturn.lines.forEach(async line => {
                await line.$.set({ status: 'inProgress' });
                await line.$.set({ approvalStatus: 'approved' });
            });
            await newPurchaseReturn.$.save();

            const purchaseInvoice = await context.create(xtremPurchasing.nodes.PurchaseInvoice, {
                site: '#US001',
                billBySupplier: '#LECLERC',
                currency: '#EUR',
                invoiceDate: date.today(),
                totalAmountExcludingTax: await newPurchaseReturn.totalAmountExcludingTax,
                lines: [
                    {
                        item: '#Muesli',
                        quantity: 6,
                        unit: '#KILOGRAM',
                        grossPrice: 30,
                        purchaseReturnLines: [
                            {
                                purchaseReturnLine: (await newPurchaseReturn.lines.elementAt(0))._id,
                                invoicedQuantity: 6,
                            },
                        ],
                    },
                ],
            });
            await purchaseInvoice.$.save();

            assert.equal(await purchaseInvoice.lines.length, 1);
            const purchaseInvoiceLine = await purchaseInvoice.lines.elementAt(0);
            assert.equal(await purchaseInvoiceLine.purchaseReturnLines.length, 1);
            assert.equal(
                (await (await purchaseInvoiceLine.purchaseReturnLines.elementAt(0)).invoicedQuantity).toString(),
                '6',
            );
            assert.equal(
                (
                    await (
                        await purchaseInvoiceLine.purchaseReturnLines.elementAt(0)
                    ).invoicedQuantityInStockUnit
                ).toString(),
                '6000',
            );

            newPurchaseReturn = await context.read(xtremPurchasing.nodes.PurchaseReturn, {
                _id: newPurchaseReturn._id,
            });
            assert.equal((await (await newPurchaseReturn.lines.elementAt(0)).invoicedQuantity).toString(), '6');
            assert.equal(await (await newPurchaseReturn.lines.elementAt(0)).lineInvoiceStatus, 'partiallyInvoiced');
            assert.equal('inProgress', await newPurchaseReturn.status);
        }));
    it('Create purchase return and then partially and fully invoice it in a row', () =>
        Test.withContext(
            async context => {
                let newPurchaseReturn = await context.create(xtremPurchasing.nodes.PurchaseReturn, {
                    returnSite: '#US001',
                    businessRelation: '#LECLERC',
                    currency: '#EUR',
                    returnRequestDate: date.make(2020, 8, 10),
                    lines: [
                        {
                            item: '#Muesli',
                            quantity: 10,
                            unit: '#KILOGRAM',
                            grossPrice: 30,
                            reason: { id: 'R1' },
                            purchaseReceiptLine: {
                                purchaseReceiptLine: 1034,
                                returnedQuantity: 10,
                                returnedQuantityInStockUnit: 10000,
                            },
                        },
                    ],
                });
                await newPurchaseReturn.$.save({ flushDeferredActions: true });

                // Submitted for approval
                await newPurchaseReturn.$.set({ approvalStatus: 'pendingApproval' });
                await newPurchaseReturn.$.set({ status: 'pending' });
                await newPurchaseReturn.lines.forEach(async line => {
                    await line.$.set({ status: 'pending' });
                    await line.$.set({ approvalStatus: 'pendingApproval' });
                });
                await newPurchaseReturn.$.save();

                // Approved
                await newPurchaseReturn.$.set({ approvalStatus: 'approved' });
                await newPurchaseReturn.$.set({ status: 'inProgress' });
                await newPurchaseReturn.lines.forEach(async line => {
                    await line.$.set({ status: 'inProgress' });
                    await line.$.set({ approvalStatus: 'approved' });
                });
                await newPurchaseReturn.$.save();
                let purchaseInvoice = await context.create(xtremPurchasing.nodes.PurchaseInvoice, {
                    site: '#US001',
                    billBySupplier: '#LECLERC',
                    currency: '#EUR',
                    invoiceDate: date.today(),
                    totalAmountExcludingTax: await newPurchaseReturn.totalAmountExcludingTax,
                    lines: [
                        {
                            item: '#Muesli',
                            quantity: 7,
                            unit: '#KILOGRAM',
                            grossPrice: 30,
                            purchaseReturnLines: [
                                {
                                    purchaseReturnLine: (await newPurchaseReturn.lines.elementAt(0))._id,
                                    invoicedQuantity: 7,
                                },
                            ],
                        },
                    ],
                });
                await purchaseInvoice.$.save();

                assert.equal(await purchaseInvoice.lines.length, 1);
                const purchaseInvoiceLine = await purchaseInvoice.lines.elementAt(0);
                assert.equal(await purchaseInvoiceLine.purchaseReturnLines.length, 1);
                assert.equal(
                    (await (await purchaseInvoiceLine.purchaseReturnLines.elementAt(0)).invoicedQuantity).toString(),
                    '7',
                );
                assert.equal(
                    (
                        await (
                            await purchaseInvoiceLine.purchaseReturnLines.elementAt(0)
                        ).invoicedQuantityInStockUnit
                    ).toString(),
                    '7000',
                );

                newPurchaseReturn = await context.read(xtremPurchasing.nodes.PurchaseReturn, {
                    _id: newPurchaseReturn._id,
                });
                assert.equal(await (await newPurchaseReturn.lines.elementAt(0)).lineInvoiceStatus, 'partiallyInvoiced');
                assert.equal(await newPurchaseReturn.status, 'inProgress');

                newPurchaseReturn = await context.read(xtremPurchasing.nodes.PurchaseReturn, {
                    _id: newPurchaseReturn._id,
                });
                purchaseInvoice = await context.create(xtremPurchasing.nodes.PurchaseInvoice, {
                    site: '#US001',
                    billBySupplier: '#LECLERC',
                    currency: '#EUR',
                    invoiceDate: date.today(),
                    totalAmountExcludingTax: await newPurchaseReturn.totalAmountExcludingTax,
                    lines: [
                        {
                            item: '#Muesli',
                            quantity: 3,
                            unit: '#KILOGRAM',
                            grossPrice: 30,
                            purchaseReturnLines: [
                                {
                                    purchaseReturnLine: (await newPurchaseReturn.lines.elementAt(0))._id,
                                    invoicedQuantity: 3,
                                },
                            ],
                        },
                    ],
                });
                await purchaseInvoice.$.save();

                assert.equal(await purchaseInvoice.lines.length, 1);
                assert.equal(await (await purchaseInvoice.lines.elementAt(0)).purchaseReturnLines.length, 1);
                assert.equal(
                    (
                        await (
                            await (await purchaseInvoice.lines.elementAt(0)).purchaseReturnLines.elementAt(0)
                        ).invoicedQuantity
                    ).toString(),
                    '3',
                );
                assert.equal(
                    (
                        await (
                            await (await purchaseInvoice.lines.elementAt(0)).purchaseReturnLines.elementAt(0)
                        ).invoicedQuantityInStockUnit
                    ).toString(),
                    '3000',
                );

                newPurchaseReturn = await context.read(xtremPurchasing.nodes.PurchaseReturn, {
                    _id: newPurchaseReturn._id,
                });
                assert.equal(await (await newPurchaseReturn.lines.elementAt(0)).lineInvoiceStatus, 'invoiced');
                assert.equal(await newPurchaseReturn.status, 'closed');
            },
            { today: '2020-08-10' },
        ));
});
