import { Test } from '@sage/xtrem-core';
import { assert } from 'chai';
import * as xtremPurchasing from '../../../index';

// This test checks that the discountPaymentBeforeDate is updated when the supplierDocumentDate is changed.
describe('discountPaymentBeforeDate is updated when the supplierDocumentDate is changed', () => {
    it('supplierDocumentDate update should force an update of discountPaymentBeforeDate on invoice', () =>
        Test.withContext(async context => {
            let purchaseInvoice = await context.read(
                xtremPurchasing.nodes.PurchaseInvoice,
                { number: 'PI250015' },
                { forUpdate: true },
            );

            let supplierDocumentDate = await purchaseInvoice.supplierDocumentDate;
            let discountPaymentBeforeDate = await (await purchaseInvoice.paymentTracking)?.discountPaymentBeforeDate;
            assert.strictEqual(supplierDocumentDate.toString(), '2025-05-19');
            assert.strictEqual(discountPaymentBeforeDate?.toString(), '2025-06-18');

            await purchaseInvoice.$.set({
                supplierDocumentDate: (await purchaseInvoice.supplierDocumentDate).addDays(-2),
            });
            await purchaseInvoice.$.save();
            purchaseInvoice = await context.read(xtremPurchasing.nodes.PurchaseInvoice, { number: 'PI250015' });
            supplierDocumentDate = await purchaseInvoice.supplierDocumentDate;
            discountPaymentBeforeDate = await (await purchaseInvoice.paymentTracking)?.discountPaymentBeforeDate;
            assert.strictEqual(supplierDocumentDate.toString(), '2025-05-17');
            assert.strictEqual(discountPaymentBeforeDate?.toString(), '2025-06-16');
        }));

    it('supplierDocumentDate update should force an update of discountPaymentBeforeDate on credit memo', () =>
        Test.withContext(async context => {
            let purchaseCreditMemo = await context.read(
                xtremPurchasing.nodes.PurchaseCreditMemo,
                { number: 'PC240001' },
                { forUpdate: true },
            );

            let supplierDocumentDate = await purchaseCreditMemo.supplierDocumentDate;
            let discountPaymentBeforeDate = await (await purchaseCreditMemo.paymentTracking)?.discountPaymentBeforeDate;
            assert.strictEqual(supplierDocumentDate.toString(), '2024-10-09');
            assert.strictEqual(discountPaymentBeforeDate?.toString(), '2024-11-08');

            await purchaseCreditMemo.$.set({
                supplierDocumentDate: (await purchaseCreditMemo.supplierDocumentDate).addDays(-2),
            });
            await purchaseCreditMemo.$.save();
            purchaseCreditMemo = await context.read(xtremPurchasing.nodes.PurchaseCreditMemo, { number: 'PC240001' });
            supplierDocumentDate = await purchaseCreditMemo.supplierDocumentDate;
            discountPaymentBeforeDate = await (await purchaseCreditMemo.paymentTracking)?.discountPaymentBeforeDate;
            assert.strictEqual(supplierDocumentDate.toString(), '2024-10-07');
            assert.strictEqual(discountPaymentBeforeDate?.toString(), '2024-11-06');
        }));
});
