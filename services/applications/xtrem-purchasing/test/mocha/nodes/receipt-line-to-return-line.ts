import type { Context } from '@sage/xtrem-core';
import { Test, date } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import { assert } from 'chai';
import * as xtremPurchasing from '../../../lib/index';
import * as TestHelper from '../../fixtures/lib/functions';

async function getValuesFromReceipt(
    context: Context,
    receipt: { number: string; line: { itemId: string; _sortValue?: number } },
) {
    const purchaseReceipt = await context.read(
        xtremPurchasing.nodes.PurchaseReceipt,
        { number: receipt.number },
        { forUpdate: true }, // forUpdate is required so it will hide the cache
    );
    const receiptItems = await purchaseReceipt.lines.map(async rlines => (await rlines.item).id).toArray();
    const line = await purchaseReceipt.lines.find(
        async receiptLine =>
            (await (await receiptLine.item).id) === receipt.line.itemId &&
            (!receipt.line._sortValue || (await receiptLine._sortValue) === receipt.line._sortValue),
    );
    return {
        values: {
            receiptReturnStatus: await purchaseReceipt.returnStatus,
            receiptItems,
            receiptLineCount: await purchaseReceipt.lines.length,
            lineReturnStatus: (await line?.lineReturnStatus) ?? '',
            quantity: {
                returned: Number(await line?.returnedQuantity),
                returnedInStockUnit: Number(await line?.returnedQuantityInStockUnit),
                remainingInStockUnit: Number(await line?.remainingQuantityInStockUnit),
            },
        },
        purchaseReceipt,
        line,
    };
}

// fixes The item: Chemical C, is not managed for the supplier: LECLERC.
async function fixChemicalC(context: Context): Promise<void> {
    const chemicalItem = await context.read(xtremMasterData.nodes.Item, { id: 'Chemical C' }, { forUpdate: true });
    await chemicalItem.$.set({ suppliers: [{ supplier: '#LECLERC', isActive: true, purchaseUnitOfMeasure: '#GRAM' }] });
    await chemicalItem.$.trySave();
    assert.deepEqual(context.diagnoses, [], context.diagnoses.map(d => d.message).join('\n'));
    const itemSiteSupplier = await context.create(xtremMasterData.nodes.ItemSiteSupplier, {
        itemSite: '#Chemical C|US001',
        supplier: '#LECLERC',
        isDefaultItemSupplier: true,
        purchaseLeadTime: 30,
        itemSupplier: '#Chemical C|LECLERC',
    });
    await itemSiteSupplier.$.trySave();
    assert.deepEqual(context.diagnoses, [], context.diagnoses.map(d => d.message).join('\n'));
}

describe('Purchase receipt line to purchase return line update', () => {
    const testDate = '2020-08-10';
    it('Update purchase return RET006 with 2 order partial lines PR20 and PR21 then fully delete the return using po.$.delete()', () =>
        Test.withContext(
            async context => {
                await fixChemicalC(context);

                const receiptLineIds = await TestHelper.PurchaseReceipt.simulateReceiptPostToStock(context, [
                    'PR20',
                    'PR21',
                ]);
                assert.equal(receiptLineIds.length, 2);

                const return06 = await context.read(
                    xtremPurchasing.nodes.PurchaseReturn,
                    { number: 'RET006' },
                    { forUpdate: true },
                );

                assert.equal(await return06.lines.length, 1);
                await return06.$.set({
                    lines: [
                        {
                            item: '#Muesli',
                            grossPrice: 100,
                            unit: '#KILOGRAM',
                            quantity: 7, // in purchase unit
                            stockUnit: '#GRAM',
                            quantityInStockUnit: 7000,
                            expectedReturnDate: date.make(2020, 8, 10),
                            purchaseReceiptLine: { purchaseReceiptLine: receiptLineIds[0], returnedQuantity: 7 }, // #PR20|1
                            reason: { id: 'R1' },
                            _action: 'create',
                        },
                        {
                            item: '#Chemical C',
                            grossPrice: 100,
                            unit: '#KILOGRAM',
                            quantity: 8,
                            stockUnit: '#GRAM',
                            quantityInStockUnit: 8000,
                            expectedReturnDate: date.make(2020, 8, 10),
                            purchaseReceiptLine: { purchaseReceiptLine: receiptLineIds[1], returnedQuantity: 8 }, // #PR21|1
                            reason: { id: 'R1' },
                            _action: 'create',
                        },
                    ],
                });
                assert.equal(await return06.lines.length, 3);
                await return06.$.save();
                assert.deepEqual(return06.$.context.diagnoses, []);

                // rollbackCache function will flush deferred actions and saves
                await Test.rollbackCache(context);

                /** First 7kg Muesli return from receive */
                const receiptMuesli = (
                    await getValuesFromReceipt(context, { number: 'PR20', line: { itemId: 'Muesli' } })
                ).values;

                assert.deepEqual(receiptMuesli, {
                    receiptItems: ['Muesli'],
                    receiptReturnStatus: 'partiallyReturned',
                    receiptLineCount: 1,
                    lineReturnStatus: 'partiallyReturned',
                    quantity: {
                        returned: 7,
                        returnedInStockUnit: 7000,
                        remainingInStockUnit: 3000,
                    },
                });

                const receiptChemical = (
                    await getValuesFromReceipt(context, {
                        number: 'PR21',
                        line: { itemId: 'Chemical C', _sortValue: 1 },
                    })
                ).values;

                assert.deepEqual(receiptChemical, {
                    receiptItems: ['Chemical C', 'Muesli', 'Chemical C'],
                    receiptReturnStatus: 'partiallyReturned',
                    receiptLineCount: 3,
                    lineReturnStatus: 'partiallyReturned',
                    quantity: {
                        returned: 8,
                        returnedInStockUnit: 8000,
                        remainingInStockUnit: 2000,
                    },
                });

                const receipt21Muesli = (
                    await getValuesFromReceipt(context, { number: 'PR21', line: { itemId: 'Muesli', _sortValue: 2 } })
                ).values;
                assert.equal(receipt21Muesli.lineReturnStatus, 'notReturned');

                const receipt21SecondChemical = (
                    await getValuesFromReceipt(context, {
                        number: 'PR21',
                        line: { itemId: 'Chemical C', _sortValue: 3 },
                    })
                ).values;
                assert.equal(receipt21SecondChemical.lineReturnStatus, 'notReturned');

                // DELETING
                await context.delete(xtremPurchasing.nodes.PurchaseReturn, { number: 'RET006' });

                await Test.rollbackCache(context);

                const receiptMuesliDelete = await getValuesFromReceipt(context, {
                    number: 'PR20',
                    line: { itemId: 'Muesli' },
                });

                const returnLine = await (
                    await receiptMuesliDelete.line?.purchaseReturnLines.at(0)
                )?.purchaseReturnLine;

                assert.isUndefined(returnLine);

                assert.deepEqual(receiptMuesliDelete.values, {
                    receiptItems: ['Muesli'],
                    receiptReturnStatus: 'notReturned',
                    receiptLineCount: 1,
                    lineReturnStatus: 'notReturned',
                    quantity: {
                        returned: 0,
                        returnedInStockUnit: 0,
                        remainingInStockUnit: 10000,
                    },
                });

                const receipt21ChemicalDelete = await getValuesFromReceipt(context, {
                    number: 'PR21',
                    line: { itemId: 'Chemical C' },
                });

                assert.deepEqual(receipt21ChemicalDelete.values, {
                    receiptItems: ['Chemical C', 'Muesli', 'Chemical C'],
                    receiptReturnStatus: 'notReturned',
                    receiptLineCount: 3,
                    lineReturnStatus: 'notReturned',
                    quantity: { returned: 0, returnedInStockUnit: 0, remainingInStockUnit: 10000 },
                });

                const receipt21MuesliDelete = await getValuesFromReceipt(context, {
                    number: 'PR21',
                    line: { itemId: 'Muesli' },
                });

                assert.deepEqual(receipt21MuesliDelete.values, {
                    receiptItems: ['Chemical C', 'Muesli', 'Chemical C'],
                    receiptReturnStatus: 'notReturned',
                    receiptLineCount: 3,
                    lineReturnStatus: 'notReturned',
                    quantity: { returned: 0, returnedInStockUnit: 0, remainingInStockUnit: 15000 },
                });
            },
            { today: testDate },
        ));
});

describe('Purchase receipt line to purchase return line delete', () => {
    it('Delete line of draft purchase return generated from invoiced purchase receipt RET57', () =>
        Test.withContext(
            async context => {
                const receipt = await context.read(
                    xtremPurchasing.nodes.PurchaseReceipt,
                    { number: 'PR57' },
                    { forUpdate: true },
                );
                assert.equal(await receipt.returnStatus, 'notReturned');
                assert.equal(await receipt.invoiceStatus, 'invoiced');
                assert.equal(await receipt.lines.length, 3);

                const [returnDoc] = await xtremPurchasing.nodes.PurchaseReceipt.createPurchaseReturns(context, receipt);
                await context.flushDeferredActions();
                assert.isDefined(returnDoc);
                assert.equal(await receipt.returnStatus, 'returned');
                const receiptLine2 = await receipt.lines.at(2);
                assert.isDefined(receiptLine2);
                assert.equal(await receiptLine2.returnedQuantityInStockUnit, 1);
                assert.equal(await receiptLine2.lineReturnStatus, 'returned');

                // To force reset of default value like it is with 2 separate transactions
                await receipt.$.set({
                    canUpdateClosedDocument: false,
                });

                const returnDocument = await context.read(
                    xtremPurchasing.nodes.PurchaseReturn,
                    { number: await returnDoc.number },
                    { forUpdate: true },
                );
                assert.equal(await returnDocument.status, 'draft');
                assert.equal(await returnDocument.lines.length, 3);
                await returnDocument.$.set({
                    lines: [{ _id: (await returnDocument.lines.at(2))?._id, _action: 'delete' }],
                });
                await returnDocument.$.save();
                assert.equal(await returnDocument.lines.length, 2);
                assert.equal(await receiptLine2.returnedQuantityInStockUnit, 0);
                assert.equal(await receiptLine2.lineReturnStatus, 'notReturned');
            },
            { today: '2025-02-27' },
        ));
});
