import type { integer } from '@sage/xtrem-core';
import { Test, asyncArray } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import { assert } from 'chai';
import * as xtremPurchasing from '../../../lib/index';
import type { LandedCostLineToTest, StockCorrectionDetailToTest } from '../fixtures/common';
import { assertionLandedCostLine, assertionStockCorrectionDetail } from '../fixtures/common';
import type { PurchaseOrderLineToTest } from '../fixtures/purchase-order';
import { assertionPurchaseOrderLine, closeOrder } from '../fixtures/purchase-order';

describe('Close purchase order with landed costs', () => {
    it('Close the 2nd line of the purchase order PO35', () =>
        Test.withContext(
            async context => {
                let po35 = await context.read(xtremPurchasing.nodes.PurchaseOrder, {
                    number: 'PO35',
                });

                await assertionPurchaseOrderLine(await po35.lines.elementAt(0), {
                    numberOfReceipts: 1,
                    receivedQuantity: 3.0,
                    receivedQuantityInStockUnit: 3.0,
                    quantityToReceive: 8.0,
                    quantityToReceiveInStockUnit: 8.0,
                    quantityReceivedInProgress: Number(0.0),
                    lineReceiptStatus: 'partiallyReceived',
                    status: 'inProgress',
                });

                await assertionPurchaseOrderLine(await po35.lines.elementAt(1), {
                    numberOfReceipts: 1,
                    receivedQuantity: 3.0,
                    receivedQuantityInStockUnit: 3.0,
                    quantityToReceive: 8.0,
                    quantityToReceiveInStockUnit: 8.0,
                    quantityReceivedInProgress: Number(0.0),
                    lineReceiptStatus: 'partiallyReceived',
                    status: 'inProgress',
                });

                await assertionLandedCostLine(context, { filter: { documentLine: { _in: [3202, 3203, 1481] } } }, [
                    {
                        documentLine: { _id: 3202 },
                        _documentLine_constructor: 'PurchaseOrderLine',
                        landedCost: { id: 'LandedCost001' },
                        landedCostAllocation: {
                            line: { documentLine: { _id: 2629 } },
                        },
                        actualCostAmountInCompanyCurrency: 200.4,
                        actualAllocatedCostAmountInCompanyCurrency: 0.0,
                    },
                    {
                        documentLine: { _id: 3203 },
                        _documentLine_constructor: 'PurchaseOrderLine',
                        landedCost: { id: 'LandedCost001' },
                        landedCostAllocation: {
                            line: { documentLine: { _id: 2629 } },
                        },
                        actualCostAmountInCompanyCurrency: 100.2,
                        actualAllocatedCostAmountInCompanyCurrency: 27.33,
                    },
                    {
                        documentLine: { _id: 1481 },
                        _documentLine_constructor: 'PurchaseReceiptLine',
                        landedCost: { id: 'LandedCost001' },
                        landedCostAllocation: {
                            line: { documentLine: { _id: 2629 } },
                        },
                        actualCostAmountInCompanyCurrency: 27.33,
                        actualAllocatedCostAmountInCompanyCurrency: 0.0,
                    },
                ]);

                const isClosed = await xtremPurchasing.nodes.PurchaseOrder.closeLine(
                    context,
                    await po35.lines.elementAt(1),
                );
                assert.isTrue(isClosed);

                // rollbackCache function will flush deferred actions and saves
                await Test.rollbackCache(context);
                po35 = await context.read(xtremPurchasing.nodes.PurchaseOrder, {
                    number: 'PO35',
                });

                const line1 = await po35.lines.elementAt(1);
                assert.deepEqual(line1._id, 3203);
                await assertionPurchaseOrderLine(line1, {
                    numberOfReceipts: 1,
                    receivedQuantity: 3.0,
                    receivedQuantityInStockUnit: 3.0,
                    quantityToReceive: 8.0,
                    quantityToReceiveInStockUnit: 8.0,
                    quantityReceivedInProgress: 3.0,
                    lineReceiptStatus: 'partiallyReceived',
                    status: 'closed',
                });

                await assertionLandedCostLine(context, { filter: { documentLine: { _in: [3202, 3203, 1481] } } }, [
                    {
                        documentLine: { _id: 3202 },
                        _documentLine_constructor: 'PurchaseOrderLine',
                        landedCost: { id: 'LandedCost001' },
                        landedCostAllocation: {
                            line: { documentLine: { _id: 2629 } },
                        },
                        actualCostAmountInCompanyCurrency: 200.4,
                        actualAllocatedCostAmountInCompanyCurrency: 0.0,
                    },
                    {
                        documentLine: { _id: 3203 },
                        _documentLine_constructor: 'PurchaseOrderLine',
                        landedCost: { id: 'LandedCost001' },
                        landedCostAllocation: {
                            line: { documentLine: { _id: 2629 } },
                        },
                        actualCostAmountInCompanyCurrency: 100.2,
                        actualAllocatedCostAmountInCompanyCurrency: 100.2,
                    },
                    {
                        documentLine: { _id: 1481 },
                        _documentLine_constructor: 'PurchaseReceiptLine',
                        landedCost: { id: 'LandedCost001' },
                        landedCostAllocation: {
                            line: { documentLine: { _id: 2629 } },
                        },
                        actualCostAmountInCompanyCurrency: 100.2,
                        actualAllocatedCostAmountInCompanyCurrency: 0.0,
                    },
                ]);
            },
            { today: '2024-01-22', testActiveServiceOptions: [xtremMasterData.serviceOptions.landedCostOption] },
        ));
    it('Close all lines of purchase order PO35', () =>
        Test.withContext(
            async context => {
                let po35 = await context.read(xtremPurchasing.nodes.PurchaseOrder, {
                    number: 'PO35',
                });

                // Check the only existing receipts have the correct quantity (3/11)
                await po35.lines.forEach(async line => {
                    const purchaseReceiptLines = await line.purchaseReceiptLines
                        .map(orderLineToReceiptLine => orderLineToReceiptLine.purchaseReceiptLine)
                        .toArray();
                    assert.deepEqual(purchaseReceiptLines.length, 1);
                    assert.deepEqual(await purchaseReceiptLines[0].quantityInStockUnit, 3.0);
                });

                const isClosed = await xtremPurchasing.nodes.PurchaseOrder.close(context, po35);
                assert.isTrue(isClosed);

                await Test.rollbackCache(context);
                po35 = await context.read(xtremPurchasing.nodes.PurchaseOrder, {
                    number: 'PO35',
                });

                const line0 = await po35.lines.elementAt(0);
                assert.deepEqual(line0._id, 3202);
                await assertionPurchaseOrderLine(line0, {
                    numberOfReceipts: 1,
                    receivedQuantity: 3.0,
                    receivedQuantityInStockUnit: 3.0,
                    quantityToReceive: 8.0,
                    quantityToReceiveInStockUnit: 8.0,
                    quantityReceivedInProgress: 3.0,
                    lineReceiptStatus: 'partiallyReceived',
                    status: 'closed',
                });
                const line1 = await po35.lines.elementAt(1);
                assert.deepEqual(line1._id, 3203);
                await assertionPurchaseOrderLine(line1, {
                    numberOfReceipts: 1,
                    receivedQuantity: 3.0,
                    receivedQuantityInStockUnit: 3.0,
                    quantityToReceive: 8.0,
                    quantityToReceiveInStockUnit: 8.0,
                    quantityReceivedInProgress: 3.0,
                    lineReceiptStatus: 'partiallyReceived',
                    status: 'closed',
                });

                await assertionLandedCostLine(
                    context,
                    { filter: { documentLine: { _in: [3202, 3203, 1480, 1481] } }, orderBy: { _id: 1 } },
                    [
                        {
                            documentLine: { _id: 3202 },
                            _documentLine_constructor: 'PurchaseOrderLine',
                            landedCost: { id: 'LandedCost001' },
                            landedCostAllocation: {
                                line: { documentLine: { _id: 2629 } },
                            },
                            actualCostAmountInCompanyCurrency: 200.4,
                            actualAllocatedCostAmountInCompanyCurrency: 200.4,
                        },
                        {
                            documentLine: { _id: 3203 },
                            _documentLine_constructor: 'PurchaseOrderLine',
                            landedCost: { id: 'LandedCost001' },
                            landedCostAllocation: {
                                line: { documentLine: { _id: 2629 } },
                            },
                            actualCostAmountInCompanyCurrency: 100.2,
                            actualAllocatedCostAmountInCompanyCurrency: 100.2,
                        },
                        {
                            documentLine: { _id: 1481 },
                            _documentLine_constructor: 'PurchaseReceiptLine',
                            landedCost: { id: 'LandedCost001' },
                            landedCostAllocation: {
                                line: { documentLine: { _id: 2629 } },
                            },
                            actualCostAmountInCompanyCurrency: 100.2,
                            actualAllocatedCostAmountInCompanyCurrency: 0.0,
                        },
                        {
                            documentLine: { _id: 1480 },
                            _documentLine_constructor: 'PurchaseReceiptLine',
                            landedCost: { id: 'LandedCost001' },
                            landedCostAllocation: {
                                line: { documentLine: { _id: 2629 } },
                            },
                            actualCostAmountInCompanyCurrency: 200.4,
                            actualAllocatedCostAmountInCompanyCurrency: 0.0,
                        },
                    ],
                );

                await assertionStockCorrectionDetail(
                    context,
                    { filter: { documentLine: { _in: [1480, 1481] } }, orderBy: { _id: 1 } },
                    [
                        {
                            documentLine: { _id: 1481 },
                            _documentLine_constructor: 'PurchaseReceiptLine',
                            reasonCode: { id: 'Landed cost adjustment' },
                            amountToAbsorb: 27.33,
                            nonAbsorbedAmount: 0.0,
                            impactedQuantity: 3.0,
                        },
                        {
                            documentLine: { _id: 1480 },
                            _documentLine_constructor: 'PurchaseReceiptLine',
                            reasonCode: { id: 'Landed cost adjustment' },
                            amountToAbsorb: 200.4,
                            nonAbsorbedAmount: 0.0,
                            impactedQuantity: 3.0,
                        },
                        {
                            documentLine: { _id: 1481 },
                            _documentLine_constructor: 'PurchaseReceiptLine',
                            reasonCode: { id: 'Landed cost adjustment' },
                            amountToAbsorb: 72.87,
                            nonAbsorbedAmount: 0.0,
                            impactedQuantity: 3.0,
                        },
                    ],
                );
            },
            { today: '2024-01-22', testActiveServiceOptions: [xtremMasterData.serviceOptions.landedCostOption] },
        ));

    it('Close all lines of purchase order PO36, PO37 and PO38', () =>
        Test.withContext(
            async context => {
                await closeOrder(context, 'PO36');
                await closeOrder(context, 'PO37');
                await closeOrder(context, 'PO38');

                const expectedResults: {
                    number: string;
                    purchaseOrderLines: {
                        orderLine: PurchaseOrderLineToTest;
                        landedCostLines: LandedCostLineToTest[];
                        purchaseReceiptLines: {
                            _id: integer;
                            landedCostLines: LandedCostLineToTest[];
                            stockCorrectionDetails: StockCorrectionDetailToTest[];
                        }[];
                    }[];
                }[] = [
                    {
                        number: 'PO36',
                        purchaseOrderLines: [
                            {
                                orderLine: {
                                    // 3204
                                    numberOfReceipts: 2,
                                    receivedQuantity: 4.0,
                                    receivedQuantityInStockUnit: 4.0,
                                    quantityToReceive: 6.0,
                                    quantityToReceiveInStockUnit: 6.0,
                                    quantityReceivedInProgress: 4.0,
                                    lineReceiptStatus: 'partiallyReceived',
                                    status: 'closed',
                                },
                                landedCostLines: [
                                    {
                                        documentLine: { _id: 3204 },
                                        _documentLine_constructor: 'PurchaseOrderLine',
                                        landedCost: { id: 'LandedCost001' },
                                        landedCostAllocation: {
                                            line: { documentLine: { _id: 2630 } }, // Purchase Invoice PI20
                                        },
                                        actualCostAmountInCompanyCurrency: 25.05,
                                        actualAllocatedCostAmountInCompanyCurrency: 25.05,
                                    },
                                    {
                                        documentLine: { _id: 3204 },
                                        _documentLine_constructor: 'PurchaseOrderLine',
                                        landedCost: { id: 'LandedCost003' },
                                        landedCostAllocation: {
                                            line: { documentLine: { _id: 2631 } },
                                        },
                                        actualCostAmountInCompanyCurrency: 14.54,
                                        actualAllocatedCostAmountInCompanyCurrency: 14.54,
                                    },
                                    {
                                        documentLine: { _id: 3204 },
                                        _documentLine_constructor: 'PurchaseOrderLine',
                                        landedCost: { id: 'LandedCost003' },
                                        landedCostAllocation: {
                                            line: { documentLine: { _id: 2631 } },
                                        },
                                        actualCostAmountInCompanyCurrency: 48.45,
                                        actualAllocatedCostAmountInCompanyCurrency: 48.45,
                                    },
                                ],
                                purchaseReceiptLines: [
                                    {
                                        _id: 1482,
                                        landedCostLines: [
                                            {
                                                documentLine: { _id: 1482 },
                                                _documentLine_constructor: 'PurchaseReceiptLine',
                                                landedCost: { id: 'LandedCost001' },
                                                landedCostAllocation: {
                                                    line: { documentLine: { _id: 2630 } },
                                                },
                                                actualCostAmountInCompanyCurrency: 25.05,
                                                actualAllocatedCostAmountInCompanyCurrency: 0.0,
                                            },
                                            {
                                                documentLine: { _id: 1482 },
                                                _documentLine_constructor: 'PurchaseReceiptLine',
                                                landedCost: { id: 'LandedCost003' },
                                                landedCostAllocation: {
                                                    line: { documentLine: { _id: 2631 } },
                                                },
                                                actualCostAmountInCompanyCurrency: 14.54,
                                                actualAllocatedCostAmountInCompanyCurrency: 0.0,
                                            },
                                            {
                                                documentLine: { _id: 1482 },
                                                _documentLine_constructor: 'PurchaseReceiptLine',
                                                landedCost: { id: 'LandedCost003' },
                                                landedCostAllocation: {
                                                    line: { documentLine: { _id: 2631 } },
                                                },
                                                actualCostAmountInCompanyCurrency: 31.15,
                                                actualAllocatedCostAmountInCompanyCurrency: 0.0,
                                            },
                                        ],
                                        stockCorrectionDetails: [
                                            {
                                                documentLine: { _id: 1482 },
                                                _documentLine_constructor: 'PurchaseReceiptLine',
                                                reasonCode: { id: 'Landed cost adjustment' },
                                                amountToAbsorb: 31.15,
                                                nonAbsorbedAmount: 0.0,
                                                impactedQuantity: 3.0,
                                            },
                                        ],
                                    },
                                    {
                                        _id: 1491,
                                        landedCostLines: [
                                            {
                                                documentLine: { _id: 1491 },
                                                _documentLine_constructor: 'PurchaseReceiptLine',
                                                landedCost: { id: 'LandedCost003' },
                                                landedCostAllocation: {
                                                    line: { documentLine: { _id: 2631 } },
                                                },
                                                actualCostAmountInCompanyCurrency: 17.3,
                                                actualAllocatedCostAmountInCompanyCurrency: 0.0,
                                            },
                                        ],
                                        stockCorrectionDetails: [
                                            {
                                                documentLine: { _id: 1491 },
                                                _documentLine_constructor: 'PurchaseReceiptLine',
                                                reasonCode: { id: 'Landed cost adjustment' },
                                                amountToAbsorb: 6.92,
                                                nonAbsorbedAmount: 0.0,
                                                impactedQuantity: 1.0,
                                            },
                                            {
                                                documentLine: { _id: 1491 },
                                                _documentLine_constructor: 'PurchaseReceiptLine',
                                                reasonCode: { id: 'Landed cost adjustment' },
                                                amountToAbsorb: 10.38,
                                                nonAbsorbedAmount: 0.0,
                                                impactedQuantity: 1.0,
                                            },
                                        ],
                                    },
                                ],
                            },
                            {
                                orderLine: {
                                    // 3205
                                    numberOfReceipts: 1,
                                    receivedQuantity: 6.0,
                                    receivedQuantityInStockUnit: 6.0,
                                    quantityToReceive: 14.0,
                                    quantityToReceiveInStockUnit: 14.0,
                                    quantityReceivedInProgress: 6.0,
                                    lineReceiptStatus: 'partiallyReceived',
                                    status: 'closed',
                                },
                                landedCostLines: [
                                    {
                                        documentLine: { _id: 3205 },
                                        _documentLine_constructor: 'PurchaseOrderLine',
                                        landedCost: { id: 'LandedCost001' },
                                        landedCostAllocation: {
                                            line: { documentLine: { _id: 2630 } },
                                        },
                                        actualCostAmountInCompanyCurrency: 50.1,
                                        actualAllocatedCostAmountInCompanyCurrency: 50.1,
                                    },
                                    {
                                        documentLine: { _id: 3205 },
                                        _documentLine_constructor: 'PurchaseOrderLine',
                                        landedCost: { id: 'LandedCost003' },
                                        landedCostAllocation: {
                                            line: { documentLine: { _id: 2631 } },
                                        },
                                        actualCostAmountInCompanyCurrency: 31.71,
                                        actualAllocatedCostAmountInCompanyCurrency: 31.71,
                                    },
                                    {
                                        documentLine: { _id: 3205 },
                                        _documentLine_constructor: 'PurchaseOrderLine',
                                        landedCost: { id: 'LandedCost003' },
                                        landedCostAllocation: {
                                            line: { documentLine: { _id: 2631 } },
                                        },
                                        actualCostAmountInCompanyCurrency: 105.7,
                                        actualAllocatedCostAmountInCompanyCurrency: 105.7,
                                    },
                                ],
                                purchaseReceiptLines: [
                                    {
                                        _id: 1483,
                                        landedCostLines: [
                                            {
                                                documentLine: { _id: 1483 },
                                                _documentLine_constructor: 'PurchaseReceiptLine',
                                                landedCost: { id: 'LandedCost001' },
                                                landedCostAllocation: {
                                                    line: { documentLine: { _id: 2630 } },
                                                },
                                                actualCostAmountInCompanyCurrency: 50.1,
                                                actualAllocatedCostAmountInCompanyCurrency: 0.0,
                                            },
                                            {
                                                documentLine: { _id: 1483 },
                                                _documentLine_constructor: 'PurchaseReceiptLine',
                                                landedCost: { id: 'LandedCost003' },
                                                landedCostAllocation: {
                                                    line: { documentLine: { _id: 2631 } },
                                                },
                                                actualCostAmountInCompanyCurrency: 31.71,
                                                actualAllocatedCostAmountInCompanyCurrency: 0.0,
                                            },
                                            {
                                                documentLine: { _id: 1483 },
                                                _documentLine_constructor: 'PurchaseReceiptLine',
                                                landedCost: { id: 'LandedCost003' },
                                                landedCostAllocation: {
                                                    line: { documentLine: { _id: 2631 } },
                                                },
                                                actualCostAmountInCompanyCurrency: 105.7,
                                                actualAllocatedCostAmountInCompanyCurrency: 0.0,
                                            },
                                        ],
                                        stockCorrectionDetails: [
                                            {
                                                documentLine: { _id: 1483 },
                                                _documentLine_constructor: 'PurchaseReceiptLine',
                                                reasonCode: { id: 'Landed cost adjustment' },
                                                amountToAbsorb: 105.7,
                                                nonAbsorbedAmount: 0.0,
                                                impactedQuantity: 6.0,
                                            },
                                        ],
                                    },
                                ],
                            },
                            {
                                orderLine: {
                                    // 3206
                                    numberOfReceipts: 1,
                                    receivedQuantity: 9.0,
                                    receivedQuantityInStockUnit: 9.0,
                                    quantityToReceive: 21.0,
                                    quantityToReceiveInStockUnit: 21.0,
                                    quantityReceivedInProgress: Number(0.0),
                                    lineReceiptStatus: 'partiallyReceived',
                                    status: 'closed',
                                },
                                landedCostLines: [],
                                purchaseReceiptLines: [
                                    {
                                        _id: 1484,
                                        landedCostLines: [],
                                        stockCorrectionDetails: [],
                                    },
                                ],
                            },
                        ],
                    },
                    {
                        number: 'PO37',
                        purchaseOrderLines: [
                            {
                                orderLine: {
                                    // 3207
                                    numberOfReceipts: 3, // PR54 - PR55 - PR56
                                    receivedQuantity: 8.0,
                                    receivedQuantityInStockUnit: 8.0,
                                    quantityToReceive: 2.0,
                                    quantityToReceiveInStockUnit: 2.0,
                                    quantityReceivedInProgress: 8.0,
                                    lineReceiptStatus: 'partiallyReceived',
                                    status: 'closed',
                                },
                                landedCostLines: [
                                    {
                                        documentLine: { _id: 3207 },
                                        _documentLine_constructor: 'PurchaseOrderLine',
                                        landedCost: { id: 'LandedCost001' },
                                        landedCostAllocation: {
                                            line: { documentLine: { _id: 2630 } },
                                        },
                                        actualCostAmountInCompanyCurrency: 25.05,
                                        actualAllocatedCostAmountInCompanyCurrency: 25.05,
                                    },
                                    {
                                        documentLine: { _id: 3207 },
                                        _documentLine_constructor: 'PurchaseOrderLine',
                                        landedCost: { id: 'LandedCost001' },
                                        landedCostAllocation: {
                                            line: { documentLine: { _id: 2632 } },
                                        },
                                        actualCostAmountInCompanyCurrency: 22.26,
                                        actualAllocatedCostAmountInCompanyCurrency: 22.26,
                                    },
                                    {
                                        documentLine: { _id: 3207 },
                                        _documentLine_constructor: 'PurchaseOrderLine',
                                        landedCost: { id: 'LandedCost003' },
                                        landedCostAllocation: {
                                            line: { documentLine: { _id: 2633 } },
                                        },
                                        actualCostAmountInCompanyCurrency: 16.15,
                                        actualAllocatedCostAmountInCompanyCurrency: 16.15,
                                    },
                                    {
                                        documentLine: { _id: 3207 },
                                        _documentLine_constructor: 'PurchaseOrderLine',
                                        landedCost: { id: 'LandedCost003' },
                                        landedCostAllocation: {
                                            line: { documentLine: { _id: 2633 } },
                                        },
                                        actualCostAmountInCompanyCurrency: 40.39,
                                        actualAllocatedCostAmountInCompanyCurrency: 40.39,
                                    },
                                ],
                                purchaseReceiptLines: [
                                    {
                                        _id: 1485,
                                        landedCostLines: [
                                            {
                                                documentLine: { _id: 1485 },
                                                _documentLine_constructor: 'PurchaseReceiptLine',
                                                landedCost: { id: 'LandedCost001' },
                                                landedCostAllocation: {
                                                    line: { documentLine: { _id: 2630 } },
                                                },
                                                actualCostAmountInCompanyCurrency: 25.05,
                                                actualAllocatedCostAmountInCompanyCurrency: 0.0,
                                            },
                                            {
                                                documentLine: { _id: 1485 },
                                                _documentLine_constructor: 'PurchaseReceiptLine',
                                                landedCost: { id: 'LandedCost003' },
                                                landedCostAllocation: {
                                                    line: { documentLine: { _id: 2633 } },
                                                },
                                                actualCostAmountInCompanyCurrency: 10.1,
                                                actualAllocatedCostAmountInCompanyCurrency: 0.0,
                                            },
                                        ],
                                        stockCorrectionDetails: [
                                            {
                                                documentLine: { _id: 1485 },
                                                _documentLine_constructor: 'PurchaseReceiptLine',
                                                reasonCode: { id: 'Landed cost adjustment' },
                                                amountToAbsorb: 10.1,
                                                nonAbsorbedAmount: 0.0,
                                                impactedQuantity: 3.0,
                                            },
                                        ],
                                    },
                                    {
                                        _id: 1487,
                                        landedCostLines: [
                                            {
                                                documentLine: { _id: 1487 },
                                                _documentLine_constructor: 'PurchaseReceiptLine',
                                                landedCost: { id: 'LandedCost001' },
                                                landedCostAllocation: {
                                                    line: { documentLine: { _id: 2632 } },
                                                },
                                                actualCostAmountInCompanyCurrency: 22.26,
                                                actualAllocatedCostAmountInCompanyCurrency: 0.0,
                                            },
                                            {
                                                documentLine: { _id: 1487 },
                                                _documentLine_constructor: 'PurchaseReceiptLine',
                                                landedCost: { id: 'LandedCost003' },
                                                landedCostAllocation: {
                                                    line: { documentLine: { _id: 2633 } },
                                                },
                                                actualCostAmountInCompanyCurrency: 16.15,
                                                actualAllocatedCostAmountInCompanyCurrency: 0.0,
                                            },
                                            {
                                                documentLine: { _id: 1487 },
                                                _documentLine_constructor: 'PurchaseReceiptLine',
                                                landedCost: { id: 'LandedCost003' },
                                                landedCostAllocation: {
                                                    line: { documentLine: { _id: 2633 } },
                                                },
                                                actualCostAmountInCompanyCurrency: 13.46,
                                                actualAllocatedCostAmountInCompanyCurrency: 0.0,
                                            },
                                        ],
                                        stockCorrectionDetails: [
                                            {
                                                documentLine: { _id: 1487 },
                                                _documentLine_constructor: 'PurchaseReceiptLine',
                                                reasonCode: { id: 'Landed cost adjustment' },
                                                amountToAbsorb: 13.46,
                                                nonAbsorbedAmount: 0.0,
                                                impactedQuantity: 4.0,
                                            },
                                        ],
                                    },
                                    {
                                        _id: 1492,
                                        landedCostLines: [
                                            {
                                                documentLine: { _id: 1492 },
                                                _documentLine_constructor: 'PurchaseReceiptLine',
                                                landedCost: { id: 'LandedCost003' },
                                                landedCostAllocation: {
                                                    line: { documentLine: { _id: 2633 } },
                                                },
                                                actualCostAmountInCompanyCurrency: 16.83,
                                                actualAllocatedCostAmountInCompanyCurrency: 0.0,
                                            },
                                        ],
                                        stockCorrectionDetails: [
                                            {
                                                documentLine: { _id: 1492 },
                                                _documentLine_constructor: 'PurchaseReceiptLine',
                                                reasonCode: { id: 'Landed cost adjustment' },
                                                amountToAbsorb: 13.46,
                                                nonAbsorbedAmount: 0.0,
                                                impactedQuantity: 1.0,
                                            },
                                            {
                                                documentLine: { _id: 1492 },
                                                _documentLine_constructor: 'PurchaseReceiptLine',
                                                reasonCode: { id: 'Landed cost adjustment' },
                                                amountToAbsorb: 3.37,
                                                nonAbsorbedAmount: 0.0,
                                                impactedQuantity: 1.0,
                                            },
                                        ],
                                    },
                                ],
                            },
                            {
                                orderLine: {
                                    // 3208
                                    numberOfReceipts: 1, // PR55
                                    receivedQuantity: 14.0,
                                    receivedQuantityInStockUnit: 14.0,
                                    quantityToReceive: 6.0,
                                    quantityToReceiveInStockUnit: 6.0,
                                    quantityReceivedInProgress: 14.0,
                                    lineReceiptStatus: 'partiallyReceived',
                                    status: 'closed',
                                },
                                landedCostLines: [
                                    {
                                        documentLine: { _id: 3208 },
                                        _documentLine_constructor: 'PurchaseOrderLine',
                                        landedCost: { id: 'LandedCost001' },
                                        landedCostAllocation: {
                                            line: { documentLine: { _id: 2632 } },
                                        },
                                        actualCostAmountInCompanyCurrency: 77.94,
                                        actualAllocatedCostAmountInCompanyCurrency: 77.94,
                                    },
                                    {
                                        documentLine: { _id: 3208 },
                                        _documentLine_constructor: 'PurchaseOrderLine',
                                        landedCost: { id: 'LandedCost003' },
                                        landedCostAllocation: {
                                            line: { documentLine: { _id: 2633 } },
                                        },
                                        actualCostAmountInCompanyCurrency: 59.24,
                                        actualAllocatedCostAmountInCompanyCurrency: 59.24,
                                    },
                                    {
                                        documentLine: { _id: 3208 },
                                        _documentLine_constructor: 'PurchaseOrderLine',
                                        landedCost: { id: 'LandedCost003' },
                                        landedCostAllocation: {
                                            line: { documentLine: { _id: 2633 } },
                                        },
                                        actualCostAmountInCompanyCurrency: 84.62,
                                        actualAllocatedCostAmountInCompanyCurrency: 84.62,
                                    },
                                ],
                                purchaseReceiptLines: [
                                    {
                                        _id: 1488,
                                        landedCostLines: [
                                            {
                                                documentLine: { _id: 1488 },
                                                _documentLine_constructor: 'PurchaseReceiptLine',
                                                landedCost: { id: 'LandedCost001' },
                                                landedCostAllocation: {
                                                    line: { documentLine: { _id: 2632 } },
                                                },
                                                actualCostAmountInCompanyCurrency: 77.94,
                                                actualAllocatedCostAmountInCompanyCurrency: 0.0,
                                            },
                                            {
                                                documentLine: { _id: 1488 },
                                                _documentLine_constructor: 'PurchaseReceiptLine',
                                                landedCost: { id: 'LandedCost003' },
                                                landedCostAllocation: {
                                                    line: { documentLine: { _id: 2633 } },
                                                },
                                                actualCostAmountInCompanyCurrency: 59.24,
                                                actualAllocatedCostAmountInCompanyCurrency: 0.0,
                                            },
                                            {
                                                documentLine: { _id: 1488 },
                                                _documentLine_constructor: 'PurchaseReceiptLine',
                                                landedCost: { id: 'LandedCost003' },
                                                landedCostAllocation: {
                                                    line: { documentLine: { _id: 2633 } },
                                                },
                                                actualCostAmountInCompanyCurrency: 84.62,
                                                actualAllocatedCostAmountInCompanyCurrency: 0.0,
                                            },
                                        ],
                                        stockCorrectionDetails: [
                                            {
                                                documentLine: { _id: 1488 },
                                                _documentLine_constructor: 'PurchaseReceiptLine',
                                                reasonCode: { id: 'Landed cost adjustment' },
                                                amountToAbsorb: 84.62,
                                                nonAbsorbedAmount: 0.0,
                                                impactedQuantity: 14.0,
                                            },
                                        ],
                                    },
                                ],
                            },
                            {
                                orderLine: {
                                    // 3209
                                    numberOfReceipts: 1, // PR55
                                    receivedQuantity: 21.0,
                                    receivedQuantityInStockUnit: 21.0,
                                    quantityToReceive: 9.0,
                                    quantityToReceiveInStockUnit: 9.0,
                                    quantityReceivedInProgress: Number(0.0),
                                    lineReceiptStatus: 'partiallyReceived',
                                    status: 'closed',
                                },
                                landedCostLines: [],
                                purchaseReceiptLines: [
                                    {
                                        _id: 1489,
                                        landedCostLines: [],
                                        stockCorrectionDetails: [],
                                    },
                                ],
                            },
                        ],
                    },
                    {
                        number: 'PO38',
                        purchaseOrderLines: [
                            {
                                orderLine: {
                                    // 3210
                                    numberOfReceipts: 1,
                                    receivedQuantity: 3.0,
                                    receivedQuantityInStockUnit: 3.0,
                                    quantityToReceive: 7.0,
                                    quantityToReceiveInStockUnit: 7.0,
                                    quantityReceivedInProgress: Number(0.0),
                                    lineReceiptStatus: 'partiallyReceived',
                                    status: 'closed',
                                },
                                landedCostLines: [],
                                purchaseReceiptLines: [
                                    {
                                        _id: 1486,
                                        landedCostLines: [],
                                        stockCorrectionDetails: [],
                                    },
                                ],
                            },
                            {
                                orderLine: {
                                    // 3211
                                    numberOfReceipts: 1,
                                    receivedQuantity: 14.0,
                                    receivedQuantityInStockUnit: 14.0,
                                    quantityToReceive: 6.0,
                                    quantityToReceiveInStockUnit: 6.0,
                                    quantityReceivedInProgress: Number(0.0),
                                    lineReceiptStatus: 'partiallyReceived',
                                    status: 'closed',
                                },
                                landedCostLines: [],
                                purchaseReceiptLines: [
                                    {
                                        _id: 1490,
                                        landedCostLines: [],
                                        stockCorrectionDetails: [],
                                    },
                                ],
                            },
                            {
                                orderLine: {
                                    // 3212
                                    numberOfReceipts: 1,
                                    receivedQuantity: 30.0,
                                    receivedQuantityInStockUnit: 30.0,
                                    quantityToReceive: 0,
                                    quantityToReceiveInStockUnit: 0.0,
                                    quantityReceivedInProgress: Number(0.0),
                                    lineReceiptStatus: 'received',
                                    status: 'closed',
                                },
                                landedCostLines: [],
                                purchaseReceiptLines: [
                                    {
                                        _id: 1493,
                                        landedCostLines: [],
                                        stockCorrectionDetails: [],
                                    },
                                ],
                            },
                        ],
                    },
                ];

                await asyncArray(expectedResults).forEach(async expectedResult => {
                    const message = `PO number=${expectedResult.number}`;
                    const purchaseOrder = await context.read(xtremPurchasing.nodes.PurchaseOrder, {
                        number: expectedResult.number,
                    });
                    assert.deepEqual(
                        await purchaseOrder.lines.length,
                        expectedResult.purchaseOrderLines.length,
                        message,
                    );

                    await purchaseOrder.lines.forEach(async (line, indexLine) => {
                        const messagePoLine = `${message} line index=${indexLine}`;
                        await assertionPurchaseOrderLine(
                            line,
                            expectedResult.purchaseOrderLines[indexLine].orderLine,
                            messagePoLine,
                        );
                        await assertionLandedCostLine(
                            context,
                            { filter: { documentLine: { _id: line._id } }, orderBy: { _id: 1 } },
                            expectedResult.purchaseOrderLines[indexLine].landedCostLines,
                            messagePoLine,
                        );

                        assert.deepEqual(
                            await line.purchaseReceiptLines.length,
                            expectedResult.purchaseOrderLines[indexLine].purchaseReceiptLines.length,
                            messagePoLine,
                        );
                        await line.purchaseReceiptLines.forEach(async (po2pr, indexPo2Pr) => {
                            const receiptLine = await po2pr.purchaseReceiptLine;
                            const messagePrLine = `${messagePoLine} PR index=${indexPo2Pr} _id = ${receiptLine._id}`;
                            const expectedReceiptLine =
                                expectedResult.purchaseOrderLines[indexLine].purchaseReceiptLines[indexPo2Pr];
                            assert.deepEqual(receiptLine._id, expectedReceiptLine._id);
                            await assertionLandedCostLine(
                                context,
                                { filter: { documentLine: { _id: receiptLine._id } }, orderBy: { _id: 1 } },
                                expectedReceiptLine.landedCostLines,
                                messagePrLine,
                            );
                            await assertionStockCorrectionDetail(
                                context,
                                {
                                    filter: { documentLine: { _id: receiptLine._id } },
                                    orderBy: { _id: 1 },
                                },
                                expectedReceiptLine.stockCorrectionDetails,
                                messagePrLine,
                            );
                        });
                    });
                });
            },
            { today: '2024-01-22', testActiveServiceOptions: [xtremMasterData.serviceOptions.landedCostOption] },
        ));
});
