// tslint:disable:no-duplicate-string
import type { Context, NodeCreateData } from '@sage/xtrem-core';
import { date, Test } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremStructure from '@sage/xtrem-structure';
import * as xtremTax from '@sage/xtrem-tax';
import { assert } from 'chai';
import * as xtremPurchasing from '../../../index';

const { withSequenceNumberContext } = xtremMasterData.functions.testLib;

describe('PurchaseInvoiceNode', () => {
    before(() => {});

    function createPurchaseInvoice(context: Context, invoiceDate: date) {
        const baseData: NodeCreateData<xtremPurchasing.nodes.PurchaseInvoice> = {
            site: '#ETS1-S01',
            billBySupplier: '#US017',
            currency: '#EUR',
            invoiceDate,
            matchingStatus: 'variance',
            totalAmountExcludingTax: 28,
            totalTaxAmount: 8,
            lines: [
                {
                    _action: 'create',
                    item: '#NonStockManagedItem',
                    quantity: 1,
                    unit: '#KILOGRAM',
                    grossPrice: 100,
                },
                {
                    _action: 'create',
                    item: '#NonStockManagedItem',
                    quantity: 4,
                    unit: '#KILOGRAM',
                    grossPrice: 44.55,
                },
                {
                    _action: 'create',
                    item: '#NonStockManagedItem',
                    quantity: 5,
                    unit: '#KILOGRAM',
                    grossPrice: 44.55,
                },
            ],
            matchingUser: '#<EMAIL>',
        };
        return context.create(xtremPurchasing.nodes.PurchaseInvoice, baseData);
    }

    it('Update purchase invoice taxAmountAdjusted', () =>
        Test.withContext(
            async context => {
                const purchaseInvoice: xtremPurchasing.nodes.PurchaseInvoice = await createPurchaseInvoice(
                    context,
                    date.make(2022, 2, 1),
                );
                await purchaseInvoice.$.save({ flushDeferredActions: true });

                assert.equal(
                    String(await (await purchaseInvoice.taxes.at(0))?.tax),
                    'Tax deductible on debits, standard rate',
                );
                assert.equal(String(await (await purchaseInvoice.taxes.at(0))?.taxAmount), '100.19');
                assert.equal(String(await (await purchaseInvoice.taxes.at(0))?.deductibleTaxAmount), '100.19');
                assert.equal(String(await purchaseInvoice.totalTaxAmount), '8');
                assert.equal(String(await purchaseInvoice.totalAmountExcludingTax), '28');
                assert.equal(String(await (await purchaseInvoice.taxes.at(0))?.taxAmountAdjusted), '100.19');
                assert.equal(String(await (await (await purchaseInvoice.lines.at(0))?.taxes.at(0))?.taxAmount), '20');
                assert.equal(
                    String(await (await (await purchaseInvoice.lines.at(0))?.taxes.at(0))?.taxAmountAdjusted),
                    '20',
                );
                assert.equal(
                    String(await (await (await purchaseInvoice.lines.at(1))?.taxes.at(0))?.taxAmount),
                    '35.64',
                );
                assert.equal(
                    String(await (await (await purchaseInvoice.lines.at(1))?.taxes.at(0))?.taxAmountAdjusted),
                    '35.64',
                );

                await purchaseInvoice.$.set({
                    taxes: [
                        {
                            _id: (await purchaseInvoice.taxes.at(0))!._id,
                            _action: 'update',
                            taxAmountAdjusted: 107.59,
                        },
                    ],
                });

                await purchaseInvoice.$.save();

                assert.equal(String(await (await purchaseInvoice.taxes.at(0))?.taxAmount), '100.19');
                assert.equal(String(await (await purchaseInvoice.taxes.at(0))?.deductibleTaxAmount), '107.59');
                assert.equal(String(await (await purchaseInvoice.taxes.at(0))?.taxAmountAdjusted), '107.59');
                assert.equal(String(await purchaseInvoice.totalTaxAmount), '8');
                assert.equal(String(await purchaseInvoice.totalAmountExcludingTax), '28');
                assert.equal(String(await (await (await purchaseInvoice.lines.at(0))?.taxes.at(0))?.taxAmount), '20');
                assert.equal(
                    String(await (await (await purchaseInvoice.lines.at(0))?.taxes.at(0))?.taxAmountAdjusted),
                    '27.4',
                );
                assert.equal(
                    String(await (await (await purchaseInvoice.lines.at(0))?.taxes.at(0))?.deductibleTaxAmount),
                    '27.4',
                );
                assert.equal(
                    String(await (await (await purchaseInvoice.lines.at(1))?.taxes.at(0))?.taxAmount),
                    '35.64',
                );
                assert.equal(
                    String(await (await (await purchaseInvoice.lines.at(1))?.taxes.at(0))?.taxAmountAdjusted),
                    '35.64',
                );
                assert.equal(
                    String(await (await (await purchaseInvoice.lines.at(1))?.taxes.at(0))?.deductibleTaxAmount),
                    '35.64',
                );

                await purchaseInvoice.$.set({
                    lines: [
                        {
                            _action: 'create',
                            item: '#NonStockManagedItem',
                            quantity: 5.9,
                            unit: '#KILOGRAM',
                            grossPrice: 3.75,
                        },
                    ],
                });

                await purchaseInvoice.$.save();

                assert.equal(String(await (await purchaseInvoice.taxes.at(0))?.taxAmount), '104.62');
                assert.equal(String(await (await purchaseInvoice.taxes.at(0))?.deductibleTaxAmount), '112.02');
                assert.equal(String(await (await purchaseInvoice.taxes.at(0))?.taxAmountAdjusted), '112.02');
                assert.equal(String(await purchaseInvoice.totalTaxAmount), '8');
                assert.equal(String(await purchaseInvoice.totalAmountExcludingTax), '28');
                assert.equal(String(await (await (await purchaseInvoice.lines.at(0))?.taxes.at(0))?.taxAmount), '20');
                assert.equal(
                    String(await (await (await purchaseInvoice.lines.at(0))?.taxes.at(0))?.taxAmountAdjusted),
                    '27.4',
                );
                assert.equal(
                    String(await (await (await purchaseInvoice.lines.at(1))?.taxes.at(0))?.taxAmount),
                    '35.64',
                );
                assert.equal(
                    String(await (await (await purchaseInvoice.lines.at(1))?.taxes.at(0))?.taxAmountAdjusted),
                    '35.64',
                );

                assert.equal(String(await (await (await purchaseInvoice.lines.at(3))?.taxes.at(0))?.taxAmount), '4.43');
                assert.equal(
                    String(await (await (await purchaseInvoice.lines.at(3))?.taxes.at(0))?.taxAmountAdjusted),
                    '4.43',
                );
            },
            { today: '2022-02-01' },
        ));
    it('Verify chronological control between two purchase invoice on update', () =>
        withSequenceNumberContext(
            'PurchaseInvoice',
            { isChronological: true },
            async context => {
                const purchaseInvoice: xtremPurchasing.nodes.PurchaseInvoice = await createPurchaseInvoice(
                    context,
                    date.make(2022, 2, 2),
                );
                await purchaseInvoice.$.save({ flushDeferredActions: true });

                const purchaseInvoiceError: xtremPurchasing.nodes.PurchaseInvoice = await createPurchaseInvoice(
                    context,
                    date.make(2022, 2, 2),
                );
                await purchaseInvoiceError.$.save({ flushDeferredActions: true });
                await purchaseInvoiceError.$.set({ invoiceDate: date.make(2022, 2, 1) });

                await assert.isRejected(purchaseInvoiceError.$.save(), 'The record was not updated.');
            },
            { today: '2022-02-02' },
        ));

    it('Allocate a landed cost line to a receipt line with stock transaction status not completed - fails', () =>
        Test.withContext(
            async context => {
                const purchaseReceipt = await context.read(xtremPurchasing.nodes.PurchaseReceipt, {
                    number: 'PR240001',
                });
                const firstPurchaseReceiptLine = await purchaseReceipt.lines.elementAt(0);
                assert(await firstPurchaseReceiptLine.stockTransactionStatus, 'inProgress');

                const purchaseInvoice = await context.read(
                    xtremPurchasing.nodes.PurchaseInvoice,
                    { number: 'PI12' },
                    { forUpdate: true },
                );
                const firstPurchaseInvoiceLine = await purchaseInvoice.lines.elementAt(0);
                assert.isNotNull(await firstPurchaseInvoiceLine.landedCost);
                await purchaseInvoice.$.set({
                    lines: [
                        {
                            _action: 'update',
                            _id: firstPurchaseInvoiceLine._id,
                            landedCost: {
                                allocations: [
                                    {
                                        _action: 'create',
                                        allocatedDocumentLine: firstPurchaseReceiptLine._id,
                                        costAmount: 0,
                                        costAmountInCompanyCurrency: 0,
                                    },
                                ],
                            },
                        },
                    ],
                });
                await assert.isRejected(purchaseInvoice.$.save());
                assert.deepEqual(purchaseInvoice.$.context.diagnoses, [
                    {
                        message:
                            'The stock status of all purchase receipt lines allocated to landed cost needs to be Completed.',
                        path: ['lines', '2618', 'landedCost', 'allocations', '-1000000001'],
                        severity: 3,
                    },
                    {
                        message: 'The item: LandedCost002, is not managed for the supplier: LECLERC.',
                        path: ['lines', '2618'],
                        severity: 2,
                    },
                ]);
            },
            { testActiveServiceOptions: [xtremMasterData.serviceOptions.landedCostOption], today: '2024-11-02' },
        ));
});

describe('validateTaxCategoryAndPaymentTerm on Purchase Invoice', () => {
    function readPurchaseInvoice(context: Context, number: string): Promise<xtremPurchasing.nodes.PurchaseInvoice> {
        return context.read(xtremPurchasing.nodes.PurchaseInvoice, { number }, { forUpdate: true });
    }

    it('VAT/GST and payment term has discount/penalty', () =>
        Test.withContext(async context => {
            context.setServiceOptionsEnabledFlag(xtremStructure.serviceOptions.intacctActivationOption, true);
            const invoice = await readPurchaseInvoice(context, 'PI240005');

            await invoice.$.set({ paymentTerm: '#TEST_NET_45_ALL' });
            await assert.isRejected(invoice.$.save());
            assert.deepEqual(invoice.$.context.diagnoses, [
                {
                    message:
                        'Discounts and penalties exist for this payment term: Net 45. You cannot apply discounts and penalties in a VAT or GST transaction.',
                    path: [],
                    severity: 3,
                },
            ]);
        }));
    it('VAT/GST and payment term with no discount/penalty', () =>
        Test.withContext(async context => {
            context.setServiceOptionsEnabledFlag(xtremStructure.serviceOptions.intacctActivationOption, true);

            const paymentTerm = await context.read(
                xtremMasterData.nodes.PaymentTerm,
                { id: 'TEST_NET_45_ALL' },
                { forUpdate: true },
            );
            await paymentTerm.$.set({ discountAmount: 0, penaltyAmount: 0 });
            await paymentTerm.$.save();

            const invoice = await readPurchaseInvoice(context, 'PI240005');

            await invoice.$.set({ paymentTerm });
            await invoice.$.save();
            assert.deepEqual(invoice.$.context.diagnoses, []);
        }));
    it('isIntacctActivationOption is disabled', () =>
        Test.withContext(async context => {
            const invoice = await readPurchaseInvoice(context, 'PI240005');

            await invoice.$.set({ paymentTerm: '#TEST_NET_45_ALL' });
            assert.isOk(await invoice.$.control());
        }));
    it('No VAT/GST and payment term has discount/penalty', () =>
        Test.withContext(async context => {
            // Delete VAT tax solution
            const taxSolution = await context.read(xtremTax.nodes.TaxSolution, { id: 'FRSOL' }, { forUpdate: true });
            const vatTaxSolutionLine = await taxSolution.lines.at(0);
            if (vatTaxSolutionLine) {
                assert.equal(await (await vatTaxSolutionLine.taxCategory).id, 'VAT');
                await taxSolution.$.set({ lines: [{ _action: 'delete', _id: vatTaxSolutionLine._id }] });
                await taxSolution.$.save();
                assert.deepEqual(taxSolution.$.context.diagnoses, []);
            }
            context.setServiceOptionsEnabledFlag(xtremStructure.serviceOptions.intacctActivationOption, true);
            const invoice = await readPurchaseInvoice(context, 'PI240006');
            await invoice.$.set({ paymentTerm: '#TEST_NET_45_ALL' });
            assert.isOk(await invoice.$.control());
            assert.deepEqual(invoice.$.context.diagnoses, []);
        }));
});
