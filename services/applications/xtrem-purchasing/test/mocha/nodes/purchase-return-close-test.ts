import { Test, ValidationSeverity } from '@sage/xtrem-core';
import type { Diagnosis } from '@sage/xtrem-shared';
import { assert } from 'chai';
import * as xtremPurchasing from '../../../lib';

async function closeReturn(returnDoc: xtremPurchasing.nodes.PurchaseReturn, diagnoses?: Diagnosis[]) {
    if (diagnoses) {
        await assert.isRejected(
            xtremPurchasing.nodes.PurchaseReturn.close(returnDoc.$.context, returnDoc),
            'The record was not updated.',
        );
        assert.deepEqual(returnDoc.$.context.diagnoses, diagnoses);
    } else {
        assert.isTrue(await xtremPurchasing.nodes.PurchaseReturn.close(returnDoc.$.context, returnDoc));
    }

    assert.equal(await returnDoc.status, 'closed');
    assert.isTrue(await returnDoc.lines.every(async line => (await line.status) === 'closed'));
}

describe('Purchase return node - close', () => {
    it('purchase return - close a draft return ', () =>
        Test.withContext(async context => {
            const return25 = await context.read(
                xtremPurchasing.nodes.PurchaseReturn,
                { number: 'RET025' },
                { forUpdate: true },
            );
            assert.equal(await return25.status, 'draft');
            await closeReturn(return25);
        }));
    it('purchase return - close a inprogress return ', () =>
        Test.withContext(async context => {
            const return26 = await context.read(
                xtremPurchasing.nodes.PurchaseReturn,
                { number: 'RET026' },
                { forUpdate: true },
            );
            assert.equal(await return26.status, 'inProgress');
            await closeReturn(return26);
        }));
    it('purchase return - close a closed return ', () =>
        Test.withContext(async context => {
            const return27 = await context.read(
                xtremPurchasing.nodes.PurchaseReturn,
                { number: 'RET027' },
                { forUpdate: true },
            );
            assert.equal(await return27.status, 'closed');
            await closeReturn(return27, [
                {
                    path: [],
                    message: 'The record for the document cannot be updated when it is Closed: RET027.',
                    severity: ValidationSeverity.error,
                },
            ]);
        }));
    it('purchase return - close a pending Approval return', () =>
        Test.withContext(async context => {
            const return24 = await context.read(
                xtremPurchasing.nodes.PurchaseReturn,
                { number: 'RET024' },
                { forUpdate: true },
            );
            assert.equal(await return24.status, 'pending');
            assert.equal(await return24.approvalStatus, 'pendingApproval');

            await closeReturn(return24, [
                {
                    path: [],
                    message: 'You cannot change the status of the purchase return to the status you selected.',
                    severity: ValidationSeverity.error,
                },
            ]);
        }));
    it('purchase return - reject a return without closing it', () =>
        Test.withContext(async context => {
            const return24 = await context.read(
                xtremPurchasing.nodes.PurchaseReturn,
                { number: 'RET024' },
                { forUpdate: true },
            );
            assert.equal(await return24.status, 'pending');
            assert.equal(await return24.approvalStatus, 'pendingApproval');

            await return24.$.set({ approvalStatus: 'rejected' });

            await assert.isRejected(return24.$.save());

            assert.deepEqual(context.diagnoses, [
                {
                    path: [],
                    message: 'You cannot change the status of the purchase return to the status you selected.',
                    severity: ValidationSeverity.error,
                },
            ]);
        }));
});
