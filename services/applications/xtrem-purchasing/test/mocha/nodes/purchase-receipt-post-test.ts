import { Test } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremStockData from '@sage/xtrem-stock-data';
import { assert } from 'chai';
import * as xtremPurchasing from '../../../lib/index';
import type { LandedCostLineToTest } from '../fixtures/common';
import { assertionLandedCostLine } from '../fixtures/common';

describe('Purchase receipt node - post', () => {
    it('Try to post a receipt linked to order lines that have landed cost allocations not posted - fails', () =>
        Test.withContext(
            async context => {
                const purchaseReceipt = await context.read(xtremPurchasing.nodes.PurchaseReceipt, { number: 'PR48' });

                await assert.isRejected(
                    xtremPurchasing.nodes.PurchaseReceipt.post(context, purchaseReceipt),
                    `You need to post all the landed cost purchase invoices associated with the purchase order lines received before you can post the receipt.

PI14 - PI15`,
                );
            },
            { testActiveServiceOptions: [xtremMasterData.serviceOptions.landedCostOption] },
        ));

    it('Post a purchase receipt where landed cost have been added to the linked purchase order - success', () =>
        Test.withContext(
            async context => {
                const purchaseReceipt = await context.read(xtremPurchasing.nodes.PurchaseReceipt, { number: 'PR49' });

                await xtremPurchasing.nodes.PurchaseReceipt.post(context, purchaseReceipt);

                const purchaseReceiptLineIds = await purchaseReceipt.lines.map(line => line._id).toArray();

                const expectedLandedCostLineResults: LandedCostLineToTest[] = [
                    {
                        documentLine: { _id: 1474 },
                        landedCost: { id: 'LandedCost001' },
                        landedCostAllocation: {
                            line: { documentLine: { _id: 2625 } },
                        },
                        actualCostAmountInCompanyCurrency: 88.41,
                        actualAllocatedCostAmountInCompanyCurrency: 0.0,
                        _documentLine_constructor: 'PurchaseReceiptLine',
                    },
                    {
                        documentLine: { _id: 1475 },
                        landedCost: { id: 'LandedCost001' },
                        landedCostAllocation: {
                            line: { documentLine: { _id: 2625 } },
                        },
                        actualCostAmountInCompanyCurrency: 117.88,
                        actualAllocatedCostAmountInCompanyCurrency: 0.0,
                        _documentLine_constructor: 'PurchaseReceiptLine',
                    },
                    {
                        documentLine: { _id: 1475 },
                        landedCost: { id: 'LandedCost002' },
                        landedCostAllocation: {
                            line: { documentLine: { _id: 2626 } },
                        },
                        actualCostAmountInCompanyCurrency: 728.73,
                        actualAllocatedCostAmountInCompanyCurrency: 0.0,
                        _documentLine_constructor: 'PurchaseReceiptLine',
                    },
                    {
                        documentLine: { _id: 1477 },
                        landedCost: { id: 'LandedCost001' },
                        landedCostAllocation: {
                            line: { documentLine: { _id: 2625 } },
                        },
                        actualCostAmountInCompanyCurrency: 176.82,
                        actualAllocatedCostAmountInCompanyCurrency: 0.0,
                        _documentLine_constructor: 'PurchaseReceiptLine',
                    },
                    {
                        documentLine: { _id: 1477 },
                        landedCost: { id: 'LandedCost002' },
                        landedCostAllocation: {
                            line: { documentLine: { _id: 2626 } },
                        },
                        actualCostAmountInCompanyCurrency: 924.92,
                        actualAllocatedCostAmountInCompanyCurrency: 0.0,
                        _documentLine_constructor: 'PurchaseReceiptLine',
                    },
                ];

                await assertionLandedCostLine(
                    context,
                    {
                        filter: { documentLine: { _in: purchaseReceiptLineIds } },
                        orderBy: { documentLine: { _id: 1 }, landedCostAllocation: { _id: 1 } },
                    },
                    expectedLandedCostLineResults,
                );

                const expectedCorrectionDetailResults = [
                    {
                        correctedStockDetail: { _id: 1616 },
                        reasonCode: { _id: 4 },
                        amountToAbsorb: 88.41,
                        nonAbsorbedAmount: 0.0,
                        _correctedStockDetail_constructor: 'StockReceiptDetail',
                    },
                    {
                        correctedStockDetail: { _id: 1617 },
                        reasonCode: { _id: 4 },
                        amountToAbsorb: 29.47,
                        nonAbsorbedAmount: 0.0,
                        _correctedStockDetail_constructor: 'StockReceiptDetail',
                    },
                    {
                        correctedStockDetail: { _id: 1617 },
                        reasonCode: { _id: 4 },
                        amountToAbsorb: 182.18,
                        nonAbsorbedAmount: 0.0,
                        _correctedStockDetail_constructor: 'StockReceiptDetail',
                    },
                    {
                        correctedStockDetail: { _id: 1618 },
                        reasonCode: { _id: 4 },
                        amountToAbsorb: 88.41,
                        nonAbsorbedAmount: 0.0,
                        _correctedStockDetail_constructor: 'StockReceiptDetail',
                    },
                    {
                        correctedStockDetail: { _id: 1618 },
                        reasonCode: { _id: 4 },
                        amountToAbsorb: 546.55,
                        nonAbsorbedAmount: 0.0,
                        _correctedStockDetail_constructor: 'StockReceiptDetail',
                    },
                    {
                        correctedStockDetail: { _id: 1619 },
                        reasonCode: { _id: 4 },
                        amountToAbsorb: 176.82,
                        nonAbsorbedAmount: 0.0,
                        _correctedStockDetail_constructor: 'StockReceiptDetail',
                    },
                    {
                        correctedStockDetail: { _id: 1619 },
                        reasonCode: { _id: 4 },
                        amountToAbsorb: 924.92,
                        nonAbsorbedAmount: 0.0,
                        _correctedStockDetail_constructor: 'StockReceiptDetail',
                    },
                ];

                const actualCorrectionDetailResults = await context.select(
                    xtremStockData.nodes.StockCorrectionDetail,
                    {
                        correctedStockDetail: { _id: true },
                        reasonCode: { _id: true },
                        amountToAbsorb: true,
                        nonAbsorbedAmount: true,
                    },
                    {
                        filter: { documentLine: { _in: purchaseReceiptLineIds } },
                        orderBy: { correctedStockDetail: { _id: 1 }, _id: 1 },
                    },
                );
                assert.deepEqual(actualCorrectionDetailResults, expectedCorrectionDetailResults);
            },
            { testActiveServiceOptions: [xtremMasterData.serviceOptions.landedCostOption] },
        ));

    it('Check onceStockCompleted is called only when the document is completed', () =>
        Test.withContext(async context => {
            const result = await xtremStockData.functions.testLib.testOnceStockCompleted(context, {
                clas: xtremPurchasing.nodes.PurchaseReceipt,
                movementType: 'receipt',
                documents: [
                    { key: { number: 'PR1' }, isCompleted: false },
                    { key: { number: 'PR2' }, isCompleted: true },
                    { key: { number: 'PR3' }, isCompleted: false },
                    { key: { number: 'PR4' }, isCompleted: true },
                ],
                returnedProperty: 'number',
            });

            assert.deepEqual(result, ['PR2', 'PR4']);
        }));
});
