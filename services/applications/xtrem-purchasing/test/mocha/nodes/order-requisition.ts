import { date, Test } from '@sage/xtrem-core';
import { assert } from 'chai';
import * as xtremPurchasing from '../../../lib';

describe('Purchase order node origin requisition lines - update', () => {
    const testDate = '2020-08-10';
    it('Update purchase order PO1 with 2 requisition partial lines REQ7 and REQ8', () =>
        Test.withContext(
            async context => {
                const requisition7 = await context.read(xtremPurchasing.nodes.PurchaseRequisition, { number: 'REQ7' });
                const reqisition8 = await context.read(xtremPurchasing.nodes.PurchaseRequisition, { number: 'REQ8' });
                const firstReqLine7 = await requisition7.lines.elementAt(0);
                const firstReqLine8 = await reqisition8.lines.elementAt(0);

                let purchaseOrder = await context.read(
                    xtremPurchasing.nodes.PurchaseOrder,
                    { number: 'PO1' },
                    { forUpdate: true },
                );
                await purchaseOrder.$.set({
                    lines: [
                        {
                            item: '#Muesli',
                            grossPrice: 100,
                            unit: '#KILOGRAM',
                            quantity: 7,
                            stockUnit: '#GRAM',
                            quantityInStockUnit: 7000,
                            expectedReceiptDate: date.make(2020, 8, 10),
                            site: '#US001',
                            stockSite: '#US001',
                            /** requisitionLine 12 requisition : 7 - confirmed status REQ7  */
                            purchaseRequisitionLines: [
                                { purchaseRequisitionLine: firstReqLine7._id, orderedQuantity: 7 },
                            ],
                            _action: 'create',
                        },
                        {
                            item: '#Chemical C',
                            grossPrice: 100,
                            unit: '#KILOGRAM',
                            quantity: 8,
                            stockUnit: '#GRAM',
                            quantityInStockUnit: 8000,
                            expectedReceiptDate: date.make(2020, 8, 10),
                            site: '#US001',
                            stockSite: '#US001',
                            /** requisitionLine 13 requisition : 8 - confirmed status  REQ8 */
                            purchaseRequisitionLines: [
                                { purchaseRequisitionLine: firstReqLine8._id, orderedQuantity: 8 },
                            ],
                            _action: 'create',
                        },
                    ],
                });
                await purchaseOrder.$.save();
                assert.deepEqual(purchaseOrder.$.context.diagnoses, [
                    {
                        severity: 2,
                        path: ['lines', '-1000000007'],
                        message: 'The item: Chemical C, is not managed for the supplier: LECLERC.',
                    },
                ]);

                const req7 = await context.read(xtremPurchasing.nodes.PurchaseRequisition, { number: 'REQ7' });
                assert.isNotNull(await (await req7.lines.elementAt(0)).purchaseOrderLines.toArray());
                assert.equal(await req7.orderStatus, 'partiallyOrdered');
                assert.equal(await req7.status, 'inProgress');
                assert.equal(await (await req7.lines.elementAt(0)).lineOrderStatus, 'partiallyOrdered');
                assert.equal(await (await req7.lines.elementAt(0)).lineStatus, 'inProgress');
                assert.equal((await (await req7.lines.elementAt(0)).orderedQuantity).toString(), '7');
                assert.equal((await (await req7.lines.elementAt(0)).orderedQuantityInStockUnit).toString(), '7000');
                assert.equal((await (await req7.lines.elementAt(0)).quantityToOrder).toString(), '3');
                assert.equal((await (await req7.lines.elementAt(0)).quantityToOrderInStockUnit).toString(), '3000');
                const req8 = await context.read(xtremPurchasing.nodes.PurchaseRequisition, { number: 'REQ8' });
                assert.isNotNull(await (await req8.lines.elementAt(0)).purchaseOrderLines.toArray());
                assert.equal((await (await req8.lines.elementAt(0)).orderedQuantity).toString(), '8');
                assert.equal((await (await req8.lines.elementAt(0)).orderedQuantityInStockUnit).toString(), '8000');
                assert.equal((await (await req8.lines.elementAt(0)).quantityToOrder).toString(), '2');
                assert.equal((await (await req8.lines.elementAt(0)).quantityToOrderInStockUnit).toString(), '2000');
                assert.equal(await req8.orderStatus, 'partiallyOrdered');
                assert.equal(await req8.status, 'inProgress');
                assert.equal(await (await req8.lines.elementAt(0)).lineOrderStatus, 'partiallyOrdered');
                assert.equal(await (await req8.lines.elementAt(0)).lineStatus, 'inProgress');
                assert.equal(await (await req8.lines.elementAt(1)).lineOrderStatus, 'notOrdered');
                assert.equal(await (await req8.lines.elementAt(1)).lineStatus, 'pending');

                purchaseOrder = await context.read(
                    xtremPurchasing.nodes.PurchaseOrder,
                    { number: 'PO1' },
                    { forUpdate: true },
                );
                await purchaseOrder.$.set({
                    lines: [
                        {
                            item: '#Muesli',
                            grossPrice: 100,
                            unit: '#KILOGRAM',
                            quantity: 3,
                            stockUnit: '#GRAM',
                            quantityInStockUnit: 3000,
                            expectedReceiptDate: date.make(2020, 8, 10),
                            site: '#US001',
                            stockSite: '#US001',
                            purchaseRequisitionLines: [
                                { purchaseRequisitionLine: firstReqLine7._id, orderedQuantity: 3 },
                            ],
                            _action: 'create',
                        },
                        {
                            item: '#Chemical C',
                            grossPrice: 100,
                            unit: '#KILOGRAM',
                            quantity: 2,
                            stockUnit: '#GRAM',
                            quantityInStockUnit: 2000,
                            expectedReceiptDate: date.make(2020, 8, 10),
                            site: '#US001',
                            stockSite: '#US001',
                            purchaseRequisitionLines: [
                                { purchaseRequisitionLine: firstReqLine8._id, orderedQuantity: 2 },
                            ],
                            _action: 'create',
                        },
                    ],
                });
                await purchaseOrder.$.save();
                assert.deepEqual(purchaseOrder.$.context.diagnoses, [
                    {
                        severity: 2,
                        path: ['lines', '1'],
                        message: 'The item: Chemical C, is not managed for the supplier: LECLERC.',
                    },
                    {
                        severity: 2,
                        path: ['lines', '-1000000028'],
                        message: 'The item: Chemical C, is not managed for the supplier: LECLERC.',
                    },
                ]);

                const req7bis = await context.read(xtremPurchasing.nodes.PurchaseRequisition, { number: 'REQ7' });
                let lines = await req7bis.lines.toArray();
                assert.isNotNull(await lines[0].purchaseOrderLines.toArray());
                assert.equal(await req7bis.orderStatus, 'ordered');
                assert.equal(await req7bis.status, 'closed');
                assert.equal(await lines[0].lineOrderStatus, 'ordered');
                assert.equal(await lines[0].lineStatus, 'closed');

                const req8bis = await context.read(xtremPurchasing.nodes.PurchaseRequisition, { number: 'REQ8' });
                lines = await req8bis.lines.toArray();
                assert.isNotNull(await lines[0].purchaseOrderLines.toArray());
                assert.equal(await req8bis.orderStatus, 'partiallyOrdered');
                assert.equal(await req8bis.status, 'inProgress');
                assert.equal(await lines[0].lineOrderStatus, 'ordered');
                assert.equal(await lines[0].lineStatus, 'closed');

                assert.equal(await lines[1].lineOrderStatus, 'notOrdered');
                assert.equal(await lines[1].lineStatus, 'pending');
            },
            { today: testDate },
        ));
    it('Update purchase order PO1 with 1 requisition partial lines REQ7 and REQ8 then delete related REQ7 order line using po.$.set', () =>
        Test.withContext(
            async context => {
                let purchaseOrder = await context.read(
                    xtremPurchasing.nodes.PurchaseOrder,
                    { number: 'PO1' },
                    { forUpdate: true },
                );
                await purchaseOrder.$.set({
                    lines: [
                        {
                            item: '#Muesli',
                            grossPrice: 100,
                            unit: '#KILOGRAM',
                            quantity: 7,
                            stockUnit: '#GRAM',
                            quantityInStockUnit: 7000,
                            expectedReceiptDate: date.make(2020, 8, 10),
                            site: '#US001',
                            stockSite: '#US001',
                            purchaseRequisitionLines: [{ purchaseRequisitionLine: '#REQ7|10', orderedQuantity: 7 }],
                            _action: 'create',
                        },
                        {
                            item: '#Chemical C',
                            grossPrice: 100,
                            unit: '#KILOGRAM',
                            quantity: 8,
                            stockUnit: '#GRAM',
                            quantityInStockUnit: 8000,
                            expectedReceiptDate: date.make(2020, 8, 10),
                            site: '#US001',
                            stockSite: '#US001',
                            purchaseRequisitionLines: [{ purchaseRequisitionLine: '#REQ8|10', orderedQuantity: 8 }],
                            _action: 'create',
                        },
                    ],
                });
                assert.equal(await purchaseOrder.lines.length, 3);
                // purchaseOrder.$.control();
                // assert.deepEqual(purchaseOrder.$.context.diagnoses, []);
                await purchaseOrder.$.save();
                assert.deepEqual(purchaseOrder.$.context.diagnoses, [
                    {
                        severity: 2,
                        path: ['lines', '-1000000007'],
                        message: 'The item: Chemical C, is not managed for the supplier: LECLERC.',
                    },
                ]);

                purchaseOrder = await context.read(xtremPurchasing.nodes.PurchaseOrder, { number: 'PO1' });
                const lineToDeleteId = (await purchaseOrder.lines.elementAt(1))._id;
                const req7 = await context.read(xtremPurchasing.nodes.PurchaseRequisition, { number: 'REQ7' });
                assert.isNotNull(await (await req7.lines.elementAt(0)).purchaseOrderLines.toArray());
                assert.equal(await req7.orderStatus, 'partiallyOrdered');
                assert.equal(await req7.status, 'inProgress');
                assert.equal(await (await req7.lines.elementAt(0)).lineOrderStatus, 'partiallyOrdered');
                assert.equal(await (await req7.lines.elementAt(0)).lineStatus, 'inProgress');
                assert.equal((await (await req7.lines.elementAt(0)).orderedQuantity).toString(), '7');
                assert.equal((await (await req7.lines.elementAt(0)).orderedQuantityInStockUnit).toString(), '7000');
                assert.equal((await (await req7.lines.elementAt(0)).quantityToOrder).toString(), '3');
                assert.equal((await (await req7.lines.elementAt(0)).quantityToOrderInStockUnit).toString(), '3000');

                purchaseOrder = await context.read(
                    xtremPurchasing.nodes.PurchaseOrder,
                    { number: 'PO1' },
                    { forUpdate: true },
                );
                await purchaseOrder.$.set({
                    lines: [
                        { _sortValue: 100, _action: 'update', expectedReceiptDate: date.make(2020, 8, 11) },
                        {
                            _id: lineToDeleteId,
                            _action: 'delete',
                        },
                    ],
                });
                assert.equal(await purchaseOrder.lines.length, 2);
                await purchaseOrder.$.save();
                assert.deepEqual(purchaseOrder.$.context.diagnoses, [
                    {
                        severity: 2,
                        path: ['lines', '1'],
                        message: 'The item: Chemical C, is not managed for the supplier: LECLERC.',
                    },
                ]);

                const req7bis = await context.read(xtremPurchasing.nodes.PurchaseRequisition, { number: 'REQ7' });
                assert.isNotNull(await (await req7bis.lines.elementAt(0)).purchaseOrderLines.toArray());
                assert.equal(await req7bis.orderStatus, 'notOrdered');
                assert.equal(await req7bis.status, 'pending');
                assert.equal(await (await req7bis.lines.elementAt(0)).lineOrderStatus, 'notOrdered');
                assert.equal(await (await req7bis.lines.elementAt(0)).lineStatus, 'pending');

                purchaseOrder = await context.read(
                    xtremPurchasing.nodes.PurchaseOrder,
                    { number: 'PO1' },
                    { forUpdate: true },
                );
                await (await purchaseOrder.lines.elementAt(1)).$.delete();
                assert.deepEqual(purchaseOrder.$.context.diagnoses, []);

                const req8bis = await context.read(xtremPurchasing.nodes.PurchaseRequisition, { number: 'REQ8' });
                assert.isNotNull(await (await req8bis.lines.elementAt(0)).purchaseOrderLines.toArray());
                assert.equal(await req8bis.orderStatus, 'notOrdered');
                assert.equal(await req8bis.status, 'pending');
                assert.equal(await (await req8bis.lines.elementAt(0)).lineOrderStatus, 'notOrdered');
                assert.equal(await (await req8bis.lines.elementAt(0)).lineStatus, 'pending');
            },
            { today: testDate },
        ));
    it('Update purchase order PO1 with 2 requisition partial lines REQ7 and REQ8 then fully delete the order using po.$.delete()', () =>
        Test.withContext(
            async context => {
                let purchaseOrder = await context.read(
                    xtremPurchasing.nodes.PurchaseOrder,
                    { number: 'PO1' },
                    { forUpdate: true },
                );
                await purchaseOrder.$.set({
                    lines: [
                        {
                            _action: 'create',
                            item: '#Muesli',
                            grossPrice: 100,
                            unit: '#KILOGRAM',
                            quantity: 7,
                            stockUnit: '#GRAM',
                            quantityInStockUnit: 7000,
                            expectedReceiptDate: date.make(2020, 8, 10),
                            site: '#US001',
                            stockSite: '#US001',
                            purchaseRequisitionLines: [{ purchaseRequisitionLine: '#REQ7|10', orderedQuantity: 7 }],
                        },
                        {
                            _action: 'create',
                            item: '#Chemical C',
                            grossPrice: 100,
                            unit: '#KILOGRAM',
                            quantity: 8,
                            stockUnit: '#GRAM',
                            quantityInStockUnit: 8000,
                            expectedReceiptDate: date.make(2020, 8, 10),
                            site: '#US001',
                            stockSite: '#US001',
                            purchaseRequisitionLines: [{ purchaseRequisitionLine: '#REQ8|10', orderedQuantity: 8 }],
                        },
                    ],
                });
                assert.equal(await purchaseOrder.lines.length, 3);

                await purchaseOrder.$.save();
                assert.deepEqual(purchaseOrder.$.context.diagnoses, [
                    {
                        severity: 2,
                        path: ['lines', '-1000000007'],
                        message: 'The item: Chemical C, is not managed for the supplier: LECLERC.',
                    },
                ]);

                purchaseOrder = await context.read(xtremPurchasing.nodes.PurchaseOrder, { number: 'PO1' });
                const req7 = await context.read(xtremPurchasing.nodes.PurchaseRequisition, { number: 'REQ7' });
                assert.isNotNull(await (await req7.lines.elementAt(0)).purchaseOrderLines.toArray());
                assert.equal(await req7.orderStatus, 'partiallyOrdered');
                assert.equal(await req7.status, 'inProgress');
                assert.equal(await (await req7.lines.elementAt(0)).lineOrderStatus, 'partiallyOrdered');
                assert.equal(await (await req7.lines.elementAt(0)).lineStatus, 'inProgress');

                const req8 = await context.read(xtremPurchasing.nodes.PurchaseRequisition, { number: 'REQ8' });
                assert.isNotNull(await (await req8.lines.elementAt(0)).purchaseOrderLines.toArray());

                assert.equal(await req8.orderStatus, 'partiallyOrdered');
                assert.equal(await req8.status, 'inProgress');
                assert.equal(await (await req8.lines.elementAt(0)).lineOrderStatus, 'partiallyOrdered');
                assert.equal(await (await req8.lines.elementAt(0)).lineStatus, 'inProgress');
                assert.equal(await (await req8.lines.elementAt(1)).lineOrderStatus, 'notOrdered');
                assert.equal(await (await req8.lines.elementAt(1)).lineStatus, 'pending');

                purchaseOrder = await context.read(
                    xtremPurchasing.nodes.PurchaseOrder,
                    { number: 'PO1' },
                    { forUpdate: true },
                );
                assert.equal(await purchaseOrder.lines.length, 3);
                await purchaseOrder.$.delete();
                assert.deepEqual(purchaseOrder.$.context.diagnoses, []);

                const req7bis = await context.read(xtremPurchasing.nodes.PurchaseRequisition, { number: 'REQ7' });
                let lines = await req7bis.lines.toArray();
                assert.isNotNull(await lines[0].purchaseOrderLines.toArray());
                assert.equal(await req7bis.orderStatus, 'notOrdered');
                assert.equal(await req7bis.status, 'pending');
                assert.equal(await lines[0].lineOrderStatus, 'notOrdered');
                assert.equal(await lines[0].lineStatus, 'pending');

                const req8bis = await context.read(xtremPurchasing.nodes.PurchaseRequisition, { number: 'REQ8' });
                lines = await req8bis.lines.toArray();
                assert.isNotNull(await lines[0].purchaseOrderLines.toArray());
                assert.equal(await req8bis.orderStatus, 'notOrdered');
                assert.equal(await req8bis.status, 'pending');
                assert.equal(await lines[0].lineOrderStatus, 'notOrdered');
                assert.equal(await lines[0].lineStatus, 'pending');

                assert.equal(await lines[1].lineOrderStatus, 'notOrdered');
                assert.equal(await lines[1].lineStatus, 'pending');
                assert.equal((await lines[1].orderedQuantity).toString(), '0');
            },
            { today: testDate },
        ));
    it('Update purchase order PO17 with 3 requisition lines (fully ordering) from REQ8 and REQ9, but deleting bad existing one (for fail testing purpose) ', () =>
        Test.withContext(
            async context => {
                const purchaseOrder = await context.read(
                    xtremPurchasing.nodes.PurchaseOrder,
                    { number: 'PO17' },
                    { forUpdate: true },
                );
                await purchaseOrder.$.set({
                    lines: [
                        {
                            item: '#Muesli',
                            grossPrice: 10,
                            purchaseRequisitionLines: [{ purchaseRequisitionLine: 'REQ8|20', orderedQuantity: 15 }],
                            unit: '#KILOGRAM',
                            quantity: 15,
                            site: '#US001',
                            stockSite: '#US001',
                            stockUnit: '#GRAM',
                            _action: 'create',
                        },
                        {
                            item: '#Chemical C',
                            grossPrice: 10,
                            purchaseRequisitionLines: [{ purchaseRequisitionLine: 'REQ8|30', orderedQuantity: 20 }],
                            unit: '#KILOGRAM',
                            quantity: 20,
                            site: '#US001',
                            stockSite: '#US001',
                            stockUnit: '#GRAM',
                            _action: 'create',
                        },
                        {
                            item: '#Muesli',
                            grossPrice: 10,
                            purchaseRequisitionLines: [{ purchaseRequisitionLine: 'REQ9|10', orderedQuantity: 10 }],
                            unit: '#KILOGRAM',
                            quantity: 10,
                            site: '#US001',
                            stockSite: '#US001',
                            stockUnit: '#GRAM',
                            _action: 'create',
                        },
                        { _sortValue: 3100, _action: 'delete' },
                    ],
                });
                await purchaseOrder.$.save();
                assert.deepEqual(purchaseOrder.$.context.diagnoses, [
                    {
                        severity: 2,
                        path: ['lines', '-1000000007'],
                        message: 'The item: Chemical C, is not managed for the supplier: LECLERC.',
                    },
                ]);
                assert.equal(await purchaseOrder.lines.length, 3);
                const req8 = await context.read(xtremPurchasing.nodes.PurchaseRequisition, { number: 'REQ8' });
                assert.isNotNull(await (await req8.lines.elementAt(1)).purchaseOrderLines.toArray());
                assert.isNotNull(await (await req8.lines.elementAt(2)).purchaseOrderLines.toArray());
                assert.equal(await req8.orderStatus, 'partiallyOrdered');
                assert.equal(await req8.status, 'inProgress');
                assert.equal(await (await req8.lines.elementAt(0)).lineOrderStatus, 'notOrdered');
                assert.equal(await (await req8.lines.elementAt(0)).lineStatus, 'pending');
                assert.equal(await (await req8.lines.elementAt(1)).lineOrderStatus, 'ordered');
                assert.equal(await (await req8.lines.elementAt(1)).lineStatus, 'closed');
                assert.equal((await (await req8.lines.elementAt(1)).orderedQuantity).toString(), '15');
                assert.equal((await (await req8.lines.elementAt(1)).orderedQuantityInStockUnit).toString(), '15000');
                assert.equal((await (await req8.lines.elementAt(1)).quantityToOrder).toString(), '0');
                assert.equal((await (await req8.lines.elementAt(1)).quantityToOrderInStockUnit).toString(), '0');
                assert.equal(await (await req8.lines.elementAt(2)).lineOrderStatus, 'ordered');
                assert.equal(await (await req8.lines.elementAt(2)).lineStatus, 'closed');
                assert.equal((await (await req8.lines.elementAt(2)).orderedQuantity).toString(), '20');
                assert.equal((await (await req8.lines.elementAt(2)).orderedQuantityInStockUnit).toString(), '20000');
                assert.equal((await (await req8.lines.elementAt(2)).quantityToOrder).toString(), '0');
                assert.equal((await (await req8.lines.elementAt(2)).quantityToOrderInStockUnit).toString(), '0');
                const req9 = await context.read(xtremPurchasing.nodes.PurchaseRequisition, { number: 'REQ9' });
                assert.isNotNull(await (await req9.lines.elementAt(0)).purchaseOrderLines.toArray());
                assert.equal(await (await req9.lines.elementAt(0)).lineOrderStatus, 'ordered');
                assert.equal(await (await req9.lines.elementAt(0)).lineStatus, 'closed');
                assert.equal((await (await req9.lines.elementAt(0)).orderedQuantity).toString(), '10');
                assert.equal((await (await req9.lines.elementAt(0)).orderedQuantityInStockUnit).toString(), '10000');
                assert.equal((await (await req9.lines.elementAt(0)).quantityToOrder).toString(), '0');
                assert.equal((await (await req9.lines.elementAt(0)).quantityToOrderInStockUnit).toString(), '0');
                assert.equal(await req9.orderStatus, 'partiallyOrdered');
                assert.equal(await req9.status, 'inProgress');
                assert.equal(await (await req9.lines.elementAt(1)).lineOrderStatus, 'notOrdered');
                assert.equal(await (await req9.lines.elementAt(1)).lineStatus, 'pending');
            },
            { today: testDate },
        ));
});
describe('Purchase order node origin requisition lines - update fail', () => {
    const testDate = '2020-08-10';
    it('Update purchase order PO17 - quantity not equal to payload quantity in stock unit', () =>
        Test.withContext(
            async context => {
                const purchaseOrder = await context.read(
                    xtremPurchasing.nodes.PurchaseOrder,
                    { number: 'PO17' },
                    { forUpdate: true },
                );
                await purchaseOrder.$.set({
                    lines: [
                        {
                            _sortValue: 3100,
                            purchaseRequisitionLines: [
                                {
                                    purchaseRequisitionLine: 'REQ14|10',
                                    orderedQuantity: 8,
                                    orderedQuantityInStockUnit: 9,
                                },
                            ],
                            _action: 'update',
                        },
                    ],
                });
                await assert.isRejected(purchaseOrder.$.save());
                assert.deepEqual(purchaseOrder.$.context.diagnoses, [
                    {
                        message:
                            'The provided quantity in stock unit is different then the converted quantity from purchase unit to stock unit.',
                        path: ['lines', '31', 'purchaseRequisitionLines', '-1000000001', 'orderedQuantityInStockUnit'],
                        severity: 3,
                    },
                    {
                        message:
                            'The purchase order needs to have the same values for the following properties: supplier currency',
                        path: ['lines', '31', 'purchaseRequisitionLines', '-1000000001'],
                        severity: 3,
                    },
                ]);
                await assert.isRejected(purchaseOrder.$.save());
            },
            { today: testDate },
        ));
    it('Create purchase order PO17 - different purchase unit', () =>
        Test.withContext(
            async context => {
                const purchaseOrder = await context.read(
                    xtremPurchasing.nodes.PurchaseOrder,
                    { number: 'PO17' },
                    { forUpdate: true },
                );
                await purchaseOrder.$.set({
                    orderDate: date.make(2020, 8, 10),
                    lines: [
                        {
                            _sortValue: 3100,
                            quantity: 9,
                            unit: '#GRAM',
                            grossPrice: 30,
                            expectedReceiptDate: date.today(),
                            purchaseRequisitionLines: [
                                {
                                    purchaseRequisitionLine: 'REQ14|10',
                                    orderedQuantity: 9,
                                },
                            ],
                            _action: 'update',
                        },
                    ],
                });
                await assert.isRejected(purchaseOrder.$.save());
                assert.deepEqual(purchaseOrder.$.context.diagnoses, [
                    {
                        message:
                            'The purchase order needs to have the same values for the following properties: supplier currency',
                        path: ['lines', '31', 'purchaseRequisitionLines', '-1000000001'],
                        severity: 3,
                    },
                ]);
                await assert.isRejected(purchaseOrder.$.save());
            },
            { today: testDate },
        ));
    it('Create purchase order PO17 - different item', () =>
        Test.withContext(
            async context => {
                const purchaseOrder = await context.create(xtremPurchasing.nodes.PurchaseOrder, {
                    site: '#US001',
                    businessRelation: '#US017',
                    currency: '#EUR',
                    orderDate: date.make(2020, 8, 10),
                    fxRateDate: date.make(2020, 8, 10),
                    lines: [
                        {
                            item: '#Chemical C',
                            quantity: 8,
                            unit: '#KILOGRAM',
                            grossPrice: 30,
                            expectedReceiptDate: date.today(),
                            purchaseRequisitionLines: [
                                {
                                    purchaseRequisitionLine: '#REQ14|10',
                                    orderedQuantity: 8,
                                },
                            ],
                        },
                    ],
                });
                await assert.isRejected(purchaseOrder.$.save());
                assert.deepEqual(purchaseOrder.$.context.diagnoses, [
                    {
                        message:
                            'The purchase order needs to have the same values for the following properties: item currency',
                        path: ['lines', '-1000000002', 'purchaseRequisitionLines', '-1000000005'],
                        severity: 3,
                    },
                    {
                        message: 'The item: Chemical C, is not managed for the supplier: US017.',
                        path: ['lines', '-1000000002'],
                        severity: 2,
                    },
                ]);
                await assert.isRejected(purchaseOrder.$.save());
            },
            { today: testDate },
        ));
    it('Create purchase order PO17 - different supplier', () =>
        Test.withContext(
            async context => {
                const purchaseOrder = await context.read(
                    xtremPurchasing.nodes.PurchaseOrder,
                    { number: 'PO17' },
                    { forUpdate: true },
                );
                assert.equal(await purchaseOrder.status, 'draft');
                await purchaseOrder.$.set({
                    orderDate: date.make(2020, 8, 10),
                    lines: [
                        {
                            _sortValue: 3100,
                            quantity: 9,
                            unit: '#KILOGRAM',
                            grossPrice: 30,
                            expectedReceiptDate: date.today(),
                            purchaseRequisitionLines: [
                                {
                                    purchaseRequisitionLine: '#REQ14|10',
                                    orderedQuantity: 9,
                                },
                            ],
                            _action: 'update',
                        },
                    ],
                });
                await assert.isRejected(purchaseOrder.$.save());
                const lineSysID = (await purchaseOrder.lines.elementAt(0))._id.toString();
                assert.deepEqual(purchaseOrder.$.context.diagnoses, [
                    {
                        message:
                            'The purchase order needs to have the same values for the following properties: supplier currency',
                        path: ['lines', lineSysID, 'purchaseRequisitionLines', '-1000000001'],
                        severity: 3,
                    },
                ]);
                await assert.isRejected(purchaseOrder.$.save());
                assert.deepEqual(purchaseOrder.$.context.diagnoses, [
                    {
                        message:
                            'The purchase order needs to have the same values for the following properties: supplier currency',
                        path: ['lines', lineSysID, 'purchaseRequisitionLines', '-1000000001'],
                        severity: 3,
                    },
                ]);
            },
            { today: testDate },
        ));
    it('Create purchase order - different orderedQuantityInStockUnit in payload', () =>
        Test.withContext(
            async context => {
                const purchaseOrder = await context.create(xtremPurchasing.nodes.PurchaseOrder, {
                    site: '#US001',
                    businessRelation: '#US017',
                    currency: '#EUR',
                    orderDate: date.make(2020, 8, 10),
                    fxRateDate: date.make(2020, 8, 10),
                    lines: [
                        {
                            item: '#Chemical C',
                            quantity: 8,
                            unit: '#KILOGRAM',
                            grossPrice: 30,
                            expectedReceiptDate: date.today(),
                            purchaseRequisitionLines: [
                                {
                                    purchaseRequisitionLine: '#REQ14|10',
                                    orderedQuantity: 8,
                                    orderedQuantityInStockUnit: 18,
                                },
                            ],
                        },
                    ],
                });

                await assert.isRejected(purchaseOrder.$.save());
                assert.deepEqual(purchaseOrder.$.context.diagnoses, [
                    {
                        message:
                            'The provided quantity in stock unit is different then the converted quantity from purchase unit to stock unit.',
                        path: [
                            'lines',
                            '-1000000002',
                            'purchaseRequisitionLines',
                            '-1000000005',
                            'orderedQuantityInStockUnit',
                        ],
                        severity: 3,
                    },
                    {
                        message:
                            'The purchase order needs to have the same values for the following properties: item currency',
                        path: ['lines', '-1000000002', 'purchaseRequisitionLines', '-1000000005'],
                        severity: 3,
                    },
                    {
                        message: 'The item: Chemical C, is not managed for the supplier: US017.',
                        path: ['lines', '-1000000002'],
                        severity: 2,
                    },
                ]);
                await assert.isRejected(purchaseOrder.$.save());
            },
            { today: testDate },
        ));
});
