import { Test } from '@sage/xtrem-core';
import { assert } from 'chai';
import * as xtremPurchasing from '../../../lib/index';

describe('Purchase requisition line ', () => {
    it('allows changes of an allowed field if requisition is approved', () =>
        Test.withContext(async context => {
            const purchaseRequisition = await context.read(
                xtremPurchasing.nodes.PurchaseRequisition,
                { _id: '#REQ3' },
                { forUpdate: true },
            );

            assert.equal(await purchaseRequisition.approvalStatus, 'pendingApproval');
            assert.equal(await purchaseRequisition.status, 'draft');

            await xtremPurchasing.nodes.PurchaseRequisition.approve(context, purchaseRequisition, true);

            assert.equal(await purchaseRequisition.approvalStatus, 'approved');
            assert.equal(await purchaseRequisition.status, 'pending');

            await purchaseRequisition.$.set({
                lines: [
                    {
                        _action: 'update',
                        _id: (await purchaseRequisition.lines.at(0))?._id,
                        supplier: 'LECLERC',
                    },
                    {
                        _action: 'update',
                        _id: (await purchaseRequisition.lines.at(1))?._id,
                        supplier: 'LECLERC',
                    },
                ],
            });

            assert.isOk(await purchaseRequisition.$.control());
        }));

    it('denies changes of a field that does not support updates if requisition is approved', () =>
        Test.withContext(async context => {
            const purchaseRequisition = await context.read(
                xtremPurchasing.nodes.PurchaseRequisition,
                { _id: '#REQ3' },
                { forUpdate: true },
            );

            assert.equal(await purchaseRequisition.approvalStatus, 'pendingApproval');
            assert.equal(await purchaseRequisition.status, 'draft');

            await xtremPurchasing.nodes.PurchaseRequisition.approve(context, purchaseRequisition, true);

            assert.equal(await purchaseRequisition.approvalStatus, 'approved');
            assert.equal(await purchaseRequisition.status, 'pending');

            await purchaseRequisition.$.set({
                lines: [
                    {
                        _action: 'update',
                        _id: (await purchaseRequisition.lines.at(0))?._id,
                        quantity: 10,
                    },
                    {
                        _action: 'update',
                        _id: (await purchaseRequisition.lines.at(1))?._id,
                        quantity: 20,
                    },
                ],
            });

            await assert.isRejected(purchaseRequisition.$.save());

            assert.deepEqual(purchaseRequisition.$.context.diagnoses, [
                {
                    message:
                        'The purchase requisition is approved. You cannot add or delete a line. You can only edit certain fields.',
                    path: [
                        'lines',
                        String((await purchaseRequisition.lines.at(0))?._id),
                        'quantity',
                        'quantityToOrder',
                    ],
                    severity: 3,
                },
                {
                    message:
                        'The purchase requisition is approved. You cannot add or delete a line. You can only edit certain fields.',
                    path: [
                        'lines',
                        String((await purchaseRequisition.lines.at(1))?._id),
                        'quantity',
                        'quantityToOrder',
                    ],
                    severity: 3,
                },
            ]);
        }));
});
