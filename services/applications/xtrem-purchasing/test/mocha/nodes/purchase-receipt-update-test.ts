import { Test, ValidationSeverity } from '@sage/xtrem-core';
import { assert } from 'chai';
import * as xtremPurchasing from '../../../lib/index';

describe('Purchase receipt node - update', () => {
    const testDate = '2020-08-10';
    it('Update purchase receipt PR6 with 1 order partial lines PO15 and PO16 then delete related PO15 receipt line using po.$.set', () =>
        Test.withContext(
            async context => {
                let purchaseReceipt = await context.read(
                    xtremPurchasing.nodes.PurchaseReceipt,
                    { number: 'PR6' },
                    { forUpdate: true },
                );
                const chemicalOrderLine = await context.read(xtremPurchasing.nodes.PurchaseOrderLine, {
                    _id: '#PO16|2800',
                });
                const chemicalOrderId = (await chemicalOrderLine.document)._id;
                await purchaseReceipt.$.set({
                    lines: [
                        {
                            item: '#Muesli',
                            grossPrice: 100,
                            unit: '#KILOGRAM',
                            quantity: 7,
                            stockUnit: '#GRAM',
                            quantityInStockUnit: 7000,
                            purchaseOrderLine: { purchaseOrderLine: '#PO15|2700' },
                            _action: 'create',
                        },
                        {
                            item: '#Chemical C',
                            grossPrice: 100,
                            unit: '#KILOGRAM',
                            quantity: 8,
                            stockUnit: '#GRAM',
                            quantityInStockUnit: 8000,
                            purchaseOrderLine: { purchaseOrderLine: chemicalOrderLine },
                            _action: 'create',
                        },
                    ],
                });
                assert.equal(await purchaseReceipt.lines.length, 3);
                await purchaseReceipt.$.save();
                await context.flushDeferredSaves();

                Test.assertDeepEqualDiagnoses(purchaseReceipt.$.context.diagnoses, [
                    {
                        severity: ValidationSeverity.warn,
                        path: ['lines', '<???>'],
                        message: 'The item: Chemical C, is not managed for the supplier: LECLERC.',
                    },
                    {
                        message: `While saving PurchaseOrder(${chemicalOrderId}): The item: Chemical C, is not managed for the supplier: LECLERC.`,
                        path: [],
                        severity: 2,
                    },
                    {
                        message: `While saving PurchaseOrder(${chemicalOrderId}): While saving PurchaseReceipt(<???>): The item: Chemical C, is not managed for the supplier: LECLERC.`,
                        path: [],
                        severity: 2,
                    },
                    {
                        message: `While saving PurchaseOrder(${chemicalOrderId}): While saving PurchaseInvoice(<???>): The item: Chemical C, is not managed for the supplier: LECLERC.`,
                        path: [],
                        severity: 2,
                    },
                ]);
                // rollbackCache function will flush deferred actions and saves
                await Test.rollbackCache(context);

                purchaseReceipt = await context.read(
                    xtremPurchasing.nodes.PurchaseReceipt,
                    { number: 'PR6' },
                    { forUpdate: true },
                );
                assert.equal(await purchaseReceipt.lines.length, 3);
                assert.isNotNull(await (await purchaseReceipt.lines.elementAt(1)).purchaseOrderLine);
                assert.isNotNull(await (await purchaseReceipt.lines.elementAt(2)).purchaseOrderLine);

                const po15 = await context.read(xtremPurchasing.nodes.PurchaseOrder, { number: 'PO15' });
                assert.isNotNull((await po15.lines.elementAt(0)).purchaseReceiptLines);
                assert.equal(await po15.receiptStatus, 'notReceived'); // TODO: working when pnpm run start; not when mocha context ?
                assert.equal(await (await po15.lines.elementAt(0)).lineReceiptStatus, 'notReceived');
                assert.equal((await (await po15.lines.elementAt(0)).receivedQuantity).toString(), '7');
                assert.equal((await (await po15.lines.elementAt(0)).receivedQuantityInStockUnit).toString(), '7000');
                assert.equal((await (await po15.lines.elementAt(0)).quantityToReceive).toString(), '3');
                assert.equal((await (await po15.lines.elementAt(0)).quantityToReceiveInStockUnit).toString(), '3000');

                const po16 = await context.read(xtremPurchasing.nodes.PurchaseOrder, { number: 'PO16' });
                assert.isNotNull((await po16.lines.elementAt(0)).purchaseReceiptLines);
                assert.equal(await po16.receiptStatus, 'notReceived');
                assert.equal(await (await po16.lines.elementAt(0)).lineReceiptStatus, 'notReceived');
                assert.equal((await (await po16.lines.elementAt(0)).receivedQuantity).toString(), '8');
                assert.equal((await (await po16.lines.elementAt(0)).receivedQuantityInStockUnit).toString(), '8000');
                assert.equal((await (await po16.lines.elementAt(0)).quantityToReceive).toString(), '2');
                assert.equal((await (await po16.lines.elementAt(0)).quantityToReceiveInStockUnit).toString(), '2000');

                let lineToDeleteId = (await purchaseReceipt.lines.elementAt(1))._id;
                const lastLineId = (await purchaseReceipt.lines.elementAt(2))._id;
                await purchaseReceipt.$.set({
                    lines: [{ _id: lineToDeleteId, _action: 'delete' }],
                });
                assert.equal(await purchaseReceipt.lines.length, 2);
                await purchaseReceipt.$.save();
                await context.flushDeferredSaves();

                assert.deepEqual(purchaseReceipt.$.context.diagnoses, [
                    {
                        message: 'The item: Chemical C, is not managed for the supplier: LECLERC.',
                        path: ['lines', lastLineId.toString()],
                        severity: ValidationSeverity.warn,
                    },
                ]);

                await Test.rollbackCache(context);

                purchaseReceipt = await context.read(
                    xtremPurchasing.nodes.PurchaseReceipt,
                    { number: 'PR6' },
                    { forUpdate: true },
                );
                assert.equal(await purchaseReceipt.lines.length, 2);
                assert.isNotNull(await (await purchaseReceipt.lines.elementAt(1)).purchaseOrderLine);

                const po15bis = await context.read(xtremPurchasing.nodes.PurchaseOrder, { number: 'PO15' });
                assert.isNotNull((await po15bis.lines.elementAt(0)).purchaseReceiptLines);
                assert.equal(await po15bis.status, 'pending');
                assert.equal(await (await po15bis.lines.elementAt(0)).lineReceiptStatus, 'notReceived');
                assert.equal((await (await po15bis.lines.elementAt(0)).receivedQuantity).toString(), '0');
                assert.equal((await (await po15bis.lines.elementAt(0)).receivedQuantityInStockUnit).toString(), '0');
                assert.equal((await (await po15bis.lines.elementAt(0)).quantityToReceive).toString(), '10');
                assert.equal(
                    (await (await po15bis.lines.elementAt(0)).quantityToReceiveInStockUnit).toString(),
                    '10000',
                );

                // TODO:The second delete for this receipt is not persisted, so PO16 not being updated
                // I manually tested this case and it's working; seems to be related to mocha or testing framework
                lineToDeleteId = (await purchaseReceipt.lines.elementAt(1))._id;
                await purchaseReceipt.$.set({
                    lines: [{ _id: lineToDeleteId, _action: 'delete' }],
                });

                await context.flushDeferredSaves();

                // These 2 warnings are generated by PurchaseOrderLineToPurchaseReceiptLine's deleteEnd event.
                Test.assertDeepEqualDiagnoses(purchaseReceipt.$.context.diagnoses, [
                    {
                        message: `While saving PurchaseOrder(${chemicalOrderId}): The item: Chemical C, is not managed for the supplier: LECLERC.`,
                        path: [],
                        severity: ValidationSeverity.warn,
                    },
                    {
                        message: `While saving PurchaseOrder(${chemicalOrderId}): While saving PurchaseReceipt(<???>): The item: Chemical C, is not managed for the supplier: LECLERC.`,
                        path: [],
                        severity: ValidationSeverity.warn,
                    },
                    {
                        message: `While saving PurchaseOrder(${chemicalOrderId}): While saving PurchaseInvoice(<???>): The item: Chemical C, is not managed for the supplier: LECLERC.`,
                        path: [],
                        severity: ValidationSeverity.warn,
                    },
                ]);

                purchaseReceipt = await context.read(
                    xtremPurchasing.nodes.PurchaseReceipt,
                    { number: 'PR6' },
                    { forUpdate: true },
                );
                assert.equal(await purchaseReceipt.lines.length, 1);

                const po16bis = await context.read(xtremPurchasing.nodes.PurchaseOrder, { number: 'PO16' });
                assert.isNotNull(await (await po16bis.lines.elementAt(0)).purchaseReceiptLines.toArray());
                assert.equal(await po16bis.status, 'pending');
            },
            { today: testDate },
        ));

    it('Update purchase receipt PR6 with 2 order partial lines PO15 and PO16 then fully delete the receipt using po.$.delete()', () =>
        Test.withContext(
            async context => {
                let purchaseReceipt = await context.read(
                    xtremPurchasing.nodes.PurchaseReceipt,
                    { number: 'PR6' },
                    { forUpdate: true },
                );
                const chemicalOrderLine = await context.read(xtremPurchasing.nodes.PurchaseOrderLine, {
                    _id: '#PO16|2800',
                });
                const chemicalOrderId = (await chemicalOrderLine.document)._id;
                await purchaseReceipt.$.set({
                    lines: [
                        {
                            item: '#Muesli',
                            grossPrice: 100,
                            unit: '#KILOGRAM',
                            quantity: 7,
                            stockUnit: '#GRAM',
                            quantityInStockUnit: 7000,
                            purchaseOrderLine: { purchaseOrderLine: '#PO15|2700' },
                            _action: 'create',
                        },
                        {
                            item: '#Chemical C',
                            grossPrice: 100,
                            unit: '#KILOGRAM',
                            quantity: 8,
                            stockUnit: '#GRAM',
                            quantityInStockUnit: 8000,
                            purchaseOrderLine: { purchaseOrderLine: chemicalOrderLine },
                            _action: 'create',
                        },
                    ],
                });
                assert.equal(await purchaseReceipt.lines.length, 3);
                await purchaseReceipt.$.save();
                await context.flushDeferredSaves();

                Test.assertDeepEqualDiagnoses(purchaseReceipt.$.context.diagnoses, [
                    {
                        message: 'The item: Chemical C, is not managed for the supplier: LECLERC.',
                        path: ['lines', '<???>'],
                        severity: ValidationSeverity.warn,
                    },
                    {
                        message: `While saving PurchaseOrder(${chemicalOrderId}): The item: Chemical C, is not managed for the supplier: LECLERC.`,
                        path: [],
                        severity: 2,
                    },
                    {
                        message: `While saving PurchaseOrder(${chemicalOrderId}): While saving PurchaseReceipt(<???>): The item: Chemical C, is not managed for the supplier: LECLERC.`,
                        path: [],
                        severity: 2,
                    },
                    {
                        message: `While saving PurchaseOrder(${chemicalOrderId}): While saving PurchaseInvoice(<???>): The item: Chemical C, is not managed for the supplier: LECLERC.`,
                        path: [],
                        severity: 2,
                    },
                ]);

                await Test.rollbackCache(context);

                purchaseReceipt = await context.read(
                    xtremPurchasing.nodes.PurchaseReceipt,
                    { number: 'PR6' },
                    { forUpdate: true },
                );
                assert.equal(await purchaseReceipt.lines.length, 3);
                assert.isNotNull(await (await purchaseReceipt.lines.elementAt(1)).purchaseOrderLine);
                assert.isNotNull(await (await purchaseReceipt.lines.elementAt(2)).purchaseOrderLine);

                const po15 = await context.read(xtremPurchasing.nodes.PurchaseOrder, { number: 'PO15' });
                assert.isNotNull((await po15.lines.elementAt(0)).purchaseReceiptLines);
                assert.equal(await po15.receiptStatus, 'notReceived'); // TODO: working when pnpm run start; not when mocha context ?
                assert.equal(await (await po15.lines.elementAt(0)).lineReceiptStatus, 'notReceived');
                assert.equal((await (await po15.lines.elementAt(0)).receivedQuantity).toString(), '7');
                assert.equal((await (await po15.lines.elementAt(0)).receivedQuantityInStockUnit).toString(), '7000');
                assert.equal((await (await po15.lines.elementAt(0)).quantityToReceive).toString(), '3');
                assert.equal((await (await po15.lines.elementAt(0)).quantityToReceiveInStockUnit).toString(), '3000');

                const po16 = await context.read(xtremPurchasing.nodes.PurchaseOrder, { number: 'PO16' });
                assert.isNotNull((await po16.lines.elementAt(0)).purchaseReceiptLines);
                assert.equal((await (await po16.lines.elementAt(0)).receivedQuantity).toString(), '8');
                assert.equal((await (await po16.lines.elementAt(0)).receivedQuantityInStockUnit).toString(), '8000');
                assert.equal((await (await po16.lines.elementAt(0)).quantityToReceive).toString(), '2');
                assert.equal((await (await po16.lines.elementAt(0)).quantityToReceiveInStockUnit).toString(), '2000');
                assert.equal(await po16.receiptStatus, 'notReceived');
                assert.equal(await (await po16.lines.elementAt(0)).lineReceiptStatus, 'notReceived');
                assert.equal(await (await po16.lines.elementAt(1)).lineReceiptStatus, 'notReceived');
                assert.equal(await (await po16.lines.elementAt(1)).lineReceiptStatus, 'notReceived');

                await purchaseReceipt.$.delete();
                await context.flushDeferredSaves();

                Test.assertDeepEqualDiagnoses(purchaseReceipt.$.context.diagnoses, [
                    {
                        message: `While saving PurchaseOrder(${chemicalOrderId}): The item: Chemical C, is not managed for the supplier: LECLERC.`,
                        path: [],
                        severity: 2,
                    },
                    {
                        message: `While saving PurchaseOrder(${chemicalOrderId}): While saving PurchaseReceipt(<???>): The item: Chemical C, is not managed for the supplier: LECLERC.`,
                        path: [],
                        severity: 2,
                    },
                    {
                        message: `While saving PurchaseOrder(${chemicalOrderId}): While saving PurchaseInvoice(<???>): The item: Chemical C, is not managed for the supplier: LECLERC.`,
                        path: [],
                        severity: 2,
                    },
                ]);

                await Test.rollbackCache(context);
                await assert.isRejected(context.read(xtremPurchasing.nodes.PurchaseReceipt, { number: 'PR6' }));

                const po15bis = await context.read(xtremPurchasing.nodes.PurchaseOrder, { number: 'PO15' });
                assert.isNotNull((await po15bis.lines.elementAt(0)).purchaseReceiptLines);
                assert.equal(await po15bis.status, 'pending');
                assert.equal(await po15bis.receiptStatus, 'notReceived');
                assert.equal(await (await po15bis.lines.elementAt(0)).lineReceiptStatus, 'notReceived');
                const po16bis = await context.read(xtremPurchasing.nodes.PurchaseOrder, { number: 'PO16' });
                assert.isNotNull((await po16bis.lines.elementAt(0)).purchaseReceiptLines);
                assert.equal(await po16bis.status, 'pending');
                assert.equal(await po16bis.receiptStatus, 'notReceived');
                assert.equal(await (await po16bis.lines.elementAt(0)).lineReceiptStatus, 'notReceived');
            },
            { today: testDate },
        ));
    it('Create purchase receipt with 1 order line from PO15 then delete the receipt', () =>
        Test.withContext(
            async context => {
                const po15bis = await context.read(xtremPurchasing.nodes.PurchaseOrder, { number: 'PO15' });
                assert.equal(await po15bis.receiptStatus, 'notReceived');
                const purchaseReceipts = await xtremPurchasing.nodes.PurchaseOrder.createPurchaseReceipt(
                    context,
                    po15bis,
                );
                assert.equal(purchaseReceipts.length, 1);

                const po15bis2 = await context.read(xtremPurchasing.nodes.PurchaseOrder, { number: 'PO15' });
                assert.isNotNull(await (await po15bis2.lines.elementAt(0)).purchaseReceiptLines.toArray());
                assert.equal(await po15bis2.receiptStatus, 'notReceived');
                const receiptId = (
                    await (
                        await (
                            await (await po15bis2.lines.elementAt(0)).purchaseReceiptLines.elementAt(0)
                        ).purchaseReceiptLine
                    ).document
                )._id;
                const poReceipt2 = await context.read(
                    xtremPurchasing.nodes.PurchaseReceipt,
                    { _id: receiptId },
                    { forUpdate: true },
                );
                await poReceipt2.$.delete();
                const po15bis3 = await context.read(xtremPurchasing.nodes.PurchaseOrder, { number: 'PO15' });
                assert.equal(await po15bis3.receiptStatus, 'notReceived');
            },
            { today: testDate },
        ));
    it('Update purchase receipt with stock transaction status error to check display status update', () =>
        Test.withContext(
            async context => {
                const document = await context.read(xtremPurchasing.nodes.PurchaseOrder, { number: 'PO5' });
                assert.equal(await document.receiptStatus, 'notReceived');
                const [receipt] = await xtremPurchasing.nodes.PurchaseOrder.createPurchaseReceipt(context, document);
                await context.flushDeferredActions();
                assert.isDefined(receipt);
                const receiptNumber = await receipt.number;
                assert.equal(receiptNumber, 'PR200001');

                const receiptId = (
                    await (
                        await (
                            await (await document.lines.elementAt(0)).purchaseReceiptLines.elementAt(0)
                        ).purchaseReceiptLine
                    ).document
                )._id;
                const poReceipt2 = await context.read(
                    xtremPurchasing.nodes.PurchaseReceipt,
                    { _id: receiptId },
                    { forUpdate: true },
                );
                assert.equal(await poReceipt2.displayStatus, 'draft');
                const lineId = (await poReceipt2.lines.elementAt(0))._id;
                await poReceipt2.$.set({ lines: [{ _id: lineId, stockTransactionStatus: 'error' }] });
                await poReceipt2.$.save();
                assert.equal(await poReceipt2.displayStatus, 'error');
            },
            { today: testDate },
        ));
    it('Update purchase receipt with stock transaction status inProgress to check display status update', () =>
        Test.withContext(
            async context => {
                const document = await context.read(xtremPurchasing.nodes.PurchaseOrder, { number: 'PO5' });
                assert.equal(await document.receiptStatus, 'notReceived');
                const purchaseReceipts = await xtremPurchasing.nodes.PurchaseOrder.createPurchaseReceipt(
                    context,
                    document,
                );
                assert.equal(purchaseReceipts.length, 1);
                const receiptId = (
                    await (
                        await (
                            await (await document.lines.elementAt(0)).purchaseReceiptLines.elementAt(0)
                        ).purchaseReceiptLine
                    ).document
                )._id;
                const poReceipt2 = await context.read(
                    xtremPurchasing.nodes.PurchaseReceipt,
                    { _id: receiptId },
                    { forUpdate: true },
                );
                assert.equal(await poReceipt2.displayStatus, 'draft');
                const lineId = (await poReceipt2.lines.elementAt(0))._id;
                await poReceipt2.$.set({ lines: [{ _id: lineId, stockTransactionStatus: 'inProgress' }] });
                await poReceipt2.$.save();
                assert.equal(await poReceipt2.displayStatus, 'postingInProgress');
            },
            { today: testDate },
        ));
    it('Update purchase receipt with stock transaction status completed to check display status update', () =>
        Test.withContext(
            async context => {
                const document = await context.read(xtremPurchasing.nodes.PurchaseOrder, { number: 'PO5' });
                assert.equal(await document.receiptStatus, 'notReceived');
                const purchaseReceipts = await xtremPurchasing.nodes.PurchaseOrder.createPurchaseReceipt(
                    context,
                    document,
                );
                assert.equal(purchaseReceipts.length, 1);
                const receiptId = (
                    await (
                        await (
                            await (await document.lines.elementAt(0)).purchaseReceiptLines.elementAt(0)
                        ).purchaseReceiptLine
                    ).document
                )._id;
                const poReceipt2 = await context.read(
                    xtremPurchasing.nodes.PurchaseReceipt,
                    { _id: receiptId },
                    { forUpdate: true },
                );
                assert.equal(await poReceipt2.displayStatus, 'draft');
                const lineId = (await poReceipt2.lines.elementAt(0))._id;
                await poReceipt2.$.set({ lines: [{ _id: lineId, stockTransactionStatus: 'completed' }] });
                await poReceipt2.$.save();
                assert.equal(await poReceipt2.displayStatus, 'received');
            },
            { today: testDate },
        ));
    it('Purchase receipt - new line on PR36 - Status:  draft', () =>
        Test.withContext(async context => {
            const purchaseReceipt = await context.read(
                xtremPurchasing.nodes.PurchaseReceipt,
                { number: 'PR36' },
                { forUpdate: true },
            );
            const status = await purchaseReceipt.status;
            const lineStatus = await (
                await purchaseReceipt.lines.takeOne(async line => (await line._sortValue) === 1)
            )?.status;
            assert.deepEqual({ status, lineStatus }, { status: 'draft', lineStatus: 'draft' });
            await purchaseReceipt.$.set({
                lines: [
                    { _action: 'update', _sortValue: 1, priceOrigin: 'manual', orderCost: 525 },
                    {
                        _action: 'create',
                        _id: -1,
                        item: '#Muesli',
                        lineInvoiceStatus: 'notInvoiced',
                        lineReturnStatus: 'notReturned',
                        status: 'draft',
                        grossPrice: 1234,
                        priceOrigin: 'manual',
                        unit: '#GRAM',
                        quantity: 4532,
                        stockUnit: '#GRAM',
                    },
                ],
            });
            await purchaseReceipt.$.save();
        }));
    it('Purchase receipt - Update line on PR2 - Status:  inProgress', () =>
        Test.withContext(async context => {
            const purchaseReceipt = await context.read(
                xtremPurchasing.nodes.PurchaseReceipt,
                { number: 'PR2' },
                { forUpdate: true },
            );
            const status = await purchaseReceipt.status;
            const lineStatus = await (
                await purchaseReceipt.lines.takeOne(async line => (await line._sortValue) === 1)
            )?.status;
            assert.deepEqual({ status, lineStatus }, { status: 'inProgress', lineStatus: 'inProgress' });
            await assert.isRejected(
                purchaseReceipt.$.set({
                    lines: [{ _action: 'update', _sortValue: 1, priceOrigin: 'manual', orderCost: 525 }],
                }),
                'PurchaseReceiptLine.priceOrigin: cannot set value on frozen property',
            );
        }));
    it('Purchase receipt - new line on PR2 - isFrozen: true', () =>
        Test.withContext(async context => {
            const purchaseReceipt = await context.read(
                xtremPurchasing.nodes.PurchaseReceipt,
                { number: 'PR2' },
                { forUpdate: true },
            );
            const status = await purchaseReceipt.status;
            const lineStatus = await (
                await purchaseReceipt.lines.takeOne(async line => (await line._sortValue) === 2)
            )?.status;
            assert.deepEqual({ status, lineStatus }, { status: 'inProgress', lineStatus: 'closed' });
            await assert.isRejected(
                purchaseReceipt.$.set({
                    lines: [
                        { _action: 'update', _sortValue: 2, priceOrigin: 'manual', orderCost: 525 },
                        { _action: 'create', item: '#Muesli', grossPrice: 1234, quantity: 4532 },
                    ],
                }),
                'PurchaseReceiptLine.priceOrigin: cannot set value on frozen property',
            );
        }));
});
