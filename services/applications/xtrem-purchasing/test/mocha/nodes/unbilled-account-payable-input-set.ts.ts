import type { Collection, Context, NodeCreateData } from '@sage/xtrem-core';
import { Test, date } from '@sage/xtrem-core';
import * as xtremSystem from '@sage/xtrem-system';
import * as xtremTax from '@sage/xtrem-tax';
import { assert } from 'chai';
import * as sinon from 'sinon';
import * as xtremPurchasing from '../../../lib';
import * as TestHelper from '../../fixtures/lib/functions';

interface Document {
    _id: number;
    number: string;
    info: string;
    type: string;
}

function getPurchaseOrderData(currentDate: date): NodeCreateData<xtremPurchasing.nodes.PurchaseOrder> {
    return {
        site: '#ETS1-S01',
        businessRelation: '#US017',
        currency: '#EUR',
        orderDate: currentDate,
        fxRateDate: currentDate,
        approvalStatus: 'pendingApproval',
        lines: [
            {
                item: '#StockItem01',
                stockSite: '#ETS1-S01',
                quantity: 150,
                unit: '#GRAM',
                grossPrice: 100,
            },
            {
                item: '#StockItem02',
                stockSite: '#ETS1-S01',
                quantity: 100,
                unit: '#GRAM',
                grossPrice: 100,
            },
        ],
    };
}

async function invoicePurchaseReceipt(
    context: Context,
    purchaseReceipt: xtremPurchasing.nodes.PurchaseReceipt,
): Promise<xtremPurchasing.nodes.PurchaseInvoice> {
    const purchaseInvoicesCreated = await xtremPurchasing.nodes.PurchaseReceipt.createPurchaseInvoice(
        context,
        purchaseReceipt,
    );

    assert.equal(purchaseInvoicesCreated.length, 1);

    const [purchaseInvoiceCreated] = purchaseInvoicesCreated;

    const purchaseInvoice = await context.read(
        xtremPurchasing.nodes.PurchaseInvoice,
        { _id: purchaseInvoiceCreated._id },
        { forUpdate: true },
    );

    await purchaseInvoice.$.set({
        status: 'posted',
        totalAmountExcludingTax: await purchaseInvoice.calculatedTotalAmountExcludingTax,
    });
    await purchaseInvoice.$.save();

    return context.read(xtremPurchasing.nodes.PurchaseInvoice, { _id: purchaseInvoice._id });
}

async function createPurchaseReceipt(context: Context): Promise<xtremPurchasing.nodes.PurchaseReceipt> {
    const purchaseOrder = await context.create(xtremPurchasing.nodes.PurchaseOrder, getPurchaseOrderData(date.today()));
    await purchaseOrder.$.save({ flushDeferredActions: true });

    // Approve the purchase order
    await xtremPurchasing.nodes.PurchaseOrder.approve(context, purchaseOrder, true);

    // Create receipt from purchase order
    const purchaseReceipts = await xtremPurchasing.nodes.PurchaseOrder.createPurchaseReceipt(context, purchaseOrder);
    await context.flushDeferredActions();

    assert.equal(purchaseReceipts.length, 1);
    const [purchaseReceiptCreated] = purchaseReceipts;
    assert.equal(await purchaseReceiptCreated.lines.length, 2);

    const purchaseReceipt = await context.read(
        xtremPurchasing.nodes.PurchaseReceipt,
        { _id: purchaseReceiptCreated._id },
        { forUpdate: true },
    );

    // Update the stock details
    await purchaseReceipt.$.set({
        lines: [
            {
                _id: (await purchaseReceipt.lines.elementAt(0))._id,
                _action: 'update',
                stockDetails: [
                    {
                        effectiveDate: date.today(),
                        site: '#ETS1-S01',
                        item: '#StockItem01',
                        location: 'PTLOC01|ETS1-S01|Central',
                        stockUnit: '#GRAM',
                        status: '#A',
                        owner: '#ETS1-S01',
                        quantityInStockUnit: 150,
                    },
                ],
                stockTransactionStatus: 'completed',
            },
            {
                _id: (await purchaseReceipt.lines.elementAt(1))._id,
                _action: 'update',
                stockDetails: [
                    {
                        effectiveDate: date.today(),
                        site: '#ETS1-S01',
                        item: '#StockItem01',
                        location: 'PTLOC01|ETS1-S01|Central',
                        stockUnit: '#GRAM',
                        status: '#A',
                        owner: '#ETS1-S01',
                        quantityInStockUnit: 100,
                    },
                ],
                stockTransactionStatus: 'completed',
            },
        ],
    });
    await purchaseReceipt.$.save({ flushDeferredActions: true });

    // Post the purchase receipt
    await xtremPurchasing.nodes.PurchaseReceipt.post(context, purchaseReceipt);

    return context.read(xtremPurchasing.nodes.PurchaseReceipt, { _id: purchaseReceiptCreated._id });
}

async function createPurchaseReturn(
    context: Context,
    purchaseReceipt: xtremPurchasing.nodes.PurchaseReceipt,
): Promise<xtremPurchasing.nodes.PurchaseReturn | null> {
    const [returnDoc] = await xtremPurchasing.nodes.PurchaseReceipt.createPurchaseReturns(context, purchaseReceipt);

    if (returnDoc) {
        const purchaseReturn = await context.read(
            xtremPurchasing.nodes.PurchaseReturn,
            { _id: returnDoc._id },
            { forUpdate: true },
        );

        await purchaseReturn.lines.forEach(purchaseReturnLine =>
            purchaseReturn.$.set({
                lines: [{ _id: purchaseReturnLine._id, shippedStatus: 'shipped', _action: 'update', quantity: 50 }],
            }),
        );
        await purchaseReturn.$.save({ flushDeferredActions: true });

        return purchaseReturn;
    }
    return null;
}

async function getPurchaseCreditMemoData(params: {
    orderDate: date;
    returnLine: Collection<xtremPurchasing.nodes.PurchaseReturnLine>;
}): Promise<NodeCreateData<xtremPurchasing.nodes.PurchaseCreditMemo>> {
    return {
        payToSupplier: '#US017',
        lines: [
            {
                _action: 'create',
                charge: 0,
                discount: 0,
                grossPrice: 100,
                item: '#StockItem01',
                amountExcludingTax: 15000,
                amountExcludingTaxInCompanyCurrency: 15000,
                amountIncludingTax: 15000,
                amountIncludingTaxInCompanyCurrency: 15000,
                netPrice: 100,
                priceOrigin: 'manual',
                purchaseReturnLine: { purchaseReturnLine: (await params.returnLine.elementAt(0))._id },
                unit: '#GRAM',
                unitToStockUnitConversionFactor: 1,
                quantity: 150,
                recipientSite: '#ETS1-S01',
                stockUnit: '#GRAM',
                storedAttributes: null,
                storedDimensions: null,
                taxAmount: 0,
                taxAmountAdjusted: 0,
            },
            {
                _action: 'create',
                charge: 0,
                discount: 0,
                grossPrice: 100,
                item: '#StockItem02',
                amountExcludingTax: 10000,
                amountExcludingTaxInCompanyCurrency: 10000,
                amountIncludingTax: 10000,
                amountIncludingTaxInCompanyCurrency: 10000,
                netPrice: 100,
                priceOrigin: 'manual',
                purchaseReturnLine: { purchaseReturnLine: (await params.returnLine.elementAt(1))._id },
                unit: '#GRAM',
                unitToStockUnitConversionFactor: 1,
                quantity: 100,
                recipientSite: '#ETS1-S01',
                stockUnit: '#GRAM',
                storedAttributes: null,
                storedDimensions: null,
                taxAmount: 0,
                taxAmountAdjusted: 0,
            },
        ],
        reason: '#R1',
        paymentTerm: '#TEST_NET_30_SUPPLIER',
        currency: '#EUR',
        billBySupplier: '#US017',
        site: '#ETS1-S01',
        fxRateDate: params.orderDate,
        companyFxRate: 1,
        companyFxRateDivisor: 1,
        creditMemoDate: params.orderDate,
        dueDate: params.orderDate,
        status: 'draft',
        taxCalculationStatus: 'notDone',
        supplierDocumentDate: params.orderDate,
        totalAmountExcludingTax: 25000,
        totalAmountIncludingTax: 25000,
        totalTaxAmount: 0,
        totalTaxAmountAdjusted: 0,
    };
}

/** return created documents */
async function createPurchaseReceiptsAndReturns(context: Context): Promise<Document[]> {
    const orderDate: date = date.today();
    const documents: Document[] = [];

    // First purchase receipt, received, partially returned, invoiced and credited
    const purchaseReceipt1 = await createPurchaseReceipt(context);
    await invoicePurchaseReceipt(context, purchaseReceipt1);
    const purchaseReturn = await createPurchaseReturn(context, purchaseReceipt1);
    if (purchaseReturn) {
        const purchaseCreditMemoData = await getPurchaseCreditMemoData({
            orderDate,
            returnLine: purchaseReturn.lines,
        });
        const purchaseCreditNote4 = await context.create(
            xtremPurchasing.nodes.PurchaseCreditMemo,
            purchaseCreditMemoData,
        );
        await purchaseCreditNote4.$.save({ flushDeferredActions: true });
        await xtremPurchasing.nodes.PurchaseCreditMemo.post(context, purchaseCreditNote4);
    }
    // Second purchase receipt, received but not invoiced
    await createPurchaseReceipt(context);
    return documents;
}

describe('Unbilled accounts payable inquiry', () => {
    beforeEach(async () => {
        // set company S1 to the intrastat-declaration test context
        await Test.withCommittedContext(async context => {
            const companyS1 = await context.read(xtremSystem.nodes.Company, { id: 'S1' }, { forUpdate: true });
            const lines = companyS1.dimensionTypes;
            await lines.forEach(async line => {
                await line.$.delete();
            });
        });
    });
    afterEach(() => {
        sinon.restore();
    });

    it('asyncMutation unbilledAccountPayableInquiry with purchase receipts that are in draft status andl also with purchase receipts with invoices posted', () =>
        Test.withContext(
            async context => {
                // Delete TEST tax solution
                const taxSolution = await context.read(
                    xtremTax.nodes.TaxSolution,
                    { id: 'FRSOL' },
                    { forUpdate: true },
                );
                if (await taxSolution.lines.at(1)) {
                    await taxSolution.$.set({
                        lines: [{ _action: 'delete', _id: (await taxSolution.lines.at(1))?._id }],
                    });
                    await taxSolution.$.save();
                }

                TestHelper.PurchaseReceipt.activateFakeReceiptLineUpdate(context);
                // Create data for the inquiry
                await createPurchaseReceiptsAndReturns(context);

                // Create inquiry record
                let inputSet: xtremPurchasing.nodes.UnbilledAccountPayableInputSet = await context.create(
                    xtremPurchasing.nodes.UnbilledAccountPayableInputSet,
                    {
                        company: '#S1',
                        asOfDate: date.today(),
                        fromSupplier: '#US017',
                        toSupplier: '#US017',
                    },
                );
                await inputSet.$.save();

                const user = await inputSet.user;
                await xtremPurchasing.nodes.UnbilledAccountPayableInputSet.unbilledAccountPayableInquiry(
                    context,
                    user._id.toString(),
                );
                inputSet = await context.read(xtremPurchasing.nodes.UnbilledAccountPayableInputSet, { user });

                assert.deepEqual(await (await inputSet.user).email, await user.email);
                assert.deepEqual(await inputSet.lines.length, 4);

                // Verify quantities
                const line1 = await inputSet.lines.elementAt(0);
                assert.deepEqual(await line1.quantity, 150.0);
                assert.deepEqual(await line1.invoicedQuantity, 150.0);
                assert.deepEqual(await line1.creditedQuantity, 150.0);
                assert.deepEqual(await line1.returnedQuantity, 50.0);

                const line2 = await inputSet.lines.elementAt(1);
                assert.deepEqual(await line2.quantity, 100.0);
                assert.deepEqual(await line2.invoicedQuantity, 100.0);
                assert.deepEqual(await line2.creditedQuantity, 100.0);
                assert.deepEqual(await line2.returnedQuantity, 50.0);

                // Verify a few amounts
                assert.equal(await line1.invoiceReceivableAmount, 10000);
                assert.equal(await line1.invoiceReceivableAmountInCompanyCurrency, 10000);
                assert.equal(await line1.invoiceReceivableAmountInCompanyCurrencyAtAsOfDate, 10000);

                // Verify status
                const purchaseReceipt = await context.read(
                    xtremPurchasing.nodes.PurchaseReceipt,
                    { _id: await (await inputSet.lines.elementAt(0)).receiptNumber },
                    { forUpdate: true },
                );
                assert.equal(await purchaseReceipt.status, 'closed');
                assert.equal(await purchaseReceipt.invoiceStatus, 'invoiced');
            },
            { today: '2024-09-04' },
        ));
});
