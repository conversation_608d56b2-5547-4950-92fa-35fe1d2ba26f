import type { Context, NodeCreateData } from '@sage/xtrem-core';
import { Test, ValidationSeverity, date } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremStockData from '@sage/xtrem-stock-data';
import * as xtremSystem from '@sage/xtrem-system';
import { assert } from 'chai';
import * as xtremPurchasing from '../../../lib';
import { compareData } from '../fixtures/common';
import { getUser } from '../fixtures/mailer';

const { withSequenceNumberContext } = xtremMasterData.functions.testLib;

describe('Purchase order node - create/allocate-sequence/approve/reject', () => {
    it('Create purchase order', () =>
        Test.withContext(
            async context => {
                const itemSite = await context.read(xtremMasterData.nodes.ItemSite, {
                    item: '#Muesli',
                    site: '#US001',
                });
                const prevOnOrderQty = await itemSite.expectedQuantity;
                const newPurchaseOrder = await context.create(xtremPurchasing.nodes.PurchaseOrder, {
                    site: '#US001',
                    businessRelation: '#LECLERC',
                    currency: '#USD',
                    fxRateDate: date.make(2020, 8, 10),
                    orderDate: date.make(2020, 8, 10),
                    lines: [
                        {
                            item: '#Muesli',
                            quantity: 10,
                            unit: '#GRAM',
                            grossPrice: 30,
                            expectedReceiptDate: date.today(),
                        },
                    ],
                });
                await newPurchaseOrder.$.save();

                assert.deepEqual(newPurchaseOrder.$.context.diagnoses, []);
                assert.equal(
                    (await itemSite.expectedQuantity).toString(),
                    (
                        prevOnOrderQty + (await (await newPurchaseOrder.lines.elementAt(0)).quantityInStockUnit)
                    ).toString(),
                );

                assert.deepEqual(await (await newPurchaseOrder.lines.elementAt(0)).storedDimensions, {
                    dimensionType01: '300',
                    dimensionType02: 'CHANNELVALUE1',
                });

                assert.equal(
                    JSON.stringify(await (await newPurchaseOrder.lines.elementAt(0)).storedAttributes),
                    JSON.stringify({ project: 'AttPROJ', task: 'Task1' }),
                );
            },
            { today: '2020-08-10' },
        ));
    it('Approve purchase order - entry parameters testing', () =>
        Test.withContext(async context => {
            const po5 = await context.read(xtremPurchasing.nodes.PurchaseOrder, { number: 'PO5' }, { forUpdate: true });
            const po9 = await context.read(xtremPurchasing.nodes.PurchaseOrder, { number: 'PO9' }, { forUpdate: true });

            // PO5 - ready for processing (only draft accepted)
            await assert.isRejected(xtremPurchasing.nodes.PurchaseOrder.approve(context, po5, true));
            await assert.isRejected(xtremPurchasing.nodes.PurchaseOrder.approve(context, po5, false));

            // PO9 - suggested (only draft accepted)
            await assert.isRejected(xtremPurchasing.nodes.PurchaseOrder.approve(context, po9, true));
            await assert.isRejected(xtremPurchasing.nodes.PurchaseOrder.approve(context, po9, false));
        }));
    it('Approve purchase order PO2', () =>
        Test.withContext(async context => {
            const po2 = await context.read(xtremPurchasing.nodes.PurchaseOrder, { number: 'PO2' }, { forUpdate: true });
            const approveResult = await xtremPurchasing.nodes.PurchaseOrder.approve(context, po2, true);
            assert.isTrue(approveResult);

            const purchaseOrder = await context.read(xtremPurchasing.nodes.PurchaseOrder, { number: 'PO2' });

            assert.equal(await purchaseOrder.status, 'pending');
            assert.equal(await purchaseOrder.approvalStatus, 'approved');

            const lines = await purchaseOrder.lines.toArray();
            // Line 0
            assert.equal(await lines[0].status, 'pending');
            // Line 1
            assert.equal(await lines[1].status, 'pending');
        }));
    it('Reject purchase order PO2', () =>
        Test.withContext(async context => {
            const PO2 = await context.read(xtremPurchasing.nodes.PurchaseOrder, { _id: '#PO2' }, { forUpdate: true });
            const approveResult = await xtremPurchasing.nodes.PurchaseOrder.approve(context, PO2, false);
            assert.isTrue(approveResult);

            const purchaseOrder = await context.read(xtremPurchasing.nodes.PurchaseOrder, { _id: PO2._id });

            assert.equal(await purchaseOrder.status, 'closed');
            assert.equal(await purchaseOrder.approvalStatus, 'rejected');

            const lines = await purchaseOrder.lines.toArray();
            // Line 0
            assert.equal(await lines[0].status, 'closed');
            // Line 1
            assert.equal(await lines[1].status, 'closed');
        }));
    it('Create purchase order - PO default buyer = Supplier default buyer', () =>
        Test.withContext(
            async context => {
                const supplier = await context.read(
                    xtremMasterData.nodes.Supplier,
                    { _id: '#500' },
                    { forUpdate: true },
                );

                await supplier.$.set({ defaultBuyer: '<EMAIL>', deliveryMode: 'TEST_AIR_JEREZ' });
                await supplier.$.save();
                assert.deepEqual(supplier.$.context.diagnoses, []);
                assert.equal('<EMAIL>', await (await supplier.defaultBuyer)?.email);
                assert.equal('TEST_AIR_JEREZ', await (await supplier.deliveryMode)?.id);

                const newPurchaseOrder = await context.create(xtremPurchasing.nodes.PurchaseOrder, {
                    site: '#US003',
                    businessRelation: '#500',
                    currency: '#USD',
                    fxRateDate: date.make(2023, 1, 17),
                    orderDate: date.make(2023, 1, 17),
                    lines: [
                        {
                            item: '#Chemical A',
                            quantity: 10,
                            grossPrice: 30,
                        },
                    ],
                });
                await newPurchaseOrder.$.save();
                assert.deepEqual(newPurchaseOrder.$.context.diagnoses, []);
                assert.equal('<EMAIL>', await (await newPurchaseOrder.defaultBuyer).email);
                assert.equal('TEST_AIR_JEREZ', await (await newPurchaseOrder.deliveryMode)?.id);
            },
            { today: '2023-01-17' },
        ));
    it('Create purchase order - PO default buyer = User that is creating the PO', () =>
        Test.withContext(
            async context => {
                const newPurchaseOrder = await context.create(xtremPurchasing.nodes.PurchaseOrder, {
                    site: '#US003',
                    businessRelation: '#500',
                    currency: '#USD',
                    fxRateDate: date.make(2023, 1, 17),
                    orderDate: date.make(2023, 1, 17),
                    lines: [
                        {
                            item: '#Chemical A',
                            quantity: 10,
                            grossPrice: 30,
                        },
                    ],
                });
                await newPurchaseOrder.$.save();
                assert.deepEqual(newPurchaseOrder.$.context.diagnoses, []);
                assert.equal('<EMAIL>', await (await newPurchaseOrder.defaultBuyer).email);
                assert.equal(undefined, await (await newPurchaseOrder.deliveryMode)?.id);
            },
            { today: '2023-01-17' },
        ));

    it('Create purchase order - refuse PO with landed cost item', () =>
        Test.withContext(
            async context => {
                const newPurchaseOrder = await context.create(xtremPurchasing.nodes.PurchaseOrder, {
                    site: '#US003',
                    businessRelation: '#500',
                    currency: '#USD',
                    fxRateDate: date.make(2023, 1, 17),
                    orderDate: date.make(2023, 1, 17),
                    lines: [
                        {
                            item: '#LandedCost001',
                            quantity: 10,
                            grossPrice: 30,
                        },
                    ],
                });
                await assert.isRejected(newPurchaseOrder.$.save());
                assert.deepEqual(newPurchaseOrder.$.context.diagnoses, [
                    {
                        severity: ValidationSeverity.error,
                        path: ['lines', '-1000000002', 'item'],
                        message: 'The record is not valid. You need to select a different record.',
                    },
                    {
                        message: 'A landed cost item cannot be added to the document: PurchaseOrder.',
                        path: ['lines', '-1000000002', 'item'],
                        severity: 3,
                    },
                ]);
            },
            { today: '2023-01-17' },
        ));

    it('Create purchase order with purchasing site without inventory - create 2 lines with 2 different receiving sites.', () =>
        Test.withContext(
            async context => {
                const newPurchaseOrder = await context.create(xtremPurchasing.nodes.PurchaseOrder, {
                    site: '#US023',
                    businessRelation: '#US017',
                    currency: '#USD',
                    fxRateDate: date.make(2020, 8, 10),
                    orderDate: date.make(2020, 8, 10),
                    lines: [
                        {
                            item: '#Aqua',
                            stockSite: '#US007',
                            quantity: 1,
                            unit: '#LITER',
                            grossPrice: 2,
                            expectedReceiptDate: date.today(),
                        },
                        {
                            item: '#Aqua',
                            stockSite: '#US005',
                            quantity: 1,
                            unit: '#LITER',
                            grossPrice: 2,
                            expectedReceiptDate: date.today(),
                        },
                    ],
                });
                await newPurchaseOrder.$.save();
                assert.deepEqual(newPurchaseOrder.$.context.diagnoses, []);
            },
            { today: '2020-08-10' },
        ));

    it('Ensure that a Purchase Order with grossPrice 0 can be saved', () =>
        Test.withContext(async context => {
            const purchaseOrder = await context.create(xtremPurchasing.nodes.PurchaseOrder, {
                site: '#US023',
                businessRelation: '#US017',
                lines: [
                    {
                        item: '#Aqua',
                        stockSite: '#US007',
                        quantity: 5,
                        grossPrice: 0,
                    },
                ],
            });
            await purchaseOrder.$.save();
            assert.deepEqual(purchaseOrder.$.context.diagnoses, []);
        }));
});
describe('Purchase order node - create receipt from an order', () => {
    const testDate = '2020-08-10';

    // TODO: I'm not a UNIT test scenario so please refine my scope as I go in too much direction for now..
    it('Create purchase receipt for purchase order - entry parameters testing', () =>
        Test.withContext(
            async context => {
                const document = await context.read(
                    xtremPurchasing.nodes.PurchaseOrder,
                    {
                        number: 'PO9',
                    },
                    { forUpdate: true },
                );
                const purchaseReceipts = await xtremPurchasing.nodes.PurchaseOrder.createPurchaseReceipt(
                    context,
                    document,
                );
                assert.isEmpty(purchaseReceipts);
                assert.isEmpty(context.diagnoses);
            },
            { today: testDate },
        ));

    it('Create receipt for purchase order PO3 - 2 po lines on 2 pr line', () =>
        Test.withContext(
            async context => {
                const document = await context.read(
                    xtremPurchasing.nodes.PurchaseOrder,
                    {
                        number: 'PO3',
                    },
                    { forUpdate: true },
                );
                const receipts = await xtremPurchasing.nodes.PurchaseOrder.createPurchaseReceipt(context, document);
                await context.flushDeferredActions();
                assert.equal(receipts.length, 1);
                const receiptNumber = await receipts.at(0)?.number;
                assert.equal(receiptNumber, 'PR200001');
                const pr = await context.read(xtremPurchasing.nodes.PurchaseReceipt, { number: receiptNumber });
                /**
                 * Purchase receipt const year month seq
                 */
                assert.equal(await pr.number, 'PR200001');
                assert.equal(await pr.lines.length, 2);
                assert.equal(await (await pr.supplierAddress)?.name, 'COSMETIC');
                assert.equal(await (await pr.receivingAddress)?.name, 'Main address');
            },
            { today: testDate },
        ));
    it('Create receipt for purchase order PO4 - 2 po lines on 2 pr lines (different purchase unit)', () =>
        Test.withContext(
            async context => {
                const document = await context.read(
                    xtremPurchasing.nodes.PurchaseOrder,
                    {
                        number: 'PO4',
                    },
                    { forUpdate: true },
                );
                const receipts = await xtremPurchasing.nodes.PurchaseOrder.createPurchaseReceipt(context, document);
                await context.flushDeferredActions();
                assert.equal(receipts.length, 1);
                const receiptNumber = await receipts.at(0)?.number;
                assert.equal(receiptNumber, 'PR200001');
                const pr = await context.read(xtremPurchasing.nodes.PurchaseReceipt, {
                    number: receiptNumber,
                });
                assert.equal(await pr.number, 'PR200001');
                assert.equal(await pr.lines.length, 2);
            },
            { today: testDate },
        ));

    it('Create receipt for purchase order PO5 - 1 po line on 1 pr line', () =>
        Test.withContext(
            async context => {
                const document = await context.read(
                    xtremPurchasing.nodes.PurchaseOrder,
                    {
                        number: 'PO5',
                    },
                    { forUpdate: true },
                );
                const receipts = await xtremPurchasing.nodes.PurchaseOrder.createPurchaseReceipt(context, document);
                await context.flushDeferredActions();
                assert.equal(receipts.length, 1);
                const receiptNumber = await receipts.at(0)?.number;
                assert.equal(receiptNumber, 'PR200001');

                const pr = await context.read(xtremPurchasing.nodes.PurchaseReceipt, { number: receiptNumber });
                assert.equal(await pr.number, 'PR200001');
                assert.equal(await pr.lines.length, 1);
            },
            { today: testDate },
        ));
    it('Create receipt for purchase order PO25 - 2 po lines same stockSite on 1 pr', () =>
        Test.withContext(
            async context => {
                const document = await context.read(
                    xtremPurchasing.nodes.PurchaseOrder,
                    {
                        number: 'PO25',
                    },
                    { forUpdate: true },
                );
                const receipts = await xtremPurchasing.nodes.PurchaseOrder.createPurchaseReceipt(context, document);
                await context.flushDeferredActions();
                assert.equal(receipts.length, 1);
                const receiptNumber = await receipts.at(0)?.number;
                assert.equal(receiptNumber, 'PR220001');

                const pr = await context.read(xtremPurchasing.nodes.PurchaseReceipt, { number: receiptNumber });

                assert.equal(await pr.lines.length, 2);
                assert.equal(await (await pr.supplierAddress)?.name, 'Store');
                assert.equal(await (await pr.receivingAddress)?.name, 'US001');
            },
            { today: '2022-01-25' },
        ));
    it('Create receipt for purchase order PO26 - 2 po lines different stockSite on 2 pr', () =>
        Test.withContext(
            async context => {
                const document = await context.read(
                    xtremPurchasing.nodes.PurchaseOrder,
                    {
                        number: 'PO26',
                    },
                    { forUpdate: true },
                );
                const receipts = await xtremPurchasing.nodes.PurchaseOrder.createPurchaseReceipt(context, document);
                await context.flushDeferredActions();
                assert.equal(receipts.length, 2);
                const receiptNumber = await receipts.at(0)?.number;
                assert.equal(receiptNumber, 'PR220001');

                let pr = await context.read(xtremPurchasing.nodes.PurchaseReceipt, {
                    number: receiptNumber,
                });
                assert.equal(await pr.number, 'PR220001');
                assert.equal(await pr.lines.length, 1);
                assert.equal(await (await pr.supplierAddress)?.name, 'Store');
                assert.equal(await (await pr.receivingAddress)?.name, 'US001');

                pr = await context.read(xtremPurchasing.nodes.PurchaseReceipt, {
                    number: await receipts.at(1)?.number,
                });
                assert.equal(await pr.number, 'PR220002');
                assert.equal(await pr.lines.length, 1);
                assert.equal(await (await pr.supplierAddress)?.name, 'Store');
                assert.equal(await (await pr.receivingAddress)?.name, 'US001 Warehouse');
            },
            { today: '2022-01-25' },
        ));
    it('Create purchase order and then fully receive it in a row', () =>
        Test.withContext(
            async context => {
                const newPurchaseOrder = await context.create(xtremPurchasing.nodes.PurchaseOrder, {
                    site: '#US001',
                    businessRelation: '#LECLERC',
                    currency: '#USD',
                    orderDate: date.make(2020, 8, 10),
                    fxRateDate: date.make(2020, 8, 10),
                    approvalStatus: 'pendingApproval',
                    lines: [
                        {
                            item: '#Muesli',
                            quantity: 10,
                            unit: '#KILOGRAM',
                            grossPrice: 30,
                            expectedReceiptDate: date.today(),
                        },
                    ],
                });
                await newPurchaseOrder.$.save();
            },
            { today: testDate },
        ));
    it('Create purchase order and then partially receive it in a row', () =>
        Test.withContext(
            async context => {
                let newPurchaseOrder = await context.create(xtremPurchasing.nodes.PurchaseOrder, {
                    site: '#US001',
                    businessRelation: '#LECLERC',
                    currency: '#USD',
                    orderDate: date.make(2020, 8, 10),
                    fxRateDate: date.make(2020, 8, 10),
                    lines: [
                        {
                            item: '#Muesli',
                            quantity: 10,
                            unit: '#KILOGRAM',
                            grossPrice: 30,
                            expectedReceiptDate: date.today(),
                        },
                    ],
                });
                await newPurchaseOrder.$.save();

                const purchaseReceipt = await context.create(xtremPurchasing.nodes.PurchaseReceipt, {
                    site: '#US001',
                    businessRelation: '#LECLERC',
                    currency: '#USD',
                    date: date.make(2020, 8, 10),
                    lines: [
                        {
                            item: '#Muesli',
                            quantity: 6,
                            unit: '#KILOGRAM',
                            grossPrice: 30,
                            purchaseOrderLine: {
                                purchaseOrderLine: (await newPurchaseOrder.lines.elementAt(0))._id,
                            },
                        },
                    ],
                });
                await purchaseReceipt.$.save({ flushDeferredActions: true });

                // Post the purchase receipt
                await purchaseReceipt.$.set({
                    status: 'pending',
                    lines: [
                        {
                            _action: 'update',
                            _id: (await purchaseReceipt.lines.elementAt(0))._id,
                            status: 'pending',
                        },
                    ],
                });
                await purchaseReceipt.$.save();

                assert.equal(await purchaseReceipt.lines.length, 1);
                const purchaseReceiptLine = await purchaseReceipt.lines.elementAt(0);
                const purchaseOrderLine = await purchaseReceiptLine.purchaseOrderLine;
                assert.isNotNull(await purchaseReceiptLine.purchaseOrderLine);
                assert.equal((await purchaseOrderLine?.receivedQuantity)?.toString(), '6');
                assert.equal((await purchaseOrderLine?.receivedQuantityInStockUnit)?.toString(), '6000');

                newPurchaseOrder = await context.read(xtremPurchasing.nodes.PurchaseOrder, {
                    _id: newPurchaseOrder._id,
                });
                assert.equal((await (await newPurchaseOrder.lines.elementAt(0)).receivedQuantity).toString(), '6');
                assert.equal(await (await newPurchaseOrder.lines.elementAt(0)).lineReceiptStatus, 'partiallyReceived');
                assert.equal('partiallyReceived', await newPurchaseOrder.receiptStatus);
                assert.equal((await (await newPurchaseOrder.lines.elementAt(0)).invoicedQuantity).toString(), '0');
                assert.equal(
                    (await (await newPurchaseOrder.lines.elementAt(0)).remainingQuantityToInvoice).toString(),
                    '10',
                );
                assert.equal(
                    (await (await newPurchaseOrder.lines.elementAt(0)).remainingQuantityToInvoiceOnOrder).toString(),
                    '4',
                );
            },
            { today: testDate },
        ));
    it('Create purchase order and then partially and fully receive it in a row', () =>
        Test.withContext(
            async context => {
                let newPurchaseOrder = await context.create(xtremPurchasing.nodes.PurchaseOrder, {
                    site: '#US001',
                    businessRelation: '#LECLERC',
                    currency: '#USD',
                    orderDate: date.make(2020, 8, 10),
                    fxRateDate: date.make(2020, 8, 10),
                    lines: [
                        {
                            item: '#Muesli',
                            quantity: 10,
                            unit: '#KILOGRAM',
                            grossPrice: 30,
                            expectedReceiptDate: date.today(),
                        },
                    ],
                });
                await newPurchaseOrder.$.save();
                const newPurchaseOrderId = newPurchaseOrder._id;
                assert.deepEqual(newPurchaseOrder.$.context.diagnoses, []);

                let purchaseReceipt = await context.create(xtremPurchasing.nodes.PurchaseReceipt, {
                    site: '#US001',
                    businessRelation: '#LECLERC',
                    currency: '#USD',
                    date: date.make(2020, 8, 10),
                    lines: [
                        {
                            item: '#Muesli',
                            quantity: 7,
                            unit: '#KILOGRAM',
                            grossPrice: 30,
                            purchaseOrderLine: {
                                purchaseOrderLine: (await newPurchaseOrder.lines.elementAt(0))._id,
                            },
                        },
                    ],
                });
                await purchaseReceipt.$.trySave();
                assert.deepEqual(purchaseReceipt.$.context.diagnoses, []);
                const purchaseReceiptId = purchaseReceipt._id;
                const firstPurchaseLineId = (await purchaseReceipt.lines.elementAt(0))._id;
                // rollbackCache function will flush deferred actions and saves
                await Test.rollbackCache(context);

                purchaseReceipt = await context.read(
                    xtremPurchasing.nodes.PurchaseReceipt,
                    { _id: purchaseReceiptId },
                    { forUpdate: true },
                );

                // Post the purchase receipt
                await purchaseReceipt.$.set({
                    status: 'pending',
                    lines: [
                        {
                            _action: 'update',
                            _id: firstPurchaseLineId,
                            status: 'pending',
                        },
                    ],
                });
                await purchaseReceipt.$.save();
                await Test.rollbackCache(context);

                purchaseReceipt = await context.read(
                    xtremPurchasing.nodes.PurchaseReceipt,
                    { _id: purchaseReceiptId },
                    { forUpdate: true },
                );

                assert.equal(await purchaseReceipt.lines.length, 1);
                const purchaseReceiptLine = await purchaseReceipt.lines.elementAt(0);
                const purchaseOrderLine = await purchaseReceiptLine.purchaseOrderLine;
                assert.isNotNull(await purchaseReceiptLine.purchaseOrderLine);
                assert.equal((await purchaseOrderLine?.receivedQuantity)?.toString(), '7');
                assert.equal(
                    (await (await purchaseReceiptLine.purchaseOrderLine)?.receivedQuantityInStockUnit)?.toString(),
                    '7000',
                );

                newPurchaseOrder = await context.read(xtremPurchasing.nodes.PurchaseOrder, {
                    _id: newPurchaseOrderId,
                });
                assert.equal(await (await newPurchaseOrder.lines.elementAt(0)).lineReceiptStatus, 'partiallyReceived');
                assert.equal(await newPurchaseOrder.receiptStatus, 'partiallyReceived');

                newPurchaseOrder = await context.read(xtremPurchasing.nodes.PurchaseOrder, {
                    _id: newPurchaseOrderId,
                });
                let purchaseReceipt2 = await context.create(xtremPurchasing.nodes.PurchaseReceipt, {
                    site: '#US001',
                    businessRelation: '#LECLERC',
                    currency: '#USD',
                    date: date.make(2020, 8, 10),
                    lines: [
                        {
                            item: '#Muesli',
                            quantity: 3,
                            unit: '#KILOGRAM',
                            grossPrice: 30,
                            purchaseOrderLine: {
                                purchaseOrderLine: (await newPurchaseOrder.lines.elementAt(0))._id,
                            },
                        },
                    ],
                });
                await purchaseReceipt2.$.save();
                assert.deepEqual(purchaseReceipt2.$.context.diagnoses, []);
                const secondPurchaseLineId = (await purchaseReceipt2.lines.elementAt(0))._id;
                const purchaseReceipt2Id = purchaseReceipt2._id;
                await Test.rollbackCache(context);

                purchaseReceipt2 = await context.read(
                    xtremPurchasing.nodes.PurchaseReceipt,
                    { _id: purchaseReceipt2Id },
                    { forUpdate: true },
                );

                // Post the purchase receipt
                await purchaseReceipt2.$.set({
                    status: 'pending',
                    lines: [
                        {
                            _action: 'update',
                            _id: secondPurchaseLineId,
                            status: 'pending',
                        },
                    ],
                });
                await purchaseReceipt2.$.save();
                assert.deepEqual(purchaseReceipt2.$.context.diagnoses, []);
                await Test.rollbackCache(context);

                purchaseReceipt2 = await context.read(
                    xtremPurchasing.nodes.PurchaseReceipt,
                    { _id: purchaseReceipt2Id },
                    { forUpdate: true },
                );

                assert.equal(await purchaseReceipt2.lines.length, 1);
                assert.isNotNull(await (await purchaseReceipt2.lines.elementAt(0)).purchaseOrderLine);
                assert.equal(
                    (
                        await (
                            await (
                                await purchaseReceipt2.lines.elementAt(0)
                            ).purchaseOrderLine
                        )?.receivedQuantity
                    )?.toString(),
                    '3',
                );
                assert.equal(
                    (
                        await (
                            await (
                                await purchaseReceipt2.lines.elementAt(0)
                            ).purchaseOrderLine
                        )?.receivedQuantityInStockUnit
                    )?.toString(),
                    '3000',
                );

                newPurchaseOrder = await context.read(xtremPurchasing.nodes.PurchaseOrder, {
                    _id: newPurchaseOrderId,
                });
                assert.equal(await (await newPurchaseOrder.lines.elementAt(0)).lineReceiptStatus, 'received');
                assert.equal(await newPurchaseOrder.receiptStatus, 'received');
            },
            { today: testDate },
        ));
    it('Create purchase order then receive more than the original PO, receipt status should be fully received', () =>
        Test.withContext(
            async context => {
                let newPurchaseOrder = await context.create(xtremPurchasing.nodes.PurchaseOrder, {
                    site: '#US001',
                    businessRelation: '#LECLERC',
                    currency: '#USD',
                    orderDate: date.make(2020, 8, 10),
                    fxRateDate: date.make(2020, 8, 10),
                    lines: [
                        {
                            item: '#Muesli',
                            quantity: 10,
                            unit: '#KILOGRAM',
                            grossPrice: 30,
                            expectedReceiptDate: date.today(),
                        },
                    ],
                });
                await newPurchaseOrder.$.save();
                const newPurchaseOrderId = newPurchaseOrder._id;
                assert.deepEqual(newPurchaseOrder.$.context.diagnoses, []);
                const firstPurchaseOrderLine = (await newPurchaseOrder.lines.elementAt(0))._id;
                // rollbackCache function will flush deferred actions and saves
                await Test.rollbackCache(context);

                let purchaseReceipt = await context.create(xtremPurchasing.nodes.PurchaseReceipt, {
                    site: '#US001',
                    businessRelation: '#LECLERC',
                    currency: '#USD',
                    date: date.make(2020, 8, 10),
                    lines: [
                        {
                            item: '#Muesli',
                            quantity: 15,
                            unit: '#KILOGRAM',
                            grossPrice: 30,
                            purchaseOrderLine: {
                                purchaseOrderLine: firstPurchaseOrderLine,
                            },
                        },
                    ],
                });
                await purchaseReceipt.$.save();
                await context.flushDeferredSaves();

                const purchaseOrderId = (
                    await (
                        await (
                            await (
                                await purchaseReceipt.lines.at(0)
                            )?.purchaseOrderLine
                        )?.purchaseOrderLine
                    )?.document
                )?._id;

                assert.deepEqual(purchaseReceipt.$.context.diagnoses, [
                    {
                        message: `While saving PurchaseOrder(${purchaseOrderId}): The total received quantity is greater then the order line quantity.`,
                        path: [],
                        severity: 2,
                    },
                ]);
                const purchaseReceiptId = purchaseReceipt._id;
                await Test.rollbackCache(context);

                purchaseReceipt = await context.read(
                    xtremPurchasing.nodes.PurchaseReceipt,
                    { _id: purchaseReceiptId },
                    { forUpdate: true },
                );

                // Post the purchase receipt
                await purchaseReceipt.$.set({
                    status: 'pending',
                    lines: [
                        {
                            _action: 'update',
                            _id: (await purchaseReceipt.lines.elementAt(0))._id,
                            status: 'pending',
                        },
                    ],
                });
                await purchaseReceipt.$.save();
                await context.flushDeferredSaves();

                assert.deepEqual(purchaseReceipt.$.context.diagnoses, [
                    {
                        message: `While saving PurchaseOrder(${purchaseOrderId}): The total received quantity is greater then the order line quantity.`,
                        path: [],
                        severity: 2,
                    },
                ]);
                await Test.rollbackCache(context);

                purchaseReceipt = await context.read(
                    xtremPurchasing.nodes.PurchaseReceipt,
                    { _id: purchaseReceiptId },
                    { forUpdate: true },
                );

                assert.equal(await purchaseReceipt.lines.length, 1);
                const purchaseReceiptLine = await purchaseReceipt.lines.elementAt(0);
                const purchaseOrderLine = await purchaseReceiptLine.purchaseOrderLine;
                assert.isNotNull(await purchaseReceiptLine.purchaseOrderLine);
                assert.equal((await purchaseOrderLine?.receivedQuantity)?.toString(), '15');
                assert.equal(
                    (await (await purchaseReceiptLine.purchaseOrderLine)?.receivedQuantityInStockUnit)?.toString(),
                    '15000',
                );

                newPurchaseOrder = await context.read(xtremPurchasing.nodes.PurchaseOrder, {
                    _id: newPurchaseOrderId,
                });
                assert.equal(await (await newPurchaseOrder.lines.elementAt(0)).lineReceiptStatus, 'received');
                assert.equal(await newPurchaseOrder.receiptStatus, 'received');
            },
            { today: testDate },
        ));
    it('Create receipt for purchase order PO3 - isTransferHeaderNote = true isTransferLineNote = true', () =>
        Test.withContext(
            async context => {
                const document = await context.read(
                    xtremPurchasing.nodes.PurchaseOrder,
                    { number: 'PO3' },
                    { forUpdate: true },
                );
                await document.$.set({ isTransferHeaderNote: true });
                await document.$.set({ isTransferLineNote: true });
                await document.$.save();

                const receipts = await xtremPurchasing.nodes.PurchaseOrder.createPurchaseReceipt(context, document);
                await context.flushDeferredActions();
                assert.equal(receipts.length, 1);
                const receiptNumber = await receipts.at(0)?.number;
                assert.equal(receiptNumber, 'PR200001');

                const rReceipt = await context.read(xtremPurchasing.nodes.PurchaseReceipt, { number: receiptNumber });

                assert.equal((await rReceipt.internalNote).toString(), (await document.internalNote).toString());
                assert.equal(await rReceipt.isTransferHeaderNote, await document.isTransferHeaderNote);
                assert.equal(await rReceipt.isTransferLineNote, await document.isTransferLineNote);

                const purchaseReceiptLine1 = await rReceipt.lines.elementAt(0);
                const purchaseOrderLine1 = await document.lines.elementAt(0);
                assert.equal(
                    (await purchaseReceiptLine1.internalNote).toString(),
                    (await purchaseOrderLine1.internalNote).toString(),
                );

                const purchaseReceiptLine2 = await rReceipt.lines.elementAt(1);
                const purchaseOrderLine2 = await document.lines.elementAt(1);
                assert.equal(
                    (await purchaseReceiptLine2.internalNote).toString(),
                    (await purchaseOrderLine2.internalNote).toString(),
                );
            },
            { today: testDate },
        ));
    it('Create receipt for purchase order PO3 - isTransferHeaderNote = false isTransferLineNote = true', () =>
        Test.withContext(
            async context => {
                const document = await context.read(
                    xtremPurchasing.nodes.PurchaseOrder,
                    { number: 'PO3' },
                    { forUpdate: true },
                );
                await document.$.set({ isTransferHeaderNote: false });
                await document.$.set({ isTransferLineNote: true });
                await document.$.save();

                const receipts = await xtremPurchasing.nodes.PurchaseOrder.createPurchaseReceipt(context, document);
                await context.flushDeferredActions();
                assert.equal(receipts.length, 1);
                const receiptNumber = await receipts.at(0)?.number;
                assert.equal(receiptNumber, 'PR200001');

                const purchaseReceipt = await context.read(xtremPurchasing.nodes.PurchaseReceipt, {
                    number: receiptNumber,
                });
                // Must be the same of the supplier
                assert.equal(
                    (await purchaseReceipt.internalNote).toString(),
                    (await (await purchaseReceipt.businessRelation).internalNote).toString(),
                );
                assert.notEqual(
                    (await purchaseReceipt.internalNote).toString(),
                    (await document.internalNote).toString(),
                );
                assert.equal(await purchaseReceipt.isTransferHeaderNote, await document.isTransferHeaderNote);
                assert.equal(await purchaseReceipt.isTransferLineNote, await document.isTransferLineNote);

                const purchaseReceiptLine1 = await purchaseReceipt.lines.elementAt(0);
                const purchaseOrderLine1 = await document.lines.elementAt(0);
                assert.equal(
                    (await purchaseReceiptLine1.internalNote).toString(),
                    (await purchaseOrderLine1.internalNote).toString(),
                );

                const purchaseReceiptLine2 = await purchaseReceipt.lines.elementAt(1);
                const purchaseOrderLine2 = await document.lines.elementAt(1);
                assert.equal(
                    (await purchaseReceiptLine2.internalNote).toString(),
                    (await purchaseOrderLine2.internalNote).toString(),
                );
            },
            { today: testDate },
        ));
    it('Create receipt for purchase order PO3 - isTransferHeaderNote = true isTransferLineNote = false', () =>
        Test.withContext(
            async context => {
                const document = await context.read(
                    xtremPurchasing.nodes.PurchaseOrder,
                    { number: 'PO3' },
                    { forUpdate: true },
                );
                await document.$.set({ isTransferLineNote: false, isTransferHeaderNote: true });
                await document.$.save();

                const receipts = await xtremPurchasing.nodes.PurchaseOrder.createPurchaseReceipt(context, document);

                await context.flushDeferredActions();
                assert.equal(receipts.length, 1);
                const receiptNumber = await receipts.at(0)?.number;
                assert.equal(receiptNumber, 'PR200001');

                const purchaseReceipt = await context.read(xtremPurchasing.nodes.PurchaseReceipt, {
                    number: receiptNumber,
                });

                assert.equal((await purchaseReceipt.internalNote).toString(), (await document.internalNote).toString());
                assert.equal(await purchaseReceipt.isTransferHeaderNote, await document.isTransferHeaderNote);
                assert.equal(await purchaseReceipt.isTransferLineNote, await document.isTransferLineNote);

                const purchaseReceiptLine1 = await purchaseReceipt.lines.elementAt(0);
                const purchaseOrderLine1 = await document.lines.elementAt(0);
                assert.isEmpty((await purchaseReceiptLine1.internalNote).toString());
                assert.notEqual(
                    (await purchaseReceiptLine1.internalNote).toString(),
                    (await purchaseOrderLine1.internalNote).toString(),
                );

                const purchaseReceiptLine2 = await purchaseReceipt.lines.elementAt(1);
                const purchaseOrderLine2 = await document.lines.elementAt(1);
                assert.isEmpty((await purchaseReceiptLine2.internalNote).toString());
                assert.notEqual(
                    (await purchaseReceiptLine2.internalNote).toString(),
                    (await purchaseOrderLine2.internalNote).toString(),
                );
            },
            { today: testDate },
        ));
    it('Create receipt for purchase order PO3 - isTransferHeaderNote = false isTransferLineNote = false', () =>
        Test.withContext(
            async context => {
                const document = await context.read(
                    xtremPurchasing.nodes.PurchaseOrder,
                    { number: 'PO3' },
                    { forUpdate: true },
                );
                await document.$.set({ isTransferHeaderNote: false });
                await document.$.set({ isTransferLineNote: false });
                await document.$.save();

                const receipts = await xtremPurchasing.nodes.PurchaseOrder.createPurchaseReceipt(context, document);
                await context.flushDeferredActions();
                assert.equal(receipts.length, 1);
                const receiptNumber = await receipts.at(0)?.number;
                assert.equal(receiptNumber, 'PR200001');

                const purchaseReceipt = await context.read(xtremPurchasing.nodes.PurchaseReceipt, {
                    number: receiptNumber,
                });
                // Must be the same of the supplier
                assert.equal(
                    (await purchaseReceipt.internalNote).toString(),
                    (await (await purchaseReceipt.businessRelation).internalNote).toString(),
                );
                assert.notEqual(
                    (await purchaseReceipt.internalNote).toString(),
                    (await document.internalNote).toString(),
                );
                assert.equal(await purchaseReceipt.isTransferHeaderNote, await document.isTransferHeaderNote);
                assert.equal(await purchaseReceipt.isTransferLineNote, await document.isTransferLineNote);

                const purchaseReceiptLine1 = await purchaseReceipt.lines.elementAt(0);
                const purchaseOrderLine1 = await document.lines.elementAt(0);
                assert.isEmpty((await purchaseReceiptLine1.internalNote).toString());
                assert.notEqual(
                    (await purchaseReceiptLine1.internalNote).toString(),
                    (await purchaseOrderLine1.internalNote).toString(),
                );

                const purchaseReceiptLine2 = await purchaseReceipt.lines.elementAt(1);
                const purchaseOrderLine2 = await document.lines.elementAt(1);
                assert.isEmpty((await purchaseReceiptLine2.internalNote).toString());
                assert.notEqual(
                    (await purchaseReceiptLine2.internalNote).toString(),
                    (await purchaseOrderLine2.internalNote).toString(),
                );
            },
            { today: testDate },
        ));
    it('Receipt fully a purchase order with a stock site neither finance nor purchase', () =>
        Test.withContext(
            async context => {
                const site = await context.read(xtremSystem.nodes.Site, { id: 'CAS02' }, { forUpdate: true });
                assert.isFalse(await site.isFinance);
                assert.isTrue(await site.isInventory);
                await site.$.set({ isPurchase: false });
                await site.$.save();

                const newPurchaseOrder = await context.create(xtremPurchasing.nodes.PurchaseOrder, {
                    number: 'TEST-XT-95966',
                    site: '#CAS01',
                    stockSite: '#CAS02',
                    businessRelation: '#500',
                    currency: '#EUR',
                    fxRateDate: date.make(2020, 8, 10),
                    orderDate: date.make(2020, 8, 10),
                    lines: [
                        {
                            item: '#Milk',
                            quantity: 100,
                            unit: '#KILOGRAM',
                            grossPrice: 100,
                            expectedReceiptDate: date.today(),
                        },
                    ],
                });
                await newPurchaseOrder.$.save({ flushDeferredActions: true });
                assert.deepEqual(newPurchaseOrder.$.context.diagnoses, []);

                let purchaseOrder = await context.read(
                    xtremPurchasing.nodes.PurchaseOrder,
                    {
                        number: 'TEST-XT-95966',
                    },
                    { forUpdate: true },
                );

                const confirmResult = await xtremPurchasing.nodes.PurchaseOrder.confirm(
                    context,
                    newPurchaseOrder,
                    true,
                );
                assert.isTrue(confirmResult);

                purchaseOrder = await context.read(
                    xtremPurchasing.nodes.PurchaseOrder,
                    {
                        number: 'TEST-XT-95966',
                    },
                    { forUpdate: true },
                );

                assert.isTrue(await (await purchaseOrder.site).isFinance);
                assert.isFalse(await (await purchaseOrder.stockSite).isFinance);
                assert.equal(await purchaseOrder.status, 'pending');
                assert.equal(await purchaseOrder.approvalStatus, 'confirmed');
                assert.equal(await purchaseOrder.receiptStatus, 'notReceived');

                const receipts = await xtremPurchasing.nodes.PurchaseOrder.createPurchaseReceipt(
                    context,
                    purchaseOrder,
                );
                await context.flushDeferredActions();

                assert.isArray(receipts);
                assert.lengthOf(receipts, 1);
                const purchaseReceipt = await context.read(xtremPurchasing.nodes.PurchaseReceipt, {
                    number: await receipts[0].number,
                });
                assert.equal(await (await purchaseReceipt.site).id, 'CAS02');
                assert.equal(await (await purchaseReceipt.stockSite).id, 'CAS02');
            },
            { today: '2025-06-25' },
        ));
});
describe('Purchase order node origin requisition lines - create fail', () => {
    const testDate = '2020-08-10';
    it('Create purchase order PO17 - different purchase unit', () =>
        Test.withContext(
            async context => {
                const newPurchaseOrder = await context.create(xtremPurchasing.nodes.PurchaseOrder, {
                    site: '#US001',
                    businessRelation: '#US017',
                    currency: { id: 'ZAR' },
                    orderDate: date.make(2020, 8, 10),
                    fxRateDate: date.make(2020, 8, 10),
                    lines: [
                        {
                            item: '#Muesli',
                            quantity: 8,
                            quantityInStockUnit: 8000,
                            unit: '#GRAM',
                            grossPrice: 30,
                            expectedReceiptDate: date.today(),
                            unitToStockUnitConversionFactor: 1000,
                            purchaseRequisitionLines: [
                                {
                                    purchaseRequisitionLine: 'REQ14|10',
                                    orderedQuantity: 8,
                                    orderedQuantityInStockUnit: 8000,
                                },
                            ],
                        },
                    ],
                });
                await assert.isRejected(newPurchaseOrder.$.save());
                assert.deepEqual(newPurchaseOrder.$.context.diagnoses, [
                    {
                        message:
                            'The purchase order needs to have the same values for the following properties: purchase unit',
                        path: ['lines', '-1000000002', 'purchaseRequisitionLines', '-1000000005'],
                        severity: 3,
                    },
                    {
                        message: 'The item:Muesli, is not managed for the site: US001, and supplier: US017.',
                        path: ['lines', '-1000000002'],
                        severity: 2,
                    },
                ]);
                await assert.isRejected(newPurchaseOrder.$.save());
            },
            { today: testDate },
        ));
    it('Create purchase order PO17 - different item', () =>
        Test.withContext(
            async context => {
                const newPurchaseOrder = await context.create(xtremPurchasing.nodes.PurchaseOrder, {
                    site: '#US001',
                    businessRelation: '#US017',
                    currency: { id: 'ZAR' },
                    orderDate: date.make(2020, 8, 10),
                    fxRateDate: date.make(2021, 8, 10),
                    lines: [
                        {
                            item: '#Chemical C',
                            quantity: 8,
                            unit: '#KILOGRAM',
                            grossPrice: 30,
                            expectedReceiptDate: date.today(),
                            purchaseRequisitionLines: [
                                {
                                    purchaseRequisitionLine: 'REQ14|10',
                                    orderedQuantity: 8,
                                },
                            ],
                        },
                    ],
                });
                await assert.isRejected(newPurchaseOrder.$.save());
                assert.deepEqual(newPurchaseOrder.$.context.diagnoses, [
                    {
                        message: 'The purchase order needs to have the same values for the following properties: item',
                        path: ['lines', '-1000000002', 'purchaseRequisitionLines', '-1000000005'],
                        severity: 3,
                    },
                    {
                        message: 'The item: Chemical C, is not managed for the supplier: US017.',
                        path: ['lines', '-1000000002'],
                        severity: 2,
                    },
                ]);
                await assert.isRejected(newPurchaseOrder.$.save());
            },
            { today: testDate },
        ));
    it('Create purchase order PO17 - different supplier', () =>
        Test.withContext(
            async context => {
                const newPurchaseOrder = await context.create(xtremPurchasing.nodes.PurchaseOrder, {
                    site: '#US001',
                    businessRelation: '#LECLERC',
                    currency: { id: 'ZAR' },
                    orderDate: date.make(2020, 8, 10),
                    fxRateDate: date.make(2020, 8, 10),
                    lines: [
                        {
                            item: '#Muesli',
                            quantity: 8,
                            unit: '#KILOGRAM',
                            grossPrice: 30,
                            expectedReceiptDate: date.today(),
                            purchaseRequisitionLines: [
                                {
                                    purchaseRequisitionLine: 'REQ14|10',
                                    orderedQuantity: 8,
                                },
                            ],
                        },
                    ],
                });
                await assert.isRejected(newPurchaseOrder.$.save());
                assert.deepEqual(newPurchaseOrder.$.context.diagnoses, [
                    {
                        message:
                            'The purchase order needs to have the same values for the following properties: supplier',
                        path: ['lines', '-1000000002', 'purchaseRequisitionLines', '-1000000005'],
                        severity: 3,
                    },
                ]);
                await assert.isRejected(newPurchaseOrder.$.save());
            },
            { today: testDate },
        ));
    it('Create purchase order PO17 - different orderedQuantityInStockUnit in payload', () =>
        Test.withContext(
            async context => {
                const newPurchaseOrder = await context.create(xtremPurchasing.nodes.PurchaseOrder, {
                    site: '#US001',
                    businessRelation: '#US017',
                    currency: '#EUR',
                    orderDate: date.make(2020, 8, 10),
                    fxRateDate: date.make(2021, 8, 10),
                    lines: [
                        {
                            item: '#Chemical C',
                            quantity: 8,
                            unit: '#KILOGRAM',
                            grossPrice: 30,
                            expectedReceiptDate: date.today(),
                            purchaseRequisitionLines: [
                                {
                                    purchaseRequisitionLine: 'REQ14|10',
                                    orderedQuantity: 8,
                                    orderedQuantityInStockUnit: 18,
                                },
                            ],
                        },
                    ],
                });
                await assert.isRejected(newPurchaseOrder.$.save());
                assert.deepEqual(newPurchaseOrder.$.context.diagnoses, [
                    {
                        message:
                            'The provided quantity in stock unit is different then the converted quantity from purchase unit to stock unit.',
                        path: [
                            'lines',
                            '-1000000002',
                            'purchaseRequisitionLines',
                            '-1000000005',
                            'orderedQuantityInStockUnit',
                        ],
                        severity: 3,
                    },
                    {
                        message:
                            'The purchase order needs to have the same values for the following properties: item currency',
                        path: ['lines', '-1000000002', 'purchaseRequisitionLines', '-1000000005'],
                        severity: 3,
                    },
                    {
                        message: 'The item: Chemical C, is not managed for the supplier: US017.',
                        path: ['lines', '-1000000002'],
                        severity: 2,
                    },
                ]);
                await assert.isRejected(newPurchaseOrder.$.save());
            },
            { today: testDate },
        ));
    it('Get projected stock - Create purchase order node', () =>
        Test.withContext(
            async context => {
                const site = await context.read(xtremSystem.nodes.Site, { id: 'US001' });
                const item = await context.read(xtremMasterData.nodes.Item, { _id: '#Muesli' });

                let result = await xtremStockData.nodeExtensions.ItemSiteExtension.getProjectedStock(
                    context,
                    item,
                    site,
                    5,
                );
                assert.isNotNull(result);
                compareData(result, [
                    {
                        day: date.today(),
                        demand: 0,
                        supply: 0,
                        stockLevel: 100200,
                        reorderLevel: 10,
                        safetyLevel: 220,
                    },
                    {
                        day: date.today().addDays(1),
                        demand: 0,
                        supply: 0,
                        stockLevel: 100200,
                        reorderLevel: 10,
                        safetyLevel: 220,
                    },
                    {
                        day: date.today().addDays(2),
                        demand: 0,
                        supply: 0,
                        stockLevel: 100200,
                        reorderLevel: 10,
                        safetyLevel: 220,
                    },
                    {
                        day: date.today().addDays(3),
                        demand: 0,
                        supply: 0,
                        stockLevel: 100200,
                        reorderLevel: 10,
                        safetyLevel: 220,
                    },
                    {
                        day: date.today().addDays(4),
                        demand: 0,
                        supply: 0,
                        stockLevel: 100200,
                        reorderLevel: 10,
                        safetyLevel: 220,
                    },
                ]);

                const newPurchaseOrder = await context.create(xtremPurchasing.nodes.PurchaseOrder, {
                    site: '#US001',
                    businessRelation: '#LECLERC',
                    currency: '#USD',
                    fxRateDate: date.today(),
                    orderDate: date.today(),
                    lines: [
                        {
                            item: '#Muesli',
                            quantity: 10,
                            unit: '#GRAM',
                            grossPrice: 30,
                            expectedReceiptDate: date.today().addDays(3),
                        },
                    ],
                });
                await newPurchaseOrder.$.save();
                assert.deepEqual(newPurchaseOrder.$.context.diagnoses, []);

                result = await xtremStockData.nodeExtensions.ItemSiteExtension.getProjectedStock(
                    context,
                    item,
                    site,
                    5,
                );
                assert.isNotNull(result);
                compareData(result, [
                    {
                        day: date.today(),
                        demand: 0,
                        supply: 0,
                        stockLevel: 100200,
                        reorderLevel: 10,
                        safetyLevel: 220,
                    },
                    {
                        day: date.today().addDays(1),
                        demand: 0,
                        supply: 0,
                        stockLevel: 100200,
                        reorderLevel: 10,
                        safetyLevel: 220,
                    },
                    {
                        day: date.today().addDays(2),
                        demand: 0,
                        supply: 0,
                        stockLevel: 100200,
                        reorderLevel: 10,
                        safetyLevel: 220,
                    },
                    {
                        day: date.today().addDays(3),
                        demand: 0,
                        supply: 10,
                        stockLevel: 100210,
                        reorderLevel: 10,
                        safetyLevel: 220,
                    },
                    {
                        day: date.today().addDays(4),
                        demand: 0,
                        supply: 0,
                        stockLevel: 100210,
                        reorderLevel: 10,
                        safetyLevel: 220,
                    },
                ]);
            },
            {
                today: '2020-06-10',
            },
        ));
});
describe('PurchaseOrderNode tax determination', () => {
    before(() => {});

    function createPurchaseOrder(context: Context, orderDate: date, billBySupplierValue: string) {
        const baseData: NodeCreateData<xtremPurchasing.nodes.PurchaseOrder> = {
            site: '#US001',
            businessRelation: '#LECLERC',
            billBySupplier: billBySupplierValue,
            currency: '#USD',
            orderDate,
            totalAmountExcludingTax: 28,
            totalTaxAmount: 9.8,
            lines: [
                {
                    _action: 'create',
                    item: '#Muesli',
                    quantity: 1,
                    unit: '#GRAM',
                    grossPrice: 100,
                },
                {
                    _action: 'create',
                    item: '#Muesli',
                    quantity: 4,
                    unit: '#GRAM',
                    grossPrice: 44.55,
                    taxes: [{ _action: 'create', taxReference: '#FR002', taxRate: 0 }],
                },
            ],
        };
        return context.create(xtremPurchasing.nodes.PurchaseOrder, baseData);
    }

    it('Create purchase order tax determination', () =>
        Test.withContext(
            async context => {
                const company = await context.read(xtremSystem.nodes.Company, { id: 'US001' }, { forUpdate: true });

                await company.$.set({ taxEngine: 'genericTaxCalculation' });
                await company.$.save();

                const item = await context.read(xtremMasterData.nodes.Item, { _id: '#Muesli' }, { forUpdate: true });

                await item.$.set({ itemTaxGroup: '#TEST_NORMAL' });
                await item.$.save();

                let purchaseOrder: xtremPurchasing.nodes.PurchaseOrder = await createPurchaseOrder(
                    context,
                    date.make(2022, 2, 1),
                    '#LECLERC',
                );
                await purchaseOrder.$.save();

                purchaseOrder = await context.read(
                    xtremPurchasing.nodes.PurchaseOrder,
                    { _id: purchaseOrder._id },
                    { forUpdate: true },
                );

                await (
                    await (await purchaseOrder.lines.at(0))?.taxes.slice()
                )?.forEach(async tax =>
                    (await purchaseOrder.lines.at(0))?.$.set({
                        taxes: [{ _action: 'delete', _id: tax._id }],
                    }),
                );

                await purchaseOrder.$.set({
                    billBySupplier: await purchaseOrder.businessRelation,
                });

                await purchaseOrder.$.save();

                assert.equal(String(await (await purchaseOrder.taxes.at(0))?.taxAmount), '9.8');
                assert.equal(String(await (await purchaseOrder.taxes.at(0))?.deductibleTaxAmount), '9.8');
                assert.equal(String(await purchaseOrder.totalTaxAmount), '9.8');
                assert.equal(String(await purchaseOrder.totalAmountExcludingTax), '28');
                assert.equal(String(await (await purchaseOrder.taxes.at(0))?.taxAmountAdjusted), '9.8');
                assert.equal(String(await (await (await purchaseOrder.lines.at(0))?.taxes.at(0))?.taxAmount), '0');
                assert.equal(
                    String(await (await (await purchaseOrder.lines.at(0))?.taxes.at(0))?.taxAmountAdjusted),
                    '0',
                );
                assert.equal(String(await (await (await purchaseOrder.lines.at(1))?.taxes.at(0))?.taxAmount), '9.8');
                assert.equal(
                    String(await (await (await purchaseOrder.lines.at(1))?.taxes.at(0))?.taxAmountAdjusted),
                    '9.8',
                );
            },
            { today: '2022-02-01' },
        ));

    it('Verify chronological control between two purchase orders on creation', () =>
        withSequenceNumberContext(
            'PurchaseOrder',
            { isChronological: true },
            async context => {
                const newPurchaseOrder = await context.create(xtremPurchasing.nodes.PurchaseOrder, {
                    site: '#US001',
                    businessRelation: '#LECLERC',
                    currency: '#USD',
                    fxRateDate: date.make(2020, 8, 10),
                    orderDate: date.make(2020, 8, 10),
                    lines: [
                        {
                            item: '#Muesli',
                            quantity: 10,
                            unit: '#GRAM',
                            grossPrice: 30,
                            expectedReceiptDate: date.today(),
                        },
                    ],
                });
                await newPurchaseOrder.$.save({ flushDeferredActions: true });
                assert.deepEqual(newPurchaseOrder.$.context.diagnoses, []);

                const newPurchaseOrderError = await context.create(xtremPurchasing.nodes.PurchaseOrder, {
                    site: '#US001',
                    businessRelation: '#LECLERC',
                    currency: '#USD',
                    fxRateDate: date.make(2020, 8, 9),
                    orderDate: date.make(2020, 8, 9),
                    lines: [
                        {
                            item: '#Muesli',
                            quantity: 10,
                            unit: '#GRAM',
                            grossPrice: 30,
                            expectedReceiptDate: date.today(),
                        },
                    ],
                });

                await assert.isRejected(newPurchaseOrderError.$.save(), 'The record was not created.');
                assert.deepEqual(newPurchaseOrderError.$.context.diagnoses, [
                    {
                        severity: 3,
                        path: [],
                        message: 'The document date 2020-08-09 is earlier than the previous document date 2020-08-10.',
                    },
                ]);
            },
            { today: '2020-08-10' },
        ));
});
describe('Purchase order node - process flow email approval vs confirmation', () => {
    const testDate = '2020-08-10';
    it('Create purchase order in approval flow, update site. Create purchase flow in new confirmation workflow', () =>
        Test.withContext(
            async context => {
                // create purchase order with isPurchaseOrderApprovalManaged=true
                let newPurchaseOrder = await context.create(xtremPurchasing.nodes.PurchaseOrder, {
                    site: '#US002',
                    businessRelation: '#500',
                    currency: 1,
                    fxRateDate: date.make(2020, 8, 10),
                    orderDate: date.make(2020, 8, 10),
                    lines: [
                        {
                            item: '#Milk',
                            quantity: 10,
                            unit: '#KILOGRAM',
                            grossPrice: 30,
                            expectedReceiptDate: date.today(),
                        },
                    ],
                });

                await newPurchaseOrder.$.save();
                let newPurchaseOrderId = newPurchaseOrder._id;
                // rollbackCache function will flush deferred actions and saves
                await Test.rollbackCache(context);

                newPurchaseOrder = await context.read(
                    xtremPurchasing.nodes.PurchaseOrder,
                    { _id: newPurchaseOrderId },
                    { forUpdate: true },
                );

                // check status is draft
                assert.equal(await newPurchaseOrder.status, 'draft');
                assert.equal(await newPurchaseOrder.approvalStatus, 'draft');

                // send approval mail
                const isSend = await xtremPurchasing.nodes.PurchaseOrder.sendApprovalRequestMail(
                    context,
                    newPurchaseOrder,
                    await getUser(context, '<EMAIL>'),
                );
                assert.isTrue(isSend);

                newPurchaseOrder = await context.read(
                    xtremPurchasing.nodes.PurchaseOrder,
                    {
                        _id: newPurchaseOrderId,
                    },
                    { forUpdate: true },
                );

                // check status is pending approval
                assert.equal(await newPurchaseOrder.status, 'draft');
                assert.equal(await newPurchaseOrder.approvalStatus, 'pendingApproval');

                // Approve PO
                const approveResult = await xtremPurchasing.nodes.PurchaseOrder.approve(
                    context,
                    newPurchaseOrder,
                    true,
                );
                assert.isTrue(approveResult);

                newPurchaseOrder = await context.read(xtremPurchasing.nodes.PurchaseOrder, {
                    _id: newPurchaseOrderId,
                });

                // PO status is approved
                assert.equal(await newPurchaseOrder.status, 'pending');
                assert.equal(await newPurchaseOrder.approvalStatus, 'approved');

                newPurchaseOrder = await context.read(xtremPurchasing.nodes.PurchaseOrder, {
                    _id: newPurchaseOrderId,
                });

                const site = await context.read(xtremSystem.nodes.Site, { id: 'US002' }, { forUpdate: true });
                // able to update site and set isPurchaseOrderApprovalManaged=false
                await site.$.set({ isLocationManaged: false, isPurchaseOrderApprovalManaged: false });
                await site.$.save();
                assert.deepEqual(site.$.context.diagnoses, []);

                // create new PO with confirmation workflow
                newPurchaseOrder = await context.create(xtremPurchasing.nodes.PurchaseOrder, {
                    site: '#US002',
                    businessRelation: '#500',
                    currency: 1,
                    fxRateDate: date.make(2020, 8, 10),
                    orderDate: date.make(2020, 8, 10),
                    lines: [
                        {
                            item: '#Milk',
                            quantity: 10,
                            unit: '#KILOGRAM',
                            grossPrice: 30,
                            expectedReceiptDate: date.today(),
                        },
                    ],
                });

                await newPurchaseOrder.$.save();
                newPurchaseOrderId = newPurchaseOrder._id;
                // rollbackCache function will flush deferred actions and saves
                await Test.rollbackCache(context);

                newPurchaseOrder = await context.read(
                    xtremPurchasing.nodes.PurchaseOrder,
                    {
                        _id: newPurchaseOrderId,
                    },
                    { forUpdate: true },
                );

                // confirm PO
                const confirmResult = await xtremPurchasing.nodes.PurchaseOrder.confirm(
                    context,
                    newPurchaseOrder,
                    true,
                );
                assert.isTrue(confirmResult);

                newPurchaseOrder = await context.read(
                    xtremPurchasing.nodes.PurchaseOrder,
                    {
                        _id: newPurchaseOrderId,
                    },
                    { forUpdate: true },
                );

                // check status to confirm PO is confirmed
                assert.equal(await newPurchaseOrder.status, 'pending');
                assert.equal(await newPurchaseOrder.approvalStatus, 'confirmed');
            },
            {
                mocks: ['axios'],
                scenario: 'create-purchase-order-in-approval-flow',
                directory: __dirname,
                today: testDate,
            },
        ));

    it('Create purchase order in confirm workflow, control approvalStatus update to approval workflow', () =>
        Test.withContext(
            async context => {
                const site = await context.read(xtremSystem.nodes.Site, { id: 'US002' }, { forUpdate: true });

                // able to update site and set isPurchaseOrderApprovalManaged=false
                await site.$.set({ isLocationManaged: false, isPurchaseOrderApprovalManaged: false });
                await site.$.save();
                assert.deepEqual(site.$.context.diagnoses, []);

                // create new PO with approval workflow
                let newPurchaseOrder = await context.create(xtremPurchasing.nodes.PurchaseOrder, {
                    site: '#US002',
                    businessRelation: '#500',
                    currency: 1,
                    fxRateDate: date.make(2020, 8, 10),
                    orderDate: date.make(2020, 8, 10),
                    lines: [
                        {
                            item: '#Milk',
                            quantity: 10,
                            unit: '#KILOGRAM',
                            grossPrice: 30,
                            expectedReceiptDate: date.today(),
                        },
                    ],
                });

                await newPurchaseOrder.$.save();
                const newPurchaseOrderId = newPurchaseOrder._id;
                // rollbackCache function will flush deferred actions and saves
                await Test.rollbackCache(context);

                newPurchaseOrder = await context.read(
                    xtremPurchasing.nodes.PurchaseOrder,
                    {
                        _id: newPurchaseOrderId,
                    },
                    { forUpdate: true },
                );

                // check status to confirm PO is confirmed
                // check that approval status cannot be updated to approved - control
                await newPurchaseOrder.$.set({ approvalStatus: 'pendingApproval' });
                await assert.isRejected(newPurchaseOrder.$.save());
                assert.deepEqual(newPurchaseOrder.$.context.diagnoses, [
                    {
                        message: 'You cannot use this status if the approval workflow is disabled.',
                        path: ['approvalStatus'],
                        severity: 3,
                    },
                ]);
            },
            { today: testDate },
        ));

    it('Create purchase order in approval workflow, control approvalStatus update to confirm workflow', () =>
        Test.withContext(
            async context => {
                // create new PO with confirmation workflow
                let newPurchaseOrder = await context.create(xtremPurchasing.nodes.PurchaseOrder, {
                    site: '#US002',
                    businessRelation: '#500',
                    currency: 1,
                    fxRateDate: date.make(2020, 8, 10),
                    orderDate: date.make(2020, 8, 10),
                    lines: [
                        {
                            item: '#Milk',
                            quantity: 10,
                            unit: '#KILOGRAM',
                            grossPrice: 30,
                            expectedReceiptDate: date.today(),
                        },
                    ],
                });

                await newPurchaseOrder.$.save();
                const newPurchaseOrderId = newPurchaseOrder._id;
                // TODO: Replace context.flushDeferredActions() with Test.rollbackCache(context)
                await context.flushDeferredActions();

                newPurchaseOrder = await context.read(
                    xtremPurchasing.nodes.PurchaseOrder,
                    {
                        _id: newPurchaseOrderId,
                    },
                    { forUpdate: true },
                );

                // check status to confirm PO is confirmed
                // check that approval status cannot be updated to confirmed - control
                await newPurchaseOrder.$.set({ approvalStatus: 'confirmed' });
                await assert.isRejected(newPurchaseOrder.$.save());
                assert.deepEqual(newPurchaseOrder.$.context.diagnoses, [
                    {
                        message: 'You cannot use this status if the approval workflow is enabled.',
                        path: ['approvalStatus'],
                        severity: 3,
                    },
                ]);
            },
            { today: testDate },
        ));

    it('Throw exception when confirmed status update and document not in draft or isPurchaseOrderApprovalManaged=false', () =>
        Test.withContext(async context => {
            const po2 = await context.read(xtremPurchasing.nodes.PurchaseOrder, { number: 'PO2' }, { forUpdate: true });
            await assert.isRejected(
                xtremPurchasing.nodes.PurchaseOrder.confirm(context, po2, true),
                'The approval workflow needs to be disabled to update the status to confirmed.',
            );
        }));
});

describe('Note propagation from purchase requisition to purchase order', () => {
    it('Ensure the propagation of the header note and line notes', () =>
        Test.withContext(async context => {
            const purchaseRequisition = await context.read(xtremPurchasing.nodes.PurchaseRequisition, {
                number: 'REQ7',
            });
            const requisitionIsTransferHeaderNote = await purchaseRequisition.isTransferHeaderNote;
            const requisitionIsTransferLineNote = await purchaseRequisition.isTransferLineNote;
            const requisitionInternalNote = (await purchaseRequisition.internalNote).toString();
            const requisitionInternalNoteLine1 = (
                await (
                    await purchaseRequisition.lines.elementAt(0)
                ).internalNote
            ).toString();

            assert.isTrue(requisitionIsTransferHeaderNote);
            assert.isTrue(requisitionIsTransferLineNote);
            assert.equal(requisitionInternalNote, 'REQ7 internal note');
            assert.equal(requisitionInternalNoteLine1, 'REQ7 internal line note');
            const [order] = await xtremPurchasing.nodes.PurchaseRequisition.createPurchaseOrders(
                context,
                purchaseRequisition,
            );

            // Setup must be the same
            assert.equal(await order.isTransferHeaderNote, requisitionIsTransferHeaderNote);
            assert.equal(await order.isTransferHeaderNote, requisitionIsTransferHeaderNote);
            // Note should be the same
            assert.equal((await order.internalNote).toString(), requisitionInternalNote);
            // Lines - Note should be equal
            assert.equal(
                (await (await order.lines.elementAt(0)).internalNote).toString(),
                requisitionInternalNoteLine1,
            );
        }));
    it('Ensure the propagation of the header note only', () =>
        Test.withContext(async context => {
            const purchaseRequisition = await context.read(xtremPurchasing.nodes.PurchaseRequisition, {
                number: 'REQ11',
            });
            const requisitionIsTransferHeaderNote = await purchaseRequisition.isTransferHeaderNote;
            const requisitionIsTransferLineNote = await purchaseRequisition.isTransferLineNote;
            const requisitionInternalNote = (await purchaseRequisition.internalNote).toString();
            const requisitionInternalNoteLine1 = (
                await (
                    await purchaseRequisition.lines.elementAt(0)
                ).internalNote
            ).toString();
            const requisitionInternalNoteLine2 = (
                await (
                    await purchaseRequisition.lines.elementAt(1)
                ).internalNote
            ).toString();
            const requisitionInternalNoteLine3 = (
                await (
                    await purchaseRequisition.lines.elementAt(2)
                ).internalNote
            ).toString();

            assert.isTrue(requisitionIsTransferHeaderNote);
            assert.isFalse(requisitionIsTransferLineNote);
            assert.equal(requisitionInternalNote, 'REQ11 internal note');
            assert.equal(requisitionInternalNoteLine1, 'REQ11 internal line note 1');
            assert.equal(requisitionInternalNoteLine2, 'REQ11 internal line note 2');
            assert.equal(requisitionInternalNoteLine3, 'REQ11 internal line note 3');

            const [order] = await xtremPurchasing.nodes.PurchaseRequisition.createPurchaseOrders(
                context,
                purchaseRequisition,
            );

            // Setup must be the same
            assert.equal(await order.isTransferHeaderNote, requisitionIsTransferHeaderNote);
            assert.equal(await order.isTransferHeaderNote, requisitionIsTransferHeaderNote);
            // Note should be the same
            assert.equal((await order.internalNote).toString(), requisitionInternalNote);
            // Lines - Note should not be equal
            const orderNoteLine1 = (await (await order.lines.elementAt(0)).internalNote).toString();
            const orderNoteLine2 = (await (await order.lines.elementAt(1)).internalNote).toString();
            assert.isEmpty(orderNoteLine1);
            assert.isEmpty(orderNoteLine2);
            assert.notEqual(orderNoteLine1, requisitionInternalNoteLine1);
            assert.notEqual(orderNoteLine2, requisitionInternalNoteLine2);
        }));
    it('Ensure the propagation of the line notes only', () =>
        Test.withContext(async context => {
            const purchaseRequisition = await context.read(xtremPurchasing.nodes.PurchaseRequisition, {
                number: 'REQ9',
            });
            const requisitionIsTransferHeaderNote = await purchaseRequisition.isTransferHeaderNote;
            const requisitionIsTransferLineNote = await purchaseRequisition.isTransferLineNote;
            const requisitionInternalNote = (await purchaseRequisition.internalNote).toString();
            const requisitionInternalNoteLine1 = (
                await (
                    await purchaseRequisition.lines.elementAt(0)
                ).internalNote
            ).toString();
            const requisitionInternalNoteLine2 = (
                await (
                    await purchaseRequisition.lines.elementAt(1)
                ).internalNote
            ).toString();

            assert.isFalse(requisitionIsTransferHeaderNote);
            assert.isTrue(requisitionIsTransferLineNote);
            assert.equal(requisitionInternalNote, 'REQ9 internal note');
            assert.equal(requisitionInternalNoteLine1, 'REQ9 internal line note 1');
            assert.equal(requisitionInternalNoteLine2, 'REQ9 internal line note 2');

            const [purchaseOrder] = await xtremPurchasing.nodes.PurchaseRequisition.createPurchaseOrders(
                context,
                purchaseRequisition,
            );

            // Setup must be the same
            assert.equal(await purchaseOrder.isTransferHeaderNote, requisitionIsTransferHeaderNote);
            assert.equal(await purchaseOrder.isTransferHeaderNote, requisitionIsTransferHeaderNote);
            // Note should not be the same
            assert.notEqual((await purchaseOrder.internalNote).toString(), requisitionInternalNote);
            // Lines - Note should be equal
            const orderNoteLine1 = (await (await purchaseOrder.lines.elementAt(0)).internalNote).toString();
            assert.isNotEmpty(orderNoteLine1);
            assert.equal(orderNoteLine1, requisitionInternalNoteLine1);
        }));
});

describe('Purchase order node - check earliest expected date', () => {
    it('Create purchase order', () =>
        Test.withContext(
            async context => {
                const newPurchaseOrder = await context.create(xtremPurchasing.nodes.PurchaseOrder, {
                    site: '#US001',
                    businessRelation: '#LECLERC',
                    currency: '#USD',
                    fxRateDate: date.make(2020, 8, 10),
                    orderDate: date.make(2020, 8, 10),
                    lines: [
                        {
                            item: '#Muesli',
                            quantity: 10,
                            unit: '#GRAM',
                            grossPrice: 30,
                            expectedReceiptDate: date.make(2020, 8, 12),
                        },
                        {
                            item: '#Muesli',
                            quantity: 5,
                            unit: '#GRAM',
                            grossPrice: 30,
                            expectedReceiptDate: date.make(2020, 8, 11),
                        },
                    ],
                });
                await newPurchaseOrder.$.save();
                assert.deepEqual(newPurchaseOrder.$.context.diagnoses, []);
                assert.isNotNull(await newPurchaseOrder.earliestExpectedDate);
                assert.equal((await newPurchaseOrder.earliestExpectedDate)?.compare(date.make(2020, 8, 11)), 0);
            },
            { today: '2020-08-10' },
        ));

    it('Create purchase order attribute type restricted to', () =>
        Test.withContext(
            async context => {
                const newPurchaseOrder = await context.create(xtremPurchasing.nodes.PurchaseOrder, {
                    site: '#US001',
                    businessRelation: '#LECLERC',
                    currency: '#USD',
                    fxRateDate: date.make(2020, 8, 10),
                    orderDate: date.make(2020, 8, 10),
                    lines: [
                        {
                            item: '#Muesli',
                            quantity: 10,
                            unit: '#GRAM',
                            grossPrice: 30,
                            expectedReceiptDate: date.today(),
                            storedAttributes: {
                                employee: '',
                                task: 'TASK1',
                                project: '',
                            },
                        },
                    ],
                });
                await assert.isRejected(newPurchaseOrder.$.save());
                assert.deepEqual(newPurchaseOrder.$.context.diagnoses, [
                    {
                        severity: 3,
                        path: ['lines', '-1000000002', 'storedAttributes'],
                        message: 'The Project attribute needs to be filled in.',
                    },
                ]);
            },
            { today: '2020-08-10' },
        ));

    it('Create purchase order default attribute and dimensions', () =>
        Test.withContext(
            async context => {
                const newPurchaseOrder = await context.create(xtremPurchasing.nodes.PurchaseOrder, {
                    site: '#US001',
                    businessRelation: '#LECLERC',
                    currency: '#USD',
                    fxRateDate: date.make(2020, 8, 10),
                    orderDate: date.make(2020, 8, 10),
                    lines: [
                        {
                            item: '#Muesli',
                            quantity: 10,
                            unit: '#GRAM',
                            grossPrice: 30,
                            expectedReceiptDate: date.today(),
                        },
                    ],
                });
                await newPurchaseOrder.$.save();
                assert.deepEqual(await (await newPurchaseOrder.lines.elementAt(0)).storedDimensions, {
                    dimensionType01: '300',
                    dimensionType02: 'CHANNELVALUE1',
                });

                assert.equal(
                    JSON.stringify(await (await newPurchaseOrder.lines.elementAt(0)).storedAttributes),
                    JSON.stringify({
                        project: 'AttPROJ',
                        task: 'Task1',
                    }),
                );
            },
            { today: '2020-08-10' },
        ));

    it('Default site datatype should be masterDataSite', async () => {
        const query = `
            {
                getNodeDetails(nodeName: "PurchaseOrder") {
                    name
                    properties {
                    name
                    dataType
                    }
                }
            }
            `;
        const siteDefaultDatatype = await Test.withReadonlyContext(
            context =>
                Test.metadataGraphql<{
                    getNodeDetails: {
                        name: string;
                        properties: {
                            name: string;
                            dataType: string;
                            dataTypeDetails: {
                                node: string;
                                value: { bind: string };
                                helperText: { bind: string };
                                tunnelPageId: { bind: string };
                                tunnelPage: string;
                                isDefault: boolean;
                                columns: { bind: string }[];
                            }[];
                        }[];
                    };
                }>(context, query),
            { userEmail: '<EMAIL>' },
        );
        assert.equal(
            siteDefaultDatatype.data.getNodeDetails.properties.find(p => p.name === 'site')?.dataType,
            'masterDataSite',
        );
    });

    it('Duplicates a purchase order and sets correctly the line status to draft', () =>
        Test.withContext(async context => {
            const purchaseOrderDoc = await context.read(
                xtremPurchasing.nodes.PurchaseOrder,
                { number: 'PO30' },
                { forUpdate: true },
            );

            assert.equal(await (await purchaseOrderDoc.lines.elementAt(0)).status, 'inProgress');
            assert.equal(await (await purchaseOrderDoc.lines.elementAt(1)).status, 'inProgress');

            const duplicatedPurchaseOrderDoc = await purchaseOrderDoc.$.duplicate();

            assert.equal(await (await duplicatedPurchaseOrderDoc.lines.elementAt(0)).status, 'draft');
            assert.equal(await (await duplicatedPurchaseOrderDoc.lines.elementAt(1)).status, 'draft');
        }));
});
