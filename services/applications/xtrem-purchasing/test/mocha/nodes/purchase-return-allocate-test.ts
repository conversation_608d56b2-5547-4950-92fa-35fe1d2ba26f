import { Test } from '@sage/xtrem-core';
import * as xtremStockData from '@sage/xtrem-stock-data';
import { assert } from 'chai';
import * as xtremPurchasing from '../../../lib/index';

describe('Purchase return node - allocation', () => {
    it('purchase return - some lines partially allocated', () =>
        Test.withContext(async context => {
            const purchaseReturn1 = await context.read(xtremPurchasing.nodes.PurchaseReturn, { number: 'RET002' });
            const stockRecord = await context.read(xtremStockData.nodes.Stock, { _id: '31' });
            const purchaseReturnLine11 = await purchaseReturn1.lines.elementAt(0);
            assert.equal((await purchaseReturnLine11.quantityInStockUnit).toString(), '15000');
            assert.equal((await purchaseReturnLine11.quantityAllocated).toString(), '0');
            assert.equal(await purchaseReturnLine11.allocationStatus, 'notAllocated');
            const purchaseReturnLine12 = await purchaseReturn1.lines.elementAt(1);
            assert.equal((await purchaseReturnLine12.quantityInStockUnit).toString(), '100');
            assert.equal((await purchaseReturnLine12.quantityAllocated).toString(), '0');
            assert.equal(await purchaseReturnLine12.allocationStatus, 'notAllocated');
            assert.equal(await purchaseReturn1.allocationStatus, 'notAllocated');

            await xtremStockData.nodes.Stock.updateAllocations(context, {
                documentLine: purchaseReturnLine11 as any,
                allocationUpdates: [
                    {
                        action: 'create',
                        quantity: 100,
                        stockRecord: stockRecord as any,
                    },
                ],
            });

            const purchaseReturn2 = await context.read(xtremPurchasing.nodes.PurchaseReturn, { number: 'RET002' });
            const purchaseReturnLine21 = await purchaseReturn2.lines.elementAt(0);
            assert.equal((await purchaseReturnLine21.quantityInStockUnit).toString(), '15000');
            assert.equal((await purchaseReturnLine21.quantityAllocated).toString(), '100');
            assert.equal(await purchaseReturnLine21.allocationStatus, 'partiallyAllocated');
            const purchaseReturnLine22 = await purchaseReturn2.lines.elementAt(1);
            assert.equal((await purchaseReturnLine22.quantityInStockUnit).toString(), '100');
            assert.equal((await purchaseReturnLine22.quantityAllocated).toString(), '0');
            assert.equal(await purchaseReturnLine22.allocationStatus, 'notAllocated');
            assert.equal(await purchaseReturn2.allocationStatus, 'partiallyAllocated');
        }));

    it('purchase return - all lines partially allocated', () =>
        Test.withContext(async context => {
            const purchaseReturn1 = await context.read(xtremPurchasing.nodes.PurchaseReturn, { number: 'RET002' });
            const stockRecord = await context.read(xtremStockData.nodes.Stock, { _id: '31' });
            const purchaseReturnLine11 = await purchaseReturn1.lines.elementAt(0);
            assert.equal((await purchaseReturnLine11.quantityInStockUnit).toString(), '15000');
            assert.equal((await purchaseReturnLine11.quantityAllocated).toString(), '0');
            assert.equal(await purchaseReturnLine11.allocationStatus, 'notAllocated');
            const purchaseReturnLine12 = await purchaseReturn1.lines.elementAt(1);
            assert.equal((await purchaseReturnLine12.quantityInStockUnit).toString(), '100');
            assert.equal((await purchaseReturnLine12.quantityAllocated).toString(), '0');
            assert.equal(await purchaseReturnLine12.allocationStatus, 'notAllocated');
            assert.equal(await purchaseReturn1.allocationStatus, 'notAllocated');

            await xtremStockData.nodes.Stock.updateAllocations(context, {
                documentLine: purchaseReturnLine11 as any,
                allocationUpdates: [
                    {
                        action: 'create',
                        quantity: 100,
                        stockRecord: stockRecord as any,
                    },
                ],
            });
            await xtremStockData.nodes.Stock.updateAllocations(context, {
                documentLine: purchaseReturnLine12 as any,
                allocationUpdates: [
                    {
                        action: 'create',
                        quantity: 10,
                        stockRecord: stockRecord as any,
                    },
                ],
            });

            const purchaseReturn2 = await context.read(xtremPurchasing.nodes.PurchaseReturn, { number: 'RET002' });
            const purchaseReturnLine21 = await purchaseReturn2.lines.elementAt(0);
            assert.equal((await purchaseReturnLine21.quantityInStockUnit).toString(), '15000');
            assert.equal((await purchaseReturnLine21.quantityAllocated).toString(), '100');
            assert.equal(await purchaseReturnLine21.allocationStatus, 'partiallyAllocated');
            const purchaseReturnLine22 = await purchaseReturn2.lines.elementAt(1);
            assert.equal((await purchaseReturnLine22.quantityInStockUnit).toString(), '100');
            assert.equal((await purchaseReturnLine22.quantityAllocated).toString(), '10');
            assert.equal(await purchaseReturnLine22.allocationStatus, 'partiallyAllocated');
            assert.equal(await purchaseReturn2.allocationStatus, 'partiallyAllocated');
        }));

    it('purchase return - some lines allocated', () =>
        Test.withContext(async context => {
            const purchaseReturn1 = await context.read(xtremPurchasing.nodes.PurchaseReturn, { number: 'RET002' });
            const stockRecord = await context.read(xtremStockData.nodes.Stock, { _id: '31' });
            const purchaseReturnLine11 = await purchaseReturn1.lines.elementAt(0);
            assert.equal((await purchaseReturnLine11.quantityInStockUnit).toString(), '15000');
            assert.equal((await purchaseReturnLine11.quantityAllocated).toString(), '0');
            assert.equal(await purchaseReturnLine11.allocationStatus, 'notAllocated');
            const purchaseReturnLine12 = await purchaseReturn1.lines.elementAt(1);
            assert.equal((await purchaseReturnLine12.quantityInStockUnit).toString(), '100');
            assert.equal((await purchaseReturnLine12.quantityAllocated).toString(), '0');
            assert.equal(await purchaseReturnLine12.allocationStatus, 'notAllocated');
            assert.equal(await purchaseReturn1.allocationStatus, 'notAllocated');

            await xtremStockData.nodes.Stock.updateAllocations(context, {
                documentLine: purchaseReturnLine12 as any,
                allocationUpdates: [
                    {
                        action: 'create',
                        quantity: await purchaseReturnLine12.quantityInStockUnit,
                        stockRecord: stockRecord as any,
                    },
                ],
            });

            const purchaseReturn2 = await context.read(xtremPurchasing.nodes.PurchaseReturn, { number: 'RET002' });
            const purchaseReturnLine21 = await purchaseReturn2.lines.elementAt(0);
            assert.equal((await purchaseReturnLine21.quantityInStockUnit).toString(), '15000');
            assert.equal((await purchaseReturnLine21.quantityAllocated).toString(), '0');
            assert.equal(await purchaseReturnLine21.allocationStatus, 'notAllocated');
            const purchaseReturnLine22 = await purchaseReturn2.lines.elementAt(1);
            assert.equal((await purchaseReturnLine22.quantityInStockUnit).toString(), '100');
            assert.equal((await purchaseReturnLine22.quantityAllocated).toString(), '100');
            assert.equal(await purchaseReturnLine22.allocationStatus, 'allocated');
            assert.equal(await purchaseReturn2.allocationStatus, 'partiallyAllocated');
        }));

    it('purchase return - all lines allocated', () =>
        Test.withContext(async context => {
            const purchaseReturn1 = await context.read(xtremPurchasing.nodes.PurchaseReturn, { number: 'RET002' });
            const stockRecord = await context.read(xtremStockData.nodes.Stock, { _id: '31' });
            const purchaseReturnLine11 = await purchaseReturn1.lines.elementAt(0);
            assert.equal((await purchaseReturnLine11.quantityInStockUnit).toString(), '15000');
            assert.equal((await purchaseReturnLine11.quantityAllocated).toString(), '0');
            assert.equal(await purchaseReturnLine11.allocationStatus, 'notAllocated');
            const purchaseReturnLine12 = await purchaseReturn1.lines.elementAt(1);
            assert.equal((await purchaseReturnLine12.quantityInStockUnit).toString(), '100');
            assert.equal((await purchaseReturnLine12.quantityAllocated).toString(), '0');
            assert.equal(await purchaseReturnLine12.allocationStatus, 'notAllocated');
            assert.equal(await purchaseReturn1.allocationStatus, 'notAllocated');

            await xtremStockData.nodes.Stock.updateAllocations(context, {
                documentLine: purchaseReturnLine11 as any,
                allocationUpdates: [
                    {
                        action: 'create',
                        quantity: await purchaseReturnLine11.quantityInStockUnit,
                        stockRecord: stockRecord as any,
                    },
                ],
            });
            await xtremStockData.nodes.Stock.updateAllocations(context, {
                documentLine: purchaseReturnLine12 as any,
                allocationUpdates: [
                    {
                        action: 'create',
                        quantity: await purchaseReturnLine12.quantityInStockUnit,
                        stockRecord: stockRecord as any,
                    },
                ],
            });

            const purchaseReturn2 = await context.read(xtremPurchasing.nodes.PurchaseReturn, { number: 'RET002' });
            const purchaseReturnLine21 = await purchaseReturn2.lines.elementAt(0);
            assert.equal((await purchaseReturnLine21.quantityInStockUnit).toString(), '15000');
            assert.equal((await purchaseReturnLine21.quantityAllocated).toString(), '15000');
            assert.equal(await purchaseReturnLine21.allocationStatus, 'allocated');
            const purchaseReturnLine22 = await purchaseReturn2.lines.elementAt(1);
            assert.equal((await purchaseReturnLine22.quantityInStockUnit).toString(), '100');
            assert.equal((await purchaseReturnLine22.quantityAllocated).toString(), '100');
            assert.equal(await purchaseReturnLine22.allocationStatus, 'allocated');
            assert.equal(await purchaseReturn2.allocationStatus, 'allocated');
        }));

    it('purchase return - cannot delete if allocated', () =>
        Test.withContext(async context => {
            const purchaseReturn1 = await context.read(xtremPurchasing.nodes.PurchaseReturn, { number: 'RET024' });
            const stockRecord = await context.read(xtremStockData.nodes.Stock, { _id: '94' });
            const purchaseReturnLine11 = await purchaseReturn1.lines.elementAt(0);
            assert.equal((await purchaseReturnLine11.quantityInStockUnit).toString(), '15');
            assert.equal((await purchaseReturnLine11.quantityAllocated).toString(), '0');
            assert.equal(await purchaseReturnLine11.allocationStatus, 'notAllocated');

            await xtremStockData.nodes.Stock.updateAllocations(context, {
                documentLine: purchaseReturnLine11 as any,
                allocationUpdates: [
                    {
                        action: 'create',
                        quantity: 5,
                        stockRecord: stockRecord as any,
                    },
                ],
            });

            const purchaseReturn2 = await context.read(
                xtremPurchasing.nodes.PurchaseReturn,
                { number: 'RET024' },
                { forUpdate: true },
            );
            const purchaseReturnLine21 = await purchaseReturn2.lines.elementAt(0);
            assert.equal((await purchaseReturnLine21.quantityInStockUnit).toString(), '15');
            assert.equal((await purchaseReturnLine21.quantityAllocated).toString(), '5');
            assert.equal(await purchaseReturnLine21.allocationStatus, 'partiallyAllocated');
            assert.equal(await purchaseReturn2.allocationStatus, 'partiallyAllocated');
            await assert.isRejected(purchaseReturn2.$.delete(), 'The record was not deleted.');
            assert.deepEqual(context.diagnoses, [
                {
                    message: 'The allocated quantity must be equal or less than the returned stock quantity.',
                    severity: 3,
                    path: ['lines', '2442'],
                },
            ]);
        }));
});
