import { Test } from '@sage/xtrem-core';
import { assert } from 'chai';
import * as xtremPurchasing from '../../../lib';

describe('Purchase return node - Dimensions managment', () => {
    it.skip('Purchase ret23 - closed document', () =>
        Test.withContext(async context => {
            const ret23 = await context.read(
                xtremPurchasing.nodes.PurchaseReturn,
                { number: 'RET032' },
                { forUpdate: true },
            );
            assert.equal(await ret23.financeIntegrationStatus, 'toBeRecorded');

            const line = await ret23.lines.elementAt(0);

            await ret23.$.set({
                lines: [
                    {
                        _action: 'update',
                        _sortValue: await line._sortValue,
                        storedAttributes: {
                            project: 'AttPROJ',
                            task: '',
                            employee: '',
                            stockSite: 'US001',
                            financialSite: 'US001',
                            item: 'Mu<PERSON><PERSON>',
                        },
                        storedDimensions: { dimensionType03: 'DIMTYPE1VALUE1', dimensionType04: 'DIMTYPE2VALUE2' },
                    },
                ],
            });

            await ret23.$.save();
        }));
});
