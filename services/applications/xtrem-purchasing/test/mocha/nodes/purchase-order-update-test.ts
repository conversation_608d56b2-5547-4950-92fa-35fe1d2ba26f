import { Test } from '@sage/xtrem-core';
import { assert } from 'chai';
import * as xtremPurchasing from '../../../lib';

describe('Purchase order node - UPDATE  ', () => {
    it('Update purchase order update quantity Line ', () =>
        Test.withContext(async context => {
            const orderPo6 = await context.read(
                xtremPurchasing.nodes.PurchaseOrder,
                { number: 'PO6' },
                { forUpdate: true },
            );
            const orderLine = await orderPo6.lines.elementAt(0);
            assert.deepEqual((await orderLine.quantity).toString(), '150');
            await orderLine.$.set({ quantity: 250 });
            assert.isTrue(await orderLine.$.control());
            /* does not throw */ await (() => orderLine.$.save())();

            const readPo6 = await context.read(xtremPurchasing.nodes.PurchaseOrder, { number: 'PO6' });
            assert.deepEqual((await (await readPo6.lines.elementAt(0)).quantity).toString(), '250');
            assert.deepEqual((await (await readPo6.lines.elementAt(0)).quantityInStockUnit).toString(), '250000');
        }));

    it('Update purchase order - Should be possible to add a new line to a confirmed order', () =>
        Test.withContext(async context => {
            const orderPo32 = await context.read(
                xtremPurchasing.nodes.PurchaseOrder,
                { number: 'PO32' },
                { forUpdate: true },
            );
            assert.equal(await orderPo32.approvalStatus, 'confirmed');
            assert.equal(await orderPo32.isApprovalManaged, false);

            await orderPo32.lines.append({
                item: '#3542',
                quantity: 10,
                netPrice: 2,
            });

            await orderPo32.$.save(); // does not throw
        }));
});
