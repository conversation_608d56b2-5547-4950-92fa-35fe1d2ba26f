// tslint:disable:no-duplicate-string
import type { Context, decimal, integer, NodeCreateData, StaticThis } from '@sage/xtrem-core';
import { Test } from '@sage/xtrem-core';
import type * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremStockData from '@sage/xtrem-stock-data';
import { assert } from 'chai';
import * as xtremPurchasing from '../../../index';

describe('PurchaseCreditMemoNode', () => {
    // TODO : Will be better to have the return type on this function and get rid of the any
    async function getResultData(context: Context) {
        const invoiceLine = await context.read(xtremPurchasing.nodes.PurchaseInvoiceLine, {
            _id: 'PI9|261200',
        }); // _id 75 ( was 142 )
        const receiptLine = await (await invoiceLine.purchaseReceiptLine)?.purchaseReceiptLine;
        if (!receiptLine) {
            throw new Error('Receipt line not found');
        }

        const receipt = await receiptLine.document;

        const receiptLines = await receipt.lines
            .map(async line => {
                return { id: line._id, sortValue: await line._sortValue };
            })
            .toArray();
        return {
            receiptLineSysId: receiptLine._id,
            invoiceLineSysId: invoiceLine._id,
            resultData: {
                result: 'requested',
                // InvoiceLine: 2612 -> receiptLine: 1462 -> receipt: 142 => both lines of the receipt are posted: 1462 & 1463
                documents: [{ id: receipt._id, lines: receiptLines }],
            },
        };
    }

    async function getResultDataForReturnLine(context: Context) {
        const returnLine = await context.read(xtremPurchasing.nodes.PurchaseReturnLine, {
            _id: 'RET032|10 ',
        }); // _id 75 ( was 142 )
        const receiptLine = await (await returnLine.purchaseReceiptLine)?.purchaseReceiptLine;
        if (!receiptLine) {
            throw new Error('Receipt line not found');
        }

        const receipt = await receiptLine.document;

        const receiptLines = await receipt.lines
            .map(async line => {
                return { id: line._id, sortValue: await line._sortValue };
            })
            .toArray();
        return {
            receiptLineSysId: receiptLine._id,
            returnLineSysId: returnLine._id,
            resultData: {
                result: 'requested',
                documents: [{ id: receipt._id, lines: receiptLines }],
            },
        };
    }

    async function createPurchaseCreditMemoFromInvoice(
        context: Context,
        originDocumentLine: xtremPurchasing.nodes.PurchaseInvoiceLine | xtremPurchasing.nodes.PurchaseReturnLine,
        data: { quantity: decimal; grossPrice: decimal },
    ) {
        const baseData: NodeCreateData<xtremPurchasing.nodes.PurchaseCreditMemo> = {
            site: await originDocumentLine.site,
            billBySupplier: await (await originDocumentLine.document).billBySupplier,
            currency: await (await originDocumentLine.document).currency,
            totalAmountExcludingTax: data.quantity * data.grossPrice,
            totalTaxAmount: 0,
            reason: 4,
            matchingUser: '#<EMAIL>',
        };
        const creditMemo = await context.create(xtremPurchasing.nodes.PurchaseCreditMemo, baseData);

        let origin: any;
        if (originDocumentLine instanceof xtremPurchasing.nodes.PurchaseInvoiceLine) {
            origin = {
                origin: 'purchaseInvoice',
                purchaseInvoiceLine: {
                    purchaseInvoiceLine: originDocumentLine,
                    unit: await originDocumentLine.unit,
                },
                creditMemoDate: (await (await originDocumentLine.document).invoiceDate).addDays(2),
                billByLinkedAddress: await (await originDocumentLine.document).billByLinkedAddress,
                payToLinkedAddress: await (await originDocumentLine.document).payToLinkedAddress,
            };
        } else {
            origin = {
                origin: 'purchaseReturn',
                purchaseReturnLine: {
                    purchaseReturnLine: originDocumentLine,
                    unit: await originDocumentLine.unit,
                },
                creditMemoDate: (await (await originDocumentLine.document).documentDate).addDays(2),
            };
        }

        await creditMemo.$.set({
            lines: [
                {
                    grossPrice: data.grossPrice,
                    quantity: data.quantity,
                    quantityInStockUnit: data.quantity,

                    item: await originDocumentLine.item,
                    unit: await originDocumentLine.unit,
                    recipientSite: await originDocumentLine.site,
                    currency: await originDocumentLine.currency,
                    ...origin,
                    storedAttributes: await originDocumentLine.storedAttributes,
                    storedDimensions: await originDocumentLine.storedDimensions,
                    uPurchaseUnit: await originDocumentLine.unit,
                    uStockUnit: await originDocumentLine.stockUnit,
                    stockSite: await originDocumentLine.site,
                    stockTransactionStatus: 'draft',
                },
            ],
        });
        return creditMemo;
    }

    async function testCreditMemoStockValueCorrection(
        context: Context,
        testData: {
            creditMemoData: {
                originDocumentClas: StaticThis<
                    xtremPurchasing.nodes.PurchaseInvoiceLine | xtremPurchasing.nodes.PurchaseReturnLine
                >;
                originDocumentLineId: integer;
                quantity: decimal;
                grossPrice: decimal;
            };
            resultData: Omit<
                xtremStockData.interfaces.PostToStockReturnJsonType<xtremStockData.enums.StockMovementTypeEnum.correction>,
                'notificationId'
            >;
            stockCorrectionDetails: {
                _documentLine_constructor: string;
                _correctedStockDetail_constructor: string;
                documentLine: { _id: integer };
                correctedStockDetail: { _id: integer };
                impactedQuantity: decimal;
                amountToAbsorb: decimal;
                reasonCode: { id: string };
            }[];
        },
    ) {
        const originDocumentLine = await context.read(testData.creditMemoData.originDocumentClas, {
            _id: testData.creditMemoData.originDocumentLineId,
        });

        const purchaseCreditMemo: xtremPurchasing.nodes.PurchaseCreditMemo = await createPurchaseCreditMemoFromInvoice(
            context,
            originDocumentLine,
            testData.creditMemoData,
        );
        await purchaseCreditMemo.$.save({ flushDeferredActions: true });

        const postResult = await xtremPurchasing.nodes.PurchaseCreditMemo.post(context, purchaseCreditMemo);
        const stockPostingResult: Omit<
            xtremStockData.interfaces.PostToStockReturnJsonType<xtremStockData.enums.StockMovementTypeEnum.correction>,
            'notificationId'
        > & { notificationId?: string } = JSON.parse(postResult.stockPostingResult);

        // TODO: Check creation of the stock correction details
        if (stockPostingResult.result === 'requested') {
            delete stockPostingResult.notificationId;
            assert.deepEqual(stockPostingResult, testData.resultData);
        }

        await Test.rollbackCache(context);

        const actualCorrectionDetailResults = await context.select(
            xtremStockData.nodes.StockCorrectionDetail,
            {
                correctedStockDetail: { _id: true },
                documentLine: { _id: true },
                amountToAbsorb: true,
                impactedQuantity: true,
                reasonCode: { id: true },
            },
            {
                filter: { documentLine: { _in: testData.stockCorrectionDetails.map(sd => sd.documentLine._id) } },
                orderBy: { correctedStockDetail: { _id: 1 }, _id: 1 },
            },
        );

        assert.deepEqual(actualCorrectionDetailResults.length, testData.stockCorrectionDetails.length);
        assert.deepEqual(actualCorrectionDetailResults, testData.stockCorrectionDetails);
    }

    it('Create and post credit memo against invoice - same quantity, different price', () =>
        Test.withContext(
            async context => {
                const { receiptLineSysId, invoiceLineSysId, resultData } = await getResultData(context);

                await testCreditMemoStockValueCorrection(context, {
                    creditMemoData: {
                        originDocumentClas: xtremPurchasing.nodes.PurchaseInvoiceLine,
                        originDocumentLineId: invoiceLineSysId,
                        quantity: 100.0,
                        grossPrice: 0.3,
                    },
                    resultData: resultData as any,
                    stockCorrectionDetails: [
                        {
                            _correctedStockDetail_constructor: 'StockReceiptDetail',
                            correctedStockDetail: { _id: 1603 },
                            _documentLine_constructor: 'PurchaseReceiptLine',
                            documentLine: { _id: receiptLineSysId },
                            impactedQuantity: 100.0,
                            amountToAbsorb: -30.0,
                            reasonCode: { id: 'Stock value correction' },
                        },
                    ],
                });
            },
            { today: '2023-03-10' },
        ));
    it('Create and post credit memo against invoice - same quantity same price', () =>
        Test.withContext(
            async context => {
                const { receiptLineSysId, invoiceLineSysId, resultData } = await getResultData(context);

                await testCreditMemoStockValueCorrection(context, {
                    creditMemoData: {
                        originDocumentClas: xtremPurchasing.nodes.PurchaseInvoiceLine,
                        originDocumentLineId: invoiceLineSysId,
                        quantity: 100,
                        grossPrice: 121,
                    },
                    resultData: resultData as any,
                    stockCorrectionDetails: [
                        {
                            _correctedStockDetail_constructor: 'StockReceiptDetail',
                            correctedStockDetail: { _id: 1603 },
                            _documentLine_constructor: 'PurchaseReceiptLine',
                            documentLine: { _id: receiptLineSysId },
                            impactedQuantity: 100.0,
                            amountToAbsorb: -100.0, // 100 * (120 - 121)
                            reasonCode: { id: 'Stock value correction' },
                        },
                    ],
                });
            },
            { today: '2023-03-10' },
        ));
    it('Create and post credit memo against invoice - different quantity same price', () =>
        Test.withContext(
            async context => {
                const { receiptLineSysId, invoiceLineSysId, resultData } = await getResultData(context);
                await testCreditMemoStockValueCorrection(context, {
                    creditMemoData: {
                        originDocumentClas: xtremPurchasing.nodes.PurchaseInvoiceLine,
                        originDocumentLineId: invoiceLineSysId,
                        quantity: 80,
                        grossPrice: 121,
                    },
                    resultData: resultData as any,
                    stockCorrectionDetails: [
                        {
                            _correctedStockDetail_constructor: 'StockReceiptDetail',
                            correctedStockDetail: { _id: 1603 },
                            _documentLine_constructor: 'PurchaseReceiptLine',
                            documentLine: { _id: receiptLineSysId },
                            impactedQuantity: 80.0,
                            amountToAbsorb: -80.0, // 80 * (120 - 121)
                            reasonCode: { id: 'Stock value correction' },
                        },
                    ],
                });
            },
            { today: '2023-03-10' },
        ));
    it('Create and post credit memo against invoice - different quantity different price', () =>
        Test.withContext(
            async context => {
                const { receiptLineSysId, invoiceLineSysId, resultData } = await getResultData(context);

                await testCreditMemoStockValueCorrection(context, {
                    creditMemoData: {
                        originDocumentClas: xtremPurchasing.nodes.PurchaseInvoiceLine,
                        originDocumentLineId: invoiceLineSysId,
                        quantity: 70,
                        grossPrice: 0.2,
                    },
                    resultData: resultData as any,
                    stockCorrectionDetails: [
                        {
                            _correctedStockDetail_constructor: 'StockReceiptDetail',
                            correctedStockDetail: { _id: 1603 },
                            _documentLine_constructor: 'PurchaseReceiptLine',
                            documentLine: { _id: receiptLineSysId },
                            impactedQuantity: 70.0,
                            amountToAbsorb: -14.0, // - 70 * 0.2
                            reasonCode: { id: 'Stock value correction' },
                        },
                    ],
                });
            },
            { today: '2023-03-10' },
        ));
    it('Create and post credit memo against purchase return - same quantity different price', () =>
        Test.withContext(
            async context => {
                const { receiptLineSysId, returnLineSysId, resultData } = await getResultDataForReturnLine(context);

                await testCreditMemoStockValueCorrection(context, {
                    creditMemoData: {
                        originDocumentClas: xtremPurchasing.nodes.PurchaseReturnLine,
                        originDocumentLineId: returnLineSysId, // RET032|10
                        quantity: 50,
                        grossPrice: 0.5,
                    },
                    resultData: resultData as any,
                    stockCorrectionDetails: [
                        {
                            _correctedStockDetail_constructor: 'StockReceiptDetail',
                            correctedStockDetail: { _id: 1604 },
                            _documentLine_constructor: 'PurchaseReceiptLine',
                            documentLine: { _id: receiptLineSysId },
                            impactedQuantity: 37.5,
                            amountToAbsorb: -18.75, // - 50 * 0.5 * (150 / (150+50))
                            reasonCode: { id: 'Stock value correction' },
                        },
                        {
                            _correctedStockDetail_constructor: 'StockReceiptDetail',
                            correctedStockDetail: { _id: 1605 },
                            _documentLine_constructor: 'PurchaseReceiptLine',
                            documentLine: { _id: receiptLineSysId },
                            impactedQuantity: 12.5,
                            amountToAbsorb: -6.25, // - 50 * 0.5 * (50 / (150+50))
                            reasonCode: { id: 'Stock value correction' },
                        },
                    ],
                });
            },
            { today: '2023-03-10' },
        ));
    it('Create and post credit memo against purchase return - same quantity same price', () =>
        Test.withContext(
            async context => {
                const { returnLineSysId, resultData } = await getResultDataForReturnLine(context);

                await testCreditMemoStockValueCorrection(context, {
                    creditMemoData: {
                        originDocumentClas: xtremPurchasing.nodes.PurchaseReturnLine,
                        originDocumentLineId: returnLineSysId,
                        quantity: 50,
                        grossPrice: 75,
                    },
                    resultData: resultData as any,
                    stockCorrectionDetails: [], // return + same price -> no correction needed,
                });
            },
            { today: '2023-03-10' },
        ));
    it('Create and post credit memo against purchase return - different quantity same price', () =>
        Test.withContext(
            async context => {
                const { returnLineSysId, resultData } = await getResultDataForReturnLine(context);

                await testCreditMemoStockValueCorrection(context, {
                    creditMemoData: {
                        originDocumentClas: xtremPurchasing.nodes.PurchaseReturnLine,
                        originDocumentLineId: returnLineSysId,
                        quantity: 30,
                        grossPrice: 75,
                    },
                    resultData: resultData as any,
                    stockCorrectionDetails: [], // return + same price -> no correction needed,
                });
            },
            { today: '2023-03-10' },
        ));
    it('Create and post credit memo against purchase return - different quantity different price', () =>
        Test.withContext(
            async context => {
                const { receiptLineSysId, returnLineSysId, resultData } = await getResultDataForReturnLine(context);
                await testCreditMemoStockValueCorrection(context, {
                    creditMemoData: {
                        originDocumentClas: xtremPurchasing.nodes.PurchaseReturnLine,
                        originDocumentLineId: returnLineSysId,
                        quantity: 30,
                        grossPrice: 0.3,
                    },
                    resultData: resultData as any,
                    stockCorrectionDetails: [
                        {
                            _correctedStockDetail_constructor: 'StockReceiptDetail',
                            correctedStockDetail: { _id: 1604 },
                            _documentLine_constructor: 'PurchaseReceiptLine',
                            documentLine: { _id: receiptLineSysId },
                            impactedQuantity: 22.5,
                            amountToAbsorb: -6.75, // - 30 * 0.3 * (150 / (150+50))
                            reasonCode: { id: 'Stock value correction' },
                        },
                        {
                            _correctedStockDetail_constructor: 'StockReceiptDetail',
                            correctedStockDetail: { _id: 1605 },
                            _documentLine_constructor: 'PurchaseReceiptLine',
                            documentLine: { _id: receiptLineSysId },
                            impactedQuantity: 7.5,
                            amountToAbsorb: -2.25, // - 30 * 0.3 * (50 / (150+50))
                            reasonCode: { id: 'Stock value correction' },
                        },
                    ],
                });
            },
            { today: '2023-03-10' },
        ));

    it('Create purchase credit memo and repost', () =>
        Test.withContext(
            async context => {
                const { receiptLineSysId, invoiceLineSysId, resultData } = await getResultData(context);
                await testCreditMemoStockValueCorrection(context, {
                    creditMemoData: {
                        originDocumentClas: xtremPurchasing.nodes.PurchaseInvoiceLine,
                        originDocumentLineId: invoiceLineSysId,
                        quantity: 100,
                        grossPrice: 121,
                    },
                    resultData: resultData as any,
                    stockCorrectionDetails: [
                        {
                            _correctedStockDetail_constructor: 'StockReceiptDetail',
                            correctedStockDetail: { _id: 1603 },
                            _documentLine_constructor: 'PurchaseReceiptLine',
                            documentLine: { _id: receiptLineSysId },
                            impactedQuantity: 100.0,
                            amountToAbsorb: -100.0, // 100 * (120 - 121)
                            reasonCode: { id: 'Stock value correction' },
                        },
                    ],
                });

                const purchaseCreditMemo = await context
                    .query(xtremPurchasing.nodes.PurchaseCreditMemo, {
                        last: 1,
                    })
                    .elementAt(0);

                const purchaseCreditMemoLines = await purchaseCreditMemo.lines
                    .map(async line => {
                        return {
                            baseDocumentLineSysId: line._id,
                            storedAttributes:
                                (await line.storedAttributes) as xtremMasterData.interfaces.StoredAttributes,
                            storedDimensions: (await line.storedDimensions) || {},
                        };
                    })
                    .toArray();

                await assert.isRejected(
                    xtremPurchasing.nodes.PurchaseCreditMemo.repost(context, purchaseCreditMemo, {
                        header: { supplierDocumentNumber: 'SUPDOC001' },
                        lines: purchaseCreditMemoLines,
                    }),
                    "You can only repost a purchase credit memo if the status is 'Failed.'",
                );
            },
            { today: '2023-03-10' },
        ));
});

describe('Posting details', () => {
    it('Gets posting details successfully', () =>
        Test.withContext(async context => {
            const pcmDocumentNumber = 'PC240002';
            const pcmDocument = await context.read(xtremPurchasing.nodes.PurchaseCreditMemo, {
                number: pcmDocumentNumber,
            });
            await xtremPurchasing.nodes.PurchaseCreditMemo.post(context, pcmDocument);

            assert.equal(await pcmDocument.postingDetails.length, 1);
            const apInvoice = await pcmDocument.postingDetails.at(0);

            assert.equal(await (await (await apInvoice?.paymentTracking)?.document)?.number, pcmDocumentNumber);
            assert.equal(await apInvoice?.documentType, 'purchaseCreditMemo');
            assert.equal(await apInvoice?.documentNumber, pcmDocumentNumber);
            assert.equal(await apInvoice?.documentSysId, pcmDocument._id);
            assert.equal(await apInvoice?.targetDocumentType, 'accountsPayableInvoice');
        }));
});

describe('Discount before payment date control', () => {
    it('Failed control due to discountPaymentBeforeDate being after the due date', () =>
        Test.withContext(async (context: Context) => {
            const purchaseCreditMemo = await context.read(
                xtremPurchasing.nodes.PurchaseCreditMemo,
                { number: 'PC240002' },
                { forUpdate: true },
            );
            const dueDate = await purchaseCreditMemo.dueDate;
            // Update the discountPaymentBeforeDate to be after the due date
            await purchaseCreditMemo.$.set({ paymentTracking: { discountPaymentBeforeDate: dueDate.addDays(1) } });
            await assert.isRejected(
                xtremPurchasing.nodes.PurchaseCreditMemo.post(context, purchaseCreditMemo),
                'The discount date needs to be on or before the due date.',
            );
        }));
});
