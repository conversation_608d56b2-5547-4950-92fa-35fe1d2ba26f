import { Test } from '@sage/xtrem-core';
import * as xtremStockData from '@sage/xtrem-stock-data';
import { assert } from 'chai';
import * as xtremPurchasing from '../../../lib';

describe('Purchase return node - issue', () => {
    it('purchase return - issue all lines', () =>
        Test.withContext(async context => {
            let purchaseReturn = await context.read(xtremPurchasing.nodes.PurchaseReturn, { number: 'RET030' });
            const stockRecord = await context.read(xtremStockData.nodes.Stock, { _id: '32' });
            let purchaseReturnLine1 = await purchaseReturn.lines.elementAt(0);
            const purchaseReturnLine2 = await purchaseReturn.lines.elementAt(1);
            const documentId = purchaseReturn._id;

            assert.equal((await purchaseReturnLine1.quantityInStockUnit).toString(), '10000');
            assert.equal((await purchaseReturnLine1.quantityAllocated).toString(), '0');
            assert.equal(await purchaseReturnLine1.allocationStatus, 'notAllocated');

            assert.equal((await purchaseReturnLine2.quantityInStockUnit).toString(), '15000');
            assert.equal((await purchaseReturnLine2.quantityAllocated).toString(), '0');
            assert.equal(await purchaseReturnLine2.allocationStatus, 'notManaged');

            assert.equal(await purchaseReturn.allocationStatus, 'notAllocated');

            await xtremStockData.nodes.Stock.updateAllocations(context, {
                documentLine: purchaseReturnLine1 as any,
                allocationUpdates: [
                    {
                        action: 'create',
                        quantity: await purchaseReturnLine1.quantityInStockUnit,
                        stockRecord: stockRecord as any,
                    },
                ],
            });

            // rollbackCache function will flush deferred actions and saves
            await Test.rollbackCache(context);

            purchaseReturn = await context.read(
                xtremPurchasing.nodes.PurchaseReturn,
                { _id: documentId },
                { forUpdate: true },
            );
            purchaseReturnLine1 = await purchaseReturn.lines.elementAt(0);

            assert.equal((await purchaseReturnLine1.quantityInStockUnit).toString(), '10000');
            assert.equal((await purchaseReturnLine1.quantityAllocated).toString(), '10000');
            assert.equal(await purchaseReturnLine1.allocationStatus, 'allocated');
            assert.equal(await purchaseReturn.allocationStatus, 'allocated');

            const notified = await xtremPurchasing.nodes.PurchaseReturn.post(context, purchaseReturn);
            assert.equal(notified, true);

            if (!Test.hacks.isNodeStateUpdatable(purchaseReturnLine1)) {
                await purchaseReturnLine1.$.save();
            }

            // rollbackCache function will flush deferred actions and saves
            await Test.rollbackCache(context);

            await xtremStockData.functions.testLib.simulateStockTransaction(
                context,
                documentId,
                xtremPurchasing.nodes.PurchaseReturn,
                'issue',
                xtremPurchasing.nodes.PurchaseReturn.onStockReply,
                { stockUpdateResultStatus: 'decreased' },
            );

            // rollbackCache function will flush deferred actions and saves
            await Test.rollbackCache(context);

            purchaseReturn = await context.read(xtremPurchasing.nodes.PurchaseReturn, { _id: documentId });

            assert.equal(await purchaseReturn.stockTransactionStatus, 'completed', 'stockTransactionStatus incorrect');
            assert.equal(await purchaseReturn.shippingStatus, 'shipped', 'shippingStatus incorrect');
            assert.equal(await purchaseReturn.status, 'closed', 'Global status incorrect');
        }));

    // This unit test concern the valuation function of the return. They are triggered by stock engine when posting
    it('Test getStockCost for return linked to a receipt', () =>
        Test.withContext(async context => {
            const returnLine = await context.read(xtremPurchasing.nodes.PurchaseReturnLine, { _id: '#RET031|2' });
            const orderCost = await returnLine.getOrderCost();
            const valuedCost = await returnLine.getValuedCost();

            assert.equal(Number(orderCost), 123.45);
            assert.equal(Number(valuedCost), 100);
        }));

    it('Check onceStockCompleted is called only when the document is completed', () =>
        Test.withContext(async context => {
            const result = await xtremStockData.functions.testLib.testOnceStockCompleted(context, {
                clas: xtremPurchasing.nodes.PurchaseReturn,
                movementType: 'issue',
                documents: [
                    { key: { number: 'RET001' }, isCompleted: false },
                    { key: { number: 'RET002' }, isCompleted: true },
                    { key: { number: 'RET003' }, isCompleted: false },
                    { key: { number: 'RET004' }, isCompleted: true },
                ],
                returnedProperty: 'number',
            });

            assert.deepEqual(result, ['RET002', 'RET004']);
        }));

    it('Check writeable context to lock stock transaction to progress updates', () =>
        Test.withContext(async context => {
            const number = 'RET033';
            await xtremStockData.testHelpers.testPostToStockSetInProgress(context, {
                documentNode: xtremPurchasing.nodes.PurchaseReturn,
                number,
                assert,
                postToStock: xtremPurchasing.nodes.PurchaseReturn.postToStock,
            });

            await Test.rollbackCache(context);

            const purchaseReturn = await context.read(xtremPurchasing.nodes.PurchaseReturn, { number });
            // after the call to postToStock, the document must be 'inProgress' status'
            assert.equal(await purchaseReturn.status, 'inProgress');
        }));
});

describe('Posting details', () => {
    // TODO:
    it.skip('Gets posting details successfully', () =>
        Test.withContext(async context => {
            const number = 'PT250001';
            const prDocument = await context.read(xtremPurchasing.nodes.PurchaseReturn, { number });
            assert.equal(await prDocument.postingDetails.length, 1);

            const journalEntry = await prDocument.postingDetails.at(0);
            assert.equal(await journalEntry?.documentType, 'purchaseReturn');
            assert.equal(await journalEntry?.documentNumber, number);
            assert.equal(await journalEntry?.documentSysId, prDocument._id);
            assert.equal(await journalEntry?.targetDocumentType, 'journalEntry');
        }));
});
