import { Test } from '@sage/xtrem-core';
import * as xtremSystem from '@sage/xtrem-system';
import { assert } from 'chai';
import * as xtremPurchasing from '../../../lib/index';

describe('Purchase requisition node ', () => {
    it(' add lines - without item reference', () =>
        Test.withContext(async context => {
            const site = await context.read(xtremSystem.nodes.Site, { _id: '#US003' }, { forUpdate: true });
            await site.$.set({ isPurchaseRequisitionApprovalManaged: false });
            await site.$.save();

            const req26 = await context.read(
                xtremPurchasing.nodes.PurchaseRequisition,
                { _id: '#REQ26' },
                { forUpdate: true },
            );

            assert.equal(await req26.status, 'pending');

            await req26.lines.append({
                requestedItemDescription: 'Dummy',
                unit: '#EACH',
                unitToStockUnitConversionFactor: 1,
                quantity: 1,
                quantityInStockUnit: 1,
                status: 'pending',
                supplier: '#LECLERC',
                item: 'Chemical C',
            });

            assert.equal(await req26.status, 'pending');

            const itemAdded = await req26.lines.some(async line => (await line.item) != null);

            assert.isTrue(itemAdded);
        }));
});
