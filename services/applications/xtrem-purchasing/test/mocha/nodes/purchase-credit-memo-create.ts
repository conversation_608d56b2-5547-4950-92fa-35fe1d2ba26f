import type { NodeCreateData } from '@sage/xtrem-core';
import { Test } from '@sage/xtrem-core';
import { DateValue } from '@sage/xtrem-date-time';
import * as xtremMasterData from '@sage/xtrem-master-data';
import { ValidationSeverity } from '@sage/xtrem-shared';
import * as xtremSystem from '@sage/xtrem-system';
import { assert } from 'chai';
import * as xtremPurchasing from '../../../index';

describe('PurchaseCreditMemoNode', () => {
    it('Create purchasing invoice with purchasing site not belonging to legal company - create 2 lines with 2 different receiving sites.', () =>
        Test.withContext(
            async context => {
                const newPurchaseInvoice = await context.create(xtremPurchasing.nodes.PurchaseCreditMemo, {
                    site: 'US007',
                    billBySupplier: '#US017',
                    currency: '#EUR',
                    supplierDocumentNumber: '123',
                    totalAmountExcludingTax: 20,
                    totalTaxAmount: 5,
                    matchingUser: '<EMAIL>',
                    reason: 'R1',
                    lines: [
                        {
                            item: '#Service001',
                            site: '#US007',
                            stockTransactionStatus: 'draft',
                            quantity: 10,
                            grossPrice: 110,
                            unit: '#PAIR',
                            stockUnit: '#PAIR',
                        },
                    ],
                });
                await assert.isRejected(newPurchaseInvoice.$.save());
                assert.deepEqual(newPurchaseInvoice.$.context.diagnoses, [
                    {
                        path: ['lines', '-1000000002', 'recipientSite'],
                        message: 'The record is not valid. You need to select a different record.',
                        severity: 3,
                    },
                    {
                        path: ['lines', '-1000000002'],
                        message: 'The item: Service001, is not managed for the site: US007.',
                        severity: 3,
                    },
                    {
                        path: ['lines', '-1000000002'],
                        message: 'The item: Service001, is not managed for the supplier: US017.',
                        severity: 2,
                    },
                ]);
            },
            { today: '2023-01-17' },
        ));
    it('Ensure purchase credit memos with different supplier document numbers can be saved', () =>
        Test.withContext(async context => {
            // Purchase Credit Memo A.
            const supplierDocumentNumberA = '123';
            const purchaseCreditMemoDataA: NodeCreateData<xtremPurchasing.nodes.PurchaseCreditMemo> = {
                site: '#US001',
                billBySupplier: '#US017',
                currency: '#EUR',
                supplierDocumentNumber: supplierDocumentNumberA,
                totalAmountExcludingTax: 20,
                totalTaxAmount: 5,
                reason: '#R1',
                lines: [],
                matchingUser: '#<EMAIL>',
            };
            const purchaseCreditMemoA = await context.create(
                xtremPurchasing.nodes.PurchaseCreditMemo,
                purchaseCreditMemoDataA,
            );
            await purchaseCreditMemoA.$.save({ flushDeferredActions: true });

            // Purchase Credit Memo B.
            const supplierDocumentNumberB = '456';
            const purchaseCreditMemoDataB: NodeCreateData<xtremPurchasing.nodes.PurchaseCreditMemo> = {
                site: '#US001',
                billBySupplier: '#US017',
                currency: '#EUR',
                supplierDocumentNumber: supplierDocumentNumberB,
                totalAmountExcludingTax: 20,
                totalTaxAmount: 5,
                reason: 'R1',
                lines: [],
                matchingUser: '<EMAIL>',
            };
            const purchaseCreditMemoB = await context.create(
                xtremPurchasing.nodes.PurchaseCreditMemo,
                purchaseCreditMemoDataB,
            );

            await assert.isFulfilled(purchaseCreditMemoB.$.save({ flushDeferredActions: true }));
        }));

    it('Ensure purchase credit memos with equal supplier document numbers cannot be saved', () =>
        Test.withContext(async context => {
            const supplierDocumentNumber = '123';

            // Purchase Credit Memo A.
            const purchaseCreditMemoDataA: NodeCreateData<xtremPurchasing.nodes.PurchaseCreditMemo> = {
                site: '#US001',
                billBySupplier: '#US017',
                currency: '#EUR',
                supplierDocumentNumber,
                totalAmountExcludingTax: 20,
                totalTaxAmount: 5,
                reason: 'R1',
                lines: [],
                matchingUser: '<EMAIL>',
            };
            const purchaseCreditMemoA = await context.create(
                xtremPurchasing.nodes.PurchaseCreditMemo,
                purchaseCreditMemoDataA,
            );
            await purchaseCreditMemoA.$.save({ flushDeferredActions: true });

            // Purchase Credit Memo B.
            const purchaseCreditMemoDataB: NodeCreateData<xtremPurchasing.nodes.PurchaseCreditMemo> = {
                site: '#US001',
                billBySupplier: '#US017',
                currency: '#EUR',
                supplierDocumentNumber,
                totalAmountExcludingTax: 20,
                totalTaxAmount: 5,
                reason: 'R1',
                lines: [],
                matchingUser: '<EMAIL>',
            };
            const purchaseCreditMemoB = await context.create(
                xtremPurchasing.nodes.PurchaseCreditMemo,
                purchaseCreditMemoDataB,
            );
            await purchaseCreditMemoB.$.save({ flushDeferredActions: true });

            assert.deepStrictEqual(purchaseCreditMemoB.$.context.diagnoses, [
                {
                    severity: ValidationSeverity.warn,
                    path: [],
                    message: 'Supplier document number already exists.',
                },
            ]);
        }));

    it('Note propagation - Credit memo creation for PI8 isTransferHeaderNote=true, isTransferLineNote=true', () =>
        Test.withContext(async context => {
            const reasonCode = await context.read(xtremMasterData.nodes.ReasonCode, { _id: '#Decrease quantity' });
            const purchaseInvoice = await context.read(
                xtremPurchasing.nodes.PurchaseInvoice,
                { number: 'PI8' },
                { forUpdate: true },
            );
            assert.isTrue(await purchaseInvoice.isTransferHeaderNote);
            assert.isTrue(await purchaseInvoice.isTransferLineNote);

            const creditMemo = await xtremPurchasing.nodes.PurchaseInvoice.createCreditMemoFromInvoice(
                context,
                purchaseInvoice,
                reasonCode,
                100.0,
                DateValue.make(2022, 9, 1),
            );

            const purchaseCreditMemo = await context.read(xtremPurchasing.nodes.PurchaseCreditMemo, {
                _id: creditMemo._id,
            });
            const creditMemoInternalNote = (await purchaseCreditMemo.internalNote).toString();

            assert.isNotEmpty(creditMemoInternalNote);
            assert.equal(creditMemoInternalNote, (await purchaseInvoice.internalNote).toString());

            const creditMemoLine1InternalNote = (
                await (
                    await purchaseCreditMemo.lines.elementAt(0)
                ).internalNote
            ).toString();

            assert.isNotEmpty(creditMemoLine1InternalNote);
            assert.equal(
                creditMemoLine1InternalNote,
                (await (await purchaseInvoice.lines.elementAt(0)).internalNote).toString(),
            );
        }));
    it('Note propagation - Credit memo creation for PI9 isTransferHeaderNote=true, isTransferLineNote=false', () =>
        Test.withContext(async context => {
            const reasonCode = await context.read(xtremMasterData.nodes.ReasonCode, { _id: '#Decrease quantity' });
            const purchaseInvoice = await context.read(
                xtremPurchasing.nodes.PurchaseInvoice,
                { number: 'PI9' },
                { forUpdate: true },
            );
            assert.isTrue(await purchaseInvoice.isTransferHeaderNote);
            assert.isFalse(await purchaseInvoice.isTransferLineNote);

            const creditMemo = await xtremPurchasing.nodes.PurchaseInvoice.createCreditMemoFromInvoice(
                context,
                purchaseInvoice,
                reasonCode,
                100.0,
                DateValue.make(2022, 9, 1),
            );

            const purchaseCreditMemo = await context.read(xtremPurchasing.nodes.PurchaseCreditMemo, {
                _id: creditMemo._id,
            });
            const creditMemoInternalNote = (await purchaseCreditMemo.internalNote).toString();

            assert.isNotEmpty(creditMemoInternalNote);
            assert.equal(creditMemoInternalNote, (await purchaseInvoice.internalNote).toString());

            const creditMemoLine1InternalNote = (
                await (
                    await purchaseCreditMemo.lines.elementAt(0)
                ).internalNote
            ).toString();

            assert.isEmpty(creditMemoLine1InternalNote);
            assert.notEqual(
                creditMemoLine1InternalNote,
                (await (await purchaseInvoice.lines.elementAt(0)).internalNote).toString(),
            );
        }));
    it('Note propagation - Credit memo creation for PI10 isTransferHeaderNote=false, isTransferLineNote=true', () =>
        Test.withContext(async context => {
            const reasonCode = await context.read(xtremMasterData.nodes.ReasonCode, { _id: '#Decrease quantity' });
            const purchaseInvoice = await context.read(
                xtremPurchasing.nodes.PurchaseInvoice,
                { number: 'PI10' },
                { forUpdate: true },
            );
            assert.isFalse(await purchaseInvoice.isTransferHeaderNote);
            assert.isTrue(await purchaseInvoice.isTransferLineNote);

            const creditMemo = await xtremPurchasing.nodes.PurchaseInvoice.createCreditMemoFromInvoice(
                context,
                purchaseInvoice,
                reasonCode,
                100.0,
                DateValue.make(2022, 9, 1),
            );

            const purchaseCreditMemo = await context.read(xtremPurchasing.nodes.PurchaseCreditMemo, {
                _id: creditMemo._id,
            });
            const creditMemoInternalNote = (await purchaseCreditMemo.internalNote).toString();
            assert.isEmpty(creditMemoInternalNote);
            assert.notEqual(creditMemoInternalNote, (await purchaseInvoice.internalNote).toString());

            const creditMemoLine1InternalNote = (
                await (
                    await purchaseCreditMemo.lines.elementAt(0)
                ).internalNote
            ).toString();

            assert.isNotEmpty(creditMemoLine1InternalNote);
            assert.equal(
                creditMemoLine1InternalNote,
                (await (await purchaseInvoice.lines.elementAt(0)).internalNote).toString(),
            );
        }));
    it('Note propagation - Credit memo creation for PI3 isTransferHeaderNote=false, isTransferLineNote=false', () =>
        Test.withContext(async context => {
            const reasonCode = await context.read(xtremMasterData.nodes.ReasonCode, { _id: '#Decrease quantity' });
            const purchaseInvoice = await context.read(
                xtremPurchasing.nodes.PurchaseInvoice,
                { number: 'PI3' },
                { forUpdate: true },
            );
            assert.isFalse(await purchaseInvoice.isTransferHeaderNote);
            assert.isFalse(await purchaseInvoice.isTransferLineNote);

            const creditMemo = await xtremPurchasing.nodes.PurchaseInvoice.createCreditMemoFromInvoice(
                context,
                purchaseInvoice,
                reasonCode,
                100.0,
                DateValue.make(2022, 9, 1),
            );

            const purchaseCreditMemo = await context.read(xtremPurchasing.nodes.PurchaseCreditMemo, {
                _id: creditMemo._id,
            });
            const creditMemoInternalNote = (await purchaseCreditMemo.internalNote).toString();
            // Must bue the same as the supplier internal note
            assert.isEmpty(creditMemoInternalNote);
            assert.notEqual(creditMemoInternalNote, (await purchaseInvoice.internalNote).toString());

            const creditMemoLine1InternalNote = (
                await (
                    await purchaseCreditMemo.lines.elementAt(0)
                ).internalNote
            ).toString();

            assert.isEmpty(creditMemoLine1InternalNote);
            assert.notEqual(
                creditMemoLine1InternalNote,
                (await (await purchaseInvoice.lines.elementAt(0)).internalNote).toString(),
            );
        }));
    it('Note propagation - Create credit memo with an purchase return line', () =>
        Test.withContext(async context => {
            const supplierDocumentNumberA = '123';
            const purchaseCreditMemoDataA: NodeCreateData<xtremPurchasing.nodes.PurchaseCreditMemo> = {
                site: '#US001',
                billBySupplier: '#US016',
                currency: '#USD',
                supplierDocumentNumber: supplierDocumentNumberA,
                totalAmountExcludingTax: 5500,
                totalTaxAmount: 5500,
                reason: 'R1',
                lines: [
                    {
                        _action: 'create',
                        item: '#RSV9',
                        grossPrice: 110,
                        unit: '#PAIR',
                        quantity: 50,
                        site: '#US001',
                        stockUnit: '#PAIR',
                        purchaseReturnLine: { purchaseReturnLine: '#RET032|10' },
                    },
                ],
                matchingUser: '<EMAIL>',
            };
            const purchaseCreditMemoA = await context.create(
                xtremPurchasing.nodes.PurchaseCreditMemo,
                purchaseCreditMemoDataA,
            );
            await purchaseCreditMemoA.$.save({ flushDeferredActions: true });
            const purchaseReturn = await context.read(xtremPurchasing.nodes.PurchaseReturn, { _id: '#RET032' });

            assert.equal(
                (await purchaseCreditMemoA.internalNote).toString(),
                (await purchaseReturn.internalNote).toString(),
            );
            assert.equal(await purchaseCreditMemoA.isTransferHeaderNote, await purchaseReturn.isTransferHeaderNote);
            assert.equal(await purchaseCreditMemoA.isTransferLineNote, await purchaseReturn.isTransferLineNote);

            assert.equal(
                (await (await purchaseCreditMemoA.lines.elementAt(0)).internalNote).toString(),
                (await (await purchaseReturn.lines.elementAt(0)).internalNote).toString(),
            );
        }));

    it('Create credit memo with default dimensions', () =>
        Test.withContext(async context => {
            const supplierDocumentNumberA = 'CM00000123';
            const purchaseCreditMemoDataA: NodeCreateData<xtremPurchasing.nodes.PurchaseCreditMemo> = {
                site: '#US001',
                billBySupplier: '#US016',
                currency: '#USD',
                supplierDocumentNumber: supplierDocumentNumberA,
                totalAmountExcludingTax: 5500,
                totalTaxAmount: 5500,
                reason: 'R1',
                lines: [
                    {
                        _action: 'create',
                        item: '#RSV9',
                        grossPrice: 110,
                        unit: '#PAIR',
                        quantity: 50,
                        site: '#US001',
                        stockUnit: '#PAIR',
                        purchaseReturnLine: { purchaseReturnLine: '#RET032|10' },
                    },
                ],
                matchingUser: '<EMAIL>',
            };
            const purchaseCreditMemoA = await context.create(
                xtremPurchasing.nodes.PurchaseCreditMemo,
                purchaseCreditMemoDataA,
            );
            await purchaseCreditMemoA.$.save({ flushDeferredActions: true });

            // default dimensions and attributes
            const expectedAttributes = { project: 'AttPROJ', task: 'Task1' };
            const expectedDimensions = {
                dimensionType01: '300',
                dimensionType02: 'CHANNELVALUE1',
            };
            const attributesLine1 = await (await purchaseCreditMemoA.lines.elementAt(0)).storedAttributes;
            const dimensionsLine1 = await (await purchaseCreditMemoA.lines.elementAt(0)).storedDimensions;
            assert.equal(JSON.stringify(attributesLine1), JSON.stringify(expectedAttributes));
            assert.deepEqual(dimensionsLine1, expectedDimensions);
        }));

    it('Should reject purchase credit memo creation when the site is not valid for purchasing', () =>
        Test.withContext(
            async context => {
                const supplierDocumentNumber = 'CM00000123';
                const purchaseCreditMemoData: NodeCreateData<xtremPurchasing.nodes.PurchaseCreditMemo> = {
                    site: '#US001',
                    billBySupplier: '#US016',
                    currency: '#USD',
                    supplierDocumentNumber,
                    totalAmountExcludingTax: 5500,
                    totalTaxAmount: 5500,
                    reason: 'R1',
                    lines: [
                        {
                            _action: 'create',
                            item: '#RSV9',
                            grossPrice: 110,
                            unit: '#PAIR',
                            quantity: 50,
                            site: '#US001',
                            recipientSite: '#US001',
                            stockUnit: '#PAIR',
                            purchaseReturnLine: { purchaseReturnLine: '#RET032|10' },
                        },
                    ],
                    matchingUser: '<EMAIL>',
                };
                const purchaseCreditMemo = await context.create(
                    xtremPurchasing.nodes.PurchaseCreditMemo,
                    purchaseCreditMemoData,
                );
                await context.flushDeferredActions();

                const originalSite = await context.read(xtremSystem.nodes.Site, { id: 'US001' }, { forUpdate: true });
                const firstCreditMemoLine = await purchaseCreditMemo.lines.elementAt(0);
                assert.deepEqual(
                    (await firstCreditMemoLine.stockSite)._id,
                    originalSite._id,
                    'Stock site should match the original site',
                );
                assert.deepEqual(
                    (await firstCreditMemoLine.recipientSite)._id,
                    originalSite._id,
                    'Recipient site should match the original site',
                );

                // Modify the site to make it invalid for purchasing
                await originalSite.$.set({ isPurchase: false, isInventory: false });
                await originalSite.$.save();

                const invalidPurchaseCreditMemo = await context.create(
                    xtremPurchasing.nodes.PurchaseCreditMemo,
                    purchaseCreditMemoData,
                );
                await assert.isRejected(
                    invalidPurchaseCreditMemo.$.save(),
                    'The record was not created.',
                    'Saving a credit memo with an invalid site should be rejected',
                );

                const expectedErrorMessage = 'The receiving site needs to be either stock or purchasing.';
                const hasExpectedError = invalidPurchaseCreditMemo.$.context.diagnoses.some(
                    diagnosis => diagnosis.message === expectedErrorMessage,
                );
                assert.isTrue(
                    hasExpectedError,
                    `The diagnoses should include the error message: "${expectedErrorMessage}"`,
                );
            },
            { today: '2022-02-02' },
        ));
});
