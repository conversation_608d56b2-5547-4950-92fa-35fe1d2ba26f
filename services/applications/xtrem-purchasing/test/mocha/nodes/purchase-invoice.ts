// tslint:disable:no-duplicate-string
import type { Context, NodeCreateData } from '@sage/xtrem-core';
import { Test, date } from '@sage/xtrem-core';
import * as xtremFinanceData from '@sage/xtrem-finance-data';
import { assert } from 'chai';
import * as xtremPurchasing from '../../../index';

describe('PurchaseInvoiceNode', () => {
    function createPurchaseInvoice(context: Context, invoiceDate: date, taxReference = '#FR002') {
        const baseData: NodeCreateData<xtremPurchasing.nodes.PurchaseInvoice> = {
            site: '#ETS1-S01',
            billBySupplier: '#US017',
            currency: '#EUR',
            invoiceDate,
            matchingStatus: 'variance',
            supplierDocumentNumber: '123',
            totalAmountExcludingTax: 28,
            totalTaxAmount: 8,
            lines: [
                {
                    _action: 'create',
                    item: '#NonStockManagedItem',
                    quantity: 1,
                    unit: '#KILOGRAM',
                    grossPrice: 100,
                    taxes: [
                        {
                            _action: 'create',
                            taxReference,
                            taxRate: 0,
                        },
                    ],
                },
            ],
            matchingUser: '#<EMAIL>',
        };
        return context.create(xtremPurchasing.nodes.PurchaseInvoice, baseData);
    }
    it('Control on purchase invoice with wrong tax type.', () =>
        Test.withContext(
            async context => {
                const purchaseInvoice: xtremPurchasing.nodes.PurchaseInvoice = await createPurchaseInvoice(
                    context,
                    date.make(2022, 2, 2),
                    '#FR_TVA_INTERMEDIATE_COLLECTED_ON_PAYMENT',
                );
                await assert.isRejected(purchaseInvoice.$.save());
                assert.deepEqual(purchaseInvoice.$.context.diagnoses, [
                    {
                        severity: 3,
                        path: [],
                        message: "The tax type for all documents needs to be 'Purchasing' or 'Purchasing and sales'.",
                    },
                ]);
            },
            { today: '2022-02-02' },
        ));

    it('Control on purchase invoice with correct tax type.', () =>
        Test.withContext(
            async context => {
                const purchaseInvoice: xtremPurchasing.nodes.PurchaseInvoice = await createPurchaseInvoice(
                    context,
                    date.make(2022, 2, 2),
                    '#FR_TVA_NORMAL_DEDUCTIBLE_FA_INTRASTAT',
                );
                assert.isOk(await purchaseInvoice.$.control());
            },
            { today: '2022-02-02' },
        ));

    it('Resend notification for finance - rejected because there is a landed cost allocated to a PO', () =>
        Test.withContext(async context => {
            const purchaseInvoice: xtremPurchasing.nodes.PurchaseInvoice = await context.read(
                xtremPurchasing.nodes.PurchaseInvoice,
                { number: 'PI250001' },
            );

            await assert.isRejected(
                xtremPurchasing.nodes.PurchaseInvoice.resendNotificationForFinance(context, purchaseInvoice),
                'This purchase invoice has values allocated to a purchase order. A notification will not be resent.',
            );
        }));

    it('Resend notification for finance - rejected because there is a landed cost allocated to a PO', () =>
        Test.withContext(async context => {
            const purchaseInvoice: xtremPurchasing.nodes.PurchaseInvoice = await context.read(
                xtremPurchasing.nodes.PurchaseInvoice,
                { number: 'PI250002' },
            );

            await assert.isRejected(
                xtremPurchasing.nodes.PurchaseInvoice.resendNotificationForFinance(context, purchaseInvoice),
                'This purchase invoice has values allocated to a purchase order. A notification will not be resent.',
            );
        }));

    describe('Payment status listener', () => {
        it('Updates display status correctly', () =>
            Test.withContext(
                async context => {
                    const purchaseInvoice = await context.read(xtremPurchasing.nodes.PurchaseInvoice, {
                        number: 'PI8',
                    });
                    assert.equal(await purchaseInvoice.displayStatus, 'posted');
                    await xtremPurchasing.nodes.PurchaseInvoice.setPurchaseInvoiceDisplayStatus(context, {
                        _id: purchaseInvoice._id,
                        paymentStatus: 'partiallyPaid',
                    });
                    const updatedPurchaseInvoice = await context.read(xtremPurchasing.nodes.PurchaseInvoice, {
                        number: 'PI8',
                    });
                    assert.equal(await updatedPurchaseInvoice.displayStatus, 'partiallyPaid');
                },
                {
                    testActiveServiceOptions: [xtremFinanceData.serviceOptions.paymentTrackingOption],
                },
            ));
    });

    it('Create a new purchase invoice with paymentTracking', () =>
        Test.withContext(
            async context => {
                const invoice = await context.create(xtremPurchasing.nodes.PurchaseInvoice, {
                    site: '#US001',
                    billBySupplier: '#US017',
                    paymentTerm: '#TEST_01_ALL',
                    currency: '#EUR',
                    invoiceDate: date.today(),
                    totalAmountExcludingTax: 300,
                    totalTaxAmount: 0,
                    lines: [
                        {
                            item: '#NonStockManagedItem',
                            quantity: 10,
                            unit: '#KILOGRAM',
                            grossPrice: 30,
                        },
                    ],
                });
                assert.isTrue(await invoice.$.trySave());

                assert.equal(await (await invoice.paymentTerm).id, 'TEST_01_ALL');

                /** Payment tracking with payment term and discount properties correctly created  */
                assert.equal(await (await (await invoice.paymentTracking)?.paymentTerm)?.id, 'TEST_01_ALL');
                assert.equal((await (await invoice.paymentTracking)?.discountPaymentAmount)?.toString(), '10');
                assert.equal((await (await invoice.paymentTracking)?.penaltyPaymentAmount)?.toString(), '5');
                assert.equal(await (await invoice.paymentTracking)?.discountPaymentType, 'percentage');
                assert.equal(await (await invoice.paymentTracking)?.penaltyPaymentType, 'percentage');
                assert.equal(
                    (await (await invoice.paymentTracking)?.discountPaymentBeforeDate)?.toString(),
                    date.make(2025, 3, 23).toString(),
                );

                await invoice.$.set({
                    paymentTerm: 'TEST_02_ALL',
                });
                assert.isTrue(await invoice.$.trySave());
                const updatedInvoice = await context.read(xtremPurchasing.nodes.PurchaseInvoice, {
                    _id: invoice._id,
                });
                assert.equal(await (await updatedInvoice.paymentTerm).id, 'TEST_02_ALL');

                /** Payment tracking with payment term and discount properties correctly updated  */
                assert.equal(await (await (await updatedInvoice.paymentTracking)?.paymentTerm)?.id, 'TEST_02_ALL');
                assert.equal((await (await updatedInvoice.paymentTracking)?.discountPaymentAmount)?.toString(), '20');
                assert.equal((await (await updatedInvoice.paymentTracking)?.penaltyPaymentAmount)?.toString(), '20');
                assert.equal(await (await updatedInvoice.paymentTracking)?.discountPaymentType, 'percentage');
                assert.equal(await (await updatedInvoice.paymentTracking)?.penaltyPaymentType, 'percentage');
                assert.equal(
                    (await (await updatedInvoice.paymentTracking)?.discountPaymentBeforeDate)?.toString(),
                    date.make(2025, 3, 18).toString(),
                );
            },
            { today: '2025-03-13' },
        ));
});

describe('Posting details', () => {
    it('Gets posting details successfully', () =>
        Test.withContext(async context => {
            const piDocumentNumber = 'PI240004';
            const piDocument = await context.read(xtremPurchasing.nodes.PurchaseInvoice, { number: piDocumentNumber });
            assert.equal(await piDocument.postingDetails.length, 2);

            const apInvoice = await piDocument.postingDetails.at(0);
            assert.equal(await apInvoice?.documentType, 'purchaseInvoice');
            assert.equal(await apInvoice?.documentNumber, piDocumentNumber);
            assert.equal(await apInvoice?.targetDocumentType, 'accountsPayableInvoice');

            const journalEntry = await piDocument.postingDetails.at(1);
            assert.equal(await journalEntry?.documentType, 'apInvoice');
            assert.equal(await journalEntry?.documentNumber, piDocumentNumber);
            assert.equal(await journalEntry?.targetDocumentType, 'journalEntry');
        }));
});
