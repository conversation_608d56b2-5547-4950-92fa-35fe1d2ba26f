import type { Context } from '@sage/xtrem-core';
import { Test } from '@sage/xtrem-core';
import * as xtremFinanceData from '@sage/xtrem-finance-data';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremStructure from '@sage/xtrem-structure';
import * as xtremTax from '@sage/xtrem-tax';
import { assert } from 'chai';
import * as xtremPurchasing from '../../../index';

describe('PurchaseCreditMemoNode', () => {
    it('Purchase credit memo in progress, update back to draft', () =>
        Test.withContext(async context => {
            const purchaseCreditMemo = await context.read(
                xtremPurchasing.nodes.PurchaseCreditMemo,
                { number: 'PCM6' },
                { forUpdate: true },
            );

            assert.equal(
                await xtremPurchasing.nodes.PurchaseCreditMemo.synchronizeDisplayStatus(context, purchaseCreditMemo),
                true,
            );
        }));

    it('Purchase credit memo in draft, status not updated', () =>
        Test.withContext(async context => {
            const purchaseCreditMemo = await context.read(
                xtremPurchasing.nodes.PurchaseCreditMemo,
                { number: 'PCM5' },
                { forUpdate: true },
            );

            assert.equal(
                await xtremPurchasing.nodes.PurchaseCreditMemo.synchronizeDisplayStatus(context, purchaseCreditMemo),
                false,
            );
        }));

    it('Control on purchase credit memo with wrong tax type', () =>
        Test.withContext(async context => {
            const tax = await context.read(xtremTax.nodes.Tax, { _id: '#FR_TVA_INTERMEDIATE_COLLECTED_ON_PAYMENT' });
            const purchaseCreditMemo = await context.create(xtremPurchasing.nodes.PurchaseCreditMemo, {
                site: '#US001',
                billBySupplier: '#US016',
                currency: '#USD',
                totalAmountExcludingTax: 5500,
                totalTaxAmount: 5500,
                reason: 'R1',
                lines: [
                    {
                        _action: 'create',
                        item: '#RSV9',
                        grossPrice: 110,
                        unit: '#PAIR',
                        quantity: 50,
                        site: '#US001',
                        stockUnit: '#PAIR',
                        purchaseReturnLine: { purchaseReturnLine: '#RET032|10' },
                        taxes: [
                            {
                                taxReference: tax,
                                taxRate: 20,
                                taxAmount: 10,
                            },
                        ],
                    },
                ],
            });

            await assert.isRejected(purchaseCreditMemo.$.save());
            assert.deepEqual(purchaseCreditMemo.$.context.diagnoses, [
                {
                    severity: 3,
                    path: [],
                    message: "The tax type for all documents needs to be 'Purchasing' or 'Purchasing and sales'.",
                },
                {
                    severity: 2,
                    path: ['lines', '-1000000002'],
                    message: 'The item: RSV9, is not managed for the supplier: US016.',
                },
            ]);
        }));

    it('Control on purchase credit memo with correct tax type', () =>
        Test.withContext(async context => {
            const tax = await context.read(xtremTax.nodes.Tax, { _id: '#FR_TVA_NORMAL_DEDUCTIBLE_FA_INTRASTAT' });
            const purchaseCreditMemo = await context.create(xtremPurchasing.nodes.PurchaseCreditMemo, {
                site: '#US001',
                billBySupplier: '#US016',
                currency: '#USD',
                totalAmountExcludingTax: 5500,
                totalTaxAmount: 5500,
                reason: 'R1',
                lines: [
                    {
                        _action: 'create',
                        item: '#RSV9',
                        grossPrice: 110,
                        unit: '#PAIR',
                        quantity: 50,
                        site: '#US001',
                        stockUnit: '#PAIR',
                        purchaseReturnLine: { purchaseReturnLine: '#RET032|10' },
                        taxes: [
                            {
                                taxReference: tax,
                                taxRate: 20,
                                taxAmount: 10,
                            },
                        ],
                    },
                ],
            });

            assert.isOk(await purchaseCreditMemo.$.control());
            assert.deepEqual(purchaseCreditMemo.$.context.diagnoses, [
                {
                    severity: 2,
                    path: ['lines', '-1000000002'],
                    message: 'The item: RSV9, is not managed for the supplier: US016.',
                },
            ]);
        }));
});

describe('validateTaxCategoryAndPaymentTerm on Purchase credit memo', () => {
    function readPurchaseCreditMemo(
        context: Context,
        number: string,
    ): Promise<xtremPurchasing.nodes.PurchaseCreditMemo> {
        return context.read(xtremPurchasing.nodes.PurchaseCreditMemo, { number }, { forUpdate: true });
    }

    it('VAT/GST and payment term has discount/penalty', () =>
        Test.withContext(async context => {
            context.setServiceOptionsEnabledFlag(xtremStructure.serviceOptions.intacctActivationOption, true);
            const creditMemo = await readPurchaseCreditMemo(context, 'PC240001');

            await creditMemo.$.set({ paymentTerm: '#TEST_NET_45_ALL' });
            await assert.isRejected(creditMemo.$.save());
            assert.deepEqual(creditMemo.$.context.diagnoses, [
                {
                    message:
                        'Discounts and penalties exist for this payment term: Net 45. You cannot apply discounts and penalties in a VAT or GST transaction.',
                    path: [],
                    severity: 3,
                },
            ]);
        }));
    it('VAT/GST and payment term with no discount/penalty', () =>
        Test.withContext(async context => {
            context.setServiceOptionsEnabledFlag(xtremStructure.serviceOptions.intacctActivationOption, true);

            const paymentTerm = await context.read(
                xtremMasterData.nodes.PaymentTerm,
                { id: 'TEST_NET_45_ALL' },
                { forUpdate: true },
            );
            await paymentTerm.$.set({ discountAmount: 0, penaltyAmount: 0 });
            await paymentTerm.$.save();

            const creditMemo = await readPurchaseCreditMemo(context, 'PC240001');

            await creditMemo.$.set({ paymentTerm });
            assert.isOk(await creditMemo.$.control());
        }));
    it('isIntacctActivationOption is disabled', () =>
        Test.withContext(async context => {
            const creditMemo = await readPurchaseCreditMemo(context, 'PC240001');

            await creditMemo.$.set({ paymentTerm: '#TEST_NET_45_ALL' });
            assert.isOk(await creditMemo.$.control());
        }));
    it('No VAT/GST and payment term has discount/penalty', () =>
        Test.withContext(async context => {
            // Delete VAT tax solution
            const taxSolution = await context.read(xtremTax.nodes.TaxSolution, { id: 'FRSOL' }, { forUpdate: true });
            const vatTaxSolutionLine = await taxSolution.lines.at(0);
            if (vatTaxSolutionLine) {
                assert.equal(await (await vatTaxSolutionLine.taxCategory).id, 'VAT');
                await taxSolution.$.set({ lines: [{ _action: 'delete', _id: vatTaxSolutionLine._id }] });
                await taxSolution.$.save();
                assert.deepEqual(taxSolution.$.context.diagnoses, []);
            }
            context.setServiceOptionsEnabledFlag(xtremStructure.serviceOptions.intacctActivationOption, true);
            const creditMemo = await readPurchaseCreditMemo(context, 'PC240002');
            await creditMemo.$.set({ paymentTerm: '#TEST_NET_45_ALL' });
            assert.isOk(await creditMemo.$.control());
        }));

    describe('Payment status listener', () => {
        it('Updates display status correctly', () =>
            Test.withContext(
                async context => {
                    const purchaseCreditMemo = await context.read(xtremPurchasing.nodes.PurchaseCreditMemo, {
                        number: 'PCM4',
                    });
                    assert.equal(await purchaseCreditMemo.displayStatus, 'posted');
                    await xtremPurchasing.nodes.PurchaseCreditMemo.setPurchaseCreditMemoDisplayStatus(context, {
                        _id: purchaseCreditMemo._id,
                        paymentStatus: 'partiallyPaid',
                    });
                    const updatedPurchaseCreditMemo = await context.read(xtremPurchasing.nodes.PurchaseCreditMemo, {
                        number: 'PCM4',
                    });
                    assert.equal(await updatedPurchaseCreditMemo.displayStatus, 'partiallyPaid');
                },
                {
                    testActiveServiceOptions: [xtremFinanceData.serviceOptions.paymentTrackingOption],
                },
            ));
    });
});
