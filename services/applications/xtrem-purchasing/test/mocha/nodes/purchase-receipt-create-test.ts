import { Test, ValidationSeverity, date } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremStockData from '@sage/xtrem-stock-data';
import { assert } from 'chai';
import * as sinon from 'sinon';
import * as xtremPurchasing from '../../../lib/index';
import * as TestHelper from '../../fixtures/lib/functions';
import { assertionPurchaseOrderLine, createReceiptFromOrder } from '../fixtures/purchase-order';

const { withSequenceNumberContext } = xtremMasterData.functions.testLib;

describe('Purchase receipt node', () => {
    const testDate = '2020-08-10';

    // not anymore possible to unit test this because the notification feature is now needed to trigger stock movement processing...
    // we would need the possibility to send notification in test mode to perform this test
    it.skip('Create purchase receipt with stock entry', () =>
        Test.withContext(
            async context => {
                const newPurchaseReceipt = await context.create(xtremPurchasing.nodes.PurchaseReceipt, {
                    site: '#US005',
                    businessRelation: '#US017',
                    currency: '#EUR',
                    status: 'pending',
                    date: date.make(2020, 8, 10),
                    lines: [
                        {
                            item: '#Aqua',
                            status: 'pending',
                            quantity: 150,
                            unit: '#LITER',
                            grossPrice: 100,
                            // TODO: Fix this workaround when jsonProperty works as expected
                            // stockDetails: [
                            //     {
                            //         location,
                            //         status: stockStatus,
                            //         stockUnit,
                            //         stockDetailQuantityInStockUnit: 150,
                            //         stockDetailQuantity: 150,
                            //         owner: 'US005',
                            //     },
                            // ],
                        },
                    ],
                });
                await newPurchaseReceipt.$.save();

                const piLines = await newPurchaseReceipt.lines.toArray();
                assert.equal(piLines.length, 1);
                const stockEntry =
                    (await context
                        .query(xtremStockData.nodes.StockJournal, {
                            filter: {
                                sourceDocumentLine: piLines[0]._id,
                            },
                        })
                        .at(0)) || null;
                assert.exists(stockEntry, 'Should not be null');
            },
            { today: testDate },
        ));
    it('Create purchase receipt - check evolution of work in progress expectedQuantity', () =>
        Test.withContext(
            async context => {
                const itemSite = await context.read(xtremMasterData.nodes.ItemSite, {
                    item: '#Muesli',
                    site: '#US001',
                });
                const prevOnReceiptQty = await itemSite.expectedQuantity;
                const newPurchaseReceipt = await context.create(xtremPurchasing.nodes.PurchaseReceipt, {
                    site: '#US001',
                    businessRelation: '#LECLERC',
                    currency: '#USD',
                    date: date.make(2020, 8, 10),
                    lines: [
                        {
                            item: '#Muesli',
                            quantity: 10,
                            unit: '#GRAM',
                            grossPrice: 30,
                        },
                    ],
                });
                await newPurchaseReceipt.$.save();
                assert.deepEqual(newPurchaseReceipt.$.context.diagnoses, []);
                assert.equal(prevOnReceiptQty.toString(), (await itemSite.expectedQuantity).toString());
            },
            { today: testDate },
        ));

    it('Create purchase receipt and repost', () =>
        Test.withContext(
            async context => {
                const newPurchaseReceipt = await context.create(xtremPurchasing.nodes.PurchaseReceipt, {
                    site: '#US001',
                    businessRelation: '#LECLERC',
                    currency: '#USD',
                    date: date.make(2020, 8, 10),
                    lines: [
                        {
                            item: '#Muesli',
                            quantity: 10,
                            unit: '#GRAM',
                            grossPrice: 30,
                        },
                    ],
                });
                await newPurchaseReceipt.$.save({ flushDeferredActions: true });

                const number = await newPurchaseReceipt.number;
                const receipt = await context.read(xtremPurchasing.nodes.PurchaseReceipt, { number });

                const receiptLines = await receipt.lines
                    .map(async line => {
                        return {
                            baseDocumentLineSysId: line._id,
                            storedAttributes:
                                (await line.storedAttributes) as xtremMasterData.interfaces.StoredAttributes,
                            storedDimensions: (await line.storedDimensions) || {},
                        };
                    })
                    .toArray();

                await assert.isRejected(
                    xtremPurchasing.nodes.PurchaseReceipt.repost(context, receipt, receiptLines, false, false),
                    "You can only repost a purchase receipt if the status is 'Failed' or 'Not recorded.'",
                );
            },
            { today: testDate },
        ));

    it('Create purchase receipt - refuse receipt with landed cost item', () =>
        Test.withContext(
            async context => {
                const newPurchaseReceipt = await context.create(xtremPurchasing.nodes.PurchaseReceipt, {
                    site: '#US003',
                    businessRelation: '#500',
                    currency: '#USD',
                    date: date.make(2020, 8, 10),
                    lines: [
                        {
                            item: '#LandedCost001',
                            quantity: 10,
                            unit: { id: 'EACH' },
                            grossPrice: 30,
                        },
                    ],
                });
                await assert.isRejected(newPurchaseReceipt.$.save());
                assert.deepEqual(newPurchaseReceipt.$.context.diagnoses, [
                    {
                        severity: ValidationSeverity.error,
                        path: ['lines', '-1000000002', 'item'],
                        message: 'The record is not valid. You need to select a different record.',
                    },
                    {
                        message: 'A landed cost item cannot be added to the document: PurchaseReceipt.',
                        path: ['lines', '-1000000002', 'item'],
                        severity: 3,
                    },
                ]);
            },
            { today: '2023-01-17' },
        ));

    it('Create purchase receipt from 2 order partial lines PO33 and PO31', () =>
        Test.withContext(
            async context => {
                let po33 = await context.read(xtremPurchasing.nodes.PurchaseOrder, { number: 'PO33' });

                await assertionPurchaseOrderLine(await po33.lines.elementAt(1), {
                    numberOfReceipts: 0,
                    receivedQuantity: Number(0.0),
                    receivedQuantityInStockUnit: Number(0.0),
                    quantityToReceive: 10.0,
                    quantityToReceiveInStockUnit: 10.0,
                    quantityReceivedInProgress: Number(0.0),
                    lineReceiptStatus: 'notReceived',
                    status: 'pending',
                });

                let po31 = await context.read(xtremPurchasing.nodes.PurchaseOrder, { number: 'PO31' });
                await assertionPurchaseOrderLine(await po31.lines.elementAt(3), {
                    numberOfReceipts: 1, // the receipt is not posted yet
                    receivedQuantity: 6.0,
                    receivedQuantityInStockUnit: 6.0,
                    quantityToReceive: 7.0,
                    quantityToReceiveInStockUnit: 7.0,
                    quantityReceivedInProgress: 6.0,
                    lineReceiptStatus: 'notReceived', // the receipt is not posted yet
                    status: 'inProgress',
                });

                // Todo : move 58 and 54 to get _id from the purchase order lines
                let purchaseReceipt = await createReceiptFromOrder(context, [
                    { purchaseOrderLineId: 58, receivedQuantity: 3, defaultLocation: '#LOC1|US001|Loading dock' }, // PO33 - STOFIFO - ordered qty=10 - already received=0
                    { purchaseOrderLineId: 54, receivedQuantity: 7, defaultLocation: '#LOC1|US001|Loading dock' }, // PO31 - STOFIFO - ordered qty=13 - already received=6
                ]);
                Test.assertDeepEqualDiagnoses(purchaseReceipt.$.context.diagnoses, [
                    {
                        severity: 2,
                        path: ['lines', '<???>'],
                        message: 'The item: STOFIFO, is not managed for the supplier: US016.',
                    },
                    {
                        severity: 2,
                        path: ['lines', '<???>'],
                        message: 'The item: STOFIFO, is not managed for the supplier: US016.',
                    },
                ]);

                let purchaseReceiptId = purchaseReceipt._id;
                // rollbackCache function will flush deferred actions and saves
                await Test.rollbackCache(context);

                purchaseReceipt = await context.read(
                    xtremPurchasing.nodes.PurchaseReceipt,
                    { _id: purchaseReceiptId },
                    { forUpdate: true },
                );
                let firstPurchaseReceiptLineId = (await purchaseReceipt.lines.elementAt(0))._id;
                const secondPurchaseReceiptLineId = (await purchaseReceipt.lines.elementAt(1))._id;

                // Post the purchase receipt
                await purchaseReceipt.$.set({
                    status: 'pending',
                    lines: [
                        {
                            _action: 'update',
                            _id: firstPurchaseReceiptLineId,
                            status: 'pending',
                        },
                        {
                            _action: 'update',
                            _id: secondPurchaseReceiptLineId,
                            status: 'pending',
                        },
                    ],
                });
                await purchaseReceipt.$.save();
                await context.flushDeferredSaves();

                const purchaseReceiptNumber = await purchaseReceipt.number;
                await Test.rollbackCache(context);

                purchaseReceipt = await context.read(xtremPurchasing.nodes.PurchaseReceipt, {
                    number: purchaseReceiptNumber,
                });
                assert.equal(await purchaseReceipt.lines.length, 2);
                assert.isNotNull(await (await purchaseReceipt.lines.elementAt(0)).purchaseOrderLine);
                assert.isNotNull(await (await purchaseReceipt.lines.elementAt(1)).purchaseOrderLine);
                await assert.isRejected(purchaseReceipt.lines.elementAt(2));

                po33 = await context.read(xtremPurchasing.nodes.PurchaseOrder, { number: 'PO33' });
                assert.equal(await po33.receiptStatus, 'partiallyReceived');
                assert.equal(await po33.status, 'inProgress');

                await assertionPurchaseOrderLine(await po33.lines.elementAt(0), {
                    numberOfReceipts: 1,
                    receivedQuantity: 10.0,
                    receivedQuantityInStockUnit: 10.0,
                    quantityToReceive: 0,
                    quantityToReceiveInStockUnit: 0.0,
                    quantityReceivedInProgress: Number(0.0),
                    lineReceiptStatus: 'received',
                    status: 'closed',
                });
                await assertionPurchaseOrderLine(await po33.lines.elementAt(1), {
                    numberOfReceipts: 1,
                    receivedQuantity: 3.0,
                    receivedQuantityInStockUnit: 3.0,
                    quantityToReceive: 7.0,
                    quantityToReceiveInStockUnit: 7.0,
                    quantityReceivedInProgress: 3.0,
                    lineReceiptStatus: 'partiallyReceived',
                    status: 'inProgress',
                });

                po31 = await context.read(xtremPurchasing.nodes.PurchaseOrder, { number: 'PO31' });
                assert.equal(await po31.receiptStatus, 'partiallyReceived');
                assert.equal(await po31.status, 'inProgress');
                await assertionPurchaseOrderLine(await po31.lines.elementAt(1), {
                    numberOfReceipts: 1,
                    receivedQuantity: 4.0,
                    receivedQuantityInStockUnit: 4.0,
                    quantityToReceive: 7.0,
                    quantityToReceiveInStockUnit: 7.0,
                    quantityReceivedInProgress: 4.0,
                    lineReceiptStatus: 'notReceived', // the receipt is not posted yet
                    status: 'inProgress',
                });
                await assertionPurchaseOrderLine(await po31.lines.elementAt(3), {
                    numberOfReceipts: 2,
                    receivedQuantity: 13.0,
                    receivedQuantityInStockUnit: 13.0,
                    quantityToReceive: 0,
                    quantityToReceiveInStockUnit: 0.0,
                    quantityReceivedInProgress: 13.0,
                    lineReceiptStatus: 'partiallyReceived',
                    status: 'inProgress',
                });

                // Create the 2nd receipt to close the order
                purchaseReceipt = await createReceiptFromOrder(context, [
                    { purchaseOrderLineId: 58, receivedQuantity: 7, defaultLocation: '#LOC1|US001|Loading dock' }, // PO33 - STOFIFO - ordered qty=10 - already received=3
                ]);
                purchaseReceiptId = purchaseReceipt._id;
                firstPurchaseReceiptLineId = (await purchaseReceipt.lines.elementAt(0))._id;
                await Test.rollbackCache(context);

                purchaseReceipt = await context.read(
                    xtremPurchasing.nodes.PurchaseReceipt,
                    { _id: purchaseReceiptId },
                    { forUpdate: true },
                );

                await purchaseReceipt.$.set({
                    status: 'pending',
                    lines: [
                        {
                            _action: 'update',
                            _id: firstPurchaseReceiptLineId,
                            status: 'pending',
                        },
                    ],
                });
                await purchaseReceipt.$.save();
                await Test.rollbackCache(context);

                po31 = await context.read(xtremPurchasing.nodes.PurchaseOrder, { number: 'PO33' });
                assert.equal(await po31.receiptStatus, 'received');
                assert.equal(await po31.status, 'closed');
                await assertionPurchaseOrderLine(await po31.lines.elementAt(1), {
                    numberOfReceipts: 2,
                    receivedQuantity: 10.0,
                    receivedQuantityInStockUnit: 10.0,
                    quantityToReceive: 0,
                    quantityToReceiveInStockUnit: 0.0,
                    quantityReceivedInProgress: 10.0,
                    lineReceiptStatus: 'received',
                    status: 'closed',
                });
            },
            { today: testDate, testActiveServiceOptions: [xtremMasterData.serviceOptions.landedCostOption] },
        ));

    it('Create purchase receipt - purchase-receipt-create-from-order-with-existing-draft-receipt-lines', () =>
        Test.withContext(
            async context => {
                let newPurchaseReceipt = await context.create(xtremPurchasing.nodes.PurchaseReceipt, {
                    currency: '#EUR',
                    businessRelation: '#LECLERC',
                    site: '#US001',
                    date: date.make(2020, 8, 10),
                    status: 'draft',
                    returnStatus: 'notReturned',
                    invoiceStatus: 'notInvoiced',
                    lines: [
                        {
                            charge: 0,
                            completed: false,
                            discount: 0,
                            grossPrice: 10,
                            item: '#Muesli',
                            status: 'draft',
                            netPrice: 10,
                            priceOrigin: null,
                            purchaseOrderLine: { purchaseOrderLine: { _id: 39 } },
                            unit: '#KILOGRAM',
                            unitToStockUnitConversionFactor: 1000,
                            quantity: 5,
                            stockUnit: '#GRAM',
                            storedAttributes: null,
                            storedDimensions: null,
                        },
                    ],
                });
                await newPurchaseReceipt.$.save();
                assert.deepEqual(newPurchaseReceipt.$.context.diagnoses, []);
                const newPurchaseReceiptId = newPurchaseReceipt._id;
                // rollbackCache function will flush deferred actions and saves
                await Test.rollbackCache(context);

                newPurchaseReceipt = await context.read(xtremPurchasing.nodes.PurchaseReceipt, {
                    _id: newPurchaseReceiptId,
                });

                // Header
                assert.equal(await (await newPurchaseReceipt.supplierAddress)?.name, 'Store');
                assert.equal(await (await newPurchaseReceipt.receivingAddress)?.name, 'US001');
                assert.equal(await (await newPurchaseReceipt.returnAddress)?.name, 'Store');
                assert.equal(await newPurchaseReceipt.number, 'PR200001');
                assert.equal(await (await newPurchaseReceipt.site)?.name, 'Chem. Atlanta');
                assert.deepEqual(await newPurchaseReceipt.date, date.make(2020, 8, 10));
                assert.equal(
                    await (
                        await (
                            await newPurchaseReceipt.businessRelation
                        ).businessEntity
                    ).name,
                    'LECLERC supermarket',
                );
                assert.equal(await (await newPurchaseReceipt.currency)?.name, 'Euro');
                assert.equal(await (await newPurchaseReceipt.transactionCurrency)?.id, 'EUR');
                assert.equal(await (await newPurchaseReceipt.companyCurrency)?.id, 'USD');
                assert.equal(await newPurchaseReceipt.totalAmountExcludingTax, 50);
                assert.equal(await newPurchaseReceipt.status, 'draft');
                assert.equal(await newPurchaseReceipt.returnStatus, 'notReturned');
                assert.equal(await newPurchaseReceipt.invoiceStatus, 'notInvoiced');
                // Line
                const newPurchaseReceiptFirstLine = await newPurchaseReceipt.lines.elementAt(0);
                assert.equal(await (await newPurchaseReceiptFirstLine.stockUnit)?.name, 'Gram');
                assert.equal(await (await newPurchaseReceiptFirstLine.unit)?.name, 'Kilogram');
                assert.equal(await newPurchaseReceiptFirstLine.status, 'draft');
                assert.equal(await newPurchaseReceiptFirstLine.lineReturnStatus, 'notReturned');
                assert.equal(await newPurchaseReceiptFirstLine.lineInvoiceStatus, 'notInvoiced');
                assert.equal(await newPurchaseReceiptFirstLine.itemDescription, 'A 500g box of muesli');
                assert.equal(await newPurchaseReceiptFirstLine.quantity, 5);
                assert.equal(await newPurchaseReceiptFirstLine.quantityInStockUnit, 5000);
                assert.equal(await newPurchaseReceiptFirstLine.unitToStockUnitConversionFactor, 1000);
                assert.equal(await newPurchaseReceiptFirstLine.origin, 'purchaseOrder');
                assert.equal(await newPurchaseReceiptFirstLine.grossPrice, 10);
                assert.equal(await newPurchaseReceiptFirstLine.discount, 0);
                assert.equal(await newPurchaseReceiptFirstLine.charge, 0);
                assert.equal(await newPurchaseReceiptFirstLine.netPrice, 10);
                assert.equal(await newPurchaseReceiptFirstLine.amountExcludingTax, 50);
                assert.deepEqual(await newPurchaseReceiptFirstLine.amountExcludingTaxInCompanyCurrency, 50.1);
                // PO 2 PR link
                const po2pr = await newPurchaseReceiptFirstLine.purchaseOrderLine;
                assert.equal(await po2pr?.receivedQuantity, 5);
                assert.equal(await (await (await po2pr?.purchaseReceiptLine)?.document)?.number, 'PR200001');
                assert.equal(await (await (await po2pr?.purchaseOrderLine)?.document)?.number, 'PO24');
                assert.equal(await (await po2pr?.purchaseOrderLine)?.quantityToReceive, 0);
                assert.equal(await (await po2pr?.purchaseOrderLine)?.quantity, 10);
                assert.equal(await (await po2pr?.purchaseOrderLine)?.receivedQuantity, 15);
                assert.equal(await (await po2pr?.purchaseOrderLine)?.status, 'inProgress');
            },
            { today: testDate },
        ));
});
describe('Purchase receipt node - create invoice from a receipt', () => {
    afterEach(() => {
        sinon.restore();
    });

    const testDate = '2020-11-11';
    it('Create purchase invoice for purchase receipt - entry parameters testing', () =>
        Test.withContext(
            async context => {
                const document = await context.read(
                    xtremPurchasing.nodes.PurchaseReceipt,
                    {
                        number: 'PR14',
                    },
                    { forUpdate: true },
                );
                const createResult = await xtremPurchasing.nodes.PurchaseReceipt.createPurchaseInvoice(
                    context,
                    document,
                );
                assert.deepEqual(createResult, []);
                assert.deepEqual(context.diagnoses, []);
            },
            { today: testDate },
        ));
    it('Create invoice for purchase receipt PR11 - 2 pr lines on 1 pi with 2 lines', () =>
        Test.withContext(
            async context => {
                const document = await context.read(
                    xtremPurchasing.nodes.PurchaseReceipt,
                    { number: 'PR11' },
                    { forUpdate: true },
                );
                const createInvoices = await xtremPurchasing.nodes.PurchaseReceipt.createPurchaseInvoice(
                    context,
                    document,
                );
                await context.flushDeferredActions();
                assert.equal(createInvoices.length, 1);
                const invoiceNumber = await createInvoices.at(0)?.number;
                assert.equal(invoiceNumber, 'PI200001');

                const pi = await context.read(xtremPurchasing.nodes.PurchaseInvoice, {
                    number: invoiceNumber,
                });
                assert.equal(await pi.number, 'PI200001');
                assert.equal(await pi.lines.length, 2);
            },
            { today: testDate },
        ));
    it('Create invoice for purchase receipt PR11 - 2 pr lines on 2 pi lines (different purchase unit)', () =>
        Test.withContext(
            async context => {
                const document = await context.read(
                    xtremPurchasing.nodes.PurchaseReceipt,
                    { number: 'PR11' },
                    { forUpdate: true },
                );
                const createInvoices = await xtremPurchasing.nodes.PurchaseReceipt.createPurchaseInvoice(
                    context,
                    document,
                );
                await context.flushDeferredActions();

                assert.equal(createInvoices.length, 1);
                const invoiceNumber = await createInvoices.at(0)?.number;
                assert.equal(invoiceNumber, 'PI200001');

                const pi = await context.read(xtremPurchasing.nodes.PurchaseInvoice, {
                    number: invoiceNumber,
                });
                assert.equal(await pi.number, 'PI200001');
                assert.equal(await pi.lines.length, 2);
            },
            { today: testDate },
        ));
    it('Create invoice for purchase receipt PR10 - 1 pr line on 1 pi line', () =>
        Test.withContext(
            async context => {
                const document = await context.read(
                    xtremPurchasing.nodes.PurchaseReceipt,
                    { number: 'PR10' },
                    { forUpdate: true },
                );
                const createInvoices = await xtremPurchasing.nodes.PurchaseReceipt.createPurchaseInvoice(
                    context,
                    document,
                );
                await context.flushDeferredActions();

                assert.equal(createInvoices.length, 1);
                const invoiceNumber = await createInvoices.at(0)?.number;
                assert.equal(invoiceNumber, 'PI200001');

                const pi = await context.read(xtremPurchasing.nodes.PurchaseInvoice, { number: invoiceNumber });
                assert.equal(await pi.number, 'PI200001');
                assert.equal(await pi.lines.length, 1);
            },
            { today: testDate },
        ));
    it('Create purchase receipt and then fully invoice it in a row', () =>
        Test.withContext(
            async context => {
                let newPurchaseReceipt = await context.create(xtremPurchasing.nodes.PurchaseReceipt, {
                    site: '#US001',
                    businessRelation: '#US017',
                    currency: '#EUR',
                    status: 'pending',
                    date: date.today(),
                    lines: [
                        {
                            item: '#Aqua',
                            status: 'pending',
                            quantity: 10,
                            unit: '#LITER',
                            grossPrice: 30,
                            stockTransactionStatus: 'completed',
                            jsonStockDetails: [
                                {
                                    item: '#Aqua',
                                    site: '#US001',
                                    location: '#LOC1|US001|Loading dock',
                                    stockUnit: '#LITER',
                                    status: '#A',
                                    quantityInStockUnit: 10,
                                },
                            ],
                        },
                    ],
                });
                await newPurchaseReceipt.$.save({ flushDeferredActions: true });

                TestHelper.PurchaseReceipt.activateFakeReceiptLineUpdate(context);

                const createInvoices = await xtremPurchasing.nodes.PurchaseReceipt.createPurchaseInvoice(
                    context,
                    newPurchaseReceipt,
                );
                await context.flushDeferredActions();

                assert.equal(createInvoices.length, 1);
                const invoiceNumber = await createInvoices.at(0)?.number;
                assert.equal(invoiceNumber, 'PI240007');

                const purchaseInvoice = await context.read(xtremPurchasing.nodes.PurchaseInvoice, {
                    number: invoiceNumber,
                });
                assert.equal(await purchaseInvoice.lines.length, 1);
                const purchaseInvoiceLine = await purchaseInvoice.lines.elementAt(0);
                assert.isNotNull(await purchaseInvoiceLine.purchaseReceiptLine);
                assert.equal(
                    (await (await purchaseInvoiceLine.purchaseReceiptLine)!.invoicedQuantity).toString(),
                    (await (await newPurchaseReceipt.lines.elementAt(0)).quantity).toString(),
                );
                assert.equal(
                    (await (await purchaseInvoiceLine.purchaseReceiptLine)!.invoicedQuantityInStockUnit).toString(),
                    (await (await newPurchaseReceipt.lines.elementAt(0)).quantityInStockUnit).toString(),
                );
                newPurchaseReceipt = await context.read(xtremPurchasing.nodes.PurchaseReceipt, {
                    _id: newPurchaseReceipt._id,
                });

                assert.equal(await (await newPurchaseReceipt.lines.elementAt(0)).lineInvoiceStatus, 'invoiced');
                assert.equal(await newPurchaseReceipt.status, 'closed');
            },
            { today: '2024-11-11' },
        ));
    it('Create purchase receipt and then partially invoice it in a row', () =>
        Test.withContext(async context => {
            let newPurchaseReceipt = await context.create(xtremPurchasing.nodes.PurchaseReceipt, {
                site: '#US001',
                businessRelation: '#LECLERC',
                currency: '#USD',
                date: date.today(),
                lines: [
                    {
                        item: '#Muesli',
                        quantity: 10,
                        unit: '#KILOGRAM',
                        grossPrice: 30,
                        stockTransactionStatus: 'completed',
                    },
                ],
            });
            await newPurchaseReceipt.$.save();

            TestHelper.PurchaseReceipt.activateFakeReceiptLineUpdate(context);

            const purchaseInvoice = await context.create(xtremPurchasing.nodes.PurchaseInvoice, {
                site: '#US001',
                billBySupplier: '#LECLERC',
                currency: '#USD',
                invoiceDate: date.today(),
                totalAmountExcludingTax: await newPurchaseReceipt.totalAmountExcludingTax,
                lines: [
                    {
                        item: '#Muesli',
                        quantity: 6,
                        unit: '#KILOGRAM',
                        grossPrice: 30,
                        purchaseReceiptLine: {
                            purchaseReceiptLine: (await newPurchaseReceipt.lines.elementAt(0))._id,
                        },
                    },
                ],
            });
            await purchaseInvoice.$.save();

            assert.equal(await purchaseInvoice.lines.length, 1);
            const purchaseInvoiceLine = await purchaseInvoice.lines.elementAt(0);
            assert.isNotNull(await purchaseInvoiceLine.purchaseReceiptLine);
            assert.equal((await (await purchaseInvoiceLine.purchaseReceiptLine)!.invoicedQuantity).toString(), '6');
            assert.equal(
                (await (await purchaseInvoiceLine.purchaseReceiptLine)!.invoicedQuantityInStockUnit).toString(),
                '6000',
            );

            newPurchaseReceipt = await context.read(xtremPurchasing.nodes.PurchaseReceipt, {
                _id: newPurchaseReceipt._id,
            });
            assert.equal((await (await newPurchaseReceipt.lines.elementAt(0)).invoicedQuantity).toString(), '6');
            assert.equal(await (await newPurchaseReceipt.lines.elementAt(0)).lineInvoiceStatus, 'partiallyInvoiced');
            assert.equal(await newPurchaseReceipt.status, 'inProgress');
        }));
    it('Create purchase receipt and then partially and fully invoice it in a row', () =>
        Test.withContext(
            async context => {
                let newPurchaseReceipt = await context.create(xtremPurchasing.nodes.PurchaseReceipt, {
                    site: '#US001',
                    businessRelation: '#LECLERC',
                    currency: '#USD',
                    date: date.make(2020, 8, 10),
                    lines: [
                        {
                            item: '#Muesli',
                            quantity: 10,
                            unit: '#KILOGRAM',
                            grossPrice: 30,
                            stockTransactionStatus: 'completed',
                        },
                    ],
                });
                await newPurchaseReceipt.$.save();

                TestHelper.PurchaseReceipt.activateFakeReceiptLineUpdate(context);

                let purchaseInvoice = await context.create(xtremPurchasing.nodes.PurchaseInvoice, {
                    site: '#US001',
                    billBySupplier: '#LECLERC',
                    currency: '#USD',
                    invoiceDate: date.today(),
                    totalAmountExcludingTax: await newPurchaseReceipt.totalAmountExcludingTax,
                    lines: [
                        {
                            item: '#Muesli',
                            quantity: 7,
                            unit: '#KILOGRAM',
                            grossPrice: 30,
                            purchaseReceiptLine: {
                                purchaseReceiptLine: (await newPurchaseReceipt.lines.elementAt(0))._id,
                            },
                        },
                    ],
                });
                await purchaseInvoice.$.save();

                assert.equal(await purchaseInvoice.lines.length, 1);
                const purchaseInvoiceLine = await purchaseInvoice.lines.elementAt(0);
                assert.isNotNull(await purchaseInvoiceLine.purchaseReceiptLine);
                assert.equal((await (await purchaseInvoiceLine.purchaseReceiptLine)!.invoicedQuantity).toString(), '7');
                assert.equal(
                    (await (await purchaseInvoiceLine.purchaseReceiptLine)!.invoicedQuantityInStockUnit).toString(),
                    '7000',
                );

                newPurchaseReceipt = await context.read(xtremPurchasing.nodes.PurchaseReceipt, {
                    _id: newPurchaseReceipt._id,
                });
                assert.equal(
                    await (
                        await newPurchaseReceipt.lines.elementAt(0)
                    ).lineInvoiceStatus,
                    'partiallyInvoiced',
                );
                assert.equal(await newPurchaseReceipt.status, 'inProgress');

                newPurchaseReceipt = await context.read(xtremPurchasing.nodes.PurchaseReceipt, {
                    _id: newPurchaseReceipt._id,
                });
                purchaseInvoice = await context.create(xtremPurchasing.nodes.PurchaseInvoice, {
                    site: '#US001',
                    billBySupplier: '#LECLERC',
                    currency: '#USD',
                    invoiceDate: date.today(),
                    totalAmountExcludingTax: await newPurchaseReceipt.totalAmountExcludingTax,
                    lines: [
                        {
                            item: '#Muesli',
                            quantity: 3,
                            unit: '#KILOGRAM',
                            grossPrice: 30,
                            purchaseReceiptLine: {
                                purchaseReceiptLine: (await newPurchaseReceipt.lines.elementAt(0))._id,
                            },
                        },
                    ],
                });
                await purchaseInvoice.$.save();

                assert.equal(await purchaseInvoice.lines.length, 1);
                assert.isNotNull(await (await purchaseInvoice.lines.elementAt(0)).purchaseReceiptLine);
                assert.equal(
                    (
                        await (await (
                            await purchaseInvoice.lines.elementAt(0)
                        ).purchaseReceiptLine)!.invoicedQuantity
                    ).toString(),
                    '3',
                );
                assert.equal(
                    (
                        await (await (
                            await purchaseInvoice.lines.elementAt(0)
                        ).purchaseReceiptLine)!.invoicedQuantityInStockUnit
                    ).toString(),
                    '3000',
                );

                newPurchaseReceipt = await context.read(xtremPurchasing.nodes.PurchaseReceipt, {
                    _id: newPurchaseReceipt._id,
                });
                assert.equal(await (await newPurchaseReceipt.lines.elementAt(0)).lineInvoiceStatus, 'invoiced');
                assert.equal(await newPurchaseReceipt.status, 'closed');
            },
            { today: '2020-08-10' },
        ));
    it('Create invoice for purchase receipt PR11 - isTransferHeaderNote = true isTransferLineNote = true', () =>
        Test.withContext(
            async context => {
                const document = await context.read(
                    xtremPurchasing.nodes.PurchaseReceipt,
                    { number: 'PR11' },
                    { forUpdate: true },
                );
                await document.$.set({ isTransferHeaderNote: true, isTransferLineNote: true });
                await document.$.save();

                const createInvoices = await xtremPurchasing.nodes.PurchaseReceipt.createPurchaseInvoice(
                    context,
                    document,
                );
                await context.flushDeferredActions();

                assert.equal(createInvoices.length, 1);
                const invoiceNumber = await createInvoices.at(0)?.number;
                assert.equal(invoiceNumber, 'PI200001');

                const purchaseInvoice = await context.read(xtremPurchasing.nodes.PurchaseInvoice, {
                    number: invoiceNumber,
                });
                assert.equal((await purchaseInvoice.internalNote).toString(), (await document.internalNote).toString());
                assert.equal(await purchaseInvoice.isTransferHeaderNote, await document.isTransferHeaderNote);
                assert.equal(await purchaseInvoice.isTransferLineNote, await document.isTransferLineNote);

                const purchaseInvoiceLine1 = await purchaseInvoice.lines.elementAt(0);
                const purchaseReceiptLine1 = await document.lines.elementAt(0);
                assert.equal(
                    (await purchaseInvoiceLine1.internalNote).toString(),
                    (await purchaseReceiptLine1.internalNote).toString(),
                );

                const purchaseInvoiceLine2 = await purchaseInvoice.lines.elementAt(1);
                const purchaseReceiptLine2 = await document.lines.elementAt(1);
                assert.equal(
                    (await purchaseInvoiceLine2.internalNote).toString(),
                    (await purchaseReceiptLine2.internalNote).toString(),
                );
            },
            { today: testDate },
        ));
    it('Create invoice for purchase receipt PR11 - isTransferHeaderNote = false isTransferLineNote = true', () =>
        Test.withContext(
            async context => {
                const document = await context.read(
                    xtremPurchasing.nodes.PurchaseReceipt,
                    { number: 'PR11' },
                    { forUpdate: true },
                );
                await document.$.set({ isTransferHeaderNote: false });
                await document.$.set({ isTransferLineNote: true });
                await document.$.save();

                const createInvoices = await xtremPurchasing.nodes.PurchaseReceipt.createPurchaseInvoice(
                    context,
                    document,
                );
                await context.flushDeferredActions();

                assert.equal(createInvoices.length, 1);
                const invoiceNumber = await createInvoices.at(0)?.number;
                assert.equal(invoiceNumber, 'PI200001');

                const purchaseInvoice = await context.read(xtremPurchasing.nodes.PurchaseInvoice, {
                    number: invoiceNumber,
                });

                // Must be the same of the supplier
                assert.equal(
                    (await purchaseInvoice.internalNote).toString(),
                    (await (await purchaseInvoice.businessRelation).internalNote).toString(),
                );

                assert.notEqual(
                    (await purchaseInvoice.internalNote).toString(),
                    (await document.internalNote).toString(),
                );
                assert.equal(await purchaseInvoice.isTransferHeaderNote, await document.isTransferHeaderNote);
                assert.equal(await purchaseInvoice.isTransferLineNote, await document.isTransferLineNote);

                const purchaseInvoiceLine1 = await purchaseInvoice.lines.elementAt(0);
                const purchaseReceiptLine1 = await document.lines.elementAt(0);
                assert.equal(
                    (await purchaseInvoiceLine1.internalNote).toString(),
                    (await purchaseReceiptLine1.internalNote).toString(),
                );

                const purchaseInvoiceLine2 = await purchaseInvoice.lines.elementAt(1);
                const purchaseReceiptLine2 = await document.lines.elementAt(1);
                assert.equal(
                    (await purchaseInvoiceLine2.internalNote).toString(),
                    (await purchaseReceiptLine2.internalNote).toString(),
                );
            },
            { today: testDate },
        ));
    it('Create invoice for purchase receipt PR11 - isTransferHeaderNote = true isTransferLineNote = false', () =>
        Test.withContext(
            async context => {
                const document = await context.read(
                    xtremPurchasing.nodes.PurchaseReceipt,
                    { number: 'PR11' },
                    { forUpdate: true },
                );
                await document.$.set({ isTransferHeaderNote: true });
                await document.$.set({ isTransferLineNote: false });
                await document.$.save();

                const createInvoices = await xtremPurchasing.nodes.PurchaseReceipt.createPurchaseInvoice(
                    context,
                    document,
                );
                await context.flushDeferredActions();

                assert.equal(createInvoices.length, 1);
                const invoiceNumber = await createInvoices.at(0)?.number;
                assert.equal(invoiceNumber, 'PI200001');
                const purchaseInvoice = await context.read(xtremPurchasing.nodes.PurchaseInvoice, {
                    number: invoiceNumber,
                });

                assert.equal((await purchaseInvoice.internalNote).toString(), (await document.internalNote).toString());
                assert.equal(await purchaseInvoice.isTransferHeaderNote, await document.isTransferHeaderNote);
                assert.equal(await purchaseInvoice.isTransferLineNote, await document.isTransferLineNote);

                const purchaseInvoiceLine1 = await purchaseInvoice.lines.elementAt(0);
                const purchaseReceiptLine1 = await document.lines.elementAt(0);
                assert.notEqual(
                    (await purchaseInvoiceLine1.internalNote).toString(),
                    (await purchaseReceiptLine1.internalNote).toString(),
                );
                assert.isEmpty((await purchaseInvoiceLine1.internalNote).toString());

                const purchaseInvoiceLine2 = await purchaseInvoice.lines.elementAt(1);
                const purchaseReceiptLine2 = await document.lines.elementAt(1);
                assert.notEqual(
                    (await purchaseInvoiceLine2.internalNote).toString(),
                    (await purchaseReceiptLine2.internalNote).toString(),
                );
                assert.isEmpty((await purchaseInvoiceLine2.internalNote).toString());
            },
            { today: testDate },
        ));
    it('Create invoice for purchase receipt PR11 - isTransferHeaderNote = false isTransferLineNote = false', () =>
        Test.withContext(
            async context => {
                const document = await context.read(
                    xtremPurchasing.nodes.PurchaseReceipt,
                    { number: 'PR11' },
                    { forUpdate: true },
                );
                await document.$.set({ isTransferHeaderNote: false });
                await document.$.set({ isTransferLineNote: false });
                await document.$.save();

                const createInvoices = await xtremPurchasing.nodes.PurchaseReceipt.createPurchaseInvoice(
                    context,
                    document,
                );
                await context.flushDeferredActions();

                assert.equal(createInvoices.length, 1);
                const invoiceNumber = await createInvoices.at(0)?.number;
                assert.equal(invoiceNumber, 'PI200001');

                const purchaseInvoice = await context.read(xtremPurchasing.nodes.PurchaseInvoice, {
                    number: invoiceNumber,
                });

                // Must be the same of the supplier
                assert.equal(
                    (await purchaseInvoice.internalNote).toString(),
                    (await (await purchaseInvoice.businessRelation).internalNote).toString(),
                );

                assert.notEqual(
                    (await purchaseInvoice.internalNote).toString(),
                    (await document.internalNote).toString(),
                );
                assert.equal(await purchaseInvoice.isTransferHeaderNote, await document.isTransferHeaderNote);
                assert.equal(await purchaseInvoice.isTransferLineNote, await document.isTransferLineNote);

                const purchaseInvoiceLine1 = await purchaseInvoice.lines.elementAt(0);
                const purchaseReceiptLine1 = await document.lines.elementAt(0);
                assert.notEqual(
                    (await purchaseInvoiceLine1.internalNote).toString(),
                    (await purchaseReceiptLine1.internalNote).toString(),
                );
                assert.isEmpty((await purchaseInvoiceLine1.internalNote).toString());

                const purchaseInvoiceLine2 = await purchaseInvoice.lines.elementAt(1);
                const purchaseReceiptLine2 = await document.lines.elementAt(1);
                assert.notEqual(
                    (await purchaseInvoiceLine2.internalNote).toString(),
                    (await purchaseReceiptLine2.internalNote).toString(),
                );
                assert.isEmpty((await purchaseInvoiceLine2.internalNote).toString());
            },
            { today: testDate },
        ));
});
describe('Purchase receipt node - create return from a receipt', () => {
    it('Create purchase return for purchase receipt - entry parameters testing', () =>
        Test.withContext(
            async context => {
                const receipt14 = await context.read(xtremPurchasing.nodes.PurchaseReceipt, { number: 'PR14' });
                assert.equal(await receipt14.status, 'closed');
                const [returns] = await xtremPurchasing.nodes.PurchaseReceipt.createPurchaseReturns(context, receipt14);
                await context.flushDeferredActions();
                assert.equal(await returns.number, 'PT230001');
                assert.deepEqual(context.diagnoses, []);
            },
            { today: '2023-12-15' },
        ));
    it('Create return for purchase receipt PR11 - 2 pr lines on 1 pret line', () =>
        Test.withContext(
            async context => {
                const ret11 = await context.read(xtremPurchasing.nodes.PurchaseReceipt, { number: 'PR11' });
                const [returns] = await xtremPurchasing.nodes.PurchaseReceipt.createPurchaseReturns(context, ret11);
                await context.flushDeferredActions();
                assert.equal(await returns.number, 'PT200001');
                const pi = await context.read(xtremPurchasing.nodes.PurchaseReturn, {
                    number: await returns.number,
                });
                assert.equal(await pi.number, 'PT200001');
                assert.equal(await pi.lines.length, 2);
            },
            { today: '2020-11-11' },
        ));
    it('Create return for purchase receipt PR9 - 2 pr lines on 2 pret lines (different purchase unit)', () =>
        Test.withContext(
            async context => {
                const ret9 = await context.read(xtremPurchasing.nodes.PurchaseReceipt, { number: 'PR9' });
                const [returns] = await xtremPurchasing.nodes.PurchaseReceipt.createPurchaseReturns(context, ret9);
                await context.flushDeferredActions();
                assert.equal(await returns.number, 'PT200001');
                const pi = await context.read(xtremPurchasing.nodes.PurchaseReturn, {
                    number: await returns.number,
                });
                assert.equal(await pi.number, 'PT200001');
                assert.equal(await pi.lines.length, 2);
            },
            { today: '2020-11-11' },
        ));
    it('Create return for purchase receipt PR10 - 1 pr line on 1 pret line', () =>
        Test.withContext(
            async context => {
                const ret10 = await context.read(xtremPurchasing.nodes.PurchaseReceipt, { number: 'PR10' });
                const [returns] = await xtremPurchasing.nodes.PurchaseReceipt.createPurchaseReturns(context, ret10);
                await context.flushDeferredActions();
                assert.equal(await returns.number, 'PT200001');
                const pi = await context.read(xtremPurchasing.nodes.PurchaseReturn, {
                    number: await returns.number,
                });
                assert.equal(await pi.number, 'PT200001');
                assert.equal(await pi.lines.length, 1);
            },
            { today: '2020-11-11' },
        ));
    it('Create purchase receipt and then fully return it in a row', () =>
        Test.withContext(async context => {
            let newPurchaseReceipt = await context.create(xtremPurchasing.nodes.PurchaseReceipt, {
                site: '#US001',
                businessRelation: '#LECLERC',
                currency: '#USD',
                status: 'pending',
                date: date.today(),
                lines: [
                    {
                        item: '#Muesli',
                        status: 'pending',
                        quantity: 10,
                        unit: '#KILOGRAM',
                        grossPrice: 30,
                    },
                ],
            });
            await newPurchaseReceipt.$.save({ flushDeferredActions: true });

            const [returns] = await xtremPurchasing.nodes.PurchaseReceipt.createPurchaseReturns(
                context,
                newPurchaseReceipt,
            );
            await context.flushDeferredActions();
            const returnNumber = await returns.number;
            assert.equal(await returns.lines.length, 1);

            const purchaseReturn = await context.read(xtremPurchasing.nodes.PurchaseReturn, {
                number: returnNumber,
            });
            assert.equal(await purchaseReturn.lines.length, 1);
            const purchaseReturnLine = await purchaseReturn.lines.elementAt(0);
            assert.isNotNull(await purchaseReturnLine.purchaseReceiptLine);
            assert.equal(
                (await (await purchaseReturnLine.purchaseReceiptLine).returnedQuantity).toString(),
                (await (await newPurchaseReceipt.lines.elementAt(0)).quantity).toString(),
            );
            assert.equal(
                (await (await purchaseReturnLine.purchaseReceiptLine).returnedQuantityInStockUnit).toString(),
                (await (await newPurchaseReceipt.lines.elementAt(0)).quantityInStockUnit).toString(),
            );
            newPurchaseReceipt = await context.read(xtremPurchasing.nodes.PurchaseReceipt, {
                _id: newPurchaseReceipt._id,
            });
            assert.equal(await (await newPurchaseReceipt.lines.elementAt(0)).lineReturnStatus, 'returned');
            assert.equal(await newPurchaseReceipt.status, 'closed');
        }));
    it('Create purchase receipt and then partially return it in a row', () =>
        Test.withContext(async context => {
            let newPurchaseReceipt = await context.create(xtremPurchasing.nodes.PurchaseReceipt, {
                site: '#US001',
                businessRelation: '#LECLERC',
                currency: '#USD',
                date: date.today(),
                status: 'pending',
                lines: [
                    {
                        item: '#Muesli',
                        quantity: 10,
                        unit: '#KILOGRAM',
                        grossPrice: 30,
                        stockTransactionStatus: 'inProgress',
                        status: 'pending',
                    },
                ],
            });
            await newPurchaseReceipt.$.save();

            const purchaseReturn = await context.create(xtremPurchasing.nodes.PurchaseReturn, {
                returnSite: '#US001',
                businessRelation: '#LECLERC',
                currency: '#USD',
                returnRequestDate: date.today(),
                lines: [
                    {
                        item: '#Muesli',
                        quantity: 6,
                        unit: '#KILOGRAM',
                        grossPrice: 30,
                        purchaseReceiptLine: {
                            purchaseReceiptLine: (await newPurchaseReceipt.lines.elementAt(0))._id,
                            returnedQuantity: 6,
                        },
                        reason: { id: 'R1' },
                    },
                ],
            });
            await purchaseReturn.$.save();

            assert.equal(await purchaseReturn.lines.length, 1);
            const purchaseReturnLine = await purchaseReturn.lines.elementAt(0);
            assert.isNotNull(await purchaseReturnLine.purchaseReceiptLine);
            assert.equal((await (await purchaseReturnLine.purchaseReceiptLine).returnedQuantity).toString(), '6');
            assert.equal(
                (await (await purchaseReturnLine.purchaseReceiptLine).returnedQuantityInStockUnit).toString(),
                '6000',
            );

            newPurchaseReceipt = await context.read(xtremPurchasing.nodes.PurchaseReceipt, {
                _id: newPurchaseReceipt._id,
            });
            assert.equal((await (await newPurchaseReceipt.lines.elementAt(0)).returnedQuantity).toString(), '6');
            assert.equal(await (await newPurchaseReceipt.lines.elementAt(0)).lineReturnStatus, 'partiallyReturned');
            assert.equal('inProgress', await newPurchaseReceipt.status);
        }));
    it('Create purchase receipt and then partially and fully return it in a row', () =>
        Test.withContext(
            async context => {
                let newPurchaseReceipt = await context.create(xtremPurchasing.nodes.PurchaseReceipt, {
                    site: '#US001',
                    businessRelation: '#LECLERC',
                    currency: '#USD',
                    date: date.make(2020, 8, 10),
                    status: 'pending',
                    lines: [
                        {
                            item: '#Muesli',
                            quantity: 10,
                            unit: '#KILOGRAM',
                            grossPrice: 30,
                            stockTransactionStatus: 'inProgress',
                            status: 'pending',
                        },
                    ],
                });
                await newPurchaseReceipt.$.save();

                let purchaseReturn = await context.create(xtremPurchasing.nodes.PurchaseReturn, {
                    returnSite: '#US001',
                    businessRelation: '#LECLERC',
                    currency: '#USD',
                    returnRequestDate: date.today(),
                    lines: [
                        {
                            item: '#Muesli',
                            quantity: 7,
                            unit: '#KILOGRAM',
                            grossPrice: 30,
                            purchaseReceiptLine: {
                                purchaseReceiptLine: (await newPurchaseReceipt.lines.elementAt(0))._id,
                                returnedQuantity: 7,
                            },
                            reason: { id: 'R1' },
                        },
                    ],
                });
                await purchaseReturn.$.save();

                assert.equal(await purchaseReturn.lines.length, 1);
                const purchaseReturnLine = await purchaseReturn.lines.elementAt(0);
                assert.isNotNull(await purchaseReturnLine.purchaseReceiptLine);
                assert.equal((await (await purchaseReturnLine.purchaseReceiptLine).returnedQuantity).toString(), '7');
                assert.equal(
                    (await (await purchaseReturnLine.purchaseReceiptLine).returnedQuantityInStockUnit).toString(),
                    '7000',
                );

                newPurchaseReceipt = await context.read(xtremPurchasing.nodes.PurchaseReceipt, {
                    _id: newPurchaseReceipt._id,
                });
                assert.equal(await (await newPurchaseReceipt.lines.elementAt(0)).lineReturnStatus, 'partiallyReturned');
                assert.equal(await newPurchaseReceipt.status, 'inProgress');

                newPurchaseReceipt = await context.read(xtremPurchasing.nodes.PurchaseReceipt, {
                    _id: newPurchaseReceipt._id,
                });
                purchaseReturn = await context.create(xtremPurchasing.nodes.PurchaseReturn, {
                    returnSite: '#US001',
                    businessRelation: '#LECLERC',
                    currency: '#USD',
                    returnRequestDate: date.today(),
                    lines: [
                        {
                            item: '#Muesli',
                            quantity: 3,
                            unit: '#KILOGRAM',
                            grossPrice: 30,
                            purchaseReceiptLine: {
                                purchaseReceiptLine: (await newPurchaseReceipt.lines.elementAt(0))._id,
                                returnedQuantity: 3,
                            },
                            reason: { id: 'R1' },
                        },
                    ],
                });
                assert.equal(
                    (
                        await (
                            await (
                                await purchaseReturn.lines.elementAt(0)
                            ).purchaseReceiptLine
                        ).returnedQuantityInStockUnit
                    ).toString(),
                    '3000',
                );
                await purchaseReturn.$.save();

                newPurchaseReceipt = await context.read(xtremPurchasing.nodes.PurchaseReceipt, {
                    _id: newPurchaseReceipt._id,
                });
                assert.equal(await (await newPurchaseReceipt.lines.elementAt(0)).lineReturnStatus, 'returned');
                assert.equal(await newPurchaseReceipt.status, 'closed');
            },
            { today: '2020-08-10' },
        ));
    it('Create return for purchase receipt PR9 - isTransferHeaderNote = true isTransferLineNote = true', () =>
        Test.withContext(
            async context => {
                const purchaseReceipt = await context.read(
                    xtremPurchasing.nodes.PurchaseReceipt,
                    { number: 'PR9' },
                    { forUpdate: true },
                );
                await purchaseReceipt.$.set({ isTransferHeaderNote: true });
                await purchaseReceipt.$.set({ isTransferLineNote: true });
                await purchaseReceipt.$.save();

                const ret9 = await context.read(xtremPurchasing.nodes.PurchaseReceipt, { number: 'PR9' });
                const [returns] = await xtremPurchasing.nodes.PurchaseReceipt.createPurchaseReturns(context, ret9);
                await context.flushDeferredActions();
                assert.equal(await returns.number, 'PT230001');
                const purchaseReturn = await context.read(xtremPurchasing.nodes.PurchaseReturn, {
                    number: await returns.number,
                });
                assert.equal(
                    (await purchaseReturn.internalNote).toString(),
                    (await purchaseReceipt.internalNote).toString(),
                );
                assert.equal(await purchaseReturn.isTransferHeaderNote, await purchaseReceipt.isTransferHeaderNote);
                assert.equal(await purchaseReturn.isTransferLineNote, await purchaseReceipt.isTransferLineNote);

                const purchaseReturnLine1 = await purchaseReturn.lines.elementAt(0);
                const purchaseReceiptLine1 = await purchaseReceipt.lines.elementAt(0);
                assert.equal(
                    (await purchaseReturnLine1.internalNote).toString(),
                    (await purchaseReceiptLine1.internalNote).toString(),
                );

                const purchaseReturnLine2 = await purchaseReturn.lines.elementAt(1);
                const purchaseReceiptLine2 = await purchaseReceipt.lines.elementAt(1);
                assert.equal(
                    (await purchaseReturnLine2.internalNote).toString(),
                    (await purchaseReceiptLine2.internalNote).toString(),
                );
            },
            { today: '2023-12-15' },
        ));
    it('Create return for purchase receipt PR9 - isTransferHeaderNote = false isTransferLineNote = true', () =>
        Test.withContext(
            async context => {
                const purchaseReceipt = await context.read(
                    xtremPurchasing.nodes.PurchaseReceipt,
                    { number: 'PR9' },
                    { forUpdate: true },
                );
                await purchaseReceipt.$.set({ isTransferHeaderNote: false });
                await purchaseReceipt.$.set({ isTransferLineNote: true });
                await purchaseReceipt.$.save();

                const ret9 = await context.read(xtremPurchasing.nodes.PurchaseReceipt, { number: 'PR9' });
                const [returns] = await xtremPurchasing.nodes.PurchaseReceipt.createPurchaseReturns(context, ret9);
                await context.flushDeferredActions();
                assert.equal(await returns.number, 'PT230001');
                const purchaseReturn = await context.read(xtremPurchasing.nodes.PurchaseReturn, {
                    number: await returns.number,
                });

                // Must be the same of the supplier
                assert.equal(
                    (await purchaseReturn.internalNote).toString(),
                    (await (await purchaseReturn.businessRelation).internalNote).toString(),
                );

                assert.notEqual(
                    (await purchaseReturn.internalNote).toString(),
                    (await purchaseReceipt.internalNote).toString(),
                );
                assert.equal(await purchaseReturn.isTransferHeaderNote, await purchaseReceipt.isTransferHeaderNote);
                assert.equal(await purchaseReturn.isTransferLineNote, await purchaseReceipt.isTransferLineNote);

                const purchaseReturnLine1 = await purchaseReturn.lines.elementAt(0);
                const purchaseReceiptLine1 = await purchaseReceipt.lines.elementAt(0);
                assert.equal(
                    (await purchaseReturnLine1.internalNote).toString(),
                    (await purchaseReceiptLine1.internalNote).toString(),
                );

                const purchaseReturnLine2 = await purchaseReturn.lines.elementAt(1);
                const purchaseReceiptLine2 = await purchaseReceipt.lines.elementAt(1);
                assert.equal(
                    (await purchaseReturnLine2.internalNote).toString(),
                    (await purchaseReceiptLine2.internalNote).toString(),
                );
            },
            { today: '2023-12-15' },
        ));
    it('Create return for purchase receipt PR9 - isTransferHeaderNote = true isTransferLineNote = false', () =>
        Test.withContext(
            async context => {
                const purchaseReceipt = await context.read(
                    xtremPurchasing.nodes.PurchaseReceipt,
                    { number: 'PR9' },
                    { forUpdate: true },
                );
                await purchaseReceipt.$.set({ isTransferHeaderNote: true });
                await purchaseReceipt.$.set({ isTransferLineNote: false });
                await purchaseReceipt.$.save();

                const ret9 = await context.read(xtremPurchasing.nodes.PurchaseReceipt, { number: 'PR9' });
                const [returns] = await xtremPurchasing.nodes.PurchaseReceipt.createPurchaseReturns(context, ret9);
                await context.flushDeferredActions();
                assert.equal(await returns.number, 'PT230001');
                const purchaseReturn = await context.read(xtremPurchasing.nodes.PurchaseReturn, {
                    number: await returns.number,
                });

                assert.equal(
                    (await purchaseReturn.internalNote).toString(),
                    (await purchaseReceipt.internalNote).toString(),
                );
                assert.equal(await purchaseReturn.isTransferHeaderNote, await purchaseReceipt.isTransferHeaderNote);
                assert.equal(await purchaseReturn.isTransferLineNote, await purchaseReceipt.isTransferLineNote);

                const purchaseReturnLine1 = await purchaseReturn.lines.elementAt(0);
                const purchaseReceiptLine1 = await purchaseReceipt.lines.elementAt(0);
                assert.notEqual(
                    (await purchaseReturnLine1.internalNote).toString(),
                    (await purchaseReceiptLine1.internalNote).toString(),
                );
                assert.isEmpty((await purchaseReturnLine1.internalNote).toString());

                const purchaseReturnLine2 = await purchaseReturn.lines.elementAt(1);
                const purchaseReceiptLine2 = await purchaseReceipt.lines.elementAt(1);
                assert.notEqual(
                    (await purchaseReturnLine2.internalNote).toString(),
                    (await purchaseReceiptLine2.internalNote).toString(),
                );
                assert.isEmpty((await purchaseReturnLine2.internalNote).toString());
            },
            { today: '2023-12-15' },
        ));
    it('Create return for purchase receipt PR9 - isTransferHeaderNote = false isTransferLineNote = false', () =>
        Test.withContext(
            async context => {
                const purchaseReceipt = await context.read(
                    xtremPurchasing.nodes.PurchaseReceipt,
                    { number: 'PR9' },
                    { forUpdate: true },
                );
                await purchaseReceipt.$.set({ isTransferHeaderNote: false });
                await purchaseReceipt.$.set({ isTransferLineNote: false });
                await purchaseReceipt.$.save();

                const ret9 = await context.read(xtremPurchasing.nodes.PurchaseReceipt, { number: 'PR9' });
                const [returns] = await xtremPurchasing.nodes.PurchaseReceipt.createPurchaseReturns(context, ret9);
                await context.flushDeferredActions();
                assert.equal(await returns.number, 'PT230001');
                const purchaseReturn = await context.read(xtremPurchasing.nodes.PurchaseReturn, {
                    number: await returns.number,
                });

                // Must be the same of the supplier
                assert.equal(
                    (await purchaseReturn.internalNote).toString(),
                    (await (await purchaseReturn.businessRelation).internalNote).toString(),
                );

                assert.notEqual(
                    (await purchaseReturn.internalNote).toString(),
                    (await purchaseReceipt.internalNote).toString(),
                );
                assert.equal(await purchaseReturn.isTransferHeaderNote, await purchaseReceipt.isTransferHeaderNote);
                assert.equal(await purchaseReturn.isTransferLineNote, await purchaseReceipt.isTransferLineNote);

                const purchaseReturnLine1 = await purchaseReturn.lines.elementAt(0);
                const purchaseReceiptLine1 = await purchaseReceipt.lines.elementAt(0);
                assert.notEqual(
                    (await purchaseReturnLine1.internalNote).toString(),
                    (await purchaseReceiptLine1.internalNote).toString(),
                );
                assert.isEmpty((await purchaseReturnLine1.internalNote).toString());

                const purchaseReturnLine2 = await purchaseReturn.lines.elementAt(1);
                const purchaseReceiptLine2 = await purchaseReceipt.lines.elementAt(1);
                assert.notEqual(
                    (await purchaseReturnLine2.internalNote).toString(),
                    (await purchaseReceiptLine2.internalNote).toString(),
                );
                assert.isEmpty((await purchaseReturnLine2.internalNote).toString());
            },
            { today: '2023-12-15' },
        ));
});

describe('Purchase receipt - check if price has changed', () => {
    it('Create purchase receipt and then fully invoice as the same price then compare', () =>
        Test.withContext(async context => {
            const newPurchaseReceipt = await context.create(xtremPurchasing.nodes.PurchaseReceipt, {
                site: '#US001',
                businessRelation: '#LECLERC',
                currency: '#USD',
                status: 'pending',
                date: date.today(),
                lines: [
                    {
                        item: '#Muesli',
                        status: 'pending',
                        quantity: 10,
                        unit: '#KILOGRAM',
                        grossPrice: 30,
                        stockTransactionStatus: 'completed',
                    },
                ],
            });

            await newPurchaseReceipt.$.save();

            const purchaseInvoice = await context.create(xtremPurchasing.nodes.PurchaseInvoice, {
                site: '#US001',
                billBySupplier: '#LECLERC',
                currency: '#USD',
                invoiceDate: date.today(),
                totalAmountExcludingTax: await newPurchaseReceipt.totalAmountExcludingTax,
                lines: [
                    {
                        item: '#Muesli',
                        quantity: 10,
                        unit: '#KILOGRAM',
                        grossPrice: 30,
                        purchaseReceiptLine: {
                            purchaseReceiptLine: (await newPurchaseReceipt.lines.elementAt(0))._id,
                        },
                    },
                ],
            });
            await purchaseInvoice.$.save();
            assert.isFalse(
                await xtremPurchasing.functions.isDocumentLineCostChanged(await purchaseInvoice.lines.elementAt(0)),
            );
        }));
    it('Create purchase invoice then compare', () =>
        Test.withContext(async context => {
            const purchaseInvoice = await context.create(xtremPurchasing.nodes.PurchaseInvoice, {
                site: '#US001',
                billBySupplier: '#LECLERC',
                currency: '#USD',
                totalAmountExcludingTax: 240,
                lines: [
                    {
                        item: '#SalesItem81',
                        quantity: 6,
                        unit: '#GRAM',
                        grossPrice: 40,
                        quantityInStockUnit: 6,
                        unitToStockUnitConversionFactor: 1,
                    },
                ],
            });
            await purchaseInvoice.$.save();
            assert.deepEqual(purchaseInvoice.$.context.diagnoses, [
                {
                    message: 'The item: SalesItem81, is not managed for the supplier: LECLERC.',
                    path: ['lines', '-1000000002'],
                    severity: 2,
                },
            ]);
            await purchaseInvoice.$.save();
            assert.isFalse(
                await xtremPurchasing.functions.isDocumentLineCostChanged(await purchaseInvoice.lines.elementAt(0)),
            );
        }));
    it('Create purchase receipt and then fully invoice it with another price then compare', () =>
        Test.withContext(async context => {
            const newPurchaseReceipt = await context.create(xtremPurchasing.nodes.PurchaseReceipt, {
                site: '#US001',
                businessRelation: '#LECLERC',
                currency: '#USD',
                status: 'pending',
                date: date.today(),
                lines: [
                    {
                        item: '#Muesli',
                        status: 'pending',
                        quantity: 10,
                        unit: '#KILOGRAM',
                        grossPrice: 30,
                        stockTransactionStatus: 'completed',
                    },
                ],
            });
            await newPurchaseReceipt.$.save();

            const purchaseInvoice = await context.create(xtremPurchasing.nodes.PurchaseInvoice, {
                site: '#US001',
                billBySupplier: '#LECLERC',
                currency: '#USD',
                invoiceDate: date.today(),
                totalAmountExcludingTax: await newPurchaseReceipt.totalAmountExcludingTax,
                lines: [
                    {
                        item: '#Muesli',
                        quantity: 10,
                        unit: '#KILOGRAM',
                        grossPrice: 300,
                        purchaseReceiptLine: {
                            purchaseReceiptLine: (await newPurchaseReceipt.lines.elementAt(0))._id,
                        },
                    },
                ],
            });
            await purchaseInvoice.$.save();
            assert.isTrue(
                await xtremPurchasing.functions.isDocumentLineCostChanged(await purchaseInvoice.lines.elementAt(0)),
            );
        }));
    it('Create purchase receipt and then fully invoice it with lower price then compare', () =>
        Test.withContext(async context => {
            const newPurchaseReceipt = await context.create(xtremPurchasing.nodes.PurchaseReceipt, {
                site: '#US001',
                businessRelation: '#LECLERC',
                currency: '#USD',
                status: 'pending',
                date: date.today(),
                lines: [
                    {
                        item: '#Muesli',
                        status: 'pending',
                        quantity: 10,
                        unit: '#KILOGRAM',
                        grossPrice: 30,
                        stockTransactionStatus: 'completed',
                    },
                ],
            });
            await newPurchaseReceipt.$.save();

            const purchaseInvoice = await context.create(xtremPurchasing.nodes.PurchaseInvoice, {
                site: '#US001',
                billBySupplier: '#LECLERC',
                currency: '#USD',
                invoiceDate: date.today(),
                totalAmountExcludingTax: await newPurchaseReceipt.totalAmountExcludingTax,
                lines: [
                    {
                        item: '#Muesli',
                        quantity: 10,
                        unit: '#KILOGRAM',
                        grossPrice: 3,
                        purchaseReceiptLine: {
                            purchaseReceiptLine: (await newPurchaseReceipt.lines.elementAt(0))._id,
                        },
                    },
                ],
            });
            await purchaseInvoice.$.save();
            assert.isTrue(
                await xtremPurchasing.functions.isDocumentLineCostChanged(await purchaseInvoice.lines.elementAt(0)),
            );
        }));

    it('Create purchase receipt and then return it and issue a credit memo with same price then compare', () =>
        Test.withContext(async context => {
            const newPurchaseReceipt = await context.create(xtremPurchasing.nodes.PurchaseReceipt, {
                site: '#US001',
                businessRelation: '#LECLERC',
                currency: '#USD',
                status: 'pending',
                date: date.today(),
                lines: [
                    {
                        item: '#Muesli',
                        status: 'pending',
                        quantity: 10,
                        unit: '#KILOGRAM',
                        grossPrice: 30,
                    },
                ],
            });
            await newPurchaseReceipt.$.save();

            const purchaseReturn = await context.create(xtremPurchasing.nodes.PurchaseReturn, {
                returnSite: '#US001',
                businessRelation: '#LECLERC',
                currency: '#USD',
                returnRequestDate: date.today(),
                lines: [
                    {
                        item: '#Muesli',
                        quantity: 6,
                        unit: '#KILOGRAM',
                        grossPrice: 30,
                        purchaseReceiptLine: {
                            purchaseReceiptLine: (await newPurchaseReceipt.lines.elementAt(0))._id,
                            returnedQuantity: 6,
                        },
                        reason: { id: 'R1' },
                    },
                ],
            });
            await purchaseReturn.$.save();

            const purchaseCreditMemo = await context.create(xtremPurchasing.nodes.PurchaseCreditMemo, {
                site: '#US001',
                billBySupplier: '#LECLERC',
                currency: '#USD',
                totalAmountExcludingTax: 180,
                reason: { id: 'R1' },
                lines: [
                    {
                        item: '#Muesli',
                        quantity: 6,
                        unit: '#KILOGRAM',
                        grossPrice: 30,
                        purchaseReturnLine: {
                            purchaseReturnLine: (await purchaseReturn.lines.elementAt(0))._id,
                        },
                    },
                ],
            });
            await purchaseCreditMemo.$.save();
            assert.isFalse(
                await xtremPurchasing.functions.isDocumentLineCostChanged(await purchaseCreditMemo.lines.elementAt(0)),
            );
        }));
    it('Create purchase receipt and then return it and issue a credit memo with higher price then compare', () =>
        Test.withContext(async context => {
            const newPurchaseReceipt = await context.create(xtremPurchasing.nodes.PurchaseReceipt, {
                site: '#US001',
                businessRelation: '#LECLERC',
                currency: '#USD',
                status: 'pending',
                date: date.today(),
                lines: [
                    {
                        item: '#Muesli',
                        status: 'pending',
                        quantity: 10,
                        unit: '#KILOGRAM',
                        grossPrice: 30,
                    },
                ],
            });
            await newPurchaseReceipt.$.save();

            const purchaseReturn = await context.create(xtremPurchasing.nodes.PurchaseReturn, {
                returnSite: '#US001',
                businessRelation: '#LECLERC',
                currency: '#USD',
                returnRequestDate: date.today(),
                lines: [
                    {
                        item: '#Muesli',
                        quantity: 6,
                        unit: '#KILOGRAM',
                        grossPrice: 30,
                        purchaseReceiptLine: {
                            purchaseReceiptLine: (await newPurchaseReceipt.lines.elementAt(0))._id,
                            returnedQuantity: 6,
                        },
                        reason: { id: 'R1' },
                    },
                ],
            });
            await purchaseReturn.$.save();

            const purchaseCreditMemo = await context.create(xtremPurchasing.nodes.PurchaseCreditMemo, {
                site: '#US001',
                billBySupplier: '#LECLERC',
                currency: '#USD',
                totalAmountExcludingTax: 240,
                reason: { id: 'R1' },
                lines: [
                    {
                        item: '#Muesli',
                        quantity: 6,
                        unit: '#KILOGRAM',
                        grossPrice: 40,
                        purchaseReturnLine: {
                            purchaseReturnLine: (await purchaseReturn.lines.elementAt(0))._id,
                        },
                    },
                ],
            });
            await purchaseCreditMemo.$.save();
            assert.isTrue(
                await xtremPurchasing.functions.isDocumentLineCostChanged(await purchaseCreditMemo.lines.elementAt(0)),
            );
        }));
    it('Create credit memo without return then compare', () =>
        Test.withContext(async context => {
            const purchaseCreditMemo = await context.create(xtremPurchasing.nodes.PurchaseCreditMemo, {
                site: '#US001',
                billBySupplier: '#LECLERC',
                currency: '#USD',
                totalAmountExcludingTax: 240,
                reason: { id: 'R1' },
                lines: [
                    {
                        item: '#SalesItem81',
                        quantity: 6,
                        unit: '#GRAM',
                        grossPrice: 40,
                        quantityInStockUnit: 6,
                        unitToStockUnitConversionFactor: 1,
                    },
                ],
            });
            await purchaseCreditMemo.$.save();
            assert.deepEqual(purchaseCreditMemo.$.context.diagnoses, [
                {
                    message: 'The item: SalesItem81, is not managed for the supplier: LECLERC.',
                    path: ['lines', '-1000000002'],
                    severity: 2,
                },
            ]);
            await purchaseCreditMemo.$.save();
            assert.isFalse(
                await xtremPurchasing.functions.isDocumentLineCostChanged(await purchaseCreditMemo.lines.elementAt(0)),
            );
        }));
    it('Verify chronological control between two purchase receipts on creation', () =>
        withSequenceNumberContext('PurchaseReceipt', { isChronological: true }, async context => {
            const newPurchaseReceipt = await context.create(xtremPurchasing.nodes.PurchaseReceipt, {
                site: '#US001',
                businessRelation: '#LECLERC',
                currency: '#USD',
                status: 'pending',
                date: date.make(2022, 2, 2),
                lines: [
                    {
                        item: '#Muesli',
                        status: 'pending',
                        quantity: 10,
                        unit: '#KILOGRAM',
                        grossPrice: 30,
                    },
                ],
            });
            await newPurchaseReceipt.$.save({ flushDeferredActions: true });
            assert.deepEqual(newPurchaseReceipt.$.context.diagnoses, []);

            const newPurchaseReceiptError = await context.create(xtremPurchasing.nodes.PurchaseReceipt, {
                site: '#US001',
                businessRelation: '#LECLERC',
                currency: '#USD',
                status: 'pending',
                date: date.make(2022, 2, 1),
                lines: [
                    {
                        item: '#Muesli',
                        status: 'pending',
                        quantity: 10,
                        unit: '#KILOGRAM',
                        grossPrice: 30,
                    },
                ],
            });
            await assert.isRejected(newPurchaseReceiptError.$.save(), 'The record was not created.');
            assert.deepEqual(newPurchaseReceiptError.$.context.diagnoses, [
                {
                    severity: 3,
                    path: [],
                    message: 'The document date 2022-02-01 is earlier than the previous document date 2022-02-02.',
                },
            ]);
        }));

    it('Create purchase receipt default dimensions', () =>
        Test.withContext(async context => {
            const newPurchaseReceipt = await context.create(xtremPurchasing.nodes.PurchaseReceipt, {
                site: '#US001',
                businessRelation: 3,
                currency: '#USD',
                status: 'pending',
                date: date.today(),
                lines: [
                    {
                        item: '#Muesli',
                        status: 'pending',
                        quantity: 10,
                        unit: '#KILOGRAM',
                        grossPrice: 30,
                    },
                ],
            });
            await newPurchaseReceipt.$.save({ flushDeferredActions: true });

            // default dimensions and attributes
            const expectedAttributes = { project: 'AttPROJ', task: 'Task1' };
            const expectedDimensions = {
                dimensionType01: '300',
                dimensionType02: 'CHANNELVALUE1',
            };
            const attributesLine1 = await (await newPurchaseReceipt.lines.elementAt(0)).storedAttributes;
            const dimensionsLine1 = await (await newPurchaseReceipt.lines.elementAt(0)).storedDimensions;
            assert.equal(JSON.stringify(attributesLine1), JSON.stringify(expectedAttributes));
            assert.deepEqual(dimensionsLine1, expectedDimensions);
        }));

    it('Create purchase receipt attribute type restricted to to', () =>
        Test.withContext(
            async context => {
                const newPurchaseReceipt = await context.create(xtremPurchasing.nodes.PurchaseReceipt, {
                    site: '#US001',
                    businessRelation: '#LECLERC',
                    currency: '#USD',
                    date: date.make(2020, 8, 10),
                    lines: [
                        {
                            item: '#Muesli',
                            quantity: 10,
                            unit: '#GRAM',
                            grossPrice: 30,
                            storedAttributes: { employee: '', task: 'TASK1', project: '' },
                        },
                    ],
                });
                await assert.isRejected(newPurchaseReceipt.$.save());

                assert.deepEqual(newPurchaseReceipt.$.context.diagnoses, [
                    {
                        severity: 3,
                        path: ['lines', '-1000000002', 'storedAttributes'],
                        message: 'The Project attribute needs to be filled in.',
                    },
                ]);
            },
            { today: '2020-08-10' },
        ));

    it('Create purchase receipt default dimensions attributes', () =>
        Test.withContext(
            async context => {
                const newPurchaseReceipt = await context.create(xtremPurchasing.nodes.PurchaseReceipt, {
                    site: '#US001',
                    businessRelation: '#LECLERC',
                    currency: '#USD',
                    date: date.make(2020, 8, 10),
                    lines: [
                        {
                            item: '#Muesli',
                            quantity: 10,
                            unit: '#GRAM',
                            grossPrice: 30,
                        },
                    ],
                });
                await newPurchaseReceipt.$.save();

                assert.deepEqual(await (await newPurchaseReceipt.lines.elementAt(0)).storedDimensions, {
                    dimensionType01: '300',
                    dimensionType02: 'CHANNELVALUE1',
                });

                assert.equal(
                    JSON.stringify(await (await newPurchaseReceipt.lines.elementAt(0)).storedAttributes),
                    JSON.stringify({
                        project: 'AttPROJ',
                        task: 'Task1',
                    }),
                );
            },
            { today: '2020-08-10' },
        ));
});
