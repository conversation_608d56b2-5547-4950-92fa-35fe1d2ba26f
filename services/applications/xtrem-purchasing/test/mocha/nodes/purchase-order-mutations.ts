import { Test } from '@sage/xtrem-core';
import { assert } from 'chai';
import * as xtremPurchasing from '../../../lib';

describe('Purchase order node - Mutations tests  ', () => {
    it('Set isSent and isPrinted to a confirmed PO', () =>
        Test.withContext(async context => {
            const po32 = await context.read(
                xtremPurchasing.nodes.PurchaseOrder,
                { number: 'PO32' },
                { forUpdate: true },
            );
            assert.equal(await po32.isSent, false);
            assert.equal(await po32.isPrinted, false);

            const setIsSentPurchaseOrder = await xtremPurchasing.nodes.PurchaseOrder.setIsSentPurchaseOrder(
                context,
                po32,
            );
            assert.equal(setIsSentPurchaseOrder, true);

            assert.equal(await po32.isSent, true);
            assert.equal(await po32.isPrinted, true);
        }));
});
