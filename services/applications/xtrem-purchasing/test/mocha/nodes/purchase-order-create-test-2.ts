import { Test, date } from '@sage/xtrem-core';
import * as xtremSystem from '@sage/xtrem-system';
import { assert } from 'chai';
import * as xtremPurchasing from '../../../lib';

describe('Purchase order node - work in progress site', () => {
    it('Create purchase order', () =>
        Test.withContext(
            async context => {
                const newPurchaseOrder = await context.create(xtremPurchasing.nodes.PurchaseOrder, {
                    site: '#US001',
                    businessRelation: '#LECLERC',
                    currency: '#USD',
                    fxRateDate: date.today(),
                    orderDate: date.today(),
                    lines: [
                        {
                            item: '#Muesli',
                            quantity: 10,
                            unit: '#GRAM',
                            grossPrice: 30,
                            expectedReceiptDate: date.today(),
                            stockSite: '#US002',
                        },
                    ],
                });
                const wipSite = await (await (await newPurchaseOrder.lines.elementAt(0)).workInProgress)?.site;
                assert.equal(wipSite?._id, (await context.read(xtremSystem.nodes.Site, { _id: '#US002' }))._id);
            },
            { today: '2025-07-21' },
        ));
});
