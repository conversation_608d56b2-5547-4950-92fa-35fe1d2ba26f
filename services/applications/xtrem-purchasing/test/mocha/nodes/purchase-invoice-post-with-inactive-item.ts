import type { Context, NodeCreateData } from '@sage/xtrem-core';
import { Test, date } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import { assert } from 'chai';
import * as xtremPurchasing from '../../../index';

describe('PurchaseInvoiceNode', () => {
    const itemKey = '#NonStockManagedItem';

    function createPurchaseInvoice(context: Context) {
        const baseData: NodeCreateData<xtremPurchasing.nodes.PurchaseInvoice> = {
            site: '#US001',
            billBySupplier: '#US016',
            currency: '#EUR',
            invoiceDate: date.today(),
            matchingStatus: 'varianceApproved',
            taxCalculationStatus: 'done',
            totalAmountExcludingTax: 1,
            totalTaxAmount: 0,
            lines: [
                {
                    _action: 'create',
                    item: itemKey,
                    quantity: 1,
                    unit: '#KILOGRAM',
                    grossPrice: 1,
                },
            ],
            matchingUser: '#<EMAIL>',
        };
        return context.create(xtremPurchasing.nodes.PurchaseInvoice, baseData);
    }

    it('Purchase invoices with lines containing inactive items cannot be posted.', () =>
        Test.withContext(
            async context => {
                const invoice: xtremPurchasing.nodes.PurchaseInvoice = await createPurchaseInvoice(context);

                // Set the item to obsolete (one of several inactive states)
                const item = await context.read(xtremMasterData.nodes.Item, { _id: itemKey }, { forUpdate: true });
                await item.$.set({ status: 'obsolete' });
                await item.$.save();

                await assert.isRejected(
                    xtremPurchasing.nodes.PurchaseInvoice.post(context, invoice),
                    'The record was not created.',
                );
                assert.deepEqual(context.diagnoses, [
                    {
                        path: ['lines', '-1000000002', 'item'],
                        severity: 3,
                        message: 'You need to remove the inactive items before you change the document status.',
                    },
                    {
                        path: ['lines', '-1000000002'],
                        severity: 2,
                        message: 'The item: NonStockManagedItem, is not managed for the supplier: US016.',
                    },
                ]);
            },
            { today: '2025-07-04' },
        ));
});
