import { Test, date } from '@sage/xtrem-core';
import { assert } from 'chai';
import * as xtremPurchasing from '../../../lib';
import { updatePendingApproval } from '../../../lib/functions/document-approval';

describe('Purchase order node - approvals tests   ', () => {
    it('Create an order pending approval refuse', () =>
        Test.withContext(async context => {
            const newPurchaseOrder = await context.create(xtremPurchasing.nodes.PurchaseOrder, {
                site: '#US001',
                businessRelation: '#LECLERC',
                currency: '#USD',
                fxRateDate: date.make(2020, 8, 10),
                orderDate: date.make(2020, 8, 10),
                lines: [
                    {
                        item: '#Muesli',
                        quantity: 10,
                        unit: '#GRAM',
                        grossPrice: 30,
                        expectedReceiptDate: date.today(),
                    },
                ],
            });
            await newPurchaseOrder.$.save({ flushDeferredActions: true });

            await updatePendingApproval(newPurchaseOrder);

            await xtremPurchasing.functions.PurchaseOrderLib.managePurchaseOrderStatusApproval(context, {
                document: newPurchaseOrder,
                isApproved: false,
            });

            const purchaseOrder = await context.read(xtremPurchasing.nodes.PurchaseOrder, {
                _id: newPurchaseOrder._id,
            });
            assert.equal(await purchaseOrder.approvalStatus, 'rejected');
            assert.equal(await purchaseOrder.status, 'closed');

            assert.isTrue(await purchaseOrder.lines.every(async line => (await line.status) === 'closed'));
        }));

    it('Confirm purchase order with different stock site for header & line', () =>
        Test.withContext(
            async context => {
                const newPurchaseOrder = await context.create(xtremPurchasing.nodes.PurchaseOrder, {
                    number: 'TEST-XT-91137',
                    site: '#CAS01',
                    stockSite: '#CAS01',
                    businessRelation: '#500',
                    currency: '#EUR',
                    fxRateDate: date.make(2020, 8, 10),
                    orderDate: date.make(2020, 8, 10),
                    lines: [
                        {
                            item: '#Milk',
                            stockSite: '#CAS02',
                            quantity: 100,
                            unit: '#KILOGRAM',
                            grossPrice: 100,
                            expectedReceiptDate: date.today(),
                        },
                    ],
                });
                await newPurchaseOrder.$.save({ flushDeferredActions: true });
                assert.deepEqual(newPurchaseOrder.$.context.diagnoses, []);

                let purchaseOrder = await context.read(
                    xtremPurchasing.nodes.PurchaseOrder,
                    {
                        number: 'TEST-XT-91137',
                    },
                    { forUpdate: true },
                );
                const confirmResult = await xtremPurchasing.nodes.PurchaseOrder.confirm(
                    context,
                    newPurchaseOrder,
                    true,
                );
                assert.isTrue(confirmResult);

                purchaseOrder = await context.read(xtremPurchasing.nodes.PurchaseOrder, {
                    number: 'TEST-XT-91137',
                });
                const purchaseOrderLine = await purchaseOrder.lines.at(0);
                assert.isDefined(purchaseOrderLine);
                assert.equal(await purchaseOrder.status, 'pending');
                assert.equal(await purchaseOrder.approvalStatus, 'confirmed');
                assert.equal(await purchaseOrder.receiptStatus, 'notReceived');
                assert.equal(await (await purchaseOrder.stockSite).id, 'CAS01');
                assert.equal(await (await purchaseOrderLine.stockSite).id, 'CAS02');
                assert.isFalse(await (await purchaseOrderLine.stockSite).isFinance);
            },
            { today: '2025-06-13' },
        ));
});
