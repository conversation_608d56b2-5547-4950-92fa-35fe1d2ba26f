import type { Context } from '@sage/xtrem-core';
import { date, Test } from '@sage/xtrem-core';
import * as xtremDistribution from '@sage/xtrem-distribution';
import { assert } from 'chai';
import * as xtremPurchasing from '../../../lib';

describe('Purchase order node', () => {
    it('Control on purchase order with wrong tax type', () =>
        Test.withContext(async context => {
            const purchaseOrder = await context.create(xtremPurchasing.nodes.PurchaseOrder, {
                site: '#US001',
                businessRelation: '#LECLERC',
                currency: '#USD',
                fxRateDate: date.make(2020, 8, 10),
                orderDate: date.make(2020, 8, 10),
                lines: [
                    {
                        item: '#Muesli',
                        quantity: 10,
                        unit: '#GRAM',
                        grossPrice: 30,
                        expectedReceiptDate: date.today(),
                        taxes: [
                            {
                                taxReference: '#FR_TVA_INTERMEDIATE_COLLECTED_ON_PAYMENT',
                                taxRate: 20,
                                taxAmount: 10,
                            },
                        ],
                    },
                ],
            });

            await assert.isRejected(purchaseOrder.$.save());
            assert.deepEqual(purchaseOrder.$.context.diagnoses, [
                {
                    severity: 3,
                    path: [],
                    message: "The tax type for all documents needs to be 'Purchasing' or 'Purchasing and sales'.",
                },
            ]);
        }));

    it('Control on purchase order with correct tax type', () =>
        Test.withContext(async context => {
            const purchaseOrder = await context.create(xtremPurchasing.nodes.PurchaseOrder, {
                site: '#US001',
                businessRelation: '#LECLERC',
                currency: '#USD',
                fxRateDate: date.make(2020, 8, 10),
                orderDate: date.make(2020, 8, 10),
                lines: [
                    {
                        item: '#Muesli',
                        quantity: 10,
                        unit: '#GRAM',
                        grossPrice: 30,
                        expectedReceiptDate: date.today(),
                        taxes: [
                            {
                                taxReference: '#FR_TVA_NORMAL_DEDUCTIBLE_FA_INTRASTAT',
                                taxRate: 20,
                                taxAmount: 10,
                            },
                        ],
                    },
                ],
            });

            assert.isOk(await purchaseOrder.$.control());
        }));

    it('Returns true on isClosedOrReceived prop if status is either closed or received', () =>
        Test.withContext(async context => {
            const order = await context.read(xtremPurchasing.nodes.PurchaseOrder, { number: 'PO9' });
            assert.equal(await order.status, 'closed');
            assert.equal(await order.isClosedOrReceived, true);
        }));

    it('Returns false on isClosedOrReceived prop if status is neither closed nor received', () =>
        Test.withContext(async context => {
            const order = await context.read(xtremPurchasing.nodes.PurchaseOrder, { number: 'PO3' });
            assert.equal(await order.status, 'pending');
            assert.equal(await order.isClosedOrReceived, false);
        }));

    it('Sets isClosedOrReceived props to true, once the purchaseOrder is closed', () =>
        Test.withContext(async context => {
            const orderPo3 = await context.read(
                xtremPurchasing.nodes.PurchaseOrder,
                { number: 'PO3' },
                { forUpdate: true },
            );
            assert.equal(await orderPo3.status, 'pending');
            assert.equal(await orderPo3.isClosedOrReceived, false);

            await orderPo3.$.set({ totalAmountExcludingTax: 1500 });

            await orderPo3.$.set({ status: 'closed' });
            assert.equal(await orderPo3.isClosedOrReceived, false);

            // TODO: /** Waiting fo XT-82531 to enable again */
            // await assert.isRejected(
            //     orderPo3.$.set({ totalAmountExcludingTax: 1000 }),
            //     'PurchaseOrder.totalAmountExcludingTax: cannot set value on frozen property',
            // );
        }));
});

describe('Pre/Post printing mutations', () => {
    it('purchaseOrder - beforePrintPurchaseOrder - taxCalculationStatus', () =>
        Test.withContext(async context => {
            let purchaseOrder = await context.read(xtremPurchasing.nodes.PurchaseOrder, { _id: '#PO38' });
            const baseDistributionDocument = await context.read(
                xtremDistribution.nodes.BaseDistributionDocument,
                { _id: purchaseOrder._id },
                { forUpdate: true },
            );
            await baseDistributionDocument.$.set({ taxCalculationStatus: 'failed' });
            await baseDistributionDocument.$.save();

            purchaseOrder = await context.read(xtremPurchasing.nodes.PurchaseOrder, { _id: '#PO38' });
            assert.equal(await purchaseOrder.taxCalculationStatus, 'failed');
            await assert.isRejected(
                xtremPurchasing.nodes.PurchaseOrder.beforePrintPurchaseOrder(context, purchaseOrder),
                'The tax calculation for this order failed. You can print this order after the tax details are corrected.',
            );
        }));
    it('purchaseOrder - beforePrintPurchaseOrder - status', () =>
        Test.withContext(async context => {
            let purchaseOrder = await context.read(xtremPurchasing.nodes.PurchaseOrder, { _id: '#PO38' });
            const baseDistributionDocument = await context.read(
                xtremDistribution.nodes.BaseDistributionDocument,
                { _id: purchaseOrder._id },
                { forUpdate: true },
            );
            await baseDistributionDocument.$.set({ status: 'error' });
            await baseDistributionDocument.$.save();

            purchaseOrder = await context.read(xtremPurchasing.nodes.PurchaseOrder, { _id: '#PO38' });
            assert.equal(await purchaseOrder.status, 'error');
            await assert.isRejected(
                xtremPurchasing.nodes.PurchaseOrder.beforePrintPurchaseOrder(context, purchaseOrder),
                'You need to correct the order details before you can print the order.',
            );
        }));
    it('purchaseOrder - afterPrintPackingSlip - sets the isPrinted flag to true', () =>
        Test.withContext(async context => {
            let purchaseOrder = await context.read(
                xtremPurchasing.nodes.PurchaseOrder,
                { _id: '#PO38' },
                { forUpdate: true },
            );
            assert.isFalse(await purchaseOrder.isPrinted);

            const resultAfterPrint = await xtremPurchasing.nodes.PurchaseOrder.afterPrintPurchaseOrder(
                context,
                purchaseOrder,
            );
            assert.isTrue(resultAfterPrint);

            purchaseOrder = await context.read(xtremPurchasing.nodes.PurchaseOrder, { _id: '#PO38' });
            assert.isTrue(await purchaseOrder.isPrinted);
        }));
});

describe('purchase order resync', () => {
    async function checkStatusesAfterResync(context: Context, orderNumber: string) {
        const closedOrder = await context.read(
            xtremPurchasing.nodes.PurchaseOrder,
            { number: orderNumber },
            { forUpdate: true },
        );
        assert.equal(await closedOrder.receiptStatus, 'partiallyReceived');
        assert.equal(await closedOrder.displayStatus, 'partiallyReceived');

        const orderLine = await closedOrder.lines.at(0);
        assert.isNotNull(orderLine);
        assert.equal(await orderLine!.lineReceiptStatus, 'partiallyReceived');
        assert.equal(await orderLine!.quantityInStockUnit, 5);
        assert.equal(await orderLine!.receivedQuantityInStockUnit, 2);
    }

    it('update invoice status to invoiced if the line status is invoiced', () =>
        Test.withContext(async context => {
            let invoicedOrder = await context.read(
                xtremPurchasing.nodes.PurchaseOrder,
                { number: 'PO8003' },
                { forUpdate: true },
            );
            assert.equal(await invoicedOrder.invoiceStatus, 'notInvoiced');
            await invoicedOrder.resynchronize();
            invoicedOrder = await context.read(xtremPurchasing.nodes.PurchaseOrder, { number: 'PO8003' });
            assert.equal(await invoicedOrder.invoiceStatus, 'invoiced');
        }));

    it('do not change notInvoiced status to Invoiced if the line status is notInvoiced', () =>
        Test.withContext(async context => {
            let invoicedOrder = await context.read(
                xtremPurchasing.nodes.PurchaseOrder,
                { number: 'PO1' },
                { forUpdate: true },
            );
            assert.equal(await invoicedOrder.invoiceStatus, 'notInvoiced');
            await invoicedOrder.resynchronize();
            invoicedOrder = await context.read(xtremPurchasing.nodes.PurchaseOrder, { number: 'PO1' });
            assert.equal(await invoicedOrder.invoiceStatus, 'notInvoiced');
        }));

    it('update line & header receipt status from received to partially received for a closed order', () =>
        Test.withContext(async context => {
            const closedOrder = await context.read(
                xtremPurchasing.nodes.PurchaseOrder,
                { number: 'TEST94092' },
                { forUpdate: true },
            );
            // header before resync
            assert.equal(await closedOrder.status, 'closed');
            assert.equal(await closedOrder.receiptStatus, 'received');
            assert.equal(await closedOrder.displayStatus, 'received');
            // line before resync
            assert.equal(await closedOrder.lines.length, 1);
            const orderLine = await closedOrder.lines.at(0);
            assert.isNotNull(orderLine);
            assert.equal(await orderLine!.lineStatus, 'closed');
            assert.equal(await orderLine!.lineReceiptStatus, 'received');
            assert.equal(await orderLine!.quantityInStockUnit, 5);
            assert.equal(await orderLine!.receivedQuantityInStockUnit, 2);

            // 1st run to correct wrong statuses
            await closedOrder.resynchronize();
            await checkStatusesAfterResync(context, 'TEST94092');

            // 2nd run to check correction stability
            await closedOrder.resynchronize();
            await checkStatusesAfterResync(context, 'TEST94092');
        }));
});
