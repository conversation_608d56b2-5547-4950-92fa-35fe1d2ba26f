import { date, Test } from '@sage/xtrem-core';
import { assert } from 'chai';
import * as xtremPurchasing from '../../../lib';

describe('Unbilled accounts payable inquiry', () => {
    it('asyncMutation unbilledAccountPaybleInquiry', () =>
        Test.withContext(
            async context => {
                let inputSet: xtremPurchasing.nodes.UnbilledAccountPayableInputSet = await context.create(
                    xtremPurchasing.nodes.UnbilledAccountPayableInputSet,
                    {
                        company: '#US001',
                        asOfDate: date.today(),
                        fromSupplier: '#US017',
                        toSupplier: '#US017',
                    },
                );
                await inputSet.$.save();
                const user = await inputSet.user;
                await xtremPurchasing.nodes.UnbilledAccountPayableInputSet.unbilledAccountPayableInquiry(
                    context,
                    user._id.toString(),
                );
                inputSet = await context.read(xtremPurchasing.nodes.UnbilledAccountPayableInputSet, { user });
                assert.deepEqual(await (await inputSet.user).email, await user.email);
                assert.deepEqual(await inputSet.lines.length, 2);
                assert.deepEqual((await (await inputSet.lines.elementAt(0)).invoiceReceivableQuantity).toString(), '3');
                assert.deepEqual((await (await inputSet.lines.elementAt(1)).invoiceReceivableAmount).toString(), '300');
            },
            {
                today: '2024-01-22',
            },
        ));
});
